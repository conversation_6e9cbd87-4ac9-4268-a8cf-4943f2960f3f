// lib: , url: package:dio/src/options.dart

// class id: 1049610, size: 0x8
class :: {

  [closure] static bool _defaultValidateStatus(dynamic, int?) {
    // ** addr: 0x885b54, size: 0x40
    // 0x885b54: ldr             x1, [SP]
    // 0x885b58: cmp             w1, NULL
    // 0x885b5c: b.eq            #0x885b8c
    // 0x885b60: r2 = LoadInt32Instr(r1)
    //     0x885b60: sbfx            x2, x1, #1, #0x1f
    //     0x885b64: tbz             w1, #0, #0x885b6c
    //     0x885b68: ldur            x2, [x1, #7]
    // 0x885b6c: cmp             x2, #0xc8
    // 0x885b70: b.lt            #0x885b8c
    // 0x885b74: cmp             x2, #0x12c
    // 0x885b78: r16 = true
    //     0x885b78: add             x16, NULL, #0x20  ; true
    // 0x885b7c: r17 = false
    //     0x885b7c: add             x17, NULL, #0x30  ; false
    // 0x885b80: csel            x1, x16, x17, lt
    // 0x885b84: mov             x0, x1
    // 0x885b88: b               #0x885b90
    // 0x885b8c: r0 = false
    //     0x885b8c: add             x0, NULL, #0x30  ; false
    // 0x885b90: ret
    //     0x885b90: ret             
  }
}

// class id: 4975, size: 0x48, field offset: 0x8
abstract class _RequestConfig extends Object {

  late ResponseType responseType; // offset: 0x20
  late bool preserveHeaderCase; // offset: 0x10
  late (dynamic, int?) => bool validateStatus; // offset: 0x24
  late bool receiveDataWhenStatusError; // offset: 0x28
  late String method; // offset: 0x8
  late Map<String, dynamic> _headers; // offset: 0xc
  late bool followRedirects; // offset: 0x30
  late int maxRedirects; // offset: 0x34
  late bool persistentConnection; // offset: 0x38
  late ListFormat listFormat; // offset: 0x44
  late Map<String, dynamic> extra; // offset: 0x2c

  get _ contentType(/* No info */) {
    // ** addr: 0x882e68, size: 0xb4
    // 0x882e68: EnterFrame
    //     0x882e68: stp             fp, lr, [SP, #-0x10]!
    //     0x882e6c: mov             fp, SP
    // 0x882e70: AllocStack(0x8)
    //     0x882e70: sub             SP, SP, #8
    // 0x882e74: CheckStackOverflow
    //     0x882e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x882e78: cmp             SP, x16
    //     0x882e7c: b.ls            #0x882f08
    // 0x882e80: LoadField: r0 = r1->field_b
    //     0x882e80: ldur            w0, [x1, #0xb]
    // 0x882e84: DecompressPointer r0
    //     0x882e84: add             x0, x0, HEAP, lsl #32
    // 0x882e88: r16 = Sentinel
    //     0x882e88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x882e8c: cmp             w0, w16
    // 0x882e90: b.eq            #0x882f10
    // 0x882e94: r1 = LoadClassIdInstr(r0)
    //     0x882e94: ldur            x1, [x0, #-1]
    //     0x882e98: ubfx            x1, x1, #0xc, #0x14
    // 0x882e9c: mov             x16, x0
    // 0x882ea0: mov             x0, x1
    // 0x882ea4: mov             x1, x16
    // 0x882ea8: r2 = "content-type"
    //     0x882ea8: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x882eac: ldr             x2, [x2, #0x568]
    // 0x882eb0: r0 = GDT[cid_x0 + -0xfe]()
    //     0x882eb0: sub             lr, x0, #0xfe
    //     0x882eb4: ldr             lr, [x21, lr, lsl #3]
    //     0x882eb8: blr             lr
    // 0x882ebc: mov             x3, x0
    // 0x882ec0: r2 = Null
    //     0x882ec0: mov             x2, NULL
    // 0x882ec4: r1 = Null
    //     0x882ec4: mov             x1, NULL
    // 0x882ec8: stur            x3, [fp, #-8]
    // 0x882ecc: r4 = 60
    //     0x882ecc: movz            x4, #0x3c
    // 0x882ed0: branchIfSmi(r0, 0x882edc)
    //     0x882ed0: tbz             w0, #0, #0x882edc
    // 0x882ed4: r4 = LoadClassIdInstr(r0)
    //     0x882ed4: ldur            x4, [x0, #-1]
    //     0x882ed8: ubfx            x4, x4, #0xc, #0x14
    // 0x882edc: sub             x4, x4, #0x5e
    // 0x882ee0: cmp             x4, #1
    // 0x882ee4: b.ls            #0x882ef8
    // 0x882ee8: r8 = String?
    //     0x882ee8: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x882eec: r3 = Null
    //     0x882eec: add             x3, PP, #0xa, lsl #12  ; [pp+0xa628] Null
    //     0x882ef0: ldr             x3, [x3, #0x628]
    // 0x882ef4: r0 = String?()
    //     0x882ef4: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x882ef8: ldur            x0, [fp, #-8]
    // 0x882efc: LeaveFrame
    //     0x882efc: mov             SP, fp
    //     0x882f00: ldp             fp, lr, [SP], #0x10
    // 0x882f04: ret
    //     0x882f04: ret             
    // 0x882f08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x882f08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x882f0c: b               #0x882e80
    // 0x882f10: r9 = _headers
    //     0x882f10: add             x9, PP, #8, lsl #12  ; [pp+0x8c80] Field <_RequestConfig@914184022._headers@914184022>: late (offset: 0xc)
    //     0x882f14: ldr             x9, [x9, #0xc80]
    // 0x882f18: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x882f18: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _RequestConfig(/* No info */) {
    // ** addr: 0x885654, size: 0x31c
    // 0x885654: EnterFrame
    //     0x885654: stp             fp, lr, [SP, #-0x10]!
    //     0x885658: mov             fp, SP
    // 0x88565c: AllocStack(0x30)
    //     0x88565c: sub             SP, SP, #0x30
    // 0x885660: r0 = Sentinel
    //     0x885660: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x885664: mov             x4, x1
    // 0x885668: stur            x1, [fp, #-8]
    // 0x88566c: mov             x1, x2
    // 0x885670: stur            x2, [fp, #-0x10]
    // 0x885674: mov             x2, x6
    // 0x885678: stur            x5, [fp, #-0x18]
    // 0x88567c: stur            x6, [fp, #-0x20]
    // 0x885680: CheckStackOverflow
    //     0x885680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x885684: cmp             SP, x16
    //     0x885688: b.ls            #0x88595c
    // 0x88568c: StoreField: r4->field_b = r0
    //     0x88568c: stur            w0, [x4, #0xb]
    // 0x885690: ldr             x0, [fp, #0x20]
    // 0x885694: ArrayStore: r4[0] = r0  ; List_4
    //     0x885694: stur            w0, [x4, #0x17]
    //     0x885698: ldurb           w16, [x4, #-1]
    //     0x88569c: ldurb           w17, [x0, #-1]
    //     0x8856a0: and             x16, x17, x16, lsr #2
    //     0x8856a4: tst             x16, HEAP, lsr #32
    //     0x8856a8: b.eq            #0x8856b0
    //     0x8856ac: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x8856b0: ldr             x0, [fp, #0x40]
    // 0x8856b4: cmp             w0, NULL
    // 0x8856b8: b.ne            #0x8856c4
    // 0x8856bc: r0 = "GET"
    //     0x8856bc: add             x0, PP, #8, lsl #12  ; [pp+0x8f78] "GET"
    //     0x8856c0: ldr             x0, [x0, #0xf78]
    // 0x8856c4: ldr             x6, [fp, #0x30]
    // 0x8856c8: StoreField: r4->field_7 = r0
    //     0x8856c8: stur            w0, [x4, #7]
    //     0x8856cc: ldurb           w16, [x4, #-1]
    //     0x8856d0: ldurb           w17, [x0, #-1]
    //     0x8856d4: and             x16, x17, x16, lsr #2
    //     0x8856d8: tst             x16, HEAP, lsr #32
    //     0x8856dc: b.eq            #0x8856e4
    //     0x8856e0: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x8856e4: cmp             w6, NULL
    // 0x8856e8: b.ne            #0x8856f4
    // 0x8856ec: r0 = false
    //     0x8856ec: add             x0, NULL, #0x30  ; false
    // 0x8856f0: b               #0x8856f8
    // 0x8856f4: mov             x0, x6
    // 0x8856f8: StoreField: r4->field_f = r0
    //     0x8856f8: stur            w0, [x4, #0xf]
    // 0x8856fc: cmp             w7, NULL
    // 0x885700: b.ne            #0x885710
    // 0x885704: r0 = Instance_ListFormat
    //     0x885704: add             x0, PP, #0xa, lsl #12  ; [pp+0xa7d8] Obj!ListFormat@d750c1
    //     0x885708: ldr             x0, [x0, #0x7d8]
    // 0x88570c: b               #0x885714
    // 0x885710: mov             x0, x7
    // 0x885714: StoreField: r4->field_43 = r0
    //     0x885714: stur            w0, [x4, #0x43]
    //     0x885718: ldurb           w16, [x4, #-1]
    //     0x88571c: ldurb           w17, [x0, #-1]
    //     0x885720: and             x16, x17, x16, lsr #2
    //     0x885724: tst             x16, HEAP, lsr #32
    //     0x885728: b.eq            #0x885730
    //     0x88572c: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x885730: cmp             w3, NULL
    // 0x885734: b.ne            #0x88574c
    // 0x885738: r16 = <String, dynamic>
    //     0x885738: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x88573c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x885740: stp             lr, x16, [SP]
    // 0x885744: r0 = Map._fromLiteral()
    //     0x885744: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x885748: b               #0x885750
    // 0x88574c: mov             x0, x3
    // 0x885750: ldur            x3, [fp, #-8]
    // 0x885754: ldur            x1, [fp, #-0x18]
    // 0x885758: StoreField: r3->field_2b = r0
    //     0x885758: stur            w0, [x3, #0x2b]
    //     0x88575c: tbz             w0, #0, #0x885778
    //     0x885760: ldurb           w16, [x3, #-1]
    //     0x885764: ldurb           w17, [x0, #-1]
    //     0x885768: and             x16, x17, x16, lsr #2
    //     0x88576c: tst             x16, HEAP, lsr #32
    //     0x885770: b.eq            #0x885778
    //     0x885774: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x885778: cmp             w1, NULL
    // 0x88577c: b.ne            #0x885784
    // 0x885780: r1 = true
    //     0x885780: add             x1, NULL, #0x20  ; true
    // 0x885784: ldr             x0, [fp, #0x48]
    // 0x885788: StoreField: r3->field_2f = r1
    //     0x885788: stur            w1, [x3, #0x2f]
    // 0x88578c: cmp             w0, NULL
    // 0x885790: b.ne            #0x88579c
    // 0x885794: r1 = 5
    //     0x885794: movz            x1, #0x5
    // 0x885798: b               #0x8857a0
    // 0x88579c: r1 = LoadInt32Instr(r0)
    //     0x88579c: sbfx            x1, x0, #1, #0x1f
    // 0x8857a0: ldr             x0, [fp, #0x38]
    // 0x8857a4: lsl             x2, x1, #1
    // 0x8857a8: StoreField: r3->field_33 = r2
    //     0x8857a8: stur            w2, [x3, #0x33]
    // 0x8857ac: cmp             w0, NULL
    // 0x8857b0: b.ne            #0x8857bc
    // 0x8857b4: r1 = true
    //     0x8857b4: add             x1, NULL, #0x20  ; true
    // 0x8857b8: b               #0x8857c0
    // 0x8857bc: mov             x1, x0
    // 0x8857c0: ldr             x0, [fp, #0x28]
    // 0x8857c4: StoreField: r3->field_37 = r1
    //     0x8857c4: stur            w1, [x3, #0x37]
    // 0x8857c8: cmp             w0, NULL
    // 0x8857cc: b.ne            #0x8857d8
    // 0x8857d0: r1 = true
    //     0x8857d0: add             x1, NULL, #0x20  ; true
    // 0x8857d4: b               #0x8857dc
    // 0x8857d8: mov             x1, x0
    // 0x8857dc: ldr             x0, [fp, #0x10]
    // 0x8857e0: StoreField: r3->field_27 = r1
    //     0x8857e0: stur            w1, [x3, #0x27]
    // 0x8857e4: cmp             w0, NULL
    // 0x8857e8: b.ne            #0x8857f4
    // 0x8857ec: r0 = Closure: (int?) => bool from Function '_defaultValidateStatus@914184022': static.
    //     0x8857ec: add             x0, PP, #0xa, lsl #12  ; [pp+0xa7d0] Closure: (int?) => bool from Function '_defaultValidateStatus@914184022': static. (0x7fa737885b54)
    //     0x8857f0: ldr             x0, [x0, #0x7d0]
    // 0x8857f4: ldr             x1, [fp, #0x18]
    // 0x8857f8: StoreField: r3->field_23 = r0
    //     0x8857f8: stur            w0, [x3, #0x23]
    //     0x8857fc: ldurb           w16, [x3, #-1]
    //     0x885800: ldurb           w17, [x0, #-1]
    //     0x885804: and             x16, x17, x16, lsr #2
    //     0x885808: tst             x16, HEAP, lsr #32
    //     0x88580c: b.eq            #0x885814
    //     0x885810: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x885814: cmp             w1, NULL
    // 0x885818: b.ne            #0x885828
    // 0x88581c: r0 = Instance_ResponseType
    //     0x88581c: add             x0, PP, #8, lsl #12  ; [pp+0x8348] Obj!ResponseType@d750e1
    //     0x885820: ldr             x0, [x0, #0x348]
    // 0x885824: b               #0x88582c
    // 0x885828: mov             x0, x1
    // 0x88582c: ldur            x4, [fp, #-0x10]
    // 0x885830: StoreField: r3->field_1f = r0
    //     0x885830: stur            w0, [x3, #0x1f]
    //     0x885834: ldurb           w16, [x3, #-1]
    //     0x885838: ldurb           w17, [x0, #-1]
    //     0x88583c: and             x16, x17, x16, lsr #2
    //     0x885840: tst             x16, HEAP, lsr #32
    //     0x885844: b.eq            #0x88584c
    //     0x885848: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x88584c: mov             x1, x3
    // 0x885850: ldur            x2, [fp, #-0x20]
    // 0x885854: r0 = headers=()
    //     0x885854: bl              #0x885a8c  ; [package:dio/src/options.dart] _RequestConfig::headers=
    // 0x885858: ldur            x3, [fp, #-8]
    // 0x88585c: LoadField: r1 = r3->field_b
    //     0x88585c: ldur            w1, [x3, #0xb]
    // 0x885860: DecompressPointer r1
    //     0x885860: add             x1, x1, HEAP, lsl #32
    // 0x885864: r16 = Sentinel
    //     0x885864: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x885868: cmp             w1, w16
    // 0x88586c: b.eq            #0x885964
    // 0x885870: r0 = LoadClassIdInstr(r1)
    //     0x885870: ldur            x0, [x1, #-1]
    //     0x885874: ubfx            x0, x0, #0xc, #0x14
    // 0x885878: r2 = "content-type"
    //     0x885878: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x88587c: ldr             x2, [x2, #0x568]
    // 0x885880: r0 = GDT[cid_x0 + 0x39d]()
    //     0x885880: add             lr, x0, #0x39d
    //     0x885884: ldr             lr, [x21, lr, lsl #3]
    //     0x885888: blr             lr
    // 0x88588c: mov             x4, x0
    // 0x885890: ldur            x3, [fp, #-0x10]
    // 0x885894: stur            x4, [fp, #-0x18]
    // 0x885898: cmp             w3, NULL
    // 0x88589c: b.eq            #0x8858f8
    // 0x8858a0: tbnz            w4, #4, #0x8858f8
    // 0x8858a4: ldur            x5, [fp, #-8]
    // 0x8858a8: LoadField: r1 = r5->field_b
    //     0x8858a8: ldur            w1, [x5, #0xb]
    // 0x8858ac: DecompressPointer r1
    //     0x8858ac: add             x1, x1, HEAP, lsl #32
    // 0x8858b0: r0 = LoadClassIdInstr(r1)
    //     0x8858b0: ldur            x0, [x1, #-1]
    //     0x8858b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8858b8: r2 = "content-type"
    //     0x8858b8: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x8858bc: ldr             x2, [x2, #0x568]
    // 0x8858c0: r0 = GDT[cid_x0 + -0xfe]()
    //     0x8858c0: sub             lr, x0, #0xfe
    //     0x8858c4: ldr             lr, [x21, lr, lsl #3]
    //     0x8858c8: blr             lr
    // 0x8858cc: r1 = 60
    //     0x8858cc: movz            x1, #0x3c
    // 0x8858d0: branchIfSmi(r0, 0x8858dc)
    //     0x8858d0: tbz             w0, #0, #0x8858dc
    // 0x8858d4: r1 = LoadClassIdInstr(r0)
    //     0x8858d4: ldur            x1, [x0, #-1]
    //     0x8858d8: ubfx            x1, x1, #0xc, #0x14
    // 0x8858dc: ldur            x16, [fp, #-0x10]
    // 0x8858e0: stp             x16, x0, [SP]
    // 0x8858e4: mov             x0, x1
    // 0x8858e8: mov             lr, x0
    // 0x8858ec: ldr             lr, [x21, lr, lsl #3]
    // 0x8858f0: blr             lr
    // 0x8858f4: tbnz            w0, #4, #0x88591c
    // 0x8858f8: ldur            x0, [fp, #-0x18]
    // 0x8858fc: tbz             w0, #4, #0x88590c
    // 0x885900: ldur            x1, [fp, #-8]
    // 0x885904: ldur            x2, [fp, #-0x10]
    // 0x885908: r0 = contentType=()
    //     0x885908: bl              #0x885970  ; [package:dio/src/options.dart] _RequestConfig::contentType=
    // 0x88590c: r0 = Null
    //     0x88590c: mov             x0, NULL
    // 0x885910: LeaveFrame
    //     0x885910: mov             SP, fp
    //     0x885914: ldp             fp, lr, [SP], #0x10
    // 0x885918: ret
    //     0x885918: ret             
    // 0x88591c: ldur            x0, [fp, #-0x10]
    // 0x885920: r0 = ArgumentError()
    //     0x885920: bl              #0x61ba20  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x885924: mov             x1, x0
    // 0x885928: r0 = "contentType"
    //     0x885928: add             x0, PP, #0xa, lsl #12  ; [pp+0xa808] "contentType"
    //     0x88592c: ldr             x0, [x0, #0x808]
    // 0x885930: StoreField: r1->field_13 = r0
    //     0x885930: stur            w0, [x1, #0x13]
    // 0x885934: r0 = "Unable to set different values for `contentType` and the content-type header."
    //     0x885934: add             x0, PP, #0xa, lsl #12  ; [pp+0xa868] "Unable to set different values for `contentType` and the content-type header."
    //     0x885938: ldr             x0, [x0, #0x868]
    // 0x88593c: ArrayStore: r1[0] = r0  ; List_4
    //     0x88593c: stur            w0, [x1, #0x17]
    // 0x885940: ldur            x0, [fp, #-0x10]
    // 0x885944: StoreField: r1->field_f = r0
    //     0x885944: stur            w0, [x1, #0xf]
    // 0x885948: r0 = true
    //     0x885948: add             x0, NULL, #0x20  ; true
    // 0x88594c: StoreField: r1->field_b = r0
    //     0x88594c: stur            w0, [x1, #0xb]
    // 0x885950: mov             x0, x1
    // 0x885954: r0 = Throw()
    //     0x885954: bl              #0x16f5420  ; ThrowStub
    // 0x885958: brk             #0
    // 0x88595c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88595c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x885960: b               #0x88568c
    // 0x885964: r9 = _headers
    //     0x885964: add             x9, PP, #8, lsl #12  ; [pp+0x8c80] Field <_RequestConfig@914184022._headers@914184022>: late (offset: 0xc)
    //     0x885968: ldr             x9, [x9, #0xc80]
    // 0x88596c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x88596c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  set _ contentType=(/* No info */) {
    // ** addr: 0x885970, size: 0x11c
    // 0x885970: EnterFrame
    //     0x885970: stp             fp, lr, [SP, #-0x10]!
    //     0x885974: mov             fp, SP
    // 0x885978: AllocStack(0x8)
    //     0x885978: sub             SP, SP, #8
    // 0x88597c: SetupParameters(_RequestConfig this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1 */)
    //     0x88597c: mov             x0, x1
    //     0x885980: stur            x1, [fp, #-8]
    //     0x885984: mov             x1, x2
    // 0x885988: CheckStackOverflow
    //     0x885988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88598c: cmp             SP, x16
    //     0x885990: b.ls            #0x885a6c
    // 0x885994: cmp             w1, NULL
    // 0x885998: b.ne            #0x8859a8
    // 0x88599c: mov             x1, x0
    // 0x8859a0: r2 = Null
    //     0x8859a0: mov             x2, NULL
    // 0x8859a4: b               #0x8859b4
    // 0x8859a8: r0 = trim()
    //     0x8859a8: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x8859ac: mov             x2, x0
    // 0x8859b0: ldur            x1, [fp, #-8]
    // 0x8859b4: mov             x0, x2
    // 0x8859b8: StoreField: r1->field_1b = r0
    //     0x8859b8: stur            w0, [x1, #0x1b]
    //     0x8859bc: ldurb           w16, [x1, #-1]
    //     0x8859c0: ldurb           w17, [x0, #-1]
    //     0x8859c4: and             x16, x17, x16, lsr #2
    //     0x8859c8: tst             x16, HEAP, lsr #32
    //     0x8859cc: b.eq            #0x8859d4
    //     0x8859d0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8859d4: cmp             w2, NULL
    // 0x8859d8: b.eq            #0x885a20
    // 0x8859dc: LoadField: r0 = r1->field_b
    //     0x8859dc: ldur            w0, [x1, #0xb]
    // 0x8859e0: DecompressPointer r0
    //     0x8859e0: add             x0, x0, HEAP, lsl #32
    // 0x8859e4: r16 = Sentinel
    //     0x8859e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8859e8: cmp             w0, w16
    // 0x8859ec: b.eq            #0x885a74
    // 0x8859f0: r1 = LoadClassIdInstr(r0)
    //     0x8859f0: ldur            x1, [x0, #-1]
    //     0x8859f4: ubfx            x1, x1, #0xc, #0x14
    // 0x8859f8: mov             x16, x0
    // 0x8859fc: mov             x0, x1
    // 0x885a00: mov             x1, x16
    // 0x885a04: mov             x3, x2
    // 0x885a08: r2 = "content-type"
    //     0x885a08: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x885a0c: ldr             x2, [x2, #0x568]
    // 0x885a10: r0 = GDT[cid_x0 + 0x35a]()
    //     0x885a10: add             lr, x0, #0x35a
    //     0x885a14: ldr             lr, [x21, lr, lsl #3]
    //     0x885a18: blr             lr
    // 0x885a1c: b               #0x885a5c
    // 0x885a20: LoadField: r0 = r1->field_b
    //     0x885a20: ldur            w0, [x1, #0xb]
    // 0x885a24: DecompressPointer r0
    //     0x885a24: add             x0, x0, HEAP, lsl #32
    // 0x885a28: r16 = Sentinel
    //     0x885a28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x885a2c: cmp             w0, w16
    // 0x885a30: b.eq            #0x885a80
    // 0x885a34: r1 = LoadClassIdInstr(r0)
    //     0x885a34: ldur            x1, [x0, #-1]
    //     0x885a38: ubfx            x1, x1, #0xc, #0x14
    // 0x885a3c: mov             x16, x0
    // 0x885a40: mov             x0, x1
    // 0x885a44: mov             x1, x16
    // 0x885a48: r2 = "content-type"
    //     0x885a48: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x885a4c: ldr             x2, [x2, #0x568]
    // 0x885a50: r0 = GDT[cid_x0 + 0x71f]()
    //     0x885a50: add             lr, x0, #0x71f
    //     0x885a54: ldr             lr, [x21, lr, lsl #3]
    //     0x885a58: blr             lr
    // 0x885a5c: r0 = Null
    //     0x885a5c: mov             x0, NULL
    // 0x885a60: LeaveFrame
    //     0x885a60: mov             SP, fp
    //     0x885a64: ldp             fp, lr, [SP], #0x10
    // 0x885a68: ret
    //     0x885a68: ret             
    // 0x885a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x885a6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x885a70: b               #0x885994
    // 0x885a74: r9 = _headers
    //     0x885a74: add             x9, PP, #8, lsl #12  ; [pp+0x8c80] Field <_RequestConfig@914184022._headers@914184022>: late (offset: 0xc)
    //     0x885a78: ldr             x9, [x9, #0xc80]
    // 0x885a7c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x885a7c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x885a80: r9 = _headers
    //     0x885a80: add             x9, PP, #8, lsl #12  ; [pp+0x8c80] Field <_RequestConfig@914184022._headers@914184022>: late (offset: 0xc)
    //     0x885a84: ldr             x9, [x9, #0xc80]
    // 0x885a88: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x885a88: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  set _ headers=(/* No info */) {
    // ** addr: 0x885a8c, size: 0xbc
    // 0x885a8c: EnterFrame
    //     0x885a8c: stp             fp, lr, [SP, #-0x10]!
    //     0x885a90: mov             fp, SP
    // 0x885a94: AllocStack(0x18)
    //     0x885a94: sub             SP, SP, #0x18
    // 0x885a98: SetupParameters(_RequestConfig this /* r1 => r1, fp-0x8 */)
    //     0x885a98: stur            x1, [fp, #-8]
    // 0x885a9c: CheckStackOverflow
    //     0x885a9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x885aa0: cmp             SP, x16
    //     0x885aa4: b.ls            #0x885b40
    // 0x885aa8: stp             x2, NULL, [SP]
    // 0x885aac: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x885aac: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x885ab0: r0 = caseInsensitiveKeyMap()
    //     0x885ab0: bl              #0x8626d4  ; [package:dio/src/utils.dart] ::caseInsensitiveKeyMap
    // 0x885ab4: mov             x1, x0
    // 0x885ab8: ldur            x3, [fp, #-8]
    // 0x885abc: StoreField: r3->field_b = r0
    //     0x885abc: stur            w0, [x3, #0xb]
    //     0x885ac0: ldurb           w16, [x3, #-1]
    //     0x885ac4: ldurb           w17, [x0, #-1]
    //     0x885ac8: and             x16, x17, x16, lsr #2
    //     0x885acc: tst             x16, HEAP, lsr #32
    //     0x885ad0: b.eq            #0x885ad8
    //     0x885ad4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x885ad8: r0 = LoadClassIdInstr(r1)
    //     0x885ad8: ldur            x0, [x1, #-1]
    //     0x885adc: ubfx            x0, x0, #0xc, #0x14
    // 0x885ae0: r2 = "content-type"
    //     0x885ae0: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x885ae4: ldr             x2, [x2, #0x568]
    // 0x885ae8: r0 = GDT[cid_x0 + 0x39d]()
    //     0x885ae8: add             lr, x0, #0x39d
    //     0x885aec: ldr             lr, [x21, lr, lsl #3]
    //     0x885af0: blr             lr
    // 0x885af4: tbz             w0, #4, #0x885b30
    // 0x885af8: ldur            x0, [fp, #-8]
    // 0x885afc: LoadField: r3 = r0->field_1b
    //     0x885afc: ldur            w3, [x0, #0x1b]
    // 0x885b00: DecompressPointer r3
    //     0x885b00: add             x3, x3, HEAP, lsl #32
    // 0x885b04: cmp             w3, NULL
    // 0x885b08: b.eq            #0x885b30
    // 0x885b0c: LoadField: r1 = r0->field_b
    //     0x885b0c: ldur            w1, [x0, #0xb]
    // 0x885b10: DecompressPointer r1
    //     0x885b10: add             x1, x1, HEAP, lsl #32
    // 0x885b14: r0 = LoadClassIdInstr(r1)
    //     0x885b14: ldur            x0, [x1, #-1]
    //     0x885b18: ubfx            x0, x0, #0xc, #0x14
    // 0x885b1c: r2 = "content-type"
    //     0x885b1c: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x885b20: ldr             x2, [x2, #0x568]
    // 0x885b24: r0 = GDT[cid_x0 + 0x35a]()
    //     0x885b24: add             lr, x0, #0x35a
    //     0x885b28: ldr             lr, [x21, lr, lsl #3]
    //     0x885b2c: blr             lr
    // 0x885b30: r0 = Null
    //     0x885b30: mov             x0, NULL
    // 0x885b34: LeaveFrame
    //     0x885b34: mov             SP, fp
    //     0x885b38: ldp             fp, lr, [SP], #0x10
    // 0x885b3c: ret
    //     0x885b3c: ret             
    // 0x885b40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x885b40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x885b44: b               #0x885aa8
  }
}

// class id: 4976, size: 0x54, field offset: 0x48
//   transformed mixin,
abstract class _BaseOptions&_RequestConfig&OptionsMixin extends _RequestConfig
     with OptionsMixin {

  late String _baseUrl; // offset: 0x48
  late Map<String, dynamic> queryParameters; // offset: 0x4c

  set _ connectTimeout=(/* No info */) {
    // ** addr: 0x88549c, size: 0x68
    // 0x88549c: EnterFrame
    //     0x88549c: stp             fp, lr, [SP, #-0x10]!
    //     0x8854a0: mov             fp, SP
    // 0x8854a4: mov             x0, x2
    // 0x8854a8: cmp             w0, NULL
    // 0x8854ac: b.eq            #0x8854b8
    // 0x8854b0: LoadField: r2 = r0->field_7
    //     0x8854b0: ldur            x2, [x0, #7]
    // 0x8854b4: tbnz            x2, #0x3f, #0x8854e4
    // 0x8854b8: StoreField: r1->field_4f = r0
    //     0x8854b8: stur            w0, [x1, #0x4f]
    //     0x8854bc: ldurb           w16, [x1, #-1]
    //     0x8854c0: ldurb           w17, [x0, #-1]
    //     0x8854c4: and             x16, x17, x16, lsr #2
    //     0x8854c8: tst             x16, HEAP, lsr #32
    //     0x8854cc: b.eq            #0x8854d4
    //     0x8854d0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8854d4: r0 = Null
    //     0x8854d4: mov             x0, NULL
    // 0x8854d8: LeaveFrame
    //     0x8854d8: mov             SP, fp
    //     0x8854dc: ldp             fp, lr, [SP], #0x10
    // 0x8854e0: ret
    //     0x8854e0: ret             
    // 0x8854e4: r0 = StateError()
    //     0x8854e4: bl              #0x622864  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8854e8: mov             x1, x0
    // 0x8854ec: r0 = "connectTimeout should be positive"
    //     0x8854ec: add             x0, PP, #0xa, lsl #12  ; [pp+0xa858] "connectTimeout should be positive"
    //     0x8854f0: ldr             x0, [x0, #0x858]
    // 0x8854f4: StoreField: r1->field_b = r0
    //     0x8854f4: stur            w0, [x1, #0xb]
    // 0x8854f8: mov             x0, x1
    // 0x8854fc: r0 = Throw()
    //     0x8854fc: bl              #0x16f5420  ; ThrowStub
    // 0x885500: brk             #0
  }
  set _ baseUrl=(/* No info */) {
    // ** addr: 0x885504, size: 0xe0
    // 0x885504: EnterFrame
    //     0x885504: stp             fp, lr, [SP, #-0x10]!
    //     0x885508: mov             fp, SP
    // 0x88550c: AllocStack(0x10)
    //     0x88550c: sub             SP, SP, #0x10
    // 0x885510: SetupParameters(_BaseOptions&_RequestConfig&OptionsMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x885510: mov             x0, x2
    //     0x885514: stur            x2, [fp, #-0x10]
    //     0x885518: mov             x2, x1
    //     0x88551c: stur            x1, [fp, #-8]
    // 0x885520: CheckStackOverflow
    //     0x885520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x885524: cmp             SP, x16
    //     0x885528: b.ls            #0x8855dc
    // 0x88552c: LoadField: r1 = r0->field_7
    //     0x88552c: ldur            w1, [x0, #7]
    // 0x885530: cbz             w1, #0x885568
    // 0x885534: mov             x1, x0
    // 0x885538: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x885538: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x88553c: r0 = parse()
    //     0x88553c: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0x885540: r1 = LoadClassIdInstr(r0)
    //     0x885540: ldur            x1, [x0, #-1]
    //     0x885544: ubfx            x1, x1, #0xc, #0x14
    // 0x885548: mov             x16, x0
    // 0x88554c: mov             x0, x1
    // 0x885550: mov             x1, x16
    // 0x885554: r0 = GDT[cid_x0 + -0xfd7]()
    //     0x885554: sub             lr, x0, #0xfd7
    //     0x885558: ldr             lr, [x21, lr, lsl #3]
    //     0x88555c: blr             lr
    // 0x885560: LoadField: r1 = r0->field_7
    //     0x885560: ldur            w1, [x0, #7]
    // 0x885564: cbz             w1, #0x88559c
    // 0x885568: ldur            x1, [fp, #-8]
    // 0x88556c: ldur            x0, [fp, #-0x10]
    // 0x885570: StoreField: r1->field_47 = r0
    //     0x885570: stur            w0, [x1, #0x47]
    //     0x885574: ldurb           w16, [x1, #-1]
    //     0x885578: ldurb           w17, [x0, #-1]
    //     0x88557c: and             x16, x17, x16, lsr #2
    //     0x885580: tst             x16, HEAP, lsr #32
    //     0x885584: b.eq            #0x88558c
    //     0x885588: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x88558c: r0 = Null
    //     0x88558c: mov             x0, NULL
    // 0x885590: LeaveFrame
    //     0x885590: mov             SP, fp
    //     0x885594: ldp             fp, lr, [SP], #0x10
    // 0x885598: ret
    //     0x885598: ret             
    // 0x88559c: ldur            x0, [fp, #-0x10]
    // 0x8855a0: r0 = ArgumentError()
    //     0x8855a0: bl              #0x61ba20  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x8855a4: mov             x1, x0
    // 0x8855a8: r0 = "baseUrl"
    //     0x8855a8: add             x0, PP, #0xa, lsl #12  ; [pp+0xa7f0] "baseUrl"
    //     0x8855ac: ldr             x0, [x0, #0x7f0]
    // 0x8855b0: StoreField: r1->field_13 = r0
    //     0x8855b0: stur            w0, [x1, #0x13]
    // 0x8855b4: r0 = "Must be a valid URL on platforms other than Web."
    //     0x8855b4: add             x0, PP, #0xa, lsl #12  ; [pp+0xa860] "Must be a valid URL on platforms other than Web."
    //     0x8855b8: ldr             x0, [x0, #0x860]
    // 0x8855bc: ArrayStore: r1[0] = r0  ; List_4
    //     0x8855bc: stur            w0, [x1, #0x17]
    // 0x8855c0: ldur            x0, [fp, #-0x10]
    // 0x8855c4: StoreField: r1->field_f = r0
    //     0x8855c4: stur            w0, [x1, #0xf]
    // 0x8855c8: r0 = true
    //     0x8855c8: add             x0, NULL, #0x20  ; true
    // 0x8855cc: StoreField: r1->field_b = r0
    //     0x8855cc: stur            w0, [x1, #0xb]
    // 0x8855d0: mov             x0, x1
    // 0x8855d4: r0 = Throw()
    //     0x8855d4: bl              #0x16f5420  ; ThrowStub
    // 0x8855d8: brk             #0
    // 0x8855dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8855dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8855e0: b               #0x88552c
  }
  _ _BaseOptions&_RequestConfig&OptionsMixin(/* No info */) {
    // ** addr: 0x8855e4, size: 0x70
    // 0x8855e4: EnterFrame
    //     0x8855e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8855e8: mov             fp, SP
    // 0x8855ec: AllocStack(0x40)
    //     0x8855ec: sub             SP, SP, #0x40
    // 0x8855f0: r0 = Sentinel
    //     0x8855f0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8855f4: CheckStackOverflow
    //     0x8855f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8855f8: cmp             SP, x16
    //     0x8855fc: b.ls            #0x88564c
    // 0x885600: StoreField: r1->field_47 = r0
    //     0x885600: stur            w0, [x1, #0x47]
    // 0x885604: StoreField: r1->field_4b = r0
    //     0x885604: stur            w0, [x1, #0x4b]
    // 0x885608: ldr             x16, [fp, #0x48]
    // 0x88560c: ldr             lr, [fp, #0x40]
    // 0x885610: stp             lr, x16, [SP, #0x30]
    // 0x885614: ldr             x16, [fp, #0x38]
    // 0x885618: ldr             lr, [fp, #0x30]
    // 0x88561c: stp             lr, x16, [SP, #0x20]
    // 0x885620: ldr             x16, [fp, #0x28]
    // 0x885624: ldr             lr, [fp, #0x20]
    // 0x885628: stp             lr, x16, [SP, #0x10]
    // 0x88562c: ldr             x16, [fp, #0x18]
    // 0x885630: ldr             lr, [fp, #0x10]
    // 0x885634: stp             lr, x16, [SP]
    // 0x885638: r0 = _RequestConfig()
    //     0x885638: bl              #0x885654  ; [package:dio/src/options.dart] _RequestConfig::_RequestConfig
    // 0x88563c: r0 = Null
    //     0x88563c: mov             x0, NULL
    // 0x885640: LeaveFrame
    //     0x885640: mov             SP, fp
    //     0x885644: ldp             fp, lr, [SP], #0x10
    // 0x885648: ret
    //     0x885648: ret             
    // 0x88564c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88564c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x885650: b               #0x885600
  }
}

// class id: 4977, size: 0x6c, field offset: 0x54
class RequestOptions extends _BaseOptions&_RequestConfig&OptionsMixin {

  dynamic dyn:get:data(RequestOptions) {
    // ** addr: 0x862fe4, size: 0x28
    // 0x862fe4: ldr             x1, [SP]
    // 0x862fe8: LoadField: r0 = r1->field_57
    //     0x862fe8: ldur            w0, [x1, #0x57]
    // 0x862fec: DecompressPointer r0
    //     0x862fec: add             x0, x0, HEAP, lsl #32
    // 0x862ff0: ret
    //     0x862ff0: ret             
  }
  get _ uri(/* No info */) {
    // ** addr: 0x8800dc, size: 0x290
    // 0x8800dc: EnterFrame
    //     0x8800dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8800e0: mov             fp, SP
    // 0x8800e4: AllocStack(0x68)
    //     0x8800e4: sub             SP, SP, #0x68
    // 0x8800e8: SetupParameters(RequestOptions this /* r1 => r1, fp-0x10 */)
    //     0x8800e8: stur            x1, [fp, #-0x10]
    // 0x8800ec: CheckStackOverflow
    //     0x8800ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8800f0: cmp             SP, x16
    //     0x8800f4: b.ls            #0x880338
    // 0x8800f8: LoadField: r0 = r1->field_5b
    //     0x8800f8: ldur            w0, [x1, #0x5b]
    // 0x8800fc: DecompressPointer r0
    //     0x8800fc: add             x0, x0, HEAP, lsl #32
    // 0x880100: stur            x0, [fp, #-8]
    // 0x880104: r16 = "https\?:"
    //     0x880104: add             x16, PP, #0xa, lsl #12  ; [pp+0xa3c0] "https\?:"
    //     0x880108: ldr             x16, [x16, #0x3c0]
    // 0x88010c: stp             x16, NULL, [SP, #0x20]
    // 0x880110: r16 = false
    //     0x880110: add             x16, NULL, #0x30  ; false
    // 0x880114: r30 = true
    //     0x880114: add             lr, NULL, #0x20  ; true
    // 0x880118: stp             lr, x16, [SP, #0x10]
    // 0x88011c: r16 = false
    //     0x88011c: add             x16, NULL, #0x30  ; false
    // 0x880120: r30 = false
    //     0x880120: add             lr, NULL, #0x30  ; false
    // 0x880124: stp             lr, x16, [SP]
    // 0x880128: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x880128: ldr             x4, [PP, #0xa20]  ; [pp+0xa20] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x88012c: r0 = _RegExp()
    //     0x88012c: bl              #0x6289d0  ; [dart:core] _RegExp::_RegExp
    // 0x880130: ldur            x1, [fp, #-8]
    // 0x880134: mov             x2, x0
    // 0x880138: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x880138: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x88013c: r0 = startsWith()
    //     0x88013c: bl              #0x627958  ; [dart:core] _StringBase::startsWith
    // 0x880140: tbz             w0, #4, #0x880264
    // 0x880144: ldur            x0, [fp, #-0x10]
    // 0x880148: LoadField: r1 = r0->field_47
    //     0x880148: ldur            w1, [x0, #0x47]
    // 0x88014c: DecompressPointer r1
    //     0x88014c: add             x1, x1, HEAP, lsl #32
    // 0x880150: r16 = Sentinel
    //     0x880150: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x880154: cmp             w1, w16
    // 0x880158: b.eq            #0x880340
    // 0x88015c: ldur            x16, [fp, #-8]
    // 0x880160: stp             x16, x1, [SP]
    // 0x880164: r0 = +()
    //     0x880164: bl              #0x61de44  ; [dart:core] _StringBase::+
    // 0x880168: mov             x3, x0
    // 0x88016c: stur            x3, [fp, #-0x18]
    // 0x880170: r0 = LoadClassIdInstr(r3)
    //     0x880170: ldur            x0, [x3, #-1]
    //     0x880174: ubfx            x0, x0, #0xc, #0x14
    // 0x880178: mov             x1, x3
    // 0x88017c: r2 = ":/"
    //     0x88017c: add             x2, PP, #0xa, lsl #12  ; [pp+0xa3c8] ":/"
    //     0x880180: ldr             x2, [x2, #0x3c8]
    // 0x880184: r0 = GDT[cid_x0 + -0xffc]()
    //     0x880184: sub             lr, x0, #0xffc
    //     0x880188: ldr             lr, [x21, lr, lsl #3]
    //     0x88018c: blr             lr
    // 0x880190: mov             x2, x0
    // 0x880194: LoadField: r0 = r2->field_b
    //     0x880194: ldur            w0, [x2, #0xb]
    // 0x880198: r3 = LoadInt32Instr(r0)
    //     0x880198: sbfx            x3, x0, #1, #0x1f
    // 0x88019c: stur            x3, [fp, #-0x30]
    // 0x8801a0: cmp             x3, #2
    // 0x8801a4: b.ne            #0x880258
    // 0x8801a8: mov             x0, x3
    // 0x8801ac: r1 = 0
    //     0x8801ac: movz            x1, #0
    // 0x8801b0: cmp             x1, x0
    // 0x8801b4: b.hs            #0x88034c
    // 0x8801b8: LoadField: r0 = r2->field_f
    //     0x8801b8: ldur            w0, [x2, #0xf]
    // 0x8801bc: DecompressPointer r0
    //     0x8801bc: add             x0, x0, HEAP, lsl #32
    // 0x8801c0: stur            x0, [fp, #-0x28]
    // 0x8801c4: LoadField: r4 = r0->field_f
    //     0x8801c4: ldur            w4, [x0, #0xf]
    // 0x8801c8: DecompressPointer r4
    //     0x8801c8: add             x4, x4, HEAP, lsl #32
    // 0x8801cc: stur            x4, [fp, #-0x20]
    // 0x8801d0: r1 = Null
    //     0x8801d0: mov             x1, NULL
    // 0x8801d4: r2 = 6
    //     0x8801d4: movz            x2, #0x6
    // 0x8801d8: r0 = AllocateArray()
    //     0x8801d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8801dc: mov             x4, x0
    // 0x8801e0: ldur            x0, [fp, #-0x20]
    // 0x8801e4: stur            x4, [fp, #-0x38]
    // 0x8801e8: StoreField: r4->field_f = r0
    //     0x8801e8: stur            w0, [x4, #0xf]
    // 0x8801ec: r16 = ":/"
    //     0x8801ec: add             x16, PP, #0xa, lsl #12  ; [pp+0xa3c8] ":/"
    //     0x8801f0: ldr             x16, [x16, #0x3c8]
    // 0x8801f4: StoreField: r4->field_13 = r16
    //     0x8801f4: stur            w16, [x4, #0x13]
    // 0x8801f8: ldur            x0, [fp, #-0x30]
    // 0x8801fc: r1 = 1
    //     0x8801fc: movz            x1, #0x1
    // 0x880200: cmp             x1, x0
    // 0x880204: b.hs            #0x880350
    // 0x880208: ldur            x0, [fp, #-0x28]
    // 0x88020c: LoadField: r1 = r0->field_13
    //     0x88020c: ldur            w1, [x0, #0x13]
    // 0x880210: DecompressPointer r1
    //     0x880210: add             x1, x1, HEAP, lsl #32
    // 0x880214: r2 = "//"
    //     0x880214: ldr             x2, [PP, #0x3578]  ; [pp+0x3578] "//"
    // 0x880218: r3 = "/"
    //     0x880218: ldr             x3, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x88021c: r0 = replaceAll()
    //     0x88021c: bl              #0x628c30  ; [dart:core] _StringBase::replaceAll
    // 0x880220: ldur            x1, [fp, #-0x38]
    // 0x880224: ArrayStore: r1[2] = r0  ; List_4
    //     0x880224: add             x25, x1, #0x17
    //     0x880228: str             w0, [x25]
    //     0x88022c: tbz             w0, #0, #0x880248
    //     0x880230: ldurb           w16, [x1, #-1]
    //     0x880234: ldurb           w17, [x0, #-1]
    //     0x880238: and             x16, x17, x16, lsr #2
    //     0x88023c: tst             x16, HEAP, lsr #32
    //     0x880240: b.eq            #0x880248
    //     0x880244: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x880248: ldur            x16, [fp, #-0x38]
    // 0x88024c: str             x16, [SP]
    // 0x880250: r0 = _interpolate()
    //     0x880250: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x880254: b               #0x88025c
    // 0x880258: ldur            x0, [fp, #-0x18]
    // 0x88025c: mov             x2, x0
    // 0x880260: b               #0x880268
    // 0x880264: ldur            x2, [fp, #-8]
    // 0x880268: ldur            x0, [fp, #-0x10]
    // 0x88026c: stur            x2, [fp, #-8]
    // 0x880270: LoadField: r1 = r0->field_4b
    //     0x880270: ldur            w1, [x0, #0x4b]
    // 0x880274: DecompressPointer r1
    //     0x880274: add             x1, x1, HEAP, lsl #32
    // 0x880278: r16 = Sentinel
    //     0x880278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x88027c: cmp             w1, w16
    // 0x880280: b.eq            #0x880354
    // 0x880284: LoadField: r3 = r0->field_43
    //     0x880284: ldur            w3, [x0, #0x43]
    // 0x880288: DecompressPointer r3
    //     0x880288: add             x3, x3, HEAP, lsl #32
    // 0x88028c: r16 = Sentinel
    //     0x88028c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x880290: cmp             w3, w16
    // 0x880294: b.eq            #0x880360
    // 0x880298: r0 = urlEncodeQueryMap()
    //     0x880298: bl              #0x88036c  ; [package:dio/src/transformer.dart] Transformer::urlEncodeQueryMap
    // 0x88029c: mov             x3, x0
    // 0x8802a0: stur            x3, [fp, #-0x10]
    // 0x8802a4: LoadField: r0 = r3->field_7
    //     0x8802a4: ldur            w0, [x3, #7]
    // 0x8802a8: cbz             w0, #0x880300
    // 0x8802ac: ldur            x4, [fp, #-8]
    // 0x8802b0: r0 = LoadClassIdInstr(r4)
    //     0x8802b0: ldur            x0, [x4, #-1]
    //     0x8802b4: ubfx            x0, x0, #0xc, #0x14
    // 0x8802b8: mov             x1, x4
    // 0x8802bc: r2 = "\?"
    //     0x8802bc: ldr             x2, [PP, #0x1310]  ; [pp+0x1310] "\?"
    // 0x8802c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8802c0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8802c4: r0 = GDT[cid_x0 + -0xffe]()
    //     0x8802c4: sub             lr, x0, #0xffe
    //     0x8802c8: ldr             lr, [x21, lr, lsl #3]
    //     0x8802cc: blr             lr
    // 0x8802d0: tbnz            w0, #4, #0x8802dc
    // 0x8802d4: r0 = "&"
    //     0x8802d4: ldr             x0, [PP, #0x1188]  ; [pp+0x1188] "&"
    // 0x8802d8: b               #0x8802e0
    // 0x8802dc: r0 = "\?"
    //     0x8802dc: ldr             x0, [PP, #0x1310]  ; [pp+0x1310] "\?"
    // 0x8802e0: ldur            x16, [fp, #-0x10]
    // 0x8802e4: stp             x16, x0, [SP]
    // 0x8802e8: r0 = +()
    //     0x8802e8: bl              #0x61de44  ; [dart:core] _StringBase::+
    // 0x8802ec: ldur            x16, [fp, #-8]
    // 0x8802f0: stp             x0, x16, [SP]
    // 0x8802f4: r0 = +()
    //     0x8802f4: bl              #0x61de44  ; [dart:core] _StringBase::+
    // 0x8802f8: mov             x1, x0
    // 0x8802fc: b               #0x880304
    // 0x880300: ldur            x1, [fp, #-8]
    // 0x880304: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x880304: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x880308: r0 = parse()
    //     0x880308: bl              #0x62c7a0  ; [dart:core] Uri::parse
    // 0x88030c: r1 = LoadClassIdInstr(r0)
    //     0x88030c: ldur            x1, [x0, #-1]
    //     0x880310: ubfx            x1, x1, #0xc, #0x14
    // 0x880314: mov             x16, x0
    // 0x880318: mov             x0, x1
    // 0x88031c: mov             x1, x16
    // 0x880320: r0 = GDT[cid_x0 + 0x258]()
    //     0x880320: add             lr, x0, #0x258
    //     0x880324: ldr             lr, [x21, lr, lsl #3]
    //     0x880328: blr             lr
    // 0x88032c: LeaveFrame
    //     0x88032c: mov             SP, fp
    //     0x880330: ldp             fp, lr, [SP], #0x10
    // 0x880334: ret
    //     0x880334: ret             
    // 0x880338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x880338: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x88033c: b               #0x8800f8
    // 0x880340: r9 = _baseUrl
    //     0x880340: add             x9, PP, #0xa, lsl #12  ; [pp+0xa3d0] Field <_BaseOptions&_RequestConfig&OptionsMixin@914184022._baseUrl@914184022>: late (offset: 0x48)
    //     0x880344: ldr             x9, [x9, #0x3d0]
    // 0x880348: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x880348: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x88034c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x88034c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x880350: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x880350: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x880354: r9 = queryParameters
    //     0x880354: add             x9, PP, #0xa, lsl #12  ; [pp+0xa3d8] Field <_BaseOptions&_RequestConfig&<EMAIL>>: late (offset: 0x4c)
    //     0x880358: ldr             x9, [x9, #0x3d8]
    // 0x88035c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x88035c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x880360: r9 = listFormat
    //     0x880360: add             x9, PP, #0xa, lsl #12  ; [pp+0xa3e0] Field <<EMAIL>>: late (offset: 0x44)
    //     0x880364: ldr             x9, [x9, #0x3e0]
    // 0x880368: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x880368: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ RequestOptions(/* No info */) {
    // ** addr: 0x884c2c, size: 0x870
    // 0x884c2c: EnterFrame
    //     0x884c2c: stp             fp, lr, [SP, #-0x10]!
    //     0x884c30: mov             fp, SP
    // 0x884c34: AllocStack(0x80)
    //     0x884c34: sub             SP, SP, #0x80
    // 0x884c38: SetupParameters(RequestOptions this /* r1 => r8, fp-0x40 */, {dynamic baseUrl = Null /* fp-0x8 */, dynamic cancelToken = Null /* r2 */, dynamic connectTimeout = Null /* fp-0x10 */, dynamic contentType = Null /* fp-0x20 */, dynamic data = Null /* r5 */, dynamic extra = Null /* fp-0x28 */, dynamic followRedirects = Null /* fp-0x30 */, dynamic headers = Null /* r11 */, dynamic listFormat = Null /* r12 */, dynamic maxRedirects = Null /* r13 */, dynamic method = Null /* r14 */, dynamic path = "" /* r19 */, dynamic persistentConnection = Null /* r20 */, dynamic preserveHeaderCase = Null /* r9 */, dynamic queryParameters = Null /* fp-0x18 */, dynamic receiveDataWhenStatusError = Null /* r10 */, dynamic receiveTimeout = Null /* r3 */, dynamic responseType = Null /* r6 */, dynamic sourceStackTrace = Null /* r23, fp-0x38 */, dynamic validateStatus = Null /* r1 */})
    //     0x884c38: mov             x8, x1
    //     0x884c3c: stur            x1, [fp, #-0x40]
    //     0x884c40: ldur            w0, [x4, #0x13]
    //     0x884c44: ldur            w1, [x4, #0x1f]
    //     0x884c48: add             x1, x1, HEAP, lsl #32
    //     0x884c4c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa7f0] "baseUrl"
    //     0x884c50: ldr             x16, [x16, #0x7f0]
    //     0x884c54: cmp             w1, w16
    //     0x884c58: b.ne            #0x884c7c
    //     0x884c5c: ldur            w1, [x4, #0x23]
    //     0x884c60: add             x1, x1, HEAP, lsl #32
    //     0x884c64: sub             w2, w0, w1
    //     0x884c68: add             x1, fp, w2, sxtw #2
    //     0x884c6c: ldr             x1, [x1, #8]
    //     0x884c70: mov             x9, x1
    //     0x884c74: movz            x1, #0x1
    //     0x884c78: b               #0x884c84
    //     0x884c7c: mov             x9, NULL
    //     0x884c80: movz            x1, #0
    //     0x884c84: stur            x9, [fp, #-8]
    //     0x884c88: lsl             x2, x1, #1
    //     0x884c8c: lsl             w3, w2, #1
    //     0x884c90: add             w5, w3, #8
    //     0x884c94: add             x16, x4, w5, sxtw #1
    //     0x884c98: ldur            w6, [x16, #0xf]
    //     0x884c9c: add             x6, x6, HEAP, lsl #32
    //     0x884ca0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa7f8] "cancelToken"
    //     0x884ca4: ldr             x16, [x16, #0x7f8]
    //     0x884ca8: cmp             w6, w16
    //     0x884cac: b.ne            #0x884ce0
    //     0x884cb0: add             w1, w3, #0xa
    //     0x884cb4: add             x16, x4, w1, sxtw #1
    //     0x884cb8: ldur            w3, [x16, #0xf]
    //     0x884cbc: add             x3, x3, HEAP, lsl #32
    //     0x884cc0: sub             w1, w0, w3
    //     0x884cc4: add             x3, fp, w1, sxtw #2
    //     0x884cc8: ldr             x3, [x3, #8]
    //     0x884ccc: add             w1, w2, #2
    //     0x884cd0: sbfx            x2, x1, #1, #0x1f
    //     0x884cd4: mov             x1, x2
    //     0x884cd8: mov             x2, x3
    //     0x884cdc: b               #0x884ce4
    //     0x884ce0: mov             x2, NULL
    //     0x884ce4: lsl             x3, x1, #1
    //     0x884ce8: lsl             w5, w3, #1
    //     0x884cec: add             w6, w5, #8
    //     0x884cf0: add             x16, x4, w6, sxtw #1
    //     0x884cf4: ldur            w7, [x16, #0xf]
    //     0x884cf8: add             x7, x7, HEAP, lsl #32
    //     0x884cfc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa800] "connectTimeout"
    //     0x884d00: ldr             x16, [x16, #0x800]
    //     0x884d04: cmp             w7, w16
    //     0x884d08: b.ne            #0x884d3c
    //     0x884d0c: add             w1, w5, #0xa
    //     0x884d10: add             x16, x4, w1, sxtw #1
    //     0x884d14: ldur            w5, [x16, #0xf]
    //     0x884d18: add             x5, x5, HEAP, lsl #32
    //     0x884d1c: sub             w1, w0, w5
    //     0x884d20: add             x5, fp, w1, sxtw #2
    //     0x884d24: ldr             x5, [x5, #8]
    //     0x884d28: add             w1, w3, #2
    //     0x884d2c: sbfx            x3, x1, #1, #0x1f
    //     0x884d30: mov             x10, x5
    //     0x884d34: mov             x1, x3
    //     0x884d38: b               #0x884d40
    //     0x884d3c: mov             x10, NULL
    //     0x884d40: stur            x10, [fp, #-0x10]
    //     0x884d44: lsl             x3, x1, #1
    //     0x884d48: lsl             w5, w3, #1
    //     0x884d4c: add             w6, w5, #8
    //     0x884d50: add             x16, x4, w6, sxtw #1
    //     0x884d54: ldur            w7, [x16, #0xf]
    //     0x884d58: add             x7, x7, HEAP, lsl #32
    //     0x884d5c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa808] "contentType"
    //     0x884d60: ldr             x16, [x16, #0x808]
    //     0x884d64: cmp             w7, w16
    //     0x884d68: b.ne            #0x884d9c
    //     0x884d6c: add             w1, w5, #0xa
    //     0x884d70: add             x16, x4, w1, sxtw #1
    //     0x884d74: ldur            w5, [x16, #0xf]
    //     0x884d78: add             x5, x5, HEAP, lsl #32
    //     0x884d7c: sub             w1, w0, w5
    //     0x884d80: add             x5, fp, w1, sxtw #2
    //     0x884d84: ldr             x5, [x5, #8]
    //     0x884d88: add             w1, w3, #2
    //     0x884d8c: sbfx            x3, x1, #1, #0x1f
    //     0x884d90: mov             x1, x3
    //     0x884d94: mov             x3, x5
    //     0x884d98: b               #0x884da0
    //     0x884d9c: mov             x3, NULL
    //     0x884da0: stur            x3, [fp, #-0x20]
    //     0x884da4: lsl             x5, x1, #1
    //     0x884da8: lsl             w6, w5, #1
    //     0x884dac: add             w7, w6, #8
    //     0x884db0: add             x16, x4, w7, sxtw #1
    //     0x884db4: ldur            w11, [x16, #0xf]
    //     0x884db8: add             x11, x11, HEAP, lsl #32
    //     0x884dbc: ldr             x16, [PP, #0x1318]  ; [pp+0x1318] "data"
    //     0x884dc0: cmp             w11, w16
    //     0x884dc4: b.ne            #0x884df8
    //     0x884dc8: add             w1, w6, #0xa
    //     0x884dcc: add             x16, x4, w1, sxtw #1
    //     0x884dd0: ldur            w6, [x16, #0xf]
    //     0x884dd4: add             x6, x6, HEAP, lsl #32
    //     0x884dd8: sub             w1, w0, w6
    //     0x884ddc: add             x6, fp, w1, sxtw #2
    //     0x884de0: ldr             x6, [x6, #8]
    //     0x884de4: add             w1, w5, #2
    //     0x884de8: sbfx            x5, x1, #1, #0x1f
    //     0x884dec: mov             x1, x5
    //     0x884df0: mov             x5, x6
    //     0x884df4: b               #0x884dfc
    //     0x884df8: mov             x5, NULL
    //     0x884dfc: lsl             x6, x1, #1
    //     0x884e00: lsl             w7, w6, #1
    //     0x884e04: add             w11, w7, #8
    //     0x884e08: add             x16, x4, w11, sxtw #1
    //     0x884e0c: ldur            w12, [x16, #0xf]
    //     0x884e10: add             x12, x12, HEAP, lsl #32
    //     0x884e14: add             x16, PP, #0xa, lsl #12  ; [pp+0xa778] "extra"
    //     0x884e18: ldr             x16, [x16, #0x778]
    //     0x884e1c: cmp             w12, w16
    //     0x884e20: b.ne            #0x884e54
    //     0x884e24: add             w1, w7, #0xa
    //     0x884e28: add             x16, x4, w1, sxtw #1
    //     0x884e2c: ldur            w7, [x16, #0xf]
    //     0x884e30: add             x7, x7, HEAP, lsl #32
    //     0x884e34: sub             w1, w0, w7
    //     0x884e38: add             x7, fp, w1, sxtw #2
    //     0x884e3c: ldr             x7, [x7, #8]
    //     0x884e40: add             w1, w6, #2
    //     0x884e44: sbfx            x6, x1, #1, #0x1f
    //     0x884e48: mov             x1, x6
    //     0x884e4c: mov             x6, x7
    //     0x884e50: b               #0x884e58
    //     0x884e54: mov             x6, NULL
    //     0x884e58: stur            x6, [fp, #-0x28]
    //     0x884e5c: lsl             x7, x1, #1
    //     0x884e60: lsl             w11, w7, #1
    //     0x884e64: add             w12, w11, #8
    //     0x884e68: add             x16, x4, w12, sxtw #1
    //     0x884e6c: ldur            w13, [x16, #0xf]
    //     0x884e70: add             x13, x13, HEAP, lsl #32
    //     0x884e74: add             x16, PP, #0xa, lsl #12  ; [pp+0xa810] "followRedirects"
    //     0x884e78: ldr             x16, [x16, #0x810]
    //     0x884e7c: cmp             w13, w16
    //     0x884e80: b.ne            #0x884eb4
    //     0x884e84: add             w1, w11, #0xa
    //     0x884e88: add             x16, x4, w1, sxtw #1
    //     0x884e8c: ldur            w11, [x16, #0xf]
    //     0x884e90: add             x11, x11, HEAP, lsl #32
    //     0x884e94: sub             w1, w0, w11
    //     0x884e98: add             x11, fp, w1, sxtw #2
    //     0x884e9c: ldr             x11, [x11, #8]
    //     0x884ea0: add             w1, w7, #2
    //     0x884ea4: sbfx            x7, x1, #1, #0x1f
    //     0x884ea8: mov             x1, x7
    //     0x884eac: mov             x7, x11
    //     0x884eb0: b               #0x884eb8
    //     0x884eb4: mov             x7, NULL
    //     0x884eb8: stur            x7, [fp, #-0x30]
    //     0x884ebc: lsl             x11, x1, #1
    //     0x884ec0: lsl             w12, w11, #1
    //     0x884ec4: add             w13, w12, #8
    //     0x884ec8: add             x16, x4, w13, sxtw #1
    //     0x884ecc: ldur            w14, [x16, #0xf]
    //     0x884ed0: add             x14, x14, HEAP, lsl #32
    //     0x884ed4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa780] "headers"
    //     0x884ed8: ldr             x16, [x16, #0x780]
    //     0x884edc: cmp             w14, w16
    //     0x884ee0: b.ne            #0x884f14
    //     0x884ee4: add             w1, w12, #0xa
    //     0x884ee8: add             x16, x4, w1, sxtw #1
    //     0x884eec: ldur            w12, [x16, #0xf]
    //     0x884ef0: add             x12, x12, HEAP, lsl #32
    //     0x884ef4: sub             w1, w0, w12
    //     0x884ef8: add             x12, fp, w1, sxtw #2
    //     0x884efc: ldr             x12, [x12, #8]
    //     0x884f00: add             w1, w11, #2
    //     0x884f04: sbfx            x11, x1, #1, #0x1f
    //     0x884f08: mov             x1, x11
    //     0x884f0c: mov             x11, x12
    //     0x884f10: b               #0x884f18
    //     0x884f14: mov             x11, NULL
    //     0x884f18: lsl             x12, x1, #1
    //     0x884f1c: lsl             w13, w12, #1
    //     0x884f20: add             w14, w13, #8
    //     0x884f24: add             x16, x4, w14, sxtw #1
    //     0x884f28: ldur            w19, [x16, #0xf]
    //     0x884f2c: add             x19, x19, HEAP, lsl #32
    //     0x884f30: add             x16, PP, #0xa, lsl #12  ; [pp+0xa818] "listFormat"
    //     0x884f34: ldr             x16, [x16, #0x818]
    //     0x884f38: cmp             w19, w16
    //     0x884f3c: b.ne            #0x884f70
    //     0x884f40: add             w1, w13, #0xa
    //     0x884f44: add             x16, x4, w1, sxtw #1
    //     0x884f48: ldur            w13, [x16, #0xf]
    //     0x884f4c: add             x13, x13, HEAP, lsl #32
    //     0x884f50: sub             w1, w0, w13
    //     0x884f54: add             x13, fp, w1, sxtw #2
    //     0x884f58: ldr             x13, [x13, #8]
    //     0x884f5c: add             w1, w12, #2
    //     0x884f60: sbfx            x12, x1, #1, #0x1f
    //     0x884f64: mov             x1, x12
    //     0x884f68: mov             x12, x13
    //     0x884f6c: b               #0x884f74
    //     0x884f70: mov             x12, NULL
    //     0x884f74: lsl             x13, x1, #1
    //     0x884f78: lsl             w14, w13, #1
    //     0x884f7c: add             w19, w14, #8
    //     0x884f80: add             x16, x4, w19, sxtw #1
    //     0x884f84: ldur            w20, [x16, #0xf]
    //     0x884f88: add             x20, x20, HEAP, lsl #32
    //     0x884f8c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa820] "maxRedirects"
    //     0x884f90: ldr             x16, [x16, #0x820]
    //     0x884f94: cmp             w20, w16
    //     0x884f98: b.ne            #0x884fcc
    //     0x884f9c: add             w1, w14, #0xa
    //     0x884fa0: add             x16, x4, w1, sxtw #1
    //     0x884fa4: ldur            w14, [x16, #0xf]
    //     0x884fa8: add             x14, x14, HEAP, lsl #32
    //     0x884fac: sub             w1, w0, w14
    //     0x884fb0: add             x14, fp, w1, sxtw #2
    //     0x884fb4: ldr             x14, [x14, #8]
    //     0x884fb8: add             w1, w13, #2
    //     0x884fbc: sbfx            x13, x1, #1, #0x1f
    //     0x884fc0: mov             x1, x13
    //     0x884fc4: mov             x13, x14
    //     0x884fc8: b               #0x884fd0
    //     0x884fcc: mov             x13, NULL
    //     0x884fd0: lsl             x14, x1, #1
    //     0x884fd4: lsl             w19, w14, #1
    //     0x884fd8: add             w20, w19, #8
    //     0x884fdc: add             x16, x4, w20, sxtw #1
    //     0x884fe0: ldur            w23, [x16, #0xf]
    //     0x884fe4: add             x23, x23, HEAP, lsl #32
    //     0x884fe8: add             x16, PP, #9, lsl #12  ; [pp+0x91e8] "method"
    //     0x884fec: ldr             x16, [x16, #0x1e8]
    //     0x884ff0: cmp             w23, w16
    //     0x884ff4: b.ne            #0x885028
    //     0x884ff8: add             w1, w19, #0xa
    //     0x884ffc: add             x16, x4, w1, sxtw #1
    //     0x885000: ldur            w19, [x16, #0xf]
    //     0x885004: add             x19, x19, HEAP, lsl #32
    //     0x885008: sub             w1, w0, w19
    //     0x88500c: add             x19, fp, w1, sxtw #2
    //     0x885010: ldr             x19, [x19, #8]
    //     0x885014: add             w1, w14, #2
    //     0x885018: sbfx            x14, x1, #1, #0x1f
    //     0x88501c: mov             x1, x14
    //     0x885020: mov             x14, x19
    //     0x885024: b               #0x88502c
    //     0x885028: mov             x14, NULL
    //     0x88502c: lsl             x19, x1, #1
    //     0x885030: lsl             w20, w19, #1
    //     0x885034: add             w23, w20, #8
    //     0x885038: add             x16, x4, w23, sxtw #1
    //     0x88503c: ldur            w24, [x16, #0xf]
    //     0x885040: add             x24, x24, HEAP, lsl #32
    //     0x885044: ldr             x16, [PP, #0x3548]  ; [pp+0x3548] "path"
    //     0x885048: cmp             w24, w16
    //     0x88504c: b.ne            #0x885080
    //     0x885050: add             w1, w20, #0xa
    //     0x885054: add             x16, x4, w1, sxtw #1
    //     0x885058: ldur            w20, [x16, #0xf]
    //     0x88505c: add             x20, x20, HEAP, lsl #32
    //     0x885060: sub             w1, w0, w20
    //     0x885064: add             x20, fp, w1, sxtw #2
    //     0x885068: ldr             x20, [x20, #8]
    //     0x88506c: add             w1, w19, #2
    //     0x885070: sbfx            x19, x1, #1, #0x1f
    //     0x885074: mov             x1, x19
    //     0x885078: mov             x19, x20
    //     0x88507c: b               #0x885084
    //     0x885080: ldr             x19, [PP, #0x8e0]  ; [pp+0x8e0] ""
    //     0x885084: lsl             x20, x1, #1
    //     0x885088: lsl             w23, w20, #1
    //     0x88508c: add             w24, w23, #8
    //     0x885090: add             x16, x4, w24, sxtw #1
    //     0x885094: ldur            w25, [x16, #0xf]
    //     0x885098: add             x25, x25, HEAP, lsl #32
    //     0x88509c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa828] "persistentConnection"
    //     0x8850a0: ldr             x16, [x16, #0x828]
    //     0x8850a4: cmp             w25, w16
    //     0x8850a8: b.ne            #0x8850dc
    //     0x8850ac: add             w1, w23, #0xa
    //     0x8850b0: add             x16, x4, w1, sxtw #1
    //     0x8850b4: ldur            w23, [x16, #0xf]
    //     0x8850b8: add             x23, x23, HEAP, lsl #32
    //     0x8850bc: sub             w1, w0, w23
    //     0x8850c0: add             x23, fp, w1, sxtw #2
    //     0x8850c4: ldr             x23, [x23, #8]
    //     0x8850c8: add             w1, w20, #2
    //     0x8850cc: sbfx            x20, x1, #1, #0x1f
    //     0x8850d0: mov             x1, x20
    //     0x8850d4: mov             x20, x23
    //     0x8850d8: b               #0x8850e0
    //     0x8850dc: mov             x20, NULL
    //     0x8850e0: lsl             x23, x1, #1
    //     0x8850e4: lsl             w24, w23, #1
    //     0x8850e8: add             w25, w24, #8
    //     0x8850ec: add             x16, x4, w25, sxtw #1
    //     0x8850f0: ldur            w9, [x16, #0xf]
    //     0x8850f4: add             x9, x9, HEAP, lsl #32
    //     0x8850f8: add             x16, PP, #9, lsl #12  ; [pp+0x9190] "preserveHeaderCase"
    //     0x8850fc: ldr             x16, [x16, #0x190]
    //     0x885100: cmp             w9, w16
    //     0x885104: b.ne            #0x885134
    //     0x885108: add             w1, w24, #0xa
    //     0x88510c: add             x16, x4, w1, sxtw #1
    //     0x885110: ldur            w9, [x16, #0xf]
    //     0x885114: add             x9, x9, HEAP, lsl #32
    //     0x885118: sub             w1, w0, w9
    //     0x88511c: add             x9, fp, w1, sxtw #2
    //     0x885120: ldr             x9, [x9, #8]
    //     0x885124: add             w1, w23, #2
    //     0x885128: sbfx            x23, x1, #1, #0x1f
    //     0x88512c: mov             x1, x23
    //     0x885130: b               #0x885138
    //     0x885134: mov             x9, NULL
    //     0x885138: lsl             x23, x1, #1
    //     0x88513c: lsl             w24, w23, #1
    //     0x885140: add             w25, w24, #8
    //     0x885144: add             x16, x4, w25, sxtw #1
    //     0x885148: ldur            w10, [x16, #0xf]
    //     0x88514c: add             x10, x10, HEAP, lsl #32
    //     0x885150: ldr             x16, [PP, #0x3568]  ; [pp+0x3568] "queryParameters"
    //     0x885154: cmp             w10, w16
    //     0x885158: b.ne            #0x885188
    //     0x88515c: add             w1, w24, #0xa
    //     0x885160: add             x16, x4, w1, sxtw #1
    //     0x885164: ldur            w10, [x16, #0xf]
    //     0x885168: add             x10, x10, HEAP, lsl #32
    //     0x88516c: sub             w1, w0, w10
    //     0x885170: add             x10, fp, w1, sxtw #2
    //     0x885174: ldr             x10, [x10, #8]
    //     0x885178: add             w1, w23, #2
    //     0x88517c: sbfx            x23, x1, #1, #0x1f
    //     0x885180: mov             x1, x23
    //     0x885184: b               #0x88518c
    //     0x885188: mov             x10, NULL
    //     0x88518c: stur            x10, [fp, #-0x18]
    //     0x885190: lsl             x23, x1, #1
    //     0x885194: lsl             w24, w23, #1
    //     0x885198: add             w25, w24, #8
    //     0x88519c: add             x16, x4, w25, sxtw #1
    //     0x8851a0: ldur            w10, [x16, #0xf]
    //     0x8851a4: add             x10, x10, HEAP, lsl #32
    //     0x8851a8: add             x16, PP, #0xa, lsl #12  ; [pp+0xa830] "receiveDataWhenStatusError"
    //     0x8851ac: ldr             x16, [x16, #0x830]
    //     0x8851b0: cmp             w10, w16
    //     0x8851b4: b.ne            #0x8851e4
    //     0x8851b8: add             w1, w24, #0xa
    //     0x8851bc: add             x16, x4, w1, sxtw #1
    //     0x8851c0: ldur            w10, [x16, #0xf]
    //     0x8851c4: add             x10, x10, HEAP, lsl #32
    //     0x8851c8: sub             w1, w0, w10
    //     0x8851cc: add             x10, fp, w1, sxtw #2
    //     0x8851d0: ldr             x10, [x10, #8]
    //     0x8851d4: add             w1, w23, #2
    //     0x8851d8: sbfx            x23, x1, #1, #0x1f
    //     0x8851dc: mov             x1, x23
    //     0x8851e0: b               #0x8851e8
    //     0x8851e4: mov             x10, NULL
    //     0x8851e8: lsl             x23, x1, #1
    //     0x8851ec: lsl             w24, w23, #1
    //     0x8851f0: add             w25, w24, #8
    //     0x8851f4: add             x16, x4, w25, sxtw #1
    //     0x8851f8: ldur            w3, [x16, #0xf]
    //     0x8851fc: add             x3, x3, HEAP, lsl #32
    //     0x885200: add             x16, PP, #0xa, lsl #12  ; [pp+0xa838] "receiveTimeout"
    //     0x885204: ldr             x16, [x16, #0x838]
    //     0x885208: cmp             w3, w16
    //     0x88520c: b.ne            #0x88523c
    //     0x885210: add             w1, w24, #0xa
    //     0x885214: add             x16, x4, w1, sxtw #1
    //     0x885218: ldur            w3, [x16, #0xf]
    //     0x88521c: add             x3, x3, HEAP, lsl #32
    //     0x885220: sub             w1, w0, w3
    //     0x885224: add             x3, fp, w1, sxtw #2
    //     0x885228: ldr             x3, [x3, #8]
    //     0x88522c: add             w1, w23, #2
    //     0x885230: sbfx            x23, x1, #1, #0x1f
    //     0x885234: mov             x1, x23
    //     0x885238: b               #0x885240
    //     0x88523c: mov             x3, NULL
    //     0x885240: lsl             x23, x1, #1
    //     0x885244: lsl             w24, w23, #1
    //     0x885248: add             w25, w24, #8
    //     0x88524c: add             x16, x4, w25, sxtw #1
    //     0x885250: ldur            w6, [x16, #0xf]
    //     0x885254: add             x6, x6, HEAP, lsl #32
    //     0x885258: add             x16, PP, #0xa, lsl #12  ; [pp+0xa840] "responseType"
    //     0x88525c: ldr             x16, [x16, #0x840]
    //     0x885260: cmp             w6, w16
    //     0x885264: b.ne            #0x885294
    //     0x885268: add             w1, w24, #0xa
    //     0x88526c: add             x16, x4, w1, sxtw #1
    //     0x885270: ldur            w6, [x16, #0xf]
    //     0x885274: add             x6, x6, HEAP, lsl #32
    //     0x885278: sub             w1, w0, w6
    //     0x88527c: add             x6, fp, w1, sxtw #2
    //     0x885280: ldr             x6, [x6, #8]
    //     0x885284: add             w1, w23, #2
    //     0x885288: sbfx            x23, x1, #1, #0x1f
    //     0x88528c: mov             x1, x23
    //     0x885290: b               #0x885298
    //     0x885294: mov             x6, NULL
    //     0x885298: lsl             x23, x1, #1
    //     0x88529c: lsl             w24, w23, #1
    //     0x8852a0: add             w25, w24, #8
    //     0x8852a4: add             x16, x4, w25, sxtw #1
    //     0x8852a8: ldur            w7, [x16, #0xf]
    //     0x8852ac: add             x7, x7, HEAP, lsl #32
    //     0x8852b0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa848] "sourceStackTrace"
    //     0x8852b4: ldr             x16, [x16, #0x848]
    //     0x8852b8: cmp             w7, w16
    //     0x8852bc: b.ne            #0x8852f0
    //     0x8852c0: add             w1, w24, #0xa
    //     0x8852c4: add             x16, x4, w1, sxtw #1
    //     0x8852c8: ldur            w7, [x16, #0xf]
    //     0x8852cc: add             x7, x7, HEAP, lsl #32
    //     0x8852d0: sub             w1, w0, w7
    //     0x8852d4: add             x7, fp, w1, sxtw #2
    //     0x8852d8: ldr             x7, [x7, #8]
    //     0x8852dc: add             w1, w23, #2
    //     0x8852e0: sbfx            x23, x1, #1, #0x1f
    //     0x8852e4: mov             x1, x23
    //     0x8852e8: mov             x23, x7
    //     0x8852ec: b               #0x8852f4
    //     0x8852f0: mov             x23, NULL
    //     0x8852f4: stur            x23, [fp, #-0x38]
    //     0x8852f8: lsl             x7, x1, #1
    //     0x8852fc: lsl             w1, w7, #1
    //     0x885300: add             w7, w1, #8
    //     0x885304: add             x16, x4, w7, sxtw #1
    //     0x885308: ldur            w24, [x16, #0xf]
    //     0x88530c: add             x24, x24, HEAP, lsl #32
    //     0x885310: add             x16, PP, #0xa, lsl #12  ; [pp+0xa850] "validateStatus"
    //     0x885314: ldr             x16, [x16, #0x850]
    //     0x885318: cmp             w24, w16
    //     0x88531c: b.ne            #0x885344
    //     0x885320: add             w7, w1, #0xa
    //     0x885324: add             x16, x4, w7, sxtw #1
    //     0x885328: ldur            w1, [x16, #0xf]
    //     0x88532c: add             x1, x1, HEAP, lsl #32
    //     0x885330: sub             w4, w0, w1
    //     0x885334: add             x0, fp, w4, sxtw #2
    //     0x885338: ldr             x0, [x0, #8]
    //     0x88533c: mov             x1, x0
    //     0x885340: b               #0x885348
    //     0x885344: mov             x1, NULL
    // 0x885348: CheckStackOverflow
    //     0x885348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88534c: cmp             SP, x16
    //     0x885350: b.ls            #0x885494
    // 0x885354: mov             x0, x19
    // 0x885358: StoreField: r8->field_5b = r0
    //     0x885358: stur            w0, [x8, #0x5b]
    //     0x88535c: ldurb           w16, [x8, #-1]
    //     0x885360: ldurb           w17, [x0, #-1]
    //     0x885364: and             x16, x17, x16, lsr #2
    //     0x885368: tst             x16, HEAP, lsr #32
    //     0x88536c: b.eq            #0x885374
    //     0x885370: bl              #0x16f5968  ; WriteBarrierWrappersStub
    // 0x885374: mov             x0, x5
    // 0x885378: StoreField: r8->field_57 = r0
    //     0x885378: stur            w0, [x8, #0x57]
    //     0x88537c: tbz             w0, #0, #0x885398
    //     0x885380: ldurb           w16, [x8, #-1]
    //     0x885384: ldurb           w17, [x0, #-1]
    //     0x885388: and             x16, x17, x16, lsr #2
    //     0x88538c: tst             x16, HEAP, lsr #32
    //     0x885390: b.eq            #0x885398
    //     0x885394: bl              #0x16f5968  ; WriteBarrierWrappersStub
    // 0x885398: mov             x0, x2
    // 0x88539c: StoreField: r8->field_5f = r0
    //     0x88539c: stur            w0, [x8, #0x5f]
    //     0x8853a0: ldurb           w16, [x8, #-1]
    //     0x8853a4: ldurb           w17, [x0, #-1]
    //     0x8853a8: and             x16, x17, x16, lsr #2
    //     0x8853ac: tst             x16, HEAP, lsr #32
    //     0x8853b0: b.eq            #0x8853b8
    //     0x8853b4: bl              #0x16f5968  ; WriteBarrierWrappersStub
    // 0x8853b8: stp             x14, x13, [SP, #0x30]
    // 0x8853bc: stp             x9, x20, [SP, #0x20]
    // 0x8853c0: stp             x3, x10, [SP, #0x10]
    // 0x8853c4: stp             x1, x6, [SP]
    // 0x8853c8: mov             x1, x8
    // 0x8853cc: ldur            x2, [fp, #-0x20]
    // 0x8853d0: ldur            x3, [fp, #-0x28]
    // 0x8853d4: ldur            x5, [fp, #-0x30]
    // 0x8853d8: mov             x6, x11
    // 0x8853dc: mov             x7, x12
    // 0x8853e0: r0 = _BaseOptions&_RequestConfig&OptionsMixin()
    //     0x8853e0: bl              #0x8855e4  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::_BaseOptions&_RequestConfig&OptionsMixin
    // 0x8853e4: ldur            x0, [fp, #-0x38]
    // 0x8853e8: cmp             w0, NULL
    // 0x8853ec: b.ne            #0x8853f4
    // 0x8853f0: r0 = current()
    //     0x8853f0: bl              #0x61b99c  ; [dart:core] StackTrace::current
    // 0x8853f4: ldur            x1, [fp, #-0x40]
    // 0x8853f8: ldur            x2, [fp, #-0x18]
    // 0x8853fc: StoreField: r1->field_53 = r0
    //     0x8853fc: stur            w0, [x1, #0x53]
    //     0x885400: ldurb           w16, [x1, #-1]
    //     0x885404: ldurb           w17, [x0, #-1]
    //     0x885408: and             x16, x17, x16, lsr #2
    //     0x88540c: tst             x16, HEAP, lsr #32
    //     0x885410: b.eq            #0x885418
    //     0x885414: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x885418: cmp             w2, NULL
    // 0x88541c: b.ne            #0x885434
    // 0x885420: r16 = <String, dynamic>
    //     0x885420: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x885424: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x885428: stp             lr, x16, [SP]
    // 0x88542c: r0 = Map._fromLiteral()
    //     0x88542c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x885430: b               #0x885438
    // 0x885434: mov             x0, x2
    // 0x885438: ldur            x3, [fp, #-0x40]
    // 0x88543c: ldur            x1, [fp, #-8]
    // 0x885440: StoreField: r3->field_4b = r0
    //     0x885440: stur            w0, [x3, #0x4b]
    //     0x885444: ldurb           w16, [x3, #-1]
    //     0x885448: ldurb           w17, [x0, #-1]
    //     0x88544c: and             x16, x17, x16, lsr #2
    //     0x885450: tst             x16, HEAP, lsr #32
    //     0x885454: b.eq            #0x88545c
    //     0x885458: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x88545c: cmp             w1, NULL
    // 0x885460: b.ne            #0x88546c
    // 0x885464: r2 = ""
    //     0x885464: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x885468: b               #0x885470
    // 0x88546c: mov             x2, x1
    // 0x885470: mov             x1, x3
    // 0x885474: r0 = baseUrl=()
    //     0x885474: bl              #0x885504  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::baseUrl=
    // 0x885478: ldur            x1, [fp, #-0x40]
    // 0x88547c: ldur            x2, [fp, #-0x10]
    // 0x885480: r0 = connectTimeout=()
    //     0x885480: bl              #0x88549c  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::connectTimeout=
    // 0x885484: r0 = Null
    //     0x885484: mov             x0, NULL
    // 0x885488: LeaveFrame
    //     0x885488: mov             SP, fp
    //     0x88548c: ldp             fp, lr, [SP], #0x10
    // 0x885490: ret
    //     0x885490: ret             
    // 0x885494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x885494: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x885498: b               #0x885354
  }
}

// class id: 4978, size: 0x54, field offset: 0x54
class BaseOptions extends _BaseOptions&_RequestConfig&OptionsMixin {

  _ BaseOptions(/* No info */) {
    // ** addr: 0x888ba0, size: 0x1a8
    // 0x888ba0: EnterFrame
    //     0x888ba0: stp             fp, lr, [SP, #-0x10]!
    //     0x888ba4: mov             fp, SP
    // 0x888ba8: AllocStack(0x58)
    //     0x888ba8: sub             SP, SP, #0x58
    // 0x888bac: SetupParameters(BaseOptions this /* r1 => r0, fp-0x18 */, {dynamic baseUrl = "" /* r8, fp-0x10 */, dynamic connectTimeout = Null /* r9, fp-0x8 */, dynamic receiveTimeout = Null /* r1 */})
    //     0x888bac: mov             x0, x1
    //     0x888bb0: stur            x1, [fp, #-0x18]
    //     0x888bb4: ldur            w1, [x4, #0x13]
    //     0x888bb8: ldur            w2, [x4, #0x1f]
    //     0x888bbc: add             x2, x2, HEAP, lsl #32
    //     0x888bc0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa7f0] "baseUrl"
    //     0x888bc4: ldr             x16, [x16, #0x7f0]
    //     0x888bc8: cmp             w2, w16
    //     0x888bcc: b.ne            #0x888bf0
    //     0x888bd0: ldur            w2, [x4, #0x23]
    //     0x888bd4: add             x2, x2, HEAP, lsl #32
    //     0x888bd8: sub             w3, w1, w2
    //     0x888bdc: add             x2, fp, w3, sxtw #2
    //     0x888be0: ldr             x2, [x2, #8]
    //     0x888be4: mov             x8, x2
    //     0x888be8: movz            x2, #0x1
    //     0x888bec: b               #0x888bf8
    //     0x888bf0: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    //     0x888bf4: movz            x2, #0
    //     0x888bf8: stur            x8, [fp, #-0x10]
    //     0x888bfc: lsl             x3, x2, #1
    //     0x888c00: lsl             w5, w3, #1
    //     0x888c04: add             w6, w5, #8
    //     0x888c08: add             x16, x4, w6, sxtw #1
    //     0x888c0c: ldur            w7, [x16, #0xf]
    //     0x888c10: add             x7, x7, HEAP, lsl #32
    //     0x888c14: add             x16, PP, #0xa, lsl #12  ; [pp+0xa800] "connectTimeout"
    //     0x888c18: ldr             x16, [x16, #0x800]
    //     0x888c1c: cmp             w7, w16
    //     0x888c20: b.ne            #0x888c54
    //     0x888c24: add             w2, w5, #0xa
    //     0x888c28: add             x16, x4, w2, sxtw #1
    //     0x888c2c: ldur            w5, [x16, #0xf]
    //     0x888c30: add             x5, x5, HEAP, lsl #32
    //     0x888c34: sub             w2, w1, w5
    //     0x888c38: add             x5, fp, w2, sxtw #2
    //     0x888c3c: ldr             x5, [x5, #8]
    //     0x888c40: add             w2, w3, #2
    //     0x888c44: sbfx            x3, x2, #1, #0x1f
    //     0x888c48: mov             x9, x5
    //     0x888c4c: mov             x2, x3
    //     0x888c50: b               #0x888c58
    //     0x888c54: mov             x9, NULL
    //     0x888c58: stur            x9, [fp, #-8]
    //     0x888c5c: lsl             x3, x2, #1
    //     0x888c60: lsl             w2, w3, #1
    //     0x888c64: add             w3, w2, #8
    //     0x888c68: add             x16, x4, w3, sxtw #1
    //     0x888c6c: ldur            w5, [x16, #0xf]
    //     0x888c70: add             x5, x5, HEAP, lsl #32
    //     0x888c74: add             x16, PP, #0xa, lsl #12  ; [pp+0xa838] "receiveTimeout"
    //     0x888c78: ldr             x16, [x16, #0x838]
    //     0x888c7c: cmp             w5, w16
    //     0x888c80: b.ne            #0x888ca4
    //     0x888c84: add             w3, w2, #0xa
    //     0x888c88: add             x16, x4, w3, sxtw #1
    //     0x888c8c: ldur            w2, [x16, #0xf]
    //     0x888c90: add             x2, x2, HEAP, lsl #32
    //     0x888c94: sub             w3, w1, w2
    //     0x888c98: add             x1, fp, w3, sxtw #2
    //     0x888c9c: ldr             x1, [x1, #8]
    //     0x888ca0: b               #0x888ca8
    //     0x888ca4: mov             x1, NULL
    // 0x888ca8: CheckStackOverflow
    //     0x888ca8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x888cac: cmp             SP, x16
    //     0x888cb0: b.ls            #0x888d40
    // 0x888cb4: stp             NULL, NULL, [SP, #0x30]
    // 0x888cb8: r16 = false
    //     0x888cb8: add             x16, NULL, #0x30  ; false
    // 0x888cbc: stp             x16, NULL, [SP, #0x20]
    // 0x888cc0: stp             x1, NULL, [SP, #0x10]
    // 0x888cc4: r16 = Instance_ResponseType
    //     0x888cc4: add             x16, PP, #8, lsl #12  ; [pp+0x8348] Obj!ResponseType@d750e1
    //     0x888cc8: ldr             x16, [x16, #0x348]
    // 0x888ccc: stp             NULL, x16, [SP]
    // 0x888cd0: mov             x1, x0
    // 0x888cd4: r2 = Null
    //     0x888cd4: mov             x2, NULL
    // 0x888cd8: r3 = Null
    //     0x888cd8: mov             x3, NULL
    // 0x888cdc: r5 = Null
    //     0x888cdc: mov             x5, NULL
    // 0x888ce0: r6 = Null
    //     0x888ce0: mov             x6, NULL
    // 0x888ce4: r7 = Null
    //     0x888ce4: mov             x7, NULL
    // 0x888ce8: r0 = _BaseOptions&_RequestConfig&OptionsMixin()
    //     0x888ce8: bl              #0x8855e4  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::_BaseOptions&_RequestConfig&OptionsMixin
    // 0x888cec: ldur            x1, [fp, #-0x18]
    // 0x888cf0: ldur            x2, [fp, #-0x10]
    // 0x888cf4: r0 = baseUrl=()
    //     0x888cf4: bl              #0x885504  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::baseUrl=
    // 0x888cf8: r16 = <String, dynamic>
    //     0x888cf8: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x888cfc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x888d00: stp             lr, x16, [SP]
    // 0x888d04: r0 = Map._fromLiteral()
    //     0x888d04: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x888d08: ldur            x1, [fp, #-0x18]
    // 0x888d0c: StoreField: r1->field_4b = r0
    //     0x888d0c: stur            w0, [x1, #0x4b]
    //     0x888d10: ldurb           w16, [x1, #-1]
    //     0x888d14: ldurb           w17, [x0, #-1]
    //     0x888d18: and             x16, x17, x16, lsr #2
    //     0x888d1c: tst             x16, HEAP, lsr #32
    //     0x888d20: b.eq            #0x888d28
    //     0x888d24: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x888d28: ldur            x2, [fp, #-8]
    // 0x888d2c: r0 = connectTimeout=()
    //     0x888d2c: bl              #0x88549c  ; [package:dio/src/options.dart] _BaseOptions&_RequestConfig&OptionsMixin::connectTimeout=
    // 0x888d30: r0 = Null
    //     0x888d30: mov             x0, NULL
    // 0x888d34: LeaveFrame
    //     0x888d34: mov             SP, fp
    //     0x888d38: ldp             fp, lr, [SP], #0x10
    // 0x888d3c: ret
    //     0x888d3c: ret             
    // 0x888d40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x888d40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x888d44: b               #0x888cb4
  }
}

// class id: 4979, size: 0x48, field offset: 0x8
class Options extends Object {

  _ compose(/* No info */) {
    // ** addr: 0x88474c, size: 0x4e0
    // 0x88474c: EnterFrame
    //     0x88474c: stp             fp, lr, [SP, #-0x10]!
    //     0x884750: mov             fp, SP
    // 0x884754: AllocStack(0x110)
    //     0x884754: sub             SP, SP, #0x110
    // 0x884758: SetupParameters(Options this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r2, fp-0x30 */)
    //     0x884758: mov             x0, x2
    //     0x88475c: stur            x2, [fp, #-0x10]
    //     0x884760: mov             x2, x7
    //     0x884764: stur            x1, [fp, #-8]
    //     0x884768: stur            x3, [fp, #-0x18]
    //     0x88476c: stur            x5, [fp, #-0x20]
    //     0x884770: stur            x6, [fp, #-0x28]
    //     0x884774: stur            x7, [fp, #-0x30]
    // 0x884778: CheckStackOverflow
    //     0x884778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88477c: cmp             SP, x16
    //     0x884780: b.ls            #0x884b88
    // 0x884784: r16 = <String, dynamic>
    //     0x884784: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x884788: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x88478c: stp             lr, x16, [SP]
    // 0x884790: r0 = Map._fromLiteral()
    //     0x884790: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x884794: mov             x3, x0
    // 0x884798: ldur            x0, [fp, #-0x10]
    // 0x88479c: stur            x3, [fp, #-0x38]
    // 0x8847a0: LoadField: r2 = r0->field_4b
    //     0x8847a0: ldur            w2, [x0, #0x4b]
    // 0x8847a4: DecompressPointer r2
    //     0x8847a4: add             x2, x2, HEAP, lsl #32
    // 0x8847a8: r16 = Sentinel
    //     0x8847a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8847ac: cmp             w2, w16
    // 0x8847b0: b.eq            #0x884b90
    // 0x8847b4: mov             x1, x3
    // 0x8847b8: r0 = addAll()
    //     0x8847b8: bl              #0x16e31f4  ; [dart:_compact_hash] _Map::addAll
    // 0x8847bc: ldur            x2, [fp, #-0x30]
    // 0x8847c0: cmp             w2, NULL
    // 0x8847c4: b.eq            #0x8847d0
    // 0x8847c8: ldur            x1, [fp, #-0x38]
    // 0x8847cc: r0 = addAll()
    //     0x8847cc: bl              #0x16e31f4  ; [dart:_compact_hash] _Map::addAll
    // 0x8847d0: ldur            x1, [fp, #-8]
    // 0x8847d4: ldur            x0, [fp, #-0x10]
    // 0x8847d8: LoadField: r2 = r0->field_b
    //     0x8847d8: ldur            w2, [x0, #0xb]
    // 0x8847dc: DecompressPointer r2
    //     0x8847dc: add             x2, x2, HEAP, lsl #32
    // 0x8847e0: r16 = Sentinel
    //     0x8847e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8847e4: cmp             w2, w16
    // 0x8847e8: b.eq            #0x884b9c
    // 0x8847ec: stp             x2, NULL, [SP]
    // 0x8847f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8847f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8847f4: r0 = caseInsensitiveKeyMap()
    //     0x8847f4: bl              #0x8626d4  ; [package:dio/src/utils.dart] ::caseInsensitiveKeyMap
    // 0x8847f8: mov             x5, x0
    // 0x8847fc: ldur            x4, [fp, #-8]
    // 0x884800: stur            x5, [fp, #-0x30]
    // 0x884804: LoadField: r3 = r4->field_1b
    //     0x884804: ldur            w3, [x4, #0x1b]
    // 0x884808: DecompressPointer r3
    //     0x884808: add             x3, x3, HEAP, lsl #32
    // 0x88480c: cmp             w3, NULL
    // 0x884810: b.eq            #0x884834
    // 0x884814: r0 = LoadClassIdInstr(r5)
    //     0x884814: ldur            x0, [x5, #-1]
    //     0x884818: ubfx            x0, x0, #0xc, #0x14
    // 0x88481c: mov             x1, x5
    // 0x884820: r2 = "content-type"
    //     0x884820: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x884824: ldr             x2, [x2, #0x568]
    // 0x884828: r0 = GDT[cid_x0 + 0x35a]()
    //     0x884828: add             lr, x0, #0x35a
    //     0x88482c: ldr             lr, [x21, lr, lsl #3]
    //     0x884830: blr             lr
    // 0x884834: ldur            x3, [fp, #-8]
    // 0x884838: ldur            x5, [fp, #-0x10]
    // 0x88483c: ldur            x4, [fp, #-0x30]
    // 0x884840: r0 = LoadClassIdInstr(r4)
    //     0x884840: ldur            x0, [x4, #-1]
    //     0x884844: ubfx            x0, x0, #0xc, #0x14
    // 0x884848: mov             x1, x4
    // 0x88484c: r2 = "content-type"
    //     0x88484c: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x884850: ldr             x2, [x2, #0x568]
    // 0x884854: r0 = GDT[cid_x0 + -0xfe]()
    //     0x884854: sub             lr, x0, #0xfe
    //     0x884858: ldr             lr, [x21, lr, lsl #3]
    //     0x88485c: blr             lr
    // 0x884860: mov             x3, x0
    // 0x884864: r2 = Null
    //     0x884864: mov             x2, NULL
    // 0x884868: r1 = Null
    //     0x884868: mov             x1, NULL
    // 0x88486c: stur            x3, [fp, #-0x40]
    // 0x884870: r4 = 60
    //     0x884870: movz            x4, #0x3c
    // 0x884874: branchIfSmi(r0, 0x884880)
    //     0x884874: tbz             w0, #0, #0x884880
    // 0x884878: r4 = LoadClassIdInstr(r0)
    //     0x884878: ldur            x4, [x0, #-1]
    //     0x88487c: ubfx            x4, x4, #0xc, #0x14
    // 0x884880: sub             x4, x4, #0x5e
    // 0x884884: cmp             x4, #1
    // 0x884888: b.ls            #0x88489c
    // 0x88488c: r8 = String?
    //     0x88488c: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x884890: r3 = Null
    //     0x884890: add             x3, PP, #0xa, lsl #12  ; [pp+0xa7b0] Null
    //     0x884894: ldr             x3, [x3, #0x7b0]
    // 0x884898: r0 = String?()
    //     0x884898: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x88489c: ldur            x0, [fp, #-0x10]
    // 0x8848a0: LoadField: r2 = r0->field_2b
    //     0x8848a0: ldur            w2, [x0, #0x2b]
    // 0x8848a4: DecompressPointer r2
    //     0x8848a4: add             x2, x2, HEAP, lsl #32
    // 0x8848a8: r16 = Sentinel
    //     0x8848a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8848ac: cmp             w2, w16
    // 0x8848b0: b.eq            #0x884ba8
    // 0x8848b4: r1 = <String, dynamic>
    //     0x8848b4: ldr             x1, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x8848b8: r0 = LinkedHashMap.from()
    //     0x8848b8: bl              #0x68ae6c  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0x8848bc: mov             x2, x0
    // 0x8848c0: ldur            x1, [fp, #-8]
    // 0x8848c4: stur            x2, [fp, #-0x48]
    // 0x8848c8: LoadField: r0 = r1->field_7
    //     0x8848c8: ldur            w0, [x1, #7]
    // 0x8848cc: DecompressPointer r0
    //     0x8848cc: add             x0, x0, HEAP, lsl #32
    // 0x8848d0: cmp             w0, NULL
    // 0x8848d4: b.ne            #0x8848f4
    // 0x8848d8: ldur            x3, [fp, #-0x10]
    // 0x8848dc: LoadField: r0 = r3->field_7
    //     0x8848dc: ldur            w0, [x3, #7]
    // 0x8848e0: DecompressPointer r0
    //     0x8848e0: add             x0, x0, HEAP, lsl #32
    // 0x8848e4: r16 = Sentinel
    //     0x8848e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8848e8: cmp             w0, w16
    // 0x8848ec: b.eq            #0x884bb4
    // 0x8848f0: b               #0x8848f8
    // 0x8848f4: ldur            x3, [fp, #-0x10]
    // 0x8848f8: r4 = LoadClassIdInstr(r0)
    //     0x8848f8: ldur            x4, [x0, #-1]
    //     0x8848fc: ubfx            x4, x4, #0xc, #0x14
    // 0x884900: str             x0, [SP]
    // 0x884904: mov             x0, x4
    // 0x884908: r0 = GDT[cid_x0 + -0x1000]()
    //     0x884908: sub             lr, x0, #1, lsl #12
    //     0x88490c: ldr             lr, [x21, lr, lsl #3]
    //     0x884910: blr             lr
    // 0x884914: mov             x3, x0
    // 0x884918: ldur            x0, [fp, #-0x10]
    // 0x88491c: stur            x3, [fp, #-0x70]
    // 0x884920: LoadField: r4 = r0->field_47
    //     0x884920: ldur            w4, [x0, #0x47]
    // 0x884924: DecompressPointer r4
    //     0x884924: add             x4, x4, HEAP, lsl #32
    // 0x884928: r16 = Sentinel
    //     0x884928: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x88492c: cmp             w4, w16
    // 0x884930: b.eq            #0x884bc0
    // 0x884934: stur            x4, [fp, #-0x68]
    // 0x884938: LoadField: r1 = r0->field_f
    //     0x884938: ldur            w1, [x0, #0xf]
    // 0x88493c: DecompressPointer r1
    //     0x88493c: add             x1, x1, HEAP, lsl #32
    // 0x884940: r16 = Sentinel
    //     0x884940: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x884944: cmp             w1, w16
    // 0x884948: b.eq            #0x884bcc
    // 0x88494c: LoadField: r5 = r0->field_4f
    //     0x88494c: ldur            w5, [x0, #0x4f]
    // 0x884950: DecompressPointer r5
    //     0x884950: add             x5, x5, HEAP, lsl #32
    // 0x884954: stur            x5, [fp, #-0x60]
    // 0x884958: ArrayLoad: r6 = r0[0]  ; List_4
    //     0x884958: ldur            w6, [x0, #0x17]
    // 0x88495c: DecompressPointer r6
    //     0x88495c: add             x6, x6, HEAP, lsl #32
    // 0x884960: ldur            x1, [fp, #-8]
    // 0x884964: stur            x6, [fp, #-0x58]
    // 0x884968: LoadField: r2 = r1->field_1f
    //     0x884968: ldur            w2, [x1, #0x1f]
    // 0x88496c: DecompressPointer r2
    //     0x88496c: add             x2, x2, HEAP, lsl #32
    // 0x884970: cmp             w2, NULL
    // 0x884974: b.ne            #0x884994
    // 0x884978: LoadField: r2 = r0->field_1f
    //     0x884978: ldur            w2, [x0, #0x1f]
    // 0x88497c: DecompressPointer r2
    //     0x88497c: add             x2, x2, HEAP, lsl #32
    // 0x884980: r16 = Sentinel
    //     0x884980: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x884984: cmp             w2, w16
    // 0x884988: b.eq            #0x884bd8
    // 0x88498c: mov             x7, x2
    // 0x884990: b               #0x884998
    // 0x884994: mov             x7, x2
    // 0x884998: ldur            x2, [fp, #-0x40]
    // 0x88499c: stur            x7, [fp, #-0x50]
    // 0x8849a0: LoadField: r8 = r0->field_23
    //     0x8849a0: ldur            w8, [x0, #0x23]
    // 0x8849a4: DecompressPointer r8
    //     0x8849a4: add             x8, x8, HEAP, lsl #32
    // 0x8849a8: r16 = Sentinel
    //     0x8849a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8849ac: cmp             w8, w16
    // 0x8849b0: b.eq            #0x884be4
    // 0x8849b4: LoadField: r8 = r0->field_27
    //     0x8849b4: ldur            w8, [x0, #0x27]
    // 0x8849b8: DecompressPointer r8
    //     0x8849b8: add             x8, x8, HEAP, lsl #32
    // 0x8849bc: r16 = Sentinel
    //     0x8849bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8849c0: cmp             w8, w16
    // 0x8849c4: b.eq            #0x884bf0
    // 0x8849c8: LoadField: r8 = r0->field_2f
    //     0x8849c8: ldur            w8, [x0, #0x2f]
    // 0x8849cc: DecompressPointer r8
    //     0x8849cc: add             x8, x8, HEAP, lsl #32
    // 0x8849d0: r16 = Sentinel
    //     0x8849d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8849d4: cmp             w8, w16
    // 0x8849d8: b.eq            #0x884bfc
    // 0x8849dc: LoadField: r8 = r0->field_33
    //     0x8849dc: ldur            w8, [x0, #0x33]
    // 0x8849e0: DecompressPointer r8
    //     0x8849e0: add             x8, x8, HEAP, lsl #32
    // 0x8849e4: r16 = Sentinel
    //     0x8849e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8849e8: cmp             w8, w16
    // 0x8849ec: b.eq            #0x884c08
    // 0x8849f0: LoadField: r8 = r0->field_37
    //     0x8849f0: ldur            w8, [x0, #0x37]
    // 0x8849f4: DecompressPointer r8
    //     0x8849f4: add             x8, x8, HEAP, lsl #32
    // 0x8849f8: r16 = Sentinel
    //     0x8849f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8849fc: cmp             w8, w16
    // 0x884a00: b.eq            #0x884c14
    // 0x884a04: LoadField: r8 = r0->field_43
    //     0x884a04: ldur            w8, [x0, #0x43]
    // 0x884a08: DecompressPointer r8
    //     0x884a08: add             x8, x8, HEAP, lsl #32
    // 0x884a0c: r16 = Sentinel
    //     0x884a0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x884a10: cmp             w8, w16
    // 0x884a14: b.eq            #0x884c20
    // 0x884a18: cmp             w2, NULL
    // 0x884a1c: b.ne            #0x884a30
    // 0x884a20: LoadField: r2 = r1->field_1b
    //     0x884a20: ldur            w2, [x1, #0x1b]
    // 0x884a24: DecompressPointer r2
    //     0x884a24: add             x2, x2, HEAP, lsl #32
    // 0x884a28: mov             x1, x2
    // 0x884a2c: b               #0x884a34
    // 0x884a30: mov             x1, x2
    // 0x884a34: cmp             w1, NULL
    // 0x884a38: b.ne            #0x884aa4
    // 0x884a3c: LoadField: r1 = r0->field_b
    //     0x884a3c: ldur            w1, [x0, #0xb]
    // 0x884a40: DecompressPointer r1
    //     0x884a40: add             x1, x1, HEAP, lsl #32
    // 0x884a44: r0 = LoadClassIdInstr(r1)
    //     0x884a44: ldur            x0, [x1, #-1]
    //     0x884a48: ubfx            x0, x0, #0xc, #0x14
    // 0x884a4c: r2 = "content-type"
    //     0x884a4c: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x884a50: ldr             x2, [x2, #0x568]
    // 0x884a54: r0 = GDT[cid_x0 + -0xfe]()
    //     0x884a54: sub             lr, x0, #0xfe
    //     0x884a58: ldr             lr, [x21, lr, lsl #3]
    //     0x884a5c: blr             lr
    // 0x884a60: mov             x3, x0
    // 0x884a64: r2 = Null
    //     0x884a64: mov             x2, NULL
    // 0x884a68: r1 = Null
    //     0x884a68: mov             x1, NULL
    // 0x884a6c: stur            x3, [fp, #-8]
    // 0x884a70: r4 = 60
    //     0x884a70: movz            x4, #0x3c
    // 0x884a74: branchIfSmi(r0, 0x884a80)
    //     0x884a74: tbz             w0, #0, #0x884a80
    // 0x884a78: r4 = LoadClassIdInstr(r0)
    //     0x884a78: ldur            x4, [x0, #-1]
    //     0x884a7c: ubfx            x4, x4, #0xc, #0x14
    // 0x884a80: sub             x4, x4, #0x5e
    // 0x884a84: cmp             x4, #1
    // 0x884a88: b.ls            #0x884a9c
    // 0x884a8c: r8 = String?
    //     0x884a8c: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x884a90: r3 = Null
    //     0x884a90: add             x3, PP, #0xa, lsl #12  ; [pp+0xa7c0] Null
    //     0x884a94: ldr             x3, [x3, #0x7c0]
    // 0x884a98: r0 = String?()
    //     0x884a98: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x884a9c: ldur            x0, [fp, #-8]
    // 0x884aa0: b               #0x884aa8
    // 0x884aa4: mov             x0, x1
    // 0x884aa8: stur            x0, [fp, #-8]
    // 0x884aac: r0 = RequestOptions()
    //     0x884aac: bl              #0x885b48  ; AllocateRequestOptionsStub -> RequestOptions (size=0x6c)
    // 0x884ab0: stur            x0, [fp, #-0x10]
    // 0x884ab4: ldur            x16, [fp, #-0x70]
    // 0x884ab8: ldur            lr, [fp, #-0x30]
    // 0x884abc: stp             lr, x16, [SP, #0x90]
    // 0x884ac0: ldur            x16, [fp, #-0x48]
    // 0x884ac4: ldur            lr, [fp, #-0x68]
    // 0x884ac8: stp             lr, x16, [SP, #0x80]
    // 0x884acc: ldur            x16, [fp, #-0x18]
    // 0x884ad0: ldur            lr, [fp, #-0x28]
    // 0x884ad4: stp             lr, x16, [SP, #0x70]
    // 0x884ad8: r16 = false
    //     0x884ad8: add             x16, NULL, #0x30  ; false
    // 0x884adc: ldr             lr, [fp, #0x10]
    // 0x884ae0: stp             lr, x16, [SP, #0x60]
    // 0x884ae4: ldur            x16, [fp, #-0x60]
    // 0x884ae8: ldur            lr, [fp, #-0x58]
    // 0x884aec: stp             lr, x16, [SP, #0x50]
    // 0x884af0: ldur            x16, [fp, #-0x50]
    // 0x884af4: r30 = Closure: (int?) => bool from Function '_defaultValidateStatus@914184022': static.
    //     0x884af4: add             lr, PP, #0xa, lsl #12  ; [pp+0xa7d0] Closure: (int?) => bool from Function '_defaultValidateStatus@914184022': static. (0x7fa737885b54)
    //     0x884af8: ldr             lr, [lr, #0x7d0]
    // 0x884afc: stp             lr, x16, [SP, #0x40]
    // 0x884b00: r16 = true
    //     0x884b00: add             x16, NULL, #0x20  ; true
    // 0x884b04: r30 = true
    //     0x884b04: add             lr, NULL, #0x20  ; true
    // 0x884b08: stp             lr, x16, [SP, #0x30]
    // 0x884b0c: r16 = 10
    //     0x884b0c: movz            x16, #0xa
    // 0x884b10: r30 = true
    //     0x884b10: add             lr, NULL, #0x20  ; true
    // 0x884b14: stp             lr, x16, [SP, #0x20]
    // 0x884b18: ldur            x16, [fp, #-0x38]
    // 0x884b1c: r30 = Instance_ListFormat
    //     0x884b1c: add             lr, PP, #0xa, lsl #12  ; [pp+0xa7d8] Obj!ListFormat@d750c1
    //     0x884b20: ldr             lr, [lr, #0x7d8]
    // 0x884b24: stp             lr, x16, [SP, #0x10]
    // 0x884b28: ldur            x16, [fp, #-0x20]
    // 0x884b2c: ldur            lr, [fp, #-8]
    // 0x884b30: stp             lr, x16, [SP]
    // 0x884b34: mov             x1, x0
    // 0x884b38: r4 = const [0, 0x15, 0x14, 0x1, baseUrl, 0x4, cancelToken, 0x13, connectTimeout, 0x9, contentType, 0x14, data, 0x6, extra, 0x3, followRedirects, 0xe, headers, 0x2, listFormat, 0x12, maxRedirects, 0xf, method, 0x1, path, 0x5, persistentConnection, 0x10, preserveHeaderCase, 0x7, queryParameters, 0x11, receiveDataWhenStatusError, 0xd, receiveTimeout, 0xa, responseType, 0xb, sourceStackTrace, 0x8, validateStatus, 0xc, null]
    //     0x884b38: add             x4, PP, #0xa, lsl #12  ; [pp+0xa7e0] List(45) [0, 0x15, 0x14, 0x1, "baseUrl", 0x4, "cancelToken", 0x13, "connectTimeout", 0x9, "contentType", 0x14, "data", 0x6, "extra", 0x3, "followRedirects", 0xe, "headers", 0x2, "listFormat", 0x12, "maxRedirects", 0xf, "method", 0x1, "path", 0x5, "persistentConnection", 0x10, "preserveHeaderCase", 0x7, "queryParameters", 0x11, "receiveDataWhenStatusError", 0xd, "receiveTimeout", 0xa, "responseType", 0xb, "sourceStackTrace", 0x8, "validateStatus", 0xc, Null]
    //     0x884b3c: ldr             x4, [x4, #0x7e0]
    // 0x884b40: r0 = RequestOptions()
    //     0x884b40: bl              #0x884c2c  ; [package:dio/src/options.dart] RequestOptions::RequestOptions
    // 0x884b44: ldur            x1, [fp, #-0x10]
    // 0x884b48: LoadField: r2 = r1->field_5f
    //     0x884b48: ldur            w2, [x1, #0x5f]
    // 0x884b4c: DecompressPointer r2
    //     0x884b4c: add             x2, x2, HEAP, lsl #32
    // 0x884b50: cmp             w2, NULL
    // 0x884b54: b.eq            #0x884b78
    // 0x884b58: mov             x0, x1
    // 0x884b5c: StoreField: r2->field_f = r0
    //     0x884b5c: stur            w0, [x2, #0xf]
    //     0x884b60: ldurb           w16, [x2, #-1]
    //     0x884b64: ldurb           w17, [x0, #-1]
    //     0x884b68: and             x16, x17, x16, lsr #2
    //     0x884b6c: tst             x16, HEAP, lsr #32
    //     0x884b70: b.eq            #0x884b78
    //     0x884b74: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x884b78: mov             x0, x1
    // 0x884b7c: LeaveFrame
    //     0x884b7c: mov             SP, fp
    //     0x884b80: ldp             fp, lr, [SP], #0x10
    // 0x884b84: ret
    //     0x884b84: ret             
    // 0x884b88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x884b88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x884b8c: b               #0x884784
    // 0x884b90: r9 = queryParameters
    //     0x884b90: add             x9, PP, #0xa, lsl #12  ; [pp+0xa3d8] Field <_BaseOptions&_RequestConfig&<EMAIL>>: late (offset: 0x4c)
    //     0x884b94: ldr             x9, [x9, #0x3d8]
    // 0x884b98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884b98: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884b9c: r9 = _headers
    //     0x884b9c: add             x9, PP, #8, lsl #12  ; [pp+0x8c80] Field <_RequestConfig@914184022._headers@914184022>: late (offset: 0xc)
    //     0x884ba0: ldr             x9, [x9, #0xc80]
    // 0x884ba4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884ba4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884ba8: r9 = extra
    //     0x884ba8: add             x9, PP, #0xa, lsl #12  ; [pp+0xa7e8] Field <<EMAIL>>: late (offset: 0x2c)
    //     0x884bac: ldr             x9, [x9, #0x7e8]
    // 0x884bb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884bb0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884bb4: r9 = method
    //     0x884bb4: add             x9, PP, #8, lsl #12  ; [pp+0x8c78] Field <<EMAIL>>: late (offset: 0x8)
    //     0x884bb8: ldr             x9, [x9, #0xc78]
    // 0x884bbc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884bbc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884bc0: r9 = _baseUrl
    //     0x884bc0: add             x9, PP, #0xa, lsl #12  ; [pp+0xa3d0] Field <_BaseOptions&_RequestConfig&OptionsMixin@914184022._baseUrl@914184022>: late (offset: 0x48)
    //     0x884bc4: ldr             x9, [x9, #0x3d0]
    // 0x884bc8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884bc8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884bcc: r9 = preserveHeaderCase
    //     0x884bcc: add             x9, PP, #8, lsl #12  ; [pp+0x84c0] Field <<EMAIL>>: late (offset: 0x10)
    //     0x884bd0: ldr             x9, [x9, #0x4c0]
    // 0x884bd4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884bd4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884bd8: r9 = responseType
    //     0x884bd8: add             x9, PP, #8, lsl #12  ; [pp+0x83a8] Field <<EMAIL>>: late (offset: 0x20)
    //     0x884bdc: ldr             x9, [x9, #0x3a8]
    // 0x884be0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884be0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884be4: r9 = validateStatus
    //     0x884be4: add             x9, PP, #8, lsl #12  ; [pp+0x84c8] Field <<EMAIL>>: late (offset: 0x24)
    //     0x884be8: ldr             x9, [x9, #0x4c8]
    // 0x884bec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884bec: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884bf0: r9 = receiveDataWhenStatusError
    //     0x884bf0: add             x9, PP, #8, lsl #12  ; [pp+0x84d0] Field <<EMAIL>>: late (offset: 0x28)
    //     0x884bf4: ldr             x9, [x9, #0x4d0]
    // 0x884bf8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884bf8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884bfc: r9 = followRedirects
    //     0x884bfc: add             x9, PP, #8, lsl #12  ; [pp+0x8c88] Field <<EMAIL>>: late (offset: 0x30)
    //     0x884c00: ldr             x9, [x9, #0xc88]
    // 0x884c04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884c04: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884c08: r9 = maxRedirects
    //     0x884c08: add             x9, PP, #8, lsl #12  ; [pp+0x8c90] Field <<EMAIL>>: late (offset: 0x34)
    //     0x884c0c: ldr             x9, [x9, #0xc90]
    // 0x884c10: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884c10: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884c14: r9 = persistentConnection
    //     0x884c14: add             x9, PP, #8, lsl #12  ; [pp+0x8c98] Field <<EMAIL>>: late (offset: 0x38)
    //     0x884c18: ldr             x9, [x9, #0xc98]
    // 0x884c1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884c1c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x884c20: r9 = listFormat
    //     0x884c20: add             x9, PP, #0xa, lsl #12  ; [pp+0xa3e0] Field <<EMAIL>>: late (offset: 0x44)
    //     0x884c24: ldr             x9, [x9, #0x3e0]
    // 0x884c28: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x884c28: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4980, size: 0x8, field offset: 0x8
abstract class OptionsMixin extends Object {
}

// class id: 7077, size: 0x14, field offset: 0x14
enum ListFormat extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x1585880, size: 0x64
    // 0x1585880: EnterFrame
    //     0x1585880: stp             fp, lr, [SP, #-0x10]!
    //     0x1585884: mov             fp, SP
    // 0x1585888: AllocStack(0x10)
    //     0x1585888: sub             SP, SP, #0x10
    // 0x158588c: SetupParameters(ListFormat this /* r1 => r0, fp-0x8 */)
    //     0x158588c: mov             x0, x1
    //     0x1585890: stur            x1, [fp, #-8]
    // 0x1585894: CheckStackOverflow
    //     0x1585894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1585898: cmp             SP, x16
    //     0x158589c: b.ls            #0x15858dc
    // 0x15858a0: r1 = Null
    //     0x15858a0: mov             x1, NULL
    // 0x15858a4: r2 = 4
    //     0x15858a4: movz            x2, #0x4
    // 0x15858a8: r0 = AllocateArray()
    //     0x15858a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15858ac: r16 = "ListFormat."
    //     0x15858ac: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2afa0] "ListFormat."
    //     0x15858b0: ldr             x16, [x16, #0xfa0]
    // 0x15858b4: StoreField: r0->field_f = r16
    //     0x15858b4: stur            w16, [x0, #0xf]
    // 0x15858b8: ldur            x1, [fp, #-8]
    // 0x15858bc: LoadField: r2 = r1->field_f
    //     0x15858bc: ldur            w2, [x1, #0xf]
    // 0x15858c0: DecompressPointer r2
    //     0x15858c0: add             x2, x2, HEAP, lsl #32
    // 0x15858c4: StoreField: r0->field_13 = r2
    //     0x15858c4: stur            w2, [x0, #0x13]
    // 0x15858c8: str             x0, [SP]
    // 0x15858cc: r0 = _interpolate()
    //     0x15858cc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x15858d0: LeaveFrame
    //     0x15858d0: mov             SP, fp
    //     0x15858d4: ldp             fp, lr, [SP], #0x10
    // 0x15858d8: ret
    //     0x15858d8: ret             
    // 0x15858dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15858dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15858e0: b               #0x15858a0
  }
}

// class id: 7078, size: 0x14, field offset: 0x14
enum ResponseType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x158581c, size: 0x64
    // 0x158581c: EnterFrame
    //     0x158581c: stp             fp, lr, [SP, #-0x10]!
    //     0x1585820: mov             fp, SP
    // 0x1585824: AllocStack(0x10)
    //     0x1585824: sub             SP, SP, #0x10
    // 0x1585828: SetupParameters(ResponseType this /* r1 => r0, fp-0x8 */)
    //     0x1585828: mov             x0, x1
    //     0x158582c: stur            x1, [fp, #-8]
    // 0x1585830: CheckStackOverflow
    //     0x1585830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1585834: cmp             SP, x16
    //     0x1585838: b.ls            #0x1585878
    // 0x158583c: r1 = Null
    //     0x158583c: mov             x1, NULL
    // 0x1585840: r2 = 4
    //     0x1585840: movz            x2, #0x4
    // 0x1585844: r0 = AllocateArray()
    //     0x1585844: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1585848: r16 = "ResponseType."
    //     0x1585848: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2afa8] "ResponseType."
    //     0x158584c: ldr             x16, [x16, #0xfa8]
    // 0x1585850: StoreField: r0->field_f = r16
    //     0x1585850: stur            w16, [x0, #0xf]
    // 0x1585854: ldur            x1, [fp, #-8]
    // 0x1585858: LoadField: r2 = r1->field_f
    //     0x1585858: ldur            w2, [x1, #0xf]
    // 0x158585c: DecompressPointer r2
    //     0x158585c: add             x2, x2, HEAP, lsl #32
    // 0x1585860: StoreField: r0->field_13 = r2
    //     0x1585860: stur            w2, [x0, #0x13]
    // 0x1585864: str             x0, [SP]
    // 0x1585868: r0 = _interpolate()
    //     0x1585868: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x158586c: LeaveFrame
    //     0x158586c: mov             SP, fp
    //     0x1585870: ldp             fp, lr, [SP], #0x10
    // 0x1585874: ret
    //     0x1585874: ret             
    // 0x1585878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1585878: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x158587c: b               #0x158583c
  }
}
