// lib: , url: package:customer_app/app/presentation/views/line/customization/customized_page.dart

// class id: 1049510, size: 0x8
class :: {
}

// class id: 4538, size: 0x14, field offset: 0x14
//   const constructor, 
class CustomizedPage extends BaseView<dynamic> {

  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x1308f28, size: 0x918
    // 0x1308f28: EnterFrame
    //     0x1308f28: stp             fp, lr, [SP, #-0x10]!
    //     0x1308f2c: mov             fp, SP
    // 0x1308f30: AllocStack(0x60)
    //     0x1308f30: sub             SP, SP, #0x60
    // 0x1308f34: SetupParameters(CustomizedPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1308f34: stur            NULL, [fp, #-8]
    //     0x1308f38: movz            x0, #0
    //     0x1308f3c: add             x1, fp, w0, sxtw #2
    //     0x1308f40: ldr             x1, [x1, #0x18]
    //     0x1308f44: add             x2, fp, w0, sxtw #2
    //     0x1308f48: ldr             x2, [x2, #0x10]
    //     0x1308f4c: stur            x2, [fp, #-0x18]
    //     0x1308f50: ldur            w3, [x1, #0x17]
    //     0x1308f54: add             x3, x3, HEAP, lsl #32
    //     0x1308f58: stur            x3, [fp, #-0x10]
    // 0x1308f5c: CheckStackOverflow
    //     0x1308f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1308f60: cmp             SP, x16
    //     0x1308f64: b.ls            #0x1309838
    // 0x1308f68: InitAsync() -> Future<Null?>
    //     0x1308f68: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x1308f6c: bl              #0x6326e0  ; InitAsyncStub
    // 0x1308f70: ldur            x0, [fp, #-0x18]
    // 0x1308f74: r1 = LoadClassIdInstr(r0)
    //     0x1308f74: ldur            x1, [x0, #-1]
    //     0x1308f78: ubfx            x1, x1, #0xc, #0x14
    // 0x1308f7c: r16 = ""
    //     0x1308f7c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1308f80: stp             x16, x0, [SP]
    // 0x1308f84: mov             x0, x1
    // 0x1308f88: mov             lr, x0
    // 0x1308f8c: ldr             lr, [x21, lr, lsl #3]
    // 0x1308f90: blr             lr
    // 0x1308f94: tbz             w0, #4, #0x1309488
    // 0x1308f98: ldur            x0, [fp, #-0x10]
    // 0x1308f9c: LoadField: r1 = r0->field_f
    //     0x1308f9c: ldur            w1, [x0, #0xf]
    // 0x1308fa0: DecompressPointer r1
    //     0x1308fa0: add             x1, x1, HEAP, lsl #32
    // 0x1308fa4: r0 = controller()
    //     0x1308fa4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308fa8: LoadField: r1 = r0->field_63
    //     0x1308fa8: ldur            w1, [x0, #0x63]
    // 0x1308fac: DecompressPointer r1
    //     0x1308fac: add             x1, x1, HEAP, lsl #32
    // 0x1308fb0: cmp             w1, NULL
    // 0x1308fb4: b.ne            #0x1308fc0
    // 0x1308fb8: r0 = Null
    //     0x1308fb8: mov             x0, NULL
    // 0x1308fbc: b               #0x1308fec
    // 0x1308fc0: LoadField: r0 = r1->field_23
    //     0x1308fc0: ldur            w0, [x1, #0x23]
    // 0x1308fc4: DecompressPointer r0
    //     0x1308fc4: add             x0, x0, HEAP, lsl #32
    // 0x1308fc8: cmp             w0, NULL
    // 0x1308fcc: b.ne            #0x1308fd8
    // 0x1308fd0: r0 = Null
    //     0x1308fd0: mov             x0, NULL
    // 0x1308fd4: b               #0x1308fec
    // 0x1308fd8: LoadField: r1 = r0->field_b
    //     0x1308fd8: ldur            w1, [x0, #0xb]
    // 0x1308fdc: cbnz            w1, #0x1308fe8
    // 0x1308fe0: r0 = false
    //     0x1308fe0: add             x0, NULL, #0x30  ; false
    // 0x1308fe4: b               #0x1308fec
    // 0x1308fe8: r0 = true
    //     0x1308fe8: add             x0, NULL, #0x20  ; true
    // 0x1308fec: cmp             w0, NULL
    // 0x1308ff0: b.eq            #0x13090c0
    // 0x1308ff4: tbnz            w0, #4, #0x13090c0
    // 0x1308ff8: ldur            x0, [fp, #-0x10]
    // 0x1308ffc: LoadField: r1 = r0->field_f
    //     0x1308ffc: ldur            w1, [x0, #0xf]
    // 0x1309000: DecompressPointer r1
    //     0x1309000: add             x1, x1, HEAP, lsl #32
    // 0x1309004: r0 = controller()
    //     0x1309004: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309008: LoadField: r2 = r0->field_9b
    //     0x1309008: ldur            w2, [x0, #0x9b]
    // 0x130900c: DecompressPointer r2
    //     0x130900c: add             x2, x2, HEAP, lsl #32
    // 0x1309010: ldur            x0, [fp, #-0x10]
    // 0x1309014: stur            x2, [fp, #-0x18]
    // 0x1309018: LoadField: r1 = r0->field_f
    //     0x1309018: ldur            w1, [x0, #0xf]
    // 0x130901c: DecompressPointer r1
    //     0x130901c: add             x1, x1, HEAP, lsl #32
    // 0x1309020: r0 = controller()
    //     0x1309020: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309024: LoadField: r1 = r0->field_63
    //     0x1309024: ldur            w1, [x0, #0x63]
    // 0x1309028: DecompressPointer r1
    //     0x1309028: add             x1, x1, HEAP, lsl #32
    // 0x130902c: cmp             w1, NULL
    // 0x1309030: b.ne            #0x1309040
    // 0x1309034: r0 = ProductCustomisation()
    //     0x1309034: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0x1309038: mov             x2, x0
    // 0x130903c: b               #0x1309044
    // 0x1309040: mov             x2, x1
    // 0x1309044: ldur            x0, [fp, #-0x18]
    // 0x1309048: stur            x2, [fp, #-0x28]
    // 0x130904c: LoadField: r1 = r0->field_b
    //     0x130904c: ldur            w1, [x0, #0xb]
    // 0x1309050: LoadField: r3 = r0->field_f
    //     0x1309050: ldur            w3, [x0, #0xf]
    // 0x1309054: DecompressPointer r3
    //     0x1309054: add             x3, x3, HEAP, lsl #32
    // 0x1309058: LoadField: r4 = r3->field_b
    //     0x1309058: ldur            w4, [x3, #0xb]
    // 0x130905c: r3 = LoadInt32Instr(r1)
    //     0x130905c: sbfx            x3, x1, #1, #0x1f
    // 0x1309060: stur            x3, [fp, #-0x20]
    // 0x1309064: r1 = LoadInt32Instr(r4)
    //     0x1309064: sbfx            x1, x4, #1, #0x1f
    // 0x1309068: cmp             x3, x1
    // 0x130906c: b.ne            #0x1309078
    // 0x1309070: mov             x1, x0
    // 0x1309074: r0 = _growToNextCapacity()
    //     0x1309074: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1309078: ldur            x0, [fp, #-0x18]
    // 0x130907c: ldur            x2, [fp, #-0x20]
    // 0x1309080: add             x1, x2, #1
    // 0x1309084: lsl             x3, x1, #1
    // 0x1309088: StoreField: r0->field_b = r3
    //     0x1309088: stur            w3, [x0, #0xb]
    // 0x130908c: LoadField: r1 = r0->field_f
    //     0x130908c: ldur            w1, [x0, #0xf]
    // 0x1309090: DecompressPointer r1
    //     0x1309090: add             x1, x1, HEAP, lsl #32
    // 0x1309094: ldur            x0, [fp, #-0x28]
    // 0x1309098: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1309098: add             x25, x1, x2, lsl #2
    //     0x130909c: add             x25, x25, #0xf
    //     0x13090a0: str             w0, [x25]
    //     0x13090a4: tbz             w0, #0, #0x13090c0
    //     0x13090a8: ldurb           w16, [x1, #-1]
    //     0x13090ac: ldurb           w17, [x0, #-1]
    //     0x13090b0: and             x16, x17, x16, lsr #2
    //     0x13090b4: tst             x16, HEAP, lsr #32
    //     0x13090b8: b.eq            #0x13090c0
    //     0x13090bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13090c0: ldur            x0, [fp, #-0x10]
    // 0x13090c4: LoadField: r1 = r0->field_f
    //     0x13090c4: ldur            w1, [x0, #0xf]
    // 0x13090c8: DecompressPointer r1
    //     0x13090c8: add             x1, x1, HEAP, lsl #32
    // 0x13090cc: r0 = controller()
    //     0x13090cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13090d0: LoadField: r1 = r0->field_8f
    //     0x13090d0: ldur            w1, [x0, #0x8f]
    // 0x13090d4: DecompressPointer r1
    //     0x13090d4: add             x1, x1, HEAP, lsl #32
    // 0x13090d8: r0 = value()
    //     0x13090d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13090dc: mov             x2, x0
    // 0x13090e0: ldur            x0, [fp, #-0x10]
    // 0x13090e4: stur            x2, [fp, #-0x18]
    // 0x13090e8: LoadField: r1 = r0->field_f
    //     0x13090e8: ldur            w1, [x0, #0xf]
    // 0x13090ec: DecompressPointer r1
    //     0x13090ec: add             x1, x1, HEAP, lsl #32
    // 0x13090f0: r0 = controller()
    //     0x13090f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13090f4: LoadField: r1 = r0->field_93
    //     0x13090f4: ldur            w1, [x0, #0x93]
    // 0x13090f8: DecompressPointer r1
    //     0x13090f8: add             x1, x1, HEAP, lsl #32
    // 0x13090fc: r0 = value()
    //     0x13090fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309100: mov             x2, x0
    // 0x1309104: ldur            x0, [fp, #-0x10]
    // 0x1309108: stur            x2, [fp, #-0x28]
    // 0x130910c: LoadField: r1 = r0->field_f
    //     0x130910c: ldur            w1, [x0, #0xf]
    // 0x1309110: DecompressPointer r1
    //     0x1309110: add             x1, x1, HEAP, lsl #32
    // 0x1309114: r0 = controller()
    //     0x1309114: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309118: LoadField: r1 = r0->field_ab
    //     0x1309118: ldur            w1, [x0, #0xab]
    // 0x130911c: DecompressPointer r1
    //     0x130911c: add             x1, x1, HEAP, lsl #32
    // 0x1309120: r0 = value()
    //     0x1309120: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309124: mov             x2, x0
    // 0x1309128: ldur            x0, [fp, #-0x10]
    // 0x130912c: stur            x2, [fp, #-0x30]
    // 0x1309130: LoadField: r1 = r0->field_f
    //     0x1309130: ldur            w1, [x0, #0xf]
    // 0x1309134: DecompressPointer r1
    //     0x1309134: add             x1, x1, HEAP, lsl #32
    // 0x1309138: r0 = controller()
    //     0x1309138: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130913c: LoadField: r2 = r0->field_9b
    //     0x130913c: ldur            w2, [x0, #0x9b]
    // 0x1309140: DecompressPointer r2
    //     0x1309140: add             x2, x2, HEAP, lsl #32
    // 0x1309144: ldur            x0, [fp, #-0x10]
    // 0x1309148: stur            x2, [fp, #-0x38]
    // 0x130914c: LoadField: r1 = r0->field_f
    //     0x130914c: ldur            w1, [x0, #0xf]
    // 0x1309150: DecompressPointer r1
    //     0x1309150: add             x1, x1, HEAP, lsl #32
    // 0x1309154: r0 = controller()
    //     0x1309154: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309158: LoadField: r1 = r0->field_97
    //     0x1309158: ldur            w1, [x0, #0x97]
    // 0x130915c: DecompressPointer r1
    //     0x130915c: add             x1, x1, HEAP, lsl #32
    // 0x1309160: r0 = value()
    //     0x1309160: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309164: stur            x0, [fp, #-0x40]
    // 0x1309168: r0 = CustomizedRequest()
    //     0x1309168: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x130916c: mov             x1, x0
    // 0x1309170: ldur            x0, [fp, #-0x18]
    // 0x1309174: stur            x1, [fp, #-0x48]
    // 0x1309178: StoreField: r1->field_7 = r0
    //     0x1309178: stur            w0, [x1, #7]
    // 0x130917c: ldur            x0, [fp, #-0x28]
    // 0x1309180: StoreField: r1->field_b = r0
    //     0x1309180: stur            w0, [x1, #0xb]
    // 0x1309184: ldur            x0, [fp, #-0x30]
    // 0x1309188: StoreField: r1->field_f = r0
    //     0x1309188: stur            w0, [x1, #0xf]
    // 0x130918c: ldur            x0, [fp, #-0x38]
    // 0x1309190: StoreField: r1->field_13 = r0
    //     0x1309190: stur            w0, [x1, #0x13]
    // 0x1309194: ldur            x0, [fp, #-0x40]
    // 0x1309198: ArrayStore: r1[0] = r0  ; List_4
    //     0x1309198: stur            w0, [x1, #0x17]
    // 0x130919c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130919c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13091a0: ldr             x0, [x0, #0x1c80]
    //     0x13091a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13091a8: cmp             w0, w16
    //     0x13091ac: b.ne            #0x13091b8
    //     0x13091b0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13091b4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13091b8: r1 = Null
    //     0x13091b8: mov             x1, NULL
    // 0x13091bc: r2 = 36
    //     0x13091bc: movz            x2, #0x24
    // 0x13091c0: r0 = AllocateArray()
    //     0x13091c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13091c4: stur            x0, [fp, #-0x18]
    // 0x13091c8: r16 = "coming_from"
    //     0x13091c8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x13091cc: ldr             x16, [x16, #0x328]
    // 0x13091d0: StoreField: r0->field_f = r16
    //     0x13091d0: stur            w16, [x0, #0xf]
    // 0x13091d4: r16 = "buy_now"
    //     0x13091d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe78] "buy_now"
    //     0x13091d8: ldr             x16, [x16, #0xe78]
    // 0x13091dc: StoreField: r0->field_13 = r16
    //     0x13091dc: stur            w16, [x0, #0x13]
    // 0x13091e0: r16 = "couponCode"
    //     0x13091e0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x13091e4: ldr             x16, [x16, #0x310]
    // 0x13091e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x13091e8: stur            w16, [x0, #0x17]
    // 0x13091ec: r16 = ""
    //     0x13091ec: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13091f0: StoreField: r0->field_1b = r16
    //     0x13091f0: stur            w16, [x0, #0x1b]
    // 0x13091f4: r16 = "product_id"
    //     0x13091f4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x13091f8: ldr             x16, [x16, #0x9b8]
    // 0x13091fc: StoreField: r0->field_1f = r16
    //     0x13091fc: stur            w16, [x0, #0x1f]
    // 0x1309200: ldur            x2, [fp, #-0x10]
    // 0x1309204: LoadField: r1 = r2->field_f
    //     0x1309204: ldur            w1, [x2, #0xf]
    // 0x1309208: DecompressPointer r1
    //     0x1309208: add             x1, x1, HEAP, lsl #32
    // 0x130920c: r0 = controller()
    //     0x130920c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309210: LoadField: r1 = r0->field_8f
    //     0x1309210: ldur            w1, [x0, #0x8f]
    // 0x1309214: DecompressPointer r1
    //     0x1309214: add             x1, x1, HEAP, lsl #32
    // 0x1309218: r0 = value()
    //     0x1309218: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130921c: ldur            x1, [fp, #-0x18]
    // 0x1309220: ArrayStore: r1[5] = r0  ; List_4
    //     0x1309220: add             x25, x1, #0x23
    //     0x1309224: str             w0, [x25]
    //     0x1309228: tbz             w0, #0, #0x1309244
    //     0x130922c: ldurb           w16, [x1, #-1]
    //     0x1309230: ldurb           w17, [x0, #-1]
    //     0x1309234: and             x16, x17, x16, lsr #2
    //     0x1309238: tst             x16, HEAP, lsr #32
    //     0x130923c: b.eq            #0x1309244
    //     0x1309240: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309244: ldur            x0, [fp, #-0x18]
    // 0x1309248: r16 = "sku_id"
    //     0x1309248: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130924c: ldr             x16, [x16, #0x498]
    // 0x1309250: StoreField: r0->field_27 = r16
    //     0x1309250: stur            w16, [x0, #0x27]
    // 0x1309254: ldur            x2, [fp, #-0x10]
    // 0x1309258: LoadField: r1 = r2->field_f
    //     0x1309258: ldur            w1, [x2, #0xf]
    // 0x130925c: DecompressPointer r1
    //     0x130925c: add             x1, x1, HEAP, lsl #32
    // 0x1309260: r0 = controller()
    //     0x1309260: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309264: LoadField: r1 = r0->field_93
    //     0x1309264: ldur            w1, [x0, #0x93]
    // 0x1309268: DecompressPointer r1
    //     0x1309268: add             x1, x1, HEAP, lsl #32
    // 0x130926c: r0 = value()
    //     0x130926c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309270: ldur            x1, [fp, #-0x18]
    // 0x1309274: ArrayStore: r1[7] = r0  ; List_4
    //     0x1309274: add             x25, x1, #0x2b
    //     0x1309278: str             w0, [x25]
    //     0x130927c: tbz             w0, #0, #0x1309298
    //     0x1309280: ldurb           w16, [x1, #-1]
    //     0x1309284: ldurb           w17, [x0, #-1]
    //     0x1309288: and             x16, x17, x16, lsr #2
    //     0x130928c: tst             x16, HEAP, lsr #32
    //     0x1309290: b.eq            #0x1309298
    //     0x1309294: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309298: ldur            x0, [fp, #-0x18]
    // 0x130929c: r16 = "quantity"
    //     0x130929c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x13092a0: ldr             x16, [x16, #0x428]
    // 0x13092a4: StoreField: r0->field_2f = r16
    //     0x13092a4: stur            w16, [x0, #0x2f]
    // 0x13092a8: ldur            x2, [fp, #-0x10]
    // 0x13092ac: LoadField: r1 = r2->field_f
    //     0x13092ac: ldur            w1, [x2, #0xf]
    // 0x13092b0: DecompressPointer r1
    //     0x13092b0: add             x1, x1, HEAP, lsl #32
    // 0x13092b4: r0 = controller()
    //     0x13092b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13092b8: LoadField: r1 = r0->field_ab
    //     0x13092b8: ldur            w1, [x0, #0xab]
    // 0x13092bc: DecompressPointer r1
    //     0x13092bc: add             x1, x1, HEAP, lsl #32
    // 0x13092c0: r0 = value()
    //     0x13092c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13092c4: r1 = 60
    //     0x13092c4: movz            x1, #0x3c
    // 0x13092c8: branchIfSmi(r0, 0x13092d4)
    //     0x13092c8: tbz             w0, #0, #0x13092d4
    // 0x13092cc: r1 = LoadClassIdInstr(r0)
    //     0x13092cc: ldur            x1, [x0, #-1]
    //     0x13092d0: ubfx            x1, x1, #0xc, #0x14
    // 0x13092d4: str             x0, [SP]
    // 0x13092d8: mov             x0, x1
    // 0x13092dc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x13092dc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x13092e0: r0 = GDT[cid_x0 + 0x2700]()
    //     0x13092e0: movz            x17, #0x2700
    //     0x13092e4: add             lr, x0, x17
    //     0x13092e8: ldr             lr, [x21, lr, lsl #3]
    //     0x13092ec: blr             lr
    // 0x13092f0: ldur            x1, [fp, #-0x18]
    // 0x13092f4: ArrayStore: r1[9] = r0  ; List_4
    //     0x13092f4: add             x25, x1, #0x33
    //     0x13092f8: str             w0, [x25]
    //     0x13092fc: tbz             w0, #0, #0x1309318
    //     0x1309300: ldurb           w16, [x1, #-1]
    //     0x1309304: ldurb           w17, [x0, #-1]
    //     0x1309308: and             x16, x17, x16, lsr #2
    //     0x130930c: tst             x16, HEAP, lsr #32
    //     0x1309310: b.eq            #0x1309318
    //     0x1309314: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309318: ldur            x3, [fp, #-0x18]
    // 0x130931c: r16 = "type"
    //     0x130931c: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x1309320: StoreField: r3->field_37 = r16
    //     0x1309320: stur            w16, [x3, #0x37]
    // 0x1309324: r16 = "replace-new"
    //     0x1309324: add             x16, PP, #0x32, lsl #12  ; [pp+0x32ad8] "replace-new"
    //     0x1309328: ldr             x16, [x16, #0xad8]
    // 0x130932c: StoreField: r3->field_3b = r16
    //     0x130932c: stur            w16, [x3, #0x3b]
    // 0x1309330: r16 = "previousScreenSource"
    //     0x1309330: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x1309334: ldr             x16, [x16, #0x448]
    // 0x1309338: StoreField: r3->field_3f = r16
    //     0x1309338: stur            w16, [x3, #0x3f]
    // 0x130933c: r16 = "product_page"
    //     0x130933c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1309340: ldr             x16, [x16, #0x480]
    // 0x1309344: StoreField: r3->field_43 = r16
    //     0x1309344: stur            w16, [x3, #0x43]
    // 0x1309348: r16 = "customization_request"
    //     0x1309348: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130934c: ldr             x16, [x16, #0x2b8]
    // 0x1309350: StoreField: r3->field_47 = r16
    //     0x1309350: stur            w16, [x3, #0x47]
    // 0x1309354: mov             x1, x3
    // 0x1309358: ldur            x0, [fp, #-0x48]
    // 0x130935c: ArrayStore: r1[15] = r0  ; List_4
    //     0x130935c: add             x25, x1, #0x4b
    //     0x1309360: str             w0, [x25]
    //     0x1309364: tbz             w0, #0, #0x1309380
    //     0x1309368: ldurb           w16, [x1, #-1]
    //     0x130936c: ldurb           w17, [x0, #-1]
    //     0x1309370: and             x16, x17, x16, lsr #2
    //     0x1309374: tst             x16, HEAP, lsr #32
    //     0x1309378: b.eq            #0x1309380
    //     0x130937c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309380: r16 = "customization_prize"
    //     0x1309380: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x1309384: ldr             x16, [x16, #0x2e8]
    // 0x1309388: StoreField: r3->field_4f = r16
    //     0x1309388: stur            w16, [x3, #0x4f]
    // 0x130938c: r1 = Null
    //     0x130938c: mov             x1, NULL
    // 0x1309390: r2 = 4
    //     0x1309390: movz            x2, #0x4
    // 0x1309394: r0 = AllocateArray()
    //     0x1309394: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1309398: stur            x0, [fp, #-0x28]
    // 0x130939c: r16 = "₹"
    //     0x130939c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x13093a0: ldr             x16, [x16, #0x360]
    // 0x13093a4: StoreField: r0->field_f = r16
    //     0x13093a4: stur            w16, [x0, #0xf]
    // 0x13093a8: r1 = Function '<anonymous closure>': static.
    //     0x13093a8: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x13093ac: ldr             x1, [x1, #0x1a0]
    // 0x13093b0: r2 = Null
    //     0x13093b0: mov             x2, NULL
    // 0x13093b4: r0 = AllocateClosure()
    //     0x13093b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13093b8: mov             x3, x0
    // 0x13093bc: r1 = Null
    //     0x13093bc: mov             x1, NULL
    // 0x13093c0: r2 = Null
    //     0x13093c0: mov             x2, NULL
    // 0x13093c4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x13093c4: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x13093c8: r0 = NumberFormat._forPattern()
    //     0x13093c8: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x13093cc: mov             x2, x0
    // 0x13093d0: ldur            x0, [fp, #-0x10]
    // 0x13093d4: stur            x2, [fp, #-0x30]
    // 0x13093d8: LoadField: r1 = r0->field_f
    //     0x13093d8: ldur            w1, [x0, #0xf]
    // 0x13093dc: DecompressPointer r1
    //     0x13093dc: add             x1, x1, HEAP, lsl #32
    // 0x13093e0: r0 = controller()
    //     0x13093e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13093e4: LoadField: r1 = r0->field_67
    //     0x13093e4: ldur            w1, [x0, #0x67]
    // 0x13093e8: DecompressPointer r1
    //     0x13093e8: add             x1, x1, HEAP, lsl #32
    // 0x13093ec: r0 = value()
    //     0x13093ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13093f0: ldur            x1, [fp, #-0x30]
    // 0x13093f4: mov             x2, x0
    // 0x13093f8: r0 = format()
    //     0x13093f8: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x13093fc: ldur            x1, [fp, #-0x28]
    // 0x1309400: ArrayStore: r1[1] = r0  ; List_4
    //     0x1309400: add             x25, x1, #0x13
    //     0x1309404: str             w0, [x25]
    //     0x1309408: tbz             w0, #0, #0x1309424
    //     0x130940c: ldurb           w16, [x1, #-1]
    //     0x1309410: ldurb           w17, [x0, #-1]
    //     0x1309414: and             x16, x17, x16, lsr #2
    //     0x1309418: tst             x16, HEAP, lsr #32
    //     0x130941c: b.eq            #0x1309424
    //     0x1309420: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309424: ldur            x16, [fp, #-0x28]
    // 0x1309428: str             x16, [SP]
    // 0x130942c: r0 = _interpolate()
    //     0x130942c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1309430: ldur            x1, [fp, #-0x18]
    // 0x1309434: ArrayStore: r1[17] = r0  ; List_4
    //     0x1309434: add             x25, x1, #0x53
    //     0x1309438: str             w0, [x25]
    //     0x130943c: tbz             w0, #0, #0x1309458
    //     0x1309440: ldurb           w16, [x1, #-1]
    //     0x1309444: ldurb           w17, [x0, #-1]
    //     0x1309448: and             x16, x17, x16, lsr #2
    //     0x130944c: tst             x16, HEAP, lsr #32
    //     0x1309450: b.eq            #0x1309458
    //     0x1309454: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309458: r16 = <String, dynamic>
    //     0x1309458: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130945c: ldur            lr, [fp, #-0x18]
    // 0x1309460: stp             lr, x16, [SP]
    // 0x1309464: r0 = Map._fromLiteral()
    //     0x1309464: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x1309468: r16 = "/exchange-checkout"
    //     0x1309468: add             x16, PP, #0xd, lsl #12  ; [pp+0xd988] "/exchange-checkout"
    //     0x130946c: ldr             x16, [x16, #0x988]
    // 0x1309470: stp             x16, NULL, [SP, #8]
    // 0x1309474: str             x0, [SP]
    // 0x1309478: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x1309478: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130947c: ldr             x4, [x4, #0x438]
    // 0x1309480: r0 = GetNavigation.toNamed()
    //     0x1309480: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1309484: b               #0x1309830
    // 0x1309488: ldur            x0, [fp, #-0x10]
    // 0x130948c: LoadField: r1 = r0->field_f
    //     0x130948c: ldur            w1, [x0, #0xf]
    // 0x1309490: DecompressPointer r1
    //     0x1309490: add             x1, x1, HEAP, lsl #32
    // 0x1309494: r0 = controller()
    //     0x1309494: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309498: LoadField: r1 = r0->field_8f
    //     0x1309498: ldur            w1, [x0, #0x8f]
    // 0x130949c: DecompressPointer r1
    //     0x130949c: add             x1, x1, HEAP, lsl #32
    // 0x13094a0: r0 = value()
    //     0x13094a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13094a4: mov             x2, x0
    // 0x13094a8: ldur            x0, [fp, #-0x10]
    // 0x13094ac: stur            x2, [fp, #-0x18]
    // 0x13094b0: LoadField: r1 = r0->field_f
    //     0x13094b0: ldur            w1, [x0, #0xf]
    // 0x13094b4: DecompressPointer r1
    //     0x13094b4: add             x1, x1, HEAP, lsl #32
    // 0x13094b8: r0 = controller()
    //     0x13094b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13094bc: LoadField: r1 = r0->field_93
    //     0x13094bc: ldur            w1, [x0, #0x93]
    // 0x13094c0: DecompressPointer r1
    //     0x13094c0: add             x1, x1, HEAP, lsl #32
    // 0x13094c4: r0 = value()
    //     0x13094c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13094c8: mov             x2, x0
    // 0x13094cc: ldur            x0, [fp, #-0x10]
    // 0x13094d0: stur            x2, [fp, #-0x28]
    // 0x13094d4: LoadField: r1 = r0->field_f
    //     0x13094d4: ldur            w1, [x0, #0xf]
    // 0x13094d8: DecompressPointer r1
    //     0x13094d8: add             x1, x1, HEAP, lsl #32
    // 0x13094dc: r0 = controller()
    //     0x13094dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13094e0: LoadField: r1 = r0->field_ab
    //     0x13094e0: ldur            w1, [x0, #0xab]
    // 0x13094e4: DecompressPointer r1
    //     0x13094e4: add             x1, x1, HEAP, lsl #32
    // 0x13094e8: r0 = value()
    //     0x13094e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13094ec: mov             x2, x0
    // 0x13094f0: ldur            x0, [fp, #-0x10]
    // 0x13094f4: stur            x2, [fp, #-0x30]
    // 0x13094f8: LoadField: r1 = r0->field_f
    //     0x13094f8: ldur            w1, [x0, #0xf]
    // 0x13094fc: DecompressPointer r1
    //     0x13094fc: add             x1, x1, HEAP, lsl #32
    // 0x1309500: r0 = controller()
    //     0x1309500: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309504: LoadField: r2 = r0->field_9b
    //     0x1309504: ldur            w2, [x0, #0x9b]
    // 0x1309508: DecompressPointer r2
    //     0x1309508: add             x2, x2, HEAP, lsl #32
    // 0x130950c: ldur            x0, [fp, #-0x10]
    // 0x1309510: stur            x2, [fp, #-0x38]
    // 0x1309514: LoadField: r1 = r0->field_f
    //     0x1309514: ldur            w1, [x0, #0xf]
    // 0x1309518: DecompressPointer r1
    //     0x1309518: add             x1, x1, HEAP, lsl #32
    // 0x130951c: r0 = controller()
    //     0x130951c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309520: LoadField: r1 = r0->field_97
    //     0x1309520: ldur            w1, [x0, #0x97]
    // 0x1309524: DecompressPointer r1
    //     0x1309524: add             x1, x1, HEAP, lsl #32
    // 0x1309528: r0 = value()
    //     0x1309528: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130952c: stur            x0, [fp, #-0x40]
    // 0x1309530: r0 = CustomizedRequest()
    //     0x1309530: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x1309534: mov             x1, x0
    // 0x1309538: ldur            x0, [fp, #-0x18]
    // 0x130953c: stur            x1, [fp, #-0x48]
    // 0x1309540: StoreField: r1->field_7 = r0
    //     0x1309540: stur            w0, [x1, #7]
    // 0x1309544: ldur            x0, [fp, #-0x28]
    // 0x1309548: StoreField: r1->field_b = r0
    //     0x1309548: stur            w0, [x1, #0xb]
    // 0x130954c: ldur            x0, [fp, #-0x30]
    // 0x1309550: StoreField: r1->field_f = r0
    //     0x1309550: stur            w0, [x1, #0xf]
    // 0x1309554: ldur            x0, [fp, #-0x38]
    // 0x1309558: StoreField: r1->field_13 = r0
    //     0x1309558: stur            w0, [x1, #0x13]
    // 0x130955c: ldur            x0, [fp, #-0x40]
    // 0x1309560: ArrayStore: r1[0] = r0  ; List_4
    //     0x1309560: stur            w0, [x1, #0x17]
    // 0x1309564: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1309564: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1309568: ldr             x0, [x0, #0x1c80]
    //     0x130956c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1309570: cmp             w0, w16
    //     0x1309574: b.ne            #0x1309580
    //     0x1309578: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130957c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1309580: r1 = Null
    //     0x1309580: mov             x1, NULL
    // 0x1309584: r2 = 28
    //     0x1309584: movz            x2, #0x1c
    // 0x1309588: r0 = AllocateArray()
    //     0x1309588: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130958c: stur            x0, [fp, #-0x18]
    // 0x1309590: r16 = "previousScreenSource"
    //     0x1309590: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x1309594: ldr             x16, [x16, #0x448]
    // 0x1309598: StoreField: r0->field_f = r16
    //     0x1309598: stur            w16, [x0, #0xf]
    // 0x130959c: r16 = "product_page"
    //     0x130959c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x13095a0: ldr             x16, [x16, #0x480]
    // 0x13095a4: StoreField: r0->field_13 = r16
    //     0x13095a4: stur            w16, [x0, #0x13]
    // 0x13095a8: r16 = "product_id"
    //     0x13095a8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x13095ac: ldr             x16, [x16, #0x9b8]
    // 0x13095b0: ArrayStore: r0[0] = r16  ; List_4
    //     0x13095b0: stur            w16, [x0, #0x17]
    // 0x13095b4: ldur            x2, [fp, #-0x10]
    // 0x13095b8: LoadField: r1 = r2->field_f
    //     0x13095b8: ldur            w1, [x2, #0xf]
    // 0x13095bc: DecompressPointer r1
    //     0x13095bc: add             x1, x1, HEAP, lsl #32
    // 0x13095c0: r0 = controller()
    //     0x13095c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13095c4: LoadField: r1 = r0->field_8f
    //     0x13095c4: ldur            w1, [x0, #0x8f]
    // 0x13095c8: DecompressPointer r1
    //     0x13095c8: add             x1, x1, HEAP, lsl #32
    // 0x13095cc: r0 = value()
    //     0x13095cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13095d0: ldur            x1, [fp, #-0x18]
    // 0x13095d4: ArrayStore: r1[3] = r0  ; List_4
    //     0x13095d4: add             x25, x1, #0x1b
    //     0x13095d8: str             w0, [x25]
    //     0x13095dc: tbz             w0, #0, #0x13095f8
    //     0x13095e0: ldurb           w16, [x1, #-1]
    //     0x13095e4: ldurb           w17, [x0, #-1]
    //     0x13095e8: and             x16, x17, x16, lsr #2
    //     0x13095ec: tst             x16, HEAP, lsr #32
    //     0x13095f0: b.eq            #0x13095f8
    //     0x13095f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13095f8: ldur            x0, [fp, #-0x18]
    // 0x13095fc: r16 = "sku_id"
    //     0x13095fc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x1309600: ldr             x16, [x16, #0x498]
    // 0x1309604: StoreField: r0->field_1f = r16
    //     0x1309604: stur            w16, [x0, #0x1f]
    // 0x1309608: ldur            x2, [fp, #-0x10]
    // 0x130960c: LoadField: r1 = r2->field_f
    //     0x130960c: ldur            w1, [x2, #0xf]
    // 0x1309610: DecompressPointer r1
    //     0x1309610: add             x1, x1, HEAP, lsl #32
    // 0x1309614: r0 = controller()
    //     0x1309614: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309618: LoadField: r1 = r0->field_93
    //     0x1309618: ldur            w1, [x0, #0x93]
    // 0x130961c: DecompressPointer r1
    //     0x130961c: add             x1, x1, HEAP, lsl #32
    // 0x1309620: r0 = value()
    //     0x1309620: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309624: ldur            x1, [fp, #-0x18]
    // 0x1309628: ArrayStore: r1[5] = r0  ; List_4
    //     0x1309628: add             x25, x1, #0x23
    //     0x130962c: str             w0, [x25]
    //     0x1309630: tbz             w0, #0, #0x130964c
    //     0x1309634: ldurb           w16, [x1, #-1]
    //     0x1309638: ldurb           w17, [x0, #-1]
    //     0x130963c: and             x16, x17, x16, lsr #2
    //     0x1309640: tst             x16, HEAP, lsr #32
    //     0x1309644: b.eq            #0x130964c
    //     0x1309648: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130964c: ldur            x0, [fp, #-0x18]
    // 0x1309650: r16 = "quantity"
    //     0x1309650: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x1309654: ldr             x16, [x16, #0x428]
    // 0x1309658: StoreField: r0->field_27 = r16
    //     0x1309658: stur            w16, [x0, #0x27]
    // 0x130965c: ldur            x2, [fp, #-0x10]
    // 0x1309660: LoadField: r1 = r2->field_f
    //     0x1309660: ldur            w1, [x2, #0xf]
    // 0x1309664: DecompressPointer r1
    //     0x1309664: add             x1, x1, HEAP, lsl #32
    // 0x1309668: r0 = controller()
    //     0x1309668: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130966c: LoadField: r1 = r0->field_ab
    //     0x130966c: ldur            w1, [x0, #0xab]
    // 0x1309670: DecompressPointer r1
    //     0x1309670: add             x1, x1, HEAP, lsl #32
    // 0x1309674: r0 = value()
    //     0x1309674: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309678: r1 = 60
    //     0x1309678: movz            x1, #0x3c
    // 0x130967c: branchIfSmi(r0, 0x1309688)
    //     0x130967c: tbz             w0, #0, #0x1309688
    // 0x1309680: r1 = LoadClassIdInstr(r0)
    //     0x1309680: ldur            x1, [x0, #-1]
    //     0x1309684: ubfx            x1, x1, #0xc, #0x14
    // 0x1309688: str             x0, [SP]
    // 0x130968c: mov             x0, x1
    // 0x1309690: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x1309690: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x1309694: r0 = GDT[cid_x0 + 0x2700]()
    //     0x1309694: movz            x17, #0x2700
    //     0x1309698: add             lr, x0, x17
    //     0x130969c: ldr             lr, [x21, lr, lsl #3]
    //     0x13096a0: blr             lr
    // 0x13096a4: ldur            x1, [fp, #-0x18]
    // 0x13096a8: ArrayStore: r1[7] = r0  ; List_4
    //     0x13096a8: add             x25, x1, #0x2b
    //     0x13096ac: str             w0, [x25]
    //     0x13096b0: tbz             w0, #0, #0x13096cc
    //     0x13096b4: ldurb           w16, [x1, #-1]
    //     0x13096b8: ldurb           w17, [x0, #-1]
    //     0x13096bc: and             x16, x17, x16, lsr #2
    //     0x13096c0: tst             x16, HEAP, lsr #32
    //     0x13096c4: b.eq            #0x13096cc
    //     0x13096c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13096cc: ldur            x3, [fp, #-0x18]
    // 0x13096d0: r16 = "customization_request"
    //     0x13096d0: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x13096d4: ldr             x16, [x16, #0x2b8]
    // 0x13096d8: StoreField: r3->field_2f = r16
    //     0x13096d8: stur            w16, [x3, #0x2f]
    // 0x13096dc: mov             x1, x3
    // 0x13096e0: ldur            x0, [fp, #-0x48]
    // 0x13096e4: ArrayStore: r1[9] = r0  ; List_4
    //     0x13096e4: add             x25, x1, #0x33
    //     0x13096e8: str             w0, [x25]
    //     0x13096ec: tbz             w0, #0, #0x1309708
    //     0x13096f0: ldurb           w16, [x1, #-1]
    //     0x13096f4: ldurb           w17, [x0, #-1]
    //     0x13096f8: and             x16, x17, x16, lsr #2
    //     0x13096fc: tst             x16, HEAP, lsr #32
    //     0x1309700: b.eq            #0x1309708
    //     0x1309704: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309708: r16 = "coming_from"
    //     0x1309708: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130970c: ldr             x16, [x16, #0x328]
    // 0x1309710: StoreField: r3->field_37 = r16
    //     0x1309710: stur            w16, [x3, #0x37]
    // 0x1309714: r16 = "bag"
    //     0x1309714: add             x16, PP, #0xb, lsl #12  ; [pp+0xb460] "bag"
    //     0x1309718: ldr             x16, [x16, #0x460]
    // 0x130971c: StoreField: r3->field_3b = r16
    //     0x130971c: stur            w16, [x3, #0x3b]
    // 0x1309720: r16 = "customization_prize"
    //     0x1309720: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x1309724: ldr             x16, [x16, #0x2e8]
    // 0x1309728: StoreField: r3->field_3f = r16
    //     0x1309728: stur            w16, [x3, #0x3f]
    // 0x130972c: r1 = Null
    //     0x130972c: mov             x1, NULL
    // 0x1309730: r2 = 4
    //     0x1309730: movz            x2, #0x4
    // 0x1309734: r0 = AllocateArray()
    //     0x1309734: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1309738: stur            x0, [fp, #-0x28]
    // 0x130973c: r16 = "₹"
    //     0x130973c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x1309740: ldr             x16, [x16, #0x360]
    // 0x1309744: StoreField: r0->field_f = r16
    //     0x1309744: stur            w16, [x0, #0xf]
    // 0x1309748: r1 = Function '<anonymous closure>': static.
    //     0x1309748: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x130974c: ldr             x1, [x1, #0x1a0]
    // 0x1309750: r2 = Null
    //     0x1309750: mov             x2, NULL
    // 0x1309754: r0 = AllocateClosure()
    //     0x1309754: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1309758: mov             x3, x0
    // 0x130975c: r1 = Null
    //     0x130975c: mov             x1, NULL
    // 0x1309760: r2 = Null
    //     0x1309760: mov             x2, NULL
    // 0x1309764: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x1309764: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x1309768: r0 = NumberFormat._forPattern()
    //     0x1309768: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x130976c: mov             x2, x0
    // 0x1309770: ldur            x0, [fp, #-0x10]
    // 0x1309774: stur            x2, [fp, #-0x30]
    // 0x1309778: LoadField: r1 = r0->field_f
    //     0x1309778: ldur            w1, [x0, #0xf]
    // 0x130977c: DecompressPointer r1
    //     0x130977c: add             x1, x1, HEAP, lsl #32
    // 0x1309780: r0 = controller()
    //     0x1309780: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309784: LoadField: r1 = r0->field_67
    //     0x1309784: ldur            w1, [x0, #0x67]
    // 0x1309788: DecompressPointer r1
    //     0x1309788: add             x1, x1, HEAP, lsl #32
    // 0x130978c: r0 = value()
    //     0x130978c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309790: ldur            x1, [fp, #-0x30]
    // 0x1309794: mov             x2, x0
    // 0x1309798: r0 = format()
    //     0x1309798: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130979c: ldur            x1, [fp, #-0x28]
    // 0x13097a0: ArrayStore: r1[1] = r0  ; List_4
    //     0x13097a0: add             x25, x1, #0x13
    //     0x13097a4: str             w0, [x25]
    //     0x13097a8: tbz             w0, #0, #0x13097c4
    //     0x13097ac: ldurb           w16, [x1, #-1]
    //     0x13097b0: ldurb           w17, [x0, #-1]
    //     0x13097b4: and             x16, x17, x16, lsr #2
    //     0x13097b8: tst             x16, HEAP, lsr #32
    //     0x13097bc: b.eq            #0x13097c4
    //     0x13097c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13097c4: ldur            x16, [fp, #-0x28]
    // 0x13097c8: str             x16, [SP]
    // 0x13097cc: r0 = _interpolate()
    //     0x13097cc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13097d0: ldur            x1, [fp, #-0x18]
    // 0x13097d4: ArrayStore: r1[13] = r0  ; List_4
    //     0x13097d4: add             x25, x1, #0x43
    //     0x13097d8: str             w0, [x25]
    //     0x13097dc: tbz             w0, #0, #0x13097f8
    //     0x13097e0: ldurb           w16, [x1, #-1]
    //     0x13097e4: ldurb           w17, [x0, #-1]
    //     0x13097e8: and             x16, x17, x16, lsr #2
    //     0x13097ec: tst             x16, HEAP, lsr #32
    //     0x13097f0: b.eq            #0x13097f8
    //     0x13097f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13097f8: r16 = <String, dynamic>
    //     0x13097f8: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x13097fc: ldur            lr, [fp, #-0x18]
    // 0x1309800: stp             lr, x16, [SP]
    // 0x1309804: r0 = Map._fromLiteral()
    //     0x1309804: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x1309808: r16 = "/login"
    //     0x1309808: add             x16, PP, #0xd, lsl #12  ; [pp+0xd880] "/login"
    //     0x130980c: ldr             x16, [x16, #0x880]
    // 0x1309810: stp             x16, NULL, [SP, #8]
    // 0x1309814: str             x0, [SP]
    // 0x1309818: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x1309818: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130981c: ldr             x4, [x4, #0x438]
    // 0x1309820: r0 = GetNavigation.toNamed()
    //     0x1309820: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1309824: mov             x1, x0
    // 0x1309828: stur            x1, [fp, #-0x18]
    // 0x130982c: r0 = Await()
    //     0x130982c: bl              #0x63248c  ; AwaitStub
    // 0x1309830: r0 = Null
    //     0x1309830: mov             x0, NULL
    // 0x1309834: r0 = ReturnAsyncNotFuture()
    //     0x1309834: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1309838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1309838: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130983c: b               #0x1308f68
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1309840, size: 0xcc
    // 0x1309840: EnterFrame
    //     0x1309840: stp             fp, lr, [SP, #-0x10]!
    //     0x1309844: mov             fp, SP
    // 0x1309848: AllocStack(0x20)
    //     0x1309848: sub             SP, SP, #0x20
    // 0x130984c: SetupParameters()
    //     0x130984c: ldr             x0, [fp, #0x10]
    //     0x1309850: ldur            w2, [x0, #0x17]
    //     0x1309854: add             x2, x2, HEAP, lsl #32
    //     0x1309858: stur            x2, [fp, #-8]
    // 0x130985c: CheckStackOverflow
    //     0x130985c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1309860: cmp             SP, x16
    //     0x1309864: b.ls            #0x1309904
    // 0x1309868: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1309868: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130986c: ldr             x0, [x0, #0x1c80]
    //     0x1309870: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1309874: cmp             w0, w16
    //     0x1309878: b.ne            #0x1309884
    //     0x130987c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1309880: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1309884: str             NULL, [SP]
    // 0x1309888: r4 = const [0x1, 0, 0, 0, null]
    //     0x1309888: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x130988c: r0 = GetNavigation.back()
    //     0x130988c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1309890: r16 = PreferenceManager
    //     0x1309890: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x1309894: ldr             x16, [x16, #0x878]
    // 0x1309898: str             x16, [SP]
    // 0x130989c: r0 = toString()
    //     0x130989c: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x13098a0: r16 = <PreferenceManager>
    //     0x13098a0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x13098a4: ldr             x16, [x16, #0x880]
    // 0x13098a8: stp             x0, x16, [SP]
    // 0x13098ac: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x13098ac: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x13098b0: r0 = Inst.find()
    //     0x13098b0: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x13098b4: mov             x1, x0
    // 0x13098b8: r2 = "token"
    //     0x13098b8: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x13098bc: ldr             x2, [x2, #0x958]
    // 0x13098c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13098c0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13098c4: r0 = getString()
    //     0x13098c4: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x13098c8: ldur            x2, [fp, #-8]
    // 0x13098cc: r1 = Function '<anonymous closure>':.
    //     0x13098cc: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b950] AnonymousClosure: (0x1308f28), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x13098d0: ldr             x1, [x1, #0x950]
    // 0x13098d4: stur            x0, [fp, #-8]
    // 0x13098d8: r0 = AllocateClosure()
    //     0x13098d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13098dc: r16 = <Null?>
    //     0x13098dc: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x13098e0: ldur            lr, [fp, #-8]
    // 0x13098e4: stp             lr, x16, [SP, #8]
    // 0x13098e8: str             x0, [SP]
    // 0x13098ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x13098ec: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x13098f0: r0 = then()
    //     0x13098f0: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x13098f4: r0 = Null
    //     0x13098f4: mov             x0, NULL
    // 0x13098f8: LeaveFrame
    //     0x13098f8: mov             SP, fp
    //     0x13098fc: ldp             fp, lr, [SP], #0x10
    // 0x1309900: ret
    //     0x1309900: ret             
    // 0x1309904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1309904: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1309908: b               #0x1309868
  }
  [closure] SingleExchangeProductBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x130990c, size: 0x1d0
    // 0x130990c: EnterFrame
    //     0x130990c: stp             fp, lr, [SP, #-0x10]!
    //     0x1309910: mov             fp, SP
    // 0x1309914: AllocStack(0x58)
    //     0x1309914: sub             SP, SP, #0x58
    // 0x1309918: SetupParameters()
    //     0x1309918: ldr             x0, [fp, #0x18]
    //     0x130991c: ldur            w2, [x0, #0x17]
    //     0x1309920: add             x2, x2, HEAP, lsl #32
    //     0x1309924: stur            x2, [fp, #-8]
    // 0x1309928: CheckStackOverflow
    //     0x1309928: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130992c: cmp             SP, x16
    //     0x1309930: b.ls            #0x1309ad4
    // 0x1309934: LoadField: r1 = r2->field_f
    //     0x1309934: ldur            w1, [x2, #0xf]
    // 0x1309938: DecompressPointer r1
    //     0x1309938: add             x1, x1, HEAP, lsl #32
    // 0x130993c: r0 = controller()
    //     0x130993c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309940: mov             x1, x0
    // 0x1309944: r0 = offersResponse()
    //     0x1309944: bl              #0x9eefa8  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::offersResponse
    // 0x1309948: ldur            x2, [fp, #-8]
    // 0x130994c: stur            x0, [fp, #-0x10]
    // 0x1309950: LoadField: r1 = r2->field_f
    //     0x1309950: ldur            w1, [x2, #0xf]
    // 0x1309954: DecompressPointer r1
    //     0x1309954: add             x1, x1, HEAP, lsl #32
    // 0x1309958: r0 = controller()
    //     0x1309958: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130995c: LoadField: r2 = r0->field_bf
    //     0x130995c: ldur            w2, [x0, #0xbf]
    // 0x1309960: DecompressPointer r2
    //     0x1309960: add             x2, x2, HEAP, lsl #32
    // 0x1309964: ldur            x0, [fp, #-8]
    // 0x1309968: stur            x2, [fp, #-0x18]
    // 0x130996c: LoadField: r1 = r0->field_f
    //     0x130996c: ldur            w1, [x0, #0xf]
    // 0x1309970: DecompressPointer r1
    //     0x1309970: add             x1, x1, HEAP, lsl #32
    // 0x1309974: r0 = controller()
    //     0x1309974: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309978: LoadField: r2 = r0->field_c3
    //     0x1309978: ldur            w2, [x0, #0xc3]
    // 0x130997c: DecompressPointer r2
    //     0x130997c: add             x2, x2, HEAP, lsl #32
    // 0x1309980: ldur            x0, [fp, #-8]
    // 0x1309984: stur            x2, [fp, #-0x20]
    // 0x1309988: LoadField: r1 = r0->field_f
    //     0x1309988: ldur            w1, [x0, #0xf]
    // 0x130998c: DecompressPointer r1
    //     0x130998c: add             x1, x1, HEAP, lsl #32
    // 0x1309990: r0 = controller()
    //     0x1309990: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309994: LoadField: r2 = r0->field_c7
    //     0x1309994: ldur            w2, [x0, #0xc7]
    // 0x1309998: DecompressPointer r2
    //     0x1309998: add             x2, x2, HEAP, lsl #32
    // 0x130999c: ldur            x0, [fp, #-8]
    // 0x13099a0: stur            x2, [fp, #-0x28]
    // 0x13099a4: LoadField: r1 = r0->field_f
    //     0x13099a4: ldur            w1, [x0, #0xf]
    // 0x13099a8: DecompressPointer r1
    //     0x13099a8: add             x1, x1, HEAP, lsl #32
    // 0x13099ac: r0 = controller()
    //     0x13099ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13099b0: LoadField: r1 = r0->field_ab
    //     0x13099b0: ldur            w1, [x0, #0xab]
    // 0x13099b4: DecompressPointer r1
    //     0x13099b4: add             x1, x1, HEAP, lsl #32
    // 0x13099b8: r0 = value()
    //     0x13099b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13099bc: r1 = 60
    //     0x13099bc: movz            x1, #0x3c
    // 0x13099c0: branchIfSmi(r0, 0x13099cc)
    //     0x13099c0: tbz             w0, #0, #0x13099cc
    // 0x13099c4: r1 = LoadClassIdInstr(r0)
    //     0x13099c4: ldur            x1, [x0, #-1]
    //     0x13099c8: ubfx            x1, x1, #0xc, #0x14
    // 0x13099cc: str             x0, [SP]
    // 0x13099d0: mov             x0, x1
    // 0x13099d4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x13099d4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x13099d8: r0 = GDT[cid_x0 + 0x2700]()
    //     0x13099d8: movz            x17, #0x2700
    //     0x13099dc: add             lr, x0, x17
    //     0x13099e0: ldr             lr, [x21, lr, lsl #3]
    //     0x13099e4: blr             lr
    // 0x13099e8: ldur            x2, [fp, #-8]
    // 0x13099ec: stur            x0, [fp, #-0x30]
    // 0x13099f0: LoadField: r1 = r2->field_f
    //     0x13099f0: ldur            w1, [x2, #0xf]
    // 0x13099f4: DecompressPointer r1
    //     0x13099f4: add             x1, x1, HEAP, lsl #32
    // 0x13099f8: r0 = controller()
    //     0x13099f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13099fc: LoadField: r2 = r0->field_9b
    //     0x13099fc: ldur            w2, [x0, #0x9b]
    // 0x1309a00: DecompressPointer r2
    //     0x1309a00: add             x2, x2, HEAP, lsl #32
    // 0x1309a04: ldur            x0, [fp, #-8]
    // 0x1309a08: stur            x2, [fp, #-0x38]
    // 0x1309a0c: LoadField: r1 = r0->field_f
    //     0x1309a0c: ldur            w1, [x0, #0xf]
    // 0x1309a10: DecompressPointer r1
    //     0x1309a10: add             x1, x1, HEAP, lsl #32
    // 0x1309a14: r0 = controller()
    //     0x1309a14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309a18: mov             x1, x0
    // 0x1309a1c: r0 = getCustomisedPrice()
    //     0x1309a1c: bl              #0x1306ca0  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::getCustomisedPrice
    // 0x1309a20: ldur            x2, [fp, #-8]
    // 0x1309a24: stur            x0, [fp, #-0x40]
    // 0x1309a28: LoadField: r1 = r2->field_f
    //     0x1309a28: ldur            w1, [x2, #0xf]
    // 0x1309a2c: DecompressPointer r1
    //     0x1309a2c: add             x1, x1, HEAP, lsl #32
    // 0x1309a30: r0 = controller()
    //     0x1309a30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309a34: LoadField: r1 = r0->field_5b
    //     0x1309a34: ldur            w1, [x0, #0x5b]
    // 0x1309a38: DecompressPointer r1
    //     0x1309a38: add             x1, x1, HEAP, lsl #32
    // 0x1309a3c: r0 = value()
    //     0x1309a3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309a40: stur            x0, [fp, #-0x48]
    // 0x1309a44: r0 = SingleExchangeProductBottomSheet()
    //     0x1309a44: bl              #0xbe39d8  ; AllocateSingleExchangeProductBottomSheetStub -> SingleExchangeProductBottomSheet (size=0x34)
    // 0x1309a48: mov             x3, x0
    // 0x1309a4c: ldur            x0, [fp, #-0x10]
    // 0x1309a50: stur            x3, [fp, #-0x50]
    // 0x1309a54: StoreField: r3->field_b = r0
    //     0x1309a54: stur            w0, [x3, #0xb]
    // 0x1309a58: ldur            x0, [fp, #-0x18]
    // 0x1309a5c: StoreField: r3->field_f = r0
    //     0x1309a5c: stur            w0, [x3, #0xf]
    // 0x1309a60: ldur            x0, [fp, #-0x20]
    // 0x1309a64: ArrayStore: r3[0] = r0  ; List_4
    //     0x1309a64: stur            w0, [x3, #0x17]
    // 0x1309a68: ldur            x0, [fp, #-0x28]
    // 0x1309a6c: StoreField: r3->field_13 = r0
    //     0x1309a6c: stur            w0, [x3, #0x13]
    // 0x1309a70: ldur            x0, [fp, #-0x30]
    // 0x1309a74: StoreField: r3->field_1b = r0
    //     0x1309a74: stur            w0, [x3, #0x1b]
    // 0x1309a78: ldur            x2, [fp, #-8]
    // 0x1309a7c: r1 = Function '<anonymous closure>':.
    //     0x1309a7c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b940] AnonymousClosure: (0x1309adc), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x1309a80: ldr             x1, [x1, #0x940]
    // 0x1309a84: r0 = AllocateClosure()
    //     0x1309a84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1309a88: mov             x1, x0
    // 0x1309a8c: ldur            x0, [fp, #-0x50]
    // 0x1309a90: StoreField: r0->field_1f = r1
    //     0x1309a90: stur            w1, [x0, #0x1f]
    // 0x1309a94: ldur            x2, [fp, #-8]
    // 0x1309a98: r1 = Function '<anonymous closure>':.
    //     0x1309a98: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b948] AnonymousClosure: (0x1309840), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x1309a9c: ldr             x1, [x1, #0x948]
    // 0x1309aa0: r0 = AllocateClosure()
    //     0x1309aa0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1309aa4: mov             x1, x0
    // 0x1309aa8: ldur            x0, [fp, #-0x50]
    // 0x1309aac: StoreField: r0->field_23 = r1
    //     0x1309aac: stur            w1, [x0, #0x23]
    // 0x1309ab0: ldur            x1, [fp, #-0x38]
    // 0x1309ab4: StoreField: r0->field_2b = r1
    //     0x1309ab4: stur            w1, [x0, #0x2b]
    // 0x1309ab8: ldur            x1, [fp, #-0x40]
    // 0x1309abc: StoreField: r0->field_27 = r1
    //     0x1309abc: stur            w1, [x0, #0x27]
    // 0x1309ac0: ldur            x1, [fp, #-0x48]
    // 0x1309ac4: StoreField: r0->field_2f = r1
    //     0x1309ac4: stur            w1, [x0, #0x2f]
    // 0x1309ac8: LeaveFrame
    //     0x1309ac8: mov             SP, fp
    //     0x1309acc: ldp             fp, lr, [SP], #0x10
    // 0x1309ad0: ret
    //     0x1309ad0: ret             
    // 0x1309ad4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1309ad4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1309ad8: b               #0x1309934
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1309adc, size: 0xcc
    // 0x1309adc: EnterFrame
    //     0x1309adc: stp             fp, lr, [SP, #-0x10]!
    //     0x1309ae0: mov             fp, SP
    // 0x1309ae4: AllocStack(0x20)
    //     0x1309ae4: sub             SP, SP, #0x20
    // 0x1309ae8: SetupParameters()
    //     0x1309ae8: ldr             x0, [fp, #0x10]
    //     0x1309aec: ldur            w2, [x0, #0x17]
    //     0x1309af0: add             x2, x2, HEAP, lsl #32
    //     0x1309af4: stur            x2, [fp, #-8]
    // 0x1309af8: CheckStackOverflow
    //     0x1309af8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1309afc: cmp             SP, x16
    //     0x1309b00: b.ls            #0x1309ba0
    // 0x1309b04: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1309b04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1309b08: ldr             x0, [x0, #0x1c80]
    //     0x1309b0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1309b10: cmp             w0, w16
    //     0x1309b14: b.ne            #0x1309b20
    //     0x1309b18: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1309b1c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1309b20: str             NULL, [SP]
    // 0x1309b24: r4 = const [0x1, 0, 0, 0, null]
    //     0x1309b24: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x1309b28: r0 = GetNavigation.back()
    //     0x1309b28: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1309b2c: r16 = PreferenceManager
    //     0x1309b2c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x1309b30: ldr             x16, [x16, #0x878]
    // 0x1309b34: str             x16, [SP]
    // 0x1309b38: r0 = toString()
    //     0x1309b38: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1309b3c: r16 = <PreferenceManager>
    //     0x1309b3c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x1309b40: ldr             x16, [x16, #0x880]
    // 0x1309b44: stp             x0, x16, [SP]
    // 0x1309b48: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1309b48: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1309b4c: r0 = Inst.find()
    //     0x1309b4c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1309b50: mov             x1, x0
    // 0x1309b54: r2 = "token"
    //     0x1309b54: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x1309b58: ldr             x2, [x2, #0x958]
    // 0x1309b5c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1309b5c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1309b60: r0 = getString()
    //     0x1309b60: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x1309b64: ldur            x2, [fp, #-8]
    // 0x1309b68: r1 = Function '<anonymous closure>':.
    //     0x1309b68: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b958] AnonymousClosure: (0x1309ba8), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x1309b6c: ldr             x1, [x1, #0x958]
    // 0x1309b70: stur            x0, [fp, #-8]
    // 0x1309b74: r0 = AllocateClosure()
    //     0x1309b74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1309b78: r16 = <Null?>
    //     0x1309b78: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x1309b7c: ldur            lr, [fp, #-8]
    // 0x1309b80: stp             lr, x16, [SP, #8]
    // 0x1309b84: str             x0, [SP]
    // 0x1309b88: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x1309b88: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x1309b8c: r0 = then()
    //     0x1309b8c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x1309b90: r0 = Null
    //     0x1309b90: mov             x0, NULL
    // 0x1309b94: LeaveFrame
    //     0x1309b94: mov             SP, fp
    //     0x1309b98: ldp             fp, lr, [SP], #0x10
    // 0x1309b9c: ret
    //     0x1309b9c: ret             
    // 0x1309ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1309ba0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1309ba4: b               #0x1309b04
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x1309ba8, size: 0xc78
    // 0x1309ba8: EnterFrame
    //     0x1309ba8: stp             fp, lr, [SP, #-0x10]!
    //     0x1309bac: mov             fp, SP
    // 0x1309bb0: AllocStack(0x60)
    //     0x1309bb0: sub             SP, SP, #0x60
    // 0x1309bb4: SetupParameters(CustomizedPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1309bb4: stur            NULL, [fp, #-8]
    //     0x1309bb8: movz            x0, #0
    //     0x1309bbc: add             x1, fp, w0, sxtw #2
    //     0x1309bc0: ldr             x1, [x1, #0x18]
    //     0x1309bc4: add             x2, fp, w0, sxtw #2
    //     0x1309bc8: ldr             x2, [x2, #0x10]
    //     0x1309bcc: stur            x2, [fp, #-0x18]
    //     0x1309bd0: ldur            w3, [x1, #0x17]
    //     0x1309bd4: add             x3, x3, HEAP, lsl #32
    //     0x1309bd8: stur            x3, [fp, #-0x10]
    // 0x1309bdc: CheckStackOverflow
    //     0x1309bdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1309be0: cmp             SP, x16
    //     0x1309be4: b.ls            #0x130a818
    // 0x1309be8: InitAsync() -> Future<Null?>
    //     0x1309be8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x1309bec: bl              #0x6326e0  ; InitAsyncStub
    // 0x1309bf0: ldur            x0, [fp, #-0x18]
    // 0x1309bf4: r1 = LoadClassIdInstr(r0)
    //     0x1309bf4: ldur            x1, [x0, #-1]
    //     0x1309bf8: ubfx            x1, x1, #0xc, #0x14
    // 0x1309bfc: r16 = ""
    //     0x1309bfc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1309c00: stp             x16, x0, [SP]
    // 0x1309c04: mov             x0, x1
    // 0x1309c08: mov             lr, x0
    // 0x1309c0c: ldr             lr, [x21, lr, lsl #3]
    // 0x1309c10: blr             lr
    // 0x1309c14: tbz             w0, #4, #0x130a55c
    // 0x1309c18: ldur            x0, [fp, #-0x10]
    // 0x1309c1c: LoadField: r1 = r0->field_f
    //     0x1309c1c: ldur            w1, [x0, #0xf]
    // 0x1309c20: DecompressPointer r1
    //     0x1309c20: add             x1, x1, HEAP, lsl #32
    // 0x1309c24: r0 = controller()
    //     0x1309c24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309c28: LoadField: r1 = r0->field_63
    //     0x1309c28: ldur            w1, [x0, #0x63]
    // 0x1309c2c: DecompressPointer r1
    //     0x1309c2c: add             x1, x1, HEAP, lsl #32
    // 0x1309c30: cmp             w1, NULL
    // 0x1309c34: b.ne            #0x1309c40
    // 0x1309c38: r0 = Null
    //     0x1309c38: mov             x0, NULL
    // 0x1309c3c: b               #0x1309c6c
    // 0x1309c40: LoadField: r0 = r1->field_23
    //     0x1309c40: ldur            w0, [x1, #0x23]
    // 0x1309c44: DecompressPointer r0
    //     0x1309c44: add             x0, x0, HEAP, lsl #32
    // 0x1309c48: cmp             w0, NULL
    // 0x1309c4c: b.ne            #0x1309c58
    // 0x1309c50: r0 = Null
    //     0x1309c50: mov             x0, NULL
    // 0x1309c54: b               #0x1309c6c
    // 0x1309c58: LoadField: r1 = r0->field_b
    //     0x1309c58: ldur            w1, [x0, #0xb]
    // 0x1309c5c: cbnz            w1, #0x1309c68
    // 0x1309c60: r0 = false
    //     0x1309c60: add             x0, NULL, #0x30  ; false
    // 0x1309c64: b               #0x1309c6c
    // 0x1309c68: r0 = true
    //     0x1309c68: add             x0, NULL, #0x20  ; true
    // 0x1309c6c: cmp             w0, NULL
    // 0x1309c70: b.eq            #0x1309d40
    // 0x1309c74: tbnz            w0, #4, #0x1309d40
    // 0x1309c78: ldur            x0, [fp, #-0x10]
    // 0x1309c7c: LoadField: r1 = r0->field_f
    //     0x1309c7c: ldur            w1, [x0, #0xf]
    // 0x1309c80: DecompressPointer r1
    //     0x1309c80: add             x1, x1, HEAP, lsl #32
    // 0x1309c84: r0 = controller()
    //     0x1309c84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309c88: LoadField: r2 = r0->field_9b
    //     0x1309c88: ldur            w2, [x0, #0x9b]
    // 0x1309c8c: DecompressPointer r2
    //     0x1309c8c: add             x2, x2, HEAP, lsl #32
    // 0x1309c90: ldur            x0, [fp, #-0x10]
    // 0x1309c94: stur            x2, [fp, #-0x18]
    // 0x1309c98: LoadField: r1 = r0->field_f
    //     0x1309c98: ldur            w1, [x0, #0xf]
    // 0x1309c9c: DecompressPointer r1
    //     0x1309c9c: add             x1, x1, HEAP, lsl #32
    // 0x1309ca0: r0 = controller()
    //     0x1309ca0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309ca4: LoadField: r1 = r0->field_63
    //     0x1309ca4: ldur            w1, [x0, #0x63]
    // 0x1309ca8: DecompressPointer r1
    //     0x1309ca8: add             x1, x1, HEAP, lsl #32
    // 0x1309cac: cmp             w1, NULL
    // 0x1309cb0: b.ne            #0x1309cc0
    // 0x1309cb4: r0 = ProductCustomisation()
    //     0x1309cb4: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0x1309cb8: mov             x2, x0
    // 0x1309cbc: b               #0x1309cc4
    // 0x1309cc0: mov             x2, x1
    // 0x1309cc4: ldur            x0, [fp, #-0x18]
    // 0x1309cc8: stur            x2, [fp, #-0x28]
    // 0x1309ccc: LoadField: r1 = r0->field_b
    //     0x1309ccc: ldur            w1, [x0, #0xb]
    // 0x1309cd0: LoadField: r3 = r0->field_f
    //     0x1309cd0: ldur            w3, [x0, #0xf]
    // 0x1309cd4: DecompressPointer r3
    //     0x1309cd4: add             x3, x3, HEAP, lsl #32
    // 0x1309cd8: LoadField: r4 = r3->field_b
    //     0x1309cd8: ldur            w4, [x3, #0xb]
    // 0x1309cdc: r3 = LoadInt32Instr(r1)
    //     0x1309cdc: sbfx            x3, x1, #1, #0x1f
    // 0x1309ce0: stur            x3, [fp, #-0x20]
    // 0x1309ce4: r1 = LoadInt32Instr(r4)
    //     0x1309ce4: sbfx            x1, x4, #1, #0x1f
    // 0x1309ce8: cmp             x3, x1
    // 0x1309cec: b.ne            #0x1309cf8
    // 0x1309cf0: mov             x1, x0
    // 0x1309cf4: r0 = _growToNextCapacity()
    //     0x1309cf4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1309cf8: ldur            x0, [fp, #-0x18]
    // 0x1309cfc: ldur            x2, [fp, #-0x20]
    // 0x1309d00: add             x1, x2, #1
    // 0x1309d04: lsl             x3, x1, #1
    // 0x1309d08: StoreField: r0->field_b = r3
    //     0x1309d08: stur            w3, [x0, #0xb]
    // 0x1309d0c: LoadField: r1 = r0->field_f
    //     0x1309d0c: ldur            w1, [x0, #0xf]
    // 0x1309d10: DecompressPointer r1
    //     0x1309d10: add             x1, x1, HEAP, lsl #32
    // 0x1309d14: ldur            x0, [fp, #-0x28]
    // 0x1309d18: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1309d18: add             x25, x1, x2, lsl #2
    //     0x1309d1c: add             x25, x25, #0xf
    //     0x1309d20: str             w0, [x25]
    //     0x1309d24: tbz             w0, #0, #0x1309d40
    //     0x1309d28: ldurb           w16, [x1, #-1]
    //     0x1309d2c: ldurb           w17, [x0, #-1]
    //     0x1309d30: and             x16, x17, x16, lsr #2
    //     0x1309d34: tst             x16, HEAP, lsr #32
    //     0x1309d38: b.eq            #0x1309d40
    //     0x1309d3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309d40: ldur            x0, [fp, #-0x10]
    // 0x1309d44: LoadField: r1 = r0->field_f
    //     0x1309d44: ldur            w1, [x0, #0xf]
    // 0x1309d48: DecompressPointer r1
    //     0x1309d48: add             x1, x1, HEAP, lsl #32
    // 0x1309d4c: r0 = controller()
    //     0x1309d4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309d50: LoadField: r1 = r0->field_8f
    //     0x1309d50: ldur            w1, [x0, #0x8f]
    // 0x1309d54: DecompressPointer r1
    //     0x1309d54: add             x1, x1, HEAP, lsl #32
    // 0x1309d58: r0 = value()
    //     0x1309d58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309d5c: mov             x2, x0
    // 0x1309d60: ldur            x0, [fp, #-0x10]
    // 0x1309d64: stur            x2, [fp, #-0x18]
    // 0x1309d68: LoadField: r1 = r0->field_f
    //     0x1309d68: ldur            w1, [x0, #0xf]
    // 0x1309d6c: DecompressPointer r1
    //     0x1309d6c: add             x1, x1, HEAP, lsl #32
    // 0x1309d70: r0 = controller()
    //     0x1309d70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309d74: LoadField: r1 = r0->field_93
    //     0x1309d74: ldur            w1, [x0, #0x93]
    // 0x1309d78: DecompressPointer r1
    //     0x1309d78: add             x1, x1, HEAP, lsl #32
    // 0x1309d7c: r0 = value()
    //     0x1309d7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309d80: mov             x2, x0
    // 0x1309d84: ldur            x0, [fp, #-0x10]
    // 0x1309d88: stur            x2, [fp, #-0x28]
    // 0x1309d8c: LoadField: r1 = r0->field_f
    //     0x1309d8c: ldur            w1, [x0, #0xf]
    // 0x1309d90: DecompressPointer r1
    //     0x1309d90: add             x1, x1, HEAP, lsl #32
    // 0x1309d94: r0 = controller()
    //     0x1309d94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309d98: LoadField: r1 = r0->field_ab
    //     0x1309d98: ldur            w1, [x0, #0xab]
    // 0x1309d9c: DecompressPointer r1
    //     0x1309d9c: add             x1, x1, HEAP, lsl #32
    // 0x1309da0: r0 = value()
    //     0x1309da0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309da4: mov             x2, x0
    // 0x1309da8: ldur            x0, [fp, #-0x10]
    // 0x1309dac: stur            x2, [fp, #-0x30]
    // 0x1309db0: LoadField: r1 = r0->field_f
    //     0x1309db0: ldur            w1, [x0, #0xf]
    // 0x1309db4: DecompressPointer r1
    //     0x1309db4: add             x1, x1, HEAP, lsl #32
    // 0x1309db8: r0 = controller()
    //     0x1309db8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309dbc: LoadField: r2 = r0->field_9b
    //     0x1309dbc: ldur            w2, [x0, #0x9b]
    // 0x1309dc0: DecompressPointer r2
    //     0x1309dc0: add             x2, x2, HEAP, lsl #32
    // 0x1309dc4: ldur            x0, [fp, #-0x10]
    // 0x1309dc8: stur            x2, [fp, #-0x38]
    // 0x1309dcc: LoadField: r1 = r0->field_f
    //     0x1309dcc: ldur            w1, [x0, #0xf]
    // 0x1309dd0: DecompressPointer r1
    //     0x1309dd0: add             x1, x1, HEAP, lsl #32
    // 0x1309dd4: r0 = controller()
    //     0x1309dd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309dd8: LoadField: r1 = r0->field_97
    //     0x1309dd8: ldur            w1, [x0, #0x97]
    // 0x1309ddc: DecompressPointer r1
    //     0x1309ddc: add             x1, x1, HEAP, lsl #32
    // 0x1309de0: r0 = value()
    //     0x1309de0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309de4: stur            x0, [fp, #-0x40]
    // 0x1309de8: r0 = CustomizedRequest()
    //     0x1309de8: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x1309dec: mov             x2, x0
    // 0x1309df0: ldur            x0, [fp, #-0x18]
    // 0x1309df4: stur            x2, [fp, #-0x48]
    // 0x1309df8: StoreField: r2->field_7 = r0
    //     0x1309df8: stur            w0, [x2, #7]
    // 0x1309dfc: ldur            x0, [fp, #-0x28]
    // 0x1309e00: StoreField: r2->field_b = r0
    //     0x1309e00: stur            w0, [x2, #0xb]
    // 0x1309e04: ldur            x0, [fp, #-0x30]
    // 0x1309e08: StoreField: r2->field_f = r0
    //     0x1309e08: stur            w0, [x2, #0xf]
    // 0x1309e0c: ldur            x0, [fp, #-0x38]
    // 0x1309e10: StoreField: r2->field_13 = r0
    //     0x1309e10: stur            w0, [x2, #0x13]
    // 0x1309e14: ldur            x0, [fp, #-0x40]
    // 0x1309e18: ArrayStore: r2[0] = r0  ; List_4
    //     0x1309e18: stur            w0, [x2, #0x17]
    // 0x1309e1c: ldur            x0, [fp, #-0x10]
    // 0x1309e20: LoadField: r1 = r0->field_f
    //     0x1309e20: ldur            w1, [x0, #0xf]
    // 0x1309e24: DecompressPointer r1
    //     0x1309e24: add             x1, x1, HEAP, lsl #32
    // 0x1309e28: r0 = controller()
    //     0x1309e28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309e2c: LoadField: r1 = r0->field_5f
    //     0x1309e2c: ldur            w1, [x0, #0x5f]
    // 0x1309e30: DecompressPointer r1
    //     0x1309e30: add             x1, x1, HEAP, lsl #32
    // 0x1309e34: r0 = value()
    //     0x1309e34: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309e38: LoadField: r1 = r0->field_1b
    //     0x1309e38: ldur            w1, [x0, #0x1b]
    // 0x1309e3c: DecompressPointer r1
    //     0x1309e3c: add             x1, x1, HEAP, lsl #32
    // 0x1309e40: cmp             w1, NULL
    // 0x1309e44: b.ne            #0x1309e50
    // 0x1309e48: r0 = Null
    //     0x1309e48: mov             x0, NULL
    // 0x1309e4c: b               #0x1309e68
    // 0x1309e50: LoadField: r0 = r1->field_b
    //     0x1309e50: ldur            w0, [x1, #0xb]
    // 0x1309e54: cbz             w0, #0x1309e60
    // 0x1309e58: r1 = false
    //     0x1309e58: add             x1, NULL, #0x30  ; false
    // 0x1309e5c: b               #0x1309e64
    // 0x1309e60: r1 = true
    //     0x1309e60: add             x1, NULL, #0x20  ; true
    // 0x1309e64: mov             x0, x1
    // 0x1309e68: cmp             w0, NULL
    // 0x1309e6c: b.eq            #0x1309e74
    // 0x1309e70: tbz             w0, #4, #0x1309f00
    // 0x1309e74: ldur            x0, [fp, #-0x10]
    // 0x1309e78: LoadField: r1 = r0->field_f
    //     0x1309e78: ldur            w1, [x0, #0xf]
    // 0x1309e7c: DecompressPointer r1
    //     0x1309e7c: add             x1, x1, HEAP, lsl #32
    // 0x1309e80: r0 = controller()
    //     0x1309e80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309e84: LoadField: r1 = r0->field_5f
    //     0x1309e84: ldur            w1, [x0, #0x5f]
    // 0x1309e88: DecompressPointer r1
    //     0x1309e88: add             x1, x1, HEAP, lsl #32
    // 0x1309e8c: r0 = value()
    //     0x1309e8c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309e90: LoadField: r1 = r0->field_1b
    //     0x1309e90: ldur            w1, [x0, #0x1b]
    // 0x1309e94: DecompressPointer r1
    //     0x1309e94: add             x1, x1, HEAP, lsl #32
    // 0x1309e98: cmp             w1, NULL
    // 0x1309e9c: b.ne            #0x1309ea8
    // 0x1309ea0: r0 = Null
    //     0x1309ea0: mov             x0, NULL
    // 0x1309ea4: b               #0x1309eec
    // 0x1309ea8: r0 = first()
    //     0x1309ea8: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0x1309eac: cmp             w0, NULL
    // 0x1309eb0: b.ne            #0x1309ebc
    // 0x1309eb4: r0 = Null
    //     0x1309eb4: mov             x0, NULL
    // 0x1309eb8: b               #0x1309eec
    // 0x1309ebc: LoadField: r1 = r0->field_13
    //     0x1309ebc: ldur            w1, [x0, #0x13]
    // 0x1309ec0: DecompressPointer r1
    //     0x1309ec0: add             x1, x1, HEAP, lsl #32
    // 0x1309ec4: cmp             w1, NULL
    // 0x1309ec8: b.ne            #0x1309ed4
    // 0x1309ecc: r0 = Null
    //     0x1309ecc: mov             x0, NULL
    // 0x1309ed0: b               #0x1309eec
    // 0x1309ed4: LoadField: r0 = r1->field_7
    //     0x1309ed4: ldur            w0, [x1, #7]
    // 0x1309ed8: cbz             w0, #0x1309ee4
    // 0x1309edc: r1 = false
    //     0x1309edc: add             x1, NULL, #0x30  ; false
    // 0x1309ee0: b               #0x1309ee8
    // 0x1309ee4: r1 = true
    //     0x1309ee4: add             x1, NULL, #0x20  ; true
    // 0x1309ee8: mov             x0, x1
    // 0x1309eec: cmp             w0, NULL
    // 0x1309ef0: b.ne            #0x1309efc
    // 0x1309ef4: ldur            x1, [fp, #-0x10]
    // 0x1309ef8: b               #0x130a234
    // 0x1309efc: tbnz            w0, #4, #0x130a230
    // 0x1309f00: ldur            x0, [fp, #-0x10]
    // 0x1309f04: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1309f04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1309f08: ldr             x0, [x0, #0x1c80]
    //     0x1309f0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1309f10: cmp             w0, w16
    //     0x1309f14: b.ne            #0x1309f20
    //     0x1309f18: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1309f1c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1309f20: r1 = Null
    //     0x1309f20: mov             x1, NULL
    // 0x1309f24: r2 = 44
    //     0x1309f24: movz            x2, #0x2c
    // 0x1309f28: r0 = AllocateArray()
    //     0x1309f28: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1309f2c: stur            x0, [fp, #-0x18]
    // 0x1309f30: r16 = "couponCode"
    //     0x1309f30: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x1309f34: ldr             x16, [x16, #0x310]
    // 0x1309f38: StoreField: r0->field_f = r16
    //     0x1309f38: stur            w16, [x0, #0xf]
    // 0x1309f3c: r16 = ""
    //     0x1309f3c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1309f40: StoreField: r0->field_13 = r16
    //     0x1309f40: stur            w16, [x0, #0x13]
    // 0x1309f44: r16 = "product_id"
    //     0x1309f44: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x1309f48: ldr             x16, [x16, #0x9b8]
    // 0x1309f4c: ArrayStore: r0[0] = r16  ; List_4
    //     0x1309f4c: stur            w16, [x0, #0x17]
    // 0x1309f50: ldur            x2, [fp, #-0x10]
    // 0x1309f54: LoadField: r1 = r2->field_f
    //     0x1309f54: ldur            w1, [x2, #0xf]
    // 0x1309f58: DecompressPointer r1
    //     0x1309f58: add             x1, x1, HEAP, lsl #32
    // 0x1309f5c: r0 = controller()
    //     0x1309f5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309f60: LoadField: r1 = r0->field_8f
    //     0x1309f60: ldur            w1, [x0, #0x8f]
    // 0x1309f64: DecompressPointer r1
    //     0x1309f64: add             x1, x1, HEAP, lsl #32
    // 0x1309f68: r0 = value()
    //     0x1309f68: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309f6c: ldur            x1, [fp, #-0x18]
    // 0x1309f70: ArrayStore: r1[3] = r0  ; List_4
    //     0x1309f70: add             x25, x1, #0x1b
    //     0x1309f74: str             w0, [x25]
    //     0x1309f78: tbz             w0, #0, #0x1309f94
    //     0x1309f7c: ldurb           w16, [x1, #-1]
    //     0x1309f80: ldurb           w17, [x0, #-1]
    //     0x1309f84: and             x16, x17, x16, lsr #2
    //     0x1309f88: tst             x16, HEAP, lsr #32
    //     0x1309f8c: b.eq            #0x1309f94
    //     0x1309f90: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309f94: ldur            x0, [fp, #-0x18]
    // 0x1309f98: r16 = "sku_id"
    //     0x1309f98: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x1309f9c: ldr             x16, [x16, #0x498]
    // 0x1309fa0: StoreField: r0->field_1f = r16
    //     0x1309fa0: stur            w16, [x0, #0x1f]
    // 0x1309fa4: ldur            x2, [fp, #-0x10]
    // 0x1309fa8: LoadField: r1 = r2->field_f
    //     0x1309fa8: ldur            w1, [x2, #0xf]
    // 0x1309fac: DecompressPointer r1
    //     0x1309fac: add             x1, x1, HEAP, lsl #32
    // 0x1309fb0: r0 = controller()
    //     0x1309fb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1309fb4: LoadField: r1 = r0->field_93
    //     0x1309fb4: ldur            w1, [x0, #0x93]
    // 0x1309fb8: DecompressPointer r1
    //     0x1309fb8: add             x1, x1, HEAP, lsl #32
    // 0x1309fbc: r0 = value()
    //     0x1309fbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1309fc0: ldur            x1, [fp, #-0x18]
    // 0x1309fc4: ArrayStore: r1[5] = r0  ; List_4
    //     0x1309fc4: add             x25, x1, #0x23
    //     0x1309fc8: str             w0, [x25]
    //     0x1309fcc: tbz             w0, #0, #0x1309fe8
    //     0x1309fd0: ldurb           w16, [x1, #-1]
    //     0x1309fd4: ldurb           w17, [x0, #-1]
    //     0x1309fd8: and             x16, x17, x16, lsr #2
    //     0x1309fdc: tst             x16, HEAP, lsr #32
    //     0x1309fe0: b.eq            #0x1309fe8
    //     0x1309fe4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1309fe8: ldur            x0, [fp, #-0x18]
    // 0x1309fec: r16 = "coming_from"
    //     0x1309fec: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x1309ff0: ldr             x16, [x16, #0x328]
    // 0x1309ff4: StoreField: r0->field_27 = r16
    //     0x1309ff4: stur            w16, [x0, #0x27]
    // 0x1309ff8: r16 = "buyNow"
    //     0x1309ff8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x1309ffc: ldr             x16, [x16, #0x358]
    // 0x130a000: StoreField: r0->field_2b = r16
    //     0x130a000: stur            w16, [x0, #0x2b]
    // 0x130a004: r16 = "quantity"
    //     0x130a004: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130a008: ldr             x16, [x16, #0x428]
    // 0x130a00c: StoreField: r0->field_2f = r16
    //     0x130a00c: stur            w16, [x0, #0x2f]
    // 0x130a010: ldur            x2, [fp, #-0x10]
    // 0x130a014: LoadField: r1 = r2->field_f
    //     0x130a014: ldur            w1, [x2, #0xf]
    // 0x130a018: DecompressPointer r1
    //     0x130a018: add             x1, x1, HEAP, lsl #32
    // 0x130a01c: r0 = controller()
    //     0x130a01c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a020: LoadField: r1 = r0->field_ab
    //     0x130a020: ldur            w1, [x0, #0xab]
    // 0x130a024: DecompressPointer r1
    //     0x130a024: add             x1, x1, HEAP, lsl #32
    // 0x130a028: r0 = value()
    //     0x130a028: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a02c: ldur            x1, [fp, #-0x18]
    // 0x130a030: ArrayStore: r1[9] = r0  ; List_4
    //     0x130a030: add             x25, x1, #0x33
    //     0x130a034: str             w0, [x25]
    //     0x130a038: tbz             w0, #0, #0x130a054
    //     0x130a03c: ldurb           w16, [x1, #-1]
    //     0x130a040: ldurb           w17, [x0, #-1]
    //     0x130a044: and             x16, x17, x16, lsr #2
    //     0x130a048: tst             x16, HEAP, lsr #32
    //     0x130a04c: b.eq            #0x130a054
    //     0x130a050: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a054: ldur            x3, [fp, #-0x18]
    // 0x130a058: r16 = "previousScreenSource"
    //     0x130a058: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130a05c: ldr             x16, [x16, #0x448]
    // 0x130a060: StoreField: r3->field_37 = r16
    //     0x130a060: stur            w16, [x3, #0x37]
    // 0x130a064: r16 = "product_page"
    //     0x130a064: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130a068: ldr             x16, [x16, #0x480]
    // 0x130a06c: StoreField: r3->field_3b = r16
    //     0x130a06c: stur            w16, [x3, #0x3b]
    // 0x130a070: r16 = "customization_request"
    //     0x130a070: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130a074: ldr             x16, [x16, #0x2b8]
    // 0x130a078: StoreField: r3->field_3f = r16
    //     0x130a078: stur            w16, [x3, #0x3f]
    // 0x130a07c: mov             x1, x3
    // 0x130a080: ldur            x0, [fp, #-0x48]
    // 0x130a084: ArrayStore: r1[13] = r0  ; List_4
    //     0x130a084: add             x25, x1, #0x43
    //     0x130a088: str             w0, [x25]
    //     0x130a08c: tbz             w0, #0, #0x130a0a8
    //     0x130a090: ldurb           w16, [x1, #-1]
    //     0x130a094: ldurb           w17, [x0, #-1]
    //     0x130a098: and             x16, x17, x16, lsr #2
    //     0x130a09c: tst             x16, HEAP, lsr #32
    //     0x130a0a0: b.eq            #0x130a0a8
    //     0x130a0a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a0a8: r16 = "customization_prize"
    //     0x130a0a8: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130a0ac: ldr             x16, [x16, #0x2e8]
    // 0x130a0b0: StoreField: r3->field_47 = r16
    //     0x130a0b0: stur            w16, [x3, #0x47]
    // 0x130a0b4: r1 = Null
    //     0x130a0b4: mov             x1, NULL
    // 0x130a0b8: r2 = 4
    //     0x130a0b8: movz            x2, #0x4
    // 0x130a0bc: r0 = AllocateArray()
    //     0x130a0bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130a0c0: stur            x0, [fp, #-0x28]
    // 0x130a0c4: r16 = "₹"
    //     0x130a0c4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130a0c8: ldr             x16, [x16, #0x360]
    // 0x130a0cc: StoreField: r0->field_f = r16
    //     0x130a0cc: stur            w16, [x0, #0xf]
    // 0x130a0d0: r1 = Function '<anonymous closure>': static.
    //     0x130a0d0: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x130a0d4: ldr             x1, [x1, #0x1a0]
    // 0x130a0d8: r2 = Null
    //     0x130a0d8: mov             x2, NULL
    // 0x130a0dc: r0 = AllocateClosure()
    //     0x130a0dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130a0e0: mov             x3, x0
    // 0x130a0e4: r1 = Null
    //     0x130a0e4: mov             x1, NULL
    // 0x130a0e8: r2 = Null
    //     0x130a0e8: mov             x2, NULL
    // 0x130a0ec: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x130a0ec: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x130a0f0: r0 = NumberFormat._forPattern()
    //     0x130a0f0: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x130a0f4: mov             x2, x0
    // 0x130a0f8: ldur            x0, [fp, #-0x10]
    // 0x130a0fc: stur            x2, [fp, #-0x30]
    // 0x130a100: LoadField: r1 = r0->field_f
    //     0x130a100: ldur            w1, [x0, #0xf]
    // 0x130a104: DecompressPointer r1
    //     0x130a104: add             x1, x1, HEAP, lsl #32
    // 0x130a108: r0 = controller()
    //     0x130a108: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a10c: LoadField: r1 = r0->field_67
    //     0x130a10c: ldur            w1, [x0, #0x67]
    // 0x130a110: DecompressPointer r1
    //     0x130a110: add             x1, x1, HEAP, lsl #32
    // 0x130a114: r0 = value()
    //     0x130a114: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a118: ldur            x1, [fp, #-0x30]
    // 0x130a11c: mov             x2, x0
    // 0x130a120: r0 = format()
    //     0x130a120: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130a124: ldur            x1, [fp, #-0x28]
    // 0x130a128: ArrayStore: r1[1] = r0  ; List_4
    //     0x130a128: add             x25, x1, #0x13
    //     0x130a12c: str             w0, [x25]
    //     0x130a130: tbz             w0, #0, #0x130a14c
    //     0x130a134: ldurb           w16, [x1, #-1]
    //     0x130a138: ldurb           w17, [x0, #-1]
    //     0x130a13c: and             x16, x17, x16, lsr #2
    //     0x130a140: tst             x16, HEAP, lsr #32
    //     0x130a144: b.eq            #0x130a14c
    //     0x130a148: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a14c: ldur            x16, [fp, #-0x28]
    // 0x130a150: str             x16, [SP]
    // 0x130a154: r0 = _interpolate()
    //     0x130a154: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130a158: ldur            x1, [fp, #-0x18]
    // 0x130a15c: ArrayStore: r1[15] = r0  ; List_4
    //     0x130a15c: add             x25, x1, #0x4b
    //     0x130a160: str             w0, [x25]
    //     0x130a164: tbz             w0, #0, #0x130a180
    //     0x130a168: ldurb           w16, [x1, #-1]
    //     0x130a16c: ldurb           w17, [x0, #-1]
    //     0x130a170: and             x16, x17, x16, lsr #2
    //     0x130a174: tst             x16, HEAP, lsr #32
    //     0x130a178: b.eq            #0x130a180
    //     0x130a17c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a180: ldur            x0, [fp, #-0x18]
    // 0x130a184: r16 = "is_skipped_address"
    //     0x130a184: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130a188: ldr             x16, [x16, #0xb80]
    // 0x130a18c: StoreField: r0->field_4f = r16
    //     0x130a18c: stur            w16, [x0, #0x4f]
    // 0x130a190: r16 = false
    //     0x130a190: add             x16, NULL, #0x30  ; false
    // 0x130a194: StoreField: r0->field_53 = r16
    //     0x130a194: stur            w16, [x0, #0x53]
    // 0x130a198: r16 = "checkout_id"
    //     0x130a198: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130a19c: ldr             x16, [x16, #0xb88]
    // 0x130a1a0: StoreField: r0->field_57 = r16
    //     0x130a1a0: stur            w16, [x0, #0x57]
    // 0x130a1a4: r16 = ""
    //     0x130a1a4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130a1a8: StoreField: r0->field_5b = r16
    //     0x130a1a8: stur            w16, [x0, #0x5b]
    // 0x130a1ac: r16 = "user_data"
    //     0x130a1ac: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x130a1b0: ldr             x16, [x16, #0x58]
    // 0x130a1b4: StoreField: r0->field_5f = r16
    //     0x130a1b4: stur            w16, [x0, #0x5f]
    // 0x130a1b8: ldur            x1, [fp, #-0x10]
    // 0x130a1bc: LoadField: r2 = r1->field_f
    //     0x130a1bc: ldur            w2, [x1, #0xf]
    // 0x130a1c0: DecompressPointer r2
    //     0x130a1c0: add             x2, x2, HEAP, lsl #32
    // 0x130a1c4: mov             x1, x2
    // 0x130a1c8: r0 = controller()
    //     0x130a1c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a1cc: LoadField: r1 = r0->field_5f
    //     0x130a1cc: ldur            w1, [x0, #0x5f]
    // 0x130a1d0: DecompressPointer r1
    //     0x130a1d0: add             x1, x1, HEAP, lsl #32
    // 0x130a1d4: r0 = value()
    //     0x130a1d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a1d8: ldur            x1, [fp, #-0x18]
    // 0x130a1dc: ArrayStore: r1[21] = r0  ; List_4
    //     0x130a1dc: add             x25, x1, #0x63
    //     0x130a1e0: str             w0, [x25]
    //     0x130a1e4: tbz             w0, #0, #0x130a200
    //     0x130a1e8: ldurb           w16, [x1, #-1]
    //     0x130a1ec: ldurb           w17, [x0, #-1]
    //     0x130a1f0: and             x16, x17, x16, lsr #2
    //     0x130a1f4: tst             x16, HEAP, lsr #32
    //     0x130a1f8: b.eq            #0x130a200
    //     0x130a1fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a200: r16 = <String, dynamic>
    //     0x130a200: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130a204: ldur            lr, [fp, #-0x18]
    // 0x130a208: stp             lr, x16, [SP]
    // 0x130a20c: r0 = Map._fromLiteral()
    //     0x130a20c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130a210: r16 = "/checkout_request_address_page"
    //     0x130a210: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0x130a214: ldr             x16, [x16, #0x9e8]
    // 0x130a218: stp             x16, NULL, [SP, #8]
    // 0x130a21c: str             x0, [SP]
    // 0x130a220: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130a220: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130a224: ldr             x4, [x4, #0x438]
    // 0x130a228: r0 = GetNavigation.toNamed()
    //     0x130a228: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130a22c: b               #0x130a810
    // 0x130a230: ldur            x1, [fp, #-0x10]
    // 0x130a234: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130a234: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130a238: ldr             x0, [x0, #0x1c80]
    //     0x130a23c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130a240: cmp             w0, w16
    //     0x130a244: b.ne            #0x130a250
    //     0x130a248: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130a24c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130a250: r1 = Null
    //     0x130a250: mov             x1, NULL
    // 0x130a254: r2 = 44
    //     0x130a254: movz            x2, #0x2c
    // 0x130a258: r0 = AllocateArray()
    //     0x130a258: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130a25c: stur            x0, [fp, #-0x18]
    // 0x130a260: r16 = "couponCode"
    //     0x130a260: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x130a264: ldr             x16, [x16, #0x310]
    // 0x130a268: StoreField: r0->field_f = r16
    //     0x130a268: stur            w16, [x0, #0xf]
    // 0x130a26c: r16 = ""
    //     0x130a26c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130a270: StoreField: r0->field_13 = r16
    //     0x130a270: stur            w16, [x0, #0x13]
    // 0x130a274: r16 = "product_id"
    //     0x130a274: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130a278: ldr             x16, [x16, #0x9b8]
    // 0x130a27c: ArrayStore: r0[0] = r16  ; List_4
    //     0x130a27c: stur            w16, [x0, #0x17]
    // 0x130a280: ldur            x2, [fp, #-0x10]
    // 0x130a284: LoadField: r1 = r2->field_f
    //     0x130a284: ldur            w1, [x2, #0xf]
    // 0x130a288: DecompressPointer r1
    //     0x130a288: add             x1, x1, HEAP, lsl #32
    // 0x130a28c: r0 = controller()
    //     0x130a28c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a290: LoadField: r1 = r0->field_8f
    //     0x130a290: ldur            w1, [x0, #0x8f]
    // 0x130a294: DecompressPointer r1
    //     0x130a294: add             x1, x1, HEAP, lsl #32
    // 0x130a298: r0 = value()
    //     0x130a298: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a29c: ldur            x1, [fp, #-0x18]
    // 0x130a2a0: ArrayStore: r1[3] = r0  ; List_4
    //     0x130a2a0: add             x25, x1, #0x1b
    //     0x130a2a4: str             w0, [x25]
    //     0x130a2a8: tbz             w0, #0, #0x130a2c4
    //     0x130a2ac: ldurb           w16, [x1, #-1]
    //     0x130a2b0: ldurb           w17, [x0, #-1]
    //     0x130a2b4: and             x16, x17, x16, lsr #2
    //     0x130a2b8: tst             x16, HEAP, lsr #32
    //     0x130a2bc: b.eq            #0x130a2c4
    //     0x130a2c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a2c4: ldur            x0, [fp, #-0x18]
    // 0x130a2c8: r16 = "sku_id"
    //     0x130a2c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130a2cc: ldr             x16, [x16, #0x498]
    // 0x130a2d0: StoreField: r0->field_1f = r16
    //     0x130a2d0: stur            w16, [x0, #0x1f]
    // 0x130a2d4: ldur            x2, [fp, #-0x10]
    // 0x130a2d8: LoadField: r1 = r2->field_f
    //     0x130a2d8: ldur            w1, [x2, #0xf]
    // 0x130a2dc: DecompressPointer r1
    //     0x130a2dc: add             x1, x1, HEAP, lsl #32
    // 0x130a2e0: r0 = controller()
    //     0x130a2e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a2e4: LoadField: r1 = r0->field_93
    //     0x130a2e4: ldur            w1, [x0, #0x93]
    // 0x130a2e8: DecompressPointer r1
    //     0x130a2e8: add             x1, x1, HEAP, lsl #32
    // 0x130a2ec: r0 = value()
    //     0x130a2ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a2f0: ldur            x1, [fp, #-0x18]
    // 0x130a2f4: ArrayStore: r1[5] = r0  ; List_4
    //     0x130a2f4: add             x25, x1, #0x23
    //     0x130a2f8: str             w0, [x25]
    //     0x130a2fc: tbz             w0, #0, #0x130a318
    //     0x130a300: ldurb           w16, [x1, #-1]
    //     0x130a304: ldurb           w17, [x0, #-1]
    //     0x130a308: and             x16, x17, x16, lsr #2
    //     0x130a30c: tst             x16, HEAP, lsr #32
    //     0x130a310: b.eq            #0x130a318
    //     0x130a314: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a318: ldur            x0, [fp, #-0x18]
    // 0x130a31c: r16 = "coming_from"
    //     0x130a31c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130a320: ldr             x16, [x16, #0x328]
    // 0x130a324: StoreField: r0->field_27 = r16
    //     0x130a324: stur            w16, [x0, #0x27]
    // 0x130a328: r16 = "buyNow"
    //     0x130a328: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130a32c: ldr             x16, [x16, #0x358]
    // 0x130a330: StoreField: r0->field_2b = r16
    //     0x130a330: stur            w16, [x0, #0x2b]
    // 0x130a334: r16 = "quantity"
    //     0x130a334: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130a338: ldr             x16, [x16, #0x428]
    // 0x130a33c: StoreField: r0->field_2f = r16
    //     0x130a33c: stur            w16, [x0, #0x2f]
    // 0x130a340: ldur            x2, [fp, #-0x10]
    // 0x130a344: LoadField: r1 = r2->field_f
    //     0x130a344: ldur            w1, [x2, #0xf]
    // 0x130a348: DecompressPointer r1
    //     0x130a348: add             x1, x1, HEAP, lsl #32
    // 0x130a34c: r0 = controller()
    //     0x130a34c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a350: LoadField: r1 = r0->field_ab
    //     0x130a350: ldur            w1, [x0, #0xab]
    // 0x130a354: DecompressPointer r1
    //     0x130a354: add             x1, x1, HEAP, lsl #32
    // 0x130a358: r0 = value()
    //     0x130a358: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a35c: ldur            x1, [fp, #-0x18]
    // 0x130a360: ArrayStore: r1[9] = r0  ; List_4
    //     0x130a360: add             x25, x1, #0x33
    //     0x130a364: str             w0, [x25]
    //     0x130a368: tbz             w0, #0, #0x130a384
    //     0x130a36c: ldurb           w16, [x1, #-1]
    //     0x130a370: ldurb           w17, [x0, #-1]
    //     0x130a374: and             x16, x17, x16, lsr #2
    //     0x130a378: tst             x16, HEAP, lsr #32
    //     0x130a37c: b.eq            #0x130a384
    //     0x130a380: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a384: ldur            x3, [fp, #-0x18]
    // 0x130a388: r16 = "previousScreenSource"
    //     0x130a388: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130a38c: ldr             x16, [x16, #0x448]
    // 0x130a390: StoreField: r3->field_37 = r16
    //     0x130a390: stur            w16, [x3, #0x37]
    // 0x130a394: r16 = "product_page"
    //     0x130a394: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130a398: ldr             x16, [x16, #0x480]
    // 0x130a39c: StoreField: r3->field_3b = r16
    //     0x130a39c: stur            w16, [x3, #0x3b]
    // 0x130a3a0: r16 = "customization_request"
    //     0x130a3a0: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130a3a4: ldr             x16, [x16, #0x2b8]
    // 0x130a3a8: StoreField: r3->field_3f = r16
    //     0x130a3a8: stur            w16, [x3, #0x3f]
    // 0x130a3ac: mov             x1, x3
    // 0x130a3b0: ldur            x0, [fp, #-0x48]
    // 0x130a3b4: ArrayStore: r1[13] = r0  ; List_4
    //     0x130a3b4: add             x25, x1, #0x43
    //     0x130a3b8: str             w0, [x25]
    //     0x130a3bc: tbz             w0, #0, #0x130a3d8
    //     0x130a3c0: ldurb           w16, [x1, #-1]
    //     0x130a3c4: ldurb           w17, [x0, #-1]
    //     0x130a3c8: and             x16, x17, x16, lsr #2
    //     0x130a3cc: tst             x16, HEAP, lsr #32
    //     0x130a3d0: b.eq            #0x130a3d8
    //     0x130a3d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a3d8: r16 = "customization_prize"
    //     0x130a3d8: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130a3dc: ldr             x16, [x16, #0x2e8]
    // 0x130a3e0: StoreField: r3->field_47 = r16
    //     0x130a3e0: stur            w16, [x3, #0x47]
    // 0x130a3e4: r1 = Null
    //     0x130a3e4: mov             x1, NULL
    // 0x130a3e8: r2 = 4
    //     0x130a3e8: movz            x2, #0x4
    // 0x130a3ec: r0 = AllocateArray()
    //     0x130a3ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130a3f0: stur            x0, [fp, #-0x28]
    // 0x130a3f4: r16 = "₹"
    //     0x130a3f4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130a3f8: ldr             x16, [x16, #0x360]
    // 0x130a3fc: StoreField: r0->field_f = r16
    //     0x130a3fc: stur            w16, [x0, #0xf]
    // 0x130a400: r1 = Function '<anonymous closure>': static.
    //     0x130a400: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x130a404: ldr             x1, [x1, #0x1a0]
    // 0x130a408: r2 = Null
    //     0x130a408: mov             x2, NULL
    // 0x130a40c: r0 = AllocateClosure()
    //     0x130a40c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130a410: mov             x3, x0
    // 0x130a414: r1 = Null
    //     0x130a414: mov             x1, NULL
    // 0x130a418: r2 = Null
    //     0x130a418: mov             x2, NULL
    // 0x130a41c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x130a41c: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x130a420: r0 = NumberFormat._forPattern()
    //     0x130a420: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x130a424: mov             x2, x0
    // 0x130a428: ldur            x0, [fp, #-0x10]
    // 0x130a42c: stur            x2, [fp, #-0x30]
    // 0x130a430: LoadField: r1 = r0->field_f
    //     0x130a430: ldur            w1, [x0, #0xf]
    // 0x130a434: DecompressPointer r1
    //     0x130a434: add             x1, x1, HEAP, lsl #32
    // 0x130a438: r0 = controller()
    //     0x130a438: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a43c: LoadField: r1 = r0->field_67
    //     0x130a43c: ldur            w1, [x0, #0x67]
    // 0x130a440: DecompressPointer r1
    //     0x130a440: add             x1, x1, HEAP, lsl #32
    // 0x130a444: r0 = value()
    //     0x130a444: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a448: ldur            x1, [fp, #-0x30]
    // 0x130a44c: mov             x2, x0
    // 0x130a450: r0 = format()
    //     0x130a450: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130a454: ldur            x1, [fp, #-0x28]
    // 0x130a458: ArrayStore: r1[1] = r0  ; List_4
    //     0x130a458: add             x25, x1, #0x13
    //     0x130a45c: str             w0, [x25]
    //     0x130a460: tbz             w0, #0, #0x130a47c
    //     0x130a464: ldurb           w16, [x1, #-1]
    //     0x130a468: ldurb           w17, [x0, #-1]
    //     0x130a46c: and             x16, x17, x16, lsr #2
    //     0x130a470: tst             x16, HEAP, lsr #32
    //     0x130a474: b.eq            #0x130a47c
    //     0x130a478: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a47c: ldur            x16, [fp, #-0x28]
    // 0x130a480: str             x16, [SP]
    // 0x130a484: r0 = _interpolate()
    //     0x130a484: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130a488: ldur            x1, [fp, #-0x18]
    // 0x130a48c: ArrayStore: r1[15] = r0  ; List_4
    //     0x130a48c: add             x25, x1, #0x4b
    //     0x130a490: str             w0, [x25]
    //     0x130a494: tbz             w0, #0, #0x130a4b0
    //     0x130a498: ldurb           w16, [x1, #-1]
    //     0x130a49c: ldurb           w17, [x0, #-1]
    //     0x130a4a0: and             x16, x17, x16, lsr #2
    //     0x130a4a4: tst             x16, HEAP, lsr #32
    //     0x130a4a8: b.eq            #0x130a4b0
    //     0x130a4ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a4b0: ldur            x0, [fp, #-0x18]
    // 0x130a4b4: r16 = "is_skipped_address"
    //     0x130a4b4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130a4b8: ldr             x16, [x16, #0xb80]
    // 0x130a4bc: StoreField: r0->field_4f = r16
    //     0x130a4bc: stur            w16, [x0, #0x4f]
    // 0x130a4c0: r16 = true
    //     0x130a4c0: add             x16, NULL, #0x20  ; true
    // 0x130a4c4: StoreField: r0->field_53 = r16
    //     0x130a4c4: stur            w16, [x0, #0x53]
    // 0x130a4c8: r16 = "checkout_id"
    //     0x130a4c8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130a4cc: ldr             x16, [x16, #0xb88]
    // 0x130a4d0: StoreField: r0->field_57 = r16
    //     0x130a4d0: stur            w16, [x0, #0x57]
    // 0x130a4d4: r16 = ""
    //     0x130a4d4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130a4d8: StoreField: r0->field_5b = r16
    //     0x130a4d8: stur            w16, [x0, #0x5b]
    // 0x130a4dc: r16 = "user_data"
    //     0x130a4dc: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x130a4e0: ldr             x16, [x16, #0x58]
    // 0x130a4e4: StoreField: r0->field_5f = r16
    //     0x130a4e4: stur            w16, [x0, #0x5f]
    // 0x130a4e8: ldur            x2, [fp, #-0x10]
    // 0x130a4ec: LoadField: r1 = r2->field_f
    //     0x130a4ec: ldur            w1, [x2, #0xf]
    // 0x130a4f0: DecompressPointer r1
    //     0x130a4f0: add             x1, x1, HEAP, lsl #32
    // 0x130a4f4: r0 = controller()
    //     0x130a4f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a4f8: LoadField: r1 = r0->field_5f
    //     0x130a4f8: ldur            w1, [x0, #0x5f]
    // 0x130a4fc: DecompressPointer r1
    //     0x130a4fc: add             x1, x1, HEAP, lsl #32
    // 0x130a500: r0 = value()
    //     0x130a500: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a504: ldur            x1, [fp, #-0x18]
    // 0x130a508: ArrayStore: r1[21] = r0  ; List_4
    //     0x130a508: add             x25, x1, #0x63
    //     0x130a50c: str             w0, [x25]
    //     0x130a510: tbz             w0, #0, #0x130a52c
    //     0x130a514: ldurb           w16, [x1, #-1]
    //     0x130a518: ldurb           w17, [x0, #-1]
    //     0x130a51c: and             x16, x17, x16, lsr #2
    //     0x130a520: tst             x16, HEAP, lsr #32
    //     0x130a524: b.eq            #0x130a52c
    //     0x130a528: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a52c: r16 = <String, dynamic>
    //     0x130a52c: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130a530: ldur            lr, [fp, #-0x18]
    // 0x130a534: stp             lr, x16, [SP]
    // 0x130a538: r0 = Map._fromLiteral()
    //     0x130a538: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130a53c: r16 = "/checkout_order_summary_page"
    //     0x130a53c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0x130a540: ldr             x16, [x16, #0x9d8]
    // 0x130a544: stp             x16, NULL, [SP, #8]
    // 0x130a548: str             x0, [SP]
    // 0x130a54c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130a54c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130a550: ldr             x4, [x4, #0x438]
    // 0x130a554: r0 = GetNavigation.toNamed()
    //     0x130a554: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130a558: b               #0x130a810
    // 0x130a55c: ldur            x2, [fp, #-0x10]
    // 0x130a560: LoadField: r1 = r2->field_f
    //     0x130a560: ldur            w1, [x2, #0xf]
    // 0x130a564: DecompressPointer r1
    //     0x130a564: add             x1, x1, HEAP, lsl #32
    // 0x130a568: r0 = controller()
    //     0x130a568: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a56c: LoadField: r1 = r0->field_8f
    //     0x130a56c: ldur            w1, [x0, #0x8f]
    // 0x130a570: DecompressPointer r1
    //     0x130a570: add             x1, x1, HEAP, lsl #32
    // 0x130a574: r0 = value()
    //     0x130a574: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a578: mov             x2, x0
    // 0x130a57c: ldur            x0, [fp, #-0x10]
    // 0x130a580: stur            x2, [fp, #-0x18]
    // 0x130a584: LoadField: r1 = r0->field_f
    //     0x130a584: ldur            w1, [x0, #0xf]
    // 0x130a588: DecompressPointer r1
    //     0x130a588: add             x1, x1, HEAP, lsl #32
    // 0x130a58c: r0 = controller()
    //     0x130a58c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a590: LoadField: r1 = r0->field_93
    //     0x130a590: ldur            w1, [x0, #0x93]
    // 0x130a594: DecompressPointer r1
    //     0x130a594: add             x1, x1, HEAP, lsl #32
    // 0x130a598: r0 = value()
    //     0x130a598: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a59c: mov             x2, x0
    // 0x130a5a0: ldur            x0, [fp, #-0x10]
    // 0x130a5a4: stur            x2, [fp, #-0x28]
    // 0x130a5a8: LoadField: r1 = r0->field_f
    //     0x130a5a8: ldur            w1, [x0, #0xf]
    // 0x130a5ac: DecompressPointer r1
    //     0x130a5ac: add             x1, x1, HEAP, lsl #32
    // 0x130a5b0: r0 = controller()
    //     0x130a5b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a5b4: LoadField: r1 = r0->field_ab
    //     0x130a5b4: ldur            w1, [x0, #0xab]
    // 0x130a5b8: DecompressPointer r1
    //     0x130a5b8: add             x1, x1, HEAP, lsl #32
    // 0x130a5bc: r0 = value()
    //     0x130a5bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a5c0: mov             x2, x0
    // 0x130a5c4: ldur            x0, [fp, #-0x10]
    // 0x130a5c8: stur            x2, [fp, #-0x30]
    // 0x130a5cc: LoadField: r1 = r0->field_f
    //     0x130a5cc: ldur            w1, [x0, #0xf]
    // 0x130a5d0: DecompressPointer r1
    //     0x130a5d0: add             x1, x1, HEAP, lsl #32
    // 0x130a5d4: r0 = controller()
    //     0x130a5d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a5d8: LoadField: r2 = r0->field_9b
    //     0x130a5d8: ldur            w2, [x0, #0x9b]
    // 0x130a5dc: DecompressPointer r2
    //     0x130a5dc: add             x2, x2, HEAP, lsl #32
    // 0x130a5e0: ldur            x0, [fp, #-0x10]
    // 0x130a5e4: stur            x2, [fp, #-0x38]
    // 0x130a5e8: LoadField: r1 = r0->field_f
    //     0x130a5e8: ldur            w1, [x0, #0xf]
    // 0x130a5ec: DecompressPointer r1
    //     0x130a5ec: add             x1, x1, HEAP, lsl #32
    // 0x130a5f0: r0 = controller()
    //     0x130a5f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a5f4: LoadField: r1 = r0->field_97
    //     0x130a5f4: ldur            w1, [x0, #0x97]
    // 0x130a5f8: DecompressPointer r1
    //     0x130a5f8: add             x1, x1, HEAP, lsl #32
    // 0x130a5fc: r0 = value()
    //     0x130a5fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a600: stur            x0, [fp, #-0x40]
    // 0x130a604: r0 = CustomizedRequest()
    //     0x130a604: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x130a608: mov             x1, x0
    // 0x130a60c: ldur            x0, [fp, #-0x18]
    // 0x130a610: stur            x1, [fp, #-0x48]
    // 0x130a614: StoreField: r1->field_7 = r0
    //     0x130a614: stur            w0, [x1, #7]
    // 0x130a618: ldur            x0, [fp, #-0x28]
    // 0x130a61c: StoreField: r1->field_b = r0
    //     0x130a61c: stur            w0, [x1, #0xb]
    // 0x130a620: ldur            x0, [fp, #-0x30]
    // 0x130a624: StoreField: r1->field_f = r0
    //     0x130a624: stur            w0, [x1, #0xf]
    // 0x130a628: ldur            x0, [fp, #-0x38]
    // 0x130a62c: StoreField: r1->field_13 = r0
    //     0x130a62c: stur            w0, [x1, #0x13]
    // 0x130a630: ldur            x0, [fp, #-0x40]
    // 0x130a634: ArrayStore: r1[0] = r0  ; List_4
    //     0x130a634: stur            w0, [x1, #0x17]
    // 0x130a638: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130a638: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130a63c: ldr             x0, [x0, #0x1c80]
    //     0x130a640: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130a644: cmp             w0, w16
    //     0x130a648: b.ne            #0x130a654
    //     0x130a64c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130a650: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130a654: r1 = Null
    //     0x130a654: mov             x1, NULL
    // 0x130a658: r2 = 28
    //     0x130a658: movz            x2, #0x1c
    // 0x130a65c: r0 = AllocateArray()
    //     0x130a65c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130a660: stur            x0, [fp, #-0x18]
    // 0x130a664: r16 = "previousScreenSource"
    //     0x130a664: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130a668: ldr             x16, [x16, #0x448]
    // 0x130a66c: StoreField: r0->field_f = r16
    //     0x130a66c: stur            w16, [x0, #0xf]
    // 0x130a670: r16 = "product_page"
    //     0x130a670: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130a674: ldr             x16, [x16, #0x480]
    // 0x130a678: StoreField: r0->field_13 = r16
    //     0x130a678: stur            w16, [x0, #0x13]
    // 0x130a67c: r16 = "product_id"
    //     0x130a67c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130a680: ldr             x16, [x16, #0x9b8]
    // 0x130a684: ArrayStore: r0[0] = r16  ; List_4
    //     0x130a684: stur            w16, [x0, #0x17]
    // 0x130a688: ldur            x2, [fp, #-0x10]
    // 0x130a68c: LoadField: r1 = r2->field_f
    //     0x130a68c: ldur            w1, [x2, #0xf]
    // 0x130a690: DecompressPointer r1
    //     0x130a690: add             x1, x1, HEAP, lsl #32
    // 0x130a694: r0 = controller()
    //     0x130a694: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a698: LoadField: r1 = r0->field_8f
    //     0x130a698: ldur            w1, [x0, #0x8f]
    // 0x130a69c: DecompressPointer r1
    //     0x130a69c: add             x1, x1, HEAP, lsl #32
    // 0x130a6a0: r0 = value()
    //     0x130a6a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a6a4: ldur            x1, [fp, #-0x18]
    // 0x130a6a8: ArrayStore: r1[3] = r0  ; List_4
    //     0x130a6a8: add             x25, x1, #0x1b
    //     0x130a6ac: str             w0, [x25]
    //     0x130a6b0: tbz             w0, #0, #0x130a6cc
    //     0x130a6b4: ldurb           w16, [x1, #-1]
    //     0x130a6b8: ldurb           w17, [x0, #-1]
    //     0x130a6bc: and             x16, x17, x16, lsr #2
    //     0x130a6c0: tst             x16, HEAP, lsr #32
    //     0x130a6c4: b.eq            #0x130a6cc
    //     0x130a6c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a6cc: ldur            x0, [fp, #-0x18]
    // 0x130a6d0: r16 = "sku_id"
    //     0x130a6d0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130a6d4: ldr             x16, [x16, #0x498]
    // 0x130a6d8: StoreField: r0->field_1f = r16
    //     0x130a6d8: stur            w16, [x0, #0x1f]
    // 0x130a6dc: ldur            x2, [fp, #-0x10]
    // 0x130a6e0: LoadField: r1 = r2->field_f
    //     0x130a6e0: ldur            w1, [x2, #0xf]
    // 0x130a6e4: DecompressPointer r1
    //     0x130a6e4: add             x1, x1, HEAP, lsl #32
    // 0x130a6e8: r0 = controller()
    //     0x130a6e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a6ec: LoadField: r1 = r0->field_93
    //     0x130a6ec: ldur            w1, [x0, #0x93]
    // 0x130a6f0: DecompressPointer r1
    //     0x130a6f0: add             x1, x1, HEAP, lsl #32
    // 0x130a6f4: r0 = value()
    //     0x130a6f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a6f8: ldur            x1, [fp, #-0x18]
    // 0x130a6fc: ArrayStore: r1[5] = r0  ; List_4
    //     0x130a6fc: add             x25, x1, #0x23
    //     0x130a700: str             w0, [x25]
    //     0x130a704: tbz             w0, #0, #0x130a720
    //     0x130a708: ldurb           w16, [x1, #-1]
    //     0x130a70c: ldurb           w17, [x0, #-1]
    //     0x130a710: and             x16, x17, x16, lsr #2
    //     0x130a714: tst             x16, HEAP, lsr #32
    //     0x130a718: b.eq            #0x130a720
    //     0x130a71c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a720: ldur            x0, [fp, #-0x18]
    // 0x130a724: r16 = "quantity"
    //     0x130a724: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130a728: ldr             x16, [x16, #0x428]
    // 0x130a72c: StoreField: r0->field_27 = r16
    //     0x130a72c: stur            w16, [x0, #0x27]
    // 0x130a730: ldur            x2, [fp, #-0x10]
    // 0x130a734: LoadField: r1 = r2->field_f
    //     0x130a734: ldur            w1, [x2, #0xf]
    // 0x130a738: DecompressPointer r1
    //     0x130a738: add             x1, x1, HEAP, lsl #32
    // 0x130a73c: r0 = controller()
    //     0x130a73c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a740: LoadField: r1 = r0->field_ab
    //     0x130a740: ldur            w1, [x0, #0xab]
    // 0x130a744: DecompressPointer r1
    //     0x130a744: add             x1, x1, HEAP, lsl #32
    // 0x130a748: r0 = value()
    //     0x130a748: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a74c: ldur            x1, [fp, #-0x18]
    // 0x130a750: ArrayStore: r1[7] = r0  ; List_4
    //     0x130a750: add             x25, x1, #0x2b
    //     0x130a754: str             w0, [x25]
    //     0x130a758: tbz             w0, #0, #0x130a774
    //     0x130a75c: ldurb           w16, [x1, #-1]
    //     0x130a760: ldurb           w17, [x0, #-1]
    //     0x130a764: and             x16, x17, x16, lsr #2
    //     0x130a768: tst             x16, HEAP, lsr #32
    //     0x130a76c: b.eq            #0x130a774
    //     0x130a770: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a774: ldur            x2, [fp, #-0x18]
    // 0x130a778: r16 = "customization_request"
    //     0x130a778: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130a77c: ldr             x16, [x16, #0x2b8]
    // 0x130a780: StoreField: r2->field_2f = r16
    //     0x130a780: stur            w16, [x2, #0x2f]
    // 0x130a784: mov             x1, x2
    // 0x130a788: ldur            x0, [fp, #-0x48]
    // 0x130a78c: ArrayStore: r1[9] = r0  ; List_4
    //     0x130a78c: add             x25, x1, #0x33
    //     0x130a790: str             w0, [x25]
    //     0x130a794: tbz             w0, #0, #0x130a7b0
    //     0x130a798: ldurb           w16, [x1, #-1]
    //     0x130a79c: ldurb           w17, [x0, #-1]
    //     0x130a7a0: and             x16, x17, x16, lsr #2
    //     0x130a7a4: tst             x16, HEAP, lsr #32
    //     0x130a7a8: b.eq            #0x130a7b0
    //     0x130a7ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a7b0: r16 = "coming_from"
    //     0x130a7b0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130a7b4: ldr             x16, [x16, #0x328]
    // 0x130a7b8: StoreField: r2->field_37 = r16
    //     0x130a7b8: stur            w16, [x2, #0x37]
    // 0x130a7bc: r16 = "buyNow"
    //     0x130a7bc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130a7c0: ldr             x16, [x16, #0x358]
    // 0x130a7c4: StoreField: r2->field_3b = r16
    //     0x130a7c4: stur            w16, [x2, #0x3b]
    // 0x130a7c8: r16 = "is_skipped_address"
    //     0x130a7c8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130a7cc: ldr             x16, [x16, #0xb80]
    // 0x130a7d0: StoreField: r2->field_3f = r16
    //     0x130a7d0: stur            w16, [x2, #0x3f]
    // 0x130a7d4: r16 = true
    //     0x130a7d4: add             x16, NULL, #0x20  ; true
    // 0x130a7d8: StoreField: r2->field_43 = r16
    //     0x130a7d8: stur            w16, [x2, #0x43]
    // 0x130a7dc: r16 = <String, dynamic>
    //     0x130a7dc: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130a7e0: stp             x2, x16, [SP]
    // 0x130a7e4: r0 = Map._fromLiteral()
    //     0x130a7e4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130a7e8: r16 = "/checkout_request_number_page"
    //     0x130a7e8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0x130a7ec: ldr             x16, [x16, #0x9f8]
    // 0x130a7f0: stp             x16, NULL, [SP, #8]
    // 0x130a7f4: str             x0, [SP]
    // 0x130a7f8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130a7f8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130a7fc: ldr             x4, [x4, #0x438]
    // 0x130a800: r0 = GetNavigation.toNamed()
    //     0x130a800: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130a804: mov             x1, x0
    // 0x130a808: stur            x1, [fp, #-0x18]
    // 0x130a80c: r0 = Await()
    //     0x130a80c: bl              #0x63248c  ; AwaitStub
    // 0x130a810: r0 = Null
    //     0x130a810: mov             x0, NULL
    // 0x130a814: r0 = ReturnAsyncNotFuture()
    //     0x130a814: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x130a818: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130a818: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130a81c: b               #0x1309be8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x130a820, size: 0x3e8
    // 0x130a820: EnterFrame
    //     0x130a820: stp             fp, lr, [SP, #-0x10]!
    //     0x130a824: mov             fp, SP
    // 0x130a828: AllocStack(0x50)
    //     0x130a828: sub             SP, SP, #0x50
    // 0x130a82c: SetupParameters()
    //     0x130a82c: ldr             x0, [fp, #0x10]
    //     0x130a830: ldur            w2, [x0, #0x17]
    //     0x130a834: add             x2, x2, HEAP, lsl #32
    //     0x130a838: stur            x2, [fp, #-8]
    // 0x130a83c: CheckStackOverflow
    //     0x130a83c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130a840: cmp             SP, x16
    //     0x130a844: b.ls            #0x130ac00
    // 0x130a848: LoadField: r1 = r2->field_f
    //     0x130a848: ldur            w1, [x2, #0xf]
    // 0x130a84c: DecompressPointer r1
    //     0x130a84c: add             x1, x1, HEAP, lsl #32
    // 0x130a850: r0 = controller()
    //     0x130a850: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a854: mov             x1, x0
    // 0x130a858: r0 = appConfigResponse()
    //     0x130a858: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0x130a85c: LoadField: r1 = r0->field_67
    //     0x130a85c: ldur            w1, [x0, #0x67]
    // 0x130a860: DecompressPointer r1
    //     0x130a860: add             x1, x1, HEAP, lsl #32
    // 0x130a864: cmp             w1, NULL
    // 0x130a868: b.ne            #0x130a874
    // 0x130a86c: r0 = Null
    //     0x130a86c: mov             x0, NULL
    // 0x130a870: b               #0x130a8ac
    // 0x130a874: LoadField: r0 = r1->field_7
    //     0x130a874: ldur            w0, [x1, #7]
    // 0x130a878: DecompressPointer r0
    //     0x130a878: add             x0, x0, HEAP, lsl #32
    // 0x130a87c: cmp             w0, NULL
    // 0x130a880: b.ne            #0x130a88c
    // 0x130a884: r0 = Null
    //     0x130a884: mov             x0, NULL
    // 0x130a888: b               #0x130a8ac
    // 0x130a88c: LoadField: r1 = r0->field_b
    //     0x130a88c: ldur            w1, [x0, #0xb]
    // 0x130a890: DecompressPointer r1
    //     0x130a890: add             x1, x1, HEAP, lsl #32
    // 0x130a894: cmp             w1, NULL
    // 0x130a898: b.ne            #0x130a8a4
    // 0x130a89c: r0 = Null
    //     0x130a89c: mov             x0, NULL
    // 0x130a8a0: b               #0x130a8ac
    // 0x130a8a4: LoadField: r0 = r1->field_7
    //     0x130a8a4: ldur            w0, [x1, #7]
    // 0x130a8a8: DecompressPointer r0
    //     0x130a8a8: add             x0, x0, HEAP, lsl #32
    // 0x130a8ac: cmp             w0, NULL
    // 0x130a8b0: b.eq            #0x130aa8c
    // 0x130a8b4: tbnz            w0, #4, #0x130aa8c
    // 0x130a8b8: ldur            x2, [fp, #-8]
    // 0x130a8bc: LoadField: r1 = r2->field_f
    //     0x130a8bc: ldur            w1, [x2, #0xf]
    // 0x130a8c0: DecompressPointer r1
    //     0x130a8c0: add             x1, x1, HEAP, lsl #32
    // 0x130a8c4: r0 = controller()
    //     0x130a8c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a8c8: LoadField: r2 = r0->field_4b
    //     0x130a8c8: ldur            w2, [x0, #0x4b]
    // 0x130a8cc: DecompressPointer r2
    //     0x130a8cc: add             x2, x2, HEAP, lsl #32
    // 0x130a8d0: ldur            x0, [fp, #-8]
    // 0x130a8d4: stur            x2, [fp, #-0x10]
    // 0x130a8d8: LoadField: r1 = r0->field_f
    //     0x130a8d8: ldur            w1, [x0, #0xf]
    // 0x130a8dc: DecompressPointer r1
    //     0x130a8dc: add             x1, x1, HEAP, lsl #32
    // 0x130a8e0: r0 = controller()
    //     0x130a8e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a8e4: mov             x1, x0
    // 0x130a8e8: r0 = cataloguePricingResponse()
    //     0x130a8e8: bl              #0x9c5f34  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::cataloguePricingResponse
    // 0x130a8ec: ldur            x2, [fp, #-8]
    // 0x130a8f0: stur            x0, [fp, #-0x18]
    // 0x130a8f4: LoadField: r1 = r2->field_f
    //     0x130a8f4: ldur            w1, [x2, #0xf]
    // 0x130a8f8: DecompressPointer r1
    //     0x130a8f8: add             x1, x1, HEAP, lsl #32
    // 0x130a8fc: r0 = controller()
    //     0x130a8fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a900: mov             x1, x0
    // 0x130a904: r0 = sellingPrice()
    //     0x130a904: bl              #0x13071d8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::sellingPrice
    // 0x130a908: mov             x2, x0
    // 0x130a90c: r0 = BoxInt64Instr(r2)
    //     0x130a90c: sbfiz           x0, x2, #1, #0x1f
    //     0x130a910: cmp             x2, x0, asr #1
    //     0x130a914: b.eq            #0x130a920
    //     0x130a918: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x130a91c: stur            x2, [x0, #7]
    // 0x130a920: stp             x0, NULL, [SP]
    // 0x130a924: r0 = _Double.fromInteger()
    //     0x130a924: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x130a928: r1 = Null
    //     0x130a928: mov             x1, NULL
    // 0x130a92c: r2 = 12
    //     0x130a92c: movz            x2, #0xc
    // 0x130a930: stur            x0, [fp, #-0x20]
    // 0x130a934: r0 = AllocateArray()
    //     0x130a934: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130a938: stur            x0, [fp, #-0x28]
    // 0x130a93c: r16 = "id"
    //     0x130a93c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0x130a940: ldr             x16, [x16, #0x400]
    // 0x130a944: StoreField: r0->field_f = r16
    //     0x130a944: stur            w16, [x0, #0xf]
    // 0x130a948: ldur            x2, [fp, #-8]
    // 0x130a94c: LoadField: r1 = r2->field_f
    //     0x130a94c: ldur            w1, [x2, #0xf]
    // 0x130a950: DecompressPointer r1
    //     0x130a950: add             x1, x1, HEAP, lsl #32
    // 0x130a954: r0 = controller()
    //     0x130a954: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a958: LoadField: r1 = r0->field_93
    //     0x130a958: ldur            w1, [x0, #0x93]
    // 0x130a95c: DecompressPointer r1
    //     0x130a95c: add             x1, x1, HEAP, lsl #32
    // 0x130a960: r0 = value()
    //     0x130a960: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130a964: ldur            x1, [fp, #-0x28]
    // 0x130a968: ArrayStore: r1[1] = r0  ; List_4
    //     0x130a968: add             x25, x1, #0x13
    //     0x130a96c: str             w0, [x25]
    //     0x130a970: tbz             w0, #0, #0x130a98c
    //     0x130a974: ldurb           w16, [x1, #-1]
    //     0x130a978: ldurb           w17, [x0, #-1]
    //     0x130a97c: and             x16, x17, x16, lsr #2
    //     0x130a980: tst             x16, HEAP, lsr #32
    //     0x130a984: b.eq            #0x130a98c
    //     0x130a988: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a98c: ldur            x0, [fp, #-0x28]
    // 0x130a990: r16 = "quantity"
    //     0x130a990: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130a994: ldr             x16, [x16, #0x428]
    // 0x130a998: ArrayStore: r0[0] = r16  ; List_4
    //     0x130a998: stur            w16, [x0, #0x17]
    // 0x130a99c: ldur            x2, [fp, #-8]
    // 0x130a9a0: LoadField: r1 = r2->field_f
    //     0x130a9a0: ldur            w1, [x2, #0xf]
    // 0x130a9a4: DecompressPointer r1
    //     0x130a9a4: add             x1, x1, HEAP, lsl #32
    // 0x130a9a8: r0 = controller()
    //     0x130a9a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130a9ac: mov             x1, x0
    // 0x130a9b0: r0 = addTypeValue()
    //     0x130a9b0: bl              #0x1307190  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::addTypeValue
    // 0x130a9b4: mov             x2, x0
    // 0x130a9b8: r0 = BoxInt64Instr(r2)
    //     0x130a9b8: sbfiz           x0, x2, #1, #0x1f
    //     0x130a9bc: cmp             x2, x0, asr #1
    //     0x130a9c0: b.eq            #0x130a9cc
    //     0x130a9c4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x130a9c8: stur            x2, [x0, #7]
    // 0x130a9cc: ldur            x1, [fp, #-0x28]
    // 0x130a9d0: ArrayStore: r1[3] = r0  ; List_4
    //     0x130a9d0: add             x25, x1, #0x1b
    //     0x130a9d4: str             w0, [x25]
    //     0x130a9d8: tbz             w0, #0, #0x130a9f4
    //     0x130a9dc: ldurb           w16, [x1, #-1]
    //     0x130a9e0: ldurb           w17, [x0, #-1]
    //     0x130a9e4: and             x16, x17, x16, lsr #2
    //     0x130a9e8: tst             x16, HEAP, lsr #32
    //     0x130a9ec: b.eq            #0x130a9f4
    //     0x130a9f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130a9f4: ldur            x0, [fp, #-0x28]
    // 0x130a9f8: r16 = "item_price"
    //     0x130a9f8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0x130a9fc: ldr             x16, [x16, #0x498]
    // 0x130aa00: StoreField: r0->field_1f = r16
    //     0x130aa00: stur            w16, [x0, #0x1f]
    // 0x130aa04: ldur            x2, [fp, #-8]
    // 0x130aa08: LoadField: r1 = r2->field_f
    //     0x130aa08: ldur            w1, [x2, #0xf]
    // 0x130aa0c: DecompressPointer r1
    //     0x130aa0c: add             x1, x1, HEAP, lsl #32
    // 0x130aa10: r0 = controller()
    //     0x130aa10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130aa14: LoadField: r1 = r0->field_af
    //     0x130aa14: ldur            w1, [x0, #0xaf]
    // 0x130aa18: DecompressPointer r1
    //     0x130aa18: add             x1, x1, HEAP, lsl #32
    // 0x130aa1c: r0 = value()
    //     0x130aa1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130aa20: ldur            x1, [fp, #-0x28]
    // 0x130aa24: ArrayStore: r1[5] = r0  ; List_4
    //     0x130aa24: add             x25, x1, #0x23
    //     0x130aa28: str             w0, [x25]
    //     0x130aa2c: tbz             w0, #0, #0x130aa48
    //     0x130aa30: ldurb           w16, [x1, #-1]
    //     0x130aa34: ldurb           w17, [x0, #-1]
    //     0x130aa38: and             x16, x17, x16, lsr #2
    //     0x130aa3c: tst             x16, HEAP, lsr #32
    //     0x130aa40: b.eq            #0x130aa48
    //     0x130aa44: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130aa48: r16 = <String, dynamic>
    //     0x130aa48: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130aa4c: ldur            lr, [fp, #-0x28]
    // 0x130aa50: stp             lr, x16, [SP]
    // 0x130aa54: r0 = Map._fromLiteral()
    //     0x130aa54: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130aa58: ldur            x16, [fp, #-0x18]
    // 0x130aa5c: r30 = "product"
    //     0x130aa5c: add             lr, PP, #0x12, lsl #12  ; [pp+0x12240] "product"
    //     0x130aa60: ldr             lr, [lr, #0x240]
    // 0x130aa64: stp             lr, x16, [SP, #0x18]
    // 0x130aa68: r16 = "INR"
    //     0x130aa68: add             x16, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0x130aa6c: ldr             x16, [x16, #0x4c0]
    // 0x130aa70: ldur            lr, [fp, #-0x20]
    // 0x130aa74: stp             lr, x16, [SP, #8]
    // 0x130aa78: str             x0, [SP]
    // 0x130aa7c: ldur            x1, [fp, #-0x10]
    // 0x130aa80: r4 = const [0, 0x6, 0x5, 0x1, content, 0x5, currency, 0x3, id, 0x1, price, 0x4, type, 0x2, null]
    //     0x130aa80: add             x4, PP, #0x32, lsl #12  ; [pp+0x32318] List(15) [0, 0x6, 0x5, 0x1, "content", 0x5, "currency", 0x3, "id", 0x1, "price", 0x4, "type", 0x2, Null]
    //     0x130aa84: ldr             x4, [x4, #0x318]
    // 0x130aa88: r0 = logAddToCart()
    //     0x130aa88: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0x130aa8c: ldur            x2, [fp, #-8]
    // 0x130aa90: LoadField: r1 = r2->field_f
    //     0x130aa90: ldur            w1, [x2, #0xf]
    // 0x130aa94: DecompressPointer r1
    //     0x130aa94: add             x1, x1, HEAP, lsl #32
    // 0x130aa98: r0 = controller()
    //     0x130aa98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130aa9c: LoadField: r1 = r0->field_bb
    //     0x130aa9c: ldur            w1, [x0, #0xbb]
    // 0x130aaa0: DecompressPointer r1
    //     0x130aaa0: add             x1, x1, HEAP, lsl #32
    // 0x130aaa4: r0 = LoadClassIdInstr(r1)
    //     0x130aaa4: ldur            x0, [x1, #-1]
    //     0x130aaa8: ubfx            x0, x0, #0xc, #0x14
    // 0x130aaac: r16 = "add_to_bag"
    //     0x130aaac: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0x130aab0: ldr             x16, [x16, #0xa38]
    // 0x130aab4: stp             x16, x1, [SP]
    // 0x130aab8: mov             lr, x0
    // 0x130aabc: ldr             lr, [x21, lr, lsl #3]
    // 0x130aac0: blr             lr
    // 0x130aac4: tbnz            w0, #4, #0x130aae4
    // 0x130aac8: ldur            x2, [fp, #-8]
    // 0x130aacc: LoadField: r1 = r2->field_f
    //     0x130aacc: ldur            w1, [x2, #0xf]
    // 0x130aad0: DecompressPointer r1
    //     0x130aad0: add             x1, x1, HEAP, lsl #32
    // 0x130aad4: r0 = controller()
    //     0x130aad4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130aad8: mov             x1, x0
    // 0x130aadc: r0 = addToBag()
    //     0x130aadc: bl              #0x1307220  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::addToBag
    // 0x130aae0: b               #0x130abf0
    // 0x130aae4: ldur            x2, [fp, #-8]
    // 0x130aae8: LoadField: r1 = r2->field_f
    //     0x130aae8: ldur            w1, [x2, #0xf]
    // 0x130aaec: DecompressPointer r1
    //     0x130aaec: add             x1, x1, HEAP, lsl #32
    // 0x130aaf0: r0 = controller()
    //     0x130aaf0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130aaf4: mov             x1, x0
    // 0x130aaf8: r0 = bumperCouponData()
    //     0x130aaf8: bl              #0x8a2a70  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::bumperCouponData
    // 0x130aafc: LoadField: r1 = r0->field_b
    //     0x130aafc: ldur            w1, [x0, #0xb]
    // 0x130ab00: DecompressPointer r1
    //     0x130ab00: add             x1, x1, HEAP, lsl #32
    // 0x130ab04: cmp             w1, NULL
    // 0x130ab08: b.ne            #0x130ab14
    // 0x130ab0c: ldur            x2, [fp, #-8]
    // 0x130ab10: b               #0x130ab70
    // 0x130ab14: LoadField: r0 = r1->field_7
    //     0x130ab14: ldur            w0, [x1, #7]
    // 0x130ab18: DecompressPointer r0
    //     0x130ab18: add             x0, x0, HEAP, lsl #32
    // 0x130ab1c: cmp             w0, NULL
    // 0x130ab20: b.eq            #0x130ab6c
    // 0x130ab24: ldur            x2, [fp, #-8]
    // 0x130ab28: LoadField: r0 = r2->field_13
    //     0x130ab28: ldur            w0, [x2, #0x13]
    // 0x130ab2c: DecompressPointer r0
    //     0x130ab2c: add             x0, x0, HEAP, lsl #32
    // 0x130ab30: stur            x0, [fp, #-0x10]
    // 0x130ab34: r1 = Function '<anonymous closure>':.
    //     0x130ab34: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b038] AnonymousClosure: (0x130990c), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x130ab38: ldr             x1, [x1, #0x38]
    // 0x130ab3c: r0 = AllocateClosure()
    //     0x130ab3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130ab40: stp             x0, NULL, [SP, #0x18]
    // 0x130ab44: ldur            x16, [fp, #-0x10]
    // 0x130ab48: r30 = true
    //     0x130ab48: add             lr, NULL, #0x20  ; true
    // 0x130ab4c: stp             lr, x16, [SP, #8]
    // 0x130ab50: r16 = Instance_RoundedRectangleBorder
    //     0x130ab50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x130ab54: ldr             x16, [x16, #0xd68]
    // 0x130ab58: str             x16, [SP]
    // 0x130ab5c: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x130ab5c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x130ab60: ldr             x4, [x4, #0xb20]
    // 0x130ab64: r0 = showModalBottomSheet()
    //     0x130ab64: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x130ab68: b               #0x130abf0
    // 0x130ab6c: ldur            x2, [fp, #-8]
    // 0x130ab70: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130ab70: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130ab74: ldr             x0, [x0, #0x1c80]
    //     0x130ab78: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130ab7c: cmp             w0, w16
    //     0x130ab80: b.ne            #0x130ab8c
    //     0x130ab84: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130ab88: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130ab8c: r16 = PreferenceManager
    //     0x130ab8c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x130ab90: ldr             x16, [x16, #0x878]
    // 0x130ab94: str             x16, [SP]
    // 0x130ab98: r0 = toString()
    //     0x130ab98: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x130ab9c: r16 = <PreferenceManager>
    //     0x130ab9c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x130aba0: ldr             x16, [x16, #0x880]
    // 0x130aba4: stp             x0, x16, [SP]
    // 0x130aba8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x130aba8: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x130abac: r0 = Inst.find()
    //     0x130abac: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x130abb0: mov             x1, x0
    // 0x130abb4: r2 = "token"
    //     0x130abb4: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x130abb8: ldr             x2, [x2, #0x958]
    // 0x130abbc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x130abbc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x130abc0: r0 = getString()
    //     0x130abc0: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x130abc4: ldur            x2, [fp, #-8]
    // 0x130abc8: r1 = Function '<anonymous closure>':.
    //     0x130abc8: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b040] AnonymousClosure: (0x130ac08), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x130abcc: ldr             x1, [x1, #0x40]
    // 0x130abd0: stur            x0, [fp, #-8]
    // 0x130abd4: r0 = AllocateClosure()
    //     0x130abd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130abd8: r16 = <Null?>
    //     0x130abd8: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x130abdc: ldur            lr, [fp, #-8]
    // 0x130abe0: stp             lr, x16, [SP, #8]
    // 0x130abe4: str             x0, [SP]
    // 0x130abe8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130abe8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130abec: r0 = then()
    //     0x130abec: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x130abf0: r0 = Null
    //     0x130abf0: mov             x0, NULL
    // 0x130abf4: LeaveFrame
    //     0x130abf4: mov             SP, fp
    //     0x130abf8: ldp             fp, lr, [SP], #0x10
    // 0x130abfc: ret
    //     0x130abfc: ret             
    // 0x130ac00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130ac00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130ac04: b               #0x130a848
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x130ac08, size: 0xd08
    // 0x130ac08: EnterFrame
    //     0x130ac08: stp             fp, lr, [SP, #-0x10]!
    //     0x130ac0c: mov             fp, SP
    // 0x130ac10: AllocStack(0x60)
    //     0x130ac10: sub             SP, SP, #0x60
    // 0x130ac14: SetupParameters(CustomizedPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x130ac14: stur            NULL, [fp, #-8]
    //     0x130ac18: movz            x0, #0
    //     0x130ac1c: add             x1, fp, w0, sxtw #2
    //     0x130ac20: ldr             x1, [x1, #0x18]
    //     0x130ac24: add             x2, fp, w0, sxtw #2
    //     0x130ac28: ldr             x2, [x2, #0x10]
    //     0x130ac2c: stur            x2, [fp, #-0x18]
    //     0x130ac30: ldur            w3, [x1, #0x17]
    //     0x130ac34: add             x3, x3, HEAP, lsl #32
    //     0x130ac38: stur            x3, [fp, #-0x10]
    // 0x130ac3c: CheckStackOverflow
    //     0x130ac3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130ac40: cmp             SP, x16
    //     0x130ac44: b.ls            #0x130b908
    // 0x130ac48: InitAsync() -> Future<Null?>
    //     0x130ac48: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x130ac4c: bl              #0x6326e0  ; InitAsyncStub
    // 0x130ac50: ldur            x0, [fp, #-0x18]
    // 0x130ac54: r1 = LoadClassIdInstr(r0)
    //     0x130ac54: ldur            x1, [x0, #-1]
    //     0x130ac58: ubfx            x1, x1, #0xc, #0x14
    // 0x130ac5c: r16 = ""
    //     0x130ac5c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130ac60: stp             x16, x0, [SP]
    // 0x130ac64: mov             x0, x1
    // 0x130ac68: mov             lr, x0
    // 0x130ac6c: ldr             lr, [x21, lr, lsl #3]
    // 0x130ac70: blr             lr
    // 0x130ac74: tbz             w0, #4, #0x130b580
    // 0x130ac78: ldur            x0, [fp, #-0x10]
    // 0x130ac7c: LoadField: r1 = r0->field_f
    //     0x130ac7c: ldur            w1, [x0, #0xf]
    // 0x130ac80: DecompressPointer r1
    //     0x130ac80: add             x1, x1, HEAP, lsl #32
    // 0x130ac84: r0 = controller()
    //     0x130ac84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ac88: LoadField: r1 = r0->field_63
    //     0x130ac88: ldur            w1, [x0, #0x63]
    // 0x130ac8c: DecompressPointer r1
    //     0x130ac8c: add             x1, x1, HEAP, lsl #32
    // 0x130ac90: cmp             w1, NULL
    // 0x130ac94: b.ne            #0x130aca0
    // 0x130ac98: r0 = Null
    //     0x130ac98: mov             x0, NULL
    // 0x130ac9c: b               #0x130accc
    // 0x130aca0: LoadField: r0 = r1->field_23
    //     0x130aca0: ldur            w0, [x1, #0x23]
    // 0x130aca4: DecompressPointer r0
    //     0x130aca4: add             x0, x0, HEAP, lsl #32
    // 0x130aca8: cmp             w0, NULL
    // 0x130acac: b.ne            #0x130acb8
    // 0x130acb0: r0 = Null
    //     0x130acb0: mov             x0, NULL
    // 0x130acb4: b               #0x130accc
    // 0x130acb8: LoadField: r1 = r0->field_b
    //     0x130acb8: ldur            w1, [x0, #0xb]
    // 0x130acbc: cbnz            w1, #0x130acc8
    // 0x130acc0: r0 = false
    //     0x130acc0: add             x0, NULL, #0x30  ; false
    // 0x130acc4: b               #0x130accc
    // 0x130acc8: r0 = true
    //     0x130acc8: add             x0, NULL, #0x20  ; true
    // 0x130accc: cmp             w0, NULL
    // 0x130acd0: b.eq            #0x130ada0
    // 0x130acd4: tbnz            w0, #4, #0x130ada0
    // 0x130acd8: ldur            x0, [fp, #-0x10]
    // 0x130acdc: LoadField: r1 = r0->field_f
    //     0x130acdc: ldur            w1, [x0, #0xf]
    // 0x130ace0: DecompressPointer r1
    //     0x130ace0: add             x1, x1, HEAP, lsl #32
    // 0x130ace4: r0 = controller()
    //     0x130ace4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ace8: LoadField: r2 = r0->field_9b
    //     0x130ace8: ldur            w2, [x0, #0x9b]
    // 0x130acec: DecompressPointer r2
    //     0x130acec: add             x2, x2, HEAP, lsl #32
    // 0x130acf0: ldur            x0, [fp, #-0x10]
    // 0x130acf4: stur            x2, [fp, #-0x18]
    // 0x130acf8: LoadField: r1 = r0->field_f
    //     0x130acf8: ldur            w1, [x0, #0xf]
    // 0x130acfc: DecompressPointer r1
    //     0x130acfc: add             x1, x1, HEAP, lsl #32
    // 0x130ad00: r0 = controller()
    //     0x130ad00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ad04: LoadField: r1 = r0->field_63
    //     0x130ad04: ldur            w1, [x0, #0x63]
    // 0x130ad08: DecompressPointer r1
    //     0x130ad08: add             x1, x1, HEAP, lsl #32
    // 0x130ad0c: cmp             w1, NULL
    // 0x130ad10: b.ne            #0x130ad20
    // 0x130ad14: r0 = ProductCustomisation()
    //     0x130ad14: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0x130ad18: mov             x2, x0
    // 0x130ad1c: b               #0x130ad24
    // 0x130ad20: mov             x2, x1
    // 0x130ad24: ldur            x0, [fp, #-0x18]
    // 0x130ad28: stur            x2, [fp, #-0x28]
    // 0x130ad2c: LoadField: r1 = r0->field_b
    //     0x130ad2c: ldur            w1, [x0, #0xb]
    // 0x130ad30: LoadField: r3 = r0->field_f
    //     0x130ad30: ldur            w3, [x0, #0xf]
    // 0x130ad34: DecompressPointer r3
    //     0x130ad34: add             x3, x3, HEAP, lsl #32
    // 0x130ad38: LoadField: r4 = r3->field_b
    //     0x130ad38: ldur            w4, [x3, #0xb]
    // 0x130ad3c: r3 = LoadInt32Instr(r1)
    //     0x130ad3c: sbfx            x3, x1, #1, #0x1f
    // 0x130ad40: stur            x3, [fp, #-0x20]
    // 0x130ad44: r1 = LoadInt32Instr(r4)
    //     0x130ad44: sbfx            x1, x4, #1, #0x1f
    // 0x130ad48: cmp             x3, x1
    // 0x130ad4c: b.ne            #0x130ad58
    // 0x130ad50: mov             x1, x0
    // 0x130ad54: r0 = _growToNextCapacity()
    //     0x130ad54: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x130ad58: ldur            x0, [fp, #-0x18]
    // 0x130ad5c: ldur            x2, [fp, #-0x20]
    // 0x130ad60: add             x1, x2, #1
    // 0x130ad64: lsl             x3, x1, #1
    // 0x130ad68: StoreField: r0->field_b = r3
    //     0x130ad68: stur            w3, [x0, #0xb]
    // 0x130ad6c: LoadField: r1 = r0->field_f
    //     0x130ad6c: ldur            w1, [x0, #0xf]
    // 0x130ad70: DecompressPointer r1
    //     0x130ad70: add             x1, x1, HEAP, lsl #32
    // 0x130ad74: ldur            x0, [fp, #-0x28]
    // 0x130ad78: ArrayStore: r1[r2] = r0  ; List_4
    //     0x130ad78: add             x25, x1, x2, lsl #2
    //     0x130ad7c: add             x25, x25, #0xf
    //     0x130ad80: str             w0, [x25]
    //     0x130ad84: tbz             w0, #0, #0x130ada0
    //     0x130ad88: ldurb           w16, [x1, #-1]
    //     0x130ad8c: ldurb           w17, [x0, #-1]
    //     0x130ad90: and             x16, x17, x16, lsr #2
    //     0x130ad94: tst             x16, HEAP, lsr #32
    //     0x130ad98: b.eq            #0x130ada0
    //     0x130ad9c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130ada0: ldur            x0, [fp, #-0x10]
    // 0x130ada4: LoadField: r1 = r0->field_f
    //     0x130ada4: ldur            w1, [x0, #0xf]
    // 0x130ada8: DecompressPointer r1
    //     0x130ada8: add             x1, x1, HEAP, lsl #32
    // 0x130adac: r0 = controller()
    //     0x130adac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130adb0: LoadField: r1 = r0->field_8f
    //     0x130adb0: ldur            w1, [x0, #0x8f]
    // 0x130adb4: DecompressPointer r1
    //     0x130adb4: add             x1, x1, HEAP, lsl #32
    // 0x130adb8: r0 = value()
    //     0x130adb8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130adbc: mov             x2, x0
    // 0x130adc0: ldur            x0, [fp, #-0x10]
    // 0x130adc4: stur            x2, [fp, #-0x18]
    // 0x130adc8: LoadField: r1 = r0->field_f
    //     0x130adc8: ldur            w1, [x0, #0xf]
    // 0x130adcc: DecompressPointer r1
    //     0x130adcc: add             x1, x1, HEAP, lsl #32
    // 0x130add0: r0 = controller()
    //     0x130add0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130add4: LoadField: r1 = r0->field_93
    //     0x130add4: ldur            w1, [x0, #0x93]
    // 0x130add8: DecompressPointer r1
    //     0x130add8: add             x1, x1, HEAP, lsl #32
    // 0x130addc: r0 = value()
    //     0x130addc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ade0: mov             x2, x0
    // 0x130ade4: ldur            x0, [fp, #-0x10]
    // 0x130ade8: stur            x2, [fp, #-0x28]
    // 0x130adec: LoadField: r1 = r0->field_f
    //     0x130adec: ldur            w1, [x0, #0xf]
    // 0x130adf0: DecompressPointer r1
    //     0x130adf0: add             x1, x1, HEAP, lsl #32
    // 0x130adf4: r0 = controller()
    //     0x130adf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130adf8: LoadField: r1 = r0->field_ab
    //     0x130adf8: ldur            w1, [x0, #0xab]
    // 0x130adfc: DecompressPointer r1
    //     0x130adfc: add             x1, x1, HEAP, lsl #32
    // 0x130ae00: r0 = value()
    //     0x130ae00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ae04: mov             x2, x0
    // 0x130ae08: ldur            x0, [fp, #-0x10]
    // 0x130ae0c: stur            x2, [fp, #-0x30]
    // 0x130ae10: LoadField: r1 = r0->field_f
    //     0x130ae10: ldur            w1, [x0, #0xf]
    // 0x130ae14: DecompressPointer r1
    //     0x130ae14: add             x1, x1, HEAP, lsl #32
    // 0x130ae18: r0 = controller()
    //     0x130ae18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ae1c: LoadField: r2 = r0->field_9b
    //     0x130ae1c: ldur            w2, [x0, #0x9b]
    // 0x130ae20: DecompressPointer r2
    //     0x130ae20: add             x2, x2, HEAP, lsl #32
    // 0x130ae24: ldur            x0, [fp, #-0x10]
    // 0x130ae28: stur            x2, [fp, #-0x38]
    // 0x130ae2c: LoadField: r1 = r0->field_f
    //     0x130ae2c: ldur            w1, [x0, #0xf]
    // 0x130ae30: DecompressPointer r1
    //     0x130ae30: add             x1, x1, HEAP, lsl #32
    // 0x130ae34: r0 = controller()
    //     0x130ae34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ae38: LoadField: r1 = r0->field_97
    //     0x130ae38: ldur            w1, [x0, #0x97]
    // 0x130ae3c: DecompressPointer r1
    //     0x130ae3c: add             x1, x1, HEAP, lsl #32
    // 0x130ae40: r0 = value()
    //     0x130ae40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ae44: stur            x0, [fp, #-0x40]
    // 0x130ae48: r0 = CustomizedRequest()
    //     0x130ae48: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x130ae4c: mov             x2, x0
    // 0x130ae50: ldur            x0, [fp, #-0x18]
    // 0x130ae54: stur            x2, [fp, #-0x48]
    // 0x130ae58: StoreField: r2->field_7 = r0
    //     0x130ae58: stur            w0, [x2, #7]
    // 0x130ae5c: ldur            x0, [fp, #-0x28]
    // 0x130ae60: StoreField: r2->field_b = r0
    //     0x130ae60: stur            w0, [x2, #0xb]
    // 0x130ae64: ldur            x0, [fp, #-0x30]
    // 0x130ae68: StoreField: r2->field_f = r0
    //     0x130ae68: stur            w0, [x2, #0xf]
    // 0x130ae6c: ldur            x0, [fp, #-0x38]
    // 0x130ae70: StoreField: r2->field_13 = r0
    //     0x130ae70: stur            w0, [x2, #0x13]
    // 0x130ae74: ldur            x0, [fp, #-0x40]
    // 0x130ae78: ArrayStore: r2[0] = r0  ; List_4
    //     0x130ae78: stur            w0, [x2, #0x17]
    // 0x130ae7c: ldur            x0, [fp, #-0x10]
    // 0x130ae80: LoadField: r1 = r0->field_f
    //     0x130ae80: ldur            w1, [x0, #0xf]
    // 0x130ae84: DecompressPointer r1
    //     0x130ae84: add             x1, x1, HEAP, lsl #32
    // 0x130ae88: r0 = controller()
    //     0x130ae88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ae8c: LoadField: r1 = r0->field_5f
    //     0x130ae8c: ldur            w1, [x0, #0x5f]
    // 0x130ae90: DecompressPointer r1
    //     0x130ae90: add             x1, x1, HEAP, lsl #32
    // 0x130ae94: r0 = value()
    //     0x130ae94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ae98: LoadField: r1 = r0->field_1b
    //     0x130ae98: ldur            w1, [x0, #0x1b]
    // 0x130ae9c: DecompressPointer r1
    //     0x130ae9c: add             x1, x1, HEAP, lsl #32
    // 0x130aea0: cmp             w1, NULL
    // 0x130aea4: b.ne            #0x130aeb0
    // 0x130aea8: r0 = Null
    //     0x130aea8: mov             x0, NULL
    // 0x130aeac: b               #0x130aec8
    // 0x130aeb0: LoadField: r0 = r1->field_b
    //     0x130aeb0: ldur            w0, [x1, #0xb]
    // 0x130aeb4: cbz             w0, #0x130aec0
    // 0x130aeb8: r1 = false
    //     0x130aeb8: add             x1, NULL, #0x30  ; false
    // 0x130aebc: b               #0x130aec4
    // 0x130aec0: r1 = true
    //     0x130aec0: add             x1, NULL, #0x20  ; true
    // 0x130aec4: mov             x0, x1
    // 0x130aec8: cmp             w0, NULL
    // 0x130aecc: b.eq            #0x130aed4
    // 0x130aed0: tbz             w0, #4, #0x130af60
    // 0x130aed4: ldur            x0, [fp, #-0x10]
    // 0x130aed8: LoadField: r1 = r0->field_f
    //     0x130aed8: ldur            w1, [x0, #0xf]
    // 0x130aedc: DecompressPointer r1
    //     0x130aedc: add             x1, x1, HEAP, lsl #32
    // 0x130aee0: r0 = controller()
    //     0x130aee0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130aee4: LoadField: r1 = r0->field_5f
    //     0x130aee4: ldur            w1, [x0, #0x5f]
    // 0x130aee8: DecompressPointer r1
    //     0x130aee8: add             x1, x1, HEAP, lsl #32
    // 0x130aeec: r0 = value()
    //     0x130aeec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130aef0: LoadField: r1 = r0->field_1b
    //     0x130aef0: ldur            w1, [x0, #0x1b]
    // 0x130aef4: DecompressPointer r1
    //     0x130aef4: add             x1, x1, HEAP, lsl #32
    // 0x130aef8: cmp             w1, NULL
    // 0x130aefc: b.ne            #0x130af08
    // 0x130af00: r0 = Null
    //     0x130af00: mov             x0, NULL
    // 0x130af04: b               #0x130af4c
    // 0x130af08: r0 = first()
    //     0x130af08: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0x130af0c: cmp             w0, NULL
    // 0x130af10: b.ne            #0x130af1c
    // 0x130af14: r0 = Null
    //     0x130af14: mov             x0, NULL
    // 0x130af18: b               #0x130af4c
    // 0x130af1c: LoadField: r1 = r0->field_13
    //     0x130af1c: ldur            w1, [x0, #0x13]
    // 0x130af20: DecompressPointer r1
    //     0x130af20: add             x1, x1, HEAP, lsl #32
    // 0x130af24: cmp             w1, NULL
    // 0x130af28: b.ne            #0x130af34
    // 0x130af2c: r0 = Null
    //     0x130af2c: mov             x0, NULL
    // 0x130af30: b               #0x130af4c
    // 0x130af34: LoadField: r0 = r1->field_7
    //     0x130af34: ldur            w0, [x1, #7]
    // 0x130af38: cbz             w0, #0x130af44
    // 0x130af3c: r1 = false
    //     0x130af3c: add             x1, NULL, #0x30  ; false
    // 0x130af40: b               #0x130af48
    // 0x130af44: r1 = true
    //     0x130af44: add             x1, NULL, #0x20  ; true
    // 0x130af48: mov             x0, x1
    // 0x130af4c: cmp             w0, NULL
    // 0x130af50: b.ne            #0x130af5c
    // 0x130af54: ldur            x1, [fp, #-0x10]
    // 0x130af58: b               #0x130b278
    // 0x130af5c: tbnz            w0, #4, #0x130b274
    // 0x130af60: ldur            x0, [fp, #-0x10]
    // 0x130af64: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130af64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130af68: ldr             x0, [x0, #0x1c80]
    //     0x130af6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130af70: cmp             w0, w16
    //     0x130af74: b.ne            #0x130af80
    //     0x130af78: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130af7c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130af80: r1 = Null
    //     0x130af80: mov             x1, NULL
    // 0x130af84: r2 = 44
    //     0x130af84: movz            x2, #0x2c
    // 0x130af88: r0 = AllocateArray()
    //     0x130af88: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130af8c: stur            x0, [fp, #-0x18]
    // 0x130af90: r16 = "couponCode"
    //     0x130af90: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x130af94: ldr             x16, [x16, #0x310]
    // 0x130af98: StoreField: r0->field_f = r16
    //     0x130af98: stur            w16, [x0, #0xf]
    // 0x130af9c: r16 = ""
    //     0x130af9c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130afa0: StoreField: r0->field_13 = r16
    //     0x130afa0: stur            w16, [x0, #0x13]
    // 0x130afa4: r16 = "product_id"
    //     0x130afa4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130afa8: ldr             x16, [x16, #0x9b8]
    // 0x130afac: ArrayStore: r0[0] = r16  ; List_4
    //     0x130afac: stur            w16, [x0, #0x17]
    // 0x130afb0: ldur            x2, [fp, #-0x10]
    // 0x130afb4: LoadField: r1 = r2->field_f
    //     0x130afb4: ldur            w1, [x2, #0xf]
    // 0x130afb8: DecompressPointer r1
    //     0x130afb8: add             x1, x1, HEAP, lsl #32
    // 0x130afbc: r0 = controller()
    //     0x130afbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130afc0: LoadField: r1 = r0->field_8f
    //     0x130afc0: ldur            w1, [x0, #0x8f]
    // 0x130afc4: DecompressPointer r1
    //     0x130afc4: add             x1, x1, HEAP, lsl #32
    // 0x130afc8: r0 = value()
    //     0x130afc8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130afcc: ldur            x1, [fp, #-0x18]
    // 0x130afd0: ArrayStore: r1[3] = r0  ; List_4
    //     0x130afd0: add             x25, x1, #0x1b
    //     0x130afd4: str             w0, [x25]
    //     0x130afd8: tbz             w0, #0, #0x130aff4
    //     0x130afdc: ldurb           w16, [x1, #-1]
    //     0x130afe0: ldurb           w17, [x0, #-1]
    //     0x130afe4: and             x16, x17, x16, lsr #2
    //     0x130afe8: tst             x16, HEAP, lsr #32
    //     0x130afec: b.eq            #0x130aff4
    //     0x130aff0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130aff4: ldur            x0, [fp, #-0x18]
    // 0x130aff8: r16 = "sku_id"
    //     0x130aff8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130affc: ldr             x16, [x16, #0x498]
    // 0x130b000: StoreField: r0->field_1f = r16
    //     0x130b000: stur            w16, [x0, #0x1f]
    // 0x130b004: ldur            x2, [fp, #-0x10]
    // 0x130b008: LoadField: r1 = r2->field_f
    //     0x130b008: ldur            w1, [x2, #0xf]
    // 0x130b00c: DecompressPointer r1
    //     0x130b00c: add             x1, x1, HEAP, lsl #32
    // 0x130b010: r0 = controller()
    //     0x130b010: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b014: LoadField: r1 = r0->field_93
    //     0x130b014: ldur            w1, [x0, #0x93]
    // 0x130b018: DecompressPointer r1
    //     0x130b018: add             x1, x1, HEAP, lsl #32
    // 0x130b01c: r0 = value()
    //     0x130b01c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b020: ldur            x1, [fp, #-0x18]
    // 0x130b024: ArrayStore: r1[5] = r0  ; List_4
    //     0x130b024: add             x25, x1, #0x23
    //     0x130b028: str             w0, [x25]
    //     0x130b02c: tbz             w0, #0, #0x130b048
    //     0x130b030: ldurb           w16, [x1, #-1]
    //     0x130b034: ldurb           w17, [x0, #-1]
    //     0x130b038: and             x16, x17, x16, lsr #2
    //     0x130b03c: tst             x16, HEAP, lsr #32
    //     0x130b040: b.eq            #0x130b048
    //     0x130b044: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b048: ldur            x0, [fp, #-0x18]
    // 0x130b04c: r16 = "coming_from"
    //     0x130b04c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130b050: ldr             x16, [x16, #0x328]
    // 0x130b054: StoreField: r0->field_27 = r16
    //     0x130b054: stur            w16, [x0, #0x27]
    // 0x130b058: r16 = "buyNow"
    //     0x130b058: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130b05c: ldr             x16, [x16, #0x358]
    // 0x130b060: StoreField: r0->field_2b = r16
    //     0x130b060: stur            w16, [x0, #0x2b]
    // 0x130b064: r16 = "quantity"
    //     0x130b064: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130b068: ldr             x16, [x16, #0x428]
    // 0x130b06c: StoreField: r0->field_2f = r16
    //     0x130b06c: stur            w16, [x0, #0x2f]
    // 0x130b070: ldur            x2, [fp, #-0x10]
    // 0x130b074: LoadField: r1 = r2->field_f
    //     0x130b074: ldur            w1, [x2, #0xf]
    // 0x130b078: DecompressPointer r1
    //     0x130b078: add             x1, x1, HEAP, lsl #32
    // 0x130b07c: r0 = controller()
    //     0x130b07c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b080: LoadField: r1 = r0->field_ab
    //     0x130b080: ldur            w1, [x0, #0xab]
    // 0x130b084: DecompressPointer r1
    //     0x130b084: add             x1, x1, HEAP, lsl #32
    // 0x130b088: r0 = value()
    //     0x130b088: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b08c: ldur            x1, [fp, #-0x18]
    // 0x130b090: ArrayStore: r1[9] = r0  ; List_4
    //     0x130b090: add             x25, x1, #0x33
    //     0x130b094: str             w0, [x25]
    //     0x130b098: tbz             w0, #0, #0x130b0b4
    //     0x130b09c: ldurb           w16, [x1, #-1]
    //     0x130b0a0: ldurb           w17, [x0, #-1]
    //     0x130b0a4: and             x16, x17, x16, lsr #2
    //     0x130b0a8: tst             x16, HEAP, lsr #32
    //     0x130b0ac: b.eq            #0x130b0b4
    //     0x130b0b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b0b4: ldur            x3, [fp, #-0x18]
    // 0x130b0b8: r16 = "previousScreenSource"
    //     0x130b0b8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130b0bc: ldr             x16, [x16, #0x448]
    // 0x130b0c0: StoreField: r3->field_37 = r16
    //     0x130b0c0: stur            w16, [x3, #0x37]
    // 0x130b0c4: r16 = "product_page"
    //     0x130b0c4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130b0c8: ldr             x16, [x16, #0x480]
    // 0x130b0cc: StoreField: r3->field_3b = r16
    //     0x130b0cc: stur            w16, [x3, #0x3b]
    // 0x130b0d0: r16 = "customization_request"
    //     0x130b0d0: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130b0d4: ldr             x16, [x16, #0x2b8]
    // 0x130b0d8: StoreField: r3->field_3f = r16
    //     0x130b0d8: stur            w16, [x3, #0x3f]
    // 0x130b0dc: mov             x1, x3
    // 0x130b0e0: ldur            x0, [fp, #-0x48]
    // 0x130b0e4: ArrayStore: r1[13] = r0  ; List_4
    //     0x130b0e4: add             x25, x1, #0x43
    //     0x130b0e8: str             w0, [x25]
    //     0x130b0ec: tbz             w0, #0, #0x130b108
    //     0x130b0f0: ldurb           w16, [x1, #-1]
    //     0x130b0f4: ldurb           w17, [x0, #-1]
    //     0x130b0f8: and             x16, x17, x16, lsr #2
    //     0x130b0fc: tst             x16, HEAP, lsr #32
    //     0x130b100: b.eq            #0x130b108
    //     0x130b104: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b108: r16 = "customization_prize"
    //     0x130b108: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130b10c: ldr             x16, [x16, #0x2e8]
    // 0x130b110: StoreField: r3->field_47 = r16
    //     0x130b110: stur            w16, [x3, #0x47]
    // 0x130b114: r1 = Null
    //     0x130b114: mov             x1, NULL
    // 0x130b118: r2 = 4
    //     0x130b118: movz            x2, #0x4
    // 0x130b11c: r0 = AllocateArray()
    //     0x130b11c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130b120: stur            x0, [fp, #-0x28]
    // 0x130b124: r16 = "₹"
    //     0x130b124: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130b128: ldr             x16, [x16, #0x360]
    // 0x130b12c: StoreField: r0->field_f = r16
    //     0x130b12c: stur            w16, [x0, #0xf]
    // 0x130b130: r1 = Null
    //     0x130b130: mov             x1, NULL
    // 0x130b134: r0 = NumberFormat()
    //     0x130b134: bl              #0xa30f6c  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat
    // 0x130b138: mov             x2, x0
    // 0x130b13c: ldur            x0, [fp, #-0x10]
    // 0x130b140: stur            x2, [fp, #-0x30]
    // 0x130b144: LoadField: r1 = r0->field_f
    //     0x130b144: ldur            w1, [x0, #0xf]
    // 0x130b148: DecompressPointer r1
    //     0x130b148: add             x1, x1, HEAP, lsl #32
    // 0x130b14c: r0 = controller()
    //     0x130b14c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b150: LoadField: r1 = r0->field_67
    //     0x130b150: ldur            w1, [x0, #0x67]
    // 0x130b154: DecompressPointer r1
    //     0x130b154: add             x1, x1, HEAP, lsl #32
    // 0x130b158: r0 = value()
    //     0x130b158: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b15c: ldur            x1, [fp, #-0x30]
    // 0x130b160: mov             x2, x0
    // 0x130b164: r0 = format()
    //     0x130b164: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130b168: ldur            x1, [fp, #-0x28]
    // 0x130b16c: ArrayStore: r1[1] = r0  ; List_4
    //     0x130b16c: add             x25, x1, #0x13
    //     0x130b170: str             w0, [x25]
    //     0x130b174: tbz             w0, #0, #0x130b190
    //     0x130b178: ldurb           w16, [x1, #-1]
    //     0x130b17c: ldurb           w17, [x0, #-1]
    //     0x130b180: and             x16, x17, x16, lsr #2
    //     0x130b184: tst             x16, HEAP, lsr #32
    //     0x130b188: b.eq            #0x130b190
    //     0x130b18c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b190: ldur            x16, [fp, #-0x28]
    // 0x130b194: str             x16, [SP]
    // 0x130b198: r0 = _interpolate()
    //     0x130b198: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130b19c: ldur            x1, [fp, #-0x18]
    // 0x130b1a0: ArrayStore: r1[15] = r0  ; List_4
    //     0x130b1a0: add             x25, x1, #0x4b
    //     0x130b1a4: str             w0, [x25]
    //     0x130b1a8: tbz             w0, #0, #0x130b1c4
    //     0x130b1ac: ldurb           w16, [x1, #-1]
    //     0x130b1b0: ldurb           w17, [x0, #-1]
    //     0x130b1b4: and             x16, x17, x16, lsr #2
    //     0x130b1b8: tst             x16, HEAP, lsr #32
    //     0x130b1bc: b.eq            #0x130b1c4
    //     0x130b1c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b1c4: ldur            x0, [fp, #-0x18]
    // 0x130b1c8: r16 = "is_skipped_address"
    //     0x130b1c8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130b1cc: ldr             x16, [x16, #0xb80]
    // 0x130b1d0: StoreField: r0->field_4f = r16
    //     0x130b1d0: stur            w16, [x0, #0x4f]
    // 0x130b1d4: r16 = false
    //     0x130b1d4: add             x16, NULL, #0x30  ; false
    // 0x130b1d8: StoreField: r0->field_53 = r16
    //     0x130b1d8: stur            w16, [x0, #0x53]
    // 0x130b1dc: r16 = "checkout_id"
    //     0x130b1dc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130b1e0: ldr             x16, [x16, #0xb88]
    // 0x130b1e4: StoreField: r0->field_57 = r16
    //     0x130b1e4: stur            w16, [x0, #0x57]
    // 0x130b1e8: r16 = ""
    //     0x130b1e8: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130b1ec: StoreField: r0->field_5b = r16
    //     0x130b1ec: stur            w16, [x0, #0x5b]
    // 0x130b1f0: r16 = "user_data"
    //     0x130b1f0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x130b1f4: ldr             x16, [x16, #0x58]
    // 0x130b1f8: StoreField: r0->field_5f = r16
    //     0x130b1f8: stur            w16, [x0, #0x5f]
    // 0x130b1fc: ldur            x1, [fp, #-0x10]
    // 0x130b200: LoadField: r2 = r1->field_f
    //     0x130b200: ldur            w2, [x1, #0xf]
    // 0x130b204: DecompressPointer r2
    //     0x130b204: add             x2, x2, HEAP, lsl #32
    // 0x130b208: mov             x1, x2
    // 0x130b20c: r0 = controller()
    //     0x130b20c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b210: LoadField: r1 = r0->field_5f
    //     0x130b210: ldur            w1, [x0, #0x5f]
    // 0x130b214: DecompressPointer r1
    //     0x130b214: add             x1, x1, HEAP, lsl #32
    // 0x130b218: r0 = value()
    //     0x130b218: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b21c: ldur            x1, [fp, #-0x18]
    // 0x130b220: ArrayStore: r1[21] = r0  ; List_4
    //     0x130b220: add             x25, x1, #0x63
    //     0x130b224: str             w0, [x25]
    //     0x130b228: tbz             w0, #0, #0x130b244
    //     0x130b22c: ldurb           w16, [x1, #-1]
    //     0x130b230: ldurb           w17, [x0, #-1]
    //     0x130b234: and             x16, x17, x16, lsr #2
    //     0x130b238: tst             x16, HEAP, lsr #32
    //     0x130b23c: b.eq            #0x130b244
    //     0x130b240: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b244: r16 = <String, dynamic>
    //     0x130b244: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130b248: ldur            lr, [fp, #-0x18]
    // 0x130b24c: stp             lr, x16, [SP]
    // 0x130b250: r0 = Map._fromLiteral()
    //     0x130b250: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130b254: r16 = "/checkout_request_address_page"
    //     0x130b254: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0x130b258: ldr             x16, [x16, #0x9e8]
    // 0x130b25c: stp             x16, NULL, [SP, #8]
    // 0x130b260: str             x0, [SP]
    // 0x130b264: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130b264: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130b268: ldr             x4, [x4, #0x438]
    // 0x130b26c: r0 = GetNavigation.toNamed()
    //     0x130b26c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130b270: b               #0x130b900
    // 0x130b274: ldur            x1, [fp, #-0x10]
    // 0x130b278: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130b278: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130b27c: ldr             x0, [x0, #0x1c80]
    //     0x130b280: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130b284: cmp             w0, w16
    //     0x130b288: b.ne            #0x130b294
    //     0x130b28c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130b290: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130b294: r1 = Null
    //     0x130b294: mov             x1, NULL
    // 0x130b298: r2 = 44
    //     0x130b298: movz            x2, #0x2c
    // 0x130b29c: r0 = AllocateArray()
    //     0x130b29c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130b2a0: stur            x0, [fp, #-0x18]
    // 0x130b2a4: r16 = "couponCode"
    //     0x130b2a4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x130b2a8: ldr             x16, [x16, #0x310]
    // 0x130b2ac: StoreField: r0->field_f = r16
    //     0x130b2ac: stur            w16, [x0, #0xf]
    // 0x130b2b0: r16 = ""
    //     0x130b2b0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130b2b4: StoreField: r0->field_13 = r16
    //     0x130b2b4: stur            w16, [x0, #0x13]
    // 0x130b2b8: r16 = "product_id"
    //     0x130b2b8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130b2bc: ldr             x16, [x16, #0x9b8]
    // 0x130b2c0: ArrayStore: r0[0] = r16  ; List_4
    //     0x130b2c0: stur            w16, [x0, #0x17]
    // 0x130b2c4: ldur            x2, [fp, #-0x10]
    // 0x130b2c8: LoadField: r1 = r2->field_f
    //     0x130b2c8: ldur            w1, [x2, #0xf]
    // 0x130b2cc: DecompressPointer r1
    //     0x130b2cc: add             x1, x1, HEAP, lsl #32
    // 0x130b2d0: r0 = controller()
    //     0x130b2d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b2d4: LoadField: r1 = r0->field_8f
    //     0x130b2d4: ldur            w1, [x0, #0x8f]
    // 0x130b2d8: DecompressPointer r1
    //     0x130b2d8: add             x1, x1, HEAP, lsl #32
    // 0x130b2dc: r0 = value()
    //     0x130b2dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b2e0: ldur            x1, [fp, #-0x18]
    // 0x130b2e4: ArrayStore: r1[3] = r0  ; List_4
    //     0x130b2e4: add             x25, x1, #0x1b
    //     0x130b2e8: str             w0, [x25]
    //     0x130b2ec: tbz             w0, #0, #0x130b308
    //     0x130b2f0: ldurb           w16, [x1, #-1]
    //     0x130b2f4: ldurb           w17, [x0, #-1]
    //     0x130b2f8: and             x16, x17, x16, lsr #2
    //     0x130b2fc: tst             x16, HEAP, lsr #32
    //     0x130b300: b.eq            #0x130b308
    //     0x130b304: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b308: ldur            x0, [fp, #-0x18]
    // 0x130b30c: r16 = "sku_id"
    //     0x130b30c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130b310: ldr             x16, [x16, #0x498]
    // 0x130b314: StoreField: r0->field_1f = r16
    //     0x130b314: stur            w16, [x0, #0x1f]
    // 0x130b318: ldur            x2, [fp, #-0x10]
    // 0x130b31c: LoadField: r1 = r2->field_f
    //     0x130b31c: ldur            w1, [x2, #0xf]
    // 0x130b320: DecompressPointer r1
    //     0x130b320: add             x1, x1, HEAP, lsl #32
    // 0x130b324: r0 = controller()
    //     0x130b324: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b328: LoadField: r1 = r0->field_93
    //     0x130b328: ldur            w1, [x0, #0x93]
    // 0x130b32c: DecompressPointer r1
    //     0x130b32c: add             x1, x1, HEAP, lsl #32
    // 0x130b330: r0 = value()
    //     0x130b330: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b334: ldur            x1, [fp, #-0x18]
    // 0x130b338: ArrayStore: r1[5] = r0  ; List_4
    //     0x130b338: add             x25, x1, #0x23
    //     0x130b33c: str             w0, [x25]
    //     0x130b340: tbz             w0, #0, #0x130b35c
    //     0x130b344: ldurb           w16, [x1, #-1]
    //     0x130b348: ldurb           w17, [x0, #-1]
    //     0x130b34c: and             x16, x17, x16, lsr #2
    //     0x130b350: tst             x16, HEAP, lsr #32
    //     0x130b354: b.eq            #0x130b35c
    //     0x130b358: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b35c: ldur            x0, [fp, #-0x18]
    // 0x130b360: r16 = "coming_from"
    //     0x130b360: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130b364: ldr             x16, [x16, #0x328]
    // 0x130b368: StoreField: r0->field_27 = r16
    //     0x130b368: stur            w16, [x0, #0x27]
    // 0x130b36c: r16 = "buyNow"
    //     0x130b36c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130b370: ldr             x16, [x16, #0x358]
    // 0x130b374: StoreField: r0->field_2b = r16
    //     0x130b374: stur            w16, [x0, #0x2b]
    // 0x130b378: r16 = "quantity"
    //     0x130b378: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130b37c: ldr             x16, [x16, #0x428]
    // 0x130b380: StoreField: r0->field_2f = r16
    //     0x130b380: stur            w16, [x0, #0x2f]
    // 0x130b384: ldur            x2, [fp, #-0x10]
    // 0x130b388: LoadField: r1 = r2->field_f
    //     0x130b388: ldur            w1, [x2, #0xf]
    // 0x130b38c: DecompressPointer r1
    //     0x130b38c: add             x1, x1, HEAP, lsl #32
    // 0x130b390: r0 = controller()
    //     0x130b390: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b394: LoadField: r1 = r0->field_ab
    //     0x130b394: ldur            w1, [x0, #0xab]
    // 0x130b398: DecompressPointer r1
    //     0x130b398: add             x1, x1, HEAP, lsl #32
    // 0x130b39c: r0 = value()
    //     0x130b39c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b3a0: ldur            x1, [fp, #-0x18]
    // 0x130b3a4: ArrayStore: r1[9] = r0  ; List_4
    //     0x130b3a4: add             x25, x1, #0x33
    //     0x130b3a8: str             w0, [x25]
    //     0x130b3ac: tbz             w0, #0, #0x130b3c8
    //     0x130b3b0: ldurb           w16, [x1, #-1]
    //     0x130b3b4: ldurb           w17, [x0, #-1]
    //     0x130b3b8: and             x16, x17, x16, lsr #2
    //     0x130b3bc: tst             x16, HEAP, lsr #32
    //     0x130b3c0: b.eq            #0x130b3c8
    //     0x130b3c4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b3c8: ldur            x3, [fp, #-0x18]
    // 0x130b3cc: r16 = "previousScreenSource"
    //     0x130b3cc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130b3d0: ldr             x16, [x16, #0x448]
    // 0x130b3d4: StoreField: r3->field_37 = r16
    //     0x130b3d4: stur            w16, [x3, #0x37]
    // 0x130b3d8: r16 = "product_page"
    //     0x130b3d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130b3dc: ldr             x16, [x16, #0x480]
    // 0x130b3e0: StoreField: r3->field_3b = r16
    //     0x130b3e0: stur            w16, [x3, #0x3b]
    // 0x130b3e4: r16 = "customization_request"
    //     0x130b3e4: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130b3e8: ldr             x16, [x16, #0x2b8]
    // 0x130b3ec: StoreField: r3->field_3f = r16
    //     0x130b3ec: stur            w16, [x3, #0x3f]
    // 0x130b3f0: mov             x1, x3
    // 0x130b3f4: ldur            x0, [fp, #-0x48]
    // 0x130b3f8: ArrayStore: r1[13] = r0  ; List_4
    //     0x130b3f8: add             x25, x1, #0x43
    //     0x130b3fc: str             w0, [x25]
    //     0x130b400: tbz             w0, #0, #0x130b41c
    //     0x130b404: ldurb           w16, [x1, #-1]
    //     0x130b408: ldurb           w17, [x0, #-1]
    //     0x130b40c: and             x16, x17, x16, lsr #2
    //     0x130b410: tst             x16, HEAP, lsr #32
    //     0x130b414: b.eq            #0x130b41c
    //     0x130b418: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b41c: r16 = "customization_prize"
    //     0x130b41c: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130b420: ldr             x16, [x16, #0x2e8]
    // 0x130b424: StoreField: r3->field_47 = r16
    //     0x130b424: stur            w16, [x3, #0x47]
    // 0x130b428: r1 = Null
    //     0x130b428: mov             x1, NULL
    // 0x130b42c: r2 = 4
    //     0x130b42c: movz            x2, #0x4
    // 0x130b430: r0 = AllocateArray()
    //     0x130b430: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130b434: stur            x0, [fp, #-0x28]
    // 0x130b438: r16 = "₹"
    //     0x130b438: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130b43c: ldr             x16, [x16, #0x360]
    // 0x130b440: StoreField: r0->field_f = r16
    //     0x130b440: stur            w16, [x0, #0xf]
    // 0x130b444: r1 = Null
    //     0x130b444: mov             x1, NULL
    // 0x130b448: r0 = NumberFormat()
    //     0x130b448: bl              #0xa30f6c  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat
    // 0x130b44c: mov             x2, x0
    // 0x130b450: ldur            x0, [fp, #-0x10]
    // 0x130b454: stur            x2, [fp, #-0x30]
    // 0x130b458: LoadField: r1 = r0->field_f
    //     0x130b458: ldur            w1, [x0, #0xf]
    // 0x130b45c: DecompressPointer r1
    //     0x130b45c: add             x1, x1, HEAP, lsl #32
    // 0x130b460: r0 = controller()
    //     0x130b460: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b464: LoadField: r1 = r0->field_67
    //     0x130b464: ldur            w1, [x0, #0x67]
    // 0x130b468: DecompressPointer r1
    //     0x130b468: add             x1, x1, HEAP, lsl #32
    // 0x130b46c: r0 = value()
    //     0x130b46c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b470: ldur            x1, [fp, #-0x30]
    // 0x130b474: mov             x2, x0
    // 0x130b478: r0 = format()
    //     0x130b478: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130b47c: ldur            x1, [fp, #-0x28]
    // 0x130b480: ArrayStore: r1[1] = r0  ; List_4
    //     0x130b480: add             x25, x1, #0x13
    //     0x130b484: str             w0, [x25]
    //     0x130b488: tbz             w0, #0, #0x130b4a4
    //     0x130b48c: ldurb           w16, [x1, #-1]
    //     0x130b490: ldurb           w17, [x0, #-1]
    //     0x130b494: and             x16, x17, x16, lsr #2
    //     0x130b498: tst             x16, HEAP, lsr #32
    //     0x130b49c: b.eq            #0x130b4a4
    //     0x130b4a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b4a4: ldur            x16, [fp, #-0x28]
    // 0x130b4a8: str             x16, [SP]
    // 0x130b4ac: r0 = _interpolate()
    //     0x130b4ac: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130b4b0: ldur            x1, [fp, #-0x18]
    // 0x130b4b4: ArrayStore: r1[15] = r0  ; List_4
    //     0x130b4b4: add             x25, x1, #0x4b
    //     0x130b4b8: str             w0, [x25]
    //     0x130b4bc: tbz             w0, #0, #0x130b4d8
    //     0x130b4c0: ldurb           w16, [x1, #-1]
    //     0x130b4c4: ldurb           w17, [x0, #-1]
    //     0x130b4c8: and             x16, x17, x16, lsr #2
    //     0x130b4cc: tst             x16, HEAP, lsr #32
    //     0x130b4d0: b.eq            #0x130b4d8
    //     0x130b4d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b4d8: ldur            x0, [fp, #-0x18]
    // 0x130b4dc: r16 = "is_skipped_address"
    //     0x130b4dc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130b4e0: ldr             x16, [x16, #0xb80]
    // 0x130b4e4: StoreField: r0->field_4f = r16
    //     0x130b4e4: stur            w16, [x0, #0x4f]
    // 0x130b4e8: r16 = true
    //     0x130b4e8: add             x16, NULL, #0x20  ; true
    // 0x130b4ec: StoreField: r0->field_53 = r16
    //     0x130b4ec: stur            w16, [x0, #0x53]
    // 0x130b4f0: r16 = "checkout_id"
    //     0x130b4f0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130b4f4: ldr             x16, [x16, #0xb88]
    // 0x130b4f8: StoreField: r0->field_57 = r16
    //     0x130b4f8: stur            w16, [x0, #0x57]
    // 0x130b4fc: r16 = ""
    //     0x130b4fc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130b500: StoreField: r0->field_5b = r16
    //     0x130b500: stur            w16, [x0, #0x5b]
    // 0x130b504: r16 = "user_data"
    //     0x130b504: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x130b508: ldr             x16, [x16, #0x58]
    // 0x130b50c: StoreField: r0->field_5f = r16
    //     0x130b50c: stur            w16, [x0, #0x5f]
    // 0x130b510: ldur            x2, [fp, #-0x10]
    // 0x130b514: LoadField: r1 = r2->field_f
    //     0x130b514: ldur            w1, [x2, #0xf]
    // 0x130b518: DecompressPointer r1
    //     0x130b518: add             x1, x1, HEAP, lsl #32
    // 0x130b51c: r0 = controller()
    //     0x130b51c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b520: mov             x1, x0
    // 0x130b524: r0 = isShowSearch()
    //     0x130b524: bl              #0x8adacc  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::isShowSearch
    // 0x130b528: ldur            x1, [fp, #-0x18]
    // 0x130b52c: ArrayStore: r1[21] = r0  ; List_4
    //     0x130b52c: add             x25, x1, #0x63
    //     0x130b530: str             w0, [x25]
    //     0x130b534: tbz             w0, #0, #0x130b550
    //     0x130b538: ldurb           w16, [x1, #-1]
    //     0x130b53c: ldurb           w17, [x0, #-1]
    //     0x130b540: and             x16, x17, x16, lsr #2
    //     0x130b544: tst             x16, HEAP, lsr #32
    //     0x130b548: b.eq            #0x130b550
    //     0x130b54c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b550: r16 = <String, dynamic>
    //     0x130b550: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130b554: ldur            lr, [fp, #-0x18]
    // 0x130b558: stp             lr, x16, [SP]
    // 0x130b55c: r0 = Map._fromLiteral()
    //     0x130b55c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130b560: r16 = "/checkout_order_summary_page"
    //     0x130b560: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0x130b564: ldr             x16, [x16, #0x9d8]
    // 0x130b568: stp             x16, NULL, [SP, #8]
    // 0x130b56c: str             x0, [SP]
    // 0x130b570: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130b570: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130b574: ldr             x4, [x4, #0x438]
    // 0x130b578: r0 = GetNavigation.toNamed()
    //     0x130b578: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130b57c: b               #0x130b900
    // 0x130b580: ldur            x2, [fp, #-0x10]
    // 0x130b584: LoadField: r1 = r2->field_f
    //     0x130b584: ldur            w1, [x2, #0xf]
    // 0x130b588: DecompressPointer r1
    //     0x130b588: add             x1, x1, HEAP, lsl #32
    // 0x130b58c: r0 = controller()
    //     0x130b58c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b590: mov             x1, x0
    // 0x130b594: r0 = bagEntities()
    //     0x130b594: bl              #0x89ec4c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::bagEntities
    // 0x130b598: mov             x2, x0
    // 0x130b59c: ldur            x0, [fp, #-0x10]
    // 0x130b5a0: stur            x2, [fp, #-0x18]
    // 0x130b5a4: LoadField: r1 = r0->field_f
    //     0x130b5a4: ldur            w1, [x0, #0xf]
    // 0x130b5a8: DecompressPointer r1
    //     0x130b5a8: add             x1, x1, HEAP, lsl #32
    // 0x130b5ac: r0 = controller()
    //     0x130b5ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b5b0: LoadField: r1 = r0->field_93
    //     0x130b5b0: ldur            w1, [x0, #0x93]
    // 0x130b5b4: DecompressPointer r1
    //     0x130b5b4: add             x1, x1, HEAP, lsl #32
    // 0x130b5b8: r0 = value()
    //     0x130b5b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b5bc: mov             x2, x0
    // 0x130b5c0: ldur            x0, [fp, #-0x10]
    // 0x130b5c4: stur            x2, [fp, #-0x28]
    // 0x130b5c8: LoadField: r1 = r0->field_f
    //     0x130b5c8: ldur            w1, [x0, #0xf]
    // 0x130b5cc: DecompressPointer r1
    //     0x130b5cc: add             x1, x1, HEAP, lsl #32
    // 0x130b5d0: r0 = controller()
    //     0x130b5d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b5d4: LoadField: r1 = r0->field_ab
    //     0x130b5d4: ldur            w1, [x0, #0xab]
    // 0x130b5d8: DecompressPointer r1
    //     0x130b5d8: add             x1, x1, HEAP, lsl #32
    // 0x130b5dc: r0 = value()
    //     0x130b5dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b5e0: mov             x2, x0
    // 0x130b5e4: ldur            x0, [fp, #-0x10]
    // 0x130b5e8: stur            x2, [fp, #-0x30]
    // 0x130b5ec: LoadField: r1 = r0->field_f
    //     0x130b5ec: ldur            w1, [x0, #0xf]
    // 0x130b5f0: DecompressPointer r1
    //     0x130b5f0: add             x1, x1, HEAP, lsl #32
    // 0x130b5f4: r0 = controller()
    //     0x130b5f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b5f8: LoadField: r2 = r0->field_9b
    //     0x130b5f8: ldur            w2, [x0, #0x9b]
    // 0x130b5fc: DecompressPointer r2
    //     0x130b5fc: add             x2, x2, HEAP, lsl #32
    // 0x130b600: ldur            x0, [fp, #-0x10]
    // 0x130b604: stur            x2, [fp, #-0x38]
    // 0x130b608: LoadField: r1 = r0->field_f
    //     0x130b608: ldur            w1, [x0, #0xf]
    // 0x130b60c: DecompressPointer r1
    //     0x130b60c: add             x1, x1, HEAP, lsl #32
    // 0x130b610: r0 = controller()
    //     0x130b610: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b614: mov             x1, x0
    // 0x130b618: r0 = offerParams()
    //     0x130b618: bl              #0x91dfa4  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::offerParams
    // 0x130b61c: stur            x0, [fp, #-0x40]
    // 0x130b620: r0 = CustomizedRequest()
    //     0x130b620: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x130b624: mov             x1, x0
    // 0x130b628: ldur            x0, [fp, #-0x18]
    // 0x130b62c: stur            x1, [fp, #-0x48]
    // 0x130b630: StoreField: r1->field_7 = r0
    //     0x130b630: stur            w0, [x1, #7]
    // 0x130b634: ldur            x0, [fp, #-0x28]
    // 0x130b638: StoreField: r1->field_b = r0
    //     0x130b638: stur            w0, [x1, #0xb]
    // 0x130b63c: ldur            x0, [fp, #-0x30]
    // 0x130b640: StoreField: r1->field_f = r0
    //     0x130b640: stur            w0, [x1, #0xf]
    // 0x130b644: ldur            x0, [fp, #-0x38]
    // 0x130b648: StoreField: r1->field_13 = r0
    //     0x130b648: stur            w0, [x1, #0x13]
    // 0x130b64c: ldur            x0, [fp, #-0x40]
    // 0x130b650: ArrayStore: r1[0] = r0  ; List_4
    //     0x130b650: stur            w0, [x1, #0x17]
    // 0x130b654: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130b654: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130b658: ldr             x0, [x0, #0x1c80]
    //     0x130b65c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130b660: cmp             w0, w16
    //     0x130b664: b.ne            #0x130b670
    //     0x130b668: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130b66c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130b670: r1 = Null
    //     0x130b670: mov             x1, NULL
    // 0x130b674: r2 = 32
    //     0x130b674: movz            x2, #0x20
    // 0x130b678: r0 = AllocateArray()
    //     0x130b678: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130b67c: stur            x0, [fp, #-0x18]
    // 0x130b680: r16 = "previousScreenSource"
    //     0x130b680: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130b684: ldr             x16, [x16, #0x448]
    // 0x130b688: StoreField: r0->field_f = r16
    //     0x130b688: stur            w16, [x0, #0xf]
    // 0x130b68c: r16 = "product_page"
    //     0x130b68c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130b690: ldr             x16, [x16, #0x480]
    // 0x130b694: StoreField: r0->field_13 = r16
    //     0x130b694: stur            w16, [x0, #0x13]
    // 0x130b698: r16 = "product_id"
    //     0x130b698: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130b69c: ldr             x16, [x16, #0x9b8]
    // 0x130b6a0: ArrayStore: r0[0] = r16  ; List_4
    //     0x130b6a0: stur            w16, [x0, #0x17]
    // 0x130b6a4: ldur            x2, [fp, #-0x10]
    // 0x130b6a8: LoadField: r1 = r2->field_f
    //     0x130b6a8: ldur            w1, [x2, #0xf]
    // 0x130b6ac: DecompressPointer r1
    //     0x130b6ac: add             x1, x1, HEAP, lsl #32
    // 0x130b6b0: r0 = controller()
    //     0x130b6b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b6b4: LoadField: r1 = r0->field_8f
    //     0x130b6b4: ldur            w1, [x0, #0x8f]
    // 0x130b6b8: DecompressPointer r1
    //     0x130b6b8: add             x1, x1, HEAP, lsl #32
    // 0x130b6bc: r0 = value()
    //     0x130b6bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b6c0: ldur            x1, [fp, #-0x18]
    // 0x130b6c4: ArrayStore: r1[3] = r0  ; List_4
    //     0x130b6c4: add             x25, x1, #0x1b
    //     0x130b6c8: str             w0, [x25]
    //     0x130b6cc: tbz             w0, #0, #0x130b6e8
    //     0x130b6d0: ldurb           w16, [x1, #-1]
    //     0x130b6d4: ldurb           w17, [x0, #-1]
    //     0x130b6d8: and             x16, x17, x16, lsr #2
    //     0x130b6dc: tst             x16, HEAP, lsr #32
    //     0x130b6e0: b.eq            #0x130b6e8
    //     0x130b6e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b6e8: ldur            x0, [fp, #-0x18]
    // 0x130b6ec: r16 = "sku_id"
    //     0x130b6ec: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130b6f0: ldr             x16, [x16, #0x498]
    // 0x130b6f4: StoreField: r0->field_1f = r16
    //     0x130b6f4: stur            w16, [x0, #0x1f]
    // 0x130b6f8: ldur            x2, [fp, #-0x10]
    // 0x130b6fc: LoadField: r1 = r2->field_f
    //     0x130b6fc: ldur            w1, [x2, #0xf]
    // 0x130b700: DecompressPointer r1
    //     0x130b700: add             x1, x1, HEAP, lsl #32
    // 0x130b704: r0 = controller()
    //     0x130b704: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b708: LoadField: r1 = r0->field_93
    //     0x130b708: ldur            w1, [x0, #0x93]
    // 0x130b70c: DecompressPointer r1
    //     0x130b70c: add             x1, x1, HEAP, lsl #32
    // 0x130b710: r0 = value()
    //     0x130b710: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b714: ldur            x1, [fp, #-0x18]
    // 0x130b718: ArrayStore: r1[5] = r0  ; List_4
    //     0x130b718: add             x25, x1, #0x23
    //     0x130b71c: str             w0, [x25]
    //     0x130b720: tbz             w0, #0, #0x130b73c
    //     0x130b724: ldurb           w16, [x1, #-1]
    //     0x130b728: ldurb           w17, [x0, #-1]
    //     0x130b72c: and             x16, x17, x16, lsr #2
    //     0x130b730: tst             x16, HEAP, lsr #32
    //     0x130b734: b.eq            #0x130b73c
    //     0x130b738: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b73c: ldur            x0, [fp, #-0x18]
    // 0x130b740: r16 = "quantity"
    //     0x130b740: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130b744: ldr             x16, [x16, #0x428]
    // 0x130b748: StoreField: r0->field_27 = r16
    //     0x130b748: stur            w16, [x0, #0x27]
    // 0x130b74c: ldur            x2, [fp, #-0x10]
    // 0x130b750: LoadField: r1 = r2->field_f
    //     0x130b750: ldur            w1, [x2, #0xf]
    // 0x130b754: DecompressPointer r1
    //     0x130b754: add             x1, x1, HEAP, lsl #32
    // 0x130b758: r0 = controller()
    //     0x130b758: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b75c: LoadField: r1 = r0->field_ab
    //     0x130b75c: ldur            w1, [x0, #0xab]
    // 0x130b760: DecompressPointer r1
    //     0x130b760: add             x1, x1, HEAP, lsl #32
    // 0x130b764: r0 = value()
    //     0x130b764: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130b768: ldur            x1, [fp, #-0x18]
    // 0x130b76c: ArrayStore: r1[7] = r0  ; List_4
    //     0x130b76c: add             x25, x1, #0x2b
    //     0x130b770: str             w0, [x25]
    //     0x130b774: tbz             w0, #0, #0x130b790
    //     0x130b778: ldurb           w16, [x1, #-1]
    //     0x130b77c: ldurb           w17, [x0, #-1]
    //     0x130b780: and             x16, x17, x16, lsr #2
    //     0x130b784: tst             x16, HEAP, lsr #32
    //     0x130b788: b.eq            #0x130b790
    //     0x130b78c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b790: ldur            x3, [fp, #-0x18]
    // 0x130b794: r16 = "customization_request"
    //     0x130b794: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130b798: ldr             x16, [x16, #0x2b8]
    // 0x130b79c: StoreField: r3->field_2f = r16
    //     0x130b79c: stur            w16, [x3, #0x2f]
    // 0x130b7a0: mov             x1, x3
    // 0x130b7a4: ldur            x0, [fp, #-0x48]
    // 0x130b7a8: ArrayStore: r1[9] = r0  ; List_4
    //     0x130b7a8: add             x25, x1, #0x33
    //     0x130b7ac: str             w0, [x25]
    //     0x130b7b0: tbz             w0, #0, #0x130b7cc
    //     0x130b7b4: ldurb           w16, [x1, #-1]
    //     0x130b7b8: ldurb           w17, [x0, #-1]
    //     0x130b7bc: and             x16, x17, x16, lsr #2
    //     0x130b7c0: tst             x16, HEAP, lsr #32
    //     0x130b7c4: b.eq            #0x130b7cc
    //     0x130b7c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b7cc: r16 = "coming_from"
    //     0x130b7cc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130b7d0: ldr             x16, [x16, #0x328]
    // 0x130b7d4: StoreField: r3->field_37 = r16
    //     0x130b7d4: stur            w16, [x3, #0x37]
    // 0x130b7d8: r16 = "buyNow"
    //     0x130b7d8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130b7dc: ldr             x16, [x16, #0x358]
    // 0x130b7e0: StoreField: r3->field_3b = r16
    //     0x130b7e0: stur            w16, [x3, #0x3b]
    // 0x130b7e4: r16 = "customization_prize"
    //     0x130b7e4: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130b7e8: ldr             x16, [x16, #0x2e8]
    // 0x130b7ec: StoreField: r3->field_3f = r16
    //     0x130b7ec: stur            w16, [x3, #0x3f]
    // 0x130b7f0: r1 = Null
    //     0x130b7f0: mov             x1, NULL
    // 0x130b7f4: r2 = 4
    //     0x130b7f4: movz            x2, #0x4
    // 0x130b7f8: r0 = AllocateArray()
    //     0x130b7f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130b7fc: stur            x0, [fp, #-0x28]
    // 0x130b800: r16 = "₹"
    //     0x130b800: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130b804: ldr             x16, [x16, #0x360]
    // 0x130b808: StoreField: r0->field_f = r16
    //     0x130b808: stur            w16, [x0, #0xf]
    // 0x130b80c: r1 = Null
    //     0x130b80c: mov             x1, NULL
    // 0x130b810: r0 = NumberFormat()
    //     0x130b810: bl              #0xa30f6c  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat
    // 0x130b814: mov             x2, x0
    // 0x130b818: ldur            x0, [fp, #-0x10]
    // 0x130b81c: stur            x2, [fp, #-0x30]
    // 0x130b820: LoadField: r1 = r0->field_f
    //     0x130b820: ldur            w1, [x0, #0xf]
    // 0x130b824: DecompressPointer r1
    //     0x130b824: add             x1, x1, HEAP, lsl #32
    // 0x130b828: r0 = controller()
    //     0x130b828: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130b82c: mov             x1, x0
    // 0x130b830: r0 = price()
    //     0x130b830: bl              #0x1306d7c  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::price
    // 0x130b834: mov             x2, x0
    // 0x130b838: r0 = BoxInt64Instr(r2)
    //     0x130b838: sbfiz           x0, x2, #1, #0x1f
    //     0x130b83c: cmp             x2, x0, asr #1
    //     0x130b840: b.eq            #0x130b84c
    //     0x130b844: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x130b848: stur            x2, [x0, #7]
    // 0x130b84c: ldur            x1, [fp, #-0x30]
    // 0x130b850: mov             x2, x0
    // 0x130b854: r0 = format()
    //     0x130b854: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130b858: ldur            x1, [fp, #-0x28]
    // 0x130b85c: ArrayStore: r1[1] = r0  ; List_4
    //     0x130b85c: add             x25, x1, #0x13
    //     0x130b860: str             w0, [x25]
    //     0x130b864: tbz             w0, #0, #0x130b880
    //     0x130b868: ldurb           w16, [x1, #-1]
    //     0x130b86c: ldurb           w17, [x0, #-1]
    //     0x130b870: and             x16, x17, x16, lsr #2
    //     0x130b874: tst             x16, HEAP, lsr #32
    //     0x130b878: b.eq            #0x130b880
    //     0x130b87c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b880: ldur            x16, [fp, #-0x28]
    // 0x130b884: str             x16, [SP]
    // 0x130b888: r0 = _interpolate()
    //     0x130b888: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130b88c: ldur            x1, [fp, #-0x18]
    // 0x130b890: ArrayStore: r1[13] = r0  ; List_4
    //     0x130b890: add             x25, x1, #0x43
    //     0x130b894: str             w0, [x25]
    //     0x130b898: tbz             w0, #0, #0x130b8b4
    //     0x130b89c: ldurb           w16, [x1, #-1]
    //     0x130b8a0: ldurb           w17, [x0, #-1]
    //     0x130b8a4: and             x16, x17, x16, lsr #2
    //     0x130b8a8: tst             x16, HEAP, lsr #32
    //     0x130b8ac: b.eq            #0x130b8b4
    //     0x130b8b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130b8b4: ldur            x0, [fp, #-0x18]
    // 0x130b8b8: r16 = "is_skipped_address"
    //     0x130b8b8: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130b8bc: ldr             x16, [x16, #0xb80]
    // 0x130b8c0: StoreField: r0->field_47 = r16
    //     0x130b8c0: stur            w16, [x0, #0x47]
    // 0x130b8c4: r16 = true
    //     0x130b8c4: add             x16, NULL, #0x20  ; true
    // 0x130b8c8: StoreField: r0->field_4b = r16
    //     0x130b8c8: stur            w16, [x0, #0x4b]
    // 0x130b8cc: r16 = <String, dynamic>
    //     0x130b8cc: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130b8d0: stp             x0, x16, [SP]
    // 0x130b8d4: r0 = Map._fromLiteral()
    //     0x130b8d4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130b8d8: r16 = "/checkout_request_number_page"
    //     0x130b8d8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0x130b8dc: ldr             x16, [x16, #0x9f8]
    // 0x130b8e0: stp             x16, NULL, [SP, #8]
    // 0x130b8e4: str             x0, [SP]
    // 0x130b8e8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130b8e8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130b8ec: ldr             x4, [x4, #0x438]
    // 0x130b8f0: r0 = GetNavigation.toNamed()
    //     0x130b8f0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130b8f4: mov             x1, x0
    // 0x130b8f8: stur            x1, [fp, #-0x18]
    // 0x130b8fc: r0 = Await()
    //     0x130b8fc: bl              #0x63248c  ; AwaitStub
    // 0x130b900: r0 = Null
    //     0x130b900: mov             x0, NULL
    // 0x130b904: r0 = ReturnAsyncNotFuture()
    //     0x130b904: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x130b908: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130b908: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130b90c: b               #0x130ac48
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x130b910, size: 0x8d8
    // 0x130b910: EnterFrame
    //     0x130b910: stp             fp, lr, [SP, #-0x10]!
    //     0x130b914: mov             fp, SP
    // 0x130b918: AllocStack(0x58)
    //     0x130b918: sub             SP, SP, #0x58
    // 0x130b91c: SetupParameters()
    //     0x130b91c: ldr             x0, [fp, #0x10]
    //     0x130b920: ldur            w2, [x0, #0x17]
    //     0x130b924: add             x2, x2, HEAP, lsl #32
    //     0x130b928: stur            x2, [fp, #-8]
    // 0x130b92c: CheckStackOverflow
    //     0x130b92c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130b930: cmp             SP, x16
    //     0x130b934: b.ls            #0x130c1e0
    // 0x130b938: LoadField: r1 = r2->field_13
    //     0x130b938: ldur            w1, [x2, #0x13]
    // 0x130b93c: DecompressPointer r1
    //     0x130b93c: add             x1, x1, HEAP, lsl #32
    // 0x130b940: r0 = of()
    //     0x130b940: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130b944: LoadField: r1 = r0->field_5b
    //     0x130b944: ldur            w1, [x0, #0x5b]
    // 0x130b948: DecompressPointer r1
    //     0x130b948: add             x1, x1, HEAP, lsl #32
    // 0x130b94c: r0 = LoadClassIdInstr(r1)
    //     0x130b94c: ldur            x0, [x1, #-1]
    //     0x130b950: ubfx            x0, x0, #0xc, #0x14
    // 0x130b954: d0 = 0.100000
    //     0x130b954: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x130b958: r0 = GDT[cid_x0 + -0xffa]()
    //     0x130b958: sub             lr, x0, #0xffa
    //     0x130b95c: ldr             lr, [x21, lr, lsl #3]
    //     0x130b960: blr             lr
    // 0x130b964: ldur            x2, [fp, #-8]
    // 0x130b968: stur            x0, [fp, #-0x10]
    // 0x130b96c: LoadField: r1 = r2->field_13
    //     0x130b96c: ldur            w1, [x2, #0x13]
    // 0x130b970: DecompressPointer r1
    //     0x130b970: add             x1, x1, HEAP, lsl #32
    // 0x130b974: r0 = of()
    //     0x130b974: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130b978: LoadField: r1 = r0->field_87
    //     0x130b978: ldur            w1, [x0, #0x87]
    // 0x130b97c: DecompressPointer r1
    //     0x130b97c: add             x1, x1, HEAP, lsl #32
    // 0x130b980: LoadField: r0 = r1->field_2b
    //     0x130b980: ldur            w0, [x1, #0x2b]
    // 0x130b984: DecompressPointer r0
    //     0x130b984: add             x0, x0, HEAP, lsl #32
    // 0x130b988: r16 = 12.000000
    //     0x130b988: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x130b98c: ldr             x16, [x16, #0x9e8]
    // 0x130b990: r30 = Instance_Color
    //     0x130b990: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130b994: stp             lr, x16, [SP]
    // 0x130b998: mov             x1, x0
    // 0x130b99c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130b99c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130b9a0: ldr             x4, [x4, #0xaa0]
    // 0x130b9a4: r0 = copyWith()
    //     0x130b9a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130b9a8: stur            x0, [fp, #-0x18]
    // 0x130b9ac: r0 = Text()
    //     0x130b9ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130b9b0: mov             x1, x0
    // 0x130b9b4: r0 = "We will contact you to confirm the customization detail before shipping the item to you"
    //     0x130b9b4: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3b008] "We will contact you to confirm the customization detail before shipping the item to you"
    //     0x130b9b8: ldr             x0, [x0, #8]
    // 0x130b9bc: stur            x1, [fp, #-0x20]
    // 0x130b9c0: StoreField: r1->field_b = r0
    //     0x130b9c0: stur            w0, [x1, #0xb]
    // 0x130b9c4: ldur            x0, [fp, #-0x18]
    // 0x130b9c8: StoreField: r1->field_13 = r0
    //     0x130b9c8: stur            w0, [x1, #0x13]
    // 0x130b9cc: r2 = 6
    //     0x130b9cc: movz            x2, #0x6
    // 0x130b9d0: StoreField: r1->field_37 = r2
    //     0x130b9d0: stur            w2, [x1, #0x37]
    // 0x130b9d4: r0 = ConstrainedBox()
    //     0x130b9d4: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x130b9d8: mov             x3, x0
    // 0x130b9dc: r0 = Instance_BoxConstraints
    //     0x130b9dc: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3b010] Obj!BoxConstraints@d56781
    //     0x130b9e0: ldr             x0, [x0, #0x10]
    // 0x130b9e4: stur            x3, [fp, #-0x18]
    // 0x130b9e8: StoreField: r3->field_f = r0
    //     0x130b9e8: stur            w0, [x3, #0xf]
    // 0x130b9ec: ldur            x0, [fp, #-0x20]
    // 0x130b9f0: StoreField: r3->field_b = r0
    //     0x130b9f0: stur            w0, [x3, #0xb]
    // 0x130b9f4: r1 = Null
    //     0x130b9f4: mov             x1, NULL
    // 0x130b9f8: r2 = 6
    //     0x130b9f8: movz            x2, #0x6
    // 0x130b9fc: r0 = AllocateArray()
    //     0x130b9fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130ba00: stur            x0, [fp, #-0x20]
    // 0x130ba04: r16 = Instance_Icon
    //     0x130ba04: add             x16, PP, #0x36, lsl #12  ; [pp+0x365a8] Obj!Icon@d66171
    //     0x130ba08: ldr             x16, [x16, #0x5a8]
    // 0x130ba0c: StoreField: r0->field_f = r16
    //     0x130ba0c: stur            w16, [x0, #0xf]
    // 0x130ba10: r16 = Instance_SizedBox
    //     0x130ba10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0x130ba14: ldr             x16, [x16, #0x940]
    // 0x130ba18: StoreField: r0->field_13 = r16
    //     0x130ba18: stur            w16, [x0, #0x13]
    // 0x130ba1c: ldur            x1, [fp, #-0x18]
    // 0x130ba20: ArrayStore: r0[0] = r1  ; List_4
    //     0x130ba20: stur            w1, [x0, #0x17]
    // 0x130ba24: r1 = <Widget>
    //     0x130ba24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130ba28: r0 = AllocateGrowableArray()
    //     0x130ba28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130ba2c: mov             x1, x0
    // 0x130ba30: ldur            x0, [fp, #-0x20]
    // 0x130ba34: stur            x1, [fp, #-0x18]
    // 0x130ba38: StoreField: r1->field_f = r0
    //     0x130ba38: stur            w0, [x1, #0xf]
    // 0x130ba3c: r2 = 6
    //     0x130ba3c: movz            x2, #0x6
    // 0x130ba40: StoreField: r1->field_b = r2
    //     0x130ba40: stur            w2, [x1, #0xb]
    // 0x130ba44: r0 = Row()
    //     0x130ba44: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x130ba48: mov             x1, x0
    // 0x130ba4c: r0 = Instance_Axis
    //     0x130ba4c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x130ba50: stur            x1, [fp, #-0x20]
    // 0x130ba54: StoreField: r1->field_f = r0
    //     0x130ba54: stur            w0, [x1, #0xf]
    // 0x130ba58: r2 = Instance_MainAxisAlignment
    //     0x130ba58: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x130ba5c: ldr             x2, [x2, #0xa08]
    // 0x130ba60: StoreField: r1->field_13 = r2
    //     0x130ba60: stur            w2, [x1, #0x13]
    // 0x130ba64: r3 = Instance_MainAxisSize
    //     0x130ba64: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130ba68: ldr             x3, [x3, #0xa10]
    // 0x130ba6c: ArrayStore: r1[0] = r3  ; List_4
    //     0x130ba6c: stur            w3, [x1, #0x17]
    // 0x130ba70: r4 = Instance_CrossAxisAlignment
    //     0x130ba70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x130ba74: ldr             x4, [x4, #0xa18]
    // 0x130ba78: StoreField: r1->field_1b = r4
    //     0x130ba78: stur            w4, [x1, #0x1b]
    // 0x130ba7c: r4 = Instance_VerticalDirection
    //     0x130ba7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130ba80: ldr             x4, [x4, #0xa20]
    // 0x130ba84: StoreField: r1->field_23 = r4
    //     0x130ba84: stur            w4, [x1, #0x23]
    // 0x130ba88: r5 = Instance_Clip
    //     0x130ba88: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130ba8c: ldr             x5, [x5, #0x38]
    // 0x130ba90: StoreField: r1->field_2b = r5
    //     0x130ba90: stur            w5, [x1, #0x2b]
    // 0x130ba94: StoreField: r1->field_2f = rZR
    //     0x130ba94: stur            xzr, [x1, #0x2f]
    // 0x130ba98: ldur            x6, [fp, #-0x18]
    // 0x130ba9c: StoreField: r1->field_b = r6
    //     0x130ba9c: stur            w6, [x1, #0xb]
    // 0x130baa0: r0 = Padding()
    //     0x130baa0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130baa4: mov             x1, x0
    // 0x130baa8: r0 = Instance_EdgeInsets
    //     0x130baa8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130baac: ldr             x0, [x0, #0x980]
    // 0x130bab0: stur            x1, [fp, #-0x18]
    // 0x130bab4: StoreField: r1->field_f = r0
    //     0x130bab4: stur            w0, [x1, #0xf]
    // 0x130bab8: ldur            x2, [fp, #-0x20]
    // 0x130babc: StoreField: r1->field_b = r2
    //     0x130babc: stur            w2, [x1, #0xb]
    // 0x130bac0: r0 = Container()
    //     0x130bac0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x130bac4: stur            x0, [fp, #-0x20]
    // 0x130bac8: r16 = 80.000000
    //     0x130bac8: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x130bacc: ldr             x16, [x16, #0x2f8]
    // 0x130bad0: r30 = Instance_Alignment
    //     0x130bad0: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x130bad4: ldr             lr, [lr, #0xb10]
    // 0x130bad8: stp             lr, x16, [SP, #0x10]
    // 0x130badc: ldur            x16, [fp, #-0x10]
    // 0x130bae0: ldur            lr, [fp, #-0x18]
    // 0x130bae4: stp             lr, x16, [SP]
    // 0x130bae8: mov             x1, x0
    // 0x130baec: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, child, 0x4, color, 0x3, height, 0x1, null]
    //     0x130baec: add             x4, PP, #0x3b, lsl #12  ; [pp+0x3b018] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "child", 0x4, "color", 0x3, "height", 0x1, Null]
    //     0x130baf0: ldr             x4, [x4, #0x18]
    // 0x130baf4: r0 = Container()
    //     0x130baf4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x130baf8: ldur            x2, [fp, #-8]
    // 0x130bafc: LoadField: r1 = r2->field_13
    //     0x130bafc: ldur            w1, [x2, #0x13]
    // 0x130bb00: DecompressPointer r1
    //     0x130bb00: add             x1, x1, HEAP, lsl #32
    // 0x130bb04: r0 = of()
    //     0x130bb04: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130bb08: LoadField: r1 = r0->field_87
    //     0x130bb08: ldur            w1, [x0, #0x87]
    // 0x130bb0c: DecompressPointer r1
    //     0x130bb0c: add             x1, x1, HEAP, lsl #32
    // 0x130bb10: LoadField: r0 = r1->field_7
    //     0x130bb10: ldur            w0, [x1, #7]
    // 0x130bb14: DecompressPointer r0
    //     0x130bb14: add             x0, x0, HEAP, lsl #32
    // 0x130bb18: r16 = 14.000000
    //     0x130bb18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x130bb1c: ldr             x16, [x16, #0x1d8]
    // 0x130bb20: r30 = Instance_Color
    //     0x130bb20: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130bb24: stp             lr, x16, [SP]
    // 0x130bb28: mov             x1, x0
    // 0x130bb2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130bb2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130bb30: ldr             x4, [x4, #0xaa0]
    // 0x130bb34: r0 = copyWith()
    //     0x130bb34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130bb38: stur            x0, [fp, #-0x10]
    // 0x130bb3c: r0 = Text()
    //     0x130bb3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130bb40: mov             x2, x0
    // 0x130bb44: r0 = "Customisation fee"
    //     0x130bb44: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3b020] "Customisation fee"
    //     0x130bb48: ldr             x0, [x0, #0x20]
    // 0x130bb4c: stur            x2, [fp, #-0x18]
    // 0x130bb50: StoreField: r2->field_b = r0
    //     0x130bb50: stur            w0, [x2, #0xb]
    // 0x130bb54: ldur            x0, [fp, #-0x10]
    // 0x130bb58: StoreField: r2->field_13 = r0
    //     0x130bb58: stur            w0, [x2, #0x13]
    // 0x130bb5c: ldur            x0, [fp, #-8]
    // 0x130bb60: LoadField: r1 = r0->field_f
    //     0x130bb60: ldur            w1, [x0, #0xf]
    // 0x130bb64: DecompressPointer r1
    //     0x130bb64: add             x1, x1, HEAP, lsl #32
    // 0x130bb68: r0 = controller()
    //     0x130bb68: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bb6c: mov             x1, x0
    // 0x130bb70: r0 = getCustomisedPrice()
    //     0x130bb70: bl              #0x1306ca0  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::getCustomisedPrice
    // 0x130bb74: ldur            x2, [fp, #-8]
    // 0x130bb78: stur            x0, [fp, #-0x10]
    // 0x130bb7c: LoadField: r1 = r2->field_13
    //     0x130bb7c: ldur            w1, [x2, #0x13]
    // 0x130bb80: DecompressPointer r1
    //     0x130bb80: add             x1, x1, HEAP, lsl #32
    // 0x130bb84: r0 = of()
    //     0x130bb84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130bb88: LoadField: r1 = r0->field_87
    //     0x130bb88: ldur            w1, [x0, #0x87]
    // 0x130bb8c: DecompressPointer r1
    //     0x130bb8c: add             x1, x1, HEAP, lsl #32
    // 0x130bb90: LoadField: r0 = r1->field_7
    //     0x130bb90: ldur            w0, [x1, #7]
    // 0x130bb94: DecompressPointer r0
    //     0x130bb94: add             x0, x0, HEAP, lsl #32
    // 0x130bb98: r16 = 14.000000
    //     0x130bb98: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x130bb9c: ldr             x16, [x16, #0x1d8]
    // 0x130bba0: r30 = Instance_Color
    //     0x130bba0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130bba4: stp             lr, x16, [SP]
    // 0x130bba8: mov             x1, x0
    // 0x130bbac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130bbac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130bbb0: ldr             x4, [x4, #0xaa0]
    // 0x130bbb4: r0 = copyWith()
    //     0x130bbb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130bbb8: stur            x0, [fp, #-0x28]
    // 0x130bbbc: r0 = Text()
    //     0x130bbbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130bbc0: mov             x3, x0
    // 0x130bbc4: ldur            x0, [fp, #-0x10]
    // 0x130bbc8: stur            x3, [fp, #-0x30]
    // 0x130bbcc: StoreField: r3->field_b = r0
    //     0x130bbcc: stur            w0, [x3, #0xb]
    // 0x130bbd0: ldur            x0, [fp, #-0x28]
    // 0x130bbd4: StoreField: r3->field_13 = r0
    //     0x130bbd4: stur            w0, [x3, #0x13]
    // 0x130bbd8: r1 = Null
    //     0x130bbd8: mov             x1, NULL
    // 0x130bbdc: r2 = 6
    //     0x130bbdc: movz            x2, #0x6
    // 0x130bbe0: r0 = AllocateArray()
    //     0x130bbe0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130bbe4: mov             x2, x0
    // 0x130bbe8: ldur            x0, [fp, #-0x18]
    // 0x130bbec: stur            x2, [fp, #-0x10]
    // 0x130bbf0: StoreField: r2->field_f = r0
    //     0x130bbf0: stur            w0, [x2, #0xf]
    // 0x130bbf4: r16 = Instance_SizedBox
    //     0x130bbf4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x130bbf8: ldr             x16, [x16, #0x8b8]
    // 0x130bbfc: StoreField: r2->field_13 = r16
    //     0x130bbfc: stur            w16, [x2, #0x13]
    // 0x130bc00: ldur            x0, [fp, #-0x30]
    // 0x130bc04: ArrayStore: r2[0] = r0  ; List_4
    //     0x130bc04: stur            w0, [x2, #0x17]
    // 0x130bc08: r1 = <Widget>
    //     0x130bc08: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130bc0c: r0 = AllocateGrowableArray()
    //     0x130bc0c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130bc10: mov             x1, x0
    // 0x130bc14: ldur            x0, [fp, #-0x10]
    // 0x130bc18: stur            x1, [fp, #-0x18]
    // 0x130bc1c: StoreField: r1->field_f = r0
    //     0x130bc1c: stur            w0, [x1, #0xf]
    // 0x130bc20: r0 = 6
    //     0x130bc20: movz            x0, #0x6
    // 0x130bc24: StoreField: r1->field_b = r0
    //     0x130bc24: stur            w0, [x1, #0xb]
    // 0x130bc28: r0 = Column()
    //     0x130bc28: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x130bc2c: mov             x1, x0
    // 0x130bc30: r0 = Instance_Axis
    //     0x130bc30: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x130bc34: stur            x1, [fp, #-0x10]
    // 0x130bc38: StoreField: r1->field_f = r0
    //     0x130bc38: stur            w0, [x1, #0xf]
    // 0x130bc3c: r0 = Instance_MainAxisAlignment
    //     0x130bc3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x130bc40: ldr             x0, [x0, #0xa08]
    // 0x130bc44: StoreField: r1->field_13 = r0
    //     0x130bc44: stur            w0, [x1, #0x13]
    // 0x130bc48: r0 = Instance_MainAxisSize
    //     0x130bc48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130bc4c: ldr             x0, [x0, #0xa10]
    // 0x130bc50: ArrayStore: r1[0] = r0  ; List_4
    //     0x130bc50: stur            w0, [x1, #0x17]
    // 0x130bc54: r2 = Instance_CrossAxisAlignment
    //     0x130bc54: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x130bc58: ldr             x2, [x2, #0x890]
    // 0x130bc5c: StoreField: r1->field_1b = r2
    //     0x130bc5c: stur            w2, [x1, #0x1b]
    // 0x130bc60: r3 = Instance_VerticalDirection
    //     0x130bc60: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130bc64: ldr             x3, [x3, #0xa20]
    // 0x130bc68: StoreField: r1->field_23 = r3
    //     0x130bc68: stur            w3, [x1, #0x23]
    // 0x130bc6c: r4 = Instance_Clip
    //     0x130bc6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130bc70: ldr             x4, [x4, #0x38]
    // 0x130bc74: StoreField: r1->field_2b = r4
    //     0x130bc74: stur            w4, [x1, #0x2b]
    // 0x130bc78: StoreField: r1->field_2f = rZR
    //     0x130bc78: stur            xzr, [x1, #0x2f]
    // 0x130bc7c: ldur            x5, [fp, #-0x18]
    // 0x130bc80: StoreField: r1->field_b = r5
    //     0x130bc80: stur            w5, [x1, #0xb]
    // 0x130bc84: r0 = Padding()
    //     0x130bc84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130bc88: mov             x1, x0
    // 0x130bc8c: r0 = Instance_EdgeInsets
    //     0x130bc8c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130bc90: ldr             x0, [x0, #0x980]
    // 0x130bc94: stur            x1, [fp, #-0x18]
    // 0x130bc98: StoreField: r1->field_f = r0
    //     0x130bc98: stur            w0, [x1, #0xf]
    // 0x130bc9c: ldur            x2, [fp, #-0x10]
    // 0x130bca0: StoreField: r1->field_b = r2
    //     0x130bca0: stur            w2, [x1, #0xb]
    // 0x130bca4: r16 = <EdgeInsets>
    //     0x130bca4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x130bca8: ldr             x16, [x16, #0xda0]
    // 0x130bcac: r30 = Instance_EdgeInsets
    //     0x130bcac: add             lr, PP, #0x3b, lsl #12  ; [pp+0x3b028] Obj!EdgeInsets@d57fb1
    //     0x130bcb0: ldr             lr, [lr, #0x28]
    // 0x130bcb4: stp             lr, x16, [SP]
    // 0x130bcb8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130bcb8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130bcbc: r0 = all()
    //     0x130bcbc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130bcc0: ldur            x2, [fp, #-8]
    // 0x130bcc4: stur            x0, [fp, #-0x10]
    // 0x130bcc8: LoadField: r1 = r2->field_f
    //     0x130bcc8: ldur            w1, [x2, #0xf]
    // 0x130bccc: DecompressPointer r1
    //     0x130bccc: add             x1, x1, HEAP, lsl #32
    // 0x130bcd0: r0 = controller()
    //     0x130bcd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bcd4: LoadField: r1 = r0->field_77
    //     0x130bcd4: ldur            w1, [x0, #0x77]
    // 0x130bcd8: DecompressPointer r1
    //     0x130bcd8: add             x1, x1, HEAP, lsl #32
    // 0x130bcdc: r0 = value()
    //     0x130bcdc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130bce0: tbz             w0, #4, #0x130bd74
    // 0x130bce4: ldur            x2, [fp, #-8]
    // 0x130bce8: LoadField: r1 = r2->field_f
    //     0x130bce8: ldur            w1, [x2, #0xf]
    // 0x130bcec: DecompressPointer r1
    //     0x130bcec: add             x1, x1, HEAP, lsl #32
    // 0x130bcf0: r0 = controller()
    //     0x130bcf0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bcf4: LoadField: r1 = r0->field_7f
    //     0x130bcf4: ldur            w1, [x0, #0x7f]
    // 0x130bcf8: DecompressPointer r1
    //     0x130bcf8: add             x1, x1, HEAP, lsl #32
    // 0x130bcfc: r0 = value()
    //     0x130bcfc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130bd00: tbz             w0, #4, #0x130bd74
    // 0x130bd04: ldur            x2, [fp, #-8]
    // 0x130bd08: LoadField: r1 = r2->field_f
    //     0x130bd08: ldur            w1, [x2, #0xf]
    // 0x130bd0c: DecompressPointer r1
    //     0x130bd0c: add             x1, x1, HEAP, lsl #32
    // 0x130bd10: r0 = controller()
    //     0x130bd10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bd14: LoadField: r1 = r0->field_73
    //     0x130bd14: ldur            w1, [x0, #0x73]
    // 0x130bd18: DecompressPointer r1
    //     0x130bd18: add             x1, x1, HEAP, lsl #32
    // 0x130bd1c: r0 = value()
    //     0x130bd1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130bd20: tbz             w0, #4, #0x130bd74
    // 0x130bd24: ldur            x2, [fp, #-8]
    // 0x130bd28: LoadField: r1 = r2->field_f
    //     0x130bd28: ldur            w1, [x2, #0xf]
    // 0x130bd2c: DecompressPointer r1
    //     0x130bd2c: add             x1, x1, HEAP, lsl #32
    // 0x130bd30: r0 = getMandatory()
    //     0x130bd30: bl              #0x130c1e8  ; [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::getMandatory
    // 0x130bd34: tbz             w0, #4, #0x130bd74
    // 0x130bd38: ldur            x2, [fp, #-8]
    // 0x130bd3c: LoadField: r1 = r2->field_f
    //     0x130bd3c: ldur            w1, [x2, #0xf]
    // 0x130bd40: DecompressPointer r1
    //     0x130bd40: add             x1, x1, HEAP, lsl #32
    // 0x130bd44: r0 = controller()
    //     0x130bd44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bd48: LoadField: r1 = r0->field_6f
    //     0x130bd48: ldur            w1, [x0, #0x6f]
    // 0x130bd4c: DecompressPointer r1
    //     0x130bd4c: add             x1, x1, HEAP, lsl #32
    // 0x130bd50: r0 = value()
    //     0x130bd50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130bd54: tbz             w0, #4, #0x130bd74
    // 0x130bd58: ldur            x2, [fp, #-8]
    // 0x130bd5c: LoadField: r1 = r2->field_13
    //     0x130bd5c: ldur            w1, [x2, #0x13]
    // 0x130bd60: DecompressPointer r1
    //     0x130bd60: add             x1, x1, HEAP, lsl #32
    // 0x130bd64: r0 = of()
    //     0x130bd64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130bd68: LoadField: r1 = r0->field_5b
    //     0x130bd68: ldur            w1, [x0, #0x5b]
    // 0x130bd6c: DecompressPointer r1
    //     0x130bd6c: add             x1, x1, HEAP, lsl #32
    // 0x130bd70: b               #0x130bd84
    // 0x130bd74: r1 = Instance_Color
    //     0x130bd74: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130bd78: d0 = 0.100000
    //     0x130bd78: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x130bd7c: r0 = withOpacity()
    //     0x130bd7c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x130bd80: mov             x1, x0
    // 0x130bd84: ldur            x2, [fp, #-8]
    // 0x130bd88: ldur            x0, [fp, #-0x10]
    // 0x130bd8c: r16 = <Color>
    //     0x130bd8c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x130bd90: ldr             x16, [x16, #0xf80]
    // 0x130bd94: stp             x1, x16, [SP]
    // 0x130bd98: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130bd98: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130bd9c: r0 = all()
    //     0x130bd9c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130bda0: stur            x0, [fp, #-0x28]
    // 0x130bda4: r16 = <RoundedRectangleBorder>
    //     0x130bda4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x130bda8: ldr             x16, [x16, #0xf78]
    // 0x130bdac: r30 = Instance_RoundedRectangleBorder
    //     0x130bdac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x130bdb0: ldr             lr, [lr, #0xd68]
    // 0x130bdb4: stp             lr, x16, [SP]
    // 0x130bdb8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130bdb8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130bdbc: r0 = all()
    //     0x130bdbc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130bdc0: stur            x0, [fp, #-0x30]
    // 0x130bdc4: r0 = ButtonStyle()
    //     0x130bdc4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x130bdc8: mov             x1, x0
    // 0x130bdcc: ldur            x0, [fp, #-0x28]
    // 0x130bdd0: stur            x1, [fp, #-0x38]
    // 0x130bdd4: StoreField: r1->field_b = r0
    //     0x130bdd4: stur            w0, [x1, #0xb]
    // 0x130bdd8: ldur            x0, [fp, #-0x10]
    // 0x130bddc: StoreField: r1->field_23 = r0
    //     0x130bddc: stur            w0, [x1, #0x23]
    // 0x130bde0: ldur            x0, [fp, #-0x30]
    // 0x130bde4: StoreField: r1->field_43 = r0
    //     0x130bde4: stur            w0, [x1, #0x43]
    // 0x130bde8: r0 = TextButtonThemeData()
    //     0x130bde8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x130bdec: mov             x2, x0
    // 0x130bdf0: ldur            x0, [fp, #-0x38]
    // 0x130bdf4: stur            x2, [fp, #-0x10]
    // 0x130bdf8: StoreField: r2->field_7 = r0
    //     0x130bdf8: stur            w0, [x2, #7]
    // 0x130bdfc: ldur            x0, [fp, #-8]
    // 0x130be00: LoadField: r1 = r0->field_f
    //     0x130be00: ldur            w1, [x0, #0xf]
    // 0x130be04: DecompressPointer r1
    //     0x130be04: add             x1, x1, HEAP, lsl #32
    // 0x130be08: r0 = controller()
    //     0x130be08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130be0c: LoadField: r1 = r0->field_77
    //     0x130be0c: ldur            w1, [x0, #0x77]
    // 0x130be10: DecompressPointer r1
    //     0x130be10: add             x1, x1, HEAP, lsl #32
    // 0x130be14: r0 = value()
    //     0x130be14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130be18: tbz             w0, #4, #0x130bea8
    // 0x130be1c: ldur            x2, [fp, #-8]
    // 0x130be20: LoadField: r1 = r2->field_f
    //     0x130be20: ldur            w1, [x2, #0xf]
    // 0x130be24: DecompressPointer r1
    //     0x130be24: add             x1, x1, HEAP, lsl #32
    // 0x130be28: r0 = controller()
    //     0x130be28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130be2c: LoadField: r1 = r0->field_7f
    //     0x130be2c: ldur            w1, [x0, #0x7f]
    // 0x130be30: DecompressPointer r1
    //     0x130be30: add             x1, x1, HEAP, lsl #32
    // 0x130be34: r0 = value()
    //     0x130be34: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130be38: tbz             w0, #4, #0x130bea8
    // 0x130be3c: ldur            x2, [fp, #-8]
    // 0x130be40: LoadField: r1 = r2->field_f
    //     0x130be40: ldur            w1, [x2, #0xf]
    // 0x130be44: DecompressPointer r1
    //     0x130be44: add             x1, x1, HEAP, lsl #32
    // 0x130be48: r0 = controller()
    //     0x130be48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130be4c: LoadField: r1 = r0->field_73
    //     0x130be4c: ldur            w1, [x0, #0x73]
    // 0x130be50: DecompressPointer r1
    //     0x130be50: add             x1, x1, HEAP, lsl #32
    // 0x130be54: r0 = value()
    //     0x130be54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130be58: tbz             w0, #4, #0x130bea8
    // 0x130be5c: ldur            x2, [fp, #-8]
    // 0x130be60: LoadField: r1 = r2->field_f
    //     0x130be60: ldur            w1, [x2, #0xf]
    // 0x130be64: DecompressPointer r1
    //     0x130be64: add             x1, x1, HEAP, lsl #32
    // 0x130be68: r0 = getMandatory()
    //     0x130be68: bl              #0x130c1e8  ; [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::getMandatory
    // 0x130be6c: tbz             w0, #4, #0x130bea8
    // 0x130be70: ldur            x2, [fp, #-8]
    // 0x130be74: LoadField: r1 = r2->field_f
    //     0x130be74: ldur            w1, [x2, #0xf]
    // 0x130be78: DecompressPointer r1
    //     0x130be78: add             x1, x1, HEAP, lsl #32
    // 0x130be7c: r0 = controller()
    //     0x130be7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130be80: LoadField: r1 = r0->field_6f
    //     0x130be80: ldur            w1, [x0, #0x6f]
    // 0x130be84: DecompressPointer r1
    //     0x130be84: add             x1, x1, HEAP, lsl #32
    // 0x130be88: r0 = value()
    //     0x130be88: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130be8c: tbz             w0, #4, #0x130bea8
    // 0x130be90: ldur            x2, [fp, #-8]
    // 0x130be94: r1 = Function '<anonymous closure>':.
    //     0x130be94: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b030] AnonymousClosure: (0x130a820), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x130be98: ldr             x1, [x1, #0x30]
    // 0x130be9c: r0 = AllocateClosure()
    //     0x130be9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130bea0: mov             x2, x0
    // 0x130bea4: b               #0x130beac
    // 0x130bea8: r2 = Null
    //     0x130bea8: mov             x2, NULL
    // 0x130beac: ldur            x0, [fp, #-8]
    // 0x130beb0: stur            x2, [fp, #-0x28]
    // 0x130beb4: LoadField: r1 = r0->field_f
    //     0x130beb4: ldur            w1, [x0, #0xf]
    // 0x130beb8: DecompressPointer r1
    //     0x130beb8: add             x1, x1, HEAP, lsl #32
    // 0x130bebc: r0 = controller()
    //     0x130bebc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bec0: LoadField: r1 = r0->field_bb
    //     0x130bec0: ldur            w1, [x0, #0xbb]
    // 0x130bec4: DecompressPointer r1
    //     0x130bec4: add             x1, x1, HEAP, lsl #32
    // 0x130bec8: r0 = LoadClassIdInstr(r1)
    //     0x130bec8: ldur            x0, [x1, #-1]
    //     0x130becc: ubfx            x0, x0, #0xc, #0x14
    // 0x130bed0: r16 = "buyNow"
    //     0x130bed0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130bed4: ldr             x16, [x16, #0x358]
    // 0x130bed8: stp             x16, x1, [SP]
    // 0x130bedc: mov             lr, x0
    // 0x130bee0: ldr             lr, [x21, lr, lsl #3]
    // 0x130bee4: blr             lr
    // 0x130bee8: tbnz            w0, #4, #0x130bef8
    // 0x130beec: r2 = "BUY NOW"
    //     0x130beec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe58] "BUY NOW"
    //     0x130bef0: ldr             x2, [x2, #0xe58]
    // 0x130bef4: b               #0x130bf00
    // 0x130bef8: r2 = "ADD TO BAG"
    //     0x130bef8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd8] "ADD TO BAG"
    //     0x130befc: ldr             x2, [x2, #0xdd8]
    // 0x130bf00: ldur            x0, [fp, #-8]
    // 0x130bf04: stur            x2, [fp, #-0x30]
    // 0x130bf08: LoadField: r1 = r0->field_13
    //     0x130bf08: ldur            w1, [x0, #0x13]
    // 0x130bf0c: DecompressPointer r1
    //     0x130bf0c: add             x1, x1, HEAP, lsl #32
    // 0x130bf10: r0 = of()
    //     0x130bf10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130bf14: LoadField: r1 = r0->field_87
    //     0x130bf14: ldur            w1, [x0, #0x87]
    // 0x130bf18: DecompressPointer r1
    //     0x130bf18: add             x1, x1, HEAP, lsl #32
    // 0x130bf1c: LoadField: r0 = r1->field_7
    //     0x130bf1c: ldur            w0, [x1, #7]
    // 0x130bf20: DecompressPointer r0
    //     0x130bf20: add             x0, x0, HEAP, lsl #32
    // 0x130bf24: ldur            x2, [fp, #-8]
    // 0x130bf28: stur            x0, [fp, #-0x38]
    // 0x130bf2c: LoadField: r1 = r2->field_f
    //     0x130bf2c: ldur            w1, [x2, #0xf]
    // 0x130bf30: DecompressPointer r1
    //     0x130bf30: add             x1, x1, HEAP, lsl #32
    // 0x130bf34: r0 = controller()
    //     0x130bf34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bf38: mov             x1, x0
    // 0x130bf3c: r0 = bumperCouponData()
    //     0x130bf3c: bl              #0x9be348  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::bumperCouponData
    // 0x130bf40: tbz             w0, #4, #0x130bfb4
    // 0x130bf44: ldur            x0, [fp, #-8]
    // 0x130bf48: LoadField: r1 = r0->field_f
    //     0x130bf48: ldur            w1, [x0, #0xf]
    // 0x130bf4c: DecompressPointer r1
    //     0x130bf4c: add             x1, x1, HEAP, lsl #32
    // 0x130bf50: r0 = controller()
    //     0x130bf50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bf54: mov             x1, x0
    // 0x130bf58: r0 = couponType()
    //     0x130bf58: bl              #0x90da88  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::couponType
    // 0x130bf5c: tbz             w0, #4, #0x130bfb4
    // 0x130bf60: ldur            x0, [fp, #-8]
    // 0x130bf64: LoadField: r1 = r0->field_f
    //     0x130bf64: ldur            w1, [x0, #0xf]
    // 0x130bf68: DecompressPointer r1
    //     0x130bf68: add             x1, x1, HEAP, lsl #32
    // 0x130bf6c: r0 = controller()
    //     0x130bf6c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bf70: mov             x1, x0
    // 0x130bf74: r0 = configData()
    //     0x130bf74: bl              #0x8a3140  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::configData
    // 0x130bf78: tbz             w0, #4, #0x130bfb4
    // 0x130bf7c: ldur            x0, [fp, #-8]
    // 0x130bf80: LoadField: r1 = r0->field_f
    //     0x130bf80: ldur            w1, [x0, #0xf]
    // 0x130bf84: DecompressPointer r1
    //     0x130bf84: add             x1, x1, HEAP, lsl #32
    // 0x130bf88: r0 = getMandatory()
    //     0x130bf88: bl              #0x130c1e8  ; [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::getMandatory
    // 0x130bf8c: tbz             w0, #4, #0x130bfb4
    // 0x130bf90: ldur            x0, [fp, #-8]
    // 0x130bf94: LoadField: r1 = r0->field_f
    //     0x130bf94: ldur            w1, [x0, #0xf]
    // 0x130bf98: DecompressPointer r1
    //     0x130bf98: add             x1, x1, HEAP, lsl #32
    // 0x130bf9c: r0 = controller()
    //     0x130bf9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130bfa0: mov             x1, x0
    // 0x130bfa4: r0 = activeExchangeResponse()
    //     0x130bfa4: bl              #0x91ea4c  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::activeExchangeResponse
    // 0x130bfa8: tbz             w0, #4, #0x130bfb4
    // 0x130bfac: r1 = Instance_Color
    //     0x130bfac: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x130bfb0: b               #0x130bfc8
    // 0x130bfb4: r1 = Instance_MaterialColor
    //     0x130bfb4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x130bfb8: ldr             x1, [x1, #0xdc0]
    // 0x130bfbc: d0 = 0.600000
    //     0x130bfbc: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0x130bfc0: r0 = withOpacity()
    //     0x130bfc0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x130bfc4: mov             x1, x0
    // 0x130bfc8: ldur            x5, [fp, #-0x20]
    // 0x130bfcc: ldur            x4, [fp, #-0x18]
    // 0x130bfd0: ldur            x3, [fp, #-0x10]
    // 0x130bfd4: ldur            x2, [fp, #-0x28]
    // 0x130bfd8: ldur            x0, [fp, #-0x30]
    // 0x130bfdc: r16 = 14.000000
    //     0x130bfdc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x130bfe0: ldr             x16, [x16, #0x1d8]
    // 0x130bfe4: stp             x1, x16, [SP]
    // 0x130bfe8: ldur            x1, [fp, #-0x38]
    // 0x130bfec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130bfec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130bff0: ldr             x4, [x4, #0xaa0]
    // 0x130bff4: r0 = copyWith()
    //     0x130bff4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130bff8: stur            x0, [fp, #-8]
    // 0x130bffc: r0 = Text()
    //     0x130bffc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130c000: mov             x1, x0
    // 0x130c004: ldur            x0, [fp, #-0x30]
    // 0x130c008: stur            x1, [fp, #-0x38]
    // 0x130c00c: StoreField: r1->field_b = r0
    //     0x130c00c: stur            w0, [x1, #0xb]
    // 0x130c010: ldur            x0, [fp, #-8]
    // 0x130c014: StoreField: r1->field_13 = r0
    //     0x130c014: stur            w0, [x1, #0x13]
    // 0x130c018: r0 = TextButton()
    //     0x130c018: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x130c01c: mov             x1, x0
    // 0x130c020: ldur            x0, [fp, #-0x28]
    // 0x130c024: stur            x1, [fp, #-8]
    // 0x130c028: StoreField: r1->field_b = r0
    //     0x130c028: stur            w0, [x1, #0xb]
    // 0x130c02c: r0 = false
    //     0x130c02c: add             x0, NULL, #0x30  ; false
    // 0x130c030: StoreField: r1->field_27 = r0
    //     0x130c030: stur            w0, [x1, #0x27]
    // 0x130c034: r0 = true
    //     0x130c034: add             x0, NULL, #0x20  ; true
    // 0x130c038: StoreField: r1->field_2f = r0
    //     0x130c038: stur            w0, [x1, #0x2f]
    // 0x130c03c: ldur            x0, [fp, #-0x38]
    // 0x130c040: StoreField: r1->field_37 = r0
    //     0x130c040: stur            w0, [x1, #0x37]
    // 0x130c044: r0 = TextButtonTheme()
    //     0x130c044: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x130c048: mov             x1, x0
    // 0x130c04c: ldur            x0, [fp, #-0x10]
    // 0x130c050: stur            x1, [fp, #-0x28]
    // 0x130c054: StoreField: r1->field_f = r0
    //     0x130c054: stur            w0, [x1, #0xf]
    // 0x130c058: ldur            x0, [fp, #-8]
    // 0x130c05c: StoreField: r1->field_b = r0
    //     0x130c05c: stur            w0, [x1, #0xb]
    // 0x130c060: r0 = Padding()
    //     0x130c060: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130c064: mov             x3, x0
    // 0x130c068: r0 = Instance_EdgeInsets
    //     0x130c068: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130c06c: ldr             x0, [x0, #0x980]
    // 0x130c070: stur            x3, [fp, #-8]
    // 0x130c074: StoreField: r3->field_f = r0
    //     0x130c074: stur            w0, [x3, #0xf]
    // 0x130c078: ldur            x1, [fp, #-0x28]
    // 0x130c07c: StoreField: r3->field_b = r1
    //     0x130c07c: stur            w1, [x3, #0xb]
    // 0x130c080: r1 = Null
    //     0x130c080: mov             x1, NULL
    // 0x130c084: r2 = 4
    //     0x130c084: movz            x2, #0x4
    // 0x130c088: r0 = AllocateArray()
    //     0x130c088: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130c08c: mov             x2, x0
    // 0x130c090: ldur            x0, [fp, #-0x18]
    // 0x130c094: stur            x2, [fp, #-0x10]
    // 0x130c098: StoreField: r2->field_f = r0
    //     0x130c098: stur            w0, [x2, #0xf]
    // 0x130c09c: ldur            x0, [fp, #-8]
    // 0x130c0a0: StoreField: r2->field_13 = r0
    //     0x130c0a0: stur            w0, [x2, #0x13]
    // 0x130c0a4: r1 = <Widget>
    //     0x130c0a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130c0a8: r0 = AllocateGrowableArray()
    //     0x130c0a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130c0ac: mov             x1, x0
    // 0x130c0b0: ldur            x0, [fp, #-0x10]
    // 0x130c0b4: stur            x1, [fp, #-8]
    // 0x130c0b8: StoreField: r1->field_f = r0
    //     0x130c0b8: stur            w0, [x1, #0xf]
    // 0x130c0bc: r2 = 4
    //     0x130c0bc: movz            x2, #0x4
    // 0x130c0c0: StoreField: r1->field_b = r2
    //     0x130c0c0: stur            w2, [x1, #0xb]
    // 0x130c0c4: r0 = Row()
    //     0x130c0c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x130c0c8: mov             x3, x0
    // 0x130c0cc: r0 = Instance_Axis
    //     0x130c0cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x130c0d0: stur            x3, [fp, #-0x10]
    // 0x130c0d4: StoreField: r3->field_f = r0
    //     0x130c0d4: stur            w0, [x3, #0xf]
    // 0x130c0d8: r1 = Instance_MainAxisAlignment
    //     0x130c0d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x130c0dc: ldr             x1, [x1, #0xa8]
    // 0x130c0e0: StoreField: r3->field_13 = r1
    //     0x130c0e0: stur            w1, [x3, #0x13]
    // 0x130c0e4: r1 = Instance_MainAxisSize
    //     0x130c0e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130c0e8: ldr             x1, [x1, #0xa10]
    // 0x130c0ec: ArrayStore: r3[0] = r1  ; List_4
    //     0x130c0ec: stur            w1, [x3, #0x17]
    // 0x130c0f0: r1 = Instance_CrossAxisAlignment
    //     0x130c0f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x130c0f4: ldr             x1, [x1, #0x890]
    // 0x130c0f8: StoreField: r3->field_1b = r1
    //     0x130c0f8: stur            w1, [x3, #0x1b]
    // 0x130c0fc: r4 = Instance_VerticalDirection
    //     0x130c0fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130c100: ldr             x4, [x4, #0xa20]
    // 0x130c104: StoreField: r3->field_23 = r4
    //     0x130c104: stur            w4, [x3, #0x23]
    // 0x130c108: r5 = Instance_Clip
    //     0x130c108: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130c10c: ldr             x5, [x5, #0x38]
    // 0x130c110: StoreField: r3->field_2b = r5
    //     0x130c110: stur            w5, [x3, #0x2b]
    // 0x130c114: StoreField: r3->field_2f = rZR
    //     0x130c114: stur            xzr, [x3, #0x2f]
    // 0x130c118: ldur            x1, [fp, #-8]
    // 0x130c11c: StoreField: r3->field_b = r1
    //     0x130c11c: stur            w1, [x3, #0xb]
    // 0x130c120: r1 = Null
    //     0x130c120: mov             x1, NULL
    // 0x130c124: r2 = 4
    //     0x130c124: movz            x2, #0x4
    // 0x130c128: r0 = AllocateArray()
    //     0x130c128: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130c12c: mov             x2, x0
    // 0x130c130: ldur            x0, [fp, #-0x20]
    // 0x130c134: stur            x2, [fp, #-8]
    // 0x130c138: StoreField: r2->field_f = r0
    //     0x130c138: stur            w0, [x2, #0xf]
    // 0x130c13c: ldur            x0, [fp, #-0x10]
    // 0x130c140: StoreField: r2->field_13 = r0
    //     0x130c140: stur            w0, [x2, #0x13]
    // 0x130c144: r1 = <Widget>
    //     0x130c144: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130c148: r0 = AllocateGrowableArray()
    //     0x130c148: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130c14c: mov             x1, x0
    // 0x130c150: ldur            x0, [fp, #-8]
    // 0x130c154: stur            x1, [fp, #-0x10]
    // 0x130c158: StoreField: r1->field_f = r0
    //     0x130c158: stur            w0, [x1, #0xf]
    // 0x130c15c: r0 = 4
    //     0x130c15c: movz            x0, #0x4
    // 0x130c160: StoreField: r1->field_b = r0
    //     0x130c160: stur            w0, [x1, #0xb]
    // 0x130c164: r0 = Wrap()
    //     0x130c164: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x130c168: mov             x1, x0
    // 0x130c16c: r0 = Instance_Axis
    //     0x130c16c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x130c170: stur            x1, [fp, #-8]
    // 0x130c174: StoreField: r1->field_f = r0
    //     0x130c174: stur            w0, [x1, #0xf]
    // 0x130c178: r0 = Instance_WrapAlignment
    //     0x130c178: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x130c17c: ldr             x0, [x0, #0x6e8]
    // 0x130c180: StoreField: r1->field_13 = r0
    //     0x130c180: stur            w0, [x1, #0x13]
    // 0x130c184: ArrayStore: r1[0] = rZR  ; List_8
    //     0x130c184: stur            xzr, [x1, #0x17]
    // 0x130c188: StoreField: r1->field_1f = r0
    //     0x130c188: stur            w0, [x1, #0x1f]
    // 0x130c18c: StoreField: r1->field_23 = rZR
    //     0x130c18c: stur            xzr, [x1, #0x23]
    // 0x130c190: r0 = Instance_WrapCrossAlignment
    //     0x130c190: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x130c194: ldr             x0, [x0, #0x6f0]
    // 0x130c198: StoreField: r1->field_2b = r0
    //     0x130c198: stur            w0, [x1, #0x2b]
    // 0x130c19c: r0 = Instance_VerticalDirection
    //     0x130c19c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130c1a0: ldr             x0, [x0, #0xa20]
    // 0x130c1a4: StoreField: r1->field_33 = r0
    //     0x130c1a4: stur            w0, [x1, #0x33]
    // 0x130c1a8: r0 = Instance_Clip
    //     0x130c1a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130c1ac: ldr             x0, [x0, #0x38]
    // 0x130c1b0: StoreField: r1->field_37 = r0
    //     0x130c1b0: stur            w0, [x1, #0x37]
    // 0x130c1b4: ldur            x0, [fp, #-0x10]
    // 0x130c1b8: StoreField: r1->field_b = r0
    //     0x130c1b8: stur            w0, [x1, #0xb]
    // 0x130c1bc: r0 = Padding()
    //     0x130c1bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130c1c0: r1 = Instance_EdgeInsets
    //     0x130c1c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130c1c4: ldr             x1, [x1, #0x980]
    // 0x130c1c8: StoreField: r0->field_f = r1
    //     0x130c1c8: stur            w1, [x0, #0xf]
    // 0x130c1cc: ldur            x1, [fp, #-8]
    // 0x130c1d0: StoreField: r0->field_b = r1
    //     0x130c1d0: stur            w1, [x0, #0xb]
    // 0x130c1d4: LeaveFrame
    //     0x130c1d4: mov             SP, fp
    //     0x130c1d8: ldp             fp, lr, [SP], #0x10
    // 0x130c1dc: ret
    //     0x130c1dc: ret             
    // 0x130c1e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130c1e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130c1e4: b               #0x130b938
  }
  _ getMandatory(/* No info */) {
    // ** addr: 0x130c1e8, size: 0x6b8
    // 0x130c1e8: EnterFrame
    //     0x130c1e8: stp             fp, lr, [SP, #-0x10]!
    //     0x130c1ec: mov             fp, SP
    // 0x130c1f0: AllocStack(0x70)
    //     0x130c1f0: sub             SP, SP, #0x70
    // 0x130c1f4: SetupParameters(CustomizedPage this /* r1 => r0, fp-0x8 */)
    //     0x130c1f4: mov             x0, x1
    //     0x130c1f8: stur            x1, [fp, #-8]
    // 0x130c1fc: CheckStackOverflow
    //     0x130c1fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130c200: cmp             SP, x16
    //     0x130c204: b.ls            #0x130c88c
    // 0x130c208: mov             x1, x0
    // 0x130c20c: r0 = controller()
    //     0x130c20c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130c210: mov             x1, x0
    // 0x130c214: r0 = headerConfigData()
    //     0x130c214: bl              #0x8a3724  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::headerConfigData
    // 0x130c218: LoadField: r1 = r0->field_b
    //     0x130c218: ldur            w1, [x0, #0xb]
    // 0x130c21c: DecompressPointer r1
    //     0x130c21c: add             x1, x1, HEAP, lsl #32
    // 0x130c220: cmp             w1, NULL
    // 0x130c224: b.eq            #0x130c894
    // 0x130c228: LoadField: r3 = r1->field_f
    //     0x130c228: ldur            w3, [x1, #0xf]
    // 0x130c22c: DecompressPointer r3
    //     0x130c22c: add             x3, x3, HEAP, lsl #32
    // 0x130c230: mov             x0, x3
    // 0x130c234: stur            x3, [fp, #-0x10]
    // 0x130c238: r2 = Null
    //     0x130c238: mov             x2, NULL
    // 0x130c23c: r1 = Null
    //     0x130c23c: mov             x1, NULL
    // 0x130c240: r8 = Iterable
    //     0x130c240: ldr             x8, [PP, #0x1160]  ; [pp+0x1160] Type: Iterable
    // 0x130c244: r3 = Null
    //     0x130c244: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3b9e0] Null
    //     0x130c248: ldr             x3, [x3, #0x9e0]
    // 0x130c24c: r0 = Iterable()
    //     0x130c24c: bl              #0x6239fc  ; IsType_Iterable_Stub
    // 0x130c250: ldur            x1, [fp, #-0x10]
    // 0x130c254: LoadField: r2 = r1->field_7
    //     0x130c254: ldur            w2, [x1, #7]
    // 0x130c258: DecompressPointer r2
    //     0x130c258: add             x2, x2, HEAP, lsl #32
    // 0x130c25c: stur            x2, [fp, #-0x30]
    // 0x130c260: LoadField: r0 = r1->field_b
    //     0x130c260: ldur            w0, [x1, #0xb]
    // 0x130c264: r3 = LoadInt32Instr(r0)
    //     0x130c264: sbfx            x3, x0, #1, #0x1f
    // 0x130c268: ldur            x0, [fp, #-8]
    // 0x130c26c: stur            x3, [fp, #-0x28]
    // 0x130c270: LoadField: r4 = r0->field_b
    //     0x130c270: ldur            w4, [x0, #0xb]
    // 0x130c274: DecompressPointer r4
    //     0x130c274: add             x4, x4, HEAP, lsl #32
    // 0x130c278: stur            x4, [fp, #-0x20]
    // 0x130c27c: r6 = false
    //     0x130c27c: add             x6, NULL, #0x30  ; false
    // 0x130c280: r5 = 0
    //     0x130c280: movz            x5, #0
    // 0x130c284: stur            x6, [fp, #-8]
    // 0x130c288: stur            x5, [fp, #-0x18]
    // 0x130c28c: CheckStackOverflow
    //     0x130c28c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130c290: cmp             SP, x16
    //     0x130c294: b.ls            #0x130c898
    // 0x130c298: str             x1, [SP]
    // 0x130c29c: r0 = 92
    //     0x130c29c: movz            x0, #0x5c
    // 0x130c2a0: r0 = GDT[cid_x0 + 0xc898]()
    //     0x130c2a0: movz            x17, #0xc898
    //     0x130c2a4: add             lr, x0, x17
    //     0x130c2a8: ldr             lr, [x21, lr, lsl #3]
    //     0x130c2ac: blr             lr
    // 0x130c2b0: r1 = LoadInt32Instr(r0)
    //     0x130c2b0: sbfx            x1, x0, #1, #0x1f
    //     0x130c2b4: tbz             w0, #0, #0x130c2bc
    //     0x130c2b8: ldur            x1, [x0, #7]
    // 0x130c2bc: ldur            x3, [fp, #-0x28]
    // 0x130c2c0: cmp             x3, x1
    // 0x130c2c4: b.ne            #0x130c86c
    // 0x130c2c8: ldur            x4, [fp, #-0x18]
    // 0x130c2cc: cmp             x4, x1
    // 0x130c2d0: b.ge            #0x130c85c
    // 0x130c2d4: ldur            x1, [fp, #-0x10]
    // 0x130c2d8: mov             x2, x4
    // 0x130c2dc: r0 = 92
    //     0x130c2dc: movz            x0, #0x5c
    // 0x130c2e0: r0 = GDT[cid_x0 + 0x114ba]()
    //     0x130c2e0: movz            x17, #0x14ba
    //     0x130c2e4: movk            x17, #0x1, lsl #16
    //     0x130c2e8: add             lr, x0, x17
    //     0x130c2ec: ldr             lr, [x21, lr, lsl #3]
    //     0x130c2f0: blr             lr
    // 0x130c2f4: mov             x3, x0
    // 0x130c2f8: ldur            x0, [fp, #-0x18]
    // 0x130c2fc: stur            x3, [fp, #-0x40]
    // 0x130c300: add             x5, x0, #1
    // 0x130c304: stur            x5, [fp, #-0x38]
    // 0x130c308: cmp             w3, NULL
    // 0x130c30c: b.ne            #0x130c340
    // 0x130c310: mov             x0, x3
    // 0x130c314: ldur            x2, [fp, #-0x30]
    // 0x130c318: r1 = Null
    //     0x130c318: mov             x1, NULL
    // 0x130c31c: cmp             w2, NULL
    // 0x130c320: b.eq            #0x130c340
    // 0x130c324: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130c324: ldur            w4, [x2, #0x17]
    // 0x130c328: DecompressPointer r4
    //     0x130c328: add             x4, x4, HEAP, lsl #32
    // 0x130c32c: r8 = X0
    //     0x130c32c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130c330: LoadField: r9 = r4->field_7
    //     0x130c330: ldur            x9, [x4, #7]
    // 0x130c334: r3 = Null
    //     0x130c334: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3b9f0] Null
    //     0x130c338: ldr             x3, [x3, #0x9f0]
    // 0x130c33c: blr             x9
    // 0x130c340: ldur            x0, [fp, #-0x40]
    // 0x130c344: LoadField: r1 = r0->field_b
    //     0x130c344: ldur            w1, [x0, #0xb]
    // 0x130c348: DecompressPointer r1
    //     0x130c348: add             x1, x1, HEAP, lsl #32
    // 0x130c34c: r16 = Instance_CustomisationType
    //     0x130c34c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0x130c350: ldr             x16, [x16, #0x680]
    // 0x130c354: cmp             w1, w16
    // 0x130c358: b.ne            #0x130c44c
    // 0x130c35c: r1 = LoadStaticField(0xcb0)
    //     0x130c35c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130c360: ldr             x1, [x1, #0x1960]
    // 0x130c364: cmp             w1, NULL
    // 0x130c368: b.ne            #0x130c380
    // 0x130c36c: r1 = Instance_GetInstance
    //     0x130c36c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c370: ldr             x1, [x1, #0x900]
    // 0x130c374: StoreStaticField(0xcb0, r1)
    //     0x130c374: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130c378: str             x1, [x2, #0x1960]
    // 0x130c37c: b               #0x130c388
    // 0x130c380: r1 = Instance_GetInstance
    //     0x130c380: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c384: ldr             x1, [x1, #0x900]
    // 0x130c388: ldur            x16, [fp, #-0x20]
    // 0x130c38c: r30 = Instance_GetInstance
    //     0x130c38c: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c390: ldr             lr, [lr, #0x900]
    // 0x130c394: stp             lr, x16, [SP, #8]
    // 0x130c398: str             NULL, [SP]
    // 0x130c39c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130c39c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130c3a0: r0 = find()
    //     0x130c3a0: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130c3a4: LoadField: r2 = r0->field_cb
    //     0x130c3a4: ldur            w2, [x0, #0xcb]
    // 0x130c3a8: DecompressPointer r2
    //     0x130c3a8: add             x2, x2, HEAP, lsl #32
    // 0x130c3ac: ldur            x0, [fp, #-0x40]
    // 0x130c3b0: stur            x2, [fp, #-0x50]
    // 0x130c3b4: LoadField: r1 = r0->field_7
    //     0x130c3b4: ldur            w1, [x0, #7]
    // 0x130c3b8: DecompressPointer r1
    //     0x130c3b8: add             x1, x1, HEAP, lsl #32
    // 0x130c3bc: cmp             w1, NULL
    // 0x130c3c0: b.ne            #0x130c3cc
    // 0x130c3c4: r0 = ""
    //     0x130c3c4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130c3c8: b               #0x130c3d0
    // 0x130c3cc: mov             x0, x1
    // 0x130c3d0: mov             x1, x2
    // 0x130c3d4: stur            x0, [fp, #-0x48]
    // 0x130c3d8: r0 = value()
    //     0x130c3d8: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130c3dc: mov             x3, x0
    // 0x130c3e0: ldur            x0, [fp, #-0x50]
    // 0x130c3e4: stur            x3, [fp, #-0x58]
    // 0x130c3e8: LoadField: r2 = r0->field_7
    //     0x130c3e8: ldur            w2, [x0, #7]
    // 0x130c3ec: DecompressPointer r2
    //     0x130c3ec: add             x2, x2, HEAP, lsl #32
    // 0x130c3f0: ldur            x0, [fp, #-0x48]
    // 0x130c3f4: r1 = Null
    //     0x130c3f4: mov             x1, NULL
    // 0x130c3f8: cmp             w2, NULL
    // 0x130c3fc: b.eq            #0x130c41c
    // 0x130c400: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130c400: ldur            w4, [x2, #0x17]
    // 0x130c404: DecompressPointer r4
    //     0x130c404: add             x4, x4, HEAP, lsl #32
    // 0x130c408: r8 = X0
    //     0x130c408: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130c40c: LoadField: r9 = r4->field_7
    //     0x130c40c: ldur            x9, [x4, #7]
    // 0x130c410: r3 = Null
    //     0x130c410: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3ba00] Null
    //     0x130c414: ldr             x3, [x3, #0xa00]
    // 0x130c418: blr             x9
    // 0x130c41c: ldur            x1, [fp, #-0x58]
    // 0x130c420: r0 = LoadClassIdInstr(r1)
    //     0x130c420: ldur            x0, [x1, #-1]
    //     0x130c424: ubfx            x0, x0, #0xc, #0x14
    // 0x130c428: ldur            x2, [fp, #-0x48]
    // 0x130c42c: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130c42c: sub             lr, x0, #0xfe
    //     0x130c430: ldr             lr, [x21, lr, lsl #3]
    //     0x130c434: blr             lr
    // 0x130c438: cmp             w0, NULL
    // 0x130c43c: b.ne            #0x130c444
    // 0x130c440: r0 = false
    //     0x130c440: add             x0, NULL, #0x30  ; false
    // 0x130c444: mov             x6, x0
    // 0x130c448: b               #0x130c844
    // 0x130c44c: r16 = Instance_CustomisationType
    //     0x130c44c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0x130c450: ldr             x16, [x16, #0x690]
    // 0x130c454: cmp             w1, w16
    // 0x130c458: b.ne            #0x130c548
    // 0x130c45c: r1 = LoadStaticField(0xcb0)
    //     0x130c45c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130c460: ldr             x1, [x1, #0x1960]
    // 0x130c464: cmp             w1, NULL
    // 0x130c468: b.ne            #0x130c480
    // 0x130c46c: r1 = Instance_GetInstance
    //     0x130c46c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c470: ldr             x1, [x1, #0x900]
    // 0x130c474: StoreStaticField(0xcb0, r1)
    //     0x130c474: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130c478: str             x1, [x2, #0x1960]
    // 0x130c47c: b               #0x130c488
    // 0x130c480: r1 = Instance_GetInstance
    //     0x130c480: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c484: ldr             x1, [x1, #0x900]
    // 0x130c488: ldur            x16, [fp, #-0x20]
    // 0x130c48c: r30 = Instance_GetInstance
    //     0x130c48c: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c490: ldr             lr, [lr, #0x900]
    // 0x130c494: stp             lr, x16, [SP, #8]
    // 0x130c498: str             NULL, [SP]
    // 0x130c49c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130c49c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130c4a0: r0 = find()
    //     0x130c4a0: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130c4a4: LoadField: r2 = r0->field_cb
    //     0x130c4a4: ldur            w2, [x0, #0xcb]
    // 0x130c4a8: DecompressPointer r2
    //     0x130c4a8: add             x2, x2, HEAP, lsl #32
    // 0x130c4ac: ldur            x0, [fp, #-0x40]
    // 0x130c4b0: stur            x2, [fp, #-0x50]
    // 0x130c4b4: LoadField: r1 = r0->field_7
    //     0x130c4b4: ldur            w1, [x0, #7]
    // 0x130c4b8: DecompressPointer r1
    //     0x130c4b8: add             x1, x1, HEAP, lsl #32
    // 0x130c4bc: cmp             w1, NULL
    // 0x130c4c0: b.ne            #0x130c4cc
    // 0x130c4c4: r0 = ""
    //     0x130c4c4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130c4c8: b               #0x130c4d0
    // 0x130c4cc: mov             x0, x1
    // 0x130c4d0: mov             x1, x2
    // 0x130c4d4: stur            x0, [fp, #-0x48]
    // 0x130c4d8: r0 = value()
    //     0x130c4d8: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130c4dc: mov             x3, x0
    // 0x130c4e0: ldur            x0, [fp, #-0x50]
    // 0x130c4e4: stur            x3, [fp, #-0x58]
    // 0x130c4e8: LoadField: r2 = r0->field_7
    //     0x130c4e8: ldur            w2, [x0, #7]
    // 0x130c4ec: DecompressPointer r2
    //     0x130c4ec: add             x2, x2, HEAP, lsl #32
    // 0x130c4f0: ldur            x0, [fp, #-0x48]
    // 0x130c4f4: r1 = Null
    //     0x130c4f4: mov             x1, NULL
    // 0x130c4f8: cmp             w2, NULL
    // 0x130c4fc: b.eq            #0x130c51c
    // 0x130c500: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130c500: ldur            w4, [x2, #0x17]
    // 0x130c504: DecompressPointer r4
    //     0x130c504: add             x4, x4, HEAP, lsl #32
    // 0x130c508: r8 = X0
    //     0x130c508: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130c50c: LoadField: r9 = r4->field_7
    //     0x130c50c: ldur            x9, [x4, #7]
    // 0x130c510: r3 = Null
    //     0x130c510: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3ba10] Null
    //     0x130c514: ldr             x3, [x3, #0xa10]
    // 0x130c518: blr             x9
    // 0x130c51c: ldur            x1, [fp, #-0x58]
    // 0x130c520: r0 = LoadClassIdInstr(r1)
    //     0x130c520: ldur            x0, [x1, #-1]
    //     0x130c524: ubfx            x0, x0, #0xc, #0x14
    // 0x130c528: ldur            x2, [fp, #-0x48]
    // 0x130c52c: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130c52c: sub             lr, x0, #0xfe
    //     0x130c530: ldr             lr, [x21, lr, lsl #3]
    //     0x130c534: blr             lr
    // 0x130c538: cmp             w0, NULL
    // 0x130c53c: b.ne            #0x130c840
    // 0x130c540: r0 = false
    //     0x130c540: add             x0, NULL, #0x30  ; false
    // 0x130c544: b               #0x130c840
    // 0x130c548: r16 = Instance_CustomisationType
    //     0x130c548: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0x130c54c: ldr             x16, [x16, #0x660]
    // 0x130c550: cmp             w1, w16
    // 0x130c554: b.ne            #0x130c644
    // 0x130c558: r1 = LoadStaticField(0xcb0)
    //     0x130c558: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130c55c: ldr             x1, [x1, #0x1960]
    // 0x130c560: cmp             w1, NULL
    // 0x130c564: b.ne            #0x130c57c
    // 0x130c568: r1 = Instance_GetInstance
    //     0x130c568: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c56c: ldr             x1, [x1, #0x900]
    // 0x130c570: StoreStaticField(0xcb0, r1)
    //     0x130c570: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130c574: str             x1, [x2, #0x1960]
    // 0x130c578: b               #0x130c584
    // 0x130c57c: r1 = Instance_GetInstance
    //     0x130c57c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c580: ldr             x1, [x1, #0x900]
    // 0x130c584: ldur            x16, [fp, #-0x20]
    // 0x130c588: r30 = Instance_GetInstance
    //     0x130c588: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c58c: ldr             lr, [lr, #0x900]
    // 0x130c590: stp             lr, x16, [SP, #8]
    // 0x130c594: str             NULL, [SP]
    // 0x130c598: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130c598: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130c59c: r0 = find()
    //     0x130c59c: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130c5a0: LoadField: r2 = r0->field_cb
    //     0x130c5a0: ldur            w2, [x0, #0xcb]
    // 0x130c5a4: DecompressPointer r2
    //     0x130c5a4: add             x2, x2, HEAP, lsl #32
    // 0x130c5a8: ldur            x0, [fp, #-0x40]
    // 0x130c5ac: stur            x2, [fp, #-0x50]
    // 0x130c5b0: LoadField: r1 = r0->field_7
    //     0x130c5b0: ldur            w1, [x0, #7]
    // 0x130c5b4: DecompressPointer r1
    //     0x130c5b4: add             x1, x1, HEAP, lsl #32
    // 0x130c5b8: cmp             w1, NULL
    // 0x130c5bc: b.ne            #0x130c5c8
    // 0x130c5c0: r0 = ""
    //     0x130c5c0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130c5c4: b               #0x130c5cc
    // 0x130c5c8: mov             x0, x1
    // 0x130c5cc: mov             x1, x2
    // 0x130c5d0: stur            x0, [fp, #-0x48]
    // 0x130c5d4: r0 = value()
    //     0x130c5d4: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130c5d8: mov             x3, x0
    // 0x130c5dc: ldur            x0, [fp, #-0x50]
    // 0x130c5e0: stur            x3, [fp, #-0x58]
    // 0x130c5e4: LoadField: r2 = r0->field_7
    //     0x130c5e4: ldur            w2, [x0, #7]
    // 0x130c5e8: DecompressPointer r2
    //     0x130c5e8: add             x2, x2, HEAP, lsl #32
    // 0x130c5ec: ldur            x0, [fp, #-0x48]
    // 0x130c5f0: r1 = Null
    //     0x130c5f0: mov             x1, NULL
    // 0x130c5f4: cmp             w2, NULL
    // 0x130c5f8: b.eq            #0x130c618
    // 0x130c5fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130c5fc: ldur            w4, [x2, #0x17]
    // 0x130c600: DecompressPointer r4
    //     0x130c600: add             x4, x4, HEAP, lsl #32
    // 0x130c604: r8 = X0
    //     0x130c604: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130c608: LoadField: r9 = r4->field_7
    //     0x130c608: ldur            x9, [x4, #7]
    // 0x130c60c: r3 = Null
    //     0x130c60c: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3ba20] Null
    //     0x130c610: ldr             x3, [x3, #0xa20]
    // 0x130c614: blr             x9
    // 0x130c618: ldur            x1, [fp, #-0x58]
    // 0x130c61c: r0 = LoadClassIdInstr(r1)
    //     0x130c61c: ldur            x0, [x1, #-1]
    //     0x130c620: ubfx            x0, x0, #0xc, #0x14
    // 0x130c624: ldur            x2, [fp, #-0x48]
    // 0x130c628: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130c628: sub             lr, x0, #0xfe
    //     0x130c62c: ldr             lr, [x21, lr, lsl #3]
    //     0x130c630: blr             lr
    // 0x130c634: cmp             w0, NULL
    // 0x130c638: b.ne            #0x130c840
    // 0x130c63c: r0 = false
    //     0x130c63c: add             x0, NULL, #0x30  ; false
    // 0x130c640: b               #0x130c840
    // 0x130c644: r16 = Instance_CustomisationType
    //     0x130c644: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0x130c648: ldr             x16, [x16, #0x670]
    // 0x130c64c: cmp             w1, w16
    // 0x130c650: b.ne            #0x130c740
    // 0x130c654: r1 = LoadStaticField(0xcb0)
    //     0x130c654: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130c658: ldr             x1, [x1, #0x1960]
    // 0x130c65c: cmp             w1, NULL
    // 0x130c660: b.ne            #0x130c678
    // 0x130c664: r1 = Instance_GetInstance
    //     0x130c664: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c668: ldr             x1, [x1, #0x900]
    // 0x130c66c: StoreStaticField(0xcb0, r1)
    //     0x130c66c: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130c670: str             x1, [x2, #0x1960]
    // 0x130c674: b               #0x130c680
    // 0x130c678: r1 = Instance_GetInstance
    //     0x130c678: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c67c: ldr             x1, [x1, #0x900]
    // 0x130c680: ldur            x16, [fp, #-0x20]
    // 0x130c684: r30 = Instance_GetInstance
    //     0x130c684: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c688: ldr             lr, [lr, #0x900]
    // 0x130c68c: stp             lr, x16, [SP, #8]
    // 0x130c690: str             NULL, [SP]
    // 0x130c694: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130c694: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130c698: r0 = find()
    //     0x130c698: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130c69c: LoadField: r2 = r0->field_cb
    //     0x130c69c: ldur            w2, [x0, #0xcb]
    // 0x130c6a0: DecompressPointer r2
    //     0x130c6a0: add             x2, x2, HEAP, lsl #32
    // 0x130c6a4: ldur            x0, [fp, #-0x40]
    // 0x130c6a8: stur            x2, [fp, #-0x50]
    // 0x130c6ac: LoadField: r1 = r0->field_7
    //     0x130c6ac: ldur            w1, [x0, #7]
    // 0x130c6b0: DecompressPointer r1
    //     0x130c6b0: add             x1, x1, HEAP, lsl #32
    // 0x130c6b4: cmp             w1, NULL
    // 0x130c6b8: b.ne            #0x130c6c4
    // 0x130c6bc: r0 = ""
    //     0x130c6bc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130c6c0: b               #0x130c6c8
    // 0x130c6c4: mov             x0, x1
    // 0x130c6c8: mov             x1, x2
    // 0x130c6cc: stur            x0, [fp, #-0x48]
    // 0x130c6d0: r0 = value()
    //     0x130c6d0: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130c6d4: mov             x3, x0
    // 0x130c6d8: ldur            x0, [fp, #-0x50]
    // 0x130c6dc: stur            x3, [fp, #-0x58]
    // 0x130c6e0: LoadField: r2 = r0->field_7
    //     0x130c6e0: ldur            w2, [x0, #7]
    // 0x130c6e4: DecompressPointer r2
    //     0x130c6e4: add             x2, x2, HEAP, lsl #32
    // 0x130c6e8: ldur            x0, [fp, #-0x48]
    // 0x130c6ec: r1 = Null
    //     0x130c6ec: mov             x1, NULL
    // 0x130c6f0: cmp             w2, NULL
    // 0x130c6f4: b.eq            #0x130c714
    // 0x130c6f8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130c6f8: ldur            w4, [x2, #0x17]
    // 0x130c6fc: DecompressPointer r4
    //     0x130c6fc: add             x4, x4, HEAP, lsl #32
    // 0x130c700: r8 = X0
    //     0x130c700: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130c704: LoadField: r9 = r4->field_7
    //     0x130c704: ldur            x9, [x4, #7]
    // 0x130c708: r3 = Null
    //     0x130c708: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3ba30] Null
    //     0x130c70c: ldr             x3, [x3, #0xa30]
    // 0x130c710: blr             x9
    // 0x130c714: ldur            x1, [fp, #-0x58]
    // 0x130c718: r0 = LoadClassIdInstr(r1)
    //     0x130c718: ldur            x0, [x1, #-1]
    //     0x130c71c: ubfx            x0, x0, #0xc, #0x14
    // 0x130c720: ldur            x2, [fp, #-0x48]
    // 0x130c724: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130c724: sub             lr, x0, #0xfe
    //     0x130c728: ldr             lr, [x21, lr, lsl #3]
    //     0x130c72c: blr             lr
    // 0x130c730: cmp             w0, NULL
    // 0x130c734: b.ne            #0x130c840
    // 0x130c738: r0 = false
    //     0x130c738: add             x0, NULL, #0x30  ; false
    // 0x130c73c: b               #0x130c840
    // 0x130c740: r16 = Instance_CustomisationType
    //     0x130c740: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0x130c744: ldr             x16, [x16, #0x650]
    // 0x130c748: cmp             w1, w16
    // 0x130c74c: b.ne            #0x130c83c
    // 0x130c750: r1 = LoadStaticField(0xcb0)
    //     0x130c750: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130c754: ldr             x1, [x1, #0x1960]
    // 0x130c758: cmp             w1, NULL
    // 0x130c75c: b.ne            #0x130c774
    // 0x130c760: r1 = Instance_GetInstance
    //     0x130c760: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c764: ldr             x1, [x1, #0x900]
    // 0x130c768: StoreStaticField(0xcb0, r1)
    //     0x130c768: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130c76c: str             x1, [x2, #0x1960]
    // 0x130c770: b               #0x130c77c
    // 0x130c774: r1 = Instance_GetInstance
    //     0x130c774: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c778: ldr             x1, [x1, #0x900]
    // 0x130c77c: ldur            x16, [fp, #-0x20]
    // 0x130c780: r30 = Instance_GetInstance
    //     0x130c780: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130c784: ldr             lr, [lr, #0x900]
    // 0x130c788: stp             lr, x16, [SP, #8]
    // 0x130c78c: str             NULL, [SP]
    // 0x130c790: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130c790: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130c794: r0 = find()
    //     0x130c794: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130c798: LoadField: r2 = r0->field_cb
    //     0x130c798: ldur            w2, [x0, #0xcb]
    // 0x130c79c: DecompressPointer r2
    //     0x130c79c: add             x2, x2, HEAP, lsl #32
    // 0x130c7a0: ldur            x0, [fp, #-0x40]
    // 0x130c7a4: stur            x2, [fp, #-0x48]
    // 0x130c7a8: LoadField: r1 = r0->field_7
    //     0x130c7a8: ldur            w1, [x0, #7]
    // 0x130c7ac: DecompressPointer r1
    //     0x130c7ac: add             x1, x1, HEAP, lsl #32
    // 0x130c7b0: cmp             w1, NULL
    // 0x130c7b4: b.ne            #0x130c7c0
    // 0x130c7b8: r0 = ""
    //     0x130c7b8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130c7bc: b               #0x130c7c4
    // 0x130c7c0: mov             x0, x1
    // 0x130c7c4: mov             x1, x2
    // 0x130c7c8: stur            x0, [fp, #-0x40]
    // 0x130c7cc: r0 = value()
    //     0x130c7cc: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130c7d0: mov             x3, x0
    // 0x130c7d4: ldur            x0, [fp, #-0x48]
    // 0x130c7d8: stur            x3, [fp, #-0x50]
    // 0x130c7dc: LoadField: r2 = r0->field_7
    //     0x130c7dc: ldur            w2, [x0, #7]
    // 0x130c7e0: DecompressPointer r2
    //     0x130c7e0: add             x2, x2, HEAP, lsl #32
    // 0x130c7e4: ldur            x0, [fp, #-0x40]
    // 0x130c7e8: r1 = Null
    //     0x130c7e8: mov             x1, NULL
    // 0x130c7ec: cmp             w2, NULL
    // 0x130c7f0: b.eq            #0x130c810
    // 0x130c7f4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130c7f4: ldur            w4, [x2, #0x17]
    // 0x130c7f8: DecompressPointer r4
    //     0x130c7f8: add             x4, x4, HEAP, lsl #32
    // 0x130c7fc: r8 = X0
    //     0x130c7fc: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130c800: LoadField: r9 = r4->field_7
    //     0x130c800: ldur            x9, [x4, #7]
    // 0x130c804: r3 = Null
    //     0x130c804: add             x3, PP, #0x3b, lsl #12  ; [pp+0x3ba40] Null
    //     0x130c808: ldr             x3, [x3, #0xa40]
    // 0x130c80c: blr             x9
    // 0x130c810: ldur            x1, [fp, #-0x50]
    // 0x130c814: r0 = LoadClassIdInstr(r1)
    //     0x130c814: ldur            x0, [x1, #-1]
    //     0x130c818: ubfx            x0, x0, #0xc, #0x14
    // 0x130c81c: ldur            x2, [fp, #-0x40]
    // 0x130c820: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130c820: sub             lr, x0, #0xfe
    //     0x130c824: ldr             lr, [x21, lr, lsl #3]
    //     0x130c828: blr             lr
    // 0x130c82c: cmp             w0, NULL
    // 0x130c830: b.ne            #0x130c840
    // 0x130c834: r0 = false
    //     0x130c834: add             x0, NULL, #0x30  ; false
    // 0x130c838: b               #0x130c840
    // 0x130c83c: ldur            x0, [fp, #-8]
    // 0x130c840: mov             x6, x0
    // 0x130c844: ldur            x5, [fp, #-0x38]
    // 0x130c848: ldur            x4, [fp, #-0x20]
    // 0x130c84c: ldur            x1, [fp, #-0x10]
    // 0x130c850: ldur            x2, [fp, #-0x30]
    // 0x130c854: ldur            x3, [fp, #-0x28]
    // 0x130c858: b               #0x130c284
    // 0x130c85c: ldur            x0, [fp, #-8]
    // 0x130c860: LeaveFrame
    //     0x130c860: mov             SP, fp
    //     0x130c864: ldp             fp, lr, [SP], #0x10
    // 0x130c868: ret
    //     0x130c868: ret             
    // 0x130c86c: ldur            x0, [fp, #-0x10]
    // 0x130c870: r0 = ConcurrentModificationError()
    //     0x130c870: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x130c874: mov             x1, x0
    // 0x130c878: ldur            x0, [fp, #-0x10]
    // 0x130c87c: StoreField: r1->field_b = r0
    //     0x130c87c: stur            w0, [x1, #0xb]
    // 0x130c880: mov             x0, x1
    // 0x130c884: r0 = Throw()
    //     0x130c884: bl              #0x16f5420  ; ThrowStub
    // 0x130c888: brk             #0
    // 0x130c88c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130c88c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130c890: b               #0x130c208
    // 0x130c894: r0 = NullErrorSharedWithoutFPURegs()
    //     0x130c894: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x130c898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130c898: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130c89c: b               #0x130c298
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1367b8c, size: 0x64
    // 0x1367b8c: EnterFrame
    //     0x1367b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x1367b90: mov             fp, SP
    // 0x1367b94: AllocStack(0x18)
    //     0x1367b94: sub             SP, SP, #0x18
    // 0x1367b98: SetupParameters(CustomizedPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1367b98: stur            x1, [fp, #-8]
    //     0x1367b9c: stur            x2, [fp, #-0x10]
    // 0x1367ba0: r1 = 2
    //     0x1367ba0: movz            x1, #0x2
    // 0x1367ba4: r0 = AllocateContext()
    //     0x1367ba4: bl              #0x16f6108  ; AllocateContextStub
    // 0x1367ba8: mov             x1, x0
    // 0x1367bac: ldur            x0, [fp, #-8]
    // 0x1367bb0: stur            x1, [fp, #-0x18]
    // 0x1367bb4: StoreField: r1->field_f = r0
    //     0x1367bb4: stur            w0, [x1, #0xf]
    // 0x1367bb8: ldur            x0, [fp, #-0x10]
    // 0x1367bbc: StoreField: r1->field_13 = r0
    //     0x1367bbc: stur            w0, [x1, #0x13]
    // 0x1367bc0: r0 = Obx()
    //     0x1367bc0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1367bc4: ldur            x2, [fp, #-0x18]
    // 0x1367bc8: r1 = Function '<anonymous closure>':.
    //     0x1367bc8: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b000] AnonymousClosure: (0x130b910), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x1367bcc: ldr             x1, [x1]
    // 0x1367bd0: stur            x0, [fp, #-8]
    // 0x1367bd4: r0 = AllocateClosure()
    //     0x1367bd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1367bd8: mov             x1, x0
    // 0x1367bdc: ldur            x0, [fp, #-8]
    // 0x1367be0: StoreField: r0->field_b = r1
    //     0x1367be0: stur            w1, [x0, #0xb]
    // 0x1367be4: LeaveFrame
    //     0x1367be4: mov             SP, fp
    //     0x1367be8: ldp             fp, lr, [SP], #0x10
    // 0x1367bec: ret
    //     0x1367bec: ret             
  }
  [closure] Null <anonymous closure>(dynamic, int, String, List<ProductCustomisation>) {
    // ** addr: 0x13bdd60, size: 0xa4
    // 0x13bdd60: EnterFrame
    //     0x13bdd60: stp             fp, lr, [SP, #-0x10]!
    //     0x13bdd64: mov             fp, SP
    // 0x13bdd68: AllocStack(0x8)
    //     0x13bdd68: sub             SP, SP, #8
    // 0x13bdd6c: SetupParameters()
    //     0x13bdd6c: ldr             x0, [fp, #0x28]
    //     0x13bdd70: ldur            w2, [x0, #0x17]
    //     0x13bdd74: add             x2, x2, HEAP, lsl #32
    //     0x13bdd78: stur            x2, [fp, #-8]
    // 0x13bdd7c: CheckStackOverflow
    //     0x13bdd7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bdd80: cmp             SP, x16
    //     0x13bdd84: b.ls            #0x13bddfc
    // 0x13bdd88: LoadField: r1 = r2->field_f
    //     0x13bdd88: ldur            w1, [x2, #0xf]
    // 0x13bdd8c: DecompressPointer r1
    //     0x13bdd8c: add             x1, x1, HEAP, lsl #32
    // 0x13bdd90: r0 = controller()
    //     0x13bdd90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bdd94: mov             x1, x0
    // 0x13bdd98: ldr             x0, [fp, #0x20]
    // 0x13bdd9c: r2 = LoadInt32Instr(r0)
    //     0x13bdd9c: sbfx            x2, x0, #1, #0x1f
    //     0x13bdda0: tbz             w0, #0, #0x13bdda8
    //     0x13bdda4: ldur            x2, [x0, #7]
    // 0x13bdda8: r0 = priceIncrement()
    //     0x13bdda8: bl              #0x13bf240  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceIncrement
    // 0x13bddac: ldur            x0, [fp, #-8]
    // 0x13bddb0: LoadField: r1 = r0->field_f
    //     0x13bddb0: ldur            w1, [x0, #0xf]
    // 0x13bddb4: DecompressPointer r1
    //     0x13bddb4: add             x1, x1, HEAP, lsl #32
    // 0x13bddb8: r0 = controller()
    //     0x13bddb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bddbc: LoadField: r1 = r0->field_cb
    //     0x13bddbc: ldur            w1, [x0, #0xcb]
    // 0x13bddc0: DecompressPointer r1
    //     0x13bddc0: add             x1, x1, HEAP, lsl #32
    // 0x13bddc4: ldr             x2, [fp, #0x18]
    // 0x13bddc8: r3 = false
    //     0x13bddc8: add             x3, NULL, #0x30  ; false
    // 0x13bddcc: r0 = []=()
    //     0x13bddcc: bl              #0x164c2a0  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::[]=
    // 0x13bddd0: ldur            x0, [fp, #-8]
    // 0x13bddd4: LoadField: r1 = r0->field_f
    //     0x13bddd4: ldur            w1, [x0, #0xf]
    // 0x13bddd8: DecompressPointer r1
    //     0x13bddd8: add             x1, x1, HEAP, lsl #32
    // 0x13bdddc: r0 = controller()
    //     0x13bdddc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bdde0: mov             x1, x0
    // 0x13bdde4: ldr             x2, [fp, #0x10]
    // 0x13bdde8: r0 = setProductCustomisationList()
    //     0x13bdde8: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bddec: r0 = Null
    //     0x13bddec: mov             x0, NULL
    // 0x13bddf0: LeaveFrame
    //     0x13bddf0: mov             SP, fp
    //     0x13bddf4: ldp             fp, lr, [SP], #0x10
    // 0x13bddf8: ret
    //     0x13bddf8: ret             
    // 0x13bddfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bddfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bde00: b               #0x13bdd88
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x13bde04, size: 0x254
    // 0x13bde04: EnterFrame
    //     0x13bde04: stp             fp, lr, [SP, #-0x10]!
    //     0x13bde08: mov             fp, SP
    // 0x13bde0c: AllocStack(0x20)
    //     0x13bde0c: sub             SP, SP, #0x20
    // 0x13bde10: SetupParameters()
    //     0x13bde10: ldr             x0, [fp, #0x10]
    //     0x13bde14: ldur            w2, [x0, #0x17]
    //     0x13bde18: add             x2, x2, HEAP, lsl #32
    //     0x13bde1c: stur            x2, [fp, #-8]
    // 0x13bde20: CheckStackOverflow
    //     0x13bde20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bde24: cmp             SP, x16
    //     0x13bde28: b.ls            #0x13be050
    // 0x13bde2c: LoadField: r1 = r2->field_f
    //     0x13bde2c: ldur            w1, [x2, #0xf]
    // 0x13bde30: DecompressPointer r1
    //     0x13bde30: add             x1, x1, HEAP, lsl #32
    // 0x13bde34: r0 = controller()
    //     0x13bde34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bde38: LoadField: r1 = r0->field_53
    //     0x13bde38: ldur            w1, [x0, #0x53]
    // 0x13bde3c: DecompressPointer r1
    //     0x13bde3c: add             x1, x1, HEAP, lsl #32
    // 0x13bde40: r0 = value()
    //     0x13bde40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bde44: ldur            x2, [fp, #-8]
    // 0x13bde48: stur            x0, [fp, #-0x10]
    // 0x13bde4c: LoadField: r1 = r2->field_f
    //     0x13bde4c: ldur            w1, [x2, #0xf]
    // 0x13bde50: DecompressPointer r1
    //     0x13bde50: add             x1, x1, HEAP, lsl #32
    // 0x13bde54: r0 = controller()
    //     0x13bde54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bde58: LoadField: r1 = r0->field_9b
    //     0x13bde58: ldur            w1, [x0, #0x9b]
    // 0x13bde5c: DecompressPointer r1
    //     0x13bde5c: add             x1, x1, HEAP, lsl #32
    // 0x13bde60: stur            x1, [fp, #-0x18]
    // 0x13bde64: r0 = CustomizedView()
    //     0x13bde64: bl              #0x13be058  ; AllocateCustomizedViewStub -> CustomizedView (size=0x4c)
    // 0x13bde68: mov             x3, x0
    // 0x13bde6c: ldur            x0, [fp, #-0x10]
    // 0x13bde70: stur            x3, [fp, #-0x20]
    // 0x13bde74: StoreField: r3->field_b = r0
    //     0x13bde74: stur            w0, [x3, #0xb]
    // 0x13bde78: ldur            x0, [fp, #-0x18]
    // 0x13bde7c: StoreField: r3->field_f = r0
    //     0x13bde7c: stur            w0, [x3, #0xf]
    // 0x13bde80: ldur            x2, [fp, #-8]
    // 0x13bde84: r1 = Function '<anonymous closure>':.
    //     0x13bde84: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba68] AnonymousClosure: (0x13bf15c), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bde88: ldr             x1, [x1, #0xa68]
    // 0x13bde8c: r0 = AllocateClosure()
    //     0x13bde8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bde90: mov             x1, x0
    // 0x13bde94: ldur            x0, [fp, #-0x20]
    // 0x13bde98: ArrayStore: r0[0] = r1  ; List_4
    //     0x13bde98: stur            w1, [x0, #0x17]
    // 0x13bde9c: ldur            x2, [fp, #-8]
    // 0x13bdea0: r1 = Function '<anonymous closure>':.
    //     0x13bdea0: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba70] AnonymousClosure: (0x13bf044), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdea4: ldr             x1, [x1, #0xa70]
    // 0x13bdea8: r0 = AllocateClosure()
    //     0x13bdea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdeac: mov             x1, x0
    // 0x13bdeb0: ldur            x0, [fp, #-0x20]
    // 0x13bdeb4: StoreField: r0->field_13 = r1
    //     0x13bdeb4: stur            w1, [x0, #0x13]
    // 0x13bdeb8: ldur            x2, [fp, #-8]
    // 0x13bdebc: r1 = Function '<anonymous closure>':.
    //     0x13bdebc: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba78] AnonymousClosure: (0x13bedd8), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdec0: ldr             x1, [x1, #0xa78]
    // 0x13bdec4: r0 = AllocateClosure()
    //     0x13bdec4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdec8: mov             x1, x0
    // 0x13bdecc: ldur            x0, [fp, #-0x20]
    // 0x13bded0: StoreField: r0->field_1b = r1
    //     0x13bded0: stur            w1, [x0, #0x1b]
    // 0x13bded4: ldur            x2, [fp, #-8]
    // 0x13bded8: r1 = Function '<anonymous closure>':.
    //     0x13bded8: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba80] AnonymousClosure: (0x13bef70), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdedc: ldr             x1, [x1, #0xa80]
    // 0x13bdee0: r0 = AllocateClosure()
    //     0x13bdee0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdee4: mov             x1, x0
    // 0x13bdee8: ldur            x0, [fp, #-0x20]
    // 0x13bdeec: StoreField: r0->field_1f = r1
    //     0x13bdeec: stur            w1, [x0, #0x1f]
    // 0x13bdef0: ldur            x2, [fp, #-8]
    // 0x13bdef4: r1 = Function '<anonymous closure>':.
    //     0x13bdef4: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba88] AnonymousClosure: (0x13bee58), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdef8: ldr             x1, [x1, #0xa88]
    // 0x13bdefc: r0 = AllocateClosure()
    //     0x13bdefc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdf00: mov             x1, x0
    // 0x13bdf04: ldur            x0, [fp, #-0x20]
    // 0x13bdf08: StoreField: r0->field_23 = r1
    //     0x13bdf08: stur            w1, [x0, #0x23]
    // 0x13bdf0c: ldur            x2, [fp, #-8]
    // 0x13bdf10: r1 = Function '<anonymous closure>':.
    //     0x13bdf10: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba90] AnonymousClosure: (0x13bedd8), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdf14: ldr             x1, [x1, #0xa90]
    // 0x13bdf18: r0 = AllocateClosure()
    //     0x13bdf18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdf1c: mov             x1, x0
    // 0x13bdf20: ldur            x0, [fp, #-0x20]
    // 0x13bdf24: StoreField: r0->field_27 = r1
    //     0x13bdf24: stur            w1, [x0, #0x27]
    // 0x13bdf28: ldur            x2, [fp, #-8]
    // 0x13bdf2c: r1 = Function '<anonymous closure>':.
    //     0x13bdf2c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba98] AnonymousClosure: (0x13bed04), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdf30: ldr             x1, [x1, #0xa98]
    // 0x13bdf34: r0 = AllocateClosure()
    //     0x13bdf34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdf38: mov             x1, x0
    // 0x13bdf3c: ldur            x0, [fp, #-0x20]
    // 0x13bdf40: StoreField: r0->field_2b = r1
    //     0x13bdf40: stur            w1, [x0, #0x2b]
    // 0x13bdf44: ldur            x2, [fp, #-8]
    // 0x13bdf48: r1 = Function '<anonymous closure>':.
    //     0x13bdf48: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3baa0] AnonymousClosure: (0x13bebec), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdf4c: ldr             x1, [x1, #0xaa0]
    // 0x13bdf50: r0 = AllocateClosure()
    //     0x13bdf50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdf54: mov             x1, x0
    // 0x13bdf58: ldur            x0, [fp, #-0x20]
    // 0x13bdf5c: StoreField: r0->field_2f = r1
    //     0x13bdf5c: stur            w1, [x0, #0x2f]
    // 0x13bdf60: ldur            x2, [fp, #-8]
    // 0x13bdf64: r1 = Function '<anonymous closure>':.
    //     0x13bdf64: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3baa8] AnonymousClosure: (0x13beb80), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdf68: ldr             x1, [x1, #0xaa8]
    // 0x13bdf6c: r0 = AllocateClosure()
    //     0x13bdf6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdf70: mov             x1, x0
    // 0x13bdf74: ldur            x0, [fp, #-0x20]
    // 0x13bdf78: StoreField: r0->field_33 = r1
    //     0x13bdf78: stur            w1, [x0, #0x33]
    // 0x13bdf7c: ldur            x2, [fp, #-8]
    // 0x13bdf80: r1 = Function '<anonymous closure>':.
    //     0x13bdf80: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bab0] AnonymousClosure: (0x13be884), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdf84: ldr             x1, [x1, #0xab0]
    // 0x13bdf88: r0 = AllocateClosure()
    //     0x13bdf88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdf8c: mov             x1, x0
    // 0x13bdf90: ldur            x0, [fp, #-0x20]
    // 0x13bdf94: StoreField: r0->field_37 = r1
    //     0x13bdf94: stur            w1, [x0, #0x37]
    // 0x13bdf98: ldur            x2, [fp, #-8]
    // 0x13bdf9c: r1 = Function '<anonymous closure>':.
    //     0x13bdf9c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bab8] AnonymousClosure: (0x13be31c), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdfa0: ldr             x1, [x1, #0xab8]
    // 0x13bdfa4: r0 = AllocateClosure()
    //     0x13bdfa4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdfa8: mov             x1, x0
    // 0x13bdfac: ldur            x0, [fp, #-0x20]
    // 0x13bdfb0: StoreField: r0->field_3b = r1
    //     0x13bdfb0: stur            w1, [x0, #0x3b]
    // 0x13bdfb4: ldur            x2, [fp, #-8]
    // 0x13bdfb8: r1 = Function '<anonymous closure>':.
    //     0x13bdfb8: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bac0] AnonymousClosure: (0x13be1f4), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdfbc: ldr             x1, [x1, #0xac0]
    // 0x13bdfc0: r0 = AllocateClosure()
    //     0x13bdfc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdfc4: mov             x1, x0
    // 0x13bdfc8: ldur            x0, [fp, #-0x20]
    // 0x13bdfcc: StoreField: r0->field_3f = r1
    //     0x13bdfcc: stur            w1, [x0, #0x3f]
    // 0x13bdfd0: ldur            x2, [fp, #-8]
    // 0x13bdfd4: r1 = Function '<anonymous closure>':.
    //     0x13bdfd4: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bac8] AnonymousClosure: (0x13be064), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdfd8: ldr             x1, [x1, #0xac8]
    // 0x13bdfdc: r0 = AllocateClosure()
    //     0x13bdfdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdfe0: mov             x1, x0
    // 0x13bdfe4: ldur            x0, [fp, #-0x20]
    // 0x13bdfe8: StoreField: r0->field_43 = r1
    //     0x13bdfe8: stur            w1, [x0, #0x43]
    // 0x13bdfec: ldur            x2, [fp, #-8]
    // 0x13bdff0: r1 = Function '<anonymous closure>':.
    //     0x13bdff0: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3bad0] AnonymousClosure: (0x13bdd60), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bdff4: ldr             x1, [x1, #0xad0]
    // 0x13bdff8: r0 = AllocateClosure()
    //     0x13bdff8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bdffc: mov             x1, x0
    // 0x13be000: ldur            x0, [fp, #-0x20]
    // 0x13be004: StoreField: r0->field_47 = r1
    //     0x13be004: stur            w1, [x0, #0x47]
    // 0x13be008: r0 = SingleChildScrollView()
    //     0x13be008: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x13be00c: r1 = Instance_Axis
    //     0x13be00c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13be010: StoreField: r0->field_b = r1
    //     0x13be010: stur            w1, [x0, #0xb]
    // 0x13be014: r1 = false
    //     0x13be014: add             x1, NULL, #0x30  ; false
    // 0x13be018: StoreField: r0->field_f = r1
    //     0x13be018: stur            w1, [x0, #0xf]
    // 0x13be01c: ldur            x1, [fp, #-0x20]
    // 0x13be020: StoreField: r0->field_23 = r1
    //     0x13be020: stur            w1, [x0, #0x23]
    // 0x13be024: r1 = Instance_DragStartBehavior
    //     0x13be024: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x13be028: StoreField: r0->field_27 = r1
    //     0x13be028: stur            w1, [x0, #0x27]
    // 0x13be02c: r1 = Instance_Clip
    //     0x13be02c: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x13be030: ldr             x1, [x1, #0x7e0]
    // 0x13be034: StoreField: r0->field_2b = r1
    //     0x13be034: stur            w1, [x0, #0x2b]
    // 0x13be038: r1 = Instance_HitTestBehavior
    //     0x13be038: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x13be03c: ldr             x1, [x1, #0x288]
    // 0x13be040: StoreField: r0->field_2f = r1
    //     0x13be040: stur            w1, [x0, #0x2f]
    // 0x13be044: LeaveFrame
    //     0x13be044: mov             SP, fp
    //     0x13be048: ldp             fp, lr, [SP], #0x10
    // 0x13be04c: ret
    //     0x13be04c: ret             
    // 0x13be050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13be050: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13be054: b               #0x13bde2c
  }
  [closure] Null <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0x13be064, size: 0x54
    // 0x13be064: EnterFrame
    //     0x13be064: stp             fp, lr, [SP, #-0x10]!
    //     0x13be068: mov             fp, SP
    // 0x13be06c: ldr             x0, [fp, #0x18]
    // 0x13be070: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13be070: ldur            w1, [x0, #0x17]
    // 0x13be074: DecompressPointer r1
    //     0x13be074: add             x1, x1, HEAP, lsl #32
    // 0x13be078: CheckStackOverflow
    //     0x13be078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13be07c: cmp             SP, x16
    //     0x13be080: b.ls            #0x13be0b0
    // 0x13be084: LoadField: r0 = r1->field_f
    //     0x13be084: ldur            w0, [x1, #0xf]
    // 0x13be088: DecompressPointer r0
    //     0x13be088: add             x0, x0, HEAP, lsl #32
    // 0x13be08c: mov             x1, x0
    // 0x13be090: r0 = controller()
    //     0x13be090: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be094: mov             x1, x0
    // 0x13be098: ldr             x2, [fp, #0x10]
    // 0x13be09c: r0 = removeProductCustomisationList()
    //     0x13be09c: bl              #0x13be0b8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::removeProductCustomisationList
    // 0x13be0a0: r0 = Null
    //     0x13be0a0: mov             x0, NULL
    // 0x13be0a4: LeaveFrame
    //     0x13be0a4: mov             SP, fp
    //     0x13be0a8: ldp             fp, lr, [SP], #0x10
    // 0x13be0ac: ret
    //     0x13be0ac: ret             
    // 0x13be0b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13be0b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13be0b4: b               #0x13be084
  }
  [closure] Null <anonymous closure>(dynamic, bool, int) {
    // ** addr: 0x13be1f4, size: 0xc8
    // 0x13be1f4: EnterFrame
    //     0x13be1f4: stp             fp, lr, [SP, #-0x10]!
    //     0x13be1f8: mov             fp, SP
    // 0x13be1fc: AllocStack(0x10)
    //     0x13be1fc: sub             SP, SP, #0x10
    // 0x13be200: SetupParameters()
    //     0x13be200: ldr             x0, [fp, #0x20]
    //     0x13be204: ldur            w2, [x0, #0x17]
    //     0x13be208: add             x2, x2, HEAP, lsl #32
    //     0x13be20c: stur            x2, [fp, #-8]
    // 0x13be210: CheckStackOverflow
    //     0x13be210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13be214: cmp             SP, x16
    //     0x13be218: b.ls            #0x13be2b4
    // 0x13be21c: LoadField: r1 = r2->field_f
    //     0x13be21c: ldur            w1, [x2, #0xf]
    // 0x13be220: DecompressPointer r1
    //     0x13be220: add             x1, x1, HEAP, lsl #32
    // 0x13be224: r0 = controller()
    //     0x13be224: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be228: mov             x1, x0
    // 0x13be22c: ldr             x2, [fp, #0x18]
    // 0x13be230: r0 = phoneNumber=()
    //     0x13be230: bl              #0x1391d4c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_number_controller.dart] CheckoutRequestNumberController::phoneNumber=
    // 0x13be234: ldr             x0, [fp, #0x10]
    // 0x13be238: r2 = LoadInt32Instr(r0)
    //     0x13be238: sbfx            x2, x0, #1, #0x1f
    //     0x13be23c: tbz             w0, #0, #0x13be244
    //     0x13be240: ldur            x2, [x0, #7]
    // 0x13be244: stur            x2, [fp, #-0x10]
    // 0x13be248: cbz             x2, #0x13be2a4
    // 0x13be24c: ldur            x0, [fp, #-8]
    // 0x13be250: LoadField: r1 = r0->field_f
    //     0x13be250: ldur            w1, [x0, #0xf]
    // 0x13be254: DecompressPointer r1
    //     0x13be254: add             x1, x1, HEAP, lsl #32
    // 0x13be258: r0 = controller()
    //     0x13be258: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be25c: LoadField: r2 = r0->field_67
    //     0x13be25c: ldur            w2, [x0, #0x67]
    // 0x13be260: DecompressPointer r2
    //     0x13be260: add             x2, x2, HEAP, lsl #32
    // 0x13be264: mov             x1, x2
    // 0x13be268: stur            x2, [fp, #-8]
    // 0x13be26c: r0 = value()
    //     0x13be26c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13be270: r1 = LoadInt32Instr(r0)
    //     0x13be270: sbfx            x1, x0, #1, #0x1f
    //     0x13be274: tbz             w0, #0, #0x13be27c
    //     0x13be278: ldur            x1, [x0, #7]
    // 0x13be27c: ldur            x0, [fp, #-0x10]
    // 0x13be280: sub             x2, x1, x0
    // 0x13be284: r0 = BoxInt64Instr(r2)
    //     0x13be284: sbfiz           x0, x2, #1, #0x1f
    //     0x13be288: cmp             x2, x0, asr #1
    //     0x13be28c: b.eq            #0x13be298
    //     0x13be290: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x13be294: stur            x2, [x0, #7]
    // 0x13be298: ldur            x1, [fp, #-8]
    // 0x13be29c: mov             x2, x0
    // 0x13be2a0: r0 = value=()
    //     0x13be2a0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13be2a4: r0 = Null
    //     0x13be2a4: mov             x0, NULL
    // 0x13be2a8: LeaveFrame
    //     0x13be2a8: mov             SP, fp
    //     0x13be2ac: ldp             fp, lr, [SP], #0x10
    // 0x13be2b0: ret
    //     0x13be2b0: ret             
    // 0x13be2b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13be2b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13be2b8: b               #0x13be21c
  }
  [closure] Null <anonymous closure>(dynamic, int, bool, bool, CustomerResponse) {
    // ** addr: 0x13be31c, size: 0xd4
    // 0x13be31c: EnterFrame
    //     0x13be31c: stp             fp, lr, [SP, #-0x10]!
    //     0x13be320: mov             fp, SP
    // 0x13be324: AllocStack(0x10)
    //     0x13be324: sub             SP, SP, #0x10
    // 0x13be328: SetupParameters()
    //     0x13be328: ldr             x0, [fp, #0x30]
    //     0x13be32c: ldur            w2, [x0, #0x17]
    //     0x13be330: add             x2, x2, HEAP, lsl #32
    //     0x13be334: stur            x2, [fp, #-8]
    // 0x13be338: CheckStackOverflow
    //     0x13be338: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13be33c: cmp             SP, x16
    //     0x13be340: b.ls            #0x13be3e8
    // 0x13be344: LoadField: r1 = r2->field_f
    //     0x13be344: ldur            w1, [x2, #0xf]
    // 0x13be348: DecompressPointer r1
    //     0x13be348: add             x1, x1, HEAP, lsl #32
    // 0x13be34c: r0 = controller()
    //     0x13be34c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be350: mov             x1, x0
    // 0x13be354: ldr             x0, [fp, #0x28]
    // 0x13be358: r3 = LoadInt32Instr(r0)
    //     0x13be358: sbfx            x3, x0, #1, #0x1f
    //     0x13be35c: tbz             w0, #0, #0x13be364
    //     0x13be360: ldur            x3, [x0, #7]
    // 0x13be364: mov             x2, x3
    // 0x13be368: stur            x3, [fp, #-0x10]
    // 0x13be36c: r0 = priceDecrement()
    //     0x13be36c: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13be370: ldr             x0, [fp, #0x20]
    // 0x13be374: tbnz            w0, #4, #0x13be39c
    // 0x13be378: ldur            x0, [fp, #-8]
    // 0x13be37c: LoadField: r1 = r0->field_f
    //     0x13be37c: ldur            w1, [x0, #0xf]
    // 0x13be380: DecompressPointer r1
    //     0x13be380: add             x1, x1, HEAP, lsl #32
    // 0x13be384: r0 = controller()
    //     0x13be384: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be388: LoadField: r1 = r0->field_77
    //     0x13be388: ldur            w1, [x0, #0x77]
    // 0x13be38c: DecompressPointer r1
    //     0x13be38c: add             x1, x1, HEAP, lsl #32
    // 0x13be390: r2 = false
    //     0x13be390: add             x2, NULL, #0x30  ; false
    // 0x13be394: r0 = value=()
    //     0x13be394: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13be398: b               #0x13be3b8
    // 0x13be39c: ldur            x0, [fp, #-8]
    // 0x13be3a0: LoadField: r1 = r0->field_f
    //     0x13be3a0: ldur            w1, [x0, #0xf]
    // 0x13be3a4: DecompressPointer r1
    //     0x13be3a4: add             x1, x1, HEAP, lsl #32
    // 0x13be3a8: r0 = controller()
    //     0x13be3a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be3ac: mov             x1, x0
    // 0x13be3b0: ldr             x2, [fp, #0x18]
    // 0x13be3b4: r0 = customisationMultiSelectMandatory=()
    //     0x13be3b4: bl              #0x13be77c  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::customisationMultiSelectMandatory=
    // 0x13be3b8: ldur            x0, [fp, #-8]
    // 0x13be3bc: LoadField: r1 = r0->field_f
    //     0x13be3bc: ldur            w1, [x0, #0xf]
    // 0x13be3c0: DecompressPointer r1
    //     0x13be3c0: add             x1, x1, HEAP, lsl #32
    // 0x13be3c4: r0 = controller()
    //     0x13be3c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be3c8: mov             x1, x0
    // 0x13be3cc: ldr             x2, [fp, #0x10]
    // 0x13be3d0: ldur            x3, [fp, #-0x10]
    // 0x13be3d4: r0 = removeCustomerCustomisationList()
    //     0x13be3d4: bl              #0x13be3f0  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::removeCustomerCustomisationList
    // 0x13be3d8: r0 = Null
    //     0x13be3d8: mov             x0, NULL
    // 0x13be3dc: LeaveFrame
    //     0x13be3dc: mov             SP, fp
    //     0x13be3e0: ldp             fp, lr, [SP], #0x10
    // 0x13be3e4: ret
    //     0x13be3e4: ret             
    // 0x13be3e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13be3e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13be3ec: b               #0x13be344
  }
  [closure] Null <anonymous closure>(dynamic, int, CustomerResponse) {
    // ** addr: 0x13be884, size: 0xac
    // 0x13be884: EnterFrame
    //     0x13be884: stp             fp, lr, [SP, #-0x10]!
    //     0x13be888: mov             fp, SP
    // 0x13be88c: AllocStack(0x10)
    //     0x13be88c: sub             SP, SP, #0x10
    // 0x13be890: SetupParameters()
    //     0x13be890: ldr             x0, [fp, #0x20]
    //     0x13be894: ldur            w2, [x0, #0x17]
    //     0x13be898: add             x2, x2, HEAP, lsl #32
    //     0x13be89c: stur            x2, [fp, #-8]
    // 0x13be8a0: CheckStackOverflow
    //     0x13be8a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13be8a4: cmp             SP, x16
    //     0x13be8a8: b.ls            #0x13be928
    // 0x13be8ac: LoadField: r1 = r2->field_f
    //     0x13be8ac: ldur            w1, [x2, #0xf]
    // 0x13be8b0: DecompressPointer r1
    //     0x13be8b0: add             x1, x1, HEAP, lsl #32
    // 0x13be8b4: r0 = controller()
    //     0x13be8b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be8b8: mov             x1, x0
    // 0x13be8bc: ldr             x0, [fp, #0x18]
    // 0x13be8c0: r3 = LoadInt32Instr(r0)
    //     0x13be8c0: sbfx            x3, x0, #1, #0x1f
    //     0x13be8c4: tbz             w0, #0, #0x13be8cc
    //     0x13be8c8: ldur            x3, [x0, #7]
    // 0x13be8cc: mov             x2, x3
    // 0x13be8d0: stur            x3, [fp, #-0x10]
    // 0x13be8d4: r0 = priceIncrement()
    //     0x13be8d4: bl              #0x13bf240  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceIncrement
    // 0x13be8d8: ldur            x0, [fp, #-8]
    // 0x13be8dc: LoadField: r1 = r0->field_f
    //     0x13be8dc: ldur            w1, [x0, #0xf]
    // 0x13be8e0: DecompressPointer r1
    //     0x13be8e0: add             x1, x1, HEAP, lsl #32
    // 0x13be8e4: r0 = controller()
    //     0x13be8e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be8e8: mov             x1, x0
    // 0x13be8ec: ldr             x2, [fp, #0x10]
    // 0x13be8f0: ldur            x3, [fp, #-0x10]
    // 0x13be8f4: r0 = setCustomerCustomisationList()
    //     0x13be8f4: bl              #0x13be930  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setCustomerCustomisationList
    // 0x13be8f8: ldur            x0, [fp, #-8]
    // 0x13be8fc: LoadField: r1 = r0->field_f
    //     0x13be8fc: ldur            w1, [x0, #0xf]
    // 0x13be900: DecompressPointer r1
    //     0x13be900: add             x1, x1, HEAP, lsl #32
    // 0x13be904: r0 = controller()
    //     0x13be904: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13be908: LoadField: r1 = r0->field_77
    //     0x13be908: ldur            w1, [x0, #0x77]
    // 0x13be90c: DecompressPointer r1
    //     0x13be90c: add             x1, x1, HEAP, lsl #32
    // 0x13be910: r2 = false
    //     0x13be910: add             x2, NULL, #0x30  ; false
    // 0x13be914: r0 = value=()
    //     0x13be914: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13be918: r0 = Null
    //     0x13be918: mov             x0, NULL
    // 0x13be91c: LeaveFrame
    //     0x13be91c: mov             SP, fp
    //     0x13be920: ldp             fp, lr, [SP], #0x10
    // 0x13be924: ret
    //     0x13be924: ret             
    // 0x13be928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13be928: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13be92c: b               #0x13be8ac
  }
  [closure] Null <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0x13beb80, size: 0x6c
    // 0x13beb80: EnterFrame
    //     0x13beb80: stp             fp, lr, [SP, #-0x10]!
    //     0x13beb84: mov             fp, SP
    // 0x13beb88: ldr             x0, [fp, #0x18]
    // 0x13beb8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13beb8c: ldur            w1, [x0, #0x17]
    // 0x13beb90: DecompressPointer r1
    //     0x13beb90: add             x1, x1, HEAP, lsl #32
    // 0x13beb94: CheckStackOverflow
    //     0x13beb94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13beb98: cmp             SP, x16
    //     0x13beb9c: b.ls            #0x13bebe4
    // 0x13beba0: LoadField: r0 = r1->field_f
    //     0x13beba0: ldur            w0, [x1, #0xf]
    // 0x13beba4: DecompressPointer r0
    //     0x13beba4: add             x0, x0, HEAP, lsl #32
    // 0x13beba8: mov             x1, x0
    // 0x13bebac: r0 = controller()
    //     0x13bebac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bebb0: mov             x1, x0
    // 0x13bebb4: ldr             x0, [fp, #0x10]
    // 0x13bebb8: StoreField: r1->field_63 = r0
    //     0x13bebb8: stur            w0, [x1, #0x63]
    //     0x13bebbc: ldurb           w16, [x1, #-1]
    //     0x13bebc0: ldurb           w17, [x0, #-1]
    //     0x13bebc4: and             x16, x17, x16, lsr #2
    //     0x13bebc8: tst             x16, HEAP, lsr #32
    //     0x13bebcc: b.eq            #0x13bebd4
    //     0x13bebd0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13bebd4: r0 = Null
    //     0x13bebd4: mov             x0, NULL
    // 0x13bebd8: LeaveFrame
    //     0x13bebd8: mov             SP, fp
    //     0x13bebdc: ldp             fp, lr, [SP], #0x10
    // 0x13bebe0: ret
    //     0x13bebe0: ret             
    // 0x13bebe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bebe4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bebe8: b               #0x13beba0
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0x13bebec, size: 0x9c
    // 0x13bebec: EnterFrame
    //     0x13bebec: stp             fp, lr, [SP, #-0x10]!
    //     0x13bebf0: mov             fp, SP
    // 0x13bebf4: AllocStack(0x8)
    //     0x13bebf4: sub             SP, SP, #8
    // 0x13bebf8: SetupParameters()
    //     0x13bebf8: ldr             x0, [fp, #0x28]
    //     0x13bebfc: ldur            w2, [x0, #0x17]
    //     0x13bec00: add             x2, x2, HEAP, lsl #32
    //     0x13bec04: stur            x2, [fp, #-8]
    // 0x13bec08: CheckStackOverflow
    //     0x13bec08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bec0c: cmp             SP, x16
    //     0x13bec10: b.ls            #0x13bec80
    // 0x13bec14: LoadField: r1 = r2->field_f
    //     0x13bec14: ldur            w1, [x2, #0xf]
    // 0x13bec18: DecompressPointer r1
    //     0x13bec18: add             x1, x1, HEAP, lsl #32
    // 0x13bec1c: r0 = controller()
    //     0x13bec1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bec20: mov             x1, x0
    // 0x13bec24: ldr             x0, [fp, #0x20]
    // 0x13bec28: r2 = LoadInt32Instr(r0)
    //     0x13bec28: sbfx            x2, x0, #1, #0x1f
    //     0x13bec2c: tbz             w0, #0, #0x13bec34
    //     0x13bec30: ldur            x2, [x0, #7]
    // 0x13bec34: r0 = priceDecrement()
    //     0x13bec34: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13bec38: ldur            x0, [fp, #-8]
    // 0x13bec3c: LoadField: r1 = r0->field_f
    //     0x13bec3c: ldur            w1, [x0, #0xf]
    // 0x13bec40: DecompressPointer r1
    //     0x13bec40: add             x1, x1, HEAP, lsl #32
    // 0x13bec44: r0 = controller()
    //     0x13bec44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bec48: mov             x1, x0
    // 0x13bec4c: ldr             x2, [fp, #0x10]
    // 0x13bec50: r0 = customisationNumberMandatory=()
    //     0x13bec50: bl              #0x13bec88  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::customisationNumberMandatory=
    // 0x13bec54: ldur            x0, [fp, #-8]
    // 0x13bec58: LoadField: r1 = r0->field_f
    //     0x13bec58: ldur            w1, [x0, #0xf]
    // 0x13bec5c: DecompressPointer r1
    //     0x13bec5c: add             x1, x1, HEAP, lsl #32
    // 0x13bec60: r0 = controller()
    //     0x13bec60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bec64: mov             x1, x0
    // 0x13bec68: ldr             x2, [fp, #0x18]
    // 0x13bec6c: r0 = setProductCustomisationList()
    //     0x13bec6c: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bec70: r0 = Null
    //     0x13bec70: mov             x0, NULL
    // 0x13bec74: LeaveFrame
    //     0x13bec74: mov             SP, fp
    //     0x13bec78: ldp             fp, lr, [SP], #0x10
    // 0x13bec7c: ret
    //     0x13bec7c: ret             
    // 0x13bec80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bec80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bec84: b               #0x13bec14
  }
  [closure] Null <anonymous closure>(dynamic, String, int, List<ProductCustomisation>, bool) {
    // ** addr: 0x13bed04, size: 0xd4
    // 0x13bed04: EnterFrame
    //     0x13bed04: stp             fp, lr, [SP, #-0x10]!
    //     0x13bed08: mov             fp, SP
    // 0x13bed0c: AllocStack(0x8)
    //     0x13bed0c: sub             SP, SP, #8
    // 0x13bed10: SetupParameters()
    //     0x13bed10: ldr             x0, [fp, #0x30]
    //     0x13bed14: ldur            w2, [x0, #0x17]
    //     0x13bed18: add             x2, x2, HEAP, lsl #32
    //     0x13bed1c: stur            x2, [fp, #-8]
    // 0x13bed20: CheckStackOverflow
    //     0x13bed20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bed24: cmp             SP, x16
    //     0x13bed28: b.ls            #0x13bedd0
    // 0x13bed2c: ldr             x0, [fp, #0x28]
    // 0x13bed30: LoadField: r1 = r0->field_7
    //     0x13bed30: ldur            w1, [x0, #7]
    // 0x13bed34: cbz             w1, #0x13beda0
    // 0x13bed38: ldr             x0, [fp, #0x20]
    // 0x13bed3c: LoadField: r1 = r2->field_f
    //     0x13bed3c: ldur            w1, [x2, #0xf]
    // 0x13bed40: DecompressPointer r1
    //     0x13bed40: add             x1, x1, HEAP, lsl #32
    // 0x13bed44: r0 = controller()
    //     0x13bed44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bed48: LoadField: r1 = r0->field_7f
    //     0x13bed48: ldur            w1, [x0, #0x7f]
    // 0x13bed4c: DecompressPointer r1
    //     0x13bed4c: add             x1, x1, HEAP, lsl #32
    // 0x13bed50: r2 = false
    //     0x13bed50: add             x2, NULL, #0x30  ; false
    // 0x13bed54: r0 = value=()
    //     0x13bed54: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bed58: ldur            x0, [fp, #-8]
    // 0x13bed5c: LoadField: r1 = r0->field_f
    //     0x13bed5c: ldur            w1, [x0, #0xf]
    // 0x13bed60: DecompressPointer r1
    //     0x13bed60: add             x1, x1, HEAP, lsl #32
    // 0x13bed64: r0 = controller()
    //     0x13bed64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bed68: mov             x1, x0
    // 0x13bed6c: ldr             x0, [fp, #0x20]
    // 0x13bed70: r2 = LoadInt32Instr(r0)
    //     0x13bed70: sbfx            x2, x0, #1, #0x1f
    //     0x13bed74: tbz             w0, #0, #0x13bed7c
    //     0x13bed78: ldur            x2, [x0, #7]
    // 0x13bed7c: r0 = priceIncrement()
    //     0x13bed7c: bl              #0x13bf240  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceIncrement
    // 0x13bed80: ldur            x0, [fp, #-8]
    // 0x13bed84: LoadField: r1 = r0->field_f
    //     0x13bed84: ldur            w1, [x0, #0xf]
    // 0x13bed88: DecompressPointer r1
    //     0x13bed88: add             x1, x1, HEAP, lsl #32
    // 0x13bed8c: r0 = controller()
    //     0x13bed8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bed90: mov             x1, x0
    // 0x13bed94: ldr             x2, [fp, #0x18]
    // 0x13bed98: r0 = setProductCustomisationList()
    //     0x13bed98: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bed9c: b               #0x13bedc0
    // 0x13beda0: mov             x0, x2
    // 0x13beda4: LoadField: r1 = r0->field_f
    //     0x13beda4: ldur            w1, [x0, #0xf]
    // 0x13beda8: DecompressPointer r1
    //     0x13beda8: add             x1, x1, HEAP, lsl #32
    // 0x13bedac: r0 = controller()
    //     0x13bedac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bedb0: LoadField: r1 = r0->field_7f
    //     0x13bedb0: ldur            w1, [x0, #0x7f]
    // 0x13bedb4: DecompressPointer r1
    //     0x13bedb4: add             x1, x1, HEAP, lsl #32
    // 0x13bedb8: ldr             x2, [fp, #0x10]
    // 0x13bedbc: r0 = value=()
    //     0x13bedbc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bedc0: r0 = Null
    //     0x13bedc0: mov             x0, NULL
    // 0x13bedc4: LeaveFrame
    //     0x13bedc4: mov             SP, fp
    //     0x13bedc8: ldp             fp, lr, [SP], #0x10
    // 0x13bedcc: ret
    //     0x13bedcc: ret             
    // 0x13bedd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bedd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bedd4: b               #0x13bed2c
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0x13bedd8, size: 0x80
    // 0x13bedd8: EnterFrame
    //     0x13bedd8: stp             fp, lr, [SP, #-0x10]!
    //     0x13beddc: mov             fp, SP
    // 0x13bede0: AllocStack(0x8)
    //     0x13bede0: sub             SP, SP, #8
    // 0x13bede4: SetupParameters()
    //     0x13bede4: ldr             x0, [fp, #0x20]
    //     0x13bede8: ldur            w2, [x0, #0x17]
    //     0x13bedec: add             x2, x2, HEAP, lsl #32
    //     0x13bedf0: stur            x2, [fp, #-8]
    // 0x13bedf4: CheckStackOverflow
    //     0x13bedf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bedf8: cmp             SP, x16
    //     0x13bedfc: b.ls            #0x13bee50
    // 0x13bee00: LoadField: r1 = r2->field_f
    //     0x13bee00: ldur            w1, [x2, #0xf]
    // 0x13bee04: DecompressPointer r1
    //     0x13bee04: add             x1, x1, HEAP, lsl #32
    // 0x13bee08: r0 = controller()
    //     0x13bee08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bee0c: mov             x1, x0
    // 0x13bee10: ldr             x2, [fp, #0x10]
    // 0x13bee14: r0 = removeProductCustomisationList()
    //     0x13bee14: bl              #0x13be0b8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::removeProductCustomisationList
    // 0x13bee18: ldur            x0, [fp, #-8]
    // 0x13bee1c: LoadField: r1 = r0->field_f
    //     0x13bee1c: ldur            w1, [x0, #0xf]
    // 0x13bee20: DecompressPointer r1
    //     0x13bee20: add             x1, x1, HEAP, lsl #32
    // 0x13bee24: r0 = controller()
    //     0x13bee24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bee28: mov             x1, x0
    // 0x13bee2c: ldr             x0, [fp, #0x18]
    // 0x13bee30: r2 = LoadInt32Instr(r0)
    //     0x13bee30: sbfx            x2, x0, #1, #0x1f
    //     0x13bee34: tbz             w0, #0, #0x13bee3c
    //     0x13bee38: ldur            x2, [x0, #7]
    // 0x13bee3c: r0 = priceDecrement()
    //     0x13bee3c: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13bee40: r0 = Null
    //     0x13bee40: mov             x0, NULL
    // 0x13bee44: LeaveFrame
    //     0x13bee44: mov             SP, fp
    //     0x13bee48: ldp             fp, lr, [SP], #0x10
    // 0x13bee4c: ret
    //     0x13bee4c: ret             
    // 0x13bee50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bee50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bee54: b               #0x13bee00
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0x13bee58, size: 0x9c
    // 0x13bee58: EnterFrame
    //     0x13bee58: stp             fp, lr, [SP, #-0x10]!
    //     0x13bee5c: mov             fp, SP
    // 0x13bee60: AllocStack(0x8)
    //     0x13bee60: sub             SP, SP, #8
    // 0x13bee64: SetupParameters()
    //     0x13bee64: ldr             x0, [fp, #0x28]
    //     0x13bee68: ldur            w2, [x0, #0x17]
    //     0x13bee6c: add             x2, x2, HEAP, lsl #32
    //     0x13bee70: stur            x2, [fp, #-8]
    // 0x13bee74: CheckStackOverflow
    //     0x13bee74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bee78: cmp             SP, x16
    //     0x13bee7c: b.ls            #0x13beeec
    // 0x13bee80: LoadField: r1 = r2->field_f
    //     0x13bee80: ldur            w1, [x2, #0xf]
    // 0x13bee84: DecompressPointer r1
    //     0x13bee84: add             x1, x1, HEAP, lsl #32
    // 0x13bee88: r0 = controller()
    //     0x13bee88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bee8c: mov             x1, x0
    // 0x13bee90: ldr             x0, [fp, #0x20]
    // 0x13bee94: r2 = LoadInt32Instr(r0)
    //     0x13bee94: sbfx            x2, x0, #1, #0x1f
    //     0x13bee98: tbz             w0, #0, #0x13beea0
    //     0x13bee9c: ldur            x2, [x0, #7]
    // 0x13beea0: r0 = priceDecrement()
    //     0x13beea0: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13beea4: ldur            x0, [fp, #-8]
    // 0x13beea8: LoadField: r1 = r0->field_f
    //     0x13beea8: ldur            w1, [x0, #0xf]
    // 0x13beeac: DecompressPointer r1
    //     0x13beeac: add             x1, x1, HEAP, lsl #32
    // 0x13beeb0: r0 = controller()
    //     0x13beeb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13beeb4: mov             x1, x0
    // 0x13beeb8: ldr             x2, [fp, #0x10]
    // 0x13beebc: r0 = customisationTextMandatory=()
    //     0x13beebc: bl              #0x13beef4  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::customisationTextMandatory=
    // 0x13beec0: ldur            x0, [fp, #-8]
    // 0x13beec4: LoadField: r1 = r0->field_f
    //     0x13beec4: ldur            w1, [x0, #0xf]
    // 0x13beec8: DecompressPointer r1
    //     0x13beec8: add             x1, x1, HEAP, lsl #32
    // 0x13beecc: r0 = controller()
    //     0x13beecc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13beed0: mov             x1, x0
    // 0x13beed4: ldr             x2, [fp, #0x18]
    // 0x13beed8: r0 = setProductCustomisationList()
    //     0x13beed8: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13beedc: r0 = Null
    //     0x13beedc: mov             x0, NULL
    // 0x13beee0: LeaveFrame
    //     0x13beee0: mov             SP, fp
    //     0x13beee4: ldp             fp, lr, [SP], #0x10
    // 0x13beee8: ret
    //     0x13beee8: ret             
    // 0x13beeec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13beeec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13beef0: b               #0x13bee80
  }
  [closure] Null <anonymous closure>(dynamic, String, int, List<ProductCustomisation>, bool) {
    // ** addr: 0x13bef70, size: 0xd4
    // 0x13bef70: EnterFrame
    //     0x13bef70: stp             fp, lr, [SP, #-0x10]!
    //     0x13bef74: mov             fp, SP
    // 0x13bef78: AllocStack(0x8)
    //     0x13bef78: sub             SP, SP, #8
    // 0x13bef7c: SetupParameters()
    //     0x13bef7c: ldr             x0, [fp, #0x30]
    //     0x13bef80: ldur            w2, [x0, #0x17]
    //     0x13bef84: add             x2, x2, HEAP, lsl #32
    //     0x13bef88: stur            x2, [fp, #-8]
    // 0x13bef8c: CheckStackOverflow
    //     0x13bef8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bef90: cmp             SP, x16
    //     0x13bef94: b.ls            #0x13bf03c
    // 0x13bef98: ldr             x0, [fp, #0x28]
    // 0x13bef9c: LoadField: r1 = r0->field_7
    //     0x13bef9c: ldur            w1, [x0, #7]
    // 0x13befa0: cbz             w1, #0x13bf00c
    // 0x13befa4: ldr             x0, [fp, #0x20]
    // 0x13befa8: LoadField: r1 = r2->field_f
    //     0x13befa8: ldur            w1, [x2, #0xf]
    // 0x13befac: DecompressPointer r1
    //     0x13befac: add             x1, x1, HEAP, lsl #32
    // 0x13befb0: r0 = controller()
    //     0x13befb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13befb4: LoadField: r1 = r0->field_73
    //     0x13befb4: ldur            w1, [x0, #0x73]
    // 0x13befb8: DecompressPointer r1
    //     0x13befb8: add             x1, x1, HEAP, lsl #32
    // 0x13befbc: r2 = false
    //     0x13befbc: add             x2, NULL, #0x30  ; false
    // 0x13befc0: r0 = value=()
    //     0x13befc0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13befc4: ldur            x0, [fp, #-8]
    // 0x13befc8: LoadField: r1 = r0->field_f
    //     0x13befc8: ldur            w1, [x0, #0xf]
    // 0x13befcc: DecompressPointer r1
    //     0x13befcc: add             x1, x1, HEAP, lsl #32
    // 0x13befd0: r0 = controller()
    //     0x13befd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13befd4: mov             x1, x0
    // 0x13befd8: ldr             x0, [fp, #0x20]
    // 0x13befdc: r2 = LoadInt32Instr(r0)
    //     0x13befdc: sbfx            x2, x0, #1, #0x1f
    //     0x13befe0: tbz             w0, #0, #0x13befe8
    //     0x13befe4: ldur            x2, [x0, #7]
    // 0x13befe8: r0 = priceIncrement()
    //     0x13befe8: bl              #0x13bf240  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceIncrement
    // 0x13befec: ldur            x0, [fp, #-8]
    // 0x13beff0: LoadField: r1 = r0->field_f
    //     0x13beff0: ldur            w1, [x0, #0xf]
    // 0x13beff4: DecompressPointer r1
    //     0x13beff4: add             x1, x1, HEAP, lsl #32
    // 0x13beff8: r0 = controller()
    //     0x13beff8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13beffc: mov             x1, x0
    // 0x13bf000: ldr             x2, [fp, #0x18]
    // 0x13bf004: r0 = setProductCustomisationList()
    //     0x13bf004: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bf008: b               #0x13bf02c
    // 0x13bf00c: mov             x0, x2
    // 0x13bf010: LoadField: r1 = r0->field_f
    //     0x13bf010: ldur            w1, [x0, #0xf]
    // 0x13bf014: DecompressPointer r1
    //     0x13bf014: add             x1, x1, HEAP, lsl #32
    // 0x13bf018: r0 = controller()
    //     0x13bf018: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf01c: LoadField: r1 = r0->field_73
    //     0x13bf01c: ldur            w1, [x0, #0x73]
    // 0x13bf020: DecompressPointer r1
    //     0x13bf020: add             x1, x1, HEAP, lsl #32
    // 0x13bf024: ldr             x2, [fp, #0x10]
    // 0x13bf028: r0 = value=()
    //     0x13bf028: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf02c: r0 = Null
    //     0x13bf02c: mov             x0, NULL
    // 0x13bf030: LeaveFrame
    //     0x13bf030: mov             SP, fp
    //     0x13bf034: ldp             fp, lr, [SP], #0x10
    // 0x13bf038: ret
    //     0x13bf038: ret             
    // 0x13bf03c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf03c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf040: b               #0x13bef98
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>) {
    // ** addr: 0x13bf044, size: 0x9c
    // 0x13bf044: EnterFrame
    //     0x13bf044: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf048: mov             fp, SP
    // 0x13bf04c: AllocStack(0x8)
    //     0x13bf04c: sub             SP, SP, #8
    // 0x13bf050: SetupParameters()
    //     0x13bf050: ldr             x0, [fp, #0x20]
    //     0x13bf054: ldur            w2, [x0, #0x17]
    //     0x13bf058: add             x2, x2, HEAP, lsl #32
    //     0x13bf05c: stur            x2, [fp, #-8]
    // 0x13bf060: CheckStackOverflow
    //     0x13bf060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf064: cmp             SP, x16
    //     0x13bf068: b.ls            #0x13bf0d8
    // 0x13bf06c: LoadField: r1 = r2->field_f
    //     0x13bf06c: ldur            w1, [x2, #0xf]
    // 0x13bf070: DecompressPointer r1
    //     0x13bf070: add             x1, x1, HEAP, lsl #32
    // 0x13bf074: r0 = controller()
    //     0x13bf074: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf078: mov             x1, x0
    // 0x13bf07c: ldr             x0, [fp, #0x18]
    // 0x13bf080: r2 = LoadInt32Instr(r0)
    //     0x13bf080: sbfx            x2, x0, #1, #0x1f
    //     0x13bf084: tbz             w0, #0, #0x13bf08c
    //     0x13bf088: ldur            x2, [x0, #7]
    // 0x13bf08c: r0 = priceIncrement()
    //     0x13bf08c: bl              #0x13bf240  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceIncrement
    // 0x13bf090: ldur            x0, [fp, #-8]
    // 0x13bf094: LoadField: r1 = r0->field_f
    //     0x13bf094: ldur            w1, [x0, #0xf]
    // 0x13bf098: DecompressPointer r1
    //     0x13bf098: add             x1, x1, HEAP, lsl #32
    // 0x13bf09c: r0 = controller()
    //     0x13bf09c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf0a0: mov             x1, x0
    // 0x13bf0a4: ldr             x2, [fp, #0x10]
    // 0x13bf0a8: r0 = setProductCustomisationList()
    //     0x13bf0a8: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bf0ac: ldur            x0, [fp, #-8]
    // 0x13bf0b0: LoadField: r1 = r0->field_f
    //     0x13bf0b0: ldur            w1, [x0, #0xf]
    // 0x13bf0b4: DecompressPointer r1
    //     0x13bf0b4: add             x1, x1, HEAP, lsl #32
    // 0x13bf0b8: r0 = controller()
    //     0x13bf0b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf0bc: mov             x1, x0
    // 0x13bf0c0: r2 = false
    //     0x13bf0c0: add             x2, NULL, #0x30  ; false
    // 0x13bf0c4: r0 = cameraPickerMandatory=()
    //     0x13bf0c4: bl              #0x13bf0e0  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::cameraPickerMandatory=
    // 0x13bf0c8: r0 = Null
    //     0x13bf0c8: mov             x0, NULL
    // 0x13bf0cc: LeaveFrame
    //     0x13bf0cc: mov             SP, fp
    //     0x13bf0d0: ldp             fp, lr, [SP], #0x10
    // 0x13bf0d4: ret
    //     0x13bf0d4: ret             
    // 0x13bf0d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf0d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf0dc: b               #0x13bf06c
  }
  [closure] Null <anonymous closure>(dynamic, int, ProductCustomisation) {
    // ** addr: 0x13bf15c, size: 0x80
    // 0x13bf15c: EnterFrame
    //     0x13bf15c: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf160: mov             fp, SP
    // 0x13bf164: AllocStack(0x8)
    //     0x13bf164: sub             SP, SP, #8
    // 0x13bf168: SetupParameters()
    //     0x13bf168: ldr             x0, [fp, #0x20]
    //     0x13bf16c: ldur            w2, [x0, #0x17]
    //     0x13bf170: add             x2, x2, HEAP, lsl #32
    //     0x13bf174: stur            x2, [fp, #-8]
    // 0x13bf178: CheckStackOverflow
    //     0x13bf178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf17c: cmp             SP, x16
    //     0x13bf180: b.ls            #0x13bf1d4
    // 0x13bf184: LoadField: r1 = r2->field_f
    //     0x13bf184: ldur            w1, [x2, #0xf]
    // 0x13bf188: DecompressPointer r1
    //     0x13bf188: add             x1, x1, HEAP, lsl #32
    // 0x13bf18c: r0 = controller()
    //     0x13bf18c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf190: mov             x1, x0
    // 0x13bf194: ldr             x0, [fp, #0x18]
    // 0x13bf198: r2 = LoadInt32Instr(r0)
    //     0x13bf198: sbfx            x2, x0, #1, #0x1f
    //     0x13bf19c: tbz             w0, #0, #0x13bf1a4
    //     0x13bf1a0: ldur            x2, [x0, #7]
    // 0x13bf1a4: r0 = priceDecrement()
    //     0x13bf1a4: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13bf1a8: ldur            x0, [fp, #-8]
    // 0x13bf1ac: LoadField: r1 = r0->field_f
    //     0x13bf1ac: ldur            w1, [x0, #0xf]
    // 0x13bf1b0: DecompressPointer r1
    //     0x13bf1b0: add             x1, x1, HEAP, lsl #32
    // 0x13bf1b4: r0 = controller()
    //     0x13bf1b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf1b8: mov             x1, x0
    // 0x13bf1bc: ldr             x2, [fp, #0x10]
    // 0x13bf1c0: r0 = removeProductCustomisationList()
    //     0x13bf1c0: bl              #0x13be0b8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::removeProductCustomisationList
    // 0x13bf1c4: r0 = Null
    //     0x13bf1c4: mov             x0, NULL
    // 0x13bf1c8: LeaveFrame
    //     0x13bf1c8: mov             SP, fp
    //     0x13bf1cc: ldp             fp, lr, [SP], #0x10
    // 0x13bf1d0: ret
    //     0x13bf1d0: ret             
    // 0x13bf1d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf1d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf1d8: b               #0x13bf184
  }
  _ body(/* No info */) {
    // ** addr: 0x15058f0, size: 0x88
    // 0x15058f0: EnterFrame
    //     0x15058f0: stp             fp, lr, [SP, #-0x10]!
    //     0x15058f4: mov             fp, SP
    // 0x15058f8: AllocStack(0x18)
    //     0x15058f8: sub             SP, SP, #0x18
    // 0x15058fc: SetupParameters(CustomizedPage this /* r1 => r1, fp-0x8 */)
    //     0x15058fc: stur            x1, [fp, #-8]
    // 0x1505900: r1 = 1
    //     0x1505900: movz            x1, #0x1
    // 0x1505904: r0 = AllocateContext()
    //     0x1505904: bl              #0x16f6108  ; AllocateContextStub
    // 0x1505908: mov             x1, x0
    // 0x150590c: ldur            x0, [fp, #-8]
    // 0x1505910: stur            x1, [fp, #-0x10]
    // 0x1505914: StoreField: r1->field_f = r0
    //     0x1505914: stur            w0, [x1, #0xf]
    // 0x1505918: r0 = Obx()
    //     0x1505918: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x150591c: ldur            x2, [fp, #-0x10]
    // 0x1505920: r1 = Function '<anonymous closure>':.
    //     0x1505920: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba58] AnonymousClosure: (0x13bde04), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x1505924: ldr             x1, [x1, #0xa58]
    // 0x1505928: stur            x0, [fp, #-8]
    // 0x150592c: r0 = AllocateClosure()
    //     0x150592c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505930: mov             x1, x0
    // 0x1505934: ldur            x0, [fp, #-8]
    // 0x1505938: StoreField: r0->field_b = r1
    //     0x1505938: stur            w1, [x0, #0xb]
    // 0x150593c: r0 = WillPopScope()
    //     0x150593c: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1505940: mov             x3, x0
    // 0x1505944: ldur            x0, [fp, #-8]
    // 0x1505948: stur            x3, [fp, #-0x18]
    // 0x150594c: StoreField: r3->field_b = r0
    //     0x150594c: stur            w0, [x3, #0xb]
    // 0x1505950: ldur            x2, [fp, #-0x10]
    // 0x1505954: r1 = Function '<anonymous closure>':.
    //     0x1505954: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3ba60] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x1505958: ldr             x1, [x1, #0xa60]
    // 0x150595c: r0 = AllocateClosure()
    //     0x150595c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1505960: mov             x1, x0
    // 0x1505964: ldur            x0, [fp, #-0x18]
    // 0x1505968: StoreField: r0->field_f = r1
    //     0x1505968: stur            w1, [x0, #0xf]
    // 0x150596c: LeaveFrame
    //     0x150596c: mov             SP, fp
    //     0x1505970: ldp             fp, lr, [SP], #0x10
    // 0x1505974: ret
    //     0x1505974: ret             
  }
}
