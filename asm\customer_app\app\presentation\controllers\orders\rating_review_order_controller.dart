// lib: , url: package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart

// class id: 1049048, size: 0x8
class :: {
}

// class id: 1287, size: 0xe0, field offset: 0x48
class RatingReviewOrderController extends BaseController {

  late OrdersRepo ordersRepo; // offset: 0x48

  static void downloadCallback(String, int, int) {
    // ** addr: 0x91d038, size: 0xac
    // 0x91d038: EnterFrame
    //     0x91d038: stp             fp, lr, [SP, #-0x10]!
    //     0x91d03c: mov             fp, SP
    // 0x91d040: AllocStack(0x20)
    //     0x91d040: sub             SP, SP, #0x20
    // 0x91d044: CheckStackOverflow
    //     0x91d044: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d048: cmp             SP, x16
    //     0x91d04c: b.ls            #0x91d0dc
    // 0x91d050: r1 = "downloader_send_port"
    //     0x91d050: add             x1, PP, #0xc, lsl #12  ; [pp+0xc4e8] "downloader_send_port"
    //     0x91d054: ldr             x1, [x1, #0x4e8]
    // 0x91d058: r0 = __lookupPortByName$Method$FfiNative()
    //     0x91d058: bl              #0x91d0e4  ; [dart:ui] IsolateNameServer::__lookupPortByName$Method$FfiNative
    // 0x91d05c: stur            x0, [fp, #-8]
    // 0x91d060: cmp             w0, NULL
    // 0x91d064: b.eq            #0x91d0cc
    // 0x91d068: ldr             x6, [fp, #0x20]
    // 0x91d06c: ldr             x5, [fp, #0x18]
    // 0x91d070: ldr             x4, [fp, #0x10]
    // 0x91d074: r3 = 6
    //     0x91d074: movz            x3, #0x6
    // 0x91d078: mov             x2, x3
    // 0x91d07c: r1 = Null
    //     0x91d07c: mov             x1, NULL
    // 0x91d080: r0 = AllocateArray()
    //     0x91d080: bl              #0x16f7198  ; AllocateArrayStub
    // 0x91d084: mov             x2, x0
    // 0x91d088: ldr             x0, [fp, #0x20]
    // 0x91d08c: stur            x2, [fp, #-0x10]
    // 0x91d090: StoreField: r2->field_f = r0
    //     0x91d090: stur            w0, [x2, #0xf]
    // 0x91d094: ldr             x0, [fp, #0x18]
    // 0x91d098: StoreField: r2->field_13 = r0
    //     0x91d098: stur            w0, [x2, #0x13]
    // 0x91d09c: ldr             x0, [fp, #0x10]
    // 0x91d0a0: ArrayStore: r2[0] = r0  ; List_4
    //     0x91d0a0: stur            w0, [x2, #0x17]
    // 0x91d0a4: r1 = <Object>
    //     0x91d0a4: ldr             x1, [PP, #0x768]  ; [pp+0x768] TypeArguments: <Object>
    // 0x91d0a8: r0 = AllocateGrowableArray()
    //     0x91d0a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x91d0ac: mov             x1, x0
    // 0x91d0b0: ldur            x0, [fp, #-0x10]
    // 0x91d0b4: StoreField: r1->field_f = r0
    //     0x91d0b4: stur            w0, [x1, #0xf]
    // 0x91d0b8: r0 = 6
    //     0x91d0b8: movz            x0, #0x6
    // 0x91d0bc: StoreField: r1->field_b = r0
    //     0x91d0bc: stur            w0, [x1, #0xb]
    // 0x91d0c0: ldur            x16, [fp, #-8]
    // 0x91d0c4: stp             x1, x16, [SP]
    // 0x91d0c8: r0 = _sendInternal()
    //     0x91d0c8: bl              #0x6300a0  ; [dart:isolate] _SendPort::_sendInternal
    // 0x91d0cc: r0 = Null
    //     0x91d0cc: mov             x0, NULL
    // 0x91d0d0: LeaveFrame
    //     0x91d0d0: mov             SP, fp
    //     0x91d0d4: ldp             fp, lr, [SP], #0x10
    // 0x91d0d8: ret
    //     0x91d0d8: ret             
    // 0x91d0dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d0dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d0e0: b               #0x91d050
  }
  [closure] static void downloadCallback(dynamic, String, int, int) {
    // ** addr: 0x91d2bc, size: 0x44
    // 0x91d2bc: EnterFrame
    //     0x91d2bc: stp             fp, lr, [SP, #-0x10]!
    //     0x91d2c0: mov             fp, SP
    // 0x91d2c4: AllocStack(0x18)
    //     0x91d2c4: sub             SP, SP, #0x18
    // 0x91d2c8: CheckStackOverflow
    //     0x91d2c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d2cc: cmp             SP, x16
    //     0x91d2d0: b.ls            #0x91d2f8
    // 0x91d2d4: ldr             x16, [fp, #0x20]
    // 0x91d2d8: ldr             lr, [fp, #0x18]
    // 0x91d2dc: stp             lr, x16, [SP, #8]
    // 0x91d2e0: ldr             x16, [fp, #0x10]
    // 0x91d2e4: str             x16, [SP]
    // 0x91d2e8: r0 = downloadCallback()
    //     0x91d2e8: bl              #0x91d038  ; [package:customer_app/app/presentation/controllers/profile/policies_controller.dart] PoliciesController::downloadCallback
    // 0x91d2ec: LeaveFrame
    //     0x91d2ec: mov             SP, fp
    //     0x91d2f0: ldp             fp, lr, [SP], #0x10
    // 0x91d2f4: ret
    //     0x91d2f4: ret             
    // 0x91d2f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d2f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d2fc: b               #0x91d2d4
  }
  _ createReview(/* No info */) {
    // ** addr: 0x131f714, size: 0x2d8
    // 0x131f714: EnterFrame
    //     0x131f714: stp             fp, lr, [SP, #-0x10]!
    //     0x131f718: mov             fp, SP
    // 0x131f71c: AllocStack(0x50)
    //     0x131f71c: sub             SP, SP, #0x50
    // 0x131f720: SetupParameters(RatingReviewOrderController this /* r1 => r0, fp-0x10 */)
    //     0x131f720: mov             x0, x1
    //     0x131f724: stur            x1, [fp, #-0x10]
    // 0x131f728: CheckStackOverflow
    //     0x131f728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131f72c: cmp             SP, x16
    //     0x131f730: b.ls            #0x131f9d8
    // 0x131f734: LoadField: r2 = r0->field_4b
    //     0x131f734: ldur            w2, [x0, #0x4b]
    // 0x131f738: DecompressPointer r2
    //     0x131f738: add             x2, x2, HEAP, lsl #32
    // 0x131f73c: mov             x1, x2
    // 0x131f740: stur            x2, [fp, #-8]
    // 0x131f744: r0 = value()
    //     0x131f744: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f748: LoadField: r1 = r0->field_b
    //     0x131f748: ldur            w1, [x0, #0xb]
    // 0x131f74c: DecompressPointer r1
    //     0x131f74c: add             x1, x1, HEAP, lsl #32
    // 0x131f750: cmp             w1, NULL
    // 0x131f754: b.ne            #0x131f760
    // 0x131f758: r0 = Null
    //     0x131f758: mov             x0, NULL
    // 0x131f75c: b               #0x131f768
    // 0x131f760: LoadField: r0 = r1->field_b
    //     0x131f760: ldur            w0, [x1, #0xb]
    // 0x131f764: DecompressPointer r0
    //     0x131f764: add             x0, x0, HEAP, lsl #32
    // 0x131f768: ldur            x1, [fp, #-8]
    // 0x131f76c: stur            x0, [fp, #-0x18]
    // 0x131f770: r0 = value()
    //     0x131f770: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x131f774: LoadField: r1 = r0->field_b
    //     0x131f774: ldur            w1, [x0, #0xb]
    // 0x131f778: DecompressPointer r1
    //     0x131f778: add             x1, x1, HEAP, lsl #32
    // 0x131f77c: cmp             w1, NULL
    // 0x131f780: b.ne            #0x131f78c
    // 0x131f784: r0 = Null
    //     0x131f784: mov             x0, NULL
    // 0x131f788: b               #0x131f7b0
    // 0x131f78c: LoadField: r0 = r1->field_1f
    //     0x131f78c: ldur            w0, [x1, #0x1f]
    // 0x131f790: DecompressPointer r0
    //     0x131f790: add             x0, x0, HEAP, lsl #32
    // 0x131f794: cmp             w0, NULL
    // 0x131f798: b.ne            #0x131f7a4
    // 0x131f79c: r0 = Null
    //     0x131f79c: mov             x0, NULL
    // 0x131f7a0: b               #0x131f7b0
    // 0x131f7a4: LoadField: r1 = r0->field_13
    //     0x131f7a4: ldur            w1, [x0, #0x13]
    // 0x131f7a8: DecompressPointer r1
    //     0x131f7a8: add             x1, x1, HEAP, lsl #32
    // 0x131f7ac: mov             x0, x1
    // 0x131f7b0: ldur            x1, [fp, #-0x10]
    // 0x131f7b4: stur            x0, [fp, #-8]
    // 0x131f7b8: LoadField: r2 = r1->field_c3
    //     0x131f7b8: ldur            w2, [x1, #0xc3]
    // 0x131f7bc: DecompressPointer r2
    //     0x131f7bc: add             x2, x2, HEAP, lsl #32
    // 0x131f7c0: r16 = <double>
    //     0x131f7c0: ldr             x16, [PP, #0x3fd8]  ; [pp+0x3fd8] TypeArguments: <double>
    // 0x131f7c4: stp             x2, x16, [SP]
    // 0x131f7c8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x131f7c8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x131f7cc: r0 = RxNumExt.toInt()
    //     0x131f7cc: bl              #0x131f9ec  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxNumExt.toInt
    // 0x131f7d0: mov             x2, x0
    // 0x131f7d4: r0 = BoxInt64Instr(r2)
    //     0x131f7d4: sbfiz           x0, x2, #1, #0x1f
    //     0x131f7d8: cmp             x2, x0, asr #1
    //     0x131f7dc: b.eq            #0x131f7e8
    //     0x131f7e0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x131f7e4: stur            x2, [x0, #7]
    // 0x131f7e8: mov             x1, x0
    // 0x131f7ec: ldur            x0, [fp, #-8]
    // 0x131f7f0: cmp             w0, w1
    // 0x131f7f4: b.eq            #0x131f86c
    // 0x131f7f8: and             w16, w0, w1
    // 0x131f7fc: branchIfSmi(r16, 0x131f830)
    //     0x131f7fc: tbz             w16, #0, #0x131f830
    // 0x131f800: r16 = LoadClassIdInstr(r0)
    //     0x131f800: ldur            x16, [x0, #-1]
    //     0x131f804: ubfx            x16, x16, #0xc, #0x14
    // 0x131f808: cmp             x16, #0x3d
    // 0x131f80c: b.ne            #0x131f830
    // 0x131f810: r16 = LoadClassIdInstr(r1)
    //     0x131f810: ldur            x16, [x1, #-1]
    //     0x131f814: ubfx            x16, x16, #0xc, #0x14
    // 0x131f818: cmp             x16, #0x3d
    // 0x131f81c: b.ne            #0x131f830
    // 0x131f820: LoadField: r16 = r0->field_7
    //     0x131f820: ldur            x16, [x0, #7]
    // 0x131f824: LoadField: r17 = r1->field_7
    //     0x131f824: ldur            x17, [x1, #7]
    // 0x131f828: cmp             x16, x17
    // 0x131f82c: b.eq            #0x131f86c
    // 0x131f830: ldur            x1, [fp, #-0x10]
    // 0x131f834: LoadField: r0 = r1->field_c3
    //     0x131f834: ldur            w0, [x1, #0xc3]
    // 0x131f838: DecompressPointer r0
    //     0x131f838: add             x0, x0, HEAP, lsl #32
    // 0x131f83c: r16 = <double>
    //     0x131f83c: ldr             x16, [PP, #0x3fd8]  ; [pp+0x3fd8] TypeArguments: <double>
    // 0x131f840: stp             x0, x16, [SP]
    // 0x131f844: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x131f844: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x131f848: r0 = RxNumExt.toInt()
    //     0x131f848: bl              #0x131f9ec  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxNumExt.toInt
    // 0x131f84c: mov             x2, x0
    // 0x131f850: r0 = BoxInt64Instr(r2)
    //     0x131f850: sbfiz           x0, x2, #1, #0x1f
    //     0x131f854: cmp             x2, x0, asr #1
    //     0x131f858: b.eq            #0x131f864
    //     0x131f85c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x131f860: stur            x2, [x0, #7]
    // 0x131f864: mov             x1, x0
    // 0x131f868: b               #0x131f8b4
    // 0x131f86c: ldur            x1, [fp, #-0x10]
    // 0x131f870: r0 = configData()
    //     0x131f870: bl              #0x13187d8  ; [package:customer_app/app/presentation/controllers/browse/browse_controller.dart] BrowseController::configData
    // 0x131f874: LoadField: r1 = r0->field_b
    //     0x131f874: ldur            w1, [x0, #0xb]
    // 0x131f878: DecompressPointer r1
    //     0x131f878: add             x1, x1, HEAP, lsl #32
    // 0x131f87c: cmp             w1, NULL
    // 0x131f880: b.ne            #0x131f88c
    // 0x131f884: r0 = Null
    //     0x131f884: mov             x0, NULL
    // 0x131f888: b               #0x131f8b0
    // 0x131f88c: LoadField: r0 = r1->field_1f
    //     0x131f88c: ldur            w0, [x1, #0x1f]
    // 0x131f890: DecompressPointer r0
    //     0x131f890: add             x0, x0, HEAP, lsl #32
    // 0x131f894: cmp             w0, NULL
    // 0x131f898: b.ne            #0x131f8a4
    // 0x131f89c: r0 = Null
    //     0x131f89c: mov             x0, NULL
    // 0x131f8a0: b               #0x131f8b0
    // 0x131f8a4: LoadField: r1 = r0->field_13
    //     0x131f8a4: ldur            w1, [x0, #0x13]
    // 0x131f8a8: DecompressPointer r1
    //     0x131f8a8: add             x1, x1, HEAP, lsl #32
    // 0x131f8ac: mov             x0, x1
    // 0x131f8b0: mov             x1, x0
    // 0x131f8b4: ldur            x2, [fp, #-0x10]
    // 0x131f8b8: ldur            x0, [fp, #-0x18]
    // 0x131f8bc: stur            x1, [fp, #-0x28]
    // 0x131f8c0: LoadField: r3 = r2->field_bb
    //     0x131f8c0: ldur            w3, [x2, #0xbb]
    // 0x131f8c4: DecompressPointer r3
    //     0x131f8c4: add             x3, x3, HEAP, lsl #32
    // 0x131f8c8: stur            x3, [fp, #-0x20]
    // 0x131f8cc: LoadField: r4 = r2->field_8f
    //     0x131f8cc: ldur            w4, [x2, #0x8f]
    // 0x131f8d0: DecompressPointer r4
    //     0x131f8d0: add             x4, x4, HEAP, lsl #32
    // 0x131f8d4: LoadField: r5 = r4->field_27
    //     0x131f8d4: ldur            w5, [x4, #0x27]
    // 0x131f8d8: DecompressPointer r5
    //     0x131f8d8: add             x5, x5, HEAP, lsl #32
    // 0x131f8dc: LoadField: r4 = r5->field_7
    //     0x131f8dc: ldur            w4, [x5, #7]
    // 0x131f8e0: DecompressPointer r4
    //     0x131f8e0: add             x4, x4, HEAP, lsl #32
    // 0x131f8e4: stur            x4, [fp, #-8]
    // 0x131f8e8: r0 = ReviewRequestData()
    //     0x131f8e8: bl              #0x8ad5e0  ; AllocateReviewRequestDataStub -> ReviewRequestData (size=0x20)
    // 0x131f8ec: mov             x1, x0
    // 0x131f8f0: ldur            x0, [fp, #-0x18]
    // 0x131f8f4: StoreField: r1->field_7 = r0
    //     0x131f8f4: stur            w0, [x1, #7]
    // 0x131f8f8: ldur            x0, [fp, #-0x28]
    // 0x131f8fc: StoreField: r1->field_b = r0
    //     0x131f8fc: stur            w0, [x1, #0xb]
    // 0x131f900: ldur            x0, [fp, #-0x20]
    // 0x131f904: StoreField: r1->field_f = r0
    //     0x131f904: stur            w0, [x1, #0xf]
    // 0x131f908: ldur            x0, [fp, #-8]
    // 0x131f90c: StoreField: r1->field_13 = r0
    //     0x131f90c: stur            w0, [x1, #0x13]
    // 0x131f910: mov             x0, x1
    // 0x131f914: ldur            x4, [fp, #-0x10]
    // 0x131f918: StoreField: r4->field_77 = r0
    //     0x131f918: stur            w0, [x4, #0x77]
    //     0x131f91c: ldurb           w16, [x4, #-1]
    //     0x131f920: ldurb           w17, [x0, #-1]
    //     0x131f924: and             x16, x17, x16, lsr #2
    //     0x131f928: tst             x16, HEAP, lsr #32
    //     0x131f92c: b.eq            #0x131f934
    //     0x131f930: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x131f934: LoadField: r0 = r4->field_47
    //     0x131f934: ldur            w0, [x4, #0x47]
    // 0x131f938: DecompressPointer r0
    //     0x131f938: add             x0, x0, HEAP, lsl #32
    // 0x131f93c: r16 = Sentinel
    //     0x131f93c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x131f940: cmp             w0, w16
    // 0x131f944: b.eq            #0x131f9e0
    // 0x131f948: LoadField: r3 = r4->field_6f
    //     0x131f948: ldur            w3, [x4, #0x6f]
    // 0x131f94c: DecompressPointer r3
    //     0x131f94c: add             x3, x3, HEAP, lsl #32
    // 0x131f950: LoadField: r2 = r4->field_97
    //     0x131f950: ldur            w2, [x4, #0x97]
    // 0x131f954: DecompressPointer r2
    //     0x131f954: add             x2, x2, HEAP, lsl #32
    // 0x131f958: LoadField: r5 = r4->field_73
    //     0x131f958: ldur            w5, [x4, #0x73]
    // 0x131f95c: DecompressPointer r5
    //     0x131f95c: add             x5, x5, HEAP, lsl #32
    // 0x131f960: stp             x5, x2, [SP]
    // 0x131f964: mov             x2, x1
    // 0x131f968: mov             x1, x0
    // 0x131f96c: r4 = const [0, 0x5, 0x2, 0x3, deletedMedia, 0x4, media, 0x3, null]
    //     0x131f96c: add             x4, PP, #0x36, lsl #12  ; [pp+0x366f8] List(9) [0, 0x5, 0x2, 0x3, "deletedMedia", 0x4, "media", 0x3, Null]
    //     0x131f970: ldr             x4, [x4, #0x6f8]
    // 0x131f974: r0 = createReview()
    //     0x131f974: bl              #0x8aa664  ; [package:customer_app/app/data/repositories/order_tab/orders_repo_impl.dart] OrdersRepoImpl::createReview
    // 0x131f978: ldur            x2, [fp, #-0x10]
    // 0x131f97c: r1 = Function '_handleCreateReviewResponse@1165119249':.
    //     0x131f97c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36700] AnonymousClosure: (0x131fb18), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleCreateReviewResponse (0x131fb54)
    //     0x131f980: ldr             x1, [x1, #0x700]
    // 0x131f984: stur            x0, [fp, #-8]
    // 0x131f988: r0 = AllocateClosure()
    //     0x131f988: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131f98c: ldur            x2, [fp, #-0x10]
    // 0x131f990: r1 = Function '_handleError@1165119249':.
    //     0x131f990: add             x1, PP, #0x36, lsl #12  ; [pp+0x36708] AnonymousClosure: (0x131fa54), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleError (0x131fa90)
    //     0x131f994: ldr             x1, [x1, #0x708]
    // 0x131f998: stur            x0, [fp, #-0x18]
    // 0x131f99c: r0 = AllocateClosure()
    //     0x131f99c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131f9a0: r16 = <CreateReviewResponse>
    //     0x131f9a0: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf50] TypeArguments: <CreateReviewResponse>
    //     0x131f9a4: ldr             x16, [x16, #0xf50]
    // 0x131f9a8: ldur            lr, [fp, #-0x10]
    // 0x131f9ac: stp             lr, x16, [SP, #0x18]
    // 0x131f9b0: ldur            x16, [fp, #-8]
    // 0x131f9b4: stp             x0, x16, [SP, #8]
    // 0x131f9b8: ldur            x16, [fp, #-0x18]
    // 0x131f9bc: str             x16, [SP]
    // 0x131f9c0: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x131f9c0: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x131f9c4: r0 = callDataService()
    //     0x131f9c4: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x131f9c8: r0 = Null
    //     0x131f9c8: mov             x0, NULL
    // 0x131f9cc: LeaveFrame
    //     0x131f9cc: mov             SP, fp
    //     0x131f9d0: ldp             fp, lr, [SP], #0x10
    // 0x131f9d4: ret
    //     0x131f9d4: ret             
    // 0x131f9d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131f9d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131f9dc: b               #0x131f734
    // 0x131f9e0: r9 = ordersRepo
    //     0x131f9e0: add             x9, PP, #0x36, lsl #12  ; [pp+0x36710] Field <RatingReviewOrderController.ordersRepo>: late (offset: 0x48)
    //     0x131f9e4: ldr             x9, [x9, #0x710]
    // 0x131f9e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x131f9e8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleError(dynamic, Exception) {
    // ** addr: 0x131fa54, size: 0x3c
    // 0x131fa54: EnterFrame
    //     0x131fa54: stp             fp, lr, [SP, #-0x10]!
    //     0x131fa58: mov             fp, SP
    // 0x131fa5c: ldr             x0, [fp, #0x18]
    // 0x131fa60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x131fa60: ldur            w1, [x0, #0x17]
    // 0x131fa64: DecompressPointer r1
    //     0x131fa64: add             x1, x1, HEAP, lsl #32
    // 0x131fa68: CheckStackOverflow
    //     0x131fa68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131fa6c: cmp             SP, x16
    //     0x131fa70: b.ls            #0x131fa88
    // 0x131fa74: ldr             x2, [fp, #0x10]
    // 0x131fa78: r0 = _handleError()
    //     0x131fa78: bl              #0x131fa90  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleError
    // 0x131fa7c: LeaveFrame
    //     0x131fa7c: mov             SP, fp
    //     0x131fa80: ldp             fp, lr, [SP], #0x10
    // 0x131fa84: ret
    //     0x131fa84: ret             
    // 0x131fa88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131fa88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131fa8c: b               #0x131fa74
  }
  _ _handleError(/* No info */) {
    // ** addr: 0x131fa90, size: 0x88
    // 0x131fa90: EnterFrame
    //     0x131fa90: stp             fp, lr, [SP, #-0x10]!
    //     0x131fa94: mov             fp, SP
    // 0x131fa98: AllocStack(0x10)
    //     0x131fa98: sub             SP, SP, #0x10
    // 0x131fa9c: SetupParameters(RatingReviewOrderController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x131fa9c: mov             x4, x1
    //     0x131faa0: mov             x3, x2
    //     0x131faa4: stur            x1, [fp, #-8]
    //     0x131faa8: stur            x2, [fp, #-0x10]
    // 0x131faac: CheckStackOverflow
    //     0x131faac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131fab0: cmp             SP, x16
    //     0x131fab4: b.ls            #0x131fb10
    // 0x131fab8: mov             x0, x3
    // 0x131fabc: r2 = Null
    //     0x131fabc: mov             x2, NULL
    // 0x131fac0: r1 = Null
    //     0x131fac0: mov             x1, NULL
    // 0x131fac4: r4 = LoadClassIdInstr(r0)
    //     0x131fac4: ldur            x4, [x0, #-1]
    //     0x131fac8: ubfx            x4, x4, #0xc, #0x14
    // 0x131facc: r17 = -5018
    //     0x131facc: movn            x17, #0x1399
    // 0x131fad0: add             x4, x4, x17
    // 0x131fad4: cmp             x4, #8
    // 0x131fad8: b.ls            #0x131faec
    // 0x131fadc: r8 = BaseException
    //     0x131fadc: ldr             x8, [PP, #0x7e90]  ; [pp+0x7e90] Type: BaseException
    // 0x131fae0: r3 = Null
    //     0x131fae0: add             x3, PP, #0x36, lsl #12  ; [pp+0x36718] Null
    //     0x131fae4: ldr             x3, [x3, #0x718]
    // 0x131fae8: r0 = DefaultTypeTest()
    //     0x131fae8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x131faec: ldur            x0, [fp, #-0x10]
    // 0x131faf0: LoadField: r2 = r0->field_7
    //     0x131faf0: ldur            w2, [x0, #7]
    // 0x131faf4: DecompressPointer r2
    //     0x131faf4: add             x2, x2, HEAP, lsl #32
    // 0x131faf8: ldur            x1, [fp, #-8]
    // 0x131fafc: r0 = showErrorMessage()
    //     0x131fafc: bl              #0x89d444  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorMessage
    // 0x131fb00: r0 = Null
    //     0x131fb00: mov             x0, NULL
    // 0x131fb04: LeaveFrame
    //     0x131fb04: mov             SP, fp
    //     0x131fb08: ldp             fp, lr, [SP], #0x10
    // 0x131fb0c: ret
    //     0x131fb0c: ret             
    // 0x131fb10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131fb10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131fb14: b               #0x131fab8
  }
  [closure] dynamic _handleCreateReviewResponse(dynamic, dynamic) {
    // ** addr: 0x131fb18, size: 0x3c
    // 0x131fb18: EnterFrame
    //     0x131fb18: stp             fp, lr, [SP, #-0x10]!
    //     0x131fb1c: mov             fp, SP
    // 0x131fb20: ldr             x0, [fp, #0x18]
    // 0x131fb24: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x131fb24: ldur            w1, [x0, #0x17]
    // 0x131fb28: DecompressPointer r1
    //     0x131fb28: add             x1, x1, HEAP, lsl #32
    // 0x131fb2c: CheckStackOverflow
    //     0x131fb2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131fb30: cmp             SP, x16
    //     0x131fb34: b.ls            #0x131fb4c
    // 0x131fb38: ldr             x2, [fp, #0x10]
    // 0x131fb3c: r0 = _handleCreateReviewResponse()
    //     0x131fb3c: bl              #0x131fb54  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleCreateReviewResponse
    // 0x131fb40: LeaveFrame
    //     0x131fb40: mov             SP, fp
    //     0x131fb44: ldp             fp, lr, [SP], #0x10
    // 0x131fb48: ret
    //     0x131fb48: ret             
    // 0x131fb4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131fb4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131fb50: b               #0x131fb38
  }
  _ _handleCreateReviewResponse(/* No info */) {
    // ** addr: 0x131fb54, size: 0x9c
    // 0x131fb54: EnterFrame
    //     0x131fb54: stp             fp, lr, [SP, #-0x10]!
    //     0x131fb58: mov             fp, SP
    // 0x131fb5c: AllocStack(0x8)
    //     0x131fb5c: sub             SP, SP, #8
    // 0x131fb60: SetupParameters(RatingReviewOrderController this /* r1 => r0, fp-0x8 */)
    //     0x131fb60: mov             x0, x1
    //     0x131fb64: stur            x1, [fp, #-8]
    // 0x131fb68: CheckStackOverflow
    //     0x131fb68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131fb6c: cmp             SP, x16
    //     0x131fb70: b.ls            #0x131fbe8
    // 0x131fb74: mov             x1, x0
    // 0x131fb78: r0 = createReviewResponse=()
    //     0x131fb78: bl              #0x1321d9c  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::createReviewResponse=
    // 0x131fb7c: ldur            x0, [fp, #-8]
    // 0x131fb80: LoadField: r1 = r0->field_67
    //     0x131fb80: ldur            x1, [x0, #0x67]
    // 0x131fb84: add             x2, x1, #1
    // 0x131fb88: StoreField: r0->field_67 = r2
    //     0x131fb88: stur            x2, [x0, #0x67]
    // 0x131fb8c: r1 = true
    //     0x131fb8c: add             x1, NULL, #0x20  ; true
    // 0x131fb90: StoreField: r0->field_c7 = r1
    //     0x131fb90: stur            w1, [x0, #0xc7]
    // 0x131fb94: LoadField: r1 = r0->field_b7
    //     0x131fb94: ldur            w1, [x0, #0xb7]
    // 0x131fb98: DecompressPointer r1
    //     0x131fb98: add             x1, x1, HEAP, lsl #32
    // 0x131fb9c: r2 = false
    //     0x131fb9c: add             x2, NULL, #0x30  ; false
    // 0x131fba0: r0 = value=()
    //     0x131fba0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x131fba4: ldur            x0, [fp, #-8]
    // 0x131fba8: LoadField: r1 = r0->field_b3
    //     0x131fba8: ldur            w1, [x0, #0xb3]
    // 0x131fbac: DecompressPointer r1
    //     0x131fbac: add             x1, x1, HEAP, lsl #32
    // 0x131fbb0: r2 = false
    //     0x131fbb0: add             x2, NULL, #0x30  ; false
    // 0x131fbb4: r0 = value=()
    //     0x131fbb4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x131fbb8: ldur            x1, [fp, #-8]
    // 0x131fbbc: r0 = exchangeCheckoutResponse()
    //     0x131fbbc: bl              #0x8fc9bc  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_controller.dart] ExchangeCheckoutController::exchangeCheckoutResponse
    // 0x131fbc0: LoadField: r1 = r0->field_7
    //     0x131fbc0: ldur            w1, [x0, #7]
    // 0x131fbc4: DecompressPointer r1
    //     0x131fbc4: add             x1, x1, HEAP, lsl #32
    // 0x131fbc8: cmp             w1, #0x190
    // 0x131fbcc: b.ne            #0x131fbd8
    // 0x131fbd0: ldur            x1, [fp, #-8]
    // 0x131fbd4: r0 = getRatingReviewOrderData()
    //     0x131fbd4: bl              #0x131fbf0  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getRatingReviewOrderData
    // 0x131fbd8: r0 = Null
    //     0x131fbd8: mov             x0, NULL
    // 0x131fbdc: LeaveFrame
    //     0x131fbdc: mov             SP, fp
    //     0x131fbe0: ldp             fp, lr, [SP], #0x10
    // 0x131fbe4: ret
    //     0x131fbe4: ret             
    // 0x131fbe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131fbe8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131fbec: b               #0x131fb74
  }
  _ getRatingReviewOrderData(/* No info */) {
    // ** addr: 0x131fbf0, size: 0xb4
    // 0x131fbf0: EnterFrame
    //     0x131fbf0: stp             fp, lr, [SP, #-0x10]!
    //     0x131fbf4: mov             fp, SP
    // 0x131fbf8: AllocStack(0x40)
    //     0x131fbf8: sub             SP, SP, #0x40
    // 0x131fbfc: SetupParameters(RatingReviewOrderController this /* r1 => r0, fp-0x8 */)
    //     0x131fbfc: mov             x0, x1
    //     0x131fc00: stur            x1, [fp, #-8]
    // 0x131fc04: CheckStackOverflow
    //     0x131fc04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x131fc08: cmp             SP, x16
    //     0x131fc0c: b.ls            #0x131fc90
    // 0x131fc10: LoadField: r1 = r0->field_47
    //     0x131fc10: ldur            w1, [x0, #0x47]
    // 0x131fc14: DecompressPointer r1
    //     0x131fc14: add             x1, x1, HEAP, lsl #32
    // 0x131fc18: r16 = Sentinel
    //     0x131fc18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x131fc1c: cmp             w1, w16
    // 0x131fc20: b.eq            #0x131fc98
    // 0x131fc24: LoadField: r2 = r0->field_bb
    //     0x131fc24: ldur            w2, [x0, #0xbb]
    // 0x131fc28: DecompressPointer r2
    //     0x131fc28: add             x2, x2, HEAP, lsl #32
    // 0x131fc2c: r0 = getRatingReviewOrder()
    //     0x131fc2c: bl              #0x131fca4  ; [package:customer_app/app/data/repositories/order_tab/orders_repo_impl.dart] OrdersRepoImpl::getRatingReviewOrder
    // 0x131fc30: ldur            x2, [fp, #-8]
    // 0x131fc34: r1 = Function '_handleRatingReviewOrderDataResponse@1165119249':.
    //     0x131fc34: add             x1, PP, #0x36, lsl #12  ; [pp+0x36728] AnonymousClosure: (0x1320e78), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleRatingReviewOrderDataResponse (0x1320eb4)
    //     0x131fc38: ldr             x1, [x1, #0x728]
    // 0x131fc3c: stur            x0, [fp, #-0x10]
    // 0x131fc40: r0 = AllocateClosure()
    //     0x131fc40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131fc44: ldur            x2, [fp, #-8]
    // 0x131fc48: r1 = Function '_handleError@1165119249':.
    //     0x131fc48: add             x1, PP, #0x36, lsl #12  ; [pp+0x36708] AnonymousClosure: (0x131fa54), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleError (0x131fa90)
    //     0x131fc4c: ldr             x1, [x1, #0x708]
    // 0x131fc50: stur            x0, [fp, #-0x18]
    // 0x131fc54: r0 = AllocateClosure()
    //     0x131fc54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131fc58: r16 = <RatingReviewOrderResponse>
    //     0x131fc58: add             x16, PP, #0x36, lsl #12  ; [pp+0x36730] TypeArguments: <RatingReviewOrderResponse>
    //     0x131fc5c: ldr             x16, [x16, #0x730]
    // 0x131fc60: ldur            lr, [fp, #-8]
    // 0x131fc64: stp             lr, x16, [SP, #0x18]
    // 0x131fc68: ldur            x16, [fp, #-0x10]
    // 0x131fc6c: stp             x0, x16, [SP, #8]
    // 0x131fc70: ldur            x16, [fp, #-0x18]
    // 0x131fc74: str             x16, [SP]
    // 0x131fc78: r4 = const [0x1, 0x4, 0x4, 0x4, null]
    //     0x131fc78: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0x1, 0x4, 0x4, 0x4, Null]
    // 0x131fc7c: r0 = callDataService()
    //     0x131fc7c: bl              #0x860494  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::callDataService
    // 0x131fc80: r0 = Null
    //     0x131fc80: mov             x0, NULL
    // 0x131fc84: LeaveFrame
    //     0x131fc84: mov             SP, fp
    //     0x131fc88: ldp             fp, lr, [SP], #0x10
    // 0x131fc8c: ret
    //     0x131fc8c: ret             
    // 0x131fc90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131fc90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x131fc94: b               #0x131fc10
    // 0x131fc98: r9 = ordersRepo
    //     0x131fc98: add             x9, PP, #0x36, lsl #12  ; [pp+0x36710] Field <RatingReviewOrderController.ordersRepo>: late (offset: 0x48)
    //     0x131fc9c: ldr             x9, [x9, #0x710]
    // 0x131fca0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x131fca0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] dynamic _handleRatingReviewOrderDataResponse(dynamic, dynamic) {
    // ** addr: 0x1320e78, size: 0x3c
    // 0x1320e78: EnterFrame
    //     0x1320e78: stp             fp, lr, [SP, #-0x10]!
    //     0x1320e7c: mov             fp, SP
    // 0x1320e80: ldr             x0, [fp, #0x18]
    // 0x1320e84: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1320e84: ldur            w1, [x0, #0x17]
    // 0x1320e88: DecompressPointer r1
    //     0x1320e88: add             x1, x1, HEAP, lsl #32
    // 0x1320e8c: CheckStackOverflow
    //     0x1320e8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1320e90: cmp             SP, x16
    //     0x1320e94: b.ls            #0x1320eac
    // 0x1320e98: ldr             x2, [fp, #0x10]
    // 0x1320e9c: r0 = _handleRatingReviewOrderDataResponse()
    //     0x1320e9c: bl              #0x1320eb4  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleRatingReviewOrderDataResponse
    // 0x1320ea0: LeaveFrame
    //     0x1320ea0: mov             SP, fp
    //     0x1320ea4: ldp             fp, lr, [SP], #0x10
    // 0x1320ea8: ret
    //     0x1320ea8: ret             
    // 0x1320eac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1320eac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1320eb0: b               #0x1320e98
  }
  _ _handleRatingReviewOrderDataResponse(/* No info */) {
    // ** addr: 0x1320eb4, size: 0x914
    // 0x1320eb4: EnterFrame
    //     0x1320eb4: stp             fp, lr, [SP, #-0x10]!
    //     0x1320eb8: mov             fp, SP
    // 0x1320ebc: AllocStack(0x78)
    //     0x1320ebc: sub             SP, SP, #0x78
    // 0x1320ec0: SetupParameters(RatingReviewOrderController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1320ec0: stur            x1, [fp, #-8]
    //     0x1320ec4: stur            x2, [fp, #-0x10]
    // 0x1320ec8: CheckStackOverflow
    //     0x1320ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1320ecc: cmp             SP, x16
    //     0x1320ed0: b.ls            #0x132178c
    // 0x1320ed4: r1 = 1
    //     0x1320ed4: movz            x1, #0x1
    // 0x1320ed8: r0 = AllocateContext()
    //     0x1320ed8: bl              #0x16f6108  ; AllocateContextStub
    // 0x1320edc: mov             x3, x0
    // 0x1320ee0: ldur            x0, [fp, #-8]
    // 0x1320ee4: stur            x3, [fp, #-0x18]
    // 0x1320ee8: StoreField: r3->field_f = r0
    //     0x1320ee8: stur            w0, [x3, #0xf]
    // 0x1320eec: mov             x1, x0
    // 0x1320ef0: ldur            x2, [fp, #-0x10]
    // 0x1320ef4: r0 = ratingReviewOrderResponse=()
    //     0x1320ef4: bl              #0x13217c8  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::ratingReviewOrderResponse=
    // 0x1320ef8: ldur            x1, [fp, #-8]
    // 0x1320efc: LoadField: r0 = r1->field_bf
    //     0x1320efc: ldur            w0, [x1, #0xbf]
    // 0x1320f00: DecompressPointer r0
    //     0x1320f00: add             x0, x0, HEAP, lsl #32
    // 0x1320f04: r2 = LoadClassIdInstr(r0)
    //     0x1320f04: ldur            x2, [x0, #-1]
    //     0x1320f08: ubfx            x2, x2, #0xc, #0x14
    // 0x1320f0c: r16 = ""
    //     0x1320f0c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1320f10: stp             x16, x0, [SP]
    // 0x1320f14: mov             x0, x2
    // 0x1320f18: mov             lr, x0
    // 0x1320f1c: ldr             lr, [x21, lr, lsl #3]
    // 0x1320f20: blr             lr
    // 0x1320f24: tbz             w0, #4, #0x1320f40
    // 0x1320f28: ldur            x0, [fp, #-8]
    // 0x1320f2c: LoadField: r1 = r0->field_bf
    //     0x1320f2c: ldur            w1, [x0, #0xbf]
    // 0x1320f30: DecompressPointer r1
    //     0x1320f30: add             x1, x1, HEAP, lsl #32
    // 0x1320f34: mov             x2, x0
    // 0x1320f38: mov             x0, x1
    // 0x1320f3c: b               #0x1320f9c
    // 0x1320f40: ldur            x0, [fp, #-8]
    // 0x1320f44: LoadField: r1 = r0->field_4b
    //     0x1320f44: ldur            w1, [x0, #0x4b]
    // 0x1320f48: DecompressPointer r1
    //     0x1320f48: add             x1, x1, HEAP, lsl #32
    // 0x1320f4c: r0 = value()
    //     0x1320f4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1320f50: LoadField: r1 = r0->field_b
    //     0x1320f50: ldur            w1, [x0, #0xb]
    // 0x1320f54: DecompressPointer r1
    //     0x1320f54: add             x1, x1, HEAP, lsl #32
    // 0x1320f58: cmp             w1, NULL
    // 0x1320f5c: b.ne            #0x1320f68
    // 0x1320f60: r0 = Null
    //     0x1320f60: mov             x0, NULL
    // 0x1320f64: b               #0x1320f8c
    // 0x1320f68: LoadField: r0 = r1->field_1f
    //     0x1320f68: ldur            w0, [x1, #0x1f]
    // 0x1320f6c: DecompressPointer r0
    //     0x1320f6c: add             x0, x0, HEAP, lsl #32
    // 0x1320f70: cmp             w0, NULL
    // 0x1320f74: b.ne            #0x1320f80
    // 0x1320f78: r0 = Null
    //     0x1320f78: mov             x0, NULL
    // 0x1320f7c: b               #0x1320f8c
    // 0x1320f80: LoadField: r1 = r0->field_b
    //     0x1320f80: ldur            w1, [x0, #0xb]
    // 0x1320f84: DecompressPointer r1
    //     0x1320f84: add             x1, x1, HEAP, lsl #32
    // 0x1320f88: mov             x0, x1
    // 0x1320f8c: cmp             w0, NULL
    // 0x1320f90: b.ne            #0x1320f98
    // 0x1320f94: r0 = ""
    //     0x1320f94: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1320f98: ldur            x2, [fp, #-8]
    // 0x1320f9c: StoreField: r2->field_6f = r0
    //     0x1320f9c: stur            w0, [x2, #0x6f]
    //     0x1320fa0: ldurb           w16, [x2, #-1]
    //     0x1320fa4: ldurb           w17, [x0, #-1]
    //     0x1320fa8: and             x16, x17, x16, lsr #2
    //     0x1320fac: tst             x16, HEAP, lsr #32
    //     0x1320fb0: b.eq            #0x1320fb8
    //     0x1320fb4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1320fb8: LoadField: r0 = r2->field_c3
    //     0x1320fb8: ldur            w0, [x2, #0xc3]
    // 0x1320fbc: DecompressPointer r0
    //     0x1320fbc: add             x0, x0, HEAP, lsl #32
    // 0x1320fc0: stur            x0, [fp, #-0x20]
    // 0x1320fc4: LoadField: r3 = r2->field_4b
    //     0x1320fc4: ldur            w3, [x2, #0x4b]
    // 0x1320fc8: DecompressPointer r3
    //     0x1320fc8: add             x3, x3, HEAP, lsl #32
    // 0x1320fcc: mov             x1, x3
    // 0x1320fd0: stur            x3, [fp, #-0x10]
    // 0x1320fd4: r0 = value()
    //     0x1320fd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1320fd8: LoadField: r1 = r0->field_b
    //     0x1320fd8: ldur            w1, [x0, #0xb]
    // 0x1320fdc: DecompressPointer r1
    //     0x1320fdc: add             x1, x1, HEAP, lsl #32
    // 0x1320fe0: cmp             w1, NULL
    // 0x1320fe4: b.ne            #0x1320ff0
    // 0x1320fe8: r0 = Null
    //     0x1320fe8: mov             x0, NULL
    // 0x1320fec: b               #0x1321028
    // 0x1320ff0: LoadField: r0 = r1->field_1f
    //     0x1320ff0: ldur            w0, [x1, #0x1f]
    // 0x1320ff4: DecompressPointer r0
    //     0x1320ff4: add             x0, x0, HEAP, lsl #32
    // 0x1320ff8: cmp             w0, NULL
    // 0x1320ffc: b.ne            #0x1321008
    // 0x1321000: r0 = Null
    //     0x1321000: mov             x0, NULL
    // 0x1321004: b               #0x1321028
    // 0x1321008: LoadField: r1 = r0->field_13
    //     0x1321008: ldur            w1, [x0, #0x13]
    // 0x132100c: DecompressPointer r1
    //     0x132100c: add             x1, x1, HEAP, lsl #32
    // 0x1321010: cmp             w1, NULL
    // 0x1321014: b.ne            #0x1321020
    // 0x1321018: r0 = Null
    //     0x1321018: mov             x0, NULL
    // 0x132101c: b               #0x1321028
    // 0x1321020: stp             x1, NULL, [SP]
    // 0x1321024: r0 = _Double.fromInteger()
    //     0x1321024: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x1321028: cmp             w0, NULL
    // 0x132102c: b.ne            #0x1321038
    // 0x1321030: d0 = 0.000000
    //     0x1321030: eor             v0.16b, v0.16b, v0.16b
    // 0x1321034: b               #0x132103c
    // 0x1321038: LoadField: d0 = r0->field_7
    //     0x1321038: ldur            d0, [x0, #7]
    // 0x132103c: ldur            x0, [fp, #-8]
    // 0x1321040: r2 = inline_Allocate_Double()
    //     0x1321040: ldp             x2, x1, [THR, #0x50]  ; THR::top
    //     0x1321044: add             x2, x2, #0x10
    //     0x1321048: cmp             x1, x2
    //     0x132104c: b.ls            #0x1321794
    //     0x1321050: str             x2, [THR, #0x50]  ; THR::top
    //     0x1321054: sub             x2, x2, #0xf
    //     0x1321058: movz            x1, #0xe15c
    //     0x132105c: movk            x1, #0x3, lsl #16
    //     0x1321060: stur            x1, [x2, #-1]
    // 0x1321064: StoreField: r2->field_7 = d0
    //     0x1321064: stur            d0, [x2, #7]
    // 0x1321068: ldur            x1, [fp, #-0x20]
    // 0x132106c: r0 = value=()
    //     0x132106c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1321070: ldur            x0, [fp, #-8]
    // 0x1321074: LoadField: r2 = r0->field_8f
    //     0x1321074: ldur            w2, [x0, #0x8f]
    // 0x1321078: DecompressPointer r2
    //     0x1321078: add             x2, x2, HEAP, lsl #32
    // 0x132107c: ldur            x1, [fp, #-0x10]
    // 0x1321080: stur            x2, [fp, #-0x20]
    // 0x1321084: r0 = value()
    //     0x1321084: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1321088: LoadField: r1 = r0->field_b
    //     0x1321088: ldur            w1, [x0, #0xb]
    // 0x132108c: DecompressPointer r1
    //     0x132108c: add             x1, x1, HEAP, lsl #32
    // 0x1321090: cmp             w1, NULL
    // 0x1321094: b.ne            #0x13210a0
    // 0x1321098: r0 = Null
    //     0x1321098: mov             x0, NULL
    // 0x132109c: b               #0x13210c4
    // 0x13210a0: LoadField: r0 = r1->field_1f
    //     0x13210a0: ldur            w0, [x1, #0x1f]
    // 0x13210a4: DecompressPointer r0
    //     0x13210a4: add             x0, x0, HEAP, lsl #32
    // 0x13210a8: cmp             w0, NULL
    // 0x13210ac: b.ne            #0x13210b8
    // 0x13210b0: r0 = Null
    //     0x13210b0: mov             x0, NULL
    // 0x13210b4: b               #0x13210c4
    // 0x13210b8: LoadField: r1 = r0->field_1b
    //     0x13210b8: ldur            w1, [x0, #0x1b]
    // 0x13210bc: DecompressPointer r1
    //     0x13210bc: add             x1, x1, HEAP, lsl #32
    // 0x13210c0: mov             x0, x1
    // 0x13210c4: cmp             w0, NULL
    // 0x13210c8: b.ne            #0x13210d4
    // 0x13210cc: r2 = ""
    //     0x13210cc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13210d0: b               #0x13210d8
    // 0x13210d4: mov             x2, x0
    // 0x13210d8: ldur            x1, [fp, #-0x20]
    // 0x13210dc: r0 = text=()
    //     0x13210dc: bl              #0x80121c  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::text=
    // 0x13210e0: ldur            x1, [fp, #-0x10]
    // 0x13210e4: r0 = value()
    //     0x13210e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13210e8: LoadField: r1 = r0->field_b
    //     0x13210e8: ldur            w1, [x0, #0xb]
    // 0x13210ec: DecompressPointer r1
    //     0x13210ec: add             x1, x1, HEAP, lsl #32
    // 0x13210f0: cmp             w1, NULL
    // 0x13210f4: b.eq            #0x13211c0
    // 0x13210f8: LoadField: r0 = r1->field_1f
    //     0x13210f8: ldur            w0, [x1, #0x1f]
    // 0x13210fc: DecompressPointer r0
    //     0x13210fc: add             x0, x0, HEAP, lsl #32
    // 0x1321100: cmp             w0, NULL
    // 0x1321104: b.eq            #0x13211c0
    // 0x1321108: LoadField: r3 = r0->field_23
    //     0x1321108: ldur            w3, [x0, #0x23]
    // 0x132110c: DecompressPointer r3
    //     0x132110c: add             x3, x3, HEAP, lsl #32
    // 0x1321110: ldur            x2, [fp, #-0x18]
    // 0x1321114: stur            x3, [fp, #-0x20]
    // 0x1321118: r1 = Function '<anonymous closure>':.
    //     0x1321118: add             x1, PP, #0x36, lsl #12  ; [pp+0x36738] AnonymousClosure: (0x1321b88), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleRatingReviewOrderDataResponse (0x1320eb4)
    //     0x132111c: ldr             x1, [x1, #0x738]
    // 0x1321120: r0 = AllocateClosure()
    //     0x1321120: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1321124: mov             x3, x0
    // 0x1321128: ldur            x2, [fp, #-0x20]
    // 0x132112c: stur            x3, [fp, #-0x38]
    // 0x1321130: LoadField: r4 = r2->field_b
    //     0x1321130: ldur            w4, [x2, #0xb]
    // 0x1321134: stur            x4, [fp, #-0x30]
    // 0x1321138: r0 = LoadInt32Instr(r4)
    //     0x1321138: sbfx            x0, x4, #1, #0x1f
    // 0x132113c: r5 = 0
    //     0x132113c: movz            x5, #0
    // 0x1321140: stur            x5, [fp, #-0x28]
    // 0x1321144: CheckStackOverflow
    //     0x1321144: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1321148: cmp             SP, x16
    //     0x132114c: b.ls            #0x13217b0
    // 0x1321150: cmp             x5, x0
    // 0x1321154: b.ge            #0x13211c0
    // 0x1321158: mov             x1, x5
    // 0x132115c: cmp             x1, x0
    // 0x1321160: b.hs            #0x13217b8
    // 0x1321164: LoadField: r0 = r2->field_f
    //     0x1321164: ldur            w0, [x2, #0xf]
    // 0x1321168: DecompressPointer r0
    //     0x1321168: add             x0, x0, HEAP, lsl #32
    // 0x132116c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x132116c: add             x16, x0, x5, lsl #2
    //     0x1321170: ldur            w1, [x16, #0xf]
    // 0x1321174: DecompressPointer r1
    //     0x1321174: add             x1, x1, HEAP, lsl #32
    // 0x1321178: stp             x1, x3, [SP]
    // 0x132117c: mov             x0, x3
    // 0x1321180: ClosureCall
    //     0x1321180: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x1321184: ldur            x2, [x0, #0x1f]
    //     0x1321188: blr             x2
    // 0x132118c: ldur            x1, [fp, #-0x20]
    // 0x1321190: LoadField: r0 = r1->field_b
    //     0x1321190: ldur            w0, [x1, #0xb]
    // 0x1321194: ldur            x2, [fp, #-0x30]
    // 0x1321198: cmp             w0, w2
    // 0x132119c: b.ne            #0x1321754
    // 0x13211a0: ldur            x3, [fp, #-0x28]
    // 0x13211a4: add             x5, x3, #1
    // 0x13211a8: r3 = LoadInt32Instr(r0)
    //     0x13211a8: sbfx            x3, x0, #1, #0x1f
    // 0x13211ac: mov             x0, x3
    // 0x13211b0: mov             x4, x2
    // 0x13211b4: mov             x2, x1
    // 0x13211b8: ldur            x3, [fp, #-0x38]
    // 0x13211bc: b               #0x1321140
    // 0x13211c0: ldur            x0, [fp, #-8]
    // 0x13211c4: LoadField: r1 = r0->field_97
    //     0x13211c4: ldur            w1, [x0, #0x97]
    // 0x13211c8: DecompressPointer r1
    //     0x13211c8: add             x1, x1, HEAP, lsl #32
    // 0x13211cc: r0 = clear()
    //     0x13211cc: bl              #0x8ba99c  ; [dart:collection] ListBase::clear
    // 0x13211d0: ldur            x1, [fp, #-0x10]
    // 0x13211d4: r0 = value()
    //     0x13211d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13211d8: LoadField: r1 = r0->field_b
    //     0x13211d8: ldur            w1, [x0, #0xb]
    // 0x13211dc: DecompressPointer r1
    //     0x13211dc: add             x1, x1, HEAP, lsl #32
    // 0x13211e0: cmp             w1, NULL
    // 0x13211e4: b.ne            #0x13211f0
    // 0x13211e8: r0 = Null
    //     0x13211e8: mov             x0, NULL
    // 0x13211ec: b               #0x1321228
    // 0x13211f0: LoadField: r0 = r1->field_1f
    //     0x13211f0: ldur            w0, [x1, #0x1f]
    // 0x13211f4: DecompressPointer r0
    //     0x13211f4: add             x0, x0, HEAP, lsl #32
    // 0x13211f8: cmp             w0, NULL
    // 0x13211fc: b.ne            #0x1321208
    // 0x1321200: r0 = Null
    //     0x1321200: mov             x0, NULL
    // 0x1321204: b               #0x1321228
    // 0x1321208: LoadField: r1 = r0->field_23
    //     0x1321208: ldur            w1, [x0, #0x23]
    // 0x132120c: DecompressPointer r1
    //     0x132120c: add             x1, x1, HEAP, lsl #32
    // 0x1321210: LoadField: r0 = r1->field_b
    //     0x1321210: ldur            w0, [x1, #0xb]
    // 0x1321214: cbnz            w0, #0x1321220
    // 0x1321218: r1 = false
    //     0x1321218: add             x1, NULL, #0x30  ; false
    // 0x132121c: b               #0x1321224
    // 0x1321220: r1 = true
    //     0x1321220: add             x1, NULL, #0x20  ; true
    // 0x1321224: mov             x0, x1
    // 0x1321228: cmp             w0, NULL
    // 0x132122c: b.eq            #0x1321488
    // 0x1321230: tbnz            w0, #4, #0x1321488
    // 0x1321234: ldur            x1, [fp, #-0x10]
    // 0x1321238: r0 = value()
    //     0x1321238: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x132123c: LoadField: r1 = r0->field_b
    //     0x132123c: ldur            w1, [x0, #0xb]
    // 0x1321240: DecompressPointer r1
    //     0x1321240: add             x1, x1, HEAP, lsl #32
    // 0x1321244: cmp             w1, NULL
    // 0x1321248: b.eq            #0x1321314
    // 0x132124c: LoadField: r0 = r1->field_1f
    //     0x132124c: ldur            w0, [x1, #0x1f]
    // 0x1321250: DecompressPointer r0
    //     0x1321250: add             x0, x0, HEAP, lsl #32
    // 0x1321254: cmp             w0, NULL
    // 0x1321258: b.eq            #0x1321314
    // 0x132125c: LoadField: r3 = r0->field_23
    //     0x132125c: ldur            w3, [x0, #0x23]
    // 0x1321260: DecompressPointer r3
    //     0x1321260: add             x3, x3, HEAP, lsl #32
    // 0x1321264: ldur            x2, [fp, #-0x18]
    // 0x1321268: stur            x3, [fp, #-0x30]
    // 0x132126c: r1 = Function '<anonymous closure>':.
    //     0x132126c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36740] AnonymousClosure: (0x1321900), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleRatingReviewOrderDataResponse (0x1320eb4)
    //     0x1321270: ldr             x1, [x1, #0x740]
    // 0x1321274: r0 = AllocateClosure()
    //     0x1321274: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1321278: mov             x3, x0
    // 0x132127c: ldur            x2, [fp, #-0x30]
    // 0x1321280: stur            x3, [fp, #-0x38]
    // 0x1321284: LoadField: r4 = r2->field_b
    //     0x1321284: ldur            w4, [x2, #0xb]
    // 0x1321288: stur            x4, [fp, #-0x18]
    // 0x132128c: r0 = LoadInt32Instr(r4)
    //     0x132128c: sbfx            x0, x4, #1, #0x1f
    // 0x1321290: r5 = 0
    //     0x1321290: movz            x5, #0
    // 0x1321294: stur            x5, [fp, #-0x28]
    // 0x1321298: CheckStackOverflow
    //     0x1321298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x132129c: cmp             SP, x16
    //     0x13212a0: b.ls            #0x13217bc
    // 0x13212a4: cmp             x5, x0
    // 0x13212a8: b.ge            #0x1321314
    // 0x13212ac: mov             x1, x5
    // 0x13212b0: cmp             x1, x0
    // 0x13212b4: b.hs            #0x13217c4
    // 0x13212b8: LoadField: r0 = r2->field_f
    //     0x13212b8: ldur            w0, [x2, #0xf]
    // 0x13212bc: DecompressPointer r0
    //     0x13212bc: add             x0, x0, HEAP, lsl #32
    // 0x13212c0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x13212c0: add             x16, x0, x5, lsl #2
    //     0x13212c4: ldur            w1, [x16, #0xf]
    // 0x13212c8: DecompressPointer r1
    //     0x13212c8: add             x1, x1, HEAP, lsl #32
    // 0x13212cc: stp             x1, x3, [SP]
    // 0x13212d0: mov             x0, x3
    // 0x13212d4: ClosureCall
    //     0x13212d4: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x13212d8: ldur            x2, [x0, #0x1f]
    //     0x13212dc: blr             x2
    // 0x13212e0: ldur            x1, [fp, #-0x30]
    // 0x13212e4: LoadField: r0 = r1->field_b
    //     0x13212e4: ldur            w0, [x1, #0xb]
    // 0x13212e8: ldur            x2, [fp, #-0x18]
    // 0x13212ec: cmp             w0, w2
    // 0x13212f0: b.ne            #0x1321770
    // 0x13212f4: ldur            x3, [fp, #-0x28]
    // 0x13212f8: add             x5, x3, #1
    // 0x13212fc: r3 = LoadInt32Instr(r0)
    //     0x13212fc: sbfx            x3, x0, #1, #0x1f
    // 0x1321300: mov             x0, x3
    // 0x1321304: mov             x4, x2
    // 0x1321308: mov             x2, x1
    // 0x132130c: ldur            x3, [fp, #-0x38]
    // 0x1321310: b               #0x1321294
    // 0x1321314: ldur            x0, [fp, #-8]
    // 0x1321318: LoadField: r2 = r0->field_9b
    //     0x1321318: ldur            w2, [x0, #0x9b]
    // 0x132131c: DecompressPointer r2
    //     0x132131c: add             x2, x2, HEAP, lsl #32
    // 0x1321320: ldur            x1, [fp, #-0x10]
    // 0x1321324: stur            x2, [fp, #-0x18]
    // 0x1321328: r0 = value()
    //     0x1321328: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x132132c: LoadField: r1 = r0->field_b
    //     0x132132c: ldur            w1, [x0, #0xb]
    // 0x1321330: DecompressPointer r1
    //     0x1321330: add             x1, x1, HEAP, lsl #32
    // 0x1321334: cmp             w1, NULL
    // 0x1321338: b.ne            #0x1321344
    // 0x132133c: r0 = Null
    //     0x132133c: mov             x0, NULL
    // 0x1321340: b               #0x132138c
    // 0x1321344: LoadField: r0 = r1->field_1f
    //     0x1321344: ldur            w0, [x1, #0x1f]
    // 0x1321348: DecompressPointer r0
    //     0x1321348: add             x0, x0, HEAP, lsl #32
    // 0x132134c: cmp             w0, NULL
    // 0x1321350: b.ne            #0x132135c
    // 0x1321354: r0 = Null
    //     0x1321354: mov             x0, NULL
    // 0x1321358: b               #0x132138c
    // 0x132135c: LoadField: r3 = r0->field_23
    //     0x132135c: ldur            w3, [x0, #0x23]
    // 0x1321360: DecompressPointer r3
    //     0x1321360: add             x3, x3, HEAP, lsl #32
    // 0x1321364: stur            x3, [fp, #-0x38]
    // 0x1321368: r1 = Function '<anonymous closure>':.
    //     0x1321368: add             x1, PP, #0x36, lsl #12  ; [pp+0x36748] AnonymousClosure: (0x13218ac), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleRatingReviewOrderDataResponse (0x1320eb4)
    //     0x132136c: ldr             x1, [x1, #0x748]
    // 0x1321370: r2 = Null
    //     0x1321370: mov             x2, NULL
    // 0x1321374: r0 = AllocateClosure()
    //     0x1321374: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1321378: ldur            x1, [fp, #-0x38]
    // 0x132137c: mov             x2, x0
    // 0x1321380: r0 = where()
    //     0x1321380: bl              #0x7d9800  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x1321384: str             x0, [SP]
    // 0x1321388: r0 = length()
    //     0x1321388: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x132138c: cmp             w0, NULL
    // 0x1321390: b.ne            #0x132139c
    // 0x1321394: r2 = 0
    //     0x1321394: movz            x2, #0
    // 0x1321398: b               #0x13213ac
    // 0x132139c: r1 = LoadInt32Instr(r0)
    //     0x132139c: sbfx            x1, x0, #1, #0x1f
    //     0x13213a0: tbz             w0, #0, #0x13213a8
    //     0x13213a4: ldur            x1, [x0, #7]
    // 0x13213a8: mov             x2, x1
    // 0x13213ac: ldur            x3, [fp, #-8]
    // 0x13213b0: r0 = BoxInt64Instr(r2)
    //     0x13213b0: sbfiz           x0, x2, #1, #0x1f
    //     0x13213b4: cmp             x2, x0, asr #1
    //     0x13213b8: b.eq            #0x13213c4
    //     0x13213bc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x13213c0: stur            x2, [x0, #7]
    // 0x13213c4: ldur            x1, [fp, #-0x18]
    // 0x13213c8: mov             x2, x0
    // 0x13213cc: r0 = value=()
    //     0x13213cc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13213d0: ldur            x0, [fp, #-8]
    // 0x13213d4: LoadField: r2 = r0->field_9f
    //     0x13213d4: ldur            w2, [x0, #0x9f]
    // 0x13213d8: DecompressPointer r2
    //     0x13213d8: add             x2, x2, HEAP, lsl #32
    // 0x13213dc: ldur            x1, [fp, #-0x10]
    // 0x13213e0: stur            x2, [fp, #-0x18]
    // 0x13213e4: r0 = value()
    //     0x13213e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13213e8: LoadField: r1 = r0->field_b
    //     0x13213e8: ldur            w1, [x0, #0xb]
    // 0x13213ec: DecompressPointer r1
    //     0x13213ec: add             x1, x1, HEAP, lsl #32
    // 0x13213f0: cmp             w1, NULL
    // 0x13213f4: b.ne            #0x1321400
    // 0x13213f8: r0 = Null
    //     0x13213f8: mov             x0, NULL
    // 0x13213fc: b               #0x1321448
    // 0x1321400: LoadField: r0 = r1->field_1f
    //     0x1321400: ldur            w0, [x1, #0x1f]
    // 0x1321404: DecompressPointer r0
    //     0x1321404: add             x0, x0, HEAP, lsl #32
    // 0x1321408: cmp             w0, NULL
    // 0x132140c: b.ne            #0x1321418
    // 0x1321410: r0 = Null
    //     0x1321410: mov             x0, NULL
    // 0x1321414: b               #0x1321448
    // 0x1321418: LoadField: r3 = r0->field_23
    //     0x1321418: ldur            w3, [x0, #0x23]
    // 0x132141c: DecompressPointer r3
    //     0x132141c: add             x3, x3, HEAP, lsl #32
    // 0x1321420: stur            x3, [fp, #-0x38]
    // 0x1321424: r1 = Function '<anonymous closure>':.
    //     0x1321424: add             x1, PP, #0x36, lsl #12  ; [pp+0x36750] AnonymousClosure: (0x1321854), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_handleRatingReviewOrderDataResponse (0x1320eb4)
    //     0x1321428: ldr             x1, [x1, #0x750]
    // 0x132142c: r2 = Null
    //     0x132142c: mov             x2, NULL
    // 0x1321430: r0 = AllocateClosure()
    //     0x1321430: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1321434: ldur            x1, [fp, #-0x38]
    // 0x1321438: mov             x2, x0
    // 0x132143c: r0 = where()
    //     0x132143c: bl              #0x7d9800  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::where
    // 0x1321440: str             x0, [SP]
    // 0x1321444: r0 = length()
    //     0x1321444: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0x1321448: cmp             w0, NULL
    // 0x132144c: b.ne            #0x1321458
    // 0x1321450: r2 = 0
    //     0x1321450: movz            x2, #0
    // 0x1321454: b               #0x1321468
    // 0x1321458: r1 = LoadInt32Instr(r0)
    //     0x1321458: sbfx            x1, x0, #1, #0x1f
    //     0x132145c: tbz             w0, #0, #0x1321464
    //     0x1321460: ldur            x1, [x0, #7]
    // 0x1321464: mov             x2, x1
    // 0x1321468: r0 = BoxInt64Instr(r2)
    //     0x1321468: sbfiz           x0, x2, #1, #0x1f
    //     0x132146c: cmp             x2, x0, asr #1
    //     0x1321470: b.eq            #0x132147c
    //     0x1321474: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x1321478: stur            x2, [x0, #7]
    // 0x132147c: ldur            x1, [fp, #-0x18]
    // 0x1321480: mov             x2, x0
    // 0x1321484: r0 = value=()
    //     0x1321484: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1321488: ldur            x1, [fp, #-0x10]
    // 0x132148c: r0 = value()
    //     0x132148c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1321490: LoadField: r1 = r0->field_7
    //     0x1321490: ldur            w1, [x0, #7]
    // 0x1321494: DecompressPointer r1
    //     0x1321494: add             x1, x1, HEAP, lsl #32
    // 0x1321498: cmp             w1, #0x190
    // 0x132149c: b.ne            #0x13214c0
    // 0x13214a0: ldur            x0, [fp, #-8]
    // 0x13214a4: LoadField: r1 = r0->field_c7
    //     0x13214a4: ldur            w1, [x0, #0xc7]
    // 0x13214a8: DecompressPointer r1
    //     0x13214a8: add             x1, x1, HEAP, lsl #32
    // 0x13214ac: tbnz            w1, #4, #0x13214c0
    // 0x13214b0: LoadField: r1 = r0->field_db
    //     0x13214b0: ldur            w1, [x0, #0xdb]
    // 0x13214b4: DecompressPointer r1
    //     0x13214b4: add             x1, x1, HEAP, lsl #32
    // 0x13214b8: r2 = true
    //     0x13214b8: add             x2, NULL, #0x20  ; true
    // 0x13214bc: r0 = value=()
    //     0x13214bc: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13214c0: ldur            x1, [fp, #-0x10]
    // 0x13214c4: r0 = value()
    //     0x13214c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13214c8: LoadField: r1 = r0->field_7
    //     0x13214c8: ldur            w1, [x0, #7]
    // 0x13214cc: DecompressPointer r1
    //     0x13214cc: add             x1, x1, HEAP, lsl #32
    // 0x13214d0: cmp             w1, #0x190
    // 0x13214d4: b.ne            #0x1321744
    // 0x13214d8: ldur            x0, [fp, #-8]
    // 0x13214dc: LoadField: r1 = r0->field_cb
    //     0x13214dc: ldur            w1, [x0, #0xcb]
    // 0x13214e0: DecompressPointer r1
    //     0x13214e0: add             x1, x1, HEAP, lsl #32
    // 0x13214e4: tbnz            w1, #4, #0x1321744
    // 0x13214e8: ldur            x1, [fp, #-0x10]
    // 0x13214ec: r0 = value()
    //     0x13214ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13214f0: LoadField: r1 = r0->field_b
    //     0x13214f0: ldur            w1, [x0, #0xb]
    // 0x13214f4: DecompressPointer r1
    //     0x13214f4: add             x1, x1, HEAP, lsl #32
    // 0x13214f8: cmp             w1, NULL
    // 0x13214fc: b.ne            #0x1321508
    // 0x1321500: r0 = Null
    //     0x1321500: mov             x0, NULL
    // 0x1321504: b               #0x132152c
    // 0x1321508: LoadField: r0 = r1->field_1f
    //     0x1321508: ldur            w0, [x1, #0x1f]
    // 0x132150c: DecompressPointer r0
    //     0x132150c: add             x0, x0, HEAP, lsl #32
    // 0x1321510: cmp             w0, NULL
    // 0x1321514: b.ne            #0x1321520
    // 0x1321518: r0 = Null
    //     0x1321518: mov             x0, NULL
    // 0x132151c: b               #0x132152c
    // 0x1321520: LoadField: r1 = r0->field_13
    //     0x1321520: ldur            w1, [x0, #0x13]
    // 0x1321524: DecompressPointer r1
    //     0x1321524: add             x1, x1, HEAP, lsl #32
    // 0x1321528: mov             x0, x1
    // 0x132152c: ldur            x1, [fp, #-0x10]
    // 0x1321530: stur            x0, [fp, #-0x18]
    // 0x1321534: r0 = value()
    //     0x1321534: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1321538: LoadField: r1 = r0->field_b
    //     0x1321538: ldur            w1, [x0, #0xb]
    // 0x132153c: DecompressPointer r1
    //     0x132153c: add             x1, x1, HEAP, lsl #32
    // 0x1321540: cmp             w1, NULL
    // 0x1321544: b.ne            #0x1321550
    // 0x1321548: r0 = Null
    //     0x1321548: mov             x0, NULL
    // 0x132154c: b               #0x1321574
    // 0x1321550: LoadField: r0 = r1->field_1f
    //     0x1321550: ldur            w0, [x1, #0x1f]
    // 0x1321554: DecompressPointer r0
    //     0x1321554: add             x0, x0, HEAP, lsl #32
    // 0x1321558: cmp             w0, NULL
    // 0x132155c: b.ne            #0x1321568
    // 0x1321560: r0 = Null
    //     0x1321560: mov             x0, NULL
    // 0x1321564: b               #0x1321574
    // 0x1321568: LoadField: r1 = r0->field_1b
    //     0x1321568: ldur            w1, [x0, #0x1b]
    // 0x132156c: DecompressPointer r1
    //     0x132156c: add             x1, x1, HEAP, lsl #32
    // 0x1321570: mov             x0, x1
    // 0x1321574: cmp             w0, NULL
    // 0x1321578: b.ne            #0x1321584
    // 0x132157c: r2 = ""
    //     0x132157c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1321580: b               #0x1321588
    // 0x1321584: mov             x2, x0
    // 0x1321588: ldur            x0, [fp, #-8]
    // 0x132158c: stur            x2, [fp, #-0x50]
    // 0x1321590: LoadField: r1 = r0->field_cf
    //     0x1321590: ldur            w1, [x0, #0xcf]
    // 0x1321594: DecompressPointer r1
    //     0x1321594: add             x1, x1, HEAP, lsl #32
    // 0x1321598: cmp             w1, NULL
    // 0x132159c: b.ne            #0x13215a8
    // 0x13215a0: r3 = Null
    //     0x13215a0: mov             x3, NULL
    // 0x13215a4: b               #0x13215c0
    // 0x13215a8: LoadField: r3 = r1->field_7
    //     0x13215a8: ldur            w3, [x1, #7]
    // 0x13215ac: cbz             w3, #0x13215b8
    // 0x13215b0: r4 = false
    //     0x13215b0: add             x4, NULL, #0x30  ; false
    // 0x13215b4: b               #0x13215bc
    // 0x13215b8: r4 = true
    //     0x13215b8: add             x4, NULL, #0x20  ; true
    // 0x13215bc: mov             x3, x4
    // 0x13215c0: cmp             w3, NULL
    // 0x13215c4: b.eq            #0x13215d4
    // 0x13215c8: tbnz            w3, #4, #0x13215d4
    // 0x13215cc: r3 = ""
    //     0x13215cc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13215d0: b               #0x13215d8
    // 0x13215d4: mov             x3, x1
    // 0x13215d8: stur            x3, [fp, #-0x48]
    // 0x13215dc: LoadField: r1 = r0->field_d3
    //     0x13215dc: ldur            w1, [x0, #0xd3]
    // 0x13215e0: DecompressPointer r1
    //     0x13215e0: add             x1, x1, HEAP, lsl #32
    // 0x13215e4: cmp             w1, NULL
    // 0x13215e8: b.ne            #0x13215f4
    // 0x13215ec: r4 = Null
    //     0x13215ec: mov             x4, NULL
    // 0x13215f0: b               #0x132160c
    // 0x13215f4: LoadField: r4 = r1->field_7
    //     0x13215f4: ldur            w4, [x1, #7]
    // 0x13215f8: cbz             w4, #0x1321604
    // 0x13215fc: r5 = false
    //     0x13215fc: add             x5, NULL, #0x30  ; false
    // 0x1321600: b               #0x1321608
    // 0x1321604: r5 = true
    //     0x1321604: add             x5, NULL, #0x20  ; true
    // 0x1321608: mov             x4, x5
    // 0x132160c: cmp             w4, NULL
    // 0x1321610: b.eq            #0x1321620
    // 0x1321614: tbnz            w4, #4, #0x1321620
    // 0x1321618: r4 = ""
    //     0x1321618: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x132161c: b               #0x1321624
    // 0x1321620: mov             x4, x1
    // 0x1321624: stur            x4, [fp, #-0x40]
    // 0x1321628: LoadField: r5 = r0->field_d7
    //     0x1321628: ldur            w5, [x0, #0xd7]
    // 0x132162c: DecompressPointer r5
    //     0x132162c: add             x5, x5, HEAP, lsl #32
    // 0x1321630: ldur            x1, [fp, #-0x10]
    // 0x1321634: stur            x5, [fp, #-0x38]
    // 0x1321638: r0 = value()
    //     0x1321638: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x132163c: LoadField: r1 = r0->field_b
    //     0x132163c: ldur            w1, [x0, #0xb]
    // 0x1321640: DecompressPointer r1
    //     0x1321640: add             x1, x1, HEAP, lsl #32
    // 0x1321644: cmp             w1, NULL
    // 0x1321648: b.ne            #0x1321654
    // 0x132164c: r6 = Null
    //     0x132164c: mov             x6, NULL
    // 0x1321650: b               #0x132167c
    // 0x1321654: LoadField: r0 = r1->field_1f
    //     0x1321654: ldur            w0, [x1, #0x1f]
    // 0x1321658: DecompressPointer r0
    //     0x1321658: add             x0, x0, HEAP, lsl #32
    // 0x132165c: cmp             w0, NULL
    // 0x1321660: b.ne            #0x132166c
    // 0x1321664: r0 = Null
    //     0x1321664: mov             x0, NULL
    // 0x1321668: b               #0x1321678
    // 0x132166c: LoadField: r1 = r0->field_2b
    //     0x132166c: ldur            w1, [x0, #0x2b]
    // 0x1321670: DecompressPointer r1
    //     0x1321670: add             x1, x1, HEAP, lsl #32
    // 0x1321674: mov             x0, x1
    // 0x1321678: mov             x6, x0
    // 0x132167c: ldur            x1, [fp, #-8]
    // 0x1321680: ldur            x5, [fp, #-0x18]
    // 0x1321684: ldur            x0, [fp, #-0x50]
    // 0x1321688: ldur            x2, [fp, #-0x48]
    // 0x132168c: ldur            x3, [fp, #-0x40]
    // 0x1321690: ldur            x4, [fp, #-0x38]
    // 0x1321694: stur            x6, [fp, #-0x60]
    // 0x1321698: LoadField: r7 = r1->field_bb
    //     0x1321698: ldur            w7, [x1, #0xbb]
    // 0x132169c: DecompressPointer r7
    //     0x132169c: add             x7, x7, HEAP, lsl #32
    // 0x13216a0: stur            x7, [fp, #-0x58]
    // 0x13216a4: LoadField: r8 = r1->field_6f
    //     0x13216a4: ldur            w8, [x1, #0x6f]
    // 0x13216a8: DecompressPointer r8
    //     0x13216a8: add             x8, x8, HEAP, lsl #32
    // 0x13216ac: stur            x8, [fp, #-0x10]
    // 0x13216b0: r0 = EventData()
    //     0x13216b0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x13216b4: mov             x1, x0
    // 0x13216b8: ldur            x0, [fp, #-0x58]
    // 0x13216bc: stur            x1, [fp, #-0x68]
    // 0x13216c0: r17 = 259
    //     0x13216c0: movz            x17, #0x103
    // 0x13216c4: str             w0, [x1, x17]
    // 0x13216c8: ldur            x0, [fp, #-0x18]
    // 0x13216cc: r17 = 287
    //     0x13216cc: movz            x17, #0x11f
    // 0x13216d0: str             w0, [x1, x17]
    // 0x13216d4: ldur            x0, [fp, #-0x48]
    // 0x13216d8: r17 = 303
    //     0x13216d8: movz            x17, #0x12f
    // 0x13216dc: str             w0, [x1, x17]
    // 0x13216e0: ldur            x0, [fp, #-0x40]
    // 0x13216e4: r17 = 311
    //     0x13216e4: movz            x17, #0x137
    // 0x13216e8: str             w0, [x1, x17]
    // 0x13216ec: ldur            x0, [fp, #-0x50]
    // 0x13216f0: r17 = 295
    //     0x13216f0: movz            x17, #0x127
    // 0x13216f4: str             w0, [x1, x17]
    // 0x13216f8: ldur            x0, [fp, #-0x60]
    // 0x13216fc: r17 = 319
    //     0x13216fc: movz            x17, #0x13f
    // 0x1321700: str             w0, [x1, x17]
    // 0x1321704: ldur            x0, [fp, #-0x10]
    // 0x1321708: r17 = 323
    //     0x1321708: movz            x17, #0x143
    // 0x132170c: str             w0, [x1, x17]
    // 0x1321710: ldur            x0, [fp, #-0x38]
    // 0x1321714: r17 = 327
    //     0x1321714: movz            x17, #0x147
    // 0x1321718: str             w0, [x1, x17]
    // 0x132171c: r0 = EventsRequest()
    //     0x132171c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x1321720: mov             x1, x0
    // 0x1321724: r0 = "rating_review_feedback_page_opened"
    //     0x1321724: add             x0, PP, #0x36, lsl #12  ; [pp+0x36758] "rating_review_feedback_page_opened"
    //     0x1321728: ldr             x0, [x0, #0x758]
    // 0x132172c: StoreField: r1->field_7 = r0
    //     0x132172c: stur            w0, [x1, #7]
    // 0x1321730: ldur            x0, [fp, #-0x68]
    // 0x1321734: StoreField: r1->field_b = r0
    //     0x1321734: stur            w0, [x1, #0xb]
    // 0x1321738: mov             x2, x1
    // 0x132173c: ldur            x1, [fp, #-8]
    // 0x1321740: r0 = postEvents()
    //     0x1321740: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x1321744: r0 = Null
    //     0x1321744: mov             x0, NULL
    // 0x1321748: LeaveFrame
    //     0x1321748: mov             SP, fp
    //     0x132174c: ldp             fp, lr, [SP], #0x10
    // 0x1321750: ret
    //     0x1321750: ret             
    // 0x1321754: r0 = ConcurrentModificationError()
    //     0x1321754: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x1321758: mov             x1, x0
    // 0x132175c: ldur            x0, [fp, #-0x20]
    // 0x1321760: StoreField: r1->field_b = r0
    //     0x1321760: stur            w0, [x1, #0xb]
    // 0x1321764: mov             x0, x1
    // 0x1321768: r0 = Throw()
    //     0x1321768: bl              #0x16f5420  ; ThrowStub
    // 0x132176c: brk             #0
    // 0x1321770: r0 = ConcurrentModificationError()
    //     0x1321770: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x1321774: mov             x1, x0
    // 0x1321778: ldur            x0, [fp, #-0x30]
    // 0x132177c: StoreField: r1->field_b = r0
    //     0x132177c: stur            w0, [x1, #0xb]
    // 0x1321780: mov             x0, x1
    // 0x1321784: r0 = Throw()
    //     0x1321784: bl              #0x16f5420  ; ThrowStub
    // 0x1321788: brk             #0
    // 0x132178c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x132178c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1321790: b               #0x1320ed4
    // 0x1321794: SaveReg d0
    //     0x1321794: str             q0, [SP, #-0x10]!
    // 0x1321798: SaveReg r0
    //     0x1321798: str             x0, [SP, #-8]!
    // 0x132179c: r0 = AllocateDouble()
    //     0x132179c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13217a0: mov             x2, x0
    // 0x13217a4: RestoreReg r0
    //     0x13217a4: ldr             x0, [SP], #8
    // 0x13217a8: RestoreReg d0
    //     0x13217a8: ldr             q0, [SP], #0x10
    // 0x13217ac: b               #0x1321064
    // 0x13217b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13217b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13217b4: b               #0x1321150
    // 0x13217b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13217b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13217bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13217bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13217c0: b               #0x13212a4
    // 0x13217c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13217c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  set _ ratingReviewOrderResponse=(/* No info */) {
    // ** addr: 0x13217c8, size: 0x8c
    // 0x13217c8: EnterFrame
    //     0x13217c8: stp             fp, lr, [SP, #-0x10]!
    //     0x13217cc: mov             fp, SP
    // 0x13217d0: AllocStack(0x10)
    //     0x13217d0: sub             SP, SP, #0x10
    // 0x13217d4: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x13217d4: mov             x3, x2
    //     0x13217d8: stur            x2, [fp, #-0x10]
    // 0x13217dc: CheckStackOverflow
    //     0x13217dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13217e0: cmp             SP, x16
    //     0x13217e4: b.ls            #0x132184c
    // 0x13217e8: LoadField: r4 = r1->field_4b
    //     0x13217e8: ldur            w4, [x1, #0x4b]
    // 0x13217ec: DecompressPointer r4
    //     0x13217ec: add             x4, x4, HEAP, lsl #32
    // 0x13217f0: mov             x0, x3
    // 0x13217f4: stur            x4, [fp, #-8]
    // 0x13217f8: r2 = Null
    //     0x13217f8: mov             x2, NULL
    // 0x13217fc: r1 = Null
    //     0x13217fc: mov             x1, NULL
    // 0x1321800: r4 = 60
    //     0x1321800: movz            x4, #0x3c
    // 0x1321804: branchIfSmi(r0, 0x1321810)
    //     0x1321804: tbz             w0, #0, #0x1321810
    // 0x1321808: r4 = LoadClassIdInstr(r0)
    //     0x1321808: ldur            x4, [x0, #-1]
    //     0x132180c: ubfx            x4, x4, #0xc, #0x14
    // 0x1321810: r17 = 5135
    //     0x1321810: movz            x17, #0x140f
    // 0x1321814: cmp             x4, x17
    // 0x1321818: b.eq            #0x1321830
    // 0x132181c: r8 = RatingReviewOrderResponse
    //     0x132181c: add             x8, PP, #0x36, lsl #12  ; [pp+0x367c8] Type: RatingReviewOrderResponse
    //     0x1321820: ldr             x8, [x8, #0x7c8]
    // 0x1321824: r3 = Null
    //     0x1321824: add             x3, PP, #0x36, lsl #12  ; [pp+0x367d0] Null
    //     0x1321828: ldr             x3, [x3, #0x7d0]
    // 0x132182c: r0 = DefaultTypeTest()
    //     0x132182c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x1321830: ldur            x1, [fp, #-8]
    // 0x1321834: ldur            x2, [fp, #-0x10]
    // 0x1321838: r0 = value=()
    //     0x1321838: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x132183c: ldur            x0, [fp, #-0x10]
    // 0x1321840: LeaveFrame
    //     0x1321840: mov             SP, fp
    //     0x1321844: ldp             fp, lr, [SP], #0x10
    // 0x1321848: ret
    //     0x1321848: ret             
    // 0x132184c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x132184c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1321850: b               #0x13217e8
  }
  [closure] bool <anonymous closure>(dynamic, Media) {
    // ** addr: 0x1321854, size: 0x58
    // 0x1321854: EnterFrame
    //     0x1321854: stp             fp, lr, [SP, #-0x10]!
    //     0x1321858: mov             fp, SP
    // 0x132185c: AllocStack(0x10)
    //     0x132185c: sub             SP, SP, #0x10
    // 0x1321860: CheckStackOverflow
    //     0x1321860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1321864: cmp             SP, x16
    //     0x1321868: b.ls            #0x13218a4
    // 0x132186c: ldr             x0, [fp, #0x10]
    // 0x1321870: LoadField: r1 = r0->field_b
    //     0x1321870: ldur            w1, [x0, #0xb]
    // 0x1321874: DecompressPointer r1
    //     0x1321874: add             x1, x1, HEAP, lsl #32
    // 0x1321878: r0 = LoadClassIdInstr(r1)
    //     0x1321878: ldur            x0, [x1, #-1]
    //     0x132187c: ubfx            x0, x0, #0xc, #0x14
    // 0x1321880: r16 = "video"
    //     0x1321880: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x1321884: ldr             x16, [x16, #0xb50]
    // 0x1321888: stp             x16, x1, [SP]
    // 0x132188c: mov             lr, x0
    // 0x1321890: ldr             lr, [x21, lr, lsl #3]
    // 0x1321894: blr             lr
    // 0x1321898: LeaveFrame
    //     0x1321898: mov             SP, fp
    //     0x132189c: ldp             fp, lr, [SP], #0x10
    // 0x13218a0: ret
    //     0x13218a0: ret             
    // 0x13218a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13218a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13218a8: b               #0x132186c
  }
  [closure] bool <anonymous closure>(dynamic, Media) {
    // ** addr: 0x13218ac, size: 0x54
    // 0x13218ac: EnterFrame
    //     0x13218ac: stp             fp, lr, [SP, #-0x10]!
    //     0x13218b0: mov             fp, SP
    // 0x13218b4: AllocStack(0x10)
    //     0x13218b4: sub             SP, SP, #0x10
    // 0x13218b8: CheckStackOverflow
    //     0x13218b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13218bc: cmp             SP, x16
    //     0x13218c0: b.ls            #0x13218f8
    // 0x13218c4: ldr             x0, [fp, #0x10]
    // 0x13218c8: LoadField: r1 = r0->field_b
    //     0x13218c8: ldur            w1, [x0, #0xb]
    // 0x13218cc: DecompressPointer r1
    //     0x13218cc: add             x1, x1, HEAP, lsl #32
    // 0x13218d0: r0 = LoadClassIdInstr(r1)
    //     0x13218d0: ldur            x0, [x1, #-1]
    //     0x13218d4: ubfx            x0, x0, #0xc, #0x14
    // 0x13218d8: r16 = "image"
    //     0x13218d8: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x13218dc: stp             x16, x1, [SP]
    // 0x13218e0: mov             lr, x0
    // 0x13218e4: ldr             lr, [x21, lr, lsl #3]
    // 0x13218e8: blr             lr
    // 0x13218ec: LeaveFrame
    //     0x13218ec: mov             SP, fp
    //     0x13218f0: ldp             fp, lr, [SP], #0x10
    // 0x13218f4: ret
    //     0x13218f4: ret             
    // 0x13218f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13218f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13218fc: b               #0x13218c4
  }
  [closure] Future<void> <anonymous closure>(dynamic, Media) async {
    // ** addr: 0x1321900, size: 0x10c
    // 0x1321900: EnterFrame
    //     0x1321900: stp             fp, lr, [SP, #-0x10]!
    //     0x1321904: mov             fp, SP
    // 0x1321908: AllocStack(0x40)
    //     0x1321908: sub             SP, SP, #0x40
    // 0x132190c: SetupParameters(RatingReviewOrderController this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x132190c: stur            NULL, [fp, #-8]
    //     0x1321910: movz            x0, #0
    //     0x1321914: add             x1, fp, w0, sxtw #2
    //     0x1321918: ldr             x1, [x1, #0x18]
    //     0x132191c: add             x2, fp, w0, sxtw #2
    //     0x1321920: ldr             x2, [x2, #0x10]
    //     0x1321924: stur            x2, [fp, #-0x18]
    //     0x1321928: ldur            w3, [x1, #0x17]
    //     0x132192c: add             x3, x3, HEAP, lsl #32
    //     0x1321930: stur            x3, [fp, #-0x10]
    // 0x1321934: CheckStackOverflow
    //     0x1321934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1321938: cmp             SP, x16
    //     0x132193c: b.ls            #0x1321a04
    // 0x1321940: InitAsync() -> Future<void?>
    //     0x1321940: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x1321944: bl              #0x6326e0  ; InitAsyncStub
    // 0x1321948: ldur            x0, [fp, #-0x18]
    // 0x132194c: LoadField: r1 = r0->field_f
    //     0x132194c: ldur            w1, [x0, #0xf]
    // 0x1321950: DecompressPointer r1
    //     0x1321950: add             x1, x1, HEAP, lsl #32
    // 0x1321954: cmp             w1, NULL
    // 0x1321958: b.ne            #0x1321960
    // 0x132195c: r1 = ""
    //     0x132195c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1321960: ldur            x2, [fp, #-0x10]
    // 0x1321964: r0 = getImageBytes()
    //     0x1321964: bl              #0x931098  ; [package:customer_app/app/core/utils/utils.dart] Utils::getImageBytes
    // 0x1321968: mov             x1, x0
    // 0x132196c: stur            x1, [fp, #-0x20]
    // 0x1321970: r0 = Await()
    //     0x1321970: bl              #0x63248c  ; AwaitStub
    // 0x1321974: mov             x1, x0
    // 0x1321978: r0 = convertUint8ListToFile()
    //     0x1321978: bl              #0x1321a0c  ; [package:customer_app/app/core/utils/utils.dart] Utils::convertUint8ListToFile
    // 0x132197c: mov             x1, x0
    // 0x1321980: stur            x1, [fp, #-0x20]
    // 0x1321984: r0 = Await()
    //     0x1321984: bl              #0x63248c  ; AwaitStub
    // 0x1321988: mov             x1, x0
    // 0x132198c: ldur            x0, [fp, #-0x10]
    // 0x1321990: stur            x1, [fp, #-0x30]
    // 0x1321994: LoadField: r2 = r0->field_f
    //     0x1321994: ldur            w2, [x0, #0xf]
    // 0x1321998: DecompressPointer r2
    //     0x1321998: add             x2, x2, HEAP, lsl #32
    // 0x132199c: LoadField: r0 = r2->field_97
    //     0x132199c: ldur            w0, [x2, #0x97]
    // 0x13219a0: DecompressPointer r0
    //     0x13219a0: add             x0, x0, HEAP, lsl #32
    // 0x13219a4: ldur            x2, [fp, #-0x18]
    // 0x13219a8: stur            x0, [fp, #-0x28]
    // 0x13219ac: LoadField: r3 = r2->field_f
    //     0x13219ac: ldur            w3, [x2, #0xf]
    // 0x13219b0: DecompressPointer r3
    //     0x13219b0: add             x3, x3, HEAP, lsl #32
    // 0x13219b4: cmp             w3, NULL
    // 0x13219b8: b.ne            #0x13219c0
    // 0x13219bc: r3 = ""
    //     0x13219bc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13219c0: stur            x3, [fp, #-0x20]
    // 0x13219c4: LoadField: r4 = r2->field_b
    //     0x13219c4: ldur            w4, [x2, #0xb]
    // 0x13219c8: DecompressPointer r4
    //     0x13219c8: add             x4, x4, HEAP, lsl #32
    // 0x13219cc: stur            x4, [fp, #-0x10]
    // 0x13219d0: r0 = Media()
    //     0x13219d0: bl              #0x8ad358  ; AllocateMediaStub -> Media (size=0x20)
    // 0x13219d4: mov             x1, x0
    // 0x13219d8: ldur            x0, [fp, #-0x10]
    // 0x13219dc: StoreField: r1->field_b = r0
    //     0x13219dc: stur            w0, [x1, #0xb]
    // 0x13219e0: ldur            x0, [fp, #-0x20]
    // 0x13219e4: StoreField: r1->field_f = r0
    //     0x13219e4: stur            w0, [x1, #0xf]
    // 0x13219e8: ldur            x0, [fp, #-0x30]
    // 0x13219ec: ArrayStore: r1[0] = r0  ; List_4
    //     0x13219ec: stur            w0, [x1, #0x17]
    // 0x13219f0: ldur            x16, [fp, #-0x28]
    // 0x13219f4: stp             x1, x16, [SP]
    // 0x13219f8: r0 = add()
    //     0x13219f8: bl              #0x1654150  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0x13219fc: r0 = Null
    //     0x13219fc: mov             x0, NULL
    // 0x1321a00: r0 = ReturnAsyncNotFuture()
    //     0x1321a00: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1321a04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1321a04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1321a08: b               #0x1321940
  }
  [closure] void <anonymous closure>(dynamic, Media) {
    // ** addr: 0x1321b88, size: 0x214
    // 0x1321b88: EnterFrame
    //     0x1321b88: stp             fp, lr, [SP, #-0x10]!
    //     0x1321b8c: mov             fp, SP
    // 0x1321b90: AllocStack(0x28)
    //     0x1321b90: sub             SP, SP, #0x28
    // 0x1321b94: SetupParameters()
    //     0x1321b94: ldr             x0, [fp, #0x18]
    //     0x1321b98: ldur            w1, [x0, #0x17]
    //     0x1321b9c: add             x1, x1, HEAP, lsl #32
    //     0x1321ba0: stur            x1, [fp, #-8]
    // 0x1321ba4: CheckStackOverflow
    //     0x1321ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1321ba8: cmp             SP, x16
    //     0x1321bac: b.ls            #0x1321d94
    // 0x1321bb0: ldr             x2, [fp, #0x10]
    // 0x1321bb4: LoadField: r0 = r2->field_b
    //     0x1321bb4: ldur            w0, [x2, #0xb]
    // 0x1321bb8: DecompressPointer r0
    //     0x1321bb8: add             x0, x0, HEAP, lsl #32
    // 0x1321bbc: r3 = LoadClassIdInstr(r0)
    //     0x1321bbc: ldur            x3, [x0, #-1]
    //     0x1321bc0: ubfx            x3, x3, #0xc, #0x14
    // 0x1321bc4: r16 = "image"
    //     0x1321bc4: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x1321bc8: stp             x16, x0, [SP]
    // 0x1321bcc: mov             x0, x3
    // 0x1321bd0: mov             lr, x0
    // 0x1321bd4: ldr             lr, [x21, lr, lsl #3]
    // 0x1321bd8: blr             lr
    // 0x1321bdc: tbnz            w0, #4, #0x1321c98
    // 0x1321be0: ldur            x3, [fp, #-8]
    // 0x1321be4: LoadField: r4 = r3->field_f
    //     0x1321be4: ldur            w4, [x3, #0xf]
    // 0x1321be8: DecompressPointer r4
    //     0x1321be8: add             x4, x4, HEAP, lsl #32
    // 0x1321bec: stur            x4, [fp, #-0x18]
    // 0x1321bf0: LoadField: r0 = r4->field_cf
    //     0x1321bf0: ldur            w0, [x4, #0xcf]
    // 0x1321bf4: DecompressPointer r0
    //     0x1321bf4: add             x0, x0, HEAP, lsl #32
    // 0x1321bf8: stur            x0, [fp, #-0x10]
    // 0x1321bfc: cmp             w0, NULL
    // 0x1321c00: b.ne            #0x1321c3c
    // 0x1321c04: ldr             x5, [fp, #0x10]
    // 0x1321c08: LoadField: r0 = r5->field_f
    //     0x1321c08: ldur            w0, [x5, #0xf]
    // 0x1321c0c: DecompressPointer r0
    //     0x1321c0c: add             x0, x0, HEAP, lsl #32
    // 0x1321c10: cmp             w0, NULL
    // 0x1321c14: b.ne            #0x1321c1c
    // 0x1321c18: r0 = ""
    //     0x1321c18: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1321c1c: StoreField: r4->field_cf = r0
    //     0x1321c1c: stur            w0, [x4, #0xcf]
    //     0x1321c20: ldurb           w16, [x4, #-1]
    //     0x1321c24: ldurb           w17, [x0, #-1]
    //     0x1321c28: and             x16, x17, x16, lsr #2
    //     0x1321c2c: tst             x16, HEAP, lsr #32
    //     0x1321c30: b.eq            #0x1321c38
    //     0x1321c34: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x1321c38: b               #0x1321c98
    // 0x1321c3c: ldr             x5, [fp, #0x10]
    // 0x1321c40: r1 = Null
    //     0x1321c40: mov             x1, NULL
    // 0x1321c44: r2 = 6
    //     0x1321c44: movz            x2, #0x6
    // 0x1321c48: r0 = AllocateArray()
    //     0x1321c48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1321c4c: mov             x1, x0
    // 0x1321c50: ldur            x0, [fp, #-0x10]
    // 0x1321c54: StoreField: r1->field_f = r0
    //     0x1321c54: stur            w0, [x1, #0xf]
    // 0x1321c58: r16 = ", "
    //     0x1321c58: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x1321c5c: StoreField: r1->field_13 = r16
    //     0x1321c5c: stur            w16, [x1, #0x13]
    // 0x1321c60: ldr             x0, [fp, #0x10]
    // 0x1321c64: LoadField: r2 = r0->field_f
    //     0x1321c64: ldur            w2, [x0, #0xf]
    // 0x1321c68: DecompressPointer r2
    //     0x1321c68: add             x2, x2, HEAP, lsl #32
    // 0x1321c6c: ArrayStore: r1[0] = r2  ; List_4
    //     0x1321c6c: stur            w2, [x1, #0x17]
    // 0x1321c70: str             x1, [SP]
    // 0x1321c74: r0 = _interpolate()
    //     0x1321c74: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1321c78: ldur            x1, [fp, #-0x18]
    // 0x1321c7c: StoreField: r1->field_cf = r0
    //     0x1321c7c: stur            w0, [x1, #0xcf]
    //     0x1321c80: ldurb           w16, [x1, #-1]
    //     0x1321c84: ldurb           w17, [x0, #-1]
    //     0x1321c88: and             x16, x17, x16, lsr #2
    //     0x1321c8c: tst             x16, HEAP, lsr #32
    //     0x1321c90: b.eq            #0x1321c98
    //     0x1321c94: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1321c98: ldr             x1, [fp, #0x10]
    // 0x1321c9c: LoadField: r0 = r1->field_b
    //     0x1321c9c: ldur            w0, [x1, #0xb]
    // 0x1321ca0: DecompressPointer r0
    //     0x1321ca0: add             x0, x0, HEAP, lsl #32
    // 0x1321ca4: r2 = LoadClassIdInstr(r0)
    //     0x1321ca4: ldur            x2, [x0, #-1]
    //     0x1321ca8: ubfx            x2, x2, #0xc, #0x14
    // 0x1321cac: r16 = "video"
    //     0x1321cac: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x1321cb0: ldr             x16, [x16, #0xb50]
    // 0x1321cb4: stp             x16, x0, [SP]
    // 0x1321cb8: mov             x0, x2
    // 0x1321cbc: mov             lr, x0
    // 0x1321cc0: ldr             lr, [x21, lr, lsl #3]
    // 0x1321cc4: blr             lr
    // 0x1321cc8: tbnz            w0, #4, #0x1321d84
    // 0x1321ccc: ldur            x0, [fp, #-8]
    // 0x1321cd0: LoadField: r3 = r0->field_f
    //     0x1321cd0: ldur            w3, [x0, #0xf]
    // 0x1321cd4: DecompressPointer r3
    //     0x1321cd4: add             x3, x3, HEAP, lsl #32
    // 0x1321cd8: stur            x3, [fp, #-0x10]
    // 0x1321cdc: LoadField: r0 = r3->field_d3
    //     0x1321cdc: ldur            w0, [x3, #0xd3]
    // 0x1321ce0: DecompressPointer r0
    //     0x1321ce0: add             x0, x0, HEAP, lsl #32
    // 0x1321ce4: stur            x0, [fp, #-8]
    // 0x1321ce8: cmp             w0, NULL
    // 0x1321cec: b.ne            #0x1321d28
    // 0x1321cf0: ldr             x4, [fp, #0x10]
    // 0x1321cf4: LoadField: r0 = r4->field_f
    //     0x1321cf4: ldur            w0, [x4, #0xf]
    // 0x1321cf8: DecompressPointer r0
    //     0x1321cf8: add             x0, x0, HEAP, lsl #32
    // 0x1321cfc: cmp             w0, NULL
    // 0x1321d00: b.ne            #0x1321d08
    // 0x1321d04: r0 = ""
    //     0x1321d04: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1321d08: StoreField: r3->field_d3 = r0
    //     0x1321d08: stur            w0, [x3, #0xd3]
    //     0x1321d0c: ldurb           w16, [x3, #-1]
    //     0x1321d10: ldurb           w17, [x0, #-1]
    //     0x1321d14: and             x16, x17, x16, lsr #2
    //     0x1321d18: tst             x16, HEAP, lsr #32
    //     0x1321d1c: b.eq            #0x1321d24
    //     0x1321d20: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x1321d24: b               #0x1321d84
    // 0x1321d28: ldr             x4, [fp, #0x10]
    // 0x1321d2c: r1 = Null
    //     0x1321d2c: mov             x1, NULL
    // 0x1321d30: r2 = 6
    //     0x1321d30: movz            x2, #0x6
    // 0x1321d34: r0 = AllocateArray()
    //     0x1321d34: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1321d38: mov             x1, x0
    // 0x1321d3c: ldur            x0, [fp, #-8]
    // 0x1321d40: StoreField: r1->field_f = r0
    //     0x1321d40: stur            w0, [x1, #0xf]
    // 0x1321d44: r16 = ", "
    //     0x1321d44: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x1321d48: StoreField: r1->field_13 = r16
    //     0x1321d48: stur            w16, [x1, #0x13]
    // 0x1321d4c: ldr             x0, [fp, #0x10]
    // 0x1321d50: LoadField: r2 = r0->field_f
    //     0x1321d50: ldur            w2, [x0, #0xf]
    // 0x1321d54: DecompressPointer r2
    //     0x1321d54: add             x2, x2, HEAP, lsl #32
    // 0x1321d58: ArrayStore: r1[0] = r2  ; List_4
    //     0x1321d58: stur            w2, [x1, #0x17]
    // 0x1321d5c: str             x1, [SP]
    // 0x1321d60: r0 = _interpolate()
    //     0x1321d60: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1321d64: ldur            x1, [fp, #-0x10]
    // 0x1321d68: StoreField: r1->field_d3 = r0
    //     0x1321d68: stur            w0, [x1, #0xd3]
    //     0x1321d6c: ldurb           w16, [x1, #-1]
    //     0x1321d70: ldurb           w17, [x0, #-1]
    //     0x1321d74: and             x16, x17, x16, lsr #2
    //     0x1321d78: tst             x16, HEAP, lsr #32
    //     0x1321d7c: b.eq            #0x1321d84
    //     0x1321d80: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1321d84: r0 = Null
    //     0x1321d84: mov             x0, NULL
    // 0x1321d88: LeaveFrame
    //     0x1321d88: mov             SP, fp
    //     0x1321d8c: ldp             fp, lr, [SP], #0x10
    // 0x1321d90: ret
    //     0x1321d90: ret             
    // 0x1321d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1321d94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1321d98: b               #0x1321bb0
  }
  set _ createReviewResponse=(/* No info */) {
    // ** addr: 0x1321d9c, size: 0x8c
    // 0x1321d9c: EnterFrame
    //     0x1321d9c: stp             fp, lr, [SP, #-0x10]!
    //     0x1321da0: mov             fp, SP
    // 0x1321da4: AllocStack(0x10)
    //     0x1321da4: sub             SP, SP, #0x10
    // 0x1321da8: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x1321da8: mov             x3, x2
    //     0x1321dac: stur            x2, [fp, #-0x10]
    // 0x1321db0: CheckStackOverflow
    //     0x1321db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1321db4: cmp             SP, x16
    //     0x1321db8: b.ls            #0x1321e20
    // 0x1321dbc: LoadField: r4 = r1->field_4f
    //     0x1321dbc: ldur            w4, [x1, #0x4f]
    // 0x1321dc0: DecompressPointer r4
    //     0x1321dc0: add             x4, x4, HEAP, lsl #32
    // 0x1321dc4: mov             x0, x3
    // 0x1321dc8: stur            x4, [fp, #-8]
    // 0x1321dcc: r2 = Null
    //     0x1321dcc: mov             x2, NULL
    // 0x1321dd0: r1 = Null
    //     0x1321dd0: mov             x1, NULL
    // 0x1321dd4: r4 = 60
    //     0x1321dd4: movz            x4, #0x3c
    // 0x1321dd8: branchIfSmi(r0, 0x1321de4)
    //     0x1321dd8: tbz             w0, #0, #0x1321de4
    // 0x1321ddc: r4 = LoadClassIdInstr(r0)
    //     0x1321ddc: ldur            x4, [x0, #-1]
    //     0x1321de0: ubfx            x4, x4, #0xc, #0x14
    // 0x1321de4: r17 = 5144
    //     0x1321de4: movz            x17, #0x1418
    // 0x1321de8: cmp             x4, x17
    // 0x1321dec: b.eq            #0x1321e04
    // 0x1321df0: r8 = CreateReviewResponse
    //     0x1321df0: add             x8, PP, #0x36, lsl #12  ; [pp+0x363a8] Type: CreateReviewResponse
    //     0x1321df4: ldr             x8, [x8, #0x3a8]
    // 0x1321df8: r3 = Null
    //     0x1321df8: add             x3, PP, #0x36, lsl #12  ; [pp+0x36978] Null
    //     0x1321dfc: ldr             x3, [x3, #0x978]
    // 0x1321e00: r0 = DefaultTypeTest()
    //     0x1321e00: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x1321e04: ldur            x1, [fp, #-8]
    // 0x1321e08: ldur            x2, [fp, #-0x10]
    // 0x1321e0c: r0 = value=()
    //     0x1321e0c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1321e10: ldur            x0, [fp, #-0x10]
    // 0x1321e14: LeaveFrame
    //     0x1321e14: mov             SP, fp
    //     0x1321e18: ldp             fp, lr, [SP], #0x10
    // 0x1321e1c: ret
    //     0x1321e1c: ret             
    // 0x1321e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1321e20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1321e24: b               #0x1321dbc
  }
  _ onSubmitEvent(/* No info */) {
    // ** addr: 0x1321e28, size: 0x6dc
    // 0x1321e28: EnterFrame
    //     0x1321e28: stp             fp, lr, [SP, #-0x10]!
    //     0x1321e2c: mov             fp, SP
    // 0x1321e30: AllocStack(0x80)
    //     0x1321e30: sub             SP, SP, #0x80
    // 0x1321e34: SetupParameters(RatingReviewOrderController this /* r1 => r0, fp-0x8 */)
    //     0x1321e34: mov             x0, x1
    //     0x1321e38: stur            x1, [fp, #-8]
    // 0x1321e3c: CheckStackOverflow
    //     0x1321e3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1321e40: cmp             SP, x16
    //     0x1321e44: b.ls            #0x13224d8
    // 0x1321e48: LoadField: r1 = r0->field_97
    //     0x1321e48: ldur            w1, [x0, #0x97]
    // 0x1321e4c: DecompressPointer r1
    //     0x1321e4c: add             x1, x1, HEAP, lsl #32
    // 0x1321e50: r0 = value()
    //     0x1321e50: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1321e54: r1 = LoadClassIdInstr(r0)
    //     0x1321e54: ldur            x1, [x0, #-1]
    //     0x1321e58: ubfx            x1, x1, #0xc, #0x14
    // 0x1321e5c: mov             x16, x0
    // 0x1321e60: mov             x0, x1
    // 0x1321e64: mov             x1, x16
    // 0x1321e68: r0 = GDT[cid_x0 + 0xc907]()
    //     0x1321e68: movz            x17, #0xc907
    //     0x1321e6c: add             lr, x0, x17
    //     0x1321e70: ldr             lr, [x21, lr, lsl #3]
    //     0x1321e74: blr             lr
    // 0x1321e78: mov             x2, x0
    // 0x1321e7c: stur            x2, [fp, #-0x20]
    // 0x1321e80: r4 = Null
    //     0x1321e80: mov             x4, NULL
    // 0x1321e84: r3 = Null
    //     0x1321e84: mov             x3, NULL
    // 0x1321e88: stur            x4, [fp, #-0x10]
    // 0x1321e8c: stur            x3, [fp, #-0x18]
    // 0x1321e90: CheckStackOverflow
    //     0x1321e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1321e94: cmp             SP, x16
    //     0x1321e98: b.ls            #0x13224e0
    // 0x1321e9c: r0 = LoadClassIdInstr(r2)
    //     0x1321e9c: ldur            x0, [x2, #-1]
    //     0x1321ea0: ubfx            x0, x0, #0xc, #0x14
    // 0x1321ea4: mov             x1, x2
    // 0x1321ea8: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x1321ea8: add             lr, x0, #0x5ea
    //     0x1321eac: ldr             lr, [x21, lr, lsl #3]
    //     0x1321eb0: blr             lr
    // 0x1321eb4: tbnz            w0, #4, #0x13221b8
    // 0x1321eb8: ldur            x2, [fp, #-0x20]
    // 0x1321ebc: r0 = LoadClassIdInstr(r2)
    //     0x1321ebc: ldur            x0, [x2, #-1]
    //     0x1321ec0: ubfx            x0, x0, #0xc, #0x14
    // 0x1321ec4: mov             x1, x2
    // 0x1321ec8: r0 = GDT[cid_x0 + 0x655]()
    //     0x1321ec8: add             lr, x0, #0x655
    //     0x1321ecc: ldr             lr, [x21, lr, lsl #3]
    //     0x1321ed0: blr             lr
    // 0x1321ed4: mov             x1, x0
    // 0x1321ed8: stur            x1, [fp, #-0x28]
    // 0x1321edc: LoadField: r0 = r1->field_b
    //     0x1321edc: ldur            w0, [x1, #0xb]
    // 0x1321ee0: DecompressPointer r0
    //     0x1321ee0: add             x0, x0, HEAP, lsl #32
    // 0x1321ee4: r2 = LoadClassIdInstr(r0)
    //     0x1321ee4: ldur            x2, [x0, #-1]
    //     0x1321ee8: ubfx            x2, x2, #0xc, #0x14
    // 0x1321eec: r16 = "image_local"
    //     0x1321eec: add             x16, PP, #0x36, lsl #12  ; [pp+0x369b8] "image_local"
    //     0x1321ef0: ldr             x16, [x16, #0x9b8]
    // 0x1321ef4: stp             x16, x0, [SP]
    // 0x1321ef8: mov             x0, x2
    // 0x1321efc: mov             lr, x0
    // 0x1321f00: ldr             lr, [x21, lr, lsl #3]
    // 0x1321f04: blr             lr
    // 0x1321f08: tbz             w0, #4, #0x1321f3c
    // 0x1321f0c: ldur            x1, [fp, #-0x28]
    // 0x1321f10: LoadField: r0 = r1->field_b
    //     0x1321f10: ldur            w0, [x1, #0xb]
    // 0x1321f14: DecompressPointer r0
    //     0x1321f14: add             x0, x0, HEAP, lsl #32
    // 0x1321f18: r2 = LoadClassIdInstr(r0)
    //     0x1321f18: ldur            x2, [x0, #-1]
    //     0x1321f1c: ubfx            x2, x2, #0xc, #0x14
    // 0x1321f20: r16 = "image"
    //     0x1321f20: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x1321f24: stp             x16, x0, [SP]
    // 0x1321f28: mov             x0, x2
    // 0x1321f2c: mov             lr, x0
    // 0x1321f30: ldr             lr, [x21, lr, lsl #3]
    // 0x1321f34: blr             lr
    // 0x1321f38: tbnz            w0, #4, #0x132203c
    // 0x1321f3c: ldur            x0, [fp, #-0x10]
    // 0x1321f40: cmp             w0, NULL
    // 0x1321f44: b.ne            #0x1321f90
    // 0x1321f48: ldur            x2, [fp, #-0x28]
    // 0x1321f4c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x1321f4c: ldur            w1, [x2, #0x17]
    // 0x1321f50: DecompressPointer r1
    //     0x1321f50: add             x1, x1, HEAP, lsl #32
    // 0x1321f54: cmp             w1, NULL
    // 0x1321f58: b.ne            #0x1321f64
    // 0x1321f5c: r0 = Null
    //     0x1321f5c: mov             x0, NULL
    // 0x1321f60: b               #0x1321f78
    // 0x1321f64: r0 = LoadClassIdInstr(r1)
    //     0x1321f64: ldur            x0, [x1, #-1]
    //     0x1321f68: ubfx            x0, x0, #0xc, #0x14
    // 0x1321f6c: r0 = GDT[cid_x0 + -0xe96]()
    //     0x1321f6c: sub             lr, x0, #0xe96
    //     0x1321f70: ldr             lr, [x21, lr, lsl #3]
    //     0x1321f74: blr             lr
    // 0x1321f78: cmp             w0, NULL
    // 0x1321f7c: b.ne            #0x1322030
    // 0x1321f80: ldur            x3, [fp, #-0x28]
    // 0x1321f84: LoadField: r0 = r3->field_f
    //     0x1321f84: ldur            w0, [x3, #0xf]
    // 0x1321f88: DecompressPointer r0
    //     0x1321f88: add             x0, x0, HEAP, lsl #32
    // 0x1321f8c: b               #0x1322030
    // 0x1321f90: ldur            x3, [fp, #-0x28]
    // 0x1321f94: r1 = Null
    //     0x1321f94: mov             x1, NULL
    // 0x1321f98: r2 = 6
    //     0x1321f98: movz            x2, #0x6
    // 0x1321f9c: r0 = AllocateArray()
    //     0x1321f9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1321fa0: mov             x2, x0
    // 0x1321fa4: ldur            x1, [fp, #-0x10]
    // 0x1321fa8: stur            x2, [fp, #-0x30]
    // 0x1321fac: StoreField: r2->field_f = r1
    //     0x1321fac: stur            w1, [x2, #0xf]
    // 0x1321fb0: r16 = ", "
    //     0x1321fb0: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x1321fb4: StoreField: r2->field_13 = r16
    //     0x1321fb4: stur            w16, [x2, #0x13]
    // 0x1321fb8: ldur            x3, [fp, #-0x28]
    // 0x1321fbc: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x1321fbc: ldur            w1, [x3, #0x17]
    // 0x1321fc0: DecompressPointer r1
    //     0x1321fc0: add             x1, x1, HEAP, lsl #32
    // 0x1321fc4: cmp             w1, NULL
    // 0x1321fc8: b.ne            #0x1321fd4
    // 0x1321fcc: r0 = Null
    //     0x1321fcc: mov             x0, NULL
    // 0x1321fd0: b               #0x1321fe8
    // 0x1321fd4: r0 = LoadClassIdInstr(r1)
    //     0x1321fd4: ldur            x0, [x1, #-1]
    //     0x1321fd8: ubfx            x0, x0, #0xc, #0x14
    // 0x1321fdc: r0 = GDT[cid_x0 + -0xe96]()
    //     0x1321fdc: sub             lr, x0, #0xe96
    //     0x1321fe0: ldr             lr, [x21, lr, lsl #3]
    //     0x1321fe4: blr             lr
    // 0x1321fe8: cmp             w0, NULL
    // 0x1321fec: b.ne            #0x1321ffc
    // 0x1321ff0: ldur            x2, [fp, #-0x28]
    // 0x1321ff4: LoadField: r0 = r2->field_f
    //     0x1321ff4: ldur            w0, [x2, #0xf]
    // 0x1321ff8: DecompressPointer r0
    //     0x1321ff8: add             x0, x0, HEAP, lsl #32
    // 0x1321ffc: ldur            x1, [fp, #-0x30]
    // 0x1322000: ArrayStore: r1[2] = r0  ; List_4
    //     0x1322000: add             x25, x1, #0x17
    //     0x1322004: str             w0, [x25]
    //     0x1322008: tbz             w0, #0, #0x1322024
    //     0x132200c: ldurb           w16, [x1, #-1]
    //     0x1322010: ldurb           w17, [x0, #-1]
    //     0x1322014: and             x16, x17, x16, lsr #2
    //     0x1322018: tst             x16, HEAP, lsr #32
    //     0x132201c: b.eq            #0x1322024
    //     0x1322020: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1322024: ldur            x16, [fp, #-0x30]
    // 0x1322028: str             x16, [SP]
    // 0x132202c: r0 = _interpolate()
    //     0x132202c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1322030: mov             x4, x0
    // 0x1322034: ldur            x3, [fp, #-0x18]
    // 0x1322038: b               #0x13221b0
    // 0x132203c: ldur            x1, [fp, #-0x10]
    // 0x1322040: ldur            x2, [fp, #-0x28]
    // 0x1322044: LoadField: r0 = r2->field_b
    //     0x1322044: ldur            w0, [x2, #0xb]
    // 0x1322048: DecompressPointer r0
    //     0x1322048: add             x0, x0, HEAP, lsl #32
    // 0x132204c: r3 = LoadClassIdInstr(r0)
    //     0x132204c: ldur            x3, [x0, #-1]
    //     0x1322050: ubfx            x3, x3, #0xc, #0x14
    // 0x1322054: r16 = "video_local"
    //     0x1322054: add             x16, PP, #0x36, lsl #12  ; [pp+0x369c0] "video_local"
    //     0x1322058: ldr             x16, [x16, #0x9c0]
    // 0x132205c: stp             x16, x0, [SP]
    // 0x1322060: mov             x0, x3
    // 0x1322064: mov             lr, x0
    // 0x1322068: ldr             lr, [x21, lr, lsl #3]
    // 0x132206c: blr             lr
    // 0x1322070: tbz             w0, #4, #0x13220a8
    // 0x1322074: ldur            x1, [fp, #-0x28]
    // 0x1322078: LoadField: r0 = r1->field_b
    //     0x1322078: ldur            w0, [x1, #0xb]
    // 0x132207c: DecompressPointer r0
    //     0x132207c: add             x0, x0, HEAP, lsl #32
    // 0x1322080: r2 = LoadClassIdInstr(r0)
    //     0x1322080: ldur            x2, [x0, #-1]
    //     0x1322084: ubfx            x2, x2, #0xc, #0x14
    // 0x1322088: r16 = "video"
    //     0x1322088: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x132208c: ldr             x16, [x16, #0xb50]
    // 0x1322090: stp             x16, x0, [SP]
    // 0x1322094: mov             x0, x2
    // 0x1322098: mov             lr, x0
    // 0x132209c: ldr             lr, [x21, lr, lsl #3]
    // 0x13220a0: blr             lr
    // 0x13220a4: tbnz            w0, #4, #0x13221a4
    // 0x13220a8: ldur            x0, [fp, #-0x18]
    // 0x13220ac: cmp             w0, NULL
    // 0x13220b0: b.ne            #0x13220fc
    // 0x13220b4: ldur            x2, [fp, #-0x28]
    // 0x13220b8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x13220b8: ldur            w1, [x2, #0x17]
    // 0x13220bc: DecompressPointer r1
    //     0x13220bc: add             x1, x1, HEAP, lsl #32
    // 0x13220c0: cmp             w1, NULL
    // 0x13220c4: b.ne            #0x13220d0
    // 0x13220c8: r0 = Null
    //     0x13220c8: mov             x0, NULL
    // 0x13220cc: b               #0x13220e4
    // 0x13220d0: r0 = LoadClassIdInstr(r1)
    //     0x13220d0: ldur            x0, [x1, #-1]
    //     0x13220d4: ubfx            x0, x0, #0xc, #0x14
    // 0x13220d8: r0 = GDT[cid_x0 + -0xe96]()
    //     0x13220d8: sub             lr, x0, #0xe96
    //     0x13220dc: ldr             lr, [x21, lr, lsl #3]
    //     0x13220e0: blr             lr
    // 0x13220e4: cmp             w0, NULL
    // 0x13220e8: b.ne            #0x13221a8
    // 0x13220ec: ldur            x3, [fp, #-0x28]
    // 0x13220f0: LoadField: r0 = r3->field_f
    //     0x13220f0: ldur            w0, [x3, #0xf]
    // 0x13220f4: DecompressPointer r0
    //     0x13220f4: add             x0, x0, HEAP, lsl #32
    // 0x13220f8: b               #0x13221a8
    // 0x13220fc: ldur            x3, [fp, #-0x28]
    // 0x1322100: r1 = Null
    //     0x1322100: mov             x1, NULL
    // 0x1322104: r2 = 6
    //     0x1322104: movz            x2, #0x6
    // 0x1322108: r0 = AllocateArray()
    //     0x1322108: bl              #0x16f7198  ; AllocateArrayStub
    // 0x132210c: mov             x2, x0
    // 0x1322110: ldur            x0, [fp, #-0x18]
    // 0x1322114: stur            x2, [fp, #-0x30]
    // 0x1322118: StoreField: r2->field_f = r0
    //     0x1322118: stur            w0, [x2, #0xf]
    // 0x132211c: r16 = ", "
    //     0x132211c: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x1322120: StoreField: r2->field_13 = r16
    //     0x1322120: stur            w16, [x2, #0x13]
    // 0x1322124: ldur            x3, [fp, #-0x28]
    // 0x1322128: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x1322128: ldur            w1, [x3, #0x17]
    // 0x132212c: DecompressPointer r1
    //     0x132212c: add             x1, x1, HEAP, lsl #32
    // 0x1322130: cmp             w1, NULL
    // 0x1322134: b.ne            #0x1322140
    // 0x1322138: r0 = Null
    //     0x1322138: mov             x0, NULL
    // 0x132213c: b               #0x1322154
    // 0x1322140: r0 = LoadClassIdInstr(r1)
    //     0x1322140: ldur            x0, [x1, #-1]
    //     0x1322144: ubfx            x0, x0, #0xc, #0x14
    // 0x1322148: r0 = GDT[cid_x0 + -0xe96]()
    //     0x1322148: sub             lr, x0, #0xe96
    //     0x132214c: ldr             lr, [x21, lr, lsl #3]
    //     0x1322150: blr             lr
    // 0x1322154: cmp             w0, NULL
    // 0x1322158: b.ne            #0x132216c
    // 0x132215c: ldur            x0, [fp, #-0x28]
    // 0x1322160: LoadField: r1 = r0->field_f
    //     0x1322160: ldur            w1, [x0, #0xf]
    // 0x1322164: DecompressPointer r1
    //     0x1322164: add             x1, x1, HEAP, lsl #32
    // 0x1322168: mov             x0, x1
    // 0x132216c: ldur            x1, [fp, #-0x30]
    // 0x1322170: ArrayStore: r1[2] = r0  ; List_4
    //     0x1322170: add             x25, x1, #0x17
    //     0x1322174: str             w0, [x25]
    //     0x1322178: tbz             w0, #0, #0x1322194
    //     0x132217c: ldurb           w16, [x1, #-1]
    //     0x1322180: ldurb           w17, [x0, #-1]
    //     0x1322184: and             x16, x17, x16, lsr #2
    //     0x1322188: tst             x16, HEAP, lsr #32
    //     0x132218c: b.eq            #0x1322194
    //     0x1322190: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1322194: ldur            x16, [fp, #-0x30]
    // 0x1322198: str             x16, [SP]
    // 0x132219c: r0 = _interpolate()
    //     0x132219c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13221a0: b               #0x13221a8
    // 0x13221a4: ldur            x0, [fp, #-0x18]
    // 0x13221a8: ldur            x4, [fp, #-0x10]
    // 0x13221ac: mov             x3, x0
    // 0x13221b0: ldur            x2, [fp, #-0x20]
    // 0x13221b4: b               #0x1321e88
    // 0x13221b8: ldur            x2, [fp, #-8]
    // 0x13221bc: ldur            x0, [fp, #-0x18]
    // 0x13221c0: LoadField: r1 = r2->field_c3
    //     0x13221c0: ldur            w1, [x2, #0xc3]
    // 0x13221c4: DecompressPointer r1
    //     0x13221c4: add             x1, x1, HEAP, lsl #32
    // 0x13221c8: r0 = value()
    //     0x13221c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13221cc: LoadField: d0 = r0->field_7
    //     0x13221cc: ldur            d0, [x0, #7]
    // 0x13221d0: fcmp            d0, d0
    // 0x13221d4: b.vs            #0x13224e8
    // 0x13221d8: fcvtzs          x0, d0
    // 0x13221dc: asr             x16, x0, #0x1e
    // 0x13221e0: cmp             x16, x0, asr #63
    // 0x13221e4: b.ne            #0x13224e8
    // 0x13221e8: lsl             x0, x0, #1
    // 0x13221ec: ldur            x2, [fp, #-8]
    // 0x13221f0: stur            x0, [fp, #-0x28]
    // 0x13221f4: LoadField: r3 = r2->field_4b
    //     0x13221f4: ldur            w3, [x2, #0x4b]
    // 0x13221f8: DecompressPointer r3
    //     0x13221f8: add             x3, x3, HEAP, lsl #32
    // 0x13221fc: mov             x1, x3
    // 0x1322200: stur            x3, [fp, #-0x20]
    // 0x1322204: r0 = value()
    //     0x1322204: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1322208: LoadField: r1 = r0->field_b
    //     0x1322208: ldur            w1, [x0, #0xb]
    // 0x132220c: DecompressPointer r1
    //     0x132220c: add             x1, x1, HEAP, lsl #32
    // 0x1322210: cmp             w1, NULL
    // 0x1322214: b.ne            #0x1322220
    // 0x1322218: r2 = Null
    //     0x1322218: mov             x2, NULL
    // 0x132221c: b               #0x1322248
    // 0x1322220: LoadField: r0 = r1->field_1f
    //     0x1322220: ldur            w0, [x1, #0x1f]
    // 0x1322224: DecompressPointer r0
    //     0x1322224: add             x0, x0, HEAP, lsl #32
    // 0x1322228: cmp             w0, NULL
    // 0x132222c: b.ne            #0x1322238
    // 0x1322230: r0 = Null
    //     0x1322230: mov             x0, NULL
    // 0x1322234: b               #0x1322244
    // 0x1322238: LoadField: r1 = r0->field_13
    //     0x1322238: ldur            w1, [x0, #0x13]
    // 0x132223c: DecompressPointer r1
    //     0x132223c: add             x1, x1, HEAP, lsl #32
    // 0x1322240: mov             x0, x1
    // 0x1322244: mov             x2, x0
    // 0x1322248: ldur            x0, [fp, #-8]
    // 0x132224c: stur            x2, [fp, #-0x30]
    // 0x1322250: LoadField: r1 = r0->field_c3
    //     0x1322250: ldur            w1, [x0, #0xc3]
    // 0x1322254: DecompressPointer r1
    //     0x1322254: add             x1, x1, HEAP, lsl #32
    // 0x1322258: r0 = value()
    //     0x1322258: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x132225c: mov             x1, x0
    // 0x1322260: ldur            x0, [fp, #-0x30]
    // 0x1322264: r2 = 60
    //     0x1322264: movz            x2, #0x3c
    // 0x1322268: branchIfSmi(r0, 0x1322274)
    //     0x1322268: tbz             w0, #0, #0x1322274
    // 0x132226c: r2 = LoadClassIdInstr(r0)
    //     0x132226c: ldur            x2, [x0, #-1]
    //     0x1322270: ubfx            x2, x2, #0xc, #0x14
    // 0x1322274: stp             x1, x0, [SP]
    // 0x1322278: mov             x0, x2
    // 0x132227c: mov             lr, x0
    // 0x1322280: ldr             lr, [x21, lr, lsl #3]
    // 0x1322284: blr             lr
    // 0x1322288: eor             x2, x0, #0x10
    // 0x132228c: ldur            x0, [fp, #-8]
    // 0x1322290: stur            x2, [fp, #-0x40]
    // 0x1322294: LoadField: r3 = r0->field_8f
    //     0x1322294: ldur            w3, [x0, #0x8f]
    // 0x1322298: DecompressPointer r3
    //     0x1322298: add             x3, x3, HEAP, lsl #32
    // 0x132229c: stur            x3, [fp, #-0x38]
    // 0x13222a0: LoadField: r1 = r3->field_27
    //     0x13222a0: ldur            w1, [x3, #0x27]
    // 0x13222a4: DecompressPointer r1
    //     0x13222a4: add             x1, x1, HEAP, lsl #32
    // 0x13222a8: LoadField: r4 = r1->field_7
    //     0x13222a8: ldur            w4, [x1, #7]
    // 0x13222ac: DecompressPointer r4
    //     0x13222ac: add             x4, x4, HEAP, lsl #32
    // 0x13222b0: ldur            x1, [fp, #-0x20]
    // 0x13222b4: stur            x4, [fp, #-0x30]
    // 0x13222b8: r0 = value()
    //     0x13222b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13222bc: LoadField: r1 = r0->field_b
    //     0x13222bc: ldur            w1, [x0, #0xb]
    // 0x13222c0: DecompressPointer r1
    //     0x13222c0: add             x1, x1, HEAP, lsl #32
    // 0x13222c4: cmp             w1, NULL
    // 0x13222c8: b.ne            #0x13222d4
    // 0x13222cc: r2 = Null
    //     0x13222cc: mov             x2, NULL
    // 0x13222d0: b               #0x13222fc
    // 0x13222d4: LoadField: r0 = r1->field_1f
    //     0x13222d4: ldur            w0, [x1, #0x1f]
    // 0x13222d8: DecompressPointer r0
    //     0x13222d8: add             x0, x0, HEAP, lsl #32
    // 0x13222dc: cmp             w0, NULL
    // 0x13222e0: b.ne            #0x13222ec
    // 0x13222e4: r0 = Null
    //     0x13222e4: mov             x0, NULL
    // 0x13222e8: b               #0x13222f8
    // 0x13222ec: LoadField: r1 = r0->field_1b
    //     0x13222ec: ldur            w1, [x0, #0x1b]
    // 0x13222f0: DecompressPointer r1
    //     0x13222f0: add             x1, x1, HEAP, lsl #32
    // 0x13222f4: mov             x0, x1
    // 0x13222f8: mov             x2, x0
    // 0x13222fc: ldur            x1, [fp, #-8]
    // 0x1322300: ldur            x0, [fp, #-0x38]
    // 0x1322304: LoadField: r3 = r0->field_27
    //     0x1322304: ldur            w3, [x0, #0x27]
    // 0x1322308: DecompressPointer r3
    //     0x1322308: add             x3, x3, HEAP, lsl #32
    // 0x132230c: LoadField: r0 = r3->field_7
    //     0x132230c: ldur            w0, [x3, #7]
    // 0x1322310: DecompressPointer r0
    //     0x1322310: add             x0, x0, HEAP, lsl #32
    // 0x1322314: r3 = LoadClassIdInstr(r2)
    //     0x1322314: ldur            x3, [x2, #-1]
    //     0x1322318: ubfx            x3, x3, #0xc, #0x14
    // 0x132231c: stp             x0, x2, [SP]
    // 0x1322320: mov             x0, x3
    // 0x1322324: mov             lr, x0
    // 0x1322328: ldr             lr, [x21, lr, lsl #3]
    // 0x132232c: blr             lr
    // 0x1322330: eor             x2, x0, #0x10
    // 0x1322334: ldur            x0, [fp, #-8]
    // 0x1322338: stur            x2, [fp, #-0x38]
    // 0x132233c: LoadField: r1 = r0->field_b3
    //     0x132233c: ldur            w1, [x0, #0xb3]
    // 0x1322340: DecompressPointer r1
    //     0x1322340: add             x1, x1, HEAP, lsl #32
    // 0x1322344: r0 = value()
    //     0x1322344: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1322348: mov             x2, x0
    // 0x132234c: ldur            x0, [fp, #-8]
    // 0x1322350: stur            x2, [fp, #-0x48]
    // 0x1322354: LoadField: r1 = r0->field_b7
    //     0x1322354: ldur            w1, [x0, #0xb7]
    // 0x1322358: DecompressPointer r1
    //     0x1322358: add             x1, x1, HEAP, lsl #32
    // 0x132235c: r0 = value()
    //     0x132235c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1322360: mov             x2, x0
    // 0x1322364: ldur            x0, [fp, #-8]
    // 0x1322368: stur            x2, [fp, #-0x58]
    // 0x132236c: LoadField: r3 = r0->field_d7
    //     0x132236c: ldur            w3, [x0, #0xd7]
    // 0x1322370: DecompressPointer r3
    //     0x1322370: add             x3, x3, HEAP, lsl #32
    // 0x1322374: ldur            x1, [fp, #-0x20]
    // 0x1322378: stur            x3, [fp, #-0x50]
    // 0x132237c: r0 = value()
    //     0x132237c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1322380: LoadField: r1 = r0->field_b
    //     0x1322380: ldur            w1, [x0, #0xb]
    // 0x1322384: DecompressPointer r1
    //     0x1322384: add             x1, x1, HEAP, lsl #32
    // 0x1322388: cmp             w1, NULL
    // 0x132238c: b.ne            #0x1322398
    // 0x1322390: r10 = Null
    //     0x1322390: mov             x10, NULL
    // 0x1322394: b               #0x13223c0
    // 0x1322398: LoadField: r0 = r1->field_1f
    //     0x1322398: ldur            w0, [x1, #0x1f]
    // 0x132239c: DecompressPointer r0
    //     0x132239c: add             x0, x0, HEAP, lsl #32
    // 0x13223a0: cmp             w0, NULL
    // 0x13223a4: b.ne            #0x13223b0
    // 0x13223a8: r0 = Null
    //     0x13223a8: mov             x0, NULL
    // 0x13223ac: b               #0x13223bc
    // 0x13223b0: LoadField: r1 = r0->field_2b
    //     0x13223b0: ldur            w1, [x0, #0x2b]
    // 0x13223b4: DecompressPointer r1
    //     0x13223b4: add             x1, x1, HEAP, lsl #32
    // 0x13223b8: mov             x0, x1
    // 0x13223bc: mov             x10, x0
    // 0x13223c0: ldur            x1, [fp, #-8]
    // 0x13223c4: ldur            x9, [fp, #-0x10]
    // 0x13223c8: ldur            x8, [fp, #-0x18]
    // 0x13223cc: ldur            x5, [fp, #-0x40]
    // 0x13223d0: ldur            x4, [fp, #-0x38]
    // 0x13223d4: ldur            x3, [fp, #-0x48]
    // 0x13223d8: ldur            x0, [fp, #-0x58]
    // 0x13223dc: ldur            x2, [fp, #-0x50]
    // 0x13223e0: ldur            x6, [fp, #-0x30]
    // 0x13223e4: ldur            x7, [fp, #-0x28]
    // 0x13223e8: stur            x10, [fp, #-0x68]
    // 0x13223ec: LoadField: r11 = r1->field_bb
    //     0x13223ec: ldur            w11, [x1, #0xbb]
    // 0x13223f0: DecompressPointer r11
    //     0x13223f0: add             x11, x11, HEAP, lsl #32
    // 0x13223f4: stur            x11, [fp, #-0x60]
    // 0x13223f8: LoadField: r12 = r1->field_6f
    //     0x13223f8: ldur            w12, [x1, #0x6f]
    // 0x13223fc: DecompressPointer r12
    //     0x13223fc: add             x12, x12, HEAP, lsl #32
    // 0x1322400: stur            x12, [fp, #-0x20]
    // 0x1322404: r0 = EventData()
    //     0x1322404: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x1322408: mov             x1, x0
    // 0x132240c: ldur            x0, [fp, #-0x60]
    // 0x1322410: stur            x1, [fp, #-0x70]
    // 0x1322414: r17 = 259
    //     0x1322414: movz            x17, #0x103
    // 0x1322418: str             w0, [x1, x17]
    // 0x132241c: ldur            x0, [fp, #-0x28]
    // 0x1322420: r17 = 287
    //     0x1322420: movz            x17, #0x11f
    // 0x1322424: str             w0, [x1, x17]
    // 0x1322428: ldur            x0, [fp, #-0x40]
    // 0x132242c: r17 = 291
    //     0x132242c: movz            x17, #0x123
    // 0x1322430: str             w0, [x1, x17]
    // 0x1322434: ldur            x0, [fp, #-0x10]
    // 0x1322438: r17 = 303
    //     0x1322438: movz            x17, #0x12f
    // 0x132243c: str             w0, [x1, x17]
    // 0x1322440: ldur            x0, [fp, #-0x48]
    // 0x1322444: r17 = 307
    //     0x1322444: movz            x17, #0x133
    // 0x1322448: str             w0, [x1, x17]
    // 0x132244c: ldur            x0, [fp, #-0x18]
    // 0x1322450: r17 = 311
    //     0x1322450: movz            x17, #0x137
    // 0x1322454: str             w0, [x1, x17]
    // 0x1322458: ldur            x0, [fp, #-0x58]
    // 0x132245c: r17 = 315
    //     0x132245c: movz            x17, #0x13b
    // 0x1322460: str             w0, [x1, x17]
    // 0x1322464: ldur            x0, [fp, #-0x30]
    // 0x1322468: r17 = 295
    //     0x1322468: movz            x17, #0x127
    // 0x132246c: str             w0, [x1, x17]
    // 0x1322470: ldur            x0, [fp, #-0x38]
    // 0x1322474: r17 = 299
    //     0x1322474: movz            x17, #0x12b
    // 0x1322478: str             w0, [x1, x17]
    // 0x132247c: ldur            x0, [fp, #-0x68]
    // 0x1322480: r17 = 319
    //     0x1322480: movz            x17, #0x13f
    // 0x1322484: str             w0, [x1, x17]
    // 0x1322488: ldur            x0, [fp, #-0x20]
    // 0x132248c: r17 = 323
    //     0x132248c: movz            x17, #0x143
    // 0x1322490: str             w0, [x1, x17]
    // 0x1322494: ldur            x0, [fp, #-0x50]
    // 0x1322498: r17 = 327
    //     0x1322498: movz            x17, #0x147
    // 0x132249c: str             w0, [x1, x17]
    // 0x13224a0: r0 = EventsRequest()
    //     0x13224a0: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x13224a4: mov             x1, x0
    // 0x13224a8: r0 = "rating_review_feedback_submit_clicked"
    //     0x13224a8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a28] "rating_review_feedback_submit_clicked"
    //     0x13224ac: ldr             x0, [x0, #0xa28]
    // 0x13224b0: StoreField: r1->field_7 = r0
    //     0x13224b0: stur            w0, [x1, #7]
    // 0x13224b4: ldur            x0, [fp, #-0x70]
    // 0x13224b8: StoreField: r1->field_b = r0
    //     0x13224b8: stur            w0, [x1, #0xb]
    // 0x13224bc: mov             x2, x1
    // 0x13224c0: ldur            x1, [fp, #-8]
    // 0x13224c4: r0 = postEvents()
    //     0x13224c4: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x13224c8: r0 = Null
    //     0x13224c8: mov             x0, NULL
    // 0x13224cc: LeaveFrame
    //     0x13224cc: mov             SP, fp
    //     0x13224d0: ldp             fp, lr, [SP], #0x10
    // 0x13224d4: ret
    //     0x13224d4: ret             
    // 0x13224d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13224d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13224dc: b               #0x1321e48
    // 0x13224e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13224e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13224e4: b               #0x1321e9c
    // 0x13224e8: SaveReg d0
    //     0x13224e8: str             q0, [SP, #-0x10]!
    // 0x13224ec: r0 = 74
    //     0x13224ec: movz            x0, #0x4a
    // 0x13224f0: r30 = DoubleToIntegerStub
    //     0x13224f0: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0x13224f4: LoadField: r30 = r30->field_7
    //     0x13224f4: ldur            lr, [lr, #7]
    // 0x13224f8: blr             lr
    // 0x13224fc: RestoreReg d0
    //     0x13224fc: ldr             q0, [SP], #0x10
    // 0x1322500: b               #0x13221ec
  }
  _ getImageVideoFromGallery(/* No info */) async {
    // ** addr: 0x140bddc, size: 0x6a4
    // 0x140bddc: EnterFrame
    //     0x140bddc: stp             fp, lr, [SP, #-0x10]!
    //     0x140bde0: mov             fp, SP
    // 0x140bde4: AllocStack(0x40)
    //     0x140bde4: sub             SP, SP, #0x40
    // 0x140bde8: SetupParameters(RatingReviewOrderController this /* r1 => r1, fp-0x10 */)
    //     0x140bde8: stur            NULL, [fp, #-8]
    //     0x140bdec: stur            x1, [fp, #-0x10]
    // 0x140bdf0: CheckStackOverflow
    //     0x140bdf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140bdf4: cmp             SP, x16
    //     0x140bdf8: b.ls            #0x140c460
    // 0x140bdfc: InitAsync() -> Future
    //     0x140bdfc: mov             x0, NULL
    //     0x140be00: bl              #0x6326e0  ; InitAsyncStub
    // 0x140be04: ldur            x0, [fp, #-0x10]
    // 0x140be08: LoadField: r1 = r0->field_a3
    //     0x140be08: ldur            w1, [x0, #0xa3]
    // 0x140be0c: DecompressPointer r1
    //     0x140be0c: add             x1, x1, HEAP, lsl #32
    // 0x140be10: r0 = value()
    //     0x140be10: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140be14: mov             x1, x0
    // 0x140be18: ldur            x0, [fp, #-0x10]
    // 0x140be1c: LoadField: r2 = r0->field_a7
    //     0x140be1c: ldur            x2, [x0, #0xa7]
    // 0x140be20: scvtf           d0, x2
    // 0x140be24: LoadField: d1 = r1->field_7
    //     0x140be24: ldur            d1, [x1, #7]
    // 0x140be28: fcmp            d0, d1
    // 0x140be2c: b.gt            #0x140be9c
    // 0x140be30: LoadField: r1 = r0->field_a3
    //     0x140be30: ldur            w1, [x0, #0xa3]
    // 0x140be34: DecompressPointer r1
    //     0x140be34: add             x1, x1, HEAP, lsl #32
    // 0x140be38: r0 = value()
    //     0x140be38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140be3c: mov             x1, x0
    // 0x140be40: ldur            x0, [fp, #-0x10]
    // 0x140be44: LoadField: r2 = r0->field_a7
    //     0x140be44: ldur            x2, [x0, #0xa7]
    // 0x140be48: scvtf           d0, x2
    // 0x140be4c: LoadField: d1 = r1->field_7
    //     0x140be4c: ldur            d1, [x1, #7]
    // 0x140be50: fcmp            d0, d1
    // 0x140be54: b.le            #0x140c448
    // 0x140be58: LoadField: r1 = r0->field_93
    //     0x140be58: ldur            w1, [x0, #0x93]
    // 0x140be5c: DecompressPointer r1
    //     0x140be5c: add             x1, x1, HEAP, lsl #32
    // 0x140be60: r0 = value()
    //     0x140be60: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140be64: r1 = LoadClassIdInstr(r0)
    //     0x140be64: ldur            x1, [x0, #-1]
    //     0x140be68: ubfx            x1, x1, #0xc, #0x14
    // 0x140be6c: str             x0, [SP]
    // 0x140be70: mov             x0, x1
    // 0x140be74: r0 = GDT[cid_x0 + 0xc898]()
    //     0x140be74: movz            x17, #0xc898
    //     0x140be78: add             lr, x0, x17
    //     0x140be7c: ldr             lr, [x21, lr, lsl #3]
    //     0x140be80: blr             lr
    // 0x140be84: r1 = LoadInt32Instr(r0)
    //     0x140be84: sbfx            x1, x0, #1, #0x1f
    //     0x140be88: tbz             w0, #0, #0x140be90
    //     0x140be8c: ldur            x1, [x0, #7]
    // 0x140be90: cmp             x1, #5
    // 0x140be94: b.ge            #0x140c448
    // 0x140be98: ldur            x0, [fp, #-0x10]
    // 0x140be9c: LoadField: r1 = r0->field_9b
    //     0x140be9c: ldur            w1, [x0, #0x9b]
    // 0x140bea0: DecompressPointer r1
    //     0x140bea0: add             x1, x1, HEAP, lsl #32
    // 0x140bea4: r0 = value()
    //     0x140bea4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140bea8: r1 = LoadInt32Instr(r0)
    //     0x140bea8: sbfx            x1, x0, #1, #0x1f
    //     0x140beac: tbz             w0, #0, #0x140beb4
    //     0x140beb0: ldur            x1, [x0, #7]
    // 0x140beb4: cmp             x1, #3
    // 0x140beb8: b.ge            #0x140befc
    // 0x140bebc: ldur            x0, [fp, #-0x10]
    // 0x140bec0: LoadField: r1 = r0->field_9f
    //     0x140bec0: ldur            w1, [x0, #0x9f]
    // 0x140bec4: DecompressPointer r1
    //     0x140bec4: add             x1, x1, HEAP, lsl #32
    // 0x140bec8: r0 = value()
    //     0x140bec8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140becc: cbnz            w0, #0x140befc
    // 0x140bed0: r0 = ImagePicker()
    //     0x140bed0: bl              #0x9a787c  ; AllocateImagePickerStub -> ImagePicker (size=0x8)
    // 0x140bed4: r16 = 200
    //     0x140bed4: movz            x16, #0xc8
    // 0x140bed8: str             x16, [SP]
    // 0x140bedc: mov             x1, x0
    // 0x140bee0: r4 = const [0, 0x2, 0x1, 0x1, imageQuality, 0x1, null]
    //     0x140bee0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b60] List(7) [0, 0x2, 0x1, 0x1, "imageQuality", 0x1, Null]
    //     0x140bee4: ldr             x4, [x4, #0xb60]
    // 0x140bee8: r0 = pickMedia()
    //     0x140bee8: bl              #0x9a726c  ; [package:image_picker/image_picker.dart] ImagePicker::pickMedia
    // 0x140beec: mov             x1, x0
    // 0x140bef0: stur            x1, [fp, #-0x18]
    // 0x140bef4: r0 = Await()
    //     0x140bef4: bl              #0x63248c  ; AwaitStub
    // 0x140bef8: b               #0x140bf88
    // 0x140befc: ldur            x0, [fp, #-0x10]
    // 0x140bf00: LoadField: r1 = r0->field_9b
    //     0x140bf00: ldur            w1, [x0, #0x9b]
    // 0x140bf04: DecompressPointer r1
    //     0x140bf04: add             x1, x1, HEAP, lsl #32
    // 0x140bf08: r0 = value()
    //     0x140bf08: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140bf0c: cmp             w0, #6
    // 0x140bf10: b.ne            #0x140bf60
    // 0x140bf14: ldur            x0, [fp, #-0x10]
    // 0x140bf18: LoadField: r1 = r0->field_9f
    //     0x140bf18: ldur            w1, [x0, #0x9f]
    // 0x140bf1c: DecompressPointer r1
    //     0x140bf1c: add             x1, x1, HEAP, lsl #32
    // 0x140bf20: r0 = value()
    //     0x140bf20: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140bf24: cbnz            w0, #0x140bf60
    // 0x140bf28: r0 = ImagePicker()
    //     0x140bf28: bl              #0x9a787c  ; AllocateImagePickerStub -> ImagePicker (size=0x8)
    // 0x140bf2c: r16 = Instance_Duration
    //     0x140bf2c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b68] Obj!Duration@d77901
    //     0x140bf30: ldr             x16, [x16, #0xb68]
    // 0x140bf34: r30 = Instance_CameraDevice
    //     0x140bf34: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b70] Obj!CameraDevice@d70b81
    //     0x140bf38: ldr             lr, [lr, #0xb70]
    // 0x140bf3c: stp             lr, x16, [SP]
    // 0x140bf40: mov             x1, x0
    // 0x140bf44: r4 = const [0, 0x3, 0x2, 0x1, maxDuration, 0x1, preferredCameraDevice, 0x2, null]
    //     0x140bf44: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b78] List(9) [0, 0x3, 0x2, 0x1, "maxDuration", 0x1, "preferredCameraDevice", 0x2, Null]
    //     0x140bf48: ldr             x4, [x4, #0xb78]
    // 0x140bf4c: r0 = pickVideo()
    //     0x140bf4c: bl              #0x9a7760  ; [package:image_picker/image_picker.dart] ImagePicker::pickVideo
    // 0x140bf50: mov             x1, x0
    // 0x140bf54: stur            x1, [fp, #-0x18]
    // 0x140bf58: r0 = Await()
    //     0x140bf58: bl              #0x63248c  ; AwaitStub
    // 0x140bf5c: b               #0x140bf88
    // 0x140bf60: r0 = ImagePicker()
    //     0x140bf60: bl              #0x9a787c  ; AllocateImagePickerStub -> ImagePicker (size=0x8)
    // 0x140bf64: r16 = 200
    //     0x140bf64: movz            x16, #0xc8
    // 0x140bf68: str             x16, [SP]
    // 0x140bf6c: mov             x1, x0
    // 0x140bf70: r4 = const [0, 0x2, 0x1, 0x1, imageQuality, 0x1, null]
    //     0x140bf70: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b60] List(7) [0, 0x2, 0x1, 0x1, "imageQuality", 0x1, Null]
    //     0x140bf74: ldr             x4, [x4, #0xb60]
    // 0x140bf78: r0 = pickImage()
    //     0x140bf78: bl              #0xa302d8  ; [package:image_picker/image_picker.dart] ImagePicker::pickImage
    // 0x140bf7c: mov             x1, x0
    // 0x140bf80: stur            x1, [fp, #-0x18]
    // 0x140bf84: r0 = Await()
    //     0x140bf84: bl              #0x63248c  ; AwaitStub
    // 0x140bf88: cmp             w0, NULL
    // 0x140bf8c: b.eq            #0x140c458
    // 0x140bf90: LoadField: r1 = r0->field_7
    //     0x140bf90: ldur            w1, [x0, #7]
    // 0x140bf94: DecompressPointer r1
    //     0x140bf94: add             x1, x1, HEAP, lsl #32
    // 0x140bf98: LoadField: r0 = r1->field_7
    //     0x140bf98: ldur            w0, [x1, #7]
    // 0x140bf9c: DecompressPointer r0
    //     0x140bf9c: add             x0, x0, HEAP, lsl #32
    // 0x140bfa0: stur            x0, [fp, #-0x18]
    // 0x140bfa4: r0 = current()
    //     0x140bfa4: bl              #0x62b7cc  ; [dart:io] IOOverrides::current
    // 0x140bfa8: r0 = _File()
    //     0x140bfa8: bl              #0x62f3ac  ; Allocate_FileStub -> _File (size=0x10)
    // 0x140bfac: mov             x2, x0
    // 0x140bfb0: ldur            x0, [fp, #-0x18]
    // 0x140bfb4: stur            x2, [fp, #-0x20]
    // 0x140bfb8: StoreField: r2->field_7 = r0
    //     0x140bfb8: stur            w0, [x2, #7]
    // 0x140bfbc: mov             x1, x0
    // 0x140bfc0: r0 = _toUtf8Array()
    //     0x140bfc0: bl              #0x62b684  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x140bfc4: ldur            x2, [fp, #-0x20]
    // 0x140bfc8: StoreField: r2->field_b = r0
    //     0x140bfc8: stur            w0, [x2, #0xb]
    //     0x140bfcc: ldurb           w16, [x2, #-1]
    //     0x140bfd0: ldurb           w17, [x0, #-1]
    //     0x140bfd4: and             x16, x17, x16, lsr #2
    //     0x140bfd8: tst             x16, HEAP, lsr #32
    //     0x140bfdc: b.eq            #0x140bfe4
    //     0x140bfe0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x140bfe4: ldur            x1, [fp, #-0x18]
    // 0x140bfe8: r0 = lookupMimeType()
    //     0x140bfe8: bl              #0x8ab24c  ; [package:mime/src/mime_type.dart] ::lookupMimeType
    // 0x140bfec: cmp             w0, NULL
    // 0x140bff0: b.ne            #0x140bffc
    // 0x140bff4: r0 = Null
    //     0x140bff4: mov             x0, NULL
    // 0x140bff8: b               #0x140c020
    // 0x140bffc: r1 = LoadClassIdInstr(r0)
    //     0x140bffc: ldur            x1, [x0, #-1]
    //     0x140c000: ubfx            x1, x1, #0xc, #0x14
    // 0x140c004: mov             x16, x0
    // 0x140c008: mov             x0, x1
    // 0x140c00c: mov             x1, x16
    // 0x140c010: r2 = "/"
    //     0x140c010: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x140c014: r0 = GDT[cid_x0 + -0xffc]()
    //     0x140c014: sub             lr, x0, #0xffc
    //     0x140c018: ldr             lr, [x21, lr, lsl #3]
    //     0x140c01c: blr             lr
    // 0x140c020: ldur            x1, [fp, #-0x20]
    // 0x140c024: stur            x0, [fp, #-0x18]
    // 0x140c028: r0 = getFileSize()
    //     0x140c028: bl              #0x140c740  ; [package:customer_app/app/core/utils/utils.dart] Utils::getFileSize
    // 0x140c02c: mov             x1, x0
    // 0x140c030: stur            x1, [fp, #-0x28]
    // 0x140c034: r0 = Await()
    //     0x140c034: bl              #0x63248c  ; AwaitStub
    // 0x140c038: mov             x3, x0
    // 0x140c03c: ldur            x2, [fp, #-0x18]
    // 0x140c040: stur            x3, [fp, #-0x28]
    // 0x140c044: cmp             w2, NULL
    // 0x140c048: b.ne            #0x140c054
    // 0x140c04c: r0 = Null
    //     0x140c04c: mov             x0, NULL
    // 0x140c050: b               #0x140c080
    // 0x140c054: LoadField: r0 = r2->field_b
    //     0x140c054: ldur            w0, [x2, #0xb]
    // 0x140c058: r1 = LoadInt32Instr(r0)
    //     0x140c058: sbfx            x1, x0, #1, #0x1f
    // 0x140c05c: mov             x0, x1
    // 0x140c060: r1 = 0
    //     0x140c060: movz            x1, #0
    // 0x140c064: cmp             x1, x0
    // 0x140c068: b.hs            #0x140c468
    // 0x140c06c: LoadField: r0 = r2->field_f
    //     0x140c06c: ldur            w0, [x2, #0xf]
    // 0x140c070: DecompressPointer r0
    //     0x140c070: add             x0, x0, HEAP, lsl #32
    // 0x140c074: LoadField: r1 = r0->field_f
    //     0x140c074: ldur            w1, [x0, #0xf]
    // 0x140c078: DecompressPointer r1
    //     0x140c078: add             x1, x1, HEAP, lsl #32
    // 0x140c07c: mov             x0, x1
    // 0x140c080: r1 = LoadClassIdInstr(r0)
    //     0x140c080: ldur            x1, [x0, #-1]
    //     0x140c084: ubfx            x1, x1, #0xc, #0x14
    // 0x140c088: r16 = "image"
    //     0x140c088: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x140c08c: stp             x16, x0, [SP]
    // 0x140c090: mov             x0, x1
    // 0x140c094: mov             lr, x0
    // 0x140c098: ldr             lr, [x21, lr, lsl #3]
    // 0x140c09c: blr             lr
    // 0x140c0a0: tbnz            w0, #4, #0x140c0dc
    // 0x140c0a4: ldur            x2, [fp, #-0x28]
    // 0x140c0a8: cmp             w2, NULL
    // 0x140c0ac: b.eq            #0x140c46c
    // 0x140c0b0: r0 = LoadInt32Instr(r2)
    //     0x140c0b0: sbfx            x0, x2, #1, #0x1f
    //     0x140c0b4: tbz             w2, #0, #0x140c0bc
    //     0x140c0b8: ldur            x0, [x2, #7]
    // 0x140c0bc: cmp             x0, #0xc00, lsl #12
    // 0x140c0c0: b.le            #0x140c0e0
    // 0x140c0c4: ldur            x1, [fp, #-0x10]
    // 0x140c0c8: r2 = "Image should be less than 12MB"
    //     0x140c0c8: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b80] "Image should be less than 12MB"
    //     0x140c0cc: ldr             x2, [x2, #0xb80]
    // 0x140c0d0: r0 = showErrorSnackBar()
    //     0x140c0d0: bl              #0x9a6030  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorSnackBar
    // 0x140c0d4: r0 = Null
    //     0x140c0d4: mov             x0, NULL
    // 0x140c0d8: r0 = ReturnAsyncNotFuture()
    //     0x140c0d8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x140c0dc: ldur            x2, [fp, #-0x28]
    // 0x140c0e0: ldur            x3, [fp, #-0x18]
    // 0x140c0e4: cmp             w3, NULL
    // 0x140c0e8: b.ne            #0x140c0f4
    // 0x140c0ec: r0 = Null
    //     0x140c0ec: mov             x0, NULL
    // 0x140c0f0: b               #0x140c120
    // 0x140c0f4: LoadField: r0 = r3->field_b
    //     0x140c0f4: ldur            w0, [x3, #0xb]
    // 0x140c0f8: r1 = LoadInt32Instr(r0)
    //     0x140c0f8: sbfx            x1, x0, #1, #0x1f
    // 0x140c0fc: mov             x0, x1
    // 0x140c100: r1 = 0
    //     0x140c100: movz            x1, #0
    // 0x140c104: cmp             x1, x0
    // 0x140c108: b.hs            #0x140c470
    // 0x140c10c: LoadField: r0 = r3->field_f
    //     0x140c10c: ldur            w0, [x3, #0xf]
    // 0x140c110: DecompressPointer r0
    //     0x140c110: add             x0, x0, HEAP, lsl #32
    // 0x140c114: LoadField: r1 = r0->field_f
    //     0x140c114: ldur            w1, [x0, #0xf]
    // 0x140c118: DecompressPointer r1
    //     0x140c118: add             x1, x1, HEAP, lsl #32
    // 0x140c11c: mov             x0, x1
    // 0x140c120: r1 = LoadClassIdInstr(r0)
    //     0x140c120: ldur            x1, [x0, #-1]
    //     0x140c124: ubfx            x1, x1, #0xc, #0x14
    // 0x140c128: r16 = "video"
    //     0x140c128: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x140c12c: ldr             x16, [x16, #0xb50]
    // 0x140c130: stp             x16, x0, [SP]
    // 0x140c134: mov             x0, x1
    // 0x140c138: mov             lr, x0
    // 0x140c13c: ldr             lr, [x21, lr, lsl #3]
    // 0x140c140: blr             lr
    // 0x140c144: tbnz            w0, #4, #0x140c184
    // 0x140c148: ldur            x0, [fp, #-0x28]
    // 0x140c14c: cmp             w0, NULL
    // 0x140c150: b.eq            #0x140c474
    // 0x140c154: r1 = LoadInt32Instr(r0)
    //     0x140c154: sbfx            x1, x0, #1, #0x1f
    //     0x140c158: tbz             w0, #0, #0x140c160
    //     0x140c15c: ldur            x1, [x0, #7]
    // 0x140c160: r17 = 800
    //     0x140c160: movz            x17, #0x320, lsl #16
    // 0x140c164: cmp             x1, x17
    // 0x140c168: b.le            #0x140c188
    // 0x140c16c: ldur            x1, [fp, #-0x10]
    // 0x140c170: r2 = "Video should be less than 50MB"
    //     0x140c170: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b88] "Video should be less than 50MB"
    //     0x140c174: ldr             x2, [x2, #0xb88]
    // 0x140c178: r0 = showErrorSnackBar()
    //     0x140c178: bl              #0x9a6030  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorSnackBar
    // 0x140c17c: r0 = Null
    //     0x140c17c: mov             x0, NULL
    // 0x140c180: r0 = ReturnAsyncNotFuture()
    //     0x140c180: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x140c184: ldur            x0, [fp, #-0x28]
    // 0x140c188: ldur            x2, [fp, #-0x10]
    // 0x140c18c: LoadField: r1 = r2->field_a3
    //     0x140c18c: ldur            w1, [x2, #0xa3]
    // 0x140c190: DecompressPointer r1
    //     0x140c190: add             x1, x1, HEAP, lsl #32
    // 0x140c194: r0 = value()
    //     0x140c194: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c198: ldur            x16, [fp, #-0x28]
    // 0x140c19c: stp             x16, x0, [SP]
    // 0x140c1a0: r0 = +()
    //     0x140c1a0: bl              #0x16f4888  ; [dart:core] _Double::+
    // 0x140c1a4: mov             x1, x0
    // 0x140c1a8: ldur            x0, [fp, #-0x10]
    // 0x140c1ac: LoadField: r2 = r0->field_a7
    //     0x140c1ac: ldur            x2, [x0, #0xa7]
    // 0x140c1b0: scvtf           d0, x2
    // 0x140c1b4: LoadField: d1 = r1->field_7
    //     0x140c1b4: ldur            d1, [x1, #7]
    // 0x140c1b8: fcmp            d1, d0
    // 0x140c1bc: b.le            #0x140c1d4
    // 0x140c1c0: mov             x1, x0
    // 0x140c1c4: r2 = "Reached your limit of 100MB"
    //     0x140c1c4: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b90] "Reached your limit of 100MB"
    //     0x140c1c8: ldr             x2, [x2, #0xb90]
    // 0x140c1cc: r0 = showErrorSnackBar()
    //     0x140c1cc: bl              #0x9a6030  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorSnackBar
    // 0x140c1d0: b               #0x140c458
    // 0x140c1d4: ldur            x2, [fp, #-0x18]
    // 0x140c1d8: LoadField: r3 = r0->field_a3
    //     0x140c1d8: ldur            w3, [x0, #0xa3]
    // 0x140c1dc: DecompressPointer r3
    //     0x140c1dc: add             x3, x3, HEAP, lsl #32
    // 0x140c1e0: mov             x1, x3
    // 0x140c1e4: stur            x3, [fp, #-0x30]
    // 0x140c1e8: r0 = value()
    //     0x140c1e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c1ec: ldur            x16, [fp, #-0x28]
    // 0x140c1f0: stp             x16, x0, [SP]
    // 0x140c1f4: r0 = +()
    //     0x140c1f4: bl              #0x16f4888  ; [dart:core] _Double::+
    // 0x140c1f8: ldur            x1, [fp, #-0x30]
    // 0x140c1fc: mov             x2, x0
    // 0x140c200: r0 = value=()
    //     0x140c200: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140c204: ldur            x2, [fp, #-0x18]
    // 0x140c208: cmp             w2, NULL
    // 0x140c20c: b.ne            #0x140c218
    // 0x140c210: r0 = Null
    //     0x140c210: mov             x0, NULL
    // 0x140c214: b               #0x140c244
    // 0x140c218: LoadField: r0 = r2->field_b
    //     0x140c218: ldur            w0, [x2, #0xb]
    // 0x140c21c: r1 = LoadInt32Instr(r0)
    //     0x140c21c: sbfx            x1, x0, #1, #0x1f
    // 0x140c220: mov             x0, x1
    // 0x140c224: r1 = 0
    //     0x140c224: movz            x1, #0
    // 0x140c228: cmp             x1, x0
    // 0x140c22c: b.hs            #0x140c478
    // 0x140c230: LoadField: r0 = r2->field_f
    //     0x140c230: ldur            w0, [x2, #0xf]
    // 0x140c234: DecompressPointer r0
    //     0x140c234: add             x0, x0, HEAP, lsl #32
    // 0x140c238: LoadField: r1 = r0->field_f
    //     0x140c238: ldur            w1, [x0, #0xf]
    // 0x140c23c: DecompressPointer r1
    //     0x140c23c: add             x1, x1, HEAP, lsl #32
    // 0x140c240: mov             x0, x1
    // 0x140c244: r1 = LoadClassIdInstr(r0)
    //     0x140c244: ldur            x1, [x0, #-1]
    //     0x140c248: ubfx            x1, x1, #0xc, #0x14
    // 0x140c24c: r16 = "video"
    //     0x140c24c: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x140c250: ldr             x16, [x16, #0xb50]
    // 0x140c254: stp             x16, x0, [SP]
    // 0x140c258: mov             x0, x1
    // 0x140c25c: mov             lr, x0
    // 0x140c260: ldr             lr, [x21, lr, lsl #3]
    // 0x140c264: blr             lr
    // 0x140c268: tbnz            w0, #4, #0x140c328
    // 0x140c26c: ldur            x0, [fp, #-0x10]
    // 0x140c270: LoadField: r1 = r0->field_b7
    //     0x140c270: ldur            w1, [x0, #0xb7]
    // 0x140c274: DecompressPointer r1
    //     0x140c274: add             x1, x1, HEAP, lsl #32
    // 0x140c278: r2 = true
    //     0x140c278: add             x2, NULL, #0x20  ; true
    // 0x140c27c: r0 = value=()
    //     0x140c27c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140c280: ldur            x0, [fp, #-0x10]
    // 0x140c284: LoadField: r1 = r0->field_9f
    //     0x140c284: ldur            w1, [x0, #0x9f]
    // 0x140c288: DecompressPointer r1
    //     0x140c288: add             x1, x1, HEAP, lsl #32
    // 0x140c28c: r0 = value()
    //     0x140c28c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c290: r1 = LoadInt32Instr(r0)
    //     0x140c290: sbfx            x1, x0, #1, #0x1f
    //     0x140c294: tbz             w0, #0, #0x140c29c
    //     0x140c298: ldur            x1, [x0, #7]
    // 0x140c29c: cmp             x1, #2
    // 0x140c2a0: b.ge            #0x140c314
    // 0x140c2a4: ldur            x0, [fp, #-0x10]
    // 0x140c2a8: LoadField: r2 = r0->field_9f
    //     0x140c2a8: ldur            w2, [x0, #0x9f]
    // 0x140c2ac: DecompressPointer r2
    //     0x140c2ac: add             x2, x2, HEAP, lsl #32
    // 0x140c2b0: mov             x1, x2
    // 0x140c2b4: stur            x2, [fp, #-0x28]
    // 0x140c2b8: r0 = value()
    //     0x140c2b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c2bc: r1 = LoadInt32Instr(r0)
    //     0x140c2bc: sbfx            x1, x0, #1, #0x1f
    //     0x140c2c0: tbz             w0, #0, #0x140c2c8
    //     0x140c2c4: ldur            x1, [x0, #7]
    // 0x140c2c8: add             x2, x1, #1
    // 0x140c2cc: r0 = BoxInt64Instr(r2)
    //     0x140c2cc: sbfiz           x0, x2, #1, #0x1f
    //     0x140c2d0: cmp             x2, x0, asr #1
    //     0x140c2d4: b.eq            #0x140c2e0
    //     0x140c2d8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140c2dc: stur            x2, [x0, #7]
    // 0x140c2e0: ldur            x1, [fp, #-0x28]
    // 0x140c2e4: mov             x2, x0
    // 0x140c2e8: r0 = value=()
    //     0x140c2e8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140c2ec: ldur            x1, [fp, #-0x10]
    // 0x140c2f0: LoadField: r0 = r1->field_93
    //     0x140c2f0: ldur            w0, [x1, #0x93]
    // 0x140c2f4: DecompressPointer r0
    //     0x140c2f4: add             x0, x0, HEAP, lsl #32
    // 0x140c2f8: ldur            x16, [fp, #-0x20]
    // 0x140c2fc: stp             x16, x0, [SP]
    // 0x140c300: r0 = add()
    //     0x140c300: bl              #0x1654150  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0x140c304: ldur            x1, [fp, #-0x10]
    // 0x140c308: ldur            x2, [fp, #-0x20]
    // 0x140c30c: r0 = getThumbNailMedia()
    //     0x140c30c: bl              #0x140c480  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getThumbNailMedia
    // 0x140c310: b               #0x140c458
    // 0x140c314: ldur            x1, [fp, #-0x10]
    // 0x140c318: r2 = "Up to 3 images and 1 video can be selected"
    //     0x140c318: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b98] "Up to 3 images and 1 video can be selected"
    //     0x140c31c: ldr             x2, [x2, #0xb98]
    // 0x140c320: r0 = showErrorSnackBar()
    //     0x140c320: bl              #0x9a6030  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorSnackBar
    // 0x140c324: b               #0x140c458
    // 0x140c328: ldur            x2, [fp, #-0x18]
    // 0x140c32c: cmp             w2, NULL
    // 0x140c330: b.ne            #0x140c33c
    // 0x140c334: r0 = Null
    //     0x140c334: mov             x0, NULL
    // 0x140c338: b               #0x140c368
    // 0x140c33c: LoadField: r0 = r2->field_b
    //     0x140c33c: ldur            w0, [x2, #0xb]
    // 0x140c340: r1 = LoadInt32Instr(r0)
    //     0x140c340: sbfx            x1, x0, #1, #0x1f
    // 0x140c344: mov             x0, x1
    // 0x140c348: r1 = 0
    //     0x140c348: movz            x1, #0
    // 0x140c34c: cmp             x1, x0
    // 0x140c350: b.hs            #0x140c47c
    // 0x140c354: LoadField: r0 = r2->field_f
    //     0x140c354: ldur            w0, [x2, #0xf]
    // 0x140c358: DecompressPointer r0
    //     0x140c358: add             x0, x0, HEAP, lsl #32
    // 0x140c35c: LoadField: r1 = r0->field_f
    //     0x140c35c: ldur            w1, [x0, #0xf]
    // 0x140c360: DecompressPointer r1
    //     0x140c360: add             x1, x1, HEAP, lsl #32
    // 0x140c364: mov             x0, x1
    // 0x140c368: r1 = LoadClassIdInstr(r0)
    //     0x140c368: ldur            x1, [x0, #-1]
    //     0x140c36c: ubfx            x1, x1, #0xc, #0x14
    // 0x140c370: r16 = "image"
    //     0x140c370: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x140c374: stp             x16, x0, [SP]
    // 0x140c378: mov             x0, x1
    // 0x140c37c: mov             lr, x0
    // 0x140c380: ldr             lr, [x21, lr, lsl #3]
    // 0x140c384: blr             lr
    // 0x140c388: tbnz            w0, #4, #0x140c458
    // 0x140c38c: ldur            x0, [fp, #-0x10]
    // 0x140c390: LoadField: r1 = r0->field_b3
    //     0x140c390: ldur            w1, [x0, #0xb3]
    // 0x140c394: DecompressPointer r1
    //     0x140c394: add             x1, x1, HEAP, lsl #32
    // 0x140c398: r2 = true
    //     0x140c398: add             x2, NULL, #0x20  ; true
    // 0x140c39c: r0 = value=()
    //     0x140c39c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140c3a0: ldur            x0, [fp, #-0x10]
    // 0x140c3a4: LoadField: r1 = r0->field_9b
    //     0x140c3a4: ldur            w1, [x0, #0x9b]
    // 0x140c3a8: DecompressPointer r1
    //     0x140c3a8: add             x1, x1, HEAP, lsl #32
    // 0x140c3ac: r0 = value()
    //     0x140c3ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c3b0: r1 = LoadInt32Instr(r0)
    //     0x140c3b0: sbfx            x1, x0, #1, #0x1f
    //     0x140c3b4: tbz             w0, #0, #0x140c3bc
    //     0x140c3b8: ldur            x1, [x0, #7]
    // 0x140c3bc: cmp             x1, #4
    // 0x140c3c0: b.ge            #0x140c434
    // 0x140c3c4: ldur            x0, [fp, #-0x10]
    // 0x140c3c8: LoadField: r2 = r0->field_9b
    //     0x140c3c8: ldur            w2, [x0, #0x9b]
    // 0x140c3cc: DecompressPointer r2
    //     0x140c3cc: add             x2, x2, HEAP, lsl #32
    // 0x140c3d0: mov             x1, x2
    // 0x140c3d4: stur            x2, [fp, #-0x18]
    // 0x140c3d8: r0 = value()
    //     0x140c3d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140c3dc: r1 = LoadInt32Instr(r0)
    //     0x140c3dc: sbfx            x1, x0, #1, #0x1f
    //     0x140c3e0: tbz             w0, #0, #0x140c3e8
    //     0x140c3e4: ldur            x1, [x0, #7]
    // 0x140c3e8: add             x2, x1, #1
    // 0x140c3ec: r0 = BoxInt64Instr(r2)
    //     0x140c3ec: sbfiz           x0, x2, #1, #0x1f
    //     0x140c3f0: cmp             x2, x0, asr #1
    //     0x140c3f4: b.eq            #0x140c400
    //     0x140c3f8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x140c3fc: stur            x2, [x0, #7]
    // 0x140c400: ldur            x1, [fp, #-0x18]
    // 0x140c404: mov             x2, x0
    // 0x140c408: r0 = value=()
    //     0x140c408: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140c40c: ldur            x1, [fp, #-0x10]
    // 0x140c410: LoadField: r0 = r1->field_93
    //     0x140c410: ldur            w0, [x1, #0x93]
    // 0x140c414: DecompressPointer r0
    //     0x140c414: add             x0, x0, HEAP, lsl #32
    // 0x140c418: ldur            x16, [fp, #-0x20]
    // 0x140c41c: stp             x16, x0, [SP]
    // 0x140c420: r0 = add()
    //     0x140c420: bl              #0x1654150  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0x140c424: ldur            x1, [fp, #-0x10]
    // 0x140c428: ldur            x2, [fp, #-0x20]
    // 0x140c42c: r0 = getThumbNailMedia()
    //     0x140c42c: bl              #0x140c480  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getThumbNailMedia
    // 0x140c430: b               #0x140c458
    // 0x140c434: ldur            x1, [fp, #-0x10]
    // 0x140c438: r2 = "Up to 3 images and 1 video can be selected"
    //     0x140c438: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b98] "Up to 3 images and 1 video can be selected"
    //     0x140c43c: ldr             x2, [x2, #0xb98]
    // 0x140c440: r0 = showErrorSnackBar()
    //     0x140c440: bl              #0x9a6030  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::showErrorSnackBar
    // 0x140c444: b               #0x140c458
    // 0x140c448: r1 = "Reached your limit of 100MB"
    //     0x140c448: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b90] "Reached your limit of 100MB"
    //     0x140c44c: ldr             x1, [x1, #0xb90]
    // 0x140c450: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x140c450: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x140c454: r0 = showToast()
    //     0x140c454: bl              #0x89cd74  ; [package:fluttertoast/fluttertoast.dart] Fluttertoast::showToast
    // 0x140c458: r0 = Null
    //     0x140c458: mov             x0, NULL
    // 0x140c45c: r0 = ReturnAsyncNotFuture()
    //     0x140c45c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x140c460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140c460: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140c464: b               #0x140bdfc
    // 0x140c468: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x140c468: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x140c46c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x140c46c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x140c470: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x140c470: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x140c474: r0 = NullErrorSharedWithoutFPURegs()
    //     0x140c474: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x140c478: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x140c478: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x140c47c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x140c47c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getThumbNailMedia(/* No info */) async {
    // ** addr: 0x140c480, size: 0x17c
    // 0x140c480: EnterFrame
    //     0x140c480: stp             fp, lr, [SP, #-0x10]!
    //     0x140c484: mov             fp, SP
    // 0x140c488: AllocStack(0x38)
    //     0x140c488: sub             SP, SP, #0x38
    // 0x140c48c: SetupParameters(RatingReviewOrderController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x140c48c: stur            NULL, [fp, #-8]
    //     0x140c490: stur            x1, [fp, #-0x10]
    //     0x140c494: stur            x2, [fp, #-0x18]
    // 0x140c498: CheckStackOverflow
    //     0x140c498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140c49c: cmp             SP, x16
    //     0x140c4a0: b.ls            #0x140c5f0
    // 0x140c4a4: r1 = 2
    //     0x140c4a4: movz            x1, #0x2
    // 0x140c4a8: r0 = AllocateContext()
    //     0x140c4a8: bl              #0x16f6108  ; AllocateContextStub
    // 0x140c4ac: mov             x2, x0
    // 0x140c4b0: ldur            x1, [fp, #-0x10]
    // 0x140c4b4: stur            x2, [fp, #-0x20]
    // 0x140c4b8: StoreField: r2->field_f = r1
    //     0x140c4b8: stur            w1, [x2, #0xf]
    // 0x140c4bc: ldur            x0, [fp, #-0x18]
    // 0x140c4c0: StoreField: r2->field_13 = r0
    //     0x140c4c0: stur            w0, [x2, #0x13]
    // 0x140c4c4: InitAsync() -> Future
    //     0x140c4c4: mov             x0, NULL
    //     0x140c4c8: bl              #0x6326e0  ; InitAsyncStub
    // 0x140c4cc: ldur            x2, [fp, #-0x20]
    // 0x140c4d0: LoadField: r0 = r2->field_13
    //     0x140c4d0: ldur            w0, [x2, #0x13]
    // 0x140c4d4: DecompressPointer r0
    //     0x140c4d4: add             x0, x0, HEAP, lsl #32
    // 0x140c4d8: LoadField: r1 = r0->field_7
    //     0x140c4d8: ldur            w1, [x0, #7]
    // 0x140c4dc: DecompressPointer r1
    //     0x140c4dc: add             x1, x1, HEAP, lsl #32
    // 0x140c4e0: r0 = lookupMimeType()
    //     0x140c4e0: bl              #0x8ab24c  ; [package:mime/src/mime_type.dart] ::lookupMimeType
    // 0x140c4e4: cmp             w0, NULL
    // 0x140c4e8: b.ne            #0x140c4f4
    // 0x140c4ec: r2 = Null
    //     0x140c4ec: mov             x2, NULL
    // 0x140c4f0: b               #0x140c51c
    // 0x140c4f4: r1 = LoadClassIdInstr(r0)
    //     0x140c4f4: ldur            x1, [x0, #-1]
    //     0x140c4f8: ubfx            x1, x1, #0xc, #0x14
    // 0x140c4fc: mov             x16, x0
    // 0x140c500: mov             x0, x1
    // 0x140c504: mov             x1, x16
    // 0x140c508: r2 = "/"
    //     0x140c508: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x140c50c: r0 = GDT[cid_x0 + -0xffc]()
    //     0x140c50c: sub             lr, x0, #0xffc
    //     0x140c510: ldr             lr, [x21, lr, lsl #3]
    //     0x140c514: blr             lr
    // 0x140c518: mov             x2, x0
    // 0x140c51c: cmp             w2, NULL
    // 0x140c520: b.ne            #0x140c52c
    // 0x140c524: r0 = Null
    //     0x140c524: mov             x0, NULL
    // 0x140c528: b               #0x140c558
    // 0x140c52c: LoadField: r0 = r2->field_b
    //     0x140c52c: ldur            w0, [x2, #0xb]
    // 0x140c530: r1 = LoadInt32Instr(r0)
    //     0x140c530: sbfx            x1, x0, #1, #0x1f
    // 0x140c534: mov             x0, x1
    // 0x140c538: r1 = 0
    //     0x140c538: movz            x1, #0
    // 0x140c53c: cmp             x1, x0
    // 0x140c540: b.hs            #0x140c5f8
    // 0x140c544: LoadField: r0 = r2->field_f
    //     0x140c544: ldur            w0, [x2, #0xf]
    // 0x140c548: DecompressPointer r0
    //     0x140c548: add             x0, x0, HEAP, lsl #32
    // 0x140c54c: LoadField: r1 = r0->field_f
    //     0x140c54c: ldur            w1, [x0, #0xf]
    // 0x140c550: DecompressPointer r1
    //     0x140c550: add             x1, x1, HEAP, lsl #32
    // 0x140c554: mov             x0, x1
    // 0x140c558: r1 = LoadClassIdInstr(r0)
    //     0x140c558: ldur            x1, [x0, #-1]
    //     0x140c55c: ubfx            x1, x1, #0xc, #0x14
    // 0x140c560: r16 = "video"
    //     0x140c560: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x140c564: ldr             x16, [x16, #0xb50]
    // 0x140c568: stp             x16, x0, [SP]
    // 0x140c56c: mov             x0, x1
    // 0x140c570: mov             lr, x0
    // 0x140c574: ldr             lr, [x21, lr, lsl #3]
    // 0x140c578: blr             lr
    // 0x140c57c: tbnz            w0, #4, #0x140c5d4
    // 0x140c580: ldur            x2, [fp, #-0x20]
    // 0x140c584: LoadField: r0 = r2->field_13
    //     0x140c584: ldur            w0, [x2, #0x13]
    // 0x140c588: DecompressPointer r0
    //     0x140c588: add             x0, x0, HEAP, lsl #32
    // 0x140c58c: LoadField: r1 = r0->field_7
    //     0x140c58c: ldur            w1, [x0, #7]
    // 0x140c590: DecompressPointer r1
    //     0x140c590: add             x1, x1, HEAP, lsl #32
    // 0x140c594: r0 = thumbnailData()
    //     0x140c594: bl              #0x9a6f90  ; [package:get_thumbnail_video/video_thumbnail.dart] VideoThumbnail::thumbnailData
    // 0x140c598: ldur            x2, [fp, #-0x20]
    // 0x140c59c: r1 = Function '<anonymous closure>':.
    //     0x140c59c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ba0] AnonymousClosure: (0x140c694), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getThumbNailMedia (0x140c480)
    //     0x140c5a0: ldr             x1, [x1, #0xba0]
    // 0x140c5a4: stur            x0, [fp, #-0x18]
    // 0x140c5a8: r0 = AllocateClosure()
    //     0x140c5a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140c5ac: r16 = <Null?>
    //     0x140c5ac: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x140c5b0: ldur            lr, [fp, #-0x18]
    // 0x140c5b4: stp             lr, x16, [SP, #8]
    // 0x140c5b8: str             x0, [SP]
    // 0x140c5bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x140c5bc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x140c5c0: r0 = then()
    //     0x140c5c0: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x140c5c4: mov             x1, x0
    // 0x140c5c8: stur            x1, [fp, #-0x18]
    // 0x140c5cc: r0 = Await()
    //     0x140c5cc: bl              #0x63248c  ; AwaitStub
    // 0x140c5d0: b               #0x140c5e8
    // 0x140c5d4: ldur            x0, [fp, #-0x20]
    // 0x140c5d8: LoadField: r2 = r0->field_13
    //     0x140c5d8: ldur            w2, [x0, #0x13]
    // 0x140c5dc: DecompressPointer r2
    //     0x140c5dc: add             x2, x2, HEAP, lsl #32
    // 0x140c5e0: ldur            x1, [fp, #-0x10]
    // 0x140c5e4: r0 = addMediaFile()
    //     0x140c5e4: bl              #0x140c5fc  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::addMediaFile
    // 0x140c5e8: r0 = Null
    //     0x140c5e8: mov             x0, NULL
    // 0x140c5ec: r0 = ReturnAsyncNotFuture()
    //     0x140c5ec: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x140c5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140c5f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140c5f4: b               #0x140c4a4
    // 0x140c5f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x140c5f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ addMediaFile(/* No info */) async {
    // ** addr: 0x140c5fc, size: 0x98
    // 0x140c5fc: EnterFrame
    //     0x140c5fc: stp             fp, lr, [SP, #-0x10]!
    //     0x140c600: mov             fp, SP
    // 0x140c604: AllocStack(0x38)
    //     0x140c604: sub             SP, SP, #0x38
    // 0x140c608: SetupParameters(RatingReviewOrderController this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x140c608: stur            NULL, [fp, #-8]
    //     0x140c60c: stur            x1, [fp, #-0x10]
    //     0x140c610: mov             x16, x2
    //     0x140c614: mov             x2, x1
    //     0x140c618: mov             x1, x16
    //     0x140c61c: stur            x1, [fp, #-0x18]
    // 0x140c620: CheckStackOverflow
    //     0x140c620: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140c624: cmp             SP, x16
    //     0x140c628: b.ls            #0x140c68c
    // 0x140c62c: InitAsync() -> Future
    //     0x140c62c: mov             x0, NULL
    //     0x140c630: bl              #0x6326e0  ; InitAsyncStub
    // 0x140c634: ldur            x1, [fp, #-0x18]
    // 0x140c638: r0 = readAsBytesSync()
    //     0x140c638: bl              #0x9a6818  ; [dart:io] _File::readAsBytesSync
    // 0x140c63c: mov             x1, x0
    // 0x140c640: ldur            x0, [fp, #-0x10]
    // 0x140c644: stur            x1, [fp, #-0x28]
    // 0x140c648: LoadField: r2 = r0->field_97
    //     0x140c648: ldur            w2, [x0, #0x97]
    // 0x140c64c: DecompressPointer r2
    //     0x140c64c: add             x2, x2, HEAP, lsl #32
    // 0x140c650: stur            x2, [fp, #-0x20]
    // 0x140c654: r0 = Media()
    //     0x140c654: bl              #0x8ad358  ; AllocateMediaStub -> Media (size=0x20)
    // 0x140c658: mov             x1, x0
    // 0x140c65c: r0 = "image_local"
    //     0x140c65c: add             x0, PP, #0x36, lsl #12  ; [pp+0x369b8] "image_local"
    //     0x140c660: ldr             x0, [x0, #0x9b8]
    // 0x140c664: StoreField: r1->field_b = r0
    //     0x140c664: stur            w0, [x1, #0xb]
    // 0x140c668: ldur            x0, [fp, #-0x28]
    // 0x140c66c: StoreField: r1->field_13 = r0
    //     0x140c66c: stur            w0, [x1, #0x13]
    // 0x140c670: ldur            x0, [fp, #-0x18]
    // 0x140c674: ArrayStore: r1[0] = r0  ; List_4
    //     0x140c674: stur            w0, [x1, #0x17]
    // 0x140c678: ldur            x16, [fp, #-0x20]
    // 0x140c67c: stp             x1, x16, [SP]
    // 0x140c680: r0 = add()
    //     0x140c680: bl              #0x1654150  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0x140c684: r0 = Null
    //     0x140c684: mov             x0, NULL
    // 0x140c688: r0 = ReturnAsyncNotFuture()
    //     0x140c688: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x140c68c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140c68c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140c690: b               #0x140c62c
  }
  [closure] Future<Null> <anonymous closure>(dynamic, Uint8List) async {
    // ** addr: 0x140c694, size: 0xac
    // 0x140c694: EnterFrame
    //     0x140c694: stp             fp, lr, [SP, #-0x10]!
    //     0x140c698: mov             fp, SP
    // 0x140c69c: AllocStack(0x38)
    //     0x140c69c: sub             SP, SP, #0x38
    // 0x140c6a0: SetupParameters(RatingReviewOrderController this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x140c6a0: stur            NULL, [fp, #-8]
    //     0x140c6a4: movz            x0, #0
    //     0x140c6a8: add             x1, fp, w0, sxtw #2
    //     0x140c6ac: ldr             x1, [x1, #0x18]
    //     0x140c6b0: add             x2, fp, w0, sxtw #2
    //     0x140c6b4: ldr             x2, [x2, #0x10]
    //     0x140c6b8: stur            x2, [fp, #-0x18]
    //     0x140c6bc: ldur            w3, [x1, #0x17]
    //     0x140c6c0: add             x3, x3, HEAP, lsl #32
    //     0x140c6c4: stur            x3, [fp, #-0x10]
    // 0x140c6c8: CheckStackOverflow
    //     0x140c6c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140c6cc: cmp             SP, x16
    //     0x140c6d0: b.ls            #0x140c738
    // 0x140c6d4: InitAsync() -> Future<Null?>
    //     0x140c6d4: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x140c6d8: bl              #0x6326e0  ; InitAsyncStub
    // 0x140c6dc: ldur            x0, [fp, #-0x10]
    // 0x140c6e0: LoadField: r1 = r0->field_f
    //     0x140c6e0: ldur            w1, [x0, #0xf]
    // 0x140c6e4: DecompressPointer r1
    //     0x140c6e4: add             x1, x1, HEAP, lsl #32
    // 0x140c6e8: LoadField: r2 = r1->field_97
    //     0x140c6e8: ldur            w2, [x1, #0x97]
    // 0x140c6ec: DecompressPointer r2
    //     0x140c6ec: add             x2, x2, HEAP, lsl #32
    // 0x140c6f0: stur            x2, [fp, #-0x28]
    // 0x140c6f4: LoadField: r1 = r0->field_13
    //     0x140c6f4: ldur            w1, [x0, #0x13]
    // 0x140c6f8: DecompressPointer r1
    //     0x140c6f8: add             x1, x1, HEAP, lsl #32
    // 0x140c6fc: stur            x1, [fp, #-0x20]
    // 0x140c700: r0 = Media()
    //     0x140c700: bl              #0x8ad358  ; AllocateMediaStub -> Media (size=0x20)
    // 0x140c704: mov             x1, x0
    // 0x140c708: r0 = "video_local"
    //     0x140c708: add             x0, PP, #0x36, lsl #12  ; [pp+0x369c0] "video_local"
    //     0x140c70c: ldr             x0, [x0, #0x9c0]
    // 0x140c710: StoreField: r1->field_b = r0
    //     0x140c710: stur            w0, [x1, #0xb]
    // 0x140c714: ldur            x0, [fp, #-0x18]
    // 0x140c718: StoreField: r1->field_13 = r0
    //     0x140c718: stur            w0, [x1, #0x13]
    // 0x140c71c: ldur            x0, [fp, #-0x20]
    // 0x140c720: ArrayStore: r1[0] = r0  ; List_4
    //     0x140c720: stur            w0, [x1, #0x17]
    // 0x140c724: ldur            x16, [fp, #-0x28]
    // 0x140c728: stp             x1, x16, [SP]
    // 0x140c72c: r0 = add()
    //     0x140c72c: bl              #0x1654150  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::add
    // 0x140c730: r0 = Null
    //     0x140c730: mov             x0, NULL
    // 0x140c734: r0 = ReturnAsyncNotFuture()
    //     0x140c734: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x140c738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140c738: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140c73c: b               #0x140c6d4
  }
  _ validateRemark(/* No info */) {
    // ** addr: 0x140e6d8, size: 0xa8
    // 0x140e6d8: EnterFrame
    //     0x140e6d8: stp             fp, lr, [SP, #-0x10]!
    //     0x140e6dc: mov             fp, SP
    // 0x140e6e0: AllocStack(0x30)
    //     0x140e6e0: sub             SP, SP, #0x30
    // 0x140e6e4: SetupParameters(RatingReviewOrderController this /* r1 => r0, fp-0x8 */)
    //     0x140e6e4: mov             x0, x1
    //     0x140e6e8: stur            x1, [fp, #-8]
    // 0x140e6ec: CheckStackOverflow
    //     0x140e6ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e6f0: cmp             SP, x16
    //     0x140e6f4: b.ls            #0x140e778
    // 0x140e6f8: cmp             w2, NULL
    // 0x140e6fc: b.eq            #0x140e754
    // 0x140e700: LoadField: r1 = r2->field_7
    //     0x140e700: ldur            w1, [x2, #7]
    // 0x140e704: r2 = LoadInt32Instr(r1)
    //     0x140e704: sbfx            x2, x1, #1, #0x1f
    // 0x140e708: cmp             x2, #0x7d0
    // 0x140e70c: b.le            #0x140e754
    // 0x140e710: r16 = Instance_Toast
    //     0x140e710: add             x16, PP, #8, lsl #12  ; [pp+0x8170] Obj!Toast@d710c1
    //     0x140e714: ldr             x16, [x16, #0x170]
    // 0x140e718: r30 = Instance_ToastGravity
    //     0x140e718: add             lr, PP, #8, lsl #12  ; [pp+0x8178] Obj!ToastGravity@d71081
    //     0x140e71c: ldr             lr, [lr, #0x178]
    // 0x140e720: stp             lr, x16, [SP, #0x18]
    // 0x140e724: r16 = Instance_MaterialColor
    //     0x140e724: add             x16, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0x140e728: ldr             x16, [x16, #0x180]
    // 0x140e72c: r30 = Instance_Color
    //     0x140e72c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x140e730: stp             lr, x16, [SP, #8]
    // 0x140e734: r16 = 16.000000
    //     0x140e734: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x140e738: ldr             x16, [x16, #0x188]
    // 0x140e73c: str             x16, [SP]
    // 0x140e740: r1 = "Maximum Limit Reached"
    //     0x140e740: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b08] "Maximum Limit Reached"
    //     0x140e744: ldr             x1, [x1, #0xb08]
    // 0x140e748: r4 = const [0, 0x6, 0x5, 0x1, backgroundColor, 0x3, fontSize, 0x5, gravity, 0x2, textColor, 0x4, toastLength, 0x1, null]
    //     0x140e748: add             x4, PP, #8, lsl #12  ; [pp+0x8190] List(15) [0, 0x6, 0x5, 0x1, "backgroundColor", 0x3, "fontSize", 0x5, "gravity", 0x2, "textColor", 0x4, "toastLength", 0x1, Null]
    //     0x140e74c: ldr             x4, [x4, #0x190]
    // 0x140e750: r0 = showToast()
    //     0x140e750: bl              #0x89cd74  ; [package:fluttertoast/fluttertoast.dart] Fluttertoast::showToast
    // 0x140e754: ldur            x0, [fp, #-8]
    // 0x140e758: LoadField: r1 = r0->field_63
    //     0x140e758: ldur            w1, [x0, #0x63]
    // 0x140e75c: DecompressPointer r1
    //     0x140e75c: add             x1, x1, HEAP, lsl #32
    // 0x140e760: r2 = true
    //     0x140e760: add             x2, NULL, #0x20  ; true
    // 0x140e764: r0 = value=()
    //     0x140e764: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140e768: r0 = Null
    //     0x140e768: mov             x0, NULL
    // 0x140e76c: LeaveFrame
    //     0x140e76c: mov             SP, fp
    //     0x140e770: ldp             fp, lr, [SP], #0x10
    // 0x140e774: ret
    //     0x140e774: ret             
    // 0x140e778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e778: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e77c: b               #0x140e6f8
  }
  [closure] String? validateRemark(dynamic, String?) {
    // ** addr: 0x140e780, size: 0x3c
    // 0x140e780: EnterFrame
    //     0x140e780: stp             fp, lr, [SP, #-0x10]!
    //     0x140e784: mov             fp, SP
    // 0x140e788: ldr             x0, [fp, #0x18]
    // 0x140e78c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140e78c: ldur            w1, [x0, #0x17]
    // 0x140e790: DecompressPointer r1
    //     0x140e790: add             x1, x1, HEAP, lsl #32
    // 0x140e794: CheckStackOverflow
    //     0x140e794: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e798: cmp             SP, x16
    //     0x140e79c: b.ls            #0x140e7b4
    // 0x140e7a0: ldr             x2, [fp, #0x10]
    // 0x140e7a4: r0 = validateRemark()
    //     0x140e7a4: bl              #0x140e6d8  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::validateRemark
    // 0x140e7a8: LeaveFrame
    //     0x140e7a8: mov             SP, fp
    //     0x140e7ac: ldp             fp, lr, [SP], #0x10
    // 0x140e7b0: ret
    //     0x140e7b0: ret             
    // 0x140e7b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e7b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e7b8: b               #0x140e7a0
  }
  _ onInit(/* No info */) {
    // ** addr: 0x15a715c, size: 0x294
    // 0x15a715c: EnterFrame
    //     0x15a715c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a7160: mov             fp, SP
    // 0x15a7164: AllocStack(0x28)
    //     0x15a7164: sub             SP, SP, #0x28
    // 0x15a7168: SetupParameters(RatingReviewOrderController this /* r1 => r0, fp-0x8 */)
    //     0x15a7168: mov             x0, x1
    //     0x15a716c: stur            x1, [fp, #-8]
    // 0x15a7170: CheckStackOverflow
    //     0x15a7170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a7174: cmp             SP, x16
    //     0x15a7178: b.ls            #0x15a73e8
    // 0x15a717c: mov             x1, x0
    // 0x15a7180: r0 = _createDir()
    //     0x15a7180: bl              #0x15a917c  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_createDir
    // 0x15a7184: ldur            x1, [fp, #-8]
    // 0x15a7188: r0 = _bindBackgroundIsolate()
    //     0x15a7188: bl              #0x15a8994  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_bindBackgroundIsolate
    // 0x15a718c: ldur            x2, [fp, #-8]
    // 0x15a7190: r0 = false
    //     0x15a7190: add             x0, NULL, #0x30  ; false
    // 0x15a7194: StoreField: r2->field_83 = r0
    //     0x15a7194: stur            w0, [x2, #0x83]
    // 0x15a7198: mov             x1, x2
    // 0x15a719c: r0 = prepare()
    //     0x15a719c: bl              #0x15a75ac  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::prepare
    // 0x15a71a0: ldur            x1, [fp, #-8]
    // 0x15a71a4: r0 = showSearchOrBag()
    //     0x15a71a4: bl              #0x15a74dc  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::showSearchOrBag
    // 0x15a71a8: ldur            x1, [fp, #-8]
    // 0x15a71ac: r0 = getConfigData()
    //     0x15a71ac: bl              #0x15a73f0  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getConfigData
    // 0x15a71b0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15a71b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15a71b4: ldr             x0, [x0, #0x1c80]
    //     0x15a71b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15a71bc: cmp             w0, w16
    //     0x15a71c0: b.ne            #0x15a71cc
    //     0x15a71c4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15a71c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15a71cc: r0 = GetNavigation.arguments()
    //     0x15a71cc: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x15a71d0: stur            x0, [fp, #-0x10]
    // 0x15a71d4: cmp             w0, NULL
    // 0x15a71d8: b.eq            #0x15a73bc
    // 0x15a71dc: r16 = "order_id"
    //     0x15a71dc: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x15a71e0: ldr             x16, [x16, #0xa38]
    // 0x15a71e4: stp             x16, x0, [SP]
    // 0x15a71e8: r4 = 0
    //     0x15a71e8: movz            x4, #0
    // 0x15a71ec: ldr             x0, [SP, #8]
    // 0x15a71f0: r16 = UnlinkedCall_0x613b5c
    //     0x15a71f0: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5cf00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a71f4: add             x16, x16, #0xf00
    // 0x15a71f8: ldp             x5, lr, [x16]
    // 0x15a71fc: blr             lr
    // 0x15a7200: cmp             w0, NULL
    // 0x15a7204: b.ne            #0x15a7210
    // 0x15a7208: r4 = ""
    //     0x15a7208: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a720c: b               #0x15a7214
    // 0x15a7210: mov             x4, x0
    // 0x15a7214: ldur            x3, [fp, #-8]
    // 0x15a7218: mov             x0, x4
    // 0x15a721c: stur            x4, [fp, #-0x18]
    // 0x15a7220: r2 = Null
    //     0x15a7220: mov             x2, NULL
    // 0x15a7224: r1 = Null
    //     0x15a7224: mov             x1, NULL
    // 0x15a7228: r4 = 60
    //     0x15a7228: movz            x4, #0x3c
    // 0x15a722c: branchIfSmi(r0, 0x15a7238)
    //     0x15a722c: tbz             w0, #0, #0x15a7238
    // 0x15a7230: r4 = LoadClassIdInstr(r0)
    //     0x15a7230: ldur            x4, [x0, #-1]
    //     0x15a7234: ubfx            x4, x4, #0xc, #0x14
    // 0x15a7238: sub             x4, x4, #0x5e
    // 0x15a723c: cmp             x4, #1
    // 0x15a7240: b.ls            #0x15a7254
    // 0x15a7244: r8 = String
    //     0x15a7244: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15a7248: r3 = Null
    //     0x15a7248: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf10] Null
    //     0x15a724c: ldr             x3, [x3, #0xf10]
    // 0x15a7250: r0 = String()
    //     0x15a7250: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15a7254: ldur            x0, [fp, #-0x18]
    // 0x15a7258: ldur            x1, [fp, #-8]
    // 0x15a725c: StoreField: r1->field_bb = r0
    //     0x15a725c: stur            w0, [x1, #0xbb]
    //     0x15a7260: ldurb           w16, [x1, #-1]
    //     0x15a7264: ldurb           w17, [x0, #-1]
    //     0x15a7268: and             x16, x17, x16, lsr #2
    //     0x15a726c: tst             x16, HEAP, lsr #32
    //     0x15a7270: b.eq            #0x15a7278
    //     0x15a7274: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a7278: ldur            x16, [fp, #-0x10]
    // 0x15a727c: r30 = "review_id"
    //     0x15a727c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f2c0] "review_id"
    //     0x15a7280: ldr             lr, [lr, #0x2c0]
    // 0x15a7284: stp             lr, x16, [SP]
    // 0x15a7288: r4 = 0
    //     0x15a7288: movz            x4, #0
    // 0x15a728c: ldr             x0, [SP, #8]
    // 0x15a7290: r16 = UnlinkedCall_0x613b5c
    //     0x15a7290: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5cf20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a7294: add             x16, x16, #0xf20
    // 0x15a7298: ldp             x5, lr, [x16]
    // 0x15a729c: blr             lr
    // 0x15a72a0: cmp             w0, NULL
    // 0x15a72a4: b.ne            #0x15a72b0
    // 0x15a72a8: r4 = ""
    //     0x15a72a8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a72ac: b               #0x15a72b4
    // 0x15a72b0: mov             x4, x0
    // 0x15a72b4: ldur            x3, [fp, #-8]
    // 0x15a72b8: mov             x0, x4
    // 0x15a72bc: stur            x4, [fp, #-0x18]
    // 0x15a72c0: r2 = Null
    //     0x15a72c0: mov             x2, NULL
    // 0x15a72c4: r1 = Null
    //     0x15a72c4: mov             x1, NULL
    // 0x15a72c8: r4 = 60
    //     0x15a72c8: movz            x4, #0x3c
    // 0x15a72cc: branchIfSmi(r0, 0x15a72d8)
    //     0x15a72cc: tbz             w0, #0, #0x15a72d8
    // 0x15a72d0: r4 = LoadClassIdInstr(r0)
    //     0x15a72d0: ldur            x4, [x0, #-1]
    //     0x15a72d4: ubfx            x4, x4, #0xc, #0x14
    // 0x15a72d8: sub             x4, x4, #0x5e
    // 0x15a72dc: cmp             x4, #1
    // 0x15a72e0: b.ls            #0x15a72f4
    // 0x15a72e4: r8 = String
    //     0x15a72e4: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15a72e8: r3 = Null
    //     0x15a72e8: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf30] Null
    //     0x15a72ec: ldr             x3, [x3, #0xf30]
    // 0x15a72f0: r0 = String()
    //     0x15a72f0: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15a72f4: ldur            x0, [fp, #-0x18]
    // 0x15a72f8: ldur            x1, [fp, #-8]
    // 0x15a72fc: StoreField: r1->field_bf = r0
    //     0x15a72fc: stur            w0, [x1, #0xbf]
    //     0x15a7300: ldurb           w16, [x1, #-1]
    //     0x15a7304: ldurb           w17, [x0, #-1]
    //     0x15a7308: and             x16, x17, x16, lsr #2
    //     0x15a730c: tst             x16, HEAP, lsr #32
    //     0x15a7310: b.eq            #0x15a7318
    //     0x15a7314: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x15a7318: ldur            x16, [fp, #-0x10]
    // 0x15a731c: r30 = "coming_from"
    //     0x15a731c: add             lr, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x15a7320: ldr             lr, [lr, #0x328]
    // 0x15a7324: stp             lr, x16, [SP]
    // 0x15a7328: r4 = 0
    //     0x15a7328: movz            x4, #0
    // 0x15a732c: ldr             x0, [SP, #8]
    // 0x15a7330: r16 = UnlinkedCall_0x613b5c
    //     0x15a7330: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5cf40] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x15a7334: add             x16, x16, #0xf40
    // 0x15a7338: ldp             x5, lr, [x16]
    // 0x15a733c: blr             lr
    // 0x15a7340: cmp             w0, NULL
    // 0x15a7344: b.ne            #0x15a7350
    // 0x15a7348: r4 = ""
    //     0x15a7348: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15a734c: b               #0x15a7354
    // 0x15a7350: mov             x4, x0
    // 0x15a7354: ldur            x3, [fp, #-8]
    // 0x15a7358: mov             x0, x4
    // 0x15a735c: stur            x4, [fp, #-0x10]
    // 0x15a7360: r2 = Null
    //     0x15a7360: mov             x2, NULL
    // 0x15a7364: r1 = Null
    //     0x15a7364: mov             x1, NULL
    // 0x15a7368: r4 = 60
    //     0x15a7368: movz            x4, #0x3c
    // 0x15a736c: branchIfSmi(r0, 0x15a7378)
    //     0x15a736c: tbz             w0, #0, #0x15a7378
    // 0x15a7370: r4 = LoadClassIdInstr(r0)
    //     0x15a7370: ldur            x4, [x0, #-1]
    //     0x15a7374: ubfx            x4, x4, #0xc, #0x14
    // 0x15a7378: sub             x4, x4, #0x5e
    // 0x15a737c: cmp             x4, #1
    // 0x15a7380: b.ls            #0x15a7394
    // 0x15a7384: r8 = String?
    //     0x15a7384: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x15a7388: r3 = Null
    //     0x15a7388: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf50] Null
    //     0x15a738c: ldr             x3, [x3, #0xf50]
    // 0x15a7390: r0 = String?()
    //     0x15a7390: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x15a7394: ldur            x0, [fp, #-0x10]
    // 0x15a7398: ldur            x2, [fp, #-8]
    // 0x15a739c: StoreField: r2->field_d7 = r0
    //     0x15a739c: stur            w0, [x2, #0xd7]
    //     0x15a73a0: ldurb           w16, [x2, #-1]
    //     0x15a73a4: ldurb           w17, [x0, #-1]
    //     0x15a73a8: and             x16, x17, x16, lsr #2
    //     0x15a73ac: tst             x16, HEAP, lsr #32
    //     0x15a73b0: b.eq            #0x15a73b8
    //     0x15a73b4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15a73b8: b               #0x15a73c0
    // 0x15a73bc: ldur            x2, [fp, #-8]
    // 0x15a73c0: mov             x1, x2
    // 0x15a73c4: r0 = getRatingReviewOrderData()
    //     0x15a73c4: bl              #0x131fbf0  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::getRatingReviewOrderData
    // 0x15a73c8: ldur            x1, [fp, #-8]
    // 0x15a73cc: r0 = true
    //     0x15a73cc: add             x0, NULL, #0x20  ; true
    // 0x15a73d0: StoreField: r1->field_cb = r0
    //     0x15a73d0: stur            w0, [x1, #0xcb]
    // 0x15a73d4: r0 = onInit()
    //     0x15a73d4: bl              #0x158ae60  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::onInit
    // 0x15a73d8: r0 = Null
    //     0x15a73d8: mov             x0, NULL
    // 0x15a73dc: LeaveFrame
    //     0x15a73dc: mov             SP, fp
    //     0x15a73e0: ldp             fp, lr, [SP], #0x10
    // 0x15a73e4: ret
    //     0x15a73e4: ret             
    // 0x15a73e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a73e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a73ec: b               #0x15a717c
  }
  _ getConfigData(/* No info */) async {
    // ** addr: 0x15a73f0, size: 0x60
    // 0x15a73f0: EnterFrame
    //     0x15a73f0: stp             fp, lr, [SP, #-0x10]!
    //     0x15a73f4: mov             fp, SP
    // 0x15a73f8: AllocStack(0x18)
    //     0x15a73f8: sub             SP, SP, #0x18
    // 0x15a73fc: SetupParameters(RatingReviewOrderController this /* r1 => r1, fp-0x10 */)
    //     0x15a73fc: stur            NULL, [fp, #-8]
    //     0x15a7400: stur            x1, [fp, #-0x10]
    // 0x15a7404: CheckStackOverflow
    //     0x15a7404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a7408: cmp             SP, x16
    //     0x15a740c: b.ls            #0x15a7448
    // 0x15a7410: InitAsync() -> Future<void?>
    //     0x15a7410: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x15a7414: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a7418: ldur            x0, [fp, #-0x10]
    // 0x15a741c: LoadField: r1 = r0->field_57
    //     0x15a741c: ldur            w1, [x0, #0x57]
    // 0x15a7420: DecompressPointer r1
    //     0x15a7420: add             x1, x1, HEAP, lsl #32
    // 0x15a7424: r0 = getConfigData()
    //     0x15a7424: bl              #0x8897b0  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getConfigData
    // 0x15a7428: mov             x1, x0
    // 0x15a742c: stur            x1, [fp, #-0x18]
    // 0x15a7430: r0 = Await()
    //     0x15a7430: bl              #0x63248c  ; AwaitStub
    // 0x15a7434: ldur            x1, [fp, #-0x10]
    // 0x15a7438: mov             x2, x0
    // 0x15a743c: r0 = configData=()
    //     0x15a743c: bl              #0x15a7450  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::configData=
    // 0x15a7440: r0 = Null
    //     0x15a7440: mov             x0, NULL
    // 0x15a7444: r0 = ReturnAsyncNotFuture()
    //     0x15a7444: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a7448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a7448: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a744c: b               #0x15a7410
  }
  set _ configData=(/* No info */) {
    // ** addr: 0x15a7450, size: 0x8c
    // 0x15a7450: EnterFrame
    //     0x15a7450: stp             fp, lr, [SP, #-0x10]!
    //     0x15a7454: mov             fp, SP
    // 0x15a7458: AllocStack(0x10)
    //     0x15a7458: sub             SP, SP, #0x10
    // 0x15a745c: SetupParameters(dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x15a745c: mov             x3, x2
    //     0x15a7460: stur            x2, [fp, #-0x10]
    // 0x15a7464: CheckStackOverflow
    //     0x15a7464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a7468: cmp             SP, x16
    //     0x15a746c: b.ls            #0x15a74d4
    // 0x15a7470: LoadField: r4 = r1->field_53
    //     0x15a7470: ldur            w4, [x1, #0x53]
    // 0x15a7474: DecompressPointer r4
    //     0x15a7474: add             x4, x4, HEAP, lsl #32
    // 0x15a7478: mov             x0, x3
    // 0x15a747c: stur            x4, [fp, #-8]
    // 0x15a7480: r2 = Null
    //     0x15a7480: mov             x2, NULL
    // 0x15a7484: r1 = Null
    //     0x15a7484: mov             x1, NULL
    // 0x15a7488: r4 = 60
    //     0x15a7488: movz            x4, #0x3c
    // 0x15a748c: branchIfSmi(r0, 0x15a7498)
    //     0x15a748c: tbz             w0, #0, #0x15a7498
    // 0x15a7490: r4 = LoadClassIdInstr(r0)
    //     0x15a7490: ldur            x4, [x0, #-1]
    //     0x15a7494: ubfx            x4, x4, #0xc, #0x14
    // 0x15a7498: r17 = 5478
    //     0x15a7498: movz            x17, #0x1566
    // 0x15a749c: cmp             x4, x17
    // 0x15a74a0: b.eq            #0x15a74b8
    // 0x15a74a4: r8 = LocalConfigData
    //     0x15a74a4: add             x8, PP, #0x11, lsl #12  ; [pp+0x11e90] Type: LocalConfigData
    //     0x15a74a8: ldr             x8, [x8, #0xe90]
    // 0x15a74ac: r3 = Null
    //     0x15a74ac: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf60] Null
    //     0x15a74b0: ldr             x3, [x3, #0xf60]
    // 0x15a74b4: r0 = LocalConfigData()
    //     0x15a74b4: bl              #0x88ff18  ; IsType_LocalConfigData_Stub
    // 0x15a74b8: ldur            x1, [fp, #-8]
    // 0x15a74bc: ldur            x2, [fp, #-0x10]
    // 0x15a74c0: r0 = value=()
    //     0x15a74c0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a74c4: ldur            x0, [fp, #-0x10]
    // 0x15a74c8: LeaveFrame
    //     0x15a74c8: mov             SP, fp
    //     0x15a74cc: ldp             fp, lr, [SP], #0x10
    // 0x15a74d0: ret
    //     0x15a74d0: ret             
    // 0x15a74d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a74d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a74d8: b               #0x15a7470
  }
  _ showSearchOrBag(/* No info */) {
    // ** addr: 0x15a74dc, size: 0x50
    // 0x15a74dc: EnterFrame
    //     0x15a74dc: stp             fp, lr, [SP, #-0x10]!
    //     0x15a74e0: mov             fp, SP
    // 0x15a74e4: AllocStack(0x8)
    //     0x15a74e4: sub             SP, SP, #8
    // 0x15a74e8: SetupParameters(RatingReviewOrderController this /* r1 => r0, fp-0x8 */)
    //     0x15a74e8: mov             x0, x1
    //     0x15a74ec: stur            x1, [fp, #-8]
    // 0x15a74f0: CheckStackOverflow
    //     0x15a74f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a74f4: cmp             SP, x16
    //     0x15a74f8: b.ls            #0x15a7524
    // 0x15a74fc: mov             x1, x0
    // 0x15a7500: r2 = true
    //     0x15a7500: add             x2, NULL, #0x20  ; true
    // 0x15a7504: r0 = isShowSearch=()
    //     0x15a7504: bl              #0x15a756c  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::isShowSearch=
    // 0x15a7508: ldur            x1, [fp, #-8]
    // 0x15a750c: r2 = true
    //     0x15a750c: add             x2, NULL, #0x20  ; true
    // 0x15a7510: r0 = isShowBag=()
    //     0x15a7510: bl              #0x15a752c  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::isShowBag=
    // 0x15a7514: r0 = Null
    //     0x15a7514: mov             x0, NULL
    // 0x15a7518: LeaveFrame
    //     0x15a7518: mov             SP, fp
    //     0x15a751c: ldp             fp, lr, [SP], #0x10
    // 0x15a7520: ret
    //     0x15a7520: ret             
    // 0x15a7524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a7524: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a7528: b               #0x15a74fc
  }
  set _ isShowBag=(/* No info */) {
    // ** addr: 0x15a752c, size: 0x40
    // 0x15a752c: EnterFrame
    //     0x15a752c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a7530: mov             fp, SP
    // 0x15a7534: CheckStackOverflow
    //     0x15a7534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a7538: cmp             SP, x16
    //     0x15a753c: b.ls            #0x15a7564
    // 0x15a7540: LoadField: r0 = r1->field_5f
    //     0x15a7540: ldur            w0, [x1, #0x5f]
    // 0x15a7544: DecompressPointer r0
    //     0x15a7544: add             x0, x0, HEAP, lsl #32
    // 0x15a7548: mov             x1, x0
    // 0x15a754c: r2 = true
    //     0x15a754c: add             x2, NULL, #0x20  ; true
    // 0x15a7550: r0 = value=()
    //     0x15a7550: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a7554: r0 = true
    //     0x15a7554: add             x0, NULL, #0x20  ; true
    // 0x15a7558: LeaveFrame
    //     0x15a7558: mov             SP, fp
    //     0x15a755c: ldp             fp, lr, [SP], #0x10
    // 0x15a7560: ret
    //     0x15a7560: ret             
    // 0x15a7564: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a7564: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a7568: b               #0x15a7540
  }
  set _ isShowSearch=(/* No info */) {
    // ** addr: 0x15a756c, size: 0x40
    // 0x15a756c: EnterFrame
    //     0x15a756c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a7570: mov             fp, SP
    // 0x15a7574: CheckStackOverflow
    //     0x15a7574: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a7578: cmp             SP, x16
    //     0x15a757c: b.ls            #0x15a75a4
    // 0x15a7580: LoadField: r0 = r1->field_5b
    //     0x15a7580: ldur            w0, [x1, #0x5b]
    // 0x15a7584: DecompressPointer r0
    //     0x15a7584: add             x0, x0, HEAP, lsl #32
    // 0x15a7588: mov             x1, x0
    // 0x15a758c: r2 = true
    //     0x15a758c: add             x2, NULL, #0x20  ; true
    // 0x15a7590: r0 = value=()
    //     0x15a7590: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15a7594: r0 = true
    //     0x15a7594: add             x0, NULL, #0x20  ; true
    // 0x15a7598: LeaveFrame
    //     0x15a7598: mov             SP, fp
    //     0x15a759c: ldp             fp, lr, [SP], #0x10
    // 0x15a75a0: ret
    //     0x15a75a0: ret             
    // 0x15a75a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a75a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a75a8: b               #0x15a7580
  }
  _ prepare(/* No info */) async {
    // ** addr: 0x15a75ac, size: 0x200
    // 0x15a75ac: EnterFrame
    //     0x15a75ac: stp             fp, lr, [SP, #-0x10]!
    //     0x15a75b0: mov             fp, SP
    // 0x15a75b4: AllocStack(0x38)
    //     0x15a75b4: sub             SP, SP, #0x38
    // 0x15a75b8: SetupParameters(RatingReviewOrderController this /* r1 => r1, fp-0x10 */)
    //     0x15a75b8: stur            NULL, [fp, #-8]
    //     0x15a75bc: stur            x1, [fp, #-0x10]
    // 0x15a75c0: CheckStackOverflow
    //     0x15a75c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a75c4: cmp             SP, x16
    //     0x15a75c8: b.ls            #0x15a7794
    // 0x15a75cc: InitAsync() -> Future<void?>
    //     0x15a75cc: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x15a75d0: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a75d4: r1 = Closure: (String, int, int) => void from Function 'downloadCallback': static.
    //     0x15a75d4: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5cf70] Closure: (String, int, int) => void from Function 'downloadCallback': static. (0x7fa73791d2bc)
    //     0x15a75d8: ldr             x1, [x1, #0xf70]
    // 0x15a75dc: r0 = registerCallback()
    //     0x15a75dc: bl              #0x15a88bc  ; [package:flutter_downloader/src/downloader.dart] FlutterDownloader::registerCallback
    // 0x15a75e0: r0 = loadTasks()
    //     0x15a75e0: bl              #0x15a81e4  ; [package:flutter_downloader/src/downloader.dart] FlutterDownloader::loadTasks
    // 0x15a75e4: mov             x1, x0
    // 0x15a75e8: stur            x1, [fp, #-0x18]
    // 0x15a75ec: r0 = Await()
    //     0x15a75ec: bl              #0x63248c  ; AwaitStub
    // 0x15a75f0: cmp             w0, NULL
    // 0x15a75f4: b.ne            #0x15a7600
    // 0x15a75f8: r0 = Null
    //     0x15a75f8: mov             x0, NULL
    // 0x15a75fc: r0 = ReturnAsyncNotFuture()
    //     0x15a75fc: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a7600: ldur            x2, [fp, #-0x10]
    // 0x15a7604: r1 = LoadClassIdInstr(r0)
    //     0x15a7604: ldur            x1, [x0, #-1]
    //     0x15a7608: ubfx            x1, x1, #0xc, #0x14
    // 0x15a760c: mov             x16, x0
    // 0x15a7610: mov             x0, x1
    // 0x15a7614: mov             x1, x16
    // 0x15a7618: r0 = GDT[cid_x0 + 0xc907]()
    //     0x15a7618: movz            x17, #0xc907
    //     0x15a761c: add             lr, x0, x17
    //     0x15a7620: ldr             lr, [x21, lr, lsl #3]
    //     0x15a7624: blr             lr
    // 0x15a7628: mov             x3, x0
    // 0x15a762c: ldur            x2, [fp, #-0x10]
    // 0x15a7630: stur            x3, [fp, #-0x28]
    // 0x15a7634: LoadField: r4 = r2->field_7f
    //     0x15a7634: ldur            w4, [x2, #0x7f]
    // 0x15a7638: DecompressPointer r4
    //     0x15a7638: add             x4, x4, HEAP, lsl #32
    // 0x15a763c: stur            x4, [fp, #-0x20]
    // 0x15a7640: LoadField: r5 = r4->field_7
    //     0x15a7640: ldur            w5, [x4, #7]
    // 0x15a7644: DecompressPointer r5
    //     0x15a7644: add             x5, x5, HEAP, lsl #32
    // 0x15a7648: stur            x5, [fp, #-0x18]
    // 0x15a764c: CheckStackOverflow
    //     0x15a764c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a7650: cmp             SP, x16
    //     0x15a7654: b.ls            #0x15a779c
    // 0x15a7658: r0 = LoadClassIdInstr(r3)
    //     0x15a7658: ldur            x0, [x3, #-1]
    //     0x15a765c: ubfx            x0, x0, #0xc, #0x14
    // 0x15a7660: mov             x1, x3
    // 0x15a7664: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x15a7664: add             lr, x0, #0x5ea
    //     0x15a7668: ldr             lr, [x21, lr, lsl #3]
    //     0x15a766c: blr             lr
    // 0x15a7670: tbnz            w0, #4, #0x15a7704
    // 0x15a7674: ldur            x2, [fp, #-0x28]
    // 0x15a7678: ldur            x3, [fp, #-0x20]
    // 0x15a767c: r0 = LoadClassIdInstr(r2)
    //     0x15a767c: ldur            x0, [x2, #-1]
    //     0x15a7680: ubfx            x0, x0, #0xc, #0x14
    // 0x15a7684: mov             x1, x2
    // 0x15a7688: r0 = GDT[cid_x0 + 0x655]()
    //     0x15a7688: add             lr, x0, #0x655
    //     0x15a768c: ldr             lr, [x21, lr, lsl #3]
    //     0x15a7690: blr             lr
    // 0x15a7694: ldur            x2, [fp, #-0x20]
    // 0x15a7698: LoadField: r3 = r2->field_f
    //     0x15a7698: ldur            w3, [x2, #0xf]
    // 0x15a769c: DecompressPointer r3
    //     0x15a769c: add             x3, x3, HEAP, lsl #32
    // 0x15a76a0: stur            x3, [fp, #-0x38]
    // 0x15a76a4: LoadField: r0 = r2->field_13
    //     0x15a76a4: ldur            w0, [x2, #0x13]
    // 0x15a76a8: r5 = LoadInt32Instr(r0)
    //     0x15a76a8: sbfx            x5, x0, #1, #0x1f
    // 0x15a76ac: ldur            x1, [fp, #-0x18]
    // 0x15a76b0: stur            x5, [fp, #-0x30]
    // 0x15a76b4: r0 = _CompactIterator()
    //     0x15a76b4: bl              #0x7e23f8  ; Allocate_CompactIteratorStub -> _CompactIterator<X0> (size=0x38)
    // 0x15a76b8: mov             x1, x0
    // 0x15a76bc: ldur            x2, [fp, #-0x20]
    // 0x15a76c0: ldur            x3, [fp, #-0x38]
    // 0x15a76c4: ldur            x5, [fp, #-0x30]
    // 0x15a76c8: r6 = -1
    //     0x15a76c8: movn            x6, #0
    // 0x15a76cc: r7 = 1
    //     0x15a76cc: movz            x7, #0x1
    // 0x15a76d0: stur            x0, [fp, #-0x38]
    // 0x15a76d4: r0 = _CompactIterator()
    //     0x15a76d4: bl              #0x7e234c  ; [dart:_compact_hash] _CompactIterator::_CompactIterator
    // 0x15a76d8: CheckStackOverflow
    //     0x15a76d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a76dc: cmp             SP, x16
    //     0x15a76e0: b.ls            #0x15a77a4
    // 0x15a76e4: ldur            x1, [fp, #-0x38]
    // 0x15a76e8: r0 = moveNext()
    //     0x15a76e8: bl              #0x1626cc8  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x15a76ec: tbz             w0, #4, #0x15a7744
    // 0x15a76f0: ldur            x2, [fp, #-0x10]
    // 0x15a76f4: ldur            x3, [fp, #-0x28]
    // 0x15a76f8: ldur            x4, [fp, #-0x20]
    // 0x15a76fc: ldur            x5, [fp, #-0x18]
    // 0x15a7700: b               #0x15a764c
    // 0x15a7704: ldur            x0, [fp, #-0x10]
    // 0x15a7708: LoadField: r1 = r0->field_87
    //     0x15a7708: ldur            w1, [x0, #0x87]
    // 0x15a770c: DecompressPointer r1
    //     0x15a770c: add             x1, x1, HEAP, lsl #32
    // 0x15a7710: r0 = checkPermission()
    //     0x15a7710: bl              #0x15a7c20  ; [package:customer_app/app/core/utils/download_files_to_locals.dart] DownloadFilesToLocals::checkPermission
    // 0x15a7714: mov             x1, x0
    // 0x15a7718: stur            x1, [fp, #-0x20]
    // 0x15a771c: r0 = Await()
    //     0x15a771c: bl              #0x63248c  ; AwaitStub
    // 0x15a7720: mov             x1, x0
    // 0x15a7724: ldur            x0, [fp, #-0x10]
    // 0x15a7728: StoreField: r0->field_83 = r1
    //     0x15a7728: stur            w1, [x0, #0x83]
    // 0x15a772c: tbz             w1, #4, #0x15a773c
    // 0x15a7730: LoadField: r1 = r0->field_87
    //     0x15a7730: ldur            w1, [x0, #0x87]
    // 0x15a7734: DecompressPointer r1
    //     0x15a7734: add             x1, x1, HEAP, lsl #32
    // 0x15a7738: r0 = requestPermission()
    //     0x15a7738: bl              #0x15a77ac  ; [package:customer_app/app/core/utils/download_files_to_locals.dart] DownloadFilesToLocals::requestPermission
    // 0x15a773c: r0 = Null
    //     0x15a773c: mov             x0, NULL
    // 0x15a7740: r0 = ReturnAsyncNotFuture()
    //     0x15a7740: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a7744: ldur            x0, [fp, #-0x38]
    // 0x15a7748: LoadField: r1 = r0->field_33
    //     0x15a7748: ldur            w1, [x0, #0x33]
    // 0x15a774c: DecompressPointer r1
    //     0x15a774c: add             x1, x1, HEAP, lsl #32
    // 0x15a7750: cmp             w1, NULL
    // 0x15a7754: b.ne            #0x15a7788
    // 0x15a7758: mov             x0, x1
    // 0x15a775c: ldur            x2, [fp, #-0x18]
    // 0x15a7760: r1 = Null
    //     0x15a7760: mov             x1, NULL
    // 0x15a7764: cmp             w2, NULL
    // 0x15a7768: b.eq            #0x15a7788
    // 0x15a776c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x15a776c: ldur            w4, [x2, #0x17]
    // 0x15a7770: DecompressPointer r4
    //     0x15a7770: add             x4, x4, HEAP, lsl #32
    // 0x15a7774: r8 = X0
    //     0x15a7774: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x15a7778: LoadField: r9 = r4->field_7
    //     0x15a7778: ldur            x9, [x4, #7]
    // 0x15a777c: r3 = Null
    //     0x15a777c: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf78] Null
    //     0x15a7780: ldr             x3, [x3, #0xf78]
    // 0x15a7784: blr             x9
    // 0x15a7788: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x15a7788: ldr             x0, [PP, #0x168]  ; [pp+0x168] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x15a778c: r0 = Throw()
    //     0x15a778c: bl              #0x16f5420  ; ThrowStub
    // 0x15a7790: brk             #0
    // 0x15a7794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a7794: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a7798: b               #0x15a75cc
    // 0x15a779c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a779c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a77a0: b               #0x15a7658
    // 0x15a77a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a77a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a77a8: b               #0x15a76e4
  }
  _ _bindBackgroundIsolate(/* No info */) {
    // ** addr: 0x15a8994, size: 0xb0
    // 0x15a8994: EnterFrame
    //     0x15a8994: stp             fp, lr, [SP, #-0x10]!
    //     0x15a8998: mov             fp, SP
    // 0x15a899c: AllocStack(0x18)
    //     0x15a899c: sub             SP, SP, #0x18
    // 0x15a89a0: SetupParameters(RatingReviewOrderController this /* r1 => r1, fp-0x8 */)
    //     0x15a89a0: stur            x1, [fp, #-8]
    // 0x15a89a4: CheckStackOverflow
    //     0x15a89a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a89a8: cmp             SP, x16
    //     0x15a89ac: b.ls            #0x15a8a3c
    // 0x15a89b0: r1 = 1
    //     0x15a89b0: movz            x1, #0x1
    // 0x15a89b4: r0 = AllocateContext()
    //     0x15a89b4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15a89b8: mov             x2, x0
    // 0x15a89bc: ldur            x0, [fp, #-8]
    // 0x15a89c0: stur            x2, [fp, #-0x18]
    // 0x15a89c4: StoreField: r2->field_f = r0
    //     0x15a89c4: stur            w0, [x2, #0xf]
    // 0x15a89c8: LoadField: r3 = r0->field_7b
    //     0x15a89c8: ldur            w3, [x0, #0x7b]
    // 0x15a89cc: DecompressPointer r3
    //     0x15a89cc: add             x3, x3, HEAP, lsl #32
    // 0x15a89d0: stur            x3, [fp, #-0x10]
    // 0x15a89d4: LoadField: r1 = r3->field_b
    //     0x15a89d4: ldur            w1, [x3, #0xb]
    // 0x15a89d8: DecompressPointer r1
    //     0x15a89d8: add             x1, x1, HEAP, lsl #32
    // 0x15a89dc: LoadField: r4 = r1->field_7
    //     0x15a89dc: ldur            w4, [x1, #7]
    // 0x15a89e0: DecompressPointer r4
    //     0x15a89e0: add             x4, x4, HEAP, lsl #32
    // 0x15a89e4: mov             x1, x4
    // 0x15a89e8: r0 = registerPortWithName()
    //     0x15a89e8: bl              #0x15a8c84  ; [dart:ui] IsolateNameServer::registerPortWithName
    // 0x15a89ec: tbz             w0, #4, #0x15a8a0c
    // 0x15a89f0: r0 = removePortNameMapping()
    //     0x15a89f0: bl              #0x15a8a44  ; [dart:ui] IsolateNameServer::removePortNameMapping
    // 0x15a89f4: ldur            x1, [fp, #-8]
    // 0x15a89f8: r0 = _bindBackgroundIsolate()
    //     0x15a89f8: bl              #0x15a8994  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_bindBackgroundIsolate
    // 0x15a89fc: r0 = Null
    //     0x15a89fc: mov             x0, NULL
    // 0x15a8a00: LeaveFrame
    //     0x15a8a00: mov             SP, fp
    //     0x15a8a04: ldp             fp, lr, [SP], #0x10
    // 0x15a8a08: ret
    //     0x15a8a08: ret             
    // 0x15a8a0c: ldur            x2, [fp, #-0x18]
    // 0x15a8a10: r1 = Function '<anonymous closure>':.
    //     0x15a8a10: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5cf88] AnonymousClosure: (0x15a8ecc), in [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::_bindBackgroundIsolate (0x15a8994)
    //     0x15a8a14: ldr             x1, [x1, #0xf88]
    // 0x15a8a18: r0 = AllocateClosure()
    //     0x15a8a18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a8a1c: ldur            x1, [fp, #-0x10]
    // 0x15a8a20: mov             x2, x0
    // 0x15a8a24: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15a8a24: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15a8a28: r0 = listen()
    //     0x15a8a28: bl              #0x163e69c  ; [dart:io] _Socket::listen
    // 0x15a8a2c: r0 = Null
    //     0x15a8a2c: mov             x0, NULL
    // 0x15a8a30: LeaveFrame
    //     0x15a8a30: mov             SP, fp
    //     0x15a8a34: ldp             fp, lr, [SP], #0x10
    // 0x15a8a38: ret
    //     0x15a8a38: ret             
    // 0x15a8a3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a8a3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a8a40: b               #0x15a89b0
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x15a8ecc, size: 0x1bc
    // 0x15a8ecc: EnterFrame
    //     0x15a8ecc: stp             fp, lr, [SP, #-0x10]!
    //     0x15a8ed0: mov             fp, SP
    // 0x15a8ed4: AllocStack(0x28)
    //     0x15a8ed4: sub             SP, SP, #0x28
    // 0x15a8ed8: SetupParameters()
    //     0x15a8ed8: ldr             x0, [fp, #0x18]
    //     0x15a8edc: ldur            w1, [x0, #0x17]
    //     0x15a8ee0: add             x1, x1, HEAP, lsl #32
    //     0x15a8ee4: stur            x1, [fp, #-8]
    // 0x15a8ee8: CheckStackOverflow
    //     0x15a8ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a8eec: cmp             SP, x16
    //     0x15a8ef0: b.ls            #0x15a9080
    // 0x15a8ef4: r1 = 1
    //     0x15a8ef4: movz            x1, #0x1
    // 0x15a8ef8: r0 = AllocateContext()
    //     0x15a8ef8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15a8efc: mov             x4, x0
    // 0x15a8f00: ldur            x3, [fp, #-8]
    // 0x15a8f04: stur            x4, [fp, #-0x10]
    // 0x15a8f08: StoreField: r4->field_b = r3
    //     0x15a8f08: stur            w3, [x4, #0xb]
    // 0x15a8f0c: ldr             x0, [fp, #0x10]
    // 0x15a8f10: r2 = Null
    //     0x15a8f10: mov             x2, NULL
    // 0x15a8f14: r1 = Null
    //     0x15a8f14: mov             x1, NULL
    // 0x15a8f18: r4 = 60
    //     0x15a8f18: movz            x4, #0x3c
    // 0x15a8f1c: branchIfSmi(r0, 0x15a8f28)
    //     0x15a8f1c: tbz             w0, #0, #0x15a8f28
    // 0x15a8f20: r4 = LoadClassIdInstr(r0)
    //     0x15a8f20: ldur            x4, [x0, #-1]
    //     0x15a8f24: ubfx            x4, x4, #0xc, #0x14
    // 0x15a8f28: sub             x4, x4, #0x5a
    // 0x15a8f2c: cmp             x4, #2
    // 0x15a8f30: b.ls            #0x15a8f44
    // 0x15a8f34: r8 = List
    //     0x15a8f34: ldr             x8, [PP, #0x2ce0]  ; [pp+0x2ce0] Type: List
    // 0x15a8f38: r3 = Null
    //     0x15a8f38: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cf90] Null
    //     0x15a8f3c: ldr             x3, [x3, #0xf90]
    // 0x15a8f40: r0 = List()
    //     0x15a8f40: bl              #0x16ff2e4  ; IsType_List_Stub
    // 0x15a8f44: ldr             x1, [fp, #0x10]
    // 0x15a8f48: r0 = LoadClassIdInstr(r1)
    //     0x15a8f48: ldur            x0, [x1, #-1]
    //     0x15a8f4c: ubfx            x0, x0, #0xc, #0x14
    // 0x15a8f50: stp             xzr, x1, [SP]
    // 0x15a8f54: r0 = GDT[cid_x0 + -0xb7]()
    //     0x15a8f54: sub             lr, x0, #0xb7
    //     0x15a8f58: ldr             lr, [x21, lr, lsl #3]
    //     0x15a8f5c: blr             lr
    // 0x15a8f60: mov             x3, x0
    // 0x15a8f64: r2 = Null
    //     0x15a8f64: mov             x2, NULL
    // 0x15a8f68: r1 = Null
    //     0x15a8f68: mov             x1, NULL
    // 0x15a8f6c: stur            x3, [fp, #-0x18]
    // 0x15a8f70: r4 = 60
    //     0x15a8f70: movz            x4, #0x3c
    // 0x15a8f74: branchIfSmi(r0, 0x15a8f80)
    //     0x15a8f74: tbz             w0, #0, #0x15a8f80
    // 0x15a8f78: r4 = LoadClassIdInstr(r0)
    //     0x15a8f78: ldur            x4, [x0, #-1]
    //     0x15a8f7c: ubfx            x4, x4, #0xc, #0x14
    // 0x15a8f80: sub             x4, x4, #0x5e
    // 0x15a8f84: cmp             x4, #1
    // 0x15a8f88: b.ls            #0x15a8f9c
    // 0x15a8f8c: r8 = String
    //     0x15a8f8c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x15a8f90: r3 = Null
    //     0x15a8f90: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cfa0] Null
    //     0x15a8f94: ldr             x3, [x3, #0xfa0]
    // 0x15a8f98: r0 = String()
    //     0x15a8f98: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x15a8f9c: ldur            x0, [fp, #-0x18]
    // 0x15a8fa0: ldur            x2, [fp, #-0x10]
    // 0x15a8fa4: StoreField: r2->field_f = r0
    //     0x15a8fa4: stur            w0, [x2, #0xf]
    //     0x15a8fa8: ldurb           w16, [x2, #-1]
    //     0x15a8fac: ldurb           w17, [x0, #-1]
    //     0x15a8fb0: and             x16, x17, x16, lsr #2
    //     0x15a8fb4: tst             x16, HEAP, lsr #32
    //     0x15a8fb8: b.eq            #0x15a8fc0
    //     0x15a8fbc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x15a8fc0: ldr             x0, [fp, #0x10]
    // 0x15a8fc4: r1 = LoadClassIdInstr(r0)
    //     0x15a8fc4: ldur            x1, [x0, #-1]
    //     0x15a8fc8: ubfx            x1, x1, #0xc, #0x14
    // 0x15a8fcc: r16 = 4
    //     0x15a8fcc: movz            x16, #0x4
    // 0x15a8fd0: stp             x16, x0, [SP]
    // 0x15a8fd4: mov             x0, x1
    // 0x15a8fd8: r0 = GDT[cid_x0 + -0xb7]()
    //     0x15a8fd8: sub             lr, x0, #0xb7
    //     0x15a8fdc: ldr             lr, [x21, lr, lsl #3]
    //     0x15a8fe0: blr             lr
    // 0x15a8fe4: r2 = Null
    //     0x15a8fe4: mov             x2, NULL
    // 0x15a8fe8: r1 = Null
    //     0x15a8fe8: mov             x1, NULL
    // 0x15a8fec: branchIfSmi(r0, 0x15a9014)
    //     0x15a8fec: tbz             w0, #0, #0x15a9014
    // 0x15a8ff0: r4 = LoadClassIdInstr(r0)
    //     0x15a8ff0: ldur            x4, [x0, #-1]
    //     0x15a8ff4: ubfx            x4, x4, #0xc, #0x14
    // 0x15a8ff8: sub             x4, x4, #0x3c
    // 0x15a8ffc: cmp             x4, #1
    // 0x15a9000: b.ls            #0x15a9014
    // 0x15a9004: r8 = int
    //     0x15a9004: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x15a9008: r3 = Null
    //     0x15a9008: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5cfb0] Null
    //     0x15a900c: ldr             x3, [x3, #0xfb0]
    // 0x15a9010: r0 = int()
    //     0x15a9010: bl              #0x16fc548  ; IsType_int_Stub
    // 0x15a9014: ldur            x0, [fp, #-8]
    // 0x15a9018: LoadField: r1 = r0->field_f
    //     0x15a9018: ldur            w1, [x0, #0xf]
    // 0x15a901c: DecompressPointer r1
    //     0x15a901c: add             x1, x1, HEAP, lsl #32
    // 0x15a9020: LoadField: r0 = r1->field_7f
    //     0x15a9020: ldur            w0, [x1, #0x7f]
    // 0x15a9024: DecompressPointer r0
    //     0x15a9024: add             x0, x0, HEAP, lsl #32
    // 0x15a9028: stur            x0, [fp, #-8]
    // 0x15a902c: LoadField: r1 = r0->field_13
    //     0x15a902c: ldur            w1, [x0, #0x13]
    // 0x15a9030: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x15a9030: ldur            w2, [x0, #0x17]
    // 0x15a9034: r3 = LoadInt32Instr(r1)
    //     0x15a9034: sbfx            x3, x1, #1, #0x1f
    // 0x15a9038: r1 = LoadInt32Instr(r2)
    //     0x15a9038: sbfx            x1, x2, #1, #0x1f
    // 0x15a903c: sub             x2, x3, x1
    // 0x15a9040: cbnz            x2, #0x15a9054
    // 0x15a9044: r0 = Null
    //     0x15a9044: mov             x0, NULL
    // 0x15a9048: LeaveFrame
    //     0x15a9048: mov             SP, fp
    //     0x15a904c: ldp             fp, lr, [SP], #0x10
    // 0x15a9050: ret
    //     0x15a9050: ret             
    // 0x15a9054: ldur            x2, [fp, #-0x10]
    // 0x15a9058: r1 = Function '<anonymous closure>':.
    //     0x15a9058: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5cfc0] AnonymousClosure: (0x1515b2c), in [package:get/get_navigation/src/routes/route_middleware.dart] MiddlewareRunner::_getMiddlewares (0x689e08)
    //     0x15a905c: ldr             x1, [x1, #0xfc0]
    // 0x15a9060: r0 = AllocateClosure()
    //     0x15a9060: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15a9064: ldur            x1, [fp, #-8]
    // 0x15a9068: mov             x2, x0
    // 0x15a906c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x15a906c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x15a9070: r0 = firstWhere()
    //     0x15a9070: bl              #0x15a9088  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin::firstWhere
    // 0x15a9074: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x15a9074: ldr             x0, [PP, #0x168]  ; [pp+0x168] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x15a9078: r0 = Throw()
    //     0x15a9078: bl              #0x16f5420  ; ThrowStub
    // 0x15a907c: brk             #0
    // 0x15a9080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a9080: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a9084: b               #0x15a8ef4
  }
  _ _createDir(/* No info */) async {
    // ** addr: 0x15a917c, size: 0x54
    // 0x15a917c: EnterFrame
    //     0x15a917c: stp             fp, lr, [SP, #-0x10]!
    //     0x15a9180: mov             fp, SP
    // 0x15a9184: AllocStack(0x10)
    //     0x15a9184: sub             SP, SP, #0x10
    // 0x15a9188: SetupParameters(RatingReviewOrderController this /* r1 => r1, fp-0x10 */)
    //     0x15a9188: stur            NULL, [fp, #-8]
    //     0x15a918c: stur            x1, [fp, #-0x10]
    // 0x15a9190: CheckStackOverflow
    //     0x15a9190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15a9194: cmp             SP, x16
    //     0x15a9198: b.ls            #0x15a91c8
    // 0x15a919c: InitAsync() -> Future<void?>
    //     0x15a919c: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x15a91a0: bl              #0x6326e0  ; InitAsyncStub
    // 0x15a91a4: ldur            x0, [fp, #-0x10]
    // 0x15a91a8: LoadField: r1 = r0->field_87
    //     0x15a91a8: ldur            w1, [x0, #0x87]
    // 0x15a91ac: DecompressPointer r1
    //     0x15a91ac: add             x1, x1, HEAP, lsl #32
    // 0x15a91b0: r0 = prepareSaveDir()
    //     0x15a91b0: bl              #0x15a91d0  ; [package:customer_app/app/core/utils/download_files_to_locals.dart] DownloadFilesToLocals::prepareSaveDir
    // 0x15a91b4: mov             x1, x0
    // 0x15a91b8: stur            x1, [fp, #-0x10]
    // 0x15a91bc: r0 = Await()
    //     0x15a91bc: bl              #0x63248c  ; AwaitStub
    // 0x15a91c0: r0 = Null
    //     0x15a91c0: mov             x0, NULL
    // 0x15a91c4: r0 = ReturnAsyncNotFuture()
    //     0x15a91c4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x15a91c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15a91c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15a91cc: b               #0x15a919c
  }
  _ RatingReviewOrderController(/* No info */) {
    // ** addr: 0x1602ae8, size: 0x5b8
    // 0x1602ae8: EnterFrame
    //     0x1602ae8: stp             fp, lr, [SP, #-0x10]!
    //     0x1602aec: mov             fp, SP
    // 0x1602af0: AllocStack(0x28)
    //     0x1602af0: sub             SP, SP, #0x28
    // 0x1602af4: r3 = Sentinel
    //     0x1602af4: ldr             x3, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1602af8: r2 = ""
    //     0x1602af8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1602afc: r0 = false
    //     0x1602afc: add             x0, NULL, #0x30  ; false
    // 0x1602b00: stur            x1, [fp, #-8]
    // 0x1602b04: CheckStackOverflow
    //     0x1602b04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1602b08: cmp             SP, x16
    //     0x1602b0c: b.ls            #0x1603098
    // 0x1602b10: StoreField: r1->field_47 = r3
    //     0x1602b10: stur            w3, [x1, #0x47]
    // 0x1602b14: StoreField: r1->field_67 = rZR
    //     0x1602b14: stur            xzr, [x1, #0x67]
    // 0x1602b18: StoreField: r1->field_6f = r2
    //     0x1602b18: stur            w2, [x1, #0x6f]
    // 0x1602b1c: StoreField: r1->field_83 = r3
    //     0x1602b1c: stur            w3, [x1, #0x83]
    // 0x1602b20: StoreField: r1->field_bb = r2
    //     0x1602b20: stur            w2, [x1, #0xbb]
    // 0x1602b24: StoreField: r1->field_bf = r2
    //     0x1602b24: stur            w2, [x1, #0xbf]
    // 0x1602b28: StoreField: r1->field_c7 = r0
    //     0x1602b28: stur            w0, [x1, #0xc7]
    // 0x1602b2c: StoreField: r1->field_cb = r0
    //     0x1602b2c: stur            w0, [x1, #0xcb]
    // 0x1602b30: StoreField: r1->field_d7 = r2
    //     0x1602b30: stur            w2, [x1, #0xd7]
    // 0x1602b34: r0 = RatingReviewOrderResponse()
    //     0x1602b34: bl              #0x1320564  ; AllocateRatingReviewOrderResponseStub -> RatingReviewOrderResponse (size=0x10)
    // 0x1602b38: r16 = <RatingReviewOrderResponse>
    //     0x1602b38: add             x16, PP, #0x36, lsl #12  ; [pp+0x36730] TypeArguments: <RatingReviewOrderResponse>
    //     0x1602b3c: ldr             x16, [x16, #0x730]
    // 0x1602b40: stp             x0, x16, [SP]
    // 0x1602b44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602b44: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602b48: r0 = RxT.obs()
    //     0x1602b48: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x1602b4c: ldur            x1, [fp, #-8]
    // 0x1602b50: StoreField: r1->field_4b = r0
    //     0x1602b50: stur            w0, [x1, #0x4b]
    //     0x1602b54: ldurb           w16, [x1, #-1]
    //     0x1602b58: ldurb           w17, [x0, #-1]
    //     0x1602b5c: and             x16, x17, x16, lsr #2
    //     0x1602b60: tst             x16, HEAP, lsr #32
    //     0x1602b64: b.eq            #0x1602b6c
    //     0x1602b68: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1602b6c: r0 = CreateReviewResponse()
    //     0x1602b6c: bl              #0x8aca8c  ; AllocateCreateReviewResponseStub -> CreateReviewResponse (size=0x14)
    // 0x1602b70: r16 = <CreateReviewResponse>
    //     0x1602b70: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf50] TypeArguments: <CreateReviewResponse>
    //     0x1602b74: ldr             x16, [x16, #0xf50]
    // 0x1602b78: stp             x0, x16, [SP]
    // 0x1602b7c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602b7c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602b80: r0 = RxT.obs()
    //     0x1602b80: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x1602b84: ldur            x1, [fp, #-8]
    // 0x1602b88: StoreField: r1->field_4f = r0
    //     0x1602b88: stur            w0, [x1, #0x4f]
    //     0x1602b8c: ldurb           w16, [x1, #-1]
    //     0x1602b90: ldurb           w17, [x0, #-1]
    //     0x1602b94: and             x16, x17, x16, lsr #2
    //     0x1602b98: tst             x16, HEAP, lsr #32
    //     0x1602b9c: b.eq            #0x1602ba4
    //     0x1602ba0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1602ba4: r0 = LocalConfigData()
    //     0x1602ba4: bl              #0x8933d8  ; AllocateLocalConfigDataStub -> LocalConfigData (size=0x80)
    // 0x1602ba8: r16 = <LocalConfigData>
    //     0x1602ba8: add             x16, PP, #0xa, lsl #12  ; [pp+0xab30] TypeArguments: <LocalConfigData>
    //     0x1602bac: ldr             x16, [x16, #0xb30]
    // 0x1602bb0: stp             x0, x16, [SP]
    // 0x1602bb4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602bb4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602bb8: r0 = RxT.obs()
    //     0x1602bb8: bl              #0x12c6190  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxT.obs
    // 0x1602bbc: ldur            x1, [fp, #-8]
    // 0x1602bc0: StoreField: r1->field_53 = r0
    //     0x1602bc0: stur            w0, [x1, #0x53]
    //     0x1602bc4: ldurb           w16, [x1, #-1]
    //     0x1602bc8: ldurb           w17, [x0, #-1]
    //     0x1602bcc: and             x16, x17, x16, lsr #2
    //     0x1602bd0: tst             x16, HEAP, lsr #32
    //     0x1602bd4: b.eq            #0x1602bdc
    //     0x1602bd8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1602bdc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1602bdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1602be0: ldr             x0, [x0, #0x1c80]
    //     0x1602be4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1602be8: cmp             w0, w16
    //     0x1602bec: b.ne            #0x1602bf8
    //     0x1602bf0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1602bf4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1602bf8: r16 = ConnectionController
    //     0x1602bf8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaaf8] Type: ConnectionController
    //     0x1602bfc: ldr             x16, [x16, #0xaf8]
    // 0x1602c00: str             x16, [SP]
    // 0x1602c04: r0 = toString()
    //     0x1602c04: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1602c08: r16 = <ConnectionController>
    //     0x1602c08: add             x16, PP, #0xa, lsl #12  ; [pp+0xab00] TypeArguments: <ConnectionController>
    //     0x1602c0c: ldr             x16, [x16, #0xb00]
    // 0x1602c10: stp             x0, x16, [SP]
    // 0x1602c14: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1602c14: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1602c18: r0 = Inst.find()
    //     0x1602c18: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1602c1c: r16 = PreferenceManager
    //     0x1602c1c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x1602c20: ldr             x16, [x16, #0x878]
    // 0x1602c24: str             x16, [SP]
    // 0x1602c28: r0 = toString()
    //     0x1602c28: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1602c2c: r16 = <PreferenceManager>
    //     0x1602c2c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x1602c30: ldr             x16, [x16, #0x880]
    // 0x1602c34: stp             x0, x16, [SP]
    // 0x1602c38: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1602c38: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1602c3c: r0 = Inst.find()
    //     0x1602c3c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1602c40: ldur            x2, [fp, #-8]
    // 0x1602c44: StoreField: r2->field_57 = r0
    //     0x1602c44: stur            w0, [x2, #0x57]
    //     0x1602c48: ldurb           w16, [x2, #-1]
    //     0x1602c4c: ldurb           w17, [x0, #-1]
    //     0x1602c50: and             x16, x17, x16, lsr #2
    //     0x1602c54: tst             x16, HEAP, lsr #32
    //     0x1602c58: b.eq            #0x1602c60
    //     0x1602c5c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602c60: r1 = false
    //     0x1602c60: add             x1, NULL, #0x30  ; false
    // 0x1602c64: r0 = BoolExtension.obs()
    //     0x1602c64: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602c68: ldur            x2, [fp, #-8]
    // 0x1602c6c: StoreField: r2->field_5b = r0
    //     0x1602c6c: stur            w0, [x2, #0x5b]
    //     0x1602c70: ldurb           w16, [x2, #-1]
    //     0x1602c74: ldurb           w17, [x0, #-1]
    //     0x1602c78: and             x16, x17, x16, lsr #2
    //     0x1602c7c: tst             x16, HEAP, lsr #32
    //     0x1602c80: b.eq            #0x1602c88
    //     0x1602c84: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602c88: r1 = false
    //     0x1602c88: add             x1, NULL, #0x30  ; false
    // 0x1602c8c: r0 = BoolExtension.obs()
    //     0x1602c8c: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602c90: ldur            x2, [fp, #-8]
    // 0x1602c94: StoreField: r2->field_5f = r0
    //     0x1602c94: stur            w0, [x2, #0x5f]
    //     0x1602c98: ldurb           w16, [x2, #-1]
    //     0x1602c9c: ldurb           w17, [x0, #-1]
    //     0x1602ca0: and             x16, x17, x16, lsr #2
    //     0x1602ca4: tst             x16, HEAP, lsr #32
    //     0x1602ca8: b.eq            #0x1602cb0
    //     0x1602cac: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602cb0: r1 = false
    //     0x1602cb0: add             x1, NULL, #0x30  ; false
    // 0x1602cb4: r0 = BoolExtension.obs()
    //     0x1602cb4: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602cb8: ldur            x3, [fp, #-8]
    // 0x1602cbc: StoreField: r3->field_63 = r0
    //     0x1602cbc: stur            w0, [x3, #0x63]
    //     0x1602cc0: ldurb           w16, [x3, #-1]
    //     0x1602cc4: ldurb           w17, [x0, #-1]
    //     0x1602cc8: and             x16, x17, x16, lsr #2
    //     0x1602ccc: tst             x16, HEAP, lsr #32
    //     0x1602cd0: b.eq            #0x1602cd8
    //     0x1602cd4: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x1602cd8: r1 = <String>
    //     0x1602cd8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x1602cdc: r2 = 0
    //     0x1602cdc: movz            x2, #0
    // 0x1602ce0: r0 = _GrowableList()
    //     0x1602ce0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x1602ce4: r16 = <String>
    //     0x1602ce4: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x1602ce8: stp             x0, x16, [SP]
    // 0x1602cec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602cec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602cf0: r0 = ListExtension.obs()
    //     0x1602cf0: bl              #0x1603118  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x1602cf4: ldur            x2, [fp, #-8]
    // 0x1602cf8: StoreField: r2->field_73 = r0
    //     0x1602cf8: stur            w0, [x2, #0x73]
    //     0x1602cfc: ldurb           w16, [x2, #-1]
    //     0x1602d00: ldurb           w17, [x0, #-1]
    //     0x1602d04: and             x16, x17, x16, lsr #2
    //     0x1602d08: tst             x16, HEAP, lsr #32
    //     0x1602d0c: b.eq            #0x1602d14
    //     0x1602d10: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602d14: r1 = Null
    //     0x1602d14: mov             x1, NULL
    // 0x1602d18: r0 = ReceivePort()
    //     0x1602d18: bl              #0x871d40  ; [dart:isolate] ReceivePort::ReceivePort
    // 0x1602d1c: ldur            x2, [fp, #-8]
    // 0x1602d20: StoreField: r2->field_7b = r0
    //     0x1602d20: stur            w0, [x2, #0x7b]
    //     0x1602d24: ldurb           w16, [x2, #-1]
    //     0x1602d28: ldurb           w17, [x0, #-1]
    //     0x1602d2c: and             x16, x17, x16, lsr #2
    //     0x1602d30: tst             x16, HEAP, lsr #32
    //     0x1602d34: b.eq            #0x1602d3c
    //     0x1602d38: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602d3c: r1 = <TaskInfo>
    //     0x1602d3c: add             x1, PP, #0xa, lsl #12  ; [pp+0xacc8] TypeArguments: <TaskInfo>
    //     0x1602d40: ldr             x1, [x1, #0xcc8]
    // 0x1602d44: r0 = _Set()
    //     0x1602d44: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x1602d48: mov             x1, x0
    // 0x1602d4c: r0 = _Uint32List
    //     0x1602d4c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x1602d50: StoreField: r1->field_1b = r0
    //     0x1602d50: stur            w0, [x1, #0x1b]
    // 0x1602d54: StoreField: r1->field_b = rZR
    //     0x1602d54: stur            wzr, [x1, #0xb]
    // 0x1602d58: r0 = const []
    //     0x1602d58: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x1602d5c: StoreField: r1->field_f = r0
    //     0x1602d5c: stur            w0, [x1, #0xf]
    // 0x1602d60: StoreField: r1->field_13 = rZR
    //     0x1602d60: stur            wzr, [x1, #0x13]
    // 0x1602d64: ArrayStore: r1[0] = rZR  ; List_4
    //     0x1602d64: stur            wzr, [x1, #0x17]
    // 0x1602d68: mov             x0, x1
    // 0x1602d6c: ldur            x1, [fp, #-8]
    // 0x1602d70: StoreField: r1->field_7f = r0
    //     0x1602d70: stur            w0, [x1, #0x7f]
    //     0x1602d74: ldurb           w16, [x1, #-1]
    //     0x1602d78: ldurb           w17, [x0, #-1]
    //     0x1602d7c: and             x16, x17, x16, lsr #2
    //     0x1602d80: tst             x16, HEAP, lsr #32
    //     0x1602d84: b.eq            #0x1602d8c
    //     0x1602d88: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1602d8c: r0 = DownloadFilesToLocals()
    //     0x1602d8c: bl              #0x160310c  ; AllocateDownloadFilesToLocalsStub -> DownloadFilesToLocals (size=0xc)
    // 0x1602d90: mov             x1, x0
    // 0x1602d94: r0 = Sentinel
    //     0x1602d94: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x1602d98: StoreField: r1->field_7 = r0
    //     0x1602d98: stur            w0, [x1, #7]
    // 0x1602d9c: mov             x0, x1
    // 0x1602da0: ldur            x2, [fp, #-8]
    // 0x1602da4: StoreField: r2->field_87 = r0
    //     0x1602da4: stur            w0, [x2, #0x87]
    //     0x1602da8: ldurb           w16, [x2, #-1]
    //     0x1602dac: ldurb           w17, [x0, #-1]
    //     0x1602db0: and             x16, x17, x16, lsr #2
    //     0x1602db4: tst             x16, HEAP, lsr #32
    //     0x1602db8: b.eq            #0x1602dc0
    //     0x1602dbc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602dc0: r1 = <FormState>
    //     0x1602dc0: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0x1602dc4: ldr             x1, [x1, #0xad8]
    // 0x1602dc8: r0 = LabeledGlobalKey()
    //     0x1602dc8: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0x1602dcc: ldur            x2, [fp, #-8]
    // 0x1602dd0: StoreField: r2->field_8b = r0
    //     0x1602dd0: stur            w0, [x2, #0x8b]
    //     0x1602dd4: ldurb           w16, [x2, #-1]
    //     0x1602dd8: ldurb           w17, [x0, #-1]
    //     0x1602ddc: and             x16, x17, x16, lsr #2
    //     0x1602de0: tst             x16, HEAP, lsr #32
    //     0x1602de4: b.eq            #0x1602dec
    //     0x1602de8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602dec: r1 = <TextEditingValue>
    //     0x1602dec: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0x1602df0: r0 = TextEditingController()
    //     0x1602df0: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x1602df4: mov             x1, x0
    // 0x1602df8: stur            x0, [fp, #-0x10]
    // 0x1602dfc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1602dfc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1602e00: r0 = TextEditingController()
    //     0x1602e00: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x1602e04: ldur            x0, [fp, #-0x10]
    // 0x1602e08: ldur            x3, [fp, #-8]
    // 0x1602e0c: StoreField: r3->field_8f = r0
    //     0x1602e0c: stur            w0, [x3, #0x8f]
    //     0x1602e10: ldurb           w16, [x3, #-1]
    //     0x1602e14: ldurb           w17, [x0, #-1]
    //     0x1602e18: and             x16, x17, x16, lsr #2
    //     0x1602e1c: tst             x16, HEAP, lsr #32
    //     0x1602e20: b.eq            #0x1602e28
    //     0x1602e24: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x1602e28: r1 = <File>
    //     0x1602e28: add             x1, PP, #0x13, lsl #12  ; [pp+0x13160] TypeArguments: <File>
    //     0x1602e2c: ldr             x1, [x1, #0x160]
    // 0x1602e30: r2 = 0
    //     0x1602e30: movz            x2, #0
    // 0x1602e34: r0 = _GrowableList()
    //     0x1602e34: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x1602e38: r16 = <File>
    //     0x1602e38: add             x16, PP, #0x13, lsl #12  ; [pp+0x13160] TypeArguments: <File>
    //     0x1602e3c: ldr             x16, [x16, #0x160]
    // 0x1602e40: stp             x0, x16, [SP]
    // 0x1602e44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602e44: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602e48: r0 = ListExtension.obs()
    //     0x1602e48: bl              #0x1603118  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x1602e4c: ldur            x3, [fp, #-8]
    // 0x1602e50: StoreField: r3->field_93 = r0
    //     0x1602e50: stur            w0, [x3, #0x93]
    //     0x1602e54: ldurb           w16, [x3, #-1]
    //     0x1602e58: ldurb           w17, [x0, #-1]
    //     0x1602e5c: and             x16, x17, x16, lsr #2
    //     0x1602e60: tst             x16, HEAP, lsr #32
    //     0x1602e64: b.eq            #0x1602e6c
    //     0x1602e68: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x1602e6c: r1 = <Media>
    //     0x1602e6c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36500] TypeArguments: <Media>
    //     0x1602e70: ldr             x1, [x1, #0x500]
    // 0x1602e74: r2 = 0
    //     0x1602e74: movz            x2, #0
    // 0x1602e78: r0 = _GrowableList()
    //     0x1602e78: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x1602e7c: r16 = <Media>
    //     0x1602e7c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36500] TypeArguments: <Media>
    //     0x1602e80: ldr             x16, [x16, #0x500]
    // 0x1602e84: stp             x0, x16, [SP]
    // 0x1602e88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1602e88: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1602e8c: r0 = ListExtension.obs()
    //     0x1602e8c: bl              #0x1603118  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::ListExtension.obs
    // 0x1602e90: ldur            x2, [fp, #-8]
    // 0x1602e94: StoreField: r2->field_97 = r0
    //     0x1602e94: stur            w0, [x2, #0x97]
    //     0x1602e98: ldurb           w16, [x2, #-1]
    //     0x1602e9c: ldurb           w17, [x0, #-1]
    //     0x1602ea0: and             x16, x17, x16, lsr #2
    //     0x1602ea4: tst             x16, HEAP, lsr #32
    //     0x1602ea8: b.eq            #0x1602eb0
    //     0x1602eac: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602eb0: r1 = 0
    //     0x1602eb0: movz            x1, #0
    // 0x1602eb4: r0 = IntExtension.obs()
    //     0x1602eb4: bl              #0x15fb3c8  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x1602eb8: ldur            x2, [fp, #-8]
    // 0x1602ebc: StoreField: r2->field_9b = r0
    //     0x1602ebc: stur            w0, [x2, #0x9b]
    //     0x1602ec0: ldurb           w16, [x2, #-1]
    //     0x1602ec4: ldurb           w17, [x0, #-1]
    //     0x1602ec8: and             x16, x17, x16, lsr #2
    //     0x1602ecc: tst             x16, HEAP, lsr #32
    //     0x1602ed0: b.eq            #0x1602ed8
    //     0x1602ed4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602ed8: r1 = 0
    //     0x1602ed8: movz            x1, #0
    // 0x1602edc: r0 = IntExtension.obs()
    //     0x1602edc: bl              #0x15fb3c8  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::IntExtension.obs
    // 0x1602ee0: ldur            x1, [fp, #-8]
    // 0x1602ee4: StoreField: r1->field_9f = r0
    //     0x1602ee4: stur            w0, [x1, #0x9f]
    //     0x1602ee8: ldurb           w16, [x1, #-1]
    //     0x1602eec: ldurb           w17, [x0, #-1]
    //     0x1602ef0: and             x16, x17, x16, lsr #2
    //     0x1602ef4: tst             x16, HEAP, lsr #32
    //     0x1602ef8: b.eq            #0x1602f00
    //     0x1602efc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1602f00: r0 = DoubleExtension.obs()
    //     0x1602f00: bl              #0x16030a0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::DoubleExtension.obs
    // 0x1602f04: ldur            x2, [fp, #-8]
    // 0x1602f08: StoreField: r2->field_a3 = r0
    //     0x1602f08: stur            w0, [x2, #0xa3]
    //     0x1602f0c: ldurb           w16, [x2, #-1]
    //     0x1602f10: ldurb           w17, [x0, #-1]
    //     0x1602f14: and             x16, x17, x16, lsr #2
    //     0x1602f18: tst             x16, HEAP, lsr #32
    //     0x1602f1c: b.eq            #0x1602f24
    //     0x1602f20: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602f24: r0 = 1600
    //     0x1602f24: movz            x0, #0x640, lsl #16
    // 0x1602f28: StoreField: r2->field_a7 = r0
    //     0x1602f28: stur            x0, [x2, #0xa7]
    // 0x1602f2c: r1 = false
    //     0x1602f2c: add             x1, NULL, #0x30  ; false
    // 0x1602f30: r0 = BoolExtension.obs()
    //     0x1602f30: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602f34: ldur            x2, [fp, #-8]
    // 0x1602f38: StoreField: r2->field_af = r0
    //     0x1602f38: stur            w0, [x2, #0xaf]
    //     0x1602f3c: ldurb           w16, [x2, #-1]
    //     0x1602f40: ldurb           w17, [x0, #-1]
    //     0x1602f44: and             x16, x17, x16, lsr #2
    //     0x1602f48: tst             x16, HEAP, lsr #32
    //     0x1602f4c: b.eq            #0x1602f54
    //     0x1602f50: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602f54: r1 = false
    //     0x1602f54: add             x1, NULL, #0x30  ; false
    // 0x1602f58: r0 = BoolExtension.obs()
    //     0x1602f58: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602f5c: r1 = false
    //     0x1602f5c: add             x1, NULL, #0x30  ; false
    // 0x1602f60: r0 = BoolExtension.obs()
    //     0x1602f60: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602f64: ldur            x2, [fp, #-8]
    // 0x1602f68: StoreField: r2->field_b3 = r0
    //     0x1602f68: stur            w0, [x2, #0xb3]
    //     0x1602f6c: ldurb           w16, [x2, #-1]
    //     0x1602f70: ldurb           w17, [x0, #-1]
    //     0x1602f74: and             x16, x17, x16, lsr #2
    //     0x1602f78: tst             x16, HEAP, lsr #32
    //     0x1602f7c: b.eq            #0x1602f84
    //     0x1602f80: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602f84: r1 = false
    //     0x1602f84: add             x1, NULL, #0x30  ; false
    // 0x1602f88: r0 = BoolExtension.obs()
    //     0x1602f88: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602f8c: ldur            x1, [fp, #-8]
    // 0x1602f90: StoreField: r1->field_b7 = r0
    //     0x1602f90: stur            w0, [x1, #0xb7]
    //     0x1602f94: ldurb           w16, [x1, #-1]
    //     0x1602f98: ldurb           w17, [x0, #-1]
    //     0x1602f9c: and             x16, x17, x16, lsr #2
    //     0x1602fa0: tst             x16, HEAP, lsr #32
    //     0x1602fa4: b.eq            #0x1602fac
    //     0x1602fa8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1602fac: r0 = DoubleExtension.obs()
    //     0x1602fac: bl              #0x16030a0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::DoubleExtension.obs
    // 0x1602fb0: ldur            x2, [fp, #-8]
    // 0x1602fb4: StoreField: r2->field_c3 = r0
    //     0x1602fb4: stur            w0, [x2, #0xc3]
    //     0x1602fb8: ldurb           w16, [x2, #-1]
    //     0x1602fbc: ldurb           w17, [x0, #-1]
    //     0x1602fc0: and             x16, x17, x16, lsr #2
    //     0x1602fc4: tst             x16, HEAP, lsr #32
    //     0x1602fc8: b.eq            #0x1602fd0
    //     0x1602fcc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602fd0: r1 = false
    //     0x1602fd0: add             x1, NULL, #0x30  ; false
    // 0x1602fd4: r0 = BoolExtension.obs()
    //     0x1602fd4: bl              #0x12c3cb4  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::BoolExtension.obs
    // 0x1602fd8: ldur            x2, [fp, #-8]
    // 0x1602fdc: StoreField: r2->field_db = r0
    //     0x1602fdc: stur            w0, [x2, #0xdb]
    //     0x1602fe0: ldurb           w16, [x2, #-1]
    //     0x1602fe4: ldurb           w17, [x0, #-1]
    //     0x1602fe8: and             x16, x17, x16, lsr #2
    //     0x1602fec: tst             x16, HEAP, lsr #32
    //     0x1602ff0: b.eq            #0x1602ff8
    //     0x1602ff4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1602ff8: mov             x1, x2
    // 0x1602ffc: r0 = BaseController()
    //     0x1602ffc: bl              #0x12c396c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::BaseController
    // 0x1603000: r16 = OrdersRepo
    //     0x1603000: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf60] Type: OrdersRepo
    //     0x1603004: ldr             x16, [x16, #0xf60]
    // 0x1603008: str             x16, [SP]
    // 0x160300c: r0 = toString()
    //     0x160300c: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1603010: r1 = Function '<anonymous closure>':.
    //     0x1603010: add             x1, PP, #0x49, lsl #12  ; [pp+0x49cf0] AnonymousClosure: (0x16028dc), in [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::OrdersController (0x1602400)
    //     0x1603014: ldr             x1, [x1, #0xcf0]
    // 0x1603018: r2 = Null
    //     0x1603018: mov             x2, NULL
    // 0x160301c: stur            x0, [fp, #-0x10]
    // 0x1603020: r0 = AllocateClosure()
    //     0x1603020: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1603024: r16 = <OrdersRepo>
    //     0x1603024: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf70] TypeArguments: <OrdersRepo>
    //     0x1603028: ldr             x16, [x16, #0xf70]
    // 0x160302c: stp             x0, x16, [SP, #8]
    // 0x1603030: ldur            x16, [fp, #-0x10]
    // 0x1603034: str             x16, [SP]
    // 0x1603038: r4 = const [0x1, 0x2, 0x2, 0x1, tag, 0x1, null]
    //     0x1603038: add             x4, PP, #0xa, lsl #12  ; [pp+0xaac8] List(7) [0x1, 0x2, 0x2, 0x1, "tag", 0x1, Null]
    //     0x160303c: ldr             x4, [x4, #0xac8]
    // 0x1603040: r0 = Inst.lazyPut()
    //     0x1603040: bl              #0x12c3844  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.lazyPut
    // 0x1603044: r16 = OrdersRepo
    //     0x1603044: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf60] Type: OrdersRepo
    //     0x1603048: ldr             x16, [x16, #0xf60]
    // 0x160304c: str             x16, [SP]
    // 0x1603050: r0 = toString()
    //     0x1603050: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1603054: r16 = <OrdersRepo>
    //     0x1603054: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf70] TypeArguments: <OrdersRepo>
    //     0x1603058: ldr             x16, [x16, #0xf70]
    // 0x160305c: stp             x0, x16, [SP]
    // 0x1603060: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1603060: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1603064: r0 = Inst.find()
    //     0x1603064: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1603068: ldur            x1, [fp, #-8]
    // 0x160306c: StoreField: r1->field_47 = r0
    //     0x160306c: stur            w0, [x1, #0x47]
    //     0x1603070: ldurb           w16, [x1, #-1]
    //     0x1603074: ldurb           w17, [x0, #-1]
    //     0x1603078: and             x16, x17, x16, lsr #2
    //     0x160307c: tst             x16, HEAP, lsr #32
    //     0x1603080: b.eq            #0x1603088
    //     0x1603084: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1603088: r0 = Null
    //     0x1603088: mov             x0, NULL
    // 0x160308c: LeaveFrame
    //     0x160308c: mov             SP, fp
    //     0x1603090: ldp             fp, lr, [SP], #0x10
    // 0x1603094: ret
    //     0x1603094: ret             
    // 0x1603098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1603098: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x160309c: b               #0x1602b10
  }
}
