// lib: , url: package:customer_app/app/presentation/views/glass/browse/browse_view.dart

// class id: 1049352, size: 0x8
class :: {
}

// class id: 4580, size: 0x14, field offset: 0x14
//   const constructor, 
class BrowseView extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14d2ef4, size: 0x58
    // 0x14d2ef4: EnterFrame
    //     0x14d2ef4: stp             fp, lr, [SP, #-0x10]!
    //     0x14d2ef8: mov             fp, SP
    // 0x14d2efc: AllocStack(0x10)
    //     0x14d2efc: sub             SP, SP, #0x10
    // 0x14d2f00: SetupParameters(BrowseView this /* r1 => r1, fp-0x8 */)
    //     0x14d2f00: stur            x1, [fp, #-8]
    // 0x14d2f04: r1 = 1
    //     0x14d2f04: movz            x1, #0x1
    // 0x14d2f08: r0 = AllocateContext()
    //     0x14d2f08: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d2f0c: mov             x1, x0
    // 0x14d2f10: ldur            x0, [fp, #-8]
    // 0x14d2f14: stur            x1, [fp, #-0x10]
    // 0x14d2f18: StoreField: r1->field_f = r0
    //     0x14d2f18: stur            w0, [x1, #0xf]
    // 0x14d2f1c: r0 = Obx()
    //     0x14d2f1c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d2f20: ldur            x2, [fp, #-0x10]
    // 0x14d2f24: r1 = Function '<anonymous closure>':.
    //     0x14d2f24: add             x1, PP, #0x41, lsl #12  ; [pp+0x419f0] AnonymousClosure: (0x14d2f4c), in [package:customer_app/app/presentation/views/glass/browse/browse_view.dart] BrowseView::body (0x14d2ef4)
    //     0x14d2f28: ldr             x1, [x1, #0x9f0]
    // 0x14d2f2c: stur            x0, [fp, #-8]
    // 0x14d2f30: r0 = AllocateClosure()
    //     0x14d2f30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2f34: mov             x1, x0
    // 0x14d2f38: ldur            x0, [fp, #-8]
    // 0x14d2f3c: StoreField: r0->field_b = r1
    //     0x14d2f3c: stur            w1, [x0, #0xb]
    // 0x14d2f40: LeaveFrame
    //     0x14d2f40: mov             SP, fp
    //     0x14d2f44: ldp             fp, lr, [SP], #0x10
    // 0x14d2f48: ret
    //     0x14d2f48: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x14d2f4c, size: 0x104
    // 0x14d2f4c: EnterFrame
    //     0x14d2f4c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d2f50: mov             fp, SP
    // 0x14d2f54: AllocStack(0x38)
    //     0x14d2f54: sub             SP, SP, #0x38
    // 0x14d2f58: SetupParameters()
    //     0x14d2f58: ldr             x0, [fp, #0x10]
    //     0x14d2f5c: ldur            w2, [x0, #0x17]
    //     0x14d2f60: add             x2, x2, HEAP, lsl #32
    //     0x14d2f64: stur            x2, [fp, #-8]
    // 0x14d2f68: CheckStackOverflow
    //     0x14d2f68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d2f6c: cmp             SP, x16
    //     0x14d2f70: b.ls            #0x14d3048
    // 0x14d2f74: LoadField: r1 = r2->field_f
    //     0x14d2f74: ldur            w1, [x2, #0xf]
    // 0x14d2f78: DecompressPointer r1
    //     0x14d2f78: add             x1, x1, HEAP, lsl #32
    // 0x14d2f7c: r0 = controller()
    //     0x14d2f7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d2f80: LoadField: r1 = r0->field_4b
    //     0x14d2f80: ldur            w1, [x0, #0x4b]
    // 0x14d2f84: DecompressPointer r1
    //     0x14d2f84: add             x1, x1, HEAP, lsl #32
    // 0x14d2f88: r0 = value()
    //     0x14d2f88: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d2f8c: LoadField: r1 = r0->field_63
    //     0x14d2f8c: ldur            w1, [x0, #0x63]
    // 0x14d2f90: DecompressPointer r1
    //     0x14d2f90: add             x1, x1, HEAP, lsl #32
    // 0x14d2f94: cmp             w1, NULL
    // 0x14d2f98: b.ne            #0x14d2fa4
    // 0x14d2f9c: r0 = Null
    //     0x14d2f9c: mov             x0, NULL
    // 0x14d2fa0: b               #0x14d2fa8
    // 0x14d2fa4: LoadField: r0 = r1->field_b
    //     0x14d2fa4: ldur            w0, [x1, #0xb]
    // 0x14d2fa8: cmp             w0, NULL
    // 0x14d2fac: b.ne            #0x14d2fb8
    // 0x14d2fb0: r3 = 0
    //     0x14d2fb0: movz            x3, #0
    // 0x14d2fb4: b               #0x14d2fc0
    // 0x14d2fb8: r1 = LoadInt32Instr(r0)
    //     0x14d2fb8: sbfx            x1, x0, #1, #0x1f
    // 0x14d2fbc: mov             x3, x1
    // 0x14d2fc0: ldur            x2, [fp, #-8]
    // 0x14d2fc4: stur            x3, [fp, #-0x10]
    // 0x14d2fc8: r1 = Function '<anonymous closure>':.
    //     0x14d2fc8: add             x1, PP, #0x41, lsl #12  ; [pp+0x419f8] AnonymousClosure: (0x14d3050), in [package:customer_app/app/presentation/views/glass/browse/browse_view.dart] BrowseView::body (0x14d2ef4)
    //     0x14d2fcc: ldr             x1, [x1, #0x9f8]
    // 0x14d2fd0: r0 = AllocateClosure()
    //     0x14d2fd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2fd4: r1 = Function '<anonymous closure>':.
    //     0x14d2fd4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a00] AnonymousClosure: (0x137cf60), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14d2fd8: ldr             x1, [x1, #0xa00]
    // 0x14d2fdc: r2 = Null
    //     0x14d2fdc: mov             x2, NULL
    // 0x14d2fe0: stur            x0, [fp, #-8]
    // 0x14d2fe4: r0 = AllocateClosure()
    //     0x14d2fe4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2fe8: stur            x0, [fp, #-0x18]
    // 0x14d2fec: r0 = ListView()
    //     0x14d2fec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14d2ff0: stur            x0, [fp, #-0x20]
    // 0x14d2ff4: r16 = Instance_Axis
    //     0x14d2ff4: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d2ff8: r30 = true
    //     0x14d2ff8: add             lr, NULL, #0x20  ; true
    // 0x14d2ffc: stp             lr, x16, [SP, #8]
    // 0x14d3000: r16 = false
    //     0x14d3000: add             x16, NULL, #0x30  ; false
    // 0x14d3004: str             x16, [SP]
    // 0x14d3008: mov             x1, x0
    // 0x14d300c: ldur            x2, [fp, #-8]
    // 0x14d3010: ldur            x3, [fp, #-0x10]
    // 0x14d3014: ldur            x5, [fp, #-0x18]
    // 0x14d3018: r4 = const [0, 0x7, 0x3, 0x4, primary, 0x6, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0x14d3018: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e528] List(11) [0, 0x7, 0x3, 0x4, "primary", 0x6, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0x14d301c: ldr             x4, [x4, #0x528]
    // 0x14d3020: r0 = ListView.separated()
    //     0x14d3020: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14d3024: r0 = Padding()
    //     0x14d3024: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d3028: r1 = Instance_EdgeInsets
    //     0x14d3028: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x14d302c: ldr             x1, [x1, #0xa00]
    // 0x14d3030: StoreField: r0->field_f = r1
    //     0x14d3030: stur            w1, [x0, #0xf]
    // 0x14d3034: ldur            x1, [fp, #-0x20]
    // 0x14d3038: StoreField: r0->field_b = r1
    //     0x14d3038: stur            w1, [x0, #0xb]
    // 0x14d303c: LeaveFrame
    //     0x14d303c: mov             SP, fp
    //     0x14d3040: ldp             fp, lr, [SP], #0x10
    // 0x14d3044: ret
    //     0x14d3044: ret             
    // 0x14d3048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d3048: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d304c: b               #0x14d2f74
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14d3050, size: 0x57c
    // 0x14d3050: EnterFrame
    //     0x14d3050: stp             fp, lr, [SP, #-0x10]!
    //     0x14d3054: mov             fp, SP
    // 0x14d3058: AllocStack(0x50)
    //     0x14d3058: sub             SP, SP, #0x50
    // 0x14d305c: SetupParameters()
    //     0x14d305c: ldr             x0, [fp, #0x20]
    //     0x14d3060: ldur            w1, [x0, #0x17]
    //     0x14d3064: add             x1, x1, HEAP, lsl #32
    //     0x14d3068: stur            x1, [fp, #-8]
    // 0x14d306c: CheckStackOverflow
    //     0x14d306c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d3070: cmp             SP, x16
    //     0x14d3074: b.ls            #0x14d35b4
    // 0x14d3078: r1 = 1
    //     0x14d3078: movz            x1, #0x1
    // 0x14d307c: r0 = AllocateContext()
    //     0x14d307c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d3080: mov             x2, x0
    // 0x14d3084: ldur            x0, [fp, #-8]
    // 0x14d3088: stur            x2, [fp, #-0x10]
    // 0x14d308c: StoreField: r2->field_b = r0
    //     0x14d308c: stur            w0, [x2, #0xb]
    // 0x14d3090: ldr             x1, [fp, #0x10]
    // 0x14d3094: StoreField: r2->field_f = r1
    //     0x14d3094: stur            w1, [x2, #0xf]
    // 0x14d3098: LoadField: r1 = r0->field_f
    //     0x14d3098: ldur            w1, [x0, #0xf]
    // 0x14d309c: DecompressPointer r1
    //     0x14d309c: add             x1, x1, HEAP, lsl #32
    // 0x14d30a0: r0 = controller()
    //     0x14d30a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d30a4: LoadField: r1 = r0->field_4b
    //     0x14d30a4: ldur            w1, [x0, #0x4b]
    // 0x14d30a8: DecompressPointer r1
    //     0x14d30a8: add             x1, x1, HEAP, lsl #32
    // 0x14d30ac: r0 = value()
    //     0x14d30ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d30b0: LoadField: r2 = r0->field_63
    //     0x14d30b0: ldur            w2, [x0, #0x63]
    // 0x14d30b4: DecompressPointer r2
    //     0x14d30b4: add             x2, x2, HEAP, lsl #32
    // 0x14d30b8: cmp             w2, NULL
    // 0x14d30bc: b.ne            #0x14d30cc
    // 0x14d30c0: ldur            x3, [fp, #-0x10]
    // 0x14d30c4: r0 = Null
    //     0x14d30c4: mov             x0, NULL
    // 0x14d30c8: b               #0x14d3148
    // 0x14d30cc: ldur            x3, [fp, #-0x10]
    // 0x14d30d0: LoadField: r0 = r3->field_f
    //     0x14d30d0: ldur            w0, [x3, #0xf]
    // 0x14d30d4: DecompressPointer r0
    //     0x14d30d4: add             x0, x0, HEAP, lsl #32
    // 0x14d30d8: LoadField: r1 = r2->field_b
    //     0x14d30d8: ldur            w1, [x2, #0xb]
    // 0x14d30dc: r4 = LoadInt32Instr(r0)
    //     0x14d30dc: sbfx            x4, x0, #1, #0x1f
    //     0x14d30e0: tbz             w0, #0, #0x14d30e8
    //     0x14d30e4: ldur            x4, [x0, #7]
    // 0x14d30e8: r0 = LoadInt32Instr(r1)
    //     0x14d30e8: sbfx            x0, x1, #1, #0x1f
    // 0x14d30ec: mov             x1, x4
    // 0x14d30f0: cmp             x1, x0
    // 0x14d30f4: b.hs            #0x14d35bc
    // 0x14d30f8: LoadField: r0 = r2->field_f
    //     0x14d30f8: ldur            w0, [x2, #0xf]
    // 0x14d30fc: DecompressPointer r0
    //     0x14d30fc: add             x0, x0, HEAP, lsl #32
    // 0x14d3100: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d3100: add             x16, x0, x4, lsl #2
    //     0x14d3104: ldur            w1, [x16, #0xf]
    // 0x14d3108: DecompressPointer r1
    //     0x14d3108: add             x1, x1, HEAP, lsl #32
    // 0x14d310c: cmp             w1, NULL
    // 0x14d3110: b.ne            #0x14d311c
    // 0x14d3114: r0 = Null
    //     0x14d3114: mov             x0, NULL
    // 0x14d3118: b               #0x14d3148
    // 0x14d311c: LoadField: r0 = r1->field_1f
    //     0x14d311c: ldur            w0, [x1, #0x1f]
    // 0x14d3120: DecompressPointer r0
    //     0x14d3120: add             x0, x0, HEAP, lsl #32
    // 0x14d3124: cmp             w0, NULL
    // 0x14d3128: b.ne            #0x14d3134
    // 0x14d312c: r0 = Null
    //     0x14d312c: mov             x0, NULL
    // 0x14d3130: b               #0x14d3148
    // 0x14d3134: LoadField: r1 = r0->field_b
    //     0x14d3134: ldur            w1, [x0, #0xb]
    // 0x14d3138: cbnz            w1, #0x14d3144
    // 0x14d313c: r0 = false
    //     0x14d313c: add             x0, NULL, #0x30  ; false
    // 0x14d3140: b               #0x14d3148
    // 0x14d3144: r0 = true
    //     0x14d3144: add             x0, NULL, #0x20  ; true
    // 0x14d3148: cmp             w0, NULL
    // 0x14d314c: b.eq            #0x14d3160
    // 0x14d3150: tbnz            w0, #4, #0x14d3160
    // 0x14d3154: r2 = Instance_Icon
    //     0x14d3154: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e530] Obj!Icon@d66771
    //     0x14d3158: ldr             x2, [x2, #0x530]
    // 0x14d315c: b               #0x14d3178
    // 0x14d3160: r0 = Container()
    //     0x14d3160: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d3164: mov             x1, x0
    // 0x14d3168: stur            x0, [fp, #-0x18]
    // 0x14d316c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d316c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d3170: r0 = Container()
    //     0x14d3170: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d3174: ldur            x2, [fp, #-0x18]
    // 0x14d3178: ldur            x0, [fp, #-8]
    // 0x14d317c: stur            x2, [fp, #-0x18]
    // 0x14d3180: LoadField: r1 = r0->field_f
    //     0x14d3180: ldur            w1, [x0, #0xf]
    // 0x14d3184: DecompressPointer r1
    //     0x14d3184: add             x1, x1, HEAP, lsl #32
    // 0x14d3188: r0 = controller()
    //     0x14d3188: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d318c: LoadField: r1 = r0->field_4b
    //     0x14d318c: ldur            w1, [x0, #0x4b]
    // 0x14d3190: DecompressPointer r1
    //     0x14d3190: add             x1, x1, HEAP, lsl #32
    // 0x14d3194: r0 = value()
    //     0x14d3194: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3198: LoadField: r2 = r0->field_63
    //     0x14d3198: ldur            w2, [x0, #0x63]
    // 0x14d319c: DecompressPointer r2
    //     0x14d319c: add             x2, x2, HEAP, lsl #32
    // 0x14d31a0: cmp             w2, NULL
    // 0x14d31a4: b.ne            #0x14d31b4
    // 0x14d31a8: ldur            x3, [fp, #-0x10]
    // 0x14d31ac: r0 = Null
    //     0x14d31ac: mov             x0, NULL
    // 0x14d31b0: b               #0x14d3230
    // 0x14d31b4: ldur            x3, [fp, #-0x10]
    // 0x14d31b8: LoadField: r0 = r3->field_f
    //     0x14d31b8: ldur            w0, [x3, #0xf]
    // 0x14d31bc: DecompressPointer r0
    //     0x14d31bc: add             x0, x0, HEAP, lsl #32
    // 0x14d31c0: LoadField: r1 = r2->field_b
    //     0x14d31c0: ldur            w1, [x2, #0xb]
    // 0x14d31c4: r4 = LoadInt32Instr(r0)
    //     0x14d31c4: sbfx            x4, x0, #1, #0x1f
    //     0x14d31c8: tbz             w0, #0, #0x14d31d0
    //     0x14d31cc: ldur            x4, [x0, #7]
    // 0x14d31d0: r0 = LoadInt32Instr(r1)
    //     0x14d31d0: sbfx            x0, x1, #1, #0x1f
    // 0x14d31d4: mov             x1, x4
    // 0x14d31d8: cmp             x1, x0
    // 0x14d31dc: b.hs            #0x14d35c0
    // 0x14d31e0: LoadField: r0 = r2->field_f
    //     0x14d31e0: ldur            w0, [x2, #0xf]
    // 0x14d31e4: DecompressPointer r0
    //     0x14d31e4: add             x0, x0, HEAP, lsl #32
    // 0x14d31e8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d31e8: add             x16, x0, x4, lsl #2
    //     0x14d31ec: ldur            w1, [x16, #0xf]
    // 0x14d31f0: DecompressPointer r1
    //     0x14d31f0: add             x1, x1, HEAP, lsl #32
    // 0x14d31f4: cmp             w1, NULL
    // 0x14d31f8: b.ne            #0x14d3204
    // 0x14d31fc: r0 = Null
    //     0x14d31fc: mov             x0, NULL
    // 0x14d3200: b               #0x14d3230
    // 0x14d3204: LoadField: r0 = r1->field_1f
    //     0x14d3204: ldur            w0, [x1, #0x1f]
    // 0x14d3208: DecompressPointer r0
    //     0x14d3208: add             x0, x0, HEAP, lsl #32
    // 0x14d320c: cmp             w0, NULL
    // 0x14d3210: b.ne            #0x14d321c
    // 0x14d3214: r0 = Null
    //     0x14d3214: mov             x0, NULL
    // 0x14d3218: b               #0x14d3230
    // 0x14d321c: LoadField: r1 = r0->field_b
    //     0x14d321c: ldur            w1, [x0, #0xb]
    // 0x14d3220: cbnz            w1, #0x14d322c
    // 0x14d3224: r0 = false
    //     0x14d3224: add             x0, NULL, #0x30  ; false
    // 0x14d3228: b               #0x14d3230
    // 0x14d322c: r0 = true
    //     0x14d322c: add             x0, NULL, #0x20  ; true
    // 0x14d3230: cmp             w0, NULL
    // 0x14d3234: b.eq            #0x14d3248
    // 0x14d3238: tbnz            w0, #4, #0x14d3248
    // 0x14d323c: r2 = Instance_Icon
    //     0x14d323c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e538] Obj!Icon@d66931
    //     0x14d3240: ldr             x2, [x2, #0x538]
    // 0x14d3244: b               #0x14d3260
    // 0x14d3248: r0 = Container()
    //     0x14d3248: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d324c: mov             x1, x0
    // 0x14d3250: stur            x0, [fp, #-0x20]
    // 0x14d3254: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d3254: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d3258: r0 = Container()
    //     0x14d3258: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d325c: ldur            x2, [fp, #-0x20]
    // 0x14d3260: ldur            x0, [fp, #-8]
    // 0x14d3264: ldr             x1, [fp, #0x18]
    // 0x14d3268: stur            x2, [fp, #-0x20]
    // 0x14d326c: r0 = of()
    //     0x14d326c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d3270: ldr             x1, [fp, #0x18]
    // 0x14d3274: r0 = of()
    //     0x14d3274: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d3278: ldur            x0, [fp, #-8]
    // 0x14d327c: LoadField: r1 = r0->field_f
    //     0x14d327c: ldur            w1, [x0, #0xf]
    // 0x14d3280: DecompressPointer r1
    //     0x14d3280: add             x1, x1, HEAP, lsl #32
    // 0x14d3284: r0 = controller()
    //     0x14d3284: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d3288: LoadField: r1 = r0->field_4b
    //     0x14d3288: ldur            w1, [x0, #0x4b]
    // 0x14d328c: DecompressPointer r1
    //     0x14d328c: add             x1, x1, HEAP, lsl #32
    // 0x14d3290: r0 = value()
    //     0x14d3290: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3294: LoadField: r2 = r0->field_63
    //     0x14d3294: ldur            w2, [x0, #0x63]
    // 0x14d3298: DecompressPointer r2
    //     0x14d3298: add             x2, x2, HEAP, lsl #32
    // 0x14d329c: cmp             w2, NULL
    // 0x14d32a0: b.ne            #0x14d32b0
    // 0x14d32a4: ldur            x3, [fp, #-0x10]
    // 0x14d32a8: r0 = Null
    //     0x14d32a8: mov             x0, NULL
    // 0x14d32ac: b               #0x14d3308
    // 0x14d32b0: ldur            x3, [fp, #-0x10]
    // 0x14d32b4: LoadField: r0 = r3->field_f
    //     0x14d32b4: ldur            w0, [x3, #0xf]
    // 0x14d32b8: DecompressPointer r0
    //     0x14d32b8: add             x0, x0, HEAP, lsl #32
    // 0x14d32bc: LoadField: r1 = r2->field_b
    //     0x14d32bc: ldur            w1, [x2, #0xb]
    // 0x14d32c0: r4 = LoadInt32Instr(r0)
    //     0x14d32c0: sbfx            x4, x0, #1, #0x1f
    //     0x14d32c4: tbz             w0, #0, #0x14d32cc
    //     0x14d32c8: ldur            x4, [x0, #7]
    // 0x14d32cc: r0 = LoadInt32Instr(r1)
    //     0x14d32cc: sbfx            x0, x1, #1, #0x1f
    // 0x14d32d0: mov             x1, x4
    // 0x14d32d4: cmp             x1, x0
    // 0x14d32d8: b.hs            #0x14d35c4
    // 0x14d32dc: LoadField: r0 = r2->field_f
    //     0x14d32dc: ldur            w0, [x2, #0xf]
    // 0x14d32e0: DecompressPointer r0
    //     0x14d32e0: add             x0, x0, HEAP, lsl #32
    // 0x14d32e4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d32e4: add             x16, x0, x4, lsl #2
    //     0x14d32e8: ldur            w1, [x16, #0xf]
    // 0x14d32ec: DecompressPointer r1
    //     0x14d32ec: add             x1, x1, HEAP, lsl #32
    // 0x14d32f0: cmp             w1, NULL
    // 0x14d32f4: b.ne            #0x14d3300
    // 0x14d32f8: r0 = Null
    //     0x14d32f8: mov             x0, NULL
    // 0x14d32fc: b               #0x14d3308
    // 0x14d3300: LoadField: r0 = r1->field_f
    //     0x14d3300: ldur            w0, [x1, #0xf]
    // 0x14d3304: DecompressPointer r0
    //     0x14d3304: add             x0, x0, HEAP, lsl #32
    // 0x14d3308: cmp             w0, NULL
    // 0x14d330c: b.ne            #0x14d3318
    // 0x14d3310: r2 = ""
    //     0x14d3310: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d3314: b               #0x14d331c
    // 0x14d3318: mov             x2, x0
    // 0x14d331c: ldur            x0, [fp, #-8]
    // 0x14d3320: ldr             x1, [fp, #0x18]
    // 0x14d3324: stur            x2, [fp, #-0x28]
    // 0x14d3328: r0 = of()
    //     0x14d3328: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d332c: LoadField: r1 = r0->field_87
    //     0x14d332c: ldur            w1, [x0, #0x87]
    // 0x14d3330: DecompressPointer r1
    //     0x14d3330: add             x1, x1, HEAP, lsl #32
    // 0x14d3334: LoadField: r0 = r1->field_7
    //     0x14d3334: ldur            w0, [x1, #7]
    // 0x14d3338: DecompressPointer r0
    //     0x14d3338: add             x0, x0, HEAP, lsl #32
    // 0x14d333c: r16 = 14.000000
    //     0x14d333c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d3340: ldr             x16, [x16, #0x1d8]
    // 0x14d3344: r30 = Instance_Color
    //     0x14d3344: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d3348: stp             lr, x16, [SP]
    // 0x14d334c: mov             x1, x0
    // 0x14d3350: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x14d3350: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x14d3354: ldr             x4, [x4, #0xaa0]
    // 0x14d3358: r0 = copyWith()
    //     0x14d3358: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d335c: stur            x0, [fp, #-0x30]
    // 0x14d3360: r0 = Text()
    //     0x14d3360: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d3364: mov             x1, x0
    // 0x14d3368: ldur            x0, [fp, #-0x28]
    // 0x14d336c: stur            x1, [fp, #-0x38]
    // 0x14d3370: StoreField: r1->field_b = r0
    //     0x14d3370: stur            w0, [x1, #0xb]
    // 0x14d3374: ldur            x0, [fp, #-0x30]
    // 0x14d3378: StoreField: r1->field_13 = r0
    //     0x14d3378: stur            w0, [x1, #0x13]
    // 0x14d337c: r0 = InkWell()
    //     0x14d337c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d3380: mov             x3, x0
    // 0x14d3384: ldur            x0, [fp, #-0x38]
    // 0x14d3388: stur            x3, [fp, #-0x28]
    // 0x14d338c: StoreField: r3->field_b = r0
    //     0x14d338c: stur            w0, [x3, #0xb]
    // 0x14d3390: ldur            x2, [fp, #-0x10]
    // 0x14d3394: r1 = Function '<anonymous closure>':.
    //     0x14d3394: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a08] AnonymousClosure: (0x137e97c), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14d3398: ldr             x1, [x1, #0xa08]
    // 0x14d339c: r0 = AllocateClosure()
    //     0x14d339c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d33a0: mov             x1, x0
    // 0x14d33a4: ldur            x0, [fp, #-0x28]
    // 0x14d33a8: StoreField: r0->field_f = r1
    //     0x14d33a8: stur            w1, [x0, #0xf]
    // 0x14d33ac: r2 = true
    //     0x14d33ac: add             x2, NULL, #0x20  ; true
    // 0x14d33b0: StoreField: r0->field_43 = r2
    //     0x14d33b0: stur            w2, [x0, #0x43]
    // 0x14d33b4: r3 = Instance_BoxShape
    //     0x14d33b4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d33b8: ldr             x3, [x3, #0x80]
    // 0x14d33bc: StoreField: r0->field_47 = r3
    //     0x14d33bc: stur            w3, [x0, #0x47]
    // 0x14d33c0: StoreField: r0->field_6f = r2
    //     0x14d33c0: stur            w2, [x0, #0x6f]
    // 0x14d33c4: r4 = false
    //     0x14d33c4: add             x4, NULL, #0x30  ; false
    // 0x14d33c8: StoreField: r0->field_73 = r4
    //     0x14d33c8: stur            w4, [x0, #0x73]
    // 0x14d33cc: StoreField: r0->field_83 = r2
    //     0x14d33cc: stur            w2, [x0, #0x83]
    // 0x14d33d0: StoreField: r0->field_7b = r4
    //     0x14d33d0: stur            w4, [x0, #0x7b]
    // 0x14d33d4: ldur            x1, [fp, #-8]
    // 0x14d33d8: LoadField: r5 = r1->field_f
    //     0x14d33d8: ldur            w5, [x1, #0xf]
    // 0x14d33dc: DecompressPointer r5
    //     0x14d33dc: add             x5, x5, HEAP, lsl #32
    // 0x14d33e0: mov             x1, x5
    // 0x14d33e4: r0 = controller()
    //     0x14d33e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d33e8: LoadField: r1 = r0->field_4b
    //     0x14d33e8: ldur            w1, [x0, #0x4b]
    // 0x14d33ec: DecompressPointer r1
    //     0x14d33ec: add             x1, x1, HEAP, lsl #32
    // 0x14d33f0: r0 = value()
    //     0x14d33f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d33f4: LoadField: r2 = r0->field_63
    //     0x14d33f4: ldur            w2, [x0, #0x63]
    // 0x14d33f8: DecompressPointer r2
    //     0x14d33f8: add             x2, x2, HEAP, lsl #32
    // 0x14d33fc: cmp             w2, NULL
    // 0x14d3400: b.ne            #0x14d3410
    // 0x14d3404: ldur            x3, [fp, #-0x10]
    // 0x14d3408: r0 = Null
    //     0x14d3408: mov             x0, NULL
    // 0x14d340c: b               #0x14d3480
    // 0x14d3410: ldur            x3, [fp, #-0x10]
    // 0x14d3414: LoadField: r0 = r3->field_f
    //     0x14d3414: ldur            w0, [x3, #0xf]
    // 0x14d3418: DecompressPointer r0
    //     0x14d3418: add             x0, x0, HEAP, lsl #32
    // 0x14d341c: LoadField: r1 = r2->field_b
    //     0x14d341c: ldur            w1, [x2, #0xb]
    // 0x14d3420: r4 = LoadInt32Instr(r0)
    //     0x14d3420: sbfx            x4, x0, #1, #0x1f
    //     0x14d3424: tbz             w0, #0, #0x14d342c
    //     0x14d3428: ldur            x4, [x0, #7]
    // 0x14d342c: r0 = LoadInt32Instr(r1)
    //     0x14d342c: sbfx            x0, x1, #1, #0x1f
    // 0x14d3430: mov             x1, x4
    // 0x14d3434: cmp             x1, x0
    // 0x14d3438: b.hs            #0x14d35c8
    // 0x14d343c: LoadField: r0 = r2->field_f
    //     0x14d343c: ldur            w0, [x2, #0xf]
    // 0x14d3440: DecompressPointer r0
    //     0x14d3440: add             x0, x0, HEAP, lsl #32
    // 0x14d3444: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d3444: add             x16, x0, x4, lsl #2
    //     0x14d3448: ldur            w1, [x16, #0xf]
    // 0x14d344c: DecompressPointer r1
    //     0x14d344c: add             x1, x1, HEAP, lsl #32
    // 0x14d3450: cmp             w1, NULL
    // 0x14d3454: b.ne            #0x14d3460
    // 0x14d3458: r0 = Null
    //     0x14d3458: mov             x0, NULL
    // 0x14d345c: b               #0x14d3480
    // 0x14d3460: LoadField: r0 = r1->field_1f
    //     0x14d3460: ldur            w0, [x1, #0x1f]
    // 0x14d3464: DecompressPointer r0
    //     0x14d3464: add             x0, x0, HEAP, lsl #32
    // 0x14d3468: cmp             w0, NULL
    // 0x14d346c: b.ne            #0x14d3478
    // 0x14d3470: r0 = Null
    //     0x14d3470: mov             x0, NULL
    // 0x14d3474: b               #0x14d3480
    // 0x14d3478: LoadField: r1 = r0->field_b
    //     0x14d3478: ldur            w1, [x0, #0xb]
    // 0x14d347c: mov             x0, x1
    // 0x14d3480: cmp             w0, NULL
    // 0x14d3484: b.ne            #0x14d3490
    // 0x14d3488: r6 = 0
    //     0x14d3488: movz            x6, #0
    // 0x14d348c: b               #0x14d3498
    // 0x14d3490: r1 = LoadInt32Instr(r0)
    //     0x14d3490: sbfx            x1, x0, #1, #0x1f
    // 0x14d3494: mov             x6, x1
    // 0x14d3498: ldur            x5, [fp, #-0x18]
    // 0x14d349c: ldur            x4, [fp, #-0x20]
    // 0x14d34a0: ldur            x0, [fp, #-0x28]
    // 0x14d34a4: mov             x2, x3
    // 0x14d34a8: stur            x6, [fp, #-0x40]
    // 0x14d34ac: r1 = Function '<anonymous closure>':.
    //     0x14d34ac: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a10] AnonymousClosure: (0x14d35d8), in [package:customer_app/app/presentation/views/glass/browse/browse_view.dart] BrowseView::body (0x14d2ef4)
    //     0x14d34b0: ldr             x1, [x1, #0xa10]
    // 0x14d34b4: r0 = AllocateClosure()
    //     0x14d34b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d34b8: r1 = Function '<anonymous closure>':.
    //     0x14d34b8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a18] AnonymousClosure: (0x137d640), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14d34bc: ldr             x1, [x1, #0xa18]
    // 0x14d34c0: r2 = Null
    //     0x14d34c0: mov             x2, NULL
    // 0x14d34c4: stur            x0, [fp, #-8]
    // 0x14d34c8: r0 = AllocateClosure()
    //     0x14d34c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d34cc: stur            x0, [fp, #-0x10]
    // 0x14d34d0: r0 = ListView()
    //     0x14d34d0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14d34d4: stur            x0, [fp, #-0x30]
    // 0x14d34d8: r16 = Instance_NeverScrollableScrollPhysics
    //     0x14d34d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14d34dc: ldr             x16, [x16, #0x1c8]
    // 0x14d34e0: r30 = true
    //     0x14d34e0: add             lr, NULL, #0x20  ; true
    // 0x14d34e4: stp             lr, x16, [SP]
    // 0x14d34e8: mov             x1, x0
    // 0x14d34ec: ldur            x2, [fp, #-8]
    // 0x14d34f0: ldur            x3, [fp, #-0x40]
    // 0x14d34f4: ldur            x5, [fp, #-0x10]
    // 0x14d34f8: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0x14d34f8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0x14d34fc: ldr             x4, [x4, #0x968]
    // 0x14d3500: r0 = ListView.separated()
    //     0x14d3500: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14d3504: r0 = BrowseAccordian()
    //     0x14d3504: bl              #0x14d35cc  ; AllocateBrowseAccordianStub -> BrowseAccordian (size=0x24)
    // 0x14d3508: mov             x1, x0
    // 0x14d350c: ldur            x0, [fp, #-0x28]
    // 0x14d3510: stur            x1, [fp, #-8]
    // 0x14d3514: StoreField: r1->field_b = r0
    //     0x14d3514: stur            w0, [x1, #0xb]
    // 0x14d3518: ldur            x0, [fp, #-0x30]
    // 0x14d351c: StoreField: r1->field_f = r0
    //     0x14d351c: stur            w0, [x1, #0xf]
    // 0x14d3520: r0 = true
    //     0x14d3520: add             x0, NULL, #0x20  ; true
    // 0x14d3524: StoreField: r1->field_13 = r0
    //     0x14d3524: stur            w0, [x1, #0x13]
    // 0x14d3528: ldur            x2, [fp, #-0x18]
    // 0x14d352c: StoreField: r1->field_1b = r2
    //     0x14d352c: stur            w2, [x1, #0x1b]
    // 0x14d3530: ldur            x2, [fp, #-0x20]
    // 0x14d3534: StoreField: r1->field_1f = r2
    //     0x14d3534: stur            w2, [x1, #0x1f]
    // 0x14d3538: r0 = InkWell()
    //     0x14d3538: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d353c: mov             x3, x0
    // 0x14d3540: ldur            x0, [fp, #-8]
    // 0x14d3544: stur            x3, [fp, #-0x10]
    // 0x14d3548: StoreField: r3->field_b = r0
    //     0x14d3548: stur            w0, [x3, #0xb]
    // 0x14d354c: r1 = Function '<anonymous closure>':.
    //     0x14d354c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a20] AnonymousClosure: (0x137d5f4), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14d3550: ldr             x1, [x1, #0xa20]
    // 0x14d3554: r2 = Null
    //     0x14d3554: mov             x2, NULL
    // 0x14d3558: r0 = AllocateClosure()
    //     0x14d3558: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d355c: mov             x1, x0
    // 0x14d3560: ldur            x0, [fp, #-0x10]
    // 0x14d3564: StoreField: r0->field_f = r1
    //     0x14d3564: stur            w1, [x0, #0xf]
    // 0x14d3568: r1 = true
    //     0x14d3568: add             x1, NULL, #0x20  ; true
    // 0x14d356c: StoreField: r0->field_43 = r1
    //     0x14d356c: stur            w1, [x0, #0x43]
    // 0x14d3570: r2 = Instance_BoxShape
    //     0x14d3570: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d3574: ldr             x2, [x2, #0x80]
    // 0x14d3578: StoreField: r0->field_47 = r2
    //     0x14d3578: stur            w2, [x0, #0x47]
    // 0x14d357c: StoreField: r0->field_6f = r1
    //     0x14d357c: stur            w1, [x0, #0x6f]
    // 0x14d3580: r2 = false
    //     0x14d3580: add             x2, NULL, #0x30  ; false
    // 0x14d3584: StoreField: r0->field_73 = r2
    //     0x14d3584: stur            w2, [x0, #0x73]
    // 0x14d3588: StoreField: r0->field_83 = r1
    //     0x14d3588: stur            w1, [x0, #0x83]
    // 0x14d358c: StoreField: r0->field_7b = r2
    //     0x14d358c: stur            w2, [x0, #0x7b]
    // 0x14d3590: r0 = Padding()
    //     0x14d3590: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14d3594: r1 = Instance_EdgeInsets
    //     0x14d3594: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0x14d3598: ldr             x1, [x1, #0x560]
    // 0x14d359c: StoreField: r0->field_f = r1
    //     0x14d359c: stur            w1, [x0, #0xf]
    // 0x14d35a0: ldur            x1, [fp, #-0x10]
    // 0x14d35a4: StoreField: r0->field_b = r1
    //     0x14d35a4: stur            w1, [x0, #0xb]
    // 0x14d35a8: LeaveFrame
    //     0x14d35a8: mov             SP, fp
    //     0x14d35ac: ldp             fp, lr, [SP], #0x10
    // 0x14d35b0: ret
    //     0x14d35b0: ret             
    // 0x14d35b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d35b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d35b8: b               #0x14d3078
    // 0x14d35bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d35bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d35c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d35c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d35c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d35c4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d35c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d35c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14d35d8, size: 0x908
    // 0x14d35d8: EnterFrame
    //     0x14d35d8: stp             fp, lr, [SP, #-0x10]!
    //     0x14d35dc: mov             fp, SP
    // 0x14d35e0: AllocStack(0x68)
    //     0x14d35e0: sub             SP, SP, #0x68
    // 0x14d35e4: SetupParameters()
    //     0x14d35e4: ldr             x0, [fp, #0x20]
    //     0x14d35e8: ldur            w1, [x0, #0x17]
    //     0x14d35ec: add             x1, x1, HEAP, lsl #32
    //     0x14d35f0: stur            x1, [fp, #-8]
    // 0x14d35f4: CheckStackOverflow
    //     0x14d35f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d35f8: cmp             SP, x16
    //     0x14d35fc: b.ls            #0x14d3eb0
    // 0x14d3600: r1 = 1
    //     0x14d3600: movz            x1, #0x1
    // 0x14d3604: r0 = AllocateContext()
    //     0x14d3604: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d3608: mov             x2, x0
    // 0x14d360c: ldur            x0, [fp, #-8]
    // 0x14d3610: stur            x2, [fp, #-0x18]
    // 0x14d3614: StoreField: r2->field_b = r0
    //     0x14d3614: stur            w0, [x2, #0xb]
    // 0x14d3618: ldr             x1, [fp, #0x10]
    // 0x14d361c: StoreField: r2->field_f = r1
    //     0x14d361c: stur            w1, [x2, #0xf]
    // 0x14d3620: LoadField: r3 = r0->field_b
    //     0x14d3620: ldur            w3, [x0, #0xb]
    // 0x14d3624: DecompressPointer r3
    //     0x14d3624: add             x3, x3, HEAP, lsl #32
    // 0x14d3628: stur            x3, [fp, #-0x10]
    // 0x14d362c: LoadField: r1 = r3->field_f
    //     0x14d362c: ldur            w1, [x3, #0xf]
    // 0x14d3630: DecompressPointer r1
    //     0x14d3630: add             x1, x1, HEAP, lsl #32
    // 0x14d3634: r0 = controller()
    //     0x14d3634: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d3638: LoadField: r1 = r0->field_4b
    //     0x14d3638: ldur            w1, [x0, #0x4b]
    // 0x14d363c: DecompressPointer r1
    //     0x14d363c: add             x1, x1, HEAP, lsl #32
    // 0x14d3640: r0 = value()
    //     0x14d3640: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3644: LoadField: r2 = r0->field_63
    //     0x14d3644: ldur            w2, [x0, #0x63]
    // 0x14d3648: DecompressPointer r2
    //     0x14d3648: add             x2, x2, HEAP, lsl #32
    // 0x14d364c: cmp             w2, NULL
    // 0x14d3650: b.ne            #0x14d3664
    // 0x14d3654: ldur            x3, [fp, #-8]
    // 0x14d3658: ldur            x4, [fp, #-0x18]
    // 0x14d365c: r0 = Null
    //     0x14d365c: mov             x0, NULL
    // 0x14d3660: b               #0x14d3750
    // 0x14d3664: ldur            x3, [fp, #-8]
    // 0x14d3668: LoadField: r0 = r3->field_f
    //     0x14d3668: ldur            w0, [x3, #0xf]
    // 0x14d366c: DecompressPointer r0
    //     0x14d366c: add             x0, x0, HEAP, lsl #32
    // 0x14d3670: LoadField: r1 = r2->field_b
    //     0x14d3670: ldur            w1, [x2, #0xb]
    // 0x14d3674: r4 = LoadInt32Instr(r0)
    //     0x14d3674: sbfx            x4, x0, #1, #0x1f
    //     0x14d3678: tbz             w0, #0, #0x14d3680
    //     0x14d367c: ldur            x4, [x0, #7]
    // 0x14d3680: r0 = LoadInt32Instr(r1)
    //     0x14d3680: sbfx            x0, x1, #1, #0x1f
    // 0x14d3684: mov             x1, x4
    // 0x14d3688: cmp             x1, x0
    // 0x14d368c: b.hs            #0x14d3eb8
    // 0x14d3690: LoadField: r0 = r2->field_f
    //     0x14d3690: ldur            w0, [x2, #0xf]
    // 0x14d3694: DecompressPointer r0
    //     0x14d3694: add             x0, x0, HEAP, lsl #32
    // 0x14d3698: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d3698: add             x16, x0, x4, lsl #2
    //     0x14d369c: ldur            w1, [x16, #0xf]
    // 0x14d36a0: DecompressPointer r1
    //     0x14d36a0: add             x1, x1, HEAP, lsl #32
    // 0x14d36a4: cmp             w1, NULL
    // 0x14d36a8: b.ne            #0x14d36b8
    // 0x14d36ac: ldur            x4, [fp, #-0x18]
    // 0x14d36b0: r0 = Null
    //     0x14d36b0: mov             x0, NULL
    // 0x14d36b4: b               #0x14d3750
    // 0x14d36b8: LoadField: r2 = r1->field_1f
    //     0x14d36b8: ldur            w2, [x1, #0x1f]
    // 0x14d36bc: DecompressPointer r2
    //     0x14d36bc: add             x2, x2, HEAP, lsl #32
    // 0x14d36c0: cmp             w2, NULL
    // 0x14d36c4: b.ne            #0x14d36d4
    // 0x14d36c8: ldur            x4, [fp, #-0x18]
    // 0x14d36cc: r0 = Null
    //     0x14d36cc: mov             x0, NULL
    // 0x14d36d0: b               #0x14d3750
    // 0x14d36d4: ldur            x4, [fp, #-0x18]
    // 0x14d36d8: LoadField: r0 = r4->field_f
    //     0x14d36d8: ldur            w0, [x4, #0xf]
    // 0x14d36dc: DecompressPointer r0
    //     0x14d36dc: add             x0, x0, HEAP, lsl #32
    // 0x14d36e0: LoadField: r1 = r2->field_b
    //     0x14d36e0: ldur            w1, [x2, #0xb]
    // 0x14d36e4: r5 = LoadInt32Instr(r0)
    //     0x14d36e4: sbfx            x5, x0, #1, #0x1f
    //     0x14d36e8: tbz             w0, #0, #0x14d36f0
    //     0x14d36ec: ldur            x5, [x0, #7]
    // 0x14d36f0: r0 = LoadInt32Instr(r1)
    //     0x14d36f0: sbfx            x0, x1, #1, #0x1f
    // 0x14d36f4: mov             x1, x5
    // 0x14d36f8: cmp             x1, x0
    // 0x14d36fc: b.hs            #0x14d3ebc
    // 0x14d3700: LoadField: r0 = r2->field_f
    //     0x14d3700: ldur            w0, [x2, #0xf]
    // 0x14d3704: DecompressPointer r0
    //     0x14d3704: add             x0, x0, HEAP, lsl #32
    // 0x14d3708: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x14d3708: add             x16, x0, x5, lsl #2
    //     0x14d370c: ldur            w1, [x16, #0xf]
    // 0x14d3710: DecompressPointer r1
    //     0x14d3710: add             x1, x1, HEAP, lsl #32
    // 0x14d3714: cmp             w1, NULL
    // 0x14d3718: b.ne            #0x14d3724
    // 0x14d371c: r0 = Null
    //     0x14d371c: mov             x0, NULL
    // 0x14d3720: b               #0x14d3750
    // 0x14d3724: LoadField: r0 = r1->field_f
    //     0x14d3724: ldur            w0, [x1, #0xf]
    // 0x14d3728: DecompressPointer r0
    //     0x14d3728: add             x0, x0, HEAP, lsl #32
    // 0x14d372c: cmp             w0, NULL
    // 0x14d3730: b.ne            #0x14d373c
    // 0x14d3734: r0 = Null
    //     0x14d3734: mov             x0, NULL
    // 0x14d3738: b               #0x14d3750
    // 0x14d373c: LoadField: r1 = r0->field_7
    //     0x14d373c: ldur            w1, [x0, #7]
    // 0x14d3740: cbnz            w1, #0x14d374c
    // 0x14d3744: r0 = false
    //     0x14d3744: add             x0, NULL, #0x30  ; false
    // 0x14d3748: b               #0x14d3750
    // 0x14d374c: r0 = true
    //     0x14d374c: add             x0, NULL, #0x20  ; true
    // 0x14d3750: cmp             w0, NULL
    // 0x14d3754: b.ne            #0x14d3764
    // 0x14d3758: r0 = Instance_BoxShape
    //     0x14d3758: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d375c: ldr             x0, [x0, #0x80]
    // 0x14d3760: b               #0x14d37f0
    // 0x14d3764: tbnz            w0, #4, #0x14d37e8
    // 0x14d3768: r1 = Instance_Color
    //     0x14d3768: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d376c: d0 = 0.100000
    //     0x14d376c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x14d3770: r0 = withOpacity()
    //     0x14d3770: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14d3774: mov             x2, x0
    // 0x14d3778: r1 = Null
    //     0x14d3778: mov             x1, NULL
    // 0x14d377c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x14d377c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x14d3780: r0 = Border.all()
    //     0x14d3780: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x14d3784: stur            x0, [fp, #-0x20]
    // 0x14d3788: r0 = Radius()
    //     0x14d3788: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14d378c: d0 = 15.000000
    //     0x14d378c: fmov            d0, #15.00000000
    // 0x14d3790: stur            x0, [fp, #-0x28]
    // 0x14d3794: StoreField: r0->field_7 = d0
    //     0x14d3794: stur            d0, [x0, #7]
    // 0x14d3798: StoreField: r0->field_f = d0
    //     0x14d3798: stur            d0, [x0, #0xf]
    // 0x14d379c: r0 = BorderRadius()
    //     0x14d379c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14d37a0: mov             x1, x0
    // 0x14d37a4: ldur            x0, [fp, #-0x28]
    // 0x14d37a8: stur            x1, [fp, #-0x30]
    // 0x14d37ac: StoreField: r1->field_7 = r0
    //     0x14d37ac: stur            w0, [x1, #7]
    // 0x14d37b0: StoreField: r1->field_b = r0
    //     0x14d37b0: stur            w0, [x1, #0xb]
    // 0x14d37b4: StoreField: r1->field_f = r0
    //     0x14d37b4: stur            w0, [x1, #0xf]
    // 0x14d37b8: StoreField: r1->field_13 = r0
    //     0x14d37b8: stur            w0, [x1, #0x13]
    // 0x14d37bc: r0 = BoxDecoration()
    //     0x14d37bc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14d37c0: mov             x1, x0
    // 0x14d37c4: ldur            x0, [fp, #-0x20]
    // 0x14d37c8: StoreField: r1->field_f = r0
    //     0x14d37c8: stur            w0, [x1, #0xf]
    // 0x14d37cc: ldur            x0, [fp, #-0x30]
    // 0x14d37d0: StoreField: r1->field_13 = r0
    //     0x14d37d0: stur            w0, [x1, #0x13]
    // 0x14d37d4: r0 = Instance_BoxShape
    //     0x14d37d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d37d8: ldr             x0, [x0, #0x80]
    // 0x14d37dc: StoreField: r1->field_23 = r0
    //     0x14d37dc: stur            w0, [x1, #0x23]
    // 0x14d37e0: mov             x3, x1
    // 0x14d37e4: b               #0x14d37f8
    // 0x14d37e8: r0 = Instance_BoxShape
    //     0x14d37e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d37ec: ldr             x0, [x0, #0x80]
    // 0x14d37f0: r3 = Instance_BoxDecoration
    //     0x14d37f0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e570] Obj!BoxDecoration@d647d1
    //     0x14d37f4: ldr             x3, [x3, #0x570]
    // 0x14d37f8: ldur            x2, [fp, #-0x10]
    // 0x14d37fc: stur            x3, [fp, #-0x20]
    // 0x14d3800: LoadField: r1 = r2->field_f
    //     0x14d3800: ldur            w1, [x2, #0xf]
    // 0x14d3804: DecompressPointer r1
    //     0x14d3804: add             x1, x1, HEAP, lsl #32
    // 0x14d3808: r0 = controller()
    //     0x14d3808: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d380c: LoadField: r1 = r0->field_4b
    //     0x14d380c: ldur            w1, [x0, #0x4b]
    // 0x14d3810: DecompressPointer r1
    //     0x14d3810: add             x1, x1, HEAP, lsl #32
    // 0x14d3814: r0 = value()
    //     0x14d3814: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3818: LoadField: r2 = r0->field_63
    //     0x14d3818: ldur            w2, [x0, #0x63]
    // 0x14d381c: DecompressPointer r2
    //     0x14d381c: add             x2, x2, HEAP, lsl #32
    // 0x14d3820: cmp             w2, NULL
    // 0x14d3824: b.ne            #0x14d3838
    // 0x14d3828: ldur            x3, [fp, #-8]
    // 0x14d382c: ldur            x4, [fp, #-0x18]
    // 0x14d3830: r0 = Null
    //     0x14d3830: mov             x0, NULL
    // 0x14d3834: b               #0x14d3924
    // 0x14d3838: ldur            x3, [fp, #-8]
    // 0x14d383c: LoadField: r0 = r3->field_f
    //     0x14d383c: ldur            w0, [x3, #0xf]
    // 0x14d3840: DecompressPointer r0
    //     0x14d3840: add             x0, x0, HEAP, lsl #32
    // 0x14d3844: LoadField: r1 = r2->field_b
    //     0x14d3844: ldur            w1, [x2, #0xb]
    // 0x14d3848: r4 = LoadInt32Instr(r0)
    //     0x14d3848: sbfx            x4, x0, #1, #0x1f
    //     0x14d384c: tbz             w0, #0, #0x14d3854
    //     0x14d3850: ldur            x4, [x0, #7]
    // 0x14d3854: r0 = LoadInt32Instr(r1)
    //     0x14d3854: sbfx            x0, x1, #1, #0x1f
    // 0x14d3858: mov             x1, x4
    // 0x14d385c: cmp             x1, x0
    // 0x14d3860: b.hs            #0x14d3ec0
    // 0x14d3864: LoadField: r0 = r2->field_f
    //     0x14d3864: ldur            w0, [x2, #0xf]
    // 0x14d3868: DecompressPointer r0
    //     0x14d3868: add             x0, x0, HEAP, lsl #32
    // 0x14d386c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d386c: add             x16, x0, x4, lsl #2
    //     0x14d3870: ldur            w1, [x16, #0xf]
    // 0x14d3874: DecompressPointer r1
    //     0x14d3874: add             x1, x1, HEAP, lsl #32
    // 0x14d3878: cmp             w1, NULL
    // 0x14d387c: b.ne            #0x14d388c
    // 0x14d3880: ldur            x4, [fp, #-0x18]
    // 0x14d3884: r0 = Null
    //     0x14d3884: mov             x0, NULL
    // 0x14d3888: b               #0x14d3924
    // 0x14d388c: LoadField: r2 = r1->field_1f
    //     0x14d388c: ldur            w2, [x1, #0x1f]
    // 0x14d3890: DecompressPointer r2
    //     0x14d3890: add             x2, x2, HEAP, lsl #32
    // 0x14d3894: cmp             w2, NULL
    // 0x14d3898: b.ne            #0x14d38a8
    // 0x14d389c: ldur            x4, [fp, #-0x18]
    // 0x14d38a0: r0 = Null
    //     0x14d38a0: mov             x0, NULL
    // 0x14d38a4: b               #0x14d3924
    // 0x14d38a8: ldur            x4, [fp, #-0x18]
    // 0x14d38ac: LoadField: r0 = r4->field_f
    //     0x14d38ac: ldur            w0, [x4, #0xf]
    // 0x14d38b0: DecompressPointer r0
    //     0x14d38b0: add             x0, x0, HEAP, lsl #32
    // 0x14d38b4: LoadField: r1 = r2->field_b
    //     0x14d38b4: ldur            w1, [x2, #0xb]
    // 0x14d38b8: r5 = LoadInt32Instr(r0)
    //     0x14d38b8: sbfx            x5, x0, #1, #0x1f
    //     0x14d38bc: tbz             w0, #0, #0x14d38c4
    //     0x14d38c0: ldur            x5, [x0, #7]
    // 0x14d38c4: r0 = LoadInt32Instr(r1)
    //     0x14d38c4: sbfx            x0, x1, #1, #0x1f
    // 0x14d38c8: mov             x1, x5
    // 0x14d38cc: cmp             x1, x0
    // 0x14d38d0: b.hs            #0x14d3ec4
    // 0x14d38d4: LoadField: r0 = r2->field_f
    //     0x14d38d4: ldur            w0, [x2, #0xf]
    // 0x14d38d8: DecompressPointer r0
    //     0x14d38d8: add             x0, x0, HEAP, lsl #32
    // 0x14d38dc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x14d38dc: add             x16, x0, x5, lsl #2
    //     0x14d38e0: ldur            w1, [x16, #0xf]
    // 0x14d38e4: DecompressPointer r1
    //     0x14d38e4: add             x1, x1, HEAP, lsl #32
    // 0x14d38e8: cmp             w1, NULL
    // 0x14d38ec: b.ne            #0x14d38f8
    // 0x14d38f0: r0 = Null
    //     0x14d38f0: mov             x0, NULL
    // 0x14d38f4: b               #0x14d3924
    // 0x14d38f8: LoadField: r0 = r1->field_1f
    //     0x14d38f8: ldur            w0, [x1, #0x1f]
    // 0x14d38fc: DecompressPointer r0
    //     0x14d38fc: add             x0, x0, HEAP, lsl #32
    // 0x14d3900: cmp             w0, NULL
    // 0x14d3904: b.ne            #0x14d3910
    // 0x14d3908: r0 = Null
    //     0x14d3908: mov             x0, NULL
    // 0x14d390c: b               #0x14d3924
    // 0x14d3910: LoadField: r1 = r0->field_b
    //     0x14d3910: ldur            w1, [x0, #0xb]
    // 0x14d3914: cbnz            w1, #0x14d3920
    // 0x14d3918: r0 = false
    //     0x14d3918: add             x0, NULL, #0x30  ; false
    // 0x14d391c: b               #0x14d3924
    // 0x14d3920: r0 = true
    //     0x14d3920: add             x0, NULL, #0x20  ; true
    // 0x14d3924: cmp             w0, NULL
    // 0x14d3928: b.eq            #0x14d393c
    // 0x14d392c: tbnz            w0, #4, #0x14d393c
    // 0x14d3930: r2 = Instance_Icon
    //     0x14d3930: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e578] Obj!Icon@d668f1
    //     0x14d3934: ldr             x2, [x2, #0x578]
    // 0x14d3938: b               #0x14d3954
    // 0x14d393c: r0 = Container()
    //     0x14d393c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d3940: mov             x1, x0
    // 0x14d3944: stur            x0, [fp, #-0x28]
    // 0x14d3948: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d3948: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d394c: r0 = Container()
    //     0x14d394c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d3950: ldur            x2, [fp, #-0x28]
    // 0x14d3954: ldur            x0, [fp, #-0x10]
    // 0x14d3958: stur            x2, [fp, #-0x28]
    // 0x14d395c: LoadField: r1 = r0->field_f
    //     0x14d395c: ldur            w1, [x0, #0xf]
    // 0x14d3960: DecompressPointer r1
    //     0x14d3960: add             x1, x1, HEAP, lsl #32
    // 0x14d3964: r0 = controller()
    //     0x14d3964: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d3968: LoadField: r1 = r0->field_4b
    //     0x14d3968: ldur            w1, [x0, #0x4b]
    // 0x14d396c: DecompressPointer r1
    //     0x14d396c: add             x1, x1, HEAP, lsl #32
    // 0x14d3970: r0 = value()
    //     0x14d3970: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3974: LoadField: r2 = r0->field_63
    //     0x14d3974: ldur            w2, [x0, #0x63]
    // 0x14d3978: DecompressPointer r2
    //     0x14d3978: add             x2, x2, HEAP, lsl #32
    // 0x14d397c: cmp             w2, NULL
    // 0x14d3980: b.ne            #0x14d3994
    // 0x14d3984: ldur            x3, [fp, #-8]
    // 0x14d3988: ldur            x4, [fp, #-0x18]
    // 0x14d398c: r0 = Null
    //     0x14d398c: mov             x0, NULL
    // 0x14d3990: b               #0x14d3a80
    // 0x14d3994: ldur            x3, [fp, #-8]
    // 0x14d3998: LoadField: r0 = r3->field_f
    //     0x14d3998: ldur            w0, [x3, #0xf]
    // 0x14d399c: DecompressPointer r0
    //     0x14d399c: add             x0, x0, HEAP, lsl #32
    // 0x14d39a0: LoadField: r1 = r2->field_b
    //     0x14d39a0: ldur            w1, [x2, #0xb]
    // 0x14d39a4: r4 = LoadInt32Instr(r0)
    //     0x14d39a4: sbfx            x4, x0, #1, #0x1f
    //     0x14d39a8: tbz             w0, #0, #0x14d39b0
    //     0x14d39ac: ldur            x4, [x0, #7]
    // 0x14d39b0: r0 = LoadInt32Instr(r1)
    //     0x14d39b0: sbfx            x0, x1, #1, #0x1f
    // 0x14d39b4: mov             x1, x4
    // 0x14d39b8: cmp             x1, x0
    // 0x14d39bc: b.hs            #0x14d3ec8
    // 0x14d39c0: LoadField: r0 = r2->field_f
    //     0x14d39c0: ldur            w0, [x2, #0xf]
    // 0x14d39c4: DecompressPointer r0
    //     0x14d39c4: add             x0, x0, HEAP, lsl #32
    // 0x14d39c8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d39c8: add             x16, x0, x4, lsl #2
    //     0x14d39cc: ldur            w1, [x16, #0xf]
    // 0x14d39d0: DecompressPointer r1
    //     0x14d39d0: add             x1, x1, HEAP, lsl #32
    // 0x14d39d4: cmp             w1, NULL
    // 0x14d39d8: b.ne            #0x14d39e8
    // 0x14d39dc: ldur            x4, [fp, #-0x18]
    // 0x14d39e0: r0 = Null
    //     0x14d39e0: mov             x0, NULL
    // 0x14d39e4: b               #0x14d3a80
    // 0x14d39e8: LoadField: r2 = r1->field_1f
    //     0x14d39e8: ldur            w2, [x1, #0x1f]
    // 0x14d39ec: DecompressPointer r2
    //     0x14d39ec: add             x2, x2, HEAP, lsl #32
    // 0x14d39f0: cmp             w2, NULL
    // 0x14d39f4: b.ne            #0x14d3a04
    // 0x14d39f8: ldur            x4, [fp, #-0x18]
    // 0x14d39fc: r0 = Null
    //     0x14d39fc: mov             x0, NULL
    // 0x14d3a00: b               #0x14d3a80
    // 0x14d3a04: ldur            x4, [fp, #-0x18]
    // 0x14d3a08: LoadField: r0 = r4->field_f
    //     0x14d3a08: ldur            w0, [x4, #0xf]
    // 0x14d3a0c: DecompressPointer r0
    //     0x14d3a0c: add             x0, x0, HEAP, lsl #32
    // 0x14d3a10: LoadField: r1 = r2->field_b
    //     0x14d3a10: ldur            w1, [x2, #0xb]
    // 0x14d3a14: r5 = LoadInt32Instr(r0)
    //     0x14d3a14: sbfx            x5, x0, #1, #0x1f
    //     0x14d3a18: tbz             w0, #0, #0x14d3a20
    //     0x14d3a1c: ldur            x5, [x0, #7]
    // 0x14d3a20: r0 = LoadInt32Instr(r1)
    //     0x14d3a20: sbfx            x0, x1, #1, #0x1f
    // 0x14d3a24: mov             x1, x5
    // 0x14d3a28: cmp             x1, x0
    // 0x14d3a2c: b.hs            #0x14d3ecc
    // 0x14d3a30: LoadField: r0 = r2->field_f
    //     0x14d3a30: ldur            w0, [x2, #0xf]
    // 0x14d3a34: DecompressPointer r0
    //     0x14d3a34: add             x0, x0, HEAP, lsl #32
    // 0x14d3a38: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x14d3a38: add             x16, x0, x5, lsl #2
    //     0x14d3a3c: ldur            w1, [x16, #0xf]
    // 0x14d3a40: DecompressPointer r1
    //     0x14d3a40: add             x1, x1, HEAP, lsl #32
    // 0x14d3a44: cmp             w1, NULL
    // 0x14d3a48: b.ne            #0x14d3a54
    // 0x14d3a4c: r0 = Null
    //     0x14d3a4c: mov             x0, NULL
    // 0x14d3a50: b               #0x14d3a80
    // 0x14d3a54: LoadField: r0 = r1->field_1f
    //     0x14d3a54: ldur            w0, [x1, #0x1f]
    // 0x14d3a58: DecompressPointer r0
    //     0x14d3a58: add             x0, x0, HEAP, lsl #32
    // 0x14d3a5c: cmp             w0, NULL
    // 0x14d3a60: b.ne            #0x14d3a6c
    // 0x14d3a64: r0 = Null
    //     0x14d3a64: mov             x0, NULL
    // 0x14d3a68: b               #0x14d3a80
    // 0x14d3a6c: LoadField: r1 = r0->field_b
    //     0x14d3a6c: ldur            w1, [x0, #0xb]
    // 0x14d3a70: cbnz            w1, #0x14d3a7c
    // 0x14d3a74: r0 = false
    //     0x14d3a74: add             x0, NULL, #0x30  ; false
    // 0x14d3a78: b               #0x14d3a80
    // 0x14d3a7c: r0 = true
    //     0x14d3a7c: add             x0, NULL, #0x20  ; true
    // 0x14d3a80: cmp             w0, NULL
    // 0x14d3a84: b.eq            #0x14d3a98
    // 0x14d3a88: tbnz            w0, #4, #0x14d3a98
    // 0x14d3a8c: r2 = Instance_Icon
    //     0x14d3a8c: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e580] Obj!Icon@d668b1
    //     0x14d3a90: ldr             x2, [x2, #0x580]
    // 0x14d3a94: b               #0x14d3ab0
    // 0x14d3a98: r0 = Container()
    //     0x14d3a98: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d3a9c: mov             x1, x0
    // 0x14d3aa0: stur            x0, [fp, #-0x30]
    // 0x14d3aa4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d3aa4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d3aa8: r0 = Container()
    //     0x14d3aa8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d3aac: ldur            x2, [fp, #-0x30]
    // 0x14d3ab0: ldur            x0, [fp, #-0x10]
    // 0x14d3ab4: ldr             x1, [fp, #0x18]
    // 0x14d3ab8: stur            x2, [fp, #-0x30]
    // 0x14d3abc: r0 = of()
    //     0x14d3abc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d3ac0: ldr             x1, [fp, #0x18]
    // 0x14d3ac4: r0 = of()
    //     0x14d3ac4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d3ac8: ldur            x0, [fp, #-0x10]
    // 0x14d3acc: LoadField: r1 = r0->field_f
    //     0x14d3acc: ldur            w1, [x0, #0xf]
    // 0x14d3ad0: DecompressPointer r1
    //     0x14d3ad0: add             x1, x1, HEAP, lsl #32
    // 0x14d3ad4: r0 = controller()
    //     0x14d3ad4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d3ad8: LoadField: r1 = r0->field_4b
    //     0x14d3ad8: ldur            w1, [x0, #0x4b]
    // 0x14d3adc: DecompressPointer r1
    //     0x14d3adc: add             x1, x1, HEAP, lsl #32
    // 0x14d3ae0: r0 = value()
    //     0x14d3ae0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3ae4: LoadField: r2 = r0->field_63
    //     0x14d3ae4: ldur            w2, [x0, #0x63]
    // 0x14d3ae8: DecompressPointer r2
    //     0x14d3ae8: add             x2, x2, HEAP, lsl #32
    // 0x14d3aec: cmp             w2, NULL
    // 0x14d3af0: b.ne            #0x14d3b04
    // 0x14d3af4: ldur            x3, [fp, #-8]
    // 0x14d3af8: ldur            x4, [fp, #-0x18]
    // 0x14d3afc: r0 = Null
    //     0x14d3afc: mov             x0, NULL
    // 0x14d3b00: b               #0x14d3bcc
    // 0x14d3b04: ldur            x3, [fp, #-8]
    // 0x14d3b08: LoadField: r0 = r3->field_f
    //     0x14d3b08: ldur            w0, [x3, #0xf]
    // 0x14d3b0c: DecompressPointer r0
    //     0x14d3b0c: add             x0, x0, HEAP, lsl #32
    // 0x14d3b10: LoadField: r1 = r2->field_b
    //     0x14d3b10: ldur            w1, [x2, #0xb]
    // 0x14d3b14: r4 = LoadInt32Instr(r0)
    //     0x14d3b14: sbfx            x4, x0, #1, #0x1f
    //     0x14d3b18: tbz             w0, #0, #0x14d3b20
    //     0x14d3b1c: ldur            x4, [x0, #7]
    // 0x14d3b20: r0 = LoadInt32Instr(r1)
    //     0x14d3b20: sbfx            x0, x1, #1, #0x1f
    // 0x14d3b24: mov             x1, x4
    // 0x14d3b28: cmp             x1, x0
    // 0x14d3b2c: b.hs            #0x14d3ed0
    // 0x14d3b30: LoadField: r0 = r2->field_f
    //     0x14d3b30: ldur            w0, [x2, #0xf]
    // 0x14d3b34: DecompressPointer r0
    //     0x14d3b34: add             x0, x0, HEAP, lsl #32
    // 0x14d3b38: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d3b38: add             x16, x0, x4, lsl #2
    //     0x14d3b3c: ldur            w1, [x16, #0xf]
    // 0x14d3b40: DecompressPointer r1
    //     0x14d3b40: add             x1, x1, HEAP, lsl #32
    // 0x14d3b44: cmp             w1, NULL
    // 0x14d3b48: b.ne            #0x14d3b58
    // 0x14d3b4c: ldur            x4, [fp, #-0x18]
    // 0x14d3b50: r0 = Null
    //     0x14d3b50: mov             x0, NULL
    // 0x14d3b54: b               #0x14d3bcc
    // 0x14d3b58: LoadField: r2 = r1->field_1f
    //     0x14d3b58: ldur            w2, [x1, #0x1f]
    // 0x14d3b5c: DecompressPointer r2
    //     0x14d3b5c: add             x2, x2, HEAP, lsl #32
    // 0x14d3b60: cmp             w2, NULL
    // 0x14d3b64: b.ne            #0x14d3b74
    // 0x14d3b68: ldur            x4, [fp, #-0x18]
    // 0x14d3b6c: r0 = Null
    //     0x14d3b6c: mov             x0, NULL
    // 0x14d3b70: b               #0x14d3bcc
    // 0x14d3b74: ldur            x4, [fp, #-0x18]
    // 0x14d3b78: LoadField: r0 = r4->field_f
    //     0x14d3b78: ldur            w0, [x4, #0xf]
    // 0x14d3b7c: DecompressPointer r0
    //     0x14d3b7c: add             x0, x0, HEAP, lsl #32
    // 0x14d3b80: LoadField: r1 = r2->field_b
    //     0x14d3b80: ldur            w1, [x2, #0xb]
    // 0x14d3b84: r5 = LoadInt32Instr(r0)
    //     0x14d3b84: sbfx            x5, x0, #1, #0x1f
    //     0x14d3b88: tbz             w0, #0, #0x14d3b90
    //     0x14d3b8c: ldur            x5, [x0, #7]
    // 0x14d3b90: r0 = LoadInt32Instr(r1)
    //     0x14d3b90: sbfx            x0, x1, #1, #0x1f
    // 0x14d3b94: mov             x1, x5
    // 0x14d3b98: cmp             x1, x0
    // 0x14d3b9c: b.hs            #0x14d3ed4
    // 0x14d3ba0: LoadField: r0 = r2->field_f
    //     0x14d3ba0: ldur            w0, [x2, #0xf]
    // 0x14d3ba4: DecompressPointer r0
    //     0x14d3ba4: add             x0, x0, HEAP, lsl #32
    // 0x14d3ba8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x14d3ba8: add             x16, x0, x5, lsl #2
    //     0x14d3bac: ldur            w1, [x16, #0xf]
    // 0x14d3bb0: DecompressPointer r1
    //     0x14d3bb0: add             x1, x1, HEAP, lsl #32
    // 0x14d3bb4: cmp             w1, NULL
    // 0x14d3bb8: b.ne            #0x14d3bc4
    // 0x14d3bbc: r0 = Null
    //     0x14d3bbc: mov             x0, NULL
    // 0x14d3bc0: b               #0x14d3bcc
    // 0x14d3bc4: LoadField: r0 = r1->field_f
    //     0x14d3bc4: ldur            w0, [x1, #0xf]
    // 0x14d3bc8: DecompressPointer r0
    //     0x14d3bc8: add             x0, x0, HEAP, lsl #32
    // 0x14d3bcc: cmp             w0, NULL
    // 0x14d3bd0: b.ne            #0x14d3bdc
    // 0x14d3bd4: r2 = ""
    //     0x14d3bd4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d3bd8: b               #0x14d3be0
    // 0x14d3bdc: mov             x2, x0
    // 0x14d3be0: ldur            x0, [fp, #-0x10]
    // 0x14d3be4: ldr             x1, [fp, #0x18]
    // 0x14d3be8: stur            x2, [fp, #-0x38]
    // 0x14d3bec: r0 = of()
    //     0x14d3bec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d3bf0: LoadField: r1 = r0->field_87
    //     0x14d3bf0: ldur            w1, [x0, #0x87]
    // 0x14d3bf4: DecompressPointer r1
    //     0x14d3bf4: add             x1, x1, HEAP, lsl #32
    // 0x14d3bf8: LoadField: r0 = r1->field_7
    //     0x14d3bf8: ldur            w0, [x1, #7]
    // 0x14d3bfc: DecompressPointer r0
    //     0x14d3bfc: add             x0, x0, HEAP, lsl #32
    // 0x14d3c00: r16 = Instance_Color
    //     0x14d3c00: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d3c04: r30 = 14.000000
    //     0x14d3c04: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d3c08: ldr             lr, [lr, #0x1d8]
    // 0x14d3c0c: stp             lr, x16, [SP]
    // 0x14d3c10: mov             x1, x0
    // 0x14d3c14: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d3c14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d3c18: ldr             x4, [x4, #0x9b8]
    // 0x14d3c1c: r0 = copyWith()
    //     0x14d3c1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d3c20: stur            x0, [fp, #-0x40]
    // 0x14d3c24: r0 = Text()
    //     0x14d3c24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d3c28: mov             x1, x0
    // 0x14d3c2c: ldur            x0, [fp, #-0x38]
    // 0x14d3c30: stur            x1, [fp, #-0x48]
    // 0x14d3c34: StoreField: r1->field_b = r0
    //     0x14d3c34: stur            w0, [x1, #0xb]
    // 0x14d3c38: ldur            x0, [fp, #-0x40]
    // 0x14d3c3c: StoreField: r1->field_13 = r0
    //     0x14d3c3c: stur            w0, [x1, #0x13]
    // 0x14d3c40: r0 = InkWell()
    //     0x14d3c40: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d3c44: mov             x3, x0
    // 0x14d3c48: ldur            x0, [fp, #-0x48]
    // 0x14d3c4c: stur            x3, [fp, #-0x38]
    // 0x14d3c50: StoreField: r3->field_b = r0
    //     0x14d3c50: stur            w0, [x3, #0xb]
    // 0x14d3c54: ldur            x2, [fp, #-0x18]
    // 0x14d3c58: r1 = Function '<anonymous closure>':.
    //     0x14d3c58: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a28] AnonymousClosure: (0x137e628), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14d3c5c: ldr             x1, [x1, #0xa28]
    // 0x14d3c60: r0 = AllocateClosure()
    //     0x14d3c60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d3c64: mov             x1, x0
    // 0x14d3c68: ldur            x0, [fp, #-0x38]
    // 0x14d3c6c: StoreField: r0->field_f = r1
    //     0x14d3c6c: stur            w1, [x0, #0xf]
    // 0x14d3c70: r2 = true
    //     0x14d3c70: add             x2, NULL, #0x20  ; true
    // 0x14d3c74: StoreField: r0->field_43 = r2
    //     0x14d3c74: stur            w2, [x0, #0x43]
    // 0x14d3c78: r1 = Instance_BoxShape
    //     0x14d3c78: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d3c7c: ldr             x1, [x1, #0x80]
    // 0x14d3c80: StoreField: r0->field_47 = r1
    //     0x14d3c80: stur            w1, [x0, #0x47]
    // 0x14d3c84: StoreField: r0->field_6f = r2
    //     0x14d3c84: stur            w2, [x0, #0x6f]
    // 0x14d3c88: r1 = false
    //     0x14d3c88: add             x1, NULL, #0x30  ; false
    // 0x14d3c8c: StoreField: r0->field_73 = r1
    //     0x14d3c8c: stur            w1, [x0, #0x73]
    // 0x14d3c90: StoreField: r0->field_83 = r2
    //     0x14d3c90: stur            w2, [x0, #0x83]
    // 0x14d3c94: StoreField: r0->field_7b = r1
    //     0x14d3c94: stur            w1, [x0, #0x7b]
    // 0x14d3c98: ldur            x1, [fp, #-0x10]
    // 0x14d3c9c: LoadField: r3 = r1->field_f
    //     0x14d3c9c: ldur            w3, [x1, #0xf]
    // 0x14d3ca0: DecompressPointer r3
    //     0x14d3ca0: add             x3, x3, HEAP, lsl #32
    // 0x14d3ca4: mov             x1, x3
    // 0x14d3ca8: r0 = controller()
    //     0x14d3ca8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d3cac: LoadField: r1 = r0->field_4b
    //     0x14d3cac: ldur            w1, [x0, #0x4b]
    // 0x14d3cb0: DecompressPointer r1
    //     0x14d3cb0: add             x1, x1, HEAP, lsl #32
    // 0x14d3cb4: r0 = value()
    //     0x14d3cb4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3cb8: LoadField: r2 = r0->field_63
    //     0x14d3cb8: ldur            w2, [x0, #0x63]
    // 0x14d3cbc: DecompressPointer r2
    //     0x14d3cbc: add             x2, x2, HEAP, lsl #32
    // 0x14d3cc0: cmp             w2, NULL
    // 0x14d3cc4: b.ne            #0x14d3cd4
    // 0x14d3cc8: ldur            x3, [fp, #-0x18]
    // 0x14d3ccc: r0 = Null
    //     0x14d3ccc: mov             x0, NULL
    // 0x14d3cd0: b               #0x14d3db8
    // 0x14d3cd4: ldur            x0, [fp, #-8]
    // 0x14d3cd8: LoadField: r1 = r0->field_f
    //     0x14d3cd8: ldur            w1, [x0, #0xf]
    // 0x14d3cdc: DecompressPointer r1
    //     0x14d3cdc: add             x1, x1, HEAP, lsl #32
    // 0x14d3ce0: LoadField: r0 = r2->field_b
    //     0x14d3ce0: ldur            w0, [x2, #0xb]
    // 0x14d3ce4: r3 = LoadInt32Instr(r1)
    //     0x14d3ce4: sbfx            x3, x1, #1, #0x1f
    //     0x14d3ce8: tbz             w1, #0, #0x14d3cf0
    //     0x14d3cec: ldur            x3, [x1, #7]
    // 0x14d3cf0: r1 = LoadInt32Instr(r0)
    //     0x14d3cf0: sbfx            x1, x0, #1, #0x1f
    // 0x14d3cf4: mov             x0, x1
    // 0x14d3cf8: mov             x1, x3
    // 0x14d3cfc: cmp             x1, x0
    // 0x14d3d00: b.hs            #0x14d3ed8
    // 0x14d3d04: LoadField: r0 = r2->field_f
    //     0x14d3d04: ldur            w0, [x2, #0xf]
    // 0x14d3d08: DecompressPointer r0
    //     0x14d3d08: add             x0, x0, HEAP, lsl #32
    // 0x14d3d0c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14d3d0c: add             x16, x0, x3, lsl #2
    //     0x14d3d10: ldur            w1, [x16, #0xf]
    // 0x14d3d14: DecompressPointer r1
    //     0x14d3d14: add             x1, x1, HEAP, lsl #32
    // 0x14d3d18: cmp             w1, NULL
    // 0x14d3d1c: b.ne            #0x14d3d2c
    // 0x14d3d20: ldur            x3, [fp, #-0x18]
    // 0x14d3d24: r0 = Null
    //     0x14d3d24: mov             x0, NULL
    // 0x14d3d28: b               #0x14d3db8
    // 0x14d3d2c: LoadField: r2 = r1->field_1f
    //     0x14d3d2c: ldur            w2, [x1, #0x1f]
    // 0x14d3d30: DecompressPointer r2
    //     0x14d3d30: add             x2, x2, HEAP, lsl #32
    // 0x14d3d34: cmp             w2, NULL
    // 0x14d3d38: b.ne            #0x14d3d48
    // 0x14d3d3c: ldur            x3, [fp, #-0x18]
    // 0x14d3d40: r0 = Null
    //     0x14d3d40: mov             x0, NULL
    // 0x14d3d44: b               #0x14d3db8
    // 0x14d3d48: ldur            x3, [fp, #-0x18]
    // 0x14d3d4c: LoadField: r0 = r3->field_f
    //     0x14d3d4c: ldur            w0, [x3, #0xf]
    // 0x14d3d50: DecompressPointer r0
    //     0x14d3d50: add             x0, x0, HEAP, lsl #32
    // 0x14d3d54: LoadField: r1 = r2->field_b
    //     0x14d3d54: ldur            w1, [x2, #0xb]
    // 0x14d3d58: r4 = LoadInt32Instr(r0)
    //     0x14d3d58: sbfx            x4, x0, #1, #0x1f
    //     0x14d3d5c: tbz             w0, #0, #0x14d3d64
    //     0x14d3d60: ldur            x4, [x0, #7]
    // 0x14d3d64: r0 = LoadInt32Instr(r1)
    //     0x14d3d64: sbfx            x0, x1, #1, #0x1f
    // 0x14d3d68: mov             x1, x4
    // 0x14d3d6c: cmp             x1, x0
    // 0x14d3d70: b.hs            #0x14d3edc
    // 0x14d3d74: LoadField: r0 = r2->field_f
    //     0x14d3d74: ldur            w0, [x2, #0xf]
    // 0x14d3d78: DecompressPointer r0
    //     0x14d3d78: add             x0, x0, HEAP, lsl #32
    // 0x14d3d7c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x14d3d7c: add             x16, x0, x4, lsl #2
    //     0x14d3d80: ldur            w1, [x16, #0xf]
    // 0x14d3d84: DecompressPointer r1
    //     0x14d3d84: add             x1, x1, HEAP, lsl #32
    // 0x14d3d88: cmp             w1, NULL
    // 0x14d3d8c: b.ne            #0x14d3d98
    // 0x14d3d90: r0 = Null
    //     0x14d3d90: mov             x0, NULL
    // 0x14d3d94: b               #0x14d3db8
    // 0x14d3d98: LoadField: r0 = r1->field_1f
    //     0x14d3d98: ldur            w0, [x1, #0x1f]
    // 0x14d3d9c: DecompressPointer r0
    //     0x14d3d9c: add             x0, x0, HEAP, lsl #32
    // 0x14d3da0: cmp             w0, NULL
    // 0x14d3da4: b.ne            #0x14d3db0
    // 0x14d3da8: r0 = Null
    //     0x14d3da8: mov             x0, NULL
    // 0x14d3dac: b               #0x14d3db8
    // 0x14d3db0: LoadField: r1 = r0->field_b
    //     0x14d3db0: ldur            w1, [x0, #0xb]
    // 0x14d3db4: mov             x0, x1
    // 0x14d3db8: cmp             w0, NULL
    // 0x14d3dbc: b.ne            #0x14d3dc8
    // 0x14d3dc0: r6 = 0
    //     0x14d3dc0: movz            x6, #0
    // 0x14d3dc4: b               #0x14d3dd0
    // 0x14d3dc8: r1 = LoadInt32Instr(r0)
    //     0x14d3dc8: sbfx            x1, x0, #1, #0x1f
    // 0x14d3dcc: mov             x6, x1
    // 0x14d3dd0: ldur            x5, [fp, #-0x28]
    // 0x14d3dd4: ldur            x4, [fp, #-0x30]
    // 0x14d3dd8: ldur            x0, [fp, #-0x38]
    // 0x14d3ddc: mov             x2, x3
    // 0x14d3de0: stur            x6, [fp, #-0x50]
    // 0x14d3de4: r1 = Function '<anonymous closure>':.
    //     0x14d3de4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a30] AnonymousClosure: (0x14d3ee0), in [package:customer_app/app/presentation/views/glass/browse/browse_view.dart] BrowseView::body (0x14d2ef4)
    //     0x14d3de8: ldr             x1, [x1, #0xa30]
    // 0x14d3dec: r0 = AllocateClosure()
    //     0x14d3dec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d3df0: r1 = Function '<anonymous closure>':.
    //     0x14d3df0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a38] AnonymousClosure: (0x137df24), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14d3df4: ldr             x1, [x1, #0xa38]
    // 0x14d3df8: r2 = Null
    //     0x14d3df8: mov             x2, NULL
    // 0x14d3dfc: stur            x0, [fp, #-8]
    // 0x14d3e00: r0 = AllocateClosure()
    //     0x14d3e00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d3e04: stur            x0, [fp, #-0x10]
    // 0x14d3e08: r0 = ListView()
    //     0x14d3e08: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x14d3e0c: stur            x0, [fp, #-0x18]
    // 0x14d3e10: r16 = Instance_NeverScrollableScrollPhysics
    //     0x14d3e10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x14d3e14: ldr             x16, [x16, #0x1c8]
    // 0x14d3e18: r30 = true
    //     0x14d3e18: add             lr, NULL, #0x20  ; true
    // 0x14d3e1c: stp             lr, x16, [SP]
    // 0x14d3e20: mov             x1, x0
    // 0x14d3e24: ldur            x2, [fp, #-8]
    // 0x14d3e28: ldur            x3, [fp, #-0x50]
    // 0x14d3e2c: ldur            x5, [fp, #-0x10]
    // 0x14d3e30: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0x14d3e30: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0x14d3e34: ldr             x4, [x4, #0x968]
    // 0x14d3e38: r0 = ListView.separated()
    //     0x14d3e38: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14d3e3c: r0 = BrowseAccordian()
    //     0x14d3e3c: bl              #0x14d35cc  ; AllocateBrowseAccordianStub -> BrowseAccordian (size=0x24)
    // 0x14d3e40: mov             x1, x0
    // 0x14d3e44: ldur            x0, [fp, #-0x38]
    // 0x14d3e48: stur            x1, [fp, #-8]
    // 0x14d3e4c: StoreField: r1->field_b = r0
    //     0x14d3e4c: stur            w0, [x1, #0xb]
    // 0x14d3e50: ldur            x0, [fp, #-0x18]
    // 0x14d3e54: StoreField: r1->field_f = r0
    //     0x14d3e54: stur            w0, [x1, #0xf]
    // 0x14d3e58: r0 = true
    //     0x14d3e58: add             x0, NULL, #0x20  ; true
    // 0x14d3e5c: StoreField: r1->field_13 = r0
    //     0x14d3e5c: stur            w0, [x1, #0x13]
    // 0x14d3e60: ldur            x0, [fp, #-0x28]
    // 0x14d3e64: StoreField: r1->field_1b = r0
    //     0x14d3e64: stur            w0, [x1, #0x1b]
    // 0x14d3e68: ldur            x0, [fp, #-0x30]
    // 0x14d3e6c: StoreField: r1->field_1f = r0
    //     0x14d3e6c: stur            w0, [x1, #0x1f]
    // 0x14d3e70: r0 = Container()
    //     0x14d3e70: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14d3e74: stur            x0, [fp, #-0x10]
    // 0x14d3e78: ldur            x16, [fp, #-0x20]
    // 0x14d3e7c: r30 = Instance_EdgeInsets
    //     0x14d3e7c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x14d3e80: ldr             lr, [lr, #0x1f0]
    // 0x14d3e84: stp             lr, x16, [SP, #8]
    // 0x14d3e88: ldur            x16, [fp, #-8]
    // 0x14d3e8c: str             x16, [SP]
    // 0x14d3e90: mov             x1, x0
    // 0x14d3e94: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0x14d3e94: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0x14d3e98: ldr             x4, [x4, #0xb40]
    // 0x14d3e9c: r0 = Container()
    //     0x14d3e9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14d3ea0: ldur            x0, [fp, #-0x10]
    // 0x14d3ea4: LeaveFrame
    //     0x14d3ea4: mov             SP, fp
    //     0x14d3ea8: ldp             fp, lr, [SP], #0x10
    // 0x14d3eac: ret
    //     0x14d3eac: ret             
    // 0x14d3eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d3eb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d3eb4: b               #0x14d3600
    // 0x14d3eb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3eb8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ebc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ebc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ec0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ec0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ec4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ec4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ec8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ec8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ecc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ecc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ed0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ed0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ed4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ed4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3ed8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3ed8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d3edc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d3edc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x14d3ee0, size: 0x298
    // 0x14d3ee0: EnterFrame
    //     0x14d3ee0: stp             fp, lr, [SP, #-0x10]!
    //     0x14d3ee4: mov             fp, SP
    // 0x14d3ee8: AllocStack(0x30)
    //     0x14d3ee8: sub             SP, SP, #0x30
    // 0x14d3eec: SetupParameters()
    //     0x14d3eec: ldr             x0, [fp, #0x20]
    //     0x14d3ef0: ldur            w1, [x0, #0x17]
    //     0x14d3ef4: add             x1, x1, HEAP, lsl #32
    //     0x14d3ef8: stur            x1, [fp, #-8]
    // 0x14d3efc: CheckStackOverflow
    //     0x14d3efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d3f00: cmp             SP, x16
    //     0x14d3f04: b.ls            #0x14d4164
    // 0x14d3f08: r1 = 1
    //     0x14d3f08: movz            x1, #0x1
    // 0x14d3f0c: r0 = AllocateContext()
    //     0x14d3f0c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d3f10: mov             x2, x0
    // 0x14d3f14: ldur            x0, [fp, #-8]
    // 0x14d3f18: stur            x2, [fp, #-0x18]
    // 0x14d3f1c: StoreField: r2->field_b = r0
    //     0x14d3f1c: stur            w0, [x2, #0xb]
    // 0x14d3f20: ldr             x3, [fp, #0x10]
    // 0x14d3f24: StoreField: r2->field_f = r3
    //     0x14d3f24: stur            w3, [x2, #0xf]
    // 0x14d3f28: LoadField: r4 = r0->field_b
    //     0x14d3f28: ldur            w4, [x0, #0xb]
    // 0x14d3f2c: DecompressPointer r4
    //     0x14d3f2c: add             x4, x4, HEAP, lsl #32
    // 0x14d3f30: stur            x4, [fp, #-0x10]
    // 0x14d3f34: LoadField: r1 = r4->field_b
    //     0x14d3f34: ldur            w1, [x4, #0xb]
    // 0x14d3f38: DecompressPointer r1
    //     0x14d3f38: add             x1, x1, HEAP, lsl #32
    // 0x14d3f3c: LoadField: r5 = r1->field_f
    //     0x14d3f3c: ldur            w5, [x1, #0xf]
    // 0x14d3f40: DecompressPointer r5
    //     0x14d3f40: add             x5, x5, HEAP, lsl #32
    // 0x14d3f44: mov             x1, x5
    // 0x14d3f48: r0 = controller()
    //     0x14d3f48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d3f4c: LoadField: r1 = r0->field_4b
    //     0x14d3f4c: ldur            w1, [x0, #0x4b]
    // 0x14d3f50: DecompressPointer r1
    //     0x14d3f50: add             x1, x1, HEAP, lsl #32
    // 0x14d3f54: r0 = value()
    //     0x14d3f54: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d3f58: LoadField: r2 = r0->field_63
    //     0x14d3f58: ldur            w2, [x0, #0x63]
    // 0x14d3f5c: DecompressPointer r2
    //     0x14d3f5c: add             x2, x2, HEAP, lsl #32
    // 0x14d3f60: cmp             w2, NULL
    // 0x14d3f64: b.ne            #0x14d3f70
    // 0x14d3f68: r0 = Null
    //     0x14d3f68: mov             x0, NULL
    // 0x14d3f6c: b               #0x14d4098
    // 0x14d3f70: ldur            x0, [fp, #-0x10]
    // 0x14d3f74: LoadField: r1 = r0->field_f
    //     0x14d3f74: ldur            w1, [x0, #0xf]
    // 0x14d3f78: DecompressPointer r1
    //     0x14d3f78: add             x1, x1, HEAP, lsl #32
    // 0x14d3f7c: LoadField: r0 = r2->field_b
    //     0x14d3f7c: ldur            w0, [x2, #0xb]
    // 0x14d3f80: r3 = LoadInt32Instr(r1)
    //     0x14d3f80: sbfx            x3, x1, #1, #0x1f
    //     0x14d3f84: tbz             w1, #0, #0x14d3f8c
    //     0x14d3f88: ldur            x3, [x1, #7]
    // 0x14d3f8c: r1 = LoadInt32Instr(r0)
    //     0x14d3f8c: sbfx            x1, x0, #1, #0x1f
    // 0x14d3f90: mov             x0, x1
    // 0x14d3f94: mov             x1, x3
    // 0x14d3f98: cmp             x1, x0
    // 0x14d3f9c: b.hs            #0x14d416c
    // 0x14d3fa0: LoadField: r0 = r2->field_f
    //     0x14d3fa0: ldur            w0, [x2, #0xf]
    // 0x14d3fa4: DecompressPointer r0
    //     0x14d3fa4: add             x0, x0, HEAP, lsl #32
    // 0x14d3fa8: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14d3fa8: add             x16, x0, x3, lsl #2
    //     0x14d3fac: ldur            w1, [x16, #0xf]
    // 0x14d3fb0: DecompressPointer r1
    //     0x14d3fb0: add             x1, x1, HEAP, lsl #32
    // 0x14d3fb4: cmp             w1, NULL
    // 0x14d3fb8: b.ne            #0x14d3fc4
    // 0x14d3fbc: r0 = Null
    //     0x14d3fbc: mov             x0, NULL
    // 0x14d3fc0: b               #0x14d4098
    // 0x14d3fc4: LoadField: r2 = r1->field_1f
    //     0x14d3fc4: ldur            w2, [x1, #0x1f]
    // 0x14d3fc8: DecompressPointer r2
    //     0x14d3fc8: add             x2, x2, HEAP, lsl #32
    // 0x14d3fcc: cmp             w2, NULL
    // 0x14d3fd0: b.ne            #0x14d3fdc
    // 0x14d3fd4: r0 = Null
    //     0x14d3fd4: mov             x0, NULL
    // 0x14d3fd8: b               #0x14d4098
    // 0x14d3fdc: ldur            x0, [fp, #-8]
    // 0x14d3fe0: LoadField: r1 = r0->field_f
    //     0x14d3fe0: ldur            w1, [x0, #0xf]
    // 0x14d3fe4: DecompressPointer r1
    //     0x14d3fe4: add             x1, x1, HEAP, lsl #32
    // 0x14d3fe8: LoadField: r0 = r2->field_b
    //     0x14d3fe8: ldur            w0, [x2, #0xb]
    // 0x14d3fec: r3 = LoadInt32Instr(r1)
    //     0x14d3fec: sbfx            x3, x1, #1, #0x1f
    //     0x14d3ff0: tbz             w1, #0, #0x14d3ff8
    //     0x14d3ff4: ldur            x3, [x1, #7]
    // 0x14d3ff8: r1 = LoadInt32Instr(r0)
    //     0x14d3ff8: sbfx            x1, x0, #1, #0x1f
    // 0x14d3ffc: mov             x0, x1
    // 0x14d4000: mov             x1, x3
    // 0x14d4004: cmp             x1, x0
    // 0x14d4008: b.hs            #0x14d4170
    // 0x14d400c: LoadField: r0 = r2->field_f
    //     0x14d400c: ldur            w0, [x2, #0xf]
    // 0x14d4010: DecompressPointer r0
    //     0x14d4010: add             x0, x0, HEAP, lsl #32
    // 0x14d4014: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14d4014: add             x16, x0, x3, lsl #2
    //     0x14d4018: ldur            w1, [x16, #0xf]
    // 0x14d401c: DecompressPointer r1
    //     0x14d401c: add             x1, x1, HEAP, lsl #32
    // 0x14d4020: cmp             w1, NULL
    // 0x14d4024: b.ne            #0x14d4030
    // 0x14d4028: r0 = Null
    //     0x14d4028: mov             x0, NULL
    // 0x14d402c: b               #0x14d4098
    // 0x14d4030: LoadField: r2 = r1->field_1f
    //     0x14d4030: ldur            w2, [x1, #0x1f]
    // 0x14d4034: DecompressPointer r2
    //     0x14d4034: add             x2, x2, HEAP, lsl #32
    // 0x14d4038: cmp             w2, NULL
    // 0x14d403c: b.ne            #0x14d4048
    // 0x14d4040: r0 = Null
    //     0x14d4040: mov             x0, NULL
    // 0x14d4044: b               #0x14d4098
    // 0x14d4048: ldr             x0, [fp, #0x10]
    // 0x14d404c: LoadField: r1 = r2->field_b
    //     0x14d404c: ldur            w1, [x2, #0xb]
    // 0x14d4050: r3 = LoadInt32Instr(r0)
    //     0x14d4050: sbfx            x3, x0, #1, #0x1f
    //     0x14d4054: tbz             w0, #0, #0x14d405c
    //     0x14d4058: ldur            x3, [x0, #7]
    // 0x14d405c: r0 = LoadInt32Instr(r1)
    //     0x14d405c: sbfx            x0, x1, #1, #0x1f
    // 0x14d4060: mov             x1, x3
    // 0x14d4064: cmp             x1, x0
    // 0x14d4068: b.hs            #0x14d4174
    // 0x14d406c: LoadField: r0 = r2->field_f
    //     0x14d406c: ldur            w0, [x2, #0xf]
    // 0x14d4070: DecompressPointer r0
    //     0x14d4070: add             x0, x0, HEAP, lsl #32
    // 0x14d4074: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x14d4074: add             x16, x0, x3, lsl #2
    //     0x14d4078: ldur            w1, [x16, #0xf]
    // 0x14d407c: DecompressPointer r1
    //     0x14d407c: add             x1, x1, HEAP, lsl #32
    // 0x14d4080: cmp             w1, NULL
    // 0x14d4084: b.ne            #0x14d4090
    // 0x14d4088: r0 = Null
    //     0x14d4088: mov             x0, NULL
    // 0x14d408c: b               #0x14d4098
    // 0x14d4090: LoadField: r0 = r1->field_f
    //     0x14d4090: ldur            w0, [x1, #0xf]
    // 0x14d4094: DecompressPointer r0
    //     0x14d4094: add             x0, x0, HEAP, lsl #32
    // 0x14d4098: cmp             w0, NULL
    // 0x14d409c: b.ne            #0x14d40a4
    // 0x14d40a0: r0 = ""
    //     0x14d40a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d40a4: ldr             x1, [fp, #0x18]
    // 0x14d40a8: stur            x0, [fp, #-8]
    // 0x14d40ac: r0 = of()
    //     0x14d40ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14d40b0: LoadField: r1 = r0->field_87
    //     0x14d40b0: ldur            w1, [x0, #0x87]
    // 0x14d40b4: DecompressPointer r1
    //     0x14d40b4: add             x1, x1, HEAP, lsl #32
    // 0x14d40b8: LoadField: r0 = r1->field_7
    //     0x14d40b8: ldur            w0, [x1, #7]
    // 0x14d40bc: DecompressPointer r0
    //     0x14d40bc: add             x0, x0, HEAP, lsl #32
    // 0x14d40c0: r16 = Instance_Color
    //     0x14d40c0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14d40c4: r30 = 14.000000
    //     0x14d40c4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x14d40c8: ldr             lr, [lr, #0x1d8]
    // 0x14d40cc: stp             lr, x16, [SP]
    // 0x14d40d0: mov             x1, x0
    // 0x14d40d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14d40d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14d40d8: ldr             x4, [x4, #0x9b8]
    // 0x14d40dc: r0 = copyWith()
    //     0x14d40dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14d40e0: stur            x0, [fp, #-0x10]
    // 0x14d40e4: r0 = Text()
    //     0x14d40e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14d40e8: mov             x1, x0
    // 0x14d40ec: ldur            x0, [fp, #-8]
    // 0x14d40f0: stur            x1, [fp, #-0x20]
    // 0x14d40f4: StoreField: r1->field_b = r0
    //     0x14d40f4: stur            w0, [x1, #0xb]
    // 0x14d40f8: ldur            x0, [fp, #-0x10]
    // 0x14d40fc: StoreField: r1->field_13 = r0
    //     0x14d40fc: stur            w0, [x1, #0x13]
    // 0x14d4100: r0 = InkWell()
    //     0x14d4100: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x14d4104: mov             x3, x0
    // 0x14d4108: ldur            x0, [fp, #-0x20]
    // 0x14d410c: stur            x3, [fp, #-8]
    // 0x14d4110: StoreField: r3->field_b = r0
    //     0x14d4110: stur            w0, [x3, #0xb]
    // 0x14d4114: ldur            x2, [fp, #-0x18]
    // 0x14d4118: r1 = Function '<anonymous closure>':.
    //     0x14d4118: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a40] AnonymousClosure: (0x137e1c8), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::body (0x14fdb8c)
    //     0x14d411c: ldr             x1, [x1, #0xa40]
    // 0x14d4120: r0 = AllocateClosure()
    //     0x14d4120: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d4124: mov             x1, x0
    // 0x14d4128: ldur            x0, [fp, #-8]
    // 0x14d412c: StoreField: r0->field_f = r1
    //     0x14d412c: stur            w1, [x0, #0xf]
    // 0x14d4130: r1 = true
    //     0x14d4130: add             x1, NULL, #0x20  ; true
    // 0x14d4134: StoreField: r0->field_43 = r1
    //     0x14d4134: stur            w1, [x0, #0x43]
    // 0x14d4138: r2 = Instance_BoxShape
    //     0x14d4138: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14d413c: ldr             x2, [x2, #0x80]
    // 0x14d4140: StoreField: r0->field_47 = r2
    //     0x14d4140: stur            w2, [x0, #0x47]
    // 0x14d4144: StoreField: r0->field_6f = r1
    //     0x14d4144: stur            w1, [x0, #0x6f]
    // 0x14d4148: r2 = false
    //     0x14d4148: add             x2, NULL, #0x30  ; false
    // 0x14d414c: StoreField: r0->field_73 = r2
    //     0x14d414c: stur            w2, [x0, #0x73]
    // 0x14d4150: StoreField: r0->field_83 = r1
    //     0x14d4150: stur            w1, [x0, #0x83]
    // 0x14d4154: StoreField: r0->field_7b = r2
    //     0x14d4154: stur            w2, [x0, #0x7b]
    // 0x14d4158: LeaveFrame
    //     0x14d4158: mov             SP, fp
    //     0x14d415c: ldp             fp, lr, [SP], #0x10
    // 0x14d4160: ret
    //     0x14d4160: ret             
    // 0x14d4164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d4164: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d4168: b               #0x14d3f08
    // 0x14d416c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d416c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d4170: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d4170: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x14d4174: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x14d4174: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15cbd2c, size: 0x42c
    // 0x15cbd2c: EnterFrame
    //     0x15cbd2c: stp             fp, lr, [SP, #-0x10]!
    //     0x15cbd30: mov             fp, SP
    // 0x15cbd34: AllocStack(0x48)
    //     0x15cbd34: sub             SP, SP, #0x48
    // 0x15cbd38: SetupParameters()
    //     0x15cbd38: ldr             x0, [fp, #0x10]
    //     0x15cbd3c: ldur            w2, [x0, #0x17]
    //     0x15cbd40: add             x2, x2, HEAP, lsl #32
    //     0x15cbd44: stur            x2, [fp, #-8]
    // 0x15cbd48: CheckStackOverflow
    //     0x15cbd48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cbd4c: cmp             SP, x16
    //     0x15cbd50: b.ls            #0x15cc150
    // 0x15cbd54: LoadField: r1 = r2->field_f
    //     0x15cbd54: ldur            w1, [x2, #0xf]
    // 0x15cbd58: DecompressPointer r1
    //     0x15cbd58: add             x1, x1, HEAP, lsl #32
    // 0x15cbd5c: r0 = controller()
    //     0x15cbd5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cbd60: LoadField: r1 = r0->field_4b
    //     0x15cbd60: ldur            w1, [x0, #0x4b]
    // 0x15cbd64: DecompressPointer r1
    //     0x15cbd64: add             x1, x1, HEAP, lsl #32
    // 0x15cbd68: r0 = value()
    //     0x15cbd68: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cbd6c: LoadField: r1 = r0->field_3f
    //     0x15cbd6c: ldur            w1, [x0, #0x3f]
    // 0x15cbd70: DecompressPointer r1
    //     0x15cbd70: add             x1, x1, HEAP, lsl #32
    // 0x15cbd74: cmp             w1, NULL
    // 0x15cbd78: b.ne            #0x15cbd84
    // 0x15cbd7c: r0 = Null
    //     0x15cbd7c: mov             x0, NULL
    // 0x15cbd80: b               #0x15cbd8c
    // 0x15cbd84: LoadField: r0 = r1->field_f
    //     0x15cbd84: ldur            w0, [x1, #0xf]
    // 0x15cbd88: DecompressPointer r0
    //     0x15cbd88: add             x0, x0, HEAP, lsl #32
    // 0x15cbd8c: r1 = LoadClassIdInstr(r0)
    //     0x15cbd8c: ldur            x1, [x0, #-1]
    //     0x15cbd90: ubfx            x1, x1, #0xc, #0x14
    // 0x15cbd94: r16 = "image_text"
    //     0x15cbd94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cbd98: ldr             x16, [x16, #0xa88]
    // 0x15cbd9c: stp             x16, x0, [SP]
    // 0x15cbda0: mov             x0, x1
    // 0x15cbda4: mov             lr, x0
    // 0x15cbda8: ldr             lr, [x21, lr, lsl #3]
    // 0x15cbdac: blr             lr
    // 0x15cbdb0: tbnz            w0, #4, #0x15cbdbc
    // 0x15cbdb4: r2 = true
    //     0x15cbdb4: add             x2, NULL, #0x20  ; true
    // 0x15cbdb8: b               #0x15cbe1c
    // 0x15cbdbc: ldur            x0, [fp, #-8]
    // 0x15cbdc0: LoadField: r1 = r0->field_f
    //     0x15cbdc0: ldur            w1, [x0, #0xf]
    // 0x15cbdc4: DecompressPointer r1
    //     0x15cbdc4: add             x1, x1, HEAP, lsl #32
    // 0x15cbdc8: r0 = controller()
    //     0x15cbdc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cbdcc: LoadField: r1 = r0->field_4b
    //     0x15cbdcc: ldur            w1, [x0, #0x4b]
    // 0x15cbdd0: DecompressPointer r1
    //     0x15cbdd0: add             x1, x1, HEAP, lsl #32
    // 0x15cbdd4: r0 = value()
    //     0x15cbdd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cbdd8: LoadField: r1 = r0->field_3f
    //     0x15cbdd8: ldur            w1, [x0, #0x3f]
    // 0x15cbddc: DecompressPointer r1
    //     0x15cbddc: add             x1, x1, HEAP, lsl #32
    // 0x15cbde0: cmp             w1, NULL
    // 0x15cbde4: b.ne            #0x15cbdf0
    // 0x15cbde8: r0 = Null
    //     0x15cbde8: mov             x0, NULL
    // 0x15cbdec: b               #0x15cbdf8
    // 0x15cbdf0: LoadField: r0 = r1->field_f
    //     0x15cbdf0: ldur            w0, [x1, #0xf]
    // 0x15cbdf4: DecompressPointer r0
    //     0x15cbdf4: add             x0, x0, HEAP, lsl #32
    // 0x15cbdf8: r1 = LoadClassIdInstr(r0)
    //     0x15cbdf8: ldur            x1, [x0, #-1]
    //     0x15cbdfc: ubfx            x1, x1, #0xc, #0x14
    // 0x15cbe00: r16 = "image"
    //     0x15cbe00: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15cbe04: stp             x16, x0, [SP]
    // 0x15cbe08: mov             x0, x1
    // 0x15cbe0c: mov             lr, x0
    // 0x15cbe10: ldr             lr, [x21, lr, lsl #3]
    // 0x15cbe14: blr             lr
    // 0x15cbe18: mov             x2, x0
    // 0x15cbe1c: ldur            x0, [fp, #-8]
    // 0x15cbe20: stur            x2, [fp, #-0x10]
    // 0x15cbe24: LoadField: r1 = r0->field_f
    //     0x15cbe24: ldur            w1, [x0, #0xf]
    // 0x15cbe28: DecompressPointer r1
    //     0x15cbe28: add             x1, x1, HEAP, lsl #32
    // 0x15cbe2c: r0 = controller()
    //     0x15cbe2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cbe30: LoadField: r1 = r0->field_4b
    //     0x15cbe30: ldur            w1, [x0, #0x4b]
    // 0x15cbe34: DecompressPointer r1
    //     0x15cbe34: add             x1, x1, HEAP, lsl #32
    // 0x15cbe38: r0 = value()
    //     0x15cbe38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cbe3c: LoadField: r1 = r0->field_27
    //     0x15cbe3c: ldur            w1, [x0, #0x27]
    // 0x15cbe40: DecompressPointer r1
    //     0x15cbe40: add             x1, x1, HEAP, lsl #32
    // 0x15cbe44: cmp             w1, NULL
    // 0x15cbe48: b.ne            #0x15cbe54
    // 0x15cbe4c: r2 = ""
    //     0x15cbe4c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cbe50: b               #0x15cbe58
    // 0x15cbe54: mov             x2, x1
    // 0x15cbe58: ldur            x0, [fp, #-8]
    // 0x15cbe5c: ldur            x1, [fp, #-0x10]
    // 0x15cbe60: stur            x2, [fp, #-0x18]
    // 0x15cbe64: r0 = ImageHeaders.forImages()
    //     0x15cbe64: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15cbe68: stur            x0, [fp, #-0x20]
    // 0x15cbe6c: r0 = CachedNetworkImage()
    //     0x15cbe6c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15cbe70: stur            x0, [fp, #-0x28]
    // 0x15cbe74: ldur            x16, [fp, #-0x20]
    // 0x15cbe78: r30 = Instance_BoxFit
    //     0x15cbe78: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15cbe7c: ldr             lr, [lr, #0xb18]
    // 0x15cbe80: stp             lr, x16, [SP, #0x10]
    // 0x15cbe84: r16 = 50.000000
    //     0x15cbe84: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cbe88: ldr             x16, [x16, #0xa90]
    // 0x15cbe8c: r30 = 50.000000
    //     0x15cbe8c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15cbe90: ldr             lr, [lr, #0xa90]
    // 0x15cbe94: stp             lr, x16, [SP]
    // 0x15cbe98: mov             x1, x0
    // 0x15cbe9c: ldur            x2, [fp, #-0x18]
    // 0x15cbea0: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15cbea0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15cbea4: ldr             x4, [x4, #0xa98]
    // 0x15cbea8: r0 = CachedNetworkImage()
    //     0x15cbea8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15cbeac: r0 = Visibility()
    //     0x15cbeac: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cbeb0: mov             x2, x0
    // 0x15cbeb4: ldur            x0, [fp, #-0x28]
    // 0x15cbeb8: stur            x2, [fp, #-0x18]
    // 0x15cbebc: StoreField: r2->field_b = r0
    //     0x15cbebc: stur            w0, [x2, #0xb]
    // 0x15cbec0: r0 = Instance_SizedBox
    //     0x15cbec0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cbec4: StoreField: r2->field_f = r0
    //     0x15cbec4: stur            w0, [x2, #0xf]
    // 0x15cbec8: ldur            x1, [fp, #-0x10]
    // 0x15cbecc: StoreField: r2->field_13 = r1
    //     0x15cbecc: stur            w1, [x2, #0x13]
    // 0x15cbed0: r3 = false
    //     0x15cbed0: add             x3, NULL, #0x30  ; false
    // 0x15cbed4: ArrayStore: r2[0] = r3  ; List_4
    //     0x15cbed4: stur            w3, [x2, #0x17]
    // 0x15cbed8: StoreField: r2->field_1b = r3
    //     0x15cbed8: stur            w3, [x2, #0x1b]
    // 0x15cbedc: StoreField: r2->field_1f = r3
    //     0x15cbedc: stur            w3, [x2, #0x1f]
    // 0x15cbee0: StoreField: r2->field_23 = r3
    //     0x15cbee0: stur            w3, [x2, #0x23]
    // 0x15cbee4: StoreField: r2->field_27 = r3
    //     0x15cbee4: stur            w3, [x2, #0x27]
    // 0x15cbee8: StoreField: r2->field_2b = r3
    //     0x15cbee8: stur            w3, [x2, #0x2b]
    // 0x15cbeec: ldur            x4, [fp, #-8]
    // 0x15cbef0: LoadField: r1 = r4->field_f
    //     0x15cbef0: ldur            w1, [x4, #0xf]
    // 0x15cbef4: DecompressPointer r1
    //     0x15cbef4: add             x1, x1, HEAP, lsl #32
    // 0x15cbef8: r0 = controller()
    //     0x15cbef8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cbefc: LoadField: r1 = r0->field_4b
    //     0x15cbefc: ldur            w1, [x0, #0x4b]
    // 0x15cbf00: DecompressPointer r1
    //     0x15cbf00: add             x1, x1, HEAP, lsl #32
    // 0x15cbf04: r0 = value()
    //     0x15cbf04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cbf08: LoadField: r1 = r0->field_3f
    //     0x15cbf08: ldur            w1, [x0, #0x3f]
    // 0x15cbf0c: DecompressPointer r1
    //     0x15cbf0c: add             x1, x1, HEAP, lsl #32
    // 0x15cbf10: cmp             w1, NULL
    // 0x15cbf14: b.ne            #0x15cbf20
    // 0x15cbf18: r0 = Null
    //     0x15cbf18: mov             x0, NULL
    // 0x15cbf1c: b               #0x15cbf28
    // 0x15cbf20: LoadField: r0 = r1->field_f
    //     0x15cbf20: ldur            w0, [x1, #0xf]
    // 0x15cbf24: DecompressPointer r0
    //     0x15cbf24: add             x0, x0, HEAP, lsl #32
    // 0x15cbf28: r1 = LoadClassIdInstr(r0)
    //     0x15cbf28: ldur            x1, [x0, #-1]
    //     0x15cbf2c: ubfx            x1, x1, #0xc, #0x14
    // 0x15cbf30: r16 = "image_text"
    //     0x15cbf30: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15cbf34: ldr             x16, [x16, #0xa88]
    // 0x15cbf38: stp             x16, x0, [SP]
    // 0x15cbf3c: mov             x0, x1
    // 0x15cbf40: mov             lr, x0
    // 0x15cbf44: ldr             lr, [x21, lr, lsl #3]
    // 0x15cbf48: blr             lr
    // 0x15cbf4c: tbnz            w0, #4, #0x15cbf58
    // 0x15cbf50: r2 = true
    //     0x15cbf50: add             x2, NULL, #0x20  ; true
    // 0x15cbf54: b               #0x15cbfb8
    // 0x15cbf58: ldur            x0, [fp, #-8]
    // 0x15cbf5c: LoadField: r1 = r0->field_f
    //     0x15cbf5c: ldur            w1, [x0, #0xf]
    // 0x15cbf60: DecompressPointer r1
    //     0x15cbf60: add             x1, x1, HEAP, lsl #32
    // 0x15cbf64: r0 = controller()
    //     0x15cbf64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cbf68: LoadField: r1 = r0->field_4b
    //     0x15cbf68: ldur            w1, [x0, #0x4b]
    // 0x15cbf6c: DecompressPointer r1
    //     0x15cbf6c: add             x1, x1, HEAP, lsl #32
    // 0x15cbf70: r0 = value()
    //     0x15cbf70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cbf74: LoadField: r1 = r0->field_3f
    //     0x15cbf74: ldur            w1, [x0, #0x3f]
    // 0x15cbf78: DecompressPointer r1
    //     0x15cbf78: add             x1, x1, HEAP, lsl #32
    // 0x15cbf7c: cmp             w1, NULL
    // 0x15cbf80: b.ne            #0x15cbf8c
    // 0x15cbf84: r0 = Null
    //     0x15cbf84: mov             x0, NULL
    // 0x15cbf88: b               #0x15cbf94
    // 0x15cbf8c: LoadField: r0 = r1->field_f
    //     0x15cbf8c: ldur            w0, [x1, #0xf]
    // 0x15cbf90: DecompressPointer r0
    //     0x15cbf90: add             x0, x0, HEAP, lsl #32
    // 0x15cbf94: r1 = LoadClassIdInstr(r0)
    //     0x15cbf94: ldur            x1, [x0, #-1]
    //     0x15cbf98: ubfx            x1, x1, #0xc, #0x14
    // 0x15cbf9c: r16 = "text"
    //     0x15cbf9c: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15cbfa0: stp             x16, x0, [SP]
    // 0x15cbfa4: mov             x0, x1
    // 0x15cbfa8: mov             lr, x0
    // 0x15cbfac: ldr             lr, [x21, lr, lsl #3]
    // 0x15cbfb0: blr             lr
    // 0x15cbfb4: mov             x2, x0
    // 0x15cbfb8: ldur            x0, [fp, #-8]
    // 0x15cbfbc: stur            x2, [fp, #-0x10]
    // 0x15cbfc0: LoadField: r1 = r0->field_f
    //     0x15cbfc0: ldur            w1, [x0, #0xf]
    // 0x15cbfc4: DecompressPointer r1
    //     0x15cbfc4: add             x1, x1, HEAP, lsl #32
    // 0x15cbfc8: r0 = controller()
    //     0x15cbfc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cbfcc: LoadField: r1 = r0->field_4b
    //     0x15cbfcc: ldur            w1, [x0, #0x4b]
    // 0x15cbfd0: DecompressPointer r1
    //     0x15cbfd0: add             x1, x1, HEAP, lsl #32
    // 0x15cbfd4: r0 = value()
    //     0x15cbfd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15cbfd8: LoadField: r1 = r0->field_2b
    //     0x15cbfd8: ldur            w1, [x0, #0x2b]
    // 0x15cbfdc: DecompressPointer r1
    //     0x15cbfdc: add             x1, x1, HEAP, lsl #32
    // 0x15cbfe0: cmp             w1, NULL
    // 0x15cbfe4: b.ne            #0x15cbff0
    // 0x15cbfe8: r4 = ""
    //     0x15cbfe8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cbfec: b               #0x15cbff4
    // 0x15cbff0: mov             x4, x1
    // 0x15cbff4: ldur            x0, [fp, #-8]
    // 0x15cbff8: ldur            x3, [fp, #-0x18]
    // 0x15cbffc: ldur            x2, [fp, #-0x10]
    // 0x15cc000: stur            x4, [fp, #-0x20]
    // 0x15cc004: LoadField: r1 = r0->field_13
    //     0x15cc004: ldur            w1, [x0, #0x13]
    // 0x15cc008: DecompressPointer r1
    //     0x15cc008: add             x1, x1, HEAP, lsl #32
    // 0x15cc00c: r0 = of()
    //     0x15cc00c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15cc010: LoadField: r1 = r0->field_87
    //     0x15cc010: ldur            w1, [x0, #0x87]
    // 0x15cc014: DecompressPointer r1
    //     0x15cc014: add             x1, x1, HEAP, lsl #32
    // 0x15cc018: LoadField: r0 = r1->field_2b
    //     0x15cc018: ldur            w0, [x1, #0x2b]
    // 0x15cc01c: DecompressPointer r0
    //     0x15cc01c: add             x0, x0, HEAP, lsl #32
    // 0x15cc020: r16 = 16.000000
    //     0x15cc020: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15cc024: ldr             x16, [x16, #0x188]
    // 0x15cc028: r30 = Instance_Color
    //     0x15cc028: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15cc02c: stp             lr, x16, [SP]
    // 0x15cc030: mov             x1, x0
    // 0x15cc034: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15cc034: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15cc038: ldr             x4, [x4, #0xaa0]
    // 0x15cc03c: r0 = copyWith()
    //     0x15cc03c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15cc040: stur            x0, [fp, #-8]
    // 0x15cc044: r0 = Text()
    //     0x15cc044: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15cc048: mov             x1, x0
    // 0x15cc04c: ldur            x0, [fp, #-0x20]
    // 0x15cc050: stur            x1, [fp, #-0x28]
    // 0x15cc054: StoreField: r1->field_b = r0
    //     0x15cc054: stur            w0, [x1, #0xb]
    // 0x15cc058: ldur            x0, [fp, #-8]
    // 0x15cc05c: StoreField: r1->field_13 = r0
    //     0x15cc05c: stur            w0, [x1, #0x13]
    // 0x15cc060: r0 = Visibility()
    //     0x15cc060: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15cc064: mov             x3, x0
    // 0x15cc068: ldur            x0, [fp, #-0x28]
    // 0x15cc06c: stur            x3, [fp, #-8]
    // 0x15cc070: StoreField: r3->field_b = r0
    //     0x15cc070: stur            w0, [x3, #0xb]
    // 0x15cc074: r0 = Instance_SizedBox
    //     0x15cc074: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15cc078: StoreField: r3->field_f = r0
    //     0x15cc078: stur            w0, [x3, #0xf]
    // 0x15cc07c: ldur            x0, [fp, #-0x10]
    // 0x15cc080: StoreField: r3->field_13 = r0
    //     0x15cc080: stur            w0, [x3, #0x13]
    // 0x15cc084: r0 = false
    //     0x15cc084: add             x0, NULL, #0x30  ; false
    // 0x15cc088: ArrayStore: r3[0] = r0  ; List_4
    //     0x15cc088: stur            w0, [x3, #0x17]
    // 0x15cc08c: StoreField: r3->field_1b = r0
    //     0x15cc08c: stur            w0, [x3, #0x1b]
    // 0x15cc090: StoreField: r3->field_1f = r0
    //     0x15cc090: stur            w0, [x3, #0x1f]
    // 0x15cc094: StoreField: r3->field_23 = r0
    //     0x15cc094: stur            w0, [x3, #0x23]
    // 0x15cc098: StoreField: r3->field_27 = r0
    //     0x15cc098: stur            w0, [x3, #0x27]
    // 0x15cc09c: StoreField: r3->field_2b = r0
    //     0x15cc09c: stur            w0, [x3, #0x2b]
    // 0x15cc0a0: r1 = Null
    //     0x15cc0a0: mov             x1, NULL
    // 0x15cc0a4: r2 = 6
    //     0x15cc0a4: movz            x2, #0x6
    // 0x15cc0a8: r0 = AllocateArray()
    //     0x15cc0a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15cc0ac: mov             x2, x0
    // 0x15cc0b0: ldur            x0, [fp, #-0x18]
    // 0x15cc0b4: stur            x2, [fp, #-0x10]
    // 0x15cc0b8: StoreField: r2->field_f = r0
    //     0x15cc0b8: stur            w0, [x2, #0xf]
    // 0x15cc0bc: r16 = Instance_SizedBox
    //     0x15cc0bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0x15cc0c0: ldr             x16, [x16, #0xaa8]
    // 0x15cc0c4: StoreField: r2->field_13 = r16
    //     0x15cc0c4: stur            w16, [x2, #0x13]
    // 0x15cc0c8: ldur            x0, [fp, #-8]
    // 0x15cc0cc: ArrayStore: r2[0] = r0  ; List_4
    //     0x15cc0cc: stur            w0, [x2, #0x17]
    // 0x15cc0d0: r1 = <Widget>
    //     0x15cc0d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15cc0d4: r0 = AllocateGrowableArray()
    //     0x15cc0d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15cc0d8: mov             x1, x0
    // 0x15cc0dc: ldur            x0, [fp, #-0x10]
    // 0x15cc0e0: stur            x1, [fp, #-8]
    // 0x15cc0e4: StoreField: r1->field_f = r0
    //     0x15cc0e4: stur            w0, [x1, #0xf]
    // 0x15cc0e8: r0 = 6
    //     0x15cc0e8: movz            x0, #0x6
    // 0x15cc0ec: StoreField: r1->field_b = r0
    //     0x15cc0ec: stur            w0, [x1, #0xb]
    // 0x15cc0f0: r0 = Row()
    //     0x15cc0f0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15cc0f4: r1 = Instance_Axis
    //     0x15cc0f4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15cc0f8: StoreField: r0->field_f = r1
    //     0x15cc0f8: stur            w1, [x0, #0xf]
    // 0x15cc0fc: r1 = Instance_MainAxisAlignment
    //     0x15cc0fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15cc100: ldr             x1, [x1, #0xab0]
    // 0x15cc104: StoreField: r0->field_13 = r1
    //     0x15cc104: stur            w1, [x0, #0x13]
    // 0x15cc108: r1 = Instance_MainAxisSize
    //     0x15cc108: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15cc10c: ldr             x1, [x1, #0xa10]
    // 0x15cc110: ArrayStore: r0[0] = r1  ; List_4
    //     0x15cc110: stur            w1, [x0, #0x17]
    // 0x15cc114: r1 = Instance_CrossAxisAlignment
    //     0x15cc114: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15cc118: ldr             x1, [x1, #0xa18]
    // 0x15cc11c: StoreField: r0->field_1b = r1
    //     0x15cc11c: stur            w1, [x0, #0x1b]
    // 0x15cc120: r1 = Instance_VerticalDirection
    //     0x15cc120: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15cc124: ldr             x1, [x1, #0xa20]
    // 0x15cc128: StoreField: r0->field_23 = r1
    //     0x15cc128: stur            w1, [x0, #0x23]
    // 0x15cc12c: r1 = Instance_Clip
    //     0x15cc12c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15cc130: ldr             x1, [x1, #0x38]
    // 0x15cc134: StoreField: r0->field_2b = r1
    //     0x15cc134: stur            w1, [x0, #0x2b]
    // 0x15cc138: StoreField: r0->field_2f = rZR
    //     0x15cc138: stur            xzr, [x0, #0x2f]
    // 0x15cc13c: ldur            x1, [fp, #-8]
    // 0x15cc140: StoreField: r0->field_b = r1
    //     0x15cc140: stur            w1, [x0, #0xb]
    // 0x15cc144: LeaveFrame
    //     0x15cc144: mov             SP, fp
    //     0x15cc148: ldp             fp, lr, [SP], #0x10
    // 0x15cc14c: ret
    //     0x15cc14c: ret             
    // 0x15cc150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cc150: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cc154: b               #0x15cbd54
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15dfa24, size: 0x2b4
    // 0x15dfa24: EnterFrame
    //     0x15dfa24: stp             fp, lr, [SP, #-0x10]!
    //     0x15dfa28: mov             fp, SP
    // 0x15dfa2c: AllocStack(0x30)
    //     0x15dfa2c: sub             SP, SP, #0x30
    // 0x15dfa30: SetupParameters(BrowseView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15dfa30: stur            x1, [fp, #-8]
    //     0x15dfa34: stur            x2, [fp, #-0x10]
    // 0x15dfa38: CheckStackOverflow
    //     0x15dfa38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dfa3c: cmp             SP, x16
    //     0x15dfa40: b.ls            #0x15dfcd0
    // 0x15dfa44: r1 = 2
    //     0x15dfa44: movz            x1, #0x2
    // 0x15dfa48: r0 = AllocateContext()
    //     0x15dfa48: bl              #0x16f6108  ; AllocateContextStub
    // 0x15dfa4c: ldur            x1, [fp, #-8]
    // 0x15dfa50: stur            x0, [fp, #-0x18]
    // 0x15dfa54: StoreField: r0->field_f = r1
    //     0x15dfa54: stur            w1, [x0, #0xf]
    // 0x15dfa58: ldur            x2, [fp, #-0x10]
    // 0x15dfa5c: StoreField: r0->field_13 = r2
    //     0x15dfa5c: stur            w2, [x0, #0x13]
    // 0x15dfa60: r0 = Obx()
    //     0x15dfa60: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15dfa64: ldur            x2, [fp, #-0x18]
    // 0x15dfa68: r1 = Function '<anonymous closure>':.
    //     0x15dfa68: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a48] AnonymousClosure: (0x15cbd2c), in [package:customer_app/app/presentation/views/glass/browse/browse_view.dart] BrowseView::appBar (0x15dfa24)
    //     0x15dfa6c: ldr             x1, [x1, #0xa48]
    // 0x15dfa70: stur            x0, [fp, #-0x10]
    // 0x15dfa74: r0 = AllocateClosure()
    //     0x15dfa74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dfa78: mov             x1, x0
    // 0x15dfa7c: ldur            x0, [fp, #-0x10]
    // 0x15dfa80: StoreField: r0->field_b = r1
    //     0x15dfa80: stur            w1, [x0, #0xb]
    // 0x15dfa84: ldur            x1, [fp, #-8]
    // 0x15dfa88: r0 = controller()
    //     0x15dfa88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dfa8c: LoadField: r1 = r0->field_57
    //     0x15dfa8c: ldur            w1, [x0, #0x57]
    // 0x15dfa90: DecompressPointer r1
    //     0x15dfa90: add             x1, x1, HEAP, lsl #32
    // 0x15dfa94: r0 = value()
    //     0x15dfa94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dfa98: tbnz            w0, #4, #0x15dfb30
    // 0x15dfa9c: ldur            x2, [fp, #-0x18]
    // 0x15dfaa0: LoadField: r1 = r2->field_13
    //     0x15dfaa0: ldur            w1, [x2, #0x13]
    // 0x15dfaa4: DecompressPointer r1
    //     0x15dfaa4: add             x1, x1, HEAP, lsl #32
    // 0x15dfaa8: r0 = of()
    //     0x15dfaa8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dfaac: LoadField: r1 = r0->field_5b
    //     0x15dfaac: ldur            w1, [x0, #0x5b]
    // 0x15dfab0: DecompressPointer r1
    //     0x15dfab0: add             x1, x1, HEAP, lsl #32
    // 0x15dfab4: stur            x1, [fp, #-8]
    // 0x15dfab8: r0 = ColorFilter()
    //     0x15dfab8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dfabc: mov             x1, x0
    // 0x15dfac0: ldur            x0, [fp, #-8]
    // 0x15dfac4: stur            x1, [fp, #-0x20]
    // 0x15dfac8: StoreField: r1->field_7 = r0
    //     0x15dfac8: stur            w0, [x1, #7]
    // 0x15dfacc: r0 = Instance_BlendMode
    //     0x15dfacc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dfad0: ldr             x0, [x0, #0xb30]
    // 0x15dfad4: StoreField: r1->field_b = r0
    //     0x15dfad4: stur            w0, [x1, #0xb]
    // 0x15dfad8: r2 = 1
    //     0x15dfad8: movz            x2, #0x1
    // 0x15dfadc: StoreField: r1->field_13 = r2
    //     0x15dfadc: stur            x2, [x1, #0x13]
    // 0x15dfae0: r0 = SvgPicture()
    //     0x15dfae0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dfae4: stur            x0, [fp, #-8]
    // 0x15dfae8: ldur            x16, [fp, #-0x20]
    // 0x15dfaec: str             x16, [SP]
    // 0x15dfaf0: mov             x1, x0
    // 0x15dfaf4: r2 = "assets/images/search.svg"
    //     0x15dfaf4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15dfaf8: ldr             x2, [x2, #0xa30]
    // 0x15dfafc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dfafc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dfb00: ldr             x4, [x4, #0xa38]
    // 0x15dfb04: r0 = SvgPicture.asset()
    //     0x15dfb04: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dfb08: r0 = Align()
    //     0x15dfb08: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dfb0c: r3 = Instance_Alignment
    //     0x15dfb0c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dfb10: ldr             x3, [x3, #0xb10]
    // 0x15dfb14: StoreField: r0->field_f = r3
    //     0x15dfb14: stur            w3, [x0, #0xf]
    // 0x15dfb18: r4 = 1.000000
    //     0x15dfb18: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dfb1c: StoreField: r0->field_13 = r4
    //     0x15dfb1c: stur            w4, [x0, #0x13]
    // 0x15dfb20: ArrayStore: r0[0] = r4  ; List_4
    //     0x15dfb20: stur            w4, [x0, #0x17]
    // 0x15dfb24: ldur            x1, [fp, #-8]
    // 0x15dfb28: StoreField: r0->field_b = r1
    //     0x15dfb28: stur            w1, [x0, #0xb]
    // 0x15dfb2c: b               #0x15dfbe0
    // 0x15dfb30: ldur            x5, [fp, #-0x18]
    // 0x15dfb34: r4 = 1.000000
    //     0x15dfb34: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dfb38: r0 = Instance_BlendMode
    //     0x15dfb38: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dfb3c: ldr             x0, [x0, #0xb30]
    // 0x15dfb40: r3 = Instance_Alignment
    //     0x15dfb40: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dfb44: ldr             x3, [x3, #0xb10]
    // 0x15dfb48: r2 = 1
    //     0x15dfb48: movz            x2, #0x1
    // 0x15dfb4c: LoadField: r1 = r5->field_13
    //     0x15dfb4c: ldur            w1, [x5, #0x13]
    // 0x15dfb50: DecompressPointer r1
    //     0x15dfb50: add             x1, x1, HEAP, lsl #32
    // 0x15dfb54: r0 = of()
    //     0x15dfb54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dfb58: LoadField: r1 = r0->field_5b
    //     0x15dfb58: ldur            w1, [x0, #0x5b]
    // 0x15dfb5c: DecompressPointer r1
    //     0x15dfb5c: add             x1, x1, HEAP, lsl #32
    // 0x15dfb60: stur            x1, [fp, #-8]
    // 0x15dfb64: r0 = ColorFilter()
    //     0x15dfb64: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dfb68: mov             x1, x0
    // 0x15dfb6c: ldur            x0, [fp, #-8]
    // 0x15dfb70: stur            x1, [fp, #-0x20]
    // 0x15dfb74: StoreField: r1->field_7 = r0
    //     0x15dfb74: stur            w0, [x1, #7]
    // 0x15dfb78: r0 = Instance_BlendMode
    //     0x15dfb78: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dfb7c: ldr             x0, [x0, #0xb30]
    // 0x15dfb80: StoreField: r1->field_b = r0
    //     0x15dfb80: stur            w0, [x1, #0xb]
    // 0x15dfb84: r0 = 1
    //     0x15dfb84: movz            x0, #0x1
    // 0x15dfb88: StoreField: r1->field_13 = r0
    //     0x15dfb88: stur            x0, [x1, #0x13]
    // 0x15dfb8c: r0 = SvgPicture()
    //     0x15dfb8c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dfb90: stur            x0, [fp, #-8]
    // 0x15dfb94: ldur            x16, [fp, #-0x20]
    // 0x15dfb98: str             x16, [SP]
    // 0x15dfb9c: mov             x1, x0
    // 0x15dfba0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15dfba0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15dfba4: ldr             x2, [x2, #0xa40]
    // 0x15dfba8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dfba8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dfbac: ldr             x4, [x4, #0xa38]
    // 0x15dfbb0: r0 = SvgPicture.asset()
    //     0x15dfbb0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dfbb4: r0 = Align()
    //     0x15dfbb4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dfbb8: mov             x1, x0
    // 0x15dfbbc: r0 = Instance_Alignment
    //     0x15dfbbc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dfbc0: ldr             x0, [x0, #0xb10]
    // 0x15dfbc4: StoreField: r1->field_f = r0
    //     0x15dfbc4: stur            w0, [x1, #0xf]
    // 0x15dfbc8: r0 = 1.000000
    //     0x15dfbc8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dfbcc: StoreField: r1->field_13 = r0
    //     0x15dfbcc: stur            w0, [x1, #0x13]
    // 0x15dfbd0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dfbd0: stur            w0, [x1, #0x17]
    // 0x15dfbd4: ldur            x0, [fp, #-8]
    // 0x15dfbd8: StoreField: r1->field_b = r0
    //     0x15dfbd8: stur            w0, [x1, #0xb]
    // 0x15dfbdc: mov             x0, x1
    // 0x15dfbe0: stur            x0, [fp, #-8]
    // 0x15dfbe4: r0 = InkWell()
    //     0x15dfbe4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dfbe8: mov             x3, x0
    // 0x15dfbec: ldur            x0, [fp, #-8]
    // 0x15dfbf0: stur            x3, [fp, #-0x20]
    // 0x15dfbf4: StoreField: r3->field_b = r0
    //     0x15dfbf4: stur            w0, [x3, #0xb]
    // 0x15dfbf8: ldur            x2, [fp, #-0x18]
    // 0x15dfbfc: r1 = Function '<anonymous closure>':.
    //     0x15dfbfc: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a50] AnonymousClosure: (0x15cbc74), in [package:customer_app/app/presentation/views/line/browse/browse_view.dart] BrowseView::appBar (0x15e6ee8)
    //     0x15dfc00: ldr             x1, [x1, #0xa50]
    // 0x15dfc04: r0 = AllocateClosure()
    //     0x15dfc04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dfc08: ldur            x2, [fp, #-0x20]
    // 0x15dfc0c: StoreField: r2->field_f = r0
    //     0x15dfc0c: stur            w0, [x2, #0xf]
    // 0x15dfc10: r0 = true
    //     0x15dfc10: add             x0, NULL, #0x20  ; true
    // 0x15dfc14: StoreField: r2->field_43 = r0
    //     0x15dfc14: stur            w0, [x2, #0x43]
    // 0x15dfc18: r1 = Instance_BoxShape
    //     0x15dfc18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dfc1c: ldr             x1, [x1, #0x80]
    // 0x15dfc20: StoreField: r2->field_47 = r1
    //     0x15dfc20: stur            w1, [x2, #0x47]
    // 0x15dfc24: StoreField: r2->field_6f = r0
    //     0x15dfc24: stur            w0, [x2, #0x6f]
    // 0x15dfc28: r1 = false
    //     0x15dfc28: add             x1, NULL, #0x30  ; false
    // 0x15dfc2c: StoreField: r2->field_73 = r1
    //     0x15dfc2c: stur            w1, [x2, #0x73]
    // 0x15dfc30: StoreField: r2->field_83 = r0
    //     0x15dfc30: stur            w0, [x2, #0x83]
    // 0x15dfc34: StoreField: r2->field_7b = r1
    //     0x15dfc34: stur            w1, [x2, #0x7b]
    // 0x15dfc38: r0 = Obx()
    //     0x15dfc38: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15dfc3c: ldur            x2, [fp, #-0x18]
    // 0x15dfc40: r1 = Function '<anonymous closure>':.
    //     0x15dfc40: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a58] AnonymousClosure: (0x15dfcd8), in [package:customer_app/app/presentation/views/glass/browse/browse_view.dart] BrowseView::appBar (0x15dfa24)
    //     0x15dfc44: ldr             x1, [x1, #0xa58]
    // 0x15dfc48: stur            x0, [fp, #-8]
    // 0x15dfc4c: r0 = AllocateClosure()
    //     0x15dfc4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dfc50: mov             x1, x0
    // 0x15dfc54: ldur            x0, [fp, #-8]
    // 0x15dfc58: StoreField: r0->field_b = r1
    //     0x15dfc58: stur            w1, [x0, #0xb]
    // 0x15dfc5c: r1 = Null
    //     0x15dfc5c: mov             x1, NULL
    // 0x15dfc60: r2 = 2
    //     0x15dfc60: movz            x2, #0x2
    // 0x15dfc64: r0 = AllocateArray()
    //     0x15dfc64: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15dfc68: mov             x2, x0
    // 0x15dfc6c: ldur            x0, [fp, #-8]
    // 0x15dfc70: stur            x2, [fp, #-0x18]
    // 0x15dfc74: StoreField: r2->field_f = r0
    //     0x15dfc74: stur            w0, [x2, #0xf]
    // 0x15dfc78: r1 = <Widget>
    //     0x15dfc78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15dfc7c: r0 = AllocateGrowableArray()
    //     0x15dfc7c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15dfc80: mov             x1, x0
    // 0x15dfc84: ldur            x0, [fp, #-0x18]
    // 0x15dfc88: stur            x1, [fp, #-8]
    // 0x15dfc8c: StoreField: r1->field_f = r0
    //     0x15dfc8c: stur            w0, [x1, #0xf]
    // 0x15dfc90: r0 = 2
    //     0x15dfc90: movz            x0, #0x2
    // 0x15dfc94: StoreField: r1->field_b = r0
    //     0x15dfc94: stur            w0, [x1, #0xb]
    // 0x15dfc98: r0 = AppBar()
    //     0x15dfc98: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dfc9c: stur            x0, [fp, #-0x18]
    // 0x15dfca0: ldur            x16, [fp, #-0x10]
    // 0x15dfca4: ldur            lr, [fp, #-8]
    // 0x15dfca8: stp             lr, x16, [SP]
    // 0x15dfcac: mov             x1, x0
    // 0x15dfcb0: ldur            x2, [fp, #-0x20]
    // 0x15dfcb4: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15dfcb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15dfcb8: ldr             x4, [x4, #0xa58]
    // 0x15dfcbc: r0 = AppBar()
    //     0x15dfcbc: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dfcc0: ldur            x0, [fp, #-0x18]
    // 0x15dfcc4: LeaveFrame
    //     0x15dfcc4: mov             SP, fp
    //     0x15dfcc8: ldp             fp, lr, [SP], #0x10
    // 0x15dfccc: ret
    //     0x15dfccc: ret             
    // 0x15dfcd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dfcd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dfcd4: b               #0x15dfa44
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15dfcd8, size: 0x304
    // 0x15dfcd8: EnterFrame
    //     0x15dfcd8: stp             fp, lr, [SP, #-0x10]!
    //     0x15dfcdc: mov             fp, SP
    // 0x15dfce0: AllocStack(0x58)
    //     0x15dfce0: sub             SP, SP, #0x58
    // 0x15dfce4: SetupParameters()
    //     0x15dfce4: ldr             x0, [fp, #0x10]
    //     0x15dfce8: ldur            w2, [x0, #0x17]
    //     0x15dfcec: add             x2, x2, HEAP, lsl #32
    //     0x15dfcf0: stur            x2, [fp, #-8]
    // 0x15dfcf4: CheckStackOverflow
    //     0x15dfcf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dfcf8: cmp             SP, x16
    //     0x15dfcfc: b.ls            #0x15dffd4
    // 0x15dfd00: LoadField: r1 = r2->field_f
    //     0x15dfd00: ldur            w1, [x2, #0xf]
    // 0x15dfd04: DecompressPointer r1
    //     0x15dfd04: add             x1, x1, HEAP, lsl #32
    // 0x15dfd08: r0 = controller()
    //     0x15dfd08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dfd0c: LoadField: r1 = r0->field_4b
    //     0x15dfd0c: ldur            w1, [x0, #0x4b]
    // 0x15dfd10: DecompressPointer r1
    //     0x15dfd10: add             x1, x1, HEAP, lsl #32
    // 0x15dfd14: r0 = value()
    //     0x15dfd14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dfd18: LoadField: r1 = r0->field_1f
    //     0x15dfd18: ldur            w1, [x0, #0x1f]
    // 0x15dfd1c: DecompressPointer r1
    //     0x15dfd1c: add             x1, x1, HEAP, lsl #32
    // 0x15dfd20: cmp             w1, NULL
    // 0x15dfd24: b.ne            #0x15dfd30
    // 0x15dfd28: r0 = Null
    //     0x15dfd28: mov             x0, NULL
    // 0x15dfd2c: b               #0x15dfd38
    // 0x15dfd30: LoadField: r0 = r1->field_7
    //     0x15dfd30: ldur            w0, [x1, #7]
    // 0x15dfd34: DecompressPointer r0
    //     0x15dfd34: add             x0, x0, HEAP, lsl #32
    // 0x15dfd38: cmp             w0, NULL
    // 0x15dfd3c: b.ne            #0x15dfd48
    // 0x15dfd40: r0 = false
    //     0x15dfd40: add             x0, NULL, #0x30  ; false
    // 0x15dfd44: b               #0x15dff3c
    // 0x15dfd48: tbnz            w0, #4, #0x15dff38
    // 0x15dfd4c: ldur            x0, [fp, #-8]
    // 0x15dfd50: LoadField: r1 = r0->field_f
    //     0x15dfd50: ldur            w1, [x0, #0xf]
    // 0x15dfd54: DecompressPointer r1
    //     0x15dfd54: add             x1, x1, HEAP, lsl #32
    // 0x15dfd58: r0 = controller()
    //     0x15dfd58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dfd5c: LoadField: r1 = r0->field_5b
    //     0x15dfd5c: ldur            w1, [x0, #0x5b]
    // 0x15dfd60: DecompressPointer r1
    //     0x15dfd60: add             x1, x1, HEAP, lsl #32
    // 0x15dfd64: r0 = value()
    //     0x15dfd64: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dfd68: mov             x2, x0
    // 0x15dfd6c: ldur            x0, [fp, #-8]
    // 0x15dfd70: stur            x2, [fp, #-0x10]
    // 0x15dfd74: LoadField: r1 = r0->field_13
    //     0x15dfd74: ldur            w1, [x0, #0x13]
    // 0x15dfd78: DecompressPointer r1
    //     0x15dfd78: add             x1, x1, HEAP, lsl #32
    // 0x15dfd7c: r0 = of()
    //     0x15dfd7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dfd80: LoadField: r2 = r0->field_5b
    //     0x15dfd80: ldur            w2, [x0, #0x5b]
    // 0x15dfd84: DecompressPointer r2
    //     0x15dfd84: add             x2, x2, HEAP, lsl #32
    // 0x15dfd88: ldur            x0, [fp, #-8]
    // 0x15dfd8c: stur            x2, [fp, #-0x18]
    // 0x15dfd90: LoadField: r1 = r0->field_f
    //     0x15dfd90: ldur            w1, [x0, #0xf]
    // 0x15dfd94: DecompressPointer r1
    //     0x15dfd94: add             x1, x1, HEAP, lsl #32
    // 0x15dfd98: r0 = controller()
    //     0x15dfd98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dfd9c: LoadField: r1 = r0->field_5f
    //     0x15dfd9c: ldur            w1, [x0, #0x5f]
    // 0x15dfda0: DecompressPointer r1
    //     0x15dfda0: add             x1, x1, HEAP, lsl #32
    // 0x15dfda4: r0 = value()
    //     0x15dfda4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dfda8: cmp             w0, NULL
    // 0x15dfdac: r16 = true
    //     0x15dfdac: add             x16, NULL, #0x20  ; true
    // 0x15dfdb0: r17 = false
    //     0x15dfdb0: add             x17, NULL, #0x30  ; false
    // 0x15dfdb4: csel            x2, x16, x17, ne
    // 0x15dfdb8: ldur            x0, [fp, #-8]
    // 0x15dfdbc: stur            x2, [fp, #-0x20]
    // 0x15dfdc0: LoadField: r1 = r0->field_f
    //     0x15dfdc0: ldur            w1, [x0, #0xf]
    // 0x15dfdc4: DecompressPointer r1
    //     0x15dfdc4: add             x1, x1, HEAP, lsl #32
    // 0x15dfdc8: r0 = controller()
    //     0x15dfdc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dfdcc: LoadField: r1 = r0->field_5f
    //     0x15dfdcc: ldur            w1, [x0, #0x5f]
    // 0x15dfdd0: DecompressPointer r1
    //     0x15dfdd0: add             x1, x1, HEAP, lsl #32
    // 0x15dfdd4: r0 = value()
    //     0x15dfdd4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dfdd8: str             x0, [SP]
    // 0x15dfddc: r0 = _interpolateSingle()
    //     0x15dfddc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15dfde0: mov             x2, x0
    // 0x15dfde4: ldur            x0, [fp, #-8]
    // 0x15dfde8: stur            x2, [fp, #-0x28]
    // 0x15dfdec: LoadField: r1 = r0->field_13
    //     0x15dfdec: ldur            w1, [x0, #0x13]
    // 0x15dfdf0: DecompressPointer r1
    //     0x15dfdf0: add             x1, x1, HEAP, lsl #32
    // 0x15dfdf4: r0 = of()
    //     0x15dfdf4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dfdf8: LoadField: r1 = r0->field_87
    //     0x15dfdf8: ldur            w1, [x0, #0x87]
    // 0x15dfdfc: DecompressPointer r1
    //     0x15dfdfc: add             x1, x1, HEAP, lsl #32
    // 0x15dfe00: LoadField: r0 = r1->field_27
    //     0x15dfe00: ldur            w0, [x1, #0x27]
    // 0x15dfe04: DecompressPointer r0
    //     0x15dfe04: add             x0, x0, HEAP, lsl #32
    // 0x15dfe08: r16 = Instance_Color
    //     0x15dfe08: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15dfe0c: str             x16, [SP]
    // 0x15dfe10: mov             x1, x0
    // 0x15dfe14: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15dfe14: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15dfe18: ldr             x4, [x4, #0xf40]
    // 0x15dfe1c: r0 = copyWith()
    //     0x15dfe1c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15dfe20: stur            x0, [fp, #-0x30]
    // 0x15dfe24: r0 = Text()
    //     0x15dfe24: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15dfe28: mov             x2, x0
    // 0x15dfe2c: ldur            x0, [fp, #-0x28]
    // 0x15dfe30: stur            x2, [fp, #-0x38]
    // 0x15dfe34: StoreField: r2->field_b = r0
    //     0x15dfe34: stur            w0, [x2, #0xb]
    // 0x15dfe38: ldur            x0, [fp, #-0x30]
    // 0x15dfe3c: StoreField: r2->field_13 = r0
    //     0x15dfe3c: stur            w0, [x2, #0x13]
    // 0x15dfe40: ldur            x0, [fp, #-8]
    // 0x15dfe44: LoadField: r1 = r0->field_13
    //     0x15dfe44: ldur            w1, [x0, #0x13]
    // 0x15dfe48: DecompressPointer r1
    //     0x15dfe48: add             x1, x1, HEAP, lsl #32
    // 0x15dfe4c: r0 = of()
    //     0x15dfe4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dfe50: LoadField: r1 = r0->field_5b
    //     0x15dfe50: ldur            w1, [x0, #0x5b]
    // 0x15dfe54: DecompressPointer r1
    //     0x15dfe54: add             x1, x1, HEAP, lsl #32
    // 0x15dfe58: stur            x1, [fp, #-8]
    // 0x15dfe5c: r0 = ColorFilter()
    //     0x15dfe5c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dfe60: mov             x1, x0
    // 0x15dfe64: ldur            x0, [fp, #-8]
    // 0x15dfe68: stur            x1, [fp, #-0x28]
    // 0x15dfe6c: StoreField: r1->field_7 = r0
    //     0x15dfe6c: stur            w0, [x1, #7]
    // 0x15dfe70: r0 = Instance_BlendMode
    //     0x15dfe70: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dfe74: ldr             x0, [x0, #0xb30]
    // 0x15dfe78: StoreField: r1->field_b = r0
    //     0x15dfe78: stur            w0, [x1, #0xb]
    // 0x15dfe7c: r0 = 1
    //     0x15dfe7c: movz            x0, #0x1
    // 0x15dfe80: StoreField: r1->field_13 = r0
    //     0x15dfe80: stur            x0, [x1, #0x13]
    // 0x15dfe84: r0 = SvgPicture()
    //     0x15dfe84: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dfe88: stur            x0, [fp, #-8]
    // 0x15dfe8c: r16 = Instance_BoxFit
    //     0x15dfe8c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15dfe90: ldr             x16, [x16, #0xb18]
    // 0x15dfe94: r30 = 24.000000
    //     0x15dfe94: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15dfe98: ldr             lr, [lr, #0xba8]
    // 0x15dfe9c: stp             lr, x16, [SP, #0x10]
    // 0x15dfea0: r16 = 24.000000
    //     0x15dfea0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15dfea4: ldr             x16, [x16, #0xba8]
    // 0x15dfea8: ldur            lr, [fp, #-0x28]
    // 0x15dfeac: stp             lr, x16, [SP]
    // 0x15dfeb0: mov             x1, x0
    // 0x15dfeb4: r2 = "assets/images/shopping_bag.svg"
    //     0x15dfeb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15dfeb8: ldr             x2, [x2, #0xa60]
    // 0x15dfebc: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15dfebc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15dfec0: ldr             x4, [x4, #0xa68]
    // 0x15dfec4: r0 = SvgPicture.asset()
    //     0x15dfec4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dfec8: r0 = Badge()
    //     0x15dfec8: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15dfecc: mov             x1, x0
    // 0x15dfed0: ldur            x0, [fp, #-0x18]
    // 0x15dfed4: stur            x1, [fp, #-0x28]
    // 0x15dfed8: StoreField: r1->field_b = r0
    //     0x15dfed8: stur            w0, [x1, #0xb]
    // 0x15dfedc: ldur            x0, [fp, #-0x38]
    // 0x15dfee0: StoreField: r1->field_27 = r0
    //     0x15dfee0: stur            w0, [x1, #0x27]
    // 0x15dfee4: ldur            x0, [fp, #-0x20]
    // 0x15dfee8: StoreField: r1->field_2b = r0
    //     0x15dfee8: stur            w0, [x1, #0x2b]
    // 0x15dfeec: ldur            x0, [fp, #-8]
    // 0x15dfef0: StoreField: r1->field_2f = r0
    //     0x15dfef0: stur            w0, [x1, #0x2f]
    // 0x15dfef4: r0 = Visibility()
    //     0x15dfef4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15dfef8: mov             x1, x0
    // 0x15dfefc: ldur            x0, [fp, #-0x28]
    // 0x15dff00: StoreField: r1->field_b = r0
    //     0x15dff00: stur            w0, [x1, #0xb]
    // 0x15dff04: r0 = Instance_SizedBox
    //     0x15dff04: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15dff08: StoreField: r1->field_f = r0
    //     0x15dff08: stur            w0, [x1, #0xf]
    // 0x15dff0c: ldur            x0, [fp, #-0x10]
    // 0x15dff10: StoreField: r1->field_13 = r0
    //     0x15dff10: stur            w0, [x1, #0x13]
    // 0x15dff14: r0 = false
    //     0x15dff14: add             x0, NULL, #0x30  ; false
    // 0x15dff18: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dff18: stur            w0, [x1, #0x17]
    // 0x15dff1c: StoreField: r1->field_1b = r0
    //     0x15dff1c: stur            w0, [x1, #0x1b]
    // 0x15dff20: StoreField: r1->field_1f = r0
    //     0x15dff20: stur            w0, [x1, #0x1f]
    // 0x15dff24: StoreField: r1->field_23 = r0
    //     0x15dff24: stur            w0, [x1, #0x23]
    // 0x15dff28: StoreField: r1->field_27 = r0
    //     0x15dff28: stur            w0, [x1, #0x27]
    // 0x15dff2c: StoreField: r1->field_2b = r0
    //     0x15dff2c: stur            w0, [x1, #0x2b]
    // 0x15dff30: mov             x0, x1
    // 0x15dff34: b               #0x15dff54
    // 0x15dff38: r0 = false
    //     0x15dff38: add             x0, NULL, #0x30  ; false
    // 0x15dff3c: r0 = Container()
    //     0x15dff3c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15dff40: mov             x1, x0
    // 0x15dff44: stur            x0, [fp, #-8]
    // 0x15dff48: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15dff48: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15dff4c: r0 = Container()
    //     0x15dff4c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15dff50: ldur            x0, [fp, #-8]
    // 0x15dff54: stur            x0, [fp, #-8]
    // 0x15dff58: r0 = InkWell()
    //     0x15dff58: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dff5c: mov             x3, x0
    // 0x15dff60: ldur            x0, [fp, #-8]
    // 0x15dff64: stur            x3, [fp, #-0x10]
    // 0x15dff68: StoreField: r3->field_b = r0
    //     0x15dff68: stur            w0, [x3, #0xb]
    // 0x15dff6c: r1 = Function '<anonymous closure>':.
    //     0x15dff6c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41a60] AnonymousClosure: (0x15cae64), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15dff70: ldr             x1, [x1, #0xa60]
    // 0x15dff74: r2 = Null
    //     0x15dff74: mov             x2, NULL
    // 0x15dff78: r0 = AllocateClosure()
    //     0x15dff78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dff7c: mov             x1, x0
    // 0x15dff80: ldur            x0, [fp, #-0x10]
    // 0x15dff84: StoreField: r0->field_f = r1
    //     0x15dff84: stur            w1, [x0, #0xf]
    // 0x15dff88: r1 = true
    //     0x15dff88: add             x1, NULL, #0x20  ; true
    // 0x15dff8c: StoreField: r0->field_43 = r1
    //     0x15dff8c: stur            w1, [x0, #0x43]
    // 0x15dff90: r2 = Instance_BoxShape
    //     0x15dff90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dff94: ldr             x2, [x2, #0x80]
    // 0x15dff98: StoreField: r0->field_47 = r2
    //     0x15dff98: stur            w2, [x0, #0x47]
    // 0x15dff9c: StoreField: r0->field_6f = r1
    //     0x15dff9c: stur            w1, [x0, #0x6f]
    // 0x15dffa0: r2 = false
    //     0x15dffa0: add             x2, NULL, #0x30  ; false
    // 0x15dffa4: StoreField: r0->field_73 = r2
    //     0x15dffa4: stur            w2, [x0, #0x73]
    // 0x15dffa8: StoreField: r0->field_83 = r1
    //     0x15dffa8: stur            w1, [x0, #0x83]
    // 0x15dffac: StoreField: r0->field_7b = r2
    //     0x15dffac: stur            w2, [x0, #0x7b]
    // 0x15dffb0: r0 = Padding()
    //     0x15dffb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15dffb4: r1 = Instance_EdgeInsets
    //     0x15dffb4: add             x1, PP, #0x37, lsl #12  ; [pp+0x37290] Obj!EdgeInsets@d5a111
    //     0x15dffb8: ldr             x1, [x1, #0x290]
    // 0x15dffbc: StoreField: r0->field_f = r1
    //     0x15dffbc: stur            w1, [x0, #0xf]
    // 0x15dffc0: ldur            x1, [fp, #-0x10]
    // 0x15dffc4: StoreField: r0->field_b = r1
    //     0x15dffc4: stur            w1, [x0, #0xb]
    // 0x15dffc8: LeaveFrame
    //     0x15dffc8: mov             SP, fp
    //     0x15dffcc: ldp             fp, lr, [SP], #0x10
    // 0x15dffd0: ret
    //     0x15dffd0: ret             
    // 0x15dffd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dffd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dffd8: b               #0x15dfd00
  }
}
