// lib: , url: package:customer_app/app/presentation/views/cosmetic/search/search_page.dart

// class id: 1049336, size: 0x8
class :: {
}

// class id: 4583, size: 0x14, field offset: 0x14
//   const constructor, 
class SearchPage extends BaseView<dynamic> {

  _ body(/* No info */) {
    // ** addr: 0x14d21dc, size: 0xe4
    // 0x14d21dc: EnterFrame
    //     0x14d21dc: stp             fp, lr, [SP, #-0x10]!
    //     0x14d21e0: mov             fp, SP
    // 0x14d21e4: AllocStack(0x20)
    //     0x14d21e4: sub             SP, SP, #0x20
    // 0x14d21e8: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */)
    //     0x14d21e8: mov             x0, x1
    //     0x14d21ec: stur            x1, [fp, #-8]
    // 0x14d21f0: CheckStackOverflow
    //     0x14d21f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d21f4: cmp             SP, x16
    //     0x14d21f8: b.ls            #0x14d22b8
    // 0x14d21fc: r1 = 1
    //     0x14d21fc: movz            x1, #0x1
    // 0x14d2200: r0 = AllocateContext()
    //     0x14d2200: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d2204: ldur            x2, [fp, #-8]
    // 0x14d2208: stur            x0, [fp, #-0x10]
    // 0x14d220c: StoreField: r0->field_f = r2
    //     0x14d220c: stur            w2, [x0, #0xf]
    // 0x14d2210: r0 = Obx()
    //     0x14d2210: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d2214: ldur            x2, [fp, #-0x10]
    // 0x14d2218: r1 = Function '<anonymous closure>':.
    //     0x14d2218: add             x1, PP, #0x41, lsl #12  ; [pp+0x41ba8] AnonymousClosure: (0x14d22f8), in [package:customer_app/app/presentation/views/cosmetic/search/search_page.dart] SearchPage::body (0x14d21dc)
    //     0x14d221c: ldr             x1, [x1, #0xba8]
    // 0x14d2220: stur            x0, [fp, #-0x18]
    // 0x14d2224: r0 = AllocateClosure()
    //     0x14d2224: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2228: mov             x1, x0
    // 0x14d222c: ldur            x0, [fp, #-0x18]
    // 0x14d2230: StoreField: r0->field_b = r1
    //     0x14d2230: stur            w1, [x0, #0xb]
    // 0x14d2234: r0 = WillPopScope()
    //     0x14d2234: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14d2238: mov             x3, x0
    // 0x14d223c: ldur            x0, [fp, #-0x18]
    // 0x14d2240: stur            x3, [fp, #-0x20]
    // 0x14d2244: StoreField: r3->field_b = r0
    //     0x14d2244: stur            w0, [x3, #0xb]
    // 0x14d2248: ldur            x2, [fp, #-8]
    // 0x14d224c: r1 = Function 'onBackPress':.
    //     0x14d224c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bb0] AnonymousClosure: (0x14d22c0), in [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress (0x1479784)
    //     0x14d2250: ldr             x1, [x1, #0xbb0]
    // 0x14d2254: r0 = AllocateClosure()
    //     0x14d2254: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2258: mov             x1, x0
    // 0x14d225c: ldur            x0, [fp, #-0x20]
    // 0x14d2260: StoreField: r0->field_f = r1
    //     0x14d2260: stur            w1, [x0, #0xf]
    // 0x14d2264: ldur            x2, [fp, #-0x10]
    // 0x14d2268: r1 = Function '<anonymous closure>':.
    //     0x14d2268: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bb8] AnonymousClosure: (0x14796e4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d226c: ldr             x1, [x1, #0xbb8]
    // 0x14d2270: r0 = AllocateClosure()
    //     0x14d2270: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2274: ldur            x2, [fp, #-0x10]
    // 0x14d2278: r1 = Function '<anonymous closure>':.
    //     0x14d2278: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bc0] AnonymousClosure: (0x1479614), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d227c: ldr             x1, [x1, #0xbc0]
    // 0x14d2280: stur            x0, [fp, #-8]
    // 0x14d2284: r0 = AllocateClosure()
    //     0x14d2284: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2288: stur            x0, [fp, #-0x10]
    // 0x14d228c: r0 = PagingView()
    //     0x14d228c: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x14d2290: mov             x1, x0
    // 0x14d2294: ldur            x2, [fp, #-0x20]
    // 0x14d2298: ldur            x3, [fp, #-0x10]
    // 0x14d229c: ldur            x5, [fp, #-8]
    // 0x14d22a0: stur            x0, [fp, #-8]
    // 0x14d22a4: r0 = PagingView()
    //     0x14d22a4: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x14d22a8: ldur            x0, [fp, #-8]
    // 0x14d22ac: LeaveFrame
    //     0x14d22ac: mov             SP, fp
    //     0x14d22b0: ldp             fp, lr, [SP], #0x10
    // 0x14d22b4: ret
    //     0x14d22b4: ret             
    // 0x14d22b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d22b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d22bc: b               #0x14d21fc
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x14d22c0, size: 0x38
    // 0x14d22c0: EnterFrame
    //     0x14d22c0: stp             fp, lr, [SP, #-0x10]!
    //     0x14d22c4: mov             fp, SP
    // 0x14d22c8: ldr             x0, [fp, #0x10]
    // 0x14d22cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d22cc: ldur            w1, [x0, #0x17]
    // 0x14d22d0: DecompressPointer r1
    //     0x14d22d0: add             x1, x1, HEAP, lsl #32
    // 0x14d22d4: CheckStackOverflow
    //     0x14d22d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d22d8: cmp             SP, x16
    //     0x14d22dc: b.ls            #0x14d22f0
    // 0x14d22e0: r0 = onBackPress()
    //     0x14d22e0: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x14d22e4: LeaveFrame
    //     0x14d22e4: mov             SP, fp
    //     0x14d22e8: ldp             fp, lr, [SP], #0x10
    // 0x14d22ec: ret
    //     0x14d22ec: ret             
    // 0x14d22f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d22f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d22f4: b               #0x14d22e0
  }
  [closure] Column <anonymous closure>(dynamic) {
    // ** addr: 0x14d22f8, size: 0x2d4
    // 0x14d22f8: EnterFrame
    //     0x14d22f8: stp             fp, lr, [SP, #-0x10]!
    //     0x14d22fc: mov             fp, SP
    // 0x14d2300: AllocStack(0xd0)
    //     0x14d2300: sub             SP, SP, #0xd0
    // 0x14d2304: SetupParameters()
    //     0x14d2304: ldr             x0, [fp, #0x10]
    //     0x14d2308: ldur            w2, [x0, #0x17]
    //     0x14d230c: add             x2, x2, HEAP, lsl #32
    //     0x14d2310: stur            x2, [fp, #-8]
    // 0x14d2314: CheckStackOverflow
    //     0x14d2314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d2318: cmp             SP, x16
    //     0x14d231c: b.ls            #0x14d25c4
    // 0x14d2320: LoadField: r1 = r2->field_f
    //     0x14d2320: ldur            w1, [x2, #0xf]
    // 0x14d2324: DecompressPointer r1
    //     0x14d2324: add             x1, x1, HEAP, lsl #32
    // 0x14d2328: r0 = controller()
    //     0x14d2328: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d232c: LoadField: r1 = r0->field_57
    //     0x14d232c: ldur            w1, [x0, #0x57]
    // 0x14d2330: DecompressPointer r1
    //     0x14d2330: add             x1, x1, HEAP, lsl #32
    // 0x14d2334: r0 = value()
    //     0x14d2334: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d2338: ldur            x2, [fp, #-8]
    // 0x14d233c: stur            x0, [fp, #-0x10]
    // 0x14d2340: LoadField: r1 = r2->field_f
    //     0x14d2340: ldur            w1, [x2, #0xf]
    // 0x14d2344: DecompressPointer r1
    //     0x14d2344: add             x1, x1, HEAP, lsl #32
    // 0x14d2348: r0 = controller()
    //     0x14d2348: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d234c: LoadField: r1 = r0->field_57
    //     0x14d234c: ldur            w1, [x0, #0x57]
    // 0x14d2350: DecompressPointer r1
    //     0x14d2350: add             x1, x1, HEAP, lsl #32
    // 0x14d2354: r0 = value()
    //     0x14d2354: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d2358: LoadField: r1 = r0->field_b
    //     0x14d2358: ldur            w1, [x0, #0xb]
    // 0x14d235c: DecompressPointer r1
    //     0x14d235c: add             x1, x1, HEAP, lsl #32
    // 0x14d2360: cmp             w1, NULL
    // 0x14d2364: b.ne            #0x14d2370
    // 0x14d2368: r0 = Null
    //     0x14d2368: mov             x0, NULL
    // 0x14d236c: b               #0x14d2384
    // 0x14d2370: LoadField: r0 = r1->field_f
    //     0x14d2370: ldur            w0, [x1, #0xf]
    // 0x14d2374: DecompressPointer r0
    //     0x14d2374: add             x0, x0, HEAP, lsl #32
    // 0x14d2378: LoadField: r1 = r0->field_b
    //     0x14d2378: ldur            w1, [x0, #0xb]
    // 0x14d237c: DecompressPointer r1
    //     0x14d237c: add             x1, x1, HEAP, lsl #32
    // 0x14d2380: mov             x0, x1
    // 0x14d2384: cmp             w0, NULL
    // 0x14d2388: b.ne            #0x14d2394
    // 0x14d238c: r3 = ""
    //     0x14d238c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14d2390: b               #0x14d2398
    // 0x14d2394: mov             x3, x0
    // 0x14d2398: ldur            x2, [fp, #-8]
    // 0x14d239c: stur            x3, [fp, #-0x18]
    // 0x14d23a0: LoadField: r1 = r2->field_f
    //     0x14d23a0: ldur            w1, [x2, #0xf]
    // 0x14d23a4: DecompressPointer r1
    //     0x14d23a4: add             x1, x1, HEAP, lsl #32
    // 0x14d23a8: r0 = controller()
    //     0x14d23a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d23ac: LoadField: r1 = r0->field_67
    //     0x14d23ac: ldur            w1, [x0, #0x67]
    // 0x14d23b0: DecompressPointer r1
    //     0x14d23b0: add             x1, x1, HEAP, lsl #32
    // 0x14d23b4: r0 = value()
    //     0x14d23b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d23b8: ldur            x2, [fp, #-8]
    // 0x14d23bc: stur            x0, [fp, #-0x20]
    // 0x14d23c0: LoadField: r1 = r2->field_f
    //     0x14d23c0: ldur            w1, [x2, #0xf]
    // 0x14d23c4: DecompressPointer r1
    //     0x14d23c4: add             x1, x1, HEAP, lsl #32
    // 0x14d23c8: r0 = controller()
    //     0x14d23c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d23cc: LoadField: r1 = r0->field_4f
    //     0x14d23cc: ldur            w1, [x0, #0x4f]
    // 0x14d23d0: DecompressPointer r1
    //     0x14d23d0: add             x1, x1, HEAP, lsl #32
    // 0x14d23d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x14d23d4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x14d23d8: r0 = toList()
    //     0x14d23d8: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x14d23dc: ldur            x2, [fp, #-8]
    // 0x14d23e0: stur            x0, [fp, #-0x28]
    // 0x14d23e4: LoadField: r1 = r2->field_f
    //     0x14d23e4: ldur            w1, [x2, #0xf]
    // 0x14d23e8: DecompressPointer r1
    //     0x14d23e8: add             x1, x1, HEAP, lsl #32
    // 0x14d23ec: r0 = controller()
    //     0x14d23ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d23f0: LoadField: r1 = r0->field_5b
    //     0x14d23f0: ldur            w1, [x0, #0x5b]
    // 0x14d23f4: DecompressPointer r1
    //     0x14d23f4: add             x1, x1, HEAP, lsl #32
    // 0x14d23f8: r0 = value()
    //     0x14d23f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d23fc: ldur            x2, [fp, #-8]
    // 0x14d2400: r1 = Function '<anonymous closure>':.
    //     0x14d2400: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bc8] AnonymousClosure: (0x13b3d00), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d2404: ldr             x1, [x1, #0xbc8]
    // 0x14d2408: stur            x0, [fp, #-0x30]
    // 0x14d240c: r0 = AllocateClosure()
    //     0x14d240c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2410: ldur            x2, [fp, #-8]
    // 0x14d2414: r1 = Function '<anonymous closure>':.
    //     0x14d2414: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bd0] AnonymousClosure: (0x13b3290), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d2418: ldr             x1, [x1, #0xbd0]
    // 0x14d241c: stur            x0, [fp, #-0x38]
    // 0x14d2420: r0 = AllocateClosure()
    //     0x14d2420: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2424: ldur            x2, [fp, #-8]
    // 0x14d2428: r1 = Function '<anonymous closure>':.
    //     0x14d2428: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bd8] AnonymousClosure: (0x13b3208), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d242c: ldr             x1, [x1, #0xbd8]
    // 0x14d2430: stur            x0, [fp, #-0x40]
    // 0x14d2434: r0 = AllocateClosure()
    //     0x14d2434: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2438: ldur            x2, [fp, #-8]
    // 0x14d243c: r1 = Function '<anonymous closure>':.
    //     0x14d243c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41be0] AnonymousClosure: (0x13b3058), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d2440: ldr             x1, [x1, #0xbe0]
    // 0x14d2444: stur            x0, [fp, #-0x48]
    // 0x14d2448: r0 = AllocateClosure()
    //     0x14d2448: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d244c: ldur            x2, [fp, #-8]
    // 0x14d2450: r1 = Function '<anonymous closure>':.
    //     0x14d2450: add             x1, PP, #0x41, lsl #12  ; [pp+0x41be8] AnonymousClosure: (0x13b2fc4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d2454: ldr             x1, [x1, #0xbe8]
    // 0x14d2458: stur            x0, [fp, #-0x50]
    // 0x14d245c: r0 = AllocateClosure()
    //     0x14d245c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2460: ldur            x2, [fp, #-8]
    // 0x14d2464: r1 = Function '<anonymous closure>':.
    //     0x14d2464: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bf0] AnonymousClosure: (0x13b2cdc), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d2468: ldr             x1, [x1, #0xbf0]
    // 0x14d246c: stur            x0, [fp, #-0x58]
    // 0x14d2470: r0 = AllocateClosure()
    //     0x14d2470: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2474: ldur            x2, [fp, #-8]
    // 0x14d2478: r1 = Function '<anonymous closure>':.
    //     0x14d2478: add             x1, PP, #0x41, lsl #12  ; [pp+0x41bf8] AnonymousClosure: (0x14d25d8), in [package:customer_app/app/presentation/views/cosmetic/search/search_page.dart] SearchPage::body (0x14d21dc)
    //     0x14d247c: ldr             x1, [x1, #0xbf8]
    // 0x14d2480: stur            x0, [fp, #-0x60]
    // 0x14d2484: r0 = AllocateClosure()
    //     0x14d2484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d2488: ldur            x2, [fp, #-8]
    // 0x14d248c: r1 = Function '<anonymous closure>':.
    //     0x14d248c: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c00] AnonymousClosure: (0x13b276c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d2490: ldr             x1, [x1, #0xc00]
    // 0x14d2494: stur            x0, [fp, #-0x68]
    // 0x14d2498: r0 = AllocateClosure()
    //     0x14d2498: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d249c: ldur            x2, [fp, #-8]
    // 0x14d24a0: r1 = Function '<anonymous closure>':.
    //     0x14d24a0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c08] AnonymousClosure: (0x13b0b1c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d24a4: ldr             x1, [x1, #0xc08]
    // 0x14d24a8: stur            x0, [fp, #-0x70]
    // 0x14d24ac: r0 = AllocateClosure()
    //     0x14d24ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d24b0: ldur            x2, [fp, #-8]
    // 0x14d24b4: r1 = Function '<anonymous closure>':.
    //     0x14d24b4: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c10] AnonymousClosure: (0x13b1124), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x14d24b8: ldr             x1, [x1, #0xc10]
    // 0x14d24bc: stur            x0, [fp, #-8]
    // 0x14d24c0: r0 = AllocateClosure()
    //     0x14d24c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d24c4: stur            x0, [fp, #-0x78]
    // 0x14d24c8: r0 = SearchView()
    //     0x14d24c8: bl              #0x14d25cc  ; AllocateSearchViewStub -> SearchView (size=0x48)
    // 0x14d24cc: stur            x0, [fp, #-0x80]
    // 0x14d24d0: ldur            x16, [fp, #-0x28]
    // 0x14d24d4: ldur            lr, [fp, #-0x78]
    // 0x14d24d8: stp             lr, x16, [SP, #0x40]
    // 0x14d24dc: ldur            x16, [fp, #-0x48]
    // 0x14d24e0: ldur            lr, [fp, #-0x40]
    // 0x14d24e4: stp             lr, x16, [SP, #0x30]
    // 0x14d24e8: ldur            x16, [fp, #-0x38]
    // 0x14d24ec: ldur            lr, [fp, #-8]
    // 0x14d24f0: stp             lr, x16, [SP, #0x20]
    // 0x14d24f4: ldur            x16, [fp, #-0x50]
    // 0x14d24f8: ldur            lr, [fp, #-0x68]
    // 0x14d24fc: stp             lr, x16, [SP, #0x10]
    // 0x14d2500: ldur            x16, [fp, #-0x30]
    // 0x14d2504: ldur            lr, [fp, #-0x70]
    // 0x14d2508: stp             lr, x16, [SP]
    // 0x14d250c: mov             x1, x0
    // 0x14d2510: ldur            x2, [fp, #-0x10]
    // 0x14d2514: ldur            x3, [fp, #-0x18]
    // 0x14d2518: ldur            x5, [fp, #-0x60]
    // 0x14d251c: ldur            x6, [fp, #-0x20]
    // 0x14d2520: ldur            x7, [fp, #-0x58]
    // 0x14d2524: r0 = SearchView()
    //     0x14d2524: bl              #0x1479b30  ; [package:customer_app/app/presentation/views/basic/search/widgets/search_view.dart] SearchView::SearchView
    // 0x14d2528: r1 = Null
    //     0x14d2528: mov             x1, NULL
    // 0x14d252c: r2 = 2
    //     0x14d252c: movz            x2, #0x2
    // 0x14d2530: r0 = AllocateArray()
    //     0x14d2530: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d2534: mov             x2, x0
    // 0x14d2538: ldur            x0, [fp, #-0x80]
    // 0x14d253c: stur            x2, [fp, #-8]
    // 0x14d2540: StoreField: r2->field_f = r0
    //     0x14d2540: stur            w0, [x2, #0xf]
    // 0x14d2544: r1 = <Widget>
    //     0x14d2544: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d2548: r0 = AllocateGrowableArray()
    //     0x14d2548: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d254c: mov             x1, x0
    // 0x14d2550: ldur            x0, [fp, #-8]
    // 0x14d2554: stur            x1, [fp, #-0x10]
    // 0x14d2558: StoreField: r1->field_f = r0
    //     0x14d2558: stur            w0, [x1, #0xf]
    // 0x14d255c: r0 = 2
    //     0x14d255c: movz            x0, #0x2
    // 0x14d2560: StoreField: r1->field_b = r0
    //     0x14d2560: stur            w0, [x1, #0xb]
    // 0x14d2564: r0 = Column()
    //     0x14d2564: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14d2568: r1 = Instance_Axis
    //     0x14d2568: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d256c: StoreField: r0->field_f = r1
    //     0x14d256c: stur            w1, [x0, #0xf]
    // 0x14d2570: r1 = Instance_MainAxisAlignment
    //     0x14d2570: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d2574: ldr             x1, [x1, #0xa08]
    // 0x14d2578: StoreField: r0->field_13 = r1
    //     0x14d2578: stur            w1, [x0, #0x13]
    // 0x14d257c: r1 = Instance_MainAxisSize
    //     0x14d257c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d2580: ldr             x1, [x1, #0xa10]
    // 0x14d2584: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d2584: stur            w1, [x0, #0x17]
    // 0x14d2588: r1 = Instance_CrossAxisAlignment
    //     0x14d2588: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d258c: ldr             x1, [x1, #0xa18]
    // 0x14d2590: StoreField: r0->field_1b = r1
    //     0x14d2590: stur            w1, [x0, #0x1b]
    // 0x14d2594: r1 = Instance_VerticalDirection
    //     0x14d2594: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d2598: ldr             x1, [x1, #0xa20]
    // 0x14d259c: StoreField: r0->field_23 = r1
    //     0x14d259c: stur            w1, [x0, #0x23]
    // 0x14d25a0: r1 = Instance_Clip
    //     0x14d25a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d25a4: ldr             x1, [x1, #0x38]
    // 0x14d25a8: StoreField: r0->field_2b = r1
    //     0x14d25a8: stur            w1, [x0, #0x2b]
    // 0x14d25ac: StoreField: r0->field_2f = rZR
    //     0x14d25ac: stur            xzr, [x0, #0x2f]
    // 0x14d25b0: ldur            x1, [fp, #-0x10]
    // 0x14d25b4: StoreField: r0->field_b = r1
    //     0x14d25b4: stur            w1, [x0, #0xb]
    // 0x14d25b8: LeaveFrame
    //     0x14d25b8: mov             SP, fp
    //     0x14d25bc: ldp             fp, lr, [SP], #0x10
    // 0x14d25c0: ret
    //     0x14d25c0: ret             
    // 0x14d25c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d25c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d25c8: b               #0x14d2320
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x14d25d8, size: 0x180
    // 0x14d25d8: EnterFrame
    //     0x14d25d8: stp             fp, lr, [SP, #-0x10]!
    //     0x14d25dc: mov             fp, SP
    // 0x14d25e0: AllocStack(0x10)
    //     0x14d25e0: sub             SP, SP, #0x10
    // 0x14d25e4: SetupParameters()
    //     0x14d25e4: ldr             x0, [fp, #0x38]
    //     0x14d25e8: ldur            w1, [x0, #0x17]
    //     0x14d25ec: add             x1, x1, HEAP, lsl #32
    // 0x14d25f0: CheckStackOverflow
    //     0x14d25f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d25f4: cmp             SP, x16
    //     0x14d25f8: b.ls            #0x14d2750
    // 0x14d25fc: LoadField: r0 = r1->field_f
    //     0x14d25fc: ldur            w0, [x1, #0xf]
    // 0x14d2600: DecompressPointer r0
    //     0x14d2600: add             x0, x0, HEAP, lsl #32
    // 0x14d2604: mov             x1, x0
    // 0x14d2608: r0 = controller()
    //     0x14d2608: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d260c: mov             x3, x0
    // 0x14d2610: ldr             x0, [fp, #0x28]
    // 0x14d2614: r2 = Null
    //     0x14d2614: mov             x2, NULL
    // 0x14d2618: r1 = Null
    //     0x14d2618: mov             x1, NULL
    // 0x14d261c: stur            x3, [fp, #-8]
    // 0x14d2620: r4 = 60
    //     0x14d2620: movz            x4, #0x3c
    // 0x14d2624: branchIfSmi(r0, 0x14d2630)
    //     0x14d2624: tbz             w0, #0, #0x14d2630
    // 0x14d2628: r4 = LoadClassIdInstr(r0)
    //     0x14d2628: ldur            x4, [x0, #-1]
    //     0x14d262c: ubfx            x4, x4, #0xc, #0x14
    // 0x14d2630: sub             x4, x4, #0x5e
    // 0x14d2634: cmp             x4, #1
    // 0x14d2638: b.ls            #0x14d264c
    // 0x14d263c: r8 = String
    //     0x14d263c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14d2640: r3 = Null
    //     0x14d2640: add             x3, PP, #0x41, lsl #12  ; [pp+0x41c18] Null
    //     0x14d2644: ldr             x3, [x3, #0xc18]
    // 0x14d2648: r0 = String()
    //     0x14d2648: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14d264c: ldr             x0, [fp, #0x20]
    // 0x14d2650: r2 = Null
    //     0x14d2650: mov             x2, NULL
    // 0x14d2654: r1 = Null
    //     0x14d2654: mov             x1, NULL
    // 0x14d2658: r4 = 60
    //     0x14d2658: movz            x4, #0x3c
    // 0x14d265c: branchIfSmi(r0, 0x14d2668)
    //     0x14d265c: tbz             w0, #0, #0x14d2668
    // 0x14d2660: r4 = LoadClassIdInstr(r0)
    //     0x14d2660: ldur            x4, [x0, #-1]
    //     0x14d2664: ubfx            x4, x4, #0xc, #0x14
    // 0x14d2668: sub             x4, x4, #0x5e
    // 0x14d266c: cmp             x4, #1
    // 0x14d2670: b.ls            #0x14d2684
    // 0x14d2674: r8 = String
    //     0x14d2674: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x14d2678: r3 = Null
    //     0x14d2678: add             x3, PP, #0x41, lsl #12  ; [pp+0x41c28] Null
    //     0x14d267c: ldr             x3, [x3, #0xc28]
    // 0x14d2680: r0 = String()
    //     0x14d2680: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x14d2684: ldr             x0, [fp, #0x18]
    // 0x14d2688: r2 = Null
    //     0x14d2688: mov             x2, NULL
    // 0x14d268c: r1 = Null
    //     0x14d268c: mov             x1, NULL
    // 0x14d2690: branchIfSmi(r0, 0x14d26b8)
    //     0x14d2690: tbz             w0, #0, #0x14d26b8
    // 0x14d2694: r4 = LoadClassIdInstr(r0)
    //     0x14d2694: ldur            x4, [x0, #-1]
    //     0x14d2698: ubfx            x4, x4, #0xc, #0x14
    // 0x14d269c: sub             x4, x4, #0x3c
    // 0x14d26a0: cmp             x4, #1
    // 0x14d26a4: b.ls            #0x14d26b8
    // 0x14d26a8: r8 = int
    //     0x14d26a8: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x14d26ac: r3 = Null
    //     0x14d26ac: add             x3, PP, #0x41, lsl #12  ; [pp+0x41c38] Null
    //     0x14d26b0: ldr             x3, [x3, #0xc38]
    // 0x14d26b4: r0 = int()
    //     0x14d26b4: bl              #0x16fc548  ; IsType_int_Stub
    // 0x14d26b8: ldr             x0, [fp, #0x10]
    // 0x14d26bc: cmp             w0, NULL
    // 0x14d26c0: b.ne            #0x14d26d0
    // 0x14d26c4: r0 = ProductRating()
    //     0x14d26c4: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0x14d26c8: mov             x4, x0
    // 0x14d26cc: b               #0x14d26d4
    // 0x14d26d0: mov             x4, x0
    // 0x14d26d4: ldr             x3, [fp, #0x18]
    // 0x14d26d8: mov             x0, x4
    // 0x14d26dc: stur            x4, [fp, #-0x10]
    // 0x14d26e0: r2 = Null
    //     0x14d26e0: mov             x2, NULL
    // 0x14d26e4: r1 = Null
    //     0x14d26e4: mov             x1, NULL
    // 0x14d26e8: r4 = 60
    //     0x14d26e8: movz            x4, #0x3c
    // 0x14d26ec: branchIfSmi(r0, 0x14d26f8)
    //     0x14d26ec: tbz             w0, #0, #0x14d26f8
    // 0x14d26f0: r4 = LoadClassIdInstr(r0)
    //     0x14d26f0: ldur            x4, [x0, #-1]
    //     0x14d26f4: ubfx            x4, x4, #0xc, #0x14
    // 0x14d26f8: r17 = 5178
    //     0x14d26f8: movz            x17, #0x143a
    // 0x14d26fc: cmp             x4, x17
    // 0x14d2700: b.eq            #0x14d2718
    // 0x14d2704: r8 = ProductRating
    //     0x14d2704: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ee30] Type: ProductRating
    //     0x14d2708: ldr             x8, [x8, #0xe30]
    // 0x14d270c: r3 = Null
    //     0x14d270c: add             x3, PP, #0x41, lsl #12  ; [pp+0x41c48] Null
    //     0x14d2710: ldr             x3, [x3, #0xc48]
    // 0x14d2714: r0 = DefaultTypeTest()
    //     0x14d2714: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x14d2718: ldr             x0, [fp, #0x18]
    // 0x14d271c: r6 = LoadInt32Instr(r0)
    //     0x14d271c: sbfx            x6, x0, #1, #0x1f
    //     0x14d2720: tbz             w0, #0, #0x14d2728
    //     0x14d2724: ldur            x6, [x0, #7]
    // 0x14d2728: ldur            x1, [fp, #-8]
    // 0x14d272c: ldr             x2, [fp, #0x30]
    // 0x14d2730: ldr             x3, [fp, #0x28]
    // 0x14d2734: ldr             x5, [fp, #0x20]
    // 0x14d2738: ldur            x7, [fp, #-0x10]
    // 0x14d273c: r0 = productViewedEvent()
    //     0x14d273c: bl              #0x13b2a20  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::productViewedEvent
    // 0x14d2740: r0 = Null
    //     0x14d2740: mov             x0, NULL
    // 0x14d2744: LeaveFrame
    //     0x14d2744: mov             SP, fp
    //     0x14d2748: ldp             fp, lr, [SP], #0x10
    // 0x14d274c: ret
    //     0x14d274c: ret             
    // 0x14d2750: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d2750: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d2754: b               #0x14d25fc
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15deca8, size: 0x18c
    // 0x15deca8: EnterFrame
    //     0x15deca8: stp             fp, lr, [SP, #-0x10]!
    //     0x15decac: mov             fp, SP
    // 0x15decb0: AllocStack(0x28)
    //     0x15decb0: sub             SP, SP, #0x28
    // 0x15decb4: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15decb4: mov             x0, x1
    //     0x15decb8: stur            x1, [fp, #-8]
    //     0x15decbc: mov             x1, x2
    //     0x15decc0: stur            x2, [fp, #-0x10]
    // 0x15decc4: CheckStackOverflow
    //     0x15decc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15decc8: cmp             SP, x16
    //     0x15deccc: b.ls            #0x15dee2c
    // 0x15decd0: r1 = 2
    //     0x15decd0: movz            x1, #0x2
    // 0x15decd4: r0 = AllocateContext()
    //     0x15decd4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15decd8: mov             x1, x0
    // 0x15decdc: ldur            x0, [fp, #-8]
    // 0x15dece0: stur            x1, [fp, #-0x18]
    // 0x15dece4: StoreField: r1->field_f = r0
    //     0x15dece4: stur            w0, [x1, #0xf]
    // 0x15dece8: ldur            x0, [fp, #-0x10]
    // 0x15decec: StoreField: r1->field_13 = r0
    //     0x15decec: stur            w0, [x1, #0x13]
    // 0x15decf0: r0 = Obx()
    //     0x15decf0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15decf4: ldur            x2, [fp, #-0x18]
    // 0x15decf8: r1 = Function '<anonymous closure>':.
    //     0x15decf8: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c58] AnonymousClosure: (0x15d7564), in [package:customer_app/app/presentation/views/glass/search/search_page.dart] SearchPage::appBar (0x15e5d14)
    //     0x15decfc: ldr             x1, [x1, #0xc58]
    // 0x15ded00: stur            x0, [fp, #-8]
    // 0x15ded04: r0 = AllocateClosure()
    //     0x15ded04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ded08: mov             x1, x0
    // 0x15ded0c: ldur            x0, [fp, #-8]
    // 0x15ded10: StoreField: r0->field_b = r1
    //     0x15ded10: stur            w1, [x0, #0xb]
    // 0x15ded14: ldur            x1, [fp, #-0x10]
    // 0x15ded18: r0 = of()
    //     0x15ded18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ded1c: LoadField: r1 = r0->field_5b
    //     0x15ded1c: ldur            w1, [x0, #0x5b]
    // 0x15ded20: DecompressPointer r1
    //     0x15ded20: add             x1, x1, HEAP, lsl #32
    // 0x15ded24: stur            x1, [fp, #-0x10]
    // 0x15ded28: r0 = ColorFilter()
    //     0x15ded28: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ded2c: mov             x1, x0
    // 0x15ded30: ldur            x0, [fp, #-0x10]
    // 0x15ded34: stur            x1, [fp, #-0x20]
    // 0x15ded38: StoreField: r1->field_7 = r0
    //     0x15ded38: stur            w0, [x1, #7]
    // 0x15ded3c: r0 = Instance_BlendMode
    //     0x15ded3c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ded40: ldr             x0, [x0, #0xb30]
    // 0x15ded44: StoreField: r1->field_b = r0
    //     0x15ded44: stur            w0, [x1, #0xb]
    // 0x15ded48: r0 = 1
    //     0x15ded48: movz            x0, #0x1
    // 0x15ded4c: StoreField: r1->field_13 = r0
    //     0x15ded4c: stur            x0, [x1, #0x13]
    // 0x15ded50: r0 = SvgPicture()
    //     0x15ded50: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ded54: stur            x0, [fp, #-0x10]
    // 0x15ded58: ldur            x16, [fp, #-0x20]
    // 0x15ded5c: str             x16, [SP]
    // 0x15ded60: mov             x1, x0
    // 0x15ded64: r2 = "assets/images/appbar_arrow.svg"
    //     0x15ded64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15ded68: ldr             x2, [x2, #0xa40]
    // 0x15ded6c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ded6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ded70: ldr             x4, [x4, #0xa38]
    // 0x15ded74: r0 = SvgPicture.asset()
    //     0x15ded74: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ded78: r0 = Align()
    //     0x15ded78: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ded7c: mov             x1, x0
    // 0x15ded80: r0 = Instance_Alignment
    //     0x15ded80: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ded84: ldr             x0, [x0, #0xb10]
    // 0x15ded88: stur            x1, [fp, #-0x20]
    // 0x15ded8c: StoreField: r1->field_f = r0
    //     0x15ded8c: stur            w0, [x1, #0xf]
    // 0x15ded90: r0 = 1.000000
    //     0x15ded90: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ded94: StoreField: r1->field_13 = r0
    //     0x15ded94: stur            w0, [x1, #0x13]
    // 0x15ded98: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ded98: stur            w0, [x1, #0x17]
    // 0x15ded9c: ldur            x0, [fp, #-0x10]
    // 0x15deda0: StoreField: r1->field_b = r0
    //     0x15deda0: stur            w0, [x1, #0xb]
    // 0x15deda4: r0 = InkWell()
    //     0x15deda4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15deda8: mov             x3, x0
    // 0x15dedac: ldur            x0, [fp, #-0x20]
    // 0x15dedb0: stur            x3, [fp, #-0x10]
    // 0x15dedb4: StoreField: r3->field_b = r0
    //     0x15dedb4: stur            w0, [x3, #0xb]
    // 0x15dedb8: ldur            x2, [fp, #-0x18]
    // 0x15dedbc: r1 = Function '<anonymous closure>':.
    //     0x15dedbc: add             x1, PP, #0x41, lsl #12  ; [pp+0x41c60] AnonymousClosure: (0x15dee34), in [package:customer_app/app/presentation/views/cosmetic/search/search_page.dart] SearchPage::appBar (0x15deca8)
    //     0x15dedc0: ldr             x1, [x1, #0xc60]
    // 0x15dedc4: r0 = AllocateClosure()
    //     0x15dedc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dedc8: ldur            x2, [fp, #-0x10]
    // 0x15dedcc: StoreField: r2->field_f = r0
    //     0x15dedcc: stur            w0, [x2, #0xf]
    // 0x15dedd0: r0 = true
    //     0x15dedd0: add             x0, NULL, #0x20  ; true
    // 0x15dedd4: StoreField: r2->field_43 = r0
    //     0x15dedd4: stur            w0, [x2, #0x43]
    // 0x15dedd8: r1 = Instance_BoxShape
    //     0x15dedd8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15deddc: ldr             x1, [x1, #0x80]
    // 0x15dede0: StoreField: r2->field_47 = r1
    //     0x15dede0: stur            w1, [x2, #0x47]
    // 0x15dede4: StoreField: r2->field_6f = r0
    //     0x15dede4: stur            w0, [x2, #0x6f]
    // 0x15dede8: r1 = false
    //     0x15dede8: add             x1, NULL, #0x30  ; false
    // 0x15dedec: StoreField: r2->field_73 = r1
    //     0x15dedec: stur            w1, [x2, #0x73]
    // 0x15dedf0: StoreField: r2->field_83 = r0
    //     0x15dedf0: stur            w0, [x2, #0x83]
    // 0x15dedf4: StoreField: r2->field_7b = r1
    //     0x15dedf4: stur            w1, [x2, #0x7b]
    // 0x15dedf8: r0 = AppBar()
    //     0x15dedf8: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dedfc: stur            x0, [fp, #-0x18]
    // 0x15dee00: ldur            x16, [fp, #-8]
    // 0x15dee04: str             x16, [SP]
    // 0x15dee08: mov             x1, x0
    // 0x15dee0c: ldur            x2, [fp, #-0x10]
    // 0x15dee10: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15dee10: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15dee14: ldr             x4, [x4, #0xf00]
    // 0x15dee18: r0 = AppBar()
    //     0x15dee18: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dee1c: ldur            x0, [fp, #-0x18]
    // 0x15dee20: LeaveFrame
    //     0x15dee20: mov             SP, fp
    //     0x15dee24: ldp             fp, lr, [SP], #0x10
    // 0x15dee28: ret
    //     0x15dee28: ret             
    // 0x15dee2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dee2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dee30: b               #0x15decd0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15dee34, size: 0x48
    // 0x15dee34: EnterFrame
    //     0x15dee34: stp             fp, lr, [SP, #-0x10]!
    //     0x15dee38: mov             fp, SP
    // 0x15dee3c: ldr             x0, [fp, #0x10]
    // 0x15dee40: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15dee40: ldur            w1, [x0, #0x17]
    // 0x15dee44: DecompressPointer r1
    //     0x15dee44: add             x1, x1, HEAP, lsl #32
    // 0x15dee48: CheckStackOverflow
    //     0x15dee48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dee4c: cmp             SP, x16
    //     0x15dee50: b.ls            #0x15dee74
    // 0x15dee54: LoadField: r0 = r1->field_f
    //     0x15dee54: ldur            w0, [x1, #0xf]
    // 0x15dee58: DecompressPointer r0
    //     0x15dee58: add             x0, x0, HEAP, lsl #32
    // 0x15dee5c: mov             x1, x0
    // 0x15dee60: r0 = onBackPress()
    //     0x15dee60: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x15dee64: r0 = Null
    //     0x15dee64: mov             x0, NULL
    // 0x15dee68: LeaveFrame
    //     0x15dee68: mov             SP, fp
    //     0x15dee6c: ldp             fp, lr, [SP], #0x10
    // 0x15dee70: ret
    //     0x15dee70: ret             
    // 0x15dee74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dee74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dee78: b               #0x15dee54
  }
}
