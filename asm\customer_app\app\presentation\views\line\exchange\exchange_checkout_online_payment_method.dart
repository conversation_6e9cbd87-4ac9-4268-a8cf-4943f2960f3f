// lib: , url: package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart

// class id: 1049513, size: 0x8
class :: {
}

// class id: 4537, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeCheckoutOnlinePaymentMethod extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1367bf0, size: 0x64
    // 0x1367bf0: EnterFrame
    //     0x1367bf0: stp             fp, lr, [SP, #-0x10]!
    //     0x1367bf4: mov             fp, SP
    // 0x1367bf8: AllocStack(0x18)
    //     0x1367bf8: sub             SP, SP, #0x18
    // 0x1367bfc: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1367bfc: stur            x1, [fp, #-8]
    //     0x1367c00: stur            x2, [fp, #-0x10]
    // 0x1367c04: r1 = 2
    //     0x1367c04: movz            x1, #0x2
    // 0x1367c08: r0 = AllocateContext()
    //     0x1367c08: bl              #0x16f6108  ; AllocateContextStub
    // 0x1367c0c: mov             x1, x0
    // 0x1367c10: ldur            x0, [fp, #-8]
    // 0x1367c14: stur            x1, [fp, #-0x18]
    // 0x1367c18: StoreField: r1->field_f = r0
    //     0x1367c18: stur            w0, [x1, #0xf]
    // 0x1367c1c: ldur            x0, [fp, #-0x10]
    // 0x1367c20: StoreField: r1->field_13 = r0
    //     0x1367c20: stur            w0, [x1, #0x13]
    // 0x1367c24: r0 = Obx()
    //     0x1367c24: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1367c28: ldur            x2, [fp, #-0x18]
    // 0x1367c2c: r1 = Function '<anonymous closure>':.
    //     0x1367c2c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39850] AnonymousClosure: (0x1367c54), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::bottomNavigationBar (0x1367bf0)
    //     0x1367c30: ldr             x1, [x1, #0x850]
    // 0x1367c34: stur            x0, [fp, #-8]
    // 0x1367c38: r0 = AllocateClosure()
    //     0x1367c38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1367c3c: mov             x1, x0
    // 0x1367c40: ldur            x0, [fp, #-8]
    // 0x1367c44: StoreField: r0->field_b = r1
    //     0x1367c44: stur            w1, [x0, #0xb]
    // 0x1367c48: LeaveFrame
    //     0x1367c48: mov             SP, fp
    //     0x1367c4c: ldp             fp, lr, [SP], #0x10
    // 0x1367c50: ret
    //     0x1367c50: ret             
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x1367c54, size: 0x5fc
    // 0x1367c54: EnterFrame
    //     0x1367c54: stp             fp, lr, [SP, #-0x10]!
    //     0x1367c58: mov             fp, SP
    // 0x1367c5c: AllocStack(0x50)
    //     0x1367c5c: sub             SP, SP, #0x50
    // 0x1367c60: SetupParameters()
    //     0x1367c60: ldr             x0, [fp, #0x10]
    //     0x1367c64: ldur            w2, [x0, #0x17]
    //     0x1367c68: add             x2, x2, HEAP, lsl #32
    //     0x1367c6c: stur            x2, [fp, #-8]
    // 0x1367c70: CheckStackOverflow
    //     0x1367c70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1367c74: cmp             SP, x16
    //     0x1367c78: b.ls            #0x1368238
    // 0x1367c7c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1367c7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1367c80: ldr             x0, [x0, #0x1c80]
    //     0x1367c84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1367c88: cmp             w0, w16
    //     0x1367c8c: b.ne            #0x1367c98
    //     0x1367c90: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1367c94: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1367c98: r0 = GetNavigation.size()
    //     0x1367c98: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1367c9c: LoadField: d0 = r0->field_f
    //     0x1367c9c: ldur            d0, [x0, #0xf]
    // 0x1367ca0: d1 = 0.120000
    //     0x1367ca0: ldr             d1, [PP, #0x54a8]  ; [pp+0x54a8] IMM: double(0.12) from 0x3fbeb851eb851eb8
    // 0x1367ca4: fmul            d2, d0, d1
    // 0x1367ca8: stur            d2, [fp, #-0x40]
    // 0x1367cac: r1 = _ConstMap len:11
    //     0x1367cac: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c28] Map<int, List<BoxShadow>>(11)
    //     0x1367cb0: ldr             x1, [x1, #0xc28]
    // 0x1367cb4: r2 = 8
    //     0x1367cb4: movz            x2, #0x8
    // 0x1367cb8: r0 = []()
    //     0x1367cb8: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x1367cbc: stur            x0, [fp, #-0x10]
    // 0x1367cc0: r0 = BoxDecoration()
    //     0x1367cc0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1367cc4: mov             x2, x0
    // 0x1367cc8: r0 = Instance_Color
    //     0x1367cc8: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1367ccc: stur            x2, [fp, #-0x18]
    // 0x1367cd0: StoreField: r2->field_7 = r0
    //     0x1367cd0: stur            w0, [x2, #7]
    // 0x1367cd4: ldur            x0, [fp, #-0x10]
    // 0x1367cd8: ArrayStore: r2[0] = r0  ; List_4
    //     0x1367cd8: stur            w0, [x2, #0x17]
    // 0x1367cdc: r0 = Instance_BoxShape
    //     0x1367cdc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1367ce0: ldr             x0, [x0, #0x80]
    // 0x1367ce4: StoreField: r2->field_23 = r0
    //     0x1367ce4: stur            w0, [x2, #0x23]
    // 0x1367ce8: ldur            x0, [fp, #-8]
    // 0x1367cec: LoadField: r1 = r0->field_f
    //     0x1367cec: ldur            w1, [x0, #0xf]
    // 0x1367cf0: DecompressPointer r1
    //     0x1367cf0: add             x1, x1, HEAP, lsl #32
    // 0x1367cf4: r0 = controller()
    //     0x1367cf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1367cf8: LoadField: r1 = r0->field_53
    //     0x1367cf8: ldur            w1, [x0, #0x53]
    // 0x1367cfc: DecompressPointer r1
    //     0x1367cfc: add             x1, x1, HEAP, lsl #32
    // 0x1367d00: r0 = value()
    //     0x1367d00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1367d04: LoadField: r1 = r0->field_f
    //     0x1367d04: ldur            w1, [x0, #0xf]
    // 0x1367d08: DecompressPointer r1
    //     0x1367d08: add             x1, x1, HEAP, lsl #32
    // 0x1367d0c: cmp             w1, NULL
    // 0x1367d10: b.ne            #0x1367d1c
    // 0x1367d14: r0 = ""
    //     0x1367d14: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1367d18: b               #0x1367d20
    // 0x1367d1c: mov             x0, x1
    // 0x1367d20: ldur            x2, [fp, #-8]
    // 0x1367d24: stur            x0, [fp, #-0x10]
    // 0x1367d28: LoadField: r1 = r2->field_13
    //     0x1367d28: ldur            w1, [x2, #0x13]
    // 0x1367d2c: DecompressPointer r1
    //     0x1367d2c: add             x1, x1, HEAP, lsl #32
    // 0x1367d30: r0 = of()
    //     0x1367d30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1367d34: LoadField: r1 = r0->field_87
    //     0x1367d34: ldur            w1, [x0, #0x87]
    // 0x1367d38: DecompressPointer r1
    //     0x1367d38: add             x1, x1, HEAP, lsl #32
    // 0x1367d3c: LoadField: r0 = r1->field_2b
    //     0x1367d3c: ldur            w0, [x1, #0x2b]
    // 0x1367d40: DecompressPointer r0
    //     0x1367d40: add             x0, x0, HEAP, lsl #32
    // 0x1367d44: stur            x0, [fp, #-0x20]
    // 0x1367d48: r1 = Instance_Color
    //     0x1367d48: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1367d4c: d0 = 0.700000
    //     0x1367d4c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1367d50: ldr             d0, [x17, #0xf48]
    // 0x1367d54: r0 = withOpacity()
    //     0x1367d54: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1367d58: r16 = 12.000000
    //     0x1367d58: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1367d5c: ldr             x16, [x16, #0x9e8]
    // 0x1367d60: stp             x0, x16, [SP]
    // 0x1367d64: ldur            x1, [fp, #-0x20]
    // 0x1367d68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1367d68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1367d6c: ldr             x4, [x4, #0xaa0]
    // 0x1367d70: r0 = copyWith()
    //     0x1367d70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1367d74: stur            x0, [fp, #-0x20]
    // 0x1367d78: r0 = Text()
    //     0x1367d78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1367d7c: mov             x2, x0
    // 0x1367d80: ldur            x0, [fp, #-0x10]
    // 0x1367d84: stur            x2, [fp, #-0x28]
    // 0x1367d88: StoreField: r2->field_b = r0
    //     0x1367d88: stur            w0, [x2, #0xb]
    // 0x1367d8c: ldur            x0, [fp, #-0x20]
    // 0x1367d90: StoreField: r2->field_13 = r0
    //     0x1367d90: stur            w0, [x2, #0x13]
    // 0x1367d94: ldur            x0, [fp, #-8]
    // 0x1367d98: LoadField: r1 = r0->field_f
    //     0x1367d98: ldur            w1, [x0, #0xf]
    // 0x1367d9c: DecompressPointer r1
    //     0x1367d9c: add             x1, x1, HEAP, lsl #32
    // 0x1367da0: r0 = controller()
    //     0x1367da0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1367da4: mov             x1, x0
    // 0x1367da8: r0 = headerConfigData()
    //     0x1367da8: bl              #0x8a3724  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::headerConfigData
    // 0x1367dac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1367dac: ldur            w1, [x0, #0x17]
    // 0x1367db0: DecompressPointer r1
    //     0x1367db0: add             x1, x1, HEAP, lsl #32
    // 0x1367db4: cmp             w1, NULL
    // 0x1367db8: b.ne            #0x1367dc4
    // 0x1367dbc: r3 = ""
    //     0x1367dbc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1367dc0: b               #0x1367dc8
    // 0x1367dc4: mov             x3, x1
    // 0x1367dc8: ldur            x2, [fp, #-8]
    // 0x1367dcc: ldur            d0, [fp, #-0x40]
    // 0x1367dd0: ldur            x0, [fp, #-0x28]
    // 0x1367dd4: stur            x3, [fp, #-0x10]
    // 0x1367dd8: LoadField: r1 = r2->field_13
    //     0x1367dd8: ldur            w1, [x2, #0x13]
    // 0x1367ddc: DecompressPointer r1
    //     0x1367ddc: add             x1, x1, HEAP, lsl #32
    // 0x1367de0: r0 = of()
    //     0x1367de0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1367de4: LoadField: r1 = r0->field_87
    //     0x1367de4: ldur            w1, [x0, #0x87]
    // 0x1367de8: DecompressPointer r1
    //     0x1367de8: add             x1, x1, HEAP, lsl #32
    // 0x1367dec: LoadField: r0 = r1->field_7
    //     0x1367dec: ldur            w0, [x1, #7]
    // 0x1367df0: DecompressPointer r0
    //     0x1367df0: add             x0, x0, HEAP, lsl #32
    // 0x1367df4: stur            x0, [fp, #-0x20]
    // 0x1367df8: r1 = Instance_Color
    //     0x1367df8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1367dfc: d0 = 0.700000
    //     0x1367dfc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1367e00: ldr             d0, [x17, #0xf48]
    // 0x1367e04: r0 = withOpacity()
    //     0x1367e04: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1367e08: r16 = 16.000000
    //     0x1367e08: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1367e0c: ldr             x16, [x16, #0x188]
    // 0x1367e10: stp             x0, x16, [SP]
    // 0x1367e14: ldur            x1, [fp, #-0x20]
    // 0x1367e18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1367e18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1367e1c: ldr             x4, [x4, #0xaa0]
    // 0x1367e20: r0 = copyWith()
    //     0x1367e20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1367e24: stur            x0, [fp, #-0x20]
    // 0x1367e28: r0 = Text()
    //     0x1367e28: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1367e2c: mov             x1, x0
    // 0x1367e30: ldur            x0, [fp, #-0x10]
    // 0x1367e34: stur            x1, [fp, #-0x30]
    // 0x1367e38: StoreField: r1->field_b = r0
    //     0x1367e38: stur            w0, [x1, #0xb]
    // 0x1367e3c: ldur            x0, [fp, #-0x20]
    // 0x1367e40: StoreField: r1->field_13 = r0
    //     0x1367e40: stur            w0, [x1, #0x13]
    // 0x1367e44: r0 = Padding()
    //     0x1367e44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1367e48: mov             x3, x0
    // 0x1367e4c: r0 = Instance_EdgeInsets
    //     0x1367e4c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0x1367e50: ldr             x0, [x0, #0x668]
    // 0x1367e54: stur            x3, [fp, #-0x10]
    // 0x1367e58: StoreField: r3->field_f = r0
    //     0x1367e58: stur            w0, [x3, #0xf]
    // 0x1367e5c: ldur            x0, [fp, #-0x30]
    // 0x1367e60: StoreField: r3->field_b = r0
    //     0x1367e60: stur            w0, [x3, #0xb]
    // 0x1367e64: r1 = Null
    //     0x1367e64: mov             x1, NULL
    // 0x1367e68: r2 = 4
    //     0x1367e68: movz            x2, #0x4
    // 0x1367e6c: r0 = AllocateArray()
    //     0x1367e6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1367e70: mov             x2, x0
    // 0x1367e74: ldur            x0, [fp, #-0x28]
    // 0x1367e78: stur            x2, [fp, #-0x20]
    // 0x1367e7c: StoreField: r2->field_f = r0
    //     0x1367e7c: stur            w0, [x2, #0xf]
    // 0x1367e80: ldur            x0, [fp, #-0x10]
    // 0x1367e84: StoreField: r2->field_13 = r0
    //     0x1367e84: stur            w0, [x2, #0x13]
    // 0x1367e88: r1 = <Widget>
    //     0x1367e88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1367e8c: r0 = AllocateGrowableArray()
    //     0x1367e8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1367e90: mov             x1, x0
    // 0x1367e94: ldur            x0, [fp, #-0x20]
    // 0x1367e98: stur            x1, [fp, #-0x10]
    // 0x1367e9c: StoreField: r1->field_f = r0
    //     0x1367e9c: stur            w0, [x1, #0xf]
    // 0x1367ea0: r0 = 4
    //     0x1367ea0: movz            x0, #0x4
    // 0x1367ea4: StoreField: r1->field_b = r0
    //     0x1367ea4: stur            w0, [x1, #0xb]
    // 0x1367ea8: r0 = Column()
    //     0x1367ea8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1367eac: mov             x1, x0
    // 0x1367eb0: r0 = Instance_Axis
    //     0x1367eb0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1367eb4: stur            x1, [fp, #-0x20]
    // 0x1367eb8: StoreField: r1->field_f = r0
    //     0x1367eb8: stur            w0, [x1, #0xf]
    // 0x1367ebc: r0 = Instance_MainAxisAlignment
    //     0x1367ebc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1367ec0: ldr             x0, [x0, #0xa08]
    // 0x1367ec4: StoreField: r1->field_13 = r0
    //     0x1367ec4: stur            w0, [x1, #0x13]
    // 0x1367ec8: r0 = Instance_MainAxisSize
    //     0x1367ec8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1367ecc: ldr             x0, [x0, #0xa10]
    // 0x1367ed0: ArrayStore: r1[0] = r0  ; List_4
    //     0x1367ed0: stur            w0, [x1, #0x17]
    // 0x1367ed4: r2 = Instance_CrossAxisAlignment
    //     0x1367ed4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1367ed8: ldr             x2, [x2, #0x890]
    // 0x1367edc: StoreField: r1->field_1b = r2
    //     0x1367edc: stur            w2, [x1, #0x1b]
    // 0x1367ee0: r2 = Instance_VerticalDirection
    //     0x1367ee0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1367ee4: ldr             x2, [x2, #0xa20]
    // 0x1367ee8: StoreField: r1->field_23 = r2
    //     0x1367ee8: stur            w2, [x1, #0x23]
    // 0x1367eec: r3 = Instance_Clip
    //     0x1367eec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1367ef0: ldr             x3, [x3, #0x38]
    // 0x1367ef4: StoreField: r1->field_2b = r3
    //     0x1367ef4: stur            w3, [x1, #0x2b]
    // 0x1367ef8: StoreField: r1->field_2f = rZR
    //     0x1367ef8: stur            xzr, [x1, #0x2f]
    // 0x1367efc: ldur            x4, [fp, #-0x10]
    // 0x1367f00: StoreField: r1->field_b = r4
    //     0x1367f00: stur            w4, [x1, #0xb]
    // 0x1367f04: r0 = Padding()
    //     0x1367f04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1367f08: mov             x1, x0
    // 0x1367f0c: r0 = Instance_EdgeInsets
    //     0x1367f0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x1367f10: ldr             x0, [x0, #0xa00]
    // 0x1367f14: stur            x1, [fp, #-0x10]
    // 0x1367f18: StoreField: r1->field_f = r0
    //     0x1367f18: stur            w0, [x1, #0xf]
    // 0x1367f1c: ldur            x0, [fp, #-0x20]
    // 0x1367f20: StoreField: r1->field_b = r0
    //     0x1367f20: stur            w0, [x1, #0xb]
    // 0x1367f24: r16 = <EdgeInsets>
    //     0x1367f24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1367f28: ldr             x16, [x16, #0xda0]
    // 0x1367f2c: r30 = Instance_EdgeInsets
    //     0x1367f2c: add             lr, PP, #0x38, lsl #12  ; [pp+0x38178] Obj!EdgeInsets@d59a51
    //     0x1367f30: ldr             lr, [lr, #0x178]
    // 0x1367f34: stp             lr, x16, [SP]
    // 0x1367f38: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1367f38: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1367f3c: r0 = all()
    //     0x1367f3c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1367f40: ldur            x2, [fp, #-8]
    // 0x1367f44: stur            x0, [fp, #-0x20]
    // 0x1367f48: LoadField: r1 = r2->field_13
    //     0x1367f48: ldur            w1, [x2, #0x13]
    // 0x1367f4c: DecompressPointer r1
    //     0x1367f4c: add             x1, x1, HEAP, lsl #32
    // 0x1367f50: r0 = of()
    //     0x1367f50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1367f54: LoadField: r1 = r0->field_5b
    //     0x1367f54: ldur            w1, [x0, #0x5b]
    // 0x1367f58: DecompressPointer r1
    //     0x1367f58: add             x1, x1, HEAP, lsl #32
    // 0x1367f5c: r16 = <Color>
    //     0x1367f5c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1367f60: ldr             x16, [x16, #0xf80]
    // 0x1367f64: stp             x1, x16, [SP]
    // 0x1367f68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1367f68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1367f6c: r0 = all()
    //     0x1367f6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1367f70: ldur            x2, [fp, #-8]
    // 0x1367f74: stur            x0, [fp, #-0x28]
    // 0x1367f78: LoadField: r1 = r2->field_13
    //     0x1367f78: ldur            w1, [x2, #0x13]
    // 0x1367f7c: DecompressPointer r1
    //     0x1367f7c: add             x1, x1, HEAP, lsl #32
    // 0x1367f80: r0 = of()
    //     0x1367f80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1367f84: LoadField: r1 = r0->field_5b
    //     0x1367f84: ldur            w1, [x0, #0x5b]
    // 0x1367f88: DecompressPointer r1
    //     0x1367f88: add             x1, x1, HEAP, lsl #32
    // 0x1367f8c: stur            x1, [fp, #-0x30]
    // 0x1367f90: r0 = BorderSide()
    //     0x1367f90: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x1367f94: mov             x1, x0
    // 0x1367f98: ldur            x0, [fp, #-0x30]
    // 0x1367f9c: stur            x1, [fp, #-0x38]
    // 0x1367fa0: StoreField: r1->field_7 = r0
    //     0x1367fa0: stur            w0, [x1, #7]
    // 0x1367fa4: d0 = 1.000000
    //     0x1367fa4: fmov            d0, #1.00000000
    // 0x1367fa8: StoreField: r1->field_b = d0
    //     0x1367fa8: stur            d0, [x1, #0xb]
    // 0x1367fac: r0 = Instance_BorderStyle
    //     0x1367fac: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x1367fb0: ldr             x0, [x0, #0xf68]
    // 0x1367fb4: StoreField: r1->field_13 = r0
    //     0x1367fb4: stur            w0, [x1, #0x13]
    // 0x1367fb8: d0 = -1.000000
    //     0x1367fb8: fmov            d0, #-1.00000000
    // 0x1367fbc: ArrayStore: r1[0] = d0  ; List_8
    //     0x1367fbc: stur            d0, [x1, #0x17]
    // 0x1367fc0: r0 = RoundedRectangleBorder()
    //     0x1367fc0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1367fc4: mov             x1, x0
    // 0x1367fc8: r0 = Instance_BorderRadius
    //     0x1367fc8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x1367fcc: ldr             x0, [x0, #0xf70]
    // 0x1367fd0: StoreField: r1->field_b = r0
    //     0x1367fd0: stur            w0, [x1, #0xb]
    // 0x1367fd4: ldur            x0, [fp, #-0x38]
    // 0x1367fd8: StoreField: r1->field_7 = r0
    //     0x1367fd8: stur            w0, [x1, #7]
    // 0x1367fdc: r16 = <RoundedRectangleBorder>
    //     0x1367fdc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1367fe0: ldr             x16, [x16, #0xf78]
    // 0x1367fe4: stp             x1, x16, [SP]
    // 0x1367fe8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1367fe8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1367fec: r0 = all()
    //     0x1367fec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1367ff0: stur            x0, [fp, #-0x30]
    // 0x1367ff4: r0 = ButtonStyle()
    //     0x1367ff4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1367ff8: mov             x1, x0
    // 0x1367ffc: ldur            x0, [fp, #-0x28]
    // 0x1368000: stur            x1, [fp, #-0x38]
    // 0x1368004: StoreField: r1->field_b = r0
    //     0x1368004: stur            w0, [x1, #0xb]
    // 0x1368008: ldur            x0, [fp, #-0x20]
    // 0x136800c: StoreField: r1->field_23 = r0
    //     0x136800c: stur            w0, [x1, #0x23]
    // 0x1368010: ldur            x0, [fp, #-0x30]
    // 0x1368014: StoreField: r1->field_43 = r0
    //     0x1368014: stur            w0, [x1, #0x43]
    // 0x1368018: r0 = TextButtonThemeData()
    //     0x1368018: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x136801c: mov             x2, x0
    // 0x1368020: ldur            x0, [fp, #-0x38]
    // 0x1368024: stur            x2, [fp, #-0x20]
    // 0x1368028: StoreField: r2->field_7 = r0
    //     0x1368028: stur            w0, [x2, #7]
    // 0x136802c: ldur            x0, [fp, #-8]
    // 0x1368030: LoadField: r1 = r0->field_13
    //     0x1368030: ldur            w1, [x0, #0x13]
    // 0x1368034: DecompressPointer r1
    //     0x1368034: add             x1, x1, HEAP, lsl #32
    // 0x1368038: r0 = of()
    //     0x1368038: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x136803c: LoadField: r1 = r0->field_87
    //     0x136803c: ldur            w1, [x0, #0x87]
    // 0x1368040: DecompressPointer r1
    //     0x1368040: add             x1, x1, HEAP, lsl #32
    // 0x1368044: LoadField: r0 = r1->field_7
    //     0x1368044: ldur            w0, [x1, #7]
    // 0x1368048: DecompressPointer r0
    //     0x1368048: add             x0, x0, HEAP, lsl #32
    // 0x136804c: r16 = Instance_Color
    //     0x136804c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1368050: r30 = 14.000000
    //     0x1368050: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1368054: ldr             lr, [lr, #0x1d8]
    // 0x1368058: stp             lr, x16, [SP]
    // 0x136805c: mov             x1, x0
    // 0x1368060: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1368060: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1368064: ldr             x4, [x4, #0x9b8]
    // 0x1368068: r0 = copyWith()
    //     0x1368068: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x136806c: stur            x0, [fp, #-0x28]
    // 0x1368070: r0 = Text()
    //     0x1368070: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1368074: mov             x3, x0
    // 0x1368078: r0 = "CHECKOUT"
    //     0x1368078: add             x0, PP, #0x39, lsl #12  ; [pp+0x39858] "CHECKOUT"
    //     0x136807c: ldr             x0, [x0, #0x858]
    // 0x1368080: stur            x3, [fp, #-0x30]
    // 0x1368084: StoreField: r3->field_b = r0
    //     0x1368084: stur            w0, [x3, #0xb]
    // 0x1368088: ldur            x0, [fp, #-0x28]
    // 0x136808c: StoreField: r3->field_13 = r0
    //     0x136808c: stur            w0, [x3, #0x13]
    // 0x1368090: ldur            x2, [fp, #-8]
    // 0x1368094: r1 = Function '<anonymous closure>':.
    //     0x1368094: add             x1, PP, #0x39, lsl #12  ; [pp+0x39860] AnonymousClosure: (0x1368250), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::bottomNavigationBar (0x1367bf0)
    //     0x1368098: ldr             x1, [x1, #0x860]
    // 0x136809c: r0 = AllocateClosure()
    //     0x136809c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13680a0: stur            x0, [fp, #-8]
    // 0x13680a4: r0 = TextButton()
    //     0x13680a4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13680a8: mov             x1, x0
    // 0x13680ac: ldur            x0, [fp, #-8]
    // 0x13680b0: stur            x1, [fp, #-0x28]
    // 0x13680b4: StoreField: r1->field_b = r0
    //     0x13680b4: stur            w0, [x1, #0xb]
    // 0x13680b8: r0 = false
    //     0x13680b8: add             x0, NULL, #0x30  ; false
    // 0x13680bc: StoreField: r1->field_27 = r0
    //     0x13680bc: stur            w0, [x1, #0x27]
    // 0x13680c0: r0 = true
    //     0x13680c0: add             x0, NULL, #0x20  ; true
    // 0x13680c4: StoreField: r1->field_2f = r0
    //     0x13680c4: stur            w0, [x1, #0x2f]
    // 0x13680c8: ldur            x0, [fp, #-0x30]
    // 0x13680cc: StoreField: r1->field_37 = r0
    //     0x13680cc: stur            w0, [x1, #0x37]
    // 0x13680d0: r0 = Instance_ValueKey
    //     0x13680d0: add             x0, PP, #0x39, lsl #12  ; [pp+0x39868] Obj!ValueKey<String>@d5b371
    //     0x13680d4: ldr             x0, [x0, #0x868]
    // 0x13680d8: StoreField: r1->field_7 = r0
    //     0x13680d8: stur            w0, [x1, #7]
    // 0x13680dc: r0 = TextButtonTheme()
    //     0x13680dc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13680e0: mov             x3, x0
    // 0x13680e4: ldur            x0, [fp, #-0x20]
    // 0x13680e8: stur            x3, [fp, #-8]
    // 0x13680ec: StoreField: r3->field_f = r0
    //     0x13680ec: stur            w0, [x3, #0xf]
    // 0x13680f0: ldur            x0, [fp, #-0x28]
    // 0x13680f4: StoreField: r3->field_b = r0
    //     0x13680f4: stur            w0, [x3, #0xb]
    // 0x13680f8: r1 = Null
    //     0x13680f8: mov             x1, NULL
    // 0x13680fc: r2 = 6
    //     0x13680fc: movz            x2, #0x6
    // 0x1368100: r0 = AllocateArray()
    //     0x1368100: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1368104: mov             x2, x0
    // 0x1368108: ldur            x0, [fp, #-0x10]
    // 0x136810c: stur            x2, [fp, #-0x20]
    // 0x1368110: StoreField: r2->field_f = r0
    //     0x1368110: stur            w0, [x2, #0xf]
    // 0x1368114: r16 = Instance_Spacer
    //     0x1368114: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x1368118: ldr             x16, [x16, #0xf0]
    // 0x136811c: StoreField: r2->field_13 = r16
    //     0x136811c: stur            w16, [x2, #0x13]
    // 0x1368120: ldur            x0, [fp, #-8]
    // 0x1368124: ArrayStore: r2[0] = r0  ; List_4
    //     0x1368124: stur            w0, [x2, #0x17]
    // 0x1368128: r1 = <Widget>
    //     0x1368128: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x136812c: r0 = AllocateGrowableArray()
    //     0x136812c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1368130: mov             x1, x0
    // 0x1368134: ldur            x0, [fp, #-0x20]
    // 0x1368138: stur            x1, [fp, #-8]
    // 0x136813c: StoreField: r1->field_f = r0
    //     0x136813c: stur            w0, [x1, #0xf]
    // 0x1368140: r0 = 6
    //     0x1368140: movz            x0, #0x6
    // 0x1368144: StoreField: r1->field_b = r0
    //     0x1368144: stur            w0, [x1, #0xb]
    // 0x1368148: r0 = Row()
    //     0x1368148: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x136814c: mov             x1, x0
    // 0x1368150: r0 = Instance_Axis
    //     0x1368150: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1368154: stur            x1, [fp, #-0x10]
    // 0x1368158: StoreField: r1->field_f = r0
    //     0x1368158: stur            w0, [x1, #0xf]
    // 0x136815c: r0 = Instance_MainAxisAlignment
    //     0x136815c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x1368160: ldr             x0, [x0, #0xab0]
    // 0x1368164: StoreField: r1->field_13 = r0
    //     0x1368164: stur            w0, [x1, #0x13]
    // 0x1368168: r0 = Instance_MainAxisSize
    //     0x1368168: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x136816c: ldr             x0, [x0, #0xa10]
    // 0x1368170: ArrayStore: r1[0] = r0  ; List_4
    //     0x1368170: stur            w0, [x1, #0x17]
    // 0x1368174: r0 = Instance_CrossAxisAlignment
    //     0x1368174: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1368178: ldr             x0, [x0, #0xa18]
    // 0x136817c: StoreField: r1->field_1b = r0
    //     0x136817c: stur            w0, [x1, #0x1b]
    // 0x1368180: r0 = Instance_VerticalDirection
    //     0x1368180: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1368184: ldr             x0, [x0, #0xa20]
    // 0x1368188: StoreField: r1->field_23 = r0
    //     0x1368188: stur            w0, [x1, #0x23]
    // 0x136818c: r0 = Instance_Clip
    //     0x136818c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1368190: ldr             x0, [x0, #0x38]
    // 0x1368194: StoreField: r1->field_2b = r0
    //     0x1368194: stur            w0, [x1, #0x2b]
    // 0x1368198: StoreField: r1->field_2f = rZR
    //     0x1368198: stur            xzr, [x1, #0x2f]
    // 0x136819c: ldur            x0, [fp, #-8]
    // 0x13681a0: StoreField: r1->field_b = r0
    //     0x13681a0: stur            w0, [x1, #0xb]
    // 0x13681a4: r0 = Padding()
    //     0x13681a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13681a8: mov             x1, x0
    // 0x13681ac: r0 = Instance_EdgeInsets
    //     0x13681ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13681b0: ldr             x0, [x0, #0x1f0]
    // 0x13681b4: stur            x1, [fp, #-8]
    // 0x13681b8: StoreField: r1->field_f = r0
    //     0x13681b8: stur            w0, [x1, #0xf]
    // 0x13681bc: ldur            x0, [fp, #-0x10]
    // 0x13681c0: StoreField: r1->field_b = r0
    //     0x13681c0: stur            w0, [x1, #0xb]
    // 0x13681c4: r0 = Container()
    //     0x13681c4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13681c8: stur            x0, [fp, #-0x10]
    // 0x13681cc: ldur            x16, [fp, #-0x18]
    // 0x13681d0: ldur            lr, [fp, #-8]
    // 0x13681d4: stp             lr, x16, [SP]
    // 0x13681d8: mov             x1, x0
    // 0x13681dc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x13681dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x13681e0: ldr             x4, [x4, #0x88]
    // 0x13681e4: r0 = Container()
    //     0x13681e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13681e8: ldur            d0, [fp, #-0x40]
    // 0x13681ec: r0 = inline_Allocate_Double()
    //     0x13681ec: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x13681f0: add             x0, x0, #0x10
    //     0x13681f4: cmp             x1, x0
    //     0x13681f8: b.ls            #0x1368240
    //     0x13681fc: str             x0, [THR, #0x50]  ; THR::top
    //     0x1368200: sub             x0, x0, #0xf
    //     0x1368204: movz            x1, #0xe15c
    //     0x1368208: movk            x1, #0x3, lsl #16
    //     0x136820c: stur            x1, [x0, #-1]
    // 0x1368210: StoreField: r0->field_7 = d0
    //     0x1368210: stur            d0, [x0, #7]
    // 0x1368214: stur            x0, [fp, #-8]
    // 0x1368218: r0 = SizedBox()
    //     0x1368218: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x136821c: ldur            x1, [fp, #-8]
    // 0x1368220: StoreField: r0->field_13 = r1
    //     0x1368220: stur            w1, [x0, #0x13]
    // 0x1368224: ldur            x1, [fp, #-0x10]
    // 0x1368228: StoreField: r0->field_b = r1
    //     0x1368228: stur            w1, [x0, #0xb]
    // 0x136822c: LeaveFrame
    //     0x136822c: mov             SP, fp
    //     0x1368230: ldp             fp, lr, [SP], #0x10
    // 0x1368234: ret
    //     0x1368234: ret             
    // 0x1368238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1368238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x136823c: b               #0x1367c7c
    // 0x1368240: SaveReg d0
    //     0x1368240: str             q0, [SP, #-0x10]!
    // 0x1368244: r0 = AllocateDouble()
    //     0x1368244: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1368248: RestoreReg d0
    //     0x1368248: ldr             q0, [SP], #0x10
    // 0x136824c: b               #0x1368210
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1368250, size: 0xf8
    // 0x1368250: EnterFrame
    //     0x1368250: stp             fp, lr, [SP, #-0x10]!
    //     0x1368254: mov             fp, SP
    // 0x1368258: AllocStack(0x20)
    //     0x1368258: sub             SP, SP, #0x20
    // 0x136825c: SetupParameters()
    //     0x136825c: ldr             x0, [fp, #0x10]
    //     0x1368260: ldur            w2, [x0, #0x17]
    //     0x1368264: add             x2, x2, HEAP, lsl #32
    //     0x1368268: stur            x2, [fp, #-8]
    // 0x136826c: CheckStackOverflow
    //     0x136826c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1368270: cmp             SP, x16
    //     0x1368274: b.ls            #0x1368340
    // 0x1368278: LoadField: r1 = r2->field_f
    //     0x1368278: ldur            w1, [x2, #0xf]
    // 0x136827c: DecompressPointer r1
    //     0x136827c: add             x1, x1, HEAP, lsl #32
    // 0x1368280: r0 = controller()
    //     0x1368280: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1368284: mov             x1, x0
    // 0x1368288: r2 = "return_exchange_checkout_clicked_online_page"
    //     0x1368288: add             x2, PP, #0x39, lsl #12  ; [pp+0x39870] "return_exchange_checkout_clicked_online_page"
    //     0x136828c: ldr             x2, [x2, #0x870]
    // 0x1368290: r0 = ctaExchangeCheckoutPostEvent()
    //     0x1368290: bl              #0x131384c  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_online_payment_controller.dart] ExchangeCheckoutOnlinePaymentController::ctaExchangeCheckoutPostEvent
    // 0x1368294: ldur            x0, [fp, #-8]
    // 0x1368298: LoadField: r1 = r0->field_f
    //     0x1368298: ldur            w1, [x0, #0xf]
    // 0x136829c: DecompressPointer r1
    //     0x136829c: add             x1, x1, HEAP, lsl #32
    // 0x13682a0: r0 = controller()
    //     0x13682a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13682a4: mov             x1, x0
    // 0x13682a8: r0 = bumperCouponData()
    //     0x13682a8: bl              #0x9be348  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::bumperCouponData
    // 0x13682ac: r1 = 60
    //     0x13682ac: movz            x1, #0x3c
    // 0x13682b0: branchIfSmi(r0, 0x13682bc)
    //     0x13682b0: tbz             w0, #0, #0x13682bc
    // 0x13682b4: r1 = LoadClassIdInstr(r0)
    //     0x13682b4: ldur            x1, [x0, #-1]
    //     0x13682b8: ubfx            x1, x1, #0xc, #0x14
    // 0x13682bc: r16 = ""
    //     0x13682bc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13682c0: stp             x16, x0, [SP]
    // 0x13682c4: mov             x0, x1
    // 0x13682c8: mov             lr, x0
    // 0x13682cc: ldr             lr, [x21, lr, lsl #3]
    // 0x13682d0: blr             lr
    // 0x13682d4: tbz             w0, #4, #0x1368318
    // 0x13682d8: ldur            x0, [fp, #-8]
    // 0x13682dc: LoadField: r1 = r0->field_f
    //     0x13682dc: ldur            w1, [x0, #0xf]
    // 0x13682e0: DecompressPointer r1
    //     0x13682e0: add             x1, x1, HEAP, lsl #32
    // 0x13682e4: r0 = controller()
    //     0x13682e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13682e8: mov             x2, x0
    // 0x13682ec: ldur            x0, [fp, #-8]
    // 0x13682f0: stur            x2, [fp, #-0x10]
    // 0x13682f4: LoadField: r1 = r0->field_f
    //     0x13682f4: ldur            w1, [x0, #0xf]
    // 0x13682f8: DecompressPointer r1
    //     0x13682f8: add             x1, x1, HEAP, lsl #32
    // 0x13682fc: r0 = controller()
    //     0x13682fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1368300: mov             x1, x0
    // 0x1368304: r0 = offersResponse()
    //     0x1368304: bl              #0x9eefa8  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::offersResponse
    // 0x1368308: ldur            x1, [fp, #-0x10]
    // 0x136830c: mov             x2, x0
    // 0x1368310: r0 = initPaymentDetails()
    //     0x1368310: bl              #0x1310458  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_online_payment_controller.dart] ExchangeCheckoutOnlinePaymentController::initPaymentDetails
    // 0x1368314: b               #0x1368330
    // 0x1368318: ldur            x0, [fp, #-8]
    // 0x136831c: LoadField: r1 = r0->field_f
    //     0x136831c: ldur            w1, [x0, #0xf]
    // 0x1368320: DecompressPointer r1
    //     0x1368320: add             x1, x1, HEAP, lsl #32
    // 0x1368324: r2 = "Please select any payment mode!"
    //     0x1368324: add             x2, PP, #0x38, lsl #12  ; [pp+0x38198] "Please select any payment mode!"
    //     0x1368328: ldr             x2, [x2, #0x198]
    // 0x136832c: r0 = showErrorSnackBar()
    //     0x136832c: bl              #0x9a5fc0  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showErrorSnackBar
    // 0x1368330: r0 = Null
    //     0x1368330: mov             x0, NULL
    // 0x1368334: LeaveFrame
    //     0x1368334: mov             SP, fp
    //     0x1368338: ldp             fp, lr, [SP], #0x10
    // 0x136833c: ret
    //     0x136833c: ret             
    // 0x1368340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1368340: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1368344: b               #0x1368278
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13c1b6c, size: 0x94
    // 0x13c1b6c: EnterFrame
    //     0x13c1b6c: stp             fp, lr, [SP, #-0x10]!
    //     0x13c1b70: mov             fp, SP
    // 0x13c1b74: AllocStack(0x8)
    //     0x13c1b74: sub             SP, SP, #8
    // 0x13c1b78: SetupParameters()
    //     0x13c1b78: ldr             x0, [fp, #0x10]
    //     0x13c1b7c: ldur            w2, [x0, #0x17]
    //     0x13c1b80: add             x2, x2, HEAP, lsl #32
    //     0x13c1b84: stur            x2, [fp, #-8]
    // 0x13c1b88: CheckStackOverflow
    //     0x13c1b88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13c1b8c: cmp             SP, x16
    //     0x13c1b90: b.ls            #0x13c1bf8
    // 0x13c1b94: LoadField: r1 = r2->field_f
    //     0x13c1b94: ldur            w1, [x2, #0xf]
    // 0x13c1b98: DecompressPointer r1
    //     0x13c1b98: add             x1, x1, HEAP, lsl #32
    // 0x13c1b9c: r0 = controller()
    //     0x13c1b9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c1ba0: mov             x1, x0
    // 0x13c1ba4: ldur            x0, [fp, #-8]
    // 0x13c1ba8: LoadField: r2 = r0->field_13
    //     0x13c1ba8: ldur            w2, [x0, #0x13]
    // 0x13c1bac: DecompressPointer r2
    //     0x13c1bac: add             x2, x2, HEAP, lsl #32
    // 0x13c1bb0: cmp             w2, NULL
    // 0x13c1bb4: b.ne            #0x13c1bc0
    // 0x13c1bb8: r2 = Null
    //     0x13c1bb8: mov             x2, NULL
    // 0x13c1bbc: b               #0x13c1bcc
    // 0x13c1bc0: LoadField: r3 = r2->field_f
    //     0x13c1bc0: ldur            w3, [x2, #0xf]
    // 0x13c1bc4: DecompressPointer r3
    //     0x13c1bc4: add             x3, x3, HEAP, lsl #32
    // 0x13c1bc8: mov             x2, x3
    // 0x13c1bcc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x13c1bcc: ldur            w3, [x0, #0x17]
    // 0x13c1bd0: DecompressPointer r3
    //     0x13c1bd0: add             x3, x3, HEAP, lsl #32
    // 0x13c1bd4: r0 = LoadInt32Instr(r3)
    //     0x13c1bd4: sbfx            x0, x3, #1, #0x1f
    //     0x13c1bd8: tbz             w3, #0, #0x13c1be0
    //     0x13c1bdc: ldur            x0, [x3, #7]
    // 0x13c1be0: mov             x3, x0
    // 0x13c1be4: r0 = setPaymentMethod()
    //     0x13c1be4: bl              #0x13c2054  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_online_payment_controller.dart] ExchangeCheckoutOnlinePaymentController::setPaymentMethod
    // 0x13c1be8: r0 = Null
    //     0x13c1be8: mov             x0, NULL
    // 0x13c1bec: LeaveFrame
    //     0x13c1bec: mov             SP, fp
    //     0x13c1bf0: ldp             fp, lr, [SP], #0x10
    // 0x13c1bf4: ret
    //     0x13c1bf4: ret             
    // 0x13c1bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13c1bf8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13c1bfc: b               #0x13c1b94
  }
  [closure] InkWell <anonymous closure>(dynamic) {
    // ** addr: 0x13c1c00, size: 0x3c4
    // 0x13c1c00: EnterFrame
    //     0x13c1c00: stp             fp, lr, [SP, #-0x10]!
    //     0x13c1c04: mov             fp, SP
    // 0x13c1c08: AllocStack(0x48)
    //     0x13c1c08: sub             SP, SP, #0x48
    // 0x13c1c0c: SetupParameters()
    //     0x13c1c0c: ldr             x0, [fp, #0x10]
    //     0x13c1c10: ldur            w2, [x0, #0x17]
    //     0x13c1c14: add             x2, x2, HEAP, lsl #32
    //     0x13c1c18: stur            x2, [fp, #-8]
    // 0x13c1c1c: CheckStackOverflow
    //     0x13c1c1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13c1c20: cmp             SP, x16
    //     0x13c1c24: b.ls            #0x13c1fbc
    // 0x13c1c28: LoadField: r1 = r2->field_f
    //     0x13c1c28: ldur            w1, [x2, #0xf]
    // 0x13c1c2c: DecompressPointer r1
    //     0x13c1c2c: add             x1, x1, HEAP, lsl #32
    // 0x13c1c30: r0 = controller()
    //     0x13c1c30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c1c34: LoadField: r1 = r0->field_77
    //     0x13c1c34: ldur            w1, [x0, #0x77]
    // 0x13c1c38: DecompressPointer r1
    //     0x13c1c38: add             x1, x1, HEAP, lsl #32
    // 0x13c1c3c: r0 = value()
    //     0x13c1c3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c1c40: ldur            x2, [fp, #-8]
    // 0x13c1c44: LoadField: r1 = r2->field_13
    //     0x13c1c44: ldur            w1, [x2, #0x13]
    // 0x13c1c48: DecompressPointer r1
    //     0x13c1c48: add             x1, x1, HEAP, lsl #32
    // 0x13c1c4c: cmp             w1, NULL
    // 0x13c1c50: b.ne            #0x13c1c5c
    // 0x13c1c54: r1 = Null
    //     0x13c1c54: mov             x1, NULL
    // 0x13c1c58: b               #0x13c1c68
    // 0x13c1c5c: LoadField: r3 = r1->field_f
    //     0x13c1c5c: ldur            w3, [x1, #0xf]
    // 0x13c1c60: DecompressPointer r3
    //     0x13c1c60: add             x3, x3, HEAP, lsl #32
    // 0x13c1c64: mov             x1, x3
    // 0x13c1c68: r3 = 60
    //     0x13c1c68: movz            x3, #0x3c
    // 0x13c1c6c: branchIfSmi(r0, 0x13c1c78)
    //     0x13c1c6c: tbz             w0, #0, #0x13c1c78
    // 0x13c1c70: r3 = LoadClassIdInstr(r0)
    //     0x13c1c70: ldur            x3, [x0, #-1]
    //     0x13c1c74: ubfx            x3, x3, #0xc, #0x14
    // 0x13c1c78: stp             x1, x0, [SP]
    // 0x13c1c7c: mov             x0, x3
    // 0x13c1c80: mov             lr, x0
    // 0x13c1c84: ldr             lr, [x21, lr, lsl #3]
    // 0x13c1c88: blr             lr
    // 0x13c1c8c: tbnz            w0, #4, #0x13c1cd4
    // 0x13c1c90: ldur            x2, [fp, #-8]
    // 0x13c1c94: LoadField: r1 = r2->field_f
    //     0x13c1c94: ldur            w1, [x2, #0xf]
    // 0x13c1c98: DecompressPointer r1
    //     0x13c1c98: add             x1, x1, HEAP, lsl #32
    // 0x13c1c9c: r0 = controller()
    //     0x13c1c9c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c1ca0: mov             x1, x0
    // 0x13c1ca4: r0 = paymentSelectedIndex()
    //     0x13c1ca4: bl              #0x13c1b24  ; [package:customer_app/app/presentation/controllers/exchange/exchange_checkout_online_payment_controller.dart] ExchangeCheckoutOnlinePaymentController::paymentSelectedIndex
    // 0x13c1ca8: ldur            x2, [fp, #-8]
    // 0x13c1cac: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x13c1cac: ldur            w1, [x2, #0x17]
    // 0x13c1cb0: DecompressPointer r1
    //     0x13c1cb0: add             x1, x1, HEAP, lsl #32
    // 0x13c1cb4: r3 = LoadInt32Instr(r1)
    //     0x13c1cb4: sbfx            x3, x1, #1, #0x1f
    //     0x13c1cb8: tbz             w1, #0, #0x13c1cc0
    //     0x13c1cbc: ldur            x3, [x1, #7]
    // 0x13c1cc0: cmp             x0, x3
    // 0x13c1cc4: b.ne            #0x13c1cd8
    // 0x13c1cc8: r0 = Instance_IconData
    //     0x13c1cc8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0x13c1ccc: ldr             x0, [x0, #0x30]
    // 0x13c1cd0: b               #0x13c1ce0
    // 0x13c1cd4: ldur            x2, [fp, #-8]
    // 0x13c1cd8: r0 = Instance_IconData
    //     0x13c1cd8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0x13c1cdc: ldr             x0, [x0, #0x38]
    // 0x13c1ce0: stur            x0, [fp, #-0x10]
    // 0x13c1ce4: LoadField: r1 = r2->field_1b
    //     0x13c1ce4: ldur            w1, [x2, #0x1b]
    // 0x13c1ce8: DecompressPointer r1
    //     0x13c1ce8: add             x1, x1, HEAP, lsl #32
    // 0x13c1cec: r0 = of()
    //     0x13c1cec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c1cf0: LoadField: r1 = r0->field_5b
    //     0x13c1cf0: ldur            w1, [x0, #0x5b]
    // 0x13c1cf4: DecompressPointer r1
    //     0x13c1cf4: add             x1, x1, HEAP, lsl #32
    // 0x13c1cf8: stur            x1, [fp, #-0x18]
    // 0x13c1cfc: r0 = Icon()
    //     0x13c1cfc: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x13c1d00: mov             x1, x0
    // 0x13c1d04: ldur            x0, [fp, #-0x10]
    // 0x13c1d08: stur            x1, [fp, #-0x20]
    // 0x13c1d0c: StoreField: r1->field_b = r0
    //     0x13c1d0c: stur            w0, [x1, #0xb]
    // 0x13c1d10: ldur            x0, [fp, #-0x18]
    // 0x13c1d14: StoreField: r1->field_23 = r0
    //     0x13c1d14: stur            w0, [x1, #0x23]
    // 0x13c1d18: ldur            x2, [fp, #-8]
    // 0x13c1d1c: LoadField: r0 = r2->field_13
    //     0x13c1d1c: ldur            w0, [x2, #0x13]
    // 0x13c1d20: DecompressPointer r0
    //     0x13c1d20: add             x0, x0, HEAP, lsl #32
    // 0x13c1d24: cmp             w0, NULL
    // 0x13c1d28: b.ne            #0x13c1d34
    // 0x13c1d2c: r0 = Null
    //     0x13c1d2c: mov             x0, NULL
    // 0x13c1d30: b               #0x13c1d40
    // 0x13c1d34: LoadField: r3 = r0->field_7
    //     0x13c1d34: ldur            w3, [x0, #7]
    // 0x13c1d38: DecompressPointer r3
    //     0x13c1d38: add             x3, x3, HEAP, lsl #32
    // 0x13c1d3c: mov             x0, x3
    // 0x13c1d40: cmp             w0, NULL
    // 0x13c1d44: b.ne            #0x13c1d4c
    // 0x13c1d48: r0 = ""
    //     0x13c1d48: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c1d4c: stur            x0, [fp, #-0x10]
    // 0x13c1d50: r0 = ImageHeaders.forImages()
    //     0x13c1d50: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x13c1d54: stur            x0, [fp, #-0x18]
    // 0x13c1d58: r0 = CachedNetworkImage()
    //     0x13c1d58: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x13c1d5c: stur            x0, [fp, #-0x28]
    // 0x13c1d60: r16 = 48.000000
    //     0x13c1d60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13c1d64: ldr             x16, [x16, #0xad8]
    // 0x13c1d68: r30 = 48.000000
    //     0x13c1d68: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13c1d6c: ldr             lr, [lr, #0xad8]
    // 0x13c1d70: stp             lr, x16, [SP, #8]
    // 0x13c1d74: ldur            x16, [fp, #-0x18]
    // 0x13c1d78: str             x16, [SP]
    // 0x13c1d7c: mov             x1, x0
    // 0x13c1d80: ldur            x2, [fp, #-0x10]
    // 0x13c1d84: r4 = const [0, 0x5, 0x3, 0x2, height, 0x2, httpHeaders, 0x4, width, 0x3, null]
    //     0x13c1d84: add             x4, PP, #0x34, lsl #12  ; [pp+0x34588] List(11) [0, 0x5, 0x3, 0x2, "height", 0x2, "httpHeaders", 0x4, "width", 0x3, Null]
    //     0x13c1d88: ldr             x4, [x4, #0x588]
    // 0x13c1d8c: r0 = CachedNetworkImage()
    //     0x13c1d8c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x13c1d90: r0 = Padding()
    //     0x13c1d90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c1d94: mov             x2, x0
    // 0x13c1d98: r0 = Instance_EdgeInsets
    //     0x13c1d98: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x13c1d9c: ldr             x0, [x0, #0xa78]
    // 0x13c1da0: stur            x2, [fp, #-0x18]
    // 0x13c1da4: StoreField: r2->field_f = r0
    //     0x13c1da4: stur            w0, [x2, #0xf]
    // 0x13c1da8: ldur            x1, [fp, #-0x28]
    // 0x13c1dac: StoreField: r2->field_b = r1
    //     0x13c1dac: stur            w1, [x2, #0xb]
    // 0x13c1db0: ldur            x3, [fp, #-8]
    // 0x13c1db4: LoadField: r1 = r3->field_13
    //     0x13c1db4: ldur            w1, [x3, #0x13]
    // 0x13c1db8: DecompressPointer r1
    //     0x13c1db8: add             x1, x1, HEAP, lsl #32
    // 0x13c1dbc: cmp             w1, NULL
    // 0x13c1dc0: b.ne            #0x13c1dcc
    // 0x13c1dc4: r1 = Null
    //     0x13c1dc4: mov             x1, NULL
    // 0x13c1dc8: b               #0x13c1dd8
    // 0x13c1dcc: LoadField: r4 = r1->field_b
    //     0x13c1dcc: ldur            w4, [x1, #0xb]
    // 0x13c1dd0: DecompressPointer r4
    //     0x13c1dd0: add             x4, x4, HEAP, lsl #32
    // 0x13c1dd4: mov             x1, x4
    // 0x13c1dd8: cmp             w1, NULL
    // 0x13c1ddc: b.ne            #0x13c1de8
    // 0x13c1de0: r5 = ""
    //     0x13c1de0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c1de4: b               #0x13c1dec
    // 0x13c1de8: mov             x5, x1
    // 0x13c1dec: ldur            x4, [fp, #-0x20]
    // 0x13c1df0: stur            x5, [fp, #-0x10]
    // 0x13c1df4: LoadField: r1 = r3->field_1b
    //     0x13c1df4: ldur            w1, [x3, #0x1b]
    // 0x13c1df8: DecompressPointer r1
    //     0x13c1df8: add             x1, x1, HEAP, lsl #32
    // 0x13c1dfc: r0 = of()
    //     0x13c1dfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c1e00: LoadField: r1 = r0->field_87
    //     0x13c1e00: ldur            w1, [x0, #0x87]
    // 0x13c1e04: DecompressPointer r1
    //     0x13c1e04: add             x1, x1, HEAP, lsl #32
    // 0x13c1e08: LoadField: r0 = r1->field_2b
    //     0x13c1e08: ldur            w0, [x1, #0x2b]
    // 0x13c1e0c: DecompressPointer r0
    //     0x13c1e0c: add             x0, x0, HEAP, lsl #32
    // 0x13c1e10: r16 = 14.000000
    //     0x13c1e10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13c1e14: ldr             x16, [x16, #0x1d8]
    // 0x13c1e18: r30 = Instance_Color
    //     0x13c1e18: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c1e1c: stp             lr, x16, [SP]
    // 0x13c1e20: mov             x1, x0
    // 0x13c1e24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13c1e24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13c1e28: ldr             x4, [x4, #0xaa0]
    // 0x13c1e2c: r0 = copyWith()
    //     0x13c1e2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c1e30: stur            x0, [fp, #-0x28]
    // 0x13c1e34: r0 = Text()
    //     0x13c1e34: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c1e38: mov             x1, x0
    // 0x13c1e3c: ldur            x0, [fp, #-0x10]
    // 0x13c1e40: stur            x1, [fp, #-0x30]
    // 0x13c1e44: StoreField: r1->field_b = r0
    //     0x13c1e44: stur            w0, [x1, #0xb]
    // 0x13c1e48: ldur            x0, [fp, #-0x28]
    // 0x13c1e4c: StoreField: r1->field_13 = r0
    //     0x13c1e4c: stur            w0, [x1, #0x13]
    // 0x13c1e50: r0 = Padding()
    //     0x13c1e50: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c1e54: mov             x3, x0
    // 0x13c1e58: r0 = Instance_EdgeInsets
    //     0x13c1e58: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x13c1e5c: ldr             x0, [x0, #0xa78]
    // 0x13c1e60: stur            x3, [fp, #-0x10]
    // 0x13c1e64: StoreField: r3->field_f = r0
    //     0x13c1e64: stur            w0, [x3, #0xf]
    // 0x13c1e68: ldur            x0, [fp, #-0x30]
    // 0x13c1e6c: StoreField: r3->field_b = r0
    //     0x13c1e6c: stur            w0, [x3, #0xb]
    // 0x13c1e70: r1 = Null
    //     0x13c1e70: mov             x1, NULL
    // 0x13c1e74: r2 = 6
    //     0x13c1e74: movz            x2, #0x6
    // 0x13c1e78: r0 = AllocateArray()
    //     0x13c1e78: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c1e7c: mov             x2, x0
    // 0x13c1e80: ldur            x0, [fp, #-0x20]
    // 0x13c1e84: stur            x2, [fp, #-0x28]
    // 0x13c1e88: StoreField: r2->field_f = r0
    //     0x13c1e88: stur            w0, [x2, #0xf]
    // 0x13c1e8c: ldur            x0, [fp, #-0x18]
    // 0x13c1e90: StoreField: r2->field_13 = r0
    //     0x13c1e90: stur            w0, [x2, #0x13]
    // 0x13c1e94: ldur            x0, [fp, #-0x10]
    // 0x13c1e98: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c1e98: stur            w0, [x2, #0x17]
    // 0x13c1e9c: r1 = <Widget>
    //     0x13c1e9c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13c1ea0: r0 = AllocateGrowableArray()
    //     0x13c1ea0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13c1ea4: mov             x1, x0
    // 0x13c1ea8: ldur            x0, [fp, #-0x28]
    // 0x13c1eac: stur            x1, [fp, #-0x10]
    // 0x13c1eb0: StoreField: r1->field_f = r0
    //     0x13c1eb0: stur            w0, [x1, #0xf]
    // 0x13c1eb4: r0 = 6
    //     0x13c1eb4: movz            x0, #0x6
    // 0x13c1eb8: StoreField: r1->field_b = r0
    //     0x13c1eb8: stur            w0, [x1, #0xb]
    // 0x13c1ebc: r0 = Row()
    //     0x13c1ebc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13c1ec0: mov             x1, x0
    // 0x13c1ec4: r0 = Instance_Axis
    //     0x13c1ec4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13c1ec8: stur            x1, [fp, #-0x18]
    // 0x13c1ecc: StoreField: r1->field_f = r0
    //     0x13c1ecc: stur            w0, [x1, #0xf]
    // 0x13c1ed0: r0 = Instance_MainAxisAlignment
    //     0x13c1ed0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13c1ed4: ldr             x0, [x0, #0xa08]
    // 0x13c1ed8: StoreField: r1->field_13 = r0
    //     0x13c1ed8: stur            w0, [x1, #0x13]
    // 0x13c1edc: r0 = Instance_MainAxisSize
    //     0x13c1edc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13c1ee0: ldr             x0, [x0, #0xa10]
    // 0x13c1ee4: ArrayStore: r1[0] = r0  ; List_4
    //     0x13c1ee4: stur            w0, [x1, #0x17]
    // 0x13c1ee8: r0 = Instance_CrossAxisAlignment
    //     0x13c1ee8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13c1eec: ldr             x0, [x0, #0xa18]
    // 0x13c1ef0: StoreField: r1->field_1b = r0
    //     0x13c1ef0: stur            w0, [x1, #0x1b]
    // 0x13c1ef4: r0 = Instance_VerticalDirection
    //     0x13c1ef4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13c1ef8: ldr             x0, [x0, #0xa20]
    // 0x13c1efc: StoreField: r1->field_23 = r0
    //     0x13c1efc: stur            w0, [x1, #0x23]
    // 0x13c1f00: r0 = Instance_Clip
    //     0x13c1f00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13c1f04: ldr             x0, [x0, #0x38]
    // 0x13c1f08: StoreField: r1->field_2b = r0
    //     0x13c1f08: stur            w0, [x1, #0x2b]
    // 0x13c1f0c: StoreField: r1->field_2f = rZR
    //     0x13c1f0c: stur            xzr, [x1, #0x2f]
    // 0x13c1f10: ldur            x0, [fp, #-0x10]
    // 0x13c1f14: StoreField: r1->field_b = r0
    //     0x13c1f14: stur            w0, [x1, #0xb]
    // 0x13c1f18: r0 = Center()
    //     0x13c1f18: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13c1f1c: mov             x1, x0
    // 0x13c1f20: r0 = Instance_Alignment
    //     0x13c1f20: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13c1f24: ldr             x0, [x0, #0xb10]
    // 0x13c1f28: stur            x1, [fp, #-0x10]
    // 0x13c1f2c: StoreField: r1->field_f = r0
    //     0x13c1f2c: stur            w0, [x1, #0xf]
    // 0x13c1f30: ldur            x0, [fp, #-0x18]
    // 0x13c1f34: StoreField: r1->field_b = r0
    //     0x13c1f34: stur            w0, [x1, #0xb]
    // 0x13c1f38: r0 = Padding()
    //     0x13c1f38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c1f3c: mov             x1, x0
    // 0x13c1f40: r0 = Instance_EdgeInsets
    //     0x13c1f40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0x13c1f44: ldr             x0, [x0, #0xe68]
    // 0x13c1f48: stur            x1, [fp, #-0x18]
    // 0x13c1f4c: StoreField: r1->field_f = r0
    //     0x13c1f4c: stur            w0, [x1, #0xf]
    // 0x13c1f50: ldur            x0, [fp, #-0x10]
    // 0x13c1f54: StoreField: r1->field_b = r0
    //     0x13c1f54: stur            w0, [x1, #0xb]
    // 0x13c1f58: r0 = InkWell()
    //     0x13c1f58: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x13c1f5c: mov             x3, x0
    // 0x13c1f60: ldur            x0, [fp, #-0x18]
    // 0x13c1f64: stur            x3, [fp, #-0x10]
    // 0x13c1f68: StoreField: r3->field_b = r0
    //     0x13c1f68: stur            w0, [x3, #0xb]
    // 0x13c1f6c: ldur            x2, [fp, #-8]
    // 0x13c1f70: r1 = Function '<anonymous closure>':.
    //     0x13c1f70: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3afb8] AnonymousClosure: (0x13c1b6c), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::lineThemePaymentMethodCard (0x13c1fc4)
    //     0x13c1f74: ldr             x1, [x1, #0xfb8]
    // 0x13c1f78: r0 = AllocateClosure()
    //     0x13c1f78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c1f7c: mov             x1, x0
    // 0x13c1f80: ldur            x0, [fp, #-0x10]
    // 0x13c1f84: StoreField: r0->field_f = r1
    //     0x13c1f84: stur            w1, [x0, #0xf]
    // 0x13c1f88: r1 = true
    //     0x13c1f88: add             x1, NULL, #0x20  ; true
    // 0x13c1f8c: StoreField: r0->field_43 = r1
    //     0x13c1f8c: stur            w1, [x0, #0x43]
    // 0x13c1f90: r2 = Instance_BoxShape
    //     0x13c1f90: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13c1f94: ldr             x2, [x2, #0x80]
    // 0x13c1f98: StoreField: r0->field_47 = r2
    //     0x13c1f98: stur            w2, [x0, #0x47]
    // 0x13c1f9c: StoreField: r0->field_6f = r1
    //     0x13c1f9c: stur            w1, [x0, #0x6f]
    // 0x13c1fa0: r2 = false
    //     0x13c1fa0: add             x2, NULL, #0x30  ; false
    // 0x13c1fa4: StoreField: r0->field_73 = r2
    //     0x13c1fa4: stur            w2, [x0, #0x73]
    // 0x13c1fa8: StoreField: r0->field_83 = r1
    //     0x13c1fa8: stur            w1, [x0, #0x83]
    // 0x13c1fac: StoreField: r0->field_7b = r2
    //     0x13c1fac: stur            w2, [x0, #0x7b]
    // 0x13c1fb0: LeaveFrame
    //     0x13c1fb0: mov             SP, fp
    //     0x13c1fb4: ldp             fp, lr, [SP], #0x10
    // 0x13c1fb8: ret
    //     0x13c1fb8: ret             
    // 0x13c1fbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13c1fbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13c1fc0: b               #0x13c1c28
  }
  _ lineThemePaymentMethodCard(/* No info */) {
    // ** addr: 0x13c1fc4, size: 0x90
    // 0x13c1fc4: EnterFrame
    //     0x13c1fc4: stp             fp, lr, [SP, #-0x10]!
    //     0x13c1fc8: mov             fp, SP
    // 0x13c1fcc: AllocStack(0x28)
    //     0x13c1fcc: sub             SP, SP, #0x28
    // 0x13c1fd0: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x13c1fd0: stur            x1, [fp, #-8]
    //     0x13c1fd4: stur            x2, [fp, #-0x10]
    //     0x13c1fd8: stur            x3, [fp, #-0x18]
    //     0x13c1fdc: stur            x5, [fp, #-0x20]
    // 0x13c1fe0: r1 = 4
    //     0x13c1fe0: movz            x1, #0x4
    // 0x13c1fe4: r0 = AllocateContext()
    //     0x13c1fe4: bl              #0x16f6108  ; AllocateContextStub
    // 0x13c1fe8: mov             x2, x0
    // 0x13c1fec: ldur            x0, [fp, #-8]
    // 0x13c1ff0: stur            x2, [fp, #-0x28]
    // 0x13c1ff4: StoreField: r2->field_f = r0
    //     0x13c1ff4: stur            w0, [x2, #0xf]
    // 0x13c1ff8: ldur            x0, [fp, #-0x10]
    // 0x13c1ffc: StoreField: r2->field_13 = r0
    //     0x13c1ffc: stur            w0, [x2, #0x13]
    // 0x13c2000: ldur            x3, [fp, #-0x18]
    // 0x13c2004: r0 = BoxInt64Instr(r3)
    //     0x13c2004: sbfiz           x0, x3, #1, #0x1f
    //     0x13c2008: cmp             x3, x0, asr #1
    //     0x13c200c: b.eq            #0x13c2018
    //     0x13c2010: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x13c2014: stur            x3, [x0, #7]
    // 0x13c2018: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c2018: stur            w0, [x2, #0x17]
    // 0x13c201c: ldur            x0, [fp, #-0x20]
    // 0x13c2020: StoreField: r2->field_1b = r0
    //     0x13c2020: stur            w0, [x2, #0x1b]
    // 0x13c2024: r0 = Obx()
    //     0x13c2024: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13c2028: ldur            x2, [fp, #-0x28]
    // 0x13c202c: r1 = Function '<anonymous closure>':.
    //     0x13c202c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3afb0] AnonymousClosure: (0x13c1c00), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::lineThemePaymentMethodCard (0x13c1fc4)
    //     0x13c2030: ldr             x1, [x1, #0xfb0]
    // 0x13c2034: stur            x0, [fp, #-8]
    // 0x13c2038: r0 = AllocateClosure()
    //     0x13c2038: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c203c: mov             x1, x0
    // 0x13c2040: ldur            x0, [fp, #-8]
    // 0x13c2044: StoreField: r0->field_b = r1
    //     0x13c2044: stur            w1, [x0, #0xb]
    // 0x13c2048: LeaveFrame
    //     0x13c2048: mov             SP, fp
    //     0x13c204c: ldp             fp, lr, [SP], #0x10
    // 0x13c2050: ret
    //     0x13c2050: ret             
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x15023c8, size: 0x70
    // 0x15023c8: EnterFrame
    //     0x15023c8: stp             fp, lr, [SP, #-0x10]!
    //     0x15023cc: mov             fp, SP
    // 0x15023d0: AllocStack(0x10)
    //     0x15023d0: sub             SP, SP, #0x10
    // 0x15023d4: CheckStackOverflow
    //     0x15023d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15023d8: cmp             SP, x16
    //     0x15023dc: b.ls            #0x1502430
    // 0x15023e0: r1 = Instance_Color
    //     0x15023e0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15023e4: d0 = 0.100000
    //     0x15023e4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x15023e8: r0 = withOpacity()
    //     0x15023e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x15023ec: stur            x0, [fp, #-8]
    // 0x15023f0: r0 = Divider()
    //     0x15023f0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x15023f4: mov             x1, x0
    // 0x15023f8: r0 = 1.000000
    //     0x15023f8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15023fc: stur            x1, [fp, #-0x10]
    // 0x1502400: StoreField: r1->field_f = r0
    //     0x1502400: stur            w0, [x1, #0xf]
    // 0x1502404: ldur            x0, [fp, #-8]
    // 0x1502408: StoreField: r1->field_1f = r0
    //     0x1502408: stur            w0, [x1, #0x1f]
    // 0x150240c: r0 = Padding()
    //     0x150240c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1502410: r1 = Instance_EdgeInsets
    //     0x1502410: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0x1502414: ldr             x1, [x1, #0xa00]
    // 0x1502418: StoreField: r0->field_f = r1
    //     0x1502418: stur            w1, [x0, #0xf]
    // 0x150241c: ldur            x1, [fp, #-0x10]
    // 0x1502420: StoreField: r0->field_b = r1
    //     0x1502420: stur            w1, [x0, #0xb]
    // 0x1502424: LeaveFrame
    //     0x1502424: mov             SP, fp
    //     0x1502428: ldp             fp, lr, [SP], #0x10
    // 0x150242c: ret
    //     0x150242c: ret             
    // 0x1502430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1502430: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1502434: b               #0x15023e0
  }
  [closure] ListView <anonymous closure>(dynamic) {
    // ** addr: 0x1502438, size: 0x1b6c
    // 0x1502438: EnterFrame
    //     0x1502438: stp             fp, lr, [SP, #-0x10]!
    //     0x150243c: mov             fp, SP
    // 0x1502440: AllocStack(0x90)
    //     0x1502440: sub             SP, SP, #0x90
    // 0x1502444: SetupParameters()
    //     0x1502444: ldr             x0, [fp, #0x10]
    //     0x1502448: ldur            w2, [x0, #0x17]
    //     0x150244c: add             x2, x2, HEAP, lsl #32
    //     0x1502450: stur            x2, [fp, #-8]
    // 0x1502454: CheckStackOverflow
    //     0x1502454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1502458: cmp             SP, x16
    //     0x150245c: b.ls            #0x1503f58
    // 0x1502460: LoadField: r1 = r2->field_13
    //     0x1502460: ldur            w1, [x2, #0x13]
    // 0x1502464: DecompressPointer r1
    //     0x1502464: add             x1, x1, HEAP, lsl #32
    // 0x1502468: r0 = of()
    //     0x1502468: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150246c: LoadField: r1 = r0->field_87
    //     0x150246c: ldur            w1, [x0, #0x87]
    // 0x1502470: DecompressPointer r1
    //     0x1502470: add             x1, x1, HEAP, lsl #32
    // 0x1502474: LoadField: r0 = r1->field_7
    //     0x1502474: ldur            w0, [x1, #7]
    // 0x1502478: DecompressPointer r0
    //     0x1502478: add             x0, x0, HEAP, lsl #32
    // 0x150247c: r16 = Instance_Color
    //     0x150247c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1502480: r30 = 14.000000
    //     0x1502480: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1502484: ldr             lr, [lr, #0x1d8]
    // 0x1502488: stp             lr, x16, [SP]
    // 0x150248c: mov             x1, x0
    // 0x1502490: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1502490: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1502494: ldr             x4, [x4, #0x9b8]
    // 0x1502498: r0 = copyWith()
    //     0x1502498: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150249c: stur            x0, [fp, #-0x10]
    // 0x15024a0: r0 = Text()
    //     0x15024a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15024a4: mov             x2, x0
    // 0x15024a8: r0 = "Bag"
    //     0x15024a8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0x15024ac: ldr             x0, [x0, #0xd60]
    // 0x15024b0: stur            x2, [fp, #-0x18]
    // 0x15024b4: StoreField: r2->field_b = r0
    //     0x15024b4: stur            w0, [x2, #0xb]
    // 0x15024b8: ldur            x0, [fp, #-0x10]
    // 0x15024bc: StoreField: r2->field_13 = r0
    //     0x15024bc: stur            w0, [x2, #0x13]
    // 0x15024c0: ldur            x0, [fp, #-8]
    // 0x15024c4: LoadField: r1 = r0->field_f
    //     0x15024c4: ldur            w1, [x0, #0xf]
    // 0x15024c8: DecompressPointer r1
    //     0x15024c8: add             x1, x1, HEAP, lsl #32
    // 0x15024cc: r0 = controller()
    //     0x15024cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15024d0: LoadField: r1 = r0->field_53
    //     0x15024d0: ldur            w1, [x0, #0x53]
    // 0x15024d4: DecompressPointer r1
    //     0x15024d4: add             x1, x1, HEAP, lsl #32
    // 0x15024d8: r0 = value()
    //     0x15024d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15024dc: LoadField: r2 = r0->field_5b
    //     0x15024dc: ldur            w2, [x0, #0x5b]
    // 0x15024e0: DecompressPointer r2
    //     0x15024e0: add             x2, x2, HEAP, lsl #32
    // 0x15024e4: cmp             w2, NULL
    // 0x15024e8: b.ne            #0x15024f4
    // 0x15024ec: r0 = Null
    //     0x15024ec: mov             x0, NULL
    // 0x15024f0: b               #0x1502524
    // 0x15024f4: LoadField: r0 = r2->field_b
    //     0x15024f4: ldur            w0, [x2, #0xb]
    // 0x15024f8: r1 = LoadInt32Instr(r0)
    //     0x15024f8: sbfx            x1, x0, #1, #0x1f
    // 0x15024fc: mov             x0, x1
    // 0x1502500: r1 = 0
    //     0x1502500: movz            x1, #0
    // 0x1502504: cmp             x1, x0
    // 0x1502508: b.hs            #0x1503f60
    // 0x150250c: LoadField: r0 = r2->field_f
    //     0x150250c: ldur            w0, [x2, #0xf]
    // 0x1502510: DecompressPointer r0
    //     0x1502510: add             x0, x0, HEAP, lsl #32
    // 0x1502514: LoadField: r1 = r0->field_f
    //     0x1502514: ldur            w1, [x0, #0xf]
    // 0x1502518: DecompressPointer r1
    //     0x1502518: add             x1, x1, HEAP, lsl #32
    // 0x150251c: LoadField: r0 = r1->field_f
    //     0x150251c: ldur            w0, [x1, #0xf]
    // 0x1502520: DecompressPointer r0
    //     0x1502520: add             x0, x0, HEAP, lsl #32
    // 0x1502524: cmp             w0, NULL
    // 0x1502528: b.ne            #0x1502530
    // 0x150252c: r0 = ""
    //     0x150252c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1502530: ldur            x2, [fp, #-8]
    // 0x1502534: stur            x0, [fp, #-0x10]
    // 0x1502538: r0 = ImageHeaders.forImages()
    //     0x1502538: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x150253c: r1 = Function '<anonymous closure>':.
    //     0x150253c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3af70] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x1502540: ldr             x1, [x1, #0xf70]
    // 0x1502544: r2 = Null
    //     0x1502544: mov             x2, NULL
    // 0x1502548: stur            x0, [fp, #-0x20]
    // 0x150254c: r0 = AllocateClosure()
    //     0x150254c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1502550: stur            x0, [fp, #-0x28]
    // 0x1502554: r0 = CachedNetworkImage()
    //     0x1502554: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x1502558: stur            x0, [fp, #-0x30]
    // 0x150255c: r16 = Instance_BoxFit
    //     0x150255c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x1502560: ldr             x16, [x16, #0x118]
    // 0x1502564: ldur            lr, [fp, #-0x20]
    // 0x1502568: stp             lr, x16, [SP, #0x18]
    // 0x150256c: r16 = 84.000000
    //     0x150256c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0x1502570: ldr             x16, [x16, #0xf90]
    // 0x1502574: r30 = 56.000000
    //     0x1502574: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1502578: ldr             lr, [lr, #0xb78]
    // 0x150257c: stp             lr, x16, [SP, #8]
    // 0x1502580: ldur            x16, [fp, #-0x28]
    // 0x1502584: str             x16, [SP]
    // 0x1502588: mov             x1, x0
    // 0x150258c: ldur            x2, [fp, #-0x10]
    // 0x1502590: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, httpHeaders, 0x3, width, 0x5, null]
    //     0x1502590: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d78] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "httpHeaders", 0x3, "width", 0x5, Null]
    //     0x1502594: ldr             x4, [x4, #0xd78]
    // 0x1502598: r0 = CachedNetworkImage()
    //     0x1502598: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x150259c: ldur            x2, [fp, #-8]
    // 0x15025a0: LoadField: r1 = r2->field_f
    //     0x15025a0: ldur            w1, [x2, #0xf]
    // 0x15025a4: DecompressPointer r1
    //     0x15025a4: add             x1, x1, HEAP, lsl #32
    // 0x15025a8: r0 = controller()
    //     0x15025a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15025ac: LoadField: r1 = r0->field_53
    //     0x15025ac: ldur            w1, [x0, #0x53]
    // 0x15025b0: DecompressPointer r1
    //     0x15025b0: add             x1, x1, HEAP, lsl #32
    // 0x15025b4: r0 = value()
    //     0x15025b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15025b8: LoadField: r2 = r0->field_5b
    //     0x15025b8: ldur            w2, [x0, #0x5b]
    // 0x15025bc: DecompressPointer r2
    //     0x15025bc: add             x2, x2, HEAP, lsl #32
    // 0x15025c0: cmp             w2, NULL
    // 0x15025c4: b.ne            #0x15025d0
    // 0x15025c8: r0 = Null
    //     0x15025c8: mov             x0, NULL
    // 0x15025cc: b               #0x1502600
    // 0x15025d0: LoadField: r0 = r2->field_b
    //     0x15025d0: ldur            w0, [x2, #0xb]
    // 0x15025d4: r1 = LoadInt32Instr(r0)
    //     0x15025d4: sbfx            x1, x0, #1, #0x1f
    // 0x15025d8: mov             x0, x1
    // 0x15025dc: r1 = 0
    //     0x15025dc: movz            x1, #0
    // 0x15025e0: cmp             x1, x0
    // 0x15025e4: b.hs            #0x1503f64
    // 0x15025e8: LoadField: r0 = r2->field_f
    //     0x15025e8: ldur            w0, [x2, #0xf]
    // 0x15025ec: DecompressPointer r0
    //     0x15025ec: add             x0, x0, HEAP, lsl #32
    // 0x15025f0: LoadField: r1 = r0->field_f
    //     0x15025f0: ldur            w1, [x0, #0xf]
    // 0x15025f4: DecompressPointer r1
    //     0x15025f4: add             x1, x1, HEAP, lsl #32
    // 0x15025f8: LoadField: r0 = r1->field_13
    //     0x15025f8: ldur            w0, [x1, #0x13]
    // 0x15025fc: DecompressPointer r0
    //     0x15025fc: add             x0, x0, HEAP, lsl #32
    // 0x1502600: cmp             w0, NULL
    // 0x1502604: b.ne            #0x150260c
    // 0x1502608: r0 = ""
    //     0x1502608: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x150260c: ldur            x2, [fp, #-8]
    // 0x1502610: stur            x0, [fp, #-0x10]
    // 0x1502614: LoadField: r1 = r2->field_13
    //     0x1502614: ldur            w1, [x2, #0x13]
    // 0x1502618: DecompressPointer r1
    //     0x1502618: add             x1, x1, HEAP, lsl #32
    // 0x150261c: r0 = of()
    //     0x150261c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1502620: LoadField: r1 = r0->field_87
    //     0x1502620: ldur            w1, [x0, #0x87]
    // 0x1502624: DecompressPointer r1
    //     0x1502624: add             x1, x1, HEAP, lsl #32
    // 0x1502628: LoadField: r0 = r1->field_7
    //     0x1502628: ldur            w0, [x1, #7]
    // 0x150262c: DecompressPointer r0
    //     0x150262c: add             x0, x0, HEAP, lsl #32
    // 0x1502630: r16 = Instance_Color
    //     0x1502630: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1502634: r30 = 12.000000
    //     0x1502634: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1502638: ldr             lr, [lr, #0x9e8]
    // 0x150263c: stp             lr, x16, [SP]
    // 0x1502640: mov             x1, x0
    // 0x1502644: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1502644: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1502648: ldr             x4, [x4, #0x9b8]
    // 0x150264c: r0 = copyWith()
    //     0x150264c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1502650: stur            x0, [fp, #-0x20]
    // 0x1502654: r0 = Text()
    //     0x1502654: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1502658: mov             x2, x0
    // 0x150265c: ldur            x0, [fp, #-0x10]
    // 0x1502660: stur            x2, [fp, #-0x28]
    // 0x1502664: StoreField: r2->field_b = r0
    //     0x1502664: stur            w0, [x2, #0xb]
    // 0x1502668: ldur            x0, [fp, #-0x20]
    // 0x150266c: StoreField: r2->field_13 = r0
    //     0x150266c: stur            w0, [x2, #0x13]
    // 0x1502670: r0 = 2
    //     0x1502670: movz            x0, #0x2
    // 0x1502674: StoreField: r2->field_37 = r0
    //     0x1502674: stur            w0, [x2, #0x37]
    // 0x1502678: ldur            x3, [fp, #-8]
    // 0x150267c: LoadField: r1 = r3->field_f
    //     0x150267c: ldur            w1, [x3, #0xf]
    // 0x1502680: DecompressPointer r1
    //     0x1502680: add             x1, x1, HEAP, lsl #32
    // 0x1502684: r0 = controller()
    //     0x1502684: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1502688: LoadField: r1 = r0->field_53
    //     0x1502688: ldur            w1, [x0, #0x53]
    // 0x150268c: DecompressPointer r1
    //     0x150268c: add             x1, x1, HEAP, lsl #32
    // 0x1502690: r0 = value()
    //     0x1502690: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502694: LoadField: r2 = r0->field_5b
    //     0x1502694: ldur            w2, [x0, #0x5b]
    // 0x1502698: DecompressPointer r2
    //     0x1502698: add             x2, x2, HEAP, lsl #32
    // 0x150269c: cmp             w2, NULL
    // 0x15026a0: b.ne            #0x15026ac
    // 0x15026a4: r0 = Null
    //     0x15026a4: mov             x0, NULL
    // 0x15026a8: b               #0x15026dc
    // 0x15026ac: LoadField: r0 = r2->field_b
    //     0x15026ac: ldur            w0, [x2, #0xb]
    // 0x15026b0: r1 = LoadInt32Instr(r0)
    //     0x15026b0: sbfx            x1, x0, #1, #0x1f
    // 0x15026b4: mov             x0, x1
    // 0x15026b8: r1 = 0
    //     0x15026b8: movz            x1, #0
    // 0x15026bc: cmp             x1, x0
    // 0x15026c0: b.hs            #0x1503f68
    // 0x15026c4: LoadField: r0 = r2->field_f
    //     0x15026c4: ldur            w0, [x2, #0xf]
    // 0x15026c8: DecompressPointer r0
    //     0x15026c8: add             x0, x0, HEAP, lsl #32
    // 0x15026cc: LoadField: r1 = r0->field_f
    //     0x15026cc: ldur            w1, [x0, #0xf]
    // 0x15026d0: DecompressPointer r1
    //     0x15026d0: add             x1, x1, HEAP, lsl #32
    // 0x15026d4: LoadField: r0 = r1->field_13
    //     0x15026d4: ldur            w0, [x1, #0x13]
    // 0x15026d8: DecompressPointer r0
    //     0x15026d8: add             x0, x0, HEAP, lsl #32
    // 0x15026dc: r1 = LoadClassIdInstr(r0)
    //     0x15026dc: ldur            x1, [x0, #-1]
    //     0x15026e0: ubfx            x1, x1, #0xc, #0x14
    // 0x15026e4: r16 = "size"
    //     0x15026e4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x15026e8: ldr             x16, [x16, #0x9c0]
    // 0x15026ec: stp             x16, x0, [SP]
    // 0x15026f0: mov             x0, x1
    // 0x15026f4: mov             lr, x0
    // 0x15026f8: ldr             lr, [x21, lr, lsl #3]
    // 0x15026fc: blr             lr
    // 0x1502700: tbnz            w0, #4, #0x15028dc
    // 0x1502704: ldur            x0, [fp, #-8]
    // 0x1502708: r1 = Null
    //     0x1502708: mov             x1, NULL
    // 0x150270c: r2 = 8
    //     0x150270c: movz            x2, #0x8
    // 0x1502710: r0 = AllocateArray()
    //     0x1502710: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1502714: stur            x0, [fp, #-0x10]
    // 0x1502718: r16 = "Size: "
    //     0x1502718: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x150271c: ldr             x16, [x16, #0xf00]
    // 0x1502720: StoreField: r0->field_f = r16
    //     0x1502720: stur            w16, [x0, #0xf]
    // 0x1502724: ldur            x2, [fp, #-8]
    // 0x1502728: LoadField: r1 = r2->field_f
    //     0x1502728: ldur            w1, [x2, #0xf]
    // 0x150272c: DecompressPointer r1
    //     0x150272c: add             x1, x1, HEAP, lsl #32
    // 0x1502730: r0 = controller()
    //     0x1502730: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1502734: LoadField: r1 = r0->field_53
    //     0x1502734: ldur            w1, [x0, #0x53]
    // 0x1502738: DecompressPointer r1
    //     0x1502738: add             x1, x1, HEAP, lsl #32
    // 0x150273c: r0 = value()
    //     0x150273c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502740: LoadField: r2 = r0->field_5b
    //     0x1502740: ldur            w2, [x0, #0x5b]
    // 0x1502744: DecompressPointer r2
    //     0x1502744: add             x2, x2, HEAP, lsl #32
    // 0x1502748: cmp             w2, NULL
    // 0x150274c: b.ne            #0x1502758
    // 0x1502750: r0 = Null
    //     0x1502750: mov             x0, NULL
    // 0x1502754: b               #0x1502788
    // 0x1502758: LoadField: r0 = r2->field_b
    //     0x1502758: ldur            w0, [x2, #0xb]
    // 0x150275c: r1 = LoadInt32Instr(r0)
    //     0x150275c: sbfx            x1, x0, #1, #0x1f
    // 0x1502760: mov             x0, x1
    // 0x1502764: r1 = 0
    //     0x1502764: movz            x1, #0
    // 0x1502768: cmp             x1, x0
    // 0x150276c: b.hs            #0x1503f6c
    // 0x1502770: LoadField: r0 = r2->field_f
    //     0x1502770: ldur            w0, [x2, #0xf]
    // 0x1502774: DecompressPointer r0
    //     0x1502774: add             x0, x0, HEAP, lsl #32
    // 0x1502778: LoadField: r1 = r0->field_f
    //     0x1502778: ldur            w1, [x0, #0xf]
    // 0x150277c: DecompressPointer r1
    //     0x150277c: add             x1, x1, HEAP, lsl #32
    // 0x1502780: LoadField: r0 = r1->field_33
    //     0x1502780: ldur            w0, [x1, #0x33]
    // 0x1502784: DecompressPointer r0
    //     0x1502784: add             x0, x0, HEAP, lsl #32
    // 0x1502788: cmp             w0, NULL
    // 0x150278c: b.ne            #0x1502794
    // 0x1502790: r0 = ""
    //     0x1502790: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1502794: ldur            x3, [fp, #-8]
    // 0x1502798: ldur            x2, [fp, #-0x10]
    // 0x150279c: mov             x1, x2
    // 0x15027a0: ArrayStore: r1[1] = r0  ; List_4
    //     0x15027a0: add             x25, x1, #0x13
    //     0x15027a4: str             w0, [x25]
    //     0x15027a8: tbz             w0, #0, #0x15027c4
    //     0x15027ac: ldurb           w16, [x1, #-1]
    //     0x15027b0: ldurb           w17, [x0, #-1]
    //     0x15027b4: and             x16, x17, x16, lsr #2
    //     0x15027b8: tst             x16, HEAP, lsr #32
    //     0x15027bc: b.eq            #0x15027c4
    //     0x15027c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15027c4: r16 = " / Qty: "
    //     0x15027c4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x15027c8: ldr             x16, [x16, #0x760]
    // 0x15027cc: ArrayStore: r2[0] = r16  ; List_4
    //     0x15027cc: stur            w16, [x2, #0x17]
    // 0x15027d0: LoadField: r1 = r3->field_f
    //     0x15027d0: ldur            w1, [x3, #0xf]
    // 0x15027d4: DecompressPointer r1
    //     0x15027d4: add             x1, x1, HEAP, lsl #32
    // 0x15027d8: r0 = controller()
    //     0x15027d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15027dc: LoadField: r1 = r0->field_53
    //     0x15027dc: ldur            w1, [x0, #0x53]
    // 0x15027e0: DecompressPointer r1
    //     0x15027e0: add             x1, x1, HEAP, lsl #32
    // 0x15027e4: r0 = value()
    //     0x15027e4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15027e8: LoadField: r2 = r0->field_5b
    //     0x15027e8: ldur            w2, [x0, #0x5b]
    // 0x15027ec: DecompressPointer r2
    //     0x15027ec: add             x2, x2, HEAP, lsl #32
    // 0x15027f0: cmp             w2, NULL
    // 0x15027f4: b.ne            #0x1502800
    // 0x15027f8: r0 = Null
    //     0x15027f8: mov             x0, NULL
    // 0x15027fc: b               #0x1502830
    // 0x1502800: LoadField: r0 = r2->field_b
    //     0x1502800: ldur            w0, [x2, #0xb]
    // 0x1502804: r1 = LoadInt32Instr(r0)
    //     0x1502804: sbfx            x1, x0, #1, #0x1f
    // 0x1502808: mov             x0, x1
    // 0x150280c: r1 = 0
    //     0x150280c: movz            x1, #0
    // 0x1502810: cmp             x1, x0
    // 0x1502814: b.hs            #0x1503f70
    // 0x1502818: LoadField: r0 = r2->field_f
    //     0x1502818: ldur            w0, [x2, #0xf]
    // 0x150281c: DecompressPointer r0
    //     0x150281c: add             x0, x0, HEAP, lsl #32
    // 0x1502820: LoadField: r1 = r0->field_f
    //     0x1502820: ldur            w1, [x0, #0xf]
    // 0x1502824: DecompressPointer r1
    //     0x1502824: add             x1, x1, HEAP, lsl #32
    // 0x1502828: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1502828: ldur            w0, [x1, #0x17]
    // 0x150282c: DecompressPointer r0
    //     0x150282c: add             x0, x0, HEAP, lsl #32
    // 0x1502830: cmp             w0, NULL
    // 0x1502834: b.ne            #0x150283c
    // 0x1502838: r0 = ""
    //     0x1502838: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x150283c: ldur            x2, [fp, #-8]
    // 0x1502840: ldur            x1, [fp, #-0x10]
    // 0x1502844: ArrayStore: r1[3] = r0  ; List_4
    //     0x1502844: add             x25, x1, #0x1b
    //     0x1502848: str             w0, [x25]
    //     0x150284c: tbz             w0, #0, #0x1502868
    //     0x1502850: ldurb           w16, [x1, #-1]
    //     0x1502854: ldurb           w17, [x0, #-1]
    //     0x1502858: and             x16, x17, x16, lsr #2
    //     0x150285c: tst             x16, HEAP, lsr #32
    //     0x1502860: b.eq            #0x1502868
    //     0x1502864: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1502868: ldur            x16, [fp, #-0x10]
    // 0x150286c: str             x16, [SP]
    // 0x1502870: r0 = _interpolate()
    //     0x1502870: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1502874: ldur            x2, [fp, #-8]
    // 0x1502878: stur            x0, [fp, #-0x10]
    // 0x150287c: LoadField: r1 = r2->field_13
    //     0x150287c: ldur            w1, [x2, #0x13]
    // 0x1502880: DecompressPointer r1
    //     0x1502880: add             x1, x1, HEAP, lsl #32
    // 0x1502884: r0 = of()
    //     0x1502884: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1502888: LoadField: r1 = r0->field_87
    //     0x1502888: ldur            w1, [x0, #0x87]
    // 0x150288c: DecompressPointer r1
    //     0x150288c: add             x1, x1, HEAP, lsl #32
    // 0x1502890: LoadField: r0 = r1->field_2b
    //     0x1502890: ldur            w0, [x1, #0x2b]
    // 0x1502894: DecompressPointer r0
    //     0x1502894: add             x0, x0, HEAP, lsl #32
    // 0x1502898: r16 = 12.000000
    //     0x1502898: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x150289c: ldr             x16, [x16, #0x9e8]
    // 0x15028a0: r30 = Instance_Color
    //     0x15028a0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15028a4: stp             lr, x16, [SP]
    // 0x15028a8: mov             x1, x0
    // 0x15028ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15028ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15028b0: ldr             x4, [x4, #0xaa0]
    // 0x15028b4: r0 = copyWith()
    //     0x15028b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15028b8: stur            x0, [fp, #-0x20]
    // 0x15028bc: r0 = Text()
    //     0x15028bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15028c0: mov             x1, x0
    // 0x15028c4: ldur            x0, [fp, #-0x10]
    // 0x15028c8: StoreField: r1->field_b = r0
    //     0x15028c8: stur            w0, [x1, #0xb]
    // 0x15028cc: ldur            x0, [fp, #-0x20]
    // 0x15028d0: StoreField: r1->field_13 = r0
    //     0x15028d0: stur            w0, [x1, #0x13]
    // 0x15028d4: mov             x0, x1
    // 0x15028d8: b               #0x1502ab0
    // 0x15028dc: ldur            x0, [fp, #-8]
    // 0x15028e0: r1 = Null
    //     0x15028e0: mov             x1, NULL
    // 0x15028e4: r2 = 8
    //     0x15028e4: movz            x2, #0x8
    // 0x15028e8: r0 = AllocateArray()
    //     0x15028e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15028ec: stur            x0, [fp, #-0x10]
    // 0x15028f0: r16 = "Variant: "
    //     0x15028f0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x15028f4: ldr             x16, [x16, #0xf08]
    // 0x15028f8: StoreField: r0->field_f = r16
    //     0x15028f8: stur            w16, [x0, #0xf]
    // 0x15028fc: ldur            x2, [fp, #-8]
    // 0x1502900: LoadField: r1 = r2->field_f
    //     0x1502900: ldur            w1, [x2, #0xf]
    // 0x1502904: DecompressPointer r1
    //     0x1502904: add             x1, x1, HEAP, lsl #32
    // 0x1502908: r0 = controller()
    //     0x1502908: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x150290c: LoadField: r1 = r0->field_53
    //     0x150290c: ldur            w1, [x0, #0x53]
    // 0x1502910: DecompressPointer r1
    //     0x1502910: add             x1, x1, HEAP, lsl #32
    // 0x1502914: r0 = value()
    //     0x1502914: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502918: LoadField: r2 = r0->field_5b
    //     0x1502918: ldur            w2, [x0, #0x5b]
    // 0x150291c: DecompressPointer r2
    //     0x150291c: add             x2, x2, HEAP, lsl #32
    // 0x1502920: cmp             w2, NULL
    // 0x1502924: b.ne            #0x1502930
    // 0x1502928: r0 = Null
    //     0x1502928: mov             x0, NULL
    // 0x150292c: b               #0x1502960
    // 0x1502930: LoadField: r0 = r2->field_b
    //     0x1502930: ldur            w0, [x2, #0xb]
    // 0x1502934: r1 = LoadInt32Instr(r0)
    //     0x1502934: sbfx            x1, x0, #1, #0x1f
    // 0x1502938: mov             x0, x1
    // 0x150293c: r1 = 0
    //     0x150293c: movz            x1, #0
    // 0x1502940: cmp             x1, x0
    // 0x1502944: b.hs            #0x1503f74
    // 0x1502948: LoadField: r0 = r2->field_f
    //     0x1502948: ldur            w0, [x2, #0xf]
    // 0x150294c: DecompressPointer r0
    //     0x150294c: add             x0, x0, HEAP, lsl #32
    // 0x1502950: LoadField: r1 = r0->field_f
    //     0x1502950: ldur            w1, [x0, #0xf]
    // 0x1502954: DecompressPointer r1
    //     0x1502954: add             x1, x1, HEAP, lsl #32
    // 0x1502958: LoadField: r0 = r1->field_33
    //     0x1502958: ldur            w0, [x1, #0x33]
    // 0x150295c: DecompressPointer r0
    //     0x150295c: add             x0, x0, HEAP, lsl #32
    // 0x1502960: cmp             w0, NULL
    // 0x1502964: b.ne            #0x150296c
    // 0x1502968: r0 = ""
    //     0x1502968: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x150296c: ldur            x3, [fp, #-8]
    // 0x1502970: ldur            x2, [fp, #-0x10]
    // 0x1502974: mov             x1, x2
    // 0x1502978: ArrayStore: r1[1] = r0  ; List_4
    //     0x1502978: add             x25, x1, #0x13
    //     0x150297c: str             w0, [x25]
    //     0x1502980: tbz             w0, #0, #0x150299c
    //     0x1502984: ldurb           w16, [x1, #-1]
    //     0x1502988: ldurb           w17, [x0, #-1]
    //     0x150298c: and             x16, x17, x16, lsr #2
    //     0x1502990: tst             x16, HEAP, lsr #32
    //     0x1502994: b.eq            #0x150299c
    //     0x1502998: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x150299c: r16 = " / Qty: "
    //     0x150299c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x15029a0: ldr             x16, [x16, #0x760]
    // 0x15029a4: ArrayStore: r2[0] = r16  ; List_4
    //     0x15029a4: stur            w16, [x2, #0x17]
    // 0x15029a8: LoadField: r1 = r3->field_f
    //     0x15029a8: ldur            w1, [x3, #0xf]
    // 0x15029ac: DecompressPointer r1
    //     0x15029ac: add             x1, x1, HEAP, lsl #32
    // 0x15029b0: r0 = controller()
    //     0x15029b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15029b4: LoadField: r1 = r0->field_53
    //     0x15029b4: ldur            w1, [x0, #0x53]
    // 0x15029b8: DecompressPointer r1
    //     0x15029b8: add             x1, x1, HEAP, lsl #32
    // 0x15029bc: r0 = value()
    //     0x15029bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15029c0: LoadField: r2 = r0->field_5b
    //     0x15029c0: ldur            w2, [x0, #0x5b]
    // 0x15029c4: DecompressPointer r2
    //     0x15029c4: add             x2, x2, HEAP, lsl #32
    // 0x15029c8: cmp             w2, NULL
    // 0x15029cc: b.ne            #0x15029d8
    // 0x15029d0: r0 = Null
    //     0x15029d0: mov             x0, NULL
    // 0x15029d4: b               #0x1502a08
    // 0x15029d8: LoadField: r0 = r2->field_b
    //     0x15029d8: ldur            w0, [x2, #0xb]
    // 0x15029dc: r1 = LoadInt32Instr(r0)
    //     0x15029dc: sbfx            x1, x0, #1, #0x1f
    // 0x15029e0: mov             x0, x1
    // 0x15029e4: r1 = 0
    //     0x15029e4: movz            x1, #0
    // 0x15029e8: cmp             x1, x0
    // 0x15029ec: b.hs            #0x1503f78
    // 0x15029f0: LoadField: r0 = r2->field_f
    //     0x15029f0: ldur            w0, [x2, #0xf]
    // 0x15029f4: DecompressPointer r0
    //     0x15029f4: add             x0, x0, HEAP, lsl #32
    // 0x15029f8: LoadField: r1 = r0->field_f
    //     0x15029f8: ldur            w1, [x0, #0xf]
    // 0x15029fc: DecompressPointer r1
    //     0x15029fc: add             x1, x1, HEAP, lsl #32
    // 0x1502a00: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1502a00: ldur            w0, [x1, #0x17]
    // 0x1502a04: DecompressPointer r0
    //     0x1502a04: add             x0, x0, HEAP, lsl #32
    // 0x1502a08: cmp             w0, NULL
    // 0x1502a0c: b.ne            #0x1502a14
    // 0x1502a10: r0 = ""
    //     0x1502a10: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1502a14: ldur            x2, [fp, #-8]
    // 0x1502a18: ldur            x1, [fp, #-0x10]
    // 0x1502a1c: ArrayStore: r1[3] = r0  ; List_4
    //     0x1502a1c: add             x25, x1, #0x1b
    //     0x1502a20: str             w0, [x25]
    //     0x1502a24: tbz             w0, #0, #0x1502a40
    //     0x1502a28: ldurb           w16, [x1, #-1]
    //     0x1502a2c: ldurb           w17, [x0, #-1]
    //     0x1502a30: and             x16, x17, x16, lsr #2
    //     0x1502a34: tst             x16, HEAP, lsr #32
    //     0x1502a38: b.eq            #0x1502a40
    //     0x1502a3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1502a40: ldur            x16, [fp, #-0x10]
    // 0x1502a44: str             x16, [SP]
    // 0x1502a48: r0 = _interpolate()
    //     0x1502a48: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1502a4c: ldur            x2, [fp, #-8]
    // 0x1502a50: stur            x0, [fp, #-0x10]
    // 0x1502a54: LoadField: r1 = r2->field_13
    //     0x1502a54: ldur            w1, [x2, #0x13]
    // 0x1502a58: DecompressPointer r1
    //     0x1502a58: add             x1, x1, HEAP, lsl #32
    // 0x1502a5c: r0 = of()
    //     0x1502a5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1502a60: LoadField: r1 = r0->field_87
    //     0x1502a60: ldur            w1, [x0, #0x87]
    // 0x1502a64: DecompressPointer r1
    //     0x1502a64: add             x1, x1, HEAP, lsl #32
    // 0x1502a68: LoadField: r0 = r1->field_2b
    //     0x1502a68: ldur            w0, [x1, #0x2b]
    // 0x1502a6c: DecompressPointer r0
    //     0x1502a6c: add             x0, x0, HEAP, lsl #32
    // 0x1502a70: r16 = 12.000000
    //     0x1502a70: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1502a74: ldr             x16, [x16, #0x9e8]
    // 0x1502a78: r30 = Instance_Color
    //     0x1502a78: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1502a7c: stp             lr, x16, [SP]
    // 0x1502a80: mov             x1, x0
    // 0x1502a84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1502a84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1502a88: ldr             x4, [x4, #0xaa0]
    // 0x1502a8c: r0 = copyWith()
    //     0x1502a8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1502a90: stur            x0, [fp, #-0x20]
    // 0x1502a94: r0 = Text()
    //     0x1502a94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1502a98: mov             x1, x0
    // 0x1502a9c: ldur            x0, [fp, #-0x10]
    // 0x1502aa0: StoreField: r1->field_b = r0
    //     0x1502aa0: stur            w0, [x1, #0xb]
    // 0x1502aa4: ldur            x0, [fp, #-0x20]
    // 0x1502aa8: StoreField: r1->field_13 = r0
    //     0x1502aa8: stur            w0, [x1, #0x13]
    // 0x1502aac: mov             x0, x1
    // 0x1502ab0: ldur            x2, [fp, #-8]
    // 0x1502ab4: stur            x0, [fp, #-0x10]
    // 0x1502ab8: r0 = Padding()
    //     0x1502ab8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1502abc: mov             x2, x0
    // 0x1502ac0: r0 = Instance_EdgeInsets
    //     0x1502ac0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x1502ac4: ldr             x0, [x0, #0x770]
    // 0x1502ac8: stur            x2, [fp, #-0x20]
    // 0x1502acc: StoreField: r2->field_f = r0
    //     0x1502acc: stur            w0, [x2, #0xf]
    // 0x1502ad0: ldur            x1, [fp, #-0x10]
    // 0x1502ad4: StoreField: r2->field_b = r1
    //     0x1502ad4: stur            w1, [x2, #0xb]
    // 0x1502ad8: ldur            x3, [fp, #-8]
    // 0x1502adc: LoadField: r1 = r3->field_f
    //     0x1502adc: ldur            w1, [x3, #0xf]
    // 0x1502ae0: DecompressPointer r1
    //     0x1502ae0: add             x1, x1, HEAP, lsl #32
    // 0x1502ae4: r0 = controller()
    //     0x1502ae4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1502ae8: LoadField: r1 = r0->field_53
    //     0x1502ae8: ldur            w1, [x0, #0x53]
    // 0x1502aec: DecompressPointer r1
    //     0x1502aec: add             x1, x1, HEAP, lsl #32
    // 0x1502af0: r0 = value()
    //     0x1502af0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502af4: LoadField: r2 = r0->field_5b
    //     0x1502af4: ldur            w2, [x0, #0x5b]
    // 0x1502af8: DecompressPointer r2
    //     0x1502af8: add             x2, x2, HEAP, lsl #32
    // 0x1502afc: cmp             w2, NULL
    // 0x1502b00: b.ne            #0x1502b0c
    // 0x1502b04: r0 = Null
    //     0x1502b04: mov             x0, NULL
    // 0x1502b08: b               #0x1502b3c
    // 0x1502b0c: LoadField: r0 = r2->field_b
    //     0x1502b0c: ldur            w0, [x2, #0xb]
    // 0x1502b10: r1 = LoadInt32Instr(r0)
    //     0x1502b10: sbfx            x1, x0, #1, #0x1f
    // 0x1502b14: mov             x0, x1
    // 0x1502b18: r1 = 0
    //     0x1502b18: movz            x1, #0
    // 0x1502b1c: cmp             x1, x0
    // 0x1502b20: b.hs            #0x1503f7c
    // 0x1502b24: LoadField: r0 = r2->field_f
    //     0x1502b24: ldur            w0, [x2, #0xf]
    // 0x1502b28: DecompressPointer r0
    //     0x1502b28: add             x0, x0, HEAP, lsl #32
    // 0x1502b2c: LoadField: r1 = r0->field_f
    //     0x1502b2c: ldur            w1, [x0, #0xf]
    // 0x1502b30: DecompressPointer r1
    //     0x1502b30: add             x1, x1, HEAP, lsl #32
    // 0x1502b34: LoadField: r0 = r1->field_2f
    //     0x1502b34: ldur            w0, [x1, #0x2f]
    // 0x1502b38: DecompressPointer r0
    //     0x1502b38: add             x0, x0, HEAP, lsl #32
    // 0x1502b3c: cmp             w0, NULL
    // 0x1502b40: b.ne            #0x1502b4c
    // 0x1502b44: r5 = ""
    //     0x1502b44: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1502b48: b               #0x1502b50
    // 0x1502b4c: mov             x5, x0
    // 0x1502b50: ldur            x2, [fp, #-8]
    // 0x1502b54: ldur            x4, [fp, #-0x30]
    // 0x1502b58: ldur            x3, [fp, #-0x28]
    // 0x1502b5c: ldur            x0, [fp, #-0x20]
    // 0x1502b60: stur            x5, [fp, #-0x10]
    // 0x1502b64: LoadField: r1 = r2->field_13
    //     0x1502b64: ldur            w1, [x2, #0x13]
    // 0x1502b68: DecompressPointer r1
    //     0x1502b68: add             x1, x1, HEAP, lsl #32
    // 0x1502b6c: r0 = of()
    //     0x1502b6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1502b70: LoadField: r1 = r0->field_87
    //     0x1502b70: ldur            w1, [x0, #0x87]
    // 0x1502b74: DecompressPointer r1
    //     0x1502b74: add             x1, x1, HEAP, lsl #32
    // 0x1502b78: LoadField: r0 = r1->field_7
    //     0x1502b78: ldur            w0, [x1, #7]
    // 0x1502b7c: DecompressPointer r0
    //     0x1502b7c: add             x0, x0, HEAP, lsl #32
    // 0x1502b80: r16 = Instance_Color
    //     0x1502b80: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1502b84: r30 = 12.000000
    //     0x1502b84: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1502b88: ldr             lr, [lr, #0x9e8]
    // 0x1502b8c: stp             lr, x16, [SP]
    // 0x1502b90: mov             x1, x0
    // 0x1502b94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1502b94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1502b98: ldr             x4, [x4, #0x9b8]
    // 0x1502b9c: r0 = copyWith()
    //     0x1502b9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1502ba0: stur            x0, [fp, #-0x38]
    // 0x1502ba4: r0 = Text()
    //     0x1502ba4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1502ba8: mov             x3, x0
    // 0x1502bac: ldur            x0, [fp, #-0x10]
    // 0x1502bb0: stur            x3, [fp, #-0x40]
    // 0x1502bb4: StoreField: r3->field_b = r0
    //     0x1502bb4: stur            w0, [x3, #0xb]
    // 0x1502bb8: ldur            x0, [fp, #-0x38]
    // 0x1502bbc: StoreField: r3->field_13 = r0
    //     0x1502bbc: stur            w0, [x3, #0x13]
    // 0x1502bc0: r1 = Null
    //     0x1502bc0: mov             x1, NULL
    // 0x1502bc4: r2 = 8
    //     0x1502bc4: movz            x2, #0x8
    // 0x1502bc8: r0 = AllocateArray()
    //     0x1502bc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1502bcc: mov             x2, x0
    // 0x1502bd0: ldur            x0, [fp, #-0x28]
    // 0x1502bd4: stur            x2, [fp, #-0x10]
    // 0x1502bd8: StoreField: r2->field_f = r0
    //     0x1502bd8: stur            w0, [x2, #0xf]
    // 0x1502bdc: ldur            x0, [fp, #-0x20]
    // 0x1502be0: StoreField: r2->field_13 = r0
    //     0x1502be0: stur            w0, [x2, #0x13]
    // 0x1502be4: r16 = Instance_SizedBox
    //     0x1502be4: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1502be8: ldr             x16, [x16, #0xc70]
    // 0x1502bec: ArrayStore: r2[0] = r16  ; List_4
    //     0x1502bec: stur            w16, [x2, #0x17]
    // 0x1502bf0: ldur            x0, [fp, #-0x40]
    // 0x1502bf4: StoreField: r2->field_1b = r0
    //     0x1502bf4: stur            w0, [x2, #0x1b]
    // 0x1502bf8: r1 = <Widget>
    //     0x1502bf8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1502bfc: r0 = AllocateGrowableArray()
    //     0x1502bfc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1502c00: mov             x1, x0
    // 0x1502c04: ldur            x0, [fp, #-0x10]
    // 0x1502c08: stur            x1, [fp, #-0x20]
    // 0x1502c0c: StoreField: r1->field_f = r0
    //     0x1502c0c: stur            w0, [x1, #0xf]
    // 0x1502c10: r2 = 8
    //     0x1502c10: movz            x2, #0x8
    // 0x1502c14: StoreField: r1->field_b = r2
    //     0x1502c14: stur            w2, [x1, #0xb]
    // 0x1502c18: r0 = Column()
    //     0x1502c18: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1502c1c: mov             x2, x0
    // 0x1502c20: r0 = Instance_Axis
    //     0x1502c20: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1502c24: stur            x2, [fp, #-0x10]
    // 0x1502c28: StoreField: r2->field_f = r0
    //     0x1502c28: stur            w0, [x2, #0xf]
    // 0x1502c2c: r3 = Instance_MainAxisAlignment
    //     0x1502c2c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1502c30: ldr             x3, [x3, #0xa08]
    // 0x1502c34: StoreField: r2->field_13 = r3
    //     0x1502c34: stur            w3, [x2, #0x13]
    // 0x1502c38: r4 = Instance_MainAxisSize
    //     0x1502c38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1502c3c: ldr             x4, [x4, #0xa10]
    // 0x1502c40: ArrayStore: r2[0] = r4  ; List_4
    //     0x1502c40: stur            w4, [x2, #0x17]
    // 0x1502c44: r5 = Instance_CrossAxisAlignment
    //     0x1502c44: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1502c48: ldr             x5, [x5, #0x890]
    // 0x1502c4c: StoreField: r2->field_1b = r5
    //     0x1502c4c: stur            w5, [x2, #0x1b]
    // 0x1502c50: r6 = Instance_VerticalDirection
    //     0x1502c50: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1502c54: ldr             x6, [x6, #0xa20]
    // 0x1502c58: StoreField: r2->field_23 = r6
    //     0x1502c58: stur            w6, [x2, #0x23]
    // 0x1502c5c: r7 = Instance_Clip
    //     0x1502c5c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1502c60: ldr             x7, [x7, #0x38]
    // 0x1502c64: StoreField: r2->field_2b = r7
    //     0x1502c64: stur            w7, [x2, #0x2b]
    // 0x1502c68: StoreField: r2->field_2f = rZR
    //     0x1502c68: stur            xzr, [x2, #0x2f]
    // 0x1502c6c: ldur            x1, [fp, #-0x20]
    // 0x1502c70: StoreField: r2->field_b = r1
    //     0x1502c70: stur            w1, [x2, #0xb]
    // 0x1502c74: r1 = <FlexParentData>
    //     0x1502c74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1502c78: ldr             x1, [x1, #0xe00]
    // 0x1502c7c: r0 = Expanded()
    //     0x1502c7c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1502c80: mov             x3, x0
    // 0x1502c84: r0 = 1
    //     0x1502c84: movz            x0, #0x1
    // 0x1502c88: stur            x3, [fp, #-0x20]
    // 0x1502c8c: StoreField: r3->field_13 = r0
    //     0x1502c8c: stur            x0, [x3, #0x13]
    // 0x1502c90: r4 = Instance_FlexFit
    //     0x1502c90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1502c94: ldr             x4, [x4, #0xe08]
    // 0x1502c98: StoreField: r3->field_1b = r4
    //     0x1502c98: stur            w4, [x3, #0x1b]
    // 0x1502c9c: ldur            x1, [fp, #-0x10]
    // 0x1502ca0: StoreField: r3->field_b = r1
    //     0x1502ca0: stur            w1, [x3, #0xb]
    // 0x1502ca4: r1 = Null
    //     0x1502ca4: mov             x1, NULL
    // 0x1502ca8: r2 = 6
    //     0x1502ca8: movz            x2, #0x6
    // 0x1502cac: r0 = AllocateArray()
    //     0x1502cac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1502cb0: mov             x2, x0
    // 0x1502cb4: ldur            x0, [fp, #-0x30]
    // 0x1502cb8: stur            x2, [fp, #-0x10]
    // 0x1502cbc: StoreField: r2->field_f = r0
    //     0x1502cbc: stur            w0, [x2, #0xf]
    // 0x1502cc0: r16 = Instance_SizedBox
    //     0x1502cc0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x1502cc4: ldr             x16, [x16, #0xb20]
    // 0x1502cc8: StoreField: r2->field_13 = r16
    //     0x1502cc8: stur            w16, [x2, #0x13]
    // 0x1502ccc: ldur            x0, [fp, #-0x20]
    // 0x1502cd0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1502cd0: stur            w0, [x2, #0x17]
    // 0x1502cd4: r1 = <Widget>
    //     0x1502cd4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1502cd8: r0 = AllocateGrowableArray()
    //     0x1502cd8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1502cdc: mov             x1, x0
    // 0x1502ce0: ldur            x0, [fp, #-0x10]
    // 0x1502ce4: stur            x1, [fp, #-0x20]
    // 0x1502ce8: StoreField: r1->field_f = r0
    //     0x1502ce8: stur            w0, [x1, #0xf]
    // 0x1502cec: r2 = 6
    //     0x1502cec: movz            x2, #0x6
    // 0x1502cf0: StoreField: r1->field_b = r2
    //     0x1502cf0: stur            w2, [x1, #0xb]
    // 0x1502cf4: r0 = Row()
    //     0x1502cf4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1502cf8: mov             x1, x0
    // 0x1502cfc: r0 = Instance_Axis
    //     0x1502cfc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1502d00: stur            x1, [fp, #-0x10]
    // 0x1502d04: StoreField: r1->field_f = r0
    //     0x1502d04: stur            w0, [x1, #0xf]
    // 0x1502d08: r2 = Instance_MainAxisAlignment
    //     0x1502d08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1502d0c: ldr             x2, [x2, #0xa08]
    // 0x1502d10: StoreField: r1->field_13 = r2
    //     0x1502d10: stur            w2, [x1, #0x13]
    // 0x1502d14: r3 = Instance_MainAxisSize
    //     0x1502d14: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1502d18: ldr             x3, [x3, #0xa10]
    // 0x1502d1c: ArrayStore: r1[0] = r3  ; List_4
    //     0x1502d1c: stur            w3, [x1, #0x17]
    // 0x1502d20: r4 = Instance_CrossAxisAlignment
    //     0x1502d20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1502d24: ldr             x4, [x4, #0xa18]
    // 0x1502d28: StoreField: r1->field_1b = r4
    //     0x1502d28: stur            w4, [x1, #0x1b]
    // 0x1502d2c: r5 = Instance_VerticalDirection
    //     0x1502d2c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1502d30: ldr             x5, [x5, #0xa20]
    // 0x1502d34: StoreField: r1->field_23 = r5
    //     0x1502d34: stur            w5, [x1, #0x23]
    // 0x1502d38: r6 = Instance_Clip
    //     0x1502d38: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1502d3c: ldr             x6, [x6, #0x38]
    // 0x1502d40: StoreField: r1->field_2b = r6
    //     0x1502d40: stur            w6, [x1, #0x2b]
    // 0x1502d44: StoreField: r1->field_2f = rZR
    //     0x1502d44: stur            xzr, [x1, #0x2f]
    // 0x1502d48: ldur            x7, [fp, #-0x20]
    // 0x1502d4c: StoreField: r1->field_b = r7
    //     0x1502d4c: stur            w7, [x1, #0xb]
    // 0x1502d50: r0 = Padding()
    //     0x1502d50: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1502d54: mov             x1, x0
    // 0x1502d58: r0 = Instance_EdgeInsets
    //     0x1502d58: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f10] Obj!EdgeInsets@d57b91
    //     0x1502d5c: ldr             x0, [x0, #0xf10]
    // 0x1502d60: stur            x1, [fp, #-0x20]
    // 0x1502d64: StoreField: r1->field_f = r0
    //     0x1502d64: stur            w0, [x1, #0xf]
    // 0x1502d68: ldur            x0, [fp, #-0x10]
    // 0x1502d6c: StoreField: r1->field_b = r0
    //     0x1502d6c: stur            w0, [x1, #0xb]
    // 0x1502d70: r0 = ColoredBox()
    //     0x1502d70: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0x1502d74: mov             x2, x0
    // 0x1502d78: r0 = Instance_Color
    //     0x1502d78: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1502d7c: stur            x2, [fp, #-0x10]
    // 0x1502d80: StoreField: r2->field_f = r0
    //     0x1502d80: stur            w0, [x2, #0xf]
    // 0x1502d84: ldur            x1, [fp, #-0x20]
    // 0x1502d88: StoreField: r2->field_b = r1
    //     0x1502d88: stur            w1, [x2, #0xb]
    // 0x1502d8c: mov             x1, x0
    // 0x1502d90: d0 = 0.100000
    //     0x1502d90: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1502d94: r0 = withOpacity()
    //     0x1502d94: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1502d98: mov             x2, x0
    // 0x1502d9c: r1 = Null
    //     0x1502d9c: mov             x1, NULL
    // 0x1502da0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1502da0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1502da4: r0 = Border.all()
    //     0x1502da4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x1502da8: stur            x0, [fp, #-0x20]
    // 0x1502dac: r0 = BoxDecoration()
    //     0x1502dac: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1502db0: mov             x2, x0
    // 0x1502db4: ldur            x0, [fp, #-0x20]
    // 0x1502db8: stur            x2, [fp, #-0x28]
    // 0x1502dbc: StoreField: r2->field_f = r0
    //     0x1502dbc: stur            w0, [x2, #0xf]
    // 0x1502dc0: r0 = Instance_BoxShape
    //     0x1502dc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1502dc4: ldr             x0, [x0, #0x80]
    // 0x1502dc8: StoreField: r2->field_23 = r0
    //     0x1502dc8: stur            w0, [x2, #0x23]
    // 0x1502dcc: ldur            x3, [fp, #-8]
    // 0x1502dd0: LoadField: r1 = r3->field_f
    //     0x1502dd0: ldur            w1, [x3, #0xf]
    // 0x1502dd4: DecompressPointer r1
    //     0x1502dd4: add             x1, x1, HEAP, lsl #32
    // 0x1502dd8: r0 = controller()
    //     0x1502dd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1502ddc: LoadField: r1 = r0->field_53
    //     0x1502ddc: ldur            w1, [x0, #0x53]
    // 0x1502de0: DecompressPointer r1
    //     0x1502de0: add             x1, x1, HEAP, lsl #32
    // 0x1502de4: r0 = value()
    //     0x1502de4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502de8: LoadField: r2 = r0->field_1b
    //     0x1502de8: ldur            w2, [x0, #0x1b]
    // 0x1502dec: DecompressPointer r2
    //     0x1502dec: add             x2, x2, HEAP, lsl #32
    // 0x1502df0: cmp             w2, NULL
    // 0x1502df4: b.ne            #0x1502e00
    // 0x1502df8: r0 = Null
    //     0x1502df8: mov             x0, NULL
    // 0x1502dfc: b               #0x1502e30
    // 0x1502e00: LoadField: r0 = r2->field_b
    //     0x1502e00: ldur            w0, [x2, #0xb]
    // 0x1502e04: r1 = LoadInt32Instr(r0)
    //     0x1502e04: sbfx            x1, x0, #1, #0x1f
    // 0x1502e08: mov             x0, x1
    // 0x1502e0c: r1 = 0
    //     0x1502e0c: movz            x1, #0
    // 0x1502e10: cmp             x1, x0
    // 0x1502e14: b.hs            #0x1503f80
    // 0x1502e18: LoadField: r0 = r2->field_f
    //     0x1502e18: ldur            w0, [x2, #0xf]
    // 0x1502e1c: DecompressPointer r0
    //     0x1502e1c: add             x0, x0, HEAP, lsl #32
    // 0x1502e20: LoadField: r1 = r0->field_f
    //     0x1502e20: ldur            w1, [x0, #0xf]
    // 0x1502e24: DecompressPointer r1
    //     0x1502e24: add             x1, x1, HEAP, lsl #32
    // 0x1502e28: LoadField: r0 = r1->field_13
    //     0x1502e28: ldur            w0, [x1, #0x13]
    // 0x1502e2c: DecompressPointer r0
    //     0x1502e2c: add             x0, x0, HEAP, lsl #32
    // 0x1502e30: cmp             w0, NULL
    // 0x1502e34: b.ne            #0x1502e3c
    // 0x1502e38: r0 = ""
    //     0x1502e38: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1502e3c: ldur            x2, [fp, #-8]
    // 0x1502e40: stur            x0, [fp, #-0x20]
    // 0x1502e44: r0 = ImageHeaders.forImages()
    //     0x1502e44: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x1502e48: r1 = Function '<anonymous closure>':.
    //     0x1502e48: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3af78] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x1502e4c: ldr             x1, [x1, #0xf78]
    // 0x1502e50: r2 = Null
    //     0x1502e50: mov             x2, NULL
    // 0x1502e54: stur            x0, [fp, #-0x30]
    // 0x1502e58: r0 = AllocateClosure()
    //     0x1502e58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1502e5c: stur            x0, [fp, #-0x38]
    // 0x1502e60: r0 = CachedNetworkImage()
    //     0x1502e60: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x1502e64: stur            x0, [fp, #-0x40]
    // 0x1502e68: r16 = Instance_BoxFit
    //     0x1502e68: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x1502e6c: ldr             x16, [x16, #0x118]
    // 0x1502e70: ldur            lr, [fp, #-0x30]
    // 0x1502e74: stp             lr, x16, [SP, #0x18]
    // 0x1502e78: r16 = 84.000000
    //     0x1502e78: add             x16, PP, #0x33, lsl #12  ; [pp+0x33f90] 84
    //     0x1502e7c: ldr             x16, [x16, #0xf90]
    // 0x1502e80: r30 = 56.000000
    //     0x1502e80: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x1502e84: ldr             lr, [lr, #0xb78]
    // 0x1502e88: stp             lr, x16, [SP, #8]
    // 0x1502e8c: ldur            x16, [fp, #-0x38]
    // 0x1502e90: str             x16, [SP]
    // 0x1502e94: mov             x1, x0
    // 0x1502e98: ldur            x2, [fp, #-0x20]
    // 0x1502e9c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x2, height, 0x4, httpHeaders, 0x3, width, 0x5, null]
    //     0x1502e9c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d78] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x2, "height", 0x4, "httpHeaders", 0x3, "width", 0x5, Null]
    //     0x1502ea0: ldr             x4, [x4, #0xd78]
    // 0x1502ea4: r0 = CachedNetworkImage()
    //     0x1502ea4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x1502ea8: ldur            x2, [fp, #-8]
    // 0x1502eac: LoadField: r1 = r2->field_f
    //     0x1502eac: ldur            w1, [x2, #0xf]
    // 0x1502eb0: DecompressPointer r1
    //     0x1502eb0: add             x1, x1, HEAP, lsl #32
    // 0x1502eb4: r0 = controller()
    //     0x1502eb4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1502eb8: LoadField: r1 = r0->field_53
    //     0x1502eb8: ldur            w1, [x0, #0x53]
    // 0x1502ebc: DecompressPointer r1
    //     0x1502ebc: add             x1, x1, HEAP, lsl #32
    // 0x1502ec0: r0 = value()
    //     0x1502ec0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502ec4: LoadField: r2 = r0->field_1b
    //     0x1502ec4: ldur            w2, [x0, #0x1b]
    // 0x1502ec8: DecompressPointer r2
    //     0x1502ec8: add             x2, x2, HEAP, lsl #32
    // 0x1502ecc: cmp             w2, NULL
    // 0x1502ed0: b.ne            #0x1502edc
    // 0x1502ed4: r0 = Null
    //     0x1502ed4: mov             x0, NULL
    // 0x1502ed8: b               #0x1502f0c
    // 0x1502edc: LoadField: r0 = r2->field_b
    //     0x1502edc: ldur            w0, [x2, #0xb]
    // 0x1502ee0: r1 = LoadInt32Instr(r0)
    //     0x1502ee0: sbfx            x1, x0, #1, #0x1f
    // 0x1502ee4: mov             x0, x1
    // 0x1502ee8: r1 = 0
    //     0x1502ee8: movz            x1, #0
    // 0x1502eec: cmp             x1, x0
    // 0x1502ef0: b.hs            #0x1503f84
    // 0x1502ef4: LoadField: r0 = r2->field_f
    //     0x1502ef4: ldur            w0, [x2, #0xf]
    // 0x1502ef8: DecompressPointer r0
    //     0x1502ef8: add             x0, x0, HEAP, lsl #32
    // 0x1502efc: LoadField: r1 = r0->field_f
    //     0x1502efc: ldur            w1, [x0, #0xf]
    // 0x1502f00: DecompressPointer r1
    //     0x1502f00: add             x1, x1, HEAP, lsl #32
    // 0x1502f04: LoadField: r0 = r1->field_f
    //     0x1502f04: ldur            w0, [x1, #0xf]
    // 0x1502f08: DecompressPointer r0
    //     0x1502f08: add             x0, x0, HEAP, lsl #32
    // 0x1502f0c: cmp             w0, NULL
    // 0x1502f10: b.ne            #0x1502f18
    // 0x1502f14: r0 = ""
    //     0x1502f14: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1502f18: ldur            x2, [fp, #-8]
    // 0x1502f1c: stur            x0, [fp, #-0x20]
    // 0x1502f20: LoadField: r1 = r2->field_13
    //     0x1502f20: ldur            w1, [x2, #0x13]
    // 0x1502f24: DecompressPointer r1
    //     0x1502f24: add             x1, x1, HEAP, lsl #32
    // 0x1502f28: r0 = of()
    //     0x1502f28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1502f2c: LoadField: r1 = r0->field_87
    //     0x1502f2c: ldur            w1, [x0, #0x87]
    // 0x1502f30: DecompressPointer r1
    //     0x1502f30: add             x1, x1, HEAP, lsl #32
    // 0x1502f34: LoadField: r0 = r1->field_7
    //     0x1502f34: ldur            w0, [x1, #7]
    // 0x1502f38: DecompressPointer r0
    //     0x1502f38: add             x0, x0, HEAP, lsl #32
    // 0x1502f3c: r16 = Instance_Color
    //     0x1502f3c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1502f40: r30 = 12.000000
    //     0x1502f40: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1502f44: ldr             lr, [lr, #0x9e8]
    // 0x1502f48: stp             lr, x16, [SP]
    // 0x1502f4c: mov             x1, x0
    // 0x1502f50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1502f50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1502f54: ldr             x4, [x4, #0x9b8]
    // 0x1502f58: r0 = copyWith()
    //     0x1502f58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1502f5c: stur            x0, [fp, #-0x30]
    // 0x1502f60: r0 = Text()
    //     0x1502f60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1502f64: mov             x2, x0
    // 0x1502f68: ldur            x0, [fp, #-0x20]
    // 0x1502f6c: stur            x2, [fp, #-0x38]
    // 0x1502f70: StoreField: r2->field_b = r0
    //     0x1502f70: stur            w0, [x2, #0xb]
    // 0x1502f74: ldur            x0, [fp, #-0x30]
    // 0x1502f78: StoreField: r2->field_13 = r0
    //     0x1502f78: stur            w0, [x2, #0x13]
    // 0x1502f7c: r0 = 2
    //     0x1502f7c: movz            x0, #0x2
    // 0x1502f80: StoreField: r2->field_37 = r0
    //     0x1502f80: stur            w0, [x2, #0x37]
    // 0x1502f84: ldur            x0, [fp, #-8]
    // 0x1502f88: LoadField: r1 = r0->field_f
    //     0x1502f88: ldur            w1, [x0, #0xf]
    // 0x1502f8c: DecompressPointer r1
    //     0x1502f8c: add             x1, x1, HEAP, lsl #32
    // 0x1502f90: r0 = controller()
    //     0x1502f90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1502f94: LoadField: r1 = r0->field_53
    //     0x1502f94: ldur            w1, [x0, #0x53]
    // 0x1502f98: DecompressPointer r1
    //     0x1502f98: add             x1, x1, HEAP, lsl #32
    // 0x1502f9c: r0 = value()
    //     0x1502f9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1502fa0: LoadField: r2 = r0->field_1b
    //     0x1502fa0: ldur            w2, [x0, #0x1b]
    // 0x1502fa4: DecompressPointer r2
    //     0x1502fa4: add             x2, x2, HEAP, lsl #32
    // 0x1502fa8: cmp             w2, NULL
    // 0x1502fac: b.ne            #0x1502fb8
    // 0x1502fb0: r0 = Null
    //     0x1502fb0: mov             x0, NULL
    // 0x1502fb4: b               #0x1502fe8
    // 0x1502fb8: LoadField: r0 = r2->field_b
    //     0x1502fb8: ldur            w0, [x2, #0xb]
    // 0x1502fbc: r1 = LoadInt32Instr(r0)
    //     0x1502fbc: sbfx            x1, x0, #1, #0x1f
    // 0x1502fc0: mov             x0, x1
    // 0x1502fc4: r1 = 0
    //     0x1502fc4: movz            x1, #0
    // 0x1502fc8: cmp             x1, x0
    // 0x1502fcc: b.hs            #0x1503f88
    // 0x1502fd0: LoadField: r0 = r2->field_f
    //     0x1502fd0: ldur            w0, [x2, #0xf]
    // 0x1502fd4: DecompressPointer r0
    //     0x1502fd4: add             x0, x0, HEAP, lsl #32
    // 0x1502fd8: LoadField: r1 = r0->field_f
    //     0x1502fd8: ldur            w1, [x0, #0xf]
    // 0x1502fdc: DecompressPointer r1
    //     0x1502fdc: add             x1, x1, HEAP, lsl #32
    // 0x1502fe0: LoadField: r0 = r1->field_13
    //     0x1502fe0: ldur            w0, [x1, #0x13]
    // 0x1502fe4: DecompressPointer r0
    //     0x1502fe4: add             x0, x0, HEAP, lsl #32
    // 0x1502fe8: r1 = LoadClassIdInstr(r0)
    //     0x1502fe8: ldur            x1, [x0, #-1]
    //     0x1502fec: ubfx            x1, x1, #0xc, #0x14
    // 0x1502ff0: r16 = "size"
    //     0x1502ff0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x1502ff4: ldr             x16, [x16, #0x9c0]
    // 0x1502ff8: stp             x16, x0, [SP]
    // 0x1502ffc: mov             x0, x1
    // 0x1503000: mov             lr, x0
    // 0x1503004: ldr             lr, [x21, lr, lsl #3]
    // 0x1503008: blr             lr
    // 0x150300c: tbnz            w0, #4, #0x15031e8
    // 0x1503010: ldur            x0, [fp, #-8]
    // 0x1503014: r1 = Null
    //     0x1503014: mov             x1, NULL
    // 0x1503018: r2 = 8
    //     0x1503018: movz            x2, #0x8
    // 0x150301c: r0 = AllocateArray()
    //     0x150301c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1503020: stur            x0, [fp, #-0x20]
    // 0x1503024: r16 = "Size: "
    //     0x1503024: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x1503028: ldr             x16, [x16, #0xf00]
    // 0x150302c: StoreField: r0->field_f = r16
    //     0x150302c: stur            w16, [x0, #0xf]
    // 0x1503030: ldur            x2, [fp, #-8]
    // 0x1503034: LoadField: r1 = r2->field_f
    //     0x1503034: ldur            w1, [x2, #0xf]
    // 0x1503038: DecompressPointer r1
    //     0x1503038: add             x1, x1, HEAP, lsl #32
    // 0x150303c: r0 = controller()
    //     0x150303c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1503040: LoadField: r1 = r0->field_53
    //     0x1503040: ldur            w1, [x0, #0x53]
    // 0x1503044: DecompressPointer r1
    //     0x1503044: add             x1, x1, HEAP, lsl #32
    // 0x1503048: r0 = value()
    //     0x1503048: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x150304c: LoadField: r2 = r0->field_1b
    //     0x150304c: ldur            w2, [x0, #0x1b]
    // 0x1503050: DecompressPointer r2
    //     0x1503050: add             x2, x2, HEAP, lsl #32
    // 0x1503054: cmp             w2, NULL
    // 0x1503058: b.ne            #0x1503064
    // 0x150305c: r0 = Null
    //     0x150305c: mov             x0, NULL
    // 0x1503060: b               #0x1503094
    // 0x1503064: LoadField: r0 = r2->field_b
    //     0x1503064: ldur            w0, [x2, #0xb]
    // 0x1503068: r1 = LoadInt32Instr(r0)
    //     0x1503068: sbfx            x1, x0, #1, #0x1f
    // 0x150306c: mov             x0, x1
    // 0x1503070: r1 = 0
    //     0x1503070: movz            x1, #0
    // 0x1503074: cmp             x1, x0
    // 0x1503078: b.hs            #0x1503f8c
    // 0x150307c: LoadField: r0 = r2->field_f
    //     0x150307c: ldur            w0, [x2, #0xf]
    // 0x1503080: DecompressPointer r0
    //     0x1503080: add             x0, x0, HEAP, lsl #32
    // 0x1503084: LoadField: r1 = r0->field_f
    //     0x1503084: ldur            w1, [x0, #0xf]
    // 0x1503088: DecompressPointer r1
    //     0x1503088: add             x1, x1, HEAP, lsl #32
    // 0x150308c: LoadField: r0 = r1->field_33
    //     0x150308c: ldur            w0, [x1, #0x33]
    // 0x1503090: DecompressPointer r0
    //     0x1503090: add             x0, x0, HEAP, lsl #32
    // 0x1503094: cmp             w0, NULL
    // 0x1503098: b.ne            #0x15030a0
    // 0x150309c: r0 = ""
    //     0x150309c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15030a0: ldur            x3, [fp, #-8]
    // 0x15030a4: ldur            x2, [fp, #-0x20]
    // 0x15030a8: mov             x1, x2
    // 0x15030ac: ArrayStore: r1[1] = r0  ; List_4
    //     0x15030ac: add             x25, x1, #0x13
    //     0x15030b0: str             w0, [x25]
    //     0x15030b4: tbz             w0, #0, #0x15030d0
    //     0x15030b8: ldurb           w16, [x1, #-1]
    //     0x15030bc: ldurb           w17, [x0, #-1]
    //     0x15030c0: and             x16, x17, x16, lsr #2
    //     0x15030c4: tst             x16, HEAP, lsr #32
    //     0x15030c8: b.eq            #0x15030d0
    //     0x15030cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15030d0: r16 = " / Qty: "
    //     0x15030d0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x15030d4: ldr             x16, [x16, #0x760]
    // 0x15030d8: ArrayStore: r2[0] = r16  ; List_4
    //     0x15030d8: stur            w16, [x2, #0x17]
    // 0x15030dc: LoadField: r1 = r3->field_f
    //     0x15030dc: ldur            w1, [x3, #0xf]
    // 0x15030e0: DecompressPointer r1
    //     0x15030e0: add             x1, x1, HEAP, lsl #32
    // 0x15030e4: r0 = controller()
    //     0x15030e4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15030e8: LoadField: r1 = r0->field_53
    //     0x15030e8: ldur            w1, [x0, #0x53]
    // 0x15030ec: DecompressPointer r1
    //     0x15030ec: add             x1, x1, HEAP, lsl #32
    // 0x15030f0: r0 = value()
    //     0x15030f0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15030f4: LoadField: r2 = r0->field_1b
    //     0x15030f4: ldur            w2, [x0, #0x1b]
    // 0x15030f8: DecompressPointer r2
    //     0x15030f8: add             x2, x2, HEAP, lsl #32
    // 0x15030fc: cmp             w2, NULL
    // 0x1503100: b.ne            #0x150310c
    // 0x1503104: r0 = Null
    //     0x1503104: mov             x0, NULL
    // 0x1503108: b               #0x150313c
    // 0x150310c: LoadField: r0 = r2->field_b
    //     0x150310c: ldur            w0, [x2, #0xb]
    // 0x1503110: r1 = LoadInt32Instr(r0)
    //     0x1503110: sbfx            x1, x0, #1, #0x1f
    // 0x1503114: mov             x0, x1
    // 0x1503118: r1 = 0
    //     0x1503118: movz            x1, #0
    // 0x150311c: cmp             x1, x0
    // 0x1503120: b.hs            #0x1503f90
    // 0x1503124: LoadField: r0 = r2->field_f
    //     0x1503124: ldur            w0, [x2, #0xf]
    // 0x1503128: DecompressPointer r0
    //     0x1503128: add             x0, x0, HEAP, lsl #32
    // 0x150312c: LoadField: r1 = r0->field_f
    //     0x150312c: ldur            w1, [x0, #0xf]
    // 0x1503130: DecompressPointer r1
    //     0x1503130: add             x1, x1, HEAP, lsl #32
    // 0x1503134: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1503134: ldur            w0, [x1, #0x17]
    // 0x1503138: DecompressPointer r0
    //     0x1503138: add             x0, x0, HEAP, lsl #32
    // 0x150313c: cmp             w0, NULL
    // 0x1503140: b.ne            #0x1503148
    // 0x1503144: r0 = ""
    //     0x1503144: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1503148: ldur            x2, [fp, #-8]
    // 0x150314c: ldur            x1, [fp, #-0x20]
    // 0x1503150: ArrayStore: r1[3] = r0  ; List_4
    //     0x1503150: add             x25, x1, #0x1b
    //     0x1503154: str             w0, [x25]
    //     0x1503158: tbz             w0, #0, #0x1503174
    //     0x150315c: ldurb           w16, [x1, #-1]
    //     0x1503160: ldurb           w17, [x0, #-1]
    //     0x1503164: and             x16, x17, x16, lsr #2
    //     0x1503168: tst             x16, HEAP, lsr #32
    //     0x150316c: b.eq            #0x1503174
    //     0x1503170: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1503174: ldur            x16, [fp, #-0x20]
    // 0x1503178: str             x16, [SP]
    // 0x150317c: r0 = _interpolate()
    //     0x150317c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1503180: ldur            x2, [fp, #-8]
    // 0x1503184: stur            x0, [fp, #-0x20]
    // 0x1503188: LoadField: r1 = r2->field_13
    //     0x1503188: ldur            w1, [x2, #0x13]
    // 0x150318c: DecompressPointer r1
    //     0x150318c: add             x1, x1, HEAP, lsl #32
    // 0x1503190: r0 = of()
    //     0x1503190: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1503194: LoadField: r1 = r0->field_87
    //     0x1503194: ldur            w1, [x0, #0x87]
    // 0x1503198: DecompressPointer r1
    //     0x1503198: add             x1, x1, HEAP, lsl #32
    // 0x150319c: LoadField: r0 = r1->field_2b
    //     0x150319c: ldur            w0, [x1, #0x2b]
    // 0x15031a0: DecompressPointer r0
    //     0x15031a0: add             x0, x0, HEAP, lsl #32
    // 0x15031a4: r16 = 12.000000
    //     0x15031a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x15031a8: ldr             x16, [x16, #0x9e8]
    // 0x15031ac: r30 = Instance_Color
    //     0x15031ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15031b0: stp             lr, x16, [SP]
    // 0x15031b4: mov             x1, x0
    // 0x15031b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15031b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15031bc: ldr             x4, [x4, #0xaa0]
    // 0x15031c0: r0 = copyWith()
    //     0x15031c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15031c4: stur            x0, [fp, #-0x30]
    // 0x15031c8: r0 = Text()
    //     0x15031c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15031cc: mov             x1, x0
    // 0x15031d0: ldur            x0, [fp, #-0x20]
    // 0x15031d4: StoreField: r1->field_b = r0
    //     0x15031d4: stur            w0, [x1, #0xb]
    // 0x15031d8: ldur            x0, [fp, #-0x30]
    // 0x15031dc: StoreField: r1->field_13 = r0
    //     0x15031dc: stur            w0, [x1, #0x13]
    // 0x15031e0: mov             x0, x1
    // 0x15031e4: b               #0x15033bc
    // 0x15031e8: ldur            x0, [fp, #-8]
    // 0x15031ec: r1 = Null
    //     0x15031ec: mov             x1, NULL
    // 0x15031f0: r2 = 8
    //     0x15031f0: movz            x2, #0x8
    // 0x15031f4: r0 = AllocateArray()
    //     0x15031f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15031f8: stur            x0, [fp, #-0x20]
    // 0x15031fc: r16 = "Variant: "
    //     0x15031fc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x1503200: ldr             x16, [x16, #0xf08]
    // 0x1503204: StoreField: r0->field_f = r16
    //     0x1503204: stur            w16, [x0, #0xf]
    // 0x1503208: ldur            x2, [fp, #-8]
    // 0x150320c: LoadField: r1 = r2->field_f
    //     0x150320c: ldur            w1, [x2, #0xf]
    // 0x1503210: DecompressPointer r1
    //     0x1503210: add             x1, x1, HEAP, lsl #32
    // 0x1503214: r0 = controller()
    //     0x1503214: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1503218: LoadField: r1 = r0->field_53
    //     0x1503218: ldur            w1, [x0, #0x53]
    // 0x150321c: DecompressPointer r1
    //     0x150321c: add             x1, x1, HEAP, lsl #32
    // 0x1503220: r0 = value()
    //     0x1503220: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1503224: LoadField: r2 = r0->field_1b
    //     0x1503224: ldur            w2, [x0, #0x1b]
    // 0x1503228: DecompressPointer r2
    //     0x1503228: add             x2, x2, HEAP, lsl #32
    // 0x150322c: cmp             w2, NULL
    // 0x1503230: b.ne            #0x150323c
    // 0x1503234: r0 = Null
    //     0x1503234: mov             x0, NULL
    // 0x1503238: b               #0x150326c
    // 0x150323c: LoadField: r0 = r2->field_b
    //     0x150323c: ldur            w0, [x2, #0xb]
    // 0x1503240: r1 = LoadInt32Instr(r0)
    //     0x1503240: sbfx            x1, x0, #1, #0x1f
    // 0x1503244: mov             x0, x1
    // 0x1503248: r1 = 0
    //     0x1503248: movz            x1, #0
    // 0x150324c: cmp             x1, x0
    // 0x1503250: b.hs            #0x1503f94
    // 0x1503254: LoadField: r0 = r2->field_f
    //     0x1503254: ldur            w0, [x2, #0xf]
    // 0x1503258: DecompressPointer r0
    //     0x1503258: add             x0, x0, HEAP, lsl #32
    // 0x150325c: LoadField: r1 = r0->field_f
    //     0x150325c: ldur            w1, [x0, #0xf]
    // 0x1503260: DecompressPointer r1
    //     0x1503260: add             x1, x1, HEAP, lsl #32
    // 0x1503264: LoadField: r0 = r1->field_33
    //     0x1503264: ldur            w0, [x1, #0x33]
    // 0x1503268: DecompressPointer r0
    //     0x1503268: add             x0, x0, HEAP, lsl #32
    // 0x150326c: cmp             w0, NULL
    // 0x1503270: b.ne            #0x1503278
    // 0x1503274: r0 = ""
    //     0x1503274: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1503278: ldur            x3, [fp, #-8]
    // 0x150327c: ldur            x2, [fp, #-0x20]
    // 0x1503280: mov             x1, x2
    // 0x1503284: ArrayStore: r1[1] = r0  ; List_4
    //     0x1503284: add             x25, x1, #0x13
    //     0x1503288: str             w0, [x25]
    //     0x150328c: tbz             w0, #0, #0x15032a8
    //     0x1503290: ldurb           w16, [x1, #-1]
    //     0x1503294: ldurb           w17, [x0, #-1]
    //     0x1503298: and             x16, x17, x16, lsr #2
    //     0x150329c: tst             x16, HEAP, lsr #32
    //     0x15032a0: b.eq            #0x15032a8
    //     0x15032a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x15032a8: r16 = " / Qty: "
    //     0x15032a8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x15032ac: ldr             x16, [x16, #0x760]
    // 0x15032b0: ArrayStore: r2[0] = r16  ; List_4
    //     0x15032b0: stur            w16, [x2, #0x17]
    // 0x15032b4: LoadField: r1 = r3->field_f
    //     0x15032b4: ldur            w1, [x3, #0xf]
    // 0x15032b8: DecompressPointer r1
    //     0x15032b8: add             x1, x1, HEAP, lsl #32
    // 0x15032bc: r0 = controller()
    //     0x15032bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15032c0: LoadField: r1 = r0->field_53
    //     0x15032c0: ldur            w1, [x0, #0x53]
    // 0x15032c4: DecompressPointer r1
    //     0x15032c4: add             x1, x1, HEAP, lsl #32
    // 0x15032c8: r0 = value()
    //     0x15032c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15032cc: LoadField: r2 = r0->field_1b
    //     0x15032cc: ldur            w2, [x0, #0x1b]
    // 0x15032d0: DecompressPointer r2
    //     0x15032d0: add             x2, x2, HEAP, lsl #32
    // 0x15032d4: cmp             w2, NULL
    // 0x15032d8: b.ne            #0x15032e4
    // 0x15032dc: r0 = Null
    //     0x15032dc: mov             x0, NULL
    // 0x15032e0: b               #0x1503314
    // 0x15032e4: LoadField: r0 = r2->field_b
    //     0x15032e4: ldur            w0, [x2, #0xb]
    // 0x15032e8: r1 = LoadInt32Instr(r0)
    //     0x15032e8: sbfx            x1, x0, #1, #0x1f
    // 0x15032ec: mov             x0, x1
    // 0x15032f0: r1 = 0
    //     0x15032f0: movz            x1, #0
    // 0x15032f4: cmp             x1, x0
    // 0x15032f8: b.hs            #0x1503f98
    // 0x15032fc: LoadField: r0 = r2->field_f
    //     0x15032fc: ldur            w0, [x2, #0xf]
    // 0x1503300: DecompressPointer r0
    //     0x1503300: add             x0, x0, HEAP, lsl #32
    // 0x1503304: LoadField: r1 = r0->field_f
    //     0x1503304: ldur            w1, [x0, #0xf]
    // 0x1503308: DecompressPointer r1
    //     0x1503308: add             x1, x1, HEAP, lsl #32
    // 0x150330c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x150330c: ldur            w0, [x1, #0x17]
    // 0x1503310: DecompressPointer r0
    //     0x1503310: add             x0, x0, HEAP, lsl #32
    // 0x1503314: cmp             w0, NULL
    // 0x1503318: b.ne            #0x1503320
    // 0x150331c: r0 = ""
    //     0x150331c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1503320: ldur            x2, [fp, #-8]
    // 0x1503324: ldur            x1, [fp, #-0x20]
    // 0x1503328: ArrayStore: r1[3] = r0  ; List_4
    //     0x1503328: add             x25, x1, #0x1b
    //     0x150332c: str             w0, [x25]
    //     0x1503330: tbz             w0, #0, #0x150334c
    //     0x1503334: ldurb           w16, [x1, #-1]
    //     0x1503338: ldurb           w17, [x0, #-1]
    //     0x150333c: and             x16, x17, x16, lsr #2
    //     0x1503340: tst             x16, HEAP, lsr #32
    //     0x1503344: b.eq            #0x150334c
    //     0x1503348: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x150334c: ldur            x16, [fp, #-0x20]
    // 0x1503350: str             x16, [SP]
    // 0x1503354: r0 = _interpolate()
    //     0x1503354: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1503358: ldur            x2, [fp, #-8]
    // 0x150335c: stur            x0, [fp, #-0x20]
    // 0x1503360: LoadField: r1 = r2->field_13
    //     0x1503360: ldur            w1, [x2, #0x13]
    // 0x1503364: DecompressPointer r1
    //     0x1503364: add             x1, x1, HEAP, lsl #32
    // 0x1503368: r0 = of()
    //     0x1503368: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x150336c: LoadField: r1 = r0->field_87
    //     0x150336c: ldur            w1, [x0, #0x87]
    // 0x1503370: DecompressPointer r1
    //     0x1503370: add             x1, x1, HEAP, lsl #32
    // 0x1503374: LoadField: r0 = r1->field_2b
    //     0x1503374: ldur            w0, [x1, #0x2b]
    // 0x1503378: DecompressPointer r0
    //     0x1503378: add             x0, x0, HEAP, lsl #32
    // 0x150337c: r16 = 12.000000
    //     0x150337c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1503380: ldr             x16, [x16, #0x9e8]
    // 0x1503384: r30 = Instance_Color
    //     0x1503384: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1503388: stp             lr, x16, [SP]
    // 0x150338c: mov             x1, x0
    // 0x1503390: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1503390: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1503394: ldr             x4, [x4, #0xaa0]
    // 0x1503398: r0 = copyWith()
    //     0x1503398: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x150339c: stur            x0, [fp, #-0x30]
    // 0x15033a0: r0 = Text()
    //     0x15033a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15033a4: mov             x1, x0
    // 0x15033a8: ldur            x0, [fp, #-0x20]
    // 0x15033ac: StoreField: r1->field_b = r0
    //     0x15033ac: stur            w0, [x1, #0xb]
    // 0x15033b0: ldur            x0, [fp, #-0x30]
    // 0x15033b4: StoreField: r1->field_13 = r0
    //     0x15033b4: stur            w0, [x1, #0x13]
    // 0x15033b8: mov             x0, x1
    // 0x15033bc: ldur            x2, [fp, #-8]
    // 0x15033c0: stur            x0, [fp, #-0x20]
    // 0x15033c4: r0 = Padding()
    //     0x15033c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15033c8: mov             x2, x0
    // 0x15033cc: r0 = Instance_EdgeInsets
    //     0x15033cc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x15033d0: ldr             x0, [x0, #0x770]
    // 0x15033d4: stur            x2, [fp, #-0x30]
    // 0x15033d8: StoreField: r2->field_f = r0
    //     0x15033d8: stur            w0, [x2, #0xf]
    // 0x15033dc: ldur            x0, [fp, #-0x20]
    // 0x15033e0: StoreField: r2->field_b = r0
    //     0x15033e0: stur            w0, [x2, #0xb]
    // 0x15033e4: ldur            x0, [fp, #-8]
    // 0x15033e8: LoadField: r1 = r0->field_f
    //     0x15033e8: ldur            w1, [x0, #0xf]
    // 0x15033ec: DecompressPointer r1
    //     0x15033ec: add             x1, x1, HEAP, lsl #32
    // 0x15033f0: r0 = controller()
    //     0x15033f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15033f4: LoadField: r1 = r0->field_53
    //     0x15033f4: ldur            w1, [x0, #0x53]
    // 0x15033f8: DecompressPointer r1
    //     0x15033f8: add             x1, x1, HEAP, lsl #32
    // 0x15033fc: r0 = value()
    //     0x15033fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1503400: LoadField: r2 = r0->field_1b
    //     0x1503400: ldur            w2, [x0, #0x1b]
    // 0x1503404: DecompressPointer r2
    //     0x1503404: add             x2, x2, HEAP, lsl #32
    // 0x1503408: cmp             w2, NULL
    // 0x150340c: b.ne            #0x1503418
    // 0x1503410: r0 = Null
    //     0x1503410: mov             x0, NULL
    // 0x1503414: b               #0x1503448
    // 0x1503418: LoadField: r0 = r2->field_b
    //     0x1503418: ldur            w0, [x2, #0xb]
    // 0x150341c: r1 = LoadInt32Instr(r0)
    //     0x150341c: sbfx            x1, x0, #1, #0x1f
    // 0x1503420: mov             x0, x1
    // 0x1503424: r1 = 0
    //     0x1503424: movz            x1, #0
    // 0x1503428: cmp             x1, x0
    // 0x150342c: b.hs            #0x1503f9c
    // 0x1503430: LoadField: r0 = r2->field_f
    //     0x1503430: ldur            w0, [x2, #0xf]
    // 0x1503434: DecompressPointer r0
    //     0x1503434: add             x0, x0, HEAP, lsl #32
    // 0x1503438: LoadField: r1 = r0->field_f
    //     0x1503438: ldur            w1, [x0, #0xf]
    // 0x150343c: DecompressPointer r1
    //     0x150343c: add             x1, x1, HEAP, lsl #32
    // 0x1503440: LoadField: r0 = r1->field_2f
    //     0x1503440: ldur            w0, [x1, #0x2f]
    // 0x1503444: DecompressPointer r0
    //     0x1503444: add             x0, x0, HEAP, lsl #32
    // 0x1503448: cmp             w0, NULL
    // 0x150344c: b.ne            #0x1503454
    // 0x1503450: r0 = ""
    //     0x1503450: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1503454: ldur            x2, [fp, #-8]
    // 0x1503458: stur            x0, [fp, #-0x20]
    // 0x150345c: LoadField: r1 = r2->field_13
    //     0x150345c: ldur            w1, [x2, #0x13]
    // 0x1503460: DecompressPointer r1
    //     0x1503460: add             x1, x1, HEAP, lsl #32
    // 0x1503464: r0 = of()
    //     0x1503464: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1503468: LoadField: r1 = r0->field_87
    //     0x1503468: ldur            w1, [x0, #0x87]
    // 0x150346c: DecompressPointer r1
    //     0x150346c: add             x1, x1, HEAP, lsl #32
    // 0x1503470: LoadField: r0 = r1->field_7
    //     0x1503470: ldur            w0, [x1, #7]
    // 0x1503474: DecompressPointer r0
    //     0x1503474: add             x0, x0, HEAP, lsl #32
    // 0x1503478: r16 = Instance_Color
    //     0x1503478: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x150347c: r30 = 12.000000
    //     0x150347c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1503480: ldr             lr, [lr, #0x9e8]
    // 0x1503484: stp             lr, x16, [SP]
    // 0x1503488: mov             x1, x0
    // 0x150348c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x150348c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1503490: ldr             x4, [x4, #0x9b8]
    // 0x1503494: r0 = copyWith()
    //     0x1503494: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1503498: stur            x0, [fp, #-0x48]
    // 0x150349c: r0 = Text()
    //     0x150349c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15034a0: mov             x2, x0
    // 0x15034a4: ldur            x0, [fp, #-0x20]
    // 0x15034a8: stur            x2, [fp, #-0x50]
    // 0x15034ac: StoreField: r2->field_b = r0
    //     0x15034ac: stur            w0, [x2, #0xb]
    // 0x15034b0: ldur            x0, [fp, #-0x48]
    // 0x15034b4: StoreField: r2->field_13 = r0
    //     0x15034b4: stur            w0, [x2, #0x13]
    // 0x15034b8: ldur            x0, [fp, #-8]
    // 0x15034bc: LoadField: r1 = r0->field_f
    //     0x15034bc: ldur            w1, [x0, #0xf]
    // 0x15034c0: DecompressPointer r1
    //     0x15034c0: add             x1, x1, HEAP, lsl #32
    // 0x15034c4: r0 = controller()
    //     0x15034c4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15034c8: mov             x1, x0
    // 0x15034cc: r0 = configData()
    //     0x15034cc: bl              #0x8a3140  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::configData
    // 0x15034d0: LoadField: r1 = r0->field_13
    //     0x15034d0: ldur            w1, [x0, #0x13]
    // 0x15034d4: DecompressPointer r1
    //     0x15034d4: add             x1, x1, HEAP, lsl #32
    // 0x15034d8: cmp             w1, NULL
    // 0x15034dc: b.ne            #0x15034e8
    // 0x15034e0: r0 = Null
    //     0x15034e0: mov             x0, NULL
    // 0x15034e4: b               #0x1503500
    // 0x15034e8: LoadField: r0 = r1->field_b
    //     0x15034e8: ldur            w0, [x1, #0xb]
    // 0x15034ec: cbnz            w0, #0x15034f8
    // 0x15034f0: r1 = false
    //     0x15034f0: add             x1, NULL, #0x30  ; false
    // 0x15034f4: b               #0x15034fc
    // 0x15034f8: r1 = true
    //     0x15034f8: add             x1, NULL, #0x20  ; true
    // 0x15034fc: mov             x0, x1
    // 0x1503500: cmp             w0, NULL
    // 0x1503504: b.eq            #0x1503514
    // 0x1503508: tbnz            w0, #4, #0x1503514
    // 0x150350c: r8 = true
    //     0x150350c: add             x8, NULL, #0x20  ; true
    // 0x1503510: b               #0x15035cc
    // 0x1503514: ldur            x2, [fp, #-8]
    // 0x1503518: LoadField: r1 = r2->field_f
    //     0x1503518: ldur            w1, [x2, #0xf]
    // 0x150351c: DecompressPointer r1
    //     0x150351c: add             x1, x1, HEAP, lsl #32
    // 0x1503520: r0 = controller()
    //     0x1503520: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1503524: LoadField: r1 = r0->field_6f
    //     0x1503524: ldur            w1, [x0, #0x6f]
    // 0x1503528: DecompressPointer r1
    //     0x1503528: add             x1, x1, HEAP, lsl #32
    // 0x150352c: r0 = value()
    //     0x150352c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1503530: LoadField: r1 = r0->field_b
    //     0x1503530: ldur            w1, [x0, #0xb]
    // 0x1503534: DecompressPointer r1
    //     0x1503534: add             x1, x1, HEAP, lsl #32
    // 0x1503538: cmp             w1, NULL
    // 0x150353c: b.ne            #0x1503548
    // 0x1503540: r0 = Null
    //     0x1503540: mov             x0, NULL
    // 0x1503544: b               #0x15035bc
    // 0x1503548: LoadField: r0 = r1->field_f
    //     0x1503548: ldur            w0, [x1, #0xf]
    // 0x150354c: DecompressPointer r0
    //     0x150354c: add             x0, x0, HEAP, lsl #32
    // 0x1503550: cmp             w0, NULL
    // 0x1503554: b.ne            #0x1503560
    // 0x1503558: r0 = Null
    //     0x1503558: mov             x0, NULL
    // 0x150355c: b               #0x15035bc
    // 0x1503560: LoadField: r2 = r0->field_b
    //     0x1503560: ldur            w2, [x0, #0xb]
    // 0x1503564: DecompressPointer r2
    //     0x1503564: add             x2, x2, HEAP, lsl #32
    // 0x1503568: LoadField: r0 = r2->field_b
    //     0x1503568: ldur            w0, [x2, #0xb]
    // 0x150356c: r1 = LoadInt32Instr(r0)
    //     0x150356c: sbfx            x1, x0, #1, #0x1f
    // 0x1503570: mov             x0, x1
    // 0x1503574: r1 = 0
    //     0x1503574: movz            x1, #0
    // 0x1503578: cmp             x1, x0
    // 0x150357c: b.hs            #0x1503fa0
    // 0x1503580: LoadField: r0 = r2->field_f
    //     0x1503580: ldur            w0, [x2, #0xf]
    // 0x1503584: DecompressPointer r0
    //     0x1503584: add             x0, x0, HEAP, lsl #32
    // 0x1503588: LoadField: r1 = r0->field_f
    //     0x1503588: ldur            w1, [x0, #0xf]
    // 0x150358c: DecompressPointer r1
    //     0x150358c: add             x1, x1, HEAP, lsl #32
    // 0x1503590: LoadField: r0 = r1->field_43
    //     0x1503590: ldur            w0, [x1, #0x43]
    // 0x1503594: DecompressPointer r0
    //     0x1503594: add             x0, x0, HEAP, lsl #32
    // 0x1503598: cmp             w0, NULL
    // 0x150359c: b.ne            #0x15035a8
    // 0x15035a0: r0 = Null
    //     0x15035a0: mov             x0, NULL
    // 0x15035a4: b               #0x15035bc
    // 0x15035a8: LoadField: r1 = r0->field_b
    //     0x15035a8: ldur            w1, [x0, #0xb]
    // 0x15035ac: cbnz            w1, #0x15035b8
    // 0x15035b0: r0 = false
    //     0x15035b0: add             x0, NULL, #0x30  ; false
    // 0x15035b4: b               #0x15035bc
    // 0x15035b8: r0 = true
    //     0x15035b8: add             x0, NULL, #0x20  ; true
    // 0x15035bc: cmp             w0, NULL
    // 0x15035c0: b.ne            #0x15035c8
    // 0x15035c4: r0 = false
    //     0x15035c4: add             x0, NULL, #0x30  ; false
    // 0x15035c8: mov             x8, x0
    // 0x15035cc: ldur            x2, [fp, #-8]
    // 0x15035d0: ldur            x7, [fp, #-0x18]
    // 0x15035d4: ldur            x6, [fp, #-0x10]
    // 0x15035d8: ldur            x5, [fp, #-0x40]
    // 0x15035dc: ldur            x4, [fp, #-0x38]
    // 0x15035e0: ldur            x3, [fp, #-0x30]
    // 0x15035e4: ldur            x0, [fp, #-0x50]
    // 0x15035e8: stur            x8, [fp, #-0x20]
    // 0x15035ec: LoadField: r1 = r2->field_13
    //     0x15035ec: ldur            w1, [x2, #0x13]
    // 0x15035f0: DecompressPointer r1
    //     0x15035f0: add             x1, x1, HEAP, lsl #32
    // 0x15035f4: r0 = of()
    //     0x15035f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15035f8: LoadField: r1 = r0->field_5b
    //     0x15035f8: ldur            w1, [x0, #0x5b]
    // 0x15035fc: DecompressPointer r1
    //     0x15035fc: add             x1, x1, HEAP, lsl #32
    // 0x1503600: r0 = LoadClassIdInstr(r1)
    //     0x1503600: ldur            x0, [x1, #-1]
    //     0x1503604: ubfx            x0, x0, #0xc, #0x14
    // 0x1503608: d0 = 0.100000
    //     0x1503608: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x150360c: r0 = GDT[cid_x0 + -0xffa]()
    //     0x150360c: sub             lr, x0, #0xffa
    //     0x1503610: ldr             lr, [x21, lr, lsl #3]
    //     0x1503614: blr             lr
    // 0x1503618: stur            x0, [fp, #-0x48]
    // 0x150361c: r0 = BoxDecoration()
    //     0x150361c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1503620: mov             x2, x0
    // 0x1503624: ldur            x0, [fp, #-0x48]
    // 0x1503628: stur            x2, [fp, #-0x58]
    // 0x150362c: StoreField: r2->field_7 = r0
    //     0x150362c: stur            w0, [x2, #7]
    // 0x1503630: r0 = Instance_BorderRadius
    //     0x1503630: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0x1503634: ldr             x0, [x0, #0xf70]
    // 0x1503638: StoreField: r2->field_13 = r0
    //     0x1503638: stur            w0, [x2, #0x13]
    // 0x150363c: r0 = Instance_BoxShape
    //     0x150363c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1503640: ldr             x0, [x0, #0x80]
    // 0x1503644: StoreField: r2->field_23 = r0
    //     0x1503644: stur            w0, [x2, #0x23]
    // 0x1503648: ldur            x0, [fp, #-8]
    // 0x150364c: LoadField: r1 = r0->field_13
    //     0x150364c: ldur            w1, [x0, #0x13]
    // 0x1503650: DecompressPointer r1
    //     0x1503650: add             x1, x1, HEAP, lsl #32
    // 0x1503654: r0 = of()
    //     0x1503654: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1503658: LoadField: r1 = r0->field_87
    //     0x1503658: ldur            w1, [x0, #0x87]
    // 0x150365c: DecompressPointer r1
    //     0x150365c: add             x1, x1, HEAP, lsl #32
    // 0x1503660: LoadField: r0 = r1->field_7
    //     0x1503660: ldur            w0, [x1, #7]
    // 0x1503664: DecompressPointer r0
    //     0x1503664: add             x0, x0, HEAP, lsl #32
    // 0x1503668: stur            x0, [fp, #-0x48]
    // 0x150366c: r1 = Instance_Color
    //     0x150366c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1503670: d0 = 0.700000
    //     0x1503670: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1503674: ldr             d0, [x17, #0xf48]
    // 0x1503678: r0 = withOpacity()
    //     0x1503678: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x150367c: r16 = 12.000000
    //     0x150367c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1503680: ldr             x16, [x16, #0x9e8]
    // 0x1503684: stp             x0, x16, [SP]
    // 0x1503688: ldur            x1, [fp, #-0x48]
    // 0x150368c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x150368c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1503690: ldr             x4, [x4, #0xaa0]
    // 0x1503694: r0 = copyWith()
    //     0x1503694: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1503698: stur            x0, [fp, #-0x48]
    // 0x150369c: r0 = Text()
    //     0x150369c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15036a0: mov             x1, x0
    // 0x15036a4: r0 = "Customised"
    //     0x15036a4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0x15036a8: ldr             x0, [x0, #0xd88]
    // 0x15036ac: stur            x1, [fp, #-0x60]
    // 0x15036b0: StoreField: r1->field_b = r0
    //     0x15036b0: stur            w0, [x1, #0xb]
    // 0x15036b4: ldur            x0, [fp, #-0x48]
    // 0x15036b8: StoreField: r1->field_13 = r0
    //     0x15036b8: stur            w0, [x1, #0x13]
    // 0x15036bc: r0 = Center()
    //     0x15036bc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x15036c0: mov             x1, x0
    // 0x15036c4: r0 = Instance_Alignment
    //     0x15036c4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15036c8: ldr             x0, [x0, #0xb10]
    // 0x15036cc: stur            x1, [fp, #-0x48]
    // 0x15036d0: StoreField: r1->field_f = r0
    //     0x15036d0: stur            w0, [x1, #0xf]
    // 0x15036d4: ldur            x2, [fp, #-0x60]
    // 0x15036d8: StoreField: r1->field_b = r2
    //     0x15036d8: stur            w2, [x1, #0xb]
    // 0x15036dc: r0 = Container()
    //     0x15036dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15036e0: stur            x0, [fp, #-0x60]
    // 0x15036e4: r16 = Instance_EdgeInsets
    //     0x15036e4: add             x16, PP, #0x38, lsl #12  ; [pp+0x38d90] Obj!EdgeInsets@d59631
    //     0x15036e8: ldr             x16, [x16, #0xd90]
    // 0x15036ec: ldur            lr, [fp, #-0x58]
    // 0x15036f0: stp             lr, x16, [SP, #8]
    // 0x15036f4: ldur            x16, [fp, #-0x48]
    // 0x15036f8: str             x16, [SP]
    // 0x15036fc: mov             x1, x0
    // 0x1503700: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x1503700: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x1503704: ldr             x4, [x4, #0x610]
    // 0x1503708: r0 = Container()
    //     0x1503708: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x150370c: r0 = Visibility()
    //     0x150370c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1503710: mov             x3, x0
    // 0x1503714: ldur            x0, [fp, #-0x60]
    // 0x1503718: stur            x3, [fp, #-0x48]
    // 0x150371c: StoreField: r3->field_b = r0
    //     0x150371c: stur            w0, [x3, #0xb]
    // 0x1503720: r0 = Instance_SizedBox
    //     0x1503720: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1503724: StoreField: r3->field_f = r0
    //     0x1503724: stur            w0, [x3, #0xf]
    // 0x1503728: ldur            x0, [fp, #-0x20]
    // 0x150372c: StoreField: r3->field_13 = r0
    //     0x150372c: stur            w0, [x3, #0x13]
    // 0x1503730: r0 = false
    //     0x1503730: add             x0, NULL, #0x30  ; false
    // 0x1503734: ArrayStore: r3[0] = r0  ; List_4
    //     0x1503734: stur            w0, [x3, #0x17]
    // 0x1503738: StoreField: r3->field_1b = r0
    //     0x1503738: stur            w0, [x3, #0x1b]
    // 0x150373c: StoreField: r3->field_1f = r0
    //     0x150373c: stur            w0, [x3, #0x1f]
    // 0x1503740: StoreField: r3->field_23 = r0
    //     0x1503740: stur            w0, [x3, #0x23]
    // 0x1503744: StoreField: r3->field_27 = r0
    //     0x1503744: stur            w0, [x3, #0x27]
    // 0x1503748: StoreField: r3->field_2b = r0
    //     0x1503748: stur            w0, [x3, #0x2b]
    // 0x150374c: r1 = Null
    //     0x150374c: mov             x1, NULL
    // 0x1503750: r2 = 6
    //     0x1503750: movz            x2, #0x6
    // 0x1503754: r0 = AllocateArray()
    //     0x1503754: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1503758: mov             x2, x0
    // 0x150375c: ldur            x0, [fp, #-0x50]
    // 0x1503760: stur            x2, [fp, #-0x20]
    // 0x1503764: StoreField: r2->field_f = r0
    //     0x1503764: stur            w0, [x2, #0xf]
    // 0x1503768: r16 = Instance_SizedBox
    //     0x1503768: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x150376c: ldr             x16, [x16, #0x998]
    // 0x1503770: StoreField: r2->field_13 = r16
    //     0x1503770: stur            w16, [x2, #0x13]
    // 0x1503774: ldur            x0, [fp, #-0x48]
    // 0x1503778: ArrayStore: r2[0] = r0  ; List_4
    //     0x1503778: stur            w0, [x2, #0x17]
    // 0x150377c: r1 = <Widget>
    //     0x150377c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1503780: r0 = AllocateGrowableArray()
    //     0x1503780: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1503784: mov             x1, x0
    // 0x1503788: ldur            x0, [fp, #-0x20]
    // 0x150378c: stur            x1, [fp, #-0x48]
    // 0x1503790: StoreField: r1->field_f = r0
    //     0x1503790: stur            w0, [x1, #0xf]
    // 0x1503794: r0 = 6
    //     0x1503794: movz            x0, #0x6
    // 0x1503798: StoreField: r1->field_b = r0
    //     0x1503798: stur            w0, [x1, #0xb]
    // 0x150379c: r0 = Row()
    //     0x150379c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15037a0: mov             x3, x0
    // 0x15037a4: r0 = Instance_Axis
    //     0x15037a4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15037a8: stur            x3, [fp, #-0x20]
    // 0x15037ac: StoreField: r3->field_f = r0
    //     0x15037ac: stur            w0, [x3, #0xf]
    // 0x15037b0: r4 = Instance_MainAxisAlignment
    //     0x15037b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x15037b4: ldr             x4, [x4, #0xa08]
    // 0x15037b8: StoreField: r3->field_13 = r4
    //     0x15037b8: stur            w4, [x3, #0x13]
    // 0x15037bc: r5 = Instance_MainAxisSize
    //     0x15037bc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15037c0: ldr             x5, [x5, #0xa10]
    // 0x15037c4: ArrayStore: r3[0] = r5  ; List_4
    //     0x15037c4: stur            w5, [x3, #0x17]
    // 0x15037c8: r6 = Instance_CrossAxisAlignment
    //     0x15037c8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15037cc: ldr             x6, [x6, #0xa18]
    // 0x15037d0: StoreField: r3->field_1b = r6
    //     0x15037d0: stur            w6, [x3, #0x1b]
    // 0x15037d4: r7 = Instance_VerticalDirection
    //     0x15037d4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15037d8: ldr             x7, [x7, #0xa20]
    // 0x15037dc: StoreField: r3->field_23 = r7
    //     0x15037dc: stur            w7, [x3, #0x23]
    // 0x15037e0: r8 = Instance_Clip
    //     0x15037e0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15037e4: ldr             x8, [x8, #0x38]
    // 0x15037e8: StoreField: r3->field_2b = r8
    //     0x15037e8: stur            w8, [x3, #0x2b]
    // 0x15037ec: StoreField: r3->field_2f = rZR
    //     0x15037ec: stur            xzr, [x3, #0x2f]
    // 0x15037f0: ldur            x1, [fp, #-0x48]
    // 0x15037f4: StoreField: r3->field_b = r1
    //     0x15037f4: stur            w1, [x3, #0xb]
    // 0x15037f8: r1 = Null
    //     0x15037f8: mov             x1, NULL
    // 0x15037fc: r2 = 8
    //     0x15037fc: movz            x2, #0x8
    // 0x1503800: r0 = AllocateArray()
    //     0x1503800: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1503804: mov             x2, x0
    // 0x1503808: ldur            x0, [fp, #-0x38]
    // 0x150380c: stur            x2, [fp, #-0x48]
    // 0x1503810: StoreField: r2->field_f = r0
    //     0x1503810: stur            w0, [x2, #0xf]
    // 0x1503814: ldur            x0, [fp, #-0x30]
    // 0x1503818: StoreField: r2->field_13 = r0
    //     0x1503818: stur            w0, [x2, #0x13]
    // 0x150381c: r16 = Instance_SizedBox
    //     0x150381c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x1503820: ldr             x16, [x16, #0xc70]
    // 0x1503824: ArrayStore: r2[0] = r16  ; List_4
    //     0x1503824: stur            w16, [x2, #0x17]
    // 0x1503828: ldur            x0, [fp, #-0x20]
    // 0x150382c: StoreField: r2->field_1b = r0
    //     0x150382c: stur            w0, [x2, #0x1b]
    // 0x1503830: r1 = <Widget>
    //     0x1503830: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1503834: r0 = AllocateGrowableArray()
    //     0x1503834: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1503838: mov             x1, x0
    // 0x150383c: ldur            x0, [fp, #-0x48]
    // 0x1503840: stur            x1, [fp, #-0x20]
    // 0x1503844: StoreField: r1->field_f = r0
    //     0x1503844: stur            w0, [x1, #0xf]
    // 0x1503848: r2 = 8
    //     0x1503848: movz            x2, #0x8
    // 0x150384c: StoreField: r1->field_b = r2
    //     0x150384c: stur            w2, [x1, #0xb]
    // 0x1503850: r0 = Column()
    //     0x1503850: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1503854: mov             x2, x0
    // 0x1503858: r0 = Instance_Axis
    //     0x1503858: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x150385c: stur            x2, [fp, #-0x30]
    // 0x1503860: StoreField: r2->field_f = r0
    //     0x1503860: stur            w0, [x2, #0xf]
    // 0x1503864: r3 = Instance_MainAxisAlignment
    //     0x1503864: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1503868: ldr             x3, [x3, #0xa08]
    // 0x150386c: StoreField: r2->field_13 = r3
    //     0x150386c: stur            w3, [x2, #0x13]
    // 0x1503870: r4 = Instance_MainAxisSize
    //     0x1503870: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1503874: ldr             x4, [x4, #0xa10]
    // 0x1503878: ArrayStore: r2[0] = r4  ; List_4
    //     0x1503878: stur            w4, [x2, #0x17]
    // 0x150387c: r1 = Instance_CrossAxisAlignment
    //     0x150387c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1503880: ldr             x1, [x1, #0x890]
    // 0x1503884: StoreField: r2->field_1b = r1
    //     0x1503884: stur            w1, [x2, #0x1b]
    // 0x1503888: r5 = Instance_VerticalDirection
    //     0x1503888: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x150388c: ldr             x5, [x5, #0xa20]
    // 0x1503890: StoreField: r2->field_23 = r5
    //     0x1503890: stur            w5, [x2, #0x23]
    // 0x1503894: r6 = Instance_Clip
    //     0x1503894: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1503898: ldr             x6, [x6, #0x38]
    // 0x150389c: StoreField: r2->field_2b = r6
    //     0x150389c: stur            w6, [x2, #0x2b]
    // 0x15038a0: StoreField: r2->field_2f = rZR
    //     0x15038a0: stur            xzr, [x2, #0x2f]
    // 0x15038a4: ldur            x1, [fp, #-0x20]
    // 0x15038a8: StoreField: r2->field_b = r1
    //     0x15038a8: stur            w1, [x2, #0xb]
    // 0x15038ac: r1 = <FlexParentData>
    //     0x15038ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x15038b0: ldr             x1, [x1, #0xe00]
    // 0x15038b4: r0 = Expanded()
    //     0x15038b4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x15038b8: mov             x3, x0
    // 0x15038bc: r0 = 1
    //     0x15038bc: movz            x0, #0x1
    // 0x15038c0: stur            x3, [fp, #-0x20]
    // 0x15038c4: StoreField: r3->field_13 = r0
    //     0x15038c4: stur            x0, [x3, #0x13]
    // 0x15038c8: r0 = Instance_FlexFit
    //     0x15038c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x15038cc: ldr             x0, [x0, #0xe08]
    // 0x15038d0: StoreField: r3->field_1b = r0
    //     0x15038d0: stur            w0, [x3, #0x1b]
    // 0x15038d4: ldur            x0, [fp, #-0x30]
    // 0x15038d8: StoreField: r3->field_b = r0
    //     0x15038d8: stur            w0, [x3, #0xb]
    // 0x15038dc: r1 = Null
    //     0x15038dc: mov             x1, NULL
    // 0x15038e0: r2 = 8
    //     0x15038e0: movz            x2, #0x8
    // 0x15038e4: r0 = AllocateArray()
    //     0x15038e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15038e8: mov             x2, x0
    // 0x15038ec: ldur            x0, [fp, #-0x40]
    // 0x15038f0: stur            x2, [fp, #-0x30]
    // 0x15038f4: StoreField: r2->field_f = r0
    //     0x15038f4: stur            w0, [x2, #0xf]
    // 0x15038f8: r16 = Instance_SizedBox
    //     0x15038f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x15038fc: ldr             x16, [x16, #0xb20]
    // 0x1503900: StoreField: r2->field_13 = r16
    //     0x1503900: stur            w16, [x2, #0x13]
    // 0x1503904: ldur            x0, [fp, #-0x20]
    // 0x1503908: ArrayStore: r2[0] = r0  ; List_4
    //     0x1503908: stur            w0, [x2, #0x17]
    // 0x150390c: r16 = Instance_SizedBox
    //     0x150390c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x1503910: ldr             x16, [x16, #0xb20]
    // 0x1503914: StoreField: r2->field_1b = r16
    //     0x1503914: stur            w16, [x2, #0x1b]
    // 0x1503918: r1 = <Widget>
    //     0x1503918: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x150391c: r0 = AllocateGrowableArray()
    //     0x150391c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1503920: mov             x1, x0
    // 0x1503924: ldur            x0, [fp, #-0x30]
    // 0x1503928: stur            x1, [fp, #-0x20]
    // 0x150392c: StoreField: r1->field_f = r0
    //     0x150392c: stur            w0, [x1, #0xf]
    // 0x1503930: r0 = 8
    //     0x1503930: movz            x0, #0x8
    // 0x1503934: StoreField: r1->field_b = r0
    //     0x1503934: stur            w0, [x1, #0xb]
    // 0x1503938: r0 = Row()
    //     0x1503938: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x150393c: mov             x1, x0
    // 0x1503940: r0 = Instance_Axis
    //     0x1503940: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1503944: stur            x1, [fp, #-0x30]
    // 0x1503948: StoreField: r1->field_f = r0
    //     0x1503948: stur            w0, [x1, #0xf]
    // 0x150394c: r2 = Instance_MainAxisAlignment
    //     0x150394c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1503950: ldr             x2, [x2, #0xa08]
    // 0x1503954: StoreField: r1->field_13 = r2
    //     0x1503954: stur            w2, [x1, #0x13]
    // 0x1503958: r3 = Instance_MainAxisSize
    //     0x1503958: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x150395c: ldr             x3, [x3, #0xa10]
    // 0x1503960: ArrayStore: r1[0] = r3  ; List_4
    //     0x1503960: stur            w3, [x1, #0x17]
    // 0x1503964: r4 = Instance_CrossAxisAlignment
    //     0x1503964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1503968: ldr             x4, [x4, #0xa18]
    // 0x150396c: StoreField: r1->field_1b = r4
    //     0x150396c: stur            w4, [x1, #0x1b]
    // 0x1503970: r5 = Instance_VerticalDirection
    //     0x1503970: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1503974: ldr             x5, [x5, #0xa20]
    // 0x1503978: StoreField: r1->field_23 = r5
    //     0x1503978: stur            w5, [x1, #0x23]
    // 0x150397c: r6 = Instance_Clip
    //     0x150397c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1503980: ldr             x6, [x6, #0x38]
    // 0x1503984: StoreField: r1->field_2b = r6
    //     0x1503984: stur            w6, [x1, #0x2b]
    // 0x1503988: StoreField: r1->field_2f = rZR
    //     0x1503988: stur            xzr, [x1, #0x2f]
    // 0x150398c: ldur            x7, [fp, #-0x20]
    // 0x1503990: StoreField: r1->field_b = r7
    //     0x1503990: stur            w7, [x1, #0xb]
    // 0x1503994: r0 = Padding()
    //     0x1503994: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1503998: mov             x1, x0
    // 0x150399c: r0 = Instance_EdgeInsets
    //     0x150399c: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f20] Obj!EdgeInsets@d57b61
    //     0x15039a0: ldr             x0, [x0, #0xf20]
    // 0x15039a4: stur            x1, [fp, #-0x20]
    // 0x15039a8: StoreField: r1->field_f = r0
    //     0x15039a8: stur            w0, [x1, #0xf]
    // 0x15039ac: ldur            x0, [fp, #-0x30]
    // 0x15039b0: StoreField: r1->field_b = r0
    //     0x15039b0: stur            w0, [x1, #0xb]
    // 0x15039b4: r0 = Container()
    //     0x15039b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15039b8: stur            x0, [fp, #-0x30]
    // 0x15039bc: ldur            x16, [fp, #-0x28]
    // 0x15039c0: ldur            lr, [fp, #-0x20]
    // 0x15039c4: stp             lr, x16, [SP]
    // 0x15039c8: mov             x1, x0
    // 0x15039cc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x15039cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x15039d0: ldr             x4, [x4, #0x88]
    // 0x15039d4: r0 = Container()
    //     0x15039d4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15039d8: r1 = Null
    //     0x15039d8: mov             x1, NULL
    // 0x15039dc: r2 = 4
    //     0x15039dc: movz            x2, #0x4
    // 0x15039e0: r0 = AllocateArray()
    //     0x15039e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15039e4: mov             x2, x0
    // 0x15039e8: ldur            x0, [fp, #-0x10]
    // 0x15039ec: stur            x2, [fp, #-0x20]
    // 0x15039f0: StoreField: r2->field_f = r0
    //     0x15039f0: stur            w0, [x2, #0xf]
    // 0x15039f4: ldur            x0, [fp, #-0x30]
    // 0x15039f8: StoreField: r2->field_13 = r0
    //     0x15039f8: stur            w0, [x2, #0x13]
    // 0x15039fc: r1 = <Widget>
    //     0x15039fc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1503a00: r0 = AllocateGrowableArray()
    //     0x1503a00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1503a04: mov             x1, x0
    // 0x1503a08: ldur            x0, [fp, #-0x20]
    // 0x1503a0c: stur            x1, [fp, #-0x10]
    // 0x1503a10: StoreField: r1->field_f = r0
    //     0x1503a10: stur            w0, [x1, #0xf]
    // 0x1503a14: r2 = 4
    //     0x1503a14: movz            x2, #0x4
    // 0x1503a18: StoreField: r1->field_b = r2
    //     0x1503a18: stur            w2, [x1, #0xb]
    // 0x1503a1c: r0 = Column()
    //     0x1503a1c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1503a20: mov             x1, x0
    // 0x1503a24: r0 = Instance_Axis
    //     0x1503a24: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1503a28: stur            x1, [fp, #-0x20]
    // 0x1503a2c: StoreField: r1->field_f = r0
    //     0x1503a2c: stur            w0, [x1, #0xf]
    // 0x1503a30: r0 = Instance_MainAxisAlignment
    //     0x1503a30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1503a34: ldr             x0, [x0, #0xa08]
    // 0x1503a38: StoreField: r1->field_13 = r0
    //     0x1503a38: stur            w0, [x1, #0x13]
    // 0x1503a3c: r0 = Instance_MainAxisSize
    //     0x1503a3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1503a40: ldr             x0, [x0, #0xa10]
    // 0x1503a44: ArrayStore: r1[0] = r0  ; List_4
    //     0x1503a44: stur            w0, [x1, #0x17]
    // 0x1503a48: r2 = Instance_CrossAxisAlignment
    //     0x1503a48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1503a4c: ldr             x2, [x2, #0xa18]
    // 0x1503a50: StoreField: r1->field_1b = r2
    //     0x1503a50: stur            w2, [x1, #0x1b]
    // 0x1503a54: r3 = Instance_VerticalDirection
    //     0x1503a54: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1503a58: ldr             x3, [x3, #0xa20]
    // 0x1503a5c: StoreField: r1->field_23 = r3
    //     0x1503a5c: stur            w3, [x1, #0x23]
    // 0x1503a60: r4 = Instance_Clip
    //     0x1503a60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1503a64: ldr             x4, [x4, #0x38]
    // 0x1503a68: StoreField: r1->field_2b = r4
    //     0x1503a68: stur            w4, [x1, #0x2b]
    // 0x1503a6c: StoreField: r1->field_2f = rZR
    //     0x1503a6c: stur            xzr, [x1, #0x2f]
    // 0x1503a70: ldur            x5, [fp, #-0x10]
    // 0x1503a74: StoreField: r1->field_b = r5
    //     0x1503a74: stur            w5, [x1, #0xb]
    // 0x1503a78: r0 = SvgPicture()
    //     0x1503a78: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1503a7c: stur            x0, [fp, #-0x10]
    // 0x1503a80: r16 = Instance_BoxFit
    //     0x1503a80: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x1503a84: ldr             x16, [x16, #0xb18]
    // 0x1503a88: str             x16, [SP]
    // 0x1503a8c: mov             x1, x0
    // 0x1503a90: r2 = "assets/images/product_between_icon.svg"
    //     0x1503a90: add             x2, PP, #0x35, lsl #12  ; [pp+0x35f28] "assets/images/product_between_icon.svg"
    //     0x1503a94: ldr             x2, [x2, #0xf28]
    // 0x1503a98: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0x1503a98: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0x1503a9c: ldr             x4, [x4, #0xb0]
    // 0x1503aa0: r0 = SvgPicture.asset()
    //     0x1503aa0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1503aa4: r1 = <StackParentData>
    //     0x1503aa4: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x1503aa8: ldr             x1, [x1, #0x8e0]
    // 0x1503aac: r0 = Positioned()
    //     0x1503aac: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x1503ab0: mov             x3, x0
    // 0x1503ab4: r0 = 0.000000
    //     0x1503ab4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x1503ab8: stur            x3, [fp, #-0x28]
    // 0x1503abc: StoreField: r3->field_13 = r0
    //     0x1503abc: stur            w0, [x3, #0x13]
    // 0x1503ac0: ArrayStore: r3[0] = r0  ; List_4
    //     0x1503ac0: stur            w0, [x3, #0x17]
    // 0x1503ac4: StoreField: r3->field_1b = r0
    //     0x1503ac4: stur            w0, [x3, #0x1b]
    // 0x1503ac8: StoreField: r3->field_1f = r0
    //     0x1503ac8: stur            w0, [x3, #0x1f]
    // 0x1503acc: ldur            x0, [fp, #-0x10]
    // 0x1503ad0: StoreField: r3->field_b = r0
    //     0x1503ad0: stur            w0, [x3, #0xb]
    // 0x1503ad4: r1 = Null
    //     0x1503ad4: mov             x1, NULL
    // 0x1503ad8: r2 = 4
    //     0x1503ad8: movz            x2, #0x4
    // 0x1503adc: r0 = AllocateArray()
    //     0x1503adc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1503ae0: mov             x2, x0
    // 0x1503ae4: ldur            x0, [fp, #-0x20]
    // 0x1503ae8: stur            x2, [fp, #-0x10]
    // 0x1503aec: StoreField: r2->field_f = r0
    //     0x1503aec: stur            w0, [x2, #0xf]
    // 0x1503af0: ldur            x0, [fp, #-0x28]
    // 0x1503af4: StoreField: r2->field_13 = r0
    //     0x1503af4: stur            w0, [x2, #0x13]
    // 0x1503af8: r1 = <Widget>
    //     0x1503af8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1503afc: r0 = AllocateGrowableArray()
    //     0x1503afc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1503b00: mov             x1, x0
    // 0x1503b04: ldur            x0, [fp, #-0x10]
    // 0x1503b08: stur            x1, [fp, #-0x20]
    // 0x1503b0c: StoreField: r1->field_f = r0
    //     0x1503b0c: stur            w0, [x1, #0xf]
    // 0x1503b10: r2 = 4
    //     0x1503b10: movz            x2, #0x4
    // 0x1503b14: StoreField: r1->field_b = r2
    //     0x1503b14: stur            w2, [x1, #0xb]
    // 0x1503b18: r0 = Stack()
    //     0x1503b18: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x1503b1c: mov             x1, x0
    // 0x1503b20: r0 = Instance_Alignment
    //     0x1503b20: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1503b24: ldr             x0, [x0, #0xb10]
    // 0x1503b28: stur            x1, [fp, #-0x10]
    // 0x1503b2c: StoreField: r1->field_f = r0
    //     0x1503b2c: stur            w0, [x1, #0xf]
    // 0x1503b30: r0 = Instance_StackFit
    //     0x1503b30: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x1503b34: ldr             x0, [x0, #0xfa8]
    // 0x1503b38: ArrayStore: r1[0] = r0  ; List_4
    //     0x1503b38: stur            w0, [x1, #0x17]
    // 0x1503b3c: r0 = Instance_Clip
    //     0x1503b3c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x1503b40: ldr             x0, [x0, #0x7e0]
    // 0x1503b44: StoreField: r1->field_1b = r0
    //     0x1503b44: stur            w0, [x1, #0x1b]
    // 0x1503b48: ldur            x0, [fp, #-0x20]
    // 0x1503b4c: StoreField: r1->field_b = r0
    //     0x1503b4c: stur            w0, [x1, #0xb]
    // 0x1503b50: r0 = Container()
    //     0x1503b50: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1503b54: mov             x1, x0
    // 0x1503b58: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1503b58: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1503b5c: r0 = Container()
    //     0x1503b5c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1503b60: r0 = Accordion()
    //     0x1503b60: bl              #0xba4874  ; AllocateAccordionStub -> Accordion (size=0x3c)
    // 0x1503b64: mov             x1, x0
    // 0x1503b68: ldur            x0, [fp, #-0x18]
    // 0x1503b6c: stur            x1, [fp, #-0x20]
    // 0x1503b70: StoreField: r1->field_b = r0
    //     0x1503b70: stur            w0, [x1, #0xb]
    // 0x1503b74: ldur            x0, [fp, #-0x10]
    // 0x1503b78: StoreField: r1->field_13 = r0
    //     0x1503b78: stur            w0, [x1, #0x13]
    // 0x1503b7c: r0 = false
    //     0x1503b7c: add             x0, NULL, #0x30  ; false
    // 0x1503b80: ArrayStore: r1[0] = r0  ; List_4
    //     0x1503b80: stur            w0, [x1, #0x17]
    // 0x1503b84: r0 = 25.000000
    //     0x1503b84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x1503b88: ldr             x0, [x0, #0x98]
    // 0x1503b8c: StoreField: r1->field_1b = r0
    //     0x1503b8c: stur            w0, [x1, #0x1b]
    // 0x1503b90: r0 = true
    //     0x1503b90: add             x0, NULL, #0x20  ; true
    // 0x1503b94: StoreField: r1->field_1f = r0
    //     0x1503b94: stur            w0, [x1, #0x1f]
    // 0x1503b98: r0 = Padding()
    //     0x1503b98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1503b9c: mov             x2, x0
    // 0x1503ba0: r0 = Instance_EdgeInsets
    //     0x1503ba0: add             x0, PP, #0x3a, lsl #12  ; [pp+0x3af80] Obj!EdgeInsets@d5a051
    //     0x1503ba4: ldr             x0, [x0, #0xf80]
    // 0x1503ba8: stur            x2, [fp, #-0x10]
    // 0x1503bac: StoreField: r2->field_f = r0
    //     0x1503bac: stur            w0, [x2, #0xf]
    // 0x1503bb0: ldur            x0, [fp, #-0x20]
    // 0x1503bb4: StoreField: r2->field_b = r0
    //     0x1503bb4: stur            w0, [x2, #0xb]
    // 0x1503bb8: ldur            x0, [fp, #-8]
    // 0x1503bbc: LoadField: r1 = r0->field_f
    //     0x1503bbc: ldur            w1, [x0, #0xf]
    // 0x1503bc0: DecompressPointer r1
    //     0x1503bc0: add             x1, x1, HEAP, lsl #32
    // 0x1503bc4: r0 = controller()
    //     0x1503bc4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1503bc8: LoadField: r1 = r0->field_53
    //     0x1503bc8: ldur            w1, [x0, #0x53]
    // 0x1503bcc: DecompressPointer r1
    //     0x1503bcc: add             x1, x1, HEAP, lsl #32
    // 0x1503bd0: r0 = value()
    //     0x1503bd0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1503bd4: LoadField: r1 = r0->field_2f
    //     0x1503bd4: ldur            w1, [x0, #0x2f]
    // 0x1503bd8: DecompressPointer r1
    //     0x1503bd8: add             x1, x1, HEAP, lsl #32
    // 0x1503bdc: cmp             w1, NULL
    // 0x1503be0: b.ne            #0x1503bec
    // 0x1503be4: r0 = ""
    //     0x1503be4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1503be8: b               #0x1503bf0
    // 0x1503bec: mov             x0, x1
    // 0x1503bf0: ldur            x2, [fp, #-8]
    // 0x1503bf4: stur            x0, [fp, #-0x18]
    // 0x1503bf8: LoadField: r1 = r2->field_13
    //     0x1503bf8: ldur            w1, [x2, #0x13]
    // 0x1503bfc: DecompressPointer r1
    //     0x1503bfc: add             x1, x1, HEAP, lsl #32
    // 0x1503c00: r0 = of()
    //     0x1503c00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1503c04: LoadField: r1 = r0->field_87
    //     0x1503c04: ldur            w1, [x0, #0x87]
    // 0x1503c08: DecompressPointer r1
    //     0x1503c08: add             x1, x1, HEAP, lsl #32
    // 0x1503c0c: LoadField: r0 = r1->field_7
    //     0x1503c0c: ldur            w0, [x1, #7]
    // 0x1503c10: DecompressPointer r0
    //     0x1503c10: add             x0, x0, HEAP, lsl #32
    // 0x1503c14: stur            x0, [fp, #-0x20]
    // 0x1503c18: r1 = Instance_Color
    //     0x1503c18: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1503c1c: d0 = 0.700000
    //     0x1503c1c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1503c20: ldr             d0, [x17, #0xf48]
    // 0x1503c24: r0 = withOpacity()
    //     0x1503c24: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1503c28: r16 = 14.000000
    //     0x1503c28: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1503c2c: ldr             x16, [x16, #0x1d8]
    // 0x1503c30: stp             x16, x0, [SP]
    // 0x1503c34: ldur            x1, [fp, #-0x20]
    // 0x1503c38: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1503c38: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1503c3c: ldr             x4, [x4, #0x9b8]
    // 0x1503c40: r0 = copyWith()
    //     0x1503c40: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1503c44: stur            x0, [fp, #-0x20]
    // 0x1503c48: r0 = Text()
    //     0x1503c48: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1503c4c: mov             x1, x0
    // 0x1503c50: ldur            x0, [fp, #-0x18]
    // 0x1503c54: stur            x1, [fp, #-0x28]
    // 0x1503c58: StoreField: r1->field_b = r0
    //     0x1503c58: stur            w0, [x1, #0xb]
    // 0x1503c5c: ldur            x0, [fp, #-0x20]
    // 0x1503c60: StoreField: r1->field_13 = r0
    //     0x1503c60: stur            w0, [x1, #0x13]
    // 0x1503c64: r0 = SvgPicture()
    //     0x1503c64: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x1503c68: stur            x0, [fp, #-0x18]
    // 0x1503c6c: r16 = "return order"
    //     0x1503c6c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0x1503c70: ldr             x16, [x16, #0xc78]
    // 0x1503c74: r30 = Instance_BoxFit
    //     0x1503c74: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x1503c78: ldr             lr, [lr, #0xb18]
    // 0x1503c7c: stp             lr, x16, [SP]
    // 0x1503c80: mov             x1, x0
    // 0x1503c84: r2 = "assets/images/secure_icon.svg"
    //     0x1503c84: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0x1503c88: ldr             x2, [x2, #0xc80]
    // 0x1503c8c: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0x1503c8c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0x1503c90: ldr             x4, [x4, #0xb28]
    // 0x1503c94: r0 = SvgPicture.asset()
    //     0x1503c94: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x1503c98: r1 = Null
    //     0x1503c98: mov             x1, NULL
    // 0x1503c9c: r2 = 4
    //     0x1503c9c: movz            x2, #0x4
    // 0x1503ca0: r0 = AllocateArray()
    //     0x1503ca0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1503ca4: mov             x2, x0
    // 0x1503ca8: ldur            x0, [fp, #-0x28]
    // 0x1503cac: stur            x2, [fp, #-0x20]
    // 0x1503cb0: StoreField: r2->field_f = r0
    //     0x1503cb0: stur            w0, [x2, #0xf]
    // 0x1503cb4: ldur            x0, [fp, #-0x18]
    // 0x1503cb8: StoreField: r2->field_13 = r0
    //     0x1503cb8: stur            w0, [x2, #0x13]
    // 0x1503cbc: r1 = <Widget>
    //     0x1503cbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1503cc0: r0 = AllocateGrowableArray()
    //     0x1503cc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1503cc4: mov             x1, x0
    // 0x1503cc8: ldur            x0, [fp, #-0x20]
    // 0x1503ccc: stur            x1, [fp, #-0x18]
    // 0x1503cd0: StoreField: r1->field_f = r0
    //     0x1503cd0: stur            w0, [x1, #0xf]
    // 0x1503cd4: r0 = 4
    //     0x1503cd4: movz            x0, #0x4
    // 0x1503cd8: StoreField: r1->field_b = r0
    //     0x1503cd8: stur            w0, [x1, #0xb]
    // 0x1503cdc: r0 = Row()
    //     0x1503cdc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1503ce0: mov             x1, x0
    // 0x1503ce4: r0 = Instance_Axis
    //     0x1503ce4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1503ce8: stur            x1, [fp, #-0x20]
    // 0x1503cec: StoreField: r1->field_f = r0
    //     0x1503cec: stur            w0, [x1, #0xf]
    // 0x1503cf0: r0 = Instance_MainAxisAlignment
    //     0x1503cf0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x1503cf4: ldr             x0, [x0, #0xa8]
    // 0x1503cf8: StoreField: r1->field_13 = r0
    //     0x1503cf8: stur            w0, [x1, #0x13]
    // 0x1503cfc: r0 = Instance_MainAxisSize
    //     0x1503cfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1503d00: ldr             x0, [x0, #0xa10]
    // 0x1503d04: ArrayStore: r1[0] = r0  ; List_4
    //     0x1503d04: stur            w0, [x1, #0x17]
    // 0x1503d08: r0 = Instance_CrossAxisAlignment
    //     0x1503d08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1503d0c: ldr             x0, [x0, #0xa18]
    // 0x1503d10: StoreField: r1->field_1b = r0
    //     0x1503d10: stur            w0, [x1, #0x1b]
    // 0x1503d14: r0 = Instance_VerticalDirection
    //     0x1503d14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1503d18: ldr             x0, [x0, #0xa20]
    // 0x1503d1c: StoreField: r1->field_23 = r0
    //     0x1503d1c: stur            w0, [x1, #0x23]
    // 0x1503d20: r0 = Instance_Clip
    //     0x1503d20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1503d24: ldr             x0, [x0, #0x38]
    // 0x1503d28: StoreField: r1->field_2b = r0
    //     0x1503d28: stur            w0, [x1, #0x2b]
    // 0x1503d2c: StoreField: r1->field_2f = rZR
    //     0x1503d2c: stur            xzr, [x1, #0x2f]
    // 0x1503d30: ldur            x0, [fp, #-0x18]
    // 0x1503d34: StoreField: r1->field_b = r0
    //     0x1503d34: stur            w0, [x1, #0xb]
    // 0x1503d38: r0 = Padding()
    //     0x1503d38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1503d3c: mov             x2, x0
    // 0x1503d40: r0 = Instance_EdgeInsets
    //     0x1503d40: add             x0, PP, #0x3a, lsl #12  ; [pp+0x3af88] Obj!EdgeInsets@d59b71
    //     0x1503d44: ldr             x0, [x0, #0xf88]
    // 0x1503d48: stur            x2, [fp, #-0x18]
    // 0x1503d4c: StoreField: r2->field_f = r0
    //     0x1503d4c: stur            w0, [x2, #0xf]
    // 0x1503d50: ldur            x0, [fp, #-0x20]
    // 0x1503d54: StoreField: r2->field_b = r0
    //     0x1503d54: stur            w0, [x2, #0xb]
    // 0x1503d58: ldur            x0, [fp, #-8]
    // 0x1503d5c: LoadField: r1 = r0->field_f
    //     0x1503d5c: ldur            w1, [x0, #0xf]
    // 0x1503d60: DecompressPointer r1
    //     0x1503d60: add             x1, x1, HEAP, lsl #32
    // 0x1503d64: r0 = controller()
    //     0x1503d64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1503d68: LoadField: r1 = r0->field_53
    //     0x1503d68: ldur            w1, [x0, #0x53]
    // 0x1503d6c: DecompressPointer r1
    //     0x1503d6c: add             x1, x1, HEAP, lsl #32
    // 0x1503d70: r0 = value()
    //     0x1503d70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1503d74: LoadField: r1 = r0->field_1f
    //     0x1503d74: ldur            w1, [x0, #0x1f]
    // 0x1503d78: DecompressPointer r1
    //     0x1503d78: add             x1, x1, HEAP, lsl #32
    // 0x1503d7c: cmp             w1, NULL
    // 0x1503d80: b.ne            #0x1503d98
    // 0x1503d84: r1 = <PaymentOptions>
    //     0x1503d84: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0x1503d88: ldr             x1, [x1, #0x5a8]
    // 0x1503d8c: r2 = 0
    //     0x1503d8c: movz            x2, #0
    // 0x1503d90: r0 = AllocateArray()
    //     0x1503d90: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1503d94: b               #0x1503d9c
    // 0x1503d98: mov             x0, x1
    // 0x1503d9c: ldur            x2, [fp, #-8]
    // 0x1503da0: ldur            x3, [fp, #-0x10]
    // 0x1503da4: ldur            x1, [fp, #-0x18]
    // 0x1503da8: r4 = LoadClassIdInstr(r0)
    //     0x1503da8: ldur            x4, [x0, #-1]
    //     0x1503dac: ubfx            x4, x4, #0xc, #0x14
    // 0x1503db0: str             x0, [SP]
    // 0x1503db4: mov             x0, x4
    // 0x1503db8: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1503db8: movz            x17, #0xc898
    //     0x1503dbc: add             lr, x0, x17
    //     0x1503dc0: ldr             lr, [x21, lr, lsl #3]
    //     0x1503dc4: blr             lr
    // 0x1503dc8: r3 = LoadInt32Instr(r0)
    //     0x1503dc8: sbfx            x3, x0, #1, #0x1f
    // 0x1503dcc: ldur            x2, [fp, #-8]
    // 0x1503dd0: stur            x3, [fp, #-0x68]
    // 0x1503dd4: r1 = Function '<anonymous closure>':.
    //     0x1503dd4: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3af90] AnonymousClosure: (0x1503fa4), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x1505978)
    //     0x1503dd8: ldr             x1, [x1, #0xf90]
    // 0x1503ddc: r0 = AllocateClosure()
    //     0x1503ddc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1503de0: r1 = Function '<anonymous closure>':.
    //     0x1503de0: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3af98] AnonymousClosure: (0x15023c8), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x1505978)
    //     0x1503de4: ldr             x1, [x1, #0xf98]
    // 0x1503de8: r2 = Null
    //     0x1503de8: mov             x2, NULL
    // 0x1503dec: stur            x0, [fp, #-0x20]
    // 0x1503df0: r0 = AllocateClosure()
    //     0x1503df0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1503df4: stur            x0, [fp, #-0x28]
    // 0x1503df8: r0 = ListView()
    //     0x1503df8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1503dfc: stur            x0, [fp, #-0x30]
    // 0x1503e00: r16 = true
    //     0x1503e00: add             x16, NULL, #0x20  ; true
    // 0x1503e04: r30 = false
    //     0x1503e04: add             lr, NULL, #0x30  ; false
    // 0x1503e08: stp             lr, x16, [SP, #8]
    // 0x1503e0c: r16 = Instance_NeverScrollableScrollPhysics
    //     0x1503e0c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0x1503e10: ldr             x16, [x16, #0x1c8]
    // 0x1503e14: str             x16, [SP]
    // 0x1503e18: mov             x1, x0
    // 0x1503e1c: ldur            x2, [fp, #-0x20]
    // 0x1503e20: ldur            x3, [fp, #-0x68]
    // 0x1503e24: ldur            x5, [fp, #-0x28]
    // 0x1503e28: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x6, primary, 0x5, shrinkWrap, 0x4, null]
    //     0x1503e28: add             x4, PP, #0x34, lsl #12  ; [pp+0x34138] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x6, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0x1503e2c: ldr             x4, [x4, #0x138]
    // 0x1503e30: r0 = ListView.separated()
    //     0x1503e30: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x1503e34: r0 = Padding()
    //     0x1503e34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1503e38: mov             x2, x0
    // 0x1503e3c: r0 = Instance_EdgeInsets
    //     0x1503e3c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1503e40: ldr             x0, [x0, #0x1f0]
    // 0x1503e44: stur            x2, [fp, #-0x20]
    // 0x1503e48: StoreField: r2->field_f = r0
    //     0x1503e48: stur            w0, [x2, #0xf]
    // 0x1503e4c: ldur            x1, [fp, #-0x30]
    // 0x1503e50: StoreField: r2->field_b = r1
    //     0x1503e50: stur            w1, [x2, #0xb]
    // 0x1503e54: ldur            x1, [fp, #-8]
    // 0x1503e58: LoadField: r3 = r1->field_f
    //     0x1503e58: ldur            w3, [x1, #0xf]
    // 0x1503e5c: DecompressPointer r3
    //     0x1503e5c: add             x3, x3, HEAP, lsl #32
    // 0x1503e60: mov             x1, x3
    // 0x1503e64: r0 = controller()
    //     0x1503e64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1503e68: LoadField: r1 = r0->field_7b
    //     0x1503e68: ldur            w1, [x0, #0x7b]
    // 0x1503e6c: DecompressPointer r1
    //     0x1503e6c: add             x1, x1, HEAP, lsl #32
    // 0x1503e70: r0 = value()
    //     0x1503e70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1503e74: stur            x0, [fp, #-8]
    // 0x1503e78: r0 = PaymentTrustMarkers()
    //     0x1503e78: bl              #0xbc7fe4  ; AllocatePaymentTrustMarkersStub -> PaymentTrustMarkers (size=0x14)
    // 0x1503e7c: mov             x1, x0
    // 0x1503e80: r0 = true
    //     0x1503e80: add             x0, NULL, #0x20  ; true
    // 0x1503e84: stur            x1, [fp, #-0x28]
    // 0x1503e88: StoreField: r1->field_b = r0
    //     0x1503e88: stur            w0, [x1, #0xb]
    // 0x1503e8c: ldur            x0, [fp, #-8]
    // 0x1503e90: StoreField: r1->field_f = r0
    //     0x1503e90: stur            w0, [x1, #0xf]
    // 0x1503e94: r0 = Padding()
    //     0x1503e94: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1503e98: mov             x3, x0
    // 0x1503e9c: r0 = Instance_EdgeInsets
    //     0x1503e9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1503ea0: ldr             x0, [x0, #0x1f0]
    // 0x1503ea4: stur            x3, [fp, #-8]
    // 0x1503ea8: StoreField: r3->field_f = r0
    //     0x1503ea8: stur            w0, [x3, #0xf]
    // 0x1503eac: ldur            x0, [fp, #-0x28]
    // 0x1503eb0: StoreField: r3->field_b = r0
    //     0x1503eb0: stur            w0, [x3, #0xb]
    // 0x1503eb4: r1 = Null
    //     0x1503eb4: mov             x1, NULL
    // 0x1503eb8: r2 = 10
    //     0x1503eb8: movz            x2, #0xa
    // 0x1503ebc: r0 = AllocateArray()
    //     0x1503ebc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1503ec0: mov             x2, x0
    // 0x1503ec4: ldur            x0, [fp, #-0x10]
    // 0x1503ec8: stur            x2, [fp, #-0x28]
    // 0x1503ecc: StoreField: r2->field_f = r0
    //     0x1503ecc: stur            w0, [x2, #0xf]
    // 0x1503ed0: r16 = Instance_Divider
    //     0x1503ed0: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0x1503ed4: ldr             x16, [x16, #0x2e0]
    // 0x1503ed8: StoreField: r2->field_13 = r16
    //     0x1503ed8: stur            w16, [x2, #0x13]
    // 0x1503edc: ldur            x0, [fp, #-0x18]
    // 0x1503ee0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1503ee0: stur            w0, [x2, #0x17]
    // 0x1503ee4: ldur            x0, [fp, #-0x20]
    // 0x1503ee8: StoreField: r2->field_1b = r0
    //     0x1503ee8: stur            w0, [x2, #0x1b]
    // 0x1503eec: ldur            x0, [fp, #-8]
    // 0x1503ef0: StoreField: r2->field_1f = r0
    //     0x1503ef0: stur            w0, [x2, #0x1f]
    // 0x1503ef4: r1 = <Widget>
    //     0x1503ef4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1503ef8: r0 = AllocateGrowableArray()
    //     0x1503ef8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1503efc: mov             x1, x0
    // 0x1503f00: ldur            x0, [fp, #-0x28]
    // 0x1503f04: stur            x1, [fp, #-8]
    // 0x1503f08: StoreField: r1->field_f = r0
    //     0x1503f08: stur            w0, [x1, #0xf]
    // 0x1503f0c: r0 = 10
    //     0x1503f0c: movz            x0, #0xa
    // 0x1503f10: StoreField: r1->field_b = r0
    //     0x1503f10: stur            w0, [x1, #0xb]
    // 0x1503f14: r0 = ListView()
    //     0x1503f14: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1503f18: stur            x0, [fp, #-0x10]
    // 0x1503f1c: r16 = true
    //     0x1503f1c: add             x16, NULL, #0x20  ; true
    // 0x1503f20: r30 = false
    //     0x1503f20: add             lr, NULL, #0x30  ; false
    // 0x1503f24: stp             lr, x16, [SP, #8]
    // 0x1503f28: r16 = Instance_RangeMaintainingScrollPhysics
    //     0x1503f28: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3afa0] Obj!RangeMaintainingScrollPhysics@d55901
    //     0x1503f2c: ldr             x16, [x16, #0xfa0]
    // 0x1503f30: str             x16, [SP]
    // 0x1503f34: mov             x1, x0
    // 0x1503f38: ldur            x2, [fp, #-8]
    // 0x1503f3c: r4 = const [0, 0x5, 0x3, 0x2, physics, 0x4, primary, 0x3, shrinkWrap, 0x2, null]
    //     0x1503f3c: add             x4, PP, #0x3a, lsl #12  ; [pp+0x3afa8] List(11) [0, 0x5, 0x3, 0x2, "physics", 0x4, "primary", 0x3, "shrinkWrap", 0x2, Null]
    //     0x1503f40: ldr             x4, [x4, #0xfa8]
    // 0x1503f44: r0 = ListView()
    //     0x1503f44: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0x1503f48: ldur            x0, [fp, #-0x10]
    // 0x1503f4c: LeaveFrame
    //     0x1503f4c: mov             SP, fp
    //     0x1503f50: ldp             fp, lr, [SP], #0x10
    // 0x1503f54: ret
    //     0x1503f54: ret             
    // 0x1503f58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1503f58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1503f5c: b               #0x1502460
    // 0x1503f60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f70: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f78: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f80: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f84: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f88: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f8c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f90: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f94: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f98: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503f9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503f9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1503fa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1503fa0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x1503fa4, size: 0xd4
    // 0x1503fa4: EnterFrame
    //     0x1503fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x1503fa8: mov             fp, SP
    // 0x1503fac: AllocStack(0x8)
    //     0x1503fac: sub             SP, SP, #8
    // 0x1503fb0: SetupParameters()
    //     0x1503fb0: ldr             x0, [fp, #0x20]
    //     0x1503fb4: ldur            w1, [x0, #0x17]
    //     0x1503fb8: add             x1, x1, HEAP, lsl #32
    // 0x1503fbc: CheckStackOverflow
    //     0x1503fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1503fc0: cmp             SP, x16
    //     0x1503fc4: b.ls            #0x150406c
    // 0x1503fc8: LoadField: r0 = r1->field_f
    //     0x1503fc8: ldur            w0, [x1, #0xf]
    // 0x1503fcc: DecompressPointer r0
    //     0x1503fcc: add             x0, x0, HEAP, lsl #32
    // 0x1503fd0: mov             x1, x0
    // 0x1503fd4: stur            x0, [fp, #-8]
    // 0x1503fd8: r0 = controller()
    //     0x1503fd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1503fdc: LoadField: r1 = r0->field_53
    //     0x1503fdc: ldur            w1, [x0, #0x53]
    // 0x1503fe0: DecompressPointer r1
    //     0x1503fe0: add             x1, x1, HEAP, lsl #32
    // 0x1503fe4: r0 = value()
    //     0x1503fe4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1503fe8: LoadField: r2 = r0->field_1f
    //     0x1503fe8: ldur            w2, [x0, #0x1f]
    // 0x1503fec: DecompressPointer r2
    //     0x1503fec: add             x2, x2, HEAP, lsl #32
    // 0x1503ff0: cmp             w2, NULL
    // 0x1503ff4: b.ne            #0x1504004
    // 0x1503ff8: ldr             x3, [fp, #0x10]
    // 0x1503ffc: r2 = Null
    //     0x1503ffc: mov             x2, NULL
    // 0x1504000: b               #0x1504044
    // 0x1504004: ldr             x3, [fp, #0x10]
    // 0x1504008: LoadField: r0 = r2->field_b
    //     0x1504008: ldur            w0, [x2, #0xb]
    // 0x150400c: r4 = LoadInt32Instr(r3)
    //     0x150400c: sbfx            x4, x3, #1, #0x1f
    //     0x1504010: tbz             w3, #0, #0x1504018
    //     0x1504014: ldur            x4, [x3, #7]
    // 0x1504018: r1 = LoadInt32Instr(r0)
    //     0x1504018: sbfx            x1, x0, #1, #0x1f
    // 0x150401c: mov             x0, x1
    // 0x1504020: mov             x1, x4
    // 0x1504024: cmp             x1, x0
    // 0x1504028: b.hs            #0x1504074
    // 0x150402c: LoadField: r0 = r2->field_f
    //     0x150402c: ldur            w0, [x2, #0xf]
    // 0x1504030: DecompressPointer r0
    //     0x1504030: add             x0, x0, HEAP, lsl #32
    // 0x1504034: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x1504034: add             x16, x0, x4, lsl #2
    //     0x1504038: ldur            w1, [x16, #0xf]
    // 0x150403c: DecompressPointer r1
    //     0x150403c: add             x1, x1, HEAP, lsl #32
    // 0x1504040: mov             x2, x1
    // 0x1504044: r0 = LoadInt32Instr(r3)
    //     0x1504044: sbfx            x0, x3, #1, #0x1f
    //     0x1504048: tbz             w3, #0, #0x1504050
    //     0x150404c: ldur            x0, [x3, #7]
    // 0x1504050: ldur            x1, [fp, #-8]
    // 0x1504054: mov             x3, x0
    // 0x1504058: ldr             x5, [fp, #0x18]
    // 0x150405c: r0 = lineThemePaymentMethodCard()
    //     0x150405c: bl              #0x13c1fc4  ; [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::lineThemePaymentMethodCard
    // 0x1504060: LeaveFrame
    //     0x1504060: mov             SP, fp
    //     0x1504064: ldp             fp, lr, [SP], #0x10
    // 0x1504068: ret
    //     0x1504068: ret             
    // 0x150406c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150406c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1504070: b               #0x1503fc8
    // 0x1504074: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1504074: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ body(/* No info */) {
    // ** addr: 0x1505978, size: 0x64
    // 0x1505978: EnterFrame
    //     0x1505978: stp             fp, lr, [SP, #-0x10]!
    //     0x150597c: mov             fp, SP
    // 0x1505980: AllocStack(0x18)
    //     0x1505980: sub             SP, SP, #0x18
    // 0x1505984: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1505984: stur            x1, [fp, #-8]
    //     0x1505988: stur            x2, [fp, #-0x10]
    // 0x150598c: r1 = 2
    //     0x150598c: movz            x1, #0x2
    // 0x1505990: r0 = AllocateContext()
    //     0x1505990: bl              #0x16f6108  ; AllocateContextStub
    // 0x1505994: mov             x1, x0
    // 0x1505998: ldur            x0, [fp, #-8]
    // 0x150599c: stur            x1, [fp, #-0x18]
    // 0x15059a0: StoreField: r1->field_f = r0
    //     0x15059a0: stur            w0, [x1, #0xf]
    // 0x15059a4: ldur            x0, [fp, #-0x10]
    // 0x15059a8: StoreField: r1->field_13 = r0
    //     0x15059a8: stur            w0, [x1, #0x13]
    // 0x15059ac: r0 = Obx()
    //     0x15059ac: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15059b0: ldur            x2, [fp, #-0x18]
    // 0x15059b4: r1 = Function '<anonymous closure>':.
    //     0x15059b4: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3af68] AnonymousClosure: (0x1502438), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::body (0x1505978)
    //     0x15059b8: ldr             x1, [x1, #0xf68]
    // 0x15059bc: stur            x0, [fp, #-8]
    // 0x15059c0: r0 = AllocateClosure()
    //     0x15059c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15059c4: mov             x1, x0
    // 0x15059c8: ldur            x0, [fp, #-8]
    // 0x15059cc: StoreField: r0->field_b = r1
    //     0x15059cc: stur            w1, [x0, #0xb]
    // 0x15059d0: LeaveFrame
    //     0x15059d0: mov             SP, fp
    //     0x15059d4: ldp             fp, lr, [SP], #0x10
    // 0x15059d8: ret
    //     0x15059d8: ret             
  }
  [closure] bool <anonymous closure>(dynamic, Route<dynamic>) {
    // ** addr: 0x15cf754, size: 0x70
    // 0x15cf754: EnterFrame
    //     0x15cf754: stp             fp, lr, [SP, #-0x10]!
    //     0x15cf758: mov             fp, SP
    // 0x15cf75c: AllocStack(0x10)
    //     0x15cf75c: sub             SP, SP, #0x10
    // 0x15cf760: CheckStackOverflow
    //     0x15cf760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cf764: cmp             SP, x16
    //     0x15cf768: b.ls            #0x15cf7bc
    // 0x15cf76c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cf76c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cf770: ldr             x0, [x0, #0x1c80]
    //     0x15cf774: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cf778: cmp             w0, w16
    //     0x15cf77c: b.ne            #0x15cf788
    //     0x15cf780: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cf784: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cf788: r0 = GetNavigation.currentRoute()
    //     0x15cf788: bl              #0x8a583c  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.currentRoute
    // 0x15cf78c: r1 = LoadClassIdInstr(r0)
    //     0x15cf78c: ldur            x1, [x0, #-1]
    //     0x15cf790: ubfx            x1, x1, #0xc, #0x14
    // 0x15cf794: r16 = "/exchange-checkout"
    //     0x15cf794: add             x16, PP, #0xd, lsl #12  ; [pp+0xd988] "/exchange-checkout"
    //     0x15cf798: ldr             x16, [x16, #0x988]
    // 0x15cf79c: stp             x16, x0, [SP]
    // 0x15cf7a0: mov             x0, x1
    // 0x15cf7a4: mov             lr, x0
    // 0x15cf7a8: ldr             lr, [x21, lr, lsl #3]
    // 0x15cf7ac: blr             lr
    // 0x15cf7b0: LeaveFrame
    //     0x15cf7b0: mov             SP, fp
    //     0x15cf7b4: ldp             fp, lr, [SP], #0x10
    // 0x15cf7b8: ret
    //     0x15cf7b8: ret             
    // 0x15cf7bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cf7bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cf7c0: b               #0x15cf76c
  }
  _ onBackPress(/* No info */) {
    // ** addr: 0x15cf7c4, size: 0xbc
    // 0x15cf7c4: EnterFrame
    //     0x15cf7c4: stp             fp, lr, [SP, #-0x10]!
    //     0x15cf7c8: mov             fp, SP
    // 0x15cf7cc: AllocStack(0x8)
    //     0x15cf7cc: sub             SP, SP, #8
    // 0x15cf7d0: CheckStackOverflow
    //     0x15cf7d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15cf7d4: cmp             SP, x16
    //     0x15cf7d8: b.ls            #0x15cf878
    // 0x15cf7dc: r0 = controller()
    //     0x15cf7dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15cf7e0: LoadField: r1 = r0->field_77
    //     0x15cf7e0: ldur            w1, [x0, #0x77]
    // 0x15cf7e4: DecompressPointer r1
    //     0x15cf7e4: add             x1, x1, HEAP, lsl #32
    // 0x15cf7e8: r2 = ""
    //     0x15cf7e8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15cf7ec: r0 = value=()
    //     0x15cf7ec: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x15cf7f0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15cf7f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cf7f4: ldr             x0, [x0, #0x1c80]
    //     0x15cf7f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cf7fc: cmp             w0, w16
    //     0x15cf800: b.ne            #0x15cf80c
    //     0x15cf804: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15cf808: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15cf80c: r1 = Function '<anonymous closure>':.
    //     0x15cf80c: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3aff8] AnonymousClosure: (0x15cf754), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::onBackPress (0x15cf7c4)
    //     0x15cf810: ldr             x1, [x1, #0xff8]
    // 0x15cf814: r2 = Null
    //     0x15cf814: mov             x2, NULL
    // 0x15cf818: r0 = AllocateClosure()
    //     0x15cf818: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15cf81c: mov             x1, x0
    // 0x15cf820: r0 = GetNavigation.until()
    //     0x15cf820: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x15cf824: r1 = <bool>
    //     0x15cf824: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x15cf828: r0 = _Future()
    //     0x15cf828: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x15cf82c: stur            x0, [fp, #-8]
    // 0x15cf830: StoreField: r0->field_b = rZR
    //     0x15cf830: stur            xzr, [x0, #0xb]
    // 0x15cf834: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x15cf834: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15cf838: ldr             x0, [x0, #0x778]
    //     0x15cf83c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15cf840: cmp             w0, w16
    //     0x15cf844: b.ne            #0x15cf850
    //     0x15cf848: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x15cf84c: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x15cf850: mov             x1, x0
    // 0x15cf854: ldur            x0, [fp, #-8]
    // 0x15cf858: StoreField: r0->field_13 = r1
    //     0x15cf858: stur            w1, [x0, #0x13]
    // 0x15cf85c: mov             x1, x0
    // 0x15cf860: r2 = true
    //     0x15cf860: add             x2, NULL, #0x20  ; true
    // 0x15cf864: r0 = _asyncComplete()
    //     0x15cf864: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x15cf868: ldur            x0, [fp, #-8]
    // 0x15cf86c: LeaveFrame
    //     0x15cf86c: mov             SP, fp
    //     0x15cf870: ldp             fp, lr, [SP], #0x10
    // 0x15cf874: ret
    //     0x15cf874: ret             
    // 0x15cf878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15cf878: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15cf87c: b               #0x15cf7dc
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e9bb0, size: 0x18c
    // 0x15e9bb0: EnterFrame
    //     0x15e9bb0: stp             fp, lr, [SP, #-0x10]!
    //     0x15e9bb4: mov             fp, SP
    // 0x15e9bb8: AllocStack(0x28)
    //     0x15e9bb8: sub             SP, SP, #0x28
    // 0x15e9bbc: SetupParameters(ExchangeCheckoutOnlinePaymentMethod this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e9bbc: mov             x0, x1
    //     0x15e9bc0: stur            x1, [fp, #-8]
    //     0x15e9bc4: mov             x1, x2
    //     0x15e9bc8: stur            x2, [fp, #-0x10]
    // 0x15e9bcc: CheckStackOverflow
    //     0x15e9bcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e9bd0: cmp             SP, x16
    //     0x15e9bd4: b.ls            #0x15e9d34
    // 0x15e9bd8: r1 = 2
    //     0x15e9bd8: movz            x1, #0x2
    // 0x15e9bdc: r0 = AllocateContext()
    //     0x15e9bdc: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e9be0: mov             x1, x0
    // 0x15e9be4: ldur            x0, [fp, #-8]
    // 0x15e9be8: stur            x1, [fp, #-0x18]
    // 0x15e9bec: StoreField: r1->field_f = r0
    //     0x15e9bec: stur            w0, [x1, #0xf]
    // 0x15e9bf0: ldur            x0, [fp, #-0x10]
    // 0x15e9bf4: StoreField: r1->field_13 = r0
    //     0x15e9bf4: stur            w0, [x1, #0x13]
    // 0x15e9bf8: r0 = Obx()
    //     0x15e9bf8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e9bfc: ldur            x2, [fp, #-0x18]
    // 0x15e9c00: r1 = Function '<anonymous closure>':.
    //     0x15e9c00: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3afe8] AnonymousClosure: (0x15cf880), in [package:customer_app/app/presentation/views/line/login/login_view.dart] LoginView::appBar (0x15ea93c)
    //     0x15e9c04: ldr             x1, [x1, #0xfe8]
    // 0x15e9c08: stur            x0, [fp, #-8]
    // 0x15e9c0c: r0 = AllocateClosure()
    //     0x15e9c0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e9c10: mov             x1, x0
    // 0x15e9c14: ldur            x0, [fp, #-8]
    // 0x15e9c18: StoreField: r0->field_b = r1
    //     0x15e9c18: stur            w1, [x0, #0xb]
    // 0x15e9c1c: ldur            x1, [fp, #-0x10]
    // 0x15e9c20: r0 = of()
    //     0x15e9c20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e9c24: LoadField: r1 = r0->field_5b
    //     0x15e9c24: ldur            w1, [x0, #0x5b]
    // 0x15e9c28: DecompressPointer r1
    //     0x15e9c28: add             x1, x1, HEAP, lsl #32
    // 0x15e9c2c: stur            x1, [fp, #-0x10]
    // 0x15e9c30: r0 = ColorFilter()
    //     0x15e9c30: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e9c34: mov             x1, x0
    // 0x15e9c38: ldur            x0, [fp, #-0x10]
    // 0x15e9c3c: stur            x1, [fp, #-0x20]
    // 0x15e9c40: StoreField: r1->field_7 = r0
    //     0x15e9c40: stur            w0, [x1, #7]
    // 0x15e9c44: r0 = Instance_BlendMode
    //     0x15e9c44: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e9c48: ldr             x0, [x0, #0xb30]
    // 0x15e9c4c: StoreField: r1->field_b = r0
    //     0x15e9c4c: stur            w0, [x1, #0xb]
    // 0x15e9c50: r0 = 1
    //     0x15e9c50: movz            x0, #0x1
    // 0x15e9c54: StoreField: r1->field_13 = r0
    //     0x15e9c54: stur            x0, [x1, #0x13]
    // 0x15e9c58: r0 = SvgPicture()
    //     0x15e9c58: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e9c5c: stur            x0, [fp, #-0x10]
    // 0x15e9c60: ldur            x16, [fp, #-0x20]
    // 0x15e9c64: str             x16, [SP]
    // 0x15e9c68: mov             x1, x0
    // 0x15e9c6c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e9c6c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e9c70: ldr             x2, [x2, #0xa40]
    // 0x15e9c74: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e9c74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e9c78: ldr             x4, [x4, #0xa38]
    // 0x15e9c7c: r0 = SvgPicture.asset()
    //     0x15e9c7c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e9c80: r0 = Align()
    //     0x15e9c80: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e9c84: mov             x1, x0
    // 0x15e9c88: r0 = Instance_Alignment
    //     0x15e9c88: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e9c8c: ldr             x0, [x0, #0xb10]
    // 0x15e9c90: stur            x1, [fp, #-0x20]
    // 0x15e9c94: StoreField: r1->field_f = r0
    //     0x15e9c94: stur            w0, [x1, #0xf]
    // 0x15e9c98: r0 = 1.000000
    //     0x15e9c98: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e9c9c: StoreField: r1->field_13 = r0
    //     0x15e9c9c: stur            w0, [x1, #0x13]
    // 0x15e9ca0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e9ca0: stur            w0, [x1, #0x17]
    // 0x15e9ca4: ldur            x0, [fp, #-0x10]
    // 0x15e9ca8: StoreField: r1->field_b = r0
    //     0x15e9ca8: stur            w0, [x1, #0xb]
    // 0x15e9cac: r0 = InkWell()
    //     0x15e9cac: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e9cb0: mov             x3, x0
    // 0x15e9cb4: ldur            x0, [fp, #-0x20]
    // 0x15e9cb8: stur            x3, [fp, #-0x10]
    // 0x15e9cbc: StoreField: r3->field_b = r0
    //     0x15e9cbc: stur            w0, [x3, #0xb]
    // 0x15e9cc0: ldur            x2, [fp, #-0x18]
    // 0x15e9cc4: r1 = Function '<anonymous closure>':.
    //     0x15e9cc4: add             x1, PP, #0x3a, lsl #12  ; [pp+0x3aff0] AnonymousClosure: (0x15e9d3c), in [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::appBar (0x15e9bb0)
    //     0x15e9cc8: ldr             x1, [x1, #0xff0]
    // 0x15e9ccc: r0 = AllocateClosure()
    //     0x15e9ccc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e9cd0: ldur            x2, [fp, #-0x10]
    // 0x15e9cd4: StoreField: r2->field_f = r0
    //     0x15e9cd4: stur            w0, [x2, #0xf]
    // 0x15e9cd8: r0 = true
    //     0x15e9cd8: add             x0, NULL, #0x20  ; true
    // 0x15e9cdc: StoreField: r2->field_43 = r0
    //     0x15e9cdc: stur            w0, [x2, #0x43]
    // 0x15e9ce0: r1 = Instance_BoxShape
    //     0x15e9ce0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e9ce4: ldr             x1, [x1, #0x80]
    // 0x15e9ce8: StoreField: r2->field_47 = r1
    //     0x15e9ce8: stur            w1, [x2, #0x47]
    // 0x15e9cec: StoreField: r2->field_6f = r0
    //     0x15e9cec: stur            w0, [x2, #0x6f]
    // 0x15e9cf0: r1 = false
    //     0x15e9cf0: add             x1, NULL, #0x30  ; false
    // 0x15e9cf4: StoreField: r2->field_73 = r1
    //     0x15e9cf4: stur            w1, [x2, #0x73]
    // 0x15e9cf8: StoreField: r2->field_83 = r0
    //     0x15e9cf8: stur            w0, [x2, #0x83]
    // 0x15e9cfc: StoreField: r2->field_7b = r1
    //     0x15e9cfc: stur            w1, [x2, #0x7b]
    // 0x15e9d00: r0 = AppBar()
    //     0x15e9d00: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e9d04: stur            x0, [fp, #-0x18]
    // 0x15e9d08: ldur            x16, [fp, #-8]
    // 0x15e9d0c: str             x16, [SP]
    // 0x15e9d10: mov             x1, x0
    // 0x15e9d14: ldur            x2, [fp, #-0x10]
    // 0x15e9d18: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e9d18: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e9d1c: ldr             x4, [x4, #0xf00]
    // 0x15e9d20: r0 = AppBar()
    //     0x15e9d20: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e9d24: ldur            x0, [fp, #-0x18]
    // 0x15e9d28: LeaveFrame
    //     0x15e9d28: mov             SP, fp
    //     0x15e9d2c: ldp             fp, lr, [SP], #0x10
    // 0x15e9d30: ret
    //     0x15e9d30: ret             
    // 0x15e9d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e9d34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e9d38: b               #0x15e9bd8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e9d3c, size: 0x48
    // 0x15e9d3c: EnterFrame
    //     0x15e9d3c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e9d40: mov             fp, SP
    // 0x15e9d44: ldr             x0, [fp, #0x10]
    // 0x15e9d48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15e9d48: ldur            w1, [x0, #0x17]
    // 0x15e9d4c: DecompressPointer r1
    //     0x15e9d4c: add             x1, x1, HEAP, lsl #32
    // 0x15e9d50: CheckStackOverflow
    //     0x15e9d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e9d54: cmp             SP, x16
    //     0x15e9d58: b.ls            #0x15e9d7c
    // 0x15e9d5c: LoadField: r0 = r1->field_f
    //     0x15e9d5c: ldur            w0, [x1, #0xf]
    // 0x15e9d60: DecompressPointer r0
    //     0x15e9d60: add             x0, x0, HEAP, lsl #32
    // 0x15e9d64: mov             x1, x0
    // 0x15e9d68: r0 = onBackPress()
    //     0x15e9d68: bl              #0x15cf7c4  ; [package:customer_app/app/presentation/views/line/exchange/exchange_checkout_online_payment_method.dart] ExchangeCheckoutOnlinePaymentMethod::onBackPress
    // 0x15e9d6c: r0 = Null
    //     0x15e9d6c: mov             x0, NULL
    // 0x15e9d70: LeaveFrame
    //     0x15e9d70: mov             SP, fp
    //     0x15e9d74: ldp             fp, lr, [SP], #0x10
    // 0x15e9d78: ret
    //     0x15e9d78: ret             
    // 0x15e9d7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e9d7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e9d80: b               #0x15e9d5c
  }
}
