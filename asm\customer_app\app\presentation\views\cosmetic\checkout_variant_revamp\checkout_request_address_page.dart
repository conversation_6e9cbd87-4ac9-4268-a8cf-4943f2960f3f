// lib: , url: package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart

// class id: 1049232, size: 0x8
class :: {
}

// class id: 4611, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestAddressPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1349318, size: 0x64
    // 0x1349318: EnterFrame
    //     0x1349318: stp             fp, lr, [SP, #-0x10]!
    //     0x134931c: mov             fp, SP
    // 0x1349320: AllocStack(0x18)
    //     0x1349320: sub             SP, SP, #0x18
    // 0x1349324: SetupParameters(CheckoutRequestAddressPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1349324: stur            x1, [fp, #-8]
    //     0x1349328: stur            x2, [fp, #-0x10]
    // 0x134932c: r1 = 2
    //     0x134932c: movz            x1, #0x2
    // 0x1349330: r0 = AllocateContext()
    //     0x1349330: bl              #0x16f6108  ; AllocateContextStub
    // 0x1349334: mov             x1, x0
    // 0x1349338: ldur            x0, [fp, #-8]
    // 0x134933c: stur            x1, [fp, #-0x18]
    // 0x1349340: StoreField: r1->field_f = r0
    //     0x1349340: stur            w0, [x1, #0xf]
    // 0x1349344: ldur            x0, [fp, #-0x10]
    // 0x1349348: StoreField: r1->field_13 = r0
    //     0x1349348: stur            w0, [x1, #0x13]
    // 0x134934c: r0 = Obx()
    //     0x134934c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1349350: ldur            x2, [fp, #-0x18]
    // 0x1349354: r1 = Function '<anonymous closure>':.
    //     0x1349354: add             x1, PP, #0x44, lsl #12  ; [pp+0x44250] AnonymousClosure: (0x134937c), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::bottomNavigationBar (0x1349318)
    //     0x1349358: ldr             x1, [x1, #0x250]
    // 0x134935c: stur            x0, [fp, #-8]
    // 0x1349360: r0 = AllocateClosure()
    //     0x1349360: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1349364: mov             x1, x0
    // 0x1349368: ldur            x0, [fp, #-8]
    // 0x134936c: StoreField: r0->field_b = r1
    //     0x134936c: stur            w1, [x0, #0xb]
    // 0x1349370: LeaveFrame
    //     0x1349370: mov             SP, fp
    //     0x1349374: ldp             fp, lr, [SP], #0x10
    // 0x1349378: ret
    //     0x1349378: ret             
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x134937c, size: 0x5a8
    // 0x134937c: EnterFrame
    //     0x134937c: stp             fp, lr, [SP, #-0x10]!
    //     0x1349380: mov             fp, SP
    // 0x1349384: AllocStack(0x58)
    //     0x1349384: sub             SP, SP, #0x58
    // 0x1349388: SetupParameters()
    //     0x1349388: ldr             x0, [fp, #0x10]
    //     0x134938c: ldur            w2, [x0, #0x17]
    //     0x1349390: add             x2, x2, HEAP, lsl #32
    //     0x1349394: stur            x2, [fp, #-8]
    // 0x1349398: CheckStackOverflow
    //     0x1349398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x134939c: cmp             SP, x16
    //     0x13493a0: b.ls            #0x13498e0
    // 0x13493a4: LoadField: r1 = r2->field_13
    //     0x13493a4: ldur            w1, [x2, #0x13]
    // 0x13493a8: DecompressPointer r1
    //     0x13493a8: add             x1, x1, HEAP, lsl #32
    // 0x13493ac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13493ac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13493b0: r0 = _of()
    //     0x13493b0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x13493b4: LoadField: r1 = r0->field_23
    //     0x13493b4: ldur            w1, [x0, #0x23]
    // 0x13493b8: DecompressPointer r1
    //     0x13493b8: add             x1, x1, HEAP, lsl #32
    // 0x13493bc: LoadField: d0 = r1->field_1f
    //     0x13493bc: ldur            d0, [x1, #0x1f]
    // 0x13493c0: stur            d0, [fp, #-0x38]
    // 0x13493c4: r0 = EdgeInsets()
    //     0x13493c4: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x13493c8: stur            x0, [fp, #-0x10]
    // 0x13493cc: StoreField: r0->field_7 = rZR
    //     0x13493cc: stur            xzr, [x0, #7]
    // 0x13493d0: StoreField: r0->field_f = rZR
    //     0x13493d0: stur            xzr, [x0, #0xf]
    // 0x13493d4: ArrayStore: r0[0] = rZR  ; List_8
    //     0x13493d4: stur            xzr, [x0, #0x17]
    // 0x13493d8: ldur            d0, [fp, #-0x38]
    // 0x13493dc: StoreField: r0->field_1f = d0
    //     0x13493dc: stur            d0, [x0, #0x1f]
    // 0x13493e0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13493e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13493e4: ldr             x0, [x0, #0x1c80]
    //     0x13493e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13493ec: cmp             w0, w16
    //     0x13493f0: b.ne            #0x13493fc
    //     0x13493f4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13493f8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13493fc: r0 = GetNavigation.size()
    //     0x13493fc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1349400: LoadField: d0 = r0->field_7
    //     0x1349400: ldur            d0, [x0, #7]
    // 0x1349404: stur            d0, [fp, #-0x38]
    // 0x1349408: r16 = <EdgeInsets>
    //     0x1349408: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x134940c: ldr             x16, [x16, #0xda0]
    // 0x1349410: r30 = Instance_EdgeInsets
    //     0x1349410: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1349414: ldr             lr, [lr, #0x1f0]
    // 0x1349418: stp             lr, x16, [SP]
    // 0x134941c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x134941c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1349420: r0 = all()
    //     0x1349420: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1349424: ldur            x2, [fp, #-8]
    // 0x1349428: stur            x0, [fp, #-0x18]
    // 0x134942c: LoadField: r1 = r2->field_f
    //     0x134942c: ldur            w1, [x2, #0xf]
    // 0x1349430: DecompressPointer r1
    //     0x1349430: add             x1, x1, HEAP, lsl #32
    // 0x1349434: r0 = controller()
    //     0x1349434: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1349438: LoadField: r1 = r0->field_9f
    //     0x1349438: ldur            w1, [x0, #0x9f]
    // 0x134943c: DecompressPointer r1
    //     0x134943c: add             x1, x1, HEAP, lsl #32
    // 0x1349440: r0 = value()
    //     0x1349440: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1349444: tbnz            w0, #4, #0x1349464
    // 0x1349448: ldur            x2, [fp, #-8]
    // 0x134944c: LoadField: r1 = r2->field_13
    //     0x134944c: ldur            w1, [x2, #0x13]
    // 0x1349450: DecompressPointer r1
    //     0x1349450: add             x1, x1, HEAP, lsl #32
    // 0x1349454: r0 = of()
    //     0x1349454: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1349458: LoadField: r1 = r0->field_5b
    //     0x1349458: ldur            w1, [x0, #0x5b]
    // 0x134945c: DecompressPointer r1
    //     0x134945c: add             x1, x1, HEAP, lsl #32
    // 0x1349460: b               #0x134946c
    // 0x1349464: r1 = Instance_MaterialColor
    //     0x1349464: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x1349468: ldr             x1, [x1, #0xdc0]
    // 0x134946c: ldur            x2, [fp, #-8]
    // 0x1349470: ldur            x0, [fp, #-0x18]
    // 0x1349474: r16 = <Color>
    //     0x1349474: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1349478: ldr             x16, [x16, #0xf80]
    // 0x134947c: stp             x1, x16, [SP]
    // 0x1349480: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1349480: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1349484: r0 = all()
    //     0x1349484: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1349488: stur            x0, [fp, #-0x20]
    // 0x134948c: r16 = <RoundedRectangleBorder>
    //     0x134948c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1349490: ldr             x16, [x16, #0xf78]
    // 0x1349494: r30 = Instance_RoundedRectangleBorder
    //     0x1349494: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x1349498: ldr             lr, [lr, #0x888]
    // 0x134949c: stp             lr, x16, [SP]
    // 0x13494a0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13494a0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13494a4: r0 = all()
    //     0x13494a4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13494a8: stur            x0, [fp, #-0x28]
    // 0x13494ac: r0 = ButtonStyle()
    //     0x13494ac: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13494b0: mov             x1, x0
    // 0x13494b4: ldur            x0, [fp, #-0x20]
    // 0x13494b8: stur            x1, [fp, #-0x30]
    // 0x13494bc: StoreField: r1->field_b = r0
    //     0x13494bc: stur            w0, [x1, #0xb]
    // 0x13494c0: ldur            x0, [fp, #-0x18]
    // 0x13494c4: StoreField: r1->field_23 = r0
    //     0x13494c4: stur            w0, [x1, #0x23]
    // 0x13494c8: ldur            x0, [fp, #-0x28]
    // 0x13494cc: StoreField: r1->field_43 = r0
    //     0x13494cc: stur            w0, [x1, #0x43]
    // 0x13494d0: r0 = TextButtonThemeData()
    //     0x13494d0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x13494d4: mov             x2, x0
    // 0x13494d8: ldur            x0, [fp, #-0x30]
    // 0x13494dc: stur            x2, [fp, #-0x18]
    // 0x13494e0: StoreField: r2->field_7 = r0
    //     0x13494e0: stur            w0, [x2, #7]
    // 0x13494e4: ldur            x0, [fp, #-8]
    // 0x13494e8: LoadField: r1 = r0->field_f
    //     0x13494e8: ldur            w1, [x0, #0xf]
    // 0x13494ec: DecompressPointer r1
    //     0x13494ec: add             x1, x1, HEAP, lsl #32
    // 0x13494f0: r0 = controller()
    //     0x13494f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13494f4: LoadField: r1 = r0->field_9f
    //     0x13494f4: ldur            w1, [x0, #0x9f]
    // 0x13494f8: DecompressPointer r1
    //     0x13494f8: add             x1, x1, HEAP, lsl #32
    // 0x13494fc: r0 = value()
    //     0x13494fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1349500: tbnz            w0, #4, #0x134951c
    // 0x1349504: ldur            x2, [fp, #-8]
    // 0x1349508: r1 = Function '<anonymous closure>':.
    //     0x1349508: add             x1, PP, #0x44, lsl #12  ; [pp+0x44258] AnonymousClosure: (0x12e5430), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::bottomNavigationBar (0x1365a7c)
    //     0x134950c: ldr             x1, [x1, #0x258]
    // 0x1349510: r0 = AllocateClosure()
    //     0x1349510: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1349514: mov             x4, x0
    // 0x1349518: b               #0x1349520
    // 0x134951c: r4 = Null
    //     0x134951c: mov             x4, NULL
    // 0x1349520: ldur            x2, [fp, #-8]
    // 0x1349524: ldur            x3, [fp, #-0x10]
    // 0x1349528: ldur            x0, [fp, #-0x18]
    // 0x134952c: ldur            d0, [fp, #-0x38]
    // 0x1349530: stur            x4, [fp, #-0x20]
    // 0x1349534: LoadField: r1 = r2->field_13
    //     0x1349534: ldur            w1, [x2, #0x13]
    // 0x1349538: DecompressPointer r1
    //     0x1349538: add             x1, x1, HEAP, lsl #32
    // 0x134953c: r0 = of()
    //     0x134953c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1349540: LoadField: r1 = r0->field_87
    //     0x1349540: ldur            w1, [x0, #0x87]
    // 0x1349544: DecompressPointer r1
    //     0x1349544: add             x1, x1, HEAP, lsl #32
    // 0x1349548: LoadField: r0 = r1->field_7
    //     0x1349548: ldur            w0, [x1, #7]
    // 0x134954c: DecompressPointer r0
    //     0x134954c: add             x0, x0, HEAP, lsl #32
    // 0x1349550: r16 = 14.000000
    //     0x1349550: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1349554: ldr             x16, [x16, #0x1d8]
    // 0x1349558: r30 = Instance_Color
    //     0x1349558: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x134955c: stp             lr, x16, [SP]
    // 0x1349560: mov             x1, x0
    // 0x1349564: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1349564: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1349568: ldr             x4, [x4, #0xaa0]
    // 0x134956c: r0 = copyWith()
    //     0x134956c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1349570: stur            x0, [fp, #-0x28]
    // 0x1349574: r0 = Text()
    //     0x1349574: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1349578: mov             x1, x0
    // 0x134957c: r0 = "Continue"
    //     0x134957c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x1349580: ldr             x0, [x0, #0xfe0]
    // 0x1349584: stur            x1, [fp, #-0x30]
    // 0x1349588: StoreField: r1->field_b = r0
    //     0x1349588: stur            w0, [x1, #0xb]
    // 0x134958c: ldur            x0, [fp, #-0x28]
    // 0x1349590: StoreField: r1->field_13 = r0
    //     0x1349590: stur            w0, [x1, #0x13]
    // 0x1349594: r0 = TextButton()
    //     0x1349594: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1349598: mov             x1, x0
    // 0x134959c: ldur            x0, [fp, #-0x20]
    // 0x13495a0: stur            x1, [fp, #-0x28]
    // 0x13495a4: StoreField: r1->field_b = r0
    //     0x13495a4: stur            w0, [x1, #0xb]
    // 0x13495a8: r0 = false
    //     0x13495a8: add             x0, NULL, #0x30  ; false
    // 0x13495ac: StoreField: r1->field_27 = r0
    //     0x13495ac: stur            w0, [x1, #0x27]
    // 0x13495b0: r2 = true
    //     0x13495b0: add             x2, NULL, #0x20  ; true
    // 0x13495b4: StoreField: r1->field_2f = r2
    //     0x13495b4: stur            w2, [x1, #0x2f]
    // 0x13495b8: ldur            x3, [fp, #-0x30]
    // 0x13495bc: StoreField: r1->field_37 = r3
    //     0x13495bc: stur            w3, [x1, #0x37]
    // 0x13495c0: r0 = TextButtonTheme()
    //     0x13495c0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13495c4: mov             x1, x0
    // 0x13495c8: ldur            x0, [fp, #-0x18]
    // 0x13495cc: stur            x1, [fp, #-0x20]
    // 0x13495d0: StoreField: r1->field_f = r0
    //     0x13495d0: stur            w0, [x1, #0xf]
    // 0x13495d4: ldur            x0, [fp, #-0x28]
    // 0x13495d8: StoreField: r1->field_b = r0
    //     0x13495d8: stur            w0, [x1, #0xb]
    // 0x13495dc: ldur            d0, [fp, #-0x38]
    // 0x13495e0: r0 = inline_Allocate_Double()
    //     0x13495e0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x13495e4: add             x0, x0, #0x10
    //     0x13495e8: cmp             x2, x0
    //     0x13495ec: b.ls            #0x13498e8
    //     0x13495f0: str             x0, [THR, #0x50]  ; THR::top
    //     0x13495f4: sub             x0, x0, #0xf
    //     0x13495f8: movz            x2, #0xe15c
    //     0x13495fc: movk            x2, #0x3, lsl #16
    //     0x1349600: stur            x2, [x0, #-1]
    // 0x1349604: StoreField: r0->field_7 = d0
    //     0x1349604: stur            d0, [x0, #7]
    // 0x1349608: stur            x0, [fp, #-0x18]
    // 0x134960c: r0 = Container()
    //     0x134960c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1349610: stur            x0, [fp, #-0x28]
    // 0x1349614: ldur            x16, [fp, #-0x18]
    // 0x1349618: r30 = Instance_EdgeInsets
    //     0x1349618: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0x134961c: ldr             lr, [lr, #0xe68]
    // 0x1349620: stp             lr, x16, [SP, #8]
    // 0x1349624: ldur            x16, [fp, #-0x20]
    // 0x1349628: str             x16, [SP]
    // 0x134962c: mov             x1, x0
    // 0x1349630: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x2, width, 0x1, null]
    //     0x1349630: add             x4, PP, #0x44, lsl #12  ; [pp+0x44260] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0x1349634: ldr             x4, [x4, #0x260]
    // 0x1349638: r0 = Container()
    //     0x1349638: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x134963c: r0 = GetNavigation.size()
    //     0x134963c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1349640: LoadField: d0 = r0->field_7
    //     0x1349640: ldur            d0, [x0, #7]
    // 0x1349644: ldur            x0, [fp, #-8]
    // 0x1349648: stur            d0, [fp, #-0x38]
    // 0x134964c: LoadField: r1 = r0->field_13
    //     0x134964c: ldur            w1, [x0, #0x13]
    // 0x1349650: DecompressPointer r1
    //     0x1349650: add             x1, x1, HEAP, lsl #32
    // 0x1349654: r0 = of()
    //     0x1349654: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1349658: LoadField: r1 = r0->field_87
    //     0x1349658: ldur            w1, [x0, #0x87]
    // 0x134965c: DecompressPointer r1
    //     0x134965c: add             x1, x1, HEAP, lsl #32
    // 0x1349660: LoadField: r0 = r1->field_2b
    //     0x1349660: ldur            w0, [x1, #0x2b]
    // 0x1349664: DecompressPointer r0
    //     0x1349664: add             x0, x0, HEAP, lsl #32
    // 0x1349668: stur            x0, [fp, #-8]
    // 0x134966c: r1 = Instance_Color
    //     0x134966c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1349670: d0 = 0.700000
    //     0x1349670: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1349674: ldr             d0, [x17, #0xf48]
    // 0x1349678: r0 = withOpacity()
    //     0x1349678: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x134967c: r16 = 10.000000
    //     0x134967c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x1349680: stp             x0, x16, [SP]
    // 0x1349684: ldur            x1, [fp, #-8]
    // 0x1349688: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1349688: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x134968c: ldr             x4, [x4, #0xaa0]
    // 0x1349690: r0 = copyWith()
    //     0x1349690: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1349694: stur            x0, [fp, #-8]
    // 0x1349698: r0 = Text()
    //     0x1349698: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x134969c: mov             x1, x0
    // 0x13496a0: r0 = "Powered By"
    //     0x13496a0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x13496a4: ldr             x0, [x0, #0x750]
    // 0x13496a8: stur            x1, [fp, #-0x18]
    // 0x13496ac: StoreField: r1->field_b = r0
    //     0x13496ac: stur            w0, [x1, #0xb]
    // 0x13496b0: ldur            x0, [fp, #-8]
    // 0x13496b4: StoreField: r1->field_13 = r0
    //     0x13496b4: stur            w0, [x1, #0x13]
    // 0x13496b8: r0 = SvgPicture()
    //     0x13496b8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x13496bc: stur            x0, [fp, #-8]
    // 0x13496c0: r16 = 20.000000
    //     0x13496c0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x13496c4: ldr             x16, [x16, #0xac8]
    // 0x13496c8: str             x16, [SP]
    // 0x13496cc: mov             x1, x0
    // 0x13496d0: r2 = "assets/images/shopdeck.svg"
    //     0x13496d0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x13496d4: ldr             x2, [x2, #0x758]
    // 0x13496d8: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x13496d8: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x13496dc: ldr             x4, [x4, #0x760]
    // 0x13496e0: r0 = SvgPicture.asset()
    //     0x13496e0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x13496e4: r1 = Null
    //     0x13496e4: mov             x1, NULL
    // 0x13496e8: r2 = 4
    //     0x13496e8: movz            x2, #0x4
    // 0x13496ec: r0 = AllocateArray()
    //     0x13496ec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13496f0: mov             x2, x0
    // 0x13496f4: ldur            x0, [fp, #-0x18]
    // 0x13496f8: stur            x2, [fp, #-0x20]
    // 0x13496fc: StoreField: r2->field_f = r0
    //     0x13496fc: stur            w0, [x2, #0xf]
    // 0x1349700: ldur            x0, [fp, #-8]
    // 0x1349704: StoreField: r2->field_13 = r0
    //     0x1349704: stur            w0, [x2, #0x13]
    // 0x1349708: r1 = <Widget>
    //     0x1349708: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x134970c: r0 = AllocateGrowableArray()
    //     0x134970c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1349710: mov             x1, x0
    // 0x1349714: ldur            x0, [fp, #-0x20]
    // 0x1349718: stur            x1, [fp, #-8]
    // 0x134971c: StoreField: r1->field_f = r0
    //     0x134971c: stur            w0, [x1, #0xf]
    // 0x1349720: r2 = 4
    //     0x1349720: movz            x2, #0x4
    // 0x1349724: StoreField: r1->field_b = r2
    //     0x1349724: stur            w2, [x1, #0xb]
    // 0x1349728: r0 = Row()
    //     0x1349728: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x134972c: mov             x1, x0
    // 0x1349730: r0 = Instance_Axis
    //     0x1349730: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1349734: stur            x1, [fp, #-0x18]
    // 0x1349738: StoreField: r1->field_f = r0
    //     0x1349738: stur            w0, [x1, #0xf]
    // 0x134973c: r2 = Instance_MainAxisAlignment
    //     0x134973c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1349740: ldr             x2, [x2, #0xa08]
    // 0x1349744: StoreField: r1->field_13 = r2
    //     0x1349744: stur            w2, [x1, #0x13]
    // 0x1349748: r2 = Instance_MainAxisSize
    //     0x1349748: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x134974c: ldr             x2, [x2, #0xdd0]
    // 0x1349750: ArrayStore: r1[0] = r2  ; List_4
    //     0x1349750: stur            w2, [x1, #0x17]
    // 0x1349754: r2 = Instance_CrossAxisAlignment
    //     0x1349754: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1349758: ldr             x2, [x2, #0xa18]
    // 0x134975c: StoreField: r1->field_1b = r2
    //     0x134975c: stur            w2, [x1, #0x1b]
    // 0x1349760: r2 = Instance_VerticalDirection
    //     0x1349760: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1349764: ldr             x2, [x2, #0xa20]
    // 0x1349768: StoreField: r1->field_23 = r2
    //     0x1349768: stur            w2, [x1, #0x23]
    // 0x134976c: r3 = Instance_Clip
    //     0x134976c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1349770: ldr             x3, [x3, #0x38]
    // 0x1349774: StoreField: r1->field_2b = r3
    //     0x1349774: stur            w3, [x1, #0x2b]
    // 0x1349778: StoreField: r1->field_2f = rZR
    //     0x1349778: stur            xzr, [x1, #0x2f]
    // 0x134977c: ldur            x4, [fp, #-8]
    // 0x1349780: StoreField: r1->field_b = r4
    //     0x1349780: stur            w4, [x1, #0xb]
    // 0x1349784: ldur            d0, [fp, #-0x38]
    // 0x1349788: r4 = inline_Allocate_Double()
    //     0x1349788: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0x134978c: add             x4, x4, #0x10
    //     0x1349790: cmp             x5, x4
    //     0x1349794: b.ls            #0x1349900
    //     0x1349798: str             x4, [THR, #0x50]  ; THR::top
    //     0x134979c: sub             x4, x4, #0xf
    //     0x13497a0: movz            x5, #0xe15c
    //     0x13497a4: movk            x5, #0x3, lsl #16
    //     0x13497a8: stur            x5, [x4, #-1]
    // 0x13497ac: StoreField: r4->field_7 = d0
    //     0x13497ac: stur            d0, [x4, #7]
    // 0x13497b0: stur            x4, [fp, #-8]
    // 0x13497b4: r0 = Container()
    //     0x13497b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x13497b8: stur            x0, [fp, #-0x20]
    // 0x13497bc: r16 = Instance_Alignment
    //     0x13497bc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13497c0: ldr             x16, [x16, #0xb10]
    // 0x13497c4: ldur            lr, [fp, #-8]
    // 0x13497c8: stp             lr, x16, [SP, #0x10]
    // 0x13497cc: r16 = Instance_BoxDecoration
    //     0x13497cc: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x13497d0: ldr             x16, [x16, #0x768]
    // 0x13497d4: ldur            lr, [fp, #-0x18]
    // 0x13497d8: stp             lr, x16, [SP]
    // 0x13497dc: mov             x1, x0
    // 0x13497e0: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x13497e0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x13497e4: ldr             x4, [x4, #0x770]
    // 0x13497e8: r0 = Container()
    //     0x13497e8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x13497ec: r1 = Null
    //     0x13497ec: mov             x1, NULL
    // 0x13497f0: r2 = 4
    //     0x13497f0: movz            x2, #0x4
    // 0x13497f4: r0 = AllocateArray()
    //     0x13497f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13497f8: mov             x2, x0
    // 0x13497fc: ldur            x0, [fp, #-0x28]
    // 0x1349800: stur            x2, [fp, #-8]
    // 0x1349804: StoreField: r2->field_f = r0
    //     0x1349804: stur            w0, [x2, #0xf]
    // 0x1349808: ldur            x0, [fp, #-0x20]
    // 0x134980c: StoreField: r2->field_13 = r0
    //     0x134980c: stur            w0, [x2, #0x13]
    // 0x1349810: r1 = <Widget>
    //     0x1349810: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1349814: r0 = AllocateGrowableArray()
    //     0x1349814: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1349818: mov             x1, x0
    // 0x134981c: ldur            x0, [fp, #-8]
    // 0x1349820: stur            x1, [fp, #-0x18]
    // 0x1349824: StoreField: r1->field_f = r0
    //     0x1349824: stur            w0, [x1, #0xf]
    // 0x1349828: r0 = 4
    //     0x1349828: movz            x0, #0x4
    // 0x134982c: StoreField: r1->field_b = r0
    //     0x134982c: stur            w0, [x1, #0xb]
    // 0x1349830: r0 = Wrap()
    //     0x1349830: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x1349834: mov             x1, x0
    // 0x1349838: r0 = Instance_Axis
    //     0x1349838: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x134983c: stur            x1, [fp, #-8]
    // 0x1349840: StoreField: r1->field_f = r0
    //     0x1349840: stur            w0, [x1, #0xf]
    // 0x1349844: r0 = Instance_WrapAlignment
    //     0x1349844: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x1349848: ldr             x0, [x0, #0x6e8]
    // 0x134984c: StoreField: r1->field_13 = r0
    //     0x134984c: stur            w0, [x1, #0x13]
    // 0x1349850: ArrayStore: r1[0] = rZR  ; List_8
    //     0x1349850: stur            xzr, [x1, #0x17]
    // 0x1349854: StoreField: r1->field_1f = r0
    //     0x1349854: stur            w0, [x1, #0x1f]
    // 0x1349858: StoreField: r1->field_23 = rZR
    //     0x1349858: stur            xzr, [x1, #0x23]
    // 0x134985c: r0 = Instance_WrapCrossAlignment
    //     0x134985c: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x1349860: ldr             x0, [x0, #0x6f0]
    // 0x1349864: StoreField: r1->field_2b = r0
    //     0x1349864: stur            w0, [x1, #0x2b]
    // 0x1349868: r0 = Instance_VerticalDirection
    //     0x1349868: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x134986c: ldr             x0, [x0, #0xa20]
    // 0x1349870: StoreField: r1->field_33 = r0
    //     0x1349870: stur            w0, [x1, #0x33]
    // 0x1349874: r0 = Instance_Clip
    //     0x1349874: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1349878: ldr             x0, [x0, #0x38]
    // 0x134987c: StoreField: r1->field_37 = r0
    //     0x134987c: stur            w0, [x1, #0x37]
    // 0x1349880: ldur            x0, [fp, #-0x18]
    // 0x1349884: StoreField: r1->field_b = r0
    //     0x1349884: stur            w0, [x1, #0xb]
    // 0x1349888: r0 = Padding()
    //     0x1349888: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x134988c: mov             x1, x0
    // 0x1349890: ldur            x0, [fp, #-0x10]
    // 0x1349894: stur            x1, [fp, #-0x18]
    // 0x1349898: StoreField: r1->field_f = r0
    //     0x1349898: stur            w0, [x1, #0xf]
    // 0x134989c: ldur            x0, [fp, #-8]
    // 0x13498a0: StoreField: r1->field_b = r0
    //     0x13498a0: stur            w0, [x1, #0xb]
    // 0x13498a4: r0 = SafeArea()
    //     0x13498a4: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x13498a8: r1 = true
    //     0x13498a8: add             x1, NULL, #0x20  ; true
    // 0x13498ac: StoreField: r0->field_b = r1
    //     0x13498ac: stur            w1, [x0, #0xb]
    // 0x13498b0: StoreField: r0->field_f = r1
    //     0x13498b0: stur            w1, [x0, #0xf]
    // 0x13498b4: StoreField: r0->field_13 = r1
    //     0x13498b4: stur            w1, [x0, #0x13]
    // 0x13498b8: ArrayStore: r0[0] = r1  ; List_4
    //     0x13498b8: stur            w1, [x0, #0x17]
    // 0x13498bc: r1 = Instance_EdgeInsets
    //     0x13498bc: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x13498c0: StoreField: r0->field_1b = r1
    //     0x13498c0: stur            w1, [x0, #0x1b]
    // 0x13498c4: r1 = false
    //     0x13498c4: add             x1, NULL, #0x30  ; false
    // 0x13498c8: StoreField: r0->field_1f = r1
    //     0x13498c8: stur            w1, [x0, #0x1f]
    // 0x13498cc: ldur            x1, [fp, #-0x18]
    // 0x13498d0: StoreField: r0->field_23 = r1
    //     0x13498d0: stur            w1, [x0, #0x23]
    // 0x13498d4: LeaveFrame
    //     0x13498d4: mov             SP, fp
    //     0x13498d8: ldp             fp, lr, [SP], #0x10
    // 0x13498dc: ret
    //     0x13498dc: ret             
    // 0x13498e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13498e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13498e4: b               #0x13493a4
    // 0x13498e8: SaveReg d0
    //     0x13498e8: str             q0, [SP, #-0x10]!
    // 0x13498ec: SaveReg r1
    //     0x13498ec: str             x1, [SP, #-8]!
    // 0x13498f0: r0 = AllocateDouble()
    //     0x13498f0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13498f4: RestoreReg r1
    //     0x13498f4: ldr             x1, [SP], #8
    // 0x13498f8: RestoreReg d0
    //     0x13498f8: ldr             q0, [SP], #0x10
    // 0x13498fc: b               #0x1349604
    // 0x1349900: SaveReg d0
    //     0x1349900: str             q0, [SP, #-0x10]!
    // 0x1349904: stp             x2, x3, [SP, #-0x10]!
    // 0x1349908: stp             x0, x1, [SP, #-0x10]!
    // 0x134990c: r0 = AllocateDouble()
    //     0x134990c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1349910: mov             x4, x0
    // 0x1349914: ldp             x0, x1, [SP], #0x10
    // 0x1349918: ldp             x2, x3, [SP], #0x10
    // 0x134991c: RestoreReg d0
    //     0x134991c: ldr             q0, [SP], #0x10
    // 0x1349920: b               #0x13497ac
  }
  _ body(/* No info */) {
    // ** addr: 0x1484fd4, size: 0x224
    // 0x1484fd4: EnterFrame
    //     0x1484fd4: stp             fp, lr, [SP, #-0x10]!
    //     0x1484fd8: mov             fp, SP
    // 0x1484fdc: AllocStack(0x28)
    //     0x1484fdc: sub             SP, SP, #0x28
    // 0x1484fe0: SetupParameters(CheckoutRequestAddressPage this /* r1 => r0, fp-0x8 */)
    //     0x1484fe0: mov             x0, x1
    //     0x1484fe4: stur            x1, [fp, #-8]
    // 0x1484fe8: r1 = 1
    //     0x1484fe8: movz            x1, #0x1
    // 0x1484fec: r0 = AllocateContext()
    //     0x1484fec: bl              #0x16f6108  ; AllocateContextStub
    // 0x1484ff0: ldur            x2, [fp, #-8]
    // 0x1484ff4: stur            x0, [fp, #-0x10]
    // 0x1484ff8: StoreField: r0->field_f = r2
    //     0x1484ff8: stur            w2, [x0, #0xf]
    // 0x1484ffc: r0 = Obx()
    //     0x1484ffc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1485000: ldur            x2, [fp, #-0x10]
    // 0x1485004: r1 = Function '<anonymous closure>':.
    //     0x1485004: add             x1, PP, #0x44, lsl #12  ; [pp+0x44268] AnonymousClosure: (0x148557c), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x1484fd4)
    //     0x1485008: ldr             x1, [x1, #0x268]
    // 0x148500c: stur            x0, [fp, #-0x18]
    // 0x1485010: r0 = AllocateClosure()
    //     0x1485010: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485014: mov             x1, x0
    // 0x1485018: ldur            x0, [fp, #-0x18]
    // 0x148501c: StoreField: r0->field_b = r1
    //     0x148501c: stur            w1, [x0, #0xb]
    // 0x1485020: r0 = Obx()
    //     0x1485020: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1485024: ldur            x2, [fp, #-0x10]
    // 0x1485028: r1 = Function '<anonymous closure>':.
    //     0x1485028: add             x1, PP, #0x44, lsl #12  ; [pp+0x44270] AnonymousClosure: (0x1485480), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x1484fd4)
    //     0x148502c: ldr             x1, [x1, #0x270]
    // 0x1485030: stur            x0, [fp, #-0x20]
    // 0x1485034: r0 = AllocateClosure()
    //     0x1485034: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485038: mov             x1, x0
    // 0x148503c: ldur            x0, [fp, #-0x20]
    // 0x1485040: StoreField: r0->field_b = r1
    //     0x1485040: stur            w1, [x0, #0xb]
    // 0x1485044: r1 = <CheckoutRequestAddressController>
    //     0x1485044: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d7c8] TypeArguments: <CheckoutRequestAddressController>
    //     0x1485048: ldr             x1, [x1, #0x7c8]
    // 0x148504c: r0 = GetBuilder()
    //     0x148504c: bl              #0x12af310  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0x1485050: mov             x3, x0
    // 0x1485054: r0 = true
    //     0x1485054: add             x0, NULL, #0x20  ; true
    // 0x1485058: stur            x3, [fp, #-0x28]
    // 0x148505c: StoreField: r3->field_13 = r0
    //     0x148505c: stur            w0, [x3, #0x13]
    // 0x1485060: ldur            x2, [fp, #-0x10]
    // 0x1485064: r1 = Function '<anonymous closure>':.
    //     0x1485064: add             x1, PP, #0x44, lsl #12  ; [pp+0x44278] AnonymousClosure: (0x14852e0), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x1484fd4)
    //     0x1485068: ldr             x1, [x1, #0x278]
    // 0x148506c: r0 = AllocateClosure()
    //     0x148506c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485070: mov             x1, x0
    // 0x1485074: ldur            x0, [fp, #-0x28]
    // 0x1485078: StoreField: r0->field_f = r1
    //     0x1485078: stur            w1, [x0, #0xf]
    // 0x148507c: r3 = true
    //     0x148507c: add             x3, NULL, #0x20  ; true
    // 0x1485080: StoreField: r0->field_1f = r3
    //     0x1485080: stur            w3, [x0, #0x1f]
    // 0x1485084: r4 = false
    //     0x1485084: add             x4, NULL, #0x30  ; false
    // 0x1485088: StoreField: r0->field_23 = r4
    //     0x1485088: stur            w4, [x0, #0x23]
    // 0x148508c: r1 = "address_widget"
    //     0x148508c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc10] "address_widget"
    //     0x1485090: ldr             x1, [x1, #0xc10]
    // 0x1485094: ArrayStore: r0[0] = r1  ; List_4
    //     0x1485094: stur            w1, [x0, #0x17]
    // 0x1485098: r1 = Null
    //     0x1485098: mov             x1, NULL
    // 0x148509c: r2 = 6
    //     0x148509c: movz            x2, #0x6
    // 0x14850a0: r0 = AllocateArray()
    //     0x14850a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14850a4: mov             x2, x0
    // 0x14850a8: ldur            x0, [fp, #-0x18]
    // 0x14850ac: stur            x2, [fp, #-0x10]
    // 0x14850b0: StoreField: r2->field_f = r0
    //     0x14850b0: stur            w0, [x2, #0xf]
    // 0x14850b4: ldur            x0, [fp, #-0x20]
    // 0x14850b8: StoreField: r2->field_13 = r0
    //     0x14850b8: stur            w0, [x2, #0x13]
    // 0x14850bc: ldur            x0, [fp, #-0x28]
    // 0x14850c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x14850c0: stur            w0, [x2, #0x17]
    // 0x14850c4: r1 = <Widget>
    //     0x14850c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14850c8: r0 = AllocateGrowableArray()
    //     0x14850c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14850cc: mov             x1, x0
    // 0x14850d0: ldur            x0, [fp, #-0x10]
    // 0x14850d4: stur            x1, [fp, #-0x18]
    // 0x14850d8: StoreField: r1->field_f = r0
    //     0x14850d8: stur            w0, [x1, #0xf]
    // 0x14850dc: r0 = 6
    //     0x14850dc: movz            x0, #0x6
    // 0x14850e0: StoreField: r1->field_b = r0
    //     0x14850e0: stur            w0, [x1, #0xb]
    // 0x14850e4: r0 = Column()
    //     0x14850e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14850e8: mov             x1, x0
    // 0x14850ec: r0 = Instance_Axis
    //     0x14850ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14850f0: stur            x1, [fp, #-0x10]
    // 0x14850f4: StoreField: r1->field_f = r0
    //     0x14850f4: stur            w0, [x1, #0xf]
    // 0x14850f8: r2 = Instance_MainAxisAlignment
    //     0x14850f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14850fc: ldr             x2, [x2, #0xa08]
    // 0x1485100: StoreField: r1->field_13 = r2
    //     0x1485100: stur            w2, [x1, #0x13]
    // 0x1485104: r2 = Instance_MainAxisSize
    //     0x1485104: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1485108: ldr             x2, [x2, #0xdd0]
    // 0x148510c: ArrayStore: r1[0] = r2  ; List_4
    //     0x148510c: stur            w2, [x1, #0x17]
    // 0x1485110: r2 = Instance_CrossAxisAlignment
    //     0x1485110: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dc18] Obj!CrossAxisAlignment@d73441
    //     0x1485114: ldr             x2, [x2, #0xc18]
    // 0x1485118: StoreField: r1->field_1b = r2
    //     0x1485118: stur            w2, [x1, #0x1b]
    // 0x148511c: r2 = Instance_VerticalDirection
    //     0x148511c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1485120: ldr             x2, [x2, #0xa20]
    // 0x1485124: StoreField: r1->field_23 = r2
    //     0x1485124: stur            w2, [x1, #0x23]
    // 0x1485128: r2 = Instance_Clip
    //     0x1485128: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x148512c: ldr             x2, [x2, #0x38]
    // 0x1485130: StoreField: r1->field_2b = r2
    //     0x1485130: stur            w2, [x1, #0x2b]
    // 0x1485134: StoreField: r1->field_2f = rZR
    //     0x1485134: stur            xzr, [x1, #0x2f]
    // 0x1485138: ldur            x2, [fp, #-0x18]
    // 0x148513c: StoreField: r1->field_b = r2
    //     0x148513c: stur            w2, [x1, #0xb]
    // 0x1485140: r0 = SingleChildScrollView()
    //     0x1485140: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x1485144: mov             x1, x0
    // 0x1485148: r0 = Instance_Axis
    //     0x1485148: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x148514c: stur            x1, [fp, #-0x18]
    // 0x1485150: StoreField: r1->field_b = r0
    //     0x1485150: stur            w0, [x1, #0xb]
    // 0x1485154: r0 = false
    //     0x1485154: add             x0, NULL, #0x30  ; false
    // 0x1485158: StoreField: r1->field_f = r0
    //     0x1485158: stur            w0, [x1, #0xf]
    // 0x148515c: ldur            x2, [fp, #-0x10]
    // 0x1485160: StoreField: r1->field_23 = r2
    //     0x1485160: stur            w2, [x1, #0x23]
    // 0x1485164: r2 = Instance_DragStartBehavior
    //     0x1485164: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x1485168: StoreField: r1->field_27 = r2
    //     0x1485168: stur            w2, [x1, #0x27]
    // 0x148516c: r2 = Instance_Clip
    //     0x148516c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x1485170: ldr             x2, [x2, #0x7e0]
    // 0x1485174: StoreField: r1->field_2b = r2
    //     0x1485174: stur            w2, [x1, #0x2b]
    // 0x1485178: r2 = Instance_HitTestBehavior
    //     0x1485178: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x148517c: ldr             x2, [x2, #0x288]
    // 0x1485180: StoreField: r1->field_2f = r2
    //     0x1485180: stur            w2, [x1, #0x2f]
    // 0x1485184: r0 = SafeArea()
    //     0x1485184: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1485188: mov             x1, x0
    // 0x148518c: r0 = true
    //     0x148518c: add             x0, NULL, #0x20  ; true
    // 0x1485190: stur            x1, [fp, #-0x10]
    // 0x1485194: StoreField: r1->field_b = r0
    //     0x1485194: stur            w0, [x1, #0xb]
    // 0x1485198: StoreField: r1->field_f = r0
    //     0x1485198: stur            w0, [x1, #0xf]
    // 0x148519c: StoreField: r1->field_13 = r0
    //     0x148519c: stur            w0, [x1, #0x13]
    // 0x14851a0: ArrayStore: r1[0] = r0  ; List_4
    //     0x14851a0: stur            w0, [x1, #0x17]
    // 0x14851a4: r0 = Instance_EdgeInsets
    //     0x14851a4: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14851a8: StoreField: r1->field_1b = r0
    //     0x14851a8: stur            w0, [x1, #0x1b]
    // 0x14851ac: r0 = false
    //     0x14851ac: add             x0, NULL, #0x30  ; false
    // 0x14851b0: StoreField: r1->field_1f = r0
    //     0x14851b0: stur            w0, [x1, #0x1f]
    // 0x14851b4: ldur            x0, [fp, #-0x18]
    // 0x14851b8: StoreField: r1->field_23 = r0
    //     0x14851b8: stur            w0, [x1, #0x23]
    // 0x14851bc: r0 = WillPopScope()
    //     0x14851bc: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14851c0: mov             x3, x0
    // 0x14851c4: ldur            x0, [fp, #-0x10]
    // 0x14851c8: stur            x3, [fp, #-0x18]
    // 0x14851cc: StoreField: r3->field_b = r0
    //     0x14851cc: stur            w0, [x3, #0xb]
    // 0x14851d0: ldur            x2, [fp, #-8]
    // 0x14851d4: r1 = Function 'getBack':.
    //     0x14851d4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44280] AnonymousClosure: (0x14851f8), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack (0x1485230)
    //     0x14851d8: ldr             x1, [x1, #0x280]
    // 0x14851dc: r0 = AllocateClosure()
    //     0x14851dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14851e0: mov             x1, x0
    // 0x14851e4: ldur            x0, [fp, #-0x18]
    // 0x14851e8: StoreField: r0->field_f = r1
    //     0x14851e8: stur            w1, [x0, #0xf]
    // 0x14851ec: LeaveFrame
    //     0x14851ec: mov             SP, fp
    //     0x14851f0: ldp             fp, lr, [SP], #0x10
    // 0x14851f4: ret
    //     0x14851f4: ret             
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x14851f8, size: 0x38
    // 0x14851f8: EnterFrame
    //     0x14851f8: stp             fp, lr, [SP, #-0x10]!
    //     0x14851fc: mov             fp, SP
    // 0x1485200: ldr             x0, [fp, #0x10]
    // 0x1485204: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1485204: ldur            w1, [x0, #0x17]
    // 0x1485208: DecompressPointer r1
    //     0x1485208: add             x1, x1, HEAP, lsl #32
    // 0x148520c: CheckStackOverflow
    //     0x148520c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485210: cmp             SP, x16
    //     0x1485214: b.ls            #0x1485228
    // 0x1485218: r0 = getBack()
    //     0x1485218: bl              #0x1485230  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack
    // 0x148521c: LeaveFrame
    //     0x148521c: mov             SP, fp
    //     0x1485220: ldp             fp, lr, [SP], #0x10
    // 0x1485224: ret
    //     0x1485224: ret             
    // 0x1485228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485228: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148522c: b               #0x1485218
  }
  _ getBack(/* No info */) {
    // ** addr: 0x1485230, size: 0xb0
    // 0x1485230: EnterFrame
    //     0x1485230: stp             fp, lr, [SP, #-0x10]!
    //     0x1485234: mov             fp, SP
    // 0x1485238: AllocStack(0x8)
    //     0x1485238: sub             SP, SP, #8
    // 0x148523c: CheckStackOverflow
    //     0x148523c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485240: cmp             SP, x16
    //     0x1485244: b.ls            #0x14852d8
    // 0x1485248: r2 = false
    //     0x1485248: add             x2, NULL, #0x30  ; false
    // 0x148524c: r0 = showLoading()
    //     0x148524c: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x1485250: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1485250: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1485254: ldr             x0, [x0, #0x1c80]
    //     0x1485258: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x148525c: cmp             w0, w16
    //     0x1485260: b.ne            #0x148526c
    //     0x1485264: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1485268: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x148526c: r1 = Function '<anonymous closure>':.
    //     0x148526c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44288] AnonymousClosure: (0x138f78c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack (0x138f970)
    //     0x1485270: ldr             x1, [x1, #0x288]
    // 0x1485274: r2 = Null
    //     0x1485274: mov             x2, NULL
    // 0x1485278: r0 = AllocateClosure()
    //     0x1485278: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148527c: mov             x1, x0
    // 0x1485280: r0 = GetNavigation.until()
    //     0x1485280: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x1485284: r1 = <bool>
    //     0x1485284: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x1485288: r0 = _Future()
    //     0x1485288: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x148528c: stur            x0, [fp, #-8]
    // 0x1485290: StoreField: r0->field_b = rZR
    //     0x1485290: stur            xzr, [x0, #0xb]
    // 0x1485294: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x1485294: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1485298: ldr             x0, [x0, #0x778]
    //     0x148529c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14852a0: cmp             w0, w16
    //     0x14852a4: b.ne            #0x14852b0
    //     0x14852a8: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x14852ac: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x14852b0: mov             x1, x0
    // 0x14852b4: ldur            x0, [fp, #-8]
    // 0x14852b8: StoreField: r0->field_13 = r1
    //     0x14852b8: stur            w1, [x0, #0x13]
    // 0x14852bc: mov             x1, x0
    // 0x14852c0: r2 = false
    //     0x14852c0: add             x2, NULL, #0x30  ; false
    // 0x14852c4: r0 = _asyncComplete()
    //     0x14852c4: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x14852c8: ldur            x0, [fp, #-8]
    // 0x14852cc: LeaveFrame
    //     0x14852cc: mov             SP, fp
    //     0x14852d0: ldp             fp, lr, [SP], #0x10
    // 0x14852d4: ret
    //     0x14852d4: ret             
    // 0x14852d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14852d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14852dc: b               #0x1485248
  }
  [closure] Flexible <anonymous closure>(dynamic, CheckoutRequestAddressController) {
    // ** addr: 0x14852e0, size: 0x158
    // 0x14852e0: EnterFrame
    //     0x14852e0: stp             fp, lr, [SP, #-0x10]!
    //     0x14852e4: mov             fp, SP
    // 0x14852e8: AllocStack(0x30)
    //     0x14852e8: sub             SP, SP, #0x30
    // 0x14852ec: SetupParameters()
    //     0x14852ec: ldr             x0, [fp, #0x18]
    //     0x14852f0: ldur            w1, [x0, #0x17]
    //     0x14852f4: add             x1, x1, HEAP, lsl #32
    //     0x14852f8: stur            x1, [fp, #-8]
    // 0x14852fc: CheckStackOverflow
    //     0x14852fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485300: cmp             SP, x16
    //     0x1485304: b.ls            #0x1485430
    // 0x1485308: r1 = 1
    //     0x1485308: movz            x1, #0x1
    // 0x148530c: r0 = AllocateContext()
    //     0x148530c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1485310: mov             x2, x0
    // 0x1485314: ldur            x0, [fp, #-8]
    // 0x1485318: stur            x2, [fp, #-0x10]
    // 0x148531c: StoreField: r2->field_b = r0
    //     0x148531c: stur            w0, [x2, #0xb]
    // 0x1485320: ldr             x3, [fp, #0x10]
    // 0x1485324: StoreField: r2->field_f = r3
    //     0x1485324: stur            w3, [x2, #0xf]
    // 0x1485328: LoadField: r1 = r3->field_53
    //     0x1485328: ldur            w1, [x3, #0x53]
    // 0x148532c: DecompressPointer r1
    //     0x148532c: add             x1, x1, HEAP, lsl #32
    // 0x1485330: r0 = value()
    //     0x1485330: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485334: mov             x2, x0
    // 0x1485338: ldr             x0, [fp, #0x10]
    // 0x148533c: stur            x2, [fp, #-0x18]
    // 0x1485340: LoadField: r1 = r0->field_73
    //     0x1485340: ldur            w1, [x0, #0x73]
    // 0x1485344: DecompressPointer r1
    //     0x1485344: add             x1, x1, HEAP, lsl #32
    // 0x1485348: r0 = value()
    //     0x1485348: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x148534c: mov             x2, x0
    // 0x1485350: ldur            x0, [fp, #-8]
    // 0x1485354: stur            x2, [fp, #-0x28]
    // 0x1485358: LoadField: r3 = r0->field_f
    //     0x1485358: ldur            w3, [x0, #0xf]
    // 0x148535c: DecompressPointer r3
    //     0x148535c: add             x3, x3, HEAP, lsl #32
    // 0x1485360: ldr             x0, [fp, #0x10]
    // 0x1485364: stur            x3, [fp, #-0x20]
    // 0x1485368: LoadField: r1 = r0->field_af
    //     0x1485368: ldur            w1, [x0, #0xaf]
    // 0x148536c: DecompressPointer r1
    //     0x148536c: add             x1, x1, HEAP, lsl #32
    // 0x1485370: r0 = value()
    //     0x1485370: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485374: stur            x0, [fp, #-8]
    // 0x1485378: r0 = CheckoutAddressWidget()
    //     0x1485378: bl              #0x1485438  ; AllocateCheckoutAddressWidgetStub -> CheckoutAddressWidget (size=0x24)
    // 0x148537c: mov             x3, x0
    // 0x1485380: ldur            x0, [fp, #-0x18]
    // 0x1485384: stur            x3, [fp, #-0x30]
    // 0x1485388: StoreField: r3->field_b = r0
    //     0x1485388: stur            w0, [x3, #0xb]
    // 0x148538c: ldur            x2, [fp, #-0x10]
    // 0x1485390: r1 = Function '<anonymous closure>':.
    //     0x1485390: add             x1, PP, #0x44, lsl #12  ; [pp+0x44290] AnonymousClosure: (0x13906d0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x1485394: ldr             x1, [x1, #0x290]
    // 0x1485398: r0 = AllocateClosure()
    //     0x1485398: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x148539c: mov             x1, x0
    // 0x14853a0: ldur            x0, [fp, #-0x30]
    // 0x14853a4: StoreField: r0->field_f = r1
    //     0x14853a4: stur            w1, [x0, #0xf]
    // 0x14853a8: ldur            x1, [fp, #-0x28]
    // 0x14853ac: StoreField: r0->field_13 = r1
    //     0x14853ac: stur            w1, [x0, #0x13]
    // 0x14853b0: ldur            x2, [fp, #-0x20]
    // 0x14853b4: r1 = Function 'pinCodeVerifyCallback':.
    //     0x14853b4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44298] AnonymousClosure: (0x1485444), in [package:customer_app/app/presentation/views/basic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::pinCodeVerifyCallback (0x138fdcc)
    //     0x14853b8: ldr             x1, [x1, #0x298]
    // 0x14853bc: r0 = AllocateClosure()
    //     0x14853bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14853c0: mov             x1, x0
    // 0x14853c4: ldur            x0, [fp, #-0x30]
    // 0x14853c8: ArrayStore: r0[0] = r1  ; List_4
    //     0x14853c8: stur            w1, [x0, #0x17]
    // 0x14853cc: ldur            x1, [fp, #-8]
    // 0x14853d0: StoreField: r0->field_1b = r1
    //     0x14853d0: stur            w1, [x0, #0x1b]
    // 0x14853d4: ldur            x2, [fp, #-0x10]
    // 0x14853d8: r1 = Function '<anonymous closure>':.
    //     0x14853d8: add             x1, PP, #0x44, lsl #12  ; [pp+0x442a0] AnonymousClosure: (0x138fbbc), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x14853dc: ldr             x1, [x1, #0x2a0]
    // 0x14853e0: r0 = AllocateClosure()
    //     0x14853e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14853e4: mov             x1, x0
    // 0x14853e8: ldur            x0, [fp, #-0x30]
    // 0x14853ec: StoreField: r0->field_1f = r1
    //     0x14853ec: stur            w1, [x0, #0x1f]
    // 0x14853f0: r1 = Instance_ValueKey
    //     0x14853f0: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc48] Obj!ValueKey<String>@d5b3a1
    //     0x14853f4: ldr             x1, [x1, #0xc48]
    // 0x14853f8: StoreField: r0->field_7 = r1
    //     0x14853f8: stur            w1, [x0, #7]
    // 0x14853fc: r1 = <FlexParentData>
    //     0x14853fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1485400: ldr             x1, [x1, #0xe00]
    // 0x1485404: r0 = Flexible()
    //     0x1485404: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x1485408: r1 = 1
    //     0x1485408: movz            x1, #0x1
    // 0x148540c: StoreField: r0->field_13 = r1
    //     0x148540c: stur            x1, [x0, #0x13]
    // 0x1485410: r1 = Instance_FlexFit
    //     0x1485410: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x1485414: ldr             x1, [x1, #0xe20]
    // 0x1485418: StoreField: r0->field_1b = r1
    //     0x1485418: stur            w1, [x0, #0x1b]
    // 0x148541c: ldur            x1, [fp, #-0x30]
    // 0x1485420: StoreField: r0->field_b = r1
    //     0x1485420: stur            w1, [x0, #0xb]
    // 0x1485424: LeaveFrame
    //     0x1485424: mov             SP, fp
    //     0x1485428: ldp             fp, lr, [SP], #0x10
    // 0x148542c: ret
    //     0x148542c: ret             
    // 0x1485430: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485430: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1485434: b               #0x1485308
  }
  [closure] void pinCodeVerifyCallback(dynamic, String) {
    // ** addr: 0x1485444, size: 0x3c
    // 0x1485444: EnterFrame
    //     0x1485444: stp             fp, lr, [SP, #-0x10]!
    //     0x1485448: mov             fp, SP
    // 0x148544c: ldr             x0, [fp, #0x18]
    // 0x1485450: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1485450: ldur            w1, [x0, #0x17]
    // 0x1485454: DecompressPointer r1
    //     0x1485454: add             x1, x1, HEAP, lsl #32
    // 0x1485458: CheckStackOverflow
    //     0x1485458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x148545c: cmp             SP, x16
    //     0x1485460: b.ls            #0x1485478
    // 0x1485464: ldr             x2, [fp, #0x10]
    // 0x1485468: r0 = pinCodeVerifyCallback()
    //     0x1485468: bl              #0x138fdcc  ; [package:customer_app/app/presentation/views/basic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::pinCodeVerifyCallback
    // 0x148546c: LeaveFrame
    //     0x148546c: mov             SP, fp
    //     0x1485470: ldp             fp, lr, [SP], #0x10
    // 0x1485474: ret
    //     0x1485474: ret             
    // 0x1485478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485478: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148547c: b               #0x1485464
  }
  [closure] CheckoutBagAccordion <anonymous closure>(dynamic) {
    // ** addr: 0x1485480, size: 0xf0
    // 0x1485480: EnterFrame
    //     0x1485480: stp             fp, lr, [SP, #-0x10]!
    //     0x1485484: mov             fp, SP
    // 0x1485488: AllocStack(0x28)
    //     0x1485488: sub             SP, SP, #0x28
    // 0x148548c: SetupParameters()
    //     0x148548c: ldr             x0, [fp, #0x10]
    //     0x1485490: ldur            w2, [x0, #0x17]
    //     0x1485494: add             x2, x2, HEAP, lsl #32
    //     0x1485498: stur            x2, [fp, #-8]
    // 0x148549c: CheckStackOverflow
    //     0x148549c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14854a0: cmp             SP, x16
    //     0x14854a4: b.ls            #0x1485568
    // 0x14854a8: LoadField: r1 = r2->field_f
    //     0x14854a8: ldur            w1, [x2, #0xf]
    // 0x14854ac: DecompressPointer r1
    //     0x14854ac: add             x1, x1, HEAP, lsl #32
    // 0x14854b0: r0 = controller()
    //     0x14854b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14854b4: LoadField: r1 = r0->field_5f
    //     0x14854b4: ldur            w1, [x0, #0x5f]
    // 0x14854b8: DecompressPointer r1
    //     0x14854b8: add             x1, x1, HEAP, lsl #32
    // 0x14854bc: r0 = value()
    //     0x14854bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14854c0: ldur            x2, [fp, #-8]
    // 0x14854c4: stur            x0, [fp, #-0x10]
    // 0x14854c8: LoadField: r1 = r2->field_f
    //     0x14854c8: ldur            w1, [x2, #0xf]
    // 0x14854cc: DecompressPointer r1
    //     0x14854cc: add             x1, x1, HEAP, lsl #32
    // 0x14854d0: r0 = controller()
    //     0x14854d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14854d4: LoadField: r1 = r0->field_5b
    //     0x14854d4: ldur            w1, [x0, #0x5b]
    // 0x14854d8: DecompressPointer r1
    //     0x14854d8: add             x1, x1, HEAP, lsl #32
    // 0x14854dc: r0 = value()
    //     0x14854dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14854e0: ldur            x2, [fp, #-8]
    // 0x14854e4: stur            x0, [fp, #-0x18]
    // 0x14854e8: LoadField: r1 = r2->field_f
    //     0x14854e8: ldur            w1, [x2, #0xf]
    // 0x14854ec: DecompressPointer r1
    //     0x14854ec: add             x1, x1, HEAP, lsl #32
    // 0x14854f0: r0 = controller()
    //     0x14854f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14854f4: LoadField: r1 = r0->field_53
    //     0x14854f4: ldur            w1, [x0, #0x53]
    // 0x14854f8: DecompressPointer r1
    //     0x14854f8: add             x1, x1, HEAP, lsl #32
    // 0x14854fc: r0 = value()
    //     0x14854fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1485500: ldur            x2, [fp, #-8]
    // 0x1485504: LoadField: r1 = r2->field_f
    //     0x1485504: ldur            w1, [x2, #0xf]
    // 0x1485508: DecompressPointer r1
    //     0x1485508: add             x1, x1, HEAP, lsl #32
    // 0x148550c: r0 = controller()
    //     0x148550c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1485510: LoadField: r1 = r0->field_8b
    //     0x1485510: ldur            w1, [x0, #0x8b]
    // 0x1485514: DecompressPointer r1
    //     0x1485514: add             x1, x1, HEAP, lsl #32
    // 0x1485518: stur            x1, [fp, #-0x20]
    // 0x148551c: r0 = CheckoutBagAccordion()
    //     0x148551c: bl              #0x1485570  ; AllocateCheckoutBagAccordionStub -> CheckoutBagAccordion (size=0x1c)
    // 0x1485520: mov             x3, x0
    // 0x1485524: ldur            x0, [fp, #-0x10]
    // 0x1485528: stur            x3, [fp, #-0x28]
    // 0x148552c: StoreField: r3->field_b = r0
    //     0x148552c: stur            w0, [x3, #0xb]
    // 0x1485530: ldur            x0, [fp, #-0x18]
    // 0x1485534: StoreField: r3->field_f = r0
    //     0x1485534: stur            w0, [x3, #0xf]
    // 0x1485538: ldur            x0, [fp, #-0x20]
    // 0x148553c: StoreField: r3->field_13 = r0
    //     0x148553c: stur            w0, [x3, #0x13]
    // 0x1485540: ldur            x2, [fp, #-8]
    // 0x1485544: r1 = Function '<anonymous closure>':.
    //     0x1485544: add             x1, PP, #0x44, lsl #12  ; [pp+0x442a8] AnonymousClosure: (0x139093c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x1485548: ldr             x1, [x1, #0x2a8]
    // 0x148554c: r0 = AllocateClosure()
    //     0x148554c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1485550: mov             x1, x0
    // 0x1485554: ldur            x0, [fp, #-0x28]
    // 0x1485558: ArrayStore: r0[0] = r1  ; List_4
    //     0x1485558: stur            w1, [x0, #0x17]
    // 0x148555c: LeaveFrame
    //     0x148555c: mov             SP, fp
    //     0x1485560: ldp             fp, lr, [SP], #0x10
    // 0x1485564: ret
    //     0x1485564: ret             
    // 0x1485568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1485568: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x148556c: b               #0x14854a8
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x148557c, size: 0x68
    // 0x148557c: EnterFrame
    //     0x148557c: stp             fp, lr, [SP, #-0x10]!
    //     0x1485580: mov             fp, SP
    // 0x1485584: AllocStack(0x8)
    //     0x1485584: sub             SP, SP, #8
    // 0x1485588: SetupParameters()
    //     0x1485588: ldr             x0, [fp, #0x10]
    //     0x148558c: ldur            w1, [x0, #0x17]
    //     0x1485590: add             x1, x1, HEAP, lsl #32
    // 0x1485594: CheckStackOverflow
    //     0x1485594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1485598: cmp             SP, x16
    //     0x148559c: b.ls            #0x14855dc
    // 0x14855a0: LoadField: r0 = r1->field_f
    //     0x14855a0: ldur            w0, [x1, #0xf]
    // 0x14855a4: DecompressPointer r0
    //     0x14855a4: add             x0, x0, HEAP, lsl #32
    // 0x14855a8: mov             x1, x0
    // 0x14855ac: r0 = controller()
    //     0x14855ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14855b0: LoadField: r1 = r0->field_7b
    //     0x14855b0: ldur            w1, [x0, #0x7b]
    // 0x14855b4: DecompressPointer r1
    //     0x14855b4: add             x1, x1, HEAP, lsl #32
    // 0x14855b8: r0 = value()
    //     0x14855b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14855bc: stur            x0, [fp, #-8]
    // 0x14855c0: r0 = CheckoutBreadCrumb()
    //     0x14855c0: bl              #0x1484fc8  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x14855c4: ldur            x1, [fp, #-8]
    // 0x14855c8: StoreField: r0->field_b = r1
    //     0x14855c8: stur            w1, [x0, #0xb]
    // 0x14855cc: StoreField: r0->field_f = rZR
    //     0x14855cc: stur            xzr, [x0, #0xf]
    // 0x14855d0: LeaveFrame
    //     0x14855d0: mov             SP, fp
    //     0x14855d4: ldp             fp, lr, [SP], #0x10
    // 0x14855d8: ret
    //     0x14855d8: ret             
    // 0x14855dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14855dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14855e0: b               #0x14855a0
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d9288, size: 0x250
    // 0x15d9288: EnterFrame
    //     0x15d9288: stp             fp, lr, [SP, #-0x10]!
    //     0x15d928c: mov             fp, SP
    // 0x15d9290: AllocStack(0x28)
    //     0x15d9290: sub             SP, SP, #0x28
    // 0x15d9294: SetupParameters(CheckoutRequestAddressPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15d9294: stur            x1, [fp, #-8]
    //     0x15d9298: stur            x2, [fp, #-0x10]
    // 0x15d929c: CheckStackOverflow
    //     0x15d929c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d92a0: cmp             SP, x16
    //     0x15d92a4: b.ls            #0x15d94d0
    // 0x15d92a8: r1 = 2
    //     0x15d92a8: movz            x1, #0x2
    // 0x15d92ac: r0 = AllocateContext()
    //     0x15d92ac: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d92b0: ldur            x1, [fp, #-8]
    // 0x15d92b4: stur            x0, [fp, #-0x18]
    // 0x15d92b8: StoreField: r0->field_f = r1
    //     0x15d92b8: stur            w1, [x0, #0xf]
    // 0x15d92bc: ldur            x2, [fp, #-0x10]
    // 0x15d92c0: StoreField: r0->field_13 = r2
    //     0x15d92c0: stur            w2, [x0, #0x13]
    // 0x15d92c4: r0 = Obx()
    //     0x15d92c4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d92c8: ldur            x2, [fp, #-0x18]
    // 0x15d92cc: r1 = Function '<anonymous closure>':.
    //     0x15d92cc: add             x1, PP, #0x44, lsl #12  ; [pp+0x442b0] AnonymousClosure: (0x15ccba8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e8384)
    //     0x15d92d0: ldr             x1, [x1, #0x2b0]
    // 0x15d92d4: stur            x0, [fp, #-0x10]
    // 0x15d92d8: r0 = AllocateClosure()
    //     0x15d92d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d92dc: mov             x1, x0
    // 0x15d92e0: ldur            x0, [fp, #-0x10]
    // 0x15d92e4: StoreField: r0->field_b = r1
    //     0x15d92e4: stur            w1, [x0, #0xb]
    // 0x15d92e8: ldur            x1, [fp, #-8]
    // 0x15d92ec: r0 = controller()
    //     0x15d92ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d92f0: LoadField: r1 = r0->field_6b
    //     0x15d92f0: ldur            w1, [x0, #0x6b]
    // 0x15d92f4: DecompressPointer r1
    //     0x15d92f4: add             x1, x1, HEAP, lsl #32
    // 0x15d92f8: r0 = value()
    //     0x15d92f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d92fc: tbnz            w0, #4, #0x15d9394
    // 0x15d9300: ldur            x2, [fp, #-0x18]
    // 0x15d9304: LoadField: r1 = r2->field_13
    //     0x15d9304: ldur            w1, [x2, #0x13]
    // 0x15d9308: DecompressPointer r1
    //     0x15d9308: add             x1, x1, HEAP, lsl #32
    // 0x15d930c: r0 = of()
    //     0x15d930c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d9310: LoadField: r1 = r0->field_5b
    //     0x15d9310: ldur            w1, [x0, #0x5b]
    // 0x15d9314: DecompressPointer r1
    //     0x15d9314: add             x1, x1, HEAP, lsl #32
    // 0x15d9318: stur            x1, [fp, #-8]
    // 0x15d931c: r0 = ColorFilter()
    //     0x15d931c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d9320: mov             x1, x0
    // 0x15d9324: ldur            x0, [fp, #-8]
    // 0x15d9328: stur            x1, [fp, #-0x20]
    // 0x15d932c: StoreField: r1->field_7 = r0
    //     0x15d932c: stur            w0, [x1, #7]
    // 0x15d9330: r0 = Instance_BlendMode
    //     0x15d9330: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d9334: ldr             x0, [x0, #0xb30]
    // 0x15d9338: StoreField: r1->field_b = r0
    //     0x15d9338: stur            w0, [x1, #0xb]
    // 0x15d933c: r2 = 1
    //     0x15d933c: movz            x2, #0x1
    // 0x15d9340: StoreField: r1->field_13 = r2
    //     0x15d9340: stur            x2, [x1, #0x13]
    // 0x15d9344: r0 = SvgPicture()
    //     0x15d9344: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d9348: stur            x0, [fp, #-8]
    // 0x15d934c: ldur            x16, [fp, #-0x20]
    // 0x15d9350: str             x16, [SP]
    // 0x15d9354: mov             x1, x0
    // 0x15d9358: r2 = "assets/images/search.svg"
    //     0x15d9358: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15d935c: ldr             x2, [x2, #0xa30]
    // 0x15d9360: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d9360: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d9364: ldr             x4, [x4, #0xa38]
    // 0x15d9368: r0 = SvgPicture.asset()
    //     0x15d9368: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d936c: r0 = Align()
    //     0x15d936c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d9370: r3 = Instance_Alignment
    //     0x15d9370: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d9374: ldr             x3, [x3, #0xb10]
    // 0x15d9378: StoreField: r0->field_f = r3
    //     0x15d9378: stur            w3, [x0, #0xf]
    // 0x15d937c: r4 = 1.000000
    //     0x15d937c: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d9380: StoreField: r0->field_13 = r4
    //     0x15d9380: stur            w4, [x0, #0x13]
    // 0x15d9384: ArrayStore: r0[0] = r4  ; List_4
    //     0x15d9384: stur            w4, [x0, #0x17]
    // 0x15d9388: ldur            x1, [fp, #-8]
    // 0x15d938c: StoreField: r0->field_b = r1
    //     0x15d938c: stur            w1, [x0, #0xb]
    // 0x15d9390: b               #0x15d9444
    // 0x15d9394: ldur            x5, [fp, #-0x18]
    // 0x15d9398: r4 = 1.000000
    //     0x15d9398: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d939c: r0 = Instance_BlendMode
    //     0x15d939c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d93a0: ldr             x0, [x0, #0xb30]
    // 0x15d93a4: r3 = Instance_Alignment
    //     0x15d93a4: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d93a8: ldr             x3, [x3, #0xb10]
    // 0x15d93ac: r2 = 1
    //     0x15d93ac: movz            x2, #0x1
    // 0x15d93b0: LoadField: r1 = r5->field_13
    //     0x15d93b0: ldur            w1, [x5, #0x13]
    // 0x15d93b4: DecompressPointer r1
    //     0x15d93b4: add             x1, x1, HEAP, lsl #32
    // 0x15d93b8: r0 = of()
    //     0x15d93b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d93bc: LoadField: r1 = r0->field_5b
    //     0x15d93bc: ldur            w1, [x0, #0x5b]
    // 0x15d93c0: DecompressPointer r1
    //     0x15d93c0: add             x1, x1, HEAP, lsl #32
    // 0x15d93c4: stur            x1, [fp, #-8]
    // 0x15d93c8: r0 = ColorFilter()
    //     0x15d93c8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d93cc: mov             x1, x0
    // 0x15d93d0: ldur            x0, [fp, #-8]
    // 0x15d93d4: stur            x1, [fp, #-0x20]
    // 0x15d93d8: StoreField: r1->field_7 = r0
    //     0x15d93d8: stur            w0, [x1, #7]
    // 0x15d93dc: r0 = Instance_BlendMode
    //     0x15d93dc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d93e0: ldr             x0, [x0, #0xb30]
    // 0x15d93e4: StoreField: r1->field_b = r0
    //     0x15d93e4: stur            w0, [x1, #0xb]
    // 0x15d93e8: r0 = 1
    //     0x15d93e8: movz            x0, #0x1
    // 0x15d93ec: StoreField: r1->field_13 = r0
    //     0x15d93ec: stur            x0, [x1, #0x13]
    // 0x15d93f0: r0 = SvgPicture()
    //     0x15d93f0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d93f4: stur            x0, [fp, #-8]
    // 0x15d93f8: ldur            x16, [fp, #-0x20]
    // 0x15d93fc: str             x16, [SP]
    // 0x15d9400: mov             x1, x0
    // 0x15d9404: r2 = "assets/images/appbar_arrow.svg"
    //     0x15d9404: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15d9408: ldr             x2, [x2, #0xa40]
    // 0x15d940c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d940c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d9410: ldr             x4, [x4, #0xa38]
    // 0x15d9414: r0 = SvgPicture.asset()
    //     0x15d9414: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d9418: r0 = Align()
    //     0x15d9418: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d941c: mov             x1, x0
    // 0x15d9420: r0 = Instance_Alignment
    //     0x15d9420: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d9424: ldr             x0, [x0, #0xb10]
    // 0x15d9428: StoreField: r1->field_f = r0
    //     0x15d9428: stur            w0, [x1, #0xf]
    // 0x15d942c: r0 = 1.000000
    //     0x15d942c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d9430: StoreField: r1->field_13 = r0
    //     0x15d9430: stur            w0, [x1, #0x13]
    // 0x15d9434: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d9434: stur            w0, [x1, #0x17]
    // 0x15d9438: ldur            x0, [fp, #-8]
    // 0x15d943c: StoreField: r1->field_b = r0
    //     0x15d943c: stur            w0, [x1, #0xb]
    // 0x15d9440: mov             x0, x1
    // 0x15d9444: stur            x0, [fp, #-8]
    // 0x15d9448: r0 = InkWell()
    //     0x15d9448: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d944c: mov             x3, x0
    // 0x15d9450: ldur            x0, [fp, #-8]
    // 0x15d9454: stur            x3, [fp, #-0x20]
    // 0x15d9458: StoreField: r3->field_b = r0
    //     0x15d9458: stur            w0, [x3, #0xb]
    // 0x15d945c: ldur            x2, [fp, #-0x18]
    // 0x15d9460: r1 = Function '<anonymous closure>':.
    //     0x15d9460: add             x1, PP, #0x44, lsl #12  ; [pp+0x442b8] AnonymousClosure: (0x15d94d8), in [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::appBar (0x15d9288)
    //     0x15d9464: ldr             x1, [x1, #0x2b8]
    // 0x15d9468: r0 = AllocateClosure()
    //     0x15d9468: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d946c: ldur            x2, [fp, #-0x20]
    // 0x15d9470: StoreField: r2->field_f = r0
    //     0x15d9470: stur            w0, [x2, #0xf]
    // 0x15d9474: r0 = true
    //     0x15d9474: add             x0, NULL, #0x20  ; true
    // 0x15d9478: StoreField: r2->field_43 = r0
    //     0x15d9478: stur            w0, [x2, #0x43]
    // 0x15d947c: r1 = Instance_BoxShape
    //     0x15d947c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d9480: ldr             x1, [x1, #0x80]
    // 0x15d9484: StoreField: r2->field_47 = r1
    //     0x15d9484: stur            w1, [x2, #0x47]
    // 0x15d9488: StoreField: r2->field_6f = r0
    //     0x15d9488: stur            w0, [x2, #0x6f]
    // 0x15d948c: r1 = false
    //     0x15d948c: add             x1, NULL, #0x30  ; false
    // 0x15d9490: StoreField: r2->field_73 = r1
    //     0x15d9490: stur            w1, [x2, #0x73]
    // 0x15d9494: StoreField: r2->field_83 = r0
    //     0x15d9494: stur            w0, [x2, #0x83]
    // 0x15d9498: StoreField: r2->field_7b = r1
    //     0x15d9498: stur            w1, [x2, #0x7b]
    // 0x15d949c: r0 = AppBar()
    //     0x15d949c: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15d94a0: stur            x0, [fp, #-8]
    // 0x15d94a4: ldur            x16, [fp, #-0x10]
    // 0x15d94a8: str             x16, [SP]
    // 0x15d94ac: mov             x1, x0
    // 0x15d94b0: ldur            x2, [fp, #-0x20]
    // 0x15d94b4: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15d94b4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15d94b8: ldr             x4, [x4, #0xf00]
    // 0x15d94bc: r0 = AppBar()
    //     0x15d94bc: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15d94c0: ldur            x0, [fp, #-8]
    // 0x15d94c4: LeaveFrame
    //     0x15d94c4: mov             SP, fp
    //     0x15d94c8: ldp             fp, lr, [SP], #0x10
    // 0x15d94cc: ret
    //     0x15d94cc: ret             
    // 0x15d94d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d94d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d94d4: b               #0x15d92a8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15d94d8, size: 0xc8
    // 0x15d94d8: EnterFrame
    //     0x15d94d8: stp             fp, lr, [SP, #-0x10]!
    //     0x15d94dc: mov             fp, SP
    // 0x15d94e0: AllocStack(0x18)
    //     0x15d94e0: sub             SP, SP, #0x18
    // 0x15d94e4: SetupParameters()
    //     0x15d94e4: ldr             x0, [fp, #0x10]
    //     0x15d94e8: ldur            w3, [x0, #0x17]
    //     0x15d94ec: add             x3, x3, HEAP, lsl #32
    //     0x15d94f0: stur            x3, [fp, #-8]
    // 0x15d94f4: CheckStackOverflow
    //     0x15d94f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d94f8: cmp             SP, x16
    //     0x15d94fc: b.ls            #0x15d9598
    // 0x15d9500: LoadField: r1 = r3->field_f
    //     0x15d9500: ldur            w1, [x3, #0xf]
    // 0x15d9504: DecompressPointer r1
    //     0x15d9504: add             x1, x1, HEAP, lsl #32
    // 0x15d9508: r2 = false
    //     0x15d9508: add             x2, NULL, #0x30  ; false
    // 0x15d950c: r0 = showLoading()
    //     0x15d950c: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15d9510: ldur            x0, [fp, #-8]
    // 0x15d9514: LoadField: r1 = r0->field_f
    //     0x15d9514: ldur            w1, [x0, #0xf]
    // 0x15d9518: DecompressPointer r1
    //     0x15d9518: add             x1, x1, HEAP, lsl #32
    // 0x15d951c: r0 = controller()
    //     0x15d951c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d9520: LoadField: r1 = r0->field_6b
    //     0x15d9520: ldur            w1, [x0, #0x6b]
    // 0x15d9524: DecompressPointer r1
    //     0x15d9524: add             x1, x1, HEAP, lsl #32
    // 0x15d9528: r0 = value()
    //     0x15d9528: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d952c: tbnz            w0, #4, #0x15d9564
    // 0x15d9530: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15d9530: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15d9534: ldr             x0, [x0, #0x1c80]
    //     0x15d9538: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15d953c: cmp             w0, w16
    //     0x15d9540: b.ne            #0x15d954c
    //     0x15d9544: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15d9548: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15d954c: r16 = "/search"
    //     0x15d954c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15d9550: ldr             x16, [x16, #0x838]
    // 0x15d9554: stp             x16, NULL, [SP]
    // 0x15d9558: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15d9558: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15d955c: r0 = GetNavigation.toNamed()
    //     0x15d955c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15d9560: b               #0x15d9588
    // 0x15d9564: ldur            x0, [fp, #-8]
    // 0x15d9568: LoadField: r1 = r0->field_f
    //     0x15d9568: ldur            w1, [x0, #0xf]
    // 0x15d956c: DecompressPointer r1
    //     0x15d956c: add             x1, x1, HEAP, lsl #32
    // 0x15d9570: r2 = false
    //     0x15d9570: add             x2, NULL, #0x30  ; false
    // 0x15d9574: r0 = showLoading()
    //     0x15d9574: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15d9578: ldur            x0, [fp, #-8]
    // 0x15d957c: LoadField: r1 = r0->field_f
    //     0x15d957c: ldur            w1, [x0, #0xf]
    // 0x15d9580: DecompressPointer r1
    //     0x15d9580: add             x1, x1, HEAP, lsl #32
    // 0x15d9584: r0 = getBack()
    //     0x15d9584: bl              #0x1485230  ; [package:customer_app/app/presentation/views/cosmetic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack
    // 0x15d9588: r0 = Null
    //     0x15d9588: mov             x0, NULL
    // 0x15d958c: LeaveFrame
    //     0x15d958c: mov             SP, fp
    //     0x15d9590: ldp             fp, lr, [SP], #0x10
    // 0x15d9594: ret
    //     0x15d9594: ret             
    // 0x15d9598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d9598: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d959c: b               #0x15d9500
  }
}
