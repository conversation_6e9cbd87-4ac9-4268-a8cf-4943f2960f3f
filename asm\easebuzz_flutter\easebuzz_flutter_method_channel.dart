// lib: , url: package:easebuzz_flutter/easebuzz_flutter_method_channel.dart

// class id: 1049622, size: 0x8
class :: {
}

// class id: 5913, size: 0x8, field offset: 0x8
class MethodChannelEasebuzzFlutter extends EasebuzzFlutterPlatform {

  _ payWithEasebuzz(/* No info */) async {
    // ** addr: 0x12d867c, size: 0x130
    // 0x12d867c: EnterFrame
    //     0x12d867c: stp             fp, lr, [SP, #-0x10]!
    //     0x12d8680: mov             fp, SP
    // 0x12d8684: AllocStack(0xb0)
    //     0x12d8684: sub             SP, SP, #0xb0
    // 0x12d8688: SetupParameters(MethodChannelEasebuzzFlutter this /* r1 => r1, fp-0x80 */, dynamic _ /* r2 => r2, fp-0x88 */)
    //     0x12d8688: stur            NULL, [fp, #-8]
    //     0x12d868c: stur            x1, [fp, #-0x80]
    //     0x12d8690: stur            x2, [fp, #-0x88]
    // 0x12d8694: CheckStackOverflow
    //     0x12d8694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12d8698: cmp             SP, x16
    //     0x12d869c: b.ls            #0x12d87a4
    // 0x12d86a0: InitAsync() -> Future<Map<String, dynamic>?>
    //     0x12d86a0: add             x0, PP, #0x25, lsl #12  ; [pp+0x25e30] TypeArguments: <Map<String, dynamic>?>
    //     0x12d86a4: ldr             x0, [x0, #0xe30]
    //     0x12d86a8: bl              #0x6326e0  ; InitAsyncStub
    // 0x12d86ac: ldur            x0, [fp, #-0x88]
    // 0x12d86b0: r1 = Null
    //     0x12d86b0: mov             x1, NULL
    // 0x12d86b4: r2 = 8
    //     0x12d86b4: movz            x2, #0x8
    // 0x12d86b8: r0 = AllocateArray()
    //     0x12d86b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12d86bc: r16 = "access_key"
    //     0x12d86bc: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a738] "access_key"
    //     0x12d86c0: ldr             x16, [x16, #0x738]
    // 0x12d86c4: StoreField: r0->field_f = r16
    //     0x12d86c4: stur            w16, [x0, #0xf]
    // 0x12d86c8: ldur            x1, [fp, #-0x88]
    // 0x12d86cc: StoreField: r0->field_13 = r1
    //     0x12d86cc: stur            w1, [x0, #0x13]
    // 0x12d86d0: r16 = "pay_mode"
    //     0x12d86d0: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a740] "pay_mode"
    //     0x12d86d4: ldr             x16, [x16, #0x740]
    // 0x12d86d8: ArrayStore: r0[0] = r16  ; List_4
    //     0x12d86d8: stur            w16, [x0, #0x17]
    // 0x12d86dc: r16 = "production"
    //     0x12d86dc: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a730] "production"
    //     0x12d86e0: ldr             x16, [x16, #0x730]
    // 0x12d86e4: StoreField: r0->field_1b = r16
    //     0x12d86e4: stur            w16, [x0, #0x1b]
    // 0x12d86e8: r16 = <String, dynamic>
    //     0x12d86e8: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x12d86ec: stp             x0, x16, [SP]
    // 0x12d86f0: r0 = Map._fromLiteral()
    //     0x12d86f0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x12d86f4: stur            x0, [fp, #-0x80]
    // 0x12d86f8: r16 = <String, dynamic>
    //     0x12d86f8: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x12d86fc: r30 = Instance_MethodChannel
    //     0x12d86fc: add             lr, PP, #0x3a, lsl #12  ; [pp+0x3a748] Obj!MethodChannel@d56101
    //     0x12d8700: ldr             lr, [lr, #0x748]
    // 0x12d8704: stp             lr, x16, [SP, #0x10]
    // 0x12d8708: r16 = "payWithEasebuzz"
    //     0x12d8708: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a750] "payWithEasebuzz"
    //     0x12d870c: ldr             x16, [x16, #0x750]
    // 0x12d8710: stp             x0, x16, [SP]
    // 0x12d8714: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0x12d8714: ldr             x4, [PP, #0x32e0]  ; [pp+0x32e0] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    // 0x12d8718: r0 = invokeMapMethod()
    //     0x12d8718: bl              #0x69ad70  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0x12d871c: mov             x1, x0
    // 0x12d8720: stur            x1, [fp, #-0x90]
    // 0x12d8724: r0 = Await()
    //     0x12d8724: bl              #0x63248c  ; AwaitStub
    // 0x12d8728: mov             x1, x0
    // 0x12d872c: stur            x1, [fp, #-0x80]
    // 0x12d8730: cmp             w1, NULL
    // 0x12d8734: b.ne            #0x12d8740
    // 0x12d8738: r0 = Null
    //     0x12d8738: mov             x0, NULL
    // 0x12d873c: b               #0x12d8760
    // 0x12d8740: r0 = LoadClassIdInstr(r1)
    //     0x12d8740: ldur            x0, [x1, #-1]
    //     0x12d8744: ubfx            x0, x0, #0xc, #0x14
    // 0x12d8748: r16 = <String, dynamic>
    //     0x12d8748: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x12d874c: stp             x1, x16, [SP]
    // 0x12d8750: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x12d8750: ldr             x4, [PP, #0x1898]  ; [pp+0x1898] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x12d8754: r0 = GDT[cid_x0 + 0x6b0]()
    //     0x12d8754: add             lr, x0, #0x6b0
    //     0x12d8758: ldr             lr, [x21, lr, lsl #3]
    //     0x12d875c: blr             lr
    // 0x12d8760: r0 = ReturnAsyncNotFuture()
    //     0x12d8760: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x12d8764: sub             SP, fp, #0xb0
    // 0x12d8768: stur            x0, [fp, #-0x80]
    // 0x12d876c: r1 = Null
    //     0x12d876c: mov             x1, NULL
    // 0x12d8770: r2 = 4
    //     0x12d8770: movz            x2, #0x4
    // 0x12d8774: r0 = AllocateArray()
    //     0x12d8774: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12d8778: r16 = "Error during payment: "
    //     0x12d8778: add             x16, PP, #0x3a, lsl #12  ; [pp+0x3a758] "Error during payment: "
    //     0x12d877c: ldr             x16, [x16, #0x758]
    // 0x12d8780: StoreField: r0->field_f = r16
    //     0x12d8780: stur            w16, [x0, #0xf]
    // 0x12d8784: ldur            x1, [fp, #-0x80]
    // 0x12d8788: StoreField: r0->field_13 = r1
    //     0x12d8788: stur            w1, [x0, #0x13]
    // 0x12d878c: str             x0, [SP]
    // 0x12d8790: r0 = _interpolate()
    //     0x12d8790: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x12d8794: mov             x1, x0
    // 0x12d8798: r0 = print()
    //     0x12d8798: bl              #0x636d0c  ; [dart:core] ::print
    // 0x12d879c: r0 = Null
    //     0x12d879c: mov             x0, NULL
    // 0x12d87a0: r0 = ReturnAsyncNotFuture()
    //     0x12d87a0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x12d87a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12d87a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12d87a8: b               #0x12d86a0
  }
}
