// lib: , url: package:customer_app/app/presentation/views/glass/browse/browse_accordion.dart

// class id: 1049351, size: 0x8
class :: {
}

// class id: 3374, size: 0x18, field offset: 0x14
class _AccordionState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb39854, size: 0x498
    // 0xb39854: EnterFrame
    //     0xb39854: stp             fp, lr, [SP, #-0x10]!
    //     0xb39858: mov             fp, SP
    // 0xb3985c: AllocStack(0x50)
    //     0xb3985c: sub             SP, SP, #0x50
    // 0xb39860: SetupParameters(_AccordionState this /* r1 => r1, fp-0x8 */)
    //     0xb39860: stur            x1, [fp, #-8]
    // 0xb39864: CheckStackOverflow
    //     0xb39864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb39868: cmp             SP, x16
    //     0xb3986c: b.ls            #0xb39ce0
    // 0xb39870: r1 = 1
    //     0xb39870: movz            x1, #0x1
    // 0xb39874: r0 = AllocateContext()
    //     0xb39874: bl              #0x16f6108  ; AllocateContextStub
    // 0xb39878: mov             x1, x0
    // 0xb3987c: ldur            x0, [fp, #-8]
    // 0xb39880: stur            x1, [fp, #-0x10]
    // 0xb39884: StoreField: r1->field_f = r0
    //     0xb39884: stur            w0, [x1, #0xf]
    // 0xb39888: r0 = Radius()
    //     0xb39888: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb3988c: d0 = 30.000000
    //     0xb3988c: fmov            d0, #30.00000000
    // 0xb39890: stur            x0, [fp, #-0x18]
    // 0xb39894: StoreField: r0->field_7 = d0
    //     0xb39894: stur            d0, [x0, #7]
    // 0xb39898: StoreField: r0->field_f = d0
    //     0xb39898: stur            d0, [x0, #0xf]
    // 0xb3989c: r0 = BorderRadius()
    //     0xb3989c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb398a0: mov             x1, x0
    // 0xb398a4: ldur            x0, [fp, #-0x18]
    // 0xb398a8: stur            x1, [fp, #-0x20]
    // 0xb398ac: StoreField: r1->field_7 = r0
    //     0xb398ac: stur            w0, [x1, #7]
    // 0xb398b0: StoreField: r1->field_b = r0
    //     0xb398b0: stur            w0, [x1, #0xb]
    // 0xb398b4: StoreField: r1->field_f = r0
    //     0xb398b4: stur            w0, [x1, #0xf]
    // 0xb398b8: StoreField: r1->field_13 = r0
    //     0xb398b8: stur            w0, [x1, #0x13]
    // 0xb398bc: r0 = RoundedRectangleBorder()
    //     0xb398bc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb398c0: mov             x1, x0
    // 0xb398c4: ldur            x0, [fp, #-0x20]
    // 0xb398c8: stur            x1, [fp, #-0x28]
    // 0xb398cc: StoreField: r1->field_b = r0
    //     0xb398cc: stur            w0, [x1, #0xb]
    // 0xb398d0: r0 = Instance_BorderSide
    //     0xb398d0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb398d4: ldr             x0, [x0, #0xe20]
    // 0xb398d8: StoreField: r1->field_7 = r0
    //     0xb398d8: stur            w0, [x1, #7]
    // 0xb398dc: ldur            x0, [fp, #-8]
    // 0xb398e0: LoadField: r2 = r0->field_b
    //     0xb398e0: ldur            w2, [x0, #0xb]
    // 0xb398e4: DecompressPointer r2
    //     0xb398e4: add             x2, x2, HEAP, lsl #32
    // 0xb398e8: stur            x2, [fp, #-0x20]
    // 0xb398ec: cmp             w2, NULL
    // 0xb398f0: b.eq            #0xb39ce8
    // 0xb398f4: LoadField: r3 = r2->field_b
    //     0xb398f4: ldur            w3, [x2, #0xb]
    // 0xb398f8: DecompressPointer r3
    //     0xb398f8: add             x3, x3, HEAP, lsl #32
    // 0xb398fc: stur            x3, [fp, #-0x18]
    // 0xb39900: r0 = InkWell()
    //     0xb39900: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb39904: mov             x3, x0
    // 0xb39908: ldur            x0, [fp, #-0x18]
    // 0xb3990c: stur            x3, [fp, #-0x30]
    // 0xb39910: StoreField: r3->field_b = r0
    //     0xb39910: stur            w0, [x3, #0xb]
    // 0xb39914: ldur            x2, [fp, #-0x10]
    // 0xb39918: r1 = Function '<anonymous closure>':.
    //     0xb39918: add             x1, PP, #0x57, lsl #12  ; [pp+0x57168] AnonymousClosure: (0xb39d70), in [package:customer_app/app/presentation/views/glass/browse/browse_accordion.dart] _AccordionState::build (0xb39854)
    //     0xb3991c: ldr             x1, [x1, #0x168]
    // 0xb39920: r0 = AllocateClosure()
    //     0xb39920: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb39924: mov             x1, x0
    // 0xb39928: ldur            x0, [fp, #-0x30]
    // 0xb3992c: StoreField: r0->field_f = r1
    //     0xb3992c: stur            w1, [x0, #0xf]
    // 0xb39930: r1 = true
    //     0xb39930: add             x1, NULL, #0x20  ; true
    // 0xb39934: StoreField: r0->field_43 = r1
    //     0xb39934: stur            w1, [x0, #0x43]
    // 0xb39938: r2 = Instance_BoxShape
    //     0xb39938: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb3993c: ldr             x2, [x2, #0x80]
    // 0xb39940: StoreField: r0->field_47 = r2
    //     0xb39940: stur            w2, [x0, #0x47]
    // 0xb39944: StoreField: r0->field_6f = r1
    //     0xb39944: stur            w1, [x0, #0x6f]
    // 0xb39948: r3 = false
    //     0xb39948: add             x3, NULL, #0x30  ; false
    // 0xb3994c: StoreField: r0->field_73 = r3
    //     0xb3994c: stur            w3, [x0, #0x73]
    // 0xb39950: StoreField: r0->field_83 = r1
    //     0xb39950: stur            w1, [x0, #0x83]
    // 0xb39954: StoreField: r0->field_7b = r3
    //     0xb39954: stur            w3, [x0, #0x7b]
    // 0xb39958: ldur            x4, [fp, #-8]
    // 0xb3995c: LoadField: r5 = r4->field_13
    //     0xb3995c: ldur            w5, [x4, #0x13]
    // 0xb39960: DecompressPointer r5
    //     0xb39960: add             x5, x5, HEAP, lsl #32
    // 0xb39964: stur            x5, [fp, #-0x18]
    // 0xb39968: tbnz            w5, #4, #0xb39980
    // 0xb3996c: ldur            x4, [fp, #-0x20]
    // 0xb39970: LoadField: r6 = r4->field_1f
    //     0xb39970: ldur            w6, [x4, #0x1f]
    // 0xb39974: DecompressPointer r6
    //     0xb39974: add             x6, x6, HEAP, lsl #32
    // 0xb39978: mov             x7, x6
    // 0xb3997c: b               #0xb39990
    // 0xb39980: ldur            x4, [fp, #-0x20]
    // 0xb39984: LoadField: r6 = r4->field_1b
    //     0xb39984: ldur            w6, [x4, #0x1b]
    // 0xb39988: DecompressPointer r6
    //     0xb39988: add             x6, x6, HEAP, lsl #32
    // 0xb3998c: mov             x7, x6
    // 0xb39990: ldur            x6, [fp, #-0x28]
    // 0xb39994: stur            x7, [fp, #-8]
    // 0xb39998: r0 = InkWell()
    //     0xb39998: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb3999c: mov             x3, x0
    // 0xb399a0: ldur            x0, [fp, #-8]
    // 0xb399a4: stur            x3, [fp, #-0x38]
    // 0xb399a8: StoreField: r3->field_b = r0
    //     0xb399a8: stur            w0, [x3, #0xb]
    // 0xb399ac: ldur            x2, [fp, #-0x10]
    // 0xb399b0: r1 = Function '<anonymous closure>':.
    //     0xb399b0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57170] AnonymousClosure: (0xb39d10), in [package:customer_app/app/presentation/views/glass/browse/browse_accordion.dart] _AccordionState::build (0xb39854)
    //     0xb399b4: ldr             x1, [x1, #0x170]
    // 0xb399b8: r0 = AllocateClosure()
    //     0xb399b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb399bc: mov             x1, x0
    // 0xb399c0: ldur            x0, [fp, #-0x38]
    // 0xb399c4: StoreField: r0->field_f = r1
    //     0xb399c4: stur            w1, [x0, #0xf]
    // 0xb399c8: r3 = true
    //     0xb399c8: add             x3, NULL, #0x20  ; true
    // 0xb399cc: StoreField: r0->field_43 = r3
    //     0xb399cc: stur            w3, [x0, #0x43]
    // 0xb399d0: r1 = Instance_BoxShape
    //     0xb399d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb399d4: ldr             x1, [x1, #0x80]
    // 0xb399d8: StoreField: r0->field_47 = r1
    //     0xb399d8: stur            w1, [x0, #0x47]
    // 0xb399dc: StoreField: r0->field_6f = r3
    //     0xb399dc: stur            w3, [x0, #0x6f]
    // 0xb399e0: r1 = false
    //     0xb399e0: add             x1, NULL, #0x30  ; false
    // 0xb399e4: StoreField: r0->field_73 = r1
    //     0xb399e4: stur            w1, [x0, #0x73]
    // 0xb399e8: StoreField: r0->field_83 = r3
    //     0xb399e8: stur            w3, [x0, #0x83]
    // 0xb399ec: StoreField: r0->field_7b = r1
    //     0xb399ec: stur            w1, [x0, #0x7b]
    // 0xb399f0: r1 = Null
    //     0xb399f0: mov             x1, NULL
    // 0xb399f4: r2 = 6
    //     0xb399f4: movz            x2, #0x6
    // 0xb399f8: r0 = AllocateArray()
    //     0xb399f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb399fc: mov             x2, x0
    // 0xb39a00: ldur            x0, [fp, #-0x30]
    // 0xb39a04: stur            x2, [fp, #-8]
    // 0xb39a08: StoreField: r2->field_f = r0
    //     0xb39a08: stur            w0, [x2, #0xf]
    // 0xb39a0c: r16 = Instance_Spacer
    //     0xb39a0c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb39a10: ldr             x16, [x16, #0xf0]
    // 0xb39a14: StoreField: r2->field_13 = r16
    //     0xb39a14: stur            w16, [x2, #0x13]
    // 0xb39a18: ldur            x0, [fp, #-0x38]
    // 0xb39a1c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb39a1c: stur            w0, [x2, #0x17]
    // 0xb39a20: r1 = <Widget>
    //     0xb39a20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb39a24: r0 = AllocateGrowableArray()
    //     0xb39a24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb39a28: mov             x1, x0
    // 0xb39a2c: ldur            x0, [fp, #-8]
    // 0xb39a30: stur            x1, [fp, #-0x10]
    // 0xb39a34: StoreField: r1->field_f = r0
    //     0xb39a34: stur            w0, [x1, #0xf]
    // 0xb39a38: r0 = 6
    //     0xb39a38: movz            x0, #0x6
    // 0xb39a3c: StoreField: r1->field_b = r0
    //     0xb39a3c: stur            w0, [x1, #0xb]
    // 0xb39a40: r0 = Row()
    //     0xb39a40: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb39a44: mov             x1, x0
    // 0xb39a48: r0 = Instance_Axis
    //     0xb39a48: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb39a4c: stur            x1, [fp, #-8]
    // 0xb39a50: StoreField: r1->field_f = r0
    //     0xb39a50: stur            w0, [x1, #0xf]
    // 0xb39a54: r0 = Instance_MainAxisAlignment
    //     0xb39a54: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4daf8] Obj!MainAxisAlignment@d734e1
    //     0xb39a58: ldr             x0, [x0, #0xaf8]
    // 0xb39a5c: StoreField: r1->field_13 = r0
    //     0xb39a5c: stur            w0, [x1, #0x13]
    // 0xb39a60: r0 = Instance_MainAxisSize
    //     0xb39a60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb39a64: ldr             x0, [x0, #0xa10]
    // 0xb39a68: ArrayStore: r1[0] = r0  ; List_4
    //     0xb39a68: stur            w0, [x1, #0x17]
    // 0xb39a6c: r2 = Instance_CrossAxisAlignment
    //     0xb39a6c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb39a70: ldr             x2, [x2, #0x890]
    // 0xb39a74: StoreField: r1->field_1b = r2
    //     0xb39a74: stur            w2, [x1, #0x1b]
    // 0xb39a78: r2 = Instance_VerticalDirection
    //     0xb39a78: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb39a7c: ldr             x2, [x2, #0xa20]
    // 0xb39a80: StoreField: r1->field_23 = r2
    //     0xb39a80: stur            w2, [x1, #0x23]
    // 0xb39a84: r3 = Instance_Clip
    //     0xb39a84: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb39a88: ldr             x3, [x3, #0x38]
    // 0xb39a8c: StoreField: r1->field_2b = r3
    //     0xb39a8c: stur            w3, [x1, #0x2b]
    // 0xb39a90: StoreField: r1->field_2f = rZR
    //     0xb39a90: stur            xzr, [x1, #0x2f]
    // 0xb39a94: ldur            x4, [fp, #-0x10]
    // 0xb39a98: StoreField: r1->field_b = r4
    //     0xb39a98: stur            w4, [x1, #0xb]
    // 0xb39a9c: r0 = Padding()
    //     0xb39a9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb39aa0: mov             x1, x0
    // 0xb39aa4: r0 = Instance_EdgeInsets
    //     0xb39aa4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb39aa8: ldr             x0, [x0, #0x1f0]
    // 0xb39aac: stur            x1, [fp, #-0x10]
    // 0xb39ab0: StoreField: r1->field_f = r0
    //     0xb39ab0: stur            w0, [x1, #0xf]
    // 0xb39ab4: ldur            x0, [fp, #-8]
    // 0xb39ab8: StoreField: r1->field_b = r0
    //     0xb39ab8: stur            w0, [x1, #0xb]
    // 0xb39abc: r0 = Card()
    //     0xb39abc: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb39ac0: mov             x3, x0
    // 0xb39ac4: r0 = 0.000000
    //     0xb39ac4: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb39ac8: stur            x3, [fp, #-8]
    // 0xb39acc: ArrayStore: r3[0] = r0  ; List_4
    //     0xb39acc: stur            w0, [x3, #0x17]
    // 0xb39ad0: ldur            x0, [fp, #-0x28]
    // 0xb39ad4: StoreField: r3->field_1b = r0
    //     0xb39ad4: stur            w0, [x3, #0x1b]
    // 0xb39ad8: r0 = true
    //     0xb39ad8: add             x0, NULL, #0x20  ; true
    // 0xb39adc: StoreField: r3->field_1f = r0
    //     0xb39adc: stur            w0, [x3, #0x1f]
    // 0xb39ae0: ldur            x1, [fp, #-0x10]
    // 0xb39ae4: StoreField: r3->field_2f = r1
    //     0xb39ae4: stur            w1, [x3, #0x2f]
    // 0xb39ae8: StoreField: r3->field_2b = r0
    //     0xb39ae8: stur            w0, [x3, #0x2b]
    // 0xb39aec: r0 = Instance__CardVariant
    //     0xb39aec: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb39af0: ldr             x0, [x0, #0xa68]
    // 0xb39af4: StoreField: r3->field_33 = r0
    //     0xb39af4: stur            w0, [x3, #0x33]
    // 0xb39af8: r1 = Null
    //     0xb39af8: mov             x1, NULL
    // 0xb39afc: r2 = 2
    //     0xb39afc: movz            x2, #0x2
    // 0xb39b00: r0 = AllocateArray()
    //     0xb39b00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb39b04: mov             x2, x0
    // 0xb39b08: ldur            x0, [fp, #-8]
    // 0xb39b0c: stur            x2, [fp, #-0x10]
    // 0xb39b10: StoreField: r2->field_f = r0
    //     0xb39b10: stur            w0, [x2, #0xf]
    // 0xb39b14: r1 = <Widget>
    //     0xb39b14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb39b18: r0 = AllocateGrowableArray()
    //     0xb39b18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb39b1c: mov             x1, x0
    // 0xb39b20: ldur            x0, [fp, #-0x10]
    // 0xb39b24: stur            x1, [fp, #-0x28]
    // 0xb39b28: StoreField: r1->field_f = r0
    //     0xb39b28: stur            w0, [x1, #0xf]
    // 0xb39b2c: r0 = 2
    //     0xb39b2c: movz            x0, #0x2
    // 0xb39b30: StoreField: r1->field_b = r0
    //     0xb39b30: stur            w0, [x1, #0xb]
    // 0xb39b34: ldur            x0, [fp, #-0x18]
    // 0xb39b38: tbnz            w0, #4, #0xb39bf0
    // 0xb39b3c: ldur            x0, [fp, #-0x20]
    // 0xb39b40: LoadField: r2 = r0->field_f
    //     0xb39b40: ldur            w2, [x0, #0xf]
    // 0xb39b44: DecompressPointer r2
    //     0xb39b44: add             x2, x2, HEAP, lsl #32
    // 0xb39b48: stur            x2, [fp, #-8]
    // 0xb39b4c: r0 = Container()
    //     0xb39b4c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb39b50: stur            x0, [fp, #-0x10]
    // 0xb39b54: r16 = Instance_EdgeInsets
    //     0xb39b54: add             x16, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb39b58: ldr             x16, [x16, #0x850]
    // 0xb39b5c: ldur            lr, [fp, #-8]
    // 0xb39b60: stp             lr, x16, [SP]
    // 0xb39b64: mov             x1, x0
    // 0xb39b68: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb39b68: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb39b6c: ldr             x4, [x4, #0x30]
    // 0xb39b70: r0 = Container()
    //     0xb39b70: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb39b74: ldur            x0, [fp, #-0x28]
    // 0xb39b78: LoadField: r1 = r0->field_b
    //     0xb39b78: ldur            w1, [x0, #0xb]
    // 0xb39b7c: LoadField: r2 = r0->field_f
    //     0xb39b7c: ldur            w2, [x0, #0xf]
    // 0xb39b80: DecompressPointer r2
    //     0xb39b80: add             x2, x2, HEAP, lsl #32
    // 0xb39b84: LoadField: r3 = r2->field_b
    //     0xb39b84: ldur            w3, [x2, #0xb]
    // 0xb39b88: r2 = LoadInt32Instr(r1)
    //     0xb39b88: sbfx            x2, x1, #1, #0x1f
    // 0xb39b8c: stur            x2, [fp, #-0x40]
    // 0xb39b90: r1 = LoadInt32Instr(r3)
    //     0xb39b90: sbfx            x1, x3, #1, #0x1f
    // 0xb39b94: cmp             x2, x1
    // 0xb39b98: b.ne            #0xb39ba4
    // 0xb39b9c: mov             x1, x0
    // 0xb39ba0: r0 = _growToNextCapacity()
    //     0xb39ba0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb39ba4: ldur            x2, [fp, #-0x28]
    // 0xb39ba8: ldur            x3, [fp, #-0x40]
    // 0xb39bac: add             x0, x3, #1
    // 0xb39bb0: lsl             x1, x0, #1
    // 0xb39bb4: StoreField: r2->field_b = r1
    //     0xb39bb4: stur            w1, [x2, #0xb]
    // 0xb39bb8: LoadField: r1 = r2->field_f
    //     0xb39bb8: ldur            w1, [x2, #0xf]
    // 0xb39bbc: DecompressPointer r1
    //     0xb39bbc: add             x1, x1, HEAP, lsl #32
    // 0xb39bc0: ldur            x0, [fp, #-0x10]
    // 0xb39bc4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb39bc4: add             x25, x1, x3, lsl #2
    //     0xb39bc8: add             x25, x25, #0xf
    //     0xb39bcc: str             w0, [x25]
    //     0xb39bd0: tbz             w0, #0, #0xb39bec
    //     0xb39bd4: ldurb           w16, [x1, #-1]
    //     0xb39bd8: ldurb           w17, [x0, #-1]
    //     0xb39bdc: and             x16, x17, x16, lsr #2
    //     0xb39be0: tst             x16, HEAP, lsr #32
    //     0xb39be4: b.eq            #0xb39bec
    //     0xb39be8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb39bec: b               #0xb39c80
    // 0xb39bf0: mov             x2, x1
    // 0xb39bf4: r0 = Container()
    //     0xb39bf4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb39bf8: mov             x1, x0
    // 0xb39bfc: stur            x0, [fp, #-8]
    // 0xb39c00: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb39c00: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb39c04: r0 = Container()
    //     0xb39c04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb39c08: ldur            x0, [fp, #-0x28]
    // 0xb39c0c: LoadField: r1 = r0->field_b
    //     0xb39c0c: ldur            w1, [x0, #0xb]
    // 0xb39c10: LoadField: r2 = r0->field_f
    //     0xb39c10: ldur            w2, [x0, #0xf]
    // 0xb39c14: DecompressPointer r2
    //     0xb39c14: add             x2, x2, HEAP, lsl #32
    // 0xb39c18: LoadField: r3 = r2->field_b
    //     0xb39c18: ldur            w3, [x2, #0xb]
    // 0xb39c1c: r2 = LoadInt32Instr(r1)
    //     0xb39c1c: sbfx            x2, x1, #1, #0x1f
    // 0xb39c20: stur            x2, [fp, #-0x40]
    // 0xb39c24: r1 = LoadInt32Instr(r3)
    //     0xb39c24: sbfx            x1, x3, #1, #0x1f
    // 0xb39c28: cmp             x2, x1
    // 0xb39c2c: b.ne            #0xb39c38
    // 0xb39c30: mov             x1, x0
    // 0xb39c34: r0 = _growToNextCapacity()
    //     0xb39c34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb39c38: ldur            x2, [fp, #-0x28]
    // 0xb39c3c: ldur            x3, [fp, #-0x40]
    // 0xb39c40: add             x0, x3, #1
    // 0xb39c44: lsl             x1, x0, #1
    // 0xb39c48: StoreField: r2->field_b = r1
    //     0xb39c48: stur            w1, [x2, #0xb]
    // 0xb39c4c: LoadField: r1 = r2->field_f
    //     0xb39c4c: ldur            w1, [x2, #0xf]
    // 0xb39c50: DecompressPointer r1
    //     0xb39c50: add             x1, x1, HEAP, lsl #32
    // 0xb39c54: ldur            x0, [fp, #-8]
    // 0xb39c58: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb39c58: add             x25, x1, x3, lsl #2
    //     0xb39c5c: add             x25, x25, #0xf
    //     0xb39c60: str             w0, [x25]
    //     0xb39c64: tbz             w0, #0, #0xb39c80
    //     0xb39c68: ldurb           w16, [x1, #-1]
    //     0xb39c6c: ldurb           w17, [x0, #-1]
    //     0xb39c70: and             x16, x17, x16, lsr #2
    //     0xb39c74: tst             x16, HEAP, lsr #32
    //     0xb39c78: b.eq            #0xb39c80
    //     0xb39c7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb39c80: r0 = Column()
    //     0xb39c80: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb39c84: r1 = Instance_Axis
    //     0xb39c84: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb39c88: StoreField: r0->field_f = r1
    //     0xb39c88: stur            w1, [x0, #0xf]
    // 0xb39c8c: r1 = Instance_MainAxisAlignment
    //     0xb39c8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb39c90: ldr             x1, [x1, #0xa08]
    // 0xb39c94: StoreField: r0->field_13 = r1
    //     0xb39c94: stur            w1, [x0, #0x13]
    // 0xb39c98: r1 = Instance_MainAxisSize
    //     0xb39c98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb39c9c: ldr             x1, [x1, #0xa10]
    // 0xb39ca0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb39ca0: stur            w1, [x0, #0x17]
    // 0xb39ca4: r1 = Instance_CrossAxisAlignment
    //     0xb39ca4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb39ca8: ldr             x1, [x1, #0xa18]
    // 0xb39cac: StoreField: r0->field_1b = r1
    //     0xb39cac: stur            w1, [x0, #0x1b]
    // 0xb39cb0: r1 = Instance_VerticalDirection
    //     0xb39cb0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb39cb4: ldr             x1, [x1, #0xa20]
    // 0xb39cb8: StoreField: r0->field_23 = r1
    //     0xb39cb8: stur            w1, [x0, #0x23]
    // 0xb39cbc: r1 = Instance_Clip
    //     0xb39cbc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb39cc0: ldr             x1, [x1, #0x38]
    // 0xb39cc4: StoreField: r0->field_2b = r1
    //     0xb39cc4: stur            w1, [x0, #0x2b]
    // 0xb39cc8: StoreField: r0->field_2f = rZR
    //     0xb39cc8: stur            xzr, [x0, #0x2f]
    // 0xb39ccc: ldur            x1, [fp, #-0x28]
    // 0xb39cd0: StoreField: r0->field_b = r1
    //     0xb39cd0: stur            w1, [x0, #0xb]
    // 0xb39cd4: LeaveFrame
    //     0xb39cd4: mov             SP, fp
    //     0xb39cd8: ldp             fp, lr, [SP], #0x10
    // 0xb39cdc: ret
    //     0xb39cdc: ret             
    // 0xb39ce0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb39ce0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb39ce4: b               #0xb39870
    // 0xb39ce8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb39ce8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb39d10, size: 0x60
    // 0xb39d10: EnterFrame
    //     0xb39d10: stp             fp, lr, [SP, #-0x10]!
    //     0xb39d14: mov             fp, SP
    // 0xb39d18: AllocStack(0x8)
    //     0xb39d18: sub             SP, SP, #8
    // 0xb39d1c: SetupParameters()
    //     0xb39d1c: ldr             x0, [fp, #0x10]
    //     0xb39d20: ldur            w2, [x0, #0x17]
    //     0xb39d24: add             x2, x2, HEAP, lsl #32
    // 0xb39d28: CheckStackOverflow
    //     0xb39d28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb39d2c: cmp             SP, x16
    //     0xb39d30: b.ls            #0xb39d68
    // 0xb39d34: LoadField: r0 = r2->field_f
    //     0xb39d34: ldur            w0, [x2, #0xf]
    // 0xb39d38: DecompressPointer r0
    //     0xb39d38: add             x0, x0, HEAP, lsl #32
    // 0xb39d3c: stur            x0, [fp, #-8]
    // 0xb39d40: r1 = Function '<anonymous closure>':.
    //     0xb39d40: add             x1, PP, #0x57, lsl #12  ; [pp+0x57178] AnonymousClosure: (0x9a129c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xb39d44: ldr             x1, [x1, #0x178]
    // 0xb39d48: r0 = AllocateClosure()
    //     0xb39d48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb39d4c: ldur            x1, [fp, #-8]
    // 0xb39d50: mov             x2, x0
    // 0xb39d54: r0 = setState()
    //     0xb39d54: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb39d58: r0 = Null
    //     0xb39d58: mov             x0, NULL
    // 0xb39d5c: LeaveFrame
    //     0xb39d5c: mov             SP, fp
    //     0xb39d60: ldp             fp, lr, [SP], #0x10
    // 0xb39d64: ret
    //     0xb39d64: ret             
    // 0xb39d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb39d68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb39d6c: b               #0xb39d34
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb39d70, size: 0x60
    // 0xb39d70: EnterFrame
    //     0xb39d70: stp             fp, lr, [SP, #-0x10]!
    //     0xb39d74: mov             fp, SP
    // 0xb39d78: AllocStack(0x8)
    //     0xb39d78: sub             SP, SP, #8
    // 0xb39d7c: SetupParameters()
    //     0xb39d7c: ldr             x0, [fp, #0x10]
    //     0xb39d80: ldur            w2, [x0, #0x17]
    //     0xb39d84: add             x2, x2, HEAP, lsl #32
    // 0xb39d88: CheckStackOverflow
    //     0xb39d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb39d8c: cmp             SP, x16
    //     0xb39d90: b.ls            #0xb39dc8
    // 0xb39d94: LoadField: r0 = r2->field_f
    //     0xb39d94: ldur            w0, [x2, #0xf]
    // 0xb39d98: DecompressPointer r0
    //     0xb39d98: add             x0, x0, HEAP, lsl #32
    // 0xb39d9c: stur            x0, [fp, #-8]
    // 0xb39da0: r1 = Function '<anonymous closure>':.
    //     0xb39da0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57180] AnonymousClosure: (0x9a129c), in [package:customer_app/app/presentation/views/line/browse/browse_accordion.dart] _AccordionState::build (0xbaa494)
    //     0xb39da4: ldr             x1, [x1, #0x180]
    // 0xb39da8: r0 = AllocateClosure()
    //     0xb39da8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb39dac: ldur            x1, [fp, #-8]
    // 0xb39db0: mov             x2, x0
    // 0xb39db4: r0 = setState()
    //     0xb39db4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb39db8: r0 = Null
    //     0xb39db8: mov             x0, NULL
    // 0xb39dbc: LeaveFrame
    //     0xb39dbc: mov             SP, fp
    //     0xb39dc0: ldp             fp, lr, [SP], #0x10
    // 0xb39dc4: ret
    //     0xb39dc4: ret             
    // 0xb39dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb39dc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb39dcc: b               #0xb39d94
  }
}

// class id: 4112, size: 0x24, field offset: 0xc
//   const constructor, 
class BrowseAccordian extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e808, size: 0x2c
    // 0xc7e808: EnterFrame
    //     0xc7e808: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e80c: mov             fp, SP
    // 0xc7e810: mov             x0, x1
    // 0xc7e814: r1 = <BrowseAccordian>
    //     0xc7e814: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a80] TypeArguments: <BrowseAccordian>
    //     0xc7e818: ldr             x1, [x1, #0xa80]
    // 0xc7e81c: r0 = _AccordionState()
    //     0xc7e81c: bl              #0xc7e834  ; Allocate_AccordionStateStub -> _AccordionState (size=0x18)
    // 0xc7e820: r1 = false
    //     0xc7e820: add             x1, NULL, #0x30  ; false
    // 0xc7e824: StoreField: r0->field_13 = r1
    //     0xc7e824: stur            w1, [x0, #0x13]
    // 0xc7e828: LeaveFrame
    //     0xc7e828: mov             SP, fp
    //     0xc7e82c: ldp             fp, lr, [SP], #0x10
    // 0xc7e830: ret
    //     0xc7e830: ret             
  }
}
