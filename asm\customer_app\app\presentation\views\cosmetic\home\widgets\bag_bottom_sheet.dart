// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart

// class id: 1049275, size: 0x8
class :: {
}

// class id: 4501, size: 0x40, field offset: 0xc
class BagBottomSheet extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x128c6c0, size: 0x448
    // 0x128c6c0: EnterFrame
    //     0x128c6c0: stp             fp, lr, [SP, #-0x10]!
    //     0x128c6c4: mov             fp, SP
    // 0x128c6c8: AllocStack(0x50)
    //     0x128c6c8: sub             SP, SP, #0x50
    // 0x128c6cc: SetupParameters(BagBottomSheet this /* r1 => r0, fp-0x20 */)
    //     0x128c6cc: mov             x0, x1
    //     0x128c6d0: stur            x1, [fp, #-0x20]
    // 0x128c6d4: CheckStackOverflow
    //     0x128c6d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128c6d8: cmp             SP, x16
    //     0x128c6dc: b.ls            #0x128cb00
    // 0x128c6e0: LoadField: r3 = r0->field_b
    //     0x128c6e0: ldur            w3, [x0, #0xb]
    // 0x128c6e4: DecompressPointer r3
    //     0x128c6e4: add             x3, x3, HEAP, lsl #32
    // 0x128c6e8: stur            x3, [fp, #-0x18]
    // 0x128c6ec: LoadField: r1 = r3->field_b
    //     0x128c6ec: ldur            w1, [x3, #0xb]
    // 0x128c6f0: DecompressPointer r1
    //     0x128c6f0: add             x1, x1, HEAP, lsl #32
    // 0x128c6f4: cmp             w1, NULL
    // 0x128c6f8: b.ne            #0x128c704
    // 0x128c6fc: r2 = Null
    //     0x128c6fc: mov             x2, NULL
    // 0x128c700: b               #0x128c70c
    // 0x128c704: LoadField: r2 = r1->field_b
    //     0x128c704: ldur            w2, [x1, #0xb]
    // 0x128c708: DecompressPointer r2
    //     0x128c708: add             x2, x2, HEAP, lsl #32
    // 0x128c70c: cmp             w2, NULL
    // 0x128c710: b.ne            #0x128c71c
    // 0x128c714: r4 = 0
    //     0x128c714: movz            x4, #0
    // 0x128c718: b               #0x128c728
    // 0x128c71c: r4 = LoadInt32Instr(r2)
    //     0x128c71c: sbfx            x4, x2, #1, #0x1f
    //     0x128c720: tbz             w2, #0, #0x128c728
    //     0x128c724: ldur            x4, [x2, #7]
    // 0x128c728: stur            x4, [fp, #-0x10]
    // 0x128c72c: cmp             w1, NULL
    // 0x128c730: b.ne            #0x128c73c
    // 0x128c734: r2 = Null
    //     0x128c734: mov             x2, NULL
    // 0x128c738: b               #0x128c744
    // 0x128c73c: LoadField: r2 = r1->field_13
    //     0x128c73c: ldur            w2, [x1, #0x13]
    // 0x128c740: DecompressPointer r2
    //     0x128c740: add             x2, x2, HEAP, lsl #32
    // 0x128c744: cmp             w2, NULL
    // 0x128c748: b.ne            #0x128c754
    // 0x128c74c: r5 = ""
    //     0x128c74c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128c750: b               #0x128c758
    // 0x128c754: mov             x5, x2
    // 0x128c758: stur            x5, [fp, #-8]
    // 0x128c75c: cmp             w1, NULL
    // 0x128c760: b.ne            #0x128c76c
    // 0x128c764: r1 = Null
    //     0x128c764: mov             x1, NULL
    // 0x128c768: b               #0x128c778
    // 0x128c76c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x128c76c: ldur            w2, [x1, #0x17]
    // 0x128c770: DecompressPointer r2
    //     0x128c770: add             x2, x2, HEAP, lsl #32
    // 0x128c774: mov             x1, x2
    // 0x128c778: cmp             w1, NULL
    // 0x128c77c: b.ne            #0x128c798
    // 0x128c780: r1 = <Catalogue>
    //     0x128c780: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0x128c784: ldr             x1, [x1, #0x418]
    // 0x128c788: r2 = 0
    //     0x128c788: movz            x2, #0
    // 0x128c78c: r0 = _GrowableList()
    //     0x128c78c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x128c790: mov             x2, x0
    // 0x128c794: b               #0x128c79c
    // 0x128c798: mov             x2, x1
    // 0x128c79c: ldur            x0, [fp, #-0x18]
    // 0x128c7a0: stur            x2, [fp, #-0x30]
    // 0x128c7a4: LoadField: r1 = r0->field_b
    //     0x128c7a4: ldur            w1, [x0, #0xb]
    // 0x128c7a8: DecompressPointer r1
    //     0x128c7a8: add             x1, x1, HEAP, lsl #32
    // 0x128c7ac: cmp             w1, NULL
    // 0x128c7b0: b.ne            #0x128c7bc
    // 0x128c7b4: r0 = Null
    //     0x128c7b4: mov             x0, NULL
    // 0x128c7b8: b               #0x128c7c4
    // 0x128c7bc: LoadField: r0 = r1->field_2f
    //     0x128c7bc: ldur            w0, [x1, #0x2f]
    // 0x128c7c0: DecompressPointer r0
    //     0x128c7c0: add             x0, x0, HEAP, lsl #32
    // 0x128c7c4: stur            x0, [fp, #-0x28]
    // 0x128c7c8: cmp             w0, NULL
    // 0x128c7cc: b.ne            #0x128c7d8
    // 0x128c7d0: r1 = Null
    //     0x128c7d0: mov             x1, NULL
    // 0x128c7d4: b               #0x128c7e0
    // 0x128c7d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x128c7d8: ldur            w1, [x0, #0x17]
    // 0x128c7dc: DecompressPointer r1
    //     0x128c7dc: add             x1, x1, HEAP, lsl #32
    // 0x128c7e0: cmp             w1, NULL
    // 0x128c7e4: b.ne            #0x128c7f0
    // 0x128c7e8: r3 = false
    //     0x128c7e8: add             x3, NULL, #0x30  ; false
    // 0x128c7ec: b               #0x128c7f4
    // 0x128c7f0: mov             x3, x1
    // 0x128c7f4: ldur            x1, [fp, #-0x20]
    // 0x128c7f8: stur            x3, [fp, #-0x18]
    // 0x128c7fc: r0 = _buildHeader()
    //     0x128c7fc: bl              #0x128ede8  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildHeader
    // 0x128c800: r1 = Null
    //     0x128c800: mov             x1, NULL
    // 0x128c804: r2 = 4
    //     0x128c804: movz            x2, #0x4
    // 0x128c808: stur            x0, [fp, #-0x38]
    // 0x128c80c: r0 = AllocateArray()
    //     0x128c80c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128c810: mov             x2, x0
    // 0x128c814: ldur            x0, [fp, #-0x38]
    // 0x128c818: stur            x2, [fp, #-0x40]
    // 0x128c81c: StoreField: r2->field_f = r0
    //     0x128c81c: stur            w0, [x2, #0xf]
    // 0x128c820: r16 = Instance_SizedBox
    //     0x128c820: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0x128c824: ldr             x16, [x16, #0xd68]
    // 0x128c828: StoreField: r2->field_13 = r16
    //     0x128c828: stur            w16, [x2, #0x13]
    // 0x128c82c: r1 = <Widget>
    //     0x128c82c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128c830: r0 = AllocateGrowableArray()
    //     0x128c830: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128c834: mov             x4, x0
    // 0x128c838: ldur            x0, [fp, #-0x40]
    // 0x128c83c: stur            x4, [fp, #-0x38]
    // 0x128c840: StoreField: r4->field_f = r0
    //     0x128c840: stur            w0, [x4, #0xf]
    // 0x128c844: r0 = 4
    //     0x128c844: movz            x0, #0x4
    // 0x128c848: StoreField: r4->field_b = r0
    //     0x128c848: stur            w0, [x4, #0xb]
    // 0x128c84c: ldur            x2, [fp, #-0x28]
    // 0x128c850: cmp             w2, NULL
    // 0x128c854: b.eq            #0x128c8e8
    // 0x128c858: ldur            x1, [fp, #-0x20]
    // 0x128c85c: ldur            x3, [fp, #-0x18]
    // 0x128c860: r0 = _buildFreeGiftItem()
    //     0x128c860: bl              #0x128e6f0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildFreeGiftItem
    // 0x128c864: mov             x2, x0
    // 0x128c868: ldur            x0, [fp, #-0x38]
    // 0x128c86c: stur            x2, [fp, #-0x18]
    // 0x128c870: LoadField: r1 = r0->field_b
    //     0x128c870: ldur            w1, [x0, #0xb]
    // 0x128c874: LoadField: r3 = r0->field_f
    //     0x128c874: ldur            w3, [x0, #0xf]
    // 0x128c878: DecompressPointer r3
    //     0x128c878: add             x3, x3, HEAP, lsl #32
    // 0x128c87c: LoadField: r4 = r3->field_b
    //     0x128c87c: ldur            w4, [x3, #0xb]
    // 0x128c880: r3 = LoadInt32Instr(r1)
    //     0x128c880: sbfx            x3, x1, #1, #0x1f
    // 0x128c884: stur            x3, [fp, #-0x48]
    // 0x128c888: r1 = LoadInt32Instr(r4)
    //     0x128c888: sbfx            x1, x4, #1, #0x1f
    // 0x128c88c: cmp             x3, x1
    // 0x128c890: b.ne            #0x128c89c
    // 0x128c894: mov             x1, x0
    // 0x128c898: r0 = _growToNextCapacity()
    //     0x128c898: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128c89c: ldur            x3, [fp, #-0x38]
    // 0x128c8a0: ldur            x2, [fp, #-0x48]
    // 0x128c8a4: add             x0, x2, #1
    // 0x128c8a8: lsl             x1, x0, #1
    // 0x128c8ac: StoreField: r3->field_b = r1
    //     0x128c8ac: stur            w1, [x3, #0xb]
    // 0x128c8b0: LoadField: r1 = r3->field_f
    //     0x128c8b0: ldur            w1, [x3, #0xf]
    // 0x128c8b4: DecompressPointer r1
    //     0x128c8b4: add             x1, x1, HEAP, lsl #32
    // 0x128c8b8: ldur            x0, [fp, #-0x18]
    // 0x128c8bc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x128c8bc: add             x25, x1, x2, lsl #2
    //     0x128c8c0: add             x25, x25, #0xf
    //     0x128c8c4: str             w0, [x25]
    //     0x128c8c8: tbz             w0, #0, #0x128c8e4
    //     0x128c8cc: ldurb           w16, [x1, #-1]
    //     0x128c8d0: ldurb           w17, [x0, #-1]
    //     0x128c8d4: and             x16, x17, x16, lsr #2
    //     0x128c8d8: tst             x16, HEAP, lsr #32
    //     0x128c8dc: b.eq            #0x128c8e4
    //     0x128c8e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128c8e4: b               #0x128c8ec
    // 0x128c8e8: mov             x3, x4
    // 0x128c8ec: ldur            x1, [fp, #-0x20]
    // 0x128c8f0: ldur            x2, [fp, #-0x30]
    // 0x128c8f4: r0 = _buildItemsList()
    //     0x128c8f4: bl              #0x128d7d0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList
    // 0x128c8f8: r1 = <FlexParentData>
    //     0x128c8f8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x128c8fc: ldr             x1, [x1, #0xe00]
    // 0x128c900: stur            x0, [fp, #-0x18]
    // 0x128c904: r0 = Flexible()
    //     0x128c904: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x128c908: mov             x2, x0
    // 0x128c90c: r0 = 1
    //     0x128c90c: movz            x0, #0x1
    // 0x128c910: stur            x2, [fp, #-0x28]
    // 0x128c914: StoreField: r2->field_13 = r0
    //     0x128c914: stur            x0, [x2, #0x13]
    // 0x128c918: r0 = Instance_FlexFit
    //     0x128c918: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x128c91c: ldr             x0, [x0, #0xe20]
    // 0x128c920: StoreField: r2->field_1b = r0
    //     0x128c920: stur            w0, [x2, #0x1b]
    // 0x128c924: ldur            x0, [fp, #-0x18]
    // 0x128c928: StoreField: r2->field_b = r0
    //     0x128c928: stur            w0, [x2, #0xb]
    // 0x128c92c: ldur            x0, [fp, #-0x38]
    // 0x128c930: LoadField: r1 = r0->field_b
    //     0x128c930: ldur            w1, [x0, #0xb]
    // 0x128c934: LoadField: r3 = r0->field_f
    //     0x128c934: ldur            w3, [x0, #0xf]
    // 0x128c938: DecompressPointer r3
    //     0x128c938: add             x3, x3, HEAP, lsl #32
    // 0x128c93c: LoadField: r4 = r3->field_b
    //     0x128c93c: ldur            w4, [x3, #0xb]
    // 0x128c940: r3 = LoadInt32Instr(r1)
    //     0x128c940: sbfx            x3, x1, #1, #0x1f
    // 0x128c944: stur            x3, [fp, #-0x48]
    // 0x128c948: r1 = LoadInt32Instr(r4)
    //     0x128c948: sbfx            x1, x4, #1, #0x1f
    // 0x128c94c: cmp             x3, x1
    // 0x128c950: b.ne            #0x128c95c
    // 0x128c954: mov             x1, x0
    // 0x128c958: r0 = _growToNextCapacity()
    //     0x128c958: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128c95c: ldur            x2, [fp, #-0x38]
    // 0x128c960: ldur            x3, [fp, #-0x48]
    // 0x128c964: add             x4, x3, #1
    // 0x128c968: stur            x4, [fp, #-0x50]
    // 0x128c96c: lsl             x0, x4, #1
    // 0x128c970: StoreField: r2->field_b = r0
    //     0x128c970: stur            w0, [x2, #0xb]
    // 0x128c974: LoadField: r5 = r2->field_f
    //     0x128c974: ldur            w5, [x2, #0xf]
    // 0x128c978: DecompressPointer r5
    //     0x128c978: add             x5, x5, HEAP, lsl #32
    // 0x128c97c: mov             x1, x5
    // 0x128c980: ldur            x0, [fp, #-0x28]
    // 0x128c984: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128c984: add             x25, x1, x3, lsl #2
    //     0x128c988: add             x25, x25, #0xf
    //     0x128c98c: str             w0, [x25]
    //     0x128c990: tbz             w0, #0, #0x128c9ac
    //     0x128c994: ldurb           w16, [x1, #-1]
    //     0x128c998: ldurb           w17, [x0, #-1]
    //     0x128c99c: and             x16, x17, x16, lsr #2
    //     0x128c9a0: tst             x16, HEAP, lsr #32
    //     0x128c9a4: b.eq            #0x128c9ac
    //     0x128c9a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128c9ac: LoadField: r0 = r5->field_b
    //     0x128c9ac: ldur            w0, [x5, #0xb]
    // 0x128c9b0: r1 = LoadInt32Instr(r0)
    //     0x128c9b0: sbfx            x1, x0, #1, #0x1f
    // 0x128c9b4: cmp             x4, x1
    // 0x128c9b8: b.ne            #0x128c9c4
    // 0x128c9bc: mov             x1, x2
    // 0x128c9c0: r0 = _growToNextCapacity()
    //     0x128c9c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128c9c4: ldur            x1, [fp, #-0x50]
    // 0x128c9c8: ldur            x0, [fp, #-0x38]
    // 0x128c9cc: add             x2, x1, #1
    // 0x128c9d0: lsl             x3, x2, #1
    // 0x128c9d4: StoreField: r0->field_b = r3
    //     0x128c9d4: stur            w3, [x0, #0xb]
    // 0x128c9d8: LoadField: r2 = r0->field_f
    //     0x128c9d8: ldur            w2, [x0, #0xf]
    // 0x128c9dc: DecompressPointer r2
    //     0x128c9dc: add             x2, x2, HEAP, lsl #32
    // 0x128c9e0: add             x3, x2, x1, lsl #2
    // 0x128c9e4: r16 = Instance_SizedBox
    //     0x128c9e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0x128c9e8: ldr             x16, [x16, #0x9f0]
    // 0x128c9ec: StoreField: r3->field_f = r16
    //     0x128c9ec: stur            w16, [x3, #0xf]
    // 0x128c9f0: ldur            x1, [fp, #-0x20]
    // 0x128c9f4: ldur            x2, [fp, #-0x10]
    // 0x128c9f8: ldur            x3, [fp, #-8]
    // 0x128c9fc: r0 = _buildFooter()
    //     0x128c9fc: bl              #0x128cb08  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildFooter
    // 0x128ca00: mov             x2, x0
    // 0x128ca04: ldur            x0, [fp, #-0x38]
    // 0x128ca08: stur            x2, [fp, #-8]
    // 0x128ca0c: LoadField: r1 = r0->field_b
    //     0x128ca0c: ldur            w1, [x0, #0xb]
    // 0x128ca10: LoadField: r3 = r0->field_f
    //     0x128ca10: ldur            w3, [x0, #0xf]
    // 0x128ca14: DecompressPointer r3
    //     0x128ca14: add             x3, x3, HEAP, lsl #32
    // 0x128ca18: LoadField: r4 = r3->field_b
    //     0x128ca18: ldur            w4, [x3, #0xb]
    // 0x128ca1c: r3 = LoadInt32Instr(r1)
    //     0x128ca1c: sbfx            x3, x1, #1, #0x1f
    // 0x128ca20: stur            x3, [fp, #-0x10]
    // 0x128ca24: r1 = LoadInt32Instr(r4)
    //     0x128ca24: sbfx            x1, x4, #1, #0x1f
    // 0x128ca28: cmp             x3, x1
    // 0x128ca2c: b.ne            #0x128ca38
    // 0x128ca30: mov             x1, x0
    // 0x128ca34: r0 = _growToNextCapacity()
    //     0x128ca34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128ca38: ldur            x2, [fp, #-0x38]
    // 0x128ca3c: ldur            x3, [fp, #-0x10]
    // 0x128ca40: add             x0, x3, #1
    // 0x128ca44: lsl             x1, x0, #1
    // 0x128ca48: StoreField: r2->field_b = r1
    //     0x128ca48: stur            w1, [x2, #0xb]
    // 0x128ca4c: LoadField: r1 = r2->field_f
    //     0x128ca4c: ldur            w1, [x2, #0xf]
    // 0x128ca50: DecompressPointer r1
    //     0x128ca50: add             x1, x1, HEAP, lsl #32
    // 0x128ca54: ldur            x0, [fp, #-8]
    // 0x128ca58: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128ca58: add             x25, x1, x3, lsl #2
    //     0x128ca5c: add             x25, x25, #0xf
    //     0x128ca60: str             w0, [x25]
    //     0x128ca64: tbz             w0, #0, #0x128ca80
    //     0x128ca68: ldurb           w16, [x1, #-1]
    //     0x128ca6c: ldurb           w17, [x0, #-1]
    //     0x128ca70: and             x16, x17, x16, lsr #2
    //     0x128ca74: tst             x16, HEAP, lsr #32
    //     0x128ca78: b.eq            #0x128ca80
    //     0x128ca7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128ca80: r0 = Column()
    //     0x128ca80: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x128ca84: mov             x1, x0
    // 0x128ca88: r0 = Instance_Axis
    //     0x128ca88: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x128ca8c: stur            x1, [fp, #-8]
    // 0x128ca90: StoreField: r1->field_f = r0
    //     0x128ca90: stur            w0, [x1, #0xf]
    // 0x128ca94: r0 = Instance_MainAxisAlignment
    //     0x128ca94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128ca98: ldr             x0, [x0, #0xa08]
    // 0x128ca9c: StoreField: r1->field_13 = r0
    //     0x128ca9c: stur            w0, [x1, #0x13]
    // 0x128caa0: r0 = Instance_MainAxisSize
    //     0x128caa0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x128caa4: ldr             x0, [x0, #0xdd0]
    // 0x128caa8: ArrayStore: r1[0] = r0  ; List_4
    //     0x128caa8: stur            w0, [x1, #0x17]
    // 0x128caac: r0 = Instance_CrossAxisAlignment
    //     0x128caac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128cab0: ldr             x0, [x0, #0xa18]
    // 0x128cab4: StoreField: r1->field_1b = r0
    //     0x128cab4: stur            w0, [x1, #0x1b]
    // 0x128cab8: r0 = Instance_VerticalDirection
    //     0x128cab8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128cabc: ldr             x0, [x0, #0xa20]
    // 0x128cac0: StoreField: r1->field_23 = r0
    //     0x128cac0: stur            w0, [x1, #0x23]
    // 0x128cac4: r0 = Instance_Clip
    //     0x128cac4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128cac8: ldr             x0, [x0, #0x38]
    // 0x128cacc: StoreField: r1->field_2b = r0
    //     0x128cacc: stur            w0, [x1, #0x2b]
    // 0x128cad0: StoreField: r1->field_2f = rZR
    //     0x128cad0: stur            xzr, [x1, #0x2f]
    // 0x128cad4: ldur            x0, [fp, #-0x38]
    // 0x128cad8: StoreField: r1->field_b = r0
    //     0x128cad8: stur            w0, [x1, #0xb]
    // 0x128cadc: r0 = Padding()
    //     0x128cadc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128cae0: r1 = Instance_EdgeInsets
    //     0x128cae0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0x128cae4: ldr             x1, [x1, #0x858]
    // 0x128cae8: StoreField: r0->field_f = r1
    //     0x128cae8: stur            w1, [x0, #0xf]
    // 0x128caec: ldur            x1, [fp, #-8]
    // 0x128caf0: StoreField: r0->field_b = r1
    //     0x128caf0: stur            w1, [x0, #0xb]
    // 0x128caf4: LeaveFrame
    //     0x128caf4: mov             SP, fp
    //     0x128caf8: ldp             fp, lr, [SP], #0x10
    // 0x128cafc: ret
    //     0x128cafc: ret             
    // 0x128cb00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128cb00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128cb04: b               #0x128c6e0
  }
  _ _buildFooter(/* No info */) {
    // ** addr: 0x128cb08, size: 0x414
    // 0x128cb08: EnterFrame
    //     0x128cb08: stp             fp, lr, [SP, #-0x10]!
    //     0x128cb0c: mov             fp, SP
    // 0x128cb10: AllocStack(0x40)
    //     0x128cb10: sub             SP, SP, #0x40
    // 0x128cb14: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x128cb14: stur            x1, [fp, #-8]
    //     0x128cb18: stur            x2, [fp, #-0x10]
    //     0x128cb1c: stur            x3, [fp, #-0x18]
    // 0x128cb20: CheckStackOverflow
    //     0x128cb20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128cb24: cmp             SP, x16
    //     0x128cb28: b.ls            #0x128cefc
    // 0x128cb2c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x128cb2c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x128cb30: ldr             x0, [x0, #0x1c80]
    //     0x128cb34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x128cb38: cmp             w0, w16
    //     0x128cb3c: b.ne            #0x128cb48
    //     0x128cb40: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x128cb44: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x128cb48: r0 = GetNavigation.size()
    //     0x128cb48: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x128cb4c: LoadField: d0 = r0->field_f
    //     0x128cb4c: ldur            d0, [x0, #0xf]
    // 0x128cb50: d1 = 0.150000
    //     0x128cb50: ldr             d1, [PP, #0x5788]  ; [pp+0x5788] IMM: double(0.15) from 0x3fc3333333333333
    // 0x128cb54: fmul            d2, d0, d1
    // 0x128cb58: stur            d2, [fp, #-0x38]
    // 0x128cb5c: r1 = Null
    //     0x128cb5c: mov             x1, NULL
    // 0x128cb60: r2 = 6
    //     0x128cb60: movz            x2, #0x6
    // 0x128cb64: r0 = AllocateArray()
    //     0x128cb64: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128cb68: mov             x2, x0
    // 0x128cb6c: r16 = "Subtotal ("
    //     0x128cb6c: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e0] "Subtotal ("
    //     0x128cb70: ldr             x16, [x16, #0x4e0]
    // 0x128cb74: StoreField: r2->field_f = r16
    //     0x128cb74: stur            w16, [x2, #0xf]
    // 0x128cb78: ldur            x3, [fp, #-0x10]
    // 0x128cb7c: r0 = BoxInt64Instr(r3)
    //     0x128cb7c: sbfiz           x0, x3, #1, #0x1f
    //     0x128cb80: cmp             x3, x0, asr #1
    //     0x128cb84: b.eq            #0x128cb90
    //     0x128cb88: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x128cb8c: stur            x3, [x0, #7]
    // 0x128cb90: StoreField: r2->field_13 = r0
    //     0x128cb90: stur            w0, [x2, #0x13]
    // 0x128cb94: r16 = " item)"
    //     0x128cb94: add             x16, PP, #0x48, lsl #12  ; [pp+0x484e8] " item)"
    //     0x128cb98: ldr             x16, [x16, #0x4e8]
    // 0x128cb9c: ArrayStore: r2[0] = r16  ; List_4
    //     0x128cb9c: stur            w16, [x2, #0x17]
    // 0x128cba0: str             x2, [SP]
    // 0x128cba4: r0 = _interpolate()
    //     0x128cba4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x128cba8: r1 = Instance_Color
    //     0x128cba8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128cbac: d0 = 0.300000
    //     0x128cbac: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x128cbb0: ldr             d0, [x17, #0x658]
    // 0x128cbb4: stur            x0, [fp, #-0x20]
    // 0x128cbb8: r0 = withOpacity()
    //     0x128cbb8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x128cbbc: stur            x0, [fp, #-0x28]
    // 0x128cbc0: r0 = TextStyle()
    //     0x128cbc0: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0x128cbc4: mov             x1, x0
    // 0x128cbc8: r0 = true
    //     0x128cbc8: add             x0, NULL, #0x20  ; true
    // 0x128cbcc: stur            x1, [fp, #-0x30]
    // 0x128cbd0: StoreField: r1->field_7 = r0
    //     0x128cbd0: stur            w0, [x1, #7]
    // 0x128cbd4: ldur            x0, [fp, #-0x28]
    // 0x128cbd8: StoreField: r1->field_b = r0
    //     0x128cbd8: stur            w0, [x1, #0xb]
    // 0x128cbdc: r0 = 16.000000
    //     0x128cbdc: add             x0, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x128cbe0: ldr             x0, [x0, #0x188]
    // 0x128cbe4: StoreField: r1->field_1f = r0
    //     0x128cbe4: stur            w0, [x1, #0x1f]
    // 0x128cbe8: r0 = Text()
    //     0x128cbe8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128cbec: mov             x1, x0
    // 0x128cbf0: ldur            x0, [fp, #-0x20]
    // 0x128cbf4: stur            x1, [fp, #-0x28]
    // 0x128cbf8: StoreField: r1->field_b = r0
    //     0x128cbf8: stur            w0, [x1, #0xb]
    // 0x128cbfc: ldur            x0, [fp, #-0x30]
    // 0x128cc00: StoreField: r1->field_13 = r0
    //     0x128cc00: stur            w0, [x1, #0x13]
    // 0x128cc04: r0 = Text()
    //     0x128cc04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128cc08: mov             x3, x0
    // 0x128cc0c: ldur            x0, [fp, #-0x18]
    // 0x128cc10: stur            x3, [fp, #-0x20]
    // 0x128cc14: StoreField: r3->field_b = r0
    //     0x128cc14: stur            w0, [x3, #0xb]
    // 0x128cc18: r0 = Instance_TextStyle
    //     0x128cc18: add             x0, PP, #0x48, lsl #12  ; [pp+0x48c38] Obj!TextStyle@d62cd1
    //     0x128cc1c: ldr             x0, [x0, #0xc38]
    // 0x128cc20: StoreField: r3->field_13 = r0
    //     0x128cc20: stur            w0, [x3, #0x13]
    // 0x128cc24: r1 = Null
    //     0x128cc24: mov             x1, NULL
    // 0x128cc28: r2 = 6
    //     0x128cc28: movz            x2, #0x6
    // 0x128cc2c: r0 = AllocateArray()
    //     0x128cc2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128cc30: mov             x2, x0
    // 0x128cc34: ldur            x0, [fp, #-0x28]
    // 0x128cc38: stur            x2, [fp, #-0x18]
    // 0x128cc3c: StoreField: r2->field_f = r0
    //     0x128cc3c: stur            w0, [x2, #0xf]
    // 0x128cc40: r16 = Instance_Spacer
    //     0x128cc40: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x128cc44: ldr             x16, [x16, #0xf0]
    // 0x128cc48: StoreField: r2->field_13 = r16
    //     0x128cc48: stur            w16, [x2, #0x13]
    // 0x128cc4c: ldur            x0, [fp, #-0x20]
    // 0x128cc50: ArrayStore: r2[0] = r0  ; List_4
    //     0x128cc50: stur            w0, [x2, #0x17]
    // 0x128cc54: r1 = <Widget>
    //     0x128cc54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128cc58: r0 = AllocateGrowableArray()
    //     0x128cc58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128cc5c: mov             x1, x0
    // 0x128cc60: ldur            x0, [fp, #-0x18]
    // 0x128cc64: stur            x1, [fp, #-0x20]
    // 0x128cc68: StoreField: r1->field_f = r0
    //     0x128cc68: stur            w0, [x1, #0xf]
    // 0x128cc6c: r0 = 6
    //     0x128cc6c: movz            x0, #0x6
    // 0x128cc70: StoreField: r1->field_b = r0
    //     0x128cc70: stur            w0, [x1, #0xb]
    // 0x128cc74: r0 = Row()
    //     0x128cc74: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x128cc78: mov             x1, x0
    // 0x128cc7c: r0 = Instance_Axis
    //     0x128cc7c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128cc80: stur            x1, [fp, #-0x18]
    // 0x128cc84: StoreField: r1->field_f = r0
    //     0x128cc84: stur            w0, [x1, #0xf]
    // 0x128cc88: r0 = Instance_MainAxisAlignment
    //     0x128cc88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128cc8c: ldr             x0, [x0, #0xa08]
    // 0x128cc90: StoreField: r1->field_13 = r0
    //     0x128cc90: stur            w0, [x1, #0x13]
    // 0x128cc94: r2 = Instance_MainAxisSize
    //     0x128cc94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128cc98: ldr             x2, [x2, #0xa10]
    // 0x128cc9c: ArrayStore: r1[0] = r2  ; List_4
    //     0x128cc9c: stur            w2, [x1, #0x17]
    // 0x128cca0: r3 = Instance_CrossAxisAlignment
    //     0x128cca0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128cca4: ldr             x3, [x3, #0xa18]
    // 0x128cca8: StoreField: r1->field_1b = r3
    //     0x128cca8: stur            w3, [x1, #0x1b]
    // 0x128ccac: r3 = Instance_VerticalDirection
    //     0x128ccac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128ccb0: ldr             x3, [x3, #0xa20]
    // 0x128ccb4: StoreField: r1->field_23 = r3
    //     0x128ccb4: stur            w3, [x1, #0x23]
    // 0x128ccb8: r4 = Instance_Clip
    //     0x128ccb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128ccbc: ldr             x4, [x4, #0x38]
    // 0x128ccc0: StoreField: r1->field_2b = r4
    //     0x128ccc0: stur            w4, [x1, #0x2b]
    // 0x128ccc4: StoreField: r1->field_2f = rZR
    //     0x128ccc4: stur            xzr, [x1, #0x2f]
    // 0x128ccc8: ldur            x5, [fp, #-0x20]
    // 0x128cccc: StoreField: r1->field_b = r5
    //     0x128cccc: stur            w5, [x1, #0xb]
    // 0x128ccd0: r0 = Padding()
    //     0x128ccd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128ccd4: mov             x3, x0
    // 0x128ccd8: r0 = Instance_EdgeInsets
    //     0x128ccd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x128ccdc: ldr             x0, [x0, #0x1f0]
    // 0x128cce0: stur            x3, [fp, #-0x20]
    // 0x128cce4: StoreField: r3->field_f = r0
    //     0x128cce4: stur            w0, [x3, #0xf]
    // 0x128cce8: ldur            x0, [fp, #-0x18]
    // 0x128ccec: StoreField: r3->field_b = r0
    //     0x128ccec: stur            w0, [x3, #0xb]
    // 0x128ccf0: r1 = Null
    //     0x128ccf0: mov             x1, NULL
    // 0x128ccf4: r2 = 2
    //     0x128ccf4: movz            x2, #0x2
    // 0x128ccf8: r0 = AllocateArray()
    //     0x128ccf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128ccfc: mov             x2, x0
    // 0x128cd00: ldur            x0, [fp, #-0x20]
    // 0x128cd04: stur            x2, [fp, #-0x18]
    // 0x128cd08: StoreField: r2->field_f = r0
    //     0x128cd08: stur            w0, [x2, #0xf]
    // 0x128cd0c: r1 = <Widget>
    //     0x128cd0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128cd10: r0 = AllocateGrowableArray()
    //     0x128cd10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128cd14: mov             x2, x0
    // 0x128cd18: ldur            x0, [fp, #-0x18]
    // 0x128cd1c: stur            x2, [fp, #-0x20]
    // 0x128cd20: StoreField: r2->field_f = r0
    //     0x128cd20: stur            w0, [x2, #0xf]
    // 0x128cd24: r0 = 2
    //     0x128cd24: movz            x0, #0x2
    // 0x128cd28: StoreField: r2->field_b = r0
    //     0x128cd28: stur            w0, [x2, #0xb]
    // 0x128cd2c: ldur            x1, [fp, #-8]
    // 0x128cd30: LoadField: r0 = r1->field_23
    //     0x128cd30: ldur            w0, [x1, #0x23]
    // 0x128cd34: DecompressPointer r0
    //     0x128cd34: add             x0, x0, HEAP, lsl #32
    // 0x128cd38: cmp             w0, NULL
    // 0x128cd3c: b.eq            #0x128cdc8
    // 0x128cd40: r0 = _buildSingleButton()
    //     0x128cd40: bl              #0x128d3c0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildSingleButton
    // 0x128cd44: mov             x2, x0
    // 0x128cd48: ldur            x0, [fp, #-0x20]
    // 0x128cd4c: stur            x2, [fp, #-0x18]
    // 0x128cd50: LoadField: r1 = r0->field_b
    //     0x128cd50: ldur            w1, [x0, #0xb]
    // 0x128cd54: LoadField: r3 = r0->field_f
    //     0x128cd54: ldur            w3, [x0, #0xf]
    // 0x128cd58: DecompressPointer r3
    //     0x128cd58: add             x3, x3, HEAP, lsl #32
    // 0x128cd5c: LoadField: r4 = r3->field_b
    //     0x128cd5c: ldur            w4, [x3, #0xb]
    // 0x128cd60: r3 = LoadInt32Instr(r1)
    //     0x128cd60: sbfx            x3, x1, #1, #0x1f
    // 0x128cd64: stur            x3, [fp, #-0x10]
    // 0x128cd68: r1 = LoadInt32Instr(r4)
    //     0x128cd68: sbfx            x1, x4, #1, #0x1f
    // 0x128cd6c: cmp             x3, x1
    // 0x128cd70: b.ne            #0x128cd7c
    // 0x128cd74: mov             x1, x0
    // 0x128cd78: r0 = _growToNextCapacity()
    //     0x128cd78: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128cd7c: ldur            x2, [fp, #-0x20]
    // 0x128cd80: ldur            x3, [fp, #-0x10]
    // 0x128cd84: add             x0, x3, #1
    // 0x128cd88: lsl             x1, x0, #1
    // 0x128cd8c: StoreField: r2->field_b = r1
    //     0x128cd8c: stur            w1, [x2, #0xb]
    // 0x128cd90: LoadField: r1 = r2->field_f
    //     0x128cd90: ldur            w1, [x2, #0xf]
    // 0x128cd94: DecompressPointer r1
    //     0x128cd94: add             x1, x1, HEAP, lsl #32
    // 0x128cd98: ldur            x0, [fp, #-0x18]
    // 0x128cd9c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128cd9c: add             x25, x1, x3, lsl #2
    //     0x128cda0: add             x25, x25, #0xf
    //     0x128cda4: str             w0, [x25]
    //     0x128cda8: tbz             w0, #0, #0x128cdc4
    //     0x128cdac: ldurb           w16, [x1, #-1]
    //     0x128cdb0: ldurb           w17, [x0, #-1]
    //     0x128cdb4: and             x16, x17, x16, lsr #2
    //     0x128cdb8: tst             x16, HEAP, lsr #32
    //     0x128cdbc: b.eq            #0x128cdc4
    //     0x128cdc0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128cdc4: b               #0x128ce4c
    // 0x128cdc8: r0 = _buildDualButtons()
    //     0x128cdc8: bl              #0x128cf1c  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons
    // 0x128cdcc: mov             x2, x0
    // 0x128cdd0: ldur            x0, [fp, #-0x20]
    // 0x128cdd4: stur            x2, [fp, #-8]
    // 0x128cdd8: LoadField: r1 = r0->field_b
    //     0x128cdd8: ldur            w1, [x0, #0xb]
    // 0x128cddc: LoadField: r3 = r0->field_f
    //     0x128cddc: ldur            w3, [x0, #0xf]
    // 0x128cde0: DecompressPointer r3
    //     0x128cde0: add             x3, x3, HEAP, lsl #32
    // 0x128cde4: LoadField: r4 = r3->field_b
    //     0x128cde4: ldur            w4, [x3, #0xb]
    // 0x128cde8: r3 = LoadInt32Instr(r1)
    //     0x128cde8: sbfx            x3, x1, #1, #0x1f
    // 0x128cdec: stur            x3, [fp, #-0x10]
    // 0x128cdf0: r1 = LoadInt32Instr(r4)
    //     0x128cdf0: sbfx            x1, x4, #1, #0x1f
    // 0x128cdf4: cmp             x3, x1
    // 0x128cdf8: b.ne            #0x128ce04
    // 0x128cdfc: mov             x1, x0
    // 0x128ce00: r0 = _growToNextCapacity()
    //     0x128ce00: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128ce04: ldur            x2, [fp, #-0x20]
    // 0x128ce08: ldur            x3, [fp, #-0x10]
    // 0x128ce0c: add             x0, x3, #1
    // 0x128ce10: lsl             x1, x0, #1
    // 0x128ce14: StoreField: r2->field_b = r1
    //     0x128ce14: stur            w1, [x2, #0xb]
    // 0x128ce18: LoadField: r1 = r2->field_f
    //     0x128ce18: ldur            w1, [x2, #0xf]
    // 0x128ce1c: DecompressPointer r1
    //     0x128ce1c: add             x1, x1, HEAP, lsl #32
    // 0x128ce20: ldur            x0, [fp, #-8]
    // 0x128ce24: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128ce24: add             x25, x1, x3, lsl #2
    //     0x128ce28: add             x25, x25, #0xf
    //     0x128ce2c: str             w0, [x25]
    //     0x128ce30: tbz             w0, #0, #0x128ce4c
    //     0x128ce34: ldurb           w16, [x1, #-1]
    //     0x128ce38: ldurb           w17, [x0, #-1]
    //     0x128ce3c: and             x16, x17, x16, lsr #2
    //     0x128ce40: tst             x16, HEAP, lsr #32
    //     0x128ce44: b.eq            #0x128ce4c
    //     0x128ce48: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128ce4c: ldur            d0, [fp, #-0x38]
    // 0x128ce50: r0 = Column()
    //     0x128ce50: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x128ce54: mov             x1, x0
    // 0x128ce58: r0 = Instance_Axis
    //     0x128ce58: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x128ce5c: stur            x1, [fp, #-0x18]
    // 0x128ce60: StoreField: r1->field_f = r0
    //     0x128ce60: stur            w0, [x1, #0xf]
    // 0x128ce64: r0 = Instance_MainAxisAlignment
    //     0x128ce64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128ce68: ldr             x0, [x0, #0xa08]
    // 0x128ce6c: StoreField: r1->field_13 = r0
    //     0x128ce6c: stur            w0, [x1, #0x13]
    // 0x128ce70: r0 = Instance_MainAxisSize
    //     0x128ce70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128ce74: ldr             x0, [x0, #0xa10]
    // 0x128ce78: ArrayStore: r1[0] = r0  ; List_4
    //     0x128ce78: stur            w0, [x1, #0x17]
    // 0x128ce7c: r0 = Instance_CrossAxisAlignment
    //     0x128ce7c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x128ce80: ldr             x0, [x0, #0x890]
    // 0x128ce84: StoreField: r1->field_1b = r0
    //     0x128ce84: stur            w0, [x1, #0x1b]
    // 0x128ce88: r0 = Instance_VerticalDirection
    //     0x128ce88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128ce8c: ldr             x0, [x0, #0xa20]
    // 0x128ce90: StoreField: r1->field_23 = r0
    //     0x128ce90: stur            w0, [x1, #0x23]
    // 0x128ce94: r0 = Instance_Clip
    //     0x128ce94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128ce98: ldr             x0, [x0, #0x38]
    // 0x128ce9c: StoreField: r1->field_2b = r0
    //     0x128ce9c: stur            w0, [x1, #0x2b]
    // 0x128cea0: StoreField: r1->field_2f = rZR
    //     0x128cea0: stur            xzr, [x1, #0x2f]
    // 0x128cea4: ldur            x0, [fp, #-0x20]
    // 0x128cea8: StoreField: r1->field_b = r0
    //     0x128cea8: stur            w0, [x1, #0xb]
    // 0x128ceac: ldur            d0, [fp, #-0x38]
    // 0x128ceb0: r0 = inline_Allocate_Double()
    //     0x128ceb0: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x128ceb4: add             x0, x0, #0x10
    //     0x128ceb8: cmp             x2, x0
    //     0x128cebc: b.ls            #0x128cf04
    //     0x128cec0: str             x0, [THR, #0x50]  ; THR::top
    //     0x128cec4: sub             x0, x0, #0xf
    //     0x128cec8: movz            x2, #0xe15c
    //     0x128cecc: movk            x2, #0x3, lsl #16
    //     0x128ced0: stur            x2, [x0, #-1]
    // 0x128ced4: StoreField: r0->field_7 = d0
    //     0x128ced4: stur            d0, [x0, #7]
    // 0x128ced8: stur            x0, [fp, #-8]
    // 0x128cedc: r0 = SizedBox()
    //     0x128cedc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x128cee0: ldur            x1, [fp, #-8]
    // 0x128cee4: StoreField: r0->field_13 = r1
    //     0x128cee4: stur            w1, [x0, #0x13]
    // 0x128cee8: ldur            x1, [fp, #-0x18]
    // 0x128ceec: StoreField: r0->field_b = r1
    //     0x128ceec: stur            w1, [x0, #0xb]
    // 0x128cef0: LeaveFrame
    //     0x128cef0: mov             SP, fp
    //     0x128cef4: ldp             fp, lr, [SP], #0x10
    // 0x128cef8: ret
    //     0x128cef8: ret             
    // 0x128cefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128cefc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128cf00: b               #0x128cb2c
    // 0x128cf04: SaveReg d0
    //     0x128cf04: str             q0, [SP, #-0x10]!
    // 0x128cf08: SaveReg r1
    //     0x128cf08: str             x1, [SP, #-8]!
    // 0x128cf0c: r0 = AllocateDouble()
    //     0x128cf0c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x128cf10: RestoreReg r1
    //     0x128cf10: ldr             x1, [SP], #8
    // 0x128cf14: RestoreReg d0
    //     0x128cf14: ldr             q0, [SP], #0x10
    // 0x128cf18: b               #0x128ced4
  }
  _ _buildDualButtons(/* No info */) {
    // ** addr: 0x128cf1c, size: 0x3dc
    // 0x128cf1c: EnterFrame
    //     0x128cf1c: stp             fp, lr, [SP, #-0x10]!
    //     0x128cf20: mov             fp, SP
    // 0x128cf24: AllocStack(0x40)
    //     0x128cf24: sub             SP, SP, #0x40
    // 0x128cf28: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */)
    //     0x128cf28: stur            x1, [fp, #-8]
    // 0x128cf2c: CheckStackOverflow
    //     0x128cf2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128cf30: cmp             SP, x16
    //     0x128cf34: b.ls            #0x128d2f0
    // 0x128cf38: r1 = 1
    //     0x128cf38: movz            x1, #0x1
    // 0x128cf3c: r0 = AllocateContext()
    //     0x128cf3c: bl              #0x16f6108  ; AllocateContextStub
    // 0x128cf40: mov             x1, x0
    // 0x128cf44: ldur            x0, [fp, #-8]
    // 0x128cf48: stur            x1, [fp, #-0x10]
    // 0x128cf4c: StoreField: r1->field_f = r0
    //     0x128cf4c: stur            w0, [x1, #0xf]
    // 0x128cf50: r16 = <EdgeInsets>
    //     0x128cf50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x128cf54: ldr             x16, [x16, #0xda0]
    // 0x128cf58: r30 = Instance_EdgeInsets
    //     0x128cf58: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x128cf5c: ldr             lr, [lr, #0x1f0]
    // 0x128cf60: stp             lr, x16, [SP]
    // 0x128cf64: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128cf64: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128cf68: r0 = all()
    //     0x128cf68: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128cf6c: mov             x1, x0
    // 0x128cf70: ldur            x0, [fp, #-8]
    // 0x128cf74: stur            x1, [fp, #-0x20]
    // 0x128cf78: LoadField: r2 = r0->field_2b
    //     0x128cf78: ldur            w2, [x0, #0x2b]
    // 0x128cf7c: DecompressPointer r2
    //     0x128cf7c: add             x2, x2, HEAP, lsl #32
    // 0x128cf80: stur            x2, [fp, #-0x18]
    // 0x128cf84: r0 = BorderSide()
    //     0x128cf84: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x128cf88: mov             x1, x0
    // 0x128cf8c: ldur            x0, [fp, #-0x18]
    // 0x128cf90: stur            x1, [fp, #-0x28]
    // 0x128cf94: StoreField: r1->field_7 = r0
    //     0x128cf94: stur            w0, [x1, #7]
    // 0x128cf98: d0 = 1.000000
    //     0x128cf98: fmov            d0, #1.00000000
    // 0x128cf9c: StoreField: r1->field_b = d0
    //     0x128cf9c: stur            d0, [x1, #0xb]
    // 0x128cfa0: r0 = Instance_BorderStyle
    //     0x128cfa0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x128cfa4: ldr             x0, [x0, #0xf68]
    // 0x128cfa8: StoreField: r1->field_13 = r0
    //     0x128cfa8: stur            w0, [x1, #0x13]
    // 0x128cfac: d0 = -1.000000
    //     0x128cfac: fmov            d0, #-1.00000000
    // 0x128cfb0: ArrayStore: r1[0] = d0  ; List_8
    //     0x128cfb0: stur            d0, [x1, #0x17]
    // 0x128cfb4: r0 = RoundedRectangleBorder()
    //     0x128cfb4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x128cfb8: mov             x1, x0
    // 0x128cfbc: r0 = Instance_BorderRadius
    //     0x128cfbc: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fdc0] Obj!BorderRadius@d5a301
    //     0x128cfc0: ldr             x0, [x0, #0xdc0]
    // 0x128cfc4: StoreField: r1->field_b = r0
    //     0x128cfc4: stur            w0, [x1, #0xb]
    // 0x128cfc8: ldur            x0, [fp, #-0x28]
    // 0x128cfcc: StoreField: r1->field_7 = r0
    //     0x128cfcc: stur            w0, [x1, #7]
    // 0x128cfd0: r16 = <RoundedRectangleBorder>
    //     0x128cfd0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x128cfd4: ldr             x16, [x16, #0xf78]
    // 0x128cfd8: stp             x1, x16, [SP]
    // 0x128cfdc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128cfdc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128cfe0: r0 = all()
    //     0x128cfe0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128cfe4: stur            x0, [fp, #-0x18]
    // 0x128cfe8: r0 = ButtonStyle()
    //     0x128cfe8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x128cfec: mov             x1, x0
    // 0x128cff0: ldur            x0, [fp, #-0x20]
    // 0x128cff4: stur            x1, [fp, #-0x28]
    // 0x128cff8: StoreField: r1->field_23 = r0
    //     0x128cff8: stur            w0, [x1, #0x23]
    // 0x128cffc: ldur            x0, [fp, #-0x18]
    // 0x128d000: StoreField: r1->field_43 = r0
    //     0x128d000: stur            w0, [x1, #0x43]
    // 0x128d004: r0 = TextButtonThemeData()
    //     0x128d004: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x128d008: mov             x1, x0
    // 0x128d00c: ldur            x0, [fp, #-0x28]
    // 0x128d010: stur            x1, [fp, #-0x20]
    // 0x128d014: StoreField: r1->field_7 = r0
    //     0x128d014: stur            w0, [x1, #7]
    // 0x128d018: ldur            x0, [fp, #-8]
    // 0x128d01c: LoadField: r2 = r0->field_27
    //     0x128d01c: ldur            w2, [x0, #0x27]
    // 0x128d020: DecompressPointer r2
    //     0x128d020: add             x2, x2, HEAP, lsl #32
    // 0x128d024: stur            x2, [fp, #-0x18]
    // 0x128d028: r0 = TextStyle()
    //     0x128d028: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0x128d02c: mov             x1, x0
    // 0x128d030: r0 = true
    //     0x128d030: add             x0, NULL, #0x20  ; true
    // 0x128d034: stur            x1, [fp, #-8]
    // 0x128d038: StoreField: r1->field_7 = r0
    //     0x128d038: stur            w0, [x1, #7]
    // 0x128d03c: ldur            x2, [fp, #-0x18]
    // 0x128d040: StoreField: r1->field_b = r2
    //     0x128d040: stur            w2, [x1, #0xb]
    // 0x128d044: r3 = 16.000000
    //     0x128d044: add             x3, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x128d048: ldr             x3, [x3, #0x188]
    // 0x128d04c: StoreField: r1->field_1f = r3
    //     0x128d04c: stur            w3, [x1, #0x1f]
    // 0x128d050: r0 = Text()
    //     0x128d050: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128d054: mov             x3, x0
    // 0x128d058: r0 = "View Bag"
    //     0x128d058: add             x0, PP, #0x48, lsl #12  ; [pp+0x488d0] "View Bag"
    //     0x128d05c: ldr             x0, [x0, #0x8d0]
    // 0x128d060: stur            x3, [fp, #-0x28]
    // 0x128d064: StoreField: r3->field_b = r0
    //     0x128d064: stur            w0, [x3, #0xb]
    // 0x128d068: ldur            x0, [fp, #-8]
    // 0x128d06c: StoreField: r3->field_13 = r0
    //     0x128d06c: stur            w0, [x3, #0x13]
    // 0x128d070: ldur            x2, [fp, #-0x10]
    // 0x128d074: r1 = Function '<anonymous closure>':.
    //     0x128d074: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c40] AnonymousClosure: (0x128d35c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons (0x128cf1c)
    //     0x128d078: ldr             x1, [x1, #0xc40]
    // 0x128d07c: r0 = AllocateClosure()
    //     0x128d07c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128d080: stur            x0, [fp, #-8]
    // 0x128d084: r0 = TextButton()
    //     0x128d084: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x128d088: mov             x1, x0
    // 0x128d08c: ldur            x0, [fp, #-8]
    // 0x128d090: stur            x1, [fp, #-0x30]
    // 0x128d094: StoreField: r1->field_b = r0
    //     0x128d094: stur            w0, [x1, #0xb]
    // 0x128d098: r0 = false
    //     0x128d098: add             x0, NULL, #0x30  ; false
    // 0x128d09c: StoreField: r1->field_27 = r0
    //     0x128d09c: stur            w0, [x1, #0x27]
    // 0x128d0a0: r2 = true
    //     0x128d0a0: add             x2, NULL, #0x20  ; true
    // 0x128d0a4: StoreField: r1->field_2f = r2
    //     0x128d0a4: stur            w2, [x1, #0x2f]
    // 0x128d0a8: ldur            x3, [fp, #-0x28]
    // 0x128d0ac: StoreField: r1->field_37 = r3
    //     0x128d0ac: stur            w3, [x1, #0x37]
    // 0x128d0b0: r0 = TextButtonTheme()
    //     0x128d0b0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x128d0b4: mov             x2, x0
    // 0x128d0b8: ldur            x0, [fp, #-0x20]
    // 0x128d0bc: stur            x2, [fp, #-8]
    // 0x128d0c0: StoreField: r2->field_f = r0
    //     0x128d0c0: stur            w0, [x2, #0xf]
    // 0x128d0c4: ldur            x0, [fp, #-0x30]
    // 0x128d0c8: StoreField: r2->field_b = r0
    //     0x128d0c8: stur            w0, [x2, #0xb]
    // 0x128d0cc: r1 = <FlexParentData>
    //     0x128d0cc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x128d0d0: ldr             x1, [x1, #0xe00]
    // 0x128d0d4: r0 = Flexible()
    //     0x128d0d4: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x128d0d8: mov             x1, x0
    // 0x128d0dc: r0 = 1
    //     0x128d0dc: movz            x0, #0x1
    // 0x128d0e0: stur            x1, [fp, #-0x20]
    // 0x128d0e4: StoreField: r1->field_13 = r0
    //     0x128d0e4: stur            x0, [x1, #0x13]
    // 0x128d0e8: r2 = Instance_FlexFit
    //     0x128d0e8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x128d0ec: ldr             x2, [x2, #0xe08]
    // 0x128d0f0: StoreField: r1->field_1b = r2
    //     0x128d0f0: stur            w2, [x1, #0x1b]
    // 0x128d0f4: ldur            x3, [fp, #-8]
    // 0x128d0f8: StoreField: r1->field_b = r3
    //     0x128d0f8: stur            w3, [x1, #0xb]
    // 0x128d0fc: r16 = <EdgeInsets>
    //     0x128d0fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x128d100: ldr             x16, [x16, #0xda0]
    // 0x128d104: r30 = Instance_EdgeInsets
    //     0x128d104: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x128d108: ldr             lr, [lr, #0x1f0]
    // 0x128d10c: stp             lr, x16, [SP]
    // 0x128d110: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128d110: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128d114: r0 = all()
    //     0x128d114: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128d118: stur            x0, [fp, #-8]
    // 0x128d11c: r16 = <Color>
    //     0x128d11c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x128d120: ldr             x16, [x16, #0xf80]
    // 0x128d124: ldur            lr, [fp, #-0x18]
    // 0x128d128: stp             lr, x16, [SP]
    // 0x128d12c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128d12c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128d130: r0 = all()
    //     0x128d130: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128d134: stur            x0, [fp, #-0x18]
    // 0x128d138: r16 = <RoundedRectangleBorder>
    //     0x128d138: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x128d13c: ldr             x16, [x16, #0xf78]
    // 0x128d140: r30 = Instance_RoundedRectangleBorder
    //     0x128d140: add             lr, PP, #0x3f, lsl #12  ; [pp+0x3f888] Obj!RoundedRectangleBorder@d5ac01
    //     0x128d144: ldr             lr, [lr, #0x888]
    // 0x128d148: stp             lr, x16, [SP]
    // 0x128d14c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128d14c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128d150: r0 = all()
    //     0x128d150: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128d154: stur            x0, [fp, #-0x28]
    // 0x128d158: r0 = ButtonStyle()
    //     0x128d158: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x128d15c: mov             x1, x0
    // 0x128d160: ldur            x0, [fp, #-0x18]
    // 0x128d164: stur            x1, [fp, #-0x30]
    // 0x128d168: StoreField: r1->field_b = r0
    //     0x128d168: stur            w0, [x1, #0xb]
    // 0x128d16c: ldur            x0, [fp, #-8]
    // 0x128d170: StoreField: r1->field_23 = r0
    //     0x128d170: stur            w0, [x1, #0x23]
    // 0x128d174: ldur            x0, [fp, #-0x28]
    // 0x128d178: StoreField: r1->field_43 = r0
    //     0x128d178: stur            w0, [x1, #0x43]
    // 0x128d17c: r0 = TextButtonThemeData()
    //     0x128d17c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x128d180: mov             x3, x0
    // 0x128d184: ldur            x0, [fp, #-0x30]
    // 0x128d188: stur            x3, [fp, #-8]
    // 0x128d18c: StoreField: r3->field_7 = r0
    //     0x128d18c: stur            w0, [x3, #7]
    // 0x128d190: ldur            x2, [fp, #-0x10]
    // 0x128d194: r1 = Function '<anonymous closure>':.
    //     0x128d194: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c48] AnonymousClosure: (0x128d2f8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildDualButtons (0x128cf1c)
    //     0x128d198: ldr             x1, [x1, #0xc48]
    // 0x128d19c: r0 = AllocateClosure()
    //     0x128d19c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128d1a0: stur            x0, [fp, #-0x10]
    // 0x128d1a4: r0 = TextButton()
    //     0x128d1a4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x128d1a8: mov             x1, x0
    // 0x128d1ac: ldur            x0, [fp, #-0x10]
    // 0x128d1b0: stur            x1, [fp, #-0x18]
    // 0x128d1b4: StoreField: r1->field_b = r0
    //     0x128d1b4: stur            w0, [x1, #0xb]
    // 0x128d1b8: r0 = false
    //     0x128d1b8: add             x0, NULL, #0x30  ; false
    // 0x128d1bc: StoreField: r1->field_27 = r0
    //     0x128d1bc: stur            w0, [x1, #0x27]
    // 0x128d1c0: r0 = true
    //     0x128d1c0: add             x0, NULL, #0x20  ; true
    // 0x128d1c4: StoreField: r1->field_2f = r0
    //     0x128d1c4: stur            w0, [x1, #0x2f]
    // 0x128d1c8: r0 = Instance_Text
    //     0x128d1c8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48c50] Obj!Text@d65b61
    //     0x128d1cc: ldr             x0, [x0, #0xc50]
    // 0x128d1d0: StoreField: r1->field_37 = r0
    //     0x128d1d0: stur            w0, [x1, #0x37]
    // 0x128d1d4: r0 = TextButtonTheme()
    //     0x128d1d4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x128d1d8: mov             x2, x0
    // 0x128d1dc: ldur            x0, [fp, #-8]
    // 0x128d1e0: stur            x2, [fp, #-0x10]
    // 0x128d1e4: StoreField: r2->field_f = r0
    //     0x128d1e4: stur            w0, [x2, #0xf]
    // 0x128d1e8: ldur            x0, [fp, #-0x18]
    // 0x128d1ec: StoreField: r2->field_b = r0
    //     0x128d1ec: stur            w0, [x2, #0xb]
    // 0x128d1f0: r1 = <FlexParentData>
    //     0x128d1f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x128d1f4: ldr             x1, [x1, #0xe00]
    // 0x128d1f8: r0 = Flexible()
    //     0x128d1f8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x128d1fc: mov             x3, x0
    // 0x128d200: r0 = 1
    //     0x128d200: movz            x0, #0x1
    // 0x128d204: stur            x3, [fp, #-8]
    // 0x128d208: StoreField: r3->field_13 = r0
    //     0x128d208: stur            x0, [x3, #0x13]
    // 0x128d20c: r0 = Instance_FlexFit
    //     0x128d20c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x128d210: ldr             x0, [x0, #0xe08]
    // 0x128d214: StoreField: r3->field_1b = r0
    //     0x128d214: stur            w0, [x3, #0x1b]
    // 0x128d218: ldur            x0, [fp, #-0x10]
    // 0x128d21c: StoreField: r3->field_b = r0
    //     0x128d21c: stur            w0, [x3, #0xb]
    // 0x128d220: r1 = Null
    //     0x128d220: mov             x1, NULL
    // 0x128d224: r2 = 6
    //     0x128d224: movz            x2, #0x6
    // 0x128d228: r0 = AllocateArray()
    //     0x128d228: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128d22c: mov             x2, x0
    // 0x128d230: ldur            x0, [fp, #-0x20]
    // 0x128d234: stur            x2, [fp, #-0x10]
    // 0x128d238: StoreField: r2->field_f = r0
    //     0x128d238: stur            w0, [x2, #0xf]
    // 0x128d23c: r16 = Instance_SizedBox
    //     0x128d23c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0x128d240: ldr             x16, [x16, #0x998]
    // 0x128d244: StoreField: r2->field_13 = r16
    //     0x128d244: stur            w16, [x2, #0x13]
    // 0x128d248: ldur            x0, [fp, #-8]
    // 0x128d24c: ArrayStore: r2[0] = r0  ; List_4
    //     0x128d24c: stur            w0, [x2, #0x17]
    // 0x128d250: r1 = <Widget>
    //     0x128d250: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128d254: r0 = AllocateGrowableArray()
    //     0x128d254: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128d258: mov             x1, x0
    // 0x128d25c: ldur            x0, [fp, #-0x10]
    // 0x128d260: stur            x1, [fp, #-8]
    // 0x128d264: StoreField: r1->field_f = r0
    //     0x128d264: stur            w0, [x1, #0xf]
    // 0x128d268: r0 = 6
    //     0x128d268: movz            x0, #0x6
    // 0x128d26c: StoreField: r1->field_b = r0
    //     0x128d26c: stur            w0, [x1, #0xb]
    // 0x128d270: r0 = Row()
    //     0x128d270: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x128d274: mov             x1, x0
    // 0x128d278: r0 = Instance_Axis
    //     0x128d278: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128d27c: stur            x1, [fp, #-0x10]
    // 0x128d280: StoreField: r1->field_f = r0
    //     0x128d280: stur            w0, [x1, #0xf]
    // 0x128d284: r0 = Instance_MainAxisAlignment
    //     0x128d284: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x128d288: ldr             x0, [x0, #0xd10]
    // 0x128d28c: StoreField: r1->field_13 = r0
    //     0x128d28c: stur            w0, [x1, #0x13]
    // 0x128d290: r0 = Instance_MainAxisSize
    //     0x128d290: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128d294: ldr             x0, [x0, #0xa10]
    // 0x128d298: ArrayStore: r1[0] = r0  ; List_4
    //     0x128d298: stur            w0, [x1, #0x17]
    // 0x128d29c: r0 = Instance_CrossAxisAlignment
    //     0x128d29c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128d2a0: ldr             x0, [x0, #0xa18]
    // 0x128d2a4: StoreField: r1->field_1b = r0
    //     0x128d2a4: stur            w0, [x1, #0x1b]
    // 0x128d2a8: r0 = Instance_VerticalDirection
    //     0x128d2a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128d2ac: ldr             x0, [x0, #0xa20]
    // 0x128d2b0: StoreField: r1->field_23 = r0
    //     0x128d2b0: stur            w0, [x1, #0x23]
    // 0x128d2b4: r0 = Instance_Clip
    //     0x128d2b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128d2b8: ldr             x0, [x0, #0x38]
    // 0x128d2bc: StoreField: r1->field_2b = r0
    //     0x128d2bc: stur            w0, [x1, #0x2b]
    // 0x128d2c0: StoreField: r1->field_2f = rZR
    //     0x128d2c0: stur            xzr, [x1, #0x2f]
    // 0x128d2c4: ldur            x0, [fp, #-8]
    // 0x128d2c8: StoreField: r1->field_b = r0
    //     0x128d2c8: stur            w0, [x1, #0xb]
    // 0x128d2cc: r0 = Padding()
    //     0x128d2cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128d2d0: r1 = Instance_EdgeInsets
    //     0x128d2d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x128d2d4: ldr             x1, [x1, #0x668]
    // 0x128d2d8: StoreField: r0->field_f = r1
    //     0x128d2d8: stur            w1, [x0, #0xf]
    // 0x128d2dc: ldur            x1, [fp, #-0x10]
    // 0x128d2e0: StoreField: r0->field_b = r1
    //     0x128d2e0: stur            w1, [x0, #0xb]
    // 0x128d2e4: LeaveFrame
    //     0x128d2e4: mov             SP, fp
    //     0x128d2e8: ldp             fp, lr, [SP], #0x10
    // 0x128d2ec: ret
    //     0x128d2ec: ret             
    // 0x128d2f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128d2f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128d2f4: b               #0x128cf38
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x128d2f8, size: 0x64
    // 0x128d2f8: EnterFrame
    //     0x128d2f8: stp             fp, lr, [SP, #-0x10]!
    //     0x128d2fc: mov             fp, SP
    // 0x128d300: AllocStack(0x8)
    //     0x128d300: sub             SP, SP, #8
    // 0x128d304: SetupParameters()
    //     0x128d304: ldr             x0, [fp, #0x10]
    //     0x128d308: ldur            w1, [x0, #0x17]
    //     0x128d30c: add             x1, x1, HEAP, lsl #32
    // 0x128d310: CheckStackOverflow
    //     0x128d310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128d314: cmp             SP, x16
    //     0x128d318: b.ls            #0x128d354
    // 0x128d31c: LoadField: r0 = r1->field_f
    //     0x128d31c: ldur            w0, [x1, #0xf]
    // 0x128d320: DecompressPointer r0
    //     0x128d320: add             x0, x0, HEAP, lsl #32
    // 0x128d324: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x128d324: ldur            w1, [x0, #0x17]
    // 0x128d328: DecompressPointer r1
    //     0x128d328: add             x1, x1, HEAP, lsl #32
    // 0x128d32c: str             x1, [SP]
    // 0x128d330: r4 = 0
    //     0x128d330: movz            x4, #0
    // 0x128d334: ldr             x0, [SP]
    // 0x128d338: r16 = UnlinkedCall_0x613b5c
    //     0x128d338: add             x16, PP, #0x48, lsl #12  ; [pp+0x48c58] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x128d33c: add             x16, x16, #0xc58
    // 0x128d340: ldp             x5, lr, [x16]
    // 0x128d344: blr             lr
    // 0x128d348: LeaveFrame
    //     0x128d348: mov             SP, fp
    //     0x128d34c: ldp             fp, lr, [SP], #0x10
    // 0x128d350: ret
    //     0x128d350: ret             
    // 0x128d354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128d354: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128d358: b               #0x128d31c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x128d35c, size: 0x64
    // 0x128d35c: EnterFrame
    //     0x128d35c: stp             fp, lr, [SP, #-0x10]!
    //     0x128d360: mov             fp, SP
    // 0x128d364: AllocStack(0x8)
    //     0x128d364: sub             SP, SP, #8
    // 0x128d368: SetupParameters()
    //     0x128d368: ldr             x0, [fp, #0x10]
    //     0x128d36c: ldur            w1, [x0, #0x17]
    //     0x128d370: add             x1, x1, HEAP, lsl #32
    // 0x128d374: CheckStackOverflow
    //     0x128d374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128d378: cmp             SP, x16
    //     0x128d37c: b.ls            #0x128d3b8
    // 0x128d380: LoadField: r0 = r1->field_f
    //     0x128d380: ldur            w0, [x1, #0xf]
    // 0x128d384: DecompressPointer r0
    //     0x128d384: add             x0, x0, HEAP, lsl #32
    // 0x128d388: LoadField: r1 = r0->field_13
    //     0x128d388: ldur            w1, [x0, #0x13]
    // 0x128d38c: DecompressPointer r1
    //     0x128d38c: add             x1, x1, HEAP, lsl #32
    // 0x128d390: str             x1, [SP]
    // 0x128d394: r4 = 0
    //     0x128d394: movz            x4, #0
    // 0x128d398: ldr             x0, [SP]
    // 0x128d39c: r16 = UnlinkedCall_0x613b5c
    //     0x128d39c: add             x16, PP, #0x48, lsl #12  ; [pp+0x48c68] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x128d3a0: add             x16, x16, #0xc68
    // 0x128d3a4: ldp             x5, lr, [x16]
    // 0x128d3a8: blr             lr
    // 0x128d3ac: LeaveFrame
    //     0x128d3ac: mov             SP, fp
    //     0x128d3b0: ldp             fp, lr, [SP], #0x10
    // 0x128d3b4: ret
    //     0x128d3b4: ret             
    // 0x128d3b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128d3b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128d3bc: b               #0x128d380
  }
  _ _buildSingleButton(/* No info */) {
    // ** addr: 0x128d3c0, size: 0x3ac
    // 0x128d3c0: EnterFrame
    //     0x128d3c0: stp             fp, lr, [SP, #-0x10]!
    //     0x128d3c4: mov             fp, SP
    // 0x128d3c8: AllocStack(0x58)
    //     0x128d3c8: sub             SP, SP, #0x58
    // 0x128d3cc: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */)
    //     0x128d3cc: stur            x1, [fp, #-8]
    // 0x128d3d0: CheckStackOverflow
    //     0x128d3d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128d3d4: cmp             SP, x16
    //     0x128d3d8: b.ls            #0x128d764
    // 0x128d3dc: r1 = 1
    //     0x128d3dc: movz            x1, #0x1
    // 0x128d3e0: r0 = AllocateContext()
    //     0x128d3e0: bl              #0x16f6108  ; AllocateContextStub
    // 0x128d3e4: mov             x1, x0
    // 0x128d3e8: ldur            x0, [fp, #-8]
    // 0x128d3ec: stur            x1, [fp, #-0x10]
    // 0x128d3f0: StoreField: r1->field_f = r0
    //     0x128d3f0: stur            w0, [x1, #0xf]
    // 0x128d3f4: r16 = <EdgeInsets>
    //     0x128d3f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x128d3f8: ldr             x16, [x16, #0xda0]
    // 0x128d3fc: r30 = Instance_EdgeInsets
    //     0x128d3fc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x128d400: ldr             lr, [lr, #0x1f0]
    // 0x128d404: stp             lr, x16, [SP]
    // 0x128d408: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128d408: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128d40c: r0 = all()
    //     0x128d40c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128d410: mov             x1, x0
    // 0x128d414: ldur            x0, [fp, #-8]
    // 0x128d418: stur            x1, [fp, #-0x30]
    // 0x128d41c: LoadField: r2 = r0->field_1f
    //     0x128d41c: ldur            w2, [x0, #0x1f]
    // 0x128d420: DecompressPointer r2
    //     0x128d420: add             x2, x2, HEAP, lsl #32
    // 0x128d424: LoadField: r3 = r2->field_3f
    //     0x128d424: ldur            w3, [x2, #0x3f]
    // 0x128d428: DecompressPointer r3
    //     0x128d428: add             x3, x3, HEAP, lsl #32
    // 0x128d42c: cmp             w3, NULL
    // 0x128d430: b.ne            #0x128d43c
    // 0x128d434: r2 = Null
    //     0x128d434: mov             x2, NULL
    // 0x128d438: b               #0x128d460
    // 0x128d43c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x128d43c: ldur            w2, [x3, #0x17]
    // 0x128d440: DecompressPointer r2
    //     0x128d440: add             x2, x2, HEAP, lsl #32
    // 0x128d444: cmp             w2, NULL
    // 0x128d448: b.ne            #0x128d454
    // 0x128d44c: r2 = Null
    //     0x128d44c: mov             x2, NULL
    // 0x128d450: b               #0x128d460
    // 0x128d454: LoadField: r4 = r2->field_7
    //     0x128d454: ldur            w4, [x2, #7]
    // 0x128d458: DecompressPointer r4
    //     0x128d458: add             x4, x4, HEAP, lsl #32
    // 0x128d45c: mov             x2, x4
    // 0x128d460: cmp             w2, NULL
    // 0x128d464: b.ne            #0x128d470
    // 0x128d468: r2 = 0
    //     0x128d468: movz            x2, #0
    // 0x128d46c: b               #0x128d480
    // 0x128d470: r4 = LoadInt32Instr(r2)
    //     0x128d470: sbfx            x4, x2, #1, #0x1f
    //     0x128d474: tbz             w2, #0, #0x128d47c
    //     0x128d478: ldur            x4, [x2, #7]
    // 0x128d47c: mov             x2, x4
    // 0x128d480: stur            x2, [fp, #-0x28]
    // 0x128d484: cmp             w3, NULL
    // 0x128d488: b.ne            #0x128d494
    // 0x128d48c: r4 = Null
    //     0x128d48c: mov             x4, NULL
    // 0x128d490: b               #0x128d4b8
    // 0x128d494: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x128d494: ldur            w4, [x3, #0x17]
    // 0x128d498: DecompressPointer r4
    //     0x128d498: add             x4, x4, HEAP, lsl #32
    // 0x128d49c: cmp             w4, NULL
    // 0x128d4a0: b.ne            #0x128d4ac
    // 0x128d4a4: r4 = Null
    //     0x128d4a4: mov             x4, NULL
    // 0x128d4a8: b               #0x128d4b8
    // 0x128d4ac: LoadField: r5 = r4->field_b
    //     0x128d4ac: ldur            w5, [x4, #0xb]
    // 0x128d4b0: DecompressPointer r5
    //     0x128d4b0: add             x5, x5, HEAP, lsl #32
    // 0x128d4b4: mov             x4, x5
    // 0x128d4b8: cmp             w4, NULL
    // 0x128d4bc: b.ne            #0x128d4c8
    // 0x128d4c0: r4 = 0
    //     0x128d4c0: movz            x4, #0
    // 0x128d4c4: b               #0x128d4d8
    // 0x128d4c8: r5 = LoadInt32Instr(r4)
    //     0x128d4c8: sbfx            x5, x4, #1, #0x1f
    //     0x128d4cc: tbz             w4, #0, #0x128d4d4
    //     0x128d4d0: ldur            x5, [x4, #7]
    // 0x128d4d4: mov             x4, x5
    // 0x128d4d8: stur            x4, [fp, #-0x20]
    // 0x128d4dc: cmp             w3, NULL
    // 0x128d4e0: b.ne            #0x128d4ec
    // 0x128d4e4: r3 = Null
    //     0x128d4e4: mov             x3, NULL
    // 0x128d4e8: b               #0x128d50c
    // 0x128d4ec: ArrayLoad: r5 = r3[0]  ; List_4
    //     0x128d4ec: ldur            w5, [x3, #0x17]
    // 0x128d4f0: DecompressPointer r5
    //     0x128d4f0: add             x5, x5, HEAP, lsl #32
    // 0x128d4f4: cmp             w5, NULL
    // 0x128d4f8: b.ne            #0x128d504
    // 0x128d4fc: r3 = Null
    //     0x128d4fc: mov             x3, NULL
    // 0x128d500: b               #0x128d50c
    // 0x128d504: LoadField: r3 = r5->field_f
    //     0x128d504: ldur            w3, [x5, #0xf]
    // 0x128d508: DecompressPointer r3
    //     0x128d508: add             x3, x3, HEAP, lsl #32
    // 0x128d50c: cmp             w3, NULL
    // 0x128d510: b.ne            #0x128d51c
    // 0x128d514: r3 = 0
    //     0x128d514: movz            x3, #0
    // 0x128d518: b               #0x128d52c
    // 0x128d51c: r5 = LoadInt32Instr(r3)
    //     0x128d51c: sbfx            x5, x3, #1, #0x1f
    //     0x128d520: tbz             w3, #0, #0x128d528
    //     0x128d524: ldur            x5, [x3, #7]
    // 0x128d528: mov             x3, x5
    // 0x128d52c: stur            x3, [fp, #-0x18]
    // 0x128d530: r0 = Color()
    //     0x128d530: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x128d534: mov             x1, x0
    // 0x128d538: r0 = Instance_ColorSpace
    //     0x128d538: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x128d53c: StoreField: r1->field_27 = r0
    //     0x128d53c: stur            w0, [x1, #0x27]
    // 0x128d540: d0 = 0.030000
    //     0x128d540: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x128d544: ldr             d0, [x17, #0x238]
    // 0x128d548: StoreField: r1->field_7 = d0
    //     0x128d548: stur            d0, [x1, #7]
    // 0x128d54c: ldur            x0, [fp, #-0x28]
    // 0x128d550: ubfx            x0, x0, #0, #0x20
    // 0x128d554: and             w2, w0, #0xff
    // 0x128d558: ubfx            x2, x2, #0, #0x20
    // 0x128d55c: scvtf           d0, x2
    // 0x128d560: d1 = 255.000000
    //     0x128d560: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x128d564: fdiv            d2, d0, d1
    // 0x128d568: StoreField: r1->field_f = d2
    //     0x128d568: stur            d2, [x1, #0xf]
    // 0x128d56c: ldur            x0, [fp, #-0x20]
    // 0x128d570: ubfx            x0, x0, #0, #0x20
    // 0x128d574: and             w2, w0, #0xff
    // 0x128d578: ubfx            x2, x2, #0, #0x20
    // 0x128d57c: scvtf           d0, x2
    // 0x128d580: fdiv            d2, d0, d1
    // 0x128d584: ArrayStore: r1[0] = d2  ; List_8
    //     0x128d584: stur            d2, [x1, #0x17]
    // 0x128d588: ldur            x0, [fp, #-0x18]
    // 0x128d58c: ubfx            x0, x0, #0, #0x20
    // 0x128d590: and             w2, w0, #0xff
    // 0x128d594: ubfx            x2, x2, #0, #0x20
    // 0x128d598: scvtf           d0, x2
    // 0x128d59c: fdiv            d2, d0, d1
    // 0x128d5a0: StoreField: r1->field_1f = d2
    //     0x128d5a0: stur            d2, [x1, #0x1f]
    // 0x128d5a4: r16 = <Color>
    //     0x128d5a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x128d5a8: ldr             x16, [x16, #0xf80]
    // 0x128d5ac: stp             x1, x16, [SP]
    // 0x128d5b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128d5b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128d5b4: r0 = all()
    //     0x128d5b4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128d5b8: mov             x1, x0
    // 0x128d5bc: ldur            x0, [fp, #-8]
    // 0x128d5c0: stur            x1, [fp, #-0x40]
    // 0x128d5c4: LoadField: r2 = r0->field_2b
    //     0x128d5c4: ldur            w2, [x0, #0x2b]
    // 0x128d5c8: DecompressPointer r2
    //     0x128d5c8: add             x2, x2, HEAP, lsl #32
    // 0x128d5cc: stur            x2, [fp, #-0x38]
    // 0x128d5d0: r0 = BorderSide()
    //     0x128d5d0: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x128d5d4: mov             x1, x0
    // 0x128d5d8: ldur            x0, [fp, #-0x38]
    // 0x128d5dc: stur            x1, [fp, #-0x48]
    // 0x128d5e0: StoreField: r1->field_7 = r0
    //     0x128d5e0: stur            w0, [x1, #7]
    // 0x128d5e4: d0 = 1.000000
    //     0x128d5e4: fmov            d0, #1.00000000
    // 0x128d5e8: StoreField: r1->field_b = d0
    //     0x128d5e8: stur            d0, [x1, #0xb]
    // 0x128d5ec: r0 = Instance_BorderStyle
    //     0x128d5ec: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x128d5f0: ldr             x0, [x0, #0xf68]
    // 0x128d5f4: StoreField: r1->field_13 = r0
    //     0x128d5f4: stur            w0, [x1, #0x13]
    // 0x128d5f8: d0 = -1.000000
    //     0x128d5f8: fmov            d0, #-1.00000000
    // 0x128d5fc: ArrayStore: r1[0] = d0  ; List_8
    //     0x128d5fc: stur            d0, [x1, #0x17]
    // 0x128d600: r0 = RoundedRectangleBorder()
    //     0x128d600: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x128d604: mov             x1, x0
    // 0x128d608: r0 = Instance_BorderRadius
    //     0x128d608: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fdc0] Obj!BorderRadius@d5a301
    //     0x128d60c: ldr             x0, [x0, #0xdc0]
    // 0x128d610: StoreField: r1->field_b = r0
    //     0x128d610: stur            w0, [x1, #0xb]
    // 0x128d614: ldur            x0, [fp, #-0x48]
    // 0x128d618: StoreField: r1->field_7 = r0
    //     0x128d618: stur            w0, [x1, #7]
    // 0x128d61c: r16 = <RoundedRectangleBorder>
    //     0x128d61c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x128d620: ldr             x16, [x16, #0xf78]
    // 0x128d624: stp             x1, x16, [SP]
    // 0x128d628: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x128d628: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x128d62c: r0 = all()
    //     0x128d62c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x128d630: stur            x0, [fp, #-0x38]
    // 0x128d634: r0 = ButtonStyle()
    //     0x128d634: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x128d638: mov             x1, x0
    // 0x128d63c: ldur            x0, [fp, #-0x40]
    // 0x128d640: stur            x1, [fp, #-0x48]
    // 0x128d644: StoreField: r1->field_b = r0
    //     0x128d644: stur            w0, [x1, #0xb]
    // 0x128d648: ldur            x0, [fp, #-0x30]
    // 0x128d64c: StoreField: r1->field_23 = r0
    //     0x128d64c: stur            w0, [x1, #0x23]
    // 0x128d650: ldur            x0, [fp, #-0x38]
    // 0x128d654: StoreField: r1->field_43 = r0
    //     0x128d654: stur            w0, [x1, #0x43]
    // 0x128d658: r0 = TextButtonThemeData()
    //     0x128d658: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x128d65c: mov             x1, x0
    // 0x128d660: ldur            x0, [fp, #-0x48]
    // 0x128d664: stur            x1, [fp, #-0x38]
    // 0x128d668: StoreField: r1->field_7 = r0
    //     0x128d668: stur            w0, [x1, #7]
    // 0x128d66c: ldur            x0, [fp, #-8]
    // 0x128d670: LoadField: r2 = r0->field_27
    //     0x128d670: ldur            w2, [x0, #0x27]
    // 0x128d674: DecompressPointer r2
    //     0x128d674: add             x2, x2, HEAP, lsl #32
    // 0x128d678: stur            x2, [fp, #-0x30]
    // 0x128d67c: r0 = TextStyle()
    //     0x128d67c: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0x128d680: mov             x1, x0
    // 0x128d684: r0 = true
    //     0x128d684: add             x0, NULL, #0x20  ; true
    // 0x128d688: stur            x1, [fp, #-8]
    // 0x128d68c: StoreField: r1->field_7 = r0
    //     0x128d68c: stur            w0, [x1, #7]
    // 0x128d690: ldur            x2, [fp, #-0x30]
    // 0x128d694: StoreField: r1->field_b = r2
    //     0x128d694: stur            w2, [x1, #0xb]
    // 0x128d698: r2 = 16.000000
    //     0x128d698: add             x2, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x128d69c: ldr             x2, [x2, #0x188]
    // 0x128d6a0: StoreField: r1->field_1f = r2
    //     0x128d6a0: stur            w2, [x1, #0x1f]
    // 0x128d6a4: r0 = Text()
    //     0x128d6a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128d6a8: mov             x3, x0
    // 0x128d6ac: r0 = "View Bag"
    //     0x128d6ac: add             x0, PP, #0x48, lsl #12  ; [pp+0x488d0] "View Bag"
    //     0x128d6b0: ldr             x0, [x0, #0x8d0]
    // 0x128d6b4: stur            x3, [fp, #-0x30]
    // 0x128d6b8: StoreField: r3->field_b = r0
    //     0x128d6b8: stur            w0, [x3, #0xb]
    // 0x128d6bc: ldur            x0, [fp, #-8]
    // 0x128d6c0: StoreField: r3->field_13 = r0
    //     0x128d6c0: stur            w0, [x3, #0x13]
    // 0x128d6c4: ldur            x2, [fp, #-0x10]
    // 0x128d6c8: r1 = Function '<anonymous closure>':.
    //     0x128d6c8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c78] AnonymousClosure: (0x128d76c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildSingleButton (0x128d3c0)
    //     0x128d6cc: ldr             x1, [x1, #0xc78]
    // 0x128d6d0: r0 = AllocateClosure()
    //     0x128d6d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128d6d4: stur            x0, [fp, #-8]
    // 0x128d6d8: r0 = TextButton()
    //     0x128d6d8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x128d6dc: mov             x1, x0
    // 0x128d6e0: ldur            x0, [fp, #-8]
    // 0x128d6e4: stur            x1, [fp, #-0x10]
    // 0x128d6e8: StoreField: r1->field_b = r0
    //     0x128d6e8: stur            w0, [x1, #0xb]
    // 0x128d6ec: r0 = false
    //     0x128d6ec: add             x0, NULL, #0x30  ; false
    // 0x128d6f0: StoreField: r1->field_27 = r0
    //     0x128d6f0: stur            w0, [x1, #0x27]
    // 0x128d6f4: r0 = true
    //     0x128d6f4: add             x0, NULL, #0x20  ; true
    // 0x128d6f8: StoreField: r1->field_2f = r0
    //     0x128d6f8: stur            w0, [x1, #0x2f]
    // 0x128d6fc: ldur            x0, [fp, #-0x30]
    // 0x128d700: StoreField: r1->field_37 = r0
    //     0x128d700: stur            w0, [x1, #0x37]
    // 0x128d704: r0 = TextButtonTheme()
    //     0x128d704: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x128d708: mov             x1, x0
    // 0x128d70c: ldur            x0, [fp, #-0x38]
    // 0x128d710: stur            x1, [fp, #-8]
    // 0x128d714: StoreField: r1->field_f = r0
    //     0x128d714: stur            w0, [x1, #0xf]
    // 0x128d718: ldur            x0, [fp, #-0x10]
    // 0x128d71c: StoreField: r1->field_b = r0
    //     0x128d71c: stur            w0, [x1, #0xb]
    // 0x128d720: r0 = SizedBox()
    //     0x128d720: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x128d724: mov             x1, x0
    // 0x128d728: r0 = inf
    //     0x128d728: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x128d72c: ldr             x0, [x0, #0x9f8]
    // 0x128d730: stur            x1, [fp, #-0x10]
    // 0x128d734: StoreField: r1->field_f = r0
    //     0x128d734: stur            w0, [x1, #0xf]
    // 0x128d738: ldur            x0, [fp, #-8]
    // 0x128d73c: StoreField: r1->field_b = r0
    //     0x128d73c: stur            w0, [x1, #0xb]
    // 0x128d740: r0 = Padding()
    //     0x128d740: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128d744: r1 = Instance_EdgeInsets
    //     0x128d744: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x128d748: ldr             x1, [x1, #0x668]
    // 0x128d74c: StoreField: r0->field_f = r1
    //     0x128d74c: stur            w1, [x0, #0xf]
    // 0x128d750: ldur            x1, [fp, #-0x10]
    // 0x128d754: StoreField: r0->field_b = r1
    //     0x128d754: stur            w1, [x0, #0xb]
    // 0x128d758: LeaveFrame
    //     0x128d758: mov             SP, fp
    //     0x128d75c: ldp             fp, lr, [SP], #0x10
    // 0x128d760: ret
    //     0x128d760: ret             
    // 0x128d764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128d764: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128d768: b               #0x128d3dc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x128d76c, size: 0x64
    // 0x128d76c: EnterFrame
    //     0x128d76c: stp             fp, lr, [SP, #-0x10]!
    //     0x128d770: mov             fp, SP
    // 0x128d774: AllocStack(0x8)
    //     0x128d774: sub             SP, SP, #8
    // 0x128d778: SetupParameters()
    //     0x128d778: ldr             x0, [fp, #0x10]
    //     0x128d77c: ldur            w1, [x0, #0x17]
    //     0x128d780: add             x1, x1, HEAP, lsl #32
    // 0x128d784: CheckStackOverflow
    //     0x128d784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128d788: cmp             SP, x16
    //     0x128d78c: b.ls            #0x128d7c8
    // 0x128d790: LoadField: r0 = r1->field_f
    //     0x128d790: ldur            w0, [x1, #0xf]
    // 0x128d794: DecompressPointer r0
    //     0x128d794: add             x0, x0, HEAP, lsl #32
    // 0x128d798: LoadField: r1 = r0->field_13
    //     0x128d798: ldur            w1, [x0, #0x13]
    // 0x128d79c: DecompressPointer r1
    //     0x128d79c: add             x1, x1, HEAP, lsl #32
    // 0x128d7a0: str             x1, [SP]
    // 0x128d7a4: r4 = 0
    //     0x128d7a4: movz            x4, #0
    // 0x128d7a8: ldr             x0, [SP]
    // 0x128d7ac: r16 = UnlinkedCall_0x613b5c
    //     0x128d7ac: add             x16, PP, #0x48, lsl #12  ; [pp+0x48c80] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x128d7b0: add             x16, x16, #0xc80
    // 0x128d7b4: ldp             x5, lr, [x16]
    // 0x128d7b8: blr             lr
    // 0x128d7bc: LeaveFrame
    //     0x128d7bc: mov             SP, fp
    //     0x128d7c0: ldp             fp, lr, [SP], #0x10
    // 0x128d7c4: ret
    //     0x128d7c4: ret             
    // 0x128d7c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128d7c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128d7cc: b               #0x128d790
  }
  _ _buildItemsList(/* No info */) {
    // ** addr: 0x128d7d0, size: 0x118
    // 0x128d7d0: EnterFrame
    //     0x128d7d0: stp             fp, lr, [SP, #-0x10]!
    //     0x128d7d4: mov             fp, SP
    // 0x128d7d8: AllocStack(0x28)
    //     0x128d7d8: sub             SP, SP, #0x28
    // 0x128d7dc: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x128d7dc: stur            x1, [fp, #-8]
    //     0x128d7e0: stur            x2, [fp, #-0x10]
    // 0x128d7e4: CheckStackOverflow
    //     0x128d7e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128d7e8: cmp             SP, x16
    //     0x128d7ec: b.ls            #0x128d8e0
    // 0x128d7f0: r1 = 2
    //     0x128d7f0: movz            x1, #0x2
    // 0x128d7f4: r0 = AllocateContext()
    //     0x128d7f4: bl              #0x16f6108  ; AllocateContextStub
    // 0x128d7f8: mov             x1, x0
    // 0x128d7fc: ldur            x0, [fp, #-8]
    // 0x128d800: StoreField: r1->field_f = r0
    //     0x128d800: stur            w0, [x1, #0xf]
    // 0x128d804: ldur            x0, [fp, #-0x10]
    // 0x128d808: StoreField: r1->field_13 = r0
    //     0x128d808: stur            w0, [x1, #0x13]
    // 0x128d80c: LoadField: r3 = r0->field_b
    //     0x128d80c: ldur            w3, [x0, #0xb]
    // 0x128d810: mov             x2, x1
    // 0x128d814: stur            x3, [fp, #-8]
    // 0x128d818: r1 = Function '<anonymous closure>':.
    //     0x128d818: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c90] AnonymousClosure: (0x128d8e8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x128d7d0)
    //     0x128d81c: ldr             x1, [x1, #0xc90]
    // 0x128d820: r0 = AllocateClosure()
    //     0x128d820: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128d824: stur            x0, [fp, #-0x10]
    // 0x128d828: r0 = ListView()
    //     0x128d828: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x128d82c: stur            x0, [fp, #-0x18]
    // 0x128d830: r16 = true
    //     0x128d830: add             x16, NULL, #0x20  ; true
    // 0x128d834: r30 = Instance_BouncingScrollPhysics
    //     0x128d834: add             lr, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x128d838: ldr             lr, [lr, #0x890]
    // 0x128d83c: stp             lr, x16, [SP]
    // 0x128d840: mov             x1, x0
    // 0x128d844: ldur            x2, [fp, #-0x10]
    // 0x128d848: ldur            x3, [fp, #-8]
    // 0x128d84c: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0x128d84c: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0x128d850: ldr             x4, [x4, #8]
    // 0x128d854: r0 = ListView.builder()
    //     0x128d854: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x128d858: r0 = Card()
    //     0x128d858: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0x128d85c: mov             x1, x0
    // 0x128d860: r0 = Instance_Color
    //     0x128d860: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x128d864: stur            x1, [fp, #-8]
    // 0x128d868: StoreField: r1->field_b = r0
    //     0x128d868: stur            w0, [x1, #0xb]
    // 0x128d86c: r0 = 0.000000
    //     0x128d86c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0x128d870: ArrayStore: r1[0] = r0  ; List_4
    //     0x128d870: stur            w0, [x1, #0x17]
    // 0x128d874: r0 = Instance_RoundedRectangleBorder
    //     0x128d874: add             x0, PP, #0x43, lsl #12  ; [pp+0x438d0] Obj!RoundedRectangleBorder@d5ac11
    //     0x128d878: ldr             x0, [x0, #0x8d0]
    // 0x128d87c: StoreField: r1->field_1b = r0
    //     0x128d87c: stur            w0, [x1, #0x1b]
    // 0x128d880: r0 = true
    //     0x128d880: add             x0, NULL, #0x20  ; true
    // 0x128d884: StoreField: r1->field_1f = r0
    //     0x128d884: stur            w0, [x1, #0x1f]
    // 0x128d888: r2 = Instance_EdgeInsets
    //     0x128d888: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x128d88c: StoreField: r1->field_27 = r2
    //     0x128d88c: stur            w2, [x1, #0x27]
    // 0x128d890: ldur            x2, [fp, #-0x18]
    // 0x128d894: StoreField: r1->field_2f = r2
    //     0x128d894: stur            w2, [x1, #0x2f]
    // 0x128d898: StoreField: r1->field_2b = r0
    //     0x128d898: stur            w0, [x1, #0x2b]
    // 0x128d89c: r0 = Instance__CardVariant
    //     0x128d89c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0x128d8a0: ldr             x0, [x0, #0xa68]
    // 0x128d8a4: StoreField: r1->field_33 = r0
    //     0x128d8a4: stur            w0, [x1, #0x33]
    // 0x128d8a8: r0 = Padding()
    //     0x128d8a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128d8ac: mov             x1, x0
    // 0x128d8b0: r0 = Instance_EdgeInsets
    //     0x128d8b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x128d8b4: ldr             x0, [x0, #0x1f0]
    // 0x128d8b8: stur            x1, [fp, #-0x10]
    // 0x128d8bc: StoreField: r1->field_f = r0
    //     0x128d8bc: stur            w0, [x1, #0xf]
    // 0x128d8c0: ldur            x0, [fp, #-8]
    // 0x128d8c4: StoreField: r1->field_b = r0
    //     0x128d8c4: stur            w0, [x1, #0xb]
    // 0x128d8c8: r0 = RepaintBoundary()
    //     0x128d8c8: bl              #0xa4360c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0x128d8cc: ldur            x1, [fp, #-0x10]
    // 0x128d8d0: StoreField: r0->field_b = r1
    //     0x128d8d0: stur            w1, [x0, #0xb]
    // 0x128d8d4: LeaveFrame
    //     0x128d8d4: mov             SP, fp
    //     0x128d8d8: ldp             fp, lr, [SP], #0x10
    // 0x128d8dc: ret
    //     0x128d8dc: ret             
    // 0x128d8e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128d8e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128d8e4: b               #0x128d7f0
  }
  [closure] RepaintBoundary <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x128d8e8, size: 0x15c
    // 0x128d8e8: EnterFrame
    //     0x128d8e8: stp             fp, lr, [SP, #-0x10]!
    //     0x128d8ec: mov             fp, SP
    // 0x128d8f0: AllocStack(0x20)
    //     0x128d8f0: sub             SP, SP, #0x20
    // 0x128d8f4: SetupParameters()
    //     0x128d8f4: ldr             x0, [fp, #0x20]
    //     0x128d8f8: ldur            w3, [x0, #0x17]
    //     0x128d8fc: add             x3, x3, HEAP, lsl #32
    //     0x128d900: stur            x3, [fp, #-0x10]
    // 0x128d904: CheckStackOverflow
    //     0x128d904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128d908: cmp             SP, x16
    //     0x128d90c: b.ls            #0x128da3c
    // 0x128d910: LoadField: r1 = r3->field_f
    //     0x128d910: ldur            w1, [x3, #0xf]
    // 0x128d914: DecompressPointer r1
    //     0x128d914: add             x1, x1, HEAP, lsl #32
    // 0x128d918: ldr             x0, [fp, #0x10]
    // 0x128d91c: r4 = LoadInt32Instr(r0)
    //     0x128d91c: sbfx            x4, x0, #1, #0x1f
    //     0x128d920: tbz             w0, #0, #0x128d928
    //     0x128d924: ldur            x4, [x0, #7]
    // 0x128d928: mov             x2, x4
    // 0x128d92c: stur            x4, [fp, #-8]
    // 0x128d930: r0 = _buildItemCard()
    //     0x128d930: bl              #0x128da44  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemCard
    // 0x128d934: r1 = Null
    //     0x128d934: mov             x1, NULL
    // 0x128d938: r2 = 2
    //     0x128d938: movz            x2, #0x2
    // 0x128d93c: stur            x0, [fp, #-0x18]
    // 0x128d940: r0 = AllocateArray()
    //     0x128d940: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128d944: mov             x2, x0
    // 0x128d948: ldur            x0, [fp, #-0x18]
    // 0x128d94c: stur            x2, [fp, #-0x20]
    // 0x128d950: StoreField: r2->field_f = r0
    //     0x128d950: stur            w0, [x2, #0xf]
    // 0x128d954: r1 = <Widget>
    //     0x128d954: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128d958: r0 = AllocateGrowableArray()
    //     0x128d958: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128d95c: mov             x2, x0
    // 0x128d960: ldur            x0, [fp, #-0x20]
    // 0x128d964: stur            x2, [fp, #-0x18]
    // 0x128d968: StoreField: r2->field_f = r0
    //     0x128d968: stur            w0, [x2, #0xf]
    // 0x128d96c: r0 = 2
    //     0x128d96c: movz            x0, #0x2
    // 0x128d970: StoreField: r2->field_b = r0
    //     0x128d970: stur            w0, [x2, #0xb]
    // 0x128d974: ldur            x0, [fp, #-0x10]
    // 0x128d978: LoadField: r1 = r0->field_13
    //     0x128d978: ldur            w1, [x0, #0x13]
    // 0x128d97c: DecompressPointer r1
    //     0x128d97c: add             x1, x1, HEAP, lsl #32
    // 0x128d980: LoadField: r0 = r1->field_b
    //     0x128d980: ldur            w0, [x1, #0xb]
    // 0x128d984: r1 = LoadInt32Instr(r0)
    //     0x128d984: sbfx            x1, x0, #1, #0x1f
    // 0x128d988: sub             x0, x1, #1
    // 0x128d98c: ldur            x1, [fp, #-8]
    // 0x128d990: cmp             x1, x0
    // 0x128d994: b.ge            #0x128d9c4
    // 0x128d998: mov             x1, x2
    // 0x128d99c: r0 = _growToNextCapacity()
    //     0x128d99c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128d9a0: ldur            x0, [fp, #-0x18]
    // 0x128d9a4: r1 = 4
    //     0x128d9a4: movz            x1, #0x4
    // 0x128d9a8: StoreField: r0->field_b = r1
    //     0x128d9a8: stur            w1, [x0, #0xb]
    // 0x128d9ac: LoadField: r1 = r0->field_f
    //     0x128d9ac: ldur            w1, [x0, #0xf]
    // 0x128d9b0: DecompressPointer r1
    //     0x128d9b0: add             x1, x1, HEAP, lsl #32
    // 0x128d9b4: r16 = Instance_Padding
    //     0x128d9b4: add             x16, PP, #0x48, lsl #12  ; [pp+0x48c98] Obj!Padding@d68501
    //     0x128d9b8: ldr             x16, [x16, #0xc98]
    // 0x128d9bc: StoreField: r1->field_13 = r16
    //     0x128d9bc: stur            w16, [x1, #0x13]
    // 0x128d9c0: b               #0x128d9c8
    // 0x128d9c4: mov             x0, x2
    // 0x128d9c8: r0 = Column()
    //     0x128d9c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x128d9cc: mov             x1, x0
    // 0x128d9d0: r0 = Instance_Axis
    //     0x128d9d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x128d9d4: stur            x1, [fp, #-0x10]
    // 0x128d9d8: StoreField: r1->field_f = r0
    //     0x128d9d8: stur            w0, [x1, #0xf]
    // 0x128d9dc: r0 = Instance_MainAxisAlignment
    //     0x128d9dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128d9e0: ldr             x0, [x0, #0xa08]
    // 0x128d9e4: StoreField: r1->field_13 = r0
    //     0x128d9e4: stur            w0, [x1, #0x13]
    // 0x128d9e8: r0 = Instance_MainAxisSize
    //     0x128d9e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128d9ec: ldr             x0, [x0, #0xa10]
    // 0x128d9f0: ArrayStore: r1[0] = r0  ; List_4
    //     0x128d9f0: stur            w0, [x1, #0x17]
    // 0x128d9f4: r0 = Instance_CrossAxisAlignment
    //     0x128d9f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128d9f8: ldr             x0, [x0, #0xa18]
    // 0x128d9fc: StoreField: r1->field_1b = r0
    //     0x128d9fc: stur            w0, [x1, #0x1b]
    // 0x128da00: r0 = Instance_VerticalDirection
    //     0x128da00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128da04: ldr             x0, [x0, #0xa20]
    // 0x128da08: StoreField: r1->field_23 = r0
    //     0x128da08: stur            w0, [x1, #0x23]
    // 0x128da0c: r0 = Instance_Clip
    //     0x128da0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128da10: ldr             x0, [x0, #0x38]
    // 0x128da14: StoreField: r1->field_2b = r0
    //     0x128da14: stur            w0, [x1, #0x2b]
    // 0x128da18: StoreField: r1->field_2f = rZR
    //     0x128da18: stur            xzr, [x1, #0x2f]
    // 0x128da1c: ldur            x0, [fp, #-0x18]
    // 0x128da20: StoreField: r1->field_b = r0
    //     0x128da20: stur            w0, [x1, #0xb]
    // 0x128da24: r0 = RepaintBoundary()
    //     0x128da24: bl              #0xa4360c  ; AllocateRepaintBoundaryStub -> RepaintBoundary (size=0x10)
    // 0x128da28: ldur            x1, [fp, #-0x10]
    // 0x128da2c: StoreField: r0->field_b = r1
    //     0x128da2c: stur            w1, [x0, #0xb]
    // 0x128da30: LeaveFrame
    //     0x128da30: mov             SP, fp
    //     0x128da34: ldp             fp, lr, [SP], #0x10
    // 0x128da38: ret
    //     0x128da38: ret             
    // 0x128da3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128da3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128da40: b               #0x128d910
  }
  _ _buildItemCard(/* No info */) {
    // ** addr: 0x128da44, size: 0xa94
    // 0x128da44: EnterFrame
    //     0x128da44: stp             fp, lr, [SP, #-0x10]!
    //     0x128da48: mov             fp, SP
    // 0x128da4c: AllocStack(0x80)
    //     0x128da4c: sub             SP, SP, #0x80
    // 0x128da50: SetupParameters(BagBottomSheet this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x128da50: mov             x0, x1
    //     0x128da54: stur            x1, [fp, #-8]
    //     0x128da58: mov             x1, x2
    //     0x128da5c: stur            x2, [fp, #-0x10]
    // 0x128da60: CheckStackOverflow
    //     0x128da60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128da64: cmp             SP, x16
    //     0x128da68: b.ls            #0x128e4cc
    // 0x128da6c: r1 = 2
    //     0x128da6c: movz            x1, #0x2
    // 0x128da70: r0 = AllocateContext()
    //     0x128da70: bl              #0x16f6108  ; AllocateContextStub
    // 0x128da74: mov             x4, x0
    // 0x128da78: ldur            x3, [fp, #-8]
    // 0x128da7c: stur            x4, [fp, #-0x20]
    // 0x128da80: StoreField: r4->field_f = r3
    //     0x128da80: stur            w3, [x4, #0xf]
    // 0x128da84: LoadField: r0 = r3->field_b
    //     0x128da84: ldur            w0, [x3, #0xb]
    // 0x128da88: DecompressPointer r0
    //     0x128da88: add             x0, x0, HEAP, lsl #32
    // 0x128da8c: LoadField: r1 = r0->field_b
    //     0x128da8c: ldur            w1, [x0, #0xb]
    // 0x128da90: DecompressPointer r1
    //     0x128da90: add             x1, x1, HEAP, lsl #32
    // 0x128da94: cmp             w1, NULL
    // 0x128da98: b.ne            #0x128daa4
    // 0x128da9c: r0 = Null
    //     0x128da9c: mov             x0, NULL
    // 0x128daa0: b               #0x128dae0
    // 0x128daa4: ldur            x2, [fp, #-0x10]
    // 0x128daa8: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x128daa8: ldur            w5, [x1, #0x17]
    // 0x128daac: DecompressPointer r5
    //     0x128daac: add             x5, x5, HEAP, lsl #32
    // 0x128dab0: LoadField: r0 = r5->field_b
    //     0x128dab0: ldur            w0, [x5, #0xb]
    // 0x128dab4: r1 = LoadInt32Instr(r0)
    //     0x128dab4: sbfx            x1, x0, #1, #0x1f
    // 0x128dab8: mov             x0, x1
    // 0x128dabc: mov             x1, x2
    // 0x128dac0: cmp             x1, x0
    // 0x128dac4: b.hs            #0x128e4d4
    // 0x128dac8: LoadField: r0 = r5->field_f
    //     0x128dac8: ldur            w0, [x5, #0xf]
    // 0x128dacc: DecompressPointer r0
    //     0x128dacc: add             x0, x0, HEAP, lsl #32
    // 0x128dad0: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x128dad0: add             x16, x0, x2, lsl #2
    //     0x128dad4: ldur            w1, [x16, #0xf]
    // 0x128dad8: DecompressPointer r1
    //     0x128dad8: add             x1, x1, HEAP, lsl #32
    // 0x128dadc: mov             x0, x1
    // 0x128dae0: stur            x0, [fp, #-0x18]
    // 0x128dae4: StoreField: r4->field_13 = r0
    //     0x128dae4: stur            w0, [x4, #0x13]
    // 0x128dae8: cmp             w0, NULL
    // 0x128daec: b.ne            #0x128db00
    // 0x128daf0: r0 = Instance_SizedBox
    //     0x128daf0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x128daf4: LeaveFrame
    //     0x128daf4: mov             SP, fp
    //     0x128daf8: ldp             fp, lr, [SP], #0x10
    // 0x128dafc: ret
    //     0x128dafc: ret             
    // 0x128db00: LoadField: r1 = r0->field_1b
    //     0x128db00: ldur            w1, [x0, #0x1b]
    // 0x128db04: DecompressPointer r1
    //     0x128db04: add             x1, x1, HEAP, lsl #32
    // 0x128db08: cmp             w1, NULL
    // 0x128db0c: b.ne            #0x128db28
    // 0x128db10: r1 = <BagImage>
    //     0x128db10: add             x1, PP, #0x25, lsl #12  ; [pp+0x25528] TypeArguments: <BagImage>
    //     0x128db14: ldr             x1, [x1, #0x528]
    // 0x128db18: r2 = 0
    //     0x128db18: movz            x2, #0
    // 0x128db1c: r0 = AllocateArray()
    //     0x128db1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128db20: mov             x2, x0
    // 0x128db24: b               #0x128db2c
    // 0x128db28: mov             x2, x1
    // 0x128db2c: stur            x2, [fp, #-0x28]
    // 0x128db30: r0 = LoadClassIdInstr(r2)
    //     0x128db30: ldur            x0, [x2, #-1]
    //     0x128db34: ubfx            x0, x0, #0xc, #0x14
    // 0x128db38: mov             x1, x2
    // 0x128db3c: r0 = GDT[cid_x0 + 0xe517]()
    //     0x128db3c: movz            x17, #0xe517
    //     0x128db40: add             lr, x0, x17
    //     0x128db44: ldr             lr, [x21, lr, lsl #3]
    //     0x128db48: blr             lr
    // 0x128db4c: tbnz            w0, #4, #0x128db90
    // 0x128db50: r1 = Function '<anonymous closure>':.
    //     0x128db50: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ca0] AnonymousClosure: (0x901144), in [package:customer_app/app/presentation/views/line/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemsList (0x901fb4)
    //     0x128db54: ldr             x1, [x1, #0xca0]
    // 0x128db58: r2 = Null
    //     0x128db58: mov             x2, NULL
    // 0x128db5c: r0 = AllocateClosure()
    //     0x128db5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128db60: r1 = Function '<anonymous closure>':.
    //     0x128db60: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ca8] AnonymousClosure: (0x8fb1b8), in [package:customer_app/app/presentation/views/glass/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildProductItem (0x8fb1d0)
    //     0x128db64: ldr             x1, [x1, #0xca8]
    // 0x128db68: r2 = Null
    //     0x128db68: mov             x2, NULL
    // 0x128db6c: stur            x0, [fp, #-0x30]
    // 0x128db70: r0 = AllocateClosure()
    //     0x128db70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128db74: str             x0, [SP]
    // 0x128db78: ldur            x1, [fp, #-0x28]
    // 0x128db7c: ldur            x2, [fp, #-0x30]
    // 0x128db80: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x128db80: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x128db84: ldr             x4, [x4, #0xb48]
    // 0x128db88: r0 = firstWhere()
    //     0x128db88: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x128db8c: b               #0x128db94
    // 0x128db90: r0 = Null
    //     0x128db90: mov             x0, NULL
    // 0x128db94: cmp             w0, NULL
    // 0x128db98: b.ne            #0x128dba4
    // 0x128db9c: r0 = Null
    //     0x128db9c: mov             x0, NULL
    // 0x128dba0: b               #0x128dbb0
    // 0x128dba4: LoadField: r1 = r0->field_b
    //     0x128dba4: ldur            w1, [x0, #0xb]
    // 0x128dba8: DecompressPointer r1
    //     0x128dba8: add             x1, x1, HEAP, lsl #32
    // 0x128dbac: mov             x0, x1
    // 0x128dbb0: cmp             w0, NULL
    // 0x128dbb4: b.ne            #0x128dbc0
    // 0x128dbb8: r2 = ""
    //     0x128dbb8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128dbbc: b               #0x128dbc4
    // 0x128dbc0: mov             x2, x0
    // 0x128dbc4: ldur            x0, [fp, #-0x18]
    // 0x128dbc8: stur            x2, [fp, #-0x30]
    // 0x128dbcc: LoadField: r1 = r0->field_6b
    //     0x128dbcc: ldur            w1, [x0, #0x6b]
    // 0x128dbd0: DecompressPointer r1
    //     0x128dbd0: add             x1, x1, HEAP, lsl #32
    // 0x128dbd4: cmp             w1, NULL
    // 0x128dbd8: b.ne            #0x128dbe4
    // 0x128dbdc: r1 = Null
    //     0x128dbdc: mov             x1, NULL
    // 0x128dbe0: b               #0x128dbf8
    // 0x128dbe4: LoadField: r3 = r1->field_b
    //     0x128dbe4: ldur            w3, [x1, #0xb]
    // 0x128dbe8: cbnz            w3, #0x128dbf4
    // 0x128dbec: r1 = false
    //     0x128dbec: add             x1, NULL, #0x30  ; false
    // 0x128dbf0: b               #0x128dbf8
    // 0x128dbf4: r1 = true
    //     0x128dbf4: add             x1, NULL, #0x20  ; true
    // 0x128dbf8: cmp             w1, NULL
    // 0x128dbfc: b.ne            #0x128dc08
    // 0x128dc00: r3 = false
    //     0x128dc00: add             x3, NULL, #0x30  ; false
    // 0x128dc04: b               #0x128dc0c
    // 0x128dc08: mov             x3, x1
    // 0x128dc0c: ldur            x1, [fp, #-8]
    // 0x128dc10: stur            x3, [fp, #-0x28]
    // 0x128dc14: r0 = Radius()
    //     0x128dc14: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x128dc18: d0 = 12.000000
    //     0x128dc18: fmov            d0, #12.00000000
    // 0x128dc1c: stur            x0, [fp, #-0x38]
    // 0x128dc20: StoreField: r0->field_7 = d0
    //     0x128dc20: stur            d0, [x0, #7]
    // 0x128dc24: StoreField: r0->field_f = d0
    //     0x128dc24: stur            d0, [x0, #0xf]
    // 0x128dc28: r0 = BorderRadius()
    //     0x128dc28: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x128dc2c: mov             x1, x0
    // 0x128dc30: ldur            x0, [fp, #-0x38]
    // 0x128dc34: stur            x1, [fp, #-0x40]
    // 0x128dc38: StoreField: r1->field_7 = r0
    //     0x128dc38: stur            w0, [x1, #7]
    // 0x128dc3c: StoreField: r1->field_b = r0
    //     0x128dc3c: stur            w0, [x1, #0xb]
    // 0x128dc40: StoreField: r1->field_f = r0
    //     0x128dc40: stur            w0, [x1, #0xf]
    // 0x128dc44: StoreField: r1->field_13 = r0
    //     0x128dc44: stur            w0, [x1, #0x13]
    // 0x128dc48: r0 = CachedNetworkImage()
    //     0x128dc48: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x128dc4c: r1 = Function '<anonymous closure>':.
    //     0x128dc4c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48cb0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x128dc50: ldr             x1, [x1, #0xcb0]
    // 0x128dc54: r2 = Null
    //     0x128dc54: mov             x2, NULL
    // 0x128dc58: stur            x0, [fp, #-0x38]
    // 0x128dc5c: r0 = AllocateClosure()
    //     0x128dc5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128dc60: r1 = Function '<anonymous closure>':.
    //     0x128dc60: add             x1, PP, #0x48, lsl #12  ; [pp+0x48cb8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x128dc64: ldr             x1, [x1, #0xcb8]
    // 0x128dc68: r2 = Null
    //     0x128dc68: mov             x2, NULL
    // 0x128dc6c: stur            x0, [fp, #-0x48]
    // 0x128dc70: r0 = AllocateClosure()
    //     0x128dc70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128dc74: r16 = 56.000000
    //     0x128dc74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x128dc78: ldr             x16, [x16, #0xb78]
    // 0x128dc7c: r30 = 56.000000
    //     0x128dc7c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x128dc80: ldr             lr, [lr, #0xb78]
    // 0x128dc84: stp             lr, x16, [SP, #0x20]
    // 0x128dc88: r16 = Instance_BoxFit
    //     0x128dc88: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x128dc8c: ldr             x16, [x16, #0x118]
    // 0x128dc90: r30 = 320
    //     0x128dc90: movz            lr, #0x140
    // 0x128dc94: stp             lr, x16, [SP, #0x10]
    // 0x128dc98: ldur            x16, [fp, #-0x48]
    // 0x128dc9c: stp             x0, x16, [SP]
    // 0x128dca0: ldur            x1, [fp, #-0x38]
    // 0x128dca4: ldur            x2, [fp, #-0x30]
    // 0x128dca8: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x4, height, 0x2, memCacheWidth, 0x5, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0x128dca8: add             x4, PP, #0x48, lsl #12  ; [pp+0x48cc0] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x4, "height", 0x2, "memCacheWidth", 0x5, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0x128dcac: ldr             x4, [x4, #0xcc0]
    // 0x128dcb0: r0 = CachedNetworkImage()
    //     0x128dcb0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x128dcb4: r0 = ClipRRect()
    //     0x128dcb4: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x128dcb8: mov             x1, x0
    // 0x128dcbc: ldur            x0, [fp, #-0x40]
    // 0x128dcc0: stur            x1, [fp, #-0x48]
    // 0x128dcc4: StoreField: r1->field_f = r0
    //     0x128dcc4: stur            w0, [x1, #0xf]
    // 0x128dcc8: r0 = Instance_Clip
    //     0x128dcc8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x128dccc: ldr             x0, [x0, #0x138]
    // 0x128dcd0: ArrayStore: r1[0] = r0  ; List_4
    //     0x128dcd0: stur            w0, [x1, #0x17]
    // 0x128dcd4: ldur            x0, [fp, #-0x38]
    // 0x128dcd8: StoreField: r1->field_b = r0
    //     0x128dcd8: stur            w0, [x1, #0xb]
    // 0x128dcdc: ldur            x0, [fp, #-8]
    // 0x128dce0: LoadField: r2 = r0->field_27
    //     0x128dce0: ldur            w2, [x0, #0x27]
    // 0x128dce4: DecompressPointer r2
    //     0x128dce4: add             x2, x2, HEAP, lsl #32
    // 0x128dce8: stur            x2, [fp, #-0x30]
    // 0x128dcec: r0 = Icon()
    //     0x128dcec: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x128dcf0: mov             x1, x0
    // 0x128dcf4: r0 = Instance_IconData
    //     0x128dcf4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f090] Obj!IconData@d55181
    //     0x128dcf8: ldr             x0, [x0, #0x90]
    // 0x128dcfc: stur            x1, [fp, #-0x38]
    // 0x128dd00: StoreField: r1->field_b = r0
    //     0x128dd00: stur            w0, [x1, #0xb]
    // 0x128dd04: r0 = 20.000000
    //     0x128dd04: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x128dd08: ldr             x0, [x0, #0xac8]
    // 0x128dd0c: StoreField: r1->field_f = r0
    //     0x128dd0c: stur            w0, [x1, #0xf]
    // 0x128dd10: ldur            x0, [fp, #-0x30]
    // 0x128dd14: StoreField: r1->field_23 = r0
    //     0x128dd14: stur            w0, [x1, #0x23]
    // 0x128dd18: r0 = Container()
    //     0x128dd18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128dd1c: stur            x0, [fp, #-0x30]
    // 0x128dd20: r16 = 24.000000
    //     0x128dd20: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x128dd24: ldr             x16, [x16, #0xba8]
    // 0x128dd28: r30 = 24.000000
    //     0x128dd28: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x128dd2c: ldr             lr, [lr, #0xba8]
    // 0x128dd30: stp             lr, x16, [SP, #0x10]
    // 0x128dd34: r16 = Instance_BoxDecoration
    //     0x128dd34: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0x128dd38: ldr             x16, [x16, #0x5a8]
    // 0x128dd3c: ldur            lr, [fp, #-0x38]
    // 0x128dd40: stp             lr, x16, [SP]
    // 0x128dd44: mov             x1, x0
    // 0x128dd48: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x128dd48: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x128dd4c: ldr             x4, [x4, #0x870]
    // 0x128dd50: r0 = Container()
    //     0x128dd50: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128dd54: r0 = InkWell()
    //     0x128dd54: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x128dd58: mov             x3, x0
    // 0x128dd5c: ldur            x0, [fp, #-0x30]
    // 0x128dd60: stur            x3, [fp, #-0x38]
    // 0x128dd64: StoreField: r3->field_b = r0
    //     0x128dd64: stur            w0, [x3, #0xb]
    // 0x128dd68: ldur            x2, [fp, #-0x20]
    // 0x128dd6c: r1 = Function '<anonymous closure>':.
    //     0x128dd6c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48cc8] AnonymousClosure: (0x128e4d8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_buildItemCard (0x128da44)
    //     0x128dd70: ldr             x1, [x1, #0xcc8]
    // 0x128dd74: r0 = AllocateClosure()
    //     0x128dd74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128dd78: mov             x1, x0
    // 0x128dd7c: ldur            x0, [fp, #-0x38]
    // 0x128dd80: StoreField: r0->field_f = r1
    //     0x128dd80: stur            w1, [x0, #0xf]
    // 0x128dd84: r3 = true
    //     0x128dd84: add             x3, NULL, #0x20  ; true
    // 0x128dd88: StoreField: r0->field_43 = r3
    //     0x128dd88: stur            w3, [x0, #0x43]
    // 0x128dd8c: r4 = Instance_BoxShape
    //     0x128dd8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128dd90: ldr             x4, [x4, #0x80]
    // 0x128dd94: StoreField: r0->field_47 = r4
    //     0x128dd94: stur            w4, [x0, #0x47]
    // 0x128dd98: StoreField: r0->field_6f = r3
    //     0x128dd98: stur            w3, [x0, #0x6f]
    // 0x128dd9c: r1 = false
    //     0x128dd9c: add             x1, NULL, #0x30  ; false
    // 0x128dda0: StoreField: r0->field_73 = r1
    //     0x128dda0: stur            w1, [x0, #0x73]
    // 0x128dda4: StoreField: r0->field_83 = r3
    //     0x128dda4: stur            w3, [x0, #0x83]
    // 0x128dda8: StoreField: r0->field_7b = r1
    //     0x128dda8: stur            w1, [x0, #0x7b]
    // 0x128ddac: r1 = Null
    //     0x128ddac: mov             x1, NULL
    // 0x128ddb0: r2 = 4
    //     0x128ddb0: movz            x2, #0x4
    // 0x128ddb4: r0 = AllocateArray()
    //     0x128ddb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128ddb8: mov             x2, x0
    // 0x128ddbc: ldur            x0, [fp, #-0x48]
    // 0x128ddc0: stur            x2, [fp, #-0x20]
    // 0x128ddc4: StoreField: r2->field_f = r0
    //     0x128ddc4: stur            w0, [x2, #0xf]
    // 0x128ddc8: ldur            x0, [fp, #-0x38]
    // 0x128ddcc: StoreField: r2->field_13 = r0
    //     0x128ddcc: stur            w0, [x2, #0x13]
    // 0x128ddd0: r1 = <Widget>
    //     0x128ddd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128ddd4: r0 = AllocateGrowableArray()
    //     0x128ddd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128ddd8: mov             x1, x0
    // 0x128dddc: ldur            x0, [fp, #-0x20]
    // 0x128dde0: stur            x1, [fp, #-0x30]
    // 0x128dde4: StoreField: r1->field_f = r0
    //     0x128dde4: stur            w0, [x1, #0xf]
    // 0x128dde8: r2 = 4
    //     0x128dde8: movz            x2, #0x4
    // 0x128ddec: StoreField: r1->field_b = r2
    //     0x128ddec: stur            w2, [x1, #0xb]
    // 0x128ddf0: r0 = Stack()
    //     0x128ddf0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x128ddf4: mov             x2, x0
    // 0x128ddf8: r0 = Instance_Alignment
    //     0x128ddf8: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0x128ddfc: ldr             x0, [x0, #0x5b8]
    // 0x128de00: stur            x2, [fp, #-0x20]
    // 0x128de04: StoreField: r2->field_f = r0
    //     0x128de04: stur            w0, [x2, #0xf]
    // 0x128de08: r0 = Instance_StackFit
    //     0x128de08: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x128de0c: ldr             x0, [x0, #0xfa8]
    // 0x128de10: ArrayStore: r2[0] = r0  ; List_4
    //     0x128de10: stur            w0, [x2, #0x17]
    // 0x128de14: r0 = Instance_Clip
    //     0x128de14: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x128de18: ldr             x0, [x0, #0x7e0]
    // 0x128de1c: StoreField: r2->field_1b = r0
    //     0x128de1c: stur            w0, [x2, #0x1b]
    // 0x128de20: ldur            x0, [fp, #-0x30]
    // 0x128de24: StoreField: r2->field_b = r0
    //     0x128de24: stur            w0, [x2, #0xb]
    // 0x128de28: ldur            x0, [fp, #-0x18]
    // 0x128de2c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x128de2c: ldur            w1, [x0, #0x17]
    // 0x128de30: DecompressPointer r1
    //     0x128de30: add             x1, x1, HEAP, lsl #32
    // 0x128de34: cmp             w1, NULL
    // 0x128de38: b.ne            #0x128de44
    // 0x128de3c: r1 = Null
    //     0x128de3c: mov             x1, NULL
    // 0x128de40: b               #0x128de50
    // 0x128de44: LoadField: r3 = r1->field_7
    //     0x128de44: ldur            w3, [x1, #7]
    // 0x128de48: DecompressPointer r3
    //     0x128de48: add             x3, x3, HEAP, lsl #32
    // 0x128de4c: mov             x1, x3
    // 0x128de50: cmp             w1, NULL
    // 0x128de54: b.ne            #0x128de5c
    // 0x128de58: r1 = ""
    //     0x128de58: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128de5c: ldur            x3, [fp, #-8]
    // 0x128de60: ldur            x4, [fp, #-0x28]
    // 0x128de64: r0 = capitalizeFirstWord()
    //     0x128de64: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x128de68: mov             x1, x0
    // 0x128de6c: ldur            x0, [fp, #-8]
    // 0x128de70: stur            x1, [fp, #-0x38]
    // 0x128de74: LoadField: r2 = r0->field_2f
    //     0x128de74: ldur            w2, [x0, #0x2f]
    // 0x128de78: DecompressPointer r2
    //     0x128de78: add             x2, x2, HEAP, lsl #32
    // 0x128de7c: stur            x2, [fp, #-0x30]
    // 0x128de80: r0 = Text()
    //     0x128de80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128de84: mov             x1, x0
    // 0x128de88: ldur            x0, [fp, #-0x38]
    // 0x128de8c: stur            x1, [fp, #-0x40]
    // 0x128de90: StoreField: r1->field_b = r0
    //     0x128de90: stur            w0, [x1, #0xb]
    // 0x128de94: ldur            x0, [fp, #-0x30]
    // 0x128de98: StoreField: r1->field_13 = r0
    //     0x128de98: stur            w0, [x1, #0x13]
    // 0x128de9c: r0 = Instance_TextOverflow
    //     0x128de9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x128dea0: ldr             x0, [x0, #0xe10]
    // 0x128dea4: StoreField: r1->field_2b = r0
    //     0x128dea4: stur            w0, [x1, #0x2b]
    // 0x128dea8: r2 = 2
    //     0x128dea8: movz            x2, #0x2
    // 0x128deac: StoreField: r1->field_37 = r2
    //     0x128deac: stur            w2, [x1, #0x37]
    // 0x128deb0: r0 = SizedBox()
    //     0x128deb0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x128deb4: mov             x1, x0
    // 0x128deb8: r0 = 200.000000
    //     0x128deb8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0x128debc: ldr             x0, [x0, #0x570]
    // 0x128dec0: stur            x1, [fp, #-0x30]
    // 0x128dec4: StoreField: r1->field_f = r0
    //     0x128dec4: stur            w0, [x1, #0xf]
    // 0x128dec8: ldur            x0, [fp, #-0x40]
    // 0x128decc: StoreField: r1->field_b = r0
    //     0x128decc: stur            w0, [x1, #0xb]
    // 0x128ded0: r0 = Padding()
    //     0x128ded0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128ded4: mov             x3, x0
    // 0x128ded8: r0 = Instance_EdgeInsets
    //     0x128ded8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48928] Obj!EdgeInsets@d56f01
    //     0x128dedc: ldr             x0, [x0, #0x928]
    // 0x128dee0: stur            x3, [fp, #-0x38]
    // 0x128dee4: StoreField: r3->field_f = r0
    //     0x128dee4: stur            w0, [x3, #0xf]
    // 0x128dee8: ldur            x0, [fp, #-0x30]
    // 0x128deec: StoreField: r3->field_b = r0
    //     0x128deec: stur            w0, [x3, #0xb]
    // 0x128def0: r1 = Null
    //     0x128def0: mov             x1, NULL
    // 0x128def4: r2 = 8
    //     0x128def4: movz            x2, #0x8
    // 0x128def8: r0 = AllocateArray()
    //     0x128def8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128defc: r16 = "Size: "
    //     0x128defc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x128df00: ldr             x16, [x16, #0xf00]
    // 0x128df04: StoreField: r0->field_f = r16
    //     0x128df04: stur            w16, [x0, #0xf]
    // 0x128df08: ldur            x1, [fp, #-0x18]
    // 0x128df0c: LoadField: r2 = r1->field_53
    //     0x128df0c: ldur            w2, [x1, #0x53]
    // 0x128df10: DecompressPointer r2
    //     0x128df10: add             x2, x2, HEAP, lsl #32
    // 0x128df14: StoreField: r0->field_13 = r2
    //     0x128df14: stur            w2, [x0, #0x13]
    // 0x128df18: r16 = " / Qty: "
    //     0x128df18: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x128df1c: ldr             x16, [x16, #0x760]
    // 0x128df20: ArrayStore: r0[0] = r16  ; List_4
    //     0x128df20: stur            w16, [x0, #0x17]
    // 0x128df24: LoadField: r2 = r1->field_5f
    //     0x128df24: ldur            w2, [x1, #0x5f]
    // 0x128df28: DecompressPointer r2
    //     0x128df28: add             x2, x2, HEAP, lsl #32
    // 0x128df2c: StoreField: r0->field_1b = r2
    //     0x128df2c: stur            w2, [x0, #0x1b]
    // 0x128df30: str             x0, [SP]
    // 0x128df34: r0 = _interpolate()
    //     0x128df34: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x128df38: mov             x1, x0
    // 0x128df3c: ldur            x0, [fp, #-8]
    // 0x128df40: stur            x1, [fp, #-0x40]
    // 0x128df44: LoadField: r2 = r0->field_33
    //     0x128df44: ldur            w2, [x0, #0x33]
    // 0x128df48: DecompressPointer r2
    //     0x128df48: add             x2, x2, HEAP, lsl #32
    // 0x128df4c: stur            x2, [fp, #-0x30]
    // 0x128df50: r0 = Text()
    //     0x128df50: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128df54: mov             x1, x0
    // 0x128df58: ldur            x0, [fp, #-0x40]
    // 0x128df5c: stur            x1, [fp, #-0x48]
    // 0x128df60: StoreField: r1->field_b = r0
    //     0x128df60: stur            w0, [x1, #0xb]
    // 0x128df64: ldur            x0, [fp, #-0x30]
    // 0x128df68: StoreField: r1->field_13 = r0
    //     0x128df68: stur            w0, [x1, #0x13]
    // 0x128df6c: r0 = Padding()
    //     0x128df6c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128df70: mov             x3, x0
    // 0x128df74: r0 = Instance_EdgeInsets
    //     0x128df74: add             x0, PP, #0x48, lsl #12  ; [pp+0x48930] Obj!EdgeInsets@d56ed1
    //     0x128df78: ldr             x0, [x0, #0x930]
    // 0x128df7c: stur            x3, [fp, #-0x40]
    // 0x128df80: StoreField: r3->field_f = r0
    //     0x128df80: stur            w0, [x3, #0xf]
    // 0x128df84: ldur            x0, [fp, #-0x48]
    // 0x128df88: StoreField: r3->field_b = r0
    //     0x128df88: stur            w0, [x3, #0xb]
    // 0x128df8c: ldur            x0, [fp, #-0x18]
    // 0x128df90: LoadField: r4 = r0->field_3f
    //     0x128df90: ldur            w4, [x0, #0x3f]
    // 0x128df94: DecompressPointer r4
    //     0x128df94: add             x4, x4, HEAP, lsl #32
    // 0x128df98: stur            x4, [fp, #-0x30]
    // 0x128df9c: r1 = Null
    //     0x128df9c: mov             x1, NULL
    // 0x128dfa0: r2 = 4
    //     0x128dfa0: movz            x2, #0x4
    // 0x128dfa4: r0 = AllocateArray()
    //     0x128dfa4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128dfa8: mov             x1, x0
    // 0x128dfac: ldur            x0, [fp, #-0x30]
    // 0x128dfb0: StoreField: r1->field_f = r0
    //     0x128dfb0: stur            w0, [x1, #0xf]
    // 0x128dfb4: r16 = " "
    //     0x128dfb4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x128dfb8: StoreField: r1->field_13 = r16
    //     0x128dfb8: stur            w16, [x1, #0x13]
    // 0x128dfbc: str             x1, [SP]
    // 0x128dfc0: r0 = _interpolate()
    //     0x128dfc0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x128dfc4: mov             x1, x0
    // 0x128dfc8: ldur            x0, [fp, #-8]
    // 0x128dfcc: stur            x1, [fp, #-0x48]
    // 0x128dfd0: LoadField: r2 = r0->field_37
    //     0x128dfd0: ldur            w2, [x0, #0x37]
    // 0x128dfd4: DecompressPointer r2
    //     0x128dfd4: add             x2, x2, HEAP, lsl #32
    // 0x128dfd8: stur            x2, [fp, #-0x30]
    // 0x128dfdc: r0 = TextSpan()
    //     0x128dfdc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x128dfe0: mov             x1, x0
    // 0x128dfe4: ldur            x0, [fp, #-0x48]
    // 0x128dfe8: stur            x1, [fp, #-0x50]
    // 0x128dfec: StoreField: r1->field_b = r0
    //     0x128dfec: stur            w0, [x1, #0xb]
    // 0x128dff0: r0 = Instance__DeferringMouseCursor
    //     0x128dff0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x128dff4: ArrayStore: r1[0] = r0  ; List_4
    //     0x128dff4: stur            w0, [x1, #0x17]
    // 0x128dff8: ldur            x2, [fp, #-0x30]
    // 0x128dffc: StoreField: r1->field_7 = r2
    //     0x128dffc: stur            w2, [x1, #7]
    // 0x128e000: ldur            x2, [fp, #-0x18]
    // 0x128e004: LoadField: r3 = r2->field_2f
    //     0x128e004: ldur            w3, [x2, #0x2f]
    // 0x128e008: DecompressPointer r3
    //     0x128e008: add             x3, x3, HEAP, lsl #32
    // 0x128e00c: str             x3, [SP]
    // 0x128e010: r0 = _interpolateSingle()
    //     0x128e010: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x128e014: mov             x1, x0
    // 0x128e018: ldur            x0, [fp, #-8]
    // 0x128e01c: stur            x1, [fp, #-0x30]
    // 0x128e020: LoadField: r2 = r0->field_3b
    //     0x128e020: ldur            w2, [x0, #0x3b]
    // 0x128e024: DecompressPointer r2
    //     0x128e024: add             x2, x2, HEAP, lsl #32
    // 0x128e028: stur            x2, [fp, #-0x18]
    // 0x128e02c: r0 = TextSpan()
    //     0x128e02c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x128e030: mov             x3, x0
    // 0x128e034: ldur            x0, [fp, #-0x30]
    // 0x128e038: stur            x3, [fp, #-0x48]
    // 0x128e03c: StoreField: r3->field_b = r0
    //     0x128e03c: stur            w0, [x3, #0xb]
    // 0x128e040: r0 = Instance__DeferringMouseCursor
    //     0x128e040: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x128e044: ArrayStore: r3[0] = r0  ; List_4
    //     0x128e044: stur            w0, [x3, #0x17]
    // 0x128e048: ldur            x1, [fp, #-0x18]
    // 0x128e04c: StoreField: r3->field_7 = r1
    //     0x128e04c: stur            w1, [x3, #7]
    // 0x128e050: r1 = Null
    //     0x128e050: mov             x1, NULL
    // 0x128e054: r2 = 4
    //     0x128e054: movz            x2, #0x4
    // 0x128e058: r0 = AllocateArray()
    //     0x128e058: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128e05c: mov             x2, x0
    // 0x128e060: ldur            x0, [fp, #-0x50]
    // 0x128e064: stur            x2, [fp, #-0x18]
    // 0x128e068: StoreField: r2->field_f = r0
    //     0x128e068: stur            w0, [x2, #0xf]
    // 0x128e06c: ldur            x0, [fp, #-0x48]
    // 0x128e070: StoreField: r2->field_13 = r0
    //     0x128e070: stur            w0, [x2, #0x13]
    // 0x128e074: r1 = <TextSpan>
    //     0x128e074: add             x1, PP, #0x48, lsl #12  ; [pp+0x48940] TypeArguments: <TextSpan>
    //     0x128e078: ldr             x1, [x1, #0x940]
    // 0x128e07c: r0 = AllocateGrowableArray()
    //     0x128e07c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128e080: mov             x1, x0
    // 0x128e084: ldur            x0, [fp, #-0x18]
    // 0x128e088: stur            x1, [fp, #-0x30]
    // 0x128e08c: StoreField: r1->field_f = r0
    //     0x128e08c: stur            w0, [x1, #0xf]
    // 0x128e090: r2 = 4
    //     0x128e090: movz            x2, #0x4
    // 0x128e094: StoreField: r1->field_b = r2
    //     0x128e094: stur            w2, [x1, #0xb]
    // 0x128e098: r0 = TextSpan()
    //     0x128e098: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x128e09c: mov             x1, x0
    // 0x128e0a0: ldur            x0, [fp, #-0x30]
    // 0x128e0a4: stur            x1, [fp, #-0x18]
    // 0x128e0a8: StoreField: r1->field_f = r0
    //     0x128e0a8: stur            w0, [x1, #0xf]
    // 0x128e0ac: r0 = Instance__DeferringMouseCursor
    //     0x128e0ac: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x128e0b0: ArrayStore: r1[0] = r0  ; List_4
    //     0x128e0b0: stur            w0, [x1, #0x17]
    // 0x128e0b4: r0 = RichText()
    //     0x128e0b4: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x128e0b8: mov             x1, x0
    // 0x128e0bc: ldur            x2, [fp, #-0x18]
    // 0x128e0c0: stur            x0, [fp, #-0x18]
    // 0x128e0c4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x128e0c4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x128e0c8: r0 = RichText()
    //     0x128e0c8: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x128e0cc: r0 = Padding()
    //     0x128e0cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128e0d0: mov             x3, x0
    // 0x128e0d4: r0 = Instance_EdgeInsets
    //     0x128e0d4: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!EdgeInsets@d57051
    //     0x128e0d8: ldr             x0, [x0, #0xe0]
    // 0x128e0dc: stur            x3, [fp, #-0x30]
    // 0x128e0e0: StoreField: r3->field_f = r0
    //     0x128e0e0: stur            w0, [x3, #0xf]
    // 0x128e0e4: ldur            x0, [fp, #-0x18]
    // 0x128e0e8: StoreField: r3->field_b = r0
    //     0x128e0e8: stur            w0, [x3, #0xb]
    // 0x128e0ec: r1 = Null
    //     0x128e0ec: mov             x1, NULL
    // 0x128e0f0: r2 = 2
    //     0x128e0f0: movz            x2, #0x2
    // 0x128e0f4: r0 = AllocateArray()
    //     0x128e0f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128e0f8: mov             x2, x0
    // 0x128e0fc: ldur            x0, [fp, #-0x30]
    // 0x128e100: stur            x2, [fp, #-0x18]
    // 0x128e104: StoreField: r2->field_f = r0
    //     0x128e104: stur            w0, [x2, #0xf]
    // 0x128e108: r1 = <Widget>
    //     0x128e108: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128e10c: r0 = AllocateGrowableArray()
    //     0x128e10c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128e110: mov             x1, x0
    // 0x128e114: ldur            x0, [fp, #-0x18]
    // 0x128e118: stur            x1, [fp, #-0x30]
    // 0x128e11c: StoreField: r1->field_f = r0
    //     0x128e11c: stur            w0, [x1, #0xf]
    // 0x128e120: r0 = 2
    //     0x128e120: movz            x0, #0x2
    // 0x128e124: StoreField: r1->field_b = r0
    //     0x128e124: stur            w0, [x1, #0xb]
    // 0x128e128: ldur            x0, [fp, #-0x28]
    // 0x128e12c: tbnz            w0, #4, #0x128e2a4
    // 0x128e130: ldur            x0, [fp, #-8]
    // 0x128e134: LoadField: r2 = r0->field_2b
    //     0x128e134: ldur            w2, [x0, #0x2b]
    // 0x128e138: DecompressPointer r2
    //     0x128e138: add             x2, x2, HEAP, lsl #32
    // 0x128e13c: stur            x2, [fp, #-0x18]
    // 0x128e140: r0 = Radius()
    //     0x128e140: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x128e144: d0 = 20.000000
    //     0x128e144: fmov            d0, #20.00000000
    // 0x128e148: stur            x0, [fp, #-8]
    // 0x128e14c: StoreField: r0->field_7 = d0
    //     0x128e14c: stur            d0, [x0, #7]
    // 0x128e150: StoreField: r0->field_f = d0
    //     0x128e150: stur            d0, [x0, #0xf]
    // 0x128e154: r0 = BorderRadius()
    //     0x128e154: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x128e158: mov             x1, x0
    // 0x128e15c: ldur            x0, [fp, #-8]
    // 0x128e160: stur            x1, [fp, #-0x28]
    // 0x128e164: StoreField: r1->field_7 = r0
    //     0x128e164: stur            w0, [x1, #7]
    // 0x128e168: StoreField: r1->field_b = r0
    //     0x128e168: stur            w0, [x1, #0xb]
    // 0x128e16c: StoreField: r1->field_f = r0
    //     0x128e16c: stur            w0, [x1, #0xf]
    // 0x128e170: StoreField: r1->field_13 = r0
    //     0x128e170: stur            w0, [x1, #0x13]
    // 0x128e174: r0 = BoxDecoration()
    //     0x128e174: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x128e178: mov             x2, x0
    // 0x128e17c: ldur            x0, [fp, #-0x18]
    // 0x128e180: stur            x2, [fp, #-8]
    // 0x128e184: StoreField: r2->field_7 = r0
    //     0x128e184: stur            w0, [x2, #7]
    // 0x128e188: ldur            x0, [fp, #-0x28]
    // 0x128e18c: StoreField: r2->field_13 = r0
    //     0x128e18c: stur            w0, [x2, #0x13]
    // 0x128e190: r0 = Instance_BoxShape
    //     0x128e190: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128e194: ldr             x0, [x0, #0x80]
    // 0x128e198: StoreField: r2->field_23 = r0
    //     0x128e198: stur            w0, [x2, #0x23]
    // 0x128e19c: r1 = Instance_Color
    //     0x128e19c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128e1a0: d0 = 0.700000
    //     0x128e1a0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x128e1a4: ldr             d0, [x17, #0xf48]
    // 0x128e1a8: r0 = withOpacity()
    //     0x128e1a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x128e1ac: stur            x0, [fp, #-0x18]
    // 0x128e1b0: r0 = TextStyle()
    //     0x128e1b0: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0x128e1b4: mov             x1, x0
    // 0x128e1b8: r0 = true
    //     0x128e1b8: add             x0, NULL, #0x20  ; true
    // 0x128e1bc: stur            x1, [fp, #-0x28]
    // 0x128e1c0: StoreField: r1->field_7 = r0
    //     0x128e1c0: stur            w0, [x1, #7]
    // 0x128e1c4: ldur            x0, [fp, #-0x18]
    // 0x128e1c8: StoreField: r1->field_b = r0
    //     0x128e1c8: stur            w0, [x1, #0xb]
    // 0x128e1cc: r0 = 12.000000
    //     0x128e1cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x128e1d0: ldr             x0, [x0, #0x9e8]
    // 0x128e1d4: StoreField: r1->field_1f = r0
    //     0x128e1d4: stur            w0, [x1, #0x1f]
    // 0x128e1d8: r0 = Text()
    //     0x128e1d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128e1dc: mov             x1, x0
    // 0x128e1e0: r0 = "Customised"
    //     0x128e1e0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d88] "Customised"
    //     0x128e1e4: ldr             x0, [x0, #0xd88]
    // 0x128e1e8: stur            x1, [fp, #-0x18]
    // 0x128e1ec: StoreField: r1->field_b = r0
    //     0x128e1ec: stur            w0, [x1, #0xb]
    // 0x128e1f0: ldur            x0, [fp, #-0x28]
    // 0x128e1f4: StoreField: r1->field_13 = r0
    //     0x128e1f4: stur            w0, [x1, #0x13]
    // 0x128e1f8: r0 = Container()
    //     0x128e1f8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128e1fc: stur            x0, [fp, #-0x28]
    // 0x128e200: r16 = Instance_EdgeInsets
    //     0x128e200: add             x16, PP, #0x48, lsl #12  ; [pp+0x48948] Obj!EdgeInsets@d56ea1
    //     0x128e204: ldr             x16, [x16, #0x948]
    // 0x128e208: ldur            lr, [fp, #-8]
    // 0x128e20c: stp             lr, x16, [SP, #8]
    // 0x128e210: ldur            x16, [fp, #-0x18]
    // 0x128e214: str             x16, [SP]
    // 0x128e218: mov             x1, x0
    // 0x128e21c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x128e21c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x128e220: ldr             x4, [x4, #0x610]
    // 0x128e224: r0 = Container()
    //     0x128e224: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128e228: ldur            x0, [fp, #-0x30]
    // 0x128e22c: LoadField: r1 = r0->field_b
    //     0x128e22c: ldur            w1, [x0, #0xb]
    // 0x128e230: LoadField: r2 = r0->field_f
    //     0x128e230: ldur            w2, [x0, #0xf]
    // 0x128e234: DecompressPointer r2
    //     0x128e234: add             x2, x2, HEAP, lsl #32
    // 0x128e238: LoadField: r3 = r2->field_b
    //     0x128e238: ldur            w3, [x2, #0xb]
    // 0x128e23c: r2 = LoadInt32Instr(r1)
    //     0x128e23c: sbfx            x2, x1, #1, #0x1f
    // 0x128e240: stur            x2, [fp, #-0x10]
    // 0x128e244: r1 = LoadInt32Instr(r3)
    //     0x128e244: sbfx            x1, x3, #1, #0x1f
    // 0x128e248: cmp             x2, x1
    // 0x128e24c: b.ne            #0x128e258
    // 0x128e250: mov             x1, x0
    // 0x128e254: r0 = _growToNextCapacity()
    //     0x128e254: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x128e258: ldur            x2, [fp, #-0x30]
    // 0x128e25c: ldur            x3, [fp, #-0x10]
    // 0x128e260: add             x0, x3, #1
    // 0x128e264: lsl             x1, x0, #1
    // 0x128e268: StoreField: r2->field_b = r1
    //     0x128e268: stur            w1, [x2, #0xb]
    // 0x128e26c: LoadField: r1 = r2->field_f
    //     0x128e26c: ldur            w1, [x2, #0xf]
    // 0x128e270: DecompressPointer r1
    //     0x128e270: add             x1, x1, HEAP, lsl #32
    // 0x128e274: ldur            x0, [fp, #-0x28]
    // 0x128e278: ArrayStore: r1[r3] = r0  ; List_4
    //     0x128e278: add             x25, x1, x3, lsl #2
    //     0x128e27c: add             x25, x25, #0xf
    //     0x128e280: str             w0, [x25]
    //     0x128e284: tbz             w0, #0, #0x128e2a0
    //     0x128e288: ldurb           w16, [x1, #-1]
    //     0x128e28c: ldurb           w17, [x0, #-1]
    //     0x128e290: and             x16, x17, x16, lsr #2
    //     0x128e294: tst             x16, HEAP, lsr #32
    //     0x128e298: b.eq            #0x128e2a0
    //     0x128e29c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x128e2a0: b               #0x128e2a8
    // 0x128e2a4: mov             x2, x1
    // 0x128e2a8: ldur            x3, [fp, #-0x20]
    // 0x128e2ac: ldur            x1, [fp, #-0x38]
    // 0x128e2b0: ldur            x0, [fp, #-0x40]
    // 0x128e2b4: r0 = Row()
    //     0x128e2b4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x128e2b8: mov             x1, x0
    // 0x128e2bc: r0 = Instance_Axis
    //     0x128e2bc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128e2c0: stur            x1, [fp, #-8]
    // 0x128e2c4: StoreField: r1->field_f = r0
    //     0x128e2c4: stur            w0, [x1, #0xf]
    // 0x128e2c8: r2 = Instance_MainAxisAlignment
    //     0x128e2c8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128e2cc: ldr             x2, [x2, #0xa08]
    // 0x128e2d0: StoreField: r1->field_13 = r2
    //     0x128e2d0: stur            w2, [x1, #0x13]
    // 0x128e2d4: r3 = Instance_MainAxisSize
    //     0x128e2d4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128e2d8: ldr             x3, [x3, #0xa10]
    // 0x128e2dc: ArrayStore: r1[0] = r3  ; List_4
    //     0x128e2dc: stur            w3, [x1, #0x17]
    // 0x128e2e0: r4 = Instance_CrossAxisAlignment
    //     0x128e2e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128e2e4: ldr             x4, [x4, #0xa18]
    // 0x128e2e8: StoreField: r1->field_1b = r4
    //     0x128e2e8: stur            w4, [x1, #0x1b]
    // 0x128e2ec: r5 = Instance_VerticalDirection
    //     0x128e2ec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128e2f0: ldr             x5, [x5, #0xa20]
    // 0x128e2f4: StoreField: r1->field_23 = r5
    //     0x128e2f4: stur            w5, [x1, #0x23]
    // 0x128e2f8: r6 = Instance_Clip
    //     0x128e2f8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128e2fc: ldr             x6, [x6, #0x38]
    // 0x128e300: StoreField: r1->field_2b = r6
    //     0x128e300: stur            w6, [x1, #0x2b]
    // 0x128e304: StoreField: r1->field_2f = rZR
    //     0x128e304: stur            xzr, [x1, #0x2f]
    // 0x128e308: ldur            x7, [fp, #-0x30]
    // 0x128e30c: StoreField: r1->field_b = r7
    //     0x128e30c: stur            w7, [x1, #0xb]
    // 0x128e310: r0 = Padding()
    //     0x128e310: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128e314: mov             x3, x0
    // 0x128e318: r0 = Instance_EdgeInsets
    //     0x128e318: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0x128e31c: ldr             x0, [x0, #0xc40]
    // 0x128e320: stur            x3, [fp, #-0x18]
    // 0x128e324: StoreField: r3->field_f = r0
    //     0x128e324: stur            w0, [x3, #0xf]
    // 0x128e328: ldur            x0, [fp, #-8]
    // 0x128e32c: StoreField: r3->field_b = r0
    //     0x128e32c: stur            w0, [x3, #0xb]
    // 0x128e330: r1 = Null
    //     0x128e330: mov             x1, NULL
    // 0x128e334: r2 = 6
    //     0x128e334: movz            x2, #0x6
    // 0x128e338: r0 = AllocateArray()
    //     0x128e338: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128e33c: mov             x2, x0
    // 0x128e340: ldur            x0, [fp, #-0x38]
    // 0x128e344: stur            x2, [fp, #-8]
    // 0x128e348: StoreField: r2->field_f = r0
    //     0x128e348: stur            w0, [x2, #0xf]
    // 0x128e34c: ldur            x0, [fp, #-0x40]
    // 0x128e350: StoreField: r2->field_13 = r0
    //     0x128e350: stur            w0, [x2, #0x13]
    // 0x128e354: ldur            x0, [fp, #-0x18]
    // 0x128e358: ArrayStore: r2[0] = r0  ; List_4
    //     0x128e358: stur            w0, [x2, #0x17]
    // 0x128e35c: r1 = <Widget>
    //     0x128e35c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128e360: r0 = AllocateGrowableArray()
    //     0x128e360: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128e364: mov             x1, x0
    // 0x128e368: ldur            x0, [fp, #-8]
    // 0x128e36c: stur            x1, [fp, #-0x18]
    // 0x128e370: StoreField: r1->field_f = r0
    //     0x128e370: stur            w0, [x1, #0xf]
    // 0x128e374: r0 = 6
    //     0x128e374: movz            x0, #0x6
    // 0x128e378: StoreField: r1->field_b = r0
    //     0x128e378: stur            w0, [x1, #0xb]
    // 0x128e37c: r0 = Column()
    //     0x128e37c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x128e380: mov             x2, x0
    // 0x128e384: r0 = Instance_Axis
    //     0x128e384: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x128e388: stur            x2, [fp, #-8]
    // 0x128e38c: StoreField: r2->field_f = r0
    //     0x128e38c: stur            w0, [x2, #0xf]
    // 0x128e390: r0 = Instance_MainAxisAlignment
    //     0x128e390: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128e394: ldr             x0, [x0, #0xa08]
    // 0x128e398: StoreField: r2->field_13 = r0
    //     0x128e398: stur            w0, [x2, #0x13]
    // 0x128e39c: r3 = Instance_MainAxisSize
    //     0x128e39c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128e3a0: ldr             x3, [x3, #0xa10]
    // 0x128e3a4: ArrayStore: r2[0] = r3  ; List_4
    //     0x128e3a4: stur            w3, [x2, #0x17]
    // 0x128e3a8: r1 = Instance_CrossAxisAlignment
    //     0x128e3a8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x128e3ac: ldr             x1, [x1, #0x890]
    // 0x128e3b0: StoreField: r2->field_1b = r1
    //     0x128e3b0: stur            w1, [x2, #0x1b]
    // 0x128e3b4: r4 = Instance_VerticalDirection
    //     0x128e3b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128e3b8: ldr             x4, [x4, #0xa20]
    // 0x128e3bc: StoreField: r2->field_23 = r4
    //     0x128e3bc: stur            w4, [x2, #0x23]
    // 0x128e3c0: r5 = Instance_Clip
    //     0x128e3c0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128e3c4: ldr             x5, [x5, #0x38]
    // 0x128e3c8: StoreField: r2->field_2b = r5
    //     0x128e3c8: stur            w5, [x2, #0x2b]
    // 0x128e3cc: StoreField: r2->field_2f = rZR
    //     0x128e3cc: stur            xzr, [x2, #0x2f]
    // 0x128e3d0: ldur            x1, [fp, #-0x18]
    // 0x128e3d4: StoreField: r2->field_b = r1
    //     0x128e3d4: stur            w1, [x2, #0xb]
    // 0x128e3d8: r1 = <FlexParentData>
    //     0x128e3d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x128e3dc: ldr             x1, [x1, #0xe00]
    // 0x128e3e0: r0 = Expanded()
    //     0x128e3e0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x128e3e4: mov             x3, x0
    // 0x128e3e8: r0 = 1
    //     0x128e3e8: movz            x0, #0x1
    // 0x128e3ec: stur            x3, [fp, #-0x18]
    // 0x128e3f0: StoreField: r3->field_13 = r0
    //     0x128e3f0: stur            x0, [x3, #0x13]
    // 0x128e3f4: r0 = Instance_FlexFit
    //     0x128e3f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x128e3f8: ldr             x0, [x0, #0xe08]
    // 0x128e3fc: StoreField: r3->field_1b = r0
    //     0x128e3fc: stur            w0, [x3, #0x1b]
    // 0x128e400: ldur            x0, [fp, #-8]
    // 0x128e404: StoreField: r3->field_b = r0
    //     0x128e404: stur            w0, [x3, #0xb]
    // 0x128e408: r1 = Null
    //     0x128e408: mov             x1, NULL
    // 0x128e40c: r2 = 4
    //     0x128e40c: movz            x2, #0x4
    // 0x128e410: r0 = AllocateArray()
    //     0x128e410: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128e414: mov             x2, x0
    // 0x128e418: ldur            x0, [fp, #-0x20]
    // 0x128e41c: stur            x2, [fp, #-8]
    // 0x128e420: StoreField: r2->field_f = r0
    //     0x128e420: stur            w0, [x2, #0xf]
    // 0x128e424: ldur            x0, [fp, #-0x18]
    // 0x128e428: StoreField: r2->field_13 = r0
    //     0x128e428: stur            w0, [x2, #0x13]
    // 0x128e42c: r1 = <Widget>
    //     0x128e42c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128e430: r0 = AllocateGrowableArray()
    //     0x128e430: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128e434: mov             x1, x0
    // 0x128e438: ldur            x0, [fp, #-8]
    // 0x128e43c: stur            x1, [fp, #-0x18]
    // 0x128e440: StoreField: r1->field_f = r0
    //     0x128e440: stur            w0, [x1, #0xf]
    // 0x128e444: r0 = 4
    //     0x128e444: movz            x0, #0x4
    // 0x128e448: StoreField: r1->field_b = r0
    //     0x128e448: stur            w0, [x1, #0xb]
    // 0x128e44c: r0 = Row()
    //     0x128e44c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x128e450: mov             x1, x0
    // 0x128e454: r0 = Instance_Axis
    //     0x128e454: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128e458: stur            x1, [fp, #-8]
    // 0x128e45c: StoreField: r1->field_f = r0
    //     0x128e45c: stur            w0, [x1, #0xf]
    // 0x128e460: r0 = Instance_MainAxisAlignment
    //     0x128e460: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128e464: ldr             x0, [x0, #0xa08]
    // 0x128e468: StoreField: r1->field_13 = r0
    //     0x128e468: stur            w0, [x1, #0x13]
    // 0x128e46c: r0 = Instance_MainAxisSize
    //     0x128e46c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128e470: ldr             x0, [x0, #0xa10]
    // 0x128e474: ArrayStore: r1[0] = r0  ; List_4
    //     0x128e474: stur            w0, [x1, #0x17]
    // 0x128e478: r0 = Instance_CrossAxisAlignment
    //     0x128e478: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128e47c: ldr             x0, [x0, #0xa18]
    // 0x128e480: StoreField: r1->field_1b = r0
    //     0x128e480: stur            w0, [x1, #0x1b]
    // 0x128e484: r0 = Instance_VerticalDirection
    //     0x128e484: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128e488: ldr             x0, [x0, #0xa20]
    // 0x128e48c: StoreField: r1->field_23 = r0
    //     0x128e48c: stur            w0, [x1, #0x23]
    // 0x128e490: r0 = Instance_Clip
    //     0x128e490: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128e494: ldr             x0, [x0, #0x38]
    // 0x128e498: StoreField: r1->field_2b = r0
    //     0x128e498: stur            w0, [x1, #0x2b]
    // 0x128e49c: StoreField: r1->field_2f = rZR
    //     0x128e49c: stur            xzr, [x1, #0x2f]
    // 0x128e4a0: ldur            x0, [fp, #-0x18]
    // 0x128e4a4: StoreField: r1->field_b = r0
    //     0x128e4a4: stur            w0, [x1, #0xb]
    // 0x128e4a8: r0 = Padding()
    //     0x128e4a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128e4ac: r1 = Instance_EdgeInsets
    //     0x128e4ac: add             x1, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0x128e4b0: ldr             x1, [x1, #0xf30]
    // 0x128e4b4: StoreField: r0->field_f = r1
    //     0x128e4b4: stur            w1, [x0, #0xf]
    // 0x128e4b8: ldur            x1, [fp, #-8]
    // 0x128e4bc: StoreField: r0->field_b = r1
    //     0x128e4bc: stur            w1, [x0, #0xb]
    // 0x128e4c0: LeaveFrame
    //     0x128e4c0: mov             SP, fp
    //     0x128e4c4: ldp             fp, lr, [SP], #0x10
    // 0x128e4c8: ret
    //     0x128e4c8: ret             
    // 0x128e4cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128e4cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128e4d0: b               #0x128da6c
    // 0x128e4d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x128e4d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x128e4d8, size: 0x50
    // 0x128e4d8: EnterFrame
    //     0x128e4d8: stp             fp, lr, [SP, #-0x10]!
    //     0x128e4dc: mov             fp, SP
    // 0x128e4e0: ldr             x0, [fp, #0x10]
    // 0x128e4e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x128e4e4: ldur            w1, [x0, #0x17]
    // 0x128e4e8: DecompressPointer r1
    //     0x128e4e8: add             x1, x1, HEAP, lsl #32
    // 0x128e4ec: CheckStackOverflow
    //     0x128e4ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128e4f0: cmp             SP, x16
    //     0x128e4f4: b.ls            #0x128e520
    // 0x128e4f8: LoadField: r0 = r1->field_f
    //     0x128e4f8: ldur            w0, [x1, #0xf]
    // 0x128e4fc: DecompressPointer r0
    //     0x128e4fc: add             x0, x0, HEAP, lsl #32
    // 0x128e500: LoadField: r2 = r1->field_13
    //     0x128e500: ldur            w2, [x1, #0x13]
    // 0x128e504: DecompressPointer r2
    //     0x128e504: add             x2, x2, HEAP, lsl #32
    // 0x128e508: mov             x1, x0
    // 0x128e50c: r0 = _removeItem()
    //     0x128e50c: bl              #0x128e528  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/bag_bottom_sheet.dart] BagBottomSheet::_removeItem
    // 0x128e510: r0 = Null
    //     0x128e510: mov             x0, NULL
    // 0x128e514: LeaveFrame
    //     0x128e514: mov             SP, fp
    //     0x128e518: ldp             fp, lr, [SP], #0x10
    // 0x128e51c: ret
    //     0x128e51c: ret             
    // 0x128e520: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128e520: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128e524: b               #0x128e4f8
  }
  _ _removeItem(/* No info */) {
    // ** addr: 0x128e528, size: 0x1c8
    // 0x128e528: EnterFrame
    //     0x128e528: stp             fp, lr, [SP, #-0x10]!
    //     0x128e52c: mov             fp, SP
    // 0x128e530: AllocStack(0x40)
    //     0x128e530: sub             SP, SP, #0x40
    // 0x128e534: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x128e534: stur            x1, [fp, #-8]
    //     0x128e538: stur            x2, [fp, #-0x10]
    // 0x128e53c: CheckStackOverflow
    //     0x128e53c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128e540: cmp             SP, x16
    //     0x128e544: b.ls            #0x128e6e4
    // 0x128e548: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x128e548: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x128e54c: ldr             x0, [x0, #0x1c80]
    //     0x128e550: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x128e554: cmp             w0, w16
    //     0x128e558: b.ne            #0x128e564
    //     0x128e55c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x128e560: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x128e564: str             NULL, [SP]
    // 0x128e568: r4 = const [0x1, 0, 0, 0, null]
    //     0x128e568: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x128e56c: r0 = GetNavigation.back()
    //     0x128e56c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x128e570: ldur            x0, [fp, #-0x10]
    // 0x128e574: cmp             w0, NULL
    // 0x128e578: b.eq            #0x128e6ec
    // 0x128e57c: LoadField: r1 = r0->field_b
    //     0x128e57c: ldur            w1, [x0, #0xb]
    // 0x128e580: DecompressPointer r1
    //     0x128e580: add             x1, x1, HEAP, lsl #32
    // 0x128e584: stur            x1, [fp, #-0x28]
    // 0x128e588: LoadField: r2 = r0->field_1f
    //     0x128e588: ldur            w2, [x0, #0x1f]
    // 0x128e58c: DecompressPointer r2
    //     0x128e58c: add             x2, x2, HEAP, lsl #32
    // 0x128e590: cmp             w2, NULL
    // 0x128e594: b.ne            #0x128e5a0
    // 0x128e598: r2 = Null
    //     0x128e598: mov             x2, NULL
    // 0x128e59c: b               #0x128e5ac
    // 0x128e5a0: LoadField: r3 = r2->field_b
    //     0x128e5a0: ldur            w3, [x2, #0xb]
    // 0x128e5a4: DecompressPointer r3
    //     0x128e5a4: add             x3, x3, HEAP, lsl #32
    // 0x128e5a8: mov             x2, x3
    // 0x128e5ac: stur            x2, [fp, #-0x20]
    // 0x128e5b0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x128e5b0: ldur            w3, [x0, #0x17]
    // 0x128e5b4: DecompressPointer r3
    //     0x128e5b4: add             x3, x3, HEAP, lsl #32
    // 0x128e5b8: cmp             w3, NULL
    // 0x128e5bc: b.ne            #0x128e5c8
    // 0x128e5c0: r4 = Null
    //     0x128e5c0: mov             x4, NULL
    // 0x128e5c4: b               #0x128e5d0
    // 0x128e5c8: LoadField: r4 = r3->field_7
    //     0x128e5c8: ldur            w4, [x3, #7]
    // 0x128e5cc: DecompressPointer r4
    //     0x128e5cc: add             x4, x4, HEAP, lsl #32
    // 0x128e5d0: ldur            x3, [fp, #-8]
    // 0x128e5d4: stur            x4, [fp, #-0x18]
    // 0x128e5d8: r0 = EventData()
    //     0x128e5d8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x128e5dc: mov             x1, x0
    // 0x128e5e0: ldur            x0, [fp, #-0x28]
    // 0x128e5e4: stur            x1, [fp, #-0x30]
    // 0x128e5e8: StoreField: r1->field_33 = r0
    //     0x128e5e8: stur            w0, [x1, #0x33]
    // 0x128e5ec: ldur            x0, [fp, #-0x20]
    // 0x128e5f0: StoreField: r1->field_8f = r0
    //     0x128e5f0: stur            w0, [x1, #0x8f]
    // 0x128e5f4: ldur            x0, [fp, #-0x18]
    // 0x128e5f8: StoreField: r1->field_93 = r0
    //     0x128e5f8: stur            w0, [x1, #0x93]
    // 0x128e5fc: r0 = EventsRequest()
    //     0x128e5fc: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x128e600: mov             x1, x0
    // 0x128e604: r0 = "product_removed"
    //     0x128e604: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7e0] "product_removed"
    //     0x128e608: ldr             x0, [x0, #0x7e0]
    // 0x128e60c: StoreField: r1->field_7 = r0
    //     0x128e60c: stur            w0, [x1, #7]
    // 0x128e610: ldur            x0, [fp, #-0x30]
    // 0x128e614: StoreField: r1->field_b = r0
    //     0x128e614: stur            w0, [x1, #0xb]
    // 0x128e618: ldur            x0, [fp, #-8]
    // 0x128e61c: LoadField: r2 = r0->field_1b
    //     0x128e61c: ldur            w2, [x0, #0x1b]
    // 0x128e620: DecompressPointer r2
    //     0x128e620: add             x2, x2, HEAP, lsl #32
    // 0x128e624: stp             x1, x2, [SP]
    // 0x128e628: r4 = 0
    //     0x128e628: movz            x4, #0
    // 0x128e62c: ldr             x0, [SP, #8]
    // 0x128e630: r16 = UnlinkedCall_0x613b5c
    //     0x128e630: add             x16, PP, #0x48, lsl #12  ; [pp+0x48cd0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x128e634: add             x16, x16, #0xcd0
    // 0x128e638: ldp             x5, lr, [x16]
    // 0x128e63c: blr             lr
    // 0x128e640: ldur            x0, [fp, #-0x10]
    // 0x128e644: LoadField: r1 = r0->field_b
    //     0x128e644: ldur            w1, [x0, #0xb]
    // 0x128e648: DecompressPointer r1
    //     0x128e648: add             x1, x1, HEAP, lsl #32
    // 0x128e64c: stur            x1, [fp, #-0x28]
    // 0x128e650: LoadField: r2 = r0->field_1f
    //     0x128e650: ldur            w2, [x0, #0x1f]
    // 0x128e654: DecompressPointer r2
    //     0x128e654: add             x2, x2, HEAP, lsl #32
    // 0x128e658: cmp             w2, NULL
    // 0x128e65c: b.ne            #0x128e668
    // 0x128e660: r3 = Null
    //     0x128e660: mov             x3, NULL
    // 0x128e664: b               #0x128e670
    // 0x128e668: LoadField: r3 = r2->field_b
    //     0x128e668: ldur            w3, [x2, #0xb]
    // 0x128e66c: DecompressPointer r3
    //     0x128e66c: add             x3, x3, HEAP, lsl #32
    // 0x128e670: ldur            x2, [fp, #-8]
    // 0x128e674: stur            x3, [fp, #-0x20]
    // 0x128e678: LoadField: r4 = r0->field_83
    //     0x128e678: ldur            w4, [x0, #0x83]
    // 0x128e67c: DecompressPointer r4
    //     0x128e67c: add             x4, x4, HEAP, lsl #32
    // 0x128e680: stur            x4, [fp, #-0x18]
    // 0x128e684: r0 = RemoveItemRequest()
    //     0x128e684: bl              #0x8fc238  ; AllocateRemoveItemRequestStub -> RemoveItemRequest (size=0x18)
    // 0x128e688: mov             x1, x0
    // 0x128e68c: ldur            x0, [fp, #-0x28]
    // 0x128e690: StoreField: r1->field_7 = r0
    //     0x128e690: stur            w0, [x1, #7]
    // 0x128e694: ldur            x0, [fp, #-0x20]
    // 0x128e698: StoreField: r1->field_b = r0
    //     0x128e698: stur            w0, [x1, #0xb]
    // 0x128e69c: r0 = true
    //     0x128e69c: add             x0, NULL, #0x20  ; true
    // 0x128e6a0: StoreField: r1->field_f = r0
    //     0x128e6a0: stur            w0, [x1, #0xf]
    // 0x128e6a4: ldur            x0, [fp, #-0x18]
    // 0x128e6a8: StoreField: r1->field_13 = r0
    //     0x128e6a8: stur            w0, [x1, #0x13]
    // 0x128e6ac: ldur            x0, [fp, #-8]
    // 0x128e6b0: LoadField: r2 = r0->field_f
    //     0x128e6b0: ldur            w2, [x0, #0xf]
    // 0x128e6b4: DecompressPointer r2
    //     0x128e6b4: add             x2, x2, HEAP, lsl #32
    // 0x128e6b8: stp             x1, x2, [SP]
    // 0x128e6bc: r4 = 0
    //     0x128e6bc: movz            x4, #0
    // 0x128e6c0: ldr             x0, [SP, #8]
    // 0x128e6c4: r16 = UnlinkedCall_0x613b5c
    //     0x128e6c4: add             x16, PP, #0x48, lsl #12  ; [pp+0x48ce0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x128e6c8: add             x16, x16, #0xce0
    // 0x128e6cc: ldp             x5, lr, [x16]
    // 0x128e6d0: blr             lr
    // 0x128e6d4: r0 = Null
    //     0x128e6d4: mov             x0, NULL
    // 0x128e6d8: LeaveFrame
    //     0x128e6d8: mov             SP, fp
    //     0x128e6dc: ldp             fp, lr, [SP], #0x10
    // 0x128e6e0: ret
    //     0x128e6e0: ret             
    // 0x128e6e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128e6e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128e6e8: b               #0x128e548
    // 0x128e6ec: r0 = NullErrorSharedWithoutFPURegs()
    //     0x128e6ec: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _buildFreeGiftItem(/* No info */) {
    // ** addr: 0x128e6f0, size: 0x6f8
    // 0x128e6f0: EnterFrame
    //     0x128e6f0: stp             fp, lr, [SP, #-0x10]!
    //     0x128e6f4: mov             fp, SP
    // 0x128e6f8: AllocStack(0x78)
    //     0x128e6f8: sub             SP, SP, #0x78
    // 0x128e6fc: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x28 */, dynamic _ /* r2 => r2, fp-0x30 */)
    //     0x128e6fc: stur            x1, [fp, #-0x28]
    //     0x128e700: stur            x2, [fp, #-0x30]
    // 0x128e704: CheckStackOverflow
    //     0x128e704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128e708: cmp             SP, x16
    //     0x128e70c: b.ls            #0x128ede0
    // 0x128e710: tbnz            w3, #4, #0x128e720
    // 0x128e714: r0 = Instance_LinearGradient
    //     0x128e714: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0x128e718: ldr             x0, [x0, #0x660]
    // 0x128e71c: b               #0x128e724
    // 0x128e720: r0 = Null
    //     0x128e720: mov             x0, NULL
    // 0x128e724: stur            x0, [fp, #-0x20]
    // 0x128e728: tbnz            w3, #4, #0x128e744
    // 0x128e72c: mov             x16, x0
    // 0x128e730: mov             x0, x2
    // 0x128e734: mov             x2, x16
    // 0x128e738: r3 = Instance_Color
    //     0x128e738: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x128e73c: ldr             x3, [x3, #0x858]
    // 0x128e740: b               #0x128e8d4
    // 0x128e744: LoadField: r3 = r1->field_1f
    //     0x128e744: ldur            w3, [x1, #0x1f]
    // 0x128e748: DecompressPointer r3
    //     0x128e748: add             x3, x3, HEAP, lsl #32
    // 0x128e74c: LoadField: r4 = r3->field_3f
    //     0x128e74c: ldur            w4, [x3, #0x3f]
    // 0x128e750: DecompressPointer r4
    //     0x128e750: add             x4, x4, HEAP, lsl #32
    // 0x128e754: cmp             w4, NULL
    // 0x128e758: b.ne            #0x128e764
    // 0x128e75c: r3 = Null
    //     0x128e75c: mov             x3, NULL
    // 0x128e760: b               #0x128e788
    // 0x128e764: ArrayLoad: r3 = r4[0]  ; List_4
    //     0x128e764: ldur            w3, [x4, #0x17]
    // 0x128e768: DecompressPointer r3
    //     0x128e768: add             x3, x3, HEAP, lsl #32
    // 0x128e76c: cmp             w3, NULL
    // 0x128e770: b.ne            #0x128e77c
    // 0x128e774: r3 = Null
    //     0x128e774: mov             x3, NULL
    // 0x128e778: b               #0x128e788
    // 0x128e77c: LoadField: r5 = r3->field_7
    //     0x128e77c: ldur            w5, [x3, #7]
    // 0x128e780: DecompressPointer r5
    //     0x128e780: add             x5, x5, HEAP, lsl #32
    // 0x128e784: mov             x3, x5
    // 0x128e788: cmp             w3, NULL
    // 0x128e78c: b.ne            #0x128e798
    // 0x128e790: r3 = 0
    //     0x128e790: movz            x3, #0
    // 0x128e794: b               #0x128e7a8
    // 0x128e798: r5 = LoadInt32Instr(r3)
    //     0x128e798: sbfx            x5, x3, #1, #0x1f
    //     0x128e79c: tbz             w3, #0, #0x128e7a4
    //     0x128e7a0: ldur            x5, [x3, #7]
    // 0x128e7a4: mov             x3, x5
    // 0x128e7a8: stur            x3, [fp, #-0x18]
    // 0x128e7ac: cmp             w4, NULL
    // 0x128e7b0: b.ne            #0x128e7bc
    // 0x128e7b4: r5 = Null
    //     0x128e7b4: mov             x5, NULL
    // 0x128e7b8: b               #0x128e7e0
    // 0x128e7bc: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x128e7bc: ldur            w5, [x4, #0x17]
    // 0x128e7c0: DecompressPointer r5
    //     0x128e7c0: add             x5, x5, HEAP, lsl #32
    // 0x128e7c4: cmp             w5, NULL
    // 0x128e7c8: b.ne            #0x128e7d4
    // 0x128e7cc: r5 = Null
    //     0x128e7cc: mov             x5, NULL
    // 0x128e7d0: b               #0x128e7e0
    // 0x128e7d4: LoadField: r6 = r5->field_b
    //     0x128e7d4: ldur            w6, [x5, #0xb]
    // 0x128e7d8: DecompressPointer r6
    //     0x128e7d8: add             x6, x6, HEAP, lsl #32
    // 0x128e7dc: mov             x5, x6
    // 0x128e7e0: cmp             w5, NULL
    // 0x128e7e4: b.ne            #0x128e7f0
    // 0x128e7e8: r5 = 0
    //     0x128e7e8: movz            x5, #0
    // 0x128e7ec: b               #0x128e800
    // 0x128e7f0: r6 = LoadInt32Instr(r5)
    //     0x128e7f0: sbfx            x6, x5, #1, #0x1f
    //     0x128e7f4: tbz             w5, #0, #0x128e7fc
    //     0x128e7f8: ldur            x6, [x5, #7]
    // 0x128e7fc: mov             x5, x6
    // 0x128e800: stur            x5, [fp, #-0x10]
    // 0x128e804: cmp             w4, NULL
    // 0x128e808: b.ne            #0x128e814
    // 0x128e80c: r4 = Null
    //     0x128e80c: mov             x4, NULL
    // 0x128e810: b               #0x128e834
    // 0x128e814: ArrayLoad: r6 = r4[0]  ; List_4
    //     0x128e814: ldur            w6, [x4, #0x17]
    // 0x128e818: DecompressPointer r6
    //     0x128e818: add             x6, x6, HEAP, lsl #32
    // 0x128e81c: cmp             w6, NULL
    // 0x128e820: b.ne            #0x128e82c
    // 0x128e824: r4 = Null
    //     0x128e824: mov             x4, NULL
    // 0x128e828: b               #0x128e834
    // 0x128e82c: LoadField: r4 = r6->field_f
    //     0x128e82c: ldur            w4, [x6, #0xf]
    // 0x128e830: DecompressPointer r4
    //     0x128e830: add             x4, x4, HEAP, lsl #32
    // 0x128e834: cmp             w4, NULL
    // 0x128e838: b.ne            #0x128e844
    // 0x128e83c: r4 = 0
    //     0x128e83c: movz            x4, #0
    // 0x128e840: b               #0x128e854
    // 0x128e844: r6 = LoadInt32Instr(r4)
    //     0x128e844: sbfx            x6, x4, #1, #0x1f
    //     0x128e848: tbz             w4, #0, #0x128e850
    //     0x128e84c: ldur            x6, [x4, #7]
    // 0x128e850: mov             x4, x6
    // 0x128e854: stur            x4, [fp, #-8]
    // 0x128e858: r0 = Color()
    //     0x128e858: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x128e85c: mov             x1, x0
    // 0x128e860: r0 = Instance_ColorSpace
    //     0x128e860: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x128e864: StoreField: r1->field_27 = r0
    //     0x128e864: stur            w0, [x1, #0x27]
    // 0x128e868: d0 = 0.400000
    //     0x128e868: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x128e86c: StoreField: r1->field_7 = d0
    //     0x128e86c: stur            d0, [x1, #7]
    // 0x128e870: ldur            x0, [fp, #-0x18]
    // 0x128e874: ubfx            x0, x0, #0, #0x20
    // 0x128e878: and             w2, w0, #0xff
    // 0x128e87c: ubfx            x2, x2, #0, #0x20
    // 0x128e880: scvtf           d0, x2
    // 0x128e884: d1 = 255.000000
    //     0x128e884: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x128e888: fdiv            d2, d0, d1
    // 0x128e88c: StoreField: r1->field_f = d2
    //     0x128e88c: stur            d2, [x1, #0xf]
    // 0x128e890: ldur            x0, [fp, #-0x10]
    // 0x128e894: ubfx            x0, x0, #0, #0x20
    // 0x128e898: and             w2, w0, #0xff
    // 0x128e89c: ubfx            x2, x2, #0, #0x20
    // 0x128e8a0: scvtf           d0, x2
    // 0x128e8a4: fdiv            d2, d0, d1
    // 0x128e8a8: ArrayStore: r1[0] = d2  ; List_8
    //     0x128e8a8: stur            d2, [x1, #0x17]
    // 0x128e8ac: ldur            x0, [fp, #-8]
    // 0x128e8b0: ubfx            x0, x0, #0, #0x20
    // 0x128e8b4: and             w2, w0, #0xff
    // 0x128e8b8: ubfx            x2, x2, #0, #0x20
    // 0x128e8bc: scvtf           d0, x2
    // 0x128e8c0: fdiv            d2, d0, d1
    // 0x128e8c4: StoreField: r1->field_1f = d2
    //     0x128e8c4: stur            d2, [x1, #0x1f]
    // 0x128e8c8: mov             x3, x1
    // 0x128e8cc: ldur            x0, [fp, #-0x30]
    // 0x128e8d0: ldur            x2, [fp, #-0x20]
    // 0x128e8d4: stur            x3, [fp, #-0x38]
    // 0x128e8d8: r1 = Instance_Color
    //     0x128e8d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128e8dc: d0 = 0.070000
    //     0x128e8dc: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0x128e8e0: ldr             d0, [x17, #0x5f8]
    // 0x128e8e4: r0 = withOpacity()
    //     0x128e8e4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x128e8e8: r16 = 1.000000
    //     0x128e8e8: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x128e8ec: str             x16, [SP]
    // 0x128e8f0: mov             x2, x0
    // 0x128e8f4: r1 = Null
    //     0x128e8f4: mov             x1, NULL
    // 0x128e8f8: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x128e8f8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x128e8fc: ldr             x4, [x4, #0x108]
    // 0x128e900: r0 = Border.all()
    //     0x128e900: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x128e904: stur            x0, [fp, #-0x40]
    // 0x128e908: r0 = Radius()
    //     0x128e908: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x128e90c: d0 = 12.000000
    //     0x128e90c: fmov            d0, #12.00000000
    // 0x128e910: stur            x0, [fp, #-0x48]
    // 0x128e914: StoreField: r0->field_7 = d0
    //     0x128e914: stur            d0, [x0, #7]
    // 0x128e918: StoreField: r0->field_f = d0
    //     0x128e918: stur            d0, [x0, #0xf]
    // 0x128e91c: r0 = BorderRadius()
    //     0x128e91c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x128e920: mov             x1, x0
    // 0x128e924: ldur            x0, [fp, #-0x48]
    // 0x128e928: stur            x1, [fp, #-0x50]
    // 0x128e92c: StoreField: r1->field_7 = r0
    //     0x128e92c: stur            w0, [x1, #7]
    // 0x128e930: StoreField: r1->field_b = r0
    //     0x128e930: stur            w0, [x1, #0xb]
    // 0x128e934: StoreField: r1->field_f = r0
    //     0x128e934: stur            w0, [x1, #0xf]
    // 0x128e938: StoreField: r1->field_13 = r0
    //     0x128e938: stur            w0, [x1, #0x13]
    // 0x128e93c: r0 = BoxDecoration()
    //     0x128e93c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x128e940: mov             x1, x0
    // 0x128e944: ldur            x0, [fp, #-0x40]
    // 0x128e948: stur            x1, [fp, #-0x48]
    // 0x128e94c: StoreField: r1->field_f = r0
    //     0x128e94c: stur            w0, [x1, #0xf]
    // 0x128e950: ldur            x0, [fp, #-0x50]
    // 0x128e954: StoreField: r1->field_13 = r0
    //     0x128e954: stur            w0, [x1, #0x13]
    // 0x128e958: ldur            x0, [fp, #-0x20]
    // 0x128e95c: StoreField: r1->field_1b = r0
    //     0x128e95c: stur            w0, [x1, #0x1b]
    // 0x128e960: r0 = Instance_BoxShape
    //     0x128e960: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128e964: ldr             x0, [x0, #0x80]
    // 0x128e968: StoreField: r1->field_23 = r0
    //     0x128e968: stur            w0, [x1, #0x23]
    // 0x128e96c: r0 = BoxDecoration()
    //     0x128e96c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x128e970: mov             x1, x0
    // 0x128e974: ldur            x0, [fp, #-0x38]
    // 0x128e978: stur            x1, [fp, #-0x20]
    // 0x128e97c: StoreField: r1->field_7 = r0
    //     0x128e97c: stur            w0, [x1, #7]
    // 0x128e980: r0 = Instance_BorderRadius
    //     0x128e980: add             x0, PP, #0x48, lsl #12  ; [pp+0x48cf0] Obj!BorderRadius@d5a481
    //     0x128e984: ldr             x0, [x0, #0xcf0]
    // 0x128e988: StoreField: r1->field_13 = r0
    //     0x128e988: stur            w0, [x1, #0x13]
    // 0x128e98c: r0 = Instance_BoxShape
    //     0x128e98c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128e990: ldr             x0, [x0, #0x80]
    // 0x128e994: StoreField: r1->field_23 = r0
    //     0x128e994: stur            w0, [x1, #0x23]
    // 0x128e998: r0 = Container()
    //     0x128e998: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128e99c: stur            x0, [fp, #-0x38]
    // 0x128e9a0: ldur            x16, [fp, #-0x20]
    // 0x128e9a4: r30 = 24.000000
    //     0x128e9a4: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x128e9a8: ldr             lr, [lr, #0xba8]
    // 0x128e9ac: stp             lr, x16, [SP, #0x10]
    // 0x128e9b0: r16 = 56.000000
    //     0x128e9b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x128e9b4: ldr             x16, [x16, #0xb78]
    // 0x128e9b8: r30 = Instance_RotatedBox
    //     0x128e9b8: add             lr, PP, #0x48, lsl #12  ; [pp+0x48cf8] Obj!RotatedBox@d685a1
    //     0x128e9bc: ldr             lr, [lr, #0xcf8]
    // 0x128e9c0: stp             lr, x16, [SP]
    // 0x128e9c4: mov             x1, x0
    // 0x128e9c8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x3, width, 0x2, null]
    //     0x128e9c8: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0x128e9cc: ldr             x4, [x4, #0x5d0]
    // 0x128e9d0: r0 = Container()
    //     0x128e9d0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128e9d4: ldur            x0, [fp, #-0x30]
    // 0x128e9d8: cmp             w0, NULL
    // 0x128e9dc: b.ne            #0x128e9e8
    // 0x128e9e0: r1 = Null
    //     0x128e9e0: mov             x1, NULL
    // 0x128e9e4: b               #0x128e9f0
    // 0x128e9e8: LoadField: r1 = r0->field_7
    //     0x128e9e8: ldur            w1, [x0, #7]
    // 0x128e9ec: DecompressPointer r1
    //     0x128e9ec: add             x1, x1, HEAP, lsl #32
    // 0x128e9f0: cmp             w1, NULL
    // 0x128e9f4: b.ne            #0x128ea00
    // 0x128e9f8: r2 = ""
    //     0x128e9f8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128e9fc: b               #0x128ea04
    // 0x128ea00: mov             x2, x1
    // 0x128ea04: stur            x2, [fp, #-0x20]
    // 0x128ea08: r0 = CachedNetworkImage()
    //     0x128ea08: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x128ea0c: stur            x0, [fp, #-0x40]
    // 0x128ea10: r16 = 56.000000
    //     0x128ea10: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x128ea14: ldr             x16, [x16, #0xb78]
    // 0x128ea18: r30 = 56.000000
    //     0x128ea18: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0x128ea1c: ldr             lr, [lr, #0xb78]
    // 0x128ea20: stp             lr, x16, [SP, #0x10]
    // 0x128ea24: r16 = Instance_BoxFit
    //     0x128ea24: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x128ea28: ldr             x16, [x16, #0x118]
    // 0x128ea2c: r30 = 224
    //     0x128ea2c: movz            lr, #0xe0
    // 0x128ea30: stp             lr, x16, [SP]
    // 0x128ea34: mov             x1, x0
    // 0x128ea38: ldur            x2, [fp, #-0x20]
    // 0x128ea3c: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x4, height, 0x3, memCacheWidth, 0x5, width, 0x2, null]
    //     0x128ea3c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48d00] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x4, "height", 0x3, "memCacheWidth", 0x5, "width", 0x2, Null]
    //     0x128ea40: ldr             x4, [x4, #0xd00]
    // 0x128ea44: r0 = CachedNetworkImage()
    //     0x128ea44: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x128ea48: ldur            x0, [fp, #-0x30]
    // 0x128ea4c: cmp             w0, NULL
    // 0x128ea50: b.ne            #0x128ea5c
    // 0x128ea54: r1 = Null
    //     0x128ea54: mov             x1, NULL
    // 0x128ea58: b               #0x128ea64
    // 0x128ea5c: LoadField: r1 = r0->field_b
    //     0x128ea5c: ldur            w1, [x0, #0xb]
    // 0x128ea60: DecompressPointer r1
    //     0x128ea60: add             x1, x1, HEAP, lsl #32
    // 0x128ea64: cmp             w1, NULL
    // 0x128ea68: b.ne            #0x128ea74
    // 0x128ea6c: r2 = ""
    //     0x128ea6c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128ea70: b               #0x128ea78
    // 0x128ea74: mov             x2, x1
    // 0x128ea78: ldur            x1, [fp, #-0x28]
    // 0x128ea7c: stur            x2, [fp, #-0x50]
    // 0x128ea80: LoadField: r3 = r1->field_2f
    //     0x128ea80: ldur            w3, [x1, #0x2f]
    // 0x128ea84: DecompressPointer r3
    //     0x128ea84: add             x3, x3, HEAP, lsl #32
    // 0x128ea88: stur            x3, [fp, #-0x20]
    // 0x128ea8c: r0 = Text()
    //     0x128ea8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128ea90: mov             x1, x0
    // 0x128ea94: ldur            x0, [fp, #-0x50]
    // 0x128ea98: stur            x1, [fp, #-0x58]
    // 0x128ea9c: StoreField: r1->field_b = r0
    //     0x128ea9c: stur            w0, [x1, #0xb]
    // 0x128eaa0: ldur            x0, [fp, #-0x20]
    // 0x128eaa4: StoreField: r1->field_13 = r0
    //     0x128eaa4: stur            w0, [x1, #0x13]
    // 0x128eaa8: r0 = Instance_TextOverflow
    //     0x128eaa8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x128eaac: ldr             x0, [x0, #0xe10]
    // 0x128eab0: StoreField: r1->field_2b = r0
    //     0x128eab0: stur            w0, [x1, #0x2b]
    // 0x128eab4: r0 = 2
    //     0x128eab4: movz            x0, #0x2
    // 0x128eab8: StoreField: r1->field_37 = r0
    //     0x128eab8: stur            w0, [x1, #0x37]
    // 0x128eabc: r0 = SizedBox()
    //     0x128eabc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x128eac0: mov             x1, x0
    // 0x128eac4: r0 = 150.000000
    //     0x128eac4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0x128eac8: ldr             x0, [x0, #0x690]
    // 0x128eacc: stur            x1, [fp, #-0x50]
    // 0x128ead0: StoreField: r1->field_f = r0
    //     0x128ead0: stur            w0, [x1, #0xf]
    // 0x128ead4: ldur            x0, [fp, #-0x58]
    // 0x128ead8: StoreField: r1->field_b = r0
    //     0x128ead8: stur            w0, [x1, #0xb]
    // 0x128eadc: ldur            x0, [fp, #-0x30]
    // 0x128eae0: cmp             w0, NULL
    // 0x128eae4: b.ne            #0x128eaf0
    // 0x128eae8: r0 = Null
    //     0x128eae8: mov             x0, NULL
    // 0x128eaec: b               #0x128eafc
    // 0x128eaf0: LoadField: r2 = r0->field_13
    //     0x128eaf0: ldur            w2, [x0, #0x13]
    // 0x128eaf4: DecompressPointer r2
    //     0x128eaf4: add             x2, x2, HEAP, lsl #32
    // 0x128eaf8: mov             x0, x2
    // 0x128eafc: cmp             w0, NULL
    // 0x128eb00: b.ne            #0x128eb0c
    // 0x128eb04: r4 = ""
    //     0x128eb04: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x128eb08: b               #0x128eb10
    // 0x128eb0c: mov             x4, x0
    // 0x128eb10: ldur            x0, [fp, #-0x28]
    // 0x128eb14: ldur            x3, [fp, #-0x38]
    // 0x128eb18: ldur            x2, [fp, #-0x40]
    // 0x128eb1c: stur            x4, [fp, #-0x30]
    // 0x128eb20: LoadField: r5 = r0->field_3b
    //     0x128eb20: ldur            w5, [x0, #0x3b]
    // 0x128eb24: DecompressPointer r5
    //     0x128eb24: add             x5, x5, HEAP, lsl #32
    // 0x128eb28: stur            x5, [fp, #-0x20]
    // 0x128eb2c: r0 = Text()
    //     0x128eb2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128eb30: mov             x3, x0
    // 0x128eb34: ldur            x0, [fp, #-0x30]
    // 0x128eb38: stur            x3, [fp, #-0x28]
    // 0x128eb3c: StoreField: r3->field_b = r0
    //     0x128eb3c: stur            w0, [x3, #0xb]
    // 0x128eb40: ldur            x0, [fp, #-0x20]
    // 0x128eb44: StoreField: r3->field_13 = r0
    //     0x128eb44: stur            w0, [x3, #0x13]
    // 0x128eb48: r1 = Null
    //     0x128eb48: mov             x1, NULL
    // 0x128eb4c: r2 = 6
    //     0x128eb4c: movz            x2, #0x6
    // 0x128eb50: r0 = AllocateArray()
    //     0x128eb50: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128eb54: stur            x0, [fp, #-0x20]
    // 0x128eb58: r16 = Instance_Text
    //     0x128eb58: add             x16, PP, #0x48, lsl #12  ; [pp+0x48d08] Obj!Text@d65bb1
    //     0x128eb5c: ldr             x16, [x16, #0xd08]
    // 0x128eb60: StoreField: r0->field_f = r16
    //     0x128eb60: stur            w16, [x0, #0xf]
    // 0x128eb64: r16 = Instance_SizedBox
    //     0x128eb64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0x128eb68: ldr             x16, [x16, #0xa50]
    // 0x128eb6c: StoreField: r0->field_13 = r16
    //     0x128eb6c: stur            w16, [x0, #0x13]
    // 0x128eb70: ldur            x1, [fp, #-0x28]
    // 0x128eb74: ArrayStore: r0[0] = r1  ; List_4
    //     0x128eb74: stur            w1, [x0, #0x17]
    // 0x128eb78: r1 = <Widget>
    //     0x128eb78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128eb7c: r0 = AllocateGrowableArray()
    //     0x128eb7c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128eb80: mov             x1, x0
    // 0x128eb84: ldur            x0, [fp, #-0x20]
    // 0x128eb88: stur            x1, [fp, #-0x28]
    // 0x128eb8c: StoreField: r1->field_f = r0
    //     0x128eb8c: stur            w0, [x1, #0xf]
    // 0x128eb90: r2 = 6
    //     0x128eb90: movz            x2, #0x6
    // 0x128eb94: StoreField: r1->field_b = r2
    //     0x128eb94: stur            w2, [x1, #0xb]
    // 0x128eb98: r0 = Row()
    //     0x128eb98: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x128eb9c: mov             x3, x0
    // 0x128eba0: r0 = Instance_Axis
    //     0x128eba0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128eba4: stur            x3, [fp, #-0x20]
    // 0x128eba8: StoreField: r3->field_f = r0
    //     0x128eba8: stur            w0, [x3, #0xf]
    // 0x128ebac: r4 = Instance_MainAxisAlignment
    //     0x128ebac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128ebb0: ldr             x4, [x4, #0xa08]
    // 0x128ebb4: StoreField: r3->field_13 = r4
    //     0x128ebb4: stur            w4, [x3, #0x13]
    // 0x128ebb8: r5 = Instance_MainAxisSize
    //     0x128ebb8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128ebbc: ldr             x5, [x5, #0xa10]
    // 0x128ebc0: ArrayStore: r3[0] = r5  ; List_4
    //     0x128ebc0: stur            w5, [x3, #0x17]
    // 0x128ebc4: r6 = Instance_CrossAxisAlignment
    //     0x128ebc4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128ebc8: ldr             x6, [x6, #0xa18]
    // 0x128ebcc: StoreField: r3->field_1b = r6
    //     0x128ebcc: stur            w6, [x3, #0x1b]
    // 0x128ebd0: r7 = Instance_VerticalDirection
    //     0x128ebd0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128ebd4: ldr             x7, [x7, #0xa20]
    // 0x128ebd8: StoreField: r3->field_23 = r7
    //     0x128ebd8: stur            w7, [x3, #0x23]
    // 0x128ebdc: r8 = Instance_Clip
    //     0x128ebdc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128ebe0: ldr             x8, [x8, #0x38]
    // 0x128ebe4: StoreField: r3->field_2b = r8
    //     0x128ebe4: stur            w8, [x3, #0x2b]
    // 0x128ebe8: StoreField: r3->field_2f = rZR
    //     0x128ebe8: stur            xzr, [x3, #0x2f]
    // 0x128ebec: ldur            x1, [fp, #-0x28]
    // 0x128ebf0: StoreField: r3->field_b = r1
    //     0x128ebf0: stur            w1, [x3, #0xb]
    // 0x128ebf4: r1 = Null
    //     0x128ebf4: mov             x1, NULL
    // 0x128ebf8: r2 = 6
    //     0x128ebf8: movz            x2, #0x6
    // 0x128ebfc: r0 = AllocateArray()
    //     0x128ebfc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128ec00: mov             x2, x0
    // 0x128ec04: ldur            x0, [fp, #-0x50]
    // 0x128ec08: stur            x2, [fp, #-0x28]
    // 0x128ec0c: StoreField: r2->field_f = r0
    //     0x128ec0c: stur            w0, [x2, #0xf]
    // 0x128ec10: r16 = Instance_SizedBox
    //     0x128ec10: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x128ec14: ldr             x16, [x16, #0xc70]
    // 0x128ec18: StoreField: r2->field_13 = r16
    //     0x128ec18: stur            w16, [x2, #0x13]
    // 0x128ec1c: ldur            x0, [fp, #-0x20]
    // 0x128ec20: ArrayStore: r2[0] = r0  ; List_4
    //     0x128ec20: stur            w0, [x2, #0x17]
    // 0x128ec24: r1 = <Widget>
    //     0x128ec24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128ec28: r0 = AllocateGrowableArray()
    //     0x128ec28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128ec2c: mov             x1, x0
    // 0x128ec30: ldur            x0, [fp, #-0x28]
    // 0x128ec34: stur            x1, [fp, #-0x20]
    // 0x128ec38: StoreField: r1->field_f = r0
    //     0x128ec38: stur            w0, [x1, #0xf]
    // 0x128ec3c: r2 = 6
    //     0x128ec3c: movz            x2, #0x6
    // 0x128ec40: StoreField: r1->field_b = r2
    //     0x128ec40: stur            w2, [x1, #0xb]
    // 0x128ec44: r0 = Column()
    //     0x128ec44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x128ec48: mov             x1, x0
    // 0x128ec4c: r0 = Instance_Axis
    //     0x128ec4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x128ec50: stur            x1, [fp, #-0x28]
    // 0x128ec54: StoreField: r1->field_f = r0
    //     0x128ec54: stur            w0, [x1, #0xf]
    // 0x128ec58: r0 = Instance_MainAxisAlignment
    //     0x128ec58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128ec5c: ldr             x0, [x0, #0xa08]
    // 0x128ec60: StoreField: r1->field_13 = r0
    //     0x128ec60: stur            w0, [x1, #0x13]
    // 0x128ec64: r2 = Instance_MainAxisSize
    //     0x128ec64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128ec68: ldr             x2, [x2, #0xa10]
    // 0x128ec6c: ArrayStore: r1[0] = r2  ; List_4
    //     0x128ec6c: stur            w2, [x1, #0x17]
    // 0x128ec70: r3 = Instance_CrossAxisAlignment
    //     0x128ec70: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x128ec74: ldr             x3, [x3, #0x890]
    // 0x128ec78: StoreField: r1->field_1b = r3
    //     0x128ec78: stur            w3, [x1, #0x1b]
    // 0x128ec7c: r3 = Instance_VerticalDirection
    //     0x128ec7c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128ec80: ldr             x3, [x3, #0xa20]
    // 0x128ec84: StoreField: r1->field_23 = r3
    //     0x128ec84: stur            w3, [x1, #0x23]
    // 0x128ec88: r4 = Instance_Clip
    //     0x128ec88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128ec8c: ldr             x4, [x4, #0x38]
    // 0x128ec90: StoreField: r1->field_2b = r4
    //     0x128ec90: stur            w4, [x1, #0x2b]
    // 0x128ec94: StoreField: r1->field_2f = rZR
    //     0x128ec94: stur            xzr, [x1, #0x2f]
    // 0x128ec98: ldur            x5, [fp, #-0x20]
    // 0x128ec9c: StoreField: r1->field_b = r5
    //     0x128ec9c: stur            w5, [x1, #0xb]
    // 0x128eca0: r0 = Padding()
    //     0x128eca0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128eca4: mov             x2, x0
    // 0x128eca8: r0 = Instance_EdgeInsets
    //     0x128eca8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x128ecac: ldr             x0, [x0, #0xa78]
    // 0x128ecb0: stur            x2, [fp, #-0x20]
    // 0x128ecb4: StoreField: r2->field_f = r0
    //     0x128ecb4: stur            w0, [x2, #0xf]
    // 0x128ecb8: ldur            x0, [fp, #-0x28]
    // 0x128ecbc: StoreField: r2->field_b = r0
    //     0x128ecbc: stur            w0, [x2, #0xb]
    // 0x128ecc0: r1 = <FlexParentData>
    //     0x128ecc0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x128ecc4: ldr             x1, [x1, #0xe00]
    // 0x128ecc8: r0 = Expanded()
    //     0x128ecc8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x128eccc: mov             x3, x0
    // 0x128ecd0: r0 = 1
    //     0x128ecd0: movz            x0, #0x1
    // 0x128ecd4: stur            x3, [fp, #-0x28]
    // 0x128ecd8: StoreField: r3->field_13 = r0
    //     0x128ecd8: stur            x0, [x3, #0x13]
    // 0x128ecdc: r0 = Instance_FlexFit
    //     0x128ecdc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x128ece0: ldr             x0, [x0, #0xe08]
    // 0x128ece4: StoreField: r3->field_1b = r0
    //     0x128ece4: stur            w0, [x3, #0x1b]
    // 0x128ece8: ldur            x0, [fp, #-0x20]
    // 0x128ecec: StoreField: r3->field_b = r0
    //     0x128ecec: stur            w0, [x3, #0xb]
    // 0x128ecf0: r1 = Null
    //     0x128ecf0: mov             x1, NULL
    // 0x128ecf4: r2 = 6
    //     0x128ecf4: movz            x2, #0x6
    // 0x128ecf8: r0 = AllocateArray()
    //     0x128ecf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128ecfc: mov             x2, x0
    // 0x128ed00: ldur            x0, [fp, #-0x38]
    // 0x128ed04: stur            x2, [fp, #-0x20]
    // 0x128ed08: StoreField: r2->field_f = r0
    //     0x128ed08: stur            w0, [x2, #0xf]
    // 0x128ed0c: ldur            x0, [fp, #-0x40]
    // 0x128ed10: StoreField: r2->field_13 = r0
    //     0x128ed10: stur            w0, [x2, #0x13]
    // 0x128ed14: ldur            x0, [fp, #-0x28]
    // 0x128ed18: ArrayStore: r2[0] = r0  ; List_4
    //     0x128ed18: stur            w0, [x2, #0x17]
    // 0x128ed1c: r1 = <Widget>
    //     0x128ed1c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128ed20: r0 = AllocateGrowableArray()
    //     0x128ed20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128ed24: mov             x1, x0
    // 0x128ed28: ldur            x0, [fp, #-0x20]
    // 0x128ed2c: stur            x1, [fp, #-0x28]
    // 0x128ed30: StoreField: r1->field_f = r0
    //     0x128ed30: stur            w0, [x1, #0xf]
    // 0x128ed34: r0 = 6
    //     0x128ed34: movz            x0, #0x6
    // 0x128ed38: StoreField: r1->field_b = r0
    //     0x128ed38: stur            w0, [x1, #0xb]
    // 0x128ed3c: r0 = Row()
    //     0x128ed3c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x128ed40: mov             x1, x0
    // 0x128ed44: r0 = Instance_Axis
    //     0x128ed44: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128ed48: stur            x1, [fp, #-0x20]
    // 0x128ed4c: StoreField: r1->field_f = r0
    //     0x128ed4c: stur            w0, [x1, #0xf]
    // 0x128ed50: r0 = Instance_MainAxisAlignment
    //     0x128ed50: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128ed54: ldr             x0, [x0, #0xa08]
    // 0x128ed58: StoreField: r1->field_13 = r0
    //     0x128ed58: stur            w0, [x1, #0x13]
    // 0x128ed5c: r0 = Instance_MainAxisSize
    //     0x128ed5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128ed60: ldr             x0, [x0, #0xa10]
    // 0x128ed64: ArrayStore: r1[0] = r0  ; List_4
    //     0x128ed64: stur            w0, [x1, #0x17]
    // 0x128ed68: r0 = Instance_CrossAxisAlignment
    //     0x128ed68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128ed6c: ldr             x0, [x0, #0xa18]
    // 0x128ed70: StoreField: r1->field_1b = r0
    //     0x128ed70: stur            w0, [x1, #0x1b]
    // 0x128ed74: r0 = Instance_VerticalDirection
    //     0x128ed74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128ed78: ldr             x0, [x0, #0xa20]
    // 0x128ed7c: StoreField: r1->field_23 = r0
    //     0x128ed7c: stur            w0, [x1, #0x23]
    // 0x128ed80: r0 = Instance_Clip
    //     0x128ed80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128ed84: ldr             x0, [x0, #0x38]
    // 0x128ed88: StoreField: r1->field_2b = r0
    //     0x128ed88: stur            w0, [x1, #0x2b]
    // 0x128ed8c: StoreField: r1->field_2f = rZR
    //     0x128ed8c: stur            xzr, [x1, #0x2f]
    // 0x128ed90: ldur            x0, [fp, #-0x28]
    // 0x128ed94: StoreField: r1->field_b = r0
    //     0x128ed94: stur            w0, [x1, #0xb]
    // 0x128ed98: r0 = Container()
    //     0x128ed98: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x128ed9c: stur            x0, [fp, #-0x28]
    // 0x128eda0: ldur            x16, [fp, #-0x48]
    // 0x128eda4: ldur            lr, [fp, #-0x20]
    // 0x128eda8: stp             lr, x16, [SP]
    // 0x128edac: mov             x1, x0
    // 0x128edb0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x128edb0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x128edb4: ldr             x4, [x4, #0x88]
    // 0x128edb8: r0 = Container()
    //     0x128edb8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x128edbc: r0 = Padding()
    //     0x128edbc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128edc0: r1 = Instance_EdgeInsets
    //     0x128edc0: add             x1, PP, #0x48, lsl #12  ; [pp+0x485e0] Obj!EdgeInsets@d59901
    //     0x128edc4: ldr             x1, [x1, #0x5e0]
    // 0x128edc8: StoreField: r0->field_f = r1
    //     0x128edc8: stur            w1, [x0, #0xf]
    // 0x128edcc: ldur            x1, [fp, #-0x28]
    // 0x128edd0: StoreField: r0->field_b = r1
    //     0x128edd0: stur            w1, [x0, #0xb]
    // 0x128edd4: LeaveFrame
    //     0x128edd4: mov             SP, fp
    //     0x128edd8: ldp             fp, lr, [SP], #0x10
    // 0x128eddc: ret
    //     0x128eddc: ret             
    // 0x128ede0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128ede0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128ede4: b               #0x128e710
  }
  _ _buildHeader(/* No info */) {
    // ** addr: 0x128ede8, size: 0x228
    // 0x128ede8: EnterFrame
    //     0x128ede8: stp             fp, lr, [SP, #-0x10]!
    //     0x128edec: mov             fp, SP
    // 0x128edf0: AllocStack(0x28)
    //     0x128edf0: sub             SP, SP, #0x28
    // 0x128edf4: SetupParameters(BagBottomSheet this /* r1 => r1, fp-0x8 */)
    //     0x128edf4: stur            x1, [fp, #-8]
    // 0x128edf8: CheckStackOverflow
    //     0x128edf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x128edfc: cmp             SP, x16
    //     0x128ee00: b.ls            #0x128f008
    // 0x128ee04: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x128ee04: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x128ee08: ldr             x0, [x0, #0x1ab0]
    //     0x128ee0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x128ee10: cmp             w0, w16
    //     0x128ee14: b.ne            #0x128ee24
    //     0x128ee18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x128ee1c: ldr             x2, [x2, #0x60]
    //     0x128ee20: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x128ee24: LoadField: r1 = r0->field_87
    //     0x128ee24: ldur            w1, [x0, #0x87]
    // 0x128ee28: DecompressPointer r1
    //     0x128ee28: add             x1, x1, HEAP, lsl #32
    // 0x128ee2c: LoadField: r0 = r1->field_7
    //     0x128ee2c: ldur            w0, [x1, #7]
    // 0x128ee30: DecompressPointer r0
    //     0x128ee30: add             x0, x0, HEAP, lsl #32
    // 0x128ee34: r16 = Instance_Color
    //     0x128ee34: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x128ee38: r30 = 16.000000
    //     0x128ee38: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x128ee3c: ldr             lr, [lr, #0x188]
    // 0x128ee40: stp             lr, x16, [SP]
    // 0x128ee44: mov             x1, x0
    // 0x128ee48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x128ee48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x128ee4c: ldr             x4, [x4, #0x9b8]
    // 0x128ee50: r0 = copyWith()
    //     0x128ee50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x128ee54: stur            x0, [fp, #-0x10]
    // 0x128ee58: r0 = Text()
    //     0x128ee58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x128ee5c: mov             x1, x0
    // 0x128ee60: r0 = "Bag"
    //     0x128ee60: add             x0, PP, #0x38, lsl #12  ; [pp+0x38d60] "Bag"
    //     0x128ee64: ldr             x0, [x0, #0xd60]
    // 0x128ee68: stur            x1, [fp, #-0x18]
    // 0x128ee6c: StoreField: r1->field_b = r0
    //     0x128ee6c: stur            w0, [x1, #0xb]
    // 0x128ee70: ldur            x0, [fp, #-0x10]
    // 0x128ee74: StoreField: r1->field_13 = r0
    //     0x128ee74: stur            w0, [x1, #0x13]
    // 0x128ee78: ldur            x0, [fp, #-8]
    // 0x128ee7c: LoadField: r2 = r0->field_27
    //     0x128ee7c: ldur            w2, [x0, #0x27]
    // 0x128ee80: DecompressPointer r2
    //     0x128ee80: add             x2, x2, HEAP, lsl #32
    // 0x128ee84: stur            x2, [fp, #-0x10]
    // 0x128ee88: r0 = ColorFilter()
    //     0x128ee88: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x128ee8c: mov             x1, x0
    // 0x128ee90: ldur            x0, [fp, #-0x10]
    // 0x128ee94: stur            x1, [fp, #-8]
    // 0x128ee98: StoreField: r1->field_7 = r0
    //     0x128ee98: stur            w0, [x1, #7]
    // 0x128ee9c: r0 = Instance_BlendMode
    //     0x128ee9c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x128eea0: ldr             x0, [x0, #0xb30]
    // 0x128eea4: StoreField: r1->field_b = r0
    //     0x128eea4: stur            w0, [x1, #0xb]
    // 0x128eea8: r0 = 1
    //     0x128eea8: movz            x0, #0x1
    // 0x128eeac: StoreField: r1->field_13 = r0
    //     0x128eeac: stur            x0, [x1, #0x13]
    // 0x128eeb0: r0 = SvgPicture()
    //     0x128eeb0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x128eeb4: stur            x0, [fp, #-0x10]
    // 0x128eeb8: r16 = Instance_BoxFit
    //     0x128eeb8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x128eebc: ldr             x16, [x16, #0xb18]
    // 0x128eec0: ldur            lr, [fp, #-8]
    // 0x128eec4: stp             lr, x16, [SP]
    // 0x128eec8: mov             x1, x0
    // 0x128eecc: r2 = "assets/images/x.svg"
    //     0x128eecc: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0x128eed0: ldr             x2, [x2, #0x5e8]
    // 0x128eed4: r4 = const [0, 0x4, 0x2, 0x2, colorFilter, 0x3, fit, 0x2, null]
    //     0x128eed4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33820] List(9) [0, 0x4, 0x2, 0x2, "colorFilter", 0x3, "fit", 0x2, Null]
    //     0x128eed8: ldr             x4, [x4, #0x820]
    // 0x128eedc: r0 = SvgPicture.asset()
    //     0x128eedc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x128eee0: r0 = InkWell()
    //     0x128eee0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x128eee4: mov             x3, x0
    // 0x128eee8: ldur            x0, [fp, #-0x10]
    // 0x128eeec: stur            x3, [fp, #-8]
    // 0x128eef0: StoreField: r3->field_b = r0
    //     0x128eef0: stur            w0, [x3, #0xb]
    // 0x128eef4: r1 = Function '<anonymous closure>':.
    //     0x128eef4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48d10] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x128eef8: ldr             x1, [x1, #0xd10]
    // 0x128eefc: r2 = Null
    //     0x128eefc: mov             x2, NULL
    // 0x128ef00: r0 = AllocateClosure()
    //     0x128ef00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x128ef04: mov             x1, x0
    // 0x128ef08: ldur            x0, [fp, #-8]
    // 0x128ef0c: StoreField: r0->field_f = r1
    //     0x128ef0c: stur            w1, [x0, #0xf]
    // 0x128ef10: r1 = true
    //     0x128ef10: add             x1, NULL, #0x20  ; true
    // 0x128ef14: StoreField: r0->field_43 = r1
    //     0x128ef14: stur            w1, [x0, #0x43]
    // 0x128ef18: r2 = Instance_BoxShape
    //     0x128ef18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x128ef1c: ldr             x2, [x2, #0x80]
    // 0x128ef20: StoreField: r0->field_47 = r2
    //     0x128ef20: stur            w2, [x0, #0x47]
    // 0x128ef24: StoreField: r0->field_6f = r1
    //     0x128ef24: stur            w1, [x0, #0x6f]
    // 0x128ef28: r2 = false
    //     0x128ef28: add             x2, NULL, #0x30  ; false
    // 0x128ef2c: StoreField: r0->field_73 = r2
    //     0x128ef2c: stur            w2, [x0, #0x73]
    // 0x128ef30: StoreField: r0->field_83 = r1
    //     0x128ef30: stur            w1, [x0, #0x83]
    // 0x128ef34: StoreField: r0->field_7b = r2
    //     0x128ef34: stur            w2, [x0, #0x7b]
    // 0x128ef38: r1 = Null
    //     0x128ef38: mov             x1, NULL
    // 0x128ef3c: r2 = 6
    //     0x128ef3c: movz            x2, #0x6
    // 0x128ef40: r0 = AllocateArray()
    //     0x128ef40: bl              #0x16f7198  ; AllocateArrayStub
    // 0x128ef44: mov             x2, x0
    // 0x128ef48: ldur            x0, [fp, #-0x18]
    // 0x128ef4c: stur            x2, [fp, #-0x10]
    // 0x128ef50: StoreField: r2->field_f = r0
    //     0x128ef50: stur            w0, [x2, #0xf]
    // 0x128ef54: r16 = Instance_Spacer
    //     0x128ef54: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x128ef58: ldr             x16, [x16, #0xf0]
    // 0x128ef5c: StoreField: r2->field_13 = r16
    //     0x128ef5c: stur            w16, [x2, #0x13]
    // 0x128ef60: ldur            x0, [fp, #-8]
    // 0x128ef64: ArrayStore: r2[0] = r0  ; List_4
    //     0x128ef64: stur            w0, [x2, #0x17]
    // 0x128ef68: r1 = <Widget>
    //     0x128ef68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x128ef6c: r0 = AllocateGrowableArray()
    //     0x128ef6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x128ef70: mov             x1, x0
    // 0x128ef74: ldur            x0, [fp, #-0x10]
    // 0x128ef78: stur            x1, [fp, #-8]
    // 0x128ef7c: StoreField: r1->field_f = r0
    //     0x128ef7c: stur            w0, [x1, #0xf]
    // 0x128ef80: r0 = 6
    //     0x128ef80: movz            x0, #0x6
    // 0x128ef84: StoreField: r1->field_b = r0
    //     0x128ef84: stur            w0, [x1, #0xb]
    // 0x128ef88: r0 = Row()
    //     0x128ef88: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x128ef8c: mov             x1, x0
    // 0x128ef90: r0 = Instance_Axis
    //     0x128ef90: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x128ef94: stur            x1, [fp, #-0x10]
    // 0x128ef98: StoreField: r1->field_f = r0
    //     0x128ef98: stur            w0, [x1, #0xf]
    // 0x128ef9c: r0 = Instance_MainAxisAlignment
    //     0x128ef9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x128efa0: ldr             x0, [x0, #0xa08]
    // 0x128efa4: StoreField: r1->field_13 = r0
    //     0x128efa4: stur            w0, [x1, #0x13]
    // 0x128efa8: r0 = Instance_MainAxisSize
    //     0x128efa8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x128efac: ldr             x0, [x0, #0xa10]
    // 0x128efb0: ArrayStore: r1[0] = r0  ; List_4
    //     0x128efb0: stur            w0, [x1, #0x17]
    // 0x128efb4: r0 = Instance_CrossAxisAlignment
    //     0x128efb4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x128efb8: ldr             x0, [x0, #0xa18]
    // 0x128efbc: StoreField: r1->field_1b = r0
    //     0x128efbc: stur            w0, [x1, #0x1b]
    // 0x128efc0: r0 = Instance_VerticalDirection
    //     0x128efc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x128efc4: ldr             x0, [x0, #0xa20]
    // 0x128efc8: StoreField: r1->field_23 = r0
    //     0x128efc8: stur            w0, [x1, #0x23]
    // 0x128efcc: r0 = Instance_Clip
    //     0x128efcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x128efd0: ldr             x0, [x0, #0x38]
    // 0x128efd4: StoreField: r1->field_2b = r0
    //     0x128efd4: stur            w0, [x1, #0x2b]
    // 0x128efd8: StoreField: r1->field_2f = rZR
    //     0x128efd8: stur            xzr, [x1, #0x2f]
    // 0x128efdc: ldur            x0, [fp, #-8]
    // 0x128efe0: StoreField: r1->field_b = r0
    //     0x128efe0: stur            w0, [x1, #0xb]
    // 0x128efe4: r0 = Padding()
    //     0x128efe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x128efe8: r1 = Instance_EdgeInsets
    //     0x128efe8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x128efec: ldr             x1, [x1, #0x668]
    // 0x128eff0: StoreField: r0->field_f = r1
    //     0x128eff0: stur            w1, [x0, #0xf]
    // 0x128eff4: ldur            x1, [fp, #-0x10]
    // 0x128eff8: StoreField: r0->field_b = r1
    //     0x128eff8: stur            w1, [x0, #0xb]
    // 0x128effc: LeaveFrame
    //     0x128effc: mov             SP, fp
    //     0x128f000: ldp             fp, lr, [SP], #0x10
    // 0x128f004: ret
    //     0x128f004: ret             
    // 0x128f008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x128f008: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x128f00c: b               #0x128ee04
  }
  _ BagBottomSheet(/* No info */) {
    // ** addr: 0x146d4cc, size: 0x5d8
    // 0x146d4cc: EnterFrame
    //     0x146d4cc: stp             fp, lr, [SP, #-0x10]!
    //     0x146d4d0: mov             fp, SP
    // 0x146d4d4: AllocStack(0x48)
    //     0x146d4d4: sub             SP, SP, #0x48
    // 0x146d4d8: SetupParameters(BagBottomSheet this /* r1 => r5, fp-0x28 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r4 */, dynamic _ /* r5 => r3 */, dynamic _ /* r6 => r2 */, dynamic _ /* r7 => r1 */)
    //     0x146d4d8: mov             x4, x3
    //     0x146d4dc: mov             x3, x5
    //     0x146d4e0: mov             x5, x1
    //     0x146d4e4: mov             x0, x2
    //     0x146d4e8: mov             x2, x6
    //     0x146d4ec: stur            x1, [fp, #-0x28]
    //     0x146d4f0: mov             x1, x7
    // 0x146d4f4: CheckStackOverflow
    //     0x146d4f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x146d4f8: cmp             SP, x16
    //     0x146d4fc: b.ls            #0x146da9c
    // 0x146d500: StoreField: r5->field_b = r0
    //     0x146d500: stur            w0, [x5, #0xb]
    //     0x146d504: ldurb           w16, [x5, #-1]
    //     0x146d508: ldurb           w17, [x0, #-1]
    //     0x146d50c: and             x16, x17, x16, lsr #2
    //     0x146d510: tst             x16, HEAP, lsr #32
    //     0x146d514: b.eq            #0x146d51c
    //     0x146d518: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x146d51c: ldr             x0, [fp, #0x18]
    // 0x146d520: StoreField: r5->field_f = r0
    //     0x146d520: stur            w0, [x5, #0xf]
    //     0x146d524: ldurb           w16, [x5, #-1]
    //     0x146d528: ldurb           w17, [x0, #-1]
    //     0x146d52c: and             x16, x17, x16, lsr #2
    //     0x146d530: tst             x16, HEAP, lsr #32
    //     0x146d534: b.eq            #0x146d53c
    //     0x146d538: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x146d53c: ldr             x0, [fp, #0x10]
    // 0x146d540: StoreField: r5->field_13 = r0
    //     0x146d540: stur            w0, [x5, #0x13]
    //     0x146d544: ldurb           w16, [x5, #-1]
    //     0x146d548: ldurb           w17, [x0, #-1]
    //     0x146d54c: and             x16, x17, x16, lsr #2
    //     0x146d550: tst             x16, HEAP, lsr #32
    //     0x146d554: b.eq            #0x146d55c
    //     0x146d558: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x146d55c: mov             x0, x4
    // 0x146d560: ArrayStore: r5[0] = r0  ; List_4
    //     0x146d560: stur            w0, [x5, #0x17]
    //     0x146d564: ldurb           w16, [x5, #-1]
    //     0x146d568: ldurb           w17, [x0, #-1]
    //     0x146d56c: and             x16, x17, x16, lsr #2
    //     0x146d570: tst             x16, HEAP, lsr #32
    //     0x146d574: b.eq            #0x146d57c
    //     0x146d578: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x146d57c: mov             x0, x1
    // 0x146d580: StoreField: r5->field_1b = r0
    //     0x146d580: stur            w0, [x5, #0x1b]
    //     0x146d584: ldurb           w16, [x5, #-1]
    //     0x146d588: ldurb           w17, [x0, #-1]
    //     0x146d58c: and             x16, x17, x16, lsr #2
    //     0x146d590: tst             x16, HEAP, lsr #32
    //     0x146d594: b.eq            #0x146d59c
    //     0x146d598: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x146d59c: mov             x0, x3
    // 0x146d5a0: StoreField: r5->field_1f = r0
    //     0x146d5a0: stur            w0, [x5, #0x1f]
    //     0x146d5a4: ldurb           w16, [x5, #-1]
    //     0x146d5a8: ldurb           w17, [x0, #-1]
    //     0x146d5ac: and             x16, x17, x16, lsr #2
    //     0x146d5b0: tst             x16, HEAP, lsr #32
    //     0x146d5b4: b.eq            #0x146d5bc
    //     0x146d5b8: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x146d5bc: mov             x0, x2
    // 0x146d5c0: StoreField: r5->field_23 = r0
    //     0x146d5c0: stur            w0, [x5, #0x23]
    //     0x146d5c4: ldurb           w16, [x5, #-1]
    //     0x146d5c8: ldurb           w17, [x0, #-1]
    //     0x146d5cc: and             x16, x17, x16, lsr #2
    //     0x146d5d0: tst             x16, HEAP, lsr #32
    //     0x146d5d4: b.eq            #0x146d5dc
    //     0x146d5d8: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x146d5dc: LoadField: r0 = r3->field_3f
    //     0x146d5dc: ldur            w0, [x3, #0x3f]
    // 0x146d5e0: DecompressPointer r0
    //     0x146d5e0: add             x0, x0, HEAP, lsl #32
    // 0x146d5e4: stur            x0, [fp, #-0x20]
    // 0x146d5e8: cmp             w0, NULL
    // 0x146d5ec: b.ne            #0x146d5f8
    // 0x146d5f0: r1 = Null
    //     0x146d5f0: mov             x1, NULL
    // 0x146d5f4: b               #0x146d61c
    // 0x146d5f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x146d5f8: ldur            w1, [x0, #0x17]
    // 0x146d5fc: DecompressPointer r1
    //     0x146d5fc: add             x1, x1, HEAP, lsl #32
    // 0x146d600: cmp             w1, NULL
    // 0x146d604: b.ne            #0x146d610
    // 0x146d608: r1 = Null
    //     0x146d608: mov             x1, NULL
    // 0x146d60c: b               #0x146d61c
    // 0x146d610: LoadField: r2 = r1->field_7
    //     0x146d610: ldur            w2, [x1, #7]
    // 0x146d614: DecompressPointer r2
    //     0x146d614: add             x2, x2, HEAP, lsl #32
    // 0x146d618: mov             x1, x2
    // 0x146d61c: cmp             w1, NULL
    // 0x146d620: b.ne            #0x146d62c
    // 0x146d624: r1 = 0
    //     0x146d624: movz            x1, #0
    // 0x146d628: b               #0x146d63c
    // 0x146d62c: r2 = LoadInt32Instr(r1)
    //     0x146d62c: sbfx            x2, x1, #1, #0x1f
    //     0x146d630: tbz             w1, #0, #0x146d638
    //     0x146d634: ldur            x2, [x1, #7]
    // 0x146d638: mov             x1, x2
    // 0x146d63c: stur            x1, [fp, #-0x18]
    // 0x146d640: cmp             w0, NULL
    // 0x146d644: b.ne            #0x146d650
    // 0x146d648: r2 = Null
    //     0x146d648: mov             x2, NULL
    // 0x146d64c: b               #0x146d674
    // 0x146d650: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x146d650: ldur            w2, [x0, #0x17]
    // 0x146d654: DecompressPointer r2
    //     0x146d654: add             x2, x2, HEAP, lsl #32
    // 0x146d658: cmp             w2, NULL
    // 0x146d65c: b.ne            #0x146d668
    // 0x146d660: r2 = Null
    //     0x146d660: mov             x2, NULL
    // 0x146d664: b               #0x146d674
    // 0x146d668: LoadField: r3 = r2->field_b
    //     0x146d668: ldur            w3, [x2, #0xb]
    // 0x146d66c: DecompressPointer r3
    //     0x146d66c: add             x3, x3, HEAP, lsl #32
    // 0x146d670: mov             x2, x3
    // 0x146d674: cmp             w2, NULL
    // 0x146d678: b.ne            #0x146d684
    // 0x146d67c: r2 = 0
    //     0x146d67c: movz            x2, #0
    // 0x146d680: b               #0x146d694
    // 0x146d684: r3 = LoadInt32Instr(r2)
    //     0x146d684: sbfx            x3, x2, #1, #0x1f
    //     0x146d688: tbz             w2, #0, #0x146d690
    //     0x146d68c: ldur            x3, [x2, #7]
    // 0x146d690: mov             x2, x3
    // 0x146d694: stur            x2, [fp, #-0x10]
    // 0x146d698: cmp             w0, NULL
    // 0x146d69c: b.ne            #0x146d6a8
    // 0x146d6a0: r3 = Null
    //     0x146d6a0: mov             x3, NULL
    // 0x146d6a4: b               #0x146d6cc
    // 0x146d6a8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x146d6a8: ldur            w3, [x0, #0x17]
    // 0x146d6ac: DecompressPointer r3
    //     0x146d6ac: add             x3, x3, HEAP, lsl #32
    // 0x146d6b0: cmp             w3, NULL
    // 0x146d6b4: b.ne            #0x146d6c0
    // 0x146d6b8: r3 = Null
    //     0x146d6b8: mov             x3, NULL
    // 0x146d6bc: b               #0x146d6cc
    // 0x146d6c0: LoadField: r4 = r3->field_f
    //     0x146d6c0: ldur            w4, [x3, #0xf]
    // 0x146d6c4: DecompressPointer r4
    //     0x146d6c4: add             x4, x4, HEAP, lsl #32
    // 0x146d6c8: mov             x3, x4
    // 0x146d6cc: cmp             w3, NULL
    // 0x146d6d0: b.ne            #0x146d6dc
    // 0x146d6d4: r3 = 0
    //     0x146d6d4: movz            x3, #0
    // 0x146d6d8: b               #0x146d6ec
    // 0x146d6dc: r4 = LoadInt32Instr(r3)
    //     0x146d6dc: sbfx            x4, x3, #1, #0x1f
    //     0x146d6e0: tbz             w3, #0, #0x146d6e8
    //     0x146d6e4: ldur            x4, [x3, #7]
    // 0x146d6e8: mov             x3, x4
    // 0x146d6ec: stur            x3, [fp, #-8]
    // 0x146d6f0: r0 = Color()
    //     0x146d6f0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x146d6f4: r1 = Instance_ColorSpace
    //     0x146d6f4: ldr             x1, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x146d6f8: StoreField: r0->field_27 = r1
    //     0x146d6f8: stur            w1, [x0, #0x27]
    // 0x146d6fc: d0 = 1.000000
    //     0x146d6fc: fmov            d0, #1.00000000
    // 0x146d700: StoreField: r0->field_7 = d0
    //     0x146d700: stur            d0, [x0, #7]
    // 0x146d704: ldur            x2, [fp, #-0x18]
    // 0x146d708: ubfx            x2, x2, #0, #0x20
    // 0x146d70c: and             w3, w2, #0xff
    // 0x146d710: ubfx            x3, x3, #0, #0x20
    // 0x146d714: scvtf           d0, x3
    // 0x146d718: d1 = 255.000000
    //     0x146d718: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x146d71c: fdiv            d2, d0, d1
    // 0x146d720: StoreField: r0->field_f = d2
    //     0x146d720: stur            d2, [x0, #0xf]
    // 0x146d724: ldur            x2, [fp, #-0x10]
    // 0x146d728: ubfx            x2, x2, #0, #0x20
    // 0x146d72c: and             w3, w2, #0xff
    // 0x146d730: ubfx            x3, x3, #0, #0x20
    // 0x146d734: scvtf           d0, x3
    // 0x146d738: fdiv            d2, d0, d1
    // 0x146d73c: ArrayStore: r0[0] = d2  ; List_8
    //     0x146d73c: stur            d2, [x0, #0x17]
    // 0x146d740: ldur            x2, [fp, #-8]
    // 0x146d744: ubfx            x2, x2, #0, #0x20
    // 0x146d748: and             w3, w2, #0xff
    // 0x146d74c: ubfx            x3, x3, #0, #0x20
    // 0x146d750: scvtf           d0, x3
    // 0x146d754: fdiv            d2, d0, d1
    // 0x146d758: StoreField: r0->field_1f = d2
    //     0x146d758: stur            d2, [x0, #0x1f]
    // 0x146d75c: ldur            x2, [fp, #-0x28]
    // 0x146d760: StoreField: r2->field_27 = r0
    //     0x146d760: stur            w0, [x2, #0x27]
    //     0x146d764: ldurb           w16, [x2, #-1]
    //     0x146d768: ldurb           w17, [x0, #-1]
    //     0x146d76c: and             x16, x17, x16, lsr #2
    //     0x146d770: tst             x16, HEAP, lsr #32
    //     0x146d774: b.eq            #0x146d77c
    //     0x146d778: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x146d77c: ldur            x0, [fp, #-0x20]
    // 0x146d780: cmp             w0, NULL
    // 0x146d784: b.ne            #0x146d790
    // 0x146d788: r3 = Null
    //     0x146d788: mov             x3, NULL
    // 0x146d78c: b               #0x146d7b4
    // 0x146d790: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x146d790: ldur            w3, [x0, #0x17]
    // 0x146d794: DecompressPointer r3
    //     0x146d794: add             x3, x3, HEAP, lsl #32
    // 0x146d798: cmp             w3, NULL
    // 0x146d79c: b.ne            #0x146d7a8
    // 0x146d7a0: r3 = Null
    //     0x146d7a0: mov             x3, NULL
    // 0x146d7a4: b               #0x146d7b4
    // 0x146d7a8: LoadField: r4 = r3->field_7
    //     0x146d7a8: ldur            w4, [x3, #7]
    // 0x146d7ac: DecompressPointer r4
    //     0x146d7ac: add             x4, x4, HEAP, lsl #32
    // 0x146d7b0: mov             x3, x4
    // 0x146d7b4: cmp             w3, NULL
    // 0x146d7b8: b.ne            #0x146d7c4
    // 0x146d7bc: r3 = 0
    //     0x146d7bc: movz            x3, #0
    // 0x146d7c0: b               #0x146d7d4
    // 0x146d7c4: r4 = LoadInt32Instr(r3)
    //     0x146d7c4: sbfx            x4, x3, #1, #0x1f
    //     0x146d7c8: tbz             w3, #0, #0x146d7d0
    //     0x146d7cc: ldur            x4, [x3, #7]
    // 0x146d7d0: mov             x3, x4
    // 0x146d7d4: stur            x3, [fp, #-0x18]
    // 0x146d7d8: cmp             w0, NULL
    // 0x146d7dc: b.ne            #0x146d7e8
    // 0x146d7e0: r4 = Null
    //     0x146d7e0: mov             x4, NULL
    // 0x146d7e4: b               #0x146d80c
    // 0x146d7e8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x146d7e8: ldur            w4, [x0, #0x17]
    // 0x146d7ec: DecompressPointer r4
    //     0x146d7ec: add             x4, x4, HEAP, lsl #32
    // 0x146d7f0: cmp             w4, NULL
    // 0x146d7f4: b.ne            #0x146d800
    // 0x146d7f8: r4 = Null
    //     0x146d7f8: mov             x4, NULL
    // 0x146d7fc: b               #0x146d80c
    // 0x146d800: LoadField: r5 = r4->field_b
    //     0x146d800: ldur            w5, [x4, #0xb]
    // 0x146d804: DecompressPointer r5
    //     0x146d804: add             x5, x5, HEAP, lsl #32
    // 0x146d808: mov             x4, x5
    // 0x146d80c: cmp             w4, NULL
    // 0x146d810: b.ne            #0x146d81c
    // 0x146d814: r4 = 0
    //     0x146d814: movz            x4, #0
    // 0x146d818: b               #0x146d82c
    // 0x146d81c: r5 = LoadInt32Instr(r4)
    //     0x146d81c: sbfx            x5, x4, #1, #0x1f
    //     0x146d820: tbz             w4, #0, #0x146d828
    //     0x146d824: ldur            x5, [x4, #7]
    // 0x146d828: mov             x4, x5
    // 0x146d82c: stur            x4, [fp, #-0x10]
    // 0x146d830: cmp             w0, NULL
    // 0x146d834: b.ne            #0x146d840
    // 0x146d838: r0 = Null
    //     0x146d838: mov             x0, NULL
    // 0x146d83c: b               #0x146d860
    // 0x146d840: ArrayLoad: r5 = r0[0]  ; List_4
    //     0x146d840: ldur            w5, [x0, #0x17]
    // 0x146d844: DecompressPointer r5
    //     0x146d844: add             x5, x5, HEAP, lsl #32
    // 0x146d848: cmp             w5, NULL
    // 0x146d84c: b.ne            #0x146d858
    // 0x146d850: r0 = Null
    //     0x146d850: mov             x0, NULL
    // 0x146d854: b               #0x146d860
    // 0x146d858: LoadField: r0 = r5->field_f
    //     0x146d858: ldur            w0, [x5, #0xf]
    // 0x146d85c: DecompressPointer r0
    //     0x146d85c: add             x0, x0, HEAP, lsl #32
    // 0x146d860: cmp             w0, NULL
    // 0x146d864: b.ne            #0x146d870
    // 0x146d868: r0 = 0
    //     0x146d868: movz            x0, #0
    // 0x146d86c: b               #0x146d880
    // 0x146d870: r5 = LoadInt32Instr(r0)
    //     0x146d870: sbfx            x5, x0, #1, #0x1f
    //     0x146d874: tbz             w0, #0, #0x146d87c
    //     0x146d878: ldur            x5, [x0, #7]
    // 0x146d87c: mov             x0, x5
    // 0x146d880: stur            x0, [fp, #-8]
    // 0x146d884: r0 = Color()
    //     0x146d884: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x146d888: mov             x1, x0
    // 0x146d88c: r0 = Instance_ColorSpace
    //     0x146d88c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x146d890: StoreField: r1->field_27 = r0
    //     0x146d890: stur            w0, [x1, #0x27]
    // 0x146d894: d0 = 0.400000
    //     0x146d894: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x146d898: StoreField: r1->field_7 = d0
    //     0x146d898: stur            d0, [x1, #7]
    // 0x146d89c: ldur            x0, [fp, #-0x18]
    // 0x146d8a0: ubfx            x0, x0, #0, #0x20
    // 0x146d8a4: and             w2, w0, #0xff
    // 0x146d8a8: ubfx            x2, x2, #0, #0x20
    // 0x146d8ac: scvtf           d1, x2
    // 0x146d8b0: d2 = 255.000000
    //     0x146d8b0: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x146d8b4: fdiv            d3, d1, d2
    // 0x146d8b8: StoreField: r1->field_f = d3
    //     0x146d8b8: stur            d3, [x1, #0xf]
    // 0x146d8bc: ldur            x0, [fp, #-0x10]
    // 0x146d8c0: ubfx            x0, x0, #0, #0x20
    // 0x146d8c4: and             w2, w0, #0xff
    // 0x146d8c8: ubfx            x2, x2, #0, #0x20
    // 0x146d8cc: scvtf           d1, x2
    // 0x146d8d0: fdiv            d3, d1, d2
    // 0x146d8d4: ArrayStore: r1[0] = d3  ; List_8
    //     0x146d8d4: stur            d3, [x1, #0x17]
    // 0x146d8d8: ldur            x0, [fp, #-8]
    // 0x146d8dc: ubfx            x0, x0, #0, #0x20
    // 0x146d8e0: and             w2, w0, #0xff
    // 0x146d8e4: ubfx            x2, x2, #0, #0x20
    // 0x146d8e8: scvtf           d1, x2
    // 0x146d8ec: fdiv            d3, d1, d2
    // 0x146d8f0: StoreField: r1->field_1f = d3
    //     0x146d8f0: stur            d3, [x1, #0x1f]
    // 0x146d8f4: mov             x0, x1
    // 0x146d8f8: ldur            x1, [fp, #-0x28]
    // 0x146d8fc: StoreField: r1->field_2b = r0
    //     0x146d8fc: stur            w0, [x1, #0x2b]
    //     0x146d900: ldurb           w16, [x1, #-1]
    //     0x146d904: ldurb           w17, [x0, #-1]
    //     0x146d908: and             x16, x17, x16, lsr #2
    //     0x146d90c: tst             x16, HEAP, lsr #32
    //     0x146d910: b.eq            #0x146d918
    //     0x146d914: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x146d918: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0x146d918: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x146d91c: ldr             x0, [x0, #0x1ab0]
    //     0x146d920: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x146d924: cmp             w0, w16
    //     0x146d928: b.ne            #0x146d938
    //     0x146d92c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0x146d930: ldr             x2, [x2, #0x60]
    //     0x146d934: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x146d938: LoadField: r2 = r0->field_87
    //     0x146d938: ldur            w2, [x0, #0x87]
    // 0x146d93c: DecompressPointer r2
    //     0x146d93c: add             x2, x2, HEAP, lsl #32
    // 0x146d940: stur            x2, [fp, #-0x30]
    // 0x146d944: LoadField: r0 = r2->field_7
    //     0x146d944: ldur            w0, [x2, #7]
    // 0x146d948: DecompressPointer r0
    //     0x146d948: add             x0, x0, HEAP, lsl #32
    // 0x146d94c: stur            x0, [fp, #-0x20]
    // 0x146d950: r16 = 12.000000
    //     0x146d950: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x146d954: ldr             x16, [x16, #0x9e8]
    // 0x146d958: r30 = Instance_Color
    //     0x146d958: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x146d95c: stp             lr, x16, [SP]
    // 0x146d960: mov             x1, x0
    // 0x146d964: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x146d964: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x146d968: ldr             x4, [x4, #0xaa0]
    // 0x146d96c: r0 = copyWith()
    //     0x146d96c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x146d970: ldur            x2, [fp, #-0x28]
    // 0x146d974: StoreField: r2->field_2f = r0
    //     0x146d974: stur            w0, [x2, #0x2f]
    //     0x146d978: ldurb           w16, [x2, #-1]
    //     0x146d97c: ldurb           w17, [x0, #-1]
    //     0x146d980: and             x16, x17, x16, lsr #2
    //     0x146d984: tst             x16, HEAP, lsr #32
    //     0x146d988: b.eq            #0x146d990
    //     0x146d98c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x146d990: r1 = Instance_Color
    //     0x146d990: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x146d994: d0 = 0.700000
    //     0x146d994: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x146d998: ldr             d0, [x17, #0xf48]
    // 0x146d99c: r0 = withOpacity()
    //     0x146d99c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x146d9a0: r16 = 12.000000
    //     0x146d9a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x146d9a4: ldr             x16, [x16, #0x9e8]
    // 0x146d9a8: stp             x0, x16, [SP]
    // 0x146d9ac: ldur            x1, [fp, #-0x20]
    // 0x146d9b0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x146d9b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x146d9b4: ldr             x4, [x4, #0xaa0]
    // 0x146d9b8: r0 = copyWith()
    //     0x146d9b8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x146d9bc: ldur            x2, [fp, #-0x28]
    // 0x146d9c0: StoreField: r2->field_33 = r0
    //     0x146d9c0: stur            w0, [x2, #0x33]
    //     0x146d9c4: ldurb           w16, [x2, #-1]
    //     0x146d9c8: ldurb           w17, [x0, #-1]
    //     0x146d9cc: and             x16, x17, x16, lsr #2
    //     0x146d9d0: tst             x16, HEAP, lsr #32
    //     0x146d9d4: b.eq            #0x146d9dc
    //     0x146d9d8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x146d9dc: ldur            x0, [fp, #-0x30]
    // 0x146d9e0: LoadField: r3 = r0->field_2b
    //     0x146d9e0: ldur            w3, [x0, #0x2b]
    // 0x146d9e4: DecompressPointer r3
    //     0x146d9e4: add             x3, x3, HEAP, lsl #32
    // 0x146d9e8: stur            x3, [fp, #-0x20]
    // 0x146d9ec: r1 = Instance_Color
    //     0x146d9ec: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x146d9f0: d0 = 0.700000
    //     0x146d9f0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x146d9f4: ldr             d0, [x17, #0xf48]
    // 0x146d9f8: r0 = withOpacity()
    //     0x146d9f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x146d9fc: r16 = 12.000000
    //     0x146d9fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x146da00: ldr             x16, [x16, #0x9e8]
    // 0x146da04: stp             x0, x16, [SP]
    // 0x146da08: ldur            x1, [fp, #-0x20]
    // 0x146da0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x146da0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x146da10: ldr             x4, [x4, #0xaa0]
    // 0x146da14: r0 = copyWith()
    //     0x146da14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x146da18: ldur            x2, [fp, #-0x28]
    // 0x146da1c: StoreField: r2->field_37 = r0
    //     0x146da1c: stur            w0, [x2, #0x37]
    //     0x146da20: ldurb           w16, [x2, #-1]
    //     0x146da24: ldurb           w17, [x0, #-1]
    //     0x146da28: and             x16, x17, x16, lsr #2
    //     0x146da2c: tst             x16, HEAP, lsr #32
    //     0x146da30: b.eq            #0x146da38
    //     0x146da34: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x146da38: r1 = Instance_Color
    //     0x146da38: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x146da3c: d0 = 0.400000
    //     0x146da3c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x146da40: r0 = withOpacity()
    //     0x146da40: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x146da44: r16 = 12.000000
    //     0x146da44: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x146da48: ldr             x16, [x16, #0x9e8]
    // 0x146da4c: stp             x0, x16, [SP, #8]
    // 0x146da50: r16 = Instance_TextDecoration
    //     0x146da50: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0x146da54: ldr             x16, [x16, #0xe30]
    // 0x146da58: str             x16, [SP]
    // 0x146da5c: ldur            x1, [fp, #-0x20]
    // 0x146da60: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0x146da60: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0x146da64: ldr             x4, [x4, #0xe38]
    // 0x146da68: r0 = copyWith()
    //     0x146da68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x146da6c: ldur            x1, [fp, #-0x28]
    // 0x146da70: StoreField: r1->field_3b = r0
    //     0x146da70: stur            w0, [x1, #0x3b]
    //     0x146da74: ldurb           w16, [x1, #-1]
    //     0x146da78: ldurb           w17, [x0, #-1]
    //     0x146da7c: and             x16, x17, x16, lsr #2
    //     0x146da80: tst             x16, HEAP, lsr #32
    //     0x146da84: b.eq            #0x146da8c
    //     0x146da88: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x146da8c: r0 = Null
    //     0x146da8c: mov             x0, NULL
    // 0x146da90: LeaveFrame
    //     0x146da90: mov             SP, fp
    //     0x146da94: ldp             fp, lr, [SP], #0x10
    // 0x146da98: ret
    //     0x146da98: ret             
    // 0x146da9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x146da9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x146daa0: b               #0x146d500
  }
}
