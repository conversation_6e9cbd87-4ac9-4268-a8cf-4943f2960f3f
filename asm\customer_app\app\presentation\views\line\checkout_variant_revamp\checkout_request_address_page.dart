// lib: , url: package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart

// class id: 1049476, size: 0x8
class :: {
}

// class id: 4545, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestAddressPage extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x12e5430, size: 0x50
    // 0x12e5430: EnterFrame
    //     0x12e5430: stp             fp, lr, [SP, #-0x10]!
    //     0x12e5434: mov             fp, SP
    // 0x12e5438: ldr             x0, [fp, #0x10]
    // 0x12e543c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x12e543c: ldur            w1, [x0, #0x17]
    // 0x12e5440: DecompressPointer r1
    //     0x12e5440: add             x1, x1, HEAP, lsl #32
    // 0x12e5444: CheckStackOverflow
    //     0x12e5444: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12e5448: cmp             SP, x16
    //     0x12e544c: b.ls            #0x12e5478
    // 0x12e5450: LoadField: r0 = r1->field_f
    //     0x12e5450: ldur            w0, [x1, #0xf]
    // 0x12e5454: DecompressPointer r0
    //     0x12e5454: add             x0, x0, HEAP, lsl #32
    // 0x12e5458: mov             x1, x0
    // 0x12e545c: r0 = controller()
    //     0x12e545c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e5460: mov             x1, x0
    // 0x12e5464: r0 = onContinueCtaClick()
    //     0x12e5464: bl              #0x12e5ab8  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::onContinueCtaClick
    // 0x12e5468: r0 = Null
    //     0x12e5468: mov             x0, NULL
    // 0x12e546c: LeaveFrame
    //     0x12e546c: mov             SP, fp
    //     0x12e5470: ldp             fp, lr, [SP], #0x10
    // 0x12e5474: ret
    //     0x12e5474: ret             
    // 0x12e5478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12e5478: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12e547c: b               #0x12e5450
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x12e5480, size: 0x638
    // 0x12e5480: EnterFrame
    //     0x12e5480: stp             fp, lr, [SP, #-0x10]!
    //     0x12e5484: mov             fp, SP
    // 0x12e5488: AllocStack(0x60)
    //     0x12e5488: sub             SP, SP, #0x60
    // 0x12e548c: SetupParameters()
    //     0x12e548c: ldr             x0, [fp, #0x10]
    //     0x12e5490: ldur            w2, [x0, #0x17]
    //     0x12e5494: add             x2, x2, HEAP, lsl #32
    //     0x12e5498: stur            x2, [fp, #-8]
    // 0x12e549c: CheckStackOverflow
    //     0x12e549c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12e54a0: cmp             SP, x16
    //     0x12e54a4: b.ls            #0x12e5a6c
    // 0x12e54a8: LoadField: r1 = r2->field_13
    //     0x12e54a8: ldur            w1, [x2, #0x13]
    // 0x12e54ac: DecompressPointer r1
    //     0x12e54ac: add             x1, x1, HEAP, lsl #32
    // 0x12e54b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x12e54b0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x12e54b4: r0 = _of()
    //     0x12e54b4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x12e54b8: LoadField: r1 = r0->field_23
    //     0x12e54b8: ldur            w1, [x0, #0x23]
    // 0x12e54bc: DecompressPointer r1
    //     0x12e54bc: add             x1, x1, HEAP, lsl #32
    // 0x12e54c0: LoadField: d0 = r1->field_1f
    //     0x12e54c0: ldur            d0, [x1, #0x1f]
    // 0x12e54c4: stur            d0, [fp, #-0x40]
    // 0x12e54c8: r0 = EdgeInsets()
    //     0x12e54c8: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x12e54cc: stur            x0, [fp, #-0x10]
    // 0x12e54d0: StoreField: r0->field_7 = rZR
    //     0x12e54d0: stur            xzr, [x0, #7]
    // 0x12e54d4: StoreField: r0->field_f = rZR
    //     0x12e54d4: stur            xzr, [x0, #0xf]
    // 0x12e54d8: ArrayStore: r0[0] = rZR  ; List_8
    //     0x12e54d8: stur            xzr, [x0, #0x17]
    // 0x12e54dc: ldur            d0, [fp, #-0x40]
    // 0x12e54e0: StoreField: r0->field_1f = d0
    //     0x12e54e0: stur            d0, [x0, #0x1f]
    // 0x12e54e4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x12e54e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12e54e8: ldr             x0, [x0, #0x1c80]
    //     0x12e54ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12e54f0: cmp             w0, w16
    //     0x12e54f4: b.ne            #0x12e5500
    //     0x12e54f8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x12e54fc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12e5500: r0 = GetNavigation.width()
    //     0x12e5500: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x12e5504: ldur            x2, [fp, #-8]
    // 0x12e5508: stur            d0, [fp, #-0x40]
    // 0x12e550c: LoadField: r1 = r2->field_13
    //     0x12e550c: ldur            w1, [x2, #0x13]
    // 0x12e5510: DecompressPointer r1
    //     0x12e5510: add             x1, x1, HEAP, lsl #32
    // 0x12e5514: r0 = of()
    //     0x12e5514: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12e5518: LoadField: r1 = r0->field_5b
    //     0x12e5518: ldur            w1, [x0, #0x5b]
    // 0x12e551c: DecompressPointer r1
    //     0x12e551c: add             x1, x1, HEAP, lsl #32
    // 0x12e5520: r0 = LoadClassIdInstr(r1)
    //     0x12e5520: ldur            x0, [x1, #-1]
    //     0x12e5524: ubfx            x0, x0, #0xc, #0x14
    // 0x12e5528: r2 = 40
    //     0x12e5528: movz            x2, #0x28
    // 0x12e552c: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x12e552c: sub             lr, x0, #0xfe7
    //     0x12e5530: ldr             lr, [x21, lr, lsl #3]
    //     0x12e5534: blr             lr
    // 0x12e5538: stur            x0, [fp, #-0x18]
    // 0x12e553c: r0 = BorderSide()
    //     0x12e553c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x12e5540: mov             x1, x0
    // 0x12e5544: ldur            x0, [fp, #-0x18]
    // 0x12e5548: stur            x1, [fp, #-0x20]
    // 0x12e554c: StoreField: r1->field_7 = r0
    //     0x12e554c: stur            w0, [x1, #7]
    // 0x12e5550: d0 = 2.000000
    //     0x12e5550: fmov            d0, #2.00000000
    // 0x12e5554: StoreField: r1->field_b = d0
    //     0x12e5554: stur            d0, [x1, #0xb]
    // 0x12e5558: r0 = Instance_BorderStyle
    //     0x12e5558: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x12e555c: ldr             x0, [x0, #0xf68]
    // 0x12e5560: StoreField: r1->field_13 = r0
    //     0x12e5560: stur            w0, [x1, #0x13]
    // 0x12e5564: d0 = -1.000000
    //     0x12e5564: fmov            d0, #-1.00000000
    // 0x12e5568: ArrayStore: r1[0] = d0  ; List_8
    //     0x12e5568: stur            d0, [x1, #0x17]
    // 0x12e556c: r0 = Border()
    //     0x12e556c: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0x12e5570: mov             x1, x0
    // 0x12e5574: ldur            x0, [fp, #-0x20]
    // 0x12e5578: stur            x1, [fp, #-0x18]
    // 0x12e557c: StoreField: r1->field_7 = r0
    //     0x12e557c: stur            w0, [x1, #7]
    // 0x12e5580: r0 = Instance_BorderSide
    //     0x12e5580: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x12e5584: ldr             x0, [x0, #0xe20]
    // 0x12e5588: StoreField: r1->field_b = r0
    //     0x12e5588: stur            w0, [x1, #0xb]
    // 0x12e558c: StoreField: r1->field_f = r0
    //     0x12e558c: stur            w0, [x1, #0xf]
    // 0x12e5590: StoreField: r1->field_13 = r0
    //     0x12e5590: stur            w0, [x1, #0x13]
    // 0x12e5594: r0 = BoxDecoration()
    //     0x12e5594: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x12e5598: mov             x2, x0
    // 0x12e559c: ldur            x0, [fp, #-0x18]
    // 0x12e55a0: stur            x2, [fp, #-0x20]
    // 0x12e55a4: StoreField: r2->field_f = r0
    //     0x12e55a4: stur            w0, [x2, #0xf]
    // 0x12e55a8: r0 = Instance_BoxShape
    //     0x12e55a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x12e55ac: ldr             x0, [x0, #0x80]
    // 0x12e55b0: StoreField: r2->field_23 = r0
    //     0x12e55b0: stur            w0, [x2, #0x23]
    // 0x12e55b4: ldur            x0, [fp, #-8]
    // 0x12e55b8: LoadField: r1 = r0->field_f
    //     0x12e55b8: ldur            w1, [x0, #0xf]
    // 0x12e55bc: DecompressPointer r1
    //     0x12e55bc: add             x1, x1, HEAP, lsl #32
    // 0x12e55c0: r0 = controller()
    //     0x12e55c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e55c4: LoadField: r1 = r0->field_9f
    //     0x12e55c4: ldur            w1, [x0, #0x9f]
    // 0x12e55c8: DecompressPointer r1
    //     0x12e55c8: add             x1, x1, HEAP, lsl #32
    // 0x12e55cc: r0 = value()
    //     0x12e55cc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12e55d0: tbnz            w0, #4, #0x12e55f4
    // 0x12e55d4: ldur            x2, [fp, #-8]
    // 0x12e55d8: LoadField: r1 = r2->field_13
    //     0x12e55d8: ldur            w1, [x2, #0x13]
    // 0x12e55dc: DecompressPointer r1
    //     0x12e55dc: add             x1, x1, HEAP, lsl #32
    // 0x12e55e0: r0 = of()
    //     0x12e55e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12e55e4: LoadField: r1 = r0->field_5b
    //     0x12e55e4: ldur            w1, [x0, #0x5b]
    // 0x12e55e8: DecompressPointer r1
    //     0x12e55e8: add             x1, x1, HEAP, lsl #32
    // 0x12e55ec: mov             x0, x1
    // 0x12e55f0: b               #0x12e55fc
    // 0x12e55f4: r0 = Instance_MaterialColor
    //     0x12e55f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x12e55f8: ldr             x0, [x0, #0xdc0]
    // 0x12e55fc: ldur            x2, [fp, #-8]
    // 0x12e5600: r16 = <Color>
    //     0x12e5600: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x12e5604: ldr             x16, [x16, #0xf80]
    // 0x12e5608: stp             x0, x16, [SP]
    // 0x12e560c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12e560c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12e5610: r0 = all()
    //     0x12e5610: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12e5614: stur            x0, [fp, #-0x18]
    // 0x12e5618: r16 = <RoundedRectangleBorder>
    //     0x12e5618: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x12e561c: ldr             x16, [x16, #0xf78]
    // 0x12e5620: r30 = Instance_RoundedRectangleBorder
    //     0x12e5620: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0x12e5624: ldr             lr, [lr, #0xd68]
    // 0x12e5628: stp             lr, x16, [SP]
    // 0x12e562c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12e562c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12e5630: r0 = all()
    //     0x12e5630: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12e5634: stur            x0, [fp, #-0x28]
    // 0x12e5638: r0 = ButtonStyle()
    //     0x12e5638: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x12e563c: mov             x1, x0
    // 0x12e5640: ldur            x0, [fp, #-0x18]
    // 0x12e5644: stur            x1, [fp, #-0x30]
    // 0x12e5648: StoreField: r1->field_b = r0
    //     0x12e5648: stur            w0, [x1, #0xb]
    // 0x12e564c: ldur            x0, [fp, #-0x28]
    // 0x12e5650: StoreField: r1->field_43 = r0
    //     0x12e5650: stur            w0, [x1, #0x43]
    // 0x12e5654: r0 = TextButtonThemeData()
    //     0x12e5654: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x12e5658: mov             x2, x0
    // 0x12e565c: ldur            x0, [fp, #-0x30]
    // 0x12e5660: stur            x2, [fp, #-0x18]
    // 0x12e5664: StoreField: r2->field_7 = r0
    //     0x12e5664: stur            w0, [x2, #7]
    // 0x12e5668: ldur            x0, [fp, #-8]
    // 0x12e566c: LoadField: r1 = r0->field_f
    //     0x12e566c: ldur            w1, [x0, #0xf]
    // 0x12e5670: DecompressPointer r1
    //     0x12e5670: add             x1, x1, HEAP, lsl #32
    // 0x12e5674: r0 = controller()
    //     0x12e5674: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12e5678: LoadField: r1 = r0->field_9f
    //     0x12e5678: ldur            w1, [x0, #0x9f]
    // 0x12e567c: DecompressPointer r1
    //     0x12e567c: add             x1, x1, HEAP, lsl #32
    // 0x12e5680: r0 = value()
    //     0x12e5680: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12e5684: tbnz            w0, #4, #0x12e56a0
    // 0x12e5688: ldur            x2, [fp, #-8]
    // 0x12e568c: r1 = Function '<anonymous closure>':.
    //     0x12e568c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbd0] AnonymousClosure: (0x12e5430), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::bottomNavigationBar (0x1365a7c)
    //     0x12e5690: ldr             x1, [x1, #0xbd0]
    // 0x12e5694: r0 = AllocateClosure()
    //     0x12e5694: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12e5698: mov             x4, x0
    // 0x12e569c: b               #0x12e56a4
    // 0x12e56a0: r4 = Null
    //     0x12e56a0: mov             x4, NULL
    // 0x12e56a4: ldur            x2, [fp, #-8]
    // 0x12e56a8: ldur            x3, [fp, #-0x10]
    // 0x12e56ac: ldur            d0, [fp, #-0x40]
    // 0x12e56b0: ldur            x0, [fp, #-0x18]
    // 0x12e56b4: stur            x4, [fp, #-0x28]
    // 0x12e56b8: LoadField: r1 = r2->field_13
    //     0x12e56b8: ldur            w1, [x2, #0x13]
    // 0x12e56bc: DecompressPointer r1
    //     0x12e56bc: add             x1, x1, HEAP, lsl #32
    // 0x12e56c0: r0 = of()
    //     0x12e56c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12e56c4: LoadField: r1 = r0->field_87
    //     0x12e56c4: ldur            w1, [x0, #0x87]
    // 0x12e56c8: DecompressPointer r1
    //     0x12e56c8: add             x1, x1, HEAP, lsl #32
    // 0x12e56cc: LoadField: r0 = r1->field_7
    //     0x12e56cc: ldur            w0, [x1, #7]
    // 0x12e56d0: DecompressPointer r0
    //     0x12e56d0: add             x0, x0, HEAP, lsl #32
    // 0x12e56d4: r16 = 14.000000
    //     0x12e56d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x12e56d8: ldr             x16, [x16, #0x1d8]
    // 0x12e56dc: r30 = Instance_Color
    //     0x12e56dc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12e56e0: stp             lr, x16, [SP]
    // 0x12e56e4: mov             x1, x0
    // 0x12e56e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12e56e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12e56ec: ldr             x4, [x4, #0xaa0]
    // 0x12e56f0: r0 = copyWith()
    //     0x12e56f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12e56f4: stur            x0, [fp, #-0x30]
    // 0x12e56f8: r0 = Text()
    //     0x12e56f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12e56fc: mov             x1, x0
    // 0x12e5700: r0 = "CONTINUE"
    //     0x12e5700: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0x12e5704: ldr             x0, [x0, #0xac8]
    // 0x12e5708: stur            x1, [fp, #-0x38]
    // 0x12e570c: StoreField: r1->field_b = r0
    //     0x12e570c: stur            w0, [x1, #0xb]
    // 0x12e5710: ldur            x0, [fp, #-0x30]
    // 0x12e5714: StoreField: r1->field_13 = r0
    //     0x12e5714: stur            w0, [x1, #0x13]
    // 0x12e5718: r0 = TextButton()
    //     0x12e5718: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x12e571c: mov             x1, x0
    // 0x12e5720: ldur            x0, [fp, #-0x28]
    // 0x12e5724: stur            x1, [fp, #-0x30]
    // 0x12e5728: StoreField: r1->field_b = r0
    //     0x12e5728: stur            w0, [x1, #0xb]
    // 0x12e572c: r0 = false
    //     0x12e572c: add             x0, NULL, #0x30  ; false
    // 0x12e5730: StoreField: r1->field_27 = r0
    //     0x12e5730: stur            w0, [x1, #0x27]
    // 0x12e5734: r2 = true
    //     0x12e5734: add             x2, NULL, #0x20  ; true
    // 0x12e5738: StoreField: r1->field_2f = r2
    //     0x12e5738: stur            w2, [x1, #0x2f]
    // 0x12e573c: ldur            x3, [fp, #-0x38]
    // 0x12e5740: StoreField: r1->field_37 = r3
    //     0x12e5740: stur            w3, [x1, #0x37]
    // 0x12e5744: r0 = TextButtonTheme()
    //     0x12e5744: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x12e5748: mov             x1, x0
    // 0x12e574c: ldur            x0, [fp, #-0x18]
    // 0x12e5750: stur            x1, [fp, #-0x28]
    // 0x12e5754: StoreField: r1->field_f = r0
    //     0x12e5754: stur            w0, [x1, #0xf]
    // 0x12e5758: ldur            x0, [fp, #-0x30]
    // 0x12e575c: StoreField: r1->field_b = r0
    //     0x12e575c: stur            w0, [x1, #0xb]
    // 0x12e5760: ldur            d0, [fp, #-0x40]
    // 0x12e5764: r0 = inline_Allocate_Double()
    //     0x12e5764: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x12e5768: add             x0, x0, #0x10
    //     0x12e576c: cmp             x2, x0
    //     0x12e5770: b.ls            #0x12e5a74
    //     0x12e5774: str             x0, [THR, #0x50]  ; THR::top
    //     0x12e5778: sub             x0, x0, #0xf
    //     0x12e577c: movz            x2, #0xe15c
    //     0x12e5780: movk            x2, #0x3, lsl #16
    //     0x12e5784: stur            x2, [x0, #-1]
    // 0x12e5788: StoreField: r0->field_7 = d0
    //     0x12e5788: stur            d0, [x0, #7]
    // 0x12e578c: stur            x0, [fp, #-0x18]
    // 0x12e5790: r0 = Container()
    //     0x12e5790: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12e5794: stur            x0, [fp, #-0x30]
    // 0x12e5798: r16 = Instance_EdgeInsets
    //     0x12e5798: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x12e579c: ldr             x16, [x16, #0xe48]
    // 0x12e57a0: ldur            lr, [fp, #-0x18]
    // 0x12e57a4: stp             lr, x16, [SP, #0x10]
    // 0x12e57a8: ldur            x16, [fp, #-0x20]
    // 0x12e57ac: ldur            lr, [fp, #-0x28]
    // 0x12e57b0: stp             lr, x16, [SP]
    // 0x12e57b4: mov             x1, x0
    // 0x12e57b8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x12e57b8: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x12e57bc: ldr             x4, [x4, #0x18]
    // 0x12e57c0: r0 = Container()
    //     0x12e57c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12e57c4: r0 = GetNavigation.size()
    //     0x12e57c4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x12e57c8: LoadField: d0 = r0->field_7
    //     0x12e57c8: ldur            d0, [x0, #7]
    // 0x12e57cc: ldur            x0, [fp, #-8]
    // 0x12e57d0: stur            d0, [fp, #-0x40]
    // 0x12e57d4: LoadField: r1 = r0->field_13
    //     0x12e57d4: ldur            w1, [x0, #0x13]
    // 0x12e57d8: DecompressPointer r1
    //     0x12e57d8: add             x1, x1, HEAP, lsl #32
    // 0x12e57dc: r0 = of()
    //     0x12e57dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12e57e0: LoadField: r1 = r0->field_87
    //     0x12e57e0: ldur            w1, [x0, #0x87]
    // 0x12e57e4: DecompressPointer r1
    //     0x12e57e4: add             x1, x1, HEAP, lsl #32
    // 0x12e57e8: LoadField: r0 = r1->field_2b
    //     0x12e57e8: ldur            w0, [x1, #0x2b]
    // 0x12e57ec: DecompressPointer r0
    //     0x12e57ec: add             x0, x0, HEAP, lsl #32
    // 0x12e57f0: stur            x0, [fp, #-8]
    // 0x12e57f4: r1 = Instance_Color
    //     0x12e57f4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12e57f8: d0 = 0.700000
    //     0x12e57f8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x12e57fc: ldr             d0, [x17, #0xf48]
    // 0x12e5800: r0 = withOpacity()
    //     0x12e5800: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12e5804: r16 = 10.000000
    //     0x12e5804: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x12e5808: stp             x0, x16, [SP]
    // 0x12e580c: ldur            x1, [fp, #-8]
    // 0x12e5810: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12e5810: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12e5814: ldr             x4, [x4, #0xaa0]
    // 0x12e5818: r0 = copyWith()
    //     0x12e5818: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12e581c: stur            x0, [fp, #-8]
    // 0x12e5820: r0 = Text()
    //     0x12e5820: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12e5824: mov             x1, x0
    // 0x12e5828: r0 = "Powered By"
    //     0x12e5828: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x12e582c: ldr             x0, [x0, #0x750]
    // 0x12e5830: stur            x1, [fp, #-0x18]
    // 0x12e5834: StoreField: r1->field_b = r0
    //     0x12e5834: stur            w0, [x1, #0xb]
    // 0x12e5838: ldur            x0, [fp, #-8]
    // 0x12e583c: StoreField: r1->field_13 = r0
    //     0x12e583c: stur            w0, [x1, #0x13]
    // 0x12e5840: r0 = SvgPicture()
    //     0x12e5840: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x12e5844: stur            x0, [fp, #-8]
    // 0x12e5848: r16 = 20.000000
    //     0x12e5848: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x12e584c: ldr             x16, [x16, #0xac8]
    // 0x12e5850: str             x16, [SP]
    // 0x12e5854: mov             x1, x0
    // 0x12e5858: r2 = "assets/images/shopdeck.svg"
    //     0x12e5858: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x12e585c: ldr             x2, [x2, #0x758]
    // 0x12e5860: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x12e5860: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x12e5864: ldr             x4, [x4, #0x760]
    // 0x12e5868: r0 = SvgPicture.asset()
    //     0x12e5868: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x12e586c: r1 = Null
    //     0x12e586c: mov             x1, NULL
    // 0x12e5870: r2 = 4
    //     0x12e5870: movz            x2, #0x4
    // 0x12e5874: r0 = AllocateArray()
    //     0x12e5874: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12e5878: mov             x2, x0
    // 0x12e587c: ldur            x0, [fp, #-0x18]
    // 0x12e5880: stur            x2, [fp, #-0x20]
    // 0x12e5884: StoreField: r2->field_f = r0
    //     0x12e5884: stur            w0, [x2, #0xf]
    // 0x12e5888: ldur            x0, [fp, #-8]
    // 0x12e588c: StoreField: r2->field_13 = r0
    //     0x12e588c: stur            w0, [x2, #0x13]
    // 0x12e5890: r1 = <Widget>
    //     0x12e5890: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12e5894: r0 = AllocateGrowableArray()
    //     0x12e5894: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12e5898: mov             x1, x0
    // 0x12e589c: ldur            x0, [fp, #-0x20]
    // 0x12e58a0: stur            x1, [fp, #-8]
    // 0x12e58a4: StoreField: r1->field_f = r0
    //     0x12e58a4: stur            w0, [x1, #0xf]
    // 0x12e58a8: r2 = 4
    //     0x12e58a8: movz            x2, #0x4
    // 0x12e58ac: StoreField: r1->field_b = r2
    //     0x12e58ac: stur            w2, [x1, #0xb]
    // 0x12e58b0: r0 = Row()
    //     0x12e58b0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12e58b4: mov             x1, x0
    // 0x12e58b8: r0 = Instance_Axis
    //     0x12e58b8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12e58bc: stur            x1, [fp, #-0x18]
    // 0x12e58c0: StoreField: r1->field_f = r0
    //     0x12e58c0: stur            w0, [x1, #0xf]
    // 0x12e58c4: r0 = Instance_MainAxisAlignment
    //     0x12e58c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12e58c8: ldr             x0, [x0, #0xa08]
    // 0x12e58cc: StoreField: r1->field_13 = r0
    //     0x12e58cc: stur            w0, [x1, #0x13]
    // 0x12e58d0: r2 = Instance_MainAxisSize
    //     0x12e58d0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x12e58d4: ldr             x2, [x2, #0xdd0]
    // 0x12e58d8: ArrayStore: r1[0] = r2  ; List_4
    //     0x12e58d8: stur            w2, [x1, #0x17]
    // 0x12e58dc: r3 = Instance_CrossAxisAlignment
    //     0x12e58dc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12e58e0: ldr             x3, [x3, #0xa18]
    // 0x12e58e4: StoreField: r1->field_1b = r3
    //     0x12e58e4: stur            w3, [x1, #0x1b]
    // 0x12e58e8: r4 = Instance_VerticalDirection
    //     0x12e58e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12e58ec: ldr             x4, [x4, #0xa20]
    // 0x12e58f0: StoreField: r1->field_23 = r4
    //     0x12e58f0: stur            w4, [x1, #0x23]
    // 0x12e58f4: r5 = Instance_Clip
    //     0x12e58f4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12e58f8: ldr             x5, [x5, #0x38]
    // 0x12e58fc: StoreField: r1->field_2b = r5
    //     0x12e58fc: stur            w5, [x1, #0x2b]
    // 0x12e5900: StoreField: r1->field_2f = rZR
    //     0x12e5900: stur            xzr, [x1, #0x2f]
    // 0x12e5904: ldur            x6, [fp, #-8]
    // 0x12e5908: StoreField: r1->field_b = r6
    //     0x12e5908: stur            w6, [x1, #0xb]
    // 0x12e590c: ldur            d0, [fp, #-0x40]
    // 0x12e5910: r6 = inline_Allocate_Double()
    //     0x12e5910: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x12e5914: add             x6, x6, #0x10
    //     0x12e5918: cmp             x7, x6
    //     0x12e591c: b.ls            #0x12e5a8c
    //     0x12e5920: str             x6, [THR, #0x50]  ; THR::top
    //     0x12e5924: sub             x6, x6, #0xf
    //     0x12e5928: movz            x7, #0xe15c
    //     0x12e592c: movk            x7, #0x3, lsl #16
    //     0x12e5930: stur            x7, [x6, #-1]
    // 0x12e5934: StoreField: r6->field_7 = d0
    //     0x12e5934: stur            d0, [x6, #7]
    // 0x12e5938: stur            x6, [fp, #-8]
    // 0x12e593c: r0 = Container()
    //     0x12e593c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12e5940: stur            x0, [fp, #-0x20]
    // 0x12e5944: r16 = Instance_Alignment
    //     0x12e5944: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x12e5948: ldr             x16, [x16, #0xb10]
    // 0x12e594c: ldur            lr, [fp, #-8]
    // 0x12e5950: stp             lr, x16, [SP, #0x10]
    // 0x12e5954: r16 = Instance_BoxDecoration
    //     0x12e5954: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x12e5958: ldr             x16, [x16, #0x768]
    // 0x12e595c: ldur            lr, [fp, #-0x18]
    // 0x12e5960: stp             lr, x16, [SP]
    // 0x12e5964: mov             x1, x0
    // 0x12e5968: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x12e5968: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x12e596c: ldr             x4, [x4, #0x770]
    // 0x12e5970: r0 = Container()
    //     0x12e5970: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12e5974: r1 = Null
    //     0x12e5974: mov             x1, NULL
    // 0x12e5978: r2 = 4
    //     0x12e5978: movz            x2, #0x4
    // 0x12e597c: r0 = AllocateArray()
    //     0x12e597c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12e5980: mov             x2, x0
    // 0x12e5984: ldur            x0, [fp, #-0x30]
    // 0x12e5988: stur            x2, [fp, #-8]
    // 0x12e598c: StoreField: r2->field_f = r0
    //     0x12e598c: stur            w0, [x2, #0xf]
    // 0x12e5990: ldur            x0, [fp, #-0x20]
    // 0x12e5994: StoreField: r2->field_13 = r0
    //     0x12e5994: stur            w0, [x2, #0x13]
    // 0x12e5998: r1 = <Widget>
    //     0x12e5998: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12e599c: r0 = AllocateGrowableArray()
    //     0x12e599c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12e59a0: mov             x1, x0
    // 0x12e59a4: ldur            x0, [fp, #-8]
    // 0x12e59a8: stur            x1, [fp, #-0x18]
    // 0x12e59ac: StoreField: r1->field_f = r0
    //     0x12e59ac: stur            w0, [x1, #0xf]
    // 0x12e59b0: r0 = 4
    //     0x12e59b0: movz            x0, #0x4
    // 0x12e59b4: StoreField: r1->field_b = r0
    //     0x12e59b4: stur            w0, [x1, #0xb]
    // 0x12e59b8: r0 = Column()
    //     0x12e59b8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x12e59bc: mov             x1, x0
    // 0x12e59c0: r0 = Instance_Axis
    //     0x12e59c0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x12e59c4: stur            x1, [fp, #-8]
    // 0x12e59c8: StoreField: r1->field_f = r0
    //     0x12e59c8: stur            w0, [x1, #0xf]
    // 0x12e59cc: r0 = Instance_MainAxisAlignment
    //     0x12e59cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12e59d0: ldr             x0, [x0, #0xa08]
    // 0x12e59d4: StoreField: r1->field_13 = r0
    //     0x12e59d4: stur            w0, [x1, #0x13]
    // 0x12e59d8: r0 = Instance_MainAxisSize
    //     0x12e59d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x12e59dc: ldr             x0, [x0, #0xdd0]
    // 0x12e59e0: ArrayStore: r1[0] = r0  ; List_4
    //     0x12e59e0: stur            w0, [x1, #0x17]
    // 0x12e59e4: r0 = Instance_CrossAxisAlignment
    //     0x12e59e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12e59e8: ldr             x0, [x0, #0xa18]
    // 0x12e59ec: StoreField: r1->field_1b = r0
    //     0x12e59ec: stur            w0, [x1, #0x1b]
    // 0x12e59f0: r0 = Instance_VerticalDirection
    //     0x12e59f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12e59f4: ldr             x0, [x0, #0xa20]
    // 0x12e59f8: StoreField: r1->field_23 = r0
    //     0x12e59f8: stur            w0, [x1, #0x23]
    // 0x12e59fc: r0 = Instance_Clip
    //     0x12e59fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12e5a00: ldr             x0, [x0, #0x38]
    // 0x12e5a04: StoreField: r1->field_2b = r0
    //     0x12e5a04: stur            w0, [x1, #0x2b]
    // 0x12e5a08: StoreField: r1->field_2f = rZR
    //     0x12e5a08: stur            xzr, [x1, #0x2f]
    // 0x12e5a0c: ldur            x0, [fp, #-0x18]
    // 0x12e5a10: StoreField: r1->field_b = r0
    //     0x12e5a10: stur            w0, [x1, #0xb]
    // 0x12e5a14: r0 = Padding()
    //     0x12e5a14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12e5a18: mov             x1, x0
    // 0x12e5a1c: ldur            x0, [fp, #-0x10]
    // 0x12e5a20: stur            x1, [fp, #-0x18]
    // 0x12e5a24: StoreField: r1->field_f = r0
    //     0x12e5a24: stur            w0, [x1, #0xf]
    // 0x12e5a28: ldur            x0, [fp, #-8]
    // 0x12e5a2c: StoreField: r1->field_b = r0
    //     0x12e5a2c: stur            w0, [x1, #0xb]
    // 0x12e5a30: r0 = SafeArea()
    //     0x12e5a30: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x12e5a34: r1 = true
    //     0x12e5a34: add             x1, NULL, #0x20  ; true
    // 0x12e5a38: StoreField: r0->field_b = r1
    //     0x12e5a38: stur            w1, [x0, #0xb]
    // 0x12e5a3c: StoreField: r0->field_f = r1
    //     0x12e5a3c: stur            w1, [x0, #0xf]
    // 0x12e5a40: StoreField: r0->field_13 = r1
    //     0x12e5a40: stur            w1, [x0, #0x13]
    // 0x12e5a44: ArrayStore: r0[0] = r1  ; List_4
    //     0x12e5a44: stur            w1, [x0, #0x17]
    // 0x12e5a48: r1 = Instance_EdgeInsets
    //     0x12e5a48: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x12e5a4c: StoreField: r0->field_1b = r1
    //     0x12e5a4c: stur            w1, [x0, #0x1b]
    // 0x12e5a50: r1 = false
    //     0x12e5a50: add             x1, NULL, #0x30  ; false
    // 0x12e5a54: StoreField: r0->field_1f = r1
    //     0x12e5a54: stur            w1, [x0, #0x1f]
    // 0x12e5a58: ldur            x1, [fp, #-0x18]
    // 0x12e5a5c: StoreField: r0->field_23 = r1
    //     0x12e5a5c: stur            w1, [x0, #0x23]
    // 0x12e5a60: LeaveFrame
    //     0x12e5a60: mov             SP, fp
    //     0x12e5a64: ldp             fp, lr, [SP], #0x10
    // 0x12e5a68: ret
    //     0x12e5a68: ret             
    // 0x12e5a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12e5a6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12e5a70: b               #0x12e54a8
    // 0x12e5a74: SaveReg d0
    //     0x12e5a74: str             q0, [SP, #-0x10]!
    // 0x12e5a78: SaveReg r1
    //     0x12e5a78: str             x1, [SP, #-8]!
    // 0x12e5a7c: r0 = AllocateDouble()
    //     0x12e5a7c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12e5a80: RestoreReg r1
    //     0x12e5a80: ldr             x1, [SP], #8
    // 0x12e5a84: RestoreReg d0
    //     0x12e5a84: ldr             q0, [SP], #0x10
    // 0x12e5a88: b               #0x12e5788
    // 0x12e5a8c: SaveReg d0
    //     0x12e5a8c: str             q0, [SP, #-0x10]!
    // 0x12e5a90: stp             x4, x5, [SP, #-0x10]!
    // 0x12e5a94: stp             x2, x3, [SP, #-0x10]!
    // 0x12e5a98: stp             x0, x1, [SP, #-0x10]!
    // 0x12e5a9c: r0 = AllocateDouble()
    //     0x12e5a9c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12e5aa0: mov             x6, x0
    // 0x12e5aa4: ldp             x0, x1, [SP], #0x10
    // 0x12e5aa8: ldp             x2, x3, [SP], #0x10
    // 0x12e5aac: ldp             x4, x5, [SP], #0x10
    // 0x12e5ab0: RestoreReg d0
    //     0x12e5ab0: ldr             q0, [SP], #0x10
    // 0x12e5ab4: b               #0x12e5934
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1365a7c, size: 0x64
    // 0x1365a7c: EnterFrame
    //     0x1365a7c: stp             fp, lr, [SP, #-0x10]!
    //     0x1365a80: mov             fp, SP
    // 0x1365a84: AllocStack(0x18)
    //     0x1365a84: sub             SP, SP, #0x18
    // 0x1365a88: SetupParameters(CheckoutRequestAddressPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1365a88: stur            x1, [fp, #-8]
    //     0x1365a8c: stur            x2, [fp, #-0x10]
    // 0x1365a90: r1 = 2
    //     0x1365a90: movz            x1, #0x2
    // 0x1365a94: r0 = AllocateContext()
    //     0x1365a94: bl              #0x16f6108  ; AllocateContextStub
    // 0x1365a98: mov             x1, x0
    // 0x1365a9c: ldur            x0, [fp, #-8]
    // 0x1365aa0: stur            x1, [fp, #-0x18]
    // 0x1365aa4: StoreField: r1->field_f = r0
    //     0x1365aa4: stur            w0, [x1, #0xf]
    // 0x1365aa8: ldur            x0, [fp, #-0x10]
    // 0x1365aac: StoreField: r1->field_13 = r0
    //     0x1365aac: stur            w0, [x1, #0x13]
    // 0x1365ab0: r0 = Obx()
    //     0x1365ab0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1365ab4: ldur            x2, [fp, #-0x18]
    // 0x1365ab8: r1 = Function '<anonymous closure>':.
    //     0x1365ab8: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbc8] AnonymousClosure: (0x12e5480), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::bottomNavigationBar (0x1365a7c)
    //     0x1365abc: ldr             x1, [x1, #0xbc8]
    // 0x1365ac0: stur            x0, [fp, #-8]
    // 0x1365ac4: r0 = AllocateClosure()
    //     0x1365ac4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1365ac8: mov             x1, x0
    // 0x1365acc: ldur            x0, [fp, #-8]
    // 0x1365ad0: StoreField: r0->field_b = r1
    //     0x1365ad0: stur            w1, [x0, #0xb]
    // 0x1365ad4: LeaveFrame
    //     0x1365ad4: mov             SP, fp
    //     0x1365ad8: ldp             fp, lr, [SP], #0x10
    // 0x1365adc: ret
    //     0x1365adc: ret             
  }
  [closure] bool <anonymous closure>(dynamic, Route<dynamic>) {
    // ** addr: 0x138f78c, size: 0x1e4
    // 0x138f78c: EnterFrame
    //     0x138f78c: stp             fp, lr, [SP, #-0x10]!
    //     0x138f790: mov             fp, SP
    // 0x138f794: AllocStack(0x10)
    //     0x138f794: sub             SP, SP, #0x10
    // 0x138f798: CheckStackOverflow
    //     0x138f798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x138f79c: cmp             SP, x16
    //     0x138f7a0: b.ls            #0x138f968
    // 0x138f7a4: ldr             x1, [fp, #0x10]
    // 0x138f7a8: LoadField: r0 = r1->field_13
    //     0x138f7a8: ldur            w0, [x1, #0x13]
    // 0x138f7ac: DecompressPointer r0
    //     0x138f7ac: add             x0, x0, HEAP, lsl #32
    // 0x138f7b0: r2 = LoadClassIdInstr(r0)
    //     0x138f7b0: ldur            x2, [x0, #-1]
    //     0x138f7b4: ubfx            x2, x2, #0xc, #0x14
    // 0x138f7b8: cmp             x2, #0x62e
    // 0x138f7bc: b.ne            #0x138f7d0
    // 0x138f7c0: LoadField: r2 = r0->field_7
    //     0x138f7c0: ldur            w2, [x0, #7]
    // 0x138f7c4: DecompressPointer r2
    //     0x138f7c4: add             x2, x2, HEAP, lsl #32
    // 0x138f7c8: mov             x0, x2
    // 0x138f7cc: b               #0x138f7dc
    // 0x138f7d0: LoadField: r2 = r0->field_6b
    //     0x138f7d0: ldur            w2, [x0, #0x6b]
    // 0x138f7d4: DecompressPointer r2
    //     0x138f7d4: add             x2, x2, HEAP, lsl #32
    // 0x138f7d8: mov             x0, x2
    // 0x138f7dc: r2 = LoadClassIdInstr(r0)
    //     0x138f7dc: ldur            x2, [x0, #-1]
    //     0x138f7e0: ubfx            x2, x2, #0xc, #0x14
    // 0x138f7e4: r16 = "/checkout_request_number_page"
    //     0x138f7e4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0x138f7e8: ldr             x16, [x16, #0x9f8]
    // 0x138f7ec: stp             x16, x0, [SP]
    // 0x138f7f0: mov             x0, x2
    // 0x138f7f4: mov             lr, x0
    // 0x138f7f8: ldr             lr, [x21, lr, lsl #3]
    // 0x138f7fc: blr             lr
    // 0x138f800: tbnz            w0, #4, #0x138f814
    // 0x138f804: r0 = true
    //     0x138f804: add             x0, NULL, #0x20  ; true
    // 0x138f808: LeaveFrame
    //     0x138f808: mov             SP, fp
    //     0x138f80c: ldp             fp, lr, [SP], #0x10
    // 0x138f810: ret
    //     0x138f810: ret             
    // 0x138f814: ldr             x1, [fp, #0x10]
    // 0x138f818: LoadField: r0 = r1->field_13
    //     0x138f818: ldur            w0, [x1, #0x13]
    // 0x138f81c: DecompressPointer r0
    //     0x138f81c: add             x0, x0, HEAP, lsl #32
    // 0x138f820: r2 = LoadClassIdInstr(r0)
    //     0x138f820: ldur            x2, [x0, #-1]
    //     0x138f824: ubfx            x2, x2, #0xc, #0x14
    // 0x138f828: cmp             x2, #0x62e
    // 0x138f82c: b.ne            #0x138f840
    // 0x138f830: LoadField: r2 = r0->field_7
    //     0x138f830: ldur            w2, [x0, #7]
    // 0x138f834: DecompressPointer r2
    //     0x138f834: add             x2, x2, HEAP, lsl #32
    // 0x138f838: mov             x0, x2
    // 0x138f83c: b               #0x138f84c
    // 0x138f840: LoadField: r2 = r0->field_6b
    //     0x138f840: ldur            w2, [x0, #0x6b]
    // 0x138f844: DecompressPointer r2
    //     0x138f844: add             x2, x2, HEAP, lsl #32
    // 0x138f848: mov             x0, x2
    // 0x138f84c: r2 = LoadClassIdInstr(r0)
    //     0x138f84c: ldur            x2, [x0, #-1]
    //     0x138f850: ubfx            x2, x2, #0xc, #0x14
    // 0x138f854: r16 = "/bag"
    //     0x138f854: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x138f858: ldr             x16, [x16, #0x468]
    // 0x138f85c: stp             x16, x0, [SP]
    // 0x138f860: mov             x0, x2
    // 0x138f864: mov             lr, x0
    // 0x138f868: ldr             lr, [x21, lr, lsl #3]
    // 0x138f86c: blr             lr
    // 0x138f870: tbnz            w0, #4, #0x138f884
    // 0x138f874: r0 = true
    //     0x138f874: add             x0, NULL, #0x20  ; true
    // 0x138f878: LeaveFrame
    //     0x138f878: mov             SP, fp
    //     0x138f87c: ldp             fp, lr, [SP], #0x10
    // 0x138f880: ret
    //     0x138f880: ret             
    // 0x138f884: ldr             x1, [fp, #0x10]
    // 0x138f888: LoadField: r0 = r1->field_13
    //     0x138f888: ldur            w0, [x1, #0x13]
    // 0x138f88c: DecompressPointer r0
    //     0x138f88c: add             x0, x0, HEAP, lsl #32
    // 0x138f890: r2 = LoadClassIdInstr(r0)
    //     0x138f890: ldur            x2, [x0, #-1]
    //     0x138f894: ubfx            x2, x2, #0xc, #0x14
    // 0x138f898: cmp             x2, #0x62e
    // 0x138f89c: b.ne            #0x138f8b0
    // 0x138f8a0: LoadField: r2 = r0->field_7
    //     0x138f8a0: ldur            w2, [x0, #7]
    // 0x138f8a4: DecompressPointer r2
    //     0x138f8a4: add             x2, x2, HEAP, lsl #32
    // 0x138f8a8: mov             x0, x2
    // 0x138f8ac: b               #0x138f8bc
    // 0x138f8b0: LoadField: r2 = r0->field_6b
    //     0x138f8b0: ldur            w2, [x0, #0x6b]
    // 0x138f8b4: DecompressPointer r2
    //     0x138f8b4: add             x2, x2, HEAP, lsl #32
    // 0x138f8b8: mov             x0, x2
    // 0x138f8bc: r2 = LoadClassIdInstr(r0)
    //     0x138f8bc: ldur            x2, [x0, #-1]
    //     0x138f8c0: ubfx            x2, x2, #0xc, #0x14
    // 0x138f8c4: r16 = "/product-detail"
    //     0x138f8c4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x138f8c8: ldr             x16, [x16, #0x4a8]
    // 0x138f8cc: stp             x16, x0, [SP]
    // 0x138f8d0: mov             x0, x2
    // 0x138f8d4: mov             lr, x0
    // 0x138f8d8: ldr             lr, [x21, lr, lsl #3]
    // 0x138f8dc: blr             lr
    // 0x138f8e0: tbnz            w0, #4, #0x138f8f4
    // 0x138f8e4: r0 = true
    //     0x138f8e4: add             x0, NULL, #0x20  ; true
    // 0x138f8e8: LeaveFrame
    //     0x138f8e8: mov             SP, fp
    //     0x138f8ec: ldp             fp, lr, [SP], #0x10
    // 0x138f8f0: ret
    //     0x138f8f0: ret             
    // 0x138f8f4: ldr             x0, [fp, #0x10]
    // 0x138f8f8: LoadField: r1 = r0->field_13
    //     0x138f8f8: ldur            w1, [x0, #0x13]
    // 0x138f8fc: DecompressPointer r1
    //     0x138f8fc: add             x1, x1, HEAP, lsl #32
    // 0x138f900: r0 = LoadClassIdInstr(r1)
    //     0x138f900: ldur            x0, [x1, #-1]
    //     0x138f904: ubfx            x0, x0, #0xc, #0x14
    // 0x138f908: cmp             x0, #0x62e
    // 0x138f90c: b.ne            #0x138f91c
    // 0x138f910: LoadField: r0 = r1->field_7
    //     0x138f910: ldur            w0, [x1, #7]
    // 0x138f914: DecompressPointer r0
    //     0x138f914: add             x0, x0, HEAP, lsl #32
    // 0x138f918: b               #0x138f924
    // 0x138f91c: LoadField: r0 = r1->field_6b
    //     0x138f91c: ldur            w0, [x1, #0x6b]
    // 0x138f920: DecompressPointer r0
    //     0x138f920: add             x0, x0, HEAP, lsl #32
    // 0x138f924: r1 = LoadClassIdInstr(r0)
    //     0x138f924: ldur            x1, [x0, #-1]
    //     0x138f928: ubfx            x1, x1, #0xc, #0x14
    // 0x138f92c: r16 = "/"
    //     0x138f92c: ldr             x16, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x138f930: stp             x16, x0, [SP]
    // 0x138f934: mov             x0, x1
    // 0x138f938: mov             lr, x0
    // 0x138f93c: ldr             lr, [x21, lr, lsl #3]
    // 0x138f940: blr             lr
    // 0x138f944: tbnz            w0, #4, #0x138f958
    // 0x138f948: r0 = true
    //     0x138f948: add             x0, NULL, #0x20  ; true
    // 0x138f94c: LeaveFrame
    //     0x138f94c: mov             SP, fp
    //     0x138f950: ldp             fp, lr, [SP], #0x10
    // 0x138f954: ret
    //     0x138f954: ret             
    // 0x138f958: r0 = false
    //     0x138f958: add             x0, NULL, #0x30  ; false
    // 0x138f95c: LeaveFrame
    //     0x138f95c: mov             SP, fp
    //     0x138f960: ldp             fp, lr, [SP], #0x10
    // 0x138f964: ret
    //     0x138f964: ret             
    // 0x138f968: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x138f968: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x138f96c: b               #0x138f7a4
  }
  _ getBack(/* No info */) {
    // ** addr: 0x138f970, size: 0xb0
    // 0x138f970: EnterFrame
    //     0x138f970: stp             fp, lr, [SP, #-0x10]!
    //     0x138f974: mov             fp, SP
    // 0x138f978: AllocStack(0x8)
    //     0x138f978: sub             SP, SP, #8
    // 0x138f97c: CheckStackOverflow
    //     0x138f97c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x138f980: cmp             SP, x16
    //     0x138f984: b.ls            #0x138fa18
    // 0x138f988: r2 = false
    //     0x138f988: add             x2, NULL, #0x30  ; false
    // 0x138f98c: r0 = showLoading()
    //     0x138f98c: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x138f990: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x138f990: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x138f994: ldr             x0, [x0, #0x1c80]
    //     0x138f998: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x138f99c: cmp             w0, w16
    //     0x138f9a0: b.ne            #0x138f9ac
    //     0x138f9a4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x138f9a8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x138f9ac: r1 = Function '<anonymous closure>':.
    //     0x138f9ac: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc28] AnonymousClosure: (0x138f78c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack (0x138f970)
    //     0x138f9b0: ldr             x1, [x1, #0xc28]
    // 0x138f9b4: r2 = Null
    //     0x138f9b4: mov             x2, NULL
    // 0x138f9b8: r0 = AllocateClosure()
    //     0x138f9b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x138f9bc: mov             x1, x0
    // 0x138f9c0: r0 = GetNavigation.until()
    //     0x138f9c0: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x138f9c4: r1 = <bool>
    //     0x138f9c4: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x138f9c8: r0 = _Future()
    //     0x138f9c8: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x138f9cc: stur            x0, [fp, #-8]
    // 0x138f9d0: StoreField: r0->field_b = rZR
    //     0x138f9d0: stur            xzr, [x0, #0xb]
    // 0x138f9d4: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x138f9d4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x138f9d8: ldr             x0, [x0, #0x778]
    //     0x138f9dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x138f9e0: cmp             w0, w16
    //     0x138f9e4: b.ne            #0x138f9f0
    //     0x138f9e8: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x138f9ec: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x138f9f0: mov             x1, x0
    // 0x138f9f4: ldur            x0, [fp, #-8]
    // 0x138f9f8: StoreField: r0->field_13 = r1
    //     0x138f9f8: stur            w1, [x0, #0x13]
    // 0x138f9fc: mov             x1, x0
    // 0x138fa00: r2 = false
    //     0x138fa00: add             x2, NULL, #0x30  ; false
    // 0x138fa04: r0 = _asyncComplete()
    //     0x138fa04: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x138fa08: ldur            x0, [fp, #-8]
    // 0x138fa0c: LeaveFrame
    //     0x138fa0c: mov             SP, fp
    //     0x138fa10: ldp             fp, lr, [SP], #0x10
    // 0x138fa14: ret
    //     0x138fa14: ret             
    // 0x138fa18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x138fa18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x138fa1c: b               #0x138f988
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x138fa20, size: 0x38
    // 0x138fa20: EnterFrame
    //     0x138fa20: stp             fp, lr, [SP, #-0x10]!
    //     0x138fa24: mov             fp, SP
    // 0x138fa28: ldr             x0, [fp, #0x10]
    // 0x138fa2c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x138fa2c: ldur            w1, [x0, #0x17]
    // 0x138fa30: DecompressPointer r1
    //     0x138fa30: add             x1, x1, HEAP, lsl #32
    // 0x138fa34: CheckStackOverflow
    //     0x138fa34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x138fa38: cmp             SP, x16
    //     0x138fa3c: b.ls            #0x138fa50
    // 0x138fa40: r0 = getBack()
    //     0x138fa40: bl              #0x138f970  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack
    // 0x138fa44: LeaveFrame
    //     0x138fa44: mov             SP, fp
    //     0x138fa48: ldp             fp, lr, [SP], #0x10
    // 0x138fa4c: ret
    //     0x138fa4c: ret             
    // 0x138fa50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x138fa50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x138fa54: b               #0x138fa40
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x138fbbc, size: 0x84
    // 0x138fbbc: EnterFrame
    //     0x138fbbc: stp             fp, lr, [SP, #-0x10]!
    //     0x138fbc0: mov             fp, SP
    // 0x138fbc4: AllocStack(0x20)
    //     0x138fbc4: sub             SP, SP, #0x20
    // 0x138fbc8: SetupParameters()
    //     0x138fbc8: ldr             x0, [fp, #0x10]
    //     0x138fbcc: ldur            w1, [x0, #0x17]
    //     0x138fbd0: add             x1, x1, HEAP, lsl #32
    // 0x138fbd4: CheckStackOverflow
    //     0x138fbd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x138fbd8: cmp             SP, x16
    //     0x138fbdc: b.ls            #0x138fc38
    // 0x138fbe0: LoadField: r0 = r1->field_f
    //     0x138fbe0: ldur            w0, [x1, #0xf]
    // 0x138fbe4: DecompressPointer r0
    //     0x138fbe4: add             x0, x0, HEAP, lsl #32
    // 0x138fbe8: r16 = "request_address_page"
    //     0x138fbe8: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dbf0] "request_address_page"
    //     0x138fbec: ldr             x16, [x16, #0xbf0]
    // 0x138fbf0: r30 = "checkout_address"
    //     0x138fbf0: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3dc50] "checkout_address"
    //     0x138fbf4: ldr             lr, [lr, #0xc50]
    // 0x138fbf8: stp             lr, x16, [SP, #0x10]
    // 0x138fbfc: r16 = "address_widget"
    //     0x138fbfc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dc10] "address_widget"
    //     0x138fc00: ldr             x16, [x16, #0xc10]
    // 0x138fc04: r30 = "address_widget"
    //     0x138fc04: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3dc10] "address_widget"
    //     0x138fc08: ldr             lr, [lr, #0xc10]
    // 0x138fc0c: stp             lr, x16, [SP]
    // 0x138fc10: mov             x1, x0
    // 0x138fc14: r2 = "checkout_widget_fill"
    //     0x138fc14: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d5c8] "checkout_widget_fill"
    //     0x138fc18: ldr             x2, [x2, #0x5c8]
    // 0x138fc1c: r4 = const [0, 0x6, 0x4, 0x2, pageId, 0x2, pageType, 0x3, widgetId, 0x4, widgetType, 0x5, null]
    //     0x138fc1c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3dc58] List(13) [0, 0x6, 0x4, 0x2, "pageId", 0x2, "pageType", 0x3, "widgetId", 0x4, "widgetType", 0x5, Null]
    //     0x138fc20: ldr             x4, [x4, #0xc58]
    // 0x138fc24: r0 = checkoutPostEvent()
    //     0x138fc24: bl              #0x12e5eb0  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::checkoutPostEvent
    // 0x138fc28: r0 = Null
    //     0x138fc28: mov             x0, NULL
    // 0x138fc2c: LeaveFrame
    //     0x138fc2c: mov             SP, fp
    //     0x138fc30: ldp             fp, lr, [SP], #0x10
    // 0x138fc34: ret
    //     0x138fc34: ret             
    // 0x138fc38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x138fc38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x138fc3c: b               #0x138fbe0
  }
  [closure] Flexible <anonymous closure>(dynamic, CheckoutRequestAddressController) {
    // ** addr: 0x138fc40, size: 0x144
    // 0x138fc40: EnterFrame
    //     0x138fc40: stp             fp, lr, [SP, #-0x10]!
    //     0x138fc44: mov             fp, SP
    // 0x138fc48: AllocStack(0x30)
    //     0x138fc48: sub             SP, SP, #0x30
    // 0x138fc4c: SetupParameters()
    //     0x138fc4c: ldr             x0, [fp, #0x18]
    //     0x138fc50: ldur            w1, [x0, #0x17]
    //     0x138fc54: add             x1, x1, HEAP, lsl #32
    //     0x138fc58: stur            x1, [fp, #-8]
    // 0x138fc5c: CheckStackOverflow
    //     0x138fc5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x138fc60: cmp             SP, x16
    //     0x138fc64: b.ls            #0x138fd7c
    // 0x138fc68: r1 = 1
    //     0x138fc68: movz            x1, #0x1
    // 0x138fc6c: r0 = AllocateContext()
    //     0x138fc6c: bl              #0x16f6108  ; AllocateContextStub
    // 0x138fc70: mov             x2, x0
    // 0x138fc74: ldur            x0, [fp, #-8]
    // 0x138fc78: stur            x2, [fp, #-0x10]
    // 0x138fc7c: StoreField: r2->field_b = r0
    //     0x138fc7c: stur            w0, [x2, #0xb]
    // 0x138fc80: ldr             x3, [fp, #0x10]
    // 0x138fc84: StoreField: r2->field_f = r3
    //     0x138fc84: stur            w3, [x2, #0xf]
    // 0x138fc88: LoadField: r1 = r3->field_53
    //     0x138fc88: ldur            w1, [x3, #0x53]
    // 0x138fc8c: DecompressPointer r1
    //     0x138fc8c: add             x1, x1, HEAP, lsl #32
    // 0x138fc90: r0 = value()
    //     0x138fc90: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x138fc94: ldr             x1, [fp, #0x10]
    // 0x138fc98: stur            x0, [fp, #-0x18]
    // 0x138fc9c: r0 = configData()
    //     0x138fc9c: bl              #0x8a3140  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::configData
    // 0x138fca0: mov             x2, x0
    // 0x138fca4: ldur            x0, [fp, #-8]
    // 0x138fca8: stur            x2, [fp, #-0x28]
    // 0x138fcac: LoadField: r3 = r0->field_f
    //     0x138fcac: ldur            w3, [x0, #0xf]
    // 0x138fcb0: DecompressPointer r3
    //     0x138fcb0: add             x3, x3, HEAP, lsl #32
    // 0x138fcb4: ldr             x1, [fp, #0x10]
    // 0x138fcb8: stur            x3, [fp, #-0x20]
    // 0x138fcbc: r0 = customizedResponse()
    //     0x138fcbc: bl              #0x12d6d00  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::customizedResponse
    // 0x138fcc0: stur            x0, [fp, #-8]
    // 0x138fcc4: r0 = CheckoutAddressWidget()
    //     0x138fcc4: bl              #0x138fd84  ; AllocateCheckoutAddressWidgetStub -> CheckoutAddressWidget (size=0x24)
    // 0x138fcc8: mov             x3, x0
    // 0x138fccc: ldur            x0, [fp, #-0x18]
    // 0x138fcd0: stur            x3, [fp, #-0x30]
    // 0x138fcd4: StoreField: r3->field_b = r0
    //     0x138fcd4: stur            w0, [x3, #0xb]
    // 0x138fcd8: ldur            x2, [fp, #-0x10]
    // 0x138fcdc: r1 = Function '<anonymous closure>':.
    //     0x138fcdc: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc30] AnonymousClosure: (0x13906d0), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x138fce0: ldr             x1, [x1, #0xc30]
    // 0x138fce4: r0 = AllocateClosure()
    //     0x138fce4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x138fce8: mov             x1, x0
    // 0x138fcec: ldur            x0, [fp, #-0x30]
    // 0x138fcf0: StoreField: r0->field_f = r1
    //     0x138fcf0: stur            w1, [x0, #0xf]
    // 0x138fcf4: ldur            x1, [fp, #-0x28]
    // 0x138fcf8: StoreField: r0->field_13 = r1
    //     0x138fcf8: stur            w1, [x0, #0x13]
    // 0x138fcfc: ldur            x2, [fp, #-0x20]
    // 0x138fd00: r1 = Function 'pinCodeVerifyCallback':.
    //     0x138fd00: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc38] AnonymousClosure: (0x138fd90), in [package:customer_app/app/presentation/views/basic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::pinCodeVerifyCallback (0x138fdcc)
    //     0x138fd04: ldr             x1, [x1, #0xc38]
    // 0x138fd08: r0 = AllocateClosure()
    //     0x138fd08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x138fd0c: mov             x1, x0
    // 0x138fd10: ldur            x0, [fp, #-0x30]
    // 0x138fd14: ArrayStore: r0[0] = r1  ; List_4
    //     0x138fd14: stur            w1, [x0, #0x17]
    // 0x138fd18: ldur            x1, [fp, #-8]
    // 0x138fd1c: StoreField: r0->field_1b = r1
    //     0x138fd1c: stur            w1, [x0, #0x1b]
    // 0x138fd20: ldur            x2, [fp, #-0x10]
    // 0x138fd24: r1 = Function '<anonymous closure>':.
    //     0x138fd24: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc40] AnonymousClosure: (0x138fbbc), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x138fd28: ldr             x1, [x1, #0xc40]
    // 0x138fd2c: r0 = AllocateClosure()
    //     0x138fd2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x138fd30: mov             x1, x0
    // 0x138fd34: ldur            x0, [fp, #-0x30]
    // 0x138fd38: StoreField: r0->field_1f = r1
    //     0x138fd38: stur            w1, [x0, #0x1f]
    // 0x138fd3c: r1 = Instance_ValueKey
    //     0x138fd3c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc48] Obj!ValueKey<String>@d5b3a1
    //     0x138fd40: ldr             x1, [x1, #0xc48]
    // 0x138fd44: StoreField: r0->field_7 = r1
    //     0x138fd44: stur            w1, [x0, #7]
    // 0x138fd48: r1 = <FlexParentData>
    //     0x138fd48: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x138fd4c: ldr             x1, [x1, #0xe00]
    // 0x138fd50: r0 = Flexible()
    //     0x138fd50: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0x138fd54: r1 = 1
    //     0x138fd54: movz            x1, #0x1
    // 0x138fd58: StoreField: r0->field_13 = r1
    //     0x138fd58: stur            x1, [x0, #0x13]
    // 0x138fd5c: r1 = Instance_FlexFit
    //     0x138fd5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0x138fd60: ldr             x1, [x1, #0xe20]
    // 0x138fd64: StoreField: r0->field_1b = r1
    //     0x138fd64: stur            w1, [x0, #0x1b]
    // 0x138fd68: ldur            x1, [fp, #-0x30]
    // 0x138fd6c: StoreField: r0->field_b = r1
    //     0x138fd6c: stur            w1, [x0, #0xb]
    // 0x138fd70: LeaveFrame
    //     0x138fd70: mov             SP, fp
    //     0x138fd74: ldp             fp, lr, [SP], #0x10
    // 0x138fd78: ret
    //     0x138fd78: ret             
    // 0x138fd7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x138fd7c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x138fd80: b               #0x138fc68
  }
  [closure] void pinCodeVerifyCallback(dynamic, String) {
    // ** addr: 0x138fd90, size: 0x3c
    // 0x138fd90: EnterFrame
    //     0x138fd90: stp             fp, lr, [SP, #-0x10]!
    //     0x138fd94: mov             fp, SP
    // 0x138fd98: ldr             x0, [fp, #0x18]
    // 0x138fd9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x138fd9c: ldur            w1, [x0, #0x17]
    // 0x138fda0: DecompressPointer r1
    //     0x138fda0: add             x1, x1, HEAP, lsl #32
    // 0x138fda4: CheckStackOverflow
    //     0x138fda4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x138fda8: cmp             SP, x16
    //     0x138fdac: b.ls            #0x138fdc4
    // 0x138fdb0: ldr             x2, [fp, #0x10]
    // 0x138fdb4: r0 = pinCodeVerifyCallback()
    //     0x138fdb4: bl              #0x138fdcc  ; [package:customer_app/app/presentation/views/basic/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::pinCodeVerifyCallback
    // 0x138fdb8: LeaveFrame
    //     0x138fdb8: mov             SP, fp
    //     0x138fdbc: ldp             fp, lr, [SP], #0x10
    // 0x138fdc0: ret
    //     0x138fdc0: ret             
    // 0x138fdc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x138fdc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x138fdc8: b               #0x138fdb0
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, bool) {
    // ** addr: 0x13906d0, size: 0x134
    // 0x13906d0: EnterFrame
    //     0x13906d0: stp             fp, lr, [SP, #-0x10]!
    //     0x13906d4: mov             fp, SP
    // 0x13906d8: AllocStack(0x8)
    //     0x13906d8: sub             SP, SP, #8
    // 0x13906dc: SetupParameters()
    //     0x13906dc: ldr             x0, [fp, #0x48]
    //     0x13906e0: ldur            w3, [x0, #0x17]
    //     0x13906e4: add             x3, x3, HEAP, lsl #32
    //     0x13906e8: stur            x3, [fp, #-8]
    // 0x13906ec: CheckStackOverflow
    //     0x13906ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13906f0: cmp             SP, x16
    //     0x13906f4: b.ls            #0x13907fc
    // 0x13906f8: LoadField: r1 = r3->field_f
    //     0x13906f8: ldur            w1, [x3, #0xf]
    // 0x13906fc: DecompressPointer r1
    //     0x13906fc: add             x1, x1, HEAP, lsl #32
    // 0x1390700: ldr             x0, [fp, #0x40]
    // 0x1390704: StoreField: r1->field_c7 = r0
    //     0x1390704: stur            w0, [x1, #0xc7]
    //     0x1390708: ldurb           w16, [x1, #-1]
    //     0x139070c: ldurb           w17, [x0, #-1]
    //     0x1390710: and             x16, x17, x16, lsr #2
    //     0x1390714: tst             x16, HEAP, lsr #32
    //     0x1390718: b.eq            #0x1390720
    //     0x139071c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1390720: ldr             x0, [fp, #0x38]
    // 0x1390724: StoreField: r1->field_bf = r0
    //     0x1390724: stur            w0, [x1, #0xbf]
    //     0x1390728: ldurb           w16, [x1, #-1]
    //     0x139072c: ldurb           w17, [x0, #-1]
    //     0x1390730: and             x16, x17, x16, lsr #2
    //     0x1390734: tst             x16, HEAP, lsr #32
    //     0x1390738: b.eq            #0x1390740
    //     0x139073c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1390740: ldr             x0, [fp, #0x30]
    // 0x1390744: StoreField: r1->field_cb = r0
    //     0x1390744: stur            w0, [x1, #0xcb]
    //     0x1390748: ldurb           w16, [x1, #-1]
    //     0x139074c: ldurb           w17, [x0, #-1]
    //     0x1390750: and             x16, x17, x16, lsr #2
    //     0x1390754: tst             x16, HEAP, lsr #32
    //     0x1390758: b.eq            #0x1390760
    //     0x139075c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1390760: ldr             x0, [fp, #0x28]
    // 0x1390764: StoreField: r1->field_c3 = r0
    //     0x1390764: stur            w0, [x1, #0xc3]
    //     0x1390768: ldurb           w16, [x1, #-1]
    //     0x139076c: ldurb           w17, [x0, #-1]
    //     0x1390770: and             x16, x17, x16, lsr #2
    //     0x1390774: tst             x16, HEAP, lsr #32
    //     0x1390778: b.eq            #0x1390780
    //     0x139077c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x1390780: ldr             x0, [fp, #0x20]
    // 0x1390784: StoreField: r1->field_d3 = r0
    //     0x1390784: stur            w0, [x1, #0xd3]
    //     0x1390788: ldurb           w16, [x1, #-1]
    //     0x139078c: ldurb           w17, [x0, #-1]
    //     0x1390790: and             x16, x17, x16, lsr #2
    //     0x1390794: tst             x16, HEAP, lsr #32
    //     0x1390798: b.eq            #0x13907a0
    //     0x139079c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13907a0: ldr             x0, [fp, #0x18]
    // 0x13907a4: StoreField: r1->field_cf = r0
    //     0x13907a4: stur            w0, [x1, #0xcf]
    //     0x13907a8: ldurb           w16, [x1, #-1]
    //     0x13907ac: ldurb           w17, [x0, #-1]
    //     0x13907b0: and             x16, x17, x16, lsr #2
    //     0x13907b4: tst             x16, HEAP, lsr #32
    //     0x13907b8: b.eq            #0x13907c0
    //     0x13907bc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x13907c0: LoadField: r0 = r1->field_9f
    //     0x13907c0: ldur            w0, [x1, #0x9f]
    // 0x13907c4: DecompressPointer r0
    //     0x13907c4: add             x0, x0, HEAP, lsl #32
    // 0x13907c8: mov             x1, x0
    // 0x13907cc: ldr             x2, [fp, #0x10]
    // 0x13907d0: r0 = value=()
    //     0x13907d0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13907d4: ldr             x0, [fp, #0x10]
    // 0x13907d8: tbnz            w0, #4, #0x13907ec
    // 0x13907dc: ldur            x0, [fp, #-8]
    // 0x13907e0: LoadField: r1 = r0->field_f
    //     0x13907e0: ldur            w1, [x0, #0xf]
    // 0x13907e4: DecompressPointer r1
    //     0x13907e4: add             x1, x1, HEAP, lsl #32
    // 0x13907e8: r0 = setPrefAddress()
    //     0x13907e8: bl              #0x139020c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::setPrefAddress
    // 0x13907ec: r0 = Null
    //     0x13907ec: mov             x0, NULL
    // 0x13907f0: LeaveFrame
    //     0x13907f0: mov             SP, fp
    //     0x13907f4: ldp             fp, lr, [SP], #0x10
    // 0x13907f8: ret
    //     0x13907f8: ret             
    // 0x13907fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13907fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1390800: b               #0x13906f8
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x139093c, size: 0x8c
    // 0x139093c: EnterFrame
    //     0x139093c: stp             fp, lr, [SP, #-0x10]!
    //     0x1390940: mov             fp, SP
    // 0x1390944: AllocStack(0x20)
    //     0x1390944: sub             SP, SP, #0x20
    // 0x1390948: SetupParameters()
    //     0x1390948: ldr             x0, [fp, #0x10]
    //     0x139094c: ldur            w1, [x0, #0x17]
    //     0x1390950: add             x1, x1, HEAP, lsl #32
    // 0x1390954: CheckStackOverflow
    //     0x1390954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1390958: cmp             SP, x16
    //     0x139095c: b.ls            #0x13909c0
    // 0x1390960: LoadField: r0 = r1->field_f
    //     0x1390960: ldur            w0, [x1, #0xf]
    // 0x1390964: DecompressPointer r0
    //     0x1390964: add             x0, x0, HEAP, lsl #32
    // 0x1390968: mov             x1, x0
    // 0x139096c: r0 = controller()
    //     0x139096c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1390970: r16 = "bag_accordion"
    //     0x1390970: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d5f8] "bag_accordion"
    //     0x1390974: ldr             x16, [x16, #0x5f8]
    // 0x1390978: r30 = "accordion_click"
    //     0x1390978: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d5e8] "accordion_click"
    //     0x139097c: ldr             lr, [lr, #0x5e8]
    // 0x1390980: stp             lr, x16, [SP, #0x10]
    // 0x1390984: r16 = "Accordion Click"
    //     0x1390984: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d5f0] "Accordion Click"
    //     0x1390988: ldr             x16, [x16, #0x5f0]
    // 0x139098c: r30 = "bag_accordion"
    //     0x139098c: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d5f8] "bag_accordion"
    //     0x1390990: ldr             lr, [lr, #0x5f8]
    // 0x1390994: stp             lr, x16, [SP]
    // 0x1390998: mov             x1, x0
    // 0x139099c: r2 = "checkout_cta_clicked"
    //     0x139099c: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x13909a0: ldr             x2, [x2, #0x7b0]
    // 0x13909a4: r4 = const [0, 0x6, 0x4, 0x2, ctaName, 0x4, ctaType, 0x3, pageType, 0x2, widgetType, 0x5, null]
    //     0x13909a4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3ca20] List(13) [0, 0x6, 0x4, 0x2, "ctaName", 0x4, "ctaType", 0x3, "pageType", 0x2, "widgetType", 0x5, Null]
    //     0x13909a8: ldr             x4, [x4, #0xa20]
    // 0x13909ac: r0 = checkoutPostEvent()
    //     0x13909ac: bl              #0x12e5eb0  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::checkoutPostEvent
    // 0x13909b0: r0 = Null
    //     0x13909b0: mov             x0, NULL
    // 0x13909b4: LeaveFrame
    //     0x13909b4: mov             SP, fp
    //     0x13909b8: ldp             fp, lr, [SP], #0x10
    // 0x13909bc: ret
    //     0x13909bc: ret             
    // 0x13909c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13909c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13909c4: b               #0x1390960
  }
  [closure] CheckoutBagAccordion <anonymous closure>(dynamic) {
    // ** addr: 0x13909c8, size: 0xf0
    // 0x13909c8: EnterFrame
    //     0x13909c8: stp             fp, lr, [SP, #-0x10]!
    //     0x13909cc: mov             fp, SP
    // 0x13909d0: AllocStack(0x28)
    //     0x13909d0: sub             SP, SP, #0x28
    // 0x13909d4: SetupParameters()
    //     0x13909d4: ldr             x0, [fp, #0x10]
    //     0x13909d8: ldur            w2, [x0, #0x17]
    //     0x13909dc: add             x2, x2, HEAP, lsl #32
    //     0x13909e0: stur            x2, [fp, #-8]
    // 0x13909e4: CheckStackOverflow
    //     0x13909e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13909e8: cmp             SP, x16
    //     0x13909ec: b.ls            #0x1390ab0
    // 0x13909f0: LoadField: r1 = r2->field_f
    //     0x13909f0: ldur            w1, [x2, #0xf]
    // 0x13909f4: DecompressPointer r1
    //     0x13909f4: add             x1, x1, HEAP, lsl #32
    // 0x13909f8: r0 = controller()
    //     0x13909f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13909fc: LoadField: r1 = r0->field_5f
    //     0x13909fc: ldur            w1, [x0, #0x5f]
    // 0x1390a00: DecompressPointer r1
    //     0x1390a00: add             x1, x1, HEAP, lsl #32
    // 0x1390a04: r0 = value()
    //     0x1390a04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1390a08: ldur            x2, [fp, #-8]
    // 0x1390a0c: stur            x0, [fp, #-0x10]
    // 0x1390a10: LoadField: r1 = r2->field_f
    //     0x1390a10: ldur            w1, [x2, #0xf]
    // 0x1390a14: DecompressPointer r1
    //     0x1390a14: add             x1, x1, HEAP, lsl #32
    // 0x1390a18: r0 = controller()
    //     0x1390a18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1390a1c: LoadField: r1 = r0->field_5b
    //     0x1390a1c: ldur            w1, [x0, #0x5b]
    // 0x1390a20: DecompressPointer r1
    //     0x1390a20: add             x1, x1, HEAP, lsl #32
    // 0x1390a24: r0 = value()
    //     0x1390a24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1390a28: ldur            x2, [fp, #-8]
    // 0x1390a2c: stur            x0, [fp, #-0x18]
    // 0x1390a30: LoadField: r1 = r2->field_f
    //     0x1390a30: ldur            w1, [x2, #0xf]
    // 0x1390a34: DecompressPointer r1
    //     0x1390a34: add             x1, x1, HEAP, lsl #32
    // 0x1390a38: r0 = controller()
    //     0x1390a38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1390a3c: LoadField: r1 = r0->field_53
    //     0x1390a3c: ldur            w1, [x0, #0x53]
    // 0x1390a40: DecompressPointer r1
    //     0x1390a40: add             x1, x1, HEAP, lsl #32
    // 0x1390a44: r0 = value()
    //     0x1390a44: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1390a48: ldur            x2, [fp, #-8]
    // 0x1390a4c: LoadField: r1 = r2->field_f
    //     0x1390a4c: ldur            w1, [x2, #0xf]
    // 0x1390a50: DecompressPointer r1
    //     0x1390a50: add             x1, x1, HEAP, lsl #32
    // 0x1390a54: r0 = controller()
    //     0x1390a54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1390a58: LoadField: r1 = r0->field_8b
    //     0x1390a58: ldur            w1, [x0, #0x8b]
    // 0x1390a5c: DecompressPointer r1
    //     0x1390a5c: add             x1, x1, HEAP, lsl #32
    // 0x1390a60: stur            x1, [fp, #-0x20]
    // 0x1390a64: r0 = CheckoutBagAccordion()
    //     0x1390a64: bl              #0x1390ab8  ; AllocateCheckoutBagAccordionStub -> CheckoutBagAccordion (size=0x1c)
    // 0x1390a68: mov             x3, x0
    // 0x1390a6c: ldur            x0, [fp, #-0x10]
    // 0x1390a70: stur            x3, [fp, #-0x28]
    // 0x1390a74: StoreField: r3->field_b = r0
    //     0x1390a74: stur            w0, [x3, #0xb]
    // 0x1390a78: ldur            x0, [fp, #-0x18]
    // 0x1390a7c: StoreField: r3->field_f = r0
    //     0x1390a7c: stur            w0, [x3, #0xf]
    // 0x1390a80: ldur            x0, [fp, #-0x20]
    // 0x1390a84: StoreField: r3->field_13 = r0
    //     0x1390a84: stur            w0, [x3, #0x13]
    // 0x1390a88: ldur            x2, [fp, #-8]
    // 0x1390a8c: r1 = Function '<anonymous closure>':.
    //     0x1390a8c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dca8] AnonymousClosure: (0x139093c), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x1390a90: ldr             x1, [x1, #0xca8]
    // 0x1390a94: r0 = AllocateClosure()
    //     0x1390a94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1390a98: mov             x1, x0
    // 0x1390a9c: ldur            x0, [fp, #-0x28]
    // 0x1390aa0: ArrayStore: r0[0] = r1  ; List_4
    //     0x1390aa0: stur            w1, [x0, #0x17]
    // 0x1390aa4: LeaveFrame
    //     0x1390aa4: mov             SP, fp
    //     0x1390aa8: ldp             fp, lr, [SP], #0x10
    // 0x1390aac: ret
    //     0x1390aac: ret             
    // 0x1390ab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1390ab0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1390ab4: b               #0x13909f0
  }
  _ body(/* No info */) {
    // ** addr: 0x15008d4, size: 0x224
    // 0x15008d4: EnterFrame
    //     0x15008d4: stp             fp, lr, [SP, #-0x10]!
    //     0x15008d8: mov             fp, SP
    // 0x15008dc: AllocStack(0x28)
    //     0x15008dc: sub             SP, SP, #0x28
    // 0x15008e0: SetupParameters(CheckoutRequestAddressPage this /* r1 => r0, fp-0x8 */)
    //     0x15008e0: mov             x0, x1
    //     0x15008e4: stur            x1, [fp, #-8]
    // 0x15008e8: r1 = 1
    //     0x15008e8: movz            x1, #0x1
    // 0x15008ec: r0 = AllocateContext()
    //     0x15008ec: bl              #0x16f6108  ; AllocateContextStub
    // 0x15008f0: ldur            x2, [fp, #-8]
    // 0x15008f4: stur            x0, [fp, #-0x10]
    // 0x15008f8: StoreField: r0->field_f = r2
    //     0x15008f8: stur            w2, [x0, #0xf]
    // 0x15008fc: r0 = Obx()
    //     0x15008fc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1500900: ldur            x2, [fp, #-0x10]
    // 0x1500904: r1 = Function '<anonymous closure>':.
    //     0x1500904: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dbf8] AnonymousClosure: (0x1500af8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x1500908: ldr             x1, [x1, #0xbf8]
    // 0x150090c: stur            x0, [fp, #-0x18]
    // 0x1500910: r0 = AllocateClosure()
    //     0x1500910: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500914: mov             x1, x0
    // 0x1500918: ldur            x0, [fp, #-0x18]
    // 0x150091c: StoreField: r0->field_b = r1
    //     0x150091c: stur            w1, [x0, #0xb]
    // 0x1500920: r0 = Obx()
    //     0x1500920: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1500924: ldur            x2, [fp, #-0x10]
    // 0x1500928: r1 = Function '<anonymous closure>':.
    //     0x1500928: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc00] AnonymousClosure: (0x13909c8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x150092c: ldr             x1, [x1, #0xc00]
    // 0x1500930: stur            x0, [fp, #-0x20]
    // 0x1500934: r0 = AllocateClosure()
    //     0x1500934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500938: mov             x1, x0
    // 0x150093c: ldur            x0, [fp, #-0x20]
    // 0x1500940: StoreField: r0->field_b = r1
    //     0x1500940: stur            w1, [x0, #0xb]
    // 0x1500944: r1 = <CheckoutRequestAddressController>
    //     0x1500944: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3d7c8] TypeArguments: <CheckoutRequestAddressController>
    //     0x1500948: ldr             x1, [x1, #0x7c8]
    // 0x150094c: r0 = GetBuilder()
    //     0x150094c: bl              #0x12af310  ; AllocateGetBuilderStub -> GetBuilder<X0 bound GetxController> (size=0x40)
    // 0x1500950: mov             x3, x0
    // 0x1500954: r0 = true
    //     0x1500954: add             x0, NULL, #0x20  ; true
    // 0x1500958: stur            x3, [fp, #-0x28]
    // 0x150095c: StoreField: r3->field_13 = r0
    //     0x150095c: stur            w0, [x3, #0x13]
    // 0x1500960: ldur            x2, [fp, #-0x10]
    // 0x1500964: r1 = Function '<anonymous closure>':.
    //     0x1500964: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc08] AnonymousClosure: (0x138fc40), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::body (0x15008d4)
    //     0x1500968: ldr             x1, [x1, #0xc08]
    // 0x150096c: r0 = AllocateClosure()
    //     0x150096c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500970: mov             x1, x0
    // 0x1500974: ldur            x0, [fp, #-0x28]
    // 0x1500978: StoreField: r0->field_f = r1
    //     0x1500978: stur            w1, [x0, #0xf]
    // 0x150097c: r3 = true
    //     0x150097c: add             x3, NULL, #0x20  ; true
    // 0x1500980: StoreField: r0->field_1f = r3
    //     0x1500980: stur            w3, [x0, #0x1f]
    // 0x1500984: r4 = false
    //     0x1500984: add             x4, NULL, #0x30  ; false
    // 0x1500988: StoreField: r0->field_23 = r4
    //     0x1500988: stur            w4, [x0, #0x23]
    // 0x150098c: r1 = "address_widget"
    //     0x150098c: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc10] "address_widget"
    //     0x1500990: ldr             x1, [x1, #0xc10]
    // 0x1500994: ArrayStore: r0[0] = r1  ; List_4
    //     0x1500994: stur            w1, [x0, #0x17]
    // 0x1500998: r1 = Null
    //     0x1500998: mov             x1, NULL
    // 0x150099c: r2 = 6
    //     0x150099c: movz            x2, #0x6
    // 0x15009a0: r0 = AllocateArray()
    //     0x15009a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15009a4: mov             x2, x0
    // 0x15009a8: ldur            x0, [fp, #-0x18]
    // 0x15009ac: stur            x2, [fp, #-0x10]
    // 0x15009b0: StoreField: r2->field_f = r0
    //     0x15009b0: stur            w0, [x2, #0xf]
    // 0x15009b4: ldur            x0, [fp, #-0x20]
    // 0x15009b8: StoreField: r2->field_13 = r0
    //     0x15009b8: stur            w0, [x2, #0x13]
    // 0x15009bc: ldur            x0, [fp, #-0x28]
    // 0x15009c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x15009c0: stur            w0, [x2, #0x17]
    // 0x15009c4: r1 = <Widget>
    //     0x15009c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15009c8: r0 = AllocateGrowableArray()
    //     0x15009c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15009cc: mov             x1, x0
    // 0x15009d0: ldur            x0, [fp, #-0x10]
    // 0x15009d4: stur            x1, [fp, #-0x18]
    // 0x15009d8: StoreField: r1->field_f = r0
    //     0x15009d8: stur            w0, [x1, #0xf]
    // 0x15009dc: r0 = 6
    //     0x15009dc: movz            x0, #0x6
    // 0x15009e0: StoreField: r1->field_b = r0
    //     0x15009e0: stur            w0, [x1, #0xb]
    // 0x15009e4: r0 = Column()
    //     0x15009e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x15009e8: mov             x1, x0
    // 0x15009ec: r0 = Instance_Axis
    //     0x15009ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x15009f0: stur            x1, [fp, #-0x10]
    // 0x15009f4: StoreField: r1->field_f = r0
    //     0x15009f4: stur            w0, [x1, #0xf]
    // 0x15009f8: r2 = Instance_MainAxisAlignment
    //     0x15009f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x15009fc: ldr             x2, [x2, #0xa08]
    // 0x1500a00: StoreField: r1->field_13 = r2
    //     0x1500a00: stur            w2, [x1, #0x13]
    // 0x1500a04: r2 = Instance_MainAxisSize
    //     0x1500a04: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x1500a08: ldr             x2, [x2, #0xdd0]
    // 0x1500a0c: ArrayStore: r1[0] = r2  ; List_4
    //     0x1500a0c: stur            w2, [x1, #0x17]
    // 0x1500a10: r2 = Instance_CrossAxisAlignment
    //     0x1500a10: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3dc18] Obj!CrossAxisAlignment@d73441
    //     0x1500a14: ldr             x2, [x2, #0xc18]
    // 0x1500a18: StoreField: r1->field_1b = r2
    //     0x1500a18: stur            w2, [x1, #0x1b]
    // 0x1500a1c: r2 = Instance_VerticalDirection
    //     0x1500a1c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1500a20: ldr             x2, [x2, #0xa20]
    // 0x1500a24: StoreField: r1->field_23 = r2
    //     0x1500a24: stur            w2, [x1, #0x23]
    // 0x1500a28: r2 = Instance_Clip
    //     0x1500a28: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1500a2c: ldr             x2, [x2, #0x38]
    // 0x1500a30: StoreField: r1->field_2b = r2
    //     0x1500a30: stur            w2, [x1, #0x2b]
    // 0x1500a34: StoreField: r1->field_2f = rZR
    //     0x1500a34: stur            xzr, [x1, #0x2f]
    // 0x1500a38: ldur            x2, [fp, #-0x18]
    // 0x1500a3c: StoreField: r1->field_b = r2
    //     0x1500a3c: stur            w2, [x1, #0xb]
    // 0x1500a40: r0 = SingleChildScrollView()
    //     0x1500a40: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x1500a44: mov             x1, x0
    // 0x1500a48: r0 = Instance_Axis
    //     0x1500a48: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1500a4c: stur            x1, [fp, #-0x18]
    // 0x1500a50: StoreField: r1->field_b = r0
    //     0x1500a50: stur            w0, [x1, #0xb]
    // 0x1500a54: r0 = false
    //     0x1500a54: add             x0, NULL, #0x30  ; false
    // 0x1500a58: StoreField: r1->field_f = r0
    //     0x1500a58: stur            w0, [x1, #0xf]
    // 0x1500a5c: ldur            x2, [fp, #-0x10]
    // 0x1500a60: StoreField: r1->field_23 = r2
    //     0x1500a60: stur            w2, [x1, #0x23]
    // 0x1500a64: r2 = Instance_DragStartBehavior
    //     0x1500a64: ldr             x2, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x1500a68: StoreField: r1->field_27 = r2
    //     0x1500a68: stur            w2, [x1, #0x27]
    // 0x1500a6c: r2 = Instance_Clip
    //     0x1500a6c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x1500a70: ldr             x2, [x2, #0x7e0]
    // 0x1500a74: StoreField: r1->field_2b = r2
    //     0x1500a74: stur            w2, [x1, #0x2b]
    // 0x1500a78: r2 = Instance_HitTestBehavior
    //     0x1500a78: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x1500a7c: ldr             x2, [x2, #0x288]
    // 0x1500a80: StoreField: r1->field_2f = r2
    //     0x1500a80: stur            w2, [x1, #0x2f]
    // 0x1500a84: r0 = SafeArea()
    //     0x1500a84: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x1500a88: mov             x1, x0
    // 0x1500a8c: r0 = true
    //     0x1500a8c: add             x0, NULL, #0x20  ; true
    // 0x1500a90: stur            x1, [fp, #-0x10]
    // 0x1500a94: StoreField: r1->field_b = r0
    //     0x1500a94: stur            w0, [x1, #0xb]
    // 0x1500a98: StoreField: r1->field_f = r0
    //     0x1500a98: stur            w0, [x1, #0xf]
    // 0x1500a9c: StoreField: r1->field_13 = r0
    //     0x1500a9c: stur            w0, [x1, #0x13]
    // 0x1500aa0: ArrayStore: r1[0] = r0  ; List_4
    //     0x1500aa0: stur            w0, [x1, #0x17]
    // 0x1500aa4: r0 = Instance_EdgeInsets
    //     0x1500aa4: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x1500aa8: StoreField: r1->field_1b = r0
    //     0x1500aa8: stur            w0, [x1, #0x1b]
    // 0x1500aac: r0 = false
    //     0x1500aac: add             x0, NULL, #0x30  ; false
    // 0x1500ab0: StoreField: r1->field_1f = r0
    //     0x1500ab0: stur            w0, [x1, #0x1f]
    // 0x1500ab4: ldur            x0, [fp, #-0x18]
    // 0x1500ab8: StoreField: r1->field_23 = r0
    //     0x1500ab8: stur            w0, [x1, #0x23]
    // 0x1500abc: r0 = WillPopScope()
    //     0x1500abc: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x1500ac0: mov             x3, x0
    // 0x1500ac4: ldur            x0, [fp, #-0x10]
    // 0x1500ac8: stur            x3, [fp, #-0x18]
    // 0x1500acc: StoreField: r3->field_b = r0
    //     0x1500acc: stur            w0, [x3, #0xb]
    // 0x1500ad0: ldur            x2, [fp, #-8]
    // 0x1500ad4: r1 = Function 'getBack':.
    //     0x1500ad4: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dc20] AnonymousClosure: (0x138fa20), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack (0x138f970)
    //     0x1500ad8: ldr             x1, [x1, #0xc20]
    // 0x1500adc: r0 = AllocateClosure()
    //     0x1500adc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1500ae0: mov             x1, x0
    // 0x1500ae4: ldur            x0, [fp, #-0x18]
    // 0x1500ae8: StoreField: r0->field_f = r1
    //     0x1500ae8: stur            w1, [x0, #0xf]
    // 0x1500aec: LeaveFrame
    //     0x1500aec: mov             SP, fp
    //     0x1500af0: ldp             fp, lr, [SP], #0x10
    // 0x1500af4: ret
    //     0x1500af4: ret             
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x1500af8, size: 0x64
    // 0x1500af8: EnterFrame
    //     0x1500af8: stp             fp, lr, [SP, #-0x10]!
    //     0x1500afc: mov             fp, SP
    // 0x1500b00: AllocStack(0x8)
    //     0x1500b00: sub             SP, SP, #8
    // 0x1500b04: SetupParameters()
    //     0x1500b04: ldr             x0, [fp, #0x10]
    //     0x1500b08: ldur            w1, [x0, #0x17]
    //     0x1500b0c: add             x1, x1, HEAP, lsl #32
    // 0x1500b10: CheckStackOverflow
    //     0x1500b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1500b14: cmp             SP, x16
    //     0x1500b18: b.ls            #0x1500b54
    // 0x1500b1c: LoadField: r0 = r1->field_f
    //     0x1500b1c: ldur            w0, [x1, #0xf]
    // 0x1500b20: DecompressPointer r0
    //     0x1500b20: add             x0, x0, HEAP, lsl #32
    // 0x1500b24: mov             x1, x0
    // 0x1500b28: r0 = controller()
    //     0x1500b28: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1500b2c: mov             x1, x0
    // 0x1500b30: r0 = userData()
    //     0x1500b30: bl              #0x8a9718  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::userData
    // 0x1500b34: stur            x0, [fp, #-8]
    // 0x1500b38: r0 = CheckoutBreadCrumb()
    //     0x1500b38: bl              #0x1391e94  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x18)
    // 0x1500b3c: ldur            x1, [fp, #-8]
    // 0x1500b40: StoreField: r0->field_b = r1
    //     0x1500b40: stur            w1, [x0, #0xb]
    // 0x1500b44: StoreField: r0->field_f = rZR
    //     0x1500b44: stur            xzr, [x0, #0xf]
    // 0x1500b48: LeaveFrame
    //     0x1500b48: mov             SP, fp
    //     0x1500b4c: ldp             fp, lr, [SP], #0x10
    // 0x1500b50: ret
    //     0x1500b50: ret             
    // 0x1500b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1500b54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1500b58: b               #0x1500b1c
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e8070, size: 0x24c
    // 0x15e8070: EnterFrame
    //     0x15e8070: stp             fp, lr, [SP, #-0x10]!
    //     0x15e8074: mov             fp, SP
    // 0x15e8078: AllocStack(0x28)
    //     0x15e8078: sub             SP, SP, #0x28
    // 0x15e807c: SetupParameters(CheckoutRequestAddressPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e807c: stur            x1, [fp, #-8]
    //     0x15e8080: stur            x2, [fp, #-0x10]
    // 0x15e8084: CheckStackOverflow
    //     0x15e8084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e8088: cmp             SP, x16
    //     0x15e808c: b.ls            #0x15e82b4
    // 0x15e8090: r1 = 2
    //     0x15e8090: movz            x1, #0x2
    // 0x15e8094: r0 = AllocateContext()
    //     0x15e8094: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e8098: ldur            x1, [fp, #-8]
    // 0x15e809c: stur            x0, [fp, #-0x18]
    // 0x15e80a0: StoreField: r0->field_f = r1
    //     0x15e80a0: stur            w1, [x0, #0xf]
    // 0x15e80a4: ldur            x2, [fp, #-0x10]
    // 0x15e80a8: StoreField: r0->field_13 = r2
    //     0x15e80a8: stur            w2, [x0, #0x13]
    // 0x15e80ac: r0 = Obx()
    //     0x15e80ac: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e80b0: ldur            x2, [fp, #-0x18]
    // 0x15e80b4: r1 = Function '<anonymous closure>':.
    //     0x15e80b4: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dcb0] AnonymousClosure: (0x15ccba8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_number_page.dart] CheckoutRequestNumberPage::appBar (0x15e8384)
    //     0x15e80b8: ldr             x1, [x1, #0xcb0]
    // 0x15e80bc: stur            x0, [fp, #-0x10]
    // 0x15e80c0: r0 = AllocateClosure()
    //     0x15e80c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e80c4: mov             x1, x0
    // 0x15e80c8: ldur            x0, [fp, #-0x10]
    // 0x15e80cc: StoreField: r0->field_b = r1
    //     0x15e80cc: stur            w1, [x0, #0xb]
    // 0x15e80d0: ldur            x1, [fp, #-8]
    // 0x15e80d4: r0 = controller()
    //     0x15e80d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e80d8: mov             x1, x0
    // 0x15e80dc: r0 = appliedCoupon()
    //     0x15e80dc: bl              #0x913a0c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_payment_method_controller.dart] CheckoutPaymentMethodController::appliedCoupon
    // 0x15e80e0: tbnz            w0, #4, #0x15e8178
    // 0x15e80e4: ldur            x2, [fp, #-0x18]
    // 0x15e80e8: LoadField: r1 = r2->field_13
    //     0x15e80e8: ldur            w1, [x2, #0x13]
    // 0x15e80ec: DecompressPointer r1
    //     0x15e80ec: add             x1, x1, HEAP, lsl #32
    // 0x15e80f0: r0 = of()
    //     0x15e80f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e80f4: LoadField: r1 = r0->field_5b
    //     0x15e80f4: ldur            w1, [x0, #0x5b]
    // 0x15e80f8: DecompressPointer r1
    //     0x15e80f8: add             x1, x1, HEAP, lsl #32
    // 0x15e80fc: stur            x1, [fp, #-8]
    // 0x15e8100: r0 = ColorFilter()
    //     0x15e8100: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e8104: mov             x1, x0
    // 0x15e8108: ldur            x0, [fp, #-8]
    // 0x15e810c: stur            x1, [fp, #-0x20]
    // 0x15e8110: StoreField: r1->field_7 = r0
    //     0x15e8110: stur            w0, [x1, #7]
    // 0x15e8114: r0 = Instance_BlendMode
    //     0x15e8114: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e8118: ldr             x0, [x0, #0xb30]
    // 0x15e811c: StoreField: r1->field_b = r0
    //     0x15e811c: stur            w0, [x1, #0xb]
    // 0x15e8120: r2 = 1
    //     0x15e8120: movz            x2, #0x1
    // 0x15e8124: StoreField: r1->field_13 = r2
    //     0x15e8124: stur            x2, [x1, #0x13]
    // 0x15e8128: r0 = SvgPicture()
    //     0x15e8128: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e812c: stur            x0, [fp, #-8]
    // 0x15e8130: ldur            x16, [fp, #-0x20]
    // 0x15e8134: str             x16, [SP]
    // 0x15e8138: mov             x1, x0
    // 0x15e813c: r2 = "assets/images/search.svg"
    //     0x15e813c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e8140: ldr             x2, [x2, #0xa30]
    // 0x15e8144: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e8144: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e8148: ldr             x4, [x4, #0xa38]
    // 0x15e814c: r0 = SvgPicture.asset()
    //     0x15e814c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e8150: r0 = Align()
    //     0x15e8150: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e8154: r3 = Instance_Alignment
    //     0x15e8154: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e8158: ldr             x3, [x3, #0xb10]
    // 0x15e815c: StoreField: r0->field_f = r3
    //     0x15e815c: stur            w3, [x0, #0xf]
    // 0x15e8160: r4 = 1.000000
    //     0x15e8160: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e8164: StoreField: r0->field_13 = r4
    //     0x15e8164: stur            w4, [x0, #0x13]
    // 0x15e8168: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e8168: stur            w4, [x0, #0x17]
    // 0x15e816c: ldur            x1, [fp, #-8]
    // 0x15e8170: StoreField: r0->field_b = r1
    //     0x15e8170: stur            w1, [x0, #0xb]
    // 0x15e8174: b               #0x15e8228
    // 0x15e8178: ldur            x5, [fp, #-0x18]
    // 0x15e817c: r4 = 1.000000
    //     0x15e817c: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e8180: r0 = Instance_BlendMode
    //     0x15e8180: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e8184: ldr             x0, [x0, #0xb30]
    // 0x15e8188: r3 = Instance_Alignment
    //     0x15e8188: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e818c: ldr             x3, [x3, #0xb10]
    // 0x15e8190: r2 = 1
    //     0x15e8190: movz            x2, #0x1
    // 0x15e8194: LoadField: r1 = r5->field_13
    //     0x15e8194: ldur            w1, [x5, #0x13]
    // 0x15e8198: DecompressPointer r1
    //     0x15e8198: add             x1, x1, HEAP, lsl #32
    // 0x15e819c: r0 = of()
    //     0x15e819c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e81a0: LoadField: r1 = r0->field_5b
    //     0x15e81a0: ldur            w1, [x0, #0x5b]
    // 0x15e81a4: DecompressPointer r1
    //     0x15e81a4: add             x1, x1, HEAP, lsl #32
    // 0x15e81a8: stur            x1, [fp, #-8]
    // 0x15e81ac: r0 = ColorFilter()
    //     0x15e81ac: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e81b0: mov             x1, x0
    // 0x15e81b4: ldur            x0, [fp, #-8]
    // 0x15e81b8: stur            x1, [fp, #-0x20]
    // 0x15e81bc: StoreField: r1->field_7 = r0
    //     0x15e81bc: stur            w0, [x1, #7]
    // 0x15e81c0: r0 = Instance_BlendMode
    //     0x15e81c0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e81c4: ldr             x0, [x0, #0xb30]
    // 0x15e81c8: StoreField: r1->field_b = r0
    //     0x15e81c8: stur            w0, [x1, #0xb]
    // 0x15e81cc: r0 = 1
    //     0x15e81cc: movz            x0, #0x1
    // 0x15e81d0: StoreField: r1->field_13 = r0
    //     0x15e81d0: stur            x0, [x1, #0x13]
    // 0x15e81d4: r0 = SvgPicture()
    //     0x15e81d4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e81d8: stur            x0, [fp, #-8]
    // 0x15e81dc: ldur            x16, [fp, #-0x20]
    // 0x15e81e0: str             x16, [SP]
    // 0x15e81e4: mov             x1, x0
    // 0x15e81e8: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e81e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e81ec: ldr             x2, [x2, #0xa40]
    // 0x15e81f0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e81f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e81f4: ldr             x4, [x4, #0xa38]
    // 0x15e81f8: r0 = SvgPicture.asset()
    //     0x15e81f8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e81fc: r0 = Align()
    //     0x15e81fc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e8200: mov             x1, x0
    // 0x15e8204: r0 = Instance_Alignment
    //     0x15e8204: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e8208: ldr             x0, [x0, #0xb10]
    // 0x15e820c: StoreField: r1->field_f = r0
    //     0x15e820c: stur            w0, [x1, #0xf]
    // 0x15e8210: r0 = 1.000000
    //     0x15e8210: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e8214: StoreField: r1->field_13 = r0
    //     0x15e8214: stur            w0, [x1, #0x13]
    // 0x15e8218: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e8218: stur            w0, [x1, #0x17]
    // 0x15e821c: ldur            x0, [fp, #-8]
    // 0x15e8220: StoreField: r1->field_b = r0
    //     0x15e8220: stur            w0, [x1, #0xb]
    // 0x15e8224: mov             x0, x1
    // 0x15e8228: stur            x0, [fp, #-8]
    // 0x15e822c: r0 = InkWell()
    //     0x15e822c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e8230: mov             x3, x0
    // 0x15e8234: ldur            x0, [fp, #-8]
    // 0x15e8238: stur            x3, [fp, #-0x20]
    // 0x15e823c: StoreField: r3->field_b = r0
    //     0x15e823c: stur            w0, [x3, #0xb]
    // 0x15e8240: ldur            x2, [fp, #-0x18]
    // 0x15e8244: r1 = Function '<anonymous closure>':.
    //     0x15e8244: add             x1, PP, #0x3d, lsl #12  ; [pp+0x3dcb8] AnonymousClosure: (0x15e82bc), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::appBar (0x15e8070)
    //     0x15e8248: ldr             x1, [x1, #0xcb8]
    // 0x15e824c: r0 = AllocateClosure()
    //     0x15e824c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e8250: ldur            x2, [fp, #-0x20]
    // 0x15e8254: StoreField: r2->field_f = r0
    //     0x15e8254: stur            w0, [x2, #0xf]
    // 0x15e8258: r0 = true
    //     0x15e8258: add             x0, NULL, #0x20  ; true
    // 0x15e825c: StoreField: r2->field_43 = r0
    //     0x15e825c: stur            w0, [x2, #0x43]
    // 0x15e8260: r1 = Instance_BoxShape
    //     0x15e8260: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e8264: ldr             x1, [x1, #0x80]
    // 0x15e8268: StoreField: r2->field_47 = r1
    //     0x15e8268: stur            w1, [x2, #0x47]
    // 0x15e826c: StoreField: r2->field_6f = r0
    //     0x15e826c: stur            w0, [x2, #0x6f]
    // 0x15e8270: r1 = false
    //     0x15e8270: add             x1, NULL, #0x30  ; false
    // 0x15e8274: StoreField: r2->field_73 = r1
    //     0x15e8274: stur            w1, [x2, #0x73]
    // 0x15e8278: StoreField: r2->field_83 = r0
    //     0x15e8278: stur            w0, [x2, #0x83]
    // 0x15e827c: StoreField: r2->field_7b = r1
    //     0x15e827c: stur            w1, [x2, #0x7b]
    // 0x15e8280: r0 = AppBar()
    //     0x15e8280: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e8284: stur            x0, [fp, #-8]
    // 0x15e8288: ldur            x16, [fp, #-0x10]
    // 0x15e828c: str             x16, [SP]
    // 0x15e8290: mov             x1, x0
    // 0x15e8294: ldur            x2, [fp, #-0x20]
    // 0x15e8298: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e8298: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e829c: ldr             x4, [x4, #0xf00]
    // 0x15e82a0: r0 = AppBar()
    //     0x15e82a0: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e82a4: ldur            x0, [fp, #-8]
    // 0x15e82a8: LeaveFrame
    //     0x15e82a8: mov             SP, fp
    //     0x15e82ac: ldp             fp, lr, [SP], #0x10
    // 0x15e82b0: ret
    //     0x15e82b0: ret             
    // 0x15e82b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e82b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e82b8: b               #0x15e8090
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e82bc, size: 0xc8
    // 0x15e82bc: EnterFrame
    //     0x15e82bc: stp             fp, lr, [SP, #-0x10]!
    //     0x15e82c0: mov             fp, SP
    // 0x15e82c4: AllocStack(0x18)
    //     0x15e82c4: sub             SP, SP, #0x18
    // 0x15e82c8: SetupParameters()
    //     0x15e82c8: ldr             x0, [fp, #0x10]
    //     0x15e82cc: ldur            w3, [x0, #0x17]
    //     0x15e82d0: add             x3, x3, HEAP, lsl #32
    //     0x15e82d4: stur            x3, [fp, #-8]
    // 0x15e82d8: CheckStackOverflow
    //     0x15e82d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e82dc: cmp             SP, x16
    //     0x15e82e0: b.ls            #0x15e837c
    // 0x15e82e4: LoadField: r1 = r3->field_f
    //     0x15e82e4: ldur            w1, [x3, #0xf]
    // 0x15e82e8: DecompressPointer r1
    //     0x15e82e8: add             x1, x1, HEAP, lsl #32
    // 0x15e82ec: r2 = false
    //     0x15e82ec: add             x2, NULL, #0x30  ; false
    // 0x15e82f0: r0 = showLoading()
    //     0x15e82f0: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e82f4: ldur            x0, [fp, #-8]
    // 0x15e82f8: LoadField: r1 = r0->field_f
    //     0x15e82f8: ldur            w1, [x0, #0xf]
    // 0x15e82fc: DecompressPointer r1
    //     0x15e82fc: add             x1, x1, HEAP, lsl #32
    // 0x15e8300: r0 = controller()
    //     0x15e8300: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e8304: LoadField: r1 = r0->field_6b
    //     0x15e8304: ldur            w1, [x0, #0x6b]
    // 0x15e8308: DecompressPointer r1
    //     0x15e8308: add             x1, x1, HEAP, lsl #32
    // 0x15e830c: r0 = value()
    //     0x15e830c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e8310: tbnz            w0, #4, #0x15e8348
    // 0x15e8314: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e8314: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e8318: ldr             x0, [x0, #0x1c80]
    //     0x15e831c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e8320: cmp             w0, w16
    //     0x15e8324: b.ne            #0x15e8330
    //     0x15e8328: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e832c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e8330: r16 = "/search"
    //     0x15e8330: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15e8334: ldr             x16, [x16, #0x838]
    // 0x15e8338: stp             x16, NULL, [SP]
    // 0x15e833c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15e833c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15e8340: r0 = GetNavigation.toNamed()
    //     0x15e8340: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e8344: b               #0x15e836c
    // 0x15e8348: ldur            x0, [fp, #-8]
    // 0x15e834c: LoadField: r1 = r0->field_f
    //     0x15e834c: ldur            w1, [x0, #0xf]
    // 0x15e8350: DecompressPointer r1
    //     0x15e8350: add             x1, x1, HEAP, lsl #32
    // 0x15e8354: r2 = false
    //     0x15e8354: add             x2, NULL, #0x30  ; false
    // 0x15e8358: r0 = showLoading()
    //     0x15e8358: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e835c: ldur            x0, [fp, #-8]
    // 0x15e8360: LoadField: r1 = r0->field_f
    //     0x15e8360: ldur            w1, [x0, #0xf]
    // 0x15e8364: DecompressPointer r1
    //     0x15e8364: add             x1, x1, HEAP, lsl #32
    // 0x15e8368: r0 = getBack()
    //     0x15e8368: bl              #0x138f970  ; [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_address_page.dart] CheckoutRequestAddressPage::getBack
    // 0x15e836c: r0 = Null
    //     0x15e836c: mov             x0, NULL
    // 0x15e8370: LeaveFrame
    //     0x15e8370: mov             SP, fp
    //     0x15e8374: ldp             fp, lr, [SP], #0x10
    // 0x15e8378: ret
    //     0x15e8378: ret             
    // 0x15e837c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e837c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e8380: b               #0x15e82e4
  }
}
