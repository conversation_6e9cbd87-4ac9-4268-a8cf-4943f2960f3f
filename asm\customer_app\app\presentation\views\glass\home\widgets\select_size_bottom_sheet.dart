// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart

// class id: 1049406, size: 0x8
class :: {
}

// class id: 3332, size: 0x30, field offset: 0x14
class _SelectSizeBottomSheetState extends State<dynamic> {

  late AllSkuDatum dropDownValue; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x941ff0, size: 0x574
    // 0x941ff0: EnterFrame
    //     0x941ff0: stp             fp, lr, [SP, #-0x10]!
    //     0x941ff4: mov             fp, SP
    // 0x941ff8: AllocStack(0x38)
    //     0x941ff8: sub             SP, SP, #0x38
    // 0x941ffc: SetupParameters(_SelectSizeBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x941ffc: stur            x1, [fp, #-8]
    // 0x942000: CheckStackOverflow
    //     0x942000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x942004: cmp             SP, x16
    //     0x942008: b.ls            #0x942534
    // 0x94200c: r1 = 1
    //     0x94200c: movz            x1, #0x1
    // 0x942010: r0 = AllocateContext()
    //     0x942010: bl              #0x16f6108  ; AllocateContextStub
    // 0x942014: mov             x2, x0
    // 0x942018: ldur            x1, [fp, #-8]
    // 0x94201c: stur            x2, [fp, #-0x10]
    // 0x942020: StoreField: r2->field_f = r1
    //     0x942020: stur            w1, [x2, #0xf]
    // 0x942024: LoadField: r0 = r1->field_b
    //     0x942024: ldur            w0, [x1, #0xb]
    // 0x942028: DecompressPointer r0
    //     0x942028: add             x0, x0, HEAP, lsl #32
    // 0x94202c: cmp             w0, NULL
    // 0x942030: b.eq            #0x94253c
    // 0x942034: LoadField: r3 = r0->field_f
    //     0x942034: ldur            w3, [x0, #0xf]
    // 0x942038: DecompressPointer r3
    //     0x942038: add             x3, x3, HEAP, lsl #32
    // 0x94203c: r0 = LoadClassIdInstr(r3)
    //     0x94203c: ldur            x0, [x3, #-1]
    //     0x942040: ubfx            x0, x0, #0xc, #0x14
    // 0x942044: r16 = "home_page"
    //     0x942044: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x942048: ldr             x16, [x16, #0xe60]
    // 0x94204c: stp             x16, x3, [SP]
    // 0x942050: mov             lr, x0
    // 0x942054: ldr             lr, [x21, lr, lsl #3]
    // 0x942058: blr             lr
    // 0x94205c: tbnz            w0, #4, #0x942074
    // 0x942060: ldur            x0, [fp, #-8]
    // 0x942064: r1 = "product_card"
    //     0x942064: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0x942068: ldr             x1, [x1, #0xc28]
    // 0x94206c: StoreField: r0->field_2b = r1
    //     0x94206c: stur            w1, [x0, #0x2b]
    // 0x942070: b               #0x942078
    // 0x942074: ldur            x0, [fp, #-8]
    // 0x942078: LoadField: r1 = r0->field_b
    //     0x942078: ldur            w1, [x0, #0xb]
    // 0x94207c: DecompressPointer r1
    //     0x94207c: add             x1, x1, HEAP, lsl #32
    // 0x942080: cmp             w1, NULL
    // 0x942084: b.eq            #0x942540
    // 0x942088: LoadField: r2 = r1->field_b
    //     0x942088: ldur            w2, [x1, #0xb]
    // 0x94208c: DecompressPointer r2
    //     0x94208c: add             x2, x2, HEAP, lsl #32
    // 0x942090: LoadField: r1 = r2->field_73
    //     0x942090: ldur            w1, [x2, #0x73]
    // 0x942094: DecompressPointer r1
    //     0x942094: add             x1, x1, HEAP, lsl #32
    // 0x942098: cmp             w1, NULL
    // 0x94209c: b.ne            #0x9420a8
    // 0x9420a0: r3 = Null
    //     0x9420a0: mov             x3, NULL
    // 0x9420a4: b               #0x9420c0
    // 0x9420a8: LoadField: r3 = r1->field_b
    //     0x9420a8: ldur            w3, [x1, #0xb]
    // 0x9420ac: cbnz            w3, #0x9420b8
    // 0x9420b0: r4 = false
    //     0x9420b0: add             x4, NULL, #0x30  ; false
    // 0x9420b4: b               #0x9420bc
    // 0x9420b8: r4 = true
    //     0x9420b8: add             x4, NULL, #0x20  ; true
    // 0x9420bc: mov             x3, x4
    // 0x9420c0: cmp             w3, NULL
    // 0x9420c4: b.ne            #0x9420d0
    // 0x9420c8: mov             x1, x0
    // 0x9420cc: b               #0x942390
    // 0x9420d0: tbnz            w3, #4, #0x94238c
    // 0x9420d4: cmp             w1, NULL
    // 0x9420d8: b.ne            #0x9420e4
    // 0x9420dc: r2 = Null
    //     0x9420dc: mov             x2, NULL
    // 0x9420e0: b               #0x94213c
    // 0x9420e4: r16 = <AllGroupedSkusDatum?>
    //     0x9420e4: add             x16, PP, #0x53, lsl #12  ; [pp+0x53c98] TypeArguments: <AllGroupedSkusDatum?>
    //     0x9420e8: ldr             x16, [x16, #0xc98]
    // 0x9420ec: stp             x1, x16, [SP]
    // 0x9420f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9420f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9420f4: r0 = cast()
    //     0x9420f4: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x9420f8: ldur            x2, [fp, #-0x10]
    // 0x9420fc: r1 = Function '<anonymous closure>':.
    //     0x9420fc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56298] AnonymousClosure: (0x9084a8), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::initState (0x949324)
    //     0x942100: ldr             x1, [x1, #0x298]
    // 0x942104: stur            x0, [fp, #-0x18]
    // 0x942108: r0 = AllocateClosure()
    //     0x942108: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94210c: r1 = Function '<anonymous closure>':.
    //     0x94210c: add             x1, PP, #0x56, lsl #12  ; [pp+0x562a0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x942110: ldr             x1, [x1, #0x2a0]
    // 0x942114: r2 = Null
    //     0x942114: mov             x2, NULL
    // 0x942118: stur            x0, [fp, #-0x20]
    // 0x94211c: r0 = AllocateClosure()
    //     0x94211c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x942120: str             x0, [SP]
    // 0x942124: ldur            x1, [fp, #-0x18]
    // 0x942128: ldur            x2, [fp, #-0x20]
    // 0x94212c: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x94212c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x942130: ldr             x4, [x4, #0xb48]
    // 0x942134: r0 = firstWhere()
    //     0x942134: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x942138: mov             x2, x0
    // 0x94213c: cmp             w2, NULL
    // 0x942140: b.eq            #0x9421c8
    // 0x942144: ldur            x0, [fp, #-8]
    // 0x942148: LoadField: r1 = r0->field_b
    //     0x942148: ldur            w1, [x0, #0xb]
    // 0x94214c: DecompressPointer r1
    //     0x94214c: add             x1, x1, HEAP, lsl #32
    // 0x942150: cmp             w1, NULL
    // 0x942154: b.eq            #0x942544
    // 0x942158: LoadField: r3 = r1->field_b
    //     0x942158: ldur            w3, [x1, #0xb]
    // 0x94215c: DecompressPointer r3
    //     0x94215c: add             x3, x3, HEAP, lsl #32
    // 0x942160: LoadField: r1 = r3->field_73
    //     0x942160: ldur            w1, [x3, #0x73]
    // 0x942164: DecompressPointer r1
    //     0x942164: add             x1, x1, HEAP, lsl #32
    // 0x942168: cmp             w1, NULL
    // 0x94216c: b.ne            #0x942178
    // 0x942170: r0 = Null
    //     0x942170: mov             x0, NULL
    // 0x942174: b               #0x942198
    // 0x942178: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x942178: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x94217c: r0 = indexOf()
    //     0x94217c: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x942180: mov             x2, x0
    // 0x942184: r0 = BoxInt64Instr(r2)
    //     0x942184: sbfiz           x0, x2, #1, #0x1f
    //     0x942188: cmp             x2, x0, asr #1
    //     0x94218c: b.eq            #0x942198
    //     0x942190: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x942194: stur            x2, [x0, #7]
    // 0x942198: cmp             w0, NULL
    // 0x94219c: b.ne            #0x9421a8
    // 0x9421a0: r0 = 0
    //     0x9421a0: movz            x0, #0
    // 0x9421a4: b               #0x9421b8
    // 0x9421a8: r1 = LoadInt32Instr(r0)
    //     0x9421a8: sbfx            x1, x0, #1, #0x1f
    //     0x9421ac: tbz             w0, #0, #0x9421b4
    //     0x9421b0: ldur            x1, [x0, #7]
    // 0x9421b4: mov             x0, x1
    // 0x9421b8: ldur            x2, [fp, #-8]
    // 0x9421bc: ArrayStore: r2[0] = r0  ; List_8
    //     0x9421bc: stur            x0, [x2, #0x17]
    // 0x9421c0: mov             x3, x0
    // 0x9421c4: b               #0x9421d4
    // 0x9421c8: ldur            x2, [fp, #-8]
    // 0x9421cc: ArrayStore: r2[0] = rZR  ; List_8
    //     0x9421cc: stur            xzr, [x2, #0x17]
    // 0x9421d0: r3 = 0
    //     0x9421d0: movz            x3, #0
    // 0x9421d4: stur            x3, [fp, #-0x28]
    // 0x9421d8: LoadField: r0 = r2->field_b
    //     0x9421d8: ldur            w0, [x2, #0xb]
    // 0x9421dc: DecompressPointer r0
    //     0x9421dc: add             x0, x0, HEAP, lsl #32
    // 0x9421e0: cmp             w0, NULL
    // 0x9421e4: b.eq            #0x942548
    // 0x9421e8: LoadField: r1 = r0->field_b
    //     0x9421e8: ldur            w1, [x0, #0xb]
    // 0x9421ec: DecompressPointer r1
    //     0x9421ec: add             x1, x1, HEAP, lsl #32
    // 0x9421f0: LoadField: r4 = r1->field_73
    //     0x9421f0: ldur            w4, [x1, #0x73]
    // 0x9421f4: DecompressPointer r4
    //     0x9421f4: add             x4, x4, HEAP, lsl #32
    // 0x9421f8: stur            x4, [fp, #-0x18]
    // 0x9421fc: cmp             w4, NULL
    // 0x942200: b.ne            #0x94220c
    // 0x942204: r0 = Null
    //     0x942204: mov             x0, NULL
    // 0x942208: b               #0x942280
    // 0x94220c: LoadField: r0 = r4->field_b
    //     0x94220c: ldur            w0, [x4, #0xb]
    // 0x942210: r1 = LoadInt32Instr(r0)
    //     0x942210: sbfx            x1, x0, #1, #0x1f
    // 0x942214: mov             x0, x1
    // 0x942218: mov             x1, x3
    // 0x94221c: cmp             x1, x0
    // 0x942220: b.hs            #0x94254c
    // 0x942224: LoadField: r0 = r4->field_f
    //     0x942224: ldur            w0, [x4, #0xf]
    // 0x942228: DecompressPointer r0
    //     0x942228: add             x0, x0, HEAP, lsl #32
    // 0x94222c: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x94222c: add             x16, x0, x3, lsl #2
    //     0x942230: ldur            w1, [x16, #0xf]
    // 0x942234: DecompressPointer r1
    //     0x942234: add             x1, x1, HEAP, lsl #32
    // 0x942238: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x942238: ldur            w5, [x1, #0x17]
    // 0x94223c: DecompressPointer r5
    //     0x94223c: add             x5, x5, HEAP, lsl #32
    // 0x942240: cmp             w5, NULL
    // 0x942244: b.ne            #0x942250
    // 0x942248: r0 = Null
    //     0x942248: mov             x0, NULL
    // 0x94224c: b               #0x942280
    // 0x942250: LoadField: r0 = r5->field_b
    //     0x942250: ldur            w0, [x5, #0xb]
    // 0x942254: r1 = LoadInt32Instr(r0)
    //     0x942254: sbfx            x1, x0, #1, #0x1f
    // 0x942258: mov             x0, x1
    // 0x94225c: mov             x1, x3
    // 0x942260: cmp             x1, x0
    // 0x942264: b.hs            #0x942550
    // 0x942268: LoadField: r0 = r5->field_f
    //     0x942268: ldur            w0, [x5, #0xf]
    // 0x94226c: DecompressPointer r0
    //     0x94226c: add             x0, x0, HEAP, lsl #32
    // 0x942270: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x942270: add             x16, x0, x3, lsl #2
    //     0x942274: ldur            w1, [x16, #0xf]
    // 0x942278: DecompressPointer r1
    //     0x942278: add             x1, x1, HEAP, lsl #32
    // 0x94227c: mov             x0, x1
    // 0x942280: cmp             w0, NULL
    // 0x942284: b.ne            #0x94228c
    // 0x942288: r0 = AllSkuDatum()
    //     0x942288: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x94228c: ldur            x1, [fp, #-8]
    // 0x942290: ldur            x2, [fp, #-0x18]
    // 0x942294: StoreField: r1->field_13 = r0
    //     0x942294: stur            w0, [x1, #0x13]
    //     0x942298: ldurb           w16, [x1, #-1]
    //     0x94229c: ldurb           w17, [x0, #-1]
    //     0x9422a0: and             x16, x17, x16, lsr #2
    //     0x9422a4: tst             x16, HEAP, lsr #32
    //     0x9422a8: b.eq            #0x9422b0
    //     0x9422ac: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x9422b0: LoadField: r3 = r1->field_27
    //     0x9422b0: ldur            w3, [x1, #0x27]
    // 0x9422b4: DecompressPointer r3
    //     0x9422b4: add             x3, x3, HEAP, lsl #32
    // 0x9422b8: stur            x3, [fp, #-0x20]
    // 0x9422bc: cmp             w2, NULL
    // 0x9422c0: b.ne            #0x9422cc
    // 0x9422c4: r0 = Null
    //     0x9422c4: mov             x0, NULL
    // 0x9422c8: b               #0x942304
    // 0x9422cc: ldur            x4, [fp, #-0x28]
    // 0x9422d0: LoadField: r0 = r2->field_b
    //     0x9422d0: ldur            w0, [x2, #0xb]
    // 0x9422d4: r1 = LoadInt32Instr(r0)
    //     0x9422d4: sbfx            x1, x0, #1, #0x1f
    // 0x9422d8: mov             x0, x1
    // 0x9422dc: mov             x1, x4
    // 0x9422e0: cmp             x1, x0
    // 0x9422e4: b.hs            #0x942554
    // 0x9422e8: LoadField: r0 = r2->field_f
    //     0x9422e8: ldur            w0, [x2, #0xf]
    // 0x9422ec: DecompressPointer r0
    //     0x9422ec: add             x0, x0, HEAP, lsl #32
    // 0x9422f0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x9422f0: add             x16, x0, x4, lsl #2
    //     0x9422f4: ldur            w1, [x16, #0xf]
    // 0x9422f8: DecompressPointer r1
    //     0x9422f8: add             x1, x1, HEAP, lsl #32
    // 0x9422fc: LoadField: r0 = r1->field_7
    //     0x9422fc: ldur            w0, [x1, #7]
    // 0x942300: DecompressPointer r0
    //     0x942300: add             x0, x0, HEAP, lsl #32
    // 0x942304: cmp             w0, NULL
    // 0x942308: b.ne            #0x942310
    // 0x94230c: r0 = ""
    //     0x94230c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x942310: stur            x0, [fp, #-0x18]
    // 0x942314: LoadField: r1 = r3->field_b
    //     0x942314: ldur            w1, [x3, #0xb]
    // 0x942318: LoadField: r2 = r3->field_f
    //     0x942318: ldur            w2, [x3, #0xf]
    // 0x94231c: DecompressPointer r2
    //     0x94231c: add             x2, x2, HEAP, lsl #32
    // 0x942320: LoadField: r4 = r2->field_b
    //     0x942320: ldur            w4, [x2, #0xb]
    // 0x942324: r2 = LoadInt32Instr(r1)
    //     0x942324: sbfx            x2, x1, #1, #0x1f
    // 0x942328: stur            x2, [fp, #-0x28]
    // 0x94232c: r1 = LoadInt32Instr(r4)
    //     0x94232c: sbfx            x1, x4, #1, #0x1f
    // 0x942330: cmp             x2, x1
    // 0x942334: b.ne            #0x942340
    // 0x942338: mov             x1, x3
    // 0x94233c: r0 = _growToNextCapacity()
    //     0x94233c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x942340: ldur            x0, [fp, #-0x20]
    // 0x942344: ldur            x2, [fp, #-0x28]
    // 0x942348: add             x1, x2, #1
    // 0x94234c: lsl             x3, x1, #1
    // 0x942350: StoreField: r0->field_b = r3
    //     0x942350: stur            w3, [x0, #0xb]
    // 0x942354: LoadField: r1 = r0->field_f
    //     0x942354: ldur            w1, [x0, #0xf]
    // 0x942358: DecompressPointer r1
    //     0x942358: add             x1, x1, HEAP, lsl #32
    // 0x94235c: ldur            x0, [fp, #-0x18]
    // 0x942360: ArrayStore: r1[r2] = r0  ; List_4
    //     0x942360: add             x25, x1, x2, lsl #2
    //     0x942364: add             x25, x25, #0xf
    //     0x942368: str             w0, [x25]
    //     0x94236c: tbz             w0, #0, #0x942388
    //     0x942370: ldurb           w16, [x1, #-1]
    //     0x942374: ldurb           w17, [x0, #-1]
    //     0x942378: and             x16, x17, x16, lsr #2
    //     0x94237c: tst             x16, HEAP, lsr #32
    //     0x942380: b.eq            #0x942388
    //     0x942384: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x942388: b               #0x942524
    // 0x94238c: mov             x1, x0
    // 0x942390: LoadField: r0 = r2->field_6f
    //     0x942390: ldur            w0, [x2, #0x6f]
    // 0x942394: DecompressPointer r0
    //     0x942394: add             x0, x0, HEAP, lsl #32
    // 0x942398: cmp             w0, NULL
    // 0x94239c: b.ne            #0x9423a8
    // 0x9423a0: r2 = Null
    //     0x9423a0: mov             x2, NULL
    // 0x9423a4: b               #0x942400
    // 0x9423a8: r16 = <AllSkuDatum?>
    //     0x9423a8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52868] TypeArguments: <AllSkuDatum?>
    //     0x9423ac: ldr             x16, [x16, #0x868]
    // 0x9423b0: stp             x0, x16, [SP]
    // 0x9423b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9423b4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9423b8: r0 = cast()
    //     0x9423b8: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x9423bc: ldur            x2, [fp, #-0x10]
    // 0x9423c0: r1 = Function '<anonymous closure>':.
    //     0x9423c0: add             x1, PP, #0x56, lsl #12  ; [pp+0x562a8] AnonymousClosure: (0x9083cc), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::initState (0x949324)
    //     0x9423c4: ldr             x1, [x1, #0x2a8]
    // 0x9423c8: stur            x0, [fp, #-0x10]
    // 0x9423cc: r0 = AllocateClosure()
    //     0x9423cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9423d0: r1 = Function '<anonymous closure>':.
    //     0x9423d0: add             x1, PP, #0x56, lsl #12  ; [pp+0x562b0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x9423d4: ldr             x1, [x1, #0x2b0]
    // 0x9423d8: r2 = Null
    //     0x9423d8: mov             x2, NULL
    // 0x9423dc: stur            x0, [fp, #-0x18]
    // 0x9423e0: r0 = AllocateClosure()
    //     0x9423e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9423e4: str             x0, [SP]
    // 0x9423e8: ldur            x1, [fp, #-0x10]
    // 0x9423ec: ldur            x2, [fp, #-0x18]
    // 0x9423f0: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x9423f0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x9423f4: ldr             x4, [x4, #0xb48]
    // 0x9423f8: r0 = firstWhere()
    //     0x9423f8: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x9423fc: mov             x2, x0
    // 0x942400: cmp             w2, NULL
    // 0x942404: b.eq            #0x94248c
    // 0x942408: ldur            x0, [fp, #-8]
    // 0x94240c: LoadField: r1 = r0->field_b
    //     0x94240c: ldur            w1, [x0, #0xb]
    // 0x942410: DecompressPointer r1
    //     0x942410: add             x1, x1, HEAP, lsl #32
    // 0x942414: cmp             w1, NULL
    // 0x942418: b.eq            #0x942558
    // 0x94241c: LoadField: r3 = r1->field_b
    //     0x94241c: ldur            w3, [x1, #0xb]
    // 0x942420: DecompressPointer r3
    //     0x942420: add             x3, x3, HEAP, lsl #32
    // 0x942424: LoadField: r1 = r3->field_6f
    //     0x942424: ldur            w1, [x3, #0x6f]
    // 0x942428: DecompressPointer r1
    //     0x942428: add             x1, x1, HEAP, lsl #32
    // 0x94242c: cmp             w1, NULL
    // 0x942430: b.ne            #0x94243c
    // 0x942434: r0 = Null
    //     0x942434: mov             x0, NULL
    // 0x942438: b               #0x94245c
    // 0x94243c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x94243c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x942440: r0 = indexOf()
    //     0x942440: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x942444: mov             x2, x0
    // 0x942448: r0 = BoxInt64Instr(r2)
    //     0x942448: sbfiz           x0, x2, #1, #0x1f
    //     0x94244c: cmp             x2, x0, asr #1
    //     0x942450: b.eq            #0x94245c
    //     0x942454: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x942458: stur            x2, [x0, #7]
    // 0x94245c: cmp             w0, NULL
    // 0x942460: b.ne            #0x94246c
    // 0x942464: r0 = 0
    //     0x942464: movz            x0, #0
    // 0x942468: b               #0x94247c
    // 0x94246c: r1 = LoadInt32Instr(r0)
    //     0x94246c: sbfx            x1, x0, #1, #0x1f
    //     0x942470: tbz             w0, #0, #0x942478
    //     0x942474: ldur            x1, [x0, #7]
    // 0x942478: mov             x0, x1
    // 0x94247c: ldur            x2, [fp, #-8]
    // 0x942480: ArrayStore: r2[0] = r0  ; List_8
    //     0x942480: stur            x0, [x2, #0x17]
    // 0x942484: mov             x3, x0
    // 0x942488: b               #0x942498
    // 0x94248c: ldur            x2, [fp, #-8]
    // 0x942490: ArrayStore: r2[0] = rZR  ; List_8
    //     0x942490: stur            xzr, [x2, #0x17]
    // 0x942494: r3 = 0
    //     0x942494: movz            x3, #0
    // 0x942498: LoadField: r0 = r2->field_b
    //     0x942498: ldur            w0, [x2, #0xb]
    // 0x94249c: DecompressPointer r0
    //     0x94249c: add             x0, x0, HEAP, lsl #32
    // 0x9424a0: cmp             w0, NULL
    // 0x9424a4: b.eq            #0x94255c
    // 0x9424a8: LoadField: r1 = r0->field_b
    //     0x9424a8: ldur            w1, [x0, #0xb]
    // 0x9424ac: DecompressPointer r1
    //     0x9424ac: add             x1, x1, HEAP, lsl #32
    // 0x9424b0: LoadField: r4 = r1->field_6f
    //     0x9424b0: ldur            w4, [x1, #0x6f]
    // 0x9424b4: DecompressPointer r4
    //     0x9424b4: add             x4, x4, HEAP, lsl #32
    // 0x9424b8: cmp             w4, NULL
    // 0x9424bc: b.ne            #0x9424c8
    // 0x9424c0: r0 = Null
    //     0x9424c0: mov             x0, NULL
    // 0x9424c4: b               #0x9424f8
    // 0x9424c8: LoadField: r0 = r4->field_b
    //     0x9424c8: ldur            w0, [x4, #0xb]
    // 0x9424cc: r1 = LoadInt32Instr(r0)
    //     0x9424cc: sbfx            x1, x0, #1, #0x1f
    // 0x9424d0: mov             x0, x1
    // 0x9424d4: mov             x1, x3
    // 0x9424d8: cmp             x1, x0
    // 0x9424dc: b.hs            #0x942560
    // 0x9424e0: LoadField: r0 = r4->field_f
    //     0x9424e0: ldur            w0, [x4, #0xf]
    // 0x9424e4: DecompressPointer r0
    //     0x9424e4: add             x0, x0, HEAP, lsl #32
    // 0x9424e8: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x9424e8: add             x16, x0, x3, lsl #2
    //     0x9424ec: ldur            w1, [x16, #0xf]
    // 0x9424f0: DecompressPointer r1
    //     0x9424f0: add             x1, x1, HEAP, lsl #32
    // 0x9424f4: mov             x0, x1
    // 0x9424f8: cmp             w0, NULL
    // 0x9424fc: b.ne            #0x942504
    // 0x942500: r0 = AllSkuDatum()
    //     0x942500: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x942504: ldur            x1, [fp, #-8]
    // 0x942508: StoreField: r1->field_13 = r0
    //     0x942508: stur            w0, [x1, #0x13]
    //     0x94250c: ldurb           w16, [x1, #-1]
    //     0x942510: ldurb           w17, [x0, #-1]
    //     0x942514: and             x16, x17, x16, lsr #2
    //     0x942518: tst             x16, HEAP, lsr #32
    //     0x94251c: b.eq            #0x942524
    //     0x942520: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x942524: r0 = Null
    //     0x942524: mov             x0, NULL
    // 0x942528: LeaveFrame
    //     0x942528: mov             SP, fp
    //     0x94252c: ldp             fp, lr, [SP], #0x10
    // 0x942530: ret
    //     0x942530: ret             
    // 0x942534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x942534: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x942538: b               #0x94200c
    // 0x94253c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94253c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x942540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x942540: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x942544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x942544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x942548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x942548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94254c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94254c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x942550: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942550: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x942554: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942554: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x942558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x942558: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94255c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94255c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x942560: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x942560: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xb6b7f4, size: 0xc20
    // 0xb6b7f4: EnterFrame
    //     0xb6b7f4: stp             fp, lr, [SP, #-0x10]!
    //     0xb6b7f8: mov             fp, SP
    // 0xb6b7fc: AllocStack(0x58)
    //     0xb6b7fc: sub             SP, SP, #0x58
    // 0xb6b800: SetupParameters(_SelectSizeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb6b800: mov             x0, x1
    //     0xb6b804: stur            x1, [fp, #-8]
    //     0xb6b808: mov             x1, x2
    //     0xb6b80c: stur            x2, [fp, #-0x10]
    // 0xb6b810: CheckStackOverflow
    //     0xb6b810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6b814: cmp             SP, x16
    //     0xb6b818: b.ls            #0xb6c3f8
    // 0xb6b81c: r1 = 2
    //     0xb6b81c: movz            x1, #0x2
    // 0xb6b820: r0 = AllocateContext()
    //     0xb6b820: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6b824: mov             x2, x0
    // 0xb6b828: ldur            x0, [fp, #-8]
    // 0xb6b82c: stur            x2, [fp, #-0x18]
    // 0xb6b830: StoreField: r2->field_f = r0
    //     0xb6b830: stur            w0, [x2, #0xf]
    // 0xb6b834: ldur            x1, [fp, #-0x10]
    // 0xb6b838: StoreField: r2->field_13 = r1
    //     0xb6b838: stur            w1, [x2, #0x13]
    // 0xb6b83c: r0 = of()
    //     0xb6b83c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6b840: LoadField: r1 = r0->field_87
    //     0xb6b840: ldur            w1, [x0, #0x87]
    // 0xb6b844: DecompressPointer r1
    //     0xb6b844: add             x1, x1, HEAP, lsl #32
    // 0xb6b848: LoadField: r0 = r1->field_7
    //     0xb6b848: ldur            w0, [x1, #7]
    // 0xb6b84c: DecompressPointer r0
    //     0xb6b84c: add             x0, x0, HEAP, lsl #32
    // 0xb6b850: r16 = Instance_Color
    //     0xb6b850: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6b854: r30 = 16.000000
    //     0xb6b854: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6b858: ldr             lr, [lr, #0x188]
    // 0xb6b85c: stp             lr, x16, [SP]
    // 0xb6b860: mov             x1, x0
    // 0xb6b864: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb6b864: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb6b868: ldr             x4, [x4, #0x9b8]
    // 0xb6b86c: r0 = copyWith()
    //     0xb6b86c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6b870: stur            x0, [fp, #-0x10]
    // 0xb6b874: r0 = Text()
    //     0xb6b874: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6b878: mov             x1, x0
    // 0xb6b87c: r0 = "Select Preference"
    //     0xb6b87c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52710] "Select Preference"
    //     0xb6b880: ldr             x0, [x0, #0x710]
    // 0xb6b884: stur            x1, [fp, #-0x20]
    // 0xb6b888: StoreField: r1->field_b = r0
    //     0xb6b888: stur            w0, [x1, #0xb]
    // 0xb6b88c: ldur            x0, [fp, #-0x10]
    // 0xb6b890: StoreField: r1->field_13 = r0
    //     0xb6b890: stur            w0, [x1, #0x13]
    // 0xb6b894: r0 = InkWell()
    //     0xb6b894: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb6b898: mov             x3, x0
    // 0xb6b89c: r0 = Instance_Icon
    //     0xb6b89c: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xb6b8a0: ldr             x0, [x0, #0x2b8]
    // 0xb6b8a4: stur            x3, [fp, #-0x10]
    // 0xb6b8a8: StoreField: r3->field_b = r0
    //     0xb6b8a8: stur            w0, [x3, #0xb]
    // 0xb6b8ac: ldur            x2, [fp, #-0x18]
    // 0xb6b8b0: r1 = Function '<anonymous closure>':.
    //     0xb6b8b0: add             x1, PP, #0x56, lsl #12  ; [pp+0x561f8] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xb6b8b4: ldr             x1, [x1, #0x1f8]
    // 0xb6b8b8: r0 = AllocateClosure()
    //     0xb6b8b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6b8bc: mov             x1, x0
    // 0xb6b8c0: ldur            x0, [fp, #-0x10]
    // 0xb6b8c4: StoreField: r0->field_f = r1
    //     0xb6b8c4: stur            w1, [x0, #0xf]
    // 0xb6b8c8: r3 = true
    //     0xb6b8c8: add             x3, NULL, #0x20  ; true
    // 0xb6b8cc: StoreField: r0->field_43 = r3
    //     0xb6b8cc: stur            w3, [x0, #0x43]
    // 0xb6b8d0: r1 = Instance_BoxShape
    //     0xb6b8d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6b8d4: ldr             x1, [x1, #0x80]
    // 0xb6b8d8: StoreField: r0->field_47 = r1
    //     0xb6b8d8: stur            w1, [x0, #0x47]
    // 0xb6b8dc: StoreField: r0->field_6f = r3
    //     0xb6b8dc: stur            w3, [x0, #0x6f]
    // 0xb6b8e0: r4 = false
    //     0xb6b8e0: add             x4, NULL, #0x30  ; false
    // 0xb6b8e4: StoreField: r0->field_73 = r4
    //     0xb6b8e4: stur            w4, [x0, #0x73]
    // 0xb6b8e8: StoreField: r0->field_83 = r3
    //     0xb6b8e8: stur            w3, [x0, #0x83]
    // 0xb6b8ec: StoreField: r0->field_7b = r4
    //     0xb6b8ec: stur            w4, [x0, #0x7b]
    // 0xb6b8f0: r1 = Null
    //     0xb6b8f0: mov             x1, NULL
    // 0xb6b8f4: r2 = 6
    //     0xb6b8f4: movz            x2, #0x6
    // 0xb6b8f8: r0 = AllocateArray()
    //     0xb6b8f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6b8fc: mov             x2, x0
    // 0xb6b900: ldur            x0, [fp, #-0x20]
    // 0xb6b904: stur            x2, [fp, #-0x28]
    // 0xb6b908: StoreField: r2->field_f = r0
    //     0xb6b908: stur            w0, [x2, #0xf]
    // 0xb6b90c: r16 = Instance_Spacer
    //     0xb6b90c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb6b910: ldr             x16, [x16, #0xf0]
    // 0xb6b914: StoreField: r2->field_13 = r16
    //     0xb6b914: stur            w16, [x2, #0x13]
    // 0xb6b918: ldur            x0, [fp, #-0x10]
    // 0xb6b91c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6b91c: stur            w0, [x2, #0x17]
    // 0xb6b920: r1 = <Widget>
    //     0xb6b920: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6b924: r0 = AllocateGrowableArray()
    //     0xb6b924: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6b928: mov             x1, x0
    // 0xb6b92c: ldur            x0, [fp, #-0x28]
    // 0xb6b930: stur            x1, [fp, #-0x10]
    // 0xb6b934: StoreField: r1->field_f = r0
    //     0xb6b934: stur            w0, [x1, #0xf]
    // 0xb6b938: r2 = 6
    //     0xb6b938: movz            x2, #0x6
    // 0xb6b93c: StoreField: r1->field_b = r2
    //     0xb6b93c: stur            w2, [x1, #0xb]
    // 0xb6b940: r0 = Row()
    //     0xb6b940: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb6b944: mov             x1, x0
    // 0xb6b948: r0 = Instance_Axis
    //     0xb6b948: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6b94c: stur            x1, [fp, #-0x20]
    // 0xb6b950: StoreField: r1->field_f = r0
    //     0xb6b950: stur            w0, [x1, #0xf]
    // 0xb6b954: r0 = Instance_MainAxisAlignment
    //     0xb6b954: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6b958: ldr             x0, [x0, #0xa08]
    // 0xb6b95c: StoreField: r1->field_13 = r0
    //     0xb6b95c: stur            w0, [x1, #0x13]
    // 0xb6b960: r2 = Instance_MainAxisSize
    //     0xb6b960: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6b964: ldr             x2, [x2, #0xa10]
    // 0xb6b968: ArrayStore: r1[0] = r2  ; List_4
    //     0xb6b968: stur            w2, [x1, #0x17]
    // 0xb6b96c: r2 = Instance_CrossAxisAlignment
    //     0xb6b96c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb6b970: ldr             x2, [x2, #0xa18]
    // 0xb6b974: StoreField: r1->field_1b = r2
    //     0xb6b974: stur            w2, [x1, #0x1b]
    // 0xb6b978: r2 = Instance_VerticalDirection
    //     0xb6b978: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6b97c: ldr             x2, [x2, #0xa20]
    // 0xb6b980: StoreField: r1->field_23 = r2
    //     0xb6b980: stur            w2, [x1, #0x23]
    // 0xb6b984: r3 = Instance_Clip
    //     0xb6b984: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6b988: ldr             x3, [x3, #0x38]
    // 0xb6b98c: StoreField: r1->field_2b = r3
    //     0xb6b98c: stur            w3, [x1, #0x2b]
    // 0xb6b990: StoreField: r1->field_2f = rZR
    //     0xb6b990: stur            xzr, [x1, #0x2f]
    // 0xb6b994: ldur            x4, [fp, #-0x10]
    // 0xb6b998: StoreField: r1->field_b = r4
    //     0xb6b998: stur            w4, [x1, #0xb]
    // 0xb6b99c: r0 = Padding()
    //     0xb6b99c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6b9a0: mov             x2, x0
    // 0xb6b9a4: r0 = Instance_EdgeInsets
    //     0xb6b9a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb6b9a8: ldr             x0, [x0, #0xd0]
    // 0xb6b9ac: stur            x2, [fp, #-0x10]
    // 0xb6b9b0: StoreField: r2->field_f = r0
    //     0xb6b9b0: stur            w0, [x2, #0xf]
    // 0xb6b9b4: ldur            x0, [fp, #-0x20]
    // 0xb6b9b8: StoreField: r2->field_b = r0
    //     0xb6b9b8: stur            w0, [x2, #0xb]
    // 0xb6b9bc: ldur            x0, [fp, #-0x18]
    // 0xb6b9c0: LoadField: r1 = r0->field_13
    //     0xb6b9c0: ldur            w1, [x0, #0x13]
    // 0xb6b9c4: DecompressPointer r1
    //     0xb6b9c4: add             x1, x1, HEAP, lsl #32
    // 0xb6b9c8: r0 = of()
    //     0xb6b9c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6b9cc: LoadField: r1 = r0->field_5b
    //     0xb6b9cc: ldur            w1, [x0, #0x5b]
    // 0xb6b9d0: DecompressPointer r1
    //     0xb6b9d0: add             x1, x1, HEAP, lsl #32
    // 0xb6b9d4: r0 = LoadClassIdInstr(r1)
    //     0xb6b9d4: ldur            x0, [x1, #-1]
    //     0xb6b9d8: ubfx            x0, x0, #0xc, #0x14
    // 0xb6b9dc: d0 = 0.030000
    //     0xb6b9dc: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb6b9e0: ldr             d0, [x17, #0x238]
    // 0xb6b9e4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb6b9e4: sub             lr, x0, #0xffa
    //     0xb6b9e8: ldr             lr, [x21, lr, lsl #3]
    //     0xb6b9ec: blr             lr
    // 0xb6b9f0: stur            x0, [fp, #-0x20]
    // 0xb6b9f4: r0 = Divider()
    //     0xb6b9f4: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb6b9f8: mov             x3, x0
    // 0xb6b9fc: ldur            x0, [fp, #-0x20]
    // 0xb6ba00: stur            x3, [fp, #-0x30]
    // 0xb6ba04: StoreField: r3->field_1f = r0
    //     0xb6ba04: stur            w0, [x3, #0x1f]
    // 0xb6ba08: ldur            x0, [fp, #-8]
    // 0xb6ba0c: LoadField: r1 = r0->field_b
    //     0xb6ba0c: ldur            w1, [x0, #0xb]
    // 0xb6ba10: DecompressPointer r1
    //     0xb6ba10: add             x1, x1, HEAP, lsl #32
    // 0xb6ba14: cmp             w1, NULL
    // 0xb6ba18: b.eq            #0xb6c400
    // 0xb6ba1c: LoadField: r2 = r1->field_b
    //     0xb6ba1c: ldur            w2, [x1, #0xb]
    // 0xb6ba20: DecompressPointer r2
    //     0xb6ba20: add             x2, x2, HEAP, lsl #32
    // 0xb6ba24: LoadField: r1 = r2->field_73
    //     0xb6ba24: ldur            w1, [x2, #0x73]
    // 0xb6ba28: DecompressPointer r1
    //     0xb6ba28: add             x1, x1, HEAP, lsl #32
    // 0xb6ba2c: cmp             w1, NULL
    // 0xb6ba30: b.ne            #0xb6ba3c
    // 0xb6ba34: r2 = Null
    //     0xb6ba34: mov             x2, NULL
    // 0xb6ba38: b               #0xb6ba54
    // 0xb6ba3c: LoadField: r2 = r1->field_b
    //     0xb6ba3c: ldur            w2, [x1, #0xb]
    // 0xb6ba40: cbnz            w2, #0xb6ba4c
    // 0xb6ba44: r4 = false
    //     0xb6ba44: add             x4, NULL, #0x30  ; false
    // 0xb6ba48: b               #0xb6ba50
    // 0xb6ba4c: r4 = true
    //     0xb6ba4c: add             x4, NULL, #0x20  ; true
    // 0xb6ba50: mov             x2, x4
    // 0xb6ba54: cmp             w2, NULL
    // 0xb6ba58: b.ne            #0xb6ba64
    // 0xb6ba5c: r4 = false
    //     0xb6ba5c: add             x4, NULL, #0x30  ; false
    // 0xb6ba60: b               #0xb6ba68
    // 0xb6ba64: mov             x4, x2
    // 0xb6ba68: stur            x4, [fp, #-0x28]
    // 0xb6ba6c: cmp             w1, NULL
    // 0xb6ba70: b.ne            #0xb6ba7c
    // 0xb6ba74: r6 = Null
    //     0xb6ba74: mov             x6, NULL
    // 0xb6ba78: b               #0xb6ba84
    // 0xb6ba7c: LoadField: r2 = r1->field_b
    //     0xb6ba7c: ldur            w2, [x1, #0xb]
    // 0xb6ba80: mov             x6, x2
    // 0xb6ba84: ldur            x5, [fp, #-0x10]
    // 0xb6ba88: ldur            x2, [fp, #-0x18]
    // 0xb6ba8c: stur            x6, [fp, #-0x20]
    // 0xb6ba90: r1 = Function '<anonymous closure>':.
    //     0xb6ba90: add             x1, PP, #0x56, lsl #12  ; [pp+0x56200] AnonymousClosure: (0xb6e014), in [package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xb6b7f4)
    //     0xb6ba94: ldr             x1, [x1, #0x200]
    // 0xb6ba98: r0 = AllocateClosure()
    //     0xb6ba98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6ba9c: stur            x0, [fp, #-0x38]
    // 0xb6baa0: r0 = ListView()
    //     0xb6baa0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb6baa4: stur            x0, [fp, #-0x40]
    // 0xb6baa8: r16 = true
    //     0xb6baa8: add             x16, NULL, #0x20  ; true
    // 0xb6baac: r30 = Instance_Axis
    //     0xb6baac: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6bab0: stp             lr, x16, [SP]
    // 0xb6bab4: mov             x1, x0
    // 0xb6bab8: ldur            x2, [fp, #-0x38]
    // 0xb6babc: ldur            x3, [fp, #-0x20]
    // 0xb6bac0: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xb6bac0: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb6bac4: ldr             x4, [x4, #0x2d0]
    // 0xb6bac8: r0 = ListView.builder()
    //     0xb6bac8: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb6bacc: r0 = SizedBox()
    //     0xb6bacc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6bad0: mov             x1, x0
    // 0xb6bad4: r0 = 130.000000
    //     0xb6bad4: add             x0, PP, #0x42, lsl #12  ; [pp+0x427b0] 130
    //     0xb6bad8: ldr             x0, [x0, #0x7b0]
    // 0xb6badc: stur            x1, [fp, #-0x20]
    // 0xb6bae0: StoreField: r1->field_13 = r0
    //     0xb6bae0: stur            w0, [x1, #0x13]
    // 0xb6bae4: ldur            x0, [fp, #-0x40]
    // 0xb6bae8: StoreField: r1->field_b = r0
    //     0xb6bae8: stur            w0, [x1, #0xb]
    // 0xb6baec: r0 = Visibility()
    //     0xb6baec: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb6baf0: mov             x3, x0
    // 0xb6baf4: ldur            x0, [fp, #-0x20]
    // 0xb6baf8: stur            x3, [fp, #-0x38]
    // 0xb6bafc: StoreField: r3->field_b = r0
    //     0xb6bafc: stur            w0, [x3, #0xb]
    // 0xb6bb00: r0 = Instance_SizedBox
    //     0xb6bb00: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb6bb04: StoreField: r3->field_f = r0
    //     0xb6bb04: stur            w0, [x3, #0xf]
    // 0xb6bb08: ldur            x0, [fp, #-0x28]
    // 0xb6bb0c: StoreField: r3->field_13 = r0
    //     0xb6bb0c: stur            w0, [x3, #0x13]
    // 0xb6bb10: r0 = false
    //     0xb6bb10: add             x0, NULL, #0x30  ; false
    // 0xb6bb14: ArrayStore: r3[0] = r0  ; List_4
    //     0xb6bb14: stur            w0, [x3, #0x17]
    // 0xb6bb18: StoreField: r3->field_1b = r0
    //     0xb6bb18: stur            w0, [x3, #0x1b]
    // 0xb6bb1c: StoreField: r3->field_1f = r0
    //     0xb6bb1c: stur            w0, [x3, #0x1f]
    // 0xb6bb20: StoreField: r3->field_23 = r0
    //     0xb6bb20: stur            w0, [x3, #0x23]
    // 0xb6bb24: StoreField: r3->field_27 = r0
    //     0xb6bb24: stur            w0, [x3, #0x27]
    // 0xb6bb28: StoreField: r3->field_2b = r0
    //     0xb6bb28: stur            w0, [x3, #0x2b]
    // 0xb6bb2c: r1 = Null
    //     0xb6bb2c: mov             x1, NULL
    // 0xb6bb30: r2 = 6
    //     0xb6bb30: movz            x2, #0x6
    // 0xb6bb34: r0 = AllocateArray()
    //     0xb6bb34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6bb38: mov             x2, x0
    // 0xb6bb3c: ldur            x0, [fp, #-0x10]
    // 0xb6bb40: stur            x2, [fp, #-0x20]
    // 0xb6bb44: StoreField: r2->field_f = r0
    //     0xb6bb44: stur            w0, [x2, #0xf]
    // 0xb6bb48: ldur            x0, [fp, #-0x30]
    // 0xb6bb4c: StoreField: r2->field_13 = r0
    //     0xb6bb4c: stur            w0, [x2, #0x13]
    // 0xb6bb50: ldur            x0, [fp, #-0x38]
    // 0xb6bb54: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6bb54: stur            w0, [x2, #0x17]
    // 0xb6bb58: r1 = <Widget>
    //     0xb6bb58: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6bb5c: r0 = AllocateGrowableArray()
    //     0xb6bb5c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6bb60: mov             x1, x0
    // 0xb6bb64: ldur            x0, [fp, #-0x20]
    // 0xb6bb68: stur            x1, [fp, #-0x10]
    // 0xb6bb6c: StoreField: r1->field_f = r0
    //     0xb6bb6c: stur            w0, [x1, #0xf]
    // 0xb6bb70: r0 = 6
    //     0xb6bb70: movz            x0, #0x6
    // 0xb6bb74: StoreField: r1->field_b = r0
    //     0xb6bb74: stur            w0, [x1, #0xb]
    // 0xb6bb78: ldur            x2, [fp, #-8]
    // 0xb6bb7c: LoadField: r0 = r2->field_b
    //     0xb6bb7c: ldur            w0, [x2, #0xb]
    // 0xb6bb80: DecompressPointer r0
    //     0xb6bb80: add             x0, x0, HEAP, lsl #32
    // 0xb6bb84: cmp             w0, NULL
    // 0xb6bb88: b.eq            #0xb6c404
    // 0xb6bb8c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb6bb8c: ldur            w3, [x0, #0x17]
    // 0xb6bb90: DecompressPointer r3
    //     0xb6bb90: add             x3, x3, HEAP, lsl #32
    // 0xb6bb94: r0 = LoadClassIdInstr(r3)
    //     0xb6bb94: ldur            x0, [x3, #-1]
    //     0xb6bb98: ubfx            x0, x0, #0xc, #0x14
    // 0xb6bb9c: r16 = "size"
    //     0xb6bb9c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xb6bba0: ldr             x16, [x16, #0x9c0]
    // 0xb6bba4: stp             x16, x3, [SP]
    // 0xb6bba8: mov             lr, x0
    // 0xb6bbac: ldr             lr, [x21, lr, lsl #3]
    // 0xb6bbb0: blr             lr
    // 0xb6bbb4: tbnz            w0, #4, #0xb6bcc0
    // 0xb6bbb8: ldur            x2, [fp, #-0x18]
    // 0xb6bbbc: ldur            x0, [fp, #-0x10]
    // 0xb6bbc0: LoadField: r1 = r2->field_13
    //     0xb6bbc0: ldur            w1, [x2, #0x13]
    // 0xb6bbc4: DecompressPointer r1
    //     0xb6bbc4: add             x1, x1, HEAP, lsl #32
    // 0xb6bbc8: r0 = of()
    //     0xb6bbc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6bbcc: LoadField: r1 = r0->field_87
    //     0xb6bbcc: ldur            w1, [x0, #0x87]
    // 0xb6bbd0: DecompressPointer r1
    //     0xb6bbd0: add             x1, x1, HEAP, lsl #32
    // 0xb6bbd4: LoadField: r0 = r1->field_7
    //     0xb6bbd4: ldur            w0, [x1, #7]
    // 0xb6bbd8: DecompressPointer r0
    //     0xb6bbd8: add             x0, x0, HEAP, lsl #32
    // 0xb6bbdc: r16 = 16.000000
    //     0xb6bbdc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6bbe0: ldr             x16, [x16, #0x188]
    // 0xb6bbe4: r30 = Instance_Color
    //     0xb6bbe4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6bbe8: stp             lr, x16, [SP]
    // 0xb6bbec: mov             x1, x0
    // 0xb6bbf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6bbf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6bbf4: ldr             x4, [x4, #0xaa0]
    // 0xb6bbf8: r0 = copyWith()
    //     0xb6bbf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6bbfc: stur            x0, [fp, #-0x20]
    // 0xb6bc00: r0 = Text()
    //     0xb6bc00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6bc04: mov             x1, x0
    // 0xb6bc08: r0 = "Select Size"
    //     0xb6bc08: add             x0, PP, #0x52, lsl #12  ; [pp+0x52370] "Select Size"
    //     0xb6bc0c: ldr             x0, [x0, #0x370]
    // 0xb6bc10: stur            x1, [fp, #-0x28]
    // 0xb6bc14: StoreField: r1->field_b = r0
    //     0xb6bc14: stur            w0, [x1, #0xb]
    // 0xb6bc18: ldur            x0, [fp, #-0x20]
    // 0xb6bc1c: StoreField: r1->field_13 = r0
    //     0xb6bc1c: stur            w0, [x1, #0x13]
    // 0xb6bc20: r0 = Padding()
    //     0xb6bc20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6bc24: mov             x2, x0
    // 0xb6bc28: r0 = Instance_EdgeInsets
    //     0xb6bc28: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb6bc2c: ldr             x0, [x0, #0xa78]
    // 0xb6bc30: stur            x2, [fp, #-0x20]
    // 0xb6bc34: StoreField: r2->field_f = r0
    //     0xb6bc34: stur            w0, [x2, #0xf]
    // 0xb6bc38: ldur            x0, [fp, #-0x28]
    // 0xb6bc3c: StoreField: r2->field_b = r0
    //     0xb6bc3c: stur            w0, [x2, #0xb]
    // 0xb6bc40: ldur            x0, [fp, #-0x10]
    // 0xb6bc44: LoadField: r1 = r0->field_b
    //     0xb6bc44: ldur            w1, [x0, #0xb]
    // 0xb6bc48: LoadField: r3 = r0->field_f
    //     0xb6bc48: ldur            w3, [x0, #0xf]
    // 0xb6bc4c: DecompressPointer r3
    //     0xb6bc4c: add             x3, x3, HEAP, lsl #32
    // 0xb6bc50: LoadField: r4 = r3->field_b
    //     0xb6bc50: ldur            w4, [x3, #0xb]
    // 0xb6bc54: r3 = LoadInt32Instr(r1)
    //     0xb6bc54: sbfx            x3, x1, #1, #0x1f
    // 0xb6bc58: stur            x3, [fp, #-0x48]
    // 0xb6bc5c: r1 = LoadInt32Instr(r4)
    //     0xb6bc5c: sbfx            x1, x4, #1, #0x1f
    // 0xb6bc60: cmp             x3, x1
    // 0xb6bc64: b.ne            #0xb6bc70
    // 0xb6bc68: mov             x1, x0
    // 0xb6bc6c: r0 = _growToNextCapacity()
    //     0xb6bc6c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6bc70: ldur            x2, [fp, #-0x10]
    // 0xb6bc74: ldur            x3, [fp, #-0x48]
    // 0xb6bc78: add             x0, x3, #1
    // 0xb6bc7c: lsl             x1, x0, #1
    // 0xb6bc80: StoreField: r2->field_b = r1
    //     0xb6bc80: stur            w1, [x2, #0xb]
    // 0xb6bc84: LoadField: r1 = r2->field_f
    //     0xb6bc84: ldur            w1, [x2, #0xf]
    // 0xb6bc88: DecompressPointer r1
    //     0xb6bc88: add             x1, x1, HEAP, lsl #32
    // 0xb6bc8c: ldur            x0, [fp, #-0x20]
    // 0xb6bc90: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6bc90: add             x25, x1, x3, lsl #2
    //     0xb6bc94: add             x25, x25, #0xf
    //     0xb6bc98: str             w0, [x25]
    //     0xb6bc9c: tbz             w0, #0, #0xb6bcb8
    //     0xb6bca0: ldurb           w16, [x1, #-1]
    //     0xb6bca4: ldurb           w17, [x0, #-1]
    //     0xb6bca8: and             x16, x17, x16, lsr #2
    //     0xb6bcac: tst             x16, HEAP, lsr #32
    //     0xb6bcb0: b.eq            #0xb6bcb8
    //     0xb6bcb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6bcb8: mov             x3, x2
    // 0xb6bcbc: b               #0xb6bdc8
    // 0xb6bcc0: ldur            x3, [fp, #-0x18]
    // 0xb6bcc4: ldur            x2, [fp, #-0x10]
    // 0xb6bcc8: r0 = Instance_EdgeInsets
    //     0xb6bcc8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb6bccc: ldr             x0, [x0, #0xa78]
    // 0xb6bcd0: LoadField: r1 = r3->field_13
    //     0xb6bcd0: ldur            w1, [x3, #0x13]
    // 0xb6bcd4: DecompressPointer r1
    //     0xb6bcd4: add             x1, x1, HEAP, lsl #32
    // 0xb6bcd8: r0 = of()
    //     0xb6bcd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6bcdc: LoadField: r1 = r0->field_87
    //     0xb6bcdc: ldur            w1, [x0, #0x87]
    // 0xb6bce0: DecompressPointer r1
    //     0xb6bce0: add             x1, x1, HEAP, lsl #32
    // 0xb6bce4: LoadField: r0 = r1->field_7
    //     0xb6bce4: ldur            w0, [x1, #7]
    // 0xb6bce8: DecompressPointer r0
    //     0xb6bce8: add             x0, x0, HEAP, lsl #32
    // 0xb6bcec: r16 = 16.000000
    //     0xb6bcec: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6bcf0: ldr             x16, [x16, #0x188]
    // 0xb6bcf4: r30 = Instance_Color
    //     0xb6bcf4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6bcf8: stp             lr, x16, [SP]
    // 0xb6bcfc: mov             x1, x0
    // 0xb6bd00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6bd00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6bd04: ldr             x4, [x4, #0xaa0]
    // 0xb6bd08: r0 = copyWith()
    //     0xb6bd08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6bd0c: stur            x0, [fp, #-0x20]
    // 0xb6bd10: r0 = Text()
    //     0xb6bd10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6bd14: mov             x1, x0
    // 0xb6bd18: r0 = "Variant"
    //     0xb6bd18: add             x0, PP, #0x52, lsl #12  ; [pp+0x52738] "Variant"
    //     0xb6bd1c: ldr             x0, [x0, #0x738]
    // 0xb6bd20: stur            x1, [fp, #-0x28]
    // 0xb6bd24: StoreField: r1->field_b = r0
    //     0xb6bd24: stur            w0, [x1, #0xb]
    // 0xb6bd28: ldur            x0, [fp, #-0x20]
    // 0xb6bd2c: StoreField: r1->field_13 = r0
    //     0xb6bd2c: stur            w0, [x1, #0x13]
    // 0xb6bd30: r0 = Padding()
    //     0xb6bd30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6bd34: mov             x2, x0
    // 0xb6bd38: r0 = Instance_EdgeInsets
    //     0xb6bd38: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb6bd3c: ldr             x0, [x0, #0xa78]
    // 0xb6bd40: stur            x2, [fp, #-0x20]
    // 0xb6bd44: StoreField: r2->field_f = r0
    //     0xb6bd44: stur            w0, [x2, #0xf]
    // 0xb6bd48: ldur            x0, [fp, #-0x28]
    // 0xb6bd4c: StoreField: r2->field_b = r0
    //     0xb6bd4c: stur            w0, [x2, #0xb]
    // 0xb6bd50: ldur            x0, [fp, #-0x10]
    // 0xb6bd54: LoadField: r1 = r0->field_b
    //     0xb6bd54: ldur            w1, [x0, #0xb]
    // 0xb6bd58: LoadField: r3 = r0->field_f
    //     0xb6bd58: ldur            w3, [x0, #0xf]
    // 0xb6bd5c: DecompressPointer r3
    //     0xb6bd5c: add             x3, x3, HEAP, lsl #32
    // 0xb6bd60: LoadField: r4 = r3->field_b
    //     0xb6bd60: ldur            w4, [x3, #0xb]
    // 0xb6bd64: r3 = LoadInt32Instr(r1)
    //     0xb6bd64: sbfx            x3, x1, #1, #0x1f
    // 0xb6bd68: stur            x3, [fp, #-0x48]
    // 0xb6bd6c: r1 = LoadInt32Instr(r4)
    //     0xb6bd6c: sbfx            x1, x4, #1, #0x1f
    // 0xb6bd70: cmp             x3, x1
    // 0xb6bd74: b.ne            #0xb6bd80
    // 0xb6bd78: mov             x1, x0
    // 0xb6bd7c: r0 = _growToNextCapacity()
    //     0xb6bd7c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6bd80: ldur            x3, [fp, #-0x10]
    // 0xb6bd84: ldur            x2, [fp, #-0x48]
    // 0xb6bd88: add             x0, x2, #1
    // 0xb6bd8c: lsl             x1, x0, #1
    // 0xb6bd90: StoreField: r3->field_b = r1
    //     0xb6bd90: stur            w1, [x3, #0xb]
    // 0xb6bd94: LoadField: r1 = r3->field_f
    //     0xb6bd94: ldur            w1, [x3, #0xf]
    // 0xb6bd98: DecompressPointer r1
    //     0xb6bd98: add             x1, x1, HEAP, lsl #32
    // 0xb6bd9c: ldur            x0, [fp, #-0x20]
    // 0xb6bda0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb6bda0: add             x25, x1, x2, lsl #2
    //     0xb6bda4: add             x25, x25, #0xf
    //     0xb6bda8: str             w0, [x25]
    //     0xb6bdac: tbz             w0, #0, #0xb6bdc8
    //     0xb6bdb0: ldurb           w16, [x1, #-1]
    //     0xb6bdb4: ldurb           w17, [x0, #-1]
    //     0xb6bdb8: and             x16, x17, x16, lsr #2
    //     0xb6bdbc: tst             x16, HEAP, lsr #32
    //     0xb6bdc0: b.eq            #0xb6bdc8
    //     0xb6bdc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6bdc8: ldur            x0, [fp, #-8]
    // 0xb6bdcc: LoadField: r1 = r0->field_b
    //     0xb6bdcc: ldur            w1, [x0, #0xb]
    // 0xb6bdd0: DecompressPointer r1
    //     0xb6bdd0: add             x1, x1, HEAP, lsl #32
    // 0xb6bdd4: cmp             w1, NULL
    // 0xb6bdd8: b.eq            #0xb6c408
    // 0xb6bddc: LoadField: r2 = r1->field_b
    //     0xb6bddc: ldur            w2, [x1, #0xb]
    // 0xb6bde0: DecompressPointer r2
    //     0xb6bde0: add             x2, x2, HEAP, lsl #32
    // 0xb6bde4: LoadField: r4 = r2->field_73
    //     0xb6bde4: ldur            w4, [x2, #0x73]
    // 0xb6bde8: DecompressPointer r4
    //     0xb6bde8: add             x4, x4, HEAP, lsl #32
    // 0xb6bdec: cmp             w4, NULL
    // 0xb6bdf0: b.ne            #0xb6bdfc
    // 0xb6bdf4: r1 = Null
    //     0xb6bdf4: mov             x1, NULL
    // 0xb6bdf8: b               #0xb6be14
    // 0xb6bdfc: LoadField: r1 = r4->field_b
    //     0xb6bdfc: ldur            w1, [x4, #0xb]
    // 0xb6be00: cbz             w1, #0xb6be0c
    // 0xb6be04: r5 = false
    //     0xb6be04: add             x5, NULL, #0x30  ; false
    // 0xb6be08: b               #0xb6be10
    // 0xb6be0c: r5 = true
    //     0xb6be0c: add             x5, NULL, #0x20  ; true
    // 0xb6be10: mov             x1, x5
    // 0xb6be14: cmp             w1, NULL
    // 0xb6be18: b.eq            #0xb6be20
    // 0xb6be1c: tbnz            w1, #4, #0xb6bf48
    // 0xb6be20: LoadField: r1 = r2->field_6f
    //     0xb6be20: ldur            w1, [x2, #0x6f]
    // 0xb6be24: DecompressPointer r1
    //     0xb6be24: add             x1, x1, HEAP, lsl #32
    // 0xb6be28: cmp             w1, NULL
    // 0xb6be2c: b.ne            #0xb6be38
    // 0xb6be30: r1 = Null
    //     0xb6be30: mov             x1, NULL
    // 0xb6be34: b               #0xb6be40
    // 0xb6be38: LoadField: r2 = r1->field_b
    //     0xb6be38: ldur            w2, [x1, #0xb]
    // 0xb6be3c: mov             x1, x2
    // 0xb6be40: cmp             w1, NULL
    // 0xb6be44: b.ne            #0xb6be50
    // 0xb6be48: r1 = 0
    //     0xb6be48: movz            x1, #0
    // 0xb6be4c: b               #0xb6be58
    // 0xb6be50: r2 = LoadInt32Instr(r1)
    //     0xb6be50: sbfx            x2, x1, #1, #0x1f
    // 0xb6be54: mov             x1, x2
    // 0xb6be58: lsl             x4, x1, #1
    // 0xb6be5c: ldur            x2, [fp, #-0x18]
    // 0xb6be60: stur            x4, [fp, #-0x20]
    // 0xb6be64: r1 = Function '<anonymous closure>':.
    //     0xb6be64: add             x1, PP, #0x56, lsl #12  ; [pp+0x56208] AnonymousClosure: (0xb6dc80), in [package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xb6b7f4)
    //     0xb6be68: ldr             x1, [x1, #0x208]
    // 0xb6be6c: r0 = AllocateClosure()
    //     0xb6be6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6be70: stur            x0, [fp, #-0x28]
    // 0xb6be74: r0 = ListView()
    //     0xb6be74: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb6be78: stur            x0, [fp, #-0x30]
    // 0xb6be7c: r16 = Instance_Axis
    //     0xb6be7c: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6be80: str             x16, [SP]
    // 0xb6be84: mov             x1, x0
    // 0xb6be88: ldur            x2, [fp, #-0x28]
    // 0xb6be8c: ldur            x3, [fp, #-0x20]
    // 0xb6be90: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xb6be90: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xb6be94: ldr             x4, [x4, #0x4a0]
    // 0xb6be98: r0 = ListView.builder()
    //     0xb6be98: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb6be9c: r0 = SizedBox()
    //     0xb6be9c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6bea0: mov             x2, x0
    // 0xb6bea4: r0 = inf
    //     0xb6bea4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb6bea8: ldr             x0, [x0, #0x9f8]
    // 0xb6beac: stur            x2, [fp, #-0x20]
    // 0xb6beb0: StoreField: r2->field_f = r0
    //     0xb6beb0: stur            w0, [x2, #0xf]
    // 0xb6beb4: r3 = 55.000000
    //     0xb6beb4: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb6beb8: ldr             x3, [x3, #0x9b8]
    // 0xb6bebc: StoreField: r2->field_13 = r3
    //     0xb6bebc: stur            w3, [x2, #0x13]
    // 0xb6bec0: ldur            x1, [fp, #-0x30]
    // 0xb6bec4: StoreField: r2->field_b = r1
    //     0xb6bec4: stur            w1, [x2, #0xb]
    // 0xb6bec8: ldur            x3, [fp, #-0x10]
    // 0xb6becc: LoadField: r1 = r3->field_b
    //     0xb6becc: ldur            w1, [x3, #0xb]
    // 0xb6bed0: LoadField: r4 = r3->field_f
    //     0xb6bed0: ldur            w4, [x3, #0xf]
    // 0xb6bed4: DecompressPointer r4
    //     0xb6bed4: add             x4, x4, HEAP, lsl #32
    // 0xb6bed8: LoadField: r5 = r4->field_b
    //     0xb6bed8: ldur            w5, [x4, #0xb]
    // 0xb6bedc: r4 = LoadInt32Instr(r1)
    //     0xb6bedc: sbfx            x4, x1, #1, #0x1f
    // 0xb6bee0: stur            x4, [fp, #-0x48]
    // 0xb6bee4: r1 = LoadInt32Instr(r5)
    //     0xb6bee4: sbfx            x1, x5, #1, #0x1f
    // 0xb6bee8: cmp             x4, x1
    // 0xb6beec: b.ne            #0xb6bef8
    // 0xb6bef0: mov             x1, x3
    // 0xb6bef4: r0 = _growToNextCapacity()
    //     0xb6bef4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6bef8: ldur            x5, [fp, #-0x10]
    // 0xb6befc: ldur            x2, [fp, #-0x48]
    // 0xb6bf00: add             x0, x2, #1
    // 0xb6bf04: lsl             x1, x0, #1
    // 0xb6bf08: StoreField: r5->field_b = r1
    //     0xb6bf08: stur            w1, [x5, #0xb]
    // 0xb6bf0c: LoadField: r1 = r5->field_f
    //     0xb6bf0c: ldur            w1, [x5, #0xf]
    // 0xb6bf10: DecompressPointer r1
    //     0xb6bf10: add             x1, x1, HEAP, lsl #32
    // 0xb6bf14: ldur            x0, [fp, #-0x20]
    // 0xb6bf18: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb6bf18: add             x25, x1, x2, lsl #2
    //     0xb6bf1c: add             x25, x25, #0xf
    //     0xb6bf20: str             w0, [x25]
    //     0xb6bf24: tbz             w0, #0, #0xb6bf40
    //     0xb6bf28: ldurb           w16, [x1, #-1]
    //     0xb6bf2c: ldurb           w17, [x0, #-1]
    //     0xb6bf30: and             x16, x17, x16, lsr #2
    //     0xb6bf34: tst             x16, HEAP, lsr #32
    //     0xb6bf38: b.eq            #0xb6bf40
    //     0xb6bf3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6bf40: mov             x2, x5
    // 0xb6bf44: b               #0xb6c0bc
    // 0xb6bf48: mov             x5, x3
    // 0xb6bf4c: r3 = 55.000000
    //     0xb6bf4c: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb6bf50: ldr             x3, [x3, #0x9b8]
    // 0xb6bf54: cmp             w4, NULL
    // 0xb6bf58: b.ne            #0xb6bf68
    // 0xb6bf5c: ldur            x6, [fp, #-8]
    // 0xb6bf60: r0 = Null
    //     0xb6bf60: mov             x0, NULL
    // 0xb6bf64: b               #0xb6bfbc
    // 0xb6bf68: ldur            x6, [fp, #-8]
    // 0xb6bf6c: LoadField: r2 = r6->field_1f
    //     0xb6bf6c: ldur            x2, [x6, #0x1f]
    // 0xb6bf70: LoadField: r0 = r4->field_b
    //     0xb6bf70: ldur            w0, [x4, #0xb]
    // 0xb6bf74: r1 = LoadInt32Instr(r0)
    //     0xb6bf74: sbfx            x1, x0, #1, #0x1f
    // 0xb6bf78: mov             x0, x1
    // 0xb6bf7c: mov             x1, x2
    // 0xb6bf80: cmp             x1, x0
    // 0xb6bf84: b.hs            #0xb6c40c
    // 0xb6bf88: LoadField: r0 = r4->field_f
    //     0xb6bf88: ldur            w0, [x4, #0xf]
    // 0xb6bf8c: DecompressPointer r0
    //     0xb6bf8c: add             x0, x0, HEAP, lsl #32
    // 0xb6bf90: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xb6bf90: add             x16, x0, x2, lsl #2
    //     0xb6bf94: ldur            w1, [x16, #0xf]
    // 0xb6bf98: DecompressPointer r1
    //     0xb6bf98: add             x1, x1, HEAP, lsl #32
    // 0xb6bf9c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb6bf9c: ldur            w0, [x1, #0x17]
    // 0xb6bfa0: DecompressPointer r0
    //     0xb6bfa0: add             x0, x0, HEAP, lsl #32
    // 0xb6bfa4: cmp             w0, NULL
    // 0xb6bfa8: b.ne            #0xb6bfb4
    // 0xb6bfac: r0 = Null
    //     0xb6bfac: mov             x0, NULL
    // 0xb6bfb0: b               #0xb6bfbc
    // 0xb6bfb4: LoadField: r1 = r0->field_b
    //     0xb6bfb4: ldur            w1, [x0, #0xb]
    // 0xb6bfb8: mov             x0, x1
    // 0xb6bfbc: cmp             w0, NULL
    // 0xb6bfc0: b.ne            #0xb6bfcc
    // 0xb6bfc4: r0 = 0
    //     0xb6bfc4: movz            x0, #0
    // 0xb6bfc8: b               #0xb6bfd4
    // 0xb6bfcc: r1 = LoadInt32Instr(r0)
    //     0xb6bfcc: sbfx            x1, x0, #1, #0x1f
    // 0xb6bfd0: mov             x0, x1
    // 0xb6bfd4: lsl             x4, x0, #1
    // 0xb6bfd8: ldur            x2, [fp, #-0x18]
    // 0xb6bfdc: stur            x4, [fp, #-0x20]
    // 0xb6bfe0: r1 = Function '<anonymous closure>':.
    //     0xb6bfe0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56210] AnonymousClosure: (0xb6d89c), in [package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xb6b7f4)
    //     0xb6bfe4: ldr             x1, [x1, #0x210]
    // 0xb6bfe8: r0 = AllocateClosure()
    //     0xb6bfe8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6bfec: stur            x0, [fp, #-0x28]
    // 0xb6bff0: r0 = ListView()
    //     0xb6bff0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb6bff4: stur            x0, [fp, #-0x30]
    // 0xb6bff8: r16 = Instance_Axis
    //     0xb6bff8: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6bffc: str             x16, [SP]
    // 0xb6c000: mov             x1, x0
    // 0xb6c004: ldur            x2, [fp, #-0x28]
    // 0xb6c008: ldur            x3, [fp, #-0x20]
    // 0xb6c00c: r4 = const [0, 0x4, 0x1, 0x3, scrollDirection, 0x3, null]
    //     0xb6c00c: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(7) [0, 0x4, 0x1, 0x3, "scrollDirection", 0x3, Null]
    //     0xb6c010: ldr             x4, [x4, #0x4a0]
    // 0xb6c014: r0 = ListView.builder()
    //     0xb6c014: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb6c018: r0 = SizedBox()
    //     0xb6c018: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6c01c: mov             x2, x0
    // 0xb6c020: r0 = inf
    //     0xb6c020: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb6c024: ldr             x0, [x0, #0x9f8]
    // 0xb6c028: stur            x2, [fp, #-0x20]
    // 0xb6c02c: StoreField: r2->field_f = r0
    //     0xb6c02c: stur            w0, [x2, #0xf]
    // 0xb6c030: r1 = 55.000000
    //     0xb6c030: add             x1, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xb6c034: ldr             x1, [x1, #0x9b8]
    // 0xb6c038: StoreField: r2->field_13 = r1
    //     0xb6c038: stur            w1, [x2, #0x13]
    // 0xb6c03c: ldur            x1, [fp, #-0x30]
    // 0xb6c040: StoreField: r2->field_b = r1
    //     0xb6c040: stur            w1, [x2, #0xb]
    // 0xb6c044: ldur            x3, [fp, #-0x10]
    // 0xb6c048: LoadField: r1 = r3->field_b
    //     0xb6c048: ldur            w1, [x3, #0xb]
    // 0xb6c04c: LoadField: r4 = r3->field_f
    //     0xb6c04c: ldur            w4, [x3, #0xf]
    // 0xb6c050: DecompressPointer r4
    //     0xb6c050: add             x4, x4, HEAP, lsl #32
    // 0xb6c054: LoadField: r5 = r4->field_b
    //     0xb6c054: ldur            w5, [x4, #0xb]
    // 0xb6c058: r4 = LoadInt32Instr(r1)
    //     0xb6c058: sbfx            x4, x1, #1, #0x1f
    // 0xb6c05c: stur            x4, [fp, #-0x48]
    // 0xb6c060: r1 = LoadInt32Instr(r5)
    //     0xb6c060: sbfx            x1, x5, #1, #0x1f
    // 0xb6c064: cmp             x4, x1
    // 0xb6c068: b.ne            #0xb6c074
    // 0xb6c06c: mov             x1, x3
    // 0xb6c070: r0 = _growToNextCapacity()
    //     0xb6c070: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6c074: ldur            x2, [fp, #-0x10]
    // 0xb6c078: ldur            x3, [fp, #-0x48]
    // 0xb6c07c: add             x0, x3, #1
    // 0xb6c080: lsl             x1, x0, #1
    // 0xb6c084: StoreField: r2->field_b = r1
    //     0xb6c084: stur            w1, [x2, #0xb]
    // 0xb6c088: LoadField: r1 = r2->field_f
    //     0xb6c088: ldur            w1, [x2, #0xf]
    // 0xb6c08c: DecompressPointer r1
    //     0xb6c08c: add             x1, x1, HEAP, lsl #32
    // 0xb6c090: ldur            x0, [fp, #-0x20]
    // 0xb6c094: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6c094: add             x25, x1, x3, lsl #2
    //     0xb6c098: add             x25, x25, #0xf
    //     0xb6c09c: str             w0, [x25]
    //     0xb6c0a0: tbz             w0, #0, #0xb6c0bc
    //     0xb6c0a4: ldurb           w16, [x1, #-1]
    //     0xb6c0a8: ldurb           w17, [x0, #-1]
    //     0xb6c0ac: and             x16, x17, x16, lsr #2
    //     0xb6c0b0: tst             x16, HEAP, lsr #32
    //     0xb6c0b4: b.eq            #0xb6c0bc
    //     0xb6c0b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6c0bc: ldur            x0, [fp, #-8]
    // 0xb6c0c0: ldur            x1, [fp, #-0x18]
    // 0xb6c0c4: r16 = <EdgeInsets>
    //     0xb6c0c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb6c0c8: ldr             x16, [x16, #0xda0]
    // 0xb6c0cc: r30 = Instance_EdgeInsets
    //     0xb6c0cc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb6c0d0: ldr             lr, [lr, #0x1f0]
    // 0xb6c0d4: stp             lr, x16, [SP]
    // 0xb6c0d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6c0d8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb6c0dc: r0 = all()
    //     0xb6c0dc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb6c0e0: ldur            x2, [fp, #-0x18]
    // 0xb6c0e4: stur            x0, [fp, #-0x20]
    // 0xb6c0e8: LoadField: r1 = r2->field_13
    //     0xb6c0e8: ldur            w1, [x2, #0x13]
    // 0xb6c0ec: DecompressPointer r1
    //     0xb6c0ec: add             x1, x1, HEAP, lsl #32
    // 0xb6c0f0: r0 = of()
    //     0xb6c0f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6c0f4: LoadField: r1 = r0->field_5b
    //     0xb6c0f4: ldur            w1, [x0, #0x5b]
    // 0xb6c0f8: DecompressPointer r1
    //     0xb6c0f8: add             x1, x1, HEAP, lsl #32
    // 0xb6c0fc: r16 = <Color>
    //     0xb6c0fc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb6c100: ldr             x16, [x16, #0xf80]
    // 0xb6c104: stp             x1, x16, [SP]
    // 0xb6c108: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6c108: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb6c10c: r0 = all()
    //     0xb6c10c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb6c110: stur            x0, [fp, #-0x28]
    // 0xb6c114: r16 = <Color>
    //     0xb6c114: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb6c118: ldr             x16, [x16, #0xf80]
    // 0xb6c11c: r30 = Instance_Color
    //     0xb6c11c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6c120: stp             lr, x16, [SP]
    // 0xb6c124: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6c124: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb6c128: r0 = all()
    //     0xb6c128: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb6c12c: stur            x0, [fp, #-0x30]
    // 0xb6c130: r0 = Radius()
    //     0xb6c130: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6c134: d0 = 30.000000
    //     0xb6c134: fmov            d0, #30.00000000
    // 0xb6c138: stur            x0, [fp, #-0x38]
    // 0xb6c13c: StoreField: r0->field_7 = d0
    //     0xb6c13c: stur            d0, [x0, #7]
    // 0xb6c140: StoreField: r0->field_f = d0
    //     0xb6c140: stur            d0, [x0, #0xf]
    // 0xb6c144: r0 = BorderRadius()
    //     0xb6c144: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb6c148: mov             x1, x0
    // 0xb6c14c: ldur            x0, [fp, #-0x38]
    // 0xb6c150: stur            x1, [fp, #-0x40]
    // 0xb6c154: StoreField: r1->field_7 = r0
    //     0xb6c154: stur            w0, [x1, #7]
    // 0xb6c158: StoreField: r1->field_b = r0
    //     0xb6c158: stur            w0, [x1, #0xb]
    // 0xb6c15c: StoreField: r1->field_f = r0
    //     0xb6c15c: stur            w0, [x1, #0xf]
    // 0xb6c160: StoreField: r1->field_13 = r0
    //     0xb6c160: stur            w0, [x1, #0x13]
    // 0xb6c164: r0 = RoundedRectangleBorder()
    //     0xb6c164: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb6c168: mov             x1, x0
    // 0xb6c16c: ldur            x0, [fp, #-0x40]
    // 0xb6c170: StoreField: r1->field_b = r0
    //     0xb6c170: stur            w0, [x1, #0xb]
    // 0xb6c174: r0 = Instance_BorderSide
    //     0xb6c174: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb6c178: ldr             x0, [x0, #0xe20]
    // 0xb6c17c: StoreField: r1->field_7 = r0
    //     0xb6c17c: stur            w0, [x1, #7]
    // 0xb6c180: r16 = <RoundedRectangleBorder>
    //     0xb6c180: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb6c184: ldr             x16, [x16, #0xf78]
    // 0xb6c188: stp             x1, x16, [SP]
    // 0xb6c18c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6c18c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb6c190: r0 = all()
    //     0xb6c190: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb6c194: stur            x0, [fp, #-0x38]
    // 0xb6c198: r0 = ButtonStyle()
    //     0xb6c198: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb6c19c: mov             x1, x0
    // 0xb6c1a0: ldur            x0, [fp, #-0x28]
    // 0xb6c1a4: stur            x1, [fp, #-0x40]
    // 0xb6c1a8: StoreField: r1->field_b = r0
    //     0xb6c1a8: stur            w0, [x1, #0xb]
    // 0xb6c1ac: ldur            x0, [fp, #-0x30]
    // 0xb6c1b0: StoreField: r1->field_f = r0
    //     0xb6c1b0: stur            w0, [x1, #0xf]
    // 0xb6c1b4: ldur            x0, [fp, #-0x20]
    // 0xb6c1b8: StoreField: r1->field_23 = r0
    //     0xb6c1b8: stur            w0, [x1, #0x23]
    // 0xb6c1bc: ldur            x0, [fp, #-0x38]
    // 0xb6c1c0: StoreField: r1->field_43 = r0
    //     0xb6c1c0: stur            w0, [x1, #0x43]
    // 0xb6c1c4: r0 = TextButtonThemeData()
    //     0xb6c1c4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb6c1c8: mov             x2, x0
    // 0xb6c1cc: ldur            x0, [fp, #-0x40]
    // 0xb6c1d0: stur            x2, [fp, #-0x20]
    // 0xb6c1d4: StoreField: r2->field_7 = r0
    //     0xb6c1d4: stur            w0, [x2, #7]
    // 0xb6c1d8: ldur            x0, [fp, #-8]
    // 0xb6c1dc: LoadField: r1 = r0->field_b
    //     0xb6c1dc: ldur            w1, [x0, #0xb]
    // 0xb6c1e0: DecompressPointer r1
    //     0xb6c1e0: add             x1, x1, HEAP, lsl #32
    // 0xb6c1e4: cmp             w1, NULL
    // 0xb6c1e8: b.eq            #0xb6c410
    // 0xb6c1ec: LoadField: r0 = r1->field_b
    //     0xb6c1ec: ldur            w0, [x1, #0xb]
    // 0xb6c1f0: DecompressPointer r0
    //     0xb6c1f0: add             x0, x0, HEAP, lsl #32
    // 0xb6c1f4: LoadField: r1 = r0->field_f
    //     0xb6c1f4: ldur            w1, [x0, #0xf]
    // 0xb6c1f8: DecompressPointer r1
    //     0xb6c1f8: add             x1, x1, HEAP, lsl #32
    // 0xb6c1fc: cmp             w1, NULL
    // 0xb6c200: b.ne            #0xb6c208
    // 0xb6c204: r1 = ""
    //     0xb6c204: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6c208: ldur            x0, [fp, #-0x18]
    // 0xb6c20c: ldur            x3, [fp, #-0x10]
    // 0xb6c210: r0 = capitalizeFirstWord()
    //     0xb6c210: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb6c214: ldur            x2, [fp, #-0x18]
    // 0xb6c218: stur            x0, [fp, #-8]
    // 0xb6c21c: LoadField: r1 = r2->field_13
    //     0xb6c21c: ldur            w1, [x2, #0x13]
    // 0xb6c220: DecompressPointer r1
    //     0xb6c220: add             x1, x1, HEAP, lsl #32
    // 0xb6c224: r0 = of()
    //     0xb6c224: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6c228: LoadField: r1 = r0->field_87
    //     0xb6c228: ldur            w1, [x0, #0x87]
    // 0xb6c22c: DecompressPointer r1
    //     0xb6c22c: add             x1, x1, HEAP, lsl #32
    // 0xb6c230: LoadField: r0 = r1->field_7
    //     0xb6c230: ldur            w0, [x1, #7]
    // 0xb6c234: DecompressPointer r0
    //     0xb6c234: add             x0, x0, HEAP, lsl #32
    // 0xb6c238: r16 = 16.000000
    //     0xb6c238: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6c23c: ldr             x16, [x16, #0x188]
    // 0xb6c240: r30 = Instance_Color
    //     0xb6c240: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6c244: stp             lr, x16, [SP]
    // 0xb6c248: mov             x1, x0
    // 0xb6c24c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6c24c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6c250: ldr             x4, [x4, #0xaa0]
    // 0xb6c254: r0 = copyWith()
    //     0xb6c254: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6c258: stur            x0, [fp, #-0x28]
    // 0xb6c25c: r0 = Text()
    //     0xb6c25c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6c260: mov             x3, x0
    // 0xb6c264: ldur            x0, [fp, #-8]
    // 0xb6c268: stur            x3, [fp, #-0x30]
    // 0xb6c26c: StoreField: r3->field_b = r0
    //     0xb6c26c: stur            w0, [x3, #0xb]
    // 0xb6c270: ldur            x0, [fp, #-0x28]
    // 0xb6c274: StoreField: r3->field_13 = r0
    //     0xb6c274: stur            w0, [x3, #0x13]
    // 0xb6c278: ldur            x2, [fp, #-0x18]
    // 0xb6c27c: r1 = Function '<anonymous closure>':.
    //     0xb6c27c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56218] AnonymousClosure: (0xb6c414), in [package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xb6b7f4)
    //     0xb6c280: ldr             x1, [x1, #0x218]
    // 0xb6c284: r0 = AllocateClosure()
    //     0xb6c284: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6c288: stur            x0, [fp, #-8]
    // 0xb6c28c: r0 = TextButton()
    //     0xb6c28c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb6c290: mov             x1, x0
    // 0xb6c294: ldur            x0, [fp, #-8]
    // 0xb6c298: stur            x1, [fp, #-0x18]
    // 0xb6c29c: StoreField: r1->field_b = r0
    //     0xb6c29c: stur            w0, [x1, #0xb]
    // 0xb6c2a0: r0 = false
    //     0xb6c2a0: add             x0, NULL, #0x30  ; false
    // 0xb6c2a4: StoreField: r1->field_27 = r0
    //     0xb6c2a4: stur            w0, [x1, #0x27]
    // 0xb6c2a8: r0 = true
    //     0xb6c2a8: add             x0, NULL, #0x20  ; true
    // 0xb6c2ac: StoreField: r1->field_2f = r0
    //     0xb6c2ac: stur            w0, [x1, #0x2f]
    // 0xb6c2b0: ldur            x0, [fp, #-0x30]
    // 0xb6c2b4: StoreField: r1->field_37 = r0
    //     0xb6c2b4: stur            w0, [x1, #0x37]
    // 0xb6c2b8: r0 = Instance_ValueKey
    //     0xb6c2b8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53c18] Obj!ValueKey<String>@d5b2f1
    //     0xb6c2bc: ldr             x0, [x0, #0xc18]
    // 0xb6c2c0: StoreField: r1->field_7 = r0
    //     0xb6c2c0: stur            w0, [x1, #7]
    // 0xb6c2c4: r0 = TextButtonTheme()
    //     0xb6c2c4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb6c2c8: mov             x1, x0
    // 0xb6c2cc: ldur            x0, [fp, #-0x20]
    // 0xb6c2d0: stur            x1, [fp, #-8]
    // 0xb6c2d4: StoreField: r1->field_f = r0
    //     0xb6c2d4: stur            w0, [x1, #0xf]
    // 0xb6c2d8: ldur            x0, [fp, #-0x18]
    // 0xb6c2dc: StoreField: r1->field_b = r0
    //     0xb6c2dc: stur            w0, [x1, #0xb]
    // 0xb6c2e0: r0 = SizedBox()
    //     0xb6c2e0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6c2e4: mov             x1, x0
    // 0xb6c2e8: r0 = inf
    //     0xb6c2e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb6c2ec: ldr             x0, [x0, #0x9f8]
    // 0xb6c2f0: stur            x1, [fp, #-0x18]
    // 0xb6c2f4: StoreField: r1->field_f = r0
    //     0xb6c2f4: stur            w0, [x1, #0xf]
    // 0xb6c2f8: ldur            x0, [fp, #-8]
    // 0xb6c2fc: StoreField: r1->field_b = r0
    //     0xb6c2fc: stur            w0, [x1, #0xb]
    // 0xb6c300: r0 = Padding()
    //     0xb6c300: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6c304: mov             x2, x0
    // 0xb6c308: r0 = Instance_EdgeInsets
    //     0xb6c308: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!EdgeInsets@d57b01
    //     0xb6c30c: ldr             x0, [x0, #0xf48]
    // 0xb6c310: stur            x2, [fp, #-8]
    // 0xb6c314: StoreField: r2->field_f = r0
    //     0xb6c314: stur            w0, [x2, #0xf]
    // 0xb6c318: ldur            x0, [fp, #-0x18]
    // 0xb6c31c: StoreField: r2->field_b = r0
    //     0xb6c31c: stur            w0, [x2, #0xb]
    // 0xb6c320: ldur            x0, [fp, #-0x10]
    // 0xb6c324: LoadField: r1 = r0->field_b
    //     0xb6c324: ldur            w1, [x0, #0xb]
    // 0xb6c328: LoadField: r3 = r0->field_f
    //     0xb6c328: ldur            w3, [x0, #0xf]
    // 0xb6c32c: DecompressPointer r3
    //     0xb6c32c: add             x3, x3, HEAP, lsl #32
    // 0xb6c330: LoadField: r4 = r3->field_b
    //     0xb6c330: ldur            w4, [x3, #0xb]
    // 0xb6c334: r3 = LoadInt32Instr(r1)
    //     0xb6c334: sbfx            x3, x1, #1, #0x1f
    // 0xb6c338: stur            x3, [fp, #-0x48]
    // 0xb6c33c: r1 = LoadInt32Instr(r4)
    //     0xb6c33c: sbfx            x1, x4, #1, #0x1f
    // 0xb6c340: cmp             x3, x1
    // 0xb6c344: b.ne            #0xb6c350
    // 0xb6c348: mov             x1, x0
    // 0xb6c34c: r0 = _growToNextCapacity()
    //     0xb6c34c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb6c350: ldur            x2, [fp, #-0x10]
    // 0xb6c354: ldur            x3, [fp, #-0x48]
    // 0xb6c358: add             x0, x3, #1
    // 0xb6c35c: lsl             x1, x0, #1
    // 0xb6c360: StoreField: r2->field_b = r1
    //     0xb6c360: stur            w1, [x2, #0xb]
    // 0xb6c364: LoadField: r1 = r2->field_f
    //     0xb6c364: ldur            w1, [x2, #0xf]
    // 0xb6c368: DecompressPointer r1
    //     0xb6c368: add             x1, x1, HEAP, lsl #32
    // 0xb6c36c: ldur            x0, [fp, #-8]
    // 0xb6c370: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6c370: add             x25, x1, x3, lsl #2
    //     0xb6c374: add             x25, x25, #0xf
    //     0xb6c378: str             w0, [x25]
    //     0xb6c37c: tbz             w0, #0, #0xb6c398
    //     0xb6c380: ldurb           w16, [x1, #-1]
    //     0xb6c384: ldurb           w17, [x0, #-1]
    //     0xb6c388: and             x16, x17, x16, lsr #2
    //     0xb6c38c: tst             x16, HEAP, lsr #32
    //     0xb6c390: b.eq            #0xb6c398
    //     0xb6c394: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6c398: r0 = Column()
    //     0xb6c398: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb6c39c: r1 = Instance_Axis
    //     0xb6c39c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb6c3a0: StoreField: r0->field_f = r1
    //     0xb6c3a0: stur            w1, [x0, #0xf]
    // 0xb6c3a4: r1 = Instance_MainAxisAlignment
    //     0xb6c3a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6c3a8: ldr             x1, [x1, #0xa08]
    // 0xb6c3ac: StoreField: r0->field_13 = r1
    //     0xb6c3ac: stur            w1, [x0, #0x13]
    // 0xb6c3b0: r1 = Instance_MainAxisSize
    //     0xb6c3b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb6c3b4: ldr             x1, [x1, #0xdd0]
    // 0xb6c3b8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb6c3b8: stur            w1, [x0, #0x17]
    // 0xb6c3bc: r1 = Instance_CrossAxisAlignment
    //     0xb6c3bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb6c3c0: ldr             x1, [x1, #0x890]
    // 0xb6c3c4: StoreField: r0->field_1b = r1
    //     0xb6c3c4: stur            w1, [x0, #0x1b]
    // 0xb6c3c8: r1 = Instance_VerticalDirection
    //     0xb6c3c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6c3cc: ldr             x1, [x1, #0xa20]
    // 0xb6c3d0: StoreField: r0->field_23 = r1
    //     0xb6c3d0: stur            w1, [x0, #0x23]
    // 0xb6c3d4: r1 = Instance_Clip
    //     0xb6c3d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6c3d8: ldr             x1, [x1, #0x38]
    // 0xb6c3dc: StoreField: r0->field_2b = r1
    //     0xb6c3dc: stur            w1, [x0, #0x2b]
    // 0xb6c3e0: StoreField: r0->field_2f = rZR
    //     0xb6c3e0: stur            xzr, [x0, #0x2f]
    // 0xb6c3e4: ldur            x1, [fp, #-0x10]
    // 0xb6c3e8: StoreField: r0->field_b = r1
    //     0xb6c3e8: stur            w1, [x0, #0xb]
    // 0xb6c3ec: LeaveFrame
    //     0xb6c3ec: mov             SP, fp
    //     0xb6c3f0: ldp             fp, lr, [SP], #0x10
    // 0xb6c3f4: ret
    //     0xb6c3f4: ret             
    // 0xb6c3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6c3f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6c3fc: b               #0xb6b81c
    // 0xb6c400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6c400: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6c404: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6c404: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6c408: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6c408: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6c40c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6c40c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6c410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6c410: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6c414, size: 0x1488
    // 0xb6c414: EnterFrame
    //     0xb6c414: stp             fp, lr, [SP, #-0x10]!
    //     0xb6c418: mov             fp, SP
    // 0xb6c41c: AllocStack(0x80)
    //     0xb6c41c: sub             SP, SP, #0x80
    // 0xb6c420: SetupParameters()
    //     0xb6c420: ldr             x0, [fp, #0x10]
    //     0xb6c424: ldur            w1, [x0, #0x17]
    //     0xb6c428: add             x1, x1, HEAP, lsl #32
    //     0xb6c42c: stur            x1, [fp, #-8]
    // 0xb6c430: CheckStackOverflow
    //     0xb6c430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6c434: cmp             SP, x16
    //     0xb6c438: b.ls            #0xb6d7a0
    // 0xb6c43c: LoadField: r0 = r1->field_13
    //     0xb6c43c: ldur            w0, [x1, #0x13]
    // 0xb6c440: DecompressPointer r0
    //     0xb6c440: add             x0, x0, HEAP, lsl #32
    // 0xb6c444: r16 = <Object?>
    //     0xb6c444: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xb6c448: stp             x0, x16, [SP]
    // 0xb6c44c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6c44c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb6c450: r0 = pop()
    //     0xb6c450: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xb6c454: ldur            x1, [fp, #-8]
    // 0xb6c458: LoadField: r0 = r1->field_f
    //     0xb6c458: ldur            w0, [x1, #0xf]
    // 0xb6c45c: DecompressPointer r0
    //     0xb6c45c: add             x0, x0, HEAP, lsl #32
    // 0xb6c460: LoadField: r2 = r0->field_b
    //     0xb6c460: ldur            w2, [x0, #0xb]
    // 0xb6c464: DecompressPointer r2
    //     0xb6c464: add             x2, x2, HEAP, lsl #32
    // 0xb6c468: cmp             w2, NULL
    // 0xb6c46c: b.eq            #0xb6d7a8
    // 0xb6c470: LoadField: r0 = r2->field_f
    //     0xb6c470: ldur            w0, [x2, #0xf]
    // 0xb6c474: DecompressPointer r0
    //     0xb6c474: add             x0, x0, HEAP, lsl #32
    // 0xb6c478: r2 = LoadClassIdInstr(r0)
    //     0xb6c478: ldur            x2, [x0, #-1]
    //     0xb6c47c: ubfx            x2, x2, #0xc, #0x14
    // 0xb6c480: r16 = "home_page"
    //     0xb6c480: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xb6c484: ldr             x16, [x16, #0xe60]
    // 0xb6c488: stp             x16, x0, [SP]
    // 0xb6c48c: mov             x0, x2
    // 0xb6c490: mov             lr, x0
    // 0xb6c494: ldr             lr, [x21, lr, lsl #3]
    // 0xb6c498: blr             lr
    // 0xb6c49c: tbnz            w0, #4, #0xb6c4dc
    // 0xb6c4a0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb6c4a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb6c4a4: ldr             x0, [x0, #0x1c80]
    //     0xb6c4a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb6c4ac: cmp             w0, w16
    //     0xb6c4b0: b.ne            #0xb6c4bc
    //     0xb6c4b4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb6c4b8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb6c4bc: r16 = <HomeController>
    //     0xb6c4bc: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xb6c4c0: ldr             x16, [x16, #0xf98]
    // 0xb6c4c4: str             x16, [SP]
    // 0xb6c4c8: r4 = const [0x1, 0, 0, 0, null]
    //     0xb6c4c8: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb6c4cc: r0 = Inst.find()
    //     0xb6c4cc: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb6c4d0: r3 = "product_card"
    //     0xb6c4d0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xb6c4d4: ldr             x3, [x3, #0xc28]
    // 0xb6c4d8: b               #0xb6c670
    // 0xb6c4dc: ldur            x1, [fp, #-8]
    // 0xb6c4e0: LoadField: r0 = r1->field_f
    //     0xb6c4e0: ldur            w0, [x1, #0xf]
    // 0xb6c4e4: DecompressPointer r0
    //     0xb6c4e4: add             x0, x0, HEAP, lsl #32
    // 0xb6c4e8: LoadField: r2 = r0->field_b
    //     0xb6c4e8: ldur            w2, [x0, #0xb]
    // 0xb6c4ec: DecompressPointer r2
    //     0xb6c4ec: add             x2, x2, HEAP, lsl #32
    // 0xb6c4f0: cmp             w2, NULL
    // 0xb6c4f4: b.eq            #0xb6d7ac
    // 0xb6c4f8: LoadField: r0 = r2->field_f
    //     0xb6c4f8: ldur            w0, [x2, #0xf]
    // 0xb6c4fc: DecompressPointer r0
    //     0xb6c4fc: add             x0, x0, HEAP, lsl #32
    // 0xb6c500: r2 = LoadClassIdInstr(r0)
    //     0xb6c500: ldur            x2, [x0, #-1]
    //     0xb6c504: ubfx            x2, x2, #0xc, #0x14
    // 0xb6c508: r16 = "collection_page"
    //     0xb6c508: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xb6c50c: ldr             x16, [x16, #0x118]
    // 0xb6c510: stp             x16, x0, [SP]
    // 0xb6c514: mov             x0, x2
    // 0xb6c518: mov             lr, x0
    // 0xb6c51c: ldr             lr, [x21, lr, lsl #3]
    // 0xb6c520: blr             lr
    // 0xb6c524: tbnz            w0, #4, #0xb6c564
    // 0xb6c528: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb6c528: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb6c52c: ldr             x0, [x0, #0x1c80]
    //     0xb6c530: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb6c534: cmp             w0, w16
    //     0xb6c538: b.ne            #0xb6c544
    //     0xb6c53c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb6c540: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb6c544: r16 = <CollectionsController>
    //     0xb6c544: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0xb6c548: ldr             x16, [x16, #0xb00]
    // 0xb6c54c: str             x16, [SP]
    // 0xb6c550: r4 = const [0x1, 0, 0, 0, null]
    //     0xb6c550: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb6c554: r0 = Inst.find()
    //     0xb6c554: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb6c558: r0 = "collection_page"
    //     0xb6c558: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xb6c55c: ldr             x0, [x0, #0x118]
    // 0xb6c560: b               #0xb6c66c
    // 0xb6c564: ldur            x1, [fp, #-8]
    // 0xb6c568: LoadField: r0 = r1->field_f
    //     0xb6c568: ldur            w0, [x1, #0xf]
    // 0xb6c56c: DecompressPointer r0
    //     0xb6c56c: add             x0, x0, HEAP, lsl #32
    // 0xb6c570: LoadField: r2 = r0->field_b
    //     0xb6c570: ldur            w2, [x0, #0xb]
    // 0xb6c574: DecompressPointer r2
    //     0xb6c574: add             x2, x2, HEAP, lsl #32
    // 0xb6c578: cmp             w2, NULL
    // 0xb6c57c: b.eq            #0xb6d7b0
    // 0xb6c580: LoadField: r0 = r2->field_f
    //     0xb6c580: ldur            w0, [x2, #0xf]
    // 0xb6c584: DecompressPointer r0
    //     0xb6c584: add             x0, x0, HEAP, lsl #32
    // 0xb6c588: r2 = LoadClassIdInstr(r0)
    //     0xb6c588: ldur            x2, [x0, #-1]
    //     0xb6c58c: ubfx            x2, x2, #0xc, #0x14
    // 0xb6c590: r16 = "order_success_page"
    //     0xb6c590: add             x16, PP, #0x34, lsl #12  ; [pp+0x341c0] "order_success_page"
    //     0xb6c594: ldr             x16, [x16, #0x1c0]
    // 0xb6c598: stp             x16, x0, [SP]
    // 0xb6c59c: mov             x0, x2
    // 0xb6c5a0: mov             lr, x0
    // 0xb6c5a4: ldr             lr, [x21, lr, lsl #3]
    // 0xb6c5a8: blr             lr
    // 0xb6c5ac: tbnz            w0, #4, #0xb6c634
    // 0xb6c5b0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb6c5b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb6c5b4: ldr             x0, [x0, #0x1c80]
    //     0xb6c5b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb6c5bc: cmp             w0, w16
    //     0xb6c5c0: b.ne            #0xb6c5cc
    //     0xb6c5c4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb6c5c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb6c5cc: r16 = <OrderSuccessController>
    //     0xb6c5cc: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c878] TypeArguments: <OrderSuccessController>
    //     0xb6c5d0: ldr             x16, [x16, #0x878]
    // 0xb6c5d4: str             x16, [SP]
    // 0xb6c5d8: r4 = const [0x1, 0, 0, 0, null]
    //     0xb6c5d8: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb6c5dc: r0 = Inst.find()
    //     0xb6c5dc: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb6c5e0: stur            x0, [fp, #-0x10]
    // 0xb6c5e4: r16 = <ProductDetailController>
    //     0xb6c5e4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xb6c5e8: ldr             x16, [x16, #0xde0]
    // 0xb6c5ec: str             x16, [SP]
    // 0xb6c5f0: r4 = const [0x1, 0, 0, 0, null]
    //     0xb6c5f0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb6c5f4: r0 = Inst.delete()
    //     0xb6c5f4: bl              #0x9c7d48  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.delete
    // 0xb6c5f8: r16 = <Type>
    //     0xb6c5f8: ldr             x16, [PP, #0x4b40]  ; [pp+0x4b40] TypeArguments: <Type>
    // 0xb6c5fc: r30 = ProductDetailController
    //     0xb6c5fc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ede8] Type: ProductDetailController
    //     0xb6c600: ldr             lr, [lr, #0xde8]
    // 0xb6c604: stp             lr, x16, [SP]
    // 0xb6c608: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6c608: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb6c60c: r0 = Inst.put()
    //     0xb6c60c: bl              #0xa5f404  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.put
    // 0xb6c610: ldur            x0, [fp, #-0x10]
    // 0xb6c614: r17 = 283
    //     0xb6c614: movz            x17, #0x11b
    // 0xb6c618: ldr             w1, [x0, x17]
    // 0xb6c61c: DecompressPointer r1
    //     0xb6c61c: add             x1, x1, HEAP, lsl #32
    // 0xb6c620: r2 = false
    //     0xb6c620: add             x2, NULL, #0x30  ; false
    // 0xb6c624: r0 = value=()
    //     0xb6c624: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xb6c628: r0 = "order_success_page"
    //     0xb6c628: add             x0, PP, #0x34, lsl #12  ; [pp+0x341c0] "order_success_page"
    //     0xb6c62c: ldr             x0, [x0, #0x1c0]
    // 0xb6c630: b               #0xb6c66c
    // 0xb6c634: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb6c634: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb6c638: ldr             x0, [x0, #0x1c80]
    //     0xb6c63c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb6c640: cmp             w0, w16
    //     0xb6c644: b.ne            #0xb6c650
    //     0xb6c648: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb6c64c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb6c650: r16 = <HomeController>
    //     0xb6c650: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xb6c654: ldr             x16, [x16, #0xf98]
    // 0xb6c658: str             x16, [SP]
    // 0xb6c65c: r4 = const [0x1, 0, 0, 0, null]
    //     0xb6c65c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xb6c660: r0 = Inst.find()
    //     0xb6c660: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xb6c664: r0 = "home_page"
    //     0xb6c664: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xb6c668: ldr             x0, [x0, #0xe60]
    // 0xb6c66c: mov             x3, x0
    // 0xb6c670: ldur            x2, [fp, #-8]
    // 0xb6c674: stur            x3, [fp, #-0x30]
    // 0xb6c678: LoadField: r4 = r2->field_f
    //     0xb6c678: ldur            w4, [x2, #0xf]
    // 0xb6c67c: DecompressPointer r4
    //     0xb6c67c: add             x4, x4, HEAP, lsl #32
    // 0xb6c680: LoadField: r5 = r4->field_b
    //     0xb6c680: ldur            w5, [x4, #0xb]
    // 0xb6c684: DecompressPointer r5
    //     0xb6c684: add             x5, x5, HEAP, lsl #32
    // 0xb6c688: stur            x5, [fp, #-0x28]
    // 0xb6c68c: cmp             w5, NULL
    // 0xb6c690: b.eq            #0xb6d7b4
    // 0xb6c694: LoadField: r6 = r5->field_f
    //     0xb6c694: ldur            w6, [x5, #0xf]
    // 0xb6c698: DecompressPointer r6
    //     0xb6c698: add             x6, x6, HEAP, lsl #32
    // 0xb6c69c: stur            x6, [fp, #-0x20]
    // 0xb6c6a0: LoadField: r0 = r5->field_b
    //     0xb6c6a0: ldur            w0, [x5, #0xb]
    // 0xb6c6a4: DecompressPointer r0
    //     0xb6c6a4: add             x0, x0, HEAP, lsl #32
    // 0xb6c6a8: LoadField: r7 = r0->field_6f
    //     0xb6c6a8: ldur            w7, [x0, #0x6f]
    // 0xb6c6ac: DecompressPointer r7
    //     0xb6c6ac: add             x7, x7, HEAP, lsl #32
    // 0xb6c6b0: cmp             w7, NULL
    // 0xb6c6b4: b.ne            #0xb6c6c0
    // 0xb6c6b8: r8 = Null
    //     0xb6c6b8: mov             x8, NULL
    // 0xb6c6bc: b               #0xb6c6fc
    // 0xb6c6c0: ArrayLoad: r8 = r4[0]  ; List_8
    //     0xb6c6c0: ldur            x8, [x4, #0x17]
    // 0xb6c6c4: LoadField: r0 = r7->field_b
    //     0xb6c6c4: ldur            w0, [x7, #0xb]
    // 0xb6c6c8: r1 = LoadInt32Instr(r0)
    //     0xb6c6c8: sbfx            x1, x0, #1, #0x1f
    // 0xb6c6cc: mov             x0, x1
    // 0xb6c6d0: mov             x1, x8
    // 0xb6c6d4: cmp             x1, x0
    // 0xb6c6d8: b.hs            #0xb6d7b8
    // 0xb6c6dc: LoadField: r0 = r7->field_f
    //     0xb6c6dc: ldur            w0, [x7, #0xf]
    // 0xb6c6e0: DecompressPointer r0
    //     0xb6c6e0: add             x0, x0, HEAP, lsl #32
    // 0xb6c6e4: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb6c6e4: add             x16, x0, x8, lsl #2
    //     0xb6c6e8: ldur            w1, [x16, #0xf]
    // 0xb6c6ec: DecompressPointer r1
    //     0xb6c6ec: add             x1, x1, HEAP, lsl #32
    // 0xb6c6f0: LoadField: r0 = r1->field_f
    //     0xb6c6f0: ldur            w0, [x1, #0xf]
    // 0xb6c6f4: DecompressPointer r0
    //     0xb6c6f4: add             x0, x0, HEAP, lsl #32
    // 0xb6c6f8: mov             x8, x0
    // 0xb6c6fc: stur            x8, [fp, #-0x18]
    // 0xb6c700: cmp             w7, NULL
    // 0xb6c704: b.ne            #0xb6c710
    // 0xb6c708: r9 = Null
    //     0xb6c708: mov             x9, NULL
    // 0xb6c70c: b               #0xb6c74c
    // 0xb6c710: ArrayLoad: r9 = r4[0]  ; List_8
    //     0xb6c710: ldur            x9, [x4, #0x17]
    // 0xb6c714: LoadField: r0 = r7->field_b
    //     0xb6c714: ldur            w0, [x7, #0xb]
    // 0xb6c718: r1 = LoadInt32Instr(r0)
    //     0xb6c718: sbfx            x1, x0, #1, #0x1f
    // 0xb6c71c: mov             x0, x1
    // 0xb6c720: mov             x1, x9
    // 0xb6c724: cmp             x1, x0
    // 0xb6c728: b.hs            #0xb6d7bc
    // 0xb6c72c: LoadField: r0 = r7->field_f
    //     0xb6c72c: ldur            w0, [x7, #0xf]
    // 0xb6c730: DecompressPointer r0
    //     0xb6c730: add             x0, x0, HEAP, lsl #32
    // 0xb6c734: ArrayLoad: r1 = r0[r9]  ; Unknown_4
    //     0xb6c734: add             x16, x0, x9, lsl #2
    //     0xb6c738: ldur            w1, [x16, #0xf]
    // 0xb6c73c: DecompressPointer r1
    //     0xb6c73c: add             x1, x1, HEAP, lsl #32
    // 0xb6c740: LoadField: r0 = r1->field_b
    //     0xb6c740: ldur            w0, [x1, #0xb]
    // 0xb6c744: DecompressPointer r0
    //     0xb6c744: add             x0, x0, HEAP, lsl #32
    // 0xb6c748: mov             x9, x0
    // 0xb6c74c: stur            x9, [fp, #-0x10]
    // 0xb6c750: cmp             w7, NULL
    // 0xb6c754: b.ne            #0xb6c778
    // 0xb6c758: mov             x0, x2
    // 0xb6c75c: mov             x1, x3
    // 0xb6c760: mov             x3, x6
    // 0xb6c764: mov             x4, x8
    // 0xb6c768: mov             x2, x5
    // 0xb6c76c: mov             x5, x9
    // 0xb6c770: r6 = Null
    //     0xb6c770: mov             x6, NULL
    // 0xb6c774: b               #0xb6c7e4
    // 0xb6c778: ArrayLoad: r10 = r4[0]  ; List_8
    //     0xb6c778: ldur            x10, [x4, #0x17]
    // 0xb6c77c: LoadField: r0 = r7->field_b
    //     0xb6c77c: ldur            w0, [x7, #0xb]
    // 0xb6c780: r1 = LoadInt32Instr(r0)
    //     0xb6c780: sbfx            x1, x0, #1, #0x1f
    // 0xb6c784: mov             x0, x1
    // 0xb6c788: mov             x1, x10
    // 0xb6c78c: cmp             x1, x0
    // 0xb6c790: b.hs            #0xb6d7c0
    // 0xb6c794: LoadField: r0 = r7->field_f
    //     0xb6c794: ldur            w0, [x7, #0xf]
    // 0xb6c798: DecompressPointer r0
    //     0xb6c798: add             x0, x0, HEAP, lsl #32
    // 0xb6c79c: ArrayLoad: r1 = r0[r10]  ; Unknown_4
    //     0xb6c79c: add             x16, x0, x10, lsl #2
    //     0xb6c7a0: ldur            w1, [x16, #0xf]
    // 0xb6c7a4: DecompressPointer r1
    //     0xb6c7a4: add             x1, x1, HEAP, lsl #32
    // 0xb6c7a8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb6c7a8: ldur            w0, [x1, #0x17]
    // 0xb6c7ac: DecompressPointer r0
    //     0xb6c7ac: add             x0, x0, HEAP, lsl #32
    // 0xb6c7b0: cmp             w0, NULL
    // 0xb6c7b4: b.ne            #0xb6c7c0
    // 0xb6c7b8: r0 = Null
    //     0xb6c7b8: mov             x0, NULL
    // 0xb6c7bc: b               #0xb6c7c8
    // 0xb6c7c0: stp             x0, NULL, [SP]
    // 0xb6c7c4: r0 = _Double.fromInteger()
    //     0xb6c7c4: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xb6c7c8: mov             x6, x0
    // 0xb6c7cc: ldur            x0, [fp, #-8]
    // 0xb6c7d0: ldur            x1, [fp, #-0x30]
    // 0xb6c7d4: ldur            x3, [fp, #-0x20]
    // 0xb6c7d8: ldur            x4, [fp, #-0x18]
    // 0xb6c7dc: ldur            x5, [fp, #-0x10]
    // 0xb6c7e0: ldur            x2, [fp, #-0x28]
    // 0xb6c7e4: stur            x6, [fp, #-0x38]
    // 0xb6c7e8: r0 = EventData()
    //     0xb6c7e8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb6c7ec: mov             x1, x0
    // 0xb6c7f0: ldur            x0, [fp, #-0x20]
    // 0xb6c7f4: stur            x1, [fp, #-0x40]
    // 0xb6c7f8: StoreField: r1->field_13 = r0
    //     0xb6c7f8: stur            w0, [x1, #0x13]
    // 0xb6c7fc: ldur            x0, [fp, #-0x38]
    // 0xb6c800: StoreField: r1->field_2f = r0
    //     0xb6c800: stur            w0, [x1, #0x2f]
    // 0xb6c804: ldur            x0, [fp, #-0x18]
    // 0xb6c808: StoreField: r1->field_33 = r0
    //     0xb6c808: stur            w0, [x1, #0x33]
    // 0xb6c80c: ldur            x0, [fp, #-0x30]
    // 0xb6c810: StoreField: r1->field_3b = r0
    //     0xb6c810: stur            w0, [x1, #0x3b]
    // 0xb6c814: StoreField: r1->field_87 = r0
    //     0xb6c814: stur            w0, [x1, #0x87]
    // 0xb6c818: ldur            x0, [fp, #-0x10]
    // 0xb6c81c: StoreField: r1->field_8f = r0
    //     0xb6c81c: stur            w0, [x1, #0x8f]
    // 0xb6c820: r0 = EventsRequest()
    //     0xb6c820: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb6c824: mov             x1, x0
    // 0xb6c828: r0 = "add_to_bag_clicked"
    //     0xb6c828: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fec8] "add_to_bag_clicked"
    //     0xb6c82c: ldr             x0, [x0, #0xec8]
    // 0xb6c830: StoreField: r1->field_7 = r0
    //     0xb6c830: stur            w0, [x1, #7]
    // 0xb6c834: ldur            x0, [fp, #-0x40]
    // 0xb6c838: StoreField: r1->field_b = r0
    //     0xb6c838: stur            w0, [x1, #0xb]
    // 0xb6c83c: ldur            x0, [fp, #-0x28]
    // 0xb6c840: LoadField: r2 = r0->field_13
    //     0xb6c840: ldur            w2, [x0, #0x13]
    // 0xb6c844: DecompressPointer r2
    //     0xb6c844: add             x2, x2, HEAP, lsl #32
    // 0xb6c848: stp             x1, x2, [SP]
    // 0xb6c84c: r4 = 0
    //     0xb6c84c: movz            x4, #0
    // 0xb6c850: ldr             x0, [SP, #8]
    // 0xb6c854: r16 = UnlinkedCall_0x613b5c
    //     0xb6c854: add             x16, PP, #0x56, lsl #12  ; [pp+0x56220] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb6c858: add             x16, x16, #0x220
    // 0xb6c85c: ldp             x5, lr, [x16]
    // 0xb6c860: blr             lr
    // 0xb6c864: ldur            x2, [fp, #-8]
    // 0xb6c868: LoadField: r3 = r2->field_f
    //     0xb6c868: ldur            w3, [x2, #0xf]
    // 0xb6c86c: DecompressPointer r3
    //     0xb6c86c: add             x3, x3, HEAP, lsl #32
    // 0xb6c870: LoadField: r4 = r3->field_b
    //     0xb6c870: ldur            w4, [x3, #0xb]
    // 0xb6c874: DecompressPointer r4
    //     0xb6c874: add             x4, x4, HEAP, lsl #32
    // 0xb6c878: stur            x4, [fp, #-0x18]
    // 0xb6c87c: cmp             w4, NULL
    // 0xb6c880: b.eq            #0xb6d7c4
    // 0xb6c884: LoadField: r0 = r4->field_b
    //     0xb6c884: ldur            w0, [x4, #0xb]
    // 0xb6c888: DecompressPointer r0
    //     0xb6c888: add             x0, x0, HEAP, lsl #32
    // 0xb6c88c: LoadField: r1 = r0->field_8b
    //     0xb6c88c: ldur            w1, [x0, #0x8b]
    // 0xb6c890: DecompressPointer r1
    //     0xb6c890: add             x1, x1, HEAP, lsl #32
    // 0xb6c894: cmp             w1, NULL
    // 0xb6c898: b.eq            #0xb6cf6c
    // 0xb6c89c: tbnz            w1, #4, #0xb6cf6c
    // 0xb6c8a0: LoadField: r1 = r0->field_87
    //     0xb6c8a0: ldur            w1, [x0, #0x87]
    // 0xb6c8a4: DecompressPointer r1
    //     0xb6c8a4: add             x1, x1, HEAP, lsl #32
    // 0xb6c8a8: cmp             w1, NULL
    // 0xb6c8ac: b.eq            #0xb6cc10
    // 0xb6c8b0: tbnz            w1, #4, #0xb6cc10
    // 0xb6c8b4: LoadField: r2 = r0->field_73
    //     0xb6c8b4: ldur            w2, [x0, #0x73]
    // 0xb6c8b8: DecompressPointer r2
    //     0xb6c8b8: add             x2, x2, HEAP, lsl #32
    // 0xb6c8bc: cmp             w2, NULL
    // 0xb6c8c0: b.ne            #0xb6c8cc
    // 0xb6c8c4: r1 = Null
    //     0xb6c8c4: mov             x1, NULL
    // 0xb6c8c8: b               #0xb6c8e4
    // 0xb6c8cc: LoadField: r1 = r2->field_b
    //     0xb6c8cc: ldur            w1, [x2, #0xb]
    // 0xb6c8d0: cbz             w1, #0xb6c8dc
    // 0xb6c8d4: r5 = false
    //     0xb6c8d4: add             x5, NULL, #0x30  ; false
    // 0xb6c8d8: b               #0xb6c8e0
    // 0xb6c8dc: r5 = true
    //     0xb6c8dc: add             x5, NULL, #0x20  ; true
    // 0xb6c8e0: mov             x1, x5
    // 0xb6c8e4: cmp             w1, NULL
    // 0xb6c8e8: b.eq            #0xb6c8f0
    // 0xb6c8ec: tbnz            w1, #4, #0xb6ca84
    // 0xb6c8f0: LoadField: r1 = r0->field_8f
    //     0xb6c8f0: ldur            w1, [x0, #0x8f]
    // 0xb6c8f4: DecompressPointer r1
    //     0xb6c8f4: add             x1, x1, HEAP, lsl #32
    // 0xb6c8f8: cmp             w1, NULL
    // 0xb6c8fc: b.ne            #0xb6c908
    // 0xb6c900: r2 = ""
    //     0xb6c900: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6c904: b               #0xb6c90c
    // 0xb6c908: mov             x2, x1
    // 0xb6c90c: LoadField: r5 = r0->field_6f
    //     0xb6c90c: ldur            w5, [x0, #0x6f]
    // 0xb6c910: DecompressPointer r5
    //     0xb6c910: add             x5, x5, HEAP, lsl #32
    // 0xb6c914: cmp             w5, NULL
    // 0xb6c918: b.ne            #0xb6c924
    // 0xb6c91c: r0 = Null
    //     0xb6c91c: mov             x0, NULL
    // 0xb6c920: b               #0xb6c95c
    // 0xb6c924: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xb6c924: ldur            x6, [x3, #0x17]
    // 0xb6c928: LoadField: r0 = r5->field_b
    //     0xb6c928: ldur            w0, [x5, #0xb]
    // 0xb6c92c: r1 = LoadInt32Instr(r0)
    //     0xb6c92c: sbfx            x1, x0, #1, #0x1f
    // 0xb6c930: mov             x0, x1
    // 0xb6c934: mov             x1, x6
    // 0xb6c938: cmp             x1, x0
    // 0xb6c93c: b.hs            #0xb6d7c8
    // 0xb6c940: LoadField: r0 = r5->field_f
    //     0xb6c940: ldur            w0, [x5, #0xf]
    // 0xb6c944: DecompressPointer r0
    //     0xb6c944: add             x0, x0, HEAP, lsl #32
    // 0xb6c948: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6c948: add             x16, x0, x6, lsl #2
    //     0xb6c94c: ldur            w1, [x16, #0xf]
    // 0xb6c950: DecompressPointer r1
    //     0xb6c950: add             x1, x1, HEAP, lsl #32
    // 0xb6c954: LoadField: r0 = r1->field_f
    //     0xb6c954: ldur            w0, [x1, #0xf]
    // 0xb6c958: DecompressPointer r0
    //     0xb6c958: add             x0, x0, HEAP, lsl #32
    // 0xb6c95c: cmp             w0, NULL
    // 0xb6c960: b.ne            #0xb6c96c
    // 0xb6c964: r6 = ""
    //     0xb6c964: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6c968: b               #0xb6c970
    // 0xb6c96c: mov             x6, x0
    // 0xb6c970: cmp             w5, NULL
    // 0xb6c974: b.ne            #0xb6c980
    // 0xb6c978: r0 = Null
    //     0xb6c978: mov             x0, NULL
    // 0xb6c97c: b               #0xb6c9b8
    // 0xb6c980: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xb6c980: ldur            x7, [x3, #0x17]
    // 0xb6c984: LoadField: r0 = r5->field_b
    //     0xb6c984: ldur            w0, [x5, #0xb]
    // 0xb6c988: r1 = LoadInt32Instr(r0)
    //     0xb6c988: sbfx            x1, x0, #1, #0x1f
    // 0xb6c98c: mov             x0, x1
    // 0xb6c990: mov             x1, x7
    // 0xb6c994: cmp             x1, x0
    // 0xb6c998: b.hs            #0xb6d7cc
    // 0xb6c99c: LoadField: r0 = r5->field_f
    //     0xb6c99c: ldur            w0, [x5, #0xf]
    // 0xb6c9a0: DecompressPointer r0
    //     0xb6c9a0: add             x0, x0, HEAP, lsl #32
    // 0xb6c9a4: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6c9a4: add             x16, x0, x7, lsl #2
    //     0xb6c9a8: ldur            w1, [x16, #0xf]
    // 0xb6c9ac: DecompressPointer r1
    //     0xb6c9ac: add             x1, x1, HEAP, lsl #32
    // 0xb6c9b0: LoadField: r0 = r1->field_b
    //     0xb6c9b0: ldur            w0, [x1, #0xb]
    // 0xb6c9b4: DecompressPointer r0
    //     0xb6c9b4: add             x0, x0, HEAP, lsl #32
    // 0xb6c9b8: cmp             w0, NULL
    // 0xb6c9bc: b.ne            #0xb6c9c8
    // 0xb6c9c0: r7 = ""
    //     0xb6c9c0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6c9c4: b               #0xb6c9cc
    // 0xb6c9c8: mov             x7, x0
    // 0xb6c9cc: cmp             w5, NULL
    // 0xb6c9d0: b.ne            #0xb6c9dc
    // 0xb6c9d4: r0 = Null
    //     0xb6c9d4: mov             x0, NULL
    // 0xb6c9d8: b               #0xb6ca14
    // 0xb6c9dc: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xb6c9dc: ldur            x8, [x3, #0x17]
    // 0xb6c9e0: LoadField: r0 = r5->field_b
    //     0xb6c9e0: ldur            w0, [x5, #0xb]
    // 0xb6c9e4: r1 = LoadInt32Instr(r0)
    //     0xb6c9e4: sbfx            x1, x0, #1, #0x1f
    // 0xb6c9e8: mov             x0, x1
    // 0xb6c9ec: mov             x1, x8
    // 0xb6c9f0: cmp             x1, x0
    // 0xb6c9f4: b.hs            #0xb6d7d0
    // 0xb6c9f8: LoadField: r0 = r5->field_f
    //     0xb6c9f8: ldur            w0, [x5, #0xf]
    // 0xb6c9fc: DecompressPointer r0
    //     0xb6c9fc: add             x0, x0, HEAP, lsl #32
    // 0xb6ca00: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb6ca00: add             x16, x0, x8, lsl #2
    //     0xb6ca04: ldur            w1, [x16, #0xf]
    // 0xb6ca08: DecompressPointer r1
    //     0xb6ca08: add             x1, x1, HEAP, lsl #32
    // 0xb6ca0c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb6ca0c: ldur            w0, [x1, #0x17]
    // 0xb6ca10: DecompressPointer r0
    //     0xb6ca10: add             x0, x0, HEAP, lsl #32
    // 0xb6ca14: cmp             w0, NULL
    // 0xb6ca18: b.ne            #0xb6ca24
    // 0xb6ca1c: r3 = 0
    //     0xb6ca1c: movz            x3, #0
    // 0xb6ca20: b               #0xb6ca34
    // 0xb6ca24: r1 = LoadInt32Instr(r0)
    //     0xb6ca24: sbfx            x1, x0, #1, #0x1f
    //     0xb6ca28: tbz             w0, #0, #0xb6ca30
    //     0xb6ca2c: ldur            x1, [x0, #7]
    // 0xb6ca30: mov             x3, x1
    // 0xb6ca34: LoadField: r5 = r4->field_1f
    //     0xb6ca34: ldur            w5, [x4, #0x1f]
    // 0xb6ca38: DecompressPointer r5
    //     0xb6ca38: add             x5, x5, HEAP, lsl #32
    // 0xb6ca3c: r0 = BoxInt64Instr(r3)
    //     0xb6ca3c: sbfiz           x0, x3, #1, #0x1f
    //     0xb6ca40: cmp             x3, x0, asr #1
    //     0xb6ca44: b.eq            #0xb6ca50
    //     0xb6ca48: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6ca4c: stur            x3, [x0, #7]
    // 0xb6ca50: stp             x2, x5, [SP, #0x28]
    // 0xb6ca54: r16 = true
    //     0xb6ca54: add             x16, NULL, #0x20  ; true
    // 0xb6ca58: stp             x6, x16, [SP, #0x18]
    // 0xb6ca5c: stp             x0, x7, [SP, #8]
    // 0xb6ca60: r16 = "add_to_bag"
    //     0xb6ca60: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb6ca64: ldr             x16, [x16, #0xa38]
    // 0xb6ca68: str             x16, [SP]
    // 0xb6ca6c: mov             x0, x5
    // 0xb6ca70: ClosureCall
    //     0xb6ca70: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xb6ca74: ldr             x4, [x4, #0x330]
    //     0xb6ca78: ldur            x2, [x0, #0x1f]
    //     0xb6ca7c: blr             x2
    // 0xb6ca80: b               #0xb6d790
    // 0xb6ca84: LoadField: r1 = r0->field_8f
    //     0xb6ca84: ldur            w1, [x0, #0x8f]
    // 0xb6ca88: DecompressPointer r1
    //     0xb6ca88: add             x1, x1, HEAP, lsl #32
    // 0xb6ca8c: cmp             w1, NULL
    // 0xb6ca90: b.ne            #0xb6ca9c
    // 0xb6ca94: r5 = ""
    //     0xb6ca94: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6ca98: b               #0xb6caa0
    // 0xb6ca9c: mov             x5, x1
    // 0xb6caa0: cmp             w2, NULL
    // 0xb6caa4: b.ne            #0xb6cab0
    // 0xb6caa8: r0 = Null
    //     0xb6caa8: mov             x0, NULL
    // 0xb6caac: b               #0xb6cae8
    // 0xb6cab0: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xb6cab0: ldur            x6, [x3, #0x17]
    // 0xb6cab4: LoadField: r0 = r2->field_b
    //     0xb6cab4: ldur            w0, [x2, #0xb]
    // 0xb6cab8: r1 = LoadInt32Instr(r0)
    //     0xb6cab8: sbfx            x1, x0, #1, #0x1f
    // 0xb6cabc: mov             x0, x1
    // 0xb6cac0: mov             x1, x6
    // 0xb6cac4: cmp             x1, x0
    // 0xb6cac8: b.hs            #0xb6d7d4
    // 0xb6cacc: LoadField: r0 = r2->field_f
    //     0xb6cacc: ldur            w0, [x2, #0xf]
    // 0xb6cad0: DecompressPointer r0
    //     0xb6cad0: add             x0, x0, HEAP, lsl #32
    // 0xb6cad4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6cad4: add             x16, x0, x6, lsl #2
    //     0xb6cad8: ldur            w1, [x16, #0xf]
    // 0xb6cadc: DecompressPointer r1
    //     0xb6cadc: add             x1, x1, HEAP, lsl #32
    // 0xb6cae0: LoadField: r0 = r1->field_b
    //     0xb6cae0: ldur            w0, [x1, #0xb]
    // 0xb6cae4: DecompressPointer r0
    //     0xb6cae4: add             x0, x0, HEAP, lsl #32
    // 0xb6cae8: cmp             w0, NULL
    // 0xb6caec: b.ne            #0xb6caf8
    // 0xb6caf0: r6 = ""
    //     0xb6caf0: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6caf4: b               #0xb6cafc
    // 0xb6caf8: mov             x6, x0
    // 0xb6cafc: cmp             w2, NULL
    // 0xb6cb00: b.ne            #0xb6cb0c
    // 0xb6cb04: r0 = Null
    //     0xb6cb04: mov             x0, NULL
    // 0xb6cb08: b               #0xb6cb44
    // 0xb6cb0c: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xb6cb0c: ldur            x7, [x3, #0x17]
    // 0xb6cb10: LoadField: r0 = r2->field_b
    //     0xb6cb10: ldur            w0, [x2, #0xb]
    // 0xb6cb14: r1 = LoadInt32Instr(r0)
    //     0xb6cb14: sbfx            x1, x0, #1, #0x1f
    // 0xb6cb18: mov             x0, x1
    // 0xb6cb1c: mov             x1, x7
    // 0xb6cb20: cmp             x1, x0
    // 0xb6cb24: b.hs            #0xb6d7d8
    // 0xb6cb28: LoadField: r0 = r2->field_f
    //     0xb6cb28: ldur            w0, [x2, #0xf]
    // 0xb6cb2c: DecompressPointer r0
    //     0xb6cb2c: add             x0, x0, HEAP, lsl #32
    // 0xb6cb30: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6cb30: add             x16, x0, x7, lsl #2
    //     0xb6cb34: ldur            w1, [x16, #0xf]
    // 0xb6cb38: DecompressPointer r1
    //     0xb6cb38: add             x1, x1, HEAP, lsl #32
    // 0xb6cb3c: LoadField: r0 = r1->field_7
    //     0xb6cb3c: ldur            w0, [x1, #7]
    // 0xb6cb40: DecompressPointer r0
    //     0xb6cb40: add             x0, x0, HEAP, lsl #32
    // 0xb6cb44: cmp             w0, NULL
    // 0xb6cb48: b.ne            #0xb6cb54
    // 0xb6cb4c: r7 = ""
    //     0xb6cb4c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6cb50: b               #0xb6cb58
    // 0xb6cb54: mov             x7, x0
    // 0xb6cb58: cmp             w2, NULL
    // 0xb6cb5c: b.ne            #0xb6cb68
    // 0xb6cb60: r0 = Null
    //     0xb6cb60: mov             x0, NULL
    // 0xb6cb64: b               #0xb6cba0
    // 0xb6cb68: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xb6cb68: ldur            x8, [x3, #0x17]
    // 0xb6cb6c: LoadField: r0 = r2->field_b
    //     0xb6cb6c: ldur            w0, [x2, #0xb]
    // 0xb6cb70: r1 = LoadInt32Instr(r0)
    //     0xb6cb70: sbfx            x1, x0, #1, #0x1f
    // 0xb6cb74: mov             x0, x1
    // 0xb6cb78: mov             x1, x8
    // 0xb6cb7c: cmp             x1, x0
    // 0xb6cb80: b.hs            #0xb6d7dc
    // 0xb6cb84: LoadField: r0 = r2->field_f
    //     0xb6cb84: ldur            w0, [x2, #0xf]
    // 0xb6cb88: DecompressPointer r0
    //     0xb6cb88: add             x0, x0, HEAP, lsl #32
    // 0xb6cb8c: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb6cb8c: add             x16, x0, x8, lsl #2
    //     0xb6cb90: ldur            w1, [x16, #0xf]
    // 0xb6cb94: DecompressPointer r1
    //     0xb6cb94: add             x1, x1, HEAP, lsl #32
    // 0xb6cb98: LoadField: r0 = r1->field_1b
    //     0xb6cb98: ldur            w0, [x1, #0x1b]
    // 0xb6cb9c: DecompressPointer r0
    //     0xb6cb9c: add             x0, x0, HEAP, lsl #32
    // 0xb6cba0: cmp             w0, NULL
    // 0xb6cba4: b.ne            #0xb6cbb0
    // 0xb6cba8: r2 = 0
    //     0xb6cba8: movz            x2, #0
    // 0xb6cbac: b               #0xb6cbc0
    // 0xb6cbb0: r1 = LoadInt32Instr(r0)
    //     0xb6cbb0: sbfx            x1, x0, #1, #0x1f
    //     0xb6cbb4: tbz             w0, #0, #0xb6cbbc
    //     0xb6cbb8: ldur            x1, [x0, #7]
    // 0xb6cbbc: mov             x2, x1
    // 0xb6cbc0: LoadField: r3 = r4->field_1f
    //     0xb6cbc0: ldur            w3, [x4, #0x1f]
    // 0xb6cbc4: DecompressPointer r3
    //     0xb6cbc4: add             x3, x3, HEAP, lsl #32
    // 0xb6cbc8: r0 = BoxInt64Instr(r2)
    //     0xb6cbc8: sbfiz           x0, x2, #1, #0x1f
    //     0xb6cbcc: cmp             x2, x0, asr #1
    //     0xb6cbd0: b.eq            #0xb6cbdc
    //     0xb6cbd4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6cbd8: stur            x2, [x0, #7]
    // 0xb6cbdc: stp             x5, x3, [SP, #0x28]
    // 0xb6cbe0: r16 = true
    //     0xb6cbe0: add             x16, NULL, #0x20  ; true
    // 0xb6cbe4: stp             x6, x16, [SP, #0x18]
    // 0xb6cbe8: stp             x0, x7, [SP, #8]
    // 0xb6cbec: r16 = "add_to_bag"
    //     0xb6cbec: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb6cbf0: ldr             x16, [x16, #0xa38]
    // 0xb6cbf4: str             x16, [SP]
    // 0xb6cbf8: mov             x0, x3
    // 0xb6cbfc: ClosureCall
    //     0xb6cbfc: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xb6cc00: ldr             x4, [x4, #0x330]
    //     0xb6cc04: ldur            x2, [x0, #0x1f]
    //     0xb6cc08: blr             x2
    // 0xb6cc0c: b               #0xb6d790
    // 0xb6cc10: LoadField: r2 = r0->field_73
    //     0xb6cc10: ldur            w2, [x0, #0x73]
    // 0xb6cc14: DecompressPointer r2
    //     0xb6cc14: add             x2, x2, HEAP, lsl #32
    // 0xb6cc18: cmp             w2, NULL
    // 0xb6cc1c: b.ne            #0xb6cc28
    // 0xb6cc20: r1 = Null
    //     0xb6cc20: mov             x1, NULL
    // 0xb6cc24: b               #0xb6cc40
    // 0xb6cc28: LoadField: r1 = r2->field_b
    //     0xb6cc28: ldur            w1, [x2, #0xb]
    // 0xb6cc2c: cbz             w1, #0xb6cc38
    // 0xb6cc30: r5 = false
    //     0xb6cc30: add             x5, NULL, #0x30  ; false
    // 0xb6cc34: b               #0xb6cc3c
    // 0xb6cc38: r5 = true
    //     0xb6cc38: add             x5, NULL, #0x20  ; true
    // 0xb6cc3c: mov             x1, x5
    // 0xb6cc40: cmp             w1, NULL
    // 0xb6cc44: b.eq            #0xb6cc4c
    // 0xb6cc48: tbnz            w1, #4, #0xb6cde0
    // 0xb6cc4c: LoadField: r1 = r0->field_8f
    //     0xb6cc4c: ldur            w1, [x0, #0x8f]
    // 0xb6cc50: DecompressPointer r1
    //     0xb6cc50: add             x1, x1, HEAP, lsl #32
    // 0xb6cc54: cmp             w1, NULL
    // 0xb6cc58: b.ne            #0xb6cc64
    // 0xb6cc5c: r2 = ""
    //     0xb6cc5c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6cc60: b               #0xb6cc68
    // 0xb6cc64: mov             x2, x1
    // 0xb6cc68: LoadField: r5 = r0->field_6f
    //     0xb6cc68: ldur            w5, [x0, #0x6f]
    // 0xb6cc6c: DecompressPointer r5
    //     0xb6cc6c: add             x5, x5, HEAP, lsl #32
    // 0xb6cc70: cmp             w5, NULL
    // 0xb6cc74: b.ne            #0xb6cc80
    // 0xb6cc78: r0 = Null
    //     0xb6cc78: mov             x0, NULL
    // 0xb6cc7c: b               #0xb6ccb8
    // 0xb6cc80: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xb6cc80: ldur            x6, [x3, #0x17]
    // 0xb6cc84: LoadField: r0 = r5->field_b
    //     0xb6cc84: ldur            w0, [x5, #0xb]
    // 0xb6cc88: r1 = LoadInt32Instr(r0)
    //     0xb6cc88: sbfx            x1, x0, #1, #0x1f
    // 0xb6cc8c: mov             x0, x1
    // 0xb6cc90: mov             x1, x6
    // 0xb6cc94: cmp             x1, x0
    // 0xb6cc98: b.hs            #0xb6d7e0
    // 0xb6cc9c: LoadField: r0 = r5->field_f
    //     0xb6cc9c: ldur            w0, [x5, #0xf]
    // 0xb6cca0: DecompressPointer r0
    //     0xb6cca0: add             x0, x0, HEAP, lsl #32
    // 0xb6cca4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6cca4: add             x16, x0, x6, lsl #2
    //     0xb6cca8: ldur            w1, [x16, #0xf]
    // 0xb6ccac: DecompressPointer r1
    //     0xb6ccac: add             x1, x1, HEAP, lsl #32
    // 0xb6ccb0: LoadField: r0 = r1->field_f
    //     0xb6ccb0: ldur            w0, [x1, #0xf]
    // 0xb6ccb4: DecompressPointer r0
    //     0xb6ccb4: add             x0, x0, HEAP, lsl #32
    // 0xb6ccb8: cmp             w0, NULL
    // 0xb6ccbc: b.ne            #0xb6ccc8
    // 0xb6ccc0: r6 = ""
    //     0xb6ccc0: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6ccc4: b               #0xb6cccc
    // 0xb6ccc8: mov             x6, x0
    // 0xb6cccc: cmp             w5, NULL
    // 0xb6ccd0: b.ne            #0xb6ccdc
    // 0xb6ccd4: r0 = Null
    //     0xb6ccd4: mov             x0, NULL
    // 0xb6ccd8: b               #0xb6cd14
    // 0xb6ccdc: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xb6ccdc: ldur            x7, [x3, #0x17]
    // 0xb6cce0: LoadField: r0 = r5->field_b
    //     0xb6cce0: ldur            w0, [x5, #0xb]
    // 0xb6cce4: r1 = LoadInt32Instr(r0)
    //     0xb6cce4: sbfx            x1, x0, #1, #0x1f
    // 0xb6cce8: mov             x0, x1
    // 0xb6ccec: mov             x1, x7
    // 0xb6ccf0: cmp             x1, x0
    // 0xb6ccf4: b.hs            #0xb6d7e4
    // 0xb6ccf8: LoadField: r0 = r5->field_f
    //     0xb6ccf8: ldur            w0, [x5, #0xf]
    // 0xb6ccfc: DecompressPointer r0
    //     0xb6ccfc: add             x0, x0, HEAP, lsl #32
    // 0xb6cd00: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6cd00: add             x16, x0, x7, lsl #2
    //     0xb6cd04: ldur            w1, [x16, #0xf]
    // 0xb6cd08: DecompressPointer r1
    //     0xb6cd08: add             x1, x1, HEAP, lsl #32
    // 0xb6cd0c: LoadField: r0 = r1->field_b
    //     0xb6cd0c: ldur            w0, [x1, #0xb]
    // 0xb6cd10: DecompressPointer r0
    //     0xb6cd10: add             x0, x0, HEAP, lsl #32
    // 0xb6cd14: cmp             w0, NULL
    // 0xb6cd18: b.ne            #0xb6cd24
    // 0xb6cd1c: r7 = ""
    //     0xb6cd1c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6cd20: b               #0xb6cd28
    // 0xb6cd24: mov             x7, x0
    // 0xb6cd28: cmp             w5, NULL
    // 0xb6cd2c: b.ne            #0xb6cd38
    // 0xb6cd30: r0 = Null
    //     0xb6cd30: mov             x0, NULL
    // 0xb6cd34: b               #0xb6cd70
    // 0xb6cd38: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xb6cd38: ldur            x8, [x3, #0x17]
    // 0xb6cd3c: LoadField: r0 = r5->field_b
    //     0xb6cd3c: ldur            w0, [x5, #0xb]
    // 0xb6cd40: r1 = LoadInt32Instr(r0)
    //     0xb6cd40: sbfx            x1, x0, #1, #0x1f
    // 0xb6cd44: mov             x0, x1
    // 0xb6cd48: mov             x1, x8
    // 0xb6cd4c: cmp             x1, x0
    // 0xb6cd50: b.hs            #0xb6d7e8
    // 0xb6cd54: LoadField: r0 = r5->field_f
    //     0xb6cd54: ldur            w0, [x5, #0xf]
    // 0xb6cd58: DecompressPointer r0
    //     0xb6cd58: add             x0, x0, HEAP, lsl #32
    // 0xb6cd5c: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb6cd5c: add             x16, x0, x8, lsl #2
    //     0xb6cd60: ldur            w1, [x16, #0xf]
    // 0xb6cd64: DecompressPointer r1
    //     0xb6cd64: add             x1, x1, HEAP, lsl #32
    // 0xb6cd68: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb6cd68: ldur            w0, [x1, #0x17]
    // 0xb6cd6c: DecompressPointer r0
    //     0xb6cd6c: add             x0, x0, HEAP, lsl #32
    // 0xb6cd70: cmp             w0, NULL
    // 0xb6cd74: b.ne            #0xb6cd80
    // 0xb6cd78: r3 = 0
    //     0xb6cd78: movz            x3, #0
    // 0xb6cd7c: b               #0xb6cd90
    // 0xb6cd80: r1 = LoadInt32Instr(r0)
    //     0xb6cd80: sbfx            x1, x0, #1, #0x1f
    //     0xb6cd84: tbz             w0, #0, #0xb6cd8c
    //     0xb6cd88: ldur            x1, [x0, #7]
    // 0xb6cd8c: mov             x3, x1
    // 0xb6cd90: LoadField: r5 = r4->field_1f
    //     0xb6cd90: ldur            w5, [x4, #0x1f]
    // 0xb6cd94: DecompressPointer r5
    //     0xb6cd94: add             x5, x5, HEAP, lsl #32
    // 0xb6cd98: r0 = BoxInt64Instr(r3)
    //     0xb6cd98: sbfiz           x0, x3, #1, #0x1f
    //     0xb6cd9c: cmp             x3, x0, asr #1
    //     0xb6cda0: b.eq            #0xb6cdac
    //     0xb6cda4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6cda8: stur            x3, [x0, #7]
    // 0xb6cdac: stp             x2, x5, [SP, #0x28]
    // 0xb6cdb0: r16 = true
    //     0xb6cdb0: add             x16, NULL, #0x20  ; true
    // 0xb6cdb4: stp             x6, x16, [SP, #0x18]
    // 0xb6cdb8: stp             x0, x7, [SP, #8]
    // 0xb6cdbc: r16 = "add_to_bag"
    //     0xb6cdbc: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb6cdc0: ldr             x16, [x16, #0xa38]
    // 0xb6cdc4: str             x16, [SP]
    // 0xb6cdc8: mov             x0, x5
    // 0xb6cdcc: ClosureCall
    //     0xb6cdcc: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xb6cdd0: ldr             x4, [x4, #0x330]
    //     0xb6cdd4: ldur            x2, [x0, #0x1f]
    //     0xb6cdd8: blr             x2
    // 0xb6cddc: b               #0xb6d790
    // 0xb6cde0: LoadField: r1 = r0->field_8f
    //     0xb6cde0: ldur            w1, [x0, #0x8f]
    // 0xb6cde4: DecompressPointer r1
    //     0xb6cde4: add             x1, x1, HEAP, lsl #32
    // 0xb6cde8: cmp             w1, NULL
    // 0xb6cdec: b.ne            #0xb6cdf8
    // 0xb6cdf0: r5 = ""
    //     0xb6cdf0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6cdf4: b               #0xb6cdfc
    // 0xb6cdf8: mov             x5, x1
    // 0xb6cdfc: cmp             w2, NULL
    // 0xb6ce00: b.ne            #0xb6ce0c
    // 0xb6ce04: r0 = Null
    //     0xb6ce04: mov             x0, NULL
    // 0xb6ce08: b               #0xb6ce44
    // 0xb6ce0c: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xb6ce0c: ldur            x6, [x3, #0x17]
    // 0xb6ce10: LoadField: r0 = r2->field_b
    //     0xb6ce10: ldur            w0, [x2, #0xb]
    // 0xb6ce14: r1 = LoadInt32Instr(r0)
    //     0xb6ce14: sbfx            x1, x0, #1, #0x1f
    // 0xb6ce18: mov             x0, x1
    // 0xb6ce1c: mov             x1, x6
    // 0xb6ce20: cmp             x1, x0
    // 0xb6ce24: b.hs            #0xb6d7ec
    // 0xb6ce28: LoadField: r0 = r2->field_f
    //     0xb6ce28: ldur            w0, [x2, #0xf]
    // 0xb6ce2c: DecompressPointer r0
    //     0xb6ce2c: add             x0, x0, HEAP, lsl #32
    // 0xb6ce30: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6ce30: add             x16, x0, x6, lsl #2
    //     0xb6ce34: ldur            w1, [x16, #0xf]
    // 0xb6ce38: DecompressPointer r1
    //     0xb6ce38: add             x1, x1, HEAP, lsl #32
    // 0xb6ce3c: LoadField: r0 = r1->field_b
    //     0xb6ce3c: ldur            w0, [x1, #0xb]
    // 0xb6ce40: DecompressPointer r0
    //     0xb6ce40: add             x0, x0, HEAP, lsl #32
    // 0xb6ce44: cmp             w0, NULL
    // 0xb6ce48: b.ne            #0xb6ce54
    // 0xb6ce4c: r6 = ""
    //     0xb6ce4c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6ce50: b               #0xb6ce58
    // 0xb6ce54: mov             x6, x0
    // 0xb6ce58: cmp             w2, NULL
    // 0xb6ce5c: b.ne            #0xb6ce68
    // 0xb6ce60: r0 = Null
    //     0xb6ce60: mov             x0, NULL
    // 0xb6ce64: b               #0xb6cea0
    // 0xb6ce68: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xb6ce68: ldur            x7, [x3, #0x17]
    // 0xb6ce6c: LoadField: r0 = r2->field_b
    //     0xb6ce6c: ldur            w0, [x2, #0xb]
    // 0xb6ce70: r1 = LoadInt32Instr(r0)
    //     0xb6ce70: sbfx            x1, x0, #1, #0x1f
    // 0xb6ce74: mov             x0, x1
    // 0xb6ce78: mov             x1, x7
    // 0xb6ce7c: cmp             x1, x0
    // 0xb6ce80: b.hs            #0xb6d7f0
    // 0xb6ce84: LoadField: r0 = r2->field_f
    //     0xb6ce84: ldur            w0, [x2, #0xf]
    // 0xb6ce88: DecompressPointer r0
    //     0xb6ce88: add             x0, x0, HEAP, lsl #32
    // 0xb6ce8c: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6ce8c: add             x16, x0, x7, lsl #2
    //     0xb6ce90: ldur            w1, [x16, #0xf]
    // 0xb6ce94: DecompressPointer r1
    //     0xb6ce94: add             x1, x1, HEAP, lsl #32
    // 0xb6ce98: LoadField: r0 = r1->field_7
    //     0xb6ce98: ldur            w0, [x1, #7]
    // 0xb6ce9c: DecompressPointer r0
    //     0xb6ce9c: add             x0, x0, HEAP, lsl #32
    // 0xb6cea0: cmp             w0, NULL
    // 0xb6cea4: b.ne            #0xb6ceb0
    // 0xb6cea8: r7 = ""
    //     0xb6cea8: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6ceac: b               #0xb6ceb4
    // 0xb6ceb0: mov             x7, x0
    // 0xb6ceb4: cmp             w2, NULL
    // 0xb6ceb8: b.ne            #0xb6cec4
    // 0xb6cebc: r0 = Null
    //     0xb6cebc: mov             x0, NULL
    // 0xb6cec0: b               #0xb6cefc
    // 0xb6cec4: ArrayLoad: r8 = r3[0]  ; List_8
    //     0xb6cec4: ldur            x8, [x3, #0x17]
    // 0xb6cec8: LoadField: r0 = r2->field_b
    //     0xb6cec8: ldur            w0, [x2, #0xb]
    // 0xb6cecc: r1 = LoadInt32Instr(r0)
    //     0xb6cecc: sbfx            x1, x0, #1, #0x1f
    // 0xb6ced0: mov             x0, x1
    // 0xb6ced4: mov             x1, x8
    // 0xb6ced8: cmp             x1, x0
    // 0xb6cedc: b.hs            #0xb6d7f4
    // 0xb6cee0: LoadField: r0 = r2->field_f
    //     0xb6cee0: ldur            w0, [x2, #0xf]
    // 0xb6cee4: DecompressPointer r0
    //     0xb6cee4: add             x0, x0, HEAP, lsl #32
    // 0xb6cee8: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb6cee8: add             x16, x0, x8, lsl #2
    //     0xb6ceec: ldur            w1, [x16, #0xf]
    // 0xb6cef0: DecompressPointer r1
    //     0xb6cef0: add             x1, x1, HEAP, lsl #32
    // 0xb6cef4: LoadField: r0 = r1->field_1b
    //     0xb6cef4: ldur            w0, [x1, #0x1b]
    // 0xb6cef8: DecompressPointer r0
    //     0xb6cef8: add             x0, x0, HEAP, lsl #32
    // 0xb6cefc: cmp             w0, NULL
    // 0xb6cf00: b.ne            #0xb6cf0c
    // 0xb6cf04: r2 = 0
    //     0xb6cf04: movz            x2, #0
    // 0xb6cf08: b               #0xb6cf1c
    // 0xb6cf0c: r1 = LoadInt32Instr(r0)
    //     0xb6cf0c: sbfx            x1, x0, #1, #0x1f
    //     0xb6cf10: tbz             w0, #0, #0xb6cf18
    //     0xb6cf14: ldur            x1, [x0, #7]
    // 0xb6cf18: mov             x2, x1
    // 0xb6cf1c: LoadField: r3 = r4->field_1f
    //     0xb6cf1c: ldur            w3, [x4, #0x1f]
    // 0xb6cf20: DecompressPointer r3
    //     0xb6cf20: add             x3, x3, HEAP, lsl #32
    // 0xb6cf24: r0 = BoxInt64Instr(r2)
    //     0xb6cf24: sbfiz           x0, x2, #1, #0x1f
    //     0xb6cf28: cmp             x2, x0, asr #1
    //     0xb6cf2c: b.eq            #0xb6cf38
    //     0xb6cf30: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6cf34: stur            x2, [x0, #7]
    // 0xb6cf38: stp             x5, x3, [SP, #0x28]
    // 0xb6cf3c: r16 = true
    //     0xb6cf3c: add             x16, NULL, #0x20  ; true
    // 0xb6cf40: stp             x6, x16, [SP, #0x18]
    // 0xb6cf44: stp             x0, x7, [SP, #8]
    // 0xb6cf48: r16 = "add_to_bag"
    //     0xb6cf48: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb6cf4c: ldr             x16, [x16, #0xa38]
    // 0xb6cf50: str             x16, [SP]
    // 0xb6cf54: mov             x0, x3
    // 0xb6cf58: ClosureCall
    //     0xb6cf58: add             x4, PP, #0x48, lsl #12  ; [pp+0x48330] List(5) [0, 0x7, 0x7, 0x7, Null]
    //     0xb6cf5c: ldr             x4, [x4, #0x330]
    //     0xb6cf60: ldur            x2, [x0, #0x1f]
    //     0xb6cf64: blr             x2
    // 0xb6cf68: b               #0xb6d790
    // 0xb6cf6c: LoadField: r5 = r0->field_73
    //     0xb6cf6c: ldur            w5, [x0, #0x73]
    // 0xb6cf70: DecompressPointer r5
    //     0xb6cf70: add             x5, x5, HEAP, lsl #32
    // 0xb6cf74: cmp             w5, NULL
    // 0xb6cf78: b.ne            #0xb6cf84
    // 0xb6cf7c: r1 = Null
    //     0xb6cf7c: mov             x1, NULL
    // 0xb6cf80: b               #0xb6cf9c
    // 0xb6cf84: LoadField: r1 = r5->field_b
    //     0xb6cf84: ldur            w1, [x5, #0xb]
    // 0xb6cf88: cbz             w1, #0xb6cf94
    // 0xb6cf8c: r6 = false
    //     0xb6cf8c: add             x6, NULL, #0x30  ; false
    // 0xb6cf90: b               #0xb6cf98
    // 0xb6cf94: r6 = true
    //     0xb6cf94: add             x6, NULL, #0x20  ; true
    // 0xb6cf98: mov             x1, x6
    // 0xb6cf9c: cmp             w1, NULL
    // 0xb6cfa0: b.eq            #0xb6cfa8
    // 0xb6cfa4: tbnz            w1, #4, #0xb6d394
    // 0xb6cfa8: LoadField: r1 = r4->field_27
    //     0xb6cfa8: ldur            w1, [x4, #0x27]
    // 0xb6cfac: DecompressPointer r1
    //     0xb6cfac: add             x1, x1, HEAP, lsl #32
    // 0xb6cfb0: tbnz            w1, #4, #0xb6d290
    // 0xb6cfb4: LoadField: r5 = r0->field_6f
    //     0xb6cfb4: ldur            w5, [x0, #0x6f]
    // 0xb6cfb8: DecompressPointer r5
    //     0xb6cfb8: add             x5, x5, HEAP, lsl #32
    // 0xb6cfbc: cmp             w5, NULL
    // 0xb6cfc0: b.ne            #0xb6cfcc
    // 0xb6cfc4: r0 = Null
    //     0xb6cfc4: mov             x0, NULL
    // 0xb6cfc8: b               #0xb6d004
    // 0xb6cfcc: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xb6cfcc: ldur            x6, [x3, #0x17]
    // 0xb6cfd0: LoadField: r0 = r5->field_b
    //     0xb6cfd0: ldur            w0, [x5, #0xb]
    // 0xb6cfd4: r1 = LoadInt32Instr(r0)
    //     0xb6cfd4: sbfx            x1, x0, #1, #0x1f
    // 0xb6cfd8: mov             x0, x1
    // 0xb6cfdc: mov             x1, x6
    // 0xb6cfe0: cmp             x1, x0
    // 0xb6cfe4: b.hs            #0xb6d7f8
    // 0xb6cfe8: LoadField: r0 = r5->field_f
    //     0xb6cfe8: ldur            w0, [x5, #0xf]
    // 0xb6cfec: DecompressPointer r0
    //     0xb6cfec: add             x0, x0, HEAP, lsl #32
    // 0xb6cff0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6cff0: add             x16, x0, x6, lsl #2
    //     0xb6cff4: ldur            w1, [x16, #0xf]
    // 0xb6cff8: DecompressPointer r1
    //     0xb6cff8: add             x1, x1, HEAP, lsl #32
    // 0xb6cffc: LoadField: r0 = r1->field_b
    //     0xb6cffc: ldur            w0, [x1, #0xb]
    // 0xb6d000: DecompressPointer r0
    //     0xb6d000: add             x0, x0, HEAP, lsl #32
    // 0xb6d004: cmp             w0, NULL
    // 0xb6d008: b.ne            #0xb6d014
    // 0xb6d00c: r6 = ""
    //     0xb6d00c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d010: b               #0xb6d018
    // 0xb6d014: mov             x6, x0
    // 0xb6d018: stur            x6, [fp, #-0x10]
    // 0xb6d01c: cmp             w5, NULL
    // 0xb6d020: b.ne            #0xb6d02c
    // 0xb6d024: r0 = Null
    //     0xb6d024: mov             x0, NULL
    // 0xb6d028: b               #0xb6d07c
    // 0xb6d02c: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xb6d02c: ldur            x7, [x3, #0x17]
    // 0xb6d030: LoadField: r0 = r5->field_b
    //     0xb6d030: ldur            w0, [x5, #0xb]
    // 0xb6d034: r1 = LoadInt32Instr(r0)
    //     0xb6d034: sbfx            x1, x0, #1, #0x1f
    // 0xb6d038: mov             x0, x1
    // 0xb6d03c: mov             x1, x7
    // 0xb6d040: cmp             x1, x0
    // 0xb6d044: b.hs            #0xb6d7fc
    // 0xb6d048: LoadField: r0 = r5->field_f
    //     0xb6d048: ldur            w0, [x5, #0xf]
    // 0xb6d04c: DecompressPointer r0
    //     0xb6d04c: add             x0, x0, HEAP, lsl #32
    // 0xb6d050: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6d050: add             x16, x0, x7, lsl #2
    //     0xb6d054: ldur            w1, [x16, #0xf]
    // 0xb6d058: DecompressPointer r1
    //     0xb6d058: add             x1, x1, HEAP, lsl #32
    // 0xb6d05c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb6d05c: ldur            w0, [x1, #0x17]
    // 0xb6d060: DecompressPointer r0
    //     0xb6d060: add             x0, x0, HEAP, lsl #32
    // 0xb6d064: cmp             w0, NULL
    // 0xb6d068: b.ne            #0xb6d074
    // 0xb6d06c: r0 = Null
    //     0xb6d06c: mov             x0, NULL
    // 0xb6d070: b               #0xb6d07c
    // 0xb6d074: stp             x0, NULL, [SP]
    // 0xb6d078: r0 = _Double.fromInteger()
    //     0xb6d078: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xb6d07c: cmp             w0, NULL
    // 0xb6d080: b.ne            #0xb6d08c
    // 0xb6d084: d0 = 0.000000
    //     0xb6d084: eor             v0.16b, v0.16b, v0.16b
    // 0xb6d088: b               #0xb6d090
    // 0xb6d08c: LoadField: d0 = r0->field_7
    //     0xb6d08c: ldur            d0, [x0, #7]
    // 0xb6d090: ldur            x0, [fp, #-8]
    // 0xb6d094: stur            d0, [fp, #-0x48]
    // 0xb6d098: r1 = Null
    //     0xb6d098: mov             x1, NULL
    // 0xb6d09c: r2 = 12
    //     0xb6d09c: movz            x2, #0xc
    // 0xb6d0a0: r0 = AllocateArray()
    //     0xb6d0a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6d0a4: mov             x2, x0
    // 0xb6d0a8: stur            x2, [fp, #-0x20]
    // 0xb6d0ac: r16 = "id"
    //     0xb6d0ac: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0xb6d0b0: ldr             x16, [x16, #0x400]
    // 0xb6d0b4: StoreField: r2->field_f = r16
    //     0xb6d0b4: stur            w16, [x2, #0xf]
    // 0xb6d0b8: ldur            x3, [fp, #-8]
    // 0xb6d0bc: LoadField: r4 = r3->field_f
    //     0xb6d0bc: ldur            w4, [x3, #0xf]
    // 0xb6d0c0: DecompressPointer r4
    //     0xb6d0c0: add             x4, x4, HEAP, lsl #32
    // 0xb6d0c4: LoadField: r0 = r4->field_b
    //     0xb6d0c4: ldur            w0, [x4, #0xb]
    // 0xb6d0c8: DecompressPointer r0
    //     0xb6d0c8: add             x0, x0, HEAP, lsl #32
    // 0xb6d0cc: cmp             w0, NULL
    // 0xb6d0d0: b.eq            #0xb6d800
    // 0xb6d0d4: LoadField: r1 = r0->field_b
    //     0xb6d0d4: ldur            w1, [x0, #0xb]
    // 0xb6d0d8: DecompressPointer r1
    //     0xb6d0d8: add             x1, x1, HEAP, lsl #32
    // 0xb6d0dc: LoadField: r5 = r1->field_6f
    //     0xb6d0dc: ldur            w5, [x1, #0x6f]
    // 0xb6d0e0: DecompressPointer r5
    //     0xb6d0e0: add             x5, x5, HEAP, lsl #32
    // 0xb6d0e4: cmp             w5, NULL
    // 0xb6d0e8: b.ne            #0xb6d0f4
    // 0xb6d0ec: r0 = Null
    //     0xb6d0ec: mov             x0, NULL
    // 0xb6d0f0: b               #0xb6d12c
    // 0xb6d0f4: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xb6d0f4: ldur            x6, [x4, #0x17]
    // 0xb6d0f8: LoadField: r0 = r5->field_b
    //     0xb6d0f8: ldur            w0, [x5, #0xb]
    // 0xb6d0fc: r1 = LoadInt32Instr(r0)
    //     0xb6d0fc: sbfx            x1, x0, #1, #0x1f
    // 0xb6d100: mov             x0, x1
    // 0xb6d104: mov             x1, x6
    // 0xb6d108: cmp             x1, x0
    // 0xb6d10c: b.hs            #0xb6d804
    // 0xb6d110: LoadField: r0 = r5->field_f
    //     0xb6d110: ldur            w0, [x5, #0xf]
    // 0xb6d114: DecompressPointer r0
    //     0xb6d114: add             x0, x0, HEAP, lsl #32
    // 0xb6d118: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6d118: add             x16, x0, x6, lsl #2
    //     0xb6d11c: ldur            w1, [x16, #0xf]
    // 0xb6d120: DecompressPointer r1
    //     0xb6d120: add             x1, x1, HEAP, lsl #32
    // 0xb6d124: LoadField: r0 = r1->field_b
    //     0xb6d124: ldur            w0, [x1, #0xb]
    // 0xb6d128: DecompressPointer r0
    //     0xb6d128: add             x0, x0, HEAP, lsl #32
    // 0xb6d12c: cmp             w0, NULL
    // 0xb6d130: b.ne            #0xb6d138
    // 0xb6d134: r0 = ""
    //     0xb6d134: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d138: StoreField: r2->field_13 = r0
    //     0xb6d138: stur            w0, [x2, #0x13]
    // 0xb6d13c: r16 = "quantity"
    //     0xb6d13c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xb6d140: ldr             x16, [x16, #0x428]
    // 0xb6d144: ArrayStore: r2[0] = r16  ; List_4
    //     0xb6d144: stur            w16, [x2, #0x17]
    // 0xb6d148: r16 = 2
    //     0xb6d148: movz            x16, #0x2
    // 0xb6d14c: StoreField: r2->field_1b = r16
    //     0xb6d14c: stur            w16, [x2, #0x1b]
    // 0xb6d150: r16 = "item_price"
    //     0xb6d150: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0xb6d154: ldr             x16, [x16, #0x498]
    // 0xb6d158: StoreField: r2->field_1f = r16
    //     0xb6d158: stur            w16, [x2, #0x1f]
    // 0xb6d15c: cmp             w5, NULL
    // 0xb6d160: b.ne            #0xb6d16c
    // 0xb6d164: r0 = Null
    //     0xb6d164: mov             x0, NULL
    // 0xb6d168: b               #0xb6d1bc
    // 0xb6d16c: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xb6d16c: ldur            x6, [x4, #0x17]
    // 0xb6d170: LoadField: r0 = r5->field_b
    //     0xb6d170: ldur            w0, [x5, #0xb]
    // 0xb6d174: r1 = LoadInt32Instr(r0)
    //     0xb6d174: sbfx            x1, x0, #1, #0x1f
    // 0xb6d178: mov             x0, x1
    // 0xb6d17c: mov             x1, x6
    // 0xb6d180: cmp             x1, x0
    // 0xb6d184: b.hs            #0xb6d808
    // 0xb6d188: LoadField: r0 = r5->field_f
    //     0xb6d188: ldur            w0, [x5, #0xf]
    // 0xb6d18c: DecompressPointer r0
    //     0xb6d18c: add             x0, x0, HEAP, lsl #32
    // 0xb6d190: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6d190: add             x16, x0, x6, lsl #2
    //     0xb6d194: ldur            w1, [x16, #0xf]
    // 0xb6d198: DecompressPointer r1
    //     0xb6d198: add             x1, x1, HEAP, lsl #32
    // 0xb6d19c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb6d19c: ldur            w0, [x1, #0x17]
    // 0xb6d1a0: DecompressPointer r0
    //     0xb6d1a0: add             x0, x0, HEAP, lsl #32
    // 0xb6d1a4: cmp             w0, NULL
    // 0xb6d1a8: b.ne            #0xb6d1b4
    // 0xb6d1ac: r0 = Null
    //     0xb6d1ac: mov             x0, NULL
    // 0xb6d1b0: b               #0xb6d1bc
    // 0xb6d1b4: stp             x0, NULL, [SP]
    // 0xb6d1b8: r0 = _Double.fromInteger()
    //     0xb6d1b8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xb6d1bc: cmp             w0, NULL
    // 0xb6d1c0: b.ne            #0xb6d1cc
    // 0xb6d1c4: d1 = 0.000000
    //     0xb6d1c4: eor             v1.16b, v1.16b, v1.16b
    // 0xb6d1c8: b               #0xb6d1d4
    // 0xb6d1cc: LoadField: d0 = r0->field_7
    //     0xb6d1cc: ldur            d0, [x0, #7]
    // 0xb6d1d0: mov             v1.16b, v0.16b
    // 0xb6d1d4: ldur            d0, [fp, #-0x48]
    // 0xb6d1d8: ldur            x2, [fp, #-0x18]
    // 0xb6d1dc: r0 = inline_Allocate_Double()
    //     0xb6d1dc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb6d1e0: add             x0, x0, #0x10
    //     0xb6d1e4: cmp             x1, x0
    //     0xb6d1e8: b.ls            #0xb6d80c
    //     0xb6d1ec: str             x0, [THR, #0x50]  ; THR::top
    //     0xb6d1f0: sub             x0, x0, #0xf
    //     0xb6d1f4: movz            x1, #0xe15c
    //     0xb6d1f8: movk            x1, #0x3, lsl #16
    //     0xb6d1fc: stur            x1, [x0, #-1]
    // 0xb6d200: StoreField: r0->field_7 = d1
    //     0xb6d200: stur            d1, [x0, #7]
    // 0xb6d204: ldur            x1, [fp, #-0x20]
    // 0xb6d208: ArrayStore: r1[5] = r0  ; List_4
    //     0xb6d208: add             x25, x1, #0x23
    //     0xb6d20c: str             w0, [x25]
    //     0xb6d210: tbz             w0, #0, #0xb6d22c
    //     0xb6d214: ldurb           w16, [x1, #-1]
    //     0xb6d218: ldurb           w17, [x0, #-1]
    //     0xb6d21c: and             x16, x17, x16, lsr #2
    //     0xb6d220: tst             x16, HEAP, lsr #32
    //     0xb6d224: b.eq            #0xb6d22c
    //     0xb6d228: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6d22c: r16 = <String, dynamic>
    //     0xb6d22c: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xb6d230: ldur            lr, [fp, #-0x20]
    // 0xb6d234: stp             lr, x16, [SP]
    // 0xb6d238: r0 = Map._fromLiteral()
    //     0xb6d238: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb6d23c: ldur            x2, [fp, #-0x18]
    // 0xb6d240: LoadField: r1 = r2->field_23
    //     0xb6d240: ldur            w1, [x2, #0x23]
    // 0xb6d244: DecompressPointer r1
    //     0xb6d244: add             x1, x1, HEAP, lsl #32
    // 0xb6d248: ldur            d0, [fp, #-0x48]
    // 0xb6d24c: r2 = inline_Allocate_Double()
    //     0xb6d24c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb6d250: add             x2, x2, #0x10
    //     0xb6d254: cmp             x3, x2
    //     0xb6d258: b.ls            #0xb6d824
    //     0xb6d25c: str             x2, [THR, #0x50]  ; THR::top
    //     0xb6d260: sub             x2, x2, #0xf
    //     0xb6d264: movz            x3, #0xe15c
    //     0xb6d268: movk            x3, #0x3, lsl #16
    //     0xb6d26c: stur            x3, [x2, #-1]
    // 0xb6d270: StoreField: r2->field_7 = d0
    //     0xb6d270: stur            d0, [x2, #7]
    // 0xb6d274: ldur            x16, [fp, #-0x10]
    // 0xb6d278: stp             x16, x1, [SP, #0x10]
    // 0xb6d27c: stp             x0, x2, [SP]
    // 0xb6d280: mov             x0, x1
    // 0xb6d284: ClosureCall
    //     0xb6d284: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xb6d288: ldur            x2, [x0, #0x1f]
    //     0xb6d28c: blr             x2
    // 0xb6d290: ldur            x4, [fp, #-8]
    // 0xb6d294: LoadField: r2 = r4->field_f
    //     0xb6d294: ldur            w2, [x4, #0xf]
    // 0xb6d298: DecompressPointer r2
    //     0xb6d298: add             x2, x2, HEAP, lsl #32
    // 0xb6d29c: LoadField: r3 = r2->field_b
    //     0xb6d29c: ldur            w3, [x2, #0xb]
    // 0xb6d2a0: DecompressPointer r3
    //     0xb6d2a0: add             x3, x3, HEAP, lsl #32
    // 0xb6d2a4: cmp             w3, NULL
    // 0xb6d2a8: b.eq            #0xb6d840
    // 0xb6d2ac: LoadField: r0 = r3->field_b
    //     0xb6d2ac: ldur            w0, [x3, #0xb]
    // 0xb6d2b0: DecompressPointer r0
    //     0xb6d2b0: add             x0, x0, HEAP, lsl #32
    // 0xb6d2b4: LoadField: r4 = r0->field_6f
    //     0xb6d2b4: ldur            w4, [x0, #0x6f]
    // 0xb6d2b8: DecompressPointer r4
    //     0xb6d2b8: add             x4, x4, HEAP, lsl #32
    // 0xb6d2bc: cmp             w4, NULL
    // 0xb6d2c0: b.ne            #0xb6d2cc
    // 0xb6d2c4: r0 = Null
    //     0xb6d2c4: mov             x0, NULL
    // 0xb6d2c8: b               #0xb6d304
    // 0xb6d2cc: ArrayLoad: r5 = r2[0]  ; List_8
    //     0xb6d2cc: ldur            x5, [x2, #0x17]
    // 0xb6d2d0: LoadField: r0 = r4->field_b
    //     0xb6d2d0: ldur            w0, [x4, #0xb]
    // 0xb6d2d4: r1 = LoadInt32Instr(r0)
    //     0xb6d2d4: sbfx            x1, x0, #1, #0x1f
    // 0xb6d2d8: mov             x0, x1
    // 0xb6d2dc: mov             x1, x5
    // 0xb6d2e0: cmp             x1, x0
    // 0xb6d2e4: b.hs            #0xb6d844
    // 0xb6d2e8: LoadField: r0 = r4->field_f
    //     0xb6d2e8: ldur            w0, [x4, #0xf]
    // 0xb6d2ec: DecompressPointer r0
    //     0xb6d2ec: add             x0, x0, HEAP, lsl #32
    // 0xb6d2f0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb6d2f0: add             x16, x0, x5, lsl #2
    //     0xb6d2f4: ldur            w1, [x16, #0xf]
    // 0xb6d2f8: DecompressPointer r1
    //     0xb6d2f8: add             x1, x1, HEAP, lsl #32
    // 0xb6d2fc: LoadField: r0 = r1->field_f
    //     0xb6d2fc: ldur            w0, [x1, #0xf]
    // 0xb6d300: DecompressPointer r0
    //     0xb6d300: add             x0, x0, HEAP, lsl #32
    // 0xb6d304: cmp             w0, NULL
    // 0xb6d308: b.ne            #0xb6d314
    // 0xb6d30c: r5 = ""
    //     0xb6d30c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d310: b               #0xb6d318
    // 0xb6d314: mov             x5, x0
    // 0xb6d318: cmp             w4, NULL
    // 0xb6d31c: b.ne            #0xb6d328
    // 0xb6d320: r0 = Null
    //     0xb6d320: mov             x0, NULL
    // 0xb6d324: b               #0xb6d360
    // 0xb6d328: ArrayLoad: r6 = r2[0]  ; List_8
    //     0xb6d328: ldur            x6, [x2, #0x17]
    // 0xb6d32c: LoadField: r0 = r4->field_b
    //     0xb6d32c: ldur            w0, [x4, #0xb]
    // 0xb6d330: r1 = LoadInt32Instr(r0)
    //     0xb6d330: sbfx            x1, x0, #1, #0x1f
    // 0xb6d334: mov             x0, x1
    // 0xb6d338: mov             x1, x6
    // 0xb6d33c: cmp             x1, x0
    // 0xb6d340: b.hs            #0xb6d848
    // 0xb6d344: LoadField: r0 = r4->field_f
    //     0xb6d344: ldur            w0, [x4, #0xf]
    // 0xb6d348: DecompressPointer r0
    //     0xb6d348: add             x0, x0, HEAP, lsl #32
    // 0xb6d34c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6d34c: add             x16, x0, x6, lsl #2
    //     0xb6d350: ldur            w1, [x16, #0xf]
    // 0xb6d354: DecompressPointer r1
    //     0xb6d354: add             x1, x1, HEAP, lsl #32
    // 0xb6d358: LoadField: r0 = r1->field_b
    //     0xb6d358: ldur            w0, [x1, #0xb]
    // 0xb6d35c: DecompressPointer r0
    //     0xb6d35c: add             x0, x0, HEAP, lsl #32
    // 0xb6d360: cmp             w0, NULL
    // 0xb6d364: b.ne            #0xb6d36c
    // 0xb6d368: r0 = ""
    //     0xb6d368: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d36c: LoadField: r1 = r3->field_1b
    //     0xb6d36c: ldur            w1, [x3, #0x1b]
    // 0xb6d370: DecompressPointer r1
    //     0xb6d370: add             x1, x1, HEAP, lsl #32
    // 0xb6d374: stp             x5, x1, [SP, #0x10]
    // 0xb6d378: r16 = 2
    //     0xb6d378: movz            x16, #0x2
    // 0xb6d37c: stp             x16, x0, [SP]
    // 0xb6d380: mov             x0, x1
    // 0xb6d384: ClosureCall
    //     0xb6d384: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xb6d388: ldur            x2, [x0, #0x1f]
    //     0xb6d38c: blr             x2
    // 0xb6d390: b               #0xb6d790
    // 0xb6d394: mov             x16, x4
    // 0xb6d398: mov             x4, x2
    // 0xb6d39c: mov             x2, x16
    // 0xb6d3a0: LoadField: r0 = r2->field_27
    //     0xb6d3a0: ldur            w0, [x2, #0x27]
    // 0xb6d3a4: DecompressPointer r0
    //     0xb6d3a4: add             x0, x0, HEAP, lsl #32
    // 0xb6d3a8: tbnz            w0, #4, #0xb6d684
    // 0xb6d3ac: cmp             w5, NULL
    // 0xb6d3b0: b.ne            #0xb6d3bc
    // 0xb6d3b4: r0 = Null
    //     0xb6d3b4: mov             x0, NULL
    // 0xb6d3b8: b               #0xb6d3f4
    // 0xb6d3bc: ArrayLoad: r6 = r3[0]  ; List_8
    //     0xb6d3bc: ldur            x6, [x3, #0x17]
    // 0xb6d3c0: LoadField: r0 = r5->field_b
    //     0xb6d3c0: ldur            w0, [x5, #0xb]
    // 0xb6d3c4: r1 = LoadInt32Instr(r0)
    //     0xb6d3c4: sbfx            x1, x0, #1, #0x1f
    // 0xb6d3c8: mov             x0, x1
    // 0xb6d3cc: mov             x1, x6
    // 0xb6d3d0: cmp             x1, x0
    // 0xb6d3d4: b.hs            #0xb6d84c
    // 0xb6d3d8: LoadField: r0 = r5->field_f
    //     0xb6d3d8: ldur            w0, [x5, #0xf]
    // 0xb6d3dc: DecompressPointer r0
    //     0xb6d3dc: add             x0, x0, HEAP, lsl #32
    // 0xb6d3e0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6d3e0: add             x16, x0, x6, lsl #2
    //     0xb6d3e4: ldur            w1, [x16, #0xf]
    // 0xb6d3e8: DecompressPointer r1
    //     0xb6d3e8: add             x1, x1, HEAP, lsl #32
    // 0xb6d3ec: LoadField: r0 = r1->field_7
    //     0xb6d3ec: ldur            w0, [x1, #7]
    // 0xb6d3f0: DecompressPointer r0
    //     0xb6d3f0: add             x0, x0, HEAP, lsl #32
    // 0xb6d3f4: cmp             w0, NULL
    // 0xb6d3f8: b.ne            #0xb6d404
    // 0xb6d3fc: r6 = ""
    //     0xb6d3fc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d400: b               #0xb6d408
    // 0xb6d404: mov             x6, x0
    // 0xb6d408: stur            x6, [fp, #-0x10]
    // 0xb6d40c: cmp             w5, NULL
    // 0xb6d410: b.ne            #0xb6d41c
    // 0xb6d414: r0 = Null
    //     0xb6d414: mov             x0, NULL
    // 0xb6d418: b               #0xb6d46c
    // 0xb6d41c: ArrayLoad: r7 = r3[0]  ; List_8
    //     0xb6d41c: ldur            x7, [x3, #0x17]
    // 0xb6d420: LoadField: r0 = r5->field_b
    //     0xb6d420: ldur            w0, [x5, #0xb]
    // 0xb6d424: r1 = LoadInt32Instr(r0)
    //     0xb6d424: sbfx            x1, x0, #1, #0x1f
    // 0xb6d428: mov             x0, x1
    // 0xb6d42c: mov             x1, x7
    // 0xb6d430: cmp             x1, x0
    // 0xb6d434: b.hs            #0xb6d850
    // 0xb6d438: LoadField: r0 = r5->field_f
    //     0xb6d438: ldur            w0, [x5, #0xf]
    // 0xb6d43c: DecompressPointer r0
    //     0xb6d43c: add             x0, x0, HEAP, lsl #32
    // 0xb6d440: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6d440: add             x16, x0, x7, lsl #2
    //     0xb6d444: ldur            w1, [x16, #0xf]
    // 0xb6d448: DecompressPointer r1
    //     0xb6d448: add             x1, x1, HEAP, lsl #32
    // 0xb6d44c: LoadField: r0 = r1->field_1b
    //     0xb6d44c: ldur            w0, [x1, #0x1b]
    // 0xb6d450: DecompressPointer r0
    //     0xb6d450: add             x0, x0, HEAP, lsl #32
    // 0xb6d454: cmp             w0, NULL
    // 0xb6d458: b.ne            #0xb6d464
    // 0xb6d45c: r0 = Null
    //     0xb6d45c: mov             x0, NULL
    // 0xb6d460: b               #0xb6d46c
    // 0xb6d464: stp             x0, NULL, [SP]
    // 0xb6d468: r0 = _Double.fromInteger()
    //     0xb6d468: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xb6d46c: cmp             w0, NULL
    // 0xb6d470: b.ne            #0xb6d47c
    // 0xb6d474: d0 = 0.000000
    //     0xb6d474: eor             v0.16b, v0.16b, v0.16b
    // 0xb6d478: b               #0xb6d480
    // 0xb6d47c: LoadField: d0 = r0->field_7
    //     0xb6d47c: ldur            d0, [x0, #7]
    // 0xb6d480: ldur            x0, [fp, #-8]
    // 0xb6d484: stur            d0, [fp, #-0x48]
    // 0xb6d488: r1 = Null
    //     0xb6d488: mov             x1, NULL
    // 0xb6d48c: r2 = 12
    //     0xb6d48c: movz            x2, #0xc
    // 0xb6d490: r0 = AllocateArray()
    //     0xb6d490: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6d494: mov             x2, x0
    // 0xb6d498: stur            x2, [fp, #-0x20]
    // 0xb6d49c: r16 = "id"
    //     0xb6d49c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0xb6d4a0: ldr             x16, [x16, #0x400]
    // 0xb6d4a4: StoreField: r2->field_f = r16
    //     0xb6d4a4: stur            w16, [x2, #0xf]
    // 0xb6d4a8: ldur            x3, [fp, #-8]
    // 0xb6d4ac: LoadField: r4 = r3->field_f
    //     0xb6d4ac: ldur            w4, [x3, #0xf]
    // 0xb6d4b0: DecompressPointer r4
    //     0xb6d4b0: add             x4, x4, HEAP, lsl #32
    // 0xb6d4b4: LoadField: r0 = r4->field_b
    //     0xb6d4b4: ldur            w0, [x4, #0xb]
    // 0xb6d4b8: DecompressPointer r0
    //     0xb6d4b8: add             x0, x0, HEAP, lsl #32
    // 0xb6d4bc: cmp             w0, NULL
    // 0xb6d4c0: b.eq            #0xb6d854
    // 0xb6d4c4: LoadField: r1 = r0->field_b
    //     0xb6d4c4: ldur            w1, [x0, #0xb]
    // 0xb6d4c8: DecompressPointer r1
    //     0xb6d4c8: add             x1, x1, HEAP, lsl #32
    // 0xb6d4cc: LoadField: r5 = r1->field_73
    //     0xb6d4cc: ldur            w5, [x1, #0x73]
    // 0xb6d4d0: DecompressPointer r5
    //     0xb6d4d0: add             x5, x5, HEAP, lsl #32
    // 0xb6d4d4: cmp             w5, NULL
    // 0xb6d4d8: b.ne            #0xb6d4e4
    // 0xb6d4dc: r0 = Null
    //     0xb6d4dc: mov             x0, NULL
    // 0xb6d4e0: b               #0xb6d51c
    // 0xb6d4e4: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xb6d4e4: ldur            x6, [x4, #0x17]
    // 0xb6d4e8: LoadField: r0 = r5->field_b
    //     0xb6d4e8: ldur            w0, [x5, #0xb]
    // 0xb6d4ec: r1 = LoadInt32Instr(r0)
    //     0xb6d4ec: sbfx            x1, x0, #1, #0x1f
    // 0xb6d4f0: mov             x0, x1
    // 0xb6d4f4: mov             x1, x6
    // 0xb6d4f8: cmp             x1, x0
    // 0xb6d4fc: b.hs            #0xb6d858
    // 0xb6d500: LoadField: r0 = r5->field_f
    //     0xb6d500: ldur            w0, [x5, #0xf]
    // 0xb6d504: DecompressPointer r0
    //     0xb6d504: add             x0, x0, HEAP, lsl #32
    // 0xb6d508: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6d508: add             x16, x0, x6, lsl #2
    //     0xb6d50c: ldur            w1, [x16, #0xf]
    // 0xb6d510: DecompressPointer r1
    //     0xb6d510: add             x1, x1, HEAP, lsl #32
    // 0xb6d514: LoadField: r0 = r1->field_7
    //     0xb6d514: ldur            w0, [x1, #7]
    // 0xb6d518: DecompressPointer r0
    //     0xb6d518: add             x0, x0, HEAP, lsl #32
    // 0xb6d51c: cmp             w0, NULL
    // 0xb6d520: b.ne            #0xb6d528
    // 0xb6d524: r0 = ""
    //     0xb6d524: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d528: StoreField: r2->field_13 = r0
    //     0xb6d528: stur            w0, [x2, #0x13]
    // 0xb6d52c: r16 = "quantity"
    //     0xb6d52c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xb6d530: ldr             x16, [x16, #0x428]
    // 0xb6d534: ArrayStore: r2[0] = r16  ; List_4
    //     0xb6d534: stur            w16, [x2, #0x17]
    // 0xb6d538: r16 = 2
    //     0xb6d538: movz            x16, #0x2
    // 0xb6d53c: StoreField: r2->field_1b = r16
    //     0xb6d53c: stur            w16, [x2, #0x1b]
    // 0xb6d540: r16 = "item_price"
    //     0xb6d540: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0xb6d544: ldr             x16, [x16, #0x498]
    // 0xb6d548: StoreField: r2->field_1f = r16
    //     0xb6d548: stur            w16, [x2, #0x1f]
    // 0xb6d54c: cmp             w5, NULL
    // 0xb6d550: b.ne            #0xb6d55c
    // 0xb6d554: r0 = Null
    //     0xb6d554: mov             x0, NULL
    // 0xb6d558: b               #0xb6d5ac
    // 0xb6d55c: ArrayLoad: r6 = r4[0]  ; List_8
    //     0xb6d55c: ldur            x6, [x4, #0x17]
    // 0xb6d560: LoadField: r0 = r5->field_b
    //     0xb6d560: ldur            w0, [x5, #0xb]
    // 0xb6d564: r1 = LoadInt32Instr(r0)
    //     0xb6d564: sbfx            x1, x0, #1, #0x1f
    // 0xb6d568: mov             x0, x1
    // 0xb6d56c: mov             x1, x6
    // 0xb6d570: cmp             x1, x0
    // 0xb6d574: b.hs            #0xb6d85c
    // 0xb6d578: LoadField: r0 = r5->field_f
    //     0xb6d578: ldur            w0, [x5, #0xf]
    // 0xb6d57c: DecompressPointer r0
    //     0xb6d57c: add             x0, x0, HEAP, lsl #32
    // 0xb6d580: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6d580: add             x16, x0, x6, lsl #2
    //     0xb6d584: ldur            w1, [x16, #0xf]
    // 0xb6d588: DecompressPointer r1
    //     0xb6d588: add             x1, x1, HEAP, lsl #32
    // 0xb6d58c: LoadField: r0 = r1->field_1b
    //     0xb6d58c: ldur            w0, [x1, #0x1b]
    // 0xb6d590: DecompressPointer r0
    //     0xb6d590: add             x0, x0, HEAP, lsl #32
    // 0xb6d594: cmp             w0, NULL
    // 0xb6d598: b.ne            #0xb6d5a4
    // 0xb6d59c: r0 = Null
    //     0xb6d59c: mov             x0, NULL
    // 0xb6d5a0: b               #0xb6d5ac
    // 0xb6d5a4: stp             x0, NULL, [SP]
    // 0xb6d5a8: r0 = _Double.fromInteger()
    //     0xb6d5a8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xb6d5ac: cmp             w0, NULL
    // 0xb6d5b0: b.ne            #0xb6d5bc
    // 0xb6d5b4: d1 = 0.000000
    //     0xb6d5b4: eor             v1.16b, v1.16b, v1.16b
    // 0xb6d5b8: b               #0xb6d5c4
    // 0xb6d5bc: LoadField: d0 = r0->field_7
    //     0xb6d5bc: ldur            d0, [x0, #7]
    // 0xb6d5c0: mov             v1.16b, v0.16b
    // 0xb6d5c4: ldur            d0, [fp, #-0x48]
    // 0xb6d5c8: ldur            x2, [fp, #-0x18]
    // 0xb6d5cc: r0 = inline_Allocate_Double()
    //     0xb6d5cc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb6d5d0: add             x0, x0, #0x10
    //     0xb6d5d4: cmp             x1, x0
    //     0xb6d5d8: b.ls            #0xb6d860
    //     0xb6d5dc: str             x0, [THR, #0x50]  ; THR::top
    //     0xb6d5e0: sub             x0, x0, #0xf
    //     0xb6d5e4: movz            x1, #0xe15c
    //     0xb6d5e8: movk            x1, #0x3, lsl #16
    //     0xb6d5ec: stur            x1, [x0, #-1]
    // 0xb6d5f0: StoreField: r0->field_7 = d1
    //     0xb6d5f0: stur            d1, [x0, #7]
    // 0xb6d5f4: ldur            x1, [fp, #-0x20]
    // 0xb6d5f8: ArrayStore: r1[5] = r0  ; List_4
    //     0xb6d5f8: add             x25, x1, #0x23
    //     0xb6d5fc: str             w0, [x25]
    //     0xb6d600: tbz             w0, #0, #0xb6d61c
    //     0xb6d604: ldurb           w16, [x1, #-1]
    //     0xb6d608: ldurb           w17, [x0, #-1]
    //     0xb6d60c: and             x16, x17, x16, lsr #2
    //     0xb6d610: tst             x16, HEAP, lsr #32
    //     0xb6d614: b.eq            #0xb6d61c
    //     0xb6d618: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6d61c: r16 = <String, dynamic>
    //     0xb6d61c: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xb6d620: ldur            lr, [fp, #-0x20]
    // 0xb6d624: stp             lr, x16, [SP]
    // 0xb6d628: r0 = Map._fromLiteral()
    //     0xb6d628: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xb6d62c: mov             x1, x0
    // 0xb6d630: ldur            x0, [fp, #-0x18]
    // 0xb6d634: LoadField: r2 = r0->field_23
    //     0xb6d634: ldur            w2, [x0, #0x23]
    // 0xb6d638: DecompressPointer r2
    //     0xb6d638: add             x2, x2, HEAP, lsl #32
    // 0xb6d63c: ldur            d0, [fp, #-0x48]
    // 0xb6d640: r0 = inline_Allocate_Double()
    //     0xb6d640: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xb6d644: add             x0, x0, #0x10
    //     0xb6d648: cmp             x3, x0
    //     0xb6d64c: b.ls            #0xb6d878
    //     0xb6d650: str             x0, [THR, #0x50]  ; THR::top
    //     0xb6d654: sub             x0, x0, #0xf
    //     0xb6d658: movz            x3, #0xe15c
    //     0xb6d65c: movk            x3, #0x3, lsl #16
    //     0xb6d660: stur            x3, [x0, #-1]
    // 0xb6d664: StoreField: r0->field_7 = d0
    //     0xb6d664: stur            d0, [x0, #7]
    // 0xb6d668: ldur            x16, [fp, #-0x10]
    // 0xb6d66c: stp             x16, x2, [SP, #0x10]
    // 0xb6d670: stp             x1, x0, [SP]
    // 0xb6d674: mov             x0, x2
    // 0xb6d678: ClosureCall
    //     0xb6d678: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xb6d67c: ldur            x2, [x0, #0x1f]
    //     0xb6d680: blr             x2
    // 0xb6d684: ldur            x0, [fp, #-8]
    // 0xb6d688: LoadField: r2 = r0->field_f
    //     0xb6d688: ldur            w2, [x0, #0xf]
    // 0xb6d68c: DecompressPointer r2
    //     0xb6d68c: add             x2, x2, HEAP, lsl #32
    // 0xb6d690: LoadField: r3 = r2->field_b
    //     0xb6d690: ldur            w3, [x2, #0xb]
    // 0xb6d694: DecompressPointer r3
    //     0xb6d694: add             x3, x3, HEAP, lsl #32
    // 0xb6d698: cmp             w3, NULL
    // 0xb6d69c: b.eq            #0xb6d890
    // 0xb6d6a0: LoadField: r0 = r3->field_b
    //     0xb6d6a0: ldur            w0, [x3, #0xb]
    // 0xb6d6a4: DecompressPointer r0
    //     0xb6d6a4: add             x0, x0, HEAP, lsl #32
    // 0xb6d6a8: LoadField: r4 = r0->field_73
    //     0xb6d6a8: ldur            w4, [x0, #0x73]
    // 0xb6d6ac: DecompressPointer r4
    //     0xb6d6ac: add             x4, x4, HEAP, lsl #32
    // 0xb6d6b0: cmp             w4, NULL
    // 0xb6d6b4: b.ne            #0xb6d6c0
    // 0xb6d6b8: r0 = Null
    //     0xb6d6b8: mov             x0, NULL
    // 0xb6d6bc: b               #0xb6d6f8
    // 0xb6d6c0: ArrayLoad: r5 = r2[0]  ; List_8
    //     0xb6d6c0: ldur            x5, [x2, #0x17]
    // 0xb6d6c4: LoadField: r0 = r4->field_b
    //     0xb6d6c4: ldur            w0, [x4, #0xb]
    // 0xb6d6c8: r1 = LoadInt32Instr(r0)
    //     0xb6d6c8: sbfx            x1, x0, #1, #0x1f
    // 0xb6d6cc: mov             x0, x1
    // 0xb6d6d0: mov             x1, x5
    // 0xb6d6d4: cmp             x1, x0
    // 0xb6d6d8: b.hs            #0xb6d894
    // 0xb6d6dc: LoadField: r0 = r4->field_f
    //     0xb6d6dc: ldur            w0, [x4, #0xf]
    // 0xb6d6e0: DecompressPointer r0
    //     0xb6d6e0: add             x0, x0, HEAP, lsl #32
    // 0xb6d6e4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb6d6e4: add             x16, x0, x5, lsl #2
    //     0xb6d6e8: ldur            w1, [x16, #0xf]
    // 0xb6d6ec: DecompressPointer r1
    //     0xb6d6ec: add             x1, x1, HEAP, lsl #32
    // 0xb6d6f0: LoadField: r0 = r1->field_b
    //     0xb6d6f0: ldur            w0, [x1, #0xb]
    // 0xb6d6f4: DecompressPointer r0
    //     0xb6d6f4: add             x0, x0, HEAP, lsl #32
    // 0xb6d6f8: cmp             w0, NULL
    // 0xb6d6fc: b.ne            #0xb6d708
    // 0xb6d700: r5 = ""
    //     0xb6d700: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d704: b               #0xb6d70c
    // 0xb6d708: mov             x5, x0
    // 0xb6d70c: cmp             w4, NULL
    // 0xb6d710: b.ne            #0xb6d71c
    // 0xb6d714: r0 = Null
    //     0xb6d714: mov             x0, NULL
    // 0xb6d718: b               #0xb6d754
    // 0xb6d71c: ArrayLoad: r6 = r2[0]  ; List_8
    //     0xb6d71c: ldur            x6, [x2, #0x17]
    // 0xb6d720: LoadField: r0 = r4->field_b
    //     0xb6d720: ldur            w0, [x4, #0xb]
    // 0xb6d724: r1 = LoadInt32Instr(r0)
    //     0xb6d724: sbfx            x1, x0, #1, #0x1f
    // 0xb6d728: mov             x0, x1
    // 0xb6d72c: mov             x1, x6
    // 0xb6d730: cmp             x1, x0
    // 0xb6d734: b.hs            #0xb6d898
    // 0xb6d738: LoadField: r0 = r4->field_f
    //     0xb6d738: ldur            w0, [x4, #0xf]
    // 0xb6d73c: DecompressPointer r0
    //     0xb6d73c: add             x0, x0, HEAP, lsl #32
    // 0xb6d740: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6d740: add             x16, x0, x6, lsl #2
    //     0xb6d744: ldur            w1, [x16, #0xf]
    // 0xb6d748: DecompressPointer r1
    //     0xb6d748: add             x1, x1, HEAP, lsl #32
    // 0xb6d74c: LoadField: r0 = r1->field_7
    //     0xb6d74c: ldur            w0, [x1, #7]
    // 0xb6d750: DecompressPointer r0
    //     0xb6d750: add             x0, x0, HEAP, lsl #32
    // 0xb6d754: cmp             w0, NULL
    // 0xb6d758: b.ne            #0xb6d760
    // 0xb6d75c: r0 = ""
    //     0xb6d75c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d760: LoadField: r1 = r3->field_1b
    //     0xb6d760: ldur            w1, [x3, #0x1b]
    // 0xb6d764: DecompressPointer r1
    //     0xb6d764: add             x1, x1, HEAP, lsl #32
    // 0xb6d768: stp             x5, x1, [SP, #0x18]
    // 0xb6d76c: r16 = 2
    //     0xb6d76c: movz            x16, #0x2
    // 0xb6d770: stp             x16, x0, [SP, #8]
    // 0xb6d774: r16 = ""
    //     0xb6d774: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6d778: str             x16, [SP]
    // 0xb6d77c: mov             x0, x1
    // 0xb6d780: ClosureCall
    //     0xb6d780: add             x4, PP, #0x53, lsl #12  ; [pp+0x53c40] List(7) [0, 0x5, 0x5, 0x4, "customisationId", 0x4, Null]
    //     0xb6d784: ldr             x4, [x4, #0xc40]
    //     0xb6d788: ldur            x2, [x0, #0x1f]
    //     0xb6d78c: blr             x2
    // 0xb6d790: r0 = Null
    //     0xb6d790: mov             x0, NULL
    // 0xb6d794: LeaveFrame
    //     0xb6d794: mov             SP, fp
    //     0xb6d798: ldp             fp, lr, [SP], #0x10
    // 0xb6d79c: ret
    //     0xb6d79c: ret             
    // 0xb6d7a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6d7a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6d7a4: b               #0xb6c43c
    // 0xb6d7a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d7a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d7ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d7ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d7b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d7b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d7b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d7b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d7b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d7c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d7c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7d0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7d4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7dc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7e4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d7fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d7fc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d800: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d800: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d804: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d804: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d808: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d808: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d80c: stp             q0, q1, [SP, #-0x20]!
    // 0xb6d810: SaveReg r2
    //     0xb6d810: str             x2, [SP, #-8]!
    // 0xb6d814: r0 = AllocateDouble()
    //     0xb6d814: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6d818: RestoreReg r2
    //     0xb6d818: ldr             x2, [SP], #8
    // 0xb6d81c: ldp             q0, q1, [SP], #0x20
    // 0xb6d820: b               #0xb6d200
    // 0xb6d824: SaveReg d0
    //     0xb6d824: str             q0, [SP, #-0x10]!
    // 0xb6d828: stp             x0, x1, [SP, #-0x10]!
    // 0xb6d82c: r0 = AllocateDouble()
    //     0xb6d82c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6d830: mov             x2, x0
    // 0xb6d834: ldp             x0, x1, [SP], #0x10
    // 0xb6d838: RestoreReg d0
    //     0xb6d838: ldr             q0, [SP], #0x10
    // 0xb6d83c: b               #0xb6d270
    // 0xb6d840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d840: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d844: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d844: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d848: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d848: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d84c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d84c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d850: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d850: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d854: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d854: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d858: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d858: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d85c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d85c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d860: stp             q0, q1, [SP, #-0x20]!
    // 0xb6d864: SaveReg r2
    //     0xb6d864: str             x2, [SP, #-8]!
    // 0xb6d868: r0 = AllocateDouble()
    //     0xb6d868: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6d86c: RestoreReg r2
    //     0xb6d86c: ldr             x2, [SP], #8
    // 0xb6d870: ldp             q0, q1, [SP], #0x20
    // 0xb6d874: b               #0xb6d5f0
    // 0xb6d878: SaveReg d0
    //     0xb6d878: str             q0, [SP, #-0x10]!
    // 0xb6d87c: stp             x1, x2, [SP, #-0x10]!
    // 0xb6d880: r0 = AllocateDouble()
    //     0xb6d880: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb6d884: ldp             x1, x2, [SP], #0x10
    // 0xb6d888: RestoreReg d0
    //     0xb6d888: ldr             q0, [SP], #0x10
    // 0xb6d88c: b               #0xb6d664
    // 0xb6d890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6d890: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6d894: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d894: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6d898: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6d898: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb6d89c, size: 0x2d4
    // 0xb6d89c: EnterFrame
    //     0xb6d89c: stp             fp, lr, [SP, #-0x10]!
    //     0xb6d8a0: mov             fp, SP
    // 0xb6d8a4: AllocStack(0x40)
    //     0xb6d8a4: sub             SP, SP, #0x40
    // 0xb6d8a8: SetupParameters()
    //     0xb6d8a8: ldr             x0, [fp, #0x20]
    //     0xb6d8ac: ldur            w1, [x0, #0x17]
    //     0xb6d8b0: add             x1, x1, HEAP, lsl #32
    //     0xb6d8b4: stur            x1, [fp, #-8]
    // 0xb6d8b8: CheckStackOverflow
    //     0xb6d8b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6d8bc: cmp             SP, x16
    //     0xb6d8c0: b.ls            #0xb6db44
    // 0xb6d8c4: r1 = 2
    //     0xb6d8c4: movz            x1, #0x2
    // 0xb6d8c8: r0 = AllocateContext()
    //     0xb6d8c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6d8cc: mov             x3, x0
    // 0xb6d8d0: ldur            x2, [fp, #-8]
    // 0xb6d8d4: stur            x3, [fp, #-0x18]
    // 0xb6d8d8: StoreField: r3->field_b = r2
    //     0xb6d8d8: stur            w2, [x3, #0xb]
    // 0xb6d8dc: ldr             x4, [fp, #0x10]
    // 0xb6d8e0: StoreField: r3->field_f = r4
    //     0xb6d8e0: stur            w4, [x3, #0xf]
    // 0xb6d8e4: LoadField: r5 = r2->field_f
    //     0xb6d8e4: ldur            w5, [x2, #0xf]
    // 0xb6d8e8: DecompressPointer r5
    //     0xb6d8e8: add             x5, x5, HEAP, lsl #32
    // 0xb6d8ec: LoadField: r0 = r5->field_b
    //     0xb6d8ec: ldur            w0, [x5, #0xb]
    // 0xb6d8f0: DecompressPointer r0
    //     0xb6d8f0: add             x0, x0, HEAP, lsl #32
    // 0xb6d8f4: cmp             w0, NULL
    // 0xb6d8f8: b.eq            #0xb6db4c
    // 0xb6d8fc: LoadField: r1 = r0->field_b
    //     0xb6d8fc: ldur            w1, [x0, #0xb]
    // 0xb6d900: DecompressPointer r1
    //     0xb6d900: add             x1, x1, HEAP, lsl #32
    // 0xb6d904: LoadField: r6 = r1->field_73
    //     0xb6d904: ldur            w6, [x1, #0x73]
    // 0xb6d908: DecompressPointer r6
    //     0xb6d908: add             x6, x6, HEAP, lsl #32
    // 0xb6d90c: cmp             w6, NULL
    // 0xb6d910: b.ne            #0xb6d91c
    // 0xb6d914: r0 = Null
    //     0xb6d914: mov             x0, NULL
    // 0xb6d918: b               #0xb6d9a0
    // 0xb6d91c: LoadField: r7 = r5->field_1f
    //     0xb6d91c: ldur            x7, [x5, #0x1f]
    // 0xb6d920: LoadField: r0 = r6->field_b
    //     0xb6d920: ldur            w0, [x6, #0xb]
    // 0xb6d924: r1 = LoadInt32Instr(r0)
    //     0xb6d924: sbfx            x1, x0, #1, #0x1f
    // 0xb6d928: mov             x0, x1
    // 0xb6d92c: mov             x1, x7
    // 0xb6d930: cmp             x1, x0
    // 0xb6d934: b.hs            #0xb6db50
    // 0xb6d938: LoadField: r0 = r6->field_f
    //     0xb6d938: ldur            w0, [x6, #0xf]
    // 0xb6d93c: DecompressPointer r0
    //     0xb6d93c: add             x0, x0, HEAP, lsl #32
    // 0xb6d940: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6d940: add             x16, x0, x7, lsl #2
    //     0xb6d944: ldur            w1, [x16, #0xf]
    // 0xb6d948: DecompressPointer r1
    //     0xb6d948: add             x1, x1, HEAP, lsl #32
    // 0xb6d94c: ArrayLoad: r6 = r1[0]  ; List_4
    //     0xb6d94c: ldur            w6, [x1, #0x17]
    // 0xb6d950: DecompressPointer r6
    //     0xb6d950: add             x6, x6, HEAP, lsl #32
    // 0xb6d954: cmp             w6, NULL
    // 0xb6d958: b.ne            #0xb6d964
    // 0xb6d95c: r0 = Null
    //     0xb6d95c: mov             x0, NULL
    // 0xb6d960: b               #0xb6d9a0
    // 0xb6d964: LoadField: r0 = r6->field_b
    //     0xb6d964: ldur            w0, [x6, #0xb]
    // 0xb6d968: r7 = LoadInt32Instr(r4)
    //     0xb6d968: sbfx            x7, x4, #1, #0x1f
    //     0xb6d96c: tbz             w4, #0, #0xb6d974
    //     0xb6d970: ldur            x7, [x4, #7]
    // 0xb6d974: r1 = LoadInt32Instr(r0)
    //     0xb6d974: sbfx            x1, x0, #1, #0x1f
    // 0xb6d978: mov             x0, x1
    // 0xb6d97c: mov             x1, x7
    // 0xb6d980: cmp             x1, x0
    // 0xb6d984: b.hs            #0xb6db54
    // 0xb6d988: LoadField: r0 = r6->field_f
    //     0xb6d988: ldur            w0, [x6, #0xf]
    // 0xb6d98c: DecompressPointer r0
    //     0xb6d98c: add             x0, x0, HEAP, lsl #32
    // 0xb6d990: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6d990: add             x16, x0, x7, lsl #2
    //     0xb6d994: ldur            w1, [x16, #0xf]
    // 0xb6d998: DecompressPointer r1
    //     0xb6d998: add             x1, x1, HEAP, lsl #32
    // 0xb6d99c: mov             x0, x1
    // 0xb6d9a0: stur            x0, [fp, #-0x10]
    // 0xb6d9a4: StoreField: r3->field_13 = r0
    //     0xb6d9a4: stur            w0, [x3, #0x13]
    // 0xb6d9a8: LoadField: r1 = r5->field_13
    //     0xb6d9a8: ldur            w1, [x5, #0x13]
    // 0xb6d9ac: DecompressPointer r1
    //     0xb6d9ac: add             x1, x1, HEAP, lsl #32
    // 0xb6d9b0: r16 = Sentinel
    //     0xb6d9b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6d9b4: cmp             w1, w16
    // 0xb6d9b8: b.eq            #0xb6db58
    // 0xb6d9bc: cmp             w1, w0
    // 0xb6d9c0: b.ne            #0xb6d9d8
    // 0xb6d9c4: ldr             x1, [fp, #0x18]
    // 0xb6d9c8: r0 = of()
    //     0xb6d9c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6d9cc: LoadField: r1 = r0->field_5b
    //     0xb6d9cc: ldur            w1, [x0, #0x5b]
    // 0xb6d9d0: DecompressPointer r1
    //     0xb6d9d0: add             x1, x1, HEAP, lsl #32
    // 0xb6d9d4: b               #0xb6d9dc
    // 0xb6d9d8: r1 = Instance_Color
    //     0xb6d9d8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6d9dc: ldur            x0, [fp, #-0x10]
    // 0xb6d9e0: d0 = 0.000000
    //     0xb6d9e0: eor             v0.16b, v0.16b, v0.16b
    // 0xb6d9e4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb6d9e4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb6d9e8: r0 = styleFrom()
    //     0xb6d9e8: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xb6d9ec: mov             x2, x0
    // 0xb6d9f0: ldur            x0, [fp, #-0x10]
    // 0xb6d9f4: stur            x2, [fp, #-0x28]
    // 0xb6d9f8: cmp             w0, NULL
    // 0xb6d9fc: b.ne            #0xb6da08
    // 0xb6da00: r1 = Null
    //     0xb6da00: mov             x1, NULL
    // 0xb6da04: b               #0xb6da10
    // 0xb6da08: LoadField: r1 = r0->field_7
    //     0xb6da08: ldur            w1, [x0, #7]
    // 0xb6da0c: DecompressPointer r1
    //     0xb6da0c: add             x1, x1, HEAP, lsl #32
    // 0xb6da10: cmp             w1, NULL
    // 0xb6da14: b.ne            #0xb6da20
    // 0xb6da18: r4 = ""
    //     0xb6da18: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6da1c: b               #0xb6da24
    // 0xb6da20: mov             x4, x1
    // 0xb6da24: ldur            x3, [fp, #-8]
    // 0xb6da28: ldr             x1, [fp, #0x18]
    // 0xb6da2c: stur            x4, [fp, #-0x20]
    // 0xb6da30: r0 = of()
    //     0xb6da30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6da34: LoadField: r1 = r0->field_87
    //     0xb6da34: ldur            w1, [x0, #0x87]
    // 0xb6da38: DecompressPointer r1
    //     0xb6da38: add             x1, x1, HEAP, lsl #32
    // 0xb6da3c: LoadField: r0 = r1->field_2b
    //     0xb6da3c: ldur            w0, [x1, #0x2b]
    // 0xb6da40: DecompressPointer r0
    //     0xb6da40: add             x0, x0, HEAP, lsl #32
    // 0xb6da44: ldur            x1, [fp, #-8]
    // 0xb6da48: stur            x0, [fp, #-0x30]
    // 0xb6da4c: LoadField: r2 = r1->field_f
    //     0xb6da4c: ldur            w2, [x1, #0xf]
    // 0xb6da50: DecompressPointer r2
    //     0xb6da50: add             x2, x2, HEAP, lsl #32
    // 0xb6da54: LoadField: r1 = r2->field_13
    //     0xb6da54: ldur            w1, [x2, #0x13]
    // 0xb6da58: DecompressPointer r1
    //     0xb6da58: add             x1, x1, HEAP, lsl #32
    // 0xb6da5c: r16 = Sentinel
    //     0xb6da5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6da60: cmp             w1, w16
    // 0xb6da64: b.eq            #0xb6db64
    // 0xb6da68: ldur            x2, [fp, #-0x10]
    // 0xb6da6c: cmp             w1, w2
    // 0xb6da70: b.ne            #0xb6da7c
    // 0xb6da74: r1 = Instance_Color
    //     0xb6da74: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6da78: b               #0xb6da8c
    // 0xb6da7c: r1 = Instance_Color
    //     0xb6da7c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6da80: d0 = 0.400000
    //     0xb6da80: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb6da84: r0 = withOpacity()
    //     0xb6da84: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6da88: mov             x1, x0
    // 0xb6da8c: ldur            x0, [fp, #-0x28]
    // 0xb6da90: ldur            x2, [fp, #-0x20]
    // 0xb6da94: r16 = 16.000000
    //     0xb6da94: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6da98: ldr             x16, [x16, #0x188]
    // 0xb6da9c: stp             x1, x16, [SP]
    // 0xb6daa0: ldur            x1, [fp, #-0x30]
    // 0xb6daa4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6daa4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6daa8: ldr             x4, [x4, #0xaa0]
    // 0xb6daac: r0 = copyWith()
    //     0xb6daac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6dab0: stur            x0, [fp, #-8]
    // 0xb6dab4: r0 = Text()
    //     0xb6dab4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6dab8: mov             x3, x0
    // 0xb6dabc: ldur            x0, [fp, #-0x20]
    // 0xb6dac0: stur            x3, [fp, #-0x10]
    // 0xb6dac4: StoreField: r3->field_b = r0
    //     0xb6dac4: stur            w0, [x3, #0xb]
    // 0xb6dac8: ldur            x0, [fp, #-8]
    // 0xb6dacc: StoreField: r3->field_13 = r0
    //     0xb6dacc: stur            w0, [x3, #0x13]
    // 0xb6dad0: r0 = Instance_TextAlign
    //     0xb6dad0: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb6dad4: StoreField: r3->field_1b = r0
    //     0xb6dad4: stur            w0, [x3, #0x1b]
    // 0xb6dad8: ldur            x2, [fp, #-0x18]
    // 0xb6dadc: r1 = Function '<anonymous closure>':.
    //     0xb6dadc: add             x1, PP, #0x56, lsl #12  ; [pp+0x56230] AnonymousClosure: (0xb6db70), in [package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xb6b7f4)
    //     0xb6dae0: ldr             x1, [x1, #0x230]
    // 0xb6dae4: r0 = AllocateClosure()
    //     0xb6dae4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6dae8: stur            x0, [fp, #-8]
    // 0xb6daec: r0 = ElevatedButton()
    //     0xb6daec: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xb6daf0: mov             x1, x0
    // 0xb6daf4: ldur            x0, [fp, #-8]
    // 0xb6daf8: stur            x1, [fp, #-0x18]
    // 0xb6dafc: StoreField: r1->field_b = r0
    //     0xb6dafc: stur            w0, [x1, #0xb]
    // 0xb6db00: ldur            x0, [fp, #-0x28]
    // 0xb6db04: StoreField: r1->field_1b = r0
    //     0xb6db04: stur            w0, [x1, #0x1b]
    // 0xb6db08: r0 = false
    //     0xb6db08: add             x0, NULL, #0x30  ; false
    // 0xb6db0c: StoreField: r1->field_27 = r0
    //     0xb6db0c: stur            w0, [x1, #0x27]
    // 0xb6db10: r0 = true
    //     0xb6db10: add             x0, NULL, #0x20  ; true
    // 0xb6db14: StoreField: r1->field_2f = r0
    //     0xb6db14: stur            w0, [x1, #0x2f]
    // 0xb6db18: ldur            x0, [fp, #-0x10]
    // 0xb6db1c: StoreField: r1->field_37 = r0
    //     0xb6db1c: stur            w0, [x1, #0x37]
    // 0xb6db20: r0 = Padding()
    //     0xb6db20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6db24: r1 = Instance_EdgeInsets
    //     0xb6db24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb6db28: ldr             x1, [x1, #0x980]
    // 0xb6db2c: StoreField: r0->field_f = r1
    //     0xb6db2c: stur            w1, [x0, #0xf]
    // 0xb6db30: ldur            x1, [fp, #-0x18]
    // 0xb6db34: StoreField: r0->field_b = r1
    //     0xb6db34: stur            w1, [x0, #0xb]
    // 0xb6db38: LeaveFrame
    //     0xb6db38: mov             SP, fp
    //     0xb6db3c: ldp             fp, lr, [SP], #0x10
    // 0xb6db40: ret
    //     0xb6db40: ret             
    // 0xb6db44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6db44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6db48: b               #0xb6d8c4
    // 0xb6db4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6db4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6db50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6db50: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6db54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6db54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6db58: r9 = dropDownValue
    //     0xb6db58: add             x9, PP, #0x56, lsl #12  ; [pp+0x56238] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb6db5c: ldr             x9, [x9, #0x238]
    // 0xb6db60: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb6db60: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb6db64: r9 = dropDownValue
    //     0xb6db64: add             x9, PP, #0x56, lsl #12  ; [pp+0x56238] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb6db68: ldr             x9, [x9, #0x238]
    // 0xb6db6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb6db6c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6db70, size: 0x110
    // 0xb6db70: EnterFrame
    //     0xb6db70: stp             fp, lr, [SP, #-0x10]!
    //     0xb6db74: mov             fp, SP
    // 0xb6db78: AllocStack(0x40)
    //     0xb6db78: sub             SP, SP, #0x40
    // 0xb6db7c: SetupParameters()
    //     0xb6db7c: ldr             x0, [fp, #0x10]
    //     0xb6db80: ldur            w2, [x0, #0x17]
    //     0xb6db84: add             x2, x2, HEAP, lsl #32
    //     0xb6db88: stur            x2, [fp, #-0x28]
    // 0xb6db8c: CheckStackOverflow
    //     0xb6db8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6db90: cmp             SP, x16
    //     0xb6db94: b.ls            #0xb6dc74
    // 0xb6db98: LoadField: r0 = r2->field_b
    //     0xb6db98: ldur            w0, [x2, #0xb]
    // 0xb6db9c: DecompressPointer r0
    //     0xb6db9c: add             x0, x0, HEAP, lsl #32
    // 0xb6dba0: stur            x0, [fp, #-0x20]
    // 0xb6dba4: LoadField: r1 = r0->field_f
    //     0xb6dba4: ldur            w1, [x0, #0xf]
    // 0xb6dba8: DecompressPointer r1
    //     0xb6dba8: add             x1, x1, HEAP, lsl #32
    // 0xb6dbac: LoadField: r3 = r1->field_b
    //     0xb6dbac: ldur            w3, [x1, #0xb]
    // 0xb6dbb0: DecompressPointer r3
    //     0xb6dbb0: add             x3, x3, HEAP, lsl #32
    // 0xb6dbb4: stur            x3, [fp, #-0x18]
    // 0xb6dbb8: cmp             w3, NULL
    // 0xb6dbbc: b.eq            #0xb6dc7c
    // 0xb6dbc0: LoadField: r4 = r1->field_2b
    //     0xb6dbc0: ldur            w4, [x1, #0x2b]
    // 0xb6dbc4: DecompressPointer r4
    //     0xb6dbc4: add             x4, x4, HEAP, lsl #32
    // 0xb6dbc8: stur            x4, [fp, #-0x10]
    // 0xb6dbcc: LoadField: r1 = r3->field_f
    //     0xb6dbcc: ldur            w1, [x3, #0xf]
    // 0xb6dbd0: DecompressPointer r1
    //     0xb6dbd0: add             x1, x1, HEAP, lsl #32
    // 0xb6dbd4: stur            x1, [fp, #-8]
    // 0xb6dbd8: r0 = EventData()
    //     0xb6dbd8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb6dbdc: mov             x1, x0
    // 0xb6dbe0: ldur            x0, [fp, #-8]
    // 0xb6dbe4: stur            x1, [fp, #-0x30]
    // 0xb6dbe8: StoreField: r1->field_13 = r0
    //     0xb6dbe8: stur            w0, [x1, #0x13]
    // 0xb6dbec: ldur            x0, [fp, #-0x10]
    // 0xb6dbf0: StoreField: r1->field_3b = r0
    //     0xb6dbf0: stur            w0, [x1, #0x3b]
    // 0xb6dbf4: r0 = EventsRequest()
    //     0xb6dbf4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb6dbf8: mov             x1, x0
    // 0xb6dbfc: r0 = "size_variation_clicked"
    //     0xb6dbfc: add             x0, PP, #0x32, lsl #12  ; [pp+0x32a28] "size_variation_clicked"
    //     0xb6dc00: ldr             x0, [x0, #0xa28]
    // 0xb6dc04: StoreField: r1->field_7 = r0
    //     0xb6dc04: stur            w0, [x1, #7]
    // 0xb6dc08: ldur            x0, [fp, #-0x30]
    // 0xb6dc0c: StoreField: r1->field_b = r0
    //     0xb6dc0c: stur            w0, [x1, #0xb]
    // 0xb6dc10: ldur            x0, [fp, #-0x18]
    // 0xb6dc14: LoadField: r2 = r0->field_13
    //     0xb6dc14: ldur            w2, [x0, #0x13]
    // 0xb6dc18: DecompressPointer r2
    //     0xb6dc18: add             x2, x2, HEAP, lsl #32
    // 0xb6dc1c: stp             x1, x2, [SP]
    // 0xb6dc20: r4 = 0
    //     0xb6dc20: movz            x4, #0
    // 0xb6dc24: ldr             x0, [SP, #8]
    // 0xb6dc28: r16 = UnlinkedCall_0x613b5c
    //     0xb6dc28: add             x16, PP, #0x56, lsl #12  ; [pp+0x56240] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb6dc2c: add             x16, x16, #0x240
    // 0xb6dc30: ldp             x5, lr, [x16]
    // 0xb6dc34: blr             lr
    // 0xb6dc38: ldur            x0, [fp, #-0x20]
    // 0xb6dc3c: LoadField: r3 = r0->field_f
    //     0xb6dc3c: ldur            w3, [x0, #0xf]
    // 0xb6dc40: DecompressPointer r3
    //     0xb6dc40: add             x3, x3, HEAP, lsl #32
    // 0xb6dc44: ldur            x2, [fp, #-0x28]
    // 0xb6dc48: stur            x3, [fp, #-8]
    // 0xb6dc4c: r1 = Function '<anonymous closure>':.
    //     0xb6dc4c: add             x1, PP, #0x56, lsl #12  ; [pp+0x56250] AnonymousClosure: (0xa60844), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb6dc50: ldr             x1, [x1, #0x250]
    // 0xb6dc54: r0 = AllocateClosure()
    //     0xb6dc54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6dc58: ldur            x1, [fp, #-8]
    // 0xb6dc5c: mov             x2, x0
    // 0xb6dc60: r0 = setState()
    //     0xb6dc60: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb6dc64: r0 = Null
    //     0xb6dc64: mov             x0, NULL
    // 0xb6dc68: LeaveFrame
    //     0xb6dc68: mov             SP, fp
    //     0xb6dc6c: ldp             fp, lr, [SP], #0x10
    // 0xb6dc70: ret
    //     0xb6dc70: ret             
    // 0xb6dc74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6dc74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6dc78: b               #0xb6db98
    // 0xb6dc7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6dc7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb6dc80, size: 0x284
    // 0xb6dc80: EnterFrame
    //     0xb6dc80: stp             fp, lr, [SP, #-0x10]!
    //     0xb6dc84: mov             fp, SP
    // 0xb6dc88: AllocStack(0x40)
    //     0xb6dc88: sub             SP, SP, #0x40
    // 0xb6dc8c: SetupParameters()
    //     0xb6dc8c: ldr             x0, [fp, #0x20]
    //     0xb6dc90: ldur            w1, [x0, #0x17]
    //     0xb6dc94: add             x1, x1, HEAP, lsl #32
    //     0xb6dc98: stur            x1, [fp, #-8]
    // 0xb6dc9c: CheckStackOverflow
    //     0xb6dc9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6dca0: cmp             SP, x16
    //     0xb6dca4: b.ls            #0xb6dedc
    // 0xb6dca8: r1 = 2
    //     0xb6dca8: movz            x1, #0x2
    // 0xb6dcac: r0 = AllocateContext()
    //     0xb6dcac: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6dcb0: mov             x3, x0
    // 0xb6dcb4: ldur            x2, [fp, #-8]
    // 0xb6dcb8: stur            x3, [fp, #-0x18]
    // 0xb6dcbc: StoreField: r3->field_b = r2
    //     0xb6dcbc: stur            w2, [x3, #0xb]
    // 0xb6dcc0: ldr             x0, [fp, #0x10]
    // 0xb6dcc4: StoreField: r3->field_f = r0
    //     0xb6dcc4: stur            w0, [x3, #0xf]
    // 0xb6dcc8: LoadField: r4 = r2->field_f
    //     0xb6dcc8: ldur            w4, [x2, #0xf]
    // 0xb6dccc: DecompressPointer r4
    //     0xb6dccc: add             x4, x4, HEAP, lsl #32
    // 0xb6dcd0: LoadField: r1 = r4->field_b
    //     0xb6dcd0: ldur            w1, [x4, #0xb]
    // 0xb6dcd4: DecompressPointer r1
    //     0xb6dcd4: add             x1, x1, HEAP, lsl #32
    // 0xb6dcd8: cmp             w1, NULL
    // 0xb6dcdc: b.eq            #0xb6dee4
    // 0xb6dce0: LoadField: r5 = r1->field_b
    //     0xb6dce0: ldur            w5, [x1, #0xb]
    // 0xb6dce4: DecompressPointer r5
    //     0xb6dce4: add             x5, x5, HEAP, lsl #32
    // 0xb6dce8: LoadField: r6 = r5->field_6f
    //     0xb6dce8: ldur            w6, [x5, #0x6f]
    // 0xb6dcec: DecompressPointer r6
    //     0xb6dcec: add             x6, x6, HEAP, lsl #32
    // 0xb6dcf0: cmp             w6, NULL
    // 0xb6dcf4: b.ne            #0xb6dd00
    // 0xb6dcf8: r0 = Null
    //     0xb6dcf8: mov             x0, NULL
    // 0xb6dcfc: b               #0xb6dd38
    // 0xb6dd00: LoadField: r1 = r6->field_b
    //     0xb6dd00: ldur            w1, [x6, #0xb]
    // 0xb6dd04: r5 = LoadInt32Instr(r0)
    //     0xb6dd04: sbfx            x5, x0, #1, #0x1f
    //     0xb6dd08: tbz             w0, #0, #0xb6dd10
    //     0xb6dd0c: ldur            x5, [x0, #7]
    // 0xb6dd10: r0 = LoadInt32Instr(r1)
    //     0xb6dd10: sbfx            x0, x1, #1, #0x1f
    // 0xb6dd14: mov             x1, x5
    // 0xb6dd18: cmp             x1, x0
    // 0xb6dd1c: b.hs            #0xb6dee8
    // 0xb6dd20: LoadField: r0 = r6->field_f
    //     0xb6dd20: ldur            w0, [x6, #0xf]
    // 0xb6dd24: DecompressPointer r0
    //     0xb6dd24: add             x0, x0, HEAP, lsl #32
    // 0xb6dd28: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb6dd28: add             x16, x0, x5, lsl #2
    //     0xb6dd2c: ldur            w1, [x16, #0xf]
    // 0xb6dd30: DecompressPointer r1
    //     0xb6dd30: add             x1, x1, HEAP, lsl #32
    // 0xb6dd34: mov             x0, x1
    // 0xb6dd38: stur            x0, [fp, #-0x10]
    // 0xb6dd3c: StoreField: r3->field_13 = r0
    //     0xb6dd3c: stur            w0, [x3, #0x13]
    // 0xb6dd40: LoadField: r1 = r4->field_13
    //     0xb6dd40: ldur            w1, [x4, #0x13]
    // 0xb6dd44: DecompressPointer r1
    //     0xb6dd44: add             x1, x1, HEAP, lsl #32
    // 0xb6dd48: r16 = Sentinel
    //     0xb6dd48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6dd4c: cmp             w1, w16
    // 0xb6dd50: b.eq            #0xb6deec
    // 0xb6dd54: cmp             w1, w0
    // 0xb6dd58: b.ne            #0xb6dd70
    // 0xb6dd5c: ldr             x1, [fp, #0x18]
    // 0xb6dd60: r0 = of()
    //     0xb6dd60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6dd64: LoadField: r1 = r0->field_5b
    //     0xb6dd64: ldur            w1, [x0, #0x5b]
    // 0xb6dd68: DecompressPointer r1
    //     0xb6dd68: add             x1, x1, HEAP, lsl #32
    // 0xb6dd6c: b               #0xb6dd74
    // 0xb6dd70: r1 = Instance_Color
    //     0xb6dd70: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6dd74: ldur            x0, [fp, #-0x10]
    // 0xb6dd78: d0 = 0.000000
    //     0xb6dd78: eor             v0.16b, v0.16b, v0.16b
    // 0xb6dd7c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb6dd7c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb6dd80: r0 = styleFrom()
    //     0xb6dd80: bl              #0xa5f880  ; [package:flutter/src/material/elevated_button.dart] ElevatedButton::styleFrom
    // 0xb6dd84: mov             x2, x0
    // 0xb6dd88: ldur            x0, [fp, #-0x10]
    // 0xb6dd8c: stur            x2, [fp, #-0x28]
    // 0xb6dd90: cmp             w0, NULL
    // 0xb6dd94: b.ne            #0xb6dda0
    // 0xb6dd98: r1 = Null
    //     0xb6dd98: mov             x1, NULL
    // 0xb6dd9c: b               #0xb6dda8
    // 0xb6dda0: LoadField: r1 = r0->field_7
    //     0xb6dda0: ldur            w1, [x0, #7]
    // 0xb6dda4: DecompressPointer r1
    //     0xb6dda4: add             x1, x1, HEAP, lsl #32
    // 0xb6dda8: cmp             w1, NULL
    // 0xb6ddac: b.ne            #0xb6ddb8
    // 0xb6ddb0: r4 = ""
    //     0xb6ddb0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6ddb4: b               #0xb6ddbc
    // 0xb6ddb8: mov             x4, x1
    // 0xb6ddbc: ldur            x3, [fp, #-8]
    // 0xb6ddc0: ldr             x1, [fp, #0x18]
    // 0xb6ddc4: stur            x4, [fp, #-0x20]
    // 0xb6ddc8: r0 = of()
    //     0xb6ddc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6ddcc: LoadField: r1 = r0->field_87
    //     0xb6ddcc: ldur            w1, [x0, #0x87]
    // 0xb6ddd0: DecompressPointer r1
    //     0xb6ddd0: add             x1, x1, HEAP, lsl #32
    // 0xb6ddd4: LoadField: r0 = r1->field_2b
    //     0xb6ddd4: ldur            w0, [x1, #0x2b]
    // 0xb6ddd8: DecompressPointer r0
    //     0xb6ddd8: add             x0, x0, HEAP, lsl #32
    // 0xb6dddc: ldur            x1, [fp, #-8]
    // 0xb6dde0: stur            x0, [fp, #-0x30]
    // 0xb6dde4: LoadField: r2 = r1->field_f
    //     0xb6dde4: ldur            w2, [x1, #0xf]
    // 0xb6dde8: DecompressPointer r2
    //     0xb6dde8: add             x2, x2, HEAP, lsl #32
    // 0xb6ddec: LoadField: r1 = r2->field_13
    //     0xb6ddec: ldur            w1, [x2, #0x13]
    // 0xb6ddf0: DecompressPointer r1
    //     0xb6ddf0: add             x1, x1, HEAP, lsl #32
    // 0xb6ddf4: r16 = Sentinel
    //     0xb6ddf4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb6ddf8: cmp             w1, w16
    // 0xb6ddfc: b.eq            #0xb6def8
    // 0xb6de00: ldur            x2, [fp, #-0x10]
    // 0xb6de04: cmp             w1, w2
    // 0xb6de08: b.ne            #0xb6de14
    // 0xb6de0c: r1 = Instance_Color
    //     0xb6de0c: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb6de10: b               #0xb6de24
    // 0xb6de14: r1 = Instance_Color
    //     0xb6de14: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6de18: d0 = 0.400000
    //     0xb6de18: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb6de1c: r0 = withOpacity()
    //     0xb6de1c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6de20: mov             x1, x0
    // 0xb6de24: ldur            x0, [fp, #-0x28]
    // 0xb6de28: ldur            x2, [fp, #-0x20]
    // 0xb6de2c: r16 = 16.000000
    //     0xb6de2c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6de30: ldr             x16, [x16, #0x188]
    // 0xb6de34: stp             x1, x16, [SP]
    // 0xb6de38: ldur            x1, [fp, #-0x30]
    // 0xb6de3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6de3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6de40: ldr             x4, [x4, #0xaa0]
    // 0xb6de44: r0 = copyWith()
    //     0xb6de44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6de48: stur            x0, [fp, #-8]
    // 0xb6de4c: r0 = Text()
    //     0xb6de4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6de50: mov             x3, x0
    // 0xb6de54: ldur            x0, [fp, #-0x20]
    // 0xb6de58: stur            x3, [fp, #-0x10]
    // 0xb6de5c: StoreField: r3->field_b = r0
    //     0xb6de5c: stur            w0, [x3, #0xb]
    // 0xb6de60: ldur            x0, [fp, #-8]
    // 0xb6de64: StoreField: r3->field_13 = r0
    //     0xb6de64: stur            w0, [x3, #0x13]
    // 0xb6de68: r0 = Instance_TextAlign
    //     0xb6de68: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb6de6c: StoreField: r3->field_1b = r0
    //     0xb6de6c: stur            w0, [x3, #0x1b]
    // 0xb6de70: ldur            x2, [fp, #-0x18]
    // 0xb6de74: r1 = Function '<anonymous closure>':.
    //     0xb6de74: add             x1, PP, #0x56, lsl #12  ; [pp+0x56258] AnonymousClosure: (0xb6df04), in [package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xb6b7f4)
    //     0xb6de78: ldr             x1, [x1, #0x258]
    // 0xb6de7c: r0 = AllocateClosure()
    //     0xb6de7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6de80: stur            x0, [fp, #-8]
    // 0xb6de84: r0 = ElevatedButton()
    //     0xb6de84: bl              #0xa5f874  ; AllocateElevatedButtonStub -> ElevatedButton (size=0x3c)
    // 0xb6de88: mov             x1, x0
    // 0xb6de8c: ldur            x0, [fp, #-8]
    // 0xb6de90: stur            x1, [fp, #-0x18]
    // 0xb6de94: StoreField: r1->field_b = r0
    //     0xb6de94: stur            w0, [x1, #0xb]
    // 0xb6de98: ldur            x0, [fp, #-0x28]
    // 0xb6de9c: StoreField: r1->field_1b = r0
    //     0xb6de9c: stur            w0, [x1, #0x1b]
    // 0xb6dea0: r0 = false
    //     0xb6dea0: add             x0, NULL, #0x30  ; false
    // 0xb6dea4: StoreField: r1->field_27 = r0
    //     0xb6dea4: stur            w0, [x1, #0x27]
    // 0xb6dea8: r0 = true
    //     0xb6dea8: add             x0, NULL, #0x20  ; true
    // 0xb6deac: StoreField: r1->field_2f = r0
    //     0xb6deac: stur            w0, [x1, #0x2f]
    // 0xb6deb0: ldur            x0, [fp, #-0x10]
    // 0xb6deb4: StoreField: r1->field_37 = r0
    //     0xb6deb4: stur            w0, [x1, #0x37]
    // 0xb6deb8: r0 = Padding()
    //     0xb6deb8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6debc: r1 = Instance_EdgeInsets
    //     0xb6debc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb6dec0: ldr             x1, [x1, #0x980]
    // 0xb6dec4: StoreField: r0->field_f = r1
    //     0xb6dec4: stur            w1, [x0, #0xf]
    // 0xb6dec8: ldur            x1, [fp, #-0x18]
    // 0xb6decc: StoreField: r0->field_b = r1
    //     0xb6decc: stur            w1, [x0, #0xb]
    // 0xb6ded0: LeaveFrame
    //     0xb6ded0: mov             SP, fp
    //     0xb6ded4: ldp             fp, lr, [SP], #0x10
    // 0xb6ded8: ret
    //     0xb6ded8: ret             
    // 0xb6dedc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6dedc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6dee0: b               #0xb6dca8
    // 0xb6dee4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6dee4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6dee8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6dee8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6deec: r9 = dropDownValue
    //     0xb6deec: add             x9, PP, #0x56, lsl #12  ; [pp+0x56238] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb6def0: ldr             x9, [x9, #0x238]
    // 0xb6def4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb6def4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb6def8: r9 = dropDownValue
    //     0xb6def8: add             x9, PP, #0x56, lsl #12  ; [pp+0x56238] Field <<EMAIL>>: late (offset: 0x14)
    //     0xb6defc: ldr             x9, [x9, #0x238]
    // 0xb6df00: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb6df00: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6df04, size: 0x110
    // 0xb6df04: EnterFrame
    //     0xb6df04: stp             fp, lr, [SP, #-0x10]!
    //     0xb6df08: mov             fp, SP
    // 0xb6df0c: AllocStack(0x40)
    //     0xb6df0c: sub             SP, SP, #0x40
    // 0xb6df10: SetupParameters()
    //     0xb6df10: ldr             x0, [fp, #0x10]
    //     0xb6df14: ldur            w2, [x0, #0x17]
    //     0xb6df18: add             x2, x2, HEAP, lsl #32
    //     0xb6df1c: stur            x2, [fp, #-0x28]
    // 0xb6df20: CheckStackOverflow
    //     0xb6df20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6df24: cmp             SP, x16
    //     0xb6df28: b.ls            #0xb6e008
    // 0xb6df2c: LoadField: r0 = r2->field_b
    //     0xb6df2c: ldur            w0, [x2, #0xb]
    // 0xb6df30: DecompressPointer r0
    //     0xb6df30: add             x0, x0, HEAP, lsl #32
    // 0xb6df34: stur            x0, [fp, #-0x20]
    // 0xb6df38: LoadField: r1 = r0->field_f
    //     0xb6df38: ldur            w1, [x0, #0xf]
    // 0xb6df3c: DecompressPointer r1
    //     0xb6df3c: add             x1, x1, HEAP, lsl #32
    // 0xb6df40: LoadField: r3 = r1->field_b
    //     0xb6df40: ldur            w3, [x1, #0xb]
    // 0xb6df44: DecompressPointer r3
    //     0xb6df44: add             x3, x3, HEAP, lsl #32
    // 0xb6df48: stur            x3, [fp, #-0x18]
    // 0xb6df4c: cmp             w3, NULL
    // 0xb6df50: b.eq            #0xb6e010
    // 0xb6df54: LoadField: r4 = r1->field_2b
    //     0xb6df54: ldur            w4, [x1, #0x2b]
    // 0xb6df58: DecompressPointer r4
    //     0xb6df58: add             x4, x4, HEAP, lsl #32
    // 0xb6df5c: stur            x4, [fp, #-0x10]
    // 0xb6df60: LoadField: r1 = r3->field_f
    //     0xb6df60: ldur            w1, [x3, #0xf]
    // 0xb6df64: DecompressPointer r1
    //     0xb6df64: add             x1, x1, HEAP, lsl #32
    // 0xb6df68: stur            x1, [fp, #-8]
    // 0xb6df6c: r0 = EventData()
    //     0xb6df6c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xb6df70: mov             x1, x0
    // 0xb6df74: ldur            x0, [fp, #-8]
    // 0xb6df78: stur            x1, [fp, #-0x30]
    // 0xb6df7c: StoreField: r1->field_13 = r0
    //     0xb6df7c: stur            w0, [x1, #0x13]
    // 0xb6df80: ldur            x0, [fp, #-0x10]
    // 0xb6df84: StoreField: r1->field_3b = r0
    //     0xb6df84: stur            w0, [x1, #0x3b]
    // 0xb6df88: r0 = EventsRequest()
    //     0xb6df88: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xb6df8c: mov             x1, x0
    // 0xb6df90: r0 = "size_variation_clicked"
    //     0xb6df90: add             x0, PP, #0x32, lsl #12  ; [pp+0x32a28] "size_variation_clicked"
    //     0xb6df94: ldr             x0, [x0, #0xa28]
    // 0xb6df98: StoreField: r1->field_7 = r0
    //     0xb6df98: stur            w0, [x1, #7]
    // 0xb6df9c: ldur            x0, [fp, #-0x30]
    // 0xb6dfa0: StoreField: r1->field_b = r0
    //     0xb6dfa0: stur            w0, [x1, #0xb]
    // 0xb6dfa4: ldur            x0, [fp, #-0x18]
    // 0xb6dfa8: LoadField: r2 = r0->field_13
    //     0xb6dfa8: ldur            w2, [x0, #0x13]
    // 0xb6dfac: DecompressPointer r2
    //     0xb6dfac: add             x2, x2, HEAP, lsl #32
    // 0xb6dfb0: stp             x1, x2, [SP]
    // 0xb6dfb4: r4 = 0
    //     0xb6dfb4: movz            x4, #0
    // 0xb6dfb8: ldr             x0, [SP, #8]
    // 0xb6dfbc: r16 = UnlinkedCall_0x613b5c
    //     0xb6dfbc: add             x16, PP, #0x56, lsl #12  ; [pp+0x56260] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb6dfc0: add             x16, x16, #0x260
    // 0xb6dfc4: ldp             x5, lr, [x16]
    // 0xb6dfc8: blr             lr
    // 0xb6dfcc: ldur            x0, [fp, #-0x20]
    // 0xb6dfd0: LoadField: r3 = r0->field_f
    //     0xb6dfd0: ldur            w3, [x0, #0xf]
    // 0xb6dfd4: DecompressPointer r3
    //     0xb6dfd4: add             x3, x3, HEAP, lsl #32
    // 0xb6dfd8: ldur            x2, [fp, #-0x28]
    // 0xb6dfdc: stur            x3, [fp, #-8]
    // 0xb6dfe0: r1 = Function '<anonymous closure>':.
    //     0xb6dfe0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56270] AnonymousClosure: (0xa610b8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xb86d10)
    //     0xb6dfe4: ldr             x1, [x1, #0x270]
    // 0xb6dfe8: r0 = AllocateClosure()
    //     0xb6dfe8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6dfec: ldur            x1, [fp, #-8]
    // 0xb6dff0: mov             x2, x0
    // 0xb6dff4: r0 = setState()
    //     0xb6dff4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb6dff8: r0 = Null
    //     0xb6dff8: mov             x0, NULL
    // 0xb6dffc: LeaveFrame
    //     0xb6dffc: mov             SP, fp
    //     0xb6e000: ldp             fp, lr, [SP], #0x10
    // 0xb6e004: ret
    //     0xb6e004: ret             
    // 0xb6e008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6e008: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6e00c: b               #0xb6df2c
    // 0xb6e010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6e010: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb6e014, size: 0x7a8
    // 0xb6e014: EnterFrame
    //     0xb6e014: stp             fp, lr, [SP, #-0x10]!
    //     0xb6e018: mov             fp, SP
    // 0xb6e01c: AllocStack(0x60)
    //     0xb6e01c: sub             SP, SP, #0x60
    // 0xb6e020: SetupParameters()
    //     0xb6e020: ldr             x0, [fp, #0x20]
    //     0xb6e024: ldur            w1, [x0, #0x17]
    //     0xb6e028: add             x1, x1, HEAP, lsl #32
    //     0xb6e02c: stur            x1, [fp, #-8]
    // 0xb6e030: CheckStackOverflow
    //     0xb6e030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6e034: cmp             SP, x16
    //     0xb6e038: b.ls            #0xb6e794
    // 0xb6e03c: r1 = 1
    //     0xb6e03c: movz            x1, #0x1
    // 0xb6e040: r0 = AllocateContext()
    //     0xb6e040: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6e044: mov             x4, x0
    // 0xb6e048: ldur            x3, [fp, #-8]
    // 0xb6e04c: stur            x4, [fp, #-0x10]
    // 0xb6e050: StoreField: r4->field_b = r3
    //     0xb6e050: stur            w3, [x4, #0xb]
    // 0xb6e054: ldr             x5, [fp, #0x10]
    // 0xb6e058: StoreField: r4->field_f = r5
    //     0xb6e058: stur            w5, [x4, #0xf]
    // 0xb6e05c: LoadField: r0 = r3->field_f
    //     0xb6e05c: ldur            w0, [x3, #0xf]
    // 0xb6e060: DecompressPointer r0
    //     0xb6e060: add             x0, x0, HEAP, lsl #32
    // 0xb6e064: LoadField: r2 = r0->field_27
    //     0xb6e064: ldur            w2, [x0, #0x27]
    // 0xb6e068: DecompressPointer r2
    //     0xb6e068: add             x2, x2, HEAP, lsl #32
    // 0xb6e06c: LoadField: r1 = r0->field_b
    //     0xb6e06c: ldur            w1, [x0, #0xb]
    // 0xb6e070: DecompressPointer r1
    //     0xb6e070: add             x1, x1, HEAP, lsl #32
    // 0xb6e074: cmp             w1, NULL
    // 0xb6e078: b.eq            #0xb6e79c
    // 0xb6e07c: LoadField: r0 = r1->field_b
    //     0xb6e07c: ldur            w0, [x1, #0xb]
    // 0xb6e080: DecompressPointer r0
    //     0xb6e080: add             x0, x0, HEAP, lsl #32
    // 0xb6e084: LoadField: r6 = r0->field_73
    //     0xb6e084: ldur            w6, [x0, #0x73]
    // 0xb6e088: DecompressPointer r6
    //     0xb6e088: add             x6, x6, HEAP, lsl #32
    // 0xb6e08c: cmp             w6, NULL
    // 0xb6e090: b.ne            #0xb6e09c
    // 0xb6e094: r0 = Null
    //     0xb6e094: mov             x0, NULL
    // 0xb6e098: b               #0xb6e0dc
    // 0xb6e09c: LoadField: r0 = r6->field_b
    //     0xb6e09c: ldur            w0, [x6, #0xb]
    // 0xb6e0a0: r7 = LoadInt32Instr(r5)
    //     0xb6e0a0: sbfx            x7, x5, #1, #0x1f
    //     0xb6e0a4: tbz             w5, #0, #0xb6e0ac
    //     0xb6e0a8: ldur            x7, [x5, #7]
    // 0xb6e0ac: r1 = LoadInt32Instr(r0)
    //     0xb6e0ac: sbfx            x1, x0, #1, #0x1f
    // 0xb6e0b0: mov             x0, x1
    // 0xb6e0b4: mov             x1, x7
    // 0xb6e0b8: cmp             x1, x0
    // 0xb6e0bc: b.hs            #0xb6e7a0
    // 0xb6e0c0: LoadField: r0 = r6->field_f
    //     0xb6e0c0: ldur            w0, [x6, #0xf]
    // 0xb6e0c4: DecompressPointer r0
    //     0xb6e0c4: add             x0, x0, HEAP, lsl #32
    // 0xb6e0c8: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb6e0c8: add             x16, x0, x7, lsl #2
    //     0xb6e0cc: ldur            w1, [x16, #0xf]
    // 0xb6e0d0: DecompressPointer r1
    //     0xb6e0d0: add             x1, x1, HEAP, lsl #32
    // 0xb6e0d4: LoadField: r0 = r1->field_7
    //     0xb6e0d4: ldur            w0, [x1, #7]
    // 0xb6e0d8: DecompressPointer r0
    //     0xb6e0d8: add             x0, x0, HEAP, lsl #32
    // 0xb6e0dc: cmp             w0, NULL
    // 0xb6e0e0: b.ne            #0xb6e0e8
    // 0xb6e0e4: r0 = ""
    //     0xb6e0e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6e0e8: mov             x1, x2
    // 0xb6e0ec: mov             x2, x0
    // 0xb6e0f0: r0 = contains()
    //     0xb6e0f0: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xb6e0f4: tbnz            w0, #4, #0xb6e188
    // 0xb6e0f8: ldr             x1, [fp, #0x18]
    // 0xb6e0fc: r0 = of()
    //     0xb6e0fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6e100: LoadField: r2 = r0->field_5b
    //     0xb6e100: ldur            w2, [x0, #0x5b]
    // 0xb6e104: DecompressPointer r2
    //     0xb6e104: add             x2, x2, HEAP, lsl #32
    // 0xb6e108: r16 = 2.000000
    //     0xb6e108: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xb6e10c: ldr             x16, [x16, #0xdf8]
    // 0xb6e110: str             x16, [SP]
    // 0xb6e114: r1 = Null
    //     0xb6e114: mov             x1, NULL
    // 0xb6e118: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb6e118: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb6e11c: ldr             x4, [x4, #0x108]
    // 0xb6e120: r0 = Border.all()
    //     0xb6e120: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb6e124: stur            x0, [fp, #-0x18]
    // 0xb6e128: r0 = Radius()
    //     0xb6e128: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6e12c: d0 = 10.000000
    //     0xb6e12c: fmov            d0, #10.00000000
    // 0xb6e130: stur            x0, [fp, #-0x20]
    // 0xb6e134: StoreField: r0->field_7 = d0
    //     0xb6e134: stur            d0, [x0, #7]
    // 0xb6e138: StoreField: r0->field_f = d0
    //     0xb6e138: stur            d0, [x0, #0xf]
    // 0xb6e13c: r0 = BorderRadius()
    //     0xb6e13c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb6e140: mov             x1, x0
    // 0xb6e144: ldur            x0, [fp, #-0x20]
    // 0xb6e148: stur            x1, [fp, #-0x28]
    // 0xb6e14c: StoreField: r1->field_7 = r0
    //     0xb6e14c: stur            w0, [x1, #7]
    // 0xb6e150: StoreField: r1->field_b = r0
    //     0xb6e150: stur            w0, [x1, #0xb]
    // 0xb6e154: StoreField: r1->field_f = r0
    //     0xb6e154: stur            w0, [x1, #0xf]
    // 0xb6e158: StoreField: r1->field_13 = r0
    //     0xb6e158: stur            w0, [x1, #0x13]
    // 0xb6e15c: r0 = BoxDecoration()
    //     0xb6e15c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb6e160: mov             x1, x0
    // 0xb6e164: ldur            x0, [fp, #-0x18]
    // 0xb6e168: StoreField: r1->field_f = r0
    //     0xb6e168: stur            w0, [x1, #0xf]
    // 0xb6e16c: ldur            x0, [fp, #-0x28]
    // 0xb6e170: StoreField: r1->field_13 = r0
    //     0xb6e170: stur            w0, [x1, #0x13]
    // 0xb6e174: r0 = Instance_BoxShape
    //     0xb6e174: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6e178: ldr             x0, [x0, #0x80]
    // 0xb6e17c: StoreField: r1->field_23 = r0
    //     0xb6e17c: stur            w0, [x1, #0x23]
    // 0xb6e180: mov             x2, x1
    // 0xb6e184: b               #0xb6e204
    // 0xb6e188: r0 = Instance_BoxShape
    //     0xb6e188: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6e18c: ldr             x0, [x0, #0x80]
    // 0xb6e190: r1 = Null
    //     0xb6e190: mov             x1, NULL
    // 0xb6e194: r2 = Instance_Color
    //     0xb6e194: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb6e198: ldr             x2, [x2, #0xf88]
    // 0xb6e19c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb6e19c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb6e1a0: r0 = Border.all()
    //     0xb6e1a0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb6e1a4: stur            x0, [fp, #-0x18]
    // 0xb6e1a8: r0 = Radius()
    //     0xb6e1a8: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6e1ac: d0 = 10.000000
    //     0xb6e1ac: fmov            d0, #10.00000000
    // 0xb6e1b0: stur            x0, [fp, #-0x20]
    // 0xb6e1b4: StoreField: r0->field_7 = d0
    //     0xb6e1b4: stur            d0, [x0, #7]
    // 0xb6e1b8: StoreField: r0->field_f = d0
    //     0xb6e1b8: stur            d0, [x0, #0xf]
    // 0xb6e1bc: r0 = BorderRadius()
    //     0xb6e1bc: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb6e1c0: mov             x1, x0
    // 0xb6e1c4: ldur            x0, [fp, #-0x20]
    // 0xb6e1c8: stur            x1, [fp, #-0x28]
    // 0xb6e1cc: StoreField: r1->field_7 = r0
    //     0xb6e1cc: stur            w0, [x1, #7]
    // 0xb6e1d0: StoreField: r1->field_b = r0
    //     0xb6e1d0: stur            w0, [x1, #0xb]
    // 0xb6e1d4: StoreField: r1->field_f = r0
    //     0xb6e1d4: stur            w0, [x1, #0xf]
    // 0xb6e1d8: StoreField: r1->field_13 = r0
    //     0xb6e1d8: stur            w0, [x1, #0x13]
    // 0xb6e1dc: r0 = BoxDecoration()
    //     0xb6e1dc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb6e1e0: mov             x1, x0
    // 0xb6e1e4: ldur            x0, [fp, #-0x18]
    // 0xb6e1e8: StoreField: r1->field_f = r0
    //     0xb6e1e8: stur            w0, [x1, #0xf]
    // 0xb6e1ec: ldur            x0, [fp, #-0x28]
    // 0xb6e1f0: StoreField: r1->field_13 = r0
    //     0xb6e1f0: stur            w0, [x1, #0x13]
    // 0xb6e1f4: r0 = Instance_BoxShape
    //     0xb6e1f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6e1f8: ldr             x0, [x0, #0x80]
    // 0xb6e1fc: StoreField: r1->field_23 = r0
    //     0xb6e1fc: stur            w0, [x1, #0x23]
    // 0xb6e200: mov             x2, x1
    // 0xb6e204: ldur            x1, [fp, #-8]
    // 0xb6e208: stur            x2, [fp, #-0x18]
    // 0xb6e20c: r0 = Radius()
    //     0xb6e20c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb6e210: d0 = 10.000000
    //     0xb6e210: fmov            d0, #10.00000000
    // 0xb6e214: stur            x0, [fp, #-0x20]
    // 0xb6e218: StoreField: r0->field_7 = d0
    //     0xb6e218: stur            d0, [x0, #7]
    // 0xb6e21c: StoreField: r0->field_f = d0
    //     0xb6e21c: stur            d0, [x0, #0xf]
    // 0xb6e220: r0 = BorderRadius()
    //     0xb6e220: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb6e224: mov             x1, x0
    // 0xb6e228: ldur            x0, [fp, #-0x20]
    // 0xb6e22c: stur            x1, [fp, #-0x28]
    // 0xb6e230: StoreField: r1->field_7 = r0
    //     0xb6e230: stur            w0, [x1, #7]
    // 0xb6e234: StoreField: r1->field_b = r0
    //     0xb6e234: stur            w0, [x1, #0xb]
    // 0xb6e238: StoreField: r1->field_f = r0
    //     0xb6e238: stur            w0, [x1, #0xf]
    // 0xb6e23c: StoreField: r1->field_13 = r0
    //     0xb6e23c: stur            w0, [x1, #0x13]
    // 0xb6e240: r0 = RoundedRectangleBorder()
    //     0xb6e240: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb6e244: mov             x3, x0
    // 0xb6e248: ldur            x0, [fp, #-0x28]
    // 0xb6e24c: stur            x3, [fp, #-0x30]
    // 0xb6e250: StoreField: r3->field_b = r0
    //     0xb6e250: stur            w0, [x3, #0xb]
    // 0xb6e254: r0 = Instance_BorderSide
    //     0xb6e254: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb6e258: ldr             x0, [x0, #0xe20]
    // 0xb6e25c: StoreField: r3->field_7 = r0
    //     0xb6e25c: stur            w0, [x3, #7]
    // 0xb6e260: ldur            x4, [fp, #-8]
    // 0xb6e264: LoadField: r0 = r4->field_f
    //     0xb6e264: ldur            w0, [x4, #0xf]
    // 0xb6e268: DecompressPointer r0
    //     0xb6e268: add             x0, x0, HEAP, lsl #32
    // 0xb6e26c: LoadField: r1 = r0->field_b
    //     0xb6e26c: ldur            w1, [x0, #0xb]
    // 0xb6e270: DecompressPointer r1
    //     0xb6e270: add             x1, x1, HEAP, lsl #32
    // 0xb6e274: cmp             w1, NULL
    // 0xb6e278: b.eq            #0xb6e7a4
    // 0xb6e27c: LoadField: r0 = r1->field_b
    //     0xb6e27c: ldur            w0, [x1, #0xb]
    // 0xb6e280: DecompressPointer r0
    //     0xb6e280: add             x0, x0, HEAP, lsl #32
    // 0xb6e284: LoadField: r2 = r0->field_73
    //     0xb6e284: ldur            w2, [x0, #0x73]
    // 0xb6e288: DecompressPointer r2
    //     0xb6e288: add             x2, x2, HEAP, lsl #32
    // 0xb6e28c: cmp             w2, NULL
    // 0xb6e290: b.ne            #0xb6e2a0
    // 0xb6e294: ldr             x5, [fp, #0x10]
    // 0xb6e298: r0 = Null
    //     0xb6e298: mov             x0, NULL
    // 0xb6e29c: b               #0xb6e2e4
    // 0xb6e2a0: ldr             x5, [fp, #0x10]
    // 0xb6e2a4: LoadField: r0 = r2->field_b
    //     0xb6e2a4: ldur            w0, [x2, #0xb]
    // 0xb6e2a8: r6 = LoadInt32Instr(r5)
    //     0xb6e2a8: sbfx            x6, x5, #1, #0x1f
    //     0xb6e2ac: tbz             w5, #0, #0xb6e2b4
    //     0xb6e2b0: ldur            x6, [x5, #7]
    // 0xb6e2b4: r1 = LoadInt32Instr(r0)
    //     0xb6e2b4: sbfx            x1, x0, #1, #0x1f
    // 0xb6e2b8: mov             x0, x1
    // 0xb6e2bc: mov             x1, x6
    // 0xb6e2c0: cmp             x1, x0
    // 0xb6e2c4: b.hs            #0xb6e7a8
    // 0xb6e2c8: LoadField: r0 = r2->field_f
    //     0xb6e2c8: ldur            w0, [x2, #0xf]
    // 0xb6e2cc: DecompressPointer r0
    //     0xb6e2cc: add             x0, x0, HEAP, lsl #32
    // 0xb6e2d0: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6e2d0: add             x16, x0, x6, lsl #2
    //     0xb6e2d4: ldur            w1, [x16, #0xf]
    // 0xb6e2d8: DecompressPointer r1
    //     0xb6e2d8: add             x1, x1, HEAP, lsl #32
    // 0xb6e2dc: LoadField: r0 = r1->field_f
    //     0xb6e2dc: ldur            w0, [x1, #0xf]
    // 0xb6e2e0: DecompressPointer r0
    //     0xb6e2e0: add             x0, x0, HEAP, lsl #32
    // 0xb6e2e4: cmp             w0, NULL
    // 0xb6e2e8: b.ne            #0xb6e2f0
    // 0xb6e2ec: r0 = ""
    //     0xb6e2ec: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6e2f0: stur            x0, [fp, #-0x20]
    // 0xb6e2f4: r1 = Function '<anonymous closure>':.
    //     0xb6e2f4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56278] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb6e2f8: ldr             x1, [x1, #0x278]
    // 0xb6e2fc: r2 = Null
    //     0xb6e2fc: mov             x2, NULL
    // 0xb6e300: r0 = AllocateClosure()
    //     0xb6e300: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6e304: r1 = Function '<anonymous closure>':.
    //     0xb6e304: add             x1, PP, #0x56, lsl #12  ; [pp+0x56280] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb6e308: ldr             x1, [x1, #0x280]
    // 0xb6e30c: r2 = Null
    //     0xb6e30c: mov             x2, NULL
    // 0xb6e310: stur            x0, [fp, #-0x28]
    // 0xb6e314: r0 = AllocateClosure()
    //     0xb6e314: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6e318: stur            x0, [fp, #-0x38]
    // 0xb6e31c: r0 = CachedNetworkImage()
    //     0xb6e31c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb6e320: stur            x0, [fp, #-0x40]
    // 0xb6e324: r16 = Instance_BoxFit
    //     0xb6e324: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb6e328: ldr             x16, [x16, #0xb18]
    // 0xb6e32c: ldur            lr, [fp, #-0x28]
    // 0xb6e330: stp             lr, x16, [SP, #8]
    // 0xb6e334: ldur            x16, [fp, #-0x38]
    // 0xb6e338: str             x16, [SP]
    // 0xb6e33c: mov             x1, x0
    // 0xb6e340: ldur            x2, [fp, #-0x20]
    // 0xb6e344: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb6e344: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb6e348: ldr             x4, [x4, #0x638]
    // 0xb6e34c: r0 = CachedNetworkImage()
    //     0xb6e34c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb6e350: r0 = Card()
    //     0xb6e350: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb6e354: mov             x1, x0
    // 0xb6e358: r0 = 0.000000
    //     0xb6e358: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb6e35c: stur            x1, [fp, #-0x20]
    // 0xb6e360: ArrayStore: r1[0] = r0  ; List_4
    //     0xb6e360: stur            w0, [x1, #0x17]
    // 0xb6e364: ldur            x0, [fp, #-0x30]
    // 0xb6e368: StoreField: r1->field_1b = r0
    //     0xb6e368: stur            w0, [x1, #0x1b]
    // 0xb6e36c: r0 = true
    //     0xb6e36c: add             x0, NULL, #0x20  ; true
    // 0xb6e370: StoreField: r1->field_1f = r0
    //     0xb6e370: stur            w0, [x1, #0x1f]
    // 0xb6e374: r2 = Instance_EdgeInsets
    //     0xb6e374: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb6e378: StoreField: r1->field_27 = r2
    //     0xb6e378: stur            w2, [x1, #0x27]
    // 0xb6e37c: r2 = Instance_Clip
    //     0xb6e37c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb6e380: ldr             x2, [x2, #0xb50]
    // 0xb6e384: StoreField: r1->field_23 = r2
    //     0xb6e384: stur            w2, [x1, #0x23]
    // 0xb6e388: ldur            x2, [fp, #-0x40]
    // 0xb6e38c: StoreField: r1->field_2f = r2
    //     0xb6e38c: stur            w2, [x1, #0x2f]
    // 0xb6e390: StoreField: r1->field_2b = r0
    //     0xb6e390: stur            w0, [x1, #0x2b]
    // 0xb6e394: r2 = Instance__CardVariant
    //     0xb6e394: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb6e398: ldr             x2, [x2, #0xa68]
    // 0xb6e39c: StoreField: r1->field_33 = r2
    //     0xb6e39c: stur            w2, [x1, #0x33]
    // 0xb6e3a0: r0 = AspectRatio()
    //     0xb6e3a0: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb6e3a4: d0 = 0.500000
    //     0xb6e3a4: fmov            d0, #0.50000000
    // 0xb6e3a8: stur            x0, [fp, #-0x28]
    // 0xb6e3ac: StoreField: r0->field_f = d0
    //     0xb6e3ac: stur            d0, [x0, #0xf]
    // 0xb6e3b0: ldur            x1, [fp, #-0x20]
    // 0xb6e3b4: StoreField: r0->field_b = r1
    //     0xb6e3b4: stur            w1, [x0, #0xb]
    // 0xb6e3b8: r0 = Container()
    //     0xb6e3b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6e3bc: stur            x0, [fp, #-0x20]
    // 0xb6e3c0: r16 = 80.000000
    //     0xb6e3c0: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb6e3c4: ldr             x16, [x16, #0x2f8]
    // 0xb6e3c8: r30 = 80.000000
    //     0xb6e3c8: add             lr, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0xb6e3cc: ldr             lr, [lr, #0x2f8]
    // 0xb6e3d0: stp             lr, x16, [SP, #0x10]
    // 0xb6e3d4: ldur            x16, [fp, #-0x18]
    // 0xb6e3d8: ldur            lr, [fp, #-0x28]
    // 0xb6e3dc: stp             lr, x16, [SP]
    // 0xb6e3e0: mov             x1, x0
    // 0xb6e3e4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb6e3e4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb6e3e8: ldr             x4, [x4, #0x870]
    // 0xb6e3ec: r0 = Container()
    //     0xb6e3ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb6e3f0: ldur            x0, [fp, #-8]
    // 0xb6e3f4: LoadField: r1 = r0->field_f
    //     0xb6e3f4: ldur            w1, [x0, #0xf]
    // 0xb6e3f8: DecompressPointer r1
    //     0xb6e3f8: add             x1, x1, HEAP, lsl #32
    // 0xb6e3fc: LoadField: r2 = r1->field_b
    //     0xb6e3fc: ldur            w2, [x1, #0xb]
    // 0xb6e400: DecompressPointer r2
    //     0xb6e400: add             x2, x2, HEAP, lsl #32
    // 0xb6e404: cmp             w2, NULL
    // 0xb6e408: b.eq            #0xb6e7ac
    // 0xb6e40c: LoadField: r0 = r2->field_b
    //     0xb6e40c: ldur            w0, [x2, #0xb]
    // 0xb6e410: DecompressPointer r0
    //     0xb6e410: add             x0, x0, HEAP, lsl #32
    // 0xb6e414: LoadField: r3 = r0->field_73
    //     0xb6e414: ldur            w3, [x0, #0x73]
    // 0xb6e418: DecompressPointer r3
    //     0xb6e418: add             x3, x3, HEAP, lsl #32
    // 0xb6e41c: cmp             w3, NULL
    // 0xb6e420: b.ne            #0xb6e430
    // 0xb6e424: ldr             x4, [fp, #0x10]
    // 0xb6e428: r0 = Null
    //     0xb6e428: mov             x0, NULL
    // 0xb6e42c: b               #0xb6e474
    // 0xb6e430: ldr             x4, [fp, #0x10]
    // 0xb6e434: LoadField: r0 = r3->field_b
    //     0xb6e434: ldur            w0, [x3, #0xb]
    // 0xb6e438: r5 = LoadInt32Instr(r4)
    //     0xb6e438: sbfx            x5, x4, #1, #0x1f
    //     0xb6e43c: tbz             w4, #0, #0xb6e444
    //     0xb6e440: ldur            x5, [x4, #7]
    // 0xb6e444: r1 = LoadInt32Instr(r0)
    //     0xb6e444: sbfx            x1, x0, #1, #0x1f
    // 0xb6e448: mov             x0, x1
    // 0xb6e44c: mov             x1, x5
    // 0xb6e450: cmp             x1, x0
    // 0xb6e454: b.hs            #0xb6e7b0
    // 0xb6e458: LoadField: r0 = r3->field_f
    //     0xb6e458: ldur            w0, [x3, #0xf]
    // 0xb6e45c: DecompressPointer r0
    //     0xb6e45c: add             x0, x0, HEAP, lsl #32
    // 0xb6e460: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb6e460: add             x16, x0, x5, lsl #2
    //     0xb6e464: ldur            w1, [x16, #0xf]
    // 0xb6e468: DecompressPointer r1
    //     0xb6e468: add             x1, x1, HEAP, lsl #32
    // 0xb6e46c: LoadField: r0 = r1->field_13
    //     0xb6e46c: ldur            w0, [x1, #0x13]
    // 0xb6e470: DecompressPointer r0
    //     0xb6e470: add             x0, x0, HEAP, lsl #32
    // 0xb6e474: cmp             w0, NULL
    // 0xb6e478: r16 = true
    //     0xb6e478: add             x16, NULL, #0x20  ; true
    // 0xb6e47c: r17 = false
    //     0xb6e47c: add             x17, NULL, #0x30  ; false
    // 0xb6e480: csel            x3, x16, x17, ne
    // 0xb6e484: stur            x3, [fp, #-8]
    // 0xb6e488: LoadField: r0 = r2->field_b
    //     0xb6e488: ldur            w0, [x2, #0xb]
    // 0xb6e48c: DecompressPointer r0
    //     0xb6e48c: add             x0, x0, HEAP, lsl #32
    // 0xb6e490: LoadField: r5 = r0->field_73
    //     0xb6e490: ldur            w5, [x0, #0x73]
    // 0xb6e494: DecompressPointer r5
    //     0xb6e494: add             x5, x5, HEAP, lsl #32
    // 0xb6e498: cmp             w5, NULL
    // 0xb6e49c: b.ne            #0xb6e4a8
    // 0xb6e4a0: r0 = Null
    //     0xb6e4a0: mov             x0, NULL
    // 0xb6e4a4: b               #0xb6e50c
    // 0xb6e4a8: LoadField: r0 = r5->field_b
    //     0xb6e4a8: ldur            w0, [x5, #0xb]
    // 0xb6e4ac: r6 = LoadInt32Instr(r4)
    //     0xb6e4ac: sbfx            x6, x4, #1, #0x1f
    //     0xb6e4b0: tbz             w4, #0, #0xb6e4b8
    //     0xb6e4b4: ldur            x6, [x4, #7]
    // 0xb6e4b8: r1 = LoadInt32Instr(r0)
    //     0xb6e4b8: sbfx            x1, x0, #1, #0x1f
    // 0xb6e4bc: mov             x0, x1
    // 0xb6e4c0: mov             x1, x6
    // 0xb6e4c4: cmp             x1, x0
    // 0xb6e4c8: b.hs            #0xb6e7b4
    // 0xb6e4cc: LoadField: r0 = r5->field_f
    //     0xb6e4cc: ldur            w0, [x5, #0xf]
    // 0xb6e4d0: DecompressPointer r0
    //     0xb6e4d0: add             x0, x0, HEAP, lsl #32
    // 0xb6e4d4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb6e4d4: add             x16, x0, x6, lsl #2
    //     0xb6e4d8: ldur            w1, [x16, #0xf]
    // 0xb6e4dc: DecompressPointer r1
    //     0xb6e4dc: add             x1, x1, HEAP, lsl #32
    // 0xb6e4e0: LoadField: r0 = r1->field_13
    //     0xb6e4e0: ldur            w0, [x1, #0x13]
    // 0xb6e4e4: DecompressPointer r0
    //     0xb6e4e4: add             x0, x0, HEAP, lsl #32
    // 0xb6e4e8: cmp             w0, NULL
    // 0xb6e4ec: b.ne            #0xb6e4f8
    // 0xb6e4f0: r0 = Null
    //     0xb6e4f0: mov             x0, NULL
    // 0xb6e4f4: b               #0xb6e50c
    // 0xb6e4f8: LoadField: r1 = r0->field_7
    //     0xb6e4f8: ldur            w1, [x0, #7]
    // 0xb6e4fc: cbnz            w1, #0xb6e508
    // 0xb6e500: r0 = false
    //     0xb6e500: add             x0, NULL, #0x30  ; false
    // 0xb6e504: b               #0xb6e50c
    // 0xb6e508: r0 = true
    //     0xb6e508: add             x0, NULL, #0x20  ; true
    // 0xb6e50c: cmp             w0, NULL
    // 0xb6e510: b.eq            #0xb6e5a0
    // 0xb6e514: tbnz            w0, #4, #0xb6e5a0
    // 0xb6e518: LoadField: r0 = r2->field_b
    //     0xb6e518: ldur            w0, [x2, #0xb]
    // 0xb6e51c: DecompressPointer r0
    //     0xb6e51c: add             x0, x0, HEAP, lsl #32
    // 0xb6e520: LoadField: r2 = r0->field_73
    //     0xb6e520: ldur            w2, [x0, #0x73]
    // 0xb6e524: DecompressPointer r2
    //     0xb6e524: add             x2, x2, HEAP, lsl #32
    // 0xb6e528: cmp             w2, NULL
    // 0xb6e52c: b.ne            #0xb6e538
    // 0xb6e530: r0 = Null
    //     0xb6e530: mov             x0, NULL
    // 0xb6e534: b               #0xb6e590
    // 0xb6e538: LoadField: r0 = r2->field_b
    //     0xb6e538: ldur            w0, [x2, #0xb]
    // 0xb6e53c: r5 = LoadInt32Instr(r4)
    //     0xb6e53c: sbfx            x5, x4, #1, #0x1f
    //     0xb6e540: tbz             w4, #0, #0xb6e548
    //     0xb6e544: ldur            x5, [x4, #7]
    // 0xb6e548: r1 = LoadInt32Instr(r0)
    //     0xb6e548: sbfx            x1, x0, #1, #0x1f
    // 0xb6e54c: mov             x0, x1
    // 0xb6e550: mov             x1, x5
    // 0xb6e554: cmp             x1, x0
    // 0xb6e558: b.hs            #0xb6e7b8
    // 0xb6e55c: LoadField: r0 = r2->field_f
    //     0xb6e55c: ldur            w0, [x2, #0xf]
    // 0xb6e560: DecompressPointer r0
    //     0xb6e560: add             x0, x0, HEAP, lsl #32
    // 0xb6e564: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb6e564: add             x16, x0, x5, lsl #2
    //     0xb6e568: ldur            w1, [x16, #0xf]
    // 0xb6e56c: DecompressPointer r1
    //     0xb6e56c: add             x1, x1, HEAP, lsl #32
    // 0xb6e570: LoadField: r0 = r1->field_13
    //     0xb6e570: ldur            w0, [x1, #0x13]
    // 0xb6e574: DecompressPointer r0
    //     0xb6e574: add             x0, x0, HEAP, lsl #32
    // 0xb6e578: cmp             w0, NULL
    // 0xb6e57c: b.ne            #0xb6e588
    // 0xb6e580: r0 = Null
    //     0xb6e580: mov             x0, NULL
    // 0xb6e584: b               #0xb6e590
    // 0xb6e588: mov             x1, x0
    // 0xb6e58c: r0 = StringExtension.toTitleCase()
    //     0xb6e58c: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0xb6e590: str             x0, [SP]
    // 0xb6e594: r0 = _interpolateSingle()
    //     0xb6e594: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb6e598: mov             x3, x0
    // 0xb6e59c: b               #0xb6e5a4
    // 0xb6e5a0: r3 = ""
    //     0xb6e5a0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb6e5a4: ldur            x2, [fp, #-0x20]
    // 0xb6e5a8: ldur            x0, [fp, #-8]
    // 0xb6e5ac: ldr             x1, [fp, #0x18]
    // 0xb6e5b0: stur            x3, [fp, #-0x18]
    // 0xb6e5b4: r0 = of()
    //     0xb6e5b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6e5b8: LoadField: r1 = r0->field_87
    //     0xb6e5b8: ldur            w1, [x0, #0x87]
    // 0xb6e5bc: DecompressPointer r1
    //     0xb6e5bc: add             x1, x1, HEAP, lsl #32
    // 0xb6e5c0: LoadField: r0 = r1->field_2b
    //     0xb6e5c0: ldur            w0, [x1, #0x2b]
    // 0xb6e5c4: DecompressPointer r0
    //     0xb6e5c4: add             x0, x0, HEAP, lsl #32
    // 0xb6e5c8: stur            x0, [fp, #-0x28]
    // 0xb6e5cc: r1 = Instance_Color
    //     0xb6e5cc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6e5d0: d0 = 0.700000
    //     0xb6e5d0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb6e5d4: ldr             d0, [x17, #0xf48]
    // 0xb6e5d8: r0 = withOpacity()
    //     0xb6e5d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6e5dc: r16 = 12.000000
    //     0xb6e5dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb6e5e0: ldr             x16, [x16, #0x9e8]
    // 0xb6e5e4: stp             x0, x16, [SP]
    // 0xb6e5e8: ldur            x1, [fp, #-0x28]
    // 0xb6e5ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6e5ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb6e5f0: ldr             x4, [x4, #0xaa0]
    // 0xb6e5f4: r0 = copyWith()
    //     0xb6e5f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6e5f8: stur            x0, [fp, #-0x28]
    // 0xb6e5fc: r0 = Text()
    //     0xb6e5fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb6e600: mov             x1, x0
    // 0xb6e604: ldur            x0, [fp, #-0x18]
    // 0xb6e608: stur            x1, [fp, #-0x30]
    // 0xb6e60c: StoreField: r1->field_b = r0
    //     0xb6e60c: stur            w0, [x1, #0xb]
    // 0xb6e610: ldur            x0, [fp, #-0x28]
    // 0xb6e614: StoreField: r1->field_13 = r0
    //     0xb6e614: stur            w0, [x1, #0x13]
    // 0xb6e618: r0 = Padding()
    //     0xb6e618: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6e61c: mov             x1, x0
    // 0xb6e620: r0 = Instance_EdgeInsets
    //     0xb6e620: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9e0] Obj!EdgeInsets@d577a1
    //     0xb6e624: ldr             x0, [x0, #0x9e0]
    // 0xb6e628: stur            x1, [fp, #-0x18]
    // 0xb6e62c: StoreField: r1->field_f = r0
    //     0xb6e62c: stur            w0, [x1, #0xf]
    // 0xb6e630: ldur            x0, [fp, #-0x30]
    // 0xb6e634: StoreField: r1->field_b = r0
    //     0xb6e634: stur            w0, [x1, #0xb]
    // 0xb6e638: r0 = Visibility()
    //     0xb6e638: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb6e63c: mov             x3, x0
    // 0xb6e640: ldur            x0, [fp, #-0x18]
    // 0xb6e644: stur            x3, [fp, #-0x28]
    // 0xb6e648: StoreField: r3->field_b = r0
    //     0xb6e648: stur            w0, [x3, #0xb]
    // 0xb6e64c: r0 = Instance_SizedBox
    //     0xb6e64c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb6e650: StoreField: r3->field_f = r0
    //     0xb6e650: stur            w0, [x3, #0xf]
    // 0xb6e654: ldur            x0, [fp, #-8]
    // 0xb6e658: StoreField: r3->field_13 = r0
    //     0xb6e658: stur            w0, [x3, #0x13]
    // 0xb6e65c: r0 = false
    //     0xb6e65c: add             x0, NULL, #0x30  ; false
    // 0xb6e660: ArrayStore: r3[0] = r0  ; List_4
    //     0xb6e660: stur            w0, [x3, #0x17]
    // 0xb6e664: StoreField: r3->field_1b = r0
    //     0xb6e664: stur            w0, [x3, #0x1b]
    // 0xb6e668: StoreField: r3->field_1f = r0
    //     0xb6e668: stur            w0, [x3, #0x1f]
    // 0xb6e66c: StoreField: r3->field_23 = r0
    //     0xb6e66c: stur            w0, [x3, #0x23]
    // 0xb6e670: StoreField: r3->field_27 = r0
    //     0xb6e670: stur            w0, [x3, #0x27]
    // 0xb6e674: StoreField: r3->field_2b = r0
    //     0xb6e674: stur            w0, [x3, #0x2b]
    // 0xb6e678: r1 = Null
    //     0xb6e678: mov             x1, NULL
    // 0xb6e67c: r2 = 4
    //     0xb6e67c: movz            x2, #0x4
    // 0xb6e680: r0 = AllocateArray()
    //     0xb6e680: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6e684: mov             x2, x0
    // 0xb6e688: ldur            x0, [fp, #-0x20]
    // 0xb6e68c: stur            x2, [fp, #-8]
    // 0xb6e690: StoreField: r2->field_f = r0
    //     0xb6e690: stur            w0, [x2, #0xf]
    // 0xb6e694: ldur            x0, [fp, #-0x28]
    // 0xb6e698: StoreField: r2->field_13 = r0
    //     0xb6e698: stur            w0, [x2, #0x13]
    // 0xb6e69c: r1 = <Widget>
    //     0xb6e69c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6e6a0: r0 = AllocateGrowableArray()
    //     0xb6e6a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb6e6a4: mov             x1, x0
    // 0xb6e6a8: ldur            x0, [fp, #-8]
    // 0xb6e6ac: stur            x1, [fp, #-0x18]
    // 0xb6e6b0: StoreField: r1->field_f = r0
    //     0xb6e6b0: stur            w0, [x1, #0xf]
    // 0xb6e6b4: r0 = 4
    //     0xb6e6b4: movz            x0, #0x4
    // 0xb6e6b8: StoreField: r1->field_b = r0
    //     0xb6e6b8: stur            w0, [x1, #0xb]
    // 0xb6e6bc: r0 = Column()
    //     0xb6e6bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb6e6c0: mov             x1, x0
    // 0xb6e6c4: r0 = Instance_Axis
    //     0xb6e6c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb6e6c8: stur            x1, [fp, #-8]
    // 0xb6e6cc: StoreField: r1->field_f = r0
    //     0xb6e6cc: stur            w0, [x1, #0xf]
    // 0xb6e6d0: r0 = Instance_MainAxisAlignment
    //     0xb6e6d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb6e6d4: ldr             x0, [x0, #0xa08]
    // 0xb6e6d8: StoreField: r1->field_13 = r0
    //     0xb6e6d8: stur            w0, [x1, #0x13]
    // 0xb6e6dc: r0 = Instance_MainAxisSize
    //     0xb6e6dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6e6e0: ldr             x0, [x0, #0xa10]
    // 0xb6e6e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb6e6e4: stur            w0, [x1, #0x17]
    // 0xb6e6e8: r0 = Instance_CrossAxisAlignment
    //     0xb6e6e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb6e6ec: ldr             x0, [x0, #0xa18]
    // 0xb6e6f0: StoreField: r1->field_1b = r0
    //     0xb6e6f0: stur            w0, [x1, #0x1b]
    // 0xb6e6f4: r0 = Instance_VerticalDirection
    //     0xb6e6f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb6e6f8: ldr             x0, [x0, #0xa20]
    // 0xb6e6fc: StoreField: r1->field_23 = r0
    //     0xb6e6fc: stur            w0, [x1, #0x23]
    // 0xb6e700: r0 = Instance_Clip
    //     0xb6e700: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb6e704: ldr             x0, [x0, #0x38]
    // 0xb6e708: StoreField: r1->field_2b = r0
    //     0xb6e708: stur            w0, [x1, #0x2b]
    // 0xb6e70c: StoreField: r1->field_2f = rZR
    //     0xb6e70c: stur            xzr, [x1, #0x2f]
    // 0xb6e710: ldur            x0, [fp, #-0x18]
    // 0xb6e714: StoreField: r1->field_b = r0
    //     0xb6e714: stur            w0, [x1, #0xb]
    // 0xb6e718: r0 = InkWell()
    //     0xb6e718: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb6e71c: mov             x3, x0
    // 0xb6e720: ldur            x0, [fp, #-8]
    // 0xb6e724: stur            x3, [fp, #-0x18]
    // 0xb6e728: StoreField: r3->field_b = r0
    //     0xb6e728: stur            w0, [x3, #0xb]
    // 0xb6e72c: ldur            x2, [fp, #-0x10]
    // 0xb6e730: r1 = Function '<anonymous closure>':.
    //     0xb6e730: add             x1, PP, #0x56, lsl #12  ; [pp+0x56288] AnonymousClosure: (0xb6e7bc), in [package:customer_app/app/presentation/views/glass/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xb6b7f4)
    //     0xb6e734: ldr             x1, [x1, #0x288]
    // 0xb6e738: r0 = AllocateClosure()
    //     0xb6e738: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6e73c: mov             x1, x0
    // 0xb6e740: ldur            x0, [fp, #-0x18]
    // 0xb6e744: StoreField: r0->field_f = r1
    //     0xb6e744: stur            w1, [x0, #0xf]
    // 0xb6e748: r1 = true
    //     0xb6e748: add             x1, NULL, #0x20  ; true
    // 0xb6e74c: StoreField: r0->field_43 = r1
    //     0xb6e74c: stur            w1, [x0, #0x43]
    // 0xb6e750: r2 = Instance_BoxShape
    //     0xb6e750: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb6e754: ldr             x2, [x2, #0x80]
    // 0xb6e758: StoreField: r0->field_47 = r2
    //     0xb6e758: stur            w2, [x0, #0x47]
    // 0xb6e75c: StoreField: r0->field_6f = r1
    //     0xb6e75c: stur            w1, [x0, #0x6f]
    // 0xb6e760: r2 = false
    //     0xb6e760: add             x2, NULL, #0x30  ; false
    // 0xb6e764: StoreField: r0->field_73 = r2
    //     0xb6e764: stur            w2, [x0, #0x73]
    // 0xb6e768: StoreField: r0->field_83 = r1
    //     0xb6e768: stur            w1, [x0, #0x83]
    // 0xb6e76c: StoreField: r0->field_7b = r2
    //     0xb6e76c: stur            w2, [x0, #0x7b]
    // 0xb6e770: r0 = Padding()
    //     0xb6e770: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb6e774: r1 = Instance_EdgeInsets
    //     0xb6e774: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb6e778: ldr             x1, [x1, #0x980]
    // 0xb6e77c: StoreField: r0->field_f = r1
    //     0xb6e77c: stur            w1, [x0, #0xf]
    // 0xb6e780: ldur            x1, [fp, #-0x18]
    // 0xb6e784: StoreField: r0->field_b = r1
    //     0xb6e784: stur            w1, [x0, #0xb]
    // 0xb6e788: LeaveFrame
    //     0xb6e788: mov             SP, fp
    //     0xb6e78c: ldp             fp, lr, [SP], #0x10
    // 0xb6e790: ret
    //     0xb6e790: ret             
    // 0xb6e794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6e794: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6e798: b               #0xb6e03c
    // 0xb6e79c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6e79c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6e7a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6e7a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6e7a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6e7a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6e7a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6e7a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6e7ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb6e7ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb6e7b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6e7b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6e7b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6e7b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb6e7b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb6e7b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6e7bc, size: 0x68
    // 0xb6e7bc: EnterFrame
    //     0xb6e7bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb6e7c0: mov             fp, SP
    // 0xb6e7c4: AllocStack(0x8)
    //     0xb6e7c4: sub             SP, SP, #8
    // 0xb6e7c8: SetupParameters()
    //     0xb6e7c8: ldr             x0, [fp, #0x10]
    //     0xb6e7cc: ldur            w2, [x0, #0x17]
    //     0xb6e7d0: add             x2, x2, HEAP, lsl #32
    // 0xb6e7d4: CheckStackOverflow
    //     0xb6e7d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6e7d8: cmp             SP, x16
    //     0xb6e7dc: b.ls            #0xb6e81c
    // 0xb6e7e0: LoadField: r0 = r2->field_b
    //     0xb6e7e0: ldur            w0, [x2, #0xb]
    // 0xb6e7e4: DecompressPointer r0
    //     0xb6e7e4: add             x0, x0, HEAP, lsl #32
    // 0xb6e7e8: LoadField: r3 = r0->field_f
    //     0xb6e7e8: ldur            w3, [x0, #0xf]
    // 0xb6e7ec: DecompressPointer r3
    //     0xb6e7ec: add             x3, x3, HEAP, lsl #32
    // 0xb6e7f0: stur            x3, [fp, #-8]
    // 0xb6e7f4: r1 = Function '<anonymous closure>':.
    //     0xb6e7f4: add             x1, PP, #0x56, lsl #12  ; [pp+0x56290] AnonymousClosure: (0xa61dd4), in [package:customer_app/app/presentation/views/line/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::build (0xbedbb0)
    //     0xb6e7f8: ldr             x1, [x1, #0x290]
    // 0xb6e7fc: r0 = AllocateClosure()
    //     0xb6e7fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6e800: ldur            x1, [fp, #-8]
    // 0xb6e804: mov             x2, x0
    // 0xb6e808: r0 = setState()
    //     0xb6e808: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb6e80c: r0 = Null
    //     0xb6e80c: mov             x0, NULL
    // 0xb6e810: LeaveFrame
    //     0xb6e810: mov             SP, fp
    //     0xb6e814: ldp             fp, lr, [SP], #0x10
    // 0xb6e818: ret
    //     0xb6e818: ret             
    // 0xb6e81c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb6e81c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6e820: b               #0xb6e7e0
  }
}

// class id: 4073, size: 0x2c, field offset: 0xc
//   const constructor, 
class SelectSizeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f434, size: 0x48
    // 0xc7f434: EnterFrame
    //     0xc7f434: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f438: mov             fp, SP
    // 0xc7f43c: AllocStack(0x8)
    //     0xc7f43c: sub             SP, SP, #8
    // 0xc7f440: CheckStackOverflow
    //     0xc7f440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f444: cmp             SP, x16
    //     0xc7f448: b.ls            #0xc7f474
    // 0xc7f44c: r1 = <SelectSizeBottomSheet>
    //     0xc7f44c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48870] TypeArguments: <SelectSizeBottomSheet>
    //     0xc7f450: ldr             x1, [x1, #0x870]
    // 0xc7f454: r0 = _SelectSizeBottomSheetState()
    //     0xc7f454: bl              #0xc7f47c  ; Allocate_SelectSizeBottomSheetStateStub -> _SelectSizeBottomSheetState (size=0x30)
    // 0xc7f458: mov             x1, x0
    // 0xc7f45c: stur            x0, [fp, #-8]
    // 0xc7f460: r0 = _SelectSizeBottomSheetState()
    //     0xc7f460: bl              #0xc7bc9c  ; [package:customer_app/app/presentation/views/basic/home/<USER>/select_size_bottom_sheet.dart] _SelectSizeBottomSheetState::_SelectSizeBottomSheetState
    // 0xc7f464: ldur            x0, [fp, #-8]
    // 0xc7f468: LeaveFrame
    //     0xc7f468: mov             SP, fp
    //     0xc7f46c: ldp             fp, lr, [SP], #0x10
    // 0xc7f470: ret
    //     0xc7f470: ret             
    // 0xc7f474: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f474: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f478: b               #0xc7f44c
  }
}
