// lib: , url: package:dio/src/cancel_token.dart

// class id: 1049599, size: 0x8
class :: {
}

// class id: 4995, size: 0x14, field offset: 0x8
class CancelToken extends Object {

  _ CancelToken(/* No info */) {
    // ** addr: 0xa10000, size: 0xa8
    // 0xa10000: EnterFrame
    //     0xa10000: stp             fp, lr, [SP, #-0x10]!
    //     0xa10004: mov             fp, SP
    // 0xa10008: AllocStack(0x10)
    //     0xa10008: sub             SP, SP, #0x10
    // 0xa1000c: SetupParameters(CancelToken this /* r1 => r0, fp-0x8 */)
    //     0xa1000c: mov             x0, x1
    //     0xa10010: stur            x1, [fp, #-8]
    // 0xa10014: CheckStackOverflow
    //     0xa10014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa10018: cmp             SP, x16
    //     0xa1001c: b.ls            #0xa100a0
    // 0xa10020: r1 = <DioException>
    //     0xa10020: add             x1, PP, #8, lsl #12  ; [pp+0x83c0] TypeArguments: <DioException>
    //     0xa10024: ldr             x1, [x1, #0x3c0]
    // 0xa10028: r0 = _Future()
    //     0xa10028: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xa1002c: stur            x0, [fp, #-0x10]
    // 0xa10030: StoreField: r0->field_b = rZR
    //     0xa10030: stur            xzr, [x0, #0xb]
    // 0xa10034: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0xa10034: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa10038: ldr             x0, [x0, #0x778]
    //     0xa1003c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa10040: cmp             w0, w16
    //     0xa10044: b.ne            #0xa10050
    //     0xa10048: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0xa1004c: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0xa10050: mov             x1, x0
    // 0xa10054: ldur            x0, [fp, #-0x10]
    // 0xa10058: StoreField: r0->field_13 = r1
    //     0xa10058: stur            w1, [x0, #0x13]
    // 0xa1005c: r1 = <DioException>
    //     0xa1005c: add             x1, PP, #8, lsl #12  ; [pp+0x83c0] TypeArguments: <DioException>
    //     0xa10060: ldr             x1, [x1, #0x3c0]
    // 0xa10064: r0 = _AsyncCompleter()
    //     0xa10064: bl              #0x632658  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xa10068: ldur            x1, [fp, #-0x10]
    // 0xa1006c: StoreField: r0->field_b = r1
    //     0xa1006c: stur            w1, [x0, #0xb]
    // 0xa10070: ldur            x1, [fp, #-8]
    // 0xa10074: StoreField: r1->field_7 = r0
    //     0xa10074: stur            w0, [x1, #7]
    //     0xa10078: ldurb           w16, [x1, #-1]
    //     0xa1007c: ldurb           w17, [x0, #-1]
    //     0xa10080: and             x16, x17, x16, lsr #2
    //     0xa10084: tst             x16, HEAP, lsr #32
    //     0xa10088: b.eq            #0xa10090
    //     0xa1008c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa10090: r0 = Null
    //     0xa10090: mov             x0, NULL
    // 0xa10094: LeaveFrame
    //     0xa10094: mov             SP, fp
    //     0xa10098: ldp             fp, lr, [SP], #0x10
    // 0xa1009c: ret
    //     0xa1009c: ret             
    // 0xa100a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa100a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa100a4: b               #0xa10020
  }
  _ cancel(/* No info */) {
    // ** addr: 0xa100b4, size: 0xd0
    // 0xa100b4: EnterFrame
    //     0xa100b4: stp             fp, lr, [SP, #-0x10]!
    //     0xa100b8: mov             fp, SP
    // 0xa100bc: AllocStack(0x18)
    //     0xa100bc: sub             SP, SP, #0x18
    // 0xa100c0: SetupParameters(CancelToken this /* r1 => r1, fp-0x8 */)
    //     0xa100c0: stur            x1, [fp, #-8]
    // 0xa100c4: CheckStackOverflow
    //     0xa100c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa100c8: cmp             SP, x16
    //     0xa100cc: b.ls            #0xa1017c
    // 0xa100d0: LoadField: r0 = r1->field_f
    //     0xa100d0: ldur            w0, [x1, #0xf]
    // 0xa100d4: DecompressPointer r0
    //     0xa100d4: add             x0, x0, HEAP, lsl #32
    // 0xa100d8: cmp             w0, NULL
    // 0xa100dc: b.ne            #0xa100fc
    // 0xa100e0: r0 = RequestOptions()
    //     0xa100e0: bl              #0x885b48  ; AllocateRequestOptionsStub -> RequestOptions (size=0x6c)
    // 0xa100e4: mov             x1, x0
    // 0xa100e8: stur            x0, [fp, #-0x10]
    // 0xa100ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa100ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa100f0: r0 = RequestOptions()
    //     0xa100f0: bl              #0x884c2c  ; [package:dio/src/options.dart] RequestOptions::RequestOptions
    // 0xa100f4: ldur            x2, [fp, #-0x10]
    // 0xa100f8: b               #0xa10100
    // 0xa100fc: mov             x2, x0
    // 0xa10100: ldur            x0, [fp, #-8]
    // 0xa10104: stur            x2, [fp, #-0x10]
    // 0xa10108: r0 = current()
    //     0xa10108: bl              #0x61b99c  ; [dart:core] StackTrace::current
    // 0xa1010c: ldur            x2, [fp, #-0x10]
    // 0xa10110: mov             x3, x0
    // 0xa10114: r1 = Null
    //     0xa10114: mov             x1, NULL
    // 0xa10118: r0 = DioException.requestCancelled()
    //     0xa10118: bl              #0xa10184  ; [package:dio/src/dio_exception.dart] DioException::DioException.requestCancelled
    // 0xa1011c: mov             x2, x0
    // 0xa10120: ldur            x1, [fp, #-8]
    // 0xa10124: StoreField: r1->field_b = r0
    //     0xa10124: stur            w0, [x1, #0xb]
    //     0xa10128: ldurb           w16, [x1, #-1]
    //     0xa1012c: ldurb           w17, [x0, #-1]
    //     0xa10130: and             x16, x17, x16, lsr #2
    //     0xa10134: tst             x16, HEAP, lsr #32
    //     0xa10138: b.eq            #0xa10140
    //     0xa1013c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa10140: LoadField: r0 = r1->field_7
    //     0xa10140: ldur            w0, [x1, #7]
    // 0xa10144: DecompressPointer r0
    //     0xa10144: add             x0, x0, HEAP, lsl #32
    // 0xa10148: LoadField: r1 = r0->field_b
    //     0xa10148: ldur            w1, [x0, #0xb]
    // 0xa1014c: DecompressPointer r1
    //     0xa1014c: add             x1, x1, HEAP, lsl #32
    // 0xa10150: LoadField: r3 = r1->field_b
    //     0xa10150: ldur            x3, [x1, #0xb]
    // 0xa10154: tst             x3, #0x1e
    // 0xa10158: b.ne            #0xa1016c
    // 0xa1015c: str             x2, [SP]
    // 0xa10160: mov             x1, x0
    // 0xa10164: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xa10164: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xa10168: r0 = complete()
    //     0xa10168: bl              #0x16646a8  ; [dart:async] _AsyncCompleter::complete
    // 0xa1016c: r0 = Null
    //     0xa1016c: mov             x0, NULL
    // 0xa10170: LeaveFrame
    //     0xa10170: mov             SP, fp
    //     0xa10174: ldp             fp, lr, [SP], #0x10
    // 0xa10178: ret
    //     0xa10178: ret             
    // 0xa1017c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1017c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa10180: b               #0xa100d0
  }
}
