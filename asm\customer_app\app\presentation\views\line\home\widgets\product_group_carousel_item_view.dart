// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart

// class id: 1049526, size: 0x8
class :: {
}

// class id: 3245, size: 0x24, field offset: 0x14
class _ProductGroupCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14
  late PageController _imagePageController; // offset: 0x18

  _ initState(/* No info */) {
    // ** addr: 0x949238, size: 0xcc
    // 0x949238: EnterFrame
    //     0x949238: stp             fp, lr, [SP, #-0x10]!
    //     0x94923c: mov             fp, SP
    // 0x949240: AllocStack(0x10)
    //     0x949240: sub             SP, SP, #0x10
    // 0x949244: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */)
    //     0x949244: stur            x1, [fp, #-8]
    // 0x949248: CheckStackOverflow
    //     0x949248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94924c: cmp             SP, x16
    //     0x949250: b.ls            #0x9492fc
    // 0x949254: r0 = PageController()
    //     0x949254: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x949258: stur            x0, [fp, #-0x10]
    // 0x94925c: StoreField: r0->field_3f = rZR
    //     0x94925c: stur            xzr, [x0, #0x3f]
    // 0x949260: r2 = true
    //     0x949260: add             x2, NULL, #0x20  ; true
    // 0x949264: StoreField: r0->field_47 = r2
    //     0x949264: stur            w2, [x0, #0x47]
    // 0x949268: d0 = 1.000000
    //     0x949268: fmov            d0, #1.00000000
    // 0x94926c: StoreField: r0->field_4b = d0
    //     0x94926c: stur            d0, [x0, #0x4b]
    // 0x949270: mov             x1, x0
    // 0x949274: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x949274: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x949278: r0 = ScrollController()
    //     0x949278: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x94927c: ldur            x0, [fp, #-0x10]
    // 0x949280: ldur            x1, [fp, #-8]
    // 0x949284: StoreField: r1->field_13 = r0
    //     0x949284: stur            w0, [x1, #0x13]
    //     0x949288: ldurb           w16, [x1, #-1]
    //     0x94928c: ldurb           w17, [x0, #-1]
    //     0x949290: and             x16, x17, x16, lsr #2
    //     0x949294: tst             x16, HEAP, lsr #32
    //     0x949298: b.eq            #0x9492a0
    //     0x94929c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x9492a0: r0 = PageController()
    //     0x9492a0: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x9492a4: stur            x0, [fp, #-0x10]
    // 0x9492a8: StoreField: r0->field_3f = rZR
    //     0x9492a8: stur            xzr, [x0, #0x3f]
    // 0x9492ac: r1 = true
    //     0x9492ac: add             x1, NULL, #0x20  ; true
    // 0x9492b0: StoreField: r0->field_47 = r1
    //     0x9492b0: stur            w1, [x0, #0x47]
    // 0x9492b4: d0 = 1.000000
    //     0x9492b4: fmov            d0, #1.00000000
    // 0x9492b8: StoreField: r0->field_4b = d0
    //     0x9492b8: stur            d0, [x0, #0x4b]
    // 0x9492bc: mov             x1, x0
    // 0x9492c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9492c0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9492c4: r0 = ScrollController()
    //     0x9492c4: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x9492c8: ldur            x0, [fp, #-0x10]
    // 0x9492cc: ldur            x1, [fp, #-8]
    // 0x9492d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x9492d0: stur            w0, [x1, #0x17]
    //     0x9492d4: ldurb           w16, [x1, #-1]
    //     0x9492d8: ldurb           w17, [x0, #-1]
    //     0x9492dc: and             x16, x17, x16, lsr #2
    //     0x9492e0: tst             x16, HEAP, lsr #32
    //     0x9492e4: b.eq            #0x9492ec
    //     0x9492e8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x9492ec: r0 = Null
    //     0x9492ec: mov             x0, NULL
    // 0x9492f0: LeaveFrame
    //     0x9492f0: mov             SP, fp
    //     0x9492f4: ldp             fp, lr, [SP], #0x10
    // 0x9492f8: ret
    //     0x9492f8: ret             
    // 0x9492fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9492fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949300: b               #0x949254
  }
  _ build(/* No info */) {
    // ** addr: 0xbe8154, size: 0x534
    // 0xbe8154: EnterFrame
    //     0xbe8154: stp             fp, lr, [SP, #-0x10]!
    //     0xbe8158: mov             fp, SP
    // 0xbe815c: AllocStack(0x88)
    //     0xbe815c: sub             SP, SP, #0x88
    // 0xbe8160: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbe8160: mov             x0, x1
    //     0xbe8164: stur            x1, [fp, #-8]
    //     0xbe8168: mov             x1, x2
    //     0xbe816c: stur            x2, [fp, #-0x10]
    // 0xbe8170: CheckStackOverflow
    //     0xbe8170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe8174: cmp             SP, x16
    //     0xbe8178: b.ls            #0xbe8648
    // 0xbe817c: r1 = 1
    //     0xbe817c: movz            x1, #0x1
    // 0xbe8180: r0 = AllocateContext()
    //     0xbe8180: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe8184: mov             x2, x0
    // 0xbe8188: ldur            x0, [fp, #-8]
    // 0xbe818c: stur            x2, [fp, #-0x20]
    // 0xbe8190: StoreField: r2->field_f = r0
    //     0xbe8190: stur            w0, [x2, #0xf]
    // 0xbe8194: LoadField: r1 = r0->field_b
    //     0xbe8194: ldur            w1, [x0, #0xb]
    // 0xbe8198: DecompressPointer r1
    //     0xbe8198: add             x1, x1, HEAP, lsl #32
    // 0xbe819c: cmp             w1, NULL
    // 0xbe81a0: b.eq            #0xbe8650
    // 0xbe81a4: LoadField: r3 = r1->field_b
    //     0xbe81a4: ldur            w3, [x1, #0xb]
    // 0xbe81a8: DecompressPointer r3
    //     0xbe81a8: add             x3, x3, HEAP, lsl #32
    // 0xbe81ac: cmp             w3, NULL
    // 0xbe81b0: b.ne            #0xbe81bc
    // 0xbe81b4: r3 = Null
    //     0xbe81b4: mov             x3, NULL
    // 0xbe81b8: b               #0xbe81d0
    // 0xbe81bc: LoadField: r4 = r3->field_b
    //     0xbe81bc: ldur            w4, [x3, #0xb]
    // 0xbe81c0: cbz             w4, #0xbe81cc
    // 0xbe81c4: r3 = false
    //     0xbe81c4: add             x3, NULL, #0x30  ; false
    // 0xbe81c8: b               #0xbe81d0
    // 0xbe81cc: r3 = true
    //     0xbe81cc: add             x3, NULL, #0x20  ; true
    // 0xbe81d0: cmp             w3, NULL
    // 0xbe81d4: b.eq            #0xbe81dc
    // 0xbe81d8: tbnz            w3, #4, #0xbe81ec
    // 0xbe81dc: r0 = Instance_SizedBox
    //     0xbe81dc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe81e0: LeaveFrame
    //     0xbe81e0: mov             SP, fp
    //     0xbe81e4: ldp             fp, lr, [SP], #0x10
    // 0xbe81e8: ret
    //     0xbe81e8: ret             
    // 0xbe81ec: LoadField: r3 = r1->field_1b
    //     0xbe81ec: ldur            w3, [x1, #0x1b]
    // 0xbe81f0: DecompressPointer r3
    //     0xbe81f0: add             x3, x3, HEAP, lsl #32
    // 0xbe81f4: LoadField: r1 = r3->field_7
    //     0xbe81f4: ldur            w1, [x3, #7]
    // 0xbe81f8: DecompressPointer r1
    //     0xbe81f8: add             x1, x1, HEAP, lsl #32
    // 0xbe81fc: cmp             w1, NULL
    // 0xbe8200: b.ne            #0xbe820c
    // 0xbe8204: r1 = Instance_TitleAlignment
    //     0xbe8204: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbe8208: ldr             x1, [x1, #0x518]
    // 0xbe820c: r16 = Instance_TitleAlignment
    //     0xbe820c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbe8210: ldr             x16, [x16, #0x520]
    // 0xbe8214: cmp             w1, w16
    // 0xbe8218: b.ne            #0xbe8228
    // 0xbe821c: r3 = Instance_CrossAxisAlignment
    //     0xbe821c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xbe8220: ldr             x3, [x3, #0xc68]
    // 0xbe8224: b               #0xbe824c
    // 0xbe8228: r16 = Instance_TitleAlignment
    //     0xbe8228: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbe822c: ldr             x16, [x16, #0x518]
    // 0xbe8230: cmp             w1, w16
    // 0xbe8234: b.ne            #0xbe8244
    // 0xbe8238: r3 = Instance_CrossAxisAlignment
    //     0xbe8238: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe823c: ldr             x3, [x3, #0x890]
    // 0xbe8240: b               #0xbe824c
    // 0xbe8244: r3 = Instance_CrossAxisAlignment
    //     0xbe8244: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe8248: ldr             x3, [x3, #0xa18]
    // 0xbe824c: mov             x1, x0
    // 0xbe8250: stur            x3, [fp, #-0x18]
    // 0xbe8254: r0 = _buildHeader()
    //     0xbe8254: bl              #0xbe8ec4  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildHeader
    // 0xbe8258: ldur            x1, [fp, #-8]
    // 0xbe825c: stur            x0, [fp, #-0x28]
    // 0xbe8260: r0 = _buildBumperCoupon()
    //     0xbe8260: bl              #0xbe8688  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildBumperCoupon
    // 0xbe8264: ldur            x1, [fp, #-0x10]
    // 0xbe8268: stur            x0, [fp, #-0x30]
    // 0xbe826c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbe826c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbe8270: r0 = _of()
    //     0xbe8270: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbe8274: LoadField: r1 = r0->field_7
    //     0xbe8274: ldur            w1, [x0, #7]
    // 0xbe8278: DecompressPointer r1
    //     0xbe8278: add             x1, x1, HEAP, lsl #32
    // 0xbe827c: LoadField: d0 = r1->field_f
    //     0xbe827c: ldur            d0, [x1, #0xf]
    // 0xbe8280: d1 = 0.445000
    //     0xbe8280: add             x17, PP, #0x53, lsl #12  ; [pp+0x53678] IMM: double(0.445) from 0x3fdc7ae147ae147b
    //     0xbe8284: ldr             d1, [x17, #0x678]
    // 0xbe8288: fmul            d2, d0, d1
    // 0xbe828c: ldur            x1, [fp, #-0x10]
    // 0xbe8290: stur            d2, [fp, #-0x68]
    // 0xbe8294: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbe8294: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbe8298: r0 = _of()
    //     0xbe8298: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbe829c: LoadField: r1 = r0->field_7
    //     0xbe829c: ldur            w1, [x0, #7]
    // 0xbe82a0: DecompressPointer r1
    //     0xbe82a0: add             x1, x1, HEAP, lsl #32
    // 0xbe82a4: LoadField: d0 = r1->field_7
    //     0xbe82a4: ldur            d0, [x1, #7]
    // 0xbe82a8: ldur            x1, [fp, #-8]
    // 0xbe82ac: stur            d0, [fp, #-0x70]
    // 0xbe82b0: r0 = totalPages()
    //     0xbe82b0: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xbe82b4: mov             x2, x0
    // 0xbe82b8: ldur            x3, [fp, #-8]
    // 0xbe82bc: LoadField: r4 = r3->field_13
    //     0xbe82bc: ldur            w4, [x3, #0x13]
    // 0xbe82c0: DecompressPointer r4
    //     0xbe82c0: add             x4, x4, HEAP, lsl #32
    // 0xbe82c4: r16 = Sentinel
    //     0xbe82c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe82c8: cmp             w4, w16
    // 0xbe82cc: b.eq            #0xbe8654
    // 0xbe82d0: stur            x4, [fp, #-0x40]
    // 0xbe82d4: r0 = BoxInt64Instr(r2)
    //     0xbe82d4: sbfiz           x0, x2, #1, #0x1f
    //     0xbe82d8: cmp             x2, x0, asr #1
    //     0xbe82dc: b.eq            #0xbe82e8
    //     0xbe82e0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe82e4: stur            x2, [x0, #7]
    // 0xbe82e8: ldur            x2, [fp, #-0x20]
    // 0xbe82ec: r1 = Function '<anonymous closure>':.
    //     0xbe82ec: add             x1, PP, #0x53, lsl #12  ; [pp+0x53680] AnonymousClosure: (0xbec1c8), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xbe8154)
    //     0xbe82f0: ldr             x1, [x1, #0x680]
    // 0xbe82f4: stur            x0, [fp, #-0x38]
    // 0xbe82f8: r0 = AllocateClosure()
    //     0xbe82f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe82fc: ldur            x2, [fp, #-0x20]
    // 0xbe8300: r1 = Function '<anonymous closure>':.
    //     0xbe8300: add             x1, PP, #0x53, lsl #12  ; [pp+0x53688] AnonymousClosure: (0xbe95a4), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xbe8154)
    //     0xbe8304: ldr             x1, [x1, #0x688]
    // 0xbe8308: stur            x0, [fp, #-0x20]
    // 0xbe830c: r0 = AllocateClosure()
    //     0xbe830c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe8310: stur            x0, [fp, #-0x48]
    // 0xbe8314: r0 = PageView()
    //     0xbe8314: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbe8318: stur            x0, [fp, #-0x50]
    // 0xbe831c: r16 = Instance_BouncingScrollPhysics
    //     0xbe831c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbe8320: ldr             x16, [x16, #0x890]
    // 0xbe8324: r30 = false
    //     0xbe8324: add             lr, NULL, #0x30  ; false
    // 0xbe8328: stp             lr, x16, [SP, #8]
    // 0xbe832c: ldur            x16, [fp, #-0x40]
    // 0xbe8330: str             x16, [SP]
    // 0xbe8334: mov             x1, x0
    // 0xbe8338: ldur            x2, [fp, #-0x48]
    // 0xbe833c: ldur            x3, [fp, #-0x38]
    // 0xbe8340: ldur            x5, [fp, #-0x20]
    // 0xbe8344: r4 = const [0, 0x7, 0x3, 0x4, controller, 0x6, padEnds, 0x5, physics, 0x4, null]
    //     0xbe8344: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d20] List(11) [0, 0x7, 0x3, 0x4, "controller", 0x6, "padEnds", 0x5, "physics", 0x4, Null]
    //     0xbe8348: ldr             x4, [x4, #0xd20]
    // 0xbe834c: r0 = PageView.builder()
    //     0xbe834c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbe8350: ldur            d0, [fp, #-0x70]
    // 0xbe8354: r0 = inline_Allocate_Double()
    //     0xbe8354: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbe8358: add             x0, x0, #0x10
    //     0xbe835c: cmp             x1, x0
    //     0xbe8360: b.ls            #0xbe8660
    //     0xbe8364: str             x0, [THR, #0x50]  ; THR::top
    //     0xbe8368: sub             x0, x0, #0xf
    //     0xbe836c: movz            x1, #0xe15c
    //     0xbe8370: movk            x1, #0x3, lsl #16
    //     0xbe8374: stur            x1, [x0, #-1]
    // 0xbe8378: StoreField: r0->field_7 = d0
    //     0xbe8378: stur            d0, [x0, #7]
    // 0xbe837c: stur            x0, [fp, #-0x20]
    // 0xbe8380: r0 = SizedBox()
    //     0xbe8380: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbe8384: mov             x3, x0
    // 0xbe8388: ldur            x0, [fp, #-0x20]
    // 0xbe838c: stur            x3, [fp, #-0x38]
    // 0xbe8390: StoreField: r3->field_f = r0
    //     0xbe8390: stur            w0, [x3, #0xf]
    // 0xbe8394: ldur            d0, [fp, #-0x68]
    // 0xbe8398: r0 = inline_Allocate_Double()
    //     0xbe8398: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbe839c: add             x0, x0, #0x10
    //     0xbe83a0: cmp             x1, x0
    //     0xbe83a4: b.ls            #0xbe8670
    //     0xbe83a8: str             x0, [THR, #0x50]  ; THR::top
    //     0xbe83ac: sub             x0, x0, #0xf
    //     0xbe83b0: movz            x1, #0xe15c
    //     0xbe83b4: movk            x1, #0x3, lsl #16
    //     0xbe83b8: stur            x1, [x0, #-1]
    // 0xbe83bc: StoreField: r0->field_7 = d0
    //     0xbe83bc: stur            d0, [x0, #7]
    // 0xbe83c0: StoreField: r3->field_13 = r0
    //     0xbe83c0: stur            w0, [x3, #0x13]
    // 0xbe83c4: ldur            x0, [fp, #-0x50]
    // 0xbe83c8: StoreField: r3->field_b = r0
    //     0xbe83c8: stur            w0, [x3, #0xb]
    // 0xbe83cc: r1 = Null
    //     0xbe83cc: mov             x1, NULL
    // 0xbe83d0: r2 = 6
    //     0xbe83d0: movz            x2, #0x6
    // 0xbe83d4: r0 = AllocateArray()
    //     0xbe83d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe83d8: mov             x2, x0
    // 0xbe83dc: ldur            x0, [fp, #-0x28]
    // 0xbe83e0: stur            x2, [fp, #-0x20]
    // 0xbe83e4: StoreField: r2->field_f = r0
    //     0xbe83e4: stur            w0, [x2, #0xf]
    // 0xbe83e8: ldur            x0, [fp, #-0x30]
    // 0xbe83ec: StoreField: r2->field_13 = r0
    //     0xbe83ec: stur            w0, [x2, #0x13]
    // 0xbe83f0: ldur            x0, [fp, #-0x38]
    // 0xbe83f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe83f4: stur            w0, [x2, #0x17]
    // 0xbe83f8: r1 = <Widget>
    //     0xbe83f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe83fc: r0 = AllocateGrowableArray()
    //     0xbe83fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe8400: mov             x2, x0
    // 0xbe8404: ldur            x0, [fp, #-0x20]
    // 0xbe8408: stur            x2, [fp, #-0x28]
    // 0xbe840c: StoreField: r2->field_f = r0
    //     0xbe840c: stur            w0, [x2, #0xf]
    // 0xbe8410: r0 = 6
    //     0xbe8410: movz            x0, #0x6
    // 0xbe8414: StoreField: r2->field_b = r0
    //     0xbe8414: stur            w0, [x2, #0xb]
    // 0xbe8418: ldur            x1, [fp, #-8]
    // 0xbe841c: r0 = totalPages()
    //     0xbe841c: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xbe8420: cmp             x0, #1
    // 0xbe8424: b.le            #0xbe85c4
    // 0xbe8428: ldur            x2, [fp, #-8]
    // 0xbe842c: ldur            x0, [fp, #-0x28]
    // 0xbe8430: mov             x1, x2
    // 0xbe8434: r0 = totalPages()
    //     0xbe8434: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xbe8438: mov             x2, x0
    // 0xbe843c: ldur            x0, [fp, #-8]
    // 0xbe8440: stur            x2, [fp, #-0x60]
    // 0xbe8444: LoadField: r3 = r0->field_1b
    //     0xbe8444: ldur            x3, [x0, #0x1b]
    // 0xbe8448: ldur            x1, [fp, #-0x10]
    // 0xbe844c: stur            x3, [fp, #-0x58]
    // 0xbe8450: r0 = of()
    //     0xbe8450: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe8454: LoadField: r1 = r0->field_5b
    //     0xbe8454: ldur            w1, [x0, #0x5b]
    // 0xbe8458: DecompressPointer r1
    //     0xbe8458: add             x1, x1, HEAP, lsl #32
    // 0xbe845c: stur            x1, [fp, #-8]
    // 0xbe8460: r0 = CarouselIndicator()
    //     0xbe8460: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xbe8464: mov             x3, x0
    // 0xbe8468: ldur            x0, [fp, #-0x60]
    // 0xbe846c: stur            x3, [fp, #-0x10]
    // 0xbe8470: StoreField: r3->field_b = r0
    //     0xbe8470: stur            x0, [x3, #0xb]
    // 0xbe8474: ldur            x0, [fp, #-0x58]
    // 0xbe8478: StoreField: r3->field_13 = r0
    //     0xbe8478: stur            x0, [x3, #0x13]
    // 0xbe847c: ldur            x0, [fp, #-8]
    // 0xbe8480: StoreField: r3->field_1b = r0
    //     0xbe8480: stur            w0, [x3, #0x1b]
    // 0xbe8484: r0 = Instance_Color
    //     0xbe8484: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xbe8488: ldr             x0, [x0, #0x90]
    // 0xbe848c: StoreField: r3->field_1f = r0
    //     0xbe848c: stur            w0, [x3, #0x1f]
    // 0xbe8490: r1 = Null
    //     0xbe8490: mov             x1, NULL
    // 0xbe8494: r2 = 2
    //     0xbe8494: movz            x2, #0x2
    // 0xbe8498: r0 = AllocateArray()
    //     0xbe8498: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe849c: mov             x2, x0
    // 0xbe84a0: ldur            x0, [fp, #-0x10]
    // 0xbe84a4: stur            x2, [fp, #-8]
    // 0xbe84a8: StoreField: r2->field_f = r0
    //     0xbe84a8: stur            w0, [x2, #0xf]
    // 0xbe84ac: r1 = <Widget>
    //     0xbe84ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe84b0: r0 = AllocateGrowableArray()
    //     0xbe84b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe84b4: mov             x1, x0
    // 0xbe84b8: ldur            x0, [fp, #-8]
    // 0xbe84bc: stur            x1, [fp, #-0x10]
    // 0xbe84c0: StoreField: r1->field_f = r0
    //     0xbe84c0: stur            w0, [x1, #0xf]
    // 0xbe84c4: r0 = 2
    //     0xbe84c4: movz            x0, #0x2
    // 0xbe84c8: StoreField: r1->field_b = r0
    //     0xbe84c8: stur            w0, [x1, #0xb]
    // 0xbe84cc: r0 = Row()
    //     0xbe84cc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe84d0: mov             x1, x0
    // 0xbe84d4: r0 = Instance_Axis
    //     0xbe84d4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe84d8: stur            x1, [fp, #-8]
    // 0xbe84dc: StoreField: r1->field_f = r0
    //     0xbe84dc: stur            w0, [x1, #0xf]
    // 0xbe84e0: r0 = Instance_MainAxisAlignment
    //     0xbe84e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbe84e4: ldr             x0, [x0, #0xab0]
    // 0xbe84e8: StoreField: r1->field_13 = r0
    //     0xbe84e8: stur            w0, [x1, #0x13]
    // 0xbe84ec: r0 = Instance_MainAxisSize
    //     0xbe84ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe84f0: ldr             x0, [x0, #0xa10]
    // 0xbe84f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe84f4: stur            w0, [x1, #0x17]
    // 0xbe84f8: r2 = Instance_CrossAxisAlignment
    //     0xbe84f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe84fc: ldr             x2, [x2, #0xa18]
    // 0xbe8500: StoreField: r1->field_1b = r2
    //     0xbe8500: stur            w2, [x1, #0x1b]
    // 0xbe8504: r2 = Instance_VerticalDirection
    //     0xbe8504: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe8508: ldr             x2, [x2, #0xa20]
    // 0xbe850c: StoreField: r1->field_23 = r2
    //     0xbe850c: stur            w2, [x1, #0x23]
    // 0xbe8510: r3 = Instance_Clip
    //     0xbe8510: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe8514: ldr             x3, [x3, #0x38]
    // 0xbe8518: StoreField: r1->field_2b = r3
    //     0xbe8518: stur            w3, [x1, #0x2b]
    // 0xbe851c: StoreField: r1->field_2f = rZR
    //     0xbe851c: stur            xzr, [x1, #0x2f]
    // 0xbe8520: ldur            x4, [fp, #-0x10]
    // 0xbe8524: StoreField: r1->field_b = r4
    //     0xbe8524: stur            w4, [x1, #0xb]
    // 0xbe8528: r0 = Padding()
    //     0xbe8528: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe852c: mov             x2, x0
    // 0xbe8530: r0 = Instance_EdgeInsets
    //     0xbe8530: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbe8534: ldr             x0, [x0, #0x668]
    // 0xbe8538: stur            x2, [fp, #-0x10]
    // 0xbe853c: StoreField: r2->field_f = r0
    //     0xbe853c: stur            w0, [x2, #0xf]
    // 0xbe8540: ldur            x0, [fp, #-8]
    // 0xbe8544: StoreField: r2->field_b = r0
    //     0xbe8544: stur            w0, [x2, #0xb]
    // 0xbe8548: ldur            x0, [fp, #-0x28]
    // 0xbe854c: LoadField: r1 = r0->field_b
    //     0xbe854c: ldur            w1, [x0, #0xb]
    // 0xbe8550: LoadField: r3 = r0->field_f
    //     0xbe8550: ldur            w3, [x0, #0xf]
    // 0xbe8554: DecompressPointer r3
    //     0xbe8554: add             x3, x3, HEAP, lsl #32
    // 0xbe8558: LoadField: r4 = r3->field_b
    //     0xbe8558: ldur            w4, [x3, #0xb]
    // 0xbe855c: r3 = LoadInt32Instr(r1)
    //     0xbe855c: sbfx            x3, x1, #1, #0x1f
    // 0xbe8560: stur            x3, [fp, #-0x58]
    // 0xbe8564: r1 = LoadInt32Instr(r4)
    //     0xbe8564: sbfx            x1, x4, #1, #0x1f
    // 0xbe8568: cmp             x3, x1
    // 0xbe856c: b.ne            #0xbe8578
    // 0xbe8570: mov             x1, x0
    // 0xbe8574: r0 = _growToNextCapacity()
    //     0xbe8574: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe8578: ldur            x2, [fp, #-0x28]
    // 0xbe857c: ldur            x3, [fp, #-0x58]
    // 0xbe8580: add             x0, x3, #1
    // 0xbe8584: lsl             x1, x0, #1
    // 0xbe8588: StoreField: r2->field_b = r1
    //     0xbe8588: stur            w1, [x2, #0xb]
    // 0xbe858c: LoadField: r1 = r2->field_f
    //     0xbe858c: ldur            w1, [x2, #0xf]
    // 0xbe8590: DecompressPointer r1
    //     0xbe8590: add             x1, x1, HEAP, lsl #32
    // 0xbe8594: ldur            x0, [fp, #-0x10]
    // 0xbe8598: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe8598: add             x25, x1, x3, lsl #2
    //     0xbe859c: add             x25, x25, #0xf
    //     0xbe85a0: str             w0, [x25]
    //     0xbe85a4: tbz             w0, #0, #0xbe85c0
    //     0xbe85a8: ldurb           w16, [x1, #-1]
    //     0xbe85ac: ldurb           w17, [x0, #-1]
    //     0xbe85b0: and             x16, x17, x16, lsr #2
    //     0xbe85b4: tst             x16, HEAP, lsr #32
    //     0xbe85b8: b.eq            #0xbe85c0
    //     0xbe85bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe85c0: b               #0xbe85c8
    // 0xbe85c4: ldur            x2, [fp, #-0x28]
    // 0xbe85c8: ldur            x0, [fp, #-0x18]
    // 0xbe85cc: r0 = Column()
    //     0xbe85cc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbe85d0: mov             x1, x0
    // 0xbe85d4: r0 = Instance_Axis
    //     0xbe85d4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe85d8: stur            x1, [fp, #-8]
    // 0xbe85dc: StoreField: r1->field_f = r0
    //     0xbe85dc: stur            w0, [x1, #0xf]
    // 0xbe85e0: r0 = Instance_MainAxisAlignment
    //     0xbe85e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe85e4: ldr             x0, [x0, #0xa08]
    // 0xbe85e8: StoreField: r1->field_13 = r0
    //     0xbe85e8: stur            w0, [x1, #0x13]
    // 0xbe85ec: r0 = Instance_MainAxisSize
    //     0xbe85ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe85f0: ldr             x0, [x0, #0xa10]
    // 0xbe85f4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe85f4: stur            w0, [x1, #0x17]
    // 0xbe85f8: ldur            x0, [fp, #-0x18]
    // 0xbe85fc: StoreField: r1->field_1b = r0
    //     0xbe85fc: stur            w0, [x1, #0x1b]
    // 0xbe8600: r0 = Instance_VerticalDirection
    //     0xbe8600: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe8604: ldr             x0, [x0, #0xa20]
    // 0xbe8608: StoreField: r1->field_23 = r0
    //     0xbe8608: stur            w0, [x1, #0x23]
    // 0xbe860c: r0 = Instance_Clip
    //     0xbe860c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe8610: ldr             x0, [x0, #0x38]
    // 0xbe8614: StoreField: r1->field_2b = r0
    //     0xbe8614: stur            w0, [x1, #0x2b]
    // 0xbe8618: StoreField: r1->field_2f = rZR
    //     0xbe8618: stur            xzr, [x1, #0x2f]
    // 0xbe861c: ldur            x0, [fp, #-0x28]
    // 0xbe8620: StoreField: r1->field_b = r0
    //     0xbe8620: stur            w0, [x1, #0xb]
    // 0xbe8624: r0 = Padding()
    //     0xbe8624: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe8628: r1 = Instance_EdgeInsets
    //     0xbe8628: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xbe862c: ldr             x1, [x1, #0x110]
    // 0xbe8630: StoreField: r0->field_f = r1
    //     0xbe8630: stur            w1, [x0, #0xf]
    // 0xbe8634: ldur            x1, [fp, #-8]
    // 0xbe8638: StoreField: r0->field_b = r1
    //     0xbe8638: stur            w1, [x0, #0xb]
    // 0xbe863c: LeaveFrame
    //     0xbe863c: mov             SP, fp
    //     0xbe8640: ldp             fp, lr, [SP], #0x10
    // 0xbe8644: ret
    //     0xbe8644: ret             
    // 0xbe8648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe8648: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe864c: b               #0xbe817c
    // 0xbe8650: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8650: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8654: r9 = _pageController
    //     0xbe8654: add             x9, PP, #0x53, lsl #12  ; [pp+0x53690] Field <_ProductGroupCarouselItemViewState@1706026639._pageController@1706026639>: late (offset: 0x14)
    //     0xbe8658: ldr             x9, [x9, #0x690]
    // 0xbe865c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbe865c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbe8660: SaveReg d0
    //     0xbe8660: str             q0, [SP, #-0x10]!
    // 0xbe8664: r0 = AllocateDouble()
    //     0xbe8664: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbe8668: RestoreReg d0
    //     0xbe8668: ldr             q0, [SP], #0x10
    // 0xbe866c: b               #0xbe8378
    // 0xbe8670: SaveReg d0
    //     0xbe8670: str             q0, [SP, #-0x10]!
    // 0xbe8674: SaveReg r3
    //     0xbe8674: str             x3, [SP, #-8]!
    // 0xbe8678: r0 = AllocateDouble()
    //     0xbe8678: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbe867c: RestoreReg r3
    //     0xbe867c: ldr             x3, [SP], #8
    // 0xbe8680: RestoreReg d0
    //     0xbe8680: ldr             q0, [SP], #0x10
    // 0xbe8684: b               #0xbe83bc
  }
  _ _buildBumperCoupon(/* No info */) {
    // ** addr: 0xbe8688, size: 0x83c
    // 0xbe8688: EnterFrame
    //     0xbe8688: stp             fp, lr, [SP, #-0x10]!
    //     0xbe868c: mov             fp, SP
    // 0xbe8690: AllocStack(0x68)
    //     0xbe8690: sub             SP, SP, #0x68
    // 0xbe8694: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x28 */)
    //     0xbe8694: stur            x1, [fp, #-0x28]
    // 0xbe8698: CheckStackOverflow
    //     0xbe8698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe869c: cmp             SP, x16
    //     0xbe86a0: b.ls            #0xbe8ea4
    // 0xbe86a4: LoadField: r0 = r1->field_b
    //     0xbe86a4: ldur            w0, [x1, #0xb]
    // 0xbe86a8: DecompressPointer r0
    //     0xbe86a8: add             x0, x0, HEAP, lsl #32
    // 0xbe86ac: cmp             w0, NULL
    // 0xbe86b0: b.eq            #0xbe8eac
    // 0xbe86b4: LoadField: r2 = r0->field_33
    //     0xbe86b4: ldur            w2, [x0, #0x33]
    // 0xbe86b8: DecompressPointer r2
    //     0xbe86b8: add             x2, x2, HEAP, lsl #32
    // 0xbe86bc: cmp             w2, NULL
    // 0xbe86c0: b.eq            #0xbe86d4
    // 0xbe86c4: LoadField: r0 = r2->field_f
    //     0xbe86c4: ldur            w0, [x2, #0xf]
    // 0xbe86c8: DecompressPointer r0
    //     0xbe86c8: add             x0, x0, HEAP, lsl #32
    // 0xbe86cc: cmp             w0, NULL
    // 0xbe86d0: b.ne            #0xbe86e4
    // 0xbe86d4: r0 = Instance_SizedBox
    //     0xbe86d4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbe86d8: LeaveFrame
    //     0xbe86d8: mov             SP, fp
    //     0xbe86dc: ldp             fp, lr, [SP], #0x10
    // 0xbe86e0: ret
    //     0xbe86e0: ret             
    // 0xbe86e4: LoadField: r0 = r2->field_13
    //     0xbe86e4: ldur            w0, [x2, #0x13]
    // 0xbe86e8: DecompressPointer r0
    //     0xbe86e8: add             x0, x0, HEAP, lsl #32
    // 0xbe86ec: stur            x0, [fp, #-0x20]
    // 0xbe86f0: cmp             w0, NULL
    // 0xbe86f4: b.ne            #0xbe8700
    // 0xbe86f8: r2 = Null
    //     0xbe86f8: mov             x2, NULL
    // 0xbe86fc: b               #0xbe8708
    // 0xbe8700: LoadField: r2 = r0->field_7
    //     0xbe8700: ldur            w2, [x0, #7]
    // 0xbe8704: DecompressPointer r2
    //     0xbe8704: add             x2, x2, HEAP, lsl #32
    // 0xbe8708: cmp             w2, NULL
    // 0xbe870c: b.ne            #0xbe8718
    // 0xbe8710: r2 = 0
    //     0xbe8710: movz            x2, #0
    // 0xbe8714: b               #0xbe8728
    // 0xbe8718: r3 = LoadInt32Instr(r2)
    //     0xbe8718: sbfx            x3, x2, #1, #0x1f
    //     0xbe871c: tbz             w2, #0, #0xbe8724
    //     0xbe8720: ldur            x3, [x2, #7]
    // 0xbe8724: mov             x2, x3
    // 0xbe8728: stur            x2, [fp, #-0x18]
    // 0xbe872c: cmp             w0, NULL
    // 0xbe8730: b.ne            #0xbe873c
    // 0xbe8734: r3 = Null
    //     0xbe8734: mov             x3, NULL
    // 0xbe8738: b               #0xbe8744
    // 0xbe873c: LoadField: r3 = r0->field_b
    //     0xbe873c: ldur            w3, [x0, #0xb]
    // 0xbe8740: DecompressPointer r3
    //     0xbe8740: add             x3, x3, HEAP, lsl #32
    // 0xbe8744: cmp             w3, NULL
    // 0xbe8748: b.ne            #0xbe8754
    // 0xbe874c: r3 = 0
    //     0xbe874c: movz            x3, #0
    // 0xbe8750: b               #0xbe8764
    // 0xbe8754: r4 = LoadInt32Instr(r3)
    //     0xbe8754: sbfx            x4, x3, #1, #0x1f
    //     0xbe8758: tbz             w3, #0, #0xbe8760
    //     0xbe875c: ldur            x4, [x3, #7]
    // 0xbe8760: mov             x3, x4
    // 0xbe8764: stur            x3, [fp, #-0x10]
    // 0xbe8768: cmp             w0, NULL
    // 0xbe876c: b.ne            #0xbe8778
    // 0xbe8770: r4 = Null
    //     0xbe8770: mov             x4, NULL
    // 0xbe8774: b               #0xbe8780
    // 0xbe8778: LoadField: r4 = r0->field_f
    //     0xbe8778: ldur            w4, [x0, #0xf]
    // 0xbe877c: DecompressPointer r4
    //     0xbe877c: add             x4, x4, HEAP, lsl #32
    // 0xbe8780: cmp             w4, NULL
    // 0xbe8784: b.ne            #0xbe8790
    // 0xbe8788: r4 = 0
    //     0xbe8788: movz            x4, #0
    // 0xbe878c: b               #0xbe87a0
    // 0xbe8790: r5 = LoadInt32Instr(r4)
    //     0xbe8790: sbfx            x5, x4, #1, #0x1f
    //     0xbe8794: tbz             w4, #0, #0xbe879c
    //     0xbe8798: ldur            x5, [x4, #7]
    // 0xbe879c: mov             x4, x5
    // 0xbe87a0: stur            x4, [fp, #-8]
    // 0xbe87a4: r0 = Color()
    //     0xbe87a4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe87a8: mov             x1, x0
    // 0xbe87ac: r0 = Instance_ColorSpace
    //     0xbe87ac: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe87b0: stur            x1, [fp, #-0x30]
    // 0xbe87b4: StoreField: r1->field_27 = r0
    //     0xbe87b4: stur            w0, [x1, #0x27]
    // 0xbe87b8: d0 = 1.000000
    //     0xbe87b8: fmov            d0, #1.00000000
    // 0xbe87bc: StoreField: r1->field_7 = d0
    //     0xbe87bc: stur            d0, [x1, #7]
    // 0xbe87c0: ldur            x2, [fp, #-0x18]
    // 0xbe87c4: ubfx            x2, x2, #0, #0x20
    // 0xbe87c8: and             w3, w2, #0xff
    // 0xbe87cc: ubfx            x3, x3, #0, #0x20
    // 0xbe87d0: scvtf           d1, x3
    // 0xbe87d4: d2 = 255.000000
    //     0xbe87d4: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe87d8: fdiv            d3, d1, d2
    // 0xbe87dc: StoreField: r1->field_f = d3
    //     0xbe87dc: stur            d3, [x1, #0xf]
    // 0xbe87e0: ldur            x2, [fp, #-0x10]
    // 0xbe87e4: ubfx            x2, x2, #0, #0x20
    // 0xbe87e8: and             w3, w2, #0xff
    // 0xbe87ec: ubfx            x3, x3, #0, #0x20
    // 0xbe87f0: scvtf           d1, x3
    // 0xbe87f4: fdiv            d3, d1, d2
    // 0xbe87f8: ArrayStore: r1[0] = d3  ; List_8
    //     0xbe87f8: stur            d3, [x1, #0x17]
    // 0xbe87fc: ldur            x2, [fp, #-8]
    // 0xbe8800: ubfx            x2, x2, #0, #0x20
    // 0xbe8804: and             w3, w2, #0xff
    // 0xbe8808: ubfx            x3, x3, #0, #0x20
    // 0xbe880c: scvtf           d1, x3
    // 0xbe8810: fdiv            d3, d1, d2
    // 0xbe8814: StoreField: r1->field_1f = d3
    //     0xbe8814: stur            d3, [x1, #0x1f]
    // 0xbe8818: ldur            x2, [fp, #-0x20]
    // 0xbe881c: cmp             w2, NULL
    // 0xbe8820: b.ne            #0xbe882c
    // 0xbe8824: r3 = Null
    //     0xbe8824: mov             x3, NULL
    // 0xbe8828: b               #0xbe8834
    // 0xbe882c: LoadField: r3 = r2->field_7
    //     0xbe882c: ldur            w3, [x2, #7]
    // 0xbe8830: DecompressPointer r3
    //     0xbe8830: add             x3, x3, HEAP, lsl #32
    // 0xbe8834: cmp             w3, NULL
    // 0xbe8838: b.ne            #0xbe8844
    // 0xbe883c: r3 = 0
    //     0xbe883c: movz            x3, #0
    // 0xbe8840: b               #0xbe8854
    // 0xbe8844: r4 = LoadInt32Instr(r3)
    //     0xbe8844: sbfx            x4, x3, #1, #0x1f
    //     0xbe8848: tbz             w3, #0, #0xbe8850
    //     0xbe884c: ldur            x4, [x3, #7]
    // 0xbe8850: mov             x3, x4
    // 0xbe8854: stur            x3, [fp, #-0x18]
    // 0xbe8858: cmp             w2, NULL
    // 0xbe885c: b.ne            #0xbe8868
    // 0xbe8860: r4 = Null
    //     0xbe8860: mov             x4, NULL
    // 0xbe8864: b               #0xbe8870
    // 0xbe8868: LoadField: r4 = r2->field_b
    //     0xbe8868: ldur            w4, [x2, #0xb]
    // 0xbe886c: DecompressPointer r4
    //     0xbe886c: add             x4, x4, HEAP, lsl #32
    // 0xbe8870: cmp             w4, NULL
    // 0xbe8874: b.ne            #0xbe8880
    // 0xbe8878: r4 = 0
    //     0xbe8878: movz            x4, #0
    // 0xbe887c: b               #0xbe8890
    // 0xbe8880: r5 = LoadInt32Instr(r4)
    //     0xbe8880: sbfx            x5, x4, #1, #0x1f
    //     0xbe8884: tbz             w4, #0, #0xbe888c
    //     0xbe8888: ldur            x5, [x4, #7]
    // 0xbe888c: mov             x4, x5
    // 0xbe8890: stur            x4, [fp, #-0x10]
    // 0xbe8894: cmp             w2, NULL
    // 0xbe8898: b.ne            #0xbe88a4
    // 0xbe889c: r2 = Null
    //     0xbe889c: mov             x2, NULL
    // 0xbe88a0: b               #0xbe88b0
    // 0xbe88a4: LoadField: r5 = r2->field_f
    //     0xbe88a4: ldur            w5, [x2, #0xf]
    // 0xbe88a8: DecompressPointer r5
    //     0xbe88a8: add             x5, x5, HEAP, lsl #32
    // 0xbe88ac: mov             x2, x5
    // 0xbe88b0: cmp             w2, NULL
    // 0xbe88b4: b.ne            #0xbe88c0
    // 0xbe88b8: r5 = 0
    //     0xbe88b8: movz            x5, #0
    // 0xbe88bc: b               #0xbe88cc
    // 0xbe88c0: r5 = LoadInt32Instr(r2)
    //     0xbe88c0: sbfx            x5, x2, #1, #0x1f
    //     0xbe88c4: tbz             w2, #0, #0xbe88cc
    //     0xbe88c8: ldur            x5, [x2, #7]
    // 0xbe88cc: ldur            x2, [fp, #-0x28]
    // 0xbe88d0: stur            x5, [fp, #-8]
    // 0xbe88d4: r0 = Color()
    //     0xbe88d4: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe88d8: mov             x3, x0
    // 0xbe88dc: r0 = Instance_ColorSpace
    //     0xbe88dc: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe88e0: stur            x3, [fp, #-0x20]
    // 0xbe88e4: StoreField: r3->field_27 = r0
    //     0xbe88e4: stur            w0, [x3, #0x27]
    // 0xbe88e8: d0 = 0.700000
    //     0xbe88e8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe88ec: ldr             d0, [x17, #0xf48]
    // 0xbe88f0: StoreField: r3->field_7 = d0
    //     0xbe88f0: stur            d0, [x3, #7]
    // 0xbe88f4: ldur            x0, [fp, #-0x18]
    // 0xbe88f8: ubfx            x0, x0, #0, #0x20
    // 0xbe88fc: and             w1, w0, #0xff
    // 0xbe8900: ubfx            x1, x1, #0, #0x20
    // 0xbe8904: scvtf           d0, x1
    // 0xbe8908: d1 = 255.000000
    //     0xbe8908: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe890c: fdiv            d2, d0, d1
    // 0xbe8910: StoreField: r3->field_f = d2
    //     0xbe8910: stur            d2, [x3, #0xf]
    // 0xbe8914: ldur            x0, [fp, #-0x10]
    // 0xbe8918: ubfx            x0, x0, #0, #0x20
    // 0xbe891c: and             w1, w0, #0xff
    // 0xbe8920: ubfx            x1, x1, #0, #0x20
    // 0xbe8924: scvtf           d0, x1
    // 0xbe8928: fdiv            d2, d0, d1
    // 0xbe892c: ArrayStore: r3[0] = d2  ; List_8
    //     0xbe892c: stur            d2, [x3, #0x17]
    // 0xbe8930: ldur            x0, [fp, #-8]
    // 0xbe8934: ubfx            x0, x0, #0, #0x20
    // 0xbe8938: and             w1, w0, #0xff
    // 0xbe893c: ubfx            x1, x1, #0, #0x20
    // 0xbe8940: scvtf           d0, x1
    // 0xbe8944: fdiv            d2, d0, d1
    // 0xbe8948: StoreField: r3->field_1f = d2
    //     0xbe8948: stur            d2, [x3, #0x1f]
    // 0xbe894c: r1 = Null
    //     0xbe894c: mov             x1, NULL
    // 0xbe8950: r2 = 4
    //     0xbe8950: movz            x2, #0x4
    // 0xbe8954: r0 = AllocateArray()
    //     0xbe8954: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe8958: mov             x2, x0
    // 0xbe895c: ldur            x0, [fp, #-0x30]
    // 0xbe8960: stur            x2, [fp, #-0x38]
    // 0xbe8964: StoreField: r2->field_f = r0
    //     0xbe8964: stur            w0, [x2, #0xf]
    // 0xbe8968: ldur            x0, [fp, #-0x20]
    // 0xbe896c: StoreField: r2->field_13 = r0
    //     0xbe896c: stur            w0, [x2, #0x13]
    // 0xbe8970: r1 = <Color>
    //     0xbe8970: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe8974: ldr             x1, [x1, #0xf80]
    // 0xbe8978: r0 = AllocateGrowableArray()
    //     0xbe8978: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe897c: mov             x1, x0
    // 0xbe8980: ldur            x0, [fp, #-0x38]
    // 0xbe8984: stur            x1, [fp, #-0x20]
    // 0xbe8988: StoreField: r1->field_f = r0
    //     0xbe8988: stur            w0, [x1, #0xf]
    // 0xbe898c: r2 = 4
    //     0xbe898c: movz            x2, #0x4
    // 0xbe8990: StoreField: r1->field_b = r2
    //     0xbe8990: stur            w2, [x1, #0xb]
    // 0xbe8994: r0 = LinearGradient()
    //     0xbe8994: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xbe8998: mov             x1, x0
    // 0xbe899c: r0 = Instance_Alignment
    //     0xbe899c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xbe89a0: ldr             x0, [x0, #0xce0]
    // 0xbe89a4: stur            x1, [fp, #-0x30]
    // 0xbe89a8: StoreField: r1->field_13 = r0
    //     0xbe89a8: stur            w0, [x1, #0x13]
    // 0xbe89ac: r0 = Instance_Alignment
    //     0xbe89ac: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xbe89b0: ldr             x0, [x0, #0xce8]
    // 0xbe89b4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe89b4: stur            w0, [x1, #0x17]
    // 0xbe89b8: r0 = Instance_TileMode
    //     0xbe89b8: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xbe89bc: ldr             x0, [x0, #0xcf0]
    // 0xbe89c0: StoreField: r1->field_1b = r0
    //     0xbe89c0: stur            w0, [x1, #0x1b]
    // 0xbe89c4: ldur            x0, [fp, #-0x20]
    // 0xbe89c8: StoreField: r1->field_7 = r0
    //     0xbe89c8: stur            w0, [x1, #7]
    // 0xbe89cc: r0 = BoxDecoration()
    //     0xbe89cc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbe89d0: mov             x2, x0
    // 0xbe89d4: ldur            x0, [fp, #-0x30]
    // 0xbe89d8: stur            x2, [fp, #-0x20]
    // 0xbe89dc: StoreField: r2->field_1b = r0
    //     0xbe89dc: stur            w0, [x2, #0x1b]
    // 0xbe89e0: r0 = Instance_BoxShape
    //     0xbe89e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbe89e4: ldr             x0, [x0, #0x80]
    // 0xbe89e8: StoreField: r2->field_23 = r0
    //     0xbe89e8: stur            w0, [x2, #0x23]
    // 0xbe89ec: ldur            x0, [fp, #-0x28]
    // 0xbe89f0: LoadField: r1 = r0->field_f
    //     0xbe89f0: ldur            w1, [x0, #0xf]
    // 0xbe89f4: DecompressPointer r1
    //     0xbe89f4: add             x1, x1, HEAP, lsl #32
    // 0xbe89f8: cmp             w1, NULL
    // 0xbe89fc: b.eq            #0xbe8eb0
    // 0xbe8a00: r0 = of()
    //     0xbe8a00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe8a04: LoadField: r1 = r0->field_87
    //     0xbe8a04: ldur            w1, [x0, #0x87]
    // 0xbe8a08: DecompressPointer r1
    //     0xbe8a08: add             x1, x1, HEAP, lsl #32
    // 0xbe8a0c: LoadField: r0 = r1->field_7
    //     0xbe8a0c: ldur            w0, [x1, #7]
    // 0xbe8a10: DecompressPointer r0
    //     0xbe8a10: add             x0, x0, HEAP, lsl #32
    // 0xbe8a14: r16 = 16.000000
    //     0xbe8a14: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe8a18: ldr             x16, [x16, #0x188]
    // 0xbe8a1c: r30 = Instance_Color
    //     0xbe8a1c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe8a20: stp             lr, x16, [SP]
    // 0xbe8a24: mov             x1, x0
    // 0xbe8a28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe8a28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe8a2c: ldr             x4, [x4, #0xaa0]
    // 0xbe8a30: r0 = copyWith()
    //     0xbe8a30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe8a34: stur            x0, [fp, #-0x30]
    // 0xbe8a38: r0 = TextSpan()
    //     0xbe8a38: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe8a3c: mov             x2, x0
    // 0xbe8a40: r0 = "BUMPER OFFER\n"
    //     0xbe8a40: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xbe8a44: ldr             x0, [x0, #0x338]
    // 0xbe8a48: stur            x2, [fp, #-0x38]
    // 0xbe8a4c: StoreField: r2->field_b = r0
    //     0xbe8a4c: stur            w0, [x2, #0xb]
    // 0xbe8a50: r0 = Instance__DeferringMouseCursor
    //     0xbe8a50: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe8a54: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe8a54: stur            w0, [x2, #0x17]
    // 0xbe8a58: ldur            x1, [fp, #-0x30]
    // 0xbe8a5c: StoreField: r2->field_7 = r1
    //     0xbe8a5c: stur            w1, [x2, #7]
    // 0xbe8a60: ldur            x3, [fp, #-0x28]
    // 0xbe8a64: LoadField: r1 = r3->field_f
    //     0xbe8a64: ldur            w1, [x3, #0xf]
    // 0xbe8a68: DecompressPointer r1
    //     0xbe8a68: add             x1, x1, HEAP, lsl #32
    // 0xbe8a6c: cmp             w1, NULL
    // 0xbe8a70: b.eq            #0xbe8eb4
    // 0xbe8a74: r0 = of()
    //     0xbe8a74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe8a78: LoadField: r1 = r0->field_87
    //     0xbe8a78: ldur            w1, [x0, #0x87]
    // 0xbe8a7c: DecompressPointer r1
    //     0xbe8a7c: add             x1, x1, HEAP, lsl #32
    // 0xbe8a80: LoadField: r0 = r1->field_2b
    //     0xbe8a80: ldur            w0, [x1, #0x2b]
    // 0xbe8a84: DecompressPointer r0
    //     0xbe8a84: add             x0, x0, HEAP, lsl #32
    // 0xbe8a88: r16 = 12.000000
    //     0xbe8a88: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe8a8c: ldr             x16, [x16, #0x9e8]
    // 0xbe8a90: r30 = Instance_Color
    //     0xbe8a90: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe8a94: stp             lr, x16, [SP]
    // 0xbe8a98: mov             x1, x0
    // 0xbe8a9c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe8a9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe8aa0: ldr             x4, [x4, #0xaa0]
    // 0xbe8aa4: r0 = copyWith()
    //     0xbe8aa4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe8aa8: stur            x0, [fp, #-0x30]
    // 0xbe8aac: r0 = TextSpan()
    //     0xbe8aac: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe8ab0: mov             x3, x0
    // 0xbe8ab4: r0 = "Unlocked from your last order"
    //     0xbe8ab4: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xbe8ab8: ldr             x0, [x0, #0x340]
    // 0xbe8abc: stur            x3, [fp, #-0x40]
    // 0xbe8ac0: StoreField: r3->field_b = r0
    //     0xbe8ac0: stur            w0, [x3, #0xb]
    // 0xbe8ac4: r0 = Instance__DeferringMouseCursor
    //     0xbe8ac4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe8ac8: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe8ac8: stur            w0, [x3, #0x17]
    // 0xbe8acc: ldur            x1, [fp, #-0x30]
    // 0xbe8ad0: StoreField: r3->field_7 = r1
    //     0xbe8ad0: stur            w1, [x3, #7]
    // 0xbe8ad4: r1 = Null
    //     0xbe8ad4: mov             x1, NULL
    // 0xbe8ad8: r2 = 4
    //     0xbe8ad8: movz            x2, #0x4
    // 0xbe8adc: r0 = AllocateArray()
    //     0xbe8adc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe8ae0: mov             x2, x0
    // 0xbe8ae4: ldur            x0, [fp, #-0x38]
    // 0xbe8ae8: stur            x2, [fp, #-0x30]
    // 0xbe8aec: StoreField: r2->field_f = r0
    //     0xbe8aec: stur            w0, [x2, #0xf]
    // 0xbe8af0: ldur            x0, [fp, #-0x40]
    // 0xbe8af4: StoreField: r2->field_13 = r0
    //     0xbe8af4: stur            w0, [x2, #0x13]
    // 0xbe8af8: r1 = <InlineSpan>
    //     0xbe8af8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbe8afc: ldr             x1, [x1, #0xe40]
    // 0xbe8b00: r0 = AllocateGrowableArray()
    //     0xbe8b00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe8b04: mov             x1, x0
    // 0xbe8b08: ldur            x0, [fp, #-0x30]
    // 0xbe8b0c: stur            x1, [fp, #-0x38]
    // 0xbe8b10: StoreField: r1->field_f = r0
    //     0xbe8b10: stur            w0, [x1, #0xf]
    // 0xbe8b14: r2 = 4
    //     0xbe8b14: movz            x2, #0x4
    // 0xbe8b18: StoreField: r1->field_b = r2
    //     0xbe8b18: stur            w2, [x1, #0xb]
    // 0xbe8b1c: r0 = TextSpan()
    //     0xbe8b1c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe8b20: mov             x1, x0
    // 0xbe8b24: ldur            x0, [fp, #-0x38]
    // 0xbe8b28: stur            x1, [fp, #-0x30]
    // 0xbe8b2c: StoreField: r1->field_f = r0
    //     0xbe8b2c: stur            w0, [x1, #0xf]
    // 0xbe8b30: r0 = Instance__DeferringMouseCursor
    //     0xbe8b30: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe8b34: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe8b34: stur            w0, [x1, #0x17]
    // 0xbe8b38: r0 = RichText()
    //     0xbe8b38: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbe8b3c: mov             x1, x0
    // 0xbe8b40: ldur            x2, [fp, #-0x30]
    // 0xbe8b44: stur            x0, [fp, #-0x30]
    // 0xbe8b48: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbe8b48: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbe8b4c: r0 = RichText()
    //     0xbe8b4c: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbe8b50: r1 = Instance_Color
    //     0xbe8b50: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe8b54: d0 = 0.500000
    //     0xbe8b54: fmov            d0, #0.50000000
    // 0xbe8b58: r0 = withOpacity()
    //     0xbe8b58: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe8b5c: stur            x0, [fp, #-0x38]
    // 0xbe8b60: r0 = VerticalDivider()
    //     0xbe8b60: bl              #0x99b78c  ; AllocateVerticalDividerStub -> VerticalDivider (size=0x28)
    // 0xbe8b64: d0 = 1.000000
    //     0xbe8b64: fmov            d0, #1.00000000
    // 0xbe8b68: stur            x0, [fp, #-0x40]
    // 0xbe8b6c: StoreField: r0->field_f = d0
    //     0xbe8b6c: stur            d0, [x0, #0xf]
    // 0xbe8b70: ldur            x1, [fp, #-0x38]
    // 0xbe8b74: StoreField: r0->field_1f = r1
    //     0xbe8b74: stur            w1, [x0, #0x1f]
    // 0xbe8b78: ldur            x3, [fp, #-0x28]
    // 0xbe8b7c: LoadField: r1 = r3->field_b
    //     0xbe8b7c: ldur            w1, [x3, #0xb]
    // 0xbe8b80: DecompressPointer r1
    //     0xbe8b80: add             x1, x1, HEAP, lsl #32
    // 0xbe8b84: cmp             w1, NULL
    // 0xbe8b88: b.eq            #0xbe8eb8
    // 0xbe8b8c: LoadField: r2 = r1->field_33
    //     0xbe8b8c: ldur            w2, [x1, #0x33]
    // 0xbe8b90: DecompressPointer r2
    //     0xbe8b90: add             x2, x2, HEAP, lsl #32
    // 0xbe8b94: cmp             w2, NULL
    // 0xbe8b98: b.ne            #0xbe8ba4
    // 0xbe8b9c: r5 = Null
    //     0xbe8b9c: mov             x5, NULL
    // 0xbe8ba0: b               #0xbe8bb0
    // 0xbe8ba4: LoadField: r1 = r2->field_7
    //     0xbe8ba4: ldur            w1, [x2, #7]
    // 0xbe8ba8: DecompressPointer r1
    //     0xbe8ba8: add             x1, x1, HEAP, lsl #32
    // 0xbe8bac: mov             x5, x1
    // 0xbe8bb0: ldur            x4, [fp, #-0x30]
    // 0xbe8bb4: stur            x5, [fp, #-0x38]
    // 0xbe8bb8: r1 = Null
    //     0xbe8bb8: mov             x1, NULL
    // 0xbe8bbc: r2 = 4
    //     0xbe8bbc: movz            x2, #0x4
    // 0xbe8bc0: r0 = AllocateArray()
    //     0xbe8bc0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe8bc4: mov             x1, x0
    // 0xbe8bc8: ldur            x0, [fp, #-0x38]
    // 0xbe8bcc: StoreField: r1->field_f = r0
    //     0xbe8bcc: stur            w0, [x1, #0xf]
    // 0xbe8bd0: r16 = "\n"
    //     0xbe8bd0: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xbe8bd4: StoreField: r1->field_13 = r16
    //     0xbe8bd4: stur            w16, [x1, #0x13]
    // 0xbe8bd8: str             x1, [SP]
    // 0xbe8bdc: r0 = _interpolate()
    //     0xbe8bdc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbe8be0: mov             x2, x0
    // 0xbe8be4: ldur            x0, [fp, #-0x28]
    // 0xbe8be8: stur            x2, [fp, #-0x38]
    // 0xbe8bec: LoadField: r1 = r0->field_f
    //     0xbe8bec: ldur            w1, [x0, #0xf]
    // 0xbe8bf0: DecompressPointer r1
    //     0xbe8bf0: add             x1, x1, HEAP, lsl #32
    // 0xbe8bf4: cmp             w1, NULL
    // 0xbe8bf8: b.eq            #0xbe8ebc
    // 0xbe8bfc: r0 = of()
    //     0xbe8bfc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe8c00: LoadField: r1 = r0->field_87
    //     0xbe8c00: ldur            w1, [x0, #0x87]
    // 0xbe8c04: DecompressPointer r1
    //     0xbe8c04: add             x1, x1, HEAP, lsl #32
    // 0xbe8c08: LoadField: r0 = r1->field_23
    //     0xbe8c08: ldur            w0, [x1, #0x23]
    // 0xbe8c0c: DecompressPointer r0
    //     0xbe8c0c: add             x0, x0, HEAP, lsl #32
    // 0xbe8c10: r16 = 32.000000
    //     0xbe8c10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xbe8c14: ldr             x16, [x16, #0x848]
    // 0xbe8c18: r30 = Instance_Color
    //     0xbe8c18: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe8c1c: stp             lr, x16, [SP]
    // 0xbe8c20: mov             x1, x0
    // 0xbe8c24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe8c24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe8c28: ldr             x4, [x4, #0xaa0]
    // 0xbe8c2c: r0 = copyWith()
    //     0xbe8c2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe8c30: mov             x2, x0
    // 0xbe8c34: ldur            x0, [fp, #-0x28]
    // 0xbe8c38: stur            x2, [fp, #-0x48]
    // 0xbe8c3c: LoadField: r1 = r0->field_f
    //     0xbe8c3c: ldur            w1, [x0, #0xf]
    // 0xbe8c40: DecompressPointer r1
    //     0xbe8c40: add             x1, x1, HEAP, lsl #32
    // 0xbe8c44: cmp             w1, NULL
    // 0xbe8c48: b.eq            #0xbe8ec0
    // 0xbe8c4c: r0 = of()
    //     0xbe8c4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe8c50: LoadField: r1 = r0->field_87
    //     0xbe8c50: ldur            w1, [x0, #0x87]
    // 0xbe8c54: DecompressPointer r1
    //     0xbe8c54: add             x1, x1, HEAP, lsl #32
    // 0xbe8c58: LoadField: r0 = r1->field_7
    //     0xbe8c58: ldur            w0, [x1, #7]
    // 0xbe8c5c: DecompressPointer r0
    //     0xbe8c5c: add             x0, x0, HEAP, lsl #32
    // 0xbe8c60: r16 = Instance_Color
    //     0xbe8c60: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe8c64: r30 = 16.000000
    //     0xbe8c64: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe8c68: ldr             lr, [lr, #0x188]
    // 0xbe8c6c: stp             lr, x16, [SP]
    // 0xbe8c70: mov             x1, x0
    // 0xbe8c74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbe8c74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbe8c78: ldr             x4, [x4, #0x9b8]
    // 0xbe8c7c: r0 = copyWith()
    //     0xbe8c7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe8c80: stur            x0, [fp, #-0x28]
    // 0xbe8c84: r0 = TextSpan()
    //     0xbe8c84: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe8c88: mov             x3, x0
    // 0xbe8c8c: r0 = "OFF"
    //     0xbe8c8c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xbe8c90: ldr             x0, [x0, #0x348]
    // 0xbe8c94: stur            x3, [fp, #-0x50]
    // 0xbe8c98: StoreField: r3->field_b = r0
    //     0xbe8c98: stur            w0, [x3, #0xb]
    // 0xbe8c9c: r0 = Instance__DeferringMouseCursor
    //     0xbe8c9c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe8ca0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe8ca0: stur            w0, [x3, #0x17]
    // 0xbe8ca4: ldur            x1, [fp, #-0x28]
    // 0xbe8ca8: StoreField: r3->field_7 = r1
    //     0xbe8ca8: stur            w1, [x3, #7]
    // 0xbe8cac: r1 = Null
    //     0xbe8cac: mov             x1, NULL
    // 0xbe8cb0: r2 = 2
    //     0xbe8cb0: movz            x2, #0x2
    // 0xbe8cb4: r0 = AllocateArray()
    //     0xbe8cb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe8cb8: mov             x2, x0
    // 0xbe8cbc: ldur            x0, [fp, #-0x50]
    // 0xbe8cc0: stur            x2, [fp, #-0x28]
    // 0xbe8cc4: StoreField: r2->field_f = r0
    //     0xbe8cc4: stur            w0, [x2, #0xf]
    // 0xbe8cc8: r1 = <InlineSpan>
    //     0xbe8cc8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbe8ccc: ldr             x1, [x1, #0xe40]
    // 0xbe8cd0: r0 = AllocateGrowableArray()
    //     0xbe8cd0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe8cd4: mov             x1, x0
    // 0xbe8cd8: ldur            x0, [fp, #-0x28]
    // 0xbe8cdc: stur            x1, [fp, #-0x50]
    // 0xbe8ce0: StoreField: r1->field_f = r0
    //     0xbe8ce0: stur            w0, [x1, #0xf]
    // 0xbe8ce4: r0 = 2
    //     0xbe8ce4: movz            x0, #0x2
    // 0xbe8ce8: StoreField: r1->field_b = r0
    //     0xbe8ce8: stur            w0, [x1, #0xb]
    // 0xbe8cec: r0 = TextSpan()
    //     0xbe8cec: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe8cf0: mov             x1, x0
    // 0xbe8cf4: ldur            x0, [fp, #-0x38]
    // 0xbe8cf8: stur            x1, [fp, #-0x28]
    // 0xbe8cfc: StoreField: r1->field_b = r0
    //     0xbe8cfc: stur            w0, [x1, #0xb]
    // 0xbe8d00: ldur            x0, [fp, #-0x50]
    // 0xbe8d04: StoreField: r1->field_f = r0
    //     0xbe8d04: stur            w0, [x1, #0xf]
    // 0xbe8d08: r0 = Instance__DeferringMouseCursor
    //     0xbe8d08: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe8d0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe8d0c: stur            w0, [x1, #0x17]
    // 0xbe8d10: ldur            x0, [fp, #-0x48]
    // 0xbe8d14: StoreField: r1->field_7 = r0
    //     0xbe8d14: stur            w0, [x1, #7]
    // 0xbe8d18: r0 = RichText()
    //     0xbe8d18: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbe8d1c: stur            x0, [fp, #-0x38]
    // 0xbe8d20: r16 = Instance_TextAlign
    //     0xbe8d20: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbe8d24: str             x16, [SP]
    // 0xbe8d28: mov             x1, x0
    // 0xbe8d2c: ldur            x2, [fp, #-0x28]
    // 0xbe8d30: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xbe8d30: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xbe8d34: ldr             x4, [x4, #0x350]
    // 0xbe8d38: r0 = RichText()
    //     0xbe8d38: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbe8d3c: r1 = Null
    //     0xbe8d3c: mov             x1, NULL
    // 0xbe8d40: r2 = 6
    //     0xbe8d40: movz            x2, #0x6
    // 0xbe8d44: r0 = AllocateArray()
    //     0xbe8d44: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe8d48: mov             x2, x0
    // 0xbe8d4c: ldur            x0, [fp, #-0x30]
    // 0xbe8d50: stur            x2, [fp, #-0x28]
    // 0xbe8d54: StoreField: r2->field_f = r0
    //     0xbe8d54: stur            w0, [x2, #0xf]
    // 0xbe8d58: ldur            x0, [fp, #-0x40]
    // 0xbe8d5c: StoreField: r2->field_13 = r0
    //     0xbe8d5c: stur            w0, [x2, #0x13]
    // 0xbe8d60: ldur            x0, [fp, #-0x38]
    // 0xbe8d64: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe8d64: stur            w0, [x2, #0x17]
    // 0xbe8d68: r1 = <Widget>
    //     0xbe8d68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe8d6c: r0 = AllocateGrowableArray()
    //     0xbe8d6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe8d70: mov             x1, x0
    // 0xbe8d74: ldur            x0, [fp, #-0x28]
    // 0xbe8d78: stur            x1, [fp, #-0x30]
    // 0xbe8d7c: StoreField: r1->field_f = r0
    //     0xbe8d7c: stur            w0, [x1, #0xf]
    // 0xbe8d80: r0 = 6
    //     0xbe8d80: movz            x0, #0x6
    // 0xbe8d84: StoreField: r1->field_b = r0
    //     0xbe8d84: stur            w0, [x1, #0xb]
    // 0xbe8d88: r0 = Row()
    //     0xbe8d88: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe8d8c: mov             x1, x0
    // 0xbe8d90: r0 = Instance_Axis
    //     0xbe8d90: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe8d94: stur            x1, [fp, #-0x28]
    // 0xbe8d98: StoreField: r1->field_f = r0
    //     0xbe8d98: stur            w0, [x1, #0xf]
    // 0xbe8d9c: r0 = Instance_MainAxisAlignment
    //     0xbe8d9c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbe8da0: ldr             x0, [x0, #0xa8]
    // 0xbe8da4: StoreField: r1->field_13 = r0
    //     0xbe8da4: stur            w0, [x1, #0x13]
    // 0xbe8da8: r0 = Instance_MainAxisSize
    //     0xbe8da8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe8dac: ldr             x0, [x0, #0xa10]
    // 0xbe8db0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe8db0: stur            w0, [x1, #0x17]
    // 0xbe8db4: r0 = Instance_CrossAxisAlignment
    //     0xbe8db4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe8db8: ldr             x0, [x0, #0xa18]
    // 0xbe8dbc: StoreField: r1->field_1b = r0
    //     0xbe8dbc: stur            w0, [x1, #0x1b]
    // 0xbe8dc0: r0 = Instance_VerticalDirection
    //     0xbe8dc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe8dc4: ldr             x0, [x0, #0xa20]
    // 0xbe8dc8: StoreField: r1->field_23 = r0
    //     0xbe8dc8: stur            w0, [x1, #0x23]
    // 0xbe8dcc: r0 = Instance_Clip
    //     0xbe8dcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe8dd0: ldr             x0, [x0, #0x38]
    // 0xbe8dd4: StoreField: r1->field_2b = r0
    //     0xbe8dd4: stur            w0, [x1, #0x2b]
    // 0xbe8dd8: StoreField: r1->field_2f = rZR
    //     0xbe8dd8: stur            xzr, [x1, #0x2f]
    // 0xbe8ddc: ldur            x0, [fp, #-0x30]
    // 0xbe8de0: StoreField: r1->field_b = r0
    //     0xbe8de0: stur            w0, [x1, #0xb]
    // 0xbe8de4: r0 = Padding()
    //     0xbe8de4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe8de8: mov             x1, x0
    // 0xbe8dec: r0 = Instance_EdgeInsets
    //     0xbe8dec: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0xbe8df0: ldr             x0, [x0, #0x358]
    // 0xbe8df4: stur            x1, [fp, #-0x30]
    // 0xbe8df8: StoreField: r1->field_f = r0
    //     0xbe8df8: stur            w0, [x1, #0xf]
    // 0xbe8dfc: ldur            x0, [fp, #-0x28]
    // 0xbe8e00: StoreField: r1->field_b = r0
    //     0xbe8e00: stur            w0, [x1, #0xb]
    // 0xbe8e04: r0 = IntrinsicHeight()
    //     0xbe8e04: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xbe8e08: mov             x1, x0
    // 0xbe8e0c: ldur            x0, [fp, #-0x30]
    // 0xbe8e10: stur            x1, [fp, #-0x28]
    // 0xbe8e14: StoreField: r1->field_b = r0
    //     0xbe8e14: stur            w0, [x1, #0xb]
    // 0xbe8e18: r0 = Container()
    //     0xbe8e18: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbe8e1c: stur            x0, [fp, #-0x30]
    // 0xbe8e20: r16 = 93.000000
    //     0xbe8e20: add             x16, PP, #0x48, lsl #12  ; [pp+0x48360] 93
    //     0xbe8e24: ldr             x16, [x16, #0x360]
    // 0xbe8e28: ldur            lr, [fp, #-0x20]
    // 0xbe8e2c: stp             lr, x16, [SP, #8]
    // 0xbe8e30: ldur            x16, [fp, #-0x28]
    // 0xbe8e34: str             x16, [SP]
    // 0xbe8e38: mov             x1, x0
    // 0xbe8e3c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xbe8e3c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xbe8e40: ldr             x4, [x4, #0xc78]
    // 0xbe8e44: r0 = Container()
    //     0xbe8e44: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbe8e48: r1 = <Path>
    //     0xbe8e48: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xbe8e4c: ldr             x1, [x1, #0xd30]
    // 0xbe8e50: r0 = MovieTicketClipper()
    //     0xbe8e50: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xbe8e54: stur            x0, [fp, #-0x20]
    // 0xbe8e58: r0 = ClipPath()
    //     0xbe8e58: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xbe8e5c: mov             x1, x0
    // 0xbe8e60: ldur            x0, [fp, #-0x20]
    // 0xbe8e64: stur            x1, [fp, #-0x28]
    // 0xbe8e68: StoreField: r1->field_f = r0
    //     0xbe8e68: stur            w0, [x1, #0xf]
    // 0xbe8e6c: r0 = Instance_Clip
    //     0xbe8e6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xbe8e70: ldr             x0, [x0, #0x138]
    // 0xbe8e74: StoreField: r1->field_13 = r0
    //     0xbe8e74: stur            w0, [x1, #0x13]
    // 0xbe8e78: ldur            x0, [fp, #-0x30]
    // 0xbe8e7c: StoreField: r1->field_b = r0
    //     0xbe8e7c: stur            w0, [x1, #0xb]
    // 0xbe8e80: r0 = Padding()
    //     0xbe8e80: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe8e84: r1 = Instance_EdgeInsets
    //     0xbe8e84: add             x1, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbe8e88: ldr             x1, [x1, #0x778]
    // 0xbe8e8c: StoreField: r0->field_f = r1
    //     0xbe8e8c: stur            w1, [x0, #0xf]
    // 0xbe8e90: ldur            x1, [fp, #-0x28]
    // 0xbe8e94: StoreField: r0->field_b = r1
    //     0xbe8e94: stur            w1, [x0, #0xb]
    // 0xbe8e98: LeaveFrame
    //     0xbe8e98: mov             SP, fp
    //     0xbe8e9c: ldp             fp, lr, [SP], #0x10
    // 0xbe8ea0: ret
    //     0xbe8ea0: ret             
    // 0xbe8ea4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe8ea4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe8ea8: b               #0xbe86a4
    // 0xbe8eac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8eac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8eb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8eb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8eb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8eb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8eb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8eb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8ebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8ebc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe8ec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe8ec0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildHeader(/* No info */) {
    // ** addr: 0xbe8ec4, size: 0x5b4
    // 0xbe8ec4: EnterFrame
    //     0xbe8ec4: stp             fp, lr, [SP, #-0x10]!
    //     0xbe8ec8: mov             fp, SP
    // 0xbe8ecc: AllocStack(0x60)
    //     0xbe8ecc: sub             SP, SP, #0x60
    // 0xbe8ed0: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */)
    //     0xbe8ed0: stur            x1, [fp, #-8]
    // 0xbe8ed4: CheckStackOverflow
    //     0xbe8ed4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe8ed8: cmp             SP, x16
    //     0xbe8edc: b.ls            #0xbe9458
    // 0xbe8ee0: r1 = 1
    //     0xbe8ee0: movz            x1, #0x1
    // 0xbe8ee4: r0 = AllocateContext()
    //     0xbe8ee4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe8ee8: mov             x3, x0
    // 0xbe8eec: ldur            x0, [fp, #-8]
    // 0xbe8ef0: stur            x3, [fp, #-0x18]
    // 0xbe8ef4: StoreField: r3->field_f = r0
    //     0xbe8ef4: stur            w0, [x3, #0xf]
    // 0xbe8ef8: LoadField: r1 = r0->field_b
    //     0xbe8ef8: ldur            w1, [x0, #0xb]
    // 0xbe8efc: DecompressPointer r1
    //     0xbe8efc: add             x1, x1, HEAP, lsl #32
    // 0xbe8f00: cmp             w1, NULL
    // 0xbe8f04: b.eq            #0xbe9460
    // 0xbe8f08: LoadField: r2 = r1->field_1b
    //     0xbe8f08: ldur            w2, [x1, #0x1b]
    // 0xbe8f0c: DecompressPointer r2
    //     0xbe8f0c: add             x2, x2, HEAP, lsl #32
    // 0xbe8f10: LoadField: r1 = r2->field_7
    //     0xbe8f10: ldur            w1, [x2, #7]
    // 0xbe8f14: DecompressPointer r1
    //     0xbe8f14: add             x1, x1, HEAP, lsl #32
    // 0xbe8f18: cmp             w1, NULL
    // 0xbe8f1c: b.ne            #0xbe8f28
    // 0xbe8f20: r1 = Instance_TitleAlignment
    //     0xbe8f20: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbe8f24: ldr             x1, [x1, #0x518]
    // 0xbe8f28: r16 = Instance_TitleAlignment
    //     0xbe8f28: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbe8f2c: ldr             x16, [x16, #0x520]
    // 0xbe8f30: cmp             w1, w16
    // 0xbe8f34: b.ne            #0xbe8f44
    // 0xbe8f38: r4 = Instance_CrossAxisAlignment
    //     0xbe8f38: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xbe8f3c: ldr             x4, [x4, #0xc68]
    // 0xbe8f40: b               #0xbe8f68
    // 0xbe8f44: r16 = Instance_TitleAlignment
    //     0xbe8f44: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbe8f48: ldr             x16, [x16, #0x518]
    // 0xbe8f4c: cmp             w1, w16
    // 0xbe8f50: b.ne            #0xbe8f60
    // 0xbe8f54: r4 = Instance_CrossAxisAlignment
    //     0xbe8f54: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe8f58: ldr             x4, [x4, #0x890]
    // 0xbe8f5c: b               #0xbe8f68
    // 0xbe8f60: r4 = Instance_CrossAxisAlignment
    //     0xbe8f60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe8f64: ldr             x4, [x4, #0xa18]
    // 0xbe8f68: stur            x4, [fp, #-0x10]
    // 0xbe8f6c: r1 = <Widget>
    //     0xbe8f6c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe8f70: r2 = 0
    //     0xbe8f70: movz            x2, #0
    // 0xbe8f74: r0 = _GrowableList()
    //     0xbe8f74: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbe8f78: mov             x2, x0
    // 0xbe8f7c: ldur            x1, [fp, #-8]
    // 0xbe8f80: stur            x2, [fp, #-0x20]
    // 0xbe8f84: LoadField: r0 = r1->field_b
    //     0xbe8f84: ldur            w0, [x1, #0xb]
    // 0xbe8f88: DecompressPointer r0
    //     0xbe8f88: add             x0, x0, HEAP, lsl #32
    // 0xbe8f8c: cmp             w0, NULL
    // 0xbe8f90: b.eq            #0xbe9464
    // 0xbe8f94: LoadField: r3 = r0->field_f
    //     0xbe8f94: ldur            w3, [x0, #0xf]
    // 0xbe8f98: DecompressPointer r3
    //     0xbe8f98: add             x3, x3, HEAP, lsl #32
    // 0xbe8f9c: cmp             w3, NULL
    // 0xbe8fa0: b.ne            #0xbe8fac
    // 0xbe8fa4: r0 = Null
    //     0xbe8fa4: mov             x0, NULL
    // 0xbe8fa8: b               #0xbe8fc4
    // 0xbe8fac: LoadField: r0 = r3->field_7
    //     0xbe8fac: ldur            w0, [x3, #7]
    // 0xbe8fb0: cbnz            w0, #0xbe8fbc
    // 0xbe8fb4: r4 = false
    //     0xbe8fb4: add             x4, NULL, #0x30  ; false
    // 0xbe8fb8: b               #0xbe8fc0
    // 0xbe8fbc: r4 = true
    //     0xbe8fbc: add             x4, NULL, #0x20  ; true
    // 0xbe8fc0: mov             x0, x4
    // 0xbe8fc4: cmp             w0, NULL
    // 0xbe8fc8: b.eq            #0xbe9184
    // 0xbe8fcc: tbnz            w0, #4, #0xbe9184
    // 0xbe8fd0: cmp             w3, NULL
    // 0xbe8fd4: b.ne            #0xbe8fe0
    // 0xbe8fd8: r0 = Null
    //     0xbe8fd8: mov             x0, NULL
    // 0xbe8fdc: b               #0xbe8ff8
    // 0xbe8fe0: r0 = LoadClassIdInstr(r3)
    //     0xbe8fe0: ldur            x0, [x3, #-1]
    //     0xbe8fe4: ubfx            x0, x0, #0xc, #0x14
    // 0xbe8fe8: str             x3, [SP]
    // 0xbe8fec: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbe8fec: sub             lr, x0, #1, lsl #12
    //     0xbe8ff0: ldr             lr, [x21, lr, lsl #3]
    //     0xbe8ff4: blr             lr
    // 0xbe8ff8: cmp             w0, NULL
    // 0xbe8ffc: b.ne            #0xbe9008
    // 0xbe9000: r2 = ""
    //     0xbe9000: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe9004: b               #0xbe900c
    // 0xbe9008: mov             x2, x0
    // 0xbe900c: ldur            x0, [fp, #-8]
    // 0xbe9010: stur            x2, [fp, #-0x30]
    // 0xbe9014: LoadField: r1 = r0->field_b
    //     0xbe9014: ldur            w1, [x0, #0xb]
    // 0xbe9018: DecompressPointer r1
    //     0xbe9018: add             x1, x1, HEAP, lsl #32
    // 0xbe901c: cmp             w1, NULL
    // 0xbe9020: b.eq            #0xbe9468
    // 0xbe9024: LoadField: r3 = r1->field_1b
    //     0xbe9024: ldur            w3, [x1, #0x1b]
    // 0xbe9028: DecompressPointer r3
    //     0xbe9028: add             x3, x3, HEAP, lsl #32
    // 0xbe902c: LoadField: r1 = r3->field_7
    //     0xbe902c: ldur            w1, [x3, #7]
    // 0xbe9030: DecompressPointer r1
    //     0xbe9030: add             x1, x1, HEAP, lsl #32
    // 0xbe9034: cmp             w1, NULL
    // 0xbe9038: b.ne            #0xbe9044
    // 0xbe903c: r1 = Instance_TitleAlignment
    //     0xbe903c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbe9040: ldr             x1, [x1, #0x518]
    // 0xbe9044: r16 = Instance_TitleAlignment
    //     0xbe9044: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xbe9048: ldr             x16, [x16, #0x520]
    // 0xbe904c: cmp             w1, w16
    // 0xbe9050: b.ne            #0xbe905c
    // 0xbe9054: r4 = Instance_TextAlign
    //     0xbe9054: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xbe9058: b               #0xbe9078
    // 0xbe905c: r16 = Instance_TitleAlignment
    //     0xbe905c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xbe9060: ldr             x16, [x16, #0x518]
    // 0xbe9064: cmp             w1, w16
    // 0xbe9068: b.ne            #0xbe9074
    // 0xbe906c: r4 = Instance_TextAlign
    //     0xbe906c: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xbe9070: b               #0xbe9078
    // 0xbe9074: r4 = Instance_TextAlign
    //     0xbe9074: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbe9078: ldur            x3, [fp, #-0x20]
    // 0xbe907c: stur            x4, [fp, #-0x28]
    // 0xbe9080: LoadField: r1 = r0->field_f
    //     0xbe9080: ldur            w1, [x0, #0xf]
    // 0xbe9084: DecompressPointer r1
    //     0xbe9084: add             x1, x1, HEAP, lsl #32
    // 0xbe9088: cmp             w1, NULL
    // 0xbe908c: b.eq            #0xbe946c
    // 0xbe9090: r0 = of()
    //     0xbe9090: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe9094: LoadField: r1 = r0->field_87
    //     0xbe9094: ldur            w1, [x0, #0x87]
    // 0xbe9098: DecompressPointer r1
    //     0xbe9098: add             x1, x1, HEAP, lsl #32
    // 0xbe909c: LoadField: r0 = r1->field_27
    //     0xbe909c: ldur            w0, [x1, #0x27]
    // 0xbe90a0: DecompressPointer r0
    //     0xbe90a0: add             x0, x0, HEAP, lsl #32
    // 0xbe90a4: r16 = 21.000000
    //     0xbe90a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbe90a8: ldr             x16, [x16, #0x9b0]
    // 0xbe90ac: r30 = Instance_Color
    //     0xbe90ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe90b0: stp             lr, x16, [SP]
    // 0xbe90b4: mov             x1, x0
    // 0xbe90b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe90b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe90bc: ldr             x4, [x4, #0xaa0]
    // 0xbe90c0: r0 = copyWith()
    //     0xbe90c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe90c4: stur            x0, [fp, #-0x38]
    // 0xbe90c8: r0 = Text()
    //     0xbe90c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe90cc: mov             x1, x0
    // 0xbe90d0: ldur            x0, [fp, #-0x30]
    // 0xbe90d4: stur            x1, [fp, #-0x40]
    // 0xbe90d8: StoreField: r1->field_b = r0
    //     0xbe90d8: stur            w0, [x1, #0xb]
    // 0xbe90dc: ldur            x0, [fp, #-0x38]
    // 0xbe90e0: StoreField: r1->field_13 = r0
    //     0xbe90e0: stur            w0, [x1, #0x13]
    // 0xbe90e4: ldur            x0, [fp, #-0x28]
    // 0xbe90e8: StoreField: r1->field_1b = r0
    //     0xbe90e8: stur            w0, [x1, #0x1b]
    // 0xbe90ec: r0 = Padding()
    //     0xbe90ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe90f0: mov             x2, x0
    // 0xbe90f4: r0 = Instance_EdgeInsets
    //     0xbe90f4: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xbe90f8: ldr             x0, [x0, #0x4c0]
    // 0xbe90fc: stur            x2, [fp, #-0x28]
    // 0xbe9100: StoreField: r2->field_f = r0
    //     0xbe9100: stur            w0, [x2, #0xf]
    // 0xbe9104: ldur            x0, [fp, #-0x40]
    // 0xbe9108: StoreField: r2->field_b = r0
    //     0xbe9108: stur            w0, [x2, #0xb]
    // 0xbe910c: ldur            x0, [fp, #-0x20]
    // 0xbe9110: LoadField: r1 = r0->field_b
    //     0xbe9110: ldur            w1, [x0, #0xb]
    // 0xbe9114: LoadField: r3 = r0->field_f
    //     0xbe9114: ldur            w3, [x0, #0xf]
    // 0xbe9118: DecompressPointer r3
    //     0xbe9118: add             x3, x3, HEAP, lsl #32
    // 0xbe911c: LoadField: r4 = r3->field_b
    //     0xbe911c: ldur            w4, [x3, #0xb]
    // 0xbe9120: r3 = LoadInt32Instr(r1)
    //     0xbe9120: sbfx            x3, x1, #1, #0x1f
    // 0xbe9124: stur            x3, [fp, #-0x48]
    // 0xbe9128: r1 = LoadInt32Instr(r4)
    //     0xbe9128: sbfx            x1, x4, #1, #0x1f
    // 0xbe912c: cmp             x3, x1
    // 0xbe9130: b.ne            #0xbe913c
    // 0xbe9134: mov             x1, x0
    // 0xbe9138: r0 = _growToNextCapacity()
    //     0xbe9138: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe913c: ldur            x2, [fp, #-0x20]
    // 0xbe9140: ldur            x3, [fp, #-0x48]
    // 0xbe9144: add             x0, x3, #1
    // 0xbe9148: lsl             x1, x0, #1
    // 0xbe914c: StoreField: r2->field_b = r1
    //     0xbe914c: stur            w1, [x2, #0xb]
    // 0xbe9150: LoadField: r1 = r2->field_f
    //     0xbe9150: ldur            w1, [x2, #0xf]
    // 0xbe9154: DecompressPointer r1
    //     0xbe9154: add             x1, x1, HEAP, lsl #32
    // 0xbe9158: ldur            x0, [fp, #-0x28]
    // 0xbe915c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe915c: add             x25, x1, x3, lsl #2
    //     0xbe9160: add             x25, x25, #0xf
    //     0xbe9164: str             w0, [x25]
    //     0xbe9168: tbz             w0, #0, #0xbe9184
    //     0xbe916c: ldurb           w16, [x1, #-1]
    //     0xbe9170: ldurb           w17, [x0, #-1]
    //     0xbe9174: and             x16, x17, x16, lsr #2
    //     0xbe9178: tst             x16, HEAP, lsr #32
    //     0xbe917c: b.eq            #0xbe9184
    //     0xbe9180: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe9184: ldur            x1, [fp, #-8]
    // 0xbe9188: LoadField: r0 = r1->field_b
    //     0xbe9188: ldur            w0, [x1, #0xb]
    // 0xbe918c: DecompressPointer r0
    //     0xbe918c: add             x0, x0, HEAP, lsl #32
    // 0xbe9190: cmp             w0, NULL
    // 0xbe9194: b.eq            #0xbe9470
    // 0xbe9198: LoadField: r3 = r0->field_13
    //     0xbe9198: ldur            w3, [x0, #0x13]
    // 0xbe919c: DecompressPointer r3
    //     0xbe919c: add             x3, x3, HEAP, lsl #32
    // 0xbe91a0: cmp             w3, NULL
    // 0xbe91a4: b.ne            #0xbe91b0
    // 0xbe91a8: r0 = Null
    //     0xbe91a8: mov             x0, NULL
    // 0xbe91ac: b               #0xbe91dc
    // 0xbe91b0: LoadField: r0 = r3->field_7
    //     0xbe91b0: ldur            w0, [x3, #7]
    // 0xbe91b4: DecompressPointer r0
    //     0xbe91b4: add             x0, x0, HEAP, lsl #32
    // 0xbe91b8: cmp             w0, NULL
    // 0xbe91bc: b.ne            #0xbe91c8
    // 0xbe91c0: r0 = Null
    //     0xbe91c0: mov             x0, NULL
    // 0xbe91c4: b               #0xbe91dc
    // 0xbe91c8: LoadField: r4 = r0->field_7
    //     0xbe91c8: ldur            w4, [x0, #7]
    // 0xbe91cc: cbnz            w4, #0xbe91d8
    // 0xbe91d0: r0 = false
    //     0xbe91d0: add             x0, NULL, #0x30  ; false
    // 0xbe91d4: b               #0xbe91dc
    // 0xbe91d8: r0 = true
    //     0xbe91d8: add             x0, NULL, #0x20  ; true
    // 0xbe91dc: cmp             w0, NULL
    // 0xbe91e0: b.eq            #0xbe93a0
    // 0xbe91e4: tbnz            w0, #4, #0xbe93a0
    // 0xbe91e8: cmp             w3, NULL
    // 0xbe91ec: b.ne            #0xbe91f8
    // 0xbe91f0: r0 = Null
    //     0xbe91f0: mov             x0, NULL
    // 0xbe91f4: b               #0xbe922c
    // 0xbe91f8: LoadField: r0 = r3->field_7
    //     0xbe91f8: ldur            w0, [x3, #7]
    // 0xbe91fc: DecompressPointer r0
    //     0xbe91fc: add             x0, x0, HEAP, lsl #32
    // 0xbe9200: cmp             w0, NULL
    // 0xbe9204: b.ne            #0xbe9210
    // 0xbe9208: r0 = Null
    //     0xbe9208: mov             x0, NULL
    // 0xbe920c: b               #0xbe922c
    // 0xbe9210: r3 = LoadClassIdInstr(r0)
    //     0xbe9210: ldur            x3, [x0, #-1]
    //     0xbe9214: ubfx            x3, x3, #0xc, #0x14
    // 0xbe9218: str             x0, [SP]
    // 0xbe921c: mov             x0, x3
    // 0xbe9220: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbe9220: sub             lr, x0, #1, lsl #12
    //     0xbe9224: ldr             lr, [x21, lr, lsl #3]
    //     0xbe9228: blr             lr
    // 0xbe922c: cmp             w0, NULL
    // 0xbe9230: b.ne            #0xbe923c
    // 0xbe9234: r2 = ""
    //     0xbe9234: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe9238: b               #0xbe9240
    // 0xbe923c: mov             x2, x0
    // 0xbe9240: ldur            x1, [fp, #-8]
    // 0xbe9244: ldur            x0, [fp, #-0x20]
    // 0xbe9248: stur            x2, [fp, #-0x28]
    // 0xbe924c: LoadField: r3 = r1->field_f
    //     0xbe924c: ldur            w3, [x1, #0xf]
    // 0xbe9250: DecompressPointer r3
    //     0xbe9250: add             x3, x3, HEAP, lsl #32
    // 0xbe9254: cmp             w3, NULL
    // 0xbe9258: b.eq            #0xbe9474
    // 0xbe925c: mov             x1, x3
    // 0xbe9260: r0 = of()
    //     0xbe9260: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe9264: LoadField: r1 = r0->field_87
    //     0xbe9264: ldur            w1, [x0, #0x87]
    // 0xbe9268: DecompressPointer r1
    //     0xbe9268: add             x1, x1, HEAP, lsl #32
    // 0xbe926c: LoadField: r0 = r1->field_2b
    //     0xbe926c: ldur            w0, [x1, #0x2b]
    // 0xbe9270: DecompressPointer r0
    //     0xbe9270: add             x0, x0, HEAP, lsl #32
    // 0xbe9274: stur            x0, [fp, #-8]
    // 0xbe9278: r1 = Instance_Color
    //     0xbe9278: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe927c: d0 = 0.700000
    //     0xbe927c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe9280: ldr             d0, [x17, #0xf48]
    // 0xbe9284: r0 = withOpacity()
    //     0xbe9284: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe9288: r16 = 12.000000
    //     0xbe9288: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe928c: ldr             x16, [x16, #0x9e8]
    // 0xbe9290: stp             x0, x16, [SP, #8]
    // 0xbe9294: r16 = Instance_TextDecoration
    //     0xbe9294: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbe9298: ldr             x16, [x16, #0x10]
    // 0xbe929c: str             x16, [SP]
    // 0xbe92a0: ldur            x1, [fp, #-8]
    // 0xbe92a4: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbe92a4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbe92a8: ldr             x4, [x4, #0xe38]
    // 0xbe92ac: r0 = copyWith()
    //     0xbe92ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe92b0: stur            x0, [fp, #-8]
    // 0xbe92b4: r0 = Text()
    //     0xbe92b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe92b8: mov             x1, x0
    // 0xbe92bc: ldur            x0, [fp, #-0x28]
    // 0xbe92c0: stur            x1, [fp, #-0x30]
    // 0xbe92c4: StoreField: r1->field_b = r0
    //     0xbe92c4: stur            w0, [x1, #0xb]
    // 0xbe92c8: ldur            x0, [fp, #-8]
    // 0xbe92cc: StoreField: r1->field_13 = r0
    //     0xbe92cc: stur            w0, [x1, #0x13]
    // 0xbe92d0: r0 = InkWell()
    //     0xbe92d0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbe92d4: mov             x3, x0
    // 0xbe92d8: ldur            x0, [fp, #-0x30]
    // 0xbe92dc: stur            x3, [fp, #-8]
    // 0xbe92e0: StoreField: r3->field_b = r0
    //     0xbe92e0: stur            w0, [x3, #0xb]
    // 0xbe92e4: ldur            x2, [fp, #-0x18]
    // 0xbe92e8: r1 = Function '<anonymous closure>':.
    //     0xbe92e8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53750] AnonymousClosure: (0xbe9478), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildHeader (0xbe8ec4)
    //     0xbe92ec: ldr             x1, [x1, #0x750]
    // 0xbe92f0: r0 = AllocateClosure()
    //     0xbe92f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe92f4: mov             x1, x0
    // 0xbe92f8: ldur            x0, [fp, #-8]
    // 0xbe92fc: StoreField: r0->field_f = r1
    //     0xbe92fc: stur            w1, [x0, #0xf]
    // 0xbe9300: r1 = true
    //     0xbe9300: add             x1, NULL, #0x20  ; true
    // 0xbe9304: StoreField: r0->field_43 = r1
    //     0xbe9304: stur            w1, [x0, #0x43]
    // 0xbe9308: r2 = Instance_BoxShape
    //     0xbe9308: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbe930c: ldr             x2, [x2, #0x80]
    // 0xbe9310: StoreField: r0->field_47 = r2
    //     0xbe9310: stur            w2, [x0, #0x47]
    // 0xbe9314: StoreField: r0->field_6f = r1
    //     0xbe9314: stur            w1, [x0, #0x6f]
    // 0xbe9318: r2 = false
    //     0xbe9318: add             x2, NULL, #0x30  ; false
    // 0xbe931c: StoreField: r0->field_73 = r2
    //     0xbe931c: stur            w2, [x0, #0x73]
    // 0xbe9320: StoreField: r0->field_83 = r1
    //     0xbe9320: stur            w1, [x0, #0x83]
    // 0xbe9324: StoreField: r0->field_7b = r2
    //     0xbe9324: stur            w2, [x0, #0x7b]
    // 0xbe9328: ldur            x2, [fp, #-0x20]
    // 0xbe932c: LoadField: r1 = r2->field_b
    //     0xbe932c: ldur            w1, [x2, #0xb]
    // 0xbe9330: LoadField: r3 = r2->field_f
    //     0xbe9330: ldur            w3, [x2, #0xf]
    // 0xbe9334: DecompressPointer r3
    //     0xbe9334: add             x3, x3, HEAP, lsl #32
    // 0xbe9338: LoadField: r4 = r3->field_b
    //     0xbe9338: ldur            w4, [x3, #0xb]
    // 0xbe933c: r3 = LoadInt32Instr(r1)
    //     0xbe933c: sbfx            x3, x1, #1, #0x1f
    // 0xbe9340: stur            x3, [fp, #-0x48]
    // 0xbe9344: r1 = LoadInt32Instr(r4)
    //     0xbe9344: sbfx            x1, x4, #1, #0x1f
    // 0xbe9348: cmp             x3, x1
    // 0xbe934c: b.ne            #0xbe9358
    // 0xbe9350: mov             x1, x2
    // 0xbe9354: r0 = _growToNextCapacity()
    //     0xbe9354: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe9358: ldur            x2, [fp, #-0x20]
    // 0xbe935c: ldur            x3, [fp, #-0x48]
    // 0xbe9360: add             x0, x3, #1
    // 0xbe9364: lsl             x1, x0, #1
    // 0xbe9368: StoreField: r2->field_b = r1
    //     0xbe9368: stur            w1, [x2, #0xb]
    // 0xbe936c: LoadField: r1 = r2->field_f
    //     0xbe936c: ldur            w1, [x2, #0xf]
    // 0xbe9370: DecompressPointer r1
    //     0xbe9370: add             x1, x1, HEAP, lsl #32
    // 0xbe9374: ldur            x0, [fp, #-8]
    // 0xbe9378: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe9378: add             x25, x1, x3, lsl #2
    //     0xbe937c: add             x25, x25, #0xf
    //     0xbe9380: str             w0, [x25]
    //     0xbe9384: tbz             w0, #0, #0xbe93a0
    //     0xbe9388: ldurb           w16, [x1, #-1]
    //     0xbe938c: ldurb           w17, [x0, #-1]
    //     0xbe9390: and             x16, x17, x16, lsr #2
    //     0xbe9394: tst             x16, HEAP, lsr #32
    //     0xbe9398: b.eq            #0xbe93a0
    //     0xbe939c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe93a0: LoadField: r0 = r2->field_b
    //     0xbe93a0: ldur            w0, [x2, #0xb]
    // 0xbe93a4: LoadField: r1 = r2->field_f
    //     0xbe93a4: ldur            w1, [x2, #0xf]
    // 0xbe93a8: DecompressPointer r1
    //     0xbe93a8: add             x1, x1, HEAP, lsl #32
    // 0xbe93ac: LoadField: r3 = r1->field_b
    //     0xbe93ac: ldur            w3, [x1, #0xb]
    // 0xbe93b0: r4 = LoadInt32Instr(r0)
    //     0xbe93b0: sbfx            x4, x0, #1, #0x1f
    // 0xbe93b4: stur            x4, [fp, #-0x48]
    // 0xbe93b8: r0 = LoadInt32Instr(r3)
    //     0xbe93b8: sbfx            x0, x3, #1, #0x1f
    // 0xbe93bc: cmp             x4, x0
    // 0xbe93c0: b.ne            #0xbe93cc
    // 0xbe93c4: mov             x1, x2
    // 0xbe93c8: r0 = _growToNextCapacity()
    //     0xbe93c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe93cc: ldur            x0, [fp, #-0x20]
    // 0xbe93d0: ldur            x2, [fp, #-0x10]
    // 0xbe93d4: ldur            x1, [fp, #-0x48]
    // 0xbe93d8: add             x3, x1, #1
    // 0xbe93dc: lsl             x4, x3, #1
    // 0xbe93e0: StoreField: r0->field_b = r4
    //     0xbe93e0: stur            w4, [x0, #0xb]
    // 0xbe93e4: LoadField: r3 = r0->field_f
    //     0xbe93e4: ldur            w3, [x0, #0xf]
    // 0xbe93e8: DecompressPointer r3
    //     0xbe93e8: add             x3, x3, HEAP, lsl #32
    // 0xbe93ec: add             x4, x3, x1, lsl #2
    // 0xbe93f0: r16 = Instance_SizedBox
    //     0xbe93f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xbe93f4: ldr             x16, [x16, #0x9f0]
    // 0xbe93f8: StoreField: r4->field_f = r16
    //     0xbe93f8: stur            w16, [x4, #0xf]
    // 0xbe93fc: r0 = Column()
    //     0xbe93fc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbe9400: r1 = Instance_Axis
    //     0xbe9400: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe9404: StoreField: r0->field_f = r1
    //     0xbe9404: stur            w1, [x0, #0xf]
    // 0xbe9408: r1 = Instance_MainAxisAlignment
    //     0xbe9408: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe940c: ldr             x1, [x1, #0xa08]
    // 0xbe9410: StoreField: r0->field_13 = r1
    //     0xbe9410: stur            w1, [x0, #0x13]
    // 0xbe9414: r1 = Instance_MainAxisSize
    //     0xbe9414: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe9418: ldr             x1, [x1, #0xa10]
    // 0xbe941c: ArrayStore: r0[0] = r1  ; List_4
    //     0xbe941c: stur            w1, [x0, #0x17]
    // 0xbe9420: ldur            x1, [fp, #-0x10]
    // 0xbe9424: StoreField: r0->field_1b = r1
    //     0xbe9424: stur            w1, [x0, #0x1b]
    // 0xbe9428: r1 = Instance_VerticalDirection
    //     0xbe9428: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe942c: ldr             x1, [x1, #0xa20]
    // 0xbe9430: StoreField: r0->field_23 = r1
    //     0xbe9430: stur            w1, [x0, #0x23]
    // 0xbe9434: r1 = Instance_Clip
    //     0xbe9434: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe9438: ldr             x1, [x1, #0x38]
    // 0xbe943c: StoreField: r0->field_2b = r1
    //     0xbe943c: stur            w1, [x0, #0x2b]
    // 0xbe9440: StoreField: r0->field_2f = rZR
    //     0xbe9440: stur            xzr, [x0, #0x2f]
    // 0xbe9444: ldur            x1, [fp, #-0x20]
    // 0xbe9448: StoreField: r0->field_b = r1
    //     0xbe9448: stur            w1, [x0, #0xb]
    // 0xbe944c: LeaveFrame
    //     0xbe944c: mov             SP, fp
    //     0xbe9450: ldp             fp, lr, [SP], #0x10
    // 0xbe9454: ret
    //     0xbe9454: ret             
    // 0xbe9458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe9458: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe945c: b               #0xbe8ee0
    // 0xbe9460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe9460: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe9464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe9464: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe9468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe9468: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe946c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe946c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe9470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe9470: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe9474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe9474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbe9478, size: 0x48
    // 0xbe9478: EnterFrame
    //     0xbe9478: stp             fp, lr, [SP, #-0x10]!
    //     0xbe947c: mov             fp, SP
    // 0xbe9480: ldr             x0, [fp, #0x10]
    // 0xbe9484: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbe9484: ldur            w1, [x0, #0x17]
    // 0xbe9488: DecompressPointer r1
    //     0xbe9488: add             x1, x1, HEAP, lsl #32
    // 0xbe948c: CheckStackOverflow
    //     0xbe948c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe9490: cmp             SP, x16
    //     0xbe9494: b.ls            #0xbe94b8
    // 0xbe9498: LoadField: r0 = r1->field_f
    //     0xbe9498: ldur            w0, [x1, #0xf]
    // 0xbe949c: DecompressPointer r0
    //     0xbe949c: add             x0, x0, HEAP, lsl #32
    // 0xbe94a0: mov             x1, x0
    // 0xbe94a4: r0 = _onViewAllTap()
    //     0xbe94a4: bl              #0xbe94c0  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onViewAllTap
    // 0xbe94a8: r0 = Null
    //     0xbe94a8: mov             x0, NULL
    // 0xbe94ac: LeaveFrame
    //     0xbe94ac: mov             SP, fp
    //     0xbe94b0: ldp             fp, lr, [SP], #0x10
    // 0xbe94b4: ret
    //     0xbe94b4: ret             
    // 0xbe94b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe94b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe94bc: b               #0xbe9498
  }
  _ _onViewAllTap(/* No info */) {
    // ** addr: 0xbe94c0, size: 0xe4
    // 0xbe94c0: EnterFrame
    //     0xbe94c0: stp             fp, lr, [SP, #-0x10]!
    //     0xbe94c4: mov             fp, SP
    // 0xbe94c8: AllocStack(0x30)
    //     0xbe94c8: sub             SP, SP, #0x30
    // 0xbe94cc: CheckStackOverflow
    //     0xbe94cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe94d0: cmp             SP, x16
    //     0xbe94d4: b.ls            #0xbe9598
    // 0xbe94d8: LoadField: r0 = r1->field_b
    //     0xbe94d8: ldur            w0, [x1, #0xb]
    // 0xbe94dc: DecompressPointer r0
    //     0xbe94dc: add             x0, x0, HEAP, lsl #32
    // 0xbe94e0: cmp             w0, NULL
    // 0xbe94e4: b.eq            #0xbe95a0
    // 0xbe94e8: LoadField: r1 = r0->field_27
    //     0xbe94e8: ldur            w1, [x0, #0x27]
    // 0xbe94ec: DecompressPointer r1
    //     0xbe94ec: add             x1, x1, HEAP, lsl #32
    // 0xbe94f0: cmp             w1, NULL
    // 0xbe94f4: b.ne            #0xbe94fc
    // 0xbe94f8: r1 = ""
    //     0xbe94f8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe94fc: LoadField: r2 = r0->field_23
    //     0xbe94fc: ldur            w2, [x0, #0x23]
    // 0xbe9500: DecompressPointer r2
    //     0xbe9500: add             x2, x2, HEAP, lsl #32
    // 0xbe9504: cmp             w2, NULL
    // 0xbe9508: b.ne            #0xbe9510
    // 0xbe950c: r2 = ""
    //     0xbe950c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe9510: LoadField: r3 = r0->field_2f
    //     0xbe9510: ldur            w3, [x0, #0x2f]
    // 0xbe9514: DecompressPointer r3
    //     0xbe9514: add             x3, x3, HEAP, lsl #32
    // 0xbe9518: cmp             w3, NULL
    // 0xbe951c: b.ne            #0xbe9524
    // 0xbe9520: r3 = ""
    //     0xbe9520: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe9524: LoadField: r4 = r0->field_2b
    //     0xbe9524: ldur            w4, [x0, #0x2b]
    // 0xbe9528: DecompressPointer r4
    //     0xbe9528: add             x4, x4, HEAP, lsl #32
    // 0xbe952c: cmp             w4, NULL
    // 0xbe9530: b.ne            #0xbe9538
    // 0xbe9534: r4 = ""
    //     0xbe9534: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe9538: LoadField: r5 = r0->field_13
    //     0xbe9538: ldur            w5, [x0, #0x13]
    // 0xbe953c: DecompressPointer r5
    //     0xbe953c: add             x5, x5, HEAP, lsl #32
    // 0xbe9540: cmp             w5, NULL
    // 0xbe9544: b.ne            #0xbe9550
    // 0xbe9548: r5 = Null
    //     0xbe9548: mov             x5, NULL
    // 0xbe954c: b               #0xbe955c
    // 0xbe9550: LoadField: r6 = r5->field_b
    //     0xbe9550: ldur            w6, [x5, #0xb]
    // 0xbe9554: DecompressPointer r6
    //     0xbe9554: add             x6, x6, HEAP, lsl #32
    // 0xbe9558: mov             x5, x6
    // 0xbe955c: LoadField: r6 = r0->field_3f
    //     0xbe955c: ldur            w6, [x0, #0x3f]
    // 0xbe9560: DecompressPointer r6
    //     0xbe9560: add             x6, x6, HEAP, lsl #32
    // 0xbe9564: stp             x1, x6, [SP, #0x20]
    // 0xbe9568: stp             x3, x2, [SP, #0x10]
    // 0xbe956c: stp             x5, x4, [SP]
    // 0xbe9570: r4 = 0
    //     0xbe9570: movz            x4, #0
    // 0xbe9574: ldr             x0, [SP, #0x28]
    // 0xbe9578: r16 = UnlinkedCall_0x613b5c
    //     0xbe9578: add             x16, PP, #0x53, lsl #12  ; [pp+0x53758] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe957c: add             x16, x16, #0x758
    // 0xbe9580: ldp             x5, lr, [x16]
    // 0xbe9584: blr             lr
    // 0xbe9588: r0 = Null
    //     0xbe9588: mov             x0, NULL
    // 0xbe958c: LeaveFrame
    //     0xbe958c: mov             SP, fp
    //     0xbe9590: ldp             fp, lr, [SP], #0x10
    // 0xbe9594: ret
    //     0xbe9594: ret             
    // 0xbe9598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe9598: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe959c: b               #0xbe94d8
    // 0xbe95a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe95a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbe95a4, size: 0x344
    // 0xbe95a4: EnterFrame
    //     0xbe95a4: stp             fp, lr, [SP, #-0x10]!
    //     0xbe95a8: mov             fp, SP
    // 0xbe95ac: AllocStack(0x78)
    //     0xbe95ac: sub             SP, SP, #0x78
    // 0xbe95b0: SetupParameters()
    //     0xbe95b0: ldr             x0, [fp, #0x20]
    //     0xbe95b4: ldur            w1, [x0, #0x17]
    //     0xbe95b8: add             x1, x1, HEAP, lsl #32
    //     0xbe95bc: stur            x1, [fp, #-8]
    // 0xbe95c0: CheckStackOverflow
    //     0xbe95c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe95c4: cmp             SP, x16
    //     0xbe95c8: b.ls            #0xbe98c4
    // 0xbe95cc: r1 = 3
    //     0xbe95cc: movz            x1, #0x3
    // 0xbe95d0: r0 = AllocateContext()
    //     0xbe95d0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe95d4: mov             x5, x0
    // 0xbe95d8: ldur            x4, [fp, #-8]
    // 0xbe95dc: stur            x5, [fp, #-0x18]
    // 0xbe95e0: StoreField: r5->field_b = r4
    //     0xbe95e0: stur            w4, [x5, #0xb]
    // 0xbe95e4: ldr             x0, [fp, #0x18]
    // 0xbe95e8: StoreField: r5->field_f = r0
    //     0xbe95e8: stur            w0, [x5, #0xf]
    // 0xbe95ec: ldr             x0, [fp, #0x10]
    // 0xbe95f0: r1 = LoadInt32Instr(r0)
    //     0xbe95f0: sbfx            x1, x0, #1, #0x1f
    //     0xbe95f4: tbz             w0, #0, #0xbe95fc
    //     0xbe95f8: ldur            x1, [x0, #7]
    // 0xbe95fc: lsl             x6, x1, #1
    // 0xbe9600: stur            x6, [fp, #-0x10]
    // 0xbe9604: r0 = BoxInt64Instr(r6)
    //     0xbe9604: sbfiz           x0, x6, #1, #0x1f
    //     0xbe9608: cmp             x6, x0, asr #1
    //     0xbe960c: b.eq            #0xbe9618
    //     0xbe9610: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe9614: stur            x6, [x0, #7]
    // 0xbe9618: StoreField: r5->field_13 = r0
    //     0xbe9618: stur            w0, [x5, #0x13]
    // 0xbe961c: add             x2, x6, #2
    // 0xbe9620: LoadField: r0 = r4->field_f
    //     0xbe9620: ldur            w0, [x4, #0xf]
    // 0xbe9624: DecompressPointer r0
    //     0xbe9624: add             x0, x0, HEAP, lsl #32
    // 0xbe9628: LoadField: r1 = r0->field_b
    //     0xbe9628: ldur            w1, [x0, #0xb]
    // 0xbe962c: DecompressPointer r1
    //     0xbe962c: add             x1, x1, HEAP, lsl #32
    // 0xbe9630: cmp             w1, NULL
    // 0xbe9634: b.eq            #0xbe98cc
    // 0xbe9638: LoadField: r0 = r1->field_b
    //     0xbe9638: ldur            w0, [x1, #0xb]
    // 0xbe963c: DecompressPointer r0
    //     0xbe963c: add             x0, x0, HEAP, lsl #32
    // 0xbe9640: cmp             w0, NULL
    // 0xbe9644: b.eq            #0xbe98d0
    // 0xbe9648: LoadField: r3 = r0->field_b
    //     0xbe9648: ldur            w3, [x0, #0xb]
    // 0xbe964c: r0 = BoxInt64Instr(r2)
    //     0xbe964c: sbfiz           x0, x2, #1, #0x1f
    //     0xbe9650: cmp             x2, x0, asr #1
    //     0xbe9654: b.eq            #0xbe9660
    //     0xbe9658: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe965c: stur            x2, [x0, #7]
    // 0xbe9660: mov             x1, x0
    // 0xbe9664: r2 = 0
    //     0xbe9664: movz            x2, #0
    // 0xbe9668: r0 = clamp()
    //     0xbe9668: bl              #0x6b3958  ; [dart:core] _IntegerImplementation::clamp
    // 0xbe966c: mov             x1, x0
    // 0xbe9670: ldur            x0, [fp, #-8]
    // 0xbe9674: LoadField: r2 = r0->field_f
    //     0xbe9674: ldur            w2, [x0, #0xf]
    // 0xbe9678: DecompressPointer r2
    //     0xbe9678: add             x2, x2, HEAP, lsl #32
    // 0xbe967c: LoadField: r3 = r2->field_b
    //     0xbe967c: ldur            w3, [x2, #0xb]
    // 0xbe9680: DecompressPointer r3
    //     0xbe9680: add             x3, x3, HEAP, lsl #32
    // 0xbe9684: cmp             w3, NULL
    // 0xbe9688: b.eq            #0xbe98d4
    // 0xbe968c: LoadField: r2 = r3->field_b
    //     0xbe968c: ldur            w2, [x3, #0xb]
    // 0xbe9690: DecompressPointer r2
    //     0xbe9690: add             x2, x2, HEAP, lsl #32
    // 0xbe9694: cmp             w2, NULL
    // 0xbe9698: b.eq            #0xbe98d8
    // 0xbe969c: str             x1, [SP]
    // 0xbe96a0: mov             x1, x2
    // 0xbe96a4: ldur            x2, [fp, #-0x10]
    // 0xbe96a8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xbe96a8: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xbe96ac: r0 = sublist()
    //     0xbe96ac: bl              #0x71da80  ; [dart:core] _GrowableList::sublist
    // 0xbe96b0: mov             x1, x0
    // 0xbe96b4: ldur            x2, [fp, #-0x18]
    // 0xbe96b8: stur            x1, [fp, #-0x48]
    // 0xbe96bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe96bc: stur            w0, [x2, #0x17]
    //     0xbe96c0: ldurb           w16, [x2, #-1]
    //     0xbe96c4: ldurb           w17, [x0, #-1]
    //     0xbe96c8: and             x16, x17, x16, lsr #2
    //     0xbe96cc: tst             x16, HEAP, lsr #32
    //     0xbe96d0: b.eq            #0xbe96d8
    //     0xbe96d4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbe96d8: r3 = 0
    //     0xbe96d8: movz            x3, #0
    // 0xbe96dc: ldur            x0, [fp, #-8]
    // 0xbe96e0: stur            x3, [fp, #-0x10]
    // 0xbe96e4: CheckStackOverflow
    //     0xbe96e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe96e8: cmp             SP, x16
    //     0xbe96ec: b.ls            #0xbe98dc
    // 0xbe96f0: LoadField: r4 = r1->field_b
    //     0xbe96f0: ldur            w4, [x1, #0xb]
    // 0xbe96f4: r5 = LoadInt32Instr(r4)
    //     0xbe96f4: sbfx            x5, x4, #1, #0x1f
    // 0xbe96f8: cmp             x3, x5
    // 0xbe96fc: b.ge            #0xbe97f4
    // 0xbe9700: LoadField: r4 = r1->field_f
    //     0xbe9700: ldur            w4, [x1, #0xf]
    // 0xbe9704: DecompressPointer r4
    //     0xbe9704: add             x4, x4, HEAP, lsl #32
    // 0xbe9708: ArrayLoad: r5 = r4[r3]  ; Unknown_4
    //     0xbe9708: add             x16, x4, x3, lsl #2
    //     0xbe970c: ldur            w5, [x16, #0xf]
    // 0xbe9710: DecompressPointer r5
    //     0xbe9710: add             x5, x5, HEAP, lsl #32
    // 0xbe9714: LoadField: r4 = r0->field_f
    //     0xbe9714: ldur            w4, [x0, #0xf]
    // 0xbe9718: DecompressPointer r4
    //     0xbe9718: add             x4, x4, HEAP, lsl #32
    // 0xbe971c: LoadField: r6 = r4->field_b
    //     0xbe971c: ldur            w6, [x4, #0xb]
    // 0xbe9720: DecompressPointer r6
    //     0xbe9720: add             x6, x6, HEAP, lsl #32
    // 0xbe9724: stur            x6, [fp, #-0x40]
    // 0xbe9728: cmp             w6, NULL
    // 0xbe972c: b.eq            #0xbe98e4
    // 0xbe9730: LoadField: r4 = r5->field_53
    //     0xbe9730: ldur            w4, [x5, #0x53]
    // 0xbe9734: DecompressPointer r4
    //     0xbe9734: add             x4, x4, HEAP, lsl #32
    // 0xbe9738: stur            x4, [fp, #-0x38]
    // 0xbe973c: LoadField: r7 = r5->field_3b
    //     0xbe973c: ldur            w7, [x5, #0x3b]
    // 0xbe9740: DecompressPointer r7
    //     0xbe9740: add             x7, x7, HEAP, lsl #32
    // 0xbe9744: cmp             w7, NULL
    // 0xbe9748: b.ne            #0xbe9754
    // 0xbe974c: r7 = Null
    //     0xbe974c: mov             x7, NULL
    // 0xbe9750: b               #0xbe9760
    // 0xbe9754: LoadField: r8 = r7->field_b
    //     0xbe9754: ldur            w8, [x7, #0xb]
    // 0xbe9758: DecompressPointer r8
    //     0xbe9758: add             x8, x8, HEAP, lsl #32
    // 0xbe975c: mov             x7, x8
    // 0xbe9760: stur            x7, [fp, #-0x30]
    // 0xbe9764: LoadField: r8 = r5->field_2b
    //     0xbe9764: ldur            w8, [x5, #0x2b]
    // 0xbe9768: DecompressPointer r8
    //     0xbe9768: add             x8, x8, HEAP, lsl #32
    // 0xbe976c: stur            x8, [fp, #-0x28]
    // 0xbe9770: LoadField: r9 = r5->field_57
    //     0xbe9770: ldur            w9, [x5, #0x57]
    // 0xbe9774: DecompressPointer r9
    //     0xbe9774: add             x9, x9, HEAP, lsl #32
    // 0xbe9778: stur            x9, [fp, #-0x20]
    // 0xbe977c: LoadField: r10 = r5->field_7b
    //     0xbe977c: ldur            w10, [x5, #0x7b]
    // 0xbe9780: DecompressPointer r10
    //     0xbe9780: add             x10, x10, HEAP, lsl #32
    // 0xbe9784: cmp             w10, NULL
    // 0xbe9788: b.ne            #0xbe9798
    // 0xbe978c: r0 = ProductRating()
    //     0xbe978c: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xbe9790: mov             x2, x0
    // 0xbe9794: b               #0xbe979c
    // 0xbe9798: mov             x2, x10
    // 0xbe979c: ldur            x0, [fp, #-0x10]
    // 0xbe97a0: ldur            x1, [fp, #-0x40]
    // 0xbe97a4: LoadField: r3 = r1->field_3b
    //     0xbe97a4: ldur            w3, [x1, #0x3b]
    // 0xbe97a8: DecompressPointer r3
    //     0xbe97a8: add             x3, x3, HEAP, lsl #32
    // 0xbe97ac: ldur            x16, [fp, #-0x38]
    // 0xbe97b0: stp             x16, x3, [SP, #0x20]
    // 0xbe97b4: ldur            x16, [fp, #-0x30]
    // 0xbe97b8: ldur            lr, [fp, #-0x28]
    // 0xbe97bc: stp             lr, x16, [SP, #0x10]
    // 0xbe97c0: ldur            x16, [fp, #-0x20]
    // 0xbe97c4: stp             x2, x16, [SP]
    // 0xbe97c8: r4 = 0
    //     0xbe97c8: movz            x4, #0
    // 0xbe97cc: ldr             x0, [SP, #0x28]
    // 0xbe97d0: r16 = UnlinkedCall_0x613b5c
    //     0xbe97d0: add             x16, PP, #0x53, lsl #12  ; [pp+0x53698] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe97d4: add             x16, x16, #0x698
    // 0xbe97d8: ldp             x5, lr, [x16]
    // 0xbe97dc: blr             lr
    // 0xbe97e0: ldur            x0, [fp, #-0x10]
    // 0xbe97e4: add             x3, x0, #1
    // 0xbe97e8: ldur            x2, [fp, #-0x18]
    // 0xbe97ec: ldur            x1, [fp, #-0x48]
    // 0xbe97f0: b               #0xbe96dc
    // 0xbe97f4: ldur            x1, [fp, #-0x48]
    // 0xbe97f8: r0 = asMap()
    //     0xbe97f8: bl              #0x7143ec  ; [dart:collection] ListBase::asMap
    // 0xbe97fc: mov             x1, x0
    // 0xbe9800: r0 = entries()
    //     0xbe9800: bl              #0x1641968  ; [dart:collection] MapBase::entries
    // 0xbe9804: ldur            x2, [fp, #-0x18]
    // 0xbe9808: r1 = Function '<anonymous closure>':.
    //     0xbe9808: add             x1, PP, #0x53, lsl #12  ; [pp+0x536a8] AnonymousClosure: (0xbe98e8), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xbe8154)
    //     0xbe980c: ldr             x1, [x1, #0x6a8]
    // 0xbe9810: stur            x0, [fp, #-8]
    // 0xbe9814: r0 = AllocateClosure()
    //     0xbe9814: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe9818: r16 = <SizedBox>
    //     0xbe9818: add             x16, PP, #0x53, lsl #12  ; [pp+0x536b0] TypeArguments: <SizedBox>
    //     0xbe981c: ldr             x16, [x16, #0x6b0]
    // 0xbe9820: ldur            lr, [fp, #-8]
    // 0xbe9824: stp             lr, x16, [SP, #8]
    // 0xbe9828: str             x0, [SP]
    // 0xbe982c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbe982c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbe9830: r0 = map()
    //     0xbe9830: bl              #0x78898c  ; [dart:_internal] ListIterable::map
    // 0xbe9834: mov             x1, x0
    // 0xbe9838: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbe9838: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbe983c: r0 = toList()
    //     0xbe983c: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xbe9840: stur            x0, [fp, #-8]
    // 0xbe9844: r0 = Row()
    //     0xbe9844: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe9848: mov             x1, x0
    // 0xbe984c: r0 = Instance_Axis
    //     0xbe984c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe9850: stur            x1, [fp, #-0x18]
    // 0xbe9854: StoreField: r1->field_f = r0
    //     0xbe9854: stur            w0, [x1, #0xf]
    // 0xbe9858: r0 = Instance_MainAxisAlignment
    //     0xbe9858: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe985c: ldr             x0, [x0, #0xa08]
    // 0xbe9860: StoreField: r1->field_13 = r0
    //     0xbe9860: stur            w0, [x1, #0x13]
    // 0xbe9864: r0 = Instance_MainAxisSize
    //     0xbe9864: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe9868: ldr             x0, [x0, #0xa10]
    // 0xbe986c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe986c: stur            w0, [x1, #0x17]
    // 0xbe9870: r0 = Instance_CrossAxisAlignment
    //     0xbe9870: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe9874: ldr             x0, [x0, #0xa18]
    // 0xbe9878: StoreField: r1->field_1b = r0
    //     0xbe9878: stur            w0, [x1, #0x1b]
    // 0xbe987c: r0 = Instance_VerticalDirection
    //     0xbe987c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe9880: ldr             x0, [x0, #0xa20]
    // 0xbe9884: StoreField: r1->field_23 = r0
    //     0xbe9884: stur            w0, [x1, #0x23]
    // 0xbe9888: r0 = Instance_Clip
    //     0xbe9888: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe988c: ldr             x0, [x0, #0x38]
    // 0xbe9890: StoreField: r1->field_2b = r0
    //     0xbe9890: stur            w0, [x1, #0x2b]
    // 0xbe9894: StoreField: r1->field_2f = rZR
    //     0xbe9894: stur            xzr, [x1, #0x2f]
    // 0xbe9898: ldur            x0, [fp, #-8]
    // 0xbe989c: StoreField: r1->field_b = r0
    //     0xbe989c: stur            w0, [x1, #0xb]
    // 0xbe98a0: r0 = Padding()
    //     0xbe98a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe98a4: r1 = Instance_EdgeInsets
    //     0xbe98a4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52018] Obj!EdgeInsets@d586d1
    //     0xbe98a8: ldr             x1, [x1, #0x18]
    // 0xbe98ac: StoreField: r0->field_f = r1
    //     0xbe98ac: stur            w1, [x0, #0xf]
    // 0xbe98b0: ldur            x1, [fp, #-0x18]
    // 0xbe98b4: StoreField: r0->field_b = r1
    //     0xbe98b4: stur            w1, [x0, #0xb]
    // 0xbe98b8: LeaveFrame
    //     0xbe98b8: mov             SP, fp
    //     0xbe98bc: ldp             fp, lr, [SP], #0x10
    // 0xbe98c0: ret
    //     0xbe98c0: ret             
    // 0xbe98c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe98c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe98c8: b               #0xbe95cc
    // 0xbe98cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe98cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe98d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe98d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe98d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe98d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe98d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe98d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe98dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe98dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe98e0: b               #0xbe96f0
    // 0xbe98e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe98e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SizedBox <anonymous closure>(dynamic, MapEntry<int, Entity>) {
    // ** addr: 0xbe98e8, size: 0x330
    // 0xbe98e8: EnterFrame
    //     0xbe98e8: stp             fp, lr, [SP, #-0x10]!
    //     0xbe98ec: mov             fp, SP
    // 0xbe98f0: AllocStack(0x40)
    //     0xbe98f0: sub             SP, SP, #0x40
    // 0xbe98f4: SetupParameters()
    //     0xbe98f4: ldr             x0, [fp, #0x18]
    //     0xbe98f8: ldur            w2, [x0, #0x17]
    //     0xbe98fc: add             x2, x2, HEAP, lsl #32
    //     0xbe9900: stur            x2, [fp, #-0x18]
    // 0xbe9904: CheckStackOverflow
    //     0xbe9904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe9908: cmp             SP, x16
    //     0xbe990c: b.ls            #0xbe9bf0
    // 0xbe9910: ldr             x0, [fp, #0x10]
    // 0xbe9914: LoadField: r3 = r0->field_b
    //     0xbe9914: ldur            w3, [x0, #0xb]
    // 0xbe9918: DecompressPointer r3
    //     0xbe9918: add             x3, x3, HEAP, lsl #32
    // 0xbe991c: stur            x3, [fp, #-0x10]
    // 0xbe9920: LoadField: r4 = r0->field_f
    //     0xbe9920: ldur            w4, [x0, #0xf]
    // 0xbe9924: DecompressPointer r4
    //     0xbe9924: add             x4, x4, HEAP, lsl #32
    // 0xbe9928: stur            x4, [fp, #-8]
    // 0xbe992c: LoadField: r1 = r2->field_f
    //     0xbe992c: ldur            w1, [x2, #0xf]
    // 0xbe9930: DecompressPointer r1
    //     0xbe9930: add             x1, x1, HEAP, lsl #32
    // 0xbe9934: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbe9934: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbe9938: r0 = _of()
    //     0xbe9938: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbe993c: LoadField: r1 = r0->field_7
    //     0xbe993c: ldur            w1, [x0, #7]
    // 0xbe9940: DecompressPointer r1
    //     0xbe9940: add             x1, x1, HEAP, lsl #32
    // 0xbe9944: LoadField: d0 = r1->field_7
    //     0xbe9944: ldur            d0, [x1, #7]
    // 0xbe9948: d1 = 0.450000
    //     0xbe9948: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xbe994c: ldr             d1, [x17, #0xd08]
    // 0xbe9950: fmul            d2, d0, d1
    // 0xbe9954: ldur            x0, [fp, #-0x10]
    // 0xbe9958: stur            d2, [fp, #-0x40]
    // 0xbe995c: cbnz            w0, #0xbe9968
    // 0xbe9960: d0 = 0.000000
    //     0xbe9960: eor             v0.16b, v0.16b, v0.16b
    // 0xbe9964: b               #0xbe996c
    // 0xbe9968: d0 = 2.000000
    //     0xbe9968: fmov            d0, #2.00000000
    // 0xbe996c: ldur            x1, [fp, #-0x18]
    // 0xbe9970: stur            d0, [fp, #-0x38]
    // 0xbe9974: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbe9974: ldur            w2, [x1, #0x17]
    // 0xbe9978: DecompressPointer r2
    //     0xbe9978: add             x2, x2, HEAP, lsl #32
    // 0xbe997c: LoadField: r3 = r2->field_b
    //     0xbe997c: ldur            w3, [x2, #0xb]
    // 0xbe9980: r2 = LoadInt32Instr(r3)
    //     0xbe9980: sbfx            x2, x3, #1, #0x1f
    // 0xbe9984: sub             x3, x2, #1
    // 0xbe9988: lsl             x2, x3, #1
    // 0xbe998c: cmp             w0, w2
    // 0xbe9990: b.ne            #0xbe999c
    // 0xbe9994: d1 = 0.000000
    //     0xbe9994: eor             v1.16b, v1.16b, v1.16b
    // 0xbe9998: b               #0xbe99a0
    // 0xbe999c: d1 = 2.000000
    //     0xbe999c: fmov            d1, #2.00000000
    // 0xbe99a0: ldur            x2, [fp, #-8]
    // 0xbe99a4: stur            d1, [fp, #-0x30]
    // 0xbe99a8: r0 = EdgeInsets()
    //     0xbe99a8: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xbe99ac: ldur            d0, [fp, #-0x38]
    // 0xbe99b0: stur            x0, [fp, #-0x20]
    // 0xbe99b4: StoreField: r0->field_7 = d0
    //     0xbe99b4: stur            d0, [x0, #7]
    // 0xbe99b8: StoreField: r0->field_f = rZR
    //     0xbe99b8: stur            xzr, [x0, #0xf]
    // 0xbe99bc: ldur            d0, [fp, #-0x30]
    // 0xbe99c0: ArrayStore: r0[0] = d0  ; List_8
    //     0xbe99c0: stur            d0, [x0, #0x17]
    // 0xbe99c4: StoreField: r0->field_1f = rZR
    //     0xbe99c4: stur            xzr, [x0, #0x1f]
    // 0xbe99c8: ldur            x1, [fp, #-0x18]
    // 0xbe99cc: LoadField: r2 = r1->field_b
    //     0xbe99cc: ldur            w2, [x1, #0xb]
    // 0xbe99d0: DecompressPointer r2
    //     0xbe99d0: add             x2, x2, HEAP, lsl #32
    // 0xbe99d4: LoadField: r3 = r2->field_f
    //     0xbe99d4: ldur            w3, [x2, #0xf]
    // 0xbe99d8: DecompressPointer r3
    //     0xbe99d8: add             x3, x3, HEAP, lsl #32
    // 0xbe99dc: LoadField: r2 = r1->field_13
    //     0xbe99dc: ldur            w2, [x1, #0x13]
    // 0xbe99e0: DecompressPointer r2
    //     0xbe99e0: add             x2, x2, HEAP, lsl #32
    // 0xbe99e4: ldur            x1, [fp, #-0x10]
    // 0xbe99e8: cmp             w1, NULL
    // 0xbe99ec: b.eq            #0xbe9bf8
    // 0xbe99f0: r4 = LoadInt32Instr(r2)
    //     0xbe99f0: sbfx            x4, x2, #1, #0x1f
    //     0xbe99f4: tbz             w2, #0, #0xbe99fc
    //     0xbe99f8: ldur            x4, [x2, #7]
    // 0xbe99fc: r2 = LoadInt32Instr(r1)
    //     0xbe99fc: sbfx            x2, x1, #1, #0x1f
    //     0xbe9a00: tbz             w1, #0, #0xbe9a08
    //     0xbe9a04: ldur            x2, [x1, #7]
    // 0xbe9a08: add             x1, x4, x2
    // 0xbe9a0c: mov             x16, x1
    // 0xbe9a10: mov             x1, x3
    // 0xbe9a14: mov             x3, x16
    // 0xbe9a18: ldur            x2, [fp, #-8]
    // 0xbe9a1c: r0 = lineThemeSlider()
    //     0xbe9a1c: bl              #0xbe9c18  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::lineThemeSlider
    // 0xbe9a20: r1 = Null
    //     0xbe9a20: mov             x1, NULL
    // 0xbe9a24: r2 = 2
    //     0xbe9a24: movz            x2, #0x2
    // 0xbe9a28: stur            x0, [fp, #-0x10]
    // 0xbe9a2c: r0 = AllocateArray()
    //     0xbe9a2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe9a30: mov             x2, x0
    // 0xbe9a34: ldur            x0, [fp, #-0x10]
    // 0xbe9a38: stur            x2, [fp, #-0x18]
    // 0xbe9a3c: StoreField: r2->field_f = r0
    //     0xbe9a3c: stur            w0, [x2, #0xf]
    // 0xbe9a40: r1 = <Widget>
    //     0xbe9a40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe9a44: r0 = AllocateGrowableArray()
    //     0xbe9a44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe9a48: mov             x1, x0
    // 0xbe9a4c: ldur            x0, [fp, #-0x18]
    // 0xbe9a50: stur            x1, [fp, #-0x10]
    // 0xbe9a54: StoreField: r1->field_f = r0
    //     0xbe9a54: stur            w0, [x1, #0xf]
    // 0xbe9a58: r0 = 2
    //     0xbe9a58: movz            x0, #0x2
    // 0xbe9a5c: StoreField: r1->field_b = r0
    //     0xbe9a5c: stur            w0, [x1, #0xb]
    // 0xbe9a60: ldur            x0, [fp, #-8]
    // 0xbe9a64: cmp             w0, NULL
    // 0xbe9a68: b.eq            #0xbe9bfc
    // 0xbe9a6c: LoadField: r2 = r0->field_bb
    //     0xbe9a6c: ldur            w2, [x0, #0xbb]
    // 0xbe9a70: DecompressPointer r2
    //     0xbe9a70: add             x2, x2, HEAP, lsl #32
    // 0xbe9a74: cmp             w2, NULL
    // 0xbe9a78: b.ne            #0xbe9a84
    // 0xbe9a7c: mov             x2, x1
    // 0xbe9a80: b               #0xbe9b44
    // 0xbe9a84: tbnz            w2, #4, #0xbe9b40
    // 0xbe9a88: r0 = SvgPicture()
    //     0xbe9a88: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbe9a8c: mov             x1, x0
    // 0xbe9a90: r2 = "assets/images/free-gift-icon.svg"
    //     0xbe9a90: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xbe9a94: ldr             x2, [x2, #0xd40]
    // 0xbe9a98: stur            x0, [fp, #-8]
    // 0xbe9a9c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbe9a9c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbe9aa0: r0 = SvgPicture.asset()
    //     0xbe9aa0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbe9aa4: r0 = Padding()
    //     0xbe9aa4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe9aa8: mov             x2, x0
    // 0xbe9aac: r0 = Instance_EdgeInsets
    //     0xbe9aac: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xbe9ab0: ldr             x0, [x0, #0xd48]
    // 0xbe9ab4: stur            x2, [fp, #-0x18]
    // 0xbe9ab8: StoreField: r2->field_f = r0
    //     0xbe9ab8: stur            w0, [x2, #0xf]
    // 0xbe9abc: ldur            x0, [fp, #-8]
    // 0xbe9ac0: StoreField: r2->field_b = r0
    //     0xbe9ac0: stur            w0, [x2, #0xb]
    // 0xbe9ac4: ldur            x0, [fp, #-0x10]
    // 0xbe9ac8: LoadField: r1 = r0->field_b
    //     0xbe9ac8: ldur            w1, [x0, #0xb]
    // 0xbe9acc: LoadField: r3 = r0->field_f
    //     0xbe9acc: ldur            w3, [x0, #0xf]
    // 0xbe9ad0: DecompressPointer r3
    //     0xbe9ad0: add             x3, x3, HEAP, lsl #32
    // 0xbe9ad4: LoadField: r4 = r3->field_b
    //     0xbe9ad4: ldur            w4, [x3, #0xb]
    // 0xbe9ad8: r3 = LoadInt32Instr(r1)
    //     0xbe9ad8: sbfx            x3, x1, #1, #0x1f
    // 0xbe9adc: stur            x3, [fp, #-0x28]
    // 0xbe9ae0: r1 = LoadInt32Instr(r4)
    //     0xbe9ae0: sbfx            x1, x4, #1, #0x1f
    // 0xbe9ae4: cmp             x3, x1
    // 0xbe9ae8: b.ne            #0xbe9af4
    // 0xbe9aec: mov             x1, x0
    // 0xbe9af0: r0 = _growToNextCapacity()
    //     0xbe9af0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbe9af4: ldur            x2, [fp, #-0x10]
    // 0xbe9af8: ldur            x3, [fp, #-0x28]
    // 0xbe9afc: add             x0, x3, #1
    // 0xbe9b00: lsl             x1, x0, #1
    // 0xbe9b04: StoreField: r2->field_b = r1
    //     0xbe9b04: stur            w1, [x2, #0xb]
    // 0xbe9b08: LoadField: r1 = r2->field_f
    //     0xbe9b08: ldur            w1, [x2, #0xf]
    // 0xbe9b0c: DecompressPointer r1
    //     0xbe9b0c: add             x1, x1, HEAP, lsl #32
    // 0xbe9b10: ldur            x0, [fp, #-0x18]
    // 0xbe9b14: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbe9b14: add             x25, x1, x3, lsl #2
    //     0xbe9b18: add             x25, x25, #0xf
    //     0xbe9b1c: str             w0, [x25]
    //     0xbe9b20: tbz             w0, #0, #0xbe9b3c
    //     0xbe9b24: ldurb           w16, [x1, #-1]
    //     0xbe9b28: ldurb           w17, [x0, #-1]
    //     0xbe9b2c: and             x16, x17, x16, lsr #2
    //     0xbe9b30: tst             x16, HEAP, lsr #32
    //     0xbe9b34: b.eq            #0xbe9b3c
    //     0xbe9b38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe9b3c: b               #0xbe9b44
    // 0xbe9b40: mov             x2, x1
    // 0xbe9b44: ldur            d0, [fp, #-0x40]
    // 0xbe9b48: ldur            x0, [fp, #-0x20]
    // 0xbe9b4c: r0 = Stack()
    //     0xbe9b4c: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbe9b50: mov             x1, x0
    // 0xbe9b54: r0 = Instance_Alignment
    //     0xbe9b54: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xbe9b58: ldr             x0, [x0, #0x950]
    // 0xbe9b5c: stur            x1, [fp, #-8]
    // 0xbe9b60: StoreField: r1->field_f = r0
    //     0xbe9b60: stur            w0, [x1, #0xf]
    // 0xbe9b64: r0 = Instance_StackFit
    //     0xbe9b64: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbe9b68: ldr             x0, [x0, #0xfa8]
    // 0xbe9b6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe9b6c: stur            w0, [x1, #0x17]
    // 0xbe9b70: r0 = Instance_Clip
    //     0xbe9b70: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbe9b74: ldr             x0, [x0, #0x7e0]
    // 0xbe9b78: StoreField: r1->field_1b = r0
    //     0xbe9b78: stur            w0, [x1, #0x1b]
    // 0xbe9b7c: ldur            x0, [fp, #-0x10]
    // 0xbe9b80: StoreField: r1->field_b = r0
    //     0xbe9b80: stur            w0, [x1, #0xb]
    // 0xbe9b84: r0 = Padding()
    //     0xbe9b84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe9b88: mov             x1, x0
    // 0xbe9b8c: ldur            x0, [fp, #-0x20]
    // 0xbe9b90: stur            x1, [fp, #-0x10]
    // 0xbe9b94: StoreField: r1->field_f = r0
    //     0xbe9b94: stur            w0, [x1, #0xf]
    // 0xbe9b98: ldur            x0, [fp, #-8]
    // 0xbe9b9c: StoreField: r1->field_b = r0
    //     0xbe9b9c: stur            w0, [x1, #0xb]
    // 0xbe9ba0: ldur            d0, [fp, #-0x40]
    // 0xbe9ba4: r0 = inline_Allocate_Double()
    //     0xbe9ba4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbe9ba8: add             x0, x0, #0x10
    //     0xbe9bac: cmp             x2, x0
    //     0xbe9bb0: b.ls            #0xbe9c00
    //     0xbe9bb4: str             x0, [THR, #0x50]  ; THR::top
    //     0xbe9bb8: sub             x0, x0, #0xf
    //     0xbe9bbc: movz            x2, #0xe15c
    //     0xbe9bc0: movk            x2, #0x3, lsl #16
    //     0xbe9bc4: stur            x2, [x0, #-1]
    // 0xbe9bc8: StoreField: r0->field_7 = d0
    //     0xbe9bc8: stur            d0, [x0, #7]
    // 0xbe9bcc: stur            x0, [fp, #-8]
    // 0xbe9bd0: r0 = SizedBox()
    //     0xbe9bd0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbe9bd4: ldur            x1, [fp, #-8]
    // 0xbe9bd8: StoreField: r0->field_f = r1
    //     0xbe9bd8: stur            w1, [x0, #0xf]
    // 0xbe9bdc: ldur            x1, [fp, #-0x10]
    // 0xbe9be0: StoreField: r0->field_b = r1
    //     0xbe9be0: stur            w1, [x0, #0xb]
    // 0xbe9be4: LeaveFrame
    //     0xbe9be4: mov             SP, fp
    //     0xbe9be8: ldp             fp, lr, [SP], #0x10
    // 0xbe9bec: ret
    //     0xbe9bec: ret             
    // 0xbe9bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe9bf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe9bf4: b               #0xbe9910
    // 0xbe9bf8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbe9bf8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xbe9bfc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbe9bfc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xbe9c00: SaveReg d0
    //     0xbe9c00: str             q0, [SP, #-0x10]!
    // 0xbe9c04: SaveReg r1
    //     0xbe9c04: str             x1, [SP, #-8]!
    // 0xbe9c08: r0 = AllocateDouble()
    //     0xbe9c08: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbe9c0c: RestoreReg r1
    //     0xbe9c0c: ldr             x1, [SP], #8
    // 0xbe9c10: RestoreReg d0
    //     0xbe9c10: ldr             q0, [SP], #0x10
    // 0xbe9c14: b               #0xbe9bc8
  }
  _ lineThemeSlider(/* No info */) {
    // ** addr: 0xbe9c18, size: 0x768
    // 0xbe9c18: EnterFrame
    //     0xbe9c18: stp             fp, lr, [SP, #-0x10]!
    //     0xbe9c1c: mov             fp, SP
    // 0xbe9c20: AllocStack(0x70)
    //     0xbe9c20: sub             SP, SP, #0x70
    // 0xbe9c24: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbe9c24: stur            x1, [fp, #-8]
    //     0xbe9c28: stur            x2, [fp, #-0x10]
    //     0xbe9c2c: stur            x3, [fp, #-0x18]
    // 0xbe9c30: CheckStackOverflow
    //     0xbe9c30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe9c34: cmp             SP, x16
    //     0xbe9c38: b.ls            #0xbea35c
    // 0xbe9c3c: r1 = 2
    //     0xbe9c3c: movz            x1, #0x2
    // 0xbe9c40: r0 = AllocateContext()
    //     0xbe9c40: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe9c44: mov             x3, x0
    // 0xbe9c48: ldur            x0, [fp, #-8]
    // 0xbe9c4c: stur            x3, [fp, #-0x28]
    // 0xbe9c50: StoreField: r3->field_f = r0
    //     0xbe9c50: stur            w0, [x3, #0xf]
    // 0xbe9c54: ldur            x1, [fp, #-0x10]
    // 0xbe9c58: StoreField: r3->field_13 = r1
    //     0xbe9c58: stur            w1, [x3, #0x13]
    // 0xbe9c5c: LoadField: r2 = r1->field_37
    //     0xbe9c5c: ldur            w2, [x1, #0x37]
    // 0xbe9c60: DecompressPointer r2
    //     0xbe9c60: add             x2, x2, HEAP, lsl #32
    // 0xbe9c64: cmp             w2, NULL
    // 0xbe9c68: b.ne            #0xbe9c74
    // 0xbe9c6c: r1 = Null
    //     0xbe9c6c: mov             x1, NULL
    // 0xbe9c70: b               #0xbe9c78
    // 0xbe9c74: LoadField: r1 = r2->field_b
    //     0xbe9c74: ldur            w1, [x2, #0xb]
    // 0xbe9c78: cmp             w1, NULL
    // 0xbe9c7c: b.ne            #0xbe9c88
    // 0xbe9c80: r1 = 0
    //     0xbe9c80: movz            x1, #0
    // 0xbe9c84: b               #0xbe9c90
    // 0xbe9c88: r2 = LoadInt32Instr(r1)
    //     0xbe9c88: sbfx            x2, x1, #1, #0x1f
    // 0xbe9c8c: mov             x1, x2
    // 0xbe9c90: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xbe9c90: ldur            w4, [x0, #0x17]
    // 0xbe9c94: DecompressPointer r4
    //     0xbe9c94: add             x4, x4, HEAP, lsl #32
    // 0xbe9c98: r16 = Sentinel
    //     0xbe9c98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe9c9c: cmp             w4, w16
    // 0xbe9ca0: b.eq            #0xbea364
    // 0xbe9ca4: stur            x4, [fp, #-0x20]
    // 0xbe9ca8: lsl             x5, x1, #1
    // 0xbe9cac: mov             x2, x3
    // 0xbe9cb0: stur            x5, [fp, #-0x10]
    // 0xbe9cb4: r1 = Function '<anonymous closure>':.
    //     0xbe9cb4: add             x1, PP, #0x53, lsl #12  ; [pp+0x536c0] AnonymousClosure: (0xbec144), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::lineThemeSlider (0xbe9c18)
    //     0xbe9cb8: ldr             x1, [x1, #0x6c0]
    // 0xbe9cbc: r0 = AllocateClosure()
    //     0xbe9cbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe9cc0: ldur            x2, [fp, #-0x28]
    // 0xbe9cc4: r1 = Function '<anonymous closure>':.
    //     0xbe9cc4: add             x1, PP, #0x53, lsl #12  ; [pp+0x536c8] AnonymousClosure: (0xbeb108), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::lineThemeSlider (0xbe9c18)
    //     0xbe9cc8: ldr             x1, [x1, #0x6c8]
    // 0xbe9ccc: stur            x0, [fp, #-0x30]
    // 0xbe9cd0: r0 = AllocateClosure()
    //     0xbe9cd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe9cd4: stur            x0, [fp, #-0x38]
    // 0xbe9cd8: r0 = PageView()
    //     0xbe9cd8: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xbe9cdc: stur            x0, [fp, #-0x40]
    // 0xbe9ce0: ldur            x16, [fp, #-0x20]
    // 0xbe9ce4: str             x16, [SP]
    // 0xbe9ce8: mov             x1, x0
    // 0xbe9cec: ldur            x2, [fp, #-0x38]
    // 0xbe9cf0: ldur            x3, [fp, #-0x10]
    // 0xbe9cf4: ldur            x5, [fp, #-0x30]
    // 0xbe9cf8: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xbe9cf8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xbe9cfc: ldr             x4, [x4, #0xd60]
    // 0xbe9d00: r0 = PageView.builder()
    //     0xbe9d00: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xbe9d04: r0 = AspectRatio()
    //     0xbe9d04: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xbe9d08: d0 = 0.740741
    //     0xbe9d08: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d68] IMM: double(0.7407407407407407) from 0x3fe7b425ed097b42
    //     0xbe9d0c: ldr             d0, [x17, #0xd68]
    // 0xbe9d10: stur            x0, [fp, #-0x20]
    // 0xbe9d14: StoreField: r0->field_f = d0
    //     0xbe9d14: stur            d0, [x0, #0xf]
    // 0xbe9d18: ldur            x1, [fp, #-0x40]
    // 0xbe9d1c: StoreField: r0->field_b = r1
    //     0xbe9d1c: stur            w1, [x0, #0xb]
    // 0xbe9d20: ldur            x2, [fp, #-0x28]
    // 0xbe9d24: LoadField: r1 = r2->field_13
    //     0xbe9d24: ldur            w1, [x2, #0x13]
    // 0xbe9d28: DecompressPointer r1
    //     0xbe9d28: add             x1, x1, HEAP, lsl #32
    // 0xbe9d2c: LoadField: r3 = r1->field_33
    //     0xbe9d2c: ldur            w3, [x1, #0x33]
    // 0xbe9d30: DecompressPointer r3
    //     0xbe9d30: add             x3, x3, HEAP, lsl #32
    // 0xbe9d34: cmp             w3, NULL
    // 0xbe9d38: b.ne            #0xbe9d44
    // 0xbe9d3c: r1 = Null
    //     0xbe9d3c: mov             x1, NULL
    // 0xbe9d40: b               #0xbe9d4c
    // 0xbe9d44: LoadField: r1 = r3->field_7
    //     0xbe9d44: ldur            w1, [x3, #7]
    // 0xbe9d48: DecompressPointer r1
    //     0xbe9d48: add             x1, x1, HEAP, lsl #32
    // 0xbe9d4c: cmp             w1, NULL
    // 0xbe9d50: b.ne            #0xbe9d5c
    // 0xbe9d54: r4 = ""
    //     0xbe9d54: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe9d58: b               #0xbe9d60
    // 0xbe9d5c: mov             x4, x1
    // 0xbe9d60: ldur            x3, [fp, #-8]
    // 0xbe9d64: stur            x4, [fp, #-0x10]
    // 0xbe9d68: LoadField: r1 = r3->field_f
    //     0xbe9d68: ldur            w1, [x3, #0xf]
    // 0xbe9d6c: DecompressPointer r1
    //     0xbe9d6c: add             x1, x1, HEAP, lsl #32
    // 0xbe9d70: cmp             w1, NULL
    // 0xbe9d74: b.eq            #0xbea370
    // 0xbe9d78: r0 = of()
    //     0xbe9d78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe9d7c: LoadField: r1 = r0->field_87
    //     0xbe9d7c: ldur            w1, [x0, #0x87]
    // 0xbe9d80: DecompressPointer r1
    //     0xbe9d80: add             x1, x1, HEAP, lsl #32
    // 0xbe9d84: LoadField: r0 = r1->field_2b
    //     0xbe9d84: ldur            w0, [x1, #0x2b]
    // 0xbe9d88: DecompressPointer r0
    //     0xbe9d88: add             x0, x0, HEAP, lsl #32
    // 0xbe9d8c: stur            x0, [fp, #-0x30]
    // 0xbe9d90: r1 = Instance_Color
    //     0xbe9d90: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe9d94: d0 = 0.700000
    //     0xbe9d94: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbe9d98: ldr             d0, [x17, #0xf48]
    // 0xbe9d9c: r0 = withOpacity()
    //     0xbe9d9c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe9da0: r16 = 12.000000
    //     0xbe9da0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe9da4: ldr             x16, [x16, #0x9e8]
    // 0xbe9da8: stp             x0, x16, [SP]
    // 0xbe9dac: ldur            x1, [fp, #-0x30]
    // 0xbe9db0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe9db0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe9db4: ldr             x4, [x4, #0xaa0]
    // 0xbe9db8: r0 = copyWith()
    //     0xbe9db8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe9dbc: stur            x0, [fp, #-0x30]
    // 0xbe9dc0: r0 = Text()
    //     0xbe9dc0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe9dc4: mov             x3, x0
    // 0xbe9dc8: ldur            x0, [fp, #-0x10]
    // 0xbe9dcc: stur            x3, [fp, #-0x38]
    // 0xbe9dd0: StoreField: r3->field_b = r0
    //     0xbe9dd0: stur            w0, [x3, #0xb]
    // 0xbe9dd4: ldur            x0, [fp, #-0x30]
    // 0xbe9dd8: StoreField: r3->field_13 = r0
    //     0xbe9dd8: stur            w0, [x3, #0x13]
    // 0xbe9ddc: r0 = Instance_TextOverflow
    //     0xbe9ddc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbe9de0: ldr             x0, [x0, #0xe10]
    // 0xbe9de4: StoreField: r3->field_2b = r0
    //     0xbe9de4: stur            w0, [x3, #0x2b]
    // 0xbe9de8: r0 = 2
    //     0xbe9de8: movz            x0, #0x2
    // 0xbe9dec: StoreField: r3->field_37 = r0
    //     0xbe9dec: stur            w0, [x3, #0x37]
    // 0xbe9df0: ldur            x0, [fp, #-0x28]
    // 0xbe9df4: LoadField: r2 = r0->field_13
    //     0xbe9df4: ldur            w2, [x0, #0x13]
    // 0xbe9df8: DecompressPointer r2
    //     0xbe9df8: add             x2, x2, HEAP, lsl #32
    // 0xbe9dfc: ldur            x1, [fp, #-8]
    // 0xbe9e00: r0 = _buildRatingWidget()
    //     0xbe9e00: bl              #0xbea9f0  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildRatingWidget
    // 0xbe9e04: mov             x3, x0
    // 0xbe9e08: ldur            x0, [fp, #-0x28]
    // 0xbe9e0c: stur            x3, [fp, #-0x30]
    // 0xbe9e10: LoadField: r1 = r0->field_13
    //     0xbe9e10: ldur            w1, [x0, #0x13]
    // 0xbe9e14: DecompressPointer r1
    //     0xbe9e14: add             x1, x1, HEAP, lsl #32
    // 0xbe9e18: LoadField: r4 = r1->field_43
    //     0xbe9e18: ldur            w4, [x1, #0x43]
    // 0xbe9e1c: DecompressPointer r4
    //     0xbe9e1c: add             x4, x4, HEAP, lsl #32
    // 0xbe9e20: stur            x4, [fp, #-0x10]
    // 0xbe9e24: r1 = Null
    //     0xbe9e24: mov             x1, NULL
    // 0xbe9e28: r2 = 4
    //     0xbe9e28: movz            x2, #0x4
    // 0xbe9e2c: r0 = AllocateArray()
    //     0xbe9e2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe9e30: mov             x1, x0
    // 0xbe9e34: ldur            x0, [fp, #-0x10]
    // 0xbe9e38: StoreField: r1->field_f = r0
    //     0xbe9e38: stur            w0, [x1, #0xf]
    // 0xbe9e3c: r16 = "  "
    //     0xbe9e3c: ldr             x16, [PP, #0xc58]  ; [pp+0xc58] "  "
    // 0xbe9e40: StoreField: r1->field_13 = r16
    //     0xbe9e40: stur            w16, [x1, #0x13]
    // 0xbe9e44: str             x1, [SP]
    // 0xbe9e48: r0 = _interpolate()
    //     0xbe9e48: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbe9e4c: mov             x2, x0
    // 0xbe9e50: ldur            x0, [fp, #-8]
    // 0xbe9e54: stur            x2, [fp, #-0x10]
    // 0xbe9e58: LoadField: r1 = r0->field_f
    //     0xbe9e58: ldur            w1, [x0, #0xf]
    // 0xbe9e5c: DecompressPointer r1
    //     0xbe9e5c: add             x1, x1, HEAP, lsl #32
    // 0xbe9e60: cmp             w1, NULL
    // 0xbe9e64: b.eq            #0xbea374
    // 0xbe9e68: r0 = of()
    //     0xbe9e68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe9e6c: LoadField: r1 = r0->field_87
    //     0xbe9e6c: ldur            w1, [x0, #0x87]
    // 0xbe9e70: DecompressPointer r1
    //     0xbe9e70: add             x1, x1, HEAP, lsl #32
    // 0xbe9e74: LoadField: r0 = r1->field_7
    //     0xbe9e74: ldur            w0, [x1, #7]
    // 0xbe9e78: DecompressPointer r0
    //     0xbe9e78: add             x0, x0, HEAP, lsl #32
    // 0xbe9e7c: r16 = 14.000000
    //     0xbe9e7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbe9e80: ldr             x16, [x16, #0x1d8]
    // 0xbe9e84: r30 = Instance_Color
    //     0xbe9e84: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe9e88: stp             lr, x16, [SP]
    // 0xbe9e8c: mov             x1, x0
    // 0xbe9e90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe9e90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe9e94: ldr             x4, [x4, #0xaa0]
    // 0xbe9e98: r0 = copyWith()
    //     0xbe9e98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe9e9c: stur            x0, [fp, #-0x40]
    // 0xbe9ea0: r0 = Text()
    //     0xbe9ea0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe9ea4: mov             x1, x0
    // 0xbe9ea8: ldur            x0, [fp, #-0x10]
    // 0xbe9eac: stur            x1, [fp, #-0x48]
    // 0xbe9eb0: StoreField: r1->field_b = r0
    //     0xbe9eb0: stur            w0, [x1, #0xb]
    // 0xbe9eb4: ldur            x0, [fp, #-0x40]
    // 0xbe9eb8: StoreField: r1->field_13 = r0
    //     0xbe9eb8: stur            w0, [x1, #0x13]
    // 0xbe9ebc: r0 = WidgetSpan()
    //     0xbe9ebc: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xbe9ec0: mov             x1, x0
    // 0xbe9ec4: ldur            x0, [fp, #-0x48]
    // 0xbe9ec8: stur            x1, [fp, #-0x10]
    // 0xbe9ecc: StoreField: r1->field_13 = r0
    //     0xbe9ecc: stur            w0, [x1, #0x13]
    // 0xbe9ed0: r0 = Instance_PlaceholderAlignment
    //     0xbe9ed0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xbe9ed4: ldr             x0, [x0, #0xa0]
    // 0xbe9ed8: StoreField: r1->field_b = r0
    //     0xbe9ed8: stur            w0, [x1, #0xb]
    // 0xbe9edc: ldur            x2, [fp, #-0x28]
    // 0xbe9ee0: LoadField: r0 = r2->field_13
    //     0xbe9ee0: ldur            w0, [x2, #0x13]
    // 0xbe9ee4: DecompressPointer r0
    //     0xbe9ee4: add             x0, x0, HEAP, lsl #32
    // 0xbe9ee8: LoadField: r3 = r0->field_4b
    //     0xbe9ee8: ldur            w3, [x0, #0x4b]
    // 0xbe9eec: DecompressPointer r3
    //     0xbe9eec: add             x3, x3, HEAP, lsl #32
    // 0xbe9ef0: str             x3, [SP]
    // 0xbe9ef4: r0 = _interpolateSingle()
    //     0xbe9ef4: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbe9ef8: mov             x2, x0
    // 0xbe9efc: ldur            x0, [fp, #-8]
    // 0xbe9f00: stur            x2, [fp, #-0x40]
    // 0xbe9f04: LoadField: r1 = r0->field_f
    //     0xbe9f04: ldur            w1, [x0, #0xf]
    // 0xbe9f08: DecompressPointer r1
    //     0xbe9f08: add             x1, x1, HEAP, lsl #32
    // 0xbe9f0c: cmp             w1, NULL
    // 0xbe9f10: b.eq            #0xbea378
    // 0xbe9f14: r0 = of()
    //     0xbe9f14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbe9f18: LoadField: r1 = r0->field_87
    //     0xbe9f18: ldur            w1, [x0, #0x87]
    // 0xbe9f1c: DecompressPointer r1
    //     0xbe9f1c: add             x1, x1, HEAP, lsl #32
    // 0xbe9f20: LoadField: r0 = r1->field_2b
    //     0xbe9f20: ldur            w0, [x1, #0x2b]
    // 0xbe9f24: DecompressPointer r0
    //     0xbe9f24: add             x0, x0, HEAP, lsl #32
    // 0xbe9f28: stur            x0, [fp, #-0x48]
    // 0xbe9f2c: r1 = Instance_Color
    //     0xbe9f2c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe9f30: d0 = 0.400000
    //     0xbe9f30: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbe9f34: r0 = withOpacity()
    //     0xbe9f34: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbe9f38: r16 = 12.000000
    //     0xbe9f38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbe9f3c: ldr             x16, [x16, #0x9e8]
    // 0xbe9f40: stp             x16, x0, [SP, #8]
    // 0xbe9f44: r16 = Instance_TextDecoration
    //     0xbe9f44: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbe9f48: ldr             x16, [x16, #0xe30]
    // 0xbe9f4c: str             x16, [SP]
    // 0xbe9f50: ldur            x1, [fp, #-0x48]
    // 0xbe9f54: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xbe9f54: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xbe9f58: ldr             x4, [x4, #0x7c8]
    // 0xbe9f5c: r0 = copyWith()
    //     0xbe9f5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe9f60: stur            x0, [fp, #-0x48]
    // 0xbe9f64: r0 = TextSpan()
    //     0xbe9f64: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbe9f68: mov             x3, x0
    // 0xbe9f6c: ldur            x0, [fp, #-0x40]
    // 0xbe9f70: stur            x3, [fp, #-0x50]
    // 0xbe9f74: StoreField: r3->field_b = r0
    //     0xbe9f74: stur            w0, [x3, #0xb]
    // 0xbe9f78: r0 = Instance__DeferringMouseCursor
    //     0xbe9f78: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbe9f7c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe9f7c: stur            w0, [x3, #0x17]
    // 0xbe9f80: ldur            x1, [fp, #-0x48]
    // 0xbe9f84: StoreField: r3->field_7 = r1
    //     0xbe9f84: stur            w1, [x3, #7]
    // 0xbe9f88: r1 = Null
    //     0xbe9f88: mov             x1, NULL
    // 0xbe9f8c: r2 = 4
    //     0xbe9f8c: movz            x2, #0x4
    // 0xbe9f90: r0 = AllocateArray()
    //     0xbe9f90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe9f94: mov             x2, x0
    // 0xbe9f98: ldur            x0, [fp, #-0x10]
    // 0xbe9f9c: stur            x2, [fp, #-0x40]
    // 0xbe9fa0: StoreField: r2->field_f = r0
    //     0xbe9fa0: stur            w0, [x2, #0xf]
    // 0xbe9fa4: ldur            x0, [fp, #-0x50]
    // 0xbe9fa8: StoreField: r2->field_13 = r0
    //     0xbe9fa8: stur            w0, [x2, #0x13]
    // 0xbe9fac: r1 = <InlineSpan>
    //     0xbe9fac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbe9fb0: ldr             x1, [x1, #0xe40]
    // 0xbe9fb4: r0 = AllocateGrowableArray()
    //     0xbe9fb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe9fb8: mov             x1, x0
    // 0xbe9fbc: ldur            x0, [fp, #-0x40]
    // 0xbe9fc0: stur            x1, [fp, #-0x10]
    // 0xbe9fc4: StoreField: r1->field_f = r0
    //     0xbe9fc4: stur            w0, [x1, #0xf]
    // 0xbe9fc8: r0 = 4
    //     0xbe9fc8: movz            x0, #0x4
    // 0xbe9fcc: StoreField: r1->field_b = r0
    //     0xbe9fcc: stur            w0, [x1, #0xb]
    // 0xbe9fd0: ldur            x2, [fp, #-0x28]
    // 0xbe9fd4: LoadField: r0 = r2->field_13
    //     0xbe9fd4: ldur            w0, [x2, #0x13]
    // 0xbe9fd8: DecompressPointer r0
    //     0xbe9fd8: add             x0, x0, HEAP, lsl #32
    // 0xbe9fdc: LoadField: r3 = r0->field_63
    //     0xbe9fdc: ldur            w3, [x0, #0x63]
    // 0xbe9fe0: DecompressPointer r3
    //     0xbe9fe0: add             x3, x3, HEAP, lsl #32
    // 0xbe9fe4: r0 = 60
    //     0xbe9fe4: movz            x0, #0x3c
    // 0xbe9fe8: branchIfSmi(r3, 0xbe9ff4)
    //     0xbe9fe8: tbz             w3, #0, #0xbe9ff4
    // 0xbe9fec: r0 = LoadClassIdInstr(r3)
    //     0xbe9fec: ldur            x0, [x3, #-1]
    //     0xbe9ff0: ubfx            x0, x0, #0xc, #0x14
    // 0xbe9ff4: r16 = 0.000000
    //     0xbe9ff4: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbe9ff8: stp             x16, x3, [SP]
    // 0xbe9ffc: mov             lr, x0
    // 0xbea000: ldr             lr, [x21, lr, lsl #3]
    // 0xbea004: blr             lr
    // 0xbea008: tbz             w0, #4, #0xbea19c
    // 0xbea00c: ldur            x4, [fp, #-8]
    // 0xbea010: ldur            x3, [fp, #-0x28]
    // 0xbea014: ldur            x0, [fp, #-0x10]
    // 0xbea018: r1 = Null
    //     0xbea018: mov             x1, NULL
    // 0xbea01c: r2 = 6
    //     0xbea01c: movz            x2, #0x6
    // 0xbea020: r0 = AllocateArray()
    //     0xbea020: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbea024: stur            x0, [fp, #-0x40]
    // 0xbea028: r16 = " | "
    //     0xbea028: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d80] " | "
    //     0xbea02c: ldr             x16, [x16, #0xd80]
    // 0xbea030: StoreField: r0->field_f = r16
    //     0xbea030: stur            w16, [x0, #0xf]
    // 0xbea034: ldur            x2, [fp, #-0x28]
    // 0xbea038: LoadField: r1 = r2->field_13
    //     0xbea038: ldur            w1, [x2, #0x13]
    // 0xbea03c: DecompressPointer r1
    //     0xbea03c: add             x1, x1, HEAP, lsl #32
    // 0xbea040: LoadField: r3 = r1->field_63
    //     0xbea040: ldur            w3, [x1, #0x63]
    // 0xbea044: DecompressPointer r3
    //     0xbea044: add             x3, x3, HEAP, lsl #32
    // 0xbea048: stp             xzr, x3, [SP]
    // 0xbea04c: r4 = 0
    //     0xbea04c: movz            x4, #0
    // 0xbea050: ldr             x0, [SP, #8]
    // 0xbea054: r16 = UnlinkedCall_0x613b5c
    //     0xbea054: add             x16, PP, #0x53, lsl #12  ; [pp+0x536d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbea058: add             x16, x16, #0x6d0
    // 0xbea05c: ldp             x5, lr, [x16]
    // 0xbea060: blr             lr
    // 0xbea064: ldur            x1, [fp, #-0x40]
    // 0xbea068: ArrayStore: r1[1] = r0  ; List_4
    //     0xbea068: add             x25, x1, #0x13
    //     0xbea06c: str             w0, [x25]
    //     0xbea070: tbz             w0, #0, #0xbea08c
    //     0xbea074: ldurb           w16, [x1, #-1]
    //     0xbea078: ldurb           w17, [x0, #-1]
    //     0xbea07c: and             x16, x17, x16, lsr #2
    //     0xbea080: tst             x16, HEAP, lsr #32
    //     0xbea084: b.eq            #0xbea08c
    //     0xbea088: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbea08c: ldur            x0, [fp, #-0x40]
    // 0xbea090: r16 = "% OFF"
    //     0xbea090: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xbea094: ldr             x16, [x16, #0xd98]
    // 0xbea098: ArrayStore: r0[0] = r16  ; List_4
    //     0xbea098: stur            w16, [x0, #0x17]
    // 0xbea09c: str             x0, [SP]
    // 0xbea0a0: r0 = _interpolate()
    //     0xbea0a0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbea0a4: mov             x2, x0
    // 0xbea0a8: ldur            x0, [fp, #-8]
    // 0xbea0ac: stur            x2, [fp, #-0x40]
    // 0xbea0b0: LoadField: r1 = r0->field_f
    //     0xbea0b0: ldur            w1, [x0, #0xf]
    // 0xbea0b4: DecompressPointer r1
    //     0xbea0b4: add             x1, x1, HEAP, lsl #32
    // 0xbea0b8: cmp             w1, NULL
    // 0xbea0bc: b.eq            #0xbea37c
    // 0xbea0c0: r0 = of()
    //     0xbea0c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbea0c4: LoadField: r1 = r0->field_87
    //     0xbea0c4: ldur            w1, [x0, #0x87]
    // 0xbea0c8: DecompressPointer r1
    //     0xbea0c8: add             x1, x1, HEAP, lsl #32
    // 0xbea0cc: LoadField: r0 = r1->field_2b
    //     0xbea0cc: ldur            w0, [x1, #0x2b]
    // 0xbea0d0: DecompressPointer r0
    //     0xbea0d0: add             x0, x0, HEAP, lsl #32
    // 0xbea0d4: r16 = Instance_Color
    //     0xbea0d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbea0d8: ldr             x16, [x16, #0x858]
    // 0xbea0dc: r30 = 12.000000
    //     0xbea0dc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbea0e0: ldr             lr, [lr, #0x9e8]
    // 0xbea0e4: stp             lr, x16, [SP]
    // 0xbea0e8: mov             x1, x0
    // 0xbea0ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbea0ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbea0f0: ldr             x4, [x4, #0x9b8]
    // 0xbea0f4: r0 = copyWith()
    //     0xbea0f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbea0f8: stur            x0, [fp, #-0x48]
    // 0xbea0fc: r0 = TextSpan()
    //     0xbea0fc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbea100: mov             x2, x0
    // 0xbea104: ldur            x0, [fp, #-0x40]
    // 0xbea108: stur            x2, [fp, #-0x50]
    // 0xbea10c: StoreField: r2->field_b = r0
    //     0xbea10c: stur            w0, [x2, #0xb]
    // 0xbea110: r0 = Instance__DeferringMouseCursor
    //     0xbea110: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbea114: ArrayStore: r2[0] = r0  ; List_4
    //     0xbea114: stur            w0, [x2, #0x17]
    // 0xbea118: ldur            x1, [fp, #-0x48]
    // 0xbea11c: StoreField: r2->field_7 = r1
    //     0xbea11c: stur            w1, [x2, #7]
    // 0xbea120: ldur            x3, [fp, #-0x10]
    // 0xbea124: LoadField: r1 = r3->field_b
    //     0xbea124: ldur            w1, [x3, #0xb]
    // 0xbea128: LoadField: r4 = r3->field_f
    //     0xbea128: ldur            w4, [x3, #0xf]
    // 0xbea12c: DecompressPointer r4
    //     0xbea12c: add             x4, x4, HEAP, lsl #32
    // 0xbea130: LoadField: r5 = r4->field_b
    //     0xbea130: ldur            w5, [x4, #0xb]
    // 0xbea134: r4 = LoadInt32Instr(r1)
    //     0xbea134: sbfx            x4, x1, #1, #0x1f
    // 0xbea138: stur            x4, [fp, #-0x58]
    // 0xbea13c: r1 = LoadInt32Instr(r5)
    //     0xbea13c: sbfx            x1, x5, #1, #0x1f
    // 0xbea140: cmp             x4, x1
    // 0xbea144: b.ne            #0xbea150
    // 0xbea148: mov             x1, x3
    // 0xbea14c: r0 = _growToNextCapacity()
    //     0xbea14c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbea150: ldur            x2, [fp, #-0x10]
    // 0xbea154: ldur            x3, [fp, #-0x58]
    // 0xbea158: add             x0, x3, #1
    // 0xbea15c: lsl             x1, x0, #1
    // 0xbea160: StoreField: r2->field_b = r1
    //     0xbea160: stur            w1, [x2, #0xb]
    // 0xbea164: LoadField: r1 = r2->field_f
    //     0xbea164: ldur            w1, [x2, #0xf]
    // 0xbea168: DecompressPointer r1
    //     0xbea168: add             x1, x1, HEAP, lsl #32
    // 0xbea16c: ldur            x0, [fp, #-0x50]
    // 0xbea170: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbea170: add             x25, x1, x3, lsl #2
    //     0xbea174: add             x25, x25, #0xf
    //     0xbea178: str             w0, [x25]
    //     0xbea17c: tbz             w0, #0, #0xbea198
    //     0xbea180: ldurb           w16, [x1, #-1]
    //     0xbea184: ldurb           w17, [x0, #-1]
    //     0xbea188: and             x16, x17, x16, lsr #2
    //     0xbea18c: tst             x16, HEAP, lsr #32
    //     0xbea190: b.eq            #0xbea198
    //     0xbea194: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbea198: b               #0xbea1a0
    // 0xbea19c: ldur            x2, [fp, #-0x10]
    // 0xbea1a0: ldur            x0, [fp, #-0x28]
    // 0xbea1a4: ldur            x4, [fp, #-0x20]
    // 0xbea1a8: ldur            x3, [fp, #-0x38]
    // 0xbea1ac: ldur            x1, [fp, #-0x30]
    // 0xbea1b0: r0 = TextSpan()
    //     0xbea1b0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbea1b4: mov             x1, x0
    // 0xbea1b8: ldur            x0, [fp, #-0x10]
    // 0xbea1bc: stur            x1, [fp, #-0x40]
    // 0xbea1c0: StoreField: r1->field_f = r0
    //     0xbea1c0: stur            w0, [x1, #0xf]
    // 0xbea1c4: r0 = Instance__DeferringMouseCursor
    //     0xbea1c4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbea1c8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbea1c8: stur            w0, [x1, #0x17]
    // 0xbea1cc: r0 = RichText()
    //     0xbea1cc: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbea1d0: mov             x1, x0
    // 0xbea1d4: ldur            x2, [fp, #-0x40]
    // 0xbea1d8: stur            x0, [fp, #-0x10]
    // 0xbea1dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbea1dc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbea1e0: r0 = RichText()
    //     0xbea1e0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbea1e4: r0 = SizedBox()
    //     0xbea1e4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbea1e8: mov             x1, x0
    // 0xbea1ec: r0 = 32.000000
    //     0xbea1ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xbea1f0: ldr             x0, [x0, #0x848]
    // 0xbea1f4: stur            x1, [fp, #-0x40]
    // 0xbea1f8: StoreField: r1->field_13 = r0
    //     0xbea1f8: stur            w0, [x1, #0x13]
    // 0xbea1fc: ldur            x0, [fp, #-0x10]
    // 0xbea200: StoreField: r1->field_b = r0
    //     0xbea200: stur            w0, [x1, #0xb]
    // 0xbea204: r0 = Padding()
    //     0xbea204: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbea208: mov             x4, x0
    // 0xbea20c: r0 = Instance_EdgeInsets
    //     0xbea20c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xbea210: ldr             x0, [x0, #0x990]
    // 0xbea214: stur            x4, [fp, #-0x10]
    // 0xbea218: StoreField: r4->field_f = r0
    //     0xbea218: stur            w0, [x4, #0xf]
    // 0xbea21c: ldur            x0, [fp, #-0x40]
    // 0xbea220: StoreField: r4->field_b = r0
    //     0xbea220: stur            w0, [x4, #0xb]
    // 0xbea224: ldur            x0, [fp, #-0x28]
    // 0xbea228: LoadField: r2 = r0->field_13
    //     0xbea228: ldur            w2, [x0, #0x13]
    // 0xbea22c: DecompressPointer r2
    //     0xbea22c: add             x2, x2, HEAP, lsl #32
    // 0xbea230: ldur            x1, [fp, #-8]
    // 0xbea234: ldur            x3, [fp, #-0x18]
    // 0xbea238: r0 = _buildAddToBagSection()
    //     0xbea238: bl              #0xbea380  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildAddToBagSection
    // 0xbea23c: r1 = Null
    //     0xbea23c: mov             x1, NULL
    // 0xbea240: r2 = 10
    //     0xbea240: movz            x2, #0xa
    // 0xbea244: stur            x0, [fp, #-8]
    // 0xbea248: r0 = AllocateArray()
    //     0xbea248: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbea24c: mov             x2, x0
    // 0xbea250: ldur            x0, [fp, #-0x20]
    // 0xbea254: stur            x2, [fp, #-0x40]
    // 0xbea258: StoreField: r2->field_f = r0
    //     0xbea258: stur            w0, [x2, #0xf]
    // 0xbea25c: ldur            x0, [fp, #-0x38]
    // 0xbea260: StoreField: r2->field_13 = r0
    //     0xbea260: stur            w0, [x2, #0x13]
    // 0xbea264: ldur            x0, [fp, #-0x30]
    // 0xbea268: ArrayStore: r2[0] = r0  ; List_4
    //     0xbea268: stur            w0, [x2, #0x17]
    // 0xbea26c: ldur            x0, [fp, #-0x10]
    // 0xbea270: StoreField: r2->field_1b = r0
    //     0xbea270: stur            w0, [x2, #0x1b]
    // 0xbea274: ldur            x0, [fp, #-8]
    // 0xbea278: StoreField: r2->field_1f = r0
    //     0xbea278: stur            w0, [x2, #0x1f]
    // 0xbea27c: r1 = <Widget>
    //     0xbea27c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbea280: r0 = AllocateGrowableArray()
    //     0xbea280: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbea284: mov             x1, x0
    // 0xbea288: ldur            x0, [fp, #-0x40]
    // 0xbea28c: stur            x1, [fp, #-8]
    // 0xbea290: StoreField: r1->field_f = r0
    //     0xbea290: stur            w0, [x1, #0xf]
    // 0xbea294: r0 = 10
    //     0xbea294: movz            x0, #0xa
    // 0xbea298: StoreField: r1->field_b = r0
    //     0xbea298: stur            w0, [x1, #0xb]
    // 0xbea29c: r0 = Column()
    //     0xbea29c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbea2a0: mov             x1, x0
    // 0xbea2a4: r0 = Instance_Axis
    //     0xbea2a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbea2a8: stur            x1, [fp, #-0x10]
    // 0xbea2ac: StoreField: r1->field_f = r0
    //     0xbea2ac: stur            w0, [x1, #0xf]
    // 0xbea2b0: r0 = Instance_MainAxisAlignment
    //     0xbea2b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbea2b4: ldr             x0, [x0, #0xa08]
    // 0xbea2b8: StoreField: r1->field_13 = r0
    //     0xbea2b8: stur            w0, [x1, #0x13]
    // 0xbea2bc: r0 = Instance_MainAxisSize
    //     0xbea2bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbea2c0: ldr             x0, [x0, #0xdd0]
    // 0xbea2c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xbea2c4: stur            w0, [x1, #0x17]
    // 0xbea2c8: r0 = Instance_CrossAxisAlignment
    //     0xbea2c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbea2cc: ldr             x0, [x0, #0x890]
    // 0xbea2d0: StoreField: r1->field_1b = r0
    //     0xbea2d0: stur            w0, [x1, #0x1b]
    // 0xbea2d4: r0 = Instance_VerticalDirection
    //     0xbea2d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbea2d8: ldr             x0, [x0, #0xa20]
    // 0xbea2dc: StoreField: r1->field_23 = r0
    //     0xbea2dc: stur            w0, [x1, #0x23]
    // 0xbea2e0: r0 = Instance_Clip
    //     0xbea2e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbea2e4: ldr             x0, [x0, #0x38]
    // 0xbea2e8: StoreField: r1->field_2b = r0
    //     0xbea2e8: stur            w0, [x1, #0x2b]
    // 0xbea2ec: StoreField: r1->field_2f = rZR
    //     0xbea2ec: stur            xzr, [x1, #0x2f]
    // 0xbea2f0: ldur            x0, [fp, #-8]
    // 0xbea2f4: StoreField: r1->field_b = r0
    //     0xbea2f4: stur            w0, [x1, #0xb]
    // 0xbea2f8: r0 = InkWell()
    //     0xbea2f8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbea2fc: mov             x3, x0
    // 0xbea300: ldur            x0, [fp, #-0x10]
    // 0xbea304: stur            x3, [fp, #-8]
    // 0xbea308: StoreField: r3->field_b = r0
    //     0xbea308: stur            w0, [x3, #0xb]
    // 0xbea30c: ldur            x2, [fp, #-0x28]
    // 0xbea310: r1 = Function '<anonymous closure>':.
    //     0xbea310: add             x1, PP, #0x53, lsl #12  ; [pp+0x536e0] AnonymousClosure: (0xbeafb8), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::lineThemeSlider (0xbe9c18)
    //     0xbea314: ldr             x1, [x1, #0x6e0]
    // 0xbea318: r0 = AllocateClosure()
    //     0xbea318: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbea31c: mov             x1, x0
    // 0xbea320: ldur            x0, [fp, #-8]
    // 0xbea324: StoreField: r0->field_f = r1
    //     0xbea324: stur            w1, [x0, #0xf]
    // 0xbea328: r1 = true
    //     0xbea328: add             x1, NULL, #0x20  ; true
    // 0xbea32c: StoreField: r0->field_43 = r1
    //     0xbea32c: stur            w1, [x0, #0x43]
    // 0xbea330: r2 = Instance_BoxShape
    //     0xbea330: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbea334: ldr             x2, [x2, #0x80]
    // 0xbea338: StoreField: r0->field_47 = r2
    //     0xbea338: stur            w2, [x0, #0x47]
    // 0xbea33c: StoreField: r0->field_6f = r1
    //     0xbea33c: stur            w1, [x0, #0x6f]
    // 0xbea340: r2 = false
    //     0xbea340: add             x2, NULL, #0x30  ; false
    // 0xbea344: StoreField: r0->field_73 = r2
    //     0xbea344: stur            w2, [x0, #0x73]
    // 0xbea348: StoreField: r0->field_83 = r1
    //     0xbea348: stur            w1, [x0, #0x83]
    // 0xbea34c: StoreField: r0->field_7b = r2
    //     0xbea34c: stur            w2, [x0, #0x7b]
    // 0xbea350: LeaveFrame
    //     0xbea350: mov             SP, fp
    //     0xbea354: ldp             fp, lr, [SP], #0x10
    // 0xbea358: ret
    //     0xbea358: ret             
    // 0xbea35c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbea35c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbea360: b               #0xbe9c3c
    // 0xbea364: r9 = _imagePageController
    //     0xbea364: add             x9, PP, #0x53, lsl #12  ; [pp+0x536e8] Field <_ProductGroupCarouselItemViewState@1706026639._imagePageController@1706026639>: late (offset: 0x18)
    //     0xbea368: ldr             x9, [x9, #0x6e8]
    // 0xbea36c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbea36c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbea370: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea370: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea374: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea374: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea378: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea378: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea37c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea37c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildAddToBagSection(/* No info */) {
    // ** addr: 0xbea380, size: 0x48c
    // 0xbea380: EnterFrame
    //     0xbea380: stp             fp, lr, [SP, #-0x10]!
    //     0xbea384: mov             fp, SP
    // 0xbea388: AllocStack(0x58)
    //     0xbea388: sub             SP, SP, #0x58
    // 0xbea38c: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbea38c: stur            x1, [fp, #-8]
    //     0xbea390: stur            x2, [fp, #-0x10]
    //     0xbea394: stur            x3, [fp, #-0x18]
    // 0xbea398: CheckStackOverflow
    //     0xbea398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbea39c: cmp             SP, x16
    //     0xbea3a0: b.ls            #0xbea7f0
    // 0xbea3a4: r1 = 3
    //     0xbea3a4: movz            x1, #0x3
    // 0xbea3a8: r0 = AllocateContext()
    //     0xbea3a8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbea3ac: mov             x3, x0
    // 0xbea3b0: ldur            x2, [fp, #-8]
    // 0xbea3b4: stur            x3, [fp, #-0x20]
    // 0xbea3b8: StoreField: r3->field_f = r2
    //     0xbea3b8: stur            w2, [x3, #0xf]
    // 0xbea3bc: ldur            x0, [fp, #-0x10]
    // 0xbea3c0: StoreField: r3->field_13 = r0
    //     0xbea3c0: stur            w0, [x3, #0x13]
    // 0xbea3c4: ldur            x4, [fp, #-0x18]
    // 0xbea3c8: r0 = BoxInt64Instr(r4)
    //     0xbea3c8: sbfiz           x0, x4, #1, #0x1f
    //     0xbea3cc: cmp             x4, x0, asr #1
    //     0xbea3d0: b.eq            #0xbea3dc
    //     0xbea3d4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbea3d8: stur            x4, [x0, #7]
    // 0xbea3dc: ArrayStore: r3[0] = r0  ; List_4
    //     0xbea3dc: stur            w0, [x3, #0x17]
    // 0xbea3e0: LoadField: r0 = r2->field_b
    //     0xbea3e0: ldur            w0, [x2, #0xb]
    // 0xbea3e4: DecompressPointer r0
    //     0xbea3e4: add             x0, x0, HEAP, lsl #32
    // 0xbea3e8: cmp             w0, NULL
    // 0xbea3ec: b.eq            #0xbea7f8
    // 0xbea3f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbea3f0: ldur            w1, [x0, #0x17]
    // 0xbea3f4: DecompressPointer r1
    //     0xbea3f4: add             x1, x1, HEAP, lsl #32
    // 0xbea3f8: LoadField: r0 = r1->field_1f
    //     0xbea3f8: ldur            w0, [x1, #0x1f]
    // 0xbea3fc: DecompressPointer r0
    //     0xbea3fc: add             x0, x0, HEAP, lsl #32
    // 0xbea400: cmp             w0, NULL
    // 0xbea404: b.ne            #0xbea410
    // 0xbea408: r0 = Null
    //     0xbea408: mov             x0, NULL
    // 0xbea40c: b               #0xbea41c
    // 0xbea410: LoadField: r4 = r0->field_7
    //     0xbea410: ldur            w4, [x0, #7]
    // 0xbea414: DecompressPointer r4
    //     0xbea414: add             x4, x4, HEAP, lsl #32
    // 0xbea418: mov             x0, x4
    // 0xbea41c: cmp             w0, NULL
    // 0xbea420: b.eq            #0xbea428
    // 0xbea424: tbz             w0, #4, #0xbea438
    // 0xbea428: r0 = Instance_SizedBox
    //     0xbea428: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbea42c: LeaveFrame
    //     0xbea42c: mov             SP, fp
    //     0xbea430: ldp             fp, lr, [SP], #0x10
    // 0xbea434: ret
    //     0xbea434: ret             
    // 0xbea438: LoadField: r0 = r1->field_3f
    //     0xbea438: ldur            w0, [x1, #0x3f]
    // 0xbea43c: DecompressPointer r0
    //     0xbea43c: add             x0, x0, HEAP, lsl #32
    // 0xbea440: cmp             w0, NULL
    // 0xbea444: b.ne            #0xbea450
    // 0xbea448: r0 = Null
    //     0xbea448: mov             x0, NULL
    // 0xbea44c: b               #0xbea45c
    // 0xbea450: LoadField: r1 = r0->field_23
    //     0xbea450: ldur            w1, [x0, #0x23]
    // 0xbea454: DecompressPointer r1
    //     0xbea454: add             x1, x1, HEAP, lsl #32
    // 0xbea458: mov             x0, x1
    // 0xbea45c: cmp             w0, NULL
    // 0xbea460: b.eq            #0xbea790
    // 0xbea464: tbnz            w0, #4, #0xbea790
    // 0xbea468: r16 = <Size?>
    //     0xbea468: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xbea46c: ldr             x16, [x16, #0x768]
    // 0xbea470: r30 = Instance_Size
    //     0xbea470: add             lr, PP, #0x52, lsl #12  ; [pp+0x52da0] Obj!Size@d6c221
    //     0xbea474: ldr             lr, [lr, #0xda0]
    // 0xbea478: stp             lr, x16, [SP]
    // 0xbea47c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbea47c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbea480: r0 = all()
    //     0xbea480: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbea484: ldur            x2, [fp, #-0x20]
    // 0xbea488: stur            x0, [fp, #-0x10]
    // 0xbea48c: LoadField: r1 = r2->field_13
    //     0xbea48c: ldur            w1, [x2, #0x13]
    // 0xbea490: DecompressPointer r1
    //     0xbea490: add             x1, x1, HEAP, lsl #32
    // 0xbea494: LoadField: r3 = r1->field_4f
    //     0xbea494: ldur            w3, [x1, #0x4f]
    // 0xbea498: DecompressPointer r3
    //     0xbea498: add             x3, x3, HEAP, lsl #32
    // 0xbea49c: cmp             w3, NULL
    // 0xbea4a0: b.eq            #0xbea4a8
    // 0xbea4a4: tbnz            w3, #4, #0xbea4f8
    // 0xbea4a8: ldur            x3, [fp, #-8]
    // 0xbea4ac: LoadField: r1 = r3->field_f
    //     0xbea4ac: ldur            w1, [x3, #0xf]
    // 0xbea4b0: DecompressPointer r1
    //     0xbea4b0: add             x1, x1, HEAP, lsl #32
    // 0xbea4b4: cmp             w1, NULL
    // 0xbea4b8: b.eq            #0xbea7fc
    // 0xbea4bc: r0 = of()
    //     0xbea4bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbea4c0: LoadField: r1 = r0->field_5b
    //     0xbea4c0: ldur            w1, [x0, #0x5b]
    // 0xbea4c4: DecompressPointer r1
    //     0xbea4c4: add             x1, x1, HEAP, lsl #32
    // 0xbea4c8: r0 = LoadClassIdInstr(r1)
    //     0xbea4c8: ldur            x0, [x1, #-1]
    //     0xbea4cc: ubfx            x0, x0, #0xc, #0x14
    // 0xbea4d0: d0 = 0.400000
    //     0xbea4d0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbea4d4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbea4d4: sub             lr, x0, #0xffa
    //     0xbea4d8: ldr             lr, [x21, lr, lsl #3]
    //     0xbea4dc: blr             lr
    // 0xbea4e0: r16 = <Color>
    //     0xbea4e0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbea4e4: ldr             x16, [x16, #0xf80]
    // 0xbea4e8: stp             x0, x16, [SP]
    // 0xbea4ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbea4ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbea4f0: r0 = all()
    //     0xbea4f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbea4f4: b               #0xbea510
    // 0xbea4f8: r16 = <Color>
    //     0xbea4f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbea4fc: ldr             x16, [x16, #0xf80]
    // 0xbea500: r30 = Instance_Color
    //     0xbea500: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbea504: stp             lr, x16, [SP]
    // 0xbea508: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbea508: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbea50c: r0 = all()
    //     0xbea50c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbea510: ldur            x2, [fp, #-0x20]
    // 0xbea514: stur            x0, [fp, #-0x28]
    // 0xbea518: LoadField: r1 = r2->field_13
    //     0xbea518: ldur            w1, [x2, #0x13]
    // 0xbea51c: DecompressPointer r1
    //     0xbea51c: add             x1, x1, HEAP, lsl #32
    // 0xbea520: LoadField: r3 = r1->field_4f
    //     0xbea520: ldur            w3, [x1, #0x4f]
    // 0xbea524: DecompressPointer r3
    //     0xbea524: add             x3, x3, HEAP, lsl #32
    // 0xbea528: cmp             w3, NULL
    // 0xbea52c: b.eq            #0xbea534
    // 0xbea530: tbnz            w3, #4, #0xbea588
    // 0xbea534: ldur            x3, [fp, #-8]
    // 0xbea538: LoadField: r1 = r3->field_f
    //     0xbea538: ldur            w1, [x3, #0xf]
    // 0xbea53c: DecompressPointer r1
    //     0xbea53c: add             x1, x1, HEAP, lsl #32
    // 0xbea540: cmp             w1, NULL
    // 0xbea544: b.eq            #0xbea800
    // 0xbea548: r0 = of()
    //     0xbea548: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbea54c: LoadField: r1 = r0->field_5b
    //     0xbea54c: ldur            w1, [x0, #0x5b]
    // 0xbea550: DecompressPointer r1
    //     0xbea550: add             x1, x1, HEAP, lsl #32
    // 0xbea554: r0 = LoadClassIdInstr(r1)
    //     0xbea554: ldur            x0, [x1, #-1]
    //     0xbea558: ubfx            x0, x0, #0xc, #0x14
    // 0xbea55c: d0 = 0.100000
    //     0xbea55c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbea560: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbea560: sub             lr, x0, #0xffa
    //     0xbea564: ldr             lr, [x21, lr, lsl #3]
    //     0xbea568: blr             lr
    // 0xbea56c: r16 = <Color>
    //     0xbea56c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbea570: ldr             x16, [x16, #0xf80]
    // 0xbea574: stp             x0, x16, [SP]
    // 0xbea578: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbea578: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbea57c: r0 = all()
    //     0xbea57c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbea580: mov             x3, x0
    // 0xbea584: b               #0xbea5c0
    // 0xbea588: ldur            x0, [fp, #-8]
    // 0xbea58c: LoadField: r1 = r0->field_f
    //     0xbea58c: ldur            w1, [x0, #0xf]
    // 0xbea590: DecompressPointer r1
    //     0xbea590: add             x1, x1, HEAP, lsl #32
    // 0xbea594: cmp             w1, NULL
    // 0xbea598: b.eq            #0xbea804
    // 0xbea59c: r0 = of()
    //     0xbea59c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbea5a0: LoadField: r1 = r0->field_5b
    //     0xbea5a0: ldur            w1, [x0, #0x5b]
    // 0xbea5a4: DecompressPointer r1
    //     0xbea5a4: add             x1, x1, HEAP, lsl #32
    // 0xbea5a8: r16 = <Color>
    //     0xbea5a8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbea5ac: ldr             x16, [x16, #0xf80]
    // 0xbea5b0: stp             x1, x16, [SP]
    // 0xbea5b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbea5b4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbea5b8: r0 = all()
    //     0xbea5b8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbea5bc: mov             x3, x0
    // 0xbea5c0: ldur            x2, [fp, #-0x20]
    // 0xbea5c4: ldur            x1, [fp, #-0x10]
    // 0xbea5c8: ldur            x0, [fp, #-0x28]
    // 0xbea5cc: stur            x3, [fp, #-0x30]
    // 0xbea5d0: r16 = <OutlinedBorder?>
    //     0xbea5d0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52da8] TypeArguments: <OutlinedBorder?>
    //     0xbea5d4: ldr             x16, [x16, #0xda8]
    // 0xbea5d8: r30 = Instance_RoundedRectangleBorder
    //     0xbea5d8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbea5dc: ldr             lr, [lr, #0xd68]
    // 0xbea5e0: stp             lr, x16, [SP]
    // 0xbea5e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbea5e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbea5e8: r0 = all()
    //     0xbea5e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbea5ec: stur            x0, [fp, #-0x38]
    // 0xbea5f0: r0 = ButtonStyle()
    //     0xbea5f0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbea5f4: mov             x1, x0
    // 0xbea5f8: ldur            x0, [fp, #-0x30]
    // 0xbea5fc: stur            x1, [fp, #-0x40]
    // 0xbea600: StoreField: r1->field_b = r0
    //     0xbea600: stur            w0, [x1, #0xb]
    // 0xbea604: ldur            x0, [fp, #-0x28]
    // 0xbea608: StoreField: r1->field_f = r0
    //     0xbea608: stur            w0, [x1, #0xf]
    // 0xbea60c: ldur            x0, [fp, #-0x10]
    // 0xbea610: StoreField: r1->field_27 = r0
    //     0xbea610: stur            w0, [x1, #0x27]
    // 0xbea614: ldur            x0, [fp, #-0x38]
    // 0xbea618: StoreField: r1->field_43 = r0
    //     0xbea618: stur            w0, [x1, #0x43]
    // 0xbea61c: r0 = TextButtonThemeData()
    //     0xbea61c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbea620: mov             x1, x0
    // 0xbea624: ldur            x0, [fp, #-0x40]
    // 0xbea628: stur            x1, [fp, #-0x10]
    // 0xbea62c: StoreField: r1->field_7 = r0
    //     0xbea62c: stur            w0, [x1, #7]
    // 0xbea630: ldur            x2, [fp, #-0x20]
    // 0xbea634: LoadField: r0 = r2->field_13
    //     0xbea634: ldur            w0, [x2, #0x13]
    // 0xbea638: DecompressPointer r0
    //     0xbea638: add             x0, x0, HEAP, lsl #32
    // 0xbea63c: LoadField: r3 = r0->field_f
    //     0xbea63c: ldur            w3, [x0, #0xf]
    // 0xbea640: DecompressPointer r3
    //     0xbea640: add             x3, x3, HEAP, lsl #32
    // 0xbea644: cmp             w3, NULL
    // 0xbea648: b.ne            #0xbea654
    // 0xbea64c: r0 = Null
    //     0xbea64c: mov             x0, NULL
    // 0xbea650: b               #0xbea66c
    // 0xbea654: r0 = LoadClassIdInstr(r3)
    //     0xbea654: ldur            x0, [x3, #-1]
    //     0xbea658: ubfx            x0, x0, #0xc, #0x14
    // 0xbea65c: str             x3, [SP]
    // 0xbea660: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbea660: sub             lr, x0, #1, lsl #12
    //     0xbea664: ldr             lr, [x21, lr, lsl #3]
    //     0xbea668: blr             lr
    // 0xbea66c: cmp             w0, NULL
    // 0xbea670: b.ne            #0xbea67c
    // 0xbea674: r3 = ""
    //     0xbea674: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbea678: b               #0xbea680
    // 0xbea67c: mov             x3, x0
    // 0xbea680: ldur            x0, [fp, #-8]
    // 0xbea684: ldur            x2, [fp, #-0x20]
    // 0xbea688: stur            x3, [fp, #-0x28]
    // 0xbea68c: LoadField: r1 = r0->field_f
    //     0xbea68c: ldur            w1, [x0, #0xf]
    // 0xbea690: DecompressPointer r1
    //     0xbea690: add             x1, x1, HEAP, lsl #32
    // 0xbea694: cmp             w1, NULL
    // 0xbea698: b.eq            #0xbea808
    // 0xbea69c: r0 = of()
    //     0xbea69c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbea6a0: LoadField: r1 = r0->field_87
    //     0xbea6a0: ldur            w1, [x0, #0x87]
    // 0xbea6a4: DecompressPointer r1
    //     0xbea6a4: add             x1, x1, HEAP, lsl #32
    // 0xbea6a8: LoadField: r0 = r1->field_7
    //     0xbea6a8: ldur            w0, [x1, #7]
    // 0xbea6ac: DecompressPointer r0
    //     0xbea6ac: add             x0, x0, HEAP, lsl #32
    // 0xbea6b0: ldur            x2, [fp, #-0x20]
    // 0xbea6b4: stur            x0, [fp, #-8]
    // 0xbea6b8: LoadField: r1 = r2->field_13
    //     0xbea6b8: ldur            w1, [x2, #0x13]
    // 0xbea6bc: DecompressPointer r1
    //     0xbea6bc: add             x1, x1, HEAP, lsl #32
    // 0xbea6c0: LoadField: r3 = r1->field_4f
    //     0xbea6c0: ldur            w3, [x1, #0x4f]
    // 0xbea6c4: DecompressPointer r3
    //     0xbea6c4: add             x3, x3, HEAP, lsl #32
    // 0xbea6c8: cmp             w3, NULL
    // 0xbea6cc: b.eq            #0xbea6d4
    // 0xbea6d0: tbnz            w3, #4, #0xbea6e8
    // 0xbea6d4: r1 = Instance_Color
    //     0xbea6d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbea6d8: d0 = 0.400000
    //     0xbea6d8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbea6dc: r0 = withOpacity()
    //     0xbea6dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbea6e0: mov             x1, x0
    // 0xbea6e4: b               #0xbea6ec
    // 0xbea6e8: r1 = Instance_Color
    //     0xbea6e8: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbea6ec: ldur            x2, [fp, #-0x10]
    // 0xbea6f0: ldur            x0, [fp, #-0x28]
    // 0xbea6f4: r16 = 14.000000
    //     0xbea6f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbea6f8: ldr             x16, [x16, #0x1d8]
    // 0xbea6fc: stp             x1, x16, [SP]
    // 0xbea700: ldur            x1, [fp, #-8]
    // 0xbea704: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbea704: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbea708: ldr             x4, [x4, #0xaa0]
    // 0xbea70c: r0 = copyWith()
    //     0xbea70c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbea710: stur            x0, [fp, #-8]
    // 0xbea714: r0 = Text()
    //     0xbea714: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbea718: mov             x3, x0
    // 0xbea71c: ldur            x0, [fp, #-0x28]
    // 0xbea720: stur            x3, [fp, #-0x30]
    // 0xbea724: StoreField: r3->field_b = r0
    //     0xbea724: stur            w0, [x3, #0xb]
    // 0xbea728: ldur            x0, [fp, #-8]
    // 0xbea72c: StoreField: r3->field_13 = r0
    //     0xbea72c: stur            w0, [x3, #0x13]
    // 0xbea730: ldur            x2, [fp, #-0x20]
    // 0xbea734: r1 = Function '<anonymous closure>':.
    //     0xbea734: add             x1, PP, #0x53, lsl #12  ; [pp+0x53730] AnonymousClosure: (0xbea80c), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildAddToBagSection (0xbea380)
    //     0xbea738: ldr             x1, [x1, #0x730]
    // 0xbea73c: r0 = AllocateClosure()
    //     0xbea73c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbea740: stur            x0, [fp, #-8]
    // 0xbea744: r0 = TextButton()
    //     0xbea744: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbea748: mov             x1, x0
    // 0xbea74c: ldur            x0, [fp, #-8]
    // 0xbea750: stur            x1, [fp, #-0x20]
    // 0xbea754: StoreField: r1->field_b = r0
    //     0xbea754: stur            w0, [x1, #0xb]
    // 0xbea758: r0 = false
    //     0xbea758: add             x0, NULL, #0x30  ; false
    // 0xbea75c: StoreField: r1->field_27 = r0
    //     0xbea75c: stur            w0, [x1, #0x27]
    // 0xbea760: r0 = true
    //     0xbea760: add             x0, NULL, #0x20  ; true
    // 0xbea764: StoreField: r1->field_2f = r0
    //     0xbea764: stur            w0, [x1, #0x2f]
    // 0xbea768: ldur            x0, [fp, #-0x30]
    // 0xbea76c: StoreField: r1->field_37 = r0
    //     0xbea76c: stur            w0, [x1, #0x37]
    // 0xbea770: r0 = TextButtonTheme()
    //     0xbea770: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbea774: mov             x1, x0
    // 0xbea778: ldur            x0, [fp, #-0x10]
    // 0xbea77c: StoreField: r1->field_f = r0
    //     0xbea77c: stur            w0, [x1, #0xf]
    // 0xbea780: ldur            x0, [fp, #-0x20]
    // 0xbea784: StoreField: r1->field_b = r0
    //     0xbea784: stur            w0, [x1, #0xb]
    // 0xbea788: mov             x0, x1
    // 0xbea78c: b               #0xbea794
    // 0xbea790: r0 = Instance_SizedBox
    //     0xbea790: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbea794: stur            x0, [fp, #-8]
    // 0xbea798: r0 = Container()
    //     0xbea798: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbea79c: stur            x0, [fp, #-0x10]
    // 0xbea7a0: r16 = inf
    //     0xbea7a0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbea7a4: ldr             x16, [x16, #0x9f8]
    // 0xbea7a8: r30 = Instance_Alignment
    //     0xbea7a8: add             lr, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xbea7ac: ldr             lr, [lr, #0xcb0]
    // 0xbea7b0: stp             lr, x16, [SP, #8]
    // 0xbea7b4: ldur            x16, [fp, #-8]
    // 0xbea7b8: str             x16, [SP]
    // 0xbea7bc: mov             x1, x0
    // 0xbea7c0: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x2, child, 0x3, width, 0x1, null]
    //     0xbea7c0: add             x4, PP, #0x52, lsl #12  ; [pp+0x52dd8] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x2, "child", 0x3, "width", 0x1, Null]
    //     0xbea7c4: ldr             x4, [x4, #0xdd8]
    // 0xbea7c8: r0 = Container()
    //     0xbea7c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbea7cc: r0 = Padding()
    //     0xbea7cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbea7d0: r1 = Instance_EdgeInsets
    //     0xbea7d0: add             x1, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbea7d4: ldr             x1, [x1, #0x770]
    // 0xbea7d8: StoreField: r0->field_f = r1
    //     0xbea7d8: stur            w1, [x0, #0xf]
    // 0xbea7dc: ldur            x1, [fp, #-0x10]
    // 0xbea7e0: StoreField: r0->field_b = r1
    //     0xbea7e0: stur            w1, [x0, #0xb]
    // 0xbea7e4: LeaveFrame
    //     0xbea7e4: mov             SP, fp
    //     0xbea7e8: ldp             fp, lr, [SP], #0x10
    // 0xbea7ec: ret
    //     0xbea7ec: ret             
    // 0xbea7f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbea7f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbea7f4: b               #0xbea3a4
    // 0xbea7f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea7f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea7fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea7fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea800: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea800: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea804: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea804: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea808: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea808: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbea80c, size: 0x68
    // 0xbea80c: EnterFrame
    //     0xbea80c: stp             fp, lr, [SP, #-0x10]!
    //     0xbea810: mov             fp, SP
    // 0xbea814: ldr             x0, [fp, #0x10]
    // 0xbea818: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbea818: ldur            w1, [x0, #0x17]
    // 0xbea81c: DecompressPointer r1
    //     0xbea81c: add             x1, x1, HEAP, lsl #32
    // 0xbea820: CheckStackOverflow
    //     0xbea820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbea824: cmp             SP, x16
    //     0xbea828: b.ls            #0xbea86c
    // 0xbea82c: LoadField: r0 = r1->field_f
    //     0xbea82c: ldur            w0, [x1, #0xf]
    // 0xbea830: DecompressPointer r0
    //     0xbea830: add             x0, x0, HEAP, lsl #32
    // 0xbea834: LoadField: r2 = r1->field_13
    //     0xbea834: ldur            w2, [x1, #0x13]
    // 0xbea838: DecompressPointer r2
    //     0xbea838: add             x2, x2, HEAP, lsl #32
    // 0xbea83c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbea83c: ldur            w3, [x1, #0x17]
    // 0xbea840: DecompressPointer r3
    //     0xbea840: add             x3, x3, HEAP, lsl #32
    // 0xbea844: r1 = LoadInt32Instr(r3)
    //     0xbea844: sbfx            x1, x3, #1, #0x1f
    //     0xbea848: tbz             w3, #0, #0xbea850
    //     0xbea84c: ldur            x1, [x3, #7]
    // 0xbea850: mov             x3, x1
    // 0xbea854: mov             x1, x0
    // 0xbea858: r0 = _onAddToBagPressed()
    //     0xbea858: bl              #0xbea874  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onAddToBagPressed
    // 0xbea85c: r0 = Null
    //     0xbea85c: mov             x0, NULL
    // 0xbea860: LeaveFrame
    //     0xbea860: mov             SP, fp
    //     0xbea864: ldp             fp, lr, [SP], #0x10
    // 0xbea868: ret
    //     0xbea868: ret             
    // 0xbea86c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbea86c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbea870: b               #0xbea82c
  }
  _ _onAddToBagPressed(/* No info */) {
    // ** addr: 0xbea874, size: 0x17c
    // 0xbea874: EnterFrame
    //     0xbea874: stp             fp, lr, [SP, #-0x10]!
    //     0xbea878: mov             fp, SP
    // 0xbea87c: AllocStack(0x38)
    //     0xbea87c: sub             SP, SP, #0x38
    // 0xbea880: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xbea880: mov             x0, x1
    //     0xbea884: stur            x1, [fp, #-8]
    //     0xbea888: mov             x1, x3
    //     0xbea88c: stur            x2, [fp, #-0x10]
    //     0xbea890: stur            x3, [fp, #-0x18]
    // 0xbea894: CheckStackOverflow
    //     0xbea894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbea898: cmp             SP, x16
    //     0xbea89c: b.ls            #0xbea9dc
    // 0xbea8a0: LoadField: r3 = r2->field_4f
    //     0xbea8a0: ldur            w3, [x2, #0x4f]
    // 0xbea8a4: DecompressPointer r3
    //     0xbea8a4: add             x3, x3, HEAP, lsl #32
    // 0xbea8a8: cmp             w3, NULL
    // 0xbea8ac: b.eq            #0xbea8b4
    // 0xbea8b0: tbz             w3, #4, #0xbea9cc
    // 0xbea8b4: LoadField: r3 = r0->field_b
    //     0xbea8b4: ldur            w3, [x0, #0xb]
    // 0xbea8b8: DecompressPointer r3
    //     0xbea8b8: add             x3, x3, HEAP, lsl #32
    // 0xbea8bc: cmp             w3, NULL
    // 0xbea8c0: b.eq            #0xbea9e4
    // 0xbea8c4: LoadField: r4 = r2->field_2b
    //     0xbea8c4: ldur            w4, [x2, #0x2b]
    // 0xbea8c8: DecompressPointer r4
    //     0xbea8c8: add             x4, x4, HEAP, lsl #32
    // 0xbea8cc: LoadField: r5 = r2->field_3b
    //     0xbea8cc: ldur            w5, [x2, #0x3b]
    // 0xbea8d0: DecompressPointer r5
    //     0xbea8d0: add             x5, x5, HEAP, lsl #32
    // 0xbea8d4: cmp             w5, NULL
    // 0xbea8d8: b.ne            #0xbea8e4
    // 0xbea8dc: r6 = Null
    //     0xbea8dc: mov             x6, NULL
    // 0xbea8e0: b               #0xbea8ec
    // 0xbea8e4: LoadField: r6 = r5->field_7
    //     0xbea8e4: ldur            w6, [x5, #7]
    // 0xbea8e8: DecompressPointer r6
    //     0xbea8e8: add             x6, x6, HEAP, lsl #32
    // 0xbea8ec: cmp             w5, NULL
    // 0xbea8f0: b.ne            #0xbea8fc
    // 0xbea8f4: r5 = Null
    //     0xbea8f4: mov             x5, NULL
    // 0xbea8f8: b               #0xbea908
    // 0xbea8fc: LoadField: r7 = r5->field_b
    //     0xbea8fc: ldur            w7, [x5, #0xb]
    // 0xbea900: DecompressPointer r7
    //     0xbea900: add             x7, x7, HEAP, lsl #32
    // 0xbea904: mov             x5, x7
    // 0xbea908: LoadField: r7 = r3->field_37
    //     0xbea908: ldur            w7, [x3, #0x37]
    // 0xbea90c: DecompressPointer r7
    //     0xbea90c: add             x7, x7, HEAP, lsl #32
    // 0xbea910: stp             x4, x7, [SP, #0x10]
    // 0xbea914: stp             x5, x6, [SP]
    // 0xbea918: r4 = 0
    //     0xbea918: movz            x4, #0
    // 0xbea91c: ldr             x0, [SP, #0x18]
    // 0xbea920: r16 = UnlinkedCall_0x613b5c
    //     0xbea920: add             x16, PP, #0x53, lsl #12  ; [pp+0x53738] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbea924: add             x16, x16, #0x738
    // 0xbea928: ldp             x5, lr, [x16]
    // 0xbea92c: blr             lr
    // 0xbea930: ldur            x0, [fp, #-8]
    // 0xbea934: LoadField: r2 = r0->field_b
    //     0xbea934: ldur            w2, [x0, #0xb]
    // 0xbea938: DecompressPointer r2
    //     0xbea938: add             x2, x2, HEAP, lsl #32
    // 0xbea93c: cmp             w2, NULL
    // 0xbea940: b.eq            #0xbea9e8
    // 0xbea944: LoadField: r3 = r2->field_1f
    //     0xbea944: ldur            w3, [x2, #0x1f]
    // 0xbea948: DecompressPointer r3
    //     0xbea948: add             x3, x3, HEAP, lsl #32
    // 0xbea94c: LoadField: r4 = r2->field_b
    //     0xbea94c: ldur            w4, [x2, #0xb]
    // 0xbea950: DecompressPointer r4
    //     0xbea950: add             x4, x4, HEAP, lsl #32
    // 0xbea954: cmp             w4, NULL
    // 0xbea958: b.ne            #0xbea964
    // 0xbea95c: r0 = Null
    //     0xbea95c: mov             x0, NULL
    // 0xbea960: b               #0xbea99c
    // 0xbea964: ldur            x5, [fp, #-0x18]
    // 0xbea968: LoadField: r0 = r4->field_b
    //     0xbea968: ldur            w0, [x4, #0xb]
    // 0xbea96c: r1 = LoadInt32Instr(r0)
    //     0xbea96c: sbfx            x1, x0, #1, #0x1f
    // 0xbea970: mov             x0, x1
    // 0xbea974: mov             x1, x5
    // 0xbea978: cmp             x1, x0
    // 0xbea97c: b.hs            #0xbea9ec
    // 0xbea980: LoadField: r0 = r4->field_f
    //     0xbea980: ldur            w0, [x4, #0xf]
    // 0xbea984: DecompressPointer r0
    //     0xbea984: add             x0, x0, HEAP, lsl #32
    // 0xbea988: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbea988: add             x16, x0, x5, lsl #2
    //     0xbea98c: ldur            w1, [x16, #0xf]
    // 0xbea990: DecompressPointer r1
    //     0xbea990: add             x1, x1, HEAP, lsl #32
    // 0xbea994: LoadField: r0 = r1->field_b3
    //     0xbea994: ldur            w0, [x1, #0xb3]
    // 0xbea998: DecompressPointer r0
    //     0xbea998: add             x0, x0, HEAP, lsl #32
    // 0xbea99c: cmp             w0, NULL
    // 0xbea9a0: b.ne            #0xbea9a8
    // 0xbea9a4: r0 = ""
    //     0xbea9a4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbea9a8: LoadField: r1 = r2->field_47
    //     0xbea9a8: ldur            w1, [x2, #0x47]
    // 0xbea9ac: DecompressPointer r1
    //     0xbea9ac: add             x1, x1, HEAP, lsl #32
    // 0xbea9b0: ldur            x16, [fp, #-0x10]
    // 0xbea9b4: stp             x16, x1, [SP, #0x10]
    // 0xbea9b8: stp             x0, x3, [SP]
    // 0xbea9bc: mov             x0, x1
    // 0xbea9c0: ClosureCall
    //     0xbea9c0: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbea9c4: ldur            x2, [x0, #0x1f]
    //     0xbea9c8: blr             x2
    // 0xbea9cc: r0 = Null
    //     0xbea9cc: mov             x0, NULL
    // 0xbea9d0: LeaveFrame
    //     0xbea9d0: mov             SP, fp
    //     0xbea9d4: ldp             fp, lr, [SP], #0x10
    // 0xbea9d8: ret
    //     0xbea9d8: ret             
    // 0xbea9dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbea9dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbea9e0: b               #0xbea8a0
    // 0xbea9e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea9e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea9e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbea9e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbea9ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbea9ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildRatingWidget(/* No info */) {
    // ** addr: 0xbea9f0, size: 0x5c8
    // 0xbea9f0: EnterFrame
    //     0xbea9f0: stp             fp, lr, [SP, #-0x10]!
    //     0xbea9f4: mov             fp, SP
    // 0xbea9f8: AllocStack(0x50)
    //     0xbea9f8: sub             SP, SP, #0x50
    // 0xbea9fc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbea9fc: stur            x1, [fp, #-8]
    //     0xbeaa00: stur            x2, [fp, #-0x10]
    // 0xbeaa04: CheckStackOverflow
    //     0xbeaa04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeaa08: cmp             SP, x16
    //     0xbeaa0c: b.ls            #0xbeaf74
    // 0xbeaa10: LoadField: r0 = r2->field_7b
    //     0xbeaa10: ldur            w0, [x2, #0x7b]
    // 0xbeaa14: DecompressPointer r0
    //     0xbeaa14: add             x0, x0, HEAP, lsl #32
    // 0xbeaa18: cmp             w0, NULL
    // 0xbeaa1c: b.ne            #0xbeaa28
    // 0xbeaa20: r0 = Null
    //     0xbeaa20: mov             x0, NULL
    // 0xbeaa24: b               #0xbeaa34
    // 0xbeaa28: LoadField: r3 = r0->field_7
    //     0xbeaa28: ldur            w3, [x0, #7]
    // 0xbeaa2c: DecompressPointer r3
    //     0xbeaa2c: add             x3, x3, HEAP, lsl #32
    // 0xbeaa30: mov             x0, x3
    // 0xbeaa34: r3 = LoadClassIdInstr(r0)
    //     0xbeaa34: ldur            x3, [x0, #-1]
    //     0xbeaa38: ubfx            x3, x3, #0xc, #0x14
    // 0xbeaa3c: r16 = 0.000000
    //     0xbeaa3c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbeaa40: stp             x16, x0, [SP]
    // 0xbeaa44: mov             x0, x3
    // 0xbeaa48: mov             lr, x0
    // 0xbeaa4c: ldr             lr, [x21, lr, lsl #3]
    // 0xbeaa50: blr             lr
    // 0xbeaa54: tbz             w0, #4, #0xbeaa7c
    // 0xbeaa58: ldur            x1, [fp, #-0x10]
    // 0xbeaa5c: LoadField: r0 = r1->field_7b
    //     0xbeaa5c: ldur            w0, [x1, #0x7b]
    // 0xbeaa60: DecompressPointer r0
    //     0xbeaa60: add             x0, x0, HEAP, lsl #32
    // 0xbeaa64: cmp             w0, NULL
    // 0xbeaa68: b.eq            #0xbeaa7c
    // 0xbeaa6c: LoadField: r2 = r0->field_7
    //     0xbeaa6c: ldur            w2, [x0, #7]
    // 0xbeaa70: DecompressPointer r2
    //     0xbeaa70: add             x2, x2, HEAP, lsl #32
    // 0xbeaa74: cmp             w2, NULL
    // 0xbeaa78: b.ne            #0xbeaa8c
    // 0xbeaa7c: r0 = Instance_SizedBox
    //     0xbeaa7c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbeaa80: LeaveFrame
    //     0xbeaa80: mov             SP, fp
    //     0xbeaa84: ldp             fp, lr, [SP], #0x10
    // 0xbeaa88: ret
    //     0xbeaa88: ret             
    // 0xbeaa8c: LoadField: r2 = r0->field_f
    //     0xbeaa8c: ldur            w2, [x0, #0xf]
    // 0xbeaa90: DecompressPointer r2
    //     0xbeaa90: add             x2, x2, HEAP, lsl #32
    // 0xbeaa94: r0 = LoadClassIdInstr(r2)
    //     0xbeaa94: ldur            x0, [x2, #-1]
    //     0xbeaa98: ubfx            x0, x0, #0xc, #0x14
    // 0xbeaa9c: r16 = "product_rating"
    //     0xbeaa9c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xbeaaa0: ldr             x16, [x16, #0xf20]
    // 0xbeaaa4: stp             x16, x2, [SP]
    // 0xbeaaa8: mov             lr, x0
    // 0xbeaaac: ldr             lr, [x21, lr, lsl #3]
    // 0xbeaab0: blr             lr
    // 0xbeaab4: stur            x0, [fp, #-0x18]
    // 0xbeaab8: tbnz            w0, #4, #0xbeab08
    // 0xbeaabc: ldur            x2, [fp, #-0x10]
    // 0xbeaac0: LoadField: r1 = r2->field_7b
    //     0xbeaac0: ldur            w1, [x2, #0x7b]
    // 0xbeaac4: DecompressPointer r1
    //     0xbeaac4: add             x1, x1, HEAP, lsl #32
    // 0xbeaac8: cmp             w1, NULL
    // 0xbeaacc: b.ne            #0xbeaad8
    // 0xbeaad0: r1 = Null
    //     0xbeaad0: mov             x1, NULL
    // 0xbeaad4: b               #0xbeaae4
    // 0xbeaad8: LoadField: r3 = r1->field_7
    //     0xbeaad8: ldur            w3, [x1, #7]
    // 0xbeaadc: DecompressPointer r3
    //     0xbeaadc: add             x3, x3, HEAP, lsl #32
    // 0xbeaae0: mov             x1, x3
    // 0xbeaae4: cmp             w1, NULL
    // 0xbeaae8: b.ne            #0xbeaaf4
    // 0xbeaaec: d0 = 0.000000
    //     0xbeaaec: eor             v0.16b, v0.16b, v0.16b
    // 0xbeaaf0: b               #0xbeaaf8
    // 0xbeaaf4: LoadField: d0 = r1->field_7
    //     0xbeaaf4: ldur            d0, [x1, #7]
    // 0xbeaaf8: ldur            x1, [fp, #-8]
    // 0xbeaafc: r0 = _getRatingColor()
    //     0xbeaafc: bl              #0xa582f0  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_getRatingColor
    // 0xbeab00: mov             x1, x0
    // 0xbeab04: b               #0xbeab28
    // 0xbeab08: ldur            x0, [fp, #-8]
    // 0xbeab0c: LoadField: r1 = r0->field_f
    //     0xbeab0c: ldur            w1, [x0, #0xf]
    // 0xbeab10: DecompressPointer r1
    //     0xbeab10: add             x1, x1, HEAP, lsl #32
    // 0xbeab14: cmp             w1, NULL
    // 0xbeab18: b.eq            #0xbeaf7c
    // 0xbeab1c: r0 = of()
    //     0xbeab1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbeab20: LoadField: r1 = r0->field_5b
    //     0xbeab20: ldur            w1, [x0, #0x5b]
    // 0xbeab24: DecompressPointer r1
    //     0xbeab24: add             x1, x1, HEAP, lsl #32
    // 0xbeab28: ldur            x0, [fp, #-0x10]
    // 0xbeab2c: stur            x1, [fp, #-0x20]
    // 0xbeab30: r0 = ColorFilter()
    //     0xbeab30: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbeab34: mov             x1, x0
    // 0xbeab38: ldur            x0, [fp, #-0x20]
    // 0xbeab3c: stur            x1, [fp, #-0x28]
    // 0xbeab40: StoreField: r1->field_7 = r0
    //     0xbeab40: stur            w0, [x1, #7]
    // 0xbeab44: r0 = Instance_BlendMode
    //     0xbeab44: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbeab48: ldr             x0, [x0, #0xb30]
    // 0xbeab4c: StoreField: r1->field_b = r0
    //     0xbeab4c: stur            w0, [x1, #0xb]
    // 0xbeab50: r0 = 1
    //     0xbeab50: movz            x0, #0x1
    // 0xbeab54: StoreField: r1->field_13 = r0
    //     0xbeab54: stur            x0, [x1, #0x13]
    // 0xbeab58: r0 = SvgPicture()
    //     0xbeab58: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbeab5c: stur            x0, [fp, #-0x20]
    // 0xbeab60: ldur            x16, [fp, #-0x28]
    // 0xbeab64: str             x16, [SP]
    // 0xbeab68: mov             x1, x0
    // 0xbeab6c: r2 = "assets/images/green_star.svg"
    //     0xbeab6c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xbeab70: ldr             x2, [x2, #0x9a0]
    // 0xbeab74: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbeab74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbeab78: ldr             x4, [x4, #0xa38]
    // 0xbeab7c: r0 = SvgPicture.asset()
    //     0xbeab7c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbeab80: ldur            x0, [fp, #-0x10]
    // 0xbeab84: LoadField: r1 = r0->field_7b
    //     0xbeab84: ldur            w1, [x0, #0x7b]
    // 0xbeab88: DecompressPointer r1
    //     0xbeab88: add             x1, x1, HEAP, lsl #32
    // 0xbeab8c: cmp             w1, NULL
    // 0xbeab90: b.ne            #0xbeab9c
    // 0xbeab94: r0 = Null
    //     0xbeab94: mov             x0, NULL
    // 0xbeab98: b               #0xbeabbc
    // 0xbeab9c: LoadField: r2 = r1->field_7
    //     0xbeab9c: ldur            w2, [x1, #7]
    // 0xbeaba0: DecompressPointer r2
    //     0xbeaba0: add             x2, x2, HEAP, lsl #32
    // 0xbeaba4: cmp             w2, NULL
    // 0xbeaba8: b.ne            #0xbeabb4
    // 0xbeabac: r0 = Null
    //     0xbeabac: mov             x0, NULL
    // 0xbeabb0: b               #0xbeabbc
    // 0xbeabb4: str             x2, [SP]
    // 0xbeabb8: r0 = toString()
    //     0xbeabb8: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xbeabbc: cmp             w0, NULL
    // 0xbeabc0: b.ne            #0xbeabcc
    // 0xbeabc4: r4 = ""
    //     0xbeabc4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeabc8: b               #0xbeabd0
    // 0xbeabcc: mov             x4, x0
    // 0xbeabd0: ldur            x2, [fp, #-8]
    // 0xbeabd4: ldur            x3, [fp, #-0x18]
    // 0xbeabd8: ldur            x0, [fp, #-0x20]
    // 0xbeabdc: stur            x4, [fp, #-0x28]
    // 0xbeabe0: LoadField: r1 = r2->field_f
    //     0xbeabe0: ldur            w1, [x2, #0xf]
    // 0xbeabe4: DecompressPointer r1
    //     0xbeabe4: add             x1, x1, HEAP, lsl #32
    // 0xbeabe8: cmp             w1, NULL
    // 0xbeabec: b.eq            #0xbeaf80
    // 0xbeabf0: r0 = of()
    //     0xbeabf0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbeabf4: LoadField: r1 = r0->field_87
    //     0xbeabf4: ldur            w1, [x0, #0x87]
    // 0xbeabf8: DecompressPointer r1
    //     0xbeabf8: add             x1, x1, HEAP, lsl #32
    // 0xbeabfc: LoadField: r0 = r1->field_7
    //     0xbeabfc: ldur            w0, [x1, #7]
    // 0xbeac00: DecompressPointer r0
    //     0xbeac00: add             x0, x0, HEAP, lsl #32
    // 0xbeac04: r16 = 12.000000
    //     0xbeac04: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbeac08: ldr             x16, [x16, #0x9e8]
    // 0xbeac0c: r30 = Instance_Color
    //     0xbeac0c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbeac10: stp             lr, x16, [SP]
    // 0xbeac14: mov             x1, x0
    // 0xbeac18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbeac18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbeac1c: ldr             x4, [x4, #0xaa0]
    // 0xbeac20: r0 = copyWith()
    //     0xbeac20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbeac24: stur            x0, [fp, #-0x30]
    // 0xbeac28: r0 = Text()
    //     0xbeac28: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbeac2c: mov             x3, x0
    // 0xbeac30: ldur            x0, [fp, #-0x28]
    // 0xbeac34: stur            x3, [fp, #-0x38]
    // 0xbeac38: StoreField: r3->field_b = r0
    //     0xbeac38: stur            w0, [x3, #0xb]
    // 0xbeac3c: ldur            x0, [fp, #-0x30]
    // 0xbeac40: StoreField: r3->field_13 = r0
    //     0xbeac40: stur            w0, [x3, #0x13]
    // 0xbeac44: r1 = Null
    //     0xbeac44: mov             x1, NULL
    // 0xbeac48: r2 = 4
    //     0xbeac48: movz            x2, #0x4
    // 0xbeac4c: r0 = AllocateArray()
    //     0xbeac4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbeac50: mov             x2, x0
    // 0xbeac54: ldur            x0, [fp, #-0x20]
    // 0xbeac58: stur            x2, [fp, #-0x28]
    // 0xbeac5c: StoreField: r2->field_f = r0
    //     0xbeac5c: stur            w0, [x2, #0xf]
    // 0xbeac60: ldur            x0, [fp, #-0x38]
    // 0xbeac64: StoreField: r2->field_13 = r0
    //     0xbeac64: stur            w0, [x2, #0x13]
    // 0xbeac68: r1 = <Widget>
    //     0xbeac68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbeac6c: r0 = AllocateGrowableArray()
    //     0xbeac6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbeac70: mov             x3, x0
    // 0xbeac74: ldur            x0, [fp, #-0x28]
    // 0xbeac78: stur            x3, [fp, #-0x20]
    // 0xbeac7c: StoreField: r3->field_f = r0
    //     0xbeac7c: stur            w0, [x3, #0xf]
    // 0xbeac80: r0 = 4
    //     0xbeac80: movz            x0, #0x4
    // 0xbeac84: StoreField: r3->field_b = r0
    //     0xbeac84: stur            w0, [x3, #0xb]
    // 0xbeac88: ldur            x0, [fp, #-0x18]
    // 0xbeac8c: tbnz            w0, #4, #0xbeae04
    // 0xbeac90: ldur            x1, [fp, #-0x10]
    // 0xbeac94: LoadField: r2 = r1->field_7b
    //     0xbeac94: ldur            w2, [x1, #0x7b]
    // 0xbeac98: DecompressPointer r2
    //     0xbeac98: add             x2, x2, HEAP, lsl #32
    // 0xbeac9c: cmp             w2, NULL
    // 0xbeaca0: b.ne            #0xbeacac
    // 0xbeaca4: ldur            x2, [fp, #-8]
    // 0xbeaca8: b               #0xbeae08
    // 0xbeacac: LoadField: r4 = r2->field_b
    //     0xbeacac: ldur            w4, [x2, #0xb]
    // 0xbeacb0: DecompressPointer r4
    //     0xbeacb0: add             x4, x4, HEAP, lsl #32
    // 0xbeacb4: stur            x4, [fp, #-0x10]
    // 0xbeacb8: cmp             w4, NULL
    // 0xbeacbc: b.eq            #0xbeadfc
    // 0xbeacc0: ldur            x0, [fp, #-8]
    // 0xbeacc4: r1 = Null
    //     0xbeacc4: mov             x1, NULL
    // 0xbeacc8: r2 = 6
    //     0xbeacc8: movz            x2, #0x6
    // 0xbeaccc: r0 = AllocateArray()
    //     0xbeaccc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbeacd0: r16 = " | ("
    //     0xbeacd0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xbeacd4: ldr             x16, [x16, #0xd70]
    // 0xbeacd8: StoreField: r0->field_f = r16
    //     0xbeacd8: stur            w16, [x0, #0xf]
    // 0xbeacdc: ldur            x1, [fp, #-0x10]
    // 0xbeace0: LoadField: d0 = r1->field_7
    //     0xbeace0: ldur            d0, [x1, #7]
    // 0xbeace4: fcmp            d0, d0
    // 0xbeace8: b.vs            #0xbeaf84
    // 0xbeacec: fcvtzs          x1, d0
    // 0xbeacf0: asr             x16, x1, #0x1e
    // 0xbeacf4: cmp             x16, x1, asr #63
    // 0xbeacf8: b.ne            #0xbeaf84
    // 0xbeacfc: lsl             x1, x1, #1
    // 0xbead00: StoreField: r0->field_13 = r1
    //     0xbead00: stur            w1, [x0, #0x13]
    // 0xbead04: r16 = ")"
    //     0xbead04: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xbead08: ArrayStore: r0[0] = r16  ; List_4
    //     0xbead08: stur            w16, [x0, #0x17]
    // 0xbead0c: str             x0, [SP]
    // 0xbead10: r0 = _interpolate()
    //     0xbead10: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbead14: ldur            x2, [fp, #-8]
    // 0xbead18: stur            x0, [fp, #-0x10]
    // 0xbead1c: LoadField: r1 = r2->field_f
    //     0xbead1c: ldur            w1, [x2, #0xf]
    // 0xbead20: DecompressPointer r1
    //     0xbead20: add             x1, x1, HEAP, lsl #32
    // 0xbead24: cmp             w1, NULL
    // 0xbead28: b.eq            #0xbeafac
    // 0xbead2c: r0 = of()
    //     0xbead2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbead30: LoadField: r1 = r0->field_87
    //     0xbead30: ldur            w1, [x0, #0x87]
    // 0xbead34: DecompressPointer r1
    //     0xbead34: add             x1, x1, HEAP, lsl #32
    // 0xbead38: LoadField: r0 = r1->field_2b
    //     0xbead38: ldur            w0, [x1, #0x2b]
    // 0xbead3c: DecompressPointer r0
    //     0xbead3c: add             x0, x0, HEAP, lsl #32
    // 0xbead40: r16 = 10.000000
    //     0xbead40: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xbead44: r30 = Instance_Color
    //     0xbead44: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbead48: stp             lr, x16, [SP]
    // 0xbead4c: mov             x1, x0
    // 0xbead50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbead50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbead54: ldr             x4, [x4, #0xaa0]
    // 0xbead58: r0 = copyWith()
    //     0xbead58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbead5c: stur            x0, [fp, #-0x28]
    // 0xbead60: r0 = Text()
    //     0xbead60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbead64: mov             x2, x0
    // 0xbead68: ldur            x0, [fp, #-0x10]
    // 0xbead6c: stur            x2, [fp, #-0x30]
    // 0xbead70: StoreField: r2->field_b = r0
    //     0xbead70: stur            w0, [x2, #0xb]
    // 0xbead74: ldur            x0, [fp, #-0x28]
    // 0xbead78: StoreField: r2->field_13 = r0
    //     0xbead78: stur            w0, [x2, #0x13]
    // 0xbead7c: ldur            x0, [fp, #-0x20]
    // 0xbead80: LoadField: r1 = r0->field_b
    //     0xbead80: ldur            w1, [x0, #0xb]
    // 0xbead84: LoadField: r3 = r0->field_f
    //     0xbead84: ldur            w3, [x0, #0xf]
    // 0xbead88: DecompressPointer r3
    //     0xbead88: add             x3, x3, HEAP, lsl #32
    // 0xbead8c: LoadField: r4 = r3->field_b
    //     0xbead8c: ldur            w4, [x3, #0xb]
    // 0xbead90: r3 = LoadInt32Instr(r1)
    //     0xbead90: sbfx            x3, x1, #1, #0x1f
    // 0xbead94: stur            x3, [fp, #-0x40]
    // 0xbead98: r1 = LoadInt32Instr(r4)
    //     0xbead98: sbfx            x1, x4, #1, #0x1f
    // 0xbead9c: cmp             x3, x1
    // 0xbeada0: b.ne            #0xbeadac
    // 0xbeada4: mov             x1, x0
    // 0xbeada8: r0 = _growToNextCapacity()
    //     0xbeada8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbeadac: ldur            x3, [fp, #-0x20]
    // 0xbeadb0: ldur            x2, [fp, #-0x40]
    // 0xbeadb4: add             x0, x2, #1
    // 0xbeadb8: lsl             x1, x0, #1
    // 0xbeadbc: StoreField: r3->field_b = r1
    //     0xbeadbc: stur            w1, [x3, #0xb]
    // 0xbeadc0: LoadField: r1 = r3->field_f
    //     0xbeadc0: ldur            w1, [x3, #0xf]
    // 0xbeadc4: DecompressPointer r1
    //     0xbeadc4: add             x1, x1, HEAP, lsl #32
    // 0xbeadc8: ldur            x0, [fp, #-0x30]
    // 0xbeadcc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbeadcc: add             x25, x1, x2, lsl #2
    //     0xbeadd0: add             x25, x25, #0xf
    //     0xbeadd4: str             w0, [x25]
    //     0xbeadd8: tbz             w0, #0, #0xbeadf4
    //     0xbeaddc: ldurb           w16, [x1, #-1]
    //     0xbeade0: ldurb           w17, [x0, #-1]
    //     0xbeade4: and             x16, x17, x16, lsr #2
    //     0xbeade8: tst             x16, HEAP, lsr #32
    //     0xbeadec: b.eq            #0xbeadf4
    //     0xbeadf0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbeadf4: mov             x2, x3
    // 0xbeadf8: b               #0xbeaf14
    // 0xbeadfc: ldur            x2, [fp, #-8]
    // 0xbeae00: b               #0xbeae08
    // 0xbeae04: ldur            x2, [fp, #-8]
    // 0xbeae08: tbz             w0, #4, #0xbeaf10
    // 0xbeae0c: LoadField: r1 = r2->field_f
    //     0xbeae0c: ldur            w1, [x2, #0xf]
    // 0xbeae10: DecompressPointer r1
    //     0xbeae10: add             x1, x1, HEAP, lsl #32
    // 0xbeae14: cmp             w1, NULL
    // 0xbeae18: b.eq            #0xbeafb0
    // 0xbeae1c: r0 = of()
    //     0xbeae1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbeae20: LoadField: r1 = r0->field_87
    //     0xbeae20: ldur            w1, [x0, #0x87]
    // 0xbeae24: DecompressPointer r1
    //     0xbeae24: add             x1, x1, HEAP, lsl #32
    // 0xbeae28: LoadField: r0 = r1->field_2b
    //     0xbeae28: ldur            w0, [x1, #0x2b]
    // 0xbeae2c: DecompressPointer r0
    //     0xbeae2c: add             x0, x0, HEAP, lsl #32
    // 0xbeae30: ldur            x1, [fp, #-8]
    // 0xbeae34: stur            x0, [fp, #-0x10]
    // 0xbeae38: LoadField: r2 = r1->field_f
    //     0xbeae38: ldur            w2, [x1, #0xf]
    // 0xbeae3c: DecompressPointer r2
    //     0xbeae3c: add             x2, x2, HEAP, lsl #32
    // 0xbeae40: cmp             w2, NULL
    // 0xbeae44: b.eq            #0xbeafb4
    // 0xbeae48: mov             x1, x2
    // 0xbeae4c: r0 = of()
    //     0xbeae4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbeae50: LoadField: r1 = r0->field_5b
    //     0xbeae50: ldur            w1, [x0, #0x5b]
    // 0xbeae54: DecompressPointer r1
    //     0xbeae54: add             x1, x1, HEAP, lsl #32
    // 0xbeae58: r16 = 10.000000
    //     0xbeae58: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xbeae5c: stp             x1, x16, [SP]
    // 0xbeae60: ldur            x1, [fp, #-0x10]
    // 0xbeae64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbeae64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbeae68: ldr             x4, [x4, #0xaa0]
    // 0xbeae6c: r0 = copyWith()
    //     0xbeae6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbeae70: stur            x0, [fp, #-8]
    // 0xbeae74: r0 = Text()
    //     0xbeae74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbeae78: mov             x2, x0
    // 0xbeae7c: r0 = " Brand Rating"
    //     0xbeae7c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xbeae80: ldr             x0, [x0, #0xd78]
    // 0xbeae84: stur            x2, [fp, #-0x10]
    // 0xbeae88: StoreField: r2->field_b = r0
    //     0xbeae88: stur            w0, [x2, #0xb]
    // 0xbeae8c: ldur            x0, [fp, #-8]
    // 0xbeae90: StoreField: r2->field_13 = r0
    //     0xbeae90: stur            w0, [x2, #0x13]
    // 0xbeae94: ldur            x0, [fp, #-0x20]
    // 0xbeae98: LoadField: r1 = r0->field_b
    //     0xbeae98: ldur            w1, [x0, #0xb]
    // 0xbeae9c: LoadField: r3 = r0->field_f
    //     0xbeae9c: ldur            w3, [x0, #0xf]
    // 0xbeaea0: DecompressPointer r3
    //     0xbeaea0: add             x3, x3, HEAP, lsl #32
    // 0xbeaea4: LoadField: r4 = r3->field_b
    //     0xbeaea4: ldur            w4, [x3, #0xb]
    // 0xbeaea8: r3 = LoadInt32Instr(r1)
    //     0xbeaea8: sbfx            x3, x1, #1, #0x1f
    // 0xbeaeac: stur            x3, [fp, #-0x40]
    // 0xbeaeb0: r1 = LoadInt32Instr(r4)
    //     0xbeaeb0: sbfx            x1, x4, #1, #0x1f
    // 0xbeaeb4: cmp             x3, x1
    // 0xbeaeb8: b.ne            #0xbeaec4
    // 0xbeaebc: mov             x1, x0
    // 0xbeaec0: r0 = _growToNextCapacity()
    //     0xbeaec0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbeaec4: ldur            x2, [fp, #-0x20]
    // 0xbeaec8: ldur            x3, [fp, #-0x40]
    // 0xbeaecc: add             x0, x3, #1
    // 0xbeaed0: lsl             x1, x0, #1
    // 0xbeaed4: StoreField: r2->field_b = r1
    //     0xbeaed4: stur            w1, [x2, #0xb]
    // 0xbeaed8: LoadField: r1 = r2->field_f
    //     0xbeaed8: ldur            w1, [x2, #0xf]
    // 0xbeaedc: DecompressPointer r1
    //     0xbeaedc: add             x1, x1, HEAP, lsl #32
    // 0xbeaee0: ldur            x0, [fp, #-0x10]
    // 0xbeaee4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbeaee4: add             x25, x1, x3, lsl #2
    //     0xbeaee8: add             x25, x25, #0xf
    //     0xbeaeec: str             w0, [x25]
    //     0xbeaef0: tbz             w0, #0, #0xbeaf0c
    //     0xbeaef4: ldurb           w16, [x1, #-1]
    //     0xbeaef8: ldurb           w17, [x0, #-1]
    //     0xbeaefc: and             x16, x17, x16, lsr #2
    //     0xbeaf00: tst             x16, HEAP, lsr #32
    //     0xbeaf04: b.eq            #0xbeaf0c
    //     0xbeaf08: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbeaf0c: b               #0xbeaf14
    // 0xbeaf10: mov             x2, x3
    // 0xbeaf14: r0 = Row()
    //     0xbeaf14: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbeaf18: r1 = Instance_Axis
    //     0xbeaf18: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbeaf1c: StoreField: r0->field_f = r1
    //     0xbeaf1c: stur            w1, [x0, #0xf]
    // 0xbeaf20: r1 = Instance_MainAxisAlignment
    //     0xbeaf20: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbeaf24: ldr             x1, [x1, #0xa08]
    // 0xbeaf28: StoreField: r0->field_13 = r1
    //     0xbeaf28: stur            w1, [x0, #0x13]
    // 0xbeaf2c: r1 = Instance_MainAxisSize
    //     0xbeaf2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbeaf30: ldr             x1, [x1, #0xa10]
    // 0xbeaf34: ArrayStore: r0[0] = r1  ; List_4
    //     0xbeaf34: stur            w1, [x0, #0x17]
    // 0xbeaf38: r1 = Instance_CrossAxisAlignment
    //     0xbeaf38: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbeaf3c: ldr             x1, [x1, #0xa18]
    // 0xbeaf40: StoreField: r0->field_1b = r1
    //     0xbeaf40: stur            w1, [x0, #0x1b]
    // 0xbeaf44: r1 = Instance_VerticalDirection
    //     0xbeaf44: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbeaf48: ldr             x1, [x1, #0xa20]
    // 0xbeaf4c: StoreField: r0->field_23 = r1
    //     0xbeaf4c: stur            w1, [x0, #0x23]
    // 0xbeaf50: r1 = Instance_Clip
    //     0xbeaf50: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbeaf54: ldr             x1, [x1, #0x38]
    // 0xbeaf58: StoreField: r0->field_2b = r1
    //     0xbeaf58: stur            w1, [x0, #0x2b]
    // 0xbeaf5c: StoreField: r0->field_2f = rZR
    //     0xbeaf5c: stur            xzr, [x0, #0x2f]
    // 0xbeaf60: ldur            x1, [fp, #-0x20]
    // 0xbeaf64: StoreField: r0->field_b = r1
    //     0xbeaf64: stur            w1, [x0, #0xb]
    // 0xbeaf68: LeaveFrame
    //     0xbeaf68: mov             SP, fp
    //     0xbeaf6c: ldp             fp, lr, [SP], #0x10
    // 0xbeaf70: ret
    //     0xbeaf70: ret             
    // 0xbeaf74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeaf74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeaf78: b               #0xbeaa10
    // 0xbeaf7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeaf7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbeaf80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeaf80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbeaf84: SaveReg d0
    //     0xbeaf84: str             q0, [SP, #-0x10]!
    // 0xbeaf88: SaveReg r0
    //     0xbeaf88: str             x0, [SP, #-8]!
    // 0xbeaf8c: r0 = 74
    //     0xbeaf8c: movz            x0, #0x4a
    // 0xbeaf90: r30 = DoubleToIntegerStub
    //     0xbeaf90: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xbeaf94: LoadField: r30 = r30->field_7
    //     0xbeaf94: ldur            lr, [lr, #7]
    // 0xbeaf98: blr             lr
    // 0xbeaf9c: mov             x1, x0
    // 0xbeafa0: RestoreReg r0
    //     0xbeafa0: ldr             x0, [SP], #8
    // 0xbeafa4: RestoreReg d0
    //     0xbeafa4: ldr             q0, [SP], #0x10
    // 0xbeafa8: b               #0xbead00
    // 0xbeafac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeafac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbeafb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeafb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbeafb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeafb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbeafb8, size: 0x50
    // 0xbeafb8: EnterFrame
    //     0xbeafb8: stp             fp, lr, [SP, #-0x10]!
    //     0xbeafbc: mov             fp, SP
    // 0xbeafc0: ldr             x0, [fp, #0x10]
    // 0xbeafc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbeafc4: ldur            w1, [x0, #0x17]
    // 0xbeafc8: DecompressPointer r1
    //     0xbeafc8: add             x1, x1, HEAP, lsl #32
    // 0xbeafcc: CheckStackOverflow
    //     0xbeafcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeafd0: cmp             SP, x16
    //     0xbeafd4: b.ls            #0xbeb000
    // 0xbeafd8: LoadField: r0 = r1->field_f
    //     0xbeafd8: ldur            w0, [x1, #0xf]
    // 0xbeafdc: DecompressPointer r0
    //     0xbeafdc: add             x0, x0, HEAP, lsl #32
    // 0xbeafe0: LoadField: r2 = r1->field_13
    //     0xbeafe0: ldur            w2, [x1, #0x13]
    // 0xbeafe4: DecompressPointer r2
    //     0xbeafe4: add             x2, x2, HEAP, lsl #32
    // 0xbeafe8: mov             x1, x0
    // 0xbeafec: r0 = _onProductCardTap()
    //     0xbeafec: bl              #0xbeb008  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onProductCardTap
    // 0xbeaff0: r0 = Null
    //     0xbeaff0: mov             x0, NULL
    // 0xbeaff4: LeaveFrame
    //     0xbeaff4: mov             SP, fp
    //     0xbeaff8: ldp             fp, lr, [SP], #0x10
    // 0xbeaffc: ret
    //     0xbeaffc: ret             
    // 0xbeb000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb000: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb004: b               #0xbeafd8
  }
  _ _onProductCardTap(/* No info */) {
    // ** addr: 0xbeb008, size: 0x100
    // 0xbeb008: EnterFrame
    //     0xbeb008: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb00c: mov             fp, SP
    // 0xbeb010: AllocStack(0x48)
    //     0xbeb010: sub             SP, SP, #0x48
    // 0xbeb014: CheckStackOverflow
    //     0xbeb014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb018: cmp             SP, x16
    //     0xbeb01c: b.ls            #0xbeb0fc
    // 0xbeb020: LoadField: r0 = r1->field_b
    //     0xbeb020: ldur            w0, [x1, #0xb]
    // 0xbeb024: DecompressPointer r0
    //     0xbeb024: add             x0, x0, HEAP, lsl #32
    // 0xbeb028: cmp             w0, NULL
    // 0xbeb02c: b.eq            #0xbeb104
    // 0xbeb030: LoadField: r1 = r0->field_27
    //     0xbeb030: ldur            w1, [x0, #0x27]
    // 0xbeb034: DecompressPointer r1
    //     0xbeb034: add             x1, x1, HEAP, lsl #32
    // 0xbeb038: cmp             w1, NULL
    // 0xbeb03c: b.ne            #0xbeb044
    // 0xbeb040: r1 = ""
    //     0xbeb040: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeb044: LoadField: r3 = r0->field_23
    //     0xbeb044: ldur            w3, [x0, #0x23]
    // 0xbeb048: DecompressPointer r3
    //     0xbeb048: add             x3, x3, HEAP, lsl #32
    // 0xbeb04c: cmp             w3, NULL
    // 0xbeb050: b.ne            #0xbeb058
    // 0xbeb054: r3 = ""
    //     0xbeb054: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeb058: LoadField: r4 = r0->field_2f
    //     0xbeb058: ldur            w4, [x0, #0x2f]
    // 0xbeb05c: DecompressPointer r4
    //     0xbeb05c: add             x4, x4, HEAP, lsl #32
    // 0xbeb060: LoadField: r5 = r0->field_1f
    //     0xbeb060: ldur            w5, [x0, #0x1f]
    // 0xbeb064: DecompressPointer r5
    //     0xbeb064: add             x5, x5, HEAP, lsl #32
    // 0xbeb068: LoadField: r6 = r0->field_2b
    //     0xbeb068: ldur            w6, [x0, #0x2b]
    // 0xbeb06c: DecompressPointer r6
    //     0xbeb06c: add             x6, x6, HEAP, lsl #32
    // 0xbeb070: cmp             w6, NULL
    // 0xbeb074: b.ne            #0xbeb07c
    // 0xbeb078: r6 = ""
    //     0xbeb078: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeb07c: LoadField: r7 = r0->field_f
    //     0xbeb07c: ldur            w7, [x0, #0xf]
    // 0xbeb080: DecompressPointer r7
    //     0xbeb080: add             x7, x7, HEAP, lsl #32
    // 0xbeb084: cmp             w7, NULL
    // 0xbeb088: b.ne            #0xbeb090
    // 0xbeb08c: r7 = ""
    //     0xbeb08c: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeb090: LoadField: r8 = r2->field_2b
    //     0xbeb090: ldur            w8, [x2, #0x2b]
    // 0xbeb094: DecompressPointer r8
    //     0xbeb094: add             x8, x8, HEAP, lsl #32
    // 0xbeb098: LoadField: r9 = r2->field_3b
    //     0xbeb098: ldur            w9, [x2, #0x3b]
    // 0xbeb09c: DecompressPointer r9
    //     0xbeb09c: add             x9, x9, HEAP, lsl #32
    // 0xbeb0a0: cmp             w9, NULL
    // 0xbeb0a4: b.ne            #0xbeb0b0
    // 0xbeb0a8: r2 = Null
    //     0xbeb0a8: mov             x2, NULL
    // 0xbeb0ac: b               #0xbeb0b8
    // 0xbeb0b0: LoadField: r2 = r9->field_b
    //     0xbeb0b0: ldur            w2, [x9, #0xb]
    // 0xbeb0b4: DecompressPointer r2
    //     0xbeb0b4: add             x2, x2, HEAP, lsl #32
    // 0xbeb0b8: LoadField: r9 = r0->field_43
    //     0xbeb0b8: ldur            w9, [x0, #0x43]
    // 0xbeb0bc: DecompressPointer r9
    //     0xbeb0bc: add             x9, x9, HEAP, lsl #32
    // 0xbeb0c0: stp             x1, x9, [SP, #0x38]
    // 0xbeb0c4: stp             x4, x3, [SP, #0x28]
    // 0xbeb0c8: stp             x6, x5, [SP, #0x18]
    // 0xbeb0cc: stp             x8, x7, [SP, #8]
    // 0xbeb0d0: str             x2, [SP]
    // 0xbeb0d4: r4 = 0
    //     0xbeb0d4: movz            x4, #0
    // 0xbeb0d8: ldr             x0, [SP, #0x40]
    // 0xbeb0dc: r16 = UnlinkedCall_0x613b5c
    //     0xbeb0dc: add             x16, PP, #0x53, lsl #12  ; [pp+0x536f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbeb0e0: add             x16, x16, #0x6f0
    // 0xbeb0e4: ldp             x5, lr, [x16]
    // 0xbeb0e8: blr             lr
    // 0xbeb0ec: r0 = Null
    //     0xbeb0ec: mov             x0, NULL
    // 0xbeb0f0: LeaveFrame
    //     0xbeb0f0: mov             SP, fp
    //     0xbeb0f4: ldp             fp, lr, [SP], #0x10
    // 0xbeb0f8: ret
    //     0xbeb0f8: ret             
    // 0xbeb0fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb0fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb100: b               #0xbeb020
    // 0xbeb104: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeb104: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbeb108, size: 0x64
    // 0xbeb108: EnterFrame
    //     0xbeb108: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb10c: mov             fp, SP
    // 0xbeb110: ldr             x0, [fp, #0x20]
    // 0xbeb114: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbeb114: ldur            w1, [x0, #0x17]
    // 0xbeb118: DecompressPointer r1
    //     0xbeb118: add             x1, x1, HEAP, lsl #32
    // 0xbeb11c: CheckStackOverflow
    //     0xbeb11c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb120: cmp             SP, x16
    //     0xbeb124: b.ls            #0xbeb164
    // 0xbeb128: LoadField: r0 = r1->field_f
    //     0xbeb128: ldur            w0, [x1, #0xf]
    // 0xbeb12c: DecompressPointer r0
    //     0xbeb12c: add             x0, x0, HEAP, lsl #32
    // 0xbeb130: LoadField: r3 = r1->field_13
    //     0xbeb130: ldur            w3, [x1, #0x13]
    // 0xbeb134: DecompressPointer r3
    //     0xbeb134: add             x3, x3, HEAP, lsl #32
    // 0xbeb138: LoadField: r2 = r3->field_37
    //     0xbeb138: ldur            w2, [x3, #0x37]
    // 0xbeb13c: DecompressPointer r2
    //     0xbeb13c: add             x2, x2, HEAP, lsl #32
    // 0xbeb140: ldr             x1, [fp, #0x10]
    // 0xbeb144: r5 = LoadInt32Instr(r1)
    //     0xbeb144: sbfx            x5, x1, #1, #0x1f
    //     0xbeb148: tbz             w1, #0, #0xbeb150
    //     0xbeb14c: ldur            x5, [x1, #7]
    // 0xbeb150: mov             x1, x0
    // 0xbeb154: r0 = lineThemeImageSlider()
    //     0xbeb154: bl              #0xbeb16c  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::lineThemeImageSlider
    // 0xbeb158: LeaveFrame
    //     0xbeb158: mov             SP, fp
    //     0xbeb15c: ldp             fp, lr, [SP], #0x10
    // 0xbeb160: ret
    //     0xbeb160: ret             
    // 0xbeb164: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb164: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb168: b               #0xbeb128
  }
  _ lineThemeImageSlider(/* No info */) {
    // ** addr: 0xbeb16c, size: 0x460
    // 0xbeb16c: EnterFrame
    //     0xbeb16c: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb170: mov             fp, SP
    // 0xbeb174: AllocStack(0x68)
    //     0xbeb174: sub             SP, SP, #0x68
    // 0xbeb178: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xbeb178: mov             x0, x2
    //     0xbeb17c: stur            x2, [fp, #-0x10]
    //     0xbeb180: mov             x2, x3
    //     0xbeb184: stur            x3, [fp, #-0x18]
    //     0xbeb188: mov             x3, x1
    //     0xbeb18c: stur            x1, [fp, #-8]
    //     0xbeb190: mov             x1, x5
    //     0xbeb194: stur            x5, [fp, #-0x20]
    // 0xbeb198: CheckStackOverflow
    //     0xbeb198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb19c: cmp             SP, x16
    //     0xbeb1a0: b.ls            #0xbeb5c0
    // 0xbeb1a4: r1 = 2
    //     0xbeb1a4: movz            x1, #0x2
    // 0xbeb1a8: r0 = AllocateContext()
    //     0xbeb1a8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbeb1ac: mov             x3, x0
    // 0xbeb1b0: ldur            x2, [fp, #-8]
    // 0xbeb1b4: stur            x3, [fp, #-0x28]
    // 0xbeb1b8: StoreField: r3->field_f = r2
    //     0xbeb1b8: stur            w2, [x3, #0xf]
    // 0xbeb1bc: ldur            x4, [fp, #-0x18]
    // 0xbeb1c0: StoreField: r3->field_13 = r4
    //     0xbeb1c0: stur            w4, [x3, #0x13]
    // 0xbeb1c4: ldur            x5, [fp, #-0x10]
    // 0xbeb1c8: cmp             w5, NULL
    // 0xbeb1cc: b.ne            #0xbeb1d8
    // 0xbeb1d0: r0 = Null
    //     0xbeb1d0: mov             x0, NULL
    // 0xbeb1d4: b               #0xbeb210
    // 0xbeb1d8: ldur            x6, [fp, #-0x20]
    // 0xbeb1dc: LoadField: r0 = r5->field_b
    //     0xbeb1dc: ldur            w0, [x5, #0xb]
    // 0xbeb1e0: r1 = LoadInt32Instr(r0)
    //     0xbeb1e0: sbfx            x1, x0, #1, #0x1f
    // 0xbeb1e4: mov             x0, x1
    // 0xbeb1e8: mov             x1, x6
    // 0xbeb1ec: cmp             x1, x0
    // 0xbeb1f0: b.hs            #0xbeb5c8
    // 0xbeb1f4: LoadField: r0 = r5->field_f
    //     0xbeb1f4: ldur            w0, [x5, #0xf]
    // 0xbeb1f8: DecompressPointer r0
    //     0xbeb1f8: add             x0, x0, HEAP, lsl #32
    // 0xbeb1fc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xbeb1fc: add             x16, x0, x6, lsl #2
    //     0xbeb200: ldur            w1, [x16, #0xf]
    // 0xbeb204: DecompressPointer r1
    //     0xbeb204: add             x1, x1, HEAP, lsl #32
    // 0xbeb208: LoadField: r0 = r1->field_b
    //     0xbeb208: ldur            w0, [x1, #0xb]
    // 0xbeb20c: DecompressPointer r0
    //     0xbeb20c: add             x0, x0, HEAP, lsl #32
    // 0xbeb210: cmp             w0, NULL
    // 0xbeb214: b.ne            #0xbeb21c
    // 0xbeb218: r0 = ""
    //     0xbeb218: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbeb21c: stur            x0, [fp, #-0x10]
    // 0xbeb220: r0 = ImageHeaders.forImages()
    //     0xbeb220: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbeb224: r1 = Function '<anonymous closure>':.
    //     0xbeb224: add             x1, PP, #0x53, lsl #12  ; [pp+0x53700] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xbeb228: ldr             x1, [x1, #0x700]
    // 0xbeb22c: r2 = Null
    //     0xbeb22c: mov             x2, NULL
    // 0xbeb230: stur            x0, [fp, #-0x30]
    // 0xbeb234: r0 = AllocateClosure()
    //     0xbeb234: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeb238: r1 = Function '<anonymous closure>':.
    //     0xbeb238: add             x1, PP, #0x53, lsl #12  ; [pp+0x53708] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbeb23c: ldr             x1, [x1, #0x708]
    // 0xbeb240: r2 = Null
    //     0xbeb240: mov             x2, NULL
    // 0xbeb244: stur            x0, [fp, #-0x38]
    // 0xbeb248: r0 = AllocateClosure()
    //     0xbeb248: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeb24c: stur            x0, [fp, #-0x40]
    // 0xbeb250: r0 = CachedNetworkImage()
    //     0xbeb250: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbeb254: stur            x0, [fp, #-0x48]
    // 0xbeb258: ldur            x16, [fp, #-0x30]
    // 0xbeb25c: r30 = Instance_BoxFit
    //     0xbeb25c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbeb260: ldr             lr, [lr, #0x118]
    // 0xbeb264: stp             lr, x16, [SP, #0x10]
    // 0xbeb268: ldur            x16, [fp, #-0x38]
    // 0xbeb26c: ldur            lr, [fp, #-0x40]
    // 0xbeb270: stp             lr, x16, [SP]
    // 0xbeb274: mov             x1, x0
    // 0xbeb278: ldur            x2, [fp, #-0x10]
    // 0xbeb27c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xbeb27c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xbeb280: ldr             x4, [x4, #0x828]
    // 0xbeb284: r0 = CachedNetworkImage()
    //     0xbeb284: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbeb288: r0 = Center()
    //     0xbeb288: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbeb28c: mov             x3, x0
    // 0xbeb290: r0 = Instance_Alignment
    //     0xbeb290: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbeb294: ldr             x0, [x0, #0xb10]
    // 0xbeb298: stur            x3, [fp, #-0x10]
    // 0xbeb29c: StoreField: r3->field_f = r0
    //     0xbeb29c: stur            w0, [x3, #0xf]
    // 0xbeb2a0: ldur            x0, [fp, #-0x48]
    // 0xbeb2a4: StoreField: r3->field_b = r0
    //     0xbeb2a4: stur            w0, [x3, #0xb]
    // 0xbeb2a8: r1 = Null
    //     0xbeb2a8: mov             x1, NULL
    // 0xbeb2ac: r2 = 2
    //     0xbeb2ac: movz            x2, #0x2
    // 0xbeb2b0: r0 = AllocateArray()
    //     0xbeb2b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbeb2b4: mov             x2, x0
    // 0xbeb2b8: ldur            x0, [fp, #-0x10]
    // 0xbeb2bc: stur            x2, [fp, #-0x30]
    // 0xbeb2c0: StoreField: r2->field_f = r0
    //     0xbeb2c0: stur            w0, [x2, #0xf]
    // 0xbeb2c4: r1 = <Widget>
    //     0xbeb2c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbeb2c8: r0 = AllocateGrowableArray()
    //     0xbeb2c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbeb2cc: mov             x3, x0
    // 0xbeb2d0: ldur            x0, [fp, #-0x30]
    // 0xbeb2d4: stur            x3, [fp, #-0x10]
    // 0xbeb2d8: StoreField: r3->field_f = r0
    //     0xbeb2d8: stur            w0, [x3, #0xf]
    // 0xbeb2dc: r0 = 2
    //     0xbeb2dc: movz            x0, #0x2
    // 0xbeb2e0: StoreField: r3->field_b = r0
    //     0xbeb2e0: stur            w0, [x3, #0xb]
    // 0xbeb2e4: ldur            x0, [fp, #-0x18]
    // 0xbeb2e8: LoadField: r1 = r0->field_7f
    //     0xbeb2e8: ldur            w1, [x0, #0x7f]
    // 0xbeb2ec: DecompressPointer r1
    //     0xbeb2ec: add             x1, x1, HEAP, lsl #32
    // 0xbeb2f0: cmp             w1, NULL
    // 0xbeb2f4: b.ne            #0xbeb300
    // 0xbeb2f8: r1 = Null
    //     0xbeb2f8: mov             x1, NULL
    // 0xbeb2fc: b               #0xbeb314
    // 0xbeb300: LoadField: r2 = r1->field_7
    //     0xbeb300: ldur            w2, [x1, #7]
    // 0xbeb304: cbnz            w2, #0xbeb310
    // 0xbeb308: r1 = false
    //     0xbeb308: add             x1, NULL, #0x30  ; false
    // 0xbeb30c: b               #0xbeb314
    // 0xbeb310: r1 = true
    //     0xbeb310: add             x1, NULL, #0x20  ; true
    // 0xbeb314: cmp             w1, NULL
    // 0xbeb318: b.eq            #0xbeb3ac
    // 0xbeb31c: tbnz            w1, #4, #0xbeb3ac
    // 0xbeb320: ldur            x1, [fp, #-8]
    // 0xbeb324: mov             x2, x0
    // 0xbeb328: r0 = _buildDiscountBadge()
    //     0xbeb328: bl              #0xbeb934  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildDiscountBadge
    // 0xbeb32c: mov             x2, x0
    // 0xbeb330: ldur            x0, [fp, #-0x10]
    // 0xbeb334: stur            x2, [fp, #-0x30]
    // 0xbeb338: LoadField: r1 = r0->field_b
    //     0xbeb338: ldur            w1, [x0, #0xb]
    // 0xbeb33c: LoadField: r3 = r0->field_f
    //     0xbeb33c: ldur            w3, [x0, #0xf]
    // 0xbeb340: DecompressPointer r3
    //     0xbeb340: add             x3, x3, HEAP, lsl #32
    // 0xbeb344: LoadField: r4 = r3->field_b
    //     0xbeb344: ldur            w4, [x3, #0xb]
    // 0xbeb348: r3 = LoadInt32Instr(r1)
    //     0xbeb348: sbfx            x3, x1, #1, #0x1f
    // 0xbeb34c: stur            x3, [fp, #-0x20]
    // 0xbeb350: r1 = LoadInt32Instr(r4)
    //     0xbeb350: sbfx            x1, x4, #1, #0x1f
    // 0xbeb354: cmp             x3, x1
    // 0xbeb358: b.ne            #0xbeb364
    // 0xbeb35c: mov             x1, x0
    // 0xbeb360: r0 = _growToNextCapacity()
    //     0xbeb360: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbeb364: ldur            x3, [fp, #-0x10]
    // 0xbeb368: ldur            x2, [fp, #-0x20]
    // 0xbeb36c: add             x0, x2, #1
    // 0xbeb370: lsl             x1, x0, #1
    // 0xbeb374: StoreField: r3->field_b = r1
    //     0xbeb374: stur            w1, [x3, #0xb]
    // 0xbeb378: LoadField: r1 = r3->field_f
    //     0xbeb378: ldur            w1, [x3, #0xf]
    // 0xbeb37c: DecompressPointer r1
    //     0xbeb37c: add             x1, x1, HEAP, lsl #32
    // 0xbeb380: ldur            x0, [fp, #-0x30]
    // 0xbeb384: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbeb384: add             x25, x1, x2, lsl #2
    //     0xbeb388: add             x25, x25, #0xf
    //     0xbeb38c: str             w0, [x25]
    //     0xbeb390: tbz             w0, #0, #0xbeb3ac
    //     0xbeb394: ldurb           w16, [x1, #-1]
    //     0xbeb398: ldurb           w17, [x0, #-1]
    //     0xbeb39c: and             x16, x17, x16, lsr #2
    //     0xbeb3a0: tst             x16, HEAP, lsr #32
    //     0xbeb3a4: b.eq            #0xbeb3ac
    //     0xbeb3a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbeb3ac: ldur            x0, [fp, #-0x18]
    // 0xbeb3b0: LoadField: r1 = r0->field_b7
    //     0xbeb3b0: ldur            w1, [x0, #0xb7]
    // 0xbeb3b4: DecompressPointer r1
    //     0xbeb3b4: add             x1, x1, HEAP, lsl #32
    // 0xbeb3b8: cmp             w1, NULL
    // 0xbeb3bc: b.ne            #0xbeb3c8
    // 0xbeb3c0: r1 = Null
    //     0xbeb3c0: mov             x1, NULL
    // 0xbeb3c4: b               #0xbeb3dc
    // 0xbeb3c8: LoadField: r2 = r1->field_7
    //     0xbeb3c8: ldur            w2, [x1, #7]
    // 0xbeb3cc: cbnz            w2, #0xbeb3d8
    // 0xbeb3d0: r1 = false
    //     0xbeb3d0: add             x1, NULL, #0x30  ; false
    // 0xbeb3d4: b               #0xbeb3dc
    // 0xbeb3d8: r1 = true
    //     0xbeb3d8: add             x1, NULL, #0x20  ; true
    // 0xbeb3dc: cmp             w1, NULL
    // 0xbeb3e0: b.ne            #0xbeb3ec
    // 0xbeb3e4: mov             x2, x3
    // 0xbeb3e8: b               #0xbeb484
    // 0xbeb3ec: tbnz            w1, #4, #0xbeb480
    // 0xbeb3f0: ldur            x1, [fp, #-8]
    // 0xbeb3f4: mov             x2, x0
    // 0xbeb3f8: r0 = _buildStockAlert()
    //     0xbeb3f8: bl              #0xbeb72c  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildStockAlert
    // 0xbeb3fc: mov             x2, x0
    // 0xbeb400: ldur            x0, [fp, #-0x10]
    // 0xbeb404: stur            x2, [fp, #-0x30]
    // 0xbeb408: LoadField: r1 = r0->field_b
    //     0xbeb408: ldur            w1, [x0, #0xb]
    // 0xbeb40c: LoadField: r3 = r0->field_f
    //     0xbeb40c: ldur            w3, [x0, #0xf]
    // 0xbeb410: DecompressPointer r3
    //     0xbeb410: add             x3, x3, HEAP, lsl #32
    // 0xbeb414: LoadField: r4 = r3->field_b
    //     0xbeb414: ldur            w4, [x3, #0xb]
    // 0xbeb418: r3 = LoadInt32Instr(r1)
    //     0xbeb418: sbfx            x3, x1, #1, #0x1f
    // 0xbeb41c: stur            x3, [fp, #-0x20]
    // 0xbeb420: r1 = LoadInt32Instr(r4)
    //     0xbeb420: sbfx            x1, x4, #1, #0x1f
    // 0xbeb424: cmp             x3, x1
    // 0xbeb428: b.ne            #0xbeb434
    // 0xbeb42c: mov             x1, x0
    // 0xbeb430: r0 = _growToNextCapacity()
    //     0xbeb430: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbeb434: ldur            x2, [fp, #-0x10]
    // 0xbeb438: ldur            x3, [fp, #-0x20]
    // 0xbeb43c: add             x0, x3, #1
    // 0xbeb440: lsl             x1, x0, #1
    // 0xbeb444: StoreField: r2->field_b = r1
    //     0xbeb444: stur            w1, [x2, #0xb]
    // 0xbeb448: LoadField: r1 = r2->field_f
    //     0xbeb448: ldur            w1, [x2, #0xf]
    // 0xbeb44c: DecompressPointer r1
    //     0xbeb44c: add             x1, x1, HEAP, lsl #32
    // 0xbeb450: ldur            x0, [fp, #-0x30]
    // 0xbeb454: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbeb454: add             x25, x1, x3, lsl #2
    //     0xbeb458: add             x25, x25, #0xf
    //     0xbeb45c: str             w0, [x25]
    //     0xbeb460: tbz             w0, #0, #0xbeb47c
    //     0xbeb464: ldurb           w16, [x1, #-1]
    //     0xbeb468: ldurb           w17, [x0, #-1]
    //     0xbeb46c: and             x16, x17, x16, lsr #2
    //     0xbeb470: tst             x16, HEAP, lsr #32
    //     0xbeb474: b.eq            #0xbeb47c
    //     0xbeb478: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbeb47c: b               #0xbeb484
    // 0xbeb480: mov             x2, x3
    // 0xbeb484: ldur            x0, [fp, #-0x18]
    // 0xbeb488: LoadField: r1 = r0->field_8b
    //     0xbeb488: ldur            w1, [x0, #0x8b]
    // 0xbeb48c: DecompressPointer r1
    //     0xbeb48c: add             x1, x1, HEAP, lsl #32
    // 0xbeb490: cmp             w1, NULL
    // 0xbeb494: b.eq            #0xbeb524
    // 0xbeb498: tbnz            w1, #4, #0xbeb524
    // 0xbeb49c: ldur            x1, [fp, #-8]
    // 0xbeb4a0: r0 = _buildCustomizationBadge()
    //     0xbeb4a0: bl              #0xbeb5cc  ; [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildCustomizationBadge
    // 0xbeb4a4: mov             x2, x0
    // 0xbeb4a8: ldur            x0, [fp, #-0x10]
    // 0xbeb4ac: stur            x2, [fp, #-8]
    // 0xbeb4b0: LoadField: r1 = r0->field_b
    //     0xbeb4b0: ldur            w1, [x0, #0xb]
    // 0xbeb4b4: LoadField: r3 = r0->field_f
    //     0xbeb4b4: ldur            w3, [x0, #0xf]
    // 0xbeb4b8: DecompressPointer r3
    //     0xbeb4b8: add             x3, x3, HEAP, lsl #32
    // 0xbeb4bc: LoadField: r4 = r3->field_b
    //     0xbeb4bc: ldur            w4, [x3, #0xb]
    // 0xbeb4c0: r3 = LoadInt32Instr(r1)
    //     0xbeb4c0: sbfx            x3, x1, #1, #0x1f
    // 0xbeb4c4: stur            x3, [fp, #-0x20]
    // 0xbeb4c8: r1 = LoadInt32Instr(r4)
    //     0xbeb4c8: sbfx            x1, x4, #1, #0x1f
    // 0xbeb4cc: cmp             x3, x1
    // 0xbeb4d0: b.ne            #0xbeb4dc
    // 0xbeb4d4: mov             x1, x0
    // 0xbeb4d8: r0 = _growToNextCapacity()
    //     0xbeb4d8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbeb4dc: ldur            x2, [fp, #-0x10]
    // 0xbeb4e0: ldur            x3, [fp, #-0x20]
    // 0xbeb4e4: add             x0, x3, #1
    // 0xbeb4e8: lsl             x1, x0, #1
    // 0xbeb4ec: StoreField: r2->field_b = r1
    //     0xbeb4ec: stur            w1, [x2, #0xb]
    // 0xbeb4f0: LoadField: r1 = r2->field_f
    //     0xbeb4f0: ldur            w1, [x2, #0xf]
    // 0xbeb4f4: DecompressPointer r1
    //     0xbeb4f4: add             x1, x1, HEAP, lsl #32
    // 0xbeb4f8: ldur            x0, [fp, #-8]
    // 0xbeb4fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbeb4fc: add             x25, x1, x3, lsl #2
    //     0xbeb500: add             x25, x25, #0xf
    //     0xbeb504: str             w0, [x25]
    //     0xbeb508: tbz             w0, #0, #0xbeb524
    //     0xbeb50c: ldurb           w16, [x1, #-1]
    //     0xbeb510: ldurb           w17, [x0, #-1]
    //     0xbeb514: and             x16, x17, x16, lsr #2
    //     0xbeb518: tst             x16, HEAP, lsr #32
    //     0xbeb51c: b.eq            #0xbeb524
    //     0xbeb520: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbeb524: r0 = Stack()
    //     0xbeb524: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbeb528: mov             x1, x0
    // 0xbeb52c: r0 = Instance_Alignment
    //     0xbeb52c: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xbeb530: ldr             x0, [x0, #0x5b8]
    // 0xbeb534: stur            x1, [fp, #-8]
    // 0xbeb538: StoreField: r1->field_f = r0
    //     0xbeb538: stur            w0, [x1, #0xf]
    // 0xbeb53c: r0 = Instance_StackFit
    //     0xbeb53c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbeb540: ldr             x0, [x0, #0xfa8]
    // 0xbeb544: ArrayStore: r1[0] = r0  ; List_4
    //     0xbeb544: stur            w0, [x1, #0x17]
    // 0xbeb548: r0 = Instance_Clip
    //     0xbeb548: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbeb54c: ldr             x0, [x0, #0x7e0]
    // 0xbeb550: StoreField: r1->field_1b = r0
    //     0xbeb550: stur            w0, [x1, #0x1b]
    // 0xbeb554: ldur            x0, [fp, #-0x10]
    // 0xbeb558: StoreField: r1->field_b = r0
    //     0xbeb558: stur            w0, [x1, #0xb]
    // 0xbeb55c: r0 = InkWell()
    //     0xbeb55c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbeb560: mov             x3, x0
    // 0xbeb564: ldur            x0, [fp, #-8]
    // 0xbeb568: stur            x3, [fp, #-0x10]
    // 0xbeb56c: StoreField: r3->field_b = r0
    //     0xbeb56c: stur            w0, [x3, #0xb]
    // 0xbeb570: ldur            x2, [fp, #-0x28]
    // 0xbeb574: r1 = Function '<anonymous closure>':.
    //     0xbeb574: add             x1, PP, #0x53, lsl #12  ; [pp+0x53710] AnonymousClosure: (0xbeafb8), in [package:customer_app/app/presentation/views/line/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::lineThemeSlider (0xbe9c18)
    //     0xbeb578: ldr             x1, [x1, #0x710]
    // 0xbeb57c: r0 = AllocateClosure()
    //     0xbeb57c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeb580: mov             x1, x0
    // 0xbeb584: ldur            x0, [fp, #-0x10]
    // 0xbeb588: StoreField: r0->field_f = r1
    //     0xbeb588: stur            w1, [x0, #0xf]
    // 0xbeb58c: r1 = true
    //     0xbeb58c: add             x1, NULL, #0x20  ; true
    // 0xbeb590: StoreField: r0->field_43 = r1
    //     0xbeb590: stur            w1, [x0, #0x43]
    // 0xbeb594: r2 = Instance_BoxShape
    //     0xbeb594: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbeb598: ldr             x2, [x2, #0x80]
    // 0xbeb59c: StoreField: r0->field_47 = r2
    //     0xbeb59c: stur            w2, [x0, #0x47]
    // 0xbeb5a0: StoreField: r0->field_6f = r1
    //     0xbeb5a0: stur            w1, [x0, #0x6f]
    // 0xbeb5a4: r2 = false
    //     0xbeb5a4: add             x2, NULL, #0x30  ; false
    // 0xbeb5a8: StoreField: r0->field_73 = r2
    //     0xbeb5a8: stur            w2, [x0, #0x73]
    // 0xbeb5ac: StoreField: r0->field_83 = r1
    //     0xbeb5ac: stur            w1, [x0, #0x83]
    // 0xbeb5b0: StoreField: r0->field_7b = r2
    //     0xbeb5b0: stur            w2, [x0, #0x7b]
    // 0xbeb5b4: LeaveFrame
    //     0xbeb5b4: mov             SP, fp
    //     0xbeb5b8: ldp             fp, lr, [SP], #0x10
    // 0xbeb5bc: ret
    //     0xbeb5bc: ret             
    // 0xbeb5c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb5c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb5c4: b               #0xbeb1a4
    // 0xbeb5c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbeb5c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationBadge(/* No info */) {
    // ** addr: 0xbeb5cc, size: 0x160
    // 0xbeb5cc: EnterFrame
    //     0xbeb5cc: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb5d0: mov             fp, SP
    // 0xbeb5d4: AllocStack(0x30)
    //     0xbeb5d4: sub             SP, SP, #0x30
    // 0xbeb5d8: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xbeb5d8: mov             x0, x1
    //     0xbeb5dc: stur            x1, [fp, #-8]
    // 0xbeb5e0: CheckStackOverflow
    //     0xbeb5e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb5e4: cmp             SP, x16
    //     0xbeb5e8: b.ls            #0xbeb71c
    // 0xbeb5ec: LoadField: r1 = r0->field_f
    //     0xbeb5ec: ldur            w1, [x0, #0xf]
    // 0xbeb5f0: DecompressPointer r1
    //     0xbeb5f0: add             x1, x1, HEAP, lsl #32
    // 0xbeb5f4: cmp             w1, NULL
    // 0xbeb5f8: b.eq            #0xbeb724
    // 0xbeb5fc: r0 = of()
    //     0xbeb5fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbeb600: r17 = 307
    //     0xbeb600: movz            x17, #0x133
    // 0xbeb604: ldr             w2, [x0, x17]
    // 0xbeb608: DecompressPointer r2
    //     0xbeb608: add             x2, x2, HEAP, lsl #32
    // 0xbeb60c: ldur            x0, [fp, #-8]
    // 0xbeb610: stur            x2, [fp, #-0x10]
    // 0xbeb614: LoadField: r1 = r0->field_f
    //     0xbeb614: ldur            w1, [x0, #0xf]
    // 0xbeb618: DecompressPointer r1
    //     0xbeb618: add             x1, x1, HEAP, lsl #32
    // 0xbeb61c: cmp             w1, NULL
    // 0xbeb620: b.eq            #0xbeb728
    // 0xbeb624: r0 = of()
    //     0xbeb624: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbeb628: LoadField: r1 = r0->field_87
    //     0xbeb628: ldur            w1, [x0, #0x87]
    // 0xbeb62c: DecompressPointer r1
    //     0xbeb62c: add             x1, x1, HEAP, lsl #32
    // 0xbeb630: LoadField: r0 = r1->field_2b
    //     0xbeb630: ldur            w0, [x1, #0x2b]
    // 0xbeb634: DecompressPointer r0
    //     0xbeb634: add             x0, x0, HEAP, lsl #32
    // 0xbeb638: r16 = 12.000000
    //     0xbeb638: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbeb63c: ldr             x16, [x16, #0x9e8]
    // 0xbeb640: r30 = Instance_Color
    //     0xbeb640: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbeb644: stp             lr, x16, [SP]
    // 0xbeb648: mov             x1, x0
    // 0xbeb64c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbeb64c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbeb650: ldr             x4, [x4, #0xaa0]
    // 0xbeb654: r0 = copyWith()
    //     0xbeb654: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbeb658: stur            x0, [fp, #-8]
    // 0xbeb65c: r0 = Text()
    //     0xbeb65c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbeb660: mov             x3, x0
    // 0xbeb664: r0 = "Customisable"
    //     0xbeb664: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xbeb668: ldr             x0, [x0, #0x970]
    // 0xbeb66c: stur            x3, [fp, #-0x18]
    // 0xbeb670: StoreField: r3->field_b = r0
    //     0xbeb670: stur            w0, [x3, #0xb]
    // 0xbeb674: ldur            x0, [fp, #-8]
    // 0xbeb678: StoreField: r3->field_13 = r0
    //     0xbeb678: stur            w0, [x3, #0x13]
    // 0xbeb67c: r1 = Function '<anonymous closure>':.
    //     0xbeb67c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53718] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbeb680: ldr             x1, [x1, #0x718]
    // 0xbeb684: r2 = Null
    //     0xbeb684: mov             x2, NULL
    // 0xbeb688: r0 = AllocateClosure()
    //     0xbeb688: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeb68c: stur            x0, [fp, #-8]
    // 0xbeb690: r0 = TextButton()
    //     0xbeb690: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbeb694: mov             x1, x0
    // 0xbeb698: ldur            x0, [fp, #-8]
    // 0xbeb69c: stur            x1, [fp, #-0x20]
    // 0xbeb6a0: StoreField: r1->field_b = r0
    //     0xbeb6a0: stur            w0, [x1, #0xb]
    // 0xbeb6a4: r0 = false
    //     0xbeb6a4: add             x0, NULL, #0x30  ; false
    // 0xbeb6a8: StoreField: r1->field_27 = r0
    //     0xbeb6a8: stur            w0, [x1, #0x27]
    // 0xbeb6ac: r0 = true
    //     0xbeb6ac: add             x0, NULL, #0x20  ; true
    // 0xbeb6b0: StoreField: r1->field_2f = r0
    //     0xbeb6b0: stur            w0, [x1, #0x2f]
    // 0xbeb6b4: ldur            x0, [fp, #-0x18]
    // 0xbeb6b8: StoreField: r1->field_37 = r0
    //     0xbeb6b8: stur            w0, [x1, #0x37]
    // 0xbeb6bc: r0 = TextButtonTheme()
    //     0xbeb6bc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbeb6c0: mov             x1, x0
    // 0xbeb6c4: ldur            x0, [fp, #-0x10]
    // 0xbeb6c8: stur            x1, [fp, #-8]
    // 0xbeb6cc: StoreField: r1->field_f = r0
    //     0xbeb6cc: stur            w0, [x1, #0xf]
    // 0xbeb6d0: ldur            x0, [fp, #-0x20]
    // 0xbeb6d4: StoreField: r1->field_b = r0
    //     0xbeb6d4: stur            w0, [x1, #0xb]
    // 0xbeb6d8: r0 = SizedBox()
    //     0xbeb6d8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbeb6dc: mov             x1, x0
    // 0xbeb6e0: r0 = 35.000000
    //     0xbeb6e0: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xbeb6e4: ldr             x0, [x0, #0x2b0]
    // 0xbeb6e8: stur            x1, [fp, #-0x10]
    // 0xbeb6ec: StoreField: r1->field_13 = r0
    //     0xbeb6ec: stur            w0, [x1, #0x13]
    // 0xbeb6f0: ldur            x0, [fp, #-8]
    // 0xbeb6f4: StoreField: r1->field_b = r0
    //     0xbeb6f4: stur            w0, [x1, #0xb]
    // 0xbeb6f8: r0 = Padding()
    //     0xbeb6f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbeb6fc: r1 = Instance_EdgeInsets
    //     0xbeb6fc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xbeb700: ldr             x1, [x1, #0xe68]
    // 0xbeb704: StoreField: r0->field_f = r1
    //     0xbeb704: stur            w1, [x0, #0xf]
    // 0xbeb708: ldur            x1, [fp, #-0x10]
    // 0xbeb70c: StoreField: r0->field_b = r1
    //     0xbeb70c: stur            w1, [x0, #0xb]
    // 0xbeb710: LeaveFrame
    //     0xbeb710: mov             SP, fp
    //     0xbeb714: ldp             fp, lr, [SP], #0x10
    // 0xbeb718: ret
    //     0xbeb718: ret             
    // 0xbeb71c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb71c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb720: b               #0xbeb5ec
    // 0xbeb724: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeb724: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbeb728: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeb728: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildStockAlert(/* No info */) {
    // ** addr: 0xbeb72c, size: 0x208
    // 0xbeb72c: EnterFrame
    //     0xbeb72c: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb730: mov             fp, SP
    // 0xbeb734: AllocStack(0x50)
    //     0xbeb734: sub             SP, SP, #0x50
    // 0xbeb738: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbeb738: stur            x1, [fp, #-8]
    //     0xbeb73c: stur            x2, [fp, #-0x10]
    // 0xbeb740: CheckStackOverflow
    //     0xbeb740: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb744: cmp             SP, x16
    //     0xbeb748: b.ls            #0xbeb928
    // 0xbeb74c: LoadField: r0 = r2->field_8b
    //     0xbeb74c: ldur            w0, [x2, #0x8b]
    // 0xbeb750: DecompressPointer r0
    //     0xbeb750: add             x0, x0, HEAP, lsl #32
    // 0xbeb754: cmp             w0, NULL
    // 0xbeb758: b.eq            #0xbeb76c
    // 0xbeb75c: tbnz            w0, #4, #0xbeb76c
    // 0xbeb760: d0 = 38.000000
    //     0xbeb760: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xbeb764: ldr             d0, [x17, #0xd10]
    // 0xbeb768: b               #0xbeb770
    // 0xbeb76c: d0 = 4.000000
    //     0xbeb76c: fmov            d0, #4.00000000
    // 0xbeb770: stur            d0, [fp, #-0x40]
    // 0xbeb774: r0 = EdgeInsets()
    //     0xbeb774: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xbeb778: d0 = 8.000000
    //     0xbeb778: fmov            d0, #8.00000000
    // 0xbeb77c: stur            x0, [fp, #-0x18]
    // 0xbeb780: StoreField: r0->field_7 = d0
    //     0xbeb780: stur            d0, [x0, #7]
    // 0xbeb784: StoreField: r0->field_f = rZR
    //     0xbeb784: stur            xzr, [x0, #0xf]
    // 0xbeb788: ArrayStore: r0[0] = rZR  ; List_8
    //     0xbeb788: stur            xzr, [x0, #0x17]
    // 0xbeb78c: ldur            d0, [fp, #-0x40]
    // 0xbeb790: StoreField: r0->field_1f = d0
    //     0xbeb790: stur            d0, [x0, #0x1f]
    // 0xbeb794: r16 = <EdgeInsets>
    //     0xbeb794: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbeb798: ldr             x16, [x16, #0xda0]
    // 0xbeb79c: r30 = Instance_EdgeInsets
    //     0xbeb79c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbeb7a0: ldr             lr, [lr, #0x668]
    // 0xbeb7a4: stp             lr, x16, [SP]
    // 0xbeb7a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbeb7a8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbeb7ac: r0 = all()
    //     0xbeb7ac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbeb7b0: stur            x0, [fp, #-0x20]
    // 0xbeb7b4: r16 = <Color>
    //     0xbeb7b4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbeb7b8: ldr             x16, [x16, #0xf80]
    // 0xbeb7bc: r30 = Instance_Color
    //     0xbeb7bc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbeb7c0: stp             lr, x16, [SP]
    // 0xbeb7c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbeb7c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbeb7c8: r0 = all()
    //     0xbeb7c8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbeb7cc: stur            x0, [fp, #-0x28]
    // 0xbeb7d0: r16 = <RoundedRectangleBorder>
    //     0xbeb7d0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbeb7d4: ldr             x16, [x16, #0xf78]
    // 0xbeb7d8: r30 = Instance_RoundedRectangleBorder
    //     0xbeb7d8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbeb7dc: ldr             lr, [lr, #0xd68]
    // 0xbeb7e0: stp             lr, x16, [SP]
    // 0xbeb7e4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbeb7e4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbeb7e8: r0 = all()
    //     0xbeb7e8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbeb7ec: stur            x0, [fp, #-0x30]
    // 0xbeb7f0: r0 = ButtonStyle()
    //     0xbeb7f0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbeb7f4: mov             x1, x0
    // 0xbeb7f8: ldur            x0, [fp, #-0x28]
    // 0xbeb7fc: stur            x1, [fp, #-0x38]
    // 0xbeb800: StoreField: r1->field_b = r0
    //     0xbeb800: stur            w0, [x1, #0xb]
    // 0xbeb804: ldur            x0, [fp, #-0x20]
    // 0xbeb808: StoreField: r1->field_23 = r0
    //     0xbeb808: stur            w0, [x1, #0x23]
    // 0xbeb80c: ldur            x0, [fp, #-0x30]
    // 0xbeb810: StoreField: r1->field_43 = r0
    //     0xbeb810: stur            w0, [x1, #0x43]
    // 0xbeb814: r0 = TextButtonThemeData()
    //     0xbeb814: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbeb818: mov             x1, x0
    // 0xbeb81c: ldur            x0, [fp, #-0x38]
    // 0xbeb820: stur            x1, [fp, #-0x20]
    // 0xbeb824: StoreField: r1->field_7 = r0
    //     0xbeb824: stur            w0, [x1, #7]
    // 0xbeb828: ldur            x0, [fp, #-0x10]
    // 0xbeb82c: LoadField: r2 = r0->field_b7
    //     0xbeb82c: ldur            w2, [x0, #0xb7]
    // 0xbeb830: DecompressPointer r2
    //     0xbeb830: add             x2, x2, HEAP, lsl #32
    // 0xbeb834: str             x2, [SP]
    // 0xbeb838: r0 = _interpolateSingle()
    //     0xbeb838: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbeb83c: mov             x2, x0
    // 0xbeb840: ldur            x0, [fp, #-8]
    // 0xbeb844: stur            x2, [fp, #-0x10]
    // 0xbeb848: LoadField: r1 = r0->field_f
    //     0xbeb848: ldur            w1, [x0, #0xf]
    // 0xbeb84c: DecompressPointer r1
    //     0xbeb84c: add             x1, x1, HEAP, lsl #32
    // 0xbeb850: cmp             w1, NULL
    // 0xbeb854: b.eq            #0xbeb930
    // 0xbeb858: r0 = of()
    //     0xbeb858: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbeb85c: LoadField: r1 = r0->field_87
    //     0xbeb85c: ldur            w1, [x0, #0x87]
    // 0xbeb860: DecompressPointer r1
    //     0xbeb860: add             x1, x1, HEAP, lsl #32
    // 0xbeb864: LoadField: r0 = r1->field_2b
    //     0xbeb864: ldur            w0, [x1, #0x2b]
    // 0xbeb868: DecompressPointer r0
    //     0xbeb868: add             x0, x0, HEAP, lsl #32
    // 0xbeb86c: r16 = 12.000000
    //     0xbeb86c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbeb870: ldr             x16, [x16, #0x9e8]
    // 0xbeb874: r30 = Instance_Color
    //     0xbeb874: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbeb878: stp             lr, x16, [SP]
    // 0xbeb87c: mov             x1, x0
    // 0xbeb880: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbeb880: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbeb884: ldr             x4, [x4, #0xaa0]
    // 0xbeb888: r0 = copyWith()
    //     0xbeb888: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbeb88c: stur            x0, [fp, #-8]
    // 0xbeb890: r0 = Text()
    //     0xbeb890: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbeb894: mov             x3, x0
    // 0xbeb898: ldur            x0, [fp, #-0x10]
    // 0xbeb89c: stur            x3, [fp, #-0x28]
    // 0xbeb8a0: StoreField: r3->field_b = r0
    //     0xbeb8a0: stur            w0, [x3, #0xb]
    // 0xbeb8a4: ldur            x0, [fp, #-8]
    // 0xbeb8a8: StoreField: r3->field_13 = r0
    //     0xbeb8a8: stur            w0, [x3, #0x13]
    // 0xbeb8ac: r1 = Function '<anonymous closure>':.
    //     0xbeb8ac: add             x1, PP, #0x53, lsl #12  ; [pp+0x53720] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbeb8b0: ldr             x1, [x1, #0x720]
    // 0xbeb8b4: r2 = Null
    //     0xbeb8b4: mov             x2, NULL
    // 0xbeb8b8: r0 = AllocateClosure()
    //     0xbeb8b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbeb8bc: stur            x0, [fp, #-8]
    // 0xbeb8c0: r0 = TextButton()
    //     0xbeb8c0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbeb8c4: mov             x1, x0
    // 0xbeb8c8: ldur            x0, [fp, #-8]
    // 0xbeb8cc: stur            x1, [fp, #-0x10]
    // 0xbeb8d0: StoreField: r1->field_b = r0
    //     0xbeb8d0: stur            w0, [x1, #0xb]
    // 0xbeb8d4: r0 = false
    //     0xbeb8d4: add             x0, NULL, #0x30  ; false
    // 0xbeb8d8: StoreField: r1->field_27 = r0
    //     0xbeb8d8: stur            w0, [x1, #0x27]
    // 0xbeb8dc: r0 = true
    //     0xbeb8dc: add             x0, NULL, #0x20  ; true
    // 0xbeb8e0: StoreField: r1->field_2f = r0
    //     0xbeb8e0: stur            w0, [x1, #0x2f]
    // 0xbeb8e4: ldur            x0, [fp, #-0x28]
    // 0xbeb8e8: StoreField: r1->field_37 = r0
    //     0xbeb8e8: stur            w0, [x1, #0x37]
    // 0xbeb8ec: r0 = TextButtonTheme()
    //     0xbeb8ec: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbeb8f0: mov             x1, x0
    // 0xbeb8f4: ldur            x0, [fp, #-0x20]
    // 0xbeb8f8: stur            x1, [fp, #-8]
    // 0xbeb8fc: StoreField: r1->field_f = r0
    //     0xbeb8fc: stur            w0, [x1, #0xf]
    // 0xbeb900: ldur            x0, [fp, #-0x10]
    // 0xbeb904: StoreField: r1->field_b = r0
    //     0xbeb904: stur            w0, [x1, #0xb]
    // 0xbeb908: r0 = Padding()
    //     0xbeb908: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbeb90c: ldur            x1, [fp, #-0x18]
    // 0xbeb910: StoreField: r0->field_f = r1
    //     0xbeb910: stur            w1, [x0, #0xf]
    // 0xbeb914: ldur            x1, [fp, #-8]
    // 0xbeb918: StoreField: r0->field_b = r1
    //     0xbeb918: stur            w1, [x0, #0xb]
    // 0xbeb91c: LeaveFrame
    //     0xbeb91c: mov             SP, fp
    //     0xbeb920: ldp             fp, lr, [SP], #0x10
    // 0xbeb924: ret
    //     0xbeb924: ret             
    // 0xbeb928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbeb928: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbeb92c: b               #0xbeb74c
    // 0xbeb930: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbeb930: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildDiscountBadge(/* No info */) {
    // ** addr: 0xbeb934, size: 0x810
    // 0xbeb934: EnterFrame
    //     0xbeb934: stp             fp, lr, [SP, #-0x10]!
    //     0xbeb938: mov             fp, SP
    // 0xbeb93c: AllocStack(0x70)
    //     0xbeb93c: sub             SP, SP, #0x70
    // 0xbeb940: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x28 */, dynamic _ /* r2 => r2, fp-0x30 */)
    //     0xbeb940: stur            x1, [fp, #-0x28]
    //     0xbeb944: stur            x2, [fp, #-0x30]
    // 0xbeb948: CheckStackOverflow
    //     0xbeb948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbeb94c: cmp             SP, x16
    //     0xbeb950: b.ls            #0xbec110
    // 0xbeb954: LoadField: r0 = r2->field_93
    //     0xbeb954: ldur            w0, [x2, #0x93]
    // 0xbeb958: DecompressPointer r0
    //     0xbeb958: add             x0, x0, HEAP, lsl #32
    // 0xbeb95c: cmp             w0, NULL
    // 0xbeb960: b.ne            #0xbeb984
    // 0xbeb964: mov             x3, x1
    // 0xbeb968: mov             x0, x2
    // 0xbeb96c: r4 = Instance_Alignment
    //     0xbeb96c: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbeb970: ldr             x4, [x4, #0xfa0]
    // 0xbeb974: r2 = 4
    //     0xbeb974: movz            x2, #0x4
    // 0xbeb978: d0 = 0.700000
    //     0xbeb978: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbeb97c: ldr             d0, [x17, #0xf48]
    // 0xbeb980: b               #0xbebe54
    // 0xbeb984: tbnz            w0, #4, #0xbebe38
    // 0xbeb988: LoadField: r0 = r1->field_b
    //     0xbeb988: ldur            w0, [x1, #0xb]
    // 0xbeb98c: DecompressPointer r0
    //     0xbeb98c: add             x0, x0, HEAP, lsl #32
    // 0xbeb990: cmp             w0, NULL
    // 0xbeb994: b.eq            #0xbec118
    // 0xbeb998: LoadField: r3 = r0->field_33
    //     0xbeb998: ldur            w3, [x0, #0x33]
    // 0xbeb99c: DecompressPointer r3
    //     0xbeb99c: add             x3, x3, HEAP, lsl #32
    // 0xbeb9a0: stur            x3, [fp, #-0x20]
    // 0xbeb9a4: cmp             w3, NULL
    // 0xbeb9a8: b.ne            #0xbeb9b4
    // 0xbeb9ac: r0 = Null
    //     0xbeb9ac: mov             x0, NULL
    // 0xbeb9b0: b               #0xbeb9d8
    // 0xbeb9b4: LoadField: r0 = r3->field_13
    //     0xbeb9b4: ldur            w0, [x3, #0x13]
    // 0xbeb9b8: DecompressPointer r0
    //     0xbeb9b8: add             x0, x0, HEAP, lsl #32
    // 0xbeb9bc: cmp             w0, NULL
    // 0xbeb9c0: b.ne            #0xbeb9cc
    // 0xbeb9c4: r0 = Null
    //     0xbeb9c4: mov             x0, NULL
    // 0xbeb9c8: b               #0xbeb9d8
    // 0xbeb9cc: LoadField: r4 = r0->field_7
    //     0xbeb9cc: ldur            w4, [x0, #7]
    // 0xbeb9d0: DecompressPointer r4
    //     0xbeb9d0: add             x4, x4, HEAP, lsl #32
    // 0xbeb9d4: mov             x0, x4
    // 0xbeb9d8: cmp             w0, NULL
    // 0xbeb9dc: b.ne            #0xbeb9e8
    // 0xbeb9e0: r0 = 0
    //     0xbeb9e0: movz            x0, #0
    // 0xbeb9e4: b               #0xbeb9f8
    // 0xbeb9e8: r4 = LoadInt32Instr(r0)
    //     0xbeb9e8: sbfx            x4, x0, #1, #0x1f
    //     0xbeb9ec: tbz             w0, #0, #0xbeb9f4
    //     0xbeb9f0: ldur            x4, [x0, #7]
    // 0xbeb9f4: mov             x0, x4
    // 0xbeb9f8: stur            x0, [fp, #-0x18]
    // 0xbeb9fc: cmp             w3, NULL
    // 0xbeba00: b.ne            #0xbeba0c
    // 0xbeba04: r4 = Null
    //     0xbeba04: mov             x4, NULL
    // 0xbeba08: b               #0xbeba30
    // 0xbeba0c: LoadField: r4 = r3->field_13
    //     0xbeba0c: ldur            w4, [x3, #0x13]
    // 0xbeba10: DecompressPointer r4
    //     0xbeba10: add             x4, x4, HEAP, lsl #32
    // 0xbeba14: cmp             w4, NULL
    // 0xbeba18: b.ne            #0xbeba24
    // 0xbeba1c: r4 = Null
    //     0xbeba1c: mov             x4, NULL
    // 0xbeba20: b               #0xbeba30
    // 0xbeba24: LoadField: r5 = r4->field_b
    //     0xbeba24: ldur            w5, [x4, #0xb]
    // 0xbeba28: DecompressPointer r5
    //     0xbeba28: add             x5, x5, HEAP, lsl #32
    // 0xbeba2c: mov             x4, x5
    // 0xbeba30: cmp             w4, NULL
    // 0xbeba34: b.ne            #0xbeba40
    // 0xbeba38: r4 = 0
    //     0xbeba38: movz            x4, #0
    // 0xbeba3c: b               #0xbeba50
    // 0xbeba40: r5 = LoadInt32Instr(r4)
    //     0xbeba40: sbfx            x5, x4, #1, #0x1f
    //     0xbeba44: tbz             w4, #0, #0xbeba4c
    //     0xbeba48: ldur            x5, [x4, #7]
    // 0xbeba4c: mov             x4, x5
    // 0xbeba50: stur            x4, [fp, #-0x10]
    // 0xbeba54: cmp             w3, NULL
    // 0xbeba58: b.ne            #0xbeba64
    // 0xbeba5c: r5 = Null
    //     0xbeba5c: mov             x5, NULL
    // 0xbeba60: b               #0xbeba88
    // 0xbeba64: LoadField: r5 = r3->field_13
    //     0xbeba64: ldur            w5, [x3, #0x13]
    // 0xbeba68: DecompressPointer r5
    //     0xbeba68: add             x5, x5, HEAP, lsl #32
    // 0xbeba6c: cmp             w5, NULL
    // 0xbeba70: b.ne            #0xbeba7c
    // 0xbeba74: r5 = Null
    //     0xbeba74: mov             x5, NULL
    // 0xbeba78: b               #0xbeba88
    // 0xbeba7c: LoadField: r6 = r5->field_f
    //     0xbeba7c: ldur            w6, [x5, #0xf]
    // 0xbeba80: DecompressPointer r6
    //     0xbeba80: add             x6, x6, HEAP, lsl #32
    // 0xbeba84: mov             x5, x6
    // 0xbeba88: cmp             w5, NULL
    // 0xbeba8c: b.ne            #0xbeba98
    // 0xbeba90: r5 = 0
    //     0xbeba90: movz            x5, #0
    // 0xbeba94: b               #0xbebaa8
    // 0xbeba98: r6 = LoadInt32Instr(r5)
    //     0xbeba98: sbfx            x6, x5, #1, #0x1f
    //     0xbeba9c: tbz             w5, #0, #0xbebaa4
    //     0xbebaa0: ldur            x6, [x5, #7]
    // 0xbebaa4: mov             x5, x6
    // 0xbebaa8: stur            x5, [fp, #-8]
    // 0xbebaac: r0 = Color()
    //     0xbebaac: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbebab0: mov             x1, x0
    // 0xbebab4: r0 = Instance_ColorSpace
    //     0xbebab4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbebab8: stur            x1, [fp, #-0x38]
    // 0xbebabc: StoreField: r1->field_27 = r0
    //     0xbebabc: stur            w0, [x1, #0x27]
    // 0xbebac0: d0 = 1.000000
    //     0xbebac0: fmov            d0, #1.00000000
    // 0xbebac4: StoreField: r1->field_7 = d0
    //     0xbebac4: stur            d0, [x1, #7]
    // 0xbebac8: ldur            x2, [fp, #-0x18]
    // 0xbebacc: ubfx            x2, x2, #0, #0x20
    // 0xbebad0: and             w3, w2, #0xff
    // 0xbebad4: ubfx            x3, x3, #0, #0x20
    // 0xbebad8: scvtf           d0, x3
    // 0xbebadc: d1 = 255.000000
    //     0xbebadc: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbebae0: fdiv            d2, d0, d1
    // 0xbebae4: StoreField: r1->field_f = d2
    //     0xbebae4: stur            d2, [x1, #0xf]
    // 0xbebae8: ldur            x2, [fp, #-0x10]
    // 0xbebaec: ubfx            x2, x2, #0, #0x20
    // 0xbebaf0: and             w3, w2, #0xff
    // 0xbebaf4: ubfx            x3, x3, #0, #0x20
    // 0xbebaf8: scvtf           d0, x3
    // 0xbebafc: fdiv            d2, d0, d1
    // 0xbebb00: ArrayStore: r1[0] = d2  ; List_8
    //     0xbebb00: stur            d2, [x1, #0x17]
    // 0xbebb04: ldur            x2, [fp, #-8]
    // 0xbebb08: ubfx            x2, x2, #0, #0x20
    // 0xbebb0c: and             w3, w2, #0xff
    // 0xbebb10: ubfx            x3, x3, #0, #0x20
    // 0xbebb14: scvtf           d0, x3
    // 0xbebb18: fdiv            d2, d0, d1
    // 0xbebb1c: StoreField: r1->field_1f = d2
    //     0xbebb1c: stur            d2, [x1, #0x1f]
    // 0xbebb20: ldur            x2, [fp, #-0x20]
    // 0xbebb24: cmp             w2, NULL
    // 0xbebb28: b.ne            #0xbebb34
    // 0xbebb2c: r3 = Null
    //     0xbebb2c: mov             x3, NULL
    // 0xbebb30: b               #0xbebb58
    // 0xbebb34: LoadField: r3 = r2->field_13
    //     0xbebb34: ldur            w3, [x2, #0x13]
    // 0xbebb38: DecompressPointer r3
    //     0xbebb38: add             x3, x3, HEAP, lsl #32
    // 0xbebb3c: cmp             w3, NULL
    // 0xbebb40: b.ne            #0xbebb4c
    // 0xbebb44: r3 = Null
    //     0xbebb44: mov             x3, NULL
    // 0xbebb48: b               #0xbebb58
    // 0xbebb4c: LoadField: r4 = r3->field_7
    //     0xbebb4c: ldur            w4, [x3, #7]
    // 0xbebb50: DecompressPointer r4
    //     0xbebb50: add             x4, x4, HEAP, lsl #32
    // 0xbebb54: mov             x3, x4
    // 0xbebb58: cmp             w3, NULL
    // 0xbebb5c: b.ne            #0xbebb68
    // 0xbebb60: r3 = 0
    //     0xbebb60: movz            x3, #0
    // 0xbebb64: b               #0xbebb78
    // 0xbebb68: r4 = LoadInt32Instr(r3)
    //     0xbebb68: sbfx            x4, x3, #1, #0x1f
    //     0xbebb6c: tbz             w3, #0, #0xbebb74
    //     0xbebb70: ldur            x4, [x3, #7]
    // 0xbebb74: mov             x3, x4
    // 0xbebb78: stur            x3, [fp, #-0x18]
    // 0xbebb7c: cmp             w2, NULL
    // 0xbebb80: b.ne            #0xbebb8c
    // 0xbebb84: r4 = Null
    //     0xbebb84: mov             x4, NULL
    // 0xbebb88: b               #0xbebbb0
    // 0xbebb8c: LoadField: r4 = r2->field_13
    //     0xbebb8c: ldur            w4, [x2, #0x13]
    // 0xbebb90: DecompressPointer r4
    //     0xbebb90: add             x4, x4, HEAP, lsl #32
    // 0xbebb94: cmp             w4, NULL
    // 0xbebb98: b.ne            #0xbebba4
    // 0xbebb9c: r4 = Null
    //     0xbebb9c: mov             x4, NULL
    // 0xbebba0: b               #0xbebbb0
    // 0xbebba4: LoadField: r5 = r4->field_b
    //     0xbebba4: ldur            w5, [x4, #0xb]
    // 0xbebba8: DecompressPointer r5
    //     0xbebba8: add             x5, x5, HEAP, lsl #32
    // 0xbebbac: mov             x4, x5
    // 0xbebbb0: cmp             w4, NULL
    // 0xbebbb4: b.ne            #0xbebbc0
    // 0xbebbb8: r4 = 0
    //     0xbebbb8: movz            x4, #0
    // 0xbebbbc: b               #0xbebbd0
    // 0xbebbc0: r5 = LoadInt32Instr(r4)
    //     0xbebbc0: sbfx            x5, x4, #1, #0x1f
    //     0xbebbc4: tbz             w4, #0, #0xbebbcc
    //     0xbebbc8: ldur            x5, [x4, #7]
    // 0xbebbcc: mov             x4, x5
    // 0xbebbd0: stur            x4, [fp, #-0x10]
    // 0xbebbd4: cmp             w2, NULL
    // 0xbebbd8: b.ne            #0xbebbe4
    // 0xbebbdc: r2 = Null
    //     0xbebbdc: mov             x2, NULL
    // 0xbebbe0: b               #0xbebc04
    // 0xbebbe4: LoadField: r5 = r2->field_13
    //     0xbebbe4: ldur            w5, [x2, #0x13]
    // 0xbebbe8: DecompressPointer r5
    //     0xbebbe8: add             x5, x5, HEAP, lsl #32
    // 0xbebbec: cmp             w5, NULL
    // 0xbebbf0: b.ne            #0xbebbfc
    // 0xbebbf4: r2 = Null
    //     0xbebbf4: mov             x2, NULL
    // 0xbebbf8: b               #0xbebc04
    // 0xbebbfc: LoadField: r2 = r5->field_f
    //     0xbebbfc: ldur            w2, [x5, #0xf]
    // 0xbebc00: DecompressPointer r2
    //     0xbebc00: add             x2, x2, HEAP, lsl #32
    // 0xbebc04: cmp             w2, NULL
    // 0xbebc08: b.ne            #0xbebc14
    // 0xbebc0c: r5 = 0
    //     0xbebc0c: movz            x5, #0
    // 0xbebc10: b               #0xbebc20
    // 0xbebc14: r5 = LoadInt32Instr(r2)
    //     0xbebc14: sbfx            x5, x2, #1, #0x1f
    //     0xbebc18: tbz             w2, #0, #0xbebc20
    //     0xbebc1c: ldur            x5, [x2, #7]
    // 0xbebc20: ldur            x2, [fp, #-0x30]
    // 0xbebc24: stur            x5, [fp, #-8]
    // 0xbebc28: r0 = Color()
    //     0xbebc28: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbebc2c: mov             x3, x0
    // 0xbebc30: r0 = Instance_ColorSpace
    //     0xbebc30: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbebc34: stur            x3, [fp, #-0x20]
    // 0xbebc38: StoreField: r3->field_27 = r0
    //     0xbebc38: stur            w0, [x3, #0x27]
    // 0xbebc3c: d0 = 0.700000
    //     0xbebc3c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbebc40: ldr             d0, [x17, #0xf48]
    // 0xbebc44: StoreField: r3->field_7 = d0
    //     0xbebc44: stur            d0, [x3, #7]
    // 0xbebc48: ldur            x0, [fp, #-0x18]
    // 0xbebc4c: ubfx            x0, x0, #0, #0x20
    // 0xbebc50: and             w1, w0, #0xff
    // 0xbebc54: ubfx            x1, x1, #0, #0x20
    // 0xbebc58: scvtf           d0, x1
    // 0xbebc5c: d1 = 255.000000
    //     0xbebc5c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbebc60: fdiv            d2, d0, d1
    // 0xbebc64: StoreField: r3->field_f = d2
    //     0xbebc64: stur            d2, [x3, #0xf]
    // 0xbebc68: ldur            x0, [fp, #-0x10]
    // 0xbebc6c: ubfx            x0, x0, #0, #0x20
    // 0xbebc70: and             w1, w0, #0xff
    // 0xbebc74: ubfx            x1, x1, #0, #0x20
    // 0xbebc78: scvtf           d0, x1
    // 0xbebc7c: fdiv            d2, d0, d1
    // 0xbebc80: ArrayStore: r3[0] = d2  ; List_8
    //     0xbebc80: stur            d2, [x3, #0x17]
    // 0xbebc84: ldur            x0, [fp, #-8]
    // 0xbebc88: ubfx            x0, x0, #0, #0x20
    // 0xbebc8c: and             w1, w0, #0xff
    // 0xbebc90: ubfx            x1, x1, #0, #0x20
    // 0xbebc94: scvtf           d0, x1
    // 0xbebc98: fdiv            d2, d0, d1
    // 0xbebc9c: StoreField: r3->field_1f = d2
    //     0xbebc9c: stur            d2, [x3, #0x1f]
    // 0xbebca0: r1 = Null
    //     0xbebca0: mov             x1, NULL
    // 0xbebca4: r2 = 4
    //     0xbebca4: movz            x2, #0x4
    // 0xbebca8: r0 = AllocateArray()
    //     0xbebca8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbebcac: mov             x2, x0
    // 0xbebcb0: ldur            x0, [fp, #-0x38]
    // 0xbebcb4: stur            x2, [fp, #-0x40]
    // 0xbebcb8: StoreField: r2->field_f = r0
    //     0xbebcb8: stur            w0, [x2, #0xf]
    // 0xbebcbc: ldur            x0, [fp, #-0x20]
    // 0xbebcc0: StoreField: r2->field_13 = r0
    //     0xbebcc0: stur            w0, [x2, #0x13]
    // 0xbebcc4: r1 = <Color>
    //     0xbebcc4: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbebcc8: ldr             x1, [x1, #0xf80]
    // 0xbebccc: r0 = AllocateGrowableArray()
    //     0xbebccc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbebcd0: mov             x1, x0
    // 0xbebcd4: ldur            x0, [fp, #-0x40]
    // 0xbebcd8: stur            x1, [fp, #-0x20]
    // 0xbebcdc: StoreField: r1->field_f = r0
    //     0xbebcdc: stur            w0, [x1, #0xf]
    // 0xbebce0: r2 = 4
    //     0xbebce0: movz            x2, #0x4
    // 0xbebce4: StoreField: r1->field_b = r2
    //     0xbebce4: stur            w2, [x1, #0xb]
    // 0xbebce8: r0 = LinearGradient()
    //     0xbebce8: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xbebcec: mov             x1, x0
    // 0xbebcf0: r0 = Instance_Alignment
    //     0xbebcf0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xbebcf4: ldr             x0, [x0, #0xce0]
    // 0xbebcf8: stur            x1, [fp, #-0x38]
    // 0xbebcfc: StoreField: r1->field_13 = r0
    //     0xbebcfc: stur            w0, [x1, #0x13]
    // 0xbebd00: r0 = Instance_Alignment
    //     0xbebd00: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xbebd04: ldr             x0, [x0, #0xce8]
    // 0xbebd08: ArrayStore: r1[0] = r0  ; List_4
    //     0xbebd08: stur            w0, [x1, #0x17]
    // 0xbebd0c: r0 = Instance_TileMode
    //     0xbebd0c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xbebd10: ldr             x0, [x0, #0xcf0]
    // 0xbebd14: StoreField: r1->field_1b = r0
    //     0xbebd14: stur            w0, [x1, #0x1b]
    // 0xbebd18: ldur            x0, [fp, #-0x20]
    // 0xbebd1c: StoreField: r1->field_7 = r0
    //     0xbebd1c: stur            w0, [x1, #7]
    // 0xbebd20: r0 = BoxDecoration()
    //     0xbebd20: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbebd24: mov             x2, x0
    // 0xbebd28: ldur            x0, [fp, #-0x38]
    // 0xbebd2c: stur            x2, [fp, #-0x40]
    // 0xbebd30: StoreField: r2->field_1b = r0
    //     0xbebd30: stur            w0, [x2, #0x1b]
    // 0xbebd34: r0 = Instance_BoxShape
    //     0xbebd34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbebd38: ldr             x0, [x0, #0x80]
    // 0xbebd3c: StoreField: r2->field_23 = r0
    //     0xbebd3c: stur            w0, [x2, #0x23]
    // 0xbebd40: ldur            x0, [fp, #-0x30]
    // 0xbebd44: LoadField: r1 = r0->field_7f
    //     0xbebd44: ldur            w1, [x0, #0x7f]
    // 0xbebd48: DecompressPointer r1
    //     0xbebd48: add             x1, x1, HEAP, lsl #32
    // 0xbebd4c: cmp             w1, NULL
    // 0xbebd50: b.ne            #0xbebd5c
    // 0xbebd54: r0 = ""
    //     0xbebd54: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbebd58: b               #0xbebd60
    // 0xbebd5c: mov             x0, x1
    // 0xbebd60: ldur            x3, [fp, #-0x28]
    // 0xbebd64: stur            x0, [fp, #-0x20]
    // 0xbebd68: LoadField: r1 = r3->field_f
    //     0xbebd68: ldur            w1, [x3, #0xf]
    // 0xbebd6c: DecompressPointer r1
    //     0xbebd6c: add             x1, x1, HEAP, lsl #32
    // 0xbebd70: cmp             w1, NULL
    // 0xbebd74: b.eq            #0xbec11c
    // 0xbebd78: r0 = of()
    //     0xbebd78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbebd7c: LoadField: r1 = r0->field_87
    //     0xbebd7c: ldur            w1, [x0, #0x87]
    // 0xbebd80: DecompressPointer r1
    //     0xbebd80: add             x1, x1, HEAP, lsl #32
    // 0xbebd84: LoadField: r0 = r1->field_7
    //     0xbebd84: ldur            w0, [x1, #7]
    // 0xbebd88: DecompressPointer r0
    //     0xbebd88: add             x0, x0, HEAP, lsl #32
    // 0xbebd8c: r16 = 12.000000
    //     0xbebd8c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbebd90: ldr             x16, [x16, #0x9e8]
    // 0xbebd94: r30 = Instance_Color
    //     0xbebd94: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbebd98: stp             lr, x16, [SP]
    // 0xbebd9c: mov             x1, x0
    // 0xbebda0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbebda0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbebda4: ldr             x4, [x4, #0xaa0]
    // 0xbebda8: r0 = copyWith()
    //     0xbebda8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbebdac: stur            x0, [fp, #-0x38]
    // 0xbebdb0: r0 = Text()
    //     0xbebdb0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbebdb4: mov             x1, x0
    // 0xbebdb8: ldur            x0, [fp, #-0x20]
    // 0xbebdbc: stur            x1, [fp, #-0x48]
    // 0xbebdc0: StoreField: r1->field_b = r0
    //     0xbebdc0: stur            w0, [x1, #0xb]
    // 0xbebdc4: ldur            x0, [fp, #-0x38]
    // 0xbebdc8: StoreField: r1->field_13 = r0
    //     0xbebdc8: stur            w0, [x1, #0x13]
    // 0xbebdcc: r0 = Padding()
    //     0xbebdcc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbebdd0: mov             x1, x0
    // 0xbebdd4: r0 = Instance_EdgeInsets
    //     0xbebdd4: add             x0, PP, #0x46, lsl #12  ; [pp+0x46f10] Obj!EdgeInsets@d58191
    //     0xbebdd8: ldr             x0, [x0, #0xf10]
    // 0xbebddc: stur            x1, [fp, #-0x20]
    // 0xbebde0: StoreField: r1->field_f = r0
    //     0xbebde0: stur            w0, [x1, #0xf]
    // 0xbebde4: ldur            x0, [fp, #-0x48]
    // 0xbebde8: StoreField: r1->field_b = r0
    //     0xbebde8: stur            w0, [x1, #0xb]
    // 0xbebdec: r0 = Container()
    //     0xbebdec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbebdf0: stur            x0, [fp, #-0x38]
    // 0xbebdf4: r16 = 20.000000
    //     0xbebdf4: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xbebdf8: ldr             x16, [x16, #0xac8]
    // 0xbebdfc: ldur            lr, [fp, #-0x40]
    // 0xbebe00: stp             lr, x16, [SP, #8]
    // 0xbebe04: ldur            x16, [fp, #-0x20]
    // 0xbebe08: str             x16, [SP]
    // 0xbebe0c: mov             x1, x0
    // 0xbebe10: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xbebe10: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xbebe14: ldr             x4, [x4, #0xc78]
    // 0xbebe18: r0 = Container()
    //     0xbebe18: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbebe1c: r0 = Align()
    //     0xbebe1c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbebe20: r4 = Instance_Alignment
    //     0xbebe20: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbebe24: ldr             x4, [x4, #0xfa0]
    // 0xbebe28: StoreField: r0->field_f = r4
    //     0xbebe28: stur            w4, [x0, #0xf]
    // 0xbebe2c: ldur            x1, [fp, #-0x38]
    // 0xbebe30: StoreField: r0->field_b = r1
    //     0xbebe30: stur            w1, [x0, #0xb]
    // 0xbebe34: b               #0xbec104
    // 0xbebe38: mov             x3, x1
    // 0xbebe3c: mov             x0, x2
    // 0xbebe40: r4 = Instance_Alignment
    //     0xbebe40: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbebe44: ldr             x4, [x4, #0xfa0]
    // 0xbebe48: r2 = 4
    //     0xbebe48: movz            x2, #0x4
    // 0xbebe4c: d0 = 0.700000
    //     0xbebe4c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbebe50: ldr             d0, [x17, #0xf48]
    // 0xbebe54: LoadField: r1 = r3->field_f
    //     0xbebe54: ldur            w1, [x3, #0xf]
    // 0xbebe58: DecompressPointer r1
    //     0xbebe58: add             x1, x1, HEAP, lsl #32
    // 0xbebe5c: cmp             w1, NULL
    // 0xbebe60: b.eq            #0xbec120
    // 0xbebe64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbebe64: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbebe68: r0 = _of()
    //     0xbebe68: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbebe6c: LoadField: r1 = r0->field_7
    //     0xbebe6c: ldur            w1, [x0, #7]
    // 0xbebe70: DecompressPointer r1
    //     0xbebe70: add             x1, x1, HEAP, lsl #32
    // 0xbebe74: LoadField: d0 = r1->field_7
    //     0xbebe74: ldur            d0, [x1, #7]
    // 0xbebe78: d1 = 0.370000
    //     0xbebe78: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xbebe7c: ldr             d1, [x17, #0xe40]
    // 0xbebe80: fmul            d2, d0, d1
    // 0xbebe84: ldur            x0, [fp, #-0x28]
    // 0xbebe88: stur            d2, [fp, #-0x50]
    // 0xbebe8c: LoadField: r1 = r0->field_f
    //     0xbebe8c: ldur            w1, [x0, #0xf]
    // 0xbebe90: DecompressPointer r1
    //     0xbebe90: add             x1, x1, HEAP, lsl #32
    // 0xbebe94: cmp             w1, NULL
    // 0xbebe98: b.eq            #0xbec124
    // 0xbebe9c: r0 = of()
    //     0xbebe9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbebea0: LoadField: r1 = r0->field_5b
    //     0xbebea0: ldur            w1, [x0, #0x5b]
    // 0xbebea4: DecompressPointer r1
    //     0xbebea4: add             x1, x1, HEAP, lsl #32
    // 0xbebea8: stur            x1, [fp, #-0x20]
    // 0xbebeac: r0 = ColorFilter()
    //     0xbebeac: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbebeb0: mov             x1, x0
    // 0xbebeb4: ldur            x0, [fp, #-0x20]
    // 0xbebeb8: stur            x1, [fp, #-0x38]
    // 0xbebebc: StoreField: r1->field_7 = r0
    //     0xbebebc: stur            w0, [x1, #7]
    // 0xbebec0: r0 = Instance_BlendMode
    //     0xbebec0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbebec4: ldr             x0, [x0, #0xb30]
    // 0xbebec8: StoreField: r1->field_b = r0
    //     0xbebec8: stur            w0, [x1, #0xb]
    // 0xbebecc: r0 = 1
    //     0xbebecc: movz            x0, #0x1
    // 0xbebed0: StoreField: r1->field_13 = r0
    //     0xbebed0: stur            x0, [x1, #0x13]
    // 0xbebed4: r0 = SvgPicture()
    //     0xbebed4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbebed8: stur            x0, [fp, #-0x20]
    // 0xbebedc: ldur            x16, [fp, #-0x38]
    // 0xbebee0: str             x16, [SP]
    // 0xbebee4: mov             x1, x0
    // 0xbebee8: r2 = "assets/images/bumper_coupon.svg"
    //     0xbebee8: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xbebeec: ldr             x2, [x2, #0xe48]
    // 0xbebef0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbebef0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbebef4: ldr             x4, [x4, #0xa38]
    // 0xbebef8: r0 = SvgPicture.asset()
    //     0xbebef8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbebefc: ldur            x0, [fp, #-0x30]
    // 0xbebf00: LoadField: r1 = r0->field_7f
    //     0xbebf00: ldur            w1, [x0, #0x7f]
    // 0xbebf04: DecompressPointer r1
    //     0xbebf04: add             x1, x1, HEAP, lsl #32
    // 0xbebf08: cmp             w1, NULL
    // 0xbebf0c: b.ne            #0xbebf18
    // 0xbebf10: r2 = ""
    //     0xbebf10: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbebf14: b               #0xbebf1c
    // 0xbebf18: mov             x2, x1
    // 0xbebf1c: ldur            x1, [fp, #-0x28]
    // 0xbebf20: ldur            d0, [fp, #-0x50]
    // 0xbebf24: ldur            x0, [fp, #-0x20]
    // 0xbebf28: stur            x2, [fp, #-0x30]
    // 0xbebf2c: LoadField: r3 = r1->field_f
    //     0xbebf2c: ldur            w3, [x1, #0xf]
    // 0xbebf30: DecompressPointer r3
    //     0xbebf30: add             x3, x3, HEAP, lsl #32
    // 0xbebf34: cmp             w3, NULL
    // 0xbebf38: b.eq            #0xbec128
    // 0xbebf3c: mov             x1, x3
    // 0xbebf40: r0 = of()
    //     0xbebf40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbebf44: LoadField: r1 = r0->field_87
    //     0xbebf44: ldur            w1, [x0, #0x87]
    // 0xbebf48: DecompressPointer r1
    //     0xbebf48: add             x1, x1, HEAP, lsl #32
    // 0xbebf4c: LoadField: r0 = r1->field_2b
    //     0xbebf4c: ldur            w0, [x1, #0x2b]
    // 0xbebf50: DecompressPointer r0
    //     0xbebf50: add             x0, x0, HEAP, lsl #32
    // 0xbebf54: stur            x0, [fp, #-0x28]
    // 0xbebf58: r1 = Instance_Color
    //     0xbebf58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbebf5c: d0 = 0.700000
    //     0xbebf5c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbebf60: ldr             d0, [x17, #0xf48]
    // 0xbebf64: r0 = withOpacity()
    //     0xbebf64: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbebf68: r16 = 12.000000
    //     0xbebf68: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbebf6c: ldr             x16, [x16, #0x9e8]
    // 0xbebf70: stp             x0, x16, [SP]
    // 0xbebf74: ldur            x1, [fp, #-0x28]
    // 0xbebf78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbebf78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbebf7c: ldr             x4, [x4, #0xaa0]
    // 0xbebf80: r0 = copyWith()
    //     0xbebf80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbebf84: stur            x0, [fp, #-0x28]
    // 0xbebf88: r0 = Text()
    //     0xbebf88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbebf8c: mov             x1, x0
    // 0xbebf90: ldur            x0, [fp, #-0x30]
    // 0xbebf94: stur            x1, [fp, #-0x38]
    // 0xbebf98: StoreField: r1->field_b = r0
    //     0xbebf98: stur            w0, [x1, #0xb]
    // 0xbebf9c: ldur            x0, [fp, #-0x28]
    // 0xbebfa0: StoreField: r1->field_13 = r0
    //     0xbebfa0: stur            w0, [x1, #0x13]
    // 0xbebfa4: r0 = Padding()
    //     0xbebfa4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbebfa8: mov             x3, x0
    // 0xbebfac: r0 = Instance_EdgeInsets
    //     0xbebfac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xbebfb0: ldr             x0, [x0, #0xe60]
    // 0xbebfb4: stur            x3, [fp, #-0x28]
    // 0xbebfb8: StoreField: r3->field_f = r0
    //     0xbebfb8: stur            w0, [x3, #0xf]
    // 0xbebfbc: ldur            x0, [fp, #-0x38]
    // 0xbebfc0: StoreField: r3->field_b = r0
    //     0xbebfc0: stur            w0, [x3, #0xb]
    // 0xbebfc4: r1 = Null
    //     0xbebfc4: mov             x1, NULL
    // 0xbebfc8: r2 = 4
    //     0xbebfc8: movz            x2, #0x4
    // 0xbebfcc: r0 = AllocateArray()
    //     0xbebfcc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbebfd0: mov             x2, x0
    // 0xbebfd4: ldur            x0, [fp, #-0x20]
    // 0xbebfd8: stur            x2, [fp, #-0x30]
    // 0xbebfdc: StoreField: r2->field_f = r0
    //     0xbebfdc: stur            w0, [x2, #0xf]
    // 0xbebfe0: ldur            x0, [fp, #-0x28]
    // 0xbebfe4: StoreField: r2->field_13 = r0
    //     0xbebfe4: stur            w0, [x2, #0x13]
    // 0xbebfe8: r1 = <Widget>
    //     0xbebfe8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbebfec: r0 = AllocateGrowableArray()
    //     0xbebfec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbebff0: mov             x1, x0
    // 0xbebff4: ldur            x0, [fp, #-0x30]
    // 0xbebff8: stur            x1, [fp, #-0x20]
    // 0xbebffc: StoreField: r1->field_f = r0
    //     0xbebffc: stur            w0, [x1, #0xf]
    // 0xbec000: r0 = 4
    //     0xbec000: movz            x0, #0x4
    // 0xbec004: StoreField: r1->field_b = r0
    //     0xbec004: stur            w0, [x1, #0xb]
    // 0xbec008: r0 = Row()
    //     0xbec008: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbec00c: mov             x1, x0
    // 0xbec010: r0 = Instance_Axis
    //     0xbec010: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbec014: stur            x1, [fp, #-0x28]
    // 0xbec018: StoreField: r1->field_f = r0
    //     0xbec018: stur            w0, [x1, #0xf]
    // 0xbec01c: r0 = Instance_MainAxisAlignment
    //     0xbec01c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbec020: ldr             x0, [x0, #0xab0]
    // 0xbec024: StoreField: r1->field_13 = r0
    //     0xbec024: stur            w0, [x1, #0x13]
    // 0xbec028: r0 = Instance_MainAxisSize
    //     0xbec028: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbec02c: ldr             x0, [x0, #0xa10]
    // 0xbec030: ArrayStore: r1[0] = r0  ; List_4
    //     0xbec030: stur            w0, [x1, #0x17]
    // 0xbec034: r0 = Instance_CrossAxisAlignment
    //     0xbec034: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbec038: ldr             x0, [x0, #0xa18]
    // 0xbec03c: StoreField: r1->field_1b = r0
    //     0xbec03c: stur            w0, [x1, #0x1b]
    // 0xbec040: r0 = Instance_VerticalDirection
    //     0xbec040: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbec044: ldr             x0, [x0, #0xa20]
    // 0xbec048: StoreField: r1->field_23 = r0
    //     0xbec048: stur            w0, [x1, #0x23]
    // 0xbec04c: r0 = Instance_Clip
    //     0xbec04c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbec050: ldr             x0, [x0, #0x38]
    // 0xbec054: StoreField: r1->field_2b = r0
    //     0xbec054: stur            w0, [x1, #0x2b]
    // 0xbec058: StoreField: r1->field_2f = rZR
    //     0xbec058: stur            xzr, [x1, #0x2f]
    // 0xbec05c: ldur            x0, [fp, #-0x20]
    // 0xbec060: StoreField: r1->field_b = r0
    //     0xbec060: stur            w0, [x1, #0xb]
    // 0xbec064: ldur            d0, [fp, #-0x50]
    // 0xbec068: r0 = inline_Allocate_Double()
    //     0xbec068: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbec06c: add             x0, x0, #0x10
    //     0xbec070: cmp             x2, x0
    //     0xbec074: b.ls            #0xbec12c
    //     0xbec078: str             x0, [THR, #0x50]  ; THR::top
    //     0xbec07c: sub             x0, x0, #0xf
    //     0xbec080: movz            x2, #0xe15c
    //     0xbec084: movk            x2, #0x3, lsl #16
    //     0xbec088: stur            x2, [x0, #-1]
    // 0xbec08c: StoreField: r0->field_7 = d0
    //     0xbec08c: stur            d0, [x0, #7]
    // 0xbec090: stur            x0, [fp, #-0x20]
    // 0xbec094: r0 = Container()
    //     0xbec094: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbec098: stur            x0, [fp, #-0x30]
    // 0xbec09c: r16 = 20.000000
    //     0xbec09c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xbec0a0: ldr             x16, [x16, #0xac8]
    // 0xbec0a4: ldur            lr, [fp, #-0x20]
    // 0xbec0a8: stp             lr, x16, [SP, #0x10]
    // 0xbec0ac: r16 = Instance_BoxDecoration
    //     0xbec0ac: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xbec0b0: ldr             x16, [x16, #0x5a8]
    // 0xbec0b4: ldur            lr, [fp, #-0x28]
    // 0xbec0b8: stp             lr, x16, [SP]
    // 0xbec0bc: mov             x1, x0
    // 0xbec0c0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xbec0c0: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xbec0c4: ldr             x4, [x4, #0x8c0]
    // 0xbec0c8: r0 = Container()
    //     0xbec0c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbec0cc: r0 = Padding()
    //     0xbec0cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbec0d0: mov             x1, x0
    // 0xbec0d4: r0 = Instance_EdgeInsets
    //     0xbec0d4: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xbec0d8: ldr             x0, [x0, #0xe50]
    // 0xbec0dc: stur            x1, [fp, #-0x20]
    // 0xbec0e0: StoreField: r1->field_f = r0
    //     0xbec0e0: stur            w0, [x1, #0xf]
    // 0xbec0e4: ldur            x0, [fp, #-0x30]
    // 0xbec0e8: StoreField: r1->field_b = r0
    //     0xbec0e8: stur            w0, [x1, #0xb]
    // 0xbec0ec: r0 = Align()
    //     0xbec0ec: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbec0f0: r1 = Instance_Alignment
    //     0xbec0f0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbec0f4: ldr             x1, [x1, #0xfa0]
    // 0xbec0f8: StoreField: r0->field_f = r1
    //     0xbec0f8: stur            w1, [x0, #0xf]
    // 0xbec0fc: ldur            x1, [fp, #-0x20]
    // 0xbec100: StoreField: r0->field_b = r1
    //     0xbec100: stur            w1, [x0, #0xb]
    // 0xbec104: LeaveFrame
    //     0xbec104: mov             SP, fp
    //     0xbec108: ldp             fp, lr, [SP], #0x10
    // 0xbec10c: ret
    //     0xbec10c: ret             
    // 0xbec110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbec110: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbec114: b               #0xbeb954
    // 0xbec118: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbec118: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbec11c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbec11c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbec120: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbec120: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbec124: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbec124: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbec128: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbec128: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbec12c: SaveReg d0
    //     0xbec12c: str             q0, [SP, #-0x10]!
    // 0xbec130: SaveReg r1
    //     0xbec130: str             x1, [SP, #-8]!
    // 0xbec134: r0 = AllocateDouble()
    //     0xbec134: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbec138: RestoreReg r1
    //     0xbec138: ldr             x1, [SP], #8
    // 0xbec13c: RestoreReg d0
    //     0xbec13c: ldr             q0, [SP], #0x10
    // 0xbec140: b               #0xbec08c
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbec144, size: 0x84
    // 0xbec144: EnterFrame
    //     0xbec144: stp             fp, lr, [SP, #-0x10]!
    //     0xbec148: mov             fp, SP
    // 0xbec14c: AllocStack(0x10)
    //     0xbec14c: sub             SP, SP, #0x10
    // 0xbec150: SetupParameters()
    //     0xbec150: ldr             x0, [fp, #0x18]
    //     0xbec154: ldur            w1, [x0, #0x17]
    //     0xbec158: add             x1, x1, HEAP, lsl #32
    //     0xbec15c: stur            x1, [fp, #-8]
    // 0xbec160: CheckStackOverflow
    //     0xbec160: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbec164: cmp             SP, x16
    //     0xbec168: b.ls            #0xbec1c0
    // 0xbec16c: r1 = 1
    //     0xbec16c: movz            x1, #0x1
    // 0xbec170: r0 = AllocateContext()
    //     0xbec170: bl              #0x16f6108  ; AllocateContextStub
    // 0xbec174: mov             x1, x0
    // 0xbec178: ldur            x0, [fp, #-8]
    // 0xbec17c: StoreField: r1->field_b = r0
    //     0xbec17c: stur            w0, [x1, #0xb]
    // 0xbec180: ldr             x2, [fp, #0x10]
    // 0xbec184: StoreField: r1->field_f = r2
    //     0xbec184: stur            w2, [x1, #0xf]
    // 0xbec188: LoadField: r3 = r0->field_f
    //     0xbec188: ldur            w3, [x0, #0xf]
    // 0xbec18c: DecompressPointer r3
    //     0xbec18c: add             x3, x3, HEAP, lsl #32
    // 0xbec190: mov             x2, x1
    // 0xbec194: stur            x3, [fp, #-0x10]
    // 0xbec198: r1 = Function '<anonymous closure>':.
    //     0xbec198: add             x1, PP, #0x53, lsl #12  ; [pp+0x53728] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbec19c: ldr             x1, [x1, #0x728]
    // 0xbec1a0: r0 = AllocateClosure()
    //     0xbec1a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbec1a4: ldur            x1, [fp, #-0x10]
    // 0xbec1a8: mov             x2, x0
    // 0xbec1ac: r0 = setState()
    //     0xbec1ac: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbec1b0: r0 = Null
    //     0xbec1b0: mov             x0, NULL
    // 0xbec1b4: LeaveFrame
    //     0xbec1b4: mov             SP, fp
    //     0xbec1b8: ldp             fp, lr, [SP], #0x10
    // 0xbec1bc: ret
    //     0xbec1bc: ret             
    // 0xbec1c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbec1c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbec1c4: b               #0xbec16c
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xbec1c8, size: 0x84
    // 0xbec1c8: EnterFrame
    //     0xbec1c8: stp             fp, lr, [SP, #-0x10]!
    //     0xbec1cc: mov             fp, SP
    // 0xbec1d0: AllocStack(0x10)
    //     0xbec1d0: sub             SP, SP, #0x10
    // 0xbec1d4: SetupParameters()
    //     0xbec1d4: ldr             x0, [fp, #0x18]
    //     0xbec1d8: ldur            w1, [x0, #0x17]
    //     0xbec1dc: add             x1, x1, HEAP, lsl #32
    //     0xbec1e0: stur            x1, [fp, #-8]
    // 0xbec1e4: CheckStackOverflow
    //     0xbec1e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbec1e8: cmp             SP, x16
    //     0xbec1ec: b.ls            #0xbec244
    // 0xbec1f0: r1 = 1
    //     0xbec1f0: movz            x1, #0x1
    // 0xbec1f4: r0 = AllocateContext()
    //     0xbec1f4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbec1f8: mov             x1, x0
    // 0xbec1fc: ldur            x0, [fp, #-8]
    // 0xbec200: StoreField: r1->field_b = r0
    //     0xbec200: stur            w0, [x1, #0xb]
    // 0xbec204: ldr             x2, [fp, #0x10]
    // 0xbec208: StoreField: r1->field_f = r2
    //     0xbec208: stur            w2, [x1, #0xf]
    // 0xbec20c: LoadField: r3 = r0->field_f
    //     0xbec20c: ldur            w3, [x0, #0xf]
    // 0xbec210: DecompressPointer r3
    //     0xbec210: add             x3, x3, HEAP, lsl #32
    // 0xbec214: mov             x2, x1
    // 0xbec218: stur            x3, [fp, #-0x10]
    // 0xbec21c: r1 = Function '<anonymous closure>':.
    //     0xbec21c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53748] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xbec220: ldr             x1, [x1, #0x748]
    // 0xbec224: r0 = AllocateClosure()
    //     0xbec224: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbec228: ldur            x1, [fp, #-0x10]
    // 0xbec22c: mov             x2, x0
    // 0xbec230: r0 = setState()
    //     0xbec230: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbec234: r0 = Null
    //     0xbec234: mov             x0, NULL
    // 0xbec238: LeaveFrame
    //     0xbec238: mov             SP, fp
    //     0xbec23c: ldp             fp, lr, [SP], #0x10
    // 0xbec240: ret
    //     0xbec240: ret             
    // 0xbec244: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbec244: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbec248: b               #0xbec1f0
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc88198, size: 0x84
    // 0xc88198: EnterFrame
    //     0xc88198: stp             fp, lr, [SP, #-0x10]!
    //     0xc8819c: mov             fp, SP
    // 0xc881a0: AllocStack(0x8)
    //     0xc881a0: sub             SP, SP, #8
    // 0xc881a4: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc881a4: mov             x0, x1
    //     0xc881a8: stur            x1, [fp, #-8]
    // 0xc881ac: CheckStackOverflow
    //     0xc881ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc881b0: cmp             SP, x16
    //     0xc881b4: b.ls            #0xc881fc
    // 0xc881b8: LoadField: r1 = r0->field_13
    //     0xc881b8: ldur            w1, [x0, #0x13]
    // 0xc881bc: DecompressPointer r1
    //     0xc881bc: add             x1, x1, HEAP, lsl #32
    // 0xc881c0: r16 = Sentinel
    //     0xc881c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc881c4: cmp             w1, w16
    // 0xc881c8: b.eq            #0xc88204
    // 0xc881cc: r0 = dispose()
    //     0xc881cc: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc881d0: ldur            x0, [fp, #-8]
    // 0xc881d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc881d4: ldur            w1, [x0, #0x17]
    // 0xc881d8: DecompressPointer r1
    //     0xc881d8: add             x1, x1, HEAP, lsl #32
    // 0xc881dc: r16 = Sentinel
    //     0xc881dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc881e0: cmp             w1, w16
    // 0xc881e4: b.eq            #0xc88210
    // 0xc881e8: r0 = dispose()
    //     0xc881e8: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc881ec: r0 = Null
    //     0xc881ec: mov             x0, NULL
    // 0xc881f0: LeaveFrame
    //     0xc881f0: mov             SP, fp
    //     0xc881f4: ldp             fp, lr, [SP], #0x10
    // 0xc881f8: ret
    //     0xc881f8: ret             
    // 0xc881fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc881fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88200: b               #0xc881b8
    // 0xc88204: r9 = _pageController
    //     0xc88204: add             x9, PP, #0x53, lsl #12  ; [pp+0x53690] Field <_ProductGroupCarouselItemViewState@1706026639._pageController@1706026639>: late (offset: 0x14)
    //     0xc88208: ldr             x9, [x9, #0x690]
    // 0xc8820c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc8820c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc88210: r9 = _imagePageController
    //     0xc88210: add             x9, PP, #0x53, lsl #12  ; [pp+0x536e8] Field <_ProductGroupCarouselItemViewState@1706026639._imagePageController@1706026639>: late (offset: 0x18)
    //     0xc88214: ldr             x9, [x9, #0x6e8]
    // 0xc88218: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc88218: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3991, size: 0x4c, field offset: 0xc
//   const constructor, 
class ProductGroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80ad0, size: 0x34
    // 0xc80ad0: EnterFrame
    //     0xc80ad0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80ad4: mov             fp, SP
    // 0xc80ad8: mov             x0, x1
    // 0xc80adc: r1 = <ProductGroupCarouselItemView>
    //     0xc80adc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48410] TypeArguments: <ProductGroupCarouselItemView>
    //     0xc80ae0: ldr             x1, [x1, #0x410]
    // 0xc80ae4: r0 = _ProductGroupCarouselItemViewState()
    //     0xc80ae4: bl              #0xc80b04  ; Allocate_ProductGroupCarouselItemViewStateStub -> _ProductGroupCarouselItemViewState (size=0x24)
    // 0xc80ae8: r1 = Sentinel
    //     0xc80ae8: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80aec: StoreField: r0->field_13 = r1
    //     0xc80aec: stur            w1, [x0, #0x13]
    // 0xc80af0: ArrayStore: r0[0] = r1  ; List_4
    //     0xc80af0: stur            w1, [x0, #0x17]
    // 0xc80af4: StoreField: r0->field_1b = rZR
    //     0xc80af4: stur            xzr, [x0, #0x1b]
    // 0xc80af8: LeaveFrame
    //     0xc80af8: mov             SP, fp
    //     0xc80afc: ldp             fp, lr, [SP], #0x10
    // 0xc80b00: ret
    //     0xc80b00: ret             
  }
}
