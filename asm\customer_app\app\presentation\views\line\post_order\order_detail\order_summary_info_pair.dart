// lib: , url: package:customer_app/app/presentation/views/line/post_order/order_detail/order_summary_info_pair.dart

// class id: 1049545, size: 0x8
class :: {
}

// class id: 4484, size: 0x10, field offset: 0xc
//   const constructor, 
class OrderSummaryInfoPair extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x1297e28, size: 0x3fc
    // 0x1297e28: EnterFrame
    //     0x1297e28: stp             fp, lr, [SP, #-0x10]!
    //     0x1297e2c: mov             fp, SP
    // 0x1297e30: AllocStack(0x40)
    //     0x1297e30: sub             SP, SP, #0x40
    // 0x1297e34: SetupParameters(OrderSummaryInfoPair this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x1297e34: mov             x0, x1
    //     0x1297e38: mov             x1, x2
    //     0x1297e3c: stur            x2, [fp, #-0x10]
    // 0x1297e40: CheckStackOverflow
    //     0x1297e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1297e44: cmp             SP, x16
    //     0x1297e48: b.ls            #0x129821c
    // 0x1297e4c: LoadField: r2 = r0->field_b
    //     0x1297e4c: ldur            w2, [x0, #0xb]
    // 0x1297e50: DecompressPointer r2
    //     0x1297e50: add             x2, x2, HEAP, lsl #32
    // 0x1297e54: stur            x2, [fp, #-8]
    // 0x1297e58: LoadField: r0 = r2->field_7
    //     0x1297e58: ldur            w0, [x2, #7]
    // 0x1297e5c: DecompressPointer r0
    //     0x1297e5c: add             x0, x0, HEAP, lsl #32
    // 0x1297e60: r3 = LoadClassIdInstr(r0)
    //     0x1297e60: ldur            x3, [x0, #-1]
    //     0x1297e64: ubfx            x3, x3, #0xc, #0x14
    // 0x1297e68: r16 = "final"
    //     0x1297e68: add             x16, PP, #0x48, lsl #12  ; [pp+0x483d8] "final"
    //     0x1297e6c: ldr             x16, [x16, #0x3d8]
    // 0x1297e70: stp             x16, x0, [SP]
    // 0x1297e74: mov             x0, x3
    // 0x1297e78: mov             lr, x0
    // 0x1297e7c: ldr             lr, [x21, lr, lsl #3]
    // 0x1297e80: blr             lr
    // 0x1297e84: ldur            x1, [fp, #-0x10]
    // 0x1297e88: stur            x0, [fp, #-0x18]
    // 0x1297e8c: r0 = of()
    //     0x1297e8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1297e90: LoadField: r1 = r0->field_5b
    //     0x1297e90: ldur            w1, [x0, #0x5b]
    // 0x1297e94: DecompressPointer r1
    //     0x1297e94: add             x1, x1, HEAP, lsl #32
    // 0x1297e98: r0 = LoadClassIdInstr(r1)
    //     0x1297e98: ldur            x0, [x1, #-1]
    //     0x1297e9c: ubfx            x0, x0, #0xc, #0x14
    // 0x1297ea0: d0 = 0.100000
    //     0x1297ea0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x1297ea4: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1297ea4: sub             lr, x0, #0xffa
    //     0x1297ea8: ldr             lr, [x21, lr, lsl #3]
    //     0x1297eac: blr             lr
    // 0x1297eb0: stur            x0, [fp, #-0x20]
    // 0x1297eb4: r0 = Divider()
    //     0x1297eb4: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x1297eb8: mov             x1, x0
    // 0x1297ebc: ldur            x0, [fp, #-0x20]
    // 0x1297ec0: stur            x1, [fp, #-0x28]
    // 0x1297ec4: StoreField: r1->field_1f = r0
    //     0x1297ec4: stur            w0, [x1, #0x1f]
    // 0x1297ec8: r0 = Visibility()
    //     0x1297ec8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1297ecc: mov             x2, x0
    // 0x1297ed0: ldur            x0, [fp, #-0x28]
    // 0x1297ed4: stur            x2, [fp, #-0x20]
    // 0x1297ed8: StoreField: r2->field_b = r0
    //     0x1297ed8: stur            w0, [x2, #0xb]
    // 0x1297edc: r0 = Instance_SizedBox
    //     0x1297edc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1297ee0: StoreField: r2->field_f = r0
    //     0x1297ee0: stur            w0, [x2, #0xf]
    // 0x1297ee4: ldur            x0, [fp, #-0x18]
    // 0x1297ee8: StoreField: r2->field_13 = r0
    //     0x1297ee8: stur            w0, [x2, #0x13]
    // 0x1297eec: r0 = false
    //     0x1297eec: add             x0, NULL, #0x30  ; false
    // 0x1297ef0: ArrayStore: r2[0] = r0  ; List_4
    //     0x1297ef0: stur            w0, [x2, #0x17]
    // 0x1297ef4: StoreField: r2->field_1b = r0
    //     0x1297ef4: stur            w0, [x2, #0x1b]
    // 0x1297ef8: StoreField: r2->field_1f = r0
    //     0x1297ef8: stur            w0, [x2, #0x1f]
    // 0x1297efc: StoreField: r2->field_23 = r0
    //     0x1297efc: stur            w0, [x2, #0x23]
    // 0x1297f00: StoreField: r2->field_27 = r0
    //     0x1297f00: stur            w0, [x2, #0x27]
    // 0x1297f04: StoreField: r2->field_2b = r0
    //     0x1297f04: stur            w0, [x2, #0x2b]
    // 0x1297f08: ldur            x0, [fp, #-8]
    // 0x1297f0c: LoadField: r1 = r0->field_b
    //     0x1297f0c: ldur            w1, [x0, #0xb]
    // 0x1297f10: DecompressPointer r1
    //     0x1297f10: add             x1, x1, HEAP, lsl #32
    // 0x1297f14: cmp             w1, NULL
    // 0x1297f18: b.ne            #0x1297f24
    // 0x1297f1c: r3 = ""
    //     0x1297f1c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1297f20: b               #0x1297f28
    // 0x1297f24: mov             x3, x1
    // 0x1297f28: ldur            x1, [fp, #-0x10]
    // 0x1297f2c: stur            x3, [fp, #-0x18]
    // 0x1297f30: r0 = of()
    //     0x1297f30: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1297f34: LoadField: r1 = r0->field_87
    //     0x1297f34: ldur            w1, [x0, #0x87]
    // 0x1297f38: DecompressPointer r1
    //     0x1297f38: add             x1, x1, HEAP, lsl #32
    // 0x1297f3c: LoadField: r2 = r1->field_7
    //     0x1297f3c: ldur            w2, [x1, #7]
    // 0x1297f40: DecompressPointer r2
    //     0x1297f40: add             x2, x2, HEAP, lsl #32
    // 0x1297f44: ldur            x1, [fp, #-8]
    // 0x1297f48: stur            x2, [fp, #-0x28]
    // 0x1297f4c: LoadField: r0 = r1->field_7
    //     0x1297f4c: ldur            w0, [x1, #7]
    // 0x1297f50: DecompressPointer r0
    //     0x1297f50: add             x0, x0, HEAP, lsl #32
    // 0x1297f54: r3 = LoadClassIdInstr(r0)
    //     0x1297f54: ldur            x3, [x0, #-1]
    //     0x1297f58: ubfx            x3, x3, #0xc, #0x14
    // 0x1297f5c: r16 = "final"
    //     0x1297f5c: add             x16, PP, #0x48, lsl #12  ; [pp+0x483d8] "final"
    //     0x1297f60: ldr             x16, [x16, #0x3d8]
    // 0x1297f64: stp             x16, x0, [SP]
    // 0x1297f68: mov             x0, x3
    // 0x1297f6c: mov             lr, x0
    // 0x1297f70: ldr             lr, [x21, lr, lsl #3]
    // 0x1297f74: blr             lr
    // 0x1297f78: tbnz            w0, #4, #0x1297f84
    // 0x1297f7c: r1 = Instance_Color
    //     0x1297f7c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1297f80: b               #0x1297f94
    // 0x1297f84: r1 = Instance_Color
    //     0x1297f84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1297f88: d0 = 0.400000
    //     0x1297f88: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1297f8c: r0 = withOpacity()
    //     0x1297f8c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1297f90: mov             x1, x0
    // 0x1297f94: ldur            x0, [fp, #-8]
    // 0x1297f98: ldur            x2, [fp, #-0x18]
    // 0x1297f9c: r16 = 14.000000
    //     0x1297f9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1297fa0: ldr             x16, [x16, #0x1d8]
    // 0x1297fa4: stp             x1, x16, [SP]
    // 0x1297fa8: ldur            x1, [fp, #-0x28]
    // 0x1297fac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1297fac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1297fb0: ldr             x4, [x4, #0xaa0]
    // 0x1297fb4: r0 = copyWith()
    //     0x1297fb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1297fb8: stur            x0, [fp, #-0x28]
    // 0x1297fbc: r0 = Text()
    //     0x1297fbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1297fc0: mov             x2, x0
    // 0x1297fc4: ldur            x0, [fp, #-0x18]
    // 0x1297fc8: stur            x2, [fp, #-0x30]
    // 0x1297fcc: StoreField: r2->field_b = r0
    //     0x1297fcc: stur            w0, [x2, #0xb]
    // 0x1297fd0: ldur            x0, [fp, #-0x28]
    // 0x1297fd4: StoreField: r2->field_13 = r0
    //     0x1297fd4: stur            w0, [x2, #0x13]
    // 0x1297fd8: ldur            x0, [fp, #-8]
    // 0x1297fdc: LoadField: r1 = r0->field_f
    //     0x1297fdc: ldur            w1, [x0, #0xf]
    // 0x1297fe0: DecompressPointer r1
    //     0x1297fe0: add             x1, x1, HEAP, lsl #32
    // 0x1297fe4: cmp             w1, NULL
    // 0x1297fe8: b.ne            #0x1297ff4
    // 0x1297fec: r3 = ""
    //     0x1297fec: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1297ff0: b               #0x1297ff8
    // 0x1297ff4: mov             x3, x1
    // 0x1297ff8: ldur            x1, [fp, #-0x10]
    // 0x1297ffc: stur            x3, [fp, #-0x18]
    // 0x1298000: r0 = of()
    //     0x1298000: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1298004: LoadField: r1 = r0->field_87
    //     0x1298004: ldur            w1, [x0, #0x87]
    // 0x1298008: DecompressPointer r1
    //     0x1298008: add             x1, x1, HEAP, lsl #32
    // 0x129800c: LoadField: r2 = r1->field_2b
    //     0x129800c: ldur            w2, [x1, #0x2b]
    // 0x1298010: DecompressPointer r2
    //     0x1298010: add             x2, x2, HEAP, lsl #32
    // 0x1298014: ldur            x0, [fp, #-8]
    // 0x1298018: stur            x2, [fp, #-0x10]
    // 0x129801c: LoadField: r1 = r0->field_7
    //     0x129801c: ldur            w1, [x0, #7]
    // 0x1298020: DecompressPointer r1
    //     0x1298020: add             x1, x1, HEAP, lsl #32
    // 0x1298024: r0 = LoadClassIdInstr(r1)
    //     0x1298024: ldur            x0, [x1, #-1]
    //     0x1298028: ubfx            x0, x0, #0xc, #0x14
    // 0x129802c: r16 = "final"
    //     0x129802c: add             x16, PP, #0x48, lsl #12  ; [pp+0x483d8] "final"
    //     0x1298030: ldr             x16, [x16, #0x3d8]
    // 0x1298034: stp             x16, x1, [SP]
    // 0x1298038: mov             lr, x0
    // 0x129803c: ldr             lr, [x21, lr, lsl #3]
    // 0x1298040: blr             lr
    // 0x1298044: tbnz            w0, #4, #0x1298050
    // 0x1298048: r1 = Instance_Color
    //     0x1298048: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x129804c: b               #0x1298064
    // 0x1298050: r1 = Instance_Color
    //     0x1298050: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1298054: d0 = 0.700000
    //     0x1298054: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x1298058: ldr             d0, [x17, #0xf48]
    // 0x129805c: r0 = withOpacity()
    //     0x129805c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1298060: mov             x1, x0
    // 0x1298064: ldur            x3, [fp, #-0x20]
    // 0x1298068: ldur            x0, [fp, #-0x30]
    // 0x129806c: ldur            x2, [fp, #-0x18]
    // 0x1298070: r16 = 14.000000
    //     0x1298070: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1298074: ldr             x16, [x16, #0x1d8]
    // 0x1298078: stp             x1, x16, [SP]
    // 0x129807c: ldur            x1, [fp, #-0x10]
    // 0x1298080: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1298080: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1298084: ldr             x4, [x4, #0xaa0]
    // 0x1298088: r0 = copyWith()
    //     0x1298088: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x129808c: stur            x0, [fp, #-8]
    // 0x1298090: r0 = Text()
    //     0x1298090: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1298094: mov             x3, x0
    // 0x1298098: ldur            x0, [fp, #-0x18]
    // 0x129809c: stur            x3, [fp, #-0x10]
    // 0x12980a0: StoreField: r3->field_b = r0
    //     0x12980a0: stur            w0, [x3, #0xb]
    // 0x12980a4: ldur            x0, [fp, #-8]
    // 0x12980a8: StoreField: r3->field_13 = r0
    //     0x12980a8: stur            w0, [x3, #0x13]
    // 0x12980ac: r1 = Null
    //     0x12980ac: mov             x1, NULL
    // 0x12980b0: r2 = 6
    //     0x12980b0: movz            x2, #0x6
    // 0x12980b4: r0 = AllocateArray()
    //     0x12980b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12980b8: mov             x2, x0
    // 0x12980bc: ldur            x0, [fp, #-0x30]
    // 0x12980c0: stur            x2, [fp, #-8]
    // 0x12980c4: StoreField: r2->field_f = r0
    //     0x12980c4: stur            w0, [x2, #0xf]
    // 0x12980c8: r16 = Instance_SizedBox
    //     0x12980c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0x12980cc: ldr             x16, [x16, #0x8b8]
    // 0x12980d0: StoreField: r2->field_13 = r16
    //     0x12980d0: stur            w16, [x2, #0x13]
    // 0x12980d4: ldur            x0, [fp, #-0x10]
    // 0x12980d8: ArrayStore: r2[0] = r0  ; List_4
    //     0x12980d8: stur            w0, [x2, #0x17]
    // 0x12980dc: r1 = <Widget>
    //     0x12980dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12980e0: r0 = AllocateGrowableArray()
    //     0x12980e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12980e4: mov             x1, x0
    // 0x12980e8: ldur            x0, [fp, #-8]
    // 0x12980ec: stur            x1, [fp, #-0x10]
    // 0x12980f0: StoreField: r1->field_f = r0
    //     0x12980f0: stur            w0, [x1, #0xf]
    // 0x12980f4: r0 = 6
    //     0x12980f4: movz            x0, #0x6
    // 0x12980f8: StoreField: r1->field_b = r0
    //     0x12980f8: stur            w0, [x1, #0xb]
    // 0x12980fc: r0 = Row()
    //     0x12980fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1298100: mov             x3, x0
    // 0x1298104: r0 = Instance_Axis
    //     0x1298104: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1298108: stur            x3, [fp, #-8]
    // 0x129810c: StoreField: r3->field_f = r0
    //     0x129810c: stur            w0, [x3, #0xf]
    // 0x1298110: r0 = Instance_MainAxisAlignment
    //     0x1298110: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x1298114: ldr             x0, [x0, #0xa8]
    // 0x1298118: StoreField: r3->field_13 = r0
    //     0x1298118: stur            w0, [x3, #0x13]
    // 0x129811c: r0 = Instance_MainAxisSize
    //     0x129811c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1298120: ldr             x0, [x0, #0xa10]
    // 0x1298124: ArrayStore: r3[0] = r0  ; List_4
    //     0x1298124: stur            w0, [x3, #0x17]
    // 0x1298128: r4 = Instance_CrossAxisAlignment
    //     0x1298128: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x129812c: ldr             x4, [x4, #0xa18]
    // 0x1298130: StoreField: r3->field_1b = r4
    //     0x1298130: stur            w4, [x3, #0x1b]
    // 0x1298134: r5 = Instance_VerticalDirection
    //     0x1298134: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1298138: ldr             x5, [x5, #0xa20]
    // 0x129813c: StoreField: r3->field_23 = r5
    //     0x129813c: stur            w5, [x3, #0x23]
    // 0x1298140: r6 = Instance_Clip
    //     0x1298140: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1298144: ldr             x6, [x6, #0x38]
    // 0x1298148: StoreField: r3->field_2b = r6
    //     0x1298148: stur            w6, [x3, #0x2b]
    // 0x129814c: StoreField: r3->field_2f = rZR
    //     0x129814c: stur            xzr, [x3, #0x2f]
    // 0x1298150: ldur            x1, [fp, #-0x10]
    // 0x1298154: StoreField: r3->field_b = r1
    //     0x1298154: stur            w1, [x3, #0xb]
    // 0x1298158: r1 = Null
    //     0x1298158: mov             x1, NULL
    // 0x129815c: r2 = 4
    //     0x129815c: movz            x2, #0x4
    // 0x1298160: r0 = AllocateArray()
    //     0x1298160: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1298164: mov             x2, x0
    // 0x1298168: ldur            x0, [fp, #-0x20]
    // 0x129816c: stur            x2, [fp, #-0x10]
    // 0x1298170: StoreField: r2->field_f = r0
    //     0x1298170: stur            w0, [x2, #0xf]
    // 0x1298174: ldur            x0, [fp, #-8]
    // 0x1298178: StoreField: r2->field_13 = r0
    //     0x1298178: stur            w0, [x2, #0x13]
    // 0x129817c: r1 = <Widget>
    //     0x129817c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1298180: r0 = AllocateGrowableArray()
    //     0x1298180: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1298184: mov             x1, x0
    // 0x1298188: ldur            x0, [fp, #-0x10]
    // 0x129818c: stur            x1, [fp, #-8]
    // 0x1298190: StoreField: r1->field_f = r0
    //     0x1298190: stur            w0, [x1, #0xf]
    // 0x1298194: r0 = 4
    //     0x1298194: movz            x0, #0x4
    // 0x1298198: StoreField: r1->field_b = r0
    //     0x1298198: stur            w0, [x1, #0xb]
    // 0x129819c: r0 = Column()
    //     0x129819c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x12981a0: mov             x1, x0
    // 0x12981a4: r0 = Instance_Axis
    //     0x12981a4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x12981a8: stur            x1, [fp, #-0x10]
    // 0x12981ac: StoreField: r1->field_f = r0
    //     0x12981ac: stur            w0, [x1, #0xf]
    // 0x12981b0: r0 = Instance_MainAxisAlignment
    //     0x12981b0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0x12981b4: ldr             x0, [x0, #0xd10]
    // 0x12981b8: StoreField: r1->field_13 = r0
    //     0x12981b8: stur            w0, [x1, #0x13]
    // 0x12981bc: r0 = Instance_MainAxisSize
    //     0x12981bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x12981c0: ldr             x0, [x0, #0xa10]
    // 0x12981c4: ArrayStore: r1[0] = r0  ; List_4
    //     0x12981c4: stur            w0, [x1, #0x17]
    // 0x12981c8: r0 = Instance_CrossAxisAlignment
    //     0x12981c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12981cc: ldr             x0, [x0, #0xa18]
    // 0x12981d0: StoreField: r1->field_1b = r0
    //     0x12981d0: stur            w0, [x1, #0x1b]
    // 0x12981d4: r0 = Instance_VerticalDirection
    //     0x12981d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12981d8: ldr             x0, [x0, #0xa20]
    // 0x12981dc: StoreField: r1->field_23 = r0
    //     0x12981dc: stur            w0, [x1, #0x23]
    // 0x12981e0: r0 = Instance_Clip
    //     0x12981e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12981e4: ldr             x0, [x0, #0x38]
    // 0x12981e8: StoreField: r1->field_2b = r0
    //     0x12981e8: stur            w0, [x1, #0x2b]
    // 0x12981ec: StoreField: r1->field_2f = rZR
    //     0x12981ec: stur            xzr, [x1, #0x2f]
    // 0x12981f0: ldur            x0, [fp, #-8]
    // 0x12981f4: StoreField: r1->field_b = r0
    //     0x12981f4: stur            w0, [x1, #0xb]
    // 0x12981f8: r0 = Padding()
    //     0x12981f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12981fc: r1 = Instance_EdgeInsets
    //     0x12981fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1298200: ldr             x1, [x1, #0x980]
    // 0x1298204: StoreField: r0->field_f = r1
    //     0x1298204: stur            w1, [x0, #0xf]
    // 0x1298208: ldur            x1, [fp, #-0x10]
    // 0x129820c: StoreField: r0->field_b = r1
    //     0x129820c: stur            w1, [x0, #0xb]
    // 0x1298210: LeaveFrame
    //     0x1298210: mov             SP, fp
    //     0x1298214: ldp             fp, lr, [SP], #0x10
    // 0x1298218: ret
    //     0x1298218: ret             
    // 0x129821c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x129821c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1298220: b               #0x1297e4c
  }
}
