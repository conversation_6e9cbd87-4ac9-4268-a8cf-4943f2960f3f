// lib: , url: package:customer_app/app/presentation/views/glass/customization/customisation_text.dart

// class id: 1049384, size: 0x8
class :: {
}

// class id: 3348, size: 0x1c, field offset: 0x14
class _CustomisationTextState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb574e0, size: 0x7a0
    // 0xb574e0: EnterFrame
    //     0xb574e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb574e4: mov             fp, SP
    // 0xb574e8: AllocStack(0x88)
    //     0xb574e8: sub             SP, SP, #0x88
    // 0xb574ec: SetupParameters(_CustomisationTextState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb574ec: mov             x0, x1
    //     0xb574f0: stur            x1, [fp, #-8]
    //     0xb574f4: mov             x1, x2
    //     0xb574f8: stur            x2, [fp, #-0x10]
    // 0xb574fc: CheckStackOverflow
    //     0xb574fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb57500: cmp             SP, x16
    //     0xb57504: b.ls            #0xb57c6c
    // 0xb57508: r1 = 1
    //     0xb57508: movz            x1, #0x1
    // 0xb5750c: r0 = AllocateContext()
    //     0xb5750c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb57510: mov             x3, x0
    // 0xb57514: ldur            x0, [fp, #-8]
    // 0xb57518: stur            x3, [fp, #-0x20]
    // 0xb5751c: StoreField: r3->field_f = r0
    //     0xb5751c: stur            w0, [x3, #0xf]
    // 0xb57520: LoadField: r1 = r0->field_b
    //     0xb57520: ldur            w1, [x0, #0xb]
    // 0xb57524: DecompressPointer r1
    //     0xb57524: add             x1, x1, HEAP, lsl #32
    // 0xb57528: cmp             w1, NULL
    // 0xb5752c: b.eq            #0xb57c74
    // 0xb57530: LoadField: r2 = r1->field_b
    //     0xb57530: ldur            w2, [x1, #0xb]
    // 0xb57534: DecompressPointer r2
    //     0xb57534: add             x2, x2, HEAP, lsl #32
    // 0xb57538: cmp             w2, NULL
    // 0xb5753c: b.ne            #0xb57548
    // 0xb57540: r1 = Null
    //     0xb57540: mov             x1, NULL
    // 0xb57544: b               #0xb57550
    // 0xb57548: LoadField: r1 = r2->field_2b
    //     0xb57548: ldur            w1, [x2, #0x2b]
    // 0xb5754c: DecompressPointer r1
    //     0xb5754c: add             x1, x1, HEAP, lsl #32
    // 0xb57550: cmp             w1, NULL
    // 0xb57554: b.eq            #0xb575b0
    // 0xb57558: tbnz            w1, #4, #0xb575b0
    // 0xb5755c: cmp             w2, NULL
    // 0xb57560: b.ne            #0xb5756c
    // 0xb57564: r4 = Null
    //     0xb57564: mov             x4, NULL
    // 0xb57568: b               #0xb57578
    // 0xb5756c: LoadField: r1 = r2->field_1b
    //     0xb5756c: ldur            w1, [x2, #0x1b]
    // 0xb57570: DecompressPointer r1
    //     0xb57570: add             x1, x1, HEAP, lsl #32
    // 0xb57574: mov             x4, x1
    // 0xb57578: stur            x4, [fp, #-0x18]
    // 0xb5757c: r1 = Null
    //     0xb5757c: mov             x1, NULL
    // 0xb57580: r2 = 4
    //     0xb57580: movz            x2, #0x4
    // 0xb57584: r0 = AllocateArray()
    //     0xb57584: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb57588: mov             x1, x0
    // 0xb5758c: ldur            x0, [fp, #-0x18]
    // 0xb57590: StoreField: r1->field_f = r0
    //     0xb57590: stur            w0, [x1, #0xf]
    // 0xb57594: r16 = " *"
    //     0xb57594: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xb57598: ldr             x16, [x16, #0xfc8]
    // 0xb5759c: StoreField: r1->field_13 = r16
    //     0xb5759c: stur            w16, [x1, #0x13]
    // 0xb575a0: str             x1, [SP]
    // 0xb575a4: r0 = _interpolate()
    //     0xb575a4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb575a8: mov             x2, x0
    // 0xb575ac: b               #0xb575d4
    // 0xb575b0: cmp             w2, NULL
    // 0xb575b4: b.ne            #0xb575c0
    // 0xb575b8: r0 = Null
    //     0xb575b8: mov             x0, NULL
    // 0xb575bc: b               #0xb575c8
    // 0xb575c0: LoadField: r0 = r2->field_1b
    //     0xb575c0: ldur            w0, [x2, #0x1b]
    // 0xb575c4: DecompressPointer r0
    //     0xb575c4: add             x0, x0, HEAP, lsl #32
    // 0xb575c8: str             x0, [SP]
    // 0xb575cc: r0 = _interpolateSingle()
    //     0xb575cc: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb575d0: mov             x2, x0
    // 0xb575d4: ldur            x0, [fp, #-8]
    // 0xb575d8: ldur            x1, [fp, #-0x10]
    // 0xb575dc: stur            x2, [fp, #-0x18]
    // 0xb575e0: r0 = of()
    //     0xb575e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb575e4: LoadField: r1 = r0->field_87
    //     0xb575e4: ldur            w1, [x0, #0x87]
    // 0xb575e8: DecompressPointer r1
    //     0xb575e8: add             x1, x1, HEAP, lsl #32
    // 0xb575ec: LoadField: r0 = r1->field_7
    //     0xb575ec: ldur            w0, [x1, #7]
    // 0xb575f0: DecompressPointer r0
    //     0xb575f0: add             x0, x0, HEAP, lsl #32
    // 0xb575f4: r16 = Instance_Color
    //     0xb575f4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb575f8: r30 = 14.000000
    //     0xb575f8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb575fc: ldr             lr, [lr, #0x1d8]
    // 0xb57600: stp             lr, x16, [SP]
    // 0xb57604: mov             x1, x0
    // 0xb57608: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb57608: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5760c: ldr             x4, [x4, #0x9b8]
    // 0xb57610: r0 = copyWith()
    //     0xb57610: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb57614: stur            x0, [fp, #-0x28]
    // 0xb57618: r0 = Text()
    //     0xb57618: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5761c: mov             x1, x0
    // 0xb57620: ldur            x0, [fp, #-0x18]
    // 0xb57624: stur            x1, [fp, #-0x30]
    // 0xb57628: StoreField: r1->field_b = r0
    //     0xb57628: stur            w0, [x1, #0xb]
    // 0xb5762c: ldur            x0, [fp, #-0x28]
    // 0xb57630: StoreField: r1->field_13 = r0
    //     0xb57630: stur            w0, [x1, #0x13]
    // 0xb57634: r0 = Padding()
    //     0xb57634: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb57638: mov             x3, x0
    // 0xb5763c: r0 = Instance_EdgeInsets
    //     0xb5763c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb57640: ldr             x0, [x0, #0x1f0]
    // 0xb57644: stur            x3, [fp, #-0x38]
    // 0xb57648: StoreField: r3->field_f = r0
    //     0xb57648: stur            w0, [x3, #0xf]
    // 0xb5764c: ldur            x0, [fp, #-0x30]
    // 0xb57650: StoreField: r3->field_b = r0
    //     0xb57650: stur            w0, [x3, #0xb]
    // 0xb57654: ldur            x0, [fp, #-8]
    // 0xb57658: LoadField: r1 = r0->field_b
    //     0xb57658: ldur            w1, [x0, #0xb]
    // 0xb5765c: DecompressPointer r1
    //     0xb5765c: add             x1, x1, HEAP, lsl #32
    // 0xb57660: cmp             w1, NULL
    // 0xb57664: b.eq            #0xb57c78
    // 0xb57668: LoadField: r4 = r1->field_b
    //     0xb57668: ldur            w4, [x1, #0xb]
    // 0xb5766c: DecompressPointer r4
    //     0xb5766c: add             x4, x4, HEAP, lsl #32
    // 0xb57670: stur            x4, [fp, #-0x28]
    // 0xb57674: cmp             w4, NULL
    // 0xb57678: b.ne            #0xb57684
    // 0xb5767c: r1 = Null
    //     0xb5767c: mov             x1, NULL
    // 0xb57680: b               #0xb5768c
    // 0xb57684: LoadField: r1 = r4->field_23
    //     0xb57684: ldur            w1, [x4, #0x23]
    // 0xb57688: DecompressPointer r1
    //     0xb57688: add             x1, x1, HEAP, lsl #32
    // 0xb5768c: cbnz            w1, #0xb57698
    // 0xb57690: r5 = false
    //     0xb57690: add             x5, NULL, #0x30  ; false
    // 0xb57694: b               #0xb5769c
    // 0xb57698: r5 = true
    //     0xb57698: add             x5, NULL, #0x20  ; true
    // 0xb5769c: stur            x5, [fp, #-0x18]
    // 0xb576a0: r1 = Null
    //     0xb576a0: mov             x1, NULL
    // 0xb576a4: r2 = 4
    //     0xb576a4: movz            x2, #0x4
    // 0xb576a8: r0 = AllocateArray()
    //     0xb576a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb576ac: mov             x1, x0
    // 0xb576b0: stur            x1, [fp, #-0x30]
    // 0xb576b4: r16 = "+ "
    //     0xb576b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xb576b8: ldr             x16, [x16, #0xc30]
    // 0xb576bc: StoreField: r1->field_f = r16
    //     0xb576bc: stur            w16, [x1, #0xf]
    // 0xb576c0: ldur            x0, [fp, #-0x28]
    // 0xb576c4: cmp             w0, NULL
    // 0xb576c8: b.ne            #0xb576d4
    // 0xb576cc: r0 = Null
    //     0xb576cc: mov             x0, NULL
    // 0xb576d0: b               #0xb576fc
    // 0xb576d4: LoadField: r2 = r0->field_27
    //     0xb576d4: ldur            w2, [x0, #0x27]
    // 0xb576d8: DecompressPointer r2
    //     0xb576d8: add             x2, x2, HEAP, lsl #32
    // 0xb576dc: r0 = LoadClassIdInstr(r2)
    //     0xb576dc: ldur            x0, [x2, #-1]
    //     0xb576e0: ubfx            x0, x0, #0xc, #0x14
    // 0xb576e4: str             x2, [SP]
    // 0xb576e8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb576e8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb576ec: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb576ec: movz            x17, #0x2700
    //     0xb576f0: add             lr, x0, x17
    //     0xb576f4: ldr             lr, [x21, lr, lsl #3]
    //     0xb576f8: blr             lr
    // 0xb576fc: cmp             w0, NULL
    // 0xb57700: b.ne            #0xb57708
    // 0xb57704: r0 = " "
    //     0xb57704: ldr             x0, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb57708: ldur            x3, [fp, #-8]
    // 0xb5770c: ldur            x2, [fp, #-0x38]
    // 0xb57710: ldur            x4, [fp, #-0x18]
    // 0xb57714: ldur            x1, [fp, #-0x30]
    // 0xb57718: ArrayStore: r1[1] = r0  ; List_4
    //     0xb57718: add             x25, x1, #0x13
    //     0xb5771c: str             w0, [x25]
    //     0xb57720: tbz             w0, #0, #0xb5773c
    //     0xb57724: ldurb           w16, [x1, #-1]
    //     0xb57728: ldurb           w17, [x0, #-1]
    //     0xb5772c: and             x16, x17, x16, lsr #2
    //     0xb57730: tst             x16, HEAP, lsr #32
    //     0xb57734: b.eq            #0xb5773c
    //     0xb57738: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5773c: ldur            x16, [fp, #-0x30]
    // 0xb57740: str             x16, [SP]
    // 0xb57744: r0 = _interpolate()
    //     0xb57744: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb57748: ldur            x1, [fp, #-0x10]
    // 0xb5774c: stur            x0, [fp, #-0x28]
    // 0xb57750: r0 = of()
    //     0xb57750: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb57754: LoadField: r1 = r0->field_87
    //     0xb57754: ldur            w1, [x0, #0x87]
    // 0xb57758: DecompressPointer r1
    //     0xb57758: add             x1, x1, HEAP, lsl #32
    // 0xb5775c: LoadField: r0 = r1->field_2b
    //     0xb5775c: ldur            w0, [x1, #0x2b]
    // 0xb57760: DecompressPointer r0
    //     0xb57760: add             x0, x0, HEAP, lsl #32
    // 0xb57764: stur            x0, [fp, #-0x30]
    // 0xb57768: r1 = Instance_Color
    //     0xb57768: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5776c: d0 = 0.700000
    //     0xb5776c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb57770: ldr             d0, [x17, #0xf48]
    // 0xb57774: r0 = withOpacity()
    //     0xb57774: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb57778: r16 = 14.000000
    //     0xb57778: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5777c: ldr             x16, [x16, #0x1d8]
    // 0xb57780: stp             x16, x0, [SP]
    // 0xb57784: ldur            x1, [fp, #-0x30]
    // 0xb57788: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb57788: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5778c: ldr             x4, [x4, #0x9b8]
    // 0xb57790: r0 = copyWith()
    //     0xb57790: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb57794: stur            x0, [fp, #-0x30]
    // 0xb57798: r0 = Text()
    //     0xb57798: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5779c: mov             x1, x0
    // 0xb577a0: ldur            x0, [fp, #-0x28]
    // 0xb577a4: stur            x1, [fp, #-0x40]
    // 0xb577a8: StoreField: r1->field_b = r0
    //     0xb577a8: stur            w0, [x1, #0xb]
    // 0xb577ac: ldur            x0, [fp, #-0x30]
    // 0xb577b0: StoreField: r1->field_13 = r0
    //     0xb577b0: stur            w0, [x1, #0x13]
    // 0xb577b4: r0 = Visibility()
    //     0xb577b4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb577b8: mov             x1, x0
    // 0xb577bc: ldur            x0, [fp, #-0x40]
    // 0xb577c0: stur            x1, [fp, #-0x28]
    // 0xb577c4: StoreField: r1->field_b = r0
    //     0xb577c4: stur            w0, [x1, #0xb]
    // 0xb577c8: r0 = Instance_SizedBox
    //     0xb577c8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb577cc: StoreField: r1->field_f = r0
    //     0xb577cc: stur            w0, [x1, #0xf]
    // 0xb577d0: ldur            x2, [fp, #-0x18]
    // 0xb577d4: StoreField: r1->field_13 = r2
    //     0xb577d4: stur            w2, [x1, #0x13]
    // 0xb577d8: r2 = false
    //     0xb577d8: add             x2, NULL, #0x30  ; false
    // 0xb577dc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb577dc: stur            w2, [x1, #0x17]
    // 0xb577e0: StoreField: r1->field_1b = r2
    //     0xb577e0: stur            w2, [x1, #0x1b]
    // 0xb577e4: StoreField: r1->field_1f = r2
    //     0xb577e4: stur            w2, [x1, #0x1f]
    // 0xb577e8: StoreField: r1->field_23 = r2
    //     0xb577e8: stur            w2, [x1, #0x23]
    // 0xb577ec: StoreField: r1->field_27 = r2
    //     0xb577ec: stur            w2, [x1, #0x27]
    // 0xb577f0: StoreField: r1->field_2b = r2
    //     0xb577f0: stur            w2, [x1, #0x2b]
    // 0xb577f4: r0 = Padding()
    //     0xb577f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb577f8: mov             x3, x0
    // 0xb577fc: r0 = Instance_EdgeInsets
    //     0xb577fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb57800: ldr             x0, [x0, #0x980]
    // 0xb57804: stur            x3, [fp, #-0x18]
    // 0xb57808: StoreField: r3->field_f = r0
    //     0xb57808: stur            w0, [x3, #0xf]
    // 0xb5780c: ldur            x0, [fp, #-0x28]
    // 0xb57810: StoreField: r3->field_b = r0
    //     0xb57810: stur            w0, [x3, #0xb]
    // 0xb57814: r1 = Null
    //     0xb57814: mov             x1, NULL
    // 0xb57818: r2 = 6
    //     0xb57818: movz            x2, #0x6
    // 0xb5781c: r0 = AllocateArray()
    //     0xb5781c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb57820: mov             x2, x0
    // 0xb57824: ldur            x0, [fp, #-0x38]
    // 0xb57828: stur            x2, [fp, #-0x28]
    // 0xb5782c: StoreField: r2->field_f = r0
    //     0xb5782c: stur            w0, [x2, #0xf]
    // 0xb57830: r16 = Instance_Spacer
    //     0xb57830: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb57834: ldr             x16, [x16, #0xf0]
    // 0xb57838: StoreField: r2->field_13 = r16
    //     0xb57838: stur            w16, [x2, #0x13]
    // 0xb5783c: ldur            x0, [fp, #-0x18]
    // 0xb57840: ArrayStore: r2[0] = r0  ; List_4
    //     0xb57840: stur            w0, [x2, #0x17]
    // 0xb57844: r1 = <Widget>
    //     0xb57844: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb57848: r0 = AllocateGrowableArray()
    //     0xb57848: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5784c: mov             x1, x0
    // 0xb57850: ldur            x0, [fp, #-0x28]
    // 0xb57854: stur            x1, [fp, #-0x18]
    // 0xb57858: StoreField: r1->field_f = r0
    //     0xb57858: stur            w0, [x1, #0xf]
    // 0xb5785c: r2 = 6
    //     0xb5785c: movz            x2, #0x6
    // 0xb57860: StoreField: r1->field_b = r2
    //     0xb57860: stur            w2, [x1, #0xb]
    // 0xb57864: r0 = Row()
    //     0xb57864: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb57868: mov             x2, x0
    // 0xb5786c: r0 = Instance_Axis
    //     0xb5786c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb57870: stur            x2, [fp, #-0x30]
    // 0xb57874: StoreField: r2->field_f = r0
    //     0xb57874: stur            w0, [x2, #0xf]
    // 0xb57878: r0 = Instance_MainAxisAlignment
    //     0xb57878: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb5787c: ldr             x0, [x0, #0xa08]
    // 0xb57880: StoreField: r2->field_13 = r0
    //     0xb57880: stur            w0, [x2, #0x13]
    // 0xb57884: r3 = Instance_MainAxisSize
    //     0xb57884: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb57888: ldr             x3, [x3, #0xa10]
    // 0xb5788c: ArrayStore: r2[0] = r3  ; List_4
    //     0xb5788c: stur            w3, [x2, #0x17]
    // 0xb57890: r1 = Instance_CrossAxisAlignment
    //     0xb57890: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb57894: ldr             x1, [x1, #0xa18]
    // 0xb57898: StoreField: r2->field_1b = r1
    //     0xb57898: stur            w1, [x2, #0x1b]
    // 0xb5789c: r4 = Instance_VerticalDirection
    //     0xb5789c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb578a0: ldr             x4, [x4, #0xa20]
    // 0xb578a4: StoreField: r2->field_23 = r4
    //     0xb578a4: stur            w4, [x2, #0x23]
    // 0xb578a8: r5 = Instance_Clip
    //     0xb578a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb578ac: ldr             x5, [x5, #0x38]
    // 0xb578b0: StoreField: r2->field_2b = r5
    //     0xb578b0: stur            w5, [x2, #0x2b]
    // 0xb578b4: StoreField: r2->field_2f = rZR
    //     0xb578b4: stur            xzr, [x2, #0x2f]
    // 0xb578b8: ldur            x1, [fp, #-0x18]
    // 0xb578bc: StoreField: r2->field_b = r1
    //     0xb578bc: stur            w1, [x2, #0xb]
    // 0xb578c0: ldur            x6, [fp, #-8]
    // 0xb578c4: LoadField: r1 = r6->field_b
    //     0xb578c4: ldur            w1, [x6, #0xb]
    // 0xb578c8: DecompressPointer r1
    //     0xb578c8: add             x1, x1, HEAP, lsl #32
    // 0xb578cc: cmp             w1, NULL
    // 0xb578d0: b.eq            #0xb57c7c
    // 0xb578d4: LoadField: r7 = r1->field_b
    //     0xb578d4: ldur            w7, [x1, #0xb]
    // 0xb578d8: DecompressPointer r7
    //     0xb578d8: add             x7, x7, HEAP, lsl #32
    // 0xb578dc: cmp             w7, NULL
    // 0xb578e0: b.ne            #0xb578ec
    // 0xb578e4: r1 = Null
    //     0xb578e4: mov             x1, NULL
    // 0xb578e8: b               #0xb57918
    // 0xb578ec: LoadField: r1 = r7->field_1f
    //     0xb578ec: ldur            w1, [x7, #0x1f]
    // 0xb578f0: DecompressPointer r1
    //     0xb578f0: add             x1, x1, HEAP, lsl #32
    // 0xb578f4: cmp             w1, NULL
    // 0xb578f8: b.ne            #0xb57904
    // 0xb578fc: r1 = Null
    //     0xb578fc: mov             x1, NULL
    // 0xb57900: b               #0xb57918
    // 0xb57904: LoadField: r8 = r1->field_7
    //     0xb57904: ldur            w8, [x1, #7]
    // 0xb57908: cbnz            w8, #0xb57914
    // 0xb5790c: r1 = false
    //     0xb5790c: add             x1, NULL, #0x30  ; false
    // 0xb57910: b               #0xb57918
    // 0xb57914: r1 = true
    //     0xb57914: add             x1, NULL, #0x20  ; true
    // 0xb57918: cmp             w1, NULL
    // 0xb5791c: b.ne            #0xb57928
    // 0xb57920: r8 = false
    //     0xb57920: add             x8, NULL, #0x30  ; false
    // 0xb57924: b               #0xb5792c
    // 0xb57928: mov             x8, x1
    // 0xb5792c: stur            x8, [fp, #-0x28]
    // 0xb57930: cmp             w7, NULL
    // 0xb57934: b.ne            #0xb57940
    // 0xb57938: r1 = Null
    //     0xb57938: mov             x1, NULL
    // 0xb5793c: b               #0xb57948
    // 0xb57940: LoadField: r1 = r7->field_1f
    //     0xb57940: ldur            w1, [x7, #0x1f]
    // 0xb57944: DecompressPointer r1
    //     0xb57944: add             x1, x1, HEAP, lsl #32
    // 0xb57948: cmp             w1, NULL
    // 0xb5794c: b.ne            #0xb57958
    // 0xb57950: r7 = ""
    //     0xb57950: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb57954: b               #0xb5795c
    // 0xb57958: mov             x7, x1
    // 0xb5795c: ldur            x1, [fp, #-0x10]
    // 0xb57960: stur            x7, [fp, #-0x18]
    // 0xb57964: r0 = of()
    //     0xb57964: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb57968: LoadField: r1 = r0->field_87
    //     0xb57968: ldur            w1, [x0, #0x87]
    // 0xb5796c: DecompressPointer r1
    //     0xb5796c: add             x1, x1, HEAP, lsl #32
    // 0xb57970: LoadField: r0 = r1->field_2b
    //     0xb57970: ldur            w0, [x1, #0x2b]
    // 0xb57974: DecompressPointer r0
    //     0xb57974: add             x0, x0, HEAP, lsl #32
    // 0xb57978: stur            x0, [fp, #-0x38]
    // 0xb5797c: r1 = Instance_Color
    //     0xb5797c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb57980: d0 = 0.400000
    //     0xb57980: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb57984: r0 = withOpacity()
    //     0xb57984: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb57988: r16 = 12.000000
    //     0xb57988: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb5798c: ldr             x16, [x16, #0x9e8]
    // 0xb57990: stp             x16, x0, [SP]
    // 0xb57994: ldur            x1, [fp, #-0x38]
    // 0xb57998: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb57998: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5799c: ldr             x4, [x4, #0x9b8]
    // 0xb579a0: r0 = copyWith()
    //     0xb579a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb579a4: stur            x0, [fp, #-0x38]
    // 0xb579a8: r0 = Text()
    //     0xb579a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb579ac: mov             x1, x0
    // 0xb579b0: ldur            x0, [fp, #-0x18]
    // 0xb579b4: stur            x1, [fp, #-0x40]
    // 0xb579b8: StoreField: r1->field_b = r0
    //     0xb579b8: stur            w0, [x1, #0xb]
    // 0xb579bc: ldur            x0, [fp, #-0x38]
    // 0xb579c0: StoreField: r1->field_13 = r0
    //     0xb579c0: stur            w0, [x1, #0x13]
    // 0xb579c4: r0 = Padding()
    //     0xb579c4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb579c8: mov             x1, x0
    // 0xb579cc: r0 = Instance_EdgeInsets
    //     0xb579cc: add             x0, PP, #0x6a, lsl #12  ; [pp+0x6a408] Obj!EdgeInsets@d582b1
    //     0xb579d0: ldr             x0, [x0, #0x408]
    // 0xb579d4: stur            x1, [fp, #-0x18]
    // 0xb579d8: StoreField: r1->field_f = r0
    //     0xb579d8: stur            w0, [x1, #0xf]
    // 0xb579dc: ldur            x0, [fp, #-0x40]
    // 0xb579e0: StoreField: r1->field_b = r0
    //     0xb579e0: stur            w0, [x1, #0xb]
    // 0xb579e4: r0 = Visibility()
    //     0xb579e4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb579e8: mov             x1, x0
    // 0xb579ec: ldur            x0, [fp, #-0x18]
    // 0xb579f0: stur            x1, [fp, #-0x38]
    // 0xb579f4: StoreField: r1->field_b = r0
    //     0xb579f4: stur            w0, [x1, #0xb]
    // 0xb579f8: r0 = Instance_SizedBox
    //     0xb579f8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb579fc: StoreField: r1->field_f = r0
    //     0xb579fc: stur            w0, [x1, #0xf]
    // 0xb57a00: ldur            x0, [fp, #-0x28]
    // 0xb57a04: StoreField: r1->field_13 = r0
    //     0xb57a04: stur            w0, [x1, #0x13]
    // 0xb57a08: r0 = false
    //     0xb57a08: add             x0, NULL, #0x30  ; false
    // 0xb57a0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb57a0c: stur            w0, [x1, #0x17]
    // 0xb57a10: StoreField: r1->field_1b = r0
    //     0xb57a10: stur            w0, [x1, #0x1b]
    // 0xb57a14: StoreField: r1->field_1f = r0
    //     0xb57a14: stur            w0, [x1, #0x1f]
    // 0xb57a18: StoreField: r1->field_23 = r0
    //     0xb57a18: stur            w0, [x1, #0x23]
    // 0xb57a1c: StoreField: r1->field_27 = r0
    //     0xb57a1c: stur            w0, [x1, #0x27]
    // 0xb57a20: StoreField: r1->field_2b = r0
    //     0xb57a20: stur            w0, [x1, #0x2b]
    // 0xb57a24: ldur            x0, [fp, #-8]
    // 0xb57a28: LoadField: r2 = r0->field_13
    //     0xb57a28: ldur            w2, [x0, #0x13]
    // 0xb57a2c: DecompressPointer r2
    //     0xb57a2c: add             x2, x2, HEAP, lsl #32
    // 0xb57a30: stur            x2, [fp, #-0x18]
    // 0xb57a34: r0 = LengthLimitingTextInputFormatter()
    //     0xb57a34: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xb57a38: mov             x3, x0
    // 0xb57a3c: r0 = 60
    //     0xb57a3c: movz            x0, #0x3c
    // 0xb57a40: stur            x3, [fp, #-0x28]
    // 0xb57a44: StoreField: r3->field_7 = r0
    //     0xb57a44: stur            w0, [x3, #7]
    // 0xb57a48: r1 = Null
    //     0xb57a48: mov             x1, NULL
    // 0xb57a4c: r2 = 2
    //     0xb57a4c: movz            x2, #0x2
    // 0xb57a50: r0 = AllocateArray()
    //     0xb57a50: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb57a54: mov             x2, x0
    // 0xb57a58: ldur            x0, [fp, #-0x28]
    // 0xb57a5c: stur            x2, [fp, #-0x40]
    // 0xb57a60: StoreField: r2->field_f = r0
    //     0xb57a60: stur            w0, [x2, #0xf]
    // 0xb57a64: r1 = <TextInputFormatter>
    //     0xb57a64: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xb57a68: ldr             x1, [x1, #0x7b0]
    // 0xb57a6c: r0 = AllocateGrowableArray()
    //     0xb57a6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb57a70: mov             x2, x0
    // 0xb57a74: ldur            x0, [fp, #-0x40]
    // 0xb57a78: stur            x2, [fp, #-0x28]
    // 0xb57a7c: StoreField: r2->field_f = r0
    //     0xb57a7c: stur            w0, [x2, #0xf]
    // 0xb57a80: r0 = 2
    //     0xb57a80: movz            x0, #0x2
    // 0xb57a84: StoreField: r2->field_b = r0
    //     0xb57a84: stur            w0, [x2, #0xb]
    // 0xb57a88: ldur            x1, [fp, #-0x10]
    // 0xb57a8c: r0 = of()
    //     0xb57a8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb57a90: LoadField: r1 = r0->field_87
    //     0xb57a90: ldur            w1, [x0, #0x87]
    // 0xb57a94: DecompressPointer r1
    //     0xb57a94: add             x1, x1, HEAP, lsl #32
    // 0xb57a98: LoadField: r0 = r1->field_2b
    //     0xb57a98: ldur            w0, [x1, #0x2b]
    // 0xb57a9c: DecompressPointer r0
    //     0xb57a9c: add             x0, x0, HEAP, lsl #32
    // 0xb57aa0: ldur            x1, [fp, #-0x10]
    // 0xb57aa4: stur            x0, [fp, #-0x40]
    // 0xb57aa8: r0 = of()
    //     0xb57aa8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb57aac: LoadField: r1 = r0->field_5b
    //     0xb57aac: ldur            w1, [x0, #0x5b]
    // 0xb57ab0: DecompressPointer r1
    //     0xb57ab0: add             x1, x1, HEAP, lsl #32
    // 0xb57ab4: r16 = 12.000000
    //     0xb57ab4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb57ab8: ldr             x16, [x16, #0x9e8]
    // 0xb57abc: stp             x1, x16, [SP]
    // 0xb57ac0: ldur            x1, [fp, #-0x40]
    // 0xb57ac4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb57ac4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb57ac8: ldr             x4, [x4, #0xaa0]
    // 0xb57acc: r0 = copyWith()
    //     0xb57acc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb57ad0: mov             x2, x0
    // 0xb57ad4: ldur            x0, [fp, #-8]
    // 0xb57ad8: stur            x2, [fp, #-0x48]
    // 0xb57adc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb57adc: ldur            w3, [x0, #0x17]
    // 0xb57ae0: DecompressPointer r3
    //     0xb57ae0: add             x3, x3, HEAP, lsl #32
    // 0xb57ae4: ldur            x1, [fp, #-0x10]
    // 0xb57ae8: stur            x3, [fp, #-0x40]
    // 0xb57aec: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0xb57aec: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0xb57af0: r16 = true
    //     0xb57af0: add             x16, NULL, #0x20  ; true
    // 0xb57af4: str             x16, [SP]
    // 0xb57af8: mov             x1, x0
    // 0xb57afc: r4 = const [0, 0x2, 0x1, 0x1, filled, 0x1, null]
    //     0xb57afc: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a948] List(7) [0, 0x2, 0x1, 0x1, "filled", 0x1, Null]
    //     0xb57b00: ldr             x4, [x4, #0x948]
    // 0xb57b04: r0 = copyWith()
    //     0xb57b04: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xb57b08: ldur            x2, [fp, #-0x20]
    // 0xb57b0c: r1 = Function '<anonymous closure>':.
    //     0xb57b0c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a9f8] AnonymousClosure: (0xb57ca0), in [package:customer_app/app/presentation/views/glass/customization/customisation_text.dart] _CustomisationTextState::build (0xb574e0)
    //     0xb57b10: ldr             x1, [x1, #0x9f8]
    // 0xb57b14: stur            x0, [fp, #-8]
    // 0xb57b18: r0 = AllocateClosure()
    //     0xb57b18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb57b1c: r1 = <String>
    //     0xb57b1c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb57b20: stur            x0, [fp, #-0x10]
    // 0xb57b24: r0 = TextFormField()
    //     0xb57b24: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb57b28: stur            x0, [fp, #-0x20]
    // 0xb57b2c: r16 = false
    //     0xb57b2c: add             x16, NULL, #0x30  ; false
    // 0xb57b30: r30 = Instance_AutovalidateMode
    //     0xb57b30: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xb57b34: ldr             lr, [lr, #0x7e8]
    // 0xb57b38: stp             lr, x16, [SP, #0x30]
    // 0xb57b3c: ldur            x16, [fp, #-0x28]
    // 0xb57b40: r30 = Instance_TextInputType
    //     0xb57b40: add             lr, PP, #0x54, lsl #12  ; [pp+0x54068] Obj!TextInputType@d55ba1
    //     0xb57b44: ldr             lr, [lr, #0x68]
    // 0xb57b48: stp             lr, x16, [SP, #0x20]
    // 0xb57b4c: r16 = 2
    //     0xb57b4c: movz            x16, #0x2
    // 0xb57b50: ldur            lr, [fp, #-0x48]
    // 0xb57b54: stp             lr, x16, [SP, #0x10]
    // 0xb57b58: ldur            x16, [fp, #-0x40]
    // 0xb57b5c: ldur            lr, [fp, #-0x10]
    // 0xb57b60: stp             lr, x16, [SP]
    // 0xb57b64: mov             x1, x0
    // 0xb57b68: ldur            x2, [fp, #-8]
    // 0xb57b6c: r4 = const [0, 0xa, 0x8, 0x2, autovalidateMode, 0x3, controller, 0x8, enableSuggestions, 0x2, inputFormatters, 0x4, keyboardType, 0x5, maxLines, 0x6, onChanged, 0x9, style, 0x7, null]
    //     0xb57b6c: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a428] List(21) [0, 0xa, 0x8, 0x2, "autovalidateMode", 0x3, "controller", 0x8, "enableSuggestions", 0x2, "inputFormatters", 0x4, "keyboardType", 0x5, "maxLines", 0x6, "onChanged", 0x9, "style", 0x7, Null]
    //     0xb57b70: ldr             x4, [x4, #0x428]
    // 0xb57b74: r0 = TextFormField()
    //     0xb57b74: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb57b78: r0 = Form()
    //     0xb57b78: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xb57b7c: mov             x1, x0
    // 0xb57b80: ldur            x0, [fp, #-0x20]
    // 0xb57b84: stur            x1, [fp, #-8]
    // 0xb57b88: StoreField: r1->field_b = r0
    //     0xb57b88: stur            w0, [x1, #0xb]
    // 0xb57b8c: r0 = Instance_AutovalidateMode
    //     0xb57b8c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xb57b90: ldr             x0, [x0, #0x800]
    // 0xb57b94: StoreField: r1->field_23 = r0
    //     0xb57b94: stur            w0, [x1, #0x23]
    // 0xb57b98: ldur            x0, [fp, #-0x18]
    // 0xb57b9c: StoreField: r1->field_7 = r0
    //     0xb57b9c: stur            w0, [x1, #7]
    // 0xb57ba0: r0 = Padding()
    //     0xb57ba0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb57ba4: mov             x3, x0
    // 0xb57ba8: r0 = Instance_EdgeInsets
    //     0xb57ba8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb57bac: ldr             x0, [x0, #0x668]
    // 0xb57bb0: stur            x3, [fp, #-0x10]
    // 0xb57bb4: StoreField: r3->field_f = r0
    //     0xb57bb4: stur            w0, [x3, #0xf]
    // 0xb57bb8: ldur            x0, [fp, #-8]
    // 0xb57bbc: StoreField: r3->field_b = r0
    //     0xb57bbc: stur            w0, [x3, #0xb]
    // 0xb57bc0: r1 = Null
    //     0xb57bc0: mov             x1, NULL
    // 0xb57bc4: r2 = 6
    //     0xb57bc4: movz            x2, #0x6
    // 0xb57bc8: r0 = AllocateArray()
    //     0xb57bc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb57bcc: mov             x2, x0
    // 0xb57bd0: ldur            x0, [fp, #-0x30]
    // 0xb57bd4: stur            x2, [fp, #-8]
    // 0xb57bd8: StoreField: r2->field_f = r0
    //     0xb57bd8: stur            w0, [x2, #0xf]
    // 0xb57bdc: ldur            x0, [fp, #-0x38]
    // 0xb57be0: StoreField: r2->field_13 = r0
    //     0xb57be0: stur            w0, [x2, #0x13]
    // 0xb57be4: ldur            x0, [fp, #-0x10]
    // 0xb57be8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb57be8: stur            w0, [x2, #0x17]
    // 0xb57bec: r1 = <Widget>
    //     0xb57bec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb57bf0: r0 = AllocateGrowableArray()
    //     0xb57bf0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb57bf4: mov             x1, x0
    // 0xb57bf8: ldur            x0, [fp, #-8]
    // 0xb57bfc: stur            x1, [fp, #-0x10]
    // 0xb57c00: StoreField: r1->field_f = r0
    //     0xb57c00: stur            w0, [x1, #0xf]
    // 0xb57c04: r0 = 6
    //     0xb57c04: movz            x0, #0x6
    // 0xb57c08: StoreField: r1->field_b = r0
    //     0xb57c08: stur            w0, [x1, #0xb]
    // 0xb57c0c: r0 = Column()
    //     0xb57c0c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb57c10: r1 = Instance_Axis
    //     0xb57c10: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb57c14: StoreField: r0->field_f = r1
    //     0xb57c14: stur            w1, [x0, #0xf]
    // 0xb57c18: r1 = Instance_MainAxisAlignment
    //     0xb57c18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb57c1c: ldr             x1, [x1, #0xa08]
    // 0xb57c20: StoreField: r0->field_13 = r1
    //     0xb57c20: stur            w1, [x0, #0x13]
    // 0xb57c24: r1 = Instance_MainAxisSize
    //     0xb57c24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb57c28: ldr             x1, [x1, #0xa10]
    // 0xb57c2c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb57c2c: stur            w1, [x0, #0x17]
    // 0xb57c30: r1 = Instance_CrossAxisAlignment
    //     0xb57c30: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb57c34: ldr             x1, [x1, #0x890]
    // 0xb57c38: StoreField: r0->field_1b = r1
    //     0xb57c38: stur            w1, [x0, #0x1b]
    // 0xb57c3c: r1 = Instance_VerticalDirection
    //     0xb57c3c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb57c40: ldr             x1, [x1, #0xa20]
    // 0xb57c44: StoreField: r0->field_23 = r1
    //     0xb57c44: stur            w1, [x0, #0x23]
    // 0xb57c48: r1 = Instance_Clip
    //     0xb57c48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb57c4c: ldr             x1, [x1, #0x38]
    // 0xb57c50: StoreField: r0->field_2b = r1
    //     0xb57c50: stur            w1, [x0, #0x2b]
    // 0xb57c54: StoreField: r0->field_2f = rZR
    //     0xb57c54: stur            xzr, [x0, #0x2f]
    // 0xb57c58: ldur            x1, [fp, #-0x10]
    // 0xb57c5c: StoreField: r0->field_b = r1
    //     0xb57c5c: stur            w1, [x0, #0xb]
    // 0xb57c60: LeaveFrame
    //     0xb57c60: mov             SP, fp
    //     0xb57c64: ldp             fp, lr, [SP], #0x10
    // 0xb57c68: ret
    //     0xb57c68: ret             
    // 0xb57c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb57c6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb57c70: b               #0xb57508
    // 0xb57c74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57c74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb57c78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57c78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb57c7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb57c7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb57ca0, size: 0x5cc
    // 0xb57ca0: EnterFrame
    //     0xb57ca0: stp             fp, lr, [SP, #-0x10]!
    //     0xb57ca4: mov             fp, SP
    // 0xb57ca8: AllocStack(0x78)
    //     0xb57ca8: sub             SP, SP, #0x78
    // 0xb57cac: SetupParameters()
    //     0xb57cac: ldr             x0, [fp, #0x18]
    //     0xb57cb0: ldur            w3, [x0, #0x17]
    //     0xb57cb4: add             x3, x3, HEAP, lsl #32
    //     0xb57cb8: stur            x3, [fp, #-0x10]
    // 0xb57cbc: CheckStackOverflow
    //     0xb57cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb57cc0: cmp             SP, x16
    //     0xb57cc4: b.ls            #0xb58248
    // 0xb57cc8: LoadField: r0 = r3->field_f
    //     0xb57cc8: ldur            w0, [x3, #0xf]
    // 0xb57ccc: DecompressPointer r0
    //     0xb57ccc: add             x0, x0, HEAP, lsl #32
    // 0xb57cd0: LoadField: r1 = r0->field_b
    //     0xb57cd0: ldur            w1, [x0, #0xb]
    // 0xb57cd4: DecompressPointer r1
    //     0xb57cd4: add             x1, x1, HEAP, lsl #32
    // 0xb57cd8: cmp             w1, NULL
    // 0xb57cdc: b.eq            #0xb58250
    // 0xb57ce0: LoadField: r0 = r1->field_f
    //     0xb57ce0: ldur            w0, [x1, #0xf]
    // 0xb57ce4: DecompressPointer r0
    //     0xb57ce4: add             x0, x0, HEAP, lsl #32
    // 0xb57ce8: stur            x0, [fp, #-8]
    // 0xb57cec: LoadField: r1 = r0->field_b
    //     0xb57cec: ldur            w1, [x0, #0xb]
    // 0xb57cf0: cbz             w1, #0xb57da8
    // 0xb57cf4: mov             x2, x3
    // 0xb57cf8: r1 = Function '<anonymous closure>':.
    //     0xb57cf8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa00] AnonymousClosure: (0xa3afc0), in [package:customer_app/app/presentation/views/line/customization/customization_number.dart] _CustomisationNumberState::build (0xbd9810)
    //     0xb57cfc: ldr             x1, [x1, #0xa00]
    // 0xb57d00: r0 = AllocateClosure()
    //     0xb57d00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb57d04: r1 = Function '<anonymous closure>':.
    //     0xb57d04: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6aa08] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xb57d08: ldr             x1, [x1, #0xa08]
    // 0xb57d0c: r2 = Null
    //     0xb57d0c: mov             x2, NULL
    // 0xb57d10: stur            x0, [fp, #-0x18]
    // 0xb57d14: r0 = AllocateClosure()
    //     0xb57d14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb57d18: str             x0, [SP]
    // 0xb57d1c: ldur            x1, [fp, #-8]
    // 0xb57d20: ldur            x2, [fp, #-0x18]
    // 0xb57d24: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb57d24: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb57d28: ldr             x4, [x4, #0xb48]
    // 0xb57d2c: r0 = firstWhere()
    //     0xb57d2c: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xb57d30: LoadField: r1 = r0->field_b
    //     0xb57d30: ldur            w1, [x0, #0xb]
    // 0xb57d34: DecompressPointer r1
    //     0xb57d34: add             x1, x1, HEAP, lsl #32
    // 0xb57d38: cmp             w1, NULL
    // 0xb57d3c: b.eq            #0xb57da8
    // 0xb57d40: ldur            x1, [fp, #-0x10]
    // 0xb57d44: LoadField: r2 = r1->field_f
    //     0xb57d44: ldur            w2, [x1, #0xf]
    // 0xb57d48: DecompressPointer r2
    //     0xb57d48: add             x2, x2, HEAP, lsl #32
    // 0xb57d4c: LoadField: r3 = r2->field_b
    //     0xb57d4c: ldur            w3, [x2, #0xb]
    // 0xb57d50: DecompressPointer r3
    //     0xb57d50: add             x3, x3, HEAP, lsl #32
    // 0xb57d54: cmp             w3, NULL
    // 0xb57d58: b.eq            #0xb58254
    // 0xb57d5c: LoadField: r2 = r3->field_b
    //     0xb57d5c: ldur            w2, [x3, #0xb]
    // 0xb57d60: DecompressPointer r2
    //     0xb57d60: add             x2, x2, HEAP, lsl #32
    // 0xb57d64: cmp             w2, NULL
    // 0xb57d68: b.ne            #0xb57d74
    // 0xb57d6c: r2 = Null
    //     0xb57d6c: mov             x2, NULL
    // 0xb57d70: b               #0xb57d80
    // 0xb57d74: LoadField: r4 = r2->field_23
    //     0xb57d74: ldur            w4, [x2, #0x23]
    // 0xb57d78: DecompressPointer r4
    //     0xb57d78: add             x4, x4, HEAP, lsl #32
    // 0xb57d7c: mov             x2, x4
    // 0xb57d80: LoadField: r4 = r3->field_13
    //     0xb57d80: ldur            w4, [x3, #0x13]
    // 0xb57d84: DecompressPointer r4
    //     0xb57d84: add             x4, x4, HEAP, lsl #32
    // 0xb57d88: stp             x2, x4, [SP, #8]
    // 0xb57d8c: str             x0, [SP]
    // 0xb57d90: r4 = 0
    //     0xb57d90: movz            x4, #0
    // 0xb57d94: ldr             x0, [SP, #0x10]
    // 0xb57d98: r16 = UnlinkedCall_0x613b5c
    //     0xb57d98: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aa10] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb57d9c: add             x16, x16, #0xa10
    // 0xb57da0: ldp             x5, lr, [x16]
    // 0xb57da4: blr             lr
    // 0xb57da8: ldr             x0, [fp, #0x10]
    // 0xb57dac: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xb57dac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb57db0: ldr             x0, [x0]
    //     0xb57db4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb57db8: cmp             w0, w16
    //     0xb57dbc: b.ne            #0xb57dc8
    //     0xb57dc0: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xb57dc4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb57dc8: r1 = <CustomerResponse>
    //     0xb57dc8: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xb57dcc: ldr             x1, [x1, #0x5a8]
    // 0xb57dd0: stur            x0, [fp, #-8]
    // 0xb57dd4: r0 = AllocateGrowableArray()
    //     0xb57dd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb57dd8: mov             x2, x0
    // 0xb57ddc: ldur            x0, [fp, #-8]
    // 0xb57de0: stur            x2, [fp, #-0x18]
    // 0xb57de4: StoreField: r2->field_f = r0
    //     0xb57de4: stur            w0, [x2, #0xf]
    // 0xb57de8: StoreField: r2->field_b = rZR
    //     0xb57de8: stur            wzr, [x2, #0xb]
    // 0xb57dec: r1 = <ProductCustomisation>
    //     0xb57dec: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xb57df0: ldr             x1, [x1, #0x370]
    // 0xb57df4: r0 = AllocateGrowableArray()
    //     0xb57df4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb57df8: mov             x1, x0
    // 0xb57dfc: ldur            x0, [fp, #-8]
    // 0xb57e00: stur            x1, [fp, #-0x28]
    // 0xb57e04: StoreField: r1->field_f = r0
    //     0xb57e04: stur            w0, [x1, #0xf]
    // 0xb57e08: StoreField: r1->field_b = rZR
    //     0xb57e08: stur            wzr, [x1, #0xb]
    // 0xb57e0c: ldr             x2, [fp, #0x10]
    // 0xb57e10: cmp             w2, NULL
    // 0xb57e14: b.eq            #0xb58198
    // 0xb57e18: ldur            x3, [fp, #-0x10]
    // 0xb57e1c: LoadField: r4 = r3->field_f
    //     0xb57e1c: ldur            w4, [x3, #0xf]
    // 0xb57e20: DecompressPointer r4
    //     0xb57e20: add             x4, x4, HEAP, lsl #32
    // 0xb57e24: LoadField: r5 = r4->field_b
    //     0xb57e24: ldur            w5, [x4, #0xb]
    // 0xb57e28: DecompressPointer r5
    //     0xb57e28: add             x5, x5, HEAP, lsl #32
    // 0xb57e2c: cmp             w5, NULL
    // 0xb57e30: b.eq            #0xb58258
    // 0xb57e34: LoadField: r4 = r5->field_b
    //     0xb57e34: ldur            w4, [x5, #0xb]
    // 0xb57e38: DecompressPointer r4
    //     0xb57e38: add             x4, x4, HEAP, lsl #32
    // 0xb57e3c: cmp             w4, NULL
    // 0xb57e40: b.ne            #0xb57e4c
    // 0xb57e44: r4 = Null
    //     0xb57e44: mov             x4, NULL
    // 0xb57e48: b               #0xb57e58
    // 0xb57e4c: LoadField: r5 = r4->field_1b
    //     0xb57e4c: ldur            w5, [x4, #0x1b]
    // 0xb57e50: DecompressPointer r5
    //     0xb57e50: add             x5, x5, HEAP, lsl #32
    // 0xb57e54: mov             x4, x5
    // 0xb57e58: stur            x4, [fp, #-0x20]
    // 0xb57e5c: r0 = CustomerResponse()
    //     0xb57e5c: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xb57e60: mov             x2, x0
    // 0xb57e64: ldur            x0, [fp, #-0x20]
    // 0xb57e68: stur            x2, [fp, #-0x30]
    // 0xb57e6c: StoreField: r2->field_7 = r0
    //     0xb57e6c: stur            w0, [x2, #7]
    // 0xb57e70: ldr             x0, [fp, #0x10]
    // 0xb57e74: StoreField: r2->field_b = r0
    //     0xb57e74: stur            w0, [x2, #0xb]
    // 0xb57e78: ldur            x1, [fp, #-8]
    // 0xb57e7c: LoadField: r3 = r1->field_b
    //     0xb57e7c: ldur            w3, [x1, #0xb]
    // 0xb57e80: cbnz            w3, #0xb57e8c
    // 0xb57e84: ldur            x1, [fp, #-0x18]
    // 0xb57e88: r0 = _growToNextCapacity()
    //     0xb57e88: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb57e8c: ldur            x3, [fp, #-0x10]
    // 0xb57e90: ldur            x4, [fp, #-0x18]
    // 0xb57e94: r0 = 2
    //     0xb57e94: movz            x0, #0x2
    // 0xb57e98: StoreField: r4->field_b = r0
    //     0xb57e98: stur            w0, [x4, #0xb]
    // 0xb57e9c: LoadField: r1 = r4->field_f
    //     0xb57e9c: ldur            w1, [x4, #0xf]
    // 0xb57ea0: DecompressPointer r1
    //     0xb57ea0: add             x1, x1, HEAP, lsl #32
    // 0xb57ea4: ldur            x0, [fp, #-0x30]
    // 0xb57ea8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb57ea8: add             x25, x1, #0xf
    //     0xb57eac: str             w0, [x25]
    //     0xb57eb0: tbz             w0, #0, #0xb57ecc
    //     0xb57eb4: ldurb           w16, [x1, #-1]
    //     0xb57eb8: ldurb           w17, [x0, #-1]
    //     0xb57ebc: and             x16, x17, x16, lsr #2
    //     0xb57ec0: tst             x16, HEAP, lsr #32
    //     0xb57ec4: b.eq            #0xb57ecc
    //     0xb57ec8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb57ecc: LoadField: r0 = r3->field_f
    //     0xb57ecc: ldur            w0, [x3, #0xf]
    // 0xb57ed0: DecompressPointer r0
    //     0xb57ed0: add             x0, x0, HEAP, lsl #32
    // 0xb57ed4: LoadField: r1 = r0->field_b
    //     0xb57ed4: ldur            w1, [x0, #0xb]
    // 0xb57ed8: DecompressPointer r1
    //     0xb57ed8: add             x1, x1, HEAP, lsl #32
    // 0xb57edc: cmp             w1, NULL
    // 0xb57ee0: b.eq            #0xb5825c
    // 0xb57ee4: LoadField: r0 = r1->field_b
    //     0xb57ee4: ldur            w0, [x1, #0xb]
    // 0xb57ee8: DecompressPointer r0
    //     0xb57ee8: add             x0, x0, HEAP, lsl #32
    // 0xb57eec: cmp             w0, NULL
    // 0xb57ef0: b.ne            #0xb57efc
    // 0xb57ef4: r5 = Null
    //     0xb57ef4: mov             x5, NULL
    // 0xb57ef8: b               #0xb57f08
    // 0xb57efc: LoadField: r1 = r0->field_7
    //     0xb57efc: ldur            w1, [x0, #7]
    // 0xb57f00: DecompressPointer r1
    //     0xb57f00: add             x1, x1, HEAP, lsl #32
    // 0xb57f04: mov             x5, x1
    // 0xb57f08: stur            x5, [fp, #-0x38]
    // 0xb57f0c: cmp             w0, NULL
    // 0xb57f10: b.ne            #0xb57f1c
    // 0xb57f14: r6 = Null
    //     0xb57f14: mov             x6, NULL
    // 0xb57f18: b               #0xb57f28
    // 0xb57f1c: LoadField: r1 = r0->field_b
    //     0xb57f1c: ldur            w1, [x0, #0xb]
    // 0xb57f20: DecompressPointer r1
    //     0xb57f20: add             x1, x1, HEAP, lsl #32
    // 0xb57f24: mov             x6, x1
    // 0xb57f28: stur            x6, [fp, #-0x30]
    // 0xb57f2c: cmp             w0, NULL
    // 0xb57f30: b.ne            #0xb57f3c
    // 0xb57f34: r7 = Null
    //     0xb57f34: mov             x7, NULL
    // 0xb57f38: b               #0xb57f48
    // 0xb57f3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb57f3c: ldur            w1, [x0, #0x17]
    // 0xb57f40: DecompressPointer r1
    //     0xb57f40: add             x1, x1, HEAP, lsl #32
    // 0xb57f44: mov             x7, x1
    // 0xb57f48: stur            x7, [fp, #-0x20]
    // 0xb57f4c: cmp             w0, NULL
    // 0xb57f50: b.ne            #0xb57f5c
    // 0xb57f54: r0 = Null
    //     0xb57f54: mov             x0, NULL
    // 0xb57f58: b               #0xb57f68
    // 0xb57f5c: LoadField: r1 = r0->field_23
    //     0xb57f5c: ldur            w1, [x0, #0x23]
    // 0xb57f60: DecompressPointer r1
    //     0xb57f60: add             x1, x1, HEAP, lsl #32
    // 0xb57f64: mov             x0, x1
    // 0xb57f68: stur            x0, [fp, #-8]
    // 0xb57f6c: r1 = Null
    //     0xb57f6c: mov             x1, NULL
    // 0xb57f70: r2 = 4
    //     0xb57f70: movz            x2, #0x4
    // 0xb57f74: r0 = AllocateArray()
    //     0xb57f74: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb57f78: stur            x0, [fp, #-0x40]
    // 0xb57f7c: r16 = "₹"
    //     0xb57f7c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0xb57f80: ldr             x16, [x16, #0x360]
    // 0xb57f84: StoreField: r0->field_f = r16
    //     0xb57f84: stur            w16, [x0, #0xf]
    // 0xb57f88: r1 = Function '<anonymous closure>': static.
    //     0xb57f88: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0xb57f8c: ldr             x1, [x1, #0x1a0]
    // 0xb57f90: r2 = Null
    //     0xb57f90: mov             x2, NULL
    // 0xb57f94: r0 = AllocateClosure()
    //     0xb57f94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb57f98: mov             x3, x0
    // 0xb57f9c: r1 = Null
    //     0xb57f9c: mov             x1, NULL
    // 0xb57fa0: r2 = Null
    //     0xb57fa0: mov             x2, NULL
    // 0xb57fa4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb57fa4: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb57fa8: r0 = NumberFormat._forPattern()
    //     0xb57fa8: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xb57fac: mov             x1, x0
    // 0xb57fb0: ldur            x0, [fp, #-0x10]
    // 0xb57fb4: LoadField: r2 = r0->field_f
    //     0xb57fb4: ldur            w2, [x0, #0xf]
    // 0xb57fb8: DecompressPointer r2
    //     0xb57fb8: add             x2, x2, HEAP, lsl #32
    // 0xb57fbc: LoadField: r3 = r2->field_b
    //     0xb57fbc: ldur            w3, [x2, #0xb]
    // 0xb57fc0: DecompressPointer r3
    //     0xb57fc0: add             x3, x3, HEAP, lsl #32
    // 0xb57fc4: cmp             w3, NULL
    // 0xb57fc8: b.eq            #0xb58260
    // 0xb57fcc: LoadField: r2 = r3->field_b
    //     0xb57fcc: ldur            w2, [x3, #0xb]
    // 0xb57fd0: DecompressPointer r2
    //     0xb57fd0: add             x2, x2, HEAP, lsl #32
    // 0xb57fd4: cmp             w2, NULL
    // 0xb57fd8: b.ne            #0xb57fe4
    // 0xb57fdc: r2 = Null
    //     0xb57fdc: mov             x2, NULL
    // 0xb57fe0: b               #0xb57ff0
    // 0xb57fe4: LoadField: r3 = r2->field_23
    //     0xb57fe4: ldur            w3, [x2, #0x23]
    // 0xb57fe8: DecompressPointer r3
    //     0xb57fe8: add             x3, x3, HEAP, lsl #32
    // 0xb57fec: mov             x2, x3
    // 0xb57ff0: ldur            x4, [fp, #-0x38]
    // 0xb57ff4: ldur            x5, [fp, #-0x30]
    // 0xb57ff8: ldur            x6, [fp, #-0x20]
    // 0xb57ffc: ldur            x7, [fp, #-8]
    // 0xb58000: ldur            x3, [fp, #-0x18]
    // 0xb58004: ldur            x8, [fp, #-0x28]
    // 0xb58008: r0 = format()
    //     0xb58008: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xb5800c: ldur            x1, [fp, #-0x40]
    // 0xb58010: ArrayStore: r1[1] = r0  ; List_4
    //     0xb58010: add             x25, x1, #0x13
    //     0xb58014: str             w0, [x25]
    //     0xb58018: tbz             w0, #0, #0xb58034
    //     0xb5801c: ldurb           w16, [x1, #-1]
    //     0xb58020: ldurb           w17, [x0, #-1]
    //     0xb58024: and             x16, x17, x16, lsr #2
    //     0xb58028: tst             x16, HEAP, lsr #32
    //     0xb5802c: b.eq            #0xb58034
    //     0xb58030: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb58034: ldur            x16, [fp, #-0x40]
    // 0xb58038: str             x16, [SP]
    // 0xb5803c: r0 = _interpolate()
    //     0xb5803c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb58040: stur            x0, [fp, #-0x40]
    // 0xb58044: r0 = ProductCustomisation()
    //     0xb58044: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xb58048: mov             x2, x0
    // 0xb5804c: ldur            x0, [fp, #-0x38]
    // 0xb58050: stur            x2, [fp, #-0x48]
    // 0xb58054: StoreField: r2->field_b = r0
    //     0xb58054: stur            w0, [x2, #0xb]
    // 0xb58058: ldur            x0, [fp, #-0x30]
    // 0xb5805c: StoreField: r2->field_f = r0
    //     0xb5805c: stur            w0, [x2, #0xf]
    // 0xb58060: ldur            x0, [fp, #-0x20]
    // 0xb58064: ArrayStore: r2[0] = r0  ; List_4
    //     0xb58064: stur            w0, [x2, #0x17]
    // 0xb58068: ldur            x0, [fp, #-0x18]
    // 0xb5806c: StoreField: r2->field_23 = r0
    //     0xb5806c: stur            w0, [x2, #0x23]
    // 0xb58070: ldur            x0, [fp, #-8]
    // 0xb58074: StoreField: r2->field_27 = r0
    //     0xb58074: stur            w0, [x2, #0x27]
    // 0xb58078: ldur            x0, [fp, #-0x40]
    // 0xb5807c: StoreField: r2->field_2b = r0
    //     0xb5807c: stur            w0, [x2, #0x2b]
    // 0xb58080: ldur            x1, [fp, #-0x28]
    // 0xb58084: r0 = clear()
    //     0xb58084: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xb58088: ldur            x0, [fp, #-0x28]
    // 0xb5808c: LoadField: r1 = r0->field_b
    //     0xb5808c: ldur            w1, [x0, #0xb]
    // 0xb58090: LoadField: r2 = r0->field_f
    //     0xb58090: ldur            w2, [x0, #0xf]
    // 0xb58094: DecompressPointer r2
    //     0xb58094: add             x2, x2, HEAP, lsl #32
    // 0xb58098: LoadField: r3 = r2->field_b
    //     0xb58098: ldur            w3, [x2, #0xb]
    // 0xb5809c: r2 = LoadInt32Instr(r1)
    //     0xb5809c: sbfx            x2, x1, #1, #0x1f
    // 0xb580a0: stur            x2, [fp, #-0x50]
    // 0xb580a4: r1 = LoadInt32Instr(r3)
    //     0xb580a4: sbfx            x1, x3, #1, #0x1f
    // 0xb580a8: cmp             x2, x1
    // 0xb580ac: b.ne            #0xb580b8
    // 0xb580b0: mov             x1, x0
    // 0xb580b4: r0 = _growToNextCapacity()
    //     0xb580b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb580b8: ldur            x4, [fp, #-0x10]
    // 0xb580bc: ldur            x2, [fp, #-0x28]
    // 0xb580c0: ldur            x3, [fp, #-0x50]
    // 0xb580c4: add             x0, x3, #1
    // 0xb580c8: lsl             x1, x0, #1
    // 0xb580cc: StoreField: r2->field_b = r1
    //     0xb580cc: stur            w1, [x2, #0xb]
    // 0xb580d0: LoadField: r1 = r2->field_f
    //     0xb580d0: ldur            w1, [x2, #0xf]
    // 0xb580d4: DecompressPointer r1
    //     0xb580d4: add             x1, x1, HEAP, lsl #32
    // 0xb580d8: ldur            x0, [fp, #-0x48]
    // 0xb580dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb580dc: add             x25, x1, x3, lsl #2
    //     0xb580e0: add             x25, x25, #0xf
    //     0xb580e4: str             w0, [x25]
    //     0xb580e8: tbz             w0, #0, #0xb58104
    //     0xb580ec: ldurb           w16, [x1, #-1]
    //     0xb580f0: ldurb           w17, [x0, #-1]
    //     0xb580f4: and             x16, x17, x16, lsr #2
    //     0xb580f8: tst             x16, HEAP, lsr #32
    //     0xb580fc: b.eq            #0xb58104
    //     0xb58100: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb58104: LoadField: r0 = r4->field_f
    //     0xb58104: ldur            w0, [x4, #0xf]
    // 0xb58108: DecompressPointer r0
    //     0xb58108: add             x0, x0, HEAP, lsl #32
    // 0xb5810c: LoadField: r1 = r0->field_b
    //     0xb5810c: ldur            w1, [x0, #0xb]
    // 0xb58110: DecompressPointer r1
    //     0xb58110: add             x1, x1, HEAP, lsl #32
    // 0xb58114: cmp             w1, NULL
    // 0xb58118: b.eq            #0xb58264
    // 0xb5811c: LoadField: r0 = r1->field_b
    //     0xb5811c: ldur            w0, [x1, #0xb]
    // 0xb58120: DecompressPointer r0
    //     0xb58120: add             x0, x0, HEAP, lsl #32
    // 0xb58124: cmp             w0, NULL
    // 0xb58128: b.ne            #0xb58134
    // 0xb5812c: r3 = Null
    //     0xb5812c: mov             x3, NULL
    // 0xb58130: b               #0xb5813c
    // 0xb58134: LoadField: r3 = r0->field_23
    //     0xb58134: ldur            w3, [x0, #0x23]
    // 0xb58138: DecompressPointer r3
    //     0xb58138: add             x3, x3, HEAP, lsl #32
    // 0xb5813c: cmp             w0, NULL
    // 0xb58140: b.ne            #0xb5814c
    // 0xb58144: r0 = Null
    //     0xb58144: mov             x0, NULL
    // 0xb58148: b               #0xb58158
    // 0xb5814c: LoadField: r4 = r0->field_2b
    //     0xb5814c: ldur            w4, [x0, #0x2b]
    // 0xb58150: DecompressPointer r4
    //     0xb58150: add             x4, x4, HEAP, lsl #32
    // 0xb58154: mov             x0, x4
    // 0xb58158: cmp             w0, NULL
    // 0xb5815c: b.ne            #0xb58164
    // 0xb58160: r0 = false
    //     0xb58160: add             x0, NULL, #0x30  ; false
    // 0xb58164: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xb58164: ldur            w4, [x1, #0x17]
    // 0xb58168: DecompressPointer r4
    //     0xb58168: add             x4, x4, HEAP, lsl #32
    // 0xb5816c: ldr             x16, [fp, #0x10]
    // 0xb58170: stp             x16, x4, [SP, #0x18]
    // 0xb58174: stp             x2, x3, [SP, #8]
    // 0xb58178: str             x0, [SP]
    // 0xb5817c: r4 = 0
    //     0xb5817c: movz            x4, #0
    // 0xb58180: ldr             x0, [SP, #0x20]
    // 0xb58184: r16 = UnlinkedCall_0x613b5c
    //     0xb58184: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aa20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb58188: add             x16, x16, #0xa20
    // 0xb5818c: ldp             x5, lr, [x16]
    // 0xb58190: blr             lr
    // 0xb58194: b               #0xb58238
    // 0xb58198: ldur            x4, [fp, #-0x10]
    // 0xb5819c: mov             x2, x1
    // 0xb581a0: mov             x1, x2
    // 0xb581a4: r0 = clear()
    //     0xb581a4: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xb581a8: ldur            x0, [fp, #-0x10]
    // 0xb581ac: LoadField: r1 = r0->field_f
    //     0xb581ac: ldur            w1, [x0, #0xf]
    // 0xb581b0: DecompressPointer r1
    //     0xb581b0: add             x1, x1, HEAP, lsl #32
    // 0xb581b4: LoadField: r0 = r1->field_b
    //     0xb581b4: ldur            w0, [x1, #0xb]
    // 0xb581b8: DecompressPointer r0
    //     0xb581b8: add             x0, x0, HEAP, lsl #32
    // 0xb581bc: cmp             w0, NULL
    // 0xb581c0: b.eq            #0xb58268
    // 0xb581c4: LoadField: r1 = r0->field_b
    //     0xb581c4: ldur            w1, [x0, #0xb]
    // 0xb581c8: DecompressPointer r1
    //     0xb581c8: add             x1, x1, HEAP, lsl #32
    // 0xb581cc: cmp             w1, NULL
    // 0xb581d0: b.ne            #0xb581dc
    // 0xb581d4: r2 = Null
    //     0xb581d4: mov             x2, NULL
    // 0xb581d8: b               #0xb581e4
    // 0xb581dc: LoadField: r2 = r1->field_23
    //     0xb581dc: ldur            w2, [x1, #0x23]
    // 0xb581e0: DecompressPointer r2
    //     0xb581e0: add             x2, x2, HEAP, lsl #32
    // 0xb581e4: cmp             w1, NULL
    // 0xb581e8: b.ne            #0xb581f4
    // 0xb581ec: r1 = Null
    //     0xb581ec: mov             x1, NULL
    // 0xb581f0: b               #0xb58200
    // 0xb581f4: LoadField: r3 = r1->field_2b
    //     0xb581f4: ldur            w3, [x1, #0x2b]
    // 0xb581f8: DecompressPointer r3
    //     0xb581f8: add             x3, x3, HEAP, lsl #32
    // 0xb581fc: mov             x1, x3
    // 0xb58200: cmp             w1, NULL
    // 0xb58204: b.ne            #0xb5820c
    // 0xb58208: r1 = false
    //     0xb58208: add             x1, NULL, #0x30  ; false
    // 0xb5820c: LoadField: r3 = r0->field_1b
    //     0xb5820c: ldur            w3, [x0, #0x1b]
    // 0xb58210: DecompressPointer r3
    //     0xb58210: add             x3, x3, HEAP, lsl #32
    // 0xb58214: stp             x2, x3, [SP, #0x10]
    // 0xb58218: ldur            x16, [fp, #-0x28]
    // 0xb5821c: stp             x1, x16, [SP]
    // 0xb58220: r4 = 0
    //     0xb58220: movz            x4, #0
    // 0xb58224: ldr             x0, [SP, #0x18]
    // 0xb58228: r16 = UnlinkedCall_0x613b5c
    //     0xb58228: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6aa30] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5822c: add             x16, x16, #0xa30
    // 0xb58230: ldp             x5, lr, [x16]
    // 0xb58234: blr             lr
    // 0xb58238: r0 = Null
    //     0xb58238: mov             x0, NULL
    // 0xb5823c: LeaveFrame
    //     0xb5823c: mov             SP, fp
    //     0xb58240: ldp             fp, lr, [SP], #0x10
    // 0xb58244: ret
    //     0xb58244: ret             
    // 0xb58248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb58248: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5824c: b               #0xb57cc8
    // 0xb58250: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58250: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb58254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb58258: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58258: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5825c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5825c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb58260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58260: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb58264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58264: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb58268: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb58268: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4088, size: 0x20, field offset: 0xc
//   const constructor, 
class CustomisationText extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f04c, size: 0x48
    // 0xc7f04c: EnterFrame
    //     0xc7f04c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f050: mov             fp, SP
    // 0xc7f054: AllocStack(0x8)
    //     0xc7f054: sub             SP, SP, #8
    // 0xc7f058: CheckStackOverflow
    //     0xc7f058: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7f05c: cmp             SP, x16
    //     0xc7f060: b.ls            #0xc7f08c
    // 0xc7f064: r1 = <CustomisationText>
    //     0xc7f064: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e28] TypeArguments: <CustomisationText>
    //     0xc7f068: ldr             x1, [x1, #0xe28]
    // 0xc7f06c: r0 = _CustomisationTextState()
    //     0xc7f06c: bl              #0xc7f094  ; Allocate_CustomisationTextStateStub -> _CustomisationTextState (size=0x1c)
    // 0xc7f070: mov             x1, x0
    // 0xc7f074: stur            x0, [fp, #-8]
    // 0xc7f078: r0 = _CustomisationTextState()
    //     0xc7f078: bl              #0xc7b760  ; [package:customer_app/app/presentation/views/basic/customization/customisation_text.dart] _CustomisationTextState::_CustomisationTextState
    // 0xc7f07c: ldur            x0, [fp, #-8]
    // 0xc7f080: LeaveFrame
    //     0xc7f080: mov             SP, fp
    //     0xc7f084: ldp             fp, lr, [SP], #0x10
    // 0xc7f088: ret
    //     0xc7f088: ret             
    // 0xc7f08c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7f08c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7f090: b               #0xc7f064
  }
}
