// lib: , url: package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart

// class id: 1049337, size: 0x8
class :: {
}

// class id: 3386, size: 0x24, field offset: 0x14
class _SearchViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb275fc, size: 0x11bc
    // 0xb275fc: EnterFrame
    //     0xb275fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb27600: mov             fp, SP
    // 0xb27604: AllocStack(0x90)
    //     0xb27604: sub             SP, SP, #0x90
    // 0xb27608: SetupParameters(_SearchViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb27608: mov             x0, x1
    //     0xb2760c: stur            x1, [fp, #-8]
    //     0xb27610: mov             x1, x2
    //     0xb27614: stur            x2, [fp, #-0x10]
    // 0xb27618: CheckStackOverflow
    //     0xb27618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2761c: cmp             SP, x16
    //     0xb27620: b.ls            #0xb28794
    // 0xb27624: r1 = 2
    //     0xb27624: movz            x1, #0x2
    // 0xb27628: r0 = AllocateContext()
    //     0xb27628: bl              #0x16f6108  ; AllocateContextStub
    // 0xb2762c: mov             x2, x0
    // 0xb27630: ldur            x0, [fp, #-8]
    // 0xb27634: stur            x2, [fp, #-0x18]
    // 0xb27638: StoreField: r2->field_f = r0
    //     0xb27638: stur            w0, [x2, #0xf]
    // 0xb2763c: ldur            x1, [fp, #-0x10]
    // 0xb27640: StoreField: r2->field_13 = r1
    //     0xb27640: stur            w1, [x2, #0x13]
    // 0xb27644: r0 = of()
    //     0xb27644: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb27648: LoadField: r1 = r0->field_87
    //     0xb27648: ldur            w1, [x0, #0x87]
    // 0xb2764c: DecompressPointer r1
    //     0xb2764c: add             x1, x1, HEAP, lsl #32
    // 0xb27650: LoadField: r0 = r1->field_2b
    //     0xb27650: ldur            w0, [x1, #0x2b]
    // 0xb27654: DecompressPointer r0
    //     0xb27654: add             x0, x0, HEAP, lsl #32
    // 0xb27658: stur            x0, [fp, #-0x10]
    // 0xb2765c: r1 = Instance_Color
    //     0xb2765c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb27660: d0 = 0.700000
    //     0xb27660: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb27664: ldr             d0, [x17, #0xf48]
    // 0xb27668: r0 = withOpacity()
    //     0xb27668: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb2766c: r16 = 16.000000
    //     0xb2766c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb27670: ldr             x16, [x16, #0x188]
    // 0xb27674: stp             x0, x16, [SP]
    // 0xb27678: ldur            x1, [fp, #-0x10]
    // 0xb2767c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2767c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb27680: ldr             x4, [x4, #0xaa0]
    // 0xb27684: r0 = copyWith()
    //     0xb27684: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb27688: mov             x2, x0
    // 0xb2768c: ldur            x0, [fp, #-8]
    // 0xb27690: stur            x2, [fp, #-0x20]
    // 0xb27694: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb27694: ldur            w3, [x0, #0x17]
    // 0xb27698: DecompressPointer r3
    //     0xb27698: add             x3, x3, HEAP, lsl #32
    // 0xb2769c: ldur            x4, [fp, #-0x18]
    // 0xb276a0: stur            x3, [fp, #-0x10]
    // 0xb276a4: LoadField: r1 = r4->field_13
    //     0xb276a4: ldur            w1, [x4, #0x13]
    // 0xb276a8: DecompressPointer r1
    //     0xb276a8: add             x1, x1, HEAP, lsl #32
    // 0xb276ac: r0 = of()
    //     0xb276ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb276b0: LoadField: r1 = r0->field_87
    //     0xb276b0: ldur            w1, [x0, #0x87]
    // 0xb276b4: DecompressPointer r1
    //     0xb276b4: add             x1, x1, HEAP, lsl #32
    // 0xb276b8: LoadField: r0 = r1->field_2b
    //     0xb276b8: ldur            w0, [x1, #0x2b]
    // 0xb276bc: DecompressPointer r0
    //     0xb276bc: add             x0, x0, HEAP, lsl #32
    // 0xb276c0: stur            x0, [fp, #-0x28]
    // 0xb276c4: r1 = Instance_Color
    //     0xb276c4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb276c8: d0 = 0.400000
    //     0xb276c8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb276cc: r0 = withOpacity()
    //     0xb276cc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb276d0: r16 = 16.000000
    //     0xb276d0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb276d4: ldr             x16, [x16, #0x188]
    // 0xb276d8: stp             x16, x0, [SP]
    // 0xb276dc: ldur            x1, [fp, #-0x28]
    // 0xb276e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb276e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb276e4: ldr             x4, [x4, #0x9b8]
    // 0xb276e8: r0 = copyWith()
    //     0xb276e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb276ec: ldur            x2, [fp, #-0x18]
    // 0xb276f0: stur            x0, [fp, #-0x28]
    // 0xb276f4: LoadField: r1 = r2->field_13
    //     0xb276f4: ldur            w1, [x2, #0x13]
    // 0xb276f8: DecompressPointer r1
    //     0xb276f8: add             x1, x1, HEAP, lsl #32
    // 0xb276fc: r0 = of()
    //     0xb276fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb27700: LoadField: r1 = r0->field_5b
    //     0xb27700: ldur            w1, [x0, #0x5b]
    // 0xb27704: DecompressPointer r1
    //     0xb27704: add             x1, x1, HEAP, lsl #32
    // 0xb27708: stur            x1, [fp, #-0x30]
    // 0xb2770c: r0 = ColorFilter()
    //     0xb2770c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb27710: mov             x1, x0
    // 0xb27714: ldur            x0, [fp, #-0x30]
    // 0xb27718: stur            x1, [fp, #-0x38]
    // 0xb2771c: StoreField: r1->field_7 = r0
    //     0xb2771c: stur            w0, [x1, #7]
    // 0xb27720: r0 = Instance_BlendMode
    //     0xb27720: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb27724: ldr             x0, [x0, #0xb30]
    // 0xb27728: StoreField: r1->field_b = r0
    //     0xb27728: stur            w0, [x1, #0xb]
    // 0xb2772c: r2 = 1
    //     0xb2772c: movz            x2, #0x1
    // 0xb27730: StoreField: r1->field_13 = r2
    //     0xb27730: stur            x2, [x1, #0x13]
    // 0xb27734: r0 = SvgPicture()
    //     0xb27734: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb27738: stur            x0, [fp, #-0x30]
    // 0xb2773c: ldur            x16, [fp, #-0x38]
    // 0xb27740: str             x16, [SP]
    // 0xb27744: mov             x1, x0
    // 0xb27748: r2 = "assets/images/search.svg"
    //     0xb27748: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0xb2774c: ldr             x2, [x2, #0xa30]
    // 0xb27750: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb27750: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb27754: ldr             x4, [x4, #0xa38]
    // 0xb27758: r0 = SvgPicture.asset()
    //     0xb27758: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb2775c: r0 = Align()
    //     0xb2775c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb27760: mov             x1, x0
    // 0xb27764: r0 = Instance_Alignment
    //     0xb27764: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb27768: ldr             x0, [x0, #0xb10]
    // 0xb2776c: stur            x1, [fp, #-0x38]
    // 0xb27770: StoreField: r1->field_f = r0
    //     0xb27770: stur            w0, [x1, #0xf]
    // 0xb27774: r2 = 1.000000
    //     0xb27774: ldr             x2, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb27778: StoreField: r1->field_13 = r2
    //     0xb27778: stur            w2, [x1, #0x13]
    // 0xb2777c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb2777c: stur            w2, [x1, #0x17]
    // 0xb27780: ldur            x3, [fp, #-0x30]
    // 0xb27784: StoreField: r1->field_b = r3
    //     0xb27784: stur            w3, [x1, #0xb]
    // 0xb27788: r0 = Align()
    //     0xb27788: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb2778c: mov             x1, x0
    // 0xb27790: r0 = Instance_Alignment
    //     0xb27790: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb27794: ldr             x0, [x0, #0xb10]
    // 0xb27798: stur            x1, [fp, #-0x30]
    // 0xb2779c: StoreField: r1->field_f = r0
    //     0xb2779c: stur            w0, [x1, #0xf]
    // 0xb277a0: r0 = 1.000000
    //     0xb277a0: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb277a4: StoreField: r1->field_13 = r0
    //     0xb277a4: stur            w0, [x1, #0x13]
    // 0xb277a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb277a8: stur            w0, [x1, #0x17]
    // 0xb277ac: ldur            x2, [fp, #-0x38]
    // 0xb277b0: StoreField: r1->field_b = r2
    //     0xb277b0: stur            w2, [x1, #0xb]
    // 0xb277b4: r0 = InputDecoration()
    //     0xb277b4: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0xb277b8: mov             x3, x0
    // 0xb277bc: r0 = "Search"
    //     0xb277bc: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c08] "Search"
    //     0xb277c0: ldr             x0, [x0, #0xc08]
    // 0xb277c4: stur            x3, [fp, #-0x38]
    // 0xb277c8: StoreField: r3->field_2f = r0
    //     0xb277c8: stur            w0, [x3, #0x2f]
    // 0xb277cc: ldur            x0, [fp, #-0x28]
    // 0xb277d0: StoreField: r3->field_37 = r0
    //     0xb277d0: stur            w0, [x3, #0x37]
    // 0xb277d4: r0 = true
    //     0xb277d4: add             x0, NULL, #0x20  ; true
    // 0xb277d8: StoreField: r3->field_47 = r0
    //     0xb277d8: stur            w0, [x3, #0x47]
    // 0xb277dc: StoreField: r3->field_4b = r0
    //     0xb277dc: stur            w0, [x3, #0x4b]
    // 0xb277e0: ldur            x1, [fp, #-0x30]
    // 0xb277e4: StoreField: r3->field_73 = r1
    //     0xb277e4: stur            w1, [x3, #0x73]
    // 0xb277e8: r1 = Instance__NoInputBorder
    //     0xb277e8: add             x1, PP, #0x33, lsl #12  ; [pp+0x33ee8] Obj!_NoInputBorder@d5ad71
    //     0xb277ec: ldr             x1, [x1, #0xee8]
    // 0xb277f0: StoreField: r3->field_d3 = r1
    //     0xb277f0: stur            w1, [x3, #0xd3]
    // 0xb277f4: StoreField: r3->field_d7 = r0
    //     0xb277f4: stur            w0, [x3, #0xd7]
    // 0xb277f8: ldur            x2, [fp, #-0x18]
    // 0xb277fc: r1 = Function '<anonymous closure>':.
    //     0xb277fc: add             x1, PP, #0x57, lsl #12  ; [pp+0x573d0] AnonymousClosure: (0xb29510), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb27800: ldr             x1, [x1, #0x3d0]
    // 0xb27804: r0 = AllocateClosure()
    //     0xb27804: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb27808: ldur            x2, [fp, #-0x18]
    // 0xb2780c: r1 = Function '<anonymous closure>':.
    //     0xb2780c: add             x1, PP, #0x57, lsl #12  ; [pp+0x573d8] AnonymousClosure: (0xb292f0), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb27810: ldr             x1, [x1, #0x3d8]
    // 0xb27814: stur            x0, [fp, #-0x28]
    // 0xb27818: r0 = AllocateClosure()
    //     0xb27818: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2781c: r1 = <String>
    //     0xb2781c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb27820: stur            x0, [fp, #-0x30]
    // 0xb27824: r0 = TextFormField()
    //     0xb27824: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xb27828: stur            x0, [fp, #-0x40]
    // 0xb2782c: r16 = true
    //     0xb2782c: add             x16, NULL, #0x20  ; true
    // 0xb27830: ldur            lr, [fp, #-0x20]
    // 0xb27834: stp             lr, x16, [SP, #0x20]
    // 0xb27838: ldur            x16, [fp, #-0x10]
    // 0xb2783c: r30 = Instance_TextInputAction
    //     0xb2783c: ldr             lr, [PP, #0x7460]  ; [pp+0x7460] Obj!TextInputAction@d72a01
    // 0xb27840: stp             lr, x16, [SP, #0x10]
    // 0xb27844: ldur            x16, [fp, #-0x28]
    // 0xb27848: ldur            lr, [fp, #-0x30]
    // 0xb2784c: stp             lr, x16, [SP]
    // 0xb27850: mov             x1, x0
    // 0xb27854: ldur            x2, [fp, #-0x38]
    // 0xb27858: r4 = const [0, 0x8, 0x6, 0x2, autofocus, 0x2, controller, 0x4, onChanged, 0x7, onFieldSubmitted, 0x6, style, 0x3, textInputAction, 0x5, null]
    //     0xb27858: add             x4, PP, #0x51, lsl #12  ; [pp+0x51c20] List(17) [0, 0x8, 0x6, 0x2, "autofocus", 0x2, "controller", 0x4, "onChanged", 0x7, "onFieldSubmitted", 0x6, "style", 0x3, "textInputAction", 0x5, Null]
    //     0xb2785c: ldr             x4, [x4, #0xc20]
    // 0xb27860: r0 = TextFormField()
    //     0xb27860: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xb27864: r0 = Padding()
    //     0xb27864: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb27868: mov             x2, x0
    // 0xb2786c: r0 = Instance_EdgeInsets
    //     0xb2786c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb27870: ldr             x0, [x0, #0xa78]
    // 0xb27874: stur            x2, [fp, #-0x20]
    // 0xb27878: StoreField: r2->field_f = r0
    //     0xb27878: stur            w0, [x2, #0xf]
    // 0xb2787c: ldur            x0, [fp, #-0x40]
    // 0xb27880: StoreField: r2->field_b = r0
    //     0xb27880: stur            w0, [x2, #0xb]
    // 0xb27884: r1 = <FlexParentData>
    //     0xb27884: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb27888: ldr             x1, [x1, #0xe00]
    // 0xb2788c: r0 = Expanded()
    //     0xb2788c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb27890: mov             x2, x0
    // 0xb27894: r0 = 1
    //     0xb27894: movz            x0, #0x1
    // 0xb27898: stur            x2, [fp, #-0x28]
    // 0xb2789c: StoreField: r2->field_13 = r0
    //     0xb2789c: stur            x0, [x2, #0x13]
    // 0xb278a0: r3 = Instance_FlexFit
    //     0xb278a0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb278a4: ldr             x3, [x3, #0xe08]
    // 0xb278a8: StoreField: r2->field_1b = r3
    //     0xb278a8: stur            w3, [x2, #0x1b]
    // 0xb278ac: ldur            x1, [fp, #-0x20]
    // 0xb278b0: StoreField: r2->field_b = r1
    //     0xb278b0: stur            w1, [x2, #0xb]
    // 0xb278b4: ldur            x4, [fp, #-8]
    // 0xb278b8: LoadField: r5 = r4->field_13
    //     0xb278b8: ldur            w5, [x4, #0x13]
    // 0xb278bc: DecompressPointer r5
    //     0xb278bc: add             x5, x5, HEAP, lsl #32
    // 0xb278c0: ldur            x6, [fp, #-0x18]
    // 0xb278c4: stur            x5, [fp, #-0x20]
    // 0xb278c8: LoadField: r1 = r6->field_13
    //     0xb278c8: ldur            w1, [x6, #0x13]
    // 0xb278cc: DecompressPointer r1
    //     0xb278cc: add             x1, x1, HEAP, lsl #32
    // 0xb278d0: r0 = of()
    //     0xb278d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb278d4: LoadField: r1 = r0->field_5b
    //     0xb278d4: ldur            w1, [x0, #0x5b]
    // 0xb278d8: DecompressPointer r1
    //     0xb278d8: add             x1, x1, HEAP, lsl #32
    // 0xb278dc: stur            x1, [fp, #-0x30]
    // 0xb278e0: r0 = ColorFilter()
    //     0xb278e0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb278e4: mov             x1, x0
    // 0xb278e8: ldur            x0, [fp, #-0x30]
    // 0xb278ec: stur            x1, [fp, #-0x38]
    // 0xb278f0: StoreField: r1->field_7 = r0
    //     0xb278f0: stur            w0, [x1, #7]
    // 0xb278f4: r0 = Instance_BlendMode
    //     0xb278f4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb278f8: ldr             x0, [x0, #0xb30]
    // 0xb278fc: StoreField: r1->field_b = r0
    //     0xb278fc: stur            w0, [x1, #0xb]
    // 0xb27900: r2 = 1
    //     0xb27900: movz            x2, #0x1
    // 0xb27904: StoreField: r1->field_13 = r2
    //     0xb27904: stur            x2, [x1, #0x13]
    // 0xb27908: r0 = SvgPicture()
    //     0xb27908: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb2790c: stur            x0, [fp, #-0x30]
    // 0xb27910: ldur            x16, [fp, #-0x38]
    // 0xb27914: str             x16, [SP]
    // 0xb27918: mov             x1, x0
    // 0xb2791c: r2 = "assets/images/x.svg"
    //     0xb2791c: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xb27920: ldr             x2, [x2, #0x5e8]
    // 0xb27924: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb27924: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb27928: ldr             x4, [x4, #0xa38]
    // 0xb2792c: r0 = SvgPicture.asset()
    //     0xb2792c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb27930: r0 = InkWell()
    //     0xb27930: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb27934: mov             x3, x0
    // 0xb27938: ldur            x0, [fp, #-0x30]
    // 0xb2793c: stur            x3, [fp, #-0x38]
    // 0xb27940: StoreField: r3->field_b = r0
    //     0xb27940: stur            w0, [x3, #0xb]
    // 0xb27944: ldur            x2, [fp, #-0x18]
    // 0xb27948: r1 = Function '<anonymous closure>':.
    //     0xb27948: add             x1, PP, #0x57, lsl #12  ; [pp+0x573e0] AnonymousClosure: (0xb29248), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb2794c: ldr             x1, [x1, #0x3e0]
    // 0xb27950: r0 = AllocateClosure()
    //     0xb27950: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb27954: mov             x1, x0
    // 0xb27958: ldur            x0, [fp, #-0x38]
    // 0xb2795c: StoreField: r0->field_f = r1
    //     0xb2795c: stur            w1, [x0, #0xf]
    // 0xb27960: r1 = true
    //     0xb27960: add             x1, NULL, #0x20  ; true
    // 0xb27964: StoreField: r0->field_43 = r1
    //     0xb27964: stur            w1, [x0, #0x43]
    // 0xb27968: r2 = Instance_BoxShape
    //     0xb27968: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb2796c: ldr             x2, [x2, #0x80]
    // 0xb27970: StoreField: r0->field_47 = r2
    //     0xb27970: stur            w2, [x0, #0x47]
    // 0xb27974: StoreField: r0->field_6f = r1
    //     0xb27974: stur            w1, [x0, #0x6f]
    // 0xb27978: r2 = false
    //     0xb27978: add             x2, NULL, #0x30  ; false
    // 0xb2797c: StoreField: r0->field_73 = r2
    //     0xb2797c: stur            w2, [x0, #0x73]
    // 0xb27980: StoreField: r0->field_83 = r1
    //     0xb27980: stur            w1, [x0, #0x83]
    // 0xb27984: StoreField: r0->field_7b = r2
    //     0xb27984: stur            w2, [x0, #0x7b]
    // 0xb27988: r0 = Visibility()
    //     0xb27988: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb2798c: mov             x1, x0
    // 0xb27990: ldur            x0, [fp, #-0x38]
    // 0xb27994: stur            x1, [fp, #-0x30]
    // 0xb27998: StoreField: r1->field_b = r0
    //     0xb27998: stur            w0, [x1, #0xb]
    // 0xb2799c: r0 = Instance_SizedBox
    //     0xb2799c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb279a0: StoreField: r1->field_f = r0
    //     0xb279a0: stur            w0, [x1, #0xf]
    // 0xb279a4: ldur            x2, [fp, #-0x20]
    // 0xb279a8: StoreField: r1->field_13 = r2
    //     0xb279a8: stur            w2, [x1, #0x13]
    // 0xb279ac: r2 = false
    //     0xb279ac: add             x2, NULL, #0x30  ; false
    // 0xb279b0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb279b0: stur            w2, [x1, #0x17]
    // 0xb279b4: StoreField: r1->field_1b = r2
    //     0xb279b4: stur            w2, [x1, #0x1b]
    // 0xb279b8: StoreField: r1->field_1f = r2
    //     0xb279b8: stur            w2, [x1, #0x1f]
    // 0xb279bc: StoreField: r1->field_23 = r2
    //     0xb279bc: stur            w2, [x1, #0x23]
    // 0xb279c0: StoreField: r1->field_27 = r2
    //     0xb279c0: stur            w2, [x1, #0x27]
    // 0xb279c4: StoreField: r1->field_2b = r2
    //     0xb279c4: stur            w2, [x1, #0x2b]
    // 0xb279c8: r0 = Padding()
    //     0xb279c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb279cc: mov             x3, x0
    // 0xb279d0: r0 = Instance_EdgeInsets
    //     0xb279d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb279d4: ldr             x0, [x0, #0xd48]
    // 0xb279d8: stur            x3, [fp, #-0x20]
    // 0xb279dc: StoreField: r3->field_f = r0
    //     0xb279dc: stur            w0, [x3, #0xf]
    // 0xb279e0: ldur            x1, [fp, #-0x30]
    // 0xb279e4: StoreField: r3->field_b = r1
    //     0xb279e4: stur            w1, [x3, #0xb]
    // 0xb279e8: r1 = Null
    //     0xb279e8: mov             x1, NULL
    // 0xb279ec: r2 = 4
    //     0xb279ec: movz            x2, #0x4
    // 0xb279f0: r0 = AllocateArray()
    //     0xb279f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb279f4: mov             x2, x0
    // 0xb279f8: ldur            x0, [fp, #-0x28]
    // 0xb279fc: stur            x2, [fp, #-0x30]
    // 0xb27a00: StoreField: r2->field_f = r0
    //     0xb27a00: stur            w0, [x2, #0xf]
    // 0xb27a04: ldur            x0, [fp, #-0x20]
    // 0xb27a08: StoreField: r2->field_13 = r0
    //     0xb27a08: stur            w0, [x2, #0x13]
    // 0xb27a0c: r1 = <Widget>
    //     0xb27a0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb27a10: r0 = AllocateGrowableArray()
    //     0xb27a10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb27a14: mov             x1, x0
    // 0xb27a18: ldur            x0, [fp, #-0x30]
    // 0xb27a1c: stur            x1, [fp, #-0x20]
    // 0xb27a20: StoreField: r1->field_f = r0
    //     0xb27a20: stur            w0, [x1, #0xf]
    // 0xb27a24: r2 = 4
    //     0xb27a24: movz            x2, #0x4
    // 0xb27a28: StoreField: r1->field_b = r2
    //     0xb27a28: stur            w2, [x1, #0xb]
    // 0xb27a2c: r0 = Row()
    //     0xb27a2c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb27a30: mov             x3, x0
    // 0xb27a34: r0 = Instance_Axis
    //     0xb27a34: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb27a38: stur            x3, [fp, #-0x28]
    // 0xb27a3c: StoreField: r3->field_f = r0
    //     0xb27a3c: stur            w0, [x3, #0xf]
    // 0xb27a40: r4 = Instance_MainAxisAlignment
    //     0xb27a40: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb27a44: ldr             x4, [x4, #0xa08]
    // 0xb27a48: StoreField: r3->field_13 = r4
    //     0xb27a48: stur            w4, [x3, #0x13]
    // 0xb27a4c: r5 = Instance_MainAxisSize
    //     0xb27a4c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb27a50: ldr             x5, [x5, #0xa10]
    // 0xb27a54: ArrayStore: r3[0] = r5  ; List_4
    //     0xb27a54: stur            w5, [x3, #0x17]
    // 0xb27a58: r1 = Instance_CrossAxisAlignment
    //     0xb27a58: add             x1, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb27a5c: ldr             x1, [x1, #0xc68]
    // 0xb27a60: StoreField: r3->field_1b = r1
    //     0xb27a60: stur            w1, [x3, #0x1b]
    // 0xb27a64: r6 = Instance_VerticalDirection
    //     0xb27a64: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb27a68: ldr             x6, [x6, #0xa20]
    // 0xb27a6c: StoreField: r3->field_23 = r6
    //     0xb27a6c: stur            w6, [x3, #0x23]
    // 0xb27a70: r7 = Instance_Clip
    //     0xb27a70: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb27a74: ldr             x7, [x7, #0x38]
    // 0xb27a78: StoreField: r3->field_2b = r7
    //     0xb27a78: stur            w7, [x3, #0x2b]
    // 0xb27a7c: StoreField: r3->field_2f = rZR
    //     0xb27a7c: stur            xzr, [x3, #0x2f]
    // 0xb27a80: ldur            x1, [fp, #-0x20]
    // 0xb27a84: StoreField: r3->field_b = r1
    //     0xb27a84: stur            w1, [x3, #0xb]
    // 0xb27a88: ldur            x8, [fp, #-8]
    // 0xb27a8c: LoadField: r1 = r8->field_b
    //     0xb27a8c: ldur            w1, [x8, #0xb]
    // 0xb27a90: DecompressPointer r1
    //     0xb27a90: add             x1, x1, HEAP, lsl #32
    // 0xb27a94: cmp             w1, NULL
    // 0xb27a98: b.eq            #0xb2879c
    // 0xb27a9c: LoadField: r2 = r1->field_b
    //     0xb27a9c: ldur            w2, [x1, #0xb]
    // 0xb27aa0: DecompressPointer r2
    //     0xb27aa0: add             x2, x2, HEAP, lsl #32
    // 0xb27aa4: LoadField: r9 = r2->field_f
    //     0xb27aa4: ldur            w9, [x2, #0xf]
    // 0xb27aa8: DecompressPointer r9
    //     0xb27aa8: add             x9, x9, HEAP, lsl #32
    // 0xb27aac: cmp             w9, NULL
    // 0xb27ab0: b.eq            #0xb27ad8
    // 0xb27ab4: LoadField: r2 = r1->field_3f
    //     0xb27ab4: ldur            w2, [x1, #0x3f]
    // 0xb27ab8: DecompressPointer r2
    //     0xb27ab8: add             x2, x2, HEAP, lsl #32
    // 0xb27abc: LoadField: r9 = r2->field_b
    //     0xb27abc: ldur            w9, [x2, #0xb]
    // 0xb27ac0: cbz             w9, #0xb27acc
    // 0xb27ac4: r2 = false
    //     0xb27ac4: add             x2, NULL, #0x30  ; false
    // 0xb27ac8: b               #0xb27ad0
    // 0xb27acc: r2 = true
    //     0xb27acc: add             x2, NULL, #0x20  ; true
    // 0xb27ad0: mov             x9, x2
    // 0xb27ad4: b               #0xb27adc
    // 0xb27ad8: r9 = false
    //     0xb27ad8: add             x9, NULL, #0x30  ; false
    // 0xb27adc: stur            x9, [fp, #-0x20]
    // 0xb27ae0: LoadField: r2 = r1->field_3f
    //     0xb27ae0: ldur            w2, [x1, #0x3f]
    // 0xb27ae4: DecompressPointer r2
    //     0xb27ae4: add             x2, x2, HEAP, lsl #32
    // 0xb27ae8: LoadField: r1 = r2->field_b
    //     0xb27ae8: ldur            w1, [x2, #0xb]
    // 0xb27aec: cbnz            w1, #0xb27bd0
    // 0xb27af0: ldur            x10, [fp, #-0x18]
    // 0xb27af4: ldur            x11, [fp, #-0x10]
    // 0xb27af8: r1 = Null
    //     0xb27af8: mov             x1, NULL
    // 0xb27afc: r2 = 6
    //     0xb27afc: movz            x2, #0x6
    // 0xb27b00: r0 = AllocateArray()
    //     0xb27b00: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb27b04: r16 = "No results found for \""
    //     0xb27b04: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c30] "No results found for \""
    //     0xb27b08: ldr             x16, [x16, #0xc30]
    // 0xb27b0c: StoreField: r0->field_f = r16
    //     0xb27b0c: stur            w16, [x0, #0xf]
    // 0xb27b10: ldur            x1, [fp, #-0x10]
    // 0xb27b14: LoadField: r2 = r1->field_27
    //     0xb27b14: ldur            w2, [x1, #0x27]
    // 0xb27b18: DecompressPointer r2
    //     0xb27b18: add             x2, x2, HEAP, lsl #32
    // 0xb27b1c: LoadField: r1 = r2->field_7
    //     0xb27b1c: ldur            w1, [x2, #7]
    // 0xb27b20: DecompressPointer r1
    //     0xb27b20: add             x1, x1, HEAP, lsl #32
    // 0xb27b24: StoreField: r0->field_13 = r1
    //     0xb27b24: stur            w1, [x0, #0x13]
    // 0xb27b28: r16 = "\""
    //     0xb27b28: add             x16, PP, #8, lsl #12  ; [pp+0x8550] "\""
    //     0xb27b2c: ldr             x16, [x16, #0x550]
    // 0xb27b30: ArrayStore: r0[0] = r16  ; List_4
    //     0xb27b30: stur            w16, [x0, #0x17]
    // 0xb27b34: str             x0, [SP]
    // 0xb27b38: r0 = _interpolate()
    //     0xb27b38: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb27b3c: ldur            x2, [fp, #-0x18]
    // 0xb27b40: stur            x0, [fp, #-0x10]
    // 0xb27b44: LoadField: r1 = r2->field_13
    //     0xb27b44: ldur            w1, [x2, #0x13]
    // 0xb27b48: DecompressPointer r1
    //     0xb27b48: add             x1, x1, HEAP, lsl #32
    // 0xb27b4c: r0 = of()
    //     0xb27b4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb27b50: LoadField: r1 = r0->field_87
    //     0xb27b50: ldur            w1, [x0, #0x87]
    // 0xb27b54: DecompressPointer r1
    //     0xb27b54: add             x1, x1, HEAP, lsl #32
    // 0xb27b58: LoadField: r0 = r1->field_7
    //     0xb27b58: ldur            w0, [x1, #7]
    // 0xb27b5c: DecompressPointer r0
    //     0xb27b5c: add             x0, x0, HEAP, lsl #32
    // 0xb27b60: stur            x0, [fp, #-0x30]
    // 0xb27b64: r1 = Instance_Color
    //     0xb27b64: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb27b68: d0 = 0.400000
    //     0xb27b68: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb27b6c: r0 = withOpacity()
    //     0xb27b6c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb27b70: r16 = 16.000000
    //     0xb27b70: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb27b74: ldr             x16, [x16, #0x188]
    // 0xb27b78: stp             x16, x0, [SP]
    // 0xb27b7c: ldur            x1, [fp, #-0x30]
    // 0xb27b80: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb27b80: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb27b84: ldr             x4, [x4, #0x9b8]
    // 0xb27b88: r0 = copyWith()
    //     0xb27b88: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb27b8c: stur            x0, [fp, #-0x30]
    // 0xb27b90: r0 = Text()
    //     0xb27b90: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb27b94: mov             x1, x0
    // 0xb27b98: ldur            x0, [fp, #-0x10]
    // 0xb27b9c: stur            x1, [fp, #-0x38]
    // 0xb27ba0: StoreField: r1->field_b = r0
    //     0xb27ba0: stur            w0, [x1, #0xb]
    // 0xb27ba4: ldur            x0, [fp, #-0x30]
    // 0xb27ba8: StoreField: r1->field_13 = r0
    //     0xb27ba8: stur            w0, [x1, #0x13]
    // 0xb27bac: r0 = Padding()
    //     0xb27bac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb27bb0: mov             x1, x0
    // 0xb27bb4: r0 = Instance_EdgeInsets
    //     0xb27bb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb27bb8: ldr             x0, [x0, #0x980]
    // 0xb27bbc: StoreField: r1->field_f = r0
    //     0xb27bbc: stur            w0, [x1, #0xf]
    // 0xb27bc0: ldur            x0, [fp, #-0x38]
    // 0xb27bc4: StoreField: r1->field_b = r0
    //     0xb27bc4: stur            w0, [x1, #0xb]
    // 0xb27bc8: mov             x3, x1
    // 0xb27bcc: b               #0xb27be8
    // 0xb27bd0: r0 = Container()
    //     0xb27bd0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb27bd4: mov             x1, x0
    // 0xb27bd8: stur            x0, [fp, #-0x10]
    // 0xb27bdc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb27bdc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb27be0: r0 = Container()
    //     0xb27be0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb27be4: ldur            x3, [fp, #-0x10]
    // 0xb27be8: ldur            x0, [fp, #-8]
    // 0xb27bec: ldur            x2, [fp, #-0x18]
    // 0xb27bf0: ldur            x1, [fp, #-0x20]
    // 0xb27bf4: stur            x3, [fp, #-0x10]
    // 0xb27bf8: r0 = Visibility()
    //     0xb27bf8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb27bfc: mov             x2, x0
    // 0xb27c00: ldur            x0, [fp, #-0x10]
    // 0xb27c04: stur            x2, [fp, #-0x30]
    // 0xb27c08: StoreField: r2->field_b = r0
    //     0xb27c08: stur            w0, [x2, #0xb]
    // 0xb27c0c: r0 = Instance_SizedBox
    //     0xb27c0c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb27c10: StoreField: r2->field_f = r0
    //     0xb27c10: stur            w0, [x2, #0xf]
    // 0xb27c14: ldur            x1, [fp, #-0x20]
    // 0xb27c18: StoreField: r2->field_13 = r1
    //     0xb27c18: stur            w1, [x2, #0x13]
    // 0xb27c1c: r3 = false
    //     0xb27c1c: add             x3, NULL, #0x30  ; false
    // 0xb27c20: ArrayStore: r2[0] = r3  ; List_4
    //     0xb27c20: stur            w3, [x2, #0x17]
    // 0xb27c24: StoreField: r2->field_1b = r3
    //     0xb27c24: stur            w3, [x2, #0x1b]
    // 0xb27c28: StoreField: r2->field_1f = r3
    //     0xb27c28: stur            w3, [x2, #0x1f]
    // 0xb27c2c: StoreField: r2->field_23 = r3
    //     0xb27c2c: stur            w3, [x2, #0x23]
    // 0xb27c30: StoreField: r2->field_27 = r3
    //     0xb27c30: stur            w3, [x2, #0x27]
    // 0xb27c34: StoreField: r2->field_2b = r3
    //     0xb27c34: stur            w3, [x2, #0x2b]
    // 0xb27c38: ldur            x4, [fp, #-8]
    // 0xb27c3c: LoadField: r1 = r4->field_b
    //     0xb27c3c: ldur            w1, [x4, #0xb]
    // 0xb27c40: DecompressPointer r1
    //     0xb27c40: add             x1, x1, HEAP, lsl #32
    // 0xb27c44: cmp             w1, NULL
    // 0xb27c48: b.eq            #0xb287a0
    // 0xb27c4c: LoadField: r5 = r1->field_3f
    //     0xb27c4c: ldur            w5, [x1, #0x3f]
    // 0xb27c50: DecompressPointer r5
    //     0xb27c50: add             x5, x5, HEAP, lsl #32
    // 0xb27c54: LoadField: r1 = r5->field_b
    //     0xb27c54: ldur            w1, [x5, #0xb]
    // 0xb27c58: cbnz            w1, #0xb27c64
    // 0xb27c5c: r5 = false
    //     0xb27c5c: add             x5, NULL, #0x30  ; false
    // 0xb27c60: b               #0xb27c68
    // 0xb27c64: r5 = true
    //     0xb27c64: add             x5, NULL, #0x20  ; true
    // 0xb27c68: ldur            x6, [fp, #-0x18]
    // 0xb27c6c: stur            x5, [fp, #-0x10]
    // 0xb27c70: LoadField: r1 = r6->field_13
    //     0xb27c70: ldur            w1, [x6, #0x13]
    // 0xb27c74: DecompressPointer r1
    //     0xb27c74: add             x1, x1, HEAP, lsl #32
    // 0xb27c78: r0 = of()
    //     0xb27c78: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb27c7c: LoadField: r1 = r0->field_5b
    //     0xb27c7c: ldur            w1, [x0, #0x5b]
    // 0xb27c80: DecompressPointer r1
    //     0xb27c80: add             x1, x1, HEAP, lsl #32
    // 0xb27c84: stur            x1, [fp, #-0x20]
    // 0xb27c88: r0 = ColorFilter()
    //     0xb27c88: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb27c8c: mov             x1, x0
    // 0xb27c90: ldur            x0, [fp, #-0x20]
    // 0xb27c94: stur            x1, [fp, #-0x38]
    // 0xb27c98: StoreField: r1->field_7 = r0
    //     0xb27c98: stur            w0, [x1, #7]
    // 0xb27c9c: r0 = Instance_BlendMode
    //     0xb27c9c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb27ca0: ldr             x0, [x0, #0xb30]
    // 0xb27ca4: StoreField: r1->field_b = r0
    //     0xb27ca4: stur            w0, [x1, #0xb]
    // 0xb27ca8: r0 = 1
    //     0xb27ca8: movz            x0, #0x1
    // 0xb27cac: StoreField: r1->field_13 = r0
    //     0xb27cac: stur            x0, [x1, #0x13]
    // 0xb27cb0: r0 = SvgPicture()
    //     0xb27cb0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb27cb4: stur            x0, [fp, #-0x20]
    // 0xb27cb8: ldur            x16, [fp, #-0x38]
    // 0xb27cbc: str             x16, [SP]
    // 0xb27cc0: mov             x1, x0
    // 0xb27cc4: r2 = "assets/images/filter.svg"
    //     0xb27cc4: add             x2, PP, #0x51, lsl #12  ; [pp+0x51c38] "assets/images/filter.svg"
    //     0xb27cc8: ldr             x2, [x2, #0xc38]
    // 0xb27ccc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb27ccc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb27cd0: ldr             x4, [x4, #0xa38]
    // 0xb27cd4: r0 = SvgPicture.asset()
    //     0xb27cd4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb27cd8: ldur            x0, [fp, #-8]
    // 0xb27cdc: LoadField: r1 = r0->field_b
    //     0xb27cdc: ldur            w1, [x0, #0xb]
    // 0xb27ce0: DecompressPointer r1
    //     0xb27ce0: add             x1, x1, HEAP, lsl #32
    // 0xb27ce4: cmp             w1, NULL
    // 0xb27ce8: b.eq            #0xb287a4
    // 0xb27cec: LoadField: r2 = r1->field_f
    //     0xb27cec: ldur            w2, [x1, #0xf]
    // 0xb27cf0: DecompressPointer r2
    //     0xb27cf0: add             x2, x2, HEAP, lsl #32
    // 0xb27cf4: LoadField: r3 = r2->field_7
    //     0xb27cf4: ldur            w3, [x2, #7]
    // 0xb27cf8: cbz             w3, #0xb27d04
    // 0xb27cfc: mov             x5, x2
    // 0xb27d00: b               #0xb27d08
    // 0xb27d04: r5 = Null
    //     0xb27d04: mov             x5, NULL
    // 0xb27d08: stur            x5, [fp, #-0x40]
    // 0xb27d0c: LoadField: r2 = r1->field_b
    //     0xb27d0c: ldur            w2, [x1, #0xb]
    // 0xb27d10: DecompressPointer r2
    //     0xb27d10: add             x2, x2, HEAP, lsl #32
    // 0xb27d14: LoadField: r1 = r2->field_b
    //     0xb27d14: ldur            w1, [x2, #0xb]
    // 0xb27d18: DecompressPointer r1
    //     0xb27d18: add             x1, x1, HEAP, lsl #32
    // 0xb27d1c: cmp             w1, NULL
    // 0xb27d20: b.ne            #0xb27d2c
    // 0xb27d24: r3 = Null
    //     0xb27d24: mov             x3, NULL
    // 0xb27d28: b               #0xb27d78
    // 0xb27d2c: LoadField: r3 = r1->field_b
    //     0xb27d2c: ldur            w3, [x1, #0xb]
    // 0xb27d30: DecompressPointer r3
    //     0xb27d30: add             x3, x3, HEAP, lsl #32
    // 0xb27d34: ldur            x2, [fp, #-0x18]
    // 0xb27d38: stur            x3, [fp, #-0x38]
    // 0xb27d3c: r1 = Function '<anonymous closure>':.
    //     0xb27d3c: add             x1, PP, #0x57, lsl #12  ; [pp+0x573e8] AnonymousClosure: (0xaacbec), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb27d40: ldr             x1, [x1, #0x3e8]
    // 0xb27d44: r0 = AllocateClosure()
    //     0xb27d44: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb27d48: r16 = <DropdownMenuItem<String>>
    //     0xb27d48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f918] TypeArguments: <DropdownMenuItem<String>>
    //     0xb27d4c: ldr             x16, [x16, #0x918]
    // 0xb27d50: ldur            lr, [fp, #-0x38]
    // 0xb27d54: stp             lr, x16, [SP, #8]
    // 0xb27d58: str             x0, [SP]
    // 0xb27d5c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb27d5c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb27d60: r0 = map()
    //     0xb27d60: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xb27d64: mov             x1, x0
    // 0xb27d68: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb27d68: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb27d6c: r0 = toList()
    //     0xb27d6c: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xb27d70: mov             x3, x0
    // 0xb27d74: ldur            x0, [fp, #-8]
    // 0xb27d78: ldur            x2, [fp, #-0x18]
    // 0xb27d7c: stur            x3, [fp, #-0x38]
    // 0xb27d80: r1 = Function '<anonymous closure>':.
    //     0xb27d80: add             x1, PP, #0x57, lsl #12  ; [pp+0x573f0] AnonymousClosure: (0xb28fe8), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb27d84: ldr             x1, [x1, #0x3f0]
    // 0xb27d88: r0 = AllocateClosure()
    //     0xb27d88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb27d8c: r1 = <String>
    //     0xb27d8c: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb27d90: stur            x0, [fp, #-0x48]
    // 0xb27d94: r0 = DropdownButton()
    //     0xb27d94: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xb27d98: stur            x0, [fp, #-0x50]
    // 0xb27d9c: r16 = 0.000000
    //     0xb27d9c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb27da0: str             x16, [SP]
    // 0xb27da4: mov             x1, x0
    // 0xb27da8: ldur            x2, [fp, #-0x38]
    // 0xb27dac: ldur            x3, [fp, #-0x48]
    // 0xb27db0: ldur            x5, [fp, #-0x40]
    // 0xb27db4: r4 = const [0, 0x5, 0x1, 0x4, iconSize, 0x4, null]
    //     0xb27db4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c0d8] List(7) [0, 0x5, 0x1, 0x4, "iconSize", 0x4, Null]
    //     0xb27db8: ldr             x4, [x4, #0xd8]
    // 0xb27dbc: r0 = DropdownButton()
    //     0xb27dbc: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xb27dc0: r0 = DropdownButtonHideUnderline()
    //     0xb27dc0: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xb27dc4: mov             x1, x0
    // 0xb27dc8: ldur            x0, [fp, #-0x50]
    // 0xb27dcc: stur            x1, [fp, #-0x38]
    // 0xb27dd0: StoreField: r1->field_b = r0
    //     0xb27dd0: stur            w0, [x1, #0xb]
    // 0xb27dd4: r0 = Padding()
    //     0xb27dd4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb27dd8: mov             x2, x0
    // 0xb27ddc: r0 = Instance_EdgeInsets
    //     0xb27ddc: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb27de0: ldr             x0, [x0, #0xc40]
    // 0xb27de4: stur            x2, [fp, #-0x40]
    // 0xb27de8: StoreField: r2->field_f = r0
    //     0xb27de8: stur            w0, [x2, #0xf]
    // 0xb27dec: ldur            x0, [fp, #-0x38]
    // 0xb27df0: StoreField: r2->field_b = r0
    //     0xb27df0: stur            w0, [x2, #0xb]
    // 0xb27df4: r1 = <FlexParentData>
    //     0xb27df4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb27df8: ldr             x1, [x1, #0xe00]
    // 0xb27dfc: r0 = Expanded()
    //     0xb27dfc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb27e00: stur            x0, [fp, #-0x48]
    // 0xb27e04: StoreField: r0->field_13 = rZR
    //     0xb27e04: stur            xzr, [x0, #0x13]
    // 0xb27e08: r1 = Instance_FlexFit
    //     0xb27e08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb27e0c: ldr             x1, [x1, #0xe08]
    // 0xb27e10: StoreField: r0->field_1b = r1
    //     0xb27e10: stur            w1, [x0, #0x1b]
    // 0xb27e14: ldur            x1, [fp, #-0x40]
    // 0xb27e18: StoreField: r0->field_b = r1
    //     0xb27e18: stur            w1, [x0, #0xb]
    // 0xb27e1c: ldur            x3, [fp, #-8]
    // 0xb27e20: LoadField: r1 = r3->field_b
    //     0xb27e20: ldur            w1, [x3, #0xb]
    // 0xb27e24: DecompressPointer r1
    //     0xb27e24: add             x1, x1, HEAP, lsl #32
    // 0xb27e28: cmp             w1, NULL
    // 0xb27e2c: b.eq            #0xb287a8
    // 0xb27e30: LoadField: r2 = r1->field_3f
    //     0xb27e30: ldur            w2, [x1, #0x3f]
    // 0xb27e34: DecompressPointer r2
    //     0xb27e34: add             x2, x2, HEAP, lsl #32
    // 0xb27e38: LoadField: r4 = r2->field_b
    //     0xb27e38: ldur            w4, [x2, #0xb]
    // 0xb27e3c: cbnz            w4, #0xb27e48
    // 0xb27e40: r5 = false
    //     0xb27e40: add             x5, NULL, #0x30  ; false
    // 0xb27e44: b               #0xb27e4c
    // 0xb27e48: r5 = true
    //     0xb27e48: add             x5, NULL, #0x20  ; true
    // 0xb27e4c: stur            x5, [fp, #-0x40]
    // 0xb27e50: LoadField: r2 = r1->field_b
    //     0xb27e50: ldur            w2, [x1, #0xb]
    // 0xb27e54: DecompressPointer r2
    //     0xb27e54: add             x2, x2, HEAP, lsl #32
    // 0xb27e58: LoadField: r1 = r2->field_f
    //     0xb27e58: ldur            w1, [x2, #0xf]
    // 0xb27e5c: DecompressPointer r1
    //     0xb27e5c: add             x1, x1, HEAP, lsl #32
    // 0xb27e60: cmp             w1, NULL
    // 0xb27e64: b.ne            #0xb27e70
    // 0xb27e68: r10 = Null
    //     0xb27e68: mov             x10, NULL
    // 0xb27e6c: b               #0xb27e7c
    // 0xb27e70: LoadField: r2 = r1->field_13
    //     0xb27e70: ldur            w2, [x1, #0x13]
    // 0xb27e74: DecompressPointer r2
    //     0xb27e74: add             x2, x2, HEAP, lsl #32
    // 0xb27e78: mov             x10, x2
    // 0xb27e7c: ldur            x8, [fp, #-0x18]
    // 0xb27e80: ldur            x9, [fp, #-0x28]
    // 0xb27e84: ldur            x6, [fp, #-0x30]
    // 0xb27e88: ldur            x4, [fp, #-0x20]
    // 0xb27e8c: ldur            x7, [fp, #-0x10]
    // 0xb27e90: stur            x10, [fp, #-0x38]
    // 0xb27e94: r1 = Null
    //     0xb27e94: mov             x1, NULL
    // 0xb27e98: r2 = 4
    //     0xb27e98: movz            x2, #0x4
    // 0xb27e9c: r0 = AllocateArray()
    //     0xb27e9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb27ea0: mov             x1, x0
    // 0xb27ea4: ldur            x0, [fp, #-0x38]
    // 0xb27ea8: StoreField: r1->field_f = r0
    //     0xb27ea8: stur            w0, [x1, #0xf]
    // 0xb27eac: r16 = " items"
    //     0xb27eac: add             x16, PP, #0x51, lsl #12  ; [pp+0x51c50] " items"
    //     0xb27eb0: ldr             x16, [x16, #0xc50]
    // 0xb27eb4: StoreField: r1->field_13 = r16
    //     0xb27eb4: stur            w16, [x1, #0x13]
    // 0xb27eb8: str             x1, [SP]
    // 0xb27ebc: r0 = _interpolate()
    //     0xb27ebc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb27ec0: ldur            x2, [fp, #-0x18]
    // 0xb27ec4: stur            x0, [fp, #-0x38]
    // 0xb27ec8: LoadField: r1 = r2->field_13
    //     0xb27ec8: ldur            w1, [x2, #0x13]
    // 0xb27ecc: DecompressPointer r1
    //     0xb27ecc: add             x1, x1, HEAP, lsl #32
    // 0xb27ed0: r0 = of()
    //     0xb27ed0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb27ed4: LoadField: r1 = r0->field_87
    //     0xb27ed4: ldur            w1, [x0, #0x87]
    // 0xb27ed8: DecompressPointer r1
    //     0xb27ed8: add             x1, x1, HEAP, lsl #32
    // 0xb27edc: LoadField: r0 = r1->field_2b
    //     0xb27edc: ldur            w0, [x1, #0x2b]
    // 0xb27ee0: DecompressPointer r0
    //     0xb27ee0: add             x0, x0, HEAP, lsl #32
    // 0xb27ee4: r16 = 14.000000
    //     0xb27ee4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb27ee8: ldr             x16, [x16, #0x1d8]
    // 0xb27eec: r30 = Instance_Color
    //     0xb27eec: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb27ef0: stp             lr, x16, [SP]
    // 0xb27ef4: mov             x1, x0
    // 0xb27ef8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb27ef8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb27efc: ldr             x4, [x4, #0xaa0]
    // 0xb27f00: r0 = copyWith()
    //     0xb27f00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb27f04: stur            x0, [fp, #-0x50]
    // 0xb27f08: r0 = Text()
    //     0xb27f08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb27f0c: mov             x1, x0
    // 0xb27f10: ldur            x0, [fp, #-0x38]
    // 0xb27f14: stur            x1, [fp, #-0x58]
    // 0xb27f18: StoreField: r1->field_b = r0
    //     0xb27f18: stur            w0, [x1, #0xb]
    // 0xb27f1c: ldur            x0, [fp, #-0x50]
    // 0xb27f20: StoreField: r1->field_13 = r0
    //     0xb27f20: stur            w0, [x1, #0x13]
    // 0xb27f24: r0 = Visibility()
    //     0xb27f24: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb27f28: mov             x1, x0
    // 0xb27f2c: ldur            x0, [fp, #-0x58]
    // 0xb27f30: stur            x1, [fp, #-0x38]
    // 0xb27f34: StoreField: r1->field_b = r0
    //     0xb27f34: stur            w0, [x1, #0xb]
    // 0xb27f38: r0 = Instance_SizedBox
    //     0xb27f38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb27f3c: StoreField: r1->field_f = r0
    //     0xb27f3c: stur            w0, [x1, #0xf]
    // 0xb27f40: ldur            x2, [fp, #-0x40]
    // 0xb27f44: StoreField: r1->field_13 = r2
    //     0xb27f44: stur            w2, [x1, #0x13]
    // 0xb27f48: r2 = false
    //     0xb27f48: add             x2, NULL, #0x30  ; false
    // 0xb27f4c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb27f4c: stur            w2, [x1, #0x17]
    // 0xb27f50: StoreField: r1->field_1b = r2
    //     0xb27f50: stur            w2, [x1, #0x1b]
    // 0xb27f54: StoreField: r1->field_1f = r2
    //     0xb27f54: stur            w2, [x1, #0x1f]
    // 0xb27f58: StoreField: r1->field_23 = r2
    //     0xb27f58: stur            w2, [x1, #0x23]
    // 0xb27f5c: StoreField: r1->field_27 = r2
    //     0xb27f5c: stur            w2, [x1, #0x27]
    // 0xb27f60: StoreField: r1->field_2b = r2
    //     0xb27f60: stur            w2, [x1, #0x2b]
    // 0xb27f64: r0 = Padding()
    //     0xb27f64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb27f68: mov             x3, x0
    // 0xb27f6c: r0 = Instance_EdgeInsets
    //     0xb27f6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb27f70: ldr             x0, [x0, #0xd48]
    // 0xb27f74: stur            x3, [fp, #-0x40]
    // 0xb27f78: StoreField: r3->field_f = r0
    //     0xb27f78: stur            w0, [x3, #0xf]
    // 0xb27f7c: ldur            x0, [fp, #-0x38]
    // 0xb27f80: StoreField: r3->field_b = r0
    //     0xb27f80: stur            w0, [x3, #0xb]
    // 0xb27f84: r1 = Null
    //     0xb27f84: mov             x1, NULL
    // 0xb27f88: r2 = 8
    //     0xb27f88: movz            x2, #0x8
    // 0xb27f8c: r0 = AllocateArray()
    //     0xb27f8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb27f90: mov             x2, x0
    // 0xb27f94: ldur            x0, [fp, #-0x20]
    // 0xb27f98: stur            x2, [fp, #-0x38]
    // 0xb27f9c: StoreField: r2->field_f = r0
    //     0xb27f9c: stur            w0, [x2, #0xf]
    // 0xb27fa0: ldur            x0, [fp, #-0x48]
    // 0xb27fa4: StoreField: r2->field_13 = r0
    //     0xb27fa4: stur            w0, [x2, #0x13]
    // 0xb27fa8: r16 = Instance_Spacer
    //     0xb27fa8: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb27fac: ldr             x16, [x16, #0xf0]
    // 0xb27fb0: ArrayStore: r2[0] = r16  ; List_4
    //     0xb27fb0: stur            w16, [x2, #0x17]
    // 0xb27fb4: ldur            x0, [fp, #-0x40]
    // 0xb27fb8: StoreField: r2->field_1b = r0
    //     0xb27fb8: stur            w0, [x2, #0x1b]
    // 0xb27fbc: r1 = <Widget>
    //     0xb27fbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb27fc0: r0 = AllocateGrowableArray()
    //     0xb27fc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb27fc4: mov             x1, x0
    // 0xb27fc8: ldur            x0, [fp, #-0x38]
    // 0xb27fcc: stur            x1, [fp, #-0x20]
    // 0xb27fd0: StoreField: r1->field_f = r0
    //     0xb27fd0: stur            w0, [x1, #0xf]
    // 0xb27fd4: r0 = 8
    //     0xb27fd4: movz            x0, #0x8
    // 0xb27fd8: StoreField: r1->field_b = r0
    //     0xb27fd8: stur            w0, [x1, #0xb]
    // 0xb27fdc: r0 = Row()
    //     0xb27fdc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb27fe0: mov             x1, x0
    // 0xb27fe4: r0 = Instance_Axis
    //     0xb27fe4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb27fe8: stur            x1, [fp, #-0x38]
    // 0xb27fec: StoreField: r1->field_f = r0
    //     0xb27fec: stur            w0, [x1, #0xf]
    // 0xb27ff0: r0 = Instance_MainAxisAlignment
    //     0xb27ff0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb27ff4: ldr             x0, [x0, #0xa08]
    // 0xb27ff8: StoreField: r1->field_13 = r0
    //     0xb27ff8: stur            w0, [x1, #0x13]
    // 0xb27ffc: r2 = Instance_MainAxisSize
    //     0xb27ffc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb28000: ldr             x2, [x2, #0xa10]
    // 0xb28004: ArrayStore: r1[0] = r2  ; List_4
    //     0xb28004: stur            w2, [x1, #0x17]
    // 0xb28008: r3 = Instance_CrossAxisAlignment
    //     0xb28008: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2800c: ldr             x3, [x3, #0xa18]
    // 0xb28010: StoreField: r1->field_1b = r3
    //     0xb28010: stur            w3, [x1, #0x1b]
    // 0xb28014: r4 = Instance_VerticalDirection
    //     0xb28014: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb28018: ldr             x4, [x4, #0xa20]
    // 0xb2801c: StoreField: r1->field_23 = r4
    //     0xb2801c: stur            w4, [x1, #0x23]
    // 0xb28020: r5 = Instance_Clip
    //     0xb28020: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb28024: ldr             x5, [x5, #0x38]
    // 0xb28028: StoreField: r1->field_2b = r5
    //     0xb28028: stur            w5, [x1, #0x2b]
    // 0xb2802c: StoreField: r1->field_2f = rZR
    //     0xb2802c: stur            xzr, [x1, #0x2f]
    // 0xb28030: ldur            x6, [fp, #-0x20]
    // 0xb28034: StoreField: r1->field_b = r6
    //     0xb28034: stur            w6, [x1, #0xb]
    // 0xb28038: r0 = Padding()
    //     0xb28038: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb2803c: mov             x1, x0
    // 0xb28040: r0 = Instance_EdgeInsets
    //     0xb28040: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb28044: ldr             x0, [x0, #0x668]
    // 0xb28048: stur            x1, [fp, #-0x20]
    // 0xb2804c: StoreField: r1->field_f = r0
    //     0xb2804c: stur            w0, [x1, #0xf]
    // 0xb28050: ldur            x0, [fp, #-0x38]
    // 0xb28054: StoreField: r1->field_b = r0
    //     0xb28054: stur            w0, [x1, #0xb]
    // 0xb28058: r0 = Visibility()
    //     0xb28058: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb2805c: mov             x2, x0
    // 0xb28060: ldur            x0, [fp, #-0x20]
    // 0xb28064: stur            x2, [fp, #-0x38]
    // 0xb28068: StoreField: r2->field_b = r0
    //     0xb28068: stur            w0, [x2, #0xb]
    // 0xb2806c: r0 = Instance_SizedBox
    //     0xb2806c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb28070: StoreField: r2->field_f = r0
    //     0xb28070: stur            w0, [x2, #0xf]
    // 0xb28074: ldur            x1, [fp, #-0x10]
    // 0xb28078: StoreField: r2->field_13 = r1
    //     0xb28078: stur            w1, [x2, #0x13]
    // 0xb2807c: r3 = false
    //     0xb2807c: add             x3, NULL, #0x30  ; false
    // 0xb28080: ArrayStore: r2[0] = r3  ; List_4
    //     0xb28080: stur            w3, [x2, #0x17]
    // 0xb28084: StoreField: r2->field_1b = r3
    //     0xb28084: stur            w3, [x2, #0x1b]
    // 0xb28088: StoreField: r2->field_1f = r3
    //     0xb28088: stur            w3, [x2, #0x1f]
    // 0xb2808c: StoreField: r2->field_23 = r3
    //     0xb2808c: stur            w3, [x2, #0x23]
    // 0xb28090: StoreField: r2->field_27 = r3
    //     0xb28090: stur            w3, [x2, #0x27]
    // 0xb28094: StoreField: r2->field_2b = r3
    //     0xb28094: stur            w3, [x2, #0x2b]
    // 0xb28098: ldur            x4, [fp, #-8]
    // 0xb2809c: LoadField: r1 = r4->field_b
    //     0xb2809c: ldur            w1, [x4, #0xb]
    // 0xb280a0: DecompressPointer r1
    //     0xb280a0: add             x1, x1, HEAP, lsl #32
    // 0xb280a4: cmp             w1, NULL
    // 0xb280a8: b.eq            #0xb287ac
    // 0xb280ac: LoadField: r5 = r1->field_3f
    //     0xb280ac: ldur            w5, [x1, #0x3f]
    // 0xb280b0: DecompressPointer r5
    //     0xb280b0: add             x5, x5, HEAP, lsl #32
    // 0xb280b4: LoadField: r1 = r5->field_b
    //     0xb280b4: ldur            w1, [x5, #0xb]
    // 0xb280b8: cbnz            w1, #0xb280c4
    // 0xb280bc: r5 = false
    //     0xb280bc: add             x5, NULL, #0x30  ; false
    // 0xb280c0: b               #0xb280c8
    // 0xb280c4: r5 = true
    //     0xb280c4: add             x5, NULL, #0x20  ; true
    // 0xb280c8: stur            x5, [fp, #-0x10]
    // 0xb280cc: r1 = Instance_Color
    //     0xb280cc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb280d0: d0 = 0.100000
    //     0xb280d0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb280d4: r0 = withOpacity()
    //     0xb280d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb280d8: stur            x0, [fp, #-0x20]
    // 0xb280dc: r0 = Divider()
    //     0xb280dc: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xb280e0: mov             x1, x0
    // 0xb280e4: r0 = 1.000000
    //     0xb280e4: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb280e8: stur            x1, [fp, #-0x40]
    // 0xb280ec: StoreField: r1->field_f = r0
    //     0xb280ec: stur            w0, [x1, #0xf]
    // 0xb280f0: ldur            x0, [fp, #-0x20]
    // 0xb280f4: StoreField: r1->field_1f = r0
    //     0xb280f4: stur            w0, [x1, #0x1f]
    // 0xb280f8: r0 = Visibility()
    //     0xb280f8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb280fc: mov             x1, x0
    // 0xb28100: ldur            x0, [fp, #-0x40]
    // 0xb28104: stur            x1, [fp, #-0x48]
    // 0xb28108: StoreField: r1->field_b = r0
    //     0xb28108: stur            w0, [x1, #0xb]
    // 0xb2810c: r0 = Instance_SizedBox
    //     0xb2810c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb28110: StoreField: r1->field_f = r0
    //     0xb28110: stur            w0, [x1, #0xf]
    // 0xb28114: ldur            x2, [fp, #-0x10]
    // 0xb28118: StoreField: r1->field_13 = r2
    //     0xb28118: stur            w2, [x1, #0x13]
    // 0xb2811c: r2 = false
    //     0xb2811c: add             x2, NULL, #0x30  ; false
    // 0xb28120: ArrayStore: r1[0] = r2  ; List_4
    //     0xb28120: stur            w2, [x1, #0x17]
    // 0xb28124: StoreField: r1->field_1b = r2
    //     0xb28124: stur            w2, [x1, #0x1b]
    // 0xb28128: StoreField: r1->field_1f = r2
    //     0xb28128: stur            w2, [x1, #0x1f]
    // 0xb2812c: StoreField: r1->field_23 = r2
    //     0xb2812c: stur            w2, [x1, #0x23]
    // 0xb28130: StoreField: r1->field_27 = r2
    //     0xb28130: stur            w2, [x1, #0x27]
    // 0xb28134: StoreField: r1->field_2b = r2
    //     0xb28134: stur            w2, [x1, #0x2b]
    // 0xb28138: ldur            x3, [fp, #-8]
    // 0xb2813c: LoadField: r4 = r3->field_b
    //     0xb2813c: ldur            w4, [x3, #0xb]
    // 0xb28140: DecompressPointer r4
    //     0xb28140: add             x4, x4, HEAP, lsl #32
    // 0xb28144: cmp             w4, NULL
    // 0xb28148: b.eq            #0xb287b0
    // 0xb2814c: LoadField: r5 = r4->field_3f
    //     0xb2814c: ldur            w5, [x4, #0x3f]
    // 0xb28150: DecompressPointer r5
    //     0xb28150: add             x5, x5, HEAP, lsl #32
    // 0xb28154: stur            x5, [fp, #-0x40]
    // 0xb28158: LoadField: r6 = r5->field_b
    //     0xb28158: ldur            w6, [x5, #0xb]
    // 0xb2815c: cbnz            w6, #0xb28168
    // 0xb28160: r7 = false
    //     0xb28160: add             x7, NULL, #0x30  ; false
    // 0xb28164: b               #0xb2816c
    // 0xb28168: r7 = true
    //     0xb28168: add             x7, NULL, #0x20  ; true
    // 0xb2816c: stur            x7, [fp, #-0x20]
    // 0xb28170: LoadField: r6 = r4->field_13
    //     0xb28170: ldur            w6, [x4, #0x13]
    // 0xb28174: DecompressPointer r6
    //     0xb28174: add             x6, x6, HEAP, lsl #32
    // 0xb28178: stur            x6, [fp, #-0x10]
    // 0xb2817c: r0 = CustomStyle()
    //     0xb2817c: bl              #0x910168  ; AllocateCustomStyleStub -> CustomStyle (size=0x10)
    // 0xb28180: mov             x1, x0
    // 0xb28184: r0 = Instance_TitleAlignment
    //     0xb28184: add             x0, PP, #0x24, lsl #12  ; [pp+0x24510] Obj!TitleAlignment@d75601
    //     0xb28188: ldr             x0, [x0, #0x510]
    // 0xb2818c: stur            x1, [fp, #-0x50]
    // 0xb28190: StoreField: r1->field_7 = r0
    //     0xb28190: stur            w0, [x1, #7]
    // 0xb28194: r0 = ProductGridItemView()
    //     0xb28194: bl              #0xb287dc  ; AllocateProductGridItemViewStub -> ProductGridItemView (size=0x5c)
    // 0xb28198: mov             x1, x0
    // 0xb2819c: ldur            x0, [fp, #-0x40]
    // 0xb281a0: stur            x1, [fp, #-0x58]
    // 0xb281a4: StoreField: r1->field_b = r0
    //     0xb281a4: stur            w0, [x1, #0xb]
    // 0xb281a8: r0 = ""
    //     0xb281a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb281ac: StoreField: r1->field_f = r0
    //     0xb281ac: stur            w0, [x1, #0xf]
    // 0xb281b0: r0 = false
    //     0xb281b0: add             x0, NULL, #0x30  ; false
    // 0xb281b4: StoreField: r1->field_13 = r0
    //     0xb281b4: stur            w0, [x1, #0x13]
    // 0xb281b8: r0 = ViewAll()
    //     0xb281b8: bl              #0x90ff98  ; AllocateViewAllStub -> ViewAll (size=0x10)
    // 0xb281bc: mov             x1, x0
    // 0xb281c0: ldur            x0, [fp, #-0x58]
    // 0xb281c4: ArrayStore: r0[0] = r1  ; List_4
    //     0xb281c4: stur            w1, [x0, #0x17]
    // 0xb281c8: r3 = false
    //     0xb281c8: add             x3, NULL, #0x30  ; false
    // 0xb281cc: StoreField: r0->field_1b = r3
    //     0xb281cc: stur            w3, [x0, #0x1b]
    // 0xb281d0: ldur            x1, [fp, #-0x10]
    // 0xb281d4: StoreField: r0->field_1f = r1
    //     0xb281d4: stur            w1, [x0, #0x1f]
    // 0xb281d8: ldur            x1, [fp, #-0x50]
    // 0xb281dc: StoreField: r0->field_23 = r1
    //     0xb281dc: stur            w1, [x0, #0x23]
    // 0xb281e0: r4 = "search_page"
    //     0xb281e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xb281e4: ldr             x4, [x4, #0xe58]
    // 0xb281e8: StoreField: r0->field_37 = r4
    //     0xb281e8: stur            w4, [x0, #0x37]
    // 0xb281ec: StoreField: r0->field_3b = r4
    //     0xb281ec: stur            w4, [x0, #0x3b]
    // 0xb281f0: ldur            x2, [fp, #-0x18]
    // 0xb281f4: r1 = Function '<anonymous closure>':.
    //     0xb281f4: add             x1, PP, #0x57, lsl #12  ; [pp+0x573f8] AnonymousClosure: (0xb28f5c), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb281f8: ldr             x1, [x1, #0x3f8]
    // 0xb281fc: r0 = AllocateClosure()
    //     0xb281fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb28200: mov             x1, x0
    // 0xb28204: ldur            x0, [fp, #-0x58]
    // 0xb28208: StoreField: r0->field_47 = r1
    //     0xb28208: stur            w1, [x0, #0x47]
    // 0xb2820c: ldur            x2, [fp, #-0x18]
    // 0xb28210: r1 = Function '<anonymous closure>':.
    //     0xb28210: add             x1, PP, #0x57, lsl #12  ; [pp+0x57400] AnonymousClosure: (0xb28ec4), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb28214: ldr             x1, [x1, #0x400]
    // 0xb28218: r0 = AllocateClosure()
    //     0xb28218: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2821c: mov             x1, x0
    // 0xb28220: ldur            x0, [fp, #-0x58]
    // 0xb28224: StoreField: r0->field_4f = r1
    //     0xb28224: stur            w1, [x0, #0x4f]
    // 0xb28228: ldur            x2, [fp, #-0x18]
    // 0xb2822c: r1 = Function '<anonymous closure>':.
    //     0xb2822c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57408] AnonymousClosure: (0xb28e18), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb28230: ldr             x1, [x1, #0x408]
    // 0xb28234: r0 = AllocateClosure()
    //     0xb28234: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb28238: mov             x1, x0
    // 0xb2823c: ldur            x0, [fp, #-0x58]
    // 0xb28240: StoreField: r0->field_53 = r1
    //     0xb28240: stur            w1, [x0, #0x53]
    // 0xb28244: r1 = "search_page"
    //     0xb28244: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xb28248: ldr             x1, [x1, #0xe58]
    // 0xb2824c: StoreField: r0->field_3f = r1
    //     0xb2824c: stur            w1, [x0, #0x3f]
    // 0xb28250: ldur            x2, [fp, #-0x18]
    // 0xb28254: r1 = Function '<anonymous closure>':.
    //     0xb28254: add             x1, PP, #0x57, lsl #12  ; [pp+0x57410] AnonymousClosure: (0xb28d60), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb28258: ldr             x1, [x1, #0x410]
    // 0xb2825c: r0 = AllocateClosure()
    //     0xb2825c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb28260: mov             x1, x0
    // 0xb28264: ldur            x0, [fp, #-0x58]
    // 0xb28268: StoreField: r0->field_4b = r1
    //     0xb28268: stur            w1, [x0, #0x4b]
    // 0xb2826c: r1 = Function '<anonymous closure>':.
    //     0xb2826c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57418] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb28270: ldr             x1, [x1, #0x418]
    // 0xb28274: r2 = Null
    //     0xb28274: mov             x2, NULL
    // 0xb28278: r0 = AllocateClosure()
    //     0xb28278: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2827c: mov             x1, x0
    // 0xb28280: ldur            x0, [fp, #-0x58]
    // 0xb28284: StoreField: r0->field_57 = r1
    //     0xb28284: stur            w1, [x0, #0x57]
    // 0xb28288: r0 = Visibility()
    //     0xb28288: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb2828c: mov             x3, x0
    // 0xb28290: ldur            x0, [fp, #-0x58]
    // 0xb28294: stur            x3, [fp, #-0x10]
    // 0xb28298: StoreField: r3->field_b = r0
    //     0xb28298: stur            w0, [x3, #0xb]
    // 0xb2829c: r0 = Instance_SizedBox
    //     0xb2829c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb282a0: StoreField: r3->field_f = r0
    //     0xb282a0: stur            w0, [x3, #0xf]
    // 0xb282a4: ldur            x0, [fp, #-0x20]
    // 0xb282a8: StoreField: r3->field_13 = r0
    //     0xb282a8: stur            w0, [x3, #0x13]
    // 0xb282ac: r0 = false
    //     0xb282ac: add             x0, NULL, #0x30  ; false
    // 0xb282b0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb282b0: stur            w0, [x3, #0x17]
    // 0xb282b4: StoreField: r3->field_1b = r0
    //     0xb282b4: stur            w0, [x3, #0x1b]
    // 0xb282b8: StoreField: r3->field_1f = r0
    //     0xb282b8: stur            w0, [x3, #0x1f]
    // 0xb282bc: StoreField: r3->field_23 = r0
    //     0xb282bc: stur            w0, [x3, #0x23]
    // 0xb282c0: StoreField: r3->field_27 = r0
    //     0xb282c0: stur            w0, [x3, #0x27]
    // 0xb282c4: StoreField: r3->field_2b = r0
    //     0xb282c4: stur            w0, [x3, #0x2b]
    // 0xb282c8: r1 = Null
    //     0xb282c8: mov             x1, NULL
    // 0xb282cc: r2 = 12
    //     0xb282cc: movz            x2, #0xc
    // 0xb282d0: r0 = AllocateArray()
    //     0xb282d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb282d4: mov             x2, x0
    // 0xb282d8: ldur            x0, [fp, #-0x28]
    // 0xb282dc: stur            x2, [fp, #-0x20]
    // 0xb282e0: StoreField: r2->field_f = r0
    //     0xb282e0: stur            w0, [x2, #0xf]
    // 0xb282e4: r16 = Instance_Divider
    //     0xb282e4: add             x16, PP, #0x48, lsl #12  ; [pp+0x484d0] Obj!Divider@d66ca1
    //     0xb282e8: ldr             x16, [x16, #0x4d0]
    // 0xb282ec: StoreField: r2->field_13 = r16
    //     0xb282ec: stur            w16, [x2, #0x13]
    // 0xb282f0: ldur            x0, [fp, #-0x30]
    // 0xb282f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb282f4: stur            w0, [x2, #0x17]
    // 0xb282f8: ldur            x0, [fp, #-0x38]
    // 0xb282fc: StoreField: r2->field_1b = r0
    //     0xb282fc: stur            w0, [x2, #0x1b]
    // 0xb28300: ldur            x0, [fp, #-0x48]
    // 0xb28304: StoreField: r2->field_1f = r0
    //     0xb28304: stur            w0, [x2, #0x1f]
    // 0xb28308: ldur            x0, [fp, #-0x10]
    // 0xb2830c: StoreField: r2->field_23 = r0
    //     0xb2830c: stur            w0, [x2, #0x23]
    // 0xb28310: r1 = <Widget>
    //     0xb28310: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb28314: r0 = AllocateGrowableArray()
    //     0xb28314: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb28318: mov             x1, x0
    // 0xb2831c: ldur            x0, [fp, #-0x20]
    // 0xb28320: stur            x1, [fp, #-0x10]
    // 0xb28324: StoreField: r1->field_f = r0
    //     0xb28324: stur            w0, [x1, #0xf]
    // 0xb28328: r0 = 12
    //     0xb28328: movz            x0, #0xc
    // 0xb2832c: StoreField: r1->field_b = r0
    //     0xb2832c: stur            w0, [x1, #0xb]
    // 0xb28330: r0 = Column()
    //     0xb28330: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb28334: mov             x3, x0
    // 0xb28338: r0 = Instance_Axis
    //     0xb28338: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2833c: stur            x3, [fp, #-0x20]
    // 0xb28340: StoreField: r3->field_f = r0
    //     0xb28340: stur            w0, [x3, #0xf]
    // 0xb28344: r4 = Instance_MainAxisAlignment
    //     0xb28344: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb28348: ldr             x4, [x4, #0xa08]
    // 0xb2834c: StoreField: r3->field_13 = r4
    //     0xb2834c: stur            w4, [x3, #0x13]
    // 0xb28350: r5 = Instance_MainAxisSize
    //     0xb28350: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb28354: ldr             x5, [x5, #0xa10]
    // 0xb28358: ArrayStore: r3[0] = r5  ; List_4
    //     0xb28358: stur            w5, [x3, #0x17]
    // 0xb2835c: r1 = Instance_CrossAxisAlignment
    //     0xb2835c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb28360: ldr             x1, [x1, #0xa18]
    // 0xb28364: StoreField: r3->field_1b = r1
    //     0xb28364: stur            w1, [x3, #0x1b]
    // 0xb28368: r6 = Instance_VerticalDirection
    //     0xb28368: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2836c: ldr             x6, [x6, #0xa20]
    // 0xb28370: StoreField: r3->field_23 = r6
    //     0xb28370: stur            w6, [x3, #0x23]
    // 0xb28374: r7 = Instance_Clip
    //     0xb28374: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb28378: ldr             x7, [x7, #0x38]
    // 0xb2837c: StoreField: r3->field_2b = r7
    //     0xb2837c: stur            w7, [x3, #0x2b]
    // 0xb28380: StoreField: r3->field_2f = rZR
    //     0xb28380: stur            xzr, [x3, #0x2f]
    // 0xb28384: ldur            x1, [fp, #-0x10]
    // 0xb28388: StoreField: r3->field_b = r1
    //     0xb28388: stur            w1, [x3, #0xb]
    // 0xb2838c: r1 = Null
    //     0xb2838c: mov             x1, NULL
    // 0xb28390: r2 = 2
    //     0xb28390: movz            x2, #0x2
    // 0xb28394: r0 = AllocateArray()
    //     0xb28394: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb28398: mov             x2, x0
    // 0xb2839c: ldur            x0, [fp, #-0x20]
    // 0xb283a0: stur            x2, [fp, #-0x10]
    // 0xb283a4: StoreField: r2->field_f = r0
    //     0xb283a4: stur            w0, [x2, #0xf]
    // 0xb283a8: r1 = <Widget>
    //     0xb283a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb283ac: r0 = AllocateGrowableArray()
    //     0xb283ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb283b0: mov             x2, x0
    // 0xb283b4: ldur            x0, [fp, #-0x10]
    // 0xb283b8: stur            x2, [fp, #-0x20]
    // 0xb283bc: StoreField: r2->field_f = r0
    //     0xb283bc: stur            w0, [x2, #0xf]
    // 0xb283c0: r0 = 2
    //     0xb283c0: movz            x0, #0x2
    // 0xb283c4: StoreField: r2->field_b = r0
    //     0xb283c4: stur            w0, [x2, #0xb]
    // 0xb283c8: ldur            x0, [fp, #-8]
    // 0xb283cc: LoadField: r1 = r0->field_1f
    //     0xb283cc: ldur            w1, [x0, #0x1f]
    // 0xb283d0: DecompressPointer r1
    //     0xb283d0: add             x1, x1, HEAP, lsl #32
    // 0xb283d4: tbnz            w1, #4, #0xb28714
    // 0xb283d8: ldur            x3, [fp, #-0x18]
    // 0xb283dc: LoadField: r1 = r3->field_13
    //     0xb283dc: ldur            w1, [x3, #0x13]
    // 0xb283e0: DecompressPointer r1
    //     0xb283e0: add             x1, x1, HEAP, lsl #32
    // 0xb283e4: r0 = of()
    //     0xb283e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb283e8: LoadField: r1 = r0->field_87
    //     0xb283e8: ldur            w1, [x0, #0x87]
    // 0xb283ec: DecompressPointer r1
    //     0xb283ec: add             x1, x1, HEAP, lsl #32
    // 0xb283f0: LoadField: r0 = r1->field_2b
    //     0xb283f0: ldur            w0, [x1, #0x2b]
    // 0xb283f4: DecompressPointer r0
    //     0xb283f4: add             x0, x0, HEAP, lsl #32
    // 0xb283f8: r16 = Instance_Color
    //     0xb283f8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb283fc: r30 = 12.000000
    //     0xb283fc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb28400: ldr             lr, [lr, #0x9e8]
    // 0xb28404: stp             lr, x16, [SP]
    // 0xb28408: mov             x1, x0
    // 0xb2840c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb2840c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb28410: ldr             x4, [x4, #0x9b8]
    // 0xb28414: r0 = copyWith()
    //     0xb28414: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb28418: stur            x0, [fp, #-0x10]
    // 0xb2841c: r0 = Text()
    //     0xb2841c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb28420: mov             x1, x0
    // 0xb28424: r0 = "Suggestions"
    //     0xb28424: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c80] "Suggestions"
    //     0xb28428: ldr             x0, [x0, #0xc80]
    // 0xb2842c: stur            x1, [fp, #-0x28]
    // 0xb28430: StoreField: r1->field_b = r0
    //     0xb28430: stur            w0, [x1, #0xb]
    // 0xb28434: ldur            x0, [fp, #-0x10]
    // 0xb28438: StoreField: r1->field_13 = r0
    //     0xb28438: stur            w0, [x1, #0x13]
    // 0xb2843c: r0 = Padding()
    //     0xb2843c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb28440: mov             x3, x0
    // 0xb28444: r0 = Instance_EdgeInsets
    //     0xb28444: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb28448: ldr             x0, [x0, #0xd0]
    // 0xb2844c: stur            x3, [fp, #-0x30]
    // 0xb28450: StoreField: r3->field_f = r0
    //     0xb28450: stur            w0, [x3, #0xf]
    // 0xb28454: ldur            x0, [fp, #-0x28]
    // 0xb28458: StoreField: r3->field_b = r0
    //     0xb28458: stur            w0, [x3, #0xb]
    // 0xb2845c: ldur            x0, [fp, #-8]
    // 0xb28460: LoadField: r1 = r0->field_b
    //     0xb28460: ldur            w1, [x0, #0xb]
    // 0xb28464: DecompressPointer r1
    //     0xb28464: add             x1, x1, HEAP, lsl #32
    // 0xb28468: cmp             w1, NULL
    // 0xb2846c: b.eq            #0xb287b4
    // 0xb28470: LoadField: r0 = r1->field_43
    //     0xb28470: ldur            w0, [x1, #0x43]
    // 0xb28474: DecompressPointer r0
    //     0xb28474: add             x0, x0, HEAP, lsl #32
    // 0xb28478: LoadField: r1 = r0->field_b
    //     0xb28478: ldur            w1, [x0, #0xb]
    // 0xb2847c: DecompressPointer r1
    //     0xb2847c: add             x1, x1, HEAP, lsl #32
    // 0xb28480: cmp             w1, NULL
    // 0xb28484: b.ne            #0xb28490
    // 0xb28488: r0 = Null
    //     0xb28488: mov             x0, NULL
    // 0xb2848c: b               #0xb28494
    // 0xb28490: LoadField: r0 = r1->field_b
    //     0xb28490: ldur            w0, [x1, #0xb]
    // 0xb28494: cmp             w0, NULL
    // 0xb28498: b.ne            #0xb284a4
    // 0xb2849c: r0 = 0
    //     0xb2849c: movz            x0, #0
    // 0xb284a0: b               #0xb284ac
    // 0xb284a4: r2 = LoadInt32Instr(r0)
    //     0xb284a4: sbfx            x2, x0, #1, #0x1f
    // 0xb284a8: mov             x0, x2
    // 0xb284ac: cmp             x0, #3
    // 0xb284b0: b.le            #0xb284c0
    // 0xb284b4: r0 = 180.000000
    //     0xb284b4: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c88] 180
    //     0xb284b8: ldr             x0, [x0, #0xc88]
    // 0xb284bc: b               #0xb284c4
    // 0xb284c0: r0 = Null
    //     0xb284c0: mov             x0, NULL
    // 0xb284c4: stur            x0, [fp, #-0x10]
    // 0xb284c8: cmp             w1, NULL
    // 0xb284cc: b.ne            #0xb284d8
    // 0xb284d0: r1 = Null
    //     0xb284d0: mov             x1, NULL
    // 0xb284d4: b               #0xb284e0
    // 0xb284d8: LoadField: r2 = r1->field_b
    //     0xb284d8: ldur            w2, [x1, #0xb]
    // 0xb284dc: mov             x1, x2
    // 0xb284e0: cmp             w1, NULL
    // 0xb284e4: b.ne            #0xb284f0
    // 0xb284e8: r1 = 0
    //     0xb284e8: movz            x1, #0
    // 0xb284ec: b               #0xb284f8
    // 0xb284f0: r2 = LoadInt32Instr(r1)
    //     0xb284f0: sbfx            x2, x1, #1, #0x1f
    // 0xb284f4: mov             x1, x2
    // 0xb284f8: ldur            x4, [fp, #-0x20]
    // 0xb284fc: lsl             x5, x1, #1
    // 0xb28500: ldur            x2, [fp, #-0x18]
    // 0xb28504: stur            x5, [fp, #-8]
    // 0xb28508: r1 = Function '<anonymous closure>':.
    //     0xb28508: add             x1, PP, #0x57, lsl #12  ; [pp+0x57420] AnonymousClosure: (0xb287e8), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb2850c: ldr             x1, [x1, #0x420]
    // 0xb28510: r0 = AllocateClosure()
    //     0xb28510: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb28514: stur            x0, [fp, #-0x18]
    // 0xb28518: r0 = ListView()
    //     0xb28518: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb2851c: stur            x0, [fp, #-0x28]
    // 0xb28520: r16 = Instance_EdgeInsets
    //     0xb28520: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb28524: ldr             x16, [x16, #0x980]
    // 0xb28528: r30 = true
    //     0xb28528: add             lr, NULL, #0x20  ; true
    // 0xb2852c: stp             lr, x16, [SP]
    // 0xb28530: mov             x1, x0
    // 0xb28534: ldur            x2, [fp, #-0x18]
    // 0xb28538: ldur            x3, [fp, #-8]
    // 0xb2853c: r4 = const [0, 0x5, 0x2, 0x3, padding, 0x3, shrinkWrap, 0x4, null]
    //     0xb2853c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51c98] List(9) [0, 0x5, 0x2, 0x3, "padding", 0x3, "shrinkWrap", 0x4, Null]
    //     0xb28540: ldr             x4, [x4, #0xc98]
    // 0xb28544: r0 = ListView.builder()
    //     0xb28544: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb28548: r0 = Scrollbar()
    //     0xb28548: bl              #0x997690  ; AllocateScrollbarStub -> Scrollbar (size=0x30)
    // 0xb2854c: mov             x1, x0
    // 0xb28550: ldur            x0, [fp, #-0x28]
    // 0xb28554: stur            x1, [fp, #-8]
    // 0xb28558: StoreField: r1->field_b = r0
    //     0xb28558: stur            w0, [x1, #0xb]
    // 0xb2855c: r0 = SizedBox()
    //     0xb2855c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb28560: mov             x3, x0
    // 0xb28564: r0 = inf
    //     0xb28564: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb28568: ldr             x0, [x0, #0x9f8]
    // 0xb2856c: stur            x3, [fp, #-0x18]
    // 0xb28570: StoreField: r3->field_f = r0
    //     0xb28570: stur            w0, [x3, #0xf]
    // 0xb28574: ldur            x0, [fp, #-0x10]
    // 0xb28578: StoreField: r3->field_13 = r0
    //     0xb28578: stur            w0, [x3, #0x13]
    // 0xb2857c: ldur            x0, [fp, #-8]
    // 0xb28580: StoreField: r3->field_b = r0
    //     0xb28580: stur            w0, [x3, #0xb]
    // 0xb28584: r1 = Null
    //     0xb28584: mov             x1, NULL
    // 0xb28588: r2 = 4
    //     0xb28588: movz            x2, #0x4
    // 0xb2858c: r0 = AllocateArray()
    //     0xb2858c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb28590: mov             x2, x0
    // 0xb28594: ldur            x0, [fp, #-0x30]
    // 0xb28598: stur            x2, [fp, #-8]
    // 0xb2859c: StoreField: r2->field_f = r0
    //     0xb2859c: stur            w0, [x2, #0xf]
    // 0xb285a0: ldur            x0, [fp, #-0x18]
    // 0xb285a4: StoreField: r2->field_13 = r0
    //     0xb285a4: stur            w0, [x2, #0x13]
    // 0xb285a8: r1 = <Widget>
    //     0xb285a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb285ac: r0 = AllocateGrowableArray()
    //     0xb285ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb285b0: mov             x1, x0
    // 0xb285b4: ldur            x0, [fp, #-8]
    // 0xb285b8: stur            x1, [fp, #-0x10]
    // 0xb285bc: StoreField: r1->field_f = r0
    //     0xb285bc: stur            w0, [x1, #0xf]
    // 0xb285c0: r0 = 4
    //     0xb285c0: movz            x0, #0x4
    // 0xb285c4: StoreField: r1->field_b = r0
    //     0xb285c4: stur            w0, [x1, #0xb]
    // 0xb285c8: r0 = Column()
    //     0xb285c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb285cc: mov             x1, x0
    // 0xb285d0: r0 = Instance_Axis
    //     0xb285d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb285d4: stur            x1, [fp, #-8]
    // 0xb285d8: StoreField: r1->field_f = r0
    //     0xb285d8: stur            w0, [x1, #0xf]
    // 0xb285dc: r2 = Instance_MainAxisAlignment
    //     0xb285dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb285e0: ldr             x2, [x2, #0xa08]
    // 0xb285e4: StoreField: r1->field_13 = r2
    //     0xb285e4: stur            w2, [x1, #0x13]
    // 0xb285e8: r2 = Instance_MainAxisSize
    //     0xb285e8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb285ec: ldr             x2, [x2, #0xa10]
    // 0xb285f0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb285f0: stur            w2, [x1, #0x17]
    // 0xb285f4: r2 = Instance_CrossAxisAlignment
    //     0xb285f4: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb285f8: ldr             x2, [x2, #0x890]
    // 0xb285fc: StoreField: r1->field_1b = r2
    //     0xb285fc: stur            w2, [x1, #0x1b]
    // 0xb28600: r2 = Instance_VerticalDirection
    //     0xb28600: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb28604: ldr             x2, [x2, #0xa20]
    // 0xb28608: StoreField: r1->field_23 = r2
    //     0xb28608: stur            w2, [x1, #0x23]
    // 0xb2860c: r2 = Instance_Clip
    //     0xb2860c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb28610: ldr             x2, [x2, #0x38]
    // 0xb28614: StoreField: r1->field_2b = r2
    //     0xb28614: stur            w2, [x1, #0x2b]
    // 0xb28618: StoreField: r1->field_2f = rZR
    //     0xb28618: stur            xzr, [x1, #0x2f]
    // 0xb2861c: ldur            x3, [fp, #-0x10]
    // 0xb28620: StoreField: r1->field_b = r3
    //     0xb28620: stur            w3, [x1, #0xb]
    // 0xb28624: r0 = Material()
    //     0xb28624: bl              #0xaabb1c  ; AllocateMaterialStub -> Material (size=0x44)
    // 0xb28628: mov             x1, x0
    // 0xb2862c: r0 = Instance_MaterialType
    //     0xb2862c: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bd20] Obj!MaterialType@d741e1
    //     0xb28630: ldr             x0, [x0, #0xd20]
    // 0xb28634: stur            x1, [fp, #-0x10]
    // 0xb28638: StoreField: r1->field_f = r0
    //     0xb28638: stur            w0, [x1, #0xf]
    // 0xb2863c: d0 = 2.000000
    //     0xb2863c: fmov            d0, #2.00000000
    // 0xb28640: ArrayStore: r1[0] = d0  ; List_8
    //     0xb28640: stur            d0, [x1, #0x17]
    // 0xb28644: r0 = Instance_Color
    //     0xb28644: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb28648: StoreField: r1->field_1f = r0
    //     0xb28648: stur            w0, [x1, #0x1f]
    // 0xb2864c: r0 = true
    //     0xb2864c: add             x0, NULL, #0x20  ; true
    // 0xb28650: StoreField: r1->field_33 = r0
    //     0xb28650: stur            w0, [x1, #0x33]
    // 0xb28654: r0 = Instance_Clip
    //     0xb28654: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb28658: ldr             x0, [x0, #0x38]
    // 0xb2865c: StoreField: r1->field_37 = r0
    //     0xb2865c: stur            w0, [x1, #0x37]
    // 0xb28660: r0 = Instance_Duration
    //     0xb28660: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e150] Obj!Duration@d77761
    //     0xb28664: ldr             x0, [x0, #0x150]
    // 0xb28668: StoreField: r1->field_3b = r0
    //     0xb28668: stur            w0, [x1, #0x3b]
    // 0xb2866c: ldur            x0, [fp, #-8]
    // 0xb28670: StoreField: r1->field_b = r0
    //     0xb28670: stur            w0, [x1, #0xb]
    // 0xb28674: r0 = false
    //     0xb28674: add             x0, NULL, #0x30  ; false
    // 0xb28678: StoreField: r1->field_13 = r0
    //     0xb28678: stur            w0, [x1, #0x13]
    // 0xb2867c: r0 = Padding()
    //     0xb2867c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb28680: mov             x2, x0
    // 0xb28684: r0 = Instance_EdgeInsets
    //     0xb28684: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ca0] Obj!EdgeInsets@d58b81
    //     0xb28688: ldr             x0, [x0, #0xca0]
    // 0xb2868c: stur            x2, [fp, #-8]
    // 0xb28690: StoreField: r2->field_f = r0
    //     0xb28690: stur            w0, [x2, #0xf]
    // 0xb28694: ldur            x0, [fp, #-0x10]
    // 0xb28698: StoreField: r2->field_b = r0
    //     0xb28698: stur            w0, [x2, #0xb]
    // 0xb2869c: ldur            x0, [fp, #-0x20]
    // 0xb286a0: LoadField: r1 = r0->field_b
    //     0xb286a0: ldur            w1, [x0, #0xb]
    // 0xb286a4: LoadField: r3 = r0->field_f
    //     0xb286a4: ldur            w3, [x0, #0xf]
    // 0xb286a8: DecompressPointer r3
    //     0xb286a8: add             x3, x3, HEAP, lsl #32
    // 0xb286ac: LoadField: r4 = r3->field_b
    //     0xb286ac: ldur            w4, [x3, #0xb]
    // 0xb286b0: r3 = LoadInt32Instr(r1)
    //     0xb286b0: sbfx            x3, x1, #1, #0x1f
    // 0xb286b4: stur            x3, [fp, #-0x60]
    // 0xb286b8: r1 = LoadInt32Instr(r4)
    //     0xb286b8: sbfx            x1, x4, #1, #0x1f
    // 0xb286bc: cmp             x3, x1
    // 0xb286c0: b.ne            #0xb286cc
    // 0xb286c4: mov             x1, x0
    // 0xb286c8: r0 = _growToNextCapacity()
    //     0xb286c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb286cc: ldur            x2, [fp, #-0x20]
    // 0xb286d0: ldur            x3, [fp, #-0x60]
    // 0xb286d4: add             x0, x3, #1
    // 0xb286d8: lsl             x1, x0, #1
    // 0xb286dc: StoreField: r2->field_b = r1
    //     0xb286dc: stur            w1, [x2, #0xb]
    // 0xb286e0: LoadField: r1 = r2->field_f
    //     0xb286e0: ldur            w1, [x2, #0xf]
    // 0xb286e4: DecompressPointer r1
    //     0xb286e4: add             x1, x1, HEAP, lsl #32
    // 0xb286e8: ldur            x0, [fp, #-8]
    // 0xb286ec: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb286ec: add             x25, x1, x3, lsl #2
    //     0xb286f0: add             x25, x25, #0xf
    //     0xb286f4: str             w0, [x25]
    //     0xb286f8: tbz             w0, #0, #0xb28714
    //     0xb286fc: ldurb           w16, [x1, #-1]
    //     0xb28700: ldurb           w17, [x0, #-1]
    //     0xb28704: and             x16, x17, x16, lsr #2
    //     0xb28708: tst             x16, HEAP, lsr #32
    //     0xb2870c: b.eq            #0xb28714
    //     0xb28710: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb28714: r0 = Stack()
    //     0xb28714: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb28718: mov             x1, x0
    // 0xb2871c: r0 = Instance_Alignment
    //     0xb2871c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb28720: ldr             x0, [x0, #0xfa0]
    // 0xb28724: stur            x1, [fp, #-8]
    // 0xb28728: StoreField: r1->field_f = r0
    //     0xb28728: stur            w0, [x1, #0xf]
    // 0xb2872c: r0 = Instance_StackFit
    //     0xb2872c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb28730: ldr             x0, [x0, #0xfa8]
    // 0xb28734: ArrayStore: r1[0] = r0  ; List_4
    //     0xb28734: stur            w0, [x1, #0x17]
    // 0xb28738: r0 = Instance_Clip
    //     0xb28738: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb2873c: ldr             x0, [x0, #0x7e0]
    // 0xb28740: StoreField: r1->field_1b = r0
    //     0xb28740: stur            w0, [x1, #0x1b]
    // 0xb28744: ldur            x2, [fp, #-0x20]
    // 0xb28748: StoreField: r1->field_b = r2
    //     0xb28748: stur            w2, [x1, #0xb]
    // 0xb2874c: r0 = SingleChildScrollView()
    //     0xb2874c: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xb28750: r1 = Instance_Axis
    //     0xb28750: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb28754: StoreField: r0->field_b = r1
    //     0xb28754: stur            w1, [x0, #0xb]
    // 0xb28758: r1 = false
    //     0xb28758: add             x1, NULL, #0x30  ; false
    // 0xb2875c: StoreField: r0->field_f = r1
    //     0xb2875c: stur            w1, [x0, #0xf]
    // 0xb28760: ldur            x1, [fp, #-8]
    // 0xb28764: StoreField: r0->field_23 = r1
    //     0xb28764: stur            w1, [x0, #0x23]
    // 0xb28768: r1 = Instance_DragStartBehavior
    //     0xb28768: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xb2876c: StoreField: r0->field_27 = r1
    //     0xb2876c: stur            w1, [x0, #0x27]
    // 0xb28770: r1 = Instance_Clip
    //     0xb28770: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb28774: ldr             x1, [x1, #0x7e0]
    // 0xb28778: StoreField: r0->field_2b = r1
    //     0xb28778: stur            w1, [x0, #0x2b]
    // 0xb2877c: r1 = Instance_HitTestBehavior
    //     0xb2877c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xb28780: ldr             x1, [x1, #0x288]
    // 0xb28784: StoreField: r0->field_2f = r1
    //     0xb28784: stur            w1, [x0, #0x2f]
    // 0xb28788: LeaveFrame
    //     0xb28788: mov             SP, fp
    //     0xb2878c: ldp             fp, lr, [SP], #0x10
    // 0xb28790: ret
    //     0xb28790: ret             
    // 0xb28794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb28794: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb28798: b               #0xb27624
    // 0xb2879c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2879c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb287a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb287a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb287a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb287a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb287a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb287a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb287ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb287ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb287b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb287b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb287b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb287b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] GestureDetector <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb287e8, size: 0x474
    // 0xb287e8: EnterFrame
    //     0xb287e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb287ec: mov             fp, SP
    // 0xb287f0: AllocStack(0x50)
    //     0xb287f0: sub             SP, SP, #0x50
    // 0xb287f4: SetupParameters()
    //     0xb287f4: ldr             x0, [fp, #0x20]
    //     0xb287f8: ldur            w1, [x0, #0x17]
    //     0xb287fc: add             x1, x1, HEAP, lsl #32
    //     0xb28800: stur            x1, [fp, #-8]
    // 0xb28804: CheckStackOverflow
    //     0xb28804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb28808: cmp             SP, x16
    //     0xb2880c: b.ls            #0xb28c4c
    // 0xb28810: r1 = 1
    //     0xb28810: movz            x1, #0x1
    // 0xb28814: r0 = AllocateContext()
    //     0xb28814: bl              #0x16f6108  ; AllocateContextStub
    // 0xb28818: mov             x3, x0
    // 0xb2881c: ldur            x0, [fp, #-8]
    // 0xb28820: stur            x3, [fp, #-0x18]
    // 0xb28824: StoreField: r3->field_b = r0
    //     0xb28824: stur            w0, [x3, #0xb]
    // 0xb28828: LoadField: r1 = r0->field_f
    //     0xb28828: ldur            w1, [x0, #0xf]
    // 0xb2882c: DecompressPointer r1
    //     0xb2882c: add             x1, x1, HEAP, lsl #32
    // 0xb28830: LoadField: r0 = r1->field_b
    //     0xb28830: ldur            w0, [x1, #0xb]
    // 0xb28834: DecompressPointer r0
    //     0xb28834: add             x0, x0, HEAP, lsl #32
    // 0xb28838: cmp             w0, NULL
    // 0xb2883c: b.eq            #0xb28c54
    // 0xb28840: LoadField: r1 = r0->field_43
    //     0xb28840: ldur            w1, [x0, #0x43]
    // 0xb28844: DecompressPointer r1
    //     0xb28844: add             x1, x1, HEAP, lsl #32
    // 0xb28848: LoadField: r2 = r1->field_b
    //     0xb28848: ldur            w2, [x1, #0xb]
    // 0xb2884c: DecompressPointer r2
    //     0xb2884c: add             x2, x2, HEAP, lsl #32
    // 0xb28850: cmp             w2, NULL
    // 0xb28854: b.ne            #0xb28860
    // 0xb28858: r0 = Null
    //     0xb28858: mov             x0, NULL
    // 0xb2885c: b               #0xb2889c
    // 0xb28860: ldr             x0, [fp, #0x10]
    // 0xb28864: LoadField: r1 = r2->field_b
    //     0xb28864: ldur            w1, [x2, #0xb]
    // 0xb28868: r4 = LoadInt32Instr(r0)
    //     0xb28868: sbfx            x4, x0, #1, #0x1f
    //     0xb2886c: tbz             w0, #0, #0xb28874
    //     0xb28870: ldur            x4, [x0, #7]
    // 0xb28874: r0 = LoadInt32Instr(r1)
    //     0xb28874: sbfx            x0, x1, #1, #0x1f
    // 0xb28878: mov             x1, x4
    // 0xb2887c: cmp             x1, x0
    // 0xb28880: b.hs            #0xb28c58
    // 0xb28884: LoadField: r0 = r2->field_f
    //     0xb28884: ldur            w0, [x2, #0xf]
    // 0xb28888: DecompressPointer r0
    //     0xb28888: add             x0, x0, HEAP, lsl #32
    // 0xb2888c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb2888c: add             x16, x0, x4, lsl #2
    //     0xb28890: ldur            w1, [x16, #0xf]
    // 0xb28894: DecompressPointer r1
    //     0xb28894: add             x1, x1, HEAP, lsl #32
    // 0xb28898: mov             x0, x1
    // 0xb2889c: stur            x0, [fp, #-0x10]
    // 0xb288a0: StoreField: r3->field_f = r0
    //     0xb288a0: stur            w0, [x3, #0xf]
    // 0xb288a4: cmp             w0, NULL
    // 0xb288a8: b.ne            #0xb288b4
    // 0xb288ac: r1 = Null
    //     0xb288ac: mov             x1, NULL
    // 0xb288b0: b               #0xb288bc
    // 0xb288b4: LoadField: r1 = r0->field_7
    //     0xb288b4: ldur            w1, [x0, #7]
    // 0xb288b8: DecompressPointer r1
    //     0xb288b8: add             x1, x1, HEAP, lsl #32
    // 0xb288bc: cmp             w1, NULL
    // 0xb288c0: b.ne            #0xb288cc
    // 0xb288c4: r4 = ""
    //     0xb288c4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb288c8: b               #0xb288d0
    // 0xb288cc: mov             x4, x1
    // 0xb288d0: stur            x4, [fp, #-8]
    // 0xb288d4: r1 = Function '<anonymous closure>':.
    //     0xb288d4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57428] AnonymousClosure: (0xaac6d0), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb288d8: ldr             x1, [x1, #0x428]
    // 0xb288dc: r2 = Null
    //     0xb288dc: mov             x2, NULL
    // 0xb288e0: r0 = AllocateClosure()
    //     0xb288e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb288e4: stur            x0, [fp, #-0x20]
    // 0xb288e8: r0 = CachedNetworkImage()
    //     0xb288e8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb288ec: stur            x0, [fp, #-0x28]
    // 0xb288f0: r16 = 35.000000
    //     0xb288f0: add             x16, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xb288f4: ldr             x16, [x16, #0x2b0]
    // 0xb288f8: r30 = 35.000000
    //     0xb288f8: add             lr, PP, #0x37, lsl #12  ; [pp+0x372b0] 35
    //     0xb288fc: ldr             lr, [lr, #0x2b0]
    // 0xb28900: stp             lr, x16, [SP, #0x10]
    // 0xb28904: r16 = Instance_BoxFit
    //     0xb28904: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb28908: ldr             x16, [x16, #0x118]
    // 0xb2890c: ldur            lr, [fp, #-0x20]
    // 0xb28910: stp             lr, x16, [SP]
    // 0xb28914: mov             x1, x0
    // 0xb28918: ldur            x2, [fp, #-8]
    // 0xb2891c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb2891c: add             x4, PP, #0x54, lsl #12  ; [pp+0x54df8] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb28920: ldr             x4, [x4, #0xdf8]
    // 0xb28924: r0 = CachedNetworkImage()
    //     0xb28924: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb28928: ldur            x0, [fp, #-0x10]
    // 0xb2892c: cmp             w0, NULL
    // 0xb28930: b.ne            #0xb2893c
    // 0xb28934: r1 = Null
    //     0xb28934: mov             x1, NULL
    // 0xb28938: b               #0xb28944
    // 0xb2893c: LoadField: r1 = r0->field_b
    //     0xb2893c: ldur            w1, [x0, #0xb]
    // 0xb28940: DecompressPointer r1
    //     0xb28940: add             x1, x1, HEAP, lsl #32
    // 0xb28944: cmp             w1, NULL
    // 0xb28948: b.ne            #0xb28954
    // 0xb2894c: r2 = ""
    //     0xb2894c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb28950: b               #0xb28958
    // 0xb28954: mov             x2, x1
    // 0xb28958: ldr             x1, [fp, #0x18]
    // 0xb2895c: stur            x2, [fp, #-8]
    // 0xb28960: r0 = of()
    //     0xb28960: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb28964: LoadField: r1 = r0->field_87
    //     0xb28964: ldur            w1, [x0, #0x87]
    // 0xb28968: DecompressPointer r1
    //     0xb28968: add             x1, x1, HEAP, lsl #32
    // 0xb2896c: LoadField: r0 = r1->field_2b
    //     0xb2896c: ldur            w0, [x1, #0x2b]
    // 0xb28970: DecompressPointer r0
    //     0xb28970: add             x0, x0, HEAP, lsl #32
    // 0xb28974: r16 = Instance_Color
    //     0xb28974: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb28978: r30 = 12.000000
    //     0xb28978: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2897c: ldr             lr, [lr, #0x9e8]
    // 0xb28980: stp             lr, x16, [SP]
    // 0xb28984: mov             x1, x0
    // 0xb28988: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb28988: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb2898c: ldr             x4, [x4, #0x9b8]
    // 0xb28990: r0 = copyWith()
    //     0xb28990: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb28994: stur            x0, [fp, #-0x20]
    // 0xb28998: r0 = Text()
    //     0xb28998: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2899c: mov             x2, x0
    // 0xb289a0: ldur            x0, [fp, #-8]
    // 0xb289a4: stur            x2, [fp, #-0x30]
    // 0xb289a8: StoreField: r2->field_b = r0
    //     0xb289a8: stur            w0, [x2, #0xb]
    // 0xb289ac: ldur            x0, [fp, #-0x20]
    // 0xb289b0: StoreField: r2->field_13 = r0
    //     0xb289b0: stur            w0, [x2, #0x13]
    // 0xb289b4: ldur            x0, [fp, #-0x10]
    // 0xb289b8: cmp             w0, NULL
    // 0xb289bc: b.ne            #0xb289c8
    // 0xb289c0: r0 = Null
    //     0xb289c0: mov             x0, NULL
    // 0xb289c4: b               #0xb289d4
    // 0xb289c8: LoadField: r1 = r0->field_f
    //     0xb289c8: ldur            w1, [x0, #0xf]
    // 0xb289cc: DecompressPointer r1
    //     0xb289cc: add             x1, x1, HEAP, lsl #32
    // 0xb289d0: mov             x0, x1
    // 0xb289d4: cmp             w0, NULL
    // 0xb289d8: b.ne            #0xb289e4
    // 0xb289dc: r3 = ""
    //     0xb289dc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb289e0: b               #0xb289e8
    // 0xb289e4: mov             x3, x0
    // 0xb289e8: ldur            x0, [fp, #-0x28]
    // 0xb289ec: ldr             x1, [fp, #0x18]
    // 0xb289f0: stur            x3, [fp, #-8]
    // 0xb289f4: r0 = of()
    //     0xb289f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb289f8: LoadField: r1 = r0->field_87
    //     0xb289f8: ldur            w1, [x0, #0x87]
    // 0xb289fc: DecompressPointer r1
    //     0xb289fc: add             x1, x1, HEAP, lsl #32
    // 0xb28a00: LoadField: r0 = r1->field_2b
    //     0xb28a00: ldur            w0, [x1, #0x2b]
    // 0xb28a04: DecompressPointer r0
    //     0xb28a04: add             x0, x0, HEAP, lsl #32
    // 0xb28a08: r16 = Instance_Color
    //     0xb28a08: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb28a0c: r30 = 12.000000
    //     0xb28a0c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb28a10: ldr             lr, [lr, #0x9e8]
    // 0xb28a14: stp             lr, x16, [SP]
    // 0xb28a18: mov             x1, x0
    // 0xb28a1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb28a1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb28a20: ldr             x4, [x4, #0x9b8]
    // 0xb28a24: r0 = copyWith()
    //     0xb28a24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb28a28: stur            x0, [fp, #-0x10]
    // 0xb28a2c: r0 = Text()
    //     0xb28a2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb28a30: mov             x3, x0
    // 0xb28a34: ldur            x0, [fp, #-8]
    // 0xb28a38: stur            x3, [fp, #-0x20]
    // 0xb28a3c: StoreField: r3->field_b = r0
    //     0xb28a3c: stur            w0, [x3, #0xb]
    // 0xb28a40: ldur            x0, [fp, #-0x10]
    // 0xb28a44: StoreField: r3->field_13 = r0
    //     0xb28a44: stur            w0, [x3, #0x13]
    // 0xb28a48: r0 = Instance_TextOverflow
    //     0xb28a48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb28a4c: ldr             x0, [x0, #0xe10]
    // 0xb28a50: StoreField: r3->field_2b = r0
    //     0xb28a50: stur            w0, [x3, #0x2b]
    // 0xb28a54: r0 = 2
    //     0xb28a54: movz            x0, #0x2
    // 0xb28a58: StoreField: r3->field_37 = r0
    //     0xb28a58: stur            w0, [x3, #0x37]
    // 0xb28a5c: r1 = Null
    //     0xb28a5c: mov             x1, NULL
    // 0xb28a60: r2 = 6
    //     0xb28a60: movz            x2, #0x6
    // 0xb28a64: r0 = AllocateArray()
    //     0xb28a64: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb28a68: mov             x2, x0
    // 0xb28a6c: ldur            x0, [fp, #-0x30]
    // 0xb28a70: stur            x2, [fp, #-8]
    // 0xb28a74: StoreField: r2->field_f = r0
    //     0xb28a74: stur            w0, [x2, #0xf]
    // 0xb28a78: r16 = Instance_SizedBox
    //     0xb28a78: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb28a7c: ldr             x16, [x16, #0xc70]
    // 0xb28a80: StoreField: r2->field_13 = r16
    //     0xb28a80: stur            w16, [x2, #0x13]
    // 0xb28a84: ldur            x0, [fp, #-0x20]
    // 0xb28a88: ArrayStore: r2[0] = r0  ; List_4
    //     0xb28a88: stur            w0, [x2, #0x17]
    // 0xb28a8c: r1 = <Widget>
    //     0xb28a8c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb28a90: r0 = AllocateGrowableArray()
    //     0xb28a90: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb28a94: mov             x1, x0
    // 0xb28a98: ldur            x0, [fp, #-8]
    // 0xb28a9c: stur            x1, [fp, #-0x10]
    // 0xb28aa0: StoreField: r1->field_f = r0
    //     0xb28aa0: stur            w0, [x1, #0xf]
    // 0xb28aa4: r2 = 6
    //     0xb28aa4: movz            x2, #0x6
    // 0xb28aa8: StoreField: r1->field_b = r2
    //     0xb28aa8: stur            w2, [x1, #0xb]
    // 0xb28aac: r0 = Column()
    //     0xb28aac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb28ab0: mov             x2, x0
    // 0xb28ab4: r0 = Instance_Axis
    //     0xb28ab4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb28ab8: stur            x2, [fp, #-8]
    // 0xb28abc: StoreField: r2->field_f = r0
    //     0xb28abc: stur            w0, [x2, #0xf]
    // 0xb28ac0: r0 = Instance_MainAxisAlignment
    //     0xb28ac0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb28ac4: ldr             x0, [x0, #0xa08]
    // 0xb28ac8: StoreField: r2->field_13 = r0
    //     0xb28ac8: stur            w0, [x2, #0x13]
    // 0xb28acc: r3 = Instance_MainAxisSize
    //     0xb28acc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb28ad0: ldr             x3, [x3, #0xa10]
    // 0xb28ad4: ArrayStore: r2[0] = r3  ; List_4
    //     0xb28ad4: stur            w3, [x2, #0x17]
    // 0xb28ad8: r1 = Instance_CrossAxisAlignment
    //     0xb28ad8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb28adc: ldr             x1, [x1, #0x890]
    // 0xb28ae0: StoreField: r2->field_1b = r1
    //     0xb28ae0: stur            w1, [x2, #0x1b]
    // 0xb28ae4: r4 = Instance_VerticalDirection
    //     0xb28ae4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb28ae8: ldr             x4, [x4, #0xa20]
    // 0xb28aec: StoreField: r2->field_23 = r4
    //     0xb28aec: stur            w4, [x2, #0x23]
    // 0xb28af0: r5 = Instance_Clip
    //     0xb28af0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb28af4: ldr             x5, [x5, #0x38]
    // 0xb28af8: StoreField: r2->field_2b = r5
    //     0xb28af8: stur            w5, [x2, #0x2b]
    // 0xb28afc: StoreField: r2->field_2f = rZR
    //     0xb28afc: stur            xzr, [x2, #0x2f]
    // 0xb28b00: ldur            x1, [fp, #-0x10]
    // 0xb28b04: StoreField: r2->field_b = r1
    //     0xb28b04: stur            w1, [x2, #0xb]
    // 0xb28b08: r1 = <FlexParentData>
    //     0xb28b08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb28b0c: ldr             x1, [x1, #0xe00]
    // 0xb28b10: r0 = Expanded()
    //     0xb28b10: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb28b14: mov             x3, x0
    // 0xb28b18: r0 = 1
    //     0xb28b18: movz            x0, #0x1
    // 0xb28b1c: stur            x3, [fp, #-0x10]
    // 0xb28b20: StoreField: r3->field_13 = r0
    //     0xb28b20: stur            x0, [x3, #0x13]
    // 0xb28b24: r0 = Instance_FlexFit
    //     0xb28b24: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb28b28: ldr             x0, [x0, #0xe08]
    // 0xb28b2c: StoreField: r3->field_1b = r0
    //     0xb28b2c: stur            w0, [x3, #0x1b]
    // 0xb28b30: ldur            x0, [fp, #-8]
    // 0xb28b34: StoreField: r3->field_b = r0
    //     0xb28b34: stur            w0, [x3, #0xb]
    // 0xb28b38: r1 = Null
    //     0xb28b38: mov             x1, NULL
    // 0xb28b3c: r2 = 6
    //     0xb28b3c: movz            x2, #0x6
    // 0xb28b40: r0 = AllocateArray()
    //     0xb28b40: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb28b44: mov             x2, x0
    // 0xb28b48: ldur            x0, [fp, #-0x28]
    // 0xb28b4c: stur            x2, [fp, #-8]
    // 0xb28b50: StoreField: r2->field_f = r0
    //     0xb28b50: stur            w0, [x2, #0xf]
    // 0xb28b54: r16 = Instance_SizedBox
    //     0xb28b54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xb28b58: ldr             x16, [x16, #0xb20]
    // 0xb28b5c: StoreField: r2->field_13 = r16
    //     0xb28b5c: stur            w16, [x2, #0x13]
    // 0xb28b60: ldur            x0, [fp, #-0x10]
    // 0xb28b64: ArrayStore: r2[0] = r0  ; List_4
    //     0xb28b64: stur            w0, [x2, #0x17]
    // 0xb28b68: r1 = <Widget>
    //     0xb28b68: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb28b6c: r0 = AllocateGrowableArray()
    //     0xb28b6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb28b70: mov             x1, x0
    // 0xb28b74: ldur            x0, [fp, #-8]
    // 0xb28b78: stur            x1, [fp, #-0x10]
    // 0xb28b7c: StoreField: r1->field_f = r0
    //     0xb28b7c: stur            w0, [x1, #0xf]
    // 0xb28b80: r0 = 6
    //     0xb28b80: movz            x0, #0x6
    // 0xb28b84: StoreField: r1->field_b = r0
    //     0xb28b84: stur            w0, [x1, #0xb]
    // 0xb28b88: r0 = Row()
    //     0xb28b88: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb28b8c: mov             x1, x0
    // 0xb28b90: r0 = Instance_Axis
    //     0xb28b90: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb28b94: stur            x1, [fp, #-8]
    // 0xb28b98: StoreField: r1->field_f = r0
    //     0xb28b98: stur            w0, [x1, #0xf]
    // 0xb28b9c: r0 = Instance_MainAxisAlignment
    //     0xb28b9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb28ba0: ldr             x0, [x0, #0xa08]
    // 0xb28ba4: StoreField: r1->field_13 = r0
    //     0xb28ba4: stur            w0, [x1, #0x13]
    // 0xb28ba8: r0 = Instance_MainAxisSize
    //     0xb28ba8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb28bac: ldr             x0, [x0, #0xa10]
    // 0xb28bb0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb28bb0: stur            w0, [x1, #0x17]
    // 0xb28bb4: r0 = Instance_CrossAxisAlignment
    //     0xb28bb4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb28bb8: ldr             x0, [x0, #0xa18]
    // 0xb28bbc: StoreField: r1->field_1b = r0
    //     0xb28bbc: stur            w0, [x1, #0x1b]
    // 0xb28bc0: r0 = Instance_VerticalDirection
    //     0xb28bc0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb28bc4: ldr             x0, [x0, #0xa20]
    // 0xb28bc8: StoreField: r1->field_23 = r0
    //     0xb28bc8: stur            w0, [x1, #0x23]
    // 0xb28bcc: r0 = Instance_Clip
    //     0xb28bcc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb28bd0: ldr             x0, [x0, #0x38]
    // 0xb28bd4: StoreField: r1->field_2b = r0
    //     0xb28bd4: stur            w0, [x1, #0x2b]
    // 0xb28bd8: StoreField: r1->field_2f = rZR
    //     0xb28bd8: stur            xzr, [x1, #0x2f]
    // 0xb28bdc: ldur            x0, [fp, #-0x10]
    // 0xb28be0: StoreField: r1->field_b = r0
    //     0xb28be0: stur            w0, [x1, #0xb]
    // 0xb28be4: r0 = Container()
    //     0xb28be4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb28be8: stur            x0, [fp, #-0x10]
    // 0xb28bec: r16 = Instance_EdgeInsets
    //     0xb28bec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb28bf0: ldr             x16, [x16, #0x980]
    // 0xb28bf4: ldur            lr, [fp, #-8]
    // 0xb28bf8: stp             lr, x16, [SP]
    // 0xb28bfc: mov             x1, x0
    // 0xb28c00: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xb28c00: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xb28c04: ldr             x4, [x4, #0x30]
    // 0xb28c08: r0 = Container()
    //     0xb28c08: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb28c0c: r0 = GestureDetector()
    //     0xb28c0c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb28c10: ldur            x2, [fp, #-0x18]
    // 0xb28c14: r1 = Function '<anonymous closure>':.
    //     0xb28c14: add             x1, PP, #0x57, lsl #12  ; [pp+0x57430] AnonymousClosure: (0xb28c5c), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb28c18: ldr             x1, [x1, #0x430]
    // 0xb28c1c: stur            x0, [fp, #-8]
    // 0xb28c20: r0 = AllocateClosure()
    //     0xb28c20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb28c24: ldur            x16, [fp, #-0x10]
    // 0xb28c28: stp             x16, x0, [SP]
    // 0xb28c2c: ldur            x1, [fp, #-8]
    // 0xb28c30: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb28c30: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb28c34: ldr             x4, [x4, #0xaf0]
    // 0xb28c38: r0 = GestureDetector()
    //     0xb28c38: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb28c3c: ldur            x0, [fp, #-8]
    // 0xb28c40: LeaveFrame
    //     0xb28c40: mov             SP, fp
    //     0xb28c44: ldp             fp, lr, [SP], #0x10
    // 0xb28c48: ret
    //     0xb28c48: ret             
    // 0xb28c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb28c4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb28c50: b               #0xb28810
    // 0xb28c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb28c54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb28c58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb28c58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb28c5c, size: 0x104
    // 0xb28c5c: EnterFrame
    //     0xb28c5c: stp             fp, lr, [SP, #-0x10]!
    //     0xb28c60: mov             fp, SP
    // 0xb28c64: AllocStack(0x30)
    //     0xb28c64: sub             SP, SP, #0x30
    // 0xb28c68: SetupParameters()
    //     0xb28c68: ldr             x0, [fp, #0x10]
    //     0xb28c6c: ldur            w2, [x0, #0x17]
    //     0xb28c70: add             x2, x2, HEAP, lsl #32
    //     0xb28c74: stur            x2, [fp, #-0x10]
    // 0xb28c78: CheckStackOverflow
    //     0xb28c78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb28c7c: cmp             SP, x16
    //     0xb28c80: b.ls            #0xb28d54
    // 0xb28c84: LoadField: r0 = r2->field_b
    //     0xb28c84: ldur            w0, [x2, #0xb]
    // 0xb28c88: DecompressPointer r0
    //     0xb28c88: add             x0, x0, HEAP, lsl #32
    // 0xb28c8c: stur            x0, [fp, #-8]
    // 0xb28c90: LoadField: r1 = r0->field_f
    //     0xb28c90: ldur            w1, [x0, #0xf]
    // 0xb28c94: DecompressPointer r1
    //     0xb28c94: add             x1, x1, HEAP, lsl #32
    // 0xb28c98: LoadField: r3 = r1->field_b
    //     0xb28c98: ldur            w3, [x1, #0xb]
    // 0xb28c9c: DecompressPointer r3
    //     0xb28c9c: add             x3, x3, HEAP, lsl #32
    // 0xb28ca0: cmp             w3, NULL
    // 0xb28ca4: b.eq            #0xb28d5c
    // 0xb28ca8: LoadField: r4 = r2->field_f
    //     0xb28ca8: ldur            w4, [x2, #0xf]
    // 0xb28cac: DecompressPointer r4
    //     0xb28cac: add             x4, x4, HEAP, lsl #32
    // 0xb28cb0: cmp             w4, NULL
    // 0xb28cb4: b.ne            #0xb28cc0
    // 0xb28cb8: r4 = Null
    //     0xb28cb8: mov             x4, NULL
    // 0xb28cbc: b               #0xb28ccc
    // 0xb28cc0: LoadField: r5 = r4->field_b
    //     0xb28cc0: ldur            w5, [x4, #0xb]
    // 0xb28cc4: DecompressPointer r5
    //     0xb28cc4: add             x5, x5, HEAP, lsl #32
    // 0xb28cc8: mov             x4, x5
    // 0xb28ccc: cmp             w4, NULL
    // 0xb28cd0: b.ne            #0xb28cd8
    // 0xb28cd4: r4 = " "
    //     0xb28cd4: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb28cd8: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xb28cd8: ldur            w5, [x1, #0x17]
    // 0xb28cdc: DecompressPointer r5
    //     0xb28cdc: add             x5, x5, HEAP, lsl #32
    // 0xb28ce0: LoadField: r1 = r5->field_27
    //     0xb28ce0: ldur            w1, [x5, #0x27]
    // 0xb28ce4: DecompressPointer r1
    //     0xb28ce4: add             x1, x1, HEAP, lsl #32
    // 0xb28ce8: LoadField: r5 = r1->field_7
    //     0xb28ce8: ldur            w5, [x1, #7]
    // 0xb28cec: DecompressPointer r5
    //     0xb28cec: add             x5, x5, HEAP, lsl #32
    // 0xb28cf0: LoadField: r1 = r3->field_3b
    //     0xb28cf0: ldur            w1, [x3, #0x3b]
    // 0xb28cf4: DecompressPointer r1
    //     0xb28cf4: add             x1, x1, HEAP, lsl #32
    // 0xb28cf8: stp             x4, x1, [SP, #8]
    // 0xb28cfc: str             x5, [SP]
    // 0xb28d00: r4 = 0
    //     0xb28d00: movz            x4, #0
    // 0xb28d04: ldr             x0, [SP, #0x10]
    // 0xb28d08: r16 = UnlinkedCall_0x613b5c
    //     0xb28d08: add             x16, PP, #0x57, lsl #12  ; [pp+0x57438] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb28d0c: add             x16, x16, #0x438
    // 0xb28d10: ldp             x5, lr, [x16]
    // 0xb28d14: blr             lr
    // 0xb28d18: ldur            x0, [fp, #-8]
    // 0xb28d1c: LoadField: r3 = r0->field_f
    //     0xb28d1c: ldur            w3, [x0, #0xf]
    // 0xb28d20: DecompressPointer r3
    //     0xb28d20: add             x3, x3, HEAP, lsl #32
    // 0xb28d24: ldur            x2, [fp, #-0x10]
    // 0xb28d28: stur            x3, [fp, #-0x18]
    // 0xb28d2c: r1 = Function '<anonymous closure>':.
    //     0xb28d2c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57448] AnonymousClosure: (0xaac0ac), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb28d30: ldr             x1, [x1, #0x448]
    // 0xb28d34: r0 = AllocateClosure()
    //     0xb28d34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb28d38: ldur            x1, [fp, #-0x18]
    // 0xb28d3c: mov             x2, x0
    // 0xb28d40: r0 = setState()
    //     0xb28d40: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb28d44: r0 = Null
    //     0xb28d44: mov             x0, NULL
    // 0xb28d48: LeaveFrame
    //     0xb28d48: mov             SP, fp
    //     0xb28d4c: ldp             fp, lr, [SP], #0x10
    // 0xb28d50: ret
    //     0xb28d50: ret             
    // 0xb28d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb28d54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb28d58: b               #0xb28c84
    // 0xb28d5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb28d5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0xb28d60, size: 0xb8
    // 0xb28d60: EnterFrame
    //     0xb28d60: stp             fp, lr, [SP, #-0x10]!
    //     0xb28d64: mov             fp, SP
    // 0xb28d68: AllocStack(0x38)
    //     0xb28d68: sub             SP, SP, #0x38
    // 0xb28d6c: SetupParameters()
    //     0xb28d6c: ldr             x0, [fp, #0x38]
    //     0xb28d70: ldur            w1, [x0, #0x17]
    //     0xb28d74: add             x1, x1, HEAP, lsl #32
    // 0xb28d78: CheckStackOverflow
    //     0xb28d78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb28d7c: cmp             SP, x16
    //     0xb28d80: b.ls            #0xb28e0c
    // 0xb28d84: LoadField: r0 = r1->field_f
    //     0xb28d84: ldur            w0, [x1, #0xf]
    // 0xb28d88: DecompressPointer r0
    //     0xb28d88: add             x0, x0, HEAP, lsl #32
    // 0xb28d8c: LoadField: r1 = r0->field_b
    //     0xb28d8c: ldur            w1, [x0, #0xb]
    // 0xb28d90: DecompressPointer r1
    //     0xb28d90: add             x1, x1, HEAP, lsl #32
    // 0xb28d94: stur            x1, [fp, #-8]
    // 0xb28d98: cmp             w1, NULL
    // 0xb28d9c: b.eq            #0xb28e14
    // 0xb28da0: ldr             x0, [fp, #0x10]
    // 0xb28da4: cmp             w0, NULL
    // 0xb28da8: b.ne            #0xb28db8
    // 0xb28dac: r0 = ProductRating()
    //     0xb28dac: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xb28db0: mov             x1, x0
    // 0xb28db4: b               #0xb28dbc
    // 0xb28db8: mov             x1, x0
    // 0xb28dbc: ldur            x0, [fp, #-8]
    // 0xb28dc0: LoadField: r2 = r0->field_2f
    //     0xb28dc0: ldur            w2, [x0, #0x2f]
    // 0xb28dc4: DecompressPointer r2
    //     0xb28dc4: add             x2, x2, HEAP, lsl #32
    // 0xb28dc8: ldr             x16, [fp, #0x30]
    // 0xb28dcc: stp             x16, x2, [SP, #0x20]
    // 0xb28dd0: ldr             x16, [fp, #0x28]
    // 0xb28dd4: ldr             lr, [fp, #0x20]
    // 0xb28dd8: stp             lr, x16, [SP, #0x10]
    // 0xb28ddc: ldr             x16, [fp, #0x18]
    // 0xb28de0: stp             x1, x16, [SP]
    // 0xb28de4: r4 = 0
    //     0xb28de4: movz            x4, #0
    // 0xb28de8: ldr             x0, [SP, #0x28]
    // 0xb28dec: r16 = UnlinkedCall_0x613b5c
    //     0xb28dec: add             x16, PP, #0x57, lsl #12  ; [pp+0x57450] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb28df0: add             x16, x16, #0x450
    // 0xb28df4: ldp             x5, lr, [x16]
    // 0xb28df8: blr             lr
    // 0xb28dfc: r0 = Null
    //     0xb28dfc: mov             x0, NULL
    // 0xb28e00: LeaveFrame
    //     0xb28e00: mov             SP, fp
    //     0xb28e04: ldp             fp, lr, [SP], #0x10
    // 0xb28e08: ret
    //     0xb28e08: ret             
    // 0xb28e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb28e0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb28e10: b               #0xb28d84
    // 0xb28e14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb28e14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String, String) {
    // ** addr: 0xb28e18, size: 0xac
    // 0xb28e18: EnterFrame
    //     0xb28e18: stp             fp, lr, [SP, #-0x10]!
    //     0xb28e1c: mov             fp, SP
    // 0xb28e20: AllocStack(0x48)
    //     0xb28e20: sub             SP, SP, #0x48
    // 0xb28e24: SetupParameters()
    //     0xb28e24: ldr             x0, [fp, #0x50]
    //     0xb28e28: ldur            w1, [x0, #0x17]
    //     0xb28e2c: add             x1, x1, HEAP, lsl #32
    // 0xb28e30: CheckStackOverflow
    //     0xb28e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb28e34: cmp             SP, x16
    //     0xb28e38: b.ls            #0xb28eb8
    // 0xb28e3c: LoadField: r0 = r1->field_f
    //     0xb28e3c: ldur            w0, [x1, #0xf]
    // 0xb28e40: DecompressPointer r0
    //     0xb28e40: add             x0, x0, HEAP, lsl #32
    // 0xb28e44: LoadField: r1 = r0->field_b
    //     0xb28e44: ldur            w1, [x0, #0xb]
    // 0xb28e48: DecompressPointer r1
    //     0xb28e48: add             x1, x1, HEAP, lsl #32
    // 0xb28e4c: cmp             w1, NULL
    // 0xb28e50: b.eq            #0xb28ec0
    // 0xb28e54: LoadField: r0 = r1->field_37
    //     0xb28e54: ldur            w0, [x1, #0x37]
    // 0xb28e58: DecompressPointer r0
    //     0xb28e58: add             x0, x0, HEAP, lsl #32
    // 0xb28e5c: ldr             x16, [fp, #0x48]
    // 0xb28e60: stp             x16, x0, [SP, #0x38]
    // 0xb28e64: ldr             x16, [fp, #0x40]
    // 0xb28e68: ldr             lr, [fp, #0x38]
    // 0xb28e6c: stp             lr, x16, [SP, #0x28]
    // 0xb28e70: ldr             x16, [fp, #0x30]
    // 0xb28e74: ldr             lr, [fp, #0x28]
    // 0xb28e78: stp             lr, x16, [SP, #0x18]
    // 0xb28e7c: ldr             x16, [fp, #0x20]
    // 0xb28e80: ldr             lr, [fp, #0x18]
    // 0xb28e84: stp             lr, x16, [SP, #8]
    // 0xb28e88: ldr             x16, [fp, #0x10]
    // 0xb28e8c: str             x16, [SP]
    // 0xb28e90: r4 = 0
    //     0xb28e90: movz            x4, #0
    // 0xb28e94: ldr             x0, [SP, #0x40]
    // 0xb28e98: r16 = UnlinkedCall_0x613b5c
    //     0xb28e98: add             x16, PP, #0x57, lsl #12  ; [pp+0x57460] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb28e9c: add             x16, x16, #0x460
    // 0xb28ea0: ldp             x5, lr, [x16]
    // 0xb28ea4: blr             lr
    // 0xb28ea8: r0 = Null
    //     0xb28ea8: mov             x0, NULL
    // 0xb28eac: LeaveFrame
    //     0xb28eac: mov             SP, fp
    //     0xb28eb0: ldp             fp, lr, [SP], #0x10
    // 0xb28eb4: ret
    //     0xb28eb4: ret             
    // 0xb28eb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb28eb8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb28ebc: b               #0xb28e3c
    // 0xb28ec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb28ec0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String) {
    // ** addr: 0xb28ec4, size: 0x98
    // 0xb28ec4: EnterFrame
    //     0xb28ec4: stp             fp, lr, [SP, #-0x10]!
    //     0xb28ec8: mov             fp, SP
    // 0xb28ecc: AllocStack(0x30)
    //     0xb28ecc: sub             SP, SP, #0x30
    // 0xb28ed0: SetupParameters()
    //     0xb28ed0: ldr             x0, [fp, #0x38]
    //     0xb28ed4: ldur            w1, [x0, #0x17]
    //     0xb28ed8: add             x1, x1, HEAP, lsl #32
    // 0xb28edc: CheckStackOverflow
    //     0xb28edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb28ee0: cmp             SP, x16
    //     0xb28ee4: b.ls            #0xb28f50
    // 0xb28ee8: LoadField: r0 = r1->field_f
    //     0xb28ee8: ldur            w0, [x1, #0xf]
    // 0xb28eec: DecompressPointer r0
    //     0xb28eec: add             x0, x0, HEAP, lsl #32
    // 0xb28ef0: LoadField: r1 = r0->field_b
    //     0xb28ef0: ldur            w1, [x0, #0xb]
    // 0xb28ef4: DecompressPointer r1
    //     0xb28ef4: add             x1, x1, HEAP, lsl #32
    // 0xb28ef8: cmp             w1, NULL
    // 0xb28efc: b.eq            #0xb28f58
    // 0xb28f00: LoadField: r0 = r1->field_33
    //     0xb28f00: ldur            w0, [x1, #0x33]
    // 0xb28f04: DecompressPointer r0
    //     0xb28f04: add             x0, x0, HEAP, lsl #32
    // 0xb28f08: ldr             x16, [fp, #0x30]
    // 0xb28f0c: stp             x16, x0, [SP, #0x20]
    // 0xb28f10: ldr             x16, [fp, #0x28]
    // 0xb28f14: ldr             lr, [fp, #0x20]
    // 0xb28f18: stp             lr, x16, [SP, #0x10]
    // 0xb28f1c: ldr             x16, [fp, #0x18]
    // 0xb28f20: ldr             lr, [fp, #0x10]
    // 0xb28f24: stp             lr, x16, [SP]
    // 0xb28f28: r4 = 0
    //     0xb28f28: movz            x4, #0
    // 0xb28f2c: ldr             x0, [SP, #0x28]
    // 0xb28f30: r16 = UnlinkedCall_0x613b5c
    //     0xb28f30: add             x16, PP, #0x57, lsl #12  ; [pp+0x57470] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb28f34: add             x16, x16, #0x470
    // 0xb28f38: ldp             x5, lr, [x16]
    // 0xb28f3c: blr             lr
    // 0xb28f40: r0 = Null
    //     0xb28f40: mov             x0, NULL
    // 0xb28f44: LeaveFrame
    //     0xb28f44: mov             SP, fp
    //     0xb28f48: ldp             fp, lr, [SP], #0x10
    // 0xb28f4c: ret
    //     0xb28f4c: ret             
    // 0xb28f50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb28f50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb28f54: b               #0xb28ee8
    // 0xb28f58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb28f58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, int, String) {
    // ** addr: 0xb28f5c, size: 0x8c
    // 0xb28f5c: EnterFrame
    //     0xb28f5c: stp             fp, lr, [SP, #-0x10]!
    //     0xb28f60: mov             fp, SP
    // 0xb28f64: AllocStack(0x20)
    //     0xb28f64: sub             SP, SP, #0x20
    // 0xb28f68: SetupParameters()
    //     0xb28f68: ldr             x0, [fp, #0x28]
    //     0xb28f6c: ldur            w1, [x0, #0x17]
    //     0xb28f70: add             x1, x1, HEAP, lsl #32
    // 0xb28f74: CheckStackOverflow
    //     0xb28f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb28f78: cmp             SP, x16
    //     0xb28f7c: b.ls            #0xb28fdc
    // 0xb28f80: LoadField: r0 = r1->field_f
    //     0xb28f80: ldur            w0, [x1, #0xf]
    // 0xb28f84: DecompressPointer r0
    //     0xb28f84: add             x0, x0, HEAP, lsl #32
    // 0xb28f88: LoadField: r1 = r0->field_b
    //     0xb28f88: ldur            w1, [x0, #0xb]
    // 0xb28f8c: DecompressPointer r1
    //     0xb28f8c: add             x1, x1, HEAP, lsl #32
    // 0xb28f90: cmp             w1, NULL
    // 0xb28f94: b.eq            #0xb28fe4
    // 0xb28f98: LoadField: r0 = r1->field_2b
    //     0xb28f98: ldur            w0, [x1, #0x2b]
    // 0xb28f9c: DecompressPointer r0
    //     0xb28f9c: add             x0, x0, HEAP, lsl #32
    // 0xb28fa0: ldr             x16, [fp, #0x20]
    // 0xb28fa4: stp             x16, x0, [SP, #0x10]
    // 0xb28fa8: ldr             x16, [fp, #0x18]
    // 0xb28fac: ldr             lr, [fp, #0x10]
    // 0xb28fb0: stp             lr, x16, [SP]
    // 0xb28fb4: r4 = 0
    //     0xb28fb4: movz            x4, #0
    // 0xb28fb8: ldr             x0, [SP, #0x18]
    // 0xb28fbc: r16 = UnlinkedCall_0x613b5c
    //     0xb28fbc: add             x16, PP, #0x57, lsl #12  ; [pp+0x57480] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb28fc0: add             x16, x16, #0x480
    // 0xb28fc4: ldp             x5, lr, [x16]
    // 0xb28fc8: blr             lr
    // 0xb28fcc: r0 = Null
    //     0xb28fcc: mov             x0, NULL
    // 0xb28fd0: LeaveFrame
    //     0xb28fd0: mov             SP, fp
    //     0xb28fd4: ldp             fp, lr, [SP], #0x10
    // 0xb28fd8: ret
    //     0xb28fd8: ret             
    // 0xb28fdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb28fdc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb28fe0: b               #0xb28f80
    // 0xb28fe4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb28fe4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xb28fe8, size: 0x84
    // 0xb28fe8: EnterFrame
    //     0xb28fe8: stp             fp, lr, [SP, #-0x10]!
    //     0xb28fec: mov             fp, SP
    // 0xb28ff0: AllocStack(0x10)
    //     0xb28ff0: sub             SP, SP, #0x10
    // 0xb28ff4: SetupParameters()
    //     0xb28ff4: ldr             x0, [fp, #0x18]
    //     0xb28ff8: ldur            w1, [x0, #0x17]
    //     0xb28ffc: add             x1, x1, HEAP, lsl #32
    //     0xb29000: stur            x1, [fp, #-8]
    // 0xb29004: CheckStackOverflow
    //     0xb29004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb29008: cmp             SP, x16
    //     0xb2900c: b.ls            #0xb29064
    // 0xb29010: r1 = 1
    //     0xb29010: movz            x1, #0x1
    // 0xb29014: r0 = AllocateContext()
    //     0xb29014: bl              #0x16f6108  ; AllocateContextStub
    // 0xb29018: mov             x1, x0
    // 0xb2901c: ldur            x0, [fp, #-8]
    // 0xb29020: StoreField: r1->field_b = r0
    //     0xb29020: stur            w0, [x1, #0xb]
    // 0xb29024: ldr             x2, [fp, #0x10]
    // 0xb29028: StoreField: r1->field_f = r2
    //     0xb29028: stur            w2, [x1, #0xf]
    // 0xb2902c: LoadField: r3 = r0->field_f
    //     0xb2902c: ldur            w3, [x0, #0xf]
    // 0xb29030: DecompressPointer r3
    //     0xb29030: add             x3, x3, HEAP, lsl #32
    // 0xb29034: mov             x2, x1
    // 0xb29038: stur            x3, [fp, #-0x10]
    // 0xb2903c: r1 = Function '<anonymous closure>':.
    //     0xb2903c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57490] AnonymousClosure: (0xb2906c), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb29040: ldr             x1, [x1, #0x490]
    // 0xb29044: r0 = AllocateClosure()
    //     0xb29044: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb29048: ldur            x1, [fp, #-0x10]
    // 0xb2904c: mov             x2, x0
    // 0xb29050: r0 = setState()
    //     0xb29050: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb29054: r0 = Null
    //     0xb29054: mov             x0, NULL
    // 0xb29058: LeaveFrame
    //     0xb29058: mov             SP, fp
    //     0xb2905c: ldp             fp, lr, [SP], #0x10
    // 0xb29060: ret
    //     0xb29060: ret             
    // 0xb29064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb29064: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb29068: b               #0xb29010
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2906c, size: 0x1dc
    // 0xb2906c: EnterFrame
    //     0xb2906c: stp             fp, lr, [SP, #-0x10]!
    //     0xb29070: mov             fp, SP
    // 0xb29074: AllocStack(0x38)
    //     0xb29074: sub             SP, SP, #0x38
    // 0xb29078: SetupParameters()
    //     0xb29078: ldr             x0, [fp, #0x10]
    //     0xb2907c: ldur            w3, [x0, #0x17]
    //     0xb29080: add             x3, x3, HEAP, lsl #32
    //     0xb29084: stur            x3, [fp, #-0x18]
    // 0xb29088: CheckStackOverflow
    //     0xb29088: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2908c: cmp             SP, x16
    //     0xb29090: b.ls            #0xb29234
    // 0xb29094: LoadField: r0 = r3->field_b
    //     0xb29094: ldur            w0, [x3, #0xb]
    // 0xb29098: DecompressPointer r0
    //     0xb29098: add             x0, x0, HEAP, lsl #32
    // 0xb2909c: stur            x0, [fp, #-0x10]
    // 0xb290a0: LoadField: r4 = r0->field_f
    //     0xb290a0: ldur            w4, [x0, #0xf]
    // 0xb290a4: DecompressPointer r4
    //     0xb290a4: add             x4, x4, HEAP, lsl #32
    // 0xb290a8: stur            x4, [fp, #-8]
    // 0xb290ac: LoadField: r2 = r3->field_f
    //     0xb290ac: ldur            w2, [x3, #0xf]
    // 0xb290b0: DecompressPointer r2
    //     0xb290b0: add             x2, x2, HEAP, lsl #32
    // 0xb290b4: r1 = _ConstMap len:5
    //     0xb290b4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51da8] Map<String, String>(5)
    //     0xb290b8: ldr             x1, [x1, #0xda8]
    // 0xb290bc: r0 = []()
    //     0xb290bc: bl              #0x16a3e7c  ; [dart:_compact_hash] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xb290c0: cmp             w0, NULL
    // 0xb290c4: b.ne            #0xb290e4
    // 0xb290c8: ldur            x1, [fp, #-0x10]
    // 0xb290cc: LoadField: r0 = r1->field_f
    //     0xb290cc: ldur            w0, [x1, #0xf]
    // 0xb290d0: DecompressPointer r0
    //     0xb290d0: add             x0, x0, HEAP, lsl #32
    // 0xb290d4: LoadField: r2 = r0->field_1b
    //     0xb290d4: ldur            w2, [x0, #0x1b]
    // 0xb290d8: DecompressPointer r2
    //     0xb290d8: add             x2, x2, HEAP, lsl #32
    // 0xb290dc: mov             x0, x2
    // 0xb290e0: b               #0xb290e8
    // 0xb290e4: ldur            x1, [fp, #-0x10]
    // 0xb290e8: ldur            x2, [fp, #-8]
    // 0xb290ec: StoreField: r2->field_1b = r0
    //     0xb290ec: stur            w0, [x2, #0x1b]
    //     0xb290f0: ldurb           w16, [x2, #-1]
    //     0xb290f4: ldurb           w17, [x0, #-1]
    //     0xb290f8: and             x16, x17, x16, lsr #2
    //     0xb290fc: tst             x16, HEAP, lsr #32
    //     0xb29100: b.eq            #0xb29108
    //     0xb29104: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb29108: LoadField: r0 = r1->field_f
    //     0xb29108: ldur            w0, [x1, #0xf]
    // 0xb2910c: DecompressPointer r0
    //     0xb2910c: add             x0, x0, HEAP, lsl #32
    // 0xb29110: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xb29110: ldur            w2, [x0, #0x17]
    // 0xb29114: DecompressPointer r2
    //     0xb29114: add             x2, x2, HEAP, lsl #32
    // 0xb29118: LoadField: r3 = r2->field_27
    //     0xb29118: ldur            w3, [x2, #0x27]
    // 0xb2911c: DecompressPointer r3
    //     0xb2911c: add             x3, x3, HEAP, lsl #32
    // 0xb29120: LoadField: r2 = r3->field_7
    //     0xb29120: ldur            w2, [x3, #7]
    // 0xb29124: DecompressPointer r2
    //     0xb29124: add             x2, x2, HEAP, lsl #32
    // 0xb29128: LoadField: r4 = r2->field_7
    //     0xb29128: ldur            w4, [x2, #7]
    // 0xb2912c: cbz             w4, #0xb29140
    // 0xb29130: LoadField: r2 = r3->field_7
    //     0xb29130: ldur            w2, [x3, #7]
    // 0xb29134: DecompressPointer r2
    //     0xb29134: add             x2, x2, HEAP, lsl #32
    // 0xb29138: mov             x4, x2
    // 0xb2913c: b               #0xb29144
    // 0xb29140: r4 = " "
    //     0xb29140: ldr             x4, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb29144: ldur            x2, [fp, #-0x18]
    // 0xb29148: LoadField: r5 = r0->field_b
    //     0xb29148: ldur            w5, [x0, #0xb]
    // 0xb2914c: DecompressPointer r5
    //     0xb2914c: add             x5, x5, HEAP, lsl #32
    // 0xb29150: cmp             w5, NULL
    // 0xb29154: b.eq            #0xb2923c
    // 0xb29158: LoadField: r6 = r0->field_1b
    //     0xb29158: ldur            w6, [x0, #0x1b]
    // 0xb2915c: DecompressPointer r6
    //     0xb2915c: add             x6, x6, HEAP, lsl #32
    // 0xb29160: LoadField: r0 = r3->field_7
    //     0xb29160: ldur            w0, [x3, #7]
    // 0xb29164: DecompressPointer r0
    //     0xb29164: add             x0, x0, HEAP, lsl #32
    // 0xb29168: LoadField: r3 = r5->field_27
    //     0xb29168: ldur            w3, [x5, #0x27]
    // 0xb2916c: DecompressPointer r3
    //     0xb2916c: add             x3, x3, HEAP, lsl #32
    // 0xb29170: stp             x4, x3, [SP, #0x10]
    // 0xb29174: stp             x0, x6, [SP]
    // 0xb29178: r4 = 0
    //     0xb29178: movz            x4, #0
    // 0xb2917c: ldr             x0, [SP, #0x18]
    // 0xb29180: r16 = UnlinkedCall_0x613b5c
    //     0xb29180: add             x16, PP, #0x57, lsl #12  ; [pp+0x57498] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb29184: add             x16, x16, #0x498
    // 0xb29188: ldp             x5, lr, [x16]
    // 0xb2918c: blr             lr
    // 0xb29190: ldur            x0, [fp, #-0x10]
    // 0xb29194: LoadField: r1 = r0->field_f
    //     0xb29194: ldur            w1, [x0, #0xf]
    // 0xb29198: DecompressPointer r1
    //     0xb29198: add             x1, x1, HEAP, lsl #32
    // 0xb2919c: LoadField: r3 = r1->field_b
    //     0xb2919c: ldur            w3, [x1, #0xb]
    // 0xb291a0: DecompressPointer r3
    //     0xb291a0: add             x3, x3, HEAP, lsl #32
    // 0xb291a4: stur            x3, [fp, #-0x10]
    // 0xb291a8: cmp             w3, NULL
    // 0xb291ac: b.eq            #0xb29240
    // 0xb291b0: ldur            x0, [fp, #-0x18]
    // 0xb291b4: LoadField: r4 = r0->field_f
    //     0xb291b4: ldur            w4, [x0, #0xf]
    // 0xb291b8: DecompressPointer r4
    //     0xb291b8: add             x4, x4, HEAP, lsl #32
    // 0xb291bc: stur            x4, [fp, #-8]
    // 0xb291c0: cmp             w4, NULL
    // 0xb291c4: b.eq            #0xb29244
    // 0xb291c8: mov             x0, x4
    // 0xb291cc: r2 = Null
    //     0xb291cc: mov             x2, NULL
    // 0xb291d0: r1 = Null
    //     0xb291d0: mov             x1, NULL
    // 0xb291d4: r4 = 60
    //     0xb291d4: movz            x4, #0x3c
    // 0xb291d8: branchIfSmi(r0, 0xb291e4)
    //     0xb291d8: tbz             w0, #0, #0xb291e4
    // 0xb291dc: r4 = LoadClassIdInstr(r0)
    //     0xb291dc: ldur            x4, [x0, #-1]
    //     0xb291e0: ubfx            x4, x4, #0xc, #0x14
    // 0xb291e4: sub             x4, x4, #0x5e
    // 0xb291e8: cmp             x4, #1
    // 0xb291ec: b.ls            #0xb29200
    // 0xb291f0: r8 = String
    //     0xb291f0: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xb291f4: r3 = Null
    //     0xb291f4: add             x3, PP, #0x57, lsl #12  ; [pp+0x574a8] Null
    //     0xb291f8: ldr             x3, [x3, #0x4a8]
    // 0xb291fc: r0 = String()
    //     0xb291fc: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xb29200: ldur            x0, [fp, #-8]
    // 0xb29204: ldur            x1, [fp, #-0x10]
    // 0xb29208: StoreField: r1->field_f = r0
    //     0xb29208: stur            w0, [x1, #0xf]
    //     0xb2920c: ldurb           w16, [x1, #-1]
    //     0xb29210: ldurb           w17, [x0, #-1]
    //     0xb29214: and             x16, x17, x16, lsr #2
    //     0xb29218: tst             x16, HEAP, lsr #32
    //     0xb2921c: b.eq            #0xb29224
    //     0xb29220: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb29224: r0 = Null
    //     0xb29224: mov             x0, NULL
    // 0xb29228: LeaveFrame
    //     0xb29228: mov             SP, fp
    //     0xb2922c: ldp             fp, lr, [SP], #0x10
    // 0xb29230: ret
    //     0xb29230: ret             
    // 0xb29234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb29234: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb29238: b               #0xb29094
    // 0xb2923c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2923c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb29240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb29240: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb29244: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb29244: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb29248, size: 0xa8
    // 0xb29248: EnterFrame
    //     0xb29248: stp             fp, lr, [SP, #-0x10]!
    //     0xb2924c: mov             fp, SP
    // 0xb29250: AllocStack(0x18)
    //     0xb29250: sub             SP, SP, #0x18
    // 0xb29254: SetupParameters()
    //     0xb29254: ldr             x0, [fp, #0x10]
    //     0xb29258: ldur            w2, [x0, #0x17]
    //     0xb2925c: add             x2, x2, HEAP, lsl #32
    //     0xb29260: stur            x2, [fp, #-8]
    // 0xb29264: CheckStackOverflow
    //     0xb29264: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb29268: cmp             SP, x16
    //     0xb2926c: b.ls            #0xb292e4
    // 0xb29270: LoadField: r0 = r2->field_f
    //     0xb29270: ldur            w0, [x2, #0xf]
    // 0xb29274: DecompressPointer r0
    //     0xb29274: add             x0, x0, HEAP, lsl #32
    // 0xb29278: LoadField: r1 = r0->field_b
    //     0xb29278: ldur            w1, [x0, #0xb]
    // 0xb2927c: DecompressPointer r1
    //     0xb2927c: add             x1, x1, HEAP, lsl #32
    // 0xb29280: cmp             w1, NULL
    // 0xb29284: b.eq            #0xb292ec
    // 0xb29288: LoadField: r0 = r1->field_23
    //     0xb29288: ldur            w0, [x1, #0x23]
    // 0xb2928c: DecompressPointer r0
    //     0xb2928c: add             x0, x0, HEAP, lsl #32
    // 0xb29290: str             x0, [SP]
    // 0xb29294: r4 = 0
    //     0xb29294: movz            x4, #0
    // 0xb29298: ldr             x0, [SP]
    // 0xb2929c: r16 = UnlinkedCall_0x613b5c
    //     0xb2929c: add             x16, PP, #0x57, lsl #12  ; [pp+0x574b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb292a0: add             x16, x16, #0x4b8
    // 0xb292a4: ldp             x5, lr, [x16]
    // 0xb292a8: blr             lr
    // 0xb292ac: ldur            x2, [fp, #-8]
    // 0xb292b0: LoadField: r0 = r2->field_f
    //     0xb292b0: ldur            w0, [x2, #0xf]
    // 0xb292b4: DecompressPointer r0
    //     0xb292b4: add             x0, x0, HEAP, lsl #32
    // 0xb292b8: stur            x0, [fp, #-0x10]
    // 0xb292bc: r1 = Function '<anonymous closure>':.
    //     0xb292bc: add             x1, PP, #0x57, lsl #12  ; [pp+0x574c8] AnonymousClosure: (0xaacdec), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb292c0: ldr             x1, [x1, #0x4c8]
    // 0xb292c4: r0 = AllocateClosure()
    //     0xb292c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb292c8: ldur            x1, [fp, #-0x10]
    // 0xb292cc: mov             x2, x0
    // 0xb292d0: r0 = setState()
    //     0xb292d0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb292d4: r0 = Null
    //     0xb292d4: mov             x0, NULL
    // 0xb292d8: LeaveFrame
    //     0xb292d8: mov             SP, fp
    //     0xb292dc: ldp             fp, lr, [SP], #0x10
    // 0xb292e0: ret
    //     0xb292e0: ret             
    // 0xb292e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb292e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb292e8: b               #0xb29270
    // 0xb292ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb292ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xb292f0, size: 0x188
    // 0xb292f0: EnterFrame
    //     0xb292f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb292f4: mov             fp, SP
    // 0xb292f8: AllocStack(0x38)
    //     0xb292f8: sub             SP, SP, #0x38
    // 0xb292fc: SetupParameters(_SearchViewState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xb292fc: stur            NULL, [fp, #-8]
    //     0xb29300: movz            x0, #0
    //     0xb29304: add             x1, fp, w0, sxtw #2
    //     0xb29308: ldr             x1, [x1, #0x18]
    //     0xb2930c: add             x2, fp, w0, sxtw #2
    //     0xb29310: ldr             x2, [x2, #0x10]
    //     0xb29314: stur            x2, [fp, #-0x18]
    //     0xb29318: ldur            w3, [x1, #0x17]
    //     0xb2931c: add             x3, x3, HEAP, lsl #32
    //     0xb29320: stur            x3, [fp, #-0x10]
    // 0xb29324: CheckStackOverflow
    //     0xb29324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb29328: cmp             SP, x16
    //     0xb2932c: b.ls            #0xb2946c
    // 0xb29330: InitAsync() -> Future<void?>
    //     0xb29330: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0xb29334: bl              #0x6326e0  ; InitAsyncStub
    // 0xb29338: r1 = 1
    //     0xb29338: movz            x1, #0x1
    // 0xb2933c: r0 = AllocateContext()
    //     0xb2933c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb29340: mov             x2, x0
    // 0xb29344: ldur            x0, [fp, #-0x10]
    // 0xb29348: stur            x2, [fp, #-0x20]
    // 0xb2934c: StoreField: r2->field_b = r0
    //     0xb2934c: stur            w0, [x2, #0xb]
    // 0xb29350: ldur            x1, [fp, #-0x18]
    // 0xb29354: r0 = trim()
    //     0xb29354: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb29358: mov             x1, x0
    // 0xb2935c: ldur            x2, [fp, #-0x20]
    // 0xb29360: StoreField: r2->field_f = r0
    //     0xb29360: stur            w0, [x2, #0xf]
    //     0xb29364: ldurb           w16, [x2, #-1]
    //     0xb29368: ldurb           w17, [x0, #-1]
    //     0xb2936c: and             x16, x17, x16, lsr #2
    //     0xb29370: tst             x16, HEAP, lsr #32
    //     0xb29374: b.eq            #0xb2937c
    //     0xb29378: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb2937c: LoadField: r0 = r1->field_7
    //     0xb2937c: ldur            w0, [x1, #7]
    // 0xb29380: cbz             w0, #0xb29438
    // 0xb29384: ldur            x3, [fp, #-0x10]
    // 0xb29388: r4 = true
    //     0xb29388: add             x4, NULL, #0x20  ; true
    // 0xb2938c: LoadField: r5 = r3->field_f
    //     0xb2938c: ldur            w5, [x3, #0xf]
    // 0xb29390: DecompressPointer r5
    //     0xb29390: add             x5, x5, HEAP, lsl #32
    // 0xb29394: stur            x5, [fp, #-0x28]
    // 0xb29398: StoreField: r5->field_13 = r4
    //     0xb29398: stur            w4, [x5, #0x13]
    // 0xb2939c: r4 = LoadInt32Instr(r0)
    //     0xb2939c: sbfx            x4, x0, #1, #0x1f
    // 0xb293a0: cmp             x4, #3
    // 0xb293a4: b.lt            #0xb29418
    // 0xb293a8: LoadField: r0 = r5->field_b
    //     0xb293a8: ldur            w0, [x5, #0xb]
    // 0xb293ac: DecompressPointer r0
    //     0xb293ac: add             x0, x0, HEAP, lsl #32
    // 0xb293b0: cmp             w0, NULL
    // 0xb293b4: b.eq            #0xb29474
    // 0xb293b8: LoadField: r4 = r0->field_1b
    //     0xb293b8: ldur            w4, [x0, #0x1b]
    // 0xb293bc: DecompressPointer r4
    //     0xb293bc: add             x4, x4, HEAP, lsl #32
    // 0xb293c0: stp             x1, x4, [SP]
    // 0xb293c4: r4 = 0
    //     0xb293c4: movz            x4, #0
    // 0xb293c8: ldr             x0, [SP, #8]
    // 0xb293cc: r16 = UnlinkedCall_0x613b5c
    //     0xb293cc: add             x16, PP, #0x57, lsl #12  ; [pp+0x574d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb293d0: add             x16, x16, #0x4d0
    // 0xb293d4: ldp             x5, lr, [x16]
    // 0xb293d8: blr             lr
    // 0xb293dc: mov             x1, x0
    // 0xb293e0: stur            x1, [fp, #-0x18]
    // 0xb293e4: r0 = Await()
    //     0xb293e4: bl              #0x63248c  ; AwaitStub
    // 0xb293e8: ldur            x0, [fp, #-0x10]
    // 0xb293ec: LoadField: r3 = r0->field_f
    //     0xb293ec: ldur            w3, [x0, #0xf]
    // 0xb293f0: DecompressPointer r3
    //     0xb293f0: add             x3, x3, HEAP, lsl #32
    // 0xb293f4: ldur            x2, [fp, #-0x20]
    // 0xb293f8: stur            x3, [fp, #-0x18]
    // 0xb293fc: r1 = Function '<anonymous closure>':.
    //     0xb293fc: add             x1, PP, #0x57, lsl #12  ; [pp+0x574e0] AnonymousClosure: (0xaad370), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb29400: ldr             x1, [x1, #0x4e0]
    // 0xb29404: r0 = AllocateClosure()
    //     0xb29404: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb29408: ldur            x1, [fp, #-0x18]
    // 0xb2940c: mov             x2, x0
    // 0xb29410: r0 = setState()
    //     0xb29410: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb29414: b               #0xb29464
    // 0xb29418: ldur            x2, [fp, #-0x20]
    // 0xb2941c: r1 = Function '<anonymous closure>':.
    //     0xb2941c: add             x1, PP, #0x57, lsl #12  ; [pp+0x574e8] AnonymousClosure: (0xaad124), in [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] _SearchViewState::build (0xc16ba0)
    //     0xb29420: ldr             x1, [x1, #0x4e8]
    // 0xb29424: r0 = AllocateClosure()
    //     0xb29424: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb29428: ldur            x1, [fp, #-0x28]
    // 0xb2942c: mov             x2, x0
    // 0xb29430: r0 = setState()
    //     0xb29430: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb29434: b               #0xb29464
    // 0xb29438: ldur            x0, [fp, #-0x10]
    // 0xb2943c: LoadField: r3 = r0->field_f
    //     0xb2943c: ldur            w3, [x0, #0xf]
    // 0xb29440: DecompressPointer r3
    //     0xb29440: add             x3, x3, HEAP, lsl #32
    // 0xb29444: ldur            x2, [fp, #-0x20]
    // 0xb29448: stur            x3, [fp, #-0x18]
    // 0xb2944c: r1 = Function '<anonymous closure>':.
    //     0xb2944c: add             x1, PP, #0x57, lsl #12  ; [pp+0x574f0] AnonymousClosure: (0xb29478), in [package:customer_app/app/presentation/views/cosmetic/search/widgets/search_view.dart] _SearchViewState::build (0xb275fc)
    //     0xb29450: ldr             x1, [x1, #0x4f0]
    // 0xb29454: r0 = AllocateClosure()
    //     0xb29454: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb29458: ldur            x1, [fp, #-0x18]
    // 0xb2945c: mov             x2, x0
    // 0xb29460: r0 = setState()
    //     0xb29460: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb29464: r0 = Null
    //     0xb29464: mov             x0, NULL
    // 0xb29468: r0 = ReturnAsyncNotFuture()
    //     0xb29468: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xb2946c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2946c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb29470: b               #0xb29330
    // 0xb29474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb29474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb29478, size: 0x98
    // 0xb29478: EnterFrame
    //     0xb29478: stp             fp, lr, [SP, #-0x10]!
    //     0xb2947c: mov             fp, SP
    // 0xb29480: AllocStack(0x10)
    //     0xb29480: sub             SP, SP, #0x10
    // 0xb29484: SetupParameters()
    //     0xb29484: add             x0, NULL, #0x30  ; false
    //     0xb29488: ldr             x1, [fp, #0x10]
    //     0xb2948c: ldur            w2, [x1, #0x17]
    //     0xb29490: add             x2, x2, HEAP, lsl #32
    // 0xb29484: r0 = false
    // 0xb29494: CheckStackOverflow
    //     0xb29494: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb29498: cmp             SP, x16
    //     0xb2949c: b.ls            #0xb29504
    // 0xb294a0: LoadField: r1 = r2->field_b
    //     0xb294a0: ldur            w1, [x2, #0xb]
    // 0xb294a4: DecompressPointer r1
    //     0xb294a4: add             x1, x1, HEAP, lsl #32
    // 0xb294a8: LoadField: r3 = r1->field_f
    //     0xb294a8: ldur            w3, [x1, #0xf]
    // 0xb294ac: DecompressPointer r3
    //     0xb294ac: add             x3, x3, HEAP, lsl #32
    // 0xb294b0: StoreField: r3->field_13 = r0
    //     0xb294b0: stur            w0, [x3, #0x13]
    // 0xb294b4: StoreField: r3->field_1f = r0
    //     0xb294b4: stur            w0, [x3, #0x1f]
    // 0xb294b8: LoadField: r0 = r3->field_b
    //     0xb294b8: ldur            w0, [x3, #0xb]
    // 0xb294bc: DecompressPointer r0
    //     0xb294bc: add             x0, x0, HEAP, lsl #32
    // 0xb294c0: cmp             w0, NULL
    // 0xb294c4: b.eq            #0xb2950c
    // 0xb294c8: LoadField: r1 = r2->field_f
    //     0xb294c8: ldur            w1, [x2, #0xf]
    // 0xb294cc: DecompressPointer r1
    //     0xb294cc: add             x1, x1, HEAP, lsl #32
    // 0xb294d0: LoadField: r2 = r0->field_1f
    //     0xb294d0: ldur            w2, [x0, #0x1f]
    // 0xb294d4: DecompressPointer r2
    //     0xb294d4: add             x2, x2, HEAP, lsl #32
    // 0xb294d8: stp             x1, x2, [SP]
    // 0xb294dc: r4 = 0
    //     0xb294dc: movz            x4, #0
    // 0xb294e0: ldr             x0, [SP, #8]
    // 0xb294e4: r16 = UnlinkedCall_0x613b5c
    //     0xb294e4: add             x16, PP, #0x57, lsl #12  ; [pp+0x574f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb294e8: add             x16, x16, #0x4f8
    // 0xb294ec: ldp             x5, lr, [x16]
    // 0xb294f0: blr             lr
    // 0xb294f4: r0 = Null
    //     0xb294f4: mov             x0, NULL
    // 0xb294f8: LeaveFrame
    //     0xb294f8: mov             SP, fp
    //     0xb294fc: ldp             fp, lr, [SP], #0x10
    // 0xb29500: ret
    //     0xb29500: ret             
    // 0xb29504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb29504: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb29508: b               #0xb294a0
    // 0xb2950c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2950c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0xb29510, size: 0xb0
    // 0xb29510: EnterFrame
    //     0xb29510: stp             fp, lr, [SP, #-0x10]!
    //     0xb29514: mov             fp, SP
    // 0xb29518: AllocStack(0x20)
    //     0xb29518: sub             SP, SP, #0x20
    // 0xb2951c: SetupParameters()
    //     0xb2951c: ldr             x0, [fp, #0x18]
    //     0xb29520: ldur            w2, [x0, #0x17]
    //     0xb29524: add             x2, x2, HEAP, lsl #32
    //     0xb29528: stur            x2, [fp, #-8]
    // 0xb2952c: CheckStackOverflow
    //     0xb2952c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb29530: cmp             SP, x16
    //     0xb29534: b.ls            #0xb295b4
    // 0xb29538: ldr             x1, [fp, #0x10]
    // 0xb2953c: r0 = trim()
    //     0xb2953c: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xb29540: mov             x1, x0
    // 0xb29544: ldur            x0, [fp, #-8]
    // 0xb29548: LoadField: r2 = r0->field_f
    //     0xb29548: ldur            w2, [x0, #0xf]
    // 0xb2954c: DecompressPointer r2
    //     0xb2954c: add             x2, x2, HEAP, lsl #32
    // 0xb29550: LoadField: r3 = r2->field_b
    //     0xb29550: ldur            w3, [x2, #0xb]
    // 0xb29554: DecompressPointer r3
    //     0xb29554: add             x3, x3, HEAP, lsl #32
    // 0xb29558: cmp             w3, NULL
    // 0xb2955c: b.eq            #0xb295bc
    // 0xb29560: LoadField: r4 = r2->field_1b
    //     0xb29560: ldur            w4, [x2, #0x1b]
    // 0xb29564: DecompressPointer r4
    //     0xb29564: add             x4, x4, HEAP, lsl #32
    // 0xb29568: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb29568: ldur            w2, [x3, #0x17]
    // 0xb2956c: DecompressPointer r2
    //     0xb2956c: add             x2, x2, HEAP, lsl #32
    // 0xb29570: stp             x1, x2, [SP, #8]
    // 0xb29574: str             x4, [SP]
    // 0xb29578: r4 = 0
    //     0xb29578: movz            x4, #0
    // 0xb2957c: ldr             x0, [SP, #0x10]
    // 0xb29580: r16 = UnlinkedCall_0x613b5c
    //     0xb29580: add             x16, PP, #0x57, lsl #12  ; [pp+0x57508] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb29584: add             x16, x16, #0x508
    // 0xb29588: ldp             x5, lr, [x16]
    // 0xb2958c: blr             lr
    // 0xb29590: ldur            x1, [fp, #-8]
    // 0xb29594: LoadField: r2 = r1->field_f
    //     0xb29594: ldur            w2, [x1, #0xf]
    // 0xb29598: DecompressPointer r2
    //     0xb29598: add             x2, x2, HEAP, lsl #32
    // 0xb2959c: r1 = false
    //     0xb2959c: add             x1, NULL, #0x30  ; false
    // 0xb295a0: StoreField: r2->field_1f = r1
    //     0xb295a0: stur            w1, [x2, #0x1f]
    // 0xb295a4: r0 = Null
    //     0xb295a4: mov             x0, NULL
    // 0xb295a8: LeaveFrame
    //     0xb295a8: mov             SP, fp
    //     0xb295ac: ldp             fp, lr, [SP], #0x10
    // 0xb295b0: ret
    //     0xb295b0: ret             
    // 0xb295b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb295b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb295b8: b               #0xb29538
    // 0xb295bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb295bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4124, size: 0x48, field offset: 0xc
class SearchView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e4b4, size: 0x48
    // 0xc7e4b4: EnterFrame
    //     0xc7e4b4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e4b8: mov             fp, SP
    // 0xc7e4bc: AllocStack(0x8)
    //     0xc7e4bc: sub             SP, SP, #8
    // 0xc7e4c0: CheckStackOverflow
    //     0xc7e4c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e4c4: cmp             SP, x16
    //     0xc7e4c8: b.ls            #0xc7e4f4
    // 0xc7e4cc: r1 = <SearchView>
    //     0xc7e4cc: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ab8] TypeArguments: <SearchView>
    //     0xc7e4d0: ldr             x1, [x1, #0xab8]
    // 0xc7e4d4: r0 = _SearchViewState()
    //     0xc7e4d4: bl              #0xc7e4fc  ; Allocate_SearchViewStateStub -> _SearchViewState (size=0x24)
    // 0xc7e4d8: mov             x1, x0
    // 0xc7e4dc: stur            x0, [fp, #-8]
    // 0xc7e4e0: r0 = _SearchViewState()
    //     0xc7e4e0: bl              #0xc7ccc4  ; [package:customer_app/app/presentation/views/basic/search/widgets/search_view.dart] _SearchViewState::_SearchViewState
    // 0xc7e4e4: ldur            x0, [fp, #-8]
    // 0xc7e4e8: LeaveFrame
    //     0xc7e4e8: mov             SP, fp
    //     0xc7e4ec: ldp             fp, lr, [SP], #0x10
    // 0xc7e4f0: ret
    //     0xc7e4f0: ret             
    // 0xc7e4f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e4f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e4f8: b               #0xc7e4cc
  }
}
