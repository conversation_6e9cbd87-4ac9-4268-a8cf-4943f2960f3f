// lib: , url: package:customer_app/app/routes/glass.dart

// class id: 1049588, size: 0x8
class :: {
}

// class id: 5005, size: 0x8, field offset: 0x8
abstract class GlassAppPages extends Object {

  static late final List<GetPage<dynamic>> glassPages; // offset: 0xe84

  static List<GetPage<dynamic>> glassPages() {
    // ** addr: 0x91bf7c, size: 0xf18
    // 0x91bf7c: EnterFrame
    //     0x91bf7c: stp             fp, lr, [SP, #-0x10]!
    //     0x91bf80: mov             fp, SP
    // 0x91bf84: AllocStack(0x18)
    //     0x91bf84: sub             SP, SP, #0x18
    // 0x91bf88: CheckStackOverflow
    //     0x91bf88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91bf8c: cmp             SP, x16
    //     0x91bf90: b.ls            #0x91ce8c
    // 0x91bf94: r1 = Function '<anonymous closure>': static.
    //     0x91bf94: add             x1, PP, #0xd, lsl #12  ; [pp+0xd818] AnonymousClosure: static (0x920474), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91bf98: ldr             x1, [x1, #0x818]
    // 0x91bf9c: r2 = Null
    //     0x91bf9c: mov             x2, NULL
    // 0x91bfa0: r0 = AllocateClosure()
    //     0x91bfa0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91bfa4: stur            x0, [fp, #-8]
    // 0x91bfa8: r0 = InitialBinding()
    //     0x91bfa8: bl              #0x91be08  ; AllocateInitialBindingStub -> InitialBinding (size=0x8)
    // 0x91bfac: r1 = Null
    //     0x91bfac: mov             x1, NULL
    // 0x91bfb0: stur            x0, [fp, #-0x10]
    // 0x91bfb4: r0 = GetPage()
    //     0x91bfb4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91bfb8: mov             x1, x0
    // 0x91bfbc: ldur            x2, [fp, #-0x10]
    // 0x91bfc0: ldur            x5, [fp, #-8]
    // 0x91bfc4: r3 = "/"
    //     0x91bfc4: ldr             x3, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x91bfc8: stur            x0, [fp, #-8]
    // 0x91bfcc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91bfcc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91bfd0: r0 = GetPage()
    //     0x91bfd0: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91bfd4: r1 = <GetPage>
    //     0x91bfd4: add             x1, PP, #0xb, lsl #12  ; [pp+0xba40] TypeArguments: <GetPage>
    //     0x91bfd8: ldr             x1, [x1, #0xa40]
    // 0x91bfdc: r2 = 68
    //     0x91bfdc: movz            x2, #0x44
    // 0x91bfe0: r0 = AllocateArray()
    //     0x91bfe0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x91bfe4: mov             x3, x0
    // 0x91bfe8: ldur            x0, [fp, #-8]
    // 0x91bfec: stur            x3, [fp, #-0x10]
    // 0x91bff0: StoreField: r3->field_f = r0
    //     0x91bff0: stur            w0, [x3, #0xf]
    // 0x91bff4: r1 = Function '<anonymous closure>': static.
    //     0x91bff4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd820] AnonymousClosure: static (0x920468), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91bff8: ldr             x1, [x1, #0x820]
    // 0x91bffc: r2 = Null
    //     0x91bffc: mov             x2, NULL
    // 0x91c000: r0 = AllocateClosure()
    //     0x91c000: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c004: stur            x0, [fp, #-8]
    // 0x91c008: r0 = HomePageBinding()
    //     0x91c008: bl              #0x91cfe4  ; AllocateHomePageBindingStub -> HomePageBinding (size=0x8)
    // 0x91c00c: r1 = Null
    //     0x91c00c: mov             x1, NULL
    // 0x91c010: stur            x0, [fp, #-0x18]
    // 0x91c014: r0 = GetPage()
    //     0x91c014: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c018: mov             x1, x0
    // 0x91c01c: ldur            x2, [fp, #-0x18]
    // 0x91c020: ldur            x5, [fp, #-8]
    // 0x91c024: r3 = "/home"
    //     0x91c024: add             x3, PP, #0xd, lsl #12  ; [pp+0xd828] "/home"
    //     0x91c028: ldr             x3, [x3, #0x828]
    // 0x91c02c: stur            x0, [fp, #-8]
    // 0x91c030: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c030: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c034: r0 = GetPage()
    //     0x91c034: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c038: ldur            x1, [fp, #-0x10]
    // 0x91c03c: ldur            x0, [fp, #-8]
    // 0x91c040: ArrayStore: r1[1] = r0  ; List_4
    //     0x91c040: add             x25, x1, #0x13
    //     0x91c044: str             w0, [x25]
    //     0x91c048: tbz             w0, #0, #0x91c064
    //     0x91c04c: ldurb           w16, [x1, #-1]
    //     0x91c050: ldurb           w17, [x0, #-1]
    //     0x91c054: and             x16, x17, x16, lsr #2
    //     0x91c058: tst             x16, HEAP, lsr #32
    //     0x91c05c: b.eq            #0x91c064
    //     0x91c060: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c064: r1 = Function '<anonymous closure>': static.
    //     0x91c064: add             x1, PP, #0xd, lsl #12  ; [pp+0xd830] AnonymousClosure: static (0x92045c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c068: ldr             x1, [x1, #0x830]
    // 0x91c06c: r2 = Null
    //     0x91c06c: mov             x2, NULL
    // 0x91c070: r0 = AllocateClosure()
    //     0x91c070: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c074: stur            x0, [fp, #-8]
    // 0x91c078: r0 = SearchPageBinding()
    //     0x91c078: bl              #0x91cfd8  ; AllocateSearchPageBindingStub -> SearchPageBinding (size=0x8)
    // 0x91c07c: r1 = Null
    //     0x91c07c: mov             x1, NULL
    // 0x91c080: stur            x0, [fp, #-0x18]
    // 0x91c084: r0 = GetPage()
    //     0x91c084: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c088: mov             x1, x0
    // 0x91c08c: ldur            x2, [fp, #-0x18]
    // 0x91c090: ldur            x5, [fp, #-8]
    // 0x91c094: r3 = "/search"
    //     0x91c094: add             x3, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x91c098: ldr             x3, [x3, #0x838]
    // 0x91c09c: stur            x0, [fp, #-8]
    // 0x91c0a0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c0a0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c0a4: r0 = GetPage()
    //     0x91c0a4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c0a8: ldur            x1, [fp, #-0x10]
    // 0x91c0ac: ldur            x0, [fp, #-8]
    // 0x91c0b0: ArrayStore: r1[2] = r0  ; List_4
    //     0x91c0b0: add             x25, x1, #0x17
    //     0x91c0b4: str             w0, [x25]
    //     0x91c0b8: tbz             w0, #0, #0x91c0d4
    //     0x91c0bc: ldurb           w16, [x1, #-1]
    //     0x91c0c0: ldurb           w17, [x0, #-1]
    //     0x91c0c4: and             x16, x17, x16, lsr #2
    //     0x91c0c8: tst             x16, HEAP, lsr #32
    //     0x91c0cc: b.eq            #0x91c0d4
    //     0x91c0d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c0d4: r1 = Function '<anonymous closure>': static.
    //     0x91c0d4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd840] AnonymousClosure: static (0x91d62c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c0d8: ldr             x1, [x1, #0x840]
    // 0x91c0dc: r2 = Null
    //     0x91c0dc: mov             x2, NULL
    // 0x91c0e0: r0 = AllocateClosure()
    //     0x91c0e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c0e4: stur            x0, [fp, #-8]
    // 0x91c0e8: r0 = CollectionPageBinding()
    //     0x91c0e8: bl              #0x91cfcc  ; AllocateCollectionPageBindingStub -> CollectionPageBinding (size=0x8)
    // 0x91c0ec: r1 = Null
    //     0x91c0ec: mov             x1, NULL
    // 0x91c0f0: stur            x0, [fp, #-0x18]
    // 0x91c0f4: r0 = GetPage()
    //     0x91c0f4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c0f8: mov             x1, x0
    // 0x91c0fc: ldur            x2, [fp, #-0x18]
    // 0x91c100: ldur            x5, [fp, #-8]
    // 0x91c104: r3 = "/collection"
    //     0x91c104: add             x3, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x91c108: ldr             x3, [x3, #0x458]
    // 0x91c10c: stur            x0, [fp, #-8]
    // 0x91c110: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c110: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c114: r0 = GetPage()
    //     0x91c114: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c118: ldur            x1, [fp, #-0x10]
    // 0x91c11c: ldur            x0, [fp, #-8]
    // 0x91c120: ArrayStore: r1[3] = r0  ; List_4
    //     0x91c120: add             x25, x1, #0x1b
    //     0x91c124: str             w0, [x25]
    //     0x91c128: tbz             w0, #0, #0x91c144
    //     0x91c12c: ldurb           w16, [x1, #-1]
    //     0x91c130: ldurb           w17, [x0, #-1]
    //     0x91c134: and             x16, x17, x16, lsr #2
    //     0x91c138: tst             x16, HEAP, lsr #32
    //     0x91c13c: b.eq            #0x91c144
    //     0x91c140: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c144: r1 = Function '<anonymous closure>': static.
    //     0x91c144: add             x1, PP, #0xd, lsl #12  ; [pp+0xd848] AnonymousClosure: static (0x91d620), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c148: ldr             x1, [x1, #0x848]
    // 0x91c14c: r2 = Null
    //     0x91c14c: mov             x2, NULL
    // 0x91c150: r0 = AllocateClosure()
    //     0x91c150: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c154: stur            x0, [fp, #-8]
    // 0x91c158: r0 = ProductDetailBinding()
    //     0x91c158: bl              #0x91cfc0  ; AllocateProductDetailBindingStub -> ProductDetailBinding (size=0x8)
    // 0x91c15c: r1 = Null
    //     0x91c15c: mov             x1, NULL
    // 0x91c160: stur            x0, [fp, #-0x18]
    // 0x91c164: r0 = GetPage()
    //     0x91c164: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c168: mov             x1, x0
    // 0x91c16c: ldur            x2, [fp, #-0x18]
    // 0x91c170: ldur            x5, [fp, #-8]
    // 0x91c174: r3 = "/product-detail"
    //     0x91c174: add             x3, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x91c178: ldr             x3, [x3, #0x4a8]
    // 0x91c17c: stur            x0, [fp, #-8]
    // 0x91c180: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c180: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c184: r0 = GetPage()
    //     0x91c184: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c188: ldur            x1, [fp, #-0x10]
    // 0x91c18c: ldur            x0, [fp, #-8]
    // 0x91c190: ArrayStore: r1[4] = r0  ; List_4
    //     0x91c190: add             x25, x1, #0x1f
    //     0x91c194: str             w0, [x25]
    //     0x91c198: tbz             w0, #0, #0x91c1b4
    //     0x91c19c: ldurb           w16, [x1, #-1]
    //     0x91c1a0: ldurb           w17, [x0, #-1]
    //     0x91c1a4: and             x16, x17, x16, lsr #2
    //     0x91c1a8: tst             x16, HEAP, lsr #32
    //     0x91c1ac: b.eq            #0x91c1b4
    //     0x91c1b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c1b4: r1 = Function '<anonymous closure>': static.
    //     0x91c1b4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd850] AnonymousClosure: static (0x91d614), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c1b8: ldr             x1, [x1, #0x850]
    // 0x91c1bc: r2 = Null
    //     0x91c1bc: mov             x2, NULL
    // 0x91c1c0: r0 = AllocateClosure()
    //     0x91c1c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c1c4: stur            x0, [fp, #-8]
    // 0x91c1c8: r0 = BagBinding()
    //     0x91c1c8: bl              #0x91cfb4  ; AllocateBagBindingStub -> BagBinding (size=0x8)
    // 0x91c1cc: r1 = Null
    //     0x91c1cc: mov             x1, NULL
    // 0x91c1d0: stur            x0, [fp, #-0x18]
    // 0x91c1d4: r0 = GetPage()
    //     0x91c1d4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c1d8: mov             x1, x0
    // 0x91c1dc: ldur            x2, [fp, #-0x18]
    // 0x91c1e0: ldur            x5, [fp, #-8]
    // 0x91c1e4: r3 = "/bag"
    //     0x91c1e4: add             x3, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x91c1e8: ldr             x3, [x3, #0x468]
    // 0x91c1ec: stur            x0, [fp, #-8]
    // 0x91c1f0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c1f0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c1f4: r0 = GetPage()
    //     0x91c1f4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c1f8: ldur            x1, [fp, #-0x10]
    // 0x91c1fc: ldur            x0, [fp, #-8]
    // 0x91c200: ArrayStore: r1[5] = r0  ; List_4
    //     0x91c200: add             x25, x1, #0x23
    //     0x91c204: str             w0, [x25]
    //     0x91c208: tbz             w0, #0, #0x91c224
    //     0x91c20c: ldurb           w16, [x1, #-1]
    //     0x91c210: ldurb           w17, [x0, #-1]
    //     0x91c214: and             x16, x17, x16, lsr #2
    //     0x91c218: tst             x16, HEAP, lsr #32
    //     0x91c21c: b.eq            #0x91c224
    //     0x91c220: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c224: r1 = Function '<anonymous closure>': static.
    //     0x91c224: add             x1, PP, #0xd, lsl #12  ; [pp+0xd858] AnonymousClosure: static (0x91d608), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c228: ldr             x1, [x1, #0x858]
    // 0x91c22c: r2 = Null
    //     0x91c22c: mov             x2, NULL
    // 0x91c230: r0 = AllocateClosure()
    //     0x91c230: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c234: stur            x0, [fp, #-8]
    // 0x91c238: r0 = ProfileBinding()
    //     0x91c238: bl              #0x91cfa8  ; AllocateProfileBindingStub -> ProfileBinding (size=0x8)
    // 0x91c23c: r1 = Null
    //     0x91c23c: mov             x1, NULL
    // 0x91c240: stur            x0, [fp, #-0x18]
    // 0x91c244: r0 = GetPage()
    //     0x91c244: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c248: mov             x1, x0
    // 0x91c24c: ldur            x2, [fp, #-0x18]
    // 0x91c250: ldur            x5, [fp, #-8]
    // 0x91c254: r3 = "/profile"
    //     0x91c254: add             x3, PP, #0xd, lsl #12  ; [pp+0xd860] "/profile"
    //     0x91c258: ldr             x3, [x3, #0x860]
    // 0x91c25c: stur            x0, [fp, #-8]
    // 0x91c260: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c260: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c264: r0 = GetPage()
    //     0x91c264: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c268: ldur            x1, [fp, #-0x10]
    // 0x91c26c: ldur            x0, [fp, #-8]
    // 0x91c270: ArrayStore: r1[6] = r0  ; List_4
    //     0x91c270: add             x25, x1, #0x27
    //     0x91c274: str             w0, [x25]
    //     0x91c278: tbz             w0, #0, #0x91c294
    //     0x91c27c: ldurb           w16, [x1, #-1]
    //     0x91c280: ldurb           w17, [x0, #-1]
    //     0x91c284: and             x16, x17, x16, lsr #2
    //     0x91c288: tst             x16, HEAP, lsr #32
    //     0x91c28c: b.eq            #0x91c294
    //     0x91c290: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c294: r1 = Function '<anonymous closure>': static.
    //     0x91c294: add             x1, PP, #0xd, lsl #12  ; [pp+0xd868] AnonymousClosure: static (0x91d5fc), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c298: ldr             x1, [x1, #0x868]
    // 0x91c29c: r2 = Null
    //     0x91c29c: mov             x2, NULL
    // 0x91c2a0: r0 = AllocateClosure()
    //     0x91c2a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c2a4: stur            x0, [fp, #-8]
    // 0x91c2a8: r0 = OrdersBinding()
    //     0x91c2a8: bl              #0x91cf9c  ; AllocateOrdersBindingStub -> OrdersBinding (size=0x8)
    // 0x91c2ac: r1 = Null
    //     0x91c2ac: mov             x1, NULL
    // 0x91c2b0: stur            x0, [fp, #-0x18]
    // 0x91c2b4: r0 = GetPage()
    //     0x91c2b4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c2b8: mov             x1, x0
    // 0x91c2bc: ldur            x2, [fp, #-0x18]
    // 0x91c2c0: ldur            x5, [fp, #-8]
    // 0x91c2c4: r3 = "/orderss"
    //     0x91c2c4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd870] "/orderss"
    //     0x91c2c8: ldr             x3, [x3, #0x870]
    // 0x91c2cc: stur            x0, [fp, #-8]
    // 0x91c2d0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c2d0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c2d4: r0 = GetPage()
    //     0x91c2d4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c2d8: ldur            x1, [fp, #-0x10]
    // 0x91c2dc: ldur            x0, [fp, #-8]
    // 0x91c2e0: ArrayStore: r1[7] = r0  ; List_4
    //     0x91c2e0: add             x25, x1, #0x2b
    //     0x91c2e4: str             w0, [x25]
    //     0x91c2e8: tbz             w0, #0, #0x91c304
    //     0x91c2ec: ldurb           w16, [x1, #-1]
    //     0x91c2f0: ldurb           w17, [x0, #-1]
    //     0x91c2f4: and             x16, x17, x16, lsr #2
    //     0x91c2f8: tst             x16, HEAP, lsr #32
    //     0x91c2fc: b.eq            #0x91c304
    //     0x91c300: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c304: r1 = Function '<anonymous closure>': static.
    //     0x91c304: add             x1, PP, #0xd, lsl #12  ; [pp+0xd878] AnonymousClosure: static (0x91d580), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c308: ldr             x1, [x1, #0x878]
    // 0x91c30c: r2 = Null
    //     0x91c30c: mov             x2, NULL
    // 0x91c310: r0 = AllocateClosure()
    //     0x91c310: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c314: stur            x0, [fp, #-8]
    // 0x91c318: r0 = LoginBinding()
    //     0x91c318: bl              #0x91cf90  ; AllocateLoginBindingStub -> LoginBinding (size=0x8)
    // 0x91c31c: r1 = Null
    //     0x91c31c: mov             x1, NULL
    // 0x91c320: stur            x0, [fp, #-0x18]
    // 0x91c324: r0 = GetPage()
    //     0x91c324: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c328: mov             x1, x0
    // 0x91c32c: ldur            x2, [fp, #-0x18]
    // 0x91c330: ldur            x5, [fp, #-8]
    // 0x91c334: r3 = "/login"
    //     0x91c334: add             x3, PP, #0xd, lsl #12  ; [pp+0xd880] "/login"
    //     0x91c338: ldr             x3, [x3, #0x880]
    // 0x91c33c: stur            x0, [fp, #-8]
    // 0x91c340: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c340: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c344: r0 = GetPage()
    //     0x91c344: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c348: ldur            x1, [fp, #-0x10]
    // 0x91c34c: ldur            x0, [fp, #-8]
    // 0x91c350: ArrayStore: r1[8] = r0  ; List_4
    //     0x91c350: add             x25, x1, #0x2f
    //     0x91c354: str             w0, [x25]
    //     0x91c358: tbz             w0, #0, #0x91c374
    //     0x91c35c: ldurb           w16, [x1, #-1]
    //     0x91c360: ldurb           w17, [x0, #-1]
    //     0x91c364: and             x16, x17, x16, lsr #2
    //     0x91c368: tst             x16, HEAP, lsr #32
    //     0x91c36c: b.eq            #0x91c374
    //     0x91c370: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c374: r1 = Function '<anonymous closure>': static.
    //     0x91c374: add             x1, PP, #0xd, lsl #12  ; [pp+0xd888] AnonymousClosure: static (0x91d4bc), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c378: ldr             x1, [x1, #0x888]
    // 0x91c37c: r2 = Null
    //     0x91c37c: mov             x2, NULL
    // 0x91c380: r0 = AllocateClosure()
    //     0x91c380: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c384: stur            x0, [fp, #-8]
    // 0x91c388: r0 = OrderDetailBinding()
    //     0x91c388: bl              #0x91cf84  ; AllocateOrderDetailBindingStub -> OrderDetailBinding (size=0x8)
    // 0x91c38c: r1 = Null
    //     0x91c38c: mov             x1, NULL
    // 0x91c390: stur            x0, [fp, #-0x18]
    // 0x91c394: r0 = GetPage()
    //     0x91c394: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c398: mov             x1, x0
    // 0x91c39c: ldur            x2, [fp, #-0x18]
    // 0x91c3a0: ldur            x5, [fp, #-8]
    // 0x91c3a4: r3 = "/order"
    //     0x91c3a4: add             x3, PP, #0xb, lsl #12  ; [pp+0xb430] "/order"
    //     0x91c3a8: ldr             x3, [x3, #0x430]
    // 0x91c3ac: stur            x0, [fp, #-8]
    // 0x91c3b0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c3b0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c3b4: r0 = GetPage()
    //     0x91c3b4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c3b8: ldur            x1, [fp, #-0x10]
    // 0x91c3bc: ldur            x0, [fp, #-8]
    // 0x91c3c0: ArrayStore: r1[9] = r0  ; List_4
    //     0x91c3c0: add             x25, x1, #0x33
    //     0x91c3c4: str             w0, [x25]
    //     0x91c3c8: tbz             w0, #0, #0x91c3e4
    //     0x91c3cc: ldurb           w16, [x1, #-1]
    //     0x91c3d0: ldurb           w17, [x0, #-1]
    //     0x91c3d4: and             x16, x17, x16, lsr #2
    //     0x91c3d8: tst             x16, HEAP, lsr #32
    //     0x91c3dc: b.eq            #0x91c3e4
    //     0x91c3e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c3e4: r1 = Function '<anonymous closure>': static.
    //     0x91c3e4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd890] AnonymousClosure: static (0x91d4b0), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c3e8: ldr             x1, [x1, #0x890]
    // 0x91c3ec: r2 = Null
    //     0x91c3ec: mov             x2, NULL
    // 0x91c3f0: r0 = AllocateClosure()
    //     0x91c3f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c3f4: stur            x0, [fp, #-8]
    // 0x91c3f8: r0 = TestimonialsBinding()
    //     0x91c3f8: bl              #0x91cf78  ; AllocateTestimonialsBindingStub -> TestimonialsBinding (size=0x8)
    // 0x91c3fc: r1 = Null
    //     0x91c3fc: mov             x1, NULL
    // 0x91c400: stur            x0, [fp, #-0x18]
    // 0x91c404: r0 = GetPage()
    //     0x91c404: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c408: mov             x1, x0
    // 0x91c40c: ldur            x2, [fp, #-0x18]
    // 0x91c410: ldur            x5, [fp, #-8]
    // 0x91c414: r3 = "/testimonials"
    //     0x91c414: add             x3, PP, #0xd, lsl #12  ; [pp+0xd898] "/testimonials"
    //     0x91c418: ldr             x3, [x3, #0x898]
    // 0x91c41c: stur            x0, [fp, #-8]
    // 0x91c420: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c420: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c424: r0 = GetPage()
    //     0x91c424: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c428: ldur            x1, [fp, #-0x10]
    // 0x91c42c: ldur            x0, [fp, #-8]
    // 0x91c430: ArrayStore: r1[10] = r0  ; List_4
    //     0x91c430: add             x25, x1, #0x37
    //     0x91c434: str             w0, [x25]
    //     0x91c438: tbz             w0, #0, #0x91c454
    //     0x91c43c: ldurb           w16, [x1, #-1]
    //     0x91c440: ldurb           w17, [x0, #-1]
    //     0x91c444: and             x16, x17, x16, lsr #2
    //     0x91c448: tst             x16, HEAP, lsr #32
    //     0x91c44c: b.eq            #0x91c454
    //     0x91c450: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c454: r1 = Function '<anonymous closure>': static.
    //     0x91c454: add             x1, PP, #0xd, lsl #12  ; [pp+0xd8a0] AnonymousClosure: static (0x91d47c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c458: ldr             x1, [x1, #0x8a0]
    // 0x91c45c: r2 = Null
    //     0x91c45c: mov             x2, NULL
    // 0x91c460: r0 = AllocateClosure()
    //     0x91c460: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c464: stur            x0, [fp, #-8]
    // 0x91c468: r0 = CustomizationBinding()
    //     0x91c468: bl              #0x91cf6c  ; AllocateCustomizationBindingStub -> CustomizationBinding (size=0x8)
    // 0x91c46c: r1 = Null
    //     0x91c46c: mov             x1, NULL
    // 0x91c470: stur            x0, [fp, #-0x18]
    // 0x91c474: r0 = GetPage()
    //     0x91c474: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c478: mov             x1, x0
    // 0x91c47c: ldur            x2, [fp, #-0x18]
    // 0x91c480: ldur            x5, [fp, #-8]
    // 0x91c484: r3 = "/customization"
    //     0x91c484: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8a8] "/customization"
    //     0x91c488: ldr             x3, [x3, #0x8a8]
    // 0x91c48c: stur            x0, [fp, #-8]
    // 0x91c490: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c490: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c494: r0 = GetPage()
    //     0x91c494: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c498: ldur            x1, [fp, #-0x10]
    // 0x91c49c: ldur            x0, [fp, #-8]
    // 0x91c4a0: ArrayStore: r1[11] = r0  ; List_4
    //     0x91c4a0: add             x25, x1, #0x3b
    //     0x91c4a4: str             w0, [x25]
    //     0x91c4a8: tbz             w0, #0, #0x91c4c4
    //     0x91c4ac: ldurb           w16, [x1, #-1]
    //     0x91c4b0: ldurb           w17, [x0, #-1]
    //     0x91c4b4: and             x16, x17, x16, lsr #2
    //     0x91c4b8: tst             x16, HEAP, lsr #32
    //     0x91c4bc: b.eq            #0x91c4c4
    //     0x91c4c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c4c4: r1 = Function '<anonymous closure>': static.
    //     0x91c4c4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd8b0] AnonymousClosure: static (0x91d470), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c4c8: ldr             x1, [x1, #0x8b0]
    // 0x91c4cc: r2 = Null
    //     0x91c4cc: mov             x2, NULL
    // 0x91c4d0: r0 = AllocateClosure()
    //     0x91c4d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c4d4: stur            x0, [fp, #-8]
    // 0x91c4d8: r0 = ReturnOrderBinding()
    //     0x91c4d8: bl              #0x91cf60  ; AllocateReturnOrderBindingStub -> ReturnOrderBinding (size=0x8)
    // 0x91c4dc: r1 = Null
    //     0x91c4dc: mov             x1, NULL
    // 0x91c4e0: stur            x0, [fp, #-0x18]
    // 0x91c4e4: r0 = GetPage()
    //     0x91c4e4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c4e8: mov             x1, x0
    // 0x91c4ec: ldur            x2, [fp, #-0x18]
    // 0x91c4f0: ldur            x5, [fp, #-8]
    // 0x91c4f4: r3 = "/return-order"
    //     0x91c4f4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x91c4f8: ldr             x3, [x3, #0x8b8]
    // 0x91c4fc: stur            x0, [fp, #-8]
    // 0x91c500: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c500: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c504: r0 = GetPage()
    //     0x91c504: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c508: ldur            x1, [fp, #-0x10]
    // 0x91c50c: ldur            x0, [fp, #-8]
    // 0x91c510: ArrayStore: r1[12] = r0  ; List_4
    //     0x91c510: add             x25, x1, #0x3f
    //     0x91c514: str             w0, [x25]
    //     0x91c518: tbz             w0, #0, #0x91c534
    //     0x91c51c: ldurb           w16, [x1, #-1]
    //     0x91c520: ldurb           w17, [x0, #-1]
    //     0x91c524: and             x16, x17, x16, lsr #2
    //     0x91c528: tst             x16, HEAP, lsr #32
    //     0x91c52c: b.eq            #0x91c534
    //     0x91c530: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c534: r1 = Function '<anonymous closure>': static.
    //     0x91c534: add             x1, PP, #0xd, lsl #12  ; [pp+0xd8c0] AnonymousClosure: static (0x91d43c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c538: ldr             x1, [x1, #0x8c0]
    // 0x91c53c: r2 = Null
    //     0x91c53c: mov             x2, NULL
    // 0x91c540: r0 = AllocateClosure()
    //     0x91c540: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c544: stur            x0, [fp, #-8]
    // 0x91c548: r0 = ReturnOrderBinding()
    //     0x91c548: bl              #0x91cf60  ; AllocateReturnOrderBindingStub -> ReturnOrderBinding (size=0x8)
    // 0x91c54c: r1 = Null
    //     0x91c54c: mov             x1, NULL
    // 0x91c550: stur            x0, [fp, #-0x18]
    // 0x91c554: r0 = GetPage()
    //     0x91c554: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c558: mov             x1, x0
    // 0x91c55c: ldur            x2, [fp, #-0x18]
    // 0x91c560: ldur            x5, [fp, #-8]
    // 0x91c564: r3 = "/return-order-confirm"
    //     0x91c564: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8c8] "/return-order-confirm"
    //     0x91c568: ldr             x3, [x3, #0x8c8]
    // 0x91c56c: stur            x0, [fp, #-8]
    // 0x91c570: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c570: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c574: r0 = GetPage()
    //     0x91c574: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c578: ldur            x1, [fp, #-0x10]
    // 0x91c57c: ldur            x0, [fp, #-8]
    // 0x91c580: ArrayStore: r1[13] = r0  ; List_4
    //     0x91c580: add             x25, x1, #0x43
    //     0x91c584: str             w0, [x25]
    //     0x91c588: tbz             w0, #0, #0x91c5a4
    //     0x91c58c: ldurb           w16, [x1, #-1]
    //     0x91c590: ldurb           w17, [x0, #-1]
    //     0x91c594: and             x16, x17, x16, lsr #2
    //     0x91c598: tst             x16, HEAP, lsr #32
    //     0x91c59c: b.eq            #0x91c5a4
    //     0x91c5a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c5a4: r1 = Function '<anonymous closure>': static.
    //     0x91c5a4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd8d0] AnonymousClosure: static (0x91d430), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c5a8: ldr             x1, [x1, #0x8d0]
    // 0x91c5ac: r2 = Null
    //     0x91c5ac: mov             x2, NULL
    // 0x91c5b0: r0 = AllocateClosure()
    //     0x91c5b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c5b4: stur            x0, [fp, #-8]
    // 0x91c5b8: r0 = BrowseBinding()
    //     0x91c5b8: bl              #0x91cf54  ; AllocateBrowseBindingStub -> BrowseBinding (size=0x8)
    // 0x91c5bc: r1 = Null
    //     0x91c5bc: mov             x1, NULL
    // 0x91c5c0: stur            x0, [fp, #-0x18]
    // 0x91c5c4: r0 = GetPage()
    //     0x91c5c4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c5c8: mov             x1, x0
    // 0x91c5cc: ldur            x2, [fp, #-0x18]
    // 0x91c5d0: ldur            x5, [fp, #-8]
    // 0x91c5d4: r3 = "/browse"
    //     0x91c5d4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8d8] "/browse"
    //     0x91c5d8: ldr             x3, [x3, #0x8d8]
    // 0x91c5dc: stur            x0, [fp, #-8]
    // 0x91c5e0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c5e0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c5e4: r0 = GetPage()
    //     0x91c5e4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c5e8: ldur            x1, [fp, #-0x10]
    // 0x91c5ec: ldur            x0, [fp, #-8]
    // 0x91c5f0: ArrayStore: r1[14] = r0  ; List_4
    //     0x91c5f0: add             x25, x1, #0x47
    //     0x91c5f4: str             w0, [x25]
    //     0x91c5f8: tbz             w0, #0, #0x91c614
    //     0x91c5fc: ldurb           w16, [x1, #-1]
    //     0x91c600: ldurb           w17, [x0, #-1]
    //     0x91c604: and             x16, x17, x16, lsr #2
    //     0x91c608: tst             x16, HEAP, lsr #32
    //     0x91c60c: b.eq            #0x91c614
    //     0x91c610: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c614: r1 = Function '<anonymous closure>': static.
    //     0x91c614: add             x1, PP, #0xd, lsl #12  ; [pp+0xd8e0] AnonymousClosure: static (0x91d424), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c618: ldr             x1, [x1, #0x8e0]
    // 0x91c61c: r2 = Null
    //     0x91c61c: mov             x2, NULL
    // 0x91c620: r0 = AllocateClosure()
    //     0x91c620: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c624: stur            x0, [fp, #-8]
    // 0x91c628: r0 = EnlargeImageBinding()
    //     0x91c628: bl              #0x91cf48  ; AllocateEnlargeImageBindingStub -> EnlargeImageBinding (size=0x8)
    // 0x91c62c: r1 = Null
    //     0x91c62c: mov             x1, NULL
    // 0x91c630: stur            x0, [fp, #-0x18]
    // 0x91c634: r0 = GetPage()
    //     0x91c634: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c638: mov             x1, x0
    // 0x91c63c: ldur            x2, [fp, #-0x18]
    // 0x91c640: ldur            x5, [fp, #-8]
    // 0x91c644: r3 = "/enlarge_image_view"
    //     0x91c644: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8e8] "/enlarge_image_view"
    //     0x91c648: ldr             x3, [x3, #0x8e8]
    // 0x91c64c: stur            x0, [fp, #-8]
    // 0x91c650: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c650: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c654: r0 = GetPage()
    //     0x91c654: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c658: ldur            x1, [fp, #-0x10]
    // 0x91c65c: ldur            x0, [fp, #-8]
    // 0x91c660: ArrayStore: r1[15] = r0  ; List_4
    //     0x91c660: add             x25, x1, #0x4b
    //     0x91c664: str             w0, [x25]
    //     0x91c668: tbz             w0, #0, #0x91c684
    //     0x91c66c: ldurb           w16, [x1, #-1]
    //     0x91c670: ldurb           w17, [x0, #-1]
    //     0x91c674: and             x16, x17, x16, lsr #2
    //     0x91c678: tst             x16, HEAP, lsr #32
    //     0x91c67c: b.eq            #0x91c684
    //     0x91c680: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c684: r1 = Function '<anonymous closure>': static.
    //     0x91c684: add             x1, PP, #0xd, lsl #12  ; [pp+0xd8f0] AnonymousClosure: static (0x91d418), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c688: ldr             x1, [x1, #0x8f0]
    // 0x91c68c: r2 = Null
    //     0x91c68c: mov             x2, NULL
    // 0x91c690: r0 = AllocateClosure()
    //     0x91c690: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c694: stur            x0, [fp, #-8]
    // 0x91c698: r0 = ReplaceCallOrderBinding()
    //     0x91c698: bl              #0x91cf3c  ; AllocateReplaceCallOrderBindingStub -> ReplaceCallOrderBinding (size=0x8)
    // 0x91c69c: r1 = Null
    //     0x91c69c: mov             x1, NULL
    // 0x91c6a0: stur            x0, [fp, #-0x18]
    // 0x91c6a4: r0 = GetPage()
    //     0x91c6a4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c6a8: mov             x1, x0
    // 0x91c6ac: ldur            x2, [fp, #-0x18]
    // 0x91c6b0: ldur            x5, [fp, #-8]
    // 0x91c6b4: r3 = "/replace_call_order"
    //     0x91c6b4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd8f8] "/replace_call_order"
    //     0x91c6b8: ldr             x3, [x3, #0x8f8]
    // 0x91c6bc: stur            x0, [fp, #-8]
    // 0x91c6c0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c6c0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c6c4: r0 = GetPage()
    //     0x91c6c4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c6c8: ldur            x1, [fp, #-0x10]
    // 0x91c6cc: ldur            x0, [fp, #-8]
    // 0x91c6d0: ArrayStore: r1[16] = r0  ; List_4
    //     0x91c6d0: add             x25, x1, #0x4f
    //     0x91c6d4: str             w0, [x25]
    //     0x91c6d8: tbz             w0, #0, #0x91c6f4
    //     0x91c6dc: ldurb           w16, [x1, #-1]
    //     0x91c6e0: ldurb           w17, [x0, #-1]
    //     0x91c6e4: and             x16, x17, x16, lsr #2
    //     0x91c6e8: tst             x16, HEAP, lsr #32
    //     0x91c6ec: b.eq            #0x91c6f4
    //     0x91c6f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c6f4: r1 = Function '<anonymous closure>': static.
    //     0x91c6f4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd900] AnonymousClosure: static (0x91d40c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c6f8: ldr             x1, [x1, #0x900]
    // 0x91c6fc: r2 = Null
    //     0x91c6fc: mov             x2, NULL
    // 0x91c700: r0 = AllocateClosure()
    //     0x91c700: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c704: stur            x0, [fp, #-8]
    // 0x91c708: r0 = CheckoutPaymentMethodBinding()
    //     0x91c708: bl              #0x91cf30  ; AllocateCheckoutPaymentMethodBindingStub -> CheckoutPaymentMethodBinding (size=0x8)
    // 0x91c70c: r1 = Null
    //     0x91c70c: mov             x1, NULL
    // 0x91c710: stur            x0, [fp, #-0x18]
    // 0x91c714: r0 = GetPage()
    //     0x91c714: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c718: mov             x1, x0
    // 0x91c71c: ldur            x2, [fp, #-0x18]
    // 0x91c720: ldur            x5, [fp, #-8]
    // 0x91c724: r3 = "/cod_online_payment_methods"
    //     0x91c724: add             x3, PP, #0xd, lsl #12  ; [pp+0xd908] "/cod_online_payment_methods"
    //     0x91c728: ldr             x3, [x3, #0x908]
    // 0x91c72c: stur            x0, [fp, #-8]
    // 0x91c730: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c730: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c734: r0 = GetPage()
    //     0x91c734: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c738: ldur            x1, [fp, #-0x10]
    // 0x91c73c: ldur            x0, [fp, #-8]
    // 0x91c740: ArrayStore: r1[17] = r0  ; List_4
    //     0x91c740: add             x25, x1, #0x53
    //     0x91c744: str             w0, [x25]
    //     0x91c748: tbz             w0, #0, #0x91c764
    //     0x91c74c: ldurb           w16, [x1, #-1]
    //     0x91c750: ldurb           w17, [x0, #-1]
    //     0x91c754: and             x16, x17, x16, lsr #2
    //     0x91c758: tst             x16, HEAP, lsr #32
    //     0x91c75c: b.eq            #0x91c764
    //     0x91c760: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c764: r1 = Function '<anonymous closure>': static.
    //     0x91c764: add             x1, PP, #0xd, lsl #12  ; [pp+0xd910] AnonymousClosure: static (0x91d400), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c768: ldr             x1, [x1, #0x910]
    // 0x91c76c: r2 = Null
    //     0x91c76c: mov             x2, NULL
    // 0x91c770: r0 = AllocateClosure()
    //     0x91c770: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c774: stur            x0, [fp, #-8]
    // 0x91c778: r0 = OrderSuccessBindings()
    //     0x91c778: bl              #0x91cf24  ; AllocateOrderSuccessBindingsStub -> OrderSuccessBindings (size=0x8)
    // 0x91c77c: r1 = Null
    //     0x91c77c: mov             x1, NULL
    // 0x91c780: stur            x0, [fp, #-0x18]
    // 0x91c784: r0 = GetPage()
    //     0x91c784: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c788: mov             x1, x0
    // 0x91c78c: ldur            x2, [fp, #-0x18]
    // 0x91c790: ldur            x5, [fp, #-8]
    // 0x91c794: r3 = "/orderSuccess"
    //     0x91c794: add             x3, PP, #0xd, lsl #12  ; [pp+0xd918] "/orderSuccess"
    //     0x91c798: ldr             x3, [x3, #0x918]
    // 0x91c79c: stur            x0, [fp, #-8]
    // 0x91c7a0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c7a0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c7a4: r0 = GetPage()
    //     0x91c7a4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c7a8: ldur            x1, [fp, #-0x10]
    // 0x91c7ac: ldur            x0, [fp, #-8]
    // 0x91c7b0: ArrayStore: r1[18] = r0  ; List_4
    //     0x91c7b0: add             x25, x1, #0x57
    //     0x91c7b4: str             w0, [x25]
    //     0x91c7b8: tbz             w0, #0, #0x91c7d4
    //     0x91c7bc: ldurb           w16, [x1, #-1]
    //     0x91c7c0: ldurb           w17, [x0, #-1]
    //     0x91c7c4: and             x16, x17, x16, lsr #2
    //     0x91c7c8: tst             x16, HEAP, lsr #32
    //     0x91c7cc: b.eq            #0x91c7d4
    //     0x91c7d0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c7d4: r1 = Function '<anonymous closure>': static.
    //     0x91c7d4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd920] AnonymousClosure: static (0x91d3f4), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c7d8: ldr             x1, [x1, #0x920]
    // 0x91c7dc: r2 = Null
    //     0x91c7dc: mov             x2, NULL
    // 0x91c7e0: r0 = AllocateClosure()
    //     0x91c7e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c7e4: stur            x0, [fp, #-8]
    // 0x91c7e8: r0 = OrderFailureBindings()
    //     0x91c7e8: bl              #0x91cf18  ; AllocateOrderFailureBindingsStub -> OrderFailureBindings (size=0x8)
    // 0x91c7ec: r1 = Null
    //     0x91c7ec: mov             x1, NULL
    // 0x91c7f0: stur            x0, [fp, #-0x18]
    // 0x91c7f4: r0 = GetPage()
    //     0x91c7f4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c7f8: mov             x1, x0
    // 0x91c7fc: ldur            x2, [fp, #-0x18]
    // 0x91c800: ldur            x5, [fp, #-8]
    // 0x91c804: r3 = "/order_failure"
    //     0x91c804: add             x3, PP, #0xd, lsl #12  ; [pp+0xd928] "/order_failure"
    //     0x91c808: ldr             x3, [x3, #0x928]
    // 0x91c80c: stur            x0, [fp, #-8]
    // 0x91c810: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c810: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c814: r0 = GetPage()
    //     0x91c814: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c818: ldur            x1, [fp, #-0x10]
    // 0x91c81c: ldur            x0, [fp, #-8]
    // 0x91c820: ArrayStore: r1[19] = r0  ; List_4
    //     0x91c820: add             x25, x1, #0x5b
    //     0x91c824: str             w0, [x25]
    //     0x91c828: tbz             w0, #0, #0x91c844
    //     0x91c82c: ldurb           w16, [x1, #-1]
    //     0x91c830: ldurb           w17, [x0, #-1]
    //     0x91c834: and             x16, x17, x16, lsr #2
    //     0x91c838: tst             x16, HEAP, lsr #32
    //     0x91c83c: b.eq            #0x91c844
    //     0x91c840: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c844: r1 = Function '<anonymous closure>': static.
    //     0x91c844: add             x1, PP, #0xd, lsl #12  ; [pp+0xd930] AnonymousClosure: static (0x91d3a4), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c848: ldr             x1, [x1, #0x930]
    // 0x91c84c: r2 = Null
    //     0x91c84c: mov             x2, NULL
    // 0x91c850: r0 = AllocateClosure()
    //     0x91c850: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c854: stur            x0, [fp, #-8]
    // 0x91c858: r0 = PoliciesBinding()
    //     0x91c858: bl              #0x91cf0c  ; AllocatePoliciesBindingStub -> PoliciesBinding (size=0x8)
    // 0x91c85c: r1 = Null
    //     0x91c85c: mov             x1, NULL
    // 0x91c860: stur            x0, [fp, #-0x18]
    // 0x91c864: r0 = GetPage()
    //     0x91c864: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c868: mov             x1, x0
    // 0x91c86c: ldur            x2, [fp, #-0x18]
    // 0x91c870: ldur            x5, [fp, #-8]
    // 0x91c874: r3 = "/policies"
    //     0x91c874: add             x3, PP, #0xd, lsl #12  ; [pp+0xd938] "/policies"
    //     0x91c878: ldr             x3, [x3, #0x938]
    // 0x91c87c: stur            x0, [fp, #-8]
    // 0x91c880: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c880: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c884: r0 = GetPage()
    //     0x91c884: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c888: ldur            x1, [fp, #-0x10]
    // 0x91c88c: ldur            x0, [fp, #-8]
    // 0x91c890: ArrayStore: r1[20] = r0  ; List_4
    //     0x91c890: add             x25, x1, #0x5f
    //     0x91c894: str             w0, [x25]
    //     0x91c898: tbz             w0, #0, #0x91c8b4
    //     0x91c89c: ldurb           w16, [x1, #-1]
    //     0x91c8a0: ldurb           w17, [x0, #-1]
    //     0x91c8a4: and             x16, x17, x16, lsr #2
    //     0x91c8a8: tst             x16, HEAP, lsr #32
    //     0x91c8ac: b.eq            #0x91c8b4
    //     0x91c8b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c8b4: r1 = Function '<anonymous closure>': static.
    //     0x91c8b4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd940] AnonymousClosure: static (0x91d398), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c8b8: ldr             x1, [x1, #0x940]
    // 0x91c8bc: r2 = Null
    //     0x91c8bc: mov             x2, NULL
    // 0x91c8c0: r0 = AllocateClosure()
    //     0x91c8c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c8c4: stur            x0, [fp, #-8]
    // 0x91c8c8: r0 = ViewAllReviewsBinding()
    //     0x91c8c8: bl              #0x91cf00  ; AllocateViewAllReviewsBindingStub -> ViewAllReviewsBinding (size=0x8)
    // 0x91c8cc: r1 = Null
    //     0x91c8cc: mov             x1, NULL
    // 0x91c8d0: stur            x0, [fp, #-0x18]
    // 0x91c8d4: r0 = GetPage()
    //     0x91c8d4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c8d8: mov             x1, x0
    // 0x91c8dc: ldur            x2, [fp, #-0x18]
    // 0x91c8e0: ldur            x5, [fp, #-8]
    // 0x91c8e4: r3 = "/all-reviews"
    //     0x91c8e4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd948] "/all-reviews"
    //     0x91c8e8: ldr             x3, [x3, #0x948]
    // 0x91c8ec: stur            x0, [fp, #-8]
    // 0x91c8f0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c8f0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c8f4: r0 = GetPage()
    //     0x91c8f4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c8f8: ldur            x1, [fp, #-0x10]
    // 0x91c8fc: ldur            x0, [fp, #-8]
    // 0x91c900: ArrayStore: r1[21] = r0  ; List_4
    //     0x91c900: add             x25, x1, #0x63
    //     0x91c904: str             w0, [x25]
    //     0x91c908: tbz             w0, #0, #0x91c924
    //     0x91c90c: ldurb           w16, [x1, #-1]
    //     0x91c910: ldurb           w17, [x0, #-1]
    //     0x91c914: and             x16, x17, x16, lsr #2
    //     0x91c918: tst             x16, HEAP, lsr #32
    //     0x91c91c: b.eq            #0x91c924
    //     0x91c920: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c924: r1 = Function '<anonymous closure>': static.
    //     0x91c924: add             x1, PP, #0xd, lsl #12  ; [pp+0xd950] AnonymousClosure: static (0x91d38c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c928: ldr             x1, [x1, #0x950]
    // 0x91c92c: r2 = Null
    //     0x91c92c: mov             x2, NULL
    // 0x91c930: r0 = AllocateClosure()
    //     0x91c930: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c934: stur            x0, [fp, #-8]
    // 0x91c938: r0 = ContactUsBinding()
    //     0x91c938: bl              #0x91cef4  ; AllocateContactUsBindingStub -> ContactUsBinding (size=0x8)
    // 0x91c93c: r1 = Null
    //     0x91c93c: mov             x1, NULL
    // 0x91c940: stur            x0, [fp, #-0x18]
    // 0x91c944: r0 = GetPage()
    //     0x91c944: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c948: mov             x1, x0
    // 0x91c94c: ldur            x2, [fp, #-0x18]
    // 0x91c950: ldur            x5, [fp, #-8]
    // 0x91c954: r3 = "/contact-us"
    //     0x91c954: add             x3, PP, #0xd, lsl #12  ; [pp+0xd958] "/contact-us"
    //     0x91c958: ldr             x3, [x3, #0x958]
    // 0x91c95c: stur            x0, [fp, #-8]
    // 0x91c960: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c960: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c964: r0 = GetPage()
    //     0x91c964: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c968: ldur            x1, [fp, #-0x10]
    // 0x91c96c: ldur            x0, [fp, #-8]
    // 0x91c970: ArrayStore: r1[22] = r0  ; List_4
    //     0x91c970: add             x25, x1, #0x67
    //     0x91c974: str             w0, [x25]
    //     0x91c978: tbz             w0, #0, #0x91c994
    //     0x91c97c: ldurb           w16, [x1, #-1]
    //     0x91c980: ldurb           w17, [x0, #-1]
    //     0x91c984: and             x16, x17, x16, lsr #2
    //     0x91c988: tst             x16, HEAP, lsr #32
    //     0x91c98c: b.eq            #0x91c994
    //     0x91c990: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91c994: r1 = Function '<anonymous closure>': static.
    //     0x91c994: add             x1, PP, #0xd, lsl #12  ; [pp+0xd960] AnonymousClosure: static (0x91d380), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91c998: ldr             x1, [x1, #0x960]
    // 0x91c99c: r2 = Null
    //     0x91c99c: mov             x2, NULL
    // 0x91c9a0: r0 = AllocateClosure()
    //     0x91c9a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91c9a4: stur            x0, [fp, #-8]
    // 0x91c9a8: r0 = ExchangeBinding()
    //     0x91c9a8: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x91c9ac: r1 = Null
    //     0x91c9ac: mov             x1, NULL
    // 0x91c9b0: stur            x0, [fp, #-0x18]
    // 0x91c9b4: r0 = GetPage()
    //     0x91c9b4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91c9b8: mov             x1, x0
    // 0x91c9bc: ldur            x2, [fp, #-0x18]
    // 0x91c9c0: ldur            x5, [fp, #-8]
    // 0x91c9c4: r3 = "/exchange-return-intermediate"
    //     0x91c9c4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd968] "/exchange-return-intermediate"
    //     0x91c9c8: ldr             x3, [x3, #0x968]
    // 0x91c9cc: stur            x0, [fp, #-8]
    // 0x91c9d0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91c9d0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91c9d4: r0 = GetPage()
    //     0x91c9d4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91c9d8: ldur            x1, [fp, #-0x10]
    // 0x91c9dc: ldur            x0, [fp, #-8]
    // 0x91c9e0: ArrayStore: r1[23] = r0  ; List_4
    //     0x91c9e0: add             x25, x1, #0x6b
    //     0x91c9e4: str             w0, [x25]
    //     0x91c9e8: tbz             w0, #0, #0x91ca04
    //     0x91c9ec: ldurb           w16, [x1, #-1]
    //     0x91c9f0: ldurb           w17, [x0, #-1]
    //     0x91c9f4: and             x16, x17, x16, lsr #2
    //     0x91c9f8: tst             x16, HEAP, lsr #32
    //     0x91c9fc: b.eq            #0x91ca04
    //     0x91ca00: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91ca04: r1 = Function '<anonymous closure>': static.
    //     0x91ca04: add             x1, PP, #0xd, lsl #12  ; [pp+0xd970] AnonymousClosure: static (0x91d34c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91ca08: ldr             x1, [x1, #0x970]
    // 0x91ca0c: r2 = Null
    //     0x91ca0c: mov             x2, NULL
    // 0x91ca10: r0 = AllocateClosure()
    //     0x91ca10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91ca14: stur            x0, [fp, #-8]
    // 0x91ca18: r0 = ExchangeBinding()
    //     0x91ca18: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x91ca1c: r1 = Null
    //     0x91ca1c: mov             x1, NULL
    // 0x91ca20: stur            x0, [fp, #-0x18]
    // 0x91ca24: r0 = GetPage()
    //     0x91ca24: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91ca28: mov             x1, x0
    // 0x91ca2c: ldur            x2, [fp, #-0x18]
    // 0x91ca30: ldur            x5, [fp, #-8]
    // 0x91ca34: r3 = "/exchange-product-skus"
    //     0x91ca34: add             x3, PP, #0xd, lsl #12  ; [pp+0xd978] "/exchange-product-skus"
    //     0x91ca38: ldr             x3, [x3, #0x978]
    // 0x91ca3c: stur            x0, [fp, #-8]
    // 0x91ca40: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91ca40: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91ca44: r0 = GetPage()
    //     0x91ca44: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91ca48: ldur            x1, [fp, #-0x10]
    // 0x91ca4c: ldur            x0, [fp, #-8]
    // 0x91ca50: ArrayStore: r1[24] = r0  ; List_4
    //     0x91ca50: add             x25, x1, #0x6f
    //     0x91ca54: str             w0, [x25]
    //     0x91ca58: tbz             w0, #0, #0x91ca74
    //     0x91ca5c: ldurb           w16, [x1, #-1]
    //     0x91ca60: ldurb           w17, [x0, #-1]
    //     0x91ca64: and             x16, x17, x16, lsr #2
    //     0x91ca68: tst             x16, HEAP, lsr #32
    //     0x91ca6c: b.eq            #0x91ca74
    //     0x91ca70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91ca74: r1 = Function '<anonymous closure>': static.
    //     0x91ca74: add             x1, PP, #0xd, lsl #12  ; [pp+0xd980] AnonymousClosure: static (0x91d340), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91ca78: ldr             x1, [x1, #0x980]
    // 0x91ca7c: r2 = Null
    //     0x91ca7c: mov             x2, NULL
    // 0x91ca80: r0 = AllocateClosure()
    //     0x91ca80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91ca84: stur            x0, [fp, #-8]
    // 0x91ca88: r0 = ExchangeBinding()
    //     0x91ca88: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x91ca8c: r1 = Null
    //     0x91ca8c: mov             x1, NULL
    // 0x91ca90: stur            x0, [fp, #-0x18]
    // 0x91ca94: r0 = GetPage()
    //     0x91ca94: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91ca98: mov             x1, x0
    // 0x91ca9c: ldur            x2, [fp, #-0x18]
    // 0x91caa0: ldur            x5, [fp, #-8]
    // 0x91caa4: r3 = "/exchange-checkout"
    //     0x91caa4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd988] "/exchange-checkout"
    //     0x91caa8: ldr             x3, [x3, #0x988]
    // 0x91caac: stur            x0, [fp, #-8]
    // 0x91cab0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cab0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cab4: r0 = GetPage()
    //     0x91cab4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cab8: ldur            x1, [fp, #-0x10]
    // 0x91cabc: ldur            x0, [fp, #-8]
    // 0x91cac0: ArrayStore: r1[25] = r0  ; List_4
    //     0x91cac0: add             x25, x1, #0x73
    //     0x91cac4: str             w0, [x25]
    //     0x91cac8: tbz             w0, #0, #0x91cae4
    //     0x91cacc: ldurb           w16, [x1, #-1]
    //     0x91cad0: ldurb           w17, [x0, #-1]
    //     0x91cad4: and             x16, x17, x16, lsr #2
    //     0x91cad8: tst             x16, HEAP, lsr #32
    //     0x91cadc: b.eq            #0x91cae4
    //     0x91cae0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cae4: r1 = Function '<anonymous closure>': static.
    //     0x91cae4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd990] AnonymousClosure: static (0x91d30c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cae8: ldr             x1, [x1, #0x990]
    // 0x91caec: r2 = Null
    //     0x91caec: mov             x2, NULL
    // 0x91caf0: r0 = AllocateClosure()
    //     0x91caf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91caf4: stur            x0, [fp, #-8]
    // 0x91caf8: r0 = ExchangeBinding()
    //     0x91caf8: bl              #0x91cee8  ; AllocateExchangeBindingStub -> ExchangeBinding (size=0x8)
    // 0x91cafc: r1 = Null
    //     0x91cafc: mov             x1, NULL
    // 0x91cb00: stur            x0, [fp, #-0x18]
    // 0x91cb04: r0 = GetPage()
    //     0x91cb04: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91cb08: mov             x1, x0
    // 0x91cb0c: ldur            x2, [fp, #-0x18]
    // 0x91cb10: ldur            x5, [fp, #-8]
    // 0x91cb14: r3 = "/exchange_online_payment_methods"
    //     0x91cb14: add             x3, PP, #0xd, lsl #12  ; [pp+0xd998] "/exchange_online_payment_methods"
    //     0x91cb18: ldr             x3, [x3, #0x998]
    // 0x91cb1c: stur            x0, [fp, #-8]
    // 0x91cb20: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cb20: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cb24: r0 = GetPage()
    //     0x91cb24: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cb28: ldur            x1, [fp, #-0x10]
    // 0x91cb2c: ldur            x0, [fp, #-8]
    // 0x91cb30: ArrayStore: r1[26] = r0  ; List_4
    //     0x91cb30: add             x25, x1, #0x77
    //     0x91cb34: str             w0, [x25]
    //     0x91cb38: tbz             w0, #0, #0x91cb54
    //     0x91cb3c: ldurb           w16, [x1, #-1]
    //     0x91cb40: ldurb           w17, [x0, #-1]
    //     0x91cb44: and             x16, x17, x16, lsr #2
    //     0x91cb48: tst             x16, HEAP, lsr #32
    //     0x91cb4c: b.eq            #0x91cb54
    //     0x91cb50: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cb54: r1 = Function '<anonymous closure>': static.
    //     0x91cb54: add             x1, PP, #0xd, lsl #12  ; [pp+0xd9a0] AnonymousClosure: static (0x91d300), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cb58: ldr             x1, [x1, #0x9a0]
    // 0x91cb5c: r2 = Null
    //     0x91cb5c: mov             x2, NULL
    // 0x91cb60: r0 = AllocateClosure()
    //     0x91cb60: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91cb64: stur            x0, [fp, #-8]
    // 0x91cb68: r0 = CheckoutVariantsBindings()
    //     0x91cb68: bl              #0x91cedc  ; AllocateCheckoutVariantsBindingsStub -> CheckoutVariantsBindings (size=0x8)
    // 0x91cb6c: r1 = Null
    //     0x91cb6c: mov             x1, NULL
    // 0x91cb70: stur            x0, [fp, #-0x18]
    // 0x91cb74: r0 = GetPage()
    //     0x91cb74: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91cb78: mov             x1, x0
    // 0x91cb7c: ldur            x2, [fp, #-0x18]
    // 0x91cb80: ldur            x5, [fp, #-8]
    // 0x91cb84: r3 = "/checkout_variants"
    //     0x91cb84: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9a8] "/checkout_variants"
    //     0x91cb88: ldr             x3, [x3, #0x9a8]
    // 0x91cb8c: stur            x0, [fp, #-8]
    // 0x91cb90: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cb90: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cb94: r0 = GetPage()
    //     0x91cb94: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cb98: ldur            x1, [fp, #-0x10]
    // 0x91cb9c: ldur            x0, [fp, #-8]
    // 0x91cba0: ArrayStore: r1[27] = r0  ; List_4
    //     0x91cba0: add             x25, x1, #0x7b
    //     0x91cba4: str             w0, [x25]
    //     0x91cba8: tbz             w0, #0, #0x91cbc4
    //     0x91cbac: ldurb           w16, [x1, #-1]
    //     0x91cbb0: ldurb           w17, [x0, #-1]
    //     0x91cbb4: and             x16, x17, x16, lsr #2
    //     0x91cbb8: tst             x16, HEAP, lsr #32
    //     0x91cbbc: b.eq            #0x91cbc4
    //     0x91cbc0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cbc4: r1 = Function '<anonymous closure>': static.
    //     0x91cbc4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd9b0] AnonymousClosure: static (0x91d02c), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cbc8: ldr             x1, [x1, #0x9b0]
    // 0x91cbcc: r2 = Null
    //     0x91cbcc: mov             x2, NULL
    // 0x91cbd0: r0 = AllocateClosure()
    //     0x91cbd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91cbd4: stur            x0, [fp, #-8]
    // 0x91cbd8: r0 = RatingReviewOrderBinding()
    //     0x91cbd8: bl              #0x91ced0  ; AllocateRatingReviewOrderBindingStub -> RatingReviewOrderBinding (size=0x8)
    // 0x91cbdc: r1 = Null
    //     0x91cbdc: mov             x1, NULL
    // 0x91cbe0: stur            x0, [fp, #-0x18]
    // 0x91cbe4: r0 = GetPage()
    //     0x91cbe4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91cbe8: mov             x1, x0
    // 0x91cbec: ldur            x2, [fp, #-0x18]
    // 0x91cbf0: ldur            x5, [fp, #-8]
    // 0x91cbf4: r3 = "/rating_review_for_order"
    //     0x91cbf4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9b8] "/rating_review_for_order"
    //     0x91cbf8: ldr             x3, [x3, #0x9b8]
    // 0x91cbfc: stur            x0, [fp, #-8]
    // 0x91cc00: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cc00: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cc04: r0 = GetPage()
    //     0x91cc04: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cc08: ldur            x1, [fp, #-0x10]
    // 0x91cc0c: ldur            x0, [fp, #-8]
    // 0x91cc10: ArrayStore: r1[28] = r0  ; List_4
    //     0x91cc10: add             x25, x1, #0x7f
    //     0x91cc14: str             w0, [x25]
    //     0x91cc18: tbz             w0, #0, #0x91cc34
    //     0x91cc1c: ldurb           w16, [x1, #-1]
    //     0x91cc20: ldurb           w17, [x0, #-1]
    //     0x91cc24: and             x16, x17, x16, lsr #2
    //     0x91cc28: tst             x16, HEAP, lsr #32
    //     0x91cc2c: b.eq            #0x91cc34
    //     0x91cc30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cc34: r1 = Function '<anonymous closure>': static.
    //     0x91cc34: add             x1, PP, #0xd, lsl #12  ; [pp+0xd9c0] AnonymousClosure: static (0x91d020), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cc38: ldr             x1, [x1, #0x9c0]
    // 0x91cc3c: r2 = Null
    //     0x91cc3c: mov             x2, NULL
    // 0x91cc40: r0 = AllocateClosure()
    //     0x91cc40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91cc44: stur            x0, [fp, #-8]
    // 0x91cc48: r0 = RatingReviewBinding()
    //     0x91cc48: bl              #0x91cec4  ; AllocateRatingReviewBindingStub -> RatingReviewBinding (size=0x8)
    // 0x91cc4c: r1 = Null
    //     0x91cc4c: mov             x1, NULL
    // 0x91cc50: stur            x0, [fp, #-0x18]
    // 0x91cc54: r0 = GetPage()
    //     0x91cc54: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91cc58: mov             x1, x0
    // 0x91cc5c: ldur            x2, [fp, #-0x18]
    // 0x91cc60: ldur            x5, [fp, #-8]
    // 0x91cc64: r3 = "/rating_review_media_screen"
    //     0x91cc64: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9c8] "/rating_review_media_screen"
    //     0x91cc68: ldr             x3, [x3, #0x9c8]
    // 0x91cc6c: stur            x0, [fp, #-8]
    // 0x91cc70: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cc70: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cc74: r0 = GetPage()
    //     0x91cc74: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cc78: ldur            x1, [fp, #-0x10]
    // 0x91cc7c: ldur            x0, [fp, #-8]
    // 0x91cc80: ArrayStore: r1[29] = r0  ; List_4
    //     0x91cc80: add             x25, x1, #0x83
    //     0x91cc84: str             w0, [x25]
    //     0x91cc88: tbz             w0, #0, #0x91cca4
    //     0x91cc8c: ldurb           w16, [x1, #-1]
    //     0x91cc90: ldurb           w17, [x0, #-1]
    //     0x91cc94: and             x16, x17, x16, lsr #2
    //     0x91cc98: tst             x16, HEAP, lsr #32
    //     0x91cc9c: b.eq            #0x91cca4
    //     0x91cca0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cca4: r1 = Function '<anonymous closure>': static.
    //     0x91cca4: add             x1, PP, #0xd, lsl #12  ; [pp+0xd9d0] AnonymousClosure: static (0x91d014), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cca8: ldr             x1, [x1, #0x9d0]
    // 0x91ccac: r2 = Null
    //     0x91ccac: mov             x2, NULL
    // 0x91ccb0: r0 = AllocateClosure()
    //     0x91ccb0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91ccb4: stur            x0, [fp, #-8]
    // 0x91ccb8: r0 = CheckoutOrderSummaryBinding()
    //     0x91ccb8: bl              #0x91ceb8  ; AllocateCheckoutOrderSummaryBindingStub -> CheckoutOrderSummaryBinding (size=0x8)
    // 0x91ccbc: r1 = Null
    //     0x91ccbc: mov             x1, NULL
    // 0x91ccc0: stur            x0, [fp, #-0x18]
    // 0x91ccc4: r0 = GetPage()
    //     0x91ccc4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91ccc8: mov             x1, x0
    // 0x91cccc: ldur            x2, [fp, #-0x18]
    // 0x91ccd0: ldur            x5, [fp, #-8]
    // 0x91ccd4: r3 = "/checkout_order_summary_page"
    //     0x91ccd4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0x91ccd8: ldr             x3, [x3, #0x9d8]
    // 0x91ccdc: stur            x0, [fp, #-8]
    // 0x91cce0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cce0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cce4: r0 = GetPage()
    //     0x91cce4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cce8: ldur            x1, [fp, #-0x10]
    // 0x91ccec: ldur            x0, [fp, #-8]
    // 0x91ccf0: ArrayStore: r1[30] = r0  ; List_4
    //     0x91ccf0: add             x25, x1, #0x87
    //     0x91ccf4: str             w0, [x25]
    //     0x91ccf8: tbz             w0, #0, #0x91cd14
    //     0x91ccfc: ldurb           w16, [x1, #-1]
    //     0x91cd00: ldurb           w17, [x0, #-1]
    //     0x91cd04: and             x16, x17, x16, lsr #2
    //     0x91cd08: tst             x16, HEAP, lsr #32
    //     0x91cd0c: b.eq            #0x91cd14
    //     0x91cd10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cd14: r1 = Function '<anonymous closure>': static.
    //     0x91cd14: add             x1, PP, #0xd, lsl #12  ; [pp+0xd9e0] AnonymousClosure: static (0x91d008), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cd18: ldr             x1, [x1, #0x9e0]
    // 0x91cd1c: r2 = Null
    //     0x91cd1c: mov             x2, NULL
    // 0x91cd20: r0 = AllocateClosure()
    //     0x91cd20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91cd24: stur            x0, [fp, #-8]
    // 0x91cd28: r0 = CheckoutRequestAddressBinding()
    //     0x91cd28: bl              #0x91ceac  ; AllocateCheckoutRequestAddressBindingStub -> CheckoutRequestAddressBinding (size=0x8)
    // 0x91cd2c: r1 = Null
    //     0x91cd2c: mov             x1, NULL
    // 0x91cd30: stur            x0, [fp, #-0x18]
    // 0x91cd34: r0 = GetPage()
    //     0x91cd34: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91cd38: mov             x1, x0
    // 0x91cd3c: ldur            x2, [fp, #-0x18]
    // 0x91cd40: ldur            x5, [fp, #-8]
    // 0x91cd44: r3 = "/checkout_request_address_page"
    //     0x91cd44: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0x91cd48: ldr             x3, [x3, #0x9e8]
    // 0x91cd4c: stur            x0, [fp, #-8]
    // 0x91cd50: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cd50: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cd54: r0 = GetPage()
    //     0x91cd54: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cd58: ldur            x1, [fp, #-0x10]
    // 0x91cd5c: ldur            x0, [fp, #-8]
    // 0x91cd60: ArrayStore: r1[31] = r0  ; List_4
    //     0x91cd60: add             x25, x1, #0x8b
    //     0x91cd64: str             w0, [x25]
    //     0x91cd68: tbz             w0, #0, #0x91cd84
    //     0x91cd6c: ldurb           w16, [x1, #-1]
    //     0x91cd70: ldurb           w17, [x0, #-1]
    //     0x91cd74: and             x16, x17, x16, lsr #2
    //     0x91cd78: tst             x16, HEAP, lsr #32
    //     0x91cd7c: b.eq            #0x91cd84
    //     0x91cd80: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cd84: r1 = Function '<anonymous closure>': static.
    //     0x91cd84: add             x1, PP, #0xd, lsl #12  ; [pp+0xd9f0] AnonymousClosure: static (0x91cffc), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cd88: ldr             x1, [x1, #0x9f0]
    // 0x91cd8c: r2 = Null
    //     0x91cd8c: mov             x2, NULL
    // 0x91cd90: r0 = AllocateClosure()
    //     0x91cd90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91cd94: stur            x0, [fp, #-8]
    // 0x91cd98: r0 = CheckoutRequestNumberBinding()
    //     0x91cd98: bl              #0x91cea0  ; AllocateCheckoutRequestNumberBindingStub -> CheckoutRequestNumberBinding (size=0x8)
    // 0x91cd9c: r1 = Null
    //     0x91cd9c: mov             x1, NULL
    // 0x91cda0: stur            x0, [fp, #-0x18]
    // 0x91cda4: r0 = GetPage()
    //     0x91cda4: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91cda8: mov             x1, x0
    // 0x91cdac: ldur            x2, [fp, #-0x18]
    // 0x91cdb0: ldur            x5, [fp, #-8]
    // 0x91cdb4: r3 = "/checkout_request_number_page"
    //     0x91cdb4: add             x3, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0x91cdb8: ldr             x3, [x3, #0x9f8]
    // 0x91cdbc: stur            x0, [fp, #-8]
    // 0x91cdc0: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91cdc0: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91cdc4: r0 = GetPage()
    //     0x91cdc4: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91cdc8: ldur            x1, [fp, #-0x10]
    // 0x91cdcc: ldur            x0, [fp, #-8]
    // 0x91cdd0: ArrayStore: r1[32] = r0  ; List_4
    //     0x91cdd0: add             x25, x1, #0x8f
    //     0x91cdd4: str             w0, [x25]
    //     0x91cdd8: tbz             w0, #0, #0x91cdf4
    //     0x91cddc: ldurb           w16, [x1, #-1]
    //     0x91cde0: ldurb           w17, [x0, #-1]
    //     0x91cde4: and             x16, x17, x16, lsr #2
    //     0x91cde8: tst             x16, HEAP, lsr #32
    //     0x91cdec: b.eq            #0x91cdf4
    //     0x91cdf0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91cdf4: r1 = Function '<anonymous closure>': static.
    //     0x91cdf4: add             x1, PP, #0xd, lsl #12  ; [pp+0xda00] AnonymousClosure: static (0x91cff0), in [package:customer_app/app/routes/glass.dart] GlassAppPages::glassPages (0x91bf7c)
    //     0x91cdf8: ldr             x1, [x1, #0xa00]
    // 0x91cdfc: r2 = Null
    //     0x91cdfc: mov             x2, NULL
    // 0x91ce00: r0 = AllocateClosure()
    //     0x91ce00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91ce04: stur            x0, [fp, #-8]
    // 0x91ce08: r0 = CheckoutRequestOtpBinding()
    //     0x91ce08: bl              #0x91ce94  ; AllocateCheckoutRequestOtpBindingStub -> CheckoutRequestOtpBinding (size=0x8)
    // 0x91ce0c: r1 = Null
    //     0x91ce0c: mov             x1, NULL
    // 0x91ce10: stur            x0, [fp, #-0x18]
    // 0x91ce14: r0 = GetPage()
    //     0x91ce14: bl              #0x68c014  ; AllocateGetPageStub -> GetPage<X0> (size=0x84)
    // 0x91ce18: mov             x1, x0
    // 0x91ce1c: ldur            x2, [fp, #-0x18]
    // 0x91ce20: ldur            x5, [fp, #-8]
    // 0x91ce24: r3 = "/checkout_request_otp_page"
    //     0x91ce24: add             x3, PP, #0xd, lsl #12  ; [pp+0xda08] "/checkout_request_otp_page"
    //     0x91ce28: ldr             x3, [x3, #0xa08]
    // 0x91ce2c: stur            x0, [fp, #-8]
    // 0x91ce30: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x91ce30: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x91ce34: r0 = GetPage()
    //     0x91ce34: bl              #0x68b228  ; [package:get/get_navigation/src/routes/get_route.dart] GetPage::GetPage
    // 0x91ce38: ldur            x1, [fp, #-0x10]
    // 0x91ce3c: ldur            x0, [fp, #-8]
    // 0x91ce40: ArrayStore: r1[33] = r0  ; List_4
    //     0x91ce40: add             x25, x1, #0x93
    //     0x91ce44: str             w0, [x25]
    //     0x91ce48: tbz             w0, #0, #0x91ce64
    //     0x91ce4c: ldurb           w16, [x1, #-1]
    //     0x91ce50: ldurb           w17, [x0, #-1]
    //     0x91ce54: and             x16, x17, x16, lsr #2
    //     0x91ce58: tst             x16, HEAP, lsr #32
    //     0x91ce5c: b.eq            #0x91ce64
    //     0x91ce60: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x91ce64: r1 = <GetPage>
    //     0x91ce64: add             x1, PP, #0xb, lsl #12  ; [pp+0xba40] TypeArguments: <GetPage>
    //     0x91ce68: ldr             x1, [x1, #0xa40]
    // 0x91ce6c: r0 = AllocateGrowableArray()
    //     0x91ce6c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x91ce70: ldur            x1, [fp, #-0x10]
    // 0x91ce74: StoreField: r0->field_f = r1
    //     0x91ce74: stur            w1, [x0, #0xf]
    // 0x91ce78: r1 = 68
    //     0x91ce78: movz            x1, #0x44
    // 0x91ce7c: StoreField: r0->field_b = r1
    //     0x91ce7c: stur            w1, [x0, #0xb]
    // 0x91ce80: LeaveFrame
    //     0x91ce80: mov             SP, fp
    //     0x91ce84: ldp             fp, lr, [SP], #0x10
    // 0x91ce88: ret
    //     0x91ce88: ret             
    // 0x91ce8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ce8c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ce90: b               #0x91bf94
  }
  [closure] static CheckoutRequestOtpPage <anonymous closure>(dynamic) {
    // ** addr: 0x91cff0, size: 0xc
    // 0x91cff0: r0 = Instance_CheckoutRequestOtpPage
    //     0x91cff0: add             x0, PP, #0xd, lsl #12  ; [pp+0xda10] Obj!CheckoutRequestOtpPage@d67461
    //     0x91cff4: ldr             x0, [x0, #0xa10]
    // 0x91cff8: ret
    //     0x91cff8: ret             
  }
  [closure] static CheckoutRequestNumberPage <anonymous closure>(dynamic) {
    // ** addr: 0x91cffc, size: 0xc
    // 0x91cffc: r0 = Instance_CheckoutRequestNumberPage
    //     0x91cffc: add             x0, PP, #0xd, lsl #12  ; [pp+0xda18] Obj!CheckoutRequestNumberPage@d67481
    //     0x91d000: ldr             x0, [x0, #0xa18]
    // 0x91d004: ret
    //     0x91d004: ret             
  }
  [closure] static CheckoutRequestAddressPage <anonymous closure>(dynamic) {
    // ** addr: 0x91d008, size: 0xc
    // 0x91d008: r0 = Instance_CheckoutRequestAddressPage
    //     0x91d008: add             x0, PP, #0xd, lsl #12  ; [pp+0xda20] Obj!CheckoutRequestAddressPage@d674a1
    //     0x91d00c: ldr             x0, [x0, #0xa20]
    // 0x91d010: ret
    //     0x91d010: ret             
  }
  [closure] static CheckoutOrderSummaryPage <anonymous closure>(dynamic) {
    // ** addr: 0x91d014, size: 0xc
    // 0x91d014: r0 = Instance_CheckoutOrderSummaryPage
    //     0x91d014: add             x0, PP, #0xd, lsl #12  ; [pp+0xda28] Obj!CheckoutOrderSummaryPage@d674c1
    //     0x91d018: ldr             x0, [x0, #0xa28]
    // 0x91d01c: ret
    //     0x91d01c: ret             
  }
  [closure] static RatingReviewMediaScreen <anonymous closure>(dynamic) {
    // ** addr: 0x91d020, size: 0xc
    // 0x91d020: r0 = Instance_RatingReviewMediaScreen
    //     0x91d020: add             x0, PP, #0xd, lsl #12  ; [pp+0xda30] Obj!RatingReviewMediaScreen@d671a1
    //     0x91d024: ldr             x0, [x0, #0xa30]
    // 0x91d028: ret
    //     0x91d028: ret             
  }
  [closure] static RatingReviewOrderPage <anonymous closure>(dynamic) {
    // ** addr: 0x91d02c, size: 0xc
    // 0x91d02c: r0 = Instance_RatingReviewOrderPage
    //     0x91d02c: add             x0, PP, #0xd, lsl #12  ; [pp+0xda38] Obj!RatingReviewOrderPage@d672e1
    //     0x91d030: ldr             x0, [x0, #0xa38]
    // 0x91d034: ret
    //     0x91d034: ret             
  }
  [closure] static CheckoutVariantsView <anonymous closure>(dynamic) {
    // ** addr: 0x91d300, size: 0xc
    // 0x91d300: r0 = Instance_CheckoutVariantsView
    //     0x91d300: add             x0, PP, #0xd, lsl #12  ; [pp+0xda40] Obj!CheckoutVariantsView@d67441
    //     0x91d304: ldr             x0, [x0, #0xa40]
    // 0x91d308: ret
    //     0x91d308: ret             
  }
  [closure] static ExchangeCheckoutOnlinePaymentMethod <anonymous closure>(dynamic) {
    // ** addr: 0x91d30c, size: 0xc
    // 0x91d30c: r0 = Instance_ExchangeCheckoutOnlinePaymentMethod
    //     0x91d30c: add             x0, PP, #0xd, lsl #12  ; [pp+0xda48] Obj!ExchangeCheckoutOnlinePaymentMethod@d673c1
    //     0x91d310: ldr             x0, [x0, #0xa48]
    // 0x91d314: ret
    //     0x91d314: ret             
  }
  [closure] static ExchangeCheckoutScreen <anonymous closure>(dynamic) {
    // ** addr: 0x91d340, size: 0xc
    // 0x91d340: r0 = Instance_ExchangeCheckoutScreen
    //     0x91d340: add             x0, PP, #0xd, lsl #12  ; [pp+0xda50] Obj!ExchangeCheckoutScreen@d673a1
    //     0x91d344: ldr             x0, [x0, #0xa50]
    // 0x91d348: ret
    //     0x91d348: ret             
  }
  [closure] static ExchangeProductSkusScreen <anonymous closure>(dynamic) {
    // ** addr: 0x91d34c, size: 0xc
    // 0x91d34c: r0 = Instance_ExchangeProductSkusScreen
    //     0x91d34c: add             x0, PP, #0xd, lsl #12  ; [pp+0xda58] Obj!ExchangeProductSkusScreen@d67381
    //     0x91d350: ldr             x0, [x0, #0xa58]
    // 0x91d354: ret
    //     0x91d354: ret             
  }
  [closure] static ExchangeReturnIntermediateScreen <anonymous closure>(dynamic) {
    // ** addr: 0x91d380, size: 0xc
    // 0x91d380: r0 = Instance_ExchangeReturnIntermediateScreen
    //     0x91d380: add             x0, PP, #0xd, lsl #12  ; [pp+0xda60] Obj!ExchangeReturnIntermediateScreen@d67361
    //     0x91d384: ldr             x0, [x0, #0xa60]
    // 0x91d388: ret
    //     0x91d388: ret             
  }
  [closure] static ContactUsWidget <anonymous closure>(dynamic) {
    // ** addr: 0x91d38c, size: 0xc
    // 0x91d38c: r0 = Instance_ContactUsWidget
    //     0x91d38c: add             x0, PP, #0xd, lsl #12  ; [pp+0xda68] Obj!ContactUsWidget@d67401
    //     0x91d390: ldr             x0, [x0, #0xa68]
    // 0x91d394: ret
    //     0x91d394: ret             
  }
  [closure] static ReviewListWidget <anonymous closure>(dynamic) {
    // ** addr: 0x91d398, size: 0xc
    // 0x91d398: r0 = Instance_ReviewListWidget
    //     0x91d398: add             x0, PP, #0xd, lsl #12  ; [pp+0xda70] Obj!ReviewListWidget@d67201
    //     0x91d39c: ldr             x0, [x0, #0xa70]
    // 0x91d3a0: ret
    //     0x91d3a0: ret             
  }
  [closure] static PolicyWidget <anonymous closure>(dynamic) {
    // ** addr: 0x91d3a4, size: 0xc
    // 0x91d3a4: r0 = Instance_PolicyWidget
    //     0x91d3a4: add             x0, PP, #0xd, lsl #12  ; [pp+0xda78] Obj!PolicyWidget@d671c1
    //     0x91d3a8: ldr             x0, [x0, #0xa78]
    // 0x91d3ac: ret
    //     0x91d3ac: ret             
  }
  [closure] static OrderFailedWidget <anonymous closure>(dynamic) {
    // ** addr: 0x91d3f4, size: 0xc
    // 0x91d3f4: r0 = Instance_OrderFailedWidget
    //     0x91d3f4: add             x0, PP, #0xd, lsl #12  ; [pp+0xda80] Obj!OrderFailedWidget@d672c1
    //     0x91d3f8: ldr             x0, [x0, #0xa80]
    // 0x91d3fc: ret
    //     0x91d3fc: ret             
  }
  [closure] static OrderSuccessWidget <anonymous closure>(dynamic) {
    // ** addr: 0x91d400, size: 0xc
    // 0x91d400: r0 = Instance_OrderSuccessWidget
    //     0x91d400: add             x0, PP, #0xd, lsl #12  ; [pp+0xda88] Obj!OrderSuccessWidget@d672a1
    //     0x91d404: ldr             x0, [x0, #0xa88]
    // 0x91d408: ret
    //     0x91d408: ret             
  }
  [closure] static PaymentMethodsCheckoutWidget <anonymous closure>(dynamic) {
    // ** addr: 0x91d40c, size: 0xc
    // 0x91d40c: r0 = Instance_PaymentMethodsCheckoutWidget
    //     0x91d40c: add             x0, PP, #0xd, lsl #12  ; [pp+0xda90] Obj!PaymentMethodsCheckoutWidget@d67421
    //     0x91d410: ldr             x0, [x0, #0xa90]
    // 0x91d414: ret
    //     0x91d414: ret             
  }
  [closure] static ReplaceCallOrderView <anonymous closure>(dynamic) {
    // ** addr: 0x91d418, size: 0xc
    // 0x91d418: r0 = Instance_ReplaceCallOrderView
    //     0x91d418: add             x0, PP, #0xd, lsl #12  ; [pp+0xda98] Obj!ReplaceCallOrderView@d67281
    //     0x91d41c: ldr             x0, [x0, #0xa98]
    // 0x91d420: ret
    //     0x91d420: ret             
  }
  [closure] static EnlargeImageView <anonymous closure>(dynamic) {
    // ** addr: 0x91d424, size: 0xc
    // 0x91d424: r0 = Instance_EnlargeImageView
    //     0x91d424: add             x0, PP, #0xd, lsl #12  ; [pp+0xdaa0] Obj!EnlargeImageView@d67321
    //     0x91d428: ldr             x0, [x0, #0xaa0]
    // 0x91d42c: ret
    //     0x91d42c: ret             
  }
  [closure] static BrowseView <anonymous closure>(dynamic) {
    // ** addr: 0x91d430, size: 0xc
    // 0x91d430: r0 = Instance_BrowseView
    //     0x91d430: add             x0, PP, #0xd, lsl #12  ; [pp+0xdaa8] Obj!BrowseView@d674e1
    //     0x91d434: ldr             x0, [x0, #0xaa8]
    // 0x91d438: ret
    //     0x91d438: ret             
  }
  [closure] static ReturnOrderWithProofView <anonymous closure>(dynamic) {
    // ** addr: 0x91d43c, size: 0xc
    // 0x91d43c: r0 = Instance_ReturnOrderWithProofView
    //     0x91d43c: add             x0, PP, #0xd, lsl #12  ; [pp+0xdab0] Obj!ReturnOrderWithProofView@d67241
    //     0x91d440: ldr             x0, [x0, #0xab0]
    // 0x91d444: ret
    //     0x91d444: ret             
  }
  [closure] static ReturnOrderView <anonymous closure>(dynamic) {
    // ** addr: 0x91d470, size: 0xc
    // 0x91d470: r0 = Instance_ReturnOrderView
    //     0x91d470: add             x0, PP, #0xd, lsl #12  ; [pp+0xdab8] Obj!ReturnOrderView@d67261
    //     0x91d474: ldr             x0, [x0, #0xab8]
    // 0x91d478: ret
    //     0x91d478: ret             
  }
  [closure] static CustomizedPage <anonymous closure>(dynamic) {
    // ** addr: 0x91d47c, size: 0xc
    // 0x91d47c: r0 = Instance_CustomizedPage
    //     0x91d47c: add             x0, PP, #0xd, lsl #12  ; [pp+0xdac0] Obj!CustomizedPage@d673e1
    //     0x91d480: ldr             x0, [x0, #0xac0]
    // 0x91d484: ret
    //     0x91d484: ret             
  }
  [closure] static TestimonialsView <anonymous closure>(dynamic) {
    // ** addr: 0x91d4b0, size: 0xc
    // 0x91d4b0: r0 = Instance_TestimonialsView
    //     0x91d4b0: add             x0, PP, #0xd, lsl #12  ; [pp+0xdac8] Obj!TestimonialsView@d67161
    //     0x91d4b4: ldr             x0, [x0, #0xac8]
    // 0x91d4b8: ret
    //     0x91d4b8: ret             
  }
  [closure] static OrderDetailView <anonymous closure>(dynamic) {
    // ** addr: 0x91d4bc, size: 0x54
    // 0x91d4bc: EnterFrame
    //     0x91d4bc: stp             fp, lr, [SP, #-0x10]!
    //     0x91d4c0: mov             fp, SP
    // 0x91d4c4: AllocStack(0x8)
    //     0x91d4c4: sub             SP, SP, #8
    // 0x91d4c8: CheckStackOverflow
    //     0x91d4c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d4cc: cmp             SP, x16
    //     0x91d4d0: b.ls            #0x91d508
    // 0x91d4d4: r0 = ScrollController()
    //     0x91d4d4: bl              #0x675ac8  ; AllocateScrollControllerStub -> ScrollController (size=0x40)
    // 0x91d4d8: mov             x1, x0
    // 0x91d4dc: stur            x0, [fp, #-8]
    // 0x91d4e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91d4e0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91d4e4: r0 = ScrollController()
    //     0x91d4e4: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x91d4e8: r1 = <OrderDetailController>
    //     0x91d4e8: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad0] TypeArguments: <OrderDetailController>
    //     0x91d4ec: ldr             x1, [x1, #0xad0]
    // 0x91d4f0: r0 = OrderDetailView()
    //     0x91d4f0: bl              #0x91d510  ; AllocateOrderDetailViewStub -> OrderDetailView (size=0x18)
    // 0x91d4f4: ldur            x1, [fp, #-8]
    // 0x91d4f8: StoreField: r0->field_13 = r1
    //     0x91d4f8: stur            w1, [x0, #0x13]
    // 0x91d4fc: LeaveFrame
    //     0x91d4fc: mov             SP, fp
    //     0x91d500: ldp             fp, lr, [SP], #0x10
    // 0x91d504: ret
    //     0x91d504: ret             
    // 0x91d508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d508: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d50c: b               #0x91d4d4
  }
  [closure] static LoginView <anonymous closure>(dynamic) {
    // ** addr: 0x91d580, size: 0x50
    // 0x91d580: EnterFrame
    //     0x91d580: stp             fp, lr, [SP, #-0x10]!
    //     0x91d584: mov             fp, SP
    // 0x91d588: AllocStack(0x8)
    //     0x91d588: sub             SP, SP, #8
    // 0x91d58c: SetupParameters()
    //     0x91d58c: add             x1, PP, #0xa, lsl #12  ; [pp+0xac08] TypeArguments: <LoginController>
    //     0x91d590: ldr             x1, [x1, #0xc08]
    // 0x91d58c: r1 = <LoginController>
    // 0x91d594: r0 = LoginView()
    //     0x91d594: bl              #0x91d5d0  ; AllocateLoginViewStub -> LoginView (size=0x20)
    // 0x91d598: mov             x2, x0
    // 0x91d59c: r0 = ""
    //     0x91d59c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x91d5a0: stur            x2, [fp, #-8]
    // 0x91d5a4: ArrayStore: r2[0] = r0  ; List_4
    //     0x91d5a4: stur            w0, [x2, #0x17]
    // 0x91d5a8: StoreField: r2->field_1b = r0
    //     0x91d5a8: stur            w0, [x2, #0x1b]
    // 0x91d5ac: r1 = <FormState>
    //     0x91d5ac: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0x91d5b0: ldr             x1, [x1, #0xad8]
    // 0x91d5b4: r0 = LabeledGlobalKey()
    //     0x91d5b4: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0x91d5b8: mov             x1, x0
    // 0x91d5bc: ldur            x0, [fp, #-8]
    // 0x91d5c0: StoreField: r0->field_13 = r1
    //     0x91d5c0: stur            w1, [x0, #0x13]
    // 0x91d5c4: LeaveFrame
    //     0x91d5c4: mov             SP, fp
    //     0x91d5c8: ldp             fp, lr, [SP], #0x10
    // 0x91d5cc: ret
    //     0x91d5cc: ret             
  }
  [closure] static OrdersView <anonymous closure>(dynamic) {
    // ** addr: 0x91d5fc, size: 0xc
    // 0x91d5fc: r0 = Instance_OrdersView
    //     0x91d5fc: add             x0, PP, #0xd, lsl #12  ; [pp+0xdae0] Obj!OrdersView@d67301
    //     0x91d600: ldr             x0, [x0, #0xae0]
    // 0x91d604: ret
    //     0x91d604: ret             
  }
  [closure] static ProfileView <anonymous closure>(dynamic) {
    // ** addr: 0x91d608, size: 0xc
    // 0x91d608: r0 = Instance_ProfileView
    //     0x91d608: add             x0, PP, #0xd, lsl #12  ; [pp+0xdae8] Obj!ProfileView@d671e1
    //     0x91d60c: ldr             x0, [x0, #0xae8]
    // 0x91d610: ret
    //     0x91d610: ret             
  }
  [closure] static BagView <anonymous closure>(dynamic) {
    // ** addr: 0x91d614, size: 0xc
    // 0x91d614: r0 = Instance_BagView
    //     0x91d614: add             x0, PP, #0xd, lsl #12  ; [pp+0xdaf0] Obj!BagView@d67501
    //     0x91d618: ldr             x0, [x0, #0xaf0]
    // 0x91d61c: ret
    //     0x91d61c: ret             
  }
  [closure] static ProductDetailView <anonymous closure>(dynamic) {
    // ** addr: 0x91d620, size: 0xc
    // 0x91d620: r0 = Instance_ProductDetailView
    //     0x91d620: add             x0, PP, #0xd, lsl #12  ; [pp+0xdaf8] Obj!ProductDetailView@d67221
    //     0x91d624: ldr             x0, [x0, #0xaf8]
    // 0x91d628: ret
    //     0x91d628: ret             
  }
  [closure] static CollectionPage <anonymous closure>(dynamic) {
    // ** addr: 0x91d62c, size: 0x48
    // 0x91d62c: EnterFrame
    //     0x91d62c: stp             fp, lr, [SP, #-0x10]!
    //     0x91d630: mov             fp, SP
    // 0x91d634: AllocStack(0x8)
    //     0x91d634: sub             SP, SP, #8
    // 0x91d638: CheckStackOverflow
    //     0x91d638: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d63c: cmp             SP, x16
    //     0x91d640: b.ls            #0x91d66c
    // 0x91d644: r1 = <CollectionsController>
    //     0x91d644: add             x1, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0x91d648: ldr             x1, [x1, #0xb00]
    // 0x91d64c: r0 = CollectionPage()
    //     0x91d64c: bl              #0x91dafc  ; AllocateCollectionPageStub -> CollectionPage (size=0x2c)
    // 0x91d650: mov             x1, x0
    // 0x91d654: stur            x0, [fp, #-8]
    // 0x91d658: r0 = CollectionPage()
    //     0x91d658: bl              #0x91d674  ; [package:customer_app/app/presentation/views/basic/collections/collection_page.dart] CollectionPage::CollectionPage
    // 0x91d65c: ldur            x0, [fp, #-8]
    // 0x91d660: LeaveFrame
    //     0x91d660: mov             SP, fp
    //     0x91d664: ldp             fp, lr, [SP], #0x10
    // 0x91d668: ret
    //     0x91d668: ret             
    // 0x91d66c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d66c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d670: b               #0x91d644
  }
  [closure] static SearchPage <anonymous closure>(dynamic) {
    // ** addr: 0x92045c, size: 0xc
    // 0x92045c: r0 = Instance_SearchPage
    //     0x92045c: add             x0, PP, #0xd, lsl #12  ; [pp+0xdb10] Obj!SearchPage@d67181
    //     0x920460: ldr             x0, [x0, #0xb10]
    // 0x920464: ret
    //     0x920464: ret             
  }
  [closure] static HomePage <anonymous closure>(dynamic) {
    // ** addr: 0x920468, size: 0xc
    // 0x920468: r0 = Instance_HomePage
    //     0x920468: add             x0, PP, #0xd, lsl #12  ; [pp+0xdb18] Obj!HomePage@d67341
    //     0x92046c: ldr             x0, [x0, #0xb18]
    // 0x920470: ret
    //     0x920470: ret             
  }
  [closure] static MainPage <anonymous closure>(dynamic) {
    // ** addr: 0x920474, size: 0xc
    // 0x920474: r0 = Instance_MainPage
    //     0x920474: add             x0, PP, #0xd, lsl #12  ; [pp+0xdb20] Obj!MainPage@d658a1
    //     0x920478: ldr             x0, [x0, #0xb20]
    // 0x92047c: ret
    //     0x92047c: ret             
  }
}
