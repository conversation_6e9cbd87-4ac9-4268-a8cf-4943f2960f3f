// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_title_item_view.dart

// class id: 1049563, size: 0x8
class :: {
}

// class id: 3220, size: 0x14, field offset: 0x14
class _ProductTitleItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xc081f4, size: 0xa2c
    // 0xc081f4: EnterFrame
    //     0xc081f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc081f8: mov             fp, SP
    // 0xc081fc: AllocStack(0x68)
    //     0xc081fc: sub             SP, SP, #0x68
    // 0xc08200: SetupParameters(_ProductTitleItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc08200: stur            x1, [fp, #-8]
    //     0xc08204: stur            x2, [fp, #-0x10]
    // 0xc08208: CheckStackOverflow
    //     0xc08208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0820c: cmp             SP, x16
    //     0xc08210: b.ls            #0xc08c04
    // 0xc08214: r1 = 2
    //     0xc08214: movz            x1, #0x2
    // 0xc08218: r0 = AllocateContext()
    //     0xc08218: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0821c: mov             x1, x0
    // 0xc08220: ldur            x0, [fp, #-8]
    // 0xc08224: stur            x1, [fp, #-0x28]
    // 0xc08228: StoreField: r1->field_f = r0
    //     0xc08228: stur            w0, [x1, #0xf]
    // 0xc0822c: ldur            x2, [fp, #-0x10]
    // 0xc08230: StoreField: r1->field_13 = r2
    //     0xc08230: stur            w2, [x1, #0x13]
    // 0xc08234: LoadField: r2 = r0->field_b
    //     0xc08234: ldur            w2, [x0, #0xb]
    // 0xc08238: DecompressPointer r2
    //     0xc08238: add             x2, x2, HEAP, lsl #32
    // 0xc0823c: cmp             w2, NULL
    // 0xc08240: b.eq            #0xc08c0c
    // 0xc08244: LoadField: r3 = r2->field_b
    //     0xc08244: ldur            w3, [x2, #0xb]
    // 0xc08248: DecompressPointer r3
    //     0xc08248: add             x3, x3, HEAP, lsl #32
    // 0xc0824c: stur            x3, [fp, #-0x20]
    // 0xc08250: LoadField: r4 = r2->field_f
    //     0xc08250: ldur            w4, [x2, #0xf]
    // 0xc08254: DecompressPointer r4
    //     0xc08254: add             x4, x4, HEAP, lsl #32
    // 0xc08258: stur            x4, [fp, #-0x18]
    // 0xc0825c: LoadField: r5 = r2->field_13
    //     0xc0825c: ldur            w5, [x2, #0x13]
    // 0xc08260: DecompressPointer r5
    //     0xc08260: add             x5, x5, HEAP, lsl #32
    // 0xc08264: stur            x5, [fp, #-0x10]
    // 0xc08268: r0 = ProductTitleWidget()
    //     0xc08268: bl              #0xa898b0  ; AllocateProductTitleWidgetStub -> ProductTitleWidget (size=0x20)
    // 0xc0826c: mov             x3, x0
    // 0xc08270: ldur            x0, [fp, #-0x20]
    // 0xc08274: stur            x3, [fp, #-0x30]
    // 0xc08278: StoreField: r3->field_f = r0
    //     0xc08278: stur            w0, [x3, #0xf]
    // 0xc0827c: ldur            x1, [fp, #-0x10]
    // 0xc08280: StoreField: r3->field_b = r1
    //     0xc08280: stur            w1, [x3, #0xb]
    // 0xc08284: ldur            x1, [fp, #-0x18]
    // 0xc08288: StoreField: r3->field_13 = r1
    //     0xc08288: stur            w1, [x3, #0x13]
    // 0xc0828c: ldur            x2, [fp, #-0x28]
    // 0xc08290: r1 = Function '<anonymous closure>':.
    //     0xc08290: add             x1, PP, #0x52, lsl #12  ; [pp+0x525f0] AnonymousClosure: (0xc08cd0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_title_item_view.dart] _ProductTitleItemViewState::build (0xc081f4)
    //     0xc08294: ldr             x1, [x1, #0x5f0]
    // 0xc08298: r0 = AllocateClosure()
    //     0xc08298: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0829c: mov             x1, x0
    // 0xc082a0: ldur            x0, [fp, #-0x30]
    // 0xc082a4: StoreField: r0->field_1b = r1
    //     0xc082a4: stur            w1, [x0, #0x1b]
    // 0xc082a8: r1 = Instance_BorderRadius
    //     0xc082a8: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xc082ac: ldr             x1, [x1, #0xf70]
    // 0xc082b0: ArrayStore: r0[0] = r1  ; List_4
    //     0xc082b0: stur            w1, [x0, #0x17]
    // 0xc082b4: ldur            x1, [fp, #-0x20]
    // 0xc082b8: LoadField: r2 = r1->field_4b
    //     0xc082b8: ldur            w2, [x1, #0x4b]
    // 0xc082bc: DecompressPointer r2
    //     0xc082bc: add             x2, x2, HEAP, lsl #32
    // 0xc082c0: cmp             w2, NULL
    // 0xc082c4: b.ne            #0xc082d0
    // 0xc082c8: r1 = Null
    //     0xc082c8: mov             x1, NULL
    // 0xc082cc: b               #0xc082e8
    // 0xc082d0: LoadField: r1 = r2->field_7
    //     0xc082d0: ldur            w1, [x2, #7]
    // 0xc082d4: cbnz            w1, #0xc082e0
    // 0xc082d8: r2 = false
    //     0xc082d8: add             x2, NULL, #0x30  ; false
    // 0xc082dc: b               #0xc082e4
    // 0xc082e0: r2 = true
    //     0xc082e0: add             x2, NULL, #0x20  ; true
    // 0xc082e4: mov             x1, x2
    // 0xc082e8: cmp             w1, NULL
    // 0xc082ec: b.ne            #0xc082f8
    // 0xc082f0: r3 = false
    //     0xc082f0: add             x3, NULL, #0x30  ; false
    // 0xc082f4: b               #0xc082fc
    // 0xc082f8: mov             x3, x1
    // 0xc082fc: ldur            x2, [fp, #-8]
    // 0xc08300: stur            x3, [fp, #-0x10]
    // 0xc08304: r1 = Instance_Color
    //     0xc08304: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc08308: ldr             x1, [x1, #0x858]
    // 0xc0830c: d0 = 0.080000
    //     0xc0830c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xc08310: ldr             d0, [x17, #0x798]
    // 0xc08314: r0 = withOpacity()
    //     0xc08314: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc08318: stur            x0, [fp, #-0x18]
    // 0xc0831c: r0 = BoxDecoration()
    //     0xc0831c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc08320: mov             x2, x0
    // 0xc08324: ldur            x0, [fp, #-0x18]
    // 0xc08328: stur            x2, [fp, #-0x20]
    // 0xc0832c: StoreField: r2->field_7 = r0
    //     0xc0832c: stur            w0, [x2, #7]
    // 0xc08330: r0 = Instance_BoxShape
    //     0xc08330: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc08334: ldr             x0, [x0, #0x80]
    // 0xc08338: StoreField: r2->field_23 = r0
    //     0xc08338: stur            w0, [x2, #0x23]
    // 0xc0833c: r1 = Instance_Color
    //     0xc0833c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc08340: d0 = 0.100000
    //     0xc08340: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc08344: r0 = withOpacity()
    //     0xc08344: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc08348: mov             x2, x0
    // 0xc0834c: ldur            x0, [fp, #-8]
    // 0xc08350: stur            x2, [fp, #-0x38]
    // 0xc08354: LoadField: r1 = r0->field_b
    //     0xc08354: ldur            w1, [x0, #0xb]
    // 0xc08358: DecompressPointer r1
    //     0xc08358: add             x1, x1, HEAP, lsl #32
    // 0xc0835c: cmp             w1, NULL
    // 0xc08360: b.eq            #0xc08c10
    // 0xc08364: LoadField: r3 = r1->field_b
    //     0xc08364: ldur            w3, [x1, #0xb]
    // 0xc08368: DecompressPointer r3
    //     0xc08368: add             x3, x3, HEAP, lsl #32
    // 0xc0836c: LoadField: r1 = r3->field_4b
    //     0xc0836c: ldur            w1, [x3, #0x4b]
    // 0xc08370: DecompressPointer r1
    //     0xc08370: add             x1, x1, HEAP, lsl #32
    // 0xc08374: cmp             w1, NULL
    // 0xc08378: b.ne            #0xc08384
    // 0xc0837c: r5 = ""
    //     0xc0837c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc08380: b               #0xc08388
    // 0xc08384: mov             x5, x1
    // 0xc08388: ldur            x4, [fp, #-0x28]
    // 0xc0838c: ldur            x3, [fp, #-0x10]
    // 0xc08390: stur            x5, [fp, #-0x18]
    // 0xc08394: LoadField: r1 = r4->field_13
    //     0xc08394: ldur            w1, [x4, #0x13]
    // 0xc08398: DecompressPointer r1
    //     0xc08398: add             x1, x1, HEAP, lsl #32
    // 0xc0839c: r0 = of()
    //     0xc0839c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc083a0: LoadField: r1 = r0->field_87
    //     0xc083a0: ldur            w1, [x0, #0x87]
    // 0xc083a4: DecompressPointer r1
    //     0xc083a4: add             x1, x1, HEAP, lsl #32
    // 0xc083a8: LoadField: r0 = r1->field_7
    //     0xc083a8: ldur            w0, [x1, #7]
    // 0xc083ac: DecompressPointer r0
    //     0xc083ac: add             x0, x0, HEAP, lsl #32
    // 0xc083b0: r16 = 12.000000
    //     0xc083b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc083b4: ldr             x16, [x16, #0x9e8]
    // 0xc083b8: str             x16, [SP]
    // 0xc083bc: mov             x1, x0
    // 0xc083c0: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xc083c0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xc083c4: ldr             x4, [x4, #0x798]
    // 0xc083c8: r0 = copyWith()
    //     0xc083c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc083cc: stur            x0, [fp, #-0x40]
    // 0xc083d0: r0 = Text()
    //     0xc083d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc083d4: mov             x1, x0
    // 0xc083d8: ldur            x0, [fp, #-0x18]
    // 0xc083dc: stur            x1, [fp, #-0x48]
    // 0xc083e0: StoreField: r1->field_b = r0
    //     0xc083e0: stur            w0, [x1, #0xb]
    // 0xc083e4: ldur            x0, [fp, #-0x40]
    // 0xc083e8: StoreField: r1->field_13 = r0
    //     0xc083e8: stur            w0, [x1, #0x13]
    // 0xc083ec: r0 = Instance_TextAlign
    //     0xc083ec: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xc083f0: StoreField: r1->field_1b = r0
    //     0xc083f0: stur            w0, [x1, #0x1b]
    // 0xc083f4: r0 = Padding()
    //     0xc083f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc083f8: mov             x3, x0
    // 0xc083fc: r0 = Instance_EdgeInsets
    //     0xc083fc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0xc08400: ldr             x0, [x0, #0xa68]
    // 0xc08404: stur            x3, [fp, #-0x18]
    // 0xc08408: StoreField: r3->field_f = r0
    //     0xc08408: stur            w0, [x3, #0xf]
    // 0xc0840c: ldur            x0, [fp, #-0x48]
    // 0xc08410: StoreField: r3->field_b = r0
    //     0xc08410: stur            w0, [x3, #0xb]
    // 0xc08414: r1 = Null
    //     0xc08414: mov             x1, NULL
    // 0xc08418: r2 = 2
    //     0xc08418: movz            x2, #0x2
    // 0xc0841c: r0 = AllocateArray()
    //     0xc0841c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc08420: mov             x2, x0
    // 0xc08424: ldur            x0, [fp, #-0x18]
    // 0xc08428: stur            x2, [fp, #-0x40]
    // 0xc0842c: StoreField: r2->field_f = r0
    //     0xc0842c: stur            w0, [x2, #0xf]
    // 0xc08430: r1 = <Widget>
    //     0xc08430: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc08434: r0 = AllocateGrowableArray()
    //     0xc08434: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc08438: mov             x1, x0
    // 0xc0843c: ldur            x0, [fp, #-0x40]
    // 0xc08440: stur            x1, [fp, #-0x18]
    // 0xc08444: StoreField: r1->field_f = r0
    //     0xc08444: stur            w0, [x1, #0xf]
    // 0xc08448: r0 = 2
    //     0xc08448: movz            x0, #0x2
    // 0xc0844c: StoreField: r1->field_b = r0
    //     0xc0844c: stur            w0, [x1, #0xb]
    // 0xc08450: r0 = Row()
    //     0xc08450: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc08454: mov             x1, x0
    // 0xc08458: r0 = Instance_Axis
    //     0xc08458: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0845c: stur            x1, [fp, #-0x40]
    // 0xc08460: StoreField: r1->field_f = r0
    //     0xc08460: stur            w0, [x1, #0xf]
    // 0xc08464: r2 = Instance_MainAxisAlignment
    //     0xc08464: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc08468: ldr             x2, [x2, #0xab0]
    // 0xc0846c: StoreField: r1->field_13 = r2
    //     0xc0846c: stur            w2, [x1, #0x13]
    // 0xc08470: r3 = Instance_MainAxisSize
    //     0xc08470: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc08474: ldr             x3, [x3, #0xa10]
    // 0xc08478: ArrayStore: r1[0] = r3  ; List_4
    //     0xc08478: stur            w3, [x1, #0x17]
    // 0xc0847c: r4 = Instance_CrossAxisAlignment
    //     0xc0847c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc08480: ldr             x4, [x4, #0xa18]
    // 0xc08484: StoreField: r1->field_1b = r4
    //     0xc08484: stur            w4, [x1, #0x1b]
    // 0xc08488: r5 = Instance_VerticalDirection
    //     0xc08488: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0848c: ldr             x5, [x5, #0xa20]
    // 0xc08490: StoreField: r1->field_23 = r5
    //     0xc08490: stur            w5, [x1, #0x23]
    // 0xc08494: r6 = Instance_Clip
    //     0xc08494: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc08498: ldr             x6, [x6, #0x38]
    // 0xc0849c: StoreField: r1->field_2b = r6
    //     0xc0849c: stur            w6, [x1, #0x2b]
    // 0xc084a0: StoreField: r1->field_2f = rZR
    //     0xc084a0: stur            xzr, [x1, #0x2f]
    // 0xc084a4: ldur            x7, [fp, #-0x18]
    // 0xc084a8: StoreField: r1->field_b = r7
    //     0xc084a8: stur            w7, [x1, #0xb]
    // 0xc084ac: r0 = Center()
    //     0xc084ac: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc084b0: mov             x1, x0
    // 0xc084b4: r0 = Instance_Alignment
    //     0xc084b4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc084b8: ldr             x0, [x0, #0xb10]
    // 0xc084bc: stur            x1, [fp, #-0x18]
    // 0xc084c0: StoreField: r1->field_f = r0
    //     0xc084c0: stur            w0, [x1, #0xf]
    // 0xc084c4: ldur            x2, [fp, #-0x40]
    // 0xc084c8: StoreField: r1->field_b = r2
    //     0xc084c8: stur            w2, [x1, #0xb]
    // 0xc084cc: r0 = DottedBorder()
    //     0xc084cc: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0xc084d0: mov             x1, x0
    // 0xc084d4: ldur            x2, [fp, #-0x18]
    // 0xc084d8: ldur            x3, [fp, #-0x38]
    // 0xc084dc: r5 = const [2.0, 2.0]
    //     0xc084dc: add             x5, PP, #0x52, lsl #12  ; [pp+0x525f8] List<double>(2)
    //     0xc084e0: ldr             x5, [x5, #0x5f8]
    // 0xc084e4: d0 = 0.500000
    //     0xc084e4: fmov            d0, #0.50000000
    // 0xc084e8: stur            x0, [fp, #-0x18]
    // 0xc084ec: r4 = const [0, 0x5, 0, 0x5, null]
    //     0xc084ec: ldr             x4, [PP, #0x1330]  ; [pp+0x1330] List(5) [0, 0x5, 0, 0x5, Null]
    // 0xc084f0: r0 = DottedBorder()
    //     0xc084f0: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0xc084f4: r0 = Container()
    //     0xc084f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc084f8: stur            x0, [fp, #-0x38]
    // 0xc084fc: r16 = 28.000000
    //     0xc084fc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52600] 28
    //     0xc08500: ldr             x16, [x16, #0x600]
    // 0xc08504: ldur            lr, [fp, #-0x20]
    // 0xc08508: stp             lr, x16, [SP, #8]
    // 0xc0850c: ldur            x16, [fp, #-0x18]
    // 0xc08510: str             x16, [SP]
    // 0xc08514: mov             x1, x0
    // 0xc08518: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xc08518: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xc0851c: ldr             x4, [x4, #0xc78]
    // 0xc08520: r0 = Container()
    //     0xc08520: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc08524: r0 = IntrinsicWidth()
    //     0xc08524: bl              #0xa898a4  ; AllocateIntrinsicWidthStub -> IntrinsicWidth (size=0x18)
    // 0xc08528: mov             x1, x0
    // 0xc0852c: ldur            x0, [fp, #-0x38]
    // 0xc08530: stur            x1, [fp, #-0x18]
    // 0xc08534: StoreField: r1->field_b = r0
    //     0xc08534: stur            w0, [x1, #0xb]
    // 0xc08538: r0 = Visibility()
    //     0xc08538: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0853c: mov             x2, x0
    // 0xc08540: ldur            x0, [fp, #-0x18]
    // 0xc08544: stur            x2, [fp, #-0x20]
    // 0xc08548: StoreField: r2->field_b = r0
    //     0xc08548: stur            w0, [x2, #0xb]
    // 0xc0854c: r0 = Instance_SizedBox
    //     0xc0854c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc08550: StoreField: r2->field_f = r0
    //     0xc08550: stur            w0, [x2, #0xf]
    // 0xc08554: ldur            x0, [fp, #-0x10]
    // 0xc08558: StoreField: r2->field_13 = r0
    //     0xc08558: stur            w0, [x2, #0x13]
    // 0xc0855c: r0 = false
    //     0xc0855c: add             x0, NULL, #0x30  ; false
    // 0xc08560: ArrayStore: r2[0] = r0  ; List_4
    //     0xc08560: stur            w0, [x2, #0x17]
    // 0xc08564: StoreField: r2->field_1b = r0
    //     0xc08564: stur            w0, [x2, #0x1b]
    // 0xc08568: StoreField: r2->field_1f = r0
    //     0xc08568: stur            w0, [x2, #0x1f]
    // 0xc0856c: StoreField: r2->field_23 = r0
    //     0xc0856c: stur            w0, [x2, #0x23]
    // 0xc08570: StoreField: r2->field_27 = r0
    //     0xc08570: stur            w0, [x2, #0x27]
    // 0xc08574: StoreField: r2->field_2b = r0
    //     0xc08574: stur            w0, [x2, #0x2b]
    // 0xc08578: ldur            x3, [fp, #-8]
    // 0xc0857c: LoadField: r1 = r3->field_b
    //     0xc0857c: ldur            w1, [x3, #0xb]
    // 0xc08580: DecompressPointer r1
    //     0xc08580: add             x1, x1, HEAP, lsl #32
    // 0xc08584: cmp             w1, NULL
    // 0xc08588: b.eq            #0xc08c14
    // 0xc0858c: LoadField: r4 = r1->field_b
    //     0xc0858c: ldur            w4, [x1, #0xb]
    // 0xc08590: DecompressPointer r4
    //     0xc08590: add             x4, x4, HEAP, lsl #32
    // 0xc08594: LoadField: r1 = r4->field_4b
    //     0xc08594: ldur            w1, [x4, #0x4b]
    // 0xc08598: DecompressPointer r1
    //     0xc08598: add             x1, x1, HEAP, lsl #32
    // 0xc0859c: cmp             w1, NULL
    // 0xc085a0: b.ne            #0xc085ac
    // 0xc085a4: r1 = Null
    //     0xc085a4: mov             x1, NULL
    // 0xc085a8: b               #0xc085c0
    // 0xc085ac: LoadField: r4 = r1->field_7
    //     0xc085ac: ldur            w4, [x1, #7]
    // 0xc085b0: cbnz            w4, #0xc085bc
    // 0xc085b4: r1 = false
    //     0xc085b4: add             x1, NULL, #0x30  ; false
    // 0xc085b8: b               #0xc085c0
    // 0xc085bc: r1 = true
    //     0xc085bc: add             x1, NULL, #0x20  ; true
    // 0xc085c0: cmp             w1, NULL
    // 0xc085c4: b.eq            #0xc085d8
    // 0xc085c8: tbnz            w1, #4, #0xc085d8
    // 0xc085cc: r4 = Instance_EdgeInsets
    //     0xc085cc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xc085d0: ldr             x4, [x4, #0xe60]
    // 0xc085d4: b               #0xc085dc
    // 0xc085d8: r4 = Instance_EdgeInsets
    //     0xc085d8: ldr             x4, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xc085dc: stur            x4, [fp, #-0x10]
    // 0xc085e0: r1 = Instance_Color
    //     0xc085e0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc085e4: ldr             x1, [x1, #0x858]
    // 0xc085e8: d0 = 0.080000
    //     0xc085e8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xc085ec: ldr             d0, [x17, #0x798]
    // 0xc085f0: r0 = withOpacity()
    //     0xc085f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc085f4: stur            x0, [fp, #-0x18]
    // 0xc085f8: r0 = BoxDecoration()
    //     0xc085f8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc085fc: mov             x2, x0
    // 0xc08600: ldur            x0, [fp, #-0x18]
    // 0xc08604: stur            x2, [fp, #-0x38]
    // 0xc08608: StoreField: r2->field_7 = r0
    //     0xc08608: stur            w0, [x2, #7]
    // 0xc0860c: r0 = Instance_BoxShape
    //     0xc0860c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc08610: ldr             x0, [x0, #0x80]
    // 0xc08614: StoreField: r2->field_23 = r0
    //     0xc08614: stur            w0, [x2, #0x23]
    // 0xc08618: r1 = Instance_Color
    //     0xc08618: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0861c: d0 = 0.100000
    //     0xc0861c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc08620: r0 = withOpacity()
    //     0xc08620: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc08624: mov             x2, x0
    // 0xc08628: ldur            x0, [fp, #-8]
    // 0xc0862c: stur            x2, [fp, #-0x40]
    // 0xc08630: LoadField: r1 = r0->field_b
    //     0xc08630: ldur            w1, [x0, #0xb]
    // 0xc08634: DecompressPointer r1
    //     0xc08634: add             x1, x1, HEAP, lsl #32
    // 0xc08638: cmp             w1, NULL
    // 0xc0863c: b.eq            #0xc08c18
    // 0xc08640: LoadField: r3 = r1->field_27
    //     0xc08640: ldur            w3, [x1, #0x27]
    // 0xc08644: DecompressPointer r3
    //     0xc08644: add             x3, x3, HEAP, lsl #32
    // 0xc08648: cmp             w3, NULL
    // 0xc0864c: b.ne            #0xc08658
    // 0xc08650: r1 = Null
    //     0xc08650: mov             x1, NULL
    // 0xc08654: b               #0xc08660
    // 0xc08658: LoadField: r1 = r3->field_b
    //     0xc08658: ldur            w1, [x3, #0xb]
    // 0xc0865c: DecompressPointer r1
    //     0xc0865c: add             x1, x1, HEAP, lsl #32
    // 0xc08660: cmp             w1, NULL
    // 0xc08664: b.eq            #0xc08688
    // 0xc08668: r3 = LoadInt32Instr(r1)
    //     0xc08668: sbfx            x3, x1, #1, #0x1f
    //     0xc0866c: tbz             w1, #0, #0xc08674
    //     0xc08670: ldur            x3, [x1, #7]
    // 0xc08674: cmp             x3, #0
    // 0xc08678: b.le            #0xc08688
    // 0xc0867c: r4 = "Get this as low as "
    //     0xc0867c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52608] "Get this as low as "
    //     0xc08680: ldr             x4, [x4, #0x608]
    // 0xc08684: b               #0xc08690
    // 0xc08688: r4 = "View available offers"
    //     0xc08688: add             x4, PP, #0x52, lsl #12  ; [pp+0x52610] "View available offers"
    //     0xc0868c: ldr             x4, [x4, #0x610]
    // 0xc08690: ldur            x3, [fp, #-0x28]
    // 0xc08694: stur            x4, [fp, #-0x18]
    // 0xc08698: LoadField: r1 = r3->field_13
    //     0xc08698: ldur            w1, [x3, #0x13]
    // 0xc0869c: DecompressPointer r1
    //     0xc0869c: add             x1, x1, HEAP, lsl #32
    // 0xc086a0: r0 = of()
    //     0xc086a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc086a4: LoadField: r1 = r0->field_87
    //     0xc086a4: ldur            w1, [x0, #0x87]
    // 0xc086a8: DecompressPointer r1
    //     0xc086a8: add             x1, x1, HEAP, lsl #32
    // 0xc086ac: LoadField: r0 = r1->field_7
    //     0xc086ac: ldur            w0, [x1, #7]
    // 0xc086b0: DecompressPointer r0
    //     0xc086b0: add             x0, x0, HEAP, lsl #32
    // 0xc086b4: r16 = Instance_Color
    //     0xc086b4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc086b8: r30 = 12.000000
    //     0xc086b8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc086bc: ldr             lr, [lr, #0x9e8]
    // 0xc086c0: stp             lr, x16, [SP]
    // 0xc086c4: mov             x1, x0
    // 0xc086c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc086c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc086cc: ldr             x4, [x4, #0x9b8]
    // 0xc086d0: r0 = copyWith()
    //     0xc086d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc086d4: stur            x0, [fp, #-0x48]
    // 0xc086d8: r0 = TextSpan()
    //     0xc086d8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc086dc: mov             x1, x0
    // 0xc086e0: ldur            x0, [fp, #-0x18]
    // 0xc086e4: stur            x1, [fp, #-0x50]
    // 0xc086e8: StoreField: r1->field_b = r0
    //     0xc086e8: stur            w0, [x1, #0xb]
    // 0xc086ec: r0 = Instance__DeferringMouseCursor
    //     0xc086ec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc086f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc086f0: stur            w0, [x1, #0x17]
    // 0xc086f4: ldur            x2, [fp, #-0x48]
    // 0xc086f8: StoreField: r1->field_7 = r2
    //     0xc086f8: stur            w2, [x1, #7]
    // 0xc086fc: ldur            x2, [fp, #-8]
    // 0xc08700: LoadField: r3 = r2->field_b
    //     0xc08700: ldur            w3, [x2, #0xb]
    // 0xc08704: DecompressPointer r3
    //     0xc08704: add             x3, x3, HEAP, lsl #32
    // 0xc08708: cmp             w3, NULL
    // 0xc0870c: b.eq            #0xc08c1c
    // 0xc08710: LoadField: r2 = r3->field_27
    //     0xc08710: ldur            w2, [x3, #0x27]
    // 0xc08714: DecompressPointer r2
    //     0xc08714: add             x2, x2, HEAP, lsl #32
    // 0xc08718: cmp             w2, NULL
    // 0xc0871c: b.ne            #0xc08728
    // 0xc08720: r3 = Null
    //     0xc08720: mov             x3, NULL
    // 0xc08724: b               #0xc08730
    // 0xc08728: LoadField: r3 = r2->field_b
    //     0xc08728: ldur            w3, [x2, #0xb]
    // 0xc0872c: DecompressPointer r3
    //     0xc0872c: add             x3, x3, HEAP, lsl #32
    // 0xc08730: cmp             w3, NULL
    // 0xc08734: b.eq            #0xc08778
    // 0xc08738: r4 = LoadInt32Instr(r3)
    //     0xc08738: sbfx            x4, x3, #1, #0x1f
    //     0xc0873c: tbz             w3, #0, #0xc08744
    //     0xc08740: ldur            x4, [x3, #7]
    // 0xc08744: cmp             x4, #0
    // 0xc08748: b.le            #0xc08778
    // 0xc0874c: cmp             w2, NULL
    // 0xc08750: b.ne            #0xc0875c
    // 0xc08754: r2 = Null
    //     0xc08754: mov             x2, NULL
    // 0xc08758: b               #0xc08768
    // 0xc0875c: LoadField: r3 = r2->field_13
    //     0xc0875c: ldur            w3, [x2, #0x13]
    // 0xc08760: DecompressPointer r3
    //     0xc08760: add             x3, x3, HEAP, lsl #32
    // 0xc08764: mov             x2, x3
    // 0xc08768: str             x2, [SP]
    // 0xc0876c: r0 = _interpolateSingle()
    //     0xc0876c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xc08770: mov             x6, x0
    // 0xc08774: b               #0xc0877c
    // 0xc08778: r6 = ""
    //     0xc08778: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0877c: ldur            x2, [fp, #-0x28]
    // 0xc08780: ldur            x5, [fp, #-0x30]
    // 0xc08784: ldur            x3, [fp, #-0x20]
    // 0xc08788: ldur            x4, [fp, #-0x10]
    // 0xc0878c: ldur            x0, [fp, #-0x50]
    // 0xc08790: stur            x6, [fp, #-8]
    // 0xc08794: LoadField: r1 = r2->field_13
    //     0xc08794: ldur            w1, [x2, #0x13]
    // 0xc08798: DecompressPointer r1
    //     0xc08798: add             x1, x1, HEAP, lsl #32
    // 0xc0879c: r0 = of()
    //     0xc0879c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc087a0: LoadField: r1 = r0->field_87
    //     0xc087a0: ldur            w1, [x0, #0x87]
    // 0xc087a4: DecompressPointer r1
    //     0xc087a4: add             x1, x1, HEAP, lsl #32
    // 0xc087a8: LoadField: r0 = r1->field_7
    //     0xc087a8: ldur            w0, [x1, #7]
    // 0xc087ac: DecompressPointer r0
    //     0xc087ac: add             x0, x0, HEAP, lsl #32
    // 0xc087b0: r16 = Instance_Color
    //     0xc087b0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc087b4: ldr             x16, [x16, #0x858]
    // 0xc087b8: r30 = 12.000000
    //     0xc087b8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc087bc: ldr             lr, [lr, #0x9e8]
    // 0xc087c0: stp             lr, x16, [SP]
    // 0xc087c4: mov             x1, x0
    // 0xc087c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc087c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc087cc: ldr             x4, [x4, #0x9b8]
    // 0xc087d0: r0 = copyWith()
    //     0xc087d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc087d4: stur            x0, [fp, #-0x18]
    // 0xc087d8: r0 = TextSpan()
    //     0xc087d8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc087dc: mov             x3, x0
    // 0xc087e0: ldur            x0, [fp, #-8]
    // 0xc087e4: stur            x3, [fp, #-0x48]
    // 0xc087e8: StoreField: r3->field_b = r0
    //     0xc087e8: stur            w0, [x3, #0xb]
    // 0xc087ec: r0 = Instance__DeferringMouseCursor
    //     0xc087ec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc087f0: ArrayStore: r3[0] = r0  ; List_4
    //     0xc087f0: stur            w0, [x3, #0x17]
    // 0xc087f4: ldur            x1, [fp, #-0x18]
    // 0xc087f8: StoreField: r3->field_7 = r1
    //     0xc087f8: stur            w1, [x3, #7]
    // 0xc087fc: r1 = Null
    //     0xc087fc: mov             x1, NULL
    // 0xc08800: r2 = 4
    //     0xc08800: movz            x2, #0x4
    // 0xc08804: r0 = AllocateArray()
    //     0xc08804: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc08808: mov             x2, x0
    // 0xc0880c: ldur            x0, [fp, #-0x50]
    // 0xc08810: stur            x2, [fp, #-8]
    // 0xc08814: StoreField: r2->field_f = r0
    //     0xc08814: stur            w0, [x2, #0xf]
    // 0xc08818: ldur            x0, [fp, #-0x48]
    // 0xc0881c: StoreField: r2->field_13 = r0
    //     0xc0881c: stur            w0, [x2, #0x13]
    // 0xc08820: r1 = <InlineSpan>
    //     0xc08820: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xc08824: ldr             x1, [x1, #0xe40]
    // 0xc08828: r0 = AllocateGrowableArray()
    //     0xc08828: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0882c: mov             x1, x0
    // 0xc08830: ldur            x0, [fp, #-8]
    // 0xc08834: stur            x1, [fp, #-0x18]
    // 0xc08838: StoreField: r1->field_f = r0
    //     0xc08838: stur            w0, [x1, #0xf]
    // 0xc0883c: r2 = 4
    //     0xc0883c: movz            x2, #0x4
    // 0xc08840: StoreField: r1->field_b = r2
    //     0xc08840: stur            w2, [x1, #0xb]
    // 0xc08844: r0 = TextSpan()
    //     0xc08844: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xc08848: mov             x1, x0
    // 0xc0884c: ldur            x0, [fp, #-0x18]
    // 0xc08850: stur            x1, [fp, #-8]
    // 0xc08854: StoreField: r1->field_f = r0
    //     0xc08854: stur            w0, [x1, #0xf]
    // 0xc08858: r0 = Instance__DeferringMouseCursor
    //     0xc08858: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xc0885c: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0885c: stur            w0, [x1, #0x17]
    // 0xc08860: r0 = RichText()
    //     0xc08860: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xc08864: mov             x1, x0
    // 0xc08868: ldur            x2, [fp, #-8]
    // 0xc0886c: stur            x0, [fp, #-8]
    // 0xc08870: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc08870: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc08874: r0 = RichText()
    //     0xc08874: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xc08878: r0 = Padding()
    //     0xc08878: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0887c: mov             x1, x0
    // 0xc08880: r0 = Instance_EdgeInsets
    //     0xc08880: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xc08884: ldr             x0, [x0, #0xe60]
    // 0xc08888: stur            x1, [fp, #-0x18]
    // 0xc0888c: StoreField: r1->field_f = r0
    //     0xc0888c: stur            w0, [x1, #0xf]
    // 0xc08890: ldur            x0, [fp, #-8]
    // 0xc08894: StoreField: r1->field_b = r0
    //     0xc08894: stur            w0, [x1, #0xb]
    // 0xc08898: r0 = SvgPicture()
    //     0xc08898: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc0889c: mov             x1, x0
    // 0xc088a0: r2 = "assets/images/small_right_green.svg"
    //     0xc088a0: add             x2, PP, #0x52, lsl #12  ; [pp+0x52618] "assets/images/small_right_green.svg"
    //     0xc088a4: ldr             x2, [x2, #0x618]
    // 0xc088a8: stur            x0, [fp, #-8]
    // 0xc088ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc088ac: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc088b0: r0 = SvgPicture.asset()
    //     0xc088b0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc088b4: r0 = Padding()
    //     0xc088b4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc088b8: mov             x3, x0
    // 0xc088bc: r0 = Instance_EdgeInsets
    //     0xc088bc: add             x0, PP, #0x34, lsl #12  ; [pp+0x340e0] Obj!EdgeInsets@d57051
    //     0xc088c0: ldr             x0, [x0, #0xe0]
    // 0xc088c4: stur            x3, [fp, #-0x48]
    // 0xc088c8: StoreField: r3->field_f = r0
    //     0xc088c8: stur            w0, [x3, #0xf]
    // 0xc088cc: ldur            x0, [fp, #-8]
    // 0xc088d0: StoreField: r3->field_b = r0
    //     0xc088d0: stur            w0, [x3, #0xb]
    // 0xc088d4: r1 = Null
    //     0xc088d4: mov             x1, NULL
    // 0xc088d8: r2 = 6
    //     0xc088d8: movz            x2, #0x6
    // 0xc088dc: r0 = AllocateArray()
    //     0xc088dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc088e0: mov             x2, x0
    // 0xc088e4: ldur            x0, [fp, #-0x18]
    // 0xc088e8: stur            x2, [fp, #-8]
    // 0xc088ec: StoreField: r2->field_f = r0
    //     0xc088ec: stur            w0, [x2, #0xf]
    // 0xc088f0: r16 = Instance_Spacer
    //     0xc088f0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xc088f4: ldr             x16, [x16, #0xf0]
    // 0xc088f8: StoreField: r2->field_13 = r16
    //     0xc088f8: stur            w16, [x2, #0x13]
    // 0xc088fc: ldur            x0, [fp, #-0x48]
    // 0xc08900: ArrayStore: r2[0] = r0  ; List_4
    //     0xc08900: stur            w0, [x2, #0x17]
    // 0xc08904: r1 = <Widget>
    //     0xc08904: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc08908: r0 = AllocateGrowableArray()
    //     0xc08908: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0890c: mov             x1, x0
    // 0xc08910: ldur            x0, [fp, #-8]
    // 0xc08914: stur            x1, [fp, #-0x18]
    // 0xc08918: StoreField: r1->field_f = r0
    //     0xc08918: stur            w0, [x1, #0xf]
    // 0xc0891c: r0 = 6
    //     0xc0891c: movz            x0, #0x6
    // 0xc08920: StoreField: r1->field_b = r0
    //     0xc08920: stur            w0, [x1, #0xb]
    // 0xc08924: r0 = Row()
    //     0xc08924: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc08928: mov             x1, x0
    // 0xc0892c: r0 = Instance_Axis
    //     0xc0892c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc08930: stur            x1, [fp, #-8]
    // 0xc08934: StoreField: r1->field_f = r0
    //     0xc08934: stur            w0, [x1, #0xf]
    // 0xc08938: r2 = Instance_MainAxisAlignment
    //     0xc08938: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc0893c: ldr             x2, [x2, #0xab0]
    // 0xc08940: StoreField: r1->field_13 = r2
    //     0xc08940: stur            w2, [x1, #0x13]
    // 0xc08944: r2 = Instance_MainAxisSize
    //     0xc08944: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc08948: ldr             x2, [x2, #0xa10]
    // 0xc0894c: ArrayStore: r1[0] = r2  ; List_4
    //     0xc0894c: stur            w2, [x1, #0x17]
    // 0xc08950: r3 = Instance_CrossAxisAlignment
    //     0xc08950: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc08954: ldr             x3, [x3, #0xa18]
    // 0xc08958: StoreField: r1->field_1b = r3
    //     0xc08958: stur            w3, [x1, #0x1b]
    // 0xc0895c: r4 = Instance_VerticalDirection
    //     0xc0895c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc08960: ldr             x4, [x4, #0xa20]
    // 0xc08964: StoreField: r1->field_23 = r4
    //     0xc08964: stur            w4, [x1, #0x23]
    // 0xc08968: r5 = Instance_Clip
    //     0xc08968: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0896c: ldr             x5, [x5, #0x38]
    // 0xc08970: StoreField: r1->field_2b = r5
    //     0xc08970: stur            w5, [x1, #0x2b]
    // 0xc08974: StoreField: r1->field_2f = rZR
    //     0xc08974: stur            xzr, [x1, #0x2f]
    // 0xc08978: ldur            x6, [fp, #-0x18]
    // 0xc0897c: StoreField: r1->field_b = r6
    //     0xc0897c: stur            w6, [x1, #0xb]
    // 0xc08980: r0 = Center()
    //     0xc08980: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc08984: mov             x1, x0
    // 0xc08988: r0 = Instance_Alignment
    //     0xc08988: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0898c: ldr             x0, [x0, #0xb10]
    // 0xc08990: stur            x1, [fp, #-0x18]
    // 0xc08994: StoreField: r1->field_f = r0
    //     0xc08994: stur            w0, [x1, #0xf]
    // 0xc08998: ldur            x0, [fp, #-8]
    // 0xc0899c: StoreField: r1->field_b = r0
    //     0xc0899c: stur            w0, [x1, #0xb]
    // 0xc089a0: r0 = DottedBorder()
    //     0xc089a0: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0xc089a4: mov             x1, x0
    // 0xc089a8: ldur            x2, [fp, #-0x18]
    // 0xc089ac: ldur            x3, [fp, #-0x40]
    // 0xc089b0: r5 = const [2.0, 2.0]
    //     0xc089b0: add             x5, PP, #0x52, lsl #12  ; [pp+0x525f8] List<double>(2)
    //     0xc089b4: ldr             x5, [x5, #0x5f8]
    // 0xc089b8: d0 = 0.500000
    //     0xc089b8: fmov            d0, #0.50000000
    // 0xc089bc: stur            x0, [fp, #-8]
    // 0xc089c0: r4 = const [0, 0x5, 0, 0x5, null]
    //     0xc089c0: ldr             x4, [PP, #0x1330]  ; [pp+0x1330] List(5) [0, 0x5, 0, 0x5, Null]
    // 0xc089c4: r0 = DottedBorder()
    //     0xc089c4: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0xc089c8: r0 = Container()
    //     0xc089c8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc089cc: stur            x0, [fp, #-0x18]
    // 0xc089d0: r16 = 28.000000
    //     0xc089d0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52600] 28
    //     0xc089d4: ldr             x16, [x16, #0x600]
    // 0xc089d8: ldur            lr, [fp, #-0x38]
    // 0xc089dc: stp             lr, x16, [SP, #8]
    // 0xc089e0: ldur            x16, [fp, #-8]
    // 0xc089e4: str             x16, [SP]
    // 0xc089e8: mov             x1, x0
    // 0xc089ec: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xc089ec: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xc089f0: ldr             x4, [x4, #0xc78]
    // 0xc089f4: r0 = Container()
    //     0xc089f4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc089f8: r0 = Padding()
    //     0xc089f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc089fc: mov             x1, x0
    // 0xc08a00: ldur            x0, [fp, #-0x10]
    // 0xc08a04: stur            x1, [fp, #-8]
    // 0xc08a08: StoreField: r1->field_f = r0
    //     0xc08a08: stur            w0, [x1, #0xf]
    // 0xc08a0c: ldur            x0, [fp, #-0x18]
    // 0xc08a10: StoreField: r1->field_b = r0
    //     0xc08a10: stur            w0, [x1, #0xb]
    // 0xc08a14: r0 = IntrinsicWidth()
    //     0xc08a14: bl              #0xa898a4  ; AllocateIntrinsicWidthStub -> IntrinsicWidth (size=0x18)
    // 0xc08a18: mov             x1, x0
    // 0xc08a1c: ldur            x0, [fp, #-8]
    // 0xc08a20: stur            x1, [fp, #-0x10]
    // 0xc08a24: StoreField: r1->field_b = r0
    //     0xc08a24: stur            w0, [x1, #0xb]
    // 0xc08a28: r0 = InkWell()
    //     0xc08a28: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc08a2c: mov             x3, x0
    // 0xc08a30: ldur            x0, [fp, #-0x10]
    // 0xc08a34: stur            x3, [fp, #-8]
    // 0xc08a38: StoreField: r3->field_b = r0
    //     0xc08a38: stur            w0, [x3, #0xb]
    // 0xc08a3c: ldur            x2, [fp, #-0x28]
    // 0xc08a40: r1 = Function '<anonymous closure>':.
    //     0xc08a40: add             x1, PP, #0x52, lsl #12  ; [pp+0x52620] AnonymousClosure: (0xc08c40), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_title_item_view.dart] _ProductTitleItemViewState::build (0xc081f4)
    //     0xc08a44: ldr             x1, [x1, #0x620]
    // 0xc08a48: r0 = AllocateClosure()
    //     0xc08a48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc08a4c: mov             x1, x0
    // 0xc08a50: ldur            x0, [fp, #-8]
    // 0xc08a54: StoreField: r0->field_f = r1
    //     0xc08a54: stur            w1, [x0, #0xf]
    // 0xc08a58: r1 = true
    //     0xc08a58: add             x1, NULL, #0x20  ; true
    // 0xc08a5c: StoreField: r0->field_43 = r1
    //     0xc08a5c: stur            w1, [x0, #0x43]
    // 0xc08a60: r2 = Instance_BoxShape
    //     0xc08a60: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc08a64: ldr             x2, [x2, #0x80]
    // 0xc08a68: StoreField: r0->field_47 = r2
    //     0xc08a68: stur            w2, [x0, #0x47]
    // 0xc08a6c: StoreField: r0->field_6f = r1
    //     0xc08a6c: stur            w1, [x0, #0x6f]
    // 0xc08a70: r2 = false
    //     0xc08a70: add             x2, NULL, #0x30  ; false
    // 0xc08a74: StoreField: r0->field_73 = r2
    //     0xc08a74: stur            w2, [x0, #0x73]
    // 0xc08a78: StoreField: r0->field_83 = r1
    //     0xc08a78: stur            w1, [x0, #0x83]
    // 0xc08a7c: StoreField: r0->field_7b = r2
    //     0xc08a7c: stur            w2, [x0, #0x7b]
    // 0xc08a80: r1 = Null
    //     0xc08a80: mov             x1, NULL
    // 0xc08a84: r2 = 4
    //     0xc08a84: movz            x2, #0x4
    // 0xc08a88: r0 = AllocateArray()
    //     0xc08a88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc08a8c: mov             x2, x0
    // 0xc08a90: ldur            x0, [fp, #-0x20]
    // 0xc08a94: stur            x2, [fp, #-0x10]
    // 0xc08a98: StoreField: r2->field_f = r0
    //     0xc08a98: stur            w0, [x2, #0xf]
    // 0xc08a9c: ldur            x0, [fp, #-8]
    // 0xc08aa0: StoreField: r2->field_13 = r0
    //     0xc08aa0: stur            w0, [x2, #0x13]
    // 0xc08aa4: r1 = <Widget>
    //     0xc08aa4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc08aa8: r0 = AllocateGrowableArray()
    //     0xc08aa8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc08aac: mov             x1, x0
    // 0xc08ab0: ldur            x0, [fp, #-0x10]
    // 0xc08ab4: stur            x1, [fp, #-8]
    // 0xc08ab8: StoreField: r1->field_f = r0
    //     0xc08ab8: stur            w0, [x1, #0xf]
    // 0xc08abc: r2 = 4
    //     0xc08abc: movz            x2, #0x4
    // 0xc08ac0: StoreField: r1->field_b = r2
    //     0xc08ac0: stur            w2, [x1, #0xb]
    // 0xc08ac4: r0 = Row()
    //     0xc08ac4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc08ac8: mov             x1, x0
    // 0xc08acc: r0 = Instance_Axis
    //     0xc08acc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc08ad0: stur            x1, [fp, #-0x10]
    // 0xc08ad4: StoreField: r1->field_f = r0
    //     0xc08ad4: stur            w0, [x1, #0xf]
    // 0xc08ad8: r0 = Instance_MainAxisAlignment
    //     0xc08ad8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc08adc: ldr             x0, [x0, #0xa08]
    // 0xc08ae0: StoreField: r1->field_13 = r0
    //     0xc08ae0: stur            w0, [x1, #0x13]
    // 0xc08ae4: r2 = Instance_MainAxisSize
    //     0xc08ae4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc08ae8: ldr             x2, [x2, #0xa10]
    // 0xc08aec: ArrayStore: r1[0] = r2  ; List_4
    //     0xc08aec: stur            w2, [x1, #0x17]
    // 0xc08af0: r2 = Instance_CrossAxisAlignment
    //     0xc08af0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc08af4: ldr             x2, [x2, #0xa18]
    // 0xc08af8: StoreField: r1->field_1b = r2
    //     0xc08af8: stur            w2, [x1, #0x1b]
    // 0xc08afc: r2 = Instance_VerticalDirection
    //     0xc08afc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc08b00: ldr             x2, [x2, #0xa20]
    // 0xc08b04: StoreField: r1->field_23 = r2
    //     0xc08b04: stur            w2, [x1, #0x23]
    // 0xc08b08: r3 = Instance_Clip
    //     0xc08b08: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc08b0c: ldr             x3, [x3, #0x38]
    // 0xc08b10: StoreField: r1->field_2b = r3
    //     0xc08b10: stur            w3, [x1, #0x2b]
    // 0xc08b14: StoreField: r1->field_2f = rZR
    //     0xc08b14: stur            xzr, [x1, #0x2f]
    // 0xc08b18: ldur            x4, [fp, #-8]
    // 0xc08b1c: StoreField: r1->field_b = r4
    //     0xc08b1c: stur            w4, [x1, #0xb]
    // 0xc08b20: r0 = Padding()
    //     0xc08b20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc08b24: mov             x3, x0
    // 0xc08b28: r0 = Instance_EdgeInsets
    //     0xc08b28: add             x0, PP, #0x52, lsl #12  ; [pp+0x52628] Obj!EdgeInsets@d589d1
    //     0xc08b2c: ldr             x0, [x0, #0x628]
    // 0xc08b30: stur            x3, [fp, #-8]
    // 0xc08b34: StoreField: r3->field_f = r0
    //     0xc08b34: stur            w0, [x3, #0xf]
    // 0xc08b38: ldur            x0, [fp, #-0x10]
    // 0xc08b3c: StoreField: r3->field_b = r0
    //     0xc08b3c: stur            w0, [x3, #0xb]
    // 0xc08b40: r1 = Null
    //     0xc08b40: mov             x1, NULL
    // 0xc08b44: r2 = 4
    //     0xc08b44: movz            x2, #0x4
    // 0xc08b48: r0 = AllocateArray()
    //     0xc08b48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc08b4c: mov             x2, x0
    // 0xc08b50: ldur            x0, [fp, #-0x30]
    // 0xc08b54: stur            x2, [fp, #-0x10]
    // 0xc08b58: StoreField: r2->field_f = r0
    //     0xc08b58: stur            w0, [x2, #0xf]
    // 0xc08b5c: ldur            x0, [fp, #-8]
    // 0xc08b60: StoreField: r2->field_13 = r0
    //     0xc08b60: stur            w0, [x2, #0x13]
    // 0xc08b64: r1 = <Widget>
    //     0xc08b64: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc08b68: r0 = AllocateGrowableArray()
    //     0xc08b68: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc08b6c: mov             x1, x0
    // 0xc08b70: ldur            x0, [fp, #-0x10]
    // 0xc08b74: stur            x1, [fp, #-8]
    // 0xc08b78: StoreField: r1->field_f = r0
    //     0xc08b78: stur            w0, [x1, #0xf]
    // 0xc08b7c: r0 = 4
    //     0xc08b7c: movz            x0, #0x4
    // 0xc08b80: StoreField: r1->field_b = r0
    //     0xc08b80: stur            w0, [x1, #0xb]
    // 0xc08b84: r0 = Column()
    //     0xc08b84: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc08b88: mov             x1, x0
    // 0xc08b8c: r0 = Instance_Axis
    //     0xc08b8c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc08b90: stur            x1, [fp, #-0x10]
    // 0xc08b94: StoreField: r1->field_f = r0
    //     0xc08b94: stur            w0, [x1, #0xf]
    // 0xc08b98: r0 = Instance_MainAxisAlignment
    //     0xc08b98: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc08b9c: ldr             x0, [x0, #0xa08]
    // 0xc08ba0: StoreField: r1->field_13 = r0
    //     0xc08ba0: stur            w0, [x1, #0x13]
    // 0xc08ba4: r0 = Instance_MainAxisSize
    //     0xc08ba4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc08ba8: ldr             x0, [x0, #0xdd0]
    // 0xc08bac: ArrayStore: r1[0] = r0  ; List_4
    //     0xc08bac: stur            w0, [x1, #0x17]
    // 0xc08bb0: r0 = Instance_CrossAxisAlignment
    //     0xc08bb0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc08bb4: ldr             x0, [x0, #0x890]
    // 0xc08bb8: StoreField: r1->field_1b = r0
    //     0xc08bb8: stur            w0, [x1, #0x1b]
    // 0xc08bbc: r0 = Instance_VerticalDirection
    //     0xc08bbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc08bc0: ldr             x0, [x0, #0xa20]
    // 0xc08bc4: StoreField: r1->field_23 = r0
    //     0xc08bc4: stur            w0, [x1, #0x23]
    // 0xc08bc8: r0 = Instance_Clip
    //     0xc08bc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc08bcc: ldr             x0, [x0, #0x38]
    // 0xc08bd0: StoreField: r1->field_2b = r0
    //     0xc08bd0: stur            w0, [x1, #0x2b]
    // 0xc08bd4: StoreField: r1->field_2f = rZR
    //     0xc08bd4: stur            xzr, [x1, #0x2f]
    // 0xc08bd8: ldur            x0, [fp, #-8]
    // 0xc08bdc: StoreField: r1->field_b = r0
    //     0xc08bdc: stur            w0, [x1, #0xb]
    // 0xc08be0: r0 = Padding()
    //     0xc08be0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc08be4: r1 = Instance_EdgeInsets
    //     0xc08be4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40be0] Obj!EdgeInsets@d57da1
    //     0xc08be8: ldr             x1, [x1, #0xbe0]
    // 0xc08bec: StoreField: r0->field_f = r1
    //     0xc08bec: stur            w1, [x0, #0xf]
    // 0xc08bf0: ldur            x1, [fp, #-0x10]
    // 0xc08bf4: StoreField: r0->field_b = r1
    //     0xc08bf4: stur            w1, [x0, #0xb]
    // 0xc08bf8: LeaveFrame
    //     0xc08bf8: mov             SP, fp
    //     0xc08bfc: ldp             fp, lr, [SP], #0x10
    // 0xc08c00: ret
    //     0xc08c00: ret             
    // 0xc08c04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc08c04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08c08: b               #0xc08214
    // 0xc08c0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08c0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08c10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08c10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08c14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08c14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08c18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08c18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08c1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08c1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc08c40, size: 0x90
    // 0xc08c40: EnterFrame
    //     0xc08c40: stp             fp, lr, [SP, #-0x10]!
    //     0xc08c44: mov             fp, SP
    // 0xc08c48: AllocStack(0x18)
    //     0xc08c48: sub             SP, SP, #0x18
    // 0xc08c4c: SetupParameters()
    //     0xc08c4c: ldr             x0, [fp, #0x10]
    //     0xc08c50: ldur            w1, [x0, #0x17]
    //     0xc08c54: add             x1, x1, HEAP, lsl #32
    // 0xc08c58: CheckStackOverflow
    //     0xc08c58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08c5c: cmp             SP, x16
    //     0xc08c60: b.ls            #0xc08cc4
    // 0xc08c64: LoadField: r0 = r1->field_f
    //     0xc08c64: ldur            w0, [x1, #0xf]
    // 0xc08c68: DecompressPointer r0
    //     0xc08c68: add             x0, x0, HEAP, lsl #32
    // 0xc08c6c: LoadField: r2 = r0->field_b
    //     0xc08c6c: ldur            w2, [x0, #0xb]
    // 0xc08c70: DecompressPointer r2
    //     0xc08c70: add             x2, x2, HEAP, lsl #32
    // 0xc08c74: cmp             w2, NULL
    // 0xc08c78: b.eq            #0xc08ccc
    // 0xc08c7c: LoadField: r0 = r1->field_13
    //     0xc08c7c: ldur            w0, [x1, #0x13]
    // 0xc08c80: DecompressPointer r0
    //     0xc08c80: add             x0, x0, HEAP, lsl #32
    // 0xc08c84: LoadField: r1 = r2->field_1f
    //     0xc08c84: ldur            w1, [x2, #0x1f]
    // 0xc08c88: DecompressPointer r1
    //     0xc08c88: add             x1, x1, HEAP, lsl #32
    // 0xc08c8c: r16 = "line"
    //     0xc08c8c: add             x16, PP, #0xd, lsl #12  ; [pp+0xd4a0] "line"
    //     0xc08c90: ldr             x16, [x16, #0x4a0]
    // 0xc08c94: stp             x16, x1, [SP, #8]
    // 0xc08c98: str             x0, [SP]
    // 0xc08c9c: r4 = 0
    //     0xc08c9c: movz            x4, #0
    // 0xc08ca0: ldr             x0, [SP, #0x10]
    // 0xc08ca4: r16 = UnlinkedCall_0x613b5c
    //     0xc08ca4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52630] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc08ca8: add             x16, x16, #0x630
    // 0xc08cac: ldp             x5, lr, [x16]
    // 0xc08cb0: blr             lr
    // 0xc08cb4: r0 = Null
    //     0xc08cb4: mov             x0, NULL
    // 0xc08cb8: LeaveFrame
    //     0xc08cb8: mov             SP, fp
    //     0xc08cbc: ldp             fp, lr, [SP], #0x10
    // 0xc08cc0: ret
    //     0xc08cc0: ret             
    // 0xc08cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc08cc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08cc8: b               #0xc08c64
    // 0xc08ccc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08ccc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, WidgetRatings?) {
    // ** addr: 0xc08cd0, size: 0x88
    // 0xc08cd0: EnterFrame
    //     0xc08cd0: stp             fp, lr, [SP, #-0x10]!
    //     0xc08cd4: mov             fp, SP
    // 0xc08cd8: AllocStack(0x18)
    //     0xc08cd8: sub             SP, SP, #0x18
    // 0xc08cdc: SetupParameters()
    //     0xc08cdc: ldr             x0, [fp, #0x20]
    //     0xc08ce0: ldur            w1, [x0, #0x17]
    //     0xc08ce4: add             x1, x1, HEAP, lsl #32
    // 0xc08ce8: CheckStackOverflow
    //     0xc08ce8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08cec: cmp             SP, x16
    //     0xc08cf0: b.ls            #0xc08d4c
    // 0xc08cf4: LoadField: r0 = r1->field_f
    //     0xc08cf4: ldur            w0, [x1, #0xf]
    // 0xc08cf8: DecompressPointer r0
    //     0xc08cf8: add             x0, x0, HEAP, lsl #32
    // 0xc08cfc: LoadField: r1 = r0->field_b
    //     0xc08cfc: ldur            w1, [x0, #0xb]
    // 0xc08d00: DecompressPointer r1
    //     0xc08d00: add             x1, x1, HEAP, lsl #32
    // 0xc08d04: cmp             w1, NULL
    // 0xc08d08: b.eq            #0xc08d54
    // 0xc08d0c: LoadField: r0 = r1->field_1b
    //     0xc08d0c: ldur            w0, [x1, #0x1b]
    // 0xc08d10: DecompressPointer r0
    //     0xc08d10: add             x0, x0, HEAP, lsl #32
    // 0xc08d14: ldr             x16, [fp, #0x18]
    // 0xc08d18: stp             x16, x0, [SP, #8]
    // 0xc08d1c: ldr             x16, [fp, #0x10]
    // 0xc08d20: str             x16, [SP]
    // 0xc08d24: r4 = 0
    //     0xc08d24: movz            x4, #0
    // 0xc08d28: ldr             x0, [SP, #0x10]
    // 0xc08d2c: r16 = UnlinkedCall_0x613b5c
    //     0xc08d2c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52640] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc08d30: add             x16, x16, #0x640
    // 0xc08d34: ldp             x5, lr, [x16]
    // 0xc08d38: blr             lr
    // 0xc08d3c: r0 = Null
    //     0xc08d3c: mov             x0, NULL
    // 0xc08d40: LeaveFrame
    //     0xc08d40: mov             SP, fp
    //     0xc08d44: ldp             fp, lr, [SP], #0x10
    // 0xc08d48: ret
    //     0xc08d48: ret             
    // 0xc08d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc08d4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08d50: b               #0xc08cf4
    // 0xc08d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08d54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3968, size: 0x2c, field offset: 0xc
//   const constructor, 
class ProductTitleItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81124, size: 0x24
    // 0xc81124: EnterFrame
    //     0xc81124: stp             fp, lr, [SP, #-0x10]!
    //     0xc81128: mov             fp, SP
    // 0xc8112c: mov             x0, x1
    // 0xc81130: r1 = <ProductTitleItemView>
    //     0xc81130: add             x1, PP, #0x48, lsl #12  ; [pp+0x48288] TypeArguments: <ProductTitleItemView>
    //     0xc81134: ldr             x1, [x1, #0x288]
    // 0xc81138: r0 = _ProductTitleItemViewState()
    //     0xc81138: bl              #0xc81148  ; Allocate_ProductTitleItemViewStateStub -> _ProductTitleItemViewState (size=0x14)
    // 0xc8113c: LeaveFrame
    //     0xc8113c: mov             SP, fp
    //     0xc81140: ldp             fp, lr, [SP], #0x10
    // 0xc81144: ret
    //     0xc81144: ret             
  }
}
