// lib: , url: package:customer_app/app/presentation/views/cosmetic/orders/order_detail_accordion.dart

// class id: 1049292, size: 0x8
class :: {
}

// class id: 3414, size: 0x18, field offset: 0x14
class _OrderDetailAccordionState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xafd264, size: 0x430
    // 0xafd264: EnterFrame
    //     0xafd264: stp             fp, lr, [SP, #-0x10]!
    //     0xafd268: mov             fp, SP
    // 0xafd26c: AllocStack(0x50)
    //     0xafd26c: sub             SP, SP, #0x50
    // 0xafd270: SetupParameters(_OrderDetailAccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xafd270: mov             x0, x1
    //     0xafd274: stur            x1, [fp, #-8]
    //     0xafd278: mov             x1, x2
    //     0xafd27c: stur            x2, [fp, #-0x10]
    // 0xafd280: CheckStackOverflow
    //     0xafd280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafd284: cmp             SP, x16
    //     0xafd288: b.ls            #0xafd684
    // 0xafd28c: r1 = 1
    //     0xafd28c: movz            x1, #0x1
    // 0xafd290: r0 = AllocateContext()
    //     0xafd290: bl              #0x16f6108  ; AllocateContextStub
    // 0xafd294: mov             x1, x0
    // 0xafd298: ldur            x0, [fp, #-8]
    // 0xafd29c: stur            x1, [fp, #-0x18]
    // 0xafd2a0: StoreField: r1->field_f = r0
    //     0xafd2a0: stur            w0, [x1, #0xf]
    // 0xafd2a4: r0 = Radius()
    //     0xafd2a4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xafd2a8: d0 = 10.000000
    //     0xafd2a8: fmov            d0, #10.00000000
    // 0xafd2ac: stur            x0, [fp, #-0x20]
    // 0xafd2b0: StoreField: r0->field_7 = d0
    //     0xafd2b0: stur            d0, [x0, #7]
    // 0xafd2b4: StoreField: r0->field_f = d0
    //     0xafd2b4: stur            d0, [x0, #0xf]
    // 0xafd2b8: r0 = BorderRadius()
    //     0xafd2b8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xafd2bc: mov             x1, x0
    // 0xafd2c0: ldur            x0, [fp, #-0x20]
    // 0xafd2c4: stur            x1, [fp, #-0x28]
    // 0xafd2c8: StoreField: r1->field_7 = r0
    //     0xafd2c8: stur            w0, [x1, #7]
    // 0xafd2cc: StoreField: r1->field_b = r0
    //     0xafd2cc: stur            w0, [x1, #0xb]
    // 0xafd2d0: StoreField: r1->field_f = r0
    //     0xafd2d0: stur            w0, [x1, #0xf]
    // 0xafd2d4: StoreField: r1->field_13 = r0
    //     0xafd2d4: stur            w0, [x1, #0x13]
    // 0xafd2d8: r0 = RoundedRectangleBorder()
    //     0xafd2d8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xafd2dc: mov             x2, x0
    // 0xafd2e0: ldur            x0, [fp, #-0x28]
    // 0xafd2e4: stur            x2, [fp, #-0x30]
    // 0xafd2e8: StoreField: r2->field_b = r0
    //     0xafd2e8: stur            w0, [x2, #0xb]
    // 0xafd2ec: r0 = Instance_BorderSide
    //     0xafd2ec: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xafd2f0: ldr             x0, [x0, #0xe20]
    // 0xafd2f4: StoreField: r2->field_7 = r0
    //     0xafd2f4: stur            w0, [x2, #7]
    // 0xafd2f8: ldur            x0, [fp, #-8]
    // 0xafd2fc: LoadField: r1 = r0->field_b
    //     0xafd2fc: ldur            w1, [x0, #0xb]
    // 0xafd300: DecompressPointer r1
    //     0xafd300: add             x1, x1, HEAP, lsl #32
    // 0xafd304: cmp             w1, NULL
    // 0xafd308: b.eq            #0xafd68c
    // 0xafd30c: LoadField: r3 = r1->field_b
    //     0xafd30c: ldur            w3, [x1, #0xb]
    // 0xafd310: DecompressPointer r3
    //     0xafd310: add             x3, x3, HEAP, lsl #32
    // 0xafd314: stur            x3, [fp, #-0x28]
    // 0xafd318: LoadField: r1 = r0->field_13
    //     0xafd318: ldur            w1, [x0, #0x13]
    // 0xafd31c: DecompressPointer r1
    //     0xafd31c: add             x1, x1, HEAP, lsl #32
    // 0xafd320: tbnz            w1, #4, #0xafd330
    // 0xafd324: r4 = Instance_IconData
    //     0xafd324: add             x4, PP, #0x53, lsl #12  ; [pp+0x53440] Obj!IconData@d55621
    //     0xafd328: ldr             x4, [x4, #0x440]
    // 0xafd32c: b               #0xafd338
    // 0xafd330: r4 = Instance_IconData
    //     0xafd330: add             x4, PP, #0x53, lsl #12  ; [pp+0x53448] Obj!IconData@d553a1
    //     0xafd334: ldr             x4, [x4, #0x448]
    // 0xafd338: stur            x4, [fp, #-0x20]
    // 0xafd33c: tbnz            w1, #4, #0xafd358
    // 0xafd340: ldur            x1, [fp, #-0x10]
    // 0xafd344: r0 = of()
    //     0xafd344: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafd348: LoadField: r1 = r0->field_5b
    //     0xafd348: ldur            w1, [x0, #0x5b]
    // 0xafd34c: DecompressPointer r1
    //     0xafd34c: add             x1, x1, HEAP, lsl #32
    // 0xafd350: mov             x3, x1
    // 0xafd354: b               #0xafd36c
    // 0xafd358: ldur            x1, [fp, #-0x10]
    // 0xafd35c: r0 = of()
    //     0xafd35c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xafd360: LoadField: r1 = r0->field_5b
    //     0xafd360: ldur            w1, [x0, #0x5b]
    // 0xafd364: DecompressPointer r1
    //     0xafd364: add             x1, x1, HEAP, lsl #32
    // 0xafd368: mov             x3, x1
    // 0xafd36c: ldur            x0, [fp, #-8]
    // 0xafd370: ldur            x1, [fp, #-0x28]
    // 0xafd374: ldur            x2, [fp, #-0x20]
    // 0xafd378: stur            x3, [fp, #-0x10]
    // 0xafd37c: r0 = Icon()
    //     0xafd37c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xafd380: mov             x1, x0
    // 0xafd384: ldur            x0, [fp, #-0x20]
    // 0xafd388: stur            x1, [fp, #-0x38]
    // 0xafd38c: StoreField: r1->field_b = r0
    //     0xafd38c: stur            w0, [x1, #0xb]
    // 0xafd390: r0 = 32.000000
    //     0xafd390: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xafd394: ldr             x0, [x0, #0x848]
    // 0xafd398: StoreField: r1->field_f = r0
    //     0xafd398: stur            w0, [x1, #0xf]
    // 0xafd39c: ldur            x0, [fp, #-0x10]
    // 0xafd3a0: StoreField: r1->field_23 = r0
    //     0xafd3a0: stur            w0, [x1, #0x23]
    // 0xafd3a4: ldur            x0, [fp, #-8]
    // 0xafd3a8: LoadField: r2 = r0->field_b
    //     0xafd3a8: ldur            w2, [x0, #0xb]
    // 0xafd3ac: DecompressPointer r2
    //     0xafd3ac: add             x2, x2, HEAP, lsl #32
    // 0xafd3b0: stur            x2, [fp, #-0x10]
    // 0xafd3b4: cmp             w2, NULL
    // 0xafd3b8: b.eq            #0xafd690
    // 0xafd3bc: r0 = RichTextIcon()
    //     0xafd3bc: bl              #0xafd6b8  ; AllocateRichTextIconStub -> RichTextIcon (size=0x14)
    // 0xafd3c0: mov             x1, x0
    // 0xafd3c4: ldur            x0, [fp, #-0x38]
    // 0xafd3c8: stur            x1, [fp, #-8]
    // 0xafd3cc: StoreField: r1->field_b = r0
    //     0xafd3cc: stur            w0, [x1, #0xb]
    // 0xafd3d0: r0 = ""
    //     0xafd3d0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xafd3d4: StoreField: r1->field_f = r0
    //     0xafd3d4: stur            w0, [x1, #0xf]
    // 0xafd3d8: r0 = ListTile()
    //     0xafd3d8: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xafd3dc: mov             x3, x0
    // 0xafd3e0: ldur            x0, [fp, #-0x28]
    // 0xafd3e4: stur            x3, [fp, #-0x20]
    // 0xafd3e8: StoreField: r3->field_f = r0
    //     0xafd3e8: stur            w0, [x3, #0xf]
    // 0xafd3ec: ldur            x0, [fp, #-8]
    // 0xafd3f0: ArrayStore: r3[0] = r0  ; List_4
    //     0xafd3f0: stur            w0, [x3, #0x17]
    // 0xafd3f4: r0 = true
    //     0xafd3f4: add             x0, NULL, #0x20  ; true
    // 0xafd3f8: StoreField: r3->field_4b = r0
    //     0xafd3f8: stur            w0, [x3, #0x4b]
    // 0xafd3fc: ldur            x2, [fp, #-0x18]
    // 0xafd400: r1 = Function '<anonymous closure>':.
    //     0xafd400: add             x1, PP, #0x57, lsl #12  ; [pp+0x57fe8] AnonymousClosure: (0xafd6c4), in [package:customer_app/app/presentation/views/cosmetic/orders/order_detail_accordion.dart] _OrderDetailAccordionState::build (0xafd264)
    //     0xafd404: ldr             x1, [x1, #0xfe8]
    // 0xafd408: r0 = AllocateClosure()
    //     0xafd408: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafd40c: mov             x1, x0
    // 0xafd410: ldur            x0, [fp, #-0x20]
    // 0xafd414: StoreField: r0->field_4f = r1
    //     0xafd414: stur            w1, [x0, #0x4f]
    // 0xafd418: r1 = false
    //     0xafd418: add             x1, NULL, #0x30  ; false
    // 0xafd41c: StoreField: r0->field_5f = r1
    //     0xafd41c: stur            w1, [x0, #0x5f]
    // 0xafd420: StoreField: r0->field_73 = r1
    //     0xafd420: stur            w1, [x0, #0x73]
    // 0xafd424: r3 = true
    //     0xafd424: add             x3, NULL, #0x20  ; true
    // 0xafd428: StoreField: r0->field_97 = r3
    //     0xafd428: stur            w3, [x0, #0x97]
    // 0xafd42c: r1 = Null
    //     0xafd42c: mov             x1, NULL
    // 0xafd430: r2 = 2
    //     0xafd430: movz            x2, #0x2
    // 0xafd434: r0 = AllocateArray()
    //     0xafd434: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafd438: mov             x2, x0
    // 0xafd43c: ldur            x0, [fp, #-0x20]
    // 0xafd440: stur            x2, [fp, #-8]
    // 0xafd444: StoreField: r2->field_f = r0
    //     0xafd444: stur            w0, [x2, #0xf]
    // 0xafd448: r1 = <Widget>
    //     0xafd448: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafd44c: r0 = AllocateGrowableArray()
    //     0xafd44c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafd450: mov             x1, x0
    // 0xafd454: ldur            x0, [fp, #-8]
    // 0xafd458: stur            x1, [fp, #-0x18]
    // 0xafd45c: StoreField: r1->field_f = r0
    //     0xafd45c: stur            w0, [x1, #0xf]
    // 0xafd460: r0 = 2
    //     0xafd460: movz            x0, #0x2
    // 0xafd464: StoreField: r1->field_b = r0
    //     0xafd464: stur            w0, [x1, #0xb]
    // 0xafd468: ldur            x0, [fp, #-0x10]
    // 0xafd46c: LoadField: r2 = r0->field_1f
    //     0xafd46c: ldur            w2, [x0, #0x1f]
    // 0xafd470: DecompressPointer r2
    //     0xafd470: add             x2, x2, HEAP, lsl #32
    // 0xafd474: tbnz            w2, #4, #0xafd528
    // 0xafd478: LoadField: r2 = r0->field_f
    //     0xafd478: ldur            w2, [x0, #0xf]
    // 0xafd47c: DecompressPointer r2
    //     0xafd47c: add             x2, x2, HEAP, lsl #32
    // 0xafd480: stur            x2, [fp, #-8]
    // 0xafd484: r0 = Container()
    //     0xafd484: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafd488: stur            x0, [fp, #-0x10]
    // 0xafd48c: r16 = Instance_EdgeInsets
    //     0xafd48c: add             x16, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0xafd490: ldr             x16, [x16, #0x670]
    // 0xafd494: ldur            lr, [fp, #-8]
    // 0xafd498: stp             lr, x16, [SP]
    // 0xafd49c: mov             x1, x0
    // 0xafd4a0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xafd4a0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xafd4a4: ldr             x4, [x4, #0x30]
    // 0xafd4a8: r0 = Container()
    //     0xafd4a8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafd4ac: ldur            x0, [fp, #-0x18]
    // 0xafd4b0: LoadField: r1 = r0->field_b
    //     0xafd4b0: ldur            w1, [x0, #0xb]
    // 0xafd4b4: LoadField: r2 = r0->field_f
    //     0xafd4b4: ldur            w2, [x0, #0xf]
    // 0xafd4b8: DecompressPointer r2
    //     0xafd4b8: add             x2, x2, HEAP, lsl #32
    // 0xafd4bc: LoadField: r3 = r2->field_b
    //     0xafd4bc: ldur            w3, [x2, #0xb]
    // 0xafd4c0: r2 = LoadInt32Instr(r1)
    //     0xafd4c0: sbfx            x2, x1, #1, #0x1f
    // 0xafd4c4: stur            x2, [fp, #-0x40]
    // 0xafd4c8: r1 = LoadInt32Instr(r3)
    //     0xafd4c8: sbfx            x1, x3, #1, #0x1f
    // 0xafd4cc: cmp             x2, x1
    // 0xafd4d0: b.ne            #0xafd4dc
    // 0xafd4d4: mov             x1, x0
    // 0xafd4d8: r0 = _growToNextCapacity()
    //     0xafd4d8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafd4dc: ldur            x2, [fp, #-0x18]
    // 0xafd4e0: ldur            x3, [fp, #-0x40]
    // 0xafd4e4: add             x0, x3, #1
    // 0xafd4e8: lsl             x1, x0, #1
    // 0xafd4ec: StoreField: r2->field_b = r1
    //     0xafd4ec: stur            w1, [x2, #0xb]
    // 0xafd4f0: LoadField: r1 = r2->field_f
    //     0xafd4f0: ldur            w1, [x2, #0xf]
    // 0xafd4f4: DecompressPointer r1
    //     0xafd4f4: add             x1, x1, HEAP, lsl #32
    // 0xafd4f8: ldur            x0, [fp, #-0x10]
    // 0xafd4fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafd4fc: add             x25, x1, x3, lsl #2
    //     0xafd500: add             x25, x25, #0xf
    //     0xafd504: str             w0, [x25]
    //     0xafd508: tbz             w0, #0, #0xafd524
    //     0xafd50c: ldurb           w16, [x1, #-1]
    //     0xafd510: ldurb           w17, [x0, #-1]
    //     0xafd514: and             x16, x17, x16, lsr #2
    //     0xafd518: tst             x16, HEAP, lsr #32
    //     0xafd51c: b.eq            #0xafd524
    //     0xafd520: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xafd524: b               #0xafd5b8
    // 0xafd528: mov             x2, x1
    // 0xafd52c: r0 = Container()
    //     0xafd52c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafd530: mov             x1, x0
    // 0xafd534: stur            x0, [fp, #-8]
    // 0xafd538: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xafd538: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xafd53c: r0 = Container()
    //     0xafd53c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafd540: ldur            x0, [fp, #-0x18]
    // 0xafd544: LoadField: r1 = r0->field_b
    //     0xafd544: ldur            w1, [x0, #0xb]
    // 0xafd548: LoadField: r2 = r0->field_f
    //     0xafd548: ldur            w2, [x0, #0xf]
    // 0xafd54c: DecompressPointer r2
    //     0xafd54c: add             x2, x2, HEAP, lsl #32
    // 0xafd550: LoadField: r3 = r2->field_b
    //     0xafd550: ldur            w3, [x2, #0xb]
    // 0xafd554: r2 = LoadInt32Instr(r1)
    //     0xafd554: sbfx            x2, x1, #1, #0x1f
    // 0xafd558: stur            x2, [fp, #-0x40]
    // 0xafd55c: r1 = LoadInt32Instr(r3)
    //     0xafd55c: sbfx            x1, x3, #1, #0x1f
    // 0xafd560: cmp             x2, x1
    // 0xafd564: b.ne            #0xafd570
    // 0xafd568: mov             x1, x0
    // 0xafd56c: r0 = _growToNextCapacity()
    //     0xafd56c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xafd570: ldur            x2, [fp, #-0x18]
    // 0xafd574: ldur            x3, [fp, #-0x40]
    // 0xafd578: add             x0, x3, #1
    // 0xafd57c: lsl             x1, x0, #1
    // 0xafd580: StoreField: r2->field_b = r1
    //     0xafd580: stur            w1, [x2, #0xb]
    // 0xafd584: LoadField: r1 = r2->field_f
    //     0xafd584: ldur            w1, [x2, #0xf]
    // 0xafd588: DecompressPointer r1
    //     0xafd588: add             x1, x1, HEAP, lsl #32
    // 0xafd58c: ldur            x0, [fp, #-8]
    // 0xafd590: ArrayStore: r1[r3] = r0  ; List_4
    //     0xafd590: add             x25, x1, x3, lsl #2
    //     0xafd594: add             x25, x25, #0xf
    //     0xafd598: str             w0, [x25]
    //     0xafd59c: tbz             w0, #0, #0xafd5b8
    //     0xafd5a0: ldurb           w16, [x1, #-1]
    //     0xafd5a4: ldurb           w17, [x0, #-1]
    //     0xafd5a8: and             x16, x17, x16, lsr #2
    //     0xafd5ac: tst             x16, HEAP, lsr #32
    //     0xafd5b0: b.eq            #0xafd5b8
    //     0xafd5b4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xafd5b8: ldur            x0, [fp, #-0x30]
    // 0xafd5bc: r0 = Column()
    //     0xafd5bc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafd5c0: mov             x1, x0
    // 0xafd5c4: r0 = Instance_Axis
    //     0xafd5c4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafd5c8: stur            x1, [fp, #-8]
    // 0xafd5cc: StoreField: r1->field_f = r0
    //     0xafd5cc: stur            w0, [x1, #0xf]
    // 0xafd5d0: r0 = Instance_MainAxisAlignment
    //     0xafd5d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafd5d4: ldr             x0, [x0, #0xa08]
    // 0xafd5d8: StoreField: r1->field_13 = r0
    //     0xafd5d8: stur            w0, [x1, #0x13]
    // 0xafd5dc: r0 = Instance_MainAxisSize
    //     0xafd5dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafd5e0: ldr             x0, [x0, #0xa10]
    // 0xafd5e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xafd5e4: stur            w0, [x1, #0x17]
    // 0xafd5e8: r0 = Instance_CrossAxisAlignment
    //     0xafd5e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xafd5ec: ldr             x0, [x0, #0xa18]
    // 0xafd5f0: StoreField: r1->field_1b = r0
    //     0xafd5f0: stur            w0, [x1, #0x1b]
    // 0xafd5f4: r0 = Instance_VerticalDirection
    //     0xafd5f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafd5f8: ldr             x0, [x0, #0xa20]
    // 0xafd5fc: StoreField: r1->field_23 = r0
    //     0xafd5fc: stur            w0, [x1, #0x23]
    // 0xafd600: r0 = Instance_Clip
    //     0xafd600: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafd604: ldr             x0, [x0, #0x38]
    // 0xafd608: StoreField: r1->field_2b = r0
    //     0xafd608: stur            w0, [x1, #0x2b]
    // 0xafd60c: StoreField: r1->field_2f = rZR
    //     0xafd60c: stur            xzr, [x1, #0x2f]
    // 0xafd610: ldur            x0, [fp, #-0x18]
    // 0xafd614: StoreField: r1->field_b = r0
    //     0xafd614: stur            w0, [x1, #0xb]
    // 0xafd618: r0 = Card()
    //     0xafd618: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xafd61c: mov             x1, x0
    // 0xafd620: r0 = 0.000000
    //     0xafd620: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xafd624: stur            x1, [fp, #-0x10]
    // 0xafd628: ArrayStore: r1[0] = r0  ; List_4
    //     0xafd628: stur            w0, [x1, #0x17]
    // 0xafd62c: ldur            x0, [fp, #-0x30]
    // 0xafd630: StoreField: r1->field_1b = r0
    //     0xafd630: stur            w0, [x1, #0x1b]
    // 0xafd634: r0 = true
    //     0xafd634: add             x0, NULL, #0x20  ; true
    // 0xafd638: StoreField: r1->field_1f = r0
    //     0xafd638: stur            w0, [x1, #0x1f]
    // 0xafd63c: r2 = Instance_Clip
    //     0xafd63c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xafd640: ldr             x2, [x2, #0xb50]
    // 0xafd644: StoreField: r1->field_23 = r2
    //     0xafd644: stur            w2, [x1, #0x23]
    // 0xafd648: ldur            x2, [fp, #-8]
    // 0xafd64c: StoreField: r1->field_2f = r2
    //     0xafd64c: stur            w2, [x1, #0x2f]
    // 0xafd650: StoreField: r1->field_2b = r0
    //     0xafd650: stur            w0, [x1, #0x2b]
    // 0xafd654: r0 = Instance__CardVariant
    //     0xafd654: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xafd658: ldr             x0, [x0, #0xa68]
    // 0xafd65c: StoreField: r1->field_33 = r0
    //     0xafd65c: stur            w0, [x1, #0x33]
    // 0xafd660: r0 = Padding()
    //     0xafd660: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafd664: r1 = Instance_EdgeInsets
    //     0xafd664: add             x1, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xafd668: ldr             x1, [x1, #0x668]
    // 0xafd66c: StoreField: r0->field_f = r1
    //     0xafd66c: stur            w1, [x0, #0xf]
    // 0xafd670: ldur            x1, [fp, #-0x10]
    // 0xafd674: StoreField: r0->field_b = r1
    //     0xafd674: stur            w1, [x0, #0xb]
    // 0xafd678: LeaveFrame
    //     0xafd678: mov             SP, fp
    //     0xafd67c: ldp             fp, lr, [SP], #0x10
    // 0xafd680: ret
    //     0xafd680: ret             
    // 0xafd684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafd684: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafd688: b               #0xafd28c
    // 0xafd68c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd68c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafd690: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd690: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xafd6c4, size: 0x60
    // 0xafd6c4: EnterFrame
    //     0xafd6c4: stp             fp, lr, [SP, #-0x10]!
    //     0xafd6c8: mov             fp, SP
    // 0xafd6cc: AllocStack(0x8)
    //     0xafd6cc: sub             SP, SP, #8
    // 0xafd6d0: SetupParameters()
    //     0xafd6d0: ldr             x0, [fp, #0x10]
    //     0xafd6d4: ldur            w2, [x0, #0x17]
    //     0xafd6d8: add             x2, x2, HEAP, lsl #32
    // 0xafd6dc: CheckStackOverflow
    //     0xafd6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafd6e0: cmp             SP, x16
    //     0xafd6e4: b.ls            #0xafd71c
    // 0xafd6e8: LoadField: r0 = r2->field_f
    //     0xafd6e8: ldur            w0, [x2, #0xf]
    // 0xafd6ec: DecompressPointer r0
    //     0xafd6ec: add             x0, x0, HEAP, lsl #32
    // 0xafd6f0: stur            x0, [fp, #-8]
    // 0xafd6f4: r1 = Function '<anonymous closure>':.
    //     0xafd6f4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57ff0] AnonymousClosure: (0xafd724), in [package:customer_app/app/presentation/views/cosmetic/orders/order_detail_accordion.dart] _OrderDetailAccordionState::build (0xafd264)
    //     0xafd6f8: ldr             x1, [x1, #0xff0]
    // 0xafd6fc: r0 = AllocateClosure()
    //     0xafd6fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafd700: ldur            x1, [fp, #-8]
    // 0xafd704: mov             x2, x0
    // 0xafd708: r0 = setState()
    //     0xafd708: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xafd70c: r0 = Null
    //     0xafd70c: mov             x0, NULL
    // 0xafd710: LeaveFrame
    //     0xafd710: mov             SP, fp
    //     0xafd714: ldp             fp, lr, [SP], #0x10
    // 0xafd718: ret
    //     0xafd718: ret             
    // 0xafd71c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafd71c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafd720: b               #0xafd6e8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xafd724, size: 0x94
    // 0xafd724: EnterFrame
    //     0xafd724: stp             fp, lr, [SP, #-0x10]!
    //     0xafd728: mov             fp, SP
    // 0xafd72c: AllocStack(0x10)
    //     0xafd72c: sub             SP, SP, #0x10
    // 0xafd730: SetupParameters()
    //     0xafd730: ldr             x0, [fp, #0x10]
    //     0xafd734: ldur            w1, [x0, #0x17]
    //     0xafd738: add             x1, x1, HEAP, lsl #32
    // 0xafd73c: CheckStackOverflow
    //     0xafd73c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafd740: cmp             SP, x16
    //     0xafd744: b.ls            #0xafd7ac
    // 0xafd748: LoadField: r0 = r1->field_f
    //     0xafd748: ldur            w0, [x1, #0xf]
    // 0xafd74c: DecompressPointer r0
    //     0xafd74c: add             x0, x0, HEAP, lsl #32
    // 0xafd750: LoadField: r1 = r0->field_13
    //     0xafd750: ldur            w1, [x0, #0x13]
    // 0xafd754: DecompressPointer r1
    //     0xafd754: add             x1, x1, HEAP, lsl #32
    // 0xafd758: eor             x2, x1, #0x10
    // 0xafd75c: StoreField: r0->field_13 = r2
    //     0xafd75c: stur            w2, [x0, #0x13]
    // 0xafd760: LoadField: r1 = r0->field_b
    //     0xafd760: ldur            w1, [x0, #0xb]
    // 0xafd764: DecompressPointer r1
    //     0xafd764: add             x1, x1, HEAP, lsl #32
    // 0xafd768: cmp             w1, NULL
    // 0xafd76c: b.eq            #0xafd7b4
    // 0xafd770: LoadField: r0 = r1->field_1f
    //     0xafd770: ldur            w0, [x1, #0x1f]
    // 0xafd774: DecompressPointer r0
    //     0xafd774: add             x0, x0, HEAP, lsl #32
    // 0xafd778: LoadField: r2 = r1->field_33
    //     0xafd778: ldur            w2, [x1, #0x33]
    // 0xafd77c: DecompressPointer r2
    //     0xafd77c: add             x2, x2, HEAP, lsl #32
    // 0xafd780: stp             x0, x2, [SP]
    // 0xafd784: r4 = 0
    //     0xafd784: movz            x4, #0
    // 0xafd788: ldr             x0, [SP, #8]
    // 0xafd78c: r16 = UnlinkedCall_0x613b5c
    //     0xafd78c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57ff8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafd790: add             x16, x16, #0xff8
    // 0xafd794: ldp             x5, lr, [x16]
    // 0xafd798: blr             lr
    // 0xafd79c: r0 = Null
    //     0xafd79c: mov             x0, NULL
    // 0xafd7a0: LeaveFrame
    //     0xafd7a0: mov             SP, fp
    //     0xafd7a4: ldp             fp, lr, [SP], #0x10
    // 0xafd7a8: ret
    //     0xafd7a8: ret             
    // 0xafd7ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafd7ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafd7b0: b               #0xafd748
    // 0xafd7b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafd7b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4151, size: 0x38, field offset: 0xc
//   const constructor, 
class OrderDetailAccordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dd9c, size: 0x2c
    // 0xc7dd9c: EnterFrame
    //     0xc7dd9c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dda0: mov             fp, SP
    // 0xc7dda4: mov             x0, x1
    // 0xc7dda8: r1 = <OrderDetailAccordion>
    //     0xc7dda8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48bb0] TypeArguments: <OrderDetailAccordion>
    //     0xc7ddac: ldr             x1, [x1, #0xbb0]
    // 0xc7ddb0: r0 = _OrderDetailAccordionState()
    //     0xc7ddb0: bl              #0xc7ddc8  ; Allocate_OrderDetailAccordionStateStub -> _OrderDetailAccordionState (size=0x18)
    // 0xc7ddb4: r1 = false
    //     0xc7ddb4: add             x1, NULL, #0x30  ; false
    // 0xc7ddb8: StoreField: r0->field_13 = r1
    //     0xc7ddb8: stur            w1, [x0, #0x13]
    // 0xc7ddbc: LeaveFrame
    //     0xc7ddbc: mov             SP, fp
    //     0xc7ddc0: ldp             fp, lr, [SP], #0x10
    // 0xc7ddc4: ret
    //     0xc7ddc4: ret             
  }
}
