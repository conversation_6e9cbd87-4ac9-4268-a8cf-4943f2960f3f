// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/view_size_widget.dart

// class id: 1049330, size: 0x8
class :: {
}

// class id: 3389, size: 0x14, field offset: 0x14
class _ViewSizeWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb20e84, size: 0x3d0
    // 0xb20e84: EnterFrame
    //     0xb20e84: stp             fp, lr, [SP, #-0x10]!
    //     0xb20e88: mov             fp, SP
    // 0xb20e8c: AllocStack(0x50)
    //     0xb20e8c: sub             SP, SP, #0x50
    // 0xb20e90: SetupParameters(_ViewSizeWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb20e90: mov             x0, x1
    //     0xb20e94: stur            x1, [fp, #-8]
    //     0xb20e98: mov             x1, x2
    //     0xb20e9c: stur            x2, [fp, #-0x10]
    // 0xb20ea0: CheckStackOverflow
    //     0xb20ea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb20ea4: cmp             SP, x16
    //     0xb20ea8: b.ls            #0xb21230
    // 0xb20eac: r1 = 1
    //     0xb20eac: movz            x1, #0x1
    // 0xb20eb0: r0 = AllocateContext()
    //     0xb20eb0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb20eb4: mov             x1, x0
    // 0xb20eb8: ldur            x0, [fp, #-8]
    // 0xb20ebc: stur            x1, [fp, #-0x18]
    // 0xb20ec0: StoreField: r1->field_f = r0
    //     0xb20ec0: stur            w0, [x1, #0xf]
    // 0xb20ec4: LoadField: r2 = r0->field_b
    //     0xb20ec4: ldur            w2, [x0, #0xb]
    // 0xb20ec8: DecompressPointer r2
    //     0xb20ec8: add             x2, x2, HEAP, lsl #32
    // 0xb20ecc: cmp             w2, NULL
    // 0xb20ed0: b.eq            #0xb21238
    // 0xb20ed4: LoadField: r0 = r2->field_b
    //     0xb20ed4: ldur            w0, [x2, #0xb]
    // 0xb20ed8: DecompressPointer r0
    //     0xb20ed8: add             x0, x0, HEAP, lsl #32
    // 0xb20edc: LoadField: r2 = r0->field_7
    //     0xb20edc: ldur            w2, [x0, #7]
    // 0xb20ee0: cbz             w2, #0xb211b0
    // 0xb20ee4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb20ee4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb20ee8: ldr             x0, [x0, #0x1c80]
    //     0xb20eec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb20ef0: cmp             w0, w16
    //     0xb20ef4: b.ne            #0xb20f00
    //     0xb20ef8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb20efc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb20f00: r0 = GetNavigation.size()
    //     0xb20f00: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xb20f04: LoadField: d0 = r0->field_7
    //     0xb20f04: ldur            d0, [x0, #7]
    // 0xb20f08: stur            d0, [fp, #-0x30]
    // 0xb20f0c: r0 = Radius()
    //     0xb20f0c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb20f10: d0 = 30.000000
    //     0xb20f10: fmov            d0, #30.00000000
    // 0xb20f14: stur            x0, [fp, #-8]
    // 0xb20f18: StoreField: r0->field_7 = d0
    //     0xb20f18: stur            d0, [x0, #7]
    // 0xb20f1c: StoreField: r0->field_f = d0
    //     0xb20f1c: stur            d0, [x0, #0xf]
    // 0xb20f20: r0 = BorderRadius()
    //     0xb20f20: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb20f24: mov             x1, x0
    // 0xb20f28: ldur            x0, [fp, #-8]
    // 0xb20f2c: stur            x1, [fp, #-0x20]
    // 0xb20f30: StoreField: r1->field_7 = r0
    //     0xb20f30: stur            w0, [x1, #7]
    // 0xb20f34: StoreField: r1->field_b = r0
    //     0xb20f34: stur            w0, [x1, #0xb]
    // 0xb20f38: StoreField: r1->field_f = r0
    //     0xb20f38: stur            w0, [x1, #0xf]
    // 0xb20f3c: StoreField: r1->field_13 = r0
    //     0xb20f3c: stur            w0, [x1, #0x13]
    // 0xb20f40: r0 = BoxDecoration()
    //     0xb20f40: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb20f44: mov             x2, x0
    // 0xb20f48: r0 = Instance_Color
    //     0xb20f48: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb20f4c: stur            x2, [fp, #-8]
    // 0xb20f50: StoreField: r2->field_7 = r0
    //     0xb20f50: stur            w0, [x2, #7]
    // 0xb20f54: ldur            x0, [fp, #-0x20]
    // 0xb20f58: StoreField: r2->field_13 = r0
    //     0xb20f58: stur            w0, [x2, #0x13]
    // 0xb20f5c: r0 = Instance_BoxShape
    //     0xb20f5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb20f60: ldr             x0, [x0, #0x80]
    // 0xb20f64: StoreField: r2->field_23 = r0
    //     0xb20f64: stur            w0, [x2, #0x23]
    // 0xb20f68: ldur            x1, [fp, #-0x10]
    // 0xb20f6c: r0 = of()
    //     0xb20f6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb20f70: LoadField: r1 = r0->field_87
    //     0xb20f70: ldur            w1, [x0, #0x87]
    // 0xb20f74: DecompressPointer r1
    //     0xb20f74: add             x1, x1, HEAP, lsl #32
    // 0xb20f78: LoadField: r0 = r1->field_7
    //     0xb20f78: ldur            w0, [x1, #7]
    // 0xb20f7c: DecompressPointer r0
    //     0xb20f7c: add             x0, x0, HEAP, lsl #32
    // 0xb20f80: r16 = Instance_Color
    //     0xb20f80: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb20f84: r30 = 16.000000
    //     0xb20f84: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb20f88: ldr             lr, [lr, #0x188]
    // 0xb20f8c: stp             lr, x16, [SP]
    // 0xb20f90: mov             x1, x0
    // 0xb20f94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb20f94: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb20f98: ldr             x4, [x4, #0x9b8]
    // 0xb20f9c: r0 = copyWith()
    //     0xb20f9c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb20fa0: stur            x0, [fp, #-0x20]
    // 0xb20fa4: r0 = Text()
    //     0xb20fa4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb20fa8: mov             x2, x0
    // 0xb20fac: r0 = "View Chart"
    //     0xb20fac: add             x0, PP, #0x43, lsl #12  ; [pp+0x436d0] "View Chart"
    //     0xb20fb0: ldr             x0, [x0, #0x6d0]
    // 0xb20fb4: stur            x2, [fp, #-0x28]
    // 0xb20fb8: StoreField: r2->field_b = r0
    //     0xb20fb8: stur            w0, [x2, #0xb]
    // 0xb20fbc: ldur            x0, [fp, #-0x20]
    // 0xb20fc0: StoreField: r2->field_13 = r0
    //     0xb20fc0: stur            w0, [x2, #0x13]
    // 0xb20fc4: ldur            x1, [fp, #-0x10]
    // 0xb20fc8: r0 = of()
    //     0xb20fc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb20fcc: LoadField: r1 = r0->field_5b
    //     0xb20fcc: ldur            w1, [x0, #0x5b]
    // 0xb20fd0: DecompressPointer r1
    //     0xb20fd0: add             x1, x1, HEAP, lsl #32
    // 0xb20fd4: stur            x1, [fp, #-0x10]
    // 0xb20fd8: r0 = ColorFilter()
    //     0xb20fd8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb20fdc: mov             x1, x0
    // 0xb20fe0: ldur            x0, [fp, #-0x10]
    // 0xb20fe4: stur            x1, [fp, #-0x20]
    // 0xb20fe8: StoreField: r1->field_7 = r0
    //     0xb20fe8: stur            w0, [x1, #7]
    // 0xb20fec: r0 = Instance_BlendMode
    //     0xb20fec: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb20ff0: ldr             x0, [x0, #0xb30]
    // 0xb20ff4: StoreField: r1->field_b = r0
    //     0xb20ff4: stur            w0, [x1, #0xb]
    // 0xb20ff8: r0 = 1
    //     0xb20ff8: movz            x0, #0x1
    // 0xb20ffc: StoreField: r1->field_13 = r0
    //     0xb20ffc: stur            x0, [x1, #0x13]
    // 0xb21000: r0 = SvgPicture()
    //     0xb21000: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb21004: stur            x0, [fp, #-0x10]
    // 0xb21008: r16 = Instance_BoxFit
    //     0xb21008: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb2100c: ldr             x16, [x16, #0xb18]
    // 0xb21010: ldur            lr, [fp, #-0x20]
    // 0xb21014: stp             lr, x16, [SP, #0x10]
    // 0xb21018: r16 = 40.000000
    //     0xb21018: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb2101c: ldr             x16, [x16, #8]
    // 0xb21020: r30 = 40.000000
    //     0xb21020: add             lr, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb21024: ldr             lr, [lr, #8]
    // 0xb21028: stp             lr, x16, [SP]
    // 0xb2102c: mov             x1, x0
    // 0xb21030: r2 = "assets/images/view_chart.svg"
    //     0xb21030: add             x2, PP, #0x43, lsl #12  ; [pp+0x436d8] "assets/images/view_chart.svg"
    //     0xb21034: ldr             x2, [x2, #0x6d8]
    // 0xb21038: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x3, fit, 0x2, height, 0x4, width, 0x5, null]
    //     0xb21038: add             x4, PP, #0x43, lsl #12  ; [pp+0x436e0] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x3, "fit", 0x2, "height", 0x4, "width", 0x5, Null]
    //     0xb2103c: ldr             x4, [x4, #0x6e0]
    // 0xb21040: r0 = SvgPicture.asset()
    //     0xb21040: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb21044: r1 = Null
    //     0xb21044: mov             x1, NULL
    // 0xb21048: r2 = 6
    //     0xb21048: movz            x2, #0x6
    // 0xb2104c: r0 = AllocateArray()
    //     0xb2104c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb21050: mov             x2, x0
    // 0xb21054: ldur            x0, [fp, #-0x28]
    // 0xb21058: stur            x2, [fp, #-0x20]
    // 0xb2105c: StoreField: r2->field_f = r0
    //     0xb2105c: stur            w0, [x2, #0xf]
    // 0xb21060: r16 = Instance_SizedBox
    //     0xb21060: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0xb21064: ldr             x16, [x16, #0x940]
    // 0xb21068: StoreField: r2->field_13 = r16
    //     0xb21068: stur            w16, [x2, #0x13]
    // 0xb2106c: ldur            x0, [fp, #-0x10]
    // 0xb21070: ArrayStore: r2[0] = r0  ; List_4
    //     0xb21070: stur            w0, [x2, #0x17]
    // 0xb21074: r1 = <Widget>
    //     0xb21074: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb21078: r0 = AllocateGrowableArray()
    //     0xb21078: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2107c: mov             x1, x0
    // 0xb21080: ldur            x0, [fp, #-0x20]
    // 0xb21084: stur            x1, [fp, #-0x10]
    // 0xb21088: StoreField: r1->field_f = r0
    //     0xb21088: stur            w0, [x1, #0xf]
    // 0xb2108c: r0 = 6
    //     0xb2108c: movz            x0, #0x6
    // 0xb21090: StoreField: r1->field_b = r0
    //     0xb21090: stur            w0, [x1, #0xb]
    // 0xb21094: r0 = Row()
    //     0xb21094: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb21098: mov             x1, x0
    // 0xb2109c: r0 = Instance_Axis
    //     0xb2109c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb210a0: stur            x1, [fp, #-0x20]
    // 0xb210a4: StoreField: r1->field_f = r0
    //     0xb210a4: stur            w0, [x1, #0xf]
    // 0xb210a8: r0 = Instance_MainAxisAlignment
    //     0xb210a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb210ac: ldr             x0, [x0, #0xab0]
    // 0xb210b0: StoreField: r1->field_13 = r0
    //     0xb210b0: stur            w0, [x1, #0x13]
    // 0xb210b4: r0 = Instance_MainAxisSize
    //     0xb210b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb210b8: ldr             x0, [x0, #0xa10]
    // 0xb210bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb210bc: stur            w0, [x1, #0x17]
    // 0xb210c0: r0 = Instance_CrossAxisAlignment
    //     0xb210c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb210c4: ldr             x0, [x0, #0xa18]
    // 0xb210c8: StoreField: r1->field_1b = r0
    //     0xb210c8: stur            w0, [x1, #0x1b]
    // 0xb210cc: r0 = Instance_VerticalDirection
    //     0xb210cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb210d0: ldr             x0, [x0, #0xa20]
    // 0xb210d4: StoreField: r1->field_23 = r0
    //     0xb210d4: stur            w0, [x1, #0x23]
    // 0xb210d8: r0 = Instance_Clip
    //     0xb210d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb210dc: ldr             x0, [x0, #0x38]
    // 0xb210e0: StoreField: r1->field_2b = r0
    //     0xb210e0: stur            w0, [x1, #0x2b]
    // 0xb210e4: StoreField: r1->field_2f = rZR
    //     0xb210e4: stur            xzr, [x1, #0x2f]
    // 0xb210e8: ldur            x0, [fp, #-0x10]
    // 0xb210ec: StoreField: r1->field_b = r0
    //     0xb210ec: stur            w0, [x1, #0xb]
    // 0xb210f0: ldur            d0, [fp, #-0x30]
    // 0xb210f4: r0 = inline_Allocate_Double()
    //     0xb210f4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb210f8: add             x0, x0, #0x10
    //     0xb210fc: cmp             x2, x0
    //     0xb21100: b.ls            #0xb2123c
    //     0xb21104: str             x0, [THR, #0x50]  ; THR::top
    //     0xb21108: sub             x0, x0, #0xf
    //     0xb2110c: movz            x2, #0xe15c
    //     0xb21110: movk            x2, #0x3, lsl #16
    //     0xb21114: stur            x2, [x0, #-1]
    // 0xb21118: StoreField: r0->field_7 = d0
    //     0xb21118: stur            d0, [x0, #7]
    // 0xb2111c: stur            x0, [fp, #-0x10]
    // 0xb21120: r0 = Container()
    //     0xb21120: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb21124: stur            x0, [fp, #-0x28]
    // 0xb21128: ldur            x16, [fp, #-0x10]
    // 0xb2112c: r30 = 48.000000
    //     0xb2112c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xb21130: ldr             lr, [lr, #0xad8]
    // 0xb21134: stp             lr, x16, [SP, #0x10]
    // 0xb21138: ldur            x16, [fp, #-8]
    // 0xb2113c: ldur            lr, [fp, #-0x20]
    // 0xb21140: stp             lr, x16, [SP]
    // 0xb21144: mov             x1, x0
    // 0xb21148: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xb21148: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb2114c: ldr             x4, [x4, #0x870]
    // 0xb21150: r0 = Container()
    //     0xb21150: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb21154: r0 = Padding()
    //     0xb21154: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb21158: mov             x1, x0
    // 0xb2115c: r0 = Instance_EdgeInsets
    //     0xb2115c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0xb21160: ldr             x0, [x0, #0x110]
    // 0xb21164: stur            x1, [fp, #-8]
    // 0xb21168: StoreField: r1->field_f = r0
    //     0xb21168: stur            w0, [x1, #0xf]
    // 0xb2116c: ldur            x0, [fp, #-0x28]
    // 0xb21170: StoreField: r1->field_b = r0
    //     0xb21170: stur            w0, [x1, #0xb]
    // 0xb21174: r0 = Container()
    //     0xb21174: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb21178: stur            x0, [fp, #-0x10]
    // 0xb2117c: r16 = Instance_Color
    //     0xb2117c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb21180: ldr             x16, [x16, #0x90]
    // 0xb21184: r30 = inf
    //     0xb21184: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb21188: ldr             lr, [lr, #0x9f8]
    // 0xb2118c: stp             lr, x16, [SP, #8]
    // 0xb21190: ldur            x16, [fp, #-8]
    // 0xb21194: str             x16, [SP]
    // 0xb21198: mov             x1, x0
    // 0xb2119c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x1, width, 0x2, null]
    //     0xb2119c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52908] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x1, "width", 0x2, Null]
    //     0xb211a0: ldr             x4, [x4, #0x908]
    // 0xb211a4: r0 = Container()
    //     0xb211a4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb211a8: ldur            x0, [fp, #-0x10]
    // 0xb211ac: b               #0xb211c8
    // 0xb211b0: r0 = Container()
    //     0xb211b0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb211b4: mov             x1, x0
    // 0xb211b8: stur            x0, [fp, #-8]
    // 0xb211bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb211bc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb211c0: r0 = Container()
    //     0xb211c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb211c4: ldur            x0, [fp, #-8]
    // 0xb211c8: stur            x0, [fp, #-8]
    // 0xb211cc: r0 = InkWell()
    //     0xb211cc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb211d0: mov             x3, x0
    // 0xb211d4: ldur            x0, [fp, #-8]
    // 0xb211d8: stur            x3, [fp, #-0x10]
    // 0xb211dc: StoreField: r3->field_b = r0
    //     0xb211dc: stur            w0, [x3, #0xb]
    // 0xb211e0: ldur            x2, [fp, #-0x18]
    // 0xb211e4: r1 = Function '<anonymous closure>':.
    //     0xb211e4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57688] AnonymousClosure: (0xb21278), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/view_size_widget.dart] _ViewSizeWidgetState::build (0xb20e84)
    //     0xb211e8: ldr             x1, [x1, #0x688]
    // 0xb211ec: r0 = AllocateClosure()
    //     0xb211ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb211f0: mov             x1, x0
    // 0xb211f4: ldur            x0, [fp, #-0x10]
    // 0xb211f8: StoreField: r0->field_f = r1
    //     0xb211f8: stur            w1, [x0, #0xf]
    // 0xb211fc: r1 = true
    //     0xb211fc: add             x1, NULL, #0x20  ; true
    // 0xb21200: StoreField: r0->field_43 = r1
    //     0xb21200: stur            w1, [x0, #0x43]
    // 0xb21204: r2 = Instance_BoxShape
    //     0xb21204: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb21208: ldr             x2, [x2, #0x80]
    // 0xb2120c: StoreField: r0->field_47 = r2
    //     0xb2120c: stur            w2, [x0, #0x47]
    // 0xb21210: StoreField: r0->field_6f = r1
    //     0xb21210: stur            w1, [x0, #0x6f]
    // 0xb21214: r2 = false
    //     0xb21214: add             x2, NULL, #0x30  ; false
    // 0xb21218: StoreField: r0->field_73 = r2
    //     0xb21218: stur            w2, [x0, #0x73]
    // 0xb2121c: StoreField: r0->field_83 = r1
    //     0xb2121c: stur            w1, [x0, #0x83]
    // 0xb21220: StoreField: r0->field_7b = r2
    //     0xb21220: stur            w2, [x0, #0x7b]
    // 0xb21224: LeaveFrame
    //     0xb21224: mov             SP, fp
    //     0xb21228: ldp             fp, lr, [SP], #0x10
    // 0xb2122c: ret
    //     0xb2122c: ret             
    // 0xb21230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb21230: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb21234: b               #0xb20eac
    // 0xb21238: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb21238: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2123c: SaveReg d0
    //     0xb2123c: str             q0, [SP, #-0x10]!
    // 0xb21240: SaveReg r1
    //     0xb21240: str             x1, [SP, #-8]!
    // 0xb21244: r0 = AllocateDouble()
    //     0xb21244: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb21248: RestoreReg r1
    //     0xb21248: ldr             x1, [SP], #8
    // 0xb2124c: RestoreReg d0
    //     0xb2124c: ldr             q0, [SP], #0x10
    // 0xb21250: b               #0xb21118
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb21278, size: 0x78
    // 0xb21278: EnterFrame
    //     0xb21278: stp             fp, lr, [SP, #-0x10]!
    //     0xb2127c: mov             fp, SP
    // 0xb21280: AllocStack(0x18)
    //     0xb21280: sub             SP, SP, #0x18
    // 0xb21284: SetupParameters()
    //     0xb21284: ldr             x0, [fp, #0x10]
    //     0xb21288: ldur            w2, [x0, #0x17]
    //     0xb2128c: add             x2, x2, HEAP, lsl #32
    //     0xb21290: stur            x2, [fp, #-8]
    // 0xb21294: CheckStackOverflow
    //     0xb21294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb21298: cmp             SP, x16
    //     0xb2129c: b.ls            #0xb212e8
    // 0xb212a0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb212a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb212a4: ldr             x0, [x0, #0x1c80]
    //     0xb212a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb212ac: cmp             w0, w16
    //     0xb212b0: b.ne            #0xb212bc
    //     0xb212b4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb212b8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb212bc: ldur            x2, [fp, #-8]
    // 0xb212c0: r1 = Function '<anonymous closure>':.
    //     0xb212c0: add             x1, PP, #0x57, lsl #12  ; [pp+0x57690] AnonymousClosure: (0xb212f0), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/view_size_widget.dart] _ViewSizeWidgetState::build (0xb20e84)
    //     0xb212c4: ldr             x1, [x1, #0x690]
    // 0xb212c8: r0 = AllocateClosure()
    //     0xb212c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb212cc: stp             x0, NULL, [SP]
    // 0xb212d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb212d0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb212d4: r0 = GetNavigation.to()
    //     0xb212d4: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xb212d8: r0 = Null
    //     0xb212d8: mov             x0, NULL
    // 0xb212dc: LeaveFrame
    //     0xb212dc: mov             SP, fp
    //     0xb212e0: ldp             fp, lr, [SP], #0x10
    // 0xb212e4: ret
    //     0xb212e4: ret             
    // 0xb212e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb212e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb212ec: b               #0xb212a0
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0xb212f0, size: 0x58
    // 0xb212f0: EnterFrame
    //     0xb212f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb212f4: mov             fp, SP
    // 0xb212f8: AllocStack(0x8)
    //     0xb212f8: sub             SP, SP, #8
    // 0xb212fc: SetupParameters()
    //     0xb212fc: ldr             x0, [fp, #0x10]
    //     0xb21300: ldur            w1, [x0, #0x17]
    //     0xb21304: add             x1, x1, HEAP, lsl #32
    // 0xb21308: LoadField: r0 = r1->field_f
    //     0xb21308: ldur            w0, [x1, #0xf]
    // 0xb2130c: DecompressPointer r0
    //     0xb2130c: add             x0, x0, HEAP, lsl #32
    // 0xb21310: LoadField: r1 = r0->field_b
    //     0xb21310: ldur            w1, [x0, #0xb]
    // 0xb21314: DecompressPointer r1
    //     0xb21314: add             x1, x1, HEAP, lsl #32
    // 0xb21318: cmp             w1, NULL
    // 0xb2131c: b.eq            #0xb21344
    // 0xb21320: LoadField: r0 = r1->field_b
    //     0xb21320: ldur            w0, [x1, #0xb]
    // 0xb21324: DecompressPointer r0
    //     0xb21324: add             x0, x0, HEAP, lsl #32
    // 0xb21328: stur            x0, [fp, #-8]
    // 0xb2132c: r0 = ViewSizeChart()
    //     0xb2132c: bl              #0xacebb4  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0xb21330: ldur            x1, [fp, #-8]
    // 0xb21334: StoreField: r0->field_b = r1
    //     0xb21334: stur            w1, [x0, #0xb]
    // 0xb21338: LeaveFrame
    //     0xb21338: mov             SP, fp
    //     0xb2133c: ldp             fp, lr, [SP], #0x10
    // 0xb21340: ret
    //     0xb21340: ret             
    // 0xb21344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb21344: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4127, size: 0x10, field offset: 0xc
//   const constructor, 
class ViewSizeWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e3dc, size: 0x24
    // 0xc7e3dc: EnterFrame
    //     0xc7e3dc: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e3e0: mov             fp, SP
    // 0xc7e3e4: mov             x0, x1
    // 0xc7e3e8: r1 = <ViewSizeWidget>
    //     0xc7e3e8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ad0] TypeArguments: <ViewSizeWidget>
    //     0xc7e3ec: ldr             x1, [x1, #0xad0]
    // 0xc7e3f0: r0 = _ViewSizeWidgetState()
    //     0xc7e3f0: bl              #0xc7e400  ; Allocate_ViewSizeWidgetStateStub -> _ViewSizeWidgetState (size=0x14)
    // 0xc7e3f4: LeaveFrame
    //     0xc7e3f4: mov             SP, fp
    //     0xc7e3f8: ldp             fp, lr, [SP], #0x10
    // 0xc7e3fc: ret
    //     0xc7e3fc: ret             
  }
}
