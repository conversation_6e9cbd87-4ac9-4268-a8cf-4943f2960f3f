// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/color_picker_detail.dart

// class id: 1049553, size: 0x8
class :: {
}

// class id: 4483, size: 0x18, field offset: 0xc
//   const constructor, 
class ColorPickerDetail extends StatelessWidget {

  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0xa858b4, size: 0x120
    // 0xa858b4: EnterFrame
    //     0xa858b4: stp             fp, lr, [SP, #-0x10]!
    //     0xa858b8: mov             fp, SP
    // 0xa858bc: AllocStack(0x18)
    //     0xa858bc: sub             SP, SP, #0x18
    // 0xa858c0: CheckStackOverflow
    //     0xa858c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa858c4: cmp             SP, x16
    //     0xa858c8: b.ls            #0xa859bc
    // 0xa858cc: ldr             x0, [fp, #0x10]
    // 0xa858d0: LoadField: r1 = r0->field_b
    //     0xa858d0: ldur            w1, [x0, #0xb]
    // 0xa858d4: DecompressPointer r1
    //     0xa858d4: add             x1, x1, HEAP, lsl #32
    // 0xa858d8: cmp             w1, NULL
    // 0xa858dc: b.eq            #0xa858f8
    // 0xa858e0: LoadField: r2 = r0->field_f
    //     0xa858e0: ldur            x2, [x0, #0xf]
    // 0xa858e4: r0 = LoadInt32Instr(r1)
    //     0xa858e4: sbfx            x0, x1, #1, #0x1f
    //     0xa858e8: tbz             w1, #0, #0xa858f0
    //     0xa858ec: ldur            x0, [x1, #7]
    // 0xa858f0: cmp             x2, x0
    // 0xa858f4: b.le            #0xa85900
    // 0xa858f8: r0 = Null
    //     0xa858f8: mov             x0, NULL
    // 0xa858fc: b               #0xa85934
    // 0xa85900: scvtf           d0, x2
    // 0xa85904: scvtf           d1, x0
    // 0xa85908: fdiv            d2, d0, d1
    // 0xa8590c: r0 = inline_Allocate_Double()
    //     0xa8590c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa85910: add             x0, x0, #0x10
    //     0xa85914: cmp             x1, x0
    //     0xa85918: b.ls            #0xa859c4
    //     0xa8591c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa85920: sub             x0, x0, #0xf
    //     0xa85924: movz            x1, #0xe15c
    //     0xa85928: movk            x1, #0x3, lsl #16
    //     0xa8592c: stur            x1, [x0, #-1]
    // 0xa85930: StoreField: r0->field_7 = d2
    //     0xa85930: stur            d2, [x0, #7]
    // 0xa85934: ldr             x1, [fp, #0x20]
    // 0xa85938: stur            x0, [fp, #-8]
    // 0xa8593c: r0 = of()
    //     0xa8593c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa85940: LoadField: r1 = r0->field_5b
    //     0xa85940: ldur            w1, [x0, #0x5b]
    // 0xa85944: DecompressPointer r1
    //     0xa85944: add             x1, x1, HEAP, lsl #32
    // 0xa85948: stur            x1, [fp, #-0x10]
    // 0xa8594c: r0 = CircularProgressIndicator()
    //     0xa8594c: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xa85950: mov             x1, x0
    // 0xa85954: r0 = Instance__ActivityIndicatorType
    //     0xa85954: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xa85958: ldr             x0, [x0, #0x1b0]
    // 0xa8595c: stur            x1, [fp, #-0x18]
    // 0xa85960: StoreField: r1->field_23 = r0
    //     0xa85960: stur            w0, [x1, #0x23]
    // 0xa85964: ldur            x0, [fp, #-8]
    // 0xa85968: StoreField: r1->field_b = r0
    //     0xa85968: stur            w0, [x1, #0xb]
    // 0xa8596c: ldur            x0, [fp, #-0x10]
    // 0xa85970: StoreField: r1->field_13 = r0
    //     0xa85970: stur            w0, [x1, #0x13]
    // 0xa85974: r0 = SizedBox()
    //     0xa85974: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa85978: mov             x1, x0
    // 0xa8597c: r0 = 20.000000
    //     0xa8597c: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa85980: ldr             x0, [x0, #0xac8]
    // 0xa85984: stur            x1, [fp, #-8]
    // 0xa85988: StoreField: r1->field_f = r0
    //     0xa85988: stur            w0, [x1, #0xf]
    // 0xa8598c: StoreField: r1->field_13 = r0
    //     0xa8598c: stur            w0, [x1, #0x13]
    // 0xa85990: ldur            x0, [fp, #-0x18]
    // 0xa85994: StoreField: r1->field_b = r0
    //     0xa85994: stur            w0, [x1, #0xb]
    // 0xa85998: r0 = Center()
    //     0xa85998: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa8599c: r1 = Instance_Alignment
    //     0xa8599c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa859a0: ldr             x1, [x1, #0xb10]
    // 0xa859a4: StoreField: r0->field_f = r1
    //     0xa859a4: stur            w1, [x0, #0xf]
    // 0xa859a8: ldur            x1, [fp, #-8]
    // 0xa859ac: StoreField: r0->field_b = r1
    //     0xa859ac: stur            w1, [x0, #0xb]
    // 0xa859b0: LeaveFrame
    //     0xa859b0: mov             SP, fp
    //     0xa859b4: ldp             fp, lr, [SP], #0x10
    // 0xa859b8: ret
    //     0xa859b8: ret             
    // 0xa859bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa859bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa859c0: b               #0xa858cc
    // 0xa859c4: SaveReg d2
    //     0xa859c4: str             q2, [SP, #-0x10]!
    // 0xa859c8: r0 = AllocateDouble()
    //     0xa859c8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa859cc: RestoreReg d2
    //     0xa859cc: ldr             q2, [SP], #0x10
    // 0xa859d0: b               #0xa85930
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa859d4, size: 0x484
    // 0xa859d4: EnterFrame
    //     0xa859d4: stp             fp, lr, [SP, #-0x10]!
    //     0xa859d8: mov             fp, SP
    // 0xa859dc: AllocStack(0x70)
    //     0xa859dc: sub             SP, SP, #0x70
    // 0xa859e0: SetupParameters()
    //     0xa859e0: ldr             x0, [fp, #0x20]
    //     0xa859e4: ldur            w1, [x0, #0x17]
    //     0xa859e8: add             x1, x1, HEAP, lsl #32
    //     0xa859ec: stur            x1, [fp, #-8]
    // 0xa859f0: CheckStackOverflow
    //     0xa859f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa859f4: cmp             SP, x16
    //     0xa859f8: b.ls            #0xa85e48
    // 0xa859fc: r1 = 2
    //     0xa859fc: movz            x1, #0x2
    // 0xa85a00: r0 = AllocateContext()
    //     0xa85a00: bl              #0x16f6108  ; AllocateContextStub
    // 0xa85a04: mov             x2, x0
    // 0xa85a08: ldur            x0, [fp, #-8]
    // 0xa85a0c: stur            x2, [fp, #-0x10]
    // 0xa85a10: StoreField: r2->field_b = r0
    //     0xa85a10: stur            w0, [x2, #0xb]
    // 0xa85a14: LoadField: r3 = r0->field_f
    //     0xa85a14: ldur            w3, [x0, #0xf]
    // 0xa85a18: DecompressPointer r3
    //     0xa85a18: add             x3, x3, HEAP, lsl #32
    // 0xa85a1c: LoadField: r0 = r3->field_b
    //     0xa85a1c: ldur            w0, [x3, #0xb]
    // 0xa85a20: DecompressPointer r0
    //     0xa85a20: add             x0, x0, HEAP, lsl #32
    // 0xa85a24: ArrayLoad: r4 = r0[0]  ; List_4
    //     0xa85a24: ldur            w4, [x0, #0x17]
    // 0xa85a28: DecompressPointer r4
    //     0xa85a28: add             x4, x4, HEAP, lsl #32
    // 0xa85a2c: cmp             w4, NULL
    // 0xa85a30: b.eq            #0xa85e50
    // 0xa85a34: LoadField: r0 = r4->field_b
    //     0xa85a34: ldur            w0, [x4, #0xb]
    // 0xa85a38: ldr             x1, [fp, #0x10]
    // 0xa85a3c: r5 = LoadInt32Instr(r1)
    //     0xa85a3c: sbfx            x5, x1, #1, #0x1f
    //     0xa85a40: tbz             w1, #0, #0xa85a48
    //     0xa85a44: ldur            x5, [x1, #7]
    // 0xa85a48: r1 = LoadInt32Instr(r0)
    //     0xa85a48: sbfx            x1, x0, #1, #0x1f
    // 0xa85a4c: mov             x0, x1
    // 0xa85a50: mov             x1, x5
    // 0xa85a54: cmp             x1, x0
    // 0xa85a58: b.hs            #0xa85e54
    // 0xa85a5c: LoadField: r0 = r4->field_f
    //     0xa85a5c: ldur            w0, [x4, #0xf]
    // 0xa85a60: DecompressPointer r0
    //     0xa85a60: add             x0, x0, HEAP, lsl #32
    // 0xa85a64: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa85a64: add             x16, x0, x5, lsl #2
    //     0xa85a68: ldur            w1, [x16, #0xf]
    // 0xa85a6c: DecompressPointer r1
    //     0xa85a6c: add             x1, x1, HEAP, lsl #32
    // 0xa85a70: stur            x1, [fp, #-8]
    // 0xa85a74: StoreField: r2->field_f = r1
    //     0xa85a74: stur            w1, [x2, #0xf]
    // 0xa85a78: LoadField: r0 = r1->field_37
    //     0xa85a78: ldur            w0, [x1, #0x37]
    // 0xa85a7c: DecompressPointer r0
    //     0xa85a7c: add             x0, x0, HEAP, lsl #32
    // 0xa85a80: LoadField: r4 = r3->field_f
    //     0xa85a80: ldur            w4, [x3, #0xf]
    // 0xa85a84: DecompressPointer r4
    //     0xa85a84: add             x4, x4, HEAP, lsl #32
    // 0xa85a88: r3 = LoadClassIdInstr(r0)
    //     0xa85a88: ldur            x3, [x0, #-1]
    //     0xa85a8c: ubfx            x3, x3, #0xc, #0x14
    // 0xa85a90: stp             x4, x0, [SP]
    // 0xa85a94: mov             x0, x3
    // 0xa85a98: mov             lr, x0
    // 0xa85a9c: ldr             lr, [x21, lr, lsl #3]
    // 0xa85aa0: blr             lr
    // 0xa85aa4: ldur            x2, [fp, #-0x10]
    // 0xa85aa8: stur            x0, [fp, #-0x18]
    // 0xa85aac: StoreField: r2->field_13 = r0
    //     0xa85aac: stur            w0, [x2, #0x13]
    // 0xa85ab0: ldur            x3, [fp, #-8]
    // 0xa85ab4: r17 = 279
    //     0xa85ab4: movz            x17, #0x117
    // 0xa85ab8: ldr             w1, [x3, x17]
    // 0xa85abc: DecompressPointer r1
    //     0xa85abc: add             x1, x1, HEAP, lsl #32
    // 0xa85ac0: cmp             w1, NULL
    // 0xa85ac4: b.ne            #0xa85ad0
    // 0xa85ac8: r0 = Null
    //     0xa85ac8: mov             x0, NULL
    // 0xa85acc: b               #0xa85ad4
    // 0xa85ad0: r0 = StringExtension.toTitleCase()
    //     0xa85ad0: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0xa85ad4: cmp             w0, NULL
    // 0xa85ad8: b.ne            #0xa85ae4
    // 0xa85adc: r2 = ""
    //     0xa85adc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa85ae0: b               #0xa85ae8
    // 0xa85ae4: mov             x2, x0
    // 0xa85ae8: ldur            x0, [fp, #-0x18]
    // 0xa85aec: ldr             x1, [fp, #0x18]
    // 0xa85af0: stur            x2, [fp, #-0x20]
    // 0xa85af4: r0 = of()
    //     0xa85af4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa85af8: LoadField: r1 = r0->field_87
    //     0xa85af8: ldur            w1, [x0, #0x87]
    // 0xa85afc: DecompressPointer r1
    //     0xa85afc: add             x1, x1, HEAP, lsl #32
    // 0xa85b00: LoadField: r0 = r1->field_2b
    //     0xa85b00: ldur            w0, [x1, #0x2b]
    // 0xa85b04: DecompressPointer r0
    //     0xa85b04: add             x0, x0, HEAP, lsl #32
    // 0xa85b08: ldur            x2, [fp, #-0x18]
    // 0xa85b0c: stur            x0, [fp, #-0x28]
    // 0xa85b10: tbnz            w2, #4, #0xa85b20
    // 0xa85b14: mov             x0, x2
    // 0xa85b18: r1 = Instance_Color
    //     0xa85b18: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa85b1c: b               #0xa85b34
    // 0xa85b20: r1 = Instance_Color
    //     0xa85b20: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa85b24: d0 = 0.400000
    //     0xa85b24: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa85b28: r0 = withOpacity()
    //     0xa85b28: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa85b2c: mov             x1, x0
    // 0xa85b30: ldur            x0, [fp, #-0x18]
    // 0xa85b34: tbnz            w0, #4, #0xa85b44
    // 0xa85b38: r3 = Instance_FontWeight
    //     0xa85b38: add             x3, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xa85b3c: ldr             x3, [x3, #0x20]
    // 0xa85b40: b               #0xa85b4c
    // 0xa85b44: r3 = Instance_FontWeight
    //     0xa85b44: add             x3, PP, #0x12, lsl #12  ; [pp+0x12ff0] Obj!FontWeight@d68ca1
    //     0xa85b48: ldr             x3, [x3, #0xff0]
    // 0xa85b4c: ldur            x2, [fp, #-0x20]
    // 0xa85b50: r16 = 12.000000
    //     0xa85b50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa85b54: ldr             x16, [x16, #0x9e8]
    // 0xa85b58: stp             x1, x16, [SP, #8]
    // 0xa85b5c: str             x3, [SP]
    // 0xa85b60: ldur            x1, [fp, #-0x28]
    // 0xa85b64: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xa85b64: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xa85b68: ldr             x4, [x4, #0xc48]
    // 0xa85b6c: r0 = copyWith()
    //     0xa85b6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa85b70: stur            x0, [fp, #-0x28]
    // 0xa85b74: r0 = Text()
    //     0xa85b74: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa85b78: mov             x1, x0
    // 0xa85b7c: ldur            x0, [fp, #-0x20]
    // 0xa85b80: stur            x1, [fp, #-0x30]
    // 0xa85b84: StoreField: r1->field_b = r0
    //     0xa85b84: stur            w0, [x1, #0xb]
    // 0xa85b88: ldur            x0, [fp, #-0x28]
    // 0xa85b8c: StoreField: r1->field_13 = r0
    //     0xa85b8c: stur            w0, [x1, #0x13]
    // 0xa85b90: r0 = Padding()
    //     0xa85b90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa85b94: mov             x2, x0
    // 0xa85b98: r0 = Instance_EdgeInsets
    //     0xa85b98: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f4c0] Obj!EdgeInsets@d579e1
    //     0xa85b9c: ldr             x0, [x0, #0x4c0]
    // 0xa85ba0: stur            x2, [fp, #-0x20]
    // 0xa85ba4: StoreField: r2->field_f = r0
    //     0xa85ba4: stur            w0, [x2, #0xf]
    // 0xa85ba8: ldur            x0, [fp, #-0x30]
    // 0xa85bac: StoreField: r2->field_b = r0
    //     0xa85bac: stur            w0, [x2, #0xb]
    // 0xa85bb0: ldur            x0, [fp, #-0x18]
    // 0xa85bb4: tbnz            w0, #4, #0xa85bd0
    // 0xa85bb8: ldr             x1, [fp, #0x18]
    // 0xa85bbc: r0 = of()
    //     0xa85bbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa85bc0: LoadField: r1 = r0->field_5b
    //     0xa85bc0: ldur            w1, [x0, #0x5b]
    // 0xa85bc4: DecompressPointer r1
    //     0xa85bc4: add             x1, x1, HEAP, lsl #32
    // 0xa85bc8: mov             x2, x1
    // 0xa85bcc: b               #0xa85bd8
    // 0xa85bd0: r2 = Instance_Color
    //     0xa85bd0: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xa85bd4: ldr             x2, [x2, #0xf88]
    // 0xa85bd8: ldur            x0, [fp, #-8]
    // 0xa85bdc: r16 = 2.000000
    //     0xa85bdc: add             x16, PP, #0x40, lsl #12  ; [pp+0x40df8] 2
    //     0xa85be0: ldr             x16, [x16, #0xdf8]
    // 0xa85be4: str             x16, [SP]
    // 0xa85be8: r1 = Null
    //     0xa85be8: mov             x1, NULL
    // 0xa85bec: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xa85bec: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xa85bf0: ldr             x4, [x4, #0x108]
    // 0xa85bf4: r0 = Border.all()
    //     0xa85bf4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa85bf8: ldr             x1, [fp, #0x18]
    // 0xa85bfc: stur            x0, [fp, #-0x18]
    // 0xa85c00: r0 = of()
    //     0xa85c00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa85c04: LoadField: r1 = r0->field_5b
    //     0xa85c04: ldur            w1, [x0, #0x5b]
    // 0xa85c08: DecompressPointer r1
    //     0xa85c08: add             x1, x1, HEAP, lsl #32
    // 0xa85c0c: r0 = LoadClassIdInstr(r1)
    //     0xa85c0c: ldur            x0, [x1, #-1]
    //     0xa85c10: ubfx            x0, x0, #0xc, #0x14
    // 0xa85c14: d0 = 0.030000
    //     0xa85c14: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xa85c18: ldr             d0, [x17, #0x238]
    // 0xa85c1c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa85c1c: sub             lr, x0, #0xffa
    //     0xa85c20: ldr             lr, [x21, lr, lsl #3]
    //     0xa85c24: blr             lr
    // 0xa85c28: stur            x0, [fp, #-0x28]
    // 0xa85c2c: r0 = BoxDecoration()
    //     0xa85c2c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa85c30: mov             x1, x0
    // 0xa85c34: ldur            x0, [fp, #-0x28]
    // 0xa85c38: stur            x1, [fp, #-0x30]
    // 0xa85c3c: StoreField: r1->field_7 = r0
    //     0xa85c3c: stur            w0, [x1, #7]
    // 0xa85c40: ldur            x0, [fp, #-0x18]
    // 0xa85c44: StoreField: r1->field_f = r0
    //     0xa85c44: stur            w0, [x1, #0xf]
    // 0xa85c48: r0 = Instance_BoxShape
    //     0xa85c48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa85c4c: ldr             x0, [x0, #0x80]
    // 0xa85c50: StoreField: r1->field_23 = r0
    //     0xa85c50: stur            w0, [x1, #0x23]
    // 0xa85c54: r0 = ImageHeaders.forImages()
    //     0xa85c54: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa85c58: mov             x3, x0
    // 0xa85c5c: ldur            x0, [fp, #-8]
    // 0xa85c60: stur            x3, [fp, #-0x18]
    // 0xa85c64: LoadField: r1 = r0->field_13
    //     0xa85c64: ldur            w1, [x0, #0x13]
    // 0xa85c68: DecompressPointer r1
    //     0xa85c68: add             x1, x1, HEAP, lsl #32
    // 0xa85c6c: cmp             w1, NULL
    // 0xa85c70: b.ne            #0xa85c7c
    // 0xa85c74: r4 = ""
    //     0xa85c74: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa85c78: b               #0xa85c80
    // 0xa85c7c: mov             x4, x1
    // 0xa85c80: ldur            x0, [fp, #-0x20]
    // 0xa85c84: stur            x4, [fp, #-8]
    // 0xa85c88: r1 = Function '<anonymous closure>':.
    //     0xa85c88: add             x1, PP, #0x48, lsl #12  ; [pp+0x483a8] AnonymousClosure: (0xa858b4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x1298224)
    //     0xa85c8c: ldr             x1, [x1, #0x3a8]
    // 0xa85c90: r2 = Null
    //     0xa85c90: mov             x2, NULL
    // 0xa85c94: r0 = AllocateClosure()
    //     0xa85c94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa85c98: r1 = Function '<anonymous closure>':.
    //     0xa85c98: add             x1, PP, #0x48, lsl #12  ; [pp+0x483b0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa85c9c: ldr             x1, [x1, #0x3b0]
    // 0xa85ca0: r2 = Null
    //     0xa85ca0: mov             x2, NULL
    // 0xa85ca4: stur            x0, [fp, #-0x28]
    // 0xa85ca8: r0 = AllocateClosure()
    //     0xa85ca8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa85cac: stur            x0, [fp, #-0x38]
    // 0xa85cb0: r0 = CachedNetworkImage()
    //     0xa85cb0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa85cb4: stur            x0, [fp, #-0x40]
    // 0xa85cb8: ldur            x16, [fp, #-0x18]
    // 0xa85cbc: r30 = 60.000000
    //     0xa85cbc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa85cc0: ldr             lr, [lr, #0x110]
    // 0xa85cc4: stp             lr, x16, [SP, #0x20]
    // 0xa85cc8: r16 = 60.000000
    //     0xa85cc8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa85ccc: ldr             x16, [x16, #0x110]
    // 0xa85cd0: r30 = Instance_BoxFit
    //     0xa85cd0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa85cd4: ldr             lr, [lr, #0x118]
    // 0xa85cd8: stp             lr, x16, [SP, #0x10]
    // 0xa85cdc: ldur            x16, [fp, #-0x28]
    // 0xa85ce0: ldur            lr, [fp, #-0x38]
    // 0xa85ce4: stp             lr, x16, [SP]
    // 0xa85ce8: mov             x1, x0
    // 0xa85cec: ldur            x2, [fp, #-8]
    // 0xa85cf0: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x4, httpHeaders, 0x2, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xa85cf0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c688] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x4, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xa85cf4: ldr             x4, [x4, #0x688]
    // 0xa85cf8: r0 = CachedNetworkImage()
    //     0xa85cf8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa85cfc: r0 = Container()
    //     0xa85cfc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa85d00: stur            x0, [fp, #-8]
    // 0xa85d04: ldur            x16, [fp, #-0x30]
    // 0xa85d08: r30 = Instance_EdgeInsets
    //     0xa85d08: add             lr, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xa85d0c: ldr             lr, [lr, #0xc10]
    // 0xa85d10: stp             lr, x16, [SP, #8]
    // 0xa85d14: ldur            x16, [fp, #-0x40]
    // 0xa85d18: str             x16, [SP]
    // 0xa85d1c: mov             x1, x0
    // 0xa85d20: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xa85d20: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xa85d24: ldr             x4, [x4, #0xb40]
    // 0xa85d28: r0 = Container()
    //     0xa85d28: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa85d2c: r1 = Null
    //     0xa85d2c: mov             x1, NULL
    // 0xa85d30: r2 = 4
    //     0xa85d30: movz            x2, #0x4
    // 0xa85d34: r0 = AllocateArray()
    //     0xa85d34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa85d38: mov             x2, x0
    // 0xa85d3c: ldur            x0, [fp, #-0x20]
    // 0xa85d40: stur            x2, [fp, #-0x18]
    // 0xa85d44: StoreField: r2->field_f = r0
    //     0xa85d44: stur            w0, [x2, #0xf]
    // 0xa85d48: ldur            x0, [fp, #-8]
    // 0xa85d4c: StoreField: r2->field_13 = r0
    //     0xa85d4c: stur            w0, [x2, #0x13]
    // 0xa85d50: r1 = <Widget>
    //     0xa85d50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa85d54: r0 = AllocateGrowableArray()
    //     0xa85d54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa85d58: mov             x1, x0
    // 0xa85d5c: ldur            x0, [fp, #-0x18]
    // 0xa85d60: stur            x1, [fp, #-8]
    // 0xa85d64: StoreField: r1->field_f = r0
    //     0xa85d64: stur            w0, [x1, #0xf]
    // 0xa85d68: r0 = 4
    //     0xa85d68: movz            x0, #0x4
    // 0xa85d6c: StoreField: r1->field_b = r0
    //     0xa85d6c: stur            w0, [x1, #0xb]
    // 0xa85d70: r0 = Column()
    //     0xa85d70: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa85d74: mov             x1, x0
    // 0xa85d78: r0 = Instance_Axis
    //     0xa85d78: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa85d7c: stur            x1, [fp, #-0x18]
    // 0xa85d80: StoreField: r1->field_f = r0
    //     0xa85d80: stur            w0, [x1, #0xf]
    // 0xa85d84: r0 = Instance_MainAxisAlignment
    //     0xa85d84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa85d88: ldr             x0, [x0, #0xa08]
    // 0xa85d8c: StoreField: r1->field_13 = r0
    //     0xa85d8c: stur            w0, [x1, #0x13]
    // 0xa85d90: r0 = Instance_MainAxisSize
    //     0xa85d90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xa85d94: ldr             x0, [x0, #0xdd0]
    // 0xa85d98: ArrayStore: r1[0] = r0  ; List_4
    //     0xa85d98: stur            w0, [x1, #0x17]
    // 0xa85d9c: r0 = Instance_CrossAxisAlignment
    //     0xa85d9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa85da0: ldr             x0, [x0, #0xa18]
    // 0xa85da4: StoreField: r1->field_1b = r0
    //     0xa85da4: stur            w0, [x1, #0x1b]
    // 0xa85da8: r0 = Instance_VerticalDirection
    //     0xa85da8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa85dac: ldr             x0, [x0, #0xa20]
    // 0xa85db0: StoreField: r1->field_23 = r0
    //     0xa85db0: stur            w0, [x1, #0x23]
    // 0xa85db4: r0 = Instance_Clip
    //     0xa85db4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa85db8: ldr             x0, [x0, #0x38]
    // 0xa85dbc: StoreField: r1->field_2b = r0
    //     0xa85dbc: stur            w0, [x1, #0x2b]
    // 0xa85dc0: StoreField: r1->field_2f = rZR
    //     0xa85dc0: stur            xzr, [x1, #0x2f]
    // 0xa85dc4: ldur            x0, [fp, #-8]
    // 0xa85dc8: StoreField: r1->field_b = r0
    //     0xa85dc8: stur            w0, [x1, #0xb]
    // 0xa85dcc: r0 = InkWell()
    //     0xa85dcc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa85dd0: mov             x3, x0
    // 0xa85dd4: ldur            x0, [fp, #-0x18]
    // 0xa85dd8: stur            x3, [fp, #-8]
    // 0xa85ddc: StoreField: r3->field_b = r0
    //     0xa85ddc: stur            w0, [x3, #0xb]
    // 0xa85de0: ldur            x2, [fp, #-0x10]
    // 0xa85de4: r1 = Function '<anonymous closure>':.
    //     0xa85de4: add             x1, PP, #0x48, lsl #12  ; [pp+0x483b8] AnonymousClosure: (0xa85e58), in [package:customer_app/app/presentation/views/line/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x1298224)
    //     0xa85de8: ldr             x1, [x1, #0x3b8]
    // 0xa85dec: r0 = AllocateClosure()
    //     0xa85dec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa85df0: mov             x1, x0
    // 0xa85df4: ldur            x0, [fp, #-8]
    // 0xa85df8: StoreField: r0->field_f = r1
    //     0xa85df8: stur            w1, [x0, #0xf]
    // 0xa85dfc: r1 = true
    //     0xa85dfc: add             x1, NULL, #0x20  ; true
    // 0xa85e00: StoreField: r0->field_43 = r1
    //     0xa85e00: stur            w1, [x0, #0x43]
    // 0xa85e04: r2 = Instance_BoxShape
    //     0xa85e04: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa85e08: ldr             x2, [x2, #0x80]
    // 0xa85e0c: StoreField: r0->field_47 = r2
    //     0xa85e0c: stur            w2, [x0, #0x47]
    // 0xa85e10: StoreField: r0->field_6f = r1
    //     0xa85e10: stur            w1, [x0, #0x6f]
    // 0xa85e14: r2 = false
    //     0xa85e14: add             x2, NULL, #0x30  ; false
    // 0xa85e18: StoreField: r0->field_73 = r2
    //     0xa85e18: stur            w2, [x0, #0x73]
    // 0xa85e1c: StoreField: r0->field_83 = r1
    //     0xa85e1c: stur            w1, [x0, #0x83]
    // 0xa85e20: StoreField: r0->field_7b = r2
    //     0xa85e20: stur            w2, [x0, #0x7b]
    // 0xa85e24: r0 = Padding()
    //     0xa85e24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa85e28: r1 = Instance_EdgeInsets
    //     0xa85e28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xa85e2c: ldr             x1, [x1, #0xd48]
    // 0xa85e30: StoreField: r0->field_f = r1
    //     0xa85e30: stur            w1, [x0, #0xf]
    // 0xa85e34: ldur            x1, [fp, #-8]
    // 0xa85e38: StoreField: r0->field_b = r1
    //     0xa85e38: stur            w1, [x0, #0xb]
    // 0xa85e3c: LeaveFrame
    //     0xa85e3c: mov             SP, fp
    //     0xa85e40: ldp             fp, lr, [SP], #0x10
    // 0xa85e44: ret
    //     0xa85e44: ret             
    // 0xa85e48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85e48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85e4c: b               #0xa859fc
    // 0xa85e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa85e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa85e54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa85e54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa85e58, size: 0xb4
    // 0xa85e58: EnterFrame
    //     0xa85e58: stp             fp, lr, [SP, #-0x10]!
    //     0xa85e5c: mov             fp, SP
    // 0xa85e60: AllocStack(0x18)
    //     0xa85e60: sub             SP, SP, #0x18
    // 0xa85e64: SetupParameters()
    //     0xa85e64: ldr             x0, [fp, #0x10]
    //     0xa85e68: ldur            w1, [x0, #0x17]
    //     0xa85e6c: add             x1, x1, HEAP, lsl #32
    // 0xa85e70: CheckStackOverflow
    //     0xa85e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85e74: cmp             SP, x16
    //     0xa85e78: b.ls            #0xa85f04
    // 0xa85e7c: LoadField: r0 = r1->field_13
    //     0xa85e7c: ldur            w0, [x1, #0x13]
    // 0xa85e80: DecompressPointer r0
    //     0xa85e80: add             x0, x0, HEAP, lsl #32
    // 0xa85e84: tbz             w0, #4, #0xa85ef4
    // 0xa85e88: LoadField: r0 = r1->field_f
    //     0xa85e88: ldur            w0, [x1, #0xf]
    // 0xa85e8c: DecompressPointer r0
    //     0xa85e8c: add             x0, x0, HEAP, lsl #32
    // 0xa85e90: r17 = 283
    //     0xa85e90: movz            x17, #0x11b
    // 0xa85e94: ldr             w2, [x0, x17]
    // 0xa85e98: DecompressPointer r2
    //     0xa85e98: add             x2, x2, HEAP, lsl #32
    // 0xa85e9c: cmp             w2, NULL
    // 0xa85ea0: b.ne            #0xa85ea8
    // 0xa85ea4: r2 = ""
    //     0xa85ea4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa85ea8: LoadField: r3 = r0->field_37
    //     0xa85ea8: ldur            w3, [x0, #0x37]
    // 0xa85eac: DecompressPointer r3
    //     0xa85eac: add             x3, x3, HEAP, lsl #32
    // 0xa85eb0: cmp             w3, NULL
    // 0xa85eb4: b.ne            #0xa85ec0
    // 0xa85eb8: r0 = ""
    //     0xa85eb8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa85ebc: b               #0xa85ec4
    // 0xa85ec0: mov             x0, x3
    // 0xa85ec4: LoadField: r3 = r1->field_b
    //     0xa85ec4: ldur            w3, [x1, #0xb]
    // 0xa85ec8: DecompressPointer r3
    //     0xa85ec8: add             x3, x3, HEAP, lsl #32
    // 0xa85ecc: LoadField: r1 = r3->field_f
    //     0xa85ecc: ldur            w1, [x3, #0xf]
    // 0xa85ed0: DecompressPointer r1
    //     0xa85ed0: add             x1, x1, HEAP, lsl #32
    // 0xa85ed4: LoadField: r3 = r1->field_13
    //     0xa85ed4: ldur            w3, [x1, #0x13]
    // 0xa85ed8: DecompressPointer r3
    //     0xa85ed8: add             x3, x3, HEAP, lsl #32
    // 0xa85edc: stp             x2, x3, [SP, #8]
    // 0xa85ee0: str             x0, [SP]
    // 0xa85ee4: mov             x0, x3
    // 0xa85ee8: ClosureCall
    //     0xa85ee8: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xa85eec: ldur            x2, [x0, #0x1f]
    //     0xa85ef0: blr             x2
    // 0xa85ef4: r0 = Null
    //     0xa85ef4: mov             x0, NULL
    // 0xa85ef8: LeaveFrame
    //     0xa85ef8: mov             SP, fp
    //     0xa85efc: ldp             fp, lr, [SP], #0x10
    // 0xa85f00: ret
    //     0xa85f00: ret             
    // 0xa85f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85f04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85f08: b               #0xa85e7c
  }
  _ build(/* No info */) {
    // ** addr: 0x1298224, size: 0xd4
    // 0x1298224: EnterFrame
    //     0x1298224: stp             fp, lr, [SP, #-0x10]!
    //     0x1298228: mov             fp, SP
    // 0x129822c: AllocStack(0x28)
    //     0x129822c: sub             SP, SP, #0x28
    // 0x1298230: SetupParameters(ColorPickerDetail this /* r1 => r1, fp-0x8 */)
    //     0x1298230: stur            x1, [fp, #-8]
    // 0x1298234: CheckStackOverflow
    //     0x1298234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1298238: cmp             SP, x16
    //     0x129823c: b.ls            #0x12982f0
    // 0x1298240: r1 = 1
    //     0x1298240: movz            x1, #0x1
    // 0x1298244: r0 = AllocateContext()
    //     0x1298244: bl              #0x16f6108  ; AllocateContextStub
    // 0x1298248: mov             x1, x0
    // 0x129824c: ldur            x0, [fp, #-8]
    // 0x1298250: StoreField: r1->field_f = r0
    //     0x1298250: stur            w0, [x1, #0xf]
    // 0x1298254: LoadField: r2 = r0->field_b
    //     0x1298254: ldur            w2, [x0, #0xb]
    // 0x1298258: DecompressPointer r2
    //     0x1298258: add             x2, x2, HEAP, lsl #32
    // 0x129825c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x129825c: ldur            w0, [x2, #0x17]
    // 0x1298260: DecompressPointer r0
    //     0x1298260: add             x0, x0, HEAP, lsl #32
    // 0x1298264: cmp             w0, NULL
    // 0x1298268: b.eq            #0x1298278
    // 0x129826c: LoadField: r3 = r0->field_b
    //     0x129826c: ldur            w3, [x0, #0xb]
    // 0x1298270: stur            x3, [fp, #-8]
    // 0x1298274: cbnz            w3, #0x1298288
    // 0x1298278: r0 = Instance_SizedBox
    //     0x1298278: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x129827c: LeaveFrame
    //     0x129827c: mov             SP, fp
    //     0x1298280: ldp             fp, lr, [SP], #0x10
    // 0x1298284: ret
    //     0x1298284: ret             
    // 0x1298288: mov             x2, x1
    // 0x129828c: r1 = Function '<anonymous closure>':.
    //     0x129828c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48390] AnonymousClosure: (0xa859d4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x1298224)
    //     0x1298290: ldr             x1, [x1, #0x390]
    // 0x1298294: r0 = AllocateClosure()
    //     0x1298294: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1298298: stur            x0, [fp, #-0x10]
    // 0x129829c: r0 = ListView()
    //     0x129829c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x12982a0: stur            x0, [fp, #-0x18]
    // 0x12982a4: r16 = Instance_EdgeInsets
    //     0x12982a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0x12982a8: ldr             x16, [x16, #0xe68]
    // 0x12982ac: r30 = Instance_Axis
    //     0x12982ac: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12982b0: stp             lr, x16, [SP]
    // 0x12982b4: mov             x1, x0
    // 0x12982b8: ldur            x2, [fp, #-0x10]
    // 0x12982bc: ldur            x3, [fp, #-8]
    // 0x12982c0: r4 = const [0, 0x5, 0x2, 0x3, padding, 0x3, scrollDirection, 0x4, null]
    //     0x12982c0: add             x4, PP, #0x48, lsl #12  ; [pp+0x48398] List(9) [0, 0x5, 0x2, 0x3, "padding", 0x3, "scrollDirection", 0x4, Null]
    //     0x12982c4: ldr             x4, [x4, #0x398]
    // 0x12982c8: r0 = ListView.builder()
    //     0x12982c8: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0x12982cc: r0 = SizedBox()
    //     0x12982cc: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x12982d0: r1 = 120.000000
    //     0x12982d0: add             x1, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0x12982d4: ldr             x1, [x1, #0x3a0]
    // 0x12982d8: StoreField: r0->field_13 = r1
    //     0x12982d8: stur            w1, [x0, #0x13]
    // 0x12982dc: ldur            x1, [fp, #-0x18]
    // 0x12982e0: StoreField: r0->field_b = r1
    //     0x12982e0: stur            w1, [x0, #0xb]
    // 0x12982e4: LeaveFrame
    //     0x12982e4: mov             SP, fp
    //     0x12982e8: ldp             fp, lr, [SP], #0x10
    // 0x12982ec: ret
    //     0x12982ec: ret             
    // 0x12982f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12982f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12982f4: b               #0x1298240
  }
}
