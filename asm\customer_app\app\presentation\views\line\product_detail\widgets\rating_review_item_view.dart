// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart

// class id: 1049565, size: 0x8
class :: {
}

// class id: 3218, size: 0x14, field offset: 0x14
class _RatingReviewItemViewState extends State<dynamic> {

  [closure] Null <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xa8c4e0, size: 0x74
    // 0xa8c4e0: EnterFrame
    //     0xa8c4e0: stp             fp, lr, [SP, #-0x10]!
    //     0xa8c4e4: mov             fp, SP
    // 0xa8c4e8: AllocStack(0x10)
    //     0xa8c4e8: sub             SP, SP, #0x10
    // 0xa8c4ec: SetupParameters()
    //     0xa8c4ec: ldr             x0, [fp, #0x18]
    //     0xa8c4f0: ldur            w1, [x0, #0x17]
    //     0xa8c4f4: add             x1, x1, HEAP, lsl #32
    // 0xa8c4f8: CheckStackOverflow
    //     0xa8c4f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8c4fc: cmp             SP, x16
    //     0xa8c500: b.ls            #0xa8c548
    // 0xa8c504: LoadField: r0 = r1->field_f
    //     0xa8c504: ldur            w0, [x1, #0xf]
    // 0xa8c508: DecompressPointer r0
    //     0xa8c508: add             x0, x0, HEAP, lsl #32
    // 0xa8c50c: LoadField: r1 = r0->field_b
    //     0xa8c50c: ldur            w1, [x0, #0xb]
    // 0xa8c510: DecompressPointer r1
    //     0xa8c510: add             x1, x1, HEAP, lsl #32
    // 0xa8c514: cmp             w1, NULL
    // 0xa8c518: b.eq            #0xa8c550
    // 0xa8c51c: LoadField: r0 = r1->field_1b
    //     0xa8c51c: ldur            w0, [x1, #0x1b]
    // 0xa8c520: DecompressPointer r0
    //     0xa8c520: add             x0, x0, HEAP, lsl #32
    // 0xa8c524: ldr             x16, [fp, #0x10]
    // 0xa8c528: stp             x16, x0, [SP]
    // 0xa8c52c: ClosureCall
    //     0xa8c52c: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa8c530: ldur            x2, [x0, #0x1f]
    //     0xa8c534: blr             x2
    // 0xa8c538: r0 = Null
    //     0xa8c538: mov             x0, NULL
    // 0xa8c53c: LeaveFrame
    //     0xa8c53c: mov             SP, fp
    //     0xa8c540: ldp             fp, lr, [SP], #0x10
    // 0xa8c544: ret
    //     0xa8c544: ret             
    // 0xa8c548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8c548: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8c54c: b               #0xa8c504
    // 0xa8c550: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8c550: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0xa8d1a0, size: 0x80
    // 0xa8d1a0: EnterFrame
    //     0xa8d1a0: stp             fp, lr, [SP, #-0x10]!
    //     0xa8d1a4: mov             fp, SP
    // 0xa8d1a8: AllocStack(0x10)
    //     0xa8d1a8: sub             SP, SP, #0x10
    // 0xa8d1ac: SetupParameters()
    //     0xa8d1ac: ldr             x0, [fp, #0x18]
    //     0xa8d1b0: ldur            w1, [x0, #0x17]
    //     0xa8d1b4: add             x1, x1, HEAP, lsl #32
    // 0xa8d1b8: CheckStackOverflow
    //     0xa8d1b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8d1bc: cmp             SP, x16
    //     0xa8d1c0: b.ls            #0xa8d214
    // 0xa8d1c4: LoadField: r0 = r1->field_b
    //     0xa8d1c4: ldur            w0, [x1, #0xb]
    // 0xa8d1c8: DecompressPointer r0
    //     0xa8d1c8: add             x0, x0, HEAP, lsl #32
    // 0xa8d1cc: LoadField: r1 = r0->field_f
    //     0xa8d1cc: ldur            w1, [x0, #0xf]
    // 0xa8d1d0: DecompressPointer r1
    //     0xa8d1d0: add             x1, x1, HEAP, lsl #32
    // 0xa8d1d4: LoadField: r0 = r1->field_b
    //     0xa8d1d4: ldur            w0, [x1, #0xb]
    // 0xa8d1d8: DecompressPointer r0
    //     0xa8d1d8: add             x0, x0, HEAP, lsl #32
    // 0xa8d1dc: cmp             w0, NULL
    // 0xa8d1e0: b.eq            #0xa8d21c
    // 0xa8d1e4: LoadField: r1 = r0->field_1b
    //     0xa8d1e4: ldur            w1, [x0, #0x1b]
    // 0xa8d1e8: DecompressPointer r1
    //     0xa8d1e8: add             x1, x1, HEAP, lsl #32
    // 0xa8d1ec: ldr             x16, [fp, #0x10]
    // 0xa8d1f0: stp             x16, x1, [SP]
    // 0xa8d1f4: mov             x0, x1
    // 0xa8d1f8: ClosureCall
    //     0xa8d1f8: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xa8d1fc: ldur            x2, [x0, #0x1f]
    //     0xa8d200: blr             x2
    // 0xa8d204: r0 = Null
    //     0xa8d204: mov             x0, NULL
    // 0xa8d208: LeaveFrame
    //     0xa8d208: mov             SP, fp
    //     0xa8d20c: ldp             fp, lr, [SP], #0x10
    // 0xa8d210: ret
    //     0xa8d210: ret             
    // 0xa8d214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8d214: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8d218: b               #0xa8d1c4
    // 0xa8d21c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8d21c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewAllMediaOnTapImage <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa8d220, size: 0x1e0
    // 0xa8d220: EnterFrame
    //     0xa8d220: stp             fp, lr, [SP, #-0x10]!
    //     0xa8d224: mov             fp, SP
    // 0xa8d228: AllocStack(0x38)
    //     0xa8d228: sub             SP, SP, #0x38
    // 0xa8d22c: SetupParameters()
    //     0xa8d22c: ldr             x0, [fp, #0x18]
    //     0xa8d230: ldur            w3, [x0, #0x17]
    //     0xa8d234: add             x3, x3, HEAP, lsl #32
    //     0xa8d238: stur            x3, [fp, #-0x10]
    // 0xa8d23c: CheckStackOverflow
    //     0xa8d23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8d240: cmp             SP, x16
    //     0xa8d244: b.ls            #0xa8d3f0
    // 0xa8d248: LoadField: r0 = r3->field_b
    //     0xa8d248: ldur            w0, [x3, #0xb]
    // 0xa8d24c: DecompressPointer r0
    //     0xa8d24c: add             x0, x0, HEAP, lsl #32
    // 0xa8d250: stur            x0, [fp, #-8]
    // 0xa8d254: LoadField: r1 = r0->field_f
    //     0xa8d254: ldur            w1, [x0, #0xf]
    // 0xa8d258: DecompressPointer r1
    //     0xa8d258: add             x1, x1, HEAP, lsl #32
    // 0xa8d25c: LoadField: r2 = r1->field_b
    //     0xa8d25c: ldur            w2, [x1, #0xb]
    // 0xa8d260: DecompressPointer r2
    //     0xa8d260: add             x2, x2, HEAP, lsl #32
    // 0xa8d264: cmp             w2, NULL
    // 0xa8d268: b.eq            #0xa8d3f8
    // 0xa8d26c: LoadField: r1 = r2->field_f
    //     0xa8d26c: ldur            w1, [x2, #0xf]
    // 0xa8d270: DecompressPointer r1
    //     0xa8d270: add             x1, x1, HEAP, lsl #32
    // 0xa8d274: LoadField: r2 = r1->field_b
    //     0xa8d274: ldur            w2, [x1, #0xb]
    // 0xa8d278: DecompressPointer r2
    //     0xa8d278: add             x2, x2, HEAP, lsl #32
    // 0xa8d27c: cmp             w2, NULL
    // 0xa8d280: b.ne            #0xa8d28c
    // 0xa8d284: r1 = Null
    //     0xa8d284: mov             x1, NULL
    // 0xa8d288: b               #0xa8d2b0
    // 0xa8d28c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa8d28c: ldur            w1, [x2, #0x17]
    // 0xa8d290: DecompressPointer r1
    //     0xa8d290: add             x1, x1, HEAP, lsl #32
    // 0xa8d294: cmp             w1, NULL
    // 0xa8d298: b.ne            #0xa8d2a4
    // 0xa8d29c: r1 = Null
    //     0xa8d29c: mov             x1, NULL
    // 0xa8d2a0: b               #0xa8d2b0
    // 0xa8d2a4: LoadField: r2 = r1->field_f
    //     0xa8d2a4: ldur            w2, [x1, #0xf]
    // 0xa8d2a8: DecompressPointer r2
    //     0xa8d2a8: add             x2, x2, HEAP, lsl #32
    // 0xa8d2ac: mov             x1, x2
    // 0xa8d2b0: cmp             w1, NULL
    // 0xa8d2b4: b.ne            #0xa8d2cc
    // 0xa8d2b8: r1 = <ReviewRatingEntity>
    //     0xa8d2b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f150] TypeArguments: <ReviewRatingEntity>
    //     0xa8d2bc: ldr             x1, [x1, #0x150]
    // 0xa8d2c0: r2 = 0
    //     0xa8d2c0: movz            x2, #0
    // 0xa8d2c4: r0 = _GrowableList()
    //     0xa8d2c4: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa8d2c8: mov             x1, x0
    // 0xa8d2cc: ldur            x2, [fp, #-0x10]
    // 0xa8d2d0: ldur            x0, [fp, #-8]
    // 0xa8d2d4: stur            x1, [fp, #-0x20]
    // 0xa8d2d8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xa8d2d8: ldur            w3, [x2, #0x17]
    // 0xa8d2dc: DecompressPointer r3
    //     0xa8d2dc: add             x3, x3, HEAP, lsl #32
    // 0xa8d2e0: stur            x3, [fp, #-0x18]
    // 0xa8d2e4: str             x3, [SP]
    // 0xa8d2e8: r4 = 0
    //     0xa8d2e8: movz            x4, #0
    // 0xa8d2ec: ldr             x0, [SP]
    // 0xa8d2f0: r16 = UnlinkedCall_0x613b5c
    //     0xa8d2f0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52590] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d2f4: add             x16, x16, #0x590
    // 0xa8d2f8: ldp             x5, lr, [x16]
    // 0xa8d2fc: blr             lr
    // 0xa8d300: stur            x0, [fp, #-0x28]
    // 0xa8d304: ldur            x16, [fp, #-0x18]
    // 0xa8d308: str             x16, [SP]
    // 0xa8d30c: r4 = 0
    //     0xa8d30c: movz            x4, #0
    // 0xa8d310: ldr             x0, [SP]
    // 0xa8d314: r16 = UnlinkedCall_0x613b5c
    //     0xa8d314: add             x16, PP, #0x52, lsl #12  ; [pp+0x525a0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d318: add             x16, x16, #0x5a0
    // 0xa8d31c: ldp             x5, lr, [x16]
    // 0xa8d320: blr             lr
    // 0xa8d324: mov             x3, x0
    // 0xa8d328: r2 = Null
    //     0xa8d328: mov             x2, NULL
    // 0xa8d32c: r1 = Null
    //     0xa8d32c: mov             x1, NULL
    // 0xa8d330: stur            x3, [fp, #-0x18]
    // 0xa8d334: branchIfSmi(r0, 0xa8d35c)
    //     0xa8d334: tbz             w0, #0, #0xa8d35c
    // 0xa8d338: r4 = LoadClassIdInstr(r0)
    //     0xa8d338: ldur            x4, [x0, #-1]
    //     0xa8d33c: ubfx            x4, x4, #0xc, #0x14
    // 0xa8d340: sub             x4, x4, #0x3c
    // 0xa8d344: cmp             x4, #1
    // 0xa8d348: b.ls            #0xa8d35c
    // 0xa8d34c: r8 = int?
    //     0xa8d34c: ldr             x8, [PP, #0x38b0]  ; [pp+0x38b0] Type: int?
    // 0xa8d350: r3 = Null
    //     0xa8d350: add             x3, PP, #0x52, lsl #12  ; [pp+0x525b0] Null
    //     0xa8d354: ldr             x3, [x3, #0x5b0]
    // 0xa8d358: r0 = int?()
    //     0xa8d358: bl              #0x16fc50c  ; IsType_int?_Stub
    // 0xa8d35c: ldur            x0, [fp, #-8]
    // 0xa8d360: LoadField: r1 = r0->field_f
    //     0xa8d360: ldur            w1, [x0, #0xf]
    // 0xa8d364: DecompressPointer r1
    //     0xa8d364: add             x1, x1, HEAP, lsl #32
    // 0xa8d368: LoadField: r0 = r1->field_b
    //     0xa8d368: ldur            w0, [x1, #0xb]
    // 0xa8d36c: DecompressPointer r0
    //     0xa8d36c: add             x0, x0, HEAP, lsl #32
    // 0xa8d370: cmp             w0, NULL
    // 0xa8d374: b.eq            #0xa8d3fc
    // 0xa8d378: LoadField: r1 = r0->field_1f
    //     0xa8d378: ldur            w1, [x0, #0x1f]
    // 0xa8d37c: DecompressPointer r1
    //     0xa8d37c: add             x1, x1, HEAP, lsl #32
    // 0xa8d380: stur            x1, [fp, #-8]
    // 0xa8d384: r0 = RatingReviewAllMediaOnTapImage()
    //     0xa8d384: bl              #0x8ff180  ; AllocateRatingReviewAllMediaOnTapImageStub -> RatingReviewAllMediaOnTapImage (size=0x28)
    // 0xa8d388: mov             x3, x0
    // 0xa8d38c: ldur            x0, [fp, #-0x20]
    // 0xa8d390: stur            x3, [fp, #-0x30]
    // 0xa8d394: StoreField: r3->field_f = r0
    //     0xa8d394: stur            w0, [x3, #0xf]
    // 0xa8d398: ldur            x0, [fp, #-0x28]
    // 0xa8d39c: r1 = LoadInt32Instr(r0)
    //     0xa8d39c: sbfx            x1, x0, #1, #0x1f
    //     0xa8d3a0: tbz             w0, #0, #0xa8d3a8
    //     0xa8d3a4: ldur            x1, [x0, #7]
    // 0xa8d3a8: StoreField: r3->field_13 = r1
    //     0xa8d3a8: stur            x1, [x3, #0x13]
    // 0xa8d3ac: ldur            x0, [fp, #-0x18]
    // 0xa8d3b0: StoreField: r3->field_1b = r0
    //     0xa8d3b0: stur            w0, [x3, #0x1b]
    // 0xa8d3b4: r0 = "direct_image"
    //     0xa8d3b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fc98] "direct_image"
    //     0xa8d3b8: ldr             x0, [x0, #0xc98]
    // 0xa8d3bc: StoreField: r3->field_b = r0
    //     0xa8d3bc: stur            w0, [x3, #0xb]
    // 0xa8d3c0: ldur            x2, [fp, #-0x10]
    // 0xa8d3c4: r1 = Function '<anonymous closure>':.
    //     0xa8d3c4: add             x1, PP, #0x52, lsl #12  ; [pp+0x525c0] AnonymousClosure: (0xa8d1a0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xa8d3c8: ldr             x1, [x1, #0x5c0]
    // 0xa8d3cc: r0 = AllocateClosure()
    //     0xa8d3cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d3d0: mov             x1, x0
    // 0xa8d3d4: ldur            x0, [fp, #-0x30]
    // 0xa8d3d8: StoreField: r0->field_1f = r1
    //     0xa8d3d8: stur            w1, [x0, #0x1f]
    // 0xa8d3dc: ldur            x1, [fp, #-8]
    // 0xa8d3e0: StoreField: r0->field_23 = r1
    //     0xa8d3e0: stur            w1, [x0, #0x23]
    // 0xa8d3e4: LeaveFrame
    //     0xa8d3e4: mov             SP, fp
    //     0xa8d3e8: ldp             fp, lr, [SP], #0x10
    // 0xa8d3ec: ret
    //     0xa8d3ec: ret             
    // 0xa8d3f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8d3f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8d3f4: b               #0xa8d248
    // 0xa8d3f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8d3f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa8d3fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8d3fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa8d400, size: 0x128
    // 0xa8d400: EnterFrame
    //     0xa8d400: stp             fp, lr, [SP, #-0x10]!
    //     0xa8d404: mov             fp, SP
    // 0xa8d408: AllocStack(0x30)
    //     0xa8d408: sub             SP, SP, #0x30
    // 0xa8d40c: SetupParameters()
    //     0xa8d40c: ldr             x0, [fp, #0x10]
    //     0xa8d410: ldur            w2, [x0, #0x17]
    //     0xa8d414: add             x2, x2, HEAP, lsl #32
    //     0xa8d418: stur            x2, [fp, #-0x10]
    // 0xa8d41c: CheckStackOverflow
    //     0xa8d41c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8d420: cmp             SP, x16
    //     0xa8d424: b.ls            #0xa8d51c
    // 0xa8d428: LoadField: r0 = r2->field_b
    //     0xa8d428: ldur            w0, [x2, #0xb]
    // 0xa8d42c: DecompressPointer r0
    //     0xa8d42c: add             x0, x0, HEAP, lsl #32
    // 0xa8d430: LoadField: r1 = r0->field_f
    //     0xa8d430: ldur            w1, [x0, #0xf]
    // 0xa8d434: DecompressPointer r1
    //     0xa8d434: add             x1, x1, HEAP, lsl #32
    // 0xa8d438: LoadField: r0 = r1->field_b
    //     0xa8d438: ldur            w0, [x1, #0xb]
    // 0xa8d43c: DecompressPointer r0
    //     0xa8d43c: add             x0, x0, HEAP, lsl #32
    // 0xa8d440: stur            x0, [fp, #-8]
    // 0xa8d444: cmp             w0, NULL
    // 0xa8d448: b.eq            #0xa8d524
    // 0xa8d44c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa8d44c: ldur            w1, [x2, #0x17]
    // 0xa8d450: DecompressPointer r1
    //     0xa8d450: add             x1, x1, HEAP, lsl #32
    // 0xa8d454: str             x1, [SP]
    // 0xa8d458: r4 = 0
    //     0xa8d458: movz            x4, #0
    // 0xa8d45c: ldr             x0, [SP]
    // 0xa8d460: r16 = UnlinkedCall_0x613b5c
    //     0xa8d460: add             x16, PP, #0x52, lsl #12  ; [pp+0x52568] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d464: add             x16, x16, #0x568
    // 0xa8d468: ldp             x5, lr, [x16]
    // 0xa8d46c: blr             lr
    // 0xa8d470: mov             x1, x0
    // 0xa8d474: ldur            x0, [fp, #-8]
    // 0xa8d478: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa8d478: ldur            w2, [x0, #0x17]
    // 0xa8d47c: DecompressPointer r2
    //     0xa8d47c: add             x2, x2, HEAP, lsl #32
    // 0xa8d480: r16 = "single_media"
    //     0xa8d480: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fab0] "single_media"
    //     0xa8d484: ldr             x16, [x16, #0xab0]
    // 0xa8d488: stp             x16, x2, [SP, #0x10]
    // 0xa8d48c: r16 = false
    //     0xa8d48c: add             x16, NULL, #0x30  ; false
    // 0xa8d490: stp             x16, x1, [SP]
    // 0xa8d494: r4 = 0
    //     0xa8d494: movz            x4, #0
    // 0xa8d498: ldr             x0, [SP, #0x18]
    // 0xa8d49c: r16 = UnlinkedCall_0x613b5c
    //     0xa8d49c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52578] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d4a0: add             x16, x16, #0x578
    // 0xa8d4a4: ldp             x5, lr, [x16]
    // 0xa8d4a8: blr             lr
    // 0xa8d4ac: ldur            x2, [fp, #-0x10]
    // 0xa8d4b0: LoadField: r1 = r2->field_f
    //     0xa8d4b0: ldur            w1, [x2, #0xf]
    // 0xa8d4b4: DecompressPointer r1
    //     0xa8d4b4: add             x1, x1, HEAP, lsl #32
    // 0xa8d4b8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa8d4b8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa8d4bc: r0 = of()
    //     0xa8d4bc: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xa8d4c0: ldur            x2, [fp, #-0x10]
    // 0xa8d4c4: r1 = Function '<anonymous closure>':.
    //     0xa8d4c4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52588] AnonymousClosure: (0xa8d220), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xa8d4c8: ldr             x1, [x1, #0x588]
    // 0xa8d4cc: stur            x0, [fp, #-8]
    // 0xa8d4d0: r0 = AllocateClosure()
    //     0xa8d4d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d4d4: r1 = Null
    //     0xa8d4d4: mov             x1, NULL
    // 0xa8d4d8: stur            x0, [fp, #-0x10]
    // 0xa8d4dc: r0 = MaterialPageRoute()
    //     0xa8d4dc: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xa8d4e0: mov             x1, x0
    // 0xa8d4e4: ldur            x2, [fp, #-0x10]
    // 0xa8d4e8: stur            x0, [fp, #-0x10]
    // 0xa8d4ec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa8d4ec: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa8d4f0: r0 = MaterialPageRoute()
    //     0xa8d4f0: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xa8d4f4: ldur            x16, [fp, #-8]
    // 0xa8d4f8: stp             x16, NULL, [SP, #8]
    // 0xa8d4fc: ldur            x16, [fp, #-0x10]
    // 0xa8d500: str             x16, [SP]
    // 0xa8d504: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa8d504: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa8d508: r0 = push()
    //     0xa8d508: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xa8d50c: r0 = Null
    //     0xa8d50c: mov             x0, NULL
    // 0xa8d510: LeaveFrame
    //     0xa8d510: mov             SP, fp
    //     0xa8d514: ldp             fp, lr, [SP], #0x10
    // 0xa8d518: ret
    //     0xa8d518: ret             
    // 0xa8d51c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8d51c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8d520: b               #0xa8d428
    // 0xa8d524: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8d524: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa8d528, size: 0x6cc
    // 0xa8d528: EnterFrame
    //     0xa8d528: stp             fp, lr, [SP, #-0x10]!
    //     0xa8d52c: mov             fp, SP
    // 0xa8d530: AllocStack(0x70)
    //     0xa8d530: sub             SP, SP, #0x70
    // 0xa8d534: SetupParameters()
    //     0xa8d534: ldr             x0, [fp, #0x20]
    //     0xa8d538: ldur            w1, [x0, #0x17]
    //     0xa8d53c: add             x1, x1, HEAP, lsl #32
    //     0xa8d540: stur            x1, [fp, #-8]
    // 0xa8d544: CheckStackOverflow
    //     0xa8d544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8d548: cmp             SP, x16
    //     0xa8d54c: b.ls            #0xa8dbe0
    // 0xa8d550: r1 = 3
    //     0xa8d550: movz            x1, #0x3
    // 0xa8d554: r0 = AllocateContext()
    //     0xa8d554: bl              #0x16f6108  ; AllocateContextStub
    // 0xa8d558: mov             x3, x0
    // 0xa8d55c: ldur            x0, [fp, #-8]
    // 0xa8d560: stur            x3, [fp, #-0x18]
    // 0xa8d564: StoreField: r3->field_b = r0
    //     0xa8d564: stur            w0, [x3, #0xb]
    // 0xa8d568: ldr             x4, [fp, #0x18]
    // 0xa8d56c: StoreField: r3->field_f = r4
    //     0xa8d56c: stur            w4, [x3, #0xf]
    // 0xa8d570: ldr             x5, [fp, #0x10]
    // 0xa8d574: StoreField: r3->field_13 = r5
    //     0xa8d574: stur            w5, [x3, #0x13]
    // 0xa8d578: LoadField: r1 = r0->field_f
    //     0xa8d578: ldur            w1, [x0, #0xf]
    // 0xa8d57c: DecompressPointer r1
    //     0xa8d57c: add             x1, x1, HEAP, lsl #32
    // 0xa8d580: LoadField: r2 = r1->field_b
    //     0xa8d580: ldur            w2, [x1, #0xb]
    // 0xa8d584: DecompressPointer r2
    //     0xa8d584: add             x2, x2, HEAP, lsl #32
    // 0xa8d588: cmp             w2, NULL
    // 0xa8d58c: b.eq            #0xa8dbe8
    // 0xa8d590: LoadField: r1 = r2->field_f
    //     0xa8d590: ldur            w1, [x2, #0xf]
    // 0xa8d594: DecompressPointer r1
    //     0xa8d594: add             x1, x1, HEAP, lsl #32
    // 0xa8d598: LoadField: r2 = r1->field_b
    //     0xa8d598: ldur            w2, [x1, #0xb]
    // 0xa8d59c: DecompressPointer r2
    //     0xa8d59c: add             x2, x2, HEAP, lsl #32
    // 0xa8d5a0: cmp             w2, NULL
    // 0xa8d5a4: b.ne            #0xa8d5b0
    // 0xa8d5a8: r2 = Null
    //     0xa8d5a8: mov             x2, NULL
    // 0xa8d5ac: b               #0xa8d618
    // 0xa8d5b0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa8d5b0: ldur            w1, [x2, #0x17]
    // 0xa8d5b4: DecompressPointer r1
    //     0xa8d5b4: add             x1, x1, HEAP, lsl #32
    // 0xa8d5b8: cmp             w1, NULL
    // 0xa8d5bc: b.ne            #0xa8d5c8
    // 0xa8d5c0: r0 = Null
    //     0xa8d5c0: mov             x0, NULL
    // 0xa8d5c4: b               #0xa8d614
    // 0xa8d5c8: LoadField: r6 = r1->field_f
    //     0xa8d5c8: ldur            w6, [x1, #0xf]
    // 0xa8d5cc: DecompressPointer r6
    //     0xa8d5cc: add             x6, x6, HEAP, lsl #32
    // 0xa8d5d0: stur            x6, [fp, #-0x10]
    // 0xa8d5d4: cmp             w6, NULL
    // 0xa8d5d8: b.ne            #0xa8d5e4
    // 0xa8d5dc: r0 = Null
    //     0xa8d5dc: mov             x0, NULL
    // 0xa8d5e0: b               #0xa8d614
    // 0xa8d5e4: r1 = Function '<anonymous closure>':.
    //     0xa8d5e4: add             x1, PP, #0x52, lsl #12  ; [pp+0x524b0] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xa8d5e8: ldr             x1, [x1, #0x4b0]
    // 0xa8d5ec: r2 = Null
    //     0xa8d5ec: mov             x2, NULL
    // 0xa8d5f0: r0 = AllocateClosure()
    //     0xa8d5f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d5f4: ldur            x16, [fp, #-0x10]
    // 0xa8d5f8: stp             x16, NULL, [SP, #8]
    // 0xa8d5fc: str             x0, [SP]
    // 0xa8d600: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa8d600: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa8d604: r0 = expand()
    //     0xa8d604: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xa8d608: mov             x1, x0
    // 0xa8d60c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa8d60c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa8d610: r0 = toList()
    //     0xa8d610: bl              #0x78a798  ; [dart:core] Iterable::toList
    // 0xa8d614: mov             x2, x0
    // 0xa8d618: cmp             w2, NULL
    // 0xa8d61c: b.ne            #0xa8d62c
    // 0xa8d620: ldr             x3, [fp, #0x10]
    // 0xa8d624: r1 = Null
    //     0xa8d624: mov             x1, NULL
    // 0xa8d628: b               #0xa8d668
    // 0xa8d62c: ldr             x3, [fp, #0x10]
    // 0xa8d630: LoadField: r0 = r2->field_b
    //     0xa8d630: ldur            w0, [x2, #0xb]
    // 0xa8d634: r4 = LoadInt32Instr(r3)
    //     0xa8d634: sbfx            x4, x3, #1, #0x1f
    //     0xa8d638: tbz             w3, #0, #0xa8d640
    //     0xa8d63c: ldur            x4, [x3, #7]
    // 0xa8d640: r1 = LoadInt32Instr(r0)
    //     0xa8d640: sbfx            x1, x0, #1, #0x1f
    // 0xa8d644: mov             x0, x1
    // 0xa8d648: mov             x1, x4
    // 0xa8d64c: cmp             x1, x0
    // 0xa8d650: b.hs            #0xa8dbec
    // 0xa8d654: LoadField: r0 = r2->field_f
    //     0xa8d654: ldur            w0, [x2, #0xf]
    // 0xa8d658: DecompressPointer r0
    //     0xa8d658: add             x0, x0, HEAP, lsl #32
    // 0xa8d65c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xa8d65c: add             x16, x0, x4, lsl #2
    //     0xa8d660: ldur            w1, [x16, #0xf]
    // 0xa8d664: DecompressPointer r1
    //     0xa8d664: add             x1, x1, HEAP, lsl #32
    // 0xa8d668: ldur            x2, [fp, #-0x18]
    // 0xa8d66c: mov             x0, x1
    // 0xa8d670: stur            x1, [fp, #-0x10]
    // 0xa8d674: ArrayStore: r2[0] = r0  ; List_4
    //     0xa8d674: stur            w0, [x2, #0x17]
    //     0xa8d678: tbz             w0, #0, #0xa8d694
    //     0xa8d67c: ldurb           w16, [x2, #-1]
    //     0xa8d680: ldurb           w17, [x0, #-1]
    //     0xa8d684: and             x16, x17, x16, lsr #2
    //     0xa8d688: tst             x16, HEAP, lsr #32
    //     0xa8d68c: b.eq            #0xa8d694
    //     0xa8d690: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa8d694: r0 = LoadInt32Instr(r3)
    //     0xa8d694: sbfx            x0, x3, #1, #0x1f
    //     0xa8d698: tbz             w3, #0, #0xa8d6a0
    //     0xa8d69c: ldur            x0, [x3, #7]
    // 0xa8d6a0: cmp             x0, #4
    // 0xa8d6a4: b.ge            #0xa8d8a8
    // 0xa8d6a8: str             x1, [SP]
    // 0xa8d6ac: r4 = 0
    //     0xa8d6ac: movz            x4, #0
    // 0xa8d6b0: ldr             x0, [SP]
    // 0xa8d6b4: r16 = UnlinkedCall_0x613b5c
    //     0xa8d6b4: add             x16, PP, #0x52, lsl #12  ; [pp+0x524b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d6b8: add             x16, x16, #0x4b8
    // 0xa8d6bc: ldp             x5, lr, [x16]
    // 0xa8d6c0: blr             lr
    // 0xa8d6c4: r1 = 60
    //     0xa8d6c4: movz            x1, #0x3c
    // 0xa8d6c8: branchIfSmi(r0, 0xa8d6d4)
    //     0xa8d6c8: tbz             w0, #0, #0xa8d6d4
    // 0xa8d6cc: r1 = LoadClassIdInstr(r0)
    //     0xa8d6cc: ldur            x1, [x0, #-1]
    //     0xa8d6d0: ubfx            x1, x1, #0xc, #0x14
    // 0xa8d6d4: r16 = "image"
    //     0xa8d6d4: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xa8d6d8: stp             x16, x0, [SP]
    // 0xa8d6dc: mov             x0, x1
    // 0xa8d6e0: mov             lr, x0
    // 0xa8d6e4: ldr             lr, [x21, lr, lsl #3]
    // 0xa8d6e8: blr             lr
    // 0xa8d6ec: tbnz            w0, #4, #0xa8d7d0
    // 0xa8d6f0: r0 = ImageHeaders.forImages()
    //     0xa8d6f0: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa8d6f4: stur            x0, [fp, #-0x20]
    // 0xa8d6f8: ldur            x16, [fp, #-0x10]
    // 0xa8d6fc: str             x16, [SP]
    // 0xa8d700: r4 = 0
    //     0xa8d700: movz            x4, #0
    // 0xa8d704: ldr             x0, [SP]
    // 0xa8d708: r16 = UnlinkedCall_0x613b5c
    //     0xa8d708: add             x16, PP, #0x52, lsl #12  ; [pp+0x524c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d70c: add             x16, x16, #0x4c8
    // 0xa8d710: ldp             x5, lr, [x16]
    // 0xa8d714: blr             lr
    // 0xa8d718: mov             x3, x0
    // 0xa8d71c: r2 = Null
    //     0xa8d71c: mov             x2, NULL
    // 0xa8d720: r1 = Null
    //     0xa8d720: mov             x1, NULL
    // 0xa8d724: stur            x3, [fp, #-0x28]
    // 0xa8d728: r4 = 60
    //     0xa8d728: movz            x4, #0x3c
    // 0xa8d72c: branchIfSmi(r0, 0xa8d738)
    //     0xa8d72c: tbz             w0, #0, #0xa8d738
    // 0xa8d730: r4 = LoadClassIdInstr(r0)
    //     0xa8d730: ldur            x4, [x0, #-1]
    //     0xa8d734: ubfx            x4, x4, #0xc, #0x14
    // 0xa8d738: sub             x4, x4, #0x5e
    // 0xa8d73c: cmp             x4, #1
    // 0xa8d740: b.ls            #0xa8d754
    // 0xa8d744: r8 = String
    //     0xa8d744: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa8d748: r3 = Null
    //     0xa8d748: add             x3, PP, #0x52, lsl #12  ; [pp+0x524d8] Null
    //     0xa8d74c: ldr             x3, [x3, #0x4d8]
    // 0xa8d750: r0 = String()
    //     0xa8d750: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa8d754: r1 = Function '<anonymous closure>':.
    //     0xa8d754: add             x1, PP, #0x52, lsl #12  ; [pp+0x524e8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa8d758: ldr             x1, [x1, #0x4e8]
    // 0xa8d75c: r2 = Null
    //     0xa8d75c: mov             x2, NULL
    // 0xa8d760: r0 = AllocateClosure()
    //     0xa8d760: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d764: r1 = Function '<anonymous closure>':.
    //     0xa8d764: add             x1, PP, #0x52, lsl #12  ; [pp+0x524f0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa8d768: ldr             x1, [x1, #0x4f0]
    // 0xa8d76c: r2 = Null
    //     0xa8d76c: mov             x2, NULL
    // 0xa8d770: stur            x0, [fp, #-0x30]
    // 0xa8d774: r0 = AllocateClosure()
    //     0xa8d774: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d778: stur            x0, [fp, #-0x38]
    // 0xa8d77c: r0 = CachedNetworkImage()
    //     0xa8d77c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa8d780: stur            x0, [fp, #-0x40]
    // 0xa8d784: r16 = 60.000000
    //     0xa8d784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa8d788: ldr             x16, [x16, #0x110]
    // 0xa8d78c: r30 = 60.000000
    //     0xa8d78c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa8d790: ldr             lr, [lr, #0x110]
    // 0xa8d794: stp             lr, x16, [SP, #0x20]
    // 0xa8d798: r16 = Instance_BoxFit
    //     0xa8d798: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa8d79c: ldr             x16, [x16, #0x118]
    // 0xa8d7a0: ldur            lr, [fp, #-0x20]
    // 0xa8d7a4: stp             lr, x16, [SP, #0x10]
    // 0xa8d7a8: ldur            x16, [fp, #-0x30]
    // 0xa8d7ac: ldur            lr, [fp, #-0x38]
    // 0xa8d7b0: stp             lr, x16, [SP]
    // 0xa8d7b4: mov             x1, x0
    // 0xa8d7b8: ldur            x2, [fp, #-0x28]
    // 0xa8d7bc: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x4, height, 0x2, httpHeaders, 0x5, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xa8d7bc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f120] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x4, "height", 0x2, "httpHeaders", 0x5, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xa8d7c0: ldr             x4, [x4, #0x120]
    // 0xa8d7c4: r0 = CachedNetworkImage()
    //     0xa8d7c4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa8d7c8: ldur            x0, [fp, #-0x40]
    // 0xa8d7cc: b               #0xa8d840
    // 0xa8d7d0: ldur            x16, [fp, #-0x10]
    // 0xa8d7d4: str             x16, [SP]
    // 0xa8d7d8: r4 = 0
    //     0xa8d7d8: movz            x4, #0
    // 0xa8d7dc: ldr             x0, [SP]
    // 0xa8d7e0: r16 = UnlinkedCall_0x613b5c
    //     0xa8d7e0: add             x16, PP, #0x52, lsl #12  ; [pp+0x524f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d7e4: add             x16, x16, #0x4f8
    // 0xa8d7e8: ldp             x5, lr, [x16]
    // 0xa8d7ec: blr             lr
    // 0xa8d7f0: mov             x3, x0
    // 0xa8d7f4: r2 = Null
    //     0xa8d7f4: mov             x2, NULL
    // 0xa8d7f8: r1 = Null
    //     0xa8d7f8: mov             x1, NULL
    // 0xa8d7fc: stur            x3, [fp, #-0x20]
    // 0xa8d800: r4 = 60
    //     0xa8d800: movz            x4, #0x3c
    // 0xa8d804: branchIfSmi(r0, 0xa8d810)
    //     0xa8d804: tbz             w0, #0, #0xa8d810
    // 0xa8d808: r4 = LoadClassIdInstr(r0)
    //     0xa8d808: ldur            x4, [x0, #-1]
    //     0xa8d80c: ubfx            x4, x4, #0xc, #0x14
    // 0xa8d810: sub             x4, x4, #0x5e
    // 0xa8d814: cmp             x4, #1
    // 0xa8d818: b.ls            #0xa8d82c
    // 0xa8d81c: r8 = String
    //     0xa8d81c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa8d820: r3 = Null
    //     0xa8d820: add             x3, PP, #0x52, lsl #12  ; [pp+0x52508] Null
    //     0xa8d824: ldr             x3, [x3, #0x508]
    // 0xa8d828: r0 = String()
    //     0xa8d828: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa8d82c: r0 = VideoPlayerWidget()
    //     0xa8d82c: bl              #0x8fed7c  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xa8d830: mov             x1, x0
    // 0xa8d834: ldur            x0, [fp, #-0x20]
    // 0xa8d838: StoreField: r1->field_b = r0
    //     0xa8d838: stur            w0, [x1, #0xb]
    // 0xa8d83c: mov             x0, x1
    // 0xa8d840: stur            x0, [fp, #-0x20]
    // 0xa8d844: r0 = InkWell()
    //     0xa8d844: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa8d848: mov             x3, x0
    // 0xa8d84c: ldur            x0, [fp, #-0x20]
    // 0xa8d850: stur            x3, [fp, #-0x28]
    // 0xa8d854: StoreField: r3->field_b = r0
    //     0xa8d854: stur            w0, [x3, #0xb]
    // 0xa8d858: ldur            x2, [fp, #-0x18]
    // 0xa8d85c: r1 = Function '<anonymous closure>':.
    //     0xa8d85c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52518] AnonymousClosure: (0xa8d400), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xa8d860: ldr             x1, [x1, #0x518]
    // 0xa8d864: r0 = AllocateClosure()
    //     0xa8d864: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d868: mov             x1, x0
    // 0xa8d86c: ldur            x0, [fp, #-0x28]
    // 0xa8d870: StoreField: r0->field_f = r1
    //     0xa8d870: stur            w1, [x0, #0xf]
    // 0xa8d874: r1 = true
    //     0xa8d874: add             x1, NULL, #0x20  ; true
    // 0xa8d878: StoreField: r0->field_43 = r1
    //     0xa8d878: stur            w1, [x0, #0x43]
    // 0xa8d87c: r2 = Instance_BoxShape
    //     0xa8d87c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa8d880: ldr             x2, [x2, #0x80]
    // 0xa8d884: StoreField: r0->field_47 = r2
    //     0xa8d884: stur            w2, [x0, #0x47]
    // 0xa8d888: StoreField: r0->field_6f = r1
    //     0xa8d888: stur            w1, [x0, #0x6f]
    // 0xa8d88c: r3 = false
    //     0xa8d88c: add             x3, NULL, #0x30  ; false
    // 0xa8d890: StoreField: r0->field_73 = r3
    //     0xa8d890: stur            w3, [x0, #0x73]
    // 0xa8d894: StoreField: r0->field_83 = r1
    //     0xa8d894: stur            w1, [x0, #0x83]
    // 0xa8d898: StoreField: r0->field_7b = r3
    //     0xa8d898: stur            w3, [x0, #0x7b]
    // 0xa8d89c: LeaveFrame
    //     0xa8d89c: mov             SP, fp
    //     0xa8d8a0: ldp             fp, lr, [SP], #0x10
    // 0xa8d8a4: ret
    //     0xa8d8a4: ret             
    // 0xa8d8a8: ldur            x0, [fp, #-8]
    // 0xa8d8ac: r1 = true
    //     0xa8d8ac: add             x1, NULL, #0x20  ; true
    // 0xa8d8b0: r3 = false
    //     0xa8d8b0: add             x3, NULL, #0x30  ; false
    // 0xa8d8b4: r2 = Instance_BoxShape
    //     0xa8d8b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa8d8b8: ldr             x2, [x2, #0x80]
    // 0xa8d8bc: r0 = ImageHeaders.forImages()
    //     0xa8d8bc: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa8d8c0: stur            x0, [fp, #-0x20]
    // 0xa8d8c4: ldur            x16, [fp, #-0x10]
    // 0xa8d8c8: str             x16, [SP]
    // 0xa8d8cc: r4 = 0
    //     0xa8d8cc: movz            x4, #0
    // 0xa8d8d0: ldr             x0, [SP]
    // 0xa8d8d4: r16 = UnlinkedCall_0x613b5c
    //     0xa8d8d4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52520] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8d8d8: add             x16, x16, #0x520
    // 0xa8d8dc: ldp             x5, lr, [x16]
    // 0xa8d8e0: blr             lr
    // 0xa8d8e4: mov             x3, x0
    // 0xa8d8e8: r2 = Null
    //     0xa8d8e8: mov             x2, NULL
    // 0xa8d8ec: r1 = Null
    //     0xa8d8ec: mov             x1, NULL
    // 0xa8d8f0: stur            x3, [fp, #-0x10]
    // 0xa8d8f4: r4 = 60
    //     0xa8d8f4: movz            x4, #0x3c
    // 0xa8d8f8: branchIfSmi(r0, 0xa8d904)
    //     0xa8d8f8: tbz             w0, #0, #0xa8d904
    // 0xa8d8fc: r4 = LoadClassIdInstr(r0)
    //     0xa8d8fc: ldur            x4, [x0, #-1]
    //     0xa8d900: ubfx            x4, x4, #0xc, #0x14
    // 0xa8d904: sub             x4, x4, #0x5e
    // 0xa8d908: cmp             x4, #1
    // 0xa8d90c: b.ls            #0xa8d920
    // 0xa8d910: r8 = String
    //     0xa8d910: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xa8d914: r3 = Null
    //     0xa8d914: add             x3, PP, #0x52, lsl #12  ; [pp+0x52530] Null
    //     0xa8d918: ldr             x3, [x3, #0x530]
    // 0xa8d91c: r0 = String()
    //     0xa8d91c: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xa8d920: r1 = Function '<anonymous closure>':.
    //     0xa8d920: add             x1, PP, #0x52, lsl #12  ; [pp+0x52540] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa8d924: ldr             x1, [x1, #0x540]
    // 0xa8d928: r2 = Null
    //     0xa8d928: mov             x2, NULL
    // 0xa8d92c: r0 = AllocateClosure()
    //     0xa8d92c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d930: r1 = Function '<anonymous closure>':.
    //     0xa8d930: add             x1, PP, #0x52, lsl #12  ; [pp+0x52548] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa8d934: ldr             x1, [x1, #0x548]
    // 0xa8d938: r2 = Null
    //     0xa8d938: mov             x2, NULL
    // 0xa8d93c: stur            x0, [fp, #-0x28]
    // 0xa8d940: r0 = AllocateClosure()
    //     0xa8d940: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8d944: stur            x0, [fp, #-0x30]
    // 0xa8d948: r0 = CachedNetworkImage()
    //     0xa8d948: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa8d94c: stur            x0, [fp, #-0x38]
    // 0xa8d950: r16 = 60.000000
    //     0xa8d950: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa8d954: ldr             x16, [x16, #0x110]
    // 0xa8d958: r30 = 60.000000
    //     0xa8d958: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa8d95c: ldr             lr, [lr, #0x110]
    // 0xa8d960: stp             lr, x16, [SP, #0x20]
    // 0xa8d964: r16 = Instance_BoxFit
    //     0xa8d964: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xa8d968: ldr             x16, [x16, #0x118]
    // 0xa8d96c: ldur            lr, [fp, #-0x20]
    // 0xa8d970: stp             lr, x16, [SP, #0x10]
    // 0xa8d974: ldur            x16, [fp, #-0x28]
    // 0xa8d978: ldur            lr, [fp, #-0x30]
    // 0xa8d97c: stp             lr, x16, [SP]
    // 0xa8d980: mov             x1, x0
    // 0xa8d984: ldur            x2, [fp, #-0x10]
    // 0xa8d988: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x4, height, 0x2, httpHeaders, 0x5, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xa8d988: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f120] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x4, "height", 0x2, "httpHeaders", 0x5, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xa8d98c: ldr             x4, [x4, #0x120]
    // 0xa8d990: r0 = CachedNetworkImage()
    //     0xa8d990: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa8d994: r1 = Instance_Color
    //     0xa8d994: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa8d998: d0 = 0.600000
    //     0xa8d998: ldr             d0, [PP, #0x54f8]  ; [pp+0x54f8] IMM: double(0.6) from 0x3fe3333333333333
    // 0xa8d99c: r0 = withOpacity()
    //     0xa8d99c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa8d9a0: r1 = Null
    //     0xa8d9a0: mov             x1, NULL
    // 0xa8d9a4: r2 = 4
    //     0xa8d9a4: movz            x2, #0x4
    // 0xa8d9a8: stur            x0, [fp, #-0x10]
    // 0xa8d9ac: r0 = AllocateArray()
    //     0xa8d9ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa8d9b0: mov             x2, x0
    // 0xa8d9b4: r16 = "+"
    //     0xa8d9b4: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xa8d9b8: StoreField: r2->field_f = r16
    //     0xa8d9b8: stur            w16, [x2, #0xf]
    // 0xa8d9bc: ldur            x0, [fp, #-8]
    // 0xa8d9c0: LoadField: r1 = r0->field_f
    //     0xa8d9c0: ldur            w1, [x0, #0xf]
    // 0xa8d9c4: DecompressPointer r1
    //     0xa8d9c4: add             x1, x1, HEAP, lsl #32
    // 0xa8d9c8: LoadField: r0 = r1->field_b
    //     0xa8d9c8: ldur            w0, [x1, #0xb]
    // 0xa8d9cc: DecompressPointer r0
    //     0xa8d9cc: add             x0, x0, HEAP, lsl #32
    // 0xa8d9d0: cmp             w0, NULL
    // 0xa8d9d4: b.eq            #0xa8dbf0
    // 0xa8d9d8: LoadField: r1 = r0->field_f
    //     0xa8d9d8: ldur            w1, [x0, #0xf]
    // 0xa8d9dc: DecompressPointer r1
    //     0xa8d9dc: add             x1, x1, HEAP, lsl #32
    // 0xa8d9e0: LoadField: r0 = r1->field_b
    //     0xa8d9e0: ldur            w0, [x1, #0xb]
    // 0xa8d9e4: DecompressPointer r0
    //     0xa8d9e4: add             x0, x0, HEAP, lsl #32
    // 0xa8d9e8: cmp             w0, NULL
    // 0xa8d9ec: b.ne            #0xa8d9f8
    // 0xa8d9f0: r0 = Null
    //     0xa8d9f0: mov             x0, NULL
    // 0xa8d9f4: b               #0xa8da18
    // 0xa8d9f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa8d9f8: ldur            w1, [x0, #0x17]
    // 0xa8d9fc: DecompressPointer r1
    //     0xa8d9fc: add             x1, x1, HEAP, lsl #32
    // 0xa8da00: cmp             w1, NULL
    // 0xa8da04: b.ne            #0xa8da10
    // 0xa8da08: r0 = Null
    //     0xa8da08: mov             x0, NULL
    // 0xa8da0c: b               #0xa8da18
    // 0xa8da10: LoadField: r0 = r1->field_b
    //     0xa8da10: ldur            w0, [x1, #0xb]
    // 0xa8da14: DecompressPointer r0
    //     0xa8da14: add             x0, x0, HEAP, lsl #32
    // 0xa8da18: cmp             w0, NULL
    // 0xa8da1c: b.ne            #0xa8da28
    // 0xa8da20: r0 = 0
    //     0xa8da20: movz            x0, #0
    // 0xa8da24: b               #0xa8da38
    // 0xa8da28: r1 = LoadInt32Instr(r0)
    //     0xa8da28: sbfx            x1, x0, #1, #0x1f
    //     0xa8da2c: tbz             w0, #0, #0xa8da34
    //     0xa8da30: ldur            x1, [x0, #7]
    // 0xa8da34: mov             x0, x1
    // 0xa8da38: ldur            x3, [fp, #-0x38]
    // 0xa8da3c: sub             x4, x0, #4
    // 0xa8da40: r0 = BoxInt64Instr(r4)
    //     0xa8da40: sbfiz           x0, x4, #1, #0x1f
    //     0xa8da44: cmp             x4, x0, asr #1
    //     0xa8da48: b.eq            #0xa8da54
    //     0xa8da4c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa8da50: stur            x4, [x0, #7]
    // 0xa8da54: StoreField: r2->field_13 = r0
    //     0xa8da54: stur            w0, [x2, #0x13]
    // 0xa8da58: str             x2, [SP]
    // 0xa8da5c: r0 = _interpolate()
    //     0xa8da5c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa8da60: ldr             x1, [fp, #0x18]
    // 0xa8da64: stur            x0, [fp, #-8]
    // 0xa8da68: r0 = of()
    //     0xa8da68: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa8da6c: LoadField: r1 = r0->field_87
    //     0xa8da6c: ldur            w1, [x0, #0x87]
    // 0xa8da70: DecompressPointer r1
    //     0xa8da70: add             x1, x1, HEAP, lsl #32
    // 0xa8da74: LoadField: r0 = r1->field_7
    //     0xa8da74: ldur            w0, [x1, #7]
    // 0xa8da78: DecompressPointer r0
    //     0xa8da78: add             x0, x0, HEAP, lsl #32
    // 0xa8da7c: r16 = 12.000000
    //     0xa8da7c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa8da80: ldr             x16, [x16, #0x9e8]
    // 0xa8da84: r30 = Instance_Color
    //     0xa8da84: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa8da88: stp             lr, x16, [SP]
    // 0xa8da8c: mov             x1, x0
    // 0xa8da90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa8da90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa8da94: ldr             x4, [x4, #0xaa0]
    // 0xa8da98: r0 = copyWith()
    //     0xa8da98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa8da9c: stur            x0, [fp, #-0x20]
    // 0xa8daa0: r0 = Text()
    //     0xa8daa0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa8daa4: mov             x1, x0
    // 0xa8daa8: ldur            x0, [fp, #-8]
    // 0xa8daac: stur            x1, [fp, #-0x28]
    // 0xa8dab0: StoreField: r1->field_b = r0
    //     0xa8dab0: stur            w0, [x1, #0xb]
    // 0xa8dab4: ldur            x0, [fp, #-0x20]
    // 0xa8dab8: StoreField: r1->field_13 = r0
    //     0xa8dab8: stur            w0, [x1, #0x13]
    // 0xa8dabc: r0 = Container()
    //     0xa8dabc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa8dac0: stur            x0, [fp, #-8]
    // 0xa8dac4: r16 = 60.000000
    //     0xa8dac4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa8dac8: ldr             x16, [x16, #0x110]
    // 0xa8dacc: r30 = 60.000000
    //     0xa8dacc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xa8dad0: ldr             lr, [lr, #0x110]
    // 0xa8dad4: stp             lr, x16, [SP, #0x18]
    // 0xa8dad8: ldur            x16, [fp, #-0x10]
    // 0xa8dadc: r30 = Instance_Alignment
    //     0xa8dadc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa8dae0: ldr             lr, [lr, #0xb10]
    // 0xa8dae4: stp             lr, x16, [SP, #8]
    // 0xa8dae8: ldur            x16, [fp, #-0x28]
    // 0xa8daec: str             x16, [SP]
    // 0xa8daf0: mov             x1, x0
    // 0xa8daf4: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x4, child, 0x5, color, 0x3, height, 0x1, width, 0x2, null]
    //     0xa8daf4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc38] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x4, "child", 0x5, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa8daf8: ldr             x4, [x4, #0xc38]
    // 0xa8dafc: r0 = Container()
    //     0xa8dafc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa8db00: r1 = Null
    //     0xa8db00: mov             x1, NULL
    // 0xa8db04: r2 = 4
    //     0xa8db04: movz            x2, #0x4
    // 0xa8db08: r0 = AllocateArray()
    //     0xa8db08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa8db0c: mov             x2, x0
    // 0xa8db10: ldur            x0, [fp, #-0x38]
    // 0xa8db14: stur            x2, [fp, #-0x10]
    // 0xa8db18: StoreField: r2->field_f = r0
    //     0xa8db18: stur            w0, [x2, #0xf]
    // 0xa8db1c: ldur            x0, [fp, #-8]
    // 0xa8db20: StoreField: r2->field_13 = r0
    //     0xa8db20: stur            w0, [x2, #0x13]
    // 0xa8db24: r1 = <Widget>
    //     0xa8db24: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa8db28: r0 = AllocateGrowableArray()
    //     0xa8db28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa8db2c: mov             x1, x0
    // 0xa8db30: ldur            x0, [fp, #-0x10]
    // 0xa8db34: stur            x1, [fp, #-8]
    // 0xa8db38: StoreField: r1->field_f = r0
    //     0xa8db38: stur            w0, [x1, #0xf]
    // 0xa8db3c: r0 = 4
    //     0xa8db3c: movz            x0, #0x4
    // 0xa8db40: StoreField: r1->field_b = r0
    //     0xa8db40: stur            w0, [x1, #0xb]
    // 0xa8db44: r0 = Stack()
    //     0xa8db44: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa8db48: mov             x1, x0
    // 0xa8db4c: r0 = Instance_Alignment
    //     0xa8db4c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa8db50: ldr             x0, [x0, #0xb10]
    // 0xa8db54: stur            x1, [fp, #-0x10]
    // 0xa8db58: StoreField: r1->field_f = r0
    //     0xa8db58: stur            w0, [x1, #0xf]
    // 0xa8db5c: r0 = Instance_StackFit
    //     0xa8db5c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa8db60: ldr             x0, [x0, #0xfa8]
    // 0xa8db64: ArrayStore: r1[0] = r0  ; List_4
    //     0xa8db64: stur            w0, [x1, #0x17]
    // 0xa8db68: r0 = Instance_Clip
    //     0xa8db68: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa8db6c: ldr             x0, [x0, #0x7e0]
    // 0xa8db70: StoreField: r1->field_1b = r0
    //     0xa8db70: stur            w0, [x1, #0x1b]
    // 0xa8db74: ldur            x0, [fp, #-8]
    // 0xa8db78: StoreField: r1->field_b = r0
    //     0xa8db78: stur            w0, [x1, #0xb]
    // 0xa8db7c: r0 = InkWell()
    //     0xa8db7c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa8db80: mov             x3, x0
    // 0xa8db84: ldur            x0, [fp, #-0x10]
    // 0xa8db88: stur            x3, [fp, #-8]
    // 0xa8db8c: StoreField: r3->field_b = r0
    //     0xa8db8c: stur            w0, [x3, #0xb]
    // 0xa8db90: ldur            x2, [fp, #-0x18]
    // 0xa8db94: r1 = Function '<anonymous closure>':.
    //     0xa8db94: add             x1, PP, #0x52, lsl #12  ; [pp+0x52550] AnonymousClosure: (0xa8dbf4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xa8db98: ldr             x1, [x1, #0x550]
    // 0xa8db9c: r0 = AllocateClosure()
    //     0xa8db9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8dba0: mov             x1, x0
    // 0xa8dba4: ldur            x0, [fp, #-8]
    // 0xa8dba8: StoreField: r0->field_f = r1
    //     0xa8dba8: stur            w1, [x0, #0xf]
    // 0xa8dbac: r1 = true
    //     0xa8dbac: add             x1, NULL, #0x20  ; true
    // 0xa8dbb0: StoreField: r0->field_43 = r1
    //     0xa8dbb0: stur            w1, [x0, #0x43]
    // 0xa8dbb4: r2 = Instance_BoxShape
    //     0xa8dbb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa8dbb8: ldr             x2, [x2, #0x80]
    // 0xa8dbbc: StoreField: r0->field_47 = r2
    //     0xa8dbbc: stur            w2, [x0, #0x47]
    // 0xa8dbc0: StoreField: r0->field_6f = r1
    //     0xa8dbc0: stur            w1, [x0, #0x6f]
    // 0xa8dbc4: r2 = false
    //     0xa8dbc4: add             x2, NULL, #0x30  ; false
    // 0xa8dbc8: StoreField: r0->field_73 = r2
    //     0xa8dbc8: stur            w2, [x0, #0x73]
    // 0xa8dbcc: StoreField: r0->field_83 = r1
    //     0xa8dbcc: stur            w1, [x0, #0x83]
    // 0xa8dbd0: StoreField: r0->field_7b = r2
    //     0xa8dbd0: stur            w2, [x0, #0x7b]
    // 0xa8dbd4: LeaveFrame
    //     0xa8dbd4: mov             SP, fp
    //     0xa8dbd8: ldp             fp, lr, [SP], #0x10
    // 0xa8dbdc: ret
    //     0xa8dbdc: ret             
    // 0xa8dbe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8dbe0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8dbe4: b               #0xa8d550
    // 0xa8dbe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8dbe8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa8dbec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8dbec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8dbf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8dbf0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa8dbf4, size: 0x13c
    // 0xa8dbf4: EnterFrame
    //     0xa8dbf4: stp             fp, lr, [SP, #-0x10]!
    //     0xa8dbf8: mov             fp, SP
    // 0xa8dbfc: AllocStack(0x28)
    //     0xa8dbfc: sub             SP, SP, #0x28
    // 0xa8dc00: SetupParameters()
    //     0xa8dc00: ldr             x0, [fp, #0x10]
    //     0xa8dc04: ldur            w1, [x0, #0x17]
    //     0xa8dc08: add             x1, x1, HEAP, lsl #32
    // 0xa8dc0c: CheckStackOverflow
    //     0xa8dc0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8dc10: cmp             SP, x16
    //     0xa8dc14: b.ls            #0xa8dd20
    // 0xa8dc18: LoadField: r0 = r1->field_b
    //     0xa8dc18: ldur            w0, [x1, #0xb]
    // 0xa8dc1c: DecompressPointer r0
    //     0xa8dc1c: add             x0, x0, HEAP, lsl #32
    // 0xa8dc20: stur            x0, [fp, #-8]
    // 0xa8dc24: LoadField: r1 = r0->field_f
    //     0xa8dc24: ldur            w1, [x0, #0xf]
    // 0xa8dc28: DecompressPointer r1
    //     0xa8dc28: add             x1, x1, HEAP, lsl #32
    // 0xa8dc2c: LoadField: r2 = r1->field_b
    //     0xa8dc2c: ldur            w2, [x1, #0xb]
    // 0xa8dc30: DecompressPointer r2
    //     0xa8dc30: add             x2, x2, HEAP, lsl #32
    // 0xa8dc34: cmp             w2, NULL
    // 0xa8dc38: b.eq            #0xa8dd28
    // 0xa8dc3c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa8dc3c: ldur            w1, [x2, #0x17]
    // 0xa8dc40: DecompressPointer r1
    //     0xa8dc40: add             x1, x1, HEAP, lsl #32
    // 0xa8dc44: r16 = "more_media"
    //     0xa8dc44: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc48] "more_media"
    //     0xa8dc48: ldr             x16, [x16, #0xc48]
    // 0xa8dc4c: stp             x16, x1, [SP, #0x10]
    // 0xa8dc50: r16 = ""
    //     0xa8dc50: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa8dc54: r30 = false
    //     0xa8dc54: add             lr, NULL, #0x30  ; false
    // 0xa8dc58: stp             lr, x16, [SP]
    // 0xa8dc5c: r4 = 0
    //     0xa8dc5c: movz            x4, #0
    // 0xa8dc60: ldr             x0, [SP, #0x18]
    // 0xa8dc64: r16 = UnlinkedCall_0x613b5c
    //     0xa8dc64: add             x16, PP, #0x52, lsl #12  ; [pp+0x52558] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa8dc68: add             x16, x16, #0x558
    // 0xa8dc6c: ldp             x5, lr, [x16]
    // 0xa8dc70: blr             lr
    // 0xa8dc74: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa8dc74: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa8dc78: ldr             x0, [x0, #0x1c80]
    //     0xa8dc7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa8dc80: cmp             w0, w16
    //     0xa8dc84: b.ne            #0xa8dc90
    //     0xa8dc88: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa8dc8c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa8dc90: r1 = Null
    //     0xa8dc90: mov             x1, NULL
    // 0xa8dc94: r2 = 8
    //     0xa8dc94: movz            x2, #0x8
    // 0xa8dc98: r0 = AllocateArray()
    //     0xa8dc98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa8dc9c: r16 = "product_short_id"
    //     0xa8dc9c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb490] "product_short_id"
    //     0xa8dca0: ldr             x16, [x16, #0x490]
    // 0xa8dca4: StoreField: r0->field_f = r16
    //     0xa8dca4: stur            w16, [x0, #0xf]
    // 0xa8dca8: ldur            x1, [fp, #-8]
    // 0xa8dcac: LoadField: r2 = r1->field_f
    //     0xa8dcac: ldur            w2, [x1, #0xf]
    // 0xa8dcb0: DecompressPointer r2
    //     0xa8dcb0: add             x2, x2, HEAP, lsl #32
    // 0xa8dcb4: LoadField: r1 = r2->field_b
    //     0xa8dcb4: ldur            w1, [x2, #0xb]
    // 0xa8dcb8: DecompressPointer r1
    //     0xa8dcb8: add             x1, x1, HEAP, lsl #32
    // 0xa8dcbc: cmp             w1, NULL
    // 0xa8dcc0: b.eq            #0xa8dd2c
    // 0xa8dcc4: LoadField: r2 = r1->field_b
    //     0xa8dcc4: ldur            w2, [x1, #0xb]
    // 0xa8dcc8: DecompressPointer r2
    //     0xa8dcc8: add             x2, x2, HEAP, lsl #32
    // 0xa8dccc: StoreField: r0->field_13 = r2
    //     0xa8dccc: stur            w2, [x0, #0x13]
    // 0xa8dcd0: r16 = "is_product_page"
    //     0xa8dcd0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f368] "is_product_page"
    //     0xa8dcd4: ldr             x16, [x16, #0x368]
    // 0xa8dcd8: ArrayStore: r0[0] = r16  ; List_4
    //     0xa8dcd8: stur            w16, [x0, #0x17]
    // 0xa8dcdc: r16 = true
    //     0xa8dcdc: add             x16, NULL, #0x20  ; true
    // 0xa8dce0: StoreField: r0->field_1b = r16
    //     0xa8dce0: stur            w16, [x0, #0x1b]
    // 0xa8dce4: r16 = <String, Object?>
    //     0xa8dce4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xa8dce8: ldr             x16, [x16, #0xc28]
    // 0xa8dcec: stp             x0, x16, [SP]
    // 0xa8dcf0: r0 = Map._fromLiteral()
    //     0xa8dcf0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xa8dcf4: r16 = "/rating_review_media_screen"
    //     0xa8dcf4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9c8] "/rating_review_media_screen"
    //     0xa8dcf8: ldr             x16, [x16, #0x9c8]
    // 0xa8dcfc: stp             x16, NULL, [SP, #8]
    // 0xa8dd00: str             x0, [SP]
    // 0xa8dd04: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xa8dd04: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xa8dd08: ldr             x4, [x4, #0x438]
    // 0xa8dd0c: r0 = GetNavigation.toNamed()
    //     0xa8dd0c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xa8dd10: r0 = Null
    //     0xa8dd10: mov             x0, NULL
    // 0xa8dd14: LeaveFrame
    //     0xa8dd14: mov             SP, fp
    //     0xa8dd18: ldp             fp, lr, [SP], #0x10
    // 0xa8dd1c: ret
    //     0xa8dd1c: ret             
    // 0xa8dd20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8dd20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8dd24: b               #0xa8dc18
    // 0xa8dd28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8dd28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa8dd2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8dd2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc08ed4, size: 0x2840
    // 0xc08ed4: EnterFrame
    //     0xc08ed4: stp             fp, lr, [SP, #-0x10]!
    //     0xc08ed8: mov             fp, SP
    // 0xc08edc: AllocStack(0x80)
    //     0xc08edc: sub             SP, SP, #0x80
    // 0xc08ee0: SetupParameters(_RatingReviewItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc08ee0: stur            x1, [fp, #-8]
    //     0xc08ee4: stur            x2, [fp, #-0x10]
    // 0xc08ee8: CheckStackOverflow
    //     0xc08ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08eec: cmp             SP, x16
    //     0xc08ef0: b.ls            #0xc0b6c4
    // 0xc08ef4: r1 = 2
    //     0xc08ef4: movz            x1, #0x2
    // 0xc08ef8: r0 = AllocateContext()
    //     0xc08ef8: bl              #0x16f6108  ; AllocateContextStub
    // 0xc08efc: mov             x2, x0
    // 0xc08f00: ldur            x1, [fp, #-8]
    // 0xc08f04: stur            x2, [fp, #-0x18]
    // 0xc08f08: StoreField: r2->field_f = r1
    //     0xc08f08: stur            w1, [x2, #0xf]
    // 0xc08f0c: ldur            x0, [fp, #-0x10]
    // 0xc08f10: StoreField: r2->field_13 = r0
    //     0xc08f10: stur            w0, [x2, #0x13]
    // 0xc08f14: LoadField: r0 = r1->field_b
    //     0xc08f14: ldur            w0, [x1, #0xb]
    // 0xc08f18: DecompressPointer r0
    //     0xc08f18: add             x0, x0, HEAP, lsl #32
    // 0xc08f1c: cmp             w0, NULL
    // 0xc08f20: b.eq            #0xc0b6cc
    // 0xc08f24: LoadField: r3 = r0->field_f
    //     0xc08f24: ldur            w3, [x0, #0xf]
    // 0xc08f28: DecompressPointer r3
    //     0xc08f28: add             x3, x3, HEAP, lsl #32
    // 0xc08f2c: LoadField: r4 = r3->field_b
    //     0xc08f2c: ldur            w4, [x3, #0xb]
    // 0xc08f30: DecompressPointer r4
    //     0xc08f30: add             x4, x4, HEAP, lsl #32
    // 0xc08f34: cmp             w4, NULL
    // 0xc08f38: b.eq            #0xc0b6b0
    // 0xc08f3c: LoadField: r3 = r4->field_f
    //     0xc08f3c: ldur            w3, [x4, #0xf]
    // 0xc08f40: DecompressPointer r3
    //     0xc08f40: add             x3, x3, HEAP, lsl #32
    // 0xc08f44: cmp             w3, NULL
    // 0xc08f48: b.ne            #0xc08f54
    // 0xc08f4c: r3 = Null
    //     0xc08f4c: mov             x3, NULL
    // 0xc08f50: b               #0xc08f60
    // 0xc08f54: LoadField: r4 = r3->field_13
    //     0xc08f54: ldur            w4, [x3, #0x13]
    // 0xc08f58: DecompressPointer r4
    //     0xc08f58: add             x4, x4, HEAP, lsl #32
    // 0xc08f5c: mov             x3, x4
    // 0xc08f60: cbnz            w3, #0xc08f6c
    // 0xc08f64: r4 = false
    //     0xc08f64: add             x4, NULL, #0x30  ; false
    // 0xc08f68: b               #0xc08f70
    // 0xc08f6c: r4 = true
    //     0xc08f6c: add             x4, NULL, #0x20  ; true
    // 0xc08f70: stur            x4, [fp, #-0x10]
    // 0xc08f74: LoadField: r3 = r0->field_13
    //     0xc08f74: ldur            w3, [x0, #0x13]
    // 0xc08f78: DecompressPointer r3
    //     0xc08f78: add             x3, x3, HEAP, lsl #32
    // 0xc08f7c: cmp             w3, NULL
    // 0xc08f80: b.ne            #0xc08f8c
    // 0xc08f84: r0 = ""
    //     0xc08f84: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc08f88: b               #0xc08f90
    // 0xc08f8c: mov             x0, x3
    // 0xc08f90: r3 = LoadClassIdInstr(r0)
    //     0xc08f90: ldur            x3, [x0, #-1]
    //     0xc08f94: ubfx            x3, x3, #0xc, #0x14
    // 0xc08f98: str             x0, [SP]
    // 0xc08f9c: mov             x0, x3
    // 0xc08fa0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc08fa0: sub             lr, x0, #1, lsl #12
    //     0xc08fa4: ldr             lr, [x21, lr, lsl #3]
    //     0xc08fa8: blr             lr
    // 0xc08fac: ldur            x2, [fp, #-0x18]
    // 0xc08fb0: stur            x0, [fp, #-0x20]
    // 0xc08fb4: LoadField: r1 = r2->field_13
    //     0xc08fb4: ldur            w1, [x2, #0x13]
    // 0xc08fb8: DecompressPointer r1
    //     0xc08fb8: add             x1, x1, HEAP, lsl #32
    // 0xc08fbc: r0 = of()
    //     0xc08fbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc08fc0: LoadField: r1 = r0->field_87
    //     0xc08fc0: ldur            w1, [x0, #0x87]
    // 0xc08fc4: DecompressPointer r1
    //     0xc08fc4: add             x1, x1, HEAP, lsl #32
    // 0xc08fc8: LoadField: r0 = r1->field_7
    //     0xc08fc8: ldur            w0, [x1, #7]
    // 0xc08fcc: DecompressPointer r0
    //     0xc08fcc: add             x0, x0, HEAP, lsl #32
    // 0xc08fd0: r16 = 21.000000
    //     0xc08fd0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xc08fd4: ldr             x16, [x16, #0x9b0]
    // 0xc08fd8: r30 = Instance_Color
    //     0xc08fd8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc08fdc: stp             lr, x16, [SP]
    // 0xc08fe0: mov             x1, x0
    // 0xc08fe4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc08fe4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc08fe8: ldr             x4, [x4, #0xaa0]
    // 0xc08fec: r0 = copyWith()
    //     0xc08fec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc08ff0: stur            x0, [fp, #-0x28]
    // 0xc08ff4: r0 = Text()
    //     0xc08ff4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc08ff8: mov             x1, x0
    // 0xc08ffc: ldur            x0, [fp, #-0x20]
    // 0xc09000: stur            x1, [fp, #-0x30]
    // 0xc09004: StoreField: r1->field_b = r0
    //     0xc09004: stur            w0, [x1, #0xb]
    // 0xc09008: ldur            x0, [fp, #-0x28]
    // 0xc0900c: StoreField: r1->field_13 = r0
    //     0xc0900c: stur            w0, [x1, #0x13]
    // 0xc09010: r0 = Center()
    //     0xc09010: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc09014: mov             x2, x0
    // 0xc09018: r1 = Instance_Alignment
    //     0xc09018: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0901c: ldr             x1, [x1, #0xb10]
    // 0xc09020: stur            x2, [fp, #-0x20]
    // 0xc09024: StoreField: r2->field_f = r1
    //     0xc09024: stur            w1, [x2, #0xf]
    // 0xc09028: ldur            x0, [fp, #-0x30]
    // 0xc0902c: StoreField: r2->field_b = r0
    //     0xc0902c: stur            w0, [x2, #0xb]
    // 0xc09030: ldur            x3, [fp, #-8]
    // 0xc09034: LoadField: r0 = r3->field_b
    //     0xc09034: ldur            w0, [x3, #0xb]
    // 0xc09038: DecompressPointer r0
    //     0xc09038: add             x0, x0, HEAP, lsl #32
    // 0xc0903c: cmp             w0, NULL
    // 0xc09040: b.eq            #0xc0b6d0
    // 0xc09044: LoadField: r4 = r0->field_f
    //     0xc09044: ldur            w4, [x0, #0xf]
    // 0xc09048: DecompressPointer r4
    //     0xc09048: add             x4, x4, HEAP, lsl #32
    // 0xc0904c: LoadField: r0 = r4->field_b
    //     0xc0904c: ldur            w0, [x4, #0xb]
    // 0xc09050: DecompressPointer r0
    //     0xc09050: add             x0, x0, HEAP, lsl #32
    // 0xc09054: cmp             w0, NULL
    // 0xc09058: b.ne            #0xc09064
    // 0xc0905c: r0 = Null
    //     0xc0905c: mov             x0, NULL
    // 0xc09060: b               #0xc090a8
    // 0xc09064: LoadField: r4 = r0->field_f
    //     0xc09064: ldur            w4, [x0, #0xf]
    // 0xc09068: DecompressPointer r4
    //     0xc09068: add             x4, x4, HEAP, lsl #32
    // 0xc0906c: cmp             w4, NULL
    // 0xc09070: b.ne            #0xc0907c
    // 0xc09074: r0 = Null
    //     0xc09074: mov             x0, NULL
    // 0xc09078: b               #0xc090a8
    // 0xc0907c: LoadField: r0 = r4->field_f
    //     0xc0907c: ldur            w0, [x4, #0xf]
    // 0xc09080: DecompressPointer r0
    //     0xc09080: add             x0, x0, HEAP, lsl #32
    // 0xc09084: r4 = LoadClassIdInstr(r0)
    //     0xc09084: ldur            x4, [x0, #-1]
    //     0xc09088: ubfx            x4, x4, #0xc, #0x14
    // 0xc0908c: str             x0, [SP]
    // 0xc09090: mov             x0, x4
    // 0xc09094: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc09094: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc09098: r0 = GDT[cid_x0 + 0x2700]()
    //     0xc09098: movz            x17, #0x2700
    //     0xc0909c: add             lr, x0, x17
    //     0xc090a0: ldr             lr, [x21, lr, lsl #3]
    //     0xc090a4: blr             lr
    // 0xc090a8: cmp             w0, NULL
    // 0xc090ac: b.ne            #0xc090b8
    // 0xc090b0: r3 = ""
    //     0xc090b0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc090b4: b               #0xc090bc
    // 0xc090b8: mov             x3, x0
    // 0xc090bc: ldur            x0, [fp, #-8]
    // 0xc090c0: ldur            x2, [fp, #-0x18]
    // 0xc090c4: stur            x3, [fp, #-0x28]
    // 0xc090c8: LoadField: r1 = r2->field_13
    //     0xc090c8: ldur            w1, [x2, #0x13]
    // 0xc090cc: DecompressPointer r1
    //     0xc090cc: add             x1, x1, HEAP, lsl #32
    // 0xc090d0: r0 = of()
    //     0xc090d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc090d4: LoadField: r1 = r0->field_87
    //     0xc090d4: ldur            w1, [x0, #0x87]
    // 0xc090d8: DecompressPointer r1
    //     0xc090d8: add             x1, x1, HEAP, lsl #32
    // 0xc090dc: LoadField: r0 = r1->field_23
    //     0xc090dc: ldur            w0, [x1, #0x23]
    // 0xc090e0: DecompressPointer r0
    //     0xc090e0: add             x0, x0, HEAP, lsl #32
    // 0xc090e4: r16 = Instance_Color
    //     0xc090e4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc090e8: r30 = 32.000000
    //     0xc090e8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xc090ec: ldr             lr, [lr, #0x848]
    // 0xc090f0: stp             lr, x16, [SP]
    // 0xc090f4: mov             x1, x0
    // 0xc090f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc090f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc090fc: ldr             x4, [x4, #0x9b8]
    // 0xc09100: r0 = copyWith()
    //     0xc09100: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc09104: stur            x0, [fp, #-0x30]
    // 0xc09108: r0 = Text()
    //     0xc09108: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0910c: mov             x1, x0
    // 0xc09110: ldur            x0, [fp, #-0x28]
    // 0xc09114: stur            x1, [fp, #-0x38]
    // 0xc09118: StoreField: r1->field_b = r0
    //     0xc09118: stur            w0, [x1, #0xb]
    // 0xc0911c: ldur            x0, [fp, #-0x30]
    // 0xc09120: StoreField: r1->field_13 = r0
    //     0xc09120: stur            w0, [x1, #0x13]
    // 0xc09124: r0 = Padding()
    //     0xc09124: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc09128: mov             x2, x0
    // 0xc0912c: r0 = Instance_EdgeInsets
    //     0xc0912c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f850] Obj!EdgeInsets@d57801
    //     0xc09130: ldr             x0, [x0, #0x850]
    // 0xc09134: stur            x2, [fp, #-0x28]
    // 0xc09138: StoreField: r2->field_f = r0
    //     0xc09138: stur            w0, [x2, #0xf]
    // 0xc0913c: ldur            x0, [fp, #-0x38]
    // 0xc09140: StoreField: r2->field_b = r0
    //     0xc09140: stur            w0, [x2, #0xb]
    // 0xc09144: ldur            x0, [fp, #-8]
    // 0xc09148: LoadField: r1 = r0->field_b
    //     0xc09148: ldur            w1, [x0, #0xb]
    // 0xc0914c: DecompressPointer r1
    //     0xc0914c: add             x1, x1, HEAP, lsl #32
    // 0xc09150: cmp             w1, NULL
    // 0xc09154: b.eq            #0xc0b6d4
    // 0xc09158: LoadField: r3 = r1->field_f
    //     0xc09158: ldur            w3, [x1, #0xf]
    // 0xc0915c: DecompressPointer r3
    //     0xc0915c: add             x3, x3, HEAP, lsl #32
    // 0xc09160: LoadField: r1 = r3->field_b
    //     0xc09160: ldur            w1, [x3, #0xb]
    // 0xc09164: DecompressPointer r1
    //     0xc09164: add             x1, x1, HEAP, lsl #32
    // 0xc09168: cmp             w1, NULL
    // 0xc0916c: b.ne            #0xc09178
    // 0xc09170: r3 = Null
    //     0xc09170: mov             x3, NULL
    // 0xc09174: b               #0xc0919c
    // 0xc09178: LoadField: r3 = r1->field_f
    //     0xc09178: ldur            w3, [x1, #0xf]
    // 0xc0917c: DecompressPointer r3
    //     0xc0917c: add             x3, x3, HEAP, lsl #32
    // 0xc09180: cmp             w3, NULL
    // 0xc09184: b.ne            #0xc09190
    // 0xc09188: r3 = Null
    //     0xc09188: mov             x3, NULL
    // 0xc0918c: b               #0xc0919c
    // 0xc09190: LoadField: r4 = r3->field_f
    //     0xc09190: ldur            w4, [x3, #0xf]
    // 0xc09194: DecompressPointer r4
    //     0xc09194: add             x4, x4, HEAP, lsl #32
    // 0xc09198: mov             x3, x4
    // 0xc0919c: cmp             w3, NULL
    // 0xc091a0: b.ne            #0xc091ac
    // 0xc091a4: d1 = 0.000000
    //     0xc091a4: eor             v1.16b, v1.16b, v1.16b
    // 0xc091a8: b               #0xc091b4
    // 0xc091ac: LoadField: d0 = r3->field_7
    //     0xc091ac: ldur            d0, [x3, #7]
    // 0xc091b0: mov             v1.16b, v0.16b
    // 0xc091b4: d0 = 4.000000
    //     0xc091b4: fmov            d0, #4.00000000
    // 0xc091b8: fcmp            d1, d0
    // 0xc091bc: b.lt            #0xc091d4
    // 0xc091c0: mov             x1, x0
    // 0xc091c4: mov             x0, x2
    // 0xc091c8: r2 = Instance_Color
    //     0xc091c8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc091cc: ldr             x2, [x2, #0x858]
    // 0xc091d0: b               #0xc092bc
    // 0xc091d4: cmp             w1, NULL
    // 0xc091d8: b.ne            #0xc091e4
    // 0xc091dc: r3 = Null
    //     0xc091dc: mov             x3, NULL
    // 0xc091e0: b               #0xc09208
    // 0xc091e4: LoadField: r3 = r1->field_f
    //     0xc091e4: ldur            w3, [x1, #0xf]
    // 0xc091e8: DecompressPointer r3
    //     0xc091e8: add             x3, x3, HEAP, lsl #32
    // 0xc091ec: cmp             w3, NULL
    // 0xc091f0: b.ne            #0xc091fc
    // 0xc091f4: r3 = Null
    //     0xc091f4: mov             x3, NULL
    // 0xc091f8: b               #0xc09208
    // 0xc091fc: LoadField: r4 = r3->field_f
    //     0xc091fc: ldur            w4, [x3, #0xf]
    // 0xc09200: DecompressPointer r4
    //     0xc09200: add             x4, x4, HEAP, lsl #32
    // 0xc09204: mov             x3, x4
    // 0xc09208: cmp             w3, NULL
    // 0xc0920c: b.ne            #0xc09218
    // 0xc09210: d1 = 0.000000
    //     0xc09210: eor             v1.16b, v1.16b, v1.16b
    // 0xc09214: b               #0xc09220
    // 0xc09218: LoadField: d0 = r3->field_7
    //     0xc09218: ldur            d0, [x3, #7]
    // 0xc0921c: mov             v1.16b, v0.16b
    // 0xc09220: d0 = 3.500000
    //     0xc09220: fmov            d0, #3.50000000
    // 0xc09224: fcmp            d1, d0
    // 0xc09228: b.lt            #0xc09244
    // 0xc0922c: r1 = Instance_Color
    //     0xc0922c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xc09230: ldr             x1, [x1, #0x858]
    // 0xc09234: d0 = 0.700000
    //     0xc09234: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc09238: ldr             d0, [x17, #0xf48]
    // 0xc0923c: r0 = withOpacity()
    //     0xc0923c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc09240: b               #0xc092b0
    // 0xc09244: cmp             w1, NULL
    // 0xc09248: b.ne            #0xc09254
    // 0xc0924c: r0 = Null
    //     0xc0924c: mov             x0, NULL
    // 0xc09250: b               #0xc09278
    // 0xc09254: LoadField: r0 = r1->field_f
    //     0xc09254: ldur            w0, [x1, #0xf]
    // 0xc09258: DecompressPointer r0
    //     0xc09258: add             x0, x0, HEAP, lsl #32
    // 0xc0925c: cmp             w0, NULL
    // 0xc09260: b.ne            #0xc0926c
    // 0xc09264: r0 = Null
    //     0xc09264: mov             x0, NULL
    // 0xc09268: b               #0xc09278
    // 0xc0926c: LoadField: r1 = r0->field_f
    //     0xc0926c: ldur            w1, [x0, #0xf]
    // 0xc09270: DecompressPointer r1
    //     0xc09270: add             x1, x1, HEAP, lsl #32
    // 0xc09274: mov             x0, x1
    // 0xc09278: cmp             w0, NULL
    // 0xc0927c: b.ne            #0xc09288
    // 0xc09280: d1 = 0.000000
    //     0xc09280: eor             v1.16b, v1.16b, v1.16b
    // 0xc09284: b               #0xc09290
    // 0xc09288: LoadField: d0 = r0->field_7
    //     0xc09288: ldur            d0, [x0, #7]
    // 0xc0928c: mov             v1.16b, v0.16b
    // 0xc09290: d0 = 2.000000
    //     0xc09290: fmov            d0, #2.00000000
    // 0xc09294: fcmp            d1, d0
    // 0xc09298: b.lt            #0xc092a8
    // 0xc0929c: r0 = Instance_Color
    //     0xc0929c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xc092a0: ldr             x0, [x0, #0x860]
    // 0xc092a4: b               #0xc092b0
    // 0xc092a8: r0 = Instance_Color
    //     0xc092a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xc092ac: ldr             x0, [x0, #0x50]
    // 0xc092b0: mov             x2, x0
    // 0xc092b4: ldur            x1, [fp, #-8]
    // 0xc092b8: ldur            x0, [fp, #-0x28]
    // 0xc092bc: stur            x2, [fp, #-0x30]
    // 0xc092c0: r0 = ColorFilter()
    //     0xc092c0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xc092c4: mov             x1, x0
    // 0xc092c8: ldur            x0, [fp, #-0x30]
    // 0xc092cc: stur            x1, [fp, #-0x38]
    // 0xc092d0: StoreField: r1->field_7 = r0
    //     0xc092d0: stur            w0, [x1, #7]
    // 0xc092d4: r0 = Instance_BlendMode
    //     0xc092d4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xc092d8: ldr             x0, [x0, #0xb30]
    // 0xc092dc: StoreField: r1->field_b = r0
    //     0xc092dc: stur            w0, [x1, #0xb]
    // 0xc092e0: r0 = 1
    //     0xc092e0: movz            x0, #0x1
    // 0xc092e4: StoreField: r1->field_13 = r0
    //     0xc092e4: stur            x0, [x1, #0x13]
    // 0xc092e8: r0 = SvgPicture()
    //     0xc092e8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc092ec: stur            x0, [fp, #-0x30]
    // 0xc092f0: ldur            x16, [fp, #-0x38]
    // 0xc092f4: str             x16, [SP]
    // 0xc092f8: mov             x1, x0
    // 0xc092fc: r2 = "assets/images/big_green_star.svg"
    //     0xc092fc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f868] "assets/images/big_green_star.svg"
    //     0xc09300: ldr             x2, [x2, #0x868]
    // 0xc09304: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xc09304: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xc09308: ldr             x4, [x4, #0xa38]
    // 0xc0930c: r0 = SvgPicture.asset()
    //     0xc0930c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc09310: r1 = Null
    //     0xc09310: mov             x1, NULL
    // 0xc09314: r2 = 4
    //     0xc09314: movz            x2, #0x4
    // 0xc09318: r0 = AllocateArray()
    //     0xc09318: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0931c: mov             x2, x0
    // 0xc09320: ldur            x0, [fp, #-0x28]
    // 0xc09324: stur            x2, [fp, #-0x38]
    // 0xc09328: StoreField: r2->field_f = r0
    //     0xc09328: stur            w0, [x2, #0xf]
    // 0xc0932c: ldur            x0, [fp, #-0x30]
    // 0xc09330: StoreField: r2->field_13 = r0
    //     0xc09330: stur            w0, [x2, #0x13]
    // 0xc09334: r1 = <Widget>
    //     0xc09334: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc09338: r0 = AllocateGrowableArray()
    //     0xc09338: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0933c: mov             x1, x0
    // 0xc09340: ldur            x0, [fp, #-0x38]
    // 0xc09344: stur            x1, [fp, #-0x28]
    // 0xc09348: StoreField: r1->field_f = r0
    //     0xc09348: stur            w0, [x1, #0xf]
    // 0xc0934c: r2 = 4
    //     0xc0934c: movz            x2, #0x4
    // 0xc09350: StoreField: r1->field_b = r2
    //     0xc09350: stur            w2, [x1, #0xb]
    // 0xc09354: r0 = Row()
    //     0xc09354: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc09358: mov             x3, x0
    // 0xc0935c: r0 = Instance_Axis
    //     0xc0935c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc09360: stur            x3, [fp, #-0x30]
    // 0xc09364: StoreField: r3->field_f = r0
    //     0xc09364: stur            w0, [x3, #0xf]
    // 0xc09368: r4 = Instance_MainAxisAlignment
    //     0xc09368: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0936c: ldr             x4, [x4, #0xa08]
    // 0xc09370: StoreField: r3->field_13 = r4
    //     0xc09370: stur            w4, [x3, #0x13]
    // 0xc09374: r5 = Instance_MainAxisSize
    //     0xc09374: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc09378: ldr             x5, [x5, #0xa10]
    // 0xc0937c: ArrayStore: r3[0] = r5  ; List_4
    //     0xc0937c: stur            w5, [x3, #0x17]
    // 0xc09380: r6 = Instance_CrossAxisAlignment
    //     0xc09380: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc09384: ldr             x6, [x6, #0xa18]
    // 0xc09388: StoreField: r3->field_1b = r6
    //     0xc09388: stur            w6, [x3, #0x1b]
    // 0xc0938c: r7 = Instance_VerticalDirection
    //     0xc0938c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc09390: ldr             x7, [x7, #0xa20]
    // 0xc09394: StoreField: r3->field_23 = r7
    //     0xc09394: stur            w7, [x3, #0x23]
    // 0xc09398: r8 = Instance_Clip
    //     0xc09398: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0939c: ldr             x8, [x8, #0x38]
    // 0xc093a0: StoreField: r3->field_2b = r8
    //     0xc093a0: stur            w8, [x3, #0x2b]
    // 0xc093a4: StoreField: r3->field_2f = rZR
    //     0xc093a4: stur            xzr, [x3, #0x2f]
    // 0xc093a8: ldur            x1, [fp, #-0x28]
    // 0xc093ac: StoreField: r3->field_b = r1
    //     0xc093ac: stur            w1, [x3, #0xb]
    // 0xc093b0: r1 = Null
    //     0xc093b0: mov             x1, NULL
    // 0xc093b4: r2 = 2
    //     0xc093b4: movz            x2, #0x2
    // 0xc093b8: r0 = AllocateArray()
    //     0xc093b8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc093bc: mov             x2, x0
    // 0xc093c0: ldur            x0, [fp, #-0x30]
    // 0xc093c4: stur            x2, [fp, #-0x28]
    // 0xc093c8: StoreField: r2->field_f = r0
    //     0xc093c8: stur            w0, [x2, #0xf]
    // 0xc093cc: r1 = <Widget>
    //     0xc093cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc093d0: r0 = AllocateGrowableArray()
    //     0xc093d0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc093d4: mov             x3, x0
    // 0xc093d8: ldur            x0, [fp, #-0x28]
    // 0xc093dc: stur            x3, [fp, #-0x30]
    // 0xc093e0: StoreField: r3->field_f = r0
    //     0xc093e0: stur            w0, [x3, #0xf]
    // 0xc093e4: r0 = 2
    //     0xc093e4: movz            x0, #0x2
    // 0xc093e8: StoreField: r3->field_b = r0
    //     0xc093e8: stur            w0, [x3, #0xb]
    // 0xc093ec: ldur            x0, [fp, #-8]
    // 0xc093f0: LoadField: r1 = r0->field_b
    //     0xc093f0: ldur            w1, [x0, #0xb]
    // 0xc093f4: DecompressPointer r1
    //     0xc093f4: add             x1, x1, HEAP, lsl #32
    // 0xc093f8: cmp             w1, NULL
    // 0xc093fc: b.eq            #0xc0b6d8
    // 0xc09400: LoadField: r2 = r1->field_f
    //     0xc09400: ldur            w2, [x1, #0xf]
    // 0xc09404: DecompressPointer r2
    //     0xc09404: add             x2, x2, HEAP, lsl #32
    // 0xc09408: LoadField: r1 = r2->field_b
    //     0xc09408: ldur            w1, [x2, #0xb]
    // 0xc0940c: DecompressPointer r1
    //     0xc0940c: add             x1, x1, HEAP, lsl #32
    // 0xc09410: cmp             w1, NULL
    // 0xc09414: b.eq            #0xc09434
    // 0xc09418: LoadField: r2 = r1->field_f
    //     0xc09418: ldur            w2, [x1, #0xf]
    // 0xc0941c: DecompressPointer r2
    //     0xc0941c: add             x2, x2, HEAP, lsl #32
    // 0xc09420: cmp             w2, NULL
    // 0xc09424: b.eq            #0xc09434
    // 0xc09428: LoadField: r4 = r2->field_13
    //     0xc09428: ldur            w4, [x2, #0x13]
    // 0xc0942c: DecompressPointer r4
    //     0xc0942c: add             x4, x4, HEAP, lsl #32
    // 0xc09430: cbz             w4, #0xc09594
    // 0xc09434: cmp             w1, NULL
    // 0xc09438: b.ne            #0xc09444
    // 0xc0943c: r1 = Null
    //     0xc0943c: mov             x1, NULL
    // 0xc09440: b               #0xc09464
    // 0xc09444: LoadField: r2 = r1->field_f
    //     0xc09444: ldur            w2, [x1, #0xf]
    // 0xc09448: DecompressPointer r2
    //     0xc09448: add             x2, x2, HEAP, lsl #32
    // 0xc0944c: cmp             w2, NULL
    // 0xc09450: b.ne            #0xc0945c
    // 0xc09454: r1 = Null
    //     0xc09454: mov             x1, NULL
    // 0xc09458: b               #0xc09464
    // 0xc0945c: LoadField: r1 = r2->field_13
    //     0xc0945c: ldur            w1, [x2, #0x13]
    // 0xc09460: DecompressPointer r1
    //     0xc09460: add             x1, x1, HEAP, lsl #32
    // 0xc09464: cmp             w1, NULL
    // 0xc09468: b.ne            #0xc09474
    // 0xc0946c: r5 = ""
    //     0xc0946c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc09470: b               #0xc09478
    // 0xc09474: mov             x5, x1
    // 0xc09478: ldur            x4, [fp, #-0x18]
    // 0xc0947c: stur            x5, [fp, #-0x28]
    // 0xc09480: r1 = Null
    //     0xc09480: mov             x1, NULL
    // 0xc09484: r2 = 4
    //     0xc09484: movz            x2, #0x4
    // 0xc09488: r0 = AllocateArray()
    //     0xc09488: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0948c: mov             x1, x0
    // 0xc09490: ldur            x0, [fp, #-0x28]
    // 0xc09494: StoreField: r1->field_f = r0
    //     0xc09494: stur            w0, [x1, #0xf]
    // 0xc09498: r16 = " Ratings"
    //     0xc09498: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f870] " Ratings"
    //     0xc0949c: ldr             x16, [x16, #0x870]
    // 0xc094a0: StoreField: r1->field_13 = r16
    //     0xc094a0: stur            w16, [x1, #0x13]
    // 0xc094a4: str             x1, [SP]
    // 0xc094a8: r0 = _interpolate()
    //     0xc094a8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc094ac: ldur            x2, [fp, #-0x18]
    // 0xc094b0: stur            x0, [fp, #-0x28]
    // 0xc094b4: LoadField: r1 = r2->field_13
    //     0xc094b4: ldur            w1, [x2, #0x13]
    // 0xc094b8: DecompressPointer r1
    //     0xc094b8: add             x1, x1, HEAP, lsl #32
    // 0xc094bc: r0 = of()
    //     0xc094bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc094c0: LoadField: r1 = r0->field_87
    //     0xc094c0: ldur            w1, [x0, #0x87]
    // 0xc094c4: DecompressPointer r1
    //     0xc094c4: add             x1, x1, HEAP, lsl #32
    // 0xc094c8: LoadField: r0 = r1->field_2b
    //     0xc094c8: ldur            w0, [x1, #0x2b]
    // 0xc094cc: DecompressPointer r0
    //     0xc094cc: add             x0, x0, HEAP, lsl #32
    // 0xc094d0: stur            x0, [fp, #-0x38]
    // 0xc094d4: r1 = Instance_Color
    //     0xc094d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc094d8: d0 = 0.850000
    //     0xc094d8: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xc094dc: ldr             d0, [x17, #0x878]
    // 0xc094e0: r0 = withOpacity()
    //     0xc094e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc094e4: r16 = 10.000000
    //     0xc094e4: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xc094e8: stp             x0, x16, [SP]
    // 0xc094ec: ldur            x1, [fp, #-0x38]
    // 0xc094f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc094f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc094f4: ldr             x4, [x4, #0xaa0]
    // 0xc094f8: r0 = copyWith()
    //     0xc094f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc094fc: stur            x0, [fp, #-0x38]
    // 0xc09500: r0 = Text()
    //     0xc09500: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc09504: mov             x2, x0
    // 0xc09508: ldur            x0, [fp, #-0x28]
    // 0xc0950c: stur            x2, [fp, #-0x48]
    // 0xc09510: StoreField: r2->field_b = r0
    //     0xc09510: stur            w0, [x2, #0xb]
    // 0xc09514: ldur            x0, [fp, #-0x38]
    // 0xc09518: StoreField: r2->field_13 = r0
    //     0xc09518: stur            w0, [x2, #0x13]
    // 0xc0951c: ldur            x0, [fp, #-0x30]
    // 0xc09520: LoadField: r1 = r0->field_b
    //     0xc09520: ldur            w1, [x0, #0xb]
    // 0xc09524: LoadField: r3 = r0->field_f
    //     0xc09524: ldur            w3, [x0, #0xf]
    // 0xc09528: DecompressPointer r3
    //     0xc09528: add             x3, x3, HEAP, lsl #32
    // 0xc0952c: LoadField: r4 = r3->field_b
    //     0xc0952c: ldur            w4, [x3, #0xb]
    // 0xc09530: r3 = LoadInt32Instr(r1)
    //     0xc09530: sbfx            x3, x1, #1, #0x1f
    // 0xc09534: stur            x3, [fp, #-0x40]
    // 0xc09538: r1 = LoadInt32Instr(r4)
    //     0xc09538: sbfx            x1, x4, #1, #0x1f
    // 0xc0953c: cmp             x3, x1
    // 0xc09540: b.ne            #0xc0954c
    // 0xc09544: mov             x1, x0
    // 0xc09548: r0 = _growToNextCapacity()
    //     0xc09548: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0954c: ldur            x3, [fp, #-0x30]
    // 0xc09550: ldur            x2, [fp, #-0x40]
    // 0xc09554: add             x0, x2, #1
    // 0xc09558: lsl             x1, x0, #1
    // 0xc0955c: StoreField: r3->field_b = r1
    //     0xc0955c: stur            w1, [x3, #0xb]
    // 0xc09560: LoadField: r1 = r3->field_f
    //     0xc09560: ldur            w1, [x3, #0xf]
    // 0xc09564: DecompressPointer r1
    //     0xc09564: add             x1, x1, HEAP, lsl #32
    // 0xc09568: ldur            x0, [fp, #-0x48]
    // 0xc0956c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc0956c: add             x25, x1, x2, lsl #2
    //     0xc09570: add             x25, x25, #0xf
    //     0xc09574: str             w0, [x25]
    //     0xc09578: tbz             w0, #0, #0xc09594
    //     0xc0957c: ldurb           w16, [x1, #-1]
    //     0xc09580: ldurb           w17, [x0, #-1]
    //     0xc09584: and             x16, x17, x16, lsr #2
    //     0xc09588: tst             x16, HEAP, lsr #32
    //     0xc0958c: b.eq            #0xc09594
    //     0xc09590: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc09594: ldur            x0, [fp, #-8]
    // 0xc09598: LoadField: r1 = r0->field_b
    //     0xc09598: ldur            w1, [x0, #0xb]
    // 0xc0959c: DecompressPointer r1
    //     0xc0959c: add             x1, x1, HEAP, lsl #32
    // 0xc095a0: cmp             w1, NULL
    // 0xc095a4: b.eq            #0xc0b6dc
    // 0xc095a8: LoadField: r2 = r1->field_f
    //     0xc095a8: ldur            w2, [x1, #0xf]
    // 0xc095ac: DecompressPointer r2
    //     0xc095ac: add             x2, x2, HEAP, lsl #32
    // 0xc095b0: LoadField: r4 = r2->field_b
    //     0xc095b0: ldur            w4, [x2, #0xb]
    // 0xc095b4: DecompressPointer r4
    //     0xc095b4: add             x4, x4, HEAP, lsl #32
    // 0xc095b8: stur            x4, [fp, #-0x28]
    // 0xc095bc: cmp             w4, NULL
    // 0xc095c0: b.eq            #0xc095e0
    // 0xc095c4: LoadField: r1 = r4->field_f
    //     0xc095c4: ldur            w1, [x4, #0xf]
    // 0xc095c8: DecompressPointer r1
    //     0xc095c8: add             x1, x1, HEAP, lsl #32
    // 0xc095cc: cmp             w1, NULL
    // 0xc095d0: b.eq            #0xc095e0
    // 0xc095d4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc095d4: ldur            w2, [x1, #0x17]
    // 0xc095d8: DecompressPointer r2
    //     0xc095d8: add             x2, x2, HEAP, lsl #32
    // 0xc095dc: cbz             w2, #0xc0974c
    // 0xc095e0: r1 = Null
    //     0xc095e0: mov             x1, NULL
    // 0xc095e4: r2 = 6
    //     0xc095e4: movz            x2, #0x6
    // 0xc095e8: r0 = AllocateArray()
    //     0xc095e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc095ec: r16 = "& "
    //     0xc095ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f880] "& "
    //     0xc095f0: ldr             x16, [x16, #0x880]
    // 0xc095f4: StoreField: r0->field_f = r16
    //     0xc095f4: stur            w16, [x0, #0xf]
    // 0xc095f8: ldur            x1, [fp, #-0x28]
    // 0xc095fc: cmp             w1, NULL
    // 0xc09600: b.ne            #0xc0960c
    // 0xc09604: r1 = Null
    //     0xc09604: mov             x1, NULL
    // 0xc09608: b               #0xc0962c
    // 0xc0960c: LoadField: r2 = r1->field_f
    //     0xc0960c: ldur            w2, [x1, #0xf]
    // 0xc09610: DecompressPointer r2
    //     0xc09610: add             x2, x2, HEAP, lsl #32
    // 0xc09614: cmp             w2, NULL
    // 0xc09618: b.ne            #0xc09624
    // 0xc0961c: r1 = Null
    //     0xc0961c: mov             x1, NULL
    // 0xc09620: b               #0xc0962c
    // 0xc09624: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xc09624: ldur            w1, [x2, #0x17]
    // 0xc09628: DecompressPointer r1
    //     0xc09628: add             x1, x1, HEAP, lsl #32
    // 0xc0962c: cmp             w1, NULL
    // 0xc09630: b.ne            #0xc0963c
    // 0xc09634: r3 = ""
    //     0xc09634: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc09638: b               #0xc09640
    // 0xc0963c: mov             x3, x1
    // 0xc09640: ldur            x2, [fp, #-0x18]
    // 0xc09644: ldur            x1, [fp, #-0x30]
    // 0xc09648: StoreField: r0->field_13 = r3
    //     0xc09648: stur            w3, [x0, #0x13]
    // 0xc0964c: r16 = " Reviews"
    //     0xc0964c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f888] " Reviews"
    //     0xc09650: ldr             x16, [x16, #0x888]
    // 0xc09654: ArrayStore: r0[0] = r16  ; List_4
    //     0xc09654: stur            w16, [x0, #0x17]
    // 0xc09658: str             x0, [SP]
    // 0xc0965c: r0 = _interpolate()
    //     0xc0965c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc09660: ldur            x2, [fp, #-0x18]
    // 0xc09664: stur            x0, [fp, #-0x28]
    // 0xc09668: LoadField: r1 = r2->field_13
    //     0xc09668: ldur            w1, [x2, #0x13]
    // 0xc0966c: DecompressPointer r1
    //     0xc0966c: add             x1, x1, HEAP, lsl #32
    // 0xc09670: r0 = of()
    //     0xc09670: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc09674: LoadField: r1 = r0->field_87
    //     0xc09674: ldur            w1, [x0, #0x87]
    // 0xc09678: DecompressPointer r1
    //     0xc09678: add             x1, x1, HEAP, lsl #32
    // 0xc0967c: LoadField: r0 = r1->field_2b
    //     0xc0967c: ldur            w0, [x1, #0x2b]
    // 0xc09680: DecompressPointer r0
    //     0xc09680: add             x0, x0, HEAP, lsl #32
    // 0xc09684: stur            x0, [fp, #-0x38]
    // 0xc09688: r1 = Instance_Color
    //     0xc09688: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0968c: d0 = 0.850000
    //     0xc0968c: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xc09690: ldr             d0, [x17, #0x878]
    // 0xc09694: r0 = withOpacity()
    //     0xc09694: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc09698: r16 = 10.000000
    //     0xc09698: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xc0969c: stp             x0, x16, [SP]
    // 0xc096a0: ldur            x1, [fp, #-0x38]
    // 0xc096a4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc096a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc096a8: ldr             x4, [x4, #0xaa0]
    // 0xc096ac: r0 = copyWith()
    //     0xc096ac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc096b0: stur            x0, [fp, #-0x38]
    // 0xc096b4: r0 = Text()
    //     0xc096b4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc096b8: mov             x2, x0
    // 0xc096bc: ldur            x0, [fp, #-0x28]
    // 0xc096c0: stur            x2, [fp, #-0x48]
    // 0xc096c4: StoreField: r2->field_b = r0
    //     0xc096c4: stur            w0, [x2, #0xb]
    // 0xc096c8: ldur            x0, [fp, #-0x38]
    // 0xc096cc: StoreField: r2->field_13 = r0
    //     0xc096cc: stur            w0, [x2, #0x13]
    // 0xc096d0: ldur            x0, [fp, #-0x30]
    // 0xc096d4: LoadField: r1 = r0->field_b
    //     0xc096d4: ldur            w1, [x0, #0xb]
    // 0xc096d8: LoadField: r3 = r0->field_f
    //     0xc096d8: ldur            w3, [x0, #0xf]
    // 0xc096dc: DecompressPointer r3
    //     0xc096dc: add             x3, x3, HEAP, lsl #32
    // 0xc096e0: LoadField: r4 = r3->field_b
    //     0xc096e0: ldur            w4, [x3, #0xb]
    // 0xc096e4: r3 = LoadInt32Instr(r1)
    //     0xc096e4: sbfx            x3, x1, #1, #0x1f
    // 0xc096e8: stur            x3, [fp, #-0x40]
    // 0xc096ec: r1 = LoadInt32Instr(r4)
    //     0xc096ec: sbfx            x1, x4, #1, #0x1f
    // 0xc096f0: cmp             x3, x1
    // 0xc096f4: b.ne            #0xc09700
    // 0xc096f8: mov             x1, x0
    // 0xc096fc: r0 = _growToNextCapacity()
    //     0xc096fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc09700: ldur            x2, [fp, #-0x30]
    // 0xc09704: ldur            x3, [fp, #-0x40]
    // 0xc09708: add             x0, x3, #1
    // 0xc0970c: lsl             x1, x0, #1
    // 0xc09710: StoreField: r2->field_b = r1
    //     0xc09710: stur            w1, [x2, #0xb]
    // 0xc09714: LoadField: r1 = r2->field_f
    //     0xc09714: ldur            w1, [x2, #0xf]
    // 0xc09718: DecompressPointer r1
    //     0xc09718: add             x1, x1, HEAP, lsl #32
    // 0xc0971c: ldur            x0, [fp, #-0x48]
    // 0xc09720: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc09720: add             x25, x1, x3, lsl #2
    //     0xc09724: add             x25, x25, #0xf
    //     0xc09728: str             w0, [x25]
    //     0xc0972c: tbz             w0, #0, #0xc09748
    //     0xc09730: ldurb           w16, [x1, #-1]
    //     0xc09734: ldurb           w17, [x0, #-1]
    //     0xc09738: and             x16, x17, x16, lsr #2
    //     0xc0973c: tst             x16, HEAP, lsr #32
    //     0xc09740: b.eq            #0xc09748
    //     0xc09744: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc09748: b               #0xc09750
    // 0xc0974c: mov             x2, x3
    // 0xc09750: ldur            x1, [fp, #-8]
    // 0xc09754: LoadField: r0 = r1->field_b
    //     0xc09754: ldur            w0, [x1, #0xb]
    // 0xc09758: DecompressPointer r0
    //     0xc09758: add             x0, x0, HEAP, lsl #32
    // 0xc0975c: cmp             w0, NULL
    // 0xc09760: b.eq            #0xc0b6e0
    // 0xc09764: LoadField: r3 = r0->field_f
    //     0xc09764: ldur            w3, [x0, #0xf]
    // 0xc09768: DecompressPointer r3
    //     0xc09768: add             x3, x3, HEAP, lsl #32
    // 0xc0976c: LoadField: r0 = r3->field_b
    //     0xc0976c: ldur            w0, [x3, #0xb]
    // 0xc09770: DecompressPointer r0
    //     0xc09770: add             x0, x0, HEAP, lsl #32
    // 0xc09774: cmp             w0, NULL
    // 0xc09778: b.ne            #0xc09784
    // 0xc0977c: r0 = Null
    //     0xc0977c: mov             x0, NULL
    // 0xc09780: b               #0xc097a4
    // 0xc09784: LoadField: r3 = r0->field_f
    //     0xc09784: ldur            w3, [x0, #0xf]
    // 0xc09788: DecompressPointer r3
    //     0xc09788: add             x3, x3, HEAP, lsl #32
    // 0xc0978c: cmp             w3, NULL
    // 0xc09790: b.ne            #0xc0979c
    // 0xc09794: r0 = Null
    //     0xc09794: mov             x0, NULL
    // 0xc09798: b               #0xc097a4
    // 0xc0979c: LoadField: r0 = r3->field_b
    //     0xc0979c: ldur            w0, [x3, #0xb]
    // 0xc097a0: DecompressPointer r0
    //     0xc097a0: add             x0, x0, HEAP, lsl #32
    // 0xc097a4: cmp             w0, NULL
    // 0xc097a8: b.ne            #0xc097b0
    // 0xc097ac: r0 = false
    //     0xc097ac: add             x0, NULL, #0x30  ; false
    // 0xc097b0: stur            x0, [fp, #-0x28]
    // 0xc097b4: r0 = SvgPicture()
    //     0xc097b4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc097b8: mov             x1, x0
    // 0xc097bc: r2 = "assets/images/shopdeck-tag.svg"
    //     0xc097bc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fd38] "assets/images/shopdeck-tag.svg"
    //     0xc097c0: ldr             x2, [x2, #0xd38]
    // 0xc097c4: stur            x0, [fp, #-0x38]
    // 0xc097c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc097c8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc097cc: r0 = SvgPicture.asset()
    //     0xc097cc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc097d0: r0 = InkWell()
    //     0xc097d0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc097d4: mov             x3, x0
    // 0xc097d8: ldur            x0, [fp, #-0x38]
    // 0xc097dc: stur            x3, [fp, #-0x48]
    // 0xc097e0: StoreField: r3->field_b = r0
    //     0xc097e0: stur            w0, [x3, #0xb]
    // 0xc097e4: ldur            x2, [fp, #-0x18]
    // 0xc097e8: r1 = Function '<anonymous closure>':.
    //     0xc097e8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52438] AnonymousClosure: (0xc0b91c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xc097ec: ldr             x1, [x1, #0x438]
    // 0xc097f0: r0 = AllocateClosure()
    //     0xc097f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc097f4: mov             x1, x0
    // 0xc097f8: ldur            x0, [fp, #-0x48]
    // 0xc097fc: StoreField: r0->field_f = r1
    //     0xc097fc: stur            w1, [x0, #0xf]
    // 0xc09800: r1 = true
    //     0xc09800: add             x1, NULL, #0x20  ; true
    // 0xc09804: StoreField: r0->field_43 = r1
    //     0xc09804: stur            w1, [x0, #0x43]
    // 0xc09808: r2 = Instance_BoxShape
    //     0xc09808: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0980c: ldr             x2, [x2, #0x80]
    // 0xc09810: StoreField: r0->field_47 = r2
    //     0xc09810: stur            w2, [x0, #0x47]
    // 0xc09814: StoreField: r0->field_6f = r1
    //     0xc09814: stur            w1, [x0, #0x6f]
    // 0xc09818: r3 = false
    //     0xc09818: add             x3, NULL, #0x30  ; false
    // 0xc0981c: StoreField: r0->field_73 = r3
    //     0xc0981c: stur            w3, [x0, #0x73]
    // 0xc09820: StoreField: r0->field_83 = r1
    //     0xc09820: stur            w1, [x0, #0x83]
    // 0xc09824: StoreField: r0->field_7b = r3
    //     0xc09824: stur            w3, [x0, #0x7b]
    // 0xc09828: r0 = Padding()
    //     0xc09828: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0982c: mov             x1, x0
    // 0xc09830: r0 = Instance_EdgeInsets
    //     0xc09830: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0xc09834: ldr             x0, [x0, #0x990]
    // 0xc09838: stur            x1, [fp, #-0x38]
    // 0xc0983c: StoreField: r1->field_f = r0
    //     0xc0983c: stur            w0, [x1, #0xf]
    // 0xc09840: ldur            x0, [fp, #-0x48]
    // 0xc09844: StoreField: r1->field_b = r0
    //     0xc09844: stur            w0, [x1, #0xb]
    // 0xc09848: r0 = Visibility()
    //     0xc09848: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0984c: mov             x2, x0
    // 0xc09850: ldur            x0, [fp, #-0x38]
    // 0xc09854: stur            x2, [fp, #-0x48]
    // 0xc09858: StoreField: r2->field_b = r0
    //     0xc09858: stur            w0, [x2, #0xb]
    // 0xc0985c: r0 = Instance_SizedBox
    //     0xc0985c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc09860: StoreField: r2->field_f = r0
    //     0xc09860: stur            w0, [x2, #0xf]
    // 0xc09864: ldur            x1, [fp, #-0x28]
    // 0xc09868: StoreField: r2->field_13 = r1
    //     0xc09868: stur            w1, [x2, #0x13]
    // 0xc0986c: r3 = false
    //     0xc0986c: add             x3, NULL, #0x30  ; false
    // 0xc09870: ArrayStore: r2[0] = r3  ; List_4
    //     0xc09870: stur            w3, [x2, #0x17]
    // 0xc09874: StoreField: r2->field_1b = r3
    //     0xc09874: stur            w3, [x2, #0x1b]
    // 0xc09878: StoreField: r2->field_1f = r3
    //     0xc09878: stur            w3, [x2, #0x1f]
    // 0xc0987c: StoreField: r2->field_23 = r3
    //     0xc0987c: stur            w3, [x2, #0x23]
    // 0xc09880: StoreField: r2->field_27 = r3
    //     0xc09880: stur            w3, [x2, #0x27]
    // 0xc09884: StoreField: r2->field_2b = r3
    //     0xc09884: stur            w3, [x2, #0x2b]
    // 0xc09888: ldur            x4, [fp, #-0x30]
    // 0xc0988c: LoadField: r1 = r4->field_b
    //     0xc0988c: ldur            w1, [x4, #0xb]
    // 0xc09890: LoadField: r5 = r4->field_f
    //     0xc09890: ldur            w5, [x4, #0xf]
    // 0xc09894: DecompressPointer r5
    //     0xc09894: add             x5, x5, HEAP, lsl #32
    // 0xc09898: LoadField: r6 = r5->field_b
    //     0xc09898: ldur            w6, [x5, #0xb]
    // 0xc0989c: r5 = LoadInt32Instr(r1)
    //     0xc0989c: sbfx            x5, x1, #1, #0x1f
    // 0xc098a0: stur            x5, [fp, #-0x40]
    // 0xc098a4: r1 = LoadInt32Instr(r6)
    //     0xc098a4: sbfx            x1, x6, #1, #0x1f
    // 0xc098a8: cmp             x5, x1
    // 0xc098ac: b.ne            #0xc098b8
    // 0xc098b0: mov             x1, x4
    // 0xc098b4: r0 = _growToNextCapacity()
    //     0xc098b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc098b8: ldur            x4, [fp, #-8]
    // 0xc098bc: ldur            x5, [fp, #-0x18]
    // 0xc098c0: ldur            x2, [fp, #-0x30]
    // 0xc098c4: ldur            x3, [fp, #-0x40]
    // 0xc098c8: add             x0, x3, #1
    // 0xc098cc: lsl             x1, x0, #1
    // 0xc098d0: StoreField: r2->field_b = r1
    //     0xc098d0: stur            w1, [x2, #0xb]
    // 0xc098d4: LoadField: r1 = r2->field_f
    //     0xc098d4: ldur            w1, [x2, #0xf]
    // 0xc098d8: DecompressPointer r1
    //     0xc098d8: add             x1, x1, HEAP, lsl #32
    // 0xc098dc: ldur            x0, [fp, #-0x48]
    // 0xc098e0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc098e0: add             x25, x1, x3, lsl #2
    //     0xc098e4: add             x25, x25, #0xf
    //     0xc098e8: str             w0, [x25]
    //     0xc098ec: tbz             w0, #0, #0xc09908
    //     0xc098f0: ldurb           w16, [x1, #-1]
    //     0xc098f4: ldurb           w17, [x0, #-1]
    //     0xc098f8: and             x16, x17, x16, lsr #2
    //     0xc098fc: tst             x16, HEAP, lsr #32
    //     0xc09900: b.eq            #0xc09908
    //     0xc09904: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc09908: r0 = Column()
    //     0xc09908: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0990c: mov             x4, x0
    // 0xc09910: r0 = Instance_Axis
    //     0xc09910: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc09914: stur            x4, [fp, #-0x28]
    // 0xc09918: StoreField: r4->field_f = r0
    //     0xc09918: stur            w0, [x4, #0xf]
    // 0xc0991c: r6 = Instance_MainAxisAlignment
    //     0xc0991c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc09920: ldr             x6, [x6, #0xa08]
    // 0xc09924: StoreField: r4->field_13 = r6
    //     0xc09924: stur            w6, [x4, #0x13]
    // 0xc09928: r7 = Instance_MainAxisSize
    //     0xc09928: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0992c: ldr             x7, [x7, #0xa10]
    // 0xc09930: ArrayStore: r4[0] = r7  ; List_4
    //     0xc09930: stur            w7, [x4, #0x17]
    // 0xc09934: r8 = Instance_CrossAxisAlignment
    //     0xc09934: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc09938: ldr             x8, [x8, #0x890]
    // 0xc0993c: StoreField: r4->field_1b = r8
    //     0xc0993c: stur            w8, [x4, #0x1b]
    // 0xc09940: r9 = Instance_VerticalDirection
    //     0xc09940: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc09944: ldr             x9, [x9, #0xa20]
    // 0xc09948: StoreField: r4->field_23 = r9
    //     0xc09948: stur            w9, [x4, #0x23]
    // 0xc0994c: r10 = Instance_Clip
    //     0xc0994c: add             x10, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc09950: ldr             x10, [x10, #0x38]
    // 0xc09954: StoreField: r4->field_2b = r10
    //     0xc09954: stur            w10, [x4, #0x2b]
    // 0xc09958: StoreField: r4->field_2f = rZR
    //     0xc09958: stur            xzr, [x4, #0x2f]
    // 0xc0995c: ldur            x1, [fp, #-0x30]
    // 0xc09960: StoreField: r4->field_b = r1
    //     0xc09960: stur            w1, [x4, #0xb]
    // 0xc09964: ldur            x11, [fp, #-0x18]
    // 0xc09968: LoadField: r2 = r11->field_13
    //     0xc09968: ldur            w2, [x11, #0x13]
    // 0xc0996c: DecompressPointer r2
    //     0xc0996c: add             x2, x2, HEAP, lsl #32
    // 0xc09970: ldur            x12, [fp, #-8]
    // 0xc09974: LoadField: r1 = r12->field_b
    //     0xc09974: ldur            w1, [x12, #0xb]
    // 0xc09978: DecompressPointer r1
    //     0xc09978: add             x1, x1, HEAP, lsl #32
    // 0xc0997c: cmp             w1, NULL
    // 0xc09980: b.eq            #0xc0b6e4
    // 0xc09984: LoadField: r3 = r1->field_f
    //     0xc09984: ldur            w3, [x1, #0xf]
    // 0xc09988: DecompressPointer r3
    //     0xc09988: add             x3, x3, HEAP, lsl #32
    // 0xc0998c: LoadField: r1 = r3->field_b
    //     0xc0998c: ldur            w1, [x3, #0xb]
    // 0xc09990: DecompressPointer r1
    //     0xc09990: add             x1, x1, HEAP, lsl #32
    // 0xc09994: cmp             w1, NULL
    // 0xc09998: b.ne            #0xc099a4
    // 0xc0999c: r3 = Null
    //     0xc0999c: mov             x3, NULL
    // 0xc099a0: b               #0xc099dc
    // 0xc099a4: LoadField: r3 = r1->field_f
    //     0xc099a4: ldur            w3, [x1, #0xf]
    // 0xc099a8: DecompressPointer r3
    //     0xc099a8: add             x3, x3, HEAP, lsl #32
    // 0xc099ac: cmp             w3, NULL
    // 0xc099b0: b.ne            #0xc099bc
    // 0xc099b4: r3 = Null
    //     0xc099b4: mov             x3, NULL
    // 0xc099b8: b               #0xc099dc
    // 0xc099bc: LoadField: r5 = r3->field_1b
    //     0xc099bc: ldur            w5, [x3, #0x1b]
    // 0xc099c0: DecompressPointer r5
    //     0xc099c0: add             x5, x5, HEAP, lsl #32
    // 0xc099c4: cmp             w5, NULL
    // 0xc099c8: b.ne            #0xc099d4
    // 0xc099cc: r3 = Null
    //     0xc099cc: mov             x3, NULL
    // 0xc099d0: b               #0xc099dc
    // 0xc099d4: LoadField: r3 = r5->field_7
    //     0xc099d4: ldur            w3, [x5, #7]
    // 0xc099d8: DecompressPointer r3
    //     0xc099d8: add             x3, x3, HEAP, lsl #32
    // 0xc099dc: cmp             w3, NULL
    // 0xc099e0: b.ne            #0xc099ec
    // 0xc099e4: r3 = 0
    //     0xc099e4: movz            x3, #0
    // 0xc099e8: b               #0xc099fc
    // 0xc099ec: r5 = LoadInt32Instr(r3)
    //     0xc099ec: sbfx            x5, x3, #1, #0x1f
    //     0xc099f0: tbz             w3, #0, #0xc099f8
    //     0xc099f4: ldur            x5, [x3, #7]
    // 0xc099f8: mov             x3, x5
    // 0xc099fc: cmp             w1, NULL
    // 0xc09a00: b.ne            #0xc09a0c
    // 0xc09a04: r5 = Null
    //     0xc09a04: mov             x5, NULL
    // 0xc09a08: b               #0xc09a44
    // 0xc09a0c: LoadField: r5 = r1->field_f
    //     0xc09a0c: ldur            w5, [x1, #0xf]
    // 0xc09a10: DecompressPointer r5
    //     0xc09a10: add             x5, x5, HEAP, lsl #32
    // 0xc09a14: cmp             w5, NULL
    // 0xc09a18: b.ne            #0xc09a24
    // 0xc09a1c: r5 = Null
    //     0xc09a1c: mov             x5, NULL
    // 0xc09a20: b               #0xc09a44
    // 0xc09a24: LoadField: r13 = r5->field_1b
    //     0xc09a24: ldur            w13, [x5, #0x1b]
    // 0xc09a28: DecompressPointer r13
    //     0xc09a28: add             x13, x13, HEAP, lsl #32
    // 0xc09a2c: cmp             w13, NULL
    // 0xc09a30: b.ne            #0xc09a3c
    // 0xc09a34: r5 = Null
    //     0xc09a34: mov             x5, NULL
    // 0xc09a38: b               #0xc09a44
    // 0xc09a3c: LoadField: r5 = r13->field_7
    //     0xc09a3c: ldur            w5, [x13, #7]
    // 0xc09a40: DecompressPointer r5
    //     0xc09a40: add             x5, x5, HEAP, lsl #32
    // 0xc09a44: cmp             w5, NULL
    // 0xc09a48: b.ne            #0xc09a54
    // 0xc09a4c: r5 = 0
    //     0xc09a4c: movz            x5, #0
    // 0xc09a50: b               #0xc09a64
    // 0xc09a54: r13 = LoadInt32Instr(r5)
    //     0xc09a54: sbfx            x13, x5, #1, #0x1f
    //     0xc09a58: tbz             w5, #0, #0xc09a60
    //     0xc09a5c: ldur            x13, [x5, #7]
    // 0xc09a60: mov             x5, x13
    // 0xc09a64: cmp             w1, NULL
    // 0xc09a68: b.ne            #0xc09a74
    // 0xc09a6c: r13 = Null
    //     0xc09a6c: mov             x13, NULL
    // 0xc09a70: b               #0xc09aac
    // 0xc09a74: LoadField: r13 = r1->field_f
    //     0xc09a74: ldur            w13, [x1, #0xf]
    // 0xc09a78: DecompressPointer r13
    //     0xc09a78: add             x13, x13, HEAP, lsl #32
    // 0xc09a7c: cmp             w13, NULL
    // 0xc09a80: b.ne            #0xc09a8c
    // 0xc09a84: r13 = Null
    //     0xc09a84: mov             x13, NULL
    // 0xc09a88: b               #0xc09aac
    // 0xc09a8c: LoadField: r14 = r13->field_1b
    //     0xc09a8c: ldur            w14, [x13, #0x1b]
    // 0xc09a90: DecompressPointer r14
    //     0xc09a90: add             x14, x14, HEAP, lsl #32
    // 0xc09a94: cmp             w14, NULL
    // 0xc09a98: b.ne            #0xc09aa4
    // 0xc09a9c: r13 = Null
    //     0xc09a9c: mov             x13, NULL
    // 0xc09aa0: b               #0xc09aac
    // 0xc09aa4: LoadField: r13 = r14->field_b
    //     0xc09aa4: ldur            w13, [x14, #0xb]
    // 0xc09aa8: DecompressPointer r13
    //     0xc09aa8: add             x13, x13, HEAP, lsl #32
    // 0xc09aac: cmp             w13, NULL
    // 0xc09ab0: b.ne            #0xc09abc
    // 0xc09ab4: r13 = 0
    //     0xc09ab4: movz            x13, #0
    // 0xc09ab8: b               #0xc09acc
    // 0xc09abc: r14 = LoadInt32Instr(r13)
    //     0xc09abc: sbfx            x14, x13, #1, #0x1f
    //     0xc09ac0: tbz             w13, #0, #0xc09ac8
    //     0xc09ac4: ldur            x14, [x13, #7]
    // 0xc09ac8: mov             x13, x14
    // 0xc09acc: add             x14, x5, x13
    // 0xc09ad0: cmp             w1, NULL
    // 0xc09ad4: b.ne            #0xc09ae0
    // 0xc09ad8: r5 = Null
    //     0xc09ad8: mov             x5, NULL
    // 0xc09adc: b               #0xc09b18
    // 0xc09ae0: LoadField: r5 = r1->field_f
    //     0xc09ae0: ldur            w5, [x1, #0xf]
    // 0xc09ae4: DecompressPointer r5
    //     0xc09ae4: add             x5, x5, HEAP, lsl #32
    // 0xc09ae8: cmp             w5, NULL
    // 0xc09aec: b.ne            #0xc09af8
    // 0xc09af0: r5 = Null
    //     0xc09af0: mov             x5, NULL
    // 0xc09af4: b               #0xc09b18
    // 0xc09af8: LoadField: r13 = r5->field_1b
    //     0xc09af8: ldur            w13, [x5, #0x1b]
    // 0xc09afc: DecompressPointer r13
    //     0xc09afc: add             x13, x13, HEAP, lsl #32
    // 0xc09b00: cmp             w13, NULL
    // 0xc09b04: b.ne            #0xc09b10
    // 0xc09b08: r5 = Null
    //     0xc09b08: mov             x5, NULL
    // 0xc09b0c: b               #0xc09b18
    // 0xc09b10: LoadField: r5 = r13->field_f
    //     0xc09b10: ldur            w5, [x13, #0xf]
    // 0xc09b14: DecompressPointer r5
    //     0xc09b14: add             x5, x5, HEAP, lsl #32
    // 0xc09b18: cmp             w5, NULL
    // 0xc09b1c: b.ne            #0xc09b28
    // 0xc09b20: r5 = 0
    //     0xc09b20: movz            x5, #0
    // 0xc09b24: b               #0xc09b38
    // 0xc09b28: r13 = LoadInt32Instr(r5)
    //     0xc09b28: sbfx            x13, x5, #1, #0x1f
    //     0xc09b2c: tbz             w5, #0, #0xc09b34
    //     0xc09b30: ldur            x13, [x5, #7]
    // 0xc09b34: mov             x5, x13
    // 0xc09b38: add             x13, x14, x5
    // 0xc09b3c: cmp             w1, NULL
    // 0xc09b40: b.ne            #0xc09b4c
    // 0xc09b44: r5 = Null
    //     0xc09b44: mov             x5, NULL
    // 0xc09b48: b               #0xc09b84
    // 0xc09b4c: LoadField: r5 = r1->field_f
    //     0xc09b4c: ldur            w5, [x1, #0xf]
    // 0xc09b50: DecompressPointer r5
    //     0xc09b50: add             x5, x5, HEAP, lsl #32
    // 0xc09b54: cmp             w5, NULL
    // 0xc09b58: b.ne            #0xc09b64
    // 0xc09b5c: r5 = Null
    //     0xc09b5c: mov             x5, NULL
    // 0xc09b60: b               #0xc09b84
    // 0xc09b64: LoadField: r14 = r5->field_1b
    //     0xc09b64: ldur            w14, [x5, #0x1b]
    // 0xc09b68: DecompressPointer r14
    //     0xc09b68: add             x14, x14, HEAP, lsl #32
    // 0xc09b6c: cmp             w14, NULL
    // 0xc09b70: b.ne            #0xc09b7c
    // 0xc09b74: r5 = Null
    //     0xc09b74: mov             x5, NULL
    // 0xc09b78: b               #0xc09b84
    // 0xc09b7c: LoadField: r5 = r14->field_13
    //     0xc09b7c: ldur            w5, [x14, #0x13]
    // 0xc09b80: DecompressPointer r5
    //     0xc09b80: add             x5, x5, HEAP, lsl #32
    // 0xc09b84: cmp             w5, NULL
    // 0xc09b88: b.ne            #0xc09b94
    // 0xc09b8c: r5 = 0
    //     0xc09b8c: movz            x5, #0
    // 0xc09b90: b               #0xc09ba4
    // 0xc09b94: r14 = LoadInt32Instr(r5)
    //     0xc09b94: sbfx            x14, x5, #1, #0x1f
    //     0xc09b98: tbz             w5, #0, #0xc09ba0
    //     0xc09b9c: ldur            x14, [x5, #7]
    // 0xc09ba0: mov             x5, x14
    // 0xc09ba4: add             x14, x13, x5
    // 0xc09ba8: cmp             w1, NULL
    // 0xc09bac: b.ne            #0xc09bb8
    // 0xc09bb0: r5 = Null
    //     0xc09bb0: mov             x5, NULL
    // 0xc09bb4: b               #0xc09bf0
    // 0xc09bb8: LoadField: r5 = r1->field_f
    //     0xc09bb8: ldur            w5, [x1, #0xf]
    // 0xc09bbc: DecompressPointer r5
    //     0xc09bbc: add             x5, x5, HEAP, lsl #32
    // 0xc09bc0: cmp             w5, NULL
    // 0xc09bc4: b.ne            #0xc09bd0
    // 0xc09bc8: r5 = Null
    //     0xc09bc8: mov             x5, NULL
    // 0xc09bcc: b               #0xc09bf0
    // 0xc09bd0: LoadField: r13 = r5->field_1b
    //     0xc09bd0: ldur            w13, [x5, #0x1b]
    // 0xc09bd4: DecompressPointer r13
    //     0xc09bd4: add             x13, x13, HEAP, lsl #32
    // 0xc09bd8: cmp             w13, NULL
    // 0xc09bdc: b.ne            #0xc09be8
    // 0xc09be0: r5 = Null
    //     0xc09be0: mov             x5, NULL
    // 0xc09be4: b               #0xc09bf0
    // 0xc09be8: ArrayLoad: r5 = r13[0]  ; List_4
    //     0xc09be8: ldur            w5, [x13, #0x17]
    // 0xc09bec: DecompressPointer r5
    //     0xc09bec: add             x5, x5, HEAP, lsl #32
    // 0xc09bf0: cmp             w5, NULL
    // 0xc09bf4: b.ne            #0xc09c00
    // 0xc09bf8: r5 = 0
    //     0xc09bf8: movz            x5, #0
    // 0xc09bfc: b               #0xc09c10
    // 0xc09c00: r13 = LoadInt32Instr(r5)
    //     0xc09c00: sbfx            x13, x5, #1, #0x1f
    //     0xc09c04: tbz             w5, #0, #0xc09c0c
    //     0xc09c08: ldur            x13, [x5, #7]
    // 0xc09c0c: mov             x5, x13
    // 0xc09c10: add             x13, x14, x5
    // 0xc09c14: scvtf           d0, x3
    // 0xc09c18: scvtf           d1, x13
    // 0xc09c1c: fdiv            d2, d0, d1
    // 0xc09c20: cmp             w1, NULL
    // 0xc09c24: b.ne            #0xc09c30
    // 0xc09c28: r1 = Null
    //     0xc09c28: mov             x1, NULL
    // 0xc09c2c: b               #0xc09c6c
    // 0xc09c30: LoadField: r3 = r1->field_f
    //     0xc09c30: ldur            w3, [x1, #0xf]
    // 0xc09c34: DecompressPointer r3
    //     0xc09c34: add             x3, x3, HEAP, lsl #32
    // 0xc09c38: cmp             w3, NULL
    // 0xc09c3c: b.ne            #0xc09c48
    // 0xc09c40: r1 = Null
    //     0xc09c40: mov             x1, NULL
    // 0xc09c44: b               #0xc09c6c
    // 0xc09c48: LoadField: r1 = r3->field_1b
    //     0xc09c48: ldur            w1, [x3, #0x1b]
    // 0xc09c4c: DecompressPointer r1
    //     0xc09c4c: add             x1, x1, HEAP, lsl #32
    // 0xc09c50: cmp             w1, NULL
    // 0xc09c54: b.ne            #0xc09c60
    // 0xc09c58: r1 = Null
    //     0xc09c58: mov             x1, NULL
    // 0xc09c5c: b               #0xc09c6c
    // 0xc09c60: LoadField: r3 = r1->field_7
    //     0xc09c60: ldur            w3, [x1, #7]
    // 0xc09c64: DecompressPointer r3
    //     0xc09c64: add             x3, x3, HEAP, lsl #32
    // 0xc09c68: mov             x1, x3
    // 0xc09c6c: cmp             w1, NULL
    // 0xc09c70: b.ne            #0xc09c7c
    // 0xc09c74: r5 = 0
    //     0xc09c74: movz            x5, #0
    // 0xc09c78: b               #0xc09c8c
    // 0xc09c7c: r3 = LoadInt32Instr(r1)
    //     0xc09c7c: sbfx            x3, x1, #1, #0x1f
    //     0xc09c80: tbz             w1, #0, #0xc09c88
    //     0xc09c84: ldur            x3, [x1, #7]
    // 0xc09c88: mov             x5, x3
    // 0xc09c8c: mov             x1, x12
    // 0xc09c90: mov             v0.16b, v2.16b
    // 0xc09c94: r3 = "5"
    //     0xc09c94: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f898] "5"
    //     0xc09c98: ldr             x3, [x3, #0x898]
    // 0xc09c9c: r0 = chartRow()
    //     0xc09c9c: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xc09ca0: mov             x4, x0
    // 0xc09ca4: ldur            x0, [fp, #-0x18]
    // 0xc09ca8: stur            x4, [fp, #-0x30]
    // 0xc09cac: LoadField: r2 = r0->field_13
    //     0xc09cac: ldur            w2, [x0, #0x13]
    // 0xc09cb0: DecompressPointer r2
    //     0xc09cb0: add             x2, x2, HEAP, lsl #32
    // 0xc09cb4: ldur            x6, [fp, #-8]
    // 0xc09cb8: LoadField: r1 = r6->field_b
    //     0xc09cb8: ldur            w1, [x6, #0xb]
    // 0xc09cbc: DecompressPointer r1
    //     0xc09cbc: add             x1, x1, HEAP, lsl #32
    // 0xc09cc0: cmp             w1, NULL
    // 0xc09cc4: b.eq            #0xc0b6e8
    // 0xc09cc8: LoadField: r3 = r1->field_f
    //     0xc09cc8: ldur            w3, [x1, #0xf]
    // 0xc09ccc: DecompressPointer r3
    //     0xc09ccc: add             x3, x3, HEAP, lsl #32
    // 0xc09cd0: LoadField: r1 = r3->field_b
    //     0xc09cd0: ldur            w1, [x3, #0xb]
    // 0xc09cd4: DecompressPointer r1
    //     0xc09cd4: add             x1, x1, HEAP, lsl #32
    // 0xc09cd8: cmp             w1, NULL
    // 0xc09cdc: b.ne            #0xc09ce8
    // 0xc09ce0: r3 = Null
    //     0xc09ce0: mov             x3, NULL
    // 0xc09ce4: b               #0xc09d20
    // 0xc09ce8: LoadField: r3 = r1->field_f
    //     0xc09ce8: ldur            w3, [x1, #0xf]
    // 0xc09cec: DecompressPointer r3
    //     0xc09cec: add             x3, x3, HEAP, lsl #32
    // 0xc09cf0: cmp             w3, NULL
    // 0xc09cf4: b.ne            #0xc09d00
    // 0xc09cf8: r3 = Null
    //     0xc09cf8: mov             x3, NULL
    // 0xc09cfc: b               #0xc09d20
    // 0xc09d00: LoadField: r5 = r3->field_1b
    //     0xc09d00: ldur            w5, [x3, #0x1b]
    // 0xc09d04: DecompressPointer r5
    //     0xc09d04: add             x5, x5, HEAP, lsl #32
    // 0xc09d08: cmp             w5, NULL
    // 0xc09d0c: b.ne            #0xc09d18
    // 0xc09d10: r3 = Null
    //     0xc09d10: mov             x3, NULL
    // 0xc09d14: b               #0xc09d20
    // 0xc09d18: LoadField: r3 = r5->field_b
    //     0xc09d18: ldur            w3, [x5, #0xb]
    // 0xc09d1c: DecompressPointer r3
    //     0xc09d1c: add             x3, x3, HEAP, lsl #32
    // 0xc09d20: cmp             w3, NULL
    // 0xc09d24: b.ne            #0xc09d30
    // 0xc09d28: r3 = 0
    //     0xc09d28: movz            x3, #0
    // 0xc09d2c: b               #0xc09d40
    // 0xc09d30: r5 = LoadInt32Instr(r3)
    //     0xc09d30: sbfx            x5, x3, #1, #0x1f
    //     0xc09d34: tbz             w3, #0, #0xc09d3c
    //     0xc09d38: ldur            x5, [x3, #7]
    // 0xc09d3c: mov             x3, x5
    // 0xc09d40: cmp             w1, NULL
    // 0xc09d44: b.ne            #0xc09d50
    // 0xc09d48: r5 = Null
    //     0xc09d48: mov             x5, NULL
    // 0xc09d4c: b               #0xc09d88
    // 0xc09d50: LoadField: r5 = r1->field_f
    //     0xc09d50: ldur            w5, [x1, #0xf]
    // 0xc09d54: DecompressPointer r5
    //     0xc09d54: add             x5, x5, HEAP, lsl #32
    // 0xc09d58: cmp             w5, NULL
    // 0xc09d5c: b.ne            #0xc09d68
    // 0xc09d60: r5 = Null
    //     0xc09d60: mov             x5, NULL
    // 0xc09d64: b               #0xc09d88
    // 0xc09d68: LoadField: r7 = r5->field_1b
    //     0xc09d68: ldur            w7, [x5, #0x1b]
    // 0xc09d6c: DecompressPointer r7
    //     0xc09d6c: add             x7, x7, HEAP, lsl #32
    // 0xc09d70: cmp             w7, NULL
    // 0xc09d74: b.ne            #0xc09d80
    // 0xc09d78: r5 = Null
    //     0xc09d78: mov             x5, NULL
    // 0xc09d7c: b               #0xc09d88
    // 0xc09d80: LoadField: r5 = r7->field_7
    //     0xc09d80: ldur            w5, [x7, #7]
    // 0xc09d84: DecompressPointer r5
    //     0xc09d84: add             x5, x5, HEAP, lsl #32
    // 0xc09d88: cmp             w5, NULL
    // 0xc09d8c: b.ne            #0xc09d98
    // 0xc09d90: r5 = 0
    //     0xc09d90: movz            x5, #0
    // 0xc09d94: b               #0xc09da8
    // 0xc09d98: r7 = LoadInt32Instr(r5)
    //     0xc09d98: sbfx            x7, x5, #1, #0x1f
    //     0xc09d9c: tbz             w5, #0, #0xc09da4
    //     0xc09da0: ldur            x7, [x5, #7]
    // 0xc09da4: mov             x5, x7
    // 0xc09da8: cmp             w1, NULL
    // 0xc09dac: b.ne            #0xc09db8
    // 0xc09db0: r7 = Null
    //     0xc09db0: mov             x7, NULL
    // 0xc09db4: b               #0xc09df0
    // 0xc09db8: LoadField: r7 = r1->field_f
    //     0xc09db8: ldur            w7, [x1, #0xf]
    // 0xc09dbc: DecompressPointer r7
    //     0xc09dbc: add             x7, x7, HEAP, lsl #32
    // 0xc09dc0: cmp             w7, NULL
    // 0xc09dc4: b.ne            #0xc09dd0
    // 0xc09dc8: r7 = Null
    //     0xc09dc8: mov             x7, NULL
    // 0xc09dcc: b               #0xc09df0
    // 0xc09dd0: LoadField: r8 = r7->field_1b
    //     0xc09dd0: ldur            w8, [x7, #0x1b]
    // 0xc09dd4: DecompressPointer r8
    //     0xc09dd4: add             x8, x8, HEAP, lsl #32
    // 0xc09dd8: cmp             w8, NULL
    // 0xc09ddc: b.ne            #0xc09de8
    // 0xc09de0: r7 = Null
    //     0xc09de0: mov             x7, NULL
    // 0xc09de4: b               #0xc09df0
    // 0xc09de8: LoadField: r7 = r8->field_b
    //     0xc09de8: ldur            w7, [x8, #0xb]
    // 0xc09dec: DecompressPointer r7
    //     0xc09dec: add             x7, x7, HEAP, lsl #32
    // 0xc09df0: cmp             w7, NULL
    // 0xc09df4: b.ne            #0xc09e00
    // 0xc09df8: r7 = 0
    //     0xc09df8: movz            x7, #0
    // 0xc09dfc: b               #0xc09e10
    // 0xc09e00: r8 = LoadInt32Instr(r7)
    //     0xc09e00: sbfx            x8, x7, #1, #0x1f
    //     0xc09e04: tbz             w7, #0, #0xc09e0c
    //     0xc09e08: ldur            x8, [x7, #7]
    // 0xc09e0c: mov             x7, x8
    // 0xc09e10: add             x8, x5, x7
    // 0xc09e14: cmp             w1, NULL
    // 0xc09e18: b.ne            #0xc09e24
    // 0xc09e1c: r5 = Null
    //     0xc09e1c: mov             x5, NULL
    // 0xc09e20: b               #0xc09e5c
    // 0xc09e24: LoadField: r5 = r1->field_f
    //     0xc09e24: ldur            w5, [x1, #0xf]
    // 0xc09e28: DecompressPointer r5
    //     0xc09e28: add             x5, x5, HEAP, lsl #32
    // 0xc09e2c: cmp             w5, NULL
    // 0xc09e30: b.ne            #0xc09e3c
    // 0xc09e34: r5 = Null
    //     0xc09e34: mov             x5, NULL
    // 0xc09e38: b               #0xc09e5c
    // 0xc09e3c: LoadField: r7 = r5->field_1b
    //     0xc09e3c: ldur            w7, [x5, #0x1b]
    // 0xc09e40: DecompressPointer r7
    //     0xc09e40: add             x7, x7, HEAP, lsl #32
    // 0xc09e44: cmp             w7, NULL
    // 0xc09e48: b.ne            #0xc09e54
    // 0xc09e4c: r5 = Null
    //     0xc09e4c: mov             x5, NULL
    // 0xc09e50: b               #0xc09e5c
    // 0xc09e54: LoadField: r5 = r7->field_f
    //     0xc09e54: ldur            w5, [x7, #0xf]
    // 0xc09e58: DecompressPointer r5
    //     0xc09e58: add             x5, x5, HEAP, lsl #32
    // 0xc09e5c: cmp             w5, NULL
    // 0xc09e60: b.ne            #0xc09e6c
    // 0xc09e64: r5 = 0
    //     0xc09e64: movz            x5, #0
    // 0xc09e68: b               #0xc09e7c
    // 0xc09e6c: r7 = LoadInt32Instr(r5)
    //     0xc09e6c: sbfx            x7, x5, #1, #0x1f
    //     0xc09e70: tbz             w5, #0, #0xc09e78
    //     0xc09e74: ldur            x7, [x5, #7]
    // 0xc09e78: mov             x5, x7
    // 0xc09e7c: add             x7, x8, x5
    // 0xc09e80: cmp             w1, NULL
    // 0xc09e84: b.ne            #0xc09e90
    // 0xc09e88: r5 = Null
    //     0xc09e88: mov             x5, NULL
    // 0xc09e8c: b               #0xc09ec8
    // 0xc09e90: LoadField: r5 = r1->field_f
    //     0xc09e90: ldur            w5, [x1, #0xf]
    // 0xc09e94: DecompressPointer r5
    //     0xc09e94: add             x5, x5, HEAP, lsl #32
    // 0xc09e98: cmp             w5, NULL
    // 0xc09e9c: b.ne            #0xc09ea8
    // 0xc09ea0: r5 = Null
    //     0xc09ea0: mov             x5, NULL
    // 0xc09ea4: b               #0xc09ec8
    // 0xc09ea8: LoadField: r8 = r5->field_1b
    //     0xc09ea8: ldur            w8, [x5, #0x1b]
    // 0xc09eac: DecompressPointer r8
    //     0xc09eac: add             x8, x8, HEAP, lsl #32
    // 0xc09eb0: cmp             w8, NULL
    // 0xc09eb4: b.ne            #0xc09ec0
    // 0xc09eb8: r5 = Null
    //     0xc09eb8: mov             x5, NULL
    // 0xc09ebc: b               #0xc09ec8
    // 0xc09ec0: LoadField: r5 = r8->field_13
    //     0xc09ec0: ldur            w5, [x8, #0x13]
    // 0xc09ec4: DecompressPointer r5
    //     0xc09ec4: add             x5, x5, HEAP, lsl #32
    // 0xc09ec8: cmp             w5, NULL
    // 0xc09ecc: b.ne            #0xc09ed8
    // 0xc09ed0: r5 = 0
    //     0xc09ed0: movz            x5, #0
    // 0xc09ed4: b               #0xc09ee8
    // 0xc09ed8: r8 = LoadInt32Instr(r5)
    //     0xc09ed8: sbfx            x8, x5, #1, #0x1f
    //     0xc09edc: tbz             w5, #0, #0xc09ee4
    //     0xc09ee0: ldur            x8, [x5, #7]
    // 0xc09ee4: mov             x5, x8
    // 0xc09ee8: add             x8, x7, x5
    // 0xc09eec: cmp             w1, NULL
    // 0xc09ef0: b.ne            #0xc09efc
    // 0xc09ef4: r5 = Null
    //     0xc09ef4: mov             x5, NULL
    // 0xc09ef8: b               #0xc09f34
    // 0xc09efc: LoadField: r5 = r1->field_f
    //     0xc09efc: ldur            w5, [x1, #0xf]
    // 0xc09f00: DecompressPointer r5
    //     0xc09f00: add             x5, x5, HEAP, lsl #32
    // 0xc09f04: cmp             w5, NULL
    // 0xc09f08: b.ne            #0xc09f14
    // 0xc09f0c: r5 = Null
    //     0xc09f0c: mov             x5, NULL
    // 0xc09f10: b               #0xc09f34
    // 0xc09f14: LoadField: r7 = r5->field_1b
    //     0xc09f14: ldur            w7, [x5, #0x1b]
    // 0xc09f18: DecompressPointer r7
    //     0xc09f18: add             x7, x7, HEAP, lsl #32
    // 0xc09f1c: cmp             w7, NULL
    // 0xc09f20: b.ne            #0xc09f2c
    // 0xc09f24: r5 = Null
    //     0xc09f24: mov             x5, NULL
    // 0xc09f28: b               #0xc09f34
    // 0xc09f2c: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xc09f2c: ldur            w5, [x7, #0x17]
    // 0xc09f30: DecompressPointer r5
    //     0xc09f30: add             x5, x5, HEAP, lsl #32
    // 0xc09f34: cmp             w5, NULL
    // 0xc09f38: b.ne            #0xc09f44
    // 0xc09f3c: r5 = 0
    //     0xc09f3c: movz            x5, #0
    // 0xc09f40: b               #0xc09f54
    // 0xc09f44: r7 = LoadInt32Instr(r5)
    //     0xc09f44: sbfx            x7, x5, #1, #0x1f
    //     0xc09f48: tbz             w5, #0, #0xc09f50
    //     0xc09f4c: ldur            x7, [x5, #7]
    // 0xc09f50: mov             x5, x7
    // 0xc09f54: add             x7, x8, x5
    // 0xc09f58: scvtf           d0, x3
    // 0xc09f5c: scvtf           d1, x7
    // 0xc09f60: fdiv            d2, d0, d1
    // 0xc09f64: cmp             w1, NULL
    // 0xc09f68: b.ne            #0xc09f74
    // 0xc09f6c: r1 = Null
    //     0xc09f6c: mov             x1, NULL
    // 0xc09f70: b               #0xc09fb0
    // 0xc09f74: LoadField: r3 = r1->field_f
    //     0xc09f74: ldur            w3, [x1, #0xf]
    // 0xc09f78: DecompressPointer r3
    //     0xc09f78: add             x3, x3, HEAP, lsl #32
    // 0xc09f7c: cmp             w3, NULL
    // 0xc09f80: b.ne            #0xc09f8c
    // 0xc09f84: r1 = Null
    //     0xc09f84: mov             x1, NULL
    // 0xc09f88: b               #0xc09fb0
    // 0xc09f8c: LoadField: r1 = r3->field_1b
    //     0xc09f8c: ldur            w1, [x3, #0x1b]
    // 0xc09f90: DecompressPointer r1
    //     0xc09f90: add             x1, x1, HEAP, lsl #32
    // 0xc09f94: cmp             w1, NULL
    // 0xc09f98: b.ne            #0xc09fa4
    // 0xc09f9c: r1 = Null
    //     0xc09f9c: mov             x1, NULL
    // 0xc09fa0: b               #0xc09fb0
    // 0xc09fa4: LoadField: r3 = r1->field_b
    //     0xc09fa4: ldur            w3, [x1, #0xb]
    // 0xc09fa8: DecompressPointer r3
    //     0xc09fa8: add             x3, x3, HEAP, lsl #32
    // 0xc09fac: mov             x1, x3
    // 0xc09fb0: cmp             w1, NULL
    // 0xc09fb4: b.ne            #0xc09fc0
    // 0xc09fb8: r5 = 0
    //     0xc09fb8: movz            x5, #0
    // 0xc09fbc: b               #0xc09fd0
    // 0xc09fc0: r3 = LoadInt32Instr(r1)
    //     0xc09fc0: sbfx            x3, x1, #1, #0x1f
    //     0xc09fc4: tbz             w1, #0, #0xc09fcc
    //     0xc09fc8: ldur            x3, [x1, #7]
    // 0xc09fcc: mov             x5, x3
    // 0xc09fd0: mov             x1, x6
    // 0xc09fd4: mov             v0.16b, v2.16b
    // 0xc09fd8: r3 = "4"
    //     0xc09fd8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a0] "4"
    //     0xc09fdc: ldr             x3, [x3, #0x8a0]
    // 0xc09fe0: r0 = chartRow()
    //     0xc09fe0: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xc09fe4: mov             x4, x0
    // 0xc09fe8: ldur            x0, [fp, #-0x18]
    // 0xc09fec: stur            x4, [fp, #-0x38]
    // 0xc09ff0: LoadField: r2 = r0->field_13
    //     0xc09ff0: ldur            w2, [x0, #0x13]
    // 0xc09ff4: DecompressPointer r2
    //     0xc09ff4: add             x2, x2, HEAP, lsl #32
    // 0xc09ff8: ldur            x6, [fp, #-8]
    // 0xc09ffc: LoadField: r1 = r6->field_b
    //     0xc09ffc: ldur            w1, [x6, #0xb]
    // 0xc0a000: DecompressPointer r1
    //     0xc0a000: add             x1, x1, HEAP, lsl #32
    // 0xc0a004: cmp             w1, NULL
    // 0xc0a008: b.eq            #0xc0b6ec
    // 0xc0a00c: LoadField: r3 = r1->field_f
    //     0xc0a00c: ldur            w3, [x1, #0xf]
    // 0xc0a010: DecompressPointer r3
    //     0xc0a010: add             x3, x3, HEAP, lsl #32
    // 0xc0a014: LoadField: r1 = r3->field_b
    //     0xc0a014: ldur            w1, [x3, #0xb]
    // 0xc0a018: DecompressPointer r1
    //     0xc0a018: add             x1, x1, HEAP, lsl #32
    // 0xc0a01c: cmp             w1, NULL
    // 0xc0a020: b.ne            #0xc0a02c
    // 0xc0a024: r3 = Null
    //     0xc0a024: mov             x3, NULL
    // 0xc0a028: b               #0xc0a064
    // 0xc0a02c: LoadField: r3 = r1->field_f
    //     0xc0a02c: ldur            w3, [x1, #0xf]
    // 0xc0a030: DecompressPointer r3
    //     0xc0a030: add             x3, x3, HEAP, lsl #32
    // 0xc0a034: cmp             w3, NULL
    // 0xc0a038: b.ne            #0xc0a044
    // 0xc0a03c: r3 = Null
    //     0xc0a03c: mov             x3, NULL
    // 0xc0a040: b               #0xc0a064
    // 0xc0a044: LoadField: r5 = r3->field_1b
    //     0xc0a044: ldur            w5, [x3, #0x1b]
    // 0xc0a048: DecompressPointer r5
    //     0xc0a048: add             x5, x5, HEAP, lsl #32
    // 0xc0a04c: cmp             w5, NULL
    // 0xc0a050: b.ne            #0xc0a05c
    // 0xc0a054: r3 = Null
    //     0xc0a054: mov             x3, NULL
    // 0xc0a058: b               #0xc0a064
    // 0xc0a05c: LoadField: r3 = r5->field_f
    //     0xc0a05c: ldur            w3, [x5, #0xf]
    // 0xc0a060: DecompressPointer r3
    //     0xc0a060: add             x3, x3, HEAP, lsl #32
    // 0xc0a064: cmp             w3, NULL
    // 0xc0a068: b.ne            #0xc0a074
    // 0xc0a06c: r3 = 0
    //     0xc0a06c: movz            x3, #0
    // 0xc0a070: b               #0xc0a084
    // 0xc0a074: r5 = LoadInt32Instr(r3)
    //     0xc0a074: sbfx            x5, x3, #1, #0x1f
    //     0xc0a078: tbz             w3, #0, #0xc0a080
    //     0xc0a07c: ldur            x5, [x3, #7]
    // 0xc0a080: mov             x3, x5
    // 0xc0a084: cmp             w1, NULL
    // 0xc0a088: b.ne            #0xc0a094
    // 0xc0a08c: r5 = Null
    //     0xc0a08c: mov             x5, NULL
    // 0xc0a090: b               #0xc0a0cc
    // 0xc0a094: LoadField: r5 = r1->field_f
    //     0xc0a094: ldur            w5, [x1, #0xf]
    // 0xc0a098: DecompressPointer r5
    //     0xc0a098: add             x5, x5, HEAP, lsl #32
    // 0xc0a09c: cmp             w5, NULL
    // 0xc0a0a0: b.ne            #0xc0a0ac
    // 0xc0a0a4: r5 = Null
    //     0xc0a0a4: mov             x5, NULL
    // 0xc0a0a8: b               #0xc0a0cc
    // 0xc0a0ac: LoadField: r7 = r5->field_1b
    //     0xc0a0ac: ldur            w7, [x5, #0x1b]
    // 0xc0a0b0: DecompressPointer r7
    //     0xc0a0b0: add             x7, x7, HEAP, lsl #32
    // 0xc0a0b4: cmp             w7, NULL
    // 0xc0a0b8: b.ne            #0xc0a0c4
    // 0xc0a0bc: r5 = Null
    //     0xc0a0bc: mov             x5, NULL
    // 0xc0a0c0: b               #0xc0a0cc
    // 0xc0a0c4: LoadField: r5 = r7->field_7
    //     0xc0a0c4: ldur            w5, [x7, #7]
    // 0xc0a0c8: DecompressPointer r5
    //     0xc0a0c8: add             x5, x5, HEAP, lsl #32
    // 0xc0a0cc: cmp             w5, NULL
    // 0xc0a0d0: b.ne            #0xc0a0dc
    // 0xc0a0d4: r5 = 0
    //     0xc0a0d4: movz            x5, #0
    // 0xc0a0d8: b               #0xc0a0ec
    // 0xc0a0dc: r7 = LoadInt32Instr(r5)
    //     0xc0a0dc: sbfx            x7, x5, #1, #0x1f
    //     0xc0a0e0: tbz             w5, #0, #0xc0a0e8
    //     0xc0a0e4: ldur            x7, [x5, #7]
    // 0xc0a0e8: mov             x5, x7
    // 0xc0a0ec: cmp             w1, NULL
    // 0xc0a0f0: b.ne            #0xc0a0fc
    // 0xc0a0f4: r7 = Null
    //     0xc0a0f4: mov             x7, NULL
    // 0xc0a0f8: b               #0xc0a134
    // 0xc0a0fc: LoadField: r7 = r1->field_f
    //     0xc0a0fc: ldur            w7, [x1, #0xf]
    // 0xc0a100: DecompressPointer r7
    //     0xc0a100: add             x7, x7, HEAP, lsl #32
    // 0xc0a104: cmp             w7, NULL
    // 0xc0a108: b.ne            #0xc0a114
    // 0xc0a10c: r7 = Null
    //     0xc0a10c: mov             x7, NULL
    // 0xc0a110: b               #0xc0a134
    // 0xc0a114: LoadField: r8 = r7->field_1b
    //     0xc0a114: ldur            w8, [x7, #0x1b]
    // 0xc0a118: DecompressPointer r8
    //     0xc0a118: add             x8, x8, HEAP, lsl #32
    // 0xc0a11c: cmp             w8, NULL
    // 0xc0a120: b.ne            #0xc0a12c
    // 0xc0a124: r7 = Null
    //     0xc0a124: mov             x7, NULL
    // 0xc0a128: b               #0xc0a134
    // 0xc0a12c: LoadField: r7 = r8->field_b
    //     0xc0a12c: ldur            w7, [x8, #0xb]
    // 0xc0a130: DecompressPointer r7
    //     0xc0a130: add             x7, x7, HEAP, lsl #32
    // 0xc0a134: cmp             w7, NULL
    // 0xc0a138: b.ne            #0xc0a144
    // 0xc0a13c: r7 = 0
    //     0xc0a13c: movz            x7, #0
    // 0xc0a140: b               #0xc0a154
    // 0xc0a144: r8 = LoadInt32Instr(r7)
    //     0xc0a144: sbfx            x8, x7, #1, #0x1f
    //     0xc0a148: tbz             w7, #0, #0xc0a150
    //     0xc0a14c: ldur            x8, [x7, #7]
    // 0xc0a150: mov             x7, x8
    // 0xc0a154: add             x8, x5, x7
    // 0xc0a158: cmp             w1, NULL
    // 0xc0a15c: b.ne            #0xc0a168
    // 0xc0a160: r5 = Null
    //     0xc0a160: mov             x5, NULL
    // 0xc0a164: b               #0xc0a1a0
    // 0xc0a168: LoadField: r5 = r1->field_f
    //     0xc0a168: ldur            w5, [x1, #0xf]
    // 0xc0a16c: DecompressPointer r5
    //     0xc0a16c: add             x5, x5, HEAP, lsl #32
    // 0xc0a170: cmp             w5, NULL
    // 0xc0a174: b.ne            #0xc0a180
    // 0xc0a178: r5 = Null
    //     0xc0a178: mov             x5, NULL
    // 0xc0a17c: b               #0xc0a1a0
    // 0xc0a180: LoadField: r7 = r5->field_1b
    //     0xc0a180: ldur            w7, [x5, #0x1b]
    // 0xc0a184: DecompressPointer r7
    //     0xc0a184: add             x7, x7, HEAP, lsl #32
    // 0xc0a188: cmp             w7, NULL
    // 0xc0a18c: b.ne            #0xc0a198
    // 0xc0a190: r5 = Null
    //     0xc0a190: mov             x5, NULL
    // 0xc0a194: b               #0xc0a1a0
    // 0xc0a198: LoadField: r5 = r7->field_f
    //     0xc0a198: ldur            w5, [x7, #0xf]
    // 0xc0a19c: DecompressPointer r5
    //     0xc0a19c: add             x5, x5, HEAP, lsl #32
    // 0xc0a1a0: cmp             w5, NULL
    // 0xc0a1a4: b.ne            #0xc0a1b0
    // 0xc0a1a8: r5 = 0
    //     0xc0a1a8: movz            x5, #0
    // 0xc0a1ac: b               #0xc0a1c0
    // 0xc0a1b0: r7 = LoadInt32Instr(r5)
    //     0xc0a1b0: sbfx            x7, x5, #1, #0x1f
    //     0xc0a1b4: tbz             w5, #0, #0xc0a1bc
    //     0xc0a1b8: ldur            x7, [x5, #7]
    // 0xc0a1bc: mov             x5, x7
    // 0xc0a1c0: add             x7, x8, x5
    // 0xc0a1c4: cmp             w1, NULL
    // 0xc0a1c8: b.ne            #0xc0a1d4
    // 0xc0a1cc: r5 = Null
    //     0xc0a1cc: mov             x5, NULL
    // 0xc0a1d0: b               #0xc0a20c
    // 0xc0a1d4: LoadField: r5 = r1->field_f
    //     0xc0a1d4: ldur            w5, [x1, #0xf]
    // 0xc0a1d8: DecompressPointer r5
    //     0xc0a1d8: add             x5, x5, HEAP, lsl #32
    // 0xc0a1dc: cmp             w5, NULL
    // 0xc0a1e0: b.ne            #0xc0a1ec
    // 0xc0a1e4: r5 = Null
    //     0xc0a1e4: mov             x5, NULL
    // 0xc0a1e8: b               #0xc0a20c
    // 0xc0a1ec: LoadField: r8 = r5->field_1b
    //     0xc0a1ec: ldur            w8, [x5, #0x1b]
    // 0xc0a1f0: DecompressPointer r8
    //     0xc0a1f0: add             x8, x8, HEAP, lsl #32
    // 0xc0a1f4: cmp             w8, NULL
    // 0xc0a1f8: b.ne            #0xc0a204
    // 0xc0a1fc: r5 = Null
    //     0xc0a1fc: mov             x5, NULL
    // 0xc0a200: b               #0xc0a20c
    // 0xc0a204: LoadField: r5 = r8->field_13
    //     0xc0a204: ldur            w5, [x8, #0x13]
    // 0xc0a208: DecompressPointer r5
    //     0xc0a208: add             x5, x5, HEAP, lsl #32
    // 0xc0a20c: cmp             w5, NULL
    // 0xc0a210: b.ne            #0xc0a21c
    // 0xc0a214: r5 = 0
    //     0xc0a214: movz            x5, #0
    // 0xc0a218: b               #0xc0a22c
    // 0xc0a21c: r8 = LoadInt32Instr(r5)
    //     0xc0a21c: sbfx            x8, x5, #1, #0x1f
    //     0xc0a220: tbz             w5, #0, #0xc0a228
    //     0xc0a224: ldur            x8, [x5, #7]
    // 0xc0a228: mov             x5, x8
    // 0xc0a22c: add             x8, x7, x5
    // 0xc0a230: cmp             w1, NULL
    // 0xc0a234: b.ne            #0xc0a240
    // 0xc0a238: r5 = Null
    //     0xc0a238: mov             x5, NULL
    // 0xc0a23c: b               #0xc0a278
    // 0xc0a240: LoadField: r5 = r1->field_f
    //     0xc0a240: ldur            w5, [x1, #0xf]
    // 0xc0a244: DecompressPointer r5
    //     0xc0a244: add             x5, x5, HEAP, lsl #32
    // 0xc0a248: cmp             w5, NULL
    // 0xc0a24c: b.ne            #0xc0a258
    // 0xc0a250: r5 = Null
    //     0xc0a250: mov             x5, NULL
    // 0xc0a254: b               #0xc0a278
    // 0xc0a258: LoadField: r7 = r5->field_1b
    //     0xc0a258: ldur            w7, [x5, #0x1b]
    // 0xc0a25c: DecompressPointer r7
    //     0xc0a25c: add             x7, x7, HEAP, lsl #32
    // 0xc0a260: cmp             w7, NULL
    // 0xc0a264: b.ne            #0xc0a270
    // 0xc0a268: r5 = Null
    //     0xc0a268: mov             x5, NULL
    // 0xc0a26c: b               #0xc0a278
    // 0xc0a270: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xc0a270: ldur            w5, [x7, #0x17]
    // 0xc0a274: DecompressPointer r5
    //     0xc0a274: add             x5, x5, HEAP, lsl #32
    // 0xc0a278: cmp             w5, NULL
    // 0xc0a27c: b.ne            #0xc0a288
    // 0xc0a280: r5 = 0
    //     0xc0a280: movz            x5, #0
    // 0xc0a284: b               #0xc0a298
    // 0xc0a288: r7 = LoadInt32Instr(r5)
    //     0xc0a288: sbfx            x7, x5, #1, #0x1f
    //     0xc0a28c: tbz             w5, #0, #0xc0a294
    //     0xc0a290: ldur            x7, [x5, #7]
    // 0xc0a294: mov             x5, x7
    // 0xc0a298: add             x7, x8, x5
    // 0xc0a29c: scvtf           d0, x3
    // 0xc0a2a0: scvtf           d1, x7
    // 0xc0a2a4: fdiv            d2, d0, d1
    // 0xc0a2a8: cmp             w1, NULL
    // 0xc0a2ac: b.ne            #0xc0a2b8
    // 0xc0a2b0: r1 = Null
    //     0xc0a2b0: mov             x1, NULL
    // 0xc0a2b4: b               #0xc0a2f4
    // 0xc0a2b8: LoadField: r3 = r1->field_f
    //     0xc0a2b8: ldur            w3, [x1, #0xf]
    // 0xc0a2bc: DecompressPointer r3
    //     0xc0a2bc: add             x3, x3, HEAP, lsl #32
    // 0xc0a2c0: cmp             w3, NULL
    // 0xc0a2c4: b.ne            #0xc0a2d0
    // 0xc0a2c8: r1 = Null
    //     0xc0a2c8: mov             x1, NULL
    // 0xc0a2cc: b               #0xc0a2f4
    // 0xc0a2d0: LoadField: r1 = r3->field_1b
    //     0xc0a2d0: ldur            w1, [x3, #0x1b]
    // 0xc0a2d4: DecompressPointer r1
    //     0xc0a2d4: add             x1, x1, HEAP, lsl #32
    // 0xc0a2d8: cmp             w1, NULL
    // 0xc0a2dc: b.ne            #0xc0a2e8
    // 0xc0a2e0: r1 = Null
    //     0xc0a2e0: mov             x1, NULL
    // 0xc0a2e4: b               #0xc0a2f4
    // 0xc0a2e8: LoadField: r3 = r1->field_f
    //     0xc0a2e8: ldur            w3, [x1, #0xf]
    // 0xc0a2ec: DecompressPointer r3
    //     0xc0a2ec: add             x3, x3, HEAP, lsl #32
    // 0xc0a2f0: mov             x1, x3
    // 0xc0a2f4: cmp             w1, NULL
    // 0xc0a2f8: b.ne            #0xc0a304
    // 0xc0a2fc: r5 = 0
    //     0xc0a2fc: movz            x5, #0
    // 0xc0a300: b               #0xc0a314
    // 0xc0a304: r3 = LoadInt32Instr(r1)
    //     0xc0a304: sbfx            x3, x1, #1, #0x1f
    //     0xc0a308: tbz             w1, #0, #0xc0a310
    //     0xc0a30c: ldur            x3, [x1, #7]
    // 0xc0a310: mov             x5, x3
    // 0xc0a314: mov             x1, x6
    // 0xc0a318: mov             v0.16b, v2.16b
    // 0xc0a31c: r3 = "3"
    //     0xc0a31c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8a8] "3"
    //     0xc0a320: ldr             x3, [x3, #0x8a8]
    // 0xc0a324: r0 = chartRow()
    //     0xc0a324: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xc0a328: mov             x4, x0
    // 0xc0a32c: ldur            x0, [fp, #-0x18]
    // 0xc0a330: stur            x4, [fp, #-0x48]
    // 0xc0a334: LoadField: r2 = r0->field_13
    //     0xc0a334: ldur            w2, [x0, #0x13]
    // 0xc0a338: DecompressPointer r2
    //     0xc0a338: add             x2, x2, HEAP, lsl #32
    // 0xc0a33c: ldur            x6, [fp, #-8]
    // 0xc0a340: LoadField: r1 = r6->field_b
    //     0xc0a340: ldur            w1, [x6, #0xb]
    // 0xc0a344: DecompressPointer r1
    //     0xc0a344: add             x1, x1, HEAP, lsl #32
    // 0xc0a348: cmp             w1, NULL
    // 0xc0a34c: b.eq            #0xc0b6f0
    // 0xc0a350: LoadField: r3 = r1->field_f
    //     0xc0a350: ldur            w3, [x1, #0xf]
    // 0xc0a354: DecompressPointer r3
    //     0xc0a354: add             x3, x3, HEAP, lsl #32
    // 0xc0a358: LoadField: r1 = r3->field_b
    //     0xc0a358: ldur            w1, [x3, #0xb]
    // 0xc0a35c: DecompressPointer r1
    //     0xc0a35c: add             x1, x1, HEAP, lsl #32
    // 0xc0a360: cmp             w1, NULL
    // 0xc0a364: b.ne            #0xc0a370
    // 0xc0a368: r3 = Null
    //     0xc0a368: mov             x3, NULL
    // 0xc0a36c: b               #0xc0a3a8
    // 0xc0a370: LoadField: r3 = r1->field_f
    //     0xc0a370: ldur            w3, [x1, #0xf]
    // 0xc0a374: DecompressPointer r3
    //     0xc0a374: add             x3, x3, HEAP, lsl #32
    // 0xc0a378: cmp             w3, NULL
    // 0xc0a37c: b.ne            #0xc0a388
    // 0xc0a380: r3 = Null
    //     0xc0a380: mov             x3, NULL
    // 0xc0a384: b               #0xc0a3a8
    // 0xc0a388: LoadField: r5 = r3->field_1b
    //     0xc0a388: ldur            w5, [x3, #0x1b]
    // 0xc0a38c: DecompressPointer r5
    //     0xc0a38c: add             x5, x5, HEAP, lsl #32
    // 0xc0a390: cmp             w5, NULL
    // 0xc0a394: b.ne            #0xc0a3a0
    // 0xc0a398: r3 = Null
    //     0xc0a398: mov             x3, NULL
    // 0xc0a39c: b               #0xc0a3a8
    // 0xc0a3a0: LoadField: r3 = r5->field_13
    //     0xc0a3a0: ldur            w3, [x5, #0x13]
    // 0xc0a3a4: DecompressPointer r3
    //     0xc0a3a4: add             x3, x3, HEAP, lsl #32
    // 0xc0a3a8: cmp             w3, NULL
    // 0xc0a3ac: b.ne            #0xc0a3b8
    // 0xc0a3b0: r3 = 0
    //     0xc0a3b0: movz            x3, #0
    // 0xc0a3b4: b               #0xc0a3c8
    // 0xc0a3b8: r5 = LoadInt32Instr(r3)
    //     0xc0a3b8: sbfx            x5, x3, #1, #0x1f
    //     0xc0a3bc: tbz             w3, #0, #0xc0a3c4
    //     0xc0a3c0: ldur            x5, [x3, #7]
    // 0xc0a3c4: mov             x3, x5
    // 0xc0a3c8: cmp             w1, NULL
    // 0xc0a3cc: b.ne            #0xc0a3d8
    // 0xc0a3d0: r5 = Null
    //     0xc0a3d0: mov             x5, NULL
    // 0xc0a3d4: b               #0xc0a410
    // 0xc0a3d8: LoadField: r5 = r1->field_f
    //     0xc0a3d8: ldur            w5, [x1, #0xf]
    // 0xc0a3dc: DecompressPointer r5
    //     0xc0a3dc: add             x5, x5, HEAP, lsl #32
    // 0xc0a3e0: cmp             w5, NULL
    // 0xc0a3e4: b.ne            #0xc0a3f0
    // 0xc0a3e8: r5 = Null
    //     0xc0a3e8: mov             x5, NULL
    // 0xc0a3ec: b               #0xc0a410
    // 0xc0a3f0: LoadField: r7 = r5->field_1b
    //     0xc0a3f0: ldur            w7, [x5, #0x1b]
    // 0xc0a3f4: DecompressPointer r7
    //     0xc0a3f4: add             x7, x7, HEAP, lsl #32
    // 0xc0a3f8: cmp             w7, NULL
    // 0xc0a3fc: b.ne            #0xc0a408
    // 0xc0a400: r5 = Null
    //     0xc0a400: mov             x5, NULL
    // 0xc0a404: b               #0xc0a410
    // 0xc0a408: LoadField: r5 = r7->field_7
    //     0xc0a408: ldur            w5, [x7, #7]
    // 0xc0a40c: DecompressPointer r5
    //     0xc0a40c: add             x5, x5, HEAP, lsl #32
    // 0xc0a410: cmp             w5, NULL
    // 0xc0a414: b.ne            #0xc0a420
    // 0xc0a418: r5 = 0
    //     0xc0a418: movz            x5, #0
    // 0xc0a41c: b               #0xc0a430
    // 0xc0a420: r7 = LoadInt32Instr(r5)
    //     0xc0a420: sbfx            x7, x5, #1, #0x1f
    //     0xc0a424: tbz             w5, #0, #0xc0a42c
    //     0xc0a428: ldur            x7, [x5, #7]
    // 0xc0a42c: mov             x5, x7
    // 0xc0a430: cmp             w1, NULL
    // 0xc0a434: b.ne            #0xc0a440
    // 0xc0a438: r7 = Null
    //     0xc0a438: mov             x7, NULL
    // 0xc0a43c: b               #0xc0a478
    // 0xc0a440: LoadField: r7 = r1->field_f
    //     0xc0a440: ldur            w7, [x1, #0xf]
    // 0xc0a444: DecompressPointer r7
    //     0xc0a444: add             x7, x7, HEAP, lsl #32
    // 0xc0a448: cmp             w7, NULL
    // 0xc0a44c: b.ne            #0xc0a458
    // 0xc0a450: r7 = Null
    //     0xc0a450: mov             x7, NULL
    // 0xc0a454: b               #0xc0a478
    // 0xc0a458: LoadField: r8 = r7->field_1b
    //     0xc0a458: ldur            w8, [x7, #0x1b]
    // 0xc0a45c: DecompressPointer r8
    //     0xc0a45c: add             x8, x8, HEAP, lsl #32
    // 0xc0a460: cmp             w8, NULL
    // 0xc0a464: b.ne            #0xc0a470
    // 0xc0a468: r7 = Null
    //     0xc0a468: mov             x7, NULL
    // 0xc0a46c: b               #0xc0a478
    // 0xc0a470: LoadField: r7 = r8->field_b
    //     0xc0a470: ldur            w7, [x8, #0xb]
    // 0xc0a474: DecompressPointer r7
    //     0xc0a474: add             x7, x7, HEAP, lsl #32
    // 0xc0a478: cmp             w7, NULL
    // 0xc0a47c: b.ne            #0xc0a488
    // 0xc0a480: r7 = 0
    //     0xc0a480: movz            x7, #0
    // 0xc0a484: b               #0xc0a498
    // 0xc0a488: r8 = LoadInt32Instr(r7)
    //     0xc0a488: sbfx            x8, x7, #1, #0x1f
    //     0xc0a48c: tbz             w7, #0, #0xc0a494
    //     0xc0a490: ldur            x8, [x7, #7]
    // 0xc0a494: mov             x7, x8
    // 0xc0a498: add             x8, x5, x7
    // 0xc0a49c: cmp             w1, NULL
    // 0xc0a4a0: b.ne            #0xc0a4ac
    // 0xc0a4a4: r5 = Null
    //     0xc0a4a4: mov             x5, NULL
    // 0xc0a4a8: b               #0xc0a4e4
    // 0xc0a4ac: LoadField: r5 = r1->field_f
    //     0xc0a4ac: ldur            w5, [x1, #0xf]
    // 0xc0a4b0: DecompressPointer r5
    //     0xc0a4b0: add             x5, x5, HEAP, lsl #32
    // 0xc0a4b4: cmp             w5, NULL
    // 0xc0a4b8: b.ne            #0xc0a4c4
    // 0xc0a4bc: r5 = Null
    //     0xc0a4bc: mov             x5, NULL
    // 0xc0a4c0: b               #0xc0a4e4
    // 0xc0a4c4: LoadField: r7 = r5->field_1b
    //     0xc0a4c4: ldur            w7, [x5, #0x1b]
    // 0xc0a4c8: DecompressPointer r7
    //     0xc0a4c8: add             x7, x7, HEAP, lsl #32
    // 0xc0a4cc: cmp             w7, NULL
    // 0xc0a4d0: b.ne            #0xc0a4dc
    // 0xc0a4d4: r5 = Null
    //     0xc0a4d4: mov             x5, NULL
    // 0xc0a4d8: b               #0xc0a4e4
    // 0xc0a4dc: LoadField: r5 = r7->field_f
    //     0xc0a4dc: ldur            w5, [x7, #0xf]
    // 0xc0a4e0: DecompressPointer r5
    //     0xc0a4e0: add             x5, x5, HEAP, lsl #32
    // 0xc0a4e4: cmp             w5, NULL
    // 0xc0a4e8: b.ne            #0xc0a4f4
    // 0xc0a4ec: r5 = 0
    //     0xc0a4ec: movz            x5, #0
    // 0xc0a4f0: b               #0xc0a504
    // 0xc0a4f4: r7 = LoadInt32Instr(r5)
    //     0xc0a4f4: sbfx            x7, x5, #1, #0x1f
    //     0xc0a4f8: tbz             w5, #0, #0xc0a500
    //     0xc0a4fc: ldur            x7, [x5, #7]
    // 0xc0a500: mov             x5, x7
    // 0xc0a504: add             x7, x8, x5
    // 0xc0a508: cmp             w1, NULL
    // 0xc0a50c: b.ne            #0xc0a518
    // 0xc0a510: r5 = Null
    //     0xc0a510: mov             x5, NULL
    // 0xc0a514: b               #0xc0a550
    // 0xc0a518: LoadField: r5 = r1->field_f
    //     0xc0a518: ldur            w5, [x1, #0xf]
    // 0xc0a51c: DecompressPointer r5
    //     0xc0a51c: add             x5, x5, HEAP, lsl #32
    // 0xc0a520: cmp             w5, NULL
    // 0xc0a524: b.ne            #0xc0a530
    // 0xc0a528: r5 = Null
    //     0xc0a528: mov             x5, NULL
    // 0xc0a52c: b               #0xc0a550
    // 0xc0a530: LoadField: r8 = r5->field_1b
    //     0xc0a530: ldur            w8, [x5, #0x1b]
    // 0xc0a534: DecompressPointer r8
    //     0xc0a534: add             x8, x8, HEAP, lsl #32
    // 0xc0a538: cmp             w8, NULL
    // 0xc0a53c: b.ne            #0xc0a548
    // 0xc0a540: r5 = Null
    //     0xc0a540: mov             x5, NULL
    // 0xc0a544: b               #0xc0a550
    // 0xc0a548: LoadField: r5 = r8->field_13
    //     0xc0a548: ldur            w5, [x8, #0x13]
    // 0xc0a54c: DecompressPointer r5
    //     0xc0a54c: add             x5, x5, HEAP, lsl #32
    // 0xc0a550: cmp             w5, NULL
    // 0xc0a554: b.ne            #0xc0a560
    // 0xc0a558: r5 = 0
    //     0xc0a558: movz            x5, #0
    // 0xc0a55c: b               #0xc0a570
    // 0xc0a560: r8 = LoadInt32Instr(r5)
    //     0xc0a560: sbfx            x8, x5, #1, #0x1f
    //     0xc0a564: tbz             w5, #0, #0xc0a56c
    //     0xc0a568: ldur            x8, [x5, #7]
    // 0xc0a56c: mov             x5, x8
    // 0xc0a570: add             x8, x7, x5
    // 0xc0a574: cmp             w1, NULL
    // 0xc0a578: b.ne            #0xc0a584
    // 0xc0a57c: r5 = Null
    //     0xc0a57c: mov             x5, NULL
    // 0xc0a580: b               #0xc0a5bc
    // 0xc0a584: LoadField: r5 = r1->field_f
    //     0xc0a584: ldur            w5, [x1, #0xf]
    // 0xc0a588: DecompressPointer r5
    //     0xc0a588: add             x5, x5, HEAP, lsl #32
    // 0xc0a58c: cmp             w5, NULL
    // 0xc0a590: b.ne            #0xc0a59c
    // 0xc0a594: r5 = Null
    //     0xc0a594: mov             x5, NULL
    // 0xc0a598: b               #0xc0a5bc
    // 0xc0a59c: LoadField: r7 = r5->field_1b
    //     0xc0a59c: ldur            w7, [x5, #0x1b]
    // 0xc0a5a0: DecompressPointer r7
    //     0xc0a5a0: add             x7, x7, HEAP, lsl #32
    // 0xc0a5a4: cmp             w7, NULL
    // 0xc0a5a8: b.ne            #0xc0a5b4
    // 0xc0a5ac: r5 = Null
    //     0xc0a5ac: mov             x5, NULL
    // 0xc0a5b0: b               #0xc0a5bc
    // 0xc0a5b4: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xc0a5b4: ldur            w5, [x7, #0x17]
    // 0xc0a5b8: DecompressPointer r5
    //     0xc0a5b8: add             x5, x5, HEAP, lsl #32
    // 0xc0a5bc: cmp             w5, NULL
    // 0xc0a5c0: b.ne            #0xc0a5cc
    // 0xc0a5c4: r5 = 0
    //     0xc0a5c4: movz            x5, #0
    // 0xc0a5c8: b               #0xc0a5dc
    // 0xc0a5cc: r7 = LoadInt32Instr(r5)
    //     0xc0a5cc: sbfx            x7, x5, #1, #0x1f
    //     0xc0a5d0: tbz             w5, #0, #0xc0a5d8
    //     0xc0a5d4: ldur            x7, [x5, #7]
    // 0xc0a5d8: mov             x5, x7
    // 0xc0a5dc: add             x7, x8, x5
    // 0xc0a5e0: scvtf           d0, x3
    // 0xc0a5e4: scvtf           d1, x7
    // 0xc0a5e8: fdiv            d2, d0, d1
    // 0xc0a5ec: cmp             w1, NULL
    // 0xc0a5f0: b.ne            #0xc0a5fc
    // 0xc0a5f4: r1 = Null
    //     0xc0a5f4: mov             x1, NULL
    // 0xc0a5f8: b               #0xc0a638
    // 0xc0a5fc: LoadField: r3 = r1->field_f
    //     0xc0a5fc: ldur            w3, [x1, #0xf]
    // 0xc0a600: DecompressPointer r3
    //     0xc0a600: add             x3, x3, HEAP, lsl #32
    // 0xc0a604: cmp             w3, NULL
    // 0xc0a608: b.ne            #0xc0a614
    // 0xc0a60c: r1 = Null
    //     0xc0a60c: mov             x1, NULL
    // 0xc0a610: b               #0xc0a638
    // 0xc0a614: LoadField: r1 = r3->field_1b
    //     0xc0a614: ldur            w1, [x3, #0x1b]
    // 0xc0a618: DecompressPointer r1
    //     0xc0a618: add             x1, x1, HEAP, lsl #32
    // 0xc0a61c: cmp             w1, NULL
    // 0xc0a620: b.ne            #0xc0a62c
    // 0xc0a624: r1 = Null
    //     0xc0a624: mov             x1, NULL
    // 0xc0a628: b               #0xc0a638
    // 0xc0a62c: LoadField: r3 = r1->field_13
    //     0xc0a62c: ldur            w3, [x1, #0x13]
    // 0xc0a630: DecompressPointer r3
    //     0xc0a630: add             x3, x3, HEAP, lsl #32
    // 0xc0a634: mov             x1, x3
    // 0xc0a638: cmp             w1, NULL
    // 0xc0a63c: b.ne            #0xc0a648
    // 0xc0a640: r5 = 0
    //     0xc0a640: movz            x5, #0
    // 0xc0a644: b               #0xc0a658
    // 0xc0a648: r3 = LoadInt32Instr(r1)
    //     0xc0a648: sbfx            x3, x1, #1, #0x1f
    //     0xc0a64c: tbz             w1, #0, #0xc0a654
    //     0xc0a650: ldur            x3, [x1, #7]
    // 0xc0a654: mov             x5, x3
    // 0xc0a658: mov             x1, x6
    // 0xc0a65c: mov             v0.16b, v2.16b
    // 0xc0a660: r3 = "2"
    //     0xc0a660: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f8b0] "2"
    //     0xc0a664: ldr             x3, [x3, #0x8b0]
    // 0xc0a668: r0 = chartRow()
    //     0xc0a668: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xc0a66c: mov             x4, x0
    // 0xc0a670: ldur            x0, [fp, #-0x18]
    // 0xc0a674: stur            x4, [fp, #-0x50]
    // 0xc0a678: LoadField: r2 = r0->field_13
    //     0xc0a678: ldur            w2, [x0, #0x13]
    // 0xc0a67c: DecompressPointer r2
    //     0xc0a67c: add             x2, x2, HEAP, lsl #32
    // 0xc0a680: ldur            x6, [fp, #-8]
    // 0xc0a684: LoadField: r1 = r6->field_b
    //     0xc0a684: ldur            w1, [x6, #0xb]
    // 0xc0a688: DecompressPointer r1
    //     0xc0a688: add             x1, x1, HEAP, lsl #32
    // 0xc0a68c: cmp             w1, NULL
    // 0xc0a690: b.eq            #0xc0b6f4
    // 0xc0a694: LoadField: r3 = r1->field_f
    //     0xc0a694: ldur            w3, [x1, #0xf]
    // 0xc0a698: DecompressPointer r3
    //     0xc0a698: add             x3, x3, HEAP, lsl #32
    // 0xc0a69c: LoadField: r1 = r3->field_b
    //     0xc0a69c: ldur            w1, [x3, #0xb]
    // 0xc0a6a0: DecompressPointer r1
    //     0xc0a6a0: add             x1, x1, HEAP, lsl #32
    // 0xc0a6a4: cmp             w1, NULL
    // 0xc0a6a8: b.ne            #0xc0a6b4
    // 0xc0a6ac: r3 = Null
    //     0xc0a6ac: mov             x3, NULL
    // 0xc0a6b0: b               #0xc0a6ec
    // 0xc0a6b4: LoadField: r3 = r1->field_f
    //     0xc0a6b4: ldur            w3, [x1, #0xf]
    // 0xc0a6b8: DecompressPointer r3
    //     0xc0a6b8: add             x3, x3, HEAP, lsl #32
    // 0xc0a6bc: cmp             w3, NULL
    // 0xc0a6c0: b.ne            #0xc0a6cc
    // 0xc0a6c4: r3 = Null
    //     0xc0a6c4: mov             x3, NULL
    // 0xc0a6c8: b               #0xc0a6ec
    // 0xc0a6cc: LoadField: r5 = r3->field_1b
    //     0xc0a6cc: ldur            w5, [x3, #0x1b]
    // 0xc0a6d0: DecompressPointer r5
    //     0xc0a6d0: add             x5, x5, HEAP, lsl #32
    // 0xc0a6d4: cmp             w5, NULL
    // 0xc0a6d8: b.ne            #0xc0a6e4
    // 0xc0a6dc: r3 = Null
    //     0xc0a6dc: mov             x3, NULL
    // 0xc0a6e0: b               #0xc0a6ec
    // 0xc0a6e4: ArrayLoad: r3 = r5[0]  ; List_4
    //     0xc0a6e4: ldur            w3, [x5, #0x17]
    // 0xc0a6e8: DecompressPointer r3
    //     0xc0a6e8: add             x3, x3, HEAP, lsl #32
    // 0xc0a6ec: cmp             w3, NULL
    // 0xc0a6f0: b.ne            #0xc0a6fc
    // 0xc0a6f4: r3 = 0
    //     0xc0a6f4: movz            x3, #0
    // 0xc0a6f8: b               #0xc0a70c
    // 0xc0a6fc: r5 = LoadInt32Instr(r3)
    //     0xc0a6fc: sbfx            x5, x3, #1, #0x1f
    //     0xc0a700: tbz             w3, #0, #0xc0a708
    //     0xc0a704: ldur            x5, [x3, #7]
    // 0xc0a708: mov             x3, x5
    // 0xc0a70c: cmp             w1, NULL
    // 0xc0a710: b.ne            #0xc0a71c
    // 0xc0a714: r5 = Null
    //     0xc0a714: mov             x5, NULL
    // 0xc0a718: b               #0xc0a754
    // 0xc0a71c: LoadField: r5 = r1->field_f
    //     0xc0a71c: ldur            w5, [x1, #0xf]
    // 0xc0a720: DecompressPointer r5
    //     0xc0a720: add             x5, x5, HEAP, lsl #32
    // 0xc0a724: cmp             w5, NULL
    // 0xc0a728: b.ne            #0xc0a734
    // 0xc0a72c: r5 = Null
    //     0xc0a72c: mov             x5, NULL
    // 0xc0a730: b               #0xc0a754
    // 0xc0a734: LoadField: r7 = r5->field_1b
    //     0xc0a734: ldur            w7, [x5, #0x1b]
    // 0xc0a738: DecompressPointer r7
    //     0xc0a738: add             x7, x7, HEAP, lsl #32
    // 0xc0a73c: cmp             w7, NULL
    // 0xc0a740: b.ne            #0xc0a74c
    // 0xc0a744: r5 = Null
    //     0xc0a744: mov             x5, NULL
    // 0xc0a748: b               #0xc0a754
    // 0xc0a74c: LoadField: r5 = r7->field_7
    //     0xc0a74c: ldur            w5, [x7, #7]
    // 0xc0a750: DecompressPointer r5
    //     0xc0a750: add             x5, x5, HEAP, lsl #32
    // 0xc0a754: cmp             w5, NULL
    // 0xc0a758: b.ne            #0xc0a764
    // 0xc0a75c: r5 = 0
    //     0xc0a75c: movz            x5, #0
    // 0xc0a760: b               #0xc0a774
    // 0xc0a764: r7 = LoadInt32Instr(r5)
    //     0xc0a764: sbfx            x7, x5, #1, #0x1f
    //     0xc0a768: tbz             w5, #0, #0xc0a770
    //     0xc0a76c: ldur            x7, [x5, #7]
    // 0xc0a770: mov             x5, x7
    // 0xc0a774: cmp             w1, NULL
    // 0xc0a778: b.ne            #0xc0a784
    // 0xc0a77c: r7 = Null
    //     0xc0a77c: mov             x7, NULL
    // 0xc0a780: b               #0xc0a7bc
    // 0xc0a784: LoadField: r7 = r1->field_f
    //     0xc0a784: ldur            w7, [x1, #0xf]
    // 0xc0a788: DecompressPointer r7
    //     0xc0a788: add             x7, x7, HEAP, lsl #32
    // 0xc0a78c: cmp             w7, NULL
    // 0xc0a790: b.ne            #0xc0a79c
    // 0xc0a794: r7 = Null
    //     0xc0a794: mov             x7, NULL
    // 0xc0a798: b               #0xc0a7bc
    // 0xc0a79c: LoadField: r8 = r7->field_1b
    //     0xc0a79c: ldur            w8, [x7, #0x1b]
    // 0xc0a7a0: DecompressPointer r8
    //     0xc0a7a0: add             x8, x8, HEAP, lsl #32
    // 0xc0a7a4: cmp             w8, NULL
    // 0xc0a7a8: b.ne            #0xc0a7b4
    // 0xc0a7ac: r7 = Null
    //     0xc0a7ac: mov             x7, NULL
    // 0xc0a7b0: b               #0xc0a7bc
    // 0xc0a7b4: LoadField: r7 = r8->field_b
    //     0xc0a7b4: ldur            w7, [x8, #0xb]
    // 0xc0a7b8: DecompressPointer r7
    //     0xc0a7b8: add             x7, x7, HEAP, lsl #32
    // 0xc0a7bc: cmp             w7, NULL
    // 0xc0a7c0: b.ne            #0xc0a7cc
    // 0xc0a7c4: r7 = 0
    //     0xc0a7c4: movz            x7, #0
    // 0xc0a7c8: b               #0xc0a7dc
    // 0xc0a7cc: r8 = LoadInt32Instr(r7)
    //     0xc0a7cc: sbfx            x8, x7, #1, #0x1f
    //     0xc0a7d0: tbz             w7, #0, #0xc0a7d8
    //     0xc0a7d4: ldur            x8, [x7, #7]
    // 0xc0a7d8: mov             x7, x8
    // 0xc0a7dc: add             x8, x5, x7
    // 0xc0a7e0: cmp             w1, NULL
    // 0xc0a7e4: b.ne            #0xc0a7f0
    // 0xc0a7e8: r5 = Null
    //     0xc0a7e8: mov             x5, NULL
    // 0xc0a7ec: b               #0xc0a828
    // 0xc0a7f0: LoadField: r5 = r1->field_f
    //     0xc0a7f0: ldur            w5, [x1, #0xf]
    // 0xc0a7f4: DecompressPointer r5
    //     0xc0a7f4: add             x5, x5, HEAP, lsl #32
    // 0xc0a7f8: cmp             w5, NULL
    // 0xc0a7fc: b.ne            #0xc0a808
    // 0xc0a800: r5 = Null
    //     0xc0a800: mov             x5, NULL
    // 0xc0a804: b               #0xc0a828
    // 0xc0a808: LoadField: r7 = r5->field_1b
    //     0xc0a808: ldur            w7, [x5, #0x1b]
    // 0xc0a80c: DecompressPointer r7
    //     0xc0a80c: add             x7, x7, HEAP, lsl #32
    // 0xc0a810: cmp             w7, NULL
    // 0xc0a814: b.ne            #0xc0a820
    // 0xc0a818: r5 = Null
    //     0xc0a818: mov             x5, NULL
    // 0xc0a81c: b               #0xc0a828
    // 0xc0a820: LoadField: r5 = r7->field_f
    //     0xc0a820: ldur            w5, [x7, #0xf]
    // 0xc0a824: DecompressPointer r5
    //     0xc0a824: add             x5, x5, HEAP, lsl #32
    // 0xc0a828: cmp             w5, NULL
    // 0xc0a82c: b.ne            #0xc0a838
    // 0xc0a830: r5 = 0
    //     0xc0a830: movz            x5, #0
    // 0xc0a834: b               #0xc0a848
    // 0xc0a838: r7 = LoadInt32Instr(r5)
    //     0xc0a838: sbfx            x7, x5, #1, #0x1f
    //     0xc0a83c: tbz             w5, #0, #0xc0a844
    //     0xc0a840: ldur            x7, [x5, #7]
    // 0xc0a844: mov             x5, x7
    // 0xc0a848: add             x7, x8, x5
    // 0xc0a84c: cmp             w1, NULL
    // 0xc0a850: b.ne            #0xc0a85c
    // 0xc0a854: r5 = Null
    //     0xc0a854: mov             x5, NULL
    // 0xc0a858: b               #0xc0a894
    // 0xc0a85c: LoadField: r5 = r1->field_f
    //     0xc0a85c: ldur            w5, [x1, #0xf]
    // 0xc0a860: DecompressPointer r5
    //     0xc0a860: add             x5, x5, HEAP, lsl #32
    // 0xc0a864: cmp             w5, NULL
    // 0xc0a868: b.ne            #0xc0a874
    // 0xc0a86c: r5 = Null
    //     0xc0a86c: mov             x5, NULL
    // 0xc0a870: b               #0xc0a894
    // 0xc0a874: LoadField: r8 = r5->field_1b
    //     0xc0a874: ldur            w8, [x5, #0x1b]
    // 0xc0a878: DecompressPointer r8
    //     0xc0a878: add             x8, x8, HEAP, lsl #32
    // 0xc0a87c: cmp             w8, NULL
    // 0xc0a880: b.ne            #0xc0a88c
    // 0xc0a884: r5 = Null
    //     0xc0a884: mov             x5, NULL
    // 0xc0a888: b               #0xc0a894
    // 0xc0a88c: LoadField: r5 = r8->field_13
    //     0xc0a88c: ldur            w5, [x8, #0x13]
    // 0xc0a890: DecompressPointer r5
    //     0xc0a890: add             x5, x5, HEAP, lsl #32
    // 0xc0a894: cmp             w5, NULL
    // 0xc0a898: b.ne            #0xc0a8a4
    // 0xc0a89c: r5 = 0
    //     0xc0a89c: movz            x5, #0
    // 0xc0a8a0: b               #0xc0a8b4
    // 0xc0a8a4: r8 = LoadInt32Instr(r5)
    //     0xc0a8a4: sbfx            x8, x5, #1, #0x1f
    //     0xc0a8a8: tbz             w5, #0, #0xc0a8b0
    //     0xc0a8ac: ldur            x8, [x5, #7]
    // 0xc0a8b0: mov             x5, x8
    // 0xc0a8b4: add             x8, x7, x5
    // 0xc0a8b8: cmp             w1, NULL
    // 0xc0a8bc: b.ne            #0xc0a8c8
    // 0xc0a8c0: r5 = Null
    //     0xc0a8c0: mov             x5, NULL
    // 0xc0a8c4: b               #0xc0a900
    // 0xc0a8c8: LoadField: r5 = r1->field_f
    //     0xc0a8c8: ldur            w5, [x1, #0xf]
    // 0xc0a8cc: DecompressPointer r5
    //     0xc0a8cc: add             x5, x5, HEAP, lsl #32
    // 0xc0a8d0: cmp             w5, NULL
    // 0xc0a8d4: b.ne            #0xc0a8e0
    // 0xc0a8d8: r5 = Null
    //     0xc0a8d8: mov             x5, NULL
    // 0xc0a8dc: b               #0xc0a900
    // 0xc0a8e0: LoadField: r7 = r5->field_1b
    //     0xc0a8e0: ldur            w7, [x5, #0x1b]
    // 0xc0a8e4: DecompressPointer r7
    //     0xc0a8e4: add             x7, x7, HEAP, lsl #32
    // 0xc0a8e8: cmp             w7, NULL
    // 0xc0a8ec: b.ne            #0xc0a8f8
    // 0xc0a8f0: r5 = Null
    //     0xc0a8f0: mov             x5, NULL
    // 0xc0a8f4: b               #0xc0a900
    // 0xc0a8f8: ArrayLoad: r5 = r7[0]  ; List_4
    //     0xc0a8f8: ldur            w5, [x7, #0x17]
    // 0xc0a8fc: DecompressPointer r5
    //     0xc0a8fc: add             x5, x5, HEAP, lsl #32
    // 0xc0a900: cmp             w5, NULL
    // 0xc0a904: b.ne            #0xc0a910
    // 0xc0a908: r5 = 0
    //     0xc0a908: movz            x5, #0
    // 0xc0a90c: b               #0xc0a920
    // 0xc0a910: r7 = LoadInt32Instr(r5)
    //     0xc0a910: sbfx            x7, x5, #1, #0x1f
    //     0xc0a914: tbz             w5, #0, #0xc0a91c
    //     0xc0a918: ldur            x7, [x5, #7]
    // 0xc0a91c: mov             x5, x7
    // 0xc0a920: add             x7, x8, x5
    // 0xc0a924: scvtf           d0, x3
    // 0xc0a928: scvtf           d1, x7
    // 0xc0a92c: fdiv            d2, d0, d1
    // 0xc0a930: cmp             w1, NULL
    // 0xc0a934: b.ne            #0xc0a940
    // 0xc0a938: r1 = Null
    //     0xc0a938: mov             x1, NULL
    // 0xc0a93c: b               #0xc0a97c
    // 0xc0a940: LoadField: r3 = r1->field_f
    //     0xc0a940: ldur            w3, [x1, #0xf]
    // 0xc0a944: DecompressPointer r3
    //     0xc0a944: add             x3, x3, HEAP, lsl #32
    // 0xc0a948: cmp             w3, NULL
    // 0xc0a94c: b.ne            #0xc0a958
    // 0xc0a950: r1 = Null
    //     0xc0a950: mov             x1, NULL
    // 0xc0a954: b               #0xc0a97c
    // 0xc0a958: LoadField: r1 = r3->field_1b
    //     0xc0a958: ldur            w1, [x3, #0x1b]
    // 0xc0a95c: DecompressPointer r1
    //     0xc0a95c: add             x1, x1, HEAP, lsl #32
    // 0xc0a960: cmp             w1, NULL
    // 0xc0a964: b.ne            #0xc0a970
    // 0xc0a968: r1 = Null
    //     0xc0a968: mov             x1, NULL
    // 0xc0a96c: b               #0xc0a97c
    // 0xc0a970: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xc0a970: ldur            w3, [x1, #0x17]
    // 0xc0a974: DecompressPointer r3
    //     0xc0a974: add             x3, x3, HEAP, lsl #32
    // 0xc0a978: mov             x1, x3
    // 0xc0a97c: cmp             w1, NULL
    // 0xc0a980: b.ne            #0xc0a98c
    // 0xc0a984: r5 = 0
    //     0xc0a984: movz            x5, #0
    // 0xc0a988: b               #0xc0a99c
    // 0xc0a98c: r3 = LoadInt32Instr(r1)
    //     0xc0a98c: sbfx            x3, x1, #1, #0x1f
    //     0xc0a990: tbz             w1, #0, #0xc0a998
    //     0xc0a994: ldur            x3, [x1, #7]
    // 0xc0a998: mov             x5, x3
    // 0xc0a99c: ldur            x11, [fp, #-0x20]
    // 0xc0a9a0: ldur            x10, [fp, #-0x28]
    // 0xc0a9a4: ldur            x9, [fp, #-0x30]
    // 0xc0a9a8: ldur            x8, [fp, #-0x38]
    // 0xc0a9ac: ldur            x7, [fp, #-0x48]
    // 0xc0a9b0: ldur            x12, [fp, #-0x10]
    // 0xc0a9b4: mov             x1, x6
    // 0xc0a9b8: mov             v0.16b, v2.16b
    // 0xc0a9bc: r3 = "1"
    //     0xc0a9bc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0xc0a9c0: ldr             x3, [x3, #0xba8]
    // 0xc0a9c4: r0 = chartRow()
    //     0xc0a9c4: bl              #0x9b6180  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::chartRow
    // 0xc0a9c8: r1 = Null
    //     0xc0a9c8: mov             x1, NULL
    // 0xc0a9cc: r2 = 14
    //     0xc0a9cc: movz            x2, #0xe
    // 0xc0a9d0: stur            x0, [fp, #-0x58]
    // 0xc0a9d4: r0 = AllocateArray()
    //     0xc0a9d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0a9d8: stur            x0, [fp, #-0x60]
    // 0xc0a9dc: r16 = Instance_SizedBox
    //     0xc0a9dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xc0a9e0: ldr             x16, [x16, #0x8b8]
    // 0xc0a9e4: StoreField: r0->field_f = r16
    //     0xc0a9e4: stur            w16, [x0, #0xf]
    // 0xc0a9e8: ldur            x1, [fp, #-0x30]
    // 0xc0a9ec: StoreField: r0->field_13 = r1
    //     0xc0a9ec: stur            w1, [x0, #0x13]
    // 0xc0a9f0: ldur            x1, [fp, #-0x38]
    // 0xc0a9f4: ArrayStore: r0[0] = r1  ; List_4
    //     0xc0a9f4: stur            w1, [x0, #0x17]
    // 0xc0a9f8: ldur            x1, [fp, #-0x48]
    // 0xc0a9fc: StoreField: r0->field_1b = r1
    //     0xc0a9fc: stur            w1, [x0, #0x1b]
    // 0xc0aa00: ldur            x1, [fp, #-0x50]
    // 0xc0aa04: StoreField: r0->field_1f = r1
    //     0xc0aa04: stur            w1, [x0, #0x1f]
    // 0xc0aa08: ldur            x1, [fp, #-0x58]
    // 0xc0aa0c: StoreField: r0->field_23 = r1
    //     0xc0aa0c: stur            w1, [x0, #0x23]
    // 0xc0aa10: r16 = Instance_SizedBox
    //     0xc0aa10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8b8] Obj!SizedBox@d67d81
    //     0xc0aa14: ldr             x16, [x16, #0x8b8]
    // 0xc0aa18: StoreField: r0->field_27 = r16
    //     0xc0aa18: stur            w16, [x0, #0x27]
    // 0xc0aa1c: r1 = <Widget>
    //     0xc0aa1c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0aa20: r0 = AllocateGrowableArray()
    //     0xc0aa20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0aa24: mov             x1, x0
    // 0xc0aa28: ldur            x0, [fp, #-0x60]
    // 0xc0aa2c: stur            x1, [fp, #-0x30]
    // 0xc0aa30: StoreField: r1->field_f = r0
    //     0xc0aa30: stur            w0, [x1, #0xf]
    // 0xc0aa34: r0 = 14
    //     0xc0aa34: movz            x0, #0xe
    // 0xc0aa38: StoreField: r1->field_b = r0
    //     0xc0aa38: stur            w0, [x1, #0xb]
    // 0xc0aa3c: r0 = Column()
    //     0xc0aa3c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0aa40: mov             x1, x0
    // 0xc0aa44: r0 = Instance_Axis
    //     0xc0aa44: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0aa48: stur            x1, [fp, #-0x38]
    // 0xc0aa4c: StoreField: r1->field_f = r0
    //     0xc0aa4c: stur            w0, [x1, #0xf]
    // 0xc0aa50: r2 = Instance_MainAxisAlignment
    //     0xc0aa50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0aa54: ldr             x2, [x2, #0xa08]
    // 0xc0aa58: StoreField: r1->field_13 = r2
    //     0xc0aa58: stur            w2, [x1, #0x13]
    // 0xc0aa5c: r3 = Instance_MainAxisSize
    //     0xc0aa5c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0aa60: ldr             x3, [x3, #0xa10]
    // 0xc0aa64: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0aa64: stur            w3, [x1, #0x17]
    // 0xc0aa68: r4 = Instance_CrossAxisAlignment
    //     0xc0aa68: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0aa6c: ldr             x4, [x4, #0x890]
    // 0xc0aa70: StoreField: r1->field_1b = r4
    //     0xc0aa70: stur            w4, [x1, #0x1b]
    // 0xc0aa74: r5 = Instance_VerticalDirection
    //     0xc0aa74: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0aa78: ldr             x5, [x5, #0xa20]
    // 0xc0aa7c: StoreField: r1->field_23 = r5
    //     0xc0aa7c: stur            w5, [x1, #0x23]
    // 0xc0aa80: r6 = Instance_Clip
    //     0xc0aa80: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0aa84: ldr             x6, [x6, #0x38]
    // 0xc0aa88: StoreField: r1->field_2b = r6
    //     0xc0aa88: stur            w6, [x1, #0x2b]
    // 0xc0aa8c: StoreField: r1->field_2f = rZR
    //     0xc0aa8c: stur            xzr, [x1, #0x2f]
    // 0xc0aa90: ldur            x7, [fp, #-0x30]
    // 0xc0aa94: StoreField: r1->field_b = r7
    //     0xc0aa94: stur            w7, [x1, #0xb]
    // 0xc0aa98: r0 = InkWell()
    //     0xc0aa98: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc0aa9c: mov             x3, x0
    // 0xc0aaa0: ldur            x0, [fp, #-0x38]
    // 0xc0aaa4: stur            x3, [fp, #-0x30]
    // 0xc0aaa8: StoreField: r3->field_b = r0
    //     0xc0aaa8: stur            w0, [x3, #0xb]
    // 0xc0aaac: ldur            x2, [fp, #-0x18]
    // 0xc0aab0: r1 = Function '<anonymous closure>':.
    //     0xc0aab0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52440] AnonymousClosure: (0xc0b83c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xc0aab4: ldr             x1, [x1, #0x440]
    // 0xc0aab8: r0 = AllocateClosure()
    //     0xc0aab8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0aabc: mov             x1, x0
    // 0xc0aac0: ldur            x0, [fp, #-0x30]
    // 0xc0aac4: StoreField: r0->field_f = r1
    //     0xc0aac4: stur            w1, [x0, #0xf]
    // 0xc0aac8: r3 = true
    //     0xc0aac8: add             x3, NULL, #0x20  ; true
    // 0xc0aacc: StoreField: r0->field_43 = r3
    //     0xc0aacc: stur            w3, [x0, #0x43]
    // 0xc0aad0: r4 = Instance_BoxShape
    //     0xc0aad0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0aad4: ldr             x4, [x4, #0x80]
    // 0xc0aad8: StoreField: r0->field_47 = r4
    //     0xc0aad8: stur            w4, [x0, #0x47]
    // 0xc0aadc: StoreField: r0->field_6f = r3
    //     0xc0aadc: stur            w3, [x0, #0x6f]
    // 0xc0aae0: r5 = false
    //     0xc0aae0: add             x5, NULL, #0x30  ; false
    // 0xc0aae4: StoreField: r0->field_73 = r5
    //     0xc0aae4: stur            w5, [x0, #0x73]
    // 0xc0aae8: StoreField: r0->field_83 = r3
    //     0xc0aae8: stur            w3, [x0, #0x83]
    // 0xc0aaec: StoreField: r0->field_7b = r5
    //     0xc0aaec: stur            w5, [x0, #0x7b]
    // 0xc0aaf0: r1 = Null
    //     0xc0aaf0: mov             x1, NULL
    // 0xc0aaf4: r2 = 4
    //     0xc0aaf4: movz            x2, #0x4
    // 0xc0aaf8: r0 = AllocateArray()
    //     0xc0aaf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0aafc: mov             x2, x0
    // 0xc0ab00: ldur            x0, [fp, #-0x28]
    // 0xc0ab04: stur            x2, [fp, #-0x38]
    // 0xc0ab08: StoreField: r2->field_f = r0
    //     0xc0ab08: stur            w0, [x2, #0xf]
    // 0xc0ab0c: ldur            x0, [fp, #-0x30]
    // 0xc0ab10: StoreField: r2->field_13 = r0
    //     0xc0ab10: stur            w0, [x2, #0x13]
    // 0xc0ab14: r1 = <Widget>
    //     0xc0ab14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0ab18: r0 = AllocateGrowableArray()
    //     0xc0ab18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0ab1c: mov             x1, x0
    // 0xc0ab20: ldur            x0, [fp, #-0x38]
    // 0xc0ab24: stur            x1, [fp, #-0x28]
    // 0xc0ab28: StoreField: r1->field_f = r0
    //     0xc0ab28: stur            w0, [x1, #0xf]
    // 0xc0ab2c: r2 = 4
    //     0xc0ab2c: movz            x2, #0x4
    // 0xc0ab30: StoreField: r1->field_b = r2
    //     0xc0ab30: stur            w2, [x1, #0xb]
    // 0xc0ab34: r0 = Row()
    //     0xc0ab34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc0ab38: mov             x3, x0
    // 0xc0ab3c: r0 = Instance_Axis
    //     0xc0ab3c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0ab40: stur            x3, [fp, #-0x30]
    // 0xc0ab44: StoreField: r3->field_f = r0
    //     0xc0ab44: stur            w0, [x3, #0xf]
    // 0xc0ab48: r0 = Instance_MainAxisAlignment
    //     0xc0ab48: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xc0ab4c: ldr             x0, [x0, #0xa8]
    // 0xc0ab50: StoreField: r3->field_13 = r0
    //     0xc0ab50: stur            w0, [x3, #0x13]
    // 0xc0ab54: r0 = Instance_MainAxisSize
    //     0xc0ab54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0ab58: ldr             x0, [x0, #0xa10]
    // 0xc0ab5c: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0ab5c: stur            w0, [x3, #0x17]
    // 0xc0ab60: r4 = Instance_CrossAxisAlignment
    //     0xc0ab60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0ab64: ldr             x4, [x4, #0xa18]
    // 0xc0ab68: StoreField: r3->field_1b = r4
    //     0xc0ab68: stur            w4, [x3, #0x1b]
    // 0xc0ab6c: r5 = Instance_VerticalDirection
    //     0xc0ab6c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0ab70: ldr             x5, [x5, #0xa20]
    // 0xc0ab74: StoreField: r3->field_23 = r5
    //     0xc0ab74: stur            w5, [x3, #0x23]
    // 0xc0ab78: r6 = Instance_Clip
    //     0xc0ab78: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0ab7c: ldr             x6, [x6, #0x38]
    // 0xc0ab80: StoreField: r3->field_2b = r6
    //     0xc0ab80: stur            w6, [x3, #0x2b]
    // 0xc0ab84: StoreField: r3->field_2f = rZR
    //     0xc0ab84: stur            xzr, [x3, #0x2f]
    // 0xc0ab88: ldur            x1, [fp, #-0x28]
    // 0xc0ab8c: StoreField: r3->field_b = r1
    //     0xc0ab8c: stur            w1, [x3, #0xb]
    // 0xc0ab90: r1 = Null
    //     0xc0ab90: mov             x1, NULL
    // 0xc0ab94: r2 = 4
    //     0xc0ab94: movz            x2, #0x4
    // 0xc0ab98: r0 = AllocateArray()
    //     0xc0ab98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0ab9c: mov             x2, x0
    // 0xc0aba0: ldur            x0, [fp, #-0x20]
    // 0xc0aba4: stur            x2, [fp, #-0x28]
    // 0xc0aba8: StoreField: r2->field_f = r0
    //     0xc0aba8: stur            w0, [x2, #0xf]
    // 0xc0abac: ldur            x0, [fp, #-0x30]
    // 0xc0abb0: StoreField: r2->field_13 = r0
    //     0xc0abb0: stur            w0, [x2, #0x13]
    // 0xc0abb4: r1 = <Widget>
    //     0xc0abb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0abb8: r0 = AllocateGrowableArray()
    //     0xc0abb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0abbc: mov             x1, x0
    // 0xc0abc0: ldur            x0, [fp, #-0x28]
    // 0xc0abc4: stur            x1, [fp, #-0x20]
    // 0xc0abc8: StoreField: r1->field_f = r0
    //     0xc0abc8: stur            w0, [x1, #0xf]
    // 0xc0abcc: r0 = 4
    //     0xc0abcc: movz            x0, #0x4
    // 0xc0abd0: StoreField: r1->field_b = r0
    //     0xc0abd0: stur            w0, [x1, #0xb]
    // 0xc0abd4: r0 = Column()
    //     0xc0abd4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0abd8: mov             x1, x0
    // 0xc0abdc: r0 = Instance_Axis
    //     0xc0abdc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0abe0: stur            x1, [fp, #-0x28]
    // 0xc0abe4: StoreField: r1->field_f = r0
    //     0xc0abe4: stur            w0, [x1, #0xf]
    // 0xc0abe8: r2 = Instance_MainAxisAlignment
    //     0xc0abe8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0abec: ldr             x2, [x2, #0xa08]
    // 0xc0abf0: StoreField: r1->field_13 = r2
    //     0xc0abf0: stur            w2, [x1, #0x13]
    // 0xc0abf4: r3 = Instance_MainAxisSize
    //     0xc0abf4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0abf8: ldr             x3, [x3, #0xa10]
    // 0xc0abfc: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0abfc: stur            w3, [x1, #0x17]
    // 0xc0ac00: r4 = Instance_CrossAxisAlignment
    //     0xc0ac00: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0ac04: ldr             x4, [x4, #0x890]
    // 0xc0ac08: StoreField: r1->field_1b = r4
    //     0xc0ac08: stur            w4, [x1, #0x1b]
    // 0xc0ac0c: r4 = Instance_VerticalDirection
    //     0xc0ac0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0ac10: ldr             x4, [x4, #0xa20]
    // 0xc0ac14: StoreField: r1->field_23 = r4
    //     0xc0ac14: stur            w4, [x1, #0x23]
    // 0xc0ac18: r5 = Instance_Clip
    //     0xc0ac18: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0ac1c: ldr             x5, [x5, #0x38]
    // 0xc0ac20: StoreField: r1->field_2b = r5
    //     0xc0ac20: stur            w5, [x1, #0x2b]
    // 0xc0ac24: StoreField: r1->field_2f = rZR
    //     0xc0ac24: stur            xzr, [x1, #0x2f]
    // 0xc0ac28: ldur            x6, [fp, #-0x20]
    // 0xc0ac2c: StoreField: r1->field_b = r6
    //     0xc0ac2c: stur            w6, [x1, #0xb]
    // 0xc0ac30: r0 = Visibility()
    //     0xc0ac30: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0ac34: mov             x2, x0
    // 0xc0ac38: ldur            x0, [fp, #-0x28]
    // 0xc0ac3c: stur            x2, [fp, #-0x20]
    // 0xc0ac40: StoreField: r2->field_b = r0
    //     0xc0ac40: stur            w0, [x2, #0xb]
    // 0xc0ac44: r0 = Instance_SizedBox
    //     0xc0ac44: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc0ac48: StoreField: r2->field_f = r0
    //     0xc0ac48: stur            w0, [x2, #0xf]
    // 0xc0ac4c: ldur            x1, [fp, #-0x10]
    // 0xc0ac50: StoreField: r2->field_13 = r1
    //     0xc0ac50: stur            w1, [x2, #0x13]
    // 0xc0ac54: r3 = false
    //     0xc0ac54: add             x3, NULL, #0x30  ; false
    // 0xc0ac58: ArrayStore: r2[0] = r3  ; List_4
    //     0xc0ac58: stur            w3, [x2, #0x17]
    // 0xc0ac5c: StoreField: r2->field_1b = r3
    //     0xc0ac5c: stur            w3, [x2, #0x1b]
    // 0xc0ac60: StoreField: r2->field_1f = r3
    //     0xc0ac60: stur            w3, [x2, #0x1f]
    // 0xc0ac64: StoreField: r2->field_23 = r3
    //     0xc0ac64: stur            w3, [x2, #0x23]
    // 0xc0ac68: StoreField: r2->field_27 = r3
    //     0xc0ac68: stur            w3, [x2, #0x27]
    // 0xc0ac6c: StoreField: r2->field_2b = r3
    //     0xc0ac6c: stur            w3, [x2, #0x2b]
    // 0xc0ac70: ldur            x4, [fp, #-8]
    // 0xc0ac74: LoadField: r1 = r4->field_b
    //     0xc0ac74: ldur            w1, [x4, #0xb]
    // 0xc0ac78: DecompressPointer r1
    //     0xc0ac78: add             x1, x1, HEAP, lsl #32
    // 0xc0ac7c: cmp             w1, NULL
    // 0xc0ac80: b.eq            #0xc0b6f8
    // 0xc0ac84: LoadField: r5 = r1->field_f
    //     0xc0ac84: ldur            w5, [x1, #0xf]
    // 0xc0ac88: DecompressPointer r5
    //     0xc0ac88: add             x5, x5, HEAP, lsl #32
    // 0xc0ac8c: LoadField: r1 = r5->field_b
    //     0xc0ac8c: ldur            w1, [x5, #0xb]
    // 0xc0ac90: DecompressPointer r1
    //     0xc0ac90: add             x1, x1, HEAP, lsl #32
    // 0xc0ac94: cmp             w1, NULL
    // 0xc0ac98: b.ne            #0xc0aca4
    // 0xc0ac9c: r1 = Null
    //     0xc0ac9c: mov             x1, NULL
    // 0xc0aca0: b               #0xc0ace8
    // 0xc0aca4: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xc0aca4: ldur            w5, [x1, #0x17]
    // 0xc0aca8: DecompressPointer r5
    //     0xc0aca8: add             x5, x5, HEAP, lsl #32
    // 0xc0acac: cmp             w5, NULL
    // 0xc0acb0: b.ne            #0xc0acbc
    // 0xc0acb4: r1 = Null
    //     0xc0acb4: mov             x1, NULL
    // 0xc0acb8: b               #0xc0ace8
    // 0xc0acbc: LoadField: r1 = r5->field_f
    //     0xc0acbc: ldur            w1, [x5, #0xf]
    // 0xc0acc0: DecompressPointer r1
    //     0xc0acc0: add             x1, x1, HEAP, lsl #32
    // 0xc0acc4: cmp             w1, NULL
    // 0xc0acc8: b.ne            #0xc0acd4
    // 0xc0accc: r1 = Null
    //     0xc0accc: mov             x1, NULL
    // 0xc0acd0: b               #0xc0ace8
    // 0xc0acd4: LoadField: r5 = r1->field_b
    //     0xc0acd4: ldur            w5, [x1, #0xb]
    // 0xc0acd8: cbnz            w5, #0xc0ace4
    // 0xc0acdc: r1 = false
    //     0xc0acdc: add             x1, NULL, #0x30  ; false
    // 0xc0ace0: b               #0xc0ace8
    // 0xc0ace4: r1 = true
    //     0xc0ace4: add             x1, NULL, #0x20  ; true
    // 0xc0ace8: cmp             w1, NULL
    // 0xc0acec: b.ne            #0xc0acf8
    // 0xc0acf0: r6 = false
    //     0xc0acf0: add             x6, NULL, #0x30  ; false
    // 0xc0acf4: b               #0xc0acfc
    // 0xc0acf8: mov             x6, x1
    // 0xc0acfc: ldur            x5, [fp, #-0x18]
    // 0xc0ad00: stur            x6, [fp, #-0x10]
    // 0xc0ad04: LoadField: r1 = r5->field_13
    //     0xc0ad04: ldur            w1, [x5, #0x13]
    // 0xc0ad08: DecompressPointer r1
    //     0xc0ad08: add             x1, x1, HEAP, lsl #32
    // 0xc0ad0c: r0 = of()
    //     0xc0ad0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0ad10: LoadField: r1 = r0->field_87
    //     0xc0ad10: ldur            w1, [x0, #0x87]
    // 0xc0ad14: DecompressPointer r1
    //     0xc0ad14: add             x1, x1, HEAP, lsl #32
    // 0xc0ad18: LoadField: r0 = r1->field_2f
    //     0xc0ad18: ldur            w0, [x1, #0x2f]
    // 0xc0ad1c: DecompressPointer r0
    //     0xc0ad1c: add             x0, x0, HEAP, lsl #32
    // 0xc0ad20: cmp             w0, NULL
    // 0xc0ad24: b.ne            #0xc0ad30
    // 0xc0ad28: r1 = Null
    //     0xc0ad28: mov             x1, NULL
    // 0xc0ad2c: b               #0xc0ad54
    // 0xc0ad30: r16 = Instance_Color
    //     0xc0ad30: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0ad34: r30 = 14.000000
    //     0xc0ad34: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc0ad38: ldr             lr, [lr, #0x1d8]
    // 0xc0ad3c: stp             lr, x16, [SP]
    // 0xc0ad40: mov             x1, x0
    // 0xc0ad44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0ad44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0ad48: ldr             x4, [x4, #0x9b8]
    // 0xc0ad4c: r0 = copyWith()
    //     0xc0ad4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0ad50: mov             x1, x0
    // 0xc0ad54: ldur            x0, [fp, #-8]
    // 0xc0ad58: stur            x1, [fp, #-0x28]
    // 0xc0ad5c: r0 = Text()
    //     0xc0ad5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0ad60: mov             x3, x0
    // 0xc0ad64: r0 = "Real images from customers"
    //     0xc0ad64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f088] "Real images from customers"
    //     0xc0ad68: ldr             x0, [x0, #0x88]
    // 0xc0ad6c: stur            x3, [fp, #-0x30]
    // 0xc0ad70: StoreField: r3->field_b = r0
    //     0xc0ad70: stur            w0, [x3, #0xb]
    // 0xc0ad74: ldur            x0, [fp, #-0x28]
    // 0xc0ad78: StoreField: r3->field_13 = r0
    //     0xc0ad78: stur            w0, [x3, #0x13]
    // 0xc0ad7c: ldur            x0, [fp, #-8]
    // 0xc0ad80: LoadField: r1 = r0->field_b
    //     0xc0ad80: ldur            w1, [x0, #0xb]
    // 0xc0ad84: DecompressPointer r1
    //     0xc0ad84: add             x1, x1, HEAP, lsl #32
    // 0xc0ad88: cmp             w1, NULL
    // 0xc0ad8c: b.eq            #0xc0b6fc
    // 0xc0ad90: LoadField: r2 = r1->field_f
    //     0xc0ad90: ldur            w2, [x1, #0xf]
    // 0xc0ad94: DecompressPointer r2
    //     0xc0ad94: add             x2, x2, HEAP, lsl #32
    // 0xc0ad98: LoadField: r1 = r2->field_b
    //     0xc0ad98: ldur            w1, [x2, #0xb]
    // 0xc0ad9c: DecompressPointer r1
    //     0xc0ad9c: add             x1, x1, HEAP, lsl #32
    // 0xc0ada0: cmp             w1, NULL
    // 0xc0ada4: b.ne            #0xc0adb0
    // 0xc0ada8: r0 = Null
    //     0xc0ada8: mov             x0, NULL
    // 0xc0adac: b               #0xc0ae10
    // 0xc0adb0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc0adb0: ldur            w2, [x1, #0x17]
    // 0xc0adb4: DecompressPointer r2
    //     0xc0adb4: add             x2, x2, HEAP, lsl #32
    // 0xc0adb8: cmp             w2, NULL
    // 0xc0adbc: b.ne            #0xc0adc8
    // 0xc0adc0: r0 = Null
    //     0xc0adc0: mov             x0, NULL
    // 0xc0adc4: b               #0xc0ae10
    // 0xc0adc8: LoadField: r4 = r2->field_f
    //     0xc0adc8: ldur            w4, [x2, #0xf]
    // 0xc0adcc: DecompressPointer r4
    //     0xc0adcc: add             x4, x4, HEAP, lsl #32
    // 0xc0add0: stur            x4, [fp, #-0x28]
    // 0xc0add4: cmp             w4, NULL
    // 0xc0add8: b.ne            #0xc0ade4
    // 0xc0addc: r0 = Null
    //     0xc0addc: mov             x0, NULL
    // 0xc0ade0: b               #0xc0ae10
    // 0xc0ade4: r1 = Function '<anonymous closure>':.
    //     0xc0ade4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52448] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xc0ade8: ldr             x1, [x1, #0x448]
    // 0xc0adec: r2 = Null
    //     0xc0adec: mov             x2, NULL
    // 0xc0adf0: r0 = AllocateClosure()
    //     0xc0adf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0adf4: ldur            x16, [fp, #-0x28]
    // 0xc0adf8: stp             x16, NULL, [SP, #8]
    // 0xc0adfc: str             x0, [SP]
    // 0xc0ae00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc0ae00: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc0ae04: r0 = expand()
    //     0xc0ae04: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xc0ae08: str             x0, [SP]
    // 0xc0ae0c: r0 = length()
    //     0xc0ae0c: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xc0ae10: cmp             w0, NULL
    // 0xc0ae14: b.ne            #0xc0ae20
    // 0xc0ae18: r0 = 0
    //     0xc0ae18: movz            x0, #0
    // 0xc0ae1c: b               #0xc0ae30
    // 0xc0ae20: r1 = LoadInt32Instr(r0)
    //     0xc0ae20: sbfx            x1, x0, #1, #0x1f
    //     0xc0ae24: tbz             w0, #0, #0xc0ae2c
    //     0xc0ae28: ldur            x1, [x0, #7]
    // 0xc0ae2c: mov             x0, x1
    // 0xc0ae30: cmp             x0, #5
    // 0xc0ae34: b.le            #0xc0ae40
    // 0xc0ae38: r5 = 5
    //     0xc0ae38: movz            x5, #0x5
    // 0xc0ae3c: b               #0xc0aef8
    // 0xc0ae40: ldur            x0, [fp, #-8]
    // 0xc0ae44: LoadField: r1 = r0->field_b
    //     0xc0ae44: ldur            w1, [x0, #0xb]
    // 0xc0ae48: DecompressPointer r1
    //     0xc0ae48: add             x1, x1, HEAP, lsl #32
    // 0xc0ae4c: cmp             w1, NULL
    // 0xc0ae50: b.eq            #0xc0b700
    // 0xc0ae54: LoadField: r2 = r1->field_f
    //     0xc0ae54: ldur            w2, [x1, #0xf]
    // 0xc0ae58: DecompressPointer r2
    //     0xc0ae58: add             x2, x2, HEAP, lsl #32
    // 0xc0ae5c: LoadField: r1 = r2->field_b
    //     0xc0ae5c: ldur            w1, [x2, #0xb]
    // 0xc0ae60: DecompressPointer r1
    //     0xc0ae60: add             x1, x1, HEAP, lsl #32
    // 0xc0ae64: cmp             w1, NULL
    // 0xc0ae68: b.ne            #0xc0ae74
    // 0xc0ae6c: r0 = Null
    //     0xc0ae6c: mov             x0, NULL
    // 0xc0ae70: b               #0xc0aed4
    // 0xc0ae74: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc0ae74: ldur            w2, [x1, #0x17]
    // 0xc0ae78: DecompressPointer r2
    //     0xc0ae78: add             x2, x2, HEAP, lsl #32
    // 0xc0ae7c: cmp             w2, NULL
    // 0xc0ae80: b.ne            #0xc0ae8c
    // 0xc0ae84: r0 = Null
    //     0xc0ae84: mov             x0, NULL
    // 0xc0ae88: b               #0xc0aed4
    // 0xc0ae8c: LoadField: r3 = r2->field_f
    //     0xc0ae8c: ldur            w3, [x2, #0xf]
    // 0xc0ae90: DecompressPointer r3
    //     0xc0ae90: add             x3, x3, HEAP, lsl #32
    // 0xc0ae94: stur            x3, [fp, #-0x28]
    // 0xc0ae98: cmp             w3, NULL
    // 0xc0ae9c: b.ne            #0xc0aea8
    // 0xc0aea0: r0 = Null
    //     0xc0aea0: mov             x0, NULL
    // 0xc0aea4: b               #0xc0aed4
    // 0xc0aea8: r1 = Function '<anonymous closure>':.
    //     0xc0aea8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52450] Function: [dart:ffi] Array::_variableLength (0x619e60)
    //     0xc0aeac: ldr             x1, [x1, #0x450]
    // 0xc0aeb0: r2 = Null
    //     0xc0aeb0: mov             x2, NULL
    // 0xc0aeb4: r0 = AllocateClosure()
    //     0xc0aeb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0aeb8: ldur            x16, [fp, #-0x28]
    // 0xc0aebc: stp             x16, NULL, [SP, #8]
    // 0xc0aec0: str             x0, [SP]
    // 0xc0aec4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc0aec4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc0aec8: r0 = expand()
    //     0xc0aec8: bl              #0x637fc4  ; [dart:collection] ListBase::expand
    // 0xc0aecc: str             x0, [SP]
    // 0xc0aed0: r0 = length()
    //     0xc0aed0: bl              #0x7ea768  ; [dart:core] Iterable::length
    // 0xc0aed4: cmp             w0, NULL
    // 0xc0aed8: b.ne            #0xc0aee4
    // 0xc0aedc: r0 = 0
    //     0xc0aedc: movz            x0, #0
    // 0xc0aee0: b               #0xc0aef4
    // 0xc0aee4: r1 = LoadInt32Instr(r0)
    //     0xc0aee4: sbfx            x1, x0, #1, #0x1f
    //     0xc0aee8: tbz             w0, #0, #0xc0aef0
    //     0xc0aeec: ldur            x1, [x0, #7]
    // 0xc0aef0: mov             x0, x1
    // 0xc0aef4: mov             x5, x0
    // 0xc0aef8: ldur            x0, [fp, #-8]
    // 0xc0aefc: ldur            x4, [fp, #-0x10]
    // 0xc0af00: ldur            x3, [fp, #-0x30]
    // 0xc0af04: ldur            x2, [fp, #-0x18]
    // 0xc0af08: stur            x5, [fp, #-0x40]
    // 0xc0af0c: r1 = Function '<anonymous closure>':.
    //     0xc0af0c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52458] AnonymousClosure: (0xa8d528), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xc0af10: ldr             x1, [x1, #0x458]
    // 0xc0af14: r0 = AllocateClosure()
    //     0xc0af14: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0af18: r1 = Function '<anonymous closure>':.
    //     0xc0af18: add             x1, PP, #0x52, lsl #12  ; [pp+0x52460] AnonymousClosure: (0x9ba768), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xc0af1c: ldr             x1, [x1, #0x460]
    // 0xc0af20: r2 = Null
    //     0xc0af20: mov             x2, NULL
    // 0xc0af24: stur            x0, [fp, #-0x28]
    // 0xc0af28: r0 = AllocateClosure()
    //     0xc0af28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0af2c: stur            x0, [fp, #-0x38]
    // 0xc0af30: r0 = ListView()
    //     0xc0af30: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc0af34: stur            x0, [fp, #-0x48]
    // 0xc0af38: r16 = true
    //     0xc0af38: add             x16, NULL, #0x20  ; true
    // 0xc0af3c: r30 = Instance_Axis
    //     0xc0af3c: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0af40: stp             lr, x16, [SP]
    // 0xc0af44: mov             x1, x0
    // 0xc0af48: ldur            x2, [fp, #-0x28]
    // 0xc0af4c: ldur            x3, [fp, #-0x40]
    // 0xc0af50: ldur            x5, [fp, #-0x38]
    // 0xc0af54: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xc0af54: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xc0af58: ldr             x4, [x4, #0x8e8]
    // 0xc0af5c: r0 = ListView.separated()
    //     0xc0af5c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xc0af60: r0 = SizedBox()
    //     0xc0af60: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc0af64: mov             x3, x0
    // 0xc0af68: r0 = 60.000000
    //     0xc0af68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xc0af6c: ldr             x0, [x0, #0x110]
    // 0xc0af70: stur            x3, [fp, #-0x28]
    // 0xc0af74: StoreField: r3->field_13 = r0
    //     0xc0af74: stur            w0, [x3, #0x13]
    // 0xc0af78: ldur            x0, [fp, #-0x48]
    // 0xc0af7c: StoreField: r3->field_b = r0
    //     0xc0af7c: stur            w0, [x3, #0xb]
    // 0xc0af80: r1 = Null
    //     0xc0af80: mov             x1, NULL
    // 0xc0af84: r2 = 8
    //     0xc0af84: movz            x2, #0x8
    // 0xc0af88: r0 = AllocateArray()
    //     0xc0af88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0af8c: mov             x2, x0
    // 0xc0af90: ldur            x0, [fp, #-0x30]
    // 0xc0af94: stur            x2, [fp, #-0x38]
    // 0xc0af98: StoreField: r2->field_f = r0
    //     0xc0af98: stur            w0, [x2, #0xf]
    // 0xc0af9c: r16 = Instance_SizedBox
    //     0xc0af9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xc0afa0: ldr             x16, [x16, #0x8f0]
    // 0xc0afa4: StoreField: r2->field_13 = r16
    //     0xc0afa4: stur            w16, [x2, #0x13]
    // 0xc0afa8: ldur            x0, [fp, #-0x28]
    // 0xc0afac: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0afac: stur            w0, [x2, #0x17]
    // 0xc0afb0: r16 = Instance_SizedBox
    //     0xc0afb0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xc0afb4: ldr             x16, [x16, #0x8f0]
    // 0xc0afb8: StoreField: r2->field_1b = r16
    //     0xc0afb8: stur            w16, [x2, #0x1b]
    // 0xc0afbc: r1 = <Widget>
    //     0xc0afbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0afc0: r0 = AllocateGrowableArray()
    //     0xc0afc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0afc4: mov             x1, x0
    // 0xc0afc8: ldur            x0, [fp, #-0x38]
    // 0xc0afcc: stur            x1, [fp, #-0x28]
    // 0xc0afd0: StoreField: r1->field_f = r0
    //     0xc0afd0: stur            w0, [x1, #0xf]
    // 0xc0afd4: r2 = 8
    //     0xc0afd4: movz            x2, #0x8
    // 0xc0afd8: StoreField: r1->field_b = r2
    //     0xc0afd8: stur            w2, [x1, #0xb]
    // 0xc0afdc: r0 = Column()
    //     0xc0afdc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0afe0: mov             x1, x0
    // 0xc0afe4: r0 = Instance_Axis
    //     0xc0afe4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0afe8: stur            x1, [fp, #-0x30]
    // 0xc0afec: StoreField: r1->field_f = r0
    //     0xc0afec: stur            w0, [x1, #0xf]
    // 0xc0aff0: r2 = Instance_MainAxisAlignment
    //     0xc0aff0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0aff4: ldr             x2, [x2, #0xa08]
    // 0xc0aff8: StoreField: r1->field_13 = r2
    //     0xc0aff8: stur            w2, [x1, #0x13]
    // 0xc0affc: r3 = Instance_MainAxisSize
    //     0xc0affc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0b000: ldr             x3, [x3, #0xa10]
    // 0xc0b004: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0b004: stur            w3, [x1, #0x17]
    // 0xc0b008: r4 = Instance_CrossAxisAlignment
    //     0xc0b008: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0b00c: ldr             x4, [x4, #0xa18]
    // 0xc0b010: StoreField: r1->field_1b = r4
    //     0xc0b010: stur            w4, [x1, #0x1b]
    // 0xc0b014: r5 = Instance_VerticalDirection
    //     0xc0b014: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0b018: ldr             x5, [x5, #0xa20]
    // 0xc0b01c: StoreField: r1->field_23 = r5
    //     0xc0b01c: stur            w5, [x1, #0x23]
    // 0xc0b020: r6 = Instance_Clip
    //     0xc0b020: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0b024: ldr             x6, [x6, #0x38]
    // 0xc0b028: StoreField: r1->field_2b = r6
    //     0xc0b028: stur            w6, [x1, #0x2b]
    // 0xc0b02c: StoreField: r1->field_2f = rZR
    //     0xc0b02c: stur            xzr, [x1, #0x2f]
    // 0xc0b030: ldur            x7, [fp, #-0x28]
    // 0xc0b034: StoreField: r1->field_b = r7
    //     0xc0b034: stur            w7, [x1, #0xb]
    // 0xc0b038: r0 = Padding()
    //     0xc0b038: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0b03c: mov             x1, x0
    // 0xc0b040: r0 = Instance_EdgeInsets
    //     0xc0b040: add             x0, PP, #0x52, lsl #12  ; [pp+0x52468] Obj!EdgeInsets@d58a01
    //     0xc0b044: ldr             x0, [x0, #0x468]
    // 0xc0b048: stur            x1, [fp, #-0x28]
    // 0xc0b04c: StoreField: r1->field_f = r0
    //     0xc0b04c: stur            w0, [x1, #0xf]
    // 0xc0b050: ldur            x0, [fp, #-0x30]
    // 0xc0b054: StoreField: r1->field_b = r0
    //     0xc0b054: stur            w0, [x1, #0xb]
    // 0xc0b058: r0 = Visibility()
    //     0xc0b058: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0b05c: mov             x2, x0
    // 0xc0b060: ldur            x0, [fp, #-0x28]
    // 0xc0b064: stur            x2, [fp, #-0x30]
    // 0xc0b068: StoreField: r2->field_b = r0
    //     0xc0b068: stur            w0, [x2, #0xb]
    // 0xc0b06c: r0 = Instance_SizedBox
    //     0xc0b06c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc0b070: StoreField: r2->field_f = r0
    //     0xc0b070: stur            w0, [x2, #0xf]
    // 0xc0b074: ldur            x1, [fp, #-0x10]
    // 0xc0b078: StoreField: r2->field_13 = r1
    //     0xc0b078: stur            w1, [x2, #0x13]
    // 0xc0b07c: r3 = false
    //     0xc0b07c: add             x3, NULL, #0x30  ; false
    // 0xc0b080: ArrayStore: r2[0] = r3  ; List_4
    //     0xc0b080: stur            w3, [x2, #0x17]
    // 0xc0b084: StoreField: r2->field_1b = r3
    //     0xc0b084: stur            w3, [x2, #0x1b]
    // 0xc0b088: StoreField: r2->field_1f = r3
    //     0xc0b088: stur            w3, [x2, #0x1f]
    // 0xc0b08c: StoreField: r2->field_23 = r3
    //     0xc0b08c: stur            w3, [x2, #0x23]
    // 0xc0b090: StoreField: r2->field_27 = r3
    //     0xc0b090: stur            w3, [x2, #0x27]
    // 0xc0b094: StoreField: r2->field_2b = r3
    //     0xc0b094: stur            w3, [x2, #0x2b]
    // 0xc0b098: ldur            x4, [fp, #-8]
    // 0xc0b09c: LoadField: r1 = r4->field_b
    //     0xc0b09c: ldur            w1, [x4, #0xb]
    // 0xc0b0a0: DecompressPointer r1
    //     0xc0b0a0: add             x1, x1, HEAP, lsl #32
    // 0xc0b0a4: cmp             w1, NULL
    // 0xc0b0a8: b.eq            #0xc0b704
    // 0xc0b0ac: LoadField: r5 = r1->field_f
    //     0xc0b0ac: ldur            w5, [x1, #0xf]
    // 0xc0b0b0: DecompressPointer r5
    //     0xc0b0b0: add             x5, x5, HEAP, lsl #32
    // 0xc0b0b4: LoadField: r1 = r5->field_b
    //     0xc0b0b4: ldur            w1, [x5, #0xb]
    // 0xc0b0b8: DecompressPointer r1
    //     0xc0b0b8: add             x1, x1, HEAP, lsl #32
    // 0xc0b0bc: cmp             w1, NULL
    // 0xc0b0c0: b.ne            #0xc0b0cc
    // 0xc0b0c4: r1 = Null
    //     0xc0b0c4: mov             x1, NULL
    // 0xc0b0c8: b               #0xc0b0ec
    // 0xc0b0cc: LoadField: r5 = r1->field_13
    //     0xc0b0cc: ldur            w5, [x1, #0x13]
    // 0xc0b0d0: DecompressPointer r5
    //     0xc0b0d0: add             x5, x5, HEAP, lsl #32
    // 0xc0b0d4: LoadField: r1 = r5->field_b
    //     0xc0b0d4: ldur            w1, [x5, #0xb]
    // 0xc0b0d8: cbnz            w1, #0xc0b0e4
    // 0xc0b0dc: r5 = false
    //     0xc0b0dc: add             x5, NULL, #0x30  ; false
    // 0xc0b0e0: b               #0xc0b0e8
    // 0xc0b0e4: r5 = true
    //     0xc0b0e4: add             x5, NULL, #0x20  ; true
    // 0xc0b0e8: mov             x1, x5
    // 0xc0b0ec: cmp             w1, NULL
    // 0xc0b0f0: b.ne            #0xc0b0fc
    // 0xc0b0f4: r5 = false
    //     0xc0b0f4: add             x5, NULL, #0x30  ; false
    // 0xc0b0f8: b               #0xc0b100
    // 0xc0b0fc: mov             x5, x1
    // 0xc0b100: stur            x5, [fp, #-0x10]
    // 0xc0b104: r1 = Instance_Color
    //     0xc0b104: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0b108: d0 = 0.100000
    //     0xc0b108: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0b10c: r0 = withOpacity()
    //     0xc0b10c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0b110: stur            x0, [fp, #-0x28]
    // 0xc0b114: r0 = Divider()
    //     0xc0b114: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xc0b118: mov             x3, x0
    // 0xc0b11c: ldur            x0, [fp, #-0x28]
    // 0xc0b120: stur            x3, [fp, #-0x48]
    // 0xc0b124: StoreField: r3->field_1f = r0
    //     0xc0b124: stur            w0, [x3, #0x1f]
    // 0xc0b128: ldur            x0, [fp, #-8]
    // 0xc0b12c: LoadField: r1 = r0->field_b
    //     0xc0b12c: ldur            w1, [x0, #0xb]
    // 0xc0b130: DecompressPointer r1
    //     0xc0b130: add             x1, x1, HEAP, lsl #32
    // 0xc0b134: cmp             w1, NULL
    // 0xc0b138: b.eq            #0xc0b708
    // 0xc0b13c: LoadField: r2 = r1->field_f
    //     0xc0b13c: ldur            w2, [x1, #0xf]
    // 0xc0b140: DecompressPointer r2
    //     0xc0b140: add             x2, x2, HEAP, lsl #32
    // 0xc0b144: LoadField: r4 = r2->field_b
    //     0xc0b144: ldur            w4, [x2, #0xb]
    // 0xc0b148: DecompressPointer r4
    //     0xc0b148: add             x4, x4, HEAP, lsl #32
    // 0xc0b14c: stur            x4, [fp, #-0x38]
    // 0xc0b150: LoadField: r5 = r1->field_1f
    //     0xc0b150: ldur            w5, [x1, #0x1f]
    // 0xc0b154: DecompressPointer r5
    //     0xc0b154: add             x5, x5, HEAP, lsl #32
    // 0xc0b158: ldur            x2, [fp, #-0x18]
    // 0xc0b15c: stur            x5, [fp, #-0x28]
    // 0xc0b160: r1 = Function '<anonymous closure>':.
    //     0xc0b160: add             x1, PP, #0x52, lsl #12  ; [pp+0x52470] AnonymousClosure: (0xa8c4e0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xc0b164: ldr             x1, [x1, #0x470]
    // 0xc0b168: r0 = AllocateClosure()
    //     0xc0b168: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0b16c: stur            x0, [fp, #-0x50]
    // 0xc0b170: r0 = ReviewWidget()
    //     0xc0b170: bl              #0xc0b714  ; AllocateReviewWidgetStub -> ReviewWidget (size=0x1c)
    // 0xc0b174: mov             x3, x0
    // 0xc0b178: ldur            x0, [fp, #-0x50]
    // 0xc0b17c: stur            x3, [fp, #-0x58]
    // 0xc0b180: StoreField: r3->field_f = r0
    //     0xc0b180: stur            w0, [x3, #0xf]
    // 0xc0b184: ldur            x0, [fp, #-0x38]
    // 0xc0b188: StoreField: r3->field_b = r0
    //     0xc0b188: stur            w0, [x3, #0xb]
    // 0xc0b18c: ldur            x0, [fp, #-0x28]
    // 0xc0b190: StoreField: r3->field_13 = r0
    //     0xc0b190: stur            w0, [x3, #0x13]
    // 0xc0b194: ldur            x2, [fp, #-0x18]
    // 0xc0b198: r1 = Function '<anonymous closure>':.
    //     0xc0b198: add             x1, PP, #0x52, lsl #12  ; [pp+0x52478] AnonymousClosure: (0xc0b7b0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xc0b19c: ldr             x1, [x1, #0x478]
    // 0xc0b1a0: r0 = AllocateClosure()
    //     0xc0b1a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0b1a4: mov             x1, x0
    // 0xc0b1a8: ldur            x0, [fp, #-0x58]
    // 0xc0b1ac: ArrayStore: r0[0] = r1  ; List_4
    //     0xc0b1ac: stur            w1, [x0, #0x17]
    // 0xc0b1b0: r1 = Instance_Color
    //     0xc0b1b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0b1b4: d0 = 0.100000
    //     0xc0b1b4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0b1b8: r0 = withOpacity()
    //     0xc0b1b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0b1bc: stur            x0, [fp, #-0x28]
    // 0xc0b1c0: r0 = Divider()
    //     0xc0b1c0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xc0b1c4: mov             x3, x0
    // 0xc0b1c8: ldur            x0, [fp, #-0x28]
    // 0xc0b1cc: stur            x3, [fp, #-0x38]
    // 0xc0b1d0: StoreField: r3->field_1f = r0
    //     0xc0b1d0: stur            w0, [x3, #0x1f]
    // 0xc0b1d4: r1 = Null
    //     0xc0b1d4: mov             x1, NULL
    // 0xc0b1d8: r2 = 10
    //     0xc0b1d8: movz            x2, #0xa
    // 0xc0b1dc: r0 = AllocateArray()
    //     0xc0b1dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0b1e0: mov             x2, x0
    // 0xc0b1e4: ldur            x0, [fp, #-0x48]
    // 0xc0b1e8: stur            x2, [fp, #-0x28]
    // 0xc0b1ec: StoreField: r2->field_f = r0
    //     0xc0b1ec: stur            w0, [x2, #0xf]
    // 0xc0b1f0: r16 = Instance_SizedBox
    //     0xc0b1f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xc0b1f4: ldr             x16, [x16, #0x8f0]
    // 0xc0b1f8: StoreField: r2->field_13 = r16
    //     0xc0b1f8: stur            w16, [x2, #0x13]
    // 0xc0b1fc: ldur            x0, [fp, #-0x58]
    // 0xc0b200: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0b200: stur            w0, [x2, #0x17]
    // 0xc0b204: ldur            x0, [fp, #-0x38]
    // 0xc0b208: StoreField: r2->field_1b = r0
    //     0xc0b208: stur            w0, [x2, #0x1b]
    // 0xc0b20c: r16 = Instance_SizedBox
    //     0xc0b20c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xc0b210: ldr             x16, [x16, #0x8f0]
    // 0xc0b214: StoreField: r2->field_1f = r16
    //     0xc0b214: stur            w16, [x2, #0x1f]
    // 0xc0b218: r1 = <Widget>
    //     0xc0b218: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0b21c: r0 = AllocateGrowableArray()
    //     0xc0b21c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0b220: mov             x2, x0
    // 0xc0b224: ldur            x0, [fp, #-0x28]
    // 0xc0b228: stur            x2, [fp, #-0x38]
    // 0xc0b22c: StoreField: r2->field_f = r0
    //     0xc0b22c: stur            w0, [x2, #0xf]
    // 0xc0b230: r0 = 10
    //     0xc0b230: movz            x0, #0xa
    // 0xc0b234: StoreField: r2->field_b = r0
    //     0xc0b234: stur            w0, [x2, #0xb]
    // 0xc0b238: ldur            x0, [fp, #-8]
    // 0xc0b23c: LoadField: r1 = r0->field_b
    //     0xc0b23c: ldur            w1, [x0, #0xb]
    // 0xc0b240: DecompressPointer r1
    //     0xc0b240: add             x1, x1, HEAP, lsl #32
    // 0xc0b244: cmp             w1, NULL
    // 0xc0b248: b.eq            #0xc0b70c
    // 0xc0b24c: LoadField: r3 = r1->field_f
    //     0xc0b24c: ldur            w3, [x1, #0xf]
    // 0xc0b250: DecompressPointer r3
    //     0xc0b250: add             x3, x3, HEAP, lsl #32
    // 0xc0b254: LoadField: r1 = r3->field_f
    //     0xc0b254: ldur            w1, [x3, #0xf]
    // 0xc0b258: DecompressPointer r1
    //     0xc0b258: add             x1, x1, HEAP, lsl #32
    // 0xc0b25c: cmp             w1, NULL
    // 0xc0b260: b.ne            #0xc0b26c
    // 0xc0b264: r1 = Null
    //     0xc0b264: mov             x1, NULL
    // 0xc0b268: b               #0xc0b278
    // 0xc0b26c: LoadField: r3 = r1->field_f
    //     0xc0b26c: ldur            w3, [x1, #0xf]
    // 0xc0b270: DecompressPointer r3
    //     0xc0b270: add             x3, x3, HEAP, lsl #32
    // 0xc0b274: mov             x1, x3
    // 0xc0b278: cmp             w1, NULL
    // 0xc0b27c: b.ne            #0xc0b288
    // 0xc0b280: r1 = 0
    //     0xc0b280: movz            x1, #0
    // 0xc0b284: b               #0xc0b298
    // 0xc0b288: r3 = LoadInt32Instr(r1)
    //     0xc0b288: sbfx            x3, x1, #1, #0x1f
    //     0xc0b28c: tbz             w1, #0, #0xc0b294
    //     0xc0b290: ldur            x3, [x1, #7]
    // 0xc0b294: mov             x1, x3
    // 0xc0b298: cmp             x1, #3
    // 0xc0b29c: b.lt            #0xc0b538
    // 0xc0b2a0: ldur            x3, [fp, #-0x18]
    // 0xc0b2a4: LoadField: r1 = r3->field_13
    //     0xc0b2a4: ldur            w1, [x3, #0x13]
    // 0xc0b2a8: DecompressPointer r1
    //     0xc0b2a8: add             x1, x1, HEAP, lsl #32
    // 0xc0b2ac: r0 = of()
    //     0xc0b2ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0b2b0: LoadField: r1 = r0->field_5b
    //     0xc0b2b0: ldur            w1, [x0, #0x5b]
    // 0xc0b2b4: DecompressPointer r1
    //     0xc0b2b4: add             x1, x1, HEAP, lsl #32
    // 0xc0b2b8: stur            x1, [fp, #-0x28]
    // 0xc0b2bc: r0 = BoxDecoration()
    //     0xc0b2bc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc0b2c0: mov             x3, x0
    // 0xc0b2c4: ldur            x0, [fp, #-0x28]
    // 0xc0b2c8: stur            x3, [fp, #-0x48]
    // 0xc0b2cc: StoreField: r3->field_7 = r0
    //     0xc0b2cc: stur            w0, [x3, #7]
    // 0xc0b2d0: r0 = Instance_BoxShape
    //     0xc0b2d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0b2d4: ldr             x0, [x0, #0x80]
    // 0xc0b2d8: StoreField: r3->field_23 = r0
    //     0xc0b2d8: stur            w0, [x3, #0x23]
    // 0xc0b2dc: r1 = Null
    //     0xc0b2dc: mov             x1, NULL
    // 0xc0b2e0: r2 = 6
    //     0xc0b2e0: movz            x2, #0x6
    // 0xc0b2e4: r0 = AllocateArray()
    //     0xc0b2e4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0b2e8: r16 = "View All Reviews ("
    //     0xc0b2e8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52480] "View All Reviews ("
    //     0xc0b2ec: ldr             x16, [x16, #0x480]
    // 0xc0b2f0: StoreField: r0->field_f = r16
    //     0xc0b2f0: stur            w16, [x0, #0xf]
    // 0xc0b2f4: ldur            x1, [fp, #-8]
    // 0xc0b2f8: LoadField: r2 = r1->field_b
    //     0xc0b2f8: ldur            w2, [x1, #0xb]
    // 0xc0b2fc: DecompressPointer r2
    //     0xc0b2fc: add             x2, x2, HEAP, lsl #32
    // 0xc0b300: cmp             w2, NULL
    // 0xc0b304: b.eq            #0xc0b710
    // 0xc0b308: LoadField: r1 = r2->field_f
    //     0xc0b308: ldur            w1, [x2, #0xf]
    // 0xc0b30c: DecompressPointer r1
    //     0xc0b30c: add             x1, x1, HEAP, lsl #32
    // 0xc0b310: LoadField: r2 = r1->field_f
    //     0xc0b310: ldur            w2, [x1, #0xf]
    // 0xc0b314: DecompressPointer r2
    //     0xc0b314: add             x2, x2, HEAP, lsl #32
    // 0xc0b318: cmp             w2, NULL
    // 0xc0b31c: b.ne            #0xc0b328
    // 0xc0b320: r1 = Null
    //     0xc0b320: mov             x1, NULL
    // 0xc0b324: b               #0xc0b330
    // 0xc0b328: LoadField: r1 = r2->field_f
    //     0xc0b328: ldur            w1, [x2, #0xf]
    // 0xc0b32c: DecompressPointer r1
    //     0xc0b32c: add             x1, x1, HEAP, lsl #32
    // 0xc0b330: cmp             w1, NULL
    // 0xc0b334: b.ne            #0xc0b33c
    // 0xc0b338: r1 = ""
    //     0xc0b338: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0b33c: ldur            x2, [fp, #-0x18]
    // 0xc0b340: StoreField: r0->field_13 = r1
    //     0xc0b340: stur            w1, [x0, #0x13]
    // 0xc0b344: r16 = ")"
    //     0xc0b344: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xc0b348: ArrayStore: r0[0] = r16  ; List_4
    //     0xc0b348: stur            w16, [x0, #0x17]
    // 0xc0b34c: str             x0, [SP]
    // 0xc0b350: r0 = _interpolate()
    //     0xc0b350: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xc0b354: r1 = LoadClassIdInstr(r0)
    //     0xc0b354: ldur            x1, [x0, #-1]
    //     0xc0b358: ubfx            x1, x1, #0xc, #0x14
    // 0xc0b35c: str             x0, [SP]
    // 0xc0b360: mov             x0, x1
    // 0xc0b364: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc0b364: sub             lr, x0, #1, lsl #12
    //     0xc0b368: ldr             lr, [x21, lr, lsl #3]
    //     0xc0b36c: blr             lr
    // 0xc0b370: ldur            x2, [fp, #-0x18]
    // 0xc0b374: stur            x0, [fp, #-8]
    // 0xc0b378: LoadField: r1 = r2->field_13
    //     0xc0b378: ldur            w1, [x2, #0x13]
    // 0xc0b37c: DecompressPointer r1
    //     0xc0b37c: add             x1, x1, HEAP, lsl #32
    // 0xc0b380: r0 = of()
    //     0xc0b380: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0b384: LoadField: r1 = r0->field_87
    //     0xc0b384: ldur            w1, [x0, #0x87]
    // 0xc0b388: DecompressPointer r1
    //     0xc0b388: add             x1, x1, HEAP, lsl #32
    // 0xc0b38c: LoadField: r0 = r1->field_b
    //     0xc0b38c: ldur            w0, [x1, #0xb]
    // 0xc0b390: DecompressPointer r0
    //     0xc0b390: add             x0, x0, HEAP, lsl #32
    // 0xc0b394: cmp             w0, NULL
    // 0xc0b398: b.ne            #0xc0b3a4
    // 0xc0b39c: r2 = Null
    //     0xc0b39c: mov             x2, NULL
    // 0xc0b3a0: b               #0xc0b3c8
    // 0xc0b3a4: r16 = Instance_Color
    //     0xc0b3a4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0b3a8: r30 = 14.000000
    //     0xc0b3a8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc0b3ac: ldr             lr, [lr, #0x1d8]
    // 0xc0b3b0: stp             lr, x16, [SP]
    // 0xc0b3b4: mov             x1, x0
    // 0xc0b3b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0b3b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0b3bc: ldr             x4, [x4, #0x9b8]
    // 0xc0b3c0: r0 = copyWith()
    //     0xc0b3c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0b3c4: mov             x2, x0
    // 0xc0b3c8: ldur            x0, [fp, #-8]
    // 0xc0b3cc: ldur            x1, [fp, #-0x38]
    // 0xc0b3d0: stur            x2, [fp, #-0x28]
    // 0xc0b3d4: r0 = Text()
    //     0xc0b3d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0b3d8: mov             x1, x0
    // 0xc0b3dc: ldur            x0, [fp, #-8]
    // 0xc0b3e0: stur            x1, [fp, #-0x50]
    // 0xc0b3e4: StoreField: r1->field_b = r0
    //     0xc0b3e4: stur            w0, [x1, #0xb]
    // 0xc0b3e8: ldur            x0, [fp, #-0x28]
    // 0xc0b3ec: StoreField: r1->field_13 = r0
    //     0xc0b3ec: stur            w0, [x1, #0x13]
    // 0xc0b3f0: r0 = Center()
    //     0xc0b3f0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc0b3f4: mov             x1, x0
    // 0xc0b3f8: r0 = Instance_Alignment
    //     0xc0b3f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0b3fc: ldr             x0, [x0, #0xb10]
    // 0xc0b400: stur            x1, [fp, #-8]
    // 0xc0b404: StoreField: r1->field_f = r0
    //     0xc0b404: stur            w0, [x1, #0xf]
    // 0xc0b408: ldur            x0, [fp, #-0x50]
    // 0xc0b40c: StoreField: r1->field_b = r0
    //     0xc0b40c: stur            w0, [x1, #0xb]
    // 0xc0b410: r0 = Container()
    //     0xc0b410: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0b414: stur            x0, [fp, #-0x28]
    // 0xc0b418: ldur            x16, [fp, #-0x48]
    // 0xc0b41c: r30 = inf
    //     0xc0b41c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc0b420: ldr             lr, [lr, #0x9f8]
    // 0xc0b424: stp             lr, x16, [SP, #0x10]
    // 0xc0b428: r16 = 40.000000
    //     0xc0b428: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xc0b42c: ldr             x16, [x16, #8]
    // 0xc0b430: ldur            lr, [fp, #-8]
    // 0xc0b434: stp             lr, x16, [SP]
    // 0xc0b438: mov             x1, x0
    // 0xc0b43c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x3, width, 0x2, null]
    //     0xc0b43c: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0xc0b440: ldr             x4, [x4, #0x5d0]
    // 0xc0b444: r0 = Container()
    //     0xc0b444: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0b448: r0 = InkWell()
    //     0xc0b448: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc0b44c: mov             x3, x0
    // 0xc0b450: ldur            x0, [fp, #-0x28]
    // 0xc0b454: stur            x3, [fp, #-8]
    // 0xc0b458: StoreField: r3->field_b = r0
    //     0xc0b458: stur            w0, [x3, #0xb]
    // 0xc0b45c: ldur            x2, [fp, #-0x18]
    // 0xc0b460: r1 = Function '<anonymous closure>':.
    //     0xc0b460: add             x1, PP, #0x52, lsl #12  ; [pp+0x52488] AnonymousClosure: (0xc0b720), in [package:customer_app/app/presentation/views/line/product_detail/widgets/rating_review_item_view.dart] _RatingReviewItemViewState::build (0xc08ed4)
    //     0xc0b464: ldr             x1, [x1, #0x488]
    // 0xc0b468: r0 = AllocateClosure()
    //     0xc0b468: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0b46c: mov             x1, x0
    // 0xc0b470: ldur            x0, [fp, #-8]
    // 0xc0b474: StoreField: r0->field_f = r1
    //     0xc0b474: stur            w1, [x0, #0xf]
    // 0xc0b478: r1 = true
    //     0xc0b478: add             x1, NULL, #0x20  ; true
    // 0xc0b47c: StoreField: r0->field_43 = r1
    //     0xc0b47c: stur            w1, [x0, #0x43]
    // 0xc0b480: r2 = Instance_BoxShape
    //     0xc0b480: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0b484: ldr             x2, [x2, #0x80]
    // 0xc0b488: StoreField: r0->field_47 = r2
    //     0xc0b488: stur            w2, [x0, #0x47]
    // 0xc0b48c: StoreField: r0->field_6f = r1
    //     0xc0b48c: stur            w1, [x0, #0x6f]
    // 0xc0b490: r2 = false
    //     0xc0b490: add             x2, NULL, #0x30  ; false
    // 0xc0b494: StoreField: r0->field_73 = r2
    //     0xc0b494: stur            w2, [x0, #0x73]
    // 0xc0b498: StoreField: r0->field_83 = r1
    //     0xc0b498: stur            w1, [x0, #0x83]
    // 0xc0b49c: StoreField: r0->field_7b = r2
    //     0xc0b49c: stur            w2, [x0, #0x7b]
    // 0xc0b4a0: r0 = Padding()
    //     0xc0b4a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0b4a4: mov             x2, x0
    // 0xc0b4a8: r0 = Instance_EdgeInsets
    //     0xc0b4a8: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xc0b4ac: ldr             x0, [x0, #0x240]
    // 0xc0b4b0: stur            x2, [fp, #-0x18]
    // 0xc0b4b4: StoreField: r2->field_f = r0
    //     0xc0b4b4: stur            w0, [x2, #0xf]
    // 0xc0b4b8: ldur            x0, [fp, #-8]
    // 0xc0b4bc: StoreField: r2->field_b = r0
    //     0xc0b4bc: stur            w0, [x2, #0xb]
    // 0xc0b4c0: ldur            x0, [fp, #-0x38]
    // 0xc0b4c4: LoadField: r1 = r0->field_b
    //     0xc0b4c4: ldur            w1, [x0, #0xb]
    // 0xc0b4c8: LoadField: r3 = r0->field_f
    //     0xc0b4c8: ldur            w3, [x0, #0xf]
    // 0xc0b4cc: DecompressPointer r3
    //     0xc0b4cc: add             x3, x3, HEAP, lsl #32
    // 0xc0b4d0: LoadField: r4 = r3->field_b
    //     0xc0b4d0: ldur            w4, [x3, #0xb]
    // 0xc0b4d4: r3 = LoadInt32Instr(r1)
    //     0xc0b4d4: sbfx            x3, x1, #1, #0x1f
    // 0xc0b4d8: stur            x3, [fp, #-0x40]
    // 0xc0b4dc: r1 = LoadInt32Instr(r4)
    //     0xc0b4dc: sbfx            x1, x4, #1, #0x1f
    // 0xc0b4e0: cmp             x3, x1
    // 0xc0b4e4: b.ne            #0xc0b4f0
    // 0xc0b4e8: mov             x1, x0
    // 0xc0b4ec: r0 = _growToNextCapacity()
    //     0xc0b4ec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0b4f0: ldur            x2, [fp, #-0x38]
    // 0xc0b4f4: ldur            x3, [fp, #-0x40]
    // 0xc0b4f8: add             x0, x3, #1
    // 0xc0b4fc: lsl             x1, x0, #1
    // 0xc0b500: StoreField: r2->field_b = r1
    //     0xc0b500: stur            w1, [x2, #0xb]
    // 0xc0b504: LoadField: r1 = r2->field_f
    //     0xc0b504: ldur            w1, [x2, #0xf]
    // 0xc0b508: DecompressPointer r1
    //     0xc0b508: add             x1, x1, HEAP, lsl #32
    // 0xc0b50c: ldur            x0, [fp, #-0x18]
    // 0xc0b510: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0b510: add             x25, x1, x3, lsl #2
    //     0xc0b514: add             x25, x25, #0xf
    //     0xc0b518: str             w0, [x25]
    //     0xc0b51c: tbz             w0, #0, #0xc0b538
    //     0xc0b520: ldurb           w16, [x1, #-1]
    //     0xc0b524: ldurb           w17, [x0, #-1]
    //     0xc0b528: and             x16, x17, x16, lsr #2
    //     0xc0b52c: tst             x16, HEAP, lsr #32
    //     0xc0b530: b.eq            #0xc0b538
    //     0xc0b534: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0b538: ldur            x3, [fp, #-0x20]
    // 0xc0b53c: ldur            x0, [fp, #-0x30]
    // 0xc0b540: ldur            x1, [fp, #-0x10]
    // 0xc0b544: r0 = Column()
    //     0xc0b544: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0b548: mov             x1, x0
    // 0xc0b54c: r0 = Instance_Axis
    //     0xc0b54c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0b550: stur            x1, [fp, #-8]
    // 0xc0b554: StoreField: r1->field_f = r0
    //     0xc0b554: stur            w0, [x1, #0xf]
    // 0xc0b558: r2 = Instance_MainAxisAlignment
    //     0xc0b558: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0b55c: ldr             x2, [x2, #0xa08]
    // 0xc0b560: StoreField: r1->field_13 = r2
    //     0xc0b560: stur            w2, [x1, #0x13]
    // 0xc0b564: r3 = Instance_MainAxisSize
    //     0xc0b564: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0b568: ldr             x3, [x3, #0xa10]
    // 0xc0b56c: ArrayStore: r1[0] = r3  ; List_4
    //     0xc0b56c: stur            w3, [x1, #0x17]
    // 0xc0b570: r4 = Instance_CrossAxisAlignment
    //     0xc0b570: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0b574: ldr             x4, [x4, #0xa18]
    // 0xc0b578: StoreField: r1->field_1b = r4
    //     0xc0b578: stur            w4, [x1, #0x1b]
    // 0xc0b57c: r5 = Instance_VerticalDirection
    //     0xc0b57c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0b580: ldr             x5, [x5, #0xa20]
    // 0xc0b584: StoreField: r1->field_23 = r5
    //     0xc0b584: stur            w5, [x1, #0x23]
    // 0xc0b588: r6 = Instance_Clip
    //     0xc0b588: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0b58c: ldr             x6, [x6, #0x38]
    // 0xc0b590: StoreField: r1->field_2b = r6
    //     0xc0b590: stur            w6, [x1, #0x2b]
    // 0xc0b594: StoreField: r1->field_2f = rZR
    //     0xc0b594: stur            xzr, [x1, #0x2f]
    // 0xc0b598: ldur            x7, [fp, #-0x38]
    // 0xc0b59c: StoreField: r1->field_b = r7
    //     0xc0b59c: stur            w7, [x1, #0xb]
    // 0xc0b5a0: r0 = Visibility()
    //     0xc0b5a0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0b5a4: mov             x3, x0
    // 0xc0b5a8: ldur            x0, [fp, #-8]
    // 0xc0b5ac: stur            x3, [fp, #-0x18]
    // 0xc0b5b0: StoreField: r3->field_b = r0
    //     0xc0b5b0: stur            w0, [x3, #0xb]
    // 0xc0b5b4: r0 = Instance_SizedBox
    //     0xc0b5b4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc0b5b8: StoreField: r3->field_f = r0
    //     0xc0b5b8: stur            w0, [x3, #0xf]
    // 0xc0b5bc: ldur            x0, [fp, #-0x10]
    // 0xc0b5c0: StoreField: r3->field_13 = r0
    //     0xc0b5c0: stur            w0, [x3, #0x13]
    // 0xc0b5c4: r0 = false
    //     0xc0b5c4: add             x0, NULL, #0x30  ; false
    // 0xc0b5c8: ArrayStore: r3[0] = r0  ; List_4
    //     0xc0b5c8: stur            w0, [x3, #0x17]
    // 0xc0b5cc: StoreField: r3->field_1b = r0
    //     0xc0b5cc: stur            w0, [x3, #0x1b]
    // 0xc0b5d0: StoreField: r3->field_1f = r0
    //     0xc0b5d0: stur            w0, [x3, #0x1f]
    // 0xc0b5d4: StoreField: r3->field_23 = r0
    //     0xc0b5d4: stur            w0, [x3, #0x23]
    // 0xc0b5d8: StoreField: r3->field_27 = r0
    //     0xc0b5d8: stur            w0, [x3, #0x27]
    // 0xc0b5dc: StoreField: r3->field_2b = r0
    //     0xc0b5dc: stur            w0, [x3, #0x2b]
    // 0xc0b5e0: r1 = Null
    //     0xc0b5e0: mov             x1, NULL
    // 0xc0b5e4: r2 = 8
    //     0xc0b5e4: movz            x2, #0x8
    // 0xc0b5e8: r0 = AllocateArray()
    //     0xc0b5e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0b5ec: mov             x2, x0
    // 0xc0b5f0: ldur            x0, [fp, #-0x20]
    // 0xc0b5f4: stur            x2, [fp, #-8]
    // 0xc0b5f8: StoreField: r2->field_f = r0
    //     0xc0b5f8: stur            w0, [x2, #0xf]
    // 0xc0b5fc: r16 = Instance_SizedBox
    //     0xc0b5fc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0xc0b600: ldr             x16, [x16, #0xd68]
    // 0xc0b604: StoreField: r2->field_13 = r16
    //     0xc0b604: stur            w16, [x2, #0x13]
    // 0xc0b608: ldur            x0, [fp, #-0x30]
    // 0xc0b60c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0b60c: stur            w0, [x2, #0x17]
    // 0xc0b610: ldur            x0, [fp, #-0x18]
    // 0xc0b614: StoreField: r2->field_1b = r0
    //     0xc0b614: stur            w0, [x2, #0x1b]
    // 0xc0b618: r1 = <Widget>
    //     0xc0b618: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0b61c: r0 = AllocateGrowableArray()
    //     0xc0b61c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0b620: mov             x1, x0
    // 0xc0b624: ldur            x0, [fp, #-8]
    // 0xc0b628: stur            x1, [fp, #-0x10]
    // 0xc0b62c: StoreField: r1->field_f = r0
    //     0xc0b62c: stur            w0, [x1, #0xf]
    // 0xc0b630: r0 = 8
    //     0xc0b630: movz            x0, #0x8
    // 0xc0b634: StoreField: r1->field_b = r0
    //     0xc0b634: stur            w0, [x1, #0xb]
    // 0xc0b638: r0 = Column()
    //     0xc0b638: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0b63c: mov             x1, x0
    // 0xc0b640: r0 = Instance_Axis
    //     0xc0b640: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0b644: stur            x1, [fp, #-8]
    // 0xc0b648: StoreField: r1->field_f = r0
    //     0xc0b648: stur            w0, [x1, #0xf]
    // 0xc0b64c: r0 = Instance_MainAxisAlignment
    //     0xc0b64c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0b650: ldr             x0, [x0, #0xa08]
    // 0xc0b654: StoreField: r1->field_13 = r0
    //     0xc0b654: stur            w0, [x1, #0x13]
    // 0xc0b658: r0 = Instance_MainAxisSize
    //     0xc0b658: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0b65c: ldr             x0, [x0, #0xa10]
    // 0xc0b660: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0b660: stur            w0, [x1, #0x17]
    // 0xc0b664: r0 = Instance_CrossAxisAlignment
    //     0xc0b664: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0b668: ldr             x0, [x0, #0xa18]
    // 0xc0b66c: StoreField: r1->field_1b = r0
    //     0xc0b66c: stur            w0, [x1, #0x1b]
    // 0xc0b670: r0 = Instance_VerticalDirection
    //     0xc0b670: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0b674: ldr             x0, [x0, #0xa20]
    // 0xc0b678: StoreField: r1->field_23 = r0
    //     0xc0b678: stur            w0, [x1, #0x23]
    // 0xc0b67c: r0 = Instance_Clip
    //     0xc0b67c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0b680: ldr             x0, [x0, #0x38]
    // 0xc0b684: StoreField: r1->field_2b = r0
    //     0xc0b684: stur            w0, [x1, #0x2b]
    // 0xc0b688: StoreField: r1->field_2f = rZR
    //     0xc0b688: stur            xzr, [x1, #0x2f]
    // 0xc0b68c: ldur            x0, [fp, #-0x10]
    // 0xc0b690: StoreField: r1->field_b = r0
    //     0xc0b690: stur            w0, [x1, #0xb]
    // 0xc0b694: r0 = Padding()
    //     0xc0b694: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0b698: r1 = Instance_EdgeInsets
    //     0xc0b698: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xc0b69c: ldr             x1, [x1, #0xd0]
    // 0xc0b6a0: StoreField: r0->field_f = r1
    //     0xc0b6a0: stur            w1, [x0, #0xf]
    // 0xc0b6a4: ldur            x1, [fp, #-8]
    // 0xc0b6a8: StoreField: r0->field_b = r1
    //     0xc0b6a8: stur            w1, [x0, #0xb]
    // 0xc0b6ac: b               #0xc0b6b8
    // 0xc0b6b0: r0 = Instance_Center
    //     0xc0b6b0: add             x0, PP, #0x32, lsl #12  ; [pp+0x326f0] Obj!Center@d68321
    //     0xc0b6b4: ldr             x0, [x0, #0x6f0]
    // 0xc0b6b8: LeaveFrame
    //     0xc0b6b8: mov             SP, fp
    //     0xc0b6bc: ldp             fp, lr, [SP], #0x10
    // 0xc0b6c0: ret
    //     0xc0b6c0: ret             
    // 0xc0b6c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0b6c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0b6c8: b               #0xc08ef4
    // 0xc0b6cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b6fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b6fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b700: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b700: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b704: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b708: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b708: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b70c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0b710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc0b720, size: 0x90
    // 0xc0b720: EnterFrame
    //     0xc0b720: stp             fp, lr, [SP, #-0x10]!
    //     0xc0b724: mov             fp, SP
    // 0xc0b728: AllocStack(0x20)
    //     0xc0b728: sub             SP, SP, #0x20
    // 0xc0b72c: SetupParameters()
    //     0xc0b72c: ldr             x0, [fp, #0x10]
    //     0xc0b730: ldur            w1, [x0, #0x17]
    //     0xc0b734: add             x1, x1, HEAP, lsl #32
    // 0xc0b738: CheckStackOverflow
    //     0xc0b738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0b73c: cmp             SP, x16
    //     0xc0b740: b.ls            #0xc0b7a4
    // 0xc0b744: LoadField: r0 = r1->field_f
    //     0xc0b744: ldur            w0, [x1, #0xf]
    // 0xc0b748: DecompressPointer r0
    //     0xc0b748: add             x0, x0, HEAP, lsl #32
    // 0xc0b74c: LoadField: r1 = r0->field_b
    //     0xc0b74c: ldur            w1, [x0, #0xb]
    // 0xc0b750: DecompressPointer r1
    //     0xc0b750: add             x1, x1, HEAP, lsl #32
    // 0xc0b754: cmp             w1, NULL
    // 0xc0b758: b.eq            #0xc0b7ac
    // 0xc0b75c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc0b75c: ldur            w0, [x1, #0x17]
    // 0xc0b760: DecompressPointer r0
    //     0xc0b760: add             x0, x0, HEAP, lsl #32
    // 0xc0b764: r16 = "view_all"
    //     0xc0b764: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xc0b768: ldr             x16, [x16, #0xba0]
    // 0xc0b76c: stp             x16, x0, [SP, #0x10]
    // 0xc0b770: r16 = ""
    //     0xc0b770: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0b774: r30 = true
    //     0xc0b774: add             lr, NULL, #0x20  ; true
    // 0xc0b778: stp             lr, x16, [SP]
    // 0xc0b77c: r4 = 0
    //     0xc0b77c: movz            x4, #0
    // 0xc0b780: ldr             x0, [SP, #0x18]
    // 0xc0b784: r16 = UnlinkedCall_0x613b5c
    //     0xc0b784: add             x16, PP, #0x52, lsl #12  ; [pp+0x52490] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0b788: add             x16, x16, #0x490
    // 0xc0b78c: ldp             x5, lr, [x16]
    // 0xc0b790: blr             lr
    // 0xc0b794: r0 = Null
    //     0xc0b794: mov             x0, NULL
    // 0xc0b798: LeaveFrame
    //     0xc0b798: mov             SP, fp
    //     0xc0b79c: ldp             fp, lr, [SP], #0x10
    // 0xc0b7a0: ret
    //     0xc0b7a0: ret             
    // 0xc0b7a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0b7a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0b7a8: b               #0xc0b744
    // 0xc0b7ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b7ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String) {
    // ** addr: 0xc0b7b0, size: 0x8c
    // 0xc0b7b0: EnterFrame
    //     0xc0b7b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc0b7b4: mov             fp, SP
    // 0xc0b7b8: AllocStack(0x20)
    //     0xc0b7b8: sub             SP, SP, #0x20
    // 0xc0b7bc: SetupParameters()
    //     0xc0b7bc: ldr             x0, [fp, #0x20]
    //     0xc0b7c0: ldur            w1, [x0, #0x17]
    //     0xc0b7c4: add             x1, x1, HEAP, lsl #32
    // 0xc0b7c8: CheckStackOverflow
    //     0xc0b7c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0b7cc: cmp             SP, x16
    //     0xc0b7d0: b.ls            #0xc0b830
    // 0xc0b7d4: LoadField: r0 = r1->field_f
    //     0xc0b7d4: ldur            w0, [x1, #0xf]
    // 0xc0b7d8: DecompressPointer r0
    //     0xc0b7d8: add             x0, x0, HEAP, lsl #32
    // 0xc0b7dc: LoadField: r1 = r0->field_b
    //     0xc0b7dc: ldur            w1, [x0, #0xb]
    // 0xc0b7e0: DecompressPointer r1
    //     0xc0b7e0: add             x1, x1, HEAP, lsl #32
    // 0xc0b7e4: cmp             w1, NULL
    // 0xc0b7e8: b.eq            #0xc0b838
    // 0xc0b7ec: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc0b7ec: ldur            w0, [x1, #0x17]
    // 0xc0b7f0: DecompressPointer r0
    //     0xc0b7f0: add             x0, x0, HEAP, lsl #32
    // 0xc0b7f4: ldr             x16, [fp, #0x18]
    // 0xc0b7f8: stp             x16, x0, [SP, #0x10]
    // 0xc0b7fc: ldr             x16, [fp, #0x10]
    // 0xc0b800: r30 = false
    //     0xc0b800: add             lr, NULL, #0x30  ; false
    // 0xc0b804: stp             lr, x16, [SP]
    // 0xc0b808: r4 = 0
    //     0xc0b808: movz            x4, #0
    // 0xc0b80c: ldr             x0, [SP, #0x18]
    // 0xc0b810: r16 = UnlinkedCall_0x613b5c
    //     0xc0b810: add             x16, PP, #0x52, lsl #12  ; [pp+0x524a0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0b814: add             x16, x16, #0x4a0
    // 0xc0b818: ldp             x5, lr, [x16]
    // 0xc0b81c: blr             lr
    // 0xc0b820: r0 = Null
    //     0xc0b820: mov             x0, NULL
    // 0xc0b824: LeaveFrame
    //     0xc0b824: mov             SP, fp
    //     0xc0b828: ldp             fp, lr, [SP], #0x10
    // 0xc0b82c: ret
    //     0xc0b82c: ret             
    // 0xc0b830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0b830: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0b834: b               #0xc0b7d4
    // 0xc0b838: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b838: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc0b83c, size: 0xe0
    // 0xc0b83c: EnterFrame
    //     0xc0b83c: stp             fp, lr, [SP, #-0x10]!
    //     0xc0b840: mov             fp, SP
    // 0xc0b844: AllocStack(0x20)
    //     0xc0b844: sub             SP, SP, #0x20
    // 0xc0b848: SetupParameters()
    //     0xc0b848: ldr             x0, [fp, #0x10]
    //     0xc0b84c: ldur            w1, [x0, #0x17]
    //     0xc0b850: add             x1, x1, HEAP, lsl #32
    // 0xc0b854: CheckStackOverflow
    //     0xc0b854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0b858: cmp             SP, x16
    //     0xc0b85c: b.ls            #0xc0b910
    // 0xc0b860: LoadField: r0 = r1->field_f
    //     0xc0b860: ldur            w0, [x1, #0xf]
    // 0xc0b864: DecompressPointer r0
    //     0xc0b864: add             x0, x0, HEAP, lsl #32
    // 0xc0b868: LoadField: r1 = r0->field_b
    //     0xc0b868: ldur            w1, [x0, #0xb]
    // 0xc0b86c: DecompressPointer r1
    //     0xc0b86c: add             x1, x1, HEAP, lsl #32
    // 0xc0b870: cmp             w1, NULL
    // 0xc0b874: b.eq            #0xc0b918
    // 0xc0b878: LoadField: r0 = r1->field_f
    //     0xc0b878: ldur            w0, [x1, #0xf]
    // 0xc0b87c: DecompressPointer r0
    //     0xc0b87c: add             x0, x0, HEAP, lsl #32
    // 0xc0b880: LoadField: r2 = r0->field_f
    //     0xc0b880: ldur            w2, [x0, #0xf]
    // 0xc0b884: DecompressPointer r2
    //     0xc0b884: add             x2, x2, HEAP, lsl #32
    // 0xc0b888: cmp             w2, NULL
    // 0xc0b88c: b.ne            #0xc0b898
    // 0xc0b890: r0 = Null
    //     0xc0b890: mov             x0, NULL
    // 0xc0b894: b               #0xc0b8a0
    // 0xc0b898: LoadField: r0 = r2->field_f
    //     0xc0b898: ldur            w0, [x2, #0xf]
    // 0xc0b89c: DecompressPointer r0
    //     0xc0b89c: add             x0, x0, HEAP, lsl #32
    // 0xc0b8a0: cmp             w0, NULL
    // 0xc0b8a4: b.ne            #0xc0b8b0
    // 0xc0b8a8: r0 = 0
    //     0xc0b8a8: movz            x0, #0
    // 0xc0b8ac: b               #0xc0b8c0
    // 0xc0b8b0: r2 = LoadInt32Instr(r0)
    //     0xc0b8b0: sbfx            x2, x0, #1, #0x1f
    //     0xc0b8b4: tbz             w0, #0, #0xc0b8bc
    //     0xc0b8b8: ldur            x2, [x0, #7]
    // 0xc0b8bc: mov             x0, x2
    // 0xc0b8c0: cmp             x0, #3
    // 0xc0b8c4: b.lt            #0xc0b900
    // 0xc0b8c8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc0b8c8: ldur            w0, [x1, #0x17]
    // 0xc0b8cc: DecompressPointer r0
    //     0xc0b8cc: add             x0, x0, HEAP, lsl #32
    // 0xc0b8d0: r16 = "view_all"
    //     0xc0b8d0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0xc0b8d4: ldr             x16, [x16, #0xba0]
    // 0xc0b8d8: stp             x16, x0, [SP, #0x10]
    // 0xc0b8dc: r16 = ""
    //     0xc0b8dc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0b8e0: r30 = true
    //     0xc0b8e0: add             lr, NULL, #0x20  ; true
    // 0xc0b8e4: stp             lr, x16, [SP]
    // 0xc0b8e8: r4 = 0
    //     0xc0b8e8: movz            x4, #0
    // 0xc0b8ec: ldr             x0, [SP, #0x18]
    // 0xc0b8f0: r16 = UnlinkedCall_0x613b5c
    //     0xc0b8f0: add             x16, PP, #0x52, lsl #12  ; [pp+0x525c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0b8f4: add             x16, x16, #0x5c8
    // 0xc0b8f8: ldp             x5, lr, [x16]
    // 0xc0b8fc: blr             lr
    // 0xc0b900: r0 = Null
    //     0xc0b900: mov             x0, NULL
    // 0xc0b904: LeaveFrame
    //     0xc0b904: mov             SP, fp
    //     0xc0b908: ldp             fp, lr, [SP], #0x10
    // 0xc0b90c: ret
    //     0xc0b90c: ret             
    // 0xc0b910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0b910: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0b914: b               #0xc0b860
    // 0xc0b918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b918: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc0b91c, size: 0xdc
    // 0xc0b91c: EnterFrame
    //     0xc0b91c: stp             fp, lr, [SP, #-0x10]!
    //     0xc0b920: mov             fp, SP
    // 0xc0b924: AllocStack(0x38)
    //     0xc0b924: sub             SP, SP, #0x38
    // 0xc0b928: SetupParameters()
    //     0xc0b928: ldr             x0, [fp, #0x10]
    //     0xc0b92c: ldur            w1, [x0, #0x17]
    //     0xc0b930: add             x1, x1, HEAP, lsl #32
    //     0xc0b934: stur            x1, [fp, #-8]
    // 0xc0b938: CheckStackOverflow
    //     0xc0b938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0b93c: cmp             SP, x16
    //     0xc0b940: b.ls            #0xc0b9ec
    // 0xc0b944: LoadField: r0 = r1->field_f
    //     0xc0b944: ldur            w0, [x1, #0xf]
    // 0xc0b948: DecompressPointer r0
    //     0xc0b948: add             x0, x0, HEAP, lsl #32
    // 0xc0b94c: LoadField: r2 = r0->field_b
    //     0xc0b94c: ldur            w2, [x0, #0xb]
    // 0xc0b950: DecompressPointer r2
    //     0xc0b950: add             x2, x2, HEAP, lsl #32
    // 0xc0b954: cmp             w2, NULL
    // 0xc0b958: b.eq            #0xc0b9f4
    // 0xc0b95c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc0b95c: ldur            w0, [x2, #0x17]
    // 0xc0b960: DecompressPointer r0
    //     0xc0b960: add             x0, x0, HEAP, lsl #32
    // 0xc0b964: r16 = "trusted_badge"
    //     0xc0b964: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd58] "trusted_badge"
    //     0xc0b968: ldr             x16, [x16, #0xd58]
    // 0xc0b96c: stp             x16, x0, [SP, #0x10]
    // 0xc0b970: r16 = ""
    //     0xc0b970: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0b974: r30 = false
    //     0xc0b974: add             lr, NULL, #0x30  ; false
    // 0xc0b978: stp             lr, x16, [SP]
    // 0xc0b97c: r4 = 0
    //     0xc0b97c: movz            x4, #0
    // 0xc0b980: ldr             x0, [SP, #0x18]
    // 0xc0b984: r16 = UnlinkedCall_0x613b5c
    //     0xc0b984: add             x16, PP, #0x52, lsl #12  ; [pp+0x525d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0b988: add             x16, x16, #0x5d8
    // 0xc0b98c: ldp             x5, lr, [x16]
    // 0xc0b990: blr             lr
    // 0xc0b994: ldur            x0, [fp, #-8]
    // 0xc0b998: LoadField: r3 = r0->field_13
    //     0xc0b998: ldur            w3, [x0, #0x13]
    // 0xc0b99c: DecompressPointer r3
    //     0xc0b99c: add             x3, x3, HEAP, lsl #32
    // 0xc0b9a0: stur            x3, [fp, #-0x10]
    // 0xc0b9a4: r1 = Function '<anonymous closure>':.
    //     0xc0b9a4: add             x1, PP, #0x52, lsl #12  ; [pp+0x525e8] AnonymousClosure: (0x999958), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::appBar (0x15edb38)
    //     0xc0b9a8: ldr             x1, [x1, #0x5e8]
    // 0xc0b9ac: r2 = Null
    //     0xc0b9ac: mov             x2, NULL
    // 0xc0b9b0: r0 = AllocateClosure()
    //     0xc0b9b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0b9b4: stp             x0, NULL, [SP, #0x18]
    // 0xc0b9b8: ldur            x16, [fp, #-0x10]
    // 0xc0b9bc: r30 = Instance_RoundedRectangleBorder
    //     0xc0b9bc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xc0b9c0: ldr             lr, [lr, #0xd68]
    // 0xc0b9c4: stp             lr, x16, [SP, #8]
    // 0xc0b9c8: r16 = true
    //     0xc0b9c8: add             x16, NULL, #0x20  ; true
    // 0xc0b9cc: str             x16, [SP]
    // 0xc0b9d0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x3, shape, 0x2, null]
    //     0xc0b9d0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fd70] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0xc0b9d4: ldr             x4, [x4, #0xd70]
    // 0xc0b9d8: r0 = showModalBottomSheet()
    //     0xc0b9d8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xc0b9dc: r0 = Null
    //     0xc0b9dc: mov             x0, NULL
    // 0xc0b9e0: LeaveFrame
    //     0xc0b9e0: mov             SP, fp
    //     0xc0b9e4: ldp             fp, lr, [SP], #0x10
    // 0xc0b9e8: ret
    //     0xc0b9e8: ret             
    // 0xc0b9ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0b9ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0b9f0: b               #0xc0b944
    // 0xc0b9f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0b9f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3966, size: 0x24, field offset: 0xc
//   const constructor, 
class RatingReviewItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8118c, size: 0x24
    // 0xc8118c: EnterFrame
    //     0xc8118c: stp             fp, lr, [SP, #-0x10]!
    //     0xc81190: mov             fp, SP
    // 0xc81194: mov             x0, x1
    // 0xc81198: r1 = <RatingReviewItemView>
    //     0xc81198: add             x1, PP, #0x48, lsl #12  ; [pp+0x48280] TypeArguments: <RatingReviewItemView>
    //     0xc8119c: ldr             x1, [x1, #0x280]
    // 0xc811a0: r0 = _RatingReviewItemViewState()
    //     0xc811a0: bl              #0xc811b0  ; Allocate_RatingReviewItemViewStateStub -> _RatingReviewItemViewState (size=0x14)
    // 0xc811a4: LeaveFrame
    //     0xc811a4: mov             SP, fp
    //     0xc811a8: ldp             fp, lr, [SP], #0x10
    // 0xc811ac: ret
    //     0xc811ac: ret             
  }
}
