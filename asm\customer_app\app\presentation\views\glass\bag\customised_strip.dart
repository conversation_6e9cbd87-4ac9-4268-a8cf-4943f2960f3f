// lib: , url: package:customer_app/app/presentation/views/glass/bag/customised_strip.dart

// class id: 1049347, size: 0x8
class :: {
}

// class id: 3378, size: 0x14, field offset: 0x14
class _CustomisedStripState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb32cd4, size: 0x434
    // 0xb32cd4: EnterFrame
    //     0xb32cd4: stp             fp, lr, [SP, #-0x10]!
    //     0xb32cd8: mov             fp, SP
    // 0xb32cdc: AllocStack(0x50)
    //     0xb32cdc: sub             SP, SP, #0x50
    // 0xb32ce0: SetupParameters(_CustomisedStripState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb32ce0: mov             x0, x1
    //     0xb32ce4: stur            x1, [fp, #-8]
    //     0xb32ce8: mov             x1, x2
    //     0xb32cec: stur            x2, [fp, #-0x10]
    // 0xb32cf0: CheckStackOverflow
    //     0xb32cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb32cf4: cmp             SP, x16
    //     0xb32cf8: b.ls            #0xb330f4
    // 0xb32cfc: r1 = 1
    //     0xb32cfc: movz            x1, #0x1
    // 0xb32d00: r0 = AllocateContext()
    //     0xb32d00: bl              #0x16f6108  ; AllocateContextStub
    // 0xb32d04: mov             x1, x0
    // 0xb32d08: ldur            x0, [fp, #-8]
    // 0xb32d0c: stur            x1, [fp, #-0x18]
    // 0xb32d10: StoreField: r1->field_f = r0
    //     0xb32d10: stur            w0, [x1, #0xf]
    // 0xb32d14: r0 = Radius()
    //     0xb32d14: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb32d18: d0 = 15.000000
    //     0xb32d18: fmov            d0, #15.00000000
    // 0xb32d1c: stur            x0, [fp, #-0x20]
    // 0xb32d20: StoreField: r0->field_7 = d0
    //     0xb32d20: stur            d0, [x0, #7]
    // 0xb32d24: StoreField: r0->field_f = d0
    //     0xb32d24: stur            d0, [x0, #0xf]
    // 0xb32d28: r0 = BorderRadius()
    //     0xb32d28: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb32d2c: mov             x2, x0
    // 0xb32d30: ldur            x0, [fp, #-0x20]
    // 0xb32d34: stur            x2, [fp, #-0x28]
    // 0xb32d38: StoreField: r2->field_7 = r0
    //     0xb32d38: stur            w0, [x2, #7]
    // 0xb32d3c: StoreField: r2->field_b = r0
    //     0xb32d3c: stur            w0, [x2, #0xb]
    // 0xb32d40: StoreField: r2->field_f = r0
    //     0xb32d40: stur            w0, [x2, #0xf]
    // 0xb32d44: StoreField: r2->field_13 = r0
    //     0xb32d44: stur            w0, [x2, #0x13]
    // 0xb32d48: ldur            x1, [fp, #-0x10]
    // 0xb32d4c: r0 = of()
    //     0xb32d4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32d50: LoadField: r1 = r0->field_5b
    //     0xb32d50: ldur            w1, [x0, #0x5b]
    // 0xb32d54: DecompressPointer r1
    //     0xb32d54: add             x1, x1, HEAP, lsl #32
    // 0xb32d58: r0 = LoadClassIdInstr(r1)
    //     0xb32d58: ldur            x0, [x1, #-1]
    //     0xb32d5c: ubfx            x0, x0, #0xc, #0x14
    // 0xb32d60: d0 = 0.100000
    //     0xb32d60: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xb32d64: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb32d64: sub             lr, x0, #0xffa
    //     0xb32d68: ldr             lr, [x21, lr, lsl #3]
    //     0xb32d6c: blr             lr
    // 0xb32d70: mov             x2, x0
    // 0xb32d74: r1 = Null
    //     0xb32d74: mov             x1, NULL
    // 0xb32d78: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb32d78: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb32d7c: r0 = Border.all()
    //     0xb32d7c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb32d80: stur            x0, [fp, #-0x20]
    // 0xb32d84: r0 = BoxDecoration()
    //     0xb32d84: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb32d88: mov             x2, x0
    // 0xb32d8c: r0 = Instance_Color
    //     0xb32d8c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb32d90: stur            x2, [fp, #-0x30]
    // 0xb32d94: StoreField: r2->field_7 = r0
    //     0xb32d94: stur            w0, [x2, #7]
    // 0xb32d98: ldur            x0, [fp, #-0x20]
    // 0xb32d9c: StoreField: r2->field_f = r0
    //     0xb32d9c: stur            w0, [x2, #0xf]
    // 0xb32da0: ldur            x0, [fp, #-0x28]
    // 0xb32da4: StoreField: r2->field_13 = r0
    //     0xb32da4: stur            w0, [x2, #0x13]
    // 0xb32da8: r0 = Instance_BoxShape
    //     0xb32da8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb32dac: ldr             x0, [x0, #0x80]
    // 0xb32db0: StoreField: r2->field_23 = r0
    //     0xb32db0: stur            w0, [x2, #0x23]
    // 0xb32db4: ldur            x1, [fp, #-0x10]
    // 0xb32db8: r0 = of()
    //     0xb32db8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32dbc: LoadField: r1 = r0->field_87
    //     0xb32dbc: ldur            w1, [x0, #0x87]
    // 0xb32dc0: DecompressPointer r1
    //     0xb32dc0: add             x1, x1, HEAP, lsl #32
    // 0xb32dc4: LoadField: r0 = r1->field_7
    //     0xb32dc4: ldur            w0, [x1, #7]
    // 0xb32dc8: DecompressPointer r0
    //     0xb32dc8: add             x0, x0, HEAP, lsl #32
    // 0xb32dcc: ldur            x1, [fp, #-0x10]
    // 0xb32dd0: stur            x0, [fp, #-0x20]
    // 0xb32dd4: r0 = of()
    //     0xb32dd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb32dd8: LoadField: r1 = r0->field_5b
    //     0xb32dd8: ldur            w1, [x0, #0x5b]
    // 0xb32ddc: DecompressPointer r1
    //     0xb32ddc: add             x1, x1, HEAP, lsl #32
    // 0xb32de0: r16 = 16.000000
    //     0xb32de0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb32de4: ldr             x16, [x16, #0x188]
    // 0xb32de8: stp             x1, x16, [SP]
    // 0xb32dec: ldur            x1, [fp, #-0x20]
    // 0xb32df0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb32df0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb32df4: ldr             x4, [x4, #0xaa0]
    // 0xb32df8: r0 = copyWith()
    //     0xb32df8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb32dfc: stur            x0, [fp, #-0x20]
    // 0xb32e00: r0 = Text()
    //     0xb32e00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb32e04: mov             x1, x0
    // 0xb32e08: r0 = "Customisation"
    //     0xb32e08: add             x0, PP, #0x56, lsl #12  ; [pp+0x56388] "Customisation"
    //     0xb32e0c: ldr             x0, [x0, #0x388]
    // 0xb32e10: stur            x1, [fp, #-0x28]
    // 0xb32e14: StoreField: r1->field_b = r0
    //     0xb32e14: stur            w0, [x1, #0xb]
    // 0xb32e18: ldur            x0, [fp, #-0x20]
    // 0xb32e1c: StoreField: r1->field_13 = r0
    //     0xb32e1c: stur            w0, [x1, #0x13]
    // 0xb32e20: r0 = Padding()
    //     0xb32e20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb32e24: mov             x2, x0
    // 0xb32e28: r0 = Instance_EdgeInsets
    //     0xb32e28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb32e2c: ldr             x0, [x0, #0x980]
    // 0xb32e30: stur            x2, [fp, #-0x20]
    // 0xb32e34: StoreField: r2->field_f = r0
    //     0xb32e34: stur            w0, [x2, #0xf]
    // 0xb32e38: ldur            x0, [fp, #-0x28]
    // 0xb32e3c: StoreField: r2->field_b = r0
    //     0xb32e3c: stur            w0, [x2, #0xb]
    // 0xb32e40: ldur            x3, [fp, #-8]
    // 0xb32e44: LoadField: r0 = r3->field_b
    //     0xb32e44: ldur            w0, [x3, #0xb]
    // 0xb32e48: DecompressPointer r0
    //     0xb32e48: add             x0, x0, HEAP, lsl #32
    // 0xb32e4c: cmp             w0, NULL
    // 0xb32e50: b.eq            #0xb330fc
    // 0xb32e54: LoadField: r1 = r0->field_b
    //     0xb32e54: ldur            w1, [x0, #0xb]
    // 0xb32e58: DecompressPointer r1
    //     0xb32e58: add             x1, x1, HEAP, lsl #32
    // 0xb32e5c: cmp             w1, NULL
    // 0xb32e60: b.ne            #0xb32e6c
    // 0xb32e64: r0 = Null
    //     0xb32e64: mov             x0, NULL
    // 0xb32e68: b               #0xb32e84
    // 0xb32e6c: r0 = LoadClassIdInstr(r1)
    //     0xb32e6c: ldur            x0, [x1, #-1]
    //     0xb32e70: ubfx            x0, x0, #0xc, #0x14
    // 0xb32e74: r0 = GDT[cid_x0 + 0xe517]()
    //     0xb32e74: movz            x17, #0xe517
    //     0xb32e78: add             lr, x0, x17
    //     0xb32e7c: ldr             lr, [x21, lr, lsl #3]
    //     0xb32e80: blr             lr
    // 0xb32e84: cmp             w0, NULL
    // 0xb32e88: b.ne            #0xb32e94
    // 0xb32e8c: ldur            x0, [fp, #-8]
    // 0xb32e90: b               #0xb32f60
    // 0xb32e94: tbnz            w0, #4, #0xb32f5c
    // 0xb32e98: ldur            x0, [fp, #-8]
    // 0xb32e9c: LoadField: r1 = r0->field_b
    //     0xb32e9c: ldur            w1, [x0, #0xb]
    // 0xb32ea0: DecompressPointer r1
    //     0xb32ea0: add             x1, x1, HEAP, lsl #32
    // 0xb32ea4: cmp             w1, NULL
    // 0xb32ea8: b.eq            #0xb33100
    // 0xb32eac: LoadField: r0 = r1->field_b
    //     0xb32eac: ldur            w0, [x1, #0xb]
    // 0xb32eb0: DecompressPointer r0
    //     0xb32eb0: add             x0, x0, HEAP, lsl #32
    // 0xb32eb4: cmp             w0, NULL
    // 0xb32eb8: b.ne            #0xb32ec4
    // 0xb32ebc: r0 = Null
    //     0xb32ebc: mov             x0, NULL
    // 0xb32ec0: b               #0xb32ee4
    // 0xb32ec4: r1 = LoadClassIdInstr(r0)
    //     0xb32ec4: ldur            x1, [x0, #-1]
    //     0xb32ec8: ubfx            x1, x1, #0xc, #0x14
    // 0xb32ecc: str             x0, [SP]
    // 0xb32ed0: mov             x0, x1
    // 0xb32ed4: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb32ed4: movz            x17, #0xc898
    //     0xb32ed8: add             lr, x0, x17
    //     0xb32edc: ldr             lr, [x21, lr, lsl #3]
    //     0xb32ee0: blr             lr
    // 0xb32ee4: cmp             w0, NULL
    // 0xb32ee8: b.ne            #0xb32ef4
    // 0xb32eec: r0 = 0
    //     0xb32eec: movz            x0, #0
    // 0xb32ef0: b               #0xb32efc
    // 0xb32ef4: r1 = LoadInt32Instr(r0)
    //     0xb32ef4: sbfx            x1, x0, #1, #0x1f
    // 0xb32ef8: mov             x0, x1
    // 0xb32efc: add             x2, x0, #1
    // 0xb32f00: r0 = BoxInt64Instr(r2)
    //     0xb32f00: sbfiz           x0, x2, #1, #0x1f
    //     0xb32f04: cmp             x2, x0, asr #1
    //     0xb32f08: b.eq            #0xb32f14
    //     0xb32f0c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb32f10: stur            x2, [x0, #7]
    // 0xb32f14: ldur            x2, [fp, #-0x18]
    // 0xb32f18: r1 = Function '<anonymous closure>':.
    //     0xb32f18: add             x1, PP, #0x57, lsl #12  ; [pp+0x57250] AnonymousClosure: (0xb33aa4), in [package:customer_app/app/presentation/views/glass/bag/customised_strip.dart] _CustomisedStripState::build (0xb32cd4)
    //     0xb32f1c: ldr             x1, [x1, #0x250]
    // 0xb32f20: stur            x0, [fp, #-0x28]
    // 0xb32f24: r0 = AllocateClosure()
    //     0xb32f24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb32f28: stur            x0, [fp, #-0x38]
    // 0xb32f2c: r0 = ListView()
    //     0xb32f2c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb32f30: stur            x0, [fp, #-0x40]
    // 0xb32f34: r16 = true
    //     0xb32f34: add             x16, NULL, #0x20  ; true
    // 0xb32f38: str             x16, [SP]
    // 0xb32f3c: mov             x1, x0
    // 0xb32f40: ldur            x2, [fp, #-0x38]
    // 0xb32f44: ldur            x3, [fp, #-0x28]
    // 0xb32f48: r4 = const [0, 0x4, 0x1, 0x3, shrinkWrap, 0x3, null]
    //     0xb32f48: add             x4, PP, #0x49, lsl #12  ; [pp+0x490f0] List(7) [0, 0x4, 0x1, 0x3, "shrinkWrap", 0x3, Null]
    //     0xb32f4c: ldr             x4, [x4, #0xf0]
    // 0xb32f50: r0 = ListView.builder()
    //     0xb32f50: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb32f54: ldur            x2, [fp, #-0x40]
    // 0xb32f58: b               #0xb33018
    // 0xb32f5c: ldur            x0, [fp, #-8]
    // 0xb32f60: LoadField: r1 = r0->field_b
    //     0xb32f60: ldur            w1, [x0, #0xb]
    // 0xb32f64: DecompressPointer r1
    //     0xb32f64: add             x1, x1, HEAP, lsl #32
    // 0xb32f68: cmp             w1, NULL
    // 0xb32f6c: b.eq            #0xb33104
    // 0xb32f70: LoadField: r0 = r1->field_f
    //     0xb32f70: ldur            w0, [x1, #0xf]
    // 0xb32f74: DecompressPointer r0
    //     0xb32f74: add             x0, x0, HEAP, lsl #32
    // 0xb32f78: cmp             w0, NULL
    // 0xb32f7c: b.ne            #0xb32f88
    // 0xb32f80: r0 = Null
    //     0xb32f80: mov             x0, NULL
    // 0xb32f84: b               #0xb32fa4
    // 0xb32f88: LoadField: r1 = r0->field_13
    //     0xb32f88: ldur            w1, [x0, #0x13]
    // 0xb32f8c: DecompressPointer r1
    //     0xb32f8c: add             x1, x1, HEAP, lsl #32
    // 0xb32f90: cmp             w1, NULL
    // 0xb32f94: b.ne            #0xb32fa0
    // 0xb32f98: r0 = Null
    //     0xb32f98: mov             x0, NULL
    // 0xb32f9c: b               #0xb32fa4
    // 0xb32fa0: LoadField: r0 = r1->field_b
    //     0xb32fa0: ldur            w0, [x1, #0xb]
    // 0xb32fa4: cmp             w0, NULL
    // 0xb32fa8: b.ne            #0xb32fb4
    // 0xb32fac: r0 = 0
    //     0xb32fac: movz            x0, #0
    // 0xb32fb0: b               #0xb32fbc
    // 0xb32fb4: r1 = LoadInt32Instr(r0)
    //     0xb32fb4: sbfx            x1, x0, #1, #0x1f
    // 0xb32fb8: mov             x0, x1
    // 0xb32fbc: add             x2, x0, #1
    // 0xb32fc0: r0 = BoxInt64Instr(r2)
    //     0xb32fc0: sbfiz           x0, x2, #1, #0x1f
    //     0xb32fc4: cmp             x2, x0, asr #1
    //     0xb32fc8: b.eq            #0xb32fd4
    //     0xb32fcc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb32fd0: stur            x2, [x0, #7]
    // 0xb32fd4: ldur            x2, [fp, #-0x18]
    // 0xb32fd8: r1 = Function '<anonymous closure>':.
    //     0xb32fd8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57258] AnonymousClosure: (0xb33108), in [package:customer_app/app/presentation/views/glass/bag/customised_strip.dart] _CustomisedStripState::build (0xb32cd4)
    //     0xb32fdc: ldr             x1, [x1, #0x258]
    // 0xb32fe0: stur            x0, [fp, #-8]
    // 0xb32fe4: r0 = AllocateClosure()
    //     0xb32fe4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb32fe8: stur            x0, [fp, #-0x18]
    // 0xb32fec: r0 = ListView()
    //     0xb32fec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb32ff0: stur            x0, [fp, #-0x28]
    // 0xb32ff4: r16 = true
    //     0xb32ff4: add             x16, NULL, #0x20  ; true
    // 0xb32ff8: str             x16, [SP]
    // 0xb32ffc: mov             x1, x0
    // 0xb33000: ldur            x2, [fp, #-0x18]
    // 0xb33004: ldur            x3, [fp, #-8]
    // 0xb33008: r4 = const [0, 0x4, 0x1, 0x3, shrinkWrap, 0x3, null]
    //     0xb33008: add             x4, PP, #0x49, lsl #12  ; [pp+0x490f0] List(7) [0, 0x4, 0x1, 0x3, "shrinkWrap", 0x3, Null]
    //     0xb3300c: ldr             x4, [x4, #0xf0]
    // 0xb33010: r0 = ListView.builder()
    //     0xb33010: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb33014: ldur            x2, [fp, #-0x28]
    // 0xb33018: ldur            x0, [fp, #-0x20]
    // 0xb3301c: ldur            x1, [fp, #-0x10]
    // 0xb33020: stur            x2, [fp, #-8]
    // 0xb33024: r0 = of()
    //     0xb33024: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb33028: LoadField: r1 = r0->field_87
    //     0xb33028: ldur            w1, [x0, #0x87]
    // 0xb3302c: DecompressPointer r1
    //     0xb3302c: add             x1, x1, HEAP, lsl #32
    // 0xb33030: LoadField: r0 = r1->field_7
    //     0xb33030: ldur            w0, [x1, #7]
    // 0xb33034: DecompressPointer r0
    //     0xb33034: add             x0, x0, HEAP, lsl #32
    // 0xb33038: r16 = 14.000000
    //     0xb33038: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3303c: ldr             x16, [x16, #0x1d8]
    // 0xb33040: r30 = Instance_Color
    //     0xb33040: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb33044: stp             lr, x16, [SP]
    // 0xb33048: mov             x1, x0
    // 0xb3304c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3304c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb33050: ldr             x4, [x4, #0xaa0]
    // 0xb33054: r0 = copyWith()
    //     0xb33054: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb33058: r0 = Accordion()
    //     0xb33058: bl              #0xa2303c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xb3305c: mov             x1, x0
    // 0xb33060: ldur            x0, [fp, #-0x20]
    // 0xb33064: stur            x1, [fp, #-0x10]
    // 0xb33068: StoreField: r1->field_b = r0
    //     0xb33068: stur            w0, [x1, #0xb]
    // 0xb3306c: ldur            x0, [fp, #-8]
    // 0xb33070: StoreField: r1->field_13 = r0
    //     0xb33070: stur            w0, [x1, #0x13]
    // 0xb33074: r0 = false
    //     0xb33074: add             x0, NULL, #0x30  ; false
    // 0xb33078: ArrayStore: r1[0] = r0  ; List_4
    //     0xb33078: stur            w0, [x1, #0x17]
    // 0xb3307c: d0 = 24.000000
    //     0xb3307c: fmov            d0, #24.00000000
    // 0xb33080: StoreField: r1->field_1b = d0
    //     0xb33080: stur            d0, [x1, #0x1b]
    // 0xb33084: r0 = true
    //     0xb33084: add             x0, NULL, #0x20  ; true
    // 0xb33088: StoreField: r1->field_23 = r0
    //     0xb33088: stur            w0, [x1, #0x23]
    // 0xb3308c: r0 = Padding()
    //     0xb3308c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb33090: mov             x1, x0
    // 0xb33094: r0 = Instance_EdgeInsets
    //     0xb33094: add             x0, PP, #0x33, lsl #12  ; [pp+0x33f30] Obj!EdgeInsets@d571d1
    //     0xb33098: ldr             x0, [x0, #0xf30]
    // 0xb3309c: stur            x1, [fp, #-8]
    // 0xb330a0: StoreField: r1->field_f = r0
    //     0xb330a0: stur            w0, [x1, #0xf]
    // 0xb330a4: ldur            x0, [fp, #-0x10]
    // 0xb330a8: StoreField: r1->field_b = r0
    //     0xb330a8: stur            w0, [x1, #0xb]
    // 0xb330ac: r0 = Container()
    //     0xb330ac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb330b0: stur            x0, [fp, #-0x10]
    // 0xb330b4: ldur            x16, [fp, #-0x30]
    // 0xb330b8: ldur            lr, [fp, #-8]
    // 0xb330bc: stp             lr, x16, [SP]
    // 0xb330c0: mov             x1, x0
    // 0xb330c4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb330c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb330c8: ldr             x4, [x4, #0x88]
    // 0xb330cc: r0 = Container()
    //     0xb330cc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb330d0: r0 = Padding()
    //     0xb330d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb330d4: r1 = Instance_EdgeInsets
    //     0xb330d4: add             x1, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb330d8: ldr             x1, [x1, #0x668]
    // 0xb330dc: StoreField: r0->field_f = r1
    //     0xb330dc: stur            w1, [x0, #0xf]
    // 0xb330e0: ldur            x1, [fp, #-0x10]
    // 0xb330e4: StoreField: r0->field_b = r1
    //     0xb330e4: stur            w1, [x0, #0xb]
    // 0xb330e8: LeaveFrame
    //     0xb330e8: mov             SP, fp
    //     0xb330ec: ldp             fp, lr, [SP], #0x10
    // 0xb330f0: ret
    //     0xb330f0: ret             
    // 0xb330f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb330f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb330f8: b               #0xb32cfc
    // 0xb330fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb330fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33100: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33100: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33104: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33104: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb33108, size: 0x96c
    // 0xb33108: EnterFrame
    //     0xb33108: stp             fp, lr, [SP, #-0x10]!
    //     0xb3310c: mov             fp, SP
    // 0xb33110: AllocStack(0x40)
    //     0xb33110: sub             SP, SP, #0x40
    // 0xb33114: SetupParameters()
    //     0xb33114: ldr             x0, [fp, #0x20]
    //     0xb33118: ldur            w1, [x0, #0x17]
    //     0xb3311c: add             x1, x1, HEAP, lsl #32
    //     0xb33120: stur            x1, [fp, #-8]
    // 0xb33124: CheckStackOverflow
    //     0xb33124: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb33128: cmp             SP, x16
    //     0xb3312c: b.ls            #0xb33a38
    // 0xb33130: r0 = Container()
    //     0xb33130: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb33134: mov             x1, x0
    // 0xb33138: stur            x0, [fp, #-0x10]
    // 0xb3313c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb3313c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb33140: r0 = Container()
    //     0xb33140: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb33144: ldur            x0, [fp, #-8]
    // 0xb33148: LoadField: r1 = r0->field_f
    //     0xb33148: ldur            w1, [x0, #0xf]
    // 0xb3314c: DecompressPointer r1
    //     0xb3314c: add             x1, x1, HEAP, lsl #32
    // 0xb33150: LoadField: r2 = r1->field_b
    //     0xb33150: ldur            w2, [x1, #0xb]
    // 0xb33154: DecompressPointer r2
    //     0xb33154: add             x2, x2, HEAP, lsl #32
    // 0xb33158: cmp             w2, NULL
    // 0xb3315c: b.eq            #0xb33a40
    // 0xb33160: LoadField: r1 = r2->field_f
    //     0xb33160: ldur            w1, [x2, #0xf]
    // 0xb33164: DecompressPointer r1
    //     0xb33164: add             x1, x1, HEAP, lsl #32
    // 0xb33168: cmp             w1, NULL
    // 0xb3316c: b.ne            #0xb33178
    // 0xb33170: r4 = Null
    //     0xb33170: mov             x4, NULL
    // 0xb33174: b               #0xb3319c
    // 0xb33178: LoadField: r3 = r1->field_13
    //     0xb33178: ldur            w3, [x1, #0x13]
    // 0xb3317c: DecompressPointer r3
    //     0xb3317c: add             x3, x3, HEAP, lsl #32
    // 0xb33180: cmp             w3, NULL
    // 0xb33184: b.ne            #0xb33190
    // 0xb33188: r3 = Null
    //     0xb33188: mov             x3, NULL
    // 0xb3318c: b               #0xb33198
    // 0xb33190: LoadField: r4 = r3->field_b
    //     0xb33190: ldur            w4, [x3, #0xb]
    // 0xb33194: mov             x3, x4
    // 0xb33198: mov             x4, x3
    // 0xb3319c: ldr             x3, [fp, #0x10]
    // 0xb331a0: cmp             w4, w3
    // 0xb331a4: b.ne            #0xb3342c
    // 0xb331a8: ldr             x1, [fp, #0x18]
    // 0xb331ac: r0 = of()
    //     0xb331ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb331b0: LoadField: r1 = r0->field_87
    //     0xb331b0: ldur            w1, [x0, #0x87]
    // 0xb331b4: DecompressPointer r1
    //     0xb331b4: add             x1, x1, HEAP, lsl #32
    // 0xb331b8: LoadField: r0 = r1->field_7
    //     0xb331b8: ldur            w0, [x1, #7]
    // 0xb331bc: DecompressPointer r0
    //     0xb331bc: add             x0, x0, HEAP, lsl #32
    // 0xb331c0: r16 = 14.000000
    //     0xb331c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb331c4: ldr             x16, [x16, #0x1d8]
    // 0xb331c8: r30 = Instance_Color
    //     0xb331c8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb331cc: stp             lr, x16, [SP]
    // 0xb331d0: mov             x1, x0
    // 0xb331d4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb331d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb331d8: ldr             x4, [x4, #0xaa0]
    // 0xb331dc: r0 = copyWith()
    //     0xb331dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb331e0: stur            x0, [fp, #-0x18]
    // 0xb331e4: r0 = Text()
    //     0xb331e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb331e8: mov             x1, x0
    // 0xb331ec: r0 = "Total"
    //     0xb331ec: add             x0, PP, #0x54, lsl #12  ; [pp+0x54bc8] "Total"
    //     0xb331f0: ldr             x0, [x0, #0xbc8]
    // 0xb331f4: stur            x1, [fp, #-0x20]
    // 0xb331f8: StoreField: r1->field_b = r0
    //     0xb331f8: stur            w0, [x1, #0xb]
    // 0xb331fc: ldur            x0, [fp, #-0x18]
    // 0xb33200: StoreField: r1->field_13 = r0
    //     0xb33200: stur            w0, [x1, #0x13]
    // 0xb33204: ldur            x4, [fp, #-8]
    // 0xb33208: LoadField: r0 = r4->field_f
    //     0xb33208: ldur            w0, [x4, #0xf]
    // 0xb3320c: DecompressPointer r0
    //     0xb3320c: add             x0, x0, HEAP, lsl #32
    // 0xb33210: LoadField: r2 = r0->field_b
    //     0xb33210: ldur            w2, [x0, #0xb]
    // 0xb33214: DecompressPointer r2
    //     0xb33214: add             x2, x2, HEAP, lsl #32
    // 0xb33218: cmp             w2, NULL
    // 0xb3321c: b.eq            #0xb33a44
    // 0xb33220: LoadField: r0 = r2->field_13
    //     0xb33220: ldur            w0, [x2, #0x13]
    // 0xb33224: DecompressPointer r0
    //     0xb33224: add             x0, x0, HEAP, lsl #32
    // 0xb33228: r2 = LoadClassIdInstr(r0)
    //     0xb33228: ldur            x2, [x0, #-1]
    //     0xb3322c: ubfx            x2, x2, #0xc, #0x14
    // 0xb33230: str             x0, [SP]
    // 0xb33234: mov             x0, x2
    // 0xb33238: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb33238: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb3323c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb3323c: movz            x17, #0x2700
    //     0xb33240: add             lr, x0, x17
    //     0xb33244: ldr             lr, [x21, lr, lsl #3]
    //     0xb33248: blr             lr
    // 0xb3324c: ldr             x1, [fp, #0x18]
    // 0xb33250: stur            x0, [fp, #-0x18]
    // 0xb33254: r0 = of()
    //     0xb33254: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb33258: LoadField: r1 = r0->field_87
    //     0xb33258: ldur            w1, [x0, #0x87]
    // 0xb3325c: DecompressPointer r1
    //     0xb3325c: add             x1, x1, HEAP, lsl #32
    // 0xb33260: LoadField: r0 = r1->field_7
    //     0xb33260: ldur            w0, [x1, #7]
    // 0xb33264: DecompressPointer r0
    //     0xb33264: add             x0, x0, HEAP, lsl #32
    // 0xb33268: r16 = 14.000000
    //     0xb33268: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb3326c: ldr             x16, [x16, #0x1d8]
    // 0xb33270: r30 = Instance_Color
    //     0xb33270: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb33274: stp             lr, x16, [SP]
    // 0xb33278: mov             x1, x0
    // 0xb3327c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb3327c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb33280: ldr             x4, [x4, #0xaa0]
    // 0xb33284: r0 = copyWith()
    //     0xb33284: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb33288: stur            x0, [fp, #-0x28]
    // 0xb3328c: r0 = Text()
    //     0xb3328c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb33290: mov             x3, x0
    // 0xb33294: ldur            x0, [fp, #-0x18]
    // 0xb33298: stur            x3, [fp, #-0x30]
    // 0xb3329c: StoreField: r3->field_b = r0
    //     0xb3329c: stur            w0, [x3, #0xb]
    // 0xb332a0: ldur            x0, [fp, #-0x28]
    // 0xb332a4: StoreField: r3->field_13 = r0
    //     0xb332a4: stur            w0, [x3, #0x13]
    // 0xb332a8: r1 = Null
    //     0xb332a8: mov             x1, NULL
    // 0xb332ac: r2 = 6
    //     0xb332ac: movz            x2, #0x6
    // 0xb332b0: r0 = AllocateArray()
    //     0xb332b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb332b4: mov             x2, x0
    // 0xb332b8: ldur            x0, [fp, #-0x20]
    // 0xb332bc: stur            x2, [fp, #-0x18]
    // 0xb332c0: StoreField: r2->field_f = r0
    //     0xb332c0: stur            w0, [x2, #0xf]
    // 0xb332c4: r16 = Instance_Spacer
    //     0xb332c4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb332c8: ldr             x16, [x16, #0xf0]
    // 0xb332cc: StoreField: r2->field_13 = r16
    //     0xb332cc: stur            w16, [x2, #0x13]
    // 0xb332d0: ldur            x0, [fp, #-0x30]
    // 0xb332d4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb332d4: stur            w0, [x2, #0x17]
    // 0xb332d8: r1 = <Widget>
    //     0xb332d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb332dc: r0 = AllocateGrowableArray()
    //     0xb332dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb332e0: mov             x1, x0
    // 0xb332e4: ldur            x0, [fp, #-0x18]
    // 0xb332e8: stur            x1, [fp, #-0x20]
    // 0xb332ec: StoreField: r1->field_f = r0
    //     0xb332ec: stur            w0, [x1, #0xf]
    // 0xb332f0: r0 = 6
    //     0xb332f0: movz            x0, #0x6
    // 0xb332f4: StoreField: r1->field_b = r0
    //     0xb332f4: stur            w0, [x1, #0xb]
    // 0xb332f8: r0 = Row()
    //     0xb332f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb332fc: mov             x3, x0
    // 0xb33300: r0 = Instance_Axis
    //     0xb33300: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb33304: stur            x3, [fp, #-0x18]
    // 0xb33308: StoreField: r3->field_f = r0
    //     0xb33308: stur            w0, [x3, #0xf]
    // 0xb3330c: r0 = Instance_MainAxisAlignment
    //     0xb3330c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb33310: ldr             x0, [x0, #0xa08]
    // 0xb33314: StoreField: r3->field_13 = r0
    //     0xb33314: stur            w0, [x3, #0x13]
    // 0xb33318: r4 = Instance_MainAxisSize
    //     0xb33318: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb3331c: ldr             x4, [x4, #0xa10]
    // 0xb33320: ArrayStore: r3[0] = r4  ; List_4
    //     0xb33320: stur            w4, [x3, #0x17]
    // 0xb33324: r5 = Instance_CrossAxisAlignment
    //     0xb33324: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb33328: ldr             x5, [x5, #0xa18]
    // 0xb3332c: StoreField: r3->field_1b = r5
    //     0xb3332c: stur            w5, [x3, #0x1b]
    // 0xb33330: r6 = Instance_VerticalDirection
    //     0xb33330: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb33334: ldr             x6, [x6, #0xa20]
    // 0xb33338: StoreField: r3->field_23 = r6
    //     0xb33338: stur            w6, [x3, #0x23]
    // 0xb3333c: r7 = Instance_Clip
    //     0xb3333c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb33340: ldr             x7, [x7, #0x38]
    // 0xb33344: StoreField: r3->field_2b = r7
    //     0xb33344: stur            w7, [x3, #0x2b]
    // 0xb33348: StoreField: r3->field_2f = rZR
    //     0xb33348: stur            xzr, [x3, #0x2f]
    // 0xb3334c: ldur            x1, [fp, #-0x20]
    // 0xb33350: StoreField: r3->field_b = r1
    //     0xb33350: stur            w1, [x3, #0xb]
    // 0xb33354: r1 = Null
    //     0xb33354: mov             x1, NULL
    // 0xb33358: r2 = 4
    //     0xb33358: movz            x2, #0x4
    // 0xb3335c: r0 = AllocateArray()
    //     0xb3335c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb33360: stur            x0, [fp, #-0x20]
    // 0xb33364: r16 = Instance_Divider
    //     0xb33364: add             x16, PP, #0x57, lsl #12  ; [pp+0x57260] Obj!Divider@d66cd1
    //     0xb33368: ldr             x16, [x16, #0x260]
    // 0xb3336c: StoreField: r0->field_f = r16
    //     0xb3336c: stur            w16, [x0, #0xf]
    // 0xb33370: ldur            x1, [fp, #-0x18]
    // 0xb33374: StoreField: r0->field_13 = r1
    //     0xb33374: stur            w1, [x0, #0x13]
    // 0xb33378: r1 = <Widget>
    //     0xb33378: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb3337c: r0 = AllocateGrowableArray()
    //     0xb3337c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb33380: mov             x1, x0
    // 0xb33384: ldur            x0, [fp, #-0x20]
    // 0xb33388: stur            x1, [fp, #-0x18]
    // 0xb3338c: StoreField: r1->field_f = r0
    //     0xb3338c: stur            w0, [x1, #0xf]
    // 0xb33390: r0 = 4
    //     0xb33390: movz            x0, #0x4
    // 0xb33394: StoreField: r1->field_b = r0
    //     0xb33394: stur            w0, [x1, #0xb]
    // 0xb33398: r0 = Column()
    //     0xb33398: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb3339c: mov             x1, x0
    // 0xb333a0: r0 = Instance_Axis
    //     0xb333a0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb333a4: stur            x1, [fp, #-0x20]
    // 0xb333a8: StoreField: r1->field_f = r0
    //     0xb333a8: stur            w0, [x1, #0xf]
    // 0xb333ac: r0 = Instance_MainAxisAlignment
    //     0xb333ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb333b0: ldr             x0, [x0, #0xa08]
    // 0xb333b4: StoreField: r1->field_13 = r0
    //     0xb333b4: stur            w0, [x1, #0x13]
    // 0xb333b8: r0 = Instance_MainAxisSize
    //     0xb333b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb333bc: ldr             x0, [x0, #0xa10]
    // 0xb333c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xb333c0: stur            w0, [x1, #0x17]
    // 0xb333c4: r0 = Instance_CrossAxisAlignment
    //     0xb333c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb333c8: ldr             x0, [x0, #0xa18]
    // 0xb333cc: StoreField: r1->field_1b = r0
    //     0xb333cc: stur            w0, [x1, #0x1b]
    // 0xb333d0: r0 = Instance_VerticalDirection
    //     0xb333d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb333d4: ldr             x0, [x0, #0xa20]
    // 0xb333d8: StoreField: r1->field_23 = r0
    //     0xb333d8: stur            w0, [x1, #0x23]
    // 0xb333dc: r0 = Instance_Clip
    //     0xb333dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb333e0: ldr             x0, [x0, #0x38]
    // 0xb333e4: StoreField: r1->field_2b = r0
    //     0xb333e4: stur            w0, [x1, #0x2b]
    // 0xb333e8: StoreField: r1->field_2f = rZR
    //     0xb333e8: stur            xzr, [x1, #0x2f]
    // 0xb333ec: ldur            x0, [fp, #-0x18]
    // 0xb333f0: StoreField: r1->field_b = r0
    //     0xb333f0: stur            w0, [x1, #0xb]
    // 0xb333f4: r0 = Padding()
    //     0xb333f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb333f8: mov             x1, x0
    // 0xb333fc: r0 = Instance_EdgeInsets
    //     0xb333fc: add             x0, PP, #0x57, lsl #12  ; [pp+0x57268] Obj!EdgeInsets@d58ca1
    //     0xb33400: ldr             x0, [x0, #0x268]
    // 0xb33404: stur            x1, [fp, #-0x18]
    // 0xb33408: StoreField: r1->field_f = r0
    //     0xb33408: stur            w0, [x1, #0xf]
    // 0xb3340c: ldur            x0, [fp, #-0x20]
    // 0xb33410: StoreField: r1->field_b = r0
    //     0xb33410: stur            w0, [x1, #0xb]
    // 0xb33414: r0 = IntrinsicHeight()
    //     0xb33414: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xb33418: mov             x1, x0
    // 0xb3341c: ldur            x0, [fp, #-0x18]
    // 0xb33420: StoreField: r1->field_b = r0
    //     0xb33420: stur            w0, [x1, #0xb]
    // 0xb33424: mov             x0, x1
    // 0xb33428: b               #0xb33a2c
    // 0xb3342c: mov             x4, x0
    // 0xb33430: cmp             w1, NULL
    // 0xb33434: b.ne            #0xb33440
    // 0xb33438: r0 = Null
    //     0xb33438: mov             x0, NULL
    // 0xb3343c: b               #0xb33498
    // 0xb33440: LoadField: r5 = r1->field_13
    //     0xb33440: ldur            w5, [x1, #0x13]
    // 0xb33444: DecompressPointer r5
    //     0xb33444: add             x5, x5, HEAP, lsl #32
    // 0xb33448: cmp             w5, NULL
    // 0xb3344c: b.ne            #0xb33458
    // 0xb33450: r0 = Null
    //     0xb33450: mov             x0, NULL
    // 0xb33454: b               #0xb33498
    // 0xb33458: LoadField: r0 = r5->field_b
    //     0xb33458: ldur            w0, [x5, #0xb]
    // 0xb3345c: r6 = LoadInt32Instr(r3)
    //     0xb3345c: sbfx            x6, x3, #1, #0x1f
    //     0xb33460: tbz             w3, #0, #0xb33468
    //     0xb33464: ldur            x6, [x3, #7]
    // 0xb33468: r1 = LoadInt32Instr(r0)
    //     0xb33468: sbfx            x1, x0, #1, #0x1f
    // 0xb3346c: mov             x0, x1
    // 0xb33470: mov             x1, x6
    // 0xb33474: cmp             x1, x0
    // 0xb33478: b.hs            #0xb33a48
    // 0xb3347c: LoadField: r0 = r5->field_f
    //     0xb3347c: ldur            w0, [x5, #0xf]
    // 0xb33480: DecompressPointer r0
    //     0xb33480: add             x0, x0, HEAP, lsl #32
    // 0xb33484: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xb33484: add             x16, x0, x6, lsl #2
    //     0xb33488: ldur            w1, [x16, #0xf]
    // 0xb3348c: DecompressPointer r1
    //     0xb3348c: add             x1, x1, HEAP, lsl #32
    // 0xb33490: LoadField: r0 = r1->field_f
    //     0xb33490: ldur            w0, [x1, #0xf]
    // 0xb33494: DecompressPointer r0
    //     0xb33494: add             x0, x0, HEAP, lsl #32
    // 0xb33498: r16 = Instance_CustomisationType
    //     0xb33498: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xb3349c: ldr             x16, [x16, #0x660]
    // 0xb334a0: cmp             w0, w16
    // 0xb334a4: b.ne            #0xb335b4
    // 0xb334a8: LoadField: r0 = r2->field_b
    //     0xb334a8: ldur            w0, [x2, #0xb]
    // 0xb334ac: DecompressPointer r0
    //     0xb334ac: add             x0, x0, HEAP, lsl #32
    // 0xb334b0: cmp             w0, NULL
    // 0xb334b4: b.ne            #0xb334c4
    // 0xb334b8: mov             x1, x4
    // 0xb334bc: r2 = Null
    //     0xb334bc: mov             x2, NULL
    // 0xb334c0: b               #0xb334e8
    // 0xb334c4: r1 = LoadClassIdInstr(r0)
    //     0xb334c4: ldur            x1, [x0, #-1]
    //     0xb334c8: ubfx            x1, x1, #0xc, #0x14
    // 0xb334cc: stp             x3, x0, [SP]
    // 0xb334d0: mov             x0, x1
    // 0xb334d4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb334d4: sub             lr, x0, #0xb7
    //     0xb334d8: ldr             lr, [x21, lr, lsl #3]
    //     0xb334dc: blr             lr
    // 0xb334e0: mov             x2, x0
    // 0xb334e4: ldur            x1, [fp, #-8]
    // 0xb334e8: stur            x2, [fp, #-0x20]
    // 0xb334ec: LoadField: r0 = r1->field_f
    //     0xb334ec: ldur            w0, [x1, #0xf]
    // 0xb334f0: DecompressPointer r0
    //     0xb334f0: add             x0, x0, HEAP, lsl #32
    // 0xb334f4: LoadField: r1 = r0->field_b
    //     0xb334f4: ldur            w1, [x0, #0xb]
    // 0xb334f8: DecompressPointer r1
    //     0xb334f8: add             x1, x1, HEAP, lsl #32
    // 0xb334fc: cmp             w1, NULL
    // 0xb33500: b.eq            #0xb33a4c
    // 0xb33504: LoadField: r0 = r1->field_f
    //     0xb33504: ldur            w0, [x1, #0xf]
    // 0xb33508: DecompressPointer r0
    //     0xb33508: add             x0, x0, HEAP, lsl #32
    // 0xb3350c: cmp             w0, NULL
    // 0xb33510: b.ne            #0xb3351c
    // 0xb33514: r0 = Null
    //     0xb33514: mov             x0, NULL
    // 0xb33518: b               #0xb33574
    // 0xb3351c: LoadField: r3 = r0->field_13
    //     0xb3351c: ldur            w3, [x0, #0x13]
    // 0xb33520: DecompressPointer r3
    //     0xb33520: add             x3, x3, HEAP, lsl #32
    // 0xb33524: cmp             w3, NULL
    // 0xb33528: b.ne            #0xb33534
    // 0xb3352c: r0 = Null
    //     0xb3352c: mov             x0, NULL
    // 0xb33530: b               #0xb33574
    // 0xb33534: ldr             x4, [fp, #0x10]
    // 0xb33538: LoadField: r0 = r3->field_b
    //     0xb33538: ldur            w0, [x3, #0xb]
    // 0xb3353c: r5 = LoadInt32Instr(r4)
    //     0xb3353c: sbfx            x5, x4, #1, #0x1f
    //     0xb33540: tbz             w4, #0, #0xb33548
    //     0xb33544: ldur            x5, [x4, #7]
    // 0xb33548: r1 = LoadInt32Instr(r0)
    //     0xb33548: sbfx            x1, x0, #1, #0x1f
    // 0xb3354c: mov             x0, x1
    // 0xb33550: mov             x1, x5
    // 0xb33554: cmp             x1, x0
    // 0xb33558: b.hs            #0xb33a50
    // 0xb3355c: LoadField: r0 = r3->field_f
    //     0xb3355c: ldur            w0, [x3, #0xf]
    // 0xb33560: DecompressPointer r0
    //     0xb33560: add             x0, x0, HEAP, lsl #32
    // 0xb33564: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb33564: add             x16, x0, x5, lsl #2
    //     0xb33568: ldur            w1, [x16, #0xf]
    // 0xb3356c: DecompressPointer r1
    //     0xb3356c: add             x1, x1, HEAP, lsl #32
    // 0xb33570: mov             x0, x1
    // 0xb33574: stur            x0, [fp, #-0x18]
    // 0xb33578: r0 = BagMultiSelect()
    //     0xb33578: bl              #0xb33a98  ; AllocateBagMultiSelectStub -> BagMultiSelect (size=0x14)
    // 0xb3357c: mov             x1, x0
    // 0xb33580: ldur            x0, [fp, #-0x20]
    // 0xb33584: stur            x1, [fp, #-0x28]
    // 0xb33588: StoreField: r1->field_b = r0
    //     0xb33588: stur            w0, [x1, #0xb]
    // 0xb3358c: ldur            x0, [fp, #-0x18]
    // 0xb33590: StoreField: r1->field_f = r0
    //     0xb33590: stur            w0, [x1, #0xf]
    // 0xb33594: r0 = Padding()
    //     0xb33594: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb33598: r3 = Instance_EdgeInsets
    //     0xb33598: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb3359c: ldr             x3, [x3, #0x668]
    // 0xb335a0: StoreField: r0->field_f = r3
    //     0xb335a0: stur            w3, [x0, #0xf]
    // 0xb335a4: ldur            x1, [fp, #-0x28]
    // 0xb335a8: StoreField: r0->field_b = r1
    //     0xb335a8: stur            w1, [x0, #0xb]
    // 0xb335ac: mov             x1, x0
    // 0xb335b0: b               #0xb33a28
    // 0xb335b4: mov             x1, x4
    // 0xb335b8: mov             x4, x3
    // 0xb335bc: r3 = Instance_EdgeInsets
    //     0xb335bc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb335c0: ldr             x3, [x3, #0x668]
    // 0xb335c4: r16 = Instance_CustomisationType
    //     0xb335c4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xb335c8: ldr             x16, [x16, #0x670]
    // 0xb335cc: cmp             w0, w16
    // 0xb335d0: b.ne            #0xb336dc
    // 0xb335d4: LoadField: r0 = r2->field_b
    //     0xb335d4: ldur            w0, [x2, #0xb]
    // 0xb335d8: DecompressPointer r0
    //     0xb335d8: add             x0, x0, HEAP, lsl #32
    // 0xb335dc: cmp             w0, NULL
    // 0xb335e0: b.ne            #0xb335ec
    // 0xb335e4: r2 = Null
    //     0xb335e4: mov             x2, NULL
    // 0xb335e8: b               #0xb33610
    // 0xb335ec: r2 = LoadClassIdInstr(r0)
    //     0xb335ec: ldur            x2, [x0, #-1]
    //     0xb335f0: ubfx            x2, x2, #0xc, #0x14
    // 0xb335f4: stp             x4, x0, [SP]
    // 0xb335f8: mov             x0, x2
    // 0xb335fc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb335fc: sub             lr, x0, #0xb7
    //     0xb33600: ldr             lr, [x21, lr, lsl #3]
    //     0xb33604: blr             lr
    // 0xb33608: mov             x2, x0
    // 0xb3360c: ldur            x1, [fp, #-8]
    // 0xb33610: stur            x2, [fp, #-0x20]
    // 0xb33614: LoadField: r0 = r1->field_f
    //     0xb33614: ldur            w0, [x1, #0xf]
    // 0xb33618: DecompressPointer r0
    //     0xb33618: add             x0, x0, HEAP, lsl #32
    // 0xb3361c: LoadField: r1 = r0->field_b
    //     0xb3361c: ldur            w1, [x0, #0xb]
    // 0xb33620: DecompressPointer r1
    //     0xb33620: add             x1, x1, HEAP, lsl #32
    // 0xb33624: cmp             w1, NULL
    // 0xb33628: b.eq            #0xb33a54
    // 0xb3362c: LoadField: r0 = r1->field_f
    //     0xb3362c: ldur            w0, [x1, #0xf]
    // 0xb33630: DecompressPointer r0
    //     0xb33630: add             x0, x0, HEAP, lsl #32
    // 0xb33634: cmp             w0, NULL
    // 0xb33638: b.ne            #0xb33644
    // 0xb3363c: r0 = Null
    //     0xb3363c: mov             x0, NULL
    // 0xb33640: b               #0xb3369c
    // 0xb33644: LoadField: r3 = r0->field_13
    //     0xb33644: ldur            w3, [x0, #0x13]
    // 0xb33648: DecompressPointer r3
    //     0xb33648: add             x3, x3, HEAP, lsl #32
    // 0xb3364c: cmp             w3, NULL
    // 0xb33650: b.ne            #0xb3365c
    // 0xb33654: r0 = Null
    //     0xb33654: mov             x0, NULL
    // 0xb33658: b               #0xb3369c
    // 0xb3365c: ldr             x4, [fp, #0x10]
    // 0xb33660: LoadField: r0 = r3->field_b
    //     0xb33660: ldur            w0, [x3, #0xb]
    // 0xb33664: r5 = LoadInt32Instr(r4)
    //     0xb33664: sbfx            x5, x4, #1, #0x1f
    //     0xb33668: tbz             w4, #0, #0xb33670
    //     0xb3366c: ldur            x5, [x4, #7]
    // 0xb33670: r1 = LoadInt32Instr(r0)
    //     0xb33670: sbfx            x1, x0, #1, #0x1f
    // 0xb33674: mov             x0, x1
    // 0xb33678: mov             x1, x5
    // 0xb3367c: cmp             x1, x0
    // 0xb33680: b.hs            #0xb33a58
    // 0xb33684: LoadField: r0 = r3->field_f
    //     0xb33684: ldur            w0, [x3, #0xf]
    // 0xb33688: DecompressPointer r0
    //     0xb33688: add             x0, x0, HEAP, lsl #32
    // 0xb3368c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb3368c: add             x16, x0, x5, lsl #2
    //     0xb33690: ldur            w1, [x16, #0xf]
    // 0xb33694: DecompressPointer r1
    //     0xb33694: add             x1, x1, HEAP, lsl #32
    // 0xb33698: mov             x0, x1
    // 0xb3369c: stur            x0, [fp, #-0x18]
    // 0xb336a0: r0 = BagSingleSelect()
    //     0xb336a0: bl              #0xb33a8c  ; AllocateBagSingleSelectStub -> BagSingleSelect (size=0x14)
    // 0xb336a4: mov             x1, x0
    // 0xb336a8: ldur            x0, [fp, #-0x20]
    // 0xb336ac: stur            x1, [fp, #-0x28]
    // 0xb336b0: StoreField: r1->field_b = r0
    //     0xb336b0: stur            w0, [x1, #0xb]
    // 0xb336b4: ldur            x0, [fp, #-0x18]
    // 0xb336b8: StoreField: r1->field_f = r0
    //     0xb336b8: stur            w0, [x1, #0xf]
    // 0xb336bc: r0 = Padding()
    //     0xb336bc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb336c0: r3 = Instance_EdgeInsets
    //     0xb336c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb336c4: ldr             x3, [x3, #0x668]
    // 0xb336c8: StoreField: r0->field_f = r3
    //     0xb336c8: stur            w3, [x0, #0xf]
    // 0xb336cc: ldur            x1, [fp, #-0x28]
    // 0xb336d0: StoreField: r0->field_b = r1
    //     0xb336d0: stur            w1, [x0, #0xb]
    // 0xb336d4: mov             x1, x0
    // 0xb336d8: b               #0xb33a28
    // 0xb336dc: r16 = Instance_CustomisationType
    //     0xb336dc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xb336e0: ldr             x16, [x16, #0x650]
    // 0xb336e4: cmp             w0, w16
    // 0xb336e8: b.ne            #0xb337f4
    // 0xb336ec: LoadField: r0 = r2->field_b
    //     0xb336ec: ldur            w0, [x2, #0xb]
    // 0xb336f0: DecompressPointer r0
    //     0xb336f0: add             x0, x0, HEAP, lsl #32
    // 0xb336f4: cmp             w0, NULL
    // 0xb336f8: b.ne            #0xb33704
    // 0xb336fc: r2 = Null
    //     0xb336fc: mov             x2, NULL
    // 0xb33700: b               #0xb33728
    // 0xb33704: r2 = LoadClassIdInstr(r0)
    //     0xb33704: ldur            x2, [x0, #-1]
    //     0xb33708: ubfx            x2, x2, #0xc, #0x14
    // 0xb3370c: stp             x4, x0, [SP]
    // 0xb33710: mov             x0, x2
    // 0xb33714: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb33714: sub             lr, x0, #0xb7
    //     0xb33718: ldr             lr, [x21, lr, lsl #3]
    //     0xb3371c: blr             lr
    // 0xb33720: mov             x2, x0
    // 0xb33724: ldur            x1, [fp, #-8]
    // 0xb33728: stur            x2, [fp, #-0x20]
    // 0xb3372c: LoadField: r0 = r1->field_f
    //     0xb3372c: ldur            w0, [x1, #0xf]
    // 0xb33730: DecompressPointer r0
    //     0xb33730: add             x0, x0, HEAP, lsl #32
    // 0xb33734: LoadField: r1 = r0->field_b
    //     0xb33734: ldur            w1, [x0, #0xb]
    // 0xb33738: DecompressPointer r1
    //     0xb33738: add             x1, x1, HEAP, lsl #32
    // 0xb3373c: cmp             w1, NULL
    // 0xb33740: b.eq            #0xb33a5c
    // 0xb33744: LoadField: r0 = r1->field_f
    //     0xb33744: ldur            w0, [x1, #0xf]
    // 0xb33748: DecompressPointer r0
    //     0xb33748: add             x0, x0, HEAP, lsl #32
    // 0xb3374c: cmp             w0, NULL
    // 0xb33750: b.ne            #0xb3375c
    // 0xb33754: r0 = Null
    //     0xb33754: mov             x0, NULL
    // 0xb33758: b               #0xb337b4
    // 0xb3375c: LoadField: r3 = r0->field_13
    //     0xb3375c: ldur            w3, [x0, #0x13]
    // 0xb33760: DecompressPointer r3
    //     0xb33760: add             x3, x3, HEAP, lsl #32
    // 0xb33764: cmp             w3, NULL
    // 0xb33768: b.ne            #0xb33774
    // 0xb3376c: r0 = Null
    //     0xb3376c: mov             x0, NULL
    // 0xb33770: b               #0xb337b4
    // 0xb33774: ldr             x4, [fp, #0x10]
    // 0xb33778: LoadField: r0 = r3->field_b
    //     0xb33778: ldur            w0, [x3, #0xb]
    // 0xb3377c: r5 = LoadInt32Instr(r4)
    //     0xb3377c: sbfx            x5, x4, #1, #0x1f
    //     0xb33780: tbz             w4, #0, #0xb33788
    //     0xb33784: ldur            x5, [x4, #7]
    // 0xb33788: r1 = LoadInt32Instr(r0)
    //     0xb33788: sbfx            x1, x0, #1, #0x1f
    // 0xb3378c: mov             x0, x1
    // 0xb33790: mov             x1, x5
    // 0xb33794: cmp             x1, x0
    // 0xb33798: b.hs            #0xb33a60
    // 0xb3379c: LoadField: r0 = r3->field_f
    //     0xb3379c: ldur            w0, [x3, #0xf]
    // 0xb337a0: DecompressPointer r0
    //     0xb337a0: add             x0, x0, HEAP, lsl #32
    // 0xb337a4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb337a4: add             x16, x0, x5, lsl #2
    //     0xb337a8: ldur            w1, [x16, #0xf]
    // 0xb337ac: DecompressPointer r1
    //     0xb337ac: add             x1, x1, HEAP, lsl #32
    // 0xb337b0: mov             x0, x1
    // 0xb337b4: stur            x0, [fp, #-0x18]
    // 0xb337b8: r0 = BagImages()
    //     0xb337b8: bl              #0xb33a80  ; AllocateBagImagesStub -> BagImages (size=0x14)
    // 0xb337bc: mov             x1, x0
    // 0xb337c0: ldur            x0, [fp, #-0x20]
    // 0xb337c4: stur            x1, [fp, #-0x28]
    // 0xb337c8: StoreField: r1->field_b = r0
    //     0xb337c8: stur            w0, [x1, #0xb]
    // 0xb337cc: ldur            x0, [fp, #-0x18]
    // 0xb337d0: StoreField: r1->field_f = r0
    //     0xb337d0: stur            w0, [x1, #0xf]
    // 0xb337d4: r0 = Padding()
    //     0xb337d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb337d8: r3 = Instance_EdgeInsets
    //     0xb337d8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb337dc: ldr             x3, [x3, #0x668]
    // 0xb337e0: StoreField: r0->field_f = r3
    //     0xb337e0: stur            w3, [x0, #0xf]
    // 0xb337e4: ldur            x1, [fp, #-0x28]
    // 0xb337e8: StoreField: r0->field_b = r1
    //     0xb337e8: stur            w1, [x0, #0xb]
    // 0xb337ec: mov             x1, x0
    // 0xb337f0: b               #0xb33a28
    // 0xb337f4: r16 = Instance_CustomisationType
    //     0xb337f4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xb337f8: ldr             x16, [x16, #0x680]
    // 0xb337fc: cmp             w0, w16
    // 0xb33800: b.ne            #0xb3390c
    // 0xb33804: LoadField: r0 = r2->field_b
    //     0xb33804: ldur            w0, [x2, #0xb]
    // 0xb33808: DecompressPointer r0
    //     0xb33808: add             x0, x0, HEAP, lsl #32
    // 0xb3380c: cmp             w0, NULL
    // 0xb33810: b.ne            #0xb3381c
    // 0xb33814: r2 = Null
    //     0xb33814: mov             x2, NULL
    // 0xb33818: b               #0xb33840
    // 0xb3381c: r2 = LoadClassIdInstr(r0)
    //     0xb3381c: ldur            x2, [x0, #-1]
    //     0xb33820: ubfx            x2, x2, #0xc, #0x14
    // 0xb33824: stp             x4, x0, [SP]
    // 0xb33828: mov             x0, x2
    // 0xb3382c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb3382c: sub             lr, x0, #0xb7
    //     0xb33830: ldr             lr, [x21, lr, lsl #3]
    //     0xb33834: blr             lr
    // 0xb33838: mov             x2, x0
    // 0xb3383c: ldur            x1, [fp, #-8]
    // 0xb33840: stur            x2, [fp, #-0x20]
    // 0xb33844: LoadField: r0 = r1->field_f
    //     0xb33844: ldur            w0, [x1, #0xf]
    // 0xb33848: DecompressPointer r0
    //     0xb33848: add             x0, x0, HEAP, lsl #32
    // 0xb3384c: LoadField: r1 = r0->field_b
    //     0xb3384c: ldur            w1, [x0, #0xb]
    // 0xb33850: DecompressPointer r1
    //     0xb33850: add             x1, x1, HEAP, lsl #32
    // 0xb33854: cmp             w1, NULL
    // 0xb33858: b.eq            #0xb33a64
    // 0xb3385c: LoadField: r0 = r1->field_f
    //     0xb3385c: ldur            w0, [x1, #0xf]
    // 0xb33860: DecompressPointer r0
    //     0xb33860: add             x0, x0, HEAP, lsl #32
    // 0xb33864: cmp             w0, NULL
    // 0xb33868: b.ne            #0xb33874
    // 0xb3386c: r0 = Null
    //     0xb3386c: mov             x0, NULL
    // 0xb33870: b               #0xb338cc
    // 0xb33874: LoadField: r3 = r0->field_13
    //     0xb33874: ldur            w3, [x0, #0x13]
    // 0xb33878: DecompressPointer r3
    //     0xb33878: add             x3, x3, HEAP, lsl #32
    // 0xb3387c: cmp             w3, NULL
    // 0xb33880: b.ne            #0xb3388c
    // 0xb33884: r0 = Null
    //     0xb33884: mov             x0, NULL
    // 0xb33888: b               #0xb338cc
    // 0xb3388c: ldr             x4, [fp, #0x10]
    // 0xb33890: LoadField: r0 = r3->field_b
    //     0xb33890: ldur            w0, [x3, #0xb]
    // 0xb33894: r5 = LoadInt32Instr(r4)
    //     0xb33894: sbfx            x5, x4, #1, #0x1f
    //     0xb33898: tbz             w4, #0, #0xb338a0
    //     0xb3389c: ldur            x5, [x4, #7]
    // 0xb338a0: r1 = LoadInt32Instr(r0)
    //     0xb338a0: sbfx            x1, x0, #1, #0x1f
    // 0xb338a4: mov             x0, x1
    // 0xb338a8: mov             x1, x5
    // 0xb338ac: cmp             x1, x0
    // 0xb338b0: b.hs            #0xb33a68
    // 0xb338b4: LoadField: r0 = r3->field_f
    //     0xb338b4: ldur            w0, [x3, #0xf]
    // 0xb338b8: DecompressPointer r0
    //     0xb338b8: add             x0, x0, HEAP, lsl #32
    // 0xb338bc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb338bc: add             x16, x0, x5, lsl #2
    //     0xb338c0: ldur            w1, [x16, #0xf]
    // 0xb338c4: DecompressPointer r1
    //     0xb338c4: add             x1, x1, HEAP, lsl #32
    // 0xb338c8: mov             x0, x1
    // 0xb338cc: stur            x0, [fp, #-0x18]
    // 0xb338d0: r0 = BagText()
    //     0xb338d0: bl              #0xb33a74  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xb338d4: mov             x1, x0
    // 0xb338d8: ldur            x0, [fp, #-0x20]
    // 0xb338dc: stur            x1, [fp, #-0x28]
    // 0xb338e0: StoreField: r1->field_b = r0
    //     0xb338e0: stur            w0, [x1, #0xb]
    // 0xb338e4: ldur            x0, [fp, #-0x18]
    // 0xb338e8: StoreField: r1->field_f = r0
    //     0xb338e8: stur            w0, [x1, #0xf]
    // 0xb338ec: r0 = Padding()
    //     0xb338ec: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb338f0: r3 = Instance_EdgeInsets
    //     0xb338f0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb338f4: ldr             x3, [x3, #0x668]
    // 0xb338f8: StoreField: r0->field_f = r3
    //     0xb338f8: stur            w3, [x0, #0xf]
    // 0xb338fc: ldur            x1, [fp, #-0x28]
    // 0xb33900: StoreField: r0->field_b = r1
    //     0xb33900: stur            w1, [x0, #0xb]
    // 0xb33904: mov             x1, x0
    // 0xb33908: b               #0xb33a28
    // 0xb3390c: r16 = Instance_CustomisationType
    //     0xb3390c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xb33910: ldr             x16, [x16, #0x690]
    // 0xb33914: cmp             w0, w16
    // 0xb33918: b.ne            #0xb33a24
    // 0xb3391c: LoadField: r0 = r2->field_b
    //     0xb3391c: ldur            w0, [x2, #0xb]
    // 0xb33920: DecompressPointer r0
    //     0xb33920: add             x0, x0, HEAP, lsl #32
    // 0xb33924: cmp             w0, NULL
    // 0xb33928: b.ne            #0xb33938
    // 0xb3392c: mov             x0, x1
    // 0xb33930: r2 = Null
    //     0xb33930: mov             x2, NULL
    // 0xb33934: b               #0xb3395c
    // 0xb33938: r2 = LoadClassIdInstr(r0)
    //     0xb33938: ldur            x2, [x0, #-1]
    //     0xb3393c: ubfx            x2, x2, #0xc, #0x14
    // 0xb33940: stp             x4, x0, [SP]
    // 0xb33944: mov             x0, x2
    // 0xb33948: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb33948: sub             lr, x0, #0xb7
    //     0xb3394c: ldr             lr, [x21, lr, lsl #3]
    //     0xb33950: blr             lr
    // 0xb33954: mov             x2, x0
    // 0xb33958: ldur            x0, [fp, #-8]
    // 0xb3395c: stur            x2, [fp, #-0x18]
    // 0xb33960: LoadField: r1 = r0->field_f
    //     0xb33960: ldur            w1, [x0, #0xf]
    // 0xb33964: DecompressPointer r1
    //     0xb33964: add             x1, x1, HEAP, lsl #32
    // 0xb33968: LoadField: r0 = r1->field_b
    //     0xb33968: ldur            w0, [x1, #0xb]
    // 0xb3396c: DecompressPointer r0
    //     0xb3396c: add             x0, x0, HEAP, lsl #32
    // 0xb33970: cmp             w0, NULL
    // 0xb33974: b.eq            #0xb33a6c
    // 0xb33978: LoadField: r1 = r0->field_f
    //     0xb33978: ldur            w1, [x0, #0xf]
    // 0xb3397c: DecompressPointer r1
    //     0xb3397c: add             x1, x1, HEAP, lsl #32
    // 0xb33980: cmp             w1, NULL
    // 0xb33984: b.ne            #0xb33990
    // 0xb33988: r0 = Null
    //     0xb33988: mov             x0, NULL
    // 0xb3398c: b               #0xb339e4
    // 0xb33990: LoadField: r3 = r1->field_13
    //     0xb33990: ldur            w3, [x1, #0x13]
    // 0xb33994: DecompressPointer r3
    //     0xb33994: add             x3, x3, HEAP, lsl #32
    // 0xb33998: cmp             w3, NULL
    // 0xb3399c: b.ne            #0xb339a8
    // 0xb339a0: r0 = Null
    //     0xb339a0: mov             x0, NULL
    // 0xb339a4: b               #0xb339e4
    // 0xb339a8: ldr             x0, [fp, #0x10]
    // 0xb339ac: LoadField: r1 = r3->field_b
    //     0xb339ac: ldur            w1, [x3, #0xb]
    // 0xb339b0: r4 = LoadInt32Instr(r0)
    //     0xb339b0: sbfx            x4, x0, #1, #0x1f
    //     0xb339b4: tbz             w0, #0, #0xb339bc
    //     0xb339b8: ldur            x4, [x0, #7]
    // 0xb339bc: r0 = LoadInt32Instr(r1)
    //     0xb339bc: sbfx            x0, x1, #1, #0x1f
    // 0xb339c0: mov             x1, x4
    // 0xb339c4: cmp             x1, x0
    // 0xb339c8: b.hs            #0xb33a70
    // 0xb339cc: LoadField: r0 = r3->field_f
    //     0xb339cc: ldur            w0, [x3, #0xf]
    // 0xb339d0: DecompressPointer r0
    //     0xb339d0: add             x0, x0, HEAP, lsl #32
    // 0xb339d4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb339d4: add             x16, x0, x4, lsl #2
    //     0xb339d8: ldur            w1, [x16, #0xf]
    // 0xb339dc: DecompressPointer r1
    //     0xb339dc: add             x1, x1, HEAP, lsl #32
    // 0xb339e0: mov             x0, x1
    // 0xb339e4: stur            x0, [fp, #-8]
    // 0xb339e8: r0 = BagText()
    //     0xb339e8: bl              #0xb33a74  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xb339ec: mov             x1, x0
    // 0xb339f0: ldur            x0, [fp, #-0x18]
    // 0xb339f4: stur            x1, [fp, #-0x20]
    // 0xb339f8: StoreField: r1->field_b = r0
    //     0xb339f8: stur            w0, [x1, #0xb]
    // 0xb339fc: ldur            x0, [fp, #-8]
    // 0xb33a00: StoreField: r1->field_f = r0
    //     0xb33a00: stur            w0, [x1, #0xf]
    // 0xb33a04: r0 = Padding()
    //     0xb33a04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb33a08: r1 = Instance_EdgeInsets
    //     0xb33a08: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb33a0c: ldr             x1, [x1, #0x668]
    // 0xb33a10: StoreField: r0->field_f = r1
    //     0xb33a10: stur            w1, [x0, #0xf]
    // 0xb33a14: ldur            x1, [fp, #-0x20]
    // 0xb33a18: StoreField: r0->field_b = r1
    //     0xb33a18: stur            w1, [x0, #0xb]
    // 0xb33a1c: mov             x1, x0
    // 0xb33a20: b               #0xb33a28
    // 0xb33a24: ldur            x1, [fp, #-0x10]
    // 0xb33a28: mov             x0, x1
    // 0xb33a2c: LeaveFrame
    //     0xb33a2c: mov             SP, fp
    //     0xb33a30: ldp             fp, lr, [SP], #0x10
    // 0xb33a34: ret
    //     0xb33a34: ret             
    // 0xb33a38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb33a38: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb33a3c: b               #0xb33130
    // 0xb33a40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33a40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33a44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33a44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33a48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb33a48: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb33a4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33a4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33a50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb33a50: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb33a54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33a54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33a58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb33a58: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb33a5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33a5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33a60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb33a60: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb33a64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33a64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33a68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb33a68: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb33a6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb33a6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb33a70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb33a70: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb33aa4, size: 0x9ec
    // 0xb33aa4: EnterFrame
    //     0xb33aa4: stp             fp, lr, [SP, #-0x10]!
    //     0xb33aa8: mov             fp, SP
    // 0xb33aac: AllocStack(0x40)
    //     0xb33aac: sub             SP, SP, #0x40
    // 0xb33ab0: SetupParameters()
    //     0xb33ab0: ldr             x0, [fp, #0x20]
    //     0xb33ab4: ldur            w1, [x0, #0x17]
    //     0xb33ab8: add             x1, x1, HEAP, lsl #32
    //     0xb33abc: stur            x1, [fp, #-8]
    // 0xb33ac0: CheckStackOverflow
    //     0xb33ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb33ac4: cmp             SP, x16
    //     0xb33ac8: b.ls            #0xb34440
    // 0xb33acc: r0 = Container()
    //     0xb33acc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb33ad0: mov             x1, x0
    // 0xb33ad4: stur            x0, [fp, #-0x10]
    // 0xb33ad8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb33ad8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb33adc: r0 = Container()
    //     0xb33adc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb33ae0: ldur            x1, [fp, #-8]
    // 0xb33ae4: LoadField: r0 = r1->field_f
    //     0xb33ae4: ldur            w0, [x1, #0xf]
    // 0xb33ae8: DecompressPointer r0
    //     0xb33ae8: add             x0, x0, HEAP, lsl #32
    // 0xb33aec: LoadField: r2 = r0->field_b
    //     0xb33aec: ldur            w2, [x0, #0xb]
    // 0xb33af0: DecompressPointer r2
    //     0xb33af0: add             x2, x2, HEAP, lsl #32
    // 0xb33af4: cmp             w2, NULL
    // 0xb33af8: b.eq            #0xb34448
    // 0xb33afc: LoadField: r0 = r2->field_b
    //     0xb33afc: ldur            w0, [x2, #0xb]
    // 0xb33b00: DecompressPointer r0
    //     0xb33b00: add             x0, x0, HEAP, lsl #32
    // 0xb33b04: cmp             w0, NULL
    // 0xb33b08: b.ne            #0xb33b14
    // 0xb33b0c: r0 = Null
    //     0xb33b0c: mov             x0, NULL
    // 0xb33b10: b               #0xb33b34
    // 0xb33b14: r2 = LoadClassIdInstr(r0)
    //     0xb33b14: ldur            x2, [x0, #-1]
    //     0xb33b18: ubfx            x2, x2, #0xc, #0x14
    // 0xb33b1c: str             x0, [SP]
    // 0xb33b20: mov             x0, x2
    // 0xb33b24: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb33b24: movz            x17, #0xc898
    //     0xb33b28: add             lr, x0, x17
    //     0xb33b2c: ldr             lr, [x21, lr, lsl #3]
    //     0xb33b30: blr             lr
    // 0xb33b34: ldr             x1, [fp, #0x10]
    // 0xb33b38: cmp             w0, w1
    // 0xb33b3c: b.ne            #0xb33dc8
    // 0xb33b40: ldur            x0, [fp, #-8]
    // 0xb33b44: ldr             x1, [fp, #0x18]
    // 0xb33b48: r0 = of()
    //     0xb33b48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb33b4c: LoadField: r1 = r0->field_87
    //     0xb33b4c: ldur            w1, [x0, #0x87]
    // 0xb33b50: DecompressPointer r1
    //     0xb33b50: add             x1, x1, HEAP, lsl #32
    // 0xb33b54: LoadField: r0 = r1->field_7
    //     0xb33b54: ldur            w0, [x1, #7]
    // 0xb33b58: DecompressPointer r0
    //     0xb33b58: add             x0, x0, HEAP, lsl #32
    // 0xb33b5c: r16 = Instance_Color
    //     0xb33b5c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb33b60: r30 = 14.000000
    //     0xb33b60: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb33b64: ldr             lr, [lr, #0x1d8]
    // 0xb33b68: stp             lr, x16, [SP]
    // 0xb33b6c: mov             x1, x0
    // 0xb33b70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb33b70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb33b74: ldr             x4, [x4, #0x9b8]
    // 0xb33b78: r0 = copyWith()
    //     0xb33b78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb33b7c: stur            x0, [fp, #-0x18]
    // 0xb33b80: r0 = Text()
    //     0xb33b80: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb33b84: mov             x1, x0
    // 0xb33b88: r0 = "Total"
    //     0xb33b88: add             x0, PP, #0x54, lsl #12  ; [pp+0x54bc8] "Total"
    //     0xb33b8c: ldr             x0, [x0, #0xbc8]
    // 0xb33b90: stur            x1, [fp, #-0x20]
    // 0xb33b94: StoreField: r1->field_b = r0
    //     0xb33b94: stur            w0, [x1, #0xb]
    // 0xb33b98: ldur            x0, [fp, #-0x18]
    // 0xb33b9c: StoreField: r1->field_13 = r0
    //     0xb33b9c: stur            w0, [x1, #0x13]
    // 0xb33ba0: ldur            x2, [fp, #-8]
    // 0xb33ba4: LoadField: r0 = r2->field_f
    //     0xb33ba4: ldur            w0, [x2, #0xf]
    // 0xb33ba8: DecompressPointer r0
    //     0xb33ba8: add             x0, x0, HEAP, lsl #32
    // 0xb33bac: LoadField: r2 = r0->field_b
    //     0xb33bac: ldur            w2, [x0, #0xb]
    // 0xb33bb0: DecompressPointer r2
    //     0xb33bb0: add             x2, x2, HEAP, lsl #32
    // 0xb33bb4: cmp             w2, NULL
    // 0xb33bb8: b.eq            #0xb3444c
    // 0xb33bbc: LoadField: r0 = r2->field_13
    //     0xb33bbc: ldur            w0, [x2, #0x13]
    // 0xb33bc0: DecompressPointer r0
    //     0xb33bc0: add             x0, x0, HEAP, lsl #32
    // 0xb33bc4: r2 = LoadClassIdInstr(r0)
    //     0xb33bc4: ldur            x2, [x0, #-1]
    //     0xb33bc8: ubfx            x2, x2, #0xc, #0x14
    // 0xb33bcc: str             x0, [SP]
    // 0xb33bd0: mov             x0, x2
    // 0xb33bd4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb33bd4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb33bd8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb33bd8: movz            x17, #0x2700
    //     0xb33bdc: add             lr, x0, x17
    //     0xb33be0: ldr             lr, [x21, lr, lsl #3]
    //     0xb33be4: blr             lr
    // 0xb33be8: ldr             x1, [fp, #0x18]
    // 0xb33bec: stur            x0, [fp, #-0x18]
    // 0xb33bf0: r0 = of()
    //     0xb33bf0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb33bf4: LoadField: r1 = r0->field_87
    //     0xb33bf4: ldur            w1, [x0, #0x87]
    // 0xb33bf8: DecompressPointer r1
    //     0xb33bf8: add             x1, x1, HEAP, lsl #32
    // 0xb33bfc: LoadField: r0 = r1->field_7
    //     0xb33bfc: ldur            w0, [x1, #7]
    // 0xb33c00: DecompressPointer r0
    //     0xb33c00: add             x0, x0, HEAP, lsl #32
    // 0xb33c04: r16 = Instance_Color
    //     0xb33c04: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb33c08: r30 = 14.000000
    //     0xb33c08: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb33c0c: ldr             lr, [lr, #0x1d8]
    // 0xb33c10: stp             lr, x16, [SP]
    // 0xb33c14: mov             x1, x0
    // 0xb33c18: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb33c18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb33c1c: ldr             x4, [x4, #0x9b8]
    // 0xb33c20: r0 = copyWith()
    //     0xb33c20: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb33c24: stur            x0, [fp, #-0x28]
    // 0xb33c28: r0 = Text()
    //     0xb33c28: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb33c2c: mov             x3, x0
    // 0xb33c30: ldur            x0, [fp, #-0x18]
    // 0xb33c34: stur            x3, [fp, #-0x30]
    // 0xb33c38: StoreField: r3->field_b = r0
    //     0xb33c38: stur            w0, [x3, #0xb]
    // 0xb33c3c: ldur            x0, [fp, #-0x28]
    // 0xb33c40: StoreField: r3->field_13 = r0
    //     0xb33c40: stur            w0, [x3, #0x13]
    // 0xb33c44: r1 = Null
    //     0xb33c44: mov             x1, NULL
    // 0xb33c48: r2 = 6
    //     0xb33c48: movz            x2, #0x6
    // 0xb33c4c: r0 = AllocateArray()
    //     0xb33c4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb33c50: mov             x2, x0
    // 0xb33c54: ldur            x0, [fp, #-0x20]
    // 0xb33c58: stur            x2, [fp, #-0x18]
    // 0xb33c5c: StoreField: r2->field_f = r0
    //     0xb33c5c: stur            w0, [x2, #0xf]
    // 0xb33c60: r16 = Instance_Spacer
    //     0xb33c60: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xb33c64: ldr             x16, [x16, #0xf0]
    // 0xb33c68: StoreField: r2->field_13 = r16
    //     0xb33c68: stur            w16, [x2, #0x13]
    // 0xb33c6c: ldur            x0, [fp, #-0x30]
    // 0xb33c70: ArrayStore: r2[0] = r0  ; List_4
    //     0xb33c70: stur            w0, [x2, #0x17]
    // 0xb33c74: r1 = <Widget>
    //     0xb33c74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb33c78: r0 = AllocateGrowableArray()
    //     0xb33c78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb33c7c: mov             x1, x0
    // 0xb33c80: ldur            x0, [fp, #-0x18]
    // 0xb33c84: stur            x1, [fp, #-0x20]
    // 0xb33c88: StoreField: r1->field_f = r0
    //     0xb33c88: stur            w0, [x1, #0xf]
    // 0xb33c8c: r0 = 6
    //     0xb33c8c: movz            x0, #0x6
    // 0xb33c90: StoreField: r1->field_b = r0
    //     0xb33c90: stur            w0, [x1, #0xb]
    // 0xb33c94: r0 = Row()
    //     0xb33c94: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb33c98: mov             x3, x0
    // 0xb33c9c: r0 = Instance_Axis
    //     0xb33c9c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb33ca0: stur            x3, [fp, #-0x18]
    // 0xb33ca4: StoreField: r3->field_f = r0
    //     0xb33ca4: stur            w0, [x3, #0xf]
    // 0xb33ca8: r0 = Instance_MainAxisAlignment
    //     0xb33ca8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb33cac: ldr             x0, [x0, #0xa08]
    // 0xb33cb0: StoreField: r3->field_13 = r0
    //     0xb33cb0: stur            w0, [x3, #0x13]
    // 0xb33cb4: r4 = Instance_MainAxisSize
    //     0xb33cb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb33cb8: ldr             x4, [x4, #0xa10]
    // 0xb33cbc: ArrayStore: r3[0] = r4  ; List_4
    //     0xb33cbc: stur            w4, [x3, #0x17]
    // 0xb33cc0: r5 = Instance_CrossAxisAlignment
    //     0xb33cc0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb33cc4: ldr             x5, [x5, #0xa18]
    // 0xb33cc8: StoreField: r3->field_1b = r5
    //     0xb33cc8: stur            w5, [x3, #0x1b]
    // 0xb33ccc: r6 = Instance_VerticalDirection
    //     0xb33ccc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb33cd0: ldr             x6, [x6, #0xa20]
    // 0xb33cd4: StoreField: r3->field_23 = r6
    //     0xb33cd4: stur            w6, [x3, #0x23]
    // 0xb33cd8: r7 = Instance_Clip
    //     0xb33cd8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb33cdc: ldr             x7, [x7, #0x38]
    // 0xb33ce0: StoreField: r3->field_2b = r7
    //     0xb33ce0: stur            w7, [x3, #0x2b]
    // 0xb33ce4: StoreField: r3->field_2f = rZR
    //     0xb33ce4: stur            xzr, [x3, #0x2f]
    // 0xb33ce8: ldur            x1, [fp, #-0x20]
    // 0xb33cec: StoreField: r3->field_b = r1
    //     0xb33cec: stur            w1, [x3, #0xb]
    // 0xb33cf0: r1 = Null
    //     0xb33cf0: mov             x1, NULL
    // 0xb33cf4: r2 = 4
    //     0xb33cf4: movz            x2, #0x4
    // 0xb33cf8: r0 = AllocateArray()
    //     0xb33cf8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb33cfc: stur            x0, [fp, #-0x20]
    // 0xb33d00: r16 = Instance_Divider
    //     0xb33d00: add             x16, PP, #0x57, lsl #12  ; [pp+0x57260] Obj!Divider@d66cd1
    //     0xb33d04: ldr             x16, [x16, #0x260]
    // 0xb33d08: StoreField: r0->field_f = r16
    //     0xb33d08: stur            w16, [x0, #0xf]
    // 0xb33d0c: ldur            x1, [fp, #-0x18]
    // 0xb33d10: StoreField: r0->field_13 = r1
    //     0xb33d10: stur            w1, [x0, #0x13]
    // 0xb33d14: r1 = <Widget>
    //     0xb33d14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb33d18: r0 = AllocateGrowableArray()
    //     0xb33d18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb33d1c: mov             x1, x0
    // 0xb33d20: ldur            x0, [fp, #-0x20]
    // 0xb33d24: stur            x1, [fp, #-0x18]
    // 0xb33d28: StoreField: r1->field_f = r0
    //     0xb33d28: stur            w0, [x1, #0xf]
    // 0xb33d2c: r0 = 4
    //     0xb33d2c: movz            x0, #0x4
    // 0xb33d30: StoreField: r1->field_b = r0
    //     0xb33d30: stur            w0, [x1, #0xb]
    // 0xb33d34: r0 = Column()
    //     0xb33d34: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb33d38: mov             x1, x0
    // 0xb33d3c: r0 = Instance_Axis
    //     0xb33d3c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb33d40: stur            x1, [fp, #-0x20]
    // 0xb33d44: StoreField: r1->field_f = r0
    //     0xb33d44: stur            w0, [x1, #0xf]
    // 0xb33d48: r0 = Instance_MainAxisAlignment
    //     0xb33d48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb33d4c: ldr             x0, [x0, #0xa08]
    // 0xb33d50: StoreField: r1->field_13 = r0
    //     0xb33d50: stur            w0, [x1, #0x13]
    // 0xb33d54: r0 = Instance_MainAxisSize
    //     0xb33d54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb33d58: ldr             x0, [x0, #0xa10]
    // 0xb33d5c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb33d5c: stur            w0, [x1, #0x17]
    // 0xb33d60: r0 = Instance_CrossAxisAlignment
    //     0xb33d60: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb33d64: ldr             x0, [x0, #0xa18]
    // 0xb33d68: StoreField: r1->field_1b = r0
    //     0xb33d68: stur            w0, [x1, #0x1b]
    // 0xb33d6c: r0 = Instance_VerticalDirection
    //     0xb33d6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb33d70: ldr             x0, [x0, #0xa20]
    // 0xb33d74: StoreField: r1->field_23 = r0
    //     0xb33d74: stur            w0, [x1, #0x23]
    // 0xb33d78: r0 = Instance_Clip
    //     0xb33d78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb33d7c: ldr             x0, [x0, #0x38]
    // 0xb33d80: StoreField: r1->field_2b = r0
    //     0xb33d80: stur            w0, [x1, #0x2b]
    // 0xb33d84: StoreField: r1->field_2f = rZR
    //     0xb33d84: stur            xzr, [x1, #0x2f]
    // 0xb33d88: ldur            x0, [fp, #-0x18]
    // 0xb33d8c: StoreField: r1->field_b = r0
    //     0xb33d8c: stur            w0, [x1, #0xb]
    // 0xb33d90: r0 = Padding()
    //     0xb33d90: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb33d94: mov             x1, x0
    // 0xb33d98: r0 = Instance_EdgeInsets
    //     0xb33d98: add             x0, PP, #0x57, lsl #12  ; [pp+0x57268] Obj!EdgeInsets@d58ca1
    //     0xb33d9c: ldr             x0, [x0, #0x268]
    // 0xb33da0: stur            x1, [fp, #-0x18]
    // 0xb33da4: StoreField: r1->field_f = r0
    //     0xb33da4: stur            w0, [x1, #0xf]
    // 0xb33da8: ldur            x0, [fp, #-0x20]
    // 0xb33dac: StoreField: r1->field_b = r0
    //     0xb33dac: stur            w0, [x1, #0xb]
    // 0xb33db0: r0 = IntrinsicHeight()
    //     0xb33db0: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xb33db4: mov             x1, x0
    // 0xb33db8: ldur            x0, [fp, #-0x18]
    // 0xb33dbc: StoreField: r1->field_b = r0
    //     0xb33dbc: stur            w0, [x1, #0xb]
    // 0xb33dc0: mov             x0, x1
    // 0xb33dc4: b               #0xb34434
    // 0xb33dc8: ldur            x2, [fp, #-8]
    // 0xb33dcc: LoadField: r0 = r2->field_f
    //     0xb33dcc: ldur            w0, [x2, #0xf]
    // 0xb33dd0: DecompressPointer r0
    //     0xb33dd0: add             x0, x0, HEAP, lsl #32
    // 0xb33dd4: LoadField: r3 = r0->field_b
    //     0xb33dd4: ldur            w3, [x0, #0xb]
    // 0xb33dd8: DecompressPointer r3
    //     0xb33dd8: add             x3, x3, HEAP, lsl #32
    // 0xb33ddc: cmp             w3, NULL
    // 0xb33de0: b.eq            #0xb34450
    // 0xb33de4: LoadField: r0 = r3->field_b
    //     0xb33de4: ldur            w0, [x3, #0xb]
    // 0xb33de8: DecompressPointer r0
    //     0xb33de8: add             x0, x0, HEAP, lsl #32
    // 0xb33dec: cmp             w0, NULL
    // 0xb33df0: b.ne            #0xb33dfc
    // 0xb33df4: r0 = Null
    //     0xb33df4: mov             x0, NULL
    // 0xb33df8: b               #0xb33e24
    // 0xb33dfc: r3 = LoadClassIdInstr(r0)
    //     0xb33dfc: ldur            x3, [x0, #-1]
    //     0xb33e00: ubfx            x3, x3, #0xc, #0x14
    // 0xb33e04: stp             x1, x0, [SP]
    // 0xb33e08: mov             x0, x3
    // 0xb33e0c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb33e0c: sub             lr, x0, #0xb7
    //     0xb33e10: ldr             lr, [x21, lr, lsl #3]
    //     0xb33e14: blr             lr
    // 0xb33e18: LoadField: r1 = r0->field_f
    //     0xb33e18: ldur            w1, [x0, #0xf]
    // 0xb33e1c: DecompressPointer r1
    //     0xb33e1c: add             x1, x1, HEAP, lsl #32
    // 0xb33e20: mov             x0, x1
    // 0xb33e24: r16 = Instance_CustomisationType
    //     0xb33e24: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xb33e28: ldr             x16, [x16, #0x660]
    // 0xb33e2c: cmp             w0, w16
    // 0xb33e30: b.ne            #0xb33f5c
    // 0xb33e34: ldur            x1, [fp, #-8]
    // 0xb33e38: LoadField: r0 = r1->field_f
    //     0xb33e38: ldur            w0, [x1, #0xf]
    // 0xb33e3c: DecompressPointer r0
    //     0xb33e3c: add             x0, x0, HEAP, lsl #32
    // 0xb33e40: LoadField: r2 = r0->field_b
    //     0xb33e40: ldur            w2, [x0, #0xb]
    // 0xb33e44: DecompressPointer r2
    //     0xb33e44: add             x2, x2, HEAP, lsl #32
    // 0xb33e48: cmp             w2, NULL
    // 0xb33e4c: b.eq            #0xb34454
    // 0xb33e50: LoadField: r0 = r2->field_b
    //     0xb33e50: ldur            w0, [x2, #0xb]
    // 0xb33e54: DecompressPointer r0
    //     0xb33e54: add             x0, x0, HEAP, lsl #32
    // 0xb33e58: cmp             w0, NULL
    // 0xb33e5c: b.ne            #0xb33e68
    // 0xb33e60: r2 = Null
    //     0xb33e60: mov             x2, NULL
    // 0xb33e64: b               #0xb33e90
    // 0xb33e68: r2 = LoadClassIdInstr(r0)
    //     0xb33e68: ldur            x2, [x0, #-1]
    //     0xb33e6c: ubfx            x2, x2, #0xc, #0x14
    // 0xb33e70: ldr             x16, [fp, #0x10]
    // 0xb33e74: stp             x16, x0, [SP]
    // 0xb33e78: mov             x0, x2
    // 0xb33e7c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb33e7c: sub             lr, x0, #0xb7
    //     0xb33e80: ldr             lr, [x21, lr, lsl #3]
    //     0xb33e84: blr             lr
    // 0xb33e88: mov             x2, x0
    // 0xb33e8c: ldur            x1, [fp, #-8]
    // 0xb33e90: stur            x2, [fp, #-0x20]
    // 0xb33e94: LoadField: r0 = r1->field_f
    //     0xb33e94: ldur            w0, [x1, #0xf]
    // 0xb33e98: DecompressPointer r0
    //     0xb33e98: add             x0, x0, HEAP, lsl #32
    // 0xb33e9c: LoadField: r1 = r0->field_b
    //     0xb33e9c: ldur            w1, [x0, #0xb]
    // 0xb33ea0: DecompressPointer r1
    //     0xb33ea0: add             x1, x1, HEAP, lsl #32
    // 0xb33ea4: cmp             w1, NULL
    // 0xb33ea8: b.eq            #0xb34458
    // 0xb33eac: LoadField: r0 = r1->field_f
    //     0xb33eac: ldur            w0, [x1, #0xf]
    // 0xb33eb0: DecompressPointer r0
    //     0xb33eb0: add             x0, x0, HEAP, lsl #32
    // 0xb33eb4: cmp             w0, NULL
    // 0xb33eb8: b.ne            #0xb33ec4
    // 0xb33ebc: r0 = Null
    //     0xb33ebc: mov             x0, NULL
    // 0xb33ec0: b               #0xb33f1c
    // 0xb33ec4: LoadField: r3 = r0->field_13
    //     0xb33ec4: ldur            w3, [x0, #0x13]
    // 0xb33ec8: DecompressPointer r3
    //     0xb33ec8: add             x3, x3, HEAP, lsl #32
    // 0xb33ecc: cmp             w3, NULL
    // 0xb33ed0: b.ne            #0xb33edc
    // 0xb33ed4: r0 = Null
    //     0xb33ed4: mov             x0, NULL
    // 0xb33ed8: b               #0xb33f1c
    // 0xb33edc: ldr             x4, [fp, #0x10]
    // 0xb33ee0: LoadField: r0 = r3->field_b
    //     0xb33ee0: ldur            w0, [x3, #0xb]
    // 0xb33ee4: r5 = LoadInt32Instr(r4)
    //     0xb33ee4: sbfx            x5, x4, #1, #0x1f
    //     0xb33ee8: tbz             w4, #0, #0xb33ef0
    //     0xb33eec: ldur            x5, [x4, #7]
    // 0xb33ef0: r1 = LoadInt32Instr(r0)
    //     0xb33ef0: sbfx            x1, x0, #1, #0x1f
    // 0xb33ef4: mov             x0, x1
    // 0xb33ef8: mov             x1, x5
    // 0xb33efc: cmp             x1, x0
    // 0xb33f00: b.hs            #0xb3445c
    // 0xb33f04: LoadField: r0 = r3->field_f
    //     0xb33f04: ldur            w0, [x3, #0xf]
    // 0xb33f08: DecompressPointer r0
    //     0xb33f08: add             x0, x0, HEAP, lsl #32
    // 0xb33f0c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb33f0c: add             x16, x0, x5, lsl #2
    //     0xb33f10: ldur            w1, [x16, #0xf]
    // 0xb33f14: DecompressPointer r1
    //     0xb33f14: add             x1, x1, HEAP, lsl #32
    // 0xb33f18: mov             x0, x1
    // 0xb33f1c: stur            x0, [fp, #-0x18]
    // 0xb33f20: r0 = BagMultiSelect()
    //     0xb33f20: bl              #0xb33a98  ; AllocateBagMultiSelectStub -> BagMultiSelect (size=0x14)
    // 0xb33f24: mov             x1, x0
    // 0xb33f28: ldur            x0, [fp, #-0x20]
    // 0xb33f2c: stur            x1, [fp, #-0x28]
    // 0xb33f30: StoreField: r1->field_b = r0
    //     0xb33f30: stur            w0, [x1, #0xb]
    // 0xb33f34: ldur            x0, [fp, #-0x18]
    // 0xb33f38: StoreField: r1->field_f = r0
    //     0xb33f38: stur            w0, [x1, #0xf]
    // 0xb33f3c: r0 = Padding()
    //     0xb33f3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb33f40: r2 = Instance_EdgeInsets
    //     0xb33f40: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb33f44: ldr             x2, [x2, #0x668]
    // 0xb33f48: StoreField: r0->field_f = r2
    //     0xb33f48: stur            w2, [x0, #0xf]
    // 0xb33f4c: ldur            x1, [fp, #-0x28]
    // 0xb33f50: StoreField: r0->field_b = r1
    //     0xb33f50: stur            w1, [x0, #0xb]
    // 0xb33f54: mov             x1, x0
    // 0xb33f58: b               #0xb34430
    // 0xb33f5c: ldr             x4, [fp, #0x10]
    // 0xb33f60: ldur            x1, [fp, #-8]
    // 0xb33f64: r2 = Instance_EdgeInsets
    //     0xb33f64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb33f68: ldr             x2, [x2, #0x668]
    // 0xb33f6c: r16 = Instance_CustomisationType
    //     0xb33f6c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xb33f70: ldr             x16, [x16, #0x670]
    // 0xb33f74: cmp             w0, w16
    // 0xb33f78: b.ne            #0xb3409c
    // 0xb33f7c: LoadField: r0 = r1->field_f
    //     0xb33f7c: ldur            w0, [x1, #0xf]
    // 0xb33f80: DecompressPointer r0
    //     0xb33f80: add             x0, x0, HEAP, lsl #32
    // 0xb33f84: LoadField: r3 = r0->field_b
    //     0xb33f84: ldur            w3, [x0, #0xb]
    // 0xb33f88: DecompressPointer r3
    //     0xb33f88: add             x3, x3, HEAP, lsl #32
    // 0xb33f8c: cmp             w3, NULL
    // 0xb33f90: b.eq            #0xb34460
    // 0xb33f94: LoadField: r0 = r3->field_b
    //     0xb33f94: ldur            w0, [x3, #0xb]
    // 0xb33f98: DecompressPointer r0
    //     0xb33f98: add             x0, x0, HEAP, lsl #32
    // 0xb33f9c: cmp             w0, NULL
    // 0xb33fa0: b.ne            #0xb33fac
    // 0xb33fa4: r2 = Null
    //     0xb33fa4: mov             x2, NULL
    // 0xb33fa8: b               #0xb33fd0
    // 0xb33fac: r3 = LoadClassIdInstr(r0)
    //     0xb33fac: ldur            x3, [x0, #-1]
    //     0xb33fb0: ubfx            x3, x3, #0xc, #0x14
    // 0xb33fb4: stp             x4, x0, [SP]
    // 0xb33fb8: mov             x0, x3
    // 0xb33fbc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb33fbc: sub             lr, x0, #0xb7
    //     0xb33fc0: ldr             lr, [x21, lr, lsl #3]
    //     0xb33fc4: blr             lr
    // 0xb33fc8: mov             x2, x0
    // 0xb33fcc: ldur            x1, [fp, #-8]
    // 0xb33fd0: stur            x2, [fp, #-0x20]
    // 0xb33fd4: LoadField: r0 = r1->field_f
    //     0xb33fd4: ldur            w0, [x1, #0xf]
    // 0xb33fd8: DecompressPointer r0
    //     0xb33fd8: add             x0, x0, HEAP, lsl #32
    // 0xb33fdc: LoadField: r1 = r0->field_b
    //     0xb33fdc: ldur            w1, [x0, #0xb]
    // 0xb33fe0: DecompressPointer r1
    //     0xb33fe0: add             x1, x1, HEAP, lsl #32
    // 0xb33fe4: cmp             w1, NULL
    // 0xb33fe8: b.eq            #0xb34464
    // 0xb33fec: LoadField: r0 = r1->field_f
    //     0xb33fec: ldur            w0, [x1, #0xf]
    // 0xb33ff0: DecompressPointer r0
    //     0xb33ff0: add             x0, x0, HEAP, lsl #32
    // 0xb33ff4: cmp             w0, NULL
    // 0xb33ff8: b.ne            #0xb34004
    // 0xb33ffc: r0 = Null
    //     0xb33ffc: mov             x0, NULL
    // 0xb34000: b               #0xb3405c
    // 0xb34004: LoadField: r3 = r0->field_13
    //     0xb34004: ldur            w3, [x0, #0x13]
    // 0xb34008: DecompressPointer r3
    //     0xb34008: add             x3, x3, HEAP, lsl #32
    // 0xb3400c: cmp             w3, NULL
    // 0xb34010: b.ne            #0xb3401c
    // 0xb34014: r0 = Null
    //     0xb34014: mov             x0, NULL
    // 0xb34018: b               #0xb3405c
    // 0xb3401c: ldr             x4, [fp, #0x10]
    // 0xb34020: LoadField: r0 = r3->field_b
    //     0xb34020: ldur            w0, [x3, #0xb]
    // 0xb34024: r5 = LoadInt32Instr(r4)
    //     0xb34024: sbfx            x5, x4, #1, #0x1f
    //     0xb34028: tbz             w4, #0, #0xb34030
    //     0xb3402c: ldur            x5, [x4, #7]
    // 0xb34030: r1 = LoadInt32Instr(r0)
    //     0xb34030: sbfx            x1, x0, #1, #0x1f
    // 0xb34034: mov             x0, x1
    // 0xb34038: mov             x1, x5
    // 0xb3403c: cmp             x1, x0
    // 0xb34040: b.hs            #0xb34468
    // 0xb34044: LoadField: r0 = r3->field_f
    //     0xb34044: ldur            w0, [x3, #0xf]
    // 0xb34048: DecompressPointer r0
    //     0xb34048: add             x0, x0, HEAP, lsl #32
    // 0xb3404c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb3404c: add             x16, x0, x5, lsl #2
    //     0xb34050: ldur            w1, [x16, #0xf]
    // 0xb34054: DecompressPointer r1
    //     0xb34054: add             x1, x1, HEAP, lsl #32
    // 0xb34058: mov             x0, x1
    // 0xb3405c: stur            x0, [fp, #-0x18]
    // 0xb34060: r0 = BagSingleSelect()
    //     0xb34060: bl              #0xb33a8c  ; AllocateBagSingleSelectStub -> BagSingleSelect (size=0x14)
    // 0xb34064: mov             x1, x0
    // 0xb34068: ldur            x0, [fp, #-0x20]
    // 0xb3406c: stur            x1, [fp, #-0x28]
    // 0xb34070: StoreField: r1->field_b = r0
    //     0xb34070: stur            w0, [x1, #0xb]
    // 0xb34074: ldur            x0, [fp, #-0x18]
    // 0xb34078: StoreField: r1->field_f = r0
    //     0xb34078: stur            w0, [x1, #0xf]
    // 0xb3407c: r0 = Padding()
    //     0xb3407c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb34080: r2 = Instance_EdgeInsets
    //     0xb34080: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb34084: ldr             x2, [x2, #0x668]
    // 0xb34088: StoreField: r0->field_f = r2
    //     0xb34088: stur            w2, [x0, #0xf]
    // 0xb3408c: ldur            x1, [fp, #-0x28]
    // 0xb34090: StoreField: r0->field_b = r1
    //     0xb34090: stur            w1, [x0, #0xb]
    // 0xb34094: mov             x1, x0
    // 0xb34098: b               #0xb34430
    // 0xb3409c: r16 = Instance_CustomisationType
    //     0xb3409c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xb340a0: ldr             x16, [x16, #0x650]
    // 0xb340a4: cmp             w0, w16
    // 0xb340a8: b.ne            #0xb341cc
    // 0xb340ac: LoadField: r0 = r1->field_f
    //     0xb340ac: ldur            w0, [x1, #0xf]
    // 0xb340b0: DecompressPointer r0
    //     0xb340b0: add             x0, x0, HEAP, lsl #32
    // 0xb340b4: LoadField: r3 = r0->field_b
    //     0xb340b4: ldur            w3, [x0, #0xb]
    // 0xb340b8: DecompressPointer r3
    //     0xb340b8: add             x3, x3, HEAP, lsl #32
    // 0xb340bc: cmp             w3, NULL
    // 0xb340c0: b.eq            #0xb3446c
    // 0xb340c4: LoadField: r0 = r3->field_b
    //     0xb340c4: ldur            w0, [x3, #0xb]
    // 0xb340c8: DecompressPointer r0
    //     0xb340c8: add             x0, x0, HEAP, lsl #32
    // 0xb340cc: cmp             w0, NULL
    // 0xb340d0: b.ne            #0xb340dc
    // 0xb340d4: r2 = Null
    //     0xb340d4: mov             x2, NULL
    // 0xb340d8: b               #0xb34100
    // 0xb340dc: r3 = LoadClassIdInstr(r0)
    //     0xb340dc: ldur            x3, [x0, #-1]
    //     0xb340e0: ubfx            x3, x3, #0xc, #0x14
    // 0xb340e4: stp             x4, x0, [SP]
    // 0xb340e8: mov             x0, x3
    // 0xb340ec: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb340ec: sub             lr, x0, #0xb7
    //     0xb340f0: ldr             lr, [x21, lr, lsl #3]
    //     0xb340f4: blr             lr
    // 0xb340f8: mov             x2, x0
    // 0xb340fc: ldur            x1, [fp, #-8]
    // 0xb34100: stur            x2, [fp, #-0x20]
    // 0xb34104: LoadField: r0 = r1->field_f
    //     0xb34104: ldur            w0, [x1, #0xf]
    // 0xb34108: DecompressPointer r0
    //     0xb34108: add             x0, x0, HEAP, lsl #32
    // 0xb3410c: LoadField: r1 = r0->field_b
    //     0xb3410c: ldur            w1, [x0, #0xb]
    // 0xb34110: DecompressPointer r1
    //     0xb34110: add             x1, x1, HEAP, lsl #32
    // 0xb34114: cmp             w1, NULL
    // 0xb34118: b.eq            #0xb34470
    // 0xb3411c: LoadField: r0 = r1->field_f
    //     0xb3411c: ldur            w0, [x1, #0xf]
    // 0xb34120: DecompressPointer r0
    //     0xb34120: add             x0, x0, HEAP, lsl #32
    // 0xb34124: cmp             w0, NULL
    // 0xb34128: b.ne            #0xb34134
    // 0xb3412c: r0 = Null
    //     0xb3412c: mov             x0, NULL
    // 0xb34130: b               #0xb3418c
    // 0xb34134: LoadField: r3 = r0->field_13
    //     0xb34134: ldur            w3, [x0, #0x13]
    // 0xb34138: DecompressPointer r3
    //     0xb34138: add             x3, x3, HEAP, lsl #32
    // 0xb3413c: cmp             w3, NULL
    // 0xb34140: b.ne            #0xb3414c
    // 0xb34144: r0 = Null
    //     0xb34144: mov             x0, NULL
    // 0xb34148: b               #0xb3418c
    // 0xb3414c: ldr             x4, [fp, #0x10]
    // 0xb34150: LoadField: r0 = r3->field_b
    //     0xb34150: ldur            w0, [x3, #0xb]
    // 0xb34154: r5 = LoadInt32Instr(r4)
    //     0xb34154: sbfx            x5, x4, #1, #0x1f
    //     0xb34158: tbz             w4, #0, #0xb34160
    //     0xb3415c: ldur            x5, [x4, #7]
    // 0xb34160: r1 = LoadInt32Instr(r0)
    //     0xb34160: sbfx            x1, x0, #1, #0x1f
    // 0xb34164: mov             x0, x1
    // 0xb34168: mov             x1, x5
    // 0xb3416c: cmp             x1, x0
    // 0xb34170: b.hs            #0xb34474
    // 0xb34174: LoadField: r0 = r3->field_f
    //     0xb34174: ldur            w0, [x3, #0xf]
    // 0xb34178: DecompressPointer r0
    //     0xb34178: add             x0, x0, HEAP, lsl #32
    // 0xb3417c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb3417c: add             x16, x0, x5, lsl #2
    //     0xb34180: ldur            w1, [x16, #0xf]
    // 0xb34184: DecompressPointer r1
    //     0xb34184: add             x1, x1, HEAP, lsl #32
    // 0xb34188: mov             x0, x1
    // 0xb3418c: stur            x0, [fp, #-0x18]
    // 0xb34190: r0 = BagImages()
    //     0xb34190: bl              #0xb33a80  ; AllocateBagImagesStub -> BagImages (size=0x14)
    // 0xb34194: mov             x1, x0
    // 0xb34198: ldur            x0, [fp, #-0x20]
    // 0xb3419c: stur            x1, [fp, #-0x28]
    // 0xb341a0: StoreField: r1->field_b = r0
    //     0xb341a0: stur            w0, [x1, #0xb]
    // 0xb341a4: ldur            x0, [fp, #-0x18]
    // 0xb341a8: StoreField: r1->field_f = r0
    //     0xb341a8: stur            w0, [x1, #0xf]
    // 0xb341ac: r0 = Padding()
    //     0xb341ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb341b0: r2 = Instance_EdgeInsets
    //     0xb341b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb341b4: ldr             x2, [x2, #0x668]
    // 0xb341b8: StoreField: r0->field_f = r2
    //     0xb341b8: stur            w2, [x0, #0xf]
    // 0xb341bc: ldur            x1, [fp, #-0x28]
    // 0xb341c0: StoreField: r0->field_b = r1
    //     0xb341c0: stur            w1, [x0, #0xb]
    // 0xb341c4: mov             x1, x0
    // 0xb341c8: b               #0xb34430
    // 0xb341cc: r16 = Instance_CustomisationType
    //     0xb341cc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xb341d0: ldr             x16, [x16, #0x680]
    // 0xb341d4: cmp             w0, w16
    // 0xb341d8: b.ne            #0xb342fc
    // 0xb341dc: LoadField: r0 = r1->field_f
    //     0xb341dc: ldur            w0, [x1, #0xf]
    // 0xb341e0: DecompressPointer r0
    //     0xb341e0: add             x0, x0, HEAP, lsl #32
    // 0xb341e4: LoadField: r3 = r0->field_b
    //     0xb341e4: ldur            w3, [x0, #0xb]
    // 0xb341e8: DecompressPointer r3
    //     0xb341e8: add             x3, x3, HEAP, lsl #32
    // 0xb341ec: cmp             w3, NULL
    // 0xb341f0: b.eq            #0xb34478
    // 0xb341f4: LoadField: r0 = r3->field_b
    //     0xb341f4: ldur            w0, [x3, #0xb]
    // 0xb341f8: DecompressPointer r0
    //     0xb341f8: add             x0, x0, HEAP, lsl #32
    // 0xb341fc: cmp             w0, NULL
    // 0xb34200: b.ne            #0xb3420c
    // 0xb34204: r2 = Null
    //     0xb34204: mov             x2, NULL
    // 0xb34208: b               #0xb34230
    // 0xb3420c: r3 = LoadClassIdInstr(r0)
    //     0xb3420c: ldur            x3, [x0, #-1]
    //     0xb34210: ubfx            x3, x3, #0xc, #0x14
    // 0xb34214: stp             x4, x0, [SP]
    // 0xb34218: mov             x0, x3
    // 0xb3421c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb3421c: sub             lr, x0, #0xb7
    //     0xb34220: ldr             lr, [x21, lr, lsl #3]
    //     0xb34224: blr             lr
    // 0xb34228: mov             x2, x0
    // 0xb3422c: ldur            x1, [fp, #-8]
    // 0xb34230: stur            x2, [fp, #-0x20]
    // 0xb34234: LoadField: r0 = r1->field_f
    //     0xb34234: ldur            w0, [x1, #0xf]
    // 0xb34238: DecompressPointer r0
    //     0xb34238: add             x0, x0, HEAP, lsl #32
    // 0xb3423c: LoadField: r1 = r0->field_b
    //     0xb3423c: ldur            w1, [x0, #0xb]
    // 0xb34240: DecompressPointer r1
    //     0xb34240: add             x1, x1, HEAP, lsl #32
    // 0xb34244: cmp             w1, NULL
    // 0xb34248: b.eq            #0xb3447c
    // 0xb3424c: LoadField: r0 = r1->field_f
    //     0xb3424c: ldur            w0, [x1, #0xf]
    // 0xb34250: DecompressPointer r0
    //     0xb34250: add             x0, x0, HEAP, lsl #32
    // 0xb34254: cmp             w0, NULL
    // 0xb34258: b.ne            #0xb34264
    // 0xb3425c: r0 = Null
    //     0xb3425c: mov             x0, NULL
    // 0xb34260: b               #0xb342bc
    // 0xb34264: LoadField: r3 = r0->field_13
    //     0xb34264: ldur            w3, [x0, #0x13]
    // 0xb34268: DecompressPointer r3
    //     0xb34268: add             x3, x3, HEAP, lsl #32
    // 0xb3426c: cmp             w3, NULL
    // 0xb34270: b.ne            #0xb3427c
    // 0xb34274: r0 = Null
    //     0xb34274: mov             x0, NULL
    // 0xb34278: b               #0xb342bc
    // 0xb3427c: ldr             x4, [fp, #0x10]
    // 0xb34280: LoadField: r0 = r3->field_b
    //     0xb34280: ldur            w0, [x3, #0xb]
    // 0xb34284: r5 = LoadInt32Instr(r4)
    //     0xb34284: sbfx            x5, x4, #1, #0x1f
    //     0xb34288: tbz             w4, #0, #0xb34290
    //     0xb3428c: ldur            x5, [x4, #7]
    // 0xb34290: r1 = LoadInt32Instr(r0)
    //     0xb34290: sbfx            x1, x0, #1, #0x1f
    // 0xb34294: mov             x0, x1
    // 0xb34298: mov             x1, x5
    // 0xb3429c: cmp             x1, x0
    // 0xb342a0: b.hs            #0xb34480
    // 0xb342a4: LoadField: r0 = r3->field_f
    //     0xb342a4: ldur            w0, [x3, #0xf]
    // 0xb342a8: DecompressPointer r0
    //     0xb342a8: add             x0, x0, HEAP, lsl #32
    // 0xb342ac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb342ac: add             x16, x0, x5, lsl #2
    //     0xb342b0: ldur            w1, [x16, #0xf]
    // 0xb342b4: DecompressPointer r1
    //     0xb342b4: add             x1, x1, HEAP, lsl #32
    // 0xb342b8: mov             x0, x1
    // 0xb342bc: stur            x0, [fp, #-0x18]
    // 0xb342c0: r0 = BagText()
    //     0xb342c0: bl              #0xb33a74  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xb342c4: mov             x1, x0
    // 0xb342c8: ldur            x0, [fp, #-0x20]
    // 0xb342cc: stur            x1, [fp, #-0x28]
    // 0xb342d0: StoreField: r1->field_b = r0
    //     0xb342d0: stur            w0, [x1, #0xb]
    // 0xb342d4: ldur            x0, [fp, #-0x18]
    // 0xb342d8: StoreField: r1->field_f = r0
    //     0xb342d8: stur            w0, [x1, #0xf]
    // 0xb342dc: r0 = Padding()
    //     0xb342dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb342e0: r2 = Instance_EdgeInsets
    //     0xb342e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb342e4: ldr             x2, [x2, #0x668]
    // 0xb342e8: StoreField: r0->field_f = r2
    //     0xb342e8: stur            w2, [x0, #0xf]
    // 0xb342ec: ldur            x1, [fp, #-0x28]
    // 0xb342f0: StoreField: r0->field_b = r1
    //     0xb342f0: stur            w1, [x0, #0xb]
    // 0xb342f4: mov             x1, x0
    // 0xb342f8: b               #0xb34430
    // 0xb342fc: r16 = Instance_CustomisationType
    //     0xb342fc: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xb34300: ldr             x16, [x16, #0x690]
    // 0xb34304: cmp             w0, w16
    // 0xb34308: b.ne            #0xb3442c
    // 0xb3430c: LoadField: r0 = r1->field_f
    //     0xb3430c: ldur            w0, [x1, #0xf]
    // 0xb34310: DecompressPointer r0
    //     0xb34310: add             x0, x0, HEAP, lsl #32
    // 0xb34314: LoadField: r3 = r0->field_b
    //     0xb34314: ldur            w3, [x0, #0xb]
    // 0xb34318: DecompressPointer r3
    //     0xb34318: add             x3, x3, HEAP, lsl #32
    // 0xb3431c: cmp             w3, NULL
    // 0xb34320: b.eq            #0xb34484
    // 0xb34324: LoadField: r0 = r3->field_b
    //     0xb34324: ldur            w0, [x3, #0xb]
    // 0xb34328: DecompressPointer r0
    //     0xb34328: add             x0, x0, HEAP, lsl #32
    // 0xb3432c: cmp             w0, NULL
    // 0xb34330: b.ne            #0xb34340
    // 0xb34334: mov             x0, x1
    // 0xb34338: r2 = Null
    //     0xb34338: mov             x2, NULL
    // 0xb3433c: b               #0xb34364
    // 0xb34340: r3 = LoadClassIdInstr(r0)
    //     0xb34340: ldur            x3, [x0, #-1]
    //     0xb34344: ubfx            x3, x3, #0xc, #0x14
    // 0xb34348: stp             x4, x0, [SP]
    // 0xb3434c: mov             x0, x3
    // 0xb34350: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb34350: sub             lr, x0, #0xb7
    //     0xb34354: ldr             lr, [x21, lr, lsl #3]
    //     0xb34358: blr             lr
    // 0xb3435c: mov             x2, x0
    // 0xb34360: ldur            x0, [fp, #-8]
    // 0xb34364: stur            x2, [fp, #-0x18]
    // 0xb34368: LoadField: r1 = r0->field_f
    //     0xb34368: ldur            w1, [x0, #0xf]
    // 0xb3436c: DecompressPointer r1
    //     0xb3436c: add             x1, x1, HEAP, lsl #32
    // 0xb34370: LoadField: r0 = r1->field_b
    //     0xb34370: ldur            w0, [x1, #0xb]
    // 0xb34374: DecompressPointer r0
    //     0xb34374: add             x0, x0, HEAP, lsl #32
    // 0xb34378: cmp             w0, NULL
    // 0xb3437c: b.eq            #0xb34488
    // 0xb34380: LoadField: r1 = r0->field_f
    //     0xb34380: ldur            w1, [x0, #0xf]
    // 0xb34384: DecompressPointer r1
    //     0xb34384: add             x1, x1, HEAP, lsl #32
    // 0xb34388: cmp             w1, NULL
    // 0xb3438c: b.ne            #0xb34398
    // 0xb34390: r0 = Null
    //     0xb34390: mov             x0, NULL
    // 0xb34394: b               #0xb343ec
    // 0xb34398: LoadField: r3 = r1->field_13
    //     0xb34398: ldur            w3, [x1, #0x13]
    // 0xb3439c: DecompressPointer r3
    //     0xb3439c: add             x3, x3, HEAP, lsl #32
    // 0xb343a0: cmp             w3, NULL
    // 0xb343a4: b.ne            #0xb343b0
    // 0xb343a8: r0 = Null
    //     0xb343a8: mov             x0, NULL
    // 0xb343ac: b               #0xb343ec
    // 0xb343b0: ldr             x0, [fp, #0x10]
    // 0xb343b4: LoadField: r1 = r3->field_b
    //     0xb343b4: ldur            w1, [x3, #0xb]
    // 0xb343b8: r4 = LoadInt32Instr(r0)
    //     0xb343b8: sbfx            x4, x0, #1, #0x1f
    //     0xb343bc: tbz             w0, #0, #0xb343c4
    //     0xb343c0: ldur            x4, [x0, #7]
    // 0xb343c4: r0 = LoadInt32Instr(r1)
    //     0xb343c4: sbfx            x0, x1, #1, #0x1f
    // 0xb343c8: mov             x1, x4
    // 0xb343cc: cmp             x1, x0
    // 0xb343d0: b.hs            #0xb3448c
    // 0xb343d4: LoadField: r0 = r3->field_f
    //     0xb343d4: ldur            w0, [x3, #0xf]
    // 0xb343d8: DecompressPointer r0
    //     0xb343d8: add             x0, x0, HEAP, lsl #32
    // 0xb343dc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb343dc: add             x16, x0, x4, lsl #2
    //     0xb343e0: ldur            w1, [x16, #0xf]
    // 0xb343e4: DecompressPointer r1
    //     0xb343e4: add             x1, x1, HEAP, lsl #32
    // 0xb343e8: mov             x0, x1
    // 0xb343ec: stur            x0, [fp, #-8]
    // 0xb343f0: r0 = BagText()
    //     0xb343f0: bl              #0xb33a74  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xb343f4: mov             x1, x0
    // 0xb343f8: ldur            x0, [fp, #-0x18]
    // 0xb343fc: stur            x1, [fp, #-0x20]
    // 0xb34400: StoreField: r1->field_b = r0
    //     0xb34400: stur            w0, [x1, #0xb]
    // 0xb34404: ldur            x0, [fp, #-8]
    // 0xb34408: StoreField: r1->field_f = r0
    //     0xb34408: stur            w0, [x1, #0xf]
    // 0xb3440c: r0 = Padding()
    //     0xb3440c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb34410: r1 = Instance_EdgeInsets
    //     0xb34410: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb34414: ldr             x1, [x1, #0x668]
    // 0xb34418: StoreField: r0->field_f = r1
    //     0xb34418: stur            w1, [x0, #0xf]
    // 0xb3441c: ldur            x1, [fp, #-0x20]
    // 0xb34420: StoreField: r0->field_b = r1
    //     0xb34420: stur            w1, [x0, #0xb]
    // 0xb34424: mov             x1, x0
    // 0xb34428: b               #0xb34430
    // 0xb3442c: ldur            x1, [fp, #-0x10]
    // 0xb34430: mov             x0, x1
    // 0xb34434: LeaveFrame
    //     0xb34434: mov             SP, fp
    //     0xb34438: ldp             fp, lr, [SP], #0x10
    // 0xb3443c: ret
    //     0xb3443c: ret             
    // 0xb34440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb34440: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb34444: b               #0xb33acc
    // 0xb34448: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34448: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3444c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3444c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34450: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34450: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34454: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34458: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3445c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3445c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb34460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34460: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34464: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34468: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb34468: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb3446c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3446c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34470: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34470: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34474: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb34474: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb34478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34478: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3447c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb3447c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34480: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb34480: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb34484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34484: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb34488: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb34488: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb3448c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb3448c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4116, size: 0x18, field offset: 0xc
//   const constructor, 
class CustomisedStrip extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e748, size: 0x24
    // 0xc7e748: EnterFrame
    //     0xc7e748: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e74c: mov             fp, SP
    // 0xc7e750: mov             x0, x1
    // 0xc7e754: r1 = <CustomisedStrip>
    //     0xc7e754: add             x1, PP, #0x48, lsl #12  ; [pp+0x48aa0] TypeArguments: <CustomisedStrip>
    //     0xc7e758: ldr             x1, [x1, #0xaa0]
    // 0xc7e75c: r0 = _CustomisedStripState()
    //     0xc7e75c: bl              #0xc7e76c  ; Allocate_CustomisedStripStateStub -> _CustomisedStripState (size=0x14)
    // 0xc7e760: LeaveFrame
    //     0xc7e760: mov             SP, fp
    //     0xc7e764: ldp             fp, lr, [SP], #0x10
    // 0xc7e768: ret
    //     0xc7e768: ret             
  }
  const _ CustomisedStrip(/* No info */) {
    // ** addr: 0x1420798, size: 0xbc
    // 0x1420798: EnterFrame
    //     0x1420798: stp             fp, lr, [SP, #-0x10]!
    //     0x142079c: mov             fp, SP
    // 0x14207a0: mov             x16, x2
    // 0x14207a4: mov             x2, x1
    // 0x14207a8: mov             x1, x16
    // 0x14207ac: mov             x0, x3
    // 0x14207b0: LoadField: r3 = r4->field_13
    //     0x14207b0: ldur            w3, [x4, #0x13]
    // 0x14207b4: LoadField: r5 = r4->field_1f
    //     0x14207b4: ldur            w5, [x4, #0x1f]
    // 0x14207b8: DecompressPointer r5
    //     0x14207b8: add             x5, x5, HEAP, lsl #32
    // 0x14207bc: r16 = "customizedRequest"
    //     0x14207bc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3dbd8] "customizedRequest"
    //     0x14207c0: ldr             x16, [x16, #0xbd8]
    // 0x14207c4: cmp             w5, w16
    // 0x14207c8: b.ne            #0x14207e4
    // 0x14207cc: LoadField: r5 = r4->field_23
    //     0x14207cc: ldur            w5, [x4, #0x23]
    // 0x14207d0: DecompressPointer r5
    //     0x14207d0: add             x5, x5, HEAP, lsl #32
    // 0x14207d4: sub             w4, w3, w5
    // 0x14207d8: add             x3, fp, w4, sxtw #2
    // 0x14207dc: ldr             x3, [x3, #8]
    // 0x14207e0: b               #0x14207e8
    // 0x14207e4: r3 = Null
    //     0x14207e4: mov             x3, NULL
    // 0x14207e8: StoreField: r2->field_b = r0
    //     0x14207e8: stur            w0, [x2, #0xb]
    //     0x14207ec: ldurb           w16, [x2, #-1]
    //     0x14207f0: ldurb           w17, [x0, #-1]
    //     0x14207f4: and             x16, x17, x16, lsr #2
    //     0x14207f8: tst             x16, HEAP, lsr #32
    //     0x14207fc: b.eq            #0x1420804
    //     0x1420800: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1420804: mov             x0, x3
    // 0x1420808: StoreField: r2->field_f = r0
    //     0x1420808: stur            w0, [x2, #0xf]
    //     0x142080c: ldurb           w16, [x2, #-1]
    //     0x1420810: ldurb           w17, [x0, #-1]
    //     0x1420814: and             x16, x17, x16, lsr #2
    //     0x1420818: tst             x16, HEAP, lsr #32
    //     0x142081c: b.eq            #0x1420824
    //     0x1420820: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1420824: mov             x0, x1
    // 0x1420828: StoreField: r2->field_13 = r0
    //     0x1420828: stur            w0, [x2, #0x13]
    //     0x142082c: ldurb           w16, [x2, #-1]
    //     0x1420830: ldurb           w17, [x0, #-1]
    //     0x1420834: and             x16, x17, x16, lsr #2
    //     0x1420838: tst             x16, HEAP, lsr #32
    //     0x142083c: b.eq            #0x1420844
    //     0x1420840: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x1420844: r0 = Null
    //     0x1420844: mov             x0, NULL
    // 0x1420848: LeaveFrame
    //     0x1420848: mov             SP, fp
    //     0x142084c: ldp             fp, lr, [SP], #0x10
    // 0x1420850: ret
    //     0x1420850: ret             
  }
}
