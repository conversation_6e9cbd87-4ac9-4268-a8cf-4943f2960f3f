// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/reply_to_seller_item_view.dart

// class id: 1049566, size: 0x8
class :: {
}

// class id: 3217, size: 0x14, field offset: 0x14
class _ReplyToSellerItemViewState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa8e78c, size: 0x94
    // 0xa8e78c: EnterFrame
    //     0xa8e78c: stp             fp, lr, [SP, #-0x10]!
    //     0xa8e790: mov             fp, SP
    // 0xa8e794: ldr             x0, [fp, #0x10]
    // 0xa8e798: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa8e798: ldur            w1, [x0, #0x17]
    // 0xa8e79c: DecompressPointer r1
    //     0xa8e79c: add             x1, x1, HEAP, lsl #32
    // 0xa8e7a0: CheckStackOverflow
    //     0xa8e7a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8e7a4: cmp             SP, x16
    //     0xa8e7a8: b.ls            #0xa8e814
    // 0xa8e7ac: LoadField: r0 = r1->field_f
    //     0xa8e7ac: ldur            w0, [x1, #0xf]
    // 0xa8e7b0: DecompressPointer r0
    //     0xa8e7b0: add             x0, x0, HEAP, lsl #32
    // 0xa8e7b4: LoadField: r1 = r0->field_b
    //     0xa8e7b4: ldur            w1, [x0, #0xb]
    // 0xa8e7b8: DecompressPointer r1
    //     0xa8e7b8: add             x1, x1, HEAP, lsl #32
    // 0xa8e7bc: cmp             w1, NULL
    // 0xa8e7c0: b.eq            #0xa8e81c
    // 0xa8e7c4: LoadField: r0 = r1->field_b
    //     0xa8e7c4: ldur            w0, [x1, #0xb]
    // 0xa8e7c8: DecompressPointer r0
    //     0xa8e7c8: add             x0, x0, HEAP, lsl #32
    // 0xa8e7cc: LoadField: r1 = r0->field_7b
    //     0xa8e7cc: ldur            w1, [x0, #0x7b]
    // 0xa8e7d0: DecompressPointer r1
    //     0xa8e7d0: add             x1, x1, HEAP, lsl #32
    // 0xa8e7d4: cmp             w1, NULL
    // 0xa8e7d8: b.ne            #0xa8e7e4
    // 0xa8e7dc: r0 = Null
    //     0xa8e7dc: mov             x0, NULL
    // 0xa8e7e0: b               #0xa8e7ec
    // 0xa8e7e4: LoadField: r0 = r1->field_b
    //     0xa8e7e4: ldur            w0, [x1, #0xb]
    // 0xa8e7e8: DecompressPointer r0
    //     0xa8e7e8: add             x0, x0, HEAP, lsl #32
    // 0xa8e7ec: cmp             w0, NULL
    // 0xa8e7f0: b.ne            #0xa8e7fc
    // 0xa8e7f4: r1 = ""
    //     0xa8e7f4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa8e7f8: b               #0xa8e800
    // 0xa8e7fc: mov             x1, x0
    // 0xa8e800: r0 = openWhatsApp()
    //     0xa8e800: bl              #0xa8e820  ; [package:customer_app/app/core/utils/utils.dart] Utils::openWhatsApp
    // 0xa8e804: r0 = Null
    //     0xa8e804: mov             x0, NULL
    // 0xa8e808: LeaveFrame
    //     0xa8e808: mov             SP, fp
    //     0xa8e80c: ldp             fp, lr, [SP], #0x10
    // 0xa8e810: ret
    //     0xa8e810: ret             
    // 0xa8e814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8e814: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8e818: b               #0xa8e7ac
    // 0xa8e81c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8e81c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc0b9f8, size: 0x818
    // 0xc0b9f8: EnterFrame
    //     0xc0b9f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc0b9fc: mov             fp, SP
    // 0xc0ba00: AllocStack(0x80)
    //     0xc0ba00: sub             SP, SP, #0x80
    // 0xc0ba04: SetupParameters(_ReplyToSellerItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc0ba04: mov             x0, x1
    //     0xc0ba08: stur            x1, [fp, #-8]
    //     0xc0ba0c: mov             x1, x2
    //     0xc0ba10: stur            x2, [fp, #-0x10]
    // 0xc0ba14: CheckStackOverflow
    //     0xc0ba14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0ba18: cmp             SP, x16
    //     0xc0ba1c: b.ls            #0xc0c1cc
    // 0xc0ba20: r1 = 1
    //     0xc0ba20: movz            x1, #0x1
    // 0xc0ba24: r0 = AllocateContext()
    //     0xc0ba24: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0ba28: mov             x2, x0
    // 0xc0ba2c: ldur            x0, [fp, #-8]
    // 0xc0ba30: stur            x2, [fp, #-0x20]
    // 0xc0ba34: StoreField: r2->field_f = r0
    //     0xc0ba34: stur            w0, [x2, #0xf]
    // 0xc0ba38: LoadField: r1 = r0->field_b
    //     0xc0ba38: ldur            w1, [x0, #0xb]
    // 0xc0ba3c: DecompressPointer r1
    //     0xc0ba3c: add             x1, x1, HEAP, lsl #32
    // 0xc0ba40: cmp             w1, NULL
    // 0xc0ba44: b.eq            #0xc0c1d4
    // 0xc0ba48: LoadField: r3 = r1->field_f
    //     0xc0ba48: ldur            w3, [x1, #0xf]
    // 0xc0ba4c: DecompressPointer r3
    //     0xc0ba4c: add             x3, x3, HEAP, lsl #32
    // 0xc0ba50: cmp             w3, NULL
    // 0xc0ba54: b.ne            #0xc0ba60
    // 0xc0ba58: r3 = Null
    //     0xc0ba58: mov             x3, NULL
    // 0xc0ba5c: b               #0xc0ba6c
    // 0xc0ba60: LoadField: r4 = r3->field_6f
    //     0xc0ba60: ldur            w4, [x3, #0x6f]
    // 0xc0ba64: DecompressPointer r4
    //     0xc0ba64: add             x4, x4, HEAP, lsl #32
    // 0xc0ba68: mov             x3, x4
    // 0xc0ba6c: cmp             w3, NULL
    // 0xc0ba70: b.eq            #0xc0bac0
    // 0xc0ba74: tbnz            w3, #4, #0xc0bac0
    // 0xc0ba78: LoadField: r3 = r1->field_b
    //     0xc0ba78: ldur            w3, [x1, #0xb]
    // 0xc0ba7c: DecompressPointer r3
    //     0xc0ba7c: add             x3, x3, HEAP, lsl #32
    // 0xc0ba80: LoadField: r1 = r3->field_f
    //     0xc0ba80: ldur            w1, [x3, #0xf]
    // 0xc0ba84: DecompressPointer r1
    //     0xc0ba84: add             x1, x1, HEAP, lsl #32
    // 0xc0ba88: cmp             w1, NULL
    // 0xc0ba8c: b.ne            #0xc0ba98
    // 0xc0ba90: r1 = Null
    //     0xc0ba90: mov             x1, NULL
    // 0xc0ba94: b               #0xc0baac
    // 0xc0ba98: LoadField: r3 = r1->field_7
    //     0xc0ba98: ldur            w3, [x1, #7]
    // 0xc0ba9c: cbnz            w3, #0xc0baa8
    // 0xc0baa0: r1 = false
    //     0xc0baa0: add             x1, NULL, #0x30  ; false
    // 0xc0baa4: b               #0xc0baac
    // 0xc0baa8: r1 = true
    //     0xc0baa8: add             x1, NULL, #0x20  ; true
    // 0xc0baac: cmp             w1, NULL
    // 0xc0bab0: b.ne            #0xc0bab8
    // 0xc0bab4: r1 = false
    //     0xc0bab4: add             x1, NULL, #0x30  ; false
    // 0xc0bab8: mov             x3, x1
    // 0xc0babc: b               #0xc0bac4
    // 0xc0bac0: r3 = false
    //     0xc0bac0: add             x3, NULL, #0x30  ; false
    // 0xc0bac4: ldur            x1, [fp, #-0x10]
    // 0xc0bac8: stur            x3, [fp, #-0x18]
    // 0xc0bacc: r0 = of()
    //     0xc0bacc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0bad0: LoadField: r1 = r0->field_5b
    //     0xc0bad0: ldur            w1, [x0, #0x5b]
    // 0xc0bad4: DecompressPointer r1
    //     0xc0bad4: add             x1, x1, HEAP, lsl #32
    // 0xc0bad8: r0 = LoadClassIdInstr(r1)
    //     0xc0bad8: ldur            x0, [x1, #-1]
    //     0xc0badc: ubfx            x0, x0, #0xc, #0x14
    // 0xc0bae0: d0 = 0.100000
    //     0xc0bae0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0bae4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc0bae4: sub             lr, x0, #0xffa
    //     0xc0bae8: ldr             lr, [x21, lr, lsl #3]
    //     0xc0baec: blr             lr
    // 0xc0baf0: r16 = 1.000000
    //     0xc0baf0: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc0baf4: str             x16, [SP]
    // 0xc0baf8: mov             x2, x0
    // 0xc0bafc: r1 = Null
    //     0xc0bafc: mov             x1, NULL
    // 0xc0bb00: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xc0bb00: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xc0bb04: ldr             x4, [x4, #0x108]
    // 0xc0bb08: r0 = Border.all()
    //     0xc0bb08: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc0bb0c: stur            x0, [fp, #-0x28]
    // 0xc0bb10: r0 = BoxDecoration()
    //     0xc0bb10: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc0bb14: mov             x2, x0
    // 0xc0bb18: r0 = Instance_DecorationImage
    //     0xc0bb18: add             x0, PP, #0x52, lsl #12  ; [pp+0x52ea0] Obj!DecorationImage@d5a141
    //     0xc0bb1c: ldr             x0, [x0, #0xea0]
    // 0xc0bb20: stur            x2, [fp, #-0x30]
    // 0xc0bb24: StoreField: r2->field_b = r0
    //     0xc0bb24: stur            w0, [x2, #0xb]
    // 0xc0bb28: ldur            x0, [fp, #-0x28]
    // 0xc0bb2c: StoreField: r2->field_f = r0
    //     0xc0bb2c: stur            w0, [x2, #0xf]
    // 0xc0bb30: r0 = Instance_BoxShape
    //     0xc0bb30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0bb34: ldr             x0, [x0, #0x80]
    // 0xc0bb38: StoreField: r2->field_23 = r0
    //     0xc0bb38: stur            w0, [x2, #0x23]
    // 0xc0bb3c: ldur            x3, [fp, #-8]
    // 0xc0bb40: LoadField: r1 = r3->field_b
    //     0xc0bb40: ldur            w1, [x3, #0xb]
    // 0xc0bb44: DecompressPointer r1
    //     0xc0bb44: add             x1, x1, HEAP, lsl #32
    // 0xc0bb48: cmp             w1, NULL
    // 0xc0bb4c: b.eq            #0xc0c1d8
    // 0xc0bb50: LoadField: r4 = r1->field_b
    //     0xc0bb50: ldur            w4, [x1, #0xb]
    // 0xc0bb54: DecompressPointer r4
    //     0xc0bb54: add             x4, x4, HEAP, lsl #32
    // 0xc0bb58: LoadField: r1 = r4->field_f
    //     0xc0bb58: ldur            w1, [x4, #0xf]
    // 0xc0bb5c: DecompressPointer r1
    //     0xc0bb5c: add             x1, x1, HEAP, lsl #32
    // 0xc0bb60: cmp             w1, NULL
    // 0xc0bb64: b.ne            #0xc0bb70
    // 0xc0bb68: r4 = ""
    //     0xc0bb68: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0bb6c: b               #0xc0bb74
    // 0xc0bb70: mov             x4, x1
    // 0xc0bb74: ldur            x1, [fp, #-0x10]
    // 0xc0bb78: stur            x4, [fp, #-0x28]
    // 0xc0bb7c: r0 = of()
    //     0xc0bb7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0bb80: LoadField: r1 = r0->field_87
    //     0xc0bb80: ldur            w1, [x0, #0x87]
    // 0xc0bb84: DecompressPointer r1
    //     0xc0bb84: add             x1, x1, HEAP, lsl #32
    // 0xc0bb88: LoadField: r0 = r1->field_7
    //     0xc0bb88: ldur            w0, [x1, #7]
    // 0xc0bb8c: DecompressPointer r0
    //     0xc0bb8c: add             x0, x0, HEAP, lsl #32
    // 0xc0bb90: stur            x0, [fp, #-0x38]
    // 0xc0bb94: r1 = Instance_Color
    //     0xc0bb94: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0bb98: d0 = 0.700000
    //     0xc0bb98: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc0bb9c: ldr             d0, [x17, #0xf48]
    // 0xc0bba0: r0 = withOpacity()
    //     0xc0bba0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0bba4: r16 = 16.000000
    //     0xc0bba4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc0bba8: ldr             x16, [x16, #0x188]
    // 0xc0bbac: stp             x0, x16, [SP]
    // 0xc0bbb0: ldur            x1, [fp, #-0x38]
    // 0xc0bbb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0bbb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0bbb8: ldr             x4, [x4, #0xaa0]
    // 0xc0bbbc: r0 = copyWith()
    //     0xc0bbbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0bbc0: stur            x0, [fp, #-0x38]
    // 0xc0bbc4: r0 = Text()
    //     0xc0bbc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0bbc8: mov             x1, x0
    // 0xc0bbcc: ldur            x0, [fp, #-0x28]
    // 0xc0bbd0: stur            x1, [fp, #-0x40]
    // 0xc0bbd4: StoreField: r1->field_b = r0
    //     0xc0bbd4: stur            w0, [x1, #0xb]
    // 0xc0bbd8: ldur            x0, [fp, #-0x38]
    // 0xc0bbdc: StoreField: r1->field_13 = r0
    //     0xc0bbdc: stur            w0, [x1, #0x13]
    // 0xc0bbe0: r0 = Padding()
    //     0xc0bbe0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0bbe4: mov             x2, x0
    // 0xc0bbe8: r0 = Instance_EdgeInsets
    //     0xc0bbe8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xc0bbec: ldr             x0, [x0, #0x858]
    // 0xc0bbf0: stur            x2, [fp, #-0x38]
    // 0xc0bbf4: StoreField: r2->field_f = r0
    //     0xc0bbf4: stur            w0, [x2, #0xf]
    // 0xc0bbf8: ldur            x0, [fp, #-0x40]
    // 0xc0bbfc: StoreField: r2->field_b = r0
    //     0xc0bbfc: stur            w0, [x2, #0xb]
    // 0xc0bc00: ldur            x0, [fp, #-8]
    // 0xc0bc04: LoadField: r1 = r0->field_b
    //     0xc0bc04: ldur            w1, [x0, #0xb]
    // 0xc0bc08: DecompressPointer r1
    //     0xc0bc08: add             x1, x1, HEAP, lsl #32
    // 0xc0bc0c: cmp             w1, NULL
    // 0xc0bc10: b.eq            #0xc0c1dc
    // 0xc0bc14: LoadField: r3 = r1->field_b
    //     0xc0bc14: ldur            w3, [x1, #0xb]
    // 0xc0bc18: DecompressPointer r3
    //     0xc0bc18: add             x3, x3, HEAP, lsl #32
    // 0xc0bc1c: LoadField: r1 = r3->field_73
    //     0xc0bc1c: ldur            w1, [x3, #0x73]
    // 0xc0bc20: DecompressPointer r1
    //     0xc0bc20: add             x1, x1, HEAP, lsl #32
    // 0xc0bc24: cmp             w1, NULL
    // 0xc0bc28: b.ne            #0xc0bc34
    // 0xc0bc2c: r3 = ""
    //     0xc0bc2c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0bc30: b               #0xc0bc38
    // 0xc0bc34: mov             x3, x1
    // 0xc0bc38: ldur            x1, [fp, #-0x10]
    // 0xc0bc3c: stur            x3, [fp, #-0x28]
    // 0xc0bc40: r0 = of()
    //     0xc0bc40: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0bc44: LoadField: r1 = r0->field_87
    //     0xc0bc44: ldur            w1, [x0, #0x87]
    // 0xc0bc48: DecompressPointer r1
    //     0xc0bc48: add             x1, x1, HEAP, lsl #32
    // 0xc0bc4c: LoadField: r0 = r1->field_2b
    //     0xc0bc4c: ldur            w0, [x1, #0x2b]
    // 0xc0bc50: DecompressPointer r0
    //     0xc0bc50: add             x0, x0, HEAP, lsl #32
    // 0xc0bc54: stur            x0, [fp, #-0x40]
    // 0xc0bc58: r1 = Instance_Color
    //     0xc0bc58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0bc5c: d0 = 0.700000
    //     0xc0bc5c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc0bc60: ldr             d0, [x17, #0xf48]
    // 0xc0bc64: r0 = withOpacity()
    //     0xc0bc64: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0bc68: r16 = 12.000000
    //     0xc0bc68: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0bc6c: ldr             x16, [x16, #0x9e8]
    // 0xc0bc70: stp             x0, x16, [SP]
    // 0xc0bc74: ldur            x1, [fp, #-0x40]
    // 0xc0bc78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0bc78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0bc7c: ldr             x4, [x4, #0xaa0]
    // 0xc0bc80: r0 = copyWith()
    //     0xc0bc80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0bc84: stur            x0, [fp, #-0x40]
    // 0xc0bc88: r0 = Text()
    //     0xc0bc88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0bc8c: mov             x1, x0
    // 0xc0bc90: ldur            x0, [fp, #-0x28]
    // 0xc0bc94: stur            x1, [fp, #-0x48]
    // 0xc0bc98: StoreField: r1->field_b = r0
    //     0xc0bc98: stur            w0, [x1, #0xb]
    // 0xc0bc9c: ldur            x0, [fp, #-0x40]
    // 0xc0bca0: StoreField: r1->field_13 = r0
    //     0xc0bca0: stur            w0, [x1, #0x13]
    // 0xc0bca4: r0 = Padding()
    //     0xc0bca4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0bca8: mov             x1, x0
    // 0xc0bcac: r0 = Instance_EdgeInsets
    //     0xc0bcac: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc0bcb0: ldr             x0, [x0, #0x668]
    // 0xc0bcb4: stur            x1, [fp, #-0x28]
    // 0xc0bcb8: StoreField: r1->field_f = r0
    //     0xc0bcb8: stur            w0, [x1, #0xf]
    // 0xc0bcbc: ldur            x0, [fp, #-0x48]
    // 0xc0bcc0: StoreField: r1->field_b = r0
    //     0xc0bcc0: stur            w0, [x1, #0xb]
    // 0xc0bcc4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc0bcc4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc0bcc8: ldr             x0, [x0, #0x1c80]
    //     0xc0bccc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc0bcd0: cmp             w0, w16
    //     0xc0bcd4: b.ne            #0xc0bce0
    //     0xc0bcd8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc0bcdc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc0bce0: r0 = GetNavigation.size()
    //     0xc0bce0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xc0bce4: LoadField: d0 = r0->field_7
    //     0xc0bce4: ldur            d0, [x0, #7]
    // 0xc0bce8: ldur            x1, [fp, #-0x10]
    // 0xc0bcec: stur            d0, [fp, #-0x58]
    // 0xc0bcf0: r0 = of()
    //     0xc0bcf0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0bcf4: LoadField: r1 = r0->field_5b
    //     0xc0bcf4: ldur            w1, [x0, #0x5b]
    // 0xc0bcf8: DecompressPointer r1
    //     0xc0bcf8: add             x1, x1, HEAP, lsl #32
    // 0xc0bcfc: r0 = LoadClassIdInstr(r1)
    //     0xc0bcfc: ldur            x0, [x1, #-1]
    //     0xc0bd00: ubfx            x0, x0, #0xc, #0x14
    // 0xc0bd04: d0 = 0.100000
    //     0xc0bd04: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0bd08: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc0bd08: sub             lr, x0, #0xffa
    //     0xc0bd0c: ldr             lr, [x21, lr, lsl #3]
    //     0xc0bd10: blr             lr
    // 0xc0bd14: r16 = 1.000000
    //     0xc0bd14: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc0bd18: str             x16, [SP]
    // 0xc0bd1c: mov             x2, x0
    // 0xc0bd20: r1 = Null
    //     0xc0bd20: mov             x1, NULL
    // 0xc0bd24: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xc0bd24: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xc0bd28: ldr             x4, [x4, #0x108]
    // 0xc0bd2c: r0 = Border.all()
    //     0xc0bd2c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc0bd30: stur            x0, [fp, #-0x40]
    // 0xc0bd34: r0 = BoxDecoration()
    //     0xc0bd34: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc0bd38: mov             x1, x0
    // 0xc0bd3c: ldur            x0, [fp, #-0x40]
    // 0xc0bd40: stur            x1, [fp, #-0x48]
    // 0xc0bd44: StoreField: r1->field_f = r0
    //     0xc0bd44: stur            w0, [x1, #0xf]
    // 0xc0bd48: r0 = Instance_BoxShape
    //     0xc0bd48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0bd4c: ldr             x0, [x0, #0x80]
    // 0xc0bd50: StoreField: r1->field_23 = r0
    //     0xc0bd50: stur            w0, [x1, #0x23]
    // 0xc0bd54: r0 = SvgPicture()
    //     0xc0bd54: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc0bd58: mov             x1, x0
    // 0xc0bd5c: r2 = "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xc0bd5c: add             x2, PP, #0x37, lsl #12  ; [pp+0x37140] "assets/images/whatsapp_icon_seeklogo.svg"
    //     0xc0bd60: ldr             x2, [x2, #0x140]
    // 0xc0bd64: stur            x0, [fp, #-0x40]
    // 0xc0bd68: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0bd68: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0bd6c: r0 = SvgPicture.asset()
    //     0xc0bd6c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc0bd70: r0 = Align()
    //     0xc0bd70: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc0bd74: mov             x1, x0
    // 0xc0bd78: r0 = Instance_Alignment
    //     0xc0bd78: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0bd7c: ldr             x0, [x0, #0xb10]
    // 0xc0bd80: stur            x1, [fp, #-0x50]
    // 0xc0bd84: StoreField: r1->field_f = r0
    //     0xc0bd84: stur            w0, [x1, #0xf]
    // 0xc0bd88: r0 = 1.000000
    //     0xc0bd88: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc0bd8c: StoreField: r1->field_13 = r0
    //     0xc0bd8c: stur            w0, [x1, #0x13]
    // 0xc0bd90: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0bd90: stur            w0, [x1, #0x17]
    // 0xc0bd94: ldur            x0, [fp, #-0x40]
    // 0xc0bd98: StoreField: r1->field_b = r0
    //     0xc0bd98: stur            w0, [x1, #0xb]
    // 0xc0bd9c: ldur            x0, [fp, #-8]
    // 0xc0bda0: LoadField: r2 = r0->field_b
    //     0xc0bda0: ldur            w2, [x0, #0xb]
    // 0xc0bda4: DecompressPointer r2
    //     0xc0bda4: add             x2, x2, HEAP, lsl #32
    // 0xc0bda8: cmp             w2, NULL
    // 0xc0bdac: b.eq            #0xc0c1e0
    // 0xc0bdb0: LoadField: r0 = r2->field_b
    //     0xc0bdb0: ldur            w0, [x2, #0xb]
    // 0xc0bdb4: DecompressPointer r0
    //     0xc0bdb4: add             x0, x0, HEAP, lsl #32
    // 0xc0bdb8: LoadField: r2 = r0->field_77
    //     0xc0bdb8: ldur            w2, [x0, #0x77]
    // 0xc0bdbc: DecompressPointer r2
    //     0xc0bdbc: add             x2, x2, HEAP, lsl #32
    // 0xc0bdc0: cmp             w2, NULL
    // 0xc0bdc4: b.ne            #0xc0bdd0
    // 0xc0bdc8: r0 = Null
    //     0xc0bdc8: mov             x0, NULL
    // 0xc0bdcc: b               #0xc0bde8
    // 0xc0bdd0: r0 = LoadClassIdInstr(r2)
    //     0xc0bdd0: ldur            x0, [x2, #-1]
    //     0xc0bdd4: ubfx            x0, x0, #0xc, #0x14
    // 0xc0bdd8: str             x2, [SP]
    // 0xc0bddc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc0bddc: sub             lr, x0, #1, lsl #12
    //     0xc0bde0: ldr             lr, [x21, lr, lsl #3]
    //     0xc0bde4: blr             lr
    // 0xc0bde8: cmp             w0, NULL
    // 0xc0bdec: b.ne            #0xc0bdf8
    // 0xc0bdf0: r5 = ""
    //     0xc0bdf0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0bdf4: b               #0xc0bdfc
    // 0xc0bdf8: mov             x5, x0
    // 0xc0bdfc: ldur            x4, [fp, #-0x18]
    // 0xc0be00: ldur            x3, [fp, #-0x38]
    // 0xc0be04: ldur            x2, [fp, #-0x28]
    // 0xc0be08: ldur            x0, [fp, #-0x50]
    // 0xc0be0c: ldur            d0, [fp, #-0x58]
    // 0xc0be10: ldur            x1, [fp, #-0x10]
    // 0xc0be14: stur            x5, [fp, #-8]
    // 0xc0be18: r0 = of()
    //     0xc0be18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0be1c: LoadField: r1 = r0->field_87
    //     0xc0be1c: ldur            w1, [x0, #0x87]
    // 0xc0be20: DecompressPointer r1
    //     0xc0be20: add             x1, x1, HEAP, lsl #32
    // 0xc0be24: LoadField: r0 = r1->field_7
    //     0xc0be24: ldur            w0, [x1, #7]
    // 0xc0be28: DecompressPointer r0
    //     0xc0be28: add             x0, x0, HEAP, lsl #32
    // 0xc0be2c: ldur            x1, [fp, #-0x10]
    // 0xc0be30: stur            x0, [fp, #-0x40]
    // 0xc0be34: r0 = of()
    //     0xc0be34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0be38: LoadField: r1 = r0->field_5b
    //     0xc0be38: ldur            w1, [x0, #0x5b]
    // 0xc0be3c: DecompressPointer r1
    //     0xc0be3c: add             x1, x1, HEAP, lsl #32
    // 0xc0be40: r16 = 14.000000
    //     0xc0be40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc0be44: ldr             x16, [x16, #0x1d8]
    // 0xc0be48: stp             x16, x1, [SP]
    // 0xc0be4c: ldur            x1, [fp, #-0x40]
    // 0xc0be50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc0be50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc0be54: ldr             x4, [x4, #0x9b8]
    // 0xc0be58: r0 = copyWith()
    //     0xc0be58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0be5c: stur            x0, [fp, #-0x10]
    // 0xc0be60: r0 = Text()
    //     0xc0be60: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0be64: mov             x1, x0
    // 0xc0be68: ldur            x0, [fp, #-8]
    // 0xc0be6c: stur            x1, [fp, #-0x40]
    // 0xc0be70: StoreField: r1->field_b = r0
    //     0xc0be70: stur            w0, [x1, #0xb]
    // 0xc0be74: ldur            x0, [fp, #-0x10]
    // 0xc0be78: StoreField: r1->field_13 = r0
    //     0xc0be78: stur            w0, [x1, #0x13]
    // 0xc0be7c: r0 = InkWell()
    //     0xc0be7c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc0be80: mov             x3, x0
    // 0xc0be84: ldur            x0, [fp, #-0x40]
    // 0xc0be88: stur            x3, [fp, #-8]
    // 0xc0be8c: StoreField: r3->field_b = r0
    //     0xc0be8c: stur            w0, [x3, #0xb]
    // 0xc0be90: ldur            x2, [fp, #-0x20]
    // 0xc0be94: r1 = Function '<anonymous closure>':.
    //     0xc0be94: add             x1, PP, #0x52, lsl #12  ; [pp+0x52ea8] AnonymousClosure: (0xa8e78c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reply_to_seller_item_view.dart] _ReplyToSellerItemViewState::build (0xc0b9f8)
    //     0xc0be98: ldr             x1, [x1, #0xea8]
    // 0xc0be9c: r0 = AllocateClosure()
    //     0xc0be9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0bea0: mov             x1, x0
    // 0xc0bea4: ldur            x0, [fp, #-8]
    // 0xc0bea8: StoreField: r0->field_f = r1
    //     0xc0bea8: stur            w1, [x0, #0xf]
    // 0xc0beac: r3 = true
    //     0xc0beac: add             x3, NULL, #0x20  ; true
    // 0xc0beb0: StoreField: r0->field_43 = r3
    //     0xc0beb0: stur            w3, [x0, #0x43]
    // 0xc0beb4: r4 = Instance_BoxShape
    //     0xc0beb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0beb8: ldr             x4, [x4, #0x80]
    // 0xc0bebc: StoreField: r0->field_47 = r4
    //     0xc0bebc: stur            w4, [x0, #0x47]
    // 0xc0bec0: StoreField: r0->field_6f = r3
    //     0xc0bec0: stur            w3, [x0, #0x6f]
    // 0xc0bec4: r5 = false
    //     0xc0bec4: add             x5, NULL, #0x30  ; false
    // 0xc0bec8: StoreField: r0->field_73 = r5
    //     0xc0bec8: stur            w5, [x0, #0x73]
    // 0xc0becc: StoreField: r0->field_83 = r3
    //     0xc0becc: stur            w3, [x0, #0x83]
    // 0xc0bed0: StoreField: r0->field_7b = r5
    //     0xc0bed0: stur            w5, [x0, #0x7b]
    // 0xc0bed4: r1 = Null
    //     0xc0bed4: mov             x1, NULL
    // 0xc0bed8: r2 = 6
    //     0xc0bed8: movz            x2, #0x6
    // 0xc0bedc: r0 = AllocateArray()
    //     0xc0bedc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0bee0: mov             x2, x0
    // 0xc0bee4: ldur            x0, [fp, #-0x50]
    // 0xc0bee8: stur            x2, [fp, #-0x10]
    // 0xc0beec: StoreField: r2->field_f = r0
    //     0xc0beec: stur            w0, [x2, #0xf]
    // 0xc0bef0: r16 = Instance_SizedBox
    //     0xc0bef0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaa8] Obj!SizedBox@d67f41
    //     0xc0bef4: ldr             x16, [x16, #0xaa8]
    // 0xc0bef8: StoreField: r2->field_13 = r16
    //     0xc0bef8: stur            w16, [x2, #0x13]
    // 0xc0befc: ldur            x0, [fp, #-8]
    // 0xc0bf00: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0bf00: stur            w0, [x2, #0x17]
    // 0xc0bf04: r1 = <Widget>
    //     0xc0bf04: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0bf08: r0 = AllocateGrowableArray()
    //     0xc0bf08: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0bf0c: mov             x1, x0
    // 0xc0bf10: ldur            x0, [fp, #-0x10]
    // 0xc0bf14: stur            x1, [fp, #-8]
    // 0xc0bf18: StoreField: r1->field_f = r0
    //     0xc0bf18: stur            w0, [x1, #0xf]
    // 0xc0bf1c: r2 = 6
    //     0xc0bf1c: movz            x2, #0x6
    // 0xc0bf20: StoreField: r1->field_b = r2
    //     0xc0bf20: stur            w2, [x1, #0xb]
    // 0xc0bf24: r0 = Row()
    //     0xc0bf24: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc0bf28: mov             x1, x0
    // 0xc0bf2c: r0 = Instance_Axis
    //     0xc0bf2c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc0bf30: stur            x1, [fp, #-0x10]
    // 0xc0bf34: StoreField: r1->field_f = r0
    //     0xc0bf34: stur            w0, [x1, #0xf]
    // 0xc0bf38: r0 = Instance_MainAxisAlignment
    //     0xc0bf38: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xc0bf3c: ldr             x0, [x0, #0xab0]
    // 0xc0bf40: StoreField: r1->field_13 = r0
    //     0xc0bf40: stur            w0, [x1, #0x13]
    // 0xc0bf44: r0 = Instance_MainAxisSize
    //     0xc0bf44: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0bf48: ldr             x0, [x0, #0xa10]
    // 0xc0bf4c: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0bf4c: stur            w0, [x1, #0x17]
    // 0xc0bf50: r2 = Instance_CrossAxisAlignment
    //     0xc0bf50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0bf54: ldr             x2, [x2, #0xa18]
    // 0xc0bf58: StoreField: r1->field_1b = r2
    //     0xc0bf58: stur            w2, [x1, #0x1b]
    // 0xc0bf5c: r3 = Instance_VerticalDirection
    //     0xc0bf5c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0bf60: ldr             x3, [x3, #0xa20]
    // 0xc0bf64: StoreField: r1->field_23 = r3
    //     0xc0bf64: stur            w3, [x1, #0x23]
    // 0xc0bf68: r4 = Instance_Clip
    //     0xc0bf68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0bf6c: ldr             x4, [x4, #0x38]
    // 0xc0bf70: StoreField: r1->field_2b = r4
    //     0xc0bf70: stur            w4, [x1, #0x2b]
    // 0xc0bf74: StoreField: r1->field_2f = rZR
    //     0xc0bf74: stur            xzr, [x1, #0x2f]
    // 0xc0bf78: ldur            x5, [fp, #-8]
    // 0xc0bf7c: StoreField: r1->field_b = r5
    //     0xc0bf7c: stur            w5, [x1, #0xb]
    // 0xc0bf80: ldur            d0, [fp, #-0x58]
    // 0xc0bf84: r5 = inline_Allocate_Double()
    //     0xc0bf84: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xc0bf88: add             x5, x5, #0x10
    //     0xc0bf8c: cmp             x6, x5
    //     0xc0bf90: b.ls            #0xc0c1e4
    //     0xc0bf94: str             x5, [THR, #0x50]  ; THR::top
    //     0xc0bf98: sub             x5, x5, #0xf
    //     0xc0bf9c: movz            x6, #0xe15c
    //     0xc0bfa0: movk            x6, #0x3, lsl #16
    //     0xc0bfa4: stur            x6, [x5, #-1]
    // 0xc0bfa8: StoreField: r5->field_7 = d0
    //     0xc0bfa8: stur            d0, [x5, #7]
    // 0xc0bfac: stur            x5, [fp, #-8]
    // 0xc0bfb0: r0 = Container()
    //     0xc0bfb0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0bfb4: stur            x0, [fp, #-0x40]
    // 0xc0bfb8: ldur            x16, [fp, #-8]
    // 0xc0bfbc: r30 = 44.000000
    //     0xc0bfbc: add             lr, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xc0bfc0: ldr             lr, [lr, #0xad8]
    // 0xc0bfc4: stp             lr, x16, [SP, #0x18]
    // 0xc0bfc8: r16 = Instance_Alignment
    //     0xc0bfc8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0bfcc: ldr             x16, [x16, #0xb10]
    // 0xc0bfd0: ldur            lr, [fp, #-0x48]
    // 0xc0bfd4: stp             lr, x16, [SP, #8]
    // 0xc0bfd8: ldur            x16, [fp, #-0x10]
    // 0xc0bfdc: str             x16, [SP]
    // 0xc0bfe0: mov             x1, x0
    // 0xc0bfe4: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x3, child, 0x5, decoration, 0x4, height, 0x2, width, 0x1, null]
    //     0xc0bfe4: add             x4, PP, #0x52, lsl #12  ; [pp+0x52eb0] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x3, "child", 0x5, "decoration", 0x4, "height", 0x2, "width", 0x1, Null]
    //     0xc0bfe8: ldr             x4, [x4, #0xeb0]
    // 0xc0bfec: r0 = Container()
    //     0xc0bfec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0bff0: r0 = InkWell()
    //     0xc0bff0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc0bff4: mov             x3, x0
    // 0xc0bff8: ldur            x0, [fp, #-0x40]
    // 0xc0bffc: stur            x3, [fp, #-8]
    // 0xc0c000: StoreField: r3->field_b = r0
    //     0xc0c000: stur            w0, [x3, #0xb]
    // 0xc0c004: ldur            x2, [fp, #-0x20]
    // 0xc0c008: r1 = Function '<anonymous closure>':.
    //     0xc0c008: add             x1, PP, #0x52, lsl #12  ; [pp+0x52eb8] AnonymousClosure: (0xa8e78c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reply_to_seller_item_view.dart] _ReplyToSellerItemViewState::build (0xc0b9f8)
    //     0xc0c00c: ldr             x1, [x1, #0xeb8]
    // 0xc0c010: r0 = AllocateClosure()
    //     0xc0c010: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0c014: mov             x1, x0
    // 0xc0c018: ldur            x0, [fp, #-8]
    // 0xc0c01c: StoreField: r0->field_f = r1
    //     0xc0c01c: stur            w1, [x0, #0xf]
    // 0xc0c020: r1 = true
    //     0xc0c020: add             x1, NULL, #0x20  ; true
    // 0xc0c024: StoreField: r0->field_43 = r1
    //     0xc0c024: stur            w1, [x0, #0x43]
    // 0xc0c028: r2 = Instance_BoxShape
    //     0xc0c028: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0c02c: ldr             x2, [x2, #0x80]
    // 0xc0c030: StoreField: r0->field_47 = r2
    //     0xc0c030: stur            w2, [x0, #0x47]
    // 0xc0c034: StoreField: r0->field_6f = r1
    //     0xc0c034: stur            w1, [x0, #0x6f]
    // 0xc0c038: r2 = false
    //     0xc0c038: add             x2, NULL, #0x30  ; false
    // 0xc0c03c: StoreField: r0->field_73 = r2
    //     0xc0c03c: stur            w2, [x0, #0x73]
    // 0xc0c040: StoreField: r0->field_83 = r1
    //     0xc0c040: stur            w1, [x0, #0x83]
    // 0xc0c044: StoreField: r0->field_7b = r2
    //     0xc0c044: stur            w2, [x0, #0x7b]
    // 0xc0c048: r0 = Padding()
    //     0xc0c048: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0c04c: mov             x3, x0
    // 0xc0c050: r0 = Instance_EdgeInsets
    //     0xc0c050: add             x0, PP, #0x52, lsl #12  ; [pp+0x52ec0] Obj!EdgeInsets@d58a31
    //     0xc0c054: ldr             x0, [x0, #0xec0]
    // 0xc0c058: stur            x3, [fp, #-0x10]
    // 0xc0c05c: StoreField: r3->field_f = r0
    //     0xc0c05c: stur            w0, [x3, #0xf]
    // 0xc0c060: ldur            x0, [fp, #-8]
    // 0xc0c064: StoreField: r3->field_b = r0
    //     0xc0c064: stur            w0, [x3, #0xb]
    // 0xc0c068: r1 = Null
    //     0xc0c068: mov             x1, NULL
    // 0xc0c06c: r2 = 6
    //     0xc0c06c: movz            x2, #0x6
    // 0xc0c070: r0 = AllocateArray()
    //     0xc0c070: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc0c074: mov             x2, x0
    // 0xc0c078: ldur            x0, [fp, #-0x38]
    // 0xc0c07c: stur            x2, [fp, #-8]
    // 0xc0c080: StoreField: r2->field_f = r0
    //     0xc0c080: stur            w0, [x2, #0xf]
    // 0xc0c084: ldur            x0, [fp, #-0x28]
    // 0xc0c088: StoreField: r2->field_13 = r0
    //     0xc0c088: stur            w0, [x2, #0x13]
    // 0xc0c08c: ldur            x0, [fp, #-0x10]
    // 0xc0c090: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0c090: stur            w0, [x2, #0x17]
    // 0xc0c094: r1 = <Widget>
    //     0xc0c094: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0c098: r0 = AllocateGrowableArray()
    //     0xc0c098: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc0c09c: mov             x1, x0
    // 0xc0c0a0: ldur            x0, [fp, #-8]
    // 0xc0c0a4: stur            x1, [fp, #-0x10]
    // 0xc0c0a8: StoreField: r1->field_f = r0
    //     0xc0c0a8: stur            w0, [x1, #0xf]
    // 0xc0c0ac: r0 = 6
    //     0xc0c0ac: movz            x0, #0x6
    // 0xc0c0b0: StoreField: r1->field_b = r0
    //     0xc0c0b0: stur            w0, [x1, #0xb]
    // 0xc0c0b4: r0 = Column()
    //     0xc0c0b4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0c0b8: mov             x1, x0
    // 0xc0c0bc: r0 = Instance_Axis
    //     0xc0c0bc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0c0c0: stur            x1, [fp, #-8]
    // 0xc0c0c4: StoreField: r1->field_f = r0
    //     0xc0c0c4: stur            w0, [x1, #0xf]
    // 0xc0c0c8: r0 = Instance_MainAxisAlignment
    //     0xc0c0c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0c0cc: ldr             x0, [x0, #0xa08]
    // 0xc0c0d0: StoreField: r1->field_13 = r0
    //     0xc0c0d0: stur            w0, [x1, #0x13]
    // 0xc0c0d4: r0 = Instance_MainAxisSize
    //     0xc0c0d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0c0d8: ldr             x0, [x0, #0xa10]
    // 0xc0c0dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0c0dc: stur            w0, [x1, #0x17]
    // 0xc0c0e0: r0 = Instance_CrossAxisAlignment
    //     0xc0c0e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc0c0e4: ldr             x0, [x0, #0xa18]
    // 0xc0c0e8: StoreField: r1->field_1b = r0
    //     0xc0c0e8: stur            w0, [x1, #0x1b]
    // 0xc0c0ec: r0 = Instance_VerticalDirection
    //     0xc0c0ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0c0f0: ldr             x0, [x0, #0xa20]
    // 0xc0c0f4: StoreField: r1->field_23 = r0
    //     0xc0c0f4: stur            w0, [x1, #0x23]
    // 0xc0c0f8: r0 = Instance_Clip
    //     0xc0c0f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0c0fc: ldr             x0, [x0, #0x38]
    // 0xc0c100: StoreField: r1->field_2b = r0
    //     0xc0c100: stur            w0, [x1, #0x2b]
    // 0xc0c104: StoreField: r1->field_2f = rZR
    //     0xc0c104: stur            xzr, [x1, #0x2f]
    // 0xc0c108: ldur            x0, [fp, #-0x10]
    // 0xc0c10c: StoreField: r1->field_b = r0
    //     0xc0c10c: stur            w0, [x1, #0xb]
    // 0xc0c110: r0 = Container()
    //     0xc0c110: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0c114: stur            x0, [fp, #-0x10]
    // 0xc0c118: r16 = inf
    //     0xc0c118: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc0c11c: ldr             x16, [x16, #0x9f8]
    // 0xc0c120: r30 = 158.000000
    //     0xc0c120: add             lr, PP, #0x52, lsl #12  ; [pp+0x52ec8] 158
    //     0xc0c124: ldr             lr, [lr, #0xec8]
    // 0xc0c128: stp             lr, x16, [SP, #0x10]
    // 0xc0c12c: ldur            x16, [fp, #-0x30]
    // 0xc0c130: ldur            lr, [fp, #-8]
    // 0xc0c134: stp             lr, x16, [SP]
    // 0xc0c138: mov             x1, x0
    // 0xc0c13c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xc0c13c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xc0c140: ldr             x4, [x4, #0x870]
    // 0xc0c144: r0 = Container()
    //     0xc0c144: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0c148: r0 = Padding()
    //     0xc0c148: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0c14c: mov             x1, x0
    // 0xc0c150: r0 = Instance_EdgeInsets
    //     0xc0c150: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xc0c154: ldr             x0, [x0, #0x668]
    // 0xc0c158: stur            x1, [fp, #-8]
    // 0xc0c15c: StoreField: r1->field_f = r0
    //     0xc0c15c: stur            w0, [x1, #0xf]
    // 0xc0c160: ldur            x0, [fp, #-0x10]
    // 0xc0c164: StoreField: r1->field_b = r0
    //     0xc0c164: stur            w0, [x1, #0xb]
    // 0xc0c168: r0 = Visibility()
    //     0xc0c168: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc0c16c: mov             x1, x0
    // 0xc0c170: ldur            x0, [fp, #-8]
    // 0xc0c174: stur            x1, [fp, #-0x10]
    // 0xc0c178: StoreField: r1->field_b = r0
    //     0xc0c178: stur            w0, [x1, #0xb]
    // 0xc0c17c: r0 = Instance_SizedBox
    //     0xc0c17c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc0c180: StoreField: r1->field_f = r0
    //     0xc0c180: stur            w0, [x1, #0xf]
    // 0xc0c184: ldur            x0, [fp, #-0x18]
    // 0xc0c188: StoreField: r1->field_13 = r0
    //     0xc0c188: stur            w0, [x1, #0x13]
    // 0xc0c18c: r0 = false
    //     0xc0c18c: add             x0, NULL, #0x30  ; false
    // 0xc0c190: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0c190: stur            w0, [x1, #0x17]
    // 0xc0c194: StoreField: r1->field_1b = r0
    //     0xc0c194: stur            w0, [x1, #0x1b]
    // 0xc0c198: StoreField: r1->field_1f = r0
    //     0xc0c198: stur            w0, [x1, #0x1f]
    // 0xc0c19c: StoreField: r1->field_23 = r0
    //     0xc0c19c: stur            w0, [x1, #0x23]
    // 0xc0c1a0: StoreField: r1->field_27 = r0
    //     0xc0c1a0: stur            w0, [x1, #0x27]
    // 0xc0c1a4: StoreField: r1->field_2b = r0
    //     0xc0c1a4: stur            w0, [x1, #0x2b]
    // 0xc0c1a8: r0 = Padding()
    //     0xc0c1a8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0c1ac: r1 = Instance_EdgeInsets
    //     0xc0c1ac: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xc0c1b0: ldr             x1, [x1, #0x240]
    // 0xc0c1b4: StoreField: r0->field_f = r1
    //     0xc0c1b4: stur            w1, [x0, #0xf]
    // 0xc0c1b8: ldur            x1, [fp, #-0x10]
    // 0xc0c1bc: StoreField: r0->field_b = r1
    //     0xc0c1bc: stur            w1, [x0, #0xb]
    // 0xc0c1c0: LeaveFrame
    //     0xc0c1c0: mov             SP, fp
    //     0xc0c1c4: ldp             fp, lr, [SP], #0x10
    // 0xc0c1c8: ret
    //     0xc0c1c8: ret             
    // 0xc0c1cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0c1cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0c1d0: b               #0xc0ba20
    // 0xc0c1d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c1d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0c1d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c1d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0c1dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c1dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0c1e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0c1e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0c1e4: SaveReg d0
    //     0xc0c1e4: str             q0, [SP, #-0x10]!
    // 0xc0c1e8: stp             x3, x4, [SP, #-0x10]!
    // 0xc0c1ec: stp             x1, x2, [SP, #-0x10]!
    // 0xc0c1f0: SaveReg r0
    //     0xc0c1f0: str             x0, [SP, #-8]!
    // 0xc0c1f4: r0 = AllocateDouble()
    //     0xc0c1f4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xc0c1f8: mov             x5, x0
    // 0xc0c1fc: RestoreReg r0
    //     0xc0c1fc: ldr             x0, [SP], #8
    // 0xc0c200: ldp             x1, x2, [SP], #0x10
    // 0xc0c204: ldp             x3, x4, [SP], #0x10
    // 0xc0c208: RestoreReg d0
    //     0xc0c208: ldr             q0, [SP], #0x10
    // 0xc0c20c: b               #0xc0bfa8
  }
}

// class id: 3965, size: 0x14, field offset: 0xc
//   const constructor, 
class ReplyToSellerItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc811bc, size: 0x24
    // 0xc811bc: EnterFrame
    //     0xc811bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc811c0: mov             fp, SP
    // 0xc811c4: mov             x0, x1
    // 0xc811c8: r1 = <ReplyToSellerItemView>
    //     0xc811c8: add             x1, PP, #0x48, lsl #12  ; [pp+0x483d0] TypeArguments: <ReplyToSellerItemView>
    //     0xc811cc: ldr             x1, [x1, #0x3d0]
    // 0xc811d0: r0 = _ReplyToSellerItemViewState()
    //     0xc811d0: bl              #0xc811e0  ; Allocate_ReplyToSellerItemViewStateStub -> _ReplyToSellerItemViewState (size=0x14)
    // 0xc811d4: LeaveFrame
    //     0xc811d4: mov             SP, fp
    //     0xc811d8: ldp             fp, lr, [SP], #0x10
    // 0xc811dc: ret
    //     0xc811dc: ret             
  }
}
