// lib: , url: package:customer_app/app/presentation/custom_widgets/home/<USER>

// class id: 1049068, size: 0x8
class :: {
}

// class id: 3590, size: 0x14, field offset: 0x14
class _WebsiteNavigatorWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x98ea98, size: 0x760
    // 0x98ea98: EnterFrame
    //     0x98ea98: stp             fp, lr, [SP, #-0x10]!
    //     0x98ea9c: mov             fp, SP
    // 0x98eaa0: AllocStack(0x68)
    //     0x98eaa0: sub             SP, SP, #0x68
    // 0x98eaa4: SetupParameters(_WebsiteNavigatorWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x98eaa4: mov             x0, x1
    //     0x98eaa8: stur            x1, [fp, #-8]
    //     0x98eaac: mov             x1, x2
    //     0x98eab0: stur            x2, [fp, #-0x10]
    // 0x98eab4: CheckStackOverflow
    //     0x98eab4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98eab8: cmp             SP, x16
    //     0x98eabc: b.ls            #0x98f1d8
    // 0x98eac0: r1 = 1
    //     0x98eac0: movz            x1, #0x1
    // 0x98eac4: r0 = AllocateContext()
    //     0x98eac4: bl              #0x16f6108  ; AllocateContextStub
    // 0x98eac8: mov             x3, x0
    // 0x98eacc: ldur            x0, [fp, #-8]
    // 0x98ead0: stur            x3, [fp, #-0x28]
    // 0x98ead4: StoreField: r3->field_f = r0
    //     0x98ead4: stur            w0, [x3, #0xf]
    // 0x98ead8: LoadField: r1 = r0->field_b
    //     0x98ead8: ldur            w1, [x0, #0xb]
    // 0x98eadc: DecompressPointer r1
    //     0x98eadc: add             x1, x1, HEAP, lsl #32
    // 0x98eae0: cmp             w1, NULL
    // 0x98eae4: b.eq            #0x98f1e0
    // 0x98eae8: LoadField: r4 = r1->field_2f
    //     0x98eae8: ldur            w4, [x1, #0x2f]
    // 0x98eaec: DecompressPointer r4
    //     0x98eaec: add             x4, x4, HEAP, lsl #32
    // 0x98eaf0: stur            x4, [fp, #-0x20]
    // 0x98eaf4: LoadField: r5 = r1->field_37
    //     0x98eaf4: ldur            w5, [x1, #0x37]
    // 0x98eaf8: DecompressPointer r5
    //     0x98eaf8: add             x5, x5, HEAP, lsl #32
    // 0x98eafc: stur            x5, [fp, #-0x18]
    // 0x98eb00: LoadField: r2 = r1->field_3b
    //     0x98eb00: ldur            w2, [x1, #0x3b]
    // 0x98eb04: DecompressPointer r2
    //     0x98eb04: add             x2, x2, HEAP, lsl #32
    // 0x98eb08: r1 = Null
    //     0x98eb08: mov             x1, NULL
    // 0x98eb0c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x98eb0c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x98eb10: r0 = Border.all()
    //     0x98eb10: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x98eb14: stur            x0, [fp, #-0x30]
    // 0x98eb18: r0 = BoxDecoration()
    //     0x98eb18: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x98eb1c: mov             x2, x0
    // 0x98eb20: ldur            x0, [fp, #-0x20]
    // 0x98eb24: stur            x2, [fp, #-0x38]
    // 0x98eb28: StoreField: r2->field_7 = r0
    //     0x98eb28: stur            w0, [x2, #7]
    // 0x98eb2c: ldur            x0, [fp, #-0x30]
    // 0x98eb30: StoreField: r2->field_f = r0
    //     0x98eb30: stur            w0, [x2, #0xf]
    // 0x98eb34: ldur            x0, [fp, #-0x18]
    // 0x98eb38: StoreField: r2->field_13 = r0
    //     0x98eb38: stur            w0, [x2, #0x13]
    // 0x98eb3c: r0 = Instance_BoxShape
    //     0x98eb3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x98eb40: ldr             x0, [x0, #0x80]
    // 0x98eb44: StoreField: r2->field_23 = r0
    //     0x98eb44: stur            w0, [x2, #0x23]
    // 0x98eb48: ldur            x3, [fp, #-8]
    // 0x98eb4c: LoadField: r1 = r3->field_b
    //     0x98eb4c: ldur            w1, [x3, #0xb]
    // 0x98eb50: DecompressPointer r1
    //     0x98eb50: add             x1, x1, HEAP, lsl #32
    // 0x98eb54: cmp             w1, NULL
    // 0x98eb58: b.eq            #0x98f1e4
    // 0x98eb5c: LoadField: r4 = r1->field_f
    //     0x98eb5c: ldur            w4, [x1, #0xf]
    // 0x98eb60: DecompressPointer r4
    //     0x98eb60: add             x4, x4, HEAP, lsl #32
    // 0x98eb64: cmp             w4, NULL
    // 0x98eb68: b.ne            #0x98eb74
    // 0x98eb6c: r1 = Null
    //     0x98eb6c: mov             x1, NULL
    // 0x98eb70: b               #0x98eb7c
    // 0x98eb74: LoadField: r1 = r4->field_7
    //     0x98eb74: ldur            w1, [x4, #7]
    // 0x98eb78: DecompressPointer r1
    //     0x98eb78: add             x1, x1, HEAP, lsl #32
    // 0x98eb7c: cmp             w1, NULL
    // 0x98eb80: b.ne            #0x98eb8c
    // 0x98eb84: r4 = ""
    //     0x98eb84: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98eb88: b               #0x98eb90
    // 0x98eb8c: mov             x4, x1
    // 0x98eb90: ldur            x1, [fp, #-0x10]
    // 0x98eb94: stur            x4, [fp, #-0x18]
    // 0x98eb98: r0 = of()
    //     0x98eb98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98eb9c: LoadField: r1 = r0->field_87
    //     0x98eb9c: ldur            w1, [x0, #0x87]
    // 0x98eba0: DecompressPointer r1
    //     0x98eba0: add             x1, x1, HEAP, lsl #32
    // 0x98eba4: LoadField: r0 = r1->field_27
    //     0x98eba4: ldur            w0, [x1, #0x27]
    // 0x98eba8: DecompressPointer r0
    //     0x98eba8: add             x0, x0, HEAP, lsl #32
    // 0x98ebac: stur            x0, [fp, #-0x20]
    // 0x98ebb0: r1 = Instance_Color
    //     0x98ebb0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x98ebb4: d0 = 0.700000
    //     0x98ebb4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98ebb8: ldr             d0, [x17, #0xf48]
    // 0x98ebbc: r0 = withOpacity()
    //     0x98ebbc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x98ebc0: r16 = 21.000000
    //     0x98ebc0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0x98ebc4: ldr             x16, [x16, #0x9b0]
    // 0x98ebc8: stp             x0, x16, [SP]
    // 0x98ebcc: ldur            x1, [fp, #-0x20]
    // 0x98ebd0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x98ebd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x98ebd4: ldr             x4, [x4, #0xaa0]
    // 0x98ebd8: r0 = copyWith()
    //     0x98ebd8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x98ebdc: stur            x0, [fp, #-0x20]
    // 0x98ebe0: r0 = Text()
    //     0x98ebe0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x98ebe4: mov             x1, x0
    // 0x98ebe8: ldur            x0, [fp, #-0x18]
    // 0x98ebec: stur            x1, [fp, #-0x30]
    // 0x98ebf0: StoreField: r1->field_b = r0
    //     0x98ebf0: stur            w0, [x1, #0xb]
    // 0x98ebf4: ldur            x0, [fp, #-0x20]
    // 0x98ebf8: StoreField: r1->field_13 = r0
    //     0x98ebf8: stur            w0, [x1, #0x13]
    // 0x98ebfc: r2 = 4
    //     0x98ebfc: movz            x2, #0x4
    // 0x98ec00: StoreField: r1->field_37 = r2
    //     0x98ec00: stur            w2, [x1, #0x37]
    // 0x98ec04: r0 = SizedBox()
    //     0x98ec04: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x98ec08: mov             x1, x0
    // 0x98ec0c: r0 = 220.000000
    //     0x98ec0c: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bb68] 220
    //     0x98ec10: ldr             x0, [x0, #0xb68]
    // 0x98ec14: stur            x1, [fp, #-0x20]
    // 0x98ec18: StoreField: r1->field_f = r0
    //     0x98ec18: stur            w0, [x1, #0xf]
    // 0x98ec1c: ldur            x0, [fp, #-0x30]
    // 0x98ec20: StoreField: r1->field_b = r0
    //     0x98ec20: stur            w0, [x1, #0xb]
    // 0x98ec24: ldur            x0, [fp, #-8]
    // 0x98ec28: LoadField: r2 = r0->field_b
    //     0x98ec28: ldur            w2, [x0, #0xb]
    // 0x98ec2c: DecompressPointer r2
    //     0x98ec2c: add             x2, x2, HEAP, lsl #32
    // 0x98ec30: cmp             w2, NULL
    // 0x98ec34: b.eq            #0x98f1e8
    // 0x98ec38: LoadField: r3 = r2->field_f
    //     0x98ec38: ldur            w3, [x2, #0xf]
    // 0x98ec3c: DecompressPointer r3
    //     0x98ec3c: add             x3, x3, HEAP, lsl #32
    // 0x98ec40: cmp             w3, NULL
    // 0x98ec44: b.ne            #0x98ec50
    // 0x98ec48: r2 = Null
    //     0x98ec48: mov             x2, NULL
    // 0x98ec4c: b               #0x98ec58
    // 0x98ec50: LoadField: r2 = r3->field_13
    //     0x98ec50: ldur            w2, [x3, #0x13]
    // 0x98ec54: DecompressPointer r2
    //     0x98ec54: add             x2, x2, HEAP, lsl #32
    // 0x98ec58: cmp             w2, NULL
    // 0x98ec5c: b.ne            #0x98ec64
    // 0x98ec60: r2 = ""
    //     0x98ec60: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98ec64: stur            x2, [fp, #-0x18]
    // 0x98ec68: r0 = Image()
    //     0x98ec68: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x98ec6c: stur            x0, [fp, #-0x30]
    // 0x98ec70: r16 = 45.000000
    //     0x98ec70: add             x16, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0x98ec74: ldr             x16, [x16, #0xc88]
    // 0x98ec78: r30 = 71.000000
    //     0x98ec78: add             lr, PP, #0x5b, lsl #12  ; [pp+0x5bb70] 71
    //     0x98ec7c: ldr             lr, [lr, #0xb70]
    // 0x98ec80: stp             lr, x16, [SP]
    // 0x98ec84: mov             x1, x0
    // 0x98ec88: ldur            x2, [fp, #-0x18]
    // 0x98ec8c: r4 = const [0, 0x4, 0x2, 0x2, height, 0x3, width, 0x2, null]
    //     0x98ec8c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53ed8] List(9) [0, 0x4, 0x2, 0x2, "height", 0x3, "width", 0x2, Null]
    //     0x98ec90: ldr             x4, [x4, #0xed8]
    // 0x98ec94: r0 = Image.network()
    //     0x98ec94: bl              #0x802090  ; [package:flutter/src/widgets/image.dart] Image::Image.network
    // 0x98ec98: r1 = Null
    //     0x98ec98: mov             x1, NULL
    // 0x98ec9c: r2 = 4
    //     0x98ec9c: movz            x2, #0x4
    // 0x98eca0: r0 = AllocateArray()
    //     0x98eca0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98eca4: mov             x2, x0
    // 0x98eca8: ldur            x0, [fp, #-0x20]
    // 0x98ecac: stur            x2, [fp, #-0x18]
    // 0x98ecb0: StoreField: r2->field_f = r0
    //     0x98ecb0: stur            w0, [x2, #0xf]
    // 0x98ecb4: ldur            x0, [fp, #-0x30]
    // 0x98ecb8: StoreField: r2->field_13 = r0
    //     0x98ecb8: stur            w0, [x2, #0x13]
    // 0x98ecbc: r1 = <Widget>
    //     0x98ecbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x98ecc0: r0 = AllocateGrowableArray()
    //     0x98ecc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98ecc4: mov             x1, x0
    // 0x98ecc8: ldur            x0, [fp, #-0x18]
    // 0x98eccc: stur            x1, [fp, #-0x20]
    // 0x98ecd0: StoreField: r1->field_f = r0
    //     0x98ecd0: stur            w0, [x1, #0xf]
    // 0x98ecd4: r2 = 4
    //     0x98ecd4: movz            x2, #0x4
    // 0x98ecd8: StoreField: r1->field_b = r2
    //     0x98ecd8: stur            w2, [x1, #0xb]
    // 0x98ecdc: r0 = Row()
    //     0x98ecdc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x98ece0: mov             x1, x0
    // 0x98ece4: r0 = Instance_Axis
    //     0x98ece4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x98ece8: stur            x1, [fp, #-0x18]
    // 0x98ecec: StoreField: r1->field_f = r0
    //     0x98ecec: stur            w0, [x1, #0xf]
    // 0x98ecf0: r0 = Instance_MainAxisAlignment
    //     0x98ecf0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x98ecf4: ldr             x0, [x0, #0xa8]
    // 0x98ecf8: StoreField: r1->field_13 = r0
    //     0x98ecf8: stur            w0, [x1, #0x13]
    // 0x98ecfc: r0 = Instance_MainAxisSize
    //     0x98ecfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x98ed00: ldr             x0, [x0, #0xa10]
    // 0x98ed04: ArrayStore: r1[0] = r0  ; List_4
    //     0x98ed04: stur            w0, [x1, #0x17]
    // 0x98ed08: r2 = Instance_CrossAxisAlignment
    //     0x98ed08: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x98ed0c: ldr             x2, [x2, #0xa18]
    // 0x98ed10: StoreField: r1->field_1b = r2
    //     0x98ed10: stur            w2, [x1, #0x1b]
    // 0x98ed14: r3 = Instance_VerticalDirection
    //     0x98ed14: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x98ed18: ldr             x3, [x3, #0xa20]
    // 0x98ed1c: StoreField: r1->field_23 = r3
    //     0x98ed1c: stur            w3, [x1, #0x23]
    // 0x98ed20: r4 = Instance_Clip
    //     0x98ed20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x98ed24: ldr             x4, [x4, #0x38]
    // 0x98ed28: StoreField: r1->field_2b = r4
    //     0x98ed28: stur            w4, [x1, #0x2b]
    // 0x98ed2c: StoreField: r1->field_2f = rZR
    //     0x98ed2c: stur            xzr, [x1, #0x2f]
    // 0x98ed30: ldur            x5, [fp, #-0x20]
    // 0x98ed34: StoreField: r1->field_b = r5
    //     0x98ed34: stur            w5, [x1, #0xb]
    // 0x98ed38: r0 = Padding()
    //     0x98ed38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x98ed3c: mov             x1, x0
    // 0x98ed40: r0 = Instance_EdgeInsets
    //     0x98ed40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0b0] Obj!EdgeInsets@d56f61
    //     0x98ed44: ldr             x0, [x0, #0xb0]
    // 0x98ed48: stur            x1, [fp, #-0x20]
    // 0x98ed4c: StoreField: r1->field_f = r0
    //     0x98ed4c: stur            w0, [x1, #0xf]
    // 0x98ed50: ldur            x0, [fp, #-0x18]
    // 0x98ed54: StoreField: r1->field_b = r0
    //     0x98ed54: stur            w0, [x1, #0xb]
    // 0x98ed58: r16 = <EdgeInsets>
    //     0x98ed58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x98ed5c: ldr             x16, [x16, #0xda0]
    // 0x98ed60: r30 = Instance_EdgeInsets
    //     0x98ed60: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x98ed64: ldr             lr, [lr, #0x1f0]
    // 0x98ed68: stp             lr, x16, [SP]
    // 0x98ed6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x98ed6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x98ed70: r0 = all()
    //     0x98ed70: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x98ed74: mov             x1, x0
    // 0x98ed78: ldur            x0, [fp, #-8]
    // 0x98ed7c: stur            x1, [fp, #-0x18]
    // 0x98ed80: LoadField: r2 = r0->field_b
    //     0x98ed80: ldur            w2, [x0, #0xb]
    // 0x98ed84: DecompressPointer r2
    //     0x98ed84: add             x2, x2, HEAP, lsl #32
    // 0x98ed88: cmp             w2, NULL
    // 0x98ed8c: b.eq            #0x98f1ec
    // 0x98ed90: LoadField: r3 = r2->field_3f
    //     0x98ed90: ldur            w3, [x2, #0x3f]
    // 0x98ed94: DecompressPointer r3
    //     0x98ed94: add             x3, x3, HEAP, lsl #32
    // 0x98ed98: r16 = <Color>
    //     0x98ed98: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x98ed9c: ldr             x16, [x16, #0xf80]
    // 0x98eda0: stp             x3, x16, [SP]
    // 0x98eda4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x98eda4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x98eda8: r0 = all()
    //     0x98eda8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x98edac: mov             x1, x0
    // 0x98edb0: ldur            x0, [fp, #-8]
    // 0x98edb4: stur            x1, [fp, #-0x48]
    // 0x98edb8: LoadField: r2 = r0->field_b
    //     0x98edb8: ldur            w2, [x0, #0xb]
    // 0x98edbc: DecompressPointer r2
    //     0x98edbc: add             x2, x2, HEAP, lsl #32
    // 0x98edc0: cmp             w2, NULL
    // 0x98edc4: b.eq            #0x98f1f0
    // 0x98edc8: LoadField: r3 = r2->field_43
    //     0x98edc8: ldur            w3, [x2, #0x43]
    // 0x98edcc: DecompressPointer r3
    //     0x98edcc: add             x3, x3, HEAP, lsl #32
    // 0x98edd0: stur            x3, [fp, #-0x40]
    // 0x98edd4: LoadField: r4 = r2->field_33
    //     0x98edd4: ldur            w4, [x2, #0x33]
    // 0x98edd8: DecompressPointer r4
    //     0x98edd8: add             x4, x4, HEAP, lsl #32
    // 0x98eddc: stur            x4, [fp, #-0x30]
    // 0x98ede0: r0 = BorderSide()
    //     0x98ede0: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x98ede4: mov             x1, x0
    // 0x98ede8: ldur            x0, [fp, #-0x30]
    // 0x98edec: stur            x1, [fp, #-0x50]
    // 0x98edf0: StoreField: r1->field_7 = r0
    //     0x98edf0: stur            w0, [x1, #7]
    // 0x98edf4: d0 = 1.000000
    //     0x98edf4: fmov            d0, #1.00000000
    // 0x98edf8: StoreField: r1->field_b = d0
    //     0x98edf8: stur            d0, [x1, #0xb]
    // 0x98edfc: r0 = Instance_BorderStyle
    //     0x98edfc: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x98ee00: ldr             x0, [x0, #0xf68]
    // 0x98ee04: StoreField: r1->field_13 = r0
    //     0x98ee04: stur            w0, [x1, #0x13]
    // 0x98ee08: d0 = -1.000000
    //     0x98ee08: fmov            d0, #-1.00000000
    // 0x98ee0c: ArrayStore: r1[0] = d0  ; List_8
    //     0x98ee0c: stur            d0, [x1, #0x17]
    // 0x98ee10: r0 = RoundedRectangleBorder()
    //     0x98ee10: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x98ee14: mov             x1, x0
    // 0x98ee18: ldur            x0, [fp, #-0x40]
    // 0x98ee1c: StoreField: r1->field_b = r0
    //     0x98ee1c: stur            w0, [x1, #0xb]
    // 0x98ee20: ldur            x0, [fp, #-0x50]
    // 0x98ee24: StoreField: r1->field_7 = r0
    //     0x98ee24: stur            w0, [x1, #7]
    // 0x98ee28: r16 = <RoundedRectangleBorder>
    //     0x98ee28: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x98ee2c: ldr             x16, [x16, #0xf78]
    // 0x98ee30: stp             x1, x16, [SP]
    // 0x98ee34: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x98ee34: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x98ee38: r0 = all()
    //     0x98ee38: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x98ee3c: stur            x0, [fp, #-0x30]
    // 0x98ee40: r0 = ButtonStyle()
    //     0x98ee40: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x98ee44: mov             x1, x0
    // 0x98ee48: ldur            x0, [fp, #-0x48]
    // 0x98ee4c: stur            x1, [fp, #-0x40]
    // 0x98ee50: StoreField: r1->field_b = r0
    //     0x98ee50: stur            w0, [x1, #0xb]
    // 0x98ee54: ldur            x0, [fp, #-0x18]
    // 0x98ee58: StoreField: r1->field_23 = r0
    //     0x98ee58: stur            w0, [x1, #0x23]
    // 0x98ee5c: ldur            x0, [fp, #-0x30]
    // 0x98ee60: StoreField: r1->field_43 = r0
    //     0x98ee60: stur            w0, [x1, #0x43]
    // 0x98ee64: r0 = TextButtonThemeData()
    //     0x98ee64: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x98ee68: mov             x2, x0
    // 0x98ee6c: ldur            x0, [fp, #-0x40]
    // 0x98ee70: stur            x2, [fp, #-0x18]
    // 0x98ee74: StoreField: r2->field_7 = r0
    //     0x98ee74: stur            w0, [x2, #7]
    // 0x98ee78: ldur            x1, [fp, #-0x10]
    // 0x98ee7c: r0 = of()
    //     0x98ee7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98ee80: LoadField: r1 = r0->field_5b
    //     0x98ee80: ldur            w1, [x0, #0x5b]
    // 0x98ee84: DecompressPointer r1
    //     0x98ee84: add             x1, x1, HEAP, lsl #32
    // 0x98ee88: r0 = LoadClassIdInstr(r1)
    //     0x98ee88: ldur            x0, [x1, #-1]
    //     0x98ee8c: ubfx            x0, x0, #0xc, #0x14
    // 0x98ee90: d0 = 0.700000
    //     0x98ee90: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98ee94: ldr             d0, [x17, #0xf48]
    // 0x98ee98: r0 = GDT[cid_x0 + -0xffa]()
    //     0x98ee98: sub             lr, x0, #0xffa
    //     0x98ee9c: ldr             lr, [x21, lr, lsl #3]
    //     0x98eea0: blr             lr
    // 0x98eea4: stur            x0, [fp, #-0x30]
    // 0x98eea8: r0 = ColorFilter()
    //     0x98eea8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x98eeac: mov             x1, x0
    // 0x98eeb0: ldur            x0, [fp, #-0x30]
    // 0x98eeb4: stur            x1, [fp, #-0x40]
    // 0x98eeb8: StoreField: r1->field_7 = r0
    //     0x98eeb8: stur            w0, [x1, #7]
    // 0x98eebc: r0 = Instance_BlendMode
    //     0x98eebc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x98eec0: ldr             x0, [x0, #0xb30]
    // 0x98eec4: StoreField: r1->field_b = r0
    //     0x98eec4: stur            w0, [x1, #0xb]
    // 0x98eec8: r0 = 1
    //     0x98eec8: movz            x0, #0x1
    // 0x98eecc: StoreField: r1->field_13 = r0
    //     0x98eecc: stur            x0, [x1, #0x13]
    // 0x98eed0: r0 = SvgPicture()
    //     0x98eed0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x98eed4: stur            x0, [fp, #-0x30]
    // 0x98eed8: ldur            x16, [fp, #-0x40]
    // 0x98eedc: str             x16, [SP]
    // 0x98eee0: mov             x1, x0
    // 0x98eee4: r2 = "assets/images/search.svg"
    //     0x98eee4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x98eee8: ldr             x2, [x2, #0xa30]
    // 0x98eeec: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x98eeec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x98eef0: ldr             x4, [x4, #0xa38]
    // 0x98eef4: r0 = SvgPicture.asset()
    //     0x98eef4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x98eef8: r0 = Align()
    //     0x98eef8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x98eefc: mov             x2, x0
    // 0x98ef00: r0 = Instance_Alignment
    //     0x98ef00: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x98ef04: ldr             x0, [x0, #0xb10]
    // 0x98ef08: stur            x2, [fp, #-0x40]
    // 0x98ef0c: StoreField: r2->field_f = r0
    //     0x98ef0c: stur            w0, [x2, #0xf]
    // 0x98ef10: r0 = 1.000000
    //     0x98ef10: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x98ef14: StoreField: r2->field_13 = r0
    //     0x98ef14: stur            w0, [x2, #0x13]
    // 0x98ef18: ArrayStore: r2[0] = r0  ; List_4
    //     0x98ef18: stur            w0, [x2, #0x17]
    // 0x98ef1c: ldur            x0, [fp, #-0x30]
    // 0x98ef20: StoreField: r2->field_b = r0
    //     0x98ef20: stur            w0, [x2, #0xb]
    // 0x98ef24: ldur            x0, [fp, #-8]
    // 0x98ef28: LoadField: r1 = r0->field_b
    //     0x98ef28: ldur            w1, [x0, #0xb]
    // 0x98ef2c: DecompressPointer r1
    //     0x98ef2c: add             x1, x1, HEAP, lsl #32
    // 0x98ef30: cmp             w1, NULL
    // 0x98ef34: b.eq            #0x98f1f4
    // 0x98ef38: LoadField: r0 = r1->field_f
    //     0x98ef38: ldur            w0, [x1, #0xf]
    // 0x98ef3c: DecompressPointer r0
    //     0x98ef3c: add             x0, x0, HEAP, lsl #32
    // 0x98ef40: cmp             w0, NULL
    // 0x98ef44: b.ne            #0x98ef50
    // 0x98ef48: r0 = Null
    //     0x98ef48: mov             x0, NULL
    // 0x98ef4c: b               #0x98ef5c
    // 0x98ef50: LoadField: r1 = r0->field_f
    //     0x98ef50: ldur            w1, [x0, #0xf]
    // 0x98ef54: DecompressPointer r1
    //     0x98ef54: add             x1, x1, HEAP, lsl #32
    // 0x98ef58: mov             x0, x1
    // 0x98ef5c: cmp             w0, NULL
    // 0x98ef60: b.ne            #0x98ef6c
    // 0x98ef64: r4 = ""
    //     0x98ef64: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98ef68: b               #0x98ef70
    // 0x98ef6c: mov             x4, x0
    // 0x98ef70: ldur            x3, [fp, #-0x20]
    // 0x98ef74: ldur            x0, [fp, #-0x18]
    // 0x98ef78: ldur            x1, [fp, #-0x10]
    // 0x98ef7c: stur            x4, [fp, #-8]
    // 0x98ef80: r0 = of()
    //     0x98ef80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98ef84: LoadField: r1 = r0->field_87
    //     0x98ef84: ldur            w1, [x0, #0x87]
    // 0x98ef88: DecompressPointer r1
    //     0x98ef88: add             x1, x1, HEAP, lsl #32
    // 0x98ef8c: LoadField: r0 = r1->field_2b
    //     0x98ef8c: ldur            w0, [x1, #0x2b]
    // 0x98ef90: DecompressPointer r0
    //     0x98ef90: add             x0, x0, HEAP, lsl #32
    // 0x98ef94: ldur            x1, [fp, #-0x10]
    // 0x98ef98: stur            x0, [fp, #-0x30]
    // 0x98ef9c: r0 = of()
    //     0x98ef9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x98efa0: LoadField: r1 = r0->field_5b
    //     0x98efa0: ldur            w1, [x0, #0x5b]
    // 0x98efa4: DecompressPointer r1
    //     0x98efa4: add             x1, x1, HEAP, lsl #32
    // 0x98efa8: r0 = LoadClassIdInstr(r1)
    //     0x98efa8: ldur            x0, [x1, #-1]
    //     0x98efac: ubfx            x0, x0, #0xc, #0x14
    // 0x98efb0: d0 = 0.700000
    //     0x98efb0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x98efb4: ldr             d0, [x17, #0xf48]
    // 0x98efb8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x98efb8: sub             lr, x0, #0xffa
    //     0x98efbc: ldr             lr, [x21, lr, lsl #3]
    //     0x98efc0: blr             lr
    // 0x98efc4: r16 = 16.000000
    //     0x98efc4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x98efc8: ldr             x16, [x16, #0x188]
    // 0x98efcc: stp             x0, x16, [SP]
    // 0x98efd0: ldur            x1, [fp, #-0x30]
    // 0x98efd4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x98efd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x98efd8: ldr             x4, [x4, #0xaa0]
    // 0x98efdc: r0 = copyWith()
    //     0x98efdc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x98efe0: stur            x0, [fp, #-0x10]
    // 0x98efe4: r0 = Text()
    //     0x98efe4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x98efe8: mov             x3, x0
    // 0x98efec: ldur            x0, [fp, #-8]
    // 0x98eff0: stur            x3, [fp, #-0x30]
    // 0x98eff4: StoreField: r3->field_b = r0
    //     0x98eff4: stur            w0, [x3, #0xb]
    // 0x98eff8: ldur            x0, [fp, #-0x10]
    // 0x98effc: StoreField: r3->field_13 = r0
    //     0x98effc: stur            w0, [x3, #0x13]
    // 0x98f000: ldur            x2, [fp, #-0x28]
    // 0x98f004: r1 = Function '<anonymous closure>':.
    //     0x98f004: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c050] AnonymousClosure: (0x98f404), in [package:customer_app/app/presentation/custom_widgets/home/<USER>
    //     0x98f008: ldr             x1, [x1, #0x50]
    // 0x98f00c: r0 = AllocateClosure()
    //     0x98f00c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98f010: ldur            x2, [fp, #-0x40]
    // 0x98f014: ldur            x3, [fp, #-0x30]
    // 0x98f018: mov             x5, x0
    // 0x98f01c: r1 = Null
    //     0x98f01c: mov             x1, NULL
    // 0x98f020: r0 = TextButton.icon()
    //     0x98f020: bl              #0x98f21c  ; [package:flutter/src/material/text_button.dart] TextButton::TextButton.icon
    // 0x98f024: stur            x0, [fp, #-8]
    // 0x98f028: r0 = TextButtonTheme()
    //     0x98f028: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x98f02c: mov             x1, x0
    // 0x98f030: ldur            x0, [fp, #-0x18]
    // 0x98f034: stur            x1, [fp, #-0x10]
    // 0x98f038: StoreField: r1->field_f = r0
    //     0x98f038: stur            w0, [x1, #0xf]
    // 0x98f03c: ldur            x0, [fp, #-8]
    // 0x98f040: StoreField: r1->field_b = r0
    //     0x98f040: stur            w0, [x1, #0xb]
    // 0x98f044: r0 = SizedBox()
    //     0x98f044: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x98f048: mov             x3, x0
    // 0x98f04c: r0 = inf
    //     0x98f04c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x98f050: ldr             x0, [x0, #0x9f8]
    // 0x98f054: stur            x3, [fp, #-8]
    // 0x98f058: StoreField: r3->field_f = r0
    //     0x98f058: stur            w0, [x3, #0xf]
    // 0x98f05c: ldur            x0, [fp, #-0x10]
    // 0x98f060: StoreField: r3->field_b = r0
    //     0x98f060: stur            w0, [x3, #0xb]
    // 0x98f064: r1 = Null
    //     0x98f064: mov             x1, NULL
    // 0x98f068: r2 = 4
    //     0x98f068: movz            x2, #0x4
    // 0x98f06c: r0 = AllocateArray()
    //     0x98f06c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x98f070: mov             x2, x0
    // 0x98f074: ldur            x0, [fp, #-0x20]
    // 0x98f078: stur            x2, [fp, #-0x10]
    // 0x98f07c: StoreField: r2->field_f = r0
    //     0x98f07c: stur            w0, [x2, #0xf]
    // 0x98f080: ldur            x0, [fp, #-8]
    // 0x98f084: StoreField: r2->field_13 = r0
    //     0x98f084: stur            w0, [x2, #0x13]
    // 0x98f088: r1 = <Widget>
    //     0x98f088: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x98f08c: r0 = AllocateGrowableArray()
    //     0x98f08c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x98f090: mov             x1, x0
    // 0x98f094: ldur            x0, [fp, #-0x10]
    // 0x98f098: stur            x1, [fp, #-8]
    // 0x98f09c: StoreField: r1->field_f = r0
    //     0x98f09c: stur            w0, [x1, #0xf]
    // 0x98f0a0: r0 = 4
    //     0x98f0a0: movz            x0, #0x4
    // 0x98f0a4: StoreField: r1->field_b = r0
    //     0x98f0a4: stur            w0, [x1, #0xb]
    // 0x98f0a8: r0 = Column()
    //     0x98f0a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x98f0ac: mov             x1, x0
    // 0x98f0b0: r0 = Instance_Axis
    //     0x98f0b0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x98f0b4: stur            x1, [fp, #-0x10]
    // 0x98f0b8: StoreField: r1->field_f = r0
    //     0x98f0b8: stur            w0, [x1, #0xf]
    // 0x98f0bc: r0 = Instance_MainAxisAlignment
    //     0x98f0bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x98f0c0: ldr             x0, [x0, #0xa08]
    // 0x98f0c4: StoreField: r1->field_13 = r0
    //     0x98f0c4: stur            w0, [x1, #0x13]
    // 0x98f0c8: r0 = Instance_MainAxisSize
    //     0x98f0c8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x98f0cc: ldr             x0, [x0, #0xa10]
    // 0x98f0d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x98f0d0: stur            w0, [x1, #0x17]
    // 0x98f0d4: r0 = Instance_CrossAxisAlignment
    //     0x98f0d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x98f0d8: ldr             x0, [x0, #0xa18]
    // 0x98f0dc: StoreField: r1->field_1b = r0
    //     0x98f0dc: stur            w0, [x1, #0x1b]
    // 0x98f0e0: r0 = Instance_VerticalDirection
    //     0x98f0e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x98f0e4: ldr             x0, [x0, #0xa20]
    // 0x98f0e8: StoreField: r1->field_23 = r0
    //     0x98f0e8: stur            w0, [x1, #0x23]
    // 0x98f0ec: r0 = Instance_Clip
    //     0x98f0ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x98f0f0: ldr             x0, [x0, #0x38]
    // 0x98f0f4: StoreField: r1->field_2b = r0
    //     0x98f0f4: stur            w0, [x1, #0x2b]
    // 0x98f0f8: StoreField: r1->field_2f = rZR
    //     0x98f0f8: stur            xzr, [x1, #0x2f]
    // 0x98f0fc: ldur            x0, [fp, #-8]
    // 0x98f100: StoreField: r1->field_b = r0
    //     0x98f100: stur            w0, [x1, #0xb]
    // 0x98f104: r0 = Padding()
    //     0x98f104: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x98f108: mov             x1, x0
    // 0x98f10c: r0 = Instance_EdgeInsets
    //     0x98f10c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x98f110: ldr             x0, [x0, #0x1f0]
    // 0x98f114: stur            x1, [fp, #-8]
    // 0x98f118: StoreField: r1->field_f = r0
    //     0x98f118: stur            w0, [x1, #0xf]
    // 0x98f11c: ldur            x0, [fp, #-0x10]
    // 0x98f120: StoreField: r1->field_b = r0
    //     0x98f120: stur            w0, [x1, #0xb]
    // 0x98f124: r0 = Container()
    //     0x98f124: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x98f128: stur            x0, [fp, #-0x10]
    // 0x98f12c: ldur            x16, [fp, #-0x38]
    // 0x98f130: r30 = Instance_EdgeInsets
    //     0x98f130: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x98f134: ldr             lr, [lr, #0x1f0]
    // 0x98f138: stp             lr, x16, [SP, #8]
    // 0x98f13c: ldur            x16, [fp, #-8]
    // 0x98f140: str             x16, [SP]
    // 0x98f144: mov             x1, x0
    // 0x98f148: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, margin, 0x2, null]
    //     0x98f148: add             x4, PP, #0x53, lsl #12  ; [pp+0x534e8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "margin", 0x2, Null]
    //     0x98f14c: ldr             x4, [x4, #0x4e8]
    // 0x98f150: r0 = Container()
    //     0x98f150: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x98f154: r0 = Padding()
    //     0x98f154: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x98f158: mov             x1, x0
    // 0x98f15c: r0 = Instance_EdgeInsets
    //     0x98f15c: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0x98f160: ldr             x0, [x0, #0x240]
    // 0x98f164: stur            x1, [fp, #-8]
    // 0x98f168: StoreField: r1->field_f = r0
    //     0x98f168: stur            w0, [x1, #0xf]
    // 0x98f16c: ldur            x0, [fp, #-0x10]
    // 0x98f170: StoreField: r1->field_b = r0
    //     0x98f170: stur            w0, [x1, #0xb]
    // 0x98f174: r0 = InkWell()
    //     0x98f174: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x98f178: mov             x3, x0
    // 0x98f17c: ldur            x0, [fp, #-8]
    // 0x98f180: stur            x3, [fp, #-0x10]
    // 0x98f184: StoreField: r3->field_b = r0
    //     0x98f184: stur            w0, [x3, #0xb]
    // 0x98f188: ldur            x2, [fp, #-0x28]
    // 0x98f18c: r1 = Function '<anonymous closure>':.
    //     0x98f18c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c058] AnonymousClosure: (0x98f314), in [package:customer_app/app/presentation/custom_widgets/home/<USER>
    //     0x98f190: ldr             x1, [x1, #0x58]
    // 0x98f194: r0 = AllocateClosure()
    //     0x98f194: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x98f198: mov             x1, x0
    // 0x98f19c: ldur            x0, [fp, #-0x10]
    // 0x98f1a0: StoreField: r0->field_f = r1
    //     0x98f1a0: stur            w1, [x0, #0xf]
    // 0x98f1a4: r1 = true
    //     0x98f1a4: add             x1, NULL, #0x20  ; true
    // 0x98f1a8: StoreField: r0->field_43 = r1
    //     0x98f1a8: stur            w1, [x0, #0x43]
    // 0x98f1ac: r2 = Instance_BoxShape
    //     0x98f1ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x98f1b0: ldr             x2, [x2, #0x80]
    // 0x98f1b4: StoreField: r0->field_47 = r2
    //     0x98f1b4: stur            w2, [x0, #0x47]
    // 0x98f1b8: StoreField: r0->field_6f = r1
    //     0x98f1b8: stur            w1, [x0, #0x6f]
    // 0x98f1bc: r2 = false
    //     0x98f1bc: add             x2, NULL, #0x30  ; false
    // 0x98f1c0: StoreField: r0->field_73 = r2
    //     0x98f1c0: stur            w2, [x0, #0x73]
    // 0x98f1c4: StoreField: r0->field_83 = r1
    //     0x98f1c4: stur            w1, [x0, #0x83]
    // 0x98f1c8: StoreField: r0->field_7b = r2
    //     0x98f1c8: stur            w2, [x0, #0x7b]
    // 0x98f1cc: LeaveFrame
    //     0x98f1cc: mov             SP, fp
    //     0x98f1d0: ldp             fp, lr, [SP], #0x10
    // 0x98f1d4: ret
    //     0x98f1d4: ret             
    // 0x98f1d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98f1d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98f1dc: b               #0x98eac0
    // 0x98f1e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f1e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98f1e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f1e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98f1e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f1e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98f1ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f1ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98f1f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f1f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x98f1f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f1f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x98f314, size: 0xf0
    // 0x98f314: EnterFrame
    //     0x98f314: stp             fp, lr, [SP, #-0x10]!
    //     0x98f318: mov             fp, SP
    // 0x98f31c: AllocStack(0x38)
    //     0x98f31c: sub             SP, SP, #0x38
    // 0x98f320: SetupParameters()
    //     0x98f320: ldr             x0, [fp, #0x10]
    //     0x98f324: ldur            w1, [x0, #0x17]
    //     0x98f328: add             x1, x1, HEAP, lsl #32
    // 0x98f32c: CheckStackOverflow
    //     0x98f32c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98f330: cmp             SP, x16
    //     0x98f334: b.ls            #0x98f3f8
    // 0x98f338: LoadField: r0 = r1->field_f
    //     0x98f338: ldur            w0, [x1, #0xf]
    // 0x98f33c: DecompressPointer r0
    //     0x98f33c: add             x0, x0, HEAP, lsl #32
    // 0x98f340: LoadField: r1 = r0->field_b
    //     0x98f340: ldur            w1, [x0, #0xb]
    // 0x98f344: DecompressPointer r1
    //     0x98f344: add             x1, x1, HEAP, lsl #32
    // 0x98f348: cmp             w1, NULL
    // 0x98f34c: b.eq            #0x98f400
    // 0x98f350: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x98f350: ldur            w0, [x1, #0x17]
    // 0x98f354: DecompressPointer r0
    //     0x98f354: add             x0, x0, HEAP, lsl #32
    // 0x98f358: cmp             w0, NULL
    // 0x98f35c: b.ne            #0x98f364
    // 0x98f360: r0 = ""
    //     0x98f360: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f364: LoadField: r2 = r1->field_13
    //     0x98f364: ldur            w2, [x1, #0x13]
    // 0x98f368: DecompressPointer r2
    //     0x98f368: add             x2, x2, HEAP, lsl #32
    // 0x98f36c: cmp             w2, NULL
    // 0x98f370: b.ne            #0x98f378
    // 0x98f374: r2 = ""
    //     0x98f374: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f378: LoadField: r3 = r1->field_27
    //     0x98f378: ldur            w3, [x1, #0x27]
    // 0x98f37c: DecompressPointer r3
    //     0x98f37c: add             x3, x3, HEAP, lsl #32
    // 0x98f380: cmp             w3, NULL
    // 0x98f384: b.ne            #0x98f38c
    // 0x98f388: r3 = ""
    //     0x98f388: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f38c: LoadField: r4 = r1->field_1f
    //     0x98f38c: ldur            w4, [x1, #0x1f]
    // 0x98f390: DecompressPointer r4
    //     0x98f390: add             x4, x4, HEAP, lsl #32
    // 0x98f394: cmp             w4, NULL
    // 0x98f398: b.ne            #0x98f3a0
    // 0x98f39c: r4 = ""
    //     0x98f39c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f3a0: LoadField: r5 = r1->field_1b
    //     0x98f3a0: ldur            w5, [x1, #0x1b]
    // 0x98f3a4: DecompressPointer r5
    //     0x98f3a4: add             x5, x5, HEAP, lsl #32
    // 0x98f3a8: cmp             w5, NULL
    // 0x98f3ac: b.ne            #0x98f3b4
    // 0x98f3b0: r5 = ""
    //     0x98f3b0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f3b4: LoadField: r6 = r1->field_2b
    //     0x98f3b4: ldur            w6, [x1, #0x2b]
    // 0x98f3b8: DecompressPointer r6
    //     0x98f3b8: add             x6, x6, HEAP, lsl #32
    // 0x98f3bc: LoadField: r7 = r1->field_23
    //     0x98f3bc: ldur            w7, [x1, #0x23]
    // 0x98f3c0: DecompressPointer r7
    //     0x98f3c0: add             x7, x7, HEAP, lsl #32
    // 0x98f3c4: stp             x0, x7, [SP, #0x28]
    // 0x98f3c8: stp             x3, x2, [SP, #0x18]
    // 0x98f3cc: stp             x5, x4, [SP, #8]
    // 0x98f3d0: str             x6, [SP]
    // 0x98f3d4: r4 = 0
    //     0x98f3d4: movz            x4, #0
    // 0x98f3d8: ldr             x0, [SP, #0x30]
    // 0x98f3dc: r5 = UnlinkedCall_0x613b5c
    //     0x98f3dc: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c060] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x98f3e0: ldp             x5, lr, [x16, #0x60]
    // 0x98f3e4: blr             lr
    // 0x98f3e8: r0 = Null
    //     0x98f3e8: mov             x0, NULL
    // 0x98f3ec: LeaveFrame
    //     0x98f3ec: mov             SP, fp
    //     0x98f3f0: ldp             fp, lr, [SP], #0x10
    // 0x98f3f4: ret
    //     0x98f3f4: ret             
    // 0x98f3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98f3f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98f3fc: b               #0x98f338
    // 0x98f400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f400: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x98f404, size: 0xf0
    // 0x98f404: EnterFrame
    //     0x98f404: stp             fp, lr, [SP, #-0x10]!
    //     0x98f408: mov             fp, SP
    // 0x98f40c: AllocStack(0x38)
    //     0x98f40c: sub             SP, SP, #0x38
    // 0x98f410: SetupParameters()
    //     0x98f410: ldr             x0, [fp, #0x10]
    //     0x98f414: ldur            w1, [x0, #0x17]
    //     0x98f418: add             x1, x1, HEAP, lsl #32
    // 0x98f41c: CheckStackOverflow
    //     0x98f41c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x98f420: cmp             SP, x16
    //     0x98f424: b.ls            #0x98f4e8
    // 0x98f428: LoadField: r0 = r1->field_f
    //     0x98f428: ldur            w0, [x1, #0xf]
    // 0x98f42c: DecompressPointer r0
    //     0x98f42c: add             x0, x0, HEAP, lsl #32
    // 0x98f430: LoadField: r1 = r0->field_b
    //     0x98f430: ldur            w1, [x0, #0xb]
    // 0x98f434: DecompressPointer r1
    //     0x98f434: add             x1, x1, HEAP, lsl #32
    // 0x98f438: cmp             w1, NULL
    // 0x98f43c: b.eq            #0x98f4f0
    // 0x98f440: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x98f440: ldur            w0, [x1, #0x17]
    // 0x98f444: DecompressPointer r0
    //     0x98f444: add             x0, x0, HEAP, lsl #32
    // 0x98f448: cmp             w0, NULL
    // 0x98f44c: b.ne            #0x98f454
    // 0x98f450: r0 = ""
    //     0x98f450: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f454: LoadField: r2 = r1->field_13
    //     0x98f454: ldur            w2, [x1, #0x13]
    // 0x98f458: DecompressPointer r2
    //     0x98f458: add             x2, x2, HEAP, lsl #32
    // 0x98f45c: cmp             w2, NULL
    // 0x98f460: b.ne            #0x98f468
    // 0x98f464: r2 = ""
    //     0x98f464: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f468: LoadField: r3 = r1->field_27
    //     0x98f468: ldur            w3, [x1, #0x27]
    // 0x98f46c: DecompressPointer r3
    //     0x98f46c: add             x3, x3, HEAP, lsl #32
    // 0x98f470: cmp             w3, NULL
    // 0x98f474: b.ne            #0x98f47c
    // 0x98f478: r3 = ""
    //     0x98f478: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f47c: LoadField: r4 = r1->field_1f
    //     0x98f47c: ldur            w4, [x1, #0x1f]
    // 0x98f480: DecompressPointer r4
    //     0x98f480: add             x4, x4, HEAP, lsl #32
    // 0x98f484: cmp             w4, NULL
    // 0x98f488: b.ne            #0x98f490
    // 0x98f48c: r4 = ""
    //     0x98f48c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f490: LoadField: r5 = r1->field_1b
    //     0x98f490: ldur            w5, [x1, #0x1b]
    // 0x98f494: DecompressPointer r5
    //     0x98f494: add             x5, x5, HEAP, lsl #32
    // 0x98f498: cmp             w5, NULL
    // 0x98f49c: b.ne            #0x98f4a4
    // 0x98f4a0: r5 = ""
    //     0x98f4a0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x98f4a4: LoadField: r6 = r1->field_2b
    //     0x98f4a4: ldur            w6, [x1, #0x2b]
    // 0x98f4a8: DecompressPointer r6
    //     0x98f4a8: add             x6, x6, HEAP, lsl #32
    // 0x98f4ac: LoadField: r7 = r1->field_23
    //     0x98f4ac: ldur            w7, [x1, #0x23]
    // 0x98f4b0: DecompressPointer r7
    //     0x98f4b0: add             x7, x7, HEAP, lsl #32
    // 0x98f4b4: stp             x0, x7, [SP, #0x28]
    // 0x98f4b8: stp             x3, x2, [SP, #0x18]
    // 0x98f4bc: stp             x5, x4, [SP, #8]
    // 0x98f4c0: str             x6, [SP]
    // 0x98f4c4: r4 = 0
    //     0x98f4c4: movz            x4, #0
    // 0x98f4c8: ldr             x0, [SP, #0x30]
    // 0x98f4cc: r5 = UnlinkedCall_0x613b5c
    //     0x98f4cc: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c070] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x98f4d0: ldp             x5, lr, [x16, #0x70]
    // 0x98f4d4: blr             lr
    // 0x98f4d8: r0 = Null
    //     0x98f4d8: mov             x0, NULL
    // 0x98f4dc: LeaveFrame
    //     0x98f4dc: mov             SP, fp
    //     0x98f4e0: ldp             fp, lr, [SP], #0x10
    // 0x98f4e4: ret
    //     0x98f4e4: ret             
    // 0x98f4e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x98f4e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x98f4ec: b               #0x98f428
    // 0x98f4f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x98f4f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4317, size: 0x48, field offset: 0xc
//   const constructor, 
class WebsiteNavigatorWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc799b8, size: 0x24
    // 0xc799b8: EnterFrame
    //     0xc799b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc799bc: mov             fp, SP
    // 0xc799c0: mov             x0, x1
    // 0xc799c4: r1 = <WebsiteNavigatorWidget>
    //     0xc799c4: add             x1, PP, #0x49, lsl #12  ; [pp+0x49378] TypeArguments: <WebsiteNavigatorWidget>
    //     0xc799c8: ldr             x1, [x1, #0x378]
    // 0xc799cc: r0 = _WebsiteNavigatorWidgetState()
    //     0xc799cc: bl              #0xc799dc  ; Allocate_WebsiteNavigatorWidgetStateStub -> _WebsiteNavigatorWidgetState (size=0x14)
    // 0xc799d0: LeaveFrame
    //     0xc799d0: mov             SP, fp
    //     0xc799d4: ldp             fp, lr, [SP], #0x10
    // 0xc799d8: ret
    //     0xc799d8: ret             
  }
}
