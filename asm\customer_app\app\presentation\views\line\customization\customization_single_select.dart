// lib: , url: package:customer_app/app/presentation/views/line/customization/customization_single_select.dart

// class id: 1049509, size: 0x8
class :: {
}

// class id: 3255, size: 0x20, field offset: 0x14
class _CustomizationSingleSelectState extends State<dynamic> {

  [closure] ProductCustomisation <anonymous closure>(dynamic) {
    // ** addr: 0xa309d8, size: 0x18
    // 0xa309d8: EnterFrame
    //     0xa309d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa309dc: mov             fp, SP
    // 0xa309e0: r0 = ProductCustomisation()
    //     0xa309e0: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xa309e4: LeaveFrame
    //     0xa309e4: mov             SP, fp
    //     0xa309e8: ldp             fp, lr, [SP], #0x10
    // 0xa309ec: ret
    //     0xa309ec: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa309f0, size: 0x57c
    // 0xa309f0: EnterFrame
    //     0xa309f0: stp             fp, lr, [SP, #-0x10]!
    //     0xa309f4: mov             fp, SP
    // 0xa309f8: AllocStack(0x70)
    //     0xa309f8: sub             SP, SP, #0x70
    // 0xa309fc: SetupParameters()
    //     0xa309fc: ldr             x0, [fp, #0x10]
    //     0xa30a00: ldur            w3, [x0, #0x17]
    //     0xa30a04: add             x3, x3, HEAP, lsl #32
    //     0xa30a08: stur            x3, [fp, #-0x18]
    // 0xa30a0c: CheckStackOverflow
    //     0xa30a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa30a10: cmp             SP, x16
    //     0xa30a14: b.ls            #0xa30f50
    // 0xa30a18: LoadField: r0 = r3->field_b
    //     0xa30a18: ldur            w0, [x3, #0xb]
    // 0xa30a1c: DecompressPointer r0
    //     0xa30a1c: add             x0, x0, HEAP, lsl #32
    // 0xa30a20: stur            x0, [fp, #-0x10]
    // 0xa30a24: LoadField: r1 = r0->field_f
    //     0xa30a24: ldur            w1, [x0, #0xf]
    // 0xa30a28: DecompressPointer r1
    //     0xa30a28: add             x1, x1, HEAP, lsl #32
    // 0xa30a2c: LoadField: r2 = r1->field_b
    //     0xa30a2c: ldur            w2, [x1, #0xb]
    // 0xa30a30: DecompressPointer r2
    //     0xa30a30: add             x2, x2, HEAP, lsl #32
    // 0xa30a34: cmp             w2, NULL
    // 0xa30a38: b.eq            #0xa30f58
    // 0xa30a3c: LoadField: r4 = r2->field_f
    //     0xa30a3c: ldur            w4, [x2, #0xf]
    // 0xa30a40: DecompressPointer r4
    //     0xa30a40: add             x4, x4, HEAP, lsl #32
    // 0xa30a44: stur            x4, [fp, #-8]
    // 0xa30a48: LoadField: r1 = r4->field_b
    //     0xa30a48: ldur            w1, [x4, #0xb]
    // 0xa30a4c: cbz             w1, #0xa30adc
    // 0xa30a50: mov             x2, x3
    // 0xa30a54: r1 = Function '<anonymous closure>':.
    //     0xa30a54: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a3d8] AnonymousClosure: (0xa3aa20), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xa30a58: ldr             x1, [x1, #0x3d8]
    // 0xa30a5c: r0 = AllocateClosure()
    //     0xa30a5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa30a60: r1 = Function '<anonymous closure>':.
    //     0xa30a60: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a3e0] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xa30a64: ldr             x1, [x1, #0x3e0]
    // 0xa30a68: r2 = Null
    //     0xa30a68: mov             x2, NULL
    // 0xa30a6c: stur            x0, [fp, #-0x20]
    // 0xa30a70: r0 = AllocateClosure()
    //     0xa30a70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa30a74: str             x0, [SP]
    // 0xa30a78: ldur            x1, [fp, #-8]
    // 0xa30a7c: ldur            x2, [fp, #-0x20]
    // 0xa30a80: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xa30a80: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xa30a84: ldr             x4, [x4, #0xb48]
    // 0xa30a88: r0 = firstWhere()
    //     0xa30a88: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xa30a8c: LoadField: r1 = r0->field_b
    //     0xa30a8c: ldur            w1, [x0, #0xb]
    // 0xa30a90: DecompressPointer r1
    //     0xa30a90: add             x1, x1, HEAP, lsl #32
    // 0xa30a94: cmp             w1, NULL
    // 0xa30a98: b.eq            #0xa30adc
    // 0xa30a9c: ldur            x1, [fp, #-0x10]
    // 0xa30aa0: LoadField: r2 = r1->field_f
    //     0xa30aa0: ldur            w2, [x1, #0xf]
    // 0xa30aa4: DecompressPointer r2
    //     0xa30aa4: add             x2, x2, HEAP, lsl #32
    // 0xa30aa8: LoadField: r3 = r2->field_b
    //     0xa30aa8: ldur            w3, [x2, #0xb]
    // 0xa30aac: DecompressPointer r3
    //     0xa30aac: add             x3, x3, HEAP, lsl #32
    // 0xa30ab0: cmp             w3, NULL
    // 0xa30ab4: b.eq            #0xa30f5c
    // 0xa30ab8: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa30ab8: ldur            w2, [x3, #0x17]
    // 0xa30abc: DecompressPointer r2
    //     0xa30abc: add             x2, x2, HEAP, lsl #32
    // 0xa30ac0: stp             x0, x2, [SP]
    // 0xa30ac4: r4 = 0
    //     0xa30ac4: movz            x4, #0
    // 0xa30ac8: ldr             x0, [SP, #8]
    // 0xa30acc: r16 = UnlinkedCall_0x613b5c
    //     0xa30acc: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a3e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa30ad0: add             x16, x16, #0x3e8
    // 0xa30ad4: ldp             x5, lr, [x16]
    // 0xa30ad8: blr             lr
    // 0xa30adc: ldur            x0, [fp, #-0x10]
    // 0xa30ae0: LoadField: r1 = r0->field_f
    //     0xa30ae0: ldur            w1, [x0, #0xf]
    // 0xa30ae4: DecompressPointer r1
    //     0xa30ae4: add             x1, x1, HEAP, lsl #32
    // 0xa30ae8: LoadField: r2 = r1->field_b
    //     0xa30ae8: ldur            w2, [x1, #0xb]
    // 0xa30aec: DecompressPointer r2
    //     0xa30aec: add             x2, x2, HEAP, lsl #32
    // 0xa30af0: cmp             w2, NULL
    // 0xa30af4: b.eq            #0xa30f60
    // 0xa30af8: LoadField: r1 = r2->field_b
    //     0xa30af8: ldur            w1, [x2, #0xb]
    // 0xa30afc: DecompressPointer r1
    //     0xa30afc: add             x1, x1, HEAP, lsl #32
    // 0xa30b00: cmp             w1, NULL
    // 0xa30b04: b.ne            #0xa30b10
    // 0xa30b08: r1 = Null
    //     0xa30b08: mov             x1, NULL
    // 0xa30b0c: b               #0xa30b1c
    // 0xa30b10: LoadField: r2 = r1->field_1b
    //     0xa30b10: ldur            w2, [x1, #0x1b]
    // 0xa30b14: DecompressPointer r2
    //     0xa30b14: add             x2, x2, HEAP, lsl #32
    // 0xa30b18: mov             x1, x2
    // 0xa30b1c: stur            x1, [fp, #-0x28]
    // 0xa30b20: LoadField: r2 = r0->field_13
    //     0xa30b20: ldur            w2, [x0, #0x13]
    // 0xa30b24: DecompressPointer r2
    //     0xa30b24: add             x2, x2, HEAP, lsl #32
    // 0xa30b28: cmp             w2, NULL
    // 0xa30b2c: b.ne            #0xa30b38
    // 0xa30b30: r3 = Null
    //     0xa30b30: mov             x3, NULL
    // 0xa30b34: b               #0xa30b40
    // 0xa30b38: LoadField: r3 = r2->field_b
    //     0xa30b38: ldur            w3, [x2, #0xb]
    // 0xa30b3c: DecompressPointer r3
    //     0xa30b3c: add             x3, x3, HEAP, lsl #32
    // 0xa30b40: ldur            x2, [fp, #-0x18]
    // 0xa30b44: stur            x3, [fp, #-0x20]
    // 0xa30b48: LoadField: r4 = r2->field_f
    //     0xa30b48: ldur            w4, [x2, #0xf]
    // 0xa30b4c: DecompressPointer r4
    //     0xa30b4c: add             x4, x4, HEAP, lsl #32
    // 0xa30b50: stur            x4, [fp, #-8]
    // 0xa30b54: r0 = CustomerResponse()
    //     0xa30b54: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xa30b58: mov             x2, x0
    // 0xa30b5c: ldur            x0, [fp, #-0x28]
    // 0xa30b60: stur            x2, [fp, #-0x38]
    // 0xa30b64: StoreField: r2->field_7 = r0
    //     0xa30b64: stur            w0, [x2, #7]
    // 0xa30b68: ldur            x0, [fp, #-8]
    // 0xa30b6c: StoreField: r2->field_b = r0
    //     0xa30b6c: stur            w0, [x2, #0xb]
    // 0xa30b70: ldur            x0, [fp, #-0x20]
    // 0xa30b74: StoreField: r2->field_f = r0
    //     0xa30b74: stur            w0, [x2, #0xf]
    // 0xa30b78: ldur            x0, [fp, #-0x18]
    // 0xa30b7c: LoadField: r3 = r0->field_13
    //     0xa30b7c: ldur            w3, [x0, #0x13]
    // 0xa30b80: DecompressPointer r3
    //     0xa30b80: add             x3, x3, HEAP, lsl #32
    // 0xa30b84: stur            x3, [fp, #-8]
    // 0xa30b88: LoadField: r1 = r3->field_b
    //     0xa30b88: ldur            w1, [x3, #0xb]
    // 0xa30b8c: LoadField: r4 = r3->field_f
    //     0xa30b8c: ldur            w4, [x3, #0xf]
    // 0xa30b90: DecompressPointer r4
    //     0xa30b90: add             x4, x4, HEAP, lsl #32
    // 0xa30b94: LoadField: r5 = r4->field_b
    //     0xa30b94: ldur            w5, [x4, #0xb]
    // 0xa30b98: r4 = LoadInt32Instr(r1)
    //     0xa30b98: sbfx            x4, x1, #1, #0x1f
    // 0xa30b9c: stur            x4, [fp, #-0x30]
    // 0xa30ba0: r1 = LoadInt32Instr(r5)
    //     0xa30ba0: sbfx            x1, x5, #1, #0x1f
    // 0xa30ba4: cmp             x4, x1
    // 0xa30ba8: b.ne            #0xa30bb4
    // 0xa30bac: mov             x1, x3
    // 0xa30bb0: r0 = _growToNextCapacity()
    //     0xa30bb0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa30bb4: ldur            x4, [fp, #-0x10]
    // 0xa30bb8: ldur            x3, [fp, #-8]
    // 0xa30bbc: ldur            x2, [fp, #-0x30]
    // 0xa30bc0: add             x0, x2, #1
    // 0xa30bc4: lsl             x1, x0, #1
    // 0xa30bc8: StoreField: r3->field_b = r1
    //     0xa30bc8: stur            w1, [x3, #0xb]
    // 0xa30bcc: LoadField: r1 = r3->field_f
    //     0xa30bcc: ldur            w1, [x3, #0xf]
    // 0xa30bd0: DecompressPointer r1
    //     0xa30bd0: add             x1, x1, HEAP, lsl #32
    // 0xa30bd4: ldur            x0, [fp, #-0x38]
    // 0xa30bd8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa30bd8: add             x25, x1, x2, lsl #2
    //     0xa30bdc: add             x25, x25, #0xf
    //     0xa30be0: str             w0, [x25]
    //     0xa30be4: tbz             w0, #0, #0xa30c00
    //     0xa30be8: ldurb           w16, [x1, #-1]
    //     0xa30bec: ldurb           w17, [x0, #-1]
    //     0xa30bf0: and             x16, x17, x16, lsr #2
    //     0xa30bf4: tst             x16, HEAP, lsr #32
    //     0xa30bf8: b.eq            #0xa30c00
    //     0xa30bfc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa30c00: LoadField: r0 = r4->field_f
    //     0xa30c00: ldur            w0, [x4, #0xf]
    // 0xa30c04: DecompressPointer r0
    //     0xa30c04: add             x0, x0, HEAP, lsl #32
    // 0xa30c08: LoadField: r1 = r0->field_b
    //     0xa30c08: ldur            w1, [x0, #0xb]
    // 0xa30c0c: DecompressPointer r1
    //     0xa30c0c: add             x1, x1, HEAP, lsl #32
    // 0xa30c10: cmp             w1, NULL
    // 0xa30c14: b.eq            #0xa30f64
    // 0xa30c18: LoadField: r0 = r1->field_b
    //     0xa30c18: ldur            w0, [x1, #0xb]
    // 0xa30c1c: DecompressPointer r0
    //     0xa30c1c: add             x0, x0, HEAP, lsl #32
    // 0xa30c20: cmp             w0, NULL
    // 0xa30c24: b.ne            #0xa30c30
    // 0xa30c28: r0 = Null
    //     0xa30c28: mov             x0, NULL
    // 0xa30c2c: b               #0xa30c3c
    // 0xa30c30: LoadField: r2 = r0->field_7
    //     0xa30c30: ldur            w2, [x0, #7]
    // 0xa30c34: DecompressPointer r2
    //     0xa30c34: add             x2, x2, HEAP, lsl #32
    // 0xa30c38: mov             x0, x2
    // 0xa30c3c: stur            x0, [fp, #-0x40]
    // 0xa30c40: LoadField: r2 = r1->field_b
    //     0xa30c40: ldur            w2, [x1, #0xb]
    // 0xa30c44: DecompressPointer r2
    //     0xa30c44: add             x2, x2, HEAP, lsl #32
    // 0xa30c48: cmp             w2, NULL
    // 0xa30c4c: b.ne            #0xa30c58
    // 0xa30c50: r5 = Null
    //     0xa30c50: mov             x5, NULL
    // 0xa30c54: b               #0xa30c60
    // 0xa30c58: LoadField: r5 = r2->field_b
    //     0xa30c58: ldur            w5, [x2, #0xb]
    // 0xa30c5c: DecompressPointer r5
    //     0xa30c5c: add             x5, x5, HEAP, lsl #32
    // 0xa30c60: stur            x5, [fp, #-0x38]
    // 0xa30c64: LoadField: r2 = r1->field_b
    //     0xa30c64: ldur            w2, [x1, #0xb]
    // 0xa30c68: DecompressPointer r2
    //     0xa30c68: add             x2, x2, HEAP, lsl #32
    // 0xa30c6c: cmp             w2, NULL
    // 0xa30c70: b.ne            #0xa30c7c
    // 0xa30c74: r6 = Null
    //     0xa30c74: mov             x6, NULL
    // 0xa30c78: b               #0xa30c88
    // 0xa30c7c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa30c7c: ldur            w1, [x2, #0x17]
    // 0xa30c80: DecompressPointer r1
    //     0xa30c80: add             x1, x1, HEAP, lsl #32
    // 0xa30c84: mov             x6, x1
    // 0xa30c88: stur            x6, [fp, #-0x28]
    // 0xa30c8c: LoadField: r1 = r4->field_13
    //     0xa30c8c: ldur            w1, [x4, #0x13]
    // 0xa30c90: DecompressPointer r1
    //     0xa30c90: add             x1, x1, HEAP, lsl #32
    // 0xa30c94: cmp             w1, NULL
    // 0xa30c98: b.ne            #0xa30ca4
    // 0xa30c9c: r7 = Null
    //     0xa30c9c: mov             x7, NULL
    // 0xa30ca0: b               #0xa30cb0
    // 0xa30ca4: LoadField: r2 = r1->field_13
    //     0xa30ca4: ldur            w2, [x1, #0x13]
    // 0xa30ca8: DecompressPointer r2
    //     0xa30ca8: add             x2, x2, HEAP, lsl #32
    // 0xa30cac: mov             x7, x2
    // 0xa30cb0: stur            x7, [fp, #-0x20]
    // 0xa30cb4: r1 = Null
    //     0xa30cb4: mov             x1, NULL
    // 0xa30cb8: r2 = 4
    //     0xa30cb8: movz            x2, #0x4
    // 0xa30cbc: r0 = AllocateArray()
    //     0xa30cbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa30cc0: stur            x0, [fp, #-0x48]
    // 0xa30cc4: r16 = "₹"
    //     0xa30cc4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0xa30cc8: ldr             x16, [x16, #0x360]
    // 0xa30ccc: StoreField: r0->field_f = r16
    //     0xa30ccc: stur            w16, [x0, #0xf]
    // 0xa30cd0: r1 = Function '<anonymous closure>': static.
    //     0xa30cd0: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0xa30cd4: ldr             x1, [x1, #0x1a0]
    // 0xa30cd8: r2 = Null
    //     0xa30cd8: mov             x2, NULL
    // 0xa30cdc: r0 = AllocateClosure()
    //     0xa30cdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa30ce0: mov             x3, x0
    // 0xa30ce4: r1 = Null
    //     0xa30ce4: mov             x1, NULL
    // 0xa30ce8: r2 = Null
    //     0xa30ce8: mov             x2, NULL
    // 0xa30cec: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xa30cec: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xa30cf0: r0 = NumberFormat._forPattern()
    //     0xa30cf0: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xa30cf4: mov             x1, x0
    // 0xa30cf8: ldur            x0, [fp, #-0x10]
    // 0xa30cfc: LoadField: r2 = r0->field_13
    //     0xa30cfc: ldur            w2, [x0, #0x13]
    // 0xa30d00: DecompressPointer r2
    //     0xa30d00: add             x2, x2, HEAP, lsl #32
    // 0xa30d04: cmp             w2, NULL
    // 0xa30d08: b.ne            #0xa30d14
    // 0xa30d0c: r2 = Null
    //     0xa30d0c: mov             x2, NULL
    // 0xa30d10: b               #0xa30d20
    // 0xa30d14: LoadField: r3 = r2->field_13
    //     0xa30d14: ldur            w3, [x2, #0x13]
    // 0xa30d18: DecompressPointer r3
    //     0xa30d18: add             x3, x3, HEAP, lsl #32
    // 0xa30d1c: mov             x2, x3
    // 0xa30d20: ldur            x8, [fp, #-0x18]
    // 0xa30d24: ldur            x3, [fp, #-8]
    // 0xa30d28: ldur            x4, [fp, #-0x40]
    // 0xa30d2c: ldur            x5, [fp, #-0x38]
    // 0xa30d30: ldur            x6, [fp, #-0x28]
    // 0xa30d34: ldur            x7, [fp, #-0x20]
    // 0xa30d38: r0 = format()
    //     0xa30d38: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xa30d3c: ldur            x1, [fp, #-0x48]
    // 0xa30d40: ArrayStore: r1[1] = r0  ; List_4
    //     0xa30d40: add             x25, x1, #0x13
    //     0xa30d44: str             w0, [x25]
    //     0xa30d48: tbz             w0, #0, #0xa30d64
    //     0xa30d4c: ldurb           w16, [x1, #-1]
    //     0xa30d50: ldurb           w17, [x0, #-1]
    //     0xa30d54: and             x16, x17, x16, lsr #2
    //     0xa30d58: tst             x16, HEAP, lsr #32
    //     0xa30d5c: b.eq            #0xa30d64
    //     0xa30d60: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa30d64: ldur            x16, [fp, #-0x48]
    // 0xa30d68: str             x16, [SP]
    // 0xa30d6c: r0 = _interpolate()
    //     0xa30d6c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa30d70: stur            x0, [fp, #-0x48]
    // 0xa30d74: r0 = ProductCustomisation()
    //     0xa30d74: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xa30d78: mov             x2, x0
    // 0xa30d7c: ldur            x0, [fp, #-0x40]
    // 0xa30d80: stur            x2, [fp, #-0x50]
    // 0xa30d84: StoreField: r2->field_b = r0
    //     0xa30d84: stur            w0, [x2, #0xb]
    // 0xa30d88: ldur            x0, [fp, #-0x38]
    // 0xa30d8c: StoreField: r2->field_f = r0
    //     0xa30d8c: stur            w0, [x2, #0xf]
    // 0xa30d90: ldur            x0, [fp, #-0x28]
    // 0xa30d94: ArrayStore: r2[0] = r0  ; List_4
    //     0xa30d94: stur            w0, [x2, #0x17]
    // 0xa30d98: ldur            x0, [fp, #-8]
    // 0xa30d9c: StoreField: r2->field_23 = r0
    //     0xa30d9c: stur            w0, [x2, #0x23]
    // 0xa30da0: ldur            x0, [fp, #-0x20]
    // 0xa30da4: StoreField: r2->field_27 = r0
    //     0xa30da4: stur            w0, [x2, #0x27]
    // 0xa30da8: ldur            x0, [fp, #-0x48]
    // 0xa30dac: StoreField: r2->field_2b = r0
    //     0xa30dac: stur            w0, [x2, #0x2b]
    // 0xa30db0: ldur            x0, [fp, #-0x18]
    // 0xa30db4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa30db4: ldur            w3, [x0, #0x17]
    // 0xa30db8: DecompressPointer r3
    //     0xa30db8: add             x3, x3, HEAP, lsl #32
    // 0xa30dbc: mov             x1, x3
    // 0xa30dc0: stur            x3, [fp, #-8]
    // 0xa30dc4: r0 = clear()
    //     0xa30dc4: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xa30dc8: ldur            x0, [fp, #-8]
    // 0xa30dcc: LoadField: r1 = r0->field_b
    //     0xa30dcc: ldur            w1, [x0, #0xb]
    // 0xa30dd0: LoadField: r2 = r0->field_f
    //     0xa30dd0: ldur            w2, [x0, #0xf]
    // 0xa30dd4: DecompressPointer r2
    //     0xa30dd4: add             x2, x2, HEAP, lsl #32
    // 0xa30dd8: LoadField: r3 = r2->field_b
    //     0xa30dd8: ldur            w3, [x2, #0xb]
    // 0xa30ddc: r2 = LoadInt32Instr(r1)
    //     0xa30ddc: sbfx            x2, x1, #1, #0x1f
    // 0xa30de0: stur            x2, [fp, #-0x30]
    // 0xa30de4: r1 = LoadInt32Instr(r3)
    //     0xa30de4: sbfx            x1, x3, #1, #0x1f
    // 0xa30de8: cmp             x2, x1
    // 0xa30dec: b.ne            #0xa30df8
    // 0xa30df0: mov             x1, x0
    // 0xa30df4: r0 = _growToNextCapacity()
    //     0xa30df4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa30df8: ldur            x4, [fp, #-0x10]
    // 0xa30dfc: ldur            x2, [fp, #-8]
    // 0xa30e00: ldur            x3, [fp, #-0x30]
    // 0xa30e04: add             x0, x3, #1
    // 0xa30e08: lsl             x1, x0, #1
    // 0xa30e0c: StoreField: r2->field_b = r1
    //     0xa30e0c: stur            w1, [x2, #0xb]
    // 0xa30e10: LoadField: r1 = r2->field_f
    //     0xa30e10: ldur            w1, [x2, #0xf]
    // 0xa30e14: DecompressPointer r1
    //     0xa30e14: add             x1, x1, HEAP, lsl #32
    // 0xa30e18: ldur            x0, [fp, #-0x50]
    // 0xa30e1c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa30e1c: add             x25, x1, x3, lsl #2
    //     0xa30e20: add             x25, x25, #0xf
    //     0xa30e24: str             w0, [x25]
    //     0xa30e28: tbz             w0, #0, #0xa30e44
    //     0xa30e2c: ldurb           w16, [x1, #-1]
    //     0xa30e30: ldurb           w17, [x0, #-1]
    //     0xa30e34: and             x16, x17, x16, lsr #2
    //     0xa30e38: tst             x16, HEAP, lsr #32
    //     0xa30e3c: b.eq            #0xa30e44
    //     0xa30e40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa30e44: LoadField: r0 = r4->field_f
    //     0xa30e44: ldur            w0, [x4, #0xf]
    // 0xa30e48: DecompressPointer r0
    //     0xa30e48: add             x0, x0, HEAP, lsl #32
    // 0xa30e4c: LoadField: r1 = r0->field_b
    //     0xa30e4c: ldur            w1, [x0, #0xb]
    // 0xa30e50: DecompressPointer r1
    //     0xa30e50: add             x1, x1, HEAP, lsl #32
    // 0xa30e54: cmp             w1, NULL
    // 0xa30e58: b.eq            #0xa30f68
    // 0xa30e5c: LoadField: r0 = r4->field_13
    //     0xa30e5c: ldur            w0, [x4, #0x13]
    // 0xa30e60: DecompressPointer r0
    //     0xa30e60: add             x0, x0, HEAP, lsl #32
    // 0xa30e64: cmp             w0, NULL
    // 0xa30e68: b.ne            #0xa30e74
    // 0xa30e6c: r0 = Null
    //     0xa30e6c: mov             x0, NULL
    // 0xa30e70: b               #0xa30e80
    // 0xa30e74: LoadField: r3 = r0->field_13
    //     0xa30e74: ldur            w3, [x0, #0x13]
    // 0xa30e78: DecompressPointer r3
    //     0xa30e78: add             x3, x3, HEAP, lsl #32
    // 0xa30e7c: mov             x0, x3
    // 0xa30e80: LoadField: r3 = r1->field_b
    //     0xa30e80: ldur            w3, [x1, #0xb]
    // 0xa30e84: DecompressPointer r3
    //     0xa30e84: add             x3, x3, HEAP, lsl #32
    // 0xa30e88: cmp             w3, NULL
    // 0xa30e8c: b.ne            #0xa30e98
    // 0xa30e90: r3 = Null
    //     0xa30e90: mov             x3, NULL
    // 0xa30e94: b               #0xa30ea4
    // 0xa30e98: LoadField: r5 = r3->field_7
    //     0xa30e98: ldur            w5, [x3, #7]
    // 0xa30e9c: DecompressPointer r5
    //     0xa30e9c: add             x5, x5, HEAP, lsl #32
    // 0xa30ea0: mov             x3, x5
    // 0xa30ea4: cmp             w3, NULL
    // 0xa30ea8: b.ne            #0xa30eb4
    // 0xa30eac: r5 = ""
    //     0xa30eac: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa30eb0: b               #0xa30eb8
    // 0xa30eb4: mov             x5, x3
    // 0xa30eb8: ldur            x3, [fp, #-0x18]
    // 0xa30ebc: LoadField: r6 = r1->field_1b
    //     0xa30ebc: ldur            w6, [x1, #0x1b]
    // 0xa30ec0: DecompressPointer r6
    //     0xa30ec0: add             x6, x6, HEAP, lsl #32
    // 0xa30ec4: stp             x0, x6, [SP, #0x10]
    // 0xa30ec8: stp             x2, x5, [SP]
    // 0xa30ecc: r4 = 0
    //     0xa30ecc: movz            x4, #0
    // 0xa30ed0: ldr             x0, [SP, #0x18]
    // 0xa30ed4: r16 = UnlinkedCall_0x613b5c
    //     0xa30ed4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a3f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa30ed8: add             x16, x16, #0x3f8
    // 0xa30edc: ldp             x5, lr, [x16]
    // 0xa30ee0: blr             lr
    // 0xa30ee4: ldur            x0, [fp, #-0x10]
    // 0xa30ee8: LoadField: r1 = r0->field_f
    //     0xa30ee8: ldur            w1, [x0, #0xf]
    // 0xa30eec: DecompressPointer r1
    //     0xa30eec: add             x1, x1, HEAP, lsl #32
    // 0xa30ef0: ldur            x0, [fp, #-0x18]
    // 0xa30ef4: stur            x1, [fp, #-8]
    // 0xa30ef8: LoadField: r2 = r0->field_f
    //     0xa30ef8: ldur            w2, [x0, #0xf]
    // 0xa30efc: DecompressPointer r2
    //     0xa30efc: add             x2, x2, HEAP, lsl #32
    // 0xa30f00: r0 = LoadClassIdInstr(r2)
    //     0xa30f00: ldur            x0, [x2, #-1]
    //     0xa30f04: ubfx            x0, x0, #0xc, #0x14
    // 0xa30f08: str             x2, [SP]
    // 0xa30f0c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xa30f0c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xa30f10: r0 = GDT[cid_x0 + 0x2700]()
    //     0xa30f10: movz            x17, #0x2700
    //     0xa30f14: add             lr, x0, x17
    //     0xa30f18: ldr             lr, [x21, lr, lsl #3]
    //     0xa30f1c: blr             lr
    // 0xa30f20: ldur            x1, [fp, #-8]
    // 0xa30f24: StoreField: r1->field_13 = r0
    //     0xa30f24: stur            w0, [x1, #0x13]
    //     0xa30f28: ldurb           w16, [x1, #-1]
    //     0xa30f2c: ldurb           w17, [x0, #-1]
    //     0xa30f30: and             x16, x17, x16, lsr #2
    //     0xa30f34: tst             x16, HEAP, lsr #32
    //     0xa30f38: b.eq            #0xa30f40
    //     0xa30f3c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa30f40: r0 = Null
    //     0xa30f40: mov             x0, NULL
    // 0xa30f44: LeaveFrame
    //     0xa30f44: mov             SP, fp
    //     0xa30f48: ldp             fp, lr, [SP], #0x10
    // 0xa30f4c: ret
    //     0xa30f4c: ret             
    // 0xa30f50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa30f50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa30f54: b               #0xa30a18
    // 0xa30f58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa30f58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa30f5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa30f5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa30f60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa30f60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa30f64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa30f64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa30f68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa30f68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, ProductCustomisation) {
    // ** addr: 0xa3aa20, size: 0xa4
    // 0xa3aa20: EnterFrame
    //     0xa3aa20: stp             fp, lr, [SP, #-0x10]!
    //     0xa3aa24: mov             fp, SP
    // 0xa3aa28: AllocStack(0x10)
    //     0xa3aa28: sub             SP, SP, #0x10
    // 0xa3aa2c: SetupParameters()
    //     0xa3aa2c: ldr             x0, [fp, #0x18]
    //     0xa3aa30: ldur            w1, [x0, #0x17]
    //     0xa3aa34: add             x1, x1, HEAP, lsl #32
    // 0xa3aa38: CheckStackOverflow
    //     0xa3aa38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3aa3c: cmp             SP, x16
    //     0xa3aa40: b.ls            #0xa3aab8
    // 0xa3aa44: ldr             x0, [fp, #0x10]
    // 0xa3aa48: LoadField: r2 = r0->field_b
    //     0xa3aa48: ldur            w2, [x0, #0xb]
    // 0xa3aa4c: DecompressPointer r2
    //     0xa3aa4c: add             x2, x2, HEAP, lsl #32
    // 0xa3aa50: LoadField: r0 = r1->field_b
    //     0xa3aa50: ldur            w0, [x1, #0xb]
    // 0xa3aa54: DecompressPointer r0
    //     0xa3aa54: add             x0, x0, HEAP, lsl #32
    // 0xa3aa58: LoadField: r1 = r0->field_f
    //     0xa3aa58: ldur            w1, [x0, #0xf]
    // 0xa3aa5c: DecompressPointer r1
    //     0xa3aa5c: add             x1, x1, HEAP, lsl #32
    // 0xa3aa60: LoadField: r0 = r1->field_b
    //     0xa3aa60: ldur            w0, [x1, #0xb]
    // 0xa3aa64: DecompressPointer r0
    //     0xa3aa64: add             x0, x0, HEAP, lsl #32
    // 0xa3aa68: cmp             w0, NULL
    // 0xa3aa6c: b.eq            #0xa3aac0
    // 0xa3aa70: LoadField: r1 = r0->field_b
    //     0xa3aa70: ldur            w1, [x0, #0xb]
    // 0xa3aa74: DecompressPointer r1
    //     0xa3aa74: add             x1, x1, HEAP, lsl #32
    // 0xa3aa78: cmp             w1, NULL
    // 0xa3aa7c: b.ne            #0xa3aa88
    // 0xa3aa80: r0 = Null
    //     0xa3aa80: mov             x0, NULL
    // 0xa3aa84: b               #0xa3aa90
    // 0xa3aa88: LoadField: r0 = r1->field_7
    //     0xa3aa88: ldur            w0, [x1, #7]
    // 0xa3aa8c: DecompressPointer r0
    //     0xa3aa8c: add             x0, x0, HEAP, lsl #32
    // 0xa3aa90: r1 = LoadClassIdInstr(r2)
    //     0xa3aa90: ldur            x1, [x2, #-1]
    //     0xa3aa94: ubfx            x1, x1, #0xc, #0x14
    // 0xa3aa98: stp             x0, x2, [SP]
    // 0xa3aa9c: mov             x0, x1
    // 0xa3aaa0: mov             lr, x0
    // 0xa3aaa4: ldr             lr, [x21, lr, lsl #3]
    // 0xa3aaa8: blr             lr
    // 0xa3aaac: LeaveFrame
    //     0xa3aaac: mov             SP, fp
    //     0xa3aab0: ldp             fp, lr, [SP], #0x10
    // 0xa3aab4: ret
    //     0xa3aab4: ret             
    // 0xa3aab8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3aab8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3aabc: b               #0xa3aa44
    // 0xa3aac0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3aac0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xa3aad0, size: 0x200
    // 0xa3aad0: EnterFrame
    //     0xa3aad0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3aad4: mov             fp, SP
    // 0xa3aad8: AllocStack(0x30)
    //     0xa3aad8: sub             SP, SP, #0x30
    // 0xa3aadc: SetupParameters()
    //     0xa3aadc: ldr             x0, [fp, #0x18]
    //     0xa3aae0: ldur            w1, [x0, #0x17]
    //     0xa3aae4: add             x1, x1, HEAP, lsl #32
    //     0xa3aae8: stur            x1, [fp, #-8]
    // 0xa3aaec: CheckStackOverflow
    //     0xa3aaec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3aaf0: cmp             SP, x16
    //     0xa3aaf4: b.ls            #0xa3acc4
    // 0xa3aaf8: r1 = 3
    //     0xa3aaf8: movz            x1, #0x3
    // 0xa3aafc: r0 = AllocateContext()
    //     0xa3aafc: bl              #0x16f6108  ; AllocateContextStub
    // 0xa3ab00: mov             x3, x0
    // 0xa3ab04: ldur            x2, [fp, #-8]
    // 0xa3ab08: stur            x3, [fp, #-0x10]
    // 0xa3ab0c: StoreField: r3->field_b = r2
    //     0xa3ab0c: stur            w2, [x3, #0xb]
    // 0xa3ab10: ldr             x0, [fp, #0x10]
    // 0xa3ab14: StoreField: r3->field_f = r0
    //     0xa3ab14: stur            w0, [x3, #0xf]
    // 0xa3ab18: LoadField: r0 = r2->field_f
    //     0xa3ab18: ldur            w0, [x2, #0xf]
    // 0xa3ab1c: DecompressPointer r0
    //     0xa3ab1c: add             x0, x0, HEAP, lsl #32
    // 0xa3ab20: LoadField: r1 = r0->field_b
    //     0xa3ab20: ldur            w1, [x0, #0xb]
    // 0xa3ab24: DecompressPointer r1
    //     0xa3ab24: add             x1, x1, HEAP, lsl #32
    // 0xa3ab28: cmp             w1, NULL
    // 0xa3ab2c: b.eq            #0xa3accc
    // 0xa3ab30: LoadField: r4 = r1->field_b
    //     0xa3ab30: ldur            w4, [x1, #0xb]
    // 0xa3ab34: DecompressPointer r4
    //     0xa3ab34: add             x4, x4, HEAP, lsl #32
    // 0xa3ab38: cmp             w4, NULL
    // 0xa3ab3c: b.ne            #0xa3ab48
    // 0xa3ab40: r4 = Null
    //     0xa3ab40: mov             x4, NULL
    // 0xa3ab44: b               #0xa3ab54
    // 0xa3ab48: LoadField: r5 = r4->field_2b
    //     0xa3ab48: ldur            w5, [x4, #0x2b]
    // 0xa3ab4c: DecompressPointer r5
    //     0xa3ab4c: add             x5, x5, HEAP, lsl #32
    // 0xa3ab50: mov             x4, x5
    // 0xa3ab54: cmp             w4, NULL
    // 0xa3ab58: b.ne            #0xa3ab60
    // 0xa3ab5c: r4 = false
    //     0xa3ab5c: add             x4, NULL, #0x30  ; false
    // 0xa3ab60: ArrayLoad: r5 = r0[0]  ; List_8
    //     0xa3ab60: ldur            x5, [x0, #0x17]
    // 0xa3ab64: LoadField: r6 = r1->field_13
    //     0xa3ab64: ldur            w6, [x1, #0x13]
    // 0xa3ab68: DecompressPointer r6
    //     0xa3ab68: add             x6, x6, HEAP, lsl #32
    // 0xa3ab6c: r0 = BoxInt64Instr(r5)
    //     0xa3ab6c: sbfiz           x0, x5, #1, #0x1f
    //     0xa3ab70: cmp             x5, x0, asr #1
    //     0xa3ab74: b.eq            #0xa3ab80
    //     0xa3ab78: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa3ab7c: stur            x5, [x0, #7]
    // 0xa3ab80: stp             x4, x6, [SP, #8]
    // 0xa3ab84: str             x0, [SP]
    // 0xa3ab88: r4 = 0
    //     0xa3ab88: movz            x4, #0
    // 0xa3ab8c: ldr             x0, [SP, #0x10]
    // 0xa3ab90: r16 = UnlinkedCall_0x613b5c
    //     0xa3ab90: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a3c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa3ab94: add             x16, x16, #0x3c0
    // 0xa3ab98: ldp             x5, lr, [x16]
    // 0xa3ab9c: blr             lr
    // 0xa3aba0: ldur            x0, [fp, #-8]
    // 0xa3aba4: LoadField: r1 = r0->field_f
    //     0xa3aba4: ldur            w1, [x0, #0xf]
    // 0xa3aba8: DecompressPointer r1
    //     0xa3aba8: add             x1, x1, HEAP, lsl #32
    // 0xa3abac: LoadField: r2 = r0->field_13
    //     0xa3abac: ldur            w2, [x0, #0x13]
    // 0xa3abb0: DecompressPointer r2
    //     0xa3abb0: add             x2, x2, HEAP, lsl #32
    // 0xa3abb4: cmp             w2, NULL
    // 0xa3abb8: b.ne            #0xa3abc4
    // 0xa3abbc: r2 = Null
    //     0xa3abbc: mov             x2, NULL
    // 0xa3abc0: b               #0xa3abd0
    // 0xa3abc4: LoadField: r3 = r2->field_13
    //     0xa3abc4: ldur            w3, [x2, #0x13]
    // 0xa3abc8: DecompressPointer r3
    //     0xa3abc8: add             x3, x3, HEAP, lsl #32
    // 0xa3abcc: mov             x2, x3
    // 0xa3abd0: cmp             w2, NULL
    // 0xa3abd4: b.ne            #0xa3abe0
    // 0xa3abd8: r3 = 0
    //     0xa3abd8: movz            x3, #0
    // 0xa3abdc: b               #0xa3abec
    // 0xa3abe0: r3 = LoadInt32Instr(r2)
    //     0xa3abe0: sbfx            x3, x2, #1, #0x1f
    //     0xa3abe4: tbz             w2, #0, #0xa3abec
    //     0xa3abe8: ldur            x3, [x2, #7]
    // 0xa3abec: ldur            x2, [fp, #-0x10]
    // 0xa3abf0: ArrayStore: r1[0] = r3  ; List_8
    //     0xa3abf0: stur            x3, [x1, #0x17]
    // 0xa3abf4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xa3abf4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa3abf8: ldr             x0, [x0]
    //     0xa3abfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa3ac00: cmp             w0, w16
    //     0xa3ac04: b.ne            #0xa3ac10
    //     0xa3ac08: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xa3ac0c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa3ac10: r1 = <CustomerResponse>
    //     0xa3ac10: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xa3ac14: ldr             x1, [x1, #0x5a8]
    // 0xa3ac18: stur            x0, [fp, #-0x18]
    // 0xa3ac1c: r0 = AllocateGrowableArray()
    //     0xa3ac1c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa3ac20: ldur            x2, [fp, #-0x18]
    // 0xa3ac24: StoreField: r0->field_f = r2
    //     0xa3ac24: stur            w2, [x0, #0xf]
    // 0xa3ac28: StoreField: r0->field_b = rZR
    //     0xa3ac28: stur            wzr, [x0, #0xb]
    // 0xa3ac2c: ldur            x3, [fp, #-0x10]
    // 0xa3ac30: StoreField: r3->field_13 = r0
    //     0xa3ac30: stur            w0, [x3, #0x13]
    //     0xa3ac34: ldurb           w16, [x3, #-1]
    //     0xa3ac38: ldurb           w17, [x0, #-1]
    //     0xa3ac3c: and             x16, x17, x16, lsr #2
    //     0xa3ac40: tst             x16, HEAP, lsr #32
    //     0xa3ac44: b.eq            #0xa3ac4c
    //     0xa3ac48: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xa3ac4c: r1 = <ProductCustomisation>
    //     0xa3ac4c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xa3ac50: ldr             x1, [x1, #0x370]
    // 0xa3ac54: r0 = AllocateGrowableArray()
    //     0xa3ac54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa3ac58: mov             x1, x0
    // 0xa3ac5c: ldur            x0, [fp, #-0x18]
    // 0xa3ac60: StoreField: r1->field_f = r0
    //     0xa3ac60: stur            w0, [x1, #0xf]
    // 0xa3ac64: StoreField: r1->field_b = rZR
    //     0xa3ac64: stur            wzr, [x1, #0xb]
    // 0xa3ac68: mov             x0, x1
    // 0xa3ac6c: ldur            x2, [fp, #-0x10]
    // 0xa3ac70: ArrayStore: r2[0] = r0  ; List_4
    //     0xa3ac70: stur            w0, [x2, #0x17]
    //     0xa3ac74: ldurb           w16, [x2, #-1]
    //     0xa3ac78: ldurb           w17, [x0, #-1]
    //     0xa3ac7c: and             x16, x17, x16, lsr #2
    //     0xa3ac80: tst             x16, HEAP, lsr #32
    //     0xa3ac84: b.eq            #0xa3ac8c
    //     0xa3ac88: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa3ac8c: ldur            x0, [fp, #-8]
    // 0xa3ac90: LoadField: r3 = r0->field_f
    //     0xa3ac90: ldur            w3, [x0, #0xf]
    // 0xa3ac94: DecompressPointer r3
    //     0xa3ac94: add             x3, x3, HEAP, lsl #32
    // 0xa3ac98: stur            x3, [fp, #-0x18]
    // 0xa3ac9c: r1 = Function '<anonymous closure>':.
    //     0xa3ac9c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a3d0] AnonymousClosure: (0xa309f0), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xa3aca0: ldr             x1, [x1, #0x3d0]
    // 0xa3aca4: r0 = AllocateClosure()
    //     0xa3aca4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa3aca8: ldur            x1, [fp, #-0x18]
    // 0xa3acac: mov             x2, x0
    // 0xa3acb0: r0 = setState()
    //     0xa3acb0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa3acb4: r0 = Null
    //     0xa3acb4: mov             x0, NULL
    // 0xa3acb8: LeaveFrame
    //     0xa3acb8: mov             SP, fp
    //     0xa3acbc: ldp             fp, lr, [SP], #0x10
    // 0xa3acc0: ret
    //     0xa3acc0: ret             
    // 0xa3acc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3acc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3acc8: b               #0xa3aaf8
    // 0xa3accc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3accc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _productBuildItem(/* No info */) {
    // ** addr: 0xa3acd0, size: 0x2d0
    // 0xa3acd0: EnterFrame
    //     0xa3acd0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3acd4: mov             fp, SP
    // 0xa3acd8: AllocStack(0x40)
    //     0xa3acd8: sub             SP, SP, #0x40
    // 0xa3acdc: SetupParameters(_CustomizationSingleSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xa3acdc: mov             x0, x1
    //     0xa3ace0: stur            x1, [fp, #-8]
    //     0xa3ace4: mov             x1, x3
    //     0xa3ace8: stur            x2, [fp, #-0x10]
    //     0xa3acec: stur            x3, [fp, #-0x18]
    // 0xa3acf0: CheckStackOverflow
    //     0xa3acf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3acf4: cmp             SP, x16
    //     0xa3acf8: b.ls            #0xa3af80
    // 0xa3acfc: r1 = 2
    //     0xa3acfc: movz            x1, #0x2
    // 0xa3ad00: r0 = AllocateContext()
    //     0xa3ad00: bl              #0x16f6108  ; AllocateContextStub
    // 0xa3ad04: mov             x2, x0
    // 0xa3ad08: ldur            x0, [fp, #-8]
    // 0xa3ad0c: stur            x2, [fp, #-0x20]
    // 0xa3ad10: StoreField: r2->field_f = r0
    //     0xa3ad10: stur            w0, [x2, #0xf]
    // 0xa3ad14: ldur            x1, [fp, #-0x10]
    // 0xa3ad18: StoreField: r2->field_13 = r1
    //     0xa3ad18: stur            w1, [x2, #0x13]
    // 0xa3ad1c: LoadField: r1 = r0->field_f
    //     0xa3ad1c: ldur            w1, [x0, #0xf]
    // 0xa3ad20: DecompressPointer r1
    //     0xa3ad20: add             x1, x1, HEAP, lsl #32
    // 0xa3ad24: cmp             w1, NULL
    // 0xa3ad28: b.eq            #0xa3af88
    // 0xa3ad2c: r0 = of()
    //     0xa3ad2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa3ad30: LoadField: r2 = r0->field_5b
    //     0xa3ad30: ldur            w2, [x0, #0x5b]
    // 0xa3ad34: DecompressPointer r2
    //     0xa3ad34: add             x2, x2, HEAP, lsl #32
    // 0xa3ad38: ldur            x3, [fp, #-8]
    // 0xa3ad3c: stur            x2, [fp, #-0x28]
    // 0xa3ad40: LoadField: r0 = r3->field_b
    //     0xa3ad40: ldur            w0, [x3, #0xb]
    // 0xa3ad44: DecompressPointer r0
    //     0xa3ad44: add             x0, x0, HEAP, lsl #32
    // 0xa3ad48: cmp             w0, NULL
    // 0xa3ad4c: b.eq            #0xa3af8c
    // 0xa3ad50: LoadField: r1 = r0->field_b
    //     0xa3ad50: ldur            w1, [x0, #0xb]
    // 0xa3ad54: DecompressPointer r1
    //     0xa3ad54: add             x1, x1, HEAP, lsl #32
    // 0xa3ad58: cmp             w1, NULL
    // 0xa3ad5c: b.ne            #0xa3ad6c
    // 0xa3ad60: ldur            x5, [fp, #-0x18]
    // 0xa3ad64: r0 = Null
    //     0xa3ad64: mov             x0, NULL
    // 0xa3ad68: b               #0xa3adc0
    // 0xa3ad6c: LoadField: r4 = r1->field_2f
    //     0xa3ad6c: ldur            w4, [x1, #0x2f]
    // 0xa3ad70: DecompressPointer r4
    //     0xa3ad70: add             x4, x4, HEAP, lsl #32
    // 0xa3ad74: cmp             w4, NULL
    // 0xa3ad78: b.ne            #0xa3ad88
    // 0xa3ad7c: ldur            x5, [fp, #-0x18]
    // 0xa3ad80: r0 = Null
    //     0xa3ad80: mov             x0, NULL
    // 0xa3ad84: b               #0xa3adc0
    // 0xa3ad88: ldur            x5, [fp, #-0x18]
    // 0xa3ad8c: LoadField: r0 = r4->field_b
    //     0xa3ad8c: ldur            w0, [x4, #0xb]
    // 0xa3ad90: r1 = LoadInt32Instr(r0)
    //     0xa3ad90: sbfx            x1, x0, #1, #0x1f
    // 0xa3ad94: mov             x0, x1
    // 0xa3ad98: mov             x1, x5
    // 0xa3ad9c: cmp             x1, x0
    // 0xa3ada0: b.hs            #0xa3af90
    // 0xa3ada4: LoadField: r0 = r4->field_f
    //     0xa3ada4: ldur            w0, [x4, #0xf]
    // 0xa3ada8: DecompressPointer r0
    //     0xa3ada8: add             x0, x0, HEAP, lsl #32
    // 0xa3adac: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa3adac: add             x16, x0, x5, lsl #2
    //     0xa3adb0: ldur            w1, [x16, #0xf]
    // 0xa3adb4: DecompressPointer r1
    //     0xa3adb4: add             x1, x1, HEAP, lsl #32
    // 0xa3adb8: LoadField: r0 = r1->field_f
    //     0xa3adb8: ldur            w0, [x1, #0xf]
    // 0xa3adbc: DecompressPointer r0
    //     0xa3adbc: add             x0, x0, HEAP, lsl #32
    // 0xa3adc0: cmp             w0, NULL
    // 0xa3adc4: b.ne            #0xa3adcc
    // 0xa3adc8: r0 = ""
    //     0xa3adc8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa3adcc: stur            x0, [fp, #-0x10]
    // 0xa3add0: LoadField: r1 = r3->field_f
    //     0xa3add0: ldur            w1, [x3, #0xf]
    // 0xa3add4: DecompressPointer r1
    //     0xa3add4: add             x1, x1, HEAP, lsl #32
    // 0xa3add8: cmp             w1, NULL
    // 0xa3addc: b.eq            #0xa3af94
    // 0xa3ade0: r0 = of()
    //     0xa3ade0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa3ade4: LoadField: r1 = r0->field_87
    //     0xa3ade4: ldur            w1, [x0, #0x87]
    // 0xa3ade8: DecompressPointer r1
    //     0xa3ade8: add             x1, x1, HEAP, lsl #32
    // 0xa3adec: LoadField: r0 = r1->field_2b
    //     0xa3adec: ldur            w0, [x1, #0x2b]
    // 0xa3adf0: DecompressPointer r0
    //     0xa3adf0: add             x0, x0, HEAP, lsl #32
    // 0xa3adf4: r16 = 14.000000
    //     0xa3adf4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa3adf8: ldr             x16, [x16, #0x1d8]
    // 0xa3adfc: str             x16, [SP]
    // 0xa3ae00: mov             x1, x0
    // 0xa3ae04: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xa3ae04: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xa3ae08: ldr             x4, [x4, #0x798]
    // 0xa3ae0c: r0 = copyWith()
    //     0xa3ae0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa3ae10: stur            x0, [fp, #-0x30]
    // 0xa3ae14: r0 = Text()
    //     0xa3ae14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa3ae18: mov             x2, x0
    // 0xa3ae1c: ldur            x0, [fp, #-0x10]
    // 0xa3ae20: stur            x2, [fp, #-0x38]
    // 0xa3ae24: StoreField: r2->field_b = r0
    //     0xa3ae24: stur            w0, [x2, #0xb]
    // 0xa3ae28: ldur            x0, [fp, #-0x30]
    // 0xa3ae2c: StoreField: r2->field_13 = r0
    //     0xa3ae2c: stur            w0, [x2, #0x13]
    // 0xa3ae30: ldur            x3, [fp, #-8]
    // 0xa3ae34: LoadField: r0 = r3->field_b
    //     0xa3ae34: ldur            w0, [x3, #0xb]
    // 0xa3ae38: DecompressPointer r0
    //     0xa3ae38: add             x0, x0, HEAP, lsl #32
    // 0xa3ae3c: cmp             w0, NULL
    // 0xa3ae40: b.eq            #0xa3af98
    // 0xa3ae44: LoadField: r1 = r0->field_b
    //     0xa3ae44: ldur            w1, [x0, #0xb]
    // 0xa3ae48: DecompressPointer r1
    //     0xa3ae48: add             x1, x1, HEAP, lsl #32
    // 0xa3ae4c: cmp             w1, NULL
    // 0xa3ae50: b.ne            #0xa3ae5c
    // 0xa3ae54: r0 = Null
    //     0xa3ae54: mov             x0, NULL
    // 0xa3ae58: b               #0xa3aeac
    // 0xa3ae5c: LoadField: r4 = r1->field_2f
    //     0xa3ae5c: ldur            w4, [x1, #0x2f]
    // 0xa3ae60: DecompressPointer r4
    //     0xa3ae60: add             x4, x4, HEAP, lsl #32
    // 0xa3ae64: cmp             w4, NULL
    // 0xa3ae68: b.ne            #0xa3ae74
    // 0xa3ae6c: r0 = Null
    //     0xa3ae6c: mov             x0, NULL
    // 0xa3ae70: b               #0xa3aeac
    // 0xa3ae74: ldur            x5, [fp, #-0x18]
    // 0xa3ae78: LoadField: r0 = r4->field_b
    //     0xa3ae78: ldur            w0, [x4, #0xb]
    // 0xa3ae7c: r1 = LoadInt32Instr(r0)
    //     0xa3ae7c: sbfx            x1, x0, #1, #0x1f
    // 0xa3ae80: mov             x0, x1
    // 0xa3ae84: mov             x1, x5
    // 0xa3ae88: cmp             x1, x0
    // 0xa3ae8c: b.hs            #0xa3af9c
    // 0xa3ae90: LoadField: r0 = r4->field_f
    //     0xa3ae90: ldur            w0, [x4, #0xf]
    // 0xa3ae94: DecompressPointer r0
    //     0xa3ae94: add             x0, x0, HEAP, lsl #32
    // 0xa3ae98: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa3ae98: add             x16, x0, x5, lsl #2
    //     0xa3ae9c: ldur            w1, [x16, #0xf]
    // 0xa3aea0: DecompressPointer r1
    //     0xa3aea0: add             x1, x1, HEAP, lsl #32
    // 0xa3aea4: LoadField: r0 = r1->field_f
    //     0xa3aea4: ldur            w0, [x1, #0xf]
    // 0xa3aea8: DecompressPointer r0
    //     0xa3aea8: add             x0, x0, HEAP, lsl #32
    // 0xa3aeac: cmp             w0, NULL
    // 0xa3aeb0: b.ne            #0xa3aebc
    // 0xa3aeb4: r4 = ""
    //     0xa3aeb4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa3aeb8: b               #0xa3aec0
    // 0xa3aebc: mov             x4, x0
    // 0xa3aec0: ldur            x0, [fp, #-0x28]
    // 0xa3aec4: stur            x4, [fp, #-0x30]
    // 0xa3aec8: LoadField: r5 = r3->field_13
    //     0xa3aec8: ldur            w5, [x3, #0x13]
    // 0xa3aecc: DecompressPointer r5
    //     0xa3aecc: add             x5, x5, HEAP, lsl #32
    // 0xa3aed0: stur            x5, [fp, #-0x10]
    // 0xa3aed4: r1 = <String>
    //     0xa3aed4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa3aed8: r0 = RadioListTile()
    //     0xa3aed8: bl              #0x997b1c  ; AllocateRadioListTileStub -> RadioListTile<X0> (size=0xa0)
    // 0xa3aedc: mov             x3, x0
    // 0xa3aee0: ldur            x0, [fp, #-0x30]
    // 0xa3aee4: stur            x3, [fp, #-8]
    // 0xa3aee8: StoreField: r3->field_f = r0
    //     0xa3aee8: stur            w0, [x3, #0xf]
    // 0xa3aeec: ldur            x0, [fp, #-0x10]
    // 0xa3aef0: StoreField: r3->field_13 = r0
    //     0xa3aef0: stur            w0, [x3, #0x13]
    // 0xa3aef4: ldur            x2, [fp, #-0x20]
    // 0xa3aef8: r1 = Function '<anonymous closure>':.
    //     0xa3aef8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a3b8] AnonymousClosure: (0xa3aad0), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xa3aefc: ldr             x1, [x1, #0x3b8]
    // 0xa3af00: r0 = AllocateClosure()
    //     0xa3af00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa3af04: mov             x1, x0
    // 0xa3af08: ldur            x0, [fp, #-8]
    // 0xa3af0c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa3af0c: stur            w1, [x0, #0x17]
    // 0xa3af10: r1 = false
    //     0xa3af10: add             x1, NULL, #0x30  ; false
    // 0xa3af14: StoreField: r0->field_1f = r1
    //     0xa3af14: stur            w1, [x0, #0x1f]
    // 0xa3af18: ldur            x2, [fp, #-0x28]
    // 0xa3af1c: StoreField: r0->field_23 = r2
    //     0xa3af1c: stur            w2, [x0, #0x23]
    // 0xa3af20: ldur            x2, [fp, #-0x38]
    // 0xa3af24: StoreField: r0->field_3b = r2
    //     0xa3af24: stur            w2, [x0, #0x3b]
    // 0xa3af28: StoreField: r0->field_4f = r1
    //     0xa3af28: stur            w1, [x0, #0x4f]
    // 0xa3af2c: StoreField: r0->field_57 = r1
    //     0xa3af2c: stur            w1, [x0, #0x57]
    // 0xa3af30: d0 = 1.000000
    //     0xa3af30: fmov            d0, #1.00000000
    // 0xa3af34: StoreField: r0->field_8b = d0
    //     0xa3af34: stur            d0, [x0, #0x8b]
    // 0xa3af38: StoreField: r0->field_83 = r1
    //     0xa3af38: stur            w1, [x0, #0x83]
    // 0xa3af3c: r2 = Instance__RadioType
    //     0xa3af3c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38050] Obj!_RadioType@d74141
    //     0xa3af40: ldr             x2, [x2, #0x50]
    // 0xa3af44: StoreField: r0->field_7b = r2
    //     0xa3af44: stur            w2, [x0, #0x7b]
    // 0xa3af48: StoreField: r0->field_87 = r1
    //     0xa3af48: stur            w1, [x0, #0x87]
    // 0xa3af4c: r1 = <FlexParentData>
    //     0xa3af4c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa3af50: ldr             x1, [x1, #0xe00]
    // 0xa3af54: r0 = Flexible()
    //     0xa3af54: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xa3af58: r1 = 1
    //     0xa3af58: movz            x1, #0x1
    // 0xa3af5c: StoreField: r0->field_13 = r1
    //     0xa3af5c: stur            x1, [x0, #0x13]
    // 0xa3af60: r1 = Instance_FlexFit
    //     0xa3af60: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0xa3af64: ldr             x1, [x1, #0xe20]
    // 0xa3af68: StoreField: r0->field_1b = r1
    //     0xa3af68: stur            w1, [x0, #0x1b]
    // 0xa3af6c: ldur            x1, [fp, #-8]
    // 0xa3af70: StoreField: r0->field_b = r1
    //     0xa3af70: stur            w1, [x0, #0xb]
    // 0xa3af74: LeaveFrame
    //     0xa3af74: mov             SP, fp
    //     0xa3af78: ldp             fp, lr, [SP], #0x10
    // 0xa3af7c: ret
    //     0xa3af7c: ret             
    // 0xa3af80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3af80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3af84: b               #0xa3acfc
    // 0xa3af88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3af88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3af8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3af8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3af90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa3af90: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa3af94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3af94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3af98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa3af98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa3af9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa3af9c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbd9fb0, size: 0x318
    // 0xbd9fb0: EnterFrame
    //     0xbd9fb0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9fb4: mov             fp, SP
    // 0xbd9fb8: AllocStack(0x40)
    //     0xbd9fb8: sub             SP, SP, #0x40
    // 0xbd9fbc: SetupParameters(_CustomizationSingleSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbd9fbc: mov             x0, x1
    //     0xbd9fc0: stur            x1, [fp, #-8]
    //     0xbd9fc4: mov             x1, x2
    //     0xbd9fc8: stur            x2, [fp, #-0x10]
    // 0xbd9fcc: CheckStackOverflow
    //     0xbd9fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9fd0: cmp             SP, x16
    //     0xbd9fd4: b.ls            #0xbda2b8
    // 0xbd9fd8: r1 = 1
    //     0xbd9fd8: movz            x1, #0x1
    // 0xbd9fdc: r0 = AllocateContext()
    //     0xbd9fdc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd9fe0: mov             x3, x0
    // 0xbd9fe4: ldur            x0, [fp, #-8]
    // 0xbd9fe8: stur            x3, [fp, #-0x20]
    // 0xbd9fec: StoreField: r3->field_f = r0
    //     0xbd9fec: stur            w0, [x3, #0xf]
    // 0xbd9ff0: LoadField: r1 = r0->field_b
    //     0xbd9ff0: ldur            w1, [x0, #0xb]
    // 0xbd9ff4: DecompressPointer r1
    //     0xbd9ff4: add             x1, x1, HEAP, lsl #32
    // 0xbd9ff8: cmp             w1, NULL
    // 0xbd9ffc: b.eq            #0xbda2c0
    // 0xbda000: LoadField: r2 = r1->field_b
    //     0xbda000: ldur            w2, [x1, #0xb]
    // 0xbda004: DecompressPointer r2
    //     0xbda004: add             x2, x2, HEAP, lsl #32
    // 0xbda008: cmp             w2, NULL
    // 0xbda00c: b.ne            #0xbda018
    // 0xbda010: r1 = Null
    //     0xbda010: mov             x1, NULL
    // 0xbda014: b               #0xbda020
    // 0xbda018: LoadField: r1 = r2->field_2b
    //     0xbda018: ldur            w1, [x2, #0x2b]
    // 0xbda01c: DecompressPointer r1
    //     0xbda01c: add             x1, x1, HEAP, lsl #32
    // 0xbda020: cmp             w1, NULL
    // 0xbda024: b.eq            #0xbda080
    // 0xbda028: tbnz            w1, #4, #0xbda080
    // 0xbda02c: cmp             w2, NULL
    // 0xbda030: b.ne            #0xbda03c
    // 0xbda034: r4 = Null
    //     0xbda034: mov             x4, NULL
    // 0xbda038: b               #0xbda048
    // 0xbda03c: LoadField: r1 = r2->field_1b
    //     0xbda03c: ldur            w1, [x2, #0x1b]
    // 0xbda040: DecompressPointer r1
    //     0xbda040: add             x1, x1, HEAP, lsl #32
    // 0xbda044: mov             x4, x1
    // 0xbda048: stur            x4, [fp, #-0x18]
    // 0xbda04c: r1 = Null
    //     0xbda04c: mov             x1, NULL
    // 0xbda050: r2 = 4
    //     0xbda050: movz            x2, #0x4
    // 0xbda054: r0 = AllocateArray()
    //     0xbda054: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbda058: mov             x1, x0
    // 0xbda05c: ldur            x0, [fp, #-0x18]
    // 0xbda060: StoreField: r1->field_f = r0
    //     0xbda060: stur            w0, [x1, #0xf]
    // 0xbda064: r16 = " *"
    //     0xbda064: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xbda068: ldr             x16, [x16, #0xfc8]
    // 0xbda06c: StoreField: r1->field_13 = r16
    //     0xbda06c: stur            w16, [x1, #0x13]
    // 0xbda070: str             x1, [SP]
    // 0xbda074: r0 = _interpolate()
    //     0xbda074: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbda078: mov             x2, x0
    // 0xbda07c: b               #0xbda0a4
    // 0xbda080: cmp             w2, NULL
    // 0xbda084: b.ne            #0xbda090
    // 0xbda088: r0 = Null
    //     0xbda088: mov             x0, NULL
    // 0xbda08c: b               #0xbda098
    // 0xbda090: LoadField: r0 = r2->field_1b
    //     0xbda090: ldur            w0, [x2, #0x1b]
    // 0xbda094: DecompressPointer r0
    //     0xbda094: add             x0, x0, HEAP, lsl #32
    // 0xbda098: str             x0, [SP]
    // 0xbda09c: r0 = _interpolateSingle()
    //     0xbda09c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbda0a0: mov             x2, x0
    // 0xbda0a4: ldur            x0, [fp, #-8]
    // 0xbda0a8: ldur            x1, [fp, #-0x10]
    // 0xbda0ac: stur            x2, [fp, #-0x18]
    // 0xbda0b0: r0 = of()
    //     0xbda0b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbda0b4: LoadField: r1 = r0->field_87
    //     0xbda0b4: ldur            w1, [x0, #0x87]
    // 0xbda0b8: DecompressPointer r1
    //     0xbda0b8: add             x1, x1, HEAP, lsl #32
    // 0xbda0bc: LoadField: r0 = r1->field_7
    //     0xbda0bc: ldur            w0, [x1, #7]
    // 0xbda0c0: DecompressPointer r0
    //     0xbda0c0: add             x0, x0, HEAP, lsl #32
    // 0xbda0c4: stur            x0, [fp, #-0x10]
    // 0xbda0c8: r1 = Instance_Color
    //     0xbda0c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbda0cc: d0 = 0.700000
    //     0xbda0cc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbda0d0: ldr             d0, [x17, #0xf48]
    // 0xbda0d4: r0 = withOpacity()
    //     0xbda0d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbda0d8: r16 = 14.000000
    //     0xbda0d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbda0dc: ldr             x16, [x16, #0x1d8]
    // 0xbda0e0: stp             x0, x16, [SP]
    // 0xbda0e4: ldur            x1, [fp, #-0x10]
    // 0xbda0e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbda0e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbda0ec: ldr             x4, [x4, #0xaa0]
    // 0xbda0f0: r0 = copyWith()
    //     0xbda0f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbda0f4: stur            x0, [fp, #-0x10]
    // 0xbda0f8: r0 = Text()
    //     0xbda0f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbda0fc: mov             x1, x0
    // 0xbda100: ldur            x0, [fp, #-0x18]
    // 0xbda104: stur            x1, [fp, #-0x28]
    // 0xbda108: StoreField: r1->field_b = r0
    //     0xbda108: stur            w0, [x1, #0xb]
    // 0xbda10c: ldur            x0, [fp, #-0x10]
    // 0xbda110: StoreField: r1->field_13 = r0
    //     0xbda110: stur            w0, [x1, #0x13]
    // 0xbda114: r0 = Padding()
    //     0xbda114: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbda118: mov             x3, x0
    // 0xbda11c: r0 = Instance_EdgeInsets
    //     0xbda11c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbda120: ldr             x0, [x0, #0x980]
    // 0xbda124: stur            x3, [fp, #-0x10]
    // 0xbda128: StoreField: r3->field_f = r0
    //     0xbda128: stur            w0, [x3, #0xf]
    // 0xbda12c: ldur            x0, [fp, #-0x28]
    // 0xbda130: StoreField: r3->field_b = r0
    //     0xbda130: stur            w0, [x3, #0xb]
    // 0xbda134: ldur            x0, [fp, #-8]
    // 0xbda138: LoadField: r1 = r0->field_b
    //     0xbda138: ldur            w1, [x0, #0xb]
    // 0xbda13c: DecompressPointer r1
    //     0xbda13c: add             x1, x1, HEAP, lsl #32
    // 0xbda140: cmp             w1, NULL
    // 0xbda144: b.eq            #0xbda2c4
    // 0xbda148: LoadField: r0 = r1->field_b
    //     0xbda148: ldur            w0, [x1, #0xb]
    // 0xbda14c: DecompressPointer r0
    //     0xbda14c: add             x0, x0, HEAP, lsl #32
    // 0xbda150: cmp             w0, NULL
    // 0xbda154: b.ne            #0xbda160
    // 0xbda158: r0 = Null
    //     0xbda158: mov             x0, NULL
    // 0xbda15c: b               #0xbda17c
    // 0xbda160: LoadField: r1 = r0->field_2f
    //     0xbda160: ldur            w1, [x0, #0x2f]
    // 0xbda164: DecompressPointer r1
    //     0xbda164: add             x1, x1, HEAP, lsl #32
    // 0xbda168: cmp             w1, NULL
    // 0xbda16c: b.ne            #0xbda178
    // 0xbda170: r0 = Null
    //     0xbda170: mov             x0, NULL
    // 0xbda174: b               #0xbda17c
    // 0xbda178: LoadField: r0 = r1->field_b
    //     0xbda178: ldur            w0, [x1, #0xb]
    // 0xbda17c: cmp             w0, NULL
    // 0xbda180: b.ne            #0xbda18c
    // 0xbda184: r0 = 0
    //     0xbda184: movz            x0, #0
    // 0xbda188: b               #0xbda194
    // 0xbda18c: r1 = LoadInt32Instr(r0)
    //     0xbda18c: sbfx            x1, x0, #1, #0x1f
    // 0xbda190: mov             x0, x1
    // 0xbda194: ldur            x2, [fp, #-0x20]
    // 0xbda198: stur            x0, [fp, #-0x30]
    // 0xbda19c: r1 = Function '<anonymous closure>':.
    //     0xbda19c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a3a8] AnonymousClosure: (0xbda2c8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::build (0xbd9fb0)
    //     0xbda1a0: ldr             x1, [x1, #0x3a8]
    // 0xbda1a4: r0 = AllocateClosure()
    //     0xbda1a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbda1a8: r1 = Function '<anonymous closure>':.
    //     0xbda1a8: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a3b0] AnonymousClosure: (0x9bccb4), in [package:customer_app/app/presentation/views/line/post_order/order_success/order_success_widget.dart] OrderSuccessWidget::body (0x1506c94)
    //     0xbda1ac: ldr             x1, [x1, #0x3b0]
    // 0xbda1b0: r2 = Null
    //     0xbda1b0: mov             x2, NULL
    // 0xbda1b4: stur            x0, [fp, #-8]
    // 0xbda1b8: r0 = AllocateClosure()
    //     0xbda1b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbda1bc: stur            x0, [fp, #-0x18]
    // 0xbda1c0: r0 = ListView()
    //     0xbda1c0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbda1c4: stur            x0, [fp, #-0x20]
    // 0xbda1c8: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbda1c8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbda1cc: ldr             x16, [x16, #0x1c8]
    // 0xbda1d0: r30 = true
    //     0xbda1d0: add             lr, NULL, #0x20  ; true
    // 0xbda1d4: stp             lr, x16, [SP]
    // 0xbda1d8: mov             x1, x0
    // 0xbda1dc: ldur            x2, [fp, #-8]
    // 0xbda1e0: ldur            x3, [fp, #-0x30]
    // 0xbda1e4: ldur            x5, [fp, #-0x18]
    // 0xbda1e8: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xbda1e8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xbda1ec: ldr             x4, [x4, #0x968]
    // 0xbda1f0: r0 = ListView.separated()
    //     0xbda1f0: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbda1f4: r0 = Padding()
    //     0xbda1f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbda1f8: mov             x3, x0
    // 0xbda1fc: r0 = Instance_EdgeInsets
    //     0xbda1fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbda200: ldr             x0, [x0, #0x1f0]
    // 0xbda204: stur            x3, [fp, #-8]
    // 0xbda208: StoreField: r3->field_f = r0
    //     0xbda208: stur            w0, [x3, #0xf]
    // 0xbda20c: ldur            x0, [fp, #-0x20]
    // 0xbda210: StoreField: r3->field_b = r0
    //     0xbda210: stur            w0, [x3, #0xb]
    // 0xbda214: r1 = Null
    //     0xbda214: mov             x1, NULL
    // 0xbda218: r2 = 4
    //     0xbda218: movz            x2, #0x4
    // 0xbda21c: r0 = AllocateArray()
    //     0xbda21c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbda220: mov             x2, x0
    // 0xbda224: ldur            x0, [fp, #-0x10]
    // 0xbda228: stur            x2, [fp, #-0x18]
    // 0xbda22c: StoreField: r2->field_f = r0
    //     0xbda22c: stur            w0, [x2, #0xf]
    // 0xbda230: ldur            x0, [fp, #-8]
    // 0xbda234: StoreField: r2->field_13 = r0
    //     0xbda234: stur            w0, [x2, #0x13]
    // 0xbda238: r1 = <Widget>
    //     0xbda238: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbda23c: r0 = AllocateGrowableArray()
    //     0xbda23c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbda240: mov             x1, x0
    // 0xbda244: ldur            x0, [fp, #-0x18]
    // 0xbda248: stur            x1, [fp, #-8]
    // 0xbda24c: StoreField: r1->field_f = r0
    //     0xbda24c: stur            w0, [x1, #0xf]
    // 0xbda250: r0 = 4
    //     0xbda250: movz            x0, #0x4
    // 0xbda254: StoreField: r1->field_b = r0
    //     0xbda254: stur            w0, [x1, #0xb]
    // 0xbda258: r0 = Column()
    //     0xbda258: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbda25c: r1 = Instance_Axis
    //     0xbda25c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbda260: StoreField: r0->field_f = r1
    //     0xbda260: stur            w1, [x0, #0xf]
    // 0xbda264: r1 = Instance_MainAxisAlignment
    //     0xbda264: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbda268: ldr             x1, [x1, #0xa08]
    // 0xbda26c: StoreField: r0->field_13 = r1
    //     0xbda26c: stur            w1, [x0, #0x13]
    // 0xbda270: r1 = Instance_MainAxisSize
    //     0xbda270: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbda274: ldr             x1, [x1, #0xa10]
    // 0xbda278: ArrayStore: r0[0] = r1  ; List_4
    //     0xbda278: stur            w1, [x0, #0x17]
    // 0xbda27c: r1 = Instance_CrossAxisAlignment
    //     0xbda27c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbda280: ldr             x1, [x1, #0x890]
    // 0xbda284: StoreField: r0->field_1b = r1
    //     0xbda284: stur            w1, [x0, #0x1b]
    // 0xbda288: r1 = Instance_VerticalDirection
    //     0xbda288: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbda28c: ldr             x1, [x1, #0xa20]
    // 0xbda290: StoreField: r0->field_23 = r1
    //     0xbda290: stur            w1, [x0, #0x23]
    // 0xbda294: r1 = Instance_Clip
    //     0xbda294: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbda298: ldr             x1, [x1, #0x38]
    // 0xbda29c: StoreField: r0->field_2b = r1
    //     0xbda29c: stur            w1, [x0, #0x2b]
    // 0xbda2a0: StoreField: r0->field_2f = rZR
    //     0xbda2a0: stur            xzr, [x0, #0x2f]
    // 0xbda2a4: ldur            x1, [fp, #-8]
    // 0xbda2a8: StoreField: r0->field_b = r1
    //     0xbda2a8: stur            w1, [x0, #0xb]
    // 0xbda2ac: LeaveFrame
    //     0xbda2ac: mov             SP, fp
    //     0xbda2b0: ldp             fp, lr, [SP], #0x10
    // 0xbda2b4: ret
    //     0xbda2b4: ret             
    // 0xbda2b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbda2b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbda2bc: b               #0xbd9fd8
    // 0xbda2c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbda2c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbda2c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbda2c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbda2c8, size: 0x430
    // 0xbda2c8: EnterFrame
    //     0xbda2c8: stp             fp, lr, [SP, #-0x10]!
    //     0xbda2cc: mov             fp, SP
    // 0xbda2d0: AllocStack(0x48)
    //     0xbda2d0: sub             SP, SP, #0x48
    // 0xbda2d4: SetupParameters()
    //     0xbda2d4: ldr             x0, [fp, #0x20]
    //     0xbda2d8: ldur            w2, [x0, #0x17]
    //     0xbda2dc: add             x2, x2, HEAP, lsl #32
    //     0xbda2e0: stur            x2, [fp, #-8]
    // 0xbda2e4: CheckStackOverflow
    //     0xbda2e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbda2e8: cmp             SP, x16
    //     0xbda2ec: b.ls            #0xbda6dc
    // 0xbda2f0: r1 = Instance_Color
    //     0xbda2f0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b8] Obj!Color@d6ad11
    //     0xbda2f4: ldr             x1, [x1, #0x7b8]
    // 0xbda2f8: d0 = 0.300000
    //     0xbda2f8: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbda2fc: ldr             d0, [x17, #0x658]
    // 0xbda300: r0 = withOpacity()
    //     0xbda300: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbda304: r16 = 1.000000
    //     0xbda304: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbda308: str             x16, [SP]
    // 0xbda30c: mov             x2, x0
    // 0xbda310: r1 = Null
    //     0xbda310: mov             x1, NULL
    // 0xbda314: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbda314: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbda318: ldr             x4, [x4, #0x108]
    // 0xbda31c: r0 = Border.all()
    //     0xbda31c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbda320: stur            x0, [fp, #-0x10]
    // 0xbda324: r0 = BoxDecoration()
    //     0xbda324: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbda328: mov             x4, x0
    // 0xbda32c: ldur            x0, [fp, #-0x10]
    // 0xbda330: stur            x4, [fp, #-0x20]
    // 0xbda334: StoreField: r4->field_f = r0
    //     0xbda334: stur            w0, [x4, #0xf]
    // 0xbda338: r0 = Instance_BoxShape
    //     0xbda338: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbda33c: ldr             x0, [x0, #0x80]
    // 0xbda340: StoreField: r4->field_23 = r0
    //     0xbda340: stur            w0, [x4, #0x23]
    // 0xbda344: ldur            x5, [fp, #-8]
    // 0xbda348: LoadField: r2 = r5->field_f
    //     0xbda348: ldur            w2, [x5, #0xf]
    // 0xbda34c: DecompressPointer r2
    //     0xbda34c: add             x2, x2, HEAP, lsl #32
    // 0xbda350: LoadField: r0 = r2->field_b
    //     0xbda350: ldur            w0, [x2, #0xb]
    // 0xbda354: DecompressPointer r0
    //     0xbda354: add             x0, x0, HEAP, lsl #32
    // 0xbda358: cmp             w0, NULL
    // 0xbda35c: b.eq            #0xbda6e4
    // 0xbda360: LoadField: r1 = r0->field_b
    //     0xbda360: ldur            w1, [x0, #0xb]
    // 0xbda364: DecompressPointer r1
    //     0xbda364: add             x1, x1, HEAP, lsl #32
    // 0xbda368: cmp             w1, NULL
    // 0xbda36c: b.ne            #0xbda37c
    // 0xbda370: ldr             x6, [fp, #0x10]
    // 0xbda374: r0 = Null
    //     0xbda374: mov             x0, NULL
    // 0xbda378: b               #0xbda3d8
    // 0xbda37c: LoadField: r3 = r1->field_2f
    //     0xbda37c: ldur            w3, [x1, #0x2f]
    // 0xbda380: DecompressPointer r3
    //     0xbda380: add             x3, x3, HEAP, lsl #32
    // 0xbda384: cmp             w3, NULL
    // 0xbda388: b.ne            #0xbda398
    // 0xbda38c: ldr             x6, [fp, #0x10]
    // 0xbda390: r0 = Null
    //     0xbda390: mov             x0, NULL
    // 0xbda394: b               #0xbda3d8
    // 0xbda398: ldr             x6, [fp, #0x10]
    // 0xbda39c: LoadField: r0 = r3->field_b
    //     0xbda39c: ldur            w0, [x3, #0xb]
    // 0xbda3a0: r7 = LoadInt32Instr(r6)
    //     0xbda3a0: sbfx            x7, x6, #1, #0x1f
    //     0xbda3a4: tbz             w6, #0, #0xbda3ac
    //     0xbda3a8: ldur            x7, [x6, #7]
    // 0xbda3ac: r1 = LoadInt32Instr(r0)
    //     0xbda3ac: sbfx            x1, x0, #1, #0x1f
    // 0xbda3b0: mov             x0, x1
    // 0xbda3b4: mov             x1, x7
    // 0xbda3b8: cmp             x1, x0
    // 0xbda3bc: b.hs            #0xbda6e8
    // 0xbda3c0: LoadField: r0 = r3->field_f
    //     0xbda3c0: ldur            w0, [x3, #0xf]
    // 0xbda3c4: DecompressPointer r0
    //     0xbda3c4: add             x0, x0, HEAP, lsl #32
    // 0xbda3c8: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xbda3c8: add             x16, x0, x7, lsl #2
    //     0xbda3cc: ldur            w1, [x16, #0xf]
    // 0xbda3d0: DecompressPointer r1
    //     0xbda3d0: add             x1, x1, HEAP, lsl #32
    // 0xbda3d4: mov             x0, x1
    // 0xbda3d8: r7 = LoadInt32Instr(r6)
    //     0xbda3d8: sbfx            x7, x6, #1, #0x1f
    //     0xbda3dc: tbz             w6, #0, #0xbda3e4
    //     0xbda3e0: ldur            x7, [x6, #7]
    // 0xbda3e4: mov             x1, x2
    // 0xbda3e8: mov             x2, x0
    // 0xbda3ec: mov             x3, x7
    // 0xbda3f0: stur            x7, [fp, #-0x18]
    // 0xbda3f4: r0 = _productBuildItem()
    //     0xbda3f4: bl              #0xa3acd0  ; [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem
    // 0xbda3f8: mov             x3, x0
    // 0xbda3fc: ldur            x0, [fp, #-8]
    // 0xbda400: stur            x3, [fp, #-0x28]
    // 0xbda404: LoadField: r1 = r0->field_f
    //     0xbda404: ldur            w1, [x0, #0xf]
    // 0xbda408: DecompressPointer r1
    //     0xbda408: add             x1, x1, HEAP, lsl #32
    // 0xbda40c: LoadField: r0 = r1->field_b
    //     0xbda40c: ldur            w0, [x1, #0xb]
    // 0xbda410: DecompressPointer r0
    //     0xbda410: add             x0, x0, HEAP, lsl #32
    // 0xbda414: cmp             w0, NULL
    // 0xbda418: b.eq            #0xbda6ec
    // 0xbda41c: LoadField: r4 = r0->field_b
    //     0xbda41c: ldur            w4, [x0, #0xb]
    // 0xbda420: DecompressPointer r4
    //     0xbda420: add             x4, x4, HEAP, lsl #32
    // 0xbda424: stur            x4, [fp, #-0x10]
    // 0xbda428: cmp             w4, NULL
    // 0xbda42c: b.ne            #0xbda43c
    // 0xbda430: ldur            x5, [fp, #-0x18]
    // 0xbda434: r0 = Null
    //     0xbda434: mov             x0, NULL
    // 0xbda438: b               #0xbda490
    // 0xbda43c: LoadField: r2 = r4->field_2f
    //     0xbda43c: ldur            w2, [x4, #0x2f]
    // 0xbda440: DecompressPointer r2
    //     0xbda440: add             x2, x2, HEAP, lsl #32
    // 0xbda444: cmp             w2, NULL
    // 0xbda448: b.ne            #0xbda458
    // 0xbda44c: ldur            x5, [fp, #-0x18]
    // 0xbda450: r0 = Null
    //     0xbda450: mov             x0, NULL
    // 0xbda454: b               #0xbda490
    // 0xbda458: ldur            x5, [fp, #-0x18]
    // 0xbda45c: LoadField: r0 = r2->field_b
    //     0xbda45c: ldur            w0, [x2, #0xb]
    // 0xbda460: r1 = LoadInt32Instr(r0)
    //     0xbda460: sbfx            x1, x0, #1, #0x1f
    // 0xbda464: mov             x0, x1
    // 0xbda468: mov             x1, x5
    // 0xbda46c: cmp             x1, x0
    // 0xbda470: b.hs            #0xbda6f0
    // 0xbda474: LoadField: r0 = r2->field_f
    //     0xbda474: ldur            w0, [x2, #0xf]
    // 0xbda478: DecompressPointer r0
    //     0xbda478: add             x0, x0, HEAP, lsl #32
    // 0xbda47c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbda47c: add             x16, x0, x5, lsl #2
    //     0xbda480: ldur            w1, [x16, #0xf]
    // 0xbda484: DecompressPointer r1
    //     0xbda484: add             x1, x1, HEAP, lsl #32
    // 0xbda488: LoadField: r0 = r1->field_13
    //     0xbda488: ldur            w0, [x1, #0x13]
    // 0xbda48c: DecompressPointer r0
    //     0xbda48c: add             x0, x0, HEAP, lsl #32
    // 0xbda490: cbnz            w0, #0xbda49c
    // 0xbda494: r6 = false
    //     0xbda494: add             x6, NULL, #0x30  ; false
    // 0xbda498: b               #0xbda4a0
    // 0xbda49c: r6 = true
    //     0xbda49c: add             x6, NULL, #0x20  ; true
    // 0xbda4a0: stur            x6, [fp, #-8]
    // 0xbda4a4: r1 = Null
    //     0xbda4a4: mov             x1, NULL
    // 0xbda4a8: r2 = 4
    //     0xbda4a8: movz            x2, #0x4
    // 0xbda4ac: r0 = AllocateArray()
    //     0xbda4ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbda4b0: mov             x2, x0
    // 0xbda4b4: r16 = "+ "
    //     0xbda4b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xbda4b8: ldr             x16, [x16, #0xc30]
    // 0xbda4bc: StoreField: r2->field_f = r16
    //     0xbda4bc: stur            w16, [x2, #0xf]
    // 0xbda4c0: ldur            x0, [fp, #-0x10]
    // 0xbda4c4: cmp             w0, NULL
    // 0xbda4c8: b.ne            #0xbda4d4
    // 0xbda4cc: r3 = Null
    //     0xbda4cc: mov             x3, NULL
    // 0xbda4d0: b               #0xbda528
    // 0xbda4d4: LoadField: r3 = r0->field_2f
    //     0xbda4d4: ldur            w3, [x0, #0x2f]
    // 0xbda4d8: DecompressPointer r3
    //     0xbda4d8: add             x3, x3, HEAP, lsl #32
    // 0xbda4dc: cmp             w3, NULL
    // 0xbda4e0: b.ne            #0xbda4ec
    // 0xbda4e4: r0 = Null
    //     0xbda4e4: mov             x0, NULL
    // 0xbda4e8: b               #0xbda524
    // 0xbda4ec: ldur            x4, [fp, #-0x18]
    // 0xbda4f0: LoadField: r0 = r3->field_b
    //     0xbda4f0: ldur            w0, [x3, #0xb]
    // 0xbda4f4: r1 = LoadInt32Instr(r0)
    //     0xbda4f4: sbfx            x1, x0, #1, #0x1f
    // 0xbda4f8: mov             x0, x1
    // 0xbda4fc: mov             x1, x4
    // 0xbda500: cmp             x1, x0
    // 0xbda504: b.hs            #0xbda6f4
    // 0xbda508: LoadField: r0 = r3->field_f
    //     0xbda508: ldur            w0, [x3, #0xf]
    // 0xbda50c: DecompressPointer r0
    //     0xbda50c: add             x0, x0, HEAP, lsl #32
    // 0xbda510: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbda510: add             x16, x0, x4, lsl #2
    //     0xbda514: ldur            w1, [x16, #0xf]
    // 0xbda518: DecompressPointer r1
    //     0xbda518: add             x1, x1, HEAP, lsl #32
    // 0xbda51c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xbda51c: ldur            w0, [x1, #0x17]
    // 0xbda520: DecompressPointer r0
    //     0xbda520: add             x0, x0, HEAP, lsl #32
    // 0xbda524: mov             x3, x0
    // 0xbda528: ldur            x0, [fp, #-0x28]
    // 0xbda52c: ldur            x1, [fp, #-8]
    // 0xbda530: StoreField: r2->field_13 = r3
    //     0xbda530: stur            w3, [x2, #0x13]
    // 0xbda534: str             x2, [SP]
    // 0xbda538: r0 = _interpolate()
    //     0xbda538: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbda53c: ldr             x1, [fp, #0x18]
    // 0xbda540: stur            x0, [fp, #-0x10]
    // 0xbda544: r0 = of()
    //     0xbda544: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbda548: LoadField: r1 = r0->field_87
    //     0xbda548: ldur            w1, [x0, #0x87]
    // 0xbda54c: DecompressPointer r1
    //     0xbda54c: add             x1, x1, HEAP, lsl #32
    // 0xbda550: LoadField: r0 = r1->field_2b
    //     0xbda550: ldur            w0, [x1, #0x2b]
    // 0xbda554: DecompressPointer r0
    //     0xbda554: add             x0, x0, HEAP, lsl #32
    // 0xbda558: stur            x0, [fp, #-0x30]
    // 0xbda55c: r1 = Instance_Color
    //     0xbda55c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbda560: d0 = 0.700000
    //     0xbda560: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbda564: ldr             d0, [x17, #0xf48]
    // 0xbda568: r0 = withOpacity()
    //     0xbda568: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbda56c: r16 = 14.000000
    //     0xbda56c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbda570: ldr             x16, [x16, #0x1d8]
    // 0xbda574: stp             x0, x16, [SP]
    // 0xbda578: ldur            x1, [fp, #-0x30]
    // 0xbda57c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbda57c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbda580: ldr             x4, [x4, #0xaa0]
    // 0xbda584: r0 = copyWith()
    //     0xbda584: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbda588: stur            x0, [fp, #-0x30]
    // 0xbda58c: r0 = Text()
    //     0xbda58c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbda590: mov             x1, x0
    // 0xbda594: ldur            x0, [fp, #-0x10]
    // 0xbda598: stur            x1, [fp, #-0x38]
    // 0xbda59c: StoreField: r1->field_b = r0
    //     0xbda59c: stur            w0, [x1, #0xb]
    // 0xbda5a0: ldur            x0, [fp, #-0x30]
    // 0xbda5a4: StoreField: r1->field_13 = r0
    //     0xbda5a4: stur            w0, [x1, #0x13]
    // 0xbda5a8: r0 = Visibility()
    //     0xbda5a8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbda5ac: mov             x3, x0
    // 0xbda5b0: ldur            x0, [fp, #-0x38]
    // 0xbda5b4: stur            x3, [fp, #-0x10]
    // 0xbda5b8: StoreField: r3->field_b = r0
    //     0xbda5b8: stur            w0, [x3, #0xb]
    // 0xbda5bc: r0 = Instance_SizedBox
    //     0xbda5bc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbda5c0: StoreField: r3->field_f = r0
    //     0xbda5c0: stur            w0, [x3, #0xf]
    // 0xbda5c4: ldur            x0, [fp, #-8]
    // 0xbda5c8: StoreField: r3->field_13 = r0
    //     0xbda5c8: stur            w0, [x3, #0x13]
    // 0xbda5cc: r0 = false
    //     0xbda5cc: add             x0, NULL, #0x30  ; false
    // 0xbda5d0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbda5d0: stur            w0, [x3, #0x17]
    // 0xbda5d4: StoreField: r3->field_1b = r0
    //     0xbda5d4: stur            w0, [x3, #0x1b]
    // 0xbda5d8: StoreField: r3->field_1f = r0
    //     0xbda5d8: stur            w0, [x3, #0x1f]
    // 0xbda5dc: StoreField: r3->field_23 = r0
    //     0xbda5dc: stur            w0, [x3, #0x23]
    // 0xbda5e0: StoreField: r3->field_27 = r0
    //     0xbda5e0: stur            w0, [x3, #0x27]
    // 0xbda5e4: StoreField: r3->field_2b = r0
    //     0xbda5e4: stur            w0, [x3, #0x2b]
    // 0xbda5e8: r1 = Null
    //     0xbda5e8: mov             x1, NULL
    // 0xbda5ec: r2 = 4
    //     0xbda5ec: movz            x2, #0x4
    // 0xbda5f0: r0 = AllocateArray()
    //     0xbda5f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbda5f4: mov             x2, x0
    // 0xbda5f8: ldur            x0, [fp, #-0x28]
    // 0xbda5fc: stur            x2, [fp, #-8]
    // 0xbda600: StoreField: r2->field_f = r0
    //     0xbda600: stur            w0, [x2, #0xf]
    // 0xbda604: ldur            x0, [fp, #-0x10]
    // 0xbda608: StoreField: r2->field_13 = r0
    //     0xbda608: stur            w0, [x2, #0x13]
    // 0xbda60c: r1 = <Widget>
    //     0xbda60c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbda610: r0 = AllocateGrowableArray()
    //     0xbda610: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbda614: mov             x1, x0
    // 0xbda618: ldur            x0, [fp, #-8]
    // 0xbda61c: stur            x1, [fp, #-0x10]
    // 0xbda620: StoreField: r1->field_f = r0
    //     0xbda620: stur            w0, [x1, #0xf]
    // 0xbda624: r0 = 4
    //     0xbda624: movz            x0, #0x4
    // 0xbda628: StoreField: r1->field_b = r0
    //     0xbda628: stur            w0, [x1, #0xb]
    // 0xbda62c: r0 = Row()
    //     0xbda62c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbda630: mov             x1, x0
    // 0xbda634: r0 = Instance_Axis
    //     0xbda634: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbda638: stur            x1, [fp, #-8]
    // 0xbda63c: StoreField: r1->field_f = r0
    //     0xbda63c: stur            w0, [x1, #0xf]
    // 0xbda640: r0 = Instance_MainAxisAlignment
    //     0xbda640: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbda644: ldr             x0, [x0, #0xa8]
    // 0xbda648: StoreField: r1->field_13 = r0
    //     0xbda648: stur            w0, [x1, #0x13]
    // 0xbda64c: r0 = Instance_MainAxisSize
    //     0xbda64c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbda650: ldr             x0, [x0, #0xa10]
    // 0xbda654: ArrayStore: r1[0] = r0  ; List_4
    //     0xbda654: stur            w0, [x1, #0x17]
    // 0xbda658: r0 = Instance_CrossAxisAlignment
    //     0xbda658: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbda65c: ldr             x0, [x0, #0xa18]
    // 0xbda660: StoreField: r1->field_1b = r0
    //     0xbda660: stur            w0, [x1, #0x1b]
    // 0xbda664: r0 = Instance_VerticalDirection
    //     0xbda664: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbda668: ldr             x0, [x0, #0xa20]
    // 0xbda66c: StoreField: r1->field_23 = r0
    //     0xbda66c: stur            w0, [x1, #0x23]
    // 0xbda670: r0 = Instance_Clip
    //     0xbda670: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbda674: ldr             x0, [x0, #0x38]
    // 0xbda678: StoreField: r1->field_2b = r0
    //     0xbda678: stur            w0, [x1, #0x2b]
    // 0xbda67c: StoreField: r1->field_2f = rZR
    //     0xbda67c: stur            xzr, [x1, #0x2f]
    // 0xbda680: ldur            x0, [fp, #-0x10]
    // 0xbda684: StoreField: r1->field_b = r0
    //     0xbda684: stur            w0, [x1, #0xb]
    // 0xbda688: r0 = Padding()
    //     0xbda688: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbda68c: mov             x1, x0
    // 0xbda690: r0 = Instance_EdgeInsets
    //     0xbda690: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xbda694: ldr             x0, [x0, #0xd48]
    // 0xbda698: stur            x1, [fp, #-0x10]
    // 0xbda69c: StoreField: r1->field_f = r0
    //     0xbda69c: stur            w0, [x1, #0xf]
    // 0xbda6a0: ldur            x0, [fp, #-8]
    // 0xbda6a4: StoreField: r1->field_b = r0
    //     0xbda6a4: stur            w0, [x1, #0xb]
    // 0xbda6a8: r0 = Container()
    //     0xbda6a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbda6ac: stur            x0, [fp, #-8]
    // 0xbda6b0: ldur            x16, [fp, #-0x20]
    // 0xbda6b4: ldur            lr, [fp, #-0x10]
    // 0xbda6b8: stp             lr, x16, [SP]
    // 0xbda6bc: mov             x1, x0
    // 0xbda6c0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbda6c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbda6c4: ldr             x4, [x4, #0x88]
    // 0xbda6c8: r0 = Container()
    //     0xbda6c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbda6cc: ldur            x0, [fp, #-8]
    // 0xbda6d0: LeaveFrame
    //     0xbda6d0: mov             SP, fp
    //     0xbda6d4: ldp             fp, lr, [SP], #0x10
    // 0xbda6d8: ret
    //     0xbda6d8: ret             
    // 0xbda6dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbda6dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbda6e0: b               #0xbda2f0
    // 0xbda6e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbda6e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbda6e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbda6e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbda6ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbda6ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbda6f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbda6f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbda6f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbda6f4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4001, size: 0x20, field offset: 0xc
//   const constructor, 
class CustomizationSingleSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc808c0, size: 0x30
    // 0xc808c0: EnterFrame
    //     0xc808c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc808c4: mov             fp, SP
    // 0xc808c8: mov             x0, x1
    // 0xc808cc: r1 = <CustomizationSingleSelect>
    //     0xc808cc: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c28] TypeArguments: <CustomizationSingleSelect>
    //     0xc808d0: ldr             x1, [x1, #0xc28]
    // 0xc808d4: r0 = _CustomizationSingleSelectState()
    //     0xc808d4: bl              #0xc808f0  ; Allocate_CustomizationSingleSelectStateStub -> _CustomizationSingleSelectState (size=0x20)
    // 0xc808d8: r1 = ""
    //     0xc808d8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc808dc: StoreField: r0->field_13 = r1
    //     0xc808dc: stur            w1, [x0, #0x13]
    // 0xc808e0: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc808e0: stur            xzr, [x0, #0x17]
    // 0xc808e4: LeaveFrame
    //     0xc808e4: mov             SP, fp
    //     0xc808e8: ldp             fp, lr, [SP], #0x10
    // 0xc808ec: ret
    //     0xc808ec: ret             
  }
}
