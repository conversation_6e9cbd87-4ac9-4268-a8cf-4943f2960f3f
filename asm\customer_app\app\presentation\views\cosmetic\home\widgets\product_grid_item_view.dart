// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart

// class id: 1049281, size: 0x8
class :: {
}

// class id: 3424, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ProductGridItemViewState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ activate(/* No info */) {
    // ** addr: 0x7f4e04, size: 0x30
    // 0x7f4e04: EnterFrame
    //     0x7f4e04: stp             fp, lr, [SP, #-0x10]!
    //     0x7f4e08: mov             fp, SP
    // 0x7f4e0c: CheckStackOverflow
    //     0x7f4e0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f4e10: cmp             SP, x16
    //     0x7f4e14: b.ls            #0x7f4e2c
    // 0x7f4e18: r0 = _updateTickerModeNotifier()
    //     0x7f4e18: bl              #0x7f4e58  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] __ProductGridItemViewState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x7f4e1c: r0 = Null
    //     0x7f4e1c: mov             x0, NULL
    // 0x7f4e20: LeaveFrame
    //     0x7f4e20: mov             SP, fp
    //     0x7f4e24: ldp             fp, lr, [SP], #0x10
    // 0x7f4e28: ret
    //     0x7f4e28: ret             
    // 0x7f4e2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f4e2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f4e30: b               #0x7f4e18
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x7f4e58, size: 0x124
    // 0x7f4e58: EnterFrame
    //     0x7f4e58: stp             fp, lr, [SP, #-0x10]!
    //     0x7f4e5c: mov             fp, SP
    // 0x7f4e60: AllocStack(0x18)
    //     0x7f4e60: sub             SP, SP, #0x18
    // 0x7f4e64: SetupParameters(__ProductGridItemViewState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x7f4e64: mov             x2, x1
    //     0x7f4e68: stur            x1, [fp, #-8]
    // 0x7f4e6c: CheckStackOverflow
    //     0x7f4e6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f4e70: cmp             SP, x16
    //     0x7f4e74: b.ls            #0x7f4f70
    // 0x7f4e78: LoadField: r1 = r2->field_f
    //     0x7f4e78: ldur            w1, [x2, #0xf]
    // 0x7f4e7c: DecompressPointer r1
    //     0x7f4e7c: add             x1, x1, HEAP, lsl #32
    // 0x7f4e80: cmp             w1, NULL
    // 0x7f4e84: b.eq            #0x7f4f78
    // 0x7f4e88: r0 = getNotifier()
    //     0x7f4e88: bl              #0x78ae54  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x7f4e8c: mov             x3, x0
    // 0x7f4e90: ldur            x0, [fp, #-8]
    // 0x7f4e94: stur            x3, [fp, #-0x18]
    // 0x7f4e98: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x7f4e98: ldur            w4, [x0, #0x17]
    // 0x7f4e9c: DecompressPointer r4
    //     0x7f4e9c: add             x4, x4, HEAP, lsl #32
    // 0x7f4ea0: stur            x4, [fp, #-0x10]
    // 0x7f4ea4: cmp             w3, w4
    // 0x7f4ea8: b.ne            #0x7f4ebc
    // 0x7f4eac: r0 = Null
    //     0x7f4eac: mov             x0, NULL
    // 0x7f4eb0: LeaveFrame
    //     0x7f4eb0: mov             SP, fp
    //     0x7f4eb4: ldp             fp, lr, [SP], #0x10
    // 0x7f4eb8: ret
    //     0x7f4eb8: ret             
    // 0x7f4ebc: cmp             w4, NULL
    // 0x7f4ec0: b.eq            #0x7f4f04
    // 0x7f4ec4: mov             x2, x0
    // 0x7f4ec8: r1 = Function '_updateTicker@356311458':.
    //     0x7f4ec8: add             x1, PP, #0x58, lsl #12  ; [pp+0x586f0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f4ecc: ldr             x1, [x1, #0x6f0]
    // 0x7f4ed0: r0 = AllocateClosure()
    //     0x7f4ed0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f4ed4: ldur            x1, [fp, #-0x10]
    // 0x7f4ed8: r2 = LoadClassIdInstr(r1)
    //     0x7f4ed8: ldur            x2, [x1, #-1]
    //     0x7f4edc: ubfx            x2, x2, #0xc, #0x14
    // 0x7f4ee0: mov             x16, x0
    // 0x7f4ee4: mov             x0, x2
    // 0x7f4ee8: mov             x2, x16
    // 0x7f4eec: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0x7f4eec: movz            x17, #0xdc2b
    //     0x7f4ef0: add             lr, x0, x17
    //     0x7f4ef4: ldr             lr, [x21, lr, lsl #3]
    //     0x7f4ef8: blr             lr
    // 0x7f4efc: ldur            x0, [fp, #-8]
    // 0x7f4f00: ldur            x3, [fp, #-0x18]
    // 0x7f4f04: mov             x2, x0
    // 0x7f4f08: r1 = Function '_updateTicker@356311458':.
    //     0x7f4f08: add             x1, PP, #0x58, lsl #12  ; [pp+0x586f0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f4f0c: ldr             x1, [x1, #0x6f0]
    // 0x7f4f10: r0 = AllocateClosure()
    //     0x7f4f10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f4f14: ldur            x3, [fp, #-0x18]
    // 0x7f4f18: r1 = LoadClassIdInstr(r3)
    //     0x7f4f18: ldur            x1, [x3, #-1]
    //     0x7f4f1c: ubfx            x1, x1, #0xc, #0x14
    // 0x7f4f20: mov             x2, x0
    // 0x7f4f24: mov             x0, x1
    // 0x7f4f28: mov             x1, x3
    // 0x7f4f2c: r0 = GDT[cid_x0 + 0xdc71]()
    //     0x7f4f2c: movz            x17, #0xdc71
    //     0x7f4f30: add             lr, x0, x17
    //     0x7f4f34: ldr             lr, [x21, lr, lsl #3]
    //     0x7f4f38: blr             lr
    // 0x7f4f3c: ldur            x0, [fp, #-0x18]
    // 0x7f4f40: ldur            x1, [fp, #-8]
    // 0x7f4f44: ArrayStore: r1[0] = r0  ; List_4
    //     0x7f4f44: stur            w0, [x1, #0x17]
    //     0x7f4f48: ldurb           w16, [x1, #-1]
    //     0x7f4f4c: ldurb           w17, [x0, #-1]
    //     0x7f4f50: and             x16, x17, x16, lsr #2
    //     0x7f4f54: tst             x16, HEAP, lsr #32
    //     0x7f4f58: b.eq            #0x7f4f60
    //     0x7f4f5c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x7f4f60: r0 = Null
    //     0x7f4f60: mov             x0, NULL
    // 0x7f4f64: LeaveFrame
    //     0x7f4f64: mov             SP, fp
    //     0x7f4f68: ldp             fp, lr, [SP], #0x10
    // 0x7f4f6c: ret
    //     0x7f4f6c: ret             
    // 0x7f4f70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f4f70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f4f74: b               #0x7f4e78
    // 0x7f4f78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f4f78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc8735c, size: 0x94
    // 0xc8735c: EnterFrame
    //     0xc8735c: stp             fp, lr, [SP, #-0x10]!
    //     0xc87360: mov             fp, SP
    // 0xc87364: AllocStack(0x10)
    //     0xc87364: sub             SP, SP, #0x10
    // 0xc87368: SetupParameters(__ProductGridItemViewState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc87368: mov             x0, x1
    //     0xc8736c: stur            x1, [fp, #-0x10]
    // 0xc87370: CheckStackOverflow
    //     0xc87370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87374: cmp             SP, x16
    //     0xc87378: b.ls            #0xc873e8
    // 0xc8737c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc8737c: ldur            w3, [x0, #0x17]
    // 0xc87380: DecompressPointer r3
    //     0xc87380: add             x3, x3, HEAP, lsl #32
    // 0xc87384: stur            x3, [fp, #-8]
    // 0xc87388: cmp             w3, NULL
    // 0xc8738c: b.ne            #0xc87398
    // 0xc87390: mov             x1, x0
    // 0xc87394: b               #0xc873d4
    // 0xc87398: mov             x2, x0
    // 0xc8739c: r1 = Function '_updateTicker@356311458':.
    //     0xc8739c: add             x1, PP, #0x58, lsl #12  ; [pp+0x586f0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc873a0: ldr             x1, [x1, #0x6f0]
    // 0xc873a4: r0 = AllocateClosure()
    //     0xc873a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc873a8: ldur            x1, [fp, #-8]
    // 0xc873ac: r2 = LoadClassIdInstr(r1)
    //     0xc873ac: ldur            x2, [x1, #-1]
    //     0xc873b0: ubfx            x2, x2, #0xc, #0x14
    // 0xc873b4: mov             x16, x0
    // 0xc873b8: mov             x0, x2
    // 0xc873bc: mov             x2, x16
    // 0xc873c0: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0xc873c0: movz            x17, #0xdc2b
    //     0xc873c4: add             lr, x0, x17
    //     0xc873c8: ldr             lr, [x21, lr, lsl #3]
    //     0xc873cc: blr             lr
    // 0xc873d0: ldur            x1, [fp, #-0x10]
    // 0xc873d4: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc873d4: stur            NULL, [x1, #0x17]
    // 0xc873d8: r0 = Null
    //     0xc873d8: mov             x0, NULL
    // 0xc873dc: LeaveFrame
    //     0xc873dc: mov             SP, fp
    //     0xc873e0: ldp             fp, lr, [SP], #0x10
    // 0xc873e4: ret
    //     0xc873e4: ret             
    // 0xc873e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc873e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc873ec: b               #0xc8737c
  }
}

// class id: 3425, size: 0x28, field offset: 0x1c
class _ProductGridItemViewState extends __ProductGridItemViewState&State&SingleTickerProviderStateMixin {

  late PageController _pageController; // offset: 0x1c

  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xa51484, size: 0x38
    // 0xa51484: ldr             x1, [SP, #8]
    // 0xa51488: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa51488: ldur            w2, [x1, #0x17]
    // 0xa5148c: DecompressPointer r2
    //     0xa5148c: add             x2, x2, HEAP, lsl #32
    // 0xa51490: LoadField: r1 = r2->field_b
    //     0xa51490: ldur            w1, [x2, #0xb]
    // 0xa51494: DecompressPointer r1
    //     0xa51494: add             x1, x1, HEAP, lsl #32
    // 0xa51498: LoadField: r2 = r1->field_f
    //     0xa51498: ldur            w2, [x1, #0xf]
    // 0xa5149c: DecompressPointer r2
    //     0xa5149c: add             x2, x2, HEAP, lsl #32
    // 0xa514a0: ldr             x1, [SP]
    // 0xa514a4: r3 = LoadInt32Instr(r1)
    //     0xa514a4: sbfx            x3, x1, #1, #0x1f
    //     0xa514a8: tbz             w1, #0, #0xa514b0
    //     0xa514ac: ldur            x3, [x1, #7]
    // 0xa514b0: StoreField: r2->field_1f = r3
    //     0xa514b0: stur            x3, [x2, #0x1f]
    // 0xa514b4: r0 = Null
    //     0xa514b4: mov             x0, NULL
    // 0xa514b8: ret
    //     0xa514b8: ret             
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa514bc, size: 0x16bc
    // 0xa514bc: EnterFrame
    //     0xa514bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa514c0: mov             fp, SP
    // 0xa514c4: AllocStack(0x80)
    //     0xa514c4: sub             SP, SP, #0x80
    // 0xa514c8: SetupParameters()
    //     0xa514c8: ldr             x0, [fp, #0x20]
    //     0xa514cc: ldur            w1, [x0, #0x17]
    //     0xa514d0: add             x1, x1, HEAP, lsl #32
    //     0xa514d4: stur            x1, [fp, #-8]
    // 0xa514d8: CheckStackOverflow
    //     0xa514d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa514dc: cmp             SP, x16
    //     0xa514e0: b.ls            #0xa52b2c
    // 0xa514e4: r1 = 1
    //     0xa514e4: movz            x1, #0x1
    // 0xa514e8: r0 = AllocateContext()
    //     0xa514e8: bl              #0x16f6108  ; AllocateContextStub
    // 0xa514ec: mov             x2, x0
    // 0xa514f0: ldur            x1, [fp, #-8]
    // 0xa514f4: stur            x2, [fp, #-0x10]
    // 0xa514f8: StoreField: r2->field_b = r1
    //     0xa514f8: stur            w1, [x2, #0xb]
    // 0xa514fc: LoadField: r0 = r1->field_f
    //     0xa514fc: ldur            w0, [x1, #0xf]
    // 0xa51500: DecompressPointer r0
    //     0xa51500: add             x0, x0, HEAP, lsl #32
    // 0xa51504: LoadField: r3 = r0->field_b
    //     0xa51504: ldur            w3, [x0, #0xb]
    // 0xa51508: DecompressPointer r3
    //     0xa51508: add             x3, x3, HEAP, lsl #32
    // 0xa5150c: cmp             w3, NULL
    // 0xa51510: b.eq            #0xa52b34
    // 0xa51514: LoadField: r0 = r3->field_b
    //     0xa51514: ldur            w0, [x3, #0xb]
    // 0xa51518: DecompressPointer r0
    //     0xa51518: add             x0, x0, HEAP, lsl #32
    // 0xa5151c: r3 = LoadClassIdInstr(r0)
    //     0xa5151c: ldur            x3, [x0, #-1]
    //     0xa51520: ubfx            x3, x3, #0xc, #0x14
    // 0xa51524: ldr             x16, [fp, #0x10]
    // 0xa51528: stp             x16, x0, [SP]
    // 0xa5152c: mov             x0, x3
    // 0xa51530: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa51530: sub             lr, x0, #0xb7
    //     0xa51534: ldr             lr, [x21, lr, lsl #3]
    //     0xa51538: blr             lr
    // 0xa5153c: mov             x1, x0
    // 0xa51540: ldur            x2, [fp, #-0x10]
    // 0xa51544: stur            x1, [fp, #-0x18]
    // 0xa51548: StoreField: r2->field_f = r0
    //     0xa51548: stur            w0, [x2, #0xf]
    //     0xa5154c: ldurb           w16, [x2, #-1]
    //     0xa51550: ldurb           w17, [x0, #-1]
    //     0xa51554: and             x16, x17, x16, lsr #2
    //     0xa51558: tst             x16, HEAP, lsr #32
    //     0xa5155c: b.eq            #0xa51564
    //     0xa51560: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xa51564: cmp             w1, NULL
    // 0xa51568: b.ne            #0xa51574
    // 0xa5156c: r0 = Null
    //     0xa5156c: mov             x0, NULL
    // 0xa51570: b               #0xa51598
    // 0xa51574: LoadField: r0 = r1->field_7b
    //     0xa51574: ldur            w0, [x1, #0x7b]
    // 0xa51578: DecompressPointer r0
    //     0xa51578: add             x0, x0, HEAP, lsl #32
    // 0xa5157c: cmp             w0, NULL
    // 0xa51580: b.ne            #0xa5158c
    // 0xa51584: r0 = Null
    //     0xa51584: mov             x0, NULL
    // 0xa51588: b               #0xa51598
    // 0xa5158c: LoadField: r3 = r0->field_7
    //     0xa5158c: ldur            w3, [x0, #7]
    // 0xa51590: DecompressPointer r3
    //     0xa51590: add             x3, x3, HEAP, lsl #32
    // 0xa51594: mov             x0, x3
    // 0xa51598: r3 = LoadClassIdInstr(r0)
    //     0xa51598: ldur            x3, [x0, #-1]
    //     0xa5159c: ubfx            x3, x3, #0xc, #0x14
    // 0xa515a0: r16 = 0.000000
    //     0xa515a0: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa515a4: stp             x16, x0, [SP]
    // 0xa515a8: mov             x0, x3
    // 0xa515ac: mov             lr, x0
    // 0xa515b0: ldr             lr, [x21, lr, lsl #3]
    // 0xa515b4: blr             lr
    // 0xa515b8: tbz             w0, #4, #0xa51608
    // 0xa515bc: ldur            x0, [fp, #-0x18]
    // 0xa515c0: cmp             w0, NULL
    // 0xa515c4: b.ne            #0xa515d0
    // 0xa515c8: r1 = Null
    //     0xa515c8: mov             x1, NULL
    // 0xa515cc: b               #0xa515f4
    // 0xa515d0: LoadField: r1 = r0->field_7b
    //     0xa515d0: ldur            w1, [x0, #0x7b]
    // 0xa515d4: DecompressPointer r1
    //     0xa515d4: add             x1, x1, HEAP, lsl #32
    // 0xa515d8: cmp             w1, NULL
    // 0xa515dc: b.ne            #0xa515e8
    // 0xa515e0: r1 = Null
    //     0xa515e0: mov             x1, NULL
    // 0xa515e4: b               #0xa515f4
    // 0xa515e8: LoadField: r2 = r1->field_7
    //     0xa515e8: ldur            w2, [x1, #7]
    // 0xa515ec: DecompressPointer r2
    //     0xa515ec: add             x2, x2, HEAP, lsl #32
    // 0xa515f0: mov             x1, x2
    // 0xa515f4: cmp             w1, NULL
    // 0xa515f8: r16 = true
    //     0xa515f8: add             x16, NULL, #0x20  ; true
    // 0xa515fc: r17 = false
    //     0xa515fc: add             x17, NULL, #0x30  ; false
    // 0xa51600: csel            x2, x16, x17, ne
    // 0xa51604: b               #0xa51610
    // 0xa51608: ldur            x0, [fp, #-0x18]
    // 0xa5160c: r2 = false
    //     0xa5160c: add             x2, NULL, #0x30  ; false
    // 0xa51610: ldur            x1, [fp, #-8]
    // 0xa51614: stur            x2, [fp, #-0x28]
    // 0xa51618: LoadField: r3 = r1->field_f
    //     0xa51618: ldur            w3, [x1, #0xf]
    // 0xa5161c: DecompressPointer r3
    //     0xa5161c: add             x3, x3, HEAP, lsl #32
    // 0xa51620: LoadField: r4 = r3->field_b
    //     0xa51620: ldur            w4, [x3, #0xb]
    // 0xa51624: DecompressPointer r4
    //     0xa51624: add             x4, x4, HEAP, lsl #32
    // 0xa51628: cmp             w4, NULL
    // 0xa5162c: b.eq            #0xa52b38
    // 0xa51630: LoadField: r3 = r4->field_1f
    //     0xa51630: ldur            w3, [x4, #0x1f]
    // 0xa51634: DecompressPointer r3
    //     0xa51634: add             x3, x3, HEAP, lsl #32
    // 0xa51638: LoadField: r5 = r3->field_1f
    //     0xa51638: ldur            w5, [x3, #0x1f]
    // 0xa5163c: DecompressPointer r5
    //     0xa5163c: add             x5, x5, HEAP, lsl #32
    // 0xa51640: cmp             w5, NULL
    // 0xa51644: b.ne            #0xa51650
    // 0xa51648: r3 = Null
    //     0xa51648: mov             x3, NULL
    // 0xa5164c: b               #0xa51658
    // 0xa51650: LoadField: r3 = r5->field_7
    //     0xa51650: ldur            w3, [x5, #7]
    // 0xa51654: DecompressPointer r3
    //     0xa51654: add             x3, x3, HEAP, lsl #32
    // 0xa51658: cmp             w3, NULL
    // 0xa5165c: b.eq            #0xa5169c
    // 0xa51660: tbnz            w3, #4, #0xa5169c
    // 0xa51664: LoadField: r3 = r4->field_1f
    //     0xa51664: ldur            w3, [x4, #0x1f]
    // 0xa51668: DecompressPointer r3
    //     0xa51668: add             x3, x3, HEAP, lsl #32
    // 0xa5166c: LoadField: r4 = r3->field_3f
    //     0xa5166c: ldur            w4, [x3, #0x3f]
    // 0xa51670: DecompressPointer r4
    //     0xa51670: add             x4, x4, HEAP, lsl #32
    // 0xa51674: cmp             w4, NULL
    // 0xa51678: b.ne            #0xa51684
    // 0xa5167c: r3 = Null
    //     0xa5167c: mov             x3, NULL
    // 0xa51680: b               #0xa5168c
    // 0xa51684: LoadField: r3 = r4->field_23
    //     0xa51684: ldur            w3, [x4, #0x23]
    // 0xa51688: DecompressPointer r3
    //     0xa51688: add             x3, x3, HEAP, lsl #32
    // 0xa5168c: cmp             w3, NULL
    // 0xa51690: b.ne            #0xa516a0
    // 0xa51694: r3 = false
    //     0xa51694: add             x3, NULL, #0x30  ; false
    // 0xa51698: b               #0xa516a0
    // 0xa5169c: r3 = false
    //     0xa5169c: add             x3, NULL, #0x30  ; false
    // 0xa516a0: stur            x3, [fp, #-0x20]
    // 0xa516a4: ldr             x16, [fp, #0x10]
    // 0xa516a8: str             x16, [SP]
    // 0xa516ac: r0 = _interpolateSingle()
    //     0xa516ac: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa516b0: r1 = <String>
    //     0xa516b0: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xa516b4: stur            x0, [fp, #-0x30]
    // 0xa516b8: r0 = ValueKey()
    //     0xa516b8: bl              #0x68b554  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xa516bc: mov             x1, x0
    // 0xa516c0: ldur            x0, [fp, #-0x30]
    // 0xa516c4: stur            x1, [fp, #-0x38]
    // 0xa516c8: StoreField: r1->field_b = r0
    //     0xa516c8: stur            w0, [x1, #0xb]
    // 0xa516cc: r0 = Radius()
    //     0xa516cc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa516d0: d0 = 12.000000
    //     0xa516d0: fmov            d0, #12.00000000
    // 0xa516d4: stur            x0, [fp, #-0x30]
    // 0xa516d8: StoreField: r0->field_7 = d0
    //     0xa516d8: stur            d0, [x0, #7]
    // 0xa516dc: StoreField: r0->field_f = d0
    //     0xa516dc: stur            d0, [x0, #0xf]
    // 0xa516e0: r0 = BorderRadius()
    //     0xa516e0: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa516e4: mov             x1, x0
    // 0xa516e8: ldur            x0, [fp, #-0x30]
    // 0xa516ec: stur            x1, [fp, #-0x40]
    // 0xa516f0: StoreField: r1->field_7 = r0
    //     0xa516f0: stur            w0, [x1, #7]
    // 0xa516f4: StoreField: r1->field_b = r0
    //     0xa516f4: stur            w0, [x1, #0xb]
    // 0xa516f8: StoreField: r1->field_f = r0
    //     0xa516f8: stur            w0, [x1, #0xf]
    // 0xa516fc: StoreField: r1->field_13 = r0
    //     0xa516fc: stur            w0, [x1, #0x13]
    // 0xa51700: r0 = RoundedRectangleBorder()
    //     0xa51700: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa51704: mov             x3, x0
    // 0xa51708: ldur            x0, [fp, #-0x40]
    // 0xa5170c: stur            x3, [fp, #-0x48]
    // 0xa51710: StoreField: r3->field_b = r0
    //     0xa51710: stur            w0, [x3, #0xb]
    // 0xa51714: r0 = Instance_BorderSide
    //     0xa51714: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa51718: ldr             x0, [x0, #0xe20]
    // 0xa5171c: StoreField: r3->field_7 = r0
    //     0xa5171c: stur            w0, [x3, #7]
    // 0xa51720: ldur            x0, [fp, #-0x18]
    // 0xa51724: cmp             w0, NULL
    // 0xa51728: b.ne            #0xa51734
    // 0xa5172c: r5 = Null
    //     0xa5172c: mov             x5, NULL
    // 0xa51730: b               #0xa5174c
    // 0xa51734: LoadField: r1 = r0->field_37
    //     0xa51734: ldur            w1, [x0, #0x37]
    // 0xa51738: DecompressPointer r1
    //     0xa51738: add             x1, x1, HEAP, lsl #32
    // 0xa5173c: cmp             w1, NULL
    // 0xa51740: b.eq            #0xa52b3c
    // 0xa51744: LoadField: r2 = r1->field_b
    //     0xa51744: ldur            w2, [x1, #0xb]
    // 0xa51748: mov             x5, x2
    // 0xa5174c: ldur            x4, [fp, #-8]
    // 0xa51750: stur            x5, [fp, #-0x40]
    // 0xa51754: LoadField: r1 = r4->field_f
    //     0xa51754: ldur            w1, [x4, #0xf]
    // 0xa51758: DecompressPointer r1
    //     0xa51758: add             x1, x1, HEAP, lsl #32
    // 0xa5175c: LoadField: r6 = r1->field_1b
    //     0xa5175c: ldur            w6, [x1, #0x1b]
    // 0xa51760: DecompressPointer r6
    //     0xa51760: add             x6, x6, HEAP, lsl #32
    // 0xa51764: r16 = Sentinel
    //     0xa51764: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa51768: cmp             w6, w16
    // 0xa5176c: b.eq            #0xa52b40
    // 0xa51770: ldur            x2, [fp, #-0x10]
    // 0xa51774: stur            x6, [fp, #-0x30]
    // 0xa51778: r1 = Function '<anonymous closure>':.
    //     0xa51778: add             x1, PP, #0x58, lsl #12  ; [pp+0x58718] AnonymousClosure: (0xa51484), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xa5177c: ldr             x1, [x1, #0x718]
    // 0xa51780: r0 = AllocateClosure()
    //     0xa51780: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa51784: ldur            x2, [fp, #-0x10]
    // 0xa51788: r1 = Function '<anonymous closure>':.
    //     0xa51788: add             x1, PP, #0x58, lsl #12  ; [pp+0x58720] AnonymousClosure: (0xa53054), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xa5178c: ldr             x1, [x1, #0x720]
    // 0xa51790: stur            x0, [fp, #-0x50]
    // 0xa51794: r0 = AllocateClosure()
    //     0xa51794: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa51798: stur            x0, [fp, #-0x58]
    // 0xa5179c: r0 = PageView()
    //     0xa5179c: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xa517a0: stur            x0, [fp, #-0x60]
    // 0xa517a4: ldur            x16, [fp, #-0x30]
    // 0xa517a8: str             x16, [SP]
    // 0xa517ac: mov             x1, x0
    // 0xa517b0: ldur            x2, [fp, #-0x58]
    // 0xa517b4: ldur            x3, [fp, #-0x40]
    // 0xa517b8: ldur            x5, [fp, #-0x50]
    // 0xa517bc: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xa517bc: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xa517c0: ldr             x4, [x4, #0xd60]
    // 0xa517c4: r0 = PageView.builder()
    //     0xa517c4: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xa517c8: r0 = AspectRatio()
    //     0xa517c8: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xa517cc: d0 = 1.000000
    //     0xa517cc: fmov            d0, #1.00000000
    // 0xa517d0: stur            x0, [fp, #-0x30]
    // 0xa517d4: StoreField: r0->field_f = d0
    //     0xa517d4: stur            d0, [x0, #0xf]
    // 0xa517d8: ldur            x1, [fp, #-0x60]
    // 0xa517dc: StoreField: r0->field_b = r1
    //     0xa517dc: stur            w1, [x0, #0xb]
    // 0xa517e0: r0 = Card()
    //     0xa517e0: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xa517e4: mov             x2, x0
    // 0xa517e8: r0 = 0.000000
    //     0xa517e8: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa517ec: stur            x2, [fp, #-0x40]
    // 0xa517f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xa517f0: stur            w0, [x2, #0x17]
    // 0xa517f4: ldur            x0, [fp, #-0x48]
    // 0xa517f8: StoreField: r2->field_1b = r0
    //     0xa517f8: stur            w0, [x2, #0x1b]
    // 0xa517fc: r0 = true
    //     0xa517fc: add             x0, NULL, #0x20  ; true
    // 0xa51800: StoreField: r2->field_1f = r0
    //     0xa51800: stur            w0, [x2, #0x1f]
    // 0xa51804: ldur            x1, [fp, #-0x30]
    // 0xa51808: StoreField: r2->field_2f = r1
    //     0xa51808: stur            w1, [x2, #0x2f]
    // 0xa5180c: StoreField: r2->field_2b = r0
    //     0xa5180c: stur            w0, [x2, #0x2b]
    // 0xa51810: r1 = Instance__CardVariant
    //     0xa51810: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xa51814: ldr             x1, [x1, #0xa68]
    // 0xa51818: StoreField: r2->field_33 = r1
    //     0xa51818: stur            w1, [x2, #0x33]
    // 0xa5181c: r1 = <FlexParentData>
    //     0xa5181c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa51820: ldr             x1, [x1, #0xe00]
    // 0xa51824: r0 = Expanded()
    //     0xa51824: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa51828: mov             x2, x0
    // 0xa5182c: r0 = 5
    //     0xa5182c: movz            x0, #0x5
    // 0xa51830: stur            x2, [fp, #-0x30]
    // 0xa51834: StoreField: r2->field_13 = r0
    //     0xa51834: stur            x0, [x2, #0x13]
    // 0xa51838: r3 = Instance_FlexFit
    //     0xa51838: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa5183c: ldr             x3, [x3, #0xe08]
    // 0xa51840: StoreField: r2->field_1b = r3
    //     0xa51840: stur            w3, [x2, #0x1b]
    // 0xa51844: ldur            x1, [fp, #-0x40]
    // 0xa51848: StoreField: r2->field_b = r1
    //     0xa51848: stur            w1, [x2, #0xb]
    // 0xa5184c: ldur            x4, [fp, #-0x18]
    // 0xa51850: cmp             w4, NULL
    // 0xa51854: b.ne            #0xa51860
    // 0xa51858: r1 = Null
    //     0xa51858: mov             x1, NULL
    // 0xa5185c: b               #0xa51884
    // 0xa51860: LoadField: r1 = r4->field_33
    //     0xa51860: ldur            w1, [x4, #0x33]
    // 0xa51864: DecompressPointer r1
    //     0xa51864: add             x1, x1, HEAP, lsl #32
    // 0xa51868: cmp             w1, NULL
    // 0xa5186c: b.ne            #0xa51878
    // 0xa51870: r1 = Null
    //     0xa51870: mov             x1, NULL
    // 0xa51874: b               #0xa51884
    // 0xa51878: LoadField: r5 = r1->field_7
    //     0xa51878: ldur            w5, [x1, #7]
    // 0xa5187c: DecompressPointer r5
    //     0xa5187c: add             x5, x5, HEAP, lsl #32
    // 0xa51880: mov             x1, x5
    // 0xa51884: cmp             w1, NULL
    // 0xa51888: b.ne            #0xa51890
    // 0xa5188c: r1 = ""
    //     0xa5188c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa51890: ldur            x5, [fp, #-0x28]
    // 0xa51894: r0 = capitalizeFirstWord()
    //     0xa51894: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa51898: ldr             x1, [fp, #0x18]
    // 0xa5189c: stur            x0, [fp, #-0x40]
    // 0xa518a0: r0 = of()
    //     0xa518a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa518a4: LoadField: r1 = r0->field_87
    //     0xa518a4: ldur            w1, [x0, #0x87]
    // 0xa518a8: DecompressPointer r1
    //     0xa518a8: add             x1, x1, HEAP, lsl #32
    // 0xa518ac: LoadField: r0 = r1->field_2b
    //     0xa518ac: ldur            w0, [x1, #0x2b]
    // 0xa518b0: DecompressPointer r0
    //     0xa518b0: add             x0, x0, HEAP, lsl #32
    // 0xa518b4: r16 = 14.000000
    //     0xa518b4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa518b8: ldr             x16, [x16, #0x1d8]
    // 0xa518bc: r30 = Instance_Color
    //     0xa518bc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa518c0: stp             lr, x16, [SP]
    // 0xa518c4: mov             x1, x0
    // 0xa518c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa518c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa518cc: ldr             x4, [x4, #0xaa0]
    // 0xa518d0: r0 = copyWith()
    //     0xa518d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa518d4: stur            x0, [fp, #-0x48]
    // 0xa518d8: r0 = Text()
    //     0xa518d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa518dc: mov             x1, x0
    // 0xa518e0: ldur            x0, [fp, #-0x40]
    // 0xa518e4: stur            x1, [fp, #-0x50]
    // 0xa518e8: StoreField: r1->field_b = r0
    //     0xa518e8: stur            w0, [x1, #0xb]
    // 0xa518ec: ldur            x0, [fp, #-0x48]
    // 0xa518f0: StoreField: r1->field_13 = r0
    //     0xa518f0: stur            w0, [x1, #0x13]
    // 0xa518f4: r0 = Instance_TextOverflow
    //     0xa518f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xa518f8: ldr             x0, [x0, #0xe10]
    // 0xa518fc: StoreField: r1->field_2b = r0
    //     0xa518fc: stur            w0, [x1, #0x2b]
    // 0xa51900: r2 = 2
    //     0xa51900: movz            x2, #0x2
    // 0xa51904: StoreField: r1->field_37 = r2
    //     0xa51904: stur            w2, [x1, #0x37]
    // 0xa51908: r0 = Padding()
    //     0xa51908: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa5190c: mov             x3, x0
    // 0xa51910: r0 = Instance_EdgeInsets
    //     0xa51910: add             x0, PP, #0x58, lsl #12  ; [pp+0x58728] Obj!EdgeInsets@d58521
    //     0xa51914: ldr             x0, [x0, #0x728]
    // 0xa51918: stur            x3, [fp, #-0x40]
    // 0xa5191c: StoreField: r3->field_f = r0
    //     0xa5191c: stur            w0, [x3, #0xf]
    // 0xa51920: ldur            x1, [fp, #-0x50]
    // 0xa51924: StoreField: r3->field_b = r1
    //     0xa51924: stur            w1, [x3, #0xb]
    // 0xa51928: r1 = Null
    //     0xa51928: mov             x1, NULL
    // 0xa5192c: r2 = 2
    //     0xa5192c: movz            x2, #0x2
    // 0xa51930: r0 = AllocateArray()
    //     0xa51930: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa51934: mov             x2, x0
    // 0xa51938: ldur            x0, [fp, #-0x40]
    // 0xa5193c: stur            x2, [fp, #-0x48]
    // 0xa51940: StoreField: r2->field_f = r0
    //     0xa51940: stur            w0, [x2, #0xf]
    // 0xa51944: r1 = <Widget>
    //     0xa51944: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa51948: r0 = AllocateGrowableArray()
    //     0xa51948: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa5194c: mov             x1, x0
    // 0xa51950: ldur            x0, [fp, #-0x48]
    // 0xa51954: stur            x1, [fp, #-0x40]
    // 0xa51958: StoreField: r1->field_f = r0
    //     0xa51958: stur            w0, [x1, #0xf]
    // 0xa5195c: r0 = 2
    //     0xa5195c: movz            x0, #0x2
    // 0xa51960: StoreField: r1->field_b = r0
    //     0xa51960: stur            w0, [x1, #0xb]
    // 0xa51964: ldur            x0, [fp, #-0x28]
    // 0xa51968: tbnz            w0, #4, #0xa52178
    // 0xa5196c: ldur            x2, [fp, #-0x18]
    // 0xa51970: cmp             w2, NULL
    // 0xa51974: b.ne            #0xa51980
    // 0xa51978: r0 = Null
    //     0xa51978: mov             x0, NULL
    // 0xa5197c: b               #0xa519a4
    // 0xa51980: LoadField: r0 = r2->field_7b
    //     0xa51980: ldur            w0, [x2, #0x7b]
    // 0xa51984: DecompressPointer r0
    //     0xa51984: add             x0, x0, HEAP, lsl #32
    // 0xa51988: cmp             w0, NULL
    // 0xa5198c: b.ne            #0xa51998
    // 0xa51990: r0 = Null
    //     0xa51990: mov             x0, NULL
    // 0xa51994: b               #0xa519a4
    // 0xa51998: LoadField: r3 = r0->field_f
    //     0xa51998: ldur            w3, [x0, #0xf]
    // 0xa5199c: DecompressPointer r3
    //     0xa5199c: add             x3, x3, HEAP, lsl #32
    // 0xa519a0: mov             x0, x3
    // 0xa519a4: r3 = LoadClassIdInstr(r0)
    //     0xa519a4: ldur            x3, [x0, #-1]
    //     0xa519a8: ubfx            x3, x3, #0xc, #0x14
    // 0xa519ac: r16 = "product_rating"
    //     0xa519ac: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xa519b0: ldr             x16, [x16, #0xf20]
    // 0xa519b4: stp             x16, x0, [SP]
    // 0xa519b8: mov             x0, x3
    // 0xa519bc: mov             lr, x0
    // 0xa519c0: ldr             lr, [x21, lr, lsl #3]
    // 0xa519c4: blr             lr
    // 0xa519c8: tbnz            w0, #4, #0xa51e64
    // 0xa519cc: ldur            x0, [fp, #-0x18]
    // 0xa519d0: cmp             w0, NULL
    // 0xa519d4: b.ne            #0xa519e0
    // 0xa519d8: r1 = Null
    //     0xa519d8: mov             x1, NULL
    // 0xa519dc: b               #0xa51a04
    // 0xa519e0: LoadField: r1 = r0->field_7b
    //     0xa519e0: ldur            w1, [x0, #0x7b]
    // 0xa519e4: DecompressPointer r1
    //     0xa519e4: add             x1, x1, HEAP, lsl #32
    // 0xa519e8: cmp             w1, NULL
    // 0xa519ec: b.ne            #0xa519f8
    // 0xa519f0: r1 = Null
    //     0xa519f0: mov             x1, NULL
    // 0xa519f4: b               #0xa51a04
    // 0xa519f8: LoadField: r2 = r1->field_7
    //     0xa519f8: ldur            w2, [x1, #7]
    // 0xa519fc: DecompressPointer r2
    //     0xa519fc: add             x2, x2, HEAP, lsl #32
    // 0xa51a00: mov             x1, x2
    // 0xa51a04: cmp             w1, NULL
    // 0xa51a08: b.ne            #0xa51a14
    // 0xa51a0c: d1 = 0.000000
    //     0xa51a0c: eor             v1.16b, v1.16b, v1.16b
    // 0xa51a10: b               #0xa51a1c
    // 0xa51a14: LoadField: d0 = r1->field_7
    //     0xa51a14: ldur            d0, [x1, #7]
    // 0xa51a18: mov             v1.16b, v0.16b
    // 0xa51a1c: d0 = 4.000000
    //     0xa51a1c: fmov            d0, #4.00000000
    // 0xa51a20: fcmp            d1, d0
    // 0xa51a24: b.lt            #0xa51a34
    // 0xa51a28: r1 = Instance_Color
    //     0xa51a28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa51a2c: ldr             x1, [x1, #0x858]
    // 0xa51a30: b               #0xa51b18
    // 0xa51a34: cmp             w0, NULL
    // 0xa51a38: b.ne            #0xa51a44
    // 0xa51a3c: r1 = Null
    //     0xa51a3c: mov             x1, NULL
    // 0xa51a40: b               #0xa51a68
    // 0xa51a44: LoadField: r1 = r0->field_7b
    //     0xa51a44: ldur            w1, [x0, #0x7b]
    // 0xa51a48: DecompressPointer r1
    //     0xa51a48: add             x1, x1, HEAP, lsl #32
    // 0xa51a4c: cmp             w1, NULL
    // 0xa51a50: b.ne            #0xa51a5c
    // 0xa51a54: r1 = Null
    //     0xa51a54: mov             x1, NULL
    // 0xa51a58: b               #0xa51a68
    // 0xa51a5c: LoadField: r2 = r1->field_7
    //     0xa51a5c: ldur            w2, [x1, #7]
    // 0xa51a60: DecompressPointer r2
    //     0xa51a60: add             x2, x2, HEAP, lsl #32
    // 0xa51a64: mov             x1, x2
    // 0xa51a68: cmp             w1, NULL
    // 0xa51a6c: b.ne            #0xa51a78
    // 0xa51a70: d1 = 0.000000
    //     0xa51a70: eor             v1.16b, v1.16b, v1.16b
    // 0xa51a74: b               #0xa51a80
    // 0xa51a78: LoadField: d0 = r1->field_7
    //     0xa51a78: ldur            d0, [x1, #7]
    // 0xa51a7c: mov             v1.16b, v0.16b
    // 0xa51a80: d0 = 3.500000
    //     0xa51a80: fmov            d0, #3.50000000
    // 0xa51a84: fcmp            d1, d0
    // 0xa51a88: b.lt            #0xa51aac
    // 0xa51a8c: r1 = Instance_Color
    //     0xa51a8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa51a90: ldr             x1, [x1, #0x858]
    // 0xa51a94: d0 = 0.700000
    //     0xa51a94: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa51a98: ldr             d0, [x17, #0xf48]
    // 0xa51a9c: r0 = withOpacity()
    //     0xa51a9c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa51aa0: mov             x1, x0
    // 0xa51aa4: ldur            x0, [fp, #-0x18]
    // 0xa51aa8: b               #0xa51b18
    // 0xa51aac: cmp             w0, NULL
    // 0xa51ab0: b.ne            #0xa51abc
    // 0xa51ab4: r1 = Null
    //     0xa51ab4: mov             x1, NULL
    // 0xa51ab8: b               #0xa51ae0
    // 0xa51abc: LoadField: r1 = r0->field_7b
    //     0xa51abc: ldur            w1, [x0, #0x7b]
    // 0xa51ac0: DecompressPointer r1
    //     0xa51ac0: add             x1, x1, HEAP, lsl #32
    // 0xa51ac4: cmp             w1, NULL
    // 0xa51ac8: b.ne            #0xa51ad4
    // 0xa51acc: r1 = Null
    //     0xa51acc: mov             x1, NULL
    // 0xa51ad0: b               #0xa51ae0
    // 0xa51ad4: LoadField: r2 = r1->field_7
    //     0xa51ad4: ldur            w2, [x1, #7]
    // 0xa51ad8: DecompressPointer r2
    //     0xa51ad8: add             x2, x2, HEAP, lsl #32
    // 0xa51adc: mov             x1, x2
    // 0xa51ae0: cmp             w1, NULL
    // 0xa51ae4: b.ne            #0xa51af0
    // 0xa51ae8: d1 = 0.000000
    //     0xa51ae8: eor             v1.16b, v1.16b, v1.16b
    // 0xa51aec: b               #0xa51af8
    // 0xa51af0: LoadField: d0 = r1->field_7
    //     0xa51af0: ldur            d0, [x1, #7]
    // 0xa51af4: mov             v1.16b, v0.16b
    // 0xa51af8: d0 = 2.000000
    //     0xa51af8: fmov            d0, #2.00000000
    // 0xa51afc: fcmp            d1, d0
    // 0xa51b00: b.lt            #0xa51b10
    // 0xa51b04: r1 = Instance_Color
    //     0xa51b04: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xa51b08: ldr             x1, [x1, #0x860]
    // 0xa51b0c: b               #0xa51b18
    // 0xa51b10: r1 = Instance_Color
    //     0xa51b10: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xa51b14: ldr             x1, [x1, #0x50]
    // 0xa51b18: stur            x1, [fp, #-0x28]
    // 0xa51b1c: r0 = ColorFilter()
    //     0xa51b1c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa51b20: mov             x1, x0
    // 0xa51b24: ldur            x0, [fp, #-0x28]
    // 0xa51b28: stur            x1, [fp, #-0x48]
    // 0xa51b2c: StoreField: r1->field_7 = r0
    //     0xa51b2c: stur            w0, [x1, #7]
    // 0xa51b30: r0 = Instance_BlendMode
    //     0xa51b30: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa51b34: ldr             x0, [x0, #0xb30]
    // 0xa51b38: StoreField: r1->field_b = r0
    //     0xa51b38: stur            w0, [x1, #0xb]
    // 0xa51b3c: r2 = 1
    //     0xa51b3c: movz            x2, #0x1
    // 0xa51b40: StoreField: r1->field_13 = r2
    //     0xa51b40: stur            x2, [x1, #0x13]
    // 0xa51b44: r0 = SvgPicture()
    //     0xa51b44: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa51b48: stur            x0, [fp, #-0x28]
    // 0xa51b4c: ldur            x16, [fp, #-0x48]
    // 0xa51b50: str             x16, [SP]
    // 0xa51b54: mov             x1, x0
    // 0xa51b58: r2 = "assets/images/green_star.svg"
    //     0xa51b58: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa51b5c: ldr             x2, [x2, #0x9a0]
    // 0xa51b60: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa51b60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa51b64: ldr             x4, [x4, #0xa38]
    // 0xa51b68: r0 = SvgPicture.asset()
    //     0xa51b68: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa51b6c: ldur            x0, [fp, #-0x18]
    // 0xa51b70: cmp             w0, NULL
    // 0xa51b74: b.ne            #0xa51b80
    // 0xa51b78: r0 = Null
    //     0xa51b78: mov             x0, NULL
    // 0xa51b7c: b               #0xa51bb8
    // 0xa51b80: LoadField: r1 = r0->field_7b
    //     0xa51b80: ldur            w1, [x0, #0x7b]
    // 0xa51b84: DecompressPointer r1
    //     0xa51b84: add             x1, x1, HEAP, lsl #32
    // 0xa51b88: cmp             w1, NULL
    // 0xa51b8c: b.ne            #0xa51b98
    // 0xa51b90: r0 = Null
    //     0xa51b90: mov             x0, NULL
    // 0xa51b94: b               #0xa51bb8
    // 0xa51b98: LoadField: r2 = r1->field_7
    //     0xa51b98: ldur            w2, [x1, #7]
    // 0xa51b9c: DecompressPointer r2
    //     0xa51b9c: add             x2, x2, HEAP, lsl #32
    // 0xa51ba0: cmp             w2, NULL
    // 0xa51ba4: b.ne            #0xa51bb0
    // 0xa51ba8: r0 = Null
    //     0xa51ba8: mov             x0, NULL
    // 0xa51bac: b               #0xa51bb8
    // 0xa51bb0: str             x2, [SP]
    // 0xa51bb4: r0 = toString()
    //     0xa51bb4: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xa51bb8: cmp             w0, NULL
    // 0xa51bbc: b.ne            #0xa51bc8
    // 0xa51bc0: r3 = ""
    //     0xa51bc0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa51bc4: b               #0xa51bcc
    // 0xa51bc8: mov             x3, x0
    // 0xa51bcc: ldur            x0, [fp, #-0x18]
    // 0xa51bd0: ldur            x2, [fp, #-0x28]
    // 0xa51bd4: ldr             x1, [fp, #0x18]
    // 0xa51bd8: stur            x3, [fp, #-0x48]
    // 0xa51bdc: r0 = of()
    //     0xa51bdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa51be0: LoadField: r1 = r0->field_87
    //     0xa51be0: ldur            w1, [x0, #0x87]
    // 0xa51be4: DecompressPointer r1
    //     0xa51be4: add             x1, x1, HEAP, lsl #32
    // 0xa51be8: LoadField: r0 = r1->field_7
    //     0xa51be8: ldur            w0, [x1, #7]
    // 0xa51bec: DecompressPointer r0
    //     0xa51bec: add             x0, x0, HEAP, lsl #32
    // 0xa51bf0: r16 = 12.000000
    //     0xa51bf0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa51bf4: ldr             x16, [x16, #0x9e8]
    // 0xa51bf8: r30 = Instance_Color
    //     0xa51bf8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa51bfc: stp             lr, x16, [SP]
    // 0xa51c00: mov             x1, x0
    // 0xa51c04: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa51c04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa51c08: ldr             x4, [x4, #0xaa0]
    // 0xa51c0c: r0 = copyWith()
    //     0xa51c0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa51c10: stur            x0, [fp, #-0x50]
    // 0xa51c14: r0 = Text()
    //     0xa51c14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa51c18: mov             x3, x0
    // 0xa51c1c: ldur            x0, [fp, #-0x48]
    // 0xa51c20: stur            x3, [fp, #-0x58]
    // 0xa51c24: StoreField: r3->field_b = r0
    //     0xa51c24: stur            w0, [x3, #0xb]
    // 0xa51c28: ldur            x0, [fp, #-0x50]
    // 0xa51c2c: StoreField: r3->field_13 = r0
    //     0xa51c2c: stur            w0, [x3, #0x13]
    // 0xa51c30: r1 = Null
    //     0xa51c30: mov             x1, NULL
    // 0xa51c34: r2 = 4
    //     0xa51c34: movz            x2, #0x4
    // 0xa51c38: r0 = AllocateArray()
    //     0xa51c38: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa51c3c: mov             x2, x0
    // 0xa51c40: ldur            x0, [fp, #-0x28]
    // 0xa51c44: stur            x2, [fp, #-0x48]
    // 0xa51c48: StoreField: r2->field_f = r0
    //     0xa51c48: stur            w0, [x2, #0xf]
    // 0xa51c4c: ldur            x0, [fp, #-0x58]
    // 0xa51c50: StoreField: r2->field_13 = r0
    //     0xa51c50: stur            w0, [x2, #0x13]
    // 0xa51c54: r1 = <Widget>
    //     0xa51c54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa51c58: r0 = AllocateGrowableArray()
    //     0xa51c58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa51c5c: mov             x3, x0
    // 0xa51c60: ldur            x0, [fp, #-0x48]
    // 0xa51c64: stur            x3, [fp, #-0x50]
    // 0xa51c68: StoreField: r3->field_f = r0
    //     0xa51c68: stur            w0, [x3, #0xf]
    // 0xa51c6c: r0 = 4
    //     0xa51c6c: movz            x0, #0x4
    // 0xa51c70: StoreField: r3->field_b = r0
    //     0xa51c70: stur            w0, [x3, #0xb]
    // 0xa51c74: ldur            x4, [fp, #-0x18]
    // 0xa51c78: cmp             w4, NULL
    // 0xa51c7c: b.ne            #0xa51c88
    // 0xa51c80: mov             x2, x3
    // 0xa51c84: b               #0xa51df8
    // 0xa51c88: LoadField: r1 = r4->field_7b
    //     0xa51c88: ldur            w1, [x4, #0x7b]
    // 0xa51c8c: DecompressPointer r1
    //     0xa51c8c: add             x1, x1, HEAP, lsl #32
    // 0xa51c90: cmp             w1, NULL
    // 0xa51c94: b.ne            #0xa51ca0
    // 0xa51c98: mov             x2, x3
    // 0xa51c9c: b               #0xa51df8
    // 0xa51ca0: LoadField: r5 = r1->field_b
    //     0xa51ca0: ldur            w5, [x1, #0xb]
    // 0xa51ca4: DecompressPointer r5
    //     0xa51ca4: add             x5, x5, HEAP, lsl #32
    // 0xa51ca8: stur            x5, [fp, #-0x28]
    // 0xa51cac: cmp             w5, NULL
    // 0xa51cb0: b.eq            #0xa51df4
    // 0xa51cb4: r1 = Null
    //     0xa51cb4: mov             x1, NULL
    // 0xa51cb8: r2 = 6
    //     0xa51cb8: movz            x2, #0x6
    // 0xa51cbc: r0 = AllocateArray()
    //     0xa51cbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa51cc0: r16 = " | ("
    //     0xa51cc0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xa51cc4: ldr             x16, [x16, #0xd70]
    // 0xa51cc8: StoreField: r0->field_f = r16
    //     0xa51cc8: stur            w16, [x0, #0xf]
    // 0xa51ccc: ldur            x1, [fp, #-0x28]
    // 0xa51cd0: cmp             w1, NULL
    // 0xa51cd4: b.ne            #0xa51ce0
    // 0xa51cd8: r2 = Null
    //     0xa51cd8: mov             x2, NULL
    // 0xa51cdc: b               #0xa51d04
    // 0xa51ce0: LoadField: d0 = r1->field_7
    //     0xa51ce0: ldur            d0, [x1, #7]
    // 0xa51ce4: fcmp            d0, d0
    // 0xa51ce8: b.vs            #0xa52b4c
    // 0xa51cec: fcvtzs          x1, d0
    // 0xa51cf0: asr             x16, x1, #0x1e
    // 0xa51cf4: cmp             x16, x1, asr #63
    // 0xa51cf8: b.ne            #0xa52b4c
    // 0xa51cfc: lsl             x1, x1, #1
    // 0xa51d00: mov             x2, x1
    // 0xa51d04: ldur            x1, [fp, #-0x50]
    // 0xa51d08: StoreField: r0->field_13 = r2
    //     0xa51d08: stur            w2, [x0, #0x13]
    // 0xa51d0c: r16 = ")"
    //     0xa51d0c: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xa51d10: ArrayStore: r0[0] = r16  ; List_4
    //     0xa51d10: stur            w16, [x0, #0x17]
    // 0xa51d14: str             x0, [SP]
    // 0xa51d18: r0 = _interpolate()
    //     0xa51d18: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa51d1c: ldr             x1, [fp, #0x18]
    // 0xa51d20: stur            x0, [fp, #-0x28]
    // 0xa51d24: r0 = of()
    //     0xa51d24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa51d28: LoadField: r1 = r0->field_87
    //     0xa51d28: ldur            w1, [x0, #0x87]
    // 0xa51d2c: DecompressPointer r1
    //     0xa51d2c: add             x1, x1, HEAP, lsl #32
    // 0xa51d30: LoadField: r0 = r1->field_2b
    //     0xa51d30: ldur            w0, [x1, #0x2b]
    // 0xa51d34: DecompressPointer r0
    //     0xa51d34: add             x0, x0, HEAP, lsl #32
    // 0xa51d38: r16 = 12.000000
    //     0xa51d38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa51d3c: ldr             x16, [x16, #0x9e8]
    // 0xa51d40: r30 = Instance_Color
    //     0xa51d40: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa51d44: stp             lr, x16, [SP]
    // 0xa51d48: mov             x1, x0
    // 0xa51d4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa51d4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa51d50: ldr             x4, [x4, #0xaa0]
    // 0xa51d54: r0 = copyWith()
    //     0xa51d54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa51d58: stur            x0, [fp, #-0x48]
    // 0xa51d5c: r0 = Text()
    //     0xa51d5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa51d60: mov             x2, x0
    // 0xa51d64: ldur            x0, [fp, #-0x28]
    // 0xa51d68: stur            x2, [fp, #-0x58]
    // 0xa51d6c: StoreField: r2->field_b = r0
    //     0xa51d6c: stur            w0, [x2, #0xb]
    // 0xa51d70: ldur            x0, [fp, #-0x48]
    // 0xa51d74: StoreField: r2->field_13 = r0
    //     0xa51d74: stur            w0, [x2, #0x13]
    // 0xa51d78: ldur            x0, [fp, #-0x50]
    // 0xa51d7c: LoadField: r1 = r0->field_b
    //     0xa51d7c: ldur            w1, [x0, #0xb]
    // 0xa51d80: LoadField: r3 = r0->field_f
    //     0xa51d80: ldur            w3, [x0, #0xf]
    // 0xa51d84: DecompressPointer r3
    //     0xa51d84: add             x3, x3, HEAP, lsl #32
    // 0xa51d88: LoadField: r4 = r3->field_b
    //     0xa51d88: ldur            w4, [x3, #0xb]
    // 0xa51d8c: r3 = LoadInt32Instr(r1)
    //     0xa51d8c: sbfx            x3, x1, #1, #0x1f
    // 0xa51d90: stur            x3, [fp, #-0x68]
    // 0xa51d94: r1 = LoadInt32Instr(r4)
    //     0xa51d94: sbfx            x1, x4, #1, #0x1f
    // 0xa51d98: cmp             x3, x1
    // 0xa51d9c: b.ne            #0xa51da8
    // 0xa51da0: mov             x1, x0
    // 0xa51da4: r0 = _growToNextCapacity()
    //     0xa51da4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa51da8: ldur            x2, [fp, #-0x50]
    // 0xa51dac: ldur            x3, [fp, #-0x68]
    // 0xa51db0: add             x0, x3, #1
    // 0xa51db4: lsl             x1, x0, #1
    // 0xa51db8: StoreField: r2->field_b = r1
    //     0xa51db8: stur            w1, [x2, #0xb]
    // 0xa51dbc: LoadField: r1 = r2->field_f
    //     0xa51dbc: ldur            w1, [x2, #0xf]
    // 0xa51dc0: DecompressPointer r1
    //     0xa51dc0: add             x1, x1, HEAP, lsl #32
    // 0xa51dc4: ldur            x0, [fp, #-0x58]
    // 0xa51dc8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa51dc8: add             x25, x1, x3, lsl #2
    //     0xa51dcc: add             x25, x25, #0xf
    //     0xa51dd0: str             w0, [x25]
    //     0xa51dd4: tbz             w0, #0, #0xa51df0
    //     0xa51dd8: ldurb           w16, [x1, #-1]
    //     0xa51ddc: ldurb           w17, [x0, #-1]
    //     0xa51de0: and             x16, x17, x16, lsr #2
    //     0xa51de4: tst             x16, HEAP, lsr #32
    //     0xa51de8: b.eq            #0xa51df0
    //     0xa51dec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa51df0: b               #0xa51df8
    // 0xa51df4: mov             x2, x3
    // 0xa51df8: r0 = Row()
    //     0xa51df8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa51dfc: r3 = Instance_Axis
    //     0xa51dfc: ldr             x3, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa51e00: StoreField: r0->field_f = r3
    //     0xa51e00: stur            w3, [x0, #0xf]
    // 0xa51e04: r4 = Instance_MainAxisAlignment
    //     0xa51e04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa51e08: ldr             x4, [x4, #0xa08]
    // 0xa51e0c: StoreField: r0->field_13 = r4
    //     0xa51e0c: stur            w4, [x0, #0x13]
    // 0xa51e10: r5 = Instance_MainAxisSize
    //     0xa51e10: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa51e14: ldr             x5, [x5, #0xa10]
    // 0xa51e18: ArrayStore: r0[0] = r5  ; List_4
    //     0xa51e18: stur            w5, [x0, #0x17]
    // 0xa51e1c: r6 = Instance_CrossAxisAlignment
    //     0xa51e1c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa51e20: ldr             x6, [x6, #0xa18]
    // 0xa51e24: StoreField: r0->field_1b = r6
    //     0xa51e24: stur            w6, [x0, #0x1b]
    // 0xa51e28: r7 = Instance_VerticalDirection
    //     0xa51e28: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa51e2c: ldr             x7, [x7, #0xa20]
    // 0xa51e30: StoreField: r0->field_23 = r7
    //     0xa51e30: stur            w7, [x0, #0x23]
    // 0xa51e34: r8 = Instance_Clip
    //     0xa51e34: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa51e38: ldr             x8, [x8, #0x38]
    // 0xa51e3c: StoreField: r0->field_2b = r8
    //     0xa51e3c: stur            w8, [x0, #0x2b]
    // 0xa51e40: StoreField: r0->field_2f = rZR
    //     0xa51e40: stur            xzr, [x0, #0x2f]
    // 0xa51e44: ldur            x1, [fp, #-0x50]
    // 0xa51e48: StoreField: r0->field_b = r1
    //     0xa51e48: stur            w1, [x0, #0xb]
    // 0xa51e4c: mov             x2, x5
    // 0xa51e50: mov             x5, x0
    // 0xa51e54: mov             x0, x4
    // 0xa51e58: mov             x3, x7
    // 0xa51e5c: mov             x4, x8
    // 0xa51e60: b               #0xa520d4
    // 0xa51e64: ldur            x9, [fp, #-0x18]
    // 0xa51e68: r4 = Instance_MainAxisAlignment
    //     0xa51e68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa51e6c: ldr             x4, [x4, #0xa08]
    // 0xa51e70: r7 = Instance_VerticalDirection
    //     0xa51e70: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa51e74: ldr             x7, [x7, #0xa20]
    // 0xa51e78: r5 = Instance_MainAxisSize
    //     0xa51e78: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa51e7c: ldr             x5, [x5, #0xa10]
    // 0xa51e80: r6 = Instance_CrossAxisAlignment
    //     0xa51e80: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa51e84: ldr             x6, [x6, #0xa18]
    // 0xa51e88: r3 = Instance_Axis
    //     0xa51e88: ldr             x3, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa51e8c: r0 = Instance_BlendMode
    //     0xa51e8c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa51e90: ldr             x0, [x0, #0xb30]
    // 0xa51e94: r8 = Instance_Clip
    //     0xa51e94: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa51e98: ldr             x8, [x8, #0x38]
    // 0xa51e9c: r2 = 1
    //     0xa51e9c: movz            x2, #0x1
    // 0xa51ea0: ldr             x1, [fp, #0x18]
    // 0xa51ea4: r0 = of()
    //     0xa51ea4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa51ea8: LoadField: r1 = r0->field_5b
    //     0xa51ea8: ldur            w1, [x0, #0x5b]
    // 0xa51eac: DecompressPointer r1
    //     0xa51eac: add             x1, x1, HEAP, lsl #32
    // 0xa51eb0: stur            x1, [fp, #-0x28]
    // 0xa51eb4: r0 = ColorFilter()
    //     0xa51eb4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xa51eb8: mov             x1, x0
    // 0xa51ebc: ldur            x0, [fp, #-0x28]
    // 0xa51ec0: stur            x1, [fp, #-0x48]
    // 0xa51ec4: StoreField: r1->field_7 = r0
    //     0xa51ec4: stur            w0, [x1, #7]
    // 0xa51ec8: r0 = Instance_BlendMode
    //     0xa51ec8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xa51ecc: ldr             x0, [x0, #0xb30]
    // 0xa51ed0: StoreField: r1->field_b = r0
    //     0xa51ed0: stur            w0, [x1, #0xb]
    // 0xa51ed4: r0 = 1
    //     0xa51ed4: movz            x0, #0x1
    // 0xa51ed8: StoreField: r1->field_13 = r0
    //     0xa51ed8: stur            x0, [x1, #0x13]
    // 0xa51edc: r0 = SvgPicture()
    //     0xa51edc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa51ee0: stur            x0, [fp, #-0x28]
    // 0xa51ee4: ldur            x16, [fp, #-0x48]
    // 0xa51ee8: str             x16, [SP]
    // 0xa51eec: mov             x1, x0
    // 0xa51ef0: r2 = "assets/images/green_star.svg"
    //     0xa51ef0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xa51ef4: ldr             x2, [x2, #0x9a0]
    // 0xa51ef8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa51ef8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa51efc: ldr             x4, [x4, #0xa38]
    // 0xa51f00: r0 = SvgPicture.asset()
    //     0xa51f00: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa51f04: ldur            x0, [fp, #-0x18]
    // 0xa51f08: cmp             w0, NULL
    // 0xa51f0c: b.ne            #0xa51f18
    // 0xa51f10: r0 = Null
    //     0xa51f10: mov             x0, NULL
    // 0xa51f14: b               #0xa51f50
    // 0xa51f18: LoadField: r1 = r0->field_7b
    //     0xa51f18: ldur            w1, [x0, #0x7b]
    // 0xa51f1c: DecompressPointer r1
    //     0xa51f1c: add             x1, x1, HEAP, lsl #32
    // 0xa51f20: cmp             w1, NULL
    // 0xa51f24: b.ne            #0xa51f30
    // 0xa51f28: r0 = Null
    //     0xa51f28: mov             x0, NULL
    // 0xa51f2c: b               #0xa51f50
    // 0xa51f30: LoadField: r2 = r1->field_7
    //     0xa51f30: ldur            w2, [x1, #7]
    // 0xa51f34: DecompressPointer r2
    //     0xa51f34: add             x2, x2, HEAP, lsl #32
    // 0xa51f38: cmp             w2, NULL
    // 0xa51f3c: b.ne            #0xa51f48
    // 0xa51f40: r0 = Null
    //     0xa51f40: mov             x0, NULL
    // 0xa51f44: b               #0xa51f50
    // 0xa51f48: str             x2, [SP]
    // 0xa51f4c: r0 = toString()
    //     0xa51f4c: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xa51f50: cmp             w0, NULL
    // 0xa51f54: b.ne            #0xa51f60
    // 0xa51f58: r2 = ""
    //     0xa51f58: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa51f5c: b               #0xa51f64
    // 0xa51f60: mov             x2, x0
    // 0xa51f64: ldur            x0, [fp, #-0x28]
    // 0xa51f68: ldr             x1, [fp, #0x18]
    // 0xa51f6c: stur            x2, [fp, #-0x48]
    // 0xa51f70: r0 = of()
    //     0xa51f70: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa51f74: LoadField: r1 = r0->field_87
    //     0xa51f74: ldur            w1, [x0, #0x87]
    // 0xa51f78: DecompressPointer r1
    //     0xa51f78: add             x1, x1, HEAP, lsl #32
    // 0xa51f7c: LoadField: r0 = r1->field_7
    //     0xa51f7c: ldur            w0, [x1, #7]
    // 0xa51f80: DecompressPointer r0
    //     0xa51f80: add             x0, x0, HEAP, lsl #32
    // 0xa51f84: r16 = 12.000000
    //     0xa51f84: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa51f88: ldr             x16, [x16, #0x9e8]
    // 0xa51f8c: r30 = Instance_Color
    //     0xa51f8c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa51f90: stp             lr, x16, [SP]
    // 0xa51f94: mov             x1, x0
    // 0xa51f98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa51f98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa51f9c: ldr             x4, [x4, #0xaa0]
    // 0xa51fa0: r0 = copyWith()
    //     0xa51fa0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa51fa4: stur            x0, [fp, #-0x50]
    // 0xa51fa8: r0 = Text()
    //     0xa51fa8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa51fac: mov             x2, x0
    // 0xa51fb0: ldur            x0, [fp, #-0x48]
    // 0xa51fb4: stur            x2, [fp, #-0x58]
    // 0xa51fb8: StoreField: r2->field_b = r0
    //     0xa51fb8: stur            w0, [x2, #0xb]
    // 0xa51fbc: ldur            x0, [fp, #-0x50]
    // 0xa51fc0: StoreField: r2->field_13 = r0
    //     0xa51fc0: stur            w0, [x2, #0x13]
    // 0xa51fc4: ldr             x1, [fp, #0x18]
    // 0xa51fc8: r0 = of()
    //     0xa51fc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa51fcc: LoadField: r1 = r0->field_87
    //     0xa51fcc: ldur            w1, [x0, #0x87]
    // 0xa51fd0: DecompressPointer r1
    //     0xa51fd0: add             x1, x1, HEAP, lsl #32
    // 0xa51fd4: LoadField: r0 = r1->field_2b
    //     0xa51fd4: ldur            w0, [x1, #0x2b]
    // 0xa51fd8: DecompressPointer r0
    //     0xa51fd8: add             x0, x0, HEAP, lsl #32
    // 0xa51fdc: ldr             x1, [fp, #0x18]
    // 0xa51fe0: stur            x0, [fp, #-0x48]
    // 0xa51fe4: r0 = of()
    //     0xa51fe4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa51fe8: LoadField: r1 = r0->field_5b
    //     0xa51fe8: ldur            w1, [x0, #0x5b]
    // 0xa51fec: DecompressPointer r1
    //     0xa51fec: add             x1, x1, HEAP, lsl #32
    // 0xa51ff0: r16 = 10.000000
    //     0xa51ff0: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa51ff4: stp             x1, x16, [SP]
    // 0xa51ff8: ldur            x1, [fp, #-0x48]
    // 0xa51ffc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa51ffc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa52000: ldr             x4, [x4, #0xaa0]
    // 0xa52004: r0 = copyWith()
    //     0xa52004: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa52008: stur            x0, [fp, #-0x48]
    // 0xa5200c: r0 = Text()
    //     0xa5200c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa52010: mov             x3, x0
    // 0xa52014: r0 = " Brand Rating"
    //     0xa52014: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xa52018: ldr             x0, [x0, #0xd78]
    // 0xa5201c: stur            x3, [fp, #-0x50]
    // 0xa52020: StoreField: r3->field_b = r0
    //     0xa52020: stur            w0, [x3, #0xb]
    // 0xa52024: ldur            x0, [fp, #-0x48]
    // 0xa52028: StoreField: r3->field_13 = r0
    //     0xa52028: stur            w0, [x3, #0x13]
    // 0xa5202c: r1 = Null
    //     0xa5202c: mov             x1, NULL
    // 0xa52030: r2 = 6
    //     0xa52030: movz            x2, #0x6
    // 0xa52034: r0 = AllocateArray()
    //     0xa52034: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa52038: mov             x2, x0
    // 0xa5203c: ldur            x0, [fp, #-0x28]
    // 0xa52040: stur            x2, [fp, #-0x48]
    // 0xa52044: StoreField: r2->field_f = r0
    //     0xa52044: stur            w0, [x2, #0xf]
    // 0xa52048: ldur            x0, [fp, #-0x58]
    // 0xa5204c: StoreField: r2->field_13 = r0
    //     0xa5204c: stur            w0, [x2, #0x13]
    // 0xa52050: ldur            x0, [fp, #-0x50]
    // 0xa52054: ArrayStore: r2[0] = r0  ; List_4
    //     0xa52054: stur            w0, [x2, #0x17]
    // 0xa52058: r1 = <Widget>
    //     0xa52058: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa5205c: r0 = AllocateGrowableArray()
    //     0xa5205c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa52060: mov             x1, x0
    // 0xa52064: ldur            x0, [fp, #-0x48]
    // 0xa52068: stur            x1, [fp, #-0x28]
    // 0xa5206c: StoreField: r1->field_f = r0
    //     0xa5206c: stur            w0, [x1, #0xf]
    // 0xa52070: r2 = 6
    //     0xa52070: movz            x2, #0x6
    // 0xa52074: StoreField: r1->field_b = r2
    //     0xa52074: stur            w2, [x1, #0xb]
    // 0xa52078: r0 = Row()
    //     0xa52078: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa5207c: mov             x1, x0
    // 0xa52080: r0 = Instance_Axis
    //     0xa52080: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa52084: StoreField: r1->field_f = r0
    //     0xa52084: stur            w0, [x1, #0xf]
    // 0xa52088: r0 = Instance_MainAxisAlignment
    //     0xa52088: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa5208c: ldr             x0, [x0, #0xa08]
    // 0xa52090: StoreField: r1->field_13 = r0
    //     0xa52090: stur            w0, [x1, #0x13]
    // 0xa52094: r2 = Instance_MainAxisSize
    //     0xa52094: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa52098: ldr             x2, [x2, #0xa10]
    // 0xa5209c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa5209c: stur            w2, [x1, #0x17]
    // 0xa520a0: r3 = Instance_CrossAxisAlignment
    //     0xa520a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa520a4: ldr             x3, [x3, #0xa18]
    // 0xa520a8: StoreField: r1->field_1b = r3
    //     0xa520a8: stur            w3, [x1, #0x1b]
    // 0xa520ac: r3 = Instance_VerticalDirection
    //     0xa520ac: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa520b0: ldr             x3, [x3, #0xa20]
    // 0xa520b4: StoreField: r1->field_23 = r3
    //     0xa520b4: stur            w3, [x1, #0x23]
    // 0xa520b8: r4 = Instance_Clip
    //     0xa520b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa520bc: ldr             x4, [x4, #0x38]
    // 0xa520c0: StoreField: r1->field_2b = r4
    //     0xa520c0: stur            w4, [x1, #0x2b]
    // 0xa520c4: StoreField: r1->field_2f = rZR
    //     0xa520c4: stur            xzr, [x1, #0x2f]
    // 0xa520c8: ldur            x5, [fp, #-0x28]
    // 0xa520cc: StoreField: r1->field_b = r5
    //     0xa520cc: stur            w5, [x1, #0xb]
    // 0xa520d0: mov             x5, x1
    // 0xa520d4: ldur            x1, [fp, #-0x40]
    // 0xa520d8: stur            x5, [fp, #-0x28]
    // 0xa520dc: r0 = Padding()
    //     0xa520dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa520e0: mov             x2, x0
    // 0xa520e4: r0 = Instance_EdgeInsets
    //     0xa520e4: add             x0, PP, #0x58, lsl #12  ; [pp+0x58728] Obj!EdgeInsets@d58521
    //     0xa520e8: ldr             x0, [x0, #0x728]
    // 0xa520ec: stur            x2, [fp, #-0x48]
    // 0xa520f0: StoreField: r2->field_f = r0
    //     0xa520f0: stur            w0, [x2, #0xf]
    // 0xa520f4: ldur            x0, [fp, #-0x28]
    // 0xa520f8: StoreField: r2->field_b = r0
    //     0xa520f8: stur            w0, [x2, #0xb]
    // 0xa520fc: ldur            x0, [fp, #-0x40]
    // 0xa52100: LoadField: r1 = r0->field_b
    //     0xa52100: ldur            w1, [x0, #0xb]
    // 0xa52104: LoadField: r3 = r0->field_f
    //     0xa52104: ldur            w3, [x0, #0xf]
    // 0xa52108: DecompressPointer r3
    //     0xa52108: add             x3, x3, HEAP, lsl #32
    // 0xa5210c: LoadField: r4 = r3->field_b
    //     0xa5210c: ldur            w4, [x3, #0xb]
    // 0xa52110: r3 = LoadInt32Instr(r1)
    //     0xa52110: sbfx            x3, x1, #1, #0x1f
    // 0xa52114: stur            x3, [fp, #-0x68]
    // 0xa52118: r1 = LoadInt32Instr(r4)
    //     0xa52118: sbfx            x1, x4, #1, #0x1f
    // 0xa5211c: cmp             x3, x1
    // 0xa52120: b.ne            #0xa5212c
    // 0xa52124: mov             x1, x0
    // 0xa52128: r0 = _growToNextCapacity()
    //     0xa52128: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa5212c: ldur            x3, [fp, #-0x40]
    // 0xa52130: ldur            x2, [fp, #-0x68]
    // 0xa52134: add             x0, x2, #1
    // 0xa52138: lsl             x1, x0, #1
    // 0xa5213c: StoreField: r3->field_b = r1
    //     0xa5213c: stur            w1, [x3, #0xb]
    // 0xa52140: LoadField: r1 = r3->field_f
    //     0xa52140: ldur            w1, [x3, #0xf]
    // 0xa52144: DecompressPointer r1
    //     0xa52144: add             x1, x1, HEAP, lsl #32
    // 0xa52148: ldur            x0, [fp, #-0x48]
    // 0xa5214c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa5214c: add             x25, x1, x2, lsl #2
    //     0xa52150: add             x25, x25, #0xf
    //     0xa52154: str             w0, [x25]
    //     0xa52158: tbz             w0, #0, #0xa52174
    //     0xa5215c: ldurb           w16, [x1, #-1]
    //     0xa52160: ldurb           w17, [x0, #-1]
    //     0xa52164: and             x16, x17, x16, lsr #2
    //     0xa52168: tst             x16, HEAP, lsr #32
    //     0xa5216c: b.eq            #0xa52174
    //     0xa52170: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa52174: b               #0xa5217c
    // 0xa52178: mov             x3, x1
    // 0xa5217c: ldur            x0, [fp, #-0x18]
    // 0xa52180: cmp             w0, NULL
    // 0xa52184: b.ne            #0xa52190
    // 0xa52188: r4 = Null
    //     0xa52188: mov             x4, NULL
    // 0xa5218c: b               #0xa5219c
    // 0xa52190: LoadField: r1 = r0->field_43
    //     0xa52190: ldur            w1, [x0, #0x43]
    // 0xa52194: DecompressPointer r1
    //     0xa52194: add             x1, x1, HEAP, lsl #32
    // 0xa52198: mov             x4, x1
    // 0xa5219c: stur            x4, [fp, #-0x28]
    // 0xa521a0: r1 = Null
    //     0xa521a0: mov             x1, NULL
    // 0xa521a4: r2 = 4
    //     0xa521a4: movz            x2, #0x4
    // 0xa521a8: r0 = AllocateArray()
    //     0xa521a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa521ac: mov             x1, x0
    // 0xa521b0: ldur            x0, [fp, #-0x28]
    // 0xa521b4: StoreField: r1->field_f = r0
    //     0xa521b4: stur            w0, [x1, #0xf]
    // 0xa521b8: r16 = " "
    //     0xa521b8: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xa521bc: StoreField: r1->field_13 = r16
    //     0xa521bc: stur            w16, [x1, #0x13]
    // 0xa521c0: str             x1, [SP]
    // 0xa521c4: r0 = _interpolate()
    //     0xa521c4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa521c8: ldr             x1, [fp, #0x18]
    // 0xa521cc: stur            x0, [fp, #-0x28]
    // 0xa521d0: r0 = of()
    //     0xa521d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa521d4: LoadField: r1 = r0->field_87
    //     0xa521d4: ldur            w1, [x0, #0x87]
    // 0xa521d8: DecompressPointer r1
    //     0xa521d8: add             x1, x1, HEAP, lsl #32
    // 0xa521dc: LoadField: r0 = r1->field_27
    //     0xa521dc: ldur            w0, [x1, #0x27]
    // 0xa521e0: DecompressPointer r0
    //     0xa521e0: add             x0, x0, HEAP, lsl #32
    // 0xa521e4: r16 = 16.000000
    //     0xa521e4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xa521e8: ldr             x16, [x16, #0x188]
    // 0xa521ec: r30 = Instance_Color
    //     0xa521ec: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa521f0: stp             lr, x16, [SP]
    // 0xa521f4: mov             x1, x0
    // 0xa521f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa521f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa521fc: ldr             x4, [x4, #0xaa0]
    // 0xa52200: r0 = copyWith()
    //     0xa52200: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa52204: stur            x0, [fp, #-0x48]
    // 0xa52208: r0 = Text()
    //     0xa52208: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa5220c: mov             x1, x0
    // 0xa52210: ldur            x0, [fp, #-0x28]
    // 0xa52214: stur            x1, [fp, #-0x50]
    // 0xa52218: StoreField: r1->field_b = r0
    //     0xa52218: stur            w0, [x1, #0xb]
    // 0xa5221c: ldur            x0, [fp, #-0x48]
    // 0xa52220: StoreField: r1->field_13 = r0
    //     0xa52220: stur            w0, [x1, #0x13]
    // 0xa52224: r0 = WidgetSpan()
    //     0xa52224: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xa52228: mov             x1, x0
    // 0xa5222c: ldur            x0, [fp, #-0x50]
    // 0xa52230: stur            x1, [fp, #-0x28]
    // 0xa52234: StoreField: r1->field_13 = r0
    //     0xa52234: stur            w0, [x1, #0x13]
    // 0xa52238: r0 = Instance_PlaceholderAlignment
    //     0xa52238: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xa5223c: ldr             x0, [x0, #0xa0]
    // 0xa52240: StoreField: r1->field_b = r0
    //     0xa52240: stur            w0, [x1, #0xb]
    // 0xa52244: ldur            x0, [fp, #-0x18]
    // 0xa52248: cmp             w0, NULL
    // 0xa5224c: b.ne            #0xa52258
    // 0xa52250: r2 = Null
    //     0xa52250: mov             x2, NULL
    // 0xa52254: b               #0xa52260
    // 0xa52258: LoadField: r2 = r0->field_4b
    //     0xa52258: ldur            w2, [x0, #0x4b]
    // 0xa5225c: DecompressPointer r2
    //     0xa5225c: add             x2, x2, HEAP, lsl #32
    // 0xa52260: str             x2, [SP]
    // 0xa52264: r0 = _interpolateSingle()
    //     0xa52264: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa52268: ldr             x1, [fp, #0x18]
    // 0xa5226c: stur            x0, [fp, #-0x48]
    // 0xa52270: r0 = of()
    //     0xa52270: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa52274: LoadField: r1 = r0->field_87
    //     0xa52274: ldur            w1, [x0, #0x87]
    // 0xa52278: DecompressPointer r1
    //     0xa52278: add             x1, x1, HEAP, lsl #32
    // 0xa5227c: LoadField: r0 = r1->field_2b
    //     0xa5227c: ldur            w0, [x1, #0x2b]
    // 0xa52280: DecompressPointer r0
    //     0xa52280: add             x0, x0, HEAP, lsl #32
    // 0xa52284: stur            x0, [fp, #-0x50]
    // 0xa52288: r1 = Instance_Color
    //     0xa52288: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa5228c: d0 = 0.400000
    //     0xa5228c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa52290: r0 = withOpacity()
    //     0xa52290: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa52294: r16 = Instance_TextDecoration
    //     0xa52294: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xa52298: ldr             x16, [x16, #0xe30]
    // 0xa5229c: r30 = 10.000000
    //     0xa5229c: ldr             lr, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa522a0: stp             lr, x16, [SP, #8]
    // 0xa522a4: str             x0, [SP]
    // 0xa522a8: ldur            x1, [fp, #-0x50]
    // 0xa522ac: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xa522ac: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xa522b0: ldr             x4, [x4, #0xb60]
    // 0xa522b4: r0 = copyWith()
    //     0xa522b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa522b8: stur            x0, [fp, #-0x50]
    // 0xa522bc: r0 = TextSpan()
    //     0xa522bc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa522c0: mov             x1, x0
    // 0xa522c4: ldur            x0, [fp, #-0x48]
    // 0xa522c8: stur            x1, [fp, #-0x58]
    // 0xa522cc: StoreField: r1->field_b = r0
    //     0xa522cc: stur            w0, [x1, #0xb]
    // 0xa522d0: r2 = Instance__DeferringMouseCursor
    //     0xa522d0: ldr             x2, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa522d4: ArrayStore: r1[0] = r2  ; List_4
    //     0xa522d4: stur            w2, [x1, #0x17]
    // 0xa522d8: ldur            x0, [fp, #-0x50]
    // 0xa522dc: StoreField: r1->field_7 = r0
    //     0xa522dc: stur            w0, [x1, #7]
    // 0xa522e0: ldur            x3, [fp, #-0x18]
    // 0xa522e4: cmp             w3, NULL
    // 0xa522e8: b.ne            #0xa522f4
    // 0xa522ec: r0 = Null
    //     0xa522ec: mov             x0, NULL
    // 0xa522f0: b               #0xa522fc
    // 0xa522f4: LoadField: r0 = r3->field_63
    //     0xa522f4: ldur            w0, [x3, #0x63]
    // 0xa522f8: DecompressPointer r0
    //     0xa522f8: add             x0, x0, HEAP, lsl #32
    // 0xa522fc: r4 = 60
    //     0xa522fc: movz            x4, #0x3c
    // 0xa52300: branchIfSmi(r0, 0xa5230c)
    //     0xa52300: tbz             w0, #0, #0xa5230c
    // 0xa52304: r4 = LoadClassIdInstr(r0)
    //     0xa52304: ldur            x4, [x0, #-1]
    //     0xa52308: ubfx            x4, x4, #0xc, #0x14
    // 0xa5230c: r16 = 0.000000
    //     0xa5230c: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa52310: stp             x16, x0, [SP]
    // 0xa52314: mov             x0, x4
    // 0xa52318: mov             lr, x0
    // 0xa5231c: ldr             lr, [x21, lr, lsl #3]
    // 0xa52320: blr             lr
    // 0xa52324: tbz             w0, #4, #0xa523cc
    // 0xa52328: ldur            x0, [fp, #-0x18]
    // 0xa5232c: cmp             w0, NULL
    // 0xa52330: b.eq            #0xa523cc
    // 0xa52334: LoadField: r3 = r0->field_63
    //     0xa52334: ldur            w3, [x0, #0x63]
    // 0xa52338: DecompressPointer r3
    //     0xa52338: add             x3, x3, HEAP, lsl #32
    // 0xa5233c: stur            x3, [fp, #-0x48]
    // 0xa52340: cmp             w3, NULL
    // 0xa52344: b.eq            #0xa523cc
    // 0xa52348: r1 = Null
    //     0xa52348: mov             x1, NULL
    // 0xa5234c: r2 = 6
    //     0xa5234c: movz            x2, #0x6
    // 0xa52350: r0 = AllocateArray()
    //     0xa52350: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa52354: stur            x0, [fp, #-0x50]
    // 0xa52358: r16 = " | "
    //     0xa52358: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d80] " | "
    //     0xa5235c: ldr             x16, [x16, #0xd80]
    // 0xa52360: StoreField: r0->field_f = r16
    //     0xa52360: stur            w16, [x0, #0xf]
    // 0xa52364: ldur            x16, [fp, #-0x48]
    // 0xa52368: stp             xzr, x16, [SP]
    // 0xa5236c: r4 = 0
    //     0xa5236c: movz            x4, #0
    // 0xa52370: ldr             x0, [SP, #8]
    // 0xa52374: r16 = UnlinkedCall_0x613b5c
    //     0xa52374: add             x16, PP, #0x58, lsl #12  ; [pp+0x58730] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa52378: add             x16, x16, #0x730
    // 0xa5237c: ldp             x5, lr, [x16]
    // 0xa52380: blr             lr
    // 0xa52384: ldur            x1, [fp, #-0x50]
    // 0xa52388: ArrayStore: r1[1] = r0  ; List_4
    //     0xa52388: add             x25, x1, #0x13
    //     0xa5238c: str             w0, [x25]
    //     0xa52390: tbz             w0, #0, #0xa523ac
    //     0xa52394: ldurb           w16, [x1, #-1]
    //     0xa52398: ldurb           w17, [x0, #-1]
    //     0xa5239c: and             x16, x17, x16, lsr #2
    //     0xa523a0: tst             x16, HEAP, lsr #32
    //     0xa523a4: b.eq            #0xa523ac
    //     0xa523a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa523ac: ldur            x0, [fp, #-0x50]
    // 0xa523b0: r16 = "% OFF"
    //     0xa523b0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xa523b4: ldr             x16, [x16, #0xd98]
    // 0xa523b8: ArrayStore: r0[0] = r16  ; List_4
    //     0xa523b8: stur            w16, [x0, #0x17]
    // 0xa523bc: str             x0, [SP]
    // 0xa523c0: r0 = _interpolate()
    //     0xa523c0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa523c4: mov             x4, x0
    // 0xa523c8: b               #0xa523d0
    // 0xa523cc: r4 = ""
    //     0xa523cc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa523d0: ldur            x2, [fp, #-0x28]
    // 0xa523d4: ldur            x0, [fp, #-0x58]
    // 0xa523d8: ldur            x3, [fp, #-0x40]
    // 0xa523dc: ldr             x1, [fp, #0x18]
    // 0xa523e0: stur            x4, [fp, #-0x48]
    // 0xa523e4: r0 = of()
    //     0xa523e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa523e8: LoadField: r1 = r0->field_87
    //     0xa523e8: ldur            w1, [x0, #0x87]
    // 0xa523ec: DecompressPointer r1
    //     0xa523ec: add             x1, x1, HEAP, lsl #32
    // 0xa523f0: LoadField: r0 = r1->field_2b
    //     0xa523f0: ldur            w0, [x1, #0x2b]
    // 0xa523f4: DecompressPointer r0
    //     0xa523f4: add             x0, x0, HEAP, lsl #32
    // 0xa523f8: r16 = Instance_Color
    //     0xa523f8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xa523fc: ldr             x16, [x16, #0x858]
    // 0xa52400: r30 = 12.000000
    //     0xa52400: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa52404: ldr             lr, [lr, #0x9e8]
    // 0xa52408: stp             lr, x16, [SP]
    // 0xa5240c: mov             x1, x0
    // 0xa52410: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa52410: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa52414: ldr             x4, [x4, #0x9b8]
    // 0xa52418: r0 = copyWith()
    //     0xa52418: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa5241c: stur            x0, [fp, #-0x50]
    // 0xa52420: r0 = TextSpan()
    //     0xa52420: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa52424: mov             x3, x0
    // 0xa52428: ldur            x0, [fp, #-0x48]
    // 0xa5242c: stur            x3, [fp, #-0x60]
    // 0xa52430: StoreField: r3->field_b = r0
    //     0xa52430: stur            w0, [x3, #0xb]
    // 0xa52434: r0 = Instance__DeferringMouseCursor
    //     0xa52434: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa52438: ArrayStore: r3[0] = r0  ; List_4
    //     0xa52438: stur            w0, [x3, #0x17]
    // 0xa5243c: ldur            x1, [fp, #-0x50]
    // 0xa52440: StoreField: r3->field_7 = r1
    //     0xa52440: stur            w1, [x3, #7]
    // 0xa52444: r1 = Null
    //     0xa52444: mov             x1, NULL
    // 0xa52448: r2 = 6
    //     0xa52448: movz            x2, #0x6
    // 0xa5244c: r0 = AllocateArray()
    //     0xa5244c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa52450: mov             x2, x0
    // 0xa52454: ldur            x0, [fp, #-0x28]
    // 0xa52458: stur            x2, [fp, #-0x48]
    // 0xa5245c: StoreField: r2->field_f = r0
    //     0xa5245c: stur            w0, [x2, #0xf]
    // 0xa52460: ldur            x0, [fp, #-0x58]
    // 0xa52464: StoreField: r2->field_13 = r0
    //     0xa52464: stur            w0, [x2, #0x13]
    // 0xa52468: ldur            x0, [fp, #-0x60]
    // 0xa5246c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa5246c: stur            w0, [x2, #0x17]
    // 0xa52470: r1 = <InlineSpan>
    //     0xa52470: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xa52474: ldr             x1, [x1, #0xe40]
    // 0xa52478: r0 = AllocateGrowableArray()
    //     0xa52478: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa5247c: mov             x1, x0
    // 0xa52480: ldur            x0, [fp, #-0x48]
    // 0xa52484: stur            x1, [fp, #-0x28]
    // 0xa52488: StoreField: r1->field_f = r0
    //     0xa52488: stur            w0, [x1, #0xf]
    // 0xa5248c: r0 = 6
    //     0xa5248c: movz            x0, #0x6
    // 0xa52490: StoreField: r1->field_b = r0
    //     0xa52490: stur            w0, [x1, #0xb]
    // 0xa52494: r0 = TextSpan()
    //     0xa52494: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xa52498: mov             x1, x0
    // 0xa5249c: ldur            x0, [fp, #-0x28]
    // 0xa524a0: stur            x1, [fp, #-0x48]
    // 0xa524a4: StoreField: r1->field_f = r0
    //     0xa524a4: stur            w0, [x1, #0xf]
    // 0xa524a8: r0 = Instance__DeferringMouseCursor
    //     0xa524a8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xa524ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xa524ac: stur            w0, [x1, #0x17]
    // 0xa524b0: r0 = RichText()
    //     0xa524b0: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xa524b4: mov             x1, x0
    // 0xa524b8: ldur            x2, [fp, #-0x48]
    // 0xa524bc: stur            x0, [fp, #-0x28]
    // 0xa524c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa524c0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa524c4: r0 = RichText()
    //     0xa524c4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xa524c8: r0 = Padding()
    //     0xa524c8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa524cc: mov             x2, x0
    // 0xa524d0: r0 = Instance_EdgeInsets
    //     0xa524d0: add             x0, PP, #0x58, lsl #12  ; [pp+0x58740] Obj!EdgeInsets@d584f1
    //     0xa524d4: ldr             x0, [x0, #0x740]
    // 0xa524d8: stur            x2, [fp, #-0x48]
    // 0xa524dc: StoreField: r2->field_f = r0
    //     0xa524dc: stur            w0, [x2, #0xf]
    // 0xa524e0: ldur            x0, [fp, #-0x28]
    // 0xa524e4: StoreField: r2->field_b = r0
    //     0xa524e4: stur            w0, [x2, #0xb]
    // 0xa524e8: ldur            x0, [fp, #-0x40]
    // 0xa524ec: LoadField: r1 = r0->field_b
    //     0xa524ec: ldur            w1, [x0, #0xb]
    // 0xa524f0: LoadField: r3 = r0->field_f
    //     0xa524f0: ldur            w3, [x0, #0xf]
    // 0xa524f4: DecompressPointer r3
    //     0xa524f4: add             x3, x3, HEAP, lsl #32
    // 0xa524f8: LoadField: r4 = r3->field_b
    //     0xa524f8: ldur            w4, [x3, #0xb]
    // 0xa524fc: r3 = LoadInt32Instr(r1)
    //     0xa524fc: sbfx            x3, x1, #1, #0x1f
    // 0xa52500: stur            x3, [fp, #-0x68]
    // 0xa52504: r1 = LoadInt32Instr(r4)
    //     0xa52504: sbfx            x1, x4, #1, #0x1f
    // 0xa52508: cmp             x3, x1
    // 0xa5250c: b.ne            #0xa52518
    // 0xa52510: mov             x1, x0
    // 0xa52514: r0 = _growToNextCapacity()
    //     0xa52514: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa52518: ldur            x4, [fp, #-0x20]
    // 0xa5251c: ldur            x2, [fp, #-0x40]
    // 0xa52520: ldur            x3, [fp, #-0x68]
    // 0xa52524: add             x0, x3, #1
    // 0xa52528: lsl             x1, x0, #1
    // 0xa5252c: StoreField: r2->field_b = r1
    //     0xa5252c: stur            w1, [x2, #0xb]
    // 0xa52530: LoadField: r1 = r2->field_f
    //     0xa52530: ldur            w1, [x2, #0xf]
    // 0xa52534: DecompressPointer r1
    //     0xa52534: add             x1, x1, HEAP, lsl #32
    // 0xa52538: ldur            x0, [fp, #-0x48]
    // 0xa5253c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa5253c: add             x25, x1, x3, lsl #2
    //     0xa52540: add             x25, x25, #0xf
    //     0xa52544: str             w0, [x25]
    //     0xa52548: tbz             w0, #0, #0xa52564
    //     0xa5254c: ldurb           w16, [x1, #-1]
    //     0xa52550: ldurb           w17, [x0, #-1]
    //     0xa52554: and             x16, x17, x16, lsr #2
    //     0xa52558: tst             x16, HEAP, lsr #32
    //     0xa5255c: b.eq            #0xa52564
    //     0xa52560: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa52564: tbnz            w4, #4, #0xa5293c
    // 0xa52568: ldur            x0, [fp, #-8]
    // 0xa5256c: LoadField: r1 = r0->field_f
    //     0xa5256c: ldur            w1, [x0, #0xf]
    // 0xa52570: DecompressPointer r1
    //     0xa52570: add             x1, x1, HEAP, lsl #32
    // 0xa52574: LoadField: r0 = r1->field_b
    //     0xa52574: ldur            w0, [x1, #0xb]
    // 0xa52578: DecompressPointer r0
    //     0xa52578: add             x0, x0, HEAP, lsl #32
    // 0xa5257c: cmp             w0, NULL
    // 0xa52580: b.eq            #0xa52b74
    // 0xa52584: LoadField: r1 = r0->field_13
    //     0xa52584: ldur            w1, [x0, #0x13]
    // 0xa52588: DecompressPointer r1
    //     0xa52588: add             x1, x1, HEAP, lsl #32
    // 0xa5258c: tbnz            w1, #4, #0xa5293c
    // 0xa52590: ldur            x0, [fp, #-0x18]
    // 0xa52594: cmp             w0, NULL
    // 0xa52598: b.ne            #0xa525a4
    // 0xa5259c: r1 = Null
    //     0xa5259c: mov             x1, NULL
    // 0xa525a0: b               #0xa525ac
    // 0xa525a4: LoadField: r1 = r0->field_4f
    //     0xa525a4: ldur            w1, [x0, #0x4f]
    // 0xa525a8: DecompressPointer r1
    //     0xa525a8: add             x1, x1, HEAP, lsl #32
    // 0xa525ac: cmp             w1, NULL
    // 0xa525b0: b.eq            #0xa525b8
    // 0xa525b4: tbnz            w1, #4, #0xa525e4
    // 0xa525b8: ldr             x1, [fp, #0x18]
    // 0xa525bc: r0 = of()
    //     0xa525bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa525c0: LoadField: r1 = r0->field_5b
    //     0xa525c0: ldur            w1, [x0, #0x5b]
    // 0xa525c4: DecompressPointer r1
    //     0xa525c4: add             x1, x1, HEAP, lsl #32
    // 0xa525c8: r16 = <Color>
    //     0xa525c8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa525cc: ldr             x16, [x16, #0xf80]
    // 0xa525d0: stp             x1, x16, [SP]
    // 0xa525d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa525d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa525d8: r0 = all()
    //     0xa525d8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa525dc: mov             x2, x0
    // 0xa525e0: b               #0xa52600
    // 0xa525e4: r16 = <Color>
    //     0xa525e4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa525e8: ldr             x16, [x16, #0xf80]
    // 0xa525ec: r30 = Instance_Color
    //     0xa525ec: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa525f0: stp             lr, x16, [SP]
    // 0xa525f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa525f4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa525f8: r0 = all()
    //     0xa525f8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa525fc: mov             x2, x0
    // 0xa52600: ldur            x0, [fp, #-0x18]
    // 0xa52604: stur            x2, [fp, #-8]
    // 0xa52608: cmp             w0, NULL
    // 0xa5260c: b.ne            #0xa52618
    // 0xa52610: r1 = Null
    //     0xa52610: mov             x1, NULL
    // 0xa52614: b               #0xa52620
    // 0xa52618: LoadField: r1 = r0->field_4f
    //     0xa52618: ldur            w1, [x0, #0x4f]
    // 0xa5261c: DecompressPointer r1
    //     0xa5261c: add             x1, x1, HEAP, lsl #32
    // 0xa52620: cmp             w1, NULL
    // 0xa52624: b.eq            #0xa5262c
    // 0xa52628: tbnz            w1, #4, #0xa52670
    // 0xa5262c: ldr             x1, [fp, #0x18]
    // 0xa52630: r0 = of()
    //     0xa52630: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa52634: LoadField: r1 = r0->field_5b
    //     0xa52634: ldur            w1, [x0, #0x5b]
    // 0xa52638: DecompressPointer r1
    //     0xa52638: add             x1, x1, HEAP, lsl #32
    // 0xa5263c: r0 = LoadClassIdInstr(r1)
    //     0xa5263c: ldur            x0, [x1, #-1]
    //     0xa52640: ubfx            x0, x0, #0xc, #0x14
    // 0xa52644: d0 = 0.200000
    //     0xa52644: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xa52648: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa52648: sub             lr, x0, #0xffa
    //     0xa5264c: ldr             lr, [x21, lr, lsl #3]
    //     0xa52650: blr             lr
    // 0xa52654: r16 = <Color>
    //     0xa52654: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa52658: ldr             x16, [x16, #0xf80]
    // 0xa5265c: stp             x0, x16, [SP]
    // 0xa52660: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa52660: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa52664: r0 = all()
    //     0xa52664: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa52668: mov             x2, x0
    // 0xa5266c: b               #0xa5268c
    // 0xa52670: r16 = <Color>
    //     0xa52670: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa52674: ldr             x16, [x16, #0xf80]
    // 0xa52678: r30 = Instance_Color
    //     0xa52678: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa5267c: stp             lr, x16, [SP]
    // 0xa52680: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa52680: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa52684: r0 = all()
    //     0xa52684: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa52688: mov             x2, x0
    // 0xa5268c: ldur            x0, [fp, #-0x18]
    // 0xa52690: stur            x2, [fp, #-0x20]
    // 0xa52694: cmp             w0, NULL
    // 0xa52698: b.ne            #0xa526a4
    // 0xa5269c: r1 = Null
    //     0xa5269c: mov             x1, NULL
    // 0xa526a0: b               #0xa526ac
    // 0xa526a4: LoadField: r1 = r0->field_4f
    //     0xa526a4: ldur            w1, [x0, #0x4f]
    // 0xa526a8: DecompressPointer r1
    //     0xa526a8: add             x1, x1, HEAP, lsl #32
    // 0xa526ac: cmp             w1, NULL
    // 0xa526b0: b.eq            #0xa526b8
    // 0xa526b4: tbnz            w1, #4, #0xa52700
    // 0xa526b8: ldr             x1, [fp, #0x18]
    // 0xa526bc: r0 = of()
    //     0xa526bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa526c0: LoadField: r1 = r0->field_5b
    //     0xa526c0: ldur            w1, [x0, #0x5b]
    // 0xa526c4: DecompressPointer r1
    //     0xa526c4: add             x1, x1, HEAP, lsl #32
    // 0xa526c8: stur            x1, [fp, #-0x28]
    // 0xa526cc: r0 = BorderSide()
    //     0xa526cc: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xa526d0: mov             x1, x0
    // 0xa526d4: ldur            x0, [fp, #-0x28]
    // 0xa526d8: StoreField: r1->field_7 = r0
    //     0xa526d8: stur            w0, [x1, #7]
    // 0xa526dc: d0 = 1.000000
    //     0xa526dc: fmov            d0, #1.00000000
    // 0xa526e0: StoreField: r1->field_b = d0
    //     0xa526e0: stur            d0, [x1, #0xb]
    // 0xa526e4: r0 = Instance_BorderStyle
    //     0xa526e4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xa526e8: ldr             x0, [x0, #0xf68]
    // 0xa526ec: StoreField: r1->field_13 = r0
    //     0xa526ec: stur            w0, [x1, #0x13]
    // 0xa526f0: d0 = -1.000000
    //     0xa526f0: fmov            d0, #-1.00000000
    // 0xa526f4: ArrayStore: r1[0] = d0  ; List_8
    //     0xa526f4: stur            d0, [x1, #0x17]
    // 0xa526f8: mov             x3, x1
    // 0xa526fc: b               #0xa52708
    // 0xa52700: r3 = Instance_BorderSide
    //     0xa52700: add             x3, PP, #0x55, lsl #12  ; [pp+0x55958] Obj!BorderSide@d62f31
    //     0xa52704: ldr             x3, [x3, #0x958]
    // 0xa52708: ldur            x0, [fp, #-0x18]
    // 0xa5270c: ldur            x2, [fp, #-8]
    // 0xa52710: ldur            x1, [fp, #-0x20]
    // 0xa52714: stur            x3, [fp, #-0x28]
    // 0xa52718: r0 = RoundedRectangleBorder()
    //     0xa52718: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa5271c: mov             x1, x0
    // 0xa52720: r0 = Instance_BorderRadius
    //     0xa52720: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xa52724: ldr             x0, [x0, #0x460]
    // 0xa52728: StoreField: r1->field_b = r0
    //     0xa52728: stur            w0, [x1, #0xb]
    // 0xa5272c: ldur            x0, [fp, #-0x28]
    // 0xa52730: StoreField: r1->field_7 = r0
    //     0xa52730: stur            w0, [x1, #7]
    // 0xa52734: r16 = <RoundedRectangleBorder>
    //     0xa52734: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa52738: ldr             x16, [x16, #0xf78]
    // 0xa5273c: stp             x1, x16, [SP]
    // 0xa52740: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa52740: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa52744: r0 = all()
    //     0xa52744: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa52748: stur            x0, [fp, #-0x28]
    // 0xa5274c: r0 = ButtonStyle()
    //     0xa5274c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa52750: mov             x1, x0
    // 0xa52754: ldur            x0, [fp, #-0x20]
    // 0xa52758: stur            x1, [fp, #-0x48]
    // 0xa5275c: StoreField: r1->field_b = r0
    //     0xa5275c: stur            w0, [x1, #0xb]
    // 0xa52760: ldur            x0, [fp, #-8]
    // 0xa52764: StoreField: r1->field_f = r0
    //     0xa52764: stur            w0, [x1, #0xf]
    // 0xa52768: ldur            x0, [fp, #-0x28]
    // 0xa5276c: StoreField: r1->field_43 = r0
    //     0xa5276c: stur            w0, [x1, #0x43]
    // 0xa52770: r0 = TextButtonThemeData()
    //     0xa52770: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa52774: mov             x2, x0
    // 0xa52778: ldur            x0, [fp, #-0x48]
    // 0xa5277c: stur            x2, [fp, #-8]
    // 0xa52780: StoreField: r2->field_7 = r0
    //     0xa52780: stur            w0, [x2, #7]
    // 0xa52784: ldur            x0, [fp, #-0x18]
    // 0xa52788: cmp             w0, NULL
    // 0xa5278c: b.ne            #0xa52798
    // 0xa52790: r0 = Null
    //     0xa52790: mov             x0, NULL
    // 0xa52794: b               #0xa527a4
    // 0xa52798: LoadField: r1 = r0->field_f
    //     0xa52798: ldur            w1, [x0, #0xf]
    // 0xa5279c: DecompressPointer r1
    //     0xa5279c: add             x1, x1, HEAP, lsl #32
    // 0xa527a0: mov             x0, x1
    // 0xa527a4: cmp             w0, NULL
    // 0xa527a8: b.ne            #0xa527b4
    // 0xa527ac: r1 = ""
    //     0xa527ac: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa527b0: b               #0xa527b8
    // 0xa527b4: mov             x1, x0
    // 0xa527b8: ldur            x0, [fp, #-0x40]
    // 0xa527bc: r0 = capitalizeFirstWord()
    //     0xa527bc: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa527c0: ldr             x1, [fp, #0x18]
    // 0xa527c4: stur            x0, [fp, #-0x18]
    // 0xa527c8: r0 = of()
    //     0xa527c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa527cc: LoadField: r1 = r0->field_87
    //     0xa527cc: ldur            w1, [x0, #0x87]
    // 0xa527d0: DecompressPointer r1
    //     0xa527d0: add             x1, x1, HEAP, lsl #32
    // 0xa527d4: LoadField: r0 = r1->field_7
    //     0xa527d4: ldur            w0, [x1, #7]
    // 0xa527d8: DecompressPointer r0
    //     0xa527d8: add             x0, x0, HEAP, lsl #32
    // 0xa527dc: r16 = 14.000000
    //     0xa527dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa527e0: ldr             x16, [x16, #0x1d8]
    // 0xa527e4: r30 = Instance_Color
    //     0xa527e4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa527e8: stp             lr, x16, [SP]
    // 0xa527ec: mov             x1, x0
    // 0xa527f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa527f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa527f4: ldr             x4, [x4, #0xaa0]
    // 0xa527f8: r0 = copyWith()
    //     0xa527f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa527fc: stur            x0, [fp, #-0x20]
    // 0xa52800: r0 = Text()
    //     0xa52800: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa52804: mov             x3, x0
    // 0xa52808: ldur            x0, [fp, #-0x18]
    // 0xa5280c: stur            x3, [fp, #-0x28]
    // 0xa52810: StoreField: r3->field_b = r0
    //     0xa52810: stur            w0, [x3, #0xb]
    // 0xa52814: ldur            x0, [fp, #-0x20]
    // 0xa52818: StoreField: r3->field_13 = r0
    //     0xa52818: stur            w0, [x3, #0x13]
    // 0xa5281c: ldur            x2, [fp, #-0x10]
    // 0xa52820: r1 = Function '<anonymous closure>':.
    //     0xa52820: add             x1, PP, #0x58, lsl #12  ; [pp+0x58748] AnonymousClosure: (0xa52e6c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xa52824: ldr             x1, [x1, #0x748]
    // 0xa52828: r0 = AllocateClosure()
    //     0xa52828: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5282c: stur            x0, [fp, #-0x18]
    // 0xa52830: r0 = TextButton()
    //     0xa52830: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa52834: mov             x1, x0
    // 0xa52838: ldur            x0, [fp, #-0x18]
    // 0xa5283c: stur            x1, [fp, #-0x20]
    // 0xa52840: StoreField: r1->field_b = r0
    //     0xa52840: stur            w0, [x1, #0xb]
    // 0xa52844: r0 = false
    //     0xa52844: add             x0, NULL, #0x30  ; false
    // 0xa52848: StoreField: r1->field_27 = r0
    //     0xa52848: stur            w0, [x1, #0x27]
    // 0xa5284c: r2 = true
    //     0xa5284c: add             x2, NULL, #0x20  ; true
    // 0xa52850: StoreField: r1->field_2f = r2
    //     0xa52850: stur            w2, [x1, #0x2f]
    // 0xa52854: ldur            x3, [fp, #-0x28]
    // 0xa52858: StoreField: r1->field_37 = r3
    //     0xa52858: stur            w3, [x1, #0x37]
    // 0xa5285c: r0 = TextButtonTheme()
    //     0xa5285c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa52860: mov             x1, x0
    // 0xa52864: ldur            x0, [fp, #-8]
    // 0xa52868: stur            x1, [fp, #-0x18]
    // 0xa5286c: StoreField: r1->field_f = r0
    //     0xa5286c: stur            w0, [x1, #0xf]
    // 0xa52870: ldur            x0, [fp, #-0x20]
    // 0xa52874: StoreField: r1->field_b = r0
    //     0xa52874: stur            w0, [x1, #0xb]
    // 0xa52878: r0 = SizedBox()
    //     0xa52878: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa5287c: mov             x1, x0
    // 0xa52880: r0 = inf
    //     0xa52880: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xa52884: ldr             x0, [x0, #0x9f8]
    // 0xa52888: stur            x1, [fp, #-8]
    // 0xa5288c: StoreField: r1->field_f = r0
    //     0xa5288c: stur            w0, [x1, #0xf]
    // 0xa52890: r0 = 40.000000
    //     0xa52890: add             x0, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xa52894: ldr             x0, [x0, #8]
    // 0xa52898: StoreField: r1->field_13 = r0
    //     0xa52898: stur            w0, [x1, #0x13]
    // 0xa5289c: ldur            x0, [fp, #-0x18]
    // 0xa528a0: StoreField: r1->field_b = r0
    //     0xa528a0: stur            w0, [x1, #0xb]
    // 0xa528a4: r0 = Padding()
    //     0xa528a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa528a8: mov             x2, x0
    // 0xa528ac: r0 = Instance_EdgeInsets
    //     0xa528ac: add             x0, PP, #0x55, lsl #12  ; [pp+0x55968] Obj!EdgeInsets@d58581
    //     0xa528b0: ldr             x0, [x0, #0x968]
    // 0xa528b4: stur            x2, [fp, #-0x18]
    // 0xa528b8: StoreField: r2->field_f = r0
    //     0xa528b8: stur            w0, [x2, #0xf]
    // 0xa528bc: ldur            x0, [fp, #-8]
    // 0xa528c0: StoreField: r2->field_b = r0
    //     0xa528c0: stur            w0, [x2, #0xb]
    // 0xa528c4: ldur            x0, [fp, #-0x40]
    // 0xa528c8: LoadField: r1 = r0->field_b
    //     0xa528c8: ldur            w1, [x0, #0xb]
    // 0xa528cc: LoadField: r3 = r0->field_f
    //     0xa528cc: ldur            w3, [x0, #0xf]
    // 0xa528d0: DecompressPointer r3
    //     0xa528d0: add             x3, x3, HEAP, lsl #32
    // 0xa528d4: LoadField: r4 = r3->field_b
    //     0xa528d4: ldur            w4, [x3, #0xb]
    // 0xa528d8: r3 = LoadInt32Instr(r1)
    //     0xa528d8: sbfx            x3, x1, #1, #0x1f
    // 0xa528dc: stur            x3, [fp, #-0x68]
    // 0xa528e0: r1 = LoadInt32Instr(r4)
    //     0xa528e0: sbfx            x1, x4, #1, #0x1f
    // 0xa528e4: cmp             x3, x1
    // 0xa528e8: b.ne            #0xa528f4
    // 0xa528ec: mov             x1, x0
    // 0xa528f0: r0 = _growToNextCapacity()
    //     0xa528f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa528f4: ldur            x2, [fp, #-0x40]
    // 0xa528f8: ldur            x3, [fp, #-0x68]
    // 0xa528fc: add             x0, x3, #1
    // 0xa52900: lsl             x1, x0, #1
    // 0xa52904: StoreField: r2->field_b = r1
    //     0xa52904: stur            w1, [x2, #0xb]
    // 0xa52908: LoadField: r1 = r2->field_f
    //     0xa52908: ldur            w1, [x2, #0xf]
    // 0xa5290c: DecompressPointer r1
    //     0xa5290c: add             x1, x1, HEAP, lsl #32
    // 0xa52910: ldur            x0, [fp, #-0x18]
    // 0xa52914: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa52914: add             x25, x1, x3, lsl #2
    //     0xa52918: add             x25, x25, #0xf
    //     0xa5291c: str             w0, [x25]
    //     0xa52920: tbz             w0, #0, #0xa5293c
    //     0xa52924: ldurb           w16, [x1, #-1]
    //     0xa52928: ldurb           w17, [x0, #-1]
    //     0xa5292c: and             x16, x17, x16, lsr #2
    //     0xa52930: tst             x16, HEAP, lsr #32
    //     0xa52934: b.eq            #0xa5293c
    //     0xa52938: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa5293c: ldur            x1, [fp, #-0x38]
    // 0xa52940: ldur            x0, [fp, #-0x30]
    // 0xa52944: r0 = Column()
    //     0xa52944: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa52948: mov             x1, x0
    // 0xa5294c: r0 = Instance_Axis
    //     0xa5294c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa52950: stur            x1, [fp, #-8]
    // 0xa52954: StoreField: r1->field_f = r0
    //     0xa52954: stur            w0, [x1, #0xf]
    // 0xa52958: r2 = Instance_MainAxisAlignment
    //     0xa52958: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa5295c: ldr             x2, [x2, #0xa08]
    // 0xa52960: StoreField: r1->field_13 = r2
    //     0xa52960: stur            w2, [x1, #0x13]
    // 0xa52964: r3 = Instance_MainAxisSize
    //     0xa52964: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xa52968: ldr             x3, [x3, #0xdd0]
    // 0xa5296c: ArrayStore: r1[0] = r3  ; List_4
    //     0xa5296c: stur            w3, [x1, #0x17]
    // 0xa52970: r3 = Instance_CrossAxisAlignment
    //     0xa52970: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa52974: ldr             x3, [x3, #0x890]
    // 0xa52978: StoreField: r1->field_1b = r3
    //     0xa52978: stur            w3, [x1, #0x1b]
    // 0xa5297c: r4 = Instance_VerticalDirection
    //     0xa5297c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa52980: ldr             x4, [x4, #0xa20]
    // 0xa52984: StoreField: r1->field_23 = r4
    //     0xa52984: stur            w4, [x1, #0x23]
    // 0xa52988: r5 = Instance_Clip
    //     0xa52988: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa5298c: ldr             x5, [x5, #0x38]
    // 0xa52990: StoreField: r1->field_2b = r5
    //     0xa52990: stur            w5, [x1, #0x2b]
    // 0xa52994: StoreField: r1->field_2f = rZR
    //     0xa52994: stur            xzr, [x1, #0x2f]
    // 0xa52998: ldur            x6, [fp, #-0x40]
    // 0xa5299c: StoreField: r1->field_b = r6
    //     0xa5299c: stur            w6, [x1, #0xb]
    // 0xa529a0: r0 = Padding()
    //     0xa529a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa529a4: mov             x2, x0
    // 0xa529a8: r0 = Instance_EdgeInsets
    //     0xa529a8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xa529ac: ldr             x0, [x0, #0x668]
    // 0xa529b0: stur            x2, [fp, #-0x18]
    // 0xa529b4: StoreField: r2->field_f = r0
    //     0xa529b4: stur            w0, [x2, #0xf]
    // 0xa529b8: ldur            x0, [fp, #-8]
    // 0xa529bc: StoreField: r2->field_b = r0
    //     0xa529bc: stur            w0, [x2, #0xb]
    // 0xa529c0: r1 = <FlexParentData>
    //     0xa529c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa529c4: ldr             x1, [x1, #0xe00]
    // 0xa529c8: r0 = Expanded()
    //     0xa529c8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa529cc: mov             x3, x0
    // 0xa529d0: r0 = 5
    //     0xa529d0: movz            x0, #0x5
    // 0xa529d4: stur            x3, [fp, #-8]
    // 0xa529d8: StoreField: r3->field_13 = r0
    //     0xa529d8: stur            x0, [x3, #0x13]
    // 0xa529dc: r0 = Instance_FlexFit
    //     0xa529dc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa529e0: ldr             x0, [x0, #0xe08]
    // 0xa529e4: StoreField: r3->field_1b = r0
    //     0xa529e4: stur            w0, [x3, #0x1b]
    // 0xa529e8: ldur            x0, [fp, #-0x18]
    // 0xa529ec: StoreField: r3->field_b = r0
    //     0xa529ec: stur            w0, [x3, #0xb]
    // 0xa529f0: r1 = Null
    //     0xa529f0: mov             x1, NULL
    // 0xa529f4: r2 = 4
    //     0xa529f4: movz            x2, #0x4
    // 0xa529f8: r0 = AllocateArray()
    //     0xa529f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa529fc: mov             x2, x0
    // 0xa52a00: ldur            x0, [fp, #-0x30]
    // 0xa52a04: stur            x2, [fp, #-0x18]
    // 0xa52a08: StoreField: r2->field_f = r0
    //     0xa52a08: stur            w0, [x2, #0xf]
    // 0xa52a0c: ldur            x0, [fp, #-8]
    // 0xa52a10: StoreField: r2->field_13 = r0
    //     0xa52a10: stur            w0, [x2, #0x13]
    // 0xa52a14: r1 = <Widget>
    //     0xa52a14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa52a18: r0 = AllocateGrowableArray()
    //     0xa52a18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa52a1c: mov             x1, x0
    // 0xa52a20: ldur            x0, [fp, #-0x18]
    // 0xa52a24: stur            x1, [fp, #-8]
    // 0xa52a28: StoreField: r1->field_f = r0
    //     0xa52a28: stur            w0, [x1, #0xf]
    // 0xa52a2c: r0 = 4
    //     0xa52a2c: movz            x0, #0x4
    // 0xa52a30: StoreField: r1->field_b = r0
    //     0xa52a30: stur            w0, [x1, #0xb]
    // 0xa52a34: r0 = Column()
    //     0xa52a34: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa52a38: mov             x3, x0
    // 0xa52a3c: r0 = Instance_Axis
    //     0xa52a3c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa52a40: stur            x3, [fp, #-0x18]
    // 0xa52a44: StoreField: r3->field_f = r0
    //     0xa52a44: stur            w0, [x3, #0xf]
    // 0xa52a48: r0 = Instance_MainAxisAlignment
    //     0xa52a48: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa52a4c: ldr             x0, [x0, #0xa08]
    // 0xa52a50: StoreField: r3->field_13 = r0
    //     0xa52a50: stur            w0, [x3, #0x13]
    // 0xa52a54: r0 = Instance_MainAxisSize
    //     0xa52a54: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa52a58: ldr             x0, [x0, #0xa10]
    // 0xa52a5c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa52a5c: stur            w0, [x3, #0x17]
    // 0xa52a60: r0 = Instance_CrossAxisAlignment
    //     0xa52a60: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa52a64: ldr             x0, [x0, #0x890]
    // 0xa52a68: StoreField: r3->field_1b = r0
    //     0xa52a68: stur            w0, [x3, #0x1b]
    // 0xa52a6c: r0 = Instance_VerticalDirection
    //     0xa52a6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa52a70: ldr             x0, [x0, #0xa20]
    // 0xa52a74: StoreField: r3->field_23 = r0
    //     0xa52a74: stur            w0, [x3, #0x23]
    // 0xa52a78: r0 = Instance_Clip
    //     0xa52a78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa52a7c: ldr             x0, [x0, #0x38]
    // 0xa52a80: StoreField: r3->field_2b = r0
    //     0xa52a80: stur            w0, [x3, #0x2b]
    // 0xa52a84: StoreField: r3->field_2f = rZR
    //     0xa52a84: stur            xzr, [x3, #0x2f]
    // 0xa52a88: ldur            x0, [fp, #-8]
    // 0xa52a8c: StoreField: r3->field_b = r0
    //     0xa52a8c: stur            w0, [x3, #0xb]
    // 0xa52a90: ldur            x2, [fp, #-0x10]
    // 0xa52a94: r1 = Function '<anonymous closure>':.
    //     0xa52a94: add             x1, PP, #0x58, lsl #12  ; [pp+0x58750] AnonymousClosure: (0xa52ce0), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xa52a98: ldr             x1, [x1, #0x750]
    // 0xa52a9c: r0 = AllocateClosure()
    //     0xa52a9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa52aa0: stur            x0, [fp, #-8]
    // 0xa52aa4: r0 = VisibilityDetector()
    //     0xa52aa4: bl              #0xa4f4ac  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0xa52aa8: mov             x1, x0
    // 0xa52aac: ldur            x0, [fp, #-8]
    // 0xa52ab0: stur            x1, [fp, #-0x20]
    // 0xa52ab4: StoreField: r1->field_f = r0
    //     0xa52ab4: stur            w0, [x1, #0xf]
    // 0xa52ab8: ldur            x0, [fp, #-0x18]
    // 0xa52abc: StoreField: r1->field_b = r0
    //     0xa52abc: stur            w0, [x1, #0xb]
    // 0xa52ac0: ldur            x0, [fp, #-0x38]
    // 0xa52ac4: StoreField: r1->field_7 = r0
    //     0xa52ac4: stur            w0, [x1, #7]
    // 0xa52ac8: r0 = InkWell()
    //     0xa52ac8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa52acc: mov             x3, x0
    // 0xa52ad0: ldur            x0, [fp, #-0x20]
    // 0xa52ad4: stur            x3, [fp, #-8]
    // 0xa52ad8: StoreField: r3->field_b = r0
    //     0xa52ad8: stur            w0, [x3, #0xb]
    // 0xa52adc: ldur            x2, [fp, #-0x10]
    // 0xa52ae0: r1 = Function '<anonymous closure>':.
    //     0xa52ae0: add             x1, PP, #0x58, lsl #12  ; [pp+0x58758] AnonymousClosure: (0xa52b78), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xa52ae4: ldr             x1, [x1, #0x758]
    // 0xa52ae8: r0 = AllocateClosure()
    //     0xa52ae8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa52aec: mov             x1, x0
    // 0xa52af0: ldur            x0, [fp, #-8]
    // 0xa52af4: StoreField: r0->field_f = r1
    //     0xa52af4: stur            w1, [x0, #0xf]
    // 0xa52af8: r1 = true
    //     0xa52af8: add             x1, NULL, #0x20  ; true
    // 0xa52afc: StoreField: r0->field_43 = r1
    //     0xa52afc: stur            w1, [x0, #0x43]
    // 0xa52b00: r2 = Instance_BoxShape
    //     0xa52b00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa52b04: ldr             x2, [x2, #0x80]
    // 0xa52b08: StoreField: r0->field_47 = r2
    //     0xa52b08: stur            w2, [x0, #0x47]
    // 0xa52b0c: StoreField: r0->field_6f = r1
    //     0xa52b0c: stur            w1, [x0, #0x6f]
    // 0xa52b10: r2 = false
    //     0xa52b10: add             x2, NULL, #0x30  ; false
    // 0xa52b14: StoreField: r0->field_73 = r2
    //     0xa52b14: stur            w2, [x0, #0x73]
    // 0xa52b18: StoreField: r0->field_83 = r1
    //     0xa52b18: stur            w1, [x0, #0x83]
    // 0xa52b1c: StoreField: r0->field_7b = r2
    //     0xa52b1c: stur            w2, [x0, #0x7b]
    // 0xa52b20: LeaveFrame
    //     0xa52b20: mov             SP, fp
    //     0xa52b24: ldp             fp, lr, [SP], #0x10
    // 0xa52b28: ret
    //     0xa52b28: ret             
    // 0xa52b2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa52b2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa52b30: b               #0xa514e4
    // 0xa52b34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa52b34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa52b38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa52b38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa52b3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa52b3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa52b40: r9 = _pageController
    //     0xa52b40: add             x9, PP, #0x58, lsl #12  ; [pp+0x58760] Field <_ProductGridItemViewState@**********._pageController@**********>: late (offset: 0x1c)
    //     0xa52b44: ldr             x9, [x9, #0x760]
    // 0xa52b48: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa52b48: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa52b4c: SaveReg d0
    //     0xa52b4c: str             q0, [SP, #-0x10]!
    // 0xa52b50: SaveReg r0
    //     0xa52b50: str             x0, [SP, #-8]!
    // 0xa52b54: r0 = 74
    //     0xa52b54: movz            x0, #0x4a
    // 0xa52b58: r30 = DoubleToIntegerStub
    //     0xa52b58: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xa52b5c: LoadField: r30 = r30->field_7
    //     0xa52b5c: ldur            lr, [lr, #7]
    // 0xa52b60: blr             lr
    // 0xa52b64: mov             x1, x0
    // 0xa52b68: RestoreReg r0
    //     0xa52b68: ldr             x0, [SP], #8
    // 0xa52b6c: RestoreReg d0
    //     0xa52b6c: ldr             q0, [SP], #0x10
    // 0xa52b70: b               #0xa51d00
    // 0xa52b74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa52b74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa52b78, size: 0x168
    // 0xa52b78: EnterFrame
    //     0xa52b78: stp             fp, lr, [SP, #-0x10]!
    //     0xa52b7c: mov             fp, SP
    // 0xa52b80: AllocStack(0x48)
    //     0xa52b80: sub             SP, SP, #0x48
    // 0xa52b84: SetupParameters()
    //     0xa52b84: ldr             x0, [fp, #0x10]
    //     0xa52b88: ldur            w1, [x0, #0x17]
    //     0xa52b8c: add             x1, x1, HEAP, lsl #32
    // 0xa52b90: CheckStackOverflow
    //     0xa52b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa52b94: cmp             SP, x16
    //     0xa52b98: b.ls            #0xa52cd4
    // 0xa52b9c: LoadField: r0 = r1->field_b
    //     0xa52b9c: ldur            w0, [x1, #0xb]
    // 0xa52ba0: DecompressPointer r0
    //     0xa52ba0: add             x0, x0, HEAP, lsl #32
    // 0xa52ba4: LoadField: r2 = r0->field_f
    //     0xa52ba4: ldur            w2, [x0, #0xf]
    // 0xa52ba8: DecompressPointer r2
    //     0xa52ba8: add             x2, x2, HEAP, lsl #32
    // 0xa52bac: LoadField: r0 = r2->field_b
    //     0xa52bac: ldur            w0, [x2, #0xb]
    // 0xa52bb0: DecompressPointer r0
    //     0xa52bb0: add             x0, x0, HEAP, lsl #32
    // 0xa52bb4: cmp             w0, NULL
    // 0xa52bb8: b.eq            #0xa52cdc
    // 0xa52bbc: LoadField: r2 = r0->field_2f
    //     0xa52bbc: ldur            w2, [x0, #0x2f]
    // 0xa52bc0: DecompressPointer r2
    //     0xa52bc0: add             x2, x2, HEAP, lsl #32
    // 0xa52bc4: cmp             w2, NULL
    // 0xa52bc8: b.ne            #0xa52bd0
    // 0xa52bcc: r2 = ""
    //     0xa52bcc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52bd0: LoadField: r3 = r0->field_2b
    //     0xa52bd0: ldur            w3, [x0, #0x2b]
    // 0xa52bd4: DecompressPointer r3
    //     0xa52bd4: add             x3, x3, HEAP, lsl #32
    // 0xa52bd8: cmp             w3, NULL
    // 0xa52bdc: b.ne            #0xa52be4
    // 0xa52be0: r3 = ""
    //     0xa52be0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52be4: LoadField: r4 = r0->field_37
    //     0xa52be4: ldur            w4, [x0, #0x37]
    // 0xa52be8: DecompressPointer r4
    //     0xa52be8: add             x4, x4, HEAP, lsl #32
    // 0xa52bec: cmp             w4, NULL
    // 0xa52bf0: b.ne            #0xa52bf8
    // 0xa52bf4: r4 = ""
    //     0xa52bf4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52bf8: LoadField: r5 = r0->field_3b
    //     0xa52bf8: ldur            w5, [x0, #0x3b]
    // 0xa52bfc: DecompressPointer r5
    //     0xa52bfc: add             x5, x5, HEAP, lsl #32
    // 0xa52c00: LoadField: r6 = r0->field_33
    //     0xa52c00: ldur            w6, [x0, #0x33]
    // 0xa52c04: DecompressPointer r6
    //     0xa52c04: add             x6, x6, HEAP, lsl #32
    // 0xa52c08: cmp             w6, NULL
    // 0xa52c0c: b.ne            #0xa52c14
    // 0xa52c10: r6 = ""
    //     0xa52c10: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52c14: LoadField: r7 = r0->field_f
    //     0xa52c14: ldur            w7, [x0, #0xf]
    // 0xa52c18: DecompressPointer r7
    //     0xa52c18: add             x7, x7, HEAP, lsl #32
    // 0xa52c1c: cmp             w7, NULL
    // 0xa52c20: b.ne            #0xa52c28
    // 0xa52c24: r7 = ""
    //     0xa52c24: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52c28: LoadField: r8 = r1->field_f
    //     0xa52c28: ldur            w8, [x1, #0xf]
    // 0xa52c2c: DecompressPointer r8
    //     0xa52c2c: add             x8, x8, HEAP, lsl #32
    // 0xa52c30: cmp             w8, NULL
    // 0xa52c34: b.ne            #0xa52c40
    // 0xa52c38: r1 = Null
    //     0xa52c38: mov             x1, NULL
    // 0xa52c3c: b               #0xa52c48
    // 0xa52c40: LoadField: r1 = r8->field_2b
    //     0xa52c40: ldur            w1, [x8, #0x2b]
    // 0xa52c44: DecompressPointer r1
    //     0xa52c44: add             x1, x1, HEAP, lsl #32
    // 0xa52c48: cmp             w1, NULL
    // 0xa52c4c: b.ne            #0xa52c54
    // 0xa52c50: r1 = ""
    //     0xa52c50: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52c54: cmp             w8, NULL
    // 0xa52c58: b.ne            #0xa52c64
    // 0xa52c5c: r8 = Null
    //     0xa52c5c: mov             x8, NULL
    // 0xa52c60: b               #0xa52c84
    // 0xa52c64: LoadField: r9 = r8->field_3b
    //     0xa52c64: ldur            w9, [x8, #0x3b]
    // 0xa52c68: DecompressPointer r9
    //     0xa52c68: add             x9, x9, HEAP, lsl #32
    // 0xa52c6c: cmp             w9, NULL
    // 0xa52c70: b.ne            #0xa52c7c
    // 0xa52c74: r8 = Null
    //     0xa52c74: mov             x8, NULL
    // 0xa52c78: b               #0xa52c84
    // 0xa52c7c: LoadField: r8 = r9->field_b
    //     0xa52c7c: ldur            w8, [x9, #0xb]
    // 0xa52c80: DecompressPointer r8
    //     0xa52c80: add             x8, x8, HEAP, lsl #32
    // 0xa52c84: cmp             w8, NULL
    // 0xa52c88: b.ne            #0xa52c90
    // 0xa52c8c: r8 = ""
    //     0xa52c8c: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52c90: LoadField: r9 = r0->field_53
    //     0xa52c90: ldur            w9, [x0, #0x53]
    // 0xa52c94: DecompressPointer r9
    //     0xa52c94: add             x9, x9, HEAP, lsl #32
    // 0xa52c98: stp             x2, x9, [SP, #0x38]
    // 0xa52c9c: stp             x4, x3, [SP, #0x28]
    // 0xa52ca0: stp             x6, x5, [SP, #0x18]
    // 0xa52ca4: stp             x1, x7, [SP, #8]
    // 0xa52ca8: str             x8, [SP]
    // 0xa52cac: r4 = 0
    //     0xa52cac: movz            x4, #0
    // 0xa52cb0: ldr             x0, [SP, #0x40]
    // 0xa52cb4: r16 = UnlinkedCall_0x613b5c
    //     0xa52cb4: add             x16, PP, #0x58, lsl #12  ; [pp+0x58768] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa52cb8: add             x16, x16, #0x768
    // 0xa52cbc: ldp             x5, lr, [x16]
    // 0xa52cc0: blr             lr
    // 0xa52cc4: r0 = Null
    //     0xa52cc4: mov             x0, NULL
    // 0xa52cc8: LeaveFrame
    //     0xa52cc8: mov             SP, fp
    //     0xa52ccc: ldp             fp, lr, [SP], #0x10
    // 0xa52cd0: ret
    //     0xa52cd0: ret             
    // 0xa52cd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa52cd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa52cd8: b               #0xa52b9c
    // 0xa52cdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa52cdc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0xa52ce0, size: 0x18c
    // 0xa52ce0: EnterFrame
    //     0xa52ce0: stp             fp, lr, [SP, #-0x10]!
    //     0xa52ce4: mov             fp, SP
    // 0xa52ce8: AllocStack(0x58)
    //     0xa52ce8: sub             SP, SP, #0x58
    // 0xa52cec: SetupParameters()
    //     0xa52cec: ldr             x0, [fp, #0x18]
    //     0xa52cf0: ldur            w2, [x0, #0x17]
    //     0xa52cf4: add             x2, x2, HEAP, lsl #32
    //     0xa52cf8: stur            x2, [fp, #-8]
    // 0xa52cfc: CheckStackOverflow
    //     0xa52cfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa52d00: cmp             SP, x16
    //     0xa52d04: b.ls            #0xa52e60
    // 0xa52d08: ldr             x1, [fp, #0x10]
    // 0xa52d0c: r0 = visibleFraction()
    //     0xa52d0c: bl              #0xa4fa10  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0xa52d10: mov             v1.16b, v0.16b
    // 0xa52d14: d0 = 0.500000
    //     0xa52d14: fmov            d0, #0.50000000
    // 0xa52d18: fcmp            d1, d0
    // 0xa52d1c: b.le            #0xa52e50
    // 0xa52d20: ldur            x0, [fp, #-8]
    // 0xa52d24: LoadField: r1 = r0->field_b
    //     0xa52d24: ldur            w1, [x0, #0xb]
    // 0xa52d28: DecompressPointer r1
    //     0xa52d28: add             x1, x1, HEAP, lsl #32
    // 0xa52d2c: LoadField: r2 = r1->field_f
    //     0xa52d2c: ldur            w2, [x1, #0xf]
    // 0xa52d30: DecompressPointer r2
    //     0xa52d30: add             x2, x2, HEAP, lsl #32
    // 0xa52d34: LoadField: r1 = r2->field_b
    //     0xa52d34: ldur            w1, [x2, #0xb]
    // 0xa52d38: DecompressPointer r1
    //     0xa52d38: add             x1, x1, HEAP, lsl #32
    // 0xa52d3c: stur            x1, [fp, #-0x28]
    // 0xa52d40: cmp             w1, NULL
    // 0xa52d44: b.eq            #0xa52e68
    // 0xa52d48: LoadField: r2 = r0->field_f
    //     0xa52d48: ldur            w2, [x0, #0xf]
    // 0xa52d4c: DecompressPointer r2
    //     0xa52d4c: add             x2, x2, HEAP, lsl #32
    // 0xa52d50: cmp             w2, NULL
    // 0xa52d54: b.ne            #0xa52d60
    // 0xa52d58: r0 = Null
    //     0xa52d58: mov             x0, NULL
    // 0xa52d5c: b               #0xa52d68
    // 0xa52d60: LoadField: r0 = r2->field_53
    //     0xa52d60: ldur            w0, [x2, #0x53]
    // 0xa52d64: DecompressPointer r0
    //     0xa52d64: add             x0, x0, HEAP, lsl #32
    // 0xa52d68: stur            x0, [fp, #-0x20]
    // 0xa52d6c: cmp             w2, NULL
    // 0xa52d70: b.ne            #0xa52d7c
    // 0xa52d74: r3 = Null
    //     0xa52d74: mov             x3, NULL
    // 0xa52d78: b               #0xa52da0
    // 0xa52d7c: LoadField: r3 = r2->field_3b
    //     0xa52d7c: ldur            w3, [x2, #0x3b]
    // 0xa52d80: DecompressPointer r3
    //     0xa52d80: add             x3, x3, HEAP, lsl #32
    // 0xa52d84: cmp             w3, NULL
    // 0xa52d88: b.ne            #0xa52d94
    // 0xa52d8c: r3 = Null
    //     0xa52d8c: mov             x3, NULL
    // 0xa52d90: b               #0xa52da0
    // 0xa52d94: LoadField: r4 = r3->field_b
    //     0xa52d94: ldur            w4, [x3, #0xb]
    // 0xa52d98: DecompressPointer r4
    //     0xa52d98: add             x4, x4, HEAP, lsl #32
    // 0xa52d9c: mov             x3, x4
    // 0xa52da0: stur            x3, [fp, #-0x18]
    // 0xa52da4: cmp             w2, NULL
    // 0xa52da8: b.ne            #0xa52db4
    // 0xa52dac: r4 = Null
    //     0xa52dac: mov             x4, NULL
    // 0xa52db0: b               #0xa52dbc
    // 0xa52db4: LoadField: r4 = r2->field_2b
    //     0xa52db4: ldur            w4, [x2, #0x2b]
    // 0xa52db8: DecompressPointer r4
    //     0xa52db8: add             x4, x4, HEAP, lsl #32
    // 0xa52dbc: stur            x4, [fp, #-0x10]
    // 0xa52dc0: cmp             w2, NULL
    // 0xa52dc4: b.ne            #0xa52dd0
    // 0xa52dc8: r5 = Null
    //     0xa52dc8: mov             x5, NULL
    // 0xa52dcc: b               #0xa52dd8
    // 0xa52dd0: LoadField: r5 = r2->field_57
    //     0xa52dd0: ldur            w5, [x2, #0x57]
    // 0xa52dd4: DecompressPointer r5
    //     0xa52dd4: add             x5, x5, HEAP, lsl #32
    // 0xa52dd8: stur            x5, [fp, #-8]
    // 0xa52ddc: cmp             w2, NULL
    // 0xa52de0: b.ne            #0xa52dec
    // 0xa52de4: r2 = Null
    //     0xa52de4: mov             x2, NULL
    // 0xa52de8: b               #0xa52df8
    // 0xa52dec: LoadField: r6 = r2->field_7b
    //     0xa52dec: ldur            w6, [x2, #0x7b]
    // 0xa52df0: DecompressPointer r6
    //     0xa52df0: add             x6, x6, HEAP, lsl #32
    // 0xa52df4: mov             x2, x6
    // 0xa52df8: cmp             w2, NULL
    // 0xa52dfc: b.ne            #0xa52e0c
    // 0xa52e00: r0 = ProductRating()
    //     0xa52e00: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0xa52e04: mov             x1, x0
    // 0xa52e08: b               #0xa52e10
    // 0xa52e0c: mov             x1, x2
    // 0xa52e10: ldur            x0, [fp, #-0x28]
    // 0xa52e14: LoadField: r2 = r0->field_4b
    //     0xa52e14: ldur            w2, [x0, #0x4b]
    // 0xa52e18: DecompressPointer r2
    //     0xa52e18: add             x2, x2, HEAP, lsl #32
    // 0xa52e1c: ldur            x16, [fp, #-0x20]
    // 0xa52e20: stp             x16, x2, [SP, #0x20]
    // 0xa52e24: ldur            x16, [fp, #-0x18]
    // 0xa52e28: ldur            lr, [fp, #-0x10]
    // 0xa52e2c: stp             lr, x16, [SP, #0x10]
    // 0xa52e30: ldur            x16, [fp, #-8]
    // 0xa52e34: stp             x1, x16, [SP]
    // 0xa52e38: r4 = 0
    //     0xa52e38: movz            x4, #0
    // 0xa52e3c: ldr             x0, [SP, #0x28]
    // 0xa52e40: r16 = UnlinkedCall_0x613b5c
    //     0xa52e40: add             x16, PP, #0x58, lsl #12  ; [pp+0x58778] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa52e44: add             x16, x16, #0x778
    // 0xa52e48: ldp             x5, lr, [x16]
    // 0xa52e4c: blr             lr
    // 0xa52e50: r0 = Null
    //     0xa52e50: mov             x0, NULL
    // 0xa52e54: LeaveFrame
    //     0xa52e54: mov             SP, fp
    //     0xa52e58: ldp             fp, lr, [SP], #0x10
    // 0xa52e5c: ret
    //     0xa52e5c: ret             
    // 0xa52e60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa52e60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa52e64: b               #0xa52d08
    // 0xa52e68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa52e68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa52e6c, size: 0x1e8
    // 0xa52e6c: EnterFrame
    //     0xa52e6c: stp             fp, lr, [SP, #-0x10]!
    //     0xa52e70: mov             fp, SP
    // 0xa52e74: AllocStack(0x30)
    //     0xa52e74: sub             SP, SP, #0x30
    // 0xa52e78: SetupParameters()
    //     0xa52e78: ldr             x0, [fp, #0x10]
    //     0xa52e7c: ldur            w1, [x0, #0x17]
    //     0xa52e80: add             x1, x1, HEAP, lsl #32
    // 0xa52e84: CheckStackOverflow
    //     0xa52e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa52e88: cmp             SP, x16
    //     0xa52e8c: b.ls            #0xa53044
    // 0xa52e90: LoadField: r0 = r1->field_f
    //     0xa52e90: ldur            w0, [x1, #0xf]
    // 0xa52e94: DecompressPointer r0
    //     0xa52e94: add             x0, x0, HEAP, lsl #32
    // 0xa52e98: stur            x0, [fp, #-0x10]
    // 0xa52e9c: cmp             w0, NULL
    // 0xa52ea0: b.ne            #0xa52eac
    // 0xa52ea4: r2 = Null
    //     0xa52ea4: mov             x2, NULL
    // 0xa52ea8: b               #0xa52eb4
    // 0xa52eac: LoadField: r2 = r0->field_4f
    //     0xa52eac: ldur            w2, [x0, #0x4f]
    // 0xa52eb0: DecompressPointer r2
    //     0xa52eb0: add             x2, x2, HEAP, lsl #32
    // 0xa52eb4: cmp             w2, NULL
    // 0xa52eb8: b.eq            #0xa52ec0
    // 0xa52ebc: tbz             w2, #4, #0xa53034
    // 0xa52ec0: LoadField: r2 = r1->field_b
    //     0xa52ec0: ldur            w2, [x1, #0xb]
    // 0xa52ec4: DecompressPointer r2
    //     0xa52ec4: add             x2, x2, HEAP, lsl #32
    // 0xa52ec8: stur            x2, [fp, #-8]
    // 0xa52ecc: LoadField: r1 = r2->field_f
    //     0xa52ecc: ldur            w1, [x2, #0xf]
    // 0xa52ed0: DecompressPointer r1
    //     0xa52ed0: add             x1, x1, HEAP, lsl #32
    // 0xa52ed4: LoadField: r3 = r1->field_b
    //     0xa52ed4: ldur            w3, [x1, #0xb]
    // 0xa52ed8: DecompressPointer r3
    //     0xa52ed8: add             x3, x3, HEAP, lsl #32
    // 0xa52edc: cmp             w3, NULL
    // 0xa52ee0: b.eq            #0xa5304c
    // 0xa52ee4: cmp             w0, NULL
    // 0xa52ee8: b.ne            #0xa52ef4
    // 0xa52eec: r1 = Null
    //     0xa52eec: mov             x1, NULL
    // 0xa52ef0: b               #0xa52efc
    // 0xa52ef4: LoadField: r1 = r0->field_2b
    //     0xa52ef4: ldur            w1, [x0, #0x2b]
    // 0xa52ef8: DecompressPointer r1
    //     0xa52ef8: add             x1, x1, HEAP, lsl #32
    // 0xa52efc: cmp             w0, NULL
    // 0xa52f00: b.ne            #0xa52f0c
    // 0xa52f04: r4 = Null
    //     0xa52f04: mov             x4, NULL
    // 0xa52f08: b               #0xa52f30
    // 0xa52f0c: LoadField: r4 = r0->field_3b
    //     0xa52f0c: ldur            w4, [x0, #0x3b]
    // 0xa52f10: DecompressPointer r4
    //     0xa52f10: add             x4, x4, HEAP, lsl #32
    // 0xa52f14: cmp             w4, NULL
    // 0xa52f18: b.ne            #0xa52f24
    // 0xa52f1c: r4 = Null
    //     0xa52f1c: mov             x4, NULL
    // 0xa52f20: b               #0xa52f30
    // 0xa52f24: LoadField: r5 = r4->field_7
    //     0xa52f24: ldur            w5, [x4, #7]
    // 0xa52f28: DecompressPointer r5
    //     0xa52f28: add             x5, x5, HEAP, lsl #32
    // 0xa52f2c: mov             x4, x5
    // 0xa52f30: cmp             w0, NULL
    // 0xa52f34: b.ne            #0xa52f40
    // 0xa52f38: r5 = Null
    //     0xa52f38: mov             x5, NULL
    // 0xa52f3c: b               #0xa52f64
    // 0xa52f40: LoadField: r5 = r0->field_3b
    //     0xa52f40: ldur            w5, [x0, #0x3b]
    // 0xa52f44: DecompressPointer r5
    //     0xa52f44: add             x5, x5, HEAP, lsl #32
    // 0xa52f48: cmp             w5, NULL
    // 0xa52f4c: b.ne            #0xa52f58
    // 0xa52f50: r5 = Null
    //     0xa52f50: mov             x5, NULL
    // 0xa52f54: b               #0xa52f64
    // 0xa52f58: LoadField: r6 = r5->field_b
    //     0xa52f58: ldur            w6, [x5, #0xb]
    // 0xa52f5c: DecompressPointer r6
    //     0xa52f5c: add             x6, x6, HEAP, lsl #32
    // 0xa52f60: mov             x5, x6
    // 0xa52f64: LoadField: r6 = r3->field_47
    //     0xa52f64: ldur            w6, [x3, #0x47]
    // 0xa52f68: DecompressPointer r6
    //     0xa52f68: add             x6, x6, HEAP, lsl #32
    // 0xa52f6c: stp             x1, x6, [SP, #0x10]
    // 0xa52f70: stp             x5, x4, [SP]
    // 0xa52f74: r4 = 0
    //     0xa52f74: movz            x4, #0
    // 0xa52f78: ldr             x0, [SP, #0x18]
    // 0xa52f7c: r16 = UnlinkedCall_0x613b5c
    //     0xa52f7c: add             x16, PP, #0x58, lsl #12  ; [pp+0x58788] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa52f80: add             x16, x16, #0x788
    // 0xa52f84: ldp             x5, lr, [x16]
    // 0xa52f88: blr             lr
    // 0xa52f8c: ldur            x0, [fp, #-8]
    // 0xa52f90: LoadField: r1 = r0->field_f
    //     0xa52f90: ldur            w1, [x0, #0xf]
    // 0xa52f94: DecompressPointer r1
    //     0xa52f94: add             x1, x1, HEAP, lsl #32
    // 0xa52f98: LoadField: r0 = r1->field_b
    //     0xa52f98: ldur            w0, [x1, #0xb]
    // 0xa52f9c: DecompressPointer r0
    //     0xa52f9c: add             x0, x0, HEAP, lsl #32
    // 0xa52fa0: stur            x0, [fp, #-8]
    // 0xa52fa4: cmp             w0, NULL
    // 0xa52fa8: b.eq            #0xa53050
    // 0xa52fac: ldur            x1, [fp, #-0x10]
    // 0xa52fb0: cmp             w1, NULL
    // 0xa52fb4: b.ne            #0xa52fc4
    // 0xa52fb8: r0 = Entity()
    //     0xa52fb8: bl              #0x9118a4  ; AllocateEntityStub -> Entity (size=0xc0)
    // 0xa52fbc: mov             x1, x0
    // 0xa52fc0: b               #0xa52fc8
    // 0xa52fc4: ldur            x1, [fp, #-0x10]
    // 0xa52fc8: ldur            x0, [fp, #-8]
    // 0xa52fcc: LoadField: r2 = r0->field_27
    //     0xa52fcc: ldur            w2, [x0, #0x27]
    // 0xa52fd0: DecompressPointer r2
    //     0xa52fd0: add             x2, x2, HEAP, lsl #32
    // 0xa52fd4: cmp             w2, NULL
    // 0xa52fd8: b.ne            #0xa52fe4
    // 0xa52fdc: r3 = ""
    //     0xa52fdc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa52fe0: b               #0xa52fe8
    // 0xa52fe4: mov             x3, x2
    // 0xa52fe8: ldur            x2, [fp, #-0x10]
    // 0xa52fec: cmp             w2, NULL
    // 0xa52ff0: b.ne            #0xa52ffc
    // 0xa52ff4: r2 = Null
    //     0xa52ff4: mov             x2, NULL
    // 0xa52ff8: b               #0xa53008
    // 0xa52ffc: LoadField: r4 = r2->field_b3
    //     0xa52ffc: ldur            w4, [x2, #0xb3]
    // 0xa53000: DecompressPointer r4
    //     0xa53000: add             x4, x4, HEAP, lsl #32
    // 0xa53004: mov             x2, x4
    // 0xa53008: cmp             w2, NULL
    // 0xa5300c: b.ne            #0xa53014
    // 0xa53010: r2 = ""
    //     0xa53010: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa53014: LoadField: r4 = r0->field_57
    //     0xa53014: ldur            w4, [x0, #0x57]
    // 0xa53018: DecompressPointer r4
    //     0xa53018: add             x4, x4, HEAP, lsl #32
    // 0xa5301c: stp             x1, x4, [SP, #0x10]
    // 0xa53020: stp             x2, x3, [SP]
    // 0xa53024: mov             x0, x4
    // 0xa53028: ClosureCall
    //     0xa53028: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xa5302c: ldur            x2, [x0, #0x1f]
    //     0xa53030: blr             x2
    // 0xa53034: r0 = Null
    //     0xa53034: mov             x0, NULL
    // 0xa53038: LeaveFrame
    //     0xa53038: mov             SP, fp
    //     0xa5303c: ldp             fp, lr, [SP], #0x10
    // 0xa53040: ret
    //     0xa53040: ret             
    // 0xa53044: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa53044: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa53048: b               #0xa52e90
    // 0xa5304c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5304c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa53050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa53050: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa53054, size: 0x1f0
    // 0xa53054: EnterFrame
    //     0xa53054: stp             fp, lr, [SP, #-0x10]!
    //     0xa53058: mov             fp, SP
    // 0xa5305c: AllocStack(0x20)
    //     0xa5305c: sub             SP, SP, #0x20
    // 0xa53060: SetupParameters()
    //     0xa53060: ldr             x0, [fp, #0x20]
    //     0xa53064: ldur            w1, [x0, #0x17]
    //     0xa53068: add             x1, x1, HEAP, lsl #32
    // 0xa5306c: CheckStackOverflow
    //     0xa5306c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa53070: cmp             SP, x16
    //     0xa53074: b.ls            #0xa53234
    // 0xa53078: LoadField: r0 = r1->field_b
    //     0xa53078: ldur            w0, [x1, #0xb]
    // 0xa5307c: DecompressPointer r0
    //     0xa5307c: add             x0, x0, HEAP, lsl #32
    // 0xa53080: LoadField: r2 = r0->field_f
    //     0xa53080: ldur            w2, [x0, #0xf]
    // 0xa53084: DecompressPointer r2
    //     0xa53084: add             x2, x2, HEAP, lsl #32
    // 0xa53088: LoadField: r4 = r1->field_f
    //     0xa53088: ldur            w4, [x1, #0xf]
    // 0xa5308c: DecompressPointer r4
    //     0xa5308c: add             x4, x4, HEAP, lsl #32
    // 0xa53090: stur            x4, [fp, #-8]
    // 0xa53094: cmp             w4, NULL
    // 0xa53098: b.ne            #0xa530a4
    // 0xa5309c: r0 = Null
    //     0xa5309c: mov             x0, NULL
    // 0xa530a0: b               #0xa530f0
    // 0xa530a4: ldr             x0, [fp, #0x10]
    // 0xa530a8: LoadField: r3 = r4->field_37
    //     0xa530a8: ldur            w3, [x4, #0x37]
    // 0xa530ac: DecompressPointer r3
    //     0xa530ac: add             x3, x3, HEAP, lsl #32
    // 0xa530b0: cmp             w3, NULL
    // 0xa530b4: b.eq            #0xa5323c
    // 0xa530b8: LoadField: r1 = r3->field_b
    //     0xa530b8: ldur            w1, [x3, #0xb]
    // 0xa530bc: r5 = LoadInt32Instr(r0)
    //     0xa530bc: sbfx            x5, x0, #1, #0x1f
    //     0xa530c0: tbz             w0, #0, #0xa530c8
    //     0xa530c4: ldur            x5, [x0, #7]
    // 0xa530c8: r0 = LoadInt32Instr(r1)
    //     0xa530c8: sbfx            x0, x1, #1, #0x1f
    // 0xa530cc: mov             x1, x5
    // 0xa530d0: cmp             x1, x0
    // 0xa530d4: b.hs            #0xa53240
    // 0xa530d8: LoadField: r0 = r3->field_f
    //     0xa530d8: ldur            w0, [x3, #0xf]
    // 0xa530dc: DecompressPointer r0
    //     0xa530dc: add             x0, x0, HEAP, lsl #32
    // 0xa530e0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa530e0: add             x16, x0, x5, lsl #2
    //     0xa530e4: ldur            w1, [x16, #0xf]
    // 0xa530e8: DecompressPointer r1
    //     0xa530e8: add             x1, x1, HEAP, lsl #32
    // 0xa530ec: mov             x0, x1
    // 0xa530f0: mov             x1, x2
    // 0xa530f4: mov             x2, x0
    // 0xa530f8: mov             x3, x4
    // 0xa530fc: r0 = cosmeticThemeSlider()
    //     0xa530fc: bl              #0xa53244  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::cosmeticThemeSlider
    // 0xa53100: mov             x1, x0
    // 0xa53104: ldur            x0, [fp, #-8]
    // 0xa53108: stur            x1, [fp, #-0x10]
    // 0xa5310c: cmp             w0, NULL
    // 0xa53110: b.ne            #0xa5311c
    // 0xa53114: r0 = Null
    //     0xa53114: mov             x0, NULL
    // 0xa53118: b               #0xa53128
    // 0xa5311c: LoadField: r2 = r0->field_bb
    //     0xa5311c: ldur            w2, [x0, #0xbb]
    // 0xa53120: DecompressPointer r2
    //     0xa53120: add             x2, x2, HEAP, lsl #32
    // 0xa53124: mov             x0, x2
    // 0xa53128: cmp             w0, NULL
    // 0xa5312c: b.ne            #0xa53134
    // 0xa53130: r0 = false
    //     0xa53130: add             x0, NULL, #0x30  ; false
    // 0xa53134: stur            x0, [fp, #-8]
    // 0xa53138: r0 = SvgPicture()
    //     0xa53138: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa5313c: mov             x1, x0
    // 0xa53140: r2 = "assets/images/free-gift-icon.svg"
    //     0xa53140: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xa53144: ldr             x2, [x2, #0xd40]
    // 0xa53148: stur            x0, [fp, #-0x18]
    // 0xa5314c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa5314c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa53150: r0 = SvgPicture.asset()
    //     0xa53150: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa53154: r0 = Padding()
    //     0xa53154: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa53158: mov             x1, x0
    // 0xa5315c: r0 = Instance_EdgeInsets
    //     0xa5315c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xa53160: ldr             x0, [x0, #0xd48]
    // 0xa53164: stur            x1, [fp, #-0x20]
    // 0xa53168: StoreField: r1->field_f = r0
    //     0xa53168: stur            w0, [x1, #0xf]
    // 0xa5316c: ldur            x0, [fp, #-0x18]
    // 0xa53170: StoreField: r1->field_b = r0
    //     0xa53170: stur            w0, [x1, #0xb]
    // 0xa53174: r0 = Visibility()
    //     0xa53174: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa53178: mov             x3, x0
    // 0xa5317c: ldur            x0, [fp, #-0x20]
    // 0xa53180: stur            x3, [fp, #-0x18]
    // 0xa53184: StoreField: r3->field_b = r0
    //     0xa53184: stur            w0, [x3, #0xb]
    // 0xa53188: r0 = Instance_SizedBox
    //     0xa53188: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa5318c: StoreField: r3->field_f = r0
    //     0xa5318c: stur            w0, [x3, #0xf]
    // 0xa53190: ldur            x0, [fp, #-8]
    // 0xa53194: StoreField: r3->field_13 = r0
    //     0xa53194: stur            w0, [x3, #0x13]
    // 0xa53198: r0 = false
    //     0xa53198: add             x0, NULL, #0x30  ; false
    // 0xa5319c: ArrayStore: r3[0] = r0  ; List_4
    //     0xa5319c: stur            w0, [x3, #0x17]
    // 0xa531a0: StoreField: r3->field_1b = r0
    //     0xa531a0: stur            w0, [x3, #0x1b]
    // 0xa531a4: StoreField: r3->field_1f = r0
    //     0xa531a4: stur            w0, [x3, #0x1f]
    // 0xa531a8: StoreField: r3->field_23 = r0
    //     0xa531a8: stur            w0, [x3, #0x23]
    // 0xa531ac: StoreField: r3->field_27 = r0
    //     0xa531ac: stur            w0, [x3, #0x27]
    // 0xa531b0: StoreField: r3->field_2b = r0
    //     0xa531b0: stur            w0, [x3, #0x2b]
    // 0xa531b4: r1 = Null
    //     0xa531b4: mov             x1, NULL
    // 0xa531b8: r2 = 4
    //     0xa531b8: movz            x2, #0x4
    // 0xa531bc: r0 = AllocateArray()
    //     0xa531bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa531c0: mov             x2, x0
    // 0xa531c4: ldur            x0, [fp, #-0x10]
    // 0xa531c8: stur            x2, [fp, #-8]
    // 0xa531cc: StoreField: r2->field_f = r0
    //     0xa531cc: stur            w0, [x2, #0xf]
    // 0xa531d0: ldur            x0, [fp, #-0x18]
    // 0xa531d4: StoreField: r2->field_13 = r0
    //     0xa531d4: stur            w0, [x2, #0x13]
    // 0xa531d8: r1 = <Widget>
    //     0xa531d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa531dc: r0 = AllocateGrowableArray()
    //     0xa531dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa531e0: mov             x1, x0
    // 0xa531e4: ldur            x0, [fp, #-8]
    // 0xa531e8: stur            x1, [fp, #-0x10]
    // 0xa531ec: StoreField: r1->field_f = r0
    //     0xa531ec: stur            w0, [x1, #0xf]
    // 0xa531f0: r0 = 4
    //     0xa531f0: movz            x0, #0x4
    // 0xa531f4: StoreField: r1->field_b = r0
    //     0xa531f4: stur            w0, [x1, #0xb]
    // 0xa531f8: r0 = Stack()
    //     0xa531f8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa531fc: r1 = Instance_Alignment
    //     0xa531fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xa53200: ldr             x1, [x1, #0x950]
    // 0xa53204: StoreField: r0->field_f = r1
    //     0xa53204: stur            w1, [x0, #0xf]
    // 0xa53208: r1 = Instance_StackFit
    //     0xa53208: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa5320c: ldr             x1, [x1, #0xfa8]
    // 0xa53210: ArrayStore: r0[0] = r1  ; List_4
    //     0xa53210: stur            w1, [x0, #0x17]
    // 0xa53214: r1 = Instance_Clip
    //     0xa53214: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa53218: ldr             x1, [x1, #0x7e0]
    // 0xa5321c: StoreField: r0->field_1b = r1
    //     0xa5321c: stur            w1, [x0, #0x1b]
    // 0xa53220: ldur            x1, [fp, #-0x10]
    // 0xa53224: StoreField: r0->field_b = r1
    //     0xa53224: stur            w1, [x0, #0xb]
    // 0xa53228: LeaveFrame
    //     0xa53228: mov             SP, fp
    //     0xa5322c: ldp             fp, lr, [SP], #0x10
    // 0xa53230: ret
    //     0xa53230: ret             
    // 0xa53234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa53234: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa53238: b               #0xa53078
    // 0xa5323c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa5323c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa53240: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa53240: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ cosmeticThemeSlider(/* No info */) {
    // ** addr: 0xa53244, size: 0x1198
    // 0xa53244: EnterFrame
    //     0xa53244: stp             fp, lr, [SP, #-0x10]!
    //     0xa53248: mov             fp, SP
    // 0xa5324c: AllocStack(0x90)
    //     0xa5324c: sub             SP, SP, #0x90
    // 0xa53250: SetupParameters(_ProductGridItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa53250: stur            x1, [fp, #-8]
    //     0xa53254: stur            x2, [fp, #-0x10]
    //     0xa53258: stur            x3, [fp, #-0x18]
    // 0xa5325c: CheckStackOverflow
    //     0xa5325c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa53260: cmp             SP, x16
    //     0xa53264: b.ls            #0xa54394
    // 0xa53268: r1 = 2
    //     0xa53268: movz            x1, #0x2
    // 0xa5326c: r0 = AllocateContext()
    //     0xa5326c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa53270: mov             x1, x0
    // 0xa53274: ldur            x0, [fp, #-8]
    // 0xa53278: stur            x1, [fp, #-0x20]
    // 0xa5327c: StoreField: r1->field_f = r0
    //     0xa5327c: stur            w0, [x1, #0xf]
    // 0xa53280: ldur            x2, [fp, #-0x18]
    // 0xa53284: StoreField: r1->field_13 = r2
    //     0xa53284: stur            w2, [x1, #0x13]
    // 0xa53288: r0 = Radius()
    //     0xa53288: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa5328c: d0 = 10.000000
    //     0xa5328c: fmov            d0, #10.00000000
    // 0xa53290: stur            x0, [fp, #-0x18]
    // 0xa53294: StoreField: r0->field_7 = d0
    //     0xa53294: stur            d0, [x0, #7]
    // 0xa53298: StoreField: r0->field_f = d0
    //     0xa53298: stur            d0, [x0, #0xf]
    // 0xa5329c: r0 = BorderRadius()
    //     0xa5329c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa532a0: mov             x1, x0
    // 0xa532a4: ldur            x0, [fp, #-0x18]
    // 0xa532a8: stur            x1, [fp, #-0x28]
    // 0xa532ac: StoreField: r1->field_7 = r0
    //     0xa532ac: stur            w0, [x1, #7]
    // 0xa532b0: StoreField: r1->field_b = r0
    //     0xa532b0: stur            w0, [x1, #0xb]
    // 0xa532b4: StoreField: r1->field_f = r0
    //     0xa532b4: stur            w0, [x1, #0xf]
    // 0xa532b8: StoreField: r1->field_13 = r0
    //     0xa532b8: stur            w0, [x1, #0x13]
    // 0xa532bc: r0 = RoundedRectangleBorder()
    //     0xa532bc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa532c0: mov             x3, x0
    // 0xa532c4: ldur            x0, [fp, #-0x28]
    // 0xa532c8: stur            x3, [fp, #-0x30]
    // 0xa532cc: StoreField: r3->field_b = r0
    //     0xa532cc: stur            w0, [x3, #0xb]
    // 0xa532d0: r0 = Instance_BorderSide
    //     0xa532d0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa532d4: ldr             x0, [x0, #0xe20]
    // 0xa532d8: StoreField: r3->field_7 = r0
    //     0xa532d8: stur            w0, [x3, #7]
    // 0xa532dc: ldur            x1, [fp, #-0x10]
    // 0xa532e0: cmp             w1, NULL
    // 0xa532e4: b.eq            #0xa5439c
    // 0xa532e8: LoadField: r4 = r1->field_b
    //     0xa532e8: ldur            w4, [x1, #0xb]
    // 0xa532ec: DecompressPointer r4
    //     0xa532ec: add             x4, x4, HEAP, lsl #32
    // 0xa532f0: stur            x4, [fp, #-0x18]
    // 0xa532f4: cmp             w4, NULL
    // 0xa532f8: b.eq            #0xa543a0
    // 0xa532fc: r1 = Function '<anonymous closure>':.
    //     0xa532fc: add             x1, PP, #0x58, lsl #12  ; [pp+0x58798] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa53300: ldr             x1, [x1, #0x798]
    // 0xa53304: r2 = Null
    //     0xa53304: mov             x2, NULL
    // 0xa53308: r0 = AllocateClosure()
    //     0xa53308: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa5330c: r1 = Function '<anonymous closure>':.
    //     0xa5330c: add             x1, PP, #0x58, lsl #12  ; [pp+0x587a0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa53310: ldr             x1, [x1, #0x7a0]
    // 0xa53314: r2 = Null
    //     0xa53314: mov             x2, NULL
    // 0xa53318: stur            x0, [fp, #-0x10]
    // 0xa5331c: r0 = AllocateClosure()
    //     0xa5331c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa53320: stur            x0, [fp, #-0x28]
    // 0xa53324: r0 = CachedNetworkImage()
    //     0xa53324: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa53328: stur            x0, [fp, #-0x38]
    // 0xa5332c: r16 = Instance_BoxFit
    //     0xa5332c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xa53330: ldr             x16, [x16, #0xb18]
    // 0xa53334: ldur            lr, [fp, #-0x10]
    // 0xa53338: stp             lr, x16, [SP, #8]
    // 0xa5333c: ldur            x16, [fp, #-0x28]
    // 0xa53340: str             x16, [SP]
    // 0xa53344: mov             x1, x0
    // 0xa53348: ldur            x2, [fp, #-0x18]
    // 0xa5334c: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xa5334c: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xa53350: ldr             x4, [x4, #0x638]
    // 0xa53354: r0 = CachedNetworkImage()
    //     0xa53354: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa53358: r0 = Card()
    //     0xa53358: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xa5335c: mov             x1, x0
    // 0xa53360: r0 = Instance_Color
    //     0xa53360: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xa53364: ldr             x0, [x0, #0xf88]
    // 0xa53368: stur            x1, [fp, #-0x10]
    // 0xa5336c: StoreField: r1->field_b = r0
    //     0xa5336c: stur            w0, [x1, #0xb]
    // 0xa53370: r0 = 0.000000
    //     0xa53370: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xa53374: ArrayStore: r1[0] = r0  ; List_4
    //     0xa53374: stur            w0, [x1, #0x17]
    // 0xa53378: ldur            x0, [fp, #-0x30]
    // 0xa5337c: StoreField: r1->field_1b = r0
    //     0xa5337c: stur            w0, [x1, #0x1b]
    // 0xa53380: r0 = true
    //     0xa53380: add             x0, NULL, #0x20  ; true
    // 0xa53384: StoreField: r1->field_1f = r0
    //     0xa53384: stur            w0, [x1, #0x1f]
    // 0xa53388: r2 = Instance_EdgeInsets
    //     0xa53388: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xa5338c: StoreField: r1->field_27 = r2
    //     0xa5338c: stur            w2, [x1, #0x27]
    // 0xa53390: r2 = Instance_Clip
    //     0xa53390: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xa53394: ldr             x2, [x2, #0xb50]
    // 0xa53398: StoreField: r1->field_23 = r2
    //     0xa53398: stur            w2, [x1, #0x23]
    // 0xa5339c: ldur            x2, [fp, #-0x38]
    // 0xa533a0: StoreField: r1->field_2f = r2
    //     0xa533a0: stur            w2, [x1, #0x2f]
    // 0xa533a4: StoreField: r1->field_2b = r0
    //     0xa533a4: stur            w0, [x1, #0x2b]
    // 0xa533a8: r2 = Instance__CardVariant
    //     0xa533a8: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xa533ac: ldr             x2, [x2, #0xa68]
    // 0xa533b0: StoreField: r1->field_33 = r2
    //     0xa533b0: stur            w2, [x1, #0x33]
    // 0xa533b4: r0 = Center()
    //     0xa533b4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa533b8: mov             x1, x0
    // 0xa533bc: r0 = Instance_Alignment
    //     0xa533bc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa533c0: ldr             x0, [x0, #0xb10]
    // 0xa533c4: stur            x1, [fp, #-0x28]
    // 0xa533c8: StoreField: r1->field_f = r0
    //     0xa533c8: stur            w0, [x1, #0xf]
    // 0xa533cc: ldur            x0, [fp, #-0x10]
    // 0xa533d0: StoreField: r1->field_b = r0
    //     0xa533d0: stur            w0, [x1, #0xb]
    // 0xa533d4: ldur            x2, [fp, #-0x20]
    // 0xa533d8: LoadField: r0 = r2->field_13
    //     0xa533d8: ldur            w0, [x2, #0x13]
    // 0xa533dc: DecompressPointer r0
    //     0xa533dc: add             x0, x0, HEAP, lsl #32
    // 0xa533e0: stur            x0, [fp, #-0x18]
    // 0xa533e4: cmp             w0, NULL
    // 0xa533e8: b.ne            #0xa533f4
    // 0xa533ec: r3 = Null
    //     0xa533ec: mov             x3, NULL
    // 0xa533f0: b               #0xa53420
    // 0xa533f4: LoadField: r3 = r0->field_7f
    //     0xa533f4: ldur            w3, [x0, #0x7f]
    // 0xa533f8: DecompressPointer r3
    //     0xa533f8: add             x3, x3, HEAP, lsl #32
    // 0xa533fc: cmp             w3, NULL
    // 0xa53400: b.ne            #0xa5340c
    // 0xa53404: r3 = Null
    //     0xa53404: mov             x3, NULL
    // 0xa53408: b               #0xa53420
    // 0xa5340c: LoadField: r4 = r3->field_7
    //     0xa5340c: ldur            w4, [x3, #7]
    // 0xa53410: cbnz            w4, #0xa5341c
    // 0xa53414: r3 = false
    //     0xa53414: add             x3, NULL, #0x30  ; false
    // 0xa53418: b               #0xa53420
    // 0xa5341c: r3 = true
    //     0xa5341c: add             x3, NULL, #0x20  ; true
    // 0xa53420: cmp             w3, NULL
    // 0xa53424: b.ne            #0xa5342c
    // 0xa53428: r3 = false
    //     0xa53428: add             x3, NULL, #0x30  ; false
    // 0xa5342c: stur            x3, [fp, #-0x10]
    // 0xa53430: cmp             w0, NULL
    // 0xa53434: b.ne            #0xa53440
    // 0xa53438: r4 = Null
    //     0xa53438: mov             x4, NULL
    // 0xa5343c: b               #0xa53448
    // 0xa53440: LoadField: r4 = r0->field_93
    //     0xa53440: ldur            w4, [x0, #0x93]
    // 0xa53444: DecompressPointer r4
    //     0xa53444: add             x4, x4, HEAP, lsl #32
    // 0xa53448: cmp             w4, NULL
    // 0xa5344c: b.ne            #0xa5346c
    // 0xa53450: mov             x4, x2
    // 0xa53454: r0 = Instance_Alignment
    //     0xa53454: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa53458: ldr             x0, [x0, #0xfa0]
    // 0xa5345c: r2 = 4
    //     0xa5345c: movz            x2, #0x4
    // 0xa53460: d0 = 0.700000
    //     0xa53460: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa53464: ldr             d0, [x17, #0xf48]
    // 0xa53468: b               #0xa539cc
    // 0xa5346c: tbnz            w4, #4, #0xa539b4
    // 0xa53470: ldur            x4, [fp, #-8]
    // 0xa53474: r0 = Radius()
    //     0xa53474: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa53478: d0 = 8.000000
    //     0xa53478: fmov            d0, #8.00000000
    // 0xa5347c: stur            x0, [fp, #-0x30]
    // 0xa53480: StoreField: r0->field_7 = d0
    //     0xa53480: stur            d0, [x0, #7]
    // 0xa53484: StoreField: r0->field_f = d0
    //     0xa53484: stur            d0, [x0, #0xf]
    // 0xa53488: r0 = BorderRadius()
    //     0xa53488: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa5348c: mov             x1, x0
    // 0xa53490: ldur            x0, [fp, #-0x30]
    // 0xa53494: stur            x1, [fp, #-0x38]
    // 0xa53498: StoreField: r1->field_7 = r0
    //     0xa53498: stur            w0, [x1, #7]
    // 0xa5349c: StoreField: r1->field_b = r0
    //     0xa5349c: stur            w0, [x1, #0xb]
    // 0xa534a0: StoreField: r1->field_f = r0
    //     0xa534a0: stur            w0, [x1, #0xf]
    // 0xa534a4: StoreField: r1->field_13 = r0
    //     0xa534a4: stur            w0, [x1, #0x13]
    // 0xa534a8: ldur            x0, [fp, #-8]
    // 0xa534ac: LoadField: r2 = r0->field_b
    //     0xa534ac: ldur            w2, [x0, #0xb]
    // 0xa534b0: DecompressPointer r2
    //     0xa534b0: add             x2, x2, HEAP, lsl #32
    // 0xa534b4: cmp             w2, NULL
    // 0xa534b8: b.eq            #0xa543a4
    // 0xa534bc: LoadField: r3 = r2->field_43
    //     0xa534bc: ldur            w3, [x2, #0x43]
    // 0xa534c0: DecompressPointer r3
    //     0xa534c0: add             x3, x3, HEAP, lsl #32
    // 0xa534c4: stur            x3, [fp, #-0x30]
    // 0xa534c8: cmp             w3, NULL
    // 0xa534cc: b.ne            #0xa534d8
    // 0xa534d0: r2 = Null
    //     0xa534d0: mov             x2, NULL
    // 0xa534d4: b               #0xa534fc
    // 0xa534d8: LoadField: r2 = r3->field_13
    //     0xa534d8: ldur            w2, [x3, #0x13]
    // 0xa534dc: DecompressPointer r2
    //     0xa534dc: add             x2, x2, HEAP, lsl #32
    // 0xa534e0: cmp             w2, NULL
    // 0xa534e4: b.ne            #0xa534f0
    // 0xa534e8: r2 = Null
    //     0xa534e8: mov             x2, NULL
    // 0xa534ec: b               #0xa534fc
    // 0xa534f0: LoadField: r4 = r2->field_7
    //     0xa534f0: ldur            w4, [x2, #7]
    // 0xa534f4: DecompressPointer r4
    //     0xa534f4: add             x4, x4, HEAP, lsl #32
    // 0xa534f8: mov             x2, x4
    // 0xa534fc: cmp             w2, NULL
    // 0xa53500: b.ne            #0xa5350c
    // 0xa53504: r2 = 0
    //     0xa53504: movz            x2, #0
    // 0xa53508: b               #0xa5351c
    // 0xa5350c: r4 = LoadInt32Instr(r2)
    //     0xa5350c: sbfx            x4, x2, #1, #0x1f
    //     0xa53510: tbz             w2, #0, #0xa53518
    //     0xa53514: ldur            x4, [x2, #7]
    // 0xa53518: mov             x2, x4
    // 0xa5351c: stur            x2, [fp, #-0x50]
    // 0xa53520: cmp             w3, NULL
    // 0xa53524: b.ne            #0xa53530
    // 0xa53528: r4 = Null
    //     0xa53528: mov             x4, NULL
    // 0xa5352c: b               #0xa53554
    // 0xa53530: LoadField: r4 = r3->field_13
    //     0xa53530: ldur            w4, [x3, #0x13]
    // 0xa53534: DecompressPointer r4
    //     0xa53534: add             x4, x4, HEAP, lsl #32
    // 0xa53538: cmp             w4, NULL
    // 0xa5353c: b.ne            #0xa53548
    // 0xa53540: r4 = Null
    //     0xa53540: mov             x4, NULL
    // 0xa53544: b               #0xa53554
    // 0xa53548: LoadField: r5 = r4->field_b
    //     0xa53548: ldur            w5, [x4, #0xb]
    // 0xa5354c: DecompressPointer r5
    //     0xa5354c: add             x5, x5, HEAP, lsl #32
    // 0xa53550: mov             x4, x5
    // 0xa53554: cmp             w4, NULL
    // 0xa53558: b.ne            #0xa53564
    // 0xa5355c: r4 = 0
    //     0xa5355c: movz            x4, #0
    // 0xa53560: b               #0xa53574
    // 0xa53564: r5 = LoadInt32Instr(r4)
    //     0xa53564: sbfx            x5, x4, #1, #0x1f
    //     0xa53568: tbz             w4, #0, #0xa53570
    //     0xa5356c: ldur            x5, [x4, #7]
    // 0xa53570: mov             x4, x5
    // 0xa53574: stur            x4, [fp, #-0x48]
    // 0xa53578: cmp             w3, NULL
    // 0xa5357c: b.ne            #0xa53588
    // 0xa53580: r5 = Null
    //     0xa53580: mov             x5, NULL
    // 0xa53584: b               #0xa535ac
    // 0xa53588: LoadField: r5 = r3->field_13
    //     0xa53588: ldur            w5, [x3, #0x13]
    // 0xa5358c: DecompressPointer r5
    //     0xa5358c: add             x5, x5, HEAP, lsl #32
    // 0xa53590: cmp             w5, NULL
    // 0xa53594: b.ne            #0xa535a0
    // 0xa53598: r5 = Null
    //     0xa53598: mov             x5, NULL
    // 0xa5359c: b               #0xa535ac
    // 0xa535a0: LoadField: r6 = r5->field_f
    //     0xa535a0: ldur            w6, [x5, #0xf]
    // 0xa535a4: DecompressPointer r6
    //     0xa535a4: add             x6, x6, HEAP, lsl #32
    // 0xa535a8: mov             x5, x6
    // 0xa535ac: cmp             w5, NULL
    // 0xa535b0: b.ne            #0xa535bc
    // 0xa535b4: r5 = 0
    //     0xa535b4: movz            x5, #0
    // 0xa535b8: b               #0xa535cc
    // 0xa535bc: r6 = LoadInt32Instr(r5)
    //     0xa535bc: sbfx            x6, x5, #1, #0x1f
    //     0xa535c0: tbz             w5, #0, #0xa535c8
    //     0xa535c4: ldur            x6, [x5, #7]
    // 0xa535c8: mov             x5, x6
    // 0xa535cc: stur            x5, [fp, #-0x40]
    // 0xa535d0: r0 = Color()
    //     0xa535d0: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa535d4: mov             x1, x0
    // 0xa535d8: r0 = Instance_ColorSpace
    //     0xa535d8: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa535dc: stur            x1, [fp, #-0x58]
    // 0xa535e0: StoreField: r1->field_27 = r0
    //     0xa535e0: stur            w0, [x1, #0x27]
    // 0xa535e4: d0 = 1.000000
    //     0xa535e4: fmov            d0, #1.00000000
    // 0xa535e8: StoreField: r1->field_7 = d0
    //     0xa535e8: stur            d0, [x1, #7]
    // 0xa535ec: ldur            x2, [fp, #-0x50]
    // 0xa535f0: ubfx            x2, x2, #0, #0x20
    // 0xa535f4: and             w3, w2, #0xff
    // 0xa535f8: ubfx            x3, x3, #0, #0x20
    // 0xa535fc: scvtf           d0, x3
    // 0xa53600: d1 = 255.000000
    //     0xa53600: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa53604: fdiv            d2, d0, d1
    // 0xa53608: StoreField: r1->field_f = d2
    //     0xa53608: stur            d2, [x1, #0xf]
    // 0xa5360c: ldur            x2, [fp, #-0x48]
    // 0xa53610: ubfx            x2, x2, #0, #0x20
    // 0xa53614: and             w3, w2, #0xff
    // 0xa53618: ubfx            x3, x3, #0, #0x20
    // 0xa5361c: scvtf           d0, x3
    // 0xa53620: fdiv            d2, d0, d1
    // 0xa53624: ArrayStore: r1[0] = d2  ; List_8
    //     0xa53624: stur            d2, [x1, #0x17]
    // 0xa53628: ldur            x2, [fp, #-0x40]
    // 0xa5362c: ubfx            x2, x2, #0, #0x20
    // 0xa53630: and             w3, w2, #0xff
    // 0xa53634: ubfx            x3, x3, #0, #0x20
    // 0xa53638: scvtf           d0, x3
    // 0xa5363c: fdiv            d2, d0, d1
    // 0xa53640: StoreField: r1->field_1f = d2
    //     0xa53640: stur            d2, [x1, #0x1f]
    // 0xa53644: ldur            x2, [fp, #-0x30]
    // 0xa53648: cmp             w2, NULL
    // 0xa5364c: b.ne            #0xa53658
    // 0xa53650: r3 = Null
    //     0xa53650: mov             x3, NULL
    // 0xa53654: b               #0xa5367c
    // 0xa53658: LoadField: r3 = r2->field_13
    //     0xa53658: ldur            w3, [x2, #0x13]
    // 0xa5365c: DecompressPointer r3
    //     0xa5365c: add             x3, x3, HEAP, lsl #32
    // 0xa53660: cmp             w3, NULL
    // 0xa53664: b.ne            #0xa53670
    // 0xa53668: r3 = Null
    //     0xa53668: mov             x3, NULL
    // 0xa5366c: b               #0xa5367c
    // 0xa53670: LoadField: r4 = r3->field_7
    //     0xa53670: ldur            w4, [x3, #7]
    // 0xa53674: DecompressPointer r4
    //     0xa53674: add             x4, x4, HEAP, lsl #32
    // 0xa53678: mov             x3, x4
    // 0xa5367c: cmp             w3, NULL
    // 0xa53680: b.ne            #0xa5368c
    // 0xa53684: r3 = 0
    //     0xa53684: movz            x3, #0
    // 0xa53688: b               #0xa5369c
    // 0xa5368c: r4 = LoadInt32Instr(r3)
    //     0xa5368c: sbfx            x4, x3, #1, #0x1f
    //     0xa53690: tbz             w3, #0, #0xa53698
    //     0xa53694: ldur            x4, [x3, #7]
    // 0xa53698: mov             x3, x4
    // 0xa5369c: stur            x3, [fp, #-0x50]
    // 0xa536a0: cmp             w2, NULL
    // 0xa536a4: b.ne            #0xa536b0
    // 0xa536a8: r4 = Null
    //     0xa536a8: mov             x4, NULL
    // 0xa536ac: b               #0xa536d4
    // 0xa536b0: LoadField: r4 = r2->field_13
    //     0xa536b0: ldur            w4, [x2, #0x13]
    // 0xa536b4: DecompressPointer r4
    //     0xa536b4: add             x4, x4, HEAP, lsl #32
    // 0xa536b8: cmp             w4, NULL
    // 0xa536bc: b.ne            #0xa536c8
    // 0xa536c0: r4 = Null
    //     0xa536c0: mov             x4, NULL
    // 0xa536c4: b               #0xa536d4
    // 0xa536c8: LoadField: r5 = r4->field_b
    //     0xa536c8: ldur            w5, [x4, #0xb]
    // 0xa536cc: DecompressPointer r5
    //     0xa536cc: add             x5, x5, HEAP, lsl #32
    // 0xa536d0: mov             x4, x5
    // 0xa536d4: cmp             w4, NULL
    // 0xa536d8: b.ne            #0xa536e4
    // 0xa536dc: r4 = 0
    //     0xa536dc: movz            x4, #0
    // 0xa536e0: b               #0xa536f4
    // 0xa536e4: r5 = LoadInt32Instr(r4)
    //     0xa536e4: sbfx            x5, x4, #1, #0x1f
    //     0xa536e8: tbz             w4, #0, #0xa536f0
    //     0xa536ec: ldur            x5, [x4, #7]
    // 0xa536f0: mov             x4, x5
    // 0xa536f4: stur            x4, [fp, #-0x48]
    // 0xa536f8: cmp             w2, NULL
    // 0xa536fc: b.ne            #0xa53708
    // 0xa53700: r2 = Null
    //     0xa53700: mov             x2, NULL
    // 0xa53704: b               #0xa53728
    // 0xa53708: LoadField: r5 = r2->field_13
    //     0xa53708: ldur            w5, [x2, #0x13]
    // 0xa5370c: DecompressPointer r5
    //     0xa5370c: add             x5, x5, HEAP, lsl #32
    // 0xa53710: cmp             w5, NULL
    // 0xa53714: b.ne            #0xa53720
    // 0xa53718: r2 = Null
    //     0xa53718: mov             x2, NULL
    // 0xa5371c: b               #0xa53728
    // 0xa53720: LoadField: r2 = r5->field_f
    //     0xa53720: ldur            w2, [x5, #0xf]
    // 0xa53724: DecompressPointer r2
    //     0xa53724: add             x2, x2, HEAP, lsl #32
    // 0xa53728: cmp             w2, NULL
    // 0xa5372c: b.ne            #0xa53738
    // 0xa53730: r6 = 0
    //     0xa53730: movz            x6, #0
    // 0xa53734: b               #0xa53748
    // 0xa53738: r5 = LoadInt32Instr(r2)
    //     0xa53738: sbfx            x5, x2, #1, #0x1f
    //     0xa5373c: tbz             w2, #0, #0xa53744
    //     0xa53740: ldur            x5, [x2, #7]
    // 0xa53744: mov             x6, x5
    // 0xa53748: ldur            x5, [fp, #-0x18]
    // 0xa5374c: ldur            x2, [fp, #-0x38]
    // 0xa53750: stur            x6, [fp, #-0x40]
    // 0xa53754: r0 = Color()
    //     0xa53754: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xa53758: mov             x3, x0
    // 0xa5375c: r0 = Instance_ColorSpace
    //     0xa5375c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xa53760: stur            x3, [fp, #-0x30]
    // 0xa53764: StoreField: r3->field_27 = r0
    //     0xa53764: stur            w0, [x3, #0x27]
    // 0xa53768: d0 = 0.700000
    //     0xa53768: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa5376c: ldr             d0, [x17, #0xf48]
    // 0xa53770: StoreField: r3->field_7 = d0
    //     0xa53770: stur            d0, [x3, #7]
    // 0xa53774: ldur            x0, [fp, #-0x50]
    // 0xa53778: ubfx            x0, x0, #0, #0x20
    // 0xa5377c: and             w1, w0, #0xff
    // 0xa53780: ubfx            x1, x1, #0, #0x20
    // 0xa53784: scvtf           d0, x1
    // 0xa53788: d1 = 255.000000
    //     0xa53788: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xa5378c: fdiv            d2, d0, d1
    // 0xa53790: StoreField: r3->field_f = d2
    //     0xa53790: stur            d2, [x3, #0xf]
    // 0xa53794: ldur            x0, [fp, #-0x48]
    // 0xa53798: ubfx            x0, x0, #0, #0x20
    // 0xa5379c: and             w1, w0, #0xff
    // 0xa537a0: ubfx            x1, x1, #0, #0x20
    // 0xa537a4: scvtf           d0, x1
    // 0xa537a8: fdiv            d2, d0, d1
    // 0xa537ac: ArrayStore: r3[0] = d2  ; List_8
    //     0xa537ac: stur            d2, [x3, #0x17]
    // 0xa537b0: ldur            x0, [fp, #-0x40]
    // 0xa537b4: ubfx            x0, x0, #0, #0x20
    // 0xa537b8: and             w1, w0, #0xff
    // 0xa537bc: ubfx            x1, x1, #0, #0x20
    // 0xa537c0: scvtf           d0, x1
    // 0xa537c4: fdiv            d2, d0, d1
    // 0xa537c8: StoreField: r3->field_1f = d2
    //     0xa537c8: stur            d2, [x3, #0x1f]
    // 0xa537cc: r1 = Null
    //     0xa537cc: mov             x1, NULL
    // 0xa537d0: r2 = 4
    //     0xa537d0: movz            x2, #0x4
    // 0xa537d4: r0 = AllocateArray()
    //     0xa537d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa537d8: mov             x2, x0
    // 0xa537dc: ldur            x0, [fp, #-0x58]
    // 0xa537e0: stur            x2, [fp, #-0x60]
    // 0xa537e4: StoreField: r2->field_f = r0
    //     0xa537e4: stur            w0, [x2, #0xf]
    // 0xa537e8: ldur            x0, [fp, #-0x30]
    // 0xa537ec: StoreField: r2->field_13 = r0
    //     0xa537ec: stur            w0, [x2, #0x13]
    // 0xa537f0: r1 = <Color>
    //     0xa537f0: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa537f4: ldr             x1, [x1, #0xf80]
    // 0xa537f8: r0 = AllocateGrowableArray()
    //     0xa537f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa537fc: mov             x1, x0
    // 0xa53800: ldur            x0, [fp, #-0x60]
    // 0xa53804: stur            x1, [fp, #-0x30]
    // 0xa53808: StoreField: r1->field_f = r0
    //     0xa53808: stur            w0, [x1, #0xf]
    // 0xa5380c: r2 = 4
    //     0xa5380c: movz            x2, #0x4
    // 0xa53810: StoreField: r1->field_b = r2
    //     0xa53810: stur            w2, [x1, #0xb]
    // 0xa53814: r0 = LinearGradient()
    //     0xa53814: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xa53818: mov             x1, x0
    // 0xa5381c: r0 = Instance_Alignment
    //     0xa5381c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xa53820: ldr             x0, [x0, #0xce0]
    // 0xa53824: stur            x1, [fp, #-0x58]
    // 0xa53828: StoreField: r1->field_13 = r0
    //     0xa53828: stur            w0, [x1, #0x13]
    // 0xa5382c: r0 = Instance_Alignment
    //     0xa5382c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xa53830: ldr             x0, [x0, #0xce8]
    // 0xa53834: ArrayStore: r1[0] = r0  ; List_4
    //     0xa53834: stur            w0, [x1, #0x17]
    // 0xa53838: r0 = Instance_TileMode
    //     0xa53838: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xa5383c: ldr             x0, [x0, #0xcf0]
    // 0xa53840: StoreField: r1->field_1b = r0
    //     0xa53840: stur            w0, [x1, #0x1b]
    // 0xa53844: ldur            x0, [fp, #-0x30]
    // 0xa53848: StoreField: r1->field_7 = r0
    //     0xa53848: stur            w0, [x1, #7]
    // 0xa5384c: r0 = BoxDecoration()
    //     0xa5384c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa53850: mov             x2, x0
    // 0xa53854: ldur            x0, [fp, #-0x38]
    // 0xa53858: stur            x2, [fp, #-0x30]
    // 0xa5385c: StoreField: r2->field_13 = r0
    //     0xa5385c: stur            w0, [x2, #0x13]
    // 0xa53860: ldur            x0, [fp, #-0x58]
    // 0xa53864: StoreField: r2->field_1b = r0
    //     0xa53864: stur            w0, [x2, #0x1b]
    // 0xa53868: r0 = Instance_BoxShape
    //     0xa53868: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa5386c: ldr             x0, [x0, #0x80]
    // 0xa53870: StoreField: r2->field_23 = r0
    //     0xa53870: stur            w0, [x2, #0x23]
    // 0xa53874: ldur            x1, [fp, #-0x18]
    // 0xa53878: cmp             w1, NULL
    // 0xa5387c: b.ne            #0xa53888
    // 0xa53880: r1 = Null
    //     0xa53880: mov             x1, NULL
    // 0xa53884: b               #0xa53894
    // 0xa53888: LoadField: r3 = r1->field_7f
    //     0xa53888: ldur            w3, [x1, #0x7f]
    // 0xa5388c: DecompressPointer r3
    //     0xa5388c: add             x3, x3, HEAP, lsl #32
    // 0xa53890: mov             x1, x3
    // 0xa53894: cmp             w1, NULL
    // 0xa53898: b.ne            #0xa538a4
    // 0xa5389c: r4 = ""
    //     0xa5389c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa538a0: b               #0xa538a8
    // 0xa538a4: mov             x4, x1
    // 0xa538a8: ldur            x3, [fp, #-8]
    // 0xa538ac: stur            x4, [fp, #-0x18]
    // 0xa538b0: LoadField: r1 = r3->field_f
    //     0xa538b0: ldur            w1, [x3, #0xf]
    // 0xa538b4: DecompressPointer r1
    //     0xa538b4: add             x1, x1, HEAP, lsl #32
    // 0xa538b8: cmp             w1, NULL
    // 0xa538bc: b.eq            #0xa543a8
    // 0xa538c0: r0 = of()
    //     0xa538c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa538c4: LoadField: r1 = r0->field_87
    //     0xa538c4: ldur            w1, [x0, #0x87]
    // 0xa538c8: DecompressPointer r1
    //     0xa538c8: add             x1, x1, HEAP, lsl #32
    // 0xa538cc: LoadField: r0 = r1->field_7
    //     0xa538cc: ldur            w0, [x1, #7]
    // 0xa538d0: DecompressPointer r0
    //     0xa538d0: add             x0, x0, HEAP, lsl #32
    // 0xa538d4: r16 = 12.000000
    //     0xa538d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa538d8: ldr             x16, [x16, #0x9e8]
    // 0xa538dc: r30 = Instance_Color
    //     0xa538dc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa538e0: stp             lr, x16, [SP]
    // 0xa538e4: mov             x1, x0
    // 0xa538e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa538e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa538ec: ldr             x4, [x4, #0xaa0]
    // 0xa538f0: r0 = copyWith()
    //     0xa538f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa538f4: stur            x0, [fp, #-0x38]
    // 0xa538f8: r0 = Text()
    //     0xa538f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa538fc: mov             x1, x0
    // 0xa53900: ldur            x0, [fp, #-0x18]
    // 0xa53904: stur            x1, [fp, #-0x58]
    // 0xa53908: StoreField: r1->field_b = r0
    //     0xa53908: stur            w0, [x1, #0xb]
    // 0xa5390c: ldur            x0, [fp, #-0x38]
    // 0xa53910: StoreField: r1->field_13 = r0
    //     0xa53910: stur            w0, [x1, #0x13]
    // 0xa53914: r0 = Instance_TextAlign
    //     0xa53914: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa53918: StoreField: r1->field_1b = r0
    //     0xa53918: stur            w0, [x1, #0x1b]
    // 0xa5391c: r0 = Padding()
    //     0xa5391c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa53920: mov             x1, x0
    // 0xa53924: r0 = Instance_EdgeInsets
    //     0xa53924: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xa53928: ldr             x0, [x0, #0x850]
    // 0xa5392c: stur            x1, [fp, #-0x18]
    // 0xa53930: StoreField: r1->field_f = r0
    //     0xa53930: stur            w0, [x1, #0xf]
    // 0xa53934: ldur            x0, [fp, #-0x58]
    // 0xa53938: StoreField: r1->field_b = r0
    //     0xa53938: stur            w0, [x1, #0xb]
    // 0xa5393c: r0 = Container()
    //     0xa5393c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa53940: stur            x0, [fp, #-0x38]
    // 0xa53944: r16 = 20.000000
    //     0xa53944: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa53948: ldr             x16, [x16, #0xac8]
    // 0xa5394c: r30 = 120.000000
    //     0xa5394c: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xa53950: ldr             lr, [lr, #0x3a0]
    // 0xa53954: stp             lr, x16, [SP, #0x10]
    // 0xa53958: ldur            x16, [fp, #-0x30]
    // 0xa5395c: ldur            lr, [fp, #-0x18]
    // 0xa53960: stp             lr, x16, [SP]
    // 0xa53964: mov             x1, x0
    // 0xa53968: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa53968: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa5396c: ldr             x4, [x4, #0x8c0]
    // 0xa53970: r0 = Container()
    //     0xa53970: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa53974: r0 = Align()
    //     0xa53974: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa53978: mov             x1, x0
    // 0xa5397c: r0 = Instance_Alignment
    //     0xa5397c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa53980: ldr             x0, [x0, #0xfa0]
    // 0xa53984: stur            x1, [fp, #-0x18]
    // 0xa53988: StoreField: r1->field_f = r0
    //     0xa53988: stur            w0, [x1, #0xf]
    // 0xa5398c: ldur            x0, [fp, #-0x38]
    // 0xa53990: StoreField: r1->field_b = r0
    //     0xa53990: stur            w0, [x1, #0xb]
    // 0xa53994: r0 = Padding()
    //     0xa53994: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa53998: mov             x1, x0
    // 0xa5399c: r0 = Instance_EdgeInsets
    //     0xa5399c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xa539a0: ldr             x0, [x0, #0xe50]
    // 0xa539a4: StoreField: r1->field_f = r0
    //     0xa539a4: stur            w0, [x1, #0xf]
    // 0xa539a8: ldur            x0, [fp, #-0x18]
    // 0xa539ac: StoreField: r1->field_b = r0
    //     0xa539ac: stur            w0, [x1, #0xb]
    // 0xa539b0: b               #0xa53cd8
    // 0xa539b4: r0 = Instance_Alignment
    //     0xa539b4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa539b8: ldr             x0, [x0, #0xfa0]
    // 0xa539bc: r2 = 4
    //     0xa539bc: movz            x2, #0x4
    // 0xa539c0: d0 = 0.700000
    //     0xa539c0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa539c4: ldr             d0, [x17, #0xf48]
    // 0xa539c8: ldur            x4, [fp, #-0x20]
    // 0xa539cc: ldur            x3, [fp, #-8]
    // 0xa539d0: LoadField: r1 = r3->field_f
    //     0xa539d0: ldur            w1, [x3, #0xf]
    // 0xa539d4: DecompressPointer r1
    //     0xa539d4: add             x1, x1, HEAP, lsl #32
    // 0xa539d8: cmp             w1, NULL
    // 0xa539dc: b.eq            #0xa543ac
    // 0xa539e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa539e0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa539e4: r0 = _of()
    //     0xa539e4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xa539e8: LoadField: r1 = r0->field_7
    //     0xa539e8: ldur            w1, [x0, #7]
    // 0xa539ec: DecompressPointer r1
    //     0xa539ec: add             x1, x1, HEAP, lsl #32
    // 0xa539f0: LoadField: d0 = r1->field_7
    //     0xa539f0: ldur            d0, [x1, #7]
    // 0xa539f4: d1 = 0.300000
    //     0xa539f4: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xa539f8: ldr             d1, [x17, #0x658]
    // 0xa539fc: fmul            d2, d0, d1
    // 0xa53a00: ldur            x0, [fp, #-8]
    // 0xa53a04: stur            d2, [fp, #-0x70]
    // 0xa53a08: LoadField: r1 = r0->field_f
    //     0xa53a08: ldur            w1, [x0, #0xf]
    // 0xa53a0c: DecompressPointer r1
    //     0xa53a0c: add             x1, x1, HEAP, lsl #32
    // 0xa53a10: cmp             w1, NULL
    // 0xa53a14: b.eq            #0xa543b0
    // 0xa53a18: r0 = of()
    //     0xa53a18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa53a1c: LoadField: r1 = r0->field_5b
    //     0xa53a1c: ldur            w1, [x0, #0x5b]
    // 0xa53a20: DecompressPointer r1
    //     0xa53a20: add             x1, x1, HEAP, lsl #32
    // 0xa53a24: r0 = LoadClassIdInstr(r1)
    //     0xa53a24: ldur            x0, [x1, #-1]
    //     0xa53a28: ubfx            x0, x0, #0xc, #0x14
    // 0xa53a2c: d0 = 0.700000
    //     0xa53a2c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa53a30: ldr             d0, [x17, #0xf48]
    // 0xa53a34: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa53a34: sub             lr, x0, #0xffa
    //     0xa53a38: ldr             lr, [x21, lr, lsl #3]
    //     0xa53a3c: blr             lr
    // 0xa53a40: stur            x0, [fp, #-0x18]
    // 0xa53a44: r0 = Radius()
    //     0xa53a44: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa53a48: d0 = 4.000000
    //     0xa53a48: fmov            d0, #4.00000000
    // 0xa53a4c: stur            x0, [fp, #-0x30]
    // 0xa53a50: StoreField: r0->field_7 = d0
    //     0xa53a50: stur            d0, [x0, #7]
    // 0xa53a54: StoreField: r0->field_f = d0
    //     0xa53a54: stur            d0, [x0, #0xf]
    // 0xa53a58: r0 = BorderRadius()
    //     0xa53a58: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa53a5c: mov             x1, x0
    // 0xa53a60: ldur            x0, [fp, #-0x30]
    // 0xa53a64: stur            x1, [fp, #-0x38]
    // 0xa53a68: StoreField: r1->field_7 = r0
    //     0xa53a68: stur            w0, [x1, #7]
    // 0xa53a6c: StoreField: r1->field_b = r0
    //     0xa53a6c: stur            w0, [x1, #0xb]
    // 0xa53a70: StoreField: r1->field_f = r0
    //     0xa53a70: stur            w0, [x1, #0xf]
    // 0xa53a74: StoreField: r1->field_13 = r0
    //     0xa53a74: stur            w0, [x1, #0x13]
    // 0xa53a78: r0 = BoxDecoration()
    //     0xa53a78: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa53a7c: mov             x1, x0
    // 0xa53a80: ldur            x0, [fp, #-0x18]
    // 0xa53a84: stur            x1, [fp, #-0x30]
    // 0xa53a88: StoreField: r1->field_7 = r0
    //     0xa53a88: stur            w0, [x1, #7]
    // 0xa53a8c: ldur            x0, [fp, #-0x38]
    // 0xa53a90: StoreField: r1->field_13 = r0
    //     0xa53a90: stur            w0, [x1, #0x13]
    // 0xa53a94: r0 = Instance_BoxShape
    //     0xa53a94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa53a98: ldr             x0, [x0, #0x80]
    // 0xa53a9c: StoreField: r1->field_23 = r0
    //     0xa53a9c: stur            w0, [x1, #0x23]
    // 0xa53aa0: r0 = SvgPicture()
    //     0xa53aa0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xa53aa4: stur            x0, [fp, #-0x18]
    // 0xa53aa8: r16 = Instance_ColorFilter
    //     0xa53aa8: add             x16, PP, #0x58, lsl #12  ; [pp+0x587a8] Obj!ColorFilter@d697e1
    //     0xa53aac: ldr             x16, [x16, #0x7a8]
    // 0xa53ab0: str             x16, [SP]
    // 0xa53ab4: mov             x1, x0
    // 0xa53ab8: r2 = "assets/images/bumper_coupon.svg"
    //     0xa53ab8: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xa53abc: ldr             x2, [x2, #0xe48]
    // 0xa53ac0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xa53ac0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xa53ac4: ldr             x4, [x4, #0xa38]
    // 0xa53ac8: r0 = SvgPicture.asset()
    //     0xa53ac8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xa53acc: ldur            x2, [fp, #-0x20]
    // 0xa53ad0: LoadField: r0 = r2->field_13
    //     0xa53ad0: ldur            w0, [x2, #0x13]
    // 0xa53ad4: DecompressPointer r0
    //     0xa53ad4: add             x0, x0, HEAP, lsl #32
    // 0xa53ad8: cmp             w0, NULL
    // 0xa53adc: b.ne            #0xa53ae8
    // 0xa53ae0: r0 = Null
    //     0xa53ae0: mov             x0, NULL
    // 0xa53ae4: b               #0xa53af4
    // 0xa53ae8: LoadField: r1 = r0->field_7f
    //     0xa53ae8: ldur            w1, [x0, #0x7f]
    // 0xa53aec: DecompressPointer r1
    //     0xa53aec: add             x1, x1, HEAP, lsl #32
    // 0xa53af0: mov             x0, x1
    // 0xa53af4: cmp             w0, NULL
    // 0xa53af8: b.ne            #0xa53b04
    // 0xa53afc: r4 = ""
    //     0xa53afc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa53b00: b               #0xa53b08
    // 0xa53b04: mov             x4, x0
    // 0xa53b08: ldur            x3, [fp, #-8]
    // 0xa53b0c: ldur            d0, [fp, #-0x70]
    // 0xa53b10: ldur            x0, [fp, #-0x18]
    // 0xa53b14: stur            x4, [fp, #-0x38]
    // 0xa53b18: LoadField: r1 = r3->field_f
    //     0xa53b18: ldur            w1, [x3, #0xf]
    // 0xa53b1c: DecompressPointer r1
    //     0xa53b1c: add             x1, x1, HEAP, lsl #32
    // 0xa53b20: cmp             w1, NULL
    // 0xa53b24: b.eq            #0xa543b4
    // 0xa53b28: r0 = of()
    //     0xa53b28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa53b2c: LoadField: r1 = r0->field_87
    //     0xa53b2c: ldur            w1, [x0, #0x87]
    // 0xa53b30: DecompressPointer r1
    //     0xa53b30: add             x1, x1, HEAP, lsl #32
    // 0xa53b34: LoadField: r0 = r1->field_2b
    //     0xa53b34: ldur            w0, [x1, #0x2b]
    // 0xa53b38: DecompressPointer r0
    //     0xa53b38: add             x0, x0, HEAP, lsl #32
    // 0xa53b3c: r16 = 10.000000
    //     0xa53b3c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xa53b40: r30 = Instance_Color
    //     0xa53b40: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa53b44: stp             lr, x16, [SP]
    // 0xa53b48: mov             x1, x0
    // 0xa53b4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa53b4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa53b50: ldr             x4, [x4, #0xaa0]
    // 0xa53b54: r0 = copyWith()
    //     0xa53b54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa53b58: stur            x0, [fp, #-0x58]
    // 0xa53b5c: r0 = Text()
    //     0xa53b5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa53b60: mov             x1, x0
    // 0xa53b64: ldur            x0, [fp, #-0x38]
    // 0xa53b68: stur            x1, [fp, #-0x60]
    // 0xa53b6c: StoreField: r1->field_b = r0
    //     0xa53b6c: stur            w0, [x1, #0xb]
    // 0xa53b70: ldur            x0, [fp, #-0x58]
    // 0xa53b74: StoreField: r1->field_13 = r0
    //     0xa53b74: stur            w0, [x1, #0x13]
    // 0xa53b78: r0 = Padding()
    //     0xa53b78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa53b7c: mov             x3, x0
    // 0xa53b80: r0 = Instance_EdgeInsets
    //     0xa53b80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe60] Obj!EdgeInsets@d56f91
    //     0xa53b84: ldr             x0, [x0, #0xe60]
    // 0xa53b88: stur            x3, [fp, #-0x38]
    // 0xa53b8c: StoreField: r3->field_f = r0
    //     0xa53b8c: stur            w0, [x3, #0xf]
    // 0xa53b90: ldur            x0, [fp, #-0x60]
    // 0xa53b94: StoreField: r3->field_b = r0
    //     0xa53b94: stur            w0, [x3, #0xb]
    // 0xa53b98: r1 = Null
    //     0xa53b98: mov             x1, NULL
    // 0xa53b9c: r2 = 4
    //     0xa53b9c: movz            x2, #0x4
    // 0xa53ba0: r0 = AllocateArray()
    //     0xa53ba0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa53ba4: mov             x2, x0
    // 0xa53ba8: ldur            x0, [fp, #-0x18]
    // 0xa53bac: stur            x2, [fp, #-0x58]
    // 0xa53bb0: StoreField: r2->field_f = r0
    //     0xa53bb0: stur            w0, [x2, #0xf]
    // 0xa53bb4: ldur            x0, [fp, #-0x38]
    // 0xa53bb8: StoreField: r2->field_13 = r0
    //     0xa53bb8: stur            w0, [x2, #0x13]
    // 0xa53bbc: r1 = <Widget>
    //     0xa53bbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa53bc0: r0 = AllocateGrowableArray()
    //     0xa53bc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa53bc4: mov             x1, x0
    // 0xa53bc8: ldur            x0, [fp, #-0x58]
    // 0xa53bcc: stur            x1, [fp, #-0x18]
    // 0xa53bd0: StoreField: r1->field_f = r0
    //     0xa53bd0: stur            w0, [x1, #0xf]
    // 0xa53bd4: r0 = 4
    //     0xa53bd4: movz            x0, #0x4
    // 0xa53bd8: StoreField: r1->field_b = r0
    //     0xa53bd8: stur            w0, [x1, #0xb]
    // 0xa53bdc: r0 = Row()
    //     0xa53bdc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa53be0: mov             x1, x0
    // 0xa53be4: r0 = Instance_Axis
    //     0xa53be4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa53be8: stur            x1, [fp, #-0x38]
    // 0xa53bec: StoreField: r1->field_f = r0
    //     0xa53bec: stur            w0, [x1, #0xf]
    // 0xa53bf0: r0 = Instance_MainAxisAlignment
    //     0xa53bf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa53bf4: ldr             x0, [x0, #0xa08]
    // 0xa53bf8: StoreField: r1->field_13 = r0
    //     0xa53bf8: stur            w0, [x1, #0x13]
    // 0xa53bfc: r0 = Instance_MainAxisSize
    //     0xa53bfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa53c00: ldr             x0, [x0, #0xa10]
    // 0xa53c04: ArrayStore: r1[0] = r0  ; List_4
    //     0xa53c04: stur            w0, [x1, #0x17]
    // 0xa53c08: r0 = Instance_CrossAxisAlignment
    //     0xa53c08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa53c0c: ldr             x0, [x0, #0xa18]
    // 0xa53c10: StoreField: r1->field_1b = r0
    //     0xa53c10: stur            w0, [x1, #0x1b]
    // 0xa53c14: r0 = Instance_VerticalDirection
    //     0xa53c14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa53c18: ldr             x0, [x0, #0xa20]
    // 0xa53c1c: StoreField: r1->field_23 = r0
    //     0xa53c1c: stur            w0, [x1, #0x23]
    // 0xa53c20: r0 = Instance_Clip
    //     0xa53c20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa53c24: ldr             x0, [x0, #0x38]
    // 0xa53c28: StoreField: r1->field_2b = r0
    //     0xa53c28: stur            w0, [x1, #0x2b]
    // 0xa53c2c: StoreField: r1->field_2f = rZR
    //     0xa53c2c: stur            xzr, [x1, #0x2f]
    // 0xa53c30: ldur            x0, [fp, #-0x18]
    // 0xa53c34: StoreField: r1->field_b = r0
    //     0xa53c34: stur            w0, [x1, #0xb]
    // 0xa53c38: ldur            d0, [fp, #-0x70]
    // 0xa53c3c: r0 = inline_Allocate_Double()
    //     0xa53c3c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xa53c40: add             x0, x0, #0x10
    //     0xa53c44: cmp             x2, x0
    //     0xa53c48: b.ls            #0xa543b8
    //     0xa53c4c: str             x0, [THR, #0x50]  ; THR::top
    //     0xa53c50: sub             x0, x0, #0xf
    //     0xa53c54: movz            x2, #0xe15c
    //     0xa53c58: movk            x2, #0x3, lsl #16
    //     0xa53c5c: stur            x2, [x0, #-1]
    // 0xa53c60: StoreField: r0->field_7 = d0
    //     0xa53c60: stur            d0, [x0, #7]
    // 0xa53c64: stur            x0, [fp, #-0x18]
    // 0xa53c68: r0 = Container()
    //     0xa53c68: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa53c6c: stur            x0, [fp, #-0x58]
    // 0xa53c70: r16 = 20.000000
    //     0xa53c70: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xa53c74: ldr             x16, [x16, #0xac8]
    // 0xa53c78: ldur            lr, [fp, #-0x18]
    // 0xa53c7c: stp             lr, x16, [SP, #0x10]
    // 0xa53c80: ldur            x16, [fp, #-0x30]
    // 0xa53c84: ldur            lr, [fp, #-0x38]
    // 0xa53c88: stp             lr, x16, [SP]
    // 0xa53c8c: mov             x1, x0
    // 0xa53c90: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa53c90: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa53c94: ldr             x4, [x4, #0x8c0]
    // 0xa53c98: r0 = Container()
    //     0xa53c98: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa53c9c: r0 = Padding()
    //     0xa53c9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa53ca0: mov             x1, x0
    // 0xa53ca4: r0 = Instance_EdgeInsets
    //     0xa53ca4: add             x0, PP, #0x58, lsl #12  ; [pp+0x587b0] Obj!EdgeInsets@d58551
    //     0xa53ca8: ldr             x0, [x0, #0x7b0]
    // 0xa53cac: stur            x1, [fp, #-0x18]
    // 0xa53cb0: StoreField: r1->field_f = r0
    //     0xa53cb0: stur            w0, [x1, #0xf]
    // 0xa53cb4: ldur            x0, [fp, #-0x58]
    // 0xa53cb8: StoreField: r1->field_b = r0
    //     0xa53cb8: stur            w0, [x1, #0xb]
    // 0xa53cbc: r0 = Align()
    //     0xa53cbc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xa53cc0: mov             x1, x0
    // 0xa53cc4: r0 = Instance_Alignment
    //     0xa53cc4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xa53cc8: ldr             x0, [x0, #0xfa0]
    // 0xa53ccc: StoreField: r1->field_f = r0
    //     0xa53ccc: stur            w0, [x1, #0xf]
    // 0xa53cd0: ldur            x0, [fp, #-0x18]
    // 0xa53cd4: StoreField: r1->field_b = r0
    //     0xa53cd4: stur            w0, [x1, #0xb]
    // 0xa53cd8: ldur            x2, [fp, #-0x20]
    // 0xa53cdc: ldur            x0, [fp, #-0x10]
    // 0xa53ce0: stur            x1, [fp, #-0x18]
    // 0xa53ce4: r0 = Visibility()
    //     0xa53ce4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa53ce8: mov             x1, x0
    // 0xa53cec: ldur            x0, [fp, #-0x18]
    // 0xa53cf0: stur            x1, [fp, #-0x30]
    // 0xa53cf4: StoreField: r1->field_b = r0
    //     0xa53cf4: stur            w0, [x1, #0xb]
    // 0xa53cf8: r0 = Instance_SizedBox
    //     0xa53cf8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa53cfc: StoreField: r1->field_f = r0
    //     0xa53cfc: stur            w0, [x1, #0xf]
    // 0xa53d00: ldur            x2, [fp, #-0x10]
    // 0xa53d04: StoreField: r1->field_13 = r2
    //     0xa53d04: stur            w2, [x1, #0x13]
    // 0xa53d08: r2 = false
    //     0xa53d08: add             x2, NULL, #0x30  ; false
    // 0xa53d0c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa53d0c: stur            w2, [x1, #0x17]
    // 0xa53d10: StoreField: r1->field_1b = r2
    //     0xa53d10: stur            w2, [x1, #0x1b]
    // 0xa53d14: StoreField: r1->field_1f = r2
    //     0xa53d14: stur            w2, [x1, #0x1f]
    // 0xa53d18: StoreField: r1->field_23 = r2
    //     0xa53d18: stur            w2, [x1, #0x23]
    // 0xa53d1c: StoreField: r1->field_27 = r2
    //     0xa53d1c: stur            w2, [x1, #0x27]
    // 0xa53d20: StoreField: r1->field_2b = r2
    //     0xa53d20: stur            w2, [x1, #0x2b]
    // 0xa53d24: ldur            x3, [fp, #-0x20]
    // 0xa53d28: LoadField: r4 = r3->field_13
    //     0xa53d28: ldur            w4, [x3, #0x13]
    // 0xa53d2c: DecompressPointer r4
    //     0xa53d2c: add             x4, x4, HEAP, lsl #32
    // 0xa53d30: cmp             w4, NULL
    // 0xa53d34: b.ne            #0xa53d40
    // 0xa53d38: r5 = Null
    //     0xa53d38: mov             x5, NULL
    // 0xa53d3c: b               #0xa53d6c
    // 0xa53d40: LoadField: r5 = r4->field_b7
    //     0xa53d40: ldur            w5, [x4, #0xb7]
    // 0xa53d44: DecompressPointer r5
    //     0xa53d44: add             x5, x5, HEAP, lsl #32
    // 0xa53d48: cmp             w5, NULL
    // 0xa53d4c: b.ne            #0xa53d58
    // 0xa53d50: r5 = Null
    //     0xa53d50: mov             x5, NULL
    // 0xa53d54: b               #0xa53d6c
    // 0xa53d58: LoadField: r6 = r5->field_7
    //     0xa53d58: ldur            w6, [x5, #7]
    // 0xa53d5c: cbnz            w6, #0xa53d68
    // 0xa53d60: r5 = false
    //     0xa53d60: add             x5, NULL, #0x30  ; false
    // 0xa53d64: b               #0xa53d6c
    // 0xa53d68: r5 = true
    //     0xa53d68: add             x5, NULL, #0x20  ; true
    // 0xa53d6c: cmp             w5, NULL
    // 0xa53d70: b.ne            #0xa53d78
    // 0xa53d74: r5 = false
    //     0xa53d74: add             x5, NULL, #0x30  ; false
    // 0xa53d78: stur            x5, [fp, #-0x10]
    // 0xa53d7c: cmp             w4, NULL
    // 0xa53d80: b.ne            #0xa53d8c
    // 0xa53d84: r4 = Null
    //     0xa53d84: mov             x4, NULL
    // 0xa53d88: b               #0xa53d98
    // 0xa53d8c: LoadField: r6 = r4->field_8b
    //     0xa53d8c: ldur            w6, [x4, #0x8b]
    // 0xa53d90: DecompressPointer r6
    //     0xa53d90: add             x6, x6, HEAP, lsl #32
    // 0xa53d94: mov             x4, x6
    // 0xa53d98: cmp             w4, NULL
    // 0xa53d9c: b.eq            #0xa53db0
    // 0xa53da0: tbnz            w4, #4, #0xa53db0
    // 0xa53da4: d0 = 38.000000
    //     0xa53da4: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xa53da8: ldr             d0, [x17, #0xd10]
    // 0xa53dac: b               #0xa53db4
    // 0xa53db0: d0 = 4.000000
    //     0xa53db0: fmov            d0, #4.00000000
    // 0xa53db4: stur            d0, [fp, #-0x70]
    // 0xa53db8: r0 = EdgeInsets()
    //     0xa53db8: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xa53dbc: d0 = 8.000000
    //     0xa53dbc: fmov            d0, #8.00000000
    // 0xa53dc0: stur            x0, [fp, #-0x18]
    // 0xa53dc4: StoreField: r0->field_7 = d0
    //     0xa53dc4: stur            d0, [x0, #7]
    // 0xa53dc8: StoreField: r0->field_f = rZR
    //     0xa53dc8: stur            xzr, [x0, #0xf]
    // 0xa53dcc: ArrayStore: r0[0] = rZR  ; List_8
    //     0xa53dcc: stur            xzr, [x0, #0x17]
    // 0xa53dd0: ldur            d0, [fp, #-0x70]
    // 0xa53dd4: StoreField: r0->field_1f = d0
    //     0xa53dd4: stur            d0, [x0, #0x1f]
    // 0xa53dd8: r16 = <EdgeInsets>
    //     0xa53dd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xa53ddc: ldr             x16, [x16, #0xda0]
    // 0xa53de0: r30 = Instance_EdgeInsets
    //     0xa53de0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xa53de4: ldr             lr, [lr, #0x668]
    // 0xa53de8: stp             lr, x16, [SP]
    // 0xa53dec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa53dec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa53df0: r0 = all()
    //     0xa53df0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa53df4: stur            x0, [fp, #-0x38]
    // 0xa53df8: r16 = <Color>
    //     0xa53df8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa53dfc: ldr             x16, [x16, #0xf80]
    // 0xa53e00: r30 = Instance_Color
    //     0xa53e00: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa53e04: stp             lr, x16, [SP]
    // 0xa53e08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa53e08: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa53e0c: r0 = all()
    //     0xa53e0c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa53e10: stur            x0, [fp, #-0x58]
    // 0xa53e14: r0 = Radius()
    //     0xa53e14: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa53e18: d0 = 5.000000
    //     0xa53e18: fmov            d0, #5.00000000
    // 0xa53e1c: stur            x0, [fp, #-0x60]
    // 0xa53e20: StoreField: r0->field_7 = d0
    //     0xa53e20: stur            d0, [x0, #7]
    // 0xa53e24: StoreField: r0->field_f = d0
    //     0xa53e24: stur            d0, [x0, #0xf]
    // 0xa53e28: r0 = BorderRadius()
    //     0xa53e28: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa53e2c: mov             x1, x0
    // 0xa53e30: ldur            x0, [fp, #-0x60]
    // 0xa53e34: stur            x1, [fp, #-0x68]
    // 0xa53e38: StoreField: r1->field_7 = r0
    //     0xa53e38: stur            w0, [x1, #7]
    // 0xa53e3c: StoreField: r1->field_b = r0
    //     0xa53e3c: stur            w0, [x1, #0xb]
    // 0xa53e40: StoreField: r1->field_f = r0
    //     0xa53e40: stur            w0, [x1, #0xf]
    // 0xa53e44: StoreField: r1->field_13 = r0
    //     0xa53e44: stur            w0, [x1, #0x13]
    // 0xa53e48: r0 = RoundedRectangleBorder()
    //     0xa53e48: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa53e4c: mov             x1, x0
    // 0xa53e50: ldur            x0, [fp, #-0x68]
    // 0xa53e54: StoreField: r1->field_b = r0
    //     0xa53e54: stur            w0, [x1, #0xb]
    // 0xa53e58: r0 = Instance_BorderSide
    //     0xa53e58: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa53e5c: ldr             x0, [x0, #0xe20]
    // 0xa53e60: StoreField: r1->field_7 = r0
    //     0xa53e60: stur            w0, [x1, #7]
    // 0xa53e64: r16 = <RoundedRectangleBorder>
    //     0xa53e64: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa53e68: ldr             x16, [x16, #0xf78]
    // 0xa53e6c: stp             x1, x16, [SP]
    // 0xa53e70: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa53e70: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa53e74: r0 = all()
    //     0xa53e74: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa53e78: stur            x0, [fp, #-0x60]
    // 0xa53e7c: r0 = ButtonStyle()
    //     0xa53e7c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa53e80: mov             x1, x0
    // 0xa53e84: ldur            x0, [fp, #-0x58]
    // 0xa53e88: stur            x1, [fp, #-0x68]
    // 0xa53e8c: StoreField: r1->field_b = r0
    //     0xa53e8c: stur            w0, [x1, #0xb]
    // 0xa53e90: ldur            x0, [fp, #-0x38]
    // 0xa53e94: StoreField: r1->field_23 = r0
    //     0xa53e94: stur            w0, [x1, #0x23]
    // 0xa53e98: ldur            x0, [fp, #-0x60]
    // 0xa53e9c: StoreField: r1->field_43 = r0
    //     0xa53e9c: stur            w0, [x1, #0x43]
    // 0xa53ea0: r0 = TextButtonThemeData()
    //     0xa53ea0: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa53ea4: mov             x1, x0
    // 0xa53ea8: ldur            x0, [fp, #-0x68]
    // 0xa53eac: stur            x1, [fp, #-0x38]
    // 0xa53eb0: StoreField: r1->field_7 = r0
    //     0xa53eb0: stur            w0, [x1, #7]
    // 0xa53eb4: ldur            x2, [fp, #-0x20]
    // 0xa53eb8: LoadField: r0 = r2->field_13
    //     0xa53eb8: ldur            w0, [x2, #0x13]
    // 0xa53ebc: DecompressPointer r0
    //     0xa53ebc: add             x0, x0, HEAP, lsl #32
    // 0xa53ec0: cmp             w0, NULL
    // 0xa53ec4: b.ne            #0xa53ed0
    // 0xa53ec8: r5 = Null
    //     0xa53ec8: mov             x5, NULL
    // 0xa53ecc: b               #0xa53edc
    // 0xa53ed0: LoadField: r3 = r0->field_b7
    //     0xa53ed0: ldur            w3, [x0, #0xb7]
    // 0xa53ed4: DecompressPointer r3
    //     0xa53ed4: add             x3, x3, HEAP, lsl #32
    // 0xa53ed8: mov             x5, x3
    // 0xa53edc: ldur            x4, [fp, #-8]
    // 0xa53ee0: ldur            x3, [fp, #-0x10]
    // 0xa53ee4: ldur            x0, [fp, #-0x18]
    // 0xa53ee8: str             x5, [SP]
    // 0xa53eec: r0 = _interpolateSingle()
    //     0xa53eec: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa53ef0: mov             x2, x0
    // 0xa53ef4: ldur            x0, [fp, #-8]
    // 0xa53ef8: stur            x2, [fp, #-0x58]
    // 0xa53efc: LoadField: r1 = r0->field_f
    //     0xa53efc: ldur            w1, [x0, #0xf]
    // 0xa53f00: DecompressPointer r1
    //     0xa53f00: add             x1, x1, HEAP, lsl #32
    // 0xa53f04: cmp             w1, NULL
    // 0xa53f08: b.eq            #0xa543d0
    // 0xa53f0c: r0 = of()
    //     0xa53f0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa53f10: LoadField: r1 = r0->field_87
    //     0xa53f10: ldur            w1, [x0, #0x87]
    // 0xa53f14: DecompressPointer r1
    //     0xa53f14: add             x1, x1, HEAP, lsl #32
    // 0xa53f18: LoadField: r0 = r1->field_2b
    //     0xa53f18: ldur            w0, [x1, #0x2b]
    // 0xa53f1c: DecompressPointer r0
    //     0xa53f1c: add             x0, x0, HEAP, lsl #32
    // 0xa53f20: r16 = 12.000000
    //     0xa53f20: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa53f24: ldr             x16, [x16, #0x9e8]
    // 0xa53f28: r30 = Instance_Color
    //     0xa53f28: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa53f2c: stp             lr, x16, [SP]
    // 0xa53f30: mov             x1, x0
    // 0xa53f34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa53f34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa53f38: ldr             x4, [x4, #0xaa0]
    // 0xa53f3c: r0 = copyWith()
    //     0xa53f3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa53f40: stur            x0, [fp, #-0x60]
    // 0xa53f44: r0 = Text()
    //     0xa53f44: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa53f48: mov             x3, x0
    // 0xa53f4c: ldur            x0, [fp, #-0x58]
    // 0xa53f50: stur            x3, [fp, #-0x68]
    // 0xa53f54: StoreField: r3->field_b = r0
    //     0xa53f54: stur            w0, [x3, #0xb]
    // 0xa53f58: ldur            x0, [fp, #-0x60]
    // 0xa53f5c: StoreField: r3->field_13 = r0
    //     0xa53f5c: stur            w0, [x3, #0x13]
    // 0xa53f60: r1 = Function '<anonymous closure>':.
    //     0xa53f60: add             x1, PP, #0x58, lsl #12  ; [pp+0x587b8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa53f64: ldr             x1, [x1, #0x7b8]
    // 0xa53f68: r2 = Null
    //     0xa53f68: mov             x2, NULL
    // 0xa53f6c: r0 = AllocateClosure()
    //     0xa53f6c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa53f70: stur            x0, [fp, #-0x58]
    // 0xa53f74: r0 = TextButton()
    //     0xa53f74: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa53f78: mov             x1, x0
    // 0xa53f7c: ldur            x0, [fp, #-0x58]
    // 0xa53f80: stur            x1, [fp, #-0x60]
    // 0xa53f84: StoreField: r1->field_b = r0
    //     0xa53f84: stur            w0, [x1, #0xb]
    // 0xa53f88: r0 = false
    //     0xa53f88: add             x0, NULL, #0x30  ; false
    // 0xa53f8c: StoreField: r1->field_27 = r0
    //     0xa53f8c: stur            w0, [x1, #0x27]
    // 0xa53f90: r2 = true
    //     0xa53f90: add             x2, NULL, #0x20  ; true
    // 0xa53f94: StoreField: r1->field_2f = r2
    //     0xa53f94: stur            w2, [x1, #0x2f]
    // 0xa53f98: ldur            x3, [fp, #-0x68]
    // 0xa53f9c: StoreField: r1->field_37 = r3
    //     0xa53f9c: stur            w3, [x1, #0x37]
    // 0xa53fa0: r0 = TextButtonTheme()
    //     0xa53fa0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa53fa4: mov             x1, x0
    // 0xa53fa8: ldur            x0, [fp, #-0x38]
    // 0xa53fac: stur            x1, [fp, #-0x58]
    // 0xa53fb0: StoreField: r1->field_f = r0
    //     0xa53fb0: stur            w0, [x1, #0xf]
    // 0xa53fb4: ldur            x0, [fp, #-0x60]
    // 0xa53fb8: StoreField: r1->field_b = r0
    //     0xa53fb8: stur            w0, [x1, #0xb]
    // 0xa53fbc: r0 = Padding()
    //     0xa53fbc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa53fc0: mov             x1, x0
    // 0xa53fc4: ldur            x0, [fp, #-0x18]
    // 0xa53fc8: stur            x1, [fp, #-0x38]
    // 0xa53fcc: StoreField: r1->field_f = r0
    //     0xa53fcc: stur            w0, [x1, #0xf]
    // 0xa53fd0: ldur            x0, [fp, #-0x58]
    // 0xa53fd4: StoreField: r1->field_b = r0
    //     0xa53fd4: stur            w0, [x1, #0xb]
    // 0xa53fd8: r0 = Visibility()
    //     0xa53fd8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa53fdc: mov             x2, x0
    // 0xa53fe0: ldur            x0, [fp, #-0x38]
    // 0xa53fe4: stur            x2, [fp, #-0x18]
    // 0xa53fe8: StoreField: r2->field_b = r0
    //     0xa53fe8: stur            w0, [x2, #0xb]
    // 0xa53fec: r0 = Instance_SizedBox
    //     0xa53fec: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa53ff0: StoreField: r2->field_f = r0
    //     0xa53ff0: stur            w0, [x2, #0xf]
    // 0xa53ff4: ldur            x1, [fp, #-0x10]
    // 0xa53ff8: StoreField: r2->field_13 = r1
    //     0xa53ff8: stur            w1, [x2, #0x13]
    // 0xa53ffc: r3 = false
    //     0xa53ffc: add             x3, NULL, #0x30  ; false
    // 0xa54000: ArrayStore: r2[0] = r3  ; List_4
    //     0xa54000: stur            w3, [x2, #0x17]
    // 0xa54004: StoreField: r2->field_1b = r3
    //     0xa54004: stur            w3, [x2, #0x1b]
    // 0xa54008: StoreField: r2->field_1f = r3
    //     0xa54008: stur            w3, [x2, #0x1f]
    // 0xa5400c: StoreField: r2->field_23 = r3
    //     0xa5400c: stur            w3, [x2, #0x23]
    // 0xa54010: StoreField: r2->field_27 = r3
    //     0xa54010: stur            w3, [x2, #0x27]
    // 0xa54014: StoreField: r2->field_2b = r3
    //     0xa54014: stur            w3, [x2, #0x2b]
    // 0xa54018: ldur            x4, [fp, #-0x20]
    // 0xa5401c: LoadField: r1 = r4->field_13
    //     0xa5401c: ldur            w1, [x4, #0x13]
    // 0xa54020: DecompressPointer r1
    //     0xa54020: add             x1, x1, HEAP, lsl #32
    // 0xa54024: cmp             w1, NULL
    // 0xa54028: b.ne            #0xa54034
    // 0xa5402c: r1 = Null
    //     0xa5402c: mov             x1, NULL
    // 0xa54030: b               #0xa54040
    // 0xa54034: LoadField: r5 = r1->field_8b
    //     0xa54034: ldur            w5, [x1, #0x8b]
    // 0xa54038: DecompressPointer r5
    //     0xa54038: add             x5, x5, HEAP, lsl #32
    // 0xa5403c: mov             x1, x5
    // 0xa54040: cmp             w1, NULL
    // 0xa54044: b.ne            #0xa54050
    // 0xa54048: r8 = false
    //     0xa54048: add             x8, NULL, #0x30  ; false
    // 0xa5404c: b               #0xa54054
    // 0xa54050: mov             x8, x1
    // 0xa54054: ldur            x5, [fp, #-8]
    // 0xa54058: ldur            x7, [fp, #-0x28]
    // 0xa5405c: ldur            x6, [fp, #-0x30]
    // 0xa54060: stur            x8, [fp, #-0x10]
    // 0xa54064: LoadField: r1 = r5->field_f
    //     0xa54064: ldur            w1, [x5, #0xf]
    // 0xa54068: DecompressPointer r1
    //     0xa54068: add             x1, x1, HEAP, lsl #32
    // 0xa5406c: cmp             w1, NULL
    // 0xa54070: b.eq            #0xa543d4
    // 0xa54074: r0 = of()
    //     0xa54074: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa54078: LoadField: r1 = r0->field_5b
    //     0xa54078: ldur            w1, [x0, #0x5b]
    // 0xa5407c: DecompressPointer r1
    //     0xa5407c: add             x1, x1, HEAP, lsl #32
    // 0xa54080: r16 = <Color>
    //     0xa54080: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xa54084: ldr             x16, [x16, #0xf80]
    // 0xa54088: stp             x1, x16, [SP]
    // 0xa5408c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa5408c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa54090: r0 = all()
    //     0xa54090: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa54094: stur            x0, [fp, #-0x38]
    // 0xa54098: r0 = Radius()
    //     0xa54098: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa5409c: d0 = 5.000000
    //     0xa5409c: fmov            d0, #5.00000000
    // 0xa540a0: stur            x0, [fp, #-0x58]
    // 0xa540a4: StoreField: r0->field_7 = d0
    //     0xa540a4: stur            d0, [x0, #7]
    // 0xa540a8: StoreField: r0->field_f = d0
    //     0xa540a8: stur            d0, [x0, #0xf]
    // 0xa540ac: r0 = BorderRadius()
    //     0xa540ac: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa540b0: mov             x1, x0
    // 0xa540b4: ldur            x0, [fp, #-0x58]
    // 0xa540b8: stur            x1, [fp, #-0x60]
    // 0xa540bc: StoreField: r1->field_7 = r0
    //     0xa540bc: stur            w0, [x1, #7]
    // 0xa540c0: StoreField: r1->field_b = r0
    //     0xa540c0: stur            w0, [x1, #0xb]
    // 0xa540c4: StoreField: r1->field_f = r0
    //     0xa540c4: stur            w0, [x1, #0xf]
    // 0xa540c8: StoreField: r1->field_13 = r0
    //     0xa540c8: stur            w0, [x1, #0x13]
    // 0xa540cc: r0 = RoundedRectangleBorder()
    //     0xa540cc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xa540d0: mov             x1, x0
    // 0xa540d4: ldur            x0, [fp, #-0x60]
    // 0xa540d8: StoreField: r1->field_b = r0
    //     0xa540d8: stur            w0, [x1, #0xb]
    // 0xa540dc: r0 = Instance_BorderSide
    //     0xa540dc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xa540e0: ldr             x0, [x0, #0xe20]
    // 0xa540e4: StoreField: r1->field_7 = r0
    //     0xa540e4: stur            w0, [x1, #7]
    // 0xa540e8: r16 = <RoundedRectangleBorder>
    //     0xa540e8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xa540ec: ldr             x16, [x16, #0xf78]
    // 0xa540f0: stp             x1, x16, [SP]
    // 0xa540f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xa540f4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xa540f8: r0 = all()
    //     0xa540f8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xa540fc: stur            x0, [fp, #-0x58]
    // 0xa54100: r0 = ButtonStyle()
    //     0xa54100: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xa54104: mov             x1, x0
    // 0xa54108: ldur            x0, [fp, #-0x38]
    // 0xa5410c: stur            x1, [fp, #-0x60]
    // 0xa54110: StoreField: r1->field_b = r0
    //     0xa54110: stur            w0, [x1, #0xb]
    // 0xa54114: ldur            x0, [fp, #-0x58]
    // 0xa54118: StoreField: r1->field_43 = r0
    //     0xa54118: stur            w0, [x1, #0x43]
    // 0xa5411c: r0 = TextButtonThemeData()
    //     0xa5411c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xa54120: mov             x2, x0
    // 0xa54124: ldur            x0, [fp, #-0x60]
    // 0xa54128: stur            x2, [fp, #-0x38]
    // 0xa5412c: StoreField: r2->field_7 = r0
    //     0xa5412c: stur            w0, [x2, #7]
    // 0xa54130: ldur            x0, [fp, #-8]
    // 0xa54134: LoadField: r1 = r0->field_f
    //     0xa54134: ldur            w1, [x0, #0xf]
    // 0xa54138: DecompressPointer r1
    //     0xa54138: add             x1, x1, HEAP, lsl #32
    // 0xa5413c: cmp             w1, NULL
    // 0xa54140: b.eq            #0xa543d8
    // 0xa54144: r0 = of()
    //     0xa54144: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa54148: LoadField: r1 = r0->field_87
    //     0xa54148: ldur            w1, [x0, #0x87]
    // 0xa5414c: DecompressPointer r1
    //     0xa5414c: add             x1, x1, HEAP, lsl #32
    // 0xa54150: LoadField: r0 = r1->field_2b
    //     0xa54150: ldur            w0, [x1, #0x2b]
    // 0xa54154: DecompressPointer r0
    //     0xa54154: add             x0, x0, HEAP, lsl #32
    // 0xa54158: r16 = 12.000000
    //     0xa54158: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa5415c: ldr             x16, [x16, #0x9e8]
    // 0xa54160: r30 = Instance_Color
    //     0xa54160: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xa54164: stp             lr, x16, [SP]
    // 0xa54168: mov             x1, x0
    // 0xa5416c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa5416c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa54170: ldr             x4, [x4, #0xaa0]
    // 0xa54174: r0 = copyWith()
    //     0xa54174: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa54178: stur            x0, [fp, #-8]
    // 0xa5417c: r0 = Text()
    //     0xa5417c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa54180: mov             x3, x0
    // 0xa54184: r0 = "Customisable"
    //     0xa54184: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xa54188: ldr             x0, [x0, #0x970]
    // 0xa5418c: stur            x3, [fp, #-0x58]
    // 0xa54190: StoreField: r3->field_b = r0
    //     0xa54190: stur            w0, [x3, #0xb]
    // 0xa54194: ldur            x0, [fp, #-8]
    // 0xa54198: StoreField: r3->field_13 = r0
    //     0xa54198: stur            w0, [x3, #0x13]
    // 0xa5419c: r1 = Function '<anonymous closure>':.
    //     0xa5419c: add             x1, PP, #0x58, lsl #12  ; [pp+0x587c0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa541a0: ldr             x1, [x1, #0x7c0]
    // 0xa541a4: r2 = Null
    //     0xa541a4: mov             x2, NULL
    // 0xa541a8: r0 = AllocateClosure()
    //     0xa541a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa541ac: stur            x0, [fp, #-8]
    // 0xa541b0: r0 = TextButton()
    //     0xa541b0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xa541b4: mov             x1, x0
    // 0xa541b8: ldur            x0, [fp, #-8]
    // 0xa541bc: stur            x1, [fp, #-0x60]
    // 0xa541c0: StoreField: r1->field_b = r0
    //     0xa541c0: stur            w0, [x1, #0xb]
    // 0xa541c4: r0 = false
    //     0xa541c4: add             x0, NULL, #0x30  ; false
    // 0xa541c8: StoreField: r1->field_27 = r0
    //     0xa541c8: stur            w0, [x1, #0x27]
    // 0xa541cc: r2 = true
    //     0xa541cc: add             x2, NULL, #0x20  ; true
    // 0xa541d0: StoreField: r1->field_2f = r2
    //     0xa541d0: stur            w2, [x1, #0x2f]
    // 0xa541d4: ldur            x3, [fp, #-0x58]
    // 0xa541d8: StoreField: r1->field_37 = r3
    //     0xa541d8: stur            w3, [x1, #0x37]
    // 0xa541dc: r0 = TextButtonTheme()
    //     0xa541dc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xa541e0: mov             x1, x0
    // 0xa541e4: ldur            x0, [fp, #-0x38]
    // 0xa541e8: stur            x1, [fp, #-8]
    // 0xa541ec: StoreField: r1->field_f = r0
    //     0xa541ec: stur            w0, [x1, #0xf]
    // 0xa541f0: ldur            x0, [fp, #-0x60]
    // 0xa541f4: StoreField: r1->field_b = r0
    //     0xa541f4: stur            w0, [x1, #0xb]
    // 0xa541f8: r0 = SizedBox()
    //     0xa541f8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa541fc: mov             x1, x0
    // 0xa54200: r0 = 30.000000
    //     0xa54200: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xa54204: ldr             x0, [x0, #0x768]
    // 0xa54208: stur            x1, [fp, #-0x38]
    // 0xa5420c: StoreField: r1->field_13 = r0
    //     0xa5420c: stur            w0, [x1, #0x13]
    // 0xa54210: ldur            x0, [fp, #-8]
    // 0xa54214: StoreField: r1->field_b = r0
    //     0xa54214: stur            w0, [x1, #0xb]
    // 0xa54218: r0 = Padding()
    //     0xa54218: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa5421c: mov             x1, x0
    // 0xa54220: r0 = Instance_EdgeInsets
    //     0xa54220: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xa54224: ldr             x0, [x0, #0xe68]
    // 0xa54228: stur            x1, [fp, #-8]
    // 0xa5422c: StoreField: r1->field_f = r0
    //     0xa5422c: stur            w0, [x1, #0xf]
    // 0xa54230: ldur            x0, [fp, #-0x38]
    // 0xa54234: StoreField: r1->field_b = r0
    //     0xa54234: stur            w0, [x1, #0xb]
    // 0xa54238: r0 = Visibility()
    //     0xa54238: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa5423c: mov             x3, x0
    // 0xa54240: ldur            x0, [fp, #-8]
    // 0xa54244: stur            x3, [fp, #-0x38]
    // 0xa54248: StoreField: r3->field_b = r0
    //     0xa54248: stur            w0, [x3, #0xb]
    // 0xa5424c: r0 = Instance_SizedBox
    //     0xa5424c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa54250: StoreField: r3->field_f = r0
    //     0xa54250: stur            w0, [x3, #0xf]
    // 0xa54254: ldur            x0, [fp, #-0x10]
    // 0xa54258: StoreField: r3->field_13 = r0
    //     0xa54258: stur            w0, [x3, #0x13]
    // 0xa5425c: r0 = false
    //     0xa5425c: add             x0, NULL, #0x30  ; false
    // 0xa54260: ArrayStore: r3[0] = r0  ; List_4
    //     0xa54260: stur            w0, [x3, #0x17]
    // 0xa54264: StoreField: r3->field_1b = r0
    //     0xa54264: stur            w0, [x3, #0x1b]
    // 0xa54268: StoreField: r3->field_1f = r0
    //     0xa54268: stur            w0, [x3, #0x1f]
    // 0xa5426c: StoreField: r3->field_23 = r0
    //     0xa5426c: stur            w0, [x3, #0x23]
    // 0xa54270: StoreField: r3->field_27 = r0
    //     0xa54270: stur            w0, [x3, #0x27]
    // 0xa54274: StoreField: r3->field_2b = r0
    //     0xa54274: stur            w0, [x3, #0x2b]
    // 0xa54278: r1 = Null
    //     0xa54278: mov             x1, NULL
    // 0xa5427c: r2 = 8
    //     0xa5427c: movz            x2, #0x8
    // 0xa54280: r0 = AllocateArray()
    //     0xa54280: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa54284: mov             x2, x0
    // 0xa54288: ldur            x0, [fp, #-0x28]
    // 0xa5428c: stur            x2, [fp, #-8]
    // 0xa54290: StoreField: r2->field_f = r0
    //     0xa54290: stur            w0, [x2, #0xf]
    // 0xa54294: ldur            x0, [fp, #-0x30]
    // 0xa54298: StoreField: r2->field_13 = r0
    //     0xa54298: stur            w0, [x2, #0x13]
    // 0xa5429c: ldur            x0, [fp, #-0x18]
    // 0xa542a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xa542a0: stur            w0, [x2, #0x17]
    // 0xa542a4: ldur            x0, [fp, #-0x38]
    // 0xa542a8: StoreField: r2->field_1b = r0
    //     0xa542a8: stur            w0, [x2, #0x1b]
    // 0xa542ac: r1 = <Widget>
    //     0xa542ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa542b0: r0 = AllocateGrowableArray()
    //     0xa542b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa542b4: mov             x1, x0
    // 0xa542b8: ldur            x0, [fp, #-8]
    // 0xa542bc: stur            x1, [fp, #-0x10]
    // 0xa542c0: StoreField: r1->field_f = r0
    //     0xa542c0: stur            w0, [x1, #0xf]
    // 0xa542c4: r0 = 8
    //     0xa542c4: movz            x0, #0x8
    // 0xa542c8: StoreField: r1->field_b = r0
    //     0xa542c8: stur            w0, [x1, #0xb]
    // 0xa542cc: r0 = Stack()
    //     0xa542cc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xa542d0: mov             x1, x0
    // 0xa542d4: r0 = Instance_Alignment
    //     0xa542d4: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xa542d8: ldr             x0, [x0, #0x5b8]
    // 0xa542dc: stur            x1, [fp, #-8]
    // 0xa542e0: StoreField: r1->field_f = r0
    //     0xa542e0: stur            w0, [x1, #0xf]
    // 0xa542e4: r0 = Instance_StackFit
    //     0xa542e4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xa542e8: ldr             x0, [x0, #0xfa8]
    // 0xa542ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xa542ec: stur            w0, [x1, #0x17]
    // 0xa542f0: r0 = Instance_Clip
    //     0xa542f0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xa542f4: ldr             x0, [x0, #0x7e0]
    // 0xa542f8: StoreField: r1->field_1b = r0
    //     0xa542f8: stur            w0, [x1, #0x1b]
    // 0xa542fc: ldur            x0, [fp, #-0x10]
    // 0xa54300: StoreField: r1->field_b = r0
    //     0xa54300: stur            w0, [x1, #0xb]
    // 0xa54304: r0 = InkWell()
    //     0xa54304: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa54308: mov             x3, x0
    // 0xa5430c: ldur            x0, [fp, #-8]
    // 0xa54310: stur            x3, [fp, #-0x10]
    // 0xa54314: StoreField: r3->field_b = r0
    //     0xa54314: stur            w0, [x3, #0xb]
    // 0xa54318: ldur            x2, [fp, #-0x20]
    // 0xa5431c: r1 = Function '<anonymous closure>':.
    //     0xa5431c: add             x1, PP, #0x58, lsl #12  ; [pp+0x587c8] AnonymousClosure: (0xa543dc), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::cosmeticThemeSlider (0xa53244)
    //     0xa54320: ldr             x1, [x1, #0x7c8]
    // 0xa54324: r0 = AllocateClosure()
    //     0xa54324: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa54328: ldur            x2, [fp, #-0x10]
    // 0xa5432c: StoreField: r2->field_f = r0
    //     0xa5432c: stur            w0, [x2, #0xf]
    // 0xa54330: r0 = true
    //     0xa54330: add             x0, NULL, #0x20  ; true
    // 0xa54334: StoreField: r2->field_43 = r0
    //     0xa54334: stur            w0, [x2, #0x43]
    // 0xa54338: r1 = Instance_BoxShape
    //     0xa54338: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa5433c: ldr             x1, [x1, #0x80]
    // 0xa54340: StoreField: r2->field_47 = r1
    //     0xa54340: stur            w1, [x2, #0x47]
    // 0xa54344: StoreField: r2->field_6f = r0
    //     0xa54344: stur            w0, [x2, #0x6f]
    // 0xa54348: r1 = false
    //     0xa54348: add             x1, NULL, #0x30  ; false
    // 0xa5434c: StoreField: r2->field_73 = r1
    //     0xa5434c: stur            w1, [x2, #0x73]
    // 0xa54350: StoreField: r2->field_83 = r0
    //     0xa54350: stur            w0, [x2, #0x83]
    // 0xa54354: StoreField: r2->field_7b = r1
    //     0xa54354: stur            w1, [x2, #0x7b]
    // 0xa54358: r0 = AnimatedContainer()
    //     0xa54358: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xa5435c: stur            x0, [fp, #-8]
    // 0xa54360: r16 = Instance_Cubic
    //     0xa54360: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xa54364: ldr             x16, [x16, #0xaf8]
    // 0xa54368: str             x16, [SP]
    // 0xa5436c: mov             x1, x0
    // 0xa54370: ldur            x2, [fp, #-0x10]
    // 0xa54374: r3 = Instance_Duration
    //     0xa54374: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xa54378: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xa54378: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xa5437c: ldr             x4, [x4, #0xbc8]
    // 0xa54380: r0 = AnimatedContainer()
    //     0xa54380: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xa54384: ldur            x0, [fp, #-8]
    // 0xa54388: LeaveFrame
    //     0xa54388: mov             SP, fp
    //     0xa5438c: ldp             fp, lr, [SP], #0x10
    // 0xa54390: ret
    //     0xa54390: ret             
    // 0xa54394: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa54394: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa54398: b               #0xa53268
    // 0xa5439c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xa5439c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xa543a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa543a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa543a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa543a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa543a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa543a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa543ac: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa543ac: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa543b0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa543b0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa543b4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa543b4: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa543b8: SaveReg d0
    //     0xa543b8: str             q0, [SP, #-0x10]!
    // 0xa543bc: SaveReg r1
    //     0xa543bc: str             x1, [SP, #-8]!
    // 0xa543c0: r0 = AllocateDouble()
    //     0xa543c0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa543c4: RestoreReg r1
    //     0xa543c4: ldr             x1, [SP], #8
    // 0xa543c8: RestoreReg d0
    //     0xa543c8: ldr             q0, [SP], #0x10
    // 0xa543cc: b               #0xa53c60
    // 0xa543d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa543d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa543d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa543d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa543d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa543d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa543dc, size: 0x13c
    // 0xa543dc: EnterFrame
    //     0xa543dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa543e0: mov             fp, SP
    // 0xa543e4: AllocStack(0x48)
    //     0xa543e4: sub             SP, SP, #0x48
    // 0xa543e8: SetupParameters()
    //     0xa543e8: ldr             x0, [fp, #0x10]
    //     0xa543ec: ldur            w1, [x0, #0x17]
    //     0xa543f0: add             x1, x1, HEAP, lsl #32
    // 0xa543f4: CheckStackOverflow
    //     0xa543f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa543f8: cmp             SP, x16
    //     0xa543fc: b.ls            #0xa5450c
    // 0xa54400: LoadField: r0 = r1->field_f
    //     0xa54400: ldur            w0, [x1, #0xf]
    // 0xa54404: DecompressPointer r0
    //     0xa54404: add             x0, x0, HEAP, lsl #32
    // 0xa54408: LoadField: r2 = r0->field_b
    //     0xa54408: ldur            w2, [x0, #0xb]
    // 0xa5440c: DecompressPointer r2
    //     0xa5440c: add             x2, x2, HEAP, lsl #32
    // 0xa54410: cmp             w2, NULL
    // 0xa54414: b.eq            #0xa54514
    // 0xa54418: LoadField: r0 = r2->field_2f
    //     0xa54418: ldur            w0, [x2, #0x2f]
    // 0xa5441c: DecompressPointer r0
    //     0xa5441c: add             x0, x0, HEAP, lsl #32
    // 0xa54420: cmp             w0, NULL
    // 0xa54424: b.ne            #0xa5442c
    // 0xa54428: r0 = ""
    //     0xa54428: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa5442c: LoadField: r3 = r2->field_2b
    //     0xa5442c: ldur            w3, [x2, #0x2b]
    // 0xa54430: DecompressPointer r3
    //     0xa54430: add             x3, x3, HEAP, lsl #32
    // 0xa54434: cmp             w3, NULL
    // 0xa54438: b.ne            #0xa54440
    // 0xa5443c: r3 = ""
    //     0xa5443c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa54440: LoadField: r4 = r2->field_37
    //     0xa54440: ldur            w4, [x2, #0x37]
    // 0xa54444: DecompressPointer r4
    //     0xa54444: add             x4, x4, HEAP, lsl #32
    // 0xa54448: LoadField: r5 = r2->field_3b
    //     0xa54448: ldur            w5, [x2, #0x3b]
    // 0xa5444c: DecompressPointer r5
    //     0xa5444c: add             x5, x5, HEAP, lsl #32
    // 0xa54450: LoadField: r6 = r2->field_33
    //     0xa54450: ldur            w6, [x2, #0x33]
    // 0xa54454: DecompressPointer r6
    //     0xa54454: add             x6, x6, HEAP, lsl #32
    // 0xa54458: cmp             w6, NULL
    // 0xa5445c: b.ne            #0xa54464
    // 0xa54460: r6 = ""
    //     0xa54460: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa54464: LoadField: r7 = r2->field_f
    //     0xa54464: ldur            w7, [x2, #0xf]
    // 0xa54468: DecompressPointer r7
    //     0xa54468: add             x7, x7, HEAP, lsl #32
    // 0xa5446c: cmp             w7, NULL
    // 0xa54470: b.ne            #0xa54478
    // 0xa54474: r7 = ""
    //     0xa54474: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa54478: LoadField: r8 = r1->field_13
    //     0xa54478: ldur            w8, [x1, #0x13]
    // 0xa5447c: DecompressPointer r8
    //     0xa5447c: add             x8, x8, HEAP, lsl #32
    // 0xa54480: cmp             w8, NULL
    // 0xa54484: b.ne            #0xa54490
    // 0xa54488: r1 = Null
    //     0xa54488: mov             x1, NULL
    // 0xa5448c: b               #0xa54498
    // 0xa54490: LoadField: r1 = r8->field_2b
    //     0xa54490: ldur            w1, [x8, #0x2b]
    // 0xa54494: DecompressPointer r1
    //     0xa54494: add             x1, x1, HEAP, lsl #32
    // 0xa54498: cmp             w8, NULL
    // 0xa5449c: b.ne            #0xa544a8
    // 0xa544a0: r8 = Null
    //     0xa544a0: mov             x8, NULL
    // 0xa544a4: b               #0xa544c8
    // 0xa544a8: LoadField: r9 = r8->field_3b
    //     0xa544a8: ldur            w9, [x8, #0x3b]
    // 0xa544ac: DecompressPointer r9
    //     0xa544ac: add             x9, x9, HEAP, lsl #32
    // 0xa544b0: cmp             w9, NULL
    // 0xa544b4: b.ne            #0xa544c0
    // 0xa544b8: r8 = Null
    //     0xa544b8: mov             x8, NULL
    // 0xa544bc: b               #0xa544c8
    // 0xa544c0: LoadField: r8 = r9->field_b
    //     0xa544c0: ldur            w8, [x9, #0xb]
    // 0xa544c4: DecompressPointer r8
    //     0xa544c4: add             x8, x8, HEAP, lsl #32
    // 0xa544c8: LoadField: r9 = r2->field_53
    //     0xa544c8: ldur            w9, [x2, #0x53]
    // 0xa544cc: DecompressPointer r9
    //     0xa544cc: add             x9, x9, HEAP, lsl #32
    // 0xa544d0: stp             x0, x9, [SP, #0x38]
    // 0xa544d4: stp             x4, x3, [SP, #0x28]
    // 0xa544d8: stp             x6, x5, [SP, #0x18]
    // 0xa544dc: stp             x1, x7, [SP, #8]
    // 0xa544e0: str             x8, [SP]
    // 0xa544e4: r4 = 0
    //     0xa544e4: movz            x4, #0
    // 0xa544e8: ldr             x0, [SP, #0x40]
    // 0xa544ec: r16 = UnlinkedCall_0x613b5c
    //     0xa544ec: add             x16, PP, #0x58, lsl #12  ; [pp+0x587d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa544f0: add             x16, x16, #0x7d0
    // 0xa544f4: ldp             x5, lr, [x16]
    // 0xa544f8: blr             lr
    // 0xa544fc: r0 = Null
    //     0xa544fc: mov             x0, NULL
    // 0xa54500: LeaveFrame
    //     0xa54500: mov             SP, fp
    //     0xa54504: ldp             fp, lr, [SP], #0x10
    // 0xa54508: ret
    //     0xa54508: ret             
    // 0xa5450c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa5450c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa54510: b               #0xa54400
    // 0xa54514: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa54514: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae9ae4, size: 0x12c0
    // 0xae9ae4: EnterFrame
    //     0xae9ae4: stp             fp, lr, [SP, #-0x10]!
    //     0xae9ae8: mov             fp, SP
    // 0xae9aec: AllocStack(0xa8)
    //     0xae9aec: sub             SP, SP, #0xa8
    // 0xae9af0: SetupParameters(_ProductGridItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xae9af0: mov             x0, x1
    //     0xae9af4: stur            x1, [fp, #-8]
    //     0xae9af8: mov             x1, x2
    //     0xae9afc: stur            x2, [fp, #-0x10]
    // 0xae9b00: CheckStackOverflow
    //     0xae9b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae9b04: cmp             SP, x16
    //     0xae9b08: b.ls            #0xaead74
    // 0xae9b0c: r1 = 1
    //     0xae9b0c: movz            x1, #0x1
    // 0xae9b10: r0 = AllocateContext()
    //     0xae9b10: bl              #0x16f6108  ; AllocateContextStub
    // 0xae9b14: mov             x1, x0
    // 0xae9b18: ldur            x0, [fp, #-8]
    // 0xae9b1c: stur            x1, [fp, #-0x20]
    // 0xae9b20: StoreField: r1->field_f = r0
    //     0xae9b20: stur            w0, [x1, #0xf]
    // 0xae9b24: LoadField: r2 = r0->field_b
    //     0xae9b24: ldur            w2, [x0, #0xb]
    // 0xae9b28: DecompressPointer r2
    //     0xae9b28: add             x2, x2, HEAP, lsl #32
    // 0xae9b2c: stur            x2, [fp, #-0x18]
    // 0xae9b30: cmp             w2, NULL
    // 0xae9b34: b.eq            #0xaead7c
    // 0xae9b38: LoadField: r3 = r2->field_f
    //     0xae9b38: ldur            w3, [x2, #0xf]
    // 0xae9b3c: DecompressPointer r3
    //     0xae9b3c: add             x3, x3, HEAP, lsl #32
    // 0xae9b40: cmp             w3, NULL
    // 0xae9b44: b.ne            #0xae9b50
    // 0xae9b48: r3 = Null
    //     0xae9b48: mov             x3, NULL
    // 0xae9b4c: b               #0xae9b64
    // 0xae9b50: LoadField: r4 = r3->field_7
    //     0xae9b50: ldur            w4, [x3, #7]
    // 0xae9b54: cbz             w4, #0xae9b60
    // 0xae9b58: r3 = false
    //     0xae9b58: add             x3, NULL, #0x30  ; false
    // 0xae9b5c: b               #0xae9b64
    // 0xae9b60: r3 = true
    //     0xae9b60: add             x3, NULL, #0x20  ; true
    // 0xae9b64: cmp             w3, NULL
    // 0xae9b68: b.eq            #0xae9bcc
    // 0xae9b6c: tbnz            w3, #4, #0xae9bcc
    // 0xae9b70: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xae9b70: ldur            w3, [x2, #0x17]
    // 0xae9b74: DecompressPointer r3
    //     0xae9b74: add             x3, x3, HEAP, lsl #32
    // 0xae9b78: cmp             w3, NULL
    // 0xae9b7c: b.ne            #0xae9b88
    // 0xae9b80: r3 = Null
    //     0xae9b80: mov             x3, NULL
    // 0xae9b84: b               #0xae9bb8
    // 0xae9b88: LoadField: r4 = r3->field_7
    //     0xae9b88: ldur            w4, [x3, #7]
    // 0xae9b8c: DecompressPointer r4
    //     0xae9b8c: add             x4, x4, HEAP, lsl #32
    // 0xae9b90: cmp             w4, NULL
    // 0xae9b94: b.ne            #0xae9ba0
    // 0xae9b98: r3 = Null
    //     0xae9b98: mov             x3, NULL
    // 0xae9b9c: b               #0xae9bb8
    // 0xae9ba0: LoadField: r3 = r4->field_7
    //     0xae9ba0: ldur            w3, [x4, #7]
    // 0xae9ba4: cbnz            w3, #0xae9bb0
    // 0xae9ba8: r4 = false
    //     0xae9ba8: add             x4, NULL, #0x30  ; false
    // 0xae9bac: b               #0xae9bb4
    // 0xae9bb0: r4 = true
    //     0xae9bb0: add             x4, NULL, #0x20  ; true
    // 0xae9bb4: mov             x3, x4
    // 0xae9bb8: cmp             w3, NULL
    // 0xae9bbc: b.eq            #0xae9bcc
    // 0xae9bc0: tbnz            w3, #4, #0xae9bcc
    // 0xae9bc4: d0 = 12.000000
    //     0xae9bc4: fmov            d0, #12.00000000
    // 0xae9bc8: b               #0xae9bd0
    // 0xae9bcc: d0 = 0.000000
    //     0xae9bcc: eor             v0.16b, v0.16b, v0.16b
    // 0xae9bd0: stur            d0, [fp, #-0x88]
    // 0xae9bd4: r0 = EdgeInsets()
    //     0xae9bd4: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xae9bd8: d0 = 12.000000
    //     0xae9bd8: fmov            d0, #12.00000000
    // 0xae9bdc: stur            x0, [fp, #-0x30]
    // 0xae9be0: StoreField: r0->field_7 = d0
    //     0xae9be0: stur            d0, [x0, #7]
    // 0xae9be4: ldur            d1, [fp, #-0x88]
    // 0xae9be8: StoreField: r0->field_f = d1
    //     0xae9be8: stur            d1, [x0, #0xf]
    // 0xae9bec: ArrayStore: r0[0] = d0  ; List_8
    //     0xae9bec: stur            d0, [x0, #0x17]
    // 0xae9bf0: d0 = 48.000000
    //     0xae9bf0: ldr             d0, [PP, #0x6d18]  ; [pp+0x6d18] IMM: double(48) from 0x4048000000000000
    // 0xae9bf4: StoreField: r0->field_1f = d0
    //     0xae9bf4: stur            d0, [x0, #0x1f]
    // 0xae9bf8: ldur            x1, [fp, #-0x18]
    // 0xae9bfc: LoadField: r2 = r1->field_1b
    //     0xae9bfc: ldur            w2, [x1, #0x1b]
    // 0xae9c00: DecompressPointer r2
    //     0xae9c00: add             x2, x2, HEAP, lsl #32
    // 0xae9c04: tbnz            w2, #4, #0xae9c14
    // 0xae9c08: r3 = Instance_MainAxisAlignment
    //     0xae9c08: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae9c0c: ldr             x3, [x3, #0xa08]
    // 0xae9c10: b               #0xae9c1c
    // 0xae9c14: r3 = Instance_MainAxisAlignment
    //     0xae9c14: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xae9c18: ldr             x3, [x3, #0xab0]
    // 0xae9c1c: stur            x3, [fp, #-0x28]
    // 0xae9c20: LoadField: r2 = r1->field_23
    //     0xae9c20: ldur            w2, [x1, #0x23]
    // 0xae9c24: DecompressPointer r2
    //     0xae9c24: add             x2, x2, HEAP, lsl #32
    // 0xae9c28: LoadField: r1 = r2->field_7
    //     0xae9c28: ldur            w1, [x2, #7]
    // 0xae9c2c: DecompressPointer r1
    //     0xae9c2c: add             x1, x1, HEAP, lsl #32
    // 0xae9c30: cmp             w1, NULL
    // 0xae9c34: b.ne            #0xae9c40
    // 0xae9c38: r1 = Instance_TitleAlignment
    //     0xae9c38: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae9c3c: ldr             x1, [x1, #0x518]
    // 0xae9c40: r16 = Instance_TitleAlignment
    //     0xae9c40: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xae9c44: ldr             x16, [x16, #0x520]
    // 0xae9c48: cmp             w1, w16
    // 0xae9c4c: b.ne            #0xae9c5c
    // 0xae9c50: r5 = Instance_CrossAxisAlignment
    //     0xae9c50: add             x5, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xae9c54: ldr             x5, [x5, #0xc68]
    // 0xae9c58: b               #0xae9c80
    // 0xae9c5c: r16 = Instance_TitleAlignment
    //     0xae9c5c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae9c60: ldr             x16, [x16, #0x518]
    // 0xae9c64: cmp             w1, w16
    // 0xae9c68: b.ne            #0xae9c78
    // 0xae9c6c: r5 = Instance_CrossAxisAlignment
    //     0xae9c6c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xae9c70: ldr             x5, [x5, #0x890]
    // 0xae9c74: b               #0xae9c80
    // 0xae9c78: r5 = Instance_CrossAxisAlignment
    //     0xae9c78: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae9c7c: ldr             x5, [x5, #0xa18]
    // 0xae9c80: ldur            x4, [fp, #-8]
    // 0xae9c84: stur            x5, [fp, #-0x18]
    // 0xae9c88: r1 = <Widget>
    //     0xae9c88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae9c8c: r2 = 0
    //     0xae9c8c: movz            x2, #0
    // 0xae9c90: r0 = _GrowableList()
    //     0xae9c90: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xae9c94: mov             x2, x0
    // 0xae9c98: ldur            x1, [fp, #-8]
    // 0xae9c9c: stur            x2, [fp, #-0x38]
    // 0xae9ca0: LoadField: r0 = r1->field_b
    //     0xae9ca0: ldur            w0, [x1, #0xb]
    // 0xae9ca4: DecompressPointer r0
    //     0xae9ca4: add             x0, x0, HEAP, lsl #32
    // 0xae9ca8: cmp             w0, NULL
    // 0xae9cac: b.eq            #0xaead80
    // 0xae9cb0: LoadField: r3 = r0->field_f
    //     0xae9cb0: ldur            w3, [x0, #0xf]
    // 0xae9cb4: DecompressPointer r3
    //     0xae9cb4: add             x3, x3, HEAP, lsl #32
    // 0xae9cb8: cmp             w3, NULL
    // 0xae9cbc: b.ne            #0xae9cc8
    // 0xae9cc0: r0 = Null
    //     0xae9cc0: mov             x0, NULL
    // 0xae9cc4: b               #0xae9ce0
    // 0xae9cc8: LoadField: r0 = r3->field_7
    //     0xae9cc8: ldur            w0, [x3, #7]
    // 0xae9ccc: cbnz            w0, #0xae9cd8
    // 0xae9cd0: r4 = false
    //     0xae9cd0: add             x4, NULL, #0x30  ; false
    // 0xae9cd4: b               #0xae9cdc
    // 0xae9cd8: r4 = true
    //     0xae9cd8: add             x4, NULL, #0x20  ; true
    // 0xae9cdc: mov             x0, x4
    // 0xae9ce0: cmp             w0, NULL
    // 0xae9ce4: b.eq            #0xae9ea8
    // 0xae9ce8: tbnz            w0, #4, #0xae9ea8
    // 0xae9cec: cmp             w3, NULL
    // 0xae9cf0: b.ne            #0xae9cfc
    // 0xae9cf4: r0 = Null
    //     0xae9cf4: mov             x0, NULL
    // 0xae9cf8: b               #0xae9d14
    // 0xae9cfc: r0 = LoadClassIdInstr(r3)
    //     0xae9cfc: ldur            x0, [x3, #-1]
    //     0xae9d00: ubfx            x0, x0, #0xc, #0x14
    // 0xae9d04: str             x3, [SP]
    // 0xae9d08: r0 = GDT[cid_x0 + -0x1000]()
    //     0xae9d08: sub             lr, x0, #1, lsl #12
    //     0xae9d0c: ldr             lr, [x21, lr, lsl #3]
    //     0xae9d10: blr             lr
    // 0xae9d14: cmp             w0, NULL
    // 0xae9d18: b.ne            #0xae9d24
    // 0xae9d1c: r2 = ""
    //     0xae9d1c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae9d20: b               #0xae9d28
    // 0xae9d24: mov             x2, x0
    // 0xae9d28: ldur            x0, [fp, #-8]
    // 0xae9d2c: stur            x2, [fp, #-0x48]
    // 0xae9d30: LoadField: r1 = r0->field_b
    //     0xae9d30: ldur            w1, [x0, #0xb]
    // 0xae9d34: DecompressPointer r1
    //     0xae9d34: add             x1, x1, HEAP, lsl #32
    // 0xae9d38: cmp             w1, NULL
    // 0xae9d3c: b.eq            #0xaead84
    // 0xae9d40: LoadField: r3 = r1->field_23
    //     0xae9d40: ldur            w3, [x1, #0x23]
    // 0xae9d44: DecompressPointer r3
    //     0xae9d44: add             x3, x3, HEAP, lsl #32
    // 0xae9d48: LoadField: r1 = r3->field_7
    //     0xae9d48: ldur            w1, [x3, #7]
    // 0xae9d4c: DecompressPointer r1
    //     0xae9d4c: add             x1, x1, HEAP, lsl #32
    // 0xae9d50: cmp             w1, NULL
    // 0xae9d54: b.ne            #0xae9d60
    // 0xae9d58: r1 = Instance_TitleAlignment
    //     0xae9d58: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae9d5c: ldr             x1, [x1, #0x518]
    // 0xae9d60: r16 = Instance_TitleAlignment
    //     0xae9d60: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xae9d64: ldr             x16, [x16, #0x520]
    // 0xae9d68: cmp             w1, w16
    // 0xae9d6c: b.ne            #0xae9d78
    // 0xae9d70: r4 = Instance_TextAlign
    //     0xae9d70: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xae9d74: b               #0xae9d94
    // 0xae9d78: r16 = Instance_TitleAlignment
    //     0xae9d78: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xae9d7c: ldr             x16, [x16, #0x518]
    // 0xae9d80: cmp             w1, w16
    // 0xae9d84: b.ne            #0xae9d90
    // 0xae9d88: r4 = Instance_TextAlign
    //     0xae9d88: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xae9d8c: b               #0xae9d94
    // 0xae9d90: r4 = Instance_TextAlign
    //     0xae9d90: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xae9d94: ldur            x3, [fp, #-0x38]
    // 0xae9d98: ldur            x1, [fp, #-0x10]
    // 0xae9d9c: stur            x4, [fp, #-0x40]
    // 0xae9da0: r0 = of()
    //     0xae9da0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae9da4: LoadField: r1 = r0->field_87
    //     0xae9da4: ldur            w1, [x0, #0x87]
    // 0xae9da8: DecompressPointer r1
    //     0xae9da8: add             x1, x1, HEAP, lsl #32
    // 0xae9dac: LoadField: r0 = r1->field_7
    //     0xae9dac: ldur            w0, [x1, #7]
    // 0xae9db0: DecompressPointer r0
    //     0xae9db0: add             x0, x0, HEAP, lsl #32
    // 0xae9db4: r16 = 32.000000
    //     0xae9db4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xae9db8: ldr             x16, [x16, #0x848]
    // 0xae9dbc: r30 = Instance_Color
    //     0xae9dbc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae9dc0: stp             lr, x16, [SP]
    // 0xae9dc4: mov             x1, x0
    // 0xae9dc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae9dc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae9dcc: ldr             x4, [x4, #0xaa0]
    // 0xae9dd0: r0 = copyWith()
    //     0xae9dd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae9dd4: stur            x0, [fp, #-0x50]
    // 0xae9dd8: r0 = Text()
    //     0xae9dd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae9ddc: mov             x1, x0
    // 0xae9de0: ldur            x0, [fp, #-0x48]
    // 0xae9de4: stur            x1, [fp, #-0x58]
    // 0xae9de8: StoreField: r1->field_b = r0
    //     0xae9de8: stur            w0, [x1, #0xb]
    // 0xae9dec: ldur            x0, [fp, #-0x50]
    // 0xae9df0: StoreField: r1->field_13 = r0
    //     0xae9df0: stur            w0, [x1, #0x13]
    // 0xae9df4: ldur            x0, [fp, #-0x40]
    // 0xae9df8: StoreField: r1->field_1b = r0
    //     0xae9df8: stur            w0, [x1, #0x1b]
    // 0xae9dfc: r0 = Padding()
    //     0xae9dfc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae9e00: mov             x2, x0
    // 0xae9e04: r0 = Instance_EdgeInsets
    //     0xae9e04: add             x0, PP, #0x45, lsl #12  ; [pp+0x45c98] Obj!EdgeInsets@d589a1
    //     0xae9e08: ldr             x0, [x0, #0xc98]
    // 0xae9e0c: stur            x2, [fp, #-0x40]
    // 0xae9e10: StoreField: r2->field_f = r0
    //     0xae9e10: stur            w0, [x2, #0xf]
    // 0xae9e14: ldur            x0, [fp, #-0x58]
    // 0xae9e18: StoreField: r2->field_b = r0
    //     0xae9e18: stur            w0, [x2, #0xb]
    // 0xae9e1c: ldur            x0, [fp, #-0x38]
    // 0xae9e20: LoadField: r1 = r0->field_b
    //     0xae9e20: ldur            w1, [x0, #0xb]
    // 0xae9e24: LoadField: r3 = r0->field_f
    //     0xae9e24: ldur            w3, [x0, #0xf]
    // 0xae9e28: DecompressPointer r3
    //     0xae9e28: add             x3, x3, HEAP, lsl #32
    // 0xae9e2c: LoadField: r4 = r3->field_b
    //     0xae9e2c: ldur            w4, [x3, #0xb]
    // 0xae9e30: r3 = LoadInt32Instr(r1)
    //     0xae9e30: sbfx            x3, x1, #1, #0x1f
    // 0xae9e34: stur            x3, [fp, #-0x60]
    // 0xae9e38: r1 = LoadInt32Instr(r4)
    //     0xae9e38: sbfx            x1, x4, #1, #0x1f
    // 0xae9e3c: cmp             x3, x1
    // 0xae9e40: b.ne            #0xae9e4c
    // 0xae9e44: mov             x1, x0
    // 0xae9e48: r0 = _growToNextCapacity()
    //     0xae9e48: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae9e4c: ldur            x2, [fp, #-0x38]
    // 0xae9e50: ldur            x3, [fp, #-0x60]
    // 0xae9e54: add             x4, x3, #1
    // 0xae9e58: lsl             x0, x4, #1
    // 0xae9e5c: StoreField: r2->field_b = r0
    //     0xae9e5c: stur            w0, [x2, #0xb]
    // 0xae9e60: LoadField: r5 = r2->field_f
    //     0xae9e60: ldur            w5, [x2, #0xf]
    // 0xae9e64: DecompressPointer r5
    //     0xae9e64: add             x5, x5, HEAP, lsl #32
    // 0xae9e68: mov             x1, x5
    // 0xae9e6c: ldur            x0, [fp, #-0x40]
    // 0xae9e70: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae9e70: add             x25, x1, x3, lsl #2
    //     0xae9e74: add             x25, x25, #0xf
    //     0xae9e78: str             w0, [x25]
    //     0xae9e7c: tbz             w0, #0, #0xae9e98
    //     0xae9e80: ldurb           w16, [x1, #-1]
    //     0xae9e84: ldurb           w17, [x0, #-1]
    //     0xae9e88: and             x16, x17, x16, lsr #2
    //     0xae9e8c: tst             x16, HEAP, lsr #32
    //     0xae9e90: b.eq            #0xae9e98
    //     0xae9e94: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xae9e98: mov             x3, x4
    // 0xae9e9c: mov             x1, x5
    // 0xae9ea0: mov             x0, x2
    // 0xae9ea4: b               #0xae9f04
    // 0xae9ea8: LoadField: r0 = r2->field_b
    //     0xae9ea8: ldur            w0, [x2, #0xb]
    // 0xae9eac: LoadField: r1 = r2->field_f
    //     0xae9eac: ldur            w1, [x2, #0xf]
    // 0xae9eb0: DecompressPointer r1
    //     0xae9eb0: add             x1, x1, HEAP, lsl #32
    // 0xae9eb4: LoadField: r3 = r1->field_b
    //     0xae9eb4: ldur            w3, [x1, #0xb]
    // 0xae9eb8: r4 = LoadInt32Instr(r0)
    //     0xae9eb8: sbfx            x4, x0, #1, #0x1f
    // 0xae9ebc: stur            x4, [fp, #-0x60]
    // 0xae9ec0: r0 = LoadInt32Instr(r3)
    //     0xae9ec0: sbfx            x0, x3, #1, #0x1f
    // 0xae9ec4: cmp             x4, x0
    // 0xae9ec8: b.ne            #0xae9ed4
    // 0xae9ecc: mov             x1, x2
    // 0xae9ed0: r0 = _growToNextCapacity()
    //     0xae9ed0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae9ed4: ldur            x0, [fp, #-0x38]
    // 0xae9ed8: ldur            x1, [fp, #-0x60]
    // 0xae9edc: add             x2, x1, #1
    // 0xae9ee0: lsl             x3, x2, #1
    // 0xae9ee4: StoreField: r0->field_b = r3
    //     0xae9ee4: stur            w3, [x0, #0xb]
    // 0xae9ee8: LoadField: r3 = r0->field_f
    //     0xae9ee8: ldur            w3, [x0, #0xf]
    // 0xae9eec: DecompressPointer r3
    //     0xae9eec: add             x3, x3, HEAP, lsl #32
    // 0xae9ef0: add             x4, x3, x1, lsl #2
    // 0xae9ef4: r16 = Instance_SizedBox
    //     0xae9ef4: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xae9ef8: StoreField: r4->field_f = r16
    //     0xae9ef8: stur            w16, [x4, #0xf]
    // 0xae9efc: mov             x1, x3
    // 0xae9f00: mov             x3, x2
    // 0xae9f04: ldur            x2, [fp, #-8]
    // 0xae9f08: stur            x3, [fp, #-0x70]
    // 0xae9f0c: LoadField: r4 = r2->field_b
    //     0xae9f0c: ldur            w4, [x2, #0xb]
    // 0xae9f10: DecompressPointer r4
    //     0xae9f10: add             x4, x4, HEAP, lsl #32
    // 0xae9f14: cmp             w4, NULL
    // 0xae9f18: b.eq            #0xaead88
    // 0xae9f1c: LoadField: r5 = r4->field_13
    //     0xae9f1c: ldur            w5, [x4, #0x13]
    // 0xae9f20: DecompressPointer r5
    //     0xae9f20: add             x5, x5, HEAP, lsl #32
    // 0xae9f24: tbnz            w5, #4, #0xaea258
    // 0xae9f28: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xae9f28: ldur            w5, [x4, #0x17]
    // 0xae9f2c: DecompressPointer r5
    //     0xae9f2c: add             x5, x5, HEAP, lsl #32
    // 0xae9f30: cmp             w5, NULL
    // 0xae9f34: b.ne            #0xae9f40
    // 0xae9f38: r4 = Null
    //     0xae9f38: mov             x4, NULL
    // 0xae9f3c: b               #0xae9f6c
    // 0xae9f40: LoadField: r4 = r5->field_7
    //     0xae9f40: ldur            w4, [x5, #7]
    // 0xae9f44: DecompressPointer r4
    //     0xae9f44: add             x4, x4, HEAP, lsl #32
    // 0xae9f48: cmp             w4, NULL
    // 0xae9f4c: b.ne            #0xae9f58
    // 0xae9f50: r4 = Null
    //     0xae9f50: mov             x4, NULL
    // 0xae9f54: b               #0xae9f6c
    // 0xae9f58: LoadField: r6 = r4->field_7
    //     0xae9f58: ldur            w6, [x4, #7]
    // 0xae9f5c: cbnz            w6, #0xae9f68
    // 0xae9f60: r4 = false
    //     0xae9f60: add             x4, NULL, #0x30  ; false
    // 0xae9f64: b               #0xae9f6c
    // 0xae9f68: r4 = true
    //     0xae9f68: add             x4, NULL, #0x20  ; true
    // 0xae9f6c: cmp             w4, NULL
    // 0xae9f70: b.ne            #0xae9f7c
    // 0xae9f74: mov             x2, x0
    // 0xae9f78: b               #0xaea25c
    // 0xae9f7c: tbnz            w4, #4, #0xaea250
    // 0xae9f80: cmp             w5, NULL
    // 0xae9f84: b.ne            #0xae9f90
    // 0xae9f88: r1 = Null
    //     0xae9f88: mov             x1, NULL
    // 0xae9f8c: b               #0xae9fbc
    // 0xae9f90: LoadField: r1 = r5->field_7
    //     0xae9f90: ldur            w1, [x5, #7]
    // 0xae9f94: DecompressPointer r1
    //     0xae9f94: add             x1, x1, HEAP, lsl #32
    // 0xae9f98: cmp             w1, NULL
    // 0xae9f9c: b.ne            #0xae9fa8
    // 0xae9fa0: r1 = Null
    //     0xae9fa0: mov             x1, NULL
    // 0xae9fa4: b               #0xae9fbc
    // 0xae9fa8: LoadField: r3 = r1->field_7
    //     0xae9fa8: ldur            w3, [x1, #7]
    // 0xae9fac: cbnz            w3, #0xae9fb8
    // 0xae9fb0: r1 = false
    //     0xae9fb0: add             x1, NULL, #0x30  ; false
    // 0xae9fb4: b               #0xae9fbc
    // 0xae9fb8: r1 = true
    //     0xae9fb8: add             x1, NULL, #0x20  ; true
    // 0xae9fbc: cmp             w1, NULL
    // 0xae9fc0: b.ne            #0xae9fcc
    // 0xae9fc4: r3 = false
    //     0xae9fc4: add             x3, NULL, #0x30  ; false
    // 0xae9fc8: b               #0xae9fd0
    // 0xae9fcc: mov             x3, x1
    // 0xae9fd0: ldur            x1, [fp, #-0x10]
    // 0xae9fd4: stur            x3, [fp, #-0x40]
    // 0xae9fd8: r0 = of()
    //     0xae9fd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae9fdc: LoadField: r1 = r0->field_5b
    //     0xae9fdc: ldur            w1, [x0, #0x5b]
    // 0xae9fe0: DecompressPointer r1
    //     0xae9fe0: add             x1, x1, HEAP, lsl #32
    // 0xae9fe4: stur            x1, [fp, #-0x48]
    // 0xae9fe8: r0 = BoxDecoration()
    //     0xae9fe8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xae9fec: mov             x2, x0
    // 0xae9ff0: ldur            x0, [fp, #-0x48]
    // 0xae9ff4: stur            x2, [fp, #-0x50]
    // 0xae9ff8: StoreField: r2->field_7 = r0
    //     0xae9ff8: stur            w0, [x2, #7]
    // 0xae9ffc: r0 = Instance_BorderRadius
    //     0xae9ffc: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xaea000: ldr             x0, [x0, #0x460]
    // 0xaea004: StoreField: r2->field_13 = r0
    //     0xaea004: stur            w0, [x2, #0x13]
    // 0xaea008: r0 = Instance_BoxShape
    //     0xaea008: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaea00c: ldr             x0, [x0, #0x80]
    // 0xaea010: StoreField: r2->field_23 = r0
    //     0xaea010: stur            w0, [x2, #0x23]
    // 0xaea014: ldur            x3, [fp, #-8]
    // 0xaea018: LoadField: r1 = r3->field_b
    //     0xaea018: ldur            w1, [x3, #0xb]
    // 0xaea01c: DecompressPointer r1
    //     0xaea01c: add             x1, x1, HEAP, lsl #32
    // 0xaea020: cmp             w1, NULL
    // 0xaea024: b.eq            #0xaead8c
    // 0xaea028: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xaea028: ldur            w4, [x1, #0x17]
    // 0xaea02c: DecompressPointer r4
    //     0xaea02c: add             x4, x4, HEAP, lsl #32
    // 0xaea030: cmp             w4, NULL
    // 0xaea034: b.ne            #0xaea040
    // 0xaea038: r1 = Null
    //     0xaea038: mov             x1, NULL
    // 0xaea03c: b               #0xaea048
    // 0xaea040: LoadField: r1 = r4->field_7
    //     0xaea040: ldur            w1, [x4, #7]
    // 0xaea044: DecompressPointer r1
    //     0xaea044: add             x1, x1, HEAP, lsl #32
    // 0xaea048: cmp             w1, NULL
    // 0xaea04c: b.ne            #0xaea058
    // 0xaea050: r6 = ""
    //     0xaea050: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaea054: b               #0xaea05c
    // 0xaea058: mov             x6, x1
    // 0xaea05c: ldur            x4, [fp, #-0x38]
    // 0xaea060: ldur            x5, [fp, #-0x40]
    // 0xaea064: ldur            x1, [fp, #-0x10]
    // 0xaea068: stur            x6, [fp, #-0x48]
    // 0xaea06c: r0 = of()
    //     0xaea06c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaea070: LoadField: r1 = r0->field_87
    //     0xaea070: ldur            w1, [x0, #0x87]
    // 0xaea074: DecompressPointer r1
    //     0xaea074: add             x1, x1, HEAP, lsl #32
    // 0xaea078: LoadField: r0 = r1->field_2b
    //     0xaea078: ldur            w0, [x1, #0x2b]
    // 0xaea07c: DecompressPointer r0
    //     0xaea07c: add             x0, x0, HEAP, lsl #32
    // 0xaea080: r16 = 16.000000
    //     0xaea080: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaea084: ldr             x16, [x16, #0x188]
    // 0xaea088: r30 = Instance_Color
    //     0xaea088: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaea08c: stp             lr, x16, [SP]
    // 0xaea090: mov             x1, x0
    // 0xaea094: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaea094: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaea098: ldr             x4, [x4, #0xaa0]
    // 0xaea09c: r0 = copyWith()
    //     0xaea09c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaea0a0: stur            x0, [fp, #-0x58]
    // 0xaea0a4: r0 = Text()
    //     0xaea0a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaea0a8: mov             x1, x0
    // 0xaea0ac: ldur            x0, [fp, #-0x48]
    // 0xaea0b0: stur            x1, [fp, #-0x68]
    // 0xaea0b4: StoreField: r1->field_b = r0
    //     0xaea0b4: stur            w0, [x1, #0xb]
    // 0xaea0b8: ldur            x0, [fp, #-0x58]
    // 0xaea0bc: StoreField: r1->field_13 = r0
    //     0xaea0bc: stur            w0, [x1, #0x13]
    // 0xaea0c0: r0 = Center()
    //     0xaea0c0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaea0c4: mov             x1, x0
    // 0xaea0c8: r0 = Instance_Alignment
    //     0xaea0c8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaea0cc: ldr             x0, [x0, #0xb10]
    // 0xaea0d0: stur            x1, [fp, #-0x48]
    // 0xaea0d4: StoreField: r1->field_f = r0
    //     0xaea0d4: stur            w0, [x1, #0xf]
    // 0xaea0d8: ldur            x0, [fp, #-0x68]
    // 0xaea0dc: StoreField: r1->field_b = r0
    //     0xaea0dc: stur            w0, [x1, #0xb]
    // 0xaea0e0: r0 = Container()
    //     0xaea0e0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaea0e4: stur            x0, [fp, #-0x58]
    // 0xaea0e8: r16 = 40.000000
    //     0xaea0e8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xaea0ec: ldr             x16, [x16, #8]
    // 0xaea0f0: r30 = 110.000000
    //     0xaea0f0: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xaea0f4: ldr             lr, [lr, #0x770]
    // 0xaea0f8: stp             lr, x16, [SP, #0x10]
    // 0xaea0fc: ldur            x16, [fp, #-0x50]
    // 0xaea100: ldur            lr, [fp, #-0x48]
    // 0xaea104: stp             lr, x16, [SP]
    // 0xaea108: mov             x1, x0
    // 0xaea10c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xaea10c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xaea110: ldr             x4, [x4, #0x8c0]
    // 0xaea114: r0 = Container()
    //     0xaea114: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaea118: r0 = Padding()
    //     0xaea118: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaea11c: mov             x1, x0
    // 0xaea120: r0 = Instance_EdgeInsets
    //     0xaea120: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xaea124: ldr             x0, [x0, #0x778]
    // 0xaea128: stur            x1, [fp, #-0x48]
    // 0xaea12c: StoreField: r1->field_f = r0
    //     0xaea12c: stur            w0, [x1, #0xf]
    // 0xaea130: ldur            x0, [fp, #-0x58]
    // 0xaea134: StoreField: r1->field_b = r0
    //     0xaea134: stur            w0, [x1, #0xb]
    // 0xaea138: r0 = Visibility()
    //     0xaea138: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xaea13c: mov             x1, x0
    // 0xaea140: ldur            x0, [fp, #-0x48]
    // 0xaea144: stur            x1, [fp, #-0x50]
    // 0xaea148: StoreField: r1->field_b = r0
    //     0xaea148: stur            w0, [x1, #0xb]
    // 0xaea14c: r0 = Instance_SizedBox
    //     0xaea14c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaea150: StoreField: r1->field_f = r0
    //     0xaea150: stur            w0, [x1, #0xf]
    // 0xaea154: ldur            x2, [fp, #-0x40]
    // 0xaea158: StoreField: r1->field_13 = r2
    //     0xaea158: stur            w2, [x1, #0x13]
    // 0xaea15c: r2 = false
    //     0xaea15c: add             x2, NULL, #0x30  ; false
    // 0xaea160: ArrayStore: r1[0] = r2  ; List_4
    //     0xaea160: stur            w2, [x1, #0x17]
    // 0xaea164: StoreField: r1->field_1b = r2
    //     0xaea164: stur            w2, [x1, #0x1b]
    // 0xaea168: StoreField: r1->field_1f = r2
    //     0xaea168: stur            w2, [x1, #0x1f]
    // 0xaea16c: StoreField: r1->field_23 = r2
    //     0xaea16c: stur            w2, [x1, #0x23]
    // 0xaea170: StoreField: r1->field_27 = r2
    //     0xaea170: stur            w2, [x1, #0x27]
    // 0xaea174: StoreField: r1->field_2b = r2
    //     0xaea174: stur            w2, [x1, #0x2b]
    // 0xaea178: r0 = InkWell()
    //     0xaea178: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaea17c: mov             x3, x0
    // 0xaea180: ldur            x0, [fp, #-0x50]
    // 0xaea184: stur            x3, [fp, #-0x40]
    // 0xaea188: StoreField: r3->field_b = r0
    //     0xaea188: stur            w0, [x3, #0xb]
    // 0xaea18c: ldur            x2, [fp, #-0x20]
    // 0xaea190: r1 = Function '<anonymous closure>':.
    //     0xaea190: add             x1, PP, #0x58, lsl #12  ; [pp+0x586f8] AnonymousClosure: (0xaeada4), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xaea194: ldr             x1, [x1, #0x6f8]
    // 0xaea198: r0 = AllocateClosure()
    //     0xaea198: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaea19c: mov             x1, x0
    // 0xaea1a0: ldur            x0, [fp, #-0x40]
    // 0xaea1a4: StoreField: r0->field_f = r1
    //     0xaea1a4: stur            w1, [x0, #0xf]
    // 0xaea1a8: r1 = true
    //     0xaea1a8: add             x1, NULL, #0x20  ; true
    // 0xaea1ac: StoreField: r0->field_43 = r1
    //     0xaea1ac: stur            w1, [x0, #0x43]
    // 0xaea1b0: r2 = Instance_BoxShape
    //     0xaea1b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaea1b4: ldr             x2, [x2, #0x80]
    // 0xaea1b8: StoreField: r0->field_47 = r2
    //     0xaea1b8: stur            w2, [x0, #0x47]
    // 0xaea1bc: StoreField: r0->field_6f = r1
    //     0xaea1bc: stur            w1, [x0, #0x6f]
    // 0xaea1c0: r3 = false
    //     0xaea1c0: add             x3, NULL, #0x30  ; false
    // 0xaea1c4: StoreField: r0->field_73 = r3
    //     0xaea1c4: stur            w3, [x0, #0x73]
    // 0xaea1c8: StoreField: r0->field_83 = r1
    //     0xaea1c8: stur            w1, [x0, #0x83]
    // 0xaea1cc: StoreField: r0->field_7b = r3
    //     0xaea1cc: stur            w3, [x0, #0x7b]
    // 0xaea1d0: ldur            x4, [fp, #-0x38]
    // 0xaea1d4: LoadField: r1 = r4->field_b
    //     0xaea1d4: ldur            w1, [x4, #0xb]
    // 0xaea1d8: LoadField: r5 = r4->field_f
    //     0xaea1d8: ldur            w5, [x4, #0xf]
    // 0xaea1dc: DecompressPointer r5
    //     0xaea1dc: add             x5, x5, HEAP, lsl #32
    // 0xaea1e0: LoadField: r6 = r5->field_b
    //     0xaea1e0: ldur            w6, [x5, #0xb]
    // 0xaea1e4: r5 = LoadInt32Instr(r1)
    //     0xaea1e4: sbfx            x5, x1, #1, #0x1f
    // 0xaea1e8: stur            x5, [fp, #-0x60]
    // 0xaea1ec: r1 = LoadInt32Instr(r6)
    //     0xaea1ec: sbfx            x1, x6, #1, #0x1f
    // 0xaea1f0: cmp             x5, x1
    // 0xaea1f4: b.ne            #0xaea200
    // 0xaea1f8: mov             x1, x4
    // 0xaea1fc: r0 = _growToNextCapacity()
    //     0xaea1fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaea200: ldur            x2, [fp, #-0x38]
    // 0xaea204: ldur            x3, [fp, #-0x60]
    // 0xaea208: add             x0, x3, #1
    // 0xaea20c: lsl             x1, x0, #1
    // 0xaea210: StoreField: r2->field_b = r1
    //     0xaea210: stur            w1, [x2, #0xb]
    // 0xaea214: LoadField: r1 = r2->field_f
    //     0xaea214: ldur            w1, [x2, #0xf]
    // 0xaea218: DecompressPointer r1
    //     0xaea218: add             x1, x1, HEAP, lsl #32
    // 0xaea21c: ldur            x0, [fp, #-0x40]
    // 0xaea220: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaea220: add             x25, x1, x3, lsl #2
    //     0xaea224: add             x25, x25, #0xf
    //     0xaea228: str             w0, [x25]
    //     0xaea22c: tbz             w0, #0, #0xaea248
    //     0xaea230: ldurb           w16, [x1, #-1]
    //     0xaea234: ldurb           w17, [x0, #-1]
    //     0xaea238: and             x16, x17, x16, lsr #2
    //     0xaea23c: tst             x16, HEAP, lsr #32
    //     0xaea240: b.eq            #0xaea248
    //     0xaea244: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaea248: mov             x3, x2
    // 0xaea24c: b               #0xaea2a8
    // 0xaea250: mov             x2, x0
    // 0xaea254: b               #0xaea25c
    // 0xaea258: mov             x2, x0
    // 0xaea25c: LoadField: r0 = r1->field_b
    //     0xaea25c: ldur            w0, [x1, #0xb]
    // 0xaea260: r1 = LoadInt32Instr(r0)
    //     0xaea260: sbfx            x1, x0, #1, #0x1f
    // 0xaea264: cmp             x3, x1
    // 0xaea268: b.ne            #0xaea274
    // 0xaea26c: mov             x1, x2
    // 0xaea270: r0 = _growToNextCapacity()
    //     0xaea270: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaea274: ldur            x3, [fp, #-0x38]
    // 0xaea278: ldur            x2, [fp, #-0x70]
    // 0xaea27c: add             x0, x2, #1
    // 0xaea280: lsl             x1, x0, #1
    // 0xaea284: StoreField: r3->field_b = r1
    //     0xaea284: stur            w1, [x3, #0xb]
    // 0xaea288: mov             x1, x2
    // 0xaea28c: cmp             x1, x0
    // 0xaea290: b.hs            #0xaead90
    // 0xaea294: LoadField: r0 = r3->field_f
    //     0xaea294: ldur            w0, [x3, #0xf]
    // 0xaea298: DecompressPointer r0
    //     0xaea298: add             x0, x0, HEAP, lsl #32
    // 0xaea29c: add             x1, x0, x2, lsl #2
    // 0xaea2a0: r16 = Instance_SizedBox
    //     0xaea2a0: ldr             x16, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaea2a4: StoreField: r1->field_f = r16
    //     0xaea2a4: stur            w16, [x1, #0xf]
    // 0xaea2a8: ldur            x0, [fp, #-8]
    // 0xaea2ac: LoadField: r1 = r0->field_b
    //     0xaea2ac: ldur            w1, [x0, #0xb]
    // 0xaea2b0: DecompressPointer r1
    //     0xaea2b0: add             x1, x1, HEAP, lsl #32
    // 0xaea2b4: cmp             w1, NULL
    // 0xaea2b8: b.eq            #0xaead94
    // 0xaea2bc: LoadField: r2 = r1->field_43
    //     0xaea2bc: ldur            w2, [x1, #0x43]
    // 0xaea2c0: DecompressPointer r2
    //     0xaea2c0: add             x2, x2, HEAP, lsl #32
    // 0xaea2c4: stur            x2, [fp, #-0x48]
    // 0xaea2c8: cmp             w2, NULL
    // 0xaea2cc: b.ne            #0xaea2d8
    // 0xaea2d0: r1 = Null
    //     0xaea2d0: mov             x1, NULL
    // 0xaea2d4: b               #0xaea2e0
    // 0xaea2d8: LoadField: r1 = r2->field_f
    //     0xaea2d8: ldur            w1, [x2, #0xf]
    // 0xaea2dc: DecompressPointer r1
    //     0xaea2dc: add             x1, x1, HEAP, lsl #32
    // 0xaea2e0: cmp             w1, NULL
    // 0xaea2e4: r16 = true
    //     0xaea2e4: add             x16, NULL, #0x20  ; true
    // 0xaea2e8: r17 = false
    //     0xaea2e8: add             x17, NULL, #0x30  ; false
    // 0xaea2ec: csel            x4, x16, x17, ne
    // 0xaea2f0: stur            x4, [fp, #-0x40]
    // 0xaea2f4: cmp             w2, NULL
    // 0xaea2f8: b.ne            #0xaea304
    // 0xaea2fc: r1 = Null
    //     0xaea2fc: mov             x1, NULL
    // 0xaea300: b               #0xaea328
    // 0xaea304: LoadField: r1 = r2->field_13
    //     0xaea304: ldur            w1, [x2, #0x13]
    // 0xaea308: DecompressPointer r1
    //     0xaea308: add             x1, x1, HEAP, lsl #32
    // 0xaea30c: cmp             w1, NULL
    // 0xaea310: b.ne            #0xaea31c
    // 0xaea314: r1 = Null
    //     0xaea314: mov             x1, NULL
    // 0xaea318: b               #0xaea328
    // 0xaea31c: LoadField: r5 = r1->field_7
    //     0xaea31c: ldur            w5, [x1, #7]
    // 0xaea320: DecompressPointer r5
    //     0xaea320: add             x5, x5, HEAP, lsl #32
    // 0xaea324: mov             x1, x5
    // 0xaea328: cmp             w1, NULL
    // 0xaea32c: b.ne            #0xaea338
    // 0xaea330: r1 = 0
    //     0xaea330: movz            x1, #0
    // 0xaea334: b               #0xaea348
    // 0xaea338: r5 = LoadInt32Instr(r1)
    //     0xaea338: sbfx            x5, x1, #1, #0x1f
    //     0xaea33c: tbz             w1, #0, #0xaea344
    //     0xaea340: ldur            x5, [x1, #7]
    // 0xaea344: mov             x1, x5
    // 0xaea348: stur            x1, [fp, #-0x78]
    // 0xaea34c: cmp             w2, NULL
    // 0xaea350: b.ne            #0xaea35c
    // 0xaea354: r5 = Null
    //     0xaea354: mov             x5, NULL
    // 0xaea358: b               #0xaea380
    // 0xaea35c: LoadField: r5 = r2->field_13
    //     0xaea35c: ldur            w5, [x2, #0x13]
    // 0xaea360: DecompressPointer r5
    //     0xaea360: add             x5, x5, HEAP, lsl #32
    // 0xaea364: cmp             w5, NULL
    // 0xaea368: b.ne            #0xaea374
    // 0xaea36c: r5 = Null
    //     0xaea36c: mov             x5, NULL
    // 0xaea370: b               #0xaea380
    // 0xaea374: LoadField: r6 = r5->field_b
    //     0xaea374: ldur            w6, [x5, #0xb]
    // 0xaea378: DecompressPointer r6
    //     0xaea378: add             x6, x6, HEAP, lsl #32
    // 0xaea37c: mov             x5, x6
    // 0xaea380: cmp             w5, NULL
    // 0xaea384: b.ne            #0xaea390
    // 0xaea388: r5 = 0
    //     0xaea388: movz            x5, #0
    // 0xaea38c: b               #0xaea3a0
    // 0xaea390: r6 = LoadInt32Instr(r5)
    //     0xaea390: sbfx            x6, x5, #1, #0x1f
    //     0xaea394: tbz             w5, #0, #0xaea39c
    //     0xaea398: ldur            x6, [x5, #7]
    // 0xaea39c: mov             x5, x6
    // 0xaea3a0: stur            x5, [fp, #-0x70]
    // 0xaea3a4: cmp             w2, NULL
    // 0xaea3a8: b.ne            #0xaea3b4
    // 0xaea3ac: r6 = Null
    //     0xaea3ac: mov             x6, NULL
    // 0xaea3b0: b               #0xaea3d8
    // 0xaea3b4: LoadField: r6 = r2->field_13
    //     0xaea3b4: ldur            w6, [x2, #0x13]
    // 0xaea3b8: DecompressPointer r6
    //     0xaea3b8: add             x6, x6, HEAP, lsl #32
    // 0xaea3bc: cmp             w6, NULL
    // 0xaea3c0: b.ne            #0xaea3cc
    // 0xaea3c4: r6 = Null
    //     0xaea3c4: mov             x6, NULL
    // 0xaea3c8: b               #0xaea3d8
    // 0xaea3cc: LoadField: r7 = r6->field_f
    //     0xaea3cc: ldur            w7, [x6, #0xf]
    // 0xaea3d0: DecompressPointer r7
    //     0xaea3d0: add             x7, x7, HEAP, lsl #32
    // 0xaea3d4: mov             x6, x7
    // 0xaea3d8: cmp             w6, NULL
    // 0xaea3dc: b.ne            #0xaea3e8
    // 0xaea3e0: r6 = 0
    //     0xaea3e0: movz            x6, #0
    // 0xaea3e4: b               #0xaea3f8
    // 0xaea3e8: r7 = LoadInt32Instr(r6)
    //     0xaea3e8: sbfx            x7, x6, #1, #0x1f
    //     0xaea3ec: tbz             w6, #0, #0xaea3f4
    //     0xaea3f0: ldur            x7, [x6, #7]
    // 0xaea3f4: mov             x6, x7
    // 0xaea3f8: stur            x6, [fp, #-0x60]
    // 0xaea3fc: r0 = Color()
    //     0xaea3fc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaea400: mov             x1, x0
    // 0xaea404: r0 = Instance_ColorSpace
    //     0xaea404: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaea408: stur            x1, [fp, #-0x50]
    // 0xaea40c: StoreField: r1->field_27 = r0
    //     0xaea40c: stur            w0, [x1, #0x27]
    // 0xaea410: d0 = 1.000000
    //     0xaea410: fmov            d0, #1.00000000
    // 0xaea414: StoreField: r1->field_7 = d0
    //     0xaea414: stur            d0, [x1, #7]
    // 0xaea418: ldur            x2, [fp, #-0x78]
    // 0xaea41c: ubfx            x2, x2, #0, #0x20
    // 0xaea420: and             w3, w2, #0xff
    // 0xaea424: ubfx            x3, x3, #0, #0x20
    // 0xaea428: scvtf           d0, x3
    // 0xaea42c: d1 = 255.000000
    //     0xaea42c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaea430: fdiv            d2, d0, d1
    // 0xaea434: StoreField: r1->field_f = d2
    //     0xaea434: stur            d2, [x1, #0xf]
    // 0xaea438: ldur            x2, [fp, #-0x70]
    // 0xaea43c: ubfx            x2, x2, #0, #0x20
    // 0xaea440: and             w3, w2, #0xff
    // 0xaea444: ubfx            x3, x3, #0, #0x20
    // 0xaea448: scvtf           d0, x3
    // 0xaea44c: fdiv            d2, d0, d1
    // 0xaea450: ArrayStore: r1[0] = d2  ; List_8
    //     0xaea450: stur            d2, [x1, #0x17]
    // 0xaea454: ldur            x2, [fp, #-0x60]
    // 0xaea458: ubfx            x2, x2, #0, #0x20
    // 0xaea45c: and             w3, w2, #0xff
    // 0xaea460: ubfx            x3, x3, #0, #0x20
    // 0xaea464: scvtf           d0, x3
    // 0xaea468: fdiv            d2, d0, d1
    // 0xaea46c: StoreField: r1->field_1f = d2
    //     0xaea46c: stur            d2, [x1, #0x1f]
    // 0xaea470: ldur            x2, [fp, #-0x48]
    // 0xaea474: cmp             w2, NULL
    // 0xaea478: b.ne            #0xaea484
    // 0xaea47c: r3 = Null
    //     0xaea47c: mov             x3, NULL
    // 0xaea480: b               #0xaea4a8
    // 0xaea484: LoadField: r3 = r2->field_13
    //     0xaea484: ldur            w3, [x2, #0x13]
    // 0xaea488: DecompressPointer r3
    //     0xaea488: add             x3, x3, HEAP, lsl #32
    // 0xaea48c: cmp             w3, NULL
    // 0xaea490: b.ne            #0xaea49c
    // 0xaea494: r3 = Null
    //     0xaea494: mov             x3, NULL
    // 0xaea498: b               #0xaea4a8
    // 0xaea49c: LoadField: r4 = r3->field_7
    //     0xaea49c: ldur            w4, [x3, #7]
    // 0xaea4a0: DecompressPointer r4
    //     0xaea4a0: add             x4, x4, HEAP, lsl #32
    // 0xaea4a4: mov             x3, x4
    // 0xaea4a8: cmp             w3, NULL
    // 0xaea4ac: b.ne            #0xaea4b8
    // 0xaea4b0: r3 = 0
    //     0xaea4b0: movz            x3, #0
    // 0xaea4b4: b               #0xaea4c8
    // 0xaea4b8: r4 = LoadInt32Instr(r3)
    //     0xaea4b8: sbfx            x4, x3, #1, #0x1f
    //     0xaea4bc: tbz             w3, #0, #0xaea4c4
    //     0xaea4c0: ldur            x4, [x3, #7]
    // 0xaea4c4: mov             x3, x4
    // 0xaea4c8: stur            x3, [fp, #-0x78]
    // 0xaea4cc: cmp             w2, NULL
    // 0xaea4d0: b.ne            #0xaea4dc
    // 0xaea4d4: r4 = Null
    //     0xaea4d4: mov             x4, NULL
    // 0xaea4d8: b               #0xaea500
    // 0xaea4dc: LoadField: r4 = r2->field_13
    //     0xaea4dc: ldur            w4, [x2, #0x13]
    // 0xaea4e0: DecompressPointer r4
    //     0xaea4e0: add             x4, x4, HEAP, lsl #32
    // 0xaea4e4: cmp             w4, NULL
    // 0xaea4e8: b.ne            #0xaea4f4
    // 0xaea4ec: r4 = Null
    //     0xaea4ec: mov             x4, NULL
    // 0xaea4f0: b               #0xaea500
    // 0xaea4f4: LoadField: r5 = r4->field_b
    //     0xaea4f4: ldur            w5, [x4, #0xb]
    // 0xaea4f8: DecompressPointer r5
    //     0xaea4f8: add             x5, x5, HEAP, lsl #32
    // 0xaea4fc: mov             x4, x5
    // 0xaea500: cmp             w4, NULL
    // 0xaea504: b.ne            #0xaea510
    // 0xaea508: r4 = 0
    //     0xaea508: movz            x4, #0
    // 0xaea50c: b               #0xaea520
    // 0xaea510: r5 = LoadInt32Instr(r4)
    //     0xaea510: sbfx            x5, x4, #1, #0x1f
    //     0xaea514: tbz             w4, #0, #0xaea51c
    //     0xaea518: ldur            x5, [x4, #7]
    // 0xaea51c: mov             x4, x5
    // 0xaea520: stur            x4, [fp, #-0x70]
    // 0xaea524: cmp             w2, NULL
    // 0xaea528: b.ne            #0xaea534
    // 0xaea52c: r2 = Null
    //     0xaea52c: mov             x2, NULL
    // 0xaea530: b               #0xaea554
    // 0xaea534: LoadField: r5 = r2->field_13
    //     0xaea534: ldur            w5, [x2, #0x13]
    // 0xaea538: DecompressPointer r5
    //     0xaea538: add             x5, x5, HEAP, lsl #32
    // 0xaea53c: cmp             w5, NULL
    // 0xaea540: b.ne            #0xaea54c
    // 0xaea544: r2 = Null
    //     0xaea544: mov             x2, NULL
    // 0xaea548: b               #0xaea554
    // 0xaea54c: LoadField: r2 = r5->field_f
    //     0xaea54c: ldur            w2, [x5, #0xf]
    // 0xaea550: DecompressPointer r2
    //     0xaea550: add             x2, x2, HEAP, lsl #32
    // 0xaea554: cmp             w2, NULL
    // 0xaea558: b.ne            #0xaea564
    // 0xaea55c: r5 = 0
    //     0xaea55c: movz            x5, #0
    // 0xaea560: b               #0xaea570
    // 0xaea564: r5 = LoadInt32Instr(r2)
    //     0xaea564: sbfx            x5, x2, #1, #0x1f
    //     0xaea568: tbz             w2, #0, #0xaea570
    //     0xaea56c: ldur            x5, [x2, #7]
    // 0xaea570: ldur            x2, [fp, #-8]
    // 0xaea574: stur            x5, [fp, #-0x60]
    // 0xaea578: r0 = Color()
    //     0xaea578: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xaea57c: mov             x3, x0
    // 0xaea580: r0 = Instance_ColorSpace
    //     0xaea580: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xaea584: stur            x3, [fp, #-0x48]
    // 0xaea588: StoreField: r3->field_27 = r0
    //     0xaea588: stur            w0, [x3, #0x27]
    // 0xaea58c: d0 = 0.700000
    //     0xaea58c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaea590: ldr             d0, [x17, #0xf48]
    // 0xaea594: StoreField: r3->field_7 = d0
    //     0xaea594: stur            d0, [x3, #7]
    // 0xaea598: ldur            x0, [fp, #-0x78]
    // 0xaea59c: ubfx            x0, x0, #0, #0x20
    // 0xaea5a0: and             w1, w0, #0xff
    // 0xaea5a4: ubfx            x1, x1, #0, #0x20
    // 0xaea5a8: scvtf           d0, x1
    // 0xaea5ac: d1 = 255.000000
    //     0xaea5ac: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xaea5b0: fdiv            d2, d0, d1
    // 0xaea5b4: StoreField: r3->field_f = d2
    //     0xaea5b4: stur            d2, [x3, #0xf]
    // 0xaea5b8: ldur            x0, [fp, #-0x70]
    // 0xaea5bc: ubfx            x0, x0, #0, #0x20
    // 0xaea5c0: and             w1, w0, #0xff
    // 0xaea5c4: ubfx            x1, x1, #0, #0x20
    // 0xaea5c8: scvtf           d0, x1
    // 0xaea5cc: fdiv            d2, d0, d1
    // 0xaea5d0: ArrayStore: r3[0] = d2  ; List_8
    //     0xaea5d0: stur            d2, [x3, #0x17]
    // 0xaea5d4: ldur            x0, [fp, #-0x60]
    // 0xaea5d8: ubfx            x0, x0, #0, #0x20
    // 0xaea5dc: and             w1, w0, #0xff
    // 0xaea5e0: ubfx            x1, x1, #0, #0x20
    // 0xaea5e4: scvtf           d0, x1
    // 0xaea5e8: fdiv            d2, d0, d1
    // 0xaea5ec: StoreField: r3->field_1f = d2
    //     0xaea5ec: stur            d2, [x3, #0x1f]
    // 0xaea5f0: r1 = Null
    //     0xaea5f0: mov             x1, NULL
    // 0xaea5f4: r2 = 4
    //     0xaea5f4: movz            x2, #0x4
    // 0xaea5f8: r0 = AllocateArray()
    //     0xaea5f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaea5fc: mov             x2, x0
    // 0xaea600: ldur            x0, [fp, #-0x50]
    // 0xaea604: stur            x2, [fp, #-0x58]
    // 0xaea608: StoreField: r2->field_f = r0
    //     0xaea608: stur            w0, [x2, #0xf]
    // 0xaea60c: ldur            x0, [fp, #-0x48]
    // 0xaea610: StoreField: r2->field_13 = r0
    //     0xaea610: stur            w0, [x2, #0x13]
    // 0xaea614: r1 = <Color>
    //     0xaea614: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaea618: ldr             x1, [x1, #0xf80]
    // 0xaea61c: r0 = AllocateGrowableArray()
    //     0xaea61c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaea620: mov             x1, x0
    // 0xaea624: ldur            x0, [fp, #-0x58]
    // 0xaea628: stur            x1, [fp, #-0x48]
    // 0xaea62c: StoreField: r1->field_f = r0
    //     0xaea62c: stur            w0, [x1, #0xf]
    // 0xaea630: r2 = 4
    //     0xaea630: movz            x2, #0x4
    // 0xaea634: StoreField: r1->field_b = r2
    //     0xaea634: stur            w2, [x1, #0xb]
    // 0xaea638: r0 = LinearGradient()
    //     0xaea638: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xaea63c: mov             x1, x0
    // 0xaea640: r0 = Instance_Alignment
    //     0xaea640: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xaea644: ldr             x0, [x0, #0xce0]
    // 0xaea648: stur            x1, [fp, #-0x50]
    // 0xaea64c: StoreField: r1->field_13 = r0
    //     0xaea64c: stur            w0, [x1, #0x13]
    // 0xaea650: r0 = Instance_Alignment
    //     0xaea650: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xaea654: ldr             x0, [x0, #0xce8]
    // 0xaea658: ArrayStore: r1[0] = r0  ; List_4
    //     0xaea658: stur            w0, [x1, #0x17]
    // 0xaea65c: r0 = Instance_TileMode
    //     0xaea65c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xaea660: ldr             x0, [x0, #0xcf0]
    // 0xaea664: StoreField: r1->field_1b = r0
    //     0xaea664: stur            w0, [x1, #0x1b]
    // 0xaea668: ldur            x0, [fp, #-0x48]
    // 0xaea66c: StoreField: r1->field_7 = r0
    //     0xaea66c: stur            w0, [x1, #7]
    // 0xaea670: r0 = BoxDecoration()
    //     0xaea670: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaea674: mov             x2, x0
    // 0xaea678: r0 = Instance_BorderRadius
    //     0xaea678: add             x0, PP, #0x48, lsl #12  ; [pp+0x48b20] Obj!BorderRadius@d5a2e1
    //     0xaea67c: ldr             x0, [x0, #0xb20]
    // 0xaea680: stur            x2, [fp, #-0x48]
    // 0xaea684: StoreField: r2->field_13 = r0
    //     0xaea684: stur            w0, [x2, #0x13]
    // 0xaea688: ldur            x0, [fp, #-0x50]
    // 0xaea68c: StoreField: r2->field_1b = r0
    //     0xaea68c: stur            w0, [x2, #0x1b]
    // 0xaea690: r0 = Instance_BoxShape
    //     0xaea690: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaea694: ldr             x0, [x0, #0x80]
    // 0xaea698: StoreField: r2->field_23 = r0
    //     0xaea698: stur            w0, [x2, #0x23]
    // 0xaea69c: ldur            x1, [fp, #-0x10]
    // 0xaea6a0: r0 = of()
    //     0xaea6a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaea6a4: LoadField: r1 = r0->field_87
    //     0xaea6a4: ldur            w1, [x0, #0x87]
    // 0xaea6a8: DecompressPointer r1
    //     0xaea6a8: add             x1, x1, HEAP, lsl #32
    // 0xaea6ac: LoadField: r0 = r1->field_7
    //     0xaea6ac: ldur            w0, [x1, #7]
    // 0xaea6b0: DecompressPointer r0
    //     0xaea6b0: add             x0, x0, HEAP, lsl #32
    // 0xaea6b4: r16 = 16.000000
    //     0xaea6b4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaea6b8: ldr             x16, [x16, #0x188]
    // 0xaea6bc: r30 = Instance_Color
    //     0xaea6bc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaea6c0: stp             lr, x16, [SP]
    // 0xaea6c4: mov             x1, x0
    // 0xaea6c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaea6c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaea6cc: ldr             x4, [x4, #0xaa0]
    // 0xaea6d0: r0 = copyWith()
    //     0xaea6d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaea6d4: stur            x0, [fp, #-0x50]
    // 0xaea6d8: r0 = TextSpan()
    //     0xaea6d8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaea6dc: mov             x2, x0
    // 0xaea6e0: r0 = "BUMPER OFFER\n"
    //     0xaea6e0: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xaea6e4: ldr             x0, [x0, #0x338]
    // 0xaea6e8: stur            x2, [fp, #-0x58]
    // 0xaea6ec: StoreField: r2->field_b = r0
    //     0xaea6ec: stur            w0, [x2, #0xb]
    // 0xaea6f0: r0 = Instance__DeferringMouseCursor
    //     0xaea6f0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaea6f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xaea6f4: stur            w0, [x2, #0x17]
    // 0xaea6f8: ldur            x1, [fp, #-0x50]
    // 0xaea6fc: StoreField: r2->field_7 = r1
    //     0xaea6fc: stur            w1, [x2, #7]
    // 0xaea700: ldur            x1, [fp, #-0x10]
    // 0xaea704: r0 = of()
    //     0xaea704: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaea708: LoadField: r1 = r0->field_87
    //     0xaea708: ldur            w1, [x0, #0x87]
    // 0xaea70c: DecompressPointer r1
    //     0xaea70c: add             x1, x1, HEAP, lsl #32
    // 0xaea710: LoadField: r0 = r1->field_2b
    //     0xaea710: ldur            w0, [x1, #0x2b]
    // 0xaea714: DecompressPointer r0
    //     0xaea714: add             x0, x0, HEAP, lsl #32
    // 0xaea718: r16 = 12.000000
    //     0xaea718: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaea71c: ldr             x16, [x16, #0x9e8]
    // 0xaea720: r30 = Instance_Color
    //     0xaea720: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaea724: stp             lr, x16, [SP]
    // 0xaea728: mov             x1, x0
    // 0xaea72c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaea72c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaea730: ldr             x4, [x4, #0xaa0]
    // 0xaea734: r0 = copyWith()
    //     0xaea734: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaea738: stur            x0, [fp, #-0x50]
    // 0xaea73c: r0 = TextSpan()
    //     0xaea73c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaea740: mov             x3, x0
    // 0xaea744: r0 = "Unlocked from your last order"
    //     0xaea744: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xaea748: ldr             x0, [x0, #0x340]
    // 0xaea74c: stur            x3, [fp, #-0x68]
    // 0xaea750: StoreField: r3->field_b = r0
    //     0xaea750: stur            w0, [x3, #0xb]
    // 0xaea754: r0 = Instance__DeferringMouseCursor
    //     0xaea754: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaea758: ArrayStore: r3[0] = r0  ; List_4
    //     0xaea758: stur            w0, [x3, #0x17]
    // 0xaea75c: ldur            x1, [fp, #-0x50]
    // 0xaea760: StoreField: r3->field_7 = r1
    //     0xaea760: stur            w1, [x3, #7]
    // 0xaea764: r1 = Null
    //     0xaea764: mov             x1, NULL
    // 0xaea768: r2 = 4
    //     0xaea768: movz            x2, #0x4
    // 0xaea76c: r0 = AllocateArray()
    //     0xaea76c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaea770: mov             x2, x0
    // 0xaea774: ldur            x0, [fp, #-0x58]
    // 0xaea778: stur            x2, [fp, #-0x50]
    // 0xaea77c: StoreField: r2->field_f = r0
    //     0xaea77c: stur            w0, [x2, #0xf]
    // 0xaea780: ldur            x0, [fp, #-0x68]
    // 0xaea784: StoreField: r2->field_13 = r0
    //     0xaea784: stur            w0, [x2, #0x13]
    // 0xaea788: r1 = <InlineSpan>
    //     0xaea788: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xaea78c: ldr             x1, [x1, #0xe40]
    // 0xaea790: r0 = AllocateGrowableArray()
    //     0xaea790: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaea794: mov             x1, x0
    // 0xaea798: ldur            x0, [fp, #-0x50]
    // 0xaea79c: stur            x1, [fp, #-0x58]
    // 0xaea7a0: StoreField: r1->field_f = r0
    //     0xaea7a0: stur            w0, [x1, #0xf]
    // 0xaea7a4: r2 = 4
    //     0xaea7a4: movz            x2, #0x4
    // 0xaea7a8: StoreField: r1->field_b = r2
    //     0xaea7a8: stur            w2, [x1, #0xb]
    // 0xaea7ac: r0 = TextSpan()
    //     0xaea7ac: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaea7b0: mov             x1, x0
    // 0xaea7b4: ldur            x0, [fp, #-0x58]
    // 0xaea7b8: stur            x1, [fp, #-0x50]
    // 0xaea7bc: StoreField: r1->field_f = r0
    //     0xaea7bc: stur            w0, [x1, #0xf]
    // 0xaea7c0: r0 = Instance__DeferringMouseCursor
    //     0xaea7c0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaea7c4: ArrayStore: r1[0] = r0  ; List_4
    //     0xaea7c4: stur            w0, [x1, #0x17]
    // 0xaea7c8: r0 = RichText()
    //     0xaea7c8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xaea7cc: mov             x1, x0
    // 0xaea7d0: ldur            x2, [fp, #-0x50]
    // 0xaea7d4: stur            x0, [fp, #-0x50]
    // 0xaea7d8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaea7d8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaea7dc: r0 = RichText()
    //     0xaea7dc: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xaea7e0: ldur            x0, [fp, #-8]
    // 0xaea7e4: LoadField: r1 = r0->field_b
    //     0xaea7e4: ldur            w1, [x0, #0xb]
    // 0xaea7e8: DecompressPointer r1
    //     0xaea7e8: add             x1, x1, HEAP, lsl #32
    // 0xaea7ec: cmp             w1, NULL
    // 0xaea7f0: b.eq            #0xaead98
    // 0xaea7f4: LoadField: r2 = r1->field_43
    //     0xaea7f4: ldur            w2, [x1, #0x43]
    // 0xaea7f8: DecompressPointer r2
    //     0xaea7f8: add             x2, x2, HEAP, lsl #32
    // 0xaea7fc: cmp             w2, NULL
    // 0xaea800: b.ne            #0xaea80c
    // 0xaea804: r6 = Null
    //     0xaea804: mov             x6, NULL
    // 0xaea808: b               #0xaea818
    // 0xaea80c: LoadField: r1 = r2->field_7
    //     0xaea80c: ldur            w1, [x2, #7]
    // 0xaea810: DecompressPointer r1
    //     0xaea810: add             x1, x1, HEAP, lsl #32
    // 0xaea814: mov             x6, x1
    // 0xaea818: ldur            x4, [fp, #-0x38]
    // 0xaea81c: ldur            x5, [fp, #-0x40]
    // 0xaea820: ldur            x3, [fp, #-0x50]
    // 0xaea824: stur            x6, [fp, #-0x58]
    // 0xaea828: r1 = Null
    //     0xaea828: mov             x1, NULL
    // 0xaea82c: r2 = 4
    //     0xaea82c: movz            x2, #0x4
    // 0xaea830: r0 = AllocateArray()
    //     0xaea830: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaea834: mov             x1, x0
    // 0xaea838: ldur            x0, [fp, #-0x58]
    // 0xaea83c: StoreField: r1->field_f = r0
    //     0xaea83c: stur            w0, [x1, #0xf]
    // 0xaea840: r16 = "\n"
    //     0xaea840: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xaea844: StoreField: r1->field_13 = r16
    //     0xaea844: stur            w16, [x1, #0x13]
    // 0xaea848: str             x1, [SP]
    // 0xaea84c: r0 = _interpolate()
    //     0xaea84c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaea850: ldur            x1, [fp, #-0x10]
    // 0xaea854: stur            x0, [fp, #-0x58]
    // 0xaea858: r0 = of()
    //     0xaea858: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaea85c: LoadField: r1 = r0->field_87
    //     0xaea85c: ldur            w1, [x0, #0x87]
    // 0xaea860: DecompressPointer r1
    //     0xaea860: add             x1, x1, HEAP, lsl #32
    // 0xaea864: LoadField: r0 = r1->field_7
    //     0xaea864: ldur            w0, [x1, #7]
    // 0xaea868: DecompressPointer r0
    //     0xaea868: add             x0, x0, HEAP, lsl #32
    // 0xaea86c: r16 = 32.000000
    //     0xaea86c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xaea870: ldr             x16, [x16, #0x848]
    // 0xaea874: r30 = Instance_Color
    //     0xaea874: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaea878: stp             lr, x16, [SP]
    // 0xaea87c: mov             x1, x0
    // 0xaea880: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaea880: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaea884: ldr             x4, [x4, #0xaa0]
    // 0xaea888: r0 = copyWith()
    //     0xaea888: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaea88c: ldur            x1, [fp, #-0x10]
    // 0xaea890: stur            x0, [fp, #-0x10]
    // 0xaea894: r0 = of()
    //     0xaea894: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaea898: LoadField: r1 = r0->field_87
    //     0xaea898: ldur            w1, [x0, #0x87]
    // 0xaea89c: DecompressPointer r1
    //     0xaea89c: add             x1, x1, HEAP, lsl #32
    // 0xaea8a0: LoadField: r0 = r1->field_2b
    //     0xaea8a0: ldur            w0, [x1, #0x2b]
    // 0xaea8a4: DecompressPointer r0
    //     0xaea8a4: add             x0, x0, HEAP, lsl #32
    // 0xaea8a8: r16 = Instance_Color
    //     0xaea8a8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaea8ac: r30 = 16.000000
    //     0xaea8ac: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaea8b0: ldr             lr, [lr, #0x188]
    // 0xaea8b4: stp             lr, x16, [SP]
    // 0xaea8b8: mov             x1, x0
    // 0xaea8bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaea8bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaea8c0: ldr             x4, [x4, #0x9b8]
    // 0xaea8c4: r0 = copyWith()
    //     0xaea8c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaea8c8: stur            x0, [fp, #-0x68]
    // 0xaea8cc: r0 = TextSpan()
    //     0xaea8cc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaea8d0: mov             x3, x0
    // 0xaea8d4: r0 = "OFF"
    //     0xaea8d4: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xaea8d8: ldr             x0, [x0, #0x348]
    // 0xaea8dc: stur            x3, [fp, #-0x80]
    // 0xaea8e0: StoreField: r3->field_b = r0
    //     0xaea8e0: stur            w0, [x3, #0xb]
    // 0xaea8e4: r0 = Instance__DeferringMouseCursor
    //     0xaea8e4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaea8e8: ArrayStore: r3[0] = r0  ; List_4
    //     0xaea8e8: stur            w0, [x3, #0x17]
    // 0xaea8ec: ldur            x1, [fp, #-0x68]
    // 0xaea8f0: StoreField: r3->field_7 = r1
    //     0xaea8f0: stur            w1, [x3, #7]
    // 0xaea8f4: r1 = Null
    //     0xaea8f4: mov             x1, NULL
    // 0xaea8f8: r2 = 2
    //     0xaea8f8: movz            x2, #0x2
    // 0xaea8fc: r0 = AllocateArray()
    //     0xaea8fc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaea900: mov             x2, x0
    // 0xaea904: ldur            x0, [fp, #-0x80]
    // 0xaea908: stur            x2, [fp, #-0x68]
    // 0xaea90c: StoreField: r2->field_f = r0
    //     0xaea90c: stur            w0, [x2, #0xf]
    // 0xaea910: r1 = <InlineSpan>
    //     0xaea910: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xaea914: ldr             x1, [x1, #0xe40]
    // 0xaea918: r0 = AllocateGrowableArray()
    //     0xaea918: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaea91c: mov             x1, x0
    // 0xaea920: ldur            x0, [fp, #-0x68]
    // 0xaea924: stur            x1, [fp, #-0x80]
    // 0xaea928: StoreField: r1->field_f = r0
    //     0xaea928: stur            w0, [x1, #0xf]
    // 0xaea92c: r0 = 2
    //     0xaea92c: movz            x0, #0x2
    // 0xaea930: StoreField: r1->field_b = r0
    //     0xaea930: stur            w0, [x1, #0xb]
    // 0xaea934: r0 = TextSpan()
    //     0xaea934: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xaea938: mov             x1, x0
    // 0xaea93c: ldur            x0, [fp, #-0x58]
    // 0xaea940: stur            x1, [fp, #-0x68]
    // 0xaea944: StoreField: r1->field_b = r0
    //     0xaea944: stur            w0, [x1, #0xb]
    // 0xaea948: ldur            x0, [fp, #-0x80]
    // 0xaea94c: StoreField: r1->field_f = r0
    //     0xaea94c: stur            w0, [x1, #0xf]
    // 0xaea950: r0 = Instance__DeferringMouseCursor
    //     0xaea950: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xaea954: ArrayStore: r1[0] = r0  ; List_4
    //     0xaea954: stur            w0, [x1, #0x17]
    // 0xaea958: ldur            x0, [fp, #-0x10]
    // 0xaea95c: StoreField: r1->field_7 = r0
    //     0xaea95c: stur            w0, [x1, #7]
    // 0xaea960: r0 = RichText()
    //     0xaea960: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xaea964: stur            x0, [fp, #-0x10]
    // 0xaea968: r16 = Instance_TextAlign
    //     0xaea968: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaea96c: str             x16, [SP]
    // 0xaea970: mov             x1, x0
    // 0xaea974: ldur            x2, [fp, #-0x68]
    // 0xaea978: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xaea978: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xaea97c: ldr             x4, [x4, #0x350]
    // 0xaea980: r0 = RichText()
    //     0xaea980: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xaea984: r1 = Null
    //     0xaea984: mov             x1, NULL
    // 0xaea988: r2 = 6
    //     0xaea988: movz            x2, #0x6
    // 0xaea98c: r0 = AllocateArray()
    //     0xaea98c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaea990: mov             x2, x0
    // 0xaea994: ldur            x0, [fp, #-0x50]
    // 0xaea998: stur            x2, [fp, #-0x58]
    // 0xaea99c: StoreField: r2->field_f = r0
    //     0xaea99c: stur            w0, [x2, #0xf]
    // 0xaea9a0: r16 = Instance_VerticalDivider
    //     0xaea9a0: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0xaea9a4: ldr             x16, [x16, #0x760]
    // 0xaea9a8: StoreField: r2->field_13 = r16
    //     0xaea9a8: stur            w16, [x2, #0x13]
    // 0xaea9ac: ldur            x0, [fp, #-0x10]
    // 0xaea9b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xaea9b0: stur            w0, [x2, #0x17]
    // 0xaea9b4: r1 = <Widget>
    //     0xaea9b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaea9b8: r0 = AllocateGrowableArray()
    //     0xaea9b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaea9bc: mov             x1, x0
    // 0xaea9c0: ldur            x0, [fp, #-0x58]
    // 0xaea9c4: stur            x1, [fp, #-0x10]
    // 0xaea9c8: StoreField: r1->field_f = r0
    //     0xaea9c8: stur            w0, [x1, #0xf]
    // 0xaea9cc: r0 = 6
    //     0xaea9cc: movz            x0, #0x6
    // 0xaea9d0: StoreField: r1->field_b = r0
    //     0xaea9d0: stur            w0, [x1, #0xb]
    // 0xaea9d4: r0 = Row()
    //     0xaea9d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaea9d8: mov             x1, x0
    // 0xaea9dc: r0 = Instance_Axis
    //     0xaea9dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaea9e0: stur            x1, [fp, #-0x50]
    // 0xaea9e4: StoreField: r1->field_f = r0
    //     0xaea9e4: stur            w0, [x1, #0xf]
    // 0xaea9e8: r0 = Instance_MainAxisAlignment
    //     0xaea9e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xaea9ec: ldr             x0, [x0, #0xa8]
    // 0xaea9f0: StoreField: r1->field_13 = r0
    //     0xaea9f0: stur            w0, [x1, #0x13]
    // 0xaea9f4: r0 = Instance_MainAxisSize
    //     0xaea9f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaea9f8: ldr             x0, [x0, #0xa10]
    // 0xaea9fc: ArrayStore: r1[0] = r0  ; List_4
    //     0xaea9fc: stur            w0, [x1, #0x17]
    // 0xaeaa00: r2 = Instance_CrossAxisAlignment
    //     0xaeaa00: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaeaa04: ldr             x2, [x2, #0xa18]
    // 0xaeaa08: StoreField: r1->field_1b = r2
    //     0xaeaa08: stur            w2, [x1, #0x1b]
    // 0xaeaa0c: r2 = Instance_VerticalDirection
    //     0xaeaa0c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaeaa10: ldr             x2, [x2, #0xa20]
    // 0xaeaa14: StoreField: r1->field_23 = r2
    //     0xaeaa14: stur            w2, [x1, #0x23]
    // 0xaeaa18: r3 = Instance_Clip
    //     0xaeaa18: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaeaa1c: ldr             x3, [x3, #0x38]
    // 0xaeaa20: StoreField: r1->field_2b = r3
    //     0xaeaa20: stur            w3, [x1, #0x2b]
    // 0xaeaa24: StoreField: r1->field_2f = rZR
    //     0xaeaa24: stur            xzr, [x1, #0x2f]
    // 0xaeaa28: ldur            x4, [fp, #-0x10]
    // 0xaeaa2c: StoreField: r1->field_b = r4
    //     0xaeaa2c: stur            w4, [x1, #0xb]
    // 0xaeaa30: r0 = Padding()
    //     0xaeaa30: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaeaa34: mov             x1, x0
    // 0xaeaa38: r0 = Instance_EdgeInsets
    //     0xaeaa38: add             x0, PP, #0x48, lsl #12  ; [pp+0x48358] Obj!EdgeInsets@d57411
    //     0xaeaa3c: ldr             x0, [x0, #0x358]
    // 0xaeaa40: stur            x1, [fp, #-0x10]
    // 0xaeaa44: StoreField: r1->field_f = r0
    //     0xaeaa44: stur            w0, [x1, #0xf]
    // 0xaeaa48: ldur            x0, [fp, #-0x50]
    // 0xaeaa4c: StoreField: r1->field_b = r0
    //     0xaeaa4c: stur            w0, [x1, #0xb]
    // 0xaeaa50: r0 = IntrinsicHeight()
    //     0xaeaa50: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xaeaa54: mov             x1, x0
    // 0xaeaa58: ldur            x0, [fp, #-0x10]
    // 0xaeaa5c: stur            x1, [fp, #-0x50]
    // 0xaeaa60: StoreField: r1->field_b = r0
    //     0xaeaa60: stur            w0, [x1, #0xb]
    // 0xaeaa64: r0 = Container()
    //     0xaeaa64: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaeaa68: stur            x0, [fp, #-0x10]
    // 0xaeaa6c: r16 = 100.000000
    //     0xaeaa6c: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xaeaa70: ldur            lr, [fp, #-0x48]
    // 0xaeaa74: stp             lr, x16, [SP, #8]
    // 0xaeaa78: ldur            x16, [fp, #-0x50]
    // 0xaeaa7c: str             x16, [SP]
    // 0xaeaa80: mov             x1, x0
    // 0xaeaa84: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xaeaa84: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xaeaa88: ldr             x4, [x4, #0xc78]
    // 0xaeaa8c: r0 = Container()
    //     0xaeaa8c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaeaa90: r1 = <Path>
    //     0xaeaa90: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xaeaa94: ldr             x1, [x1, #0xd30]
    // 0xaeaa98: r0 = MovieTicketClipper()
    //     0xaeaa98: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xaeaa9c: stur            x0, [fp, #-0x48]
    // 0xaeaaa0: r0 = ClipPath()
    //     0xaeaaa0: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xaeaaa4: mov             x1, x0
    // 0xaeaaa8: ldur            x0, [fp, #-0x48]
    // 0xaeaaac: stur            x1, [fp, #-0x50]
    // 0xaeaab0: StoreField: r1->field_f = r0
    //     0xaeaab0: stur            w0, [x1, #0xf]
    // 0xaeaab4: r0 = Instance_Clip
    //     0xaeaab4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xaeaab8: ldr             x0, [x0, #0x138]
    // 0xaeaabc: StoreField: r1->field_13 = r0
    //     0xaeaabc: stur            w0, [x1, #0x13]
    // 0xaeaac0: ldur            x0, [fp, #-0x10]
    // 0xaeaac4: StoreField: r1->field_b = r0
    //     0xaeaac4: stur            w0, [x1, #0xb]
    // 0xaeaac8: r0 = Visibility()
    //     0xaeaac8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xaeaacc: mov             x2, x0
    // 0xaeaad0: ldur            x0, [fp, #-0x50]
    // 0xaeaad4: stur            x2, [fp, #-0x10]
    // 0xaeaad8: StoreField: r2->field_b = r0
    //     0xaeaad8: stur            w0, [x2, #0xb]
    // 0xaeaadc: r0 = Instance_SizedBox
    //     0xaeaadc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaeaae0: StoreField: r2->field_f = r0
    //     0xaeaae0: stur            w0, [x2, #0xf]
    // 0xaeaae4: ldur            x0, [fp, #-0x40]
    // 0xaeaae8: StoreField: r2->field_13 = r0
    //     0xaeaae8: stur            w0, [x2, #0x13]
    // 0xaeaaec: r0 = false
    //     0xaeaaec: add             x0, NULL, #0x30  ; false
    // 0xaeaaf0: ArrayStore: r2[0] = r0  ; List_4
    //     0xaeaaf0: stur            w0, [x2, #0x17]
    // 0xaeaaf4: StoreField: r2->field_1b = r0
    //     0xaeaaf4: stur            w0, [x2, #0x1b]
    // 0xaeaaf8: StoreField: r2->field_1f = r0
    //     0xaeaaf8: stur            w0, [x2, #0x1f]
    // 0xaeaafc: StoreField: r2->field_23 = r0
    //     0xaeaafc: stur            w0, [x2, #0x23]
    // 0xaeab00: StoreField: r2->field_27 = r0
    //     0xaeab00: stur            w0, [x2, #0x27]
    // 0xaeab04: StoreField: r2->field_2b = r0
    //     0xaeab04: stur            w0, [x2, #0x2b]
    // 0xaeab08: ldur            x0, [fp, #-0x38]
    // 0xaeab0c: LoadField: r1 = r0->field_b
    //     0xaeab0c: ldur            w1, [x0, #0xb]
    // 0xaeab10: LoadField: r3 = r0->field_f
    //     0xaeab10: ldur            w3, [x0, #0xf]
    // 0xaeab14: DecompressPointer r3
    //     0xaeab14: add             x3, x3, HEAP, lsl #32
    // 0xaeab18: LoadField: r4 = r3->field_b
    //     0xaeab18: ldur            w4, [x3, #0xb]
    // 0xaeab1c: r3 = LoadInt32Instr(r1)
    //     0xaeab1c: sbfx            x3, x1, #1, #0x1f
    // 0xaeab20: stur            x3, [fp, #-0x60]
    // 0xaeab24: r1 = LoadInt32Instr(r4)
    //     0xaeab24: sbfx            x1, x4, #1, #0x1f
    // 0xaeab28: cmp             x3, x1
    // 0xaeab2c: b.ne            #0xaeab38
    // 0xaeab30: mov             x1, x0
    // 0xaeab34: r0 = _growToNextCapacity()
    //     0xaeab34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaeab38: ldur            x4, [fp, #-8]
    // 0xaeab3c: ldur            x2, [fp, #-0x38]
    // 0xaeab40: ldur            x3, [fp, #-0x60]
    // 0xaeab44: add             x0, x3, #1
    // 0xaeab48: lsl             x1, x0, #1
    // 0xaeab4c: StoreField: r2->field_b = r1
    //     0xaeab4c: stur            w1, [x2, #0xb]
    // 0xaeab50: LoadField: r1 = r2->field_f
    //     0xaeab50: ldur            w1, [x2, #0xf]
    // 0xaeab54: DecompressPointer r1
    //     0xaeab54: add             x1, x1, HEAP, lsl #32
    // 0xaeab58: ldur            x0, [fp, #-0x10]
    // 0xaeab5c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaeab5c: add             x25, x1, x3, lsl #2
    //     0xaeab60: add             x25, x25, #0xf
    //     0xaeab64: str             w0, [x25]
    //     0xaeab68: tbz             w0, #0, #0xaeab84
    //     0xaeab6c: ldurb           w16, [x1, #-1]
    //     0xaeab70: ldurb           w17, [x0, #-1]
    //     0xaeab74: and             x16, x17, x16, lsr #2
    //     0xaeab78: tst             x16, HEAP, lsr #32
    //     0xaeab7c: b.eq            #0xaeab84
    //     0xaeab80: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaeab84: LoadField: r0 = r4->field_b
    //     0xaeab84: ldur            w0, [x4, #0xb]
    // 0xaeab88: DecompressPointer r0
    //     0xaeab88: add             x0, x0, HEAP, lsl #32
    // 0xaeab8c: cmp             w0, NULL
    // 0xaeab90: b.eq            #0xaead9c
    // 0xaeab94: LoadField: r1 = r0->field_3f
    //     0xaeab94: ldur            w1, [x0, #0x3f]
    // 0xaeab98: DecompressPointer r1
    //     0xaeab98: add             x1, x1, HEAP, lsl #32
    // 0xaeab9c: r0 = LoadClassIdInstr(r1)
    //     0xaeab9c: ldur            x0, [x1, #-1]
    //     0xaeaba0: ubfx            x0, x0, #0xc, #0x14
    // 0xaeaba4: r16 = "search_page"
    //     0xaeaba4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee58] "search_page"
    //     0xaeaba8: ldr             x16, [x16, #0xe58]
    // 0xaeabac: stp             x16, x1, [SP]
    // 0xaeabb0: mov             lr, x0
    // 0xaeabb4: ldr             lr, [x21, lr, lsl #3]
    // 0xaeabb8: blr             lr
    // 0xaeabbc: tbnz            w0, #4, #0xaeabcc
    // 0xaeabc0: d0 = 0.588235
    //     0xaeabc0: add             x17, PP, #0x58, lsl #12  ; [pp+0x58700] IMM: double(0.5882352941176471) from 0x3fe2d2d2d2d2d2d3
    //     0xaeabc4: ldr             d0, [x17, #0x700]
    // 0xaeabc8: b               #0xaeabd4
    // 0xaeabcc: d0 = 0.526316
    //     0xaeabcc: add             x17, PP, #0x58, lsl #12  ; [pp+0x58708] IMM: double(0.5263157894736842) from 0x3fe0d79435e50d79
    //     0xaeabd0: ldr             d0, [x17, #0x708]
    // 0xaeabd4: ldur            x0, [fp, #-8]
    // 0xaeabd8: ldur            x1, [fp, #-0x38]
    // 0xaeabdc: stur            d0, [fp, #-0x88]
    // 0xaeabe0: r0 = SliverGridDelegateWithFixedCrossAxisCount()
    //     0xaeabe0: bl              #0xa4d630  ; AllocateSliverGridDelegateWithFixedCrossAxisCountStub -> SliverGridDelegateWithFixedCrossAxisCount (size=0x2c)
    // 0xaeabe4: mov             x1, x0
    // 0xaeabe8: r0 = 2
    //     0xaeabe8: movz            x0, #0x2
    // 0xaeabec: stur            x1, [fp, #-0x10]
    // 0xaeabf0: StoreField: r1->field_7 = r0
    //     0xaeabf0: stur            x0, [x1, #7]
    // 0xaeabf4: d0 = 24.000000
    //     0xaeabf4: fmov            d0, #24.00000000
    // 0xaeabf8: StoreField: r1->field_f = d0
    //     0xaeabf8: stur            d0, [x1, #0xf]
    // 0xaeabfc: d0 = 8.000000
    //     0xaeabfc: fmov            d0, #8.00000000
    // 0xaeac00: ArrayStore: r1[0] = d0  ; List_8
    //     0xaeac00: stur            d0, [x1, #0x17]
    // 0xaeac04: ldur            d0, [fp, #-0x88]
    // 0xaeac08: StoreField: r1->field_1f = d0
    //     0xaeac08: stur            d0, [x1, #0x1f]
    // 0xaeac0c: ldur            x0, [fp, #-8]
    // 0xaeac10: LoadField: r2 = r0->field_b
    //     0xaeac10: ldur            w2, [x0, #0xb]
    // 0xaeac14: DecompressPointer r2
    //     0xaeac14: add             x2, x2, HEAP, lsl #32
    // 0xaeac18: cmp             w2, NULL
    // 0xaeac1c: b.eq            #0xaeada0
    // 0xaeac20: LoadField: r0 = r2->field_b
    //     0xaeac20: ldur            w0, [x2, #0xb]
    // 0xaeac24: DecompressPointer r0
    //     0xaeac24: add             x0, x0, HEAP, lsl #32
    // 0xaeac28: r2 = LoadClassIdInstr(r0)
    //     0xaeac28: ldur            x2, [x0, #-1]
    //     0xaeac2c: ubfx            x2, x2, #0xc, #0x14
    // 0xaeac30: str             x0, [SP]
    // 0xaeac34: mov             x0, x2
    // 0xaeac38: r0 = GDT[cid_x0 + 0xc898]()
    //     0xaeac38: movz            x17, #0xc898
    //     0xaeac3c: add             lr, x0, x17
    //     0xaeac40: ldr             lr, [x21, lr, lsl #3]
    //     0xaeac44: blr             lr
    // 0xaeac48: ldur            x2, [fp, #-0x20]
    // 0xaeac4c: r1 = Function '<anonymous closure>':.
    //     0xaeac4c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58710] AnonymousClosure: (0xa514bc), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] _ProductGridItemViewState::build (0xae9ae4)
    //     0xaeac50: ldr             x1, [x1, #0x710]
    // 0xaeac54: stur            x0, [fp, #-8]
    // 0xaeac58: r0 = AllocateClosure()
    //     0xaeac58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaeac5c: stur            x0, [fp, #-0x20]
    // 0xaeac60: r0 = GridView()
    //     0xaeac60: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0xaeac64: mov             x1, x0
    // 0xaeac68: ldur            x2, [fp, #-0x10]
    // 0xaeac6c: ldur            x3, [fp, #-0x20]
    // 0xaeac70: ldur            x5, [fp, #-8]
    // 0xaeac74: stur            x0, [fp, #-8]
    // 0xaeac78: r0 = GridView.builder()
    //     0xaeac78: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0xaeac7c: ldur            x0, [fp, #-0x38]
    // 0xaeac80: LoadField: r1 = r0->field_b
    //     0xaeac80: ldur            w1, [x0, #0xb]
    // 0xaeac84: LoadField: r2 = r0->field_f
    //     0xaeac84: ldur            w2, [x0, #0xf]
    // 0xaeac88: DecompressPointer r2
    //     0xaeac88: add             x2, x2, HEAP, lsl #32
    // 0xaeac8c: LoadField: r3 = r2->field_b
    //     0xaeac8c: ldur            w3, [x2, #0xb]
    // 0xaeac90: r2 = LoadInt32Instr(r1)
    //     0xaeac90: sbfx            x2, x1, #1, #0x1f
    // 0xaeac94: stur            x2, [fp, #-0x60]
    // 0xaeac98: r1 = LoadInt32Instr(r3)
    //     0xaeac98: sbfx            x1, x3, #1, #0x1f
    // 0xaeac9c: cmp             x2, x1
    // 0xaeaca0: b.ne            #0xaeacac
    // 0xaeaca4: mov             x1, x0
    // 0xaeaca8: r0 = _growToNextCapacity()
    //     0xaeaca8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaeacac: ldur            x4, [fp, #-0x30]
    // 0xaeacb0: ldur            x5, [fp, #-0x28]
    // 0xaeacb4: ldur            x2, [fp, #-0x38]
    // 0xaeacb8: ldur            x6, [fp, #-0x18]
    // 0xaeacbc: ldur            x3, [fp, #-0x60]
    // 0xaeacc0: add             x0, x3, #1
    // 0xaeacc4: lsl             x1, x0, #1
    // 0xaeacc8: StoreField: r2->field_b = r1
    //     0xaeacc8: stur            w1, [x2, #0xb]
    // 0xaeaccc: LoadField: r1 = r2->field_f
    //     0xaeaccc: ldur            w1, [x2, #0xf]
    // 0xaeacd0: DecompressPointer r1
    //     0xaeacd0: add             x1, x1, HEAP, lsl #32
    // 0xaeacd4: ldur            x0, [fp, #-8]
    // 0xaeacd8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaeacd8: add             x25, x1, x3, lsl #2
    //     0xaeacdc: add             x25, x25, #0xf
    //     0xaeace0: str             w0, [x25]
    //     0xaeace4: tbz             w0, #0, #0xaead00
    //     0xaeace8: ldurb           w16, [x1, #-1]
    //     0xaeacec: ldurb           w17, [x0, #-1]
    //     0xaeacf0: and             x16, x17, x16, lsr #2
    //     0xaeacf4: tst             x16, HEAP, lsr #32
    //     0xaeacf8: b.eq            #0xaead00
    //     0xaeacfc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaead00: r0 = Column()
    //     0xaead00: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaead04: mov             x1, x0
    // 0xaead08: r0 = Instance_Axis
    //     0xaead08: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaead0c: stur            x1, [fp, #-8]
    // 0xaead10: StoreField: r1->field_f = r0
    //     0xaead10: stur            w0, [x1, #0xf]
    // 0xaead14: ldur            x0, [fp, #-0x28]
    // 0xaead18: StoreField: r1->field_13 = r0
    //     0xaead18: stur            w0, [x1, #0x13]
    // 0xaead1c: r0 = Instance_MainAxisSize
    //     0xaead1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaead20: ldr             x0, [x0, #0xa10]
    // 0xaead24: ArrayStore: r1[0] = r0  ; List_4
    //     0xaead24: stur            w0, [x1, #0x17]
    // 0xaead28: ldur            x0, [fp, #-0x18]
    // 0xaead2c: StoreField: r1->field_1b = r0
    //     0xaead2c: stur            w0, [x1, #0x1b]
    // 0xaead30: r0 = Instance_VerticalDirection
    //     0xaead30: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaead34: ldr             x0, [x0, #0xa20]
    // 0xaead38: StoreField: r1->field_23 = r0
    //     0xaead38: stur            w0, [x1, #0x23]
    // 0xaead3c: r0 = Instance_Clip
    //     0xaead3c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaead40: ldr             x0, [x0, #0x38]
    // 0xaead44: StoreField: r1->field_2b = r0
    //     0xaead44: stur            w0, [x1, #0x2b]
    // 0xaead48: StoreField: r1->field_2f = rZR
    //     0xaead48: stur            xzr, [x1, #0x2f]
    // 0xaead4c: ldur            x0, [fp, #-0x38]
    // 0xaead50: StoreField: r1->field_b = r0
    //     0xaead50: stur            w0, [x1, #0xb]
    // 0xaead54: r0 = Padding()
    //     0xaead54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaead58: ldur            x1, [fp, #-0x30]
    // 0xaead5c: StoreField: r0->field_f = r1
    //     0xaead5c: stur            w1, [x0, #0xf]
    // 0xaead60: ldur            x1, [fp, #-8]
    // 0xaead64: StoreField: r0->field_b = r1
    //     0xaead64: stur            w1, [x0, #0xb]
    // 0xaead68: LeaveFrame
    //     0xaead68: mov             SP, fp
    //     0xaead6c: ldp             fp, lr, [SP], #0x10
    // 0xaead70: ret
    //     0xaead70: ret             
    // 0xaead74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaead74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaead78: b               #0xae9b0c
    // 0xaead7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaead80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaead84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaead88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaead8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaead90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaead90: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xaead94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaead98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaead9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaead9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaeada0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaeada0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaeada4, size: 0xc8
    // 0xaeada4: EnterFrame
    //     0xaeada4: stp             fp, lr, [SP, #-0x10]!
    //     0xaeada8: mov             fp, SP
    // 0xaeadac: AllocStack(0x30)
    //     0xaeadac: sub             SP, SP, #0x30
    // 0xaeadb0: SetupParameters()
    //     0xaeadb0: ldr             x0, [fp, #0x10]
    //     0xaeadb4: ldur            w1, [x0, #0x17]
    //     0xaeadb8: add             x1, x1, HEAP, lsl #32
    // 0xaeadbc: CheckStackOverflow
    //     0xaeadbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaeadc0: cmp             SP, x16
    //     0xaeadc4: b.ls            #0xaeae60
    // 0xaeadc8: LoadField: r0 = r1->field_f
    //     0xaeadc8: ldur            w0, [x1, #0xf]
    // 0xaeadcc: DecompressPointer r0
    //     0xaeadcc: add             x0, x0, HEAP, lsl #32
    // 0xaeadd0: LoadField: r1 = r0->field_b
    //     0xaeadd0: ldur            w1, [x0, #0xb]
    // 0xaeadd4: DecompressPointer r1
    //     0xaeadd4: add             x1, x1, HEAP, lsl #32
    // 0xaeadd8: cmp             w1, NULL
    // 0xaeaddc: b.eq            #0xaeae68
    // 0xaeade0: LoadField: r0 = r1->field_2f
    //     0xaeade0: ldur            w0, [x1, #0x2f]
    // 0xaeade4: DecompressPointer r0
    //     0xaeade4: add             x0, x0, HEAP, lsl #32
    // 0xaeade8: LoadField: r2 = r1->field_2b
    //     0xaeade8: ldur            w2, [x1, #0x2b]
    // 0xaeadec: DecompressPointer r2
    //     0xaeadec: add             x2, x2, HEAP, lsl #32
    // 0xaeadf0: LoadField: r3 = r1->field_37
    //     0xaeadf0: ldur            w3, [x1, #0x37]
    // 0xaeadf4: DecompressPointer r3
    //     0xaeadf4: add             x3, x3, HEAP, lsl #32
    // 0xaeadf8: LoadField: r4 = r1->field_33
    //     0xaeadf8: ldur            w4, [x1, #0x33]
    // 0xaeadfc: DecompressPointer r4
    //     0xaeadfc: add             x4, x4, HEAP, lsl #32
    // 0xaeae00: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xaeae00: ldur            w5, [x1, #0x17]
    // 0xaeae04: DecompressPointer r5
    //     0xaeae04: add             x5, x5, HEAP, lsl #32
    // 0xaeae08: cmp             w5, NULL
    // 0xaeae0c: b.ne            #0xaeae18
    // 0xaeae10: r5 = Null
    //     0xaeae10: mov             x5, NULL
    // 0xaeae14: b               #0xaeae24
    // 0xaeae18: LoadField: r6 = r5->field_b
    //     0xaeae18: ldur            w6, [x5, #0xb]
    // 0xaeae1c: DecompressPointer r6
    //     0xaeae1c: add             x6, x6, HEAP, lsl #32
    // 0xaeae20: mov             x5, x6
    // 0xaeae24: LoadField: r6 = r1->field_4f
    //     0xaeae24: ldur            w6, [x1, #0x4f]
    // 0xaeae28: DecompressPointer r6
    //     0xaeae28: add             x6, x6, HEAP, lsl #32
    // 0xaeae2c: stp             x0, x6, [SP, #0x20]
    // 0xaeae30: stp             x3, x2, [SP, #0x10]
    // 0xaeae34: stp             x5, x4, [SP]
    // 0xaeae38: r4 = 0
    //     0xaeae38: movz            x4, #0
    // 0xaeae3c: ldr             x0, [SP, #0x28]
    // 0xaeae40: r16 = UnlinkedCall_0x613b5c
    //     0xaeae40: add             x16, PP, #0x58, lsl #12  ; [pp+0x587e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaeae44: add             x16, x16, #0x7e0
    // 0xaeae48: ldp             x5, lr, [x16]
    // 0xaeae4c: blr             lr
    // 0xaeae50: r0 = Null
    //     0xaeae50: mov             x0, NULL
    // 0xaeae54: LeaveFrame
    //     0xaeae54: mov             SP, fp
    //     0xaeae58: ldp             fp, lr, [SP], #0x10
    // 0xaeae5c: ret
    //     0xaeae5c: ret             
    // 0xaeae60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaeae60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaeae64: b               #0xaeadc8
    // 0xaeae68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaeae68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc872f8, size: 0x64
    // 0xc872f8: EnterFrame
    //     0xc872f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc872fc: mov             fp, SP
    // 0xc87300: AllocStack(0x8)
    //     0xc87300: sub             SP, SP, #8
    // 0xc87304: SetupParameters(_ProductGridItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc87304: mov             x0, x1
    //     0xc87308: stur            x1, [fp, #-8]
    // 0xc8730c: CheckStackOverflow
    //     0xc8730c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87310: cmp             SP, x16
    //     0xc87314: b.ls            #0xc87348
    // 0xc87318: LoadField: r1 = r0->field_1b
    //     0xc87318: ldur            w1, [x0, #0x1b]
    // 0xc8731c: DecompressPointer r1
    //     0xc8731c: add             x1, x1, HEAP, lsl #32
    // 0xc87320: r16 = Sentinel
    //     0xc87320: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87324: cmp             w1, w16
    // 0xc87328: b.eq            #0xc87350
    // 0xc8732c: r0 = dispose()
    //     0xc8732c: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87330: ldur            x1, [fp, #-8]
    // 0xc87334: r0 = dispose()
    //     0xc87334: bl              #0xc8735c  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_grid_item_view.dart] __ProductGridItemViewState&State&SingleTickerProviderStateMixin::dispose
    // 0xc87338: r0 = Null
    //     0xc87338: mov             x0, NULL
    // 0xc8733c: LeaveFrame
    //     0xc8733c: mov             SP, fp
    //     0xc87340: ldp             fp, lr, [SP], #0x10
    // 0xc87344: ret
    //     0xc87344: ret             
    // 0xc87348: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87348: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8734c: b               #0xc87318
    // 0xc87350: r9 = _pageController
    //     0xc87350: add             x9, PP, #0x58, lsl #12  ; [pp+0x58760] Field <_ProductGridItemViewState@**********._pageController@**********>: late (offset: 0x1c)
    //     0xc87354: ldr             x9, [x9, #0x760]
    // 0xc87358: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87358: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4160, size: 0x5c, field offset: 0xc
//   const constructor, 
class ProductGridItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dab8, size: 0x30
    // 0xc7dab8: EnterFrame
    //     0xc7dab8: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dabc: mov             fp, SP
    // 0xc7dac0: mov             x0, x1
    // 0xc7dac4: r1 = <ProductGridItemView>
    //     0xc7dac4: add             x1, PP, #0x48, lsl #12  ; [pp+0x48c28] TypeArguments: <ProductGridItemView>
    //     0xc7dac8: ldr             x1, [x1, #0xc28]
    // 0xc7dacc: r0 = _ProductGridItemViewState()
    //     0xc7dacc: bl              #0xc7dae8  ; Allocate_ProductGridItemViewStateStub -> _ProductGridItemViewState (size=0x28)
    // 0xc7dad0: r1 = Sentinel
    //     0xc7dad0: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7dad4: StoreField: r0->field_1b = r1
    //     0xc7dad4: stur            w1, [x0, #0x1b]
    // 0xc7dad8: StoreField: r0->field_1f = rZR
    //     0xc7dad8: stur            xzr, [x0, #0x1f]
    // 0xc7dadc: LeaveFrame
    //     0xc7dadc: mov             SP, fp
    //     0xc7dae0: ldp             fp, lr, [SP], #0x10
    // 0xc7dae4: ret
    //     0xc7dae4: ret             
  }
}
