// lib: , url: package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart

// class id: 1049457, size: 0x8
class :: {
}

// class id: 3298, size: 0x30, field offset: 0x14
class _RatingReviewOnTapImageState extends State<dynamic> {

  late PageController _pageController; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x944e3c, size: 0x110
    // 0x944e3c: EnterFrame
    //     0x944e3c: stp             fp, lr, [SP, #-0x10]!
    //     0x944e40: mov             fp, SP
    // 0x944e44: AllocStack(0x18)
    //     0x944e44: sub             SP, SP, #0x18
    // 0x944e48: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r2, fp-0x10 */)
    //     0x944e48: mov             x2, x1
    //     0x944e4c: stur            x1, [fp, #-0x10]
    // 0x944e50: CheckStackOverflow
    //     0x944e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944e54: cmp             SP, x16
    //     0x944e58: b.ls            #0x944f3c
    // 0x944e5c: LoadField: r0 = r2->field_b
    //     0x944e5c: ldur            w0, [x2, #0xb]
    // 0x944e60: DecompressPointer r0
    //     0x944e60: add             x0, x0, HEAP, lsl #32
    // 0x944e64: cmp             w0, NULL
    // 0x944e68: b.eq            #0x944f44
    // 0x944e6c: LoadField: r1 = r0->field_f
    //     0x944e6c: ldur            x1, [x0, #0xf]
    // 0x944e70: stur            x1, [fp, #-8]
    // 0x944e74: StoreField: r2->field_13 = r1
    //     0x944e74: stur            x1, [x2, #0x13]
    // 0x944e78: r0 = PageController()
    //     0x944e78: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x944e7c: mov             x2, x0
    // 0x944e80: ldur            x0, [fp, #-8]
    // 0x944e84: stur            x2, [fp, #-0x18]
    // 0x944e88: StoreField: r2->field_3f = r0
    //     0x944e88: stur            x0, [x2, #0x3f]
    // 0x944e8c: r0 = true
    //     0x944e8c: add             x0, NULL, #0x20  ; true
    // 0x944e90: StoreField: r2->field_47 = r0
    //     0x944e90: stur            w0, [x2, #0x47]
    // 0x944e94: d0 = 1.000000
    //     0x944e94: fmov            d0, #1.00000000
    // 0x944e98: StoreField: r2->field_4b = d0
    //     0x944e98: stur            d0, [x2, #0x4b]
    // 0x944e9c: mov             x1, x2
    // 0x944ea0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x944ea0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x944ea4: r0 = ScrollController()
    //     0x944ea4: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x944ea8: ldur            x0, [fp, #-0x18]
    // 0x944eac: ldur            x3, [fp, #-0x10]
    // 0x944eb0: StoreField: r3->field_1b = r0
    //     0x944eb0: stur            w0, [x3, #0x1b]
    //     0x944eb4: ldurb           w16, [x3, #-1]
    //     0x944eb8: ldurb           w17, [x0, #-1]
    //     0x944ebc: and             x16, x17, x16, lsr #2
    //     0x944ec0: tst             x16, HEAP, lsr #32
    //     0x944ec4: b.eq            #0x944ecc
    //     0x944ec8: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x944ecc: LoadField: r0 = r3->field_1f
    //     0x944ecc: ldur            w0, [x3, #0x1f]
    // 0x944ed0: DecompressPointer r0
    //     0x944ed0: add             x0, x0, HEAP, lsl #32
    // 0x944ed4: mov             x2, x3
    // 0x944ed8: stur            x0, [fp, #-0x18]
    // 0x944edc: r1 = Function '_onCollapseChanged@1634380928':.
    //     0x944edc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f80] AnonymousClosure: (0x944f6c), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x944fa4)
    //     0x944ee0: ldr             x1, [x1, #0xf80]
    // 0x944ee4: r0 = AllocateClosure()
    //     0x944ee4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944ee8: ldur            x1, [fp, #-0x18]
    // 0x944eec: mov             x2, x0
    // 0x944ef0: r0 = addListener()
    //     0x944ef0: bl              #0x7b8dac  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x944ef4: ldur            x1, [fp, #-0x10]
    // 0x944ef8: LoadField: r2 = r1->field_b
    //     0x944ef8: ldur            w2, [x1, #0xb]
    // 0x944efc: DecompressPointer r2
    //     0x944efc: add             x2, x2, HEAP, lsl #32
    // 0x944f00: cmp             w2, NULL
    // 0x944f04: b.eq            #0x944f48
    // 0x944f08: LoadField: r0 = r2->field_1b
    //     0x944f08: ldur            w0, [x2, #0x1b]
    // 0x944f0c: DecompressPointer r0
    //     0x944f0c: add             x0, x0, HEAP, lsl #32
    // 0x944f10: StoreField: r1->field_27 = r0
    //     0x944f10: stur            w0, [x1, #0x27]
    //     0x944f14: ldurb           w16, [x1, #-1]
    //     0x944f18: ldurb           w17, [x0, #-1]
    //     0x944f1c: and             x16, x17, x16, lsr #2
    //     0x944f20: tst             x16, HEAP, lsr #32
    //     0x944f24: b.eq            #0x944f2c
    //     0x944f28: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x944f2c: r0 = Null
    //     0x944f2c: mov             x0, NULL
    // 0x944f30: LeaveFrame
    //     0x944f30: mov             SP, fp
    //     0x944f34: ldp             fp, lr, [SP], #0x10
    // 0x944f38: ret
    //     0x944f38: ret             
    // 0x944f3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944f3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944f40: b               #0x944e5c
    // 0x944f44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944f44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x944f48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x944f48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onCollapseChanged(dynamic) {
    // ** addr: 0x944f6c, size: 0x38
    // 0x944f6c: EnterFrame
    //     0x944f6c: stp             fp, lr, [SP, #-0x10]!
    //     0x944f70: mov             fp, SP
    // 0x944f74: ldr             x0, [fp, #0x10]
    // 0x944f78: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x944f78: ldur            w1, [x0, #0x17]
    // 0x944f7c: DecompressPointer r1
    //     0x944f7c: add             x1, x1, HEAP, lsl #32
    // 0x944f80: CheckStackOverflow
    //     0x944f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944f84: cmp             SP, x16
    //     0x944f88: b.ls            #0x944f9c
    // 0x944f8c: r0 = _onCollapseChanged()
    //     0x944f8c: bl              #0x944fa4  ; [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged
    // 0x944f90: LeaveFrame
    //     0x944f90: mov             SP, fp
    //     0x944f94: ldp             fp, lr, [SP], #0x10
    // 0x944f98: ret
    //     0x944f98: ret             
    // 0x944f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x944f9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x944fa0: b               #0x944f8c
  }
  _ _onCollapseChanged(/* No info */) {
    // ** addr: 0x944fa4, size: 0x98
    // 0x944fa4: EnterFrame
    //     0x944fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x944fa8: mov             fp, SP
    // 0x944fac: AllocStack(0x8)
    //     0x944fac: sub             SP, SP, #8
    // 0x944fb0: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r1, fp-0x8 */)
    //     0x944fb0: stur            x1, [fp, #-8]
    // 0x944fb4: CheckStackOverflow
    //     0x944fb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x944fb8: cmp             SP, x16
    //     0x944fbc: b.ls            #0x945034
    // 0x944fc0: r1 = 1
    //     0x944fc0: movz            x1, #0x1
    // 0x944fc4: r0 = AllocateContext()
    //     0x944fc4: bl              #0x16f6108  ; AllocateContextStub
    // 0x944fc8: mov             x1, x0
    // 0x944fcc: ldur            x0, [fp, #-8]
    // 0x944fd0: StoreField: r1->field_f = r0
    //     0x944fd0: stur            w0, [x1, #0xf]
    // 0x944fd4: LoadField: r2 = r0->field_1f
    //     0x944fd4: ldur            w2, [x0, #0x1f]
    // 0x944fd8: DecompressPointer r2
    //     0x944fd8: add             x2, x2, HEAP, lsl #32
    // 0x944fdc: LoadField: r3 = r2->field_27
    //     0x944fdc: ldur            w3, [x2, #0x27]
    // 0x944fe0: DecompressPointer r3
    //     0x944fe0: add             x3, x3, HEAP, lsl #32
    // 0x944fe4: tbnz            w3, #4, #0x945008
    // 0x944fe8: mov             x2, x1
    // 0x944fec: r1 = Function '<anonymous closure>':.
    //     0x944fec: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f88] AnonymousClosure: (0x935b70), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0x944ff0: ldr             x1, [x1, #0xf88]
    // 0x944ff4: r0 = AllocateClosure()
    //     0x944ff4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x944ff8: ldur            x1, [fp, #-8]
    // 0x944ffc: mov             x2, x0
    // 0x945000: r0 = setState()
    //     0x945000: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x945004: b               #0x945024
    // 0x945008: mov             x2, x1
    // 0x94500c: r1 = Function '<anonymous closure>':.
    //     0x94500c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f90] AnonymousClosure: (0x935ab4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0x945010: ldr             x1, [x1, #0xf90]
    // 0x945014: r0 = AllocateClosure()
    //     0x945014: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x945018: ldur            x1, [fp, #-8]
    // 0x94501c: mov             x2, x0
    // 0x945020: r0 = setState()
    //     0x945020: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x945024: r0 = Null
    //     0x945024: mov             x0, NULL
    // 0x945028: LeaveFrame
    //     0x945028: mov             SP, fp
    //     0x94502c: ldp             fp, lr, [SP], #0x10
    // 0x945030: ret
    //     0x945030: ret             
    // 0x945034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x945034: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x945038: b               #0x944fc0
  }
  _ build(/* No info */) {
    // ** addr: 0xb97d0c, size: 0x22c4
    // 0xb97d0c: EnterFrame
    //     0xb97d0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb97d10: mov             fp, SP
    // 0xb97d14: AllocStack(0x80)
    //     0xb97d14: sub             SP, SP, #0x80
    // 0xb97d18: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb97d18: mov             x0, x1
    //     0xb97d1c: stur            x1, [fp, #-8]
    //     0xb97d20: stur            x2, [fp, #-0x10]
    // 0xb97d24: CheckStackOverflow
    //     0xb97d24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb97d28: cmp             SP, x16
    //     0xb97d2c: b.ls            #0xb99ef4
    // 0xb97d30: r1 = 2
    //     0xb97d30: movz            x1, #0x2
    // 0xb97d34: r0 = AllocateContext()
    //     0xb97d34: bl              #0x16f6108  ; AllocateContextStub
    // 0xb97d38: mov             x3, x0
    // 0xb97d3c: ldur            x0, [fp, #-8]
    // 0xb97d40: stur            x3, [fp, #-0x20]
    // 0xb97d44: StoreField: r3->field_f = r0
    //     0xb97d44: stur            w0, [x3, #0xf]
    // 0xb97d48: ldur            x1, [fp, #-0x10]
    // 0xb97d4c: StoreField: r3->field_13 = r1
    //     0xb97d4c: stur            w1, [x3, #0x13]
    // 0xb97d50: LoadField: r1 = r0->field_b
    //     0xb97d50: ldur            w1, [x0, #0xb]
    // 0xb97d54: DecompressPointer r1
    //     0xb97d54: add             x1, x1, HEAP, lsl #32
    // 0xb97d58: cmp             w1, NULL
    // 0xb97d5c: b.eq            #0xb99efc
    // 0xb97d60: LoadField: r2 = r1->field_b
    //     0xb97d60: ldur            w2, [x1, #0xb]
    // 0xb97d64: DecompressPointer r2
    //     0xb97d64: add             x2, x2, HEAP, lsl #32
    // 0xb97d68: cmp             w2, NULL
    // 0xb97d6c: b.ne            #0xb97d78
    // 0xb97d70: r4 = Null
    //     0xb97d70: mov             x4, NULL
    // 0xb97d74: b               #0xb97d88
    // 0xb97d78: LoadField: r1 = r2->field_1b
    //     0xb97d78: ldur            w1, [x2, #0x1b]
    // 0xb97d7c: DecompressPointer r1
    //     0xb97d7c: add             x1, x1, HEAP, lsl #32
    // 0xb97d80: LoadField: r2 = r1->field_b
    //     0xb97d80: ldur            w2, [x1, #0xb]
    // 0xb97d84: mov             x4, x2
    // 0xb97d88: stur            x4, [fp, #-0x18]
    // 0xb97d8c: LoadField: r5 = r0->field_1b
    //     0xb97d8c: ldur            w5, [x0, #0x1b]
    // 0xb97d90: DecompressPointer r5
    //     0xb97d90: add             x5, x5, HEAP, lsl #32
    // 0xb97d94: r16 = Sentinel
    //     0xb97d94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb97d98: cmp             w5, w16
    // 0xb97d9c: b.eq            #0xb99f00
    // 0xb97da0: mov             x2, x0
    // 0xb97da4: stur            x5, [fp, #-0x10]
    // 0xb97da8: r1 = Function '_onPageChanged@1634380928':.
    //     0xb97da8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ee8] AnonymousClosure: (0xb9acbc), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged (0xb9acf8)
    //     0xb97dac: ldr             x1, [x1, #0xee8]
    // 0xb97db0: r0 = AllocateClosure()
    //     0xb97db0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb97db4: ldur            x2, [fp, #-0x20]
    // 0xb97db8: r1 = Function '<anonymous closure>':.
    //     0xb97db8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ef0] AnonymousClosure: (0xb9a9e8), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb97d0c)
    //     0xb97dbc: ldr             x1, [x1, #0xef0]
    // 0xb97dc0: stur            x0, [fp, #-0x28]
    // 0xb97dc4: r0 = AllocateClosure()
    //     0xb97dc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb97dc8: stur            x0, [fp, #-0x30]
    // 0xb97dcc: r0 = PageView()
    //     0xb97dcc: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb97dd0: stur            x0, [fp, #-0x38]
    // 0xb97dd4: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb97dd4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb97dd8: ldr             x16, [x16, #0x1c8]
    // 0xb97ddc: ldur            lr, [fp, #-0x10]
    // 0xb97de0: stp             lr, x16, [SP]
    // 0xb97de4: mov             x1, x0
    // 0xb97de8: ldur            x2, [fp, #-0x30]
    // 0xb97dec: ldur            x3, [fp, #-0x18]
    // 0xb97df0: ldur            x5, [fp, #-0x28]
    // 0xb97df4: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb97df4: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb97df8: ldr             x4, [x4, #0xe40]
    // 0xb97dfc: r0 = PageView.builder()
    //     0xb97dfc: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb97e00: r1 = Null
    //     0xb97e00: mov             x1, NULL
    // 0xb97e04: r2 = 2
    //     0xb97e04: movz            x2, #0x2
    // 0xb97e08: r0 = AllocateArray()
    //     0xb97e08: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb97e0c: mov             x2, x0
    // 0xb97e10: ldur            x0, [fp, #-0x38]
    // 0xb97e14: stur            x2, [fp, #-0x10]
    // 0xb97e18: StoreField: r2->field_f = r0
    //     0xb97e18: stur            w0, [x2, #0xf]
    // 0xb97e1c: r1 = <Widget>
    //     0xb97e1c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb97e20: r0 = AllocateGrowableArray()
    //     0xb97e20: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb97e24: mov             x2, x0
    // 0xb97e28: ldur            x0, [fp, #-0x10]
    // 0xb97e2c: stur            x2, [fp, #-0x18]
    // 0xb97e30: StoreField: r2->field_f = r0
    //     0xb97e30: stur            w0, [x2, #0xf]
    // 0xb97e34: r0 = 2
    //     0xb97e34: movz            x0, #0x2
    // 0xb97e38: StoreField: r2->field_b = r0
    //     0xb97e38: stur            w0, [x2, #0xb]
    // 0xb97e3c: ldur            x0, [fp, #-8]
    // 0xb97e40: LoadField: r1 = r0->field_13
    //     0xb97e40: ldur            x1, [x0, #0x13]
    // 0xb97e44: cmp             x1, #0
    // 0xb97e48: b.le            #0xb97fa4
    // 0xb97e4c: ldur            x3, [fp, #-0x20]
    // 0xb97e50: LoadField: r1 = r3->field_13
    //     0xb97e50: ldur            w1, [x3, #0x13]
    // 0xb97e54: DecompressPointer r1
    //     0xb97e54: add             x1, x1, HEAP, lsl #32
    // 0xb97e58: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb97e58: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb97e5c: r0 = _of()
    //     0xb97e5c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb97e60: LoadField: r1 = r0->field_7
    //     0xb97e60: ldur            w1, [x0, #7]
    // 0xb97e64: DecompressPointer r1
    //     0xb97e64: add             x1, x1, HEAP, lsl #32
    // 0xb97e68: LoadField: d0 = r1->field_7
    //     0xb97e68: ldur            d0, [x1, #7]
    // 0xb97e6c: d1 = 0.300000
    //     0xb97e6c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb97e70: ldr             d1, [x17, #0x658]
    // 0xb97e74: fmul            d2, d0, d1
    // 0xb97e78: stur            d2, [fp, #-0x58]
    // 0xb97e7c: r0 = Container()
    //     0xb97e7c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb97e80: stur            x0, [fp, #-0x10]
    // 0xb97e84: r16 = Instance_Color
    //     0xb97e84: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb97e88: ldr             x16, [x16, #0xf88]
    // 0xb97e8c: str             x16, [SP]
    // 0xb97e90: mov             x1, x0
    // 0xb97e94: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb97e94: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb97e98: ldr             x4, [x4, #0xf40]
    // 0xb97e9c: r0 = Container()
    //     0xb97e9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb97ea0: r0 = GestureDetector()
    //     0xb97ea0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb97ea4: ldur            x2, [fp, #-0x20]
    // 0xb97ea8: r1 = Function '<anonymous closure>':.
    //     0xb97ea8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54ef8] AnonymousClosure: (0xb9a978), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb97d0c)
    //     0xb97eac: ldr             x1, [x1, #0xef8]
    // 0xb97eb0: stur            x0, [fp, #-0x28]
    // 0xb97eb4: r0 = AllocateClosure()
    //     0xb97eb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb97eb8: ldur            x16, [fp, #-0x10]
    // 0xb97ebc: stp             x16, x0, [SP]
    // 0xb97ec0: ldur            x1, [fp, #-0x28]
    // 0xb97ec4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb97ec4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb97ec8: ldr             x4, [x4, #0xaf0]
    // 0xb97ecc: r0 = GestureDetector()
    //     0xb97ecc: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb97ed0: r1 = <StackParentData>
    //     0xb97ed0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb97ed4: ldr             x1, [x1, #0x8e0]
    // 0xb97ed8: r0 = Positioned()
    //     0xb97ed8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb97edc: mov             x2, x0
    // 0xb97ee0: r0 = 0.000000
    //     0xb97ee0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb97ee4: stur            x2, [fp, #-0x10]
    // 0xb97ee8: StoreField: r2->field_13 = r0
    //     0xb97ee8: stur            w0, [x2, #0x13]
    // 0xb97eec: ArrayStore: r2[0] = r0  ; List_4
    //     0xb97eec: stur            w0, [x2, #0x17]
    // 0xb97ef0: StoreField: r2->field_1f = r0
    //     0xb97ef0: stur            w0, [x2, #0x1f]
    // 0xb97ef4: ldur            d0, [fp, #-0x58]
    // 0xb97ef8: r1 = inline_Allocate_Double()
    //     0xb97ef8: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xb97efc: add             x1, x1, #0x10
    //     0xb97f00: cmp             x3, x1
    //     0xb97f04: b.ls            #0xb99f0c
    //     0xb97f08: str             x1, [THR, #0x50]  ; THR::top
    //     0xb97f0c: sub             x1, x1, #0xf
    //     0xb97f10: movz            x3, #0xe15c
    //     0xb97f14: movk            x3, #0x3, lsl #16
    //     0xb97f18: stur            x3, [x1, #-1]
    // 0xb97f1c: StoreField: r1->field_7 = d0
    //     0xb97f1c: stur            d0, [x1, #7]
    // 0xb97f20: StoreField: r2->field_23 = r1
    //     0xb97f20: stur            w1, [x2, #0x23]
    // 0xb97f24: ldur            x1, [fp, #-0x28]
    // 0xb97f28: StoreField: r2->field_b = r1
    //     0xb97f28: stur            w1, [x2, #0xb]
    // 0xb97f2c: ldur            x3, [fp, #-0x18]
    // 0xb97f30: LoadField: r1 = r3->field_b
    //     0xb97f30: ldur            w1, [x3, #0xb]
    // 0xb97f34: LoadField: r4 = r3->field_f
    //     0xb97f34: ldur            w4, [x3, #0xf]
    // 0xb97f38: DecompressPointer r4
    //     0xb97f38: add             x4, x4, HEAP, lsl #32
    // 0xb97f3c: LoadField: r5 = r4->field_b
    //     0xb97f3c: ldur            w5, [x4, #0xb]
    // 0xb97f40: r4 = LoadInt32Instr(r1)
    //     0xb97f40: sbfx            x4, x1, #1, #0x1f
    // 0xb97f44: stur            x4, [fp, #-0x40]
    // 0xb97f48: r1 = LoadInt32Instr(r5)
    //     0xb97f48: sbfx            x1, x5, #1, #0x1f
    // 0xb97f4c: cmp             x4, x1
    // 0xb97f50: b.ne            #0xb97f5c
    // 0xb97f54: mov             x1, x3
    // 0xb97f58: r0 = _growToNextCapacity()
    //     0xb97f58: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb97f5c: ldur            x2, [fp, #-0x18]
    // 0xb97f60: ldur            x3, [fp, #-0x40]
    // 0xb97f64: add             x0, x3, #1
    // 0xb97f68: lsl             x1, x0, #1
    // 0xb97f6c: StoreField: r2->field_b = r1
    //     0xb97f6c: stur            w1, [x2, #0xb]
    // 0xb97f70: LoadField: r1 = r2->field_f
    //     0xb97f70: ldur            w1, [x2, #0xf]
    // 0xb97f74: DecompressPointer r1
    //     0xb97f74: add             x1, x1, HEAP, lsl #32
    // 0xb97f78: ldur            x0, [fp, #-0x10]
    // 0xb97f7c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb97f7c: add             x25, x1, x3, lsl #2
    //     0xb97f80: add             x25, x25, #0xf
    //     0xb97f84: str             w0, [x25]
    //     0xb97f88: tbz             w0, #0, #0xb97fa4
    //     0xb97f8c: ldurb           w16, [x1, #-1]
    //     0xb97f90: ldurb           w17, [x0, #-1]
    //     0xb97f94: and             x16, x17, x16, lsr #2
    //     0xb97f98: tst             x16, HEAP, lsr #32
    //     0xb97f9c: b.eq            #0xb97fa4
    //     0xb97fa0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb97fa4: ldur            x0, [fp, #-8]
    // 0xb97fa8: LoadField: r1 = r0->field_13
    //     0xb97fa8: ldur            x1, [x0, #0x13]
    // 0xb97fac: LoadField: r3 = r0->field_b
    //     0xb97fac: ldur            w3, [x0, #0xb]
    // 0xb97fb0: DecompressPointer r3
    //     0xb97fb0: add             x3, x3, HEAP, lsl #32
    // 0xb97fb4: cmp             w3, NULL
    // 0xb97fb8: b.eq            #0xb99f28
    // 0xb97fbc: LoadField: r4 = r3->field_b
    //     0xb97fbc: ldur            w4, [x3, #0xb]
    // 0xb97fc0: DecompressPointer r4
    //     0xb97fc0: add             x4, x4, HEAP, lsl #32
    // 0xb97fc4: cmp             w4, NULL
    // 0xb97fc8: b.ne            #0xb97fd4
    // 0xb97fcc: r3 = Null
    //     0xb97fcc: mov             x3, NULL
    // 0xb97fd0: b               #0xb97fe4
    // 0xb97fd4: LoadField: r3 = r4->field_1b
    //     0xb97fd4: ldur            w3, [x4, #0x1b]
    // 0xb97fd8: DecompressPointer r3
    //     0xb97fd8: add             x3, x3, HEAP, lsl #32
    // 0xb97fdc: LoadField: r4 = r3->field_b
    //     0xb97fdc: ldur            w4, [x3, #0xb]
    // 0xb97fe0: mov             x3, x4
    // 0xb97fe4: cmp             w3, NULL
    // 0xb97fe8: b.ne            #0xb97ff4
    // 0xb97fec: r3 = 0
    //     0xb97fec: movz            x3, #0
    // 0xb97ff0: b               #0xb97ffc
    // 0xb97ff4: r4 = LoadInt32Instr(r3)
    //     0xb97ff4: sbfx            x4, x3, #1, #0x1f
    // 0xb97ff8: mov             x3, x4
    // 0xb97ffc: sub             x4, x3, #1
    // 0xb98000: cmp             x1, x4
    // 0xb98004: b.ge            #0xb98160
    // 0xb98008: ldur            x3, [fp, #-0x20]
    // 0xb9800c: LoadField: r1 = r3->field_13
    //     0xb9800c: ldur            w1, [x3, #0x13]
    // 0xb98010: DecompressPointer r1
    //     0xb98010: add             x1, x1, HEAP, lsl #32
    // 0xb98014: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb98014: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb98018: r0 = _of()
    //     0xb98018: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb9801c: LoadField: r1 = r0->field_7
    //     0xb9801c: ldur            w1, [x0, #7]
    // 0xb98020: DecompressPointer r1
    //     0xb98020: add             x1, x1, HEAP, lsl #32
    // 0xb98024: LoadField: d0 = r1->field_7
    //     0xb98024: ldur            d0, [x1, #7]
    // 0xb98028: d1 = 0.300000
    //     0xb98028: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb9802c: ldr             d1, [x17, #0x658]
    // 0xb98030: fmul            d2, d0, d1
    // 0xb98034: stur            d2, [fp, #-0x58]
    // 0xb98038: r0 = Container()
    //     0xb98038: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb9803c: stur            x0, [fp, #-0x10]
    // 0xb98040: r16 = Instance_Color
    //     0xb98040: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb98044: ldr             x16, [x16, #0xf88]
    // 0xb98048: str             x16, [SP]
    // 0xb9804c: mov             x1, x0
    // 0xb98050: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb98050: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb98054: ldr             x4, [x4, #0xf40]
    // 0xb98058: r0 = Container()
    //     0xb98058: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb9805c: r0 = GestureDetector()
    //     0xb9805c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb98060: ldur            x2, [fp, #-0x20]
    // 0xb98064: r1 = Function '<anonymous closure>':.
    //     0xb98064: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f00] AnonymousClosure: (0xb9a8b0), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb97d0c)
    //     0xb98068: ldr             x1, [x1, #0xf00]
    // 0xb9806c: stur            x0, [fp, #-0x28]
    // 0xb98070: r0 = AllocateClosure()
    //     0xb98070: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb98074: ldur            x16, [fp, #-0x10]
    // 0xb98078: stp             x16, x0, [SP]
    // 0xb9807c: ldur            x1, [fp, #-0x28]
    // 0xb98080: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb98080: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb98084: ldr             x4, [x4, #0xaf0]
    // 0xb98088: r0 = GestureDetector()
    //     0xb98088: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb9808c: r1 = <StackParentData>
    //     0xb9808c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb98090: ldr             x1, [x1, #0x8e0]
    // 0xb98094: r0 = Positioned()
    //     0xb98094: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb98098: mov             x2, x0
    // 0xb9809c: r0 = 0.000000
    //     0xb9809c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb980a0: stur            x2, [fp, #-0x10]
    // 0xb980a4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb980a4: stur            w0, [x2, #0x17]
    // 0xb980a8: StoreField: r2->field_1b = r0
    //     0xb980a8: stur            w0, [x2, #0x1b]
    // 0xb980ac: StoreField: r2->field_1f = r0
    //     0xb980ac: stur            w0, [x2, #0x1f]
    // 0xb980b0: ldur            d0, [fp, #-0x58]
    // 0xb980b4: r1 = inline_Allocate_Double()
    //     0xb980b4: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xb980b8: add             x1, x1, #0x10
    //     0xb980bc: cmp             x3, x1
    //     0xb980c0: b.ls            #0xb99f2c
    //     0xb980c4: str             x1, [THR, #0x50]  ; THR::top
    //     0xb980c8: sub             x1, x1, #0xf
    //     0xb980cc: movz            x3, #0xe15c
    //     0xb980d0: movk            x3, #0x3, lsl #16
    //     0xb980d4: stur            x3, [x1, #-1]
    // 0xb980d8: StoreField: r1->field_7 = d0
    //     0xb980d8: stur            d0, [x1, #7]
    // 0xb980dc: StoreField: r2->field_23 = r1
    //     0xb980dc: stur            w1, [x2, #0x23]
    // 0xb980e0: ldur            x1, [fp, #-0x28]
    // 0xb980e4: StoreField: r2->field_b = r1
    //     0xb980e4: stur            w1, [x2, #0xb]
    // 0xb980e8: ldur            x3, [fp, #-0x18]
    // 0xb980ec: LoadField: r1 = r3->field_b
    //     0xb980ec: ldur            w1, [x3, #0xb]
    // 0xb980f0: LoadField: r4 = r3->field_f
    //     0xb980f0: ldur            w4, [x3, #0xf]
    // 0xb980f4: DecompressPointer r4
    //     0xb980f4: add             x4, x4, HEAP, lsl #32
    // 0xb980f8: LoadField: r5 = r4->field_b
    //     0xb980f8: ldur            w5, [x4, #0xb]
    // 0xb980fc: r4 = LoadInt32Instr(r1)
    //     0xb980fc: sbfx            x4, x1, #1, #0x1f
    // 0xb98100: stur            x4, [fp, #-0x40]
    // 0xb98104: r1 = LoadInt32Instr(r5)
    //     0xb98104: sbfx            x1, x5, #1, #0x1f
    // 0xb98108: cmp             x4, x1
    // 0xb9810c: b.ne            #0xb98118
    // 0xb98110: mov             x1, x3
    // 0xb98114: r0 = _growToNextCapacity()
    //     0xb98114: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb98118: ldur            x2, [fp, #-0x18]
    // 0xb9811c: ldur            x3, [fp, #-0x40]
    // 0xb98120: add             x0, x3, #1
    // 0xb98124: lsl             x1, x0, #1
    // 0xb98128: StoreField: r2->field_b = r1
    //     0xb98128: stur            w1, [x2, #0xb]
    // 0xb9812c: LoadField: r1 = r2->field_f
    //     0xb9812c: ldur            w1, [x2, #0xf]
    // 0xb98130: DecompressPointer r1
    //     0xb98130: add             x1, x1, HEAP, lsl #32
    // 0xb98134: ldur            x0, [fp, #-0x10]
    // 0xb98138: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb98138: add             x25, x1, x3, lsl #2
    //     0xb9813c: add             x25, x25, #0xf
    //     0xb98140: str             w0, [x25]
    //     0xb98144: tbz             w0, #0, #0xb98160
    //     0xb98148: ldurb           w16, [x1, #-1]
    //     0xb9814c: ldurb           w17, [x0, #-1]
    //     0xb98150: and             x16, x17, x16, lsr #2
    //     0xb98154: tst             x16, HEAP, lsr #32
    //     0xb98158: b.eq            #0xb98160
    //     0xb9815c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb98160: ldur            x0, [fp, #-8]
    // 0xb98164: ldur            x3, [fp, #-0x20]
    // 0xb98168: LoadField: r1 = r3->field_13
    //     0xb98168: ldur            w1, [x3, #0x13]
    // 0xb9816c: DecompressPointer r1
    //     0xb9816c: add             x1, x1, HEAP, lsl #32
    // 0xb98170: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb98170: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb98174: r0 = _of()
    //     0xb98174: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb98178: LoadField: r1 = r0->field_7
    //     0xb98178: ldur            w1, [x0, #7]
    // 0xb9817c: DecompressPointer r1
    //     0xb9817c: add             x1, x1, HEAP, lsl #32
    // 0xb98180: LoadField: d0 = r1->field_7
    //     0xb98180: ldur            d0, [x1, #7]
    // 0xb98184: d1 = 0.950000
    //     0xb98184: add             x17, PP, #0x51, lsl #12  ; [pp+0x51e58] IMM: double(0.95) from 0x3fee666666666666
    //     0xb98188: ldr             d1, [x17, #0xe58]
    // 0xb9818c: fmul            d2, d0, d1
    // 0xb98190: ldur            x0, [fp, #-8]
    // 0xb98194: stur            d2, [fp, #-0x58]
    // 0xb98198: LoadField: r1 = r0->field_b
    //     0xb98198: ldur            w1, [x0, #0xb]
    // 0xb9819c: DecompressPointer r1
    //     0xb9819c: add             x1, x1, HEAP, lsl #32
    // 0xb981a0: cmp             w1, NULL
    // 0xb981a4: b.eq            #0xb99f48
    // 0xb981a8: LoadField: r2 = r1->field_b
    //     0xb981a8: ldur            w2, [x1, #0xb]
    // 0xb981ac: DecompressPointer r2
    //     0xb981ac: add             x2, x2, HEAP, lsl #32
    // 0xb981b0: cmp             w2, NULL
    // 0xb981b4: b.ne            #0xb981c0
    // 0xb981b8: r1 = Null
    //     0xb981b8: mov             x1, NULL
    // 0xb981bc: b               #0xb981d0
    // 0xb981c0: LoadField: r1 = r2->field_1b
    //     0xb981c0: ldur            w1, [x2, #0x1b]
    // 0xb981c4: DecompressPointer r1
    //     0xb981c4: add             x1, x1, HEAP, lsl #32
    // 0xb981c8: LoadField: r2 = r1->field_b
    //     0xb981c8: ldur            w2, [x1, #0xb]
    // 0xb981cc: mov             x1, x2
    // 0xb981d0: cmp             w1, NULL
    // 0xb981d4: b.ne            #0xb981e0
    // 0xb981d8: r3 = 0
    //     0xb981d8: movz            x3, #0
    // 0xb981dc: b               #0xb981e8
    // 0xb981e0: r2 = LoadInt32Instr(r1)
    //     0xb981e0: sbfx            x2, x1, #1, #0x1f
    // 0xb981e4: mov             x3, x2
    // 0xb981e8: ldur            x2, [fp, #-0x20]
    // 0xb981ec: stur            x3, [fp, #-0x40]
    // 0xb981f0: r1 = Function '<anonymous closure>':.
    //     0xb981f0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f08] AnonymousClosure: (0xaaa17c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xb981f4: ldr             x1, [x1, #0xf08]
    // 0xb981f8: r0 = AllocateClosure()
    //     0xb981f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb981fc: ldur            x2, [fp, #-0x40]
    // 0xb98200: r1 = <Widget>
    //     0xb98200: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb98204: stur            x0, [fp, #-0x10]
    // 0xb98208: r0 = _GrowableList()
    //     0xb98208: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb9820c: mov             x1, x0
    // 0xb98210: stur            x1, [fp, #-0x28]
    // 0xb98214: r2 = 0
    //     0xb98214: movz            x2, #0
    // 0xb98218: stur            x2, [fp, #-0x40]
    // 0xb9821c: CheckStackOverflow
    //     0xb9821c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb98220: cmp             SP, x16
    //     0xb98224: b.ls            #0xb99f4c
    // 0xb98228: LoadField: r0 = r1->field_b
    //     0xb98228: ldur            w0, [x1, #0xb]
    // 0xb9822c: r3 = LoadInt32Instr(r0)
    //     0xb9822c: sbfx            x3, x0, #1, #0x1f
    // 0xb98230: cmp             x2, x3
    // 0xb98234: b.ge            #0xb982f8
    // 0xb98238: lsl             x0, x2, #1
    // 0xb9823c: ldur            x16, [fp, #-0x10]
    // 0xb98240: stp             x0, x16, [SP]
    // 0xb98244: ldur            x0, [fp, #-0x10]
    // 0xb98248: ClosureCall
    //     0xb98248: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb9824c: ldur            x2, [x0, #0x1f]
    //     0xb98250: blr             x2
    // 0xb98254: mov             x3, x0
    // 0xb98258: r2 = Null
    //     0xb98258: mov             x2, NULL
    // 0xb9825c: r1 = Null
    //     0xb9825c: mov             x1, NULL
    // 0xb98260: stur            x3, [fp, #-0x30]
    // 0xb98264: r4 = 60
    //     0xb98264: movz            x4, #0x3c
    // 0xb98268: branchIfSmi(r0, 0xb98274)
    //     0xb98268: tbz             w0, #0, #0xb98274
    // 0xb9826c: r4 = LoadClassIdInstr(r0)
    //     0xb9826c: ldur            x4, [x0, #-1]
    //     0xb98270: ubfx            x4, x4, #0xc, #0x14
    // 0xb98274: sub             x4, x4, #0xe60
    // 0xb98278: cmp             x4, #0x464
    // 0xb9827c: b.ls            #0xb98294
    // 0xb98280: r8 = Widget
    //     0xb98280: add             x8, PP, #0x51, lsl #12  ; [pp+0x51e68] Type: Widget
    //     0xb98284: ldr             x8, [x8, #0xe68]
    // 0xb98288: r3 = Null
    //     0xb98288: add             x3, PP, #0x54, lsl #12  ; [pp+0x54f10] Null
    //     0xb9828c: ldr             x3, [x3, #0xf10]
    // 0xb98290: r0 = Widget()
    //     0xb98290: bl              #0x657fb8  ; IsType_Widget_Stub
    // 0xb98294: ldur            x3, [fp, #-0x28]
    // 0xb98298: LoadField: r0 = r3->field_b
    //     0xb98298: ldur            w0, [x3, #0xb]
    // 0xb9829c: r1 = LoadInt32Instr(r0)
    //     0xb9829c: sbfx            x1, x0, #1, #0x1f
    // 0xb982a0: mov             x0, x1
    // 0xb982a4: ldur            x1, [fp, #-0x40]
    // 0xb982a8: cmp             x1, x0
    // 0xb982ac: b.hs            #0xb99f54
    // 0xb982b0: LoadField: r1 = r3->field_f
    //     0xb982b0: ldur            w1, [x3, #0xf]
    // 0xb982b4: DecompressPointer r1
    //     0xb982b4: add             x1, x1, HEAP, lsl #32
    // 0xb982b8: ldur            x0, [fp, #-0x30]
    // 0xb982bc: ldur            x2, [fp, #-0x40]
    // 0xb982c0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb982c0: add             x25, x1, x2, lsl #2
    //     0xb982c4: add             x25, x25, #0xf
    //     0xb982c8: str             w0, [x25]
    //     0xb982cc: tbz             w0, #0, #0xb982e8
    //     0xb982d0: ldurb           w16, [x1, #-1]
    //     0xb982d4: ldurb           w17, [x0, #-1]
    //     0xb982d8: and             x16, x17, x16, lsr #2
    //     0xb982dc: tst             x16, HEAP, lsr #32
    //     0xb982e0: b.eq            #0xb982e8
    //     0xb982e4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb982e8: add             x0, x2, #1
    // 0xb982ec: mov             x2, x0
    // 0xb982f0: mov             x1, x3
    // 0xb982f4: b               #0xb98218
    // 0xb982f8: ldur            d0, [fp, #-0x58]
    // 0xb982fc: mov             x3, x1
    // 0xb98300: ldur            x1, [fp, #-0x18]
    // 0xb98304: r0 = Row()
    //     0xb98304: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb98308: mov             x1, x0
    // 0xb9830c: r0 = Instance_Axis
    //     0xb9830c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb98310: stur            x1, [fp, #-0x30]
    // 0xb98314: StoreField: r1->field_f = r0
    //     0xb98314: stur            w0, [x1, #0xf]
    // 0xb98318: r2 = Instance_MainAxisAlignment
    //     0xb98318: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb9831c: ldr             x2, [x2, #0xab0]
    // 0xb98320: StoreField: r1->field_13 = r2
    //     0xb98320: stur            w2, [x1, #0x13]
    // 0xb98324: r2 = Instance_MainAxisSize
    //     0xb98324: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb98328: ldr             x2, [x2, #0xa10]
    // 0xb9832c: ArrayStore: r1[0] = r2  ; List_4
    //     0xb9832c: stur            w2, [x1, #0x17]
    // 0xb98330: r3 = Instance_CrossAxisAlignment
    //     0xb98330: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb98334: ldr             x3, [x3, #0xa18]
    // 0xb98338: StoreField: r1->field_1b = r3
    //     0xb98338: stur            w3, [x1, #0x1b]
    // 0xb9833c: r4 = Instance_VerticalDirection
    //     0xb9833c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb98340: ldr             x4, [x4, #0xa20]
    // 0xb98344: StoreField: r1->field_23 = r4
    //     0xb98344: stur            w4, [x1, #0x23]
    // 0xb98348: r5 = Instance_Clip
    //     0xb98348: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9834c: ldr             x5, [x5, #0x38]
    // 0xb98350: StoreField: r1->field_2b = r5
    //     0xb98350: stur            w5, [x1, #0x2b]
    // 0xb98354: StoreField: r1->field_2f = rZR
    //     0xb98354: stur            xzr, [x1, #0x2f]
    // 0xb98358: ldur            x6, [fp, #-0x28]
    // 0xb9835c: StoreField: r1->field_b = r6
    //     0xb9835c: stur            w6, [x1, #0xb]
    // 0xb98360: ldur            d0, [fp, #-0x58]
    // 0xb98364: r6 = inline_Allocate_Double()
    //     0xb98364: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xb98368: add             x6, x6, #0x10
    //     0xb9836c: cmp             x7, x6
    //     0xb98370: b.ls            #0xb99f58
    //     0xb98374: str             x6, [THR, #0x50]  ; THR::top
    //     0xb98378: sub             x6, x6, #0xf
    //     0xb9837c: movz            x7, #0xe15c
    //     0xb98380: movk            x7, #0x3, lsl #16
    //     0xb98384: stur            x7, [x6, #-1]
    // 0xb98388: StoreField: r6->field_7 = d0
    //     0xb98388: stur            d0, [x6, #7]
    // 0xb9838c: stur            x6, [fp, #-0x10]
    // 0xb98390: r0 = SizedBox()
    //     0xb98390: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb98394: mov             x2, x0
    // 0xb98398: ldur            x0, [fp, #-0x10]
    // 0xb9839c: stur            x2, [fp, #-0x28]
    // 0xb983a0: StoreField: r2->field_f = r0
    //     0xb983a0: stur            w0, [x2, #0xf]
    // 0xb983a4: ldur            x0, [fp, #-0x30]
    // 0xb983a8: StoreField: r2->field_b = r0
    //     0xb983a8: stur            w0, [x2, #0xb]
    // 0xb983ac: r1 = <StackParentData>
    //     0xb983ac: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb983b0: ldr             x1, [x1, #0x8e0]
    // 0xb983b4: r0 = Positioned()
    //     0xb983b4: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb983b8: mov             x2, x0
    // 0xb983bc: r0 = 12.000000
    //     0xb983bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb983c0: ldr             x0, [x0, #0x9e8]
    // 0xb983c4: stur            x2, [fp, #-0x10]
    // 0xb983c8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb983c8: stur            w0, [x2, #0x17]
    // 0xb983cc: ldur            x1, [fp, #-0x28]
    // 0xb983d0: StoreField: r2->field_b = r1
    //     0xb983d0: stur            w1, [x2, #0xb]
    // 0xb983d4: ldur            x3, [fp, #-0x18]
    // 0xb983d8: LoadField: r1 = r3->field_b
    //     0xb983d8: ldur            w1, [x3, #0xb]
    // 0xb983dc: LoadField: r4 = r3->field_f
    //     0xb983dc: ldur            w4, [x3, #0xf]
    // 0xb983e0: DecompressPointer r4
    //     0xb983e0: add             x4, x4, HEAP, lsl #32
    // 0xb983e4: LoadField: r5 = r4->field_b
    //     0xb983e4: ldur            w5, [x4, #0xb]
    // 0xb983e8: r4 = LoadInt32Instr(r1)
    //     0xb983e8: sbfx            x4, x1, #1, #0x1f
    // 0xb983ec: stur            x4, [fp, #-0x40]
    // 0xb983f0: r1 = LoadInt32Instr(r5)
    //     0xb983f0: sbfx            x1, x5, #1, #0x1f
    // 0xb983f4: cmp             x4, x1
    // 0xb983f8: b.ne            #0xb98404
    // 0xb983fc: mov             x1, x3
    // 0xb98400: r0 = _growToNextCapacity()
    //     0xb98400: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb98404: ldur            x2, [fp, #-0x18]
    // 0xb98408: ldur            x3, [fp, #-0x40]
    // 0xb9840c: add             x0, x3, #1
    // 0xb98410: lsl             x1, x0, #1
    // 0xb98414: StoreField: r2->field_b = r1
    //     0xb98414: stur            w1, [x2, #0xb]
    // 0xb98418: LoadField: r1 = r2->field_f
    //     0xb98418: ldur            w1, [x2, #0xf]
    // 0xb9841c: DecompressPointer r1
    //     0xb9841c: add             x1, x1, HEAP, lsl #32
    // 0xb98420: ldur            x0, [fp, #-0x10]
    // 0xb98424: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb98424: add             x25, x1, x3, lsl #2
    //     0xb98428: add             x25, x25, #0xf
    //     0xb9842c: str             w0, [x25]
    //     0xb98430: tbz             w0, #0, #0xb9844c
    //     0xb98434: ldurb           w16, [x1, #-1]
    //     0xb98438: ldurb           w17, [x0, #-1]
    //     0xb9843c: and             x16, x17, x16, lsr #2
    //     0xb98440: tst             x16, HEAP, lsr #32
    //     0xb98444: b.eq            #0xb9844c
    //     0xb98448: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb9844c: r0 = GestureDetector()
    //     0xb9844c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb98450: r1 = Function '<anonymous closure>':.
    //     0xb98450: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f20] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb98454: ldr             x1, [x1, #0xf20]
    // 0xb98458: r2 = Null
    //     0xb98458: mov             x2, NULL
    // 0xb9845c: stur            x0, [fp, #-0x10]
    // 0xb98460: r0 = AllocateClosure()
    //     0xb98460: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb98464: r16 = Instance_Icon
    //     0xb98464: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e88] Obj!Icon@d665f1
    //     0xb98468: ldr             x16, [x16, #0xe88]
    // 0xb9846c: stp             x16, x0, [SP]
    // 0xb98470: ldur            x1, [fp, #-0x10]
    // 0xb98474: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb98474: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb98478: ldr             x4, [x4, #0xaf0]
    // 0xb9847c: r0 = GestureDetector()
    //     0xb9847c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb98480: r1 = <StackParentData>
    //     0xb98480: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb98484: ldr             x1, [x1, #0x8e0]
    // 0xb98488: r0 = Positioned()
    //     0xb98488: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb9848c: mov             x2, x0
    // 0xb98490: r0 = 24.000000
    //     0xb98490: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb98494: ldr             x0, [x0, #0xba8]
    // 0xb98498: stur            x2, [fp, #-0x28]
    // 0xb9849c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9849c: stur            w0, [x2, #0x17]
    // 0xb984a0: r0 = 12.000000
    //     0xb984a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb984a4: ldr             x0, [x0, #0x9e8]
    // 0xb984a8: StoreField: r2->field_1b = r0
    //     0xb984a8: stur            w0, [x2, #0x1b]
    // 0xb984ac: ldur            x0, [fp, #-0x10]
    // 0xb984b0: StoreField: r2->field_b = r0
    //     0xb984b0: stur            w0, [x2, #0xb]
    // 0xb984b4: ldur            x0, [fp, #-0x18]
    // 0xb984b8: LoadField: r1 = r0->field_b
    //     0xb984b8: ldur            w1, [x0, #0xb]
    // 0xb984bc: LoadField: r3 = r0->field_f
    //     0xb984bc: ldur            w3, [x0, #0xf]
    // 0xb984c0: DecompressPointer r3
    //     0xb984c0: add             x3, x3, HEAP, lsl #32
    // 0xb984c4: LoadField: r4 = r3->field_b
    //     0xb984c4: ldur            w4, [x3, #0xb]
    // 0xb984c8: r3 = LoadInt32Instr(r1)
    //     0xb984c8: sbfx            x3, x1, #1, #0x1f
    // 0xb984cc: stur            x3, [fp, #-0x40]
    // 0xb984d0: r1 = LoadInt32Instr(r4)
    //     0xb984d0: sbfx            x1, x4, #1, #0x1f
    // 0xb984d4: cmp             x3, x1
    // 0xb984d8: b.ne            #0xb984e4
    // 0xb984dc: mov             x1, x0
    // 0xb984e0: r0 = _growToNextCapacity()
    //     0xb984e0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb984e4: ldur            x4, [fp, #-8]
    // 0xb984e8: ldur            x2, [fp, #-0x18]
    // 0xb984ec: ldur            x3, [fp, #-0x40]
    // 0xb984f0: add             x0, x3, #1
    // 0xb984f4: lsl             x1, x0, #1
    // 0xb984f8: StoreField: r2->field_b = r1
    //     0xb984f8: stur            w1, [x2, #0xb]
    // 0xb984fc: LoadField: r1 = r2->field_f
    //     0xb984fc: ldur            w1, [x2, #0xf]
    // 0xb98500: DecompressPointer r1
    //     0xb98500: add             x1, x1, HEAP, lsl #32
    // 0xb98504: ldur            x0, [fp, #-0x28]
    // 0xb98508: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb98508: add             x25, x1, x3, lsl #2
    //     0xb9850c: add             x25, x25, #0xf
    //     0xb98510: str             w0, [x25]
    //     0xb98514: tbz             w0, #0, #0xb98530
    //     0xb98518: ldurb           w16, [x1, #-1]
    //     0xb9851c: ldurb           w17, [x0, #-1]
    //     0xb98520: and             x16, x17, x16, lsr #2
    //     0xb98524: tst             x16, HEAP, lsr #32
    //     0xb98528: b.eq            #0xb98530
    //     0xb9852c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb98530: LoadField: r0 = r4->field_23
    //     0xb98530: ldur            w0, [x4, #0x23]
    // 0xb98534: DecompressPointer r0
    //     0xb98534: add             x0, x0, HEAP, lsl #32
    // 0xb98538: tbnz            w0, #4, #0xb98fb8
    // 0xb9853c: ldur            x0, [fp, #-0x20]
    // 0xb98540: LoadField: r1 = r0->field_13
    //     0xb98540: ldur            w1, [x0, #0x13]
    // 0xb98544: DecompressPointer r1
    //     0xb98544: add             x1, x1, HEAP, lsl #32
    // 0xb98548: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb98548: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb9854c: r0 = _of()
    //     0xb9854c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb98550: LoadField: r1 = r0->field_7
    //     0xb98550: ldur            w1, [x0, #7]
    // 0xb98554: DecompressPointer r1
    //     0xb98554: add             x1, x1, HEAP, lsl #32
    // 0xb98558: LoadField: d1 = r1->field_7
    //     0xb98558: ldur            d1, [x1, #7]
    // 0xb9855c: stur            d1, [fp, #-0x58]
    // 0xb98560: r1 = Instance_Color
    //     0xb98560: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb98564: d0 = 0.700000
    //     0xb98564: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb98568: ldr             d0, [x17, #0xf48]
    // 0xb9856c: r0 = withOpacity()
    //     0xb9856c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb98570: stur            x0, [fp, #-0x10]
    // 0xb98574: r0 = BoxDecoration()
    //     0xb98574: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb98578: mov             x2, x0
    // 0xb9857c: ldur            x0, [fp, #-0x10]
    // 0xb98580: stur            x2, [fp, #-0x28]
    // 0xb98584: StoreField: r2->field_7 = r0
    //     0xb98584: stur            w0, [x2, #7]
    // 0xb98588: r0 = Instance_BorderRadius
    //     0xb98588: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cbf8] Obj!BorderRadius@d5a201
    //     0xb9858c: ldr             x0, [x0, #0xbf8]
    // 0xb98590: StoreField: r2->field_13 = r0
    //     0xb98590: stur            w0, [x2, #0x13]
    // 0xb98594: r0 = Instance_BoxShape
    //     0xb98594: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb98598: ldr             x0, [x0, #0x80]
    // 0xb9859c: StoreField: r2->field_23 = r0
    //     0xb9859c: stur            w0, [x2, #0x23]
    // 0xb985a0: r1 = Instance_Color
    //     0xb985a0: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb985a4: d0 = 0.500000
    //     0xb985a4: fmov            d0, #0.50000000
    // 0xb985a8: r0 = withOpacity()
    //     0xb985a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb985ac: stur            x0, [fp, #-0x10]
    // 0xb985b0: r0 = BoxDecoration()
    //     0xb985b0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb985b4: mov             x1, x0
    // 0xb985b8: ldur            x0, [fp, #-0x10]
    // 0xb985bc: stur            x1, [fp, #-0x30]
    // 0xb985c0: StoreField: r1->field_7 = r0
    //     0xb985c0: stur            w0, [x1, #7]
    // 0xb985c4: r0 = Instance_BoxShape
    //     0xb985c4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb985c8: ldr             x0, [x0, #0x970]
    // 0xb985cc: StoreField: r1->field_23 = r0
    //     0xb985cc: stur            w0, [x1, #0x23]
    // 0xb985d0: ldur            x2, [fp, #-8]
    // 0xb985d4: LoadField: r3 = r2->field_b
    //     0xb985d4: ldur            w3, [x2, #0xb]
    // 0xb985d8: DecompressPointer r3
    //     0xb985d8: add             x3, x3, HEAP, lsl #32
    // 0xb985dc: cmp             w3, NULL
    // 0xb985e0: b.eq            #0xb99f84
    // 0xb985e4: LoadField: r4 = r3->field_b
    //     0xb985e4: ldur            w4, [x3, #0xb]
    // 0xb985e8: DecompressPointer r4
    //     0xb985e8: add             x4, x4, HEAP, lsl #32
    // 0xb985ec: cmp             w4, NULL
    // 0xb985f0: b.ne            #0xb985fc
    // 0xb985f4: r0 = Null
    //     0xb985f4: mov             x0, NULL
    // 0xb985f8: b               #0xb98638
    // 0xb985fc: LoadField: r3 = r4->field_7
    //     0xb985fc: ldur            w3, [x4, #7]
    // 0xb98600: DecompressPointer r3
    //     0xb98600: add             x3, x3, HEAP, lsl #32
    // 0xb98604: cmp             w3, NULL
    // 0xb98608: b.ne            #0xb98614
    // 0xb9860c: r0 = Null
    //     0xb9860c: mov             x0, NULL
    // 0xb98610: b               #0xb98638
    // 0xb98614: stp             xzr, x3, [SP]
    // 0xb98618: r0 = []()
    //     0xb98618: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb9861c: r1 = LoadClassIdInstr(r0)
    //     0xb9861c: ldur            x1, [x0, #-1]
    //     0xb98620: ubfx            x1, x1, #0xc, #0x14
    // 0xb98624: str             x0, [SP]
    // 0xb98628: mov             x0, x1
    // 0xb9862c: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb9862c: sub             lr, x0, #1, lsl #12
    //     0xb98630: ldr             lr, [x21, lr, lsl #3]
    //     0xb98634: blr             lr
    // 0xb98638: cmp             w0, NULL
    // 0xb9863c: b.ne            #0xb98648
    // 0xb98640: r3 = ""
    //     0xb98640: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb98644: b               #0xb9864c
    // 0xb98648: mov             x3, x0
    // 0xb9864c: ldur            x0, [fp, #-8]
    // 0xb98650: ldur            x2, [fp, #-0x20]
    // 0xb98654: stur            x3, [fp, #-0x10]
    // 0xb98658: LoadField: r1 = r2->field_13
    //     0xb98658: ldur            w1, [x2, #0x13]
    // 0xb9865c: DecompressPointer r1
    //     0xb9865c: add             x1, x1, HEAP, lsl #32
    // 0xb98660: r0 = of()
    //     0xb98660: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb98664: LoadField: r1 = r0->field_87
    //     0xb98664: ldur            w1, [x0, #0x87]
    // 0xb98668: DecompressPointer r1
    //     0xb98668: add             x1, x1, HEAP, lsl #32
    // 0xb9866c: LoadField: r0 = r1->field_7
    //     0xb9866c: ldur            w0, [x1, #7]
    // 0xb98670: DecompressPointer r0
    //     0xb98670: add             x0, x0, HEAP, lsl #32
    // 0xb98674: r16 = 16.000000
    //     0xb98674: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb98678: ldr             x16, [x16, #0x188]
    // 0xb9867c: r30 = Instance_Color
    //     0xb9867c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb98680: stp             lr, x16, [SP]
    // 0xb98684: mov             x1, x0
    // 0xb98688: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb98688: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9868c: ldr             x4, [x4, #0xaa0]
    // 0xb98690: r0 = copyWith()
    //     0xb98690: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb98694: stur            x0, [fp, #-0x38]
    // 0xb98698: r0 = Text()
    //     0xb98698: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9869c: mov             x1, x0
    // 0xb986a0: ldur            x0, [fp, #-0x10]
    // 0xb986a4: stur            x1, [fp, #-0x48]
    // 0xb986a8: StoreField: r1->field_b = r0
    //     0xb986a8: stur            w0, [x1, #0xb]
    // 0xb986ac: ldur            x0, [fp, #-0x38]
    // 0xb986b0: StoreField: r1->field_13 = r0
    //     0xb986b0: stur            w0, [x1, #0x13]
    // 0xb986b4: r0 = Center()
    //     0xb986b4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb986b8: mov             x1, x0
    // 0xb986bc: r0 = Instance_Alignment
    //     0xb986bc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb986c0: ldr             x0, [x0, #0xb10]
    // 0xb986c4: stur            x1, [fp, #-0x10]
    // 0xb986c8: StoreField: r1->field_f = r0
    //     0xb986c8: stur            w0, [x1, #0xf]
    // 0xb986cc: ldur            x2, [fp, #-0x48]
    // 0xb986d0: StoreField: r1->field_b = r2
    //     0xb986d0: stur            w2, [x1, #0xb]
    // 0xb986d4: r0 = Container()
    //     0xb986d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb986d8: stur            x0, [fp, #-0x38]
    // 0xb986dc: r16 = 34.000000
    //     0xb986dc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb986e0: ldr             x16, [x16, #0x978]
    // 0xb986e4: r30 = 34.000000
    //     0xb986e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb986e8: ldr             lr, [lr, #0x978]
    // 0xb986ec: stp             lr, x16, [SP, #0x18]
    // 0xb986f0: r16 = Instance_EdgeInsets
    //     0xb986f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb986f4: ldr             x16, [x16, #0x980]
    // 0xb986f8: ldur            lr, [fp, #-0x30]
    // 0xb986fc: stp             lr, x16, [SP, #8]
    // 0xb98700: ldur            x16, [fp, #-0x10]
    // 0xb98704: str             x16, [SP]
    // 0xb98708: mov             x1, x0
    // 0xb9870c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb9870c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb98710: ldr             x4, [x4, #0x988]
    // 0xb98714: r0 = Container()
    //     0xb98714: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb98718: ldur            x0, [fp, #-8]
    // 0xb9871c: LoadField: r1 = r0->field_b
    //     0xb9871c: ldur            w1, [x0, #0xb]
    // 0xb98720: DecompressPointer r1
    //     0xb98720: add             x1, x1, HEAP, lsl #32
    // 0xb98724: cmp             w1, NULL
    // 0xb98728: b.eq            #0xb99f88
    // 0xb9872c: LoadField: r2 = r1->field_b
    //     0xb9872c: ldur            w2, [x1, #0xb]
    // 0xb98730: DecompressPointer r2
    //     0xb98730: add             x2, x2, HEAP, lsl #32
    // 0xb98734: cmp             w2, NULL
    // 0xb98738: b.ne            #0xb98744
    // 0xb9873c: r1 = Null
    //     0xb9873c: mov             x1, NULL
    // 0xb98740: b               #0xb9874c
    // 0xb98744: LoadField: r1 = r2->field_7
    //     0xb98744: ldur            w1, [x2, #7]
    // 0xb98748: DecompressPointer r1
    //     0xb98748: add             x1, x1, HEAP, lsl #32
    // 0xb9874c: cmp             w1, NULL
    // 0xb98750: b.ne            #0xb9875c
    // 0xb98754: r3 = ""
    //     0xb98754: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb98758: b               #0xb98760
    // 0xb9875c: mov             x3, x1
    // 0xb98760: ldur            x2, [fp, #-0x20]
    // 0xb98764: stur            x3, [fp, #-0x10]
    // 0xb98768: LoadField: r1 = r2->field_13
    //     0xb98768: ldur            w1, [x2, #0x13]
    // 0xb9876c: DecompressPointer r1
    //     0xb9876c: add             x1, x1, HEAP, lsl #32
    // 0xb98770: r0 = of()
    //     0xb98770: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb98774: LoadField: r1 = r0->field_87
    //     0xb98774: ldur            w1, [x0, #0x87]
    // 0xb98778: DecompressPointer r1
    //     0xb98778: add             x1, x1, HEAP, lsl #32
    // 0xb9877c: LoadField: r0 = r1->field_7
    //     0xb9877c: ldur            w0, [x1, #7]
    // 0xb98780: DecompressPointer r0
    //     0xb98780: add             x0, x0, HEAP, lsl #32
    // 0xb98784: r16 = 14.000000
    //     0xb98784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb98788: ldr             x16, [x16, #0x1d8]
    // 0xb9878c: r30 = Instance_Color
    //     0xb9878c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb98790: stp             lr, x16, [SP]
    // 0xb98794: mov             x1, x0
    // 0xb98798: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb98798: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9879c: ldr             x4, [x4, #0xaa0]
    // 0xb987a0: r0 = copyWith()
    //     0xb987a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb987a4: stur            x0, [fp, #-0x30]
    // 0xb987a8: r0 = Text()
    //     0xb987a8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb987ac: mov             x2, x0
    // 0xb987b0: ldur            x0, [fp, #-0x10]
    // 0xb987b4: stur            x2, [fp, #-0x48]
    // 0xb987b8: StoreField: r2->field_b = r0
    //     0xb987b8: stur            w0, [x2, #0xb]
    // 0xb987bc: ldur            x0, [fp, #-0x30]
    // 0xb987c0: StoreField: r2->field_13 = r0
    //     0xb987c0: stur            w0, [x2, #0x13]
    // 0xb987c4: ldur            x0, [fp, #-8]
    // 0xb987c8: LoadField: r1 = r0->field_b
    //     0xb987c8: ldur            w1, [x0, #0xb]
    // 0xb987cc: DecompressPointer r1
    //     0xb987cc: add             x1, x1, HEAP, lsl #32
    // 0xb987d0: cmp             w1, NULL
    // 0xb987d4: b.eq            #0xb99f8c
    // 0xb987d8: LoadField: r3 = r1->field_b
    //     0xb987d8: ldur            w3, [x1, #0xb]
    // 0xb987dc: DecompressPointer r3
    //     0xb987dc: add             x3, x3, HEAP, lsl #32
    // 0xb987e0: cmp             w3, NULL
    // 0xb987e4: b.ne            #0xb987f0
    // 0xb987e8: r1 = Null
    //     0xb987e8: mov             x1, NULL
    // 0xb987ec: b               #0xb987f8
    // 0xb987f0: LoadField: r1 = r3->field_1f
    //     0xb987f0: ldur            w1, [x3, #0x1f]
    // 0xb987f4: DecompressPointer r1
    //     0xb987f4: add             x1, x1, HEAP, lsl #32
    // 0xb987f8: cmp             w1, NULL
    // 0xb987fc: b.ne            #0xb98808
    // 0xb98800: r4 = ""
    //     0xb98800: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb98804: b               #0xb9880c
    // 0xb98808: mov             x4, x1
    // 0xb9880c: ldur            x3, [fp, #-0x20]
    // 0xb98810: stur            x4, [fp, #-0x10]
    // 0xb98814: LoadField: r1 = r3->field_13
    //     0xb98814: ldur            w1, [x3, #0x13]
    // 0xb98818: DecompressPointer r1
    //     0xb98818: add             x1, x1, HEAP, lsl #32
    // 0xb9881c: r0 = of()
    //     0xb9881c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb98820: LoadField: r1 = r0->field_87
    //     0xb98820: ldur            w1, [x0, #0x87]
    // 0xb98824: DecompressPointer r1
    //     0xb98824: add             x1, x1, HEAP, lsl #32
    // 0xb98828: LoadField: r0 = r1->field_33
    //     0xb98828: ldur            w0, [x1, #0x33]
    // 0xb9882c: DecompressPointer r0
    //     0xb9882c: add             x0, x0, HEAP, lsl #32
    // 0xb98830: stur            x0, [fp, #-0x30]
    // 0xb98834: cmp             w0, NULL
    // 0xb98838: b.ne            #0xb98844
    // 0xb9883c: r4 = Null
    //     0xb9883c: mov             x4, NULL
    // 0xb98840: b               #0xb9886c
    // 0xb98844: r1 = Instance_Color
    //     0xb98844: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb98848: d0 = 0.500000
    //     0xb98848: fmov            d0, #0.50000000
    // 0xb9884c: r0 = withOpacity()
    //     0xb9884c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb98850: r16 = 10.000000
    //     0xb98850: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb98854: stp             x0, x16, [SP]
    // 0xb98858: ldur            x1, [fp, #-0x30]
    // 0xb9885c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9885c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb98860: ldr             x4, [x4, #0xaa0]
    // 0xb98864: r0 = copyWith()
    //     0xb98864: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb98868: mov             x4, x0
    // 0xb9886c: ldur            x1, [fp, #-8]
    // 0xb98870: ldur            x3, [fp, #-0x38]
    // 0xb98874: ldur            x0, [fp, #-0x48]
    // 0xb98878: ldur            x2, [fp, #-0x10]
    // 0xb9887c: stur            x4, [fp, #-0x30]
    // 0xb98880: r0 = Text()
    //     0xb98880: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb98884: mov             x1, x0
    // 0xb98888: ldur            x0, [fp, #-0x10]
    // 0xb9888c: stur            x1, [fp, #-0x50]
    // 0xb98890: StoreField: r1->field_b = r0
    //     0xb98890: stur            w0, [x1, #0xb]
    // 0xb98894: ldur            x0, [fp, #-0x30]
    // 0xb98898: StoreField: r1->field_13 = r0
    //     0xb98898: stur            w0, [x1, #0x13]
    // 0xb9889c: r0 = Padding()
    //     0xb9889c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb988a0: mov             x3, x0
    // 0xb988a4: r0 = Instance_EdgeInsets
    //     0xb988a4: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb988a8: ldr             x0, [x0, #0xe90]
    // 0xb988ac: stur            x3, [fp, #-0x10]
    // 0xb988b0: StoreField: r3->field_f = r0
    //     0xb988b0: stur            w0, [x3, #0xf]
    // 0xb988b4: ldur            x1, [fp, #-0x50]
    // 0xb988b8: StoreField: r3->field_b = r1
    //     0xb988b8: stur            w1, [x3, #0xb]
    // 0xb988bc: r1 = Null
    //     0xb988bc: mov             x1, NULL
    // 0xb988c0: r2 = 4
    //     0xb988c0: movz            x2, #0x4
    // 0xb988c4: r0 = AllocateArray()
    //     0xb988c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb988c8: mov             x2, x0
    // 0xb988cc: ldur            x0, [fp, #-0x48]
    // 0xb988d0: stur            x2, [fp, #-0x30]
    // 0xb988d4: StoreField: r2->field_f = r0
    //     0xb988d4: stur            w0, [x2, #0xf]
    // 0xb988d8: ldur            x0, [fp, #-0x10]
    // 0xb988dc: StoreField: r2->field_13 = r0
    //     0xb988dc: stur            w0, [x2, #0x13]
    // 0xb988e0: r1 = <Widget>
    //     0xb988e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb988e4: r0 = AllocateGrowableArray()
    //     0xb988e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb988e8: mov             x1, x0
    // 0xb988ec: ldur            x0, [fp, #-0x30]
    // 0xb988f0: stur            x1, [fp, #-0x10]
    // 0xb988f4: StoreField: r1->field_f = r0
    //     0xb988f4: stur            w0, [x1, #0xf]
    // 0xb988f8: r2 = 4
    //     0xb988f8: movz            x2, #0x4
    // 0xb988fc: StoreField: r1->field_b = r2
    //     0xb988fc: stur            w2, [x1, #0xb]
    // 0xb98900: r0 = Column()
    //     0xb98900: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb98904: mov             x3, x0
    // 0xb98908: r0 = Instance_Axis
    //     0xb98908: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb9890c: stur            x3, [fp, #-0x30]
    // 0xb98910: StoreField: r3->field_f = r0
    //     0xb98910: stur            w0, [x3, #0xf]
    // 0xb98914: r4 = Instance_MainAxisAlignment
    //     0xb98914: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb98918: ldr             x4, [x4, #0xa08]
    // 0xb9891c: StoreField: r3->field_13 = r4
    //     0xb9891c: stur            w4, [x3, #0x13]
    // 0xb98920: r5 = Instance_MainAxisSize
    //     0xb98920: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb98924: ldr             x5, [x5, #0xa10]
    // 0xb98928: ArrayStore: r3[0] = r5  ; List_4
    //     0xb98928: stur            w5, [x3, #0x17]
    // 0xb9892c: r6 = Instance_CrossAxisAlignment
    //     0xb9892c: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb98930: ldr             x6, [x6, #0x890]
    // 0xb98934: StoreField: r3->field_1b = r6
    //     0xb98934: stur            w6, [x3, #0x1b]
    // 0xb98938: r7 = Instance_VerticalDirection
    //     0xb98938: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9893c: ldr             x7, [x7, #0xa20]
    // 0xb98940: StoreField: r3->field_23 = r7
    //     0xb98940: stur            w7, [x3, #0x23]
    // 0xb98944: r8 = Instance_Clip
    //     0xb98944: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb98948: ldr             x8, [x8, #0x38]
    // 0xb9894c: StoreField: r3->field_2b = r8
    //     0xb9894c: stur            w8, [x3, #0x2b]
    // 0xb98950: StoreField: r3->field_2f = rZR
    //     0xb98950: stur            xzr, [x3, #0x2f]
    // 0xb98954: ldur            x1, [fp, #-0x10]
    // 0xb98958: StoreField: r3->field_b = r1
    //     0xb98958: stur            w1, [x3, #0xb]
    // 0xb9895c: r1 = Null
    //     0xb9895c: mov             x1, NULL
    // 0xb98960: r2 = 6
    //     0xb98960: movz            x2, #0x6
    // 0xb98964: r0 = AllocateArray()
    //     0xb98964: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb98968: mov             x2, x0
    // 0xb9896c: ldur            x0, [fp, #-0x38]
    // 0xb98970: stur            x2, [fp, #-0x10]
    // 0xb98974: StoreField: r2->field_f = r0
    //     0xb98974: stur            w0, [x2, #0xf]
    // 0xb98978: r16 = Instance_SizedBox
    //     0xb98978: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb9897c: ldr             x16, [x16, #0x998]
    // 0xb98980: StoreField: r2->field_13 = r16
    //     0xb98980: stur            w16, [x2, #0x13]
    // 0xb98984: ldur            x0, [fp, #-0x30]
    // 0xb98988: ArrayStore: r2[0] = r0  ; List_4
    //     0xb98988: stur            w0, [x2, #0x17]
    // 0xb9898c: r1 = <Widget>
    //     0xb9898c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb98990: r0 = AllocateGrowableArray()
    //     0xb98990: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb98994: mov             x1, x0
    // 0xb98998: ldur            x0, [fp, #-0x10]
    // 0xb9899c: stur            x1, [fp, #-0x30]
    // 0xb989a0: StoreField: r1->field_f = r0
    //     0xb989a0: stur            w0, [x1, #0xf]
    // 0xb989a4: r2 = 6
    //     0xb989a4: movz            x2, #0x6
    // 0xb989a8: StoreField: r1->field_b = r2
    //     0xb989a8: stur            w2, [x1, #0xb]
    // 0xb989ac: r0 = Row()
    //     0xb989ac: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb989b0: mov             x2, x0
    // 0xb989b4: r1 = Instance_Axis
    //     0xb989b4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb989b8: stur            x2, [fp, #-0x10]
    // 0xb989bc: StoreField: r2->field_f = r1
    //     0xb989bc: stur            w1, [x2, #0xf]
    // 0xb989c0: r3 = Instance_MainAxisAlignment
    //     0xb989c0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb989c4: ldr             x3, [x3, #0xa08]
    // 0xb989c8: StoreField: r2->field_13 = r3
    //     0xb989c8: stur            w3, [x2, #0x13]
    // 0xb989cc: r4 = Instance_MainAxisSize
    //     0xb989cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb989d0: ldr             x4, [x4, #0xa10]
    // 0xb989d4: ArrayStore: r2[0] = r4  ; List_4
    //     0xb989d4: stur            w4, [x2, #0x17]
    // 0xb989d8: r5 = Instance_CrossAxisAlignment
    //     0xb989d8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb989dc: ldr             x5, [x5, #0xa18]
    // 0xb989e0: StoreField: r2->field_1b = r5
    //     0xb989e0: stur            w5, [x2, #0x1b]
    // 0xb989e4: r6 = Instance_VerticalDirection
    //     0xb989e4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb989e8: ldr             x6, [x6, #0xa20]
    // 0xb989ec: StoreField: r2->field_23 = r6
    //     0xb989ec: stur            w6, [x2, #0x23]
    // 0xb989f0: r7 = Instance_Clip
    //     0xb989f0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb989f4: ldr             x7, [x7, #0x38]
    // 0xb989f8: StoreField: r2->field_2b = r7
    //     0xb989f8: stur            w7, [x2, #0x2b]
    // 0xb989fc: StoreField: r2->field_2f = rZR
    //     0xb989fc: stur            xzr, [x2, #0x2f]
    // 0xb98a00: ldur            x0, [fp, #-0x30]
    // 0xb98a04: StoreField: r2->field_b = r0
    //     0xb98a04: stur            w0, [x2, #0xb]
    // 0xb98a08: ldur            x8, [fp, #-8]
    // 0xb98a0c: LoadField: r0 = r8->field_b
    //     0xb98a0c: ldur            w0, [x8, #0xb]
    // 0xb98a10: DecompressPointer r0
    //     0xb98a10: add             x0, x0, HEAP, lsl #32
    // 0xb98a14: cmp             w0, NULL
    // 0xb98a18: b.eq            #0xb99f90
    // 0xb98a1c: LoadField: r9 = r0->field_b
    //     0xb98a1c: ldur            w9, [x0, #0xb]
    // 0xb98a20: DecompressPointer r9
    //     0xb98a20: add             x9, x9, HEAP, lsl #32
    // 0xb98a24: cmp             w9, NULL
    // 0xb98a28: b.ne            #0xb98a34
    // 0xb98a2c: r0 = Null
    //     0xb98a2c: mov             x0, NULL
    // 0xb98a30: b               #0xb98a68
    // 0xb98a34: LoadField: r0 = r9->field_f
    //     0xb98a34: ldur            w0, [x9, #0xf]
    // 0xb98a38: DecompressPointer r0
    //     0xb98a38: add             x0, x0, HEAP, lsl #32
    // 0xb98a3c: r9 = 60
    //     0xb98a3c: movz            x9, #0x3c
    // 0xb98a40: branchIfSmi(r0, 0xb98a4c)
    //     0xb98a40: tbz             w0, #0, #0xb98a4c
    // 0xb98a44: r9 = LoadClassIdInstr(r0)
    //     0xb98a44: ldur            x9, [x0, #-1]
    //     0xb98a48: ubfx            x9, x9, #0xc, #0x14
    // 0xb98a4c: str             x0, [SP]
    // 0xb98a50: mov             x0, x9
    // 0xb98a54: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb98a54: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb98a58: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb98a58: movz            x17, #0x2700
    //     0xb98a5c: add             lr, x0, x17
    //     0xb98a60: ldr             lr, [x21, lr, lsl #3]
    //     0xb98a64: blr             lr
    // 0xb98a68: cmp             w0, NULL
    // 0xb98a6c: b.ne            #0xb98a78
    // 0xb98a70: r1 = ""
    //     0xb98a70: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb98a74: b               #0xb98a7c
    // 0xb98a78: mov             x1, x0
    // 0xb98a7c: r0 = parse()
    //     0xb98a7c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb98a80: mov             v1.16b, v0.16b
    // 0xb98a84: d0 = 4.000000
    //     0xb98a84: fmov            d0, #4.00000000
    // 0xb98a88: fcmp            d1, d0
    // 0xb98a8c: b.lt            #0xb98aa0
    // 0xb98a90: r1 = Instance_Color
    //     0xb98a90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb98a94: ldr             x1, [x1, #0x858]
    // 0xb98a98: d0 = 2.000000
    //     0xb98a98: fmov            d0, #2.00000000
    // 0xb98a9c: b               #0xb98be0
    // 0xb98aa0: ldur            x1, [fp, #-8]
    // 0xb98aa4: LoadField: r0 = r1->field_b
    //     0xb98aa4: ldur            w0, [x1, #0xb]
    // 0xb98aa8: DecompressPointer r0
    //     0xb98aa8: add             x0, x0, HEAP, lsl #32
    // 0xb98aac: cmp             w0, NULL
    // 0xb98ab0: b.eq            #0xb99f94
    // 0xb98ab4: LoadField: r2 = r0->field_b
    //     0xb98ab4: ldur            w2, [x0, #0xb]
    // 0xb98ab8: DecompressPointer r2
    //     0xb98ab8: add             x2, x2, HEAP, lsl #32
    // 0xb98abc: cmp             w2, NULL
    // 0xb98ac0: b.ne            #0xb98acc
    // 0xb98ac4: r0 = Null
    //     0xb98ac4: mov             x0, NULL
    // 0xb98ac8: b               #0xb98b00
    // 0xb98acc: LoadField: r0 = r2->field_f
    //     0xb98acc: ldur            w0, [x2, #0xf]
    // 0xb98ad0: DecompressPointer r0
    //     0xb98ad0: add             x0, x0, HEAP, lsl #32
    // 0xb98ad4: r2 = 60
    //     0xb98ad4: movz            x2, #0x3c
    // 0xb98ad8: branchIfSmi(r0, 0xb98ae4)
    //     0xb98ad8: tbz             w0, #0, #0xb98ae4
    // 0xb98adc: r2 = LoadClassIdInstr(r0)
    //     0xb98adc: ldur            x2, [x0, #-1]
    //     0xb98ae0: ubfx            x2, x2, #0xc, #0x14
    // 0xb98ae4: str             x0, [SP]
    // 0xb98ae8: mov             x0, x2
    // 0xb98aec: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb98aec: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb98af0: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb98af0: movz            x17, #0x2700
    //     0xb98af4: add             lr, x0, x17
    //     0xb98af8: ldr             lr, [x21, lr, lsl #3]
    //     0xb98afc: blr             lr
    // 0xb98b00: cmp             w0, NULL
    // 0xb98b04: b.ne            #0xb98b10
    // 0xb98b08: r1 = ""
    //     0xb98b08: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb98b0c: b               #0xb98b14
    // 0xb98b10: mov             x1, x0
    // 0xb98b14: r0 = parse()
    //     0xb98b14: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb98b18: d1 = 3.500000
    //     0xb98b18: fmov            d1, #3.50000000
    // 0xb98b1c: fcmp            d0, d1
    // 0xb98b20: b.lt            #0xb98b40
    // 0xb98b24: r1 = Instance_Color
    //     0xb98b24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb98b28: ldr             x1, [x1, #0x858]
    // 0xb98b2c: d0 = 0.700000
    //     0xb98b2c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb98b30: ldr             d0, [x17, #0xf48]
    // 0xb98b34: r0 = withOpacity()
    //     0xb98b34: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb98b38: d0 = 2.000000
    //     0xb98b38: fmov            d0, #2.00000000
    // 0xb98b3c: b               #0xb98bdc
    // 0xb98b40: ldur            x1, [fp, #-8]
    // 0xb98b44: LoadField: r0 = r1->field_b
    //     0xb98b44: ldur            w0, [x1, #0xb]
    // 0xb98b48: DecompressPointer r0
    //     0xb98b48: add             x0, x0, HEAP, lsl #32
    // 0xb98b4c: cmp             w0, NULL
    // 0xb98b50: b.eq            #0xb99f98
    // 0xb98b54: LoadField: r2 = r0->field_b
    //     0xb98b54: ldur            w2, [x0, #0xb]
    // 0xb98b58: DecompressPointer r2
    //     0xb98b58: add             x2, x2, HEAP, lsl #32
    // 0xb98b5c: cmp             w2, NULL
    // 0xb98b60: b.ne            #0xb98b6c
    // 0xb98b64: r0 = Null
    //     0xb98b64: mov             x0, NULL
    // 0xb98b68: b               #0xb98ba0
    // 0xb98b6c: LoadField: r0 = r2->field_f
    //     0xb98b6c: ldur            w0, [x2, #0xf]
    // 0xb98b70: DecompressPointer r0
    //     0xb98b70: add             x0, x0, HEAP, lsl #32
    // 0xb98b74: r2 = 60
    //     0xb98b74: movz            x2, #0x3c
    // 0xb98b78: branchIfSmi(r0, 0xb98b84)
    //     0xb98b78: tbz             w0, #0, #0xb98b84
    // 0xb98b7c: r2 = LoadClassIdInstr(r0)
    //     0xb98b7c: ldur            x2, [x0, #-1]
    //     0xb98b80: ubfx            x2, x2, #0xc, #0x14
    // 0xb98b84: str             x0, [SP]
    // 0xb98b88: mov             x0, x2
    // 0xb98b8c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb98b8c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb98b90: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb98b90: movz            x17, #0x2700
    //     0xb98b94: add             lr, x0, x17
    //     0xb98b98: ldr             lr, [x21, lr, lsl #3]
    //     0xb98b9c: blr             lr
    // 0xb98ba0: cmp             w0, NULL
    // 0xb98ba4: b.ne            #0xb98bb0
    // 0xb98ba8: r1 = ""
    //     0xb98ba8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb98bac: b               #0xb98bb4
    // 0xb98bb0: mov             x1, x0
    // 0xb98bb4: r0 = parse()
    //     0xb98bb4: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb98bb8: mov             v1.16b, v0.16b
    // 0xb98bbc: d0 = 2.000000
    //     0xb98bbc: fmov            d0, #2.00000000
    // 0xb98bc0: fcmp            d1, d0
    // 0xb98bc4: b.lt            #0xb98bd4
    // 0xb98bc8: r0 = Instance_Color
    //     0xb98bc8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb98bcc: ldr             x0, [x0, #0x860]
    // 0xb98bd0: b               #0xb98bdc
    // 0xb98bd4: r0 = Instance_Color
    //     0xb98bd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb98bd8: ldr             x0, [x0, #0x50]
    // 0xb98bdc: mov             x1, x0
    // 0xb98be0: ldur            x0, [fp, #-8]
    // 0xb98be4: stur            x1, [fp, #-0x30]
    // 0xb98be8: r0 = ColorFilter()
    //     0xb98be8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb98bec: mov             x1, x0
    // 0xb98bf0: ldur            x0, [fp, #-0x30]
    // 0xb98bf4: stur            x1, [fp, #-0x38]
    // 0xb98bf8: StoreField: r1->field_7 = r0
    //     0xb98bf8: stur            w0, [x1, #7]
    // 0xb98bfc: r0 = Instance_BlendMode
    //     0xb98bfc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb98c00: ldr             x0, [x0, #0xb30]
    // 0xb98c04: StoreField: r1->field_b = r0
    //     0xb98c04: stur            w0, [x1, #0xb]
    // 0xb98c08: r2 = 1
    //     0xb98c08: movz            x2, #0x1
    // 0xb98c0c: StoreField: r1->field_13 = r2
    //     0xb98c0c: stur            x2, [x1, #0x13]
    // 0xb98c10: r0 = SvgPicture()
    //     0xb98c10: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb98c14: stur            x0, [fp, #-0x30]
    // 0xb98c18: ldur            x16, [fp, #-0x38]
    // 0xb98c1c: str             x16, [SP]
    // 0xb98c20: mov             x1, x0
    // 0xb98c24: r2 = "assets/images/green_star.svg"
    //     0xb98c24: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb98c28: ldr             x2, [x2, #0x9a0]
    // 0xb98c2c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb98c2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb98c30: ldr             x4, [x4, #0xa38]
    // 0xb98c34: r0 = SvgPicture.asset()
    //     0xb98c34: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb98c38: ldur            x1, [fp, #-8]
    // 0xb98c3c: LoadField: r0 = r1->field_b
    //     0xb98c3c: ldur            w0, [x1, #0xb]
    // 0xb98c40: DecompressPointer r0
    //     0xb98c40: add             x0, x0, HEAP, lsl #32
    // 0xb98c44: cmp             w0, NULL
    // 0xb98c48: b.eq            #0xb99f9c
    // 0xb98c4c: LoadField: r2 = r0->field_b
    //     0xb98c4c: ldur            w2, [x0, #0xb]
    // 0xb98c50: DecompressPointer r2
    //     0xb98c50: add             x2, x2, HEAP, lsl #32
    // 0xb98c54: cmp             w2, NULL
    // 0xb98c58: b.ne            #0xb98c64
    // 0xb98c5c: r0 = Null
    //     0xb98c5c: mov             x0, NULL
    // 0xb98c60: b               #0xb98c98
    // 0xb98c64: LoadField: r0 = r2->field_f
    //     0xb98c64: ldur            w0, [x2, #0xf]
    // 0xb98c68: DecompressPointer r0
    //     0xb98c68: add             x0, x0, HEAP, lsl #32
    // 0xb98c6c: r2 = 60
    //     0xb98c6c: movz            x2, #0x3c
    // 0xb98c70: branchIfSmi(r0, 0xb98c7c)
    //     0xb98c70: tbz             w0, #0, #0xb98c7c
    // 0xb98c74: r2 = LoadClassIdInstr(r0)
    //     0xb98c74: ldur            x2, [x0, #-1]
    //     0xb98c78: ubfx            x2, x2, #0xc, #0x14
    // 0xb98c7c: str             x0, [SP]
    // 0xb98c80: mov             x0, x2
    // 0xb98c84: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb98c84: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb98c88: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb98c88: movz            x17, #0x2700
    //     0xb98c8c: add             lr, x0, x17
    //     0xb98c90: ldr             lr, [x21, lr, lsl #3]
    //     0xb98c94: blr             lr
    // 0xb98c98: cmp             w0, NULL
    // 0xb98c9c: b.ne            #0xb98ca8
    // 0xb98ca0: r5 = ""
    //     0xb98ca0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb98ca4: b               #0xb98cac
    // 0xb98ca8: mov             x5, x0
    // 0xb98cac: ldur            x3, [fp, #-0x20]
    // 0xb98cb0: ldur            x2, [fp, #-0x10]
    // 0xb98cb4: ldur            x0, [fp, #-0x30]
    // 0xb98cb8: ldur            d0, [fp, #-0x58]
    // 0xb98cbc: ldur            x4, [fp, #-0x18]
    // 0xb98cc0: stur            x5, [fp, #-0x38]
    // 0xb98cc4: LoadField: r1 = r3->field_13
    //     0xb98cc4: ldur            w1, [x3, #0x13]
    // 0xb98cc8: DecompressPointer r1
    //     0xb98cc8: add             x1, x1, HEAP, lsl #32
    // 0xb98ccc: r0 = of()
    //     0xb98ccc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb98cd0: LoadField: r1 = r0->field_87
    //     0xb98cd0: ldur            w1, [x0, #0x87]
    // 0xb98cd4: DecompressPointer r1
    //     0xb98cd4: add             x1, x1, HEAP, lsl #32
    // 0xb98cd8: LoadField: r0 = r1->field_7
    //     0xb98cd8: ldur            w0, [x1, #7]
    // 0xb98cdc: DecompressPointer r0
    //     0xb98cdc: add             x0, x0, HEAP, lsl #32
    // 0xb98ce0: r16 = 12.000000
    //     0xb98ce0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb98ce4: ldr             x16, [x16, #0x9e8]
    // 0xb98ce8: r30 = Instance_Color
    //     0xb98ce8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb98cec: stp             lr, x16, [SP]
    // 0xb98cf0: mov             x1, x0
    // 0xb98cf4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb98cf4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb98cf8: ldr             x4, [x4, #0xaa0]
    // 0xb98cfc: r0 = copyWith()
    //     0xb98cfc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb98d00: stur            x0, [fp, #-0x48]
    // 0xb98d04: r0 = Text()
    //     0xb98d04: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb98d08: mov             x3, x0
    // 0xb98d0c: ldur            x0, [fp, #-0x38]
    // 0xb98d10: stur            x3, [fp, #-0x50]
    // 0xb98d14: StoreField: r3->field_b = r0
    //     0xb98d14: stur            w0, [x3, #0xb]
    // 0xb98d18: ldur            x0, [fp, #-0x48]
    // 0xb98d1c: StoreField: r3->field_13 = r0
    //     0xb98d1c: stur            w0, [x3, #0x13]
    // 0xb98d20: r1 = Null
    //     0xb98d20: mov             x1, NULL
    // 0xb98d24: r2 = 6
    //     0xb98d24: movz            x2, #0x6
    // 0xb98d28: r0 = AllocateArray()
    //     0xb98d28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb98d2c: mov             x2, x0
    // 0xb98d30: ldur            x0, [fp, #-0x30]
    // 0xb98d34: stur            x2, [fp, #-0x38]
    // 0xb98d38: StoreField: r2->field_f = r0
    //     0xb98d38: stur            w0, [x2, #0xf]
    // 0xb98d3c: r16 = Instance_SizedBox
    //     0xb98d3c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb98d40: ldr             x16, [x16, #0xe98]
    // 0xb98d44: StoreField: r2->field_13 = r16
    //     0xb98d44: stur            w16, [x2, #0x13]
    // 0xb98d48: ldur            x0, [fp, #-0x50]
    // 0xb98d4c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb98d4c: stur            w0, [x2, #0x17]
    // 0xb98d50: r1 = <Widget>
    //     0xb98d50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb98d54: r0 = AllocateGrowableArray()
    //     0xb98d54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb98d58: mov             x1, x0
    // 0xb98d5c: ldur            x0, [fp, #-0x38]
    // 0xb98d60: stur            x1, [fp, #-0x30]
    // 0xb98d64: StoreField: r1->field_f = r0
    //     0xb98d64: stur            w0, [x1, #0xf]
    // 0xb98d68: r2 = 6
    //     0xb98d68: movz            x2, #0x6
    // 0xb98d6c: StoreField: r1->field_b = r2
    //     0xb98d6c: stur            w2, [x1, #0xb]
    // 0xb98d70: r0 = Row()
    //     0xb98d70: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb98d74: mov             x1, x0
    // 0xb98d78: r0 = Instance_Axis
    //     0xb98d78: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb98d7c: stur            x1, [fp, #-0x38]
    // 0xb98d80: StoreField: r1->field_f = r0
    //     0xb98d80: stur            w0, [x1, #0xf]
    // 0xb98d84: r2 = Instance_MainAxisAlignment
    //     0xb98d84: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb98d88: ldr             x2, [x2, #0xa08]
    // 0xb98d8c: StoreField: r1->field_13 = r2
    //     0xb98d8c: stur            w2, [x1, #0x13]
    // 0xb98d90: r3 = Instance_MainAxisSize
    //     0xb98d90: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb98d94: ldr             x3, [x3, #0xa10]
    // 0xb98d98: ArrayStore: r1[0] = r3  ; List_4
    //     0xb98d98: stur            w3, [x1, #0x17]
    // 0xb98d9c: r4 = Instance_CrossAxisAlignment
    //     0xb98d9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb98da0: ldr             x4, [x4, #0xa18]
    // 0xb98da4: StoreField: r1->field_1b = r4
    //     0xb98da4: stur            w4, [x1, #0x1b]
    // 0xb98da8: r5 = Instance_VerticalDirection
    //     0xb98da8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb98dac: ldr             x5, [x5, #0xa20]
    // 0xb98db0: StoreField: r1->field_23 = r5
    //     0xb98db0: stur            w5, [x1, #0x23]
    // 0xb98db4: r6 = Instance_Clip
    //     0xb98db4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb98db8: ldr             x6, [x6, #0x38]
    // 0xb98dbc: StoreField: r1->field_2b = r6
    //     0xb98dbc: stur            w6, [x1, #0x2b]
    // 0xb98dc0: StoreField: r1->field_2f = rZR
    //     0xb98dc0: stur            xzr, [x1, #0x2f]
    // 0xb98dc4: ldur            x7, [fp, #-0x30]
    // 0xb98dc8: StoreField: r1->field_b = r7
    //     0xb98dc8: stur            w7, [x1, #0xb]
    // 0xb98dcc: r0 = Align()
    //     0xb98dcc: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb98dd0: mov             x3, x0
    // 0xb98dd4: r0 = Instance_Alignment
    //     0xb98dd4: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb98dd8: ldr             x0, [x0, #0xa78]
    // 0xb98ddc: stur            x3, [fp, #-0x30]
    // 0xb98de0: StoreField: r3->field_f = r0
    //     0xb98de0: stur            w0, [x3, #0xf]
    // 0xb98de4: ldur            x1, [fp, #-0x38]
    // 0xb98de8: StoreField: r3->field_b = r1
    //     0xb98de8: stur            w1, [x3, #0xb]
    // 0xb98dec: r1 = Null
    //     0xb98dec: mov             x1, NULL
    // 0xb98df0: r2 = 4
    //     0xb98df0: movz            x2, #0x4
    // 0xb98df4: r0 = AllocateArray()
    //     0xb98df4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb98df8: mov             x2, x0
    // 0xb98dfc: ldur            x0, [fp, #-0x10]
    // 0xb98e00: stur            x2, [fp, #-0x38]
    // 0xb98e04: StoreField: r2->field_f = r0
    //     0xb98e04: stur            w0, [x2, #0xf]
    // 0xb98e08: ldur            x0, [fp, #-0x30]
    // 0xb98e0c: StoreField: r2->field_13 = r0
    //     0xb98e0c: stur            w0, [x2, #0x13]
    // 0xb98e10: r1 = <Widget>
    //     0xb98e10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb98e14: r0 = AllocateGrowableArray()
    //     0xb98e14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb98e18: mov             x1, x0
    // 0xb98e1c: ldur            x0, [fp, #-0x38]
    // 0xb98e20: stur            x1, [fp, #-0x10]
    // 0xb98e24: StoreField: r1->field_f = r0
    //     0xb98e24: stur            w0, [x1, #0xf]
    // 0xb98e28: r2 = 4
    //     0xb98e28: movz            x2, #0x4
    // 0xb98e2c: StoreField: r1->field_b = r2
    //     0xb98e2c: stur            w2, [x1, #0xb]
    // 0xb98e30: r0 = Row()
    //     0xb98e30: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb98e34: mov             x1, x0
    // 0xb98e38: r0 = Instance_Axis
    //     0xb98e38: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb98e3c: stur            x1, [fp, #-0x30]
    // 0xb98e40: StoreField: r1->field_f = r0
    //     0xb98e40: stur            w0, [x1, #0xf]
    // 0xb98e44: r2 = Instance_MainAxisAlignment
    //     0xb98e44: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb98e48: ldr             x2, [x2, #0xa8]
    // 0xb98e4c: StoreField: r1->field_13 = r2
    //     0xb98e4c: stur            w2, [x1, #0x13]
    // 0xb98e50: r3 = Instance_MainAxisSize
    //     0xb98e50: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb98e54: ldr             x3, [x3, #0xa10]
    // 0xb98e58: ArrayStore: r1[0] = r3  ; List_4
    //     0xb98e58: stur            w3, [x1, #0x17]
    // 0xb98e5c: r4 = Instance_CrossAxisAlignment
    //     0xb98e5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb98e60: ldr             x4, [x4, #0xa18]
    // 0xb98e64: StoreField: r1->field_1b = r4
    //     0xb98e64: stur            w4, [x1, #0x1b]
    // 0xb98e68: r5 = Instance_VerticalDirection
    //     0xb98e68: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb98e6c: ldr             x5, [x5, #0xa20]
    // 0xb98e70: StoreField: r1->field_23 = r5
    //     0xb98e70: stur            w5, [x1, #0x23]
    // 0xb98e74: r6 = Instance_Clip
    //     0xb98e74: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb98e78: ldr             x6, [x6, #0x38]
    // 0xb98e7c: StoreField: r1->field_2b = r6
    //     0xb98e7c: stur            w6, [x1, #0x2b]
    // 0xb98e80: StoreField: r1->field_2f = rZR
    //     0xb98e80: stur            xzr, [x1, #0x2f]
    // 0xb98e84: ldur            x7, [fp, #-0x10]
    // 0xb98e88: StoreField: r1->field_b = r7
    //     0xb98e88: stur            w7, [x1, #0xb]
    // 0xb98e8c: r0 = Padding()
    //     0xb98e8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb98e90: mov             x1, x0
    // 0xb98e94: r0 = Instance_EdgeInsets
    //     0xb98e94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb98e98: ldr             x0, [x0, #0xd0]
    // 0xb98e9c: stur            x1, [fp, #-0x10]
    // 0xb98ea0: StoreField: r1->field_f = r0
    //     0xb98ea0: stur            w0, [x1, #0xf]
    // 0xb98ea4: ldur            x0, [fp, #-0x30]
    // 0xb98ea8: StoreField: r1->field_b = r0
    //     0xb98ea8: stur            w0, [x1, #0xb]
    // 0xb98eac: r0 = Container()
    //     0xb98eac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb98eb0: stur            x0, [fp, #-0x30]
    // 0xb98eb4: ldur            x16, [fp, #-0x28]
    // 0xb98eb8: ldur            lr, [fp, #-0x10]
    // 0xb98ebc: stp             lr, x16, [SP]
    // 0xb98ec0: mov             x1, x0
    // 0xb98ec4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb98ec4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb98ec8: ldr             x4, [x4, #0x88]
    // 0xb98ecc: r0 = Container()
    //     0xb98ecc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb98ed0: ldur            d0, [fp, #-0x58]
    // 0xb98ed4: r0 = inline_Allocate_Double()
    //     0xb98ed4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb98ed8: add             x0, x0, #0x10
    //     0xb98edc: cmp             x1, x0
    //     0xb98ee0: b.ls            #0xb99fa0
    //     0xb98ee4: str             x0, [THR, #0x50]  ; THR::top
    //     0xb98ee8: sub             x0, x0, #0xf
    //     0xb98eec: movz            x1, #0xe15c
    //     0xb98ef0: movk            x1, #0x3, lsl #16
    //     0xb98ef4: stur            x1, [x0, #-1]
    // 0xb98ef8: StoreField: r0->field_7 = d0
    //     0xb98ef8: stur            d0, [x0, #7]
    // 0xb98efc: stur            x0, [fp, #-0x10]
    // 0xb98f00: r0 = SizedBox()
    //     0xb98f00: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb98f04: mov             x2, x0
    // 0xb98f08: ldur            x0, [fp, #-0x10]
    // 0xb98f0c: stur            x2, [fp, #-0x28]
    // 0xb98f10: StoreField: r2->field_f = r0
    //     0xb98f10: stur            w0, [x2, #0xf]
    // 0xb98f14: ldur            x0, [fp, #-0x30]
    // 0xb98f18: StoreField: r2->field_b = r0
    //     0xb98f18: stur            w0, [x2, #0xb]
    // 0xb98f1c: r1 = <StackParentData>
    //     0xb98f1c: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb98f20: ldr             x1, [x1, #0x8e0]
    // 0xb98f24: r0 = Positioned()
    //     0xb98f24: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb98f28: mov             x2, x0
    // 0xb98f2c: r0 = 0.000000
    //     0xb98f2c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb98f30: stur            x2, [fp, #-0x10]
    // 0xb98f34: StoreField: r2->field_1f = r0
    //     0xb98f34: stur            w0, [x2, #0x1f]
    // 0xb98f38: ldur            x0, [fp, #-0x28]
    // 0xb98f3c: StoreField: r2->field_b = r0
    //     0xb98f3c: stur            w0, [x2, #0xb]
    // 0xb98f40: ldur            x0, [fp, #-0x18]
    // 0xb98f44: LoadField: r1 = r0->field_b
    //     0xb98f44: ldur            w1, [x0, #0xb]
    // 0xb98f48: LoadField: r3 = r0->field_f
    //     0xb98f48: ldur            w3, [x0, #0xf]
    // 0xb98f4c: DecompressPointer r3
    //     0xb98f4c: add             x3, x3, HEAP, lsl #32
    // 0xb98f50: LoadField: r4 = r3->field_b
    //     0xb98f50: ldur            w4, [x3, #0xb]
    // 0xb98f54: r3 = LoadInt32Instr(r1)
    //     0xb98f54: sbfx            x3, x1, #1, #0x1f
    // 0xb98f58: stur            x3, [fp, #-0x40]
    // 0xb98f5c: r1 = LoadInt32Instr(r4)
    //     0xb98f5c: sbfx            x1, x4, #1, #0x1f
    // 0xb98f60: cmp             x3, x1
    // 0xb98f64: b.ne            #0xb98f70
    // 0xb98f68: mov             x1, x0
    // 0xb98f6c: r0 = _growToNextCapacity()
    //     0xb98f6c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb98f70: ldur            x2, [fp, #-0x18]
    // 0xb98f74: ldur            x3, [fp, #-0x40]
    // 0xb98f78: add             x0, x3, #1
    // 0xb98f7c: lsl             x1, x0, #1
    // 0xb98f80: StoreField: r2->field_b = r1
    //     0xb98f80: stur            w1, [x2, #0xb]
    // 0xb98f84: LoadField: r1 = r2->field_f
    //     0xb98f84: ldur            w1, [x2, #0xf]
    // 0xb98f88: DecompressPointer r1
    //     0xb98f88: add             x1, x1, HEAP, lsl #32
    // 0xb98f8c: ldur            x0, [fp, #-0x10]
    // 0xb98f90: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb98f90: add             x25, x1, x3, lsl #2
    //     0xb98f94: add             x25, x25, #0xf
    //     0xb98f98: str             w0, [x25]
    //     0xb98f9c: tbz             w0, #0, #0xb98fb8
    //     0xb98fa0: ldurb           w16, [x1, #-1]
    //     0xb98fa4: ldurb           w17, [x0, #-1]
    //     0xb98fa8: and             x16, x17, x16, lsr #2
    //     0xb98fac: tst             x16, HEAP, lsr #32
    //     0xb98fb0: b.eq            #0xb98fb8
    //     0xb98fb4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb98fb8: ldur            x0, [fp, #-8]
    // 0xb98fbc: r0 = Stack()
    //     0xb98fbc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb98fc0: mov             x1, x0
    // 0xb98fc4: r0 = Instance_Alignment
    //     0xb98fc4: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb98fc8: ldr             x0, [x0, #0xce0]
    // 0xb98fcc: stur            x1, [fp, #-0x10]
    // 0xb98fd0: StoreField: r1->field_f = r0
    //     0xb98fd0: stur            w0, [x1, #0xf]
    // 0xb98fd4: r0 = Instance_StackFit
    //     0xb98fd4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb98fd8: ldr             x0, [x0, #0xfa8]
    // 0xb98fdc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb98fdc: stur            w0, [x1, #0x17]
    // 0xb98fe0: r0 = Instance_Clip
    //     0xb98fe0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb98fe4: ldr             x0, [x0, #0x7e0]
    // 0xb98fe8: StoreField: r1->field_1b = r0
    //     0xb98fe8: stur            w0, [x1, #0x1b]
    // 0xb98fec: ldur            x0, [fp, #-0x18]
    // 0xb98ff0: StoreField: r1->field_b = r0
    //     0xb98ff0: stur            w0, [x1, #0xb]
    // 0xb98ff4: r0 = ColoredBox()
    //     0xb98ff4: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb98ff8: mov             x2, x0
    // 0xb98ffc: r0 = Instance_Color
    //     0xb98ffc: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99000: stur            x2, [fp, #-0x18]
    // 0xb99004: StoreField: r2->field_f = r0
    //     0xb99004: stur            w0, [x2, #0xf]
    // 0xb99008: ldur            x1, [fp, #-0x10]
    // 0xb9900c: StoreField: r2->field_b = r1
    //     0xb9900c: stur            w1, [x2, #0xb]
    // 0xb99010: r1 = <FlexParentData>
    //     0xb99010: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb99014: ldr             x1, [x1, #0xe00]
    // 0xb99018: r0 = Expanded()
    //     0xb99018: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb9901c: mov             x3, x0
    // 0xb99020: r0 = 1
    //     0xb99020: movz            x0, #0x1
    // 0xb99024: stur            x3, [fp, #-0x10]
    // 0xb99028: StoreField: r3->field_13 = r0
    //     0xb99028: stur            x0, [x3, #0x13]
    // 0xb9902c: r1 = Instance_FlexFit
    //     0xb9902c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb99030: ldr             x1, [x1, #0xe08]
    // 0xb99034: StoreField: r3->field_1b = r1
    //     0xb99034: stur            w1, [x3, #0x1b]
    // 0xb99038: ldur            x1, [fp, #-0x18]
    // 0xb9903c: StoreField: r3->field_b = r1
    //     0xb9903c: stur            w1, [x3, #0xb]
    // 0xb99040: r1 = <Widget>
    //     0xb99040: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb99044: r2 = 0
    //     0xb99044: movz            x2, #0
    // 0xb99048: r0 = _GrowableList()
    //     0xb99048: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb9904c: mov             x2, x0
    // 0xb99050: ldur            x0, [fp, #-8]
    // 0xb99054: stur            x2, [fp, #-0x18]
    // 0xb99058: LoadField: r1 = r0->field_23
    //     0xb99058: ldur            w1, [x0, #0x23]
    // 0xb9905c: DecompressPointer r1
    //     0xb9905c: add             x1, x1, HEAP, lsl #32
    // 0xb99060: tbz             w1, #4, #0xb999e0
    // 0xb99064: r1 = Instance_Color
    //     0xb99064: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99068: d0 = 0.050000
    //     0xb99068: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xb9906c: r0 = withOpacity()
    //     0xb9906c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb99070: stur            x0, [fp, #-0x28]
    // 0xb99074: r0 = BoxDecoration()
    //     0xb99074: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb99078: mov             x1, x0
    // 0xb9907c: ldur            x0, [fp, #-0x28]
    // 0xb99080: stur            x1, [fp, #-0x30]
    // 0xb99084: StoreField: r1->field_7 = r0
    //     0xb99084: stur            w0, [x1, #7]
    // 0xb99088: r0 = Instance_BoxShape
    //     0xb99088: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb9908c: ldr             x0, [x0, #0x970]
    // 0xb99090: StoreField: r1->field_23 = r0
    //     0xb99090: stur            w0, [x1, #0x23]
    // 0xb99094: ldur            x0, [fp, #-8]
    // 0xb99098: LoadField: r2 = r0->field_b
    //     0xb99098: ldur            w2, [x0, #0xb]
    // 0xb9909c: DecompressPointer r2
    //     0xb9909c: add             x2, x2, HEAP, lsl #32
    // 0xb990a0: cmp             w2, NULL
    // 0xb990a4: b.eq            #0xb99fb0
    // 0xb990a8: LoadField: r3 = r2->field_b
    //     0xb990a8: ldur            w3, [x2, #0xb]
    // 0xb990ac: DecompressPointer r3
    //     0xb990ac: add             x3, x3, HEAP, lsl #32
    // 0xb990b0: cmp             w3, NULL
    // 0xb990b4: b.ne            #0xb990c0
    // 0xb990b8: r0 = Null
    //     0xb990b8: mov             x0, NULL
    // 0xb990bc: b               #0xb990fc
    // 0xb990c0: LoadField: r2 = r3->field_7
    //     0xb990c0: ldur            w2, [x3, #7]
    // 0xb990c4: DecompressPointer r2
    //     0xb990c4: add             x2, x2, HEAP, lsl #32
    // 0xb990c8: cmp             w2, NULL
    // 0xb990cc: b.ne            #0xb990d8
    // 0xb990d0: r0 = Null
    //     0xb990d0: mov             x0, NULL
    // 0xb990d4: b               #0xb990fc
    // 0xb990d8: stp             xzr, x2, [SP]
    // 0xb990dc: r0 = []()
    //     0xb990dc: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb990e0: r1 = LoadClassIdInstr(r0)
    //     0xb990e0: ldur            x1, [x0, #-1]
    //     0xb990e4: ubfx            x1, x1, #0xc, #0x14
    // 0xb990e8: str             x0, [SP]
    // 0xb990ec: mov             x0, x1
    // 0xb990f0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb990f0: sub             lr, x0, #1, lsl #12
    //     0xb990f4: ldr             lr, [x21, lr, lsl #3]
    //     0xb990f8: blr             lr
    // 0xb990fc: cmp             w0, NULL
    // 0xb99100: b.ne            #0xb9910c
    // 0xb99104: r3 = ""
    //     0xb99104: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb99108: b               #0xb99110
    // 0xb9910c: mov             x3, x0
    // 0xb99110: ldur            x0, [fp, #-8]
    // 0xb99114: ldur            x2, [fp, #-0x20]
    // 0xb99118: stur            x3, [fp, #-0x28]
    // 0xb9911c: LoadField: r1 = r2->field_13
    //     0xb9911c: ldur            w1, [x2, #0x13]
    // 0xb99120: DecompressPointer r1
    //     0xb99120: add             x1, x1, HEAP, lsl #32
    // 0xb99124: r0 = of()
    //     0xb99124: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb99128: LoadField: r1 = r0->field_87
    //     0xb99128: ldur            w1, [x0, #0x87]
    // 0xb9912c: DecompressPointer r1
    //     0xb9912c: add             x1, x1, HEAP, lsl #32
    // 0xb99130: LoadField: r0 = r1->field_7
    //     0xb99130: ldur            w0, [x1, #7]
    // 0xb99134: DecompressPointer r0
    //     0xb99134: add             x0, x0, HEAP, lsl #32
    // 0xb99138: stur            x0, [fp, #-0x38]
    // 0xb9913c: r1 = Instance_Color
    //     0xb9913c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99140: d0 = 0.500000
    //     0xb99140: fmov            d0, #0.50000000
    // 0xb99144: r0 = withOpacity()
    //     0xb99144: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb99148: r16 = 16.000000
    //     0xb99148: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb9914c: ldr             x16, [x16, #0x188]
    // 0xb99150: stp             x0, x16, [SP]
    // 0xb99154: ldur            x1, [fp, #-0x38]
    // 0xb99158: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb99158: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9915c: ldr             x4, [x4, #0xaa0]
    // 0xb99160: r0 = copyWith()
    //     0xb99160: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb99164: stur            x0, [fp, #-0x38]
    // 0xb99168: r0 = Text()
    //     0xb99168: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9916c: mov             x1, x0
    // 0xb99170: ldur            x0, [fp, #-0x28]
    // 0xb99174: stur            x1, [fp, #-0x48]
    // 0xb99178: StoreField: r1->field_b = r0
    //     0xb99178: stur            w0, [x1, #0xb]
    // 0xb9917c: ldur            x0, [fp, #-0x38]
    // 0xb99180: StoreField: r1->field_13 = r0
    //     0xb99180: stur            w0, [x1, #0x13]
    // 0xb99184: r0 = Center()
    //     0xb99184: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb99188: mov             x1, x0
    // 0xb9918c: r0 = Instance_Alignment
    //     0xb9918c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb99190: ldr             x0, [x0, #0xb10]
    // 0xb99194: stur            x1, [fp, #-0x28]
    // 0xb99198: StoreField: r1->field_f = r0
    //     0xb99198: stur            w0, [x1, #0xf]
    // 0xb9919c: ldur            x0, [fp, #-0x48]
    // 0xb991a0: StoreField: r1->field_b = r0
    //     0xb991a0: stur            w0, [x1, #0xb]
    // 0xb991a4: r0 = Container()
    //     0xb991a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb991a8: stur            x0, [fp, #-0x38]
    // 0xb991ac: r16 = 34.000000
    //     0xb991ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb991b0: ldr             x16, [x16, #0x978]
    // 0xb991b4: r30 = 34.000000
    //     0xb991b4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb991b8: ldr             lr, [lr, #0x978]
    // 0xb991bc: stp             lr, x16, [SP, #0x18]
    // 0xb991c0: r16 = Instance_EdgeInsets
    //     0xb991c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb991c4: ldr             x16, [x16, #0x980]
    // 0xb991c8: ldur            lr, [fp, #-0x30]
    // 0xb991cc: stp             lr, x16, [SP, #8]
    // 0xb991d0: ldur            x16, [fp, #-0x28]
    // 0xb991d4: str             x16, [SP]
    // 0xb991d8: mov             x1, x0
    // 0xb991dc: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb991dc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb991e0: ldr             x4, [x4, #0x988]
    // 0xb991e4: r0 = Container()
    //     0xb991e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb991e8: ldur            x0, [fp, #-8]
    // 0xb991ec: LoadField: r1 = r0->field_b
    //     0xb991ec: ldur            w1, [x0, #0xb]
    // 0xb991f0: DecompressPointer r1
    //     0xb991f0: add             x1, x1, HEAP, lsl #32
    // 0xb991f4: cmp             w1, NULL
    // 0xb991f8: b.eq            #0xb99fb4
    // 0xb991fc: LoadField: r2 = r1->field_b
    //     0xb991fc: ldur            w2, [x1, #0xb]
    // 0xb99200: DecompressPointer r2
    //     0xb99200: add             x2, x2, HEAP, lsl #32
    // 0xb99204: cmp             w2, NULL
    // 0xb99208: b.ne            #0xb99214
    // 0xb9920c: r1 = Null
    //     0xb9920c: mov             x1, NULL
    // 0xb99210: b               #0xb9921c
    // 0xb99214: LoadField: r1 = r2->field_7
    //     0xb99214: ldur            w1, [x2, #7]
    // 0xb99218: DecompressPointer r1
    //     0xb99218: add             x1, x1, HEAP, lsl #32
    // 0xb9921c: cmp             w1, NULL
    // 0xb99220: b.ne            #0xb9922c
    // 0xb99224: r3 = ""
    //     0xb99224: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb99228: b               #0xb99230
    // 0xb9922c: mov             x3, x1
    // 0xb99230: ldur            x2, [fp, #-0x20]
    // 0xb99234: stur            x3, [fp, #-0x28]
    // 0xb99238: LoadField: r1 = r2->field_13
    //     0xb99238: ldur            w1, [x2, #0x13]
    // 0xb9923c: DecompressPointer r1
    //     0xb9923c: add             x1, x1, HEAP, lsl #32
    // 0xb99240: r0 = of()
    //     0xb99240: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb99244: LoadField: r1 = r0->field_87
    //     0xb99244: ldur            w1, [x0, #0x87]
    // 0xb99248: DecompressPointer r1
    //     0xb99248: add             x1, x1, HEAP, lsl #32
    // 0xb9924c: LoadField: r0 = r1->field_7
    //     0xb9924c: ldur            w0, [x1, #7]
    // 0xb99250: DecompressPointer r0
    //     0xb99250: add             x0, x0, HEAP, lsl #32
    // 0xb99254: r16 = 14.000000
    //     0xb99254: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb99258: ldr             x16, [x16, #0x1d8]
    // 0xb9925c: r30 = Instance_Color
    //     0xb9925c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99260: stp             lr, x16, [SP]
    // 0xb99264: mov             x1, x0
    // 0xb99268: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb99268: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9926c: ldr             x4, [x4, #0xaa0]
    // 0xb99270: r0 = copyWith()
    //     0xb99270: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb99274: stur            x0, [fp, #-0x30]
    // 0xb99278: r0 = Text()
    //     0xb99278: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9927c: mov             x2, x0
    // 0xb99280: ldur            x0, [fp, #-0x28]
    // 0xb99284: stur            x2, [fp, #-0x48]
    // 0xb99288: StoreField: r2->field_b = r0
    //     0xb99288: stur            w0, [x2, #0xb]
    // 0xb9928c: ldur            x0, [fp, #-0x30]
    // 0xb99290: StoreField: r2->field_13 = r0
    //     0xb99290: stur            w0, [x2, #0x13]
    // 0xb99294: ldur            x0, [fp, #-8]
    // 0xb99298: LoadField: r1 = r0->field_b
    //     0xb99298: ldur            w1, [x0, #0xb]
    // 0xb9929c: DecompressPointer r1
    //     0xb9929c: add             x1, x1, HEAP, lsl #32
    // 0xb992a0: cmp             w1, NULL
    // 0xb992a4: b.eq            #0xb99fb8
    // 0xb992a8: LoadField: r3 = r1->field_b
    //     0xb992a8: ldur            w3, [x1, #0xb]
    // 0xb992ac: DecompressPointer r3
    //     0xb992ac: add             x3, x3, HEAP, lsl #32
    // 0xb992b0: cmp             w3, NULL
    // 0xb992b4: b.ne            #0xb992c0
    // 0xb992b8: r1 = Null
    //     0xb992b8: mov             x1, NULL
    // 0xb992bc: b               #0xb992c8
    // 0xb992c0: LoadField: r1 = r3->field_1f
    //     0xb992c0: ldur            w1, [x3, #0x1f]
    // 0xb992c4: DecompressPointer r1
    //     0xb992c4: add             x1, x1, HEAP, lsl #32
    // 0xb992c8: cmp             w1, NULL
    // 0xb992cc: b.ne            #0xb992d8
    // 0xb992d0: r4 = ""
    //     0xb992d0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb992d4: b               #0xb992dc
    // 0xb992d8: mov             x4, x1
    // 0xb992dc: ldur            x3, [fp, #-0x20]
    // 0xb992e0: stur            x4, [fp, #-0x28]
    // 0xb992e4: LoadField: r1 = r3->field_13
    //     0xb992e4: ldur            w1, [x3, #0x13]
    // 0xb992e8: DecompressPointer r1
    //     0xb992e8: add             x1, x1, HEAP, lsl #32
    // 0xb992ec: r0 = of()
    //     0xb992ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb992f0: LoadField: r1 = r0->field_87
    //     0xb992f0: ldur            w1, [x0, #0x87]
    // 0xb992f4: DecompressPointer r1
    //     0xb992f4: add             x1, x1, HEAP, lsl #32
    // 0xb992f8: LoadField: r0 = r1->field_33
    //     0xb992f8: ldur            w0, [x1, #0x33]
    // 0xb992fc: DecompressPointer r0
    //     0xb992fc: add             x0, x0, HEAP, lsl #32
    // 0xb99300: stur            x0, [fp, #-0x30]
    // 0xb99304: cmp             w0, NULL
    // 0xb99308: b.ne            #0xb99314
    // 0xb9930c: r4 = Null
    //     0xb9930c: mov             x4, NULL
    // 0xb99310: b               #0xb9933c
    // 0xb99314: r1 = Instance_Color
    //     0xb99314: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99318: d0 = 0.500000
    //     0xb99318: fmov            d0, #0.50000000
    // 0xb9931c: r0 = withOpacity()
    //     0xb9931c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb99320: r16 = 10.000000
    //     0xb99320: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb99324: stp             x0, x16, [SP]
    // 0xb99328: ldur            x1, [fp, #-0x30]
    // 0xb9932c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9932c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb99330: ldr             x4, [x4, #0xaa0]
    // 0xb99334: r0 = copyWith()
    //     0xb99334: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb99338: mov             x4, x0
    // 0xb9933c: ldur            x1, [fp, #-8]
    // 0xb99340: ldur            x3, [fp, #-0x38]
    // 0xb99344: ldur            x0, [fp, #-0x48]
    // 0xb99348: ldur            x2, [fp, #-0x28]
    // 0xb9934c: stur            x4, [fp, #-0x30]
    // 0xb99350: r0 = Text()
    //     0xb99350: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb99354: mov             x1, x0
    // 0xb99358: ldur            x0, [fp, #-0x28]
    // 0xb9935c: stur            x1, [fp, #-0x50]
    // 0xb99360: StoreField: r1->field_b = r0
    //     0xb99360: stur            w0, [x1, #0xb]
    // 0xb99364: ldur            x0, [fp, #-0x30]
    // 0xb99368: StoreField: r1->field_13 = r0
    //     0xb99368: stur            w0, [x1, #0x13]
    // 0xb9936c: r0 = Padding()
    //     0xb9936c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb99370: mov             x3, x0
    // 0xb99374: r0 = Instance_EdgeInsets
    //     0xb99374: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb99378: ldr             x0, [x0, #0xe90]
    // 0xb9937c: stur            x3, [fp, #-0x28]
    // 0xb99380: StoreField: r3->field_f = r0
    //     0xb99380: stur            w0, [x3, #0xf]
    // 0xb99384: ldur            x0, [fp, #-0x50]
    // 0xb99388: StoreField: r3->field_b = r0
    //     0xb99388: stur            w0, [x3, #0xb]
    // 0xb9938c: r1 = Null
    //     0xb9938c: mov             x1, NULL
    // 0xb99390: r2 = 4
    //     0xb99390: movz            x2, #0x4
    // 0xb99394: r0 = AllocateArray()
    //     0xb99394: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb99398: mov             x2, x0
    // 0xb9939c: ldur            x0, [fp, #-0x48]
    // 0xb993a0: stur            x2, [fp, #-0x30]
    // 0xb993a4: StoreField: r2->field_f = r0
    //     0xb993a4: stur            w0, [x2, #0xf]
    // 0xb993a8: ldur            x0, [fp, #-0x28]
    // 0xb993ac: StoreField: r2->field_13 = r0
    //     0xb993ac: stur            w0, [x2, #0x13]
    // 0xb993b0: r1 = <Widget>
    //     0xb993b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb993b4: r0 = AllocateGrowableArray()
    //     0xb993b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb993b8: mov             x1, x0
    // 0xb993bc: ldur            x0, [fp, #-0x30]
    // 0xb993c0: stur            x1, [fp, #-0x28]
    // 0xb993c4: StoreField: r1->field_f = r0
    //     0xb993c4: stur            w0, [x1, #0xf]
    // 0xb993c8: r2 = 4
    //     0xb993c8: movz            x2, #0x4
    // 0xb993cc: StoreField: r1->field_b = r2
    //     0xb993cc: stur            w2, [x1, #0xb]
    // 0xb993d0: r0 = Column()
    //     0xb993d0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb993d4: mov             x3, x0
    // 0xb993d8: r0 = Instance_Axis
    //     0xb993d8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb993dc: stur            x3, [fp, #-0x30]
    // 0xb993e0: StoreField: r3->field_f = r0
    //     0xb993e0: stur            w0, [x3, #0xf]
    // 0xb993e4: r4 = Instance_MainAxisAlignment
    //     0xb993e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb993e8: ldr             x4, [x4, #0xa08]
    // 0xb993ec: StoreField: r3->field_13 = r4
    //     0xb993ec: stur            w4, [x3, #0x13]
    // 0xb993f0: r5 = Instance_MainAxisSize
    //     0xb993f0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb993f4: ldr             x5, [x5, #0xa10]
    // 0xb993f8: ArrayStore: r3[0] = r5  ; List_4
    //     0xb993f8: stur            w5, [x3, #0x17]
    // 0xb993fc: r1 = Instance_CrossAxisAlignment
    //     0xb993fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb99400: ldr             x1, [x1, #0x890]
    // 0xb99404: StoreField: r3->field_1b = r1
    //     0xb99404: stur            w1, [x3, #0x1b]
    // 0xb99408: r6 = Instance_VerticalDirection
    //     0xb99408: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9940c: ldr             x6, [x6, #0xa20]
    // 0xb99410: StoreField: r3->field_23 = r6
    //     0xb99410: stur            w6, [x3, #0x23]
    // 0xb99414: r7 = Instance_Clip
    //     0xb99414: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb99418: ldr             x7, [x7, #0x38]
    // 0xb9941c: StoreField: r3->field_2b = r7
    //     0xb9941c: stur            w7, [x3, #0x2b]
    // 0xb99420: StoreField: r3->field_2f = rZR
    //     0xb99420: stur            xzr, [x3, #0x2f]
    // 0xb99424: ldur            x1, [fp, #-0x28]
    // 0xb99428: StoreField: r3->field_b = r1
    //     0xb99428: stur            w1, [x3, #0xb]
    // 0xb9942c: r1 = Null
    //     0xb9942c: mov             x1, NULL
    // 0xb99430: r2 = 6
    //     0xb99430: movz            x2, #0x6
    // 0xb99434: r0 = AllocateArray()
    //     0xb99434: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb99438: mov             x2, x0
    // 0xb9943c: ldur            x0, [fp, #-0x38]
    // 0xb99440: stur            x2, [fp, #-0x28]
    // 0xb99444: StoreField: r2->field_f = r0
    //     0xb99444: stur            w0, [x2, #0xf]
    // 0xb99448: r16 = Instance_SizedBox
    //     0xb99448: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb9944c: ldr             x16, [x16, #0x998]
    // 0xb99450: StoreField: r2->field_13 = r16
    //     0xb99450: stur            w16, [x2, #0x13]
    // 0xb99454: ldur            x0, [fp, #-0x30]
    // 0xb99458: ArrayStore: r2[0] = r0  ; List_4
    //     0xb99458: stur            w0, [x2, #0x17]
    // 0xb9945c: r1 = <Widget>
    //     0xb9945c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb99460: r0 = AllocateGrowableArray()
    //     0xb99460: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb99464: mov             x1, x0
    // 0xb99468: ldur            x0, [fp, #-0x28]
    // 0xb9946c: stur            x1, [fp, #-0x30]
    // 0xb99470: StoreField: r1->field_f = r0
    //     0xb99470: stur            w0, [x1, #0xf]
    // 0xb99474: r2 = 6
    //     0xb99474: movz            x2, #0x6
    // 0xb99478: StoreField: r1->field_b = r2
    //     0xb99478: stur            w2, [x1, #0xb]
    // 0xb9947c: r0 = Row()
    //     0xb9947c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb99480: mov             x2, x0
    // 0xb99484: r1 = Instance_Axis
    //     0xb99484: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb99488: stur            x2, [fp, #-0x28]
    // 0xb9948c: StoreField: r2->field_f = r1
    //     0xb9948c: stur            w1, [x2, #0xf]
    // 0xb99490: r3 = Instance_MainAxisAlignment
    //     0xb99490: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb99494: ldr             x3, [x3, #0xa08]
    // 0xb99498: StoreField: r2->field_13 = r3
    //     0xb99498: stur            w3, [x2, #0x13]
    // 0xb9949c: r4 = Instance_MainAxisSize
    //     0xb9949c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb994a0: ldr             x4, [x4, #0xa10]
    // 0xb994a4: ArrayStore: r2[0] = r4  ; List_4
    //     0xb994a4: stur            w4, [x2, #0x17]
    // 0xb994a8: r5 = Instance_CrossAxisAlignment
    //     0xb994a8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb994ac: ldr             x5, [x5, #0xa18]
    // 0xb994b0: StoreField: r2->field_1b = r5
    //     0xb994b0: stur            w5, [x2, #0x1b]
    // 0xb994b4: r6 = Instance_VerticalDirection
    //     0xb994b4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb994b8: ldr             x6, [x6, #0xa20]
    // 0xb994bc: StoreField: r2->field_23 = r6
    //     0xb994bc: stur            w6, [x2, #0x23]
    // 0xb994c0: r7 = Instance_Clip
    //     0xb994c0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb994c4: ldr             x7, [x7, #0x38]
    // 0xb994c8: StoreField: r2->field_2b = r7
    //     0xb994c8: stur            w7, [x2, #0x2b]
    // 0xb994cc: StoreField: r2->field_2f = rZR
    //     0xb994cc: stur            xzr, [x2, #0x2f]
    // 0xb994d0: ldur            x0, [fp, #-0x30]
    // 0xb994d4: StoreField: r2->field_b = r0
    //     0xb994d4: stur            w0, [x2, #0xb]
    // 0xb994d8: ldur            x8, [fp, #-8]
    // 0xb994dc: LoadField: r0 = r8->field_b
    //     0xb994dc: ldur            w0, [x8, #0xb]
    // 0xb994e0: DecompressPointer r0
    //     0xb994e0: add             x0, x0, HEAP, lsl #32
    // 0xb994e4: cmp             w0, NULL
    // 0xb994e8: b.eq            #0xb99fbc
    // 0xb994ec: LoadField: r9 = r0->field_b
    //     0xb994ec: ldur            w9, [x0, #0xb]
    // 0xb994f0: DecompressPointer r9
    //     0xb994f0: add             x9, x9, HEAP, lsl #32
    // 0xb994f4: cmp             w9, NULL
    // 0xb994f8: b.ne            #0xb99504
    // 0xb994fc: r0 = Null
    //     0xb994fc: mov             x0, NULL
    // 0xb99500: b               #0xb99538
    // 0xb99504: LoadField: r0 = r9->field_f
    //     0xb99504: ldur            w0, [x9, #0xf]
    // 0xb99508: DecompressPointer r0
    //     0xb99508: add             x0, x0, HEAP, lsl #32
    // 0xb9950c: r9 = 60
    //     0xb9950c: movz            x9, #0x3c
    // 0xb99510: branchIfSmi(r0, 0xb9951c)
    //     0xb99510: tbz             w0, #0, #0xb9951c
    // 0xb99514: r9 = LoadClassIdInstr(r0)
    //     0xb99514: ldur            x9, [x0, #-1]
    //     0xb99518: ubfx            x9, x9, #0xc, #0x14
    // 0xb9951c: str             x0, [SP]
    // 0xb99520: mov             x0, x9
    // 0xb99524: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb99524: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb99528: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb99528: movz            x17, #0x2700
    //     0xb9952c: add             lr, x0, x17
    //     0xb99530: ldr             lr, [x21, lr, lsl #3]
    //     0xb99534: blr             lr
    // 0xb99538: cmp             w0, NULL
    // 0xb9953c: b.ne            #0xb99548
    // 0xb99540: r1 = ""
    //     0xb99540: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb99544: b               #0xb9954c
    // 0xb99548: mov             x1, x0
    // 0xb9954c: r0 = parse()
    //     0xb9954c: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb99550: mov             v1.16b, v0.16b
    // 0xb99554: d0 = 4.000000
    //     0xb99554: fmov            d0, #4.00000000
    // 0xb99558: fcmp            d1, d0
    // 0xb9955c: b.lt            #0xb9956c
    // 0xb99560: r1 = Instance_Color
    //     0xb99560: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb99564: ldr             x1, [x1, #0x858]
    // 0xb99568: b               #0xb996ac
    // 0xb9956c: ldur            x1, [fp, #-8]
    // 0xb99570: LoadField: r0 = r1->field_b
    //     0xb99570: ldur            w0, [x1, #0xb]
    // 0xb99574: DecompressPointer r0
    //     0xb99574: add             x0, x0, HEAP, lsl #32
    // 0xb99578: cmp             w0, NULL
    // 0xb9957c: b.eq            #0xb99fc0
    // 0xb99580: LoadField: r2 = r0->field_b
    //     0xb99580: ldur            w2, [x0, #0xb]
    // 0xb99584: DecompressPointer r2
    //     0xb99584: add             x2, x2, HEAP, lsl #32
    // 0xb99588: cmp             w2, NULL
    // 0xb9958c: b.ne            #0xb99598
    // 0xb99590: r0 = Null
    //     0xb99590: mov             x0, NULL
    // 0xb99594: b               #0xb995cc
    // 0xb99598: LoadField: r0 = r2->field_f
    //     0xb99598: ldur            w0, [x2, #0xf]
    // 0xb9959c: DecompressPointer r0
    //     0xb9959c: add             x0, x0, HEAP, lsl #32
    // 0xb995a0: r2 = 60
    //     0xb995a0: movz            x2, #0x3c
    // 0xb995a4: branchIfSmi(r0, 0xb995b0)
    //     0xb995a4: tbz             w0, #0, #0xb995b0
    // 0xb995a8: r2 = LoadClassIdInstr(r0)
    //     0xb995a8: ldur            x2, [x0, #-1]
    //     0xb995ac: ubfx            x2, x2, #0xc, #0x14
    // 0xb995b0: str             x0, [SP]
    // 0xb995b4: mov             x0, x2
    // 0xb995b8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb995b8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb995bc: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb995bc: movz            x17, #0x2700
    //     0xb995c0: add             lr, x0, x17
    //     0xb995c4: ldr             lr, [x21, lr, lsl #3]
    //     0xb995c8: blr             lr
    // 0xb995cc: cmp             w0, NULL
    // 0xb995d0: b.ne            #0xb995dc
    // 0xb995d4: r1 = ""
    //     0xb995d4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb995d8: b               #0xb995e0
    // 0xb995dc: mov             x1, x0
    // 0xb995e0: r0 = parse()
    //     0xb995e0: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb995e4: mov             v1.16b, v0.16b
    // 0xb995e8: d0 = 3.500000
    //     0xb995e8: fmov            d0, #3.50000000
    // 0xb995ec: fcmp            d1, d0
    // 0xb995f0: b.lt            #0xb9960c
    // 0xb995f4: r1 = Instance_Color
    //     0xb995f4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb995f8: ldr             x1, [x1, #0x858]
    // 0xb995fc: d0 = 0.700000
    //     0xb995fc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb99600: ldr             d0, [x17, #0xf48]
    // 0xb99604: r0 = withOpacity()
    //     0xb99604: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb99608: b               #0xb996a8
    // 0xb9960c: ldur            x1, [fp, #-8]
    // 0xb99610: LoadField: r0 = r1->field_b
    //     0xb99610: ldur            w0, [x1, #0xb]
    // 0xb99614: DecompressPointer r0
    //     0xb99614: add             x0, x0, HEAP, lsl #32
    // 0xb99618: cmp             w0, NULL
    // 0xb9961c: b.eq            #0xb99fc4
    // 0xb99620: LoadField: r2 = r0->field_b
    //     0xb99620: ldur            w2, [x0, #0xb]
    // 0xb99624: DecompressPointer r2
    //     0xb99624: add             x2, x2, HEAP, lsl #32
    // 0xb99628: cmp             w2, NULL
    // 0xb9962c: b.ne            #0xb99638
    // 0xb99630: r0 = Null
    //     0xb99630: mov             x0, NULL
    // 0xb99634: b               #0xb9966c
    // 0xb99638: LoadField: r0 = r2->field_f
    //     0xb99638: ldur            w0, [x2, #0xf]
    // 0xb9963c: DecompressPointer r0
    //     0xb9963c: add             x0, x0, HEAP, lsl #32
    // 0xb99640: r2 = 60
    //     0xb99640: movz            x2, #0x3c
    // 0xb99644: branchIfSmi(r0, 0xb99650)
    //     0xb99644: tbz             w0, #0, #0xb99650
    // 0xb99648: r2 = LoadClassIdInstr(r0)
    //     0xb99648: ldur            x2, [x0, #-1]
    //     0xb9964c: ubfx            x2, x2, #0xc, #0x14
    // 0xb99650: str             x0, [SP]
    // 0xb99654: mov             x0, x2
    // 0xb99658: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb99658: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb9965c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb9965c: movz            x17, #0x2700
    //     0xb99660: add             lr, x0, x17
    //     0xb99664: ldr             lr, [x21, lr, lsl #3]
    //     0xb99668: blr             lr
    // 0xb9966c: cmp             w0, NULL
    // 0xb99670: b.ne            #0xb9967c
    // 0xb99674: r1 = ""
    //     0xb99674: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb99678: b               #0xb99680
    // 0xb9967c: mov             x1, x0
    // 0xb99680: r0 = parse()
    //     0xb99680: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb99684: mov             v1.16b, v0.16b
    // 0xb99688: d0 = 2.000000
    //     0xb99688: fmov            d0, #2.00000000
    // 0xb9968c: fcmp            d1, d0
    // 0xb99690: b.lt            #0xb996a0
    // 0xb99694: r0 = Instance_Color
    //     0xb99694: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb99698: ldr             x0, [x0, #0x860]
    // 0xb9969c: b               #0xb996a8
    // 0xb996a0: r0 = Instance_Color
    //     0xb996a0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb996a4: ldr             x0, [x0, #0x50]
    // 0xb996a8: mov             x1, x0
    // 0xb996ac: ldur            x0, [fp, #-8]
    // 0xb996b0: stur            x1, [fp, #-0x30]
    // 0xb996b4: r0 = ColorFilter()
    //     0xb996b4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb996b8: mov             x1, x0
    // 0xb996bc: ldur            x0, [fp, #-0x30]
    // 0xb996c0: stur            x1, [fp, #-0x38]
    // 0xb996c4: StoreField: r1->field_7 = r0
    //     0xb996c4: stur            w0, [x1, #7]
    // 0xb996c8: r0 = Instance_BlendMode
    //     0xb996c8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb996cc: ldr             x0, [x0, #0xb30]
    // 0xb996d0: StoreField: r1->field_b = r0
    //     0xb996d0: stur            w0, [x1, #0xb]
    // 0xb996d4: r0 = 1
    //     0xb996d4: movz            x0, #0x1
    // 0xb996d8: StoreField: r1->field_13 = r0
    //     0xb996d8: stur            x0, [x1, #0x13]
    // 0xb996dc: r0 = SvgPicture()
    //     0xb996dc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb996e0: stur            x0, [fp, #-0x30]
    // 0xb996e4: ldur            x16, [fp, #-0x38]
    // 0xb996e8: str             x16, [SP]
    // 0xb996ec: mov             x1, x0
    // 0xb996f0: r2 = "assets/images/green_star.svg"
    //     0xb996f0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb996f4: ldr             x2, [x2, #0x9a0]
    // 0xb996f8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb996f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb996fc: ldr             x4, [x4, #0xa38]
    // 0xb99700: r0 = SvgPicture.asset()
    //     0xb99700: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb99704: ldur            x1, [fp, #-8]
    // 0xb99708: LoadField: r0 = r1->field_b
    //     0xb99708: ldur            w0, [x1, #0xb]
    // 0xb9970c: DecompressPointer r0
    //     0xb9970c: add             x0, x0, HEAP, lsl #32
    // 0xb99710: cmp             w0, NULL
    // 0xb99714: b.eq            #0xb99fc8
    // 0xb99718: LoadField: r2 = r0->field_b
    //     0xb99718: ldur            w2, [x0, #0xb]
    // 0xb9971c: DecompressPointer r2
    //     0xb9971c: add             x2, x2, HEAP, lsl #32
    // 0xb99720: cmp             w2, NULL
    // 0xb99724: b.ne            #0xb99730
    // 0xb99728: r0 = Null
    //     0xb99728: mov             x0, NULL
    // 0xb9972c: b               #0xb99764
    // 0xb99730: LoadField: r0 = r2->field_f
    //     0xb99730: ldur            w0, [x2, #0xf]
    // 0xb99734: DecompressPointer r0
    //     0xb99734: add             x0, x0, HEAP, lsl #32
    // 0xb99738: r2 = 60
    //     0xb99738: movz            x2, #0x3c
    // 0xb9973c: branchIfSmi(r0, 0xb99748)
    //     0xb9973c: tbz             w0, #0, #0xb99748
    // 0xb99740: r2 = LoadClassIdInstr(r0)
    //     0xb99740: ldur            x2, [x0, #-1]
    //     0xb99744: ubfx            x2, x2, #0xc, #0x14
    // 0xb99748: str             x0, [SP]
    // 0xb9974c: mov             x0, x2
    // 0xb99750: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb99750: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb99754: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb99754: movz            x17, #0x2700
    //     0xb99758: add             lr, x0, x17
    //     0xb9975c: ldr             lr, [x21, lr, lsl #3]
    //     0xb99760: blr             lr
    // 0xb99764: cmp             w0, NULL
    // 0xb99768: b.ne            #0xb99774
    // 0xb9976c: r5 = ""
    //     0xb9976c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb99770: b               #0xb99778
    // 0xb99774: mov             x5, x0
    // 0xb99778: ldur            x3, [fp, #-0x20]
    // 0xb9977c: ldur            x4, [fp, #-0x18]
    // 0xb99780: ldur            x2, [fp, #-0x28]
    // 0xb99784: ldur            x0, [fp, #-0x30]
    // 0xb99788: stur            x5, [fp, #-0x38]
    // 0xb9978c: LoadField: r1 = r3->field_13
    //     0xb9978c: ldur            w1, [x3, #0x13]
    // 0xb99790: DecompressPointer r1
    //     0xb99790: add             x1, x1, HEAP, lsl #32
    // 0xb99794: r0 = of()
    //     0xb99794: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb99798: LoadField: r1 = r0->field_87
    //     0xb99798: ldur            w1, [x0, #0x87]
    // 0xb9979c: DecompressPointer r1
    //     0xb9979c: add             x1, x1, HEAP, lsl #32
    // 0xb997a0: LoadField: r0 = r1->field_7
    //     0xb997a0: ldur            w0, [x1, #7]
    // 0xb997a4: DecompressPointer r0
    //     0xb997a4: add             x0, x0, HEAP, lsl #32
    // 0xb997a8: r16 = 12.000000
    //     0xb997a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb997ac: ldr             x16, [x16, #0x9e8]
    // 0xb997b0: r30 = Instance_Color
    //     0xb997b0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb997b4: stp             lr, x16, [SP]
    // 0xb997b8: mov             x1, x0
    // 0xb997bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb997bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb997c0: ldr             x4, [x4, #0xaa0]
    // 0xb997c4: r0 = copyWith()
    //     0xb997c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb997c8: stur            x0, [fp, #-0x48]
    // 0xb997cc: r0 = Text()
    //     0xb997cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb997d0: mov             x3, x0
    // 0xb997d4: ldur            x0, [fp, #-0x38]
    // 0xb997d8: stur            x3, [fp, #-0x50]
    // 0xb997dc: StoreField: r3->field_b = r0
    //     0xb997dc: stur            w0, [x3, #0xb]
    // 0xb997e0: ldur            x0, [fp, #-0x48]
    // 0xb997e4: StoreField: r3->field_13 = r0
    //     0xb997e4: stur            w0, [x3, #0x13]
    // 0xb997e8: r1 = Null
    //     0xb997e8: mov             x1, NULL
    // 0xb997ec: r2 = 6
    //     0xb997ec: movz            x2, #0x6
    // 0xb997f0: r0 = AllocateArray()
    //     0xb997f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb997f4: mov             x2, x0
    // 0xb997f8: ldur            x0, [fp, #-0x30]
    // 0xb997fc: stur            x2, [fp, #-0x38]
    // 0xb99800: StoreField: r2->field_f = r0
    //     0xb99800: stur            w0, [x2, #0xf]
    // 0xb99804: r16 = Instance_SizedBox
    //     0xb99804: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb99808: ldr             x16, [x16, #0xe98]
    // 0xb9980c: StoreField: r2->field_13 = r16
    //     0xb9980c: stur            w16, [x2, #0x13]
    // 0xb99810: ldur            x0, [fp, #-0x50]
    // 0xb99814: ArrayStore: r2[0] = r0  ; List_4
    //     0xb99814: stur            w0, [x2, #0x17]
    // 0xb99818: r1 = <Widget>
    //     0xb99818: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9981c: r0 = AllocateGrowableArray()
    //     0xb9981c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb99820: mov             x1, x0
    // 0xb99824: ldur            x0, [fp, #-0x38]
    // 0xb99828: stur            x1, [fp, #-0x30]
    // 0xb9982c: StoreField: r1->field_f = r0
    //     0xb9982c: stur            w0, [x1, #0xf]
    // 0xb99830: r0 = 6
    //     0xb99830: movz            x0, #0x6
    // 0xb99834: StoreField: r1->field_b = r0
    //     0xb99834: stur            w0, [x1, #0xb]
    // 0xb99838: r0 = Row()
    //     0xb99838: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9983c: mov             x3, x0
    // 0xb99840: r0 = Instance_Axis
    //     0xb99840: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb99844: stur            x3, [fp, #-0x38]
    // 0xb99848: StoreField: r3->field_f = r0
    //     0xb99848: stur            w0, [x3, #0xf]
    // 0xb9984c: r4 = Instance_MainAxisAlignment
    //     0xb9984c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb99850: ldr             x4, [x4, #0xa08]
    // 0xb99854: StoreField: r3->field_13 = r4
    //     0xb99854: stur            w4, [x3, #0x13]
    // 0xb99858: r5 = Instance_MainAxisSize
    //     0xb99858: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9985c: ldr             x5, [x5, #0xa10]
    // 0xb99860: ArrayStore: r3[0] = r5  ; List_4
    //     0xb99860: stur            w5, [x3, #0x17]
    // 0xb99864: r6 = Instance_CrossAxisAlignment
    //     0xb99864: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb99868: ldr             x6, [x6, #0xa18]
    // 0xb9986c: StoreField: r3->field_1b = r6
    //     0xb9986c: stur            w6, [x3, #0x1b]
    // 0xb99870: r7 = Instance_VerticalDirection
    //     0xb99870: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb99874: ldr             x7, [x7, #0xa20]
    // 0xb99878: StoreField: r3->field_23 = r7
    //     0xb99878: stur            w7, [x3, #0x23]
    // 0xb9987c: r8 = Instance_Clip
    //     0xb9987c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb99880: ldr             x8, [x8, #0x38]
    // 0xb99884: StoreField: r3->field_2b = r8
    //     0xb99884: stur            w8, [x3, #0x2b]
    // 0xb99888: StoreField: r3->field_2f = rZR
    //     0xb99888: stur            xzr, [x3, #0x2f]
    // 0xb9988c: ldur            x1, [fp, #-0x30]
    // 0xb99890: StoreField: r3->field_b = r1
    //     0xb99890: stur            w1, [x3, #0xb]
    // 0xb99894: r1 = Null
    //     0xb99894: mov             x1, NULL
    // 0xb99898: r2 = 4
    //     0xb99898: movz            x2, #0x4
    // 0xb9989c: r0 = AllocateArray()
    //     0xb9989c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb998a0: mov             x2, x0
    // 0xb998a4: ldur            x0, [fp, #-0x28]
    // 0xb998a8: stur            x2, [fp, #-0x30]
    // 0xb998ac: StoreField: r2->field_f = r0
    //     0xb998ac: stur            w0, [x2, #0xf]
    // 0xb998b0: ldur            x0, [fp, #-0x38]
    // 0xb998b4: StoreField: r2->field_13 = r0
    //     0xb998b4: stur            w0, [x2, #0x13]
    // 0xb998b8: r1 = <Widget>
    //     0xb998b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb998bc: r0 = AllocateGrowableArray()
    //     0xb998bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb998c0: mov             x1, x0
    // 0xb998c4: ldur            x0, [fp, #-0x30]
    // 0xb998c8: stur            x1, [fp, #-0x28]
    // 0xb998cc: StoreField: r1->field_f = r0
    //     0xb998cc: stur            w0, [x1, #0xf]
    // 0xb998d0: r2 = 4
    //     0xb998d0: movz            x2, #0x4
    // 0xb998d4: StoreField: r1->field_b = r2
    //     0xb998d4: stur            w2, [x1, #0xb]
    // 0xb998d8: r0 = Row()
    //     0xb998d8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb998dc: mov             x1, x0
    // 0xb998e0: r0 = Instance_Axis
    //     0xb998e0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb998e4: stur            x1, [fp, #-0x30]
    // 0xb998e8: StoreField: r1->field_f = r0
    //     0xb998e8: stur            w0, [x1, #0xf]
    // 0xb998ec: r0 = Instance_MainAxisAlignment
    //     0xb998ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb998f0: ldr             x0, [x0, #0xa8]
    // 0xb998f4: StoreField: r1->field_13 = r0
    //     0xb998f4: stur            w0, [x1, #0x13]
    // 0xb998f8: r0 = Instance_MainAxisSize
    //     0xb998f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb998fc: ldr             x0, [x0, #0xa10]
    // 0xb99900: ArrayStore: r1[0] = r0  ; List_4
    //     0xb99900: stur            w0, [x1, #0x17]
    // 0xb99904: r2 = Instance_CrossAxisAlignment
    //     0xb99904: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb99908: ldr             x2, [x2, #0xa18]
    // 0xb9990c: StoreField: r1->field_1b = r2
    //     0xb9990c: stur            w2, [x1, #0x1b]
    // 0xb99910: r3 = Instance_VerticalDirection
    //     0xb99910: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb99914: ldr             x3, [x3, #0xa20]
    // 0xb99918: StoreField: r1->field_23 = r3
    //     0xb99918: stur            w3, [x1, #0x23]
    // 0xb9991c: r4 = Instance_Clip
    //     0xb9991c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb99920: ldr             x4, [x4, #0x38]
    // 0xb99924: StoreField: r1->field_2b = r4
    //     0xb99924: stur            w4, [x1, #0x2b]
    // 0xb99928: StoreField: r1->field_2f = rZR
    //     0xb99928: stur            xzr, [x1, #0x2f]
    // 0xb9992c: ldur            x5, [fp, #-0x28]
    // 0xb99930: StoreField: r1->field_b = r5
    //     0xb99930: stur            w5, [x1, #0xb]
    // 0xb99934: r0 = Container()
    //     0xb99934: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb99938: stur            x0, [fp, #-0x28]
    // 0xb9993c: r16 = Instance_BoxDecoration
    //     0xb9993c: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb99940: ldr             x16, [x16, #0x5a8]
    // 0xb99944: r30 = Instance_EdgeInsets
    //     0xb99944: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb99948: ldr             lr, [lr, #0x1f0]
    // 0xb9994c: stp             lr, x16, [SP, #8]
    // 0xb99950: ldur            x16, [fp, #-0x30]
    // 0xb99954: str             x16, [SP]
    // 0xb99958: mov             x1, x0
    // 0xb9995c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb9995c: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb99960: ldr             x4, [x4, #0xb40]
    // 0xb99964: r0 = Container()
    //     0xb99964: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb99968: ldur            x0, [fp, #-0x18]
    // 0xb9996c: LoadField: r1 = r0->field_b
    //     0xb9996c: ldur            w1, [x0, #0xb]
    // 0xb99970: LoadField: r2 = r0->field_f
    //     0xb99970: ldur            w2, [x0, #0xf]
    // 0xb99974: DecompressPointer r2
    //     0xb99974: add             x2, x2, HEAP, lsl #32
    // 0xb99978: LoadField: r3 = r2->field_b
    //     0xb99978: ldur            w3, [x2, #0xb]
    // 0xb9997c: r2 = LoadInt32Instr(r1)
    //     0xb9997c: sbfx            x2, x1, #1, #0x1f
    // 0xb99980: stur            x2, [fp, #-0x40]
    // 0xb99984: r1 = LoadInt32Instr(r3)
    //     0xb99984: sbfx            x1, x3, #1, #0x1f
    // 0xb99988: cmp             x2, x1
    // 0xb9998c: b.ne            #0xb99998
    // 0xb99990: mov             x1, x0
    // 0xb99994: r0 = _growToNextCapacity()
    //     0xb99994: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb99998: ldur            x2, [fp, #-0x18]
    // 0xb9999c: ldur            x3, [fp, #-0x40]
    // 0xb999a0: add             x0, x3, #1
    // 0xb999a4: lsl             x1, x0, #1
    // 0xb999a8: StoreField: r2->field_b = r1
    //     0xb999a8: stur            w1, [x2, #0xb]
    // 0xb999ac: LoadField: r1 = r2->field_f
    //     0xb999ac: ldur            w1, [x2, #0xf]
    // 0xb999b0: DecompressPointer r1
    //     0xb999b0: add             x1, x1, HEAP, lsl #32
    // 0xb999b4: ldur            x0, [fp, #-0x28]
    // 0xb999b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb999b8: add             x25, x1, x3, lsl #2
    //     0xb999bc: add             x25, x25, #0xf
    //     0xb999c0: str             w0, [x25]
    //     0xb999c4: tbz             w0, #0, #0xb999e0
    //     0xb999c8: ldurb           w16, [x1, #-1]
    //     0xb999cc: ldurb           w17, [x0, #-1]
    //     0xb999d0: and             x16, x17, x16, lsr #2
    //     0xb999d4: tst             x16, HEAP, lsr #32
    //     0xb999d8: b.eq            #0xb999e0
    //     0xb999dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb999e0: ldur            x0, [fp, #-8]
    // 0xb999e4: LoadField: r1 = r0->field_b
    //     0xb999e4: ldur            w1, [x0, #0xb]
    // 0xb999e8: DecompressPointer r1
    //     0xb999e8: add             x1, x1, HEAP, lsl #32
    // 0xb999ec: cmp             w1, NULL
    // 0xb999f0: b.eq            #0xb99fcc
    // 0xb999f4: LoadField: r3 = r1->field_b
    //     0xb999f4: ldur            w3, [x1, #0xb]
    // 0xb999f8: DecompressPointer r3
    //     0xb999f8: add             x3, x3, HEAP, lsl #32
    // 0xb999fc: cmp             w3, NULL
    // 0xb99a00: b.ne            #0xb99a0c
    // 0xb99a04: r1 = Null
    //     0xb99a04: mov             x1, NULL
    // 0xb99a08: b               #0xb99a14
    // 0xb99a0c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb99a0c: ldur            w1, [x3, #0x17]
    // 0xb99a10: DecompressPointer r1
    //     0xb99a10: add             x1, x1, HEAP, lsl #32
    // 0xb99a14: cmp             w1, NULL
    // 0xb99a18: b.ne            #0xb99a24
    // 0xb99a1c: r4 = ""
    //     0xb99a1c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb99a20: b               #0xb99a28
    // 0xb99a24: mov             x4, x1
    // 0xb99a28: ldur            x3, [fp, #-0x20]
    // 0xb99a2c: stur            x4, [fp, #-0x28]
    // 0xb99a30: LoadField: r1 = r3->field_13
    //     0xb99a30: ldur            w1, [x3, #0x13]
    // 0xb99a34: DecompressPointer r1
    //     0xb99a34: add             x1, x1, HEAP, lsl #32
    // 0xb99a38: r0 = of()
    //     0xb99a38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb99a3c: LoadField: r1 = r0->field_87
    //     0xb99a3c: ldur            w1, [x0, #0x87]
    // 0xb99a40: DecompressPointer r1
    //     0xb99a40: add             x1, x1, HEAP, lsl #32
    // 0xb99a44: LoadField: r0 = r1->field_2b
    //     0xb99a44: ldur            w0, [x1, #0x2b]
    // 0xb99a48: DecompressPointer r0
    //     0xb99a48: add             x0, x0, HEAP, lsl #32
    // 0xb99a4c: LoadField: r1 = r0->field_13
    //     0xb99a4c: ldur            w1, [x0, #0x13]
    // 0xb99a50: DecompressPointer r1
    //     0xb99a50: add             x1, x1, HEAP, lsl #32
    // 0xb99a54: r16 = Instance_Color
    //     0xb99a54: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99a58: stp             x16, x1, [SP]
    // 0xb99a5c: r1 = Instance_TextStyle
    //     0xb99a5c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xb99a60: ldr             x1, [x1, #0x9b0]
    // 0xb99a64: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb99a64: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb99a68: ldr             x4, [x4, #0x9b8]
    // 0xb99a6c: r0 = copyWith()
    //     0xb99a6c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb99a70: ldur            x2, [fp, #-0x20]
    // 0xb99a74: stur            x0, [fp, #-0x30]
    // 0xb99a78: LoadField: r1 = r2->field_13
    //     0xb99a78: ldur            w1, [x2, #0x13]
    // 0xb99a7c: DecompressPointer r1
    //     0xb99a7c: add             x1, x1, HEAP, lsl #32
    // 0xb99a80: r0 = of()
    //     0xb99a80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb99a84: LoadField: r1 = r0->field_87
    //     0xb99a84: ldur            w1, [x0, #0x87]
    // 0xb99a88: DecompressPointer r1
    //     0xb99a88: add             x1, x1, HEAP, lsl #32
    // 0xb99a8c: LoadField: r0 = r1->field_7
    //     0xb99a8c: ldur            w0, [x1, #7]
    // 0xb99a90: DecompressPointer r0
    //     0xb99a90: add             x0, x0, HEAP, lsl #32
    // 0xb99a94: r16 = 12.000000
    //     0xb99a94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb99a98: ldr             x16, [x16, #0x9e8]
    // 0xb99a9c: r30 = Instance_Color
    //     0xb99a9c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99aa0: stp             lr, x16, [SP, #8]
    // 0xb99aa4: r16 = Instance_FontWeight
    //     0xb99aa4: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb99aa8: ldr             x16, [x16, #0x20]
    // 0xb99aac: str             x16, [SP]
    // 0xb99ab0: mov             x1, x0
    // 0xb99ab4: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb99ab4: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb99ab8: ldr             x4, [x4, #0xc48]
    // 0xb99abc: r0 = copyWith()
    //     0xb99abc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb99ac0: ldur            x2, [fp, #-0x20]
    // 0xb99ac4: stur            x0, [fp, #-0x38]
    // 0xb99ac8: LoadField: r1 = r2->field_13
    //     0xb99ac8: ldur            w1, [x2, #0x13]
    // 0xb99acc: DecompressPointer r1
    //     0xb99acc: add             x1, x1, HEAP, lsl #32
    // 0xb99ad0: r0 = of()
    //     0xb99ad0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb99ad4: LoadField: r1 = r0->field_87
    //     0xb99ad4: ldur            w1, [x0, #0x87]
    // 0xb99ad8: DecompressPointer r1
    //     0xb99ad8: add             x1, x1, HEAP, lsl #32
    // 0xb99adc: LoadField: r0 = r1->field_7
    //     0xb99adc: ldur            w0, [x1, #7]
    // 0xb99ae0: DecompressPointer r0
    //     0xb99ae0: add             x0, x0, HEAP, lsl #32
    // 0xb99ae4: r16 = 12.000000
    //     0xb99ae4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb99ae8: ldr             x16, [x16, #0x9e8]
    // 0xb99aec: r30 = Instance_Color
    //     0xb99aec: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb99af0: stp             lr, x16, [SP, #8]
    // 0xb99af4: r16 = Instance_FontWeight
    //     0xb99af4: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb99af8: ldr             x16, [x16, #0x20]
    // 0xb99afc: str             x16, [SP]
    // 0xb99b00: mov             x1, x0
    // 0xb99b04: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb99b04: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb99b08: ldr             x4, [x4, #0xc48]
    // 0xb99b0c: r0 = copyWith()
    //     0xb99b0c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb99b10: mov             x1, x0
    // 0xb99b14: ldur            x0, [fp, #-8]
    // 0xb99b18: stur            x1, [fp, #-0x50]
    // 0xb99b1c: LoadField: r2 = r0->field_1f
    //     0xb99b1c: ldur            w2, [x0, #0x1f]
    // 0xb99b20: DecompressPointer r2
    //     0xb99b20: add             x2, x2, HEAP, lsl #32
    // 0xb99b24: stur            x2, [fp, #-0x48]
    // 0xb99b28: r0 = ReadMoreText()
    //     0xb99b28: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb99b2c: mov             x1, x0
    // 0xb99b30: ldur            x0, [fp, #-0x28]
    // 0xb99b34: stur            x1, [fp, #-8]
    // 0xb99b38: StoreField: r1->field_3f = r0
    //     0xb99b38: stur            w0, [x1, #0x3f]
    // 0xb99b3c: ldur            x0, [fp, #-0x48]
    // 0xb99b40: StoreField: r1->field_b = r0
    //     0xb99b40: stur            w0, [x1, #0xb]
    // 0xb99b44: r0 = " Read Less"
    //     0xb99b44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xb99b48: ldr             x0, [x0, #0x9c0]
    // 0xb99b4c: StoreField: r1->field_43 = r0
    //     0xb99b4c: stur            w0, [x1, #0x43]
    // 0xb99b50: r0 = "Read More"
    //     0xb99b50: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xb99b54: ldr             x0, [x0, #0x9c8]
    // 0xb99b58: StoreField: r1->field_47 = r0
    //     0xb99b58: stur            w0, [x1, #0x47]
    // 0xb99b5c: r0 = 240
    //     0xb99b5c: movz            x0, #0xf0
    // 0xb99b60: StoreField: r1->field_f = r0
    //     0xb99b60: stur            x0, [x1, #0xf]
    // 0xb99b64: r0 = 2
    //     0xb99b64: movz            x0, #0x2
    // 0xb99b68: ArrayStore: r1[0] = r0  ; List_8
    //     0xb99b68: stur            x0, [x1, #0x17]
    // 0xb99b6c: r0 = Instance_TrimMode
    //     0xb99b6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb99b70: ldr             x0, [x0, #0x9d0]
    // 0xb99b74: StoreField: r1->field_1f = r0
    //     0xb99b74: stur            w0, [x1, #0x1f]
    // 0xb99b78: ldur            x0, [fp, #-0x30]
    // 0xb99b7c: StoreField: r1->field_4f = r0
    //     0xb99b7c: stur            w0, [x1, #0x4f]
    // 0xb99b80: r0 = Instance_TextAlign
    //     0xb99b80: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb99b84: StoreField: r1->field_53 = r0
    //     0xb99b84: stur            w0, [x1, #0x53]
    // 0xb99b88: ldur            x0, [fp, #-0x38]
    // 0xb99b8c: StoreField: r1->field_23 = r0
    //     0xb99b8c: stur            w0, [x1, #0x23]
    // 0xb99b90: ldur            x0, [fp, #-0x50]
    // 0xb99b94: StoreField: r1->field_27 = r0
    //     0xb99b94: stur            w0, [x1, #0x27]
    // 0xb99b98: r0 = "… "
    //     0xb99b98: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb99b9c: ldr             x0, [x0, #0x9d8]
    // 0xb99ba0: StoreField: r1->field_3b = r0
    //     0xb99ba0: stur            w0, [x1, #0x3b]
    // 0xb99ba4: r0 = true
    //     0xb99ba4: add             x0, NULL, #0x20  ; true
    // 0xb99ba8: StoreField: r1->field_37 = r0
    //     0xb99ba8: stur            w0, [x1, #0x37]
    // 0xb99bac: r0 = Container()
    //     0xb99bac: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb99bb0: stur            x0, [fp, #-0x28]
    // 0xb99bb4: r16 = Instance_EdgeInsets
    //     0xb99bb4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb99bb8: ldr             x16, [x16, #0x668]
    // 0xb99bbc: r30 = Instance_BoxDecoration
    //     0xb99bbc: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb99bc0: ldr             lr, [lr, #0x5a8]
    // 0xb99bc4: stp             lr, x16, [SP, #0x10]
    // 0xb99bc8: r16 = Instance_Alignment
    //     0xb99bc8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0xb99bcc: ldr             x16, [x16, #0xf98]
    // 0xb99bd0: ldur            lr, [fp, #-8]
    // 0xb99bd4: stp             lr, x16, [SP]
    // 0xb99bd8: mov             x1, x0
    // 0xb99bdc: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x3, child, 0x4, decoration, 0x2, padding, 0x1, null]
    //     0xb99bdc: add             x4, PP, #0x51, lsl #12  ; [pp+0x51ea0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x3, "child", 0x4, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb99be0: ldr             x4, [x4, #0xea0]
    // 0xb99be4: r0 = Container()
    //     0xb99be4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb99be8: r0 = Padding()
    //     0xb99be8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb99bec: mov             x2, x0
    // 0xb99bf0: r0 = Instance_EdgeInsets
    //     0xb99bf0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a18] Obj!EdgeInsets@d58b51
    //     0xb99bf4: ldr             x0, [x0, #0xa18]
    // 0xb99bf8: stur            x2, [fp, #-8]
    // 0xb99bfc: StoreField: r2->field_f = r0
    //     0xb99bfc: stur            w0, [x2, #0xf]
    // 0xb99c00: ldur            x0, [fp, #-0x28]
    // 0xb99c04: StoreField: r2->field_b = r0
    //     0xb99c04: stur            w0, [x2, #0xb]
    // 0xb99c08: ldur            x0, [fp, #-0x18]
    // 0xb99c0c: LoadField: r1 = r0->field_b
    //     0xb99c0c: ldur            w1, [x0, #0xb]
    // 0xb99c10: LoadField: r3 = r0->field_f
    //     0xb99c10: ldur            w3, [x0, #0xf]
    // 0xb99c14: DecompressPointer r3
    //     0xb99c14: add             x3, x3, HEAP, lsl #32
    // 0xb99c18: LoadField: r4 = r3->field_b
    //     0xb99c18: ldur            w4, [x3, #0xb]
    // 0xb99c1c: r3 = LoadInt32Instr(r1)
    //     0xb99c1c: sbfx            x3, x1, #1, #0x1f
    // 0xb99c20: stur            x3, [fp, #-0x40]
    // 0xb99c24: r1 = LoadInt32Instr(r4)
    //     0xb99c24: sbfx            x1, x4, #1, #0x1f
    // 0xb99c28: cmp             x3, x1
    // 0xb99c2c: b.ne            #0xb99c38
    // 0xb99c30: mov             x1, x0
    // 0xb99c34: r0 = _growToNextCapacity()
    //     0xb99c34: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb99c38: ldur            x2, [fp, #-0x18]
    // 0xb99c3c: ldur            x3, [fp, #-0x40]
    // 0xb99c40: add             x0, x3, #1
    // 0xb99c44: lsl             x1, x0, #1
    // 0xb99c48: StoreField: r2->field_b = r1
    //     0xb99c48: stur            w1, [x2, #0xb]
    // 0xb99c4c: LoadField: r1 = r2->field_f
    //     0xb99c4c: ldur            w1, [x2, #0xf]
    // 0xb99c50: DecompressPointer r1
    //     0xb99c50: add             x1, x1, HEAP, lsl #32
    // 0xb99c54: ldur            x0, [fp, #-8]
    // 0xb99c58: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb99c58: add             x25, x1, x3, lsl #2
    //     0xb99c5c: add             x25, x25, #0xf
    //     0xb99c60: str             w0, [x25]
    //     0xb99c64: tbz             w0, #0, #0xb99c80
    //     0xb99c68: ldurb           w16, [x1, #-1]
    //     0xb99c6c: ldurb           w17, [x0, #-1]
    //     0xb99c70: and             x16, x17, x16, lsr #2
    //     0xb99c74: tst             x16, HEAP, lsr #32
    //     0xb99c78: b.eq            #0xb99c80
    //     0xb99c7c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb99c80: r0 = GestureDetector()
    //     0xb99c80: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb99c84: ldur            x2, [fp, #-0x20]
    // 0xb99c88: r1 = Function '<anonymous closure>':.
    //     0xb99c88: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f28] AnonymousClosure: (0xaaa130), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xb99c8c: ldr             x1, [x1, #0xf28]
    // 0xb99c90: stur            x0, [fp, #-8]
    // 0xb99c94: r0 = AllocateClosure()
    //     0xb99c94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb99c98: ldur            x2, [fp, #-0x20]
    // 0xb99c9c: r1 = Function '<anonymous closure>':.
    //     0xb99c9c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f30] AnonymousClosure: (0xb99fd0), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb97d0c)
    //     0xb99ca0: ldr             x1, [x1, #0xf30]
    // 0xb99ca4: stur            x0, [fp, #-0x20]
    // 0xb99ca8: r0 = AllocateClosure()
    //     0xb99ca8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb99cac: ldur            x16, [fp, #-0x20]
    // 0xb99cb0: stp             x0, x16, [SP, #8]
    // 0xb99cb4: r16 = Instance_Icon
    //     0xb99cb4: add             x16, PP, #0x51, lsl #12  ; [pp+0x51eb8] Obj!Icon@d66531
    //     0xb99cb8: ldr             x16, [x16, #0xeb8]
    // 0xb99cbc: str             x16, [SP]
    // 0xb99cc0: ldur            x1, [fp, #-8]
    // 0xb99cc4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xb99cc4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xb99cc8: ldr             x4, [x4, #0xa20]
    // 0xb99ccc: r0 = GestureDetector()
    //     0xb99ccc: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb99cd0: r0 = Align()
    //     0xb99cd0: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb99cd4: mov             x1, x0
    // 0xb99cd8: r0 = Instance_Alignment
    //     0xb99cd8: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb99cdc: ldr             x0, [x0, #0xa78]
    // 0xb99ce0: stur            x1, [fp, #-0x20]
    // 0xb99ce4: StoreField: r1->field_f = r0
    //     0xb99ce4: stur            w0, [x1, #0xf]
    // 0xb99ce8: ldur            x0, [fp, #-8]
    // 0xb99cec: StoreField: r1->field_b = r0
    //     0xb99cec: stur            w0, [x1, #0xb]
    // 0xb99cf0: r0 = Padding()
    //     0xb99cf0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb99cf4: mov             x2, x0
    // 0xb99cf8: r0 = Instance_EdgeInsets
    //     0xb99cf8: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ec0] Obj!EdgeInsets@d58af1
    //     0xb99cfc: ldr             x0, [x0, #0xec0]
    // 0xb99d00: stur            x2, [fp, #-8]
    // 0xb99d04: StoreField: r2->field_f = r0
    //     0xb99d04: stur            w0, [x2, #0xf]
    // 0xb99d08: ldur            x0, [fp, #-0x20]
    // 0xb99d0c: StoreField: r2->field_b = r0
    //     0xb99d0c: stur            w0, [x2, #0xb]
    // 0xb99d10: ldur            x0, [fp, #-0x18]
    // 0xb99d14: LoadField: r1 = r0->field_b
    //     0xb99d14: ldur            w1, [x0, #0xb]
    // 0xb99d18: LoadField: r3 = r0->field_f
    //     0xb99d18: ldur            w3, [x0, #0xf]
    // 0xb99d1c: DecompressPointer r3
    //     0xb99d1c: add             x3, x3, HEAP, lsl #32
    // 0xb99d20: LoadField: r4 = r3->field_b
    //     0xb99d20: ldur            w4, [x3, #0xb]
    // 0xb99d24: r3 = LoadInt32Instr(r1)
    //     0xb99d24: sbfx            x3, x1, #1, #0x1f
    // 0xb99d28: stur            x3, [fp, #-0x40]
    // 0xb99d2c: r1 = LoadInt32Instr(r4)
    //     0xb99d2c: sbfx            x1, x4, #1, #0x1f
    // 0xb99d30: cmp             x3, x1
    // 0xb99d34: b.ne            #0xb99d40
    // 0xb99d38: mov             x1, x0
    // 0xb99d3c: r0 = _growToNextCapacity()
    //     0xb99d3c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb99d40: ldur            x4, [fp, #-0x10]
    // 0xb99d44: ldur            x2, [fp, #-0x18]
    // 0xb99d48: ldur            x3, [fp, #-0x40]
    // 0xb99d4c: add             x0, x3, #1
    // 0xb99d50: lsl             x1, x0, #1
    // 0xb99d54: StoreField: r2->field_b = r1
    //     0xb99d54: stur            w1, [x2, #0xb]
    // 0xb99d58: LoadField: r1 = r2->field_f
    //     0xb99d58: ldur            w1, [x2, #0xf]
    // 0xb99d5c: DecompressPointer r1
    //     0xb99d5c: add             x1, x1, HEAP, lsl #32
    // 0xb99d60: ldur            x0, [fp, #-8]
    // 0xb99d64: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb99d64: add             x25, x1, x3, lsl #2
    //     0xb99d68: add             x25, x25, #0xf
    //     0xb99d6c: str             w0, [x25]
    //     0xb99d70: tbz             w0, #0, #0xb99d8c
    //     0xb99d74: ldurb           w16, [x1, #-1]
    //     0xb99d78: ldurb           w17, [x0, #-1]
    //     0xb99d7c: and             x16, x17, x16, lsr #2
    //     0xb99d80: tst             x16, HEAP, lsr #32
    //     0xb99d84: b.eq            #0xb99d8c
    //     0xb99d88: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb99d8c: r0 = Column()
    //     0xb99d8c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb99d90: mov             x3, x0
    // 0xb99d94: r0 = Instance_Axis
    //     0xb99d94: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb99d98: stur            x3, [fp, #-8]
    // 0xb99d9c: StoreField: r3->field_f = r0
    //     0xb99d9c: stur            w0, [x3, #0xf]
    // 0xb99da0: r4 = Instance_MainAxisAlignment
    //     0xb99da0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb99da4: ldr             x4, [x4, #0xa08]
    // 0xb99da8: StoreField: r3->field_13 = r4
    //     0xb99da8: stur            w4, [x3, #0x13]
    // 0xb99dac: r5 = Instance_MainAxisSize
    //     0xb99dac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb99db0: ldr             x5, [x5, #0xa10]
    // 0xb99db4: ArrayStore: r3[0] = r5  ; List_4
    //     0xb99db4: stur            w5, [x3, #0x17]
    // 0xb99db8: r6 = Instance_CrossAxisAlignment
    //     0xb99db8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb99dbc: ldr             x6, [x6, #0xa18]
    // 0xb99dc0: StoreField: r3->field_1b = r6
    //     0xb99dc0: stur            w6, [x3, #0x1b]
    // 0xb99dc4: r7 = Instance_VerticalDirection
    //     0xb99dc4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb99dc8: ldr             x7, [x7, #0xa20]
    // 0xb99dcc: StoreField: r3->field_23 = r7
    //     0xb99dcc: stur            w7, [x3, #0x23]
    // 0xb99dd0: r8 = Instance_Clip
    //     0xb99dd0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb99dd4: ldr             x8, [x8, #0x38]
    // 0xb99dd8: StoreField: r3->field_2b = r8
    //     0xb99dd8: stur            w8, [x3, #0x2b]
    // 0xb99ddc: StoreField: r3->field_2f = rZR
    //     0xb99ddc: stur            xzr, [x3, #0x2f]
    // 0xb99de0: ldur            x1, [fp, #-0x18]
    // 0xb99de4: StoreField: r3->field_b = r1
    //     0xb99de4: stur            w1, [x3, #0xb]
    // 0xb99de8: r1 = Null
    //     0xb99de8: mov             x1, NULL
    // 0xb99dec: r2 = 4
    //     0xb99dec: movz            x2, #0x4
    // 0xb99df0: r0 = AllocateArray()
    //     0xb99df0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb99df4: mov             x2, x0
    // 0xb99df8: ldur            x0, [fp, #-0x10]
    // 0xb99dfc: stur            x2, [fp, #-0x18]
    // 0xb99e00: StoreField: r2->field_f = r0
    //     0xb99e00: stur            w0, [x2, #0xf]
    // 0xb99e04: ldur            x0, [fp, #-8]
    // 0xb99e08: StoreField: r2->field_13 = r0
    //     0xb99e08: stur            w0, [x2, #0x13]
    // 0xb99e0c: r1 = <Widget>
    //     0xb99e0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb99e10: r0 = AllocateGrowableArray()
    //     0xb99e10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb99e14: mov             x1, x0
    // 0xb99e18: ldur            x0, [fp, #-0x18]
    // 0xb99e1c: stur            x1, [fp, #-8]
    // 0xb99e20: StoreField: r1->field_f = r0
    //     0xb99e20: stur            w0, [x1, #0xf]
    // 0xb99e24: r0 = 4
    //     0xb99e24: movz            x0, #0x4
    // 0xb99e28: StoreField: r1->field_b = r0
    //     0xb99e28: stur            w0, [x1, #0xb]
    // 0xb99e2c: r0 = Column()
    //     0xb99e2c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb99e30: mov             x1, x0
    // 0xb99e34: r0 = Instance_Axis
    //     0xb99e34: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb99e38: stur            x1, [fp, #-0x10]
    // 0xb99e3c: StoreField: r1->field_f = r0
    //     0xb99e3c: stur            w0, [x1, #0xf]
    // 0xb99e40: r0 = Instance_MainAxisAlignment
    //     0xb99e40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb99e44: ldr             x0, [x0, #0xa08]
    // 0xb99e48: StoreField: r1->field_13 = r0
    //     0xb99e48: stur            w0, [x1, #0x13]
    // 0xb99e4c: r0 = Instance_MainAxisSize
    //     0xb99e4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb99e50: ldr             x0, [x0, #0xa10]
    // 0xb99e54: ArrayStore: r1[0] = r0  ; List_4
    //     0xb99e54: stur            w0, [x1, #0x17]
    // 0xb99e58: r0 = Instance_CrossAxisAlignment
    //     0xb99e58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb99e5c: ldr             x0, [x0, #0xa18]
    // 0xb99e60: StoreField: r1->field_1b = r0
    //     0xb99e60: stur            w0, [x1, #0x1b]
    // 0xb99e64: r0 = Instance_VerticalDirection
    //     0xb99e64: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb99e68: ldr             x0, [x0, #0xa20]
    // 0xb99e6c: StoreField: r1->field_23 = r0
    //     0xb99e6c: stur            w0, [x1, #0x23]
    // 0xb99e70: r0 = Instance_Clip
    //     0xb99e70: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb99e74: ldr             x0, [x0, #0x38]
    // 0xb99e78: StoreField: r1->field_2b = r0
    //     0xb99e78: stur            w0, [x1, #0x2b]
    // 0xb99e7c: StoreField: r1->field_2f = rZR
    //     0xb99e7c: stur            xzr, [x1, #0x2f]
    // 0xb99e80: ldur            x0, [fp, #-8]
    // 0xb99e84: StoreField: r1->field_b = r0
    //     0xb99e84: stur            w0, [x1, #0xb]
    // 0xb99e88: r0 = SafeArea()
    //     0xb99e88: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb99e8c: mov             x1, x0
    // 0xb99e90: r0 = true
    //     0xb99e90: add             x0, NULL, #0x20  ; true
    // 0xb99e94: stur            x1, [fp, #-8]
    // 0xb99e98: StoreField: r1->field_b = r0
    //     0xb99e98: stur            w0, [x1, #0xb]
    // 0xb99e9c: StoreField: r1->field_f = r0
    //     0xb99e9c: stur            w0, [x1, #0xf]
    // 0xb99ea0: StoreField: r1->field_13 = r0
    //     0xb99ea0: stur            w0, [x1, #0x13]
    // 0xb99ea4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb99ea4: stur            w0, [x1, #0x17]
    // 0xb99ea8: r2 = Instance_EdgeInsets
    //     0xb99ea8: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb99eac: StoreField: r1->field_1b = r2
    //     0xb99eac: stur            w2, [x1, #0x1b]
    // 0xb99eb0: r2 = false
    //     0xb99eb0: add             x2, NULL, #0x30  ; false
    // 0xb99eb4: StoreField: r1->field_1f = r2
    //     0xb99eb4: stur            w2, [x1, #0x1f]
    // 0xb99eb8: ldur            x3, [fp, #-0x10]
    // 0xb99ebc: StoreField: r1->field_23 = r3
    //     0xb99ebc: stur            w3, [x1, #0x23]
    // 0xb99ec0: r0 = Scaffold()
    //     0xb99ec0: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb99ec4: ldur            x1, [fp, #-8]
    // 0xb99ec8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb99ec8: stur            w1, [x0, #0x17]
    // 0xb99ecc: r1 = Instance_Color
    //     0xb99ecc: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb99ed0: StoreField: r0->field_33 = r1
    //     0xb99ed0: stur            w1, [x0, #0x33]
    // 0xb99ed4: r1 = true
    //     0xb99ed4: add             x1, NULL, #0x20  ; true
    // 0xb99ed8: StoreField: r0->field_43 = r1
    //     0xb99ed8: stur            w1, [x0, #0x43]
    // 0xb99edc: r1 = false
    //     0xb99edc: add             x1, NULL, #0x30  ; false
    // 0xb99ee0: StoreField: r0->field_b = r1
    //     0xb99ee0: stur            w1, [x0, #0xb]
    // 0xb99ee4: StoreField: r0->field_f = r1
    //     0xb99ee4: stur            w1, [x0, #0xf]
    // 0xb99ee8: LeaveFrame
    //     0xb99ee8: mov             SP, fp
    //     0xb99eec: ldp             fp, lr, [SP], #0x10
    // 0xb99ef0: ret
    //     0xb99ef0: ret             
    // 0xb99ef4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb99ef4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb99ef8: b               #0xb97d30
    // 0xb99efc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99efc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99f00: r9 = _pageController
    //     0xb99f00: add             x9, PP, #0x54, lsl #12  ; [pp+0x54f38] Field <_RatingReviewOnTapImageState@1634380928._pageController@1634380928>: late (offset: 0x1c)
    //     0xb99f04: ldr             x9, [x9, #0xf38]
    // 0xb99f08: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb99f08: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb99f0c: SaveReg d0
    //     0xb99f0c: str             q0, [SP, #-0x10]!
    // 0xb99f10: stp             x0, x2, [SP, #-0x10]!
    // 0xb99f14: r0 = AllocateDouble()
    //     0xb99f14: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb99f18: mov             x1, x0
    // 0xb99f1c: ldp             x0, x2, [SP], #0x10
    // 0xb99f20: RestoreReg d0
    //     0xb99f20: ldr             q0, [SP], #0x10
    // 0xb99f24: b               #0xb97f1c
    // 0xb99f28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99f28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99f2c: SaveReg d0
    //     0xb99f2c: str             q0, [SP, #-0x10]!
    // 0xb99f30: stp             x0, x2, [SP, #-0x10]!
    // 0xb99f34: r0 = AllocateDouble()
    //     0xb99f34: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb99f38: mov             x1, x0
    // 0xb99f3c: ldp             x0, x2, [SP], #0x10
    // 0xb99f40: RestoreReg d0
    //     0xb99f40: ldr             q0, [SP], #0x10
    // 0xb99f44: b               #0xb980d8
    // 0xb99f48: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb99f48: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb99f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb99f4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb99f50: b               #0xb98228
    // 0xb99f54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb99f54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb99f58: SaveReg d0
    //     0xb99f58: str             q0, [SP, #-0x10]!
    // 0xb99f5c: stp             x4, x5, [SP, #-0x10]!
    // 0xb99f60: stp             x2, x3, [SP, #-0x10]!
    // 0xb99f64: stp             x0, x1, [SP, #-0x10]!
    // 0xb99f68: r0 = AllocateDouble()
    //     0xb99f68: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb99f6c: mov             x6, x0
    // 0xb99f70: ldp             x0, x1, [SP], #0x10
    // 0xb99f74: ldp             x2, x3, [SP], #0x10
    // 0xb99f78: ldp             x4, x5, [SP], #0x10
    // 0xb99f7c: RestoreReg d0
    //     0xb99f7c: ldr             q0, [SP], #0x10
    // 0xb99f80: b               #0xb98388
    // 0xb99f84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99f84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99f88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99f88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99f8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99f8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99f90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99f90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99f94: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb99f94: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb99f98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99f98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99f9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99f9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fa0: SaveReg d0
    //     0xb99fa0: str             q0, [SP, #-0x10]!
    // 0xb99fa4: r0 = AllocateDouble()
    //     0xb99fa4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb99fa8: RestoreReg d0
    //     0xb99fa8: ldr             q0, [SP], #0x10
    // 0xb99fac: b               #0xb98ef8
    // 0xb99fb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fc8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb99fcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb99fcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb99fd0, size: 0xa8
    // 0xb99fd0: EnterFrame
    //     0xb99fd0: stp             fp, lr, [SP, #-0x10]!
    //     0xb99fd4: mov             fp, SP
    // 0xb99fd8: ldr             x0, [fp, #0x10]
    // 0xb99fdc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb99fdc: ldur            w1, [x0, #0x17]
    // 0xb99fe0: DecompressPointer r1
    //     0xb99fe0: add             x1, x1, HEAP, lsl #32
    // 0xb99fe4: CheckStackOverflow
    //     0xb99fe4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb99fe8: cmp             SP, x16
    //     0xb99fec: b.ls            #0xb9a06c
    // 0xb99ff0: LoadField: r0 = r1->field_f
    //     0xb99ff0: ldur            w0, [x1, #0xf]
    // 0xb99ff4: DecompressPointer r0
    //     0xb99ff4: add             x0, x0, HEAP, lsl #32
    // 0xb99ff8: LoadField: r3 = r0->field_2b
    //     0xb99ff8: ldur            w3, [x0, #0x2b]
    // 0xb99ffc: DecompressPointer r3
    //     0xb99ffc: add             x3, x3, HEAP, lsl #32
    // 0xb9a000: cmp             w3, NULL
    // 0xb9a004: b.eq            #0xb9a05c
    // 0xb9a008: LoadField: r2 = r1->field_13
    //     0xb9a008: ldur            w2, [x1, #0x13]
    // 0xb9a00c: DecompressPointer r2
    //     0xb9a00c: add             x2, x2, HEAP, lsl #32
    // 0xb9a010: LoadField: r1 = r0->field_b
    //     0xb9a010: ldur            w1, [x0, #0xb]
    // 0xb9a014: DecompressPointer r1
    //     0xb9a014: add             x1, x1, HEAP, lsl #32
    // 0xb9a018: cmp             w1, NULL
    // 0xb9a01c: b.eq            #0xb9a074
    // 0xb9a020: LoadField: r4 = r1->field_b
    //     0xb9a020: ldur            w4, [x1, #0xb]
    // 0xb9a024: DecompressPointer r4
    //     0xb9a024: add             x4, x4, HEAP, lsl #32
    // 0xb9a028: cmp             w4, NULL
    // 0xb9a02c: b.ne            #0xb9a038
    // 0xb9a030: r1 = Null
    //     0xb9a030: mov             x1, NULL
    // 0xb9a034: b               #0xb9a040
    // 0xb9a038: LoadField: r1 = r4->field_b
    //     0xb9a038: ldur            w1, [x4, #0xb]
    // 0xb9a03c: DecompressPointer r1
    //     0xb9a03c: add             x1, x1, HEAP, lsl #32
    // 0xb9a040: cmp             w1, NULL
    // 0xb9a044: b.ne            #0xb9a050
    // 0xb9a048: r5 = ""
    //     0xb9a048: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9a04c: b               #0xb9a054
    // 0xb9a050: mov             x5, x1
    // 0xb9a054: mov             x1, x0
    // 0xb9a058: r0 = showMenuItem()
    //     0xb9a058: bl              #0xb9a078  ; [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem
    // 0xb9a05c: r0 = Null
    //     0xb9a05c: mov             x0, NULL
    // 0xb9a060: LeaveFrame
    //     0xb9a060: mov             SP, fp
    //     0xb9a064: ldp             fp, lr, [SP], #0x10
    // 0xb9a068: ret
    //     0xb9a068: ret             
    // 0xb9a06c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9a06c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9a070: b               #0xb99ff0
    // 0xb9a074: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9a074: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xb9a078, size: 0x744
    // 0xb9a078: EnterFrame
    //     0xb9a078: stp             fp, lr, [SP, #-0x10]!
    //     0xb9a07c: mov             fp, SP
    // 0xb9a080: AllocStack(0xa0)
    //     0xb9a080: sub             SP, SP, #0xa0
    // 0xb9a084: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xb9a084: mov             x0, x1
    //     0xb9a088: stur            x1, [fp, #-8]
    //     0xb9a08c: mov             x1, x2
    //     0xb9a090: stur            x2, [fp, #-0x10]
    //     0xb9a094: mov             x2, x5
    //     0xb9a098: stur            x3, [fp, #-0x18]
    //     0xb9a09c: stur            x5, [fp, #-0x20]
    // 0xb9a0a0: CheckStackOverflow
    //     0xb9a0a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9a0a4: cmp             SP, x16
    //     0xb9a0a8: b.ls            #0xb9a734
    // 0xb9a0ac: r1 = 2
    //     0xb9a0ac: movz            x1, #0x2
    // 0xb9a0b0: r0 = AllocateContext()
    //     0xb9a0b0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9a0b4: mov             x4, x0
    // 0xb9a0b8: ldur            x3, [fp, #-8]
    // 0xb9a0bc: stur            x4, [fp, #-0x28]
    // 0xb9a0c0: StoreField: r4->field_f = r3
    //     0xb9a0c0: stur            w3, [x4, #0xf]
    // 0xb9a0c4: ldur            x2, [fp, #-0x20]
    // 0xb9a0c8: StoreField: r4->field_13 = r2
    //     0xb9a0c8: stur            w2, [x4, #0x13]
    // 0xb9a0cc: LoadField: r1 = r3->field_27
    //     0xb9a0cc: ldur            w1, [x3, #0x27]
    // 0xb9a0d0: DecompressPointer r1
    //     0xb9a0d0: add             x1, x1, HEAP, lsl #32
    // 0xb9a0d4: r0 = LoadClassIdInstr(r1)
    //     0xb9a0d4: ldur            x0, [x1, #-1]
    //     0xb9a0d8: ubfx            x0, x0, #0xc, #0x14
    // 0xb9a0dc: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb9a0dc: sub             lr, x0, #0xfe
    //     0xb9a0e0: ldr             lr, [x21, lr, lsl #3]
    //     0xb9a0e4: blr             lr
    // 0xb9a0e8: r1 = 60
    //     0xb9a0e8: movz            x1, #0x3c
    // 0xb9a0ec: branchIfSmi(r0, 0xb9a0f8)
    //     0xb9a0ec: tbz             w0, #0, #0xb9a0f8
    // 0xb9a0f0: r1 = LoadClassIdInstr(r0)
    //     0xb9a0f0: ldur            x1, [x0, #-1]
    //     0xb9a0f4: ubfx            x1, x1, #0xc, #0x14
    // 0xb9a0f8: r16 = true
    //     0xb9a0f8: add             x16, NULL, #0x20  ; true
    // 0xb9a0fc: stp             x16, x0, [SP]
    // 0xb9a100: mov             x0, x1
    // 0xb9a104: mov             lr, x0
    // 0xb9a108: ldr             lr, [x21, lr, lsl #3]
    // 0xb9a10c: blr             lr
    // 0xb9a110: tbnz            w0, #4, #0xb9a11c
    // 0xb9a114: d0 = 100.000000
    //     0xb9a114: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb9a118: b               #0xb9a124
    // 0xb9a11c: d0 = 120.000000
    //     0xb9a11c: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xb9a120: ldr             d0, [x17, #0xa38]
    // 0xb9a124: ldur            x0, [fp, #-0x18]
    // 0xb9a128: stur            d0, [fp, #-0x58]
    // 0xb9a12c: r0 = BoxConstraints()
    //     0xb9a12c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb9a130: stur            x0, [fp, #-0x20]
    // 0xb9a134: StoreField: r0->field_7 = rZR
    //     0xb9a134: stur            xzr, [x0, #7]
    // 0xb9a138: ldur            d0, [fp, #-0x58]
    // 0xb9a13c: StoreField: r0->field_f = d0
    //     0xb9a13c: stur            d0, [x0, #0xf]
    // 0xb9a140: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb9a140: stur            xzr, [x0, #0x17]
    // 0xb9a144: d0 = inf
    //     0xb9a144: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb9a148: StoreField: r0->field_1f = d0
    //     0xb9a148: stur            d0, [x0, #0x1f]
    // 0xb9a14c: ldur            x1, [fp, #-0x18]
    // 0xb9a150: cmp             w1, NULL
    // 0xb9a154: b.ne            #0xb9a160
    // 0xb9a158: r2 = Null
    //     0xb9a158: mov             x2, NULL
    // 0xb9a15c: b               #0xb9a18c
    // 0xb9a160: LoadField: d0 = r1->field_7
    //     0xb9a160: ldur            d0, [x1, #7]
    // 0xb9a164: r2 = inline_Allocate_Double()
    //     0xb9a164: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb9a168: add             x2, x2, #0x10
    //     0xb9a16c: cmp             x3, x2
    //     0xb9a170: b.ls            #0xb9a73c
    //     0xb9a174: str             x2, [THR, #0x50]  ; THR::top
    //     0xb9a178: sub             x2, x2, #0xf
    //     0xb9a17c: movz            x3, #0xe15c
    //     0xb9a180: movk            x3, #0x3, lsl #16
    //     0xb9a184: stur            x3, [x2, #-1]
    // 0xb9a188: StoreField: r2->field_7 = d0
    //     0xb9a188: stur            d0, [x2, #7]
    // 0xb9a18c: cmp             w2, NULL
    // 0xb9a190: b.ne            #0xb9a19c
    // 0xb9a194: d0 = 0.000000
    //     0xb9a194: eor             v0.16b, v0.16b, v0.16b
    // 0xb9a198: b               #0xb9a1a0
    // 0xb9a19c: LoadField: d0 = r2->field_7
    //     0xb9a19c: ldur            d0, [x2, #7]
    // 0xb9a1a0: stur            d0, [fp, #-0x70]
    // 0xb9a1a4: cmp             w1, NULL
    // 0xb9a1a8: b.ne            #0xb9a1b4
    // 0xb9a1ac: r2 = Null
    //     0xb9a1ac: mov             x2, NULL
    // 0xb9a1b0: b               #0xb9a1e0
    // 0xb9a1b4: LoadField: d1 = r1->field_f
    //     0xb9a1b4: ldur            d1, [x1, #0xf]
    // 0xb9a1b8: r2 = inline_Allocate_Double()
    //     0xb9a1b8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb9a1bc: add             x2, x2, #0x10
    //     0xb9a1c0: cmp             x3, x2
    //     0xb9a1c4: b.ls            #0xb9a758
    //     0xb9a1c8: str             x2, [THR, #0x50]  ; THR::top
    //     0xb9a1cc: sub             x2, x2, #0xf
    //     0xb9a1d0: movz            x3, #0xe15c
    //     0xb9a1d4: movk            x3, #0x3, lsl #16
    //     0xb9a1d8: stur            x3, [x2, #-1]
    // 0xb9a1dc: StoreField: r2->field_7 = d1
    //     0xb9a1dc: stur            d1, [x2, #7]
    // 0xb9a1e0: cmp             w2, NULL
    // 0xb9a1e4: b.ne            #0xb9a1f0
    // 0xb9a1e8: d2 = 0.000000
    //     0xb9a1e8: eor             v2.16b, v2.16b, v2.16b
    // 0xb9a1ec: b               #0xb9a1f8
    // 0xb9a1f0: LoadField: d1 = r2->field_7
    //     0xb9a1f0: ldur            d1, [x2, #7]
    // 0xb9a1f4: mov             v2.16b, v1.16b
    // 0xb9a1f8: d1 = 50.000000
    //     0xb9a1f8: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb9a1fc: fsub            d3, d2, d1
    // 0xb9a200: stur            d3, [fp, #-0x68]
    // 0xb9a204: cmp             w1, NULL
    // 0xb9a208: b.ne            #0xb9a214
    // 0xb9a20c: r2 = Null
    //     0xb9a20c: mov             x2, NULL
    // 0xb9a210: b               #0xb9a240
    // 0xb9a214: LoadField: d2 = r1->field_7
    //     0xb9a214: ldur            d2, [x1, #7]
    // 0xb9a218: r2 = inline_Allocate_Double()
    //     0xb9a218: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb9a21c: add             x2, x2, #0x10
    //     0xb9a220: cmp             x3, x2
    //     0xb9a224: b.ls            #0xb9a774
    //     0xb9a228: str             x2, [THR, #0x50]  ; THR::top
    //     0xb9a22c: sub             x2, x2, #0xf
    //     0xb9a230: movz            x3, #0xe15c
    //     0xb9a234: movk            x3, #0x3, lsl #16
    //     0xb9a238: stur            x3, [x2, #-1]
    // 0xb9a23c: StoreField: r2->field_7 = d2
    //     0xb9a23c: stur            d2, [x2, #7]
    // 0xb9a240: cmp             w2, NULL
    // 0xb9a244: b.ne            #0xb9a250
    // 0xb9a248: d2 = 0.000000
    //     0xb9a248: eor             v2.16b, v2.16b, v2.16b
    // 0xb9a24c: b               #0xb9a254
    // 0xb9a250: LoadField: d2 = r2->field_7
    //     0xb9a250: ldur            d2, [x2, #7]
    // 0xb9a254: fadd            d4, d2, d1
    // 0xb9a258: stur            d4, [fp, #-0x60]
    // 0xb9a25c: cmp             w1, NULL
    // 0xb9a260: b.ne            #0xb9a26c
    // 0xb9a264: r1 = Null
    //     0xb9a264: mov             x1, NULL
    // 0xb9a268: b               #0xb9a298
    // 0xb9a26c: LoadField: d1 = r1->field_f
    //     0xb9a26c: ldur            d1, [x1, #0xf]
    // 0xb9a270: r1 = inline_Allocate_Double()
    //     0xb9a270: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb9a274: add             x1, x1, #0x10
    //     0xb9a278: cmp             x2, x1
    //     0xb9a27c: b.ls            #0xb9a798
    //     0xb9a280: str             x1, [THR, #0x50]  ; THR::top
    //     0xb9a284: sub             x1, x1, #0xf
    //     0xb9a288: movz            x2, #0xe15c
    //     0xb9a28c: movk            x2, #0x3, lsl #16
    //     0xb9a290: stur            x2, [x1, #-1]
    // 0xb9a294: StoreField: r1->field_7 = d1
    //     0xb9a294: stur            d1, [x1, #7]
    // 0xb9a298: cmp             w1, NULL
    // 0xb9a29c: b.ne            #0xb9a2a8
    // 0xb9a2a0: d1 = 0.000000
    //     0xb9a2a0: eor             v1.16b, v1.16b, v1.16b
    // 0xb9a2a4: b               #0xb9a2ac
    // 0xb9a2a8: LoadField: d1 = r1->field_7
    //     0xb9a2a8: ldur            d1, [x1, #7]
    // 0xb9a2ac: ldur            x1, [fp, #-8]
    // 0xb9a2b0: ldur            x2, [fp, #-0x28]
    // 0xb9a2b4: stur            d1, [fp, #-0x58]
    // 0xb9a2b8: r0 = RelativeRect()
    //     0xb9a2b8: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xb9a2bc: mov             x3, x0
    // 0xb9a2c0: ldur            d0, [fp, #-0x70]
    // 0xb9a2c4: stur            x3, [fp, #-0x18]
    // 0xb9a2c8: StoreField: r3->field_7 = d0
    //     0xb9a2c8: stur            d0, [x3, #7]
    // 0xb9a2cc: ldur            d0, [fp, #-0x68]
    // 0xb9a2d0: StoreField: r3->field_f = d0
    //     0xb9a2d0: stur            d0, [x3, #0xf]
    // 0xb9a2d4: ldur            d0, [fp, #-0x60]
    // 0xb9a2d8: ArrayStore: r3[0] = d0  ; List_8
    //     0xb9a2d8: stur            d0, [x3, #0x17]
    // 0xb9a2dc: ldur            d0, [fp, #-0x58]
    // 0xb9a2e0: StoreField: r3->field_1f = d0
    //     0xb9a2e0: stur            d0, [x3, #0x1f]
    // 0xb9a2e4: ldur            x4, [fp, #-8]
    // 0xb9a2e8: LoadField: r1 = r4->field_27
    //     0xb9a2e8: ldur            w1, [x4, #0x27]
    // 0xb9a2ec: DecompressPointer r1
    //     0xb9a2ec: add             x1, x1, HEAP, lsl #32
    // 0xb9a2f0: ldur            x5, [fp, #-0x28]
    // 0xb9a2f4: LoadField: r2 = r5->field_13
    //     0xb9a2f4: ldur            w2, [x5, #0x13]
    // 0xb9a2f8: DecompressPointer r2
    //     0xb9a2f8: add             x2, x2, HEAP, lsl #32
    // 0xb9a2fc: r0 = LoadClassIdInstr(r1)
    //     0xb9a2fc: ldur            x0, [x1, #-1]
    //     0xb9a300: ubfx            x0, x0, #0xc, #0x14
    // 0xb9a304: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb9a304: sub             lr, x0, #0xfe
    //     0xb9a308: ldr             lr, [x21, lr, lsl #3]
    //     0xb9a30c: blr             lr
    // 0xb9a310: r1 = 60
    //     0xb9a310: movz            x1, #0x3c
    // 0xb9a314: branchIfSmi(r0, 0xb9a320)
    //     0xb9a314: tbz             w0, #0, #0xb9a320
    // 0xb9a318: r1 = LoadClassIdInstr(r0)
    //     0xb9a318: ldur            x1, [x0, #-1]
    //     0xb9a31c: ubfx            x1, x1, #0xc, #0x14
    // 0xb9a320: r16 = true
    //     0xb9a320: add             x16, NULL, #0x20  ; true
    // 0xb9a324: stp             x16, x0, [SP]
    // 0xb9a328: mov             x0, x1
    // 0xb9a32c: mov             lr, x0
    // 0xb9a330: ldr             lr, [x21, lr, lsl #3]
    // 0xb9a334: blr             lr
    // 0xb9a338: tbnz            w0, #4, #0xb9a344
    // 0xb9a33c: r4 = Null
    //     0xb9a33c: mov             x4, NULL
    // 0xb9a340: b               #0xb9a358
    // 0xb9a344: ldur            x2, [fp, #-0x28]
    // 0xb9a348: r1 = Function '<anonymous closure>':.
    //     0xb9a348: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f40] AnonymousClosure: (0xb9a850), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xb9a078)
    //     0xb9a34c: ldr             x1, [x1, #0xf40]
    // 0xb9a350: r0 = AllocateClosure()
    //     0xb9a350: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9a354: mov             x4, x0
    // 0xb9a358: ldur            x0, [fp, #-8]
    // 0xb9a35c: ldur            x3, [fp, #-0x28]
    // 0xb9a360: stur            x4, [fp, #-0x30]
    // 0xb9a364: r1 = <Widget>
    //     0xb9a364: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb9a368: r2 = 0
    //     0xb9a368: movz            x2, #0
    // 0xb9a36c: r0 = _GrowableList()
    //     0xb9a36c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb9a370: mov             x4, x0
    // 0xb9a374: ldur            x3, [fp, #-8]
    // 0xb9a378: stur            x4, [fp, #-0x38]
    // 0xb9a37c: LoadField: r1 = r3->field_27
    //     0xb9a37c: ldur            w1, [x3, #0x27]
    // 0xb9a380: DecompressPointer r1
    //     0xb9a380: add             x1, x1, HEAP, lsl #32
    // 0xb9a384: ldur            x5, [fp, #-0x28]
    // 0xb9a388: LoadField: r2 = r5->field_13
    //     0xb9a388: ldur            w2, [x5, #0x13]
    // 0xb9a38c: DecompressPointer r2
    //     0xb9a38c: add             x2, x2, HEAP, lsl #32
    // 0xb9a390: r0 = LoadClassIdInstr(r1)
    //     0xb9a390: ldur            x0, [x1, #-1]
    //     0xb9a394: ubfx            x0, x0, #0xc, #0x14
    // 0xb9a398: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb9a398: sub             lr, x0, #0xfe
    //     0xb9a39c: ldr             lr, [x21, lr, lsl #3]
    //     0xb9a3a0: blr             lr
    // 0xb9a3a4: r1 = 60
    //     0xb9a3a4: movz            x1, #0x3c
    // 0xb9a3a8: branchIfSmi(r0, 0xb9a3b4)
    //     0xb9a3a8: tbz             w0, #0, #0xb9a3b4
    // 0xb9a3ac: r1 = LoadClassIdInstr(r0)
    //     0xb9a3ac: ldur            x1, [x0, #-1]
    //     0xb9a3b0: ubfx            x1, x1, #0xc, #0x14
    // 0xb9a3b4: r16 = true
    //     0xb9a3b4: add             x16, NULL, #0x20  ; true
    // 0xb9a3b8: stp             x16, x0, [SP]
    // 0xb9a3bc: mov             x0, x1
    // 0xb9a3c0: mov             lr, x0
    // 0xb9a3c4: ldr             lr, [x21, lr, lsl #3]
    // 0xb9a3c8: blr             lr
    // 0xb9a3cc: tbnz            w0, #4, #0xb9a430
    // 0xb9a3d0: ldur            x0, [fp, #-0x38]
    // 0xb9a3d4: LoadField: r1 = r0->field_b
    //     0xb9a3d4: ldur            w1, [x0, #0xb]
    // 0xb9a3d8: LoadField: r2 = r0->field_f
    //     0xb9a3d8: ldur            w2, [x0, #0xf]
    // 0xb9a3dc: DecompressPointer r2
    //     0xb9a3dc: add             x2, x2, HEAP, lsl #32
    // 0xb9a3e0: LoadField: r3 = r2->field_b
    //     0xb9a3e0: ldur            w3, [x2, #0xb]
    // 0xb9a3e4: r2 = LoadInt32Instr(r1)
    //     0xb9a3e4: sbfx            x2, x1, #1, #0x1f
    // 0xb9a3e8: stur            x2, [fp, #-0x40]
    // 0xb9a3ec: r1 = LoadInt32Instr(r3)
    //     0xb9a3ec: sbfx            x1, x3, #1, #0x1f
    // 0xb9a3f0: cmp             x2, x1
    // 0xb9a3f4: b.ne            #0xb9a400
    // 0xb9a3f8: mov             x1, x0
    // 0xb9a3fc: r0 = _growToNextCapacity()
    //     0xb9a3fc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb9a400: ldur            x0, [fp, #-0x38]
    // 0xb9a404: ldur            x1, [fp, #-0x40]
    // 0xb9a408: add             x2, x1, #1
    // 0xb9a40c: lsl             x3, x2, #1
    // 0xb9a410: StoreField: r0->field_b = r3
    //     0xb9a410: stur            w3, [x0, #0xb]
    // 0xb9a414: LoadField: r2 = r0->field_f
    //     0xb9a414: ldur            w2, [x0, #0xf]
    // 0xb9a418: DecompressPointer r2
    //     0xb9a418: add             x2, x2, HEAP, lsl #32
    // 0xb9a41c: add             x3, x2, x1, lsl #2
    // 0xb9a420: r16 = Instance_Icon
    //     0xb9a420: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xb9a424: ldr             x16, [x16, #0xa48]
    // 0xb9a428: StoreField: r3->field_f = r16
    //     0xb9a428: stur            w16, [x3, #0xf]
    // 0xb9a42c: b               #0xb9a434
    // 0xb9a430: ldur            x0, [fp, #-0x38]
    // 0xb9a434: LoadField: r1 = r0->field_b
    //     0xb9a434: ldur            w1, [x0, #0xb]
    // 0xb9a438: LoadField: r2 = r0->field_f
    //     0xb9a438: ldur            w2, [x0, #0xf]
    // 0xb9a43c: DecompressPointer r2
    //     0xb9a43c: add             x2, x2, HEAP, lsl #32
    // 0xb9a440: LoadField: r3 = r2->field_b
    //     0xb9a440: ldur            w3, [x2, #0xb]
    // 0xb9a444: r2 = LoadInt32Instr(r1)
    //     0xb9a444: sbfx            x2, x1, #1, #0x1f
    // 0xb9a448: stur            x2, [fp, #-0x40]
    // 0xb9a44c: r1 = LoadInt32Instr(r3)
    //     0xb9a44c: sbfx            x1, x3, #1, #0x1f
    // 0xb9a450: cmp             x2, x1
    // 0xb9a454: b.ne            #0xb9a460
    // 0xb9a458: mov             x1, x0
    // 0xb9a45c: r0 = _growToNextCapacity()
    //     0xb9a45c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb9a460: ldur            x1, [fp, #-8]
    // 0xb9a464: ldur            x4, [fp, #-0x28]
    // 0xb9a468: ldur            x3, [fp, #-0x38]
    // 0xb9a46c: ldur            x0, [fp, #-0x40]
    // 0xb9a470: add             x2, x0, #1
    // 0xb9a474: lsl             x5, x2, #1
    // 0xb9a478: StoreField: r3->field_b = r5
    //     0xb9a478: stur            w5, [x3, #0xb]
    // 0xb9a47c: LoadField: r2 = r3->field_f
    //     0xb9a47c: ldur            w2, [x3, #0xf]
    // 0xb9a480: DecompressPointer r2
    //     0xb9a480: add             x2, x2, HEAP, lsl #32
    // 0xb9a484: add             x5, x2, x0, lsl #2
    // 0xb9a488: r16 = Instance_SizedBox
    //     0xb9a488: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb9a48c: ldr             x16, [x16, #0xa50]
    // 0xb9a490: StoreField: r5->field_f = r16
    //     0xb9a490: stur            w16, [x5, #0xf]
    // 0xb9a494: LoadField: r0 = r1->field_27
    //     0xb9a494: ldur            w0, [x1, #0x27]
    // 0xb9a498: DecompressPointer r0
    //     0xb9a498: add             x0, x0, HEAP, lsl #32
    // 0xb9a49c: LoadField: r2 = r4->field_13
    //     0xb9a49c: ldur            w2, [x4, #0x13]
    // 0xb9a4a0: DecompressPointer r2
    //     0xb9a4a0: add             x2, x2, HEAP, lsl #32
    // 0xb9a4a4: r1 = LoadClassIdInstr(r0)
    //     0xb9a4a4: ldur            x1, [x0, #-1]
    //     0xb9a4a8: ubfx            x1, x1, #0xc, #0x14
    // 0xb9a4ac: mov             x16, x0
    // 0xb9a4b0: mov             x0, x1
    // 0xb9a4b4: mov             x1, x16
    // 0xb9a4b8: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb9a4b8: sub             lr, x0, #0xfe
    //     0xb9a4bc: ldr             lr, [x21, lr, lsl #3]
    //     0xb9a4c0: blr             lr
    // 0xb9a4c4: r1 = 60
    //     0xb9a4c4: movz            x1, #0x3c
    // 0xb9a4c8: branchIfSmi(r0, 0xb9a4d4)
    //     0xb9a4c8: tbz             w0, #0, #0xb9a4d4
    // 0xb9a4cc: r1 = LoadClassIdInstr(r0)
    //     0xb9a4cc: ldur            x1, [x0, #-1]
    //     0xb9a4d0: ubfx            x1, x1, #0xc, #0x14
    // 0xb9a4d4: r16 = true
    //     0xb9a4d4: add             x16, NULL, #0x20  ; true
    // 0xb9a4d8: stp             x16, x0, [SP]
    // 0xb9a4dc: mov             x0, x1
    // 0xb9a4e0: mov             lr, x0
    // 0xb9a4e4: ldr             lr, [x21, lr, lsl #3]
    // 0xb9a4e8: blr             lr
    // 0xb9a4ec: tbnz            w0, #4, #0xb9a4fc
    // 0xb9a4f0: r0 = "Flagged"
    //     0xb9a4f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xb9a4f4: ldr             x0, [x0, #0xa58]
    // 0xb9a4f8: b               #0xb9a504
    // 0xb9a4fc: r0 = "Flag as abusive"
    //     0xb9a4fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xb9a500: ldr             x0, [x0, #0xa60]
    // 0xb9a504: ldur            x1, [fp, #-0x10]
    // 0xb9a508: stur            x0, [fp, #-8]
    // 0xb9a50c: r0 = of()
    //     0xb9a50c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9a510: LoadField: r1 = r0->field_87
    //     0xb9a510: ldur            w1, [x0, #0x87]
    // 0xb9a514: DecompressPointer r1
    //     0xb9a514: add             x1, x1, HEAP, lsl #32
    // 0xb9a518: LoadField: r0 = r1->field_33
    //     0xb9a518: ldur            w0, [x1, #0x33]
    // 0xb9a51c: DecompressPointer r0
    //     0xb9a51c: add             x0, x0, HEAP, lsl #32
    // 0xb9a520: cmp             w0, NULL
    // 0xb9a524: b.ne            #0xb9a530
    // 0xb9a528: r2 = Null
    //     0xb9a528: mov             x2, NULL
    // 0xb9a52c: b               #0xb9a554
    // 0xb9a530: r16 = 12.000000
    //     0xb9a530: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb9a534: ldr             x16, [x16, #0x9e8]
    // 0xb9a538: r30 = Instance_Color
    //     0xb9a538: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb9a53c: stp             lr, x16, [SP]
    // 0xb9a540: mov             x1, x0
    // 0xb9a544: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb9a544: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb9a548: ldr             x4, [x4, #0xaa0]
    // 0xb9a54c: r0 = copyWith()
    //     0xb9a54c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9a550: mov             x2, x0
    // 0xb9a554: ldur            x1, [fp, #-0x38]
    // 0xb9a558: ldur            x0, [fp, #-8]
    // 0xb9a55c: stur            x2, [fp, #-0x48]
    // 0xb9a560: r0 = Text()
    //     0xb9a560: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb9a564: mov             x2, x0
    // 0xb9a568: ldur            x0, [fp, #-8]
    // 0xb9a56c: stur            x2, [fp, #-0x50]
    // 0xb9a570: StoreField: r2->field_b = r0
    //     0xb9a570: stur            w0, [x2, #0xb]
    // 0xb9a574: ldur            x0, [fp, #-0x48]
    // 0xb9a578: StoreField: r2->field_13 = r0
    //     0xb9a578: stur            w0, [x2, #0x13]
    // 0xb9a57c: ldur            x0, [fp, #-0x38]
    // 0xb9a580: LoadField: r1 = r0->field_b
    //     0xb9a580: ldur            w1, [x0, #0xb]
    // 0xb9a584: LoadField: r3 = r0->field_f
    //     0xb9a584: ldur            w3, [x0, #0xf]
    // 0xb9a588: DecompressPointer r3
    //     0xb9a588: add             x3, x3, HEAP, lsl #32
    // 0xb9a58c: LoadField: r4 = r3->field_b
    //     0xb9a58c: ldur            w4, [x3, #0xb]
    // 0xb9a590: r3 = LoadInt32Instr(r1)
    //     0xb9a590: sbfx            x3, x1, #1, #0x1f
    // 0xb9a594: stur            x3, [fp, #-0x40]
    // 0xb9a598: r1 = LoadInt32Instr(r4)
    //     0xb9a598: sbfx            x1, x4, #1, #0x1f
    // 0xb9a59c: cmp             x3, x1
    // 0xb9a5a0: b.ne            #0xb9a5ac
    // 0xb9a5a4: mov             x1, x0
    // 0xb9a5a8: r0 = _growToNextCapacity()
    //     0xb9a5a8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb9a5ac: ldur            x4, [fp, #-0x30]
    // 0xb9a5b0: ldur            x2, [fp, #-0x38]
    // 0xb9a5b4: ldur            x3, [fp, #-0x40]
    // 0xb9a5b8: add             x0, x3, #1
    // 0xb9a5bc: lsl             x1, x0, #1
    // 0xb9a5c0: StoreField: r2->field_b = r1
    //     0xb9a5c0: stur            w1, [x2, #0xb]
    // 0xb9a5c4: LoadField: r1 = r2->field_f
    //     0xb9a5c4: ldur            w1, [x2, #0xf]
    // 0xb9a5c8: DecompressPointer r1
    //     0xb9a5c8: add             x1, x1, HEAP, lsl #32
    // 0xb9a5cc: ldur            x0, [fp, #-0x50]
    // 0xb9a5d0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb9a5d0: add             x25, x1, x3, lsl #2
    //     0xb9a5d4: add             x25, x25, #0xf
    //     0xb9a5d8: str             w0, [x25]
    //     0xb9a5dc: tbz             w0, #0, #0xb9a5f8
    //     0xb9a5e0: ldurb           w16, [x1, #-1]
    //     0xb9a5e4: ldurb           w17, [x0, #-1]
    //     0xb9a5e8: and             x16, x17, x16, lsr #2
    //     0xb9a5ec: tst             x16, HEAP, lsr #32
    //     0xb9a5f0: b.eq            #0xb9a5f8
    //     0xb9a5f4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb9a5f8: r0 = Row()
    //     0xb9a5f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb9a5fc: mov             x2, x0
    // 0xb9a600: r0 = Instance_Axis
    //     0xb9a600: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb9a604: stur            x2, [fp, #-8]
    // 0xb9a608: StoreField: r2->field_f = r0
    //     0xb9a608: stur            w0, [x2, #0xf]
    // 0xb9a60c: r0 = Instance_MainAxisAlignment
    //     0xb9a60c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb9a610: ldr             x0, [x0, #0xa08]
    // 0xb9a614: StoreField: r2->field_13 = r0
    //     0xb9a614: stur            w0, [x2, #0x13]
    // 0xb9a618: r0 = Instance_MainAxisSize
    //     0xb9a618: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb9a61c: ldr             x0, [x0, #0xa10]
    // 0xb9a620: ArrayStore: r2[0] = r0  ; List_4
    //     0xb9a620: stur            w0, [x2, #0x17]
    // 0xb9a624: r0 = Instance_CrossAxisAlignment
    //     0xb9a624: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb9a628: ldr             x0, [x0, #0xa18]
    // 0xb9a62c: StoreField: r2->field_1b = r0
    //     0xb9a62c: stur            w0, [x2, #0x1b]
    // 0xb9a630: r0 = Instance_VerticalDirection
    //     0xb9a630: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb9a634: ldr             x0, [x0, #0xa20]
    // 0xb9a638: StoreField: r2->field_23 = r0
    //     0xb9a638: stur            w0, [x2, #0x23]
    // 0xb9a63c: r0 = Instance_Clip
    //     0xb9a63c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb9a640: ldr             x0, [x0, #0x38]
    // 0xb9a644: StoreField: r2->field_2b = r0
    //     0xb9a644: stur            w0, [x2, #0x2b]
    // 0xb9a648: StoreField: r2->field_2f = rZR
    //     0xb9a648: stur            xzr, [x2, #0x2f]
    // 0xb9a64c: ldur            x0, [fp, #-0x38]
    // 0xb9a650: StoreField: r2->field_b = r0
    //     0xb9a650: stur            w0, [x2, #0xb]
    // 0xb9a654: r1 = <String>
    //     0xb9a654: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb9a658: r0 = PopupMenuItem()
    //     0xb9a658: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xb9a65c: mov             x3, x0
    // 0xb9a660: r0 = "flag"
    //     0xb9a660: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb9a664: ldr             x0, [x0, #0xa68]
    // 0xb9a668: stur            x3, [fp, #-0x38]
    // 0xb9a66c: StoreField: r3->field_f = r0
    //     0xb9a66c: stur            w0, [x3, #0xf]
    // 0xb9a670: ldur            x0, [fp, #-0x30]
    // 0xb9a674: StoreField: r3->field_13 = r0
    //     0xb9a674: stur            w0, [x3, #0x13]
    // 0xb9a678: r0 = true
    //     0xb9a678: add             x0, NULL, #0x20  ; true
    // 0xb9a67c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb9a67c: stur            w0, [x3, #0x17]
    // 0xb9a680: d0 = 25.000000
    //     0xb9a680: fmov            d0, #25.00000000
    // 0xb9a684: StoreField: r3->field_1b = d0
    //     0xb9a684: stur            d0, [x3, #0x1b]
    // 0xb9a688: ldur            x0, [fp, #-8]
    // 0xb9a68c: StoreField: r3->field_33 = r0
    //     0xb9a68c: stur            w0, [x3, #0x33]
    // 0xb9a690: r1 = Null
    //     0xb9a690: mov             x1, NULL
    // 0xb9a694: r2 = 2
    //     0xb9a694: movz            x2, #0x2
    // 0xb9a698: r0 = AllocateArray()
    //     0xb9a698: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb9a69c: mov             x2, x0
    // 0xb9a6a0: ldur            x0, [fp, #-0x38]
    // 0xb9a6a4: stur            x2, [fp, #-8]
    // 0xb9a6a8: StoreField: r2->field_f = r0
    //     0xb9a6a8: stur            w0, [x2, #0xf]
    // 0xb9a6ac: r1 = <PopupMenuEntry<String>>
    //     0xb9a6ac: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xb9a6b0: ldr             x1, [x1, #0xa70]
    // 0xb9a6b4: r0 = AllocateGrowableArray()
    //     0xb9a6b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb9a6b8: mov             x1, x0
    // 0xb9a6bc: ldur            x0, [fp, #-8]
    // 0xb9a6c0: StoreField: r1->field_f = r0
    //     0xb9a6c0: stur            w0, [x1, #0xf]
    // 0xb9a6c4: r0 = 2
    //     0xb9a6c4: movz            x0, #0x2
    // 0xb9a6c8: StoreField: r1->field_b = r0
    //     0xb9a6c8: stur            w0, [x1, #0xb]
    // 0xb9a6cc: r16 = <String>
    //     0xb9a6cc: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb9a6d0: ldur            lr, [fp, #-0x10]
    // 0xb9a6d4: stp             lr, x16, [SP, #0x20]
    // 0xb9a6d8: ldur            x16, [fp, #-0x18]
    // 0xb9a6dc: stp             x16, x1, [SP, #0x10]
    // 0xb9a6e0: r16 = Instance_Color
    //     0xb9a6e0: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb9a6e4: ldur            lr, [fp, #-0x20]
    // 0xb9a6e8: stp             lr, x16, [SP]
    // 0xb9a6ec: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xb9a6ec: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xb9a6f0: ldr             x4, [x4, #0xa78]
    // 0xb9a6f4: r0 = showMenu()
    //     0xb9a6f4: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xb9a6f8: ldur            x2, [fp, #-0x28]
    // 0xb9a6fc: r1 = Function '<anonymous closure>':.
    //     0xb9a6fc: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f48] AnonymousClosure: (0xb9a7bc), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xb9a078)
    //     0xb9a700: ldr             x1, [x1, #0xf48]
    // 0xb9a704: stur            x0, [fp, #-8]
    // 0xb9a708: r0 = AllocateClosure()
    //     0xb9a708: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9a70c: r16 = <Null?>
    //     0xb9a70c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xb9a710: ldur            lr, [fp, #-8]
    // 0xb9a714: stp             lr, x16, [SP, #8]
    // 0xb9a718: str             x0, [SP]
    // 0xb9a71c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb9a71c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb9a720: r0 = then()
    //     0xb9a720: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb9a724: r0 = Null
    //     0xb9a724: mov             x0, NULL
    // 0xb9a728: LeaveFrame
    //     0xb9a728: mov             SP, fp
    //     0xb9a72c: ldp             fp, lr, [SP], #0x10
    // 0xb9a730: ret
    //     0xb9a730: ret             
    // 0xb9a734: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9a734: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9a738: b               #0xb9a0ac
    // 0xb9a73c: SaveReg d0
    //     0xb9a73c: str             q0, [SP, #-0x10]!
    // 0xb9a740: stp             x0, x1, [SP, #-0x10]!
    // 0xb9a744: r0 = AllocateDouble()
    //     0xb9a744: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb9a748: mov             x2, x0
    // 0xb9a74c: ldp             x0, x1, [SP], #0x10
    // 0xb9a750: RestoreReg d0
    //     0xb9a750: ldr             q0, [SP], #0x10
    // 0xb9a754: b               #0xb9a188
    // 0xb9a758: stp             q0, q1, [SP, #-0x20]!
    // 0xb9a75c: stp             x0, x1, [SP, #-0x10]!
    // 0xb9a760: r0 = AllocateDouble()
    //     0xb9a760: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb9a764: mov             x2, x0
    // 0xb9a768: ldp             x0, x1, [SP], #0x10
    // 0xb9a76c: ldp             q0, q1, [SP], #0x20
    // 0xb9a770: b               #0xb9a1dc
    // 0xb9a774: stp             q2, q3, [SP, #-0x20]!
    // 0xb9a778: stp             q0, q1, [SP, #-0x20]!
    // 0xb9a77c: stp             x0, x1, [SP, #-0x10]!
    // 0xb9a780: r0 = AllocateDouble()
    //     0xb9a780: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb9a784: mov             x2, x0
    // 0xb9a788: ldp             x0, x1, [SP], #0x10
    // 0xb9a78c: ldp             q0, q1, [SP], #0x20
    // 0xb9a790: ldp             q2, q3, [SP], #0x20
    // 0xb9a794: b               #0xb9a23c
    // 0xb9a798: stp             q3, q4, [SP, #-0x20]!
    // 0xb9a79c: stp             q0, q1, [SP, #-0x20]!
    // 0xb9a7a0: SaveReg r0
    //     0xb9a7a0: str             x0, [SP, #-8]!
    // 0xb9a7a4: r0 = AllocateDouble()
    //     0xb9a7a4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb9a7a8: mov             x1, x0
    // 0xb9a7ac: RestoreReg r0
    //     0xb9a7ac: ldr             x0, [SP], #8
    // 0xb9a7b0: ldp             q0, q1, [SP], #0x20
    // 0xb9a7b4: ldp             q3, q4, [SP], #0x20
    // 0xb9a7b8: b               #0xb9a294
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb9a7bc, size: 0x94
    // 0xb9a7bc: EnterFrame
    //     0xb9a7bc: stp             fp, lr, [SP, #-0x10]!
    //     0xb9a7c0: mov             fp, SP
    // 0xb9a7c4: AllocStack(0x20)
    //     0xb9a7c4: sub             SP, SP, #0x20
    // 0xb9a7c8: SetupParameters()
    //     0xb9a7c8: ldr             x0, [fp, #0x18]
    //     0xb9a7cc: ldur            w2, [x0, #0x17]
    //     0xb9a7d0: add             x2, x2, HEAP, lsl #32
    //     0xb9a7d4: stur            x2, [fp, #-8]
    // 0xb9a7d8: CheckStackOverflow
    //     0xb9a7d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9a7dc: cmp             SP, x16
    //     0xb9a7e0: b.ls            #0xb9a848
    // 0xb9a7e4: ldr             x0, [fp, #0x10]
    // 0xb9a7e8: r1 = LoadClassIdInstr(r0)
    //     0xb9a7e8: ldur            x1, [x0, #-1]
    //     0xb9a7ec: ubfx            x1, x1, #0xc, #0x14
    // 0xb9a7f0: r16 = "flag"
    //     0xb9a7f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb9a7f4: ldr             x16, [x16, #0xa68]
    // 0xb9a7f8: stp             x16, x0, [SP]
    // 0xb9a7fc: mov             x0, x1
    // 0xb9a800: mov             lr, x0
    // 0xb9a804: ldr             lr, [x21, lr, lsl #3]
    // 0xb9a808: blr             lr
    // 0xb9a80c: tbnz            w0, #4, #0xb9a838
    // 0xb9a810: ldur            x2, [fp, #-8]
    // 0xb9a814: LoadField: r0 = r2->field_f
    //     0xb9a814: ldur            w0, [x2, #0xf]
    // 0xb9a818: DecompressPointer r0
    //     0xb9a818: add             x0, x0, HEAP, lsl #32
    // 0xb9a81c: stur            x0, [fp, #-0x10]
    // 0xb9a820: r1 = Function '<anonymous closure>':.
    //     0xb9a820: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f50] AnonymousClosure: (0xaa97e4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xb9a824: ldr             x1, [x1, #0xf50]
    // 0xb9a828: r0 = AllocateClosure()
    //     0xb9a828: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9a82c: ldur            x1, [fp, #-0x10]
    // 0xb9a830: mov             x2, x0
    // 0xb9a834: r0 = setState()
    //     0xb9a834: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9a838: r0 = Null
    //     0xb9a838: mov             x0, NULL
    // 0xb9a83c: LeaveFrame
    //     0xb9a83c: mov             SP, fp
    //     0xb9a840: ldp             fp, lr, [SP], #0x10
    // 0xb9a844: ret
    //     0xb9a844: ret             
    // 0xb9a848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9a848: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9a84c: b               #0xb9a7e4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9a850, size: 0x60
    // 0xb9a850: EnterFrame
    //     0xb9a850: stp             fp, lr, [SP, #-0x10]!
    //     0xb9a854: mov             fp, SP
    // 0xb9a858: AllocStack(0x8)
    //     0xb9a858: sub             SP, SP, #8
    // 0xb9a85c: SetupParameters()
    //     0xb9a85c: ldr             x0, [fp, #0x10]
    //     0xb9a860: ldur            w2, [x0, #0x17]
    //     0xb9a864: add             x2, x2, HEAP, lsl #32
    // 0xb9a868: CheckStackOverflow
    //     0xb9a868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9a86c: cmp             SP, x16
    //     0xb9a870: b.ls            #0xb9a8a8
    // 0xb9a874: LoadField: r0 = r2->field_f
    //     0xb9a874: ldur            w0, [x2, #0xf]
    // 0xb9a878: DecompressPointer r0
    //     0xb9a878: add             x0, x0, HEAP, lsl #32
    // 0xb9a87c: stur            x0, [fp, #-8]
    // 0xb9a880: r1 = Function '<anonymous closure>':.
    //     0xb9a880: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f58] AnonymousClosure: (0xaa97e4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xb9a884: ldr             x1, [x1, #0xf58]
    // 0xb9a888: r0 = AllocateClosure()
    //     0xb9a888: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9a88c: ldur            x1, [fp, #-8]
    // 0xb9a890: mov             x2, x0
    // 0xb9a894: r0 = setState()
    //     0xb9a894: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9a898: r0 = Null
    //     0xb9a898: mov             x0, NULL
    // 0xb9a89c: LeaveFrame
    //     0xb9a89c: mov             SP, fp
    //     0xb9a8a0: ldp             fp, lr, [SP], #0x10
    // 0xb9a8a4: ret
    //     0xb9a8a4: ret             
    // 0xb9a8a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9a8a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9a8ac: b               #0xb9a874
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9a8b0, size: 0xc8
    // 0xb9a8b0: EnterFrame
    //     0xb9a8b0: stp             fp, lr, [SP, #-0x10]!
    //     0xb9a8b4: mov             fp, SP
    // 0xb9a8b8: ldr             x0, [fp, #0x10]
    // 0xb9a8bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb9a8bc: ldur            w1, [x0, #0x17]
    // 0xb9a8c0: DecompressPointer r1
    //     0xb9a8c0: add             x1, x1, HEAP, lsl #32
    // 0xb9a8c4: CheckStackOverflow
    //     0xb9a8c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9a8c8: cmp             SP, x16
    //     0xb9a8cc: b.ls            #0xb9a960
    // 0xb9a8d0: LoadField: r0 = r1->field_f
    //     0xb9a8d0: ldur            w0, [x1, #0xf]
    // 0xb9a8d4: DecompressPointer r0
    //     0xb9a8d4: add             x0, x0, HEAP, lsl #32
    // 0xb9a8d8: LoadField: r1 = r0->field_13
    //     0xb9a8d8: ldur            x1, [x0, #0x13]
    // 0xb9a8dc: LoadField: r2 = r0->field_b
    //     0xb9a8dc: ldur            w2, [x0, #0xb]
    // 0xb9a8e0: DecompressPointer r2
    //     0xb9a8e0: add             x2, x2, HEAP, lsl #32
    // 0xb9a8e4: cmp             w2, NULL
    // 0xb9a8e8: b.eq            #0xb9a968
    // 0xb9a8ec: LoadField: r3 = r2->field_b
    //     0xb9a8ec: ldur            w3, [x2, #0xb]
    // 0xb9a8f0: DecompressPointer r3
    //     0xb9a8f0: add             x3, x3, HEAP, lsl #32
    // 0xb9a8f4: cmp             w3, NULL
    // 0xb9a8f8: b.ne            #0xb9a904
    // 0xb9a8fc: r2 = Null
    //     0xb9a8fc: mov             x2, NULL
    // 0xb9a900: b               #0xb9a914
    // 0xb9a904: LoadField: r2 = r3->field_1b
    //     0xb9a904: ldur            w2, [x3, #0x1b]
    // 0xb9a908: DecompressPointer r2
    //     0xb9a908: add             x2, x2, HEAP, lsl #32
    // 0xb9a90c: LoadField: r3 = r2->field_b
    //     0xb9a90c: ldur            w3, [x2, #0xb]
    // 0xb9a910: mov             x2, x3
    // 0xb9a914: cmp             w2, NULL
    // 0xb9a918: b.ne            #0xb9a924
    // 0xb9a91c: r2 = 0
    //     0xb9a91c: movz            x2, #0
    // 0xb9a920: b               #0xb9a92c
    // 0xb9a924: r3 = LoadInt32Instr(r2)
    //     0xb9a924: sbfx            x3, x2, #1, #0x1f
    // 0xb9a928: mov             x2, x3
    // 0xb9a92c: sub             x3, x2, #1
    // 0xb9a930: cmp             x1, x3
    // 0xb9a934: b.ge            #0xb9a950
    // 0xb9a938: LoadField: r1 = r0->field_1b
    //     0xb9a938: ldur            w1, [x0, #0x1b]
    // 0xb9a93c: DecompressPointer r1
    //     0xb9a93c: add             x1, x1, HEAP, lsl #32
    // 0xb9a940: r16 = Sentinel
    //     0xb9a940: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb9a944: cmp             w1, w16
    // 0xb9a948: b.eq            #0xb9a96c
    // 0xb9a94c: r0 = nextPage()
    //     0xb9a94c: bl              #0xaa5ed0  ; [package:flutter/src/widgets/page_view.dart] PageController::nextPage
    // 0xb9a950: r0 = Null
    //     0xb9a950: mov             x0, NULL
    // 0xb9a954: LeaveFrame
    //     0xb9a954: mov             SP, fp
    //     0xb9a958: ldp             fp, lr, [SP], #0x10
    // 0xb9a95c: ret
    //     0xb9a95c: ret             
    // 0xb9a960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9a960: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9a964: b               #0xb9a8d0
    // 0xb9a968: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9a968: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9a96c: r9 = _pageController
    //     0xb9a96c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54f38] Field <_RatingReviewOnTapImageState@1634380928._pageController@1634380928>: late (offset: 0x1c)
    //     0xb9a970: ldr             x9, [x9, #0xf38]
    // 0xb9a974: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb9a974: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb9a978, size: 0x70
    // 0xb9a978: EnterFrame
    //     0xb9a978: stp             fp, lr, [SP, #-0x10]!
    //     0xb9a97c: mov             fp, SP
    // 0xb9a980: ldr             x0, [fp, #0x10]
    // 0xb9a984: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb9a984: ldur            w1, [x0, #0x17]
    // 0xb9a988: DecompressPointer r1
    //     0xb9a988: add             x1, x1, HEAP, lsl #32
    // 0xb9a98c: CheckStackOverflow
    //     0xb9a98c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9a990: cmp             SP, x16
    //     0xb9a994: b.ls            #0xb9a9d4
    // 0xb9a998: LoadField: r0 = r1->field_f
    //     0xb9a998: ldur            w0, [x1, #0xf]
    // 0xb9a99c: DecompressPointer r0
    //     0xb9a99c: add             x0, x0, HEAP, lsl #32
    // 0xb9a9a0: LoadField: r1 = r0->field_13
    //     0xb9a9a0: ldur            x1, [x0, #0x13]
    // 0xb9a9a4: cmp             x1, #0
    // 0xb9a9a8: b.le            #0xb9a9c4
    // 0xb9a9ac: LoadField: r1 = r0->field_1b
    //     0xb9a9ac: ldur            w1, [x0, #0x1b]
    // 0xb9a9b0: DecompressPointer r1
    //     0xb9a9b0: add             x1, x1, HEAP, lsl #32
    // 0xb9a9b4: r16 = Sentinel
    //     0xb9a9b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb9a9b8: cmp             w1, w16
    // 0xb9a9bc: b.eq            #0xb9a9dc
    // 0xb9a9c0: r0 = previousPage()
    //     0xb9a9c0: bl              #0xaa6010  ; [package:flutter/src/widgets/page_view.dart] PageController::previousPage
    // 0xb9a9c4: r0 = Null
    //     0xb9a9c4: mov             x0, NULL
    // 0xb9a9c8: LeaveFrame
    //     0xb9a9c8: mov             SP, fp
    //     0xb9a9cc: ldp             fp, lr, [SP], #0x10
    // 0xb9a9d0: ret
    //     0xb9a9d0: ret             
    // 0xb9a9d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9a9d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9a9d8: b               #0xb9a998
    // 0xb9a9dc: r9 = _pageController
    //     0xb9a9dc: add             x9, PP, #0x54, lsl #12  ; [pp+0x54f38] Field <_RatingReviewOnTapImageState@1634380928._pageController@1634380928>: late (offset: 0x1c)
    //     0xb9a9e0: ldr             x9, [x9, #0xf38]
    // 0xb9a9e4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb9a9e4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb9a9e8, size: 0x2d4
    // 0xb9a9e8: EnterFrame
    //     0xb9a9e8: stp             fp, lr, [SP, #-0x10]!
    //     0xb9a9ec: mov             fp, SP
    // 0xb9a9f0: AllocStack(0x50)
    //     0xb9a9f0: sub             SP, SP, #0x50
    // 0xb9a9f4: SetupParameters()
    //     0xb9a9f4: ldr             x0, [fp, #0x20]
    //     0xb9a9f8: ldur            w2, [x0, #0x17]
    //     0xb9a9fc: add             x2, x2, HEAP, lsl #32
    //     0xb9aa00: stur            x2, [fp, #-8]
    // 0xb9aa04: CheckStackOverflow
    //     0xb9aa04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9aa08: cmp             SP, x16
    //     0xb9aa0c: b.ls            #0xb9ac9c
    // 0xb9aa10: LoadField: r0 = r2->field_f
    //     0xb9aa10: ldur            w0, [x2, #0xf]
    // 0xb9aa14: DecompressPointer r0
    //     0xb9aa14: add             x0, x0, HEAP, lsl #32
    // 0xb9aa18: LoadField: r1 = r0->field_b
    //     0xb9aa18: ldur            w1, [x0, #0xb]
    // 0xb9aa1c: DecompressPointer r1
    //     0xb9aa1c: add             x1, x1, HEAP, lsl #32
    // 0xb9aa20: cmp             w1, NULL
    // 0xb9aa24: b.eq            #0xb9aca4
    // 0xb9aa28: LoadField: r0 = r1->field_b
    //     0xb9aa28: ldur            w0, [x1, #0xb]
    // 0xb9aa2c: DecompressPointer r0
    //     0xb9aa2c: add             x0, x0, HEAP, lsl #32
    // 0xb9aa30: cmp             w0, NULL
    // 0xb9aa34: b.ne            #0xb9aa44
    // 0xb9aa38: ldr             x3, [fp, #0x10]
    // 0xb9aa3c: r0 = Null
    //     0xb9aa3c: mov             x0, NULL
    // 0xb9aa40: b               #0xb9aa90
    // 0xb9aa44: ldr             x3, [fp, #0x10]
    // 0xb9aa48: LoadField: r4 = r0->field_1b
    //     0xb9aa48: ldur            w4, [x0, #0x1b]
    // 0xb9aa4c: DecompressPointer r4
    //     0xb9aa4c: add             x4, x4, HEAP, lsl #32
    // 0xb9aa50: LoadField: r0 = r4->field_b
    //     0xb9aa50: ldur            w0, [x4, #0xb]
    // 0xb9aa54: r5 = LoadInt32Instr(r3)
    //     0xb9aa54: sbfx            x5, x3, #1, #0x1f
    //     0xb9aa58: tbz             w3, #0, #0xb9aa60
    //     0xb9aa5c: ldur            x5, [x3, #7]
    // 0xb9aa60: r1 = LoadInt32Instr(r0)
    //     0xb9aa60: sbfx            x1, x0, #1, #0x1f
    // 0xb9aa64: mov             x0, x1
    // 0xb9aa68: mov             x1, x5
    // 0xb9aa6c: cmp             x1, x0
    // 0xb9aa70: b.hs            #0xb9aca8
    // 0xb9aa74: LoadField: r0 = r4->field_f
    //     0xb9aa74: ldur            w0, [x4, #0xf]
    // 0xb9aa78: DecompressPointer r0
    //     0xb9aa78: add             x0, x0, HEAP, lsl #32
    // 0xb9aa7c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb9aa7c: add             x16, x0, x5, lsl #2
    //     0xb9aa80: ldur            w1, [x16, #0xf]
    // 0xb9aa84: DecompressPointer r1
    //     0xb9aa84: add             x1, x1, HEAP, lsl #32
    // 0xb9aa88: LoadField: r0 = r1->field_f
    //     0xb9aa88: ldur            w0, [x1, #0xf]
    // 0xb9aa8c: DecompressPointer r0
    //     0xb9aa8c: add             x0, x0, HEAP, lsl #32
    // 0xb9aa90: r1 = LoadClassIdInstr(r0)
    //     0xb9aa90: ldur            x1, [x0, #-1]
    //     0xb9aa94: ubfx            x1, x1, #0xc, #0x14
    // 0xb9aa98: r16 = "image"
    //     0xb9aa98: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb9aa9c: stp             x16, x0, [SP]
    // 0xb9aaa0: mov             x0, x1
    // 0xb9aaa4: mov             lr, x0
    // 0xb9aaa8: ldr             lr, [x21, lr, lsl #3]
    // 0xb9aaac: blr             lr
    // 0xb9aab0: tbnz            w0, #4, #0xb9abec
    // 0xb9aab4: ldur            x0, [fp, #-8]
    // 0xb9aab8: LoadField: r1 = r0->field_f
    //     0xb9aab8: ldur            w1, [x0, #0xf]
    // 0xb9aabc: DecompressPointer r1
    //     0xb9aabc: add             x1, x1, HEAP, lsl #32
    // 0xb9aac0: LoadField: r0 = r1->field_23
    //     0xb9aac0: ldur            w0, [x1, #0x23]
    // 0xb9aac4: DecompressPointer r0
    //     0xb9aac4: add             x0, x0, HEAP, lsl #32
    // 0xb9aac8: tbnz            w0, #4, #0xb9aad8
    // 0xb9aacc: r3 = Instance_BoxFit
    //     0xb9aacc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb9aad0: ldr             x3, [x3, #0x118]
    // 0xb9aad4: b               #0xb9aae0
    // 0xb9aad8: r3 = Instance_BoxFit
    //     0xb9aad8: add             x3, PP, #0x51, lsl #12  ; [pp+0x51f38] Obj!BoxFit@d738e1
    //     0xb9aadc: ldr             x3, [x3, #0xf38]
    // 0xb9aae0: stur            x3, [fp, #-0x18]
    // 0xb9aae4: LoadField: r0 = r1->field_b
    //     0xb9aae4: ldur            w0, [x1, #0xb]
    // 0xb9aae8: DecompressPointer r0
    //     0xb9aae8: add             x0, x0, HEAP, lsl #32
    // 0xb9aaec: cmp             w0, NULL
    // 0xb9aaf0: b.eq            #0xb9acac
    // 0xb9aaf4: LoadField: r1 = r0->field_b
    //     0xb9aaf4: ldur            w1, [x0, #0xb]
    // 0xb9aaf8: DecompressPointer r1
    //     0xb9aaf8: add             x1, x1, HEAP, lsl #32
    // 0xb9aafc: cmp             w1, NULL
    // 0xb9ab00: b.ne            #0xb9ab0c
    // 0xb9ab04: r0 = Null
    //     0xb9ab04: mov             x0, NULL
    // 0xb9ab08: b               #0xb9ab58
    // 0xb9ab0c: ldr             x2, [fp, #0x10]
    // 0xb9ab10: LoadField: r4 = r1->field_1b
    //     0xb9ab10: ldur            w4, [x1, #0x1b]
    // 0xb9ab14: DecompressPointer r4
    //     0xb9ab14: add             x4, x4, HEAP, lsl #32
    // 0xb9ab18: LoadField: r0 = r4->field_b
    //     0xb9ab18: ldur            w0, [x4, #0xb]
    // 0xb9ab1c: r5 = LoadInt32Instr(r2)
    //     0xb9ab1c: sbfx            x5, x2, #1, #0x1f
    //     0xb9ab20: tbz             w2, #0, #0xb9ab28
    //     0xb9ab24: ldur            x5, [x2, #7]
    // 0xb9ab28: r1 = LoadInt32Instr(r0)
    //     0xb9ab28: sbfx            x1, x0, #1, #0x1f
    // 0xb9ab2c: mov             x0, x1
    // 0xb9ab30: mov             x1, x5
    // 0xb9ab34: cmp             x1, x0
    // 0xb9ab38: b.hs            #0xb9acb0
    // 0xb9ab3c: LoadField: r0 = r4->field_f
    //     0xb9ab3c: ldur            w0, [x4, #0xf]
    // 0xb9ab40: DecompressPointer r0
    //     0xb9ab40: add             x0, x0, HEAP, lsl #32
    // 0xb9ab44: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb9ab44: add             x16, x0, x5, lsl #2
    //     0xb9ab48: ldur            w1, [x16, #0xf]
    // 0xb9ab4c: DecompressPointer r1
    //     0xb9ab4c: add             x1, x1, HEAP, lsl #32
    // 0xb9ab50: LoadField: r0 = r1->field_13
    //     0xb9ab50: ldur            w0, [x1, #0x13]
    // 0xb9ab54: DecompressPointer r0
    //     0xb9ab54: add             x0, x0, HEAP, lsl #32
    // 0xb9ab58: cmp             w0, NULL
    // 0xb9ab5c: b.ne            #0xb9ab64
    // 0xb9ab60: r0 = ""
    //     0xb9ab60: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9ab64: stur            x0, [fp, #-0x10]
    // 0xb9ab68: r1 = Function '<anonymous closure>':.
    //     0xb9ab68: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f60] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9ab6c: ldr             x1, [x1, #0xf60]
    // 0xb9ab70: r2 = Null
    //     0xb9ab70: mov             x2, NULL
    // 0xb9ab74: r0 = AllocateClosure()
    //     0xb9ab74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9ab78: r1 = Function '<anonymous closure>':.
    //     0xb9ab78: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f68] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb9ab7c: ldr             x1, [x1, #0xf68]
    // 0xb9ab80: r2 = Null
    //     0xb9ab80: mov             x2, NULL
    // 0xb9ab84: stur            x0, [fp, #-0x20]
    // 0xb9ab88: r0 = AllocateClosure()
    //     0xb9ab88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9ab8c: stur            x0, [fp, #-0x28]
    // 0xb9ab90: r0 = CachedNetworkImage()
    //     0xb9ab90: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb9ab94: stur            x0, [fp, #-0x30]
    // 0xb9ab98: ldur            x16, [fp, #-0x18]
    // 0xb9ab9c: r30 = inf
    //     0xb9ab9c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb9aba0: ldr             lr, [lr, #0x9f8]
    // 0xb9aba4: stp             lr, x16, [SP, #0x10]
    // 0xb9aba8: ldur            x16, [fp, #-0x20]
    // 0xb9abac: ldur            lr, [fp, #-0x28]
    // 0xb9abb0: stp             lr, x16, [SP]
    // 0xb9abb4: mov             x1, x0
    // 0xb9abb8: ldur            x2, [fp, #-0x10]
    // 0xb9abbc: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb9abbc: add             x4, PP, #0x54, lsl #12  ; [pp+0x54f70] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb9abc0: ldr             x4, [x4, #0xf70]
    // 0xb9abc4: r0 = CachedNetworkImage()
    //     0xb9abc4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb9abc8: r0 = Center()
    //     0xb9abc8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb9abcc: mov             x1, x0
    // 0xb9abd0: r0 = Instance_Alignment
    //     0xb9abd0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb9abd4: ldr             x0, [x0, #0xb10]
    // 0xb9abd8: StoreField: r1->field_f = r0
    //     0xb9abd8: stur            w0, [x1, #0xf]
    // 0xb9abdc: ldur            x0, [fp, #-0x30]
    // 0xb9abe0: StoreField: r1->field_b = r0
    //     0xb9abe0: stur            w0, [x1, #0xb]
    // 0xb9abe4: mov             x0, x1
    // 0xb9abe8: b               #0xb9ac90
    // 0xb9abec: ldr             x2, [fp, #0x10]
    // 0xb9abf0: ldur            x0, [fp, #-8]
    // 0xb9abf4: LoadField: r1 = r0->field_f
    //     0xb9abf4: ldur            w1, [x0, #0xf]
    // 0xb9abf8: DecompressPointer r1
    //     0xb9abf8: add             x1, x1, HEAP, lsl #32
    // 0xb9abfc: LoadField: r0 = r1->field_b
    //     0xb9abfc: ldur            w0, [x1, #0xb]
    // 0xb9ac00: DecompressPointer r0
    //     0xb9ac00: add             x0, x0, HEAP, lsl #32
    // 0xb9ac04: cmp             w0, NULL
    // 0xb9ac08: b.eq            #0xb9acb4
    // 0xb9ac0c: LoadField: r1 = r0->field_b
    //     0xb9ac0c: ldur            w1, [x0, #0xb]
    // 0xb9ac10: DecompressPointer r1
    //     0xb9ac10: add             x1, x1, HEAP, lsl #32
    // 0xb9ac14: cmp             w1, NULL
    // 0xb9ac18: b.ne            #0xb9ac24
    // 0xb9ac1c: r0 = Null
    //     0xb9ac1c: mov             x0, NULL
    // 0xb9ac20: b               #0xb9ac6c
    // 0xb9ac24: LoadField: r3 = r1->field_1b
    //     0xb9ac24: ldur            w3, [x1, #0x1b]
    // 0xb9ac28: DecompressPointer r3
    //     0xb9ac28: add             x3, x3, HEAP, lsl #32
    // 0xb9ac2c: LoadField: r0 = r3->field_b
    //     0xb9ac2c: ldur            w0, [x3, #0xb]
    // 0xb9ac30: r4 = LoadInt32Instr(r2)
    //     0xb9ac30: sbfx            x4, x2, #1, #0x1f
    //     0xb9ac34: tbz             w2, #0, #0xb9ac3c
    //     0xb9ac38: ldur            x4, [x2, #7]
    // 0xb9ac3c: r1 = LoadInt32Instr(r0)
    //     0xb9ac3c: sbfx            x1, x0, #1, #0x1f
    // 0xb9ac40: mov             x0, x1
    // 0xb9ac44: mov             x1, x4
    // 0xb9ac48: cmp             x1, x0
    // 0xb9ac4c: b.hs            #0xb9acb8
    // 0xb9ac50: LoadField: r0 = r3->field_f
    //     0xb9ac50: ldur            w0, [x3, #0xf]
    // 0xb9ac54: DecompressPointer r0
    //     0xb9ac54: add             x0, x0, HEAP, lsl #32
    // 0xb9ac58: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb9ac58: add             x16, x0, x4, lsl #2
    //     0xb9ac5c: ldur            w1, [x16, #0xf]
    // 0xb9ac60: DecompressPointer r1
    //     0xb9ac60: add             x1, x1, HEAP, lsl #32
    // 0xb9ac64: LoadField: r0 = r1->field_13
    //     0xb9ac64: ldur            w0, [x1, #0x13]
    // 0xb9ac68: DecompressPointer r0
    //     0xb9ac68: add             x0, x0, HEAP, lsl #32
    // 0xb9ac6c: cmp             w0, NULL
    // 0xb9ac70: b.ne            #0xb9ac78
    // 0xb9ac74: r0 = ""
    //     0xb9ac74: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb9ac78: stur            x0, [fp, #-8]
    // 0xb9ac7c: r0 = VideoPlayerWidget()
    //     0xb9ac7c: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb9ac80: ldur            x1, [fp, #-8]
    // 0xb9ac84: StoreField: r0->field_b = r1
    //     0xb9ac84: stur            w1, [x0, #0xb]
    // 0xb9ac88: r1 = true
    //     0xb9ac88: add             x1, NULL, #0x20  ; true
    // 0xb9ac8c: StoreField: r0->field_f = r1
    //     0xb9ac8c: stur            w1, [x0, #0xf]
    // 0xb9ac90: LeaveFrame
    //     0xb9ac90: mov             SP, fp
    //     0xb9ac94: ldp             fp, lr, [SP], #0x10
    // 0xb9ac98: ret
    //     0xb9ac98: ret             
    // 0xb9ac9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9ac9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9aca0: b               #0xb9aa10
    // 0xb9aca4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9aca4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9aca8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb9aca8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb9acac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9acac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9acb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb9acb0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb9acb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb9acb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb9acb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb9acb8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void _onPageChanged(dynamic, int) {
    // ** addr: 0xb9acbc, size: 0x3c
    // 0xb9acbc: EnterFrame
    //     0xb9acbc: stp             fp, lr, [SP, #-0x10]!
    //     0xb9acc0: mov             fp, SP
    // 0xb9acc4: ldr             x0, [fp, #0x18]
    // 0xb9acc8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb9acc8: ldur            w1, [x0, #0x17]
    // 0xb9accc: DecompressPointer r1
    //     0xb9accc: add             x1, x1, HEAP, lsl #32
    // 0xb9acd0: CheckStackOverflow
    //     0xb9acd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9acd4: cmp             SP, x16
    //     0xb9acd8: b.ls            #0xb9acf0
    // 0xb9acdc: ldr             x2, [fp, #0x10]
    // 0xb9ace0: r0 = _onPageChanged()
    //     0xb9ace0: bl              #0xb9acf8  ; [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged
    // 0xb9ace4: LeaveFrame
    //     0xb9ace4: mov             SP, fp
    //     0xb9ace8: ldp             fp, lr, [SP], #0x10
    // 0xb9acec: ret
    //     0xb9acec: ret             
    // 0xb9acf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9acf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9acf4: b               #0xb9acdc
  }
  _ _onPageChanged(/* No info */) {
    // ** addr: 0xb9acf8, size: 0x70
    // 0xb9acf8: EnterFrame
    //     0xb9acf8: stp             fp, lr, [SP, #-0x10]!
    //     0xb9acfc: mov             fp, SP
    // 0xb9ad00: AllocStack(0x10)
    //     0xb9ad00: sub             SP, SP, #0x10
    // 0xb9ad04: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb9ad04: stur            x1, [fp, #-8]
    //     0xb9ad08: stur            x2, [fp, #-0x10]
    // 0xb9ad0c: CheckStackOverflow
    //     0xb9ad0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb9ad10: cmp             SP, x16
    //     0xb9ad14: b.ls            #0xb9ad60
    // 0xb9ad18: r1 = 2
    //     0xb9ad18: movz            x1, #0x2
    // 0xb9ad1c: r0 = AllocateContext()
    //     0xb9ad1c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb9ad20: mov             x1, x0
    // 0xb9ad24: ldur            x0, [fp, #-8]
    // 0xb9ad28: StoreField: r1->field_f = r0
    //     0xb9ad28: stur            w0, [x1, #0xf]
    // 0xb9ad2c: ldur            x2, [fp, #-0x10]
    // 0xb9ad30: StoreField: r1->field_13 = r2
    //     0xb9ad30: stur            w2, [x1, #0x13]
    // 0xb9ad34: mov             x2, x1
    // 0xb9ad38: r1 = Function '<anonymous closure>':.
    //     0xb9ad38: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f78] AnonymousClosure: (0xaaa7d0), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged (0xaaa838)
    //     0xb9ad3c: ldr             x1, [x1, #0xf78]
    // 0xb9ad40: r0 = AllocateClosure()
    //     0xb9ad40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb9ad44: ldur            x1, [fp, #-8]
    // 0xb9ad48: mov             x2, x0
    // 0xb9ad4c: r0 = setState()
    //     0xb9ad4c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb9ad50: r0 = Null
    //     0xb9ad50: mov             x0, NULL
    // 0xb9ad54: LeaveFrame
    //     0xb9ad54: mov             SP, fp
    //     0xb9ad58: ldp             fp, lr, [SP], #0x10
    // 0xb9ad5c: ret
    //     0xb9ad5c: ret             
    // 0xb9ad60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb9ad60: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb9ad64: b               #0xb9ad18
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87f18, size: 0x8c
    // 0xc87f18: EnterFrame
    //     0xc87f18: stp             fp, lr, [SP, #-0x10]!
    //     0xc87f1c: mov             fp, SP
    // 0xc87f20: AllocStack(0x10)
    //     0xc87f20: sub             SP, SP, #0x10
    // 0xc87f24: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r2, fp-0x8 */)
    //     0xc87f24: mov             x2, x1
    //     0xc87f28: stur            x1, [fp, #-8]
    // 0xc87f2c: CheckStackOverflow
    //     0xc87f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87f30: cmp             SP, x16
    //     0xc87f34: b.ls            #0xc87f90
    // 0xc87f38: LoadField: r1 = r2->field_1b
    //     0xc87f38: ldur            w1, [x2, #0x1b]
    // 0xc87f3c: DecompressPointer r1
    //     0xc87f3c: add             x1, x1, HEAP, lsl #32
    // 0xc87f40: r16 = Sentinel
    //     0xc87f40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87f44: cmp             w1, w16
    // 0xc87f48: b.eq            #0xc87f98
    // 0xc87f4c: r0 = dispose()
    //     0xc87f4c: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87f50: ldur            x2, [fp, #-8]
    // 0xc87f54: LoadField: r0 = r2->field_1f
    //     0xc87f54: ldur            w0, [x2, #0x1f]
    // 0xc87f58: DecompressPointer r0
    //     0xc87f58: add             x0, x0, HEAP, lsl #32
    // 0xc87f5c: stur            x0, [fp, #-0x10]
    // 0xc87f60: r1 = Function '_onCollapseChanged@1634380928':.
    //     0xc87f60: add             x1, PP, #0x54, lsl #12  ; [pp+0x54f80] AnonymousClosure: (0x944f6c), in [package:customer_app/app/presentation/views/glass/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x944fa4)
    //     0xc87f64: ldr             x1, [x1, #0xf80]
    // 0xc87f68: r0 = AllocateClosure()
    //     0xc87f68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc87f6c: ldur            x1, [fp, #-0x10]
    // 0xc87f70: mov             x2, x0
    // 0xc87f74: r0 = removeListener()
    //     0xc87f74: bl              #0x7b91b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc87f78: ldur            x1, [fp, #-0x10]
    // 0xc87f7c: r0 = dispose()
    //     0xc87f7c: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc87f80: r0 = Null
    //     0xc87f80: mov             x0, NULL
    // 0xc87f84: LeaveFrame
    //     0xc87f84: mov             SP, fp
    //     0xc87f88: ldp             fp, lr, [SP], #0x10
    // 0xc87f8c: ret
    //     0xc87f8c: ret             
    // 0xc87f90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87f90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87f94: b               #0xc87f38
    // 0xc87f98: r9 = _pageController
    //     0xc87f98: add             x9, PP, #0x54, lsl #12  ; [pp+0x54f38] Field <_RatingReviewOnTapImageState@1634380928._pageController@1634380928>: late (offset: 0x1c)
    //     0xc87f9c: ldr             x9, [x9, #0xf38]
    // 0xc87fa0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87fa0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4041, size: 0x20, field offset: 0xc
//   const constructor, 
class RatingReviewOnTapImage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fcd0, size: 0x48
    // 0xc7fcd0: EnterFrame
    //     0xc7fcd0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fcd4: mov             fp, SP
    // 0xc7fcd8: AllocStack(0x8)
    //     0xc7fcd8: sub             SP, SP, #8
    // 0xc7fcdc: CheckStackOverflow
    //     0xc7fcdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7fce0: cmp             SP, x16
    //     0xc7fce4: b.ls            #0xc7fd10
    // 0xc7fce8: r1 = <RatingReviewOnTapImage>
    //     0xc7fce8: add             x1, PP, #0x48, lsl #12  ; [pp+0x48708] TypeArguments: <RatingReviewOnTapImage>
    //     0xc7fcec: ldr             x1, [x1, #0x708]
    // 0xc7fcf0: r0 = _RatingReviewOnTapImageState()
    //     0xc7fcf0: bl              #0xc7fd18  ; Allocate_RatingReviewOnTapImageStateStub -> _RatingReviewOnTapImageState (size=0x30)
    // 0xc7fcf4: mov             x1, x0
    // 0xc7fcf8: stur            x0, [fp, #-8]
    // 0xc7fcfc: r0 = _RatingReviewOnTapImageState()
    //     0xc7fcfc: bl              #0xc7cb88  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_RatingReviewOnTapImageState
    // 0xc7fd00: ldur            x0, [fp, #-8]
    // 0xc7fd04: LeaveFrame
    //     0xc7fd04: mov             SP, fp
    //     0xc7fd08: ldp             fp, lr, [SP], #0x10
    // 0xc7fd0c: ret
    //     0xc7fd0c: ret             
    // 0xc7fd10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7fd10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7fd14: b               #0xc7fce8
  }
}
