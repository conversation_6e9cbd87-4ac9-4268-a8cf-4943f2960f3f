// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/banner_cross_link.dart

// class id: 1049398, size: 0x8
class :: {
}

// class id: 3341, size: 0x14, field offset: 0x14
class _CarouselItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb5d2cc, size: 0x1b0
    // 0xb5d2cc: EnterFrame
    //     0xb5d2cc: stp             fp, lr, [SP, #-0x10]!
    //     0xb5d2d0: mov             fp, SP
    // 0xb5d2d4: AllocStack(0x50)
    //     0xb5d2d4: sub             SP, SP, #0x50
    // 0xb5d2d8: SetupParameters(_CarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb5d2d8: mov             x0, x1
    //     0xb5d2dc: stur            x1, [fp, #-8]
    //     0xb5d2e0: mov             x1, x2
    //     0xb5d2e4: stur            x2, [fp, #-0x10]
    // 0xb5d2e8: CheckStackOverflow
    //     0xb5d2e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5d2ec: cmp             SP, x16
    //     0xb5d2f0: b.ls            #0xb5d46c
    // 0xb5d2f4: r1 = 1
    //     0xb5d2f4: movz            x1, #0x1
    // 0xb5d2f8: r0 = AllocateContext()
    //     0xb5d2f8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5d2fc: mov             x3, x0
    // 0xb5d300: ldur            x0, [fp, #-8]
    // 0xb5d304: stur            x3, [fp, #-0x18]
    // 0xb5d308: StoreField: r3->field_f = r0
    //     0xb5d308: stur            w0, [x3, #0xf]
    // 0xb5d30c: LoadField: r1 = r0->field_b
    //     0xb5d30c: ldur            w1, [x0, #0xb]
    // 0xb5d310: DecompressPointer r1
    //     0xb5d310: add             x1, x1, HEAP, lsl #32
    // 0xb5d314: cmp             w1, NULL
    // 0xb5d318: b.eq            #0xb5d474
    // 0xb5d31c: LoadField: r2 = r1->field_b
    //     0xb5d31c: ldur            w2, [x1, #0xb]
    // 0xb5d320: DecompressPointer r2
    //     0xb5d320: add             x2, x2, HEAP, lsl #32
    // 0xb5d324: cmp             w2, NULL
    // 0xb5d328: b.ne            #0xb5d340
    // 0xb5d32c: r1 = <Entity>
    //     0xb5d32c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xb5d330: ldr             x1, [x1, #0xb68]
    // 0xb5d334: r2 = 0
    //     0xb5d334: movz            x2, #0
    // 0xb5d338: r0 = _GrowableList()
    //     0xb5d338: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb5d33c: mov             x2, x0
    // 0xb5d340: ldur            x0, [fp, #-8]
    // 0xb5d344: stur            x2, [fp, #-0x48]
    // 0xb5d348: LoadField: r1 = r0->field_b
    //     0xb5d348: ldur            w1, [x0, #0xb]
    // 0xb5d34c: DecompressPointer r1
    //     0xb5d34c: add             x1, x1, HEAP, lsl #32
    // 0xb5d350: cmp             w1, NULL
    // 0xb5d354: b.eq            #0xb5d478
    // 0xb5d358: LoadField: r0 = r1->field_1b
    //     0xb5d358: ldur            w0, [x1, #0x1b]
    // 0xb5d35c: DecompressPointer r0
    //     0xb5d35c: add             x0, x0, HEAP, lsl #32
    // 0xb5d360: stur            x0, [fp, #-0x40]
    // 0xb5d364: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb5d364: ldur            w3, [x1, #0x17]
    // 0xb5d368: DecompressPointer r3
    //     0xb5d368: add             x3, x3, HEAP, lsl #32
    // 0xb5d36c: stur            x3, [fp, #-0x38]
    // 0xb5d370: LoadField: r4 = r1->field_13
    //     0xb5d370: ldur            w4, [x1, #0x13]
    // 0xb5d374: DecompressPointer r4
    //     0xb5d374: add             x4, x4, HEAP, lsl #32
    // 0xb5d378: stur            x4, [fp, #-0x30]
    // 0xb5d37c: LoadField: r5 = r1->field_f
    //     0xb5d37c: ldur            w5, [x1, #0xf]
    // 0xb5d380: DecompressPointer r5
    //     0xb5d380: add             x5, x5, HEAP, lsl #32
    // 0xb5d384: stur            x5, [fp, #-0x28]
    // 0xb5d388: LoadField: r6 = r1->field_27
    //     0xb5d388: ldur            w6, [x1, #0x27]
    // 0xb5d38c: DecompressPointer r6
    //     0xb5d38c: add             x6, x6, HEAP, lsl #32
    // 0xb5d390: stur            x6, [fp, #-0x20]
    // 0xb5d394: LoadField: r7 = r1->field_2b
    //     0xb5d394: ldur            w7, [x1, #0x2b]
    // 0xb5d398: DecompressPointer r7
    //     0xb5d398: add             x7, x7, HEAP, lsl #32
    // 0xb5d39c: ldur            x1, [fp, #-0x10]
    // 0xb5d3a0: stur            x7, [fp, #-8]
    // 0xb5d3a4: r0 = of()
    //     0xb5d3a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5d3a8: LoadField: r1 = r0->field_5b
    //     0xb5d3a8: ldur            w1, [x0, #0x5b]
    // 0xb5d3ac: DecompressPointer r1
    //     0xb5d3ac: add             x1, x1, HEAP, lsl #32
    // 0xb5d3b0: stur            x1, [fp, #-0x10]
    // 0xb5d3b4: r0 = BannerCrossWidget()
    //     0xb5d3b4: bl              #0xa419a0  ; AllocateBannerCrossWidgetStub -> BannerCrossWidget (size=0x4c)
    // 0xb5d3b8: d0 = 200.000000
    //     0xb5d3b8: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0xb5d3bc: ldr             d0, [x17, #0x360]
    // 0xb5d3c0: stur            x0, [fp, #-0x50]
    // 0xb5d3c4: StoreField: r0->field_b = d0
    //     0xb5d3c4: stur            d0, [x0, #0xb]
    // 0xb5d3c8: r1 = true
    //     0xb5d3c8: add             x1, NULL, #0x20  ; true
    // 0xb5d3cc: StoreField: r0->field_13 = r1
    //     0xb5d3cc: stur            w1, [x0, #0x13]
    // 0xb5d3d0: r1 = Instance_Duration
    //     0xb5d3d0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd8] Obj!Duration@d77741
    //     0xb5d3d4: ldr             x1, [x1, #0xbd8]
    // 0xb5d3d8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5d3d8: stur            w1, [x0, #0x17]
    // 0xb5d3dc: ldur            x1, [fp, #-0x10]
    // 0xb5d3e0: StoreField: r0->field_1b = r1
    //     0xb5d3e0: stur            w1, [x0, #0x1b]
    // 0xb5d3e4: r1 = Instance_Color
    //     0xb5d3e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb5d3e8: ldr             x1, [x1, #0x90]
    // 0xb5d3ec: StoreField: r0->field_1f = r1
    //     0xb5d3ec: stur            w1, [x0, #0x1f]
    // 0xb5d3f0: ldur            x1, [fp, #-0x48]
    // 0xb5d3f4: StoreField: r0->field_27 = r1
    //     0xb5d3f4: stur            w1, [x0, #0x27]
    // 0xb5d3f8: ldur            x2, [fp, #-0x18]
    // 0xb5d3fc: r1 = Function '<anonymous closure>':.
    //     0xb5d3fc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f28] AnonymousClosure: (0xb5deac), in [package:customer_app/app/presentation/views/glass/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::build (0xb5d2cc)
    //     0xb5d400: ldr             x1, [x1, #0xf28]
    // 0xb5d404: r0 = AllocateClosure()
    //     0xb5d404: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5d408: mov             x1, x0
    // 0xb5d40c: ldur            x0, [fp, #-0x50]
    // 0xb5d410: StoreField: r0->field_2b = r1
    //     0xb5d410: stur            w1, [x0, #0x2b]
    // 0xb5d414: ldur            x1, [fp, #-0x40]
    // 0xb5d418: StoreField: r0->field_2f = r1
    //     0xb5d418: stur            w1, [x0, #0x2f]
    // 0xb5d41c: ldur            x1, [fp, #-0x38]
    // 0xb5d420: StoreField: r0->field_33 = r1
    //     0xb5d420: stur            w1, [x0, #0x33]
    // 0xb5d424: ldur            x1, [fp, #-8]
    // 0xb5d428: StoreField: r0->field_37 = r1
    //     0xb5d428: stur            w1, [x0, #0x37]
    // 0xb5d42c: ldur            x1, [fp, #-0x30]
    // 0xb5d430: StoreField: r0->field_3b = r1
    //     0xb5d430: stur            w1, [x0, #0x3b]
    // 0xb5d434: ldur            x1, [fp, #-0x28]
    // 0xb5d438: StoreField: r0->field_3f = r1
    //     0xb5d438: stur            w1, [x0, #0x3f]
    // 0xb5d43c: ldur            x1, [fp, #-0x20]
    // 0xb5d440: StoreField: r0->field_43 = r1
    //     0xb5d440: stur            w1, [x0, #0x43]
    // 0xb5d444: ldur            x2, [fp, #-0x18]
    // 0xb5d448: r1 = Function '<anonymous closure>':.
    //     0xb5d448: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f30] AnonymousClosure: (0xb5d49c), in [package:customer_app/app/presentation/views/glass/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::build (0xb5d2cc)
    //     0xb5d44c: ldr             x1, [x1, #0xf30]
    // 0xb5d450: r0 = AllocateClosure()
    //     0xb5d450: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5d454: mov             x1, x0
    // 0xb5d458: ldur            x0, [fp, #-0x50]
    // 0xb5d45c: StoreField: r0->field_47 = r1
    //     0xb5d45c: stur            w1, [x0, #0x47]
    // 0xb5d460: LeaveFrame
    //     0xb5d460: mov             SP, fp
    //     0xb5d464: ldp             fp, lr, [SP], #0x10
    // 0xb5d468: ret
    //     0xb5d468: ret             
    // 0xb5d46c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5d46c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5d470: b               #0xb5d2f4
    // 0xb5d474: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5d474: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5d478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5d478: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, dynamic, int) {
    // ** addr: 0xb5d49c, size: 0x80
    // 0xb5d49c: EnterFrame
    //     0xb5d49c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5d4a0: mov             fp, SP
    // 0xb5d4a4: AllocStack(0x8)
    //     0xb5d4a4: sub             SP, SP, #8
    // 0xb5d4a8: SetupParameters()
    //     0xb5d4a8: ldr             x0, [fp, #0x20]
    //     0xb5d4ac: ldur            w1, [x0, #0x17]
    //     0xb5d4b0: add             x1, x1, HEAP, lsl #32
    // 0xb5d4b4: CheckStackOverflow
    //     0xb5d4b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5d4b8: cmp             SP, x16
    //     0xb5d4bc: b.ls            #0xb5d514
    // 0xb5d4c0: LoadField: r3 = r1->field_f
    //     0xb5d4c0: ldur            w3, [x1, #0xf]
    // 0xb5d4c4: DecompressPointer r3
    //     0xb5d4c4: add             x3, x3, HEAP, lsl #32
    // 0xb5d4c8: ldr             x0, [fp, #0x18]
    // 0xb5d4cc: stur            x3, [fp, #-8]
    // 0xb5d4d0: r2 = Null
    //     0xb5d4d0: mov             x2, NULL
    // 0xb5d4d4: r1 = Null
    //     0xb5d4d4: mov             x1, NULL
    // 0xb5d4d8: r8 = List<Entity>
    //     0xb5d4d8: add             x8, PP, #0x53, lsl #12  ; [pp+0x538e0] Type: List<Entity>
    //     0xb5d4dc: ldr             x8, [x8, #0x8e0]
    // 0xb5d4e0: r3 = Null
    //     0xb5d4e0: add             x3, PP, #0x55, lsl #12  ; [pp+0x55f38] Null
    //     0xb5d4e4: ldr             x3, [x3, #0xf38]
    // 0xb5d4e8: r0 = List<Entity>()
    //     0xb5d4e8: bl              #0xa43098  ; IsType_List<Entity>_Stub
    // 0xb5d4ec: ldr             x0, [fp, #0x10]
    // 0xb5d4f0: r3 = LoadInt32Instr(r0)
    //     0xb5d4f0: sbfx            x3, x0, #1, #0x1f
    //     0xb5d4f4: tbz             w0, #0, #0xb5d4fc
    //     0xb5d4f8: ldur            x3, [x0, #7]
    // 0xb5d4fc: ldur            x1, [fp, #-8]
    // 0xb5d500: ldr             x2, [fp, #0x18]
    // 0xb5d504: r0 = bannerSlider()
    //     0xb5d504: bl              #0xb5d51c  ; [package:customer_app/app/presentation/views/glass/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::bannerSlider
    // 0xb5d508: LeaveFrame
    //     0xb5d508: mov             SP, fp
    //     0xb5d50c: ldp             fp, lr, [SP], #0x10
    // 0xb5d510: ret
    //     0xb5d510: ret             
    // 0xb5d514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5d514: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5d518: b               #0xb5d4c0
  }
  _ bannerSlider(/* No info */) {
    // ** addr: 0xb5d51c, size: 0x8a0
    // 0xb5d51c: EnterFrame
    //     0xb5d51c: stp             fp, lr, [SP, #-0x10]!
    //     0xb5d520: mov             fp, SP
    // 0xb5d524: AllocStack(0x78)
    //     0xb5d524: sub             SP, SP, #0x78
    // 0xb5d528: SetupParameters(_CarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb5d528: stur            x1, [fp, #-8]
    //     0xb5d52c: stur            x2, [fp, #-0x10]
    //     0xb5d530: stur            x3, [fp, #-0x18]
    // 0xb5d534: CheckStackOverflow
    //     0xb5d534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5d538: cmp             SP, x16
    //     0xb5d53c: b.ls            #0xb5dd84
    // 0xb5d540: r1 = 2
    //     0xb5d540: movz            x1, #0x2
    // 0xb5d544: r0 = AllocateContext()
    //     0xb5d544: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5d548: mov             x3, x0
    // 0xb5d54c: ldur            x2, [fp, #-8]
    // 0xb5d550: stur            x3, [fp, #-0x28]
    // 0xb5d554: StoreField: r3->field_f = r2
    //     0xb5d554: stur            w2, [x3, #0xf]
    // 0xb5d558: ldur            x4, [fp, #-0x18]
    // 0xb5d55c: r0 = BoxInt64Instr(r4)
    //     0xb5d55c: sbfiz           x0, x4, #1, #0x1f
    //     0xb5d560: cmp             x4, x0, asr #1
    //     0xb5d564: b.eq            #0xb5d570
    //     0xb5d568: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5d56c: stur            x4, [x0, #7]
    // 0xb5d570: StoreField: r3->field_13 = r0
    //     0xb5d570: stur            w0, [x3, #0x13]
    // 0xb5d574: LoadField: r0 = r2->field_b
    //     0xb5d574: ldur            w0, [x2, #0xb]
    // 0xb5d578: DecompressPointer r0
    //     0xb5d578: add             x0, x0, HEAP, lsl #32
    // 0xb5d57c: cmp             w0, NULL
    // 0xb5d580: b.eq            #0xb5dd8c
    // 0xb5d584: LoadField: r4 = r0->field_2f
    //     0xb5d584: ldur            w4, [x0, #0x2f]
    // 0xb5d588: DecompressPointer r4
    //     0xb5d588: add             x4, x4, HEAP, lsl #32
    // 0xb5d58c: stur            x4, [fp, #-0x20]
    // 0xb5d590: LoadField: r1 = r2->field_f
    //     0xb5d590: ldur            w1, [x2, #0xf]
    // 0xb5d594: DecompressPointer r1
    //     0xb5d594: add             x1, x1, HEAP, lsl #32
    // 0xb5d598: cmp             w1, NULL
    // 0xb5d59c: b.eq            #0xb5dd90
    // 0xb5d5a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb5d5a0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb5d5a4: r0 = _of()
    //     0xb5d5a4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb5d5a8: LoadField: r1 = r0->field_7
    //     0xb5d5a8: ldur            w1, [x0, #7]
    // 0xb5d5ac: DecompressPointer r1
    //     0xb5d5ac: add             x1, x1, HEAP, lsl #32
    // 0xb5d5b0: LoadField: d0 = r1->field_7
    //     0xb5d5b0: ldur            d0, [x1, #7]
    // 0xb5d5b4: ldur            x1, [fp, #-8]
    // 0xb5d5b8: stur            d0, [fp, #-0x60]
    // 0xb5d5bc: LoadField: r0 = r1->field_b
    //     0xb5d5bc: ldur            w0, [x1, #0xb]
    // 0xb5d5c0: DecompressPointer r0
    //     0xb5d5c0: add             x0, x0, HEAP, lsl #32
    // 0xb5d5c4: cmp             w0, NULL
    // 0xb5d5c8: b.eq            #0xb5dd94
    // 0xb5d5cc: LoadField: r2 = r0->field_33
    //     0xb5d5cc: ldur            w2, [x0, #0x33]
    // 0xb5d5d0: DecompressPointer r2
    //     0xb5d5d0: add             x2, x2, HEAP, lsl #32
    // 0xb5d5d4: ldur            x3, [fp, #-0x28]
    // 0xb5d5d8: stur            x2, [fp, #-0x30]
    // 0xb5d5dc: LoadField: r0 = r3->field_13
    //     0xb5d5dc: ldur            w0, [x3, #0x13]
    // 0xb5d5e0: DecompressPointer r0
    //     0xb5d5e0: add             x0, x0, HEAP, lsl #32
    // 0xb5d5e4: ldur            x4, [fp, #-0x10]
    // 0xb5d5e8: r5 = LoadClassIdInstr(r4)
    //     0xb5d5e8: ldur            x5, [x4, #-1]
    //     0xb5d5ec: ubfx            x5, x5, #0xc, #0x14
    // 0xb5d5f0: stp             x0, x4, [SP]
    // 0xb5d5f4: mov             x0, x5
    // 0xb5d5f8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb5d5f8: sub             lr, x0, #0xb7
    //     0xb5d5fc: ldr             lr, [x21, lr, lsl #3]
    //     0xb5d600: blr             lr
    // 0xb5d604: LoadField: r1 = r0->field_13
    //     0xb5d604: ldur            w1, [x0, #0x13]
    // 0xb5d608: DecompressPointer r1
    //     0xb5d608: add             x1, x1, HEAP, lsl #32
    // 0xb5d60c: cmp             w1, NULL
    // 0xb5d610: b.ne            #0xb5d61c
    // 0xb5d614: r5 = ""
    //     0xb5d614: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5d618: b               #0xb5d620
    // 0xb5d61c: mov             x5, x1
    // 0xb5d620: ldur            x4, [fp, #-0x10]
    // 0xb5d624: ldur            x3, [fp, #-0x28]
    // 0xb5d628: ldur            x0, [fp, #-0x30]
    // 0xb5d62c: ldur            d0, [fp, #-0x60]
    // 0xb5d630: stur            x5, [fp, #-0x38]
    // 0xb5d634: r1 = Function '<anonymous closure>':.
    //     0xb5d634: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f48] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb5d638: ldr             x1, [x1, #0xf48]
    // 0xb5d63c: r2 = Null
    //     0xb5d63c: mov             x2, NULL
    // 0xb5d640: r0 = AllocateClosure()
    //     0xb5d640: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5d644: r1 = Function '<anonymous closure>':.
    //     0xb5d644: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f50] AnonymousClosure: (0xa422dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xa42328)
    //     0xb5d648: ldr             x1, [x1, #0xf50]
    // 0xb5d64c: r2 = Null
    //     0xb5d64c: mov             x2, NULL
    // 0xb5d650: stur            x0, [fp, #-0x40]
    // 0xb5d654: r0 = AllocateClosure()
    //     0xb5d654: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5d658: stur            x0, [fp, #-0x48]
    // 0xb5d65c: r0 = CachedNetworkImage()
    //     0xb5d65c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb5d660: stur            x0, [fp, #-0x50]
    // 0xb5d664: r16 = Instance_BoxFit
    //     0xb5d664: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb5d668: ldr             x16, [x16, #0x118]
    // 0xb5d66c: ldur            lr, [fp, #-0x40]
    // 0xb5d670: stp             lr, x16, [SP, #8]
    // 0xb5d674: ldur            x16, [fp, #-0x48]
    // 0xb5d678: str             x16, [SP]
    // 0xb5d67c: mov             x1, x0
    // 0xb5d680: ldur            x2, [fp, #-0x38]
    // 0xb5d684: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb5d684: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb5d688: ldr             x4, [x4, #0x638]
    // 0xb5d68c: r0 = CachedNetworkImage()
    //     0xb5d68c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb5d690: r0 = ClipRRect()
    //     0xb5d690: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xb5d694: mov             x1, x0
    // 0xb5d698: ldur            x0, [fp, #-0x30]
    // 0xb5d69c: stur            x1, [fp, #-0x38]
    // 0xb5d6a0: StoreField: r1->field_f = r0
    //     0xb5d6a0: stur            w0, [x1, #0xf]
    // 0xb5d6a4: r0 = Instance_Clip
    //     0xb5d6a4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb5d6a8: ldr             x0, [x0, #0x138]
    // 0xb5d6ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5d6ac: stur            w0, [x1, #0x17]
    // 0xb5d6b0: ldur            x0, [fp, #-0x50]
    // 0xb5d6b4: StoreField: r1->field_b = r0
    //     0xb5d6b4: stur            w0, [x1, #0xb]
    // 0xb5d6b8: ldur            d0, [fp, #-0x60]
    // 0xb5d6bc: r0 = inline_Allocate_Double()
    //     0xb5d6bc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb5d6c0: add             x0, x0, #0x10
    //     0xb5d6c4: cmp             x2, x0
    //     0xb5d6c8: b.ls            #0xb5dd98
    //     0xb5d6cc: str             x0, [THR, #0x50]  ; THR::top
    //     0xb5d6d0: sub             x0, x0, #0xf
    //     0xb5d6d4: movz            x2, #0xe15c
    //     0xb5d6d8: movk            x2, #0x3, lsl #16
    //     0xb5d6dc: stur            x2, [x0, #-1]
    // 0xb5d6e0: StoreField: r0->field_7 = d0
    //     0xb5d6e0: stur            d0, [x0, #7]
    // 0xb5d6e4: stur            x0, [fp, #-0x30]
    // 0xb5d6e8: r0 = Container()
    //     0xb5d6e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5d6ec: stur            x0, [fp, #-0x40]
    // 0xb5d6f0: ldur            x16, [fp, #-0x20]
    // 0xb5d6f4: ldur            lr, [fp, #-0x30]
    // 0xb5d6f8: stp             lr, x16, [SP, #8]
    // 0xb5d6fc: ldur            x16, [fp, #-0x38]
    // 0xb5d700: str             x16, [SP]
    // 0xb5d704: mov             x1, x0
    // 0xb5d708: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x1, width, 0x2, null]
    //     0xb5d708: add             x4, PP, #0x37, lsl #12  ; [pp+0x371b8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xb5d70c: ldr             x4, [x4, #0x1b8]
    // 0xb5d710: r0 = Container()
    //     0xb5d710: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5d714: ldur            x2, [fp, #-0x28]
    // 0xb5d718: LoadField: r0 = r2->field_13
    //     0xb5d718: ldur            w0, [x2, #0x13]
    // 0xb5d71c: DecompressPointer r0
    //     0xb5d71c: add             x0, x0, HEAP, lsl #32
    // 0xb5d720: ldur            x1, [fp, #-0x10]
    // 0xb5d724: r3 = LoadClassIdInstr(r1)
    //     0xb5d724: ldur            x3, [x1, #-1]
    //     0xb5d728: ubfx            x3, x3, #0xc, #0x14
    // 0xb5d72c: stp             x0, x1, [SP]
    // 0xb5d730: mov             x0, x3
    // 0xb5d734: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb5d734: sub             lr, x0, #0xb7
    //     0xb5d738: ldr             lr, [x21, lr, lsl #3]
    //     0xb5d73c: blr             lr
    // 0xb5d740: LoadField: r1 = r0->field_7
    //     0xb5d740: ldur            w1, [x0, #7]
    // 0xb5d744: DecompressPointer r1
    //     0xb5d744: add             x1, x1, HEAP, lsl #32
    // 0xb5d748: cmp             w1, NULL
    // 0xb5d74c: b.ne            #0xb5d758
    // 0xb5d750: r0 = Null
    //     0xb5d750: mov             x0, NULL
    // 0xb5d754: b               #0xb5d770
    // 0xb5d758: LoadField: r0 = r1->field_7
    //     0xb5d758: ldur            w0, [x1, #7]
    // 0xb5d75c: cbnz            w0, #0xb5d768
    // 0xb5d760: r1 = false
    //     0xb5d760: add             x1, NULL, #0x30  ; false
    // 0xb5d764: b               #0xb5d76c
    // 0xb5d768: r1 = true
    //     0xb5d768: add             x1, NULL, #0x20  ; true
    // 0xb5d76c: mov             x0, x1
    // 0xb5d770: cmp             w0, NULL
    // 0xb5d774: b.ne            #0xb5d780
    // 0xb5d778: r3 = false
    //     0xb5d778: add             x3, NULL, #0x30  ; false
    // 0xb5d77c: b               #0xb5d784
    // 0xb5d780: mov             x3, x0
    // 0xb5d784: ldur            x1, [fp, #-0x10]
    // 0xb5d788: ldur            x2, [fp, #-0x28]
    // 0xb5d78c: stur            x3, [fp, #-0x20]
    // 0xb5d790: LoadField: r0 = r2->field_13
    //     0xb5d790: ldur            w0, [x2, #0x13]
    // 0xb5d794: DecompressPointer r0
    //     0xb5d794: add             x0, x0, HEAP, lsl #32
    // 0xb5d798: r4 = LoadClassIdInstr(r1)
    //     0xb5d798: ldur            x4, [x1, #-1]
    //     0xb5d79c: ubfx            x4, x4, #0xc, #0x14
    // 0xb5d7a0: stp             x0, x1, [SP]
    // 0xb5d7a4: mov             x0, x4
    // 0xb5d7a8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb5d7a8: sub             lr, x0, #0xb7
    //     0xb5d7ac: ldr             lr, [x21, lr, lsl #3]
    //     0xb5d7b0: blr             lr
    // 0xb5d7b4: LoadField: r1 = r0->field_7
    //     0xb5d7b4: ldur            w1, [x0, #7]
    // 0xb5d7b8: DecompressPointer r1
    //     0xb5d7b8: add             x1, x1, HEAP, lsl #32
    // 0xb5d7bc: cmp             w1, NULL
    // 0xb5d7c0: b.ne            #0xb5d7cc
    // 0xb5d7c4: r4 = ""
    //     0xb5d7c4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5d7c8: b               #0xb5d7d0
    // 0xb5d7cc: mov             x4, x1
    // 0xb5d7d0: ldur            x3, [fp, #-8]
    // 0xb5d7d4: ldur            x0, [fp, #-0x10]
    // 0xb5d7d8: ldur            x2, [fp, #-0x28]
    // 0xb5d7dc: stur            x4, [fp, #-0x30]
    // 0xb5d7e0: LoadField: r1 = r3->field_f
    //     0xb5d7e0: ldur            w1, [x3, #0xf]
    // 0xb5d7e4: DecompressPointer r1
    //     0xb5d7e4: add             x1, x1, HEAP, lsl #32
    // 0xb5d7e8: cmp             w1, NULL
    // 0xb5d7ec: b.eq            #0xb5ddb0
    // 0xb5d7f0: r0 = of()
    //     0xb5d7f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5d7f4: LoadField: r1 = r0->field_87
    //     0xb5d7f4: ldur            w1, [x0, #0x87]
    // 0xb5d7f8: DecompressPointer r1
    //     0xb5d7f8: add             x1, x1, HEAP, lsl #32
    // 0xb5d7fc: LoadField: r0 = r1->field_27
    //     0xb5d7fc: ldur            w0, [x1, #0x27]
    // 0xb5d800: DecompressPointer r0
    //     0xb5d800: add             x0, x0, HEAP, lsl #32
    // 0xb5d804: r16 = 21.000000
    //     0xb5d804: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb5d808: ldr             x16, [x16, #0x9b0]
    // 0xb5d80c: r30 = Instance_Color
    //     0xb5d80c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb5d810: stp             lr, x16, [SP]
    // 0xb5d814: mov             x1, x0
    // 0xb5d818: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb5d818: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb5d81c: ldr             x4, [x4, #0xaa0]
    // 0xb5d820: r0 = copyWith()
    //     0xb5d820: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5d824: stur            x0, [fp, #-0x38]
    // 0xb5d828: r0 = Text()
    //     0xb5d828: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5d82c: mov             x1, x0
    // 0xb5d830: ldur            x0, [fp, #-0x30]
    // 0xb5d834: stur            x1, [fp, #-0x48]
    // 0xb5d838: StoreField: r1->field_b = r0
    //     0xb5d838: stur            w0, [x1, #0xb]
    // 0xb5d83c: ldur            x0, [fp, #-0x38]
    // 0xb5d840: StoreField: r1->field_13 = r0
    //     0xb5d840: stur            w0, [x1, #0x13]
    // 0xb5d844: r0 = Instance_TextOverflow
    //     0xb5d844: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb5d848: ldr             x0, [x0, #0xe10]
    // 0xb5d84c: StoreField: r1->field_2b = r0
    //     0xb5d84c: stur            w0, [x1, #0x2b]
    // 0xb5d850: r2 = 2
    //     0xb5d850: movz            x2, #0x2
    // 0xb5d854: StoreField: r1->field_37 = r2
    //     0xb5d854: stur            w2, [x1, #0x37]
    // 0xb5d858: r0 = Padding()
    //     0xb5d858: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5d85c: mov             x1, x0
    // 0xb5d860: r0 = Instance_EdgeInsets
    //     0xb5d860: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f078] Obj!EdgeInsets@d571a1
    //     0xb5d864: ldr             x0, [x0, #0x78]
    // 0xb5d868: stur            x1, [fp, #-0x30]
    // 0xb5d86c: StoreField: r1->field_f = r0
    //     0xb5d86c: stur            w0, [x1, #0xf]
    // 0xb5d870: ldur            x0, [fp, #-0x48]
    // 0xb5d874: StoreField: r1->field_b = r0
    //     0xb5d874: stur            w0, [x1, #0xb]
    // 0xb5d878: ldur            x2, [fp, #-0x28]
    // 0xb5d87c: LoadField: r0 = r2->field_13
    //     0xb5d87c: ldur            w0, [x2, #0x13]
    // 0xb5d880: DecompressPointer r0
    //     0xb5d880: add             x0, x0, HEAP, lsl #32
    // 0xb5d884: ldur            x3, [fp, #-0x10]
    // 0xb5d888: r4 = LoadClassIdInstr(r3)
    //     0xb5d888: ldur            x4, [x3, #-1]
    //     0xb5d88c: ubfx            x4, x4, #0xc, #0x14
    // 0xb5d890: stp             x0, x3, [SP]
    // 0xb5d894: mov             x0, x4
    // 0xb5d898: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb5d898: sub             lr, x0, #0xb7
    //     0xb5d89c: ldr             lr, [x21, lr, lsl #3]
    //     0xb5d8a0: blr             lr
    // 0xb5d8a4: LoadField: r1 = r0->field_f
    //     0xb5d8a4: ldur            w1, [x0, #0xf]
    // 0xb5d8a8: DecompressPointer r1
    //     0xb5d8a8: add             x1, x1, HEAP, lsl #32
    // 0xb5d8ac: cmp             w1, NULL
    // 0xb5d8b0: b.ne            #0xb5d8bc
    // 0xb5d8b4: r0 = Null
    //     0xb5d8b4: mov             x0, NULL
    // 0xb5d8b8: b               #0xb5d8d4
    // 0xb5d8bc: LoadField: r0 = r1->field_7
    //     0xb5d8bc: ldur            w0, [x1, #7]
    // 0xb5d8c0: cbnz            w0, #0xb5d8cc
    // 0xb5d8c4: r1 = false
    //     0xb5d8c4: add             x1, NULL, #0x30  ; false
    // 0xb5d8c8: b               #0xb5d8d0
    // 0xb5d8cc: r1 = true
    //     0xb5d8cc: add             x1, NULL, #0x20  ; true
    // 0xb5d8d0: mov             x0, x1
    // 0xb5d8d4: cmp             w0, NULL
    // 0xb5d8d8: b.ne            #0xb5d8e4
    // 0xb5d8dc: r1 = false
    //     0xb5d8dc: add             x1, NULL, #0x30  ; false
    // 0xb5d8e0: b               #0xb5d8e8
    // 0xb5d8e4: mov             x1, x0
    // 0xb5d8e8: ldur            x0, [fp, #-0x10]
    // 0xb5d8ec: ldur            x2, [fp, #-0x28]
    // 0xb5d8f0: stur            x1, [fp, #-0x38]
    // 0xb5d8f4: r16 = <Color>
    //     0xb5d8f4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb5d8f8: ldr             x16, [x16, #0xf80]
    // 0xb5d8fc: r30 = Instance_Color
    //     0xb5d8fc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb5d900: stp             lr, x16, [SP]
    // 0xb5d904: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5d904: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5d908: r0 = all()
    //     0xb5d908: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5d90c: stur            x0, [fp, #-0x48]
    // 0xb5d910: r0 = Radius()
    //     0xb5d910: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb5d914: d0 = 20.000000
    //     0xb5d914: fmov            d0, #20.00000000
    // 0xb5d918: stur            x0, [fp, #-0x50]
    // 0xb5d91c: StoreField: r0->field_7 = d0
    //     0xb5d91c: stur            d0, [x0, #7]
    // 0xb5d920: StoreField: r0->field_f = d0
    //     0xb5d920: stur            d0, [x0, #0xf]
    // 0xb5d924: r0 = BorderRadius()
    //     0xb5d924: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb5d928: mov             x1, x0
    // 0xb5d92c: ldur            x0, [fp, #-0x50]
    // 0xb5d930: stur            x1, [fp, #-0x58]
    // 0xb5d934: StoreField: r1->field_7 = r0
    //     0xb5d934: stur            w0, [x1, #7]
    // 0xb5d938: StoreField: r1->field_b = r0
    //     0xb5d938: stur            w0, [x1, #0xb]
    // 0xb5d93c: StoreField: r1->field_f = r0
    //     0xb5d93c: stur            w0, [x1, #0xf]
    // 0xb5d940: StoreField: r1->field_13 = r0
    //     0xb5d940: stur            w0, [x1, #0x13]
    // 0xb5d944: r0 = RoundedRectangleBorder()
    //     0xb5d944: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb5d948: mov             x1, x0
    // 0xb5d94c: ldur            x0, [fp, #-0x58]
    // 0xb5d950: StoreField: r1->field_b = r0
    //     0xb5d950: stur            w0, [x1, #0xb]
    // 0xb5d954: r0 = Instance_BorderSide
    //     0xb5d954: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb5d958: ldr             x0, [x0, #0xe20]
    // 0xb5d95c: StoreField: r1->field_7 = r0
    //     0xb5d95c: stur            w0, [x1, #7]
    // 0xb5d960: r16 = <RoundedRectangleBorder>
    //     0xb5d960: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb5d964: ldr             x16, [x16, #0xf78]
    // 0xb5d968: stp             x1, x16, [SP]
    // 0xb5d96c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb5d96c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb5d970: r0 = all()
    //     0xb5d970: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb5d974: stur            x0, [fp, #-0x50]
    // 0xb5d978: r0 = ButtonStyle()
    //     0xb5d978: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb5d97c: mov             x1, x0
    // 0xb5d980: ldur            x0, [fp, #-0x48]
    // 0xb5d984: stur            x1, [fp, #-0x58]
    // 0xb5d988: StoreField: r1->field_b = r0
    //     0xb5d988: stur            w0, [x1, #0xb]
    // 0xb5d98c: ldur            x0, [fp, #-0x50]
    // 0xb5d990: StoreField: r1->field_43 = r0
    //     0xb5d990: stur            w0, [x1, #0x43]
    // 0xb5d994: r0 = TextButtonThemeData()
    //     0xb5d994: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb5d998: mov             x1, x0
    // 0xb5d99c: ldur            x0, [fp, #-0x58]
    // 0xb5d9a0: stur            x1, [fp, #-0x48]
    // 0xb5d9a4: StoreField: r1->field_7 = r0
    //     0xb5d9a4: stur            w0, [x1, #7]
    // 0xb5d9a8: ldur            x2, [fp, #-0x28]
    // 0xb5d9ac: LoadField: r0 = r2->field_13
    //     0xb5d9ac: ldur            w0, [x2, #0x13]
    // 0xb5d9b0: DecompressPointer r0
    //     0xb5d9b0: add             x0, x0, HEAP, lsl #32
    // 0xb5d9b4: ldur            x3, [fp, #-0x10]
    // 0xb5d9b8: r4 = LoadClassIdInstr(r3)
    //     0xb5d9b8: ldur            x4, [x3, #-1]
    //     0xb5d9bc: ubfx            x4, x4, #0xc, #0x14
    // 0xb5d9c0: stp             x0, x3, [SP]
    // 0xb5d9c4: mov             x0, x4
    // 0xb5d9c8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb5d9c8: sub             lr, x0, #0xb7
    //     0xb5d9cc: ldr             lr, [x21, lr, lsl #3]
    //     0xb5d9d0: blr             lr
    // 0xb5d9d4: LoadField: r1 = r0->field_f
    //     0xb5d9d4: ldur            w1, [x0, #0xf]
    // 0xb5d9d8: DecompressPointer r1
    //     0xb5d9d8: add             x1, x1, HEAP, lsl #32
    // 0xb5d9dc: cmp             w1, NULL
    // 0xb5d9e0: b.ne            #0xb5d9ec
    // 0xb5d9e4: r7 = ""
    //     0xb5d9e4: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5d9e8: b               #0xb5d9f0
    // 0xb5d9ec: mov             x7, x1
    // 0xb5d9f0: ldur            x4, [fp, #-8]
    // 0xb5d9f4: ldur            x6, [fp, #-0x40]
    // 0xb5d9f8: ldur            x5, [fp, #-0x20]
    // 0xb5d9fc: ldur            x3, [fp, #-0x30]
    // 0xb5da00: ldur            x2, [fp, #-0x38]
    // 0xb5da04: ldur            x0, [fp, #-0x48]
    // 0xb5da08: stur            x7, [fp, #-0x10]
    // 0xb5da0c: LoadField: r1 = r4->field_f
    //     0xb5da0c: ldur            w1, [x4, #0xf]
    // 0xb5da10: DecompressPointer r1
    //     0xb5da10: add             x1, x1, HEAP, lsl #32
    // 0xb5da14: cmp             w1, NULL
    // 0xb5da18: b.eq            #0xb5ddb4
    // 0xb5da1c: r0 = of()
    //     0xb5da1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5da20: LoadField: r1 = r0->field_87
    //     0xb5da20: ldur            w1, [x0, #0x87]
    // 0xb5da24: DecompressPointer r1
    //     0xb5da24: add             x1, x1, HEAP, lsl #32
    // 0xb5da28: LoadField: r0 = r1->field_7
    //     0xb5da28: ldur            w0, [x1, #7]
    // 0xb5da2c: DecompressPointer r0
    //     0xb5da2c: add             x0, x0, HEAP, lsl #32
    // 0xb5da30: ldur            x1, [fp, #-8]
    // 0xb5da34: stur            x0, [fp, #-0x50]
    // 0xb5da38: LoadField: r2 = r1->field_f
    //     0xb5da38: ldur            w2, [x1, #0xf]
    // 0xb5da3c: DecompressPointer r2
    //     0xb5da3c: add             x2, x2, HEAP, lsl #32
    // 0xb5da40: cmp             w2, NULL
    // 0xb5da44: b.eq            #0xb5ddb8
    // 0xb5da48: mov             x1, x2
    // 0xb5da4c: r0 = of()
    //     0xb5da4c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5da50: LoadField: r1 = r0->field_5b
    //     0xb5da50: ldur            w1, [x0, #0x5b]
    // 0xb5da54: DecompressPointer r1
    //     0xb5da54: add             x1, x1, HEAP, lsl #32
    // 0xb5da58: r16 = 16.000000
    //     0xb5da58: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb5da5c: ldr             x16, [x16, #0x188]
    // 0xb5da60: stp             x16, x1, [SP]
    // 0xb5da64: ldur            x1, [fp, #-0x50]
    // 0xb5da68: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb5da68: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5da6c: ldr             x4, [x4, #0x9b8]
    // 0xb5da70: r0 = copyWith()
    //     0xb5da70: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5da74: stur            x0, [fp, #-8]
    // 0xb5da78: r0 = Text()
    //     0xb5da78: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5da7c: mov             x1, x0
    // 0xb5da80: ldur            x0, [fp, #-0x10]
    // 0xb5da84: stur            x1, [fp, #-0x50]
    // 0xb5da88: StoreField: r1->field_b = r0
    //     0xb5da88: stur            w0, [x1, #0xb]
    // 0xb5da8c: ldur            x0, [fp, #-8]
    // 0xb5da90: StoreField: r1->field_13 = r0
    //     0xb5da90: stur            w0, [x1, #0x13]
    // 0xb5da94: r0 = Instance_TextOverflow
    //     0xb5da94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb5da98: ldr             x0, [x0, #0xe10]
    // 0xb5da9c: StoreField: r1->field_2b = r0
    //     0xb5da9c: stur            w0, [x1, #0x2b]
    // 0xb5daa0: r2 = 2
    //     0xb5daa0: movz            x2, #0x2
    // 0xb5daa4: StoreField: r1->field_37 = r2
    //     0xb5daa4: stur            w2, [x1, #0x37]
    // 0xb5daa8: r0 = Padding()
    //     0xb5daa8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5daac: mov             x3, x0
    // 0xb5dab0: r0 = Instance_EdgeInsets
    //     0xb5dab0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xb5dab4: ldr             x0, [x0, #0xc10]
    // 0xb5dab8: stur            x3, [fp, #-8]
    // 0xb5dabc: StoreField: r3->field_f = r0
    //     0xb5dabc: stur            w0, [x3, #0xf]
    // 0xb5dac0: ldur            x0, [fp, #-0x50]
    // 0xb5dac4: StoreField: r3->field_b = r0
    //     0xb5dac4: stur            w0, [x3, #0xb]
    // 0xb5dac8: ldur            x2, [fp, #-0x28]
    // 0xb5dacc: r1 = Function '<anonymous closure>':.
    //     0xb5dacc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55f58] AnonymousClosure: (0xb5ddbc), in [package:customer_app/app/presentation/views/glass/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::bannerSlider (0xb5d51c)
    //     0xb5dad0: ldr             x1, [x1, #0xf58]
    // 0xb5dad4: r0 = AllocateClosure()
    //     0xb5dad4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5dad8: stur            x0, [fp, #-0x10]
    // 0xb5dadc: r0 = TextButton()
    //     0xb5dadc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb5dae0: mov             x1, x0
    // 0xb5dae4: ldur            x0, [fp, #-0x10]
    // 0xb5dae8: stur            x1, [fp, #-0x28]
    // 0xb5daec: StoreField: r1->field_b = r0
    //     0xb5daec: stur            w0, [x1, #0xb]
    // 0xb5daf0: r0 = false
    //     0xb5daf0: add             x0, NULL, #0x30  ; false
    // 0xb5daf4: StoreField: r1->field_27 = r0
    //     0xb5daf4: stur            w0, [x1, #0x27]
    // 0xb5daf8: r2 = true
    //     0xb5daf8: add             x2, NULL, #0x20  ; true
    // 0xb5dafc: StoreField: r1->field_2f = r2
    //     0xb5dafc: stur            w2, [x1, #0x2f]
    // 0xb5db00: ldur            x2, [fp, #-8]
    // 0xb5db04: StoreField: r1->field_37 = r2
    //     0xb5db04: stur            w2, [x1, #0x37]
    // 0xb5db08: r0 = TextButtonTheme()
    //     0xb5db08: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb5db0c: mov             x1, x0
    // 0xb5db10: ldur            x0, [fp, #-0x48]
    // 0xb5db14: stur            x1, [fp, #-8]
    // 0xb5db18: StoreField: r1->field_f = r0
    //     0xb5db18: stur            w0, [x1, #0xf]
    // 0xb5db1c: ldur            x0, [fp, #-0x28]
    // 0xb5db20: StoreField: r1->field_b = r0
    //     0xb5db20: stur            w0, [x1, #0xb]
    // 0xb5db24: r0 = Visibility()
    //     0xb5db24: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb5db28: mov             x3, x0
    // 0xb5db2c: ldur            x0, [fp, #-8]
    // 0xb5db30: stur            x3, [fp, #-0x10]
    // 0xb5db34: StoreField: r3->field_b = r0
    //     0xb5db34: stur            w0, [x3, #0xb]
    // 0xb5db38: r0 = Instance_SizedBox
    //     0xb5db38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb5db3c: StoreField: r3->field_f = r0
    //     0xb5db3c: stur            w0, [x3, #0xf]
    // 0xb5db40: ldur            x1, [fp, #-0x38]
    // 0xb5db44: StoreField: r3->field_13 = r1
    //     0xb5db44: stur            w1, [x3, #0x13]
    // 0xb5db48: r4 = false
    //     0xb5db48: add             x4, NULL, #0x30  ; false
    // 0xb5db4c: ArrayStore: r3[0] = r4  ; List_4
    //     0xb5db4c: stur            w4, [x3, #0x17]
    // 0xb5db50: StoreField: r3->field_1b = r4
    //     0xb5db50: stur            w4, [x3, #0x1b]
    // 0xb5db54: StoreField: r3->field_1f = r4
    //     0xb5db54: stur            w4, [x3, #0x1f]
    // 0xb5db58: StoreField: r3->field_23 = r4
    //     0xb5db58: stur            w4, [x3, #0x23]
    // 0xb5db5c: StoreField: r3->field_27 = r4
    //     0xb5db5c: stur            w4, [x3, #0x27]
    // 0xb5db60: StoreField: r3->field_2b = r4
    //     0xb5db60: stur            w4, [x3, #0x2b]
    // 0xb5db64: r1 = Null
    //     0xb5db64: mov             x1, NULL
    // 0xb5db68: r2 = 4
    //     0xb5db68: movz            x2, #0x4
    // 0xb5db6c: r0 = AllocateArray()
    //     0xb5db6c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5db70: mov             x2, x0
    // 0xb5db74: ldur            x0, [fp, #-0x30]
    // 0xb5db78: stur            x2, [fp, #-8]
    // 0xb5db7c: StoreField: r2->field_f = r0
    //     0xb5db7c: stur            w0, [x2, #0xf]
    // 0xb5db80: ldur            x0, [fp, #-0x10]
    // 0xb5db84: StoreField: r2->field_13 = r0
    //     0xb5db84: stur            w0, [x2, #0x13]
    // 0xb5db88: r1 = <Widget>
    //     0xb5db88: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5db8c: r0 = AllocateGrowableArray()
    //     0xb5db8c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5db90: mov             x1, x0
    // 0xb5db94: ldur            x0, [fp, #-8]
    // 0xb5db98: stur            x1, [fp, #-0x10]
    // 0xb5db9c: StoreField: r1->field_f = r0
    //     0xb5db9c: stur            w0, [x1, #0xf]
    // 0xb5dba0: r2 = 4
    //     0xb5dba0: movz            x2, #0x4
    // 0xb5dba4: StoreField: r1->field_b = r2
    //     0xb5dba4: stur            w2, [x1, #0xb]
    // 0xb5dba8: r0 = Column()
    //     0xb5dba8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb5dbac: mov             x3, x0
    // 0xb5dbb0: r0 = Instance_Axis
    //     0xb5dbb0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb5dbb4: stur            x3, [fp, #-8]
    // 0xb5dbb8: StoreField: r3->field_f = r0
    //     0xb5dbb8: stur            w0, [x3, #0xf]
    // 0xb5dbbc: r0 = Instance_MainAxisAlignment
    //     0xb5dbbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb5dbc0: ldr             x0, [x0, #0xab0]
    // 0xb5dbc4: StoreField: r3->field_13 = r0
    //     0xb5dbc4: stur            w0, [x3, #0x13]
    // 0xb5dbc8: r0 = Instance_MainAxisSize
    //     0xb5dbc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5dbcc: ldr             x0, [x0, #0xa10]
    // 0xb5dbd0: ArrayStore: r3[0] = r0  ; List_4
    //     0xb5dbd0: stur            w0, [x3, #0x17]
    // 0xb5dbd4: r0 = Instance_CrossAxisAlignment
    //     0xb5dbd4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5dbd8: ldr             x0, [x0, #0xa18]
    // 0xb5dbdc: StoreField: r3->field_1b = r0
    //     0xb5dbdc: stur            w0, [x3, #0x1b]
    // 0xb5dbe0: r0 = Instance_VerticalDirection
    //     0xb5dbe0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5dbe4: ldr             x0, [x0, #0xa20]
    // 0xb5dbe8: StoreField: r3->field_23 = r0
    //     0xb5dbe8: stur            w0, [x3, #0x23]
    // 0xb5dbec: r0 = Instance_Clip
    //     0xb5dbec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5dbf0: ldr             x0, [x0, #0x38]
    // 0xb5dbf4: StoreField: r3->field_2b = r0
    //     0xb5dbf4: stur            w0, [x3, #0x2b]
    // 0xb5dbf8: StoreField: r3->field_2f = rZR
    //     0xb5dbf8: stur            xzr, [x3, #0x2f]
    // 0xb5dbfc: ldur            x0, [fp, #-0x10]
    // 0xb5dc00: StoreField: r3->field_b = r0
    //     0xb5dc00: stur            w0, [x3, #0xb]
    // 0xb5dc04: r1 = Null
    //     0xb5dc04: mov             x1, NULL
    // 0xb5dc08: r2 = 2
    //     0xb5dc08: movz            x2, #0x2
    // 0xb5dc0c: r0 = AllocateArray()
    //     0xb5dc0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5dc10: mov             x2, x0
    // 0xb5dc14: ldur            x0, [fp, #-8]
    // 0xb5dc18: stur            x2, [fp, #-0x10]
    // 0xb5dc1c: StoreField: r2->field_f = r0
    //     0xb5dc1c: stur            w0, [x2, #0xf]
    // 0xb5dc20: r1 = <Widget>
    //     0xb5dc20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5dc24: r0 = AllocateGrowableArray()
    //     0xb5dc24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5dc28: mov             x1, x0
    // 0xb5dc2c: ldur            x0, [fp, #-0x10]
    // 0xb5dc30: stur            x1, [fp, #-8]
    // 0xb5dc34: StoreField: r1->field_f = r0
    //     0xb5dc34: stur            w0, [x1, #0xf]
    // 0xb5dc38: r0 = 2
    //     0xb5dc38: movz            x0, #0x2
    // 0xb5dc3c: StoreField: r1->field_b = r0
    //     0xb5dc3c: stur            w0, [x1, #0xb]
    // 0xb5dc40: r0 = Stack()
    //     0xb5dc40: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb5dc44: mov             x1, x0
    // 0xb5dc48: r0 = Instance_Alignment
    //     0xb5dc48: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb5dc4c: ldr             x0, [x0, #0x5b8]
    // 0xb5dc50: stur            x1, [fp, #-0x10]
    // 0xb5dc54: StoreField: r1->field_f = r0
    //     0xb5dc54: stur            w0, [x1, #0xf]
    // 0xb5dc58: r0 = Instance_StackFit
    //     0xb5dc58: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb5dc5c: ldr             x0, [x0, #0xfa8]
    // 0xb5dc60: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5dc60: stur            w0, [x1, #0x17]
    // 0xb5dc64: r2 = Instance_Clip
    //     0xb5dc64: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb5dc68: ldr             x2, [x2, #0x7e0]
    // 0xb5dc6c: StoreField: r1->field_1b = r2
    //     0xb5dc6c: stur            w2, [x1, #0x1b]
    // 0xb5dc70: ldur            x3, [fp, #-8]
    // 0xb5dc74: StoreField: r1->field_b = r3
    //     0xb5dc74: stur            w3, [x1, #0xb]
    // 0xb5dc78: r0 = SizedBox()
    //     0xb5dc78: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb5dc7c: mov             x1, x0
    // 0xb5dc80: r0 = 200.000000
    //     0xb5dc80: add             x0, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0xb5dc84: ldr             x0, [x0, #0x570]
    // 0xb5dc88: stur            x1, [fp, #-8]
    // 0xb5dc8c: StoreField: r1->field_f = r0
    //     0xb5dc8c: stur            w0, [x1, #0xf]
    // 0xb5dc90: r0 = 110.000000
    //     0xb5dc90: add             x0, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb5dc94: ldr             x0, [x0, #0x770]
    // 0xb5dc98: StoreField: r1->field_13 = r0
    //     0xb5dc98: stur            w0, [x1, #0x13]
    // 0xb5dc9c: ldur            x0, [fp, #-0x10]
    // 0xb5dca0: StoreField: r1->field_b = r0
    //     0xb5dca0: stur            w0, [x1, #0xb]
    // 0xb5dca4: r0 = Padding()
    //     0xb5dca4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5dca8: mov             x1, x0
    // 0xb5dcac: r0 = Instance_EdgeInsets
    //     0xb5dcac: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xb5dcb0: ldr             x0, [x0, #0x240]
    // 0xb5dcb4: stur            x1, [fp, #-0x10]
    // 0xb5dcb8: StoreField: r1->field_f = r0
    //     0xb5dcb8: stur            w0, [x1, #0xf]
    // 0xb5dcbc: ldur            x0, [fp, #-8]
    // 0xb5dcc0: StoreField: r1->field_b = r0
    //     0xb5dcc0: stur            w0, [x1, #0xb]
    // 0xb5dcc4: r0 = Visibility()
    //     0xb5dcc4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb5dcc8: mov             x3, x0
    // 0xb5dccc: ldur            x0, [fp, #-0x10]
    // 0xb5dcd0: stur            x3, [fp, #-8]
    // 0xb5dcd4: StoreField: r3->field_b = r0
    //     0xb5dcd4: stur            w0, [x3, #0xb]
    // 0xb5dcd8: r0 = Instance_SizedBox
    //     0xb5dcd8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb5dcdc: StoreField: r3->field_f = r0
    //     0xb5dcdc: stur            w0, [x3, #0xf]
    // 0xb5dce0: ldur            x0, [fp, #-0x20]
    // 0xb5dce4: StoreField: r3->field_13 = r0
    //     0xb5dce4: stur            w0, [x3, #0x13]
    // 0xb5dce8: r0 = false
    //     0xb5dce8: add             x0, NULL, #0x30  ; false
    // 0xb5dcec: ArrayStore: r3[0] = r0  ; List_4
    //     0xb5dcec: stur            w0, [x3, #0x17]
    // 0xb5dcf0: StoreField: r3->field_1b = r0
    //     0xb5dcf0: stur            w0, [x3, #0x1b]
    // 0xb5dcf4: StoreField: r3->field_1f = r0
    //     0xb5dcf4: stur            w0, [x3, #0x1f]
    // 0xb5dcf8: StoreField: r3->field_23 = r0
    //     0xb5dcf8: stur            w0, [x3, #0x23]
    // 0xb5dcfc: StoreField: r3->field_27 = r0
    //     0xb5dcfc: stur            w0, [x3, #0x27]
    // 0xb5dd00: StoreField: r3->field_2b = r0
    //     0xb5dd00: stur            w0, [x3, #0x2b]
    // 0xb5dd04: r1 = Null
    //     0xb5dd04: mov             x1, NULL
    // 0xb5dd08: r2 = 4
    //     0xb5dd08: movz            x2, #0x4
    // 0xb5dd0c: r0 = AllocateArray()
    //     0xb5dd0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5dd10: mov             x2, x0
    // 0xb5dd14: ldur            x0, [fp, #-0x40]
    // 0xb5dd18: stur            x2, [fp, #-0x10]
    // 0xb5dd1c: StoreField: r2->field_f = r0
    //     0xb5dd1c: stur            w0, [x2, #0xf]
    // 0xb5dd20: ldur            x0, [fp, #-8]
    // 0xb5dd24: StoreField: r2->field_13 = r0
    //     0xb5dd24: stur            w0, [x2, #0x13]
    // 0xb5dd28: r1 = <Widget>
    //     0xb5dd28: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5dd2c: r0 = AllocateGrowableArray()
    //     0xb5dd2c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5dd30: mov             x1, x0
    // 0xb5dd34: ldur            x0, [fp, #-0x10]
    // 0xb5dd38: stur            x1, [fp, #-8]
    // 0xb5dd3c: StoreField: r1->field_f = r0
    //     0xb5dd3c: stur            w0, [x1, #0xf]
    // 0xb5dd40: r0 = 4
    //     0xb5dd40: movz            x0, #0x4
    // 0xb5dd44: StoreField: r1->field_b = r0
    //     0xb5dd44: stur            w0, [x1, #0xb]
    // 0xb5dd48: r0 = Stack()
    //     0xb5dd48: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb5dd4c: r1 = Instance_Alignment
    //     0xb5dd4c: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xb5dd50: ldr             x1, [x1, #0xcb0]
    // 0xb5dd54: StoreField: r0->field_f = r1
    //     0xb5dd54: stur            w1, [x0, #0xf]
    // 0xb5dd58: r1 = Instance_StackFit
    //     0xb5dd58: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb5dd5c: ldr             x1, [x1, #0xfa8]
    // 0xb5dd60: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5dd60: stur            w1, [x0, #0x17]
    // 0xb5dd64: r1 = Instance_Clip
    //     0xb5dd64: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb5dd68: ldr             x1, [x1, #0x7e0]
    // 0xb5dd6c: StoreField: r0->field_1b = r1
    //     0xb5dd6c: stur            w1, [x0, #0x1b]
    // 0xb5dd70: ldur            x1, [fp, #-8]
    // 0xb5dd74: StoreField: r0->field_b = r1
    //     0xb5dd74: stur            w1, [x0, #0xb]
    // 0xb5dd78: LeaveFrame
    //     0xb5dd78: mov             SP, fp
    //     0xb5dd7c: ldp             fp, lr, [SP], #0x10
    // 0xb5dd80: ret
    //     0xb5dd80: ret             
    // 0xb5dd84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5dd84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5dd88: b               #0xb5d540
    // 0xb5dd8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5dd8c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5dd90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5dd90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5dd94: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb5dd94: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb5dd98: SaveReg d0
    //     0xb5dd98: str             q0, [SP, #-0x10]!
    // 0xb5dd9c: SaveReg r1
    //     0xb5dd9c: str             x1, [SP, #-8]!
    // 0xb5dda0: r0 = AllocateDouble()
    //     0xb5dda0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb5dda4: RestoreReg r1
    //     0xb5dda4: ldr             x1, [SP], #8
    // 0xb5dda8: RestoreReg d0
    //     0xb5dda8: ldr             q0, [SP], #0x10
    // 0xb5ddac: b               #0xb5d6e0
    // 0xb5ddb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ddb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5ddb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ddb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5ddb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ddb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb5ddbc, size: 0xf0
    // 0xb5ddbc: EnterFrame
    //     0xb5ddbc: stp             fp, lr, [SP, #-0x10]!
    //     0xb5ddc0: mov             fp, SP
    // 0xb5ddc4: AllocStack(0x20)
    //     0xb5ddc4: sub             SP, SP, #0x20
    // 0xb5ddc8: SetupParameters()
    //     0xb5ddc8: ldr             x0, [fp, #0x10]
    //     0xb5ddcc: ldur            w1, [x0, #0x17]
    //     0xb5ddd0: add             x1, x1, HEAP, lsl #32
    // 0xb5ddd4: CheckStackOverflow
    //     0xb5ddd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5ddd8: cmp             SP, x16
    //     0xb5dddc: b.ls            #0xb5de9c
    // 0xb5dde0: LoadField: r0 = r1->field_f
    //     0xb5dde0: ldur            w0, [x1, #0xf]
    // 0xb5dde4: DecompressPointer r0
    //     0xb5dde4: add             x0, x0, HEAP, lsl #32
    // 0xb5dde8: LoadField: r2 = r0->field_b
    //     0xb5dde8: ldur            w2, [x0, #0xb]
    // 0xb5ddec: DecompressPointer r2
    //     0xb5ddec: add             x2, x2, HEAP, lsl #32
    // 0xb5ddf0: cmp             w2, NULL
    // 0xb5ddf4: b.eq            #0xb5dea4
    // 0xb5ddf8: LoadField: r3 = r2->field_b
    //     0xb5ddf8: ldur            w3, [x2, #0xb]
    // 0xb5ddfc: DecompressPointer r3
    //     0xb5ddfc: add             x3, x3, HEAP, lsl #32
    // 0xb5de00: cmp             w3, NULL
    // 0xb5de04: b.ne            #0xb5de10
    // 0xb5de08: r0 = Null
    //     0xb5de08: mov             x0, NULL
    // 0xb5de0c: b               #0xb5de54
    // 0xb5de10: LoadField: r0 = r1->field_13
    //     0xb5de10: ldur            w0, [x1, #0x13]
    // 0xb5de14: DecompressPointer r0
    //     0xb5de14: add             x0, x0, HEAP, lsl #32
    // 0xb5de18: LoadField: r1 = r3->field_b
    //     0xb5de18: ldur            w1, [x3, #0xb]
    // 0xb5de1c: r4 = LoadInt32Instr(r0)
    //     0xb5de1c: sbfx            x4, x0, #1, #0x1f
    //     0xb5de20: tbz             w0, #0, #0xb5de28
    //     0xb5de24: ldur            x4, [x0, #7]
    // 0xb5de28: r0 = LoadInt32Instr(r1)
    //     0xb5de28: sbfx            x0, x1, #1, #0x1f
    // 0xb5de2c: mov             x1, x4
    // 0xb5de30: cmp             x1, x0
    // 0xb5de34: b.hs            #0xb5dea8
    // 0xb5de38: LoadField: r0 = r3->field_f
    //     0xb5de38: ldur            w0, [x3, #0xf]
    // 0xb5de3c: DecompressPointer r0
    //     0xb5de3c: add             x0, x0, HEAP, lsl #32
    // 0xb5de40: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb5de40: add             x16, x0, x4, lsl #2
    //     0xb5de44: ldur            w1, [x16, #0xf]
    // 0xb5de48: DecompressPointer r1
    //     0xb5de48: add             x1, x1, HEAP, lsl #32
    // 0xb5de4c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb5de4c: ldur            w0, [x1, #0x17]
    // 0xb5de50: DecompressPointer r0
    //     0xb5de50: add             x0, x0, HEAP, lsl #32
    // 0xb5de54: LoadField: r1 = r2->field_1b
    //     0xb5de54: ldur            w1, [x2, #0x1b]
    // 0xb5de58: DecompressPointer r1
    //     0xb5de58: add             x1, x1, HEAP, lsl #32
    // 0xb5de5c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb5de5c: ldur            w3, [x2, #0x17]
    // 0xb5de60: DecompressPointer r3
    //     0xb5de60: add             x3, x3, HEAP, lsl #32
    // 0xb5de64: LoadField: r4 = r2->field_23
    //     0xb5de64: ldur            w4, [x2, #0x23]
    // 0xb5de68: DecompressPointer r4
    //     0xb5de68: add             x4, x4, HEAP, lsl #32
    // 0xb5de6c: stp             x0, x4, [SP, #0x10]
    // 0xb5de70: stp             x3, x1, [SP]
    // 0xb5de74: r4 = 0
    //     0xb5de74: movz            x4, #0
    // 0xb5de78: ldr             x0, [SP, #0x18]
    // 0xb5de7c: r16 = UnlinkedCall_0x613b5c
    //     0xb5de7c: add             x16, PP, #0x55, lsl #12  ; [pp+0x55f60] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5de80: add             x16, x16, #0xf60
    // 0xb5de84: ldp             x5, lr, [x16]
    // 0xb5de88: blr             lr
    // 0xb5de8c: r0 = Null
    //     0xb5de8c: mov             x0, NULL
    // 0xb5de90: LeaveFrame
    //     0xb5de90: mov             SP, fp
    //     0xb5de94: ldp             fp, lr, [SP], #0x10
    // 0xb5de98: ret
    //     0xb5de98: ret             
    // 0xb5de9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5de9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5dea0: b               #0xb5dde0
    // 0xb5dea4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5dea4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5dea8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5dea8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String) {
    // ** addr: 0xb5deac, size: 0xa4
    // 0xb5deac: EnterFrame
    //     0xb5deac: stp             fp, lr, [SP, #-0x10]!
    //     0xb5deb0: mov             fp, SP
    // 0xb5deb4: AllocStack(0x40)
    //     0xb5deb4: sub             SP, SP, #0x40
    // 0xb5deb8: SetupParameters()
    //     0xb5deb8: ldr             x0, [fp, #0x48]
    //     0xb5debc: ldur            w1, [x0, #0x17]
    //     0xb5dec0: add             x1, x1, HEAP, lsl #32
    // 0xb5dec4: CheckStackOverflow
    //     0xb5dec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5dec8: cmp             SP, x16
    //     0xb5decc: b.ls            #0xb5df44
    // 0xb5ded0: LoadField: r0 = r1->field_f
    //     0xb5ded0: ldur            w0, [x1, #0xf]
    // 0xb5ded4: DecompressPointer r0
    //     0xb5ded4: add             x0, x0, HEAP, lsl #32
    // 0xb5ded8: LoadField: r1 = r0->field_b
    //     0xb5ded8: ldur            w1, [x0, #0xb]
    // 0xb5dedc: DecompressPointer r1
    //     0xb5dedc: add             x1, x1, HEAP, lsl #32
    // 0xb5dee0: cmp             w1, NULL
    // 0xb5dee4: b.eq            #0xb5df4c
    // 0xb5dee8: LoadField: r0 = r1->field_1f
    //     0xb5dee8: ldur            w0, [x1, #0x1f]
    // 0xb5deec: DecompressPointer r0
    //     0xb5deec: add             x0, x0, HEAP, lsl #32
    // 0xb5def0: ldr             x16, [fp, #0x40]
    // 0xb5def4: stp             x16, x0, [SP, #0x30]
    // 0xb5def8: ldr             x16, [fp, #0x38]
    // 0xb5defc: ldr             lr, [fp, #0x30]
    // 0xb5df00: stp             lr, x16, [SP, #0x20]
    // 0xb5df04: ldr             x16, [fp, #0x28]
    // 0xb5df08: ldr             lr, [fp, #0x20]
    // 0xb5df0c: stp             lr, x16, [SP, #0x10]
    // 0xb5df10: ldr             x16, [fp, #0x18]
    // 0xb5df14: ldr             lr, [fp, #0x10]
    // 0xb5df18: stp             lr, x16, [SP]
    // 0xb5df1c: r4 = 0
    //     0xb5df1c: movz            x4, #0
    // 0xb5df20: ldr             x0, [SP, #0x38]
    // 0xb5df24: r16 = UnlinkedCall_0x613b5c
    //     0xb5df24: add             x16, PP, #0x55, lsl #12  ; [pp+0x55f70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5df28: add             x16, x16, #0xf70
    // 0xb5df2c: ldp             x5, lr, [x16]
    // 0xb5df30: blr             lr
    // 0xb5df34: r0 = Null
    //     0xb5df34: mov             x0, NULL
    // 0xb5df38: LeaveFrame
    //     0xb5df38: mov             SP, fp
    //     0xb5df3c: ldp             fp, lr, [SP], #0x10
    // 0xb5df40: ret
    //     0xb5df40: ret             
    // 0xb5df44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5df44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5df48: b               #0xb5ded0
    // 0xb5df4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5df4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4081, size: 0x38, field offset: 0xc
//   const constructor, 
class BannerCrossLink extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f214, size: 0x24
    // 0xc7f214: EnterFrame
    //     0xc7f214: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f218: mov             fp, SP
    // 0xc7f21c: mov             x0, x1
    // 0xc7f220: r1 = <BannerCrossLink>
    //     0xc7f220: add             x1, PP, #0x48, lsl #12  ; [pp+0x48840] TypeArguments: <BannerCrossLink>
    //     0xc7f224: ldr             x1, [x1, #0x840]
    // 0xc7f228: r0 = _CarouselItemViewState()
    //     0xc7f228: bl              #0xc7f238  ; Allocate_CarouselItemViewStateStub -> _CarouselItemViewState (size=0x14)
    // 0xc7f22c: LeaveFrame
    //     0xc7f22c: mov             SP, fp
    //     0xc7f230: ldp             fp, lr, [SP], #0x10
    // 0xc7f234: ret
    //     0xc7f234: ret             
  }
}
