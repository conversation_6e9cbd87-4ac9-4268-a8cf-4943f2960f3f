// lib: , url: package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_content_text_and_media.dart

// class id: 1049313, size: 0x8
class :: {
}

// class id: 3405, size: 0x14, field offset: 0x14
class _ProductContentTextAndMediaState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb08164, size: 0x13c
    // 0xb08164: EnterFrame
    //     0xb08164: stp             fp, lr, [SP, #-0x10]!
    //     0xb08168: mov             fp, SP
    // 0xb0816c: AllocStack(0x28)
    //     0xb0816c: sub             SP, SP, #0x28
    // 0xb08170: SetupParameters(_ProductContentTextAndMediaState this /* r1 => r1, fp-0x8 */)
    //     0xb08170: stur            x1, [fp, #-8]
    // 0xb08174: CheckStackOverflow
    //     0xb08174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb08178: cmp             SP, x16
    //     0xb0817c: b.ls            #0xb08294
    // 0xb08180: r1 = 1
    //     0xb08180: movz            x1, #0x1
    // 0xb08184: r0 = AllocateContext()
    //     0xb08184: bl              #0x16f6108  ; AllocateContextStub
    // 0xb08188: mov             x1, x0
    // 0xb0818c: ldur            x0, [fp, #-8]
    // 0xb08190: StoreField: r1->field_f = r0
    //     0xb08190: stur            w0, [x1, #0xf]
    // 0xb08194: LoadField: r2 = r0->field_b
    //     0xb08194: ldur            w2, [x0, #0xb]
    // 0xb08198: DecompressPointer r2
    //     0xb08198: add             x2, x2, HEAP, lsl #32
    // 0xb0819c: cmp             w2, NULL
    // 0xb081a0: b.eq            #0xb0829c
    // 0xb081a4: LoadField: r0 = r2->field_b
    //     0xb081a4: ldur            w0, [x2, #0xb]
    // 0xb081a8: DecompressPointer r0
    //     0xb081a8: add             x0, x0, HEAP, lsl #32
    // 0xb081ac: LoadField: r3 = r0->field_b
    //     0xb081ac: ldur            w3, [x0, #0xb]
    // 0xb081b0: mov             x2, x1
    // 0xb081b4: stur            x3, [fp, #-8]
    // 0xb081b8: r1 = Function '<anonymous closure>':.
    //     0xb081b8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d50] AnonymousClosure: (0xb082c4), in [package:customer_app/app/presentation/views/cosmetic/product_detail/widgets/product_content_text_and_media.dart] _ProductContentTextAndMediaState::build (0xb08164)
    //     0xb081bc: ldr             x1, [x1, #0xd50]
    // 0xb081c0: r0 = AllocateClosure()
    //     0xb081c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb081c4: stur            x0, [fp, #-0x10]
    // 0xb081c8: r0 = ListView()
    //     0xb081c8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb081cc: stur            x0, [fp, #-0x18]
    // 0xb081d0: r16 = true
    //     0xb081d0: add             x16, NULL, #0x20  ; true
    // 0xb081d4: r30 = Instance_NeverScrollableScrollPhysics
    //     0xb081d4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb081d8: ldr             lr, [lr, #0x1c8]
    // 0xb081dc: stp             lr, x16, [SP]
    // 0xb081e0: mov             x1, x0
    // 0xb081e4: ldur            x2, [fp, #-0x10]
    // 0xb081e8: ldur            x3, [fp, #-8]
    // 0xb081ec: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xb081ec: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb081f0: ldr             x4, [x4, #8]
    // 0xb081f4: r0 = ListView.builder()
    //     0xb081f4: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb081f8: r1 = Null
    //     0xb081f8: mov             x1, NULL
    // 0xb081fc: r2 = 2
    //     0xb081fc: movz            x2, #0x2
    // 0xb08200: r0 = AllocateArray()
    //     0xb08200: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb08204: mov             x2, x0
    // 0xb08208: ldur            x0, [fp, #-0x18]
    // 0xb0820c: stur            x2, [fp, #-8]
    // 0xb08210: StoreField: r2->field_f = r0
    //     0xb08210: stur            w0, [x2, #0xf]
    // 0xb08214: r1 = <Widget>
    //     0xb08214: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb08218: r0 = AllocateGrowableArray()
    //     0xb08218: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb0821c: mov             x1, x0
    // 0xb08220: ldur            x0, [fp, #-8]
    // 0xb08224: stur            x1, [fp, #-0x10]
    // 0xb08228: StoreField: r1->field_f = r0
    //     0xb08228: stur            w0, [x1, #0xf]
    // 0xb0822c: r0 = 2
    //     0xb0822c: movz            x0, #0x2
    // 0xb08230: StoreField: r1->field_b = r0
    //     0xb08230: stur            w0, [x1, #0xb]
    // 0xb08234: r0 = Column()
    //     0xb08234: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb08238: r1 = Instance_Axis
    //     0xb08238: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb0823c: StoreField: r0->field_f = r1
    //     0xb0823c: stur            w1, [x0, #0xf]
    // 0xb08240: r1 = Instance_MainAxisAlignment
    //     0xb08240: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb08244: ldr             x1, [x1, #0xa08]
    // 0xb08248: StoreField: r0->field_13 = r1
    //     0xb08248: stur            w1, [x0, #0x13]
    // 0xb0824c: r1 = Instance_MainAxisSize
    //     0xb0824c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb08250: ldr             x1, [x1, #0xa10]
    // 0xb08254: ArrayStore: r0[0] = r1  ; List_4
    //     0xb08254: stur            w1, [x0, #0x17]
    // 0xb08258: r1 = Instance_CrossAxisAlignment
    //     0xb08258: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb0825c: ldr             x1, [x1, #0x890]
    // 0xb08260: StoreField: r0->field_1b = r1
    //     0xb08260: stur            w1, [x0, #0x1b]
    // 0xb08264: r1 = Instance_VerticalDirection
    //     0xb08264: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb08268: ldr             x1, [x1, #0xa20]
    // 0xb0826c: StoreField: r0->field_23 = r1
    //     0xb0826c: stur            w1, [x0, #0x23]
    // 0xb08270: r1 = Instance_Clip
    //     0xb08270: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb08274: ldr             x1, [x1, #0x38]
    // 0xb08278: StoreField: r0->field_2b = r1
    //     0xb08278: stur            w1, [x0, #0x2b]
    // 0xb0827c: StoreField: r0->field_2f = rZR
    //     0xb0827c: stur            xzr, [x0, #0x2f]
    // 0xb08280: ldur            x1, [fp, #-0x10]
    // 0xb08284: StoreField: r0->field_b = r1
    //     0xb08284: stur            w1, [x0, #0xb]
    // 0xb08288: LeaveFrame
    //     0xb08288: mov             SP, fp
    //     0xb0828c: ldp             fp, lr, [SP], #0x10
    // 0xb08290: ret
    //     0xb08290: ret             
    // 0xb08294: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb08294: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb08298: b               #0xb08180
    // 0xb0829c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb0829c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Card <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb082c4, size: 0xe38
    // 0xb082c4: EnterFrame
    //     0xb082c4: stp             fp, lr, [SP, #-0x10]!
    //     0xb082c8: mov             fp, SP
    // 0xb082cc: AllocStack(0x78)
    //     0xb082cc: sub             SP, SP, #0x78
    // 0xb082d0: SetupParameters()
    //     0xb082d0: ldr             x0, [fp, #0x20]
    //     0xb082d4: ldur            w1, [x0, #0x17]
    //     0xb082d8: add             x1, x1, HEAP, lsl #32
    //     0xb082dc: stur            x1, [fp, #-8]
    // 0xb082e0: CheckStackOverflow
    //     0xb082e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb082e4: cmp             SP, x16
    //     0xb082e8: b.ls            #0xb0909c
    // 0xb082ec: r0 = Radius()
    //     0xb082ec: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb082f0: d0 = 20.000000
    //     0xb082f0: fmov            d0, #20.00000000
    // 0xb082f4: stur            x0, [fp, #-0x10]
    // 0xb082f8: StoreField: r0->field_7 = d0
    //     0xb082f8: stur            d0, [x0, #7]
    // 0xb082fc: StoreField: r0->field_f = d0
    //     0xb082fc: stur            d0, [x0, #0xf]
    // 0xb08300: r0 = BorderRadius()
    //     0xb08300: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb08304: mov             x1, x0
    // 0xb08308: ldur            x0, [fp, #-0x10]
    // 0xb0830c: stur            x1, [fp, #-0x18]
    // 0xb08310: StoreField: r1->field_7 = r0
    //     0xb08310: stur            w0, [x1, #7]
    // 0xb08314: StoreField: r1->field_b = r0
    //     0xb08314: stur            w0, [x1, #0xb]
    // 0xb08318: StoreField: r1->field_f = r0
    //     0xb08318: stur            w0, [x1, #0xf]
    // 0xb0831c: StoreField: r1->field_13 = r0
    //     0xb0831c: stur            w0, [x1, #0x13]
    // 0xb08320: r0 = RoundedRectangleBorder()
    //     0xb08320: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb08324: mov             x2, x0
    // 0xb08328: ldur            x0, [fp, #-0x18]
    // 0xb0832c: stur            x2, [fp, #-0x10]
    // 0xb08330: StoreField: r2->field_b = r0
    //     0xb08330: stur            w0, [x2, #0xb]
    // 0xb08334: r0 = Instance_BorderSide
    //     0xb08334: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb08338: ldr             x0, [x0, #0xe20]
    // 0xb0833c: StoreField: r2->field_7 = r0
    //     0xb0833c: stur            w0, [x2, #7]
    // 0xb08340: ldur            x3, [fp, #-8]
    // 0xb08344: LoadField: r0 = r3->field_f
    //     0xb08344: ldur            w0, [x3, #0xf]
    // 0xb08348: DecompressPointer r0
    //     0xb08348: add             x0, x0, HEAP, lsl #32
    // 0xb0834c: LoadField: r1 = r0->field_b
    //     0xb0834c: ldur            w1, [x0, #0xb]
    // 0xb08350: DecompressPointer r1
    //     0xb08350: add             x1, x1, HEAP, lsl #32
    // 0xb08354: cmp             w1, NULL
    // 0xb08358: b.eq            #0xb090a4
    // 0xb0835c: LoadField: r4 = r1->field_b
    //     0xb0835c: ldur            w4, [x1, #0xb]
    // 0xb08360: DecompressPointer r4
    //     0xb08360: add             x4, x4, HEAP, lsl #32
    // 0xb08364: LoadField: r0 = r4->field_b
    //     0xb08364: ldur            w0, [x4, #0xb]
    // 0xb08368: ldr             x1, [fp, #0x10]
    // 0xb0836c: r5 = LoadInt32Instr(r1)
    //     0xb0836c: sbfx            x5, x1, #1, #0x1f
    //     0xb08370: tbz             w1, #0, #0xb08378
    //     0xb08374: ldur            x5, [x1, #7]
    // 0xb08378: stur            x5, [fp, #-0x20]
    // 0xb0837c: r1 = LoadInt32Instr(r0)
    //     0xb0837c: sbfx            x1, x0, #1, #0x1f
    // 0xb08380: mov             x0, x1
    // 0xb08384: mov             x1, x5
    // 0xb08388: cmp             x1, x0
    // 0xb0838c: b.hs            #0xb090a8
    // 0xb08390: LoadField: r0 = r4->field_f
    //     0xb08390: ldur            w0, [x4, #0xf]
    // 0xb08394: DecompressPointer r0
    //     0xb08394: add             x0, x0, HEAP, lsl #32
    // 0xb08398: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb08398: add             x16, x0, x5, lsl #2
    //     0xb0839c: ldur            w1, [x16, #0xf]
    // 0xb083a0: DecompressPointer r1
    //     0xb083a0: add             x1, x1, HEAP, lsl #32
    // 0xb083a4: LoadField: r0 = r1->field_27
    //     0xb083a4: ldur            w0, [x1, #0x27]
    // 0xb083a8: DecompressPointer r0
    //     0xb083a8: add             x0, x0, HEAP, lsl #32
    // 0xb083ac: cmp             w0, NULL
    // 0xb083b0: b.ne            #0xb083bc
    // 0xb083b4: r0 = Null
    //     0xb083b4: mov             x0, NULL
    // 0xb083b8: b               #0xb083c8
    // 0xb083bc: LoadField: r1 = r0->field_f
    //     0xb083bc: ldur            w1, [x0, #0xf]
    // 0xb083c0: DecompressPointer r1
    //     0xb083c0: add             x1, x1, HEAP, lsl #32
    // 0xb083c4: mov             x0, x1
    // 0xb083c8: r1 = LoadClassIdInstr(r0)
    //     0xb083c8: ldur            x1, [x0, #-1]
    //     0xb083cc: ubfx            x1, x1, #0xc, #0x14
    // 0xb083d0: r16 = "left"
    //     0xb083d0: ldr             x16, [PP, #0x6df8]  ; [pp+0x6df8] "left"
    // 0xb083d4: stp             x16, x0, [SP]
    // 0xb083d8: mov             x0, x1
    // 0xb083dc: mov             lr, x0
    // 0xb083e0: ldr             lr, [x21, lr, lsl #3]
    // 0xb083e4: blr             lr
    // 0xb083e8: tbnz            w0, #4, #0xb089dc
    // 0xb083ec: ldur            x3, [fp, #-8]
    // 0xb083f0: ldur            x4, [fp, #-0x20]
    // 0xb083f4: LoadField: r0 = r3->field_f
    //     0xb083f4: ldur            w0, [x3, #0xf]
    // 0xb083f8: DecompressPointer r0
    //     0xb083f8: add             x0, x0, HEAP, lsl #32
    // 0xb083fc: LoadField: r1 = r0->field_b
    //     0xb083fc: ldur            w1, [x0, #0xb]
    // 0xb08400: DecompressPointer r1
    //     0xb08400: add             x1, x1, HEAP, lsl #32
    // 0xb08404: cmp             w1, NULL
    // 0xb08408: b.eq            #0xb090ac
    // 0xb0840c: LoadField: r2 = r1->field_b
    //     0xb0840c: ldur            w2, [x1, #0xb]
    // 0xb08410: DecompressPointer r2
    //     0xb08410: add             x2, x2, HEAP, lsl #32
    // 0xb08414: LoadField: r0 = r2->field_b
    //     0xb08414: ldur            w0, [x2, #0xb]
    // 0xb08418: r1 = LoadInt32Instr(r0)
    //     0xb08418: sbfx            x1, x0, #1, #0x1f
    // 0xb0841c: mov             x0, x1
    // 0xb08420: mov             x1, x4
    // 0xb08424: cmp             x1, x0
    // 0xb08428: b.hs            #0xb090b0
    // 0xb0842c: LoadField: r0 = r2->field_f
    //     0xb0842c: ldur            w0, [x2, #0xf]
    // 0xb08430: DecompressPointer r0
    //     0xb08430: add             x0, x0, HEAP, lsl #32
    // 0xb08434: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb08434: add             x16, x0, x4, lsl #2
    //     0xb08438: ldur            w1, [x16, #0xf]
    // 0xb0843c: DecompressPointer r1
    //     0xb0843c: add             x1, x1, HEAP, lsl #32
    // 0xb08440: LoadField: r0 = r1->field_13
    //     0xb08440: ldur            w0, [x1, #0x13]
    // 0xb08444: DecompressPointer r0
    //     0xb08444: add             x0, x0, HEAP, lsl #32
    // 0xb08448: cmp             w0, NULL
    // 0xb0844c: b.ne            #0xb08454
    // 0xb08450: r0 = ""
    //     0xb08450: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb08454: stur            x0, [fp, #-0x18]
    // 0xb08458: r1 = Function '<anonymous closure>':.
    //     0xb08458: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d58] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb0845c: ldr             x1, [x1, #0xd58]
    // 0xb08460: r2 = Null
    //     0xb08460: mov             x2, NULL
    // 0xb08464: r0 = AllocateClosure()
    //     0xb08464: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb08468: r1 = Function '<anonymous closure>':.
    //     0xb08468: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d60] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb0846c: ldr             x1, [x1, #0xd60]
    // 0xb08470: r2 = Null
    //     0xb08470: mov             x2, NULL
    // 0xb08474: stur            x0, [fp, #-0x28]
    // 0xb08478: r0 = AllocateClosure()
    //     0xb08478: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0847c: stur            x0, [fp, #-0x30]
    // 0xb08480: r0 = CachedNetworkImage()
    //     0xb08480: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb08484: stur            x0, [fp, #-0x38]
    // 0xb08488: ldur            x16, [fp, #-0x28]
    // 0xb0848c: ldur            lr, [fp, #-0x30]
    // 0xb08490: stp             lr, x16, [SP, #8]
    // 0xb08494: r16 = Instance_BoxFit
    //     0xb08494: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb08498: ldr             x16, [x16, #0xb18]
    // 0xb0849c: str             x16, [SP]
    // 0xb084a0: mov             x1, x0
    // 0xb084a4: ldur            x2, [fp, #-0x18]
    // 0xb084a8: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xb084a8: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xb084ac: ldr             x4, [x4, #0x790]
    // 0xb084b0: r0 = CachedNetworkImage()
    //     0xb084b0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb084b4: ldur            x2, [fp, #-8]
    // 0xb084b8: LoadField: r0 = r2->field_f
    //     0xb084b8: ldur            w0, [x2, #0xf]
    // 0xb084bc: DecompressPointer r0
    //     0xb084bc: add             x0, x0, HEAP, lsl #32
    // 0xb084c0: LoadField: r1 = r0->field_b
    //     0xb084c0: ldur            w1, [x0, #0xb]
    // 0xb084c4: DecompressPointer r1
    //     0xb084c4: add             x1, x1, HEAP, lsl #32
    // 0xb084c8: cmp             w1, NULL
    // 0xb084cc: b.eq            #0xb090b4
    // 0xb084d0: LoadField: r3 = r1->field_b
    //     0xb084d0: ldur            w3, [x1, #0xb]
    // 0xb084d4: DecompressPointer r3
    //     0xb084d4: add             x3, x3, HEAP, lsl #32
    // 0xb084d8: LoadField: r0 = r3->field_b
    //     0xb084d8: ldur            w0, [x3, #0xb]
    // 0xb084dc: r1 = LoadInt32Instr(r0)
    //     0xb084dc: sbfx            x1, x0, #1, #0x1f
    // 0xb084e0: mov             x0, x1
    // 0xb084e4: ldur            x1, [fp, #-0x20]
    // 0xb084e8: cmp             x1, x0
    // 0xb084ec: b.hs            #0xb090b8
    // 0xb084f0: LoadField: r0 = r3->field_f
    //     0xb084f0: ldur            w0, [x3, #0xf]
    // 0xb084f4: DecompressPointer r0
    //     0xb084f4: add             x0, x0, HEAP, lsl #32
    // 0xb084f8: ldur            x3, [fp, #-0x20]
    // 0xb084fc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xb084fc: add             x16, x0, x3, lsl #2
    //     0xb08500: ldur            w1, [x16, #0xf]
    // 0xb08504: DecompressPointer r1
    //     0xb08504: add             x1, x1, HEAP, lsl #32
    // 0xb08508: LoadField: r0 = r1->field_7
    //     0xb08508: ldur            w0, [x1, #7]
    // 0xb0850c: DecompressPointer r0
    //     0xb0850c: add             x0, x0, HEAP, lsl #32
    // 0xb08510: cmp             w0, NULL
    // 0xb08514: b.ne            #0xb0851c
    // 0xb08518: r0 = ""
    //     0xb08518: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb0851c: ldr             x1, [fp, #0x18]
    // 0xb08520: stur            x0, [fp, #-0x18]
    // 0xb08524: r0 = of()
    //     0xb08524: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb08528: LoadField: r1 = r0->field_87
    //     0xb08528: ldur            w1, [x0, #0x87]
    // 0xb0852c: DecompressPointer r1
    //     0xb0852c: add             x1, x1, HEAP, lsl #32
    // 0xb08530: LoadField: r0 = r1->field_27
    //     0xb08530: ldur            w0, [x1, #0x27]
    // 0xb08534: DecompressPointer r0
    //     0xb08534: add             x0, x0, HEAP, lsl #32
    // 0xb08538: r16 = 21.000000
    //     0xb08538: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb0853c: ldr             x16, [x16, #0x9b0]
    // 0xb08540: r30 = Instance_Color
    //     0xb08540: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb08544: stp             lr, x16, [SP]
    // 0xb08548: mov             x1, x0
    // 0xb0854c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb0854c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb08550: ldr             x4, [x4, #0xaa0]
    // 0xb08554: r0 = copyWith()
    //     0xb08554: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb08558: stur            x0, [fp, #-0x28]
    // 0xb0855c: r0 = Text()
    //     0xb0855c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb08560: mov             x1, x0
    // 0xb08564: ldur            x0, [fp, #-0x18]
    // 0xb08568: stur            x1, [fp, #-0x30]
    // 0xb0856c: StoreField: r1->field_b = r0
    //     0xb0856c: stur            w0, [x1, #0xb]
    // 0xb08570: ldur            x0, [fp, #-0x28]
    // 0xb08574: StoreField: r1->field_13 = r0
    //     0xb08574: stur            w0, [x1, #0x13]
    // 0xb08578: r0 = Padding()
    //     0xb08578: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb0857c: mov             x3, x0
    // 0xb08580: r2 = Instance_EdgeInsets
    //     0xb08580: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d68] Obj!EdgeInsets@d59031
    //     0xb08584: ldr             x2, [x2, #0xd68]
    // 0xb08588: stur            x3, [fp, #-0x28]
    // 0xb0858c: StoreField: r3->field_f = r2
    //     0xb0858c: stur            w2, [x3, #0xf]
    // 0xb08590: ldur            x0, [fp, #-0x30]
    // 0xb08594: StoreField: r3->field_b = r0
    //     0xb08594: stur            w0, [x3, #0xb]
    // 0xb08598: ldur            x2, [fp, #-8]
    // 0xb0859c: LoadField: r0 = r2->field_f
    //     0xb0859c: ldur            w0, [x2, #0xf]
    // 0xb085a0: DecompressPointer r0
    //     0xb085a0: add             x0, x0, HEAP, lsl #32
    // 0xb085a4: LoadField: r1 = r0->field_b
    //     0xb085a4: ldur            w1, [x0, #0xb]
    // 0xb085a8: DecompressPointer r1
    //     0xb085a8: add             x1, x1, HEAP, lsl #32
    // 0xb085ac: cmp             w1, NULL
    // 0xb085b0: b.eq            #0xb090bc
    // 0xb085b4: LoadField: r4 = r1->field_b
    //     0xb085b4: ldur            w4, [x1, #0xb]
    // 0xb085b8: DecompressPointer r4
    //     0xb085b8: add             x4, x4, HEAP, lsl #32
    // 0xb085bc: LoadField: r0 = r4->field_b
    //     0xb085bc: ldur            w0, [x4, #0xb]
    // 0xb085c0: r1 = LoadInt32Instr(r0)
    //     0xb085c0: sbfx            x1, x0, #1, #0x1f
    // 0xb085c4: mov             x0, x1
    // 0xb085c8: ldur            x1, [fp, #-0x20]
    // 0xb085cc: cmp             x1, x0
    // 0xb085d0: b.hs            #0xb090c0
    // 0xb085d4: LoadField: r0 = r4->field_f
    //     0xb085d4: ldur            w0, [x4, #0xf]
    // 0xb085d8: DecompressPointer r0
    //     0xb085d8: add             x0, x0, HEAP, lsl #32
    // 0xb085dc: ldur            x4, [fp, #-0x20]
    // 0xb085e0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb085e0: add             x16, x0, x4, lsl #2
    //     0xb085e4: ldur            w1, [x16, #0xf]
    // 0xb085e8: DecompressPointer r1
    //     0xb085e8: add             x1, x1, HEAP, lsl #32
    // 0xb085ec: LoadField: r0 = r1->field_b
    //     0xb085ec: ldur            w0, [x1, #0xb]
    // 0xb085f0: DecompressPointer r0
    //     0xb085f0: add             x0, x0, HEAP, lsl #32
    // 0xb085f4: cmp             w0, NULL
    // 0xb085f8: b.ne            #0xb08600
    // 0xb085fc: r0 = ""
    //     0xb085fc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb08600: ldr             x1, [fp, #0x18]
    // 0xb08604: stur            x0, [fp, #-0x18]
    // 0xb08608: r0 = of()
    //     0xb08608: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0860c: LoadField: r1 = r0->field_87
    //     0xb0860c: ldur            w1, [x0, #0x87]
    // 0xb08610: DecompressPointer r1
    //     0xb08610: add             x1, x1, HEAP, lsl #32
    // 0xb08614: LoadField: r0 = r1->field_2b
    //     0xb08614: ldur            w0, [x1, #0x2b]
    // 0xb08618: DecompressPointer r0
    //     0xb08618: add             x0, x0, HEAP, lsl #32
    // 0xb0861c: r16 = 14.000000
    //     0xb0861c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb08620: ldr             x16, [x16, #0x1d8]
    // 0xb08624: r30 = Instance_Color
    //     0xb08624: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb08628: stp             lr, x16, [SP]
    // 0xb0862c: mov             x1, x0
    // 0xb08630: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb08630: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb08634: ldr             x4, [x4, #0xaa0]
    // 0xb08638: r0 = copyWith()
    //     0xb08638: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0863c: stur            x0, [fp, #-0x30]
    // 0xb08640: r0 = HtmlWidget()
    //     0xb08640: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xb08644: mov             x1, x0
    // 0xb08648: ldur            x0, [fp, #-0x18]
    // 0xb0864c: stur            x1, [fp, #-0x40]
    // 0xb08650: StoreField: r1->field_1f = r0
    //     0xb08650: stur            w0, [x1, #0x1f]
    // 0xb08654: r3 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xb08654: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xb08658: ldr             x3, [x3, #0x1e0]
    // 0xb0865c: StoreField: r1->field_23 = r3
    //     0xb0865c: stur            w3, [x1, #0x23]
    // 0xb08660: r4 = Instance_ColumnMode
    //     0xb08660: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xb08664: ldr             x4, [x4, #0x1e8]
    // 0xb08668: StoreField: r1->field_3b = r4
    //     0xb08668: stur            w4, [x1, #0x3b]
    // 0xb0866c: ldur            x0, [fp, #-0x30]
    // 0xb08670: StoreField: r1->field_3f = r0
    //     0xb08670: stur            w0, [x1, #0x3f]
    // 0xb08674: r0 = Padding()
    //     0xb08674: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb08678: r5 = Instance_EdgeInsets
    //     0xb08678: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0xb0867c: ldr             x5, [x5, #0xe48]
    // 0xb08680: stur            x0, [fp, #-0x18]
    // 0xb08684: StoreField: r0->field_f = r5
    //     0xb08684: stur            w5, [x0, #0xf]
    // 0xb08688: ldur            x1, [fp, #-0x40]
    // 0xb0868c: StoreField: r0->field_b = r1
    //     0xb0868c: stur            w1, [x0, #0xb]
    // 0xb08690: r0 = Center()
    //     0xb08690: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb08694: mov             x2, x0
    // 0xb08698: r6 = Instance_Alignment
    //     0xb08698: add             x6, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb0869c: ldr             x6, [x6, #0xb10]
    // 0xb086a0: stur            x2, [fp, #-0x30]
    // 0xb086a4: StoreField: r2->field_f = r6
    //     0xb086a4: stur            w6, [x2, #0xf]
    // 0xb086a8: ldur            x0, [fp, #-0x18]
    // 0xb086ac: StoreField: r2->field_b = r0
    //     0xb086ac: stur            w0, [x2, #0xb]
    // 0xb086b0: ldur            x3, [fp, #-8]
    // 0xb086b4: LoadField: r0 = r3->field_f
    //     0xb086b4: ldur            w0, [x3, #0xf]
    // 0xb086b8: DecompressPointer r0
    //     0xb086b8: add             x0, x0, HEAP, lsl #32
    // 0xb086bc: LoadField: r1 = r0->field_b
    //     0xb086bc: ldur            w1, [x0, #0xb]
    // 0xb086c0: DecompressPointer r1
    //     0xb086c0: add             x1, x1, HEAP, lsl #32
    // 0xb086c4: cmp             w1, NULL
    // 0xb086c8: b.eq            #0xb090c4
    // 0xb086cc: LoadField: r4 = r1->field_b
    //     0xb086cc: ldur            w4, [x1, #0xb]
    // 0xb086d0: DecompressPointer r4
    //     0xb086d0: add             x4, x4, HEAP, lsl #32
    // 0xb086d4: LoadField: r0 = r4->field_b
    //     0xb086d4: ldur            w0, [x4, #0xb]
    // 0xb086d8: r1 = LoadInt32Instr(r0)
    //     0xb086d8: sbfx            x1, x0, #1, #0x1f
    // 0xb086dc: mov             x0, x1
    // 0xb086e0: ldur            x1, [fp, #-0x20]
    // 0xb086e4: cmp             x1, x0
    // 0xb086e8: b.hs            #0xb090c8
    // 0xb086ec: LoadField: r0 = r4->field_f
    //     0xb086ec: ldur            w0, [x4, #0xf]
    // 0xb086f0: DecompressPointer r0
    //     0xb086f0: add             x0, x0, HEAP, lsl #32
    // 0xb086f4: ldur            x4, [fp, #-0x20]
    // 0xb086f8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb086f8: add             x16, x0, x4, lsl #2
    //     0xb086fc: ldur            w1, [x16, #0xf]
    // 0xb08700: DecompressPointer r1
    //     0xb08700: add             x1, x1, HEAP, lsl #32
    // 0xb08704: LoadField: r0 = r1->field_f
    //     0xb08704: ldur            w0, [x1, #0xf]
    // 0xb08708: DecompressPointer r0
    //     0xb08708: add             x0, x0, HEAP, lsl #32
    // 0xb0870c: cmp             w0, NULL
    // 0xb08710: b.ne            #0xb0871c
    // 0xb08714: r0 = Null
    //     0xb08714: mov             x0, NULL
    // 0xb08718: b               #0xb08730
    // 0xb0871c: LoadField: r1 = r0->field_7
    //     0xb0871c: ldur            w1, [x0, #7]
    // 0xb08720: cbnz            w1, #0xb0872c
    // 0xb08724: r0 = false
    //     0xb08724: add             x0, NULL, #0x30  ; false
    // 0xb08728: b               #0xb08730
    // 0xb0872c: r0 = true
    //     0xb0872c: add             x0, NULL, #0x20  ; true
    // 0xb08730: cmp             w0, NULL
    // 0xb08734: b.ne            #0xb0873c
    // 0xb08738: r0 = false
    //     0xb08738: add             x0, NULL, #0x30  ; false
    // 0xb0873c: ldr             x1, [fp, #0x18]
    // 0xb08740: stur            x0, [fp, #-0x18]
    // 0xb08744: r0 = of()
    //     0xb08744: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb08748: r17 = 307
    //     0xb08748: movz            x17, #0x133
    // 0xb0874c: ldr             w1, [x0, x17]
    // 0xb08750: DecompressPointer r1
    //     0xb08750: add             x1, x1, HEAP, lsl #32
    // 0xb08754: stur            x1, [fp, #-0x40]
    // 0xb08758: r16 = <RoundedRectangleBorder>
    //     0xb08758: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb0875c: ldr             x16, [x16, #0xf78]
    // 0xb08760: r30 = Instance_RoundedRectangleBorder
    //     0xb08760: add             lr, PP, #0x57, lsl #12  ; [pp+0x57d70] Obj!RoundedRectangleBorder@d5ac31
    //     0xb08764: ldr             lr, [lr, #0xd70]
    // 0xb08768: stp             lr, x16, [SP]
    // 0xb0876c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb0876c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb08770: r0 = all()
    //     0xb08770: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb08774: stur            x0, [fp, #-0x48]
    // 0xb08778: r0 = ButtonStyle()
    //     0xb08778: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb0877c: mov             x2, x0
    // 0xb08780: ldur            x0, [fp, #-0x48]
    // 0xb08784: stur            x2, [fp, #-0x50]
    // 0xb08788: StoreField: r2->field_43 = r0
    //     0xb08788: stur            w0, [x2, #0x43]
    // 0xb0878c: ldur            x7, [fp, #-8]
    // 0xb08790: LoadField: r0 = r7->field_f
    //     0xb08790: ldur            w0, [x7, #0xf]
    // 0xb08794: DecompressPointer r0
    //     0xb08794: add             x0, x0, HEAP, lsl #32
    // 0xb08798: LoadField: r1 = r0->field_b
    //     0xb08798: ldur            w1, [x0, #0xb]
    // 0xb0879c: DecompressPointer r1
    //     0xb0879c: add             x1, x1, HEAP, lsl #32
    // 0xb087a0: cmp             w1, NULL
    // 0xb087a4: b.eq            #0xb090cc
    // 0xb087a8: LoadField: r3 = r1->field_b
    //     0xb087a8: ldur            w3, [x1, #0xb]
    // 0xb087ac: DecompressPointer r3
    //     0xb087ac: add             x3, x3, HEAP, lsl #32
    // 0xb087b0: LoadField: r0 = r3->field_b
    //     0xb087b0: ldur            w0, [x3, #0xb]
    // 0xb087b4: r1 = LoadInt32Instr(r0)
    //     0xb087b4: sbfx            x1, x0, #1, #0x1f
    // 0xb087b8: mov             x0, x1
    // 0xb087bc: ldur            x1, [fp, #-0x20]
    // 0xb087c0: cmp             x1, x0
    // 0xb087c4: b.hs            #0xb090d0
    // 0xb087c8: LoadField: r0 = r3->field_f
    //     0xb087c8: ldur            w0, [x3, #0xf]
    // 0xb087cc: DecompressPointer r0
    //     0xb087cc: add             x0, x0, HEAP, lsl #32
    // 0xb087d0: ldur            x8, [fp, #-0x20]
    // 0xb087d4: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb087d4: add             x16, x0, x8, lsl #2
    //     0xb087d8: ldur            w1, [x16, #0xf]
    // 0xb087dc: DecompressPointer r1
    //     0xb087dc: add             x1, x1, HEAP, lsl #32
    // 0xb087e0: LoadField: r0 = r1->field_f
    //     0xb087e0: ldur            w0, [x1, #0xf]
    // 0xb087e4: DecompressPointer r0
    //     0xb087e4: add             x0, x0, HEAP, lsl #32
    // 0xb087e8: cmp             w0, NULL
    // 0xb087ec: b.ne            #0xb087f8
    // 0xb087f0: r7 = ""
    //     0xb087f0: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb087f4: b               #0xb087fc
    // 0xb087f8: mov             x7, x0
    // 0xb087fc: ldur            x6, [fp, #-0x38]
    // 0xb08800: ldur            x5, [fp, #-0x28]
    // 0xb08804: ldur            x3, [fp, #-0x30]
    // 0xb08808: ldur            x4, [fp, #-0x18]
    // 0xb0880c: ldur            x0, [fp, #-0x40]
    // 0xb08810: ldr             x1, [fp, #0x18]
    // 0xb08814: stur            x7, [fp, #-0x48]
    // 0xb08818: r0 = of()
    //     0xb08818: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb0881c: LoadField: r1 = r0->field_87
    //     0xb0881c: ldur            w1, [x0, #0x87]
    // 0xb08820: DecompressPointer r1
    //     0xb08820: add             x1, x1, HEAP, lsl #32
    // 0xb08824: LoadField: r0 = r1->field_7
    //     0xb08824: ldur            w0, [x1, #7]
    // 0xb08828: DecompressPointer r0
    //     0xb08828: add             x0, x0, HEAP, lsl #32
    // 0xb0882c: r16 = Instance_Color
    //     0xb0882c: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb08830: r30 = 16.000000
    //     0xb08830: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb08834: ldr             lr, [lr, #0x188]
    // 0xb08838: stp             lr, x16, [SP]
    // 0xb0883c: mov             x1, x0
    // 0xb08840: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb08840: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb08844: ldr             x4, [x4, #0x9b8]
    // 0xb08848: r0 = copyWith()
    //     0xb08848: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb0884c: stur            x0, [fp, #-0x58]
    // 0xb08850: r0 = Text()
    //     0xb08850: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb08854: mov             x3, x0
    // 0xb08858: ldur            x0, [fp, #-0x48]
    // 0xb0885c: stur            x3, [fp, #-0x60]
    // 0xb08860: StoreField: r3->field_b = r0
    //     0xb08860: stur            w0, [x3, #0xb]
    // 0xb08864: ldur            x0, [fp, #-0x58]
    // 0xb08868: StoreField: r3->field_13 = r0
    //     0xb08868: stur            w0, [x3, #0x13]
    // 0xb0886c: r1 = Function '<anonymous closure>':.
    //     0xb0886c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d78] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb08870: ldr             x1, [x1, #0xd78]
    // 0xb08874: r2 = Null
    //     0xb08874: mov             x2, NULL
    // 0xb08878: r0 = AllocateClosure()
    //     0xb08878: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb0887c: stur            x0, [fp, #-0x48]
    // 0xb08880: r0 = TextButton()
    //     0xb08880: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb08884: mov             x1, x0
    // 0xb08888: ldur            x0, [fp, #-0x48]
    // 0xb0888c: stur            x1, [fp, #-0x58]
    // 0xb08890: StoreField: r1->field_b = r0
    //     0xb08890: stur            w0, [x1, #0xb]
    // 0xb08894: ldur            x0, [fp, #-0x50]
    // 0xb08898: StoreField: r1->field_1b = r0
    //     0xb08898: stur            w0, [x1, #0x1b]
    // 0xb0889c: r0 = false
    //     0xb0889c: add             x0, NULL, #0x30  ; false
    // 0xb088a0: StoreField: r1->field_27 = r0
    //     0xb088a0: stur            w0, [x1, #0x27]
    // 0xb088a4: r2 = true
    //     0xb088a4: add             x2, NULL, #0x20  ; true
    // 0xb088a8: StoreField: r1->field_2f = r2
    //     0xb088a8: stur            w2, [x1, #0x2f]
    // 0xb088ac: ldur            x3, [fp, #-0x60]
    // 0xb088b0: StoreField: r1->field_37 = r3
    //     0xb088b0: stur            w3, [x1, #0x37]
    // 0xb088b4: r0 = TextButtonTheme()
    //     0xb088b4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb088b8: mov             x1, x0
    // 0xb088bc: ldur            x0, [fp, #-0x40]
    // 0xb088c0: stur            x1, [fp, #-0x48]
    // 0xb088c4: StoreField: r1->field_f = r0
    //     0xb088c4: stur            w0, [x1, #0xf]
    // 0xb088c8: ldur            x0, [fp, #-0x58]
    // 0xb088cc: StoreField: r1->field_b = r0
    //     0xb088cc: stur            w0, [x1, #0xb]
    // 0xb088d0: r0 = Visibility()
    //     0xb088d0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb088d4: mov             x1, x0
    // 0xb088d8: ldur            x0, [fp, #-0x48]
    // 0xb088dc: stur            x1, [fp, #-0x40]
    // 0xb088e0: StoreField: r1->field_b = r0
    //     0xb088e0: stur            w0, [x1, #0xb]
    // 0xb088e4: r9 = Instance_SizedBox
    //     0xb088e4: ldr             x9, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb088e8: StoreField: r1->field_f = r9
    //     0xb088e8: stur            w9, [x1, #0xf]
    // 0xb088ec: ldur            x0, [fp, #-0x18]
    // 0xb088f0: StoreField: r1->field_13 = r0
    //     0xb088f0: stur            w0, [x1, #0x13]
    // 0xb088f4: r10 = false
    //     0xb088f4: add             x10, NULL, #0x30  ; false
    // 0xb088f8: ArrayStore: r1[0] = r10  ; List_4
    //     0xb088f8: stur            w10, [x1, #0x17]
    // 0xb088fc: StoreField: r1->field_1b = r10
    //     0xb088fc: stur            w10, [x1, #0x1b]
    // 0xb08900: StoreField: r1->field_1f = r10
    //     0xb08900: stur            w10, [x1, #0x1f]
    // 0xb08904: StoreField: r1->field_23 = r10
    //     0xb08904: stur            w10, [x1, #0x23]
    // 0xb08908: StoreField: r1->field_27 = r10
    //     0xb08908: stur            w10, [x1, #0x27]
    // 0xb0890c: StoreField: r1->field_2b = r10
    //     0xb0890c: stur            w10, [x1, #0x2b]
    // 0xb08910: r0 = Padding()
    //     0xb08910: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb08914: r11 = Instance_EdgeInsets
    //     0xb08914: add             x11, PP, #0x57, lsl #12  ; [pp+0x57d80] Obj!EdgeInsets@d59001
    //     0xb08918: ldr             x11, [x11, #0xd80]
    // 0xb0891c: stur            x0, [fp, #-0x18]
    // 0xb08920: StoreField: r0->field_f = r11
    //     0xb08920: stur            w11, [x0, #0xf]
    // 0xb08924: ldur            x1, [fp, #-0x40]
    // 0xb08928: StoreField: r0->field_b = r1
    //     0xb08928: stur            w1, [x0, #0xb]
    // 0xb0892c: r1 = Null
    //     0xb0892c: mov             x1, NULL
    // 0xb08930: r2 = 8
    //     0xb08930: movz            x2, #0x8
    // 0xb08934: r0 = AllocateArray()
    //     0xb08934: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb08938: mov             x2, x0
    // 0xb0893c: ldur            x0, [fp, #-0x38]
    // 0xb08940: stur            x2, [fp, #-0x40]
    // 0xb08944: StoreField: r2->field_f = r0
    //     0xb08944: stur            w0, [x2, #0xf]
    // 0xb08948: ldur            x0, [fp, #-0x28]
    // 0xb0894c: StoreField: r2->field_13 = r0
    //     0xb0894c: stur            w0, [x2, #0x13]
    // 0xb08950: ldur            x0, [fp, #-0x30]
    // 0xb08954: ArrayStore: r2[0] = r0  ; List_4
    //     0xb08954: stur            w0, [x2, #0x17]
    // 0xb08958: ldur            x0, [fp, #-0x18]
    // 0xb0895c: StoreField: r2->field_1b = r0
    //     0xb0895c: stur            w0, [x2, #0x1b]
    // 0xb08960: r1 = <Widget>
    //     0xb08960: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb08964: r0 = AllocateGrowableArray()
    //     0xb08964: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb08968: mov             x1, x0
    // 0xb0896c: ldur            x0, [fp, #-0x40]
    // 0xb08970: stur            x1, [fp, #-0x18]
    // 0xb08974: StoreField: r1->field_f = r0
    //     0xb08974: stur            w0, [x1, #0xf]
    // 0xb08978: r12 = 8
    //     0xb08978: movz            x12, #0x8
    // 0xb0897c: StoreField: r1->field_b = r12
    //     0xb0897c: stur            w12, [x1, #0xb]
    // 0xb08980: r0 = Column()
    //     0xb08980: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb08984: r13 = Instance_Axis
    //     0xb08984: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb08988: StoreField: r0->field_f = r13
    //     0xb08988: stur            w13, [x0, #0xf]
    // 0xb0898c: r14 = Instance_MainAxisAlignment
    //     0xb0898c: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb08990: ldr             x14, [x14, #0xa08]
    // 0xb08994: StoreField: r0->field_13 = r14
    //     0xb08994: stur            w14, [x0, #0x13]
    // 0xb08998: r19 = Instance_MainAxisSize
    //     0xb08998: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb0899c: ldr             x19, [x19, #0xa10]
    // 0xb089a0: ArrayStore: r0[0] = r19  ; List_4
    //     0xb089a0: stur            w19, [x0, #0x17]
    // 0xb089a4: r20 = Instance_CrossAxisAlignment
    //     0xb089a4: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb089a8: ldr             x20, [x20, #0x890]
    // 0xb089ac: StoreField: r0->field_1b = r20
    //     0xb089ac: stur            w20, [x0, #0x1b]
    // 0xb089b0: r23 = Instance_VerticalDirection
    //     0xb089b0: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb089b4: ldr             x23, [x23, #0xa20]
    // 0xb089b8: StoreField: r0->field_23 = r23
    //     0xb089b8: stur            w23, [x0, #0x23]
    // 0xb089bc: r24 = Instance_Clip
    //     0xb089bc: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb089c0: ldr             x24, [x24, #0x38]
    // 0xb089c4: StoreField: r0->field_2b = r24
    //     0xb089c4: stur            w24, [x0, #0x2b]
    // 0xb089c8: StoreField: r0->field_2f = rZR
    //     0xb089c8: stur            xzr, [x0, #0x2f]
    // 0xb089cc: ldur            x1, [fp, #-0x18]
    // 0xb089d0: StoreField: r0->field_b = r1
    //     0xb089d0: stur            w1, [x0, #0xb]
    // 0xb089d4: mov             x1, x0
    // 0xb089d8: b               #0xb0903c
    // 0xb089dc: ldur            x7, [fp, #-8]
    // 0xb089e0: ldur            x8, [fp, #-0x20]
    // 0xb089e4: r2 = Instance_EdgeInsets
    //     0xb089e4: add             x2, PP, #0x57, lsl #12  ; [pp+0x57d68] Obj!EdgeInsets@d59031
    //     0xb089e8: ldr             x2, [x2, #0xd68]
    // 0xb089ec: r5 = Instance_EdgeInsets
    //     0xb089ec: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0xb089f0: ldr             x5, [x5, #0xe48]
    // 0xb089f4: r11 = Instance_EdgeInsets
    //     0xb089f4: add             x11, PP, #0x57, lsl #12  ; [pp+0x57d80] Obj!EdgeInsets@d59001
    //     0xb089f8: ldr             x11, [x11, #0xd80]
    // 0xb089fc: r14 = Instance_MainAxisAlignment
    //     0xb089fc: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb08a00: ldr             x14, [x14, #0xa08]
    // 0xb08a04: r20 = Instance_CrossAxisAlignment
    //     0xb08a04: add             x20, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb08a08: ldr             x20, [x20, #0x890]
    // 0xb08a0c: r10 = false
    //     0xb08a0c: add             x10, NULL, #0x30  ; false
    // 0xb08a10: r12 = 8
    //     0xb08a10: movz            x12, #0x8
    // 0xb08a14: r19 = Instance_MainAxisSize
    //     0xb08a14: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb08a18: ldr             x19, [x19, #0xa10]
    // 0xb08a1c: r23 = Instance_VerticalDirection
    //     0xb08a1c: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb08a20: ldr             x23, [x23, #0xa20]
    // 0xb08a24: r13 = Instance_Axis
    //     0xb08a24: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb08a28: r9 = Instance_SizedBox
    //     0xb08a28: ldr             x9, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb08a2c: r24 = Instance_Clip
    //     0xb08a2c: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb08a30: ldr             x24, [x24, #0x38]
    // 0xb08a34: r3 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xb08a34: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xb08a38: ldr             x3, [x3, #0x1e0]
    // 0xb08a3c: r4 = Instance_ColumnMode
    //     0xb08a3c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xb08a40: ldr             x4, [x4, #0x1e8]
    // 0xb08a44: r6 = Instance_Alignment
    //     0xb08a44: add             x6, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb08a48: ldr             x6, [x6, #0xb10]
    // 0xb08a4c: LoadField: r0 = r7->field_f
    //     0xb08a4c: ldur            w0, [x7, #0xf]
    // 0xb08a50: DecompressPointer r0
    //     0xb08a50: add             x0, x0, HEAP, lsl #32
    // 0xb08a54: LoadField: r1 = r0->field_b
    //     0xb08a54: ldur            w1, [x0, #0xb]
    // 0xb08a58: DecompressPointer r1
    //     0xb08a58: add             x1, x1, HEAP, lsl #32
    // 0xb08a5c: cmp             w1, NULL
    // 0xb08a60: b.eq            #0xb090d4
    // 0xb08a64: LoadField: r25 = r1->field_b
    //     0xb08a64: ldur            w25, [x1, #0xb]
    // 0xb08a68: DecompressPointer r25
    //     0xb08a68: add             x25, x25, HEAP, lsl #32
    // 0xb08a6c: LoadField: r0 = r25->field_b
    //     0xb08a6c: ldur            w0, [x25, #0xb]
    // 0xb08a70: r1 = LoadInt32Instr(r0)
    //     0xb08a70: sbfx            x1, x0, #1, #0x1f
    // 0xb08a74: mov             x0, x1
    // 0xb08a78: mov             x1, x8
    // 0xb08a7c: cmp             x1, x0
    // 0xb08a80: b.hs            #0xb090d8
    // 0xb08a84: LoadField: r0 = r25->field_f
    //     0xb08a84: ldur            w0, [x25, #0xf]
    // 0xb08a88: DecompressPointer r0
    //     0xb08a88: add             x0, x0, HEAP, lsl #32
    // 0xb08a8c: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xb08a8c: add             x16, x0, x8, lsl #2
    //     0xb08a90: ldur            w1, [x16, #0xf]
    // 0xb08a94: DecompressPointer r1
    //     0xb08a94: add             x1, x1, HEAP, lsl #32
    // 0xb08a98: LoadField: r0 = r1->field_7
    //     0xb08a98: ldur            w0, [x1, #7]
    // 0xb08a9c: DecompressPointer r0
    //     0xb08a9c: add             x0, x0, HEAP, lsl #32
    // 0xb08aa0: cmp             w0, NULL
    // 0xb08aa4: b.ne            #0xb08aac
    // 0xb08aa8: r0 = ""
    //     0xb08aa8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb08aac: ldr             x1, [fp, #0x18]
    // 0xb08ab0: stur            x0, [fp, #-0x18]
    // 0xb08ab4: r0 = of()
    //     0xb08ab4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb08ab8: LoadField: r1 = r0->field_87
    //     0xb08ab8: ldur            w1, [x0, #0x87]
    // 0xb08abc: DecompressPointer r1
    //     0xb08abc: add             x1, x1, HEAP, lsl #32
    // 0xb08ac0: LoadField: r0 = r1->field_23
    //     0xb08ac0: ldur            w0, [x1, #0x23]
    // 0xb08ac4: DecompressPointer r0
    //     0xb08ac4: add             x0, x0, HEAP, lsl #32
    // 0xb08ac8: r16 = 32.000000
    //     0xb08ac8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb08acc: ldr             x16, [x16, #0x848]
    // 0xb08ad0: r30 = Instance_Color
    //     0xb08ad0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb08ad4: stp             lr, x16, [SP]
    // 0xb08ad8: mov             x1, x0
    // 0xb08adc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb08adc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb08ae0: ldr             x4, [x4, #0xaa0]
    // 0xb08ae4: r0 = copyWith()
    //     0xb08ae4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb08ae8: stur            x0, [fp, #-0x28]
    // 0xb08aec: r0 = Text()
    //     0xb08aec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb08af0: mov             x1, x0
    // 0xb08af4: ldur            x0, [fp, #-0x18]
    // 0xb08af8: stur            x1, [fp, #-0x30]
    // 0xb08afc: StoreField: r1->field_b = r0
    //     0xb08afc: stur            w0, [x1, #0xb]
    // 0xb08b00: ldur            x0, [fp, #-0x28]
    // 0xb08b04: StoreField: r1->field_13 = r0
    //     0xb08b04: stur            w0, [x1, #0x13]
    // 0xb08b08: r0 = Padding()
    //     0xb08b08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb08b0c: mov             x2, x0
    // 0xb08b10: r0 = Instance_EdgeInsets
    //     0xb08b10: add             x0, PP, #0x57, lsl #12  ; [pp+0x57d68] Obj!EdgeInsets@d59031
    //     0xb08b14: ldr             x0, [x0, #0xd68]
    // 0xb08b18: stur            x2, [fp, #-0x28]
    // 0xb08b1c: StoreField: r2->field_f = r0
    //     0xb08b1c: stur            w0, [x2, #0xf]
    // 0xb08b20: ldur            x0, [fp, #-0x30]
    // 0xb08b24: StoreField: r2->field_b = r0
    //     0xb08b24: stur            w0, [x2, #0xb]
    // 0xb08b28: ldur            x3, [fp, #-8]
    // 0xb08b2c: LoadField: r0 = r3->field_f
    //     0xb08b2c: ldur            w0, [x3, #0xf]
    // 0xb08b30: DecompressPointer r0
    //     0xb08b30: add             x0, x0, HEAP, lsl #32
    // 0xb08b34: LoadField: r1 = r0->field_b
    //     0xb08b34: ldur            w1, [x0, #0xb]
    // 0xb08b38: DecompressPointer r1
    //     0xb08b38: add             x1, x1, HEAP, lsl #32
    // 0xb08b3c: cmp             w1, NULL
    // 0xb08b40: b.eq            #0xb090dc
    // 0xb08b44: LoadField: r4 = r1->field_b
    //     0xb08b44: ldur            w4, [x1, #0xb]
    // 0xb08b48: DecompressPointer r4
    //     0xb08b48: add             x4, x4, HEAP, lsl #32
    // 0xb08b4c: LoadField: r0 = r4->field_b
    //     0xb08b4c: ldur            w0, [x4, #0xb]
    // 0xb08b50: r1 = LoadInt32Instr(r0)
    //     0xb08b50: sbfx            x1, x0, #1, #0x1f
    // 0xb08b54: mov             x0, x1
    // 0xb08b58: ldur            x1, [fp, #-0x20]
    // 0xb08b5c: cmp             x1, x0
    // 0xb08b60: b.hs            #0xb090e0
    // 0xb08b64: LoadField: r0 = r4->field_f
    //     0xb08b64: ldur            w0, [x4, #0xf]
    // 0xb08b68: DecompressPointer r0
    //     0xb08b68: add             x0, x0, HEAP, lsl #32
    // 0xb08b6c: ldur            x4, [fp, #-0x20]
    // 0xb08b70: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb08b70: add             x16, x0, x4, lsl #2
    //     0xb08b74: ldur            w1, [x16, #0xf]
    // 0xb08b78: DecompressPointer r1
    //     0xb08b78: add             x1, x1, HEAP, lsl #32
    // 0xb08b7c: LoadField: r0 = r1->field_b
    //     0xb08b7c: ldur            w0, [x1, #0xb]
    // 0xb08b80: DecompressPointer r0
    //     0xb08b80: add             x0, x0, HEAP, lsl #32
    // 0xb08b84: cmp             w0, NULL
    // 0xb08b88: b.ne            #0xb08b90
    // 0xb08b8c: r0 = ""
    //     0xb08b8c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb08b90: ldr             x1, [fp, #0x18]
    // 0xb08b94: stur            x0, [fp, #-0x18]
    // 0xb08b98: r0 = of()
    //     0xb08b98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb08b9c: LoadField: r1 = r0->field_87
    //     0xb08b9c: ldur            w1, [x0, #0x87]
    // 0xb08ba0: DecompressPointer r1
    //     0xb08ba0: add             x1, x1, HEAP, lsl #32
    // 0xb08ba4: LoadField: r0 = r1->field_2b
    //     0xb08ba4: ldur            w0, [x1, #0x2b]
    // 0xb08ba8: DecompressPointer r0
    //     0xb08ba8: add             x0, x0, HEAP, lsl #32
    // 0xb08bac: r16 = 14.000000
    //     0xb08bac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb08bb0: ldr             x16, [x16, #0x1d8]
    // 0xb08bb4: r30 = Instance_Color
    //     0xb08bb4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb08bb8: stp             lr, x16, [SP]
    // 0xb08bbc: mov             x1, x0
    // 0xb08bc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb08bc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb08bc4: ldr             x4, [x4, #0xaa0]
    // 0xb08bc8: r0 = copyWith()
    //     0xb08bc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb08bcc: stur            x0, [fp, #-0x30]
    // 0xb08bd0: r0 = HtmlWidget()
    //     0xb08bd0: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xb08bd4: mov             x1, x0
    // 0xb08bd8: ldur            x0, [fp, #-0x18]
    // 0xb08bdc: stur            x1, [fp, #-0x38]
    // 0xb08be0: StoreField: r1->field_1f = r0
    //     0xb08be0: stur            w0, [x1, #0x1f]
    // 0xb08be4: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xb08be4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xb08be8: ldr             x0, [x0, #0x1e0]
    // 0xb08bec: StoreField: r1->field_23 = r0
    //     0xb08bec: stur            w0, [x1, #0x23]
    // 0xb08bf0: r0 = Instance_ColumnMode
    //     0xb08bf0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xb08bf4: ldr             x0, [x0, #0x1e8]
    // 0xb08bf8: StoreField: r1->field_3b = r0
    //     0xb08bf8: stur            w0, [x1, #0x3b]
    // 0xb08bfc: ldur            x0, [fp, #-0x30]
    // 0xb08c00: StoreField: r1->field_3f = r0
    //     0xb08c00: stur            w0, [x1, #0x3f]
    // 0xb08c04: r0 = Padding()
    //     0xb08c04: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb08c08: mov             x1, x0
    // 0xb08c0c: r0 = Instance_EdgeInsets
    //     0xb08c0c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0xb08c10: ldr             x0, [x0, #0xe48]
    // 0xb08c14: stur            x1, [fp, #-0x18]
    // 0xb08c18: StoreField: r1->field_f = r0
    //     0xb08c18: stur            w0, [x1, #0xf]
    // 0xb08c1c: ldur            x0, [fp, #-0x38]
    // 0xb08c20: StoreField: r1->field_b = r0
    //     0xb08c20: stur            w0, [x1, #0xb]
    // 0xb08c24: r0 = Center()
    //     0xb08c24: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb08c28: mov             x2, x0
    // 0xb08c2c: r0 = Instance_Alignment
    //     0xb08c2c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb08c30: ldr             x0, [x0, #0xb10]
    // 0xb08c34: stur            x2, [fp, #-0x30]
    // 0xb08c38: StoreField: r2->field_f = r0
    //     0xb08c38: stur            w0, [x2, #0xf]
    // 0xb08c3c: ldur            x0, [fp, #-0x18]
    // 0xb08c40: StoreField: r2->field_b = r0
    //     0xb08c40: stur            w0, [x2, #0xb]
    // 0xb08c44: ldur            x3, [fp, #-8]
    // 0xb08c48: LoadField: r0 = r3->field_f
    //     0xb08c48: ldur            w0, [x3, #0xf]
    // 0xb08c4c: DecompressPointer r0
    //     0xb08c4c: add             x0, x0, HEAP, lsl #32
    // 0xb08c50: LoadField: r1 = r0->field_b
    //     0xb08c50: ldur            w1, [x0, #0xb]
    // 0xb08c54: DecompressPointer r1
    //     0xb08c54: add             x1, x1, HEAP, lsl #32
    // 0xb08c58: cmp             w1, NULL
    // 0xb08c5c: b.eq            #0xb090e4
    // 0xb08c60: LoadField: r4 = r1->field_b
    //     0xb08c60: ldur            w4, [x1, #0xb]
    // 0xb08c64: DecompressPointer r4
    //     0xb08c64: add             x4, x4, HEAP, lsl #32
    // 0xb08c68: LoadField: r0 = r4->field_b
    //     0xb08c68: ldur            w0, [x4, #0xb]
    // 0xb08c6c: r1 = LoadInt32Instr(r0)
    //     0xb08c6c: sbfx            x1, x0, #1, #0x1f
    // 0xb08c70: mov             x0, x1
    // 0xb08c74: ldur            x1, [fp, #-0x20]
    // 0xb08c78: cmp             x1, x0
    // 0xb08c7c: b.hs            #0xb090e8
    // 0xb08c80: LoadField: r0 = r4->field_f
    //     0xb08c80: ldur            w0, [x4, #0xf]
    // 0xb08c84: DecompressPointer r0
    //     0xb08c84: add             x0, x0, HEAP, lsl #32
    // 0xb08c88: ldur            x4, [fp, #-0x20]
    // 0xb08c8c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb08c8c: add             x16, x0, x4, lsl #2
    //     0xb08c90: ldur            w1, [x16, #0xf]
    // 0xb08c94: DecompressPointer r1
    //     0xb08c94: add             x1, x1, HEAP, lsl #32
    // 0xb08c98: LoadField: r0 = r1->field_f
    //     0xb08c98: ldur            w0, [x1, #0xf]
    // 0xb08c9c: DecompressPointer r0
    //     0xb08c9c: add             x0, x0, HEAP, lsl #32
    // 0xb08ca0: cmp             w0, NULL
    // 0xb08ca4: b.ne            #0xb08cb0
    // 0xb08ca8: r0 = Null
    //     0xb08ca8: mov             x0, NULL
    // 0xb08cac: b               #0xb08cc4
    // 0xb08cb0: LoadField: r1 = r0->field_7
    //     0xb08cb0: ldur            w1, [x0, #7]
    // 0xb08cb4: cbnz            w1, #0xb08cc0
    // 0xb08cb8: r0 = false
    //     0xb08cb8: add             x0, NULL, #0x30  ; false
    // 0xb08cbc: b               #0xb08cc4
    // 0xb08cc0: r0 = true
    //     0xb08cc0: add             x0, NULL, #0x20  ; true
    // 0xb08cc4: cmp             w0, NULL
    // 0xb08cc8: b.ne            #0xb08cd0
    // 0xb08ccc: r0 = false
    //     0xb08ccc: add             x0, NULL, #0x30  ; false
    // 0xb08cd0: ldr             x1, [fp, #0x18]
    // 0xb08cd4: stur            x0, [fp, #-0x18]
    // 0xb08cd8: r0 = of()
    //     0xb08cd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb08cdc: r17 = 307
    //     0xb08cdc: movz            x17, #0x133
    // 0xb08ce0: ldr             w1, [x0, x17]
    // 0xb08ce4: DecompressPointer r1
    //     0xb08ce4: add             x1, x1, HEAP, lsl #32
    // 0xb08ce8: stur            x1, [fp, #-0x38]
    // 0xb08cec: r16 = <RoundedRectangleBorder>
    //     0xb08cec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb08cf0: ldr             x16, [x16, #0xf78]
    // 0xb08cf4: r30 = Instance_RoundedRectangleBorder
    //     0xb08cf4: add             lr, PP, #0x57, lsl #12  ; [pp+0x57d70] Obj!RoundedRectangleBorder@d5ac31
    //     0xb08cf8: ldr             lr, [lr, #0xd70]
    // 0xb08cfc: stp             lr, x16, [SP]
    // 0xb08d00: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb08d00: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb08d04: r0 = all()
    //     0xb08d04: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb08d08: stur            x0, [fp, #-0x40]
    // 0xb08d0c: r0 = ButtonStyle()
    //     0xb08d0c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb08d10: mov             x2, x0
    // 0xb08d14: ldur            x0, [fp, #-0x40]
    // 0xb08d18: stur            x2, [fp, #-0x48]
    // 0xb08d1c: StoreField: r2->field_43 = r0
    //     0xb08d1c: stur            w0, [x2, #0x43]
    // 0xb08d20: ldur            x3, [fp, #-8]
    // 0xb08d24: LoadField: r0 = r3->field_f
    //     0xb08d24: ldur            w0, [x3, #0xf]
    // 0xb08d28: DecompressPointer r0
    //     0xb08d28: add             x0, x0, HEAP, lsl #32
    // 0xb08d2c: LoadField: r1 = r0->field_b
    //     0xb08d2c: ldur            w1, [x0, #0xb]
    // 0xb08d30: DecompressPointer r1
    //     0xb08d30: add             x1, x1, HEAP, lsl #32
    // 0xb08d34: cmp             w1, NULL
    // 0xb08d38: b.eq            #0xb090ec
    // 0xb08d3c: LoadField: r4 = r1->field_b
    //     0xb08d3c: ldur            w4, [x1, #0xb]
    // 0xb08d40: DecompressPointer r4
    //     0xb08d40: add             x4, x4, HEAP, lsl #32
    // 0xb08d44: LoadField: r0 = r4->field_b
    //     0xb08d44: ldur            w0, [x4, #0xb]
    // 0xb08d48: r1 = LoadInt32Instr(r0)
    //     0xb08d48: sbfx            x1, x0, #1, #0x1f
    // 0xb08d4c: mov             x0, x1
    // 0xb08d50: ldur            x1, [fp, #-0x20]
    // 0xb08d54: cmp             x1, x0
    // 0xb08d58: b.hs            #0xb090f0
    // 0xb08d5c: LoadField: r0 = r4->field_f
    //     0xb08d5c: ldur            w0, [x4, #0xf]
    // 0xb08d60: DecompressPointer r0
    //     0xb08d60: add             x0, x0, HEAP, lsl #32
    // 0xb08d64: ldur            x4, [fp, #-0x20]
    // 0xb08d68: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb08d68: add             x16, x0, x4, lsl #2
    //     0xb08d6c: ldur            w1, [x16, #0xf]
    // 0xb08d70: DecompressPointer r1
    //     0xb08d70: add             x1, x1, HEAP, lsl #32
    // 0xb08d74: LoadField: r0 = r1->field_f
    //     0xb08d74: ldur            w0, [x1, #0xf]
    // 0xb08d78: DecompressPointer r0
    //     0xb08d78: add             x0, x0, HEAP, lsl #32
    // 0xb08d7c: cmp             w0, NULL
    // 0xb08d80: b.ne            #0xb08d8c
    // 0xb08d84: r6 = ""
    //     0xb08d84: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb08d88: b               #0xb08d90
    // 0xb08d8c: mov             x6, x0
    // 0xb08d90: ldur            x5, [fp, #-0x18]
    // 0xb08d94: ldur            x0, [fp, #-0x38]
    // 0xb08d98: ldr             x1, [fp, #0x18]
    // 0xb08d9c: stur            x6, [fp, #-0x40]
    // 0xb08da0: r0 = of()
    //     0xb08da0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb08da4: LoadField: r1 = r0->field_87
    //     0xb08da4: ldur            w1, [x0, #0x87]
    // 0xb08da8: DecompressPointer r1
    //     0xb08da8: add             x1, x1, HEAP, lsl #32
    // 0xb08dac: LoadField: r0 = r1->field_7
    //     0xb08dac: ldur            w0, [x1, #7]
    // 0xb08db0: DecompressPointer r0
    //     0xb08db0: add             x0, x0, HEAP, lsl #32
    // 0xb08db4: r16 = Instance_Color
    //     0xb08db4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb08db8: r30 = 16.000000
    //     0xb08db8: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb08dbc: ldr             lr, [lr, #0x188]
    // 0xb08dc0: stp             lr, x16, [SP]
    // 0xb08dc4: mov             x1, x0
    // 0xb08dc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb08dc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb08dcc: ldr             x4, [x4, #0x9b8]
    // 0xb08dd0: r0 = copyWith()
    //     0xb08dd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb08dd4: stur            x0, [fp, #-0x50]
    // 0xb08dd8: r0 = Text()
    //     0xb08dd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb08ddc: mov             x3, x0
    // 0xb08de0: ldur            x0, [fp, #-0x40]
    // 0xb08de4: stur            x3, [fp, #-0x58]
    // 0xb08de8: StoreField: r3->field_b = r0
    //     0xb08de8: stur            w0, [x3, #0xb]
    // 0xb08dec: ldur            x0, [fp, #-0x50]
    // 0xb08df0: StoreField: r3->field_13 = r0
    //     0xb08df0: stur            w0, [x3, #0x13]
    // 0xb08df4: r1 = Function '<anonymous closure>':.
    //     0xb08df4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d88] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb08df8: ldr             x1, [x1, #0xd88]
    // 0xb08dfc: r2 = Null
    //     0xb08dfc: mov             x2, NULL
    // 0xb08e00: r0 = AllocateClosure()
    //     0xb08e00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb08e04: stur            x0, [fp, #-0x40]
    // 0xb08e08: r0 = TextButton()
    //     0xb08e08: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb08e0c: mov             x1, x0
    // 0xb08e10: ldur            x0, [fp, #-0x40]
    // 0xb08e14: stur            x1, [fp, #-0x50]
    // 0xb08e18: StoreField: r1->field_b = r0
    //     0xb08e18: stur            w0, [x1, #0xb]
    // 0xb08e1c: ldur            x0, [fp, #-0x48]
    // 0xb08e20: StoreField: r1->field_1b = r0
    //     0xb08e20: stur            w0, [x1, #0x1b]
    // 0xb08e24: r0 = false
    //     0xb08e24: add             x0, NULL, #0x30  ; false
    // 0xb08e28: StoreField: r1->field_27 = r0
    //     0xb08e28: stur            w0, [x1, #0x27]
    // 0xb08e2c: r2 = true
    //     0xb08e2c: add             x2, NULL, #0x20  ; true
    // 0xb08e30: StoreField: r1->field_2f = r2
    //     0xb08e30: stur            w2, [x1, #0x2f]
    // 0xb08e34: ldur            x3, [fp, #-0x58]
    // 0xb08e38: StoreField: r1->field_37 = r3
    //     0xb08e38: stur            w3, [x1, #0x37]
    // 0xb08e3c: r0 = TextButtonTheme()
    //     0xb08e3c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb08e40: mov             x1, x0
    // 0xb08e44: ldur            x0, [fp, #-0x38]
    // 0xb08e48: stur            x1, [fp, #-0x40]
    // 0xb08e4c: StoreField: r1->field_f = r0
    //     0xb08e4c: stur            w0, [x1, #0xf]
    // 0xb08e50: ldur            x0, [fp, #-0x50]
    // 0xb08e54: StoreField: r1->field_b = r0
    //     0xb08e54: stur            w0, [x1, #0xb]
    // 0xb08e58: r0 = Visibility()
    //     0xb08e58: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb08e5c: mov             x1, x0
    // 0xb08e60: ldur            x0, [fp, #-0x40]
    // 0xb08e64: stur            x1, [fp, #-0x38]
    // 0xb08e68: StoreField: r1->field_b = r0
    //     0xb08e68: stur            w0, [x1, #0xb]
    // 0xb08e6c: r0 = Instance_SizedBox
    //     0xb08e6c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb08e70: StoreField: r1->field_f = r0
    //     0xb08e70: stur            w0, [x1, #0xf]
    // 0xb08e74: ldur            x0, [fp, #-0x18]
    // 0xb08e78: StoreField: r1->field_13 = r0
    //     0xb08e78: stur            w0, [x1, #0x13]
    // 0xb08e7c: r0 = false
    //     0xb08e7c: add             x0, NULL, #0x30  ; false
    // 0xb08e80: ArrayStore: r1[0] = r0  ; List_4
    //     0xb08e80: stur            w0, [x1, #0x17]
    // 0xb08e84: StoreField: r1->field_1b = r0
    //     0xb08e84: stur            w0, [x1, #0x1b]
    // 0xb08e88: StoreField: r1->field_1f = r0
    //     0xb08e88: stur            w0, [x1, #0x1f]
    // 0xb08e8c: StoreField: r1->field_23 = r0
    //     0xb08e8c: stur            w0, [x1, #0x23]
    // 0xb08e90: StoreField: r1->field_27 = r0
    //     0xb08e90: stur            w0, [x1, #0x27]
    // 0xb08e94: StoreField: r1->field_2b = r0
    //     0xb08e94: stur            w0, [x1, #0x2b]
    // 0xb08e98: r0 = Padding()
    //     0xb08e98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb08e9c: mov             x3, x0
    // 0xb08ea0: r0 = Instance_EdgeInsets
    //     0xb08ea0: add             x0, PP, #0x57, lsl #12  ; [pp+0x57d80] Obj!EdgeInsets@d59001
    //     0xb08ea4: ldr             x0, [x0, #0xd80]
    // 0xb08ea8: stur            x3, [fp, #-0x18]
    // 0xb08eac: StoreField: r3->field_f = r0
    //     0xb08eac: stur            w0, [x3, #0xf]
    // 0xb08eb0: ldur            x0, [fp, #-0x38]
    // 0xb08eb4: StoreField: r3->field_b = r0
    //     0xb08eb4: stur            w0, [x3, #0xb]
    // 0xb08eb8: ldur            x0, [fp, #-8]
    // 0xb08ebc: LoadField: r1 = r0->field_f
    //     0xb08ebc: ldur            w1, [x0, #0xf]
    // 0xb08ec0: DecompressPointer r1
    //     0xb08ec0: add             x1, x1, HEAP, lsl #32
    // 0xb08ec4: LoadField: r0 = r1->field_b
    //     0xb08ec4: ldur            w0, [x1, #0xb]
    // 0xb08ec8: DecompressPointer r0
    //     0xb08ec8: add             x0, x0, HEAP, lsl #32
    // 0xb08ecc: cmp             w0, NULL
    // 0xb08ed0: b.eq            #0xb090f4
    // 0xb08ed4: LoadField: r2 = r0->field_b
    //     0xb08ed4: ldur            w2, [x0, #0xb]
    // 0xb08ed8: DecompressPointer r2
    //     0xb08ed8: add             x2, x2, HEAP, lsl #32
    // 0xb08edc: LoadField: r0 = r2->field_b
    //     0xb08edc: ldur            w0, [x2, #0xb]
    // 0xb08ee0: r1 = LoadInt32Instr(r0)
    //     0xb08ee0: sbfx            x1, x0, #1, #0x1f
    // 0xb08ee4: mov             x0, x1
    // 0xb08ee8: ldur            x1, [fp, #-0x20]
    // 0xb08eec: cmp             x1, x0
    // 0xb08ef0: b.hs            #0xb090f8
    // 0xb08ef4: LoadField: r0 = r2->field_f
    //     0xb08ef4: ldur            w0, [x2, #0xf]
    // 0xb08ef8: DecompressPointer r0
    //     0xb08ef8: add             x0, x0, HEAP, lsl #32
    // 0xb08efc: ldur            x1, [fp, #-0x20]
    // 0xb08f00: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xb08f00: add             x16, x0, x1, lsl #2
    //     0xb08f04: ldur            w2, [x16, #0xf]
    // 0xb08f08: DecompressPointer r2
    //     0xb08f08: add             x2, x2, HEAP, lsl #32
    // 0xb08f0c: LoadField: r0 = r2->field_13
    //     0xb08f0c: ldur            w0, [x2, #0x13]
    // 0xb08f10: DecompressPointer r0
    //     0xb08f10: add             x0, x0, HEAP, lsl #32
    // 0xb08f14: cmp             w0, NULL
    // 0xb08f18: b.ne            #0xb08f24
    // 0xb08f1c: r5 = ""
    //     0xb08f1c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb08f20: b               #0xb08f28
    // 0xb08f24: mov             x5, x0
    // 0xb08f28: ldur            x4, [fp, #-0x28]
    // 0xb08f2c: ldur            x0, [fp, #-0x30]
    // 0xb08f30: stur            x5, [fp, #-8]
    // 0xb08f34: r1 = Function '<anonymous closure>':.
    //     0xb08f34: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d90] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb08f38: ldr             x1, [x1, #0xd90]
    // 0xb08f3c: r2 = Null
    //     0xb08f3c: mov             x2, NULL
    // 0xb08f40: r0 = AllocateClosure()
    //     0xb08f40: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb08f44: r1 = Function '<anonymous closure>':.
    //     0xb08f44: add             x1, PP, #0x57, lsl #12  ; [pp+0x57d98] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb08f48: ldr             x1, [x1, #0xd98]
    // 0xb08f4c: r2 = Null
    //     0xb08f4c: mov             x2, NULL
    // 0xb08f50: stur            x0, [fp, #-0x38]
    // 0xb08f54: r0 = AllocateClosure()
    //     0xb08f54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb08f58: stur            x0, [fp, #-0x40]
    // 0xb08f5c: r0 = CachedNetworkImage()
    //     0xb08f5c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb08f60: stur            x0, [fp, #-0x48]
    // 0xb08f64: ldur            x16, [fp, #-0x38]
    // 0xb08f68: ldur            lr, [fp, #-0x40]
    // 0xb08f6c: stp             lr, x16, [SP, #8]
    // 0xb08f70: r16 = Instance_BoxFit
    //     0xb08f70: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb08f74: ldr             x16, [x16, #0xb18]
    // 0xb08f78: str             x16, [SP]
    // 0xb08f7c: mov             x1, x0
    // 0xb08f80: ldur            x2, [fp, #-8]
    // 0xb08f84: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x3, fit, 0x4, progressIndicatorBuilder, 0x2, null]
    //     0xb08f84: add             x4, PP, #0x55, lsl #12  ; [pp+0x55790] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x3, "fit", 0x4, "progressIndicatorBuilder", 0x2, Null]
    //     0xb08f88: ldr             x4, [x4, #0x790]
    // 0xb08f8c: r0 = CachedNetworkImage()
    //     0xb08f8c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb08f90: r1 = Null
    //     0xb08f90: mov             x1, NULL
    // 0xb08f94: r2 = 8
    //     0xb08f94: movz            x2, #0x8
    // 0xb08f98: r0 = AllocateArray()
    //     0xb08f98: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb08f9c: mov             x2, x0
    // 0xb08fa0: ldur            x0, [fp, #-0x28]
    // 0xb08fa4: stur            x2, [fp, #-8]
    // 0xb08fa8: StoreField: r2->field_f = r0
    //     0xb08fa8: stur            w0, [x2, #0xf]
    // 0xb08fac: ldur            x0, [fp, #-0x30]
    // 0xb08fb0: StoreField: r2->field_13 = r0
    //     0xb08fb0: stur            w0, [x2, #0x13]
    // 0xb08fb4: ldur            x0, [fp, #-0x18]
    // 0xb08fb8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb08fb8: stur            w0, [x2, #0x17]
    // 0xb08fbc: ldur            x0, [fp, #-0x48]
    // 0xb08fc0: StoreField: r2->field_1b = r0
    //     0xb08fc0: stur            w0, [x2, #0x1b]
    // 0xb08fc4: r1 = <Widget>
    //     0xb08fc4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb08fc8: r0 = AllocateGrowableArray()
    //     0xb08fc8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb08fcc: mov             x1, x0
    // 0xb08fd0: ldur            x0, [fp, #-8]
    // 0xb08fd4: stur            x1, [fp, #-0x18]
    // 0xb08fd8: StoreField: r1->field_f = r0
    //     0xb08fd8: stur            w0, [x1, #0xf]
    // 0xb08fdc: r0 = 8
    //     0xb08fdc: movz            x0, #0x8
    // 0xb08fe0: StoreField: r1->field_b = r0
    //     0xb08fe0: stur            w0, [x1, #0xb]
    // 0xb08fe4: r0 = Column()
    //     0xb08fe4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb08fe8: mov             x1, x0
    // 0xb08fec: r0 = Instance_Axis
    //     0xb08fec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb08ff0: StoreField: r1->field_f = r0
    //     0xb08ff0: stur            w0, [x1, #0xf]
    // 0xb08ff4: r0 = Instance_MainAxisAlignment
    //     0xb08ff4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb08ff8: ldr             x0, [x0, #0xa08]
    // 0xb08ffc: StoreField: r1->field_13 = r0
    //     0xb08ffc: stur            w0, [x1, #0x13]
    // 0xb09000: r0 = Instance_MainAxisSize
    //     0xb09000: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb09004: ldr             x0, [x0, #0xa10]
    // 0xb09008: ArrayStore: r1[0] = r0  ; List_4
    //     0xb09008: stur            w0, [x1, #0x17]
    // 0xb0900c: r0 = Instance_CrossAxisAlignment
    //     0xb0900c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb09010: ldr             x0, [x0, #0x890]
    // 0xb09014: StoreField: r1->field_1b = r0
    //     0xb09014: stur            w0, [x1, #0x1b]
    // 0xb09018: r0 = Instance_VerticalDirection
    //     0xb09018: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb0901c: ldr             x0, [x0, #0xa20]
    // 0xb09020: StoreField: r1->field_23 = r0
    //     0xb09020: stur            w0, [x1, #0x23]
    // 0xb09024: r0 = Instance_Clip
    //     0xb09024: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb09028: ldr             x0, [x0, #0x38]
    // 0xb0902c: StoreField: r1->field_2b = r0
    //     0xb0902c: stur            w0, [x1, #0x2b]
    // 0xb09030: StoreField: r1->field_2f = rZR
    //     0xb09030: stur            xzr, [x1, #0x2f]
    // 0xb09034: ldur            x0, [fp, #-0x18]
    // 0xb09038: StoreField: r1->field_b = r0
    //     0xb09038: stur            w0, [x1, #0xb]
    // 0xb0903c: ldur            x0, [fp, #-0x10]
    // 0xb09040: stur            x1, [fp, #-8]
    // 0xb09044: r0 = Card()
    //     0xb09044: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb09048: r1 = 0.000000
    //     0xb09048: ldr             x1, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb0904c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb0904c: stur            w1, [x0, #0x17]
    // 0xb09050: ldur            x1, [fp, #-0x10]
    // 0xb09054: StoreField: r0->field_1b = r1
    //     0xb09054: stur            w1, [x0, #0x1b]
    // 0xb09058: r1 = true
    //     0xb09058: add             x1, NULL, #0x20  ; true
    // 0xb0905c: StoreField: r0->field_1f = r1
    //     0xb0905c: stur            w1, [x0, #0x1f]
    // 0xb09060: r2 = Instance_EdgeInsets
    //     0xb09060: add             x2, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xb09064: ldr             x2, [x2, #0x878]
    // 0xb09068: StoreField: r0->field_27 = r2
    //     0xb09068: stur            w2, [x0, #0x27]
    // 0xb0906c: r2 = Instance_Clip
    //     0xb0906c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb09070: ldr             x2, [x2, #0xb50]
    // 0xb09074: StoreField: r0->field_23 = r2
    //     0xb09074: stur            w2, [x0, #0x23]
    // 0xb09078: ldur            x2, [fp, #-8]
    // 0xb0907c: StoreField: r0->field_2f = r2
    //     0xb0907c: stur            w2, [x0, #0x2f]
    // 0xb09080: StoreField: r0->field_2b = r1
    //     0xb09080: stur            w1, [x0, #0x2b]
    // 0xb09084: r1 = Instance__CardVariant
    //     0xb09084: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb09088: ldr             x1, [x1, #0xa68]
    // 0xb0908c: StoreField: r0->field_33 = r1
    //     0xb0908c: stur            w1, [x0, #0x33]
    // 0xb09090: LeaveFrame
    //     0xb09090: mov             SP, fp
    //     0xb09094: ldp             fp, lr, [SP], #0x10
    // 0xb09098: ret
    //     0xb09098: ret             
    // 0xb0909c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb0909c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb090a0: b               #0xb082ec
    // 0xb090a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090d0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090d8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090f0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb090f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb090f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb090f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb090f8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4142, size: 0x10, field offset: 0xc
//   const constructor, 
class ProductContentTextAndMedia extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7df80, size: 0x24
    // 0xc7df80: EnterFrame
    //     0xc7df80: stp             fp, lr, [SP, #-0x10]!
    //     0xc7df84: mov             fp, SP
    // 0xc7df88: mov             x0, x1
    // 0xc7df8c: r1 = <ProductContentTextAndMedia>
    //     0xc7df8c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48b38] TypeArguments: <ProductContentTextAndMedia>
    //     0xc7df90: ldr             x1, [x1, #0xb38]
    // 0xc7df94: r0 = _ProductContentTextAndMediaState()
    //     0xc7df94: bl              #0xc7dfa4  ; Allocate_ProductContentTextAndMediaStateStub -> _ProductContentTextAndMediaState (size=0x14)
    // 0xc7df98: LeaveFrame
    //     0xc7df98: mov             SP, fp
    //     0xc7df9c: ldp             fp, lr, [SP], #0x10
    // 0xc7dfa0: ret
    //     0xc7dfa0: ret             
  }
}
