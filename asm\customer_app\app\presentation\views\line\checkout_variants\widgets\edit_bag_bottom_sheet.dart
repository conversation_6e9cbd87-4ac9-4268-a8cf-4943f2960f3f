// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart

// class id: 1049493, size: 0x8
class :: {
}

// class id: 3271, size: 0x24, field offset: 0x14
class _EditBagBottomSheetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x9480bc, size: 0x26c
    // 0x9480bc: EnterFrame
    //     0x9480bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9480c0: mov             fp, SP
    // 0x9480c4: AllocStack(0x28)
    //     0x9480c4: sub             SP, SP, #0x28
    // 0x9480c8: SetupParameters(_EditBagBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x9480c8: stur            x1, [fp, #-8]
    // 0x9480cc: CheckStackOverflow
    //     0x9480cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9480d0: cmp             SP, x16
    //     0x9480d4: b.ls            #0x94831c
    // 0x9480d8: r1 = 1
    //     0x9480d8: movz            x1, #0x1
    // 0x9480dc: r0 = AllocateContext()
    //     0x9480dc: bl              #0x16f6108  ; AllocateContextStub
    // 0x9480e0: mov             x2, x0
    // 0x9480e4: ldur            x0, [fp, #-8]
    // 0x9480e8: stur            x2, [fp, #-0x10]
    // 0x9480ec: StoreField: r2->field_f = r0
    //     0x9480ec: stur            w0, [x2, #0xf]
    // 0x9480f0: LoadField: r1 = r0->field_1f
    //     0x9480f0: ldur            w1, [x0, #0x1f]
    // 0x9480f4: DecompressPointer r1
    //     0x9480f4: add             x1, x1, HEAP, lsl #32
    // 0x9480f8: LoadField: r3 = r1->field_8f
    //     0x9480f8: ldur            w3, [x1, #0x8f]
    // 0x9480fc: DecompressPointer r3
    //     0x9480fc: add             x3, x3, HEAP, lsl #32
    // 0x948100: mov             x1, x3
    // 0x948104: r0 = value()
    //     0x948104: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x948108: LoadField: r3 = r0->field_53
    //     0x948108: ldur            w3, [x0, #0x53]
    // 0x94810c: DecompressPointer r3
    //     0x94810c: add             x3, x3, HEAP, lsl #32
    // 0x948110: stur            x3, [fp, #-0x18]
    // 0x948114: cmp             w3, NULL
    // 0x948118: b.ne            #0x948124
    // 0x94811c: r2 = Null
    //     0x94811c: mov             x2, NULL
    // 0x948120: b               #0x948164
    // 0x948124: ldur            x2, [fp, #-0x10]
    // 0x948128: r1 = Function '<anonymous closure>':.
    //     0x948128: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c430] AnonymousClosure: (0x906488), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::initState (0x9408ac)
    //     0x94812c: ldr             x1, [x1, #0x430]
    // 0x948130: r0 = AllocateClosure()
    //     0x948130: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x948134: r1 = Function '<anonymous closure>':.
    //     0x948134: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c438] AnonymousClosure: (0x90644c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::initState (0x9408ac)
    //     0x948138: ldr             x1, [x1, #0x438]
    // 0x94813c: r2 = Null
    //     0x94813c: mov             x2, NULL
    // 0x948140: stur            x0, [fp, #-0x10]
    // 0x948144: r0 = AllocateClosure()
    //     0x948144: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x948148: str             x0, [SP]
    // 0x94814c: ldur            x1, [fp, #-0x18]
    // 0x948150: ldur            x2, [fp, #-0x10]
    // 0x948154: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x948154: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x948158: ldr             x4, [x4, #0xb48]
    // 0x94815c: r0 = firstWhere()
    //     0x94815c: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x948160: mov             x2, x0
    // 0x948164: ldur            x0, [fp, #-8]
    // 0x948168: stur            x2, [fp, #-0x10]
    // 0x94816c: LoadField: r1 = r0->field_1f
    //     0x94816c: ldur            w1, [x0, #0x1f]
    // 0x948170: DecompressPointer r1
    //     0x948170: add             x1, x1, HEAP, lsl #32
    // 0x948174: LoadField: r3 = r1->field_8f
    //     0x948174: ldur            w3, [x1, #0x8f]
    // 0x948178: DecompressPointer r3
    //     0x948178: add             x3, x3, HEAP, lsl #32
    // 0x94817c: mov             x1, x3
    // 0x948180: r0 = value()
    //     0x948180: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x948184: LoadField: r1 = r0->field_53
    //     0x948184: ldur            w1, [x0, #0x53]
    // 0x948188: DecompressPointer r1
    //     0x948188: add             x1, x1, HEAP, lsl #32
    // 0x94818c: stur            x1, [fp, #-0x18]
    // 0x948190: cmp             w1, NULL
    // 0x948194: b.ne            #0x9481a0
    // 0x948198: r0 = Null
    //     0x948198: mov             x0, NULL
    // 0x94819c: b               #0x9481e0
    // 0x9481a0: ldur            x0, [fp, #-0x10]
    // 0x9481a4: cmp             w0, NULL
    // 0x9481a8: b.ne            #0x9481b8
    // 0x9481ac: r0 = SkuDetails()
    //     0x9481ac: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0x9481b0: mov             x2, x0
    // 0x9481b4: b               #0x9481bc
    // 0x9481b8: mov             x2, x0
    // 0x9481bc: ldur            x1, [fp, #-0x18]
    // 0x9481c0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9481c0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9481c4: r0 = indexOf()
    //     0x9481c4: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x9481c8: mov             x2, x0
    // 0x9481cc: r0 = BoxInt64Instr(r2)
    //     0x9481cc: sbfiz           x0, x2, #1, #0x1f
    //     0x9481d0: cmp             x2, x0, asr #1
    //     0x9481d4: b.eq            #0x9481e0
    //     0x9481d8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9481dc: stur            x2, [x0, #7]
    // 0x9481e0: cmp             w0, NULL
    // 0x9481e4: b.ne            #0x9481f0
    // 0x9481e8: r1 = 0
    //     0x9481e8: movz            x1, #0
    // 0x9481ec: b               #0x9481fc
    // 0x9481f0: r1 = LoadInt32Instr(r0)
    //     0x9481f0: sbfx            x1, x0, #1, #0x1f
    //     0x9481f4: tbz             w0, #0, #0x9481fc
    //     0x9481f8: ldur            x1, [x0, #7]
    // 0x9481fc: ldur            x0, [fp, #-8]
    // 0x948200: ArrayStore: r0[0] = r1  ; List_8
    //     0x948200: stur            x1, [x0, #0x17]
    // 0x948204: LoadField: r2 = r0->field_13
    //     0x948204: ldur            w2, [x0, #0x13]
    // 0x948208: DecompressPointer r2
    //     0x948208: add             x2, x2, HEAP, lsl #32
    // 0x94820c: stur            x2, [fp, #-0x10]
    // 0x948210: LoadField: r1 = r0->field_1f
    //     0x948210: ldur            w1, [x0, #0x1f]
    // 0x948214: DecompressPointer r1
    //     0x948214: add             x1, x1, HEAP, lsl #32
    // 0x948218: LoadField: r3 = r1->field_8f
    //     0x948218: ldur            w3, [x1, #0x8f]
    // 0x94821c: DecompressPointer r3
    //     0x94821c: add             x3, x3, HEAP, lsl #32
    // 0x948220: mov             x1, x3
    // 0x948224: r0 = value()
    //     0x948224: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x948228: LoadField: r2 = r0->field_53
    //     0x948228: ldur            w2, [x0, #0x53]
    // 0x94822c: DecompressPointer r2
    //     0x94822c: add             x2, x2, HEAP, lsl #32
    // 0x948230: cmp             w2, NULL
    // 0x948234: b.ne            #0x948240
    // 0x948238: r0 = Null
    //     0x948238: mov             x0, NULL
    // 0x94823c: b               #0x948278
    // 0x948240: ldur            x0, [fp, #-8]
    // 0x948244: ArrayLoad: r3 = r0[0]  ; List_8
    //     0x948244: ldur            x3, [x0, #0x17]
    // 0x948248: LoadField: r0 = r2->field_b
    //     0x948248: ldur            w0, [x2, #0xb]
    // 0x94824c: r1 = LoadInt32Instr(r0)
    //     0x94824c: sbfx            x1, x0, #1, #0x1f
    // 0x948250: mov             x0, x1
    // 0x948254: mov             x1, x3
    // 0x948258: cmp             x1, x0
    // 0x94825c: b.hs            #0x948324
    // 0x948260: LoadField: r0 = r2->field_f
    //     0x948260: ldur            w0, [x2, #0xf]
    // 0x948264: DecompressPointer r0
    //     0x948264: add             x0, x0, HEAP, lsl #32
    // 0x948268: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x948268: add             x16, x0, x3, lsl #2
    //     0x94826c: ldur            w1, [x16, #0xf]
    // 0x948270: DecompressPointer r1
    //     0x948270: add             x1, x1, HEAP, lsl #32
    // 0x948274: mov             x0, x1
    // 0x948278: cmp             w0, NULL
    // 0x94827c: b.ne            #0x94828c
    // 0x948280: r0 = SkuDetails()
    //     0x948280: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0x948284: mov             x2, x0
    // 0x948288: b               #0x948290
    // 0x94828c: mov             x2, x0
    // 0x948290: ldur            x0, [fp, #-0x10]
    // 0x948294: stur            x2, [fp, #-8]
    // 0x948298: LoadField: r1 = r0->field_b
    //     0x948298: ldur            w1, [x0, #0xb]
    // 0x94829c: LoadField: r3 = r0->field_f
    //     0x94829c: ldur            w3, [x0, #0xf]
    // 0x9482a0: DecompressPointer r3
    //     0x9482a0: add             x3, x3, HEAP, lsl #32
    // 0x9482a4: LoadField: r4 = r3->field_b
    //     0x9482a4: ldur            w4, [x3, #0xb]
    // 0x9482a8: r3 = LoadInt32Instr(r1)
    //     0x9482a8: sbfx            x3, x1, #1, #0x1f
    // 0x9482ac: stur            x3, [fp, #-0x20]
    // 0x9482b0: r1 = LoadInt32Instr(r4)
    //     0x9482b0: sbfx            x1, x4, #1, #0x1f
    // 0x9482b4: cmp             x3, x1
    // 0x9482b8: b.ne            #0x9482c4
    // 0x9482bc: mov             x1, x0
    // 0x9482c0: r0 = _growToNextCapacity()
    //     0x9482c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9482c4: ldur            x2, [fp, #-0x10]
    // 0x9482c8: ldur            x3, [fp, #-0x20]
    // 0x9482cc: add             x4, x3, #1
    // 0x9482d0: lsl             x5, x4, #1
    // 0x9482d4: StoreField: r2->field_b = r5
    //     0x9482d4: stur            w5, [x2, #0xb]
    // 0x9482d8: LoadField: r1 = r2->field_f
    //     0x9482d8: ldur            w1, [x2, #0xf]
    // 0x9482dc: DecompressPointer r1
    //     0x9482dc: add             x1, x1, HEAP, lsl #32
    // 0x9482e0: ldur            x0, [fp, #-8]
    // 0x9482e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9482e4: add             x25, x1, x3, lsl #2
    //     0x9482e8: add             x25, x25, #0xf
    //     0x9482ec: str             w0, [x25]
    //     0x9482f0: tbz             w0, #0, #0x94830c
    //     0x9482f4: ldurb           w16, [x1, #-1]
    //     0x9482f8: ldurb           w17, [x0, #-1]
    //     0x9482fc: and             x16, x17, x16, lsr #2
    //     0x948300: tst             x16, HEAP, lsr #32
    //     0x948304: b.eq            #0x94830c
    //     0x948308: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x94830c: r0 = Null
    //     0x94830c: mov             x0, NULL
    // 0x948310: LeaveFrame
    //     0x948310: mov             SP, fp
    //     0x948314: ldp             fp, lr, [SP], #0x10
    // 0x948318: ret
    //     0x948318: ret             
    // 0x94831c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94831c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x948320: b               #0x9480d8
    // 0x948324: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x948324: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbb9540, size: 0x64
    // 0xbb9540: EnterFrame
    //     0xbb9540: stp             fp, lr, [SP, #-0x10]!
    //     0xbb9544: mov             fp, SP
    // 0xbb9548: AllocStack(0x18)
    //     0xbb9548: sub             SP, SP, #0x18
    // 0xbb954c: SetupParameters(_EditBagBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbb954c: stur            x1, [fp, #-8]
    //     0xbb9550: stur            x2, [fp, #-0x10]
    // 0xbb9554: r1 = 2
    //     0xbb9554: movz            x1, #0x2
    // 0xbb9558: r0 = AllocateContext()
    //     0xbb9558: bl              #0x16f6108  ; AllocateContextStub
    // 0xbb955c: mov             x1, x0
    // 0xbb9560: ldur            x0, [fp, #-8]
    // 0xbb9564: stur            x1, [fp, #-0x18]
    // 0xbb9568: StoreField: r1->field_f = r0
    //     0xbb9568: stur            w0, [x1, #0xf]
    // 0xbb956c: ldur            x0, [fp, #-0x10]
    // 0xbb9570: StoreField: r1->field_13 = r0
    //     0xbb9570: stur            w0, [x1, #0x13]
    // 0xbb9574: r0 = Obx()
    //     0xbb9574: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbb9578: ldur            x2, [fp, #-0x18]
    // 0xbb957c: r1 = Function '<anonymous closure>':.
    //     0xbb957c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c388] AnonymousClosure: (0xbb95a4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbb9580: ldr             x1, [x1, #0x388]
    // 0xbb9584: stur            x0, [fp, #-8]
    // 0xbb9588: r0 = AllocateClosure()
    //     0xbb9588: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb958c: mov             x1, x0
    // 0xbb9590: ldur            x0, [fp, #-8]
    // 0xbb9594: StoreField: r0->field_b = r1
    //     0xbb9594: stur            w1, [x0, #0xb]
    // 0xbb9598: LeaveFrame
    //     0xbb9598: mov             SP, fp
    //     0xbb959c: ldp             fp, lr, [SP], #0x10
    // 0xbb95a0: ret
    //     0xbb95a0: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0xbb95a4, size: 0x1ea8
    // 0xbb95a4: EnterFrame
    //     0xbb95a4: stp             fp, lr, [SP, #-0x10]!
    //     0xbb95a8: mov             fp, SP
    // 0xbb95ac: AllocStack(0x80)
    //     0xbb95ac: sub             SP, SP, #0x80
    // 0xbb95b0: SetupParameters()
    //     0xbb95b0: ldr             x0, [fp, #0x10]
    //     0xbb95b4: ldur            w2, [x0, #0x17]
    //     0xbb95b8: add             x2, x2, HEAP, lsl #32
    //     0xbb95bc: stur            x2, [fp, #-8]
    // 0xbb95c0: CheckStackOverflow
    //     0xbb95c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbb95c4: cmp             SP, x16
    //     0xbb95c8: b.ls            #0xbbb41c
    // 0xbb95cc: LoadField: r1 = r2->field_13
    //     0xbb95cc: ldur            w1, [x2, #0x13]
    // 0xbb95d0: DecompressPointer r1
    //     0xbb95d0: add             x1, x1, HEAP, lsl #32
    // 0xbb95d4: r0 = of()
    //     0xbb95d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb95d8: LoadField: r1 = r0->field_5b
    //     0xbb95d8: ldur            w1, [x0, #0x5b]
    // 0xbb95dc: DecompressPointer r1
    //     0xbb95dc: add             x1, x1, HEAP, lsl #32
    // 0xbb95e0: stur            x1, [fp, #-0x10]
    // 0xbb95e4: r0 = ColorFilter()
    //     0xbb95e4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbb95e8: mov             x1, x0
    // 0xbb95ec: ldur            x0, [fp, #-0x10]
    // 0xbb95f0: stur            x1, [fp, #-0x18]
    // 0xbb95f4: StoreField: r1->field_7 = r0
    //     0xbb95f4: stur            w0, [x1, #7]
    // 0xbb95f8: r0 = Instance_BlendMode
    //     0xbb95f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbb95fc: ldr             x0, [x0, #0xb30]
    // 0xbb9600: StoreField: r1->field_b = r0
    //     0xbb9600: stur            w0, [x1, #0xb]
    // 0xbb9604: r2 = 1
    //     0xbb9604: movz            x2, #0x1
    // 0xbb9608: StoreField: r1->field_13 = r2
    //     0xbb9608: stur            x2, [x1, #0x13]
    // 0xbb960c: r0 = SvgPicture()
    //     0xbb960c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbb9610: stur            x0, [fp, #-0x10]
    // 0xbb9614: ldur            x16, [fp, #-0x18]
    // 0xbb9618: str             x16, [SP]
    // 0xbb961c: mov             x1, x0
    // 0xbb9620: r2 = "assets/images/x.svg"
    //     0xbb9620: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbb9624: ldr             x2, [x2, #0x5e8]
    // 0xbb9628: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbb9628: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbb962c: ldr             x4, [x4, #0xa38]
    // 0xbb9630: r0 = SvgPicture.asset()
    //     0xbb9630: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbb9634: r0 = Align()
    //     0xbb9634: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbb9638: mov             x1, x0
    // 0xbb963c: r0 = Instance_Alignment
    //     0xbb963c: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xbb9640: ldr             x0, [x0, #0xa78]
    // 0xbb9644: stur            x1, [fp, #-0x18]
    // 0xbb9648: StoreField: r1->field_f = r0
    //     0xbb9648: stur            w0, [x1, #0xf]
    // 0xbb964c: ldur            x0, [fp, #-0x10]
    // 0xbb9650: StoreField: r1->field_b = r0
    //     0xbb9650: stur            w0, [x1, #0xb]
    // 0xbb9654: r0 = InkWell()
    //     0xbb9654: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbb9658: mov             x3, x0
    // 0xbb965c: ldur            x0, [fp, #-0x18]
    // 0xbb9660: stur            x3, [fp, #-0x10]
    // 0xbb9664: StoreField: r3->field_b = r0
    //     0xbb9664: stur            w0, [x3, #0xb]
    // 0xbb9668: ldur            x2, [fp, #-8]
    // 0xbb966c: r1 = Function '<anonymous closure>':.
    //     0xbb966c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c390] AnonymousClosure: (0xbbbaf0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbb9670: ldr             x1, [x1, #0x390]
    // 0xbb9674: r0 = AllocateClosure()
    //     0xbb9674: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb9678: mov             x1, x0
    // 0xbb967c: ldur            x0, [fp, #-0x10]
    // 0xbb9680: StoreField: r0->field_f = r1
    //     0xbb9680: stur            w1, [x0, #0xf]
    // 0xbb9684: r1 = true
    //     0xbb9684: add             x1, NULL, #0x20  ; true
    // 0xbb9688: StoreField: r0->field_43 = r1
    //     0xbb9688: stur            w1, [x0, #0x43]
    // 0xbb968c: r2 = Instance_BoxShape
    //     0xbb968c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbb9690: ldr             x2, [x2, #0x80]
    // 0xbb9694: StoreField: r0->field_47 = r2
    //     0xbb9694: stur            w2, [x0, #0x47]
    // 0xbb9698: StoreField: r0->field_6f = r1
    //     0xbb9698: stur            w1, [x0, #0x6f]
    // 0xbb969c: r3 = false
    //     0xbb969c: add             x3, NULL, #0x30  ; false
    // 0xbb96a0: StoreField: r0->field_73 = r3
    //     0xbb96a0: stur            w3, [x0, #0x73]
    // 0xbb96a4: StoreField: r0->field_83 = r1
    //     0xbb96a4: stur            w1, [x0, #0x83]
    // 0xbb96a8: StoreField: r0->field_7b = r3
    //     0xbb96a8: stur            w3, [x0, #0x7b]
    // 0xbb96ac: r0 = Padding()
    //     0xbb96ac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb96b0: mov             x3, x0
    // 0xbb96b4: r0 = Instance_EdgeInsets
    //     0xbb96b4: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xbb96b8: ldr             x0, [x0, #0x240]
    // 0xbb96bc: stur            x3, [fp, #-0x18]
    // 0xbb96c0: StoreField: r3->field_f = r0
    //     0xbb96c0: stur            w0, [x3, #0xf]
    // 0xbb96c4: ldur            x0, [fp, #-0x10]
    // 0xbb96c8: StoreField: r3->field_b = r0
    //     0xbb96c8: stur            w0, [x3, #0xb]
    // 0xbb96cc: r1 = <Widget>
    //     0xbb96cc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb96d0: r2 = 18
    //     0xbb96d0: movz            x2, #0x12
    // 0xbb96d4: r0 = AllocateArray()
    //     0xbb96d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb96d8: mov             x2, x0
    // 0xbb96dc: ldur            x0, [fp, #-0x18]
    // 0xbb96e0: stur            x2, [fp, #-0x10]
    // 0xbb96e4: StoreField: r2->field_f = r0
    //     0xbb96e4: stur            w0, [x2, #0xf]
    // 0xbb96e8: ldur            x0, [fp, #-8]
    // 0xbb96ec: LoadField: r1 = r0->field_f
    //     0xbb96ec: ldur            w1, [x0, #0xf]
    // 0xbb96f0: DecompressPointer r1
    //     0xbb96f0: add             x1, x1, HEAP, lsl #32
    // 0xbb96f4: LoadField: r3 = r1->field_1f
    //     0xbb96f4: ldur            w3, [x1, #0x1f]
    // 0xbb96f8: DecompressPointer r3
    //     0xbb96f8: add             x3, x3, HEAP, lsl #32
    // 0xbb96fc: LoadField: r1 = r3->field_8f
    //     0xbb96fc: ldur            w1, [x3, #0x8f]
    // 0xbb9700: DecompressPointer r1
    //     0xbb9700: add             x1, x1, HEAP, lsl #32
    // 0xbb9704: r0 = value()
    //     0xbb9704: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb9708: LoadField: r1 = r0->field_13
    //     0xbb9708: ldur            w1, [x0, #0x13]
    // 0xbb970c: DecompressPointer r1
    //     0xbb970c: add             x1, x1, HEAP, lsl #32
    // 0xbb9710: cmp             w1, NULL
    // 0xbb9714: b.ne            #0xbb9720
    // 0xbb9718: r0 = ""
    //     0xbb9718: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb971c: b               #0xbb9724
    // 0xbb9720: mov             x0, x1
    // 0xbb9724: ldur            x2, [fp, #-8]
    // 0xbb9728: stur            x0, [fp, #-0x18]
    // 0xbb972c: r0 = ImageHeaders.forImages()
    //     0xbb972c: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbb9730: r1 = Function '<anonymous closure>':.
    //     0xbb9730: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c398] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xbb9734: ldr             x1, [x1, #0x398]
    // 0xbb9738: r2 = Null
    //     0xbb9738: mov             x2, NULL
    // 0xbb973c: stur            x0, [fp, #-0x20]
    // 0xbb9740: r0 = AllocateClosure()
    //     0xbb9740: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb9744: r1 = Function '<anonymous closure>':.
    //     0xbb9744: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3a0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbb9748: ldr             x1, [x1, #0x3a0]
    // 0xbb974c: r2 = Null
    //     0xbb974c: mov             x2, NULL
    // 0xbb9750: stur            x0, [fp, #-0x28]
    // 0xbb9754: r0 = AllocateClosure()
    //     0xbb9754: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbb9758: stur            x0, [fp, #-0x30]
    // 0xbb975c: r0 = CachedNetworkImage()
    //     0xbb975c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbb9760: stur            x0, [fp, #-0x38]
    // 0xbb9764: r16 = 56.000000
    //     0xbb9764: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb9768: ldr             x16, [x16, #0xb78]
    // 0xbb976c: r30 = 56.000000
    //     0xbb976c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbb9770: ldr             lr, [lr, #0xb78]
    // 0xbb9774: stp             lr, x16, [SP, #0x20]
    // 0xbb9778: ldur            x16, [fp, #-0x20]
    // 0xbb977c: r30 = Instance_BoxFit
    //     0xbb977c: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbb9780: ldr             lr, [lr, #0xb18]
    // 0xbb9784: stp             lr, x16, [SP, #0x10]
    // 0xbb9788: ldur            x16, [fp, #-0x28]
    // 0xbb978c: ldur            lr, [fp, #-0x30]
    // 0xbb9790: stp             lr, x16, [SP]
    // 0xbb9794: mov             x1, x0
    // 0xbb9798: ldur            x2, [fp, #-0x18]
    // 0xbb979c: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xbb979c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fbc8] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xbb97a0: ldr             x4, [x4, #0xbc8]
    // 0xbb97a4: r0 = CachedNetworkImage()
    //     0xbb97a4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbb97a8: ldur            x2, [fp, #-8]
    // 0xbb97ac: LoadField: r0 = r2->field_f
    //     0xbb97ac: ldur            w0, [x2, #0xf]
    // 0xbb97b0: DecompressPointer r0
    //     0xbb97b0: add             x0, x0, HEAP, lsl #32
    // 0xbb97b4: LoadField: r1 = r0->field_1f
    //     0xbb97b4: ldur            w1, [x0, #0x1f]
    // 0xbb97b8: DecompressPointer r1
    //     0xbb97b8: add             x1, x1, HEAP, lsl #32
    // 0xbb97bc: LoadField: r0 = r1->field_8f
    //     0xbb97bc: ldur            w0, [x1, #0x8f]
    // 0xbb97c0: DecompressPointer r0
    //     0xbb97c0: add             x0, x0, HEAP, lsl #32
    // 0xbb97c4: mov             x1, x0
    // 0xbb97c8: r0 = value()
    //     0xbb97c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb97cc: LoadField: r1 = r0->field_f
    //     0xbb97cc: ldur            w1, [x0, #0xf]
    // 0xbb97d0: DecompressPointer r1
    //     0xbb97d0: add             x1, x1, HEAP, lsl #32
    // 0xbb97d4: cmp             w1, NULL
    // 0xbb97d8: b.ne            #0xbb97e0
    // 0xbb97dc: r1 = ""
    //     0xbb97dc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbb97e0: ldur            x2, [fp, #-8]
    // 0xbb97e4: r0 = capitalizeFirstWord()
    //     0xbb97e4: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xbb97e8: ldur            x2, [fp, #-8]
    // 0xbb97ec: stur            x0, [fp, #-0x18]
    // 0xbb97f0: LoadField: r1 = r2->field_13
    //     0xbb97f0: ldur            w1, [x2, #0x13]
    // 0xbb97f4: DecompressPointer r1
    //     0xbb97f4: add             x1, x1, HEAP, lsl #32
    // 0xbb97f8: r0 = of()
    //     0xbb97f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb97fc: LoadField: r1 = r0->field_87
    //     0xbb97fc: ldur            w1, [x0, #0x87]
    // 0xbb9800: DecompressPointer r1
    //     0xbb9800: add             x1, x1, HEAP, lsl #32
    // 0xbb9804: LoadField: r0 = r1->field_7
    //     0xbb9804: ldur            w0, [x1, #7]
    // 0xbb9808: DecompressPointer r0
    //     0xbb9808: add             x0, x0, HEAP, lsl #32
    // 0xbb980c: r16 = 12.000000
    //     0xbb980c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb9810: ldr             x16, [x16, #0x9e8]
    // 0xbb9814: r30 = Instance_Color
    //     0xbb9814: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb9818: stp             lr, x16, [SP]
    // 0xbb981c: mov             x1, x0
    // 0xbb9820: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb9820: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb9824: ldr             x4, [x4, #0xaa0]
    // 0xbb9828: r0 = copyWith()
    //     0xbb9828: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb982c: stur            x0, [fp, #-0x20]
    // 0xbb9830: r0 = Text()
    //     0xbb9830: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9834: mov             x1, x0
    // 0xbb9838: ldur            x0, [fp, #-0x18]
    // 0xbb983c: stur            x1, [fp, #-0x28]
    // 0xbb9840: StoreField: r1->field_b = r0
    //     0xbb9840: stur            w0, [x1, #0xb]
    // 0xbb9844: ldur            x0, [fp, #-0x20]
    // 0xbb9848: StoreField: r1->field_13 = r0
    //     0xbb9848: stur            w0, [x1, #0x13]
    // 0xbb984c: r0 = Instance_TextOverflow
    //     0xbb984c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbb9850: ldr             x0, [x0, #0xe10]
    // 0xbb9854: StoreField: r1->field_2b = r0
    //     0xbb9854: stur            w0, [x1, #0x2b]
    // 0xbb9858: r0 = 2
    //     0xbb9858: movz            x0, #0x2
    // 0xbb985c: StoreField: r1->field_37 = r0
    //     0xbb985c: stur            w0, [x1, #0x37]
    // 0xbb9860: r0 = Padding()
    //     0xbb9860: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb9864: mov             x2, x0
    // 0xbb9868: r0 = Instance_EdgeInsets
    //     0xbb9868: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbb986c: ldr             x0, [x0, #0xa78]
    // 0xbb9870: stur            x2, [fp, #-0x18]
    // 0xbb9874: StoreField: r2->field_f = r0
    //     0xbb9874: stur            w0, [x2, #0xf]
    // 0xbb9878: ldur            x0, [fp, #-0x28]
    // 0xbb987c: StoreField: r2->field_b = r0
    //     0xbb987c: stur            w0, [x2, #0xb]
    // 0xbb9880: ldur            x0, [fp, #-8]
    // 0xbb9884: LoadField: r1 = r0->field_f
    //     0xbb9884: ldur            w1, [x0, #0xf]
    // 0xbb9888: DecompressPointer r1
    //     0xbb9888: add             x1, x1, HEAP, lsl #32
    // 0xbb988c: LoadField: r3 = r1->field_1f
    //     0xbb988c: ldur            w3, [x1, #0x1f]
    // 0xbb9890: DecompressPointer r3
    //     0xbb9890: add             x3, x3, HEAP, lsl #32
    // 0xbb9894: LoadField: r1 = r3->field_8f
    //     0xbb9894: ldur            w1, [x3, #0x8f]
    // 0xbb9898: DecompressPointer r1
    //     0xbb9898: add             x1, x1, HEAP, lsl #32
    // 0xbb989c: r0 = value()
    //     0xbb989c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb98a0: LoadField: r1 = r0->field_57
    //     0xbb98a0: ldur            w1, [x0, #0x57]
    // 0xbb98a4: DecompressPointer r1
    //     0xbb98a4: add             x1, x1, HEAP, lsl #32
    // 0xbb98a8: r0 = LoadClassIdInstr(r1)
    //     0xbb98a8: ldur            x0, [x1, #-1]
    //     0xbb98ac: ubfx            x0, x0, #0xc, #0x14
    // 0xbb98b0: r16 = "size"
    //     0xbb98b0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbb98b4: ldr             x16, [x16, #0x9c0]
    // 0xbb98b8: stp             x16, x1, [SP]
    // 0xbb98bc: mov             lr, x0
    // 0xbb98c0: ldr             lr, [x21, lr, lsl #3]
    // 0xbb98c4: blr             lr
    // 0xbb98c8: tbnz            w0, #4, #0xbb9a74
    // 0xbb98cc: ldur            x0, [fp, #-8]
    // 0xbb98d0: r1 = Null
    //     0xbb98d0: mov             x1, NULL
    // 0xbb98d4: r2 = 8
    //     0xbb98d4: movz            x2, #0x8
    // 0xbb98d8: r0 = AllocateArray()
    //     0xbb98d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb98dc: stur            x0, [fp, #-0x20]
    // 0xbb98e0: r16 = "Size: "
    //     0xbb98e0: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xbb98e4: ldr             x16, [x16, #0xf00]
    // 0xbb98e8: StoreField: r0->field_f = r16
    //     0xbb98e8: stur            w16, [x0, #0xf]
    // 0xbb98ec: ldur            x2, [fp, #-8]
    // 0xbb98f0: LoadField: r1 = r2->field_f
    //     0xbb98f0: ldur            w1, [x2, #0xf]
    // 0xbb98f4: DecompressPointer r1
    //     0xbb98f4: add             x1, x1, HEAP, lsl #32
    // 0xbb98f8: LoadField: r3 = r1->field_1f
    //     0xbb98f8: ldur            w3, [x1, #0x1f]
    // 0xbb98fc: DecompressPointer r3
    //     0xbb98fc: add             x3, x3, HEAP, lsl #32
    // 0xbb9900: LoadField: r1 = r3->field_8f
    //     0xbb9900: ldur            w1, [x3, #0x8f]
    // 0xbb9904: DecompressPointer r1
    //     0xbb9904: add             x1, x1, HEAP, lsl #32
    // 0xbb9908: r0 = value()
    //     0xbb9908: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb990c: LoadField: r2 = r0->field_53
    //     0xbb990c: ldur            w2, [x0, #0x53]
    // 0xbb9910: DecompressPointer r2
    //     0xbb9910: add             x2, x2, HEAP, lsl #32
    // 0xbb9914: cmp             w2, NULL
    // 0xbb9918: b.ne            #0xbb9928
    // 0xbb991c: ldur            x3, [fp, #-8]
    // 0xbb9920: r0 = Null
    //     0xbb9920: mov             x0, NULL
    // 0xbb9924: b               #0xbb996c
    // 0xbb9928: ldur            x3, [fp, #-8]
    // 0xbb992c: LoadField: r0 = r3->field_f
    //     0xbb992c: ldur            w0, [x3, #0xf]
    // 0xbb9930: DecompressPointer r0
    //     0xbb9930: add             x0, x0, HEAP, lsl #32
    // 0xbb9934: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xbb9934: ldur            x4, [x0, #0x17]
    // 0xbb9938: LoadField: r0 = r2->field_b
    //     0xbb9938: ldur            w0, [x2, #0xb]
    // 0xbb993c: r1 = LoadInt32Instr(r0)
    //     0xbb993c: sbfx            x1, x0, #1, #0x1f
    // 0xbb9940: mov             x0, x1
    // 0xbb9944: mov             x1, x4
    // 0xbb9948: cmp             x1, x0
    // 0xbb994c: b.hs            #0xbbb424
    // 0xbb9950: LoadField: r0 = r2->field_f
    //     0xbb9950: ldur            w0, [x2, #0xf]
    // 0xbb9954: DecompressPointer r0
    //     0xbb9954: add             x0, x0, HEAP, lsl #32
    // 0xbb9958: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbb9958: add             x16, x0, x4, lsl #2
    //     0xbb995c: ldur            w1, [x16, #0xf]
    // 0xbb9960: DecompressPointer r1
    //     0xbb9960: add             x1, x1, HEAP, lsl #32
    // 0xbb9964: LoadField: r0 = r1->field_f
    //     0xbb9964: ldur            w0, [x1, #0xf]
    // 0xbb9968: DecompressPointer r0
    //     0xbb9968: add             x0, x0, HEAP, lsl #32
    // 0xbb996c: ldur            x2, [fp, #-0x20]
    // 0xbb9970: mov             x1, x2
    // 0xbb9974: ArrayStore: r1[1] = r0  ; List_4
    //     0xbb9974: add             x25, x1, #0x13
    //     0xbb9978: str             w0, [x25]
    //     0xbb997c: tbz             w0, #0, #0xbb9998
    //     0xbb9980: ldurb           w16, [x1, #-1]
    //     0xbb9984: ldurb           w17, [x0, #-1]
    //     0xbb9988: and             x16, x17, x16, lsr #2
    //     0xbb998c: tst             x16, HEAP, lsr #32
    //     0xbb9990: b.eq            #0xbb9998
    //     0xbb9994: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb9998: r16 = " / Qty: "
    //     0xbb9998: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbb999c: ldr             x16, [x16, #0x760]
    // 0xbb99a0: ArrayStore: r2[0] = r16  ; List_4
    //     0xbb99a0: stur            w16, [x2, #0x17]
    // 0xbb99a4: LoadField: r0 = r3->field_f
    //     0xbb99a4: ldur            w0, [x3, #0xf]
    // 0xbb99a8: DecompressPointer r0
    //     0xbb99a8: add             x0, x0, HEAP, lsl #32
    // 0xbb99ac: LoadField: r1 = r0->field_1f
    //     0xbb99ac: ldur            w1, [x0, #0x1f]
    // 0xbb99b0: DecompressPointer r1
    //     0xbb99b0: add             x1, x1, HEAP, lsl #32
    // 0xbb99b4: r17 = 287
    //     0xbb99b4: movz            x17, #0x11f
    // 0xbb99b8: ldr             w0, [x1, x17]
    // 0xbb99bc: DecompressPointer r0
    //     0xbb99bc: add             x0, x0, HEAP, lsl #32
    // 0xbb99c0: mov             x1, x0
    // 0xbb99c4: r0 = value()
    //     0xbb99c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb99c8: ldur            x1, [fp, #-0x20]
    // 0xbb99cc: ArrayStore: r1[3] = r0  ; List_4
    //     0xbb99cc: add             x25, x1, #0x1b
    //     0xbb99d0: str             w0, [x25]
    //     0xbb99d4: tbz             w0, #0, #0xbb99f0
    //     0xbb99d8: ldurb           w16, [x1, #-1]
    //     0xbb99dc: ldurb           w17, [x0, #-1]
    //     0xbb99e0: and             x16, x17, x16, lsr #2
    //     0xbb99e4: tst             x16, HEAP, lsr #32
    //     0xbb99e8: b.eq            #0xbb99f0
    //     0xbb99ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb99f0: ldur            x16, [fp, #-0x20]
    // 0xbb99f4: str             x16, [SP]
    // 0xbb99f8: r0 = _interpolate()
    //     0xbb99f8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb99fc: ldur            x2, [fp, #-8]
    // 0xbb9a00: stur            x0, [fp, #-0x20]
    // 0xbb9a04: LoadField: r1 = r2->field_13
    //     0xbb9a04: ldur            w1, [x2, #0x13]
    // 0xbb9a08: DecompressPointer r1
    //     0xbb9a08: add             x1, x1, HEAP, lsl #32
    // 0xbb9a0c: r0 = of()
    //     0xbb9a0c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb9a10: LoadField: r1 = r0->field_87
    //     0xbb9a10: ldur            w1, [x0, #0x87]
    // 0xbb9a14: DecompressPointer r1
    //     0xbb9a14: add             x1, x1, HEAP, lsl #32
    // 0xbb9a18: LoadField: r0 = r1->field_2b
    //     0xbb9a18: ldur            w0, [x1, #0x2b]
    // 0xbb9a1c: DecompressPointer r0
    //     0xbb9a1c: add             x0, x0, HEAP, lsl #32
    // 0xbb9a20: stur            x0, [fp, #-0x28]
    // 0xbb9a24: r1 = Instance_Color
    //     0xbb9a24: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb9a28: d0 = 0.700000
    //     0xbb9a28: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb9a2c: ldr             d0, [x17, #0xf48]
    // 0xbb9a30: r0 = withOpacity()
    //     0xbb9a30: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb9a34: r16 = 12.000000
    //     0xbb9a34: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb9a38: ldr             x16, [x16, #0x9e8]
    // 0xbb9a3c: stp             x0, x16, [SP]
    // 0xbb9a40: ldur            x1, [fp, #-0x28]
    // 0xbb9a44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb9a44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb9a48: ldr             x4, [x4, #0xaa0]
    // 0xbb9a4c: r0 = copyWith()
    //     0xbb9a4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb9a50: stur            x0, [fp, #-0x28]
    // 0xbb9a54: r0 = Text()
    //     0xbb9a54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9a58: mov             x1, x0
    // 0xbb9a5c: ldur            x0, [fp, #-0x20]
    // 0xbb9a60: StoreField: r1->field_b = r0
    //     0xbb9a60: stur            w0, [x1, #0xb]
    // 0xbb9a64: ldur            x0, [fp, #-0x28]
    // 0xbb9a68: StoreField: r1->field_13 = r0
    //     0xbb9a68: stur            w0, [x1, #0x13]
    // 0xbb9a6c: mov             x0, x1
    // 0xbb9a70: b               #0xbb9c18
    // 0xbb9a74: ldur            x0, [fp, #-8]
    // 0xbb9a78: r1 = Null
    //     0xbb9a78: mov             x1, NULL
    // 0xbb9a7c: r2 = 8
    //     0xbb9a7c: movz            x2, #0x8
    // 0xbb9a80: r0 = AllocateArray()
    //     0xbb9a80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9a84: stur            x0, [fp, #-0x20]
    // 0xbb9a88: r16 = "Variant : "
    //     0xbb9a88: add             x16, PP, #0x33, lsl #12  ; [pp+0x33768] "Variant : "
    //     0xbb9a8c: ldr             x16, [x16, #0x768]
    // 0xbb9a90: StoreField: r0->field_f = r16
    //     0xbb9a90: stur            w16, [x0, #0xf]
    // 0xbb9a94: ldur            x2, [fp, #-8]
    // 0xbb9a98: LoadField: r1 = r2->field_f
    //     0xbb9a98: ldur            w1, [x2, #0xf]
    // 0xbb9a9c: DecompressPointer r1
    //     0xbb9a9c: add             x1, x1, HEAP, lsl #32
    // 0xbb9aa0: LoadField: r3 = r1->field_1f
    //     0xbb9aa0: ldur            w3, [x1, #0x1f]
    // 0xbb9aa4: DecompressPointer r3
    //     0xbb9aa4: add             x3, x3, HEAP, lsl #32
    // 0xbb9aa8: LoadField: r1 = r3->field_8f
    //     0xbb9aa8: ldur            w1, [x3, #0x8f]
    // 0xbb9aac: DecompressPointer r1
    //     0xbb9aac: add             x1, x1, HEAP, lsl #32
    // 0xbb9ab0: r0 = value()
    //     0xbb9ab0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb9ab4: LoadField: r2 = r0->field_53
    //     0xbb9ab4: ldur            w2, [x0, #0x53]
    // 0xbb9ab8: DecompressPointer r2
    //     0xbb9ab8: add             x2, x2, HEAP, lsl #32
    // 0xbb9abc: cmp             w2, NULL
    // 0xbb9ac0: b.ne            #0xbb9ad0
    // 0xbb9ac4: ldur            x3, [fp, #-8]
    // 0xbb9ac8: r0 = Null
    //     0xbb9ac8: mov             x0, NULL
    // 0xbb9acc: b               #0xbb9b14
    // 0xbb9ad0: ldur            x3, [fp, #-8]
    // 0xbb9ad4: LoadField: r0 = r3->field_f
    //     0xbb9ad4: ldur            w0, [x3, #0xf]
    // 0xbb9ad8: DecompressPointer r0
    //     0xbb9ad8: add             x0, x0, HEAP, lsl #32
    // 0xbb9adc: ArrayLoad: r4 = r0[0]  ; List_8
    //     0xbb9adc: ldur            x4, [x0, #0x17]
    // 0xbb9ae0: LoadField: r0 = r2->field_b
    //     0xbb9ae0: ldur            w0, [x2, #0xb]
    // 0xbb9ae4: r1 = LoadInt32Instr(r0)
    //     0xbb9ae4: sbfx            x1, x0, #1, #0x1f
    // 0xbb9ae8: mov             x0, x1
    // 0xbb9aec: mov             x1, x4
    // 0xbb9af0: cmp             x1, x0
    // 0xbb9af4: b.hs            #0xbbb428
    // 0xbb9af8: LoadField: r0 = r2->field_f
    //     0xbb9af8: ldur            w0, [x2, #0xf]
    // 0xbb9afc: DecompressPointer r0
    //     0xbb9afc: add             x0, x0, HEAP, lsl #32
    // 0xbb9b00: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbb9b00: add             x16, x0, x4, lsl #2
    //     0xbb9b04: ldur            w1, [x16, #0xf]
    // 0xbb9b08: DecompressPointer r1
    //     0xbb9b08: add             x1, x1, HEAP, lsl #32
    // 0xbb9b0c: LoadField: r0 = r1->field_f
    //     0xbb9b0c: ldur            w0, [x1, #0xf]
    // 0xbb9b10: DecompressPointer r0
    //     0xbb9b10: add             x0, x0, HEAP, lsl #32
    // 0xbb9b14: ldur            x2, [fp, #-0x20]
    // 0xbb9b18: mov             x1, x2
    // 0xbb9b1c: ArrayStore: r1[1] = r0  ; List_4
    //     0xbb9b1c: add             x25, x1, #0x13
    //     0xbb9b20: str             w0, [x25]
    //     0xbb9b24: tbz             w0, #0, #0xbb9b40
    //     0xbb9b28: ldurb           w16, [x1, #-1]
    //     0xbb9b2c: ldurb           w17, [x0, #-1]
    //     0xbb9b30: and             x16, x17, x16, lsr #2
    //     0xbb9b34: tst             x16, HEAP, lsr #32
    //     0xbb9b38: b.eq            #0xbb9b40
    //     0xbb9b3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb9b40: r16 = "/Qty: "
    //     0xbb9b40: add             x16, PP, #0x5b, lsl #12  ; [pp+0x5b3b0] "/Qty: "
    //     0xbb9b44: ldr             x16, [x16, #0x3b0]
    // 0xbb9b48: ArrayStore: r2[0] = r16  ; List_4
    //     0xbb9b48: stur            w16, [x2, #0x17]
    // 0xbb9b4c: LoadField: r0 = r3->field_f
    //     0xbb9b4c: ldur            w0, [x3, #0xf]
    // 0xbb9b50: DecompressPointer r0
    //     0xbb9b50: add             x0, x0, HEAP, lsl #32
    // 0xbb9b54: LoadField: r1 = r0->field_1f
    //     0xbb9b54: ldur            w1, [x0, #0x1f]
    // 0xbb9b58: DecompressPointer r1
    //     0xbb9b58: add             x1, x1, HEAP, lsl #32
    // 0xbb9b5c: r17 = 287
    //     0xbb9b5c: movz            x17, #0x11f
    // 0xbb9b60: ldr             w0, [x1, x17]
    // 0xbb9b64: DecompressPointer r0
    //     0xbb9b64: add             x0, x0, HEAP, lsl #32
    // 0xbb9b68: mov             x1, x0
    // 0xbb9b6c: r0 = value()
    //     0xbb9b6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb9b70: ldur            x1, [fp, #-0x20]
    // 0xbb9b74: ArrayStore: r1[3] = r0  ; List_4
    //     0xbb9b74: add             x25, x1, #0x1b
    //     0xbb9b78: str             w0, [x25]
    //     0xbb9b7c: tbz             w0, #0, #0xbb9b98
    //     0xbb9b80: ldurb           w16, [x1, #-1]
    //     0xbb9b84: ldurb           w17, [x0, #-1]
    //     0xbb9b88: and             x16, x17, x16, lsr #2
    //     0xbb9b8c: tst             x16, HEAP, lsr #32
    //     0xbb9b90: b.eq            #0xbb9b98
    //     0xbb9b94: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb9b98: ldur            x16, [fp, #-0x20]
    // 0xbb9b9c: str             x16, [SP]
    // 0xbb9ba0: r0 = _interpolate()
    //     0xbb9ba0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb9ba4: ldur            x2, [fp, #-8]
    // 0xbb9ba8: stur            x0, [fp, #-0x20]
    // 0xbb9bac: LoadField: r1 = r2->field_13
    //     0xbb9bac: ldur            w1, [x2, #0x13]
    // 0xbb9bb0: DecompressPointer r1
    //     0xbb9bb0: add             x1, x1, HEAP, lsl #32
    // 0xbb9bb4: r0 = of()
    //     0xbb9bb4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb9bb8: LoadField: r1 = r0->field_87
    //     0xbb9bb8: ldur            w1, [x0, #0x87]
    // 0xbb9bbc: DecompressPointer r1
    //     0xbb9bbc: add             x1, x1, HEAP, lsl #32
    // 0xbb9bc0: LoadField: r0 = r1->field_2b
    //     0xbb9bc0: ldur            w0, [x1, #0x2b]
    // 0xbb9bc4: DecompressPointer r0
    //     0xbb9bc4: add             x0, x0, HEAP, lsl #32
    // 0xbb9bc8: stur            x0, [fp, #-0x28]
    // 0xbb9bcc: r1 = Instance_Color
    //     0xbb9bcc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb9bd0: d0 = 0.700000
    //     0xbb9bd0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbb9bd4: ldr             d0, [x17, #0xf48]
    // 0xbb9bd8: r0 = withOpacity()
    //     0xbb9bd8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb9bdc: r16 = 12.000000
    //     0xbb9bdc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb9be0: ldr             x16, [x16, #0x9e8]
    // 0xbb9be4: stp             x0, x16, [SP]
    // 0xbb9be8: ldur            x1, [fp, #-0x28]
    // 0xbb9bec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb9bec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb9bf0: ldr             x4, [x4, #0xaa0]
    // 0xbb9bf4: r0 = copyWith()
    //     0xbb9bf4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb9bf8: stur            x0, [fp, #-0x28]
    // 0xbb9bfc: r0 = Text()
    //     0xbb9bfc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9c00: mov             x1, x0
    // 0xbb9c04: ldur            x0, [fp, #-0x20]
    // 0xbb9c08: StoreField: r1->field_b = r0
    //     0xbb9c08: stur            w0, [x1, #0xb]
    // 0xbb9c0c: ldur            x0, [fp, #-0x28]
    // 0xbb9c10: StoreField: r1->field_13 = r0
    //     0xbb9c10: stur            w0, [x1, #0x13]
    // 0xbb9c14: mov             x0, x1
    // 0xbb9c18: ldur            x2, [fp, #-8]
    // 0xbb9c1c: stur            x0, [fp, #-0x20]
    // 0xbb9c20: r0 = Padding()
    //     0xbb9c20: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb9c24: mov             x3, x0
    // 0xbb9c28: r0 = Instance_EdgeInsets
    //     0xbb9c28: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b3b8] Obj!EdgeInsets@d58041
    //     0xbb9c2c: ldr             x0, [x0, #0x3b8]
    // 0xbb9c30: stur            x3, [fp, #-0x28]
    // 0xbb9c34: StoreField: r3->field_f = r0
    //     0xbb9c34: stur            w0, [x3, #0xf]
    // 0xbb9c38: ldur            x1, [fp, #-0x20]
    // 0xbb9c3c: StoreField: r3->field_b = r1
    //     0xbb9c3c: stur            w1, [x3, #0xb]
    // 0xbb9c40: ldur            x4, [fp, #-8]
    // 0xbb9c44: LoadField: r1 = r4->field_f
    //     0xbb9c44: ldur            w1, [x4, #0xf]
    // 0xbb9c48: DecompressPointer r1
    //     0xbb9c48: add             x1, x1, HEAP, lsl #32
    // 0xbb9c4c: LoadField: r2 = r1->field_b
    //     0xbb9c4c: ldur            w2, [x1, #0xb]
    // 0xbb9c50: DecompressPointer r2
    //     0xbb9c50: add             x2, x2, HEAP, lsl #32
    // 0xbb9c54: cmp             w2, NULL
    // 0xbb9c58: b.eq            #0xbbb42c
    // 0xbb9c5c: LoadField: r1 = r2->field_b
    //     0xbb9c5c: ldur            w1, [x2, #0xb]
    // 0xbb9c60: DecompressPointer r1
    //     0xbb9c60: add             x1, x1, HEAP, lsl #32
    // 0xbb9c64: LoadField: r2 = r1->field_b
    //     0xbb9c64: ldur            w2, [x1, #0xb]
    // 0xbb9c68: DecompressPointer r2
    //     0xbb9c68: add             x2, x2, HEAP, lsl #32
    // 0xbb9c6c: cmp             w2, NULL
    // 0xbb9c70: b.ne            #0xbb9c7c
    // 0xbb9c74: r8 = Null
    //     0xbb9c74: mov             x8, NULL
    // 0xbb9c78: b               #0xbb9c88
    // 0xbb9c7c: LoadField: r1 = r2->field_1f
    //     0xbb9c7c: ldur            w1, [x2, #0x1f]
    // 0xbb9c80: DecompressPointer r1
    //     0xbb9c80: add             x1, x1, HEAP, lsl #32
    // 0xbb9c84: mov             x8, x1
    // 0xbb9c88: ldur            x6, [fp, #-0x38]
    // 0xbb9c8c: ldur            x5, [fp, #-0x18]
    // 0xbb9c90: r7 = 4
    //     0xbb9c90: movz            x7, #0x4
    // 0xbb9c94: mov             x2, x7
    // 0xbb9c98: stur            x8, [fp, #-0x20]
    // 0xbb9c9c: r1 = Null
    //     0xbb9c9c: mov             x1, NULL
    // 0xbb9ca0: r0 = AllocateArray()
    //     0xbb9ca0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9ca4: mov             x1, x0
    // 0xbb9ca8: ldur            x0, [fp, #-0x20]
    // 0xbb9cac: StoreField: r1->field_f = r0
    //     0xbb9cac: stur            w0, [x1, #0xf]
    // 0xbb9cb0: r16 = " "
    //     0xbb9cb0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbb9cb4: StoreField: r1->field_13 = r16
    //     0xbb9cb4: stur            w16, [x1, #0x13]
    // 0xbb9cb8: str             x1, [SP]
    // 0xbb9cbc: r0 = _interpolate()
    //     0xbb9cbc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbb9cc0: ldur            x2, [fp, #-8]
    // 0xbb9cc4: stur            x0, [fp, #-0x20]
    // 0xbb9cc8: LoadField: r1 = r2->field_13
    //     0xbb9cc8: ldur            w1, [x2, #0x13]
    // 0xbb9ccc: DecompressPointer r1
    //     0xbb9ccc: add             x1, x1, HEAP, lsl #32
    // 0xbb9cd0: r0 = of()
    //     0xbb9cd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbb9cd4: LoadField: r1 = r0->field_87
    //     0xbb9cd4: ldur            w1, [x0, #0x87]
    // 0xbb9cd8: DecompressPointer r1
    //     0xbb9cd8: add             x1, x1, HEAP, lsl #32
    // 0xbb9cdc: LoadField: r0 = r1->field_7
    //     0xbb9cdc: ldur            w0, [x1, #7]
    // 0xbb9ce0: DecompressPointer r0
    //     0xbb9ce0: add             x0, x0, HEAP, lsl #32
    // 0xbb9ce4: r16 = 12.000000
    //     0xbb9ce4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbb9ce8: ldr             x16, [x16, #0x9e8]
    // 0xbb9cec: r30 = Instance_Color
    //     0xbb9cec: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb9cf0: stp             lr, x16, [SP]
    // 0xbb9cf4: mov             x1, x0
    // 0xbb9cf8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbb9cf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbb9cfc: ldr             x4, [x4, #0xaa0]
    // 0xbb9d00: r0 = copyWith()
    //     0xbb9d00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbb9d04: stur            x0, [fp, #-0x30]
    // 0xbb9d08: r0 = Text()
    //     0xbb9d08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbb9d0c: mov             x1, x0
    // 0xbb9d10: ldur            x0, [fp, #-0x20]
    // 0xbb9d14: stur            x1, [fp, #-0x40]
    // 0xbb9d18: StoreField: r1->field_b = r0
    //     0xbb9d18: stur            w0, [x1, #0xb]
    // 0xbb9d1c: ldur            x0, [fp, #-0x30]
    // 0xbb9d20: StoreField: r1->field_13 = r0
    //     0xbb9d20: stur            w0, [x1, #0x13]
    // 0xbb9d24: r0 = Padding()
    //     0xbb9d24: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb9d28: mov             x3, x0
    // 0xbb9d2c: r0 = Instance_EdgeInsets
    //     0xbb9d2c: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b3b8] Obj!EdgeInsets@d58041
    //     0xbb9d30: ldr             x0, [x0, #0x3b8]
    // 0xbb9d34: stur            x3, [fp, #-0x20]
    // 0xbb9d38: StoreField: r3->field_f = r0
    //     0xbb9d38: stur            w0, [x3, #0xf]
    // 0xbb9d3c: ldur            x0, [fp, #-0x40]
    // 0xbb9d40: StoreField: r3->field_b = r0
    //     0xbb9d40: stur            w0, [x3, #0xb]
    // 0xbb9d44: r1 = Null
    //     0xbb9d44: mov             x1, NULL
    // 0xbb9d48: r2 = 6
    //     0xbb9d48: movz            x2, #0x6
    // 0xbb9d4c: r0 = AllocateArray()
    //     0xbb9d4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9d50: mov             x2, x0
    // 0xbb9d54: ldur            x0, [fp, #-0x18]
    // 0xbb9d58: stur            x2, [fp, #-0x30]
    // 0xbb9d5c: StoreField: r2->field_f = r0
    //     0xbb9d5c: stur            w0, [x2, #0xf]
    // 0xbb9d60: ldur            x0, [fp, #-0x28]
    // 0xbb9d64: StoreField: r2->field_13 = r0
    //     0xbb9d64: stur            w0, [x2, #0x13]
    // 0xbb9d68: ldur            x0, [fp, #-0x20]
    // 0xbb9d6c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbb9d6c: stur            w0, [x2, #0x17]
    // 0xbb9d70: r1 = <Widget>
    //     0xbb9d70: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb9d74: r0 = AllocateGrowableArray()
    //     0xbb9d74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb9d78: mov             x1, x0
    // 0xbb9d7c: ldur            x0, [fp, #-0x30]
    // 0xbb9d80: stur            x1, [fp, #-0x18]
    // 0xbb9d84: StoreField: r1->field_f = r0
    //     0xbb9d84: stur            w0, [x1, #0xf]
    // 0xbb9d88: r2 = 6
    //     0xbb9d88: movz            x2, #0x6
    // 0xbb9d8c: StoreField: r1->field_b = r2
    //     0xbb9d8c: stur            w2, [x1, #0xb]
    // 0xbb9d90: r0 = Column()
    //     0xbb9d90: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbb9d94: mov             x2, x0
    // 0xbb9d98: r0 = Instance_Axis
    //     0xbb9d98: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbb9d9c: stur            x2, [fp, #-0x20]
    // 0xbb9da0: StoreField: r2->field_f = r0
    //     0xbb9da0: stur            w0, [x2, #0xf]
    // 0xbb9da4: r3 = Instance_MainAxisAlignment
    //     0xbb9da4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb9da8: ldr             x3, [x3, #0xa08]
    // 0xbb9dac: StoreField: r2->field_13 = r3
    //     0xbb9dac: stur            w3, [x2, #0x13]
    // 0xbb9db0: r4 = Instance_MainAxisSize
    //     0xbb9db0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb9db4: ldr             x4, [x4, #0xa10]
    // 0xbb9db8: ArrayStore: r2[0] = r4  ; List_4
    //     0xbb9db8: stur            w4, [x2, #0x17]
    // 0xbb9dbc: r5 = Instance_CrossAxisAlignment
    //     0xbb9dbc: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbb9dc0: ldr             x5, [x5, #0x890]
    // 0xbb9dc4: StoreField: r2->field_1b = r5
    //     0xbb9dc4: stur            w5, [x2, #0x1b]
    // 0xbb9dc8: r6 = Instance_VerticalDirection
    //     0xbb9dc8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb9dcc: ldr             x6, [x6, #0xa20]
    // 0xbb9dd0: StoreField: r2->field_23 = r6
    //     0xbb9dd0: stur            w6, [x2, #0x23]
    // 0xbb9dd4: r7 = Instance_Clip
    //     0xbb9dd4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb9dd8: ldr             x7, [x7, #0x38]
    // 0xbb9ddc: StoreField: r2->field_2b = r7
    //     0xbb9ddc: stur            w7, [x2, #0x2b]
    // 0xbb9de0: StoreField: r2->field_2f = rZR
    //     0xbb9de0: stur            xzr, [x2, #0x2f]
    // 0xbb9de4: ldur            x1, [fp, #-0x18]
    // 0xbb9de8: StoreField: r2->field_b = r1
    //     0xbb9de8: stur            w1, [x2, #0xb]
    // 0xbb9dec: r1 = <FlexParentData>
    //     0xbb9dec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbb9df0: ldr             x1, [x1, #0xe00]
    // 0xbb9df4: r0 = Expanded()
    //     0xbb9df4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbb9df8: mov             x3, x0
    // 0xbb9dfc: r0 = 1
    //     0xbb9dfc: movz            x0, #0x1
    // 0xbb9e00: stur            x3, [fp, #-0x18]
    // 0xbb9e04: StoreField: r3->field_13 = r0
    //     0xbb9e04: stur            x0, [x3, #0x13]
    // 0xbb9e08: r1 = Instance_FlexFit
    //     0xbb9e08: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbb9e0c: ldr             x1, [x1, #0xe08]
    // 0xbb9e10: StoreField: r3->field_1b = r1
    //     0xbb9e10: stur            w1, [x3, #0x1b]
    // 0xbb9e14: ldur            x1, [fp, #-0x20]
    // 0xbb9e18: StoreField: r3->field_b = r1
    //     0xbb9e18: stur            w1, [x3, #0xb]
    // 0xbb9e1c: r1 = Null
    //     0xbb9e1c: mov             x1, NULL
    // 0xbb9e20: r2 = 4
    //     0xbb9e20: movz            x2, #0x4
    // 0xbb9e24: r0 = AllocateArray()
    //     0xbb9e24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbb9e28: mov             x2, x0
    // 0xbb9e2c: ldur            x0, [fp, #-0x38]
    // 0xbb9e30: stur            x2, [fp, #-0x20]
    // 0xbb9e34: StoreField: r2->field_f = r0
    //     0xbb9e34: stur            w0, [x2, #0xf]
    // 0xbb9e38: ldur            x0, [fp, #-0x18]
    // 0xbb9e3c: StoreField: r2->field_13 = r0
    //     0xbb9e3c: stur            w0, [x2, #0x13]
    // 0xbb9e40: r1 = <Widget>
    //     0xbb9e40: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbb9e44: r0 = AllocateGrowableArray()
    //     0xbb9e44: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbb9e48: mov             x1, x0
    // 0xbb9e4c: ldur            x0, [fp, #-0x20]
    // 0xbb9e50: stur            x1, [fp, #-0x18]
    // 0xbb9e54: StoreField: r1->field_f = r0
    //     0xbb9e54: stur            w0, [x1, #0xf]
    // 0xbb9e58: r2 = 4
    //     0xbb9e58: movz            x2, #0x4
    // 0xbb9e5c: StoreField: r1->field_b = r2
    //     0xbb9e5c: stur            w2, [x1, #0xb]
    // 0xbb9e60: r0 = Row()
    //     0xbb9e60: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbb9e64: r2 = Instance_Axis
    //     0xbb9e64: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbb9e68: StoreField: r0->field_f = r2
    //     0xbb9e68: stur            w2, [x0, #0xf]
    // 0xbb9e6c: r3 = Instance_MainAxisAlignment
    //     0xbb9e6c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbb9e70: ldr             x3, [x3, #0xa08]
    // 0xbb9e74: StoreField: r0->field_13 = r3
    //     0xbb9e74: stur            w3, [x0, #0x13]
    // 0xbb9e78: r4 = Instance_MainAxisSize
    //     0xbb9e78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbb9e7c: ldr             x4, [x4, #0xa10]
    // 0xbb9e80: ArrayStore: r0[0] = r4  ; List_4
    //     0xbb9e80: stur            w4, [x0, #0x17]
    // 0xbb9e84: r5 = Instance_CrossAxisAlignment
    //     0xbb9e84: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbb9e88: ldr             x5, [x5, #0xa18]
    // 0xbb9e8c: StoreField: r0->field_1b = r5
    //     0xbb9e8c: stur            w5, [x0, #0x1b]
    // 0xbb9e90: r6 = Instance_VerticalDirection
    //     0xbb9e90: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbb9e94: ldr             x6, [x6, #0xa20]
    // 0xbb9e98: StoreField: r0->field_23 = r6
    //     0xbb9e98: stur            w6, [x0, #0x23]
    // 0xbb9e9c: r7 = Instance_Clip
    //     0xbb9e9c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbb9ea0: ldr             x7, [x7, #0x38]
    // 0xbb9ea4: StoreField: r0->field_2b = r7
    //     0xbb9ea4: stur            w7, [x0, #0x2b]
    // 0xbb9ea8: StoreField: r0->field_2f = rZR
    //     0xbb9ea8: stur            xzr, [x0, #0x2f]
    // 0xbb9eac: ldur            x1, [fp, #-0x18]
    // 0xbb9eb0: StoreField: r0->field_b = r1
    //     0xbb9eb0: stur            w1, [x0, #0xb]
    // 0xbb9eb4: ldur            x1, [fp, #-0x10]
    // 0xbb9eb8: ArrayStore: r1[1] = r0  ; List_4
    //     0xbb9eb8: add             x25, x1, #0x13
    //     0xbb9ebc: str             w0, [x25]
    //     0xbb9ec0: tbz             w0, #0, #0xbb9edc
    //     0xbb9ec4: ldurb           w16, [x1, #-1]
    //     0xbb9ec8: ldurb           w17, [x0, #-1]
    //     0xbb9ecc: and             x16, x17, x16, lsr #2
    //     0xbb9ed0: tst             x16, HEAP, lsr #32
    //     0xbb9ed4: b.eq            #0xbb9edc
    //     0xbb9ed8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb9edc: r1 = Instance_Color
    //     0xbb9edc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbb9ee0: d0 = 0.100000
    //     0xbb9ee0: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbb9ee4: r0 = withOpacity()
    //     0xbb9ee4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbb9ee8: stur            x0, [fp, #-0x18]
    // 0xbb9eec: r0 = Divider()
    //     0xbb9eec: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbb9ef0: mov             x1, x0
    // 0xbb9ef4: ldur            x0, [fp, #-0x18]
    // 0xbb9ef8: stur            x1, [fp, #-0x20]
    // 0xbb9efc: StoreField: r1->field_1f = r0
    //     0xbb9efc: stur            w0, [x1, #0x1f]
    // 0xbb9f00: r0 = Padding()
    //     0xbb9f00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbb9f04: r2 = Instance_EdgeInsets
    //     0xbb9f04: add             x2, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xbb9f08: ldr             x2, [x2, #0xb00]
    // 0xbb9f0c: StoreField: r0->field_f = r2
    //     0xbb9f0c: stur            w2, [x0, #0xf]
    // 0xbb9f10: ldur            x1, [fp, #-0x20]
    // 0xbb9f14: StoreField: r0->field_b = r1
    //     0xbb9f14: stur            w1, [x0, #0xb]
    // 0xbb9f18: ldur            x1, [fp, #-0x10]
    // 0xbb9f1c: ArrayStore: r1[2] = r0  ; List_4
    //     0xbb9f1c: add             x25, x1, #0x17
    //     0xbb9f20: str             w0, [x25]
    //     0xbb9f24: tbz             w0, #0, #0xbb9f40
    //     0xbb9f28: ldurb           w16, [x1, #-1]
    //     0xbb9f2c: ldurb           w17, [x0, #-1]
    //     0xbb9f30: and             x16, x17, x16, lsr #2
    //     0xbb9f34: tst             x16, HEAP, lsr #32
    //     0xbb9f38: b.eq            #0xbb9f40
    //     0xbb9f3c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbb9f40: ldur            x0, [fp, #-8]
    // 0xbb9f44: LoadField: r1 = r0->field_f
    //     0xbb9f44: ldur            w1, [x0, #0xf]
    // 0xbb9f48: DecompressPointer r1
    //     0xbb9f48: add             x1, x1, HEAP, lsl #32
    // 0xbb9f4c: LoadField: r3 = r1->field_1f
    //     0xbb9f4c: ldur            w3, [x1, #0x1f]
    // 0xbb9f50: DecompressPointer r3
    //     0xbb9f50: add             x3, x3, HEAP, lsl #32
    // 0xbb9f54: LoadField: r1 = r3->field_8f
    //     0xbb9f54: ldur            w1, [x3, #0x8f]
    // 0xbb9f58: DecompressPointer r1
    //     0xbb9f58: add             x1, x1, HEAP, lsl #32
    // 0xbb9f5c: r0 = value()
    //     0xbb9f5c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb9f60: LoadField: r1 = r0->field_3f
    //     0xbb9f60: ldur            w1, [x0, #0x3f]
    // 0xbb9f64: DecompressPointer r1
    //     0xbb9f64: add             x1, x1, HEAP, lsl #32
    // 0xbb9f68: cmp             w1, NULL
    // 0xbb9f6c: b.ne            #0xbb9f78
    // 0xbb9f70: r0 = Null
    //     0xbb9f70: mov             x0, NULL
    // 0xbb9f74: b               #0xbb9f90
    // 0xbb9f78: LoadField: r0 = r1->field_b
    //     0xbb9f78: ldur            w0, [x1, #0xb]
    // 0xbb9f7c: cbnz            w0, #0xbb9f88
    // 0xbb9f80: r1 = false
    //     0xbb9f80: add             x1, NULL, #0x30  ; false
    // 0xbb9f84: b               #0xbb9f8c
    // 0xbb9f88: r1 = true
    //     0xbb9f88: add             x1, NULL, #0x20  ; true
    // 0xbb9f8c: mov             x0, x1
    // 0xbb9f90: cmp             w0, NULL
    // 0xbb9f94: b.eq            #0xbb9fa4
    // 0xbb9f98: tbnz            w0, #4, #0xbb9fa4
    // 0xbb9f9c: r0 = true
    //     0xbb9f9c: add             x0, NULL, #0x20  ; true
    // 0xbb9fa0: b               #0xbba004
    // 0xbb9fa4: ldur            x2, [fp, #-8]
    // 0xbb9fa8: LoadField: r0 = r2->field_f
    //     0xbb9fa8: ldur            w0, [x2, #0xf]
    // 0xbb9fac: DecompressPointer r0
    //     0xbb9fac: add             x0, x0, HEAP, lsl #32
    // 0xbb9fb0: LoadField: r1 = r0->field_1f
    //     0xbb9fb0: ldur            w1, [x0, #0x1f]
    // 0xbb9fb4: DecompressPointer r1
    //     0xbb9fb4: add             x1, x1, HEAP, lsl #32
    // 0xbb9fb8: LoadField: r0 = r1->field_73
    //     0xbb9fb8: ldur            w0, [x1, #0x73]
    // 0xbb9fbc: DecompressPointer r0
    //     0xbb9fbc: add             x0, x0, HEAP, lsl #32
    // 0xbb9fc0: mov             x1, x0
    // 0xbb9fc4: r0 = value()
    //     0xbb9fc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbb9fc8: LoadField: r1 = r0->field_13
    //     0xbb9fc8: ldur            w1, [x0, #0x13]
    // 0xbb9fcc: DecompressPointer r1
    //     0xbb9fcc: add             x1, x1, HEAP, lsl #32
    // 0xbb9fd0: cmp             w1, NULL
    // 0xbb9fd4: b.ne            #0xbb9fe0
    // 0xbb9fd8: r0 = Null
    //     0xbb9fd8: mov             x0, NULL
    // 0xbb9fdc: b               #0xbb9ff8
    // 0xbb9fe0: LoadField: r0 = r1->field_b
    //     0xbb9fe0: ldur            w0, [x1, #0xb]
    // 0xbb9fe4: cbnz            w0, #0xbb9ff0
    // 0xbb9fe8: r1 = false
    //     0xbb9fe8: add             x1, NULL, #0x30  ; false
    // 0xbb9fec: b               #0xbb9ff4
    // 0xbb9ff0: r1 = true
    //     0xbb9ff0: add             x1, NULL, #0x20  ; true
    // 0xbb9ff4: mov             x0, x1
    // 0xbb9ff8: cmp             w0, NULL
    // 0xbb9ffc: b.ne            #0xbba004
    // 0xbba000: r0 = false
    //     0xbba000: add             x0, NULL, #0x30  ; false
    // 0xbba004: ldur            x2, [fp, #-8]
    // 0xbba008: stur            x0, [fp, #-0x18]
    // 0xbba00c: LoadField: r1 = r2->field_f
    //     0xbba00c: ldur            w1, [x2, #0xf]
    // 0xbba010: DecompressPointer r1
    //     0xbba010: add             x1, x1, HEAP, lsl #32
    // 0xbba014: LoadField: r3 = r1->field_1f
    //     0xbba014: ldur            w3, [x1, #0x1f]
    // 0xbba018: DecompressPointer r3
    //     0xbba018: add             x3, x3, HEAP, lsl #32
    // 0xbba01c: LoadField: r1 = r3->field_8f
    //     0xbba01c: ldur            w1, [x3, #0x8f]
    // 0xbba020: DecompressPointer r1
    //     0xbba020: add             x1, x1, HEAP, lsl #32
    // 0xbba024: r0 = value()
    //     0xbba024: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba028: LoadField: r2 = r0->field_3f
    //     0xbba028: ldur            w2, [x0, #0x3f]
    // 0xbba02c: DecompressPointer r2
    //     0xbba02c: add             x2, x2, HEAP, lsl #32
    // 0xbba030: ldur            x0, [fp, #-8]
    // 0xbba034: stur            x2, [fp, #-0x20]
    // 0xbba038: LoadField: r1 = r0->field_f
    //     0xbba038: ldur            w1, [x0, #0xf]
    // 0xbba03c: DecompressPointer r1
    //     0xbba03c: add             x1, x1, HEAP, lsl #32
    // 0xbba040: LoadField: r3 = r1->field_1f
    //     0xbba040: ldur            w3, [x1, #0x1f]
    // 0xbba044: DecompressPointer r3
    //     0xbba044: add             x3, x3, HEAP, lsl #32
    // 0xbba048: LoadField: r1 = r3->field_73
    //     0xbba048: ldur            w1, [x3, #0x73]
    // 0xbba04c: DecompressPointer r1
    //     0xbba04c: add             x1, x1, HEAP, lsl #32
    // 0xbba050: r0 = value()
    //     0xbba050: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba054: ldur            x2, [fp, #-8]
    // 0xbba058: stur            x0, [fp, #-0x28]
    // 0xbba05c: LoadField: r1 = r2->field_f
    //     0xbba05c: ldur            w1, [x2, #0xf]
    // 0xbba060: DecompressPointer r1
    //     0xbba060: add             x1, x1, HEAP, lsl #32
    // 0xbba064: LoadField: r3 = r1->field_1f
    //     0xbba064: ldur            w3, [x1, #0x1f]
    // 0xbba068: DecompressPointer r3
    //     0xbba068: add             x3, x3, HEAP, lsl #32
    // 0xbba06c: LoadField: r1 = r3->field_8f
    //     0xbba06c: ldur            w1, [x3, #0x8f]
    // 0xbba070: DecompressPointer r1
    //     0xbba070: add             x1, x1, HEAP, lsl #32
    // 0xbba074: r0 = value()
    //     0xbba074: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba078: LoadField: r1 = r0->field_43
    //     0xbba078: ldur            w1, [x0, #0x43]
    // 0xbba07c: DecompressPointer r1
    //     0xbba07c: add             x1, x1, HEAP, lsl #32
    // 0xbba080: cbz             w1, #0xbba0b8
    // 0xbba084: ldur            x2, [fp, #-8]
    // 0xbba088: LoadField: r0 = r2->field_f
    //     0xbba088: ldur            w0, [x2, #0xf]
    // 0xbba08c: DecompressPointer r0
    //     0xbba08c: add             x0, x0, HEAP, lsl #32
    // 0xbba090: LoadField: r1 = r0->field_1f
    //     0xbba090: ldur            w1, [x0, #0x1f]
    // 0xbba094: DecompressPointer r1
    //     0xbba094: add             x1, x1, HEAP, lsl #32
    // 0xbba098: LoadField: r0 = r1->field_8f
    //     0xbba098: ldur            w0, [x1, #0x8f]
    // 0xbba09c: DecompressPointer r0
    //     0xbba09c: add             x0, x0, HEAP, lsl #32
    // 0xbba0a0: mov             x1, x0
    // 0xbba0a4: r0 = value()
    //     0xbba0a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba0a8: LoadField: r1 = r0->field_47
    //     0xbba0a8: ldur            w1, [x0, #0x47]
    // 0xbba0ac: DecompressPointer r1
    //     0xbba0ac: add             x1, x1, HEAP, lsl #32
    // 0xbba0b0: mov             x4, x1
    // 0xbba0b4: b               #0xbba140
    // 0xbba0b8: ldur            x2, [fp, #-8]
    // 0xbba0bc: LoadField: r0 = r2->field_f
    //     0xbba0bc: ldur            w0, [x2, #0xf]
    // 0xbba0c0: DecompressPointer r0
    //     0xbba0c0: add             x0, x0, HEAP, lsl #32
    // 0xbba0c4: LoadField: r1 = r0->field_1f
    //     0xbba0c4: ldur            w1, [x0, #0x1f]
    // 0xbba0c8: DecompressPointer r1
    //     0xbba0c8: add             x1, x1, HEAP, lsl #32
    // 0xbba0cc: LoadField: r0 = r1->field_eb
    //     0xbba0cc: ldur            w0, [x1, #0xeb]
    // 0xbba0d0: DecompressPointer r0
    //     0xbba0d0: add             x0, x0, HEAP, lsl #32
    // 0xbba0d4: cmp             w0, NULL
    // 0xbba0d8: b.eq            #0xbba120
    // 0xbba0dc: cmp             w0, NULL
    // 0xbba0e0: b.ne            #0xbba0ec
    // 0xbba0e4: r3 = Null
    //     0xbba0e4: mov             x3, NULL
    // 0xbba0e8: b               #0xbba104
    // 0xbba0ec: LoadField: r3 = r0->field_7
    //     0xbba0ec: ldur            w3, [x0, #7]
    // 0xbba0f0: cbnz            w3, #0xbba0fc
    // 0xbba0f4: r4 = false
    //     0xbba0f4: add             x4, NULL, #0x30  ; false
    // 0xbba0f8: b               #0xbba100
    // 0xbba0fc: r4 = true
    //     0xbba0fc: add             x4, NULL, #0x20  ; true
    // 0xbba100: mov             x3, x4
    // 0xbba104: cmp             w3, NULL
    // 0xbba108: b.eq            #0xbba120
    // 0xbba10c: tbnz            w3, #4, #0xbba120
    // 0xbba110: cmp             w0, NULL
    // 0xbba114: b.ne            #0xbba13c
    // 0xbba118: r0 = ""
    //     0xbba118: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbba11c: b               #0xbba13c
    // 0xbba120: LoadField: r0 = r1->field_8f
    //     0xbba120: ldur            w0, [x1, #0x8f]
    // 0xbba124: DecompressPointer r0
    //     0xbba124: add             x0, x0, HEAP, lsl #32
    // 0xbba128: mov             x1, x0
    // 0xbba12c: r0 = value()
    //     0xbba12c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba130: LoadField: r1 = r0->field_47
    //     0xbba130: ldur            w1, [x0, #0x47]
    // 0xbba134: DecompressPointer r1
    //     0xbba134: add             x1, x1, HEAP, lsl #32
    // 0xbba138: mov             x0, x1
    // 0xbba13c: mov             x4, x0
    // 0xbba140: ldur            x2, [fp, #-8]
    // 0xbba144: ldur            x3, [fp, #-0x18]
    // 0xbba148: ldur            x1, [fp, #-0x20]
    // 0xbba14c: ldur            x0, [fp, #-0x28]
    // 0xbba150: stur            x4, [fp, #-0x30]
    // 0xbba154: r0 = CustomisedStrip()
    //     0xbba154: bl              #0x9d72b8  ; AllocateCustomisedStripStub -> CustomisedStrip (size=0x18)
    // 0xbba158: mov             x1, x0
    // 0xbba15c: ldur            x0, [fp, #-0x20]
    // 0xbba160: stur            x1, [fp, #-0x38]
    // 0xbba164: StoreField: r1->field_b = r0
    //     0xbba164: stur            w0, [x1, #0xb]
    // 0xbba168: ldur            x0, [fp, #-0x28]
    // 0xbba16c: StoreField: r1->field_f = r0
    //     0xbba16c: stur            w0, [x1, #0xf]
    // 0xbba170: ldur            x0, [fp, #-0x30]
    // 0xbba174: StoreField: r1->field_13 = r0
    //     0xbba174: stur            w0, [x1, #0x13]
    // 0xbba178: r0 = Padding()
    //     0xbba178: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbba17c: mov             x1, x0
    // 0xbba180: r0 = Instance_EdgeInsets
    //     0xbba180: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xbba184: ldr             x0, [x0, #0xb00]
    // 0xbba188: stur            x1, [fp, #-0x20]
    // 0xbba18c: StoreField: r1->field_f = r0
    //     0xbba18c: stur            w0, [x1, #0xf]
    // 0xbba190: ldur            x2, [fp, #-0x38]
    // 0xbba194: StoreField: r1->field_b = r2
    //     0xbba194: stur            w2, [x1, #0xb]
    // 0xbba198: r0 = Visibility()
    //     0xbba198: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbba19c: mov             x1, x0
    // 0xbba1a0: ldur            x0, [fp, #-0x20]
    // 0xbba1a4: StoreField: r1->field_b = r0
    //     0xbba1a4: stur            w0, [x1, #0xb]
    // 0xbba1a8: r2 = Instance_SizedBox
    //     0xbba1a8: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbba1ac: StoreField: r1->field_f = r2
    //     0xbba1ac: stur            w2, [x1, #0xf]
    // 0xbba1b0: ldur            x0, [fp, #-0x18]
    // 0xbba1b4: StoreField: r1->field_13 = r0
    //     0xbba1b4: stur            w0, [x1, #0x13]
    // 0xbba1b8: r3 = false
    //     0xbba1b8: add             x3, NULL, #0x30  ; false
    // 0xbba1bc: ArrayStore: r1[0] = r3  ; List_4
    //     0xbba1bc: stur            w3, [x1, #0x17]
    // 0xbba1c0: StoreField: r1->field_1b = r3
    //     0xbba1c0: stur            w3, [x1, #0x1b]
    // 0xbba1c4: StoreField: r1->field_1f = r3
    //     0xbba1c4: stur            w3, [x1, #0x1f]
    // 0xbba1c8: StoreField: r1->field_23 = r3
    //     0xbba1c8: stur            w3, [x1, #0x23]
    // 0xbba1cc: StoreField: r1->field_27 = r3
    //     0xbba1cc: stur            w3, [x1, #0x27]
    // 0xbba1d0: StoreField: r1->field_2b = r3
    //     0xbba1d0: stur            w3, [x1, #0x2b]
    // 0xbba1d4: mov             x0, x1
    // 0xbba1d8: ldur            x1, [fp, #-0x10]
    // 0xbba1dc: ArrayStore: r1[3] = r0  ; List_4
    //     0xbba1dc: add             x25, x1, #0x1b
    //     0xbba1e0: str             w0, [x25]
    //     0xbba1e4: tbz             w0, #0, #0xbba200
    //     0xbba1e8: ldurb           w16, [x1, #-1]
    //     0xbba1ec: ldurb           w17, [x0, #-1]
    //     0xbba1f0: and             x16, x17, x16, lsr #2
    //     0xbba1f4: tst             x16, HEAP, lsr #32
    //     0xbba1f8: b.eq            #0xbba200
    //     0xbba1fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbba200: ldur            x1, [fp, #-8]
    // 0xbba204: LoadField: r0 = r1->field_f
    //     0xbba204: ldur            w0, [x1, #0xf]
    // 0xbba208: DecompressPointer r0
    //     0xbba208: add             x0, x0, HEAP, lsl #32
    // 0xbba20c: LoadField: r4 = r0->field_b
    //     0xbba20c: ldur            w4, [x0, #0xb]
    // 0xbba210: DecompressPointer r4
    //     0xbba210: add             x4, x4, HEAP, lsl #32
    // 0xbba214: cmp             w4, NULL
    // 0xbba218: b.eq            #0xbbb430
    // 0xbba21c: LoadField: r0 = r4->field_f
    //     0xbba21c: ldur            w0, [x4, #0xf]
    // 0xbba220: DecompressPointer r0
    //     0xbba220: add             x0, x0, HEAP, lsl #32
    // 0xbba224: r4 = LoadClassIdInstr(r0)
    //     0xbba224: ldur            x4, [x0, #-1]
    //     0xbba228: ubfx            x4, x4, #0xc, #0x14
    // 0xbba22c: r16 = "size"
    //     0xbba22c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbba230: ldr             x16, [x16, #0x9c0]
    // 0xbba234: stp             x16, x0, [SP]
    // 0xbba238: mov             x0, x4
    // 0xbba23c: mov             lr, x0
    // 0xbba240: ldr             lr, [x21, lr, lsl #3]
    // 0xbba244: blr             lr
    // 0xbba248: tbnz            w0, #4, #0xbba2b4
    // 0xbba24c: ldur            x2, [fp, #-8]
    // 0xbba250: LoadField: r1 = r2->field_13
    //     0xbba250: ldur            w1, [x2, #0x13]
    // 0xbba254: DecompressPointer r1
    //     0xbba254: add             x1, x1, HEAP, lsl #32
    // 0xbba258: r0 = of()
    //     0xbba258: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbba25c: LoadField: r1 = r0->field_87
    //     0xbba25c: ldur            w1, [x0, #0x87]
    // 0xbba260: DecompressPointer r1
    //     0xbba260: add             x1, x1, HEAP, lsl #32
    // 0xbba264: LoadField: r0 = r1->field_7
    //     0xbba264: ldur            w0, [x1, #7]
    // 0xbba268: DecompressPointer r0
    //     0xbba268: add             x0, x0, HEAP, lsl #32
    // 0xbba26c: r16 = Instance_Color
    //     0xbba26c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbba270: r30 = 14.000000
    //     0xbba270: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbba274: ldr             lr, [lr, #0x1d8]
    // 0xbba278: stp             lr, x16, [SP]
    // 0xbba27c: mov             x1, x0
    // 0xbba280: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbba280: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbba284: ldr             x4, [x4, #0x9b8]
    // 0xbba288: r0 = copyWith()
    //     0xbba288: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbba28c: stur            x0, [fp, #-0x18]
    // 0xbba290: r0 = Text()
    //     0xbba290: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbba294: mov             x1, x0
    // 0xbba298: r0 = "Select Size"
    //     0xbba298: add             x0, PP, #0x52, lsl #12  ; [pp+0x52370] "Select Size"
    //     0xbba29c: ldr             x0, [x0, #0x370]
    // 0xbba2a0: StoreField: r1->field_b = r0
    //     0xbba2a0: stur            w0, [x1, #0xb]
    // 0xbba2a4: ldur            x0, [fp, #-0x18]
    // 0xbba2a8: StoreField: r1->field_13 = r0
    //     0xbba2a8: stur            w0, [x1, #0x13]
    // 0xbba2ac: mov             x0, x1
    // 0xbba2b0: b               #0xbba318
    // 0xbba2b4: ldur            x2, [fp, #-8]
    // 0xbba2b8: LoadField: r1 = r2->field_13
    //     0xbba2b8: ldur            w1, [x2, #0x13]
    // 0xbba2bc: DecompressPointer r1
    //     0xbba2bc: add             x1, x1, HEAP, lsl #32
    // 0xbba2c0: r0 = of()
    //     0xbba2c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbba2c4: LoadField: r1 = r0->field_87
    //     0xbba2c4: ldur            w1, [x0, #0x87]
    // 0xbba2c8: DecompressPointer r1
    //     0xbba2c8: add             x1, x1, HEAP, lsl #32
    // 0xbba2cc: LoadField: r0 = r1->field_7
    //     0xbba2cc: ldur            w0, [x1, #7]
    // 0xbba2d0: DecompressPointer r0
    //     0xbba2d0: add             x0, x0, HEAP, lsl #32
    // 0xbba2d4: r16 = Instance_Color
    //     0xbba2d4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbba2d8: r30 = 14.000000
    //     0xbba2d8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbba2dc: ldr             lr, [lr, #0x1d8]
    // 0xbba2e0: stp             lr, x16, [SP]
    // 0xbba2e4: mov             x1, x0
    // 0xbba2e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbba2e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbba2ec: ldr             x4, [x4, #0x9b8]
    // 0xbba2f0: r0 = copyWith()
    //     0xbba2f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbba2f4: stur            x0, [fp, #-0x18]
    // 0xbba2f8: r0 = Text()
    //     0xbba2f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbba2fc: mov             x1, x0
    // 0xbba300: r0 = "Select Variant"
    //     0xbba300: add             x0, PP, #0x52, lsl #12  ; [pp+0x52378] "Select Variant"
    //     0xbba304: ldr             x0, [x0, #0x378]
    // 0xbba308: StoreField: r1->field_b = r0
    //     0xbba308: stur            w0, [x1, #0xb]
    // 0xbba30c: ldur            x0, [fp, #-0x18]
    // 0xbba310: StoreField: r1->field_13 = r0
    //     0xbba310: stur            w0, [x1, #0x13]
    // 0xbba314: mov             x0, x1
    // 0xbba318: ldur            x2, [fp, #-8]
    // 0xbba31c: stur            x0, [fp, #-0x18]
    // 0xbba320: r0 = Align()
    //     0xbba320: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xbba324: mov             x1, x0
    // 0xbba328: r0 = Instance_Alignment
    //     0xbba328: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xbba32c: ldr             x0, [x0, #0xfa0]
    // 0xbba330: StoreField: r1->field_f = r0
    //     0xbba330: stur            w0, [x1, #0xf]
    // 0xbba334: ldur            x0, [fp, #-0x18]
    // 0xbba338: StoreField: r1->field_b = r0
    //     0xbba338: stur            w0, [x1, #0xb]
    // 0xbba33c: mov             x0, x1
    // 0xbba340: ldur            x1, [fp, #-0x10]
    // 0xbba344: ArrayStore: r1[4] = r0  ; List_4
    //     0xbba344: add             x25, x1, #0x1f
    //     0xbba348: str             w0, [x25]
    //     0xbba34c: tbz             w0, #0, #0xbba368
    //     0xbba350: ldurb           w16, [x1, #-1]
    //     0xbba354: ldurb           w17, [x0, #-1]
    //     0xbba358: and             x16, x17, x16, lsr #2
    //     0xbba35c: tst             x16, HEAP, lsr #32
    //     0xbba360: b.eq            #0xbba368
    //     0xbba364: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbba368: ldur            x2, [fp, #-8]
    // 0xbba36c: LoadField: r0 = r2->field_f
    //     0xbba36c: ldur            w0, [x2, #0xf]
    // 0xbba370: DecompressPointer r0
    //     0xbba370: add             x0, x0, HEAP, lsl #32
    // 0xbba374: LoadField: r1 = r0->field_1f
    //     0xbba374: ldur            w1, [x0, #0x1f]
    // 0xbba378: DecompressPointer r1
    //     0xbba378: add             x1, x1, HEAP, lsl #32
    // 0xbba37c: LoadField: r0 = r1->field_8f
    //     0xbba37c: ldur            w0, [x1, #0x8f]
    // 0xbba380: DecompressPointer r0
    //     0xbba380: add             x0, x0, HEAP, lsl #32
    // 0xbba384: mov             x1, x0
    // 0xbba388: r0 = value()
    //     0xbba388: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba38c: LoadField: r1 = r0->field_53
    //     0xbba38c: ldur            w1, [x0, #0x53]
    // 0xbba390: DecompressPointer r1
    //     0xbba390: add             x1, x1, HEAP, lsl #32
    // 0xbba394: cmp             w1, NULL
    // 0xbba398: b.ne            #0xbba3a4
    // 0xbba39c: r0 = Null
    //     0xbba39c: mov             x0, NULL
    // 0xbba3a0: b               #0xbba3bc
    // 0xbba3a4: LoadField: r0 = r1->field_b
    //     0xbba3a4: ldur            w0, [x1, #0xb]
    // 0xbba3a8: cbnz            w0, #0xbba3b4
    // 0xbba3ac: r1 = false
    //     0xbba3ac: add             x1, NULL, #0x30  ; false
    // 0xbba3b0: b               #0xbba3b8
    // 0xbba3b4: r1 = true
    //     0xbba3b4: add             x1, NULL, #0x20  ; true
    // 0xbba3b8: mov             x0, x1
    // 0xbba3bc: cmp             w0, NULL
    // 0xbba3c0: b.ne            #0xbba3c8
    // 0xbba3c4: r0 = false
    //     0xbba3c4: add             x0, NULL, #0x30  ; false
    // 0xbba3c8: ldur            x2, [fp, #-8]
    // 0xbba3cc: stur            x0, [fp, #-0x18]
    // 0xbba3d0: LoadField: r1 = r2->field_f
    //     0xbba3d0: ldur            w1, [x2, #0xf]
    // 0xbba3d4: DecompressPointer r1
    //     0xbba3d4: add             x1, x1, HEAP, lsl #32
    // 0xbba3d8: LoadField: r3 = r1->field_1f
    //     0xbba3d8: ldur            w3, [x1, #0x1f]
    // 0xbba3dc: DecompressPointer r3
    //     0xbba3dc: add             x3, x3, HEAP, lsl #32
    // 0xbba3e0: LoadField: r1 = r3->field_8f
    //     0xbba3e0: ldur            w1, [x3, #0x8f]
    // 0xbba3e4: DecompressPointer r1
    //     0xbba3e4: add             x1, x1, HEAP, lsl #32
    // 0xbba3e8: r0 = value()
    //     0xbba3e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba3ec: LoadField: r1 = r0->field_53
    //     0xbba3ec: ldur            w1, [x0, #0x53]
    // 0xbba3f0: DecompressPointer r1
    //     0xbba3f0: add             x1, x1, HEAP, lsl #32
    // 0xbba3f4: cmp             w1, NULL
    // 0xbba3f8: b.ne            #0xbba404
    // 0xbba3fc: r0 = Null
    //     0xbba3fc: mov             x0, NULL
    // 0xbba400: b               #0xbba408
    // 0xbba404: LoadField: r0 = r1->field_b
    //     0xbba404: ldur            w0, [x1, #0xb]
    // 0xbba408: cmp             w0, NULL
    // 0xbba40c: b.ne            #0xbba418
    // 0xbba410: r4 = 0
    //     0xbba410: movz            x4, #0
    // 0xbba414: b               #0xbba420
    // 0xbba418: r1 = LoadInt32Instr(r0)
    //     0xbba418: sbfx            x1, x0, #1, #0x1f
    // 0xbba41c: mov             x4, x1
    // 0xbba420: ldur            x0, [fp, #-8]
    // 0xbba424: ldur            x3, [fp, #-0x18]
    // 0xbba428: mov             x2, x0
    // 0xbba42c: stur            x4, [fp, #-0x48]
    // 0xbba430: r1 = Function '<anonymous closure>':.
    //     0xbba430: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3a8] AnonymousClosure: (0xbbb774), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbba434: ldr             x1, [x1, #0x3a8]
    // 0xbba438: r0 = AllocateClosure()
    //     0xbba438: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbba43c: r1 = Function '<anonymous closure>':.
    //     0xbba43c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3b0] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0xbba440: ldr             x1, [x1, #0x3b0]
    // 0xbba444: r2 = Null
    //     0xbba444: mov             x2, NULL
    // 0xbba448: stur            x0, [fp, #-0x20]
    // 0xbba44c: r0 = AllocateClosure()
    //     0xbba44c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbba450: stur            x0, [fp, #-0x28]
    // 0xbba454: r0 = ListView()
    //     0xbba454: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbba458: stur            x0, [fp, #-0x30]
    // 0xbba45c: r16 = true
    //     0xbba45c: add             x16, NULL, #0x20  ; true
    // 0xbba460: r30 = Instance_Axis
    //     0xbba460: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbba464: stp             lr, x16, [SP]
    // 0xbba468: mov             x1, x0
    // 0xbba46c: ldur            x2, [fp, #-0x20]
    // 0xbba470: ldur            x3, [fp, #-0x48]
    // 0xbba474: ldur            x5, [fp, #-0x28]
    // 0xbba478: r4 = const [0, 0x6, 0x2, 0x4, scrollDirection, 0x5, shrinkWrap, 0x4, null]
    //     0xbba478: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f8e8] List(9) [0, 0x6, 0x2, 0x4, "scrollDirection", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbba47c: ldr             x4, [x4, #0x8e8]
    // 0xbba480: r0 = ListView.separated()
    //     0xbba480: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbba484: r0 = SizedBox()
    //     0xbba484: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbba488: mov             x1, x0
    // 0xbba48c: r0 = 44.000000
    //     0xbba48c: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xbba490: ldr             x0, [x0, #0xad8]
    // 0xbba494: stur            x1, [fp, #-0x20]
    // 0xbba498: StoreField: r1->field_13 = r0
    //     0xbba498: stur            w0, [x1, #0x13]
    // 0xbba49c: ldur            x2, [fp, #-0x30]
    // 0xbba4a0: StoreField: r1->field_b = r2
    //     0xbba4a0: stur            w2, [x1, #0xb]
    // 0xbba4a4: r0 = Padding()
    //     0xbba4a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbba4a8: mov             x1, x0
    // 0xbba4ac: r0 = Instance_EdgeInsets
    //     0xbba4ac: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xbba4b0: ldr             x0, [x0, #0xb00]
    // 0xbba4b4: stur            x1, [fp, #-0x28]
    // 0xbba4b8: StoreField: r1->field_f = r0
    //     0xbba4b8: stur            w0, [x1, #0xf]
    // 0xbba4bc: ldur            x0, [fp, #-0x20]
    // 0xbba4c0: StoreField: r1->field_b = r0
    //     0xbba4c0: stur            w0, [x1, #0xb]
    // 0xbba4c4: r0 = Visibility()
    //     0xbba4c4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbba4c8: mov             x1, x0
    // 0xbba4cc: ldur            x0, [fp, #-0x28]
    // 0xbba4d0: StoreField: r1->field_b = r0
    //     0xbba4d0: stur            w0, [x1, #0xb]
    // 0xbba4d4: r2 = Instance_SizedBox
    //     0xbba4d4: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbba4d8: StoreField: r1->field_f = r2
    //     0xbba4d8: stur            w2, [x1, #0xf]
    // 0xbba4dc: ldur            x0, [fp, #-0x18]
    // 0xbba4e0: StoreField: r1->field_13 = r0
    //     0xbba4e0: stur            w0, [x1, #0x13]
    // 0xbba4e4: r3 = false
    //     0xbba4e4: add             x3, NULL, #0x30  ; false
    // 0xbba4e8: ArrayStore: r1[0] = r3  ; List_4
    //     0xbba4e8: stur            w3, [x1, #0x17]
    // 0xbba4ec: StoreField: r1->field_1b = r3
    //     0xbba4ec: stur            w3, [x1, #0x1b]
    // 0xbba4f0: StoreField: r1->field_1f = r3
    //     0xbba4f0: stur            w3, [x1, #0x1f]
    // 0xbba4f4: StoreField: r1->field_23 = r3
    //     0xbba4f4: stur            w3, [x1, #0x23]
    // 0xbba4f8: StoreField: r1->field_27 = r3
    //     0xbba4f8: stur            w3, [x1, #0x27]
    // 0xbba4fc: StoreField: r1->field_2b = r3
    //     0xbba4fc: stur            w3, [x1, #0x2b]
    // 0xbba500: mov             x0, x1
    // 0xbba504: ldur            x1, [fp, #-0x10]
    // 0xbba508: ArrayStore: r1[5] = r0  ; List_4
    //     0xbba508: add             x25, x1, #0x23
    //     0xbba50c: str             w0, [x25]
    //     0xbba510: tbz             w0, #0, #0xbba52c
    //     0xbba514: ldurb           w16, [x1, #-1]
    //     0xbba518: ldurb           w17, [x0, #-1]
    //     0xbba51c: and             x16, x17, x16, lsr #2
    //     0xbba520: tst             x16, HEAP, lsr #32
    //     0xbba524: b.eq            #0xbba52c
    //     0xbba528: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbba52c: ldur            x0, [fp, #-8]
    // 0xbba530: LoadField: r1 = r0->field_f
    //     0xbba530: ldur            w1, [x0, #0xf]
    // 0xbba534: DecompressPointer r1
    //     0xbba534: add             x1, x1, HEAP, lsl #32
    // 0xbba538: LoadField: r4 = r1->field_1f
    //     0xbba538: ldur            w4, [x1, #0x1f]
    // 0xbba53c: DecompressPointer r4
    //     0xbba53c: add             x4, x4, HEAP, lsl #32
    // 0xbba540: LoadField: r1 = r4->field_8f
    //     0xbba540: ldur            w1, [x4, #0x8f]
    // 0xbba544: DecompressPointer r1
    //     0xbba544: add             x1, x1, HEAP, lsl #32
    // 0xbba548: r0 = value()
    //     0xbba548: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba54c: LoadField: r1 = r0->field_4f
    //     0xbba54c: ldur            w1, [x0, #0x4f]
    // 0xbba550: DecompressPointer r1
    //     0xbba550: add             x1, x1, HEAP, lsl #32
    // 0xbba554: cmp             w1, NULL
    // 0xbba558: b.eq            #0xbba5c0
    // 0xbba55c: ldur            x2, [fp, #-8]
    // 0xbba560: LoadField: r0 = r2->field_f
    //     0xbba560: ldur            w0, [x2, #0xf]
    // 0xbba564: DecompressPointer r0
    //     0xbba564: add             x0, x0, HEAP, lsl #32
    // 0xbba568: LoadField: r1 = r0->field_1f
    //     0xbba568: ldur            w1, [x0, #0x1f]
    // 0xbba56c: DecompressPointer r1
    //     0xbba56c: add             x1, x1, HEAP, lsl #32
    // 0xbba570: LoadField: r0 = r1->field_8f
    //     0xbba570: ldur            w0, [x1, #0x8f]
    // 0xbba574: DecompressPointer r0
    //     0xbba574: add             x0, x0, HEAP, lsl #32
    // 0xbba578: mov             x1, x0
    // 0xbba57c: r0 = value()
    //     0xbba57c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbba580: LoadField: r1 = r0->field_4f
    //     0xbba580: ldur            w1, [x0, #0x4f]
    // 0xbba584: DecompressPointer r1
    //     0xbba584: add             x1, x1, HEAP, lsl #32
    // 0xbba588: cmp             w1, NULL
    // 0xbba58c: b.ne            #0xbba598
    // 0xbba590: r0 = Null
    //     0xbba590: mov             x0, NULL
    // 0xbba594: b               #0xbba5b0
    // 0xbba598: LoadField: r0 = r1->field_7
    //     0xbba598: ldur            w0, [x1, #7]
    // 0xbba59c: cbnz            w0, #0xbba5a8
    // 0xbba5a0: r1 = false
    //     0xbba5a0: add             x1, NULL, #0x30  ; false
    // 0xbba5a4: b               #0xbba5ac
    // 0xbba5a8: r1 = true
    //     0xbba5a8: add             x1, NULL, #0x20  ; true
    // 0xbba5ac: mov             x0, x1
    // 0xbba5b0: cmp             w0, NULL
    // 0xbba5b4: b.ne            #0xbba5c4
    // 0xbba5b8: r0 = false
    //     0xbba5b8: add             x0, NULL, #0x30  ; false
    // 0xbba5bc: b               #0xbba5c4
    // 0xbba5c0: r0 = false
    //     0xbba5c0: add             x0, NULL, #0x30  ; false
    // 0xbba5c4: ldur            x2, [fp, #-8]
    // 0xbba5c8: ldur            x1, [fp, #-0x10]
    // 0xbba5cc: stur            x0, [fp, #-0x18]
    // 0xbba5d0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbba5d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbba5d4: ldr             x0, [x0, #0x1c80]
    //     0xbba5d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbba5dc: cmp             w0, w16
    //     0xbba5e0: b.ne            #0xbba5ec
    //     0xbba5e4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbba5e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbba5ec: r0 = GetNavigation.size()
    //     0xbba5ec: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbba5f0: LoadField: d0 = r0->field_7
    //     0xbba5f0: ldur            d0, [x0, #7]
    // 0xbba5f4: d1 = 0.940000
    //     0xbba5f4: add             x17, PP, #0x54, lsl #12  ; [pp+0x542f0] IMM: double(0.94) from 0x3fee147ae147ae14
    //     0xbba5f8: ldr             d1, [x17, #0x2f0]
    // 0xbba5fc: fmul            d2, d0, d1
    // 0xbba600: stur            d2, [fp, #-0x50]
    // 0xbba604: r16 = <EdgeInsets>
    //     0xbba604: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbba608: ldr             x16, [x16, #0xda0]
    // 0xbba60c: r30 = Instance_EdgeInsets
    //     0xbba60c: add             lr, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0xbba610: ldr             lr, [lr, #0x670]
    // 0xbba614: stp             lr, x16, [SP]
    // 0xbba618: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbba618: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbba61c: r0 = all()
    //     0xbba61c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbba620: ldur            x2, [fp, #-8]
    // 0xbba624: stur            x0, [fp, #-0x20]
    // 0xbba628: LoadField: r1 = r2->field_13
    //     0xbba628: ldur            w1, [x2, #0x13]
    // 0xbba62c: DecompressPointer r1
    //     0xbba62c: add             x1, x1, HEAP, lsl #32
    // 0xbba630: r0 = of()
    //     0xbba630: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbba634: LoadField: r1 = r0->field_5b
    //     0xbba634: ldur            w1, [x0, #0x5b]
    // 0xbba638: DecompressPointer r1
    //     0xbba638: add             x1, x1, HEAP, lsl #32
    // 0xbba63c: r0 = LoadClassIdInstr(r1)
    //     0xbba63c: ldur            x0, [x1, #-1]
    //     0xbba640: ubfx            x0, x0, #0xc, #0x14
    // 0xbba644: d0 = 0.100000
    //     0xbba644: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbba648: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbba648: sub             lr, x0, #0xffa
    //     0xbba64c: ldr             lr, [x21, lr, lsl #3]
    //     0xbba650: blr             lr
    // 0xbba654: stur            x0, [fp, #-0x28]
    // 0xbba658: r0 = BorderSide()
    //     0xbba658: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbba65c: mov             x1, x0
    // 0xbba660: ldur            x0, [fp, #-0x28]
    // 0xbba664: stur            x1, [fp, #-0x30]
    // 0xbba668: StoreField: r1->field_7 = r0
    //     0xbba668: stur            w0, [x1, #7]
    // 0xbba66c: d0 = 1.000000
    //     0xbba66c: fmov            d0, #1.00000000
    // 0xbba670: StoreField: r1->field_b = d0
    //     0xbba670: stur            d0, [x1, #0xb]
    // 0xbba674: r0 = Instance_BorderStyle
    //     0xbba674: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbba678: ldr             x0, [x0, #0xf68]
    // 0xbba67c: StoreField: r1->field_13 = r0
    //     0xbba67c: stur            w0, [x1, #0x13]
    // 0xbba680: d1 = -1.000000
    //     0xbba680: fmov            d1, #-1.00000000
    // 0xbba684: ArrayStore: r1[0] = d1  ; List_8
    //     0xbba684: stur            d1, [x1, #0x17]
    // 0xbba688: r0 = RoundedRectangleBorder()
    //     0xbba688: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbba68c: mov             x1, x0
    // 0xbba690: r0 = Instance_BorderRadius
    //     0xbba690: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbba694: ldr             x0, [x0, #0xf70]
    // 0xbba698: StoreField: r1->field_b = r0
    //     0xbba698: stur            w0, [x1, #0xb]
    // 0xbba69c: ldur            x2, [fp, #-0x30]
    // 0xbba6a0: StoreField: r1->field_7 = r2
    //     0xbba6a0: stur            w2, [x1, #7]
    // 0xbba6a4: r16 = <RoundedRectangleBorder>
    //     0xbba6a4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbba6a8: ldr             x16, [x16, #0xf78]
    // 0xbba6ac: stp             x1, x16, [SP]
    // 0xbba6b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbba6b0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbba6b4: r0 = all()
    //     0xbba6b4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbba6b8: stur            x0, [fp, #-0x28]
    // 0xbba6bc: r0 = ButtonStyle()
    //     0xbba6bc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbba6c0: mov             x1, x0
    // 0xbba6c4: ldur            x0, [fp, #-0x20]
    // 0xbba6c8: stur            x1, [fp, #-0x30]
    // 0xbba6cc: StoreField: r1->field_23 = r0
    //     0xbba6cc: stur            w0, [x1, #0x23]
    // 0xbba6d0: ldur            x0, [fp, #-0x28]
    // 0xbba6d4: StoreField: r1->field_43 = r0
    //     0xbba6d4: stur            w0, [x1, #0x43]
    // 0xbba6d8: r0 = TextButtonThemeData()
    //     0xbba6d8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbba6dc: mov             x2, x0
    // 0xbba6e0: ldur            x0, [fp, #-0x30]
    // 0xbba6e4: stur            x2, [fp, #-0x20]
    // 0xbba6e8: StoreField: r2->field_7 = r0
    //     0xbba6e8: stur            w0, [x2, #7]
    // 0xbba6ec: ldur            x0, [fp, #-8]
    // 0xbba6f0: LoadField: r1 = r0->field_13
    //     0xbba6f0: ldur            w1, [x0, #0x13]
    // 0xbba6f4: DecompressPointer r1
    //     0xbba6f4: add             x1, x1, HEAP, lsl #32
    // 0xbba6f8: r0 = of()
    //     0xbba6f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbba6fc: LoadField: r1 = r0->field_87
    //     0xbba6fc: ldur            w1, [x0, #0x87]
    // 0xbba700: DecompressPointer r1
    //     0xbba700: add             x1, x1, HEAP, lsl #32
    // 0xbba704: LoadField: r0 = r1->field_7
    //     0xbba704: ldur            w0, [x1, #7]
    // 0xbba708: DecompressPointer r0
    //     0xbba708: add             x0, x0, HEAP, lsl #32
    // 0xbba70c: ldur            x2, [fp, #-8]
    // 0xbba710: stur            x0, [fp, #-0x28]
    // 0xbba714: LoadField: r1 = r2->field_13
    //     0xbba714: ldur            w1, [x2, #0x13]
    // 0xbba718: DecompressPointer r1
    //     0xbba718: add             x1, x1, HEAP, lsl #32
    // 0xbba71c: r0 = of()
    //     0xbba71c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbba720: LoadField: r1 = r0->field_5b
    //     0xbba720: ldur            w1, [x0, #0x5b]
    // 0xbba724: DecompressPointer r1
    //     0xbba724: add             x1, x1, HEAP, lsl #32
    // 0xbba728: r16 = 14.000000
    //     0xbba728: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbba72c: ldr             x16, [x16, #0x1d8]
    // 0xbba730: stp             x1, x16, [SP]
    // 0xbba734: ldur            x1, [fp, #-0x28]
    // 0xbba738: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbba738: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbba73c: ldr             x4, [x4, #0xaa0]
    // 0xbba740: r0 = copyWith()
    //     0xbba740: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbba744: stur            x0, [fp, #-0x28]
    // 0xbba748: r0 = Text()
    //     0xbba748: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbba74c: mov             x3, x0
    // 0xbba750: r0 = "SIZE CHART"
    //     0xbba750: add             x0, PP, #0x38, lsl #12  ; [pp+0x380c0] "SIZE CHART"
    //     0xbba754: ldr             x0, [x0, #0xc0]
    // 0xbba758: stur            x3, [fp, #-0x30]
    // 0xbba75c: StoreField: r3->field_b = r0
    //     0xbba75c: stur            w0, [x3, #0xb]
    // 0xbba760: ldur            x0, [fp, #-0x28]
    // 0xbba764: StoreField: r3->field_13 = r0
    //     0xbba764: stur            w0, [x3, #0x13]
    // 0xbba768: ldur            x2, [fp, #-8]
    // 0xbba76c: r1 = Function '<anonymous closure>':.
    //     0xbba76c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3b8] AnonymousClosure: (0xbbb660), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbba770: ldr             x1, [x1, #0x3b8]
    // 0xbba774: r0 = AllocateClosure()
    //     0xbba774: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbba778: stur            x0, [fp, #-0x28]
    // 0xbba77c: r0 = TextButton()
    //     0xbba77c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbba780: mov             x1, x0
    // 0xbba784: ldur            x0, [fp, #-0x28]
    // 0xbba788: stur            x1, [fp, #-0x38]
    // 0xbba78c: StoreField: r1->field_b = r0
    //     0xbba78c: stur            w0, [x1, #0xb]
    // 0xbba790: r0 = false
    //     0xbba790: add             x0, NULL, #0x30  ; false
    // 0xbba794: StoreField: r1->field_27 = r0
    //     0xbba794: stur            w0, [x1, #0x27]
    // 0xbba798: r2 = true
    //     0xbba798: add             x2, NULL, #0x20  ; true
    // 0xbba79c: StoreField: r1->field_2f = r2
    //     0xbba79c: stur            w2, [x1, #0x2f]
    // 0xbba7a0: ldur            x3, [fp, #-0x30]
    // 0xbba7a4: StoreField: r1->field_37 = r3
    //     0xbba7a4: stur            w3, [x1, #0x37]
    // 0xbba7a8: r0 = TextButtonTheme()
    //     0xbba7a8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbba7ac: mov             x1, x0
    // 0xbba7b0: ldur            x0, [fp, #-0x20]
    // 0xbba7b4: stur            x1, [fp, #-0x28]
    // 0xbba7b8: StoreField: r1->field_f = r0
    //     0xbba7b8: stur            w0, [x1, #0xf]
    // 0xbba7bc: ldur            x0, [fp, #-0x38]
    // 0xbba7c0: StoreField: r1->field_b = r0
    //     0xbba7c0: stur            w0, [x1, #0xb]
    // 0xbba7c4: ldur            d0, [fp, #-0x50]
    // 0xbba7c8: r0 = inline_Allocate_Double()
    //     0xbba7c8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbba7cc: add             x0, x0, #0x10
    //     0xbba7d0: cmp             x2, x0
    //     0xbba7d4: b.ls            #0xbbb434
    //     0xbba7d8: str             x0, [THR, #0x50]  ; THR::top
    //     0xbba7dc: sub             x0, x0, #0xf
    //     0xbba7e0: movz            x2, #0xe15c
    //     0xbba7e4: movk            x2, #0x3, lsl #16
    //     0xbba7e8: stur            x2, [x0, #-1]
    // 0xbba7ec: StoreField: r0->field_7 = d0
    //     0xbba7ec: stur            d0, [x0, #7]
    // 0xbba7f0: stur            x0, [fp, #-0x20]
    // 0xbba7f4: r0 = SizedBox()
    //     0xbba7f4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbba7f8: mov             x1, x0
    // 0xbba7fc: ldur            x0, [fp, #-0x20]
    // 0xbba800: stur            x1, [fp, #-0x30]
    // 0xbba804: StoreField: r1->field_f = r0
    //     0xbba804: stur            w0, [x1, #0xf]
    // 0xbba808: r0 = 45.000000
    //     0xbba808: add             x0, PP, #0x52, lsl #12  ; [pp+0x52c88] 45
    //     0xbba80c: ldr             x0, [x0, #0xc88]
    // 0xbba810: StoreField: r1->field_13 = r0
    //     0xbba810: stur            w0, [x1, #0x13]
    // 0xbba814: ldur            x0, [fp, #-0x28]
    // 0xbba818: StoreField: r1->field_b = r0
    //     0xbba818: stur            w0, [x1, #0xb]
    // 0xbba81c: r0 = Padding()
    //     0xbba81c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbba820: mov             x1, x0
    // 0xbba824: r0 = Instance_EdgeInsets
    //     0xbba824: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b3d8] Obj!EdgeInsets@d58011
    //     0xbba828: ldr             x0, [x0, #0x3d8]
    // 0xbba82c: stur            x1, [fp, #-0x20]
    // 0xbba830: StoreField: r1->field_f = r0
    //     0xbba830: stur            w0, [x1, #0xf]
    // 0xbba834: ldur            x2, [fp, #-0x30]
    // 0xbba838: StoreField: r1->field_b = r2
    //     0xbba838: stur            w2, [x1, #0xb]
    // 0xbba83c: r0 = Visibility()
    //     0xbba83c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbba840: mov             x1, x0
    // 0xbba844: ldur            x0, [fp, #-0x20]
    // 0xbba848: StoreField: r1->field_b = r0
    //     0xbba848: stur            w0, [x1, #0xb]
    // 0xbba84c: r0 = Instance_SizedBox
    //     0xbba84c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbba850: StoreField: r1->field_f = r0
    //     0xbba850: stur            w0, [x1, #0xf]
    // 0xbba854: ldur            x0, [fp, #-0x18]
    // 0xbba858: StoreField: r1->field_13 = r0
    //     0xbba858: stur            w0, [x1, #0x13]
    // 0xbba85c: r2 = false
    //     0xbba85c: add             x2, NULL, #0x30  ; false
    // 0xbba860: ArrayStore: r1[0] = r2  ; List_4
    //     0xbba860: stur            w2, [x1, #0x17]
    // 0xbba864: StoreField: r1->field_1b = r2
    //     0xbba864: stur            w2, [x1, #0x1b]
    // 0xbba868: StoreField: r1->field_1f = r2
    //     0xbba868: stur            w2, [x1, #0x1f]
    // 0xbba86c: StoreField: r1->field_23 = r2
    //     0xbba86c: stur            w2, [x1, #0x23]
    // 0xbba870: StoreField: r1->field_27 = r2
    //     0xbba870: stur            w2, [x1, #0x27]
    // 0xbba874: StoreField: r1->field_2b = r2
    //     0xbba874: stur            w2, [x1, #0x2b]
    // 0xbba878: mov             x0, x1
    // 0xbba87c: ldur            x1, [fp, #-0x10]
    // 0xbba880: ArrayStore: r1[6] = r0  ; List_4
    //     0xbba880: add             x25, x1, #0x27
    //     0xbba884: str             w0, [x25]
    //     0xbba888: tbz             w0, #0, #0xbba8a4
    //     0xbba88c: ldurb           w16, [x1, #-1]
    //     0xbba890: ldurb           w17, [x0, #-1]
    //     0xbba894: and             x16, x17, x16, lsr #2
    //     0xbba898: tst             x16, HEAP, lsr #32
    //     0xbba89c: b.eq            #0xbba8a4
    //     0xbba8a0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbba8a4: ldur            x0, [fp, #-8]
    // 0xbba8a8: LoadField: r1 = r0->field_13
    //     0xbba8a8: ldur            w1, [x0, #0x13]
    // 0xbba8ac: DecompressPointer r1
    //     0xbba8ac: add             x1, x1, HEAP, lsl #32
    // 0xbba8b0: r0 = of()
    //     0xbba8b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbba8b4: LoadField: r1 = r0->field_87
    //     0xbba8b4: ldur            w1, [x0, #0x87]
    // 0xbba8b8: DecompressPointer r1
    //     0xbba8b8: add             x1, x1, HEAP, lsl #32
    // 0xbba8bc: LoadField: r0 = r1->field_7
    //     0xbba8bc: ldur            w0, [x1, #7]
    // 0xbba8c0: DecompressPointer r0
    //     0xbba8c0: add             x0, x0, HEAP, lsl #32
    // 0xbba8c4: r16 = Instance_Color
    //     0xbba8c4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbba8c8: r30 = 14.000000
    //     0xbba8c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbba8cc: ldr             lr, [lr, #0x1d8]
    // 0xbba8d0: stp             lr, x16, [SP]
    // 0xbba8d4: mov             x1, x0
    // 0xbba8d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbba8d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbba8dc: ldr             x4, [x4, #0x9b8]
    // 0xbba8e0: r0 = copyWith()
    //     0xbba8e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbba8e4: stur            x0, [fp, #-0x18]
    // 0xbba8e8: r0 = Text()
    //     0xbba8e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbba8ec: mov             x2, x0
    // 0xbba8f0: r0 = "Select Quantity"
    //     0xbba8f0: add             x0, PP, #0x56, lsl #12  ; [pp+0x56ce8] "Select Quantity"
    //     0xbba8f4: ldr             x0, [x0, #0xce8]
    // 0xbba8f8: stur            x2, [fp, #-0x20]
    // 0xbba8fc: StoreField: r2->field_b = r0
    //     0xbba8fc: stur            w0, [x2, #0xb]
    // 0xbba900: ldur            x0, [fp, #-0x18]
    // 0xbba904: StoreField: r2->field_13 = r0
    //     0xbba904: stur            w0, [x2, #0x13]
    // 0xbba908: r1 = Instance_Color
    //     0xbba908: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbba90c: d0 = 0.100000
    //     0xbba90c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbba910: r0 = withOpacity()
    //     0xbba910: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbba914: r16 = 1.000000
    //     0xbba914: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbba918: str             x16, [SP]
    // 0xbba91c: mov             x2, x0
    // 0xbba920: r1 = Null
    //     0xbba920: mov             x1, NULL
    // 0xbba924: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbba924: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbba928: ldr             x4, [x4, #0x108]
    // 0xbba92c: r0 = Border.all()
    //     0xbba92c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbba930: stur            x0, [fp, #-0x18]
    // 0xbba934: r0 = BoxDecoration()
    //     0xbba934: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbba938: mov             x2, x0
    // 0xbba93c: ldur            x0, [fp, #-0x18]
    // 0xbba940: stur            x2, [fp, #-0x28]
    // 0xbba944: StoreField: r2->field_f = r0
    //     0xbba944: stur            w0, [x2, #0xf]
    // 0xbba948: r0 = Instance_BoxShape
    //     0xbba948: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbba94c: ldr             x0, [x0, #0x80]
    // 0xbba950: StoreField: r2->field_23 = r0
    //     0xbba950: stur            w0, [x2, #0x23]
    // 0xbba954: ldur            x3, [fp, #-8]
    // 0xbba958: LoadField: r1 = r3->field_13
    //     0xbba958: ldur            w1, [x3, #0x13]
    // 0xbba95c: DecompressPointer r1
    //     0xbba95c: add             x1, x1, HEAP, lsl #32
    // 0xbba960: r0 = of()
    //     0xbba960: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbba964: LoadField: r1 = r0->field_5b
    //     0xbba964: ldur            w1, [x0, #0x5b]
    // 0xbba968: DecompressPointer r1
    //     0xbba968: add             x1, x1, HEAP, lsl #32
    // 0xbba96c: stur            x1, [fp, #-0x18]
    // 0xbba970: r0 = ColorFilter()
    //     0xbba970: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbba974: mov             x1, x0
    // 0xbba978: ldur            x0, [fp, #-0x18]
    // 0xbba97c: stur            x1, [fp, #-0x30]
    // 0xbba980: StoreField: r1->field_7 = r0
    //     0xbba980: stur            w0, [x1, #7]
    // 0xbba984: r0 = Instance_BlendMode
    //     0xbba984: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbba988: ldr             x0, [x0, #0xb30]
    // 0xbba98c: StoreField: r1->field_b = r0
    //     0xbba98c: stur            w0, [x1, #0xb]
    // 0xbba990: r2 = 1
    //     0xbba990: movz            x2, #0x1
    // 0xbba994: StoreField: r1->field_13 = r2
    //     0xbba994: stur            x2, [x1, #0x13]
    // 0xbba998: r0 = SvgPicture()
    //     0xbba998: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbba99c: stur            x0, [fp, #-0x18]
    // 0xbba9a0: ldur            x16, [fp, #-0x30]
    // 0xbba9a4: str             x16, [SP]
    // 0xbba9a8: mov             x1, x0
    // 0xbba9ac: r2 = "assets/images/minus_bag_item.svg"
    //     0xbba9ac: add             x2, PP, #0x59, lsl #12  ; [pp+0x59460] "assets/images/minus_bag_item.svg"
    //     0xbba9b0: ldr             x2, [x2, #0x460]
    // 0xbba9b4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbba9b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbba9b8: ldr             x4, [x4, #0xa38]
    // 0xbba9bc: r0 = SvgPicture.asset()
    //     0xbba9bc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbba9c0: r0 = Padding()
    //     0xbba9c0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbba9c4: mov             x1, x0
    // 0xbba9c8: r0 = Instance_EdgeInsets
    //     0xbba9c8: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b3e0] Obj!EdgeInsets@d57fe1
    //     0xbba9cc: ldr             x0, [x0, #0x3e0]
    // 0xbba9d0: stur            x1, [fp, #-0x30]
    // 0xbba9d4: StoreField: r1->field_f = r0
    //     0xbba9d4: stur            w0, [x1, #0xf]
    // 0xbba9d8: ldur            x0, [fp, #-0x18]
    // 0xbba9dc: StoreField: r1->field_b = r0
    //     0xbba9dc: stur            w0, [x1, #0xb]
    // 0xbba9e0: r0 = InkWell()
    //     0xbba9e0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbba9e4: mov             x3, x0
    // 0xbba9e8: ldur            x0, [fp, #-0x30]
    // 0xbba9ec: stur            x3, [fp, #-0x18]
    // 0xbba9f0: StoreField: r3->field_b = r0
    //     0xbba9f0: stur            w0, [x3, #0xb]
    // 0xbba9f4: ldur            x2, [fp, #-8]
    // 0xbba9f8: r1 = Function '<anonymous closure>':.
    //     0xbba9f8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3c0] AnonymousClosure: (0xa1c498), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xbba9fc: ldr             x1, [x1, #0x3c0]
    // 0xbbaa00: r0 = AllocateClosure()
    //     0xbbaa00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbaa04: mov             x1, x0
    // 0xbbaa08: ldur            x0, [fp, #-0x18]
    // 0xbbaa0c: StoreField: r0->field_f = r1
    //     0xbbaa0c: stur            w1, [x0, #0xf]
    // 0xbbaa10: r2 = true
    //     0xbbaa10: add             x2, NULL, #0x20  ; true
    // 0xbbaa14: StoreField: r0->field_43 = r2
    //     0xbbaa14: stur            w2, [x0, #0x43]
    // 0xbbaa18: r3 = Instance_BoxShape
    //     0xbbaa18: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbaa1c: ldr             x3, [x3, #0x80]
    // 0xbbaa20: StoreField: r0->field_47 = r3
    //     0xbbaa20: stur            w3, [x0, #0x47]
    // 0xbbaa24: StoreField: r0->field_6f = r2
    //     0xbbaa24: stur            w2, [x0, #0x6f]
    // 0xbbaa28: r4 = false
    //     0xbbaa28: add             x4, NULL, #0x30  ; false
    // 0xbbaa2c: StoreField: r0->field_73 = r4
    //     0xbbaa2c: stur            w4, [x0, #0x73]
    // 0xbbaa30: StoreField: r0->field_83 = r2
    //     0xbbaa30: stur            w2, [x0, #0x83]
    // 0xbbaa34: StoreField: r0->field_7b = r4
    //     0xbbaa34: stur            w4, [x0, #0x7b]
    // 0xbbaa38: ldur            x5, [fp, #-8]
    // 0xbbaa3c: LoadField: r1 = r5->field_f
    //     0xbbaa3c: ldur            w1, [x5, #0xf]
    // 0xbbaa40: DecompressPointer r1
    //     0xbbaa40: add             x1, x1, HEAP, lsl #32
    // 0xbbaa44: LoadField: r6 = r1->field_1f
    //     0xbbaa44: ldur            w6, [x1, #0x1f]
    // 0xbbaa48: DecompressPointer r6
    //     0xbbaa48: add             x6, x6, HEAP, lsl #32
    // 0xbbaa4c: r17 = 287
    //     0xbbaa4c: movz            x17, #0x11f
    // 0xbbaa50: ldr             w1, [x6, x17]
    // 0xbbaa54: DecompressPointer r1
    //     0xbbaa54: add             x1, x1, HEAP, lsl #32
    // 0xbbaa58: r0 = value()
    //     0xbbaa58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbbaa5c: r1 = 60
    //     0xbbaa5c: movz            x1, #0x3c
    // 0xbbaa60: branchIfSmi(r0, 0xbbaa6c)
    //     0xbbaa60: tbz             w0, #0, #0xbbaa6c
    // 0xbbaa64: r1 = LoadClassIdInstr(r0)
    //     0xbbaa64: ldur            x1, [x0, #-1]
    //     0xbbaa68: ubfx            x1, x1, #0xc, #0x14
    // 0xbbaa6c: str             x0, [SP]
    // 0xbbaa70: mov             x0, x1
    // 0xbbaa74: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbbaa74: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbbaa78: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbbaa78: movz            x17, #0x2700
    //     0xbbaa7c: add             lr, x0, x17
    //     0xbbaa80: ldr             lr, [x21, lr, lsl #3]
    //     0xbbaa84: blr             lr
    // 0xbbaa88: ldur            x2, [fp, #-8]
    // 0xbbaa8c: stur            x0, [fp, #-0x30]
    // 0xbbaa90: LoadField: r1 = r2->field_13
    //     0xbbaa90: ldur            w1, [x2, #0x13]
    // 0xbbaa94: DecompressPointer r1
    //     0xbbaa94: add             x1, x1, HEAP, lsl #32
    // 0xbbaa98: r0 = of()
    //     0xbbaa98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbaa9c: LoadField: r1 = r0->field_87
    //     0xbbaa9c: ldur            w1, [x0, #0x87]
    // 0xbbaaa0: DecompressPointer r1
    //     0xbbaaa0: add             x1, x1, HEAP, lsl #32
    // 0xbbaaa4: LoadField: r0 = r1->field_7
    //     0xbbaaa4: ldur            w0, [x1, #7]
    // 0xbbaaa8: DecompressPointer r0
    //     0xbbaaa8: add             x0, x0, HEAP, lsl #32
    // 0xbbaaac: stur            x0, [fp, #-0x38]
    // 0xbbaab0: r1 = Instance_Color
    //     0xbbaab0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbaab4: d0 = 0.700000
    //     0xbbaab4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbbaab8: ldr             d0, [x17, #0xf48]
    // 0xbbaabc: r0 = withOpacity()
    //     0xbbaabc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbaac0: r16 = 14.000000
    //     0xbbaac0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbaac4: ldr             x16, [x16, #0x1d8]
    // 0xbbaac8: stp             x0, x16, [SP]
    // 0xbbaacc: ldur            x1, [fp, #-0x38]
    // 0xbbaad0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbaad0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbaad4: ldr             x4, [x4, #0xaa0]
    // 0xbbaad8: r0 = copyWith()
    //     0xbbaad8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbaadc: stur            x0, [fp, #-0x38]
    // 0xbbaae0: r0 = Text()
    //     0xbbaae0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbaae4: mov             x2, x0
    // 0xbbaae8: ldur            x0, [fp, #-0x30]
    // 0xbbaaec: stur            x2, [fp, #-0x40]
    // 0xbbaaf0: StoreField: r2->field_b = r0
    //     0xbbaaf0: stur            w0, [x2, #0xb]
    // 0xbbaaf4: ldur            x0, [fp, #-0x38]
    // 0xbbaaf8: StoreField: r2->field_13 = r0
    //     0xbbaaf8: stur            w0, [x2, #0x13]
    // 0xbbaafc: ldur            x0, [fp, #-8]
    // 0xbbab00: LoadField: r1 = r0->field_13
    //     0xbbab00: ldur            w1, [x0, #0x13]
    // 0xbbab04: DecompressPointer r1
    //     0xbbab04: add             x1, x1, HEAP, lsl #32
    // 0xbbab08: r0 = of()
    //     0xbbab08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbab0c: LoadField: r1 = r0->field_5b
    //     0xbbab0c: ldur            w1, [x0, #0x5b]
    // 0xbbab10: DecompressPointer r1
    //     0xbbab10: add             x1, x1, HEAP, lsl #32
    // 0xbbab14: stur            x1, [fp, #-0x30]
    // 0xbbab18: r0 = ColorFilter()
    //     0xbbab18: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbbab1c: mov             x1, x0
    // 0xbbab20: ldur            x0, [fp, #-0x30]
    // 0xbbab24: stur            x1, [fp, #-0x38]
    // 0xbbab28: StoreField: r1->field_7 = r0
    //     0xbbab28: stur            w0, [x1, #7]
    // 0xbbab2c: r0 = Instance_BlendMode
    //     0xbbab2c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbbab30: ldr             x0, [x0, #0xb30]
    // 0xbbab34: StoreField: r1->field_b = r0
    //     0xbbab34: stur            w0, [x1, #0xb]
    // 0xbbab38: r0 = 1
    //     0xbbab38: movz            x0, #0x1
    // 0xbbab3c: StoreField: r1->field_13 = r0
    //     0xbbab3c: stur            x0, [x1, #0x13]
    // 0xbbab40: r0 = SvgPicture()
    //     0xbbab40: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbbab44: stur            x0, [fp, #-0x30]
    // 0xbbab48: ldur            x16, [fp, #-0x38]
    // 0xbbab4c: str             x16, [SP]
    // 0xbbab50: mov             x1, x0
    // 0xbbab54: r2 = "assets/images/plus_bag_item.svg"
    //     0xbbab54: add             x2, PP, #0x59, lsl #12  ; [pp+0x59470] "assets/images/plus_bag_item.svg"
    //     0xbbab58: ldr             x2, [x2, #0x470]
    // 0xbbab5c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xbbab5c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xbbab60: ldr             x4, [x4, #0xa38]
    // 0xbbab64: r0 = SvgPicture.asset()
    //     0xbbab64: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbbab68: r0 = Padding()
    //     0xbbab68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbab6c: mov             x1, x0
    // 0xbbab70: r0 = Instance_EdgeInsets
    //     0xbbab70: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b3f0] Obj!EdgeInsets@d57261
    //     0xbbab74: ldr             x0, [x0, #0x3f0]
    // 0xbbab78: stur            x1, [fp, #-0x38]
    // 0xbbab7c: StoreField: r1->field_f = r0
    //     0xbbab7c: stur            w0, [x1, #0xf]
    // 0xbbab80: ldur            x0, [fp, #-0x30]
    // 0xbbab84: StoreField: r1->field_b = r0
    //     0xbbab84: stur            w0, [x1, #0xb]
    // 0xbbab88: r0 = InkWell()
    //     0xbbab88: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbab8c: mov             x3, x0
    // 0xbbab90: ldur            x0, [fp, #-0x38]
    // 0xbbab94: stur            x3, [fp, #-0x30]
    // 0xbbab98: StoreField: r3->field_b = r0
    //     0xbbab98: stur            w0, [x3, #0xb]
    // 0xbbab9c: ldur            x2, [fp, #-8]
    // 0xbbaba0: r1 = Function '<anonymous closure>':.
    //     0xbbaba0: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3c8] AnonymousClosure: (0xa1a54c), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xbbaba4: ldr             x1, [x1, #0x3c8]
    // 0xbbaba8: r0 = AllocateClosure()
    //     0xbbaba8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbabac: mov             x1, x0
    // 0xbbabb0: ldur            x0, [fp, #-0x30]
    // 0xbbabb4: StoreField: r0->field_f = r1
    //     0xbbabb4: stur            w1, [x0, #0xf]
    // 0xbbabb8: r3 = true
    //     0xbbabb8: add             x3, NULL, #0x20  ; true
    // 0xbbabbc: StoreField: r0->field_43 = r3
    //     0xbbabbc: stur            w3, [x0, #0x43]
    // 0xbbabc0: r1 = Instance_BoxShape
    //     0xbbabc0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbabc4: ldr             x1, [x1, #0x80]
    // 0xbbabc8: StoreField: r0->field_47 = r1
    //     0xbbabc8: stur            w1, [x0, #0x47]
    // 0xbbabcc: StoreField: r0->field_6f = r3
    //     0xbbabcc: stur            w3, [x0, #0x6f]
    // 0xbbabd0: r4 = false
    //     0xbbabd0: add             x4, NULL, #0x30  ; false
    // 0xbbabd4: StoreField: r0->field_73 = r4
    //     0xbbabd4: stur            w4, [x0, #0x73]
    // 0xbbabd8: StoreField: r0->field_83 = r3
    //     0xbbabd8: stur            w3, [x0, #0x83]
    // 0xbbabdc: StoreField: r0->field_7b = r4
    //     0xbbabdc: stur            w4, [x0, #0x7b]
    // 0xbbabe0: r1 = Null
    //     0xbbabe0: mov             x1, NULL
    // 0xbbabe4: r2 = 6
    //     0xbbabe4: movz            x2, #0x6
    // 0xbbabe8: r0 = AllocateArray()
    //     0xbbabe8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbabec: mov             x2, x0
    // 0xbbabf0: ldur            x0, [fp, #-0x18]
    // 0xbbabf4: stur            x2, [fp, #-0x38]
    // 0xbbabf8: StoreField: r2->field_f = r0
    //     0xbbabf8: stur            w0, [x2, #0xf]
    // 0xbbabfc: ldur            x0, [fp, #-0x40]
    // 0xbbac00: StoreField: r2->field_13 = r0
    //     0xbbac00: stur            w0, [x2, #0x13]
    // 0xbbac04: ldur            x0, [fp, #-0x30]
    // 0xbbac08: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbac08: stur            w0, [x2, #0x17]
    // 0xbbac0c: r1 = <Widget>
    //     0xbbac0c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbac10: r0 = AllocateGrowableArray()
    //     0xbbac10: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbac14: mov             x1, x0
    // 0xbbac18: ldur            x0, [fp, #-0x38]
    // 0xbbac1c: stur            x1, [fp, #-0x18]
    // 0xbbac20: StoreField: r1->field_f = r0
    //     0xbbac20: stur            w0, [x1, #0xf]
    // 0xbbac24: r2 = 6
    //     0xbbac24: movz            x2, #0x6
    // 0xbbac28: StoreField: r1->field_b = r2
    //     0xbbac28: stur            w2, [x1, #0xb]
    // 0xbbac2c: r0 = Row()
    //     0xbbac2c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbac30: mov             x1, x0
    // 0xbbac34: r0 = Instance_Axis
    //     0xbbac34: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbac38: stur            x1, [fp, #-0x30]
    // 0xbbac3c: StoreField: r1->field_f = r0
    //     0xbbac3c: stur            w0, [x1, #0xf]
    // 0xbbac40: r2 = Instance_MainAxisAlignment
    //     0xbbac40: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbbac44: ldr             x2, [x2, #0xa8]
    // 0xbbac48: StoreField: r1->field_13 = r2
    //     0xbbac48: stur            w2, [x1, #0x13]
    // 0xbbac4c: r2 = Instance_MainAxisSize
    //     0xbbac4c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbac50: ldr             x2, [x2, #0xa10]
    // 0xbbac54: ArrayStore: r1[0] = r2  ; List_4
    //     0xbbac54: stur            w2, [x1, #0x17]
    // 0xbbac58: r3 = Instance_CrossAxisAlignment
    //     0xbbac58: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbac5c: ldr             x3, [x3, #0xa18]
    // 0xbbac60: StoreField: r1->field_1b = r3
    //     0xbbac60: stur            w3, [x1, #0x1b]
    // 0xbbac64: r4 = Instance_VerticalDirection
    //     0xbbac64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbac68: ldr             x4, [x4, #0xa20]
    // 0xbbac6c: StoreField: r1->field_23 = r4
    //     0xbbac6c: stur            w4, [x1, #0x23]
    // 0xbbac70: r5 = Instance_Clip
    //     0xbbac70: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbac74: ldr             x5, [x5, #0x38]
    // 0xbbac78: StoreField: r1->field_2b = r5
    //     0xbbac78: stur            w5, [x1, #0x2b]
    // 0xbbac7c: StoreField: r1->field_2f = rZR
    //     0xbbac7c: stur            xzr, [x1, #0x2f]
    // 0xbbac80: ldur            x6, [fp, #-0x18]
    // 0xbbac84: StoreField: r1->field_b = r6
    //     0xbbac84: stur            w6, [x1, #0xb]
    // 0xbbac88: r0 = Container()
    //     0xbbac88: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbac8c: stur            x0, [fp, #-0x18]
    // 0xbbac90: r16 = 164.000000
    //     0xbbac90: add             x16, PP, #0x54, lsl #12  ; [pp+0x54c28] 164
    //     0xbbac94: ldr             x16, [x16, #0xc28]
    // 0xbbac98: r30 = 44.000000
    //     0xbbac98: add             lr, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xbbac9c: ldr             lr, [lr, #0xad8]
    // 0xbbaca0: stp             lr, x16, [SP, #0x10]
    // 0xbbaca4: ldur            x16, [fp, #-0x28]
    // 0xbbaca8: ldur            lr, [fp, #-0x30]
    // 0xbbacac: stp             lr, x16, [SP]
    // 0xbbacb0: mov             x1, x0
    // 0xbbacb4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xbbacb4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbbacb8: ldr             x4, [x4, #0x870]
    // 0xbbacbc: r0 = Container()
    //     0xbbacbc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbacc0: r1 = Null
    //     0xbbacc0: mov             x1, NULL
    // 0xbbacc4: r2 = 6
    //     0xbbacc4: movz            x2, #0x6
    // 0xbbacc8: r0 = AllocateArray()
    //     0xbbacc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbaccc: mov             x2, x0
    // 0xbbacd0: ldur            x0, [fp, #-0x20]
    // 0xbbacd4: stur            x2, [fp, #-0x28]
    // 0xbbacd8: StoreField: r2->field_f = r0
    //     0xbbacd8: stur            w0, [x2, #0xf]
    // 0xbbacdc: r16 = Instance_Spacer
    //     0xbbacdc: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbbace0: ldr             x16, [x16, #0xf0]
    // 0xbbace4: StoreField: r2->field_13 = r16
    //     0xbbace4: stur            w16, [x2, #0x13]
    // 0xbbace8: ldur            x0, [fp, #-0x18]
    // 0xbbacec: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbacec: stur            w0, [x2, #0x17]
    // 0xbbacf0: r1 = <Widget>
    //     0xbbacf0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbacf4: r0 = AllocateGrowableArray()
    //     0xbbacf4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbacf8: mov             x1, x0
    // 0xbbacfc: ldur            x0, [fp, #-0x28]
    // 0xbbad00: stur            x1, [fp, #-0x18]
    // 0xbbad04: StoreField: r1->field_f = r0
    //     0xbbad04: stur            w0, [x1, #0xf]
    // 0xbbad08: r2 = 6
    //     0xbbad08: movz            x2, #0x6
    // 0xbbad0c: StoreField: r1->field_b = r2
    //     0xbbad0c: stur            w2, [x1, #0xb]
    // 0xbbad10: r0 = Row()
    //     0xbbad10: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbad14: mov             x1, x0
    // 0xbbad18: r0 = Instance_Axis
    //     0xbbad18: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbad1c: stur            x1, [fp, #-0x20]
    // 0xbbad20: StoreField: r1->field_f = r0
    //     0xbbad20: stur            w0, [x1, #0xf]
    // 0xbbad24: r2 = Instance_MainAxisAlignment
    //     0xbbad24: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbad28: ldr             x2, [x2, #0xa08]
    // 0xbbad2c: StoreField: r1->field_13 = r2
    //     0xbbad2c: stur            w2, [x1, #0x13]
    // 0xbbad30: r3 = Instance_MainAxisSize
    //     0xbbad30: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbad34: ldr             x3, [x3, #0xa10]
    // 0xbbad38: ArrayStore: r1[0] = r3  ; List_4
    //     0xbbad38: stur            w3, [x1, #0x17]
    // 0xbbad3c: r4 = Instance_CrossAxisAlignment
    //     0xbbad3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbad40: ldr             x4, [x4, #0xa18]
    // 0xbbad44: StoreField: r1->field_1b = r4
    //     0xbbad44: stur            w4, [x1, #0x1b]
    // 0xbbad48: r5 = Instance_VerticalDirection
    //     0xbbad48: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbad4c: ldr             x5, [x5, #0xa20]
    // 0xbbad50: StoreField: r1->field_23 = r5
    //     0xbbad50: stur            w5, [x1, #0x23]
    // 0xbbad54: r6 = Instance_Clip
    //     0xbbad54: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbad58: ldr             x6, [x6, #0x38]
    // 0xbbad5c: StoreField: r1->field_2b = r6
    //     0xbbad5c: stur            w6, [x1, #0x2b]
    // 0xbbad60: StoreField: r1->field_2f = rZR
    //     0xbbad60: stur            xzr, [x1, #0x2f]
    // 0xbbad64: ldur            x7, [fp, #-0x18]
    // 0xbbad68: StoreField: r1->field_b = r7
    //     0xbbad68: stur            w7, [x1, #0xb]
    // 0xbbad6c: r0 = Padding()
    //     0xbbad6c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbad70: mov             x1, x0
    // 0xbbad74: r0 = Instance_EdgeInsets
    //     0xbbad74: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5b3d8] Obj!EdgeInsets@d58011
    //     0xbbad78: ldr             x0, [x0, #0x3d8]
    // 0xbbad7c: StoreField: r1->field_f = r0
    //     0xbbad7c: stur            w0, [x1, #0xf]
    // 0xbbad80: ldur            x0, [fp, #-0x20]
    // 0xbbad84: StoreField: r1->field_b = r0
    //     0xbbad84: stur            w0, [x1, #0xb]
    // 0xbbad88: mov             x0, x1
    // 0xbbad8c: ldur            x1, [fp, #-0x10]
    // 0xbbad90: ArrayStore: r1[7] = r0  ; List_4
    //     0xbbad90: add             x25, x1, #0x2b
    //     0xbbad94: str             w0, [x25]
    //     0xbbad98: tbz             w0, #0, #0xbbadb4
    //     0xbbad9c: ldurb           w16, [x1, #-1]
    //     0xbbada0: ldurb           w17, [x0, #-1]
    //     0xbbada4: and             x16, x17, x16, lsr #2
    //     0xbbada8: tst             x16, HEAP, lsr #32
    //     0xbbadac: b.eq            #0xbbadb4
    //     0xbbadb0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbbadb4: r1 = Instance_Color
    //     0xbbadb4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbadb8: d0 = 0.100000
    //     0xbbadb8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbbadbc: r0 = withOpacity()
    //     0xbbadbc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbadc0: stur            x0, [fp, #-0x18]
    // 0xbbadc4: r0 = Divider()
    //     0xbbadc4: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbbadc8: mov             x1, x0
    // 0xbbadcc: ldur            x0, [fp, #-0x18]
    // 0xbbadd0: stur            x1, [fp, #-0x20]
    // 0xbbadd4: StoreField: r1->field_1f = r0
    //     0xbbadd4: stur            w0, [x1, #0x1f]
    // 0xbbadd8: r16 = <EdgeInsets>
    //     0xbbadd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbbaddc: ldr             x16, [x16, #0xda0]
    // 0xbbade0: r30 = Instance_EdgeInsets
    //     0xbbade0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbade4: ldr             lr, [lr, #0x1f0]
    // 0xbbade8: stp             lr, x16, [SP]
    // 0xbbadec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbadec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbadf0: r0 = all()
    //     0xbbadf0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbadf4: stur            x0, [fp, #-0x18]
    // 0xbbadf8: r16 = <Color?>
    //     0xbbadf8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac0] TypeArguments: <Color?>
    //     0xbbadfc: ldr             x16, [x16, #0xac0]
    // 0xbbae00: r30 = Instance_Color
    //     0xbbae00: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbbae04: stp             lr, x16, [SP]
    // 0xbbae08: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbae08: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbae0c: r0 = all()
    //     0xbbae0c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbae10: ldur            x2, [fp, #-8]
    // 0xbbae14: stur            x0, [fp, #-0x28]
    // 0xbbae18: LoadField: r1 = r2->field_13
    //     0xbbae18: ldur            w1, [x2, #0x13]
    // 0xbbae1c: DecompressPointer r1
    //     0xbbae1c: add             x1, x1, HEAP, lsl #32
    // 0xbbae20: r0 = of()
    //     0xbbae20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbae24: LoadField: r1 = r0->field_5b
    //     0xbbae24: ldur            w1, [x0, #0x5b]
    // 0xbbae28: DecompressPointer r1
    //     0xbbae28: add             x1, x1, HEAP, lsl #32
    // 0xbbae2c: stur            x1, [fp, #-0x30]
    // 0xbbae30: r0 = BorderSide()
    //     0xbbae30: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbbae34: mov             x1, x0
    // 0xbbae38: ldur            x0, [fp, #-0x30]
    // 0xbbae3c: stur            x1, [fp, #-0x38]
    // 0xbbae40: StoreField: r1->field_7 = r0
    //     0xbbae40: stur            w0, [x1, #7]
    // 0xbbae44: d0 = 1.000000
    //     0xbbae44: fmov            d0, #1.00000000
    // 0xbbae48: StoreField: r1->field_b = d0
    //     0xbbae48: stur            d0, [x1, #0xb]
    // 0xbbae4c: r0 = Instance_BorderStyle
    //     0xbbae4c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbbae50: ldr             x0, [x0, #0xf68]
    // 0xbbae54: StoreField: r1->field_13 = r0
    //     0xbbae54: stur            w0, [x1, #0x13]
    // 0xbbae58: d1 = -1.000000
    //     0xbbae58: fmov            d1, #-1.00000000
    // 0xbbae5c: ArrayStore: r1[0] = d1  ; List_8
    //     0xbbae5c: stur            d1, [x1, #0x17]
    // 0xbbae60: r0 = RoundedRectangleBorder()
    //     0xbbae60: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbbae64: mov             x1, x0
    // 0xbbae68: r0 = Instance_BorderRadius
    //     0xbbae68: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbbae6c: ldr             x0, [x0, #0xf70]
    // 0xbbae70: StoreField: r1->field_b = r0
    //     0xbbae70: stur            w0, [x1, #0xb]
    // 0xbbae74: ldur            x2, [fp, #-0x38]
    // 0xbbae78: StoreField: r1->field_7 = r2
    //     0xbbae78: stur            w2, [x1, #7]
    // 0xbbae7c: r16 = <RoundedRectangleBorder>
    //     0xbbae7c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbbae80: ldr             x16, [x16, #0xf78]
    // 0xbbae84: stp             x1, x16, [SP]
    // 0xbbae88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbae88: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbae8c: r0 = all()
    //     0xbbae8c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbae90: stur            x0, [fp, #-0x30]
    // 0xbbae94: r0 = ButtonStyle()
    //     0xbbae94: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbbae98: mov             x1, x0
    // 0xbbae9c: ldur            x0, [fp, #-0x28]
    // 0xbbaea0: stur            x1, [fp, #-0x38]
    // 0xbbaea4: StoreField: r1->field_b = r0
    //     0xbbaea4: stur            w0, [x1, #0xb]
    // 0xbbaea8: ldur            x0, [fp, #-0x18]
    // 0xbbaeac: StoreField: r1->field_23 = r0
    //     0xbbaeac: stur            w0, [x1, #0x23]
    // 0xbbaeb0: ldur            x0, [fp, #-0x30]
    // 0xbbaeb4: StoreField: r1->field_43 = r0
    //     0xbbaeb4: stur            w0, [x1, #0x43]
    // 0xbbaeb8: r0 = TextButtonThemeData()
    //     0xbbaeb8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbbaebc: mov             x2, x0
    // 0xbbaec0: ldur            x0, [fp, #-0x38]
    // 0xbbaec4: stur            x2, [fp, #-0x18]
    // 0xbbaec8: StoreField: r2->field_7 = r0
    //     0xbbaec8: stur            w0, [x2, #7]
    // 0xbbaecc: ldur            x0, [fp, #-8]
    // 0xbbaed0: LoadField: r1 = r0->field_13
    //     0xbbaed0: ldur            w1, [x0, #0x13]
    // 0xbbaed4: DecompressPointer r1
    //     0xbbaed4: add             x1, x1, HEAP, lsl #32
    // 0xbbaed8: r0 = of()
    //     0xbbaed8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbaedc: LoadField: r1 = r0->field_87
    //     0xbbaedc: ldur            w1, [x0, #0x87]
    // 0xbbaee0: DecompressPointer r1
    //     0xbbaee0: add             x1, x1, HEAP, lsl #32
    // 0xbbaee4: LoadField: r0 = r1->field_7
    //     0xbbaee4: ldur            w0, [x1, #7]
    // 0xbbaee8: DecompressPointer r0
    //     0xbbaee8: add             x0, x0, HEAP, lsl #32
    // 0xbbaeec: r16 = 14.000000
    //     0xbbaeec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbaef0: ldr             x16, [x16, #0x1d8]
    // 0xbbaef4: r30 = Instance_Color
    //     0xbbaef4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbaef8: stp             lr, x16, [SP]
    // 0xbbaefc: mov             x1, x0
    // 0xbbaf00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbaf00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbaf04: ldr             x4, [x4, #0xaa0]
    // 0xbbaf08: r0 = copyWith()
    //     0xbbaf08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbaf0c: stur            x0, [fp, #-0x28]
    // 0xbbaf10: r0 = Text()
    //     0xbbaf10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbaf14: mov             x3, x0
    // 0xbbaf18: r0 = "CANCEL"
    //     0xbbaf18: add             x0, PP, #0x5c, lsl #12  ; [pp+0x5c3d0] "CANCEL"
    //     0xbbaf1c: ldr             x0, [x0, #0x3d0]
    // 0xbbaf20: stur            x3, [fp, #-0x30]
    // 0xbbaf24: StoreField: r3->field_b = r0
    //     0xbbaf24: stur            w0, [x3, #0xb]
    // 0xbbaf28: ldur            x0, [fp, #-0x28]
    // 0xbbaf2c: StoreField: r3->field_13 = r0
    //     0xbbaf2c: stur            w0, [x3, #0x13]
    // 0xbbaf30: ldur            x2, [fp, #-8]
    // 0xbbaf34: r1 = Function '<anonymous closure>':.
    //     0xbbaf34: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3d8] AnonymousClosure: (0xbbb5c4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbbaf38: ldr             x1, [x1, #0x3d8]
    // 0xbbaf3c: r0 = AllocateClosure()
    //     0xbbaf3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbaf40: stur            x0, [fp, #-0x28]
    // 0xbbaf44: r0 = TextButton()
    //     0xbbaf44: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbbaf48: mov             x1, x0
    // 0xbbaf4c: ldur            x0, [fp, #-0x28]
    // 0xbbaf50: stur            x1, [fp, #-0x38]
    // 0xbbaf54: StoreField: r1->field_b = r0
    //     0xbbaf54: stur            w0, [x1, #0xb]
    // 0xbbaf58: r0 = false
    //     0xbbaf58: add             x0, NULL, #0x30  ; false
    // 0xbbaf5c: StoreField: r1->field_27 = r0
    //     0xbbaf5c: stur            w0, [x1, #0x27]
    // 0xbbaf60: r2 = true
    //     0xbbaf60: add             x2, NULL, #0x20  ; true
    // 0xbbaf64: StoreField: r1->field_2f = r2
    //     0xbbaf64: stur            w2, [x1, #0x2f]
    // 0xbbaf68: ldur            x3, [fp, #-0x30]
    // 0xbbaf6c: StoreField: r1->field_37 = r3
    //     0xbbaf6c: stur            w3, [x1, #0x37]
    // 0xbbaf70: r0 = TextButtonTheme()
    //     0xbbaf70: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbbaf74: mov             x1, x0
    // 0xbbaf78: ldur            x0, [fp, #-0x18]
    // 0xbbaf7c: stur            x1, [fp, #-0x28]
    // 0xbbaf80: StoreField: r1->field_f = r0
    //     0xbbaf80: stur            w0, [x1, #0xf]
    // 0xbbaf84: ldur            x0, [fp, #-0x38]
    // 0xbbaf88: StoreField: r1->field_b = r0
    //     0xbbaf88: stur            w0, [x1, #0xb]
    // 0xbbaf8c: r0 = SizedBox()
    //     0xbbaf8c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbbaf90: mov             x1, x0
    // 0xbbaf94: r0 = 140.000000
    //     0xbbaf94: add             x0, PP, #0x52, lsl #12  ; [pp+0x523c8] 140
    //     0xbbaf98: ldr             x0, [x0, #0x3c8]
    // 0xbbaf9c: stur            x1, [fp, #-0x18]
    // 0xbbafa0: StoreField: r1->field_f = r0
    //     0xbbafa0: stur            w0, [x1, #0xf]
    // 0xbbafa4: r2 = 44.000000
    //     0xbbafa4: add             x2, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xbbafa8: ldr             x2, [x2, #0xad8]
    // 0xbbafac: StoreField: r1->field_13 = r2
    //     0xbbafac: stur            w2, [x1, #0x13]
    // 0xbbafb0: ldur            x3, [fp, #-0x28]
    // 0xbbafb4: StoreField: r1->field_b = r3
    //     0xbbafb4: stur            w3, [x1, #0xb]
    // 0xbbafb8: r0 = Padding()
    //     0xbbafb8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbafbc: mov             x1, x0
    // 0xbbafc0: r0 = Instance_EdgeInsets
    //     0xbbafc0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbbafc4: ldr             x0, [x0, #0x668]
    // 0xbbafc8: stur            x1, [fp, #-0x28]
    // 0xbbafcc: StoreField: r1->field_f = r0
    //     0xbbafcc: stur            w0, [x1, #0xf]
    // 0xbbafd0: ldur            x2, [fp, #-0x18]
    // 0xbbafd4: StoreField: r1->field_b = r2
    //     0xbbafd4: stur            w2, [x1, #0xb]
    // 0xbbafd8: r16 = <EdgeInsets>
    //     0xbbafd8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbbafdc: ldr             x16, [x16, #0xda0]
    // 0xbbafe0: r30 = Instance_EdgeInsets
    //     0xbbafe0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbafe4: ldr             lr, [lr, #0x1f0]
    // 0xbbafe8: stp             lr, x16, [SP]
    // 0xbbafec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbafec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbaff0: r0 = all()
    //     0xbbaff0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbaff4: ldur            x2, [fp, #-8]
    // 0xbbaff8: stur            x0, [fp, #-0x18]
    // 0xbbaffc: LoadField: r1 = r2->field_13
    //     0xbbaffc: ldur            w1, [x2, #0x13]
    // 0xbbb000: DecompressPointer r1
    //     0xbbb000: add             x1, x1, HEAP, lsl #32
    // 0xbbb004: r0 = of()
    //     0xbbb004: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbb008: LoadField: r1 = r0->field_5b
    //     0xbbb008: ldur            w1, [x0, #0x5b]
    // 0xbbb00c: DecompressPointer r1
    //     0xbbb00c: add             x1, x1, HEAP, lsl #32
    // 0xbbb010: r16 = <Color?>
    //     0xbbb010: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac0] TypeArguments: <Color?>
    //     0xbbb014: ldr             x16, [x16, #0xac0]
    // 0xbbb018: stp             x1, x16, [SP]
    // 0xbbb01c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbb01c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbb020: r0 = all()
    //     0xbbb020: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbb024: ldur            x2, [fp, #-8]
    // 0xbbb028: stur            x0, [fp, #-0x30]
    // 0xbbb02c: LoadField: r1 = r2->field_13
    //     0xbbb02c: ldur            w1, [x2, #0x13]
    // 0xbbb030: DecompressPointer r1
    //     0xbbb030: add             x1, x1, HEAP, lsl #32
    // 0xbbb034: r0 = of()
    //     0xbbb034: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbb038: LoadField: r1 = r0->field_5b
    //     0xbbb038: ldur            w1, [x0, #0x5b]
    // 0xbbb03c: DecompressPointer r1
    //     0xbbb03c: add             x1, x1, HEAP, lsl #32
    // 0xbbb040: stur            x1, [fp, #-0x38]
    // 0xbbb044: r0 = BorderSide()
    //     0xbbb044: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbbb048: mov             x1, x0
    // 0xbbb04c: ldur            x0, [fp, #-0x38]
    // 0xbbb050: stur            x1, [fp, #-0x40]
    // 0xbbb054: StoreField: r1->field_7 = r0
    //     0xbbb054: stur            w0, [x1, #7]
    // 0xbbb058: d0 = 1.000000
    //     0xbbb058: fmov            d0, #1.00000000
    // 0xbbb05c: StoreField: r1->field_b = d0
    //     0xbbb05c: stur            d0, [x1, #0xb]
    // 0xbbb060: r0 = Instance_BorderStyle
    //     0xbbb060: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbbb064: ldr             x0, [x0, #0xf68]
    // 0xbbb068: StoreField: r1->field_13 = r0
    //     0xbbb068: stur            w0, [x1, #0x13]
    // 0xbbb06c: d0 = -1.000000
    //     0xbbb06c: fmov            d0, #-1.00000000
    // 0xbbb070: ArrayStore: r1[0] = d0  ; List_8
    //     0xbbb070: stur            d0, [x1, #0x17]
    // 0xbbb074: r0 = RoundedRectangleBorder()
    //     0xbbb074: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbbb078: mov             x1, x0
    // 0xbbb07c: r0 = Instance_BorderRadius
    //     0xbbb07c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbbb080: ldr             x0, [x0, #0xf70]
    // 0xbbb084: StoreField: r1->field_b = r0
    //     0xbbb084: stur            w0, [x1, #0xb]
    // 0xbbb088: ldur            x0, [fp, #-0x40]
    // 0xbbb08c: StoreField: r1->field_7 = r0
    //     0xbbb08c: stur            w0, [x1, #7]
    // 0xbbb090: r16 = <RoundedRectangleBorder>
    //     0xbbb090: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbbb094: ldr             x16, [x16, #0xf78]
    // 0xbbb098: stp             x1, x16, [SP]
    // 0xbbb09c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbb09c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbb0a0: r0 = all()
    //     0xbbb0a0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbb0a4: stur            x0, [fp, #-0x38]
    // 0xbbb0a8: r0 = ButtonStyle()
    //     0xbbb0a8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbbb0ac: mov             x1, x0
    // 0xbbb0b0: ldur            x0, [fp, #-0x30]
    // 0xbbb0b4: stur            x1, [fp, #-0x40]
    // 0xbbb0b8: StoreField: r1->field_b = r0
    //     0xbbb0b8: stur            w0, [x1, #0xb]
    // 0xbbb0bc: ldur            x0, [fp, #-0x18]
    // 0xbbb0c0: StoreField: r1->field_23 = r0
    //     0xbbb0c0: stur            w0, [x1, #0x23]
    // 0xbbb0c4: ldur            x0, [fp, #-0x38]
    // 0xbbb0c8: StoreField: r1->field_43 = r0
    //     0xbbb0c8: stur            w0, [x1, #0x43]
    // 0xbbb0cc: r0 = TextButtonThemeData()
    //     0xbbb0cc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbbb0d0: mov             x2, x0
    // 0xbbb0d4: ldur            x0, [fp, #-0x40]
    // 0xbbb0d8: stur            x2, [fp, #-0x18]
    // 0xbbb0dc: StoreField: r2->field_7 = r0
    //     0xbbb0dc: stur            w0, [x2, #7]
    // 0xbbb0e0: ldur            x0, [fp, #-8]
    // 0xbbb0e4: LoadField: r1 = r0->field_13
    //     0xbbb0e4: ldur            w1, [x0, #0x13]
    // 0xbbb0e8: DecompressPointer r1
    //     0xbbb0e8: add             x1, x1, HEAP, lsl #32
    // 0xbbb0ec: r0 = of()
    //     0xbbb0ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbb0f0: LoadField: r1 = r0->field_87
    //     0xbbb0f0: ldur            w1, [x0, #0x87]
    // 0xbbb0f4: DecompressPointer r1
    //     0xbbb0f4: add             x1, x1, HEAP, lsl #32
    // 0xbbb0f8: LoadField: r0 = r1->field_7
    //     0xbbb0f8: ldur            w0, [x1, #7]
    // 0xbbb0fc: DecompressPointer r0
    //     0xbbb0fc: add             x0, x0, HEAP, lsl #32
    // 0xbbb100: r16 = 14.000000
    //     0xbbb100: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbb104: ldr             x16, [x16, #0x1d8]
    // 0xbbb108: r30 = Instance_Color
    //     0xbbb108: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbbb10c: stp             lr, x16, [SP]
    // 0xbbb110: mov             x1, x0
    // 0xbbb114: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbb114: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbb118: ldr             x4, [x4, #0xaa0]
    // 0xbbb11c: r0 = copyWith()
    //     0xbbb11c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbb120: stur            x0, [fp, #-0x30]
    // 0xbbb124: r0 = Text()
    //     0xbbb124: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbb128: mov             x3, x0
    // 0xbbb12c: r0 = "ADD TO BAG"
    //     0xbbb12c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd8] "ADD TO BAG"
    //     0xbbb130: ldr             x0, [x0, #0xdd8]
    // 0xbbb134: stur            x3, [fp, #-0x38]
    // 0xbbb138: StoreField: r3->field_b = r0
    //     0xbbb138: stur            w0, [x3, #0xb]
    // 0xbbb13c: ldur            x0, [fp, #-0x30]
    // 0xbbb140: StoreField: r3->field_13 = r0
    //     0xbbb140: stur            w0, [x3, #0x13]
    // 0xbbb144: ldur            x2, [fp, #-8]
    // 0xbbb148: r1 = Function '<anonymous closure>':.
    //     0xbbb148: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c3e0] AnonymousClosure: (0xbbb44c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbbb14c: ldr             x1, [x1, #0x3e0]
    // 0xbbb150: r0 = AllocateClosure()
    //     0xbbb150: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbb154: stur            x0, [fp, #-8]
    // 0xbbb158: r0 = TextButton()
    //     0xbbb158: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbbb15c: mov             x1, x0
    // 0xbbb160: ldur            x0, [fp, #-8]
    // 0xbbb164: stur            x1, [fp, #-0x30]
    // 0xbbb168: StoreField: r1->field_b = r0
    //     0xbbb168: stur            w0, [x1, #0xb]
    // 0xbbb16c: r0 = false
    //     0xbbb16c: add             x0, NULL, #0x30  ; false
    // 0xbbb170: StoreField: r1->field_27 = r0
    //     0xbbb170: stur            w0, [x1, #0x27]
    // 0xbbb174: r0 = true
    //     0xbbb174: add             x0, NULL, #0x20  ; true
    // 0xbbb178: StoreField: r1->field_2f = r0
    //     0xbbb178: stur            w0, [x1, #0x2f]
    // 0xbbb17c: ldur            x0, [fp, #-0x38]
    // 0xbbb180: StoreField: r1->field_37 = r0
    //     0xbbb180: stur            w0, [x1, #0x37]
    // 0xbbb184: r0 = TextButtonTheme()
    //     0xbbb184: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbbb188: mov             x1, x0
    // 0xbbb18c: ldur            x0, [fp, #-0x18]
    // 0xbbb190: stur            x1, [fp, #-8]
    // 0xbbb194: StoreField: r1->field_f = r0
    //     0xbbb194: stur            w0, [x1, #0xf]
    // 0xbbb198: ldur            x0, [fp, #-0x30]
    // 0xbbb19c: StoreField: r1->field_b = r0
    //     0xbbb19c: stur            w0, [x1, #0xb]
    // 0xbbb1a0: r0 = SizedBox()
    //     0xbbb1a0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbbb1a4: mov             x1, x0
    // 0xbbb1a8: r0 = 140.000000
    //     0xbbb1a8: add             x0, PP, #0x52, lsl #12  ; [pp+0x523c8] 140
    //     0xbbb1ac: ldr             x0, [x0, #0x3c8]
    // 0xbbb1b0: stur            x1, [fp, #-0x18]
    // 0xbbb1b4: StoreField: r1->field_f = r0
    //     0xbbb1b4: stur            w0, [x1, #0xf]
    // 0xbbb1b8: r0 = 44.000000
    //     0xbbb1b8: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xbbb1bc: ldr             x0, [x0, #0xad8]
    // 0xbbb1c0: StoreField: r1->field_13 = r0
    //     0xbbb1c0: stur            w0, [x1, #0x13]
    // 0xbbb1c4: ldur            x0, [fp, #-8]
    // 0xbbb1c8: StoreField: r1->field_b = r0
    //     0xbbb1c8: stur            w0, [x1, #0xb]
    // 0xbbb1cc: r0 = Padding()
    //     0xbbb1cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbb1d0: mov             x3, x0
    // 0xbbb1d4: r0 = Instance_EdgeInsets
    //     0xbbb1d4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbbb1d8: ldr             x0, [x0, #0x668]
    // 0xbbb1dc: stur            x3, [fp, #-8]
    // 0xbbb1e0: StoreField: r3->field_f = r0
    //     0xbbb1e0: stur            w0, [x3, #0xf]
    // 0xbbb1e4: ldur            x0, [fp, #-0x18]
    // 0xbbb1e8: StoreField: r3->field_b = r0
    //     0xbbb1e8: stur            w0, [x3, #0xb]
    // 0xbbb1ec: r1 = Null
    //     0xbbb1ec: mov             x1, NULL
    // 0xbbb1f0: r2 = 6
    //     0xbbb1f0: movz            x2, #0x6
    // 0xbbb1f4: r0 = AllocateArray()
    //     0xbbb1f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbb1f8: mov             x2, x0
    // 0xbbb1fc: ldur            x0, [fp, #-0x28]
    // 0xbbb200: stur            x2, [fp, #-0x18]
    // 0xbbb204: StoreField: r2->field_f = r0
    //     0xbbb204: stur            w0, [x2, #0xf]
    // 0xbbb208: r16 = Instance_Spacer
    //     0xbbb208: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbbb20c: ldr             x16, [x16, #0xf0]
    // 0xbbb210: StoreField: r2->field_13 = r16
    //     0xbbb210: stur            w16, [x2, #0x13]
    // 0xbbb214: ldur            x0, [fp, #-8]
    // 0xbbb218: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbb218: stur            w0, [x2, #0x17]
    // 0xbbb21c: r1 = <Widget>
    //     0xbbb21c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbb220: r0 = AllocateGrowableArray()
    //     0xbbb220: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbb224: mov             x1, x0
    // 0xbbb228: ldur            x0, [fp, #-0x18]
    // 0xbbb22c: stur            x1, [fp, #-8]
    // 0xbbb230: StoreField: r1->field_f = r0
    //     0xbbb230: stur            w0, [x1, #0xf]
    // 0xbbb234: r0 = 6
    //     0xbbb234: movz            x0, #0x6
    // 0xbbb238: StoreField: r1->field_b = r0
    //     0xbbb238: stur            w0, [x1, #0xb]
    // 0xbbb23c: r0 = Row()
    //     0xbbb23c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbb240: mov             x3, x0
    // 0xbbb244: r0 = Instance_Axis
    //     0xbbb244: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbb248: stur            x3, [fp, #-0x18]
    // 0xbbb24c: StoreField: r3->field_f = r0
    //     0xbbb24c: stur            w0, [x3, #0xf]
    // 0xbbb250: r4 = Instance_MainAxisAlignment
    //     0xbbb250: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbb254: ldr             x4, [x4, #0xa08]
    // 0xbbb258: StoreField: r3->field_13 = r4
    //     0xbbb258: stur            w4, [x3, #0x13]
    // 0xbbb25c: r5 = Instance_MainAxisSize
    //     0xbbb25c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbb260: ldr             x5, [x5, #0xa10]
    // 0xbbb264: ArrayStore: r3[0] = r5  ; List_4
    //     0xbbb264: stur            w5, [x3, #0x17]
    // 0xbbb268: r1 = Instance_CrossAxisAlignment
    //     0xbbb268: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbb26c: ldr             x1, [x1, #0xa18]
    // 0xbbb270: StoreField: r3->field_1b = r1
    //     0xbbb270: stur            w1, [x3, #0x1b]
    // 0xbbb274: r6 = Instance_VerticalDirection
    //     0xbbb274: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbb278: ldr             x6, [x6, #0xa20]
    // 0xbbb27c: StoreField: r3->field_23 = r6
    //     0xbbb27c: stur            w6, [x3, #0x23]
    // 0xbbb280: r7 = Instance_Clip
    //     0xbbb280: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbb284: ldr             x7, [x7, #0x38]
    // 0xbbb288: StoreField: r3->field_2b = r7
    //     0xbbb288: stur            w7, [x3, #0x2b]
    // 0xbbb28c: StoreField: r3->field_2f = rZR
    //     0xbbb28c: stur            xzr, [x3, #0x2f]
    // 0xbbb290: ldur            x1, [fp, #-8]
    // 0xbbb294: StoreField: r3->field_b = r1
    //     0xbbb294: stur            w1, [x3, #0xb]
    // 0xbbb298: r1 = Null
    //     0xbbb298: mov             x1, NULL
    // 0xbbb29c: r2 = 4
    //     0xbbb29c: movz            x2, #0x4
    // 0xbbb2a0: r0 = AllocateArray()
    //     0xbbb2a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbb2a4: mov             x2, x0
    // 0xbbb2a8: ldur            x0, [fp, #-0x20]
    // 0xbbb2ac: stur            x2, [fp, #-8]
    // 0xbbb2b0: StoreField: r2->field_f = r0
    //     0xbbb2b0: stur            w0, [x2, #0xf]
    // 0xbbb2b4: ldur            x0, [fp, #-0x18]
    // 0xbbb2b8: StoreField: r2->field_13 = r0
    //     0xbbb2b8: stur            w0, [x2, #0x13]
    // 0xbbb2bc: r1 = <Widget>
    //     0xbbb2bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbb2c0: r0 = AllocateGrowableArray()
    //     0xbbb2c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbb2c4: mov             x1, x0
    // 0xbbb2c8: ldur            x0, [fp, #-8]
    // 0xbbb2cc: stur            x1, [fp, #-0x18]
    // 0xbbb2d0: StoreField: r1->field_f = r0
    //     0xbbb2d0: stur            w0, [x1, #0xf]
    // 0xbbb2d4: r0 = 4
    //     0xbbb2d4: movz            x0, #0x4
    // 0xbbb2d8: StoreField: r1->field_b = r0
    //     0xbbb2d8: stur            w0, [x1, #0xb]
    // 0xbbb2dc: r0 = Column()
    //     0xbbb2dc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbb2e0: mov             x1, x0
    // 0xbbb2e4: r0 = Instance_Axis
    //     0xbbb2e4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbb2e8: stur            x1, [fp, #-8]
    // 0xbbb2ec: StoreField: r1->field_f = r0
    //     0xbbb2ec: stur            w0, [x1, #0xf]
    // 0xbbb2f0: r0 = Instance_MainAxisAlignment
    //     0xbbb2f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbb2f4: ldr             x0, [x0, #0xa08]
    // 0xbbb2f8: StoreField: r1->field_13 = r0
    //     0xbbb2f8: stur            w0, [x1, #0x13]
    // 0xbbb2fc: r0 = Instance_MainAxisSize
    //     0xbbb2fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbb300: ldr             x0, [x0, #0xa10]
    // 0xbbb304: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbb304: stur            w0, [x1, #0x17]
    // 0xbbb308: r0 = Instance_CrossAxisAlignment
    //     0xbbb308: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbbb30c: ldr             x0, [x0, #0x890]
    // 0xbbb310: StoreField: r1->field_1b = r0
    //     0xbbb310: stur            w0, [x1, #0x1b]
    // 0xbbb314: r0 = Instance_VerticalDirection
    //     0xbbb314: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbb318: ldr             x0, [x0, #0xa20]
    // 0xbbb31c: StoreField: r1->field_23 = r0
    //     0xbbb31c: stur            w0, [x1, #0x23]
    // 0xbbb320: r2 = Instance_Clip
    //     0xbbb320: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbb324: ldr             x2, [x2, #0x38]
    // 0xbbb328: StoreField: r1->field_2b = r2
    //     0xbbb328: stur            w2, [x1, #0x2b]
    // 0xbbb32c: StoreField: r1->field_2f = rZR
    //     0xbbb32c: stur            xzr, [x1, #0x2f]
    // 0xbbb330: ldur            x3, [fp, #-0x18]
    // 0xbbb334: StoreField: r1->field_b = r3
    //     0xbbb334: stur            w3, [x1, #0xb]
    // 0xbbb338: r0 = Padding()
    //     0xbbb338: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbb33c: mov             x1, x0
    // 0xbbb340: r0 = Instance_EdgeInsets
    //     0xbbb340: add             x0, PP, #0x33, lsl #12  ; [pp+0x33848] Obj!EdgeInsets@d58071
    //     0xbbb344: ldr             x0, [x0, #0x848]
    // 0xbbb348: StoreField: r1->field_f = r0
    //     0xbbb348: stur            w0, [x1, #0xf]
    // 0xbbb34c: ldur            x0, [fp, #-8]
    // 0xbbb350: StoreField: r1->field_b = r0
    //     0xbbb350: stur            w0, [x1, #0xb]
    // 0xbbb354: mov             x0, x1
    // 0xbbb358: ldur            x1, [fp, #-0x10]
    // 0xbbb35c: ArrayStore: r1[8] = r0  ; List_4
    //     0xbbb35c: add             x25, x1, #0x2f
    //     0xbbb360: str             w0, [x25]
    //     0xbbb364: tbz             w0, #0, #0xbbb380
    //     0xbbb368: ldurb           w16, [x1, #-1]
    //     0xbbb36c: ldurb           w17, [x0, #-1]
    //     0xbbb370: and             x16, x17, x16, lsr #2
    //     0xbbb374: tst             x16, HEAP, lsr #32
    //     0xbbb378: b.eq            #0xbbb380
    //     0xbbb37c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbbb380: r1 = <Widget>
    //     0xbbb380: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbb384: r0 = AllocateGrowableArray()
    //     0xbbb384: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbb388: mov             x1, x0
    // 0xbbb38c: ldur            x0, [fp, #-0x10]
    // 0xbbb390: stur            x1, [fp, #-8]
    // 0xbbb394: StoreField: r1->field_f = r0
    //     0xbbb394: stur            w0, [x1, #0xf]
    // 0xbbb398: r0 = 18
    //     0xbbb398: movz            x0, #0x12
    // 0xbbb39c: StoreField: r1->field_b = r0
    //     0xbbb39c: stur            w0, [x1, #0xb]
    // 0xbbb3a0: r0 = Wrap()
    //     0xbbb3a0: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0xbbb3a4: mov             x1, x0
    // 0xbbb3a8: r0 = Instance_Axis
    //     0xbbb3a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbb3ac: stur            x1, [fp, #-0x10]
    // 0xbbb3b0: StoreField: r1->field_f = r0
    //     0xbbb3b0: stur            w0, [x1, #0xf]
    // 0xbbb3b4: r0 = Instance_WrapAlignment
    //     0xbbb3b4: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0xbbb3b8: ldr             x0, [x0, #0x6e8]
    // 0xbbb3bc: StoreField: r1->field_13 = r0
    //     0xbbb3bc: stur            w0, [x1, #0x13]
    // 0xbbb3c0: ArrayStore: r1[0] = rZR  ; List_8
    //     0xbbb3c0: stur            xzr, [x1, #0x17]
    // 0xbbb3c4: StoreField: r1->field_1f = r0
    //     0xbbb3c4: stur            w0, [x1, #0x1f]
    // 0xbbb3c8: StoreField: r1->field_23 = rZR
    //     0xbbb3c8: stur            xzr, [x1, #0x23]
    // 0xbbb3cc: r0 = Instance_WrapCrossAlignment
    //     0xbbb3cc: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0xbbb3d0: ldr             x0, [x0, #0x6f0]
    // 0xbbb3d4: StoreField: r1->field_2b = r0
    //     0xbbb3d4: stur            w0, [x1, #0x2b]
    // 0xbbb3d8: r0 = Instance_VerticalDirection
    //     0xbbb3d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbb3dc: ldr             x0, [x0, #0xa20]
    // 0xbbb3e0: StoreField: r1->field_33 = r0
    //     0xbbb3e0: stur            w0, [x1, #0x33]
    // 0xbbb3e4: r0 = Instance_Clip
    //     0xbbb3e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbb3e8: ldr             x0, [x0, #0x38]
    // 0xbbb3ec: StoreField: r1->field_37 = r0
    //     0xbbb3ec: stur            w0, [x1, #0x37]
    // 0xbbb3f0: ldur            x0, [fp, #-8]
    // 0xbbb3f4: StoreField: r1->field_b = r0
    //     0xbbb3f4: stur            w0, [x1, #0xb]
    // 0xbbb3f8: r0 = Padding()
    //     0xbbb3f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbb3fc: r1 = Instance_EdgeInsets
    //     0xbbb3fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbb400: ldr             x1, [x1, #0x1f0]
    // 0xbbb404: StoreField: r0->field_f = r1
    //     0xbbb404: stur            w1, [x0, #0xf]
    // 0xbbb408: ldur            x1, [fp, #-0x10]
    // 0xbbb40c: StoreField: r0->field_b = r1
    //     0xbbb40c: stur            w1, [x0, #0xb]
    // 0xbbb410: LeaveFrame
    //     0xbbb410: mov             SP, fp
    //     0xbbb414: ldp             fp, lr, [SP], #0x10
    // 0xbbb418: ret
    //     0xbbb418: ret             
    // 0xbbb41c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb41c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb420: b               #0xbb95cc
    // 0xbbb424: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb424: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb428: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbb428: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbb42c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbb42c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbb430: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbb430: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbb434: SaveReg d0
    //     0xbbb434: str             q0, [SP, #-0x10]!
    // 0xbbb438: SaveReg r1
    //     0xbbb438: str             x1, [SP, #-8]!
    // 0xbbb43c: r0 = AllocateDouble()
    //     0xbbb43c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbbb440: RestoreReg r1
    //     0xbbb440: ldr             x1, [SP], #8
    // 0xbbb444: RestoreReg d0
    //     0xbbb444: ldr             q0, [SP], #0x10
    // 0xbbb448: b               #0xbba7ec
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbb44c, size: 0x178
    // 0xbbb44c: EnterFrame
    //     0xbbb44c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb450: mov             fp, SP
    // 0xbbb454: AllocStack(0x38)
    //     0xbbb454: sub             SP, SP, #0x38
    // 0xbbb458: SetupParameters()
    //     0xbbb458: ldr             x0, [fp, #0x10]
    //     0xbbb45c: ldur            w1, [x0, #0x17]
    //     0xbbb460: add             x1, x1, HEAP, lsl #32
    //     0xbbb464: stur            x1, [fp, #-8]
    // 0xbbb468: CheckStackOverflow
    //     0xbbb468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb46c: cmp             SP, x16
    //     0xbbb470: b.ls            #0xbbb5b8
    // 0xbbb474: LoadField: r0 = r1->field_f
    //     0xbbb474: ldur            w0, [x1, #0xf]
    // 0xbbb478: DecompressPointer r0
    //     0xbbb478: add             x0, x0, HEAP, lsl #32
    // 0xbbb47c: LoadField: r2 = r0->field_b
    //     0xbbb47c: ldur            w2, [x0, #0xb]
    // 0xbbb480: DecompressPointer r2
    //     0xbbb480: add             x2, x2, HEAP, lsl #32
    // 0xbbb484: cmp             w2, NULL
    // 0xbbb488: b.eq            #0xbbb5c0
    // 0xbbb48c: LoadField: r0 = r2->field_13
    //     0xbbb48c: ldur            w0, [x2, #0x13]
    // 0xbbb490: DecompressPointer r0
    //     0xbbb490: add             x0, x0, HEAP, lsl #32
    // 0xbbb494: str             x0, [SP]
    // 0xbbb498: r4 = 0
    //     0xbbb498: movz            x4, #0
    // 0xbbb49c: ldr             x0, [SP]
    // 0xbbb4a0: r16 = UnlinkedCall_0x613b5c
    //     0xbbb4a0: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c3e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbb4a4: add             x16, x16, #0x3e8
    // 0xbbb4a8: ldp             x5, lr, [x16]
    // 0xbbb4ac: blr             lr
    // 0xbbb4b0: ldur            x0, [fp, #-8]
    // 0xbbb4b4: LoadField: r1 = r0->field_13
    //     0xbbb4b4: ldur            w1, [x0, #0x13]
    // 0xbbb4b8: DecompressPointer r1
    //     0xbbb4b8: add             x1, x1, HEAP, lsl #32
    // 0xbbb4bc: r16 = <Object?>
    //     0xbbb4bc: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xbbb4c0: stp             x1, x16, [SP]
    // 0xbbb4c4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbb4c4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbb4c8: r0 = pop()
    //     0xbbb4c8: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xbbb4cc: ldur            x0, [fp, #-8]
    // 0xbbb4d0: LoadField: r1 = r0->field_f
    //     0xbbb4d0: ldur            w1, [x0, #0xf]
    // 0xbbb4d4: DecompressPointer r1
    //     0xbbb4d4: add             x1, x1, HEAP, lsl #32
    // 0xbbb4d8: LoadField: r2 = r1->field_1f
    //     0xbbb4d8: ldur            w2, [x1, #0x1f]
    // 0xbbb4dc: DecompressPointer r2
    //     0xbbb4dc: add             x2, x2, HEAP, lsl #32
    // 0xbbb4e0: LoadField: r1 = r2->field_8f
    //     0xbbb4e0: ldur            w1, [x2, #0x8f]
    // 0xbbb4e4: DecompressPointer r1
    //     0xbbb4e4: add             x1, x1, HEAP, lsl #32
    // 0xbbb4e8: r0 = value()
    //     0xbbb4e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbbb4ec: LoadField: r2 = r0->field_7
    //     0xbbb4ec: ldur            w2, [x0, #7]
    // 0xbbb4f0: DecompressPointer r2
    //     0xbbb4f0: add             x2, x2, HEAP, lsl #32
    // 0xbbb4f4: ldur            x0, [fp, #-8]
    // 0xbbb4f8: stur            x2, [fp, #-0x18]
    // 0xbbb4fc: LoadField: r1 = r0->field_f
    //     0xbbb4fc: ldur            w1, [x0, #0xf]
    // 0xbbb500: DecompressPointer r1
    //     0xbbb500: add             x1, x1, HEAP, lsl #32
    // 0xbbb504: LoadField: r3 = r1->field_1f
    //     0xbbb504: ldur            w3, [x1, #0x1f]
    // 0xbbb508: DecompressPointer r3
    //     0xbbb508: add             x3, x3, HEAP, lsl #32
    // 0xbbb50c: r17 = 259
    //     0xbbb50c: movz            x17, #0x103
    // 0xbbb510: ldr             w4, [x3, x17]
    // 0xbbb514: DecompressPointer r4
    //     0xbbb514: add             x4, x4, HEAP, lsl #32
    // 0xbbb518: stur            x4, [fp, #-0x10]
    // 0xbbb51c: r17 = 287
    //     0xbbb51c: movz            x17, #0x11f
    // 0xbbb520: ldr             w1, [x3, x17]
    // 0xbbb524: DecompressPointer r1
    //     0xbbb524: add             x1, x1, HEAP, lsl #32
    // 0xbbb528: r0 = value()
    //     0xbbb528: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbbb52c: mov             x2, x0
    // 0xbbb530: ldur            x0, [fp, #-8]
    // 0xbbb534: stur            x2, [fp, #-0x20]
    // 0xbbb538: LoadField: r1 = r0->field_f
    //     0xbbb538: ldur            w1, [x0, #0xf]
    // 0xbbb53c: DecompressPointer r1
    //     0xbbb53c: add             x1, x1, HEAP, lsl #32
    // 0xbbb540: LoadField: r3 = r1->field_1f
    //     0xbbb540: ldur            w3, [x1, #0x1f]
    // 0xbbb544: DecompressPointer r3
    //     0xbbb544: add             x3, x3, HEAP, lsl #32
    // 0xbbb548: LoadField: r1 = r3->field_8f
    //     0xbbb548: ldur            w1, [x3, #0x8f]
    // 0xbbb54c: DecompressPointer r1
    //     0xbbb54c: add             x1, x1, HEAP, lsl #32
    // 0xbbb550: r0 = value()
    //     0xbbb550: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbbb554: LoadField: r1 = r0->field_4b
    //     0xbbb554: ldur            w1, [x0, #0x4b]
    // 0xbbb558: DecompressPointer r1
    //     0xbbb558: add             x1, x1, HEAP, lsl #32
    // 0xbbb55c: stur            x1, [fp, #-0x28]
    // 0xbbb560: r0 = ReplaceBagRequest()
    //     0xbbb560: bl              #0xa1a540  ; AllocateReplaceBagRequestStub -> ReplaceBagRequest (size=0x18)
    // 0xbbb564: mov             x1, x0
    // 0xbbb568: ldur            x0, [fp, #-0x18]
    // 0xbbb56c: StoreField: r1->field_7 = r0
    //     0xbbb56c: stur            w0, [x1, #7]
    // 0xbbb570: ldur            x0, [fp, #-0x10]
    // 0xbbb574: StoreField: r1->field_b = r0
    //     0xbbb574: stur            w0, [x1, #0xb]
    // 0xbbb578: ldur            x0, [fp, #-0x20]
    // 0xbbb57c: StoreField: r1->field_f = r0
    //     0xbbb57c: stur            w0, [x1, #0xf]
    // 0xbbb580: ldur            x0, [fp, #-0x28]
    // 0xbbb584: StoreField: r1->field_13 = r0
    //     0xbbb584: stur            w0, [x1, #0x13]
    // 0xbbb588: ldur            x0, [fp, #-8]
    // 0xbbb58c: LoadField: r2 = r0->field_f
    //     0xbbb58c: ldur            w2, [x0, #0xf]
    // 0xbbb590: DecompressPointer r2
    //     0xbbb590: add             x2, x2, HEAP, lsl #32
    // 0xbbb594: LoadField: r0 = r2->field_1f
    //     0xbbb594: ldur            w0, [x2, #0x1f]
    // 0xbbb598: DecompressPointer r0
    //     0xbbb598: add             x0, x0, HEAP, lsl #32
    // 0xbbb59c: mov             x2, x1
    // 0xbbb5a0: mov             x1, x0
    // 0xbbb5a4: r0 = continueButton()
    //     0xbbb5a4: bl              #0xa0f5b4  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_order_summary_controller.dart] CheckoutOrderSummaryController::continueButton
    // 0xbbb5a8: r0 = Null
    //     0xbbb5a8: mov             x0, NULL
    // 0xbbb5ac: LeaveFrame
    //     0xbbb5ac: mov             SP, fp
    //     0xbbb5b0: ldp             fp, lr, [SP], #0x10
    // 0xbbb5b4: ret
    //     0xbbb5b4: ret             
    // 0xbbb5b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb5b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb5bc: b               #0xbbb474
    // 0xbbb5c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbb5c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbb5c4, size: 0x9c
    // 0xbbb5c4: EnterFrame
    //     0xbbb5c4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb5c8: mov             fp, SP
    // 0xbbb5cc: AllocStack(0x18)
    //     0xbbb5cc: sub             SP, SP, #0x18
    // 0xbbb5d0: SetupParameters()
    //     0xbbb5d0: ldr             x0, [fp, #0x10]
    //     0xbbb5d4: ldur            w1, [x0, #0x17]
    //     0xbbb5d8: add             x1, x1, HEAP, lsl #32
    //     0xbbb5dc: stur            x1, [fp, #-8]
    // 0xbbb5e0: CheckStackOverflow
    //     0xbbb5e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb5e4: cmp             SP, x16
    //     0xbbb5e8: b.ls            #0xbbb654
    // 0xbbb5ec: LoadField: r0 = r1->field_f
    //     0xbbb5ec: ldur            w0, [x1, #0xf]
    // 0xbbb5f0: DecompressPointer r0
    //     0xbbb5f0: add             x0, x0, HEAP, lsl #32
    // 0xbbb5f4: LoadField: r2 = r0->field_b
    //     0xbbb5f4: ldur            w2, [x0, #0xb]
    // 0xbbb5f8: DecompressPointer r2
    //     0xbbb5f8: add             x2, x2, HEAP, lsl #32
    // 0xbbb5fc: cmp             w2, NULL
    // 0xbbb600: b.eq            #0xbbb65c
    // 0xbbb604: LoadField: r0 = r2->field_13
    //     0xbbb604: ldur            w0, [x2, #0x13]
    // 0xbbb608: DecompressPointer r0
    //     0xbbb608: add             x0, x0, HEAP, lsl #32
    // 0xbbb60c: str             x0, [SP]
    // 0xbbb610: r4 = 0
    //     0xbbb610: movz            x4, #0
    // 0xbbb614: ldr             x0, [SP]
    // 0xbbb618: r16 = UnlinkedCall_0x613b5c
    //     0xbbb618: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c3f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbb61c: add             x16, x16, #0x3f8
    // 0xbbb620: ldp             x5, lr, [x16]
    // 0xbbb624: blr             lr
    // 0xbbb628: ldur            x0, [fp, #-8]
    // 0xbbb62c: LoadField: r1 = r0->field_13
    //     0xbbb62c: ldur            w1, [x0, #0x13]
    // 0xbbb630: DecompressPointer r1
    //     0xbbb630: add             x1, x1, HEAP, lsl #32
    // 0xbbb634: r16 = <Object?>
    //     0xbbb634: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xbbb638: stp             x1, x16, [SP]
    // 0xbbb63c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbb63c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbb640: r0 = pop()
    //     0xbbb640: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xbbb644: r0 = Null
    //     0xbbb644: mov             x0, NULL
    // 0xbbb648: LeaveFrame
    //     0xbbb648: mov             SP, fp
    //     0xbbb64c: ldp             fp, lr, [SP], #0x10
    // 0xbbb650: ret
    //     0xbbb650: ret             
    // 0xbbb654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb654: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb658: b               #0xbbb5ec
    // 0xbbb65c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbb65c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbb660, size: 0x78
    // 0xbbb660: EnterFrame
    //     0xbbb660: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb664: mov             fp, SP
    // 0xbbb668: AllocStack(0x18)
    //     0xbbb668: sub             SP, SP, #0x18
    // 0xbbb66c: SetupParameters()
    //     0xbbb66c: ldr             x0, [fp, #0x10]
    //     0xbbb670: ldur            w2, [x0, #0x17]
    //     0xbbb674: add             x2, x2, HEAP, lsl #32
    //     0xbbb678: stur            x2, [fp, #-8]
    // 0xbbb67c: CheckStackOverflow
    //     0xbbb67c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb680: cmp             SP, x16
    //     0xbbb684: b.ls            #0xbbb6d0
    // 0xbbb688: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbbb688: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbb68c: ldr             x0, [x0, #0x1c80]
    //     0xbbb690: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbb694: cmp             w0, w16
    //     0xbbb698: b.ne            #0xbbb6a4
    //     0xbbb69c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbbb6a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbbb6a4: ldur            x2, [fp, #-8]
    // 0xbbb6a8: r1 = Function '<anonymous closure>':.
    //     0xbbb6a8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c408] AnonymousClosure: (0xbbb6d8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbbb6ac: ldr             x1, [x1, #0x408]
    // 0xbbb6b0: r0 = AllocateClosure()
    //     0xbbb6b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbb6b4: stp             x0, NULL, [SP]
    // 0xbbb6b8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbb6b8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbb6bc: r0 = GetNavigation.to()
    //     0xbbb6bc: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xbbb6c0: r0 = Null
    //     0xbbb6c0: mov             x0, NULL
    // 0xbbb6c4: LeaveFrame
    //     0xbbb6c4: mov             SP, fp
    //     0xbbb6c8: ldp             fp, lr, [SP], #0x10
    // 0xbbb6cc: ret
    //     0xbbb6cc: ret             
    // 0xbbb6d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb6d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb6d4: b               #0xbbb688
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0xbbb6d8, size: 0x70
    // 0xbbb6d8: EnterFrame
    //     0xbbb6d8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb6dc: mov             fp, SP
    // 0xbbb6e0: AllocStack(0x8)
    //     0xbbb6e0: sub             SP, SP, #8
    // 0xbbb6e4: SetupParameters()
    //     0xbbb6e4: ldr             x0, [fp, #0x10]
    //     0xbbb6e8: ldur            w1, [x0, #0x17]
    //     0xbbb6ec: add             x1, x1, HEAP, lsl #32
    // 0xbbb6f0: CheckStackOverflow
    //     0xbbb6f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb6f4: cmp             SP, x16
    //     0xbbb6f8: b.ls            #0xbbb740
    // 0xbbb6fc: LoadField: r0 = r1->field_f
    //     0xbbb6fc: ldur            w0, [x1, #0xf]
    // 0xbbb700: DecompressPointer r0
    //     0xbbb700: add             x0, x0, HEAP, lsl #32
    // 0xbbb704: LoadField: r1 = r0->field_1f
    //     0xbbb704: ldur            w1, [x0, #0x1f]
    // 0xbbb708: DecompressPointer r1
    //     0xbbb708: add             x1, x1, HEAP, lsl #32
    // 0xbbb70c: LoadField: r0 = r1->field_8f
    //     0xbbb70c: ldur            w0, [x1, #0x8f]
    // 0xbbb710: DecompressPointer r0
    //     0xbbb710: add             x0, x0, HEAP, lsl #32
    // 0xbbb714: mov             x1, x0
    // 0xbbb718: r0 = value()
    //     0xbbb718: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbbb71c: LoadField: r1 = r0->field_4f
    //     0xbbb71c: ldur            w1, [x0, #0x4f]
    // 0xbbb720: DecompressPointer r1
    //     0xbbb720: add             x1, x1, HEAP, lsl #32
    // 0xbbb724: stur            x1, [fp, #-8]
    // 0xbbb728: r0 = ViewSizeChart()
    //     0xbbb728: bl              #0xbbb748  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0xbbb72c: ldur            x1, [fp, #-8]
    // 0xbbb730: StoreField: r0->field_b = r1
    //     0xbbb730: stur            w1, [x0, #0xb]
    // 0xbbb734: LeaveFrame
    //     0xbbb734: mov             SP, fp
    //     0xbbb738: ldp             fp, lr, [SP], #0x10
    // 0xbbb73c: ret
    //     0xbbb73c: ret             
    // 0xbbb740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbb740: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbb744: b               #0xbbb6fc
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbbb774, size: 0x314
    // 0xbbb774: EnterFrame
    //     0xbbb774: stp             fp, lr, [SP, #-0x10]!
    //     0xbbb778: mov             fp, SP
    // 0xbbb77c: AllocStack(0x38)
    //     0xbbb77c: sub             SP, SP, #0x38
    // 0xbbb780: SetupParameters()
    //     0xbbb780: ldr             x0, [fp, #0x20]
    //     0xbbb784: ldur            w1, [x0, #0x17]
    //     0xbbb788: add             x1, x1, HEAP, lsl #32
    //     0xbbb78c: stur            x1, [fp, #-8]
    // 0xbbb790: CheckStackOverflow
    //     0xbbb790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbb794: cmp             SP, x16
    //     0xbbb798: b.ls            #0xbbba78
    // 0xbbb79c: r1 = 1
    //     0xbbb79c: movz            x1, #0x1
    // 0xbbb7a0: r0 = AllocateContext()
    //     0xbbb7a0: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbb7a4: mov             x2, x0
    // 0xbbb7a8: ldur            x0, [fp, #-8]
    // 0xbbb7ac: stur            x2, [fp, #-0x18]
    // 0xbbb7b0: StoreField: r2->field_b = r0
    //     0xbbb7b0: stur            w0, [x2, #0xb]
    // 0xbbb7b4: ldr             x3, [fp, #0x10]
    // 0xbbb7b8: StoreField: r2->field_f = r3
    //     0xbbb7b8: stur            w3, [x2, #0xf]
    // 0xbbb7bc: LoadField: r1 = r0->field_f
    //     0xbbb7bc: ldur            w1, [x0, #0xf]
    // 0xbbb7c0: DecompressPointer r1
    //     0xbbb7c0: add             x1, x1, HEAP, lsl #32
    // 0xbbb7c4: LoadField: r4 = r1->field_13
    //     0xbbb7c4: ldur            w4, [x1, #0x13]
    // 0xbbb7c8: DecompressPointer r4
    //     0xbbb7c8: add             x4, x4, HEAP, lsl #32
    // 0xbbb7cc: stur            x4, [fp, #-0x10]
    // 0xbbb7d0: LoadField: r5 = r1->field_1f
    //     0xbbb7d0: ldur            w5, [x1, #0x1f]
    // 0xbbb7d4: DecompressPointer r5
    //     0xbbb7d4: add             x5, x5, HEAP, lsl #32
    // 0xbbb7d8: LoadField: r1 = r5->field_8f
    //     0xbbb7d8: ldur            w1, [x5, #0x8f]
    // 0xbbb7dc: DecompressPointer r1
    //     0xbbb7dc: add             x1, x1, HEAP, lsl #32
    // 0xbbb7e0: r0 = value()
    //     0xbbb7e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbbb7e4: LoadField: r2 = r0->field_53
    //     0xbbb7e4: ldur            w2, [x0, #0x53]
    // 0xbbb7e8: DecompressPointer r2
    //     0xbbb7e8: add             x2, x2, HEAP, lsl #32
    // 0xbbb7ec: cmp             w2, NULL
    // 0xbbb7f0: b.ne            #0xbbb800
    // 0xbbb7f4: ldr             x3, [fp, #0x10]
    // 0xbbb7f8: r0 = Null
    //     0xbbb7f8: mov             x0, NULL
    // 0xbbb7fc: b               #0xbbb840
    // 0xbbb800: ldr             x3, [fp, #0x10]
    // 0xbbb804: LoadField: r0 = r2->field_b
    //     0xbbb804: ldur            w0, [x2, #0xb]
    // 0xbbb808: r4 = LoadInt32Instr(r3)
    //     0xbbb808: sbfx            x4, x3, #1, #0x1f
    //     0xbbb80c: tbz             w3, #0, #0xbbb814
    //     0xbbb810: ldur            x4, [x3, #7]
    // 0xbbb814: r1 = LoadInt32Instr(r0)
    //     0xbbb814: sbfx            x1, x0, #1, #0x1f
    // 0xbbb818: mov             x0, x1
    // 0xbbb81c: mov             x1, x4
    // 0xbbb820: cmp             x1, x0
    // 0xbbb824: b.hs            #0xbbba80
    // 0xbbb828: LoadField: r0 = r2->field_f
    //     0xbbb828: ldur            w0, [x2, #0xf]
    // 0xbbb82c: DecompressPointer r0
    //     0xbbb82c: add             x0, x0, HEAP, lsl #32
    // 0xbbb830: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbbb830: add             x16, x0, x4, lsl #2
    //     0xbbb834: ldur            w1, [x16, #0xf]
    // 0xbbb838: DecompressPointer r1
    //     0xbbb838: add             x1, x1, HEAP, lsl #32
    // 0xbbb83c: mov             x0, x1
    // 0xbbb840: cmp             w0, NULL
    // 0xbbb844: b.ne            #0xbbb854
    // 0xbbb848: r0 = SkuDetails()
    //     0xbbb848: bl              #0x906334  ; AllocateSkuDetailsStub -> SkuDetails (size=0x14)
    // 0xbbb84c: mov             x2, x0
    // 0xbbb850: b               #0xbbb858
    // 0xbbb854: mov             x2, x0
    // 0xbbb858: ldur            x1, [fp, #-0x10]
    // 0xbbb85c: r0 = contains()
    //     0xbbb85c: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xbbb860: tbnz            w0, #4, #0xbbb8a8
    // 0xbbb864: ldr             x1, [fp, #0x18]
    // 0xbbb868: r0 = of()
    //     0xbbb868: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbb86c: LoadField: r2 = r0->field_5b
    //     0xbbb86c: ldur            w2, [x0, #0x5b]
    // 0xbbb870: DecompressPointer r2
    //     0xbbb870: add             x2, x2, HEAP, lsl #32
    // 0xbbb874: r1 = Null
    //     0xbbb874: mov             x1, NULL
    // 0xbbb878: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbb878: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbb87c: r0 = Border.all()
    //     0xbbb87c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbbb880: stur            x0, [fp, #-0x10]
    // 0xbbb884: r0 = BoxDecoration()
    //     0xbbb884: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbb888: mov             x1, x0
    // 0xbbb88c: ldur            x0, [fp, #-0x10]
    // 0xbbb890: StoreField: r1->field_f = r0
    //     0xbbb890: stur            w0, [x1, #0xf]
    // 0xbbb894: r0 = Instance_BoxShape
    //     0xbbb894: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbb898: ldr             x0, [x0, #0x80]
    // 0xbbb89c: StoreField: r1->field_23 = r0
    //     0xbbb89c: stur            w0, [x1, #0x23]
    // 0xbbb8a0: mov             x2, x1
    // 0xbbb8a4: b               #0xbbb8e8
    // 0xbbb8a8: r0 = Instance_BoxShape
    //     0xbbb8a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbb8ac: ldr             x0, [x0, #0x80]
    // 0xbbb8b0: r1 = Null
    //     0xbbb8b0: mov             x1, NULL
    // 0xbbb8b4: r2 = Instance_Color
    //     0xbbb8b4: add             x2, PP, #0x33, lsl #12  ; [pp+0x337b8] Obj!Color@d6ad11
    //     0xbbb8b8: ldr             x2, [x2, #0x7b8]
    // 0xbbb8bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbb8bc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbb8c0: r0 = Border.all()
    //     0xbbb8c0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbbb8c4: stur            x0, [fp, #-0x10]
    // 0xbbb8c8: r0 = BoxDecoration()
    //     0xbbb8c8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbb8cc: mov             x1, x0
    // 0xbbb8d0: ldur            x0, [fp, #-0x10]
    // 0xbbb8d4: StoreField: r1->field_f = r0
    //     0xbbb8d4: stur            w0, [x1, #0xf]
    // 0xbbb8d8: r0 = Instance_BoxShape
    //     0xbbb8d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbb8dc: ldr             x0, [x0, #0x80]
    // 0xbbb8e0: StoreField: r1->field_23 = r0
    //     0xbbb8e0: stur            w0, [x1, #0x23]
    // 0xbbb8e4: mov             x2, x1
    // 0xbbb8e8: ldur            x1, [fp, #-8]
    // 0xbbb8ec: stur            x2, [fp, #-0x10]
    // 0xbbb8f0: LoadField: r3 = r1->field_f
    //     0xbbb8f0: ldur            w3, [x1, #0xf]
    // 0xbbb8f4: DecompressPointer r3
    //     0xbbb8f4: add             x3, x3, HEAP, lsl #32
    // 0xbbb8f8: LoadField: r1 = r3->field_1f
    //     0xbbb8f8: ldur            w1, [x3, #0x1f]
    // 0xbbb8fc: DecompressPointer r1
    //     0xbbb8fc: add             x1, x1, HEAP, lsl #32
    // 0xbbb900: LoadField: r3 = r1->field_8f
    //     0xbbb900: ldur            w3, [x1, #0x8f]
    // 0xbbb904: DecompressPointer r3
    //     0xbbb904: add             x3, x3, HEAP, lsl #32
    // 0xbbb908: mov             x1, x3
    // 0xbbb90c: r0 = value()
    //     0xbbb90c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbbb910: LoadField: r2 = r0->field_53
    //     0xbbb910: ldur            w2, [x0, #0x53]
    // 0xbbb914: DecompressPointer r2
    //     0xbbb914: add             x2, x2, HEAP, lsl #32
    // 0xbbb918: cmp             w2, NULL
    // 0xbbb91c: b.ne            #0xbbb928
    // 0xbbb920: r0 = Null
    //     0xbbb920: mov             x0, NULL
    // 0xbbb924: b               #0xbbb968
    // 0xbbb928: ldr             x0, [fp, #0x10]
    // 0xbbb92c: LoadField: r1 = r2->field_b
    //     0xbbb92c: ldur            w1, [x2, #0xb]
    // 0xbbb930: r3 = LoadInt32Instr(r0)
    //     0xbbb930: sbfx            x3, x0, #1, #0x1f
    //     0xbbb934: tbz             w0, #0, #0xbbb93c
    //     0xbbb938: ldur            x3, [x0, #7]
    // 0xbbb93c: r0 = LoadInt32Instr(r1)
    //     0xbbb93c: sbfx            x0, x1, #1, #0x1f
    // 0xbbb940: mov             x1, x3
    // 0xbbb944: cmp             x1, x0
    // 0xbbb948: b.hs            #0xbbba84
    // 0xbbb94c: LoadField: r0 = r2->field_f
    //     0xbbb94c: ldur            w0, [x2, #0xf]
    // 0xbbb950: DecompressPointer r0
    //     0xbbb950: add             x0, x0, HEAP, lsl #32
    // 0xbbb954: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xbbb954: add             x16, x0, x3, lsl #2
    //     0xbbb958: ldur            w1, [x16, #0xf]
    // 0xbbb95c: DecompressPointer r1
    //     0xbbb95c: add             x1, x1, HEAP, lsl #32
    // 0xbbb960: LoadField: r0 = r1->field_f
    //     0xbbb960: ldur            w0, [x1, #0xf]
    // 0xbbb964: DecompressPointer r0
    //     0xbbb964: add             x0, x0, HEAP, lsl #32
    // 0xbbb968: cmp             w0, NULL
    // 0xbbb96c: b.ne            #0xbbb974
    // 0xbbb970: r0 = ""
    //     0xbbb970: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbb974: ldr             x1, [fp, #0x18]
    // 0xbbb978: stur            x0, [fp, #-8]
    // 0xbbb97c: r0 = of()
    //     0xbbb97c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbb980: LoadField: r1 = r0->field_87
    //     0xbbb980: ldur            w1, [x0, #0x87]
    // 0xbbb984: DecompressPointer r1
    //     0xbbb984: add             x1, x1, HEAP, lsl #32
    // 0xbbb988: LoadField: r0 = r1->field_27
    //     0xbbb988: ldur            w0, [x1, #0x27]
    // 0xbbb98c: DecompressPointer r0
    //     0xbbb98c: add             x0, x0, HEAP, lsl #32
    // 0xbbb990: stur            x0, [fp, #-0x20]
    // 0xbbb994: r0 = Text()
    //     0xbbb994: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbb998: mov             x1, x0
    // 0xbbb99c: ldur            x0, [fp, #-8]
    // 0xbbb9a0: stur            x1, [fp, #-0x28]
    // 0xbbb9a4: StoreField: r1->field_b = r0
    //     0xbbb9a4: stur            w0, [x1, #0xb]
    // 0xbbb9a8: ldur            x0, [fp, #-0x20]
    // 0xbbb9ac: StoreField: r1->field_13 = r0
    //     0xbbb9ac: stur            w0, [x1, #0x13]
    // 0xbbb9b0: r0 = Center()
    //     0xbbb9b0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbbb9b4: mov             x1, x0
    // 0xbbb9b8: r0 = Instance_Alignment
    //     0xbbb9b8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbbb9bc: ldr             x0, [x0, #0xb10]
    // 0xbbb9c0: stur            x1, [fp, #-8]
    // 0xbbb9c4: StoreField: r1->field_f = r0
    //     0xbbb9c4: stur            w0, [x1, #0xf]
    // 0xbbb9c8: ldur            x0, [fp, #-0x28]
    // 0xbbb9cc: StoreField: r1->field_b = r0
    //     0xbbb9cc: stur            w0, [x1, #0xb]
    // 0xbbb9d0: r0 = Padding()
    //     0xbbb9d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbb9d4: mov             x1, x0
    // 0xbbb9d8: r0 = Instance_EdgeInsets
    //     0xbbb9d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbb9dc: ldr             x0, [x0, #0x1f0]
    // 0xbbb9e0: stur            x1, [fp, #-0x20]
    // 0xbbb9e4: StoreField: r1->field_f = r0
    //     0xbbb9e4: stur            w0, [x1, #0xf]
    // 0xbbb9e8: ldur            x0, [fp, #-8]
    // 0xbbb9ec: StoreField: r1->field_b = r0
    //     0xbbb9ec: stur            w0, [x1, #0xb]
    // 0xbbb9f0: r0 = Container()
    //     0xbbb9f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbb9f4: stur            x0, [fp, #-8]
    // 0xbbb9f8: ldur            x16, [fp, #-0x10]
    // 0xbbb9fc: ldur            lr, [fp, #-0x20]
    // 0xbbba00: stp             lr, x16, [SP]
    // 0xbbba04: mov             x1, x0
    // 0xbbba08: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbbba08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbbba0c: ldr             x4, [x4, #0x88]
    // 0xbbba10: r0 = Container()
    //     0xbbba10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbba14: r0 = InkWell()
    //     0xbbba14: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbba18: mov             x3, x0
    // 0xbbba1c: ldur            x0, [fp, #-8]
    // 0xbbba20: stur            x3, [fp, #-0x10]
    // 0xbbba24: StoreField: r3->field_b = r0
    //     0xbbba24: stur            w0, [x3, #0xb]
    // 0xbbba28: ldur            x2, [fp, #-0x18]
    // 0xbbba2c: r1 = Function '<anonymous closure>':.
    //     0xbbba2c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c410] AnonymousClosure: (0xbbba88), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xbb9540)
    //     0xbbba30: ldr             x1, [x1, #0x410]
    // 0xbbba34: r0 = AllocateClosure()
    //     0xbbba34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbba38: mov             x1, x0
    // 0xbbba3c: ldur            x0, [fp, #-0x10]
    // 0xbbba40: StoreField: r0->field_f = r1
    //     0xbbba40: stur            w1, [x0, #0xf]
    // 0xbbba44: r1 = true
    //     0xbbba44: add             x1, NULL, #0x20  ; true
    // 0xbbba48: StoreField: r0->field_43 = r1
    //     0xbbba48: stur            w1, [x0, #0x43]
    // 0xbbba4c: r2 = Instance_BoxShape
    //     0xbbba4c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbba50: ldr             x2, [x2, #0x80]
    // 0xbbba54: StoreField: r0->field_47 = r2
    //     0xbbba54: stur            w2, [x0, #0x47]
    // 0xbbba58: StoreField: r0->field_6f = r1
    //     0xbbba58: stur            w1, [x0, #0x6f]
    // 0xbbba5c: r2 = false
    //     0xbbba5c: add             x2, NULL, #0x30  ; false
    // 0xbbba60: StoreField: r0->field_73 = r2
    //     0xbbba60: stur            w2, [x0, #0x73]
    // 0xbbba64: StoreField: r0->field_83 = r1
    //     0xbbba64: stur            w1, [x0, #0x83]
    // 0xbbba68: StoreField: r0->field_7b = r2
    //     0xbbba68: stur            w2, [x0, #0x7b]
    // 0xbbba6c: LeaveFrame
    //     0xbbba6c: mov             SP, fp
    //     0xbbba70: ldp             fp, lr, [SP], #0x10
    // 0xbbba74: ret
    //     0xbbba74: ret             
    // 0xbbba78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbba78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbba7c: b               #0xbbb79c
    // 0xbbba80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbba80: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbbba84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbba84: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbba88, size: 0x68
    // 0xbbba88: EnterFrame
    //     0xbbba88: stp             fp, lr, [SP, #-0x10]!
    //     0xbbba8c: mov             fp, SP
    // 0xbbba90: AllocStack(0x8)
    //     0xbbba90: sub             SP, SP, #8
    // 0xbbba94: SetupParameters()
    //     0xbbba94: ldr             x0, [fp, #0x10]
    //     0xbbba98: ldur            w2, [x0, #0x17]
    //     0xbbba9c: add             x2, x2, HEAP, lsl #32
    // 0xbbbaa0: CheckStackOverflow
    //     0xbbbaa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbbaa4: cmp             SP, x16
    //     0xbbbaa8: b.ls            #0xbbbae8
    // 0xbbbaac: LoadField: r0 = r2->field_b
    //     0xbbbaac: ldur            w0, [x2, #0xb]
    // 0xbbbab0: DecompressPointer r0
    //     0xbbbab0: add             x0, x0, HEAP, lsl #32
    // 0xbbbab4: LoadField: r3 = r0->field_f
    //     0xbbbab4: ldur            w3, [x0, #0xf]
    // 0xbbbab8: DecompressPointer r3
    //     0xbbbab8: add             x3, x3, HEAP, lsl #32
    // 0xbbbabc: stur            x3, [fp, #-8]
    // 0xbbbac0: r1 = Function '<anonymous closure>':.
    //     0xbbbac0: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c418] AnonymousClosure: (0xa1d8a4), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::build (0xb49980)
    //     0xbbbac4: ldr             x1, [x1, #0x418]
    // 0xbbbac8: r0 = AllocateClosure()
    //     0xbbbac8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbbacc: ldur            x1, [fp, #-8]
    // 0xbbbad0: mov             x2, x0
    // 0xbbbad4: r0 = setState()
    //     0xbbbad4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbbbad8: r0 = Null
    //     0xbbbad8: mov             x0, NULL
    // 0xbbbadc: LeaveFrame
    //     0xbbbadc: mov             SP, fp
    //     0xbbbae0: ldp             fp, lr, [SP], #0x10
    // 0xbbbae4: ret
    //     0xbbbae4: ret             
    // 0xbbbae8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbbae8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbbaec: b               #0xbbbaac
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbbaf0, size: 0x9c
    // 0xbbbaf0: EnterFrame
    //     0xbbbaf0: stp             fp, lr, [SP, #-0x10]!
    //     0xbbbaf4: mov             fp, SP
    // 0xbbbaf8: AllocStack(0x18)
    //     0xbbbaf8: sub             SP, SP, #0x18
    // 0xbbbafc: SetupParameters()
    //     0xbbbafc: ldr             x0, [fp, #0x10]
    //     0xbbbb00: ldur            w1, [x0, #0x17]
    //     0xbbbb04: add             x1, x1, HEAP, lsl #32
    //     0xbbbb08: stur            x1, [fp, #-8]
    // 0xbbbb0c: CheckStackOverflow
    //     0xbbbb0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbbb10: cmp             SP, x16
    //     0xbbbb14: b.ls            #0xbbbb80
    // 0xbbbb18: LoadField: r0 = r1->field_13
    //     0xbbbb18: ldur            w0, [x1, #0x13]
    // 0xbbbb1c: DecompressPointer r0
    //     0xbbbb1c: add             x0, x0, HEAP, lsl #32
    // 0xbbbb20: r16 = <Object?>
    //     0xbbbb20: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0xbbbb24: stp             x0, x16, [SP]
    // 0xbbbb28: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbbb28: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbbb2c: r0 = pop()
    //     0xbbbb2c: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xbbbb30: ldur            x0, [fp, #-8]
    // 0xbbbb34: LoadField: r1 = r0->field_f
    //     0xbbbb34: ldur            w1, [x0, #0xf]
    // 0xbbbb38: DecompressPointer r1
    //     0xbbbb38: add             x1, x1, HEAP, lsl #32
    // 0xbbbb3c: LoadField: r0 = r1->field_b
    //     0xbbbb3c: ldur            w0, [x1, #0xb]
    // 0xbbbb40: DecompressPointer r0
    //     0xbbbb40: add             x0, x0, HEAP, lsl #32
    // 0xbbbb44: cmp             w0, NULL
    // 0xbbbb48: b.eq            #0xbbbb88
    // 0xbbbb4c: LoadField: r1 = r0->field_13
    //     0xbbbb4c: ldur            w1, [x0, #0x13]
    // 0xbbbb50: DecompressPointer r1
    //     0xbbbb50: add             x1, x1, HEAP, lsl #32
    // 0xbbbb54: str             x1, [SP]
    // 0xbbbb58: r4 = 0
    //     0xbbbb58: movz            x4, #0
    // 0xbbbb5c: ldr             x0, [SP]
    // 0xbbbb60: r16 = UnlinkedCall_0x613b5c
    //     0xbbbb60: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c420] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbbb64: add             x16, x16, #0x420
    // 0xbbbb68: ldp             x5, lr, [x16]
    // 0xbbbb6c: blr             lr
    // 0xbbbb70: r0 = Null
    //     0xbbbb70: mov             x0, NULL
    // 0xbbbb74: LeaveFrame
    //     0xbbbb74: mov             SP, fp
    //     0xbbbb78: ldp             fp, lr, [SP], #0x10
    // 0xbbbb7c: ret
    //     0xbbbb7c: ret             
    // 0xbbbb80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbbb80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbbb84: b               #0xbbbb18
    // 0xbbbb88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbbb88: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _EditBagBottomSheetState(/* No info */) {
    // ** addr: 0xc7af20, size: 0xe8
    // 0xc7af20: EnterFrame
    //     0xc7af20: stp             fp, lr, [SP, #-0x10]!
    //     0xc7af24: mov             fp, SP
    // 0xc7af28: AllocStack(0x18)
    //     0xc7af28: sub             SP, SP, #0x18
    // 0xc7af2c: SetupParameters(_EditBagBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0xc7af2c: stur            x1, [fp, #-8]
    // 0xc7af30: CheckStackOverflow
    //     0xc7af30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7af34: cmp             SP, x16
    //     0xc7af38: b.ls            #0xc7b000
    // 0xc7af3c: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7af3c: stur            xzr, [x1, #0x17]
    // 0xc7af40: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xc7af40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7af44: ldr             x0, [x0]
    //     0xc7af48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7af4c: cmp             w0, w16
    //     0xc7af50: b.ne            #0xc7af5c
    //     0xc7af54: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xc7af58: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7af5c: r1 = <SkuDetails>
    //     0xc7af5c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23440] TypeArguments: <SkuDetails>
    //     0xc7af60: ldr             x1, [x1, #0x440]
    // 0xc7af64: stur            x0, [fp, #-0x10]
    // 0xc7af68: r0 = AllocateGrowableArray()
    //     0xc7af68: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc7af6c: mov             x1, x0
    // 0xc7af70: ldur            x0, [fp, #-0x10]
    // 0xc7af74: StoreField: r1->field_f = r0
    //     0xc7af74: stur            w0, [x1, #0xf]
    // 0xc7af78: StoreField: r1->field_b = rZR
    //     0xc7af78: stur            wzr, [x1, #0xb]
    // 0xc7af7c: mov             x0, x1
    // 0xc7af80: ldur            x1, [fp, #-8]
    // 0xc7af84: StoreField: r1->field_13 = r0
    //     0xc7af84: stur            w0, [x1, #0x13]
    //     0xc7af88: ldurb           w16, [x1, #-1]
    //     0xc7af8c: ldurb           w17, [x0, #-1]
    //     0xc7af90: and             x16, x17, x16, lsr #2
    //     0xc7af94: tst             x16, HEAP, lsr #32
    //     0xc7af98: b.eq            #0xc7afa0
    //     0xc7af9c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7afa0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc7afa0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc7afa4: ldr             x0, [x0, #0x1c80]
    //     0xc7afa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc7afac: cmp             w0, w16
    //     0xc7afb0: b.ne            #0xc7afbc
    //     0xc7afb4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc7afb8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc7afbc: r16 = <CheckoutOrderSummaryController>
    //     0xc7afbc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d7b8] TypeArguments: <CheckoutOrderSummaryController>
    //     0xc7afc0: ldr             x16, [x16, #0x7b8]
    // 0xc7afc4: str             x16, [SP]
    // 0xc7afc8: r4 = const [0x1, 0, 0, 0, null]
    //     0xc7afc8: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xc7afcc: r0 = Inst.find()
    //     0xc7afcc: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xc7afd0: ldur            x1, [fp, #-8]
    // 0xc7afd4: StoreField: r1->field_1f = r0
    //     0xc7afd4: stur            w0, [x1, #0x1f]
    //     0xc7afd8: ldurb           w16, [x1, #-1]
    //     0xc7afdc: ldurb           w17, [x0, #-1]
    //     0xc7afe0: and             x16, x17, x16, lsr #2
    //     0xc7afe4: tst             x16, HEAP, lsr #32
    //     0xc7afe8: b.eq            #0xc7aff0
    //     0xc7afec: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7aff0: r0 = Null
    //     0xc7aff0: mov             x0, NULL
    // 0xc7aff4: LeaveFrame
    //     0xc7aff4: mov             SP, fp
    //     0xc7aff8: ldp             fp, lr, [SP], #0x10
    // 0xc7affc: ret
    //     0xc7affc: ret             
    // 0xc7b000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7b000: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7b004: b               #0xc7af3c
  }
}

// class id: 4015, size: 0x18, field offset: 0xc
//   const constructor, 
class EditBagBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc803bc, size: 0x48
    // 0xc803bc: EnterFrame
    //     0xc803bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc803c0: mov             fp, SP
    // 0xc803c4: AllocStack(0x8)
    //     0xc803c4: sub             SP, SP, #8
    // 0xc803c8: CheckStackOverflow
    //     0xc803c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc803cc: cmp             SP, x16
    //     0xc803d0: b.ls            #0xc803fc
    // 0xc803d4: r1 = <EditBagBottomSheet>
    //     0xc803d4: add             x1, PP, #0x49, lsl #12  ; [pp+0x49400] TypeArguments: <EditBagBottomSheet>
    //     0xc803d8: ldr             x1, [x1, #0x400]
    // 0xc803dc: r0 = _EditBagBottomSheetState()
    //     0xc803dc: bl              #0xc80404  ; Allocate_EditBagBottomSheetStateStub -> _EditBagBottomSheetState (size=0x24)
    // 0xc803e0: mov             x1, x0
    // 0xc803e4: stur            x0, [fp, #-8]
    // 0xc803e8: r0 = _EditBagBottomSheetState()
    //     0xc803e8: bl              #0xc7af20  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_bag_bottom_sheet.dart] _EditBagBottomSheetState::_EditBagBottomSheetState
    // 0xc803ec: ldur            x0, [fp, #-8]
    // 0xc803f0: LeaveFrame
    //     0xc803f0: mov             SP, fp
    //     0xc803f4: ldp             fp, lr, [SP], #0x10
    // 0xc803f8: ret
    //     0xc803f8: ret             
    // 0xc803fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc803fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80400: b               #0xc803d4
  }
}
