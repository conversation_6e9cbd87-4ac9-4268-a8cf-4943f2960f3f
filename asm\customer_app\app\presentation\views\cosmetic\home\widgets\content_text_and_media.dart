// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/content_text_and_media.dart

// class id: 1049279, size: 0x8
class :: {
}

// class id: 3427, size: 0x14, field offset: 0x14
class _ContentTextAndMediaState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae5b84, size: 0x158
    // 0xae5b84: EnterFrame
    //     0xae5b84: stp             fp, lr, [SP, #-0x10]!
    //     0xae5b88: mov             fp, SP
    // 0xae5b8c: AllocStack(0x28)
    //     0xae5b8c: sub             SP, SP, #0x28
    // 0xae5b90: SetupParameters(_ContentTextAndMediaState this /* r1 => r1, fp-0x8 */)
    //     0xae5b90: stur            x1, [fp, #-8]
    // 0xae5b94: CheckStackOverflow
    //     0xae5b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5b98: cmp             SP, x16
    //     0xae5b9c: b.ls            #0xae5cd0
    // 0xae5ba0: r1 = 1
    //     0xae5ba0: movz            x1, #0x1
    // 0xae5ba4: r0 = AllocateContext()
    //     0xae5ba4: bl              #0x16f6108  ; AllocateContextStub
    // 0xae5ba8: mov             x1, x0
    // 0xae5bac: ldur            x0, [fp, #-8]
    // 0xae5bb0: StoreField: r1->field_f = r0
    //     0xae5bb0: stur            w0, [x1, #0xf]
    // 0xae5bb4: LoadField: r2 = r0->field_b
    //     0xae5bb4: ldur            w2, [x0, #0xb]
    // 0xae5bb8: DecompressPointer r2
    //     0xae5bb8: add             x2, x2, HEAP, lsl #32
    // 0xae5bbc: cmp             w2, NULL
    // 0xae5bc0: b.eq            #0xae5cd8
    // 0xae5bc4: LoadField: r0 = r2->field_b
    //     0xae5bc4: ldur            w0, [x2, #0xb]
    // 0xae5bc8: DecompressPointer r0
    //     0xae5bc8: add             x0, x0, HEAP, lsl #32
    // 0xae5bcc: cmp             w0, NULL
    // 0xae5bd0: b.ne            #0xae5bdc
    // 0xae5bd4: r3 = Null
    //     0xae5bd4: mov             x3, NULL
    // 0xae5bd8: b               #0xae5be4
    // 0xae5bdc: LoadField: r2 = r0->field_b
    //     0xae5bdc: ldur            w2, [x0, #0xb]
    // 0xae5be0: mov             x3, x2
    // 0xae5be4: mov             x2, x1
    // 0xae5be8: stur            x3, [fp, #-8]
    // 0xae5bec: r1 = Function '<anonymous closure>':.
    //     0xae5bec: add             x1, PP, #0x58, lsl #12  ; [pp+0x58250] AnonymousClosure: (0xae5d00), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/content_text_and_media.dart] _ContentTextAndMediaState::build (0xae5b84)
    //     0xae5bf0: ldr             x1, [x1, #0x250]
    // 0xae5bf4: r0 = AllocateClosure()
    //     0xae5bf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae5bf8: stur            x0, [fp, #-0x10]
    // 0xae5bfc: r0 = ListView()
    //     0xae5bfc: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xae5c00: stur            x0, [fp, #-0x18]
    // 0xae5c04: r16 = true
    //     0xae5c04: add             x16, NULL, #0x20  ; true
    // 0xae5c08: r30 = Instance_NeverScrollableScrollPhysics
    //     0xae5c08: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xae5c0c: ldr             lr, [lr, #0x1c8]
    // 0xae5c10: stp             lr, x16, [SP]
    // 0xae5c14: mov             x1, x0
    // 0xae5c18: ldur            x2, [fp, #-0x10]
    // 0xae5c1c: ldur            x3, [fp, #-8]
    // 0xae5c20: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x4, shrinkWrap, 0x3, null]
    //     0xae5c20: add             x4, PP, #0x38, lsl #12  ; [pp+0x38008] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x4, "shrinkWrap", 0x3, Null]
    //     0xae5c24: ldr             x4, [x4, #8]
    // 0xae5c28: r0 = ListView.builder()
    //     0xae5c28: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xae5c2c: r1 = Null
    //     0xae5c2c: mov             x1, NULL
    // 0xae5c30: r2 = 4
    //     0xae5c30: movz            x2, #0x4
    // 0xae5c34: r0 = AllocateArray()
    //     0xae5c34: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae5c38: stur            x0, [fp, #-8]
    // 0xae5c3c: r16 = Instance_SizedBox
    //     0xae5c3c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d68] Obj!SizedBox@d67d41
    //     0xae5c40: ldr             x16, [x16, #0xd68]
    // 0xae5c44: StoreField: r0->field_f = r16
    //     0xae5c44: stur            w16, [x0, #0xf]
    // 0xae5c48: ldur            x1, [fp, #-0x18]
    // 0xae5c4c: StoreField: r0->field_13 = r1
    //     0xae5c4c: stur            w1, [x0, #0x13]
    // 0xae5c50: r1 = <Widget>
    //     0xae5c50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae5c54: r0 = AllocateGrowableArray()
    //     0xae5c54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae5c58: mov             x1, x0
    // 0xae5c5c: ldur            x0, [fp, #-8]
    // 0xae5c60: stur            x1, [fp, #-0x10]
    // 0xae5c64: StoreField: r1->field_f = r0
    //     0xae5c64: stur            w0, [x1, #0xf]
    // 0xae5c68: r0 = 4
    //     0xae5c68: movz            x0, #0x4
    // 0xae5c6c: StoreField: r1->field_b = r0
    //     0xae5c6c: stur            w0, [x1, #0xb]
    // 0xae5c70: r0 = Column()
    //     0xae5c70: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae5c74: r1 = Instance_Axis
    //     0xae5c74: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae5c78: StoreField: r0->field_f = r1
    //     0xae5c78: stur            w1, [x0, #0xf]
    // 0xae5c7c: r1 = Instance_MainAxisAlignment
    //     0xae5c7c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae5c80: ldr             x1, [x1, #0xa08]
    // 0xae5c84: StoreField: r0->field_13 = r1
    //     0xae5c84: stur            w1, [x0, #0x13]
    // 0xae5c88: r1 = Instance_MainAxisSize
    //     0xae5c88: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae5c8c: ldr             x1, [x1, #0xa10]
    // 0xae5c90: ArrayStore: r0[0] = r1  ; List_4
    //     0xae5c90: stur            w1, [x0, #0x17]
    // 0xae5c94: r1 = Instance_CrossAxisAlignment
    //     0xae5c94: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xae5c98: ldr             x1, [x1, #0x890]
    // 0xae5c9c: StoreField: r0->field_1b = r1
    //     0xae5c9c: stur            w1, [x0, #0x1b]
    // 0xae5ca0: r1 = Instance_VerticalDirection
    //     0xae5ca0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae5ca4: ldr             x1, [x1, #0xa20]
    // 0xae5ca8: StoreField: r0->field_23 = r1
    //     0xae5ca8: stur            w1, [x0, #0x23]
    // 0xae5cac: r1 = Instance_Clip
    //     0xae5cac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae5cb0: ldr             x1, [x1, #0x38]
    // 0xae5cb4: StoreField: r0->field_2b = r1
    //     0xae5cb4: stur            w1, [x0, #0x2b]
    // 0xae5cb8: StoreField: r0->field_2f = rZR
    //     0xae5cb8: stur            xzr, [x0, #0x2f]
    // 0xae5cbc: ldur            x1, [fp, #-0x10]
    // 0xae5cc0: StoreField: r0->field_b = r1
    //     0xae5cc0: stur            w1, [x0, #0xb]
    // 0xae5cc4: LeaveFrame
    //     0xae5cc4: mov             SP, fp
    //     0xae5cc8: ldp             fp, lr, [SP], #0x10
    // 0xae5ccc: ret
    //     0xae5ccc: ret             
    // 0xae5cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5cd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5cd4: b               #0xae5ba0
    // 0xae5cd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5cd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xae5d00, size: 0x100c
    // 0xae5d00: EnterFrame
    //     0xae5d00: stp             fp, lr, [SP, #-0x10]!
    //     0xae5d04: mov             fp, SP
    // 0xae5d08: AllocStack(0x80)
    //     0xae5d08: sub             SP, SP, #0x80
    // 0xae5d0c: SetupParameters()
    //     0xae5d0c: ldr             x0, [fp, #0x20]
    //     0xae5d10: ldur            w1, [x0, #0x17]
    //     0xae5d14: add             x1, x1, HEAP, lsl #32
    //     0xae5d18: stur            x1, [fp, #-8]
    // 0xae5d1c: CheckStackOverflow
    //     0xae5d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5d20: cmp             SP, x16
    //     0xae5d24: b.ls            #0xae6cac
    // 0xae5d28: r0 = Radius()
    //     0xae5d28: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xae5d2c: d0 = 20.000000
    //     0xae5d2c: fmov            d0, #20.00000000
    // 0xae5d30: stur            x0, [fp, #-0x10]
    // 0xae5d34: StoreField: r0->field_7 = d0
    //     0xae5d34: stur            d0, [x0, #7]
    // 0xae5d38: StoreField: r0->field_f = d0
    //     0xae5d38: stur            d0, [x0, #0xf]
    // 0xae5d3c: r0 = BorderRadius()
    //     0xae5d3c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xae5d40: mov             x1, x0
    // 0xae5d44: ldur            x0, [fp, #-0x10]
    // 0xae5d48: stur            x1, [fp, #-0x18]
    // 0xae5d4c: StoreField: r1->field_7 = r0
    //     0xae5d4c: stur            w0, [x1, #7]
    // 0xae5d50: StoreField: r1->field_b = r0
    //     0xae5d50: stur            w0, [x1, #0xb]
    // 0xae5d54: StoreField: r1->field_f = r0
    //     0xae5d54: stur            w0, [x1, #0xf]
    // 0xae5d58: StoreField: r1->field_13 = r0
    //     0xae5d58: stur            w0, [x1, #0x13]
    // 0xae5d5c: r0 = RoundedRectangleBorder()
    //     0xae5d5c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xae5d60: mov             x2, x0
    // 0xae5d64: ldur            x0, [fp, #-0x18]
    // 0xae5d68: stur            x2, [fp, #-0x10]
    // 0xae5d6c: StoreField: r2->field_b = r0
    //     0xae5d6c: stur            w0, [x2, #0xb]
    // 0xae5d70: r0 = Instance_BorderSide
    //     0xae5d70: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xae5d74: ldr             x0, [x0, #0xe20]
    // 0xae5d78: StoreField: r2->field_7 = r0
    //     0xae5d78: stur            w0, [x2, #7]
    // 0xae5d7c: ldur            x3, [fp, #-8]
    // 0xae5d80: LoadField: r0 = r3->field_f
    //     0xae5d80: ldur            w0, [x3, #0xf]
    // 0xae5d84: DecompressPointer r0
    //     0xae5d84: add             x0, x0, HEAP, lsl #32
    // 0xae5d88: LoadField: r1 = r0->field_b
    //     0xae5d88: ldur            w1, [x0, #0xb]
    // 0xae5d8c: DecompressPointer r1
    //     0xae5d8c: add             x1, x1, HEAP, lsl #32
    // 0xae5d90: cmp             w1, NULL
    // 0xae5d94: b.eq            #0xae6cb4
    // 0xae5d98: LoadField: r4 = r1->field_b
    //     0xae5d98: ldur            w4, [x1, #0xb]
    // 0xae5d9c: DecompressPointer r4
    //     0xae5d9c: add             x4, x4, HEAP, lsl #32
    // 0xae5da0: cmp             w4, NULL
    // 0xae5da4: b.ne            #0xae5db4
    // 0xae5da8: ldr             x5, [fp, #0x10]
    // 0xae5dac: r0 = Null
    //     0xae5dac: mov             x0, NULL
    // 0xae5db0: b               #0xae5e14
    // 0xae5db4: ldr             x5, [fp, #0x10]
    // 0xae5db8: LoadField: r0 = r4->field_b
    //     0xae5db8: ldur            w0, [x4, #0xb]
    // 0xae5dbc: r6 = LoadInt32Instr(r5)
    //     0xae5dbc: sbfx            x6, x5, #1, #0x1f
    //     0xae5dc0: tbz             w5, #0, #0xae5dc8
    //     0xae5dc4: ldur            x6, [x5, #7]
    // 0xae5dc8: r1 = LoadInt32Instr(r0)
    //     0xae5dc8: sbfx            x1, x0, #1, #0x1f
    // 0xae5dcc: mov             x0, x1
    // 0xae5dd0: mov             x1, x6
    // 0xae5dd4: cmp             x1, x0
    // 0xae5dd8: b.hs            #0xae6cb8
    // 0xae5ddc: LoadField: r0 = r4->field_f
    //     0xae5ddc: ldur            w0, [x4, #0xf]
    // 0xae5de0: DecompressPointer r0
    //     0xae5de0: add             x0, x0, HEAP, lsl #32
    // 0xae5de4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xae5de4: add             x16, x0, x6, lsl #2
    //     0xae5de8: ldur            w1, [x16, #0xf]
    // 0xae5dec: DecompressPointer r1
    //     0xae5dec: add             x1, x1, HEAP, lsl #32
    // 0xae5df0: LoadField: r0 = r1->field_27
    //     0xae5df0: ldur            w0, [x1, #0x27]
    // 0xae5df4: DecompressPointer r0
    //     0xae5df4: add             x0, x0, HEAP, lsl #32
    // 0xae5df8: cmp             w0, NULL
    // 0xae5dfc: b.ne            #0xae5e08
    // 0xae5e00: r0 = Null
    //     0xae5e00: mov             x0, NULL
    // 0xae5e04: b               #0xae5e14
    // 0xae5e08: LoadField: r1 = r0->field_f
    //     0xae5e08: ldur            w1, [x0, #0xf]
    // 0xae5e0c: DecompressPointer r1
    //     0xae5e0c: add             x1, x1, HEAP, lsl #32
    // 0xae5e10: mov             x0, x1
    // 0xae5e14: r1 = LoadClassIdInstr(r0)
    //     0xae5e14: ldur            x1, [x0, #-1]
    //     0xae5e18: ubfx            x1, x1, #0xc, #0x14
    // 0xae5e1c: r16 = "LEFT"
    //     0xae5e1c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53770] "LEFT"
    //     0xae5e20: ldr             x16, [x16, #0x770]
    // 0xae5e24: stp             x16, x0, [SP]
    // 0xae5e28: mov             x0, x1
    // 0xae5e2c: mov             lr, x0
    // 0xae5e30: ldr             lr, [x21, lr, lsl #3]
    // 0xae5e34: blr             lr
    // 0xae5e38: tbnz            w0, #4, #0xae6504
    // 0xae5e3c: ldur            x3, [fp, #-8]
    // 0xae5e40: LoadField: r0 = r3->field_f
    //     0xae5e40: ldur            w0, [x3, #0xf]
    // 0xae5e44: DecompressPointer r0
    //     0xae5e44: add             x0, x0, HEAP, lsl #32
    // 0xae5e48: LoadField: r1 = r0->field_b
    //     0xae5e48: ldur            w1, [x0, #0xb]
    // 0xae5e4c: DecompressPointer r1
    //     0xae5e4c: add             x1, x1, HEAP, lsl #32
    // 0xae5e50: cmp             w1, NULL
    // 0xae5e54: b.eq            #0xae6cbc
    // 0xae5e58: LoadField: r2 = r1->field_b
    //     0xae5e58: ldur            w2, [x1, #0xb]
    // 0xae5e5c: DecompressPointer r2
    //     0xae5e5c: add             x2, x2, HEAP, lsl #32
    // 0xae5e60: cmp             w2, NULL
    // 0xae5e64: b.ne            #0xae5e74
    // 0xae5e68: ldr             x4, [fp, #0x10]
    // 0xae5e6c: r0 = Null
    //     0xae5e6c: mov             x0, NULL
    // 0xae5e70: b               #0xae5eb8
    // 0xae5e74: ldr             x4, [fp, #0x10]
    // 0xae5e78: LoadField: r0 = r2->field_b
    //     0xae5e78: ldur            w0, [x2, #0xb]
    // 0xae5e7c: r5 = LoadInt32Instr(r4)
    //     0xae5e7c: sbfx            x5, x4, #1, #0x1f
    //     0xae5e80: tbz             w4, #0, #0xae5e88
    //     0xae5e84: ldur            x5, [x4, #7]
    // 0xae5e88: r1 = LoadInt32Instr(r0)
    //     0xae5e88: sbfx            x1, x0, #1, #0x1f
    // 0xae5e8c: mov             x0, x1
    // 0xae5e90: mov             x1, x5
    // 0xae5e94: cmp             x1, x0
    // 0xae5e98: b.hs            #0xae6cc0
    // 0xae5e9c: LoadField: r0 = r2->field_f
    //     0xae5e9c: ldur            w0, [x2, #0xf]
    // 0xae5ea0: DecompressPointer r0
    //     0xae5ea0: add             x0, x0, HEAP, lsl #32
    // 0xae5ea4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xae5ea4: add             x16, x0, x5, lsl #2
    //     0xae5ea8: ldur            w1, [x16, #0xf]
    // 0xae5eac: DecompressPointer r1
    //     0xae5eac: add             x1, x1, HEAP, lsl #32
    // 0xae5eb0: LoadField: r0 = r1->field_13
    //     0xae5eb0: ldur            w0, [x1, #0x13]
    // 0xae5eb4: DecompressPointer r0
    //     0xae5eb4: add             x0, x0, HEAP, lsl #32
    // 0xae5eb8: cmp             w0, NULL
    // 0xae5ebc: b.ne            #0xae5ec4
    // 0xae5ec0: r0 = ""
    //     0xae5ec0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae5ec4: stur            x0, [fp, #-0x18]
    // 0xae5ec8: r1 = Function '<anonymous closure>':.
    //     0xae5ec8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58258] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae5ecc: ldr             x1, [x1, #0x258]
    // 0xae5ed0: r2 = Null
    //     0xae5ed0: mov             x2, NULL
    // 0xae5ed4: r0 = AllocateClosure()
    //     0xae5ed4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae5ed8: r1 = Function '<anonymous closure>':.
    //     0xae5ed8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58260] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae5edc: ldr             x1, [x1, #0x260]
    // 0xae5ee0: r2 = Null
    //     0xae5ee0: mov             x2, NULL
    // 0xae5ee4: stur            x0, [fp, #-0x20]
    // 0xae5ee8: r0 = AllocateClosure()
    //     0xae5ee8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae5eec: stur            x0, [fp, #-0x28]
    // 0xae5ef0: r0 = CachedNetworkImage()
    //     0xae5ef0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xae5ef4: stur            x0, [fp, #-0x30]
    // 0xae5ef8: r16 = 160.000000
    //     0xae5ef8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58268] 160
    //     0xae5efc: ldr             x16, [x16, #0x268]
    // 0xae5f00: r30 = inf
    //     0xae5f00: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xae5f04: ldr             lr, [lr, #0x9f8]
    // 0xae5f08: stp             lr, x16, [SP, #0x18]
    // 0xae5f0c: ldur            x16, [fp, #-0x20]
    // 0xae5f10: ldur            lr, [fp, #-0x28]
    // 0xae5f14: stp             lr, x16, [SP, #8]
    // 0xae5f18: r16 = Instance_BoxFit
    //     0xae5f18: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xae5f1c: ldr             x16, [x16, #0x118]
    // 0xae5f20: str             x16, [SP]
    // 0xae5f24: mov             x1, x0
    // 0xae5f28: ldur            x2, [fp, #-0x18]
    // 0xae5f2c: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x5, fit, 0x6, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xae5f2c: add             x4, PP, #0x54, lsl #12  ; [pp+0x542b0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x5, "fit", 0x6, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xae5f30: ldr             x4, [x4, #0x2b0]
    // 0xae5f34: r0 = CachedNetworkImage()
    //     0xae5f34: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xae5f38: ldur            x2, [fp, #-8]
    // 0xae5f3c: LoadField: r0 = r2->field_f
    //     0xae5f3c: ldur            w0, [x2, #0xf]
    // 0xae5f40: DecompressPointer r0
    //     0xae5f40: add             x0, x0, HEAP, lsl #32
    // 0xae5f44: LoadField: r1 = r0->field_b
    //     0xae5f44: ldur            w1, [x0, #0xb]
    // 0xae5f48: DecompressPointer r1
    //     0xae5f48: add             x1, x1, HEAP, lsl #32
    // 0xae5f4c: cmp             w1, NULL
    // 0xae5f50: b.eq            #0xae6cc4
    // 0xae5f54: LoadField: r3 = r1->field_b
    //     0xae5f54: ldur            w3, [x1, #0xb]
    // 0xae5f58: DecompressPointer r3
    //     0xae5f58: add             x3, x3, HEAP, lsl #32
    // 0xae5f5c: cmp             w3, NULL
    // 0xae5f60: b.ne            #0xae5f70
    // 0xae5f64: ldr             x4, [fp, #0x10]
    // 0xae5f68: r0 = Null
    //     0xae5f68: mov             x0, NULL
    // 0xae5f6c: b               #0xae5fb4
    // 0xae5f70: ldr             x4, [fp, #0x10]
    // 0xae5f74: LoadField: r0 = r3->field_b
    //     0xae5f74: ldur            w0, [x3, #0xb]
    // 0xae5f78: r5 = LoadInt32Instr(r4)
    //     0xae5f78: sbfx            x5, x4, #1, #0x1f
    //     0xae5f7c: tbz             w4, #0, #0xae5f84
    //     0xae5f80: ldur            x5, [x4, #7]
    // 0xae5f84: r1 = LoadInt32Instr(r0)
    //     0xae5f84: sbfx            x1, x0, #1, #0x1f
    // 0xae5f88: mov             x0, x1
    // 0xae5f8c: mov             x1, x5
    // 0xae5f90: cmp             x1, x0
    // 0xae5f94: b.hs            #0xae6cc8
    // 0xae5f98: LoadField: r0 = r3->field_f
    //     0xae5f98: ldur            w0, [x3, #0xf]
    // 0xae5f9c: DecompressPointer r0
    //     0xae5f9c: add             x0, x0, HEAP, lsl #32
    // 0xae5fa0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xae5fa0: add             x16, x0, x5, lsl #2
    //     0xae5fa4: ldur            w1, [x16, #0xf]
    // 0xae5fa8: DecompressPointer r1
    //     0xae5fa8: add             x1, x1, HEAP, lsl #32
    // 0xae5fac: LoadField: r0 = r1->field_7
    //     0xae5fac: ldur            w0, [x1, #7]
    // 0xae5fb0: DecompressPointer r0
    //     0xae5fb0: add             x0, x0, HEAP, lsl #32
    // 0xae5fb4: cmp             w0, NULL
    // 0xae5fb8: b.ne            #0xae5fc0
    // 0xae5fbc: r0 = ""
    //     0xae5fbc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae5fc0: ldr             x1, [fp, #0x18]
    // 0xae5fc4: stur            x0, [fp, #-0x18]
    // 0xae5fc8: r0 = of()
    //     0xae5fc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae5fcc: LoadField: r1 = r0->field_87
    //     0xae5fcc: ldur            w1, [x0, #0x87]
    // 0xae5fd0: DecompressPointer r1
    //     0xae5fd0: add             x1, x1, HEAP, lsl #32
    // 0xae5fd4: LoadField: r0 = r1->field_7
    //     0xae5fd4: ldur            w0, [x1, #7]
    // 0xae5fd8: DecompressPointer r0
    //     0xae5fd8: add             x0, x0, HEAP, lsl #32
    // 0xae5fdc: r16 = 21.000000
    //     0xae5fdc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xae5fe0: ldr             x16, [x16, #0x9b0]
    // 0xae5fe4: r30 = Instance_Color
    //     0xae5fe4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae5fe8: stp             lr, x16, [SP]
    // 0xae5fec: mov             x1, x0
    // 0xae5ff0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae5ff0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae5ff4: ldr             x4, [x4, #0xaa0]
    // 0xae5ff8: r0 = copyWith()
    //     0xae5ff8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae5ffc: stur            x0, [fp, #-0x20]
    // 0xae6000: r0 = Text()
    //     0xae6000: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae6004: mov             x1, x0
    // 0xae6008: ldur            x0, [fp, #-0x18]
    // 0xae600c: stur            x1, [fp, #-0x28]
    // 0xae6010: StoreField: r1->field_b = r0
    //     0xae6010: stur            w0, [x1, #0xb]
    // 0xae6014: ldur            x0, [fp, #-0x20]
    // 0xae6018: StoreField: r1->field_13 = r0
    //     0xae6018: stur            w0, [x1, #0x13]
    // 0xae601c: r0 = Padding()
    //     0xae601c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae6020: mov             x2, x0
    // 0xae6024: r0 = Instance_EdgeInsets
    //     0xae6024: add             x0, PP, #0x58, lsl #12  ; [pp+0x58270] Obj!EdgeInsets@d58401
    //     0xae6028: ldr             x0, [x0, #0x270]
    // 0xae602c: stur            x2, [fp, #-0x20]
    // 0xae6030: StoreField: r2->field_f = r0
    //     0xae6030: stur            w0, [x2, #0xf]
    // 0xae6034: ldur            x0, [fp, #-0x28]
    // 0xae6038: StoreField: r2->field_b = r0
    //     0xae6038: stur            w0, [x2, #0xb]
    // 0xae603c: ldur            x3, [fp, #-8]
    // 0xae6040: LoadField: r0 = r3->field_f
    //     0xae6040: ldur            w0, [x3, #0xf]
    // 0xae6044: DecompressPointer r0
    //     0xae6044: add             x0, x0, HEAP, lsl #32
    // 0xae6048: LoadField: r1 = r0->field_b
    //     0xae6048: ldur            w1, [x0, #0xb]
    // 0xae604c: DecompressPointer r1
    //     0xae604c: add             x1, x1, HEAP, lsl #32
    // 0xae6050: cmp             w1, NULL
    // 0xae6054: b.eq            #0xae6ccc
    // 0xae6058: LoadField: r4 = r1->field_b
    //     0xae6058: ldur            w4, [x1, #0xb]
    // 0xae605c: DecompressPointer r4
    //     0xae605c: add             x4, x4, HEAP, lsl #32
    // 0xae6060: cmp             w4, NULL
    // 0xae6064: b.ne            #0xae6074
    // 0xae6068: ldr             x5, [fp, #0x10]
    // 0xae606c: r0 = Null
    //     0xae606c: mov             x0, NULL
    // 0xae6070: b               #0xae60b8
    // 0xae6074: ldr             x5, [fp, #0x10]
    // 0xae6078: LoadField: r0 = r4->field_b
    //     0xae6078: ldur            w0, [x4, #0xb]
    // 0xae607c: r6 = LoadInt32Instr(r5)
    //     0xae607c: sbfx            x6, x5, #1, #0x1f
    //     0xae6080: tbz             w5, #0, #0xae6088
    //     0xae6084: ldur            x6, [x5, #7]
    // 0xae6088: r1 = LoadInt32Instr(r0)
    //     0xae6088: sbfx            x1, x0, #1, #0x1f
    // 0xae608c: mov             x0, x1
    // 0xae6090: mov             x1, x6
    // 0xae6094: cmp             x1, x0
    // 0xae6098: b.hs            #0xae6cd0
    // 0xae609c: LoadField: r0 = r4->field_f
    //     0xae609c: ldur            w0, [x4, #0xf]
    // 0xae60a0: DecompressPointer r0
    //     0xae60a0: add             x0, x0, HEAP, lsl #32
    // 0xae60a4: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xae60a4: add             x16, x0, x6, lsl #2
    //     0xae60a8: ldur            w1, [x16, #0xf]
    // 0xae60ac: DecompressPointer r1
    //     0xae60ac: add             x1, x1, HEAP, lsl #32
    // 0xae60b0: LoadField: r0 = r1->field_b
    //     0xae60b0: ldur            w0, [x1, #0xb]
    // 0xae60b4: DecompressPointer r0
    //     0xae60b4: add             x0, x0, HEAP, lsl #32
    // 0xae60b8: cmp             w0, NULL
    // 0xae60bc: b.ne            #0xae60c4
    // 0xae60c0: r0 = ""
    //     0xae60c0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae60c4: ldr             x1, [fp, #0x18]
    // 0xae60c8: stur            x0, [fp, #-0x18]
    // 0xae60cc: r0 = of()
    //     0xae60cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae60d0: LoadField: r1 = r0->field_87
    //     0xae60d0: ldur            w1, [x0, #0x87]
    // 0xae60d4: DecompressPointer r1
    //     0xae60d4: add             x1, x1, HEAP, lsl #32
    // 0xae60d8: LoadField: r0 = r1->field_2b
    //     0xae60d8: ldur            w0, [x1, #0x2b]
    // 0xae60dc: DecompressPointer r0
    //     0xae60dc: add             x0, x0, HEAP, lsl #32
    // 0xae60e0: r16 = 14.000000
    //     0xae60e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xae60e4: ldr             x16, [x16, #0x1d8]
    // 0xae60e8: r30 = Instance_Color
    //     0xae60e8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae60ec: stp             lr, x16, [SP]
    // 0xae60f0: mov             x1, x0
    // 0xae60f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae60f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae60f8: ldr             x4, [x4, #0xaa0]
    // 0xae60fc: r0 = copyWith()
    //     0xae60fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae6100: stur            x0, [fp, #-0x28]
    // 0xae6104: r0 = HtmlWidget()
    //     0xae6104: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xae6108: mov             x1, x0
    // 0xae610c: ldur            x0, [fp, #-0x18]
    // 0xae6110: stur            x1, [fp, #-0x38]
    // 0xae6114: StoreField: r1->field_1f = r0
    //     0xae6114: stur            w0, [x1, #0x1f]
    // 0xae6118: r2 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xae6118: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xae611c: ldr             x2, [x2, #0x1e0]
    // 0xae6120: StoreField: r1->field_23 = r2
    //     0xae6120: stur            w2, [x1, #0x23]
    // 0xae6124: r3 = Instance_ColumnMode
    //     0xae6124: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xae6128: ldr             x3, [x3, #0x1e8]
    // 0xae612c: StoreField: r1->field_3b = r3
    //     0xae612c: stur            w3, [x1, #0x3b]
    // 0xae6130: ldur            x0, [fp, #-0x28]
    // 0xae6134: StoreField: r1->field_3f = r0
    //     0xae6134: stur            w0, [x1, #0x3f]
    // 0xae6138: r0 = Padding()
    //     0xae6138: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae613c: r4 = Instance_EdgeInsets
    //     0xae613c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0xae6140: ldr             x4, [x4, #0xe48]
    // 0xae6144: stur            x0, [fp, #-0x18]
    // 0xae6148: StoreField: r0->field_f = r4
    //     0xae6148: stur            w4, [x0, #0xf]
    // 0xae614c: ldur            x1, [fp, #-0x38]
    // 0xae6150: StoreField: r0->field_b = r1
    //     0xae6150: stur            w1, [x0, #0xb]
    // 0xae6154: r0 = Center()
    //     0xae6154: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae6158: mov             x2, x0
    // 0xae615c: r5 = Instance_Alignment
    //     0xae615c: add             x5, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xae6160: ldr             x5, [x5, #0xb10]
    // 0xae6164: stur            x2, [fp, #-0x28]
    // 0xae6168: StoreField: r2->field_f = r5
    //     0xae6168: stur            w5, [x2, #0xf]
    // 0xae616c: ldur            x0, [fp, #-0x18]
    // 0xae6170: StoreField: r2->field_b = r0
    //     0xae6170: stur            w0, [x2, #0xb]
    // 0xae6174: ldur            x3, [fp, #-8]
    // 0xae6178: LoadField: r0 = r3->field_f
    //     0xae6178: ldur            w0, [x3, #0xf]
    // 0xae617c: DecompressPointer r0
    //     0xae617c: add             x0, x0, HEAP, lsl #32
    // 0xae6180: LoadField: r1 = r0->field_b
    //     0xae6180: ldur            w1, [x0, #0xb]
    // 0xae6184: DecompressPointer r1
    //     0xae6184: add             x1, x1, HEAP, lsl #32
    // 0xae6188: cmp             w1, NULL
    // 0xae618c: b.eq            #0xae6cd4
    // 0xae6190: LoadField: r4 = r1->field_b
    //     0xae6190: ldur            w4, [x1, #0xb]
    // 0xae6194: DecompressPointer r4
    //     0xae6194: add             x4, x4, HEAP, lsl #32
    // 0xae6198: cmp             w4, NULL
    // 0xae619c: b.ne            #0xae61ac
    // 0xae61a0: ldr             x5, [fp, #0x10]
    // 0xae61a4: r0 = Null
    //     0xae61a4: mov             x0, NULL
    // 0xae61a8: b               #0xae6214
    // 0xae61ac: ldr             x5, [fp, #0x10]
    // 0xae61b0: LoadField: r0 = r4->field_b
    //     0xae61b0: ldur            w0, [x4, #0xb]
    // 0xae61b4: r6 = LoadInt32Instr(r5)
    //     0xae61b4: sbfx            x6, x5, #1, #0x1f
    //     0xae61b8: tbz             w5, #0, #0xae61c0
    //     0xae61bc: ldur            x6, [x5, #7]
    // 0xae61c0: r1 = LoadInt32Instr(r0)
    //     0xae61c0: sbfx            x1, x0, #1, #0x1f
    // 0xae61c4: mov             x0, x1
    // 0xae61c8: mov             x1, x6
    // 0xae61cc: cmp             x1, x0
    // 0xae61d0: b.hs            #0xae6cd8
    // 0xae61d4: LoadField: r0 = r4->field_f
    //     0xae61d4: ldur            w0, [x4, #0xf]
    // 0xae61d8: DecompressPointer r0
    //     0xae61d8: add             x0, x0, HEAP, lsl #32
    // 0xae61dc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xae61dc: add             x16, x0, x6, lsl #2
    //     0xae61e0: ldur            w1, [x16, #0xf]
    // 0xae61e4: DecompressPointer r1
    //     0xae61e4: add             x1, x1, HEAP, lsl #32
    // 0xae61e8: LoadField: r0 = r1->field_f
    //     0xae61e8: ldur            w0, [x1, #0xf]
    // 0xae61ec: DecompressPointer r0
    //     0xae61ec: add             x0, x0, HEAP, lsl #32
    // 0xae61f0: cmp             w0, NULL
    // 0xae61f4: b.ne            #0xae6200
    // 0xae61f8: r0 = Null
    //     0xae61f8: mov             x0, NULL
    // 0xae61fc: b               #0xae6214
    // 0xae6200: LoadField: r1 = r0->field_7
    //     0xae6200: ldur            w1, [x0, #7]
    // 0xae6204: cbnz            w1, #0xae6210
    // 0xae6208: r0 = false
    //     0xae6208: add             x0, NULL, #0x30  ; false
    // 0xae620c: b               #0xae6214
    // 0xae6210: r0 = true
    //     0xae6210: add             x0, NULL, #0x20  ; true
    // 0xae6214: cmp             w0, NULL
    // 0xae6218: b.ne            #0xae6220
    // 0xae621c: r0 = false
    //     0xae621c: add             x0, NULL, #0x30  ; false
    // 0xae6220: ldr             x1, [fp, #0x18]
    // 0xae6224: stur            x0, [fp, #-0x18]
    // 0xae6228: r0 = of()
    //     0xae6228: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae622c: r17 = 307
    //     0xae622c: movz            x17, #0x133
    // 0xae6230: ldr             w1, [x0, x17]
    // 0xae6234: DecompressPointer r1
    //     0xae6234: add             x1, x1, HEAP, lsl #32
    // 0xae6238: stur            x1, [fp, #-0x38]
    // 0xae623c: r16 = <EdgeInsets>
    //     0xae623c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xae6240: ldr             x16, [x16, #0xda0]
    // 0xae6244: r30 = Instance_EdgeInsets
    //     0xae6244: add             lr, PP, #0x55, lsl #12  ; [pp+0x55d48] Obj!EdgeInsets@d58ee1
    //     0xae6248: ldr             lr, [lr, #0xd48]
    // 0xae624c: stp             lr, x16, [SP]
    // 0xae6250: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae6250: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae6254: r0 = all()
    //     0xae6254: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae6258: stur            x0, [fp, #-0x40]
    // 0xae625c: r16 = <RoundedRectangleBorder>
    //     0xae625c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xae6260: ldr             x16, [x16, #0xf78]
    // 0xae6264: r30 = Instance_RoundedRectangleBorder
    //     0xae6264: add             lr, PP, #0x57, lsl #12  ; [pp+0x57d70] Obj!RoundedRectangleBorder@d5ac31
    //     0xae6268: ldr             lr, [lr, #0xd70]
    // 0xae626c: stp             lr, x16, [SP]
    // 0xae6270: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae6270: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae6274: r0 = all()
    //     0xae6274: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae6278: stur            x0, [fp, #-0x48]
    // 0xae627c: r0 = ButtonStyle()
    //     0xae627c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xae6280: mov             x2, x0
    // 0xae6284: ldur            x0, [fp, #-0x40]
    // 0xae6288: stur            x2, [fp, #-0x50]
    // 0xae628c: StoreField: r2->field_23 = r0
    //     0xae628c: stur            w0, [x2, #0x23]
    // 0xae6290: ldur            x0, [fp, #-0x48]
    // 0xae6294: StoreField: r2->field_43 = r0
    //     0xae6294: stur            w0, [x2, #0x43]
    // 0xae6298: ldur            x6, [fp, #-8]
    // 0xae629c: LoadField: r0 = r6->field_f
    //     0xae629c: ldur            w0, [x6, #0xf]
    // 0xae62a0: DecompressPointer r0
    //     0xae62a0: add             x0, x0, HEAP, lsl #32
    // 0xae62a4: LoadField: r1 = r0->field_b
    //     0xae62a4: ldur            w1, [x0, #0xb]
    // 0xae62a8: DecompressPointer r1
    //     0xae62a8: add             x1, x1, HEAP, lsl #32
    // 0xae62ac: cmp             w1, NULL
    // 0xae62b0: b.eq            #0xae6cdc
    // 0xae62b4: LoadField: r3 = r1->field_b
    //     0xae62b4: ldur            w3, [x1, #0xb]
    // 0xae62b8: DecompressPointer r3
    //     0xae62b8: add             x3, x3, HEAP, lsl #32
    // 0xae62bc: cmp             w3, NULL
    // 0xae62c0: b.ne            #0xae62cc
    // 0xae62c4: r0 = Null
    //     0xae62c4: mov             x0, NULL
    // 0xae62c8: b               #0xae6310
    // 0xae62cc: ldr             x7, [fp, #0x10]
    // 0xae62d0: LoadField: r0 = r3->field_b
    //     0xae62d0: ldur            w0, [x3, #0xb]
    // 0xae62d4: r4 = LoadInt32Instr(r7)
    //     0xae62d4: sbfx            x4, x7, #1, #0x1f
    //     0xae62d8: tbz             w7, #0, #0xae62e0
    //     0xae62dc: ldur            x4, [x7, #7]
    // 0xae62e0: r1 = LoadInt32Instr(r0)
    //     0xae62e0: sbfx            x1, x0, #1, #0x1f
    // 0xae62e4: mov             x0, x1
    // 0xae62e8: mov             x1, x4
    // 0xae62ec: cmp             x1, x0
    // 0xae62f0: b.hs            #0xae6ce0
    // 0xae62f4: LoadField: r0 = r3->field_f
    //     0xae62f4: ldur            w0, [x3, #0xf]
    // 0xae62f8: DecompressPointer r0
    //     0xae62f8: add             x0, x0, HEAP, lsl #32
    // 0xae62fc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xae62fc: add             x16, x0, x4, lsl #2
    //     0xae6300: ldur            w1, [x16, #0xf]
    // 0xae6304: DecompressPointer r1
    //     0xae6304: add             x1, x1, HEAP, lsl #32
    // 0xae6308: LoadField: r0 = r1->field_f
    //     0xae6308: ldur            w0, [x1, #0xf]
    // 0xae630c: DecompressPointer r0
    //     0xae630c: add             x0, x0, HEAP, lsl #32
    // 0xae6310: cmp             w0, NULL
    // 0xae6314: b.ne            #0xae6320
    // 0xae6318: r7 = ""
    //     0xae6318: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae631c: b               #0xae6324
    // 0xae6320: mov             x7, x0
    // 0xae6324: ldur            x6, [fp, #-0x30]
    // 0xae6328: ldur            x5, [fp, #-0x20]
    // 0xae632c: ldur            x3, [fp, #-0x28]
    // 0xae6330: ldur            x4, [fp, #-0x18]
    // 0xae6334: ldur            x0, [fp, #-0x38]
    // 0xae6338: ldr             x1, [fp, #0x18]
    // 0xae633c: stur            x7, [fp, #-0x40]
    // 0xae6340: r0 = of()
    //     0xae6340: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae6344: LoadField: r1 = r0->field_87
    //     0xae6344: ldur            w1, [x0, #0x87]
    // 0xae6348: DecompressPointer r1
    //     0xae6348: add             x1, x1, HEAP, lsl #32
    // 0xae634c: LoadField: r0 = r1->field_7
    //     0xae634c: ldur            w0, [x1, #7]
    // 0xae6350: DecompressPointer r0
    //     0xae6350: add             x0, x0, HEAP, lsl #32
    // 0xae6354: r16 = Instance_Color
    //     0xae6354: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xae6358: r30 = 16.000000
    //     0xae6358: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xae635c: ldr             lr, [lr, #0x188]
    // 0xae6360: stp             lr, x16, [SP]
    // 0xae6364: mov             x1, x0
    // 0xae6368: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xae6368: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xae636c: ldr             x4, [x4, #0x9b8]
    // 0xae6370: r0 = copyWith()
    //     0xae6370: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae6374: stur            x0, [fp, #-0x48]
    // 0xae6378: r0 = Text()
    //     0xae6378: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae637c: mov             x3, x0
    // 0xae6380: ldur            x0, [fp, #-0x40]
    // 0xae6384: stur            x3, [fp, #-0x58]
    // 0xae6388: StoreField: r3->field_b = r0
    //     0xae6388: stur            w0, [x3, #0xb]
    // 0xae638c: ldur            x0, [fp, #-0x48]
    // 0xae6390: StoreField: r3->field_13 = r0
    //     0xae6390: stur            w0, [x3, #0x13]
    // 0xae6394: r1 = Function '<anonymous closure>':.
    //     0xae6394: add             x1, PP, #0x58, lsl #12  ; [pp+0x58278] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xae6398: ldr             x1, [x1, #0x278]
    // 0xae639c: r2 = Null
    //     0xae639c: mov             x2, NULL
    // 0xae63a0: r0 = AllocateClosure()
    //     0xae63a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae63a4: stur            x0, [fp, #-0x40]
    // 0xae63a8: r0 = TextButton()
    //     0xae63a8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xae63ac: mov             x1, x0
    // 0xae63b0: ldur            x0, [fp, #-0x40]
    // 0xae63b4: stur            x1, [fp, #-0x48]
    // 0xae63b8: StoreField: r1->field_b = r0
    //     0xae63b8: stur            w0, [x1, #0xb]
    // 0xae63bc: ldur            x0, [fp, #-0x50]
    // 0xae63c0: StoreField: r1->field_1b = r0
    //     0xae63c0: stur            w0, [x1, #0x1b]
    // 0xae63c4: r0 = false
    //     0xae63c4: add             x0, NULL, #0x30  ; false
    // 0xae63c8: StoreField: r1->field_27 = r0
    //     0xae63c8: stur            w0, [x1, #0x27]
    // 0xae63cc: r2 = true
    //     0xae63cc: add             x2, NULL, #0x20  ; true
    // 0xae63d0: StoreField: r1->field_2f = r2
    //     0xae63d0: stur            w2, [x1, #0x2f]
    // 0xae63d4: ldur            x3, [fp, #-0x58]
    // 0xae63d8: StoreField: r1->field_37 = r3
    //     0xae63d8: stur            w3, [x1, #0x37]
    // 0xae63dc: r0 = TextButtonTheme()
    //     0xae63dc: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xae63e0: mov             x1, x0
    // 0xae63e4: ldur            x0, [fp, #-0x38]
    // 0xae63e8: stur            x1, [fp, #-0x40]
    // 0xae63ec: StoreField: r1->field_f = r0
    //     0xae63ec: stur            w0, [x1, #0xf]
    // 0xae63f0: ldur            x0, [fp, #-0x48]
    // 0xae63f4: StoreField: r1->field_b = r0
    //     0xae63f4: stur            w0, [x1, #0xb]
    // 0xae63f8: r0 = Visibility()
    //     0xae63f8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xae63fc: mov             x1, x0
    // 0xae6400: ldur            x0, [fp, #-0x40]
    // 0xae6404: stur            x1, [fp, #-0x38]
    // 0xae6408: StoreField: r1->field_b = r0
    //     0xae6408: stur            w0, [x1, #0xb]
    // 0xae640c: r8 = Instance_SizedBox
    //     0xae640c: ldr             x8, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xae6410: StoreField: r1->field_f = r8
    //     0xae6410: stur            w8, [x1, #0xf]
    // 0xae6414: ldur            x0, [fp, #-0x18]
    // 0xae6418: StoreField: r1->field_13 = r0
    //     0xae6418: stur            w0, [x1, #0x13]
    // 0xae641c: r9 = false
    //     0xae641c: add             x9, NULL, #0x30  ; false
    // 0xae6420: ArrayStore: r1[0] = r9  ; List_4
    //     0xae6420: stur            w9, [x1, #0x17]
    // 0xae6424: StoreField: r1->field_1b = r9
    //     0xae6424: stur            w9, [x1, #0x1b]
    // 0xae6428: StoreField: r1->field_1f = r9
    //     0xae6428: stur            w9, [x1, #0x1f]
    // 0xae642c: StoreField: r1->field_23 = r9
    //     0xae642c: stur            w9, [x1, #0x23]
    // 0xae6430: StoreField: r1->field_27 = r9
    //     0xae6430: stur            w9, [x1, #0x27]
    // 0xae6434: StoreField: r1->field_2b = r9
    //     0xae6434: stur            w9, [x1, #0x2b]
    // 0xae6438: r0 = Padding()
    //     0xae6438: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae643c: r10 = Instance_EdgeInsets
    //     0xae643c: add             x10, PP, #0x58, lsl #12  ; [pp+0x58280] Obj!EdgeInsets@d58eb1
    //     0xae6440: ldr             x10, [x10, #0x280]
    // 0xae6444: stur            x0, [fp, #-0x18]
    // 0xae6448: StoreField: r0->field_f = r10
    //     0xae6448: stur            w10, [x0, #0xf]
    // 0xae644c: ldur            x1, [fp, #-0x38]
    // 0xae6450: StoreField: r0->field_b = r1
    //     0xae6450: stur            w1, [x0, #0xb]
    // 0xae6454: r1 = Null
    //     0xae6454: mov             x1, NULL
    // 0xae6458: r2 = 8
    //     0xae6458: movz            x2, #0x8
    // 0xae645c: r0 = AllocateArray()
    //     0xae645c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae6460: mov             x2, x0
    // 0xae6464: ldur            x0, [fp, #-0x30]
    // 0xae6468: stur            x2, [fp, #-0x38]
    // 0xae646c: StoreField: r2->field_f = r0
    //     0xae646c: stur            w0, [x2, #0xf]
    // 0xae6470: ldur            x0, [fp, #-0x20]
    // 0xae6474: StoreField: r2->field_13 = r0
    //     0xae6474: stur            w0, [x2, #0x13]
    // 0xae6478: ldur            x0, [fp, #-0x28]
    // 0xae647c: ArrayStore: r2[0] = r0  ; List_4
    //     0xae647c: stur            w0, [x2, #0x17]
    // 0xae6480: ldur            x0, [fp, #-0x18]
    // 0xae6484: StoreField: r2->field_1b = r0
    //     0xae6484: stur            w0, [x2, #0x1b]
    // 0xae6488: r1 = <Widget>
    //     0xae6488: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae648c: r0 = AllocateGrowableArray()
    //     0xae648c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae6490: mov             x1, x0
    // 0xae6494: ldur            x0, [fp, #-0x38]
    // 0xae6498: stur            x1, [fp, #-0x18]
    // 0xae649c: StoreField: r1->field_f = r0
    //     0xae649c: stur            w0, [x1, #0xf]
    // 0xae64a0: r11 = 8
    //     0xae64a0: movz            x11, #0x8
    // 0xae64a4: StoreField: r1->field_b = r11
    //     0xae64a4: stur            w11, [x1, #0xb]
    // 0xae64a8: r0 = Column()
    //     0xae64a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae64ac: r12 = Instance_Axis
    //     0xae64ac: ldr             x12, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae64b0: StoreField: r0->field_f = r12
    //     0xae64b0: stur            w12, [x0, #0xf]
    // 0xae64b4: r13 = Instance_MainAxisAlignment
    //     0xae64b4: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae64b8: ldr             x13, [x13, #0xa08]
    // 0xae64bc: StoreField: r0->field_13 = r13
    //     0xae64bc: stur            w13, [x0, #0x13]
    // 0xae64c0: r14 = Instance_MainAxisSize
    //     0xae64c0: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae64c4: ldr             x14, [x14, #0xa10]
    // 0xae64c8: ArrayStore: r0[0] = r14  ; List_4
    //     0xae64c8: stur            w14, [x0, #0x17]
    // 0xae64cc: r19 = Instance_CrossAxisAlignment
    //     0xae64cc: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xae64d0: ldr             x19, [x19, #0x890]
    // 0xae64d4: StoreField: r0->field_1b = r19
    //     0xae64d4: stur            w19, [x0, #0x1b]
    // 0xae64d8: r20 = Instance_VerticalDirection
    //     0xae64d8: add             x20, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae64dc: ldr             x20, [x20, #0xa20]
    // 0xae64e0: StoreField: r0->field_23 = r20
    //     0xae64e0: stur            w20, [x0, #0x23]
    // 0xae64e4: r23 = Instance_Clip
    //     0xae64e4: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae64e8: ldr             x23, [x23, #0x38]
    // 0xae64ec: StoreField: r0->field_2b = r23
    //     0xae64ec: stur            w23, [x0, #0x2b]
    // 0xae64f0: StoreField: r0->field_2f = rZR
    //     0xae64f0: stur            xzr, [x0, #0x2f]
    // 0xae64f4: ldur            x1, [fp, #-0x18]
    // 0xae64f8: StoreField: r0->field_b = r1
    //     0xae64f8: stur            w1, [x0, #0xb]
    // 0xae64fc: mov             x1, x0
    // 0xae6500: b               #0xae6c2c
    // 0xae6504: ldr             x7, [fp, #0x10]
    // 0xae6508: ldur            x6, [fp, #-8]
    // 0xae650c: r4 = Instance_EdgeInsets
    //     0xae650c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0xae6510: ldr             x4, [x4, #0xe48]
    // 0xae6514: r10 = Instance_EdgeInsets
    //     0xae6514: add             x10, PP, #0x58, lsl #12  ; [pp+0x58280] Obj!EdgeInsets@d58eb1
    //     0xae6518: ldr             x10, [x10, #0x280]
    // 0xae651c: r19 = Instance_CrossAxisAlignment
    //     0xae651c: add             x19, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xae6520: ldr             x19, [x19, #0x890]
    // 0xae6524: r9 = false
    //     0xae6524: add             x9, NULL, #0x30  ; false
    // 0xae6528: r11 = 8
    //     0xae6528: movz            x11, #0x8
    // 0xae652c: r13 = Instance_MainAxisAlignment
    //     0xae652c: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae6530: ldr             x13, [x13, #0xa08]
    // 0xae6534: r14 = Instance_MainAxisSize
    //     0xae6534: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae6538: ldr             x14, [x14, #0xa10]
    // 0xae653c: r20 = Instance_VerticalDirection
    //     0xae653c: add             x20, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae6540: ldr             x20, [x20, #0xa20]
    // 0xae6544: r12 = Instance_Axis
    //     0xae6544: ldr             x12, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae6548: r8 = Instance_SizedBox
    //     0xae6548: ldr             x8, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xae654c: r23 = Instance_Clip
    //     0xae654c: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae6550: ldr             x23, [x23, #0x38]
    // 0xae6554: r2 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xae6554: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xae6558: ldr             x2, [x2, #0x1e0]
    // 0xae655c: r3 = Instance_ColumnMode
    //     0xae655c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xae6560: ldr             x3, [x3, #0x1e8]
    // 0xae6564: r5 = Instance_Alignment
    //     0xae6564: add             x5, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xae6568: ldr             x5, [x5, #0xb10]
    // 0xae656c: LoadField: r0 = r6->field_f
    //     0xae656c: ldur            w0, [x6, #0xf]
    // 0xae6570: DecompressPointer r0
    //     0xae6570: add             x0, x0, HEAP, lsl #32
    // 0xae6574: LoadField: r1 = r0->field_b
    //     0xae6574: ldur            w1, [x0, #0xb]
    // 0xae6578: DecompressPointer r1
    //     0xae6578: add             x1, x1, HEAP, lsl #32
    // 0xae657c: cmp             w1, NULL
    // 0xae6580: b.eq            #0xae6ce4
    // 0xae6584: LoadField: r24 = r1->field_b
    //     0xae6584: ldur            w24, [x1, #0xb]
    // 0xae6588: DecompressPointer r24
    //     0xae6588: add             x24, x24, HEAP, lsl #32
    // 0xae658c: cmp             w24, NULL
    // 0xae6590: b.ne            #0xae659c
    // 0xae6594: r0 = Null
    //     0xae6594: mov             x0, NULL
    // 0xae6598: b               #0xae65dc
    // 0xae659c: LoadField: r0 = r24->field_b
    //     0xae659c: ldur            w0, [x24, #0xb]
    // 0xae65a0: r25 = LoadInt32Instr(r7)
    //     0xae65a0: sbfx            x25, x7, #1, #0x1f
    //     0xae65a4: tbz             w7, #0, #0xae65ac
    //     0xae65a8: ldur            x25, [x7, #7]
    // 0xae65ac: r1 = LoadInt32Instr(r0)
    //     0xae65ac: sbfx            x1, x0, #1, #0x1f
    // 0xae65b0: mov             x0, x1
    // 0xae65b4: mov             x1, x25
    // 0xae65b8: cmp             x1, x0
    // 0xae65bc: b.hs            #0xae6ce8
    // 0xae65c0: LoadField: r0 = r24->field_f
    //     0xae65c0: ldur            w0, [x24, #0xf]
    // 0xae65c4: DecompressPointer r0
    //     0xae65c4: add             x0, x0, HEAP, lsl #32
    // 0xae65c8: ArrayLoad: r1 = r0[r25]  ; Unknown_4
    //     0xae65c8: add             x16, x0, x25, lsl #2
    //     0xae65cc: ldur            w1, [x16, #0xf]
    // 0xae65d0: DecompressPointer r1
    //     0xae65d0: add             x1, x1, HEAP, lsl #32
    // 0xae65d4: LoadField: r0 = r1->field_7
    //     0xae65d4: ldur            w0, [x1, #7]
    // 0xae65d8: DecompressPointer r0
    //     0xae65d8: add             x0, x0, HEAP, lsl #32
    // 0xae65dc: cmp             w0, NULL
    // 0xae65e0: b.ne            #0xae65e8
    // 0xae65e4: r0 = ""
    //     0xae65e4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae65e8: ldr             x1, [fp, #0x18]
    // 0xae65ec: stur            x0, [fp, #-0x18]
    // 0xae65f0: r0 = of()
    //     0xae65f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae65f4: LoadField: r1 = r0->field_87
    //     0xae65f4: ldur            w1, [x0, #0x87]
    // 0xae65f8: DecompressPointer r1
    //     0xae65f8: add             x1, x1, HEAP, lsl #32
    // 0xae65fc: LoadField: r0 = r1->field_7
    //     0xae65fc: ldur            w0, [x1, #7]
    // 0xae6600: DecompressPointer r0
    //     0xae6600: add             x0, x0, HEAP, lsl #32
    // 0xae6604: r16 = 21.000000
    //     0xae6604: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xae6608: ldr             x16, [x16, #0x9b0]
    // 0xae660c: r30 = Instance_Color
    //     0xae660c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae6610: stp             lr, x16, [SP]
    // 0xae6614: mov             x1, x0
    // 0xae6618: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae6618: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae661c: ldr             x4, [x4, #0xaa0]
    // 0xae6620: r0 = copyWith()
    //     0xae6620: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae6624: stur            x0, [fp, #-0x20]
    // 0xae6628: r0 = Text()
    //     0xae6628: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae662c: mov             x1, x0
    // 0xae6630: ldur            x0, [fp, #-0x18]
    // 0xae6634: stur            x1, [fp, #-0x28]
    // 0xae6638: StoreField: r1->field_b = r0
    //     0xae6638: stur            w0, [x1, #0xb]
    // 0xae663c: ldur            x0, [fp, #-0x20]
    // 0xae6640: StoreField: r1->field_13 = r0
    //     0xae6640: stur            w0, [x1, #0x13]
    // 0xae6644: r0 = Padding()
    //     0xae6644: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae6648: mov             x2, x0
    // 0xae664c: r0 = Instance_EdgeInsets
    //     0xae664c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0xae6650: ldr             x0, [x0, #0xa98]
    // 0xae6654: stur            x2, [fp, #-0x20]
    // 0xae6658: StoreField: r2->field_f = r0
    //     0xae6658: stur            w0, [x2, #0xf]
    // 0xae665c: ldur            x0, [fp, #-0x28]
    // 0xae6660: StoreField: r2->field_b = r0
    //     0xae6660: stur            w0, [x2, #0xb]
    // 0xae6664: ldur            x3, [fp, #-8]
    // 0xae6668: LoadField: r0 = r3->field_f
    //     0xae6668: ldur            w0, [x3, #0xf]
    // 0xae666c: DecompressPointer r0
    //     0xae666c: add             x0, x0, HEAP, lsl #32
    // 0xae6670: LoadField: r1 = r0->field_b
    //     0xae6670: ldur            w1, [x0, #0xb]
    // 0xae6674: DecompressPointer r1
    //     0xae6674: add             x1, x1, HEAP, lsl #32
    // 0xae6678: cmp             w1, NULL
    // 0xae667c: b.eq            #0xae6cec
    // 0xae6680: LoadField: r4 = r1->field_b
    //     0xae6680: ldur            w4, [x1, #0xb]
    // 0xae6684: DecompressPointer r4
    //     0xae6684: add             x4, x4, HEAP, lsl #32
    // 0xae6688: cmp             w4, NULL
    // 0xae668c: b.ne            #0xae669c
    // 0xae6690: ldr             x5, [fp, #0x10]
    // 0xae6694: r0 = Null
    //     0xae6694: mov             x0, NULL
    // 0xae6698: b               #0xae66e0
    // 0xae669c: ldr             x5, [fp, #0x10]
    // 0xae66a0: LoadField: r0 = r4->field_b
    //     0xae66a0: ldur            w0, [x4, #0xb]
    // 0xae66a4: r6 = LoadInt32Instr(r5)
    //     0xae66a4: sbfx            x6, x5, #1, #0x1f
    //     0xae66a8: tbz             w5, #0, #0xae66b0
    //     0xae66ac: ldur            x6, [x5, #7]
    // 0xae66b0: r1 = LoadInt32Instr(r0)
    //     0xae66b0: sbfx            x1, x0, #1, #0x1f
    // 0xae66b4: mov             x0, x1
    // 0xae66b8: mov             x1, x6
    // 0xae66bc: cmp             x1, x0
    // 0xae66c0: b.hs            #0xae6cf0
    // 0xae66c4: LoadField: r0 = r4->field_f
    //     0xae66c4: ldur            w0, [x4, #0xf]
    // 0xae66c8: DecompressPointer r0
    //     0xae66c8: add             x0, x0, HEAP, lsl #32
    // 0xae66cc: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xae66cc: add             x16, x0, x6, lsl #2
    //     0xae66d0: ldur            w1, [x16, #0xf]
    // 0xae66d4: DecompressPointer r1
    //     0xae66d4: add             x1, x1, HEAP, lsl #32
    // 0xae66d8: LoadField: r0 = r1->field_b
    //     0xae66d8: ldur            w0, [x1, #0xb]
    // 0xae66dc: DecompressPointer r0
    //     0xae66dc: add             x0, x0, HEAP, lsl #32
    // 0xae66e0: cmp             w0, NULL
    // 0xae66e4: b.ne            #0xae66ec
    // 0xae66e8: r0 = ""
    //     0xae66e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae66ec: ldr             x1, [fp, #0x18]
    // 0xae66f0: stur            x0, [fp, #-0x18]
    // 0xae66f4: r0 = of()
    //     0xae66f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae66f8: LoadField: r1 = r0->field_87
    //     0xae66f8: ldur            w1, [x0, #0x87]
    // 0xae66fc: DecompressPointer r1
    //     0xae66fc: add             x1, x1, HEAP, lsl #32
    // 0xae6700: LoadField: r0 = r1->field_2b
    //     0xae6700: ldur            w0, [x1, #0x2b]
    // 0xae6704: DecompressPointer r0
    //     0xae6704: add             x0, x0, HEAP, lsl #32
    // 0xae6708: r16 = 14.000000
    //     0xae6708: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xae670c: ldr             x16, [x16, #0x1d8]
    // 0xae6710: r30 = Instance_Color
    //     0xae6710: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xae6714: stp             lr, x16, [SP]
    // 0xae6718: mov             x1, x0
    // 0xae671c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae671c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae6720: ldr             x4, [x4, #0xaa0]
    // 0xae6724: r0 = copyWith()
    //     0xae6724: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae6728: stur            x0, [fp, #-0x28]
    // 0xae672c: r0 = HtmlWidget()
    //     0xae672c: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xae6730: mov             x1, x0
    // 0xae6734: ldur            x0, [fp, #-0x18]
    // 0xae6738: stur            x1, [fp, #-0x30]
    // 0xae673c: StoreField: r1->field_1f = r0
    //     0xae673c: stur            w0, [x1, #0x1f]
    // 0xae6740: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xae6740: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xae6744: ldr             x0, [x0, #0x1e0]
    // 0xae6748: StoreField: r1->field_23 = r0
    //     0xae6748: stur            w0, [x1, #0x23]
    // 0xae674c: r0 = Instance_ColumnMode
    //     0xae674c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xae6750: ldr             x0, [x0, #0x1e8]
    // 0xae6754: StoreField: r1->field_3b = r0
    //     0xae6754: stur            w0, [x1, #0x3b]
    // 0xae6758: ldur            x0, [fp, #-0x28]
    // 0xae675c: StoreField: r1->field_3f = r0
    //     0xae675c: stur            w0, [x1, #0x3f]
    // 0xae6760: r0 = Padding()
    //     0xae6760: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae6764: mov             x1, x0
    // 0xae6768: r0 = Instance_EdgeInsets
    //     0xae6768: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0xae676c: ldr             x0, [x0, #0xe48]
    // 0xae6770: stur            x1, [fp, #-0x18]
    // 0xae6774: StoreField: r1->field_f = r0
    //     0xae6774: stur            w0, [x1, #0xf]
    // 0xae6778: ldur            x0, [fp, #-0x30]
    // 0xae677c: StoreField: r1->field_b = r0
    //     0xae677c: stur            w0, [x1, #0xb]
    // 0xae6780: r0 = Center()
    //     0xae6780: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae6784: mov             x2, x0
    // 0xae6788: r0 = Instance_Alignment
    //     0xae6788: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xae678c: ldr             x0, [x0, #0xb10]
    // 0xae6790: stur            x2, [fp, #-0x28]
    // 0xae6794: StoreField: r2->field_f = r0
    //     0xae6794: stur            w0, [x2, #0xf]
    // 0xae6798: ldur            x0, [fp, #-0x18]
    // 0xae679c: StoreField: r2->field_b = r0
    //     0xae679c: stur            w0, [x2, #0xb]
    // 0xae67a0: ldur            x3, [fp, #-8]
    // 0xae67a4: LoadField: r0 = r3->field_f
    //     0xae67a4: ldur            w0, [x3, #0xf]
    // 0xae67a8: DecompressPointer r0
    //     0xae67a8: add             x0, x0, HEAP, lsl #32
    // 0xae67ac: LoadField: r1 = r0->field_b
    //     0xae67ac: ldur            w1, [x0, #0xb]
    // 0xae67b0: DecompressPointer r1
    //     0xae67b0: add             x1, x1, HEAP, lsl #32
    // 0xae67b4: cmp             w1, NULL
    // 0xae67b8: b.eq            #0xae6cf4
    // 0xae67bc: LoadField: r4 = r1->field_b
    //     0xae67bc: ldur            w4, [x1, #0xb]
    // 0xae67c0: DecompressPointer r4
    //     0xae67c0: add             x4, x4, HEAP, lsl #32
    // 0xae67c4: cmp             w4, NULL
    // 0xae67c8: b.ne            #0xae67d8
    // 0xae67cc: ldr             x5, [fp, #0x10]
    // 0xae67d0: r0 = Null
    //     0xae67d0: mov             x0, NULL
    // 0xae67d4: b               #0xae6840
    // 0xae67d8: ldr             x5, [fp, #0x10]
    // 0xae67dc: LoadField: r0 = r4->field_b
    //     0xae67dc: ldur            w0, [x4, #0xb]
    // 0xae67e0: r6 = LoadInt32Instr(r5)
    //     0xae67e0: sbfx            x6, x5, #1, #0x1f
    //     0xae67e4: tbz             w5, #0, #0xae67ec
    //     0xae67e8: ldur            x6, [x5, #7]
    // 0xae67ec: r1 = LoadInt32Instr(r0)
    //     0xae67ec: sbfx            x1, x0, #1, #0x1f
    // 0xae67f0: mov             x0, x1
    // 0xae67f4: mov             x1, x6
    // 0xae67f8: cmp             x1, x0
    // 0xae67fc: b.hs            #0xae6cf8
    // 0xae6800: LoadField: r0 = r4->field_f
    //     0xae6800: ldur            w0, [x4, #0xf]
    // 0xae6804: DecompressPointer r0
    //     0xae6804: add             x0, x0, HEAP, lsl #32
    // 0xae6808: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xae6808: add             x16, x0, x6, lsl #2
    //     0xae680c: ldur            w1, [x16, #0xf]
    // 0xae6810: DecompressPointer r1
    //     0xae6810: add             x1, x1, HEAP, lsl #32
    // 0xae6814: LoadField: r0 = r1->field_f
    //     0xae6814: ldur            w0, [x1, #0xf]
    // 0xae6818: DecompressPointer r0
    //     0xae6818: add             x0, x0, HEAP, lsl #32
    // 0xae681c: cmp             w0, NULL
    // 0xae6820: b.ne            #0xae682c
    // 0xae6824: r0 = Null
    //     0xae6824: mov             x0, NULL
    // 0xae6828: b               #0xae6840
    // 0xae682c: LoadField: r1 = r0->field_7
    //     0xae682c: ldur            w1, [x0, #7]
    // 0xae6830: cbnz            w1, #0xae683c
    // 0xae6834: r0 = false
    //     0xae6834: add             x0, NULL, #0x30  ; false
    // 0xae6838: b               #0xae6840
    // 0xae683c: r0 = true
    //     0xae683c: add             x0, NULL, #0x20  ; true
    // 0xae6840: cmp             w0, NULL
    // 0xae6844: b.ne            #0xae684c
    // 0xae6848: r0 = false
    //     0xae6848: add             x0, NULL, #0x30  ; false
    // 0xae684c: ldr             x1, [fp, #0x18]
    // 0xae6850: stur            x0, [fp, #-0x18]
    // 0xae6854: r0 = of()
    //     0xae6854: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae6858: r17 = 307
    //     0xae6858: movz            x17, #0x133
    // 0xae685c: ldr             w1, [x0, x17]
    // 0xae6860: DecompressPointer r1
    //     0xae6860: add             x1, x1, HEAP, lsl #32
    // 0xae6864: stur            x1, [fp, #-0x30]
    // 0xae6868: r16 = <EdgeInsets>
    //     0xae6868: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xae686c: ldr             x16, [x16, #0xda0]
    // 0xae6870: r30 = Instance_EdgeInsets
    //     0xae6870: add             lr, PP, #0x55, lsl #12  ; [pp+0x55d48] Obj!EdgeInsets@d58ee1
    //     0xae6874: ldr             lr, [lr, #0xd48]
    // 0xae6878: stp             lr, x16, [SP]
    // 0xae687c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae687c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae6880: r0 = all()
    //     0xae6880: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae6884: stur            x0, [fp, #-0x38]
    // 0xae6888: r16 = <RoundedRectangleBorder>
    //     0xae6888: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xae688c: ldr             x16, [x16, #0xf78]
    // 0xae6890: r30 = Instance_RoundedRectangleBorder
    //     0xae6890: add             lr, PP, #0x57, lsl #12  ; [pp+0x57d70] Obj!RoundedRectangleBorder@d5ac31
    //     0xae6894: ldr             lr, [lr, #0xd70]
    // 0xae6898: stp             lr, x16, [SP]
    // 0xae689c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae689c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae68a0: r0 = all()
    //     0xae68a0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae68a4: stur            x0, [fp, #-0x40]
    // 0xae68a8: r0 = ButtonStyle()
    //     0xae68a8: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xae68ac: mov             x2, x0
    // 0xae68b0: ldur            x0, [fp, #-0x38]
    // 0xae68b4: stur            x2, [fp, #-0x48]
    // 0xae68b8: StoreField: r2->field_23 = r0
    //     0xae68b8: stur            w0, [x2, #0x23]
    // 0xae68bc: ldur            x0, [fp, #-0x40]
    // 0xae68c0: StoreField: r2->field_43 = r0
    //     0xae68c0: stur            w0, [x2, #0x43]
    // 0xae68c4: ldur            x3, [fp, #-8]
    // 0xae68c8: LoadField: r0 = r3->field_f
    //     0xae68c8: ldur            w0, [x3, #0xf]
    // 0xae68cc: DecompressPointer r0
    //     0xae68cc: add             x0, x0, HEAP, lsl #32
    // 0xae68d0: LoadField: r1 = r0->field_b
    //     0xae68d0: ldur            w1, [x0, #0xb]
    // 0xae68d4: DecompressPointer r1
    //     0xae68d4: add             x1, x1, HEAP, lsl #32
    // 0xae68d8: cmp             w1, NULL
    // 0xae68dc: b.eq            #0xae6cfc
    // 0xae68e0: LoadField: r4 = r1->field_b
    //     0xae68e0: ldur            w4, [x1, #0xb]
    // 0xae68e4: DecompressPointer r4
    //     0xae68e4: add             x4, x4, HEAP, lsl #32
    // 0xae68e8: cmp             w4, NULL
    // 0xae68ec: b.ne            #0xae68fc
    // 0xae68f0: ldr             x5, [fp, #0x10]
    // 0xae68f4: r0 = Null
    //     0xae68f4: mov             x0, NULL
    // 0xae68f8: b               #0xae6940
    // 0xae68fc: ldr             x5, [fp, #0x10]
    // 0xae6900: LoadField: r0 = r4->field_b
    //     0xae6900: ldur            w0, [x4, #0xb]
    // 0xae6904: r6 = LoadInt32Instr(r5)
    //     0xae6904: sbfx            x6, x5, #1, #0x1f
    //     0xae6908: tbz             w5, #0, #0xae6910
    //     0xae690c: ldur            x6, [x5, #7]
    // 0xae6910: r1 = LoadInt32Instr(r0)
    //     0xae6910: sbfx            x1, x0, #1, #0x1f
    // 0xae6914: mov             x0, x1
    // 0xae6918: mov             x1, x6
    // 0xae691c: cmp             x1, x0
    // 0xae6920: b.hs            #0xae6d00
    // 0xae6924: LoadField: r0 = r4->field_f
    //     0xae6924: ldur            w0, [x4, #0xf]
    // 0xae6928: DecompressPointer r0
    //     0xae6928: add             x0, x0, HEAP, lsl #32
    // 0xae692c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xae692c: add             x16, x0, x6, lsl #2
    //     0xae6930: ldur            w1, [x16, #0xf]
    // 0xae6934: DecompressPointer r1
    //     0xae6934: add             x1, x1, HEAP, lsl #32
    // 0xae6938: LoadField: r0 = r1->field_f
    //     0xae6938: ldur            w0, [x1, #0xf]
    // 0xae693c: DecompressPointer r0
    //     0xae693c: add             x0, x0, HEAP, lsl #32
    // 0xae6940: cmp             w0, NULL
    // 0xae6944: b.ne            #0xae6950
    // 0xae6948: r6 = ""
    //     0xae6948: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae694c: b               #0xae6954
    // 0xae6950: mov             x6, x0
    // 0xae6954: ldur            x4, [fp, #-0x18]
    // 0xae6958: ldur            x0, [fp, #-0x30]
    // 0xae695c: ldr             x1, [fp, #0x18]
    // 0xae6960: stur            x6, [fp, #-0x38]
    // 0xae6964: r0 = of()
    //     0xae6964: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae6968: LoadField: r1 = r0->field_87
    //     0xae6968: ldur            w1, [x0, #0x87]
    // 0xae696c: DecompressPointer r1
    //     0xae696c: add             x1, x1, HEAP, lsl #32
    // 0xae6970: LoadField: r0 = r1->field_7
    //     0xae6970: ldur            w0, [x1, #7]
    // 0xae6974: DecompressPointer r0
    //     0xae6974: add             x0, x0, HEAP, lsl #32
    // 0xae6978: r16 = Instance_Color
    //     0xae6978: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xae697c: r30 = 16.000000
    //     0xae697c: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xae6980: ldr             lr, [lr, #0x188]
    // 0xae6984: stp             lr, x16, [SP]
    // 0xae6988: mov             x1, x0
    // 0xae698c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xae698c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xae6990: ldr             x4, [x4, #0x9b8]
    // 0xae6994: r0 = copyWith()
    //     0xae6994: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae6998: stur            x0, [fp, #-0x40]
    // 0xae699c: r0 = Text()
    //     0xae699c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae69a0: mov             x3, x0
    // 0xae69a4: ldur            x0, [fp, #-0x38]
    // 0xae69a8: stur            x3, [fp, #-0x50]
    // 0xae69ac: StoreField: r3->field_b = r0
    //     0xae69ac: stur            w0, [x3, #0xb]
    // 0xae69b0: ldur            x0, [fp, #-0x40]
    // 0xae69b4: StoreField: r3->field_13 = r0
    //     0xae69b4: stur            w0, [x3, #0x13]
    // 0xae69b8: r1 = Function '<anonymous closure>':.
    //     0xae69b8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58288] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xae69bc: ldr             x1, [x1, #0x288]
    // 0xae69c0: r2 = Null
    //     0xae69c0: mov             x2, NULL
    // 0xae69c4: r0 = AllocateClosure()
    //     0xae69c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae69c8: stur            x0, [fp, #-0x38]
    // 0xae69cc: r0 = TextButton()
    //     0xae69cc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xae69d0: mov             x1, x0
    // 0xae69d4: ldur            x0, [fp, #-0x38]
    // 0xae69d8: stur            x1, [fp, #-0x40]
    // 0xae69dc: StoreField: r1->field_b = r0
    //     0xae69dc: stur            w0, [x1, #0xb]
    // 0xae69e0: ldur            x0, [fp, #-0x48]
    // 0xae69e4: StoreField: r1->field_1b = r0
    //     0xae69e4: stur            w0, [x1, #0x1b]
    // 0xae69e8: r0 = false
    //     0xae69e8: add             x0, NULL, #0x30  ; false
    // 0xae69ec: StoreField: r1->field_27 = r0
    //     0xae69ec: stur            w0, [x1, #0x27]
    // 0xae69f0: r2 = true
    //     0xae69f0: add             x2, NULL, #0x20  ; true
    // 0xae69f4: StoreField: r1->field_2f = r2
    //     0xae69f4: stur            w2, [x1, #0x2f]
    // 0xae69f8: ldur            x3, [fp, #-0x50]
    // 0xae69fc: StoreField: r1->field_37 = r3
    //     0xae69fc: stur            w3, [x1, #0x37]
    // 0xae6a00: r0 = TextButtonTheme()
    //     0xae6a00: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xae6a04: mov             x1, x0
    // 0xae6a08: ldur            x0, [fp, #-0x30]
    // 0xae6a0c: stur            x1, [fp, #-0x38]
    // 0xae6a10: StoreField: r1->field_f = r0
    //     0xae6a10: stur            w0, [x1, #0xf]
    // 0xae6a14: ldur            x0, [fp, #-0x40]
    // 0xae6a18: StoreField: r1->field_b = r0
    //     0xae6a18: stur            w0, [x1, #0xb]
    // 0xae6a1c: r0 = Visibility()
    //     0xae6a1c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xae6a20: mov             x1, x0
    // 0xae6a24: ldur            x0, [fp, #-0x38]
    // 0xae6a28: stur            x1, [fp, #-0x30]
    // 0xae6a2c: StoreField: r1->field_b = r0
    //     0xae6a2c: stur            w0, [x1, #0xb]
    // 0xae6a30: r0 = Instance_SizedBox
    //     0xae6a30: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xae6a34: StoreField: r1->field_f = r0
    //     0xae6a34: stur            w0, [x1, #0xf]
    // 0xae6a38: ldur            x0, [fp, #-0x18]
    // 0xae6a3c: StoreField: r1->field_13 = r0
    //     0xae6a3c: stur            w0, [x1, #0x13]
    // 0xae6a40: r0 = false
    //     0xae6a40: add             x0, NULL, #0x30  ; false
    // 0xae6a44: ArrayStore: r1[0] = r0  ; List_4
    //     0xae6a44: stur            w0, [x1, #0x17]
    // 0xae6a48: StoreField: r1->field_1b = r0
    //     0xae6a48: stur            w0, [x1, #0x1b]
    // 0xae6a4c: StoreField: r1->field_1f = r0
    //     0xae6a4c: stur            w0, [x1, #0x1f]
    // 0xae6a50: StoreField: r1->field_23 = r0
    //     0xae6a50: stur            w0, [x1, #0x23]
    // 0xae6a54: StoreField: r1->field_27 = r0
    //     0xae6a54: stur            w0, [x1, #0x27]
    // 0xae6a58: StoreField: r1->field_2b = r0
    //     0xae6a58: stur            w0, [x1, #0x2b]
    // 0xae6a5c: r0 = Padding()
    //     0xae6a5c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae6a60: mov             x3, x0
    // 0xae6a64: r0 = Instance_EdgeInsets
    //     0xae6a64: add             x0, PP, #0x58, lsl #12  ; [pp+0x58280] Obj!EdgeInsets@d58eb1
    //     0xae6a68: ldr             x0, [x0, #0x280]
    // 0xae6a6c: stur            x3, [fp, #-0x18]
    // 0xae6a70: StoreField: r3->field_f = r0
    //     0xae6a70: stur            w0, [x3, #0xf]
    // 0xae6a74: ldur            x0, [fp, #-0x30]
    // 0xae6a78: StoreField: r3->field_b = r0
    //     0xae6a78: stur            w0, [x3, #0xb]
    // 0xae6a7c: ldur            x0, [fp, #-8]
    // 0xae6a80: LoadField: r1 = r0->field_f
    //     0xae6a80: ldur            w1, [x0, #0xf]
    // 0xae6a84: DecompressPointer r1
    //     0xae6a84: add             x1, x1, HEAP, lsl #32
    // 0xae6a88: LoadField: r0 = r1->field_b
    //     0xae6a88: ldur            w0, [x1, #0xb]
    // 0xae6a8c: DecompressPointer r0
    //     0xae6a8c: add             x0, x0, HEAP, lsl #32
    // 0xae6a90: cmp             w0, NULL
    // 0xae6a94: b.eq            #0xae6d04
    // 0xae6a98: LoadField: r2 = r0->field_b
    //     0xae6a98: ldur            w2, [x0, #0xb]
    // 0xae6a9c: DecompressPointer r2
    //     0xae6a9c: add             x2, x2, HEAP, lsl #32
    // 0xae6aa0: cmp             w2, NULL
    // 0xae6aa4: b.ne            #0xae6ab0
    // 0xae6aa8: r0 = Null
    //     0xae6aa8: mov             x0, NULL
    // 0xae6aac: b               #0xae6af0
    // 0xae6ab0: ldr             x0, [fp, #0x10]
    // 0xae6ab4: LoadField: r1 = r2->field_b
    //     0xae6ab4: ldur            w1, [x2, #0xb]
    // 0xae6ab8: r4 = LoadInt32Instr(r0)
    //     0xae6ab8: sbfx            x4, x0, #1, #0x1f
    //     0xae6abc: tbz             w0, #0, #0xae6ac4
    //     0xae6ac0: ldur            x4, [x0, #7]
    // 0xae6ac4: r0 = LoadInt32Instr(r1)
    //     0xae6ac4: sbfx            x0, x1, #1, #0x1f
    // 0xae6ac8: mov             x1, x4
    // 0xae6acc: cmp             x1, x0
    // 0xae6ad0: b.hs            #0xae6d08
    // 0xae6ad4: LoadField: r0 = r2->field_f
    //     0xae6ad4: ldur            w0, [x2, #0xf]
    // 0xae6ad8: DecompressPointer r0
    //     0xae6ad8: add             x0, x0, HEAP, lsl #32
    // 0xae6adc: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xae6adc: add             x16, x0, x4, lsl #2
    //     0xae6ae0: ldur            w1, [x16, #0xf]
    // 0xae6ae4: DecompressPointer r1
    //     0xae6ae4: add             x1, x1, HEAP, lsl #32
    // 0xae6ae8: LoadField: r0 = r1->field_13
    //     0xae6ae8: ldur            w0, [x1, #0x13]
    // 0xae6aec: DecompressPointer r0
    //     0xae6aec: add             x0, x0, HEAP, lsl #32
    // 0xae6af0: cmp             w0, NULL
    // 0xae6af4: b.ne            #0xae6b00
    // 0xae6af8: r5 = ""
    //     0xae6af8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae6afc: b               #0xae6b04
    // 0xae6b00: mov             x5, x0
    // 0xae6b04: ldur            x4, [fp, #-0x20]
    // 0xae6b08: ldur            x0, [fp, #-0x28]
    // 0xae6b0c: stur            x5, [fp, #-8]
    // 0xae6b10: r1 = Function '<anonymous closure>':.
    //     0xae6b10: add             x1, PP, #0x58, lsl #12  ; [pp+0x58290] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae6b14: ldr             x1, [x1, #0x290]
    // 0xae6b18: r2 = Null
    //     0xae6b18: mov             x2, NULL
    // 0xae6b1c: r0 = AllocateClosure()
    //     0xae6b1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae6b20: r1 = Function '<anonymous closure>':.
    //     0xae6b20: add             x1, PP, #0x58, lsl #12  ; [pp+0x58298] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae6b24: ldr             x1, [x1, #0x298]
    // 0xae6b28: r2 = Null
    //     0xae6b28: mov             x2, NULL
    // 0xae6b2c: stur            x0, [fp, #-0x30]
    // 0xae6b30: r0 = AllocateClosure()
    //     0xae6b30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae6b34: stur            x0, [fp, #-0x38]
    // 0xae6b38: r0 = CachedNetworkImage()
    //     0xae6b38: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xae6b3c: stur            x0, [fp, #-0x40]
    // 0xae6b40: r16 = 160.000000
    //     0xae6b40: add             x16, PP, #0x58, lsl #12  ; [pp+0x58268] 160
    //     0xae6b44: ldr             x16, [x16, #0x268]
    // 0xae6b48: r30 = inf
    //     0xae6b48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xae6b4c: ldr             lr, [lr, #0x9f8]
    // 0xae6b50: stp             lr, x16, [SP, #0x18]
    // 0xae6b54: ldur            x16, [fp, #-0x30]
    // 0xae6b58: ldur            lr, [fp, #-0x38]
    // 0xae6b5c: stp             lr, x16, [SP, #8]
    // 0xae6b60: r16 = Instance_BoxFit
    //     0xae6b60: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xae6b64: ldr             x16, [x16, #0x118]
    // 0xae6b68: str             x16, [SP]
    // 0xae6b6c: mov             x1, x0
    // 0xae6b70: ldur            x2, [fp, #-8]
    // 0xae6b74: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x5, fit, 0x6, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xae6b74: add             x4, PP, #0x54, lsl #12  ; [pp+0x542b0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x5, "fit", 0x6, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xae6b78: ldr             x4, [x4, #0x2b0]
    // 0xae6b7c: r0 = CachedNetworkImage()
    //     0xae6b7c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xae6b80: r1 = Null
    //     0xae6b80: mov             x1, NULL
    // 0xae6b84: r2 = 8
    //     0xae6b84: movz            x2, #0x8
    // 0xae6b88: r0 = AllocateArray()
    //     0xae6b88: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae6b8c: mov             x2, x0
    // 0xae6b90: ldur            x0, [fp, #-0x20]
    // 0xae6b94: stur            x2, [fp, #-8]
    // 0xae6b98: StoreField: r2->field_f = r0
    //     0xae6b98: stur            w0, [x2, #0xf]
    // 0xae6b9c: ldur            x0, [fp, #-0x28]
    // 0xae6ba0: StoreField: r2->field_13 = r0
    //     0xae6ba0: stur            w0, [x2, #0x13]
    // 0xae6ba4: ldur            x0, [fp, #-0x18]
    // 0xae6ba8: ArrayStore: r2[0] = r0  ; List_4
    //     0xae6ba8: stur            w0, [x2, #0x17]
    // 0xae6bac: ldur            x0, [fp, #-0x40]
    // 0xae6bb0: StoreField: r2->field_1b = r0
    //     0xae6bb0: stur            w0, [x2, #0x1b]
    // 0xae6bb4: r1 = <Widget>
    //     0xae6bb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae6bb8: r0 = AllocateGrowableArray()
    //     0xae6bb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae6bbc: mov             x1, x0
    // 0xae6bc0: ldur            x0, [fp, #-8]
    // 0xae6bc4: stur            x1, [fp, #-0x18]
    // 0xae6bc8: StoreField: r1->field_f = r0
    //     0xae6bc8: stur            w0, [x1, #0xf]
    // 0xae6bcc: r0 = 8
    //     0xae6bcc: movz            x0, #0x8
    // 0xae6bd0: StoreField: r1->field_b = r0
    //     0xae6bd0: stur            w0, [x1, #0xb]
    // 0xae6bd4: r0 = Column()
    //     0xae6bd4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae6bd8: mov             x1, x0
    // 0xae6bdc: r0 = Instance_Axis
    //     0xae6bdc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae6be0: StoreField: r1->field_f = r0
    //     0xae6be0: stur            w0, [x1, #0xf]
    // 0xae6be4: r0 = Instance_MainAxisAlignment
    //     0xae6be4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xae6be8: ldr             x0, [x0, #0xa08]
    // 0xae6bec: StoreField: r1->field_13 = r0
    //     0xae6bec: stur            w0, [x1, #0x13]
    // 0xae6bf0: r0 = Instance_MainAxisSize
    //     0xae6bf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae6bf4: ldr             x0, [x0, #0xa10]
    // 0xae6bf8: ArrayStore: r1[0] = r0  ; List_4
    //     0xae6bf8: stur            w0, [x1, #0x17]
    // 0xae6bfc: r0 = Instance_CrossAxisAlignment
    //     0xae6bfc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xae6c00: ldr             x0, [x0, #0x890]
    // 0xae6c04: StoreField: r1->field_1b = r0
    //     0xae6c04: stur            w0, [x1, #0x1b]
    // 0xae6c08: r0 = Instance_VerticalDirection
    //     0xae6c08: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae6c0c: ldr             x0, [x0, #0xa20]
    // 0xae6c10: StoreField: r1->field_23 = r0
    //     0xae6c10: stur            w0, [x1, #0x23]
    // 0xae6c14: r0 = Instance_Clip
    //     0xae6c14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae6c18: ldr             x0, [x0, #0x38]
    // 0xae6c1c: StoreField: r1->field_2b = r0
    //     0xae6c1c: stur            w0, [x1, #0x2b]
    // 0xae6c20: StoreField: r1->field_2f = rZR
    //     0xae6c20: stur            xzr, [x1, #0x2f]
    // 0xae6c24: ldur            x0, [fp, #-0x18]
    // 0xae6c28: StoreField: r1->field_b = r0
    //     0xae6c28: stur            w0, [x1, #0xb]
    // 0xae6c2c: ldur            x0, [fp, #-0x10]
    // 0xae6c30: stur            x1, [fp, #-8]
    // 0xae6c34: r0 = Card()
    //     0xae6c34: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xae6c38: mov             x1, x0
    // 0xae6c3c: r0 = 0.000000
    //     0xae6c3c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xae6c40: stur            x1, [fp, #-0x18]
    // 0xae6c44: ArrayStore: r1[0] = r0  ; List_4
    //     0xae6c44: stur            w0, [x1, #0x17]
    // 0xae6c48: ldur            x0, [fp, #-0x10]
    // 0xae6c4c: StoreField: r1->field_1b = r0
    //     0xae6c4c: stur            w0, [x1, #0x1b]
    // 0xae6c50: r0 = true
    //     0xae6c50: add             x0, NULL, #0x20  ; true
    // 0xae6c54: StoreField: r1->field_1f = r0
    //     0xae6c54: stur            w0, [x1, #0x1f]
    // 0xae6c58: r2 = Instance_EdgeInsets
    //     0xae6c58: add             x2, PP, #0x40, lsl #12  ; [pp+0x40878] Obj!EdgeInsets@d57f51
    //     0xae6c5c: ldr             x2, [x2, #0x878]
    // 0xae6c60: StoreField: r1->field_27 = r2
    //     0xae6c60: stur            w2, [x1, #0x27]
    // 0xae6c64: r2 = Instance_Clip
    //     0xae6c64: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xae6c68: ldr             x2, [x2, #0xb50]
    // 0xae6c6c: StoreField: r1->field_23 = r2
    //     0xae6c6c: stur            w2, [x1, #0x23]
    // 0xae6c70: ldur            x2, [fp, #-8]
    // 0xae6c74: StoreField: r1->field_2f = r2
    //     0xae6c74: stur            w2, [x1, #0x2f]
    // 0xae6c78: StoreField: r1->field_2b = r0
    //     0xae6c78: stur            w0, [x1, #0x2b]
    // 0xae6c7c: r0 = Instance__CardVariant
    //     0xae6c7c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xae6c80: ldr             x0, [x0, #0xa68]
    // 0xae6c84: StoreField: r1->field_33 = r0
    //     0xae6c84: stur            w0, [x1, #0x33]
    // 0xae6c88: r0 = Padding()
    //     0xae6c88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae6c8c: r1 = Instance_EdgeInsets
    //     0xae6c8c: add             x1, PP, #0x58, lsl #12  ; [pp+0x582a0] Obj!EdgeInsets@d58e81
    //     0xae6c90: ldr             x1, [x1, #0x2a0]
    // 0xae6c94: StoreField: r0->field_f = r1
    //     0xae6c94: stur            w1, [x0, #0xf]
    // 0xae6c98: ldur            x1, [fp, #-0x18]
    // 0xae6c9c: StoreField: r0->field_b = r1
    //     0xae6c9c: stur            w1, [x0, #0xb]
    // 0xae6ca0: LeaveFrame
    //     0xae6ca0: mov             SP, fp
    //     0xae6ca4: ldp             fp, lr, [SP], #0x10
    // 0xae6ca8: ret
    //     0xae6ca8: ret             
    // 0xae6cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6cac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6cb0: b               #0xae5d28
    // 0xae6cb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6cb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6cb8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6cbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6cc0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6cc0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6cc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6cc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6cc8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6ccc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6ccc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6cd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6cd0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6cd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6cd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6cd8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6cdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cdc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6ce0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6ce0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6ce4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6ce4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6ce8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6ce8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6cec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6cf0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6cf0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6cf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cf4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6cf8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6cf8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6cfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6d00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6d00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6d04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6d04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6d08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6d08: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4162, size: 0x10, field offset: 0xc
//   const constructor, 
class ContentTextAndMedia extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7da58, size: 0x24
    // 0xc7da58: EnterFrame
    //     0xc7da58: stp             fp, lr, [SP, #-0x10]!
    //     0xc7da5c: mov             fp, SP
    // 0xc7da60: mov             x0, x1
    // 0xc7da64: r1 = <ContentTextAndMedia>
    //     0xc7da64: add             x1, PP, #0x48, lsl #12  ; [pp+0x48bd8] TypeArguments: <ContentTextAndMedia>
    //     0xc7da68: ldr             x1, [x1, #0xbd8]
    // 0xc7da6c: r0 = _ContentTextAndMediaState()
    //     0xc7da6c: bl              #0xc7da7c  ; Allocate_ContentTextAndMediaStateStub -> _ContentTextAndMediaState (size=0x14)
    // 0xc7da70: LeaveFrame
    //     0xc7da70: mov             SP, fp
    //     0xc7da74: ldp             fp, lr, [SP], #0x10
    // 0xc7da78: ret
    //     0xc7da78: ret             
  }
}
