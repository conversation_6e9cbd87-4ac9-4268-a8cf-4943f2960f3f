// lib: , url: package:dio/src/response/response_stream_handler.dart

// class id: 1049614, size: 0x8
class :: {

  static _ handleResponseStream(/* No info */) {
    // ** addr: 0x8684f0, size: 0x284
    // 0x8684f0: EnterFrame
    //     0x8684f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8684f4: mov             fp, SP
    // 0x8684f8: AllocStack(0x40)
    //     0x8684f8: sub             SP, SP, #0x40
    // 0x8684fc: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x8684fc: stur            x1, [fp, #-8]
    //     0x868500: stur            x2, [fp, #-0x10]
    // 0x868504: CheckStackOverflow
    //     0x868504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868508: cmp             SP, x16
    //     0x86850c: b.ls            #0x86876c
    // 0x868510: r1 = 9
    //     0x868510: movz            x1, #0x9
    // 0x868514: r0 = AllocateContext()
    //     0x868514: bl              #0x16f6108  ; AllocateContextStub
    // 0x868518: mov             x2, x0
    // 0x86851c: ldur            x0, [fp, #-8]
    // 0x868520: stur            x2, [fp, #-0x18]
    // 0x868524: StoreField: r2->field_f = r0
    //     0x868524: stur            w0, [x2, #0xf]
    // 0x868528: ldur            x0, [fp, #-0x10]
    // 0x86852c: StoreField: r2->field_13 = r0
    //     0x86852c: stur            w0, [x2, #0x13]
    // 0x868530: LoadField: r3 = r0->field_b
    //     0x868530: ldur            w3, [x0, #0xb]
    // 0x868534: DecompressPointer r3
    //     0x868534: add             x3, x3, HEAP, lsl #32
    // 0x868538: stur            x3, [fp, #-8]
    // 0x86853c: r1 = <Uint8List>
    //     0x86853c: add             x1, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x868540: ldr             x1, [x1, #0x598]
    // 0x868544: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x868544: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x868548: r0 = StreamController()
    //     0x868548: bl              #0x657960  ; [dart:async] StreamController::StreamController
    // 0x86854c: mov             x1, x0
    // 0x868550: ldur            x2, [fp, #-0x18]
    // 0x868554: stur            x1, [fp, #-0x10]
    // 0x868558: ArrayStore: r2[0] = r0  ; List_4
    //     0x868558: stur            w0, [x2, #0x17]
    //     0x86855c: ldurb           w16, [x2, #-1]
    //     0x868560: ldurb           w17, [x0, #-1]
    //     0x868564: and             x16, x17, x16, lsr #2
    //     0x868568: tst             x16, HEAP, lsr #32
    //     0x86856c: b.eq            #0x868574
    //     0x868570: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x868574: r0 = Sentinel
    //     0x868574: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x868578: StoreField: r2->field_1b = r0
    //     0x868578: stur            w0, [x2, #0x1b]
    // 0x86857c: LoadField: r0 = r2->field_f
    //     0x86857c: ldur            w0, [x2, #0xf]
    // 0x868580: DecompressPointer r0
    //     0x868580: add             x0, x0, HEAP, lsl #32
    // 0x868584: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x868584: ldur            w3, [x0, #0x17]
    // 0x868588: DecompressPointer r3
    //     0x868588: add             x3, x3, HEAP, lsl #32
    // 0x86858c: cmp             w3, NULL
    // 0x868590: b.ne            #0x86859c
    // 0x868594: r0 = Instance_Duration
    //     0x868594: ldr             x0, [PP, #0x17a0]  ; [pp+0x17a0] Obj!Duration@d776c1
    // 0x868598: b               #0x8685a0
    // 0x86859c: mov             x0, x3
    // 0x8685a0: ldur            x3, [fp, #-8]
    // 0x8685a4: StoreField: r2->field_1f = r0
    //     0x8685a4: stur            w0, [x2, #0x1f]
    //     0x8685a8: ldurb           w16, [x2, #-1]
    //     0x8685ac: ldurb           w17, [x0, #-1]
    //     0x8685b0: and             x16, x17, x16, lsr #2
    //     0x8685b4: tst             x16, HEAP, lsr #32
    //     0x8685b8: b.eq            #0x8685c0
    //     0x8685bc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x8685c0: r0 = Stopwatch()
    //     0x8685c0: bl              #0x63785c  ; AllocateStopwatchStub -> Stopwatch (size=0x14)
    // 0x8685c4: stur            x0, [fp, #-0x20]
    // 0x8685c8: StoreField: r0->field_7 = rZR
    //     0x8685c8: stur            xzr, [x0, #7]
    // 0x8685cc: StoreField: r0->field_f = rZR
    //     0x8685cc: stur            wzr, [x0, #0xf]
    // 0x8685d0: r0 = InitLateStaticField(0x370) // [dart:core] Stopwatch::_frequency
    //     0x8685d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8685d4: ldr             x0, [x0, #0x6e0]
    //     0x8685d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8685dc: cmp             w0, w16
    //     0x8685e0: b.ne            #0x8685ec
    //     0x8685e4: ldr             x2, [PP, #0xaf8]  ; [pp+0xaf8] Field <Stopwatch._frequency@0150898>: static late final (offset: 0x370)
    //     0x8685e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8685ec: ldur            x0, [fp, #-0x20]
    // 0x8685f0: ldur            x3, [fp, #-0x18]
    // 0x8685f4: StoreField: r3->field_23 = r0
    //     0x8685f4: stur            w0, [x3, #0x23]
    //     0x8685f8: ldurb           w16, [x3, #-1]
    //     0x8685fc: ldurb           w17, [x0, #-1]
    //     0x868600: and             x16, x17, x16, lsr #2
    //     0x868604: tst             x16, HEAP, lsr #32
    //     0x868608: b.eq            #0x868610
    //     0x86860c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x868610: StoreField: r3->field_27 = rNULL
    //     0x868610: stur            NULL, [x3, #0x27]
    // 0x868614: mov             x2, x3
    // 0x868618: r1 = Function 'stopWatchReceiveTimeout': static.
    //     0x868618: add             x1, PP, #8, lsl #12  ; [pp+0x8a60] AnonymousClosure: static (0x868e74), in [package:dio/src/response/response_stream_handler.dart] ::handleResponseStream (0x8684f0)
    //     0x86861c: ldr             x1, [x1, #0xa60]
    // 0x868620: r0 = AllocateClosure()
    //     0x868620: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x868624: ldur            x3, [fp, #-0x18]
    // 0x868628: StoreField: r3->field_2b = r0
    //     0x868628: stur            w0, [x3, #0x2b]
    //     0x86862c: ldurb           w16, [x3, #-1]
    //     0x868630: ldurb           w17, [x0, #-1]
    //     0x868634: and             x16, x17, x16, lsr #2
    //     0x868638: tst             x16, HEAP, lsr #32
    //     0x86863c: b.eq            #0x868644
    //     0x868640: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x868644: mov             x2, x3
    // 0x868648: r1 = Function 'watchReceiveTimeout': static.
    //     0x868648: add             x1, PP, #8, lsl #12  ; [pp+0x8a68] AnonymousClosure: static (0x868bcc), in [package:dio/src/response/response_stream_handler.dart] ::handleResponseStream (0x8684f0)
    //     0x86864c: ldr             x1, [x1, #0xa68]
    // 0x868650: r0 = AllocateClosure()
    //     0x868650: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x868654: ldur            x3, [fp, #-0x18]
    // 0x868658: StoreField: r3->field_2f = r0
    //     0x868658: stur            w0, [x3, #0x2f]
    //     0x86865c: ldurb           w16, [x3, #-1]
    //     0x868660: ldurb           w17, [x0, #-1]
    //     0x868664: and             x16, x17, x16, lsr #2
    //     0x868668: tst             x16, HEAP, lsr #32
    //     0x86866c: b.eq            #0x868674
    //     0x868670: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x868674: mov             x2, x3
    // 0x868678: r1 = Function '<anonymous closure>': static.
    //     0x868678: add             x1, PP, #8, lsl #12  ; [pp+0x8a70] AnonymousClosure: static (0x868b38), in [package:dio/src/response/response_stream_handler.dart] ::handleResponseStream (0x8684f0)
    //     0x86867c: ldr             x1, [x1, #0xa70]
    // 0x868680: r0 = AllocateClosure()
    //     0x868680: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x868684: ldur            x2, [fp, #-0x18]
    // 0x868688: r1 = Function '<anonymous closure>': static.
    //     0x868688: add             x1, PP, #8, lsl #12  ; [pp+0x8a78] AnonymousClosure: static (0x8689f8), in [package:dio/src/response/response_stream_handler.dart] ::handleResponseStream (0x8684f0)
    //     0x86868c: ldr             x1, [x1, #0xa78]
    // 0x868690: stur            x0, [fp, #-0x20]
    // 0x868694: r0 = AllocateClosure()
    //     0x868694: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x868698: ldur            x2, [fp, #-0x18]
    // 0x86869c: r1 = Function '<anonymous closure>': static.
    //     0x86869c: add             x1, PP, #8, lsl #12  ; [pp+0x8a80] AnonymousClosure: static (0x86890c), in [package:dio/src/response/response_stream_handler.dart] ::handleResponseStream (0x8684f0)
    //     0x8686a0: ldr             x1, [x1, #0xa80]
    // 0x8686a4: stur            x0, [fp, #-0x28]
    // 0x8686a8: r0 = AllocateClosure()
    //     0x8686a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8686ac: ldur            x1, [fp, #-8]
    // 0x8686b0: r2 = LoadClassIdInstr(r1)
    //     0x8686b0: ldur            x2, [x1, #-1]
    //     0x8686b4: ubfx            x2, x2, #0xc, #0x14
    // 0x8686b8: ldur            x16, [fp, #-0x28]
    // 0x8686bc: stp             x0, x16, [SP, #8]
    // 0x8686c0: r16 = true
    //     0x8686c0: add             x16, NULL, #0x20  ; true
    // 0x8686c4: str             x16, [SP]
    // 0x8686c8: mov             x0, x2
    // 0x8686cc: ldur            x2, [fp, #-0x20]
    // 0x8686d0: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0x8686d0: add             x4, PP, #8, lsl #12  ; [pp+0x86e0] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    //     0x8686d4: ldr             x4, [x4, #0x6e0]
    // 0x8686d8: r0 = GDT[cid_x0 + 0x37a]()
    //     0x8686d8: add             lr, x0, #0x37a
    //     0x8686dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8686e0: blr             lr
    // 0x8686e4: ldur            x2, [fp, #-0x18]
    // 0x8686e8: StoreField: r2->field_1b = r0
    //     0x8686e8: stur            w0, [x2, #0x1b]
    //     0x8686ec: ldurb           w16, [x2, #-1]
    //     0x8686f0: ldurb           w17, [x0, #-1]
    //     0x8686f4: and             x16, x17, x16, lsr #2
    //     0x8686f8: tst             x16, HEAP, lsr #32
    //     0x8686fc: b.eq            #0x868704
    //     0x868700: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x868704: LoadField: r0 = r2->field_f
    //     0x868704: ldur            w0, [x2, #0xf]
    // 0x868708: DecompressPointer r0
    //     0x868708: add             x0, x0, HEAP, lsl #32
    // 0x86870c: LoadField: r1 = r0->field_5f
    //     0x86870c: ldur            w1, [x0, #0x5f]
    // 0x868710: DecompressPointer r1
    //     0x868710: add             x1, x1, HEAP, lsl #32
    // 0x868714: cmp             w1, NULL
    // 0x868718: b.eq            #0x868748
    // 0x86871c: LoadField: r0 = r1->field_7
    //     0x86871c: ldur            w0, [x1, #7]
    // 0x868720: DecompressPointer r0
    //     0x868720: add             x0, x0, HEAP, lsl #32
    // 0x868724: LoadField: r3 = r0->field_b
    //     0x868724: ldur            w3, [x0, #0xb]
    // 0x868728: DecompressPointer r3
    //     0x868728: add             x3, x3, HEAP, lsl #32
    // 0x86872c: stur            x3, [fp, #-8]
    // 0x868730: r1 = Function '<anonymous closure>': static.
    //     0x868730: add             x1, PP, #8, lsl #12  ; [pp+0x8a88] AnonymousClosure: static (0x868774), in [package:dio/src/response/response_stream_handler.dart] ::handleResponseStream (0x8684f0)
    //     0x868734: ldr             x1, [x1, #0xa88]
    // 0x868738: r0 = AllocateClosure()
    //     0x868738: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x86873c: ldur            x1, [fp, #-8]
    // 0x868740: mov             x2, x0
    // 0x868744: r0 = whenComplete()
    //     0x868744: bl              #0x167b0bc  ; [dart:async] _Future::whenComplete
    // 0x868748: ldur            x0, [fp, #-0x10]
    // 0x86874c: LoadField: r1 = r0->field_7
    //     0x86874c: ldur            w1, [x0, #7]
    // 0x868750: DecompressPointer r1
    //     0x868750: add             x1, x1, HEAP, lsl #32
    // 0x868754: r0 = _ControllerStream()
    //     0x868754: bl              #0x698904  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0x868758: ldur            x1, [fp, #-0x10]
    // 0x86875c: StoreField: r0->field_b = r1
    //     0x86875c: stur            w1, [x0, #0xb]
    // 0x868760: LeaveFrame
    //     0x868760: mov             SP, fp
    //     0x868764: ldp             fp, lr, [SP], #0x10
    // 0x868768: ret
    //     0x868768: ret             
    // 0x86876c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86876c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868770: b               #0x868510
  }
  [closure] static Null <anonymous closure>(dynamic) {
    // ** addr: 0x868774, size: 0x120
    // 0x868774: EnterFrame
    //     0x868774: stp             fp, lr, [SP, #-0x10]!
    //     0x868778: mov             fp, SP
    // 0x86877c: AllocStack(0x20)
    //     0x86877c: sub             SP, SP, #0x20
    // 0x868780: SetupParameters()
    //     0x868780: ldr             x0, [fp, #0x10]
    //     0x868784: ldur            w2, [x0, #0x17]
    //     0x868788: add             x2, x2, HEAP, lsl #32
    //     0x86878c: stur            x2, [fp, #-0x10]
    // 0x868790: CheckStackOverflow
    //     0x868790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868794: cmp             SP, x16
    //     0x868798: b.ls            #0x868884
    // 0x86879c: LoadField: r0 = r2->field_2b
    //     0x86879c: ldur            w0, [x2, #0x2b]
    // 0x8687a0: DecompressPointer r0
    //     0x8687a0: add             x0, x0, HEAP, lsl #32
    // 0x8687a4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x8687a4: ldur            w3, [x0, #0x17]
    // 0x8687a8: DecompressPointer r3
    //     0x8687a8: add             x3, x3, HEAP, lsl #32
    // 0x8687ac: stur            x3, [fp, #-8]
    // 0x8687b0: LoadField: r1 = r3->field_27
    //     0x8687b0: ldur            w1, [x3, #0x27]
    // 0x8687b4: DecompressPointer r1
    //     0x8687b4: add             x1, x1, HEAP, lsl #32
    // 0x8687b8: cmp             w1, NULL
    // 0x8687bc: b.ne            #0x8687cc
    // 0x8687c0: mov             x0, x2
    // 0x8687c4: mov             x1, x3
    // 0x8687c8: b               #0x8687d8
    // 0x8687cc: r0 = cancel()
    //     0x8687cc: bl              #0x61d6b4  ; [dart:isolate] _Timer::cancel
    // 0x8687d0: ldur            x0, [fp, #-0x10]
    // 0x8687d4: ldur            x1, [fp, #-8]
    // 0x8687d8: StoreField: r1->field_27 = rNULL
    //     0x8687d8: stur            NULL, [x1, #0x27]
    // 0x8687dc: LoadField: r2 = r1->field_23
    //     0x8687dc: ldur            w2, [x1, #0x23]
    // 0x8687e0: DecompressPointer r2
    //     0x8687e0: add             x2, x2, HEAP, lsl #32
    // 0x8687e4: mov             x1, x2
    // 0x8687e8: stur            x2, [fp, #-0x18]
    // 0x8687ec: r0 = stop()
    //     0x8687ec: bl              #0x637494  ; [dart:core] Stopwatch::stop
    // 0x8687f0: ldur            x1, [fp, #-0x18]
    // 0x8687f4: r0 = reset()
    //     0x8687f4: bl              #0x637420  ; [dart:core] Stopwatch::reset
    // 0x8687f8: ldur            x2, [fp, #-0x10]
    // 0x8687fc: LoadField: r1 = r2->field_1b
    //     0x8687fc: ldur            w1, [x2, #0x1b]
    // 0x868800: DecompressPointer r1
    //     0x868800: add             x1, x1, HEAP, lsl #32
    // 0x868804: r16 = Sentinel
    //     0x868804: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x868808: cmp             w1, w16
    // 0x86880c: b.eq            #0x868870
    // 0x868810: r0 = LoadClassIdInstr(r1)
    //     0x868810: ldur            x0, [x1, #-1]
    //     0x868814: ubfx            x0, x0, #0xc, #0x14
    // 0x868818: r0 = GDT[cid_x0 + -0xe14]()
    //     0x868818: sub             lr, x0, #0xe14
    //     0x86881c: ldr             lr, [x21, lr, lsl #3]
    //     0x868820: blr             lr
    // 0x868824: ldur            x0, [fp, #-0x10]
    // 0x868828: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x868828: ldur            w1, [x0, #0x17]
    // 0x86882c: DecompressPointer r1
    //     0x86882c: add             x1, x1, HEAP, lsl #32
    // 0x868830: LoadField: r2 = r0->field_f
    //     0x868830: ldur            w2, [x0, #0xf]
    // 0x868834: DecompressPointer r2
    //     0x868834: add             x2, x2, HEAP, lsl #32
    // 0x868838: LoadField: r0 = r2->field_5f
    //     0x868838: ldur            w0, [x2, #0x5f]
    // 0x86883c: DecompressPointer r0
    //     0x86883c: add             x0, x0, HEAP, lsl #32
    // 0x868840: cmp             w0, NULL
    // 0x868844: b.eq            #0x86888c
    // 0x868848: LoadField: r2 = r0->field_b
    //     0x868848: ldur            w2, [x0, #0xb]
    // 0x86884c: DecompressPointer r2
    //     0x86884c: add             x2, x2, HEAP, lsl #32
    // 0x868850: cmp             w2, NULL
    // 0x868854: b.eq            #0x868890
    // 0x868858: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x868858: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x86885c: r0 = _extension#0.addErrorAndClose()
    //     0x86885c: bl              #0x868894  ; [package:dio/src/response/response_stream_handler.dart] ::_extension#0.addErrorAndClose
    // 0x868860: r0 = Null
    //     0x868860: mov             x0, NULL
    // 0x868864: LeaveFrame
    //     0x868864: mov             SP, fp
    //     0x868868: ldp             fp, lr, [SP], #0x10
    // 0x86886c: ret
    //     0x86886c: ret             
    // 0x868870: r16 = "responseSubscription"
    //     0x868870: add             x16, PP, #8, lsl #12  ; [pp+0x8a90] "responseSubscription"
    //     0x868874: ldr             x16, [x16, #0xa90]
    // 0x868878: str             x16, [SP]
    // 0x86887c: r0 = _throwLocalNotInitialized()
    //     0x86887c: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x868880: brk             #0
    // 0x868884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868884: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868888: b               #0x86879c
    // 0x86888c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x86888c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x868890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x868890: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _extension#0.addErrorAndClose(/* No info */) {
    // ** addr: 0x868894, size: 0x78
    // 0x868894: EnterFrame
    //     0x868894: stp             fp, lr, [SP, #-0x10]!
    //     0x868898: mov             fp, SP
    // 0x86889c: AllocStack(0x10)
    //     0x86889c: sub             SP, SP, #0x10
    // 0x8688a0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, [dynamic _ = Null /* r1 */])
    //     0x8688a0: mov             x0, x1
    //     0x8688a4: stur            x1, [fp, #-8]
    //     0x8688a8: ldur            w1, [x4, #0x13]
    //     0x8688ac: sub             x3, x1, #4
    //     0x8688b0: cmp             w3, #2
    //     0x8688b4: b.lt            #0x8688c4
    //     0x8688b8: add             x1, fp, w3, sxtw #2
    //     0x8688bc: ldr             x1, [x1, #8]
    //     0x8688c0: b               #0x8688c8
    //     0x8688c4: mov             x1, NULL
    // 0x8688c8: CheckStackOverflow
    //     0x8688c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8688cc: cmp             SP, x16
    //     0x8688d0: b.ls            #0x868904
    // 0x8688d4: LoadField: r3 = r0->field_f
    //     0x8688d4: ldur            x3, [x0, #0xf]
    // 0x8688d8: tbnz            w3, #2, #0x8688f4
    // 0x8688dc: str             x1, [SP]
    // 0x8688e0: mov             x1, x0
    // 0x8688e4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8688e4: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8688e8: r0 = addError()
    //     0x8688e8: bl              #0x6799c4  ; [dart:async] _StreamController::addError
    // 0x8688ec: ldur            x1, [fp, #-8]
    // 0x8688f0: r0 = close()
    //     0x8688f0: bl              #0x6f11fc  ; [dart:async] _StreamController::close
    // 0x8688f4: r0 = Null
    //     0x8688f4: mov             x0, NULL
    // 0x8688f8: LeaveFrame
    //     0x8688f8: mov             SP, fp
    //     0x8688fc: ldp             fp, lr, [SP], #0x10
    // 0x868900: ret
    //     0x868900: ret             
    // 0x868904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868904: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868908: b               #0x8688d4
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x86890c, size: 0xec
    // 0x86890c: EnterFrame
    //     0x86890c: stp             fp, lr, [SP, #-0x10]!
    //     0x868910: mov             fp, SP
    // 0x868914: AllocStack(0x20)
    //     0x868914: sub             SP, SP, #0x20
    // 0x868918: SetupParameters()
    //     0x868918: ldr             x0, [fp, #0x10]
    //     0x86891c: ldur            w2, [x0, #0x17]
    //     0x868920: add             x2, x2, HEAP, lsl #32
    //     0x868924: stur            x2, [fp, #-0x10]
    // 0x868928: CheckStackOverflow
    //     0x868928: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86892c: cmp             SP, x16
    //     0x868930: b.ls            #0x8689f0
    // 0x868934: LoadField: r0 = r2->field_2b
    //     0x868934: ldur            w0, [x2, #0x2b]
    // 0x868938: DecompressPointer r0
    //     0x868938: add             x0, x0, HEAP, lsl #32
    // 0x86893c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x86893c: ldur            w3, [x0, #0x17]
    // 0x868940: DecompressPointer r3
    //     0x868940: add             x3, x3, HEAP, lsl #32
    // 0x868944: stur            x3, [fp, #-8]
    // 0x868948: LoadField: r1 = r3->field_27
    //     0x868948: ldur            w1, [x3, #0x27]
    // 0x86894c: DecompressPointer r1
    //     0x86894c: add             x1, x1, HEAP, lsl #32
    // 0x868950: cmp             w1, NULL
    // 0x868954: b.ne            #0x868964
    // 0x868958: mov             x0, x2
    // 0x86895c: mov             x1, x3
    // 0x868960: b               #0x868970
    // 0x868964: r0 = cancel()
    //     0x868964: bl              #0x61d6b4  ; [dart:isolate] _Timer::cancel
    // 0x868968: ldur            x0, [fp, #-0x10]
    // 0x86896c: ldur            x1, [fp, #-8]
    // 0x868970: StoreField: r1->field_27 = rNULL
    //     0x868970: stur            NULL, [x1, #0x27]
    // 0x868974: LoadField: r2 = r1->field_23
    //     0x868974: ldur            w2, [x1, #0x23]
    // 0x868978: DecompressPointer r2
    //     0x868978: add             x2, x2, HEAP, lsl #32
    // 0x86897c: mov             x1, x2
    // 0x868980: stur            x2, [fp, #-0x18]
    // 0x868984: r0 = stop()
    //     0x868984: bl              #0x637494  ; [dart:core] Stopwatch::stop
    // 0x868988: ldur            x1, [fp, #-0x18]
    // 0x86898c: r0 = reset()
    //     0x86898c: bl              #0x637420  ; [dart:core] Stopwatch::reset
    // 0x868990: ldur            x2, [fp, #-0x10]
    // 0x868994: LoadField: r1 = r2->field_1b
    //     0x868994: ldur            w1, [x2, #0x1b]
    // 0x868998: DecompressPointer r1
    //     0x868998: add             x1, x1, HEAP, lsl #32
    // 0x86899c: r16 = Sentinel
    //     0x86899c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8689a0: cmp             w1, w16
    // 0x8689a4: b.eq            #0x8689dc
    // 0x8689a8: r0 = LoadClassIdInstr(r1)
    //     0x8689a8: ldur            x0, [x1, #-1]
    //     0x8689ac: ubfx            x0, x0, #0xc, #0x14
    // 0x8689b0: r0 = GDT[cid_x0 + -0xe14]()
    //     0x8689b0: sub             lr, x0, #0xe14
    //     0x8689b4: ldr             lr, [x21, lr, lsl #3]
    //     0x8689b8: blr             lr
    // 0x8689bc: ldur            x0, [fp, #-0x10]
    // 0x8689c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8689c0: ldur            w1, [x0, #0x17]
    // 0x8689c4: DecompressPointer r1
    //     0x8689c4: add             x1, x1, HEAP, lsl #32
    // 0x8689c8: r0 = close()
    //     0x8689c8: bl              #0x6f11fc  ; [dart:async] _StreamController::close
    // 0x8689cc: r0 = Null
    //     0x8689cc: mov             x0, NULL
    // 0x8689d0: LeaveFrame
    //     0x8689d0: mov             SP, fp
    //     0x8689d4: ldp             fp, lr, [SP], #0x10
    // 0x8689d8: ret
    //     0x8689d8: ret             
    // 0x8689dc: r16 = "responseSubscription"
    //     0x8689dc: add             x16, PP, #8, lsl #12  ; [pp+0x8a90] "responseSubscription"
    //     0x8689e0: ldr             x16, [x16, #0xa90]
    // 0x8689e4: str             x16, [SP]
    // 0x8689e8: r0 = _throwLocalNotInitialized()
    //     0x8689e8: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8689ec: brk             #0
    // 0x8689f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8689f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8689f4: b               #0x868934
  }
  [closure] static Null <anonymous closure>(dynamic, dynamic, dynamic) {
    // ** addr: 0x8689f8, size: 0x140
    // 0x8689f8: EnterFrame
    //     0x8689f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8689fc: mov             fp, SP
    // 0x868a00: AllocStack(0x20)
    //     0x868a00: sub             SP, SP, #0x20
    // 0x868a04: SetupParameters()
    //     0x868a04: ldr             x0, [fp, #0x20]
    //     0x868a08: ldur            w2, [x0, #0x17]
    //     0x868a0c: add             x2, x2, HEAP, lsl #32
    //     0x868a10: stur            x2, [fp, #-0x10]
    // 0x868a14: CheckStackOverflow
    //     0x868a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868a18: cmp             SP, x16
    //     0x868a1c: b.ls            #0x868b30
    // 0x868a20: LoadField: r0 = r2->field_2b
    //     0x868a20: ldur            w0, [x2, #0x2b]
    // 0x868a24: DecompressPointer r0
    //     0x868a24: add             x0, x0, HEAP, lsl #32
    // 0x868a28: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x868a28: ldur            w3, [x0, #0x17]
    // 0x868a2c: DecompressPointer r3
    //     0x868a2c: add             x3, x3, HEAP, lsl #32
    // 0x868a30: stur            x3, [fp, #-8]
    // 0x868a34: LoadField: r1 = r3->field_27
    //     0x868a34: ldur            w1, [x3, #0x27]
    // 0x868a38: DecompressPointer r1
    //     0x868a38: add             x1, x1, HEAP, lsl #32
    // 0x868a3c: cmp             w1, NULL
    // 0x868a40: b.ne            #0x868a50
    // 0x868a44: mov             x0, x2
    // 0x868a48: mov             x1, x3
    // 0x868a4c: b               #0x868a5c
    // 0x868a50: r0 = cancel()
    //     0x868a50: bl              #0x61d6b4  ; [dart:isolate] _Timer::cancel
    // 0x868a54: ldur            x0, [fp, #-0x10]
    // 0x868a58: ldur            x1, [fp, #-8]
    // 0x868a5c: ldr             x2, [fp, #0x18]
    // 0x868a60: StoreField: r1->field_27 = rNULL
    //     0x868a60: stur            NULL, [x1, #0x27]
    // 0x868a64: LoadField: r3 = r1->field_23
    //     0x868a64: ldur            w3, [x1, #0x23]
    // 0x868a68: DecompressPointer r3
    //     0x868a68: add             x3, x3, HEAP, lsl #32
    // 0x868a6c: mov             x1, x3
    // 0x868a70: stur            x3, [fp, #-0x18]
    // 0x868a74: r0 = stop()
    //     0x868a74: bl              #0x637494  ; [dart:core] Stopwatch::stop
    // 0x868a78: ldur            x1, [fp, #-0x18]
    // 0x868a7c: r0 = reset()
    //     0x868a7c: bl              #0x637420  ; [dart:core] Stopwatch::reset
    // 0x868a80: ldur            x0, [fp, #-0x10]
    // 0x868a84: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x868a84: ldur            w3, [x0, #0x17]
    // 0x868a88: DecompressPointer r3
    //     0x868a88: add             x3, x3, HEAP, lsl #32
    // 0x868a8c: ldr             x4, [fp, #0x18]
    // 0x868a90: stur            x3, [fp, #-8]
    // 0x868a94: cmp             w4, NULL
    // 0x868a98: b.ne            #0x868ac0
    // 0x868a9c: mov             x0, x4
    // 0x868aa0: r2 = Null
    //     0x868aa0: mov             x2, NULL
    // 0x868aa4: r1 = Null
    //     0x868aa4: mov             x1, NULL
    // 0x868aa8: cmp             w0, NULL
    // 0x868aac: b.ne            #0x868ac0
    // 0x868ab0: r8 = Object
    //     0x868ab0: ldr             x8, [PP, #0xd8]  ; [pp+0xd8] Type: Object
    // 0x868ab4: r3 = Null
    //     0x868ab4: add             x3, PP, #8, lsl #12  ; [pp+0x8a98] Null
    //     0x868ab8: ldr             x3, [x3, #0xa98]
    // 0x868abc: r0 = Object()
    //     0x868abc: bl              #0x16fece8  ; IsType_Object_Stub
    // 0x868ac0: ldr             x0, [fp, #0x10]
    // 0x868ac4: r2 = Null
    //     0x868ac4: mov             x2, NULL
    // 0x868ac8: r1 = Null
    //     0x868ac8: mov             x1, NULL
    // 0x868acc: r4 = 60
    //     0x868acc: movz            x4, #0x3c
    // 0x868ad0: branchIfSmi(r0, 0x868adc)
    //     0x868ad0: tbz             w0, #0, #0x868adc
    // 0x868ad4: r4 = LoadClassIdInstr(r0)
    //     0x868ad4: ldur            x4, [x0, #-1]
    //     0x868ad8: ubfx            x4, x4, #0xc, #0x14
    // 0x868adc: cmp             x4, #0x4d
    // 0x868ae0: b.eq            #0x868b08
    // 0x868ae4: cmp             x4, #0x253
    // 0x868ae8: b.eq            #0x868b08
    // 0x868aec: r17 = 6665
    //     0x868aec: movz            x17, #0x1a09
    // 0x868af0: cmp             x4, x17
    // 0x868af4: b.eq            #0x868b08
    // 0x868af8: r8 = StackTrace?
    //     0x868af8: ldr             x8, [PP, #0x3338]  ; [pp+0x3338] Type: StackTrace?
    // 0x868afc: r3 = Null
    //     0x868afc: add             x3, PP, #8, lsl #12  ; [pp+0x8aa8] Null
    //     0x868b00: ldr             x3, [x3, #0xaa8]
    // 0x868b04: r0 = DefaultNullableTypeTest()
    //     0x868b04: bl              #0x16f5078  ; DefaultNullableTypeTestStub
    // 0x868b08: ldr             x16, [fp, #0x10]
    // 0x868b0c: str             x16, [SP]
    // 0x868b10: ldur            x1, [fp, #-8]
    // 0x868b14: ldr             x2, [fp, #0x18]
    // 0x868b18: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x868b18: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x868b1c: r0 = _extension#0.addErrorAndClose()
    //     0x868b1c: bl              #0x868894  ; [package:dio/src/response/response_stream_handler.dart] ::_extension#0.addErrorAndClose
    // 0x868b20: r0 = Null
    //     0x868b20: mov             x0, NULL
    // 0x868b24: LeaveFrame
    //     0x868b24: mov             SP, fp
    //     0x868b28: ldp             fp, lr, [SP], #0x10
    // 0x868b2c: ret
    //     0x868b2c: ret             
    // 0x868b30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868b30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868b34: b               #0x868a20
  }
  [closure] static void <anonymous closure>(dynamic, Uint8List) {
    // ** addr: 0x868b38, size: 0x94
    // 0x868b38: EnterFrame
    //     0x868b38: stp             fp, lr, [SP, #-0x10]!
    //     0x868b3c: mov             fp, SP
    // 0x868b40: AllocStack(0x10)
    //     0x868b40: sub             SP, SP, #0x10
    // 0x868b44: SetupParameters()
    //     0x868b44: ldr             x0, [fp, #0x18]
    //     0x868b48: ldur            w1, [x0, #0x17]
    //     0x868b4c: add             x1, x1, HEAP, lsl #32
    //     0x868b50: stur            x1, [fp, #-8]
    // 0x868b54: CheckStackOverflow
    //     0x868b54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868b58: cmp             SP, x16
    //     0x868b5c: b.ls            #0x868bc4
    // 0x868b60: LoadField: r0 = r1->field_2f
    //     0x868b60: ldur            w0, [x1, #0x2f]
    // 0x868b64: DecompressPointer r0
    //     0x868b64: add             x0, x0, HEAP, lsl #32
    // 0x868b68: str             x0, [SP]
    // 0x868b6c: ClosureCall
    //     0x868b6c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x868b70: ldur            x2, [x0, #0x1f]
    //     0x868b74: blr             x2
    // 0x868b78: ldur            x0, [fp, #-8]
    // 0x868b7c: LoadField: r1 = r0->field_23
    //     0x868b7c: ldur            w1, [x0, #0x23]
    // 0x868b80: DecompressPointer r1
    //     0x868b80: add             x1, x1, HEAP, lsl #32
    // 0x868b84: r0 = elapsedMicroseconds()
    //     0x868b84: bl              #0x637540  ; [dart:core] Stopwatch::elapsedMicroseconds
    // 0x868b88: mov             x1, x0
    // 0x868b8c: ldur            x0, [fp, #-8]
    // 0x868b90: LoadField: r2 = r0->field_1f
    //     0x868b90: ldur            w2, [x0, #0x1f]
    // 0x868b94: DecompressPointer r2
    //     0x868b94: add             x2, x2, HEAP, lsl #32
    // 0x868b98: LoadField: r3 = r2->field_7
    //     0x868b98: ldur            x3, [x2, #7]
    // 0x868b9c: cmp             x1, x3
    // 0x868ba0: b.gt            #0x868bb4
    // 0x868ba4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x868ba4: ldur            w1, [x0, #0x17]
    // 0x868ba8: DecompressPointer r1
    //     0x868ba8: add             x1, x1, HEAP, lsl #32
    // 0x868bac: ldr             x2, [fp, #0x10]
    // 0x868bb0: r0 = add()
    //     0x868bb0: bl              #0x65124c  ; [dart:async] _StreamController::add
    // 0x868bb4: r0 = Null
    //     0x868bb4: mov             x0, NULL
    // 0x868bb8: LeaveFrame
    //     0x868bb8: mov             SP, fp
    //     0x868bbc: ldp             fp, lr, [SP], #0x10
    // 0x868bc0: ret
    //     0x868bc0: ret             
    // 0x868bc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868bc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868bc8: b               #0x868b60
  }
  [closure] static void watchReceiveTimeout(dynamic) {
    // ** addr: 0x868bcc, size: 0xdc
    // 0x868bcc: EnterFrame
    //     0x868bcc: stp             fp, lr, [SP, #-0x10]!
    //     0x868bd0: mov             fp, SP
    // 0x868bd4: AllocStack(0x18)
    //     0x868bd4: sub             SP, SP, #0x18
    // 0x868bd8: SetupParameters()
    //     0x868bd8: ldr             x0, [fp, #0x10]
    //     0x868bdc: ldur            w2, [x0, #0x17]
    //     0x868be0: add             x2, x2, HEAP, lsl #32
    //     0x868be4: stur            x2, [fp, #-0x10]
    // 0x868be8: CheckStackOverflow
    //     0x868be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868bec: cmp             SP, x16
    //     0x868bf0: b.ls            #0x868ca0
    // 0x868bf4: LoadField: r0 = r2->field_1f
    //     0x868bf4: ldur            w0, [x2, #0x1f]
    // 0x868bf8: DecompressPointer r0
    //     0x868bf8: add             x0, x0, HEAP, lsl #32
    // 0x868bfc: stur            x0, [fp, #-8]
    // 0x868c00: LoadField: r1 = r0->field_7
    //     0x868c00: ldur            x1, [x0, #7]
    // 0x868c04: cmp             x1, #0
    // 0x868c08: b.gt            #0x868c1c
    // 0x868c0c: r0 = Null
    //     0x868c0c: mov             x0, NULL
    // 0x868c10: LeaveFrame
    //     0x868c10: mov             SP, fp
    //     0x868c14: ldp             fp, lr, [SP], #0x10
    // 0x868c18: ret
    //     0x868c18: ret             
    // 0x868c1c: LoadField: r1 = r2->field_27
    //     0x868c1c: ldur            w1, [x2, #0x27]
    // 0x868c20: DecompressPointer r1
    //     0x868c20: add             x1, x1, HEAP, lsl #32
    // 0x868c24: cmp             w1, NULL
    // 0x868c28: b.eq            #0x868c34
    // 0x868c2c: r0 = cancel()
    //     0x868c2c: bl              #0x61d6b4  ; [dart:isolate] _Timer::cancel
    // 0x868c30: ldur            x2, [fp, #-0x10]
    // 0x868c34: LoadField: r0 = r2->field_23
    //     0x868c34: ldur            w0, [x2, #0x23]
    // 0x868c38: DecompressPointer r0
    //     0x868c38: add             x0, x0, HEAP, lsl #32
    // 0x868c3c: mov             x1, x0
    // 0x868c40: stur            x0, [fp, #-0x18]
    // 0x868c44: r0 = reset()
    //     0x868c44: bl              #0x637420  ; [dart:core] Stopwatch::reset
    // 0x868c48: ldur            x1, [fp, #-0x18]
    // 0x868c4c: r0 = start()
    //     0x868c4c: bl              #0x636dc4  ; [dart:core] Stopwatch::start
    // 0x868c50: ldur            x2, [fp, #-0x10]
    // 0x868c54: r1 = Function '<anonymous closure>': static.
    //     0x868c54: add             x1, PP, #8, lsl #12  ; [pp+0x8af8] AnonymousClosure: static (0x868ca8), in [package:dio/src/response/response_stream_handler.dart] ::handleResponseStream (0x8684f0)
    //     0x868c58: ldr             x1, [x1, #0xaf8]
    // 0x868c5c: r0 = AllocateClosure()
    //     0x868c5c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x868c60: ldur            x2, [fp, #-8]
    // 0x868c64: mov             x3, x0
    // 0x868c68: r1 = Null
    //     0x868c68: mov             x1, NULL
    // 0x868c6c: r0 = Timer()
    //     0x868c6c: bl              #0x618b00  ; [dart:async] Timer::Timer
    // 0x868c70: ldur            x1, [fp, #-0x10]
    // 0x868c74: StoreField: r1->field_27 = r0
    //     0x868c74: stur            w0, [x1, #0x27]
    //     0x868c78: ldurb           w16, [x1, #-1]
    //     0x868c7c: ldurb           w17, [x0, #-1]
    //     0x868c80: and             x16, x17, x16, lsr #2
    //     0x868c84: tst             x16, HEAP, lsr #32
    //     0x868c88: b.eq            #0x868c90
    //     0x868c8c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x868c90: r0 = Null
    //     0x868c90: mov             x0, NULL
    // 0x868c94: LeaveFrame
    //     0x868c94: mov             SP, fp
    //     0x868c98: ldp             fp, lr, [SP], #0x10
    // 0x868c9c: ret
    //     0x868c9c: ret             
    // 0x868ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868ca0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868ca4: b               #0x868bf4
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x868ca8, size: 0x114
    // 0x868ca8: EnterFrame
    //     0x868ca8: stp             fp, lr, [SP, #-0x10]!
    //     0x868cac: mov             fp, SP
    // 0x868cb0: AllocStack(0x20)
    //     0x868cb0: sub             SP, SP, #0x20
    // 0x868cb4: SetupParameters()
    //     0x868cb4: ldr             x0, [fp, #0x10]
    //     0x868cb8: ldur            w2, [x0, #0x17]
    //     0x868cbc: add             x2, x2, HEAP, lsl #32
    //     0x868cc0: stur            x2, [fp, #-0x10]
    // 0x868cc4: CheckStackOverflow
    //     0x868cc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868cc8: cmp             SP, x16
    //     0x868ccc: b.ls            #0x868db4
    // 0x868cd0: LoadField: r0 = r2->field_2b
    //     0x868cd0: ldur            w0, [x2, #0x2b]
    // 0x868cd4: DecompressPointer r0
    //     0x868cd4: add             x0, x0, HEAP, lsl #32
    // 0x868cd8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x868cd8: ldur            w3, [x0, #0x17]
    // 0x868cdc: DecompressPointer r3
    //     0x868cdc: add             x3, x3, HEAP, lsl #32
    // 0x868ce0: stur            x3, [fp, #-8]
    // 0x868ce4: LoadField: r1 = r3->field_27
    //     0x868ce4: ldur            w1, [x3, #0x27]
    // 0x868ce8: DecompressPointer r1
    //     0x868ce8: add             x1, x1, HEAP, lsl #32
    // 0x868cec: cmp             w1, NULL
    // 0x868cf0: b.ne            #0x868d00
    // 0x868cf4: mov             x0, x2
    // 0x868cf8: mov             x1, x3
    // 0x868cfc: b               #0x868d0c
    // 0x868d00: r0 = cancel()
    //     0x868d00: bl              #0x61d6b4  ; [dart:isolate] _Timer::cancel
    // 0x868d04: ldur            x0, [fp, #-0x10]
    // 0x868d08: ldur            x1, [fp, #-8]
    // 0x868d0c: StoreField: r1->field_27 = rNULL
    //     0x868d0c: stur            NULL, [x1, #0x27]
    // 0x868d10: LoadField: r2 = r1->field_23
    //     0x868d10: ldur            w2, [x1, #0x23]
    // 0x868d14: DecompressPointer r2
    //     0x868d14: add             x2, x2, HEAP, lsl #32
    // 0x868d18: mov             x1, x2
    // 0x868d1c: stur            x2, [fp, #-0x18]
    // 0x868d20: r0 = stop()
    //     0x868d20: bl              #0x637494  ; [dart:core] Stopwatch::stop
    // 0x868d24: ldur            x1, [fp, #-0x18]
    // 0x868d28: r0 = reset()
    //     0x868d28: bl              #0x637420  ; [dart:core] Stopwatch::reset
    // 0x868d2c: ldur            x2, [fp, #-0x10]
    // 0x868d30: LoadField: r1 = r2->field_1b
    //     0x868d30: ldur            w1, [x2, #0x1b]
    // 0x868d34: DecompressPointer r1
    //     0x868d34: add             x1, x1, HEAP, lsl #32
    // 0x868d38: r16 = Sentinel
    //     0x868d38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x868d3c: cmp             w1, w16
    // 0x868d40: b.eq            #0x868da0
    // 0x868d44: r0 = LoadClassIdInstr(r1)
    //     0x868d44: ldur            x0, [x1, #-1]
    //     0x868d48: ubfx            x0, x0, #0xc, #0x14
    // 0x868d4c: r0 = GDT[cid_x0 + -0xe14]()
    //     0x868d4c: sub             lr, x0, #0xe14
    //     0x868d50: ldr             lr, [x21, lr, lsl #3]
    //     0x868d54: blr             lr
    // 0x868d58: ldur            x0, [fp, #-0x10]
    // 0x868d5c: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x868d5c: ldur            w4, [x0, #0x17]
    // 0x868d60: DecompressPointer r4
    //     0x868d60: add             x4, x4, HEAP, lsl #32
    // 0x868d64: stur            x4, [fp, #-8]
    // 0x868d68: LoadField: r2 = r0->field_f
    //     0x868d68: ldur            w2, [x0, #0xf]
    // 0x868d6c: DecompressPointer r2
    //     0x868d6c: add             x2, x2, HEAP, lsl #32
    // 0x868d70: LoadField: r3 = r0->field_1f
    //     0x868d70: ldur            w3, [x0, #0x1f]
    // 0x868d74: DecompressPointer r3
    //     0x868d74: add             x3, x3, HEAP, lsl #32
    // 0x868d78: r1 = Null
    //     0x868d78: mov             x1, NULL
    // 0x868d7c: r0 = DioException.receiveTimeout()
    //     0x868d7c: bl              #0x868dbc  ; [package:dio/src/dio_exception.dart] DioException::DioException.receiveTimeout
    // 0x868d80: ldur            x1, [fp, #-8]
    // 0x868d84: mov             x2, x0
    // 0x868d88: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x868d88: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x868d8c: r0 = _extension#0.addErrorAndClose()
    //     0x868d8c: bl              #0x868894  ; [package:dio/src/response/response_stream_handler.dart] ::_extension#0.addErrorAndClose
    // 0x868d90: r0 = Null
    //     0x868d90: mov             x0, NULL
    // 0x868d94: LeaveFrame
    //     0x868d94: mov             SP, fp
    //     0x868d98: ldp             fp, lr, [SP], #0x10
    // 0x868d9c: ret
    //     0x868d9c: ret             
    // 0x868da0: r16 = "responseSubscription"
    //     0x868da0: add             x16, PP, #8, lsl #12  ; [pp+0x8a90] "responseSubscription"
    //     0x868da4: ldr             x16, [x16, #0xa90]
    // 0x868da8: str             x16, [SP]
    // 0x868dac: r0 = _throwLocalNotInitialized()
    //     0x868dac: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x868db0: brk             #0
    // 0x868db4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868db4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868db8: b               #0x868cd0
  }
  [closure] static void stopWatchReceiveTimeout(dynamic) {
    // ** addr: 0x868e74, size: 0x80
    // 0x868e74: EnterFrame
    //     0x868e74: stp             fp, lr, [SP, #-0x10]!
    //     0x868e78: mov             fp, SP
    // 0x868e7c: AllocStack(0x10)
    //     0x868e7c: sub             SP, SP, #0x10
    // 0x868e80: SetupParameters()
    //     0x868e80: ldr             x0, [fp, #0x10]
    //     0x868e84: ldur            w2, [x0, #0x17]
    //     0x868e88: add             x2, x2, HEAP, lsl #32
    //     0x868e8c: stur            x2, [fp, #-8]
    // 0x868e90: CheckStackOverflow
    //     0x868e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x868e94: cmp             SP, x16
    //     0x868e98: b.ls            #0x868eec
    // 0x868e9c: LoadField: r1 = r2->field_27
    //     0x868e9c: ldur            w1, [x2, #0x27]
    // 0x868ea0: DecompressPointer r1
    //     0x868ea0: add             x1, x1, HEAP, lsl #32
    // 0x868ea4: cmp             w1, NULL
    // 0x868ea8: b.ne            #0x868eb4
    // 0x868eac: mov             x0, x2
    // 0x868eb0: b               #0x868ebc
    // 0x868eb4: r0 = cancel()
    //     0x868eb4: bl              #0x61d6b4  ; [dart:isolate] _Timer::cancel
    // 0x868eb8: ldur            x0, [fp, #-8]
    // 0x868ebc: StoreField: r0->field_27 = rNULL
    //     0x868ebc: stur            NULL, [x0, #0x27]
    // 0x868ec0: LoadField: r2 = r0->field_23
    //     0x868ec0: ldur            w2, [x0, #0x23]
    // 0x868ec4: DecompressPointer r2
    //     0x868ec4: add             x2, x2, HEAP, lsl #32
    // 0x868ec8: mov             x1, x2
    // 0x868ecc: stur            x2, [fp, #-0x10]
    // 0x868ed0: r0 = stop()
    //     0x868ed0: bl              #0x637494  ; [dart:core] Stopwatch::stop
    // 0x868ed4: ldur            x1, [fp, #-0x10]
    // 0x868ed8: r0 = reset()
    //     0x868ed8: bl              #0x637420  ; [dart:core] Stopwatch::reset
    // 0x868edc: r0 = Null
    //     0x868edc: mov             x0, NULL
    // 0x868ee0: LeaveFrame
    //     0x868ee0: mov             SP, fp
    //     0x868ee4: ldp             fp, lr, [SP], #0x10
    // 0x868ee8: ret
    //     0x868ee8: ret             
    // 0x868eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x868eec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x868ef0: b               #0x868e9c
  }
}
