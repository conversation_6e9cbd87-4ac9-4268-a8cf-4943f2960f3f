// lib: , url: package:customer_app/app/presentation/views/line/customization/camer_picker.dart

// class id: 1049505, size: 0x8
class :: {
}

// class id: 3259, size: 0x18, field offset: 0x14
class _CameraPickerState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbd6f78, size: 0x5f4
    // 0xbd6f78: EnterFrame
    //     0xbd6f78: stp             fp, lr, [SP, #-0x10]!
    //     0xbd6f7c: mov             fp, SP
    // 0xbd6f80: AllocStack(0x60)
    //     0xbd6f80: sub             SP, SP, #0x60
    // 0xbd6f84: SetupParameters(_CameraPickerState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbd6f84: mov             x0, x1
    //     0xbd6f88: stur            x1, [fp, #-8]
    //     0xbd6f8c: mov             x1, x2
    //     0xbd6f90: stur            x2, [fp, #-0x10]
    // 0xbd6f94: CheckStackOverflow
    //     0xbd6f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd6f98: cmp             SP, x16
    //     0xbd6f9c: b.ls            #0xbd755c
    // 0xbd6fa0: r1 = 1
    //     0xbd6fa0: movz            x1, #0x1
    // 0xbd6fa4: r0 = AllocateContext()
    //     0xbd6fa4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd6fa8: mov             x3, x0
    // 0xbd6fac: ldur            x0, [fp, #-8]
    // 0xbd6fb0: stur            x3, [fp, #-0x20]
    // 0xbd6fb4: StoreField: r3->field_f = r0
    //     0xbd6fb4: stur            w0, [x3, #0xf]
    // 0xbd6fb8: LoadField: r1 = r0->field_b
    //     0xbd6fb8: ldur            w1, [x0, #0xb]
    // 0xbd6fbc: DecompressPointer r1
    //     0xbd6fbc: add             x1, x1, HEAP, lsl #32
    // 0xbd6fc0: cmp             w1, NULL
    // 0xbd6fc4: b.eq            #0xbd7564
    // 0xbd6fc8: LoadField: r2 = r1->field_b
    //     0xbd6fc8: ldur            w2, [x1, #0xb]
    // 0xbd6fcc: DecompressPointer r2
    //     0xbd6fcc: add             x2, x2, HEAP, lsl #32
    // 0xbd6fd0: cmp             w2, NULL
    // 0xbd6fd4: b.ne            #0xbd6fe0
    // 0xbd6fd8: r1 = Null
    //     0xbd6fd8: mov             x1, NULL
    // 0xbd6fdc: b               #0xbd6fe8
    // 0xbd6fe0: LoadField: r1 = r2->field_2b
    //     0xbd6fe0: ldur            w1, [x2, #0x2b]
    // 0xbd6fe4: DecompressPointer r1
    //     0xbd6fe4: add             x1, x1, HEAP, lsl #32
    // 0xbd6fe8: cmp             w1, NULL
    // 0xbd6fec: b.eq            #0xbd7048
    // 0xbd6ff0: tbnz            w1, #4, #0xbd7048
    // 0xbd6ff4: cmp             w2, NULL
    // 0xbd6ff8: b.ne            #0xbd7004
    // 0xbd6ffc: r4 = Null
    //     0xbd6ffc: mov             x4, NULL
    // 0xbd7000: b               #0xbd7010
    // 0xbd7004: LoadField: r1 = r2->field_1b
    //     0xbd7004: ldur            w1, [x2, #0x1b]
    // 0xbd7008: DecompressPointer r1
    //     0xbd7008: add             x1, x1, HEAP, lsl #32
    // 0xbd700c: mov             x4, x1
    // 0xbd7010: stur            x4, [fp, #-0x18]
    // 0xbd7014: r1 = Null
    //     0xbd7014: mov             x1, NULL
    // 0xbd7018: r2 = 4
    //     0xbd7018: movz            x2, #0x4
    // 0xbd701c: r0 = AllocateArray()
    //     0xbd701c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd7020: mov             x1, x0
    // 0xbd7024: ldur            x0, [fp, #-0x18]
    // 0xbd7028: StoreField: r1->field_f = r0
    //     0xbd7028: stur            w0, [x1, #0xf]
    // 0xbd702c: r16 = " *"
    //     0xbd702c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xbd7030: ldr             x16, [x16, #0xfc8]
    // 0xbd7034: StoreField: r1->field_13 = r16
    //     0xbd7034: stur            w16, [x1, #0x13]
    // 0xbd7038: str             x1, [SP]
    // 0xbd703c: r0 = _interpolate()
    //     0xbd703c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd7040: mov             x2, x0
    // 0xbd7044: b               #0xbd706c
    // 0xbd7048: cmp             w2, NULL
    // 0xbd704c: b.ne            #0xbd7058
    // 0xbd7050: r0 = Null
    //     0xbd7050: mov             x0, NULL
    // 0xbd7054: b               #0xbd7060
    // 0xbd7058: LoadField: r0 = r2->field_1b
    //     0xbd7058: ldur            w0, [x2, #0x1b]
    // 0xbd705c: DecompressPointer r0
    //     0xbd705c: add             x0, x0, HEAP, lsl #32
    // 0xbd7060: str             x0, [SP]
    // 0xbd7064: r0 = _interpolateSingle()
    //     0xbd7064: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbd7068: mov             x2, x0
    // 0xbd706c: ldur            x0, [fp, #-8]
    // 0xbd7070: ldur            x1, [fp, #-0x10]
    // 0xbd7074: stur            x2, [fp, #-0x18]
    // 0xbd7078: r0 = of()
    //     0xbd7078: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd707c: LoadField: r1 = r0->field_87
    //     0xbd707c: ldur            w1, [x0, #0x87]
    // 0xbd7080: DecompressPointer r1
    //     0xbd7080: add             x1, x1, HEAP, lsl #32
    // 0xbd7084: LoadField: r0 = r1->field_7
    //     0xbd7084: ldur            w0, [x1, #7]
    // 0xbd7088: DecompressPointer r0
    //     0xbd7088: add             x0, x0, HEAP, lsl #32
    // 0xbd708c: r16 = Instance_Color
    //     0xbd708c: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd7090: r30 = 14.000000
    //     0xbd7090: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd7094: ldr             lr, [lr, #0x1d8]
    // 0xbd7098: stp             lr, x16, [SP]
    // 0xbd709c: mov             x1, x0
    // 0xbd70a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbd70a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbd70a4: ldr             x4, [x4, #0x9b8]
    // 0xbd70a8: r0 = copyWith()
    //     0xbd70a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd70ac: stur            x0, [fp, #-0x28]
    // 0xbd70b0: r0 = Text()
    //     0xbd70b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd70b4: mov             x3, x0
    // 0xbd70b8: ldur            x0, [fp, #-0x18]
    // 0xbd70bc: stur            x3, [fp, #-0x30]
    // 0xbd70c0: StoreField: r3->field_b = r0
    //     0xbd70c0: stur            w0, [x3, #0xb]
    // 0xbd70c4: ldur            x0, [fp, #-0x28]
    // 0xbd70c8: StoreField: r3->field_13 = r0
    //     0xbd70c8: stur            w0, [x3, #0x13]
    // 0xbd70cc: ldur            x0, [fp, #-8]
    // 0xbd70d0: LoadField: r1 = r0->field_b
    //     0xbd70d0: ldur            w1, [x0, #0xb]
    // 0xbd70d4: DecompressPointer r1
    //     0xbd70d4: add             x1, x1, HEAP, lsl #32
    // 0xbd70d8: cmp             w1, NULL
    // 0xbd70dc: b.eq            #0xbd7568
    // 0xbd70e0: LoadField: r4 = r1->field_b
    //     0xbd70e0: ldur            w4, [x1, #0xb]
    // 0xbd70e4: DecompressPointer r4
    //     0xbd70e4: add             x4, x4, HEAP, lsl #32
    // 0xbd70e8: stur            x4, [fp, #-0x28]
    // 0xbd70ec: cmp             w4, NULL
    // 0xbd70f0: b.ne            #0xbd70fc
    // 0xbd70f4: r1 = Null
    //     0xbd70f4: mov             x1, NULL
    // 0xbd70f8: b               #0xbd7104
    // 0xbd70fc: LoadField: r1 = r4->field_23
    //     0xbd70fc: ldur            w1, [x4, #0x23]
    // 0xbd7100: DecompressPointer r1
    //     0xbd7100: add             x1, x1, HEAP, lsl #32
    // 0xbd7104: cbnz            w1, #0xbd7110
    // 0xbd7108: r5 = false
    //     0xbd7108: add             x5, NULL, #0x30  ; false
    // 0xbd710c: b               #0xbd7114
    // 0xbd7110: r5 = true
    //     0xbd7110: add             x5, NULL, #0x20  ; true
    // 0xbd7114: stur            x5, [fp, #-0x18]
    // 0xbd7118: r1 = Null
    //     0xbd7118: mov             x1, NULL
    // 0xbd711c: r2 = 4
    //     0xbd711c: movz            x2, #0x4
    // 0xbd7120: r0 = AllocateArray()
    //     0xbd7120: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd7124: mov             x1, x0
    // 0xbd7128: stur            x1, [fp, #-0x38]
    // 0xbd712c: r16 = "+ "
    //     0xbd712c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xbd7130: ldr             x16, [x16, #0xc30]
    // 0xbd7134: StoreField: r1->field_f = r16
    //     0xbd7134: stur            w16, [x1, #0xf]
    // 0xbd7138: ldur            x0, [fp, #-0x28]
    // 0xbd713c: cmp             w0, NULL
    // 0xbd7140: b.ne            #0xbd714c
    // 0xbd7144: r0 = Null
    //     0xbd7144: mov             x0, NULL
    // 0xbd7148: b               #0xbd7174
    // 0xbd714c: LoadField: r2 = r0->field_27
    //     0xbd714c: ldur            w2, [x0, #0x27]
    // 0xbd7150: DecompressPointer r2
    //     0xbd7150: add             x2, x2, HEAP, lsl #32
    // 0xbd7154: r0 = LoadClassIdInstr(r2)
    //     0xbd7154: ldur            x0, [x2, #-1]
    //     0xbd7158: ubfx            x0, x0, #0xc, #0x14
    // 0xbd715c: str             x2, [SP]
    // 0xbd7160: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbd7160: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbd7164: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbd7164: movz            x17, #0x2700
    //     0xbd7168: add             lr, x0, x17
    //     0xbd716c: ldr             lr, [x21, lr, lsl #3]
    //     0xbd7170: blr             lr
    // 0xbd7174: cmp             w0, NULL
    // 0xbd7178: b.ne            #0xbd7180
    // 0xbd717c: r0 = ""
    //     0xbd717c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd7180: ldur            x3, [fp, #-8]
    // 0xbd7184: ldur            x2, [fp, #-0x30]
    // 0xbd7188: ldur            x4, [fp, #-0x18]
    // 0xbd718c: ldur            x1, [fp, #-0x38]
    // 0xbd7190: ArrayStore: r1[1] = r0  ; List_4
    //     0xbd7190: add             x25, x1, #0x13
    //     0xbd7194: str             w0, [x25]
    //     0xbd7198: tbz             w0, #0, #0xbd71b4
    //     0xbd719c: ldurb           w16, [x1, #-1]
    //     0xbd71a0: ldurb           w17, [x0, #-1]
    //     0xbd71a4: and             x16, x17, x16, lsr #2
    //     0xbd71a8: tst             x16, HEAP, lsr #32
    //     0xbd71ac: b.eq            #0xbd71b4
    //     0xbd71b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd71b4: ldur            x16, [fp, #-0x38]
    // 0xbd71b8: str             x16, [SP]
    // 0xbd71bc: r0 = _interpolate()
    //     0xbd71bc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd71c0: ldur            x1, [fp, #-0x10]
    // 0xbd71c4: stur            x0, [fp, #-0x28]
    // 0xbd71c8: r0 = of()
    //     0xbd71c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd71cc: LoadField: r1 = r0->field_87
    //     0xbd71cc: ldur            w1, [x0, #0x87]
    // 0xbd71d0: DecompressPointer r1
    //     0xbd71d0: add             x1, x1, HEAP, lsl #32
    // 0xbd71d4: LoadField: r0 = r1->field_2b
    //     0xbd71d4: ldur            w0, [x1, #0x2b]
    // 0xbd71d8: DecompressPointer r0
    //     0xbd71d8: add             x0, x0, HEAP, lsl #32
    // 0xbd71dc: stur            x0, [fp, #-0x38]
    // 0xbd71e0: r1 = Instance_Color
    //     0xbd71e0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd71e4: d0 = 0.700000
    //     0xbd71e4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd71e8: ldr             d0, [x17, #0xf48]
    // 0xbd71ec: r0 = withOpacity()
    //     0xbd71ec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd71f0: r16 = 14.000000
    //     0xbd71f0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd71f4: ldr             x16, [x16, #0x1d8]
    // 0xbd71f8: stp             x16, x0, [SP]
    // 0xbd71fc: ldur            x1, [fp, #-0x38]
    // 0xbd7200: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbd7200: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbd7204: ldr             x4, [x4, #0x9b8]
    // 0xbd7208: r0 = copyWith()
    //     0xbd7208: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd720c: stur            x0, [fp, #-0x38]
    // 0xbd7210: r0 = Text()
    //     0xbd7210: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd7214: mov             x1, x0
    // 0xbd7218: ldur            x0, [fp, #-0x28]
    // 0xbd721c: stur            x1, [fp, #-0x40]
    // 0xbd7220: StoreField: r1->field_b = r0
    //     0xbd7220: stur            w0, [x1, #0xb]
    // 0xbd7224: ldur            x0, [fp, #-0x38]
    // 0xbd7228: StoreField: r1->field_13 = r0
    //     0xbd7228: stur            w0, [x1, #0x13]
    // 0xbd722c: r0 = Visibility()
    //     0xbd722c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbd7230: mov             x3, x0
    // 0xbd7234: ldur            x0, [fp, #-0x40]
    // 0xbd7238: stur            x3, [fp, #-0x28]
    // 0xbd723c: StoreField: r3->field_b = r0
    //     0xbd723c: stur            w0, [x3, #0xb]
    // 0xbd7240: r0 = Instance_SizedBox
    //     0xbd7240: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd7244: StoreField: r3->field_f = r0
    //     0xbd7244: stur            w0, [x3, #0xf]
    // 0xbd7248: ldur            x0, [fp, #-0x18]
    // 0xbd724c: StoreField: r3->field_13 = r0
    //     0xbd724c: stur            w0, [x3, #0x13]
    // 0xbd7250: r0 = false
    //     0xbd7250: add             x0, NULL, #0x30  ; false
    // 0xbd7254: ArrayStore: r3[0] = r0  ; List_4
    //     0xbd7254: stur            w0, [x3, #0x17]
    // 0xbd7258: StoreField: r3->field_1b = r0
    //     0xbd7258: stur            w0, [x3, #0x1b]
    // 0xbd725c: StoreField: r3->field_1f = r0
    //     0xbd725c: stur            w0, [x3, #0x1f]
    // 0xbd7260: StoreField: r3->field_23 = r0
    //     0xbd7260: stur            w0, [x3, #0x23]
    // 0xbd7264: StoreField: r3->field_27 = r0
    //     0xbd7264: stur            w0, [x3, #0x27]
    // 0xbd7268: StoreField: r3->field_2b = r0
    //     0xbd7268: stur            w0, [x3, #0x2b]
    // 0xbd726c: r1 = Null
    //     0xbd726c: mov             x1, NULL
    // 0xbd7270: r2 = 6
    //     0xbd7270: movz            x2, #0x6
    // 0xbd7274: r0 = AllocateArray()
    //     0xbd7274: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd7278: mov             x2, x0
    // 0xbd727c: ldur            x0, [fp, #-0x30]
    // 0xbd7280: stur            x2, [fp, #-0x18]
    // 0xbd7284: StoreField: r2->field_f = r0
    //     0xbd7284: stur            w0, [x2, #0xf]
    // 0xbd7288: r16 = Instance_Spacer
    //     0xbd7288: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbd728c: ldr             x16, [x16, #0xf0]
    // 0xbd7290: StoreField: r2->field_13 = r16
    //     0xbd7290: stur            w16, [x2, #0x13]
    // 0xbd7294: ldur            x0, [fp, #-0x28]
    // 0xbd7298: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd7298: stur            w0, [x2, #0x17]
    // 0xbd729c: r1 = <Widget>
    //     0xbd729c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd72a0: r0 = AllocateGrowableArray()
    //     0xbd72a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd72a4: mov             x1, x0
    // 0xbd72a8: ldur            x0, [fp, #-0x18]
    // 0xbd72ac: stur            x1, [fp, #-0x28]
    // 0xbd72b0: StoreField: r1->field_f = r0
    //     0xbd72b0: stur            w0, [x1, #0xf]
    // 0xbd72b4: r0 = 6
    //     0xbd72b4: movz            x0, #0x6
    // 0xbd72b8: StoreField: r1->field_b = r0
    //     0xbd72b8: stur            w0, [x1, #0xb]
    // 0xbd72bc: r0 = Row()
    //     0xbd72bc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd72c0: mov             x1, x0
    // 0xbd72c4: r0 = Instance_Axis
    //     0xbd72c4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd72c8: stur            x1, [fp, #-0x18]
    // 0xbd72cc: StoreField: r1->field_f = r0
    //     0xbd72cc: stur            w0, [x1, #0xf]
    // 0xbd72d0: r0 = Instance_MainAxisAlignment
    //     0xbd72d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd72d4: ldr             x0, [x0, #0xa08]
    // 0xbd72d8: StoreField: r1->field_13 = r0
    //     0xbd72d8: stur            w0, [x1, #0x13]
    // 0xbd72dc: r2 = Instance_MainAxisSize
    //     0xbd72dc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd72e0: ldr             x2, [x2, #0xa10]
    // 0xbd72e4: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd72e4: stur            w2, [x1, #0x17]
    // 0xbd72e8: r3 = Instance_CrossAxisAlignment
    //     0xbd72e8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd72ec: ldr             x3, [x3, #0xa18]
    // 0xbd72f0: StoreField: r1->field_1b = r3
    //     0xbd72f0: stur            w3, [x1, #0x1b]
    // 0xbd72f4: r3 = Instance_VerticalDirection
    //     0xbd72f4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd72f8: ldr             x3, [x3, #0xa20]
    // 0xbd72fc: StoreField: r1->field_23 = r3
    //     0xbd72fc: stur            w3, [x1, #0x23]
    // 0xbd7300: r4 = Instance_Clip
    //     0xbd7300: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd7304: ldr             x4, [x4, #0x38]
    // 0xbd7308: StoreField: r1->field_2b = r4
    //     0xbd7308: stur            w4, [x1, #0x2b]
    // 0xbd730c: StoreField: r1->field_2f = rZR
    //     0xbd730c: stur            xzr, [x1, #0x2f]
    // 0xbd7310: ldur            x5, [fp, #-0x28]
    // 0xbd7314: StoreField: r1->field_b = r5
    //     0xbd7314: stur            w5, [x1, #0xb]
    // 0xbd7318: r0 = Padding()
    //     0xbd7318: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd731c: mov             x2, x0
    // 0xbd7320: r0 = Instance_EdgeInsets
    //     0xbd7320: add             x0, PP, #0x33, lsl #12  ; [pp+0x33788] Obj!EdgeInsets@d58e21
    //     0xbd7324: ldr             x0, [x0, #0x788]
    // 0xbd7328: stur            x2, [fp, #-0x28]
    // 0xbd732c: StoreField: r2->field_f = r0
    //     0xbd732c: stur            w0, [x2, #0xf]
    // 0xbd7330: ldur            x0, [fp, #-0x18]
    // 0xbd7334: StoreField: r2->field_b = r0
    //     0xbd7334: stur            w0, [x2, #0xb]
    // 0xbd7338: ldur            x0, [fp, #-8]
    // 0xbd733c: LoadField: r1 = r0->field_13
    //     0xbd733c: ldur            w1, [x0, #0x13]
    // 0xbd7340: DecompressPointer r1
    //     0xbd7340: add             x1, x1, HEAP, lsl #32
    // 0xbd7344: cmp             w1, NULL
    // 0xbd7348: b.ne            #0xbd73d4
    // 0xbd734c: ldur            x1, [fp, #-0x10]
    // 0xbd7350: r0 = of()
    //     0xbd7350: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd7354: LoadField: r1 = r0->field_5b
    //     0xbd7354: ldur            w1, [x0, #0x5b]
    // 0xbd7358: DecompressPointer r1
    //     0xbd7358: add             x1, x1, HEAP, lsl #32
    // 0xbd735c: stur            x1, [fp, #-8]
    // 0xbd7360: r0 = ColorFilter()
    //     0xbd7360: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbd7364: mov             x1, x0
    // 0xbd7368: ldur            x0, [fp, #-8]
    // 0xbd736c: stur            x1, [fp, #-0x10]
    // 0xbd7370: StoreField: r1->field_7 = r0
    //     0xbd7370: stur            w0, [x1, #7]
    // 0xbd7374: r0 = Instance_BlendMode
    //     0xbd7374: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbd7378: ldr             x0, [x0, #0xb30]
    // 0xbd737c: StoreField: r1->field_b = r0
    //     0xbd737c: stur            w0, [x1, #0xb]
    // 0xbd7380: r0 = 1
    //     0xbd7380: movz            x0, #0x1
    // 0xbd7384: StoreField: r1->field_13 = r0
    //     0xbd7384: stur            x0, [x1, #0x13]
    // 0xbd7388: r0 = SvgPicture()
    //     0xbd7388: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd738c: stur            x0, [fp, #-8]
    // 0xbd7390: r16 = Instance_BoxFit
    //     0xbd7390: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd7394: ldr             x16, [x16, #0xb18]
    // 0xbd7398: r30 = 60.000000
    //     0xbd7398: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xbd739c: ldr             lr, [lr, #0x110]
    // 0xbd73a0: stp             lr, x16, [SP, #0x10]
    // 0xbd73a4: r16 = 60.000000
    //     0xbd73a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xbd73a8: ldr             x16, [x16, #0x110]
    // 0xbd73ac: ldur            lr, [fp, #-0x10]
    // 0xbd73b0: stp             lr, x16, [SP]
    // 0xbd73b4: mov             x1, x0
    // 0xbd73b8: r2 = "assets/images/placeholder_image.svg"
    //     0xbd73b8: add             x2, PP, #0x6a, lsl #12  ; [pp+0x6a510] "assets/images/placeholder_image.svg"
    //     0xbd73bc: ldr             x2, [x2, #0x510]
    // 0xbd73c0: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0xbd73c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0xbd73c4: ldr             x4, [x4, #0xa68]
    // 0xbd73c8: r0 = SvgPicture.asset()
    //     0xbd73c8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd73cc: ldur            x1, [fp, #-8]
    // 0xbd73d0: b               #0xbd7438
    // 0xbd73d4: LoadField: r0 = r1->field_7
    //     0xbd73d4: ldur            w0, [x1, #7]
    // 0xbd73d8: DecompressPointer r0
    //     0xbd73d8: add             x0, x0, HEAP, lsl #32
    // 0xbd73dc: stur            x0, [fp, #-8]
    // 0xbd73e0: r0 = current()
    //     0xbd73e0: bl              #0x62b7cc  ; [dart:io] IOOverrides::current
    // 0xbd73e4: r0 = _File()
    //     0xbd73e4: bl              #0x62f3ac  ; Allocate_FileStub -> _File (size=0x10)
    // 0xbd73e8: ldur            x1, [fp, #-8]
    // 0xbd73ec: stur            x0, [fp, #-0x10]
    // 0xbd73f0: StoreField: r0->field_7 = r1
    //     0xbd73f0: stur            w1, [x0, #7]
    // 0xbd73f4: r0 = _toUtf8Array()
    //     0xbd73f4: bl              #0x62b684  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0xbd73f8: ldur            x2, [fp, #-0x10]
    // 0xbd73fc: StoreField: r2->field_b = r0
    //     0xbd73fc: stur            w0, [x2, #0xb]
    //     0xbd7400: ldurb           w16, [x2, #-1]
    //     0xbd7404: ldurb           w17, [x0, #-1]
    //     0xbd7408: and             x16, x17, x16, lsr #2
    //     0xbd740c: tst             x16, HEAP, lsr #32
    //     0xbd7410: b.eq            #0xbd7418
    //     0xbd7414: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbd7418: r0 = Image()
    //     0xbd7418: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xbd741c: mov             x1, x0
    // 0xbd7420: ldur            x2, [fp, #-0x10]
    // 0xbd7424: d0 = 60.000000
    //     0xbd7424: ldr             d0, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0xbd7428: d1 = 60.000000
    //     0xbd7428: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0xbd742c: stur            x0, [fp, #-8]
    // 0xbd7430: r0 = Image.file()
    //     0xbd7430: bl              #0x9d34d0  ; [package:flutter/src/widgets/image.dart] Image::Image.file
    // 0xbd7434: ldur            x1, [fp, #-8]
    // 0xbd7438: ldur            x0, [fp, #-0x28]
    // 0xbd743c: stur            x1, [fp, #-8]
    // 0xbd7440: r0 = InkWell()
    //     0xbd7440: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbd7444: mov             x3, x0
    // 0xbd7448: ldur            x0, [fp, #-8]
    // 0xbd744c: stur            x3, [fp, #-0x10]
    // 0xbd7450: StoreField: r3->field_b = r0
    //     0xbd7450: stur            w0, [x3, #0xb]
    // 0xbd7454: ldur            x2, [fp, #-0x20]
    // 0xbd7458: r1 = Function '<anonymous closure>':.
    //     0xbd7458: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a518] AnonymousClosure: (0xbd758c), in [package:customer_app/app/presentation/views/line/customization/camer_picker.dart] _CameraPickerState::build (0xbd6f78)
    //     0xbd745c: ldr             x1, [x1, #0x518]
    // 0xbd7460: r0 = AllocateClosure()
    //     0xbd7460: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd7464: mov             x1, x0
    // 0xbd7468: ldur            x0, [fp, #-0x10]
    // 0xbd746c: StoreField: r0->field_f = r1
    //     0xbd746c: stur            w1, [x0, #0xf]
    // 0xbd7470: r1 = true
    //     0xbd7470: add             x1, NULL, #0x20  ; true
    // 0xbd7474: StoreField: r0->field_43 = r1
    //     0xbd7474: stur            w1, [x0, #0x43]
    // 0xbd7478: r2 = Instance_BoxShape
    //     0xbd7478: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbd747c: ldr             x2, [x2, #0x80]
    // 0xbd7480: StoreField: r0->field_47 = r2
    //     0xbd7480: stur            w2, [x0, #0x47]
    // 0xbd7484: StoreField: r0->field_6f = r1
    //     0xbd7484: stur            w1, [x0, #0x6f]
    // 0xbd7488: r2 = false
    //     0xbd7488: add             x2, NULL, #0x30  ; false
    // 0xbd748c: StoreField: r0->field_73 = r2
    //     0xbd748c: stur            w2, [x0, #0x73]
    // 0xbd7490: StoreField: r0->field_83 = r1
    //     0xbd7490: stur            w1, [x0, #0x83]
    // 0xbd7494: StoreField: r0->field_7b = r2
    //     0xbd7494: stur            w2, [x0, #0x7b]
    // 0xbd7498: r1 = Null
    //     0xbd7498: mov             x1, NULL
    // 0xbd749c: r2 = 4
    //     0xbd749c: movz            x2, #0x4
    // 0xbd74a0: r0 = AllocateArray()
    //     0xbd74a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd74a4: mov             x2, x0
    // 0xbd74a8: ldur            x0, [fp, #-0x28]
    // 0xbd74ac: stur            x2, [fp, #-8]
    // 0xbd74b0: StoreField: r2->field_f = r0
    //     0xbd74b0: stur            w0, [x2, #0xf]
    // 0xbd74b4: ldur            x0, [fp, #-0x10]
    // 0xbd74b8: StoreField: r2->field_13 = r0
    //     0xbd74b8: stur            w0, [x2, #0x13]
    // 0xbd74bc: r1 = <Widget>
    //     0xbd74bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd74c0: r0 = AllocateGrowableArray()
    //     0xbd74c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd74c4: mov             x1, x0
    // 0xbd74c8: ldur            x0, [fp, #-8]
    // 0xbd74cc: stur            x1, [fp, #-0x10]
    // 0xbd74d0: StoreField: r1->field_f = r0
    //     0xbd74d0: stur            w0, [x1, #0xf]
    // 0xbd74d4: r0 = 4
    //     0xbd74d4: movz            x0, #0x4
    // 0xbd74d8: StoreField: r1->field_b = r0
    //     0xbd74d8: stur            w0, [x1, #0xb]
    // 0xbd74dc: r0 = Column()
    //     0xbd74dc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbd74e0: mov             x1, x0
    // 0xbd74e4: r0 = Instance_Axis
    //     0xbd74e4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbd74e8: stur            x1, [fp, #-8]
    // 0xbd74ec: StoreField: r1->field_f = r0
    //     0xbd74ec: stur            w0, [x1, #0xf]
    // 0xbd74f0: r0 = Instance_MainAxisAlignment
    //     0xbd74f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd74f4: ldr             x0, [x0, #0xa08]
    // 0xbd74f8: StoreField: r1->field_13 = r0
    //     0xbd74f8: stur            w0, [x1, #0x13]
    // 0xbd74fc: r0 = Instance_MainAxisSize
    //     0xbd74fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd7500: ldr             x0, [x0, #0xa10]
    // 0xbd7504: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd7504: stur            w0, [x1, #0x17]
    // 0xbd7508: r0 = Instance_CrossAxisAlignment
    //     0xbd7508: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbd750c: ldr             x0, [x0, #0x890]
    // 0xbd7510: StoreField: r1->field_1b = r0
    //     0xbd7510: stur            w0, [x1, #0x1b]
    // 0xbd7514: r0 = Instance_VerticalDirection
    //     0xbd7514: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd7518: ldr             x0, [x0, #0xa20]
    // 0xbd751c: StoreField: r1->field_23 = r0
    //     0xbd751c: stur            w0, [x1, #0x23]
    // 0xbd7520: r0 = Instance_Clip
    //     0xbd7520: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd7524: ldr             x0, [x0, #0x38]
    // 0xbd7528: StoreField: r1->field_2b = r0
    //     0xbd7528: stur            w0, [x1, #0x2b]
    // 0xbd752c: StoreField: r1->field_2f = rZR
    //     0xbd752c: stur            xzr, [x1, #0x2f]
    // 0xbd7530: ldur            x0, [fp, #-0x10]
    // 0xbd7534: StoreField: r1->field_b = r0
    //     0xbd7534: stur            w0, [x1, #0xb]
    // 0xbd7538: r0 = Padding()
    //     0xbd7538: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd753c: r1 = Instance_EdgeInsets
    //     0xbd753c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbd7540: ldr             x1, [x1, #0x1f0]
    // 0xbd7544: StoreField: r0->field_f = r1
    //     0xbd7544: stur            w1, [x0, #0xf]
    // 0xbd7548: ldur            x1, [fp, #-8]
    // 0xbd754c: StoreField: r0->field_b = r1
    //     0xbd754c: stur            w1, [x0, #0xb]
    // 0xbd7550: LeaveFrame
    //     0xbd7550: mov             SP, fp
    //     0xbd7554: ldp             fp, lr, [SP], #0x10
    // 0xbd7558: ret
    //     0xbd7558: ret             
    // 0xbd755c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd755c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7560: b               #0xbd6fa0
    // 0xbd7564: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7564: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd7568: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7568: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd758c, size: 0xac
    // 0xbd758c: EnterFrame
    //     0xbd758c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd7590: mov             fp, SP
    // 0xbd7594: AllocStack(0x38)
    //     0xbd7594: sub             SP, SP, #0x38
    // 0xbd7598: SetupParameters()
    //     0xbd7598: ldr             x0, [fp, #0x10]
    //     0xbd759c: ldur            w2, [x0, #0x17]
    //     0xbd75a0: add             x2, x2, HEAP, lsl #32
    //     0xbd75a4: stur            x2, [fp, #-8]
    // 0xbd75a8: CheckStackOverflow
    //     0xbd75a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd75ac: cmp             SP, x16
    //     0xbd75b0: b.ls            #0xbd7630
    // 0xbd75b4: LoadField: r1 = r2->field_f
    //     0xbd75b4: ldur            w1, [x2, #0xf]
    // 0xbd75b8: DecompressPointer r1
    //     0xbd75b8: add             x1, x1, HEAP, lsl #32
    // 0xbd75bc: r0 = _getImageFromGallery()
    //     0xbd75bc: bl              #0xbd7638  ; [package:customer_app/app/presentation/views/line/customization/camer_picker.dart] _CameraPickerState::_getImageFromGallery
    // 0xbd75c0: stur            x0, [fp, #-0x18]
    // 0xbd75c4: LoadField: r3 = r0->field_7
    //     0xbd75c4: ldur            w3, [x0, #7]
    // 0xbd75c8: DecompressPointer r3
    //     0xbd75c8: add             x3, x3, HEAP, lsl #32
    // 0xbd75cc: ldur            x2, [fp, #-8]
    // 0xbd75d0: stur            x3, [fp, #-0x10]
    // 0xbd75d4: r1 = Function '<anonymous closure>':.
    //     0xbd75d4: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a520] AnonymousClosure: (0xbd7e6c), in [package:customer_app/app/presentation/views/line/customization/camer_picker.dart] _CameraPickerState::build (0xbd6f78)
    //     0xbd75d8: ldr             x1, [x1, #0x520]
    // 0xbd75dc: r0 = AllocateClosure()
    //     0xbd75dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd75e0: ldur            x2, [fp, #-0x10]
    // 0xbd75e4: mov             x3, x0
    // 0xbd75e8: r1 = Null
    //     0xbd75e8: mov             x1, NULL
    // 0xbd75ec: stur            x3, [fp, #-8]
    // 0xbd75f0: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0xbd75f0: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0xbd75f4: ldr             x8, [x8, #0xce8]
    // 0xbd75f8: LoadField: r9 = r8->field_7
    //     0xbd75f8: ldur            x9, [x8, #7]
    // 0xbd75fc: r3 = Null
    //     0xbd75fc: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a528] Null
    //     0xbd7600: ldr             x3, [x3, #0x528]
    // 0xbd7604: blr             x9
    // 0xbd7608: ldur            x16, [fp, #-0x18]
    // 0xbd760c: stp             x16, NULL, [SP, #0x10]
    // 0xbd7610: ldur            x16, [fp, #-8]
    // 0xbd7614: stp             NULL, x16, [SP]
    // 0xbd7618: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0xbd7618: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0xbd761c: r0 = then()
    //     0xbd761c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xbd7620: r0 = Null
    //     0xbd7620: mov             x0, NULL
    // 0xbd7624: LeaveFrame
    //     0xbd7624: mov             SP, fp
    //     0xbd7628: ldp             fp, lr, [SP], #0x10
    // 0xbd762c: ret
    //     0xbd762c: ret             
    // 0xbd7630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7630: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7634: b               #0xbd75b4
  }
  _ _getImageFromGallery(/* No info */) async {
    // ** addr: 0xbd7638, size: 0x1e4
    // 0xbd7638: EnterFrame
    //     0xbd7638: stp             fp, lr, [SP, #-0x10]!
    //     0xbd763c: mov             fp, SP
    // 0xbd7640: AllocStack(0x38)
    //     0xbd7640: sub             SP, SP, #0x38
    // 0xbd7644: SetupParameters(_CameraPickerState this /* r1 => r1, fp-0x10 */)
    //     0xbd7644: stur            NULL, [fp, #-8]
    //     0xbd7648: stur            x1, [fp, #-0x10]
    // 0xbd764c: CheckStackOverflow
    //     0xbd764c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd7650: cmp             SP, x16
    //     0xbd7654: b.ls            #0xbd7810
    // 0xbd7658: r1 = 2
    //     0xbd7658: movz            x1, #0x2
    // 0xbd765c: r0 = AllocateContext()
    //     0xbd765c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd7660: mov             x1, x0
    // 0xbd7664: ldur            x0, [fp, #-0x10]
    // 0xbd7668: stur            x1, [fp, #-0x18]
    // 0xbd766c: StoreField: r1->field_f = r0
    //     0xbd766c: stur            w0, [x1, #0xf]
    // 0xbd7670: InitAsync() -> Future
    //     0xbd7670: mov             x0, NULL
    //     0xbd7674: bl              #0x6326e0  ; InitAsyncStub
    // 0xbd7678: r0 = ImagePicker()
    //     0xbd7678: bl              #0x9a787c  ; AllocateImagePickerStub -> ImagePicker (size=0x8)
    // 0xbd767c: r16 = 1800.000000
    //     0xbd767c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a540] 1800
    //     0xbd7680: ldr             x16, [x16, #0x540]
    // 0xbd7684: r30 = 1800.000000
    //     0xbd7684: add             lr, PP, #0x6a, lsl #12  ; [pp+0x6a540] 1800
    //     0xbd7688: ldr             lr, [lr, #0x540]
    // 0xbd768c: stp             lr, x16, [SP]
    // 0xbd7690: mov             x1, x0
    // 0xbd7694: r4 = const [0, 0x3, 0x2, 0x1, maxHeight, 0x2, maxWidth, 0x1, null]
    //     0xbd7694: add             x4, PP, #0x6a, lsl #12  ; [pp+0x6a548] List(9) [0, 0x3, 0x2, 0x1, "maxHeight", 0x2, "maxWidth", 0x1, Null]
    //     0xbd7698: ldr             x4, [x4, #0x548]
    // 0xbd769c: r0 = pickImage()
    //     0xbd769c: bl              #0xa302d8  ; [package:image_picker/image_picker.dart] ImagePicker::pickImage
    // 0xbd76a0: mov             x1, x0
    // 0xbd76a4: stur            x1, [fp, #-0x10]
    // 0xbd76a8: r0 = Await()
    //     0xbd76a8: bl              #0x63248c  ; AwaitStub
    // 0xbd76ac: cmp             w0, NULL
    // 0xbd76b0: b.eq            #0xbd7808
    // 0xbd76b4: ldur            x2, [fp, #-0x18]
    // 0xbd76b8: LoadField: r1 = r0->field_7
    //     0xbd76b8: ldur            w1, [x0, #7]
    // 0xbd76bc: DecompressPointer r1
    //     0xbd76bc: add             x1, x1, HEAP, lsl #32
    // 0xbd76c0: LoadField: r0 = r1->field_7
    //     0xbd76c0: ldur            w0, [x1, #7]
    // 0xbd76c4: DecompressPointer r0
    //     0xbd76c4: add             x0, x0, HEAP, lsl #32
    // 0xbd76c8: stur            x0, [fp, #-0x10]
    // 0xbd76cc: r0 = current()
    //     0xbd76cc: bl              #0x62b7cc  ; [dart:io] IOOverrides::current
    // 0xbd76d0: r0 = _File()
    //     0xbd76d0: bl              #0x62f3ac  ; Allocate_FileStub -> _File (size=0x10)
    // 0xbd76d4: ldur            x1, [fp, #-0x10]
    // 0xbd76d8: stur            x0, [fp, #-0x20]
    // 0xbd76dc: StoreField: r0->field_7 = r1
    //     0xbd76dc: stur            w1, [x0, #7]
    // 0xbd76e0: r0 = _toUtf8Array()
    //     0xbd76e0: bl              #0x62b684  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0xbd76e4: ldur            x1, [fp, #-0x20]
    // 0xbd76e8: StoreField: r1->field_b = r0
    //     0xbd76e8: stur            w0, [x1, #0xb]
    //     0xbd76ec: ldurb           w16, [x1, #-1]
    //     0xbd76f0: ldurb           w17, [x0, #-1]
    //     0xbd76f4: and             x16, x17, x16, lsr #2
    //     0xbd76f8: tst             x16, HEAP, lsr #32
    //     0xbd76fc: b.eq            #0xbd7704
    //     0xbd7700: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xbd7704: mov             x0, x1
    // 0xbd7708: ldur            x2, [fp, #-0x18]
    // 0xbd770c: StoreField: r2->field_13 = r0
    //     0xbd770c: stur            w0, [x2, #0x13]
    //     0xbd7710: ldurb           w16, [x2, #-1]
    //     0xbd7714: ldurb           w17, [x0, #-1]
    //     0xbd7718: and             x16, x17, x16, lsr #2
    //     0xbd771c: tst             x16, HEAP, lsr #32
    //     0xbd7720: b.eq            #0xbd7728
    //     0xbd7724: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbd7728: r0 = LoadStaticField(0x878)
    //     0xbd7728: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd772c: ldr             x0, [x0, #0x10f0]
    // 0xbd7730: cmp             w0, NULL
    // 0xbd7734: b.eq            #0xbd7818
    // 0xbd7738: LoadField: r3 = r0->field_53
    //     0xbd7738: ldur            w3, [x0, #0x53]
    // 0xbd773c: DecompressPointer r3
    //     0xbd773c: add             x3, x3, HEAP, lsl #32
    // 0xbd7740: stur            x3, [fp, #-0x20]
    // 0xbd7744: LoadField: r0 = r3->field_7
    //     0xbd7744: ldur            w0, [x3, #7]
    // 0xbd7748: DecompressPointer r0
    //     0xbd7748: add             x0, x0, HEAP, lsl #32
    // 0xbd774c: stur            x0, [fp, #-0x10]
    // 0xbd7750: r1 = Function '<anonymous closure>':.
    //     0xbd7750: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a550] AnonymousClosure: (0xbd781c), in [package:customer_app/app/presentation/views/line/customization/camer_picker.dart] _CameraPickerState::_getImageFromGallery (0xbd7638)
    //     0xbd7754: ldr             x1, [x1, #0x550]
    // 0xbd7758: r0 = AllocateClosure()
    //     0xbd7758: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd775c: ldur            x2, [fp, #-0x10]
    // 0xbd7760: mov             x3, x0
    // 0xbd7764: r1 = Null
    //     0xbd7764: mov             x1, NULL
    // 0xbd7768: stur            x3, [fp, #-0x10]
    // 0xbd776c: cmp             w2, NULL
    // 0xbd7770: b.eq            #0xbd7790
    // 0xbd7774: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbd7774: ldur            w4, [x2, #0x17]
    // 0xbd7778: DecompressPointer r4
    //     0xbd7778: add             x4, x4, HEAP, lsl #32
    // 0xbd777c: r8 = X0
    //     0xbd777c: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0xbd7780: LoadField: r9 = r4->field_7
    //     0xbd7780: ldur            x9, [x4, #7]
    // 0xbd7784: r3 = Null
    //     0xbd7784: add             x3, PP, #0x6a, lsl #12  ; [pp+0x6a558] Null
    //     0xbd7788: ldr             x3, [x3, #0x558]
    // 0xbd778c: blr             x9
    // 0xbd7790: ldur            x0, [fp, #-0x20]
    // 0xbd7794: LoadField: r1 = r0->field_b
    //     0xbd7794: ldur            w1, [x0, #0xb]
    // 0xbd7798: LoadField: r2 = r0->field_f
    //     0xbd7798: ldur            w2, [x0, #0xf]
    // 0xbd779c: DecompressPointer r2
    //     0xbd779c: add             x2, x2, HEAP, lsl #32
    // 0xbd77a0: LoadField: r3 = r2->field_b
    //     0xbd77a0: ldur            w3, [x2, #0xb]
    // 0xbd77a4: r2 = LoadInt32Instr(r1)
    //     0xbd77a4: sbfx            x2, x1, #1, #0x1f
    // 0xbd77a8: stur            x2, [fp, #-0x28]
    // 0xbd77ac: r1 = LoadInt32Instr(r3)
    //     0xbd77ac: sbfx            x1, x3, #1, #0x1f
    // 0xbd77b0: cmp             x2, x1
    // 0xbd77b4: b.ne            #0xbd77c0
    // 0xbd77b8: mov             x1, x0
    // 0xbd77bc: r0 = _growToNextCapacity()
    //     0xbd77bc: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbd77c0: ldur            x2, [fp, #-0x20]
    // 0xbd77c4: ldur            x3, [fp, #-0x28]
    // 0xbd77c8: add             x4, x3, #1
    // 0xbd77cc: lsl             x5, x4, #1
    // 0xbd77d0: StoreField: r2->field_b = r5
    //     0xbd77d0: stur            w5, [x2, #0xb]
    // 0xbd77d4: LoadField: r1 = r2->field_f
    //     0xbd77d4: ldur            w1, [x2, #0xf]
    // 0xbd77d8: DecompressPointer r1
    //     0xbd77d8: add             x1, x1, HEAP, lsl #32
    // 0xbd77dc: ldur            x0, [fp, #-0x10]
    // 0xbd77e0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbd77e0: add             x25, x1, x3, lsl #2
    //     0xbd77e4: add             x25, x25, #0xf
    //     0xbd77e8: str             w0, [x25]
    //     0xbd77ec: tbz             w0, #0, #0xbd7808
    //     0xbd77f0: ldurb           w16, [x1, #-1]
    //     0xbd77f4: ldurb           w17, [x0, #-1]
    //     0xbd77f8: and             x16, x17, x16, lsr #2
    //     0xbd77fc: tst             x16, HEAP, lsr #32
    //     0xbd7800: b.eq            #0xbd7808
    //     0xbd7804: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd7808: r0 = Null
    //     0xbd7808: mov             x0, NULL
    // 0xbd780c: r0 = ReturnAsyncNotFuture()
    //     0xbd780c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xbd7810: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7810: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7814: b               #0xbd7658
    // 0xbd7818: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7818: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0xbd781c, size: 0x60
    // 0xbd781c: EnterFrame
    //     0xbd781c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd7820: mov             fp, SP
    // 0xbd7824: AllocStack(0x8)
    //     0xbd7824: sub             SP, SP, #8
    // 0xbd7828: SetupParameters()
    //     0xbd7828: ldr             x0, [fp, #0x18]
    //     0xbd782c: ldur            w2, [x0, #0x17]
    //     0xbd7830: add             x2, x2, HEAP, lsl #32
    // 0xbd7834: CheckStackOverflow
    //     0xbd7834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd7838: cmp             SP, x16
    //     0xbd783c: b.ls            #0xbd7874
    // 0xbd7840: LoadField: r0 = r2->field_f
    //     0xbd7840: ldur            w0, [x2, #0xf]
    // 0xbd7844: DecompressPointer r0
    //     0xbd7844: add             x0, x0, HEAP, lsl #32
    // 0xbd7848: stur            x0, [fp, #-8]
    // 0xbd784c: r1 = Function '<anonymous closure>':.
    //     0xbd784c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a568] AnonymousClosure: (0xbd787c), in [package:customer_app/app/presentation/views/line/customization/camer_picker.dart] _CameraPickerState::_getImageFromGallery (0xbd7638)
    //     0xbd7850: ldr             x1, [x1, #0x568]
    // 0xbd7854: r0 = AllocateClosure()
    //     0xbd7854: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd7858: ldur            x1, [fp, #-8]
    // 0xbd785c: mov             x2, x0
    // 0xbd7860: r0 = setState()
    //     0xbd7860: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbd7864: r0 = Null
    //     0xbd7864: mov             x0, NULL
    // 0xbd7868: LeaveFrame
    //     0xbd7868: mov             SP, fp
    //     0xbd786c: ldp             fp, lr, [SP], #0x10
    // 0xbd7870: ret
    //     0xbd7870: ret             
    // 0xbd7874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7874: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7878: b               #0xbd7840
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd787c, size: 0x5f0
    // 0xbd787c: EnterFrame
    //     0xbd787c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd7880: mov             fp, SP
    // 0xbd7884: AllocStack(0x78)
    //     0xbd7884: sub             SP, SP, #0x78
    // 0xbd7888: SetupParameters()
    //     0xbd7888: ldr             x0, [fp, #0x10]
    //     0xbd788c: ldur            w2, [x0, #0x17]
    //     0xbd7890: add             x2, x2, HEAP, lsl #32
    //     0xbd7894: stur            x2, [fp, #-8]
    // 0xbd7898: CheckStackOverflow
    //     0xbd7898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd789c: cmp             SP, x16
    //     0xbd78a0: b.ls            #0xbd7e48
    // 0xbd78a4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xbd78a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd78a8: ldr             x0, [x0]
    //     0xbd78ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd78b0: cmp             w0, w16
    //     0xbd78b4: b.ne            #0xbd78c0
    //     0xbd78b8: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xbd78bc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd78c0: r1 = <CustomerResponse>
    //     0xbd78c0: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xbd78c4: ldr             x1, [x1, #0x5a8]
    // 0xbd78c8: stur            x0, [fp, #-0x10]
    // 0xbd78cc: r0 = AllocateGrowableArray()
    //     0xbd78cc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd78d0: mov             x2, x0
    // 0xbd78d4: ldur            x0, [fp, #-0x10]
    // 0xbd78d8: stur            x2, [fp, #-0x18]
    // 0xbd78dc: StoreField: r2->field_f = r0
    //     0xbd78dc: stur            w0, [x2, #0xf]
    // 0xbd78e0: StoreField: r2->field_b = rZR
    //     0xbd78e0: stur            wzr, [x2, #0xb]
    // 0xbd78e4: r1 = <ProductCustomisation>
    //     0xbd78e4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xbd78e8: ldr             x1, [x1, #0x370]
    // 0xbd78ec: r0 = AllocateGrowableArray()
    //     0xbd78ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd78f0: mov             x3, x0
    // 0xbd78f4: ldur            x0, [fp, #-0x10]
    // 0xbd78f8: stur            x3, [fp, #-0x20]
    // 0xbd78fc: StoreField: r3->field_f = r0
    //     0xbd78fc: stur            w0, [x3, #0xf]
    // 0xbd7900: StoreField: r3->field_b = rZR
    //     0xbd7900: stur            wzr, [x3, #0xb]
    // 0xbd7904: ldur            x0, [fp, #-8]
    // 0xbd7908: LoadField: r1 = r0->field_f
    //     0xbd7908: ldur            w1, [x0, #0xf]
    // 0xbd790c: DecompressPointer r1
    //     0xbd790c: add             x1, x1, HEAP, lsl #32
    // 0xbd7910: LoadField: r2 = r1->field_b
    //     0xbd7910: ldur            w2, [x1, #0xb]
    // 0xbd7914: DecompressPointer r2
    //     0xbd7914: add             x2, x2, HEAP, lsl #32
    // 0xbd7918: cmp             w2, NULL
    // 0xbd791c: b.eq            #0xbd7e50
    // 0xbd7920: LoadField: r4 = r2->field_f
    //     0xbd7920: ldur            w4, [x2, #0xf]
    // 0xbd7924: DecompressPointer r4
    //     0xbd7924: add             x4, x4, HEAP, lsl #32
    // 0xbd7928: stur            x4, [fp, #-0x10]
    // 0xbd792c: LoadField: r1 = r4->field_b
    //     0xbd792c: ldur            w1, [x4, #0xb]
    // 0xbd7930: cbz             w1, #0xbd79f0
    // 0xbd7934: mov             x2, x0
    // 0xbd7938: r1 = Function '<anonymous closure>':.
    //     0xbd7938: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a570] AnonymousClosure: (0xa3afc0), in [package:customer_app/app/presentation/views/line/customization/customization_number.dart] _CustomisationNumberState::build (0xbd9810)
    //     0xbd793c: ldr             x1, [x1, #0x570]
    // 0xbd7940: r0 = AllocateClosure()
    //     0xbd7940: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd7944: r1 = Function '<anonymous closure>':.
    //     0xbd7944: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a578] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xbd7948: ldr             x1, [x1, #0x578]
    // 0xbd794c: r2 = Null
    //     0xbd794c: mov             x2, NULL
    // 0xbd7950: stur            x0, [fp, #-0x28]
    // 0xbd7954: r0 = AllocateClosure()
    //     0xbd7954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd7958: str             x0, [SP]
    // 0xbd795c: ldur            x1, [fp, #-0x10]
    // 0xbd7960: ldur            x2, [fp, #-0x28]
    // 0xbd7964: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xbd7964: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xbd7968: ldr             x4, [x4, #0xb48]
    // 0xbd796c: r0 = firstWhere()
    //     0xbd796c: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xbd7970: LoadField: r1 = r0->field_b
    //     0xbd7970: ldur            w1, [x0, #0xb]
    // 0xbd7974: DecompressPointer r1
    //     0xbd7974: add             x1, x1, HEAP, lsl #32
    // 0xbd7978: cmp             w1, NULL
    // 0xbd797c: b.eq            #0xbd79f0
    // 0xbd7980: ldur            x1, [fp, #-8]
    // 0xbd7984: LoadField: r2 = r1->field_f
    //     0xbd7984: ldur            w2, [x1, #0xf]
    // 0xbd7988: DecompressPointer r2
    //     0xbd7988: add             x2, x2, HEAP, lsl #32
    // 0xbd798c: LoadField: r3 = r2->field_b
    //     0xbd798c: ldur            w3, [x2, #0xb]
    // 0xbd7990: DecompressPointer r3
    //     0xbd7990: add             x3, x3, HEAP, lsl #32
    // 0xbd7994: cmp             w3, NULL
    // 0xbd7998: b.eq            #0xbd7e54
    // 0xbd799c: LoadField: r2 = r3->field_b
    //     0xbd799c: ldur            w2, [x3, #0xb]
    // 0xbd79a0: DecompressPointer r2
    //     0xbd79a0: add             x2, x2, HEAP, lsl #32
    // 0xbd79a4: cmp             w2, NULL
    // 0xbd79a8: b.ne            #0xbd79b4
    // 0xbd79ac: r2 = Null
    //     0xbd79ac: mov             x2, NULL
    // 0xbd79b0: b               #0xbd79c0
    // 0xbd79b4: LoadField: r4 = r2->field_23
    //     0xbd79b4: ldur            w4, [x2, #0x23]
    // 0xbd79b8: DecompressPointer r4
    //     0xbd79b8: add             x4, x4, HEAP, lsl #32
    // 0xbd79bc: mov             x2, x4
    // 0xbd79c0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xbd79c0: ldur            w4, [x3, #0x17]
    // 0xbd79c4: DecompressPointer r4
    //     0xbd79c4: add             x4, x4, HEAP, lsl #32
    // 0xbd79c8: stp             x2, x4, [SP, #0x10]
    // 0xbd79cc: r16 = "camera_picker"
    //     0xbd79cc: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a580] "camera_picker"
    //     0xbd79d0: ldr             x16, [x16, #0x580]
    // 0xbd79d4: stp             x16, x0, [SP]
    // 0xbd79d8: r4 = 0
    //     0xbd79d8: movz            x4, #0
    // 0xbd79dc: ldr             x0, [SP, #0x18]
    // 0xbd79e0: r16 = UnlinkedCall_0x613b5c
    //     0xbd79e0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a588] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd79e4: add             x16, x16, #0x588
    // 0xbd79e8: ldp             x5, lr, [x16]
    // 0xbd79ec: blr             lr
    // 0xbd79f0: ldur            x3, [fp, #-8]
    // 0xbd79f4: LoadField: r0 = r3->field_f
    //     0xbd79f4: ldur            w0, [x3, #0xf]
    // 0xbd79f8: DecompressPointer r0
    //     0xbd79f8: add             x0, x0, HEAP, lsl #32
    // 0xbd79fc: LoadField: r1 = r0->field_b
    //     0xbd79fc: ldur            w1, [x0, #0xb]
    // 0xbd7a00: DecompressPointer r1
    //     0xbd7a00: add             x1, x1, HEAP, lsl #32
    // 0xbd7a04: cmp             w1, NULL
    // 0xbd7a08: b.eq            #0xbd7e58
    // 0xbd7a0c: LoadField: r0 = r1->field_b
    //     0xbd7a0c: ldur            w0, [x1, #0xb]
    // 0xbd7a10: DecompressPointer r0
    //     0xbd7a10: add             x0, x0, HEAP, lsl #32
    // 0xbd7a14: cmp             w0, NULL
    // 0xbd7a18: b.ne            #0xbd7a24
    // 0xbd7a1c: r4 = Null
    //     0xbd7a1c: mov             x4, NULL
    // 0xbd7a20: b               #0xbd7a30
    // 0xbd7a24: LoadField: r1 = r0->field_1b
    //     0xbd7a24: ldur            w1, [x0, #0x1b]
    // 0xbd7a28: DecompressPointer r1
    //     0xbd7a28: add             x1, x1, HEAP, lsl #32
    // 0xbd7a2c: mov             x4, x1
    // 0xbd7a30: stur            x4, [fp, #-0x30]
    // 0xbd7a34: LoadField: r5 = r3->field_13
    //     0xbd7a34: ldur            w5, [x3, #0x13]
    // 0xbd7a38: DecompressPointer r5
    //     0xbd7a38: add             x5, x5, HEAP, lsl #32
    // 0xbd7a3c: stur            x5, [fp, #-0x28]
    // 0xbd7a40: LoadField: r6 = r5->field_7
    //     0xbd7a40: ldur            w6, [x5, #7]
    // 0xbd7a44: DecompressPointer r6
    //     0xbd7a44: add             x6, x6, HEAP, lsl #32
    // 0xbd7a48: stur            x6, [fp, #-0x10]
    // 0xbd7a4c: r0 = LoadClassIdInstr(r6)
    //     0xbd7a4c: ldur            x0, [x6, #-1]
    //     0xbd7a50: ubfx            x0, x0, #0xc, #0x14
    // 0xbd7a54: mov             x1, x6
    // 0xbd7a58: r2 = "/"
    //     0xbd7a58: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0xbd7a5c: r0 = GDT[cid_x0 + -0xffc]()
    //     0xbd7a5c: sub             lr, x0, #0xffc
    //     0xbd7a60: ldr             lr, [x21, lr, lsl #3]
    //     0xbd7a64: blr             lr
    // 0xbd7a68: mov             x1, x0
    // 0xbd7a6c: r0 = last()
    //     0xbd7a6c: bl              #0x79c9a8  ; [dart:core] _GrowableList::last
    // 0xbd7a70: mov             x1, x0
    // 0xbd7a74: ldur            x0, [fp, #-8]
    // 0xbd7a78: stur            x1, [fp, #-0x40]
    // 0xbd7a7c: LoadField: r2 = r0->field_f
    //     0xbd7a7c: ldur            w2, [x0, #0xf]
    // 0xbd7a80: DecompressPointer r2
    //     0xbd7a80: add             x2, x2, HEAP, lsl #32
    // 0xbd7a84: LoadField: r3 = r2->field_b
    //     0xbd7a84: ldur            w3, [x2, #0xb]
    // 0xbd7a88: DecompressPointer r3
    //     0xbd7a88: add             x3, x3, HEAP, lsl #32
    // 0xbd7a8c: cmp             w3, NULL
    // 0xbd7a90: b.eq            #0xbd7e5c
    // 0xbd7a94: LoadField: r2 = r3->field_b
    //     0xbd7a94: ldur            w2, [x3, #0xb]
    // 0xbd7a98: DecompressPointer r2
    //     0xbd7a98: add             x2, x2, HEAP, lsl #32
    // 0xbd7a9c: cmp             w2, NULL
    // 0xbd7aa0: b.ne            #0xbd7aac
    // 0xbd7aa4: r5 = Null
    //     0xbd7aa4: mov             x5, NULL
    // 0xbd7aa8: b               #0xbd7ab8
    // 0xbd7aac: LoadField: r3 = r2->field_7
    //     0xbd7aac: ldur            w3, [x2, #7]
    // 0xbd7ab0: DecompressPointer r3
    //     0xbd7ab0: add             x3, x3, HEAP, lsl #32
    // 0xbd7ab4: mov             x5, x3
    // 0xbd7ab8: ldur            x2, [fp, #-0x30]
    // 0xbd7abc: ldur            x3, [fp, #-0x10]
    // 0xbd7ac0: ldur            x4, [fp, #-0x18]
    // 0xbd7ac4: stur            x5, [fp, #-0x38]
    // 0xbd7ac8: r0 = CustomerResponse()
    //     0xbd7ac8: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xbd7acc: mov             x2, x0
    // 0xbd7ad0: ldur            x0, [fp, #-0x30]
    // 0xbd7ad4: stur            x2, [fp, #-0x50]
    // 0xbd7ad8: StoreField: r2->field_7 = r0
    //     0xbd7ad8: stur            w0, [x2, #7]
    // 0xbd7adc: ldur            x0, [fp, #-0x40]
    // 0xbd7ae0: StoreField: r2->field_b = r0
    //     0xbd7ae0: stur            w0, [x2, #0xb]
    // 0xbd7ae4: ldur            x0, [fp, #-0x38]
    // 0xbd7ae8: StoreField: r2->field_f = r0
    //     0xbd7ae8: stur            w0, [x2, #0xf]
    // 0xbd7aec: ldur            x0, [fp, #-0x10]
    // 0xbd7af0: StoreField: r2->field_13 = r0
    //     0xbd7af0: stur            w0, [x2, #0x13]
    // 0xbd7af4: ldur            x0, [fp, #-0x18]
    // 0xbd7af8: LoadField: r1 = r0->field_b
    //     0xbd7af8: ldur            w1, [x0, #0xb]
    // 0xbd7afc: LoadField: r3 = r0->field_f
    //     0xbd7afc: ldur            w3, [x0, #0xf]
    // 0xbd7b00: DecompressPointer r3
    //     0xbd7b00: add             x3, x3, HEAP, lsl #32
    // 0xbd7b04: LoadField: r4 = r3->field_b
    //     0xbd7b04: ldur            w4, [x3, #0xb]
    // 0xbd7b08: r3 = LoadInt32Instr(r1)
    //     0xbd7b08: sbfx            x3, x1, #1, #0x1f
    // 0xbd7b0c: stur            x3, [fp, #-0x48]
    // 0xbd7b10: r1 = LoadInt32Instr(r4)
    //     0xbd7b10: sbfx            x1, x4, #1, #0x1f
    // 0xbd7b14: cmp             x3, x1
    // 0xbd7b18: b.ne            #0xbd7b24
    // 0xbd7b1c: mov             x1, x0
    // 0xbd7b20: r0 = _growToNextCapacity()
    //     0xbd7b20: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbd7b24: ldur            x4, [fp, #-8]
    // 0xbd7b28: ldur            x3, [fp, #-0x18]
    // 0xbd7b2c: ldur            x2, [fp, #-0x48]
    // 0xbd7b30: add             x0, x2, #1
    // 0xbd7b34: lsl             x1, x0, #1
    // 0xbd7b38: StoreField: r3->field_b = r1
    //     0xbd7b38: stur            w1, [x3, #0xb]
    // 0xbd7b3c: LoadField: r1 = r3->field_f
    //     0xbd7b3c: ldur            w1, [x3, #0xf]
    // 0xbd7b40: DecompressPointer r1
    //     0xbd7b40: add             x1, x1, HEAP, lsl #32
    // 0xbd7b44: ldur            x0, [fp, #-0x50]
    // 0xbd7b48: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbd7b48: add             x25, x1, x2, lsl #2
    //     0xbd7b4c: add             x25, x25, #0xf
    //     0xbd7b50: str             w0, [x25]
    //     0xbd7b54: tbz             w0, #0, #0xbd7b70
    //     0xbd7b58: ldurb           w16, [x1, #-1]
    //     0xbd7b5c: ldurb           w17, [x0, #-1]
    //     0xbd7b60: and             x16, x17, x16, lsr #2
    //     0xbd7b64: tst             x16, HEAP, lsr #32
    //     0xbd7b68: b.eq            #0xbd7b70
    //     0xbd7b6c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd7b70: LoadField: r0 = r4->field_f
    //     0xbd7b70: ldur            w0, [x4, #0xf]
    // 0xbd7b74: DecompressPointer r0
    //     0xbd7b74: add             x0, x0, HEAP, lsl #32
    // 0xbd7b78: LoadField: r1 = r0->field_b
    //     0xbd7b78: ldur            w1, [x0, #0xb]
    // 0xbd7b7c: DecompressPointer r1
    //     0xbd7b7c: add             x1, x1, HEAP, lsl #32
    // 0xbd7b80: cmp             w1, NULL
    // 0xbd7b84: b.eq            #0xbd7e60
    // 0xbd7b88: LoadField: r0 = r1->field_b
    //     0xbd7b88: ldur            w0, [x1, #0xb]
    // 0xbd7b8c: DecompressPointer r0
    //     0xbd7b8c: add             x0, x0, HEAP, lsl #32
    // 0xbd7b90: cmp             w0, NULL
    // 0xbd7b94: b.ne            #0xbd7ba0
    // 0xbd7b98: r5 = Null
    //     0xbd7b98: mov             x5, NULL
    // 0xbd7b9c: b               #0xbd7bac
    // 0xbd7ba0: LoadField: r1 = r0->field_7
    //     0xbd7ba0: ldur            w1, [x0, #7]
    // 0xbd7ba4: DecompressPointer r1
    //     0xbd7ba4: add             x1, x1, HEAP, lsl #32
    // 0xbd7ba8: mov             x5, x1
    // 0xbd7bac: stur            x5, [fp, #-0x40]
    // 0xbd7bb0: cmp             w0, NULL
    // 0xbd7bb4: b.ne            #0xbd7bc0
    // 0xbd7bb8: r6 = Null
    //     0xbd7bb8: mov             x6, NULL
    // 0xbd7bbc: b               #0xbd7bcc
    // 0xbd7bc0: LoadField: r1 = r0->field_b
    //     0xbd7bc0: ldur            w1, [x0, #0xb]
    // 0xbd7bc4: DecompressPointer r1
    //     0xbd7bc4: add             x1, x1, HEAP, lsl #32
    // 0xbd7bc8: mov             x6, x1
    // 0xbd7bcc: stur            x6, [fp, #-0x38]
    // 0xbd7bd0: cmp             w0, NULL
    // 0xbd7bd4: b.ne            #0xbd7be0
    // 0xbd7bd8: r7 = Null
    //     0xbd7bd8: mov             x7, NULL
    // 0xbd7bdc: b               #0xbd7bec
    // 0xbd7be0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd7be0: ldur            w1, [x0, #0x17]
    // 0xbd7be4: DecompressPointer r1
    //     0xbd7be4: add             x1, x1, HEAP, lsl #32
    // 0xbd7be8: mov             x7, x1
    // 0xbd7bec: stur            x7, [fp, #-0x30]
    // 0xbd7bf0: cmp             w0, NULL
    // 0xbd7bf4: b.ne            #0xbd7c00
    // 0xbd7bf8: r0 = Null
    //     0xbd7bf8: mov             x0, NULL
    // 0xbd7bfc: b               #0xbd7c0c
    // 0xbd7c00: LoadField: r1 = r0->field_23
    //     0xbd7c00: ldur            w1, [x0, #0x23]
    // 0xbd7c04: DecompressPointer r1
    //     0xbd7c04: add             x1, x1, HEAP, lsl #32
    // 0xbd7c08: mov             x0, x1
    // 0xbd7c0c: stur            x0, [fp, #-0x10]
    // 0xbd7c10: r1 = Null
    //     0xbd7c10: mov             x1, NULL
    // 0xbd7c14: r2 = 4
    //     0xbd7c14: movz            x2, #0x4
    // 0xbd7c18: r0 = AllocateArray()
    //     0xbd7c18: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd7c1c: stur            x0, [fp, #-0x50]
    // 0xbd7c20: r16 = "₹"
    //     0xbd7c20: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0xbd7c24: ldr             x16, [x16, #0x360]
    // 0xbd7c28: StoreField: r0->field_f = r16
    //     0xbd7c28: stur            w16, [x0, #0xf]
    // 0xbd7c2c: r1 = Function '<anonymous closure>': static.
    //     0xbd7c2c: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0xbd7c30: ldr             x1, [x1, #0x1a0]
    // 0xbd7c34: r2 = Null
    //     0xbd7c34: mov             x2, NULL
    // 0xbd7c38: r0 = AllocateClosure()
    //     0xbd7c38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd7c3c: mov             x3, x0
    // 0xbd7c40: r1 = Null
    //     0xbd7c40: mov             x1, NULL
    // 0xbd7c44: r2 = Null
    //     0xbd7c44: mov             x2, NULL
    // 0xbd7c48: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xbd7c48: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xbd7c4c: r0 = NumberFormat._forPattern()
    //     0xbd7c4c: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xbd7c50: mov             x1, x0
    // 0xbd7c54: ldur            x0, [fp, #-8]
    // 0xbd7c58: LoadField: r2 = r0->field_f
    //     0xbd7c58: ldur            w2, [x0, #0xf]
    // 0xbd7c5c: DecompressPointer r2
    //     0xbd7c5c: add             x2, x2, HEAP, lsl #32
    // 0xbd7c60: LoadField: r3 = r2->field_b
    //     0xbd7c60: ldur            w3, [x2, #0xb]
    // 0xbd7c64: DecompressPointer r3
    //     0xbd7c64: add             x3, x3, HEAP, lsl #32
    // 0xbd7c68: cmp             w3, NULL
    // 0xbd7c6c: b.eq            #0xbd7e64
    // 0xbd7c70: LoadField: r2 = r3->field_b
    //     0xbd7c70: ldur            w2, [x3, #0xb]
    // 0xbd7c74: DecompressPointer r2
    //     0xbd7c74: add             x2, x2, HEAP, lsl #32
    // 0xbd7c78: cmp             w2, NULL
    // 0xbd7c7c: b.ne            #0xbd7c88
    // 0xbd7c80: r2 = Null
    //     0xbd7c80: mov             x2, NULL
    // 0xbd7c84: b               #0xbd7c94
    // 0xbd7c88: LoadField: r3 = r2->field_23
    //     0xbd7c88: ldur            w3, [x2, #0x23]
    // 0xbd7c8c: DecompressPointer r3
    //     0xbd7c8c: add             x3, x3, HEAP, lsl #32
    // 0xbd7c90: mov             x2, x3
    // 0xbd7c94: ldur            x4, [fp, #-0x40]
    // 0xbd7c98: ldur            x5, [fp, #-0x38]
    // 0xbd7c9c: ldur            x6, [fp, #-0x30]
    // 0xbd7ca0: ldur            x7, [fp, #-0x10]
    // 0xbd7ca4: ldur            x3, [fp, #-0x18]
    // 0xbd7ca8: ldur            x8, [fp, #-0x20]
    // 0xbd7cac: r0 = format()
    //     0xbd7cac: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xbd7cb0: ldur            x1, [fp, #-0x50]
    // 0xbd7cb4: ArrayStore: r1[1] = r0  ; List_4
    //     0xbd7cb4: add             x25, x1, #0x13
    //     0xbd7cb8: str             w0, [x25]
    //     0xbd7cbc: tbz             w0, #0, #0xbd7cd8
    //     0xbd7cc0: ldurb           w16, [x1, #-1]
    //     0xbd7cc4: ldurb           w17, [x0, #-1]
    //     0xbd7cc8: and             x16, x17, x16, lsr #2
    //     0xbd7ccc: tst             x16, HEAP, lsr #32
    //     0xbd7cd0: b.eq            #0xbd7cd8
    //     0xbd7cd4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd7cd8: ldur            x16, [fp, #-0x50]
    // 0xbd7cdc: str             x16, [SP]
    // 0xbd7ce0: r0 = _interpolate()
    //     0xbd7ce0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd7ce4: stur            x0, [fp, #-0x50]
    // 0xbd7ce8: r0 = ProductCustomisation()
    //     0xbd7ce8: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xbd7cec: mov             x2, x0
    // 0xbd7cf0: ldur            x0, [fp, #-0x40]
    // 0xbd7cf4: stur            x2, [fp, #-0x58]
    // 0xbd7cf8: StoreField: r2->field_b = r0
    //     0xbd7cf8: stur            w0, [x2, #0xb]
    // 0xbd7cfc: ldur            x0, [fp, #-0x38]
    // 0xbd7d00: StoreField: r2->field_f = r0
    //     0xbd7d00: stur            w0, [x2, #0xf]
    // 0xbd7d04: ldur            x0, [fp, #-0x30]
    // 0xbd7d08: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd7d08: stur            w0, [x2, #0x17]
    // 0xbd7d0c: ldur            x0, [fp, #-0x18]
    // 0xbd7d10: StoreField: r2->field_23 = r0
    //     0xbd7d10: stur            w0, [x2, #0x23]
    // 0xbd7d14: ldur            x0, [fp, #-0x10]
    // 0xbd7d18: StoreField: r2->field_27 = r0
    //     0xbd7d18: stur            w0, [x2, #0x27]
    // 0xbd7d1c: ldur            x0, [fp, #-0x50]
    // 0xbd7d20: StoreField: r2->field_2b = r0
    //     0xbd7d20: stur            w0, [x2, #0x2b]
    // 0xbd7d24: ldur            x1, [fp, #-0x20]
    // 0xbd7d28: r0 = clear()
    //     0xbd7d28: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xbd7d2c: ldur            x0, [fp, #-0x20]
    // 0xbd7d30: LoadField: r1 = r0->field_b
    //     0xbd7d30: ldur            w1, [x0, #0xb]
    // 0xbd7d34: LoadField: r2 = r0->field_f
    //     0xbd7d34: ldur            w2, [x0, #0xf]
    // 0xbd7d38: DecompressPointer r2
    //     0xbd7d38: add             x2, x2, HEAP, lsl #32
    // 0xbd7d3c: LoadField: r3 = r2->field_b
    //     0xbd7d3c: ldur            w3, [x2, #0xb]
    // 0xbd7d40: r2 = LoadInt32Instr(r1)
    //     0xbd7d40: sbfx            x2, x1, #1, #0x1f
    // 0xbd7d44: stur            x2, [fp, #-0x48]
    // 0xbd7d48: r1 = LoadInt32Instr(r3)
    //     0xbd7d48: sbfx            x1, x3, #1, #0x1f
    // 0xbd7d4c: cmp             x2, x1
    // 0xbd7d50: b.ne            #0xbd7d5c
    // 0xbd7d54: mov             x1, x0
    // 0xbd7d58: r0 = _growToNextCapacity()
    //     0xbd7d58: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbd7d5c: ldur            x4, [fp, #-8]
    // 0xbd7d60: ldur            x2, [fp, #-0x20]
    // 0xbd7d64: ldur            x3, [fp, #-0x48]
    // 0xbd7d68: add             x0, x3, #1
    // 0xbd7d6c: lsl             x1, x0, #1
    // 0xbd7d70: StoreField: r2->field_b = r1
    //     0xbd7d70: stur            w1, [x2, #0xb]
    // 0xbd7d74: LoadField: r1 = r2->field_f
    //     0xbd7d74: ldur            w1, [x2, #0xf]
    // 0xbd7d78: DecompressPointer r1
    //     0xbd7d78: add             x1, x1, HEAP, lsl #32
    // 0xbd7d7c: ldur            x0, [fp, #-0x58]
    // 0xbd7d80: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbd7d80: add             x25, x1, x3, lsl #2
    //     0xbd7d84: add             x25, x25, #0xf
    //     0xbd7d88: str             w0, [x25]
    //     0xbd7d8c: tbz             w0, #0, #0xbd7da8
    //     0xbd7d90: ldurb           w16, [x1, #-1]
    //     0xbd7d94: ldurb           w17, [x0, #-1]
    //     0xbd7d98: and             x16, x17, x16, lsr #2
    //     0xbd7d9c: tst             x16, HEAP, lsr #32
    //     0xbd7da0: b.eq            #0xbd7da8
    //     0xbd7da4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd7da8: LoadField: r0 = r4->field_f
    //     0xbd7da8: ldur            w0, [x4, #0xf]
    // 0xbd7dac: DecompressPointer r0
    //     0xbd7dac: add             x0, x0, HEAP, lsl #32
    // 0xbd7db0: LoadField: r1 = r0->field_b
    //     0xbd7db0: ldur            w1, [x0, #0xb]
    // 0xbd7db4: DecompressPointer r1
    //     0xbd7db4: add             x1, x1, HEAP, lsl #32
    // 0xbd7db8: cmp             w1, NULL
    // 0xbd7dbc: b.eq            #0xbd7e68
    // 0xbd7dc0: LoadField: r0 = r1->field_b
    //     0xbd7dc0: ldur            w0, [x1, #0xb]
    // 0xbd7dc4: DecompressPointer r0
    //     0xbd7dc4: add             x0, x0, HEAP, lsl #32
    // 0xbd7dc8: cmp             w0, NULL
    // 0xbd7dcc: b.ne            #0xbd7dd8
    // 0xbd7dd0: r0 = Null
    //     0xbd7dd0: mov             x0, NULL
    // 0xbd7dd4: b               #0xbd7de4
    // 0xbd7dd8: LoadField: r3 = r0->field_23
    //     0xbd7dd8: ldur            w3, [x0, #0x23]
    // 0xbd7ddc: DecompressPointer r3
    //     0xbd7ddc: add             x3, x3, HEAP, lsl #32
    // 0xbd7de0: mov             x0, x3
    // 0xbd7de4: LoadField: r3 = r1->field_13
    //     0xbd7de4: ldur            w3, [x1, #0x13]
    // 0xbd7de8: DecompressPointer r3
    //     0xbd7de8: add             x3, x3, HEAP, lsl #32
    // 0xbd7dec: stp             x0, x3, [SP, #8]
    // 0xbd7df0: str             x2, [SP]
    // 0xbd7df4: r4 = 0
    //     0xbd7df4: movz            x4, #0
    // 0xbd7df8: ldr             x0, [SP, #0x10]
    // 0xbd7dfc: r16 = UnlinkedCall_0x613b5c
    //     0xbd7dfc: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a598] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd7e00: add             x16, x16, #0x598
    // 0xbd7e04: ldp             x5, lr, [x16]
    // 0xbd7e08: blr             lr
    // 0xbd7e0c: ldur            x1, [fp, #-8]
    // 0xbd7e10: LoadField: r2 = r1->field_f
    //     0xbd7e10: ldur            w2, [x1, #0xf]
    // 0xbd7e14: DecompressPointer r2
    //     0xbd7e14: add             x2, x2, HEAP, lsl #32
    // 0xbd7e18: ldur            x0, [fp, #-0x28]
    // 0xbd7e1c: StoreField: r2->field_13 = r0
    //     0xbd7e1c: stur            w0, [x2, #0x13]
    //     0xbd7e20: ldurb           w16, [x2, #-1]
    //     0xbd7e24: ldurb           w17, [x0, #-1]
    //     0xbd7e28: and             x16, x17, x16, lsr #2
    //     0xbd7e2c: tst             x16, HEAP, lsr #32
    //     0xbd7e30: b.eq            #0xbd7e38
    //     0xbd7e34: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbd7e38: r0 = Null
    //     0xbd7e38: mov             x0, NULL
    // 0xbd7e3c: LeaveFrame
    //     0xbd7e3c: mov             SP, fp
    //     0xbd7e40: ldp             fp, lr, [SP], #0x10
    // 0xbd7e44: ret
    //     0xbd7e44: ret             
    // 0xbd7e48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7e48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7e4c: b               #0xbd78a4
    // 0xbd7e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd7e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7e54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd7e58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7e58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd7e5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7e5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd7e60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7e60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd7e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7e64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd7e68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd7e68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xbd7e6c, size: 0x64
    // 0xbd7e6c: EnterFrame
    //     0xbd7e6c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd7e70: mov             fp, SP
    // 0xbd7e74: AllocStack(0x8)
    //     0xbd7e74: sub             SP, SP, #8
    // 0xbd7e78: SetupParameters()
    //     0xbd7e78: ldr             x0, [fp, #0x18]
    //     0xbd7e7c: ldur            w1, [x0, #0x17]
    //     0xbd7e80: add             x1, x1, HEAP, lsl #32
    // 0xbd7e84: CheckStackOverflow
    //     0xbd7e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd7e88: cmp             SP, x16
    //     0xbd7e8c: b.ls            #0xbd7ec8
    // 0xbd7e90: LoadField: r0 = r1->field_f
    //     0xbd7e90: ldur            w0, [x1, #0xf]
    // 0xbd7e94: DecompressPointer r0
    //     0xbd7e94: add             x0, x0, HEAP, lsl #32
    // 0xbd7e98: stur            x0, [fp, #-8]
    // 0xbd7e9c: r1 = Function '<anonymous closure>':.
    //     0xbd7e9c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a538] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xbd7ea0: ldr             x1, [x1, #0x538]
    // 0xbd7ea4: r2 = Null
    //     0xbd7ea4: mov             x2, NULL
    // 0xbd7ea8: r0 = AllocateClosure()
    //     0xbd7ea8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd7eac: ldur            x1, [fp, #-8]
    // 0xbd7eb0: mov             x2, x0
    // 0xbd7eb4: r0 = setState()
    //     0xbd7eb4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbd7eb8: r0 = Null
    //     0xbd7eb8: mov             x0, NULL
    // 0xbd7ebc: LeaveFrame
    //     0xbd7ebc: mov             SP, fp
    //     0xbd7ec0: ldp             fp, lr, [SP], #0x10
    // 0xbd7ec4: ret
    //     0xbd7ec4: ret             
    // 0xbd7ec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd7ec8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd7ecc: b               #0xbd7e90
  }
}

// class id: 4005, size: 0x1c, field offset: 0xc
//   const constructor, 
class CameraPicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80794, size: 0x24
    // 0xc80794: EnterFrame
    //     0xc80794: stp             fp, lr, [SP, #-0x10]!
    //     0xc80798: mov             fp, SP
    // 0xc8079c: mov             x0, x1
    // 0xc807a0: r1 = <CameraPicker>
    //     0xc807a0: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c48] TypeArguments: <CameraPicker>
    //     0xc807a4: ldr             x1, [x1, #0xc48]
    // 0xc807a8: r0 = _CameraPickerState()
    //     0xc807a8: bl              #0xc807b8  ; Allocate_CameraPickerStateStub -> _CameraPickerState (size=0x18)
    // 0xc807ac: LeaveFrame
    //     0xc807ac: mov             SP, fp
    //     0xc807b0: ldp             fp, lr, [SP], #0x10
    // 0xc807b4: ret
    //     0xc807b4: ret             
  }
}
