// lib: , url: package:customer_app/app/presentation/custom_widgets/product_detail/sale_event_widget.dart

// class id: 1049086, size: 0x8
class :: {
}

// class id: 3573, size: 0x14, field offset: 0x14
class _SaleEventWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0x9adf2c, size: 0x19c
    // 0x9adf2c: EnterFrame
    //     0x9adf2c: stp             fp, lr, [SP, #-0x10]!
    //     0x9adf30: mov             fp, SP
    // 0x9adf34: AllocStack(0x18)
    //     0x9adf34: sub             SP, SP, #0x18
    // 0x9adf38: SetupParameters(_SaleEventWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x9adf38: stur            x1, [fp, #-8]
    // 0x9adf3c: CheckStackOverflow
    //     0x9adf3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9adf40: cmp             SP, x16
    //     0x9adf44: b.ls            #0x9ae0bc
    // 0x9adf48: r1 = 1
    //     0x9adf48: movz            x1, #0x1
    // 0x9adf4c: r0 = AllocateContext()
    //     0x9adf4c: bl              #0x16f6108  ; AllocateContextStub
    // 0x9adf50: mov             x1, x0
    // 0x9adf54: ldur            x0, [fp, #-8]
    // 0x9adf58: stur            x1, [fp, #-0x10]
    // 0x9adf5c: StoreField: r1->field_f = r0
    //     0x9adf5c: stur            w0, [x1, #0xf]
    // 0x9adf60: LoadField: r2 = r0->field_b
    //     0x9adf60: ldur            w2, [x0, #0xb]
    // 0x9adf64: DecompressPointer r2
    //     0x9adf64: add             x2, x2, HEAP, lsl #32
    // 0x9adf68: cmp             w2, NULL
    // 0x9adf6c: b.eq            #0x9ae0c4
    // 0x9adf70: LoadField: r0 = r2->field_b
    //     0x9adf70: ldur            w0, [x2, #0xb]
    // 0x9adf74: DecompressPointer r0
    //     0x9adf74: add             x0, x0, HEAP, lsl #32
    // 0x9adf78: LoadField: r2 = r0->field_13
    //     0x9adf78: ldur            w2, [x0, #0x13]
    // 0x9adf7c: DecompressPointer r2
    //     0x9adf7c: add             x2, x2, HEAP, lsl #32
    // 0x9adf80: cmp             w2, NULL
    // 0x9adf84: b.ne            #0x9adfc0
    // 0x9adf88: r0 = _getCurrentMicros()
    //     0x9adf88: bl              #0x68ccd0  ; [dart:core] DateTime::_getCurrentMicros
    // 0x9adf8c: r1 = LoadInt32Instr(r0)
    //     0x9adf8c: sbfx            x1, x0, #1, #0x1f
    //     0x9adf90: tbz             w0, #0, #0x9adf98
    //     0x9adf94: ldur            x1, [x0, #7]
    // 0x9adf98: tbz             x1, #0x3f, #0x9adfa4
    // 0x9adf9c: r2 = 999
    //     0x9adf9c: movz            x2, #0x3e7
    // 0x9adfa0: b               #0x9adfa8
    // 0x9adfa4: r2 = 0
    //     0x9adfa4: movz            x2, #0
    // 0x9adfa8: r0 = 1000
    //     0x9adfa8: movz            x0, #0x3e8
    // 0x9adfac: sub             x3, x1, x2
    // 0x9adfb0: sdiv            x1, x3, x0
    // 0x9adfb4: r17 = 30000
    //     0x9adfb4: movz            x17, #0x7530
    // 0x9adfb8: add             x0, x1, x17
    // 0x9adfbc: b               #0x9adfcc
    // 0x9adfc0: r0 = LoadInt32Instr(r2)
    //     0x9adfc0: sbfx            x0, x2, #1, #0x1f
    //     0x9adfc4: tbz             w2, #0, #0x9adfcc
    //     0x9adfc8: ldur            x0, [x2, #7]
    // 0x9adfcc: ldur            x2, [fp, #-0x10]
    // 0x9adfd0: stur            x0, [fp, #-0x18]
    // 0x9adfd4: r1 = Function '<anonymous closure>':.
    //     0x9adfd4: add             x1, PP, #0x5b, lsl #12  ; [pp+0x5bb00] AnonymousClosure: (0x9ae0ec), in [package:customer_app/app/presentation/custom_widgets/product_detail/sale_event_widget.dart] _SaleEventWidgetState::build (0x9adf2c)
    //     0x9adfd8: ldr             x1, [x1, #0xb00]
    // 0x9adfdc: r0 = AllocateClosure()
    //     0x9adfdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x9adfe0: stur            x0, [fp, #-8]
    // 0x9adfe4: r0 = CountdownTimer()
    //     0x9adfe4: bl              #0x990638  ; AllocateCountdownTimerStub -> CountdownTimer (size=0x20)
    // 0x9adfe8: mov             x3, x0
    // 0x9adfec: ldur            x0, [fp, #-8]
    // 0x9adff0: stur            x3, [fp, #-0x10]
    // 0x9adff4: StoreField: r3->field_b = r0
    //     0x9adff4: stur            w0, [x3, #0xb]
    // 0x9adff8: ldur            x0, [fp, #-0x18]
    // 0x9adffc: ArrayStore: r3[0] = r0  ; List_8
    //     0x9adffc: stur            x0, [x3, #0x17]
    // 0x9ae000: r1 = Null
    //     0x9ae000: mov             x1, NULL
    // 0x9ae004: r2 = 2
    //     0x9ae004: movz            x2, #0x2
    // 0x9ae008: r0 = AllocateArray()
    //     0x9ae008: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ae00c: mov             x2, x0
    // 0x9ae010: ldur            x0, [fp, #-0x10]
    // 0x9ae014: stur            x2, [fp, #-8]
    // 0x9ae018: StoreField: r2->field_f = r0
    //     0x9ae018: stur            w0, [x2, #0xf]
    // 0x9ae01c: r1 = <Widget>
    //     0x9ae01c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ae020: r0 = AllocateGrowableArray()
    //     0x9ae020: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9ae024: mov             x1, x0
    // 0x9ae028: ldur            x0, [fp, #-8]
    // 0x9ae02c: stur            x1, [fp, #-0x10]
    // 0x9ae030: StoreField: r1->field_f = r0
    //     0x9ae030: stur            w0, [x1, #0xf]
    // 0x9ae034: r0 = 2
    //     0x9ae034: movz            x0, #0x2
    // 0x9ae038: StoreField: r1->field_b = r0
    //     0x9ae038: stur            w0, [x1, #0xb]
    // 0x9ae03c: r0 = Column()
    //     0x9ae03c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9ae040: mov             x1, x0
    // 0x9ae044: r0 = Instance_Axis
    //     0x9ae044: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9ae048: stur            x1, [fp, #-8]
    // 0x9ae04c: StoreField: r1->field_f = r0
    //     0x9ae04c: stur            w0, [x1, #0xf]
    // 0x9ae050: r0 = Instance_MainAxisAlignment
    //     0x9ae050: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x9ae054: ldr             x0, [x0, #0xa08]
    // 0x9ae058: StoreField: r1->field_13 = r0
    //     0x9ae058: stur            w0, [x1, #0x13]
    // 0x9ae05c: r0 = Instance_MainAxisSize
    //     0x9ae05c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9ae060: ldr             x0, [x0, #0xa10]
    // 0x9ae064: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ae064: stur            w0, [x1, #0x17]
    // 0x9ae068: r0 = Instance_CrossAxisAlignment
    //     0x9ae068: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9ae06c: ldr             x0, [x0, #0xa18]
    // 0x9ae070: StoreField: r1->field_1b = r0
    //     0x9ae070: stur            w0, [x1, #0x1b]
    // 0x9ae074: r0 = Instance_VerticalDirection
    //     0x9ae074: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9ae078: ldr             x0, [x0, #0xa20]
    // 0x9ae07c: StoreField: r1->field_23 = r0
    //     0x9ae07c: stur            w0, [x1, #0x23]
    // 0x9ae080: r0 = Instance_Clip
    //     0x9ae080: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9ae084: ldr             x0, [x0, #0x38]
    // 0x9ae088: StoreField: r1->field_2b = r0
    //     0x9ae088: stur            w0, [x1, #0x2b]
    // 0x9ae08c: StoreField: r1->field_2f = rZR
    //     0x9ae08c: stur            xzr, [x1, #0x2f]
    // 0x9ae090: ldur            x0, [fp, #-0x10]
    // 0x9ae094: StoreField: r1->field_b = r0
    //     0x9ae094: stur            w0, [x1, #0xb]
    // 0x9ae098: r0 = Padding()
    //     0x9ae098: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9ae09c: r1 = Instance_EdgeInsets
    //     0x9ae09c: add             x1, PP, #0x32, lsl #12  ; [pp+0x32110] Obj!EdgeInsets@d57561
    //     0x9ae0a0: ldr             x1, [x1, #0x110]
    // 0x9ae0a4: StoreField: r0->field_f = r1
    //     0x9ae0a4: stur            w1, [x0, #0xf]
    // 0x9ae0a8: ldur            x1, [fp, #-8]
    // 0x9ae0ac: StoreField: r0->field_b = r1
    //     0x9ae0ac: stur            w1, [x0, #0xb]
    // 0x9ae0b0: LeaveFrame
    //     0x9ae0b0: mov             SP, fp
    //     0x9ae0b4: ldp             fp, lr, [SP], #0x10
    // 0x9ae0b8: ret
    //     0x9ae0b8: ret             
    // 0x9ae0bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ae0bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ae0c0: b               #0x9adf48
    // 0x9ae0c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ae0c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, CurrentRemainingTime?) {
    // ** addr: 0x9ae0ec, size: 0x78
    // 0x9ae0ec: EnterFrame
    //     0x9ae0ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9ae0f0: mov             fp, SP
    // 0x9ae0f4: AllocStack(0x8)
    //     0x9ae0f4: sub             SP, SP, #8
    // 0x9ae0f8: SetupParameters()
    //     0x9ae0f8: ldr             x0, [fp, #0x20]
    //     0x9ae0fc: ldur            w1, [x0, #0x17]
    //     0x9ae100: add             x1, x1, HEAP, lsl #32
    // 0x9ae104: CheckStackOverflow
    //     0x9ae104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ae108: cmp             SP, x16
    //     0x9ae10c: b.ls            #0x9ae15c
    // 0x9ae110: ldr             x2, [fp, #0x10]
    // 0x9ae114: cmp             w2, NULL
    // 0x9ae118: b.ne            #0x9ae140
    // 0x9ae11c: r0 = Container()
    //     0x9ae11c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9ae120: mov             x1, x0
    // 0x9ae124: stur            x0, [fp, #-8]
    // 0x9ae128: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9ae128: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9ae12c: r0 = Container()
    //     0x9ae12c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9ae130: ldur            x0, [fp, #-8]
    // 0x9ae134: LeaveFrame
    //     0x9ae134: mov             SP, fp
    //     0x9ae138: ldp             fp, lr, [SP], #0x10
    // 0x9ae13c: ret
    //     0x9ae13c: ret             
    // 0x9ae140: LoadField: r0 = r1->field_f
    //     0x9ae140: ldur            w0, [x1, #0xf]
    // 0x9ae144: DecompressPointer r0
    //     0x9ae144: add             x0, x0, HEAP, lsl #32
    // 0x9ae148: mov             x1, x0
    // 0x9ae14c: r0 = timerLineThemeCard()
    //     0x9ae14c: bl              #0x9ae164  ; [package:customer_app/app/presentation/custom_widgets/product_detail/sale_event_widget.dart] _SaleEventWidgetState::timerLineThemeCard
    // 0x9ae150: LeaveFrame
    //     0x9ae150: mov             SP, fp
    //     0x9ae154: ldp             fp, lr, [SP], #0x10
    // 0x9ae158: ret
    //     0x9ae158: ret             
    // 0x9ae15c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ae15c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ae160: b               #0x9ae110
  }
  _ timerLineThemeCard(/* No info */) {
    // ** addr: 0x9ae164, size: 0x15a8
    // 0x9ae164: EnterFrame
    //     0x9ae164: stp             fp, lr, [SP, #-0x10]!
    //     0x9ae168: mov             fp, SP
    // 0x9ae16c: AllocStack(0xa0)
    //     0x9ae16c: sub             SP, SP, #0xa0
    // 0x9ae170: SetupParameters(_SaleEventWidgetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x9ae170: stur            x1, [fp, #-8]
    //     0x9ae174: stur            x2, [fp, #-0x10]
    // 0x9ae178: CheckStackOverflow
    //     0x9ae178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ae17c: cmp             SP, x16
    //     0x9ae180: b.ls            #0x9af608
    // 0x9ae184: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x9ae184: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9ae188: ldr             x0, [x0, #0x1c80]
    //     0x9ae18c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9ae190: cmp             w0, w16
    //     0x9ae194: b.ne            #0x9ae1a0
    //     0x9ae198: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x9ae19c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9ae1a0: r0 = GetNavigation.size()
    //     0x9ae1a0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x9ae1a4: LoadField: d0 = r0->field_7
    //     0x9ae1a4: ldur            d0, [x0, #7]
    // 0x9ae1a8: ldur            x0, [fp, #-8]
    // 0x9ae1ac: stur            d0, [fp, #-0x68]
    // 0x9ae1b0: LoadField: r1 = r0->field_b
    //     0x9ae1b0: ldur            w1, [x0, #0xb]
    // 0x9ae1b4: DecompressPointer r1
    //     0x9ae1b4: add             x1, x1, HEAP, lsl #32
    // 0x9ae1b8: cmp             w1, NULL
    // 0x9ae1bc: b.eq            #0x9af610
    // 0x9ae1c0: LoadField: r2 = r1->field_f
    //     0x9ae1c0: ldur            w2, [x1, #0xf]
    // 0x9ae1c4: DecompressPointer r2
    //     0x9ae1c4: add             x2, x2, HEAP, lsl #32
    // 0x9ae1c8: stur            x2, [fp, #-0x18]
    // 0x9ae1cc: LoadField: r3 = r1->field_b
    //     0x9ae1cc: ldur            w3, [x1, #0xb]
    // 0x9ae1d0: DecompressPointer r3
    //     0x9ae1d0: add             x3, x3, HEAP, lsl #32
    // 0x9ae1d4: LoadField: r1 = r3->field_2b
    //     0x9ae1d4: ldur            w1, [x3, #0x2b]
    // 0x9ae1d8: DecompressPointer r1
    //     0x9ae1d8: add             x1, x1, HEAP, lsl #32
    // 0x9ae1dc: str             x1, [SP]
    // 0x9ae1e0: r0 = _interpolateSingle()
    //     0x9ae1e0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x9ae1e4: r1 = <NetworkImage>
    //     0x9ae1e4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ba8] TypeArguments: <NetworkImage>
    //     0x9ae1e8: ldr             x1, [x1, #0xba8]
    // 0x9ae1ec: stur            x0, [fp, #-0x20]
    // 0x9ae1f0: r0 = NetworkImage()
    //     0x9ae1f0: bl              #0x80226c  ; AllocateNetworkImageStub -> NetworkImage (size=0x1c)
    // 0x9ae1f4: mov             x1, x0
    // 0x9ae1f8: ldur            x0, [fp, #-0x20]
    // 0x9ae1fc: stur            x1, [fp, #-0x28]
    // 0x9ae200: StoreField: r1->field_b = r0
    //     0x9ae200: stur            w0, [x1, #0xb]
    // 0x9ae204: d0 = 1.000000
    //     0x9ae204: fmov            d0, #1.00000000
    // 0x9ae208: StoreField: r1->field_f = d0
    //     0x9ae208: stur            d0, [x1, #0xf]
    // 0x9ae20c: r0 = DecorationImage()
    //     0x9ae20c: bl              #0x83fce0  ; AllocateDecorationImageStub -> DecorationImage (size=0x44)
    // 0x9ae210: mov             x1, x0
    // 0x9ae214: ldur            x0, [fp, #-0x28]
    // 0x9ae218: stur            x1, [fp, #-0x20]
    // 0x9ae21c: StoreField: r1->field_7 = r0
    //     0x9ae21c: stur            w0, [x1, #7]
    // 0x9ae220: r0 = Instance_BoxFit
    //     0x9ae220: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bb08] Obj!BoxFit@d738c1
    //     0x9ae224: ldr             x0, [x0, #0xb08]
    // 0x9ae228: StoreField: r1->field_13 = r0
    //     0x9ae228: stur            w0, [x1, #0x13]
    // 0x9ae22c: r2 = Instance_Alignment
    //     0x9ae22c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9ae230: ldr             x2, [x2, #0xb10]
    // 0x9ae234: ArrayStore: r1[0] = r2  ; List_4
    //     0x9ae234: stur            w2, [x1, #0x17]
    // 0x9ae238: r3 = Instance_ImageRepeat
    //     0x9ae238: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eb00] Obj!ImageRepeat@d73821
    //     0x9ae23c: ldr             x3, [x3, #0xb00]
    // 0x9ae240: StoreField: r1->field_1f = r3
    //     0x9ae240: stur            w3, [x1, #0x1f]
    // 0x9ae244: r4 = false
    //     0x9ae244: add             x4, NULL, #0x30  ; false
    // 0x9ae248: StoreField: r1->field_23 = r4
    //     0x9ae248: stur            w4, [x1, #0x23]
    // 0x9ae24c: d0 = 1.000000
    //     0x9ae24c: fmov            d0, #1.00000000
    // 0x9ae250: StoreField: r1->field_27 = d0
    //     0x9ae250: stur            d0, [x1, #0x27]
    // 0x9ae254: StoreField: r1->field_2f = d0
    //     0x9ae254: stur            d0, [x1, #0x2f]
    // 0x9ae258: r5 = Instance_FilterQuality
    //     0x9ae258: add             x5, PP, #0x33, lsl #12  ; [pp+0x33a98] Obj!FilterQuality@d77121
    //     0x9ae25c: ldr             x5, [x5, #0xa98]
    // 0x9ae260: StoreField: r1->field_37 = r5
    //     0x9ae260: stur            w5, [x1, #0x37]
    // 0x9ae264: StoreField: r1->field_3b = r4
    //     0x9ae264: stur            w4, [x1, #0x3b]
    // 0x9ae268: StoreField: r1->field_3f = r4
    //     0x9ae268: stur            w4, [x1, #0x3f]
    // 0x9ae26c: r0 = BoxDecoration()
    //     0x9ae26c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9ae270: mov             x2, x0
    // 0x9ae274: ldur            x0, [fp, #-0x20]
    // 0x9ae278: stur            x2, [fp, #-0x28]
    // 0x9ae27c: StoreField: r2->field_b = r0
    //     0x9ae27c: stur            w0, [x2, #0xb]
    // 0x9ae280: ldur            x0, [fp, #-0x18]
    // 0x9ae284: StoreField: r2->field_13 = r0
    //     0x9ae284: stur            w0, [x2, #0x13]
    // 0x9ae288: r0 = Instance_BoxShape
    //     0x9ae288: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9ae28c: ldr             x0, [x0, #0x80]
    // 0x9ae290: StoreField: r2->field_23 = r0
    //     0x9ae290: stur            w0, [x2, #0x23]
    // 0x9ae294: ldur            x3, [fp, #-8]
    // 0x9ae298: LoadField: r1 = r3->field_b
    //     0x9ae298: ldur            w1, [x3, #0xb]
    // 0x9ae29c: DecompressPointer r1
    //     0x9ae29c: add             x1, x1, HEAP, lsl #32
    // 0x9ae2a0: cmp             w1, NULL
    // 0x9ae2a4: b.eq            #0x9af614
    // 0x9ae2a8: LoadField: r4 = r1->field_b
    //     0x9ae2a8: ldur            w4, [x1, #0xb]
    // 0x9ae2ac: DecompressPointer r4
    //     0x9ae2ac: add             x4, x4, HEAP, lsl #32
    // 0x9ae2b0: LoadField: r1 = r4->field_1b
    //     0x9ae2b0: ldur            w1, [x4, #0x1b]
    // 0x9ae2b4: DecompressPointer r1
    //     0x9ae2b4: add             x1, x1, HEAP, lsl #32
    // 0x9ae2b8: cmp             w1, NULL
    // 0x9ae2bc: b.ne            #0x9ae2c8
    // 0x9ae2c0: r4 = ""
    //     0x9ae2c0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ae2c4: b               #0x9ae2cc
    // 0x9ae2c8: mov             x4, x1
    // 0x9ae2cc: stur            x4, [fp, #-0x18]
    // 0x9ae2d0: LoadField: r1 = r3->field_f
    //     0x9ae2d0: ldur            w1, [x3, #0xf]
    // 0x9ae2d4: DecompressPointer r1
    //     0x9ae2d4: add             x1, x1, HEAP, lsl #32
    // 0x9ae2d8: cmp             w1, NULL
    // 0x9ae2dc: b.eq            #0x9af618
    // 0x9ae2e0: r0 = of()
    //     0x9ae2e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ae2e4: LoadField: r1 = r0->field_87
    //     0x9ae2e4: ldur            w1, [x0, #0x87]
    // 0x9ae2e8: DecompressPointer r1
    //     0x9ae2e8: add             x1, x1, HEAP, lsl #32
    // 0x9ae2ec: LoadField: r0 = r1->field_7
    //     0x9ae2ec: ldur            w0, [x1, #7]
    // 0x9ae2f0: DecompressPointer r0
    //     0x9ae2f0: add             x0, x0, HEAP, lsl #32
    // 0x9ae2f4: ldur            x2, [fp, #-8]
    // 0x9ae2f8: stur            x0, [fp, #-0x20]
    // 0x9ae2fc: LoadField: r1 = r2->field_b
    //     0x9ae2fc: ldur            w1, [x2, #0xb]
    // 0x9ae300: DecompressPointer r1
    //     0x9ae300: add             x1, x1, HEAP, lsl #32
    // 0x9ae304: cmp             w1, NULL
    // 0x9ae308: b.eq            #0x9af61c
    // 0x9ae30c: LoadField: r3 = r1->field_b
    //     0x9ae30c: ldur            w3, [x1, #0xb]
    // 0x9ae310: DecompressPointer r3
    //     0x9ae310: add             x3, x3, HEAP, lsl #32
    // 0x9ae314: LoadField: r1 = r3->field_b
    //     0x9ae314: ldur            w1, [x3, #0xb]
    // 0x9ae318: DecompressPointer r1
    //     0x9ae318: add             x1, x1, HEAP, lsl #32
    // 0x9ae31c: cmp             w1, NULL
    // 0x9ae320: b.ne            #0x9ae330
    // 0x9ae324: mov             x0, x2
    // 0x9ae328: r1 = Null
    //     0x9ae328: mov             x1, NULL
    // 0x9ae32c: b               #0x9ae33c
    // 0x9ae330: r0 = ColorExtension.toColor()
    //     0x9ae330: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9ae334: mov             x1, x0
    // 0x9ae338: ldur            x0, [fp, #-8]
    // 0x9ae33c: ldur            x2, [fp, #-0x18]
    // 0x9ae340: ldur            d0, [fp, #-0x68]
    // 0x9ae344: r16 = 12.000000
    //     0x9ae344: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9ae348: ldr             x16, [x16, #0x9e8]
    // 0x9ae34c: stp             x16, x1, [SP]
    // 0x9ae350: ldur            x1, [fp, #-0x20]
    // 0x9ae354: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9ae354: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9ae358: ldr             x4, [x4, #0x9b8]
    // 0x9ae35c: r0 = copyWith()
    //     0x9ae35c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ae360: stur            x0, [fp, #-0x20]
    // 0x9ae364: r0 = Text()
    //     0x9ae364: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ae368: mov             x1, x0
    // 0x9ae36c: ldur            x0, [fp, #-0x18]
    // 0x9ae370: stur            x1, [fp, #-0x30]
    // 0x9ae374: StoreField: r1->field_b = r0
    //     0x9ae374: stur            w0, [x1, #0xb]
    // 0x9ae378: ldur            x0, [fp, #-0x20]
    // 0x9ae37c: StoreField: r1->field_13 = r0
    //     0x9ae37c: stur            w0, [x1, #0x13]
    // 0x9ae380: r0 = Align()
    //     0x9ae380: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x9ae384: mov             x1, x0
    // 0x9ae388: r0 = Instance_Alignment
    //     0x9ae388: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9ae38c: ldr             x0, [x0, #0xb10]
    // 0x9ae390: stur            x1, [fp, #-0x20]
    // 0x9ae394: StoreField: r1->field_f = r0
    //     0x9ae394: stur            w0, [x1, #0xf]
    // 0x9ae398: ldur            x2, [fp, #-0x30]
    // 0x9ae39c: StoreField: r1->field_b = r2
    //     0x9ae39c: stur            w2, [x1, #0xb]
    // 0x9ae3a0: ldur            d0, [fp, #-0x68]
    // 0x9ae3a4: r2 = inline_Allocate_Double()
    //     0x9ae3a4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x9ae3a8: add             x2, x2, #0x10
    //     0x9ae3ac: cmp             x3, x2
    //     0x9ae3b0: b.ls            #0x9af620
    //     0x9ae3b4: str             x2, [THR, #0x50]  ; THR::top
    //     0x9ae3b8: sub             x2, x2, #0xf
    //     0x9ae3bc: movz            x3, #0xe15c
    //     0x9ae3c0: movk            x3, #0x3, lsl #16
    //     0x9ae3c4: stur            x3, [x2, #-1]
    // 0x9ae3c8: StoreField: r2->field_7 = d0
    //     0x9ae3c8: stur            d0, [x2, #7]
    // 0x9ae3cc: stur            x2, [fp, #-0x18]
    // 0x9ae3d0: r0 = Container()
    //     0x9ae3d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9ae3d4: stur            x0, [fp, #-0x30]
    // 0x9ae3d8: r16 = Instance_Alignment
    //     0x9ae3d8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9ae3dc: ldr             x16, [x16, #0xb10]
    // 0x9ae3e0: ldur            lr, [fp, #-0x18]
    // 0x9ae3e4: stp             lr, x16, [SP, #0x18]
    // 0x9ae3e8: r16 = 50.000000
    //     0x9ae3e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x9ae3ec: ldr             x16, [x16, #0xa90]
    // 0x9ae3f0: ldur            lr, [fp, #-0x28]
    // 0x9ae3f4: stp             lr, x16, [SP, #8]
    // 0x9ae3f8: ldur            x16, [fp, #-0x20]
    // 0x9ae3fc: str             x16, [SP]
    // 0x9ae400: mov             x1, x0
    // 0x9ae404: r4 = const [0, 0x6, 0x5, 0x1, alignment, 0x1, child, 0x5, decoration, 0x4, height, 0x3, width, 0x2, null]
    //     0x9ae404: add             x4, PP, #0x5b, lsl #12  ; [pp+0x5bb10] List(15) [0, 0x6, 0x5, 0x1, "alignment", 0x1, "child", 0x5, "decoration", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0x9ae408: ldr             x4, [x4, #0xb10]
    // 0x9ae40c: r0 = Container()
    //     0x9ae40c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9ae410: ldur            x0, [fp, #-8]
    // 0x9ae414: LoadField: r1 = r0->field_b
    //     0x9ae414: ldur            w1, [x0, #0xb]
    // 0x9ae418: DecompressPointer r1
    //     0x9ae418: add             x1, x1, HEAP, lsl #32
    // 0x9ae41c: cmp             w1, NULL
    // 0x9ae420: b.eq            #0x9af63c
    // 0x9ae424: LoadField: r2 = r1->field_f
    //     0x9ae424: ldur            w2, [x1, #0xf]
    // 0x9ae428: DecompressPointer r2
    //     0x9ae428: add             x2, x2, HEAP, lsl #32
    // 0x9ae42c: stur            x2, [fp, #-0x18]
    // 0x9ae430: LoadField: r3 = r1->field_b
    //     0x9ae430: ldur            w3, [x1, #0xb]
    // 0x9ae434: DecompressPointer r3
    //     0x9ae434: add             x3, x3, HEAP, lsl #32
    // 0x9ae438: LoadField: r1 = r3->field_3b
    //     0x9ae438: ldur            w1, [x3, #0x3b]
    // 0x9ae43c: DecompressPointer r1
    //     0x9ae43c: add             x1, x1, HEAP, lsl #32
    // 0x9ae440: str             x1, [SP]
    // 0x9ae444: r0 = _interpolateSingle()
    //     0x9ae444: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x9ae448: r1 = <NetworkImage>
    //     0x9ae448: add             x1, PP, #0x36, lsl #12  ; [pp+0x36ba8] TypeArguments: <NetworkImage>
    //     0x9ae44c: ldr             x1, [x1, #0xba8]
    // 0x9ae450: stur            x0, [fp, #-0x20]
    // 0x9ae454: r0 = NetworkImage()
    //     0x9ae454: bl              #0x80226c  ; AllocateNetworkImageStub -> NetworkImage (size=0x1c)
    // 0x9ae458: mov             x1, x0
    // 0x9ae45c: ldur            x0, [fp, #-0x20]
    // 0x9ae460: stur            x1, [fp, #-0x28]
    // 0x9ae464: StoreField: r1->field_b = r0
    //     0x9ae464: stur            w0, [x1, #0xb]
    // 0x9ae468: d0 = 1.000000
    //     0x9ae468: fmov            d0, #1.00000000
    // 0x9ae46c: StoreField: r1->field_f = d0
    //     0x9ae46c: stur            d0, [x1, #0xf]
    // 0x9ae470: r0 = DecorationImage()
    //     0x9ae470: bl              #0x83fce0  ; AllocateDecorationImageStub -> DecorationImage (size=0x44)
    // 0x9ae474: mov             x1, x0
    // 0x9ae478: ldur            x0, [fp, #-0x28]
    // 0x9ae47c: stur            x1, [fp, #-0x20]
    // 0x9ae480: StoreField: r1->field_7 = r0
    //     0x9ae480: stur            w0, [x1, #7]
    // 0x9ae484: r0 = Instance_BoxFit
    //     0x9ae484: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bb08] Obj!BoxFit@d738c1
    //     0x9ae488: ldr             x0, [x0, #0xb08]
    // 0x9ae48c: StoreField: r1->field_13 = r0
    //     0x9ae48c: stur            w0, [x1, #0x13]
    // 0x9ae490: r0 = Instance_Alignment
    //     0x9ae490: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x9ae494: ldr             x0, [x0, #0xb10]
    // 0x9ae498: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ae498: stur            w0, [x1, #0x17]
    // 0x9ae49c: r0 = Instance_ImageRepeat
    //     0x9ae49c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eb00] Obj!ImageRepeat@d73821
    //     0x9ae4a0: ldr             x0, [x0, #0xb00]
    // 0x9ae4a4: StoreField: r1->field_1f = r0
    //     0x9ae4a4: stur            w0, [x1, #0x1f]
    // 0x9ae4a8: r0 = false
    //     0x9ae4a8: add             x0, NULL, #0x30  ; false
    // 0x9ae4ac: StoreField: r1->field_23 = r0
    //     0x9ae4ac: stur            w0, [x1, #0x23]
    // 0x9ae4b0: d0 = 1.000000
    //     0x9ae4b0: fmov            d0, #1.00000000
    // 0x9ae4b4: StoreField: r1->field_27 = d0
    //     0x9ae4b4: stur            d0, [x1, #0x27]
    // 0x9ae4b8: StoreField: r1->field_2f = d0
    //     0x9ae4b8: stur            d0, [x1, #0x2f]
    // 0x9ae4bc: r2 = Instance_FilterQuality
    //     0x9ae4bc: add             x2, PP, #0x33, lsl #12  ; [pp+0x33a98] Obj!FilterQuality@d77121
    //     0x9ae4c0: ldr             x2, [x2, #0xa98]
    // 0x9ae4c4: StoreField: r1->field_37 = r2
    //     0x9ae4c4: stur            w2, [x1, #0x37]
    // 0x9ae4c8: StoreField: r1->field_3b = r0
    //     0x9ae4c8: stur            w0, [x1, #0x3b]
    // 0x9ae4cc: StoreField: r1->field_3f = r0
    //     0x9ae4cc: stur            w0, [x1, #0x3f]
    // 0x9ae4d0: r0 = BoxDecoration()
    //     0x9ae4d0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x9ae4d4: mov             x1, x0
    // 0x9ae4d8: ldur            x0, [fp, #-0x20]
    // 0x9ae4dc: stur            x1, [fp, #-0x28]
    // 0x9ae4e0: StoreField: r1->field_b = r0
    //     0x9ae4e0: stur            w0, [x1, #0xb]
    // 0x9ae4e4: ldur            x0, [fp, #-0x18]
    // 0x9ae4e8: StoreField: r1->field_13 = r0
    //     0x9ae4e8: stur            w0, [x1, #0x13]
    // 0x9ae4ec: r0 = Instance_BoxShape
    //     0x9ae4ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x9ae4f0: ldr             x0, [x0, #0x80]
    // 0x9ae4f4: StoreField: r1->field_23 = r0
    //     0x9ae4f4: stur            w0, [x1, #0x23]
    // 0x9ae4f8: r0 = GetNavigation.size()
    //     0x9ae4f8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x9ae4fc: LoadField: d0 = r0->field_7
    //     0x9ae4fc: ldur            d0, [x0, #7]
    // 0x9ae500: stur            d0, [fp, #-0x68]
    // 0x9ae504: r0 = GetNavigation.size()
    //     0x9ae504: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x9ae508: LoadField: d0 = r0->field_f
    //     0x9ae508: ldur            d0, [x0, #0xf]
    // 0x9ae50c: d1 = 0.150000
    //     0x9ae50c: ldr             d1, [PP, #0x5788]  ; [pp+0x5788] IMM: double(0.15) from 0x3fc3333333333333
    // 0x9ae510: fmul            d2, d0, d1
    // 0x9ae514: stur            d2, [fp, #-0x70]
    // 0x9ae518: r0 = GetNavigation.size()
    //     0x9ae518: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x9ae51c: LoadField: d0 = r0->field_7
    //     0x9ae51c: ldur            d0, [x0, #7]
    // 0x9ae520: d1 = 0.510000
    //     0x9ae520: add             x17, PP, #0x5b, lsl #12  ; [pp+0x5bb18] IMM: double(0.51) from 0x3fe051eb851eb852
    //     0x9ae524: ldr             d1, [x17, #0xb18]
    // 0x9ae528: fmul            d2, d0, d1
    // 0x9ae52c: ldur            x0, [fp, #-8]
    // 0x9ae530: stur            d2, [fp, #-0x78]
    // 0x9ae534: LoadField: r1 = r0->field_b
    //     0x9ae534: ldur            w1, [x0, #0xb]
    // 0x9ae538: DecompressPointer r1
    //     0x9ae538: add             x1, x1, HEAP, lsl #32
    // 0x9ae53c: cmp             w1, NULL
    // 0x9ae540: b.eq            #0x9af640
    // 0x9ae544: LoadField: r2 = r1->field_b
    //     0x9ae544: ldur            w2, [x1, #0xb]
    // 0x9ae548: DecompressPointer r2
    //     0x9ae548: add             x2, x2, HEAP, lsl #32
    // 0x9ae54c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9ae54c: ldur            w1, [x2, #0x17]
    // 0x9ae550: DecompressPointer r1
    //     0x9ae550: add             x1, x1, HEAP, lsl #32
    // 0x9ae554: cmp             w1, NULL
    // 0x9ae558: b.ne            #0x9ae564
    // 0x9ae55c: r2 = ""
    //     0x9ae55c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9ae560: b               #0x9ae568
    // 0x9ae564: mov             x2, x1
    // 0x9ae568: stur            x2, [fp, #-0x18]
    // 0x9ae56c: LoadField: r1 = r0->field_f
    //     0x9ae56c: ldur            w1, [x0, #0xf]
    // 0x9ae570: DecompressPointer r1
    //     0x9ae570: add             x1, x1, HEAP, lsl #32
    // 0x9ae574: cmp             w1, NULL
    // 0x9ae578: b.eq            #0x9af644
    // 0x9ae57c: r0 = of()
    //     0x9ae57c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ae580: LoadField: r1 = r0->field_87
    //     0x9ae580: ldur            w1, [x0, #0x87]
    // 0x9ae584: DecompressPointer r1
    //     0x9ae584: add             x1, x1, HEAP, lsl #32
    // 0x9ae588: LoadField: r0 = r1->field_7
    //     0x9ae588: ldur            w0, [x1, #7]
    // 0x9ae58c: DecompressPointer r0
    //     0x9ae58c: add             x0, x0, HEAP, lsl #32
    // 0x9ae590: ldur            x2, [fp, #-8]
    // 0x9ae594: stur            x0, [fp, #-0x20]
    // 0x9ae598: LoadField: r1 = r2->field_b
    //     0x9ae598: ldur            w1, [x2, #0xb]
    // 0x9ae59c: DecompressPointer r1
    //     0x9ae59c: add             x1, x1, HEAP, lsl #32
    // 0x9ae5a0: cmp             w1, NULL
    // 0x9ae5a4: b.eq            #0x9af648
    // 0x9ae5a8: LoadField: r3 = r1->field_b
    //     0x9ae5a8: ldur            w3, [x1, #0xb]
    // 0x9ae5ac: DecompressPointer r3
    //     0x9ae5ac: add             x3, x3, HEAP, lsl #32
    // 0x9ae5b0: LoadField: r1 = r3->field_b
    //     0x9ae5b0: ldur            w1, [x3, #0xb]
    // 0x9ae5b4: DecompressPointer r1
    //     0x9ae5b4: add             x1, x1, HEAP, lsl #32
    // 0x9ae5b8: cmp             w1, NULL
    // 0x9ae5bc: b.ne            #0x9ae5c8
    // 0x9ae5c0: r1 = Null
    //     0x9ae5c0: mov             x1, NULL
    // 0x9ae5c4: b               #0x9ae5d0
    // 0x9ae5c8: r0 = ColorExtension.toColor()
    //     0x9ae5c8: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9ae5cc: mov             x1, x0
    // 0x9ae5d0: ldur            x2, [fp, #-0x10]
    // 0x9ae5d4: ldur            d0, [fp, #-0x78]
    // 0x9ae5d8: ldur            x0, [fp, #-0x18]
    // 0x9ae5dc: r16 = 16.000000
    //     0x9ae5dc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9ae5e0: ldr             x16, [x16, #0x188]
    // 0x9ae5e4: stp             x1, x16, [SP]
    // 0x9ae5e8: ldur            x1, [fp, #-0x20]
    // 0x9ae5ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x9ae5ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x9ae5f0: ldr             x4, [x4, #0xaa0]
    // 0x9ae5f4: r0 = copyWith()
    //     0x9ae5f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ae5f8: stur            x0, [fp, #-0x20]
    // 0x9ae5fc: r0 = Text()
    //     0x9ae5fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ae600: mov             x1, x0
    // 0x9ae604: ldur            x0, [fp, #-0x18]
    // 0x9ae608: stur            x1, [fp, #-0x38]
    // 0x9ae60c: StoreField: r1->field_b = r0
    //     0x9ae60c: stur            w0, [x1, #0xb]
    // 0x9ae610: ldur            x0, [fp, #-0x20]
    // 0x9ae614: StoreField: r1->field_13 = r0
    //     0x9ae614: stur            w0, [x1, #0x13]
    // 0x9ae618: ldur            d0, [fp, #-0x78]
    // 0x9ae61c: r0 = inline_Allocate_Double()
    //     0x9ae61c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x9ae620: add             x0, x0, #0x10
    //     0x9ae624: cmp             x2, x0
    //     0x9ae628: b.ls            #0x9af64c
    //     0x9ae62c: str             x0, [THR, #0x50]  ; THR::top
    //     0x9ae630: sub             x0, x0, #0xf
    //     0x9ae634: movz            x2, #0xe15c
    //     0x9ae638: movk            x2, #0x3, lsl #16
    //     0x9ae63c: stur            x2, [x0, #-1]
    // 0x9ae640: StoreField: r0->field_7 = d0
    //     0x9ae640: stur            d0, [x0, #7]
    // 0x9ae644: stur            x0, [fp, #-0x18]
    // 0x9ae648: r0 = SizedBox()
    //     0x9ae648: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9ae64c: mov             x1, x0
    // 0x9ae650: ldur            x0, [fp, #-0x18]
    // 0x9ae654: stur            x1, [fp, #-0x20]
    // 0x9ae658: StoreField: r1->field_f = r0
    //     0x9ae658: stur            w0, [x1, #0xf]
    // 0x9ae65c: ldur            x0, [fp, #-0x38]
    // 0x9ae660: StoreField: r1->field_b = r0
    //     0x9ae660: stur            w0, [x1, #0xb]
    // 0x9ae664: r0 = SizedBox()
    //     0x9ae664: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x9ae668: mov             x3, x0
    // 0x9ae66c: r0 = 180.000000
    //     0x9ae66c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51c88] 180
    //     0x9ae670: ldr             x0, [x0, #0xc88]
    // 0x9ae674: stur            x3, [fp, #-0x18]
    // 0x9ae678: StoreField: r3->field_f = r0
    //     0x9ae678: stur            w0, [x3, #0xf]
    // 0x9ae67c: ldur            x0, [fp, #-0x20]
    // 0x9ae680: StoreField: r3->field_b = r0
    //     0x9ae680: stur            w0, [x3, #0xb]
    // 0x9ae684: r1 = <Widget>
    //     0x9ae684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9ae688: r2 = 0
    //     0x9ae688: movz            x2, #0
    // 0x9ae68c: r0 = _GrowableList()
    //     0x9ae68c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x9ae690: mov             x3, x0
    // 0x9ae694: ldur            x0, [fp, #-0x10]
    // 0x9ae698: stur            x3, [fp, #-0x38]
    // 0x9ae69c: LoadField: r4 = r0->field_b
    //     0x9ae69c: ldur            w4, [x0, #0xb]
    // 0x9ae6a0: DecompressPointer r4
    //     0x9ae6a0: add             x4, x4, HEAP, lsl #32
    // 0x9ae6a4: stur            x4, [fp, #-0x20]
    // 0x9ae6a8: cmp             w4, NULL
    // 0x9ae6ac: b.eq            #0x9ae85c
    // 0x9ae6b0: r1 = LoadInt32Instr(r4)
    //     0x9ae6b0: sbfx            x1, x4, #1, #0x1f
    //     0x9ae6b4: tbz             w4, #0, #0x9ae6bc
    //     0x9ae6b8: ldur            x1, [x4, #7]
    // 0x9ae6bc: cmp             x1, #0xa
    // 0x9ae6c0: b.ge            #0x9ae6fc
    // 0x9ae6c4: r1 = Null
    //     0x9ae6c4: mov             x1, NULL
    // 0x9ae6c8: r2 = 6
    //     0x9ae6c8: movz            x2, #0x6
    // 0x9ae6cc: r0 = AllocateArray()
    //     0x9ae6cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ae6d0: r16 = " 0"
    //     0x9ae6d0: add             x16, PP, #8, lsl #12  ; [pp+0x8df0] " 0"
    //     0x9ae6d4: ldr             x16, [x16, #0xdf0]
    // 0x9ae6d8: StoreField: r0->field_f = r16
    //     0x9ae6d8: stur            w16, [x0, #0xf]
    // 0x9ae6dc: ldur            x3, [fp, #-0x20]
    // 0x9ae6e0: StoreField: r0->field_13 = r3
    //     0x9ae6e0: stur            w3, [x0, #0x13]
    // 0x9ae6e4: r16 = " "
    //     0x9ae6e4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9ae6e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x9ae6e8: stur            w16, [x0, #0x17]
    // 0x9ae6ec: str             x0, [SP]
    // 0x9ae6f0: r0 = _interpolate()
    //     0x9ae6f0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9ae6f4: mov             x2, x0
    // 0x9ae6f8: b               #0x9ae730
    // 0x9ae6fc: mov             x3, x4
    // 0x9ae700: r1 = Null
    //     0x9ae700: mov             x1, NULL
    // 0x9ae704: r2 = 6
    //     0x9ae704: movz            x2, #0x6
    // 0x9ae708: r0 = AllocateArray()
    //     0x9ae708: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9ae70c: r16 = " "
    //     0x9ae70c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9ae710: StoreField: r0->field_f = r16
    //     0x9ae710: stur            w16, [x0, #0xf]
    // 0x9ae714: ldur            x1, [fp, #-0x20]
    // 0x9ae718: StoreField: r0->field_13 = r1
    //     0x9ae718: stur            w1, [x0, #0x13]
    // 0x9ae71c: r16 = " "
    //     0x9ae71c: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9ae720: ArrayStore: r0[0] = r16  ; List_4
    //     0x9ae720: stur            w16, [x0, #0x17]
    // 0x9ae724: str             x0, [SP]
    // 0x9ae728: r0 = _interpolate()
    //     0x9ae728: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9ae72c: mov             x2, x0
    // 0x9ae730: ldur            x0, [fp, #-8]
    // 0x9ae734: stur            x2, [fp, #-0x20]
    // 0x9ae738: LoadField: r1 = r0->field_f
    //     0x9ae738: ldur            w1, [x0, #0xf]
    // 0x9ae73c: DecompressPointer r1
    //     0x9ae73c: add             x1, x1, HEAP, lsl #32
    // 0x9ae740: cmp             w1, NULL
    // 0x9ae744: b.eq            #0x9af664
    // 0x9ae748: r0 = of()
    //     0x9ae748: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ae74c: LoadField: r1 = r0->field_87
    //     0x9ae74c: ldur            w1, [x0, #0x87]
    // 0x9ae750: DecompressPointer r1
    //     0x9ae750: add             x1, x1, HEAP, lsl #32
    // 0x9ae754: LoadField: r0 = r1->field_2b
    //     0x9ae754: ldur            w0, [x1, #0x2b]
    // 0x9ae758: DecompressPointer r0
    //     0x9ae758: add             x0, x0, HEAP, lsl #32
    // 0x9ae75c: ldur            x2, [fp, #-8]
    // 0x9ae760: stur            x0, [fp, #-0x40]
    // 0x9ae764: LoadField: r1 = r2->field_b
    //     0x9ae764: ldur            w1, [x2, #0xb]
    // 0x9ae768: DecompressPointer r1
    //     0x9ae768: add             x1, x1, HEAP, lsl #32
    // 0x9ae76c: cmp             w1, NULL
    // 0x9ae770: b.eq            #0x9af668
    // 0x9ae774: LoadField: r3 = r1->field_b
    //     0x9ae774: ldur            w3, [x1, #0xb]
    // 0x9ae778: DecompressPointer r3
    //     0x9ae778: add             x3, x3, HEAP, lsl #32
    // 0x9ae77c: LoadField: r1 = r3->field_b
    //     0x9ae77c: ldur            w1, [x3, #0xb]
    // 0x9ae780: DecompressPointer r1
    //     0x9ae780: add             x1, x1, HEAP, lsl #32
    // 0x9ae784: cmp             w1, NULL
    // 0x9ae788: b.ne            #0x9ae794
    // 0x9ae78c: r1 = Null
    //     0x9ae78c: mov             x1, NULL
    // 0x9ae790: b               #0x9ae79c
    // 0x9ae794: r0 = ColorExtension.toColor()
    //     0x9ae794: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9ae798: mov             x1, x0
    // 0x9ae79c: ldur            x2, [fp, #-0x38]
    // 0x9ae7a0: ldur            x0, [fp, #-0x20]
    // 0x9ae7a4: r16 = 16.000000
    //     0x9ae7a4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9ae7a8: ldr             x16, [x16, #0x188]
    // 0x9ae7ac: stp             x16, x1, [SP]
    // 0x9ae7b0: ldur            x1, [fp, #-0x40]
    // 0x9ae7b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9ae7b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9ae7b8: ldr             x4, [x4, #0x9b8]
    // 0x9ae7bc: r0 = copyWith()
    //     0x9ae7bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ae7c0: stur            x0, [fp, #-0x40]
    // 0x9ae7c4: r0 = Text()
    //     0x9ae7c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ae7c8: mov             x2, x0
    // 0x9ae7cc: ldur            x0, [fp, #-0x20]
    // 0x9ae7d0: stur            x2, [fp, #-0x50]
    // 0x9ae7d4: StoreField: r2->field_b = r0
    //     0x9ae7d4: stur            w0, [x2, #0xb]
    // 0x9ae7d8: ldur            x0, [fp, #-0x40]
    // 0x9ae7dc: StoreField: r2->field_13 = r0
    //     0x9ae7dc: stur            w0, [x2, #0x13]
    // 0x9ae7e0: ldur            x0, [fp, #-0x38]
    // 0x9ae7e4: LoadField: r1 = r0->field_b
    //     0x9ae7e4: ldur            w1, [x0, #0xb]
    // 0x9ae7e8: LoadField: r3 = r0->field_f
    //     0x9ae7e8: ldur            w3, [x0, #0xf]
    // 0x9ae7ec: DecompressPointer r3
    //     0x9ae7ec: add             x3, x3, HEAP, lsl #32
    // 0x9ae7f0: LoadField: r4 = r3->field_b
    //     0x9ae7f0: ldur            w4, [x3, #0xb]
    // 0x9ae7f4: r3 = LoadInt32Instr(r1)
    //     0x9ae7f4: sbfx            x3, x1, #1, #0x1f
    // 0x9ae7f8: stur            x3, [fp, #-0x48]
    // 0x9ae7fc: r1 = LoadInt32Instr(r4)
    //     0x9ae7fc: sbfx            x1, x4, #1, #0x1f
    // 0x9ae800: cmp             x3, x1
    // 0x9ae804: b.ne            #0x9ae810
    // 0x9ae808: mov             x1, x0
    // 0x9ae80c: r0 = _growToNextCapacity()
    //     0x9ae80c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9ae810: ldur            x2, [fp, #-0x38]
    // 0x9ae814: ldur            x3, [fp, #-0x48]
    // 0x9ae818: add             x0, x3, #1
    // 0x9ae81c: lsl             x1, x0, #1
    // 0x9ae820: StoreField: r2->field_b = r1
    //     0x9ae820: stur            w1, [x2, #0xb]
    // 0x9ae824: LoadField: r1 = r2->field_f
    //     0x9ae824: ldur            w1, [x2, #0xf]
    // 0x9ae828: DecompressPointer r1
    //     0x9ae828: add             x1, x1, HEAP, lsl #32
    // 0x9ae82c: ldur            x0, [fp, #-0x50]
    // 0x9ae830: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9ae830: add             x25, x1, x3, lsl #2
    //     0x9ae834: add             x25, x25, #0xf
    //     0x9ae838: str             w0, [x25]
    //     0x9ae83c: tbz             w0, #0, #0x9ae858
    //     0x9ae840: ldurb           w16, [x1, #-1]
    //     0x9ae844: ldurb           w17, [x0, #-1]
    //     0x9ae848: and             x16, x17, x16, lsr #2
    //     0x9ae84c: tst             x16, HEAP, lsr #32
    //     0x9ae850: b.eq            #0x9ae858
    //     0x9ae854: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9ae858: b               #0x9ae984
    // 0x9ae85c: ldur            x0, [fp, #-8]
    // 0x9ae860: mov             x2, x3
    // 0x9ae864: LoadField: r1 = r0->field_f
    //     0x9ae864: ldur            w1, [x0, #0xf]
    // 0x9ae868: DecompressPointer r1
    //     0x9ae868: add             x1, x1, HEAP, lsl #32
    // 0x9ae86c: cmp             w1, NULL
    // 0x9ae870: b.eq            #0x9af66c
    // 0x9ae874: r0 = of()
    //     0x9ae874: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ae878: LoadField: r1 = r0->field_87
    //     0x9ae878: ldur            w1, [x0, #0x87]
    // 0x9ae87c: DecompressPointer r1
    //     0x9ae87c: add             x1, x1, HEAP, lsl #32
    // 0x9ae880: LoadField: r0 = r1->field_2b
    //     0x9ae880: ldur            w0, [x1, #0x2b]
    // 0x9ae884: DecompressPointer r0
    //     0x9ae884: add             x0, x0, HEAP, lsl #32
    // 0x9ae888: ldur            x2, [fp, #-8]
    // 0x9ae88c: stur            x0, [fp, #-0x20]
    // 0x9ae890: LoadField: r1 = r2->field_b
    //     0x9ae890: ldur            w1, [x2, #0xb]
    // 0x9ae894: DecompressPointer r1
    //     0x9ae894: add             x1, x1, HEAP, lsl #32
    // 0x9ae898: cmp             w1, NULL
    // 0x9ae89c: b.eq            #0x9af670
    // 0x9ae8a0: LoadField: r3 = r1->field_b
    //     0x9ae8a0: ldur            w3, [x1, #0xb]
    // 0x9ae8a4: DecompressPointer r3
    //     0x9ae8a4: add             x3, x3, HEAP, lsl #32
    // 0x9ae8a8: LoadField: r1 = r3->field_b
    //     0x9ae8a8: ldur            w1, [x3, #0xb]
    // 0x9ae8ac: DecompressPointer r1
    //     0x9ae8ac: add             x1, x1, HEAP, lsl #32
    // 0x9ae8b0: cmp             w1, NULL
    // 0x9ae8b4: b.ne            #0x9ae8c0
    // 0x9ae8b8: r1 = Null
    //     0x9ae8b8: mov             x1, NULL
    // 0x9ae8bc: b               #0x9ae8c8
    // 0x9ae8c0: r0 = ColorExtension.toColor()
    //     0x9ae8c0: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9ae8c4: mov             x1, x0
    // 0x9ae8c8: ldur            x0, [fp, #-0x38]
    // 0x9ae8cc: r16 = 16.000000
    //     0x9ae8cc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9ae8d0: ldr             x16, [x16, #0x188]
    // 0x9ae8d4: stp             x16, x1, [SP]
    // 0x9ae8d8: ldur            x1, [fp, #-0x20]
    // 0x9ae8dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9ae8dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9ae8e0: ldr             x4, [x4, #0x9b8]
    // 0x9ae8e4: r0 = copyWith()
    //     0x9ae8e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9ae8e8: stur            x0, [fp, #-0x20]
    // 0x9ae8ec: r0 = Text()
    //     0x9ae8ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9ae8f0: mov             x2, x0
    // 0x9ae8f4: r0 = "00"
    //     0x9ae8f4: add             x0, PP, #0x26, lsl #12  ; [pp+0x261d8] "00"
    //     0x9ae8f8: ldr             x0, [x0, #0x1d8]
    // 0x9ae8fc: stur            x2, [fp, #-0x40]
    // 0x9ae900: StoreField: r2->field_b = r0
    //     0x9ae900: stur            w0, [x2, #0xb]
    // 0x9ae904: ldur            x1, [fp, #-0x20]
    // 0x9ae908: StoreField: r2->field_13 = r1
    //     0x9ae908: stur            w1, [x2, #0x13]
    // 0x9ae90c: ldur            x3, [fp, #-0x38]
    // 0x9ae910: LoadField: r1 = r3->field_b
    //     0x9ae910: ldur            w1, [x3, #0xb]
    // 0x9ae914: LoadField: r4 = r3->field_f
    //     0x9ae914: ldur            w4, [x3, #0xf]
    // 0x9ae918: DecompressPointer r4
    //     0x9ae918: add             x4, x4, HEAP, lsl #32
    // 0x9ae91c: LoadField: r5 = r4->field_b
    //     0x9ae91c: ldur            w5, [x4, #0xb]
    // 0x9ae920: r4 = LoadInt32Instr(r1)
    //     0x9ae920: sbfx            x4, x1, #1, #0x1f
    // 0x9ae924: stur            x4, [fp, #-0x48]
    // 0x9ae928: r1 = LoadInt32Instr(r5)
    //     0x9ae928: sbfx            x1, x5, #1, #0x1f
    // 0x9ae92c: cmp             x4, x1
    // 0x9ae930: b.ne            #0x9ae93c
    // 0x9ae934: mov             x1, x3
    // 0x9ae938: r0 = _growToNextCapacity()
    //     0x9ae938: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9ae93c: ldur            x2, [fp, #-0x38]
    // 0x9ae940: ldur            x3, [fp, #-0x48]
    // 0x9ae944: add             x0, x3, #1
    // 0x9ae948: lsl             x1, x0, #1
    // 0x9ae94c: StoreField: r2->field_b = r1
    //     0x9ae94c: stur            w1, [x2, #0xb]
    // 0x9ae950: LoadField: r1 = r2->field_f
    //     0x9ae950: ldur            w1, [x2, #0xf]
    // 0x9ae954: DecompressPointer r1
    //     0x9ae954: add             x1, x1, HEAP, lsl #32
    // 0x9ae958: ldur            x0, [fp, #-0x40]
    // 0x9ae95c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9ae95c: add             x25, x1, x3, lsl #2
    //     0x9ae960: add             x25, x25, #0xf
    //     0x9ae964: str             w0, [x25]
    //     0x9ae968: tbz             w0, #0, #0x9ae984
    //     0x9ae96c: ldurb           w16, [x1, #-1]
    //     0x9ae970: ldurb           w17, [x0, #-1]
    //     0x9ae974: and             x16, x17, x16, lsr #2
    //     0x9ae978: tst             x16, HEAP, lsr #32
    //     0x9ae97c: b.eq            #0x9ae984
    //     0x9ae980: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9ae984: ldur            x0, [fp, #-8]
    // 0x9ae988: LoadField: r1 = r0->field_f
    //     0x9ae988: ldur            w1, [x0, #0xf]
    // 0x9ae98c: DecompressPointer r1
    //     0x9ae98c: add             x1, x1, HEAP, lsl #32
    // 0x9ae990: cmp             w1, NULL
    // 0x9ae994: b.eq            #0x9af674
    // 0x9ae998: r0 = of()
    //     0x9ae998: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9ae99c: LoadField: r1 = r0->field_87
    //     0x9ae99c: ldur            w1, [x0, #0x87]
    // 0x9ae9a0: DecompressPointer r1
    //     0x9ae9a0: add             x1, x1, HEAP, lsl #32
    // 0x9ae9a4: LoadField: r0 = r1->field_2b
    //     0x9ae9a4: ldur            w0, [x1, #0x2b]
    // 0x9ae9a8: DecompressPointer r0
    //     0x9ae9a8: add             x0, x0, HEAP, lsl #32
    // 0x9ae9ac: ldur            x2, [fp, #-8]
    // 0x9ae9b0: stur            x0, [fp, #-0x20]
    // 0x9ae9b4: LoadField: r1 = r2->field_b
    //     0x9ae9b4: ldur            w1, [x2, #0xb]
    // 0x9ae9b8: DecompressPointer r1
    //     0x9ae9b8: add             x1, x1, HEAP, lsl #32
    // 0x9ae9bc: cmp             w1, NULL
    // 0x9ae9c0: b.eq            #0x9af678
    // 0x9ae9c4: LoadField: r3 = r1->field_b
    //     0x9ae9c4: ldur            w3, [x1, #0xb]
    // 0x9ae9c8: DecompressPointer r3
    //     0x9ae9c8: add             x3, x3, HEAP, lsl #32
    // 0x9ae9cc: LoadField: r1 = r3->field_b
    //     0x9ae9cc: ldur            w1, [x3, #0xb]
    // 0x9ae9d0: DecompressPointer r1
    //     0x9ae9d0: add             x1, x1, HEAP, lsl #32
    // 0x9ae9d4: cmp             w1, NULL
    // 0x9ae9d8: b.ne            #0x9ae9e4
    // 0x9ae9dc: r1 = Null
    //     0x9ae9dc: mov             x1, NULL
    // 0x9ae9e0: b               #0x9ae9ec
    // 0x9ae9e4: r0 = ColorExtension.toColor()
    //     0x9ae9e4: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9ae9e8: mov             x1, x0
    // 0x9ae9ec: ldur            x0, [fp, #-0x38]
    // 0x9ae9f0: r16 = 12.000000
    //     0x9ae9f0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9ae9f4: ldr             x16, [x16, #0x9e8]
    // 0x9ae9f8: stp             x16, x1, [SP]
    // 0x9ae9fc: ldur            x1, [fp, #-0x20]
    // 0x9aea00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9aea00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9aea04: ldr             x4, [x4, #0x9b8]
    // 0x9aea08: r0 = copyWith()
    //     0x9aea08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9aea0c: stur            x0, [fp, #-0x20]
    // 0x9aea10: r0 = Text()
    //     0x9aea10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9aea14: mov             x2, x0
    // 0x9aea18: r0 = "H"
    //     0x9aea18: add             x0, PP, #0x11, lsl #12  ; [pp+0x117f8] "H"
    //     0x9aea1c: ldr             x0, [x0, #0x7f8]
    // 0x9aea20: stur            x2, [fp, #-0x40]
    // 0x9aea24: StoreField: r2->field_b = r0
    //     0x9aea24: stur            w0, [x2, #0xb]
    // 0x9aea28: ldur            x0, [fp, #-0x20]
    // 0x9aea2c: StoreField: r2->field_13 = r0
    //     0x9aea2c: stur            w0, [x2, #0x13]
    // 0x9aea30: ldur            x0, [fp, #-0x38]
    // 0x9aea34: LoadField: r1 = r0->field_b
    //     0x9aea34: ldur            w1, [x0, #0xb]
    // 0x9aea38: LoadField: r3 = r0->field_f
    //     0x9aea38: ldur            w3, [x0, #0xf]
    // 0x9aea3c: DecompressPointer r3
    //     0x9aea3c: add             x3, x3, HEAP, lsl #32
    // 0x9aea40: LoadField: r4 = r3->field_b
    //     0x9aea40: ldur            w4, [x3, #0xb]
    // 0x9aea44: r3 = LoadInt32Instr(r1)
    //     0x9aea44: sbfx            x3, x1, #1, #0x1f
    // 0x9aea48: stur            x3, [fp, #-0x48]
    // 0x9aea4c: r1 = LoadInt32Instr(r4)
    //     0x9aea4c: sbfx            x1, x4, #1, #0x1f
    // 0x9aea50: cmp             x3, x1
    // 0x9aea54: b.ne            #0x9aea60
    // 0x9aea58: mov             x1, x0
    // 0x9aea5c: r0 = _growToNextCapacity()
    //     0x9aea5c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9aea60: ldur            x4, [fp, #-8]
    // 0x9aea64: ldur            x2, [fp, #-0x38]
    // 0x9aea68: ldur            x3, [fp, #-0x48]
    // 0x9aea6c: add             x0, x3, #1
    // 0x9aea70: lsl             x1, x0, #1
    // 0x9aea74: StoreField: r2->field_b = r1
    //     0x9aea74: stur            w1, [x2, #0xb]
    // 0x9aea78: LoadField: r1 = r2->field_f
    //     0x9aea78: ldur            w1, [x2, #0xf]
    // 0x9aea7c: DecompressPointer r1
    //     0x9aea7c: add             x1, x1, HEAP, lsl #32
    // 0x9aea80: ldur            x0, [fp, #-0x40]
    // 0x9aea84: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9aea84: add             x25, x1, x3, lsl #2
    //     0x9aea88: add             x25, x25, #0xf
    //     0x9aea8c: str             w0, [x25]
    //     0x9aea90: tbz             w0, #0, #0x9aeaac
    //     0x9aea94: ldurb           w16, [x1, #-1]
    //     0x9aea98: ldurb           w17, [x0, #-1]
    //     0x9aea9c: and             x16, x17, x16, lsr #2
    //     0x9aeaa0: tst             x16, HEAP, lsr #32
    //     0x9aeaa4: b.eq            #0x9aeaac
    //     0x9aeaa8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9aeaac: r0 = Column()
    //     0x9aeaac: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9aeab0: mov             x2, x0
    // 0x9aeab4: r0 = Instance_Axis
    //     0x9aeab4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9aeab8: stur            x2, [fp, #-0x20]
    // 0x9aeabc: StoreField: r2->field_f = r0
    //     0x9aeabc: stur            w0, [x2, #0xf]
    // 0x9aeac0: r3 = Instance_MainAxisAlignment
    //     0x9aeac0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9aeac4: ldr             x3, [x3, #0xab0]
    // 0x9aeac8: StoreField: r2->field_13 = r3
    //     0x9aeac8: stur            w3, [x2, #0x13]
    // 0x9aeacc: r4 = Instance_MainAxisSize
    //     0x9aeacc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9aead0: ldr             x4, [x4, #0xa10]
    // 0x9aead4: ArrayStore: r2[0] = r4  ; List_4
    //     0x9aead4: stur            w4, [x2, #0x17]
    // 0x9aead8: r5 = Instance_CrossAxisAlignment
    //     0x9aead8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9aeadc: ldr             x5, [x5, #0xa18]
    // 0x9aeae0: StoreField: r2->field_1b = r5
    //     0x9aeae0: stur            w5, [x2, #0x1b]
    // 0x9aeae4: r6 = Instance_VerticalDirection
    //     0x9aeae4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9aeae8: ldr             x6, [x6, #0xa20]
    // 0x9aeaec: StoreField: r2->field_23 = r6
    //     0x9aeaec: stur            w6, [x2, #0x23]
    // 0x9aeaf0: r7 = Instance_Clip
    //     0x9aeaf0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9aeaf4: ldr             x7, [x7, #0x38]
    // 0x9aeaf8: StoreField: r2->field_2b = r7
    //     0x9aeaf8: stur            w7, [x2, #0x2b]
    // 0x9aeafc: StoreField: r2->field_2f = rZR
    //     0x9aeafc: stur            xzr, [x2, #0x2f]
    // 0x9aeb00: ldur            x1, [fp, #-0x38]
    // 0x9aeb04: StoreField: r2->field_b = r1
    //     0x9aeb04: stur            w1, [x2, #0xb]
    // 0x9aeb08: ldur            x8, [fp, #-8]
    // 0x9aeb0c: LoadField: r1 = r8->field_f
    //     0x9aeb0c: ldur            w1, [x8, #0xf]
    // 0x9aeb10: DecompressPointer r1
    //     0x9aeb10: add             x1, x1, HEAP, lsl #32
    // 0x9aeb14: cmp             w1, NULL
    // 0x9aeb18: b.eq            #0x9af67c
    // 0x9aeb1c: r0 = of()
    //     0x9aeb1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9aeb20: LoadField: r1 = r0->field_87
    //     0x9aeb20: ldur            w1, [x0, #0x87]
    // 0x9aeb24: DecompressPointer r1
    //     0x9aeb24: add             x1, x1, HEAP, lsl #32
    // 0x9aeb28: LoadField: r0 = r1->field_7
    //     0x9aeb28: ldur            w0, [x1, #7]
    // 0x9aeb2c: DecompressPointer r0
    //     0x9aeb2c: add             x0, x0, HEAP, lsl #32
    // 0x9aeb30: ldur            x2, [fp, #-8]
    // 0x9aeb34: stur            x0, [fp, #-0x38]
    // 0x9aeb38: LoadField: r1 = r2->field_b
    //     0x9aeb38: ldur            w1, [x2, #0xb]
    // 0x9aeb3c: DecompressPointer r1
    //     0x9aeb3c: add             x1, x1, HEAP, lsl #32
    // 0x9aeb40: cmp             w1, NULL
    // 0x9aeb44: b.eq            #0x9af680
    // 0x9aeb48: LoadField: r3 = r1->field_b
    //     0x9aeb48: ldur            w3, [x1, #0xb]
    // 0x9aeb4c: DecompressPointer r3
    //     0x9aeb4c: add             x3, x3, HEAP, lsl #32
    // 0x9aeb50: LoadField: r1 = r3->field_b
    //     0x9aeb50: ldur            w1, [x3, #0xb]
    // 0x9aeb54: DecompressPointer r1
    //     0x9aeb54: add             x1, x1, HEAP, lsl #32
    // 0x9aeb58: cmp             w1, NULL
    // 0x9aeb5c: b.ne            #0x9aeb68
    // 0x9aeb60: r1 = Null
    //     0x9aeb60: mov             x1, NULL
    // 0x9aeb64: b               #0x9aeb70
    // 0x9aeb68: r0 = ColorExtension.toColor()
    //     0x9aeb68: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9aeb6c: mov             x1, x0
    // 0x9aeb70: ldur            x0, [fp, #-0x10]
    // 0x9aeb74: r16 = 16.000000
    //     0x9aeb74: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9aeb78: ldr             x16, [x16, #0x188]
    // 0x9aeb7c: stp             x16, x1, [SP]
    // 0x9aeb80: ldur            x1, [fp, #-0x38]
    // 0x9aeb84: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9aeb84: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9aeb88: ldr             x4, [x4, #0x9b8]
    // 0x9aeb8c: r0 = copyWith()
    //     0x9aeb8c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9aeb90: stur            x0, [fp, #-0x38]
    // 0x9aeb94: r0 = Text()
    //     0x9aeb94: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9aeb98: mov             x1, x0
    // 0x9aeb9c: r0 = ":"
    //     0x9aeb9c: ldr             x0, [PP, #0xd50]  ; [pp+0xd50] ":"
    // 0x9aeba0: stur            x1, [fp, #-0x40]
    // 0x9aeba4: StoreField: r1->field_b = r0
    //     0x9aeba4: stur            w0, [x1, #0xb]
    // 0x9aeba8: ldur            x2, [fp, #-0x38]
    // 0x9aebac: StoreField: r1->field_13 = r2
    //     0x9aebac: stur            w2, [x1, #0x13]
    // 0x9aebb0: r0 = Padding()
    //     0x9aebb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9aebb4: mov             x3, x0
    // 0x9aebb8: r0 = Instance_EdgeInsets
    //     0x9aebb8: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bb20] Obj!EdgeInsets@d57771
    //     0x9aebbc: ldr             x0, [x0, #0xb20]
    // 0x9aebc0: stur            x3, [fp, #-0x38]
    // 0x9aebc4: StoreField: r3->field_f = r0
    //     0x9aebc4: stur            w0, [x3, #0xf]
    // 0x9aebc8: ldur            x1, [fp, #-0x40]
    // 0x9aebcc: StoreField: r3->field_b = r1
    //     0x9aebcc: stur            w1, [x3, #0xb]
    // 0x9aebd0: r1 = <Widget>
    //     0x9aebd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9aebd4: r2 = 0
    //     0x9aebd4: movz            x2, #0
    // 0x9aebd8: r0 = _GrowableList()
    //     0x9aebd8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x9aebdc: mov             x3, x0
    // 0x9aebe0: ldur            x0, [fp, #-0x10]
    // 0x9aebe4: stur            x3, [fp, #-0x50]
    // 0x9aebe8: LoadField: r4 = r0->field_f
    //     0x9aebe8: ldur            w4, [x0, #0xf]
    // 0x9aebec: DecompressPointer r4
    //     0x9aebec: add             x4, x4, HEAP, lsl #32
    // 0x9aebf0: stur            x4, [fp, #-0x40]
    // 0x9aebf4: cmp             w4, NULL
    // 0x9aebf8: b.eq            #0x9aeda8
    // 0x9aebfc: r1 = LoadInt32Instr(r4)
    //     0x9aebfc: sbfx            x1, x4, #1, #0x1f
    //     0x9aec00: tbz             w4, #0, #0x9aec08
    //     0x9aec04: ldur            x1, [x4, #7]
    // 0x9aec08: cmp             x1, #0xa
    // 0x9aec0c: b.ge            #0x9aec48
    // 0x9aec10: r1 = Null
    //     0x9aec10: mov             x1, NULL
    // 0x9aec14: r2 = 6
    //     0x9aec14: movz            x2, #0x6
    // 0x9aec18: r0 = AllocateArray()
    //     0x9aec18: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9aec1c: r16 = " 0"
    //     0x9aec1c: add             x16, PP, #8, lsl #12  ; [pp+0x8df0] " 0"
    //     0x9aec20: ldr             x16, [x16, #0xdf0]
    // 0x9aec24: StoreField: r0->field_f = r16
    //     0x9aec24: stur            w16, [x0, #0xf]
    // 0x9aec28: ldur            x3, [fp, #-0x40]
    // 0x9aec2c: StoreField: r0->field_13 = r3
    //     0x9aec2c: stur            w3, [x0, #0x13]
    // 0x9aec30: r16 = " "
    //     0x9aec30: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9aec34: ArrayStore: r0[0] = r16  ; List_4
    //     0x9aec34: stur            w16, [x0, #0x17]
    // 0x9aec38: str             x0, [SP]
    // 0x9aec3c: r0 = _interpolate()
    //     0x9aec3c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9aec40: mov             x2, x0
    // 0x9aec44: b               #0x9aec7c
    // 0x9aec48: mov             x3, x4
    // 0x9aec4c: r1 = Null
    //     0x9aec4c: mov             x1, NULL
    // 0x9aec50: r2 = 6
    //     0x9aec50: movz            x2, #0x6
    // 0x9aec54: r0 = AllocateArray()
    //     0x9aec54: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9aec58: r16 = " "
    //     0x9aec58: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9aec5c: StoreField: r0->field_f = r16
    //     0x9aec5c: stur            w16, [x0, #0xf]
    // 0x9aec60: ldur            x1, [fp, #-0x40]
    // 0x9aec64: StoreField: r0->field_13 = r1
    //     0x9aec64: stur            w1, [x0, #0x13]
    // 0x9aec68: r16 = " "
    //     0x9aec68: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9aec6c: ArrayStore: r0[0] = r16  ; List_4
    //     0x9aec6c: stur            w16, [x0, #0x17]
    // 0x9aec70: str             x0, [SP]
    // 0x9aec74: r0 = _interpolate()
    //     0x9aec74: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9aec78: mov             x2, x0
    // 0x9aec7c: ldur            x0, [fp, #-8]
    // 0x9aec80: stur            x2, [fp, #-0x40]
    // 0x9aec84: LoadField: r1 = r0->field_f
    //     0x9aec84: ldur            w1, [x0, #0xf]
    // 0x9aec88: DecompressPointer r1
    //     0x9aec88: add             x1, x1, HEAP, lsl #32
    // 0x9aec8c: cmp             w1, NULL
    // 0x9aec90: b.eq            #0x9af684
    // 0x9aec94: r0 = of()
    //     0x9aec94: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9aec98: LoadField: r1 = r0->field_87
    //     0x9aec98: ldur            w1, [x0, #0x87]
    // 0x9aec9c: DecompressPointer r1
    //     0x9aec9c: add             x1, x1, HEAP, lsl #32
    // 0x9aeca0: LoadField: r0 = r1->field_2b
    //     0x9aeca0: ldur            w0, [x1, #0x2b]
    // 0x9aeca4: DecompressPointer r0
    //     0x9aeca4: add             x0, x0, HEAP, lsl #32
    // 0x9aeca8: ldur            x2, [fp, #-8]
    // 0x9aecac: stur            x0, [fp, #-0x58]
    // 0x9aecb0: LoadField: r1 = r2->field_b
    //     0x9aecb0: ldur            w1, [x2, #0xb]
    // 0x9aecb4: DecompressPointer r1
    //     0x9aecb4: add             x1, x1, HEAP, lsl #32
    // 0x9aecb8: cmp             w1, NULL
    // 0x9aecbc: b.eq            #0x9af688
    // 0x9aecc0: LoadField: r3 = r1->field_b
    //     0x9aecc0: ldur            w3, [x1, #0xb]
    // 0x9aecc4: DecompressPointer r3
    //     0x9aecc4: add             x3, x3, HEAP, lsl #32
    // 0x9aecc8: LoadField: r1 = r3->field_b
    //     0x9aecc8: ldur            w1, [x3, #0xb]
    // 0x9aeccc: DecompressPointer r1
    //     0x9aeccc: add             x1, x1, HEAP, lsl #32
    // 0x9aecd0: cmp             w1, NULL
    // 0x9aecd4: b.ne            #0x9aece0
    // 0x9aecd8: r1 = Null
    //     0x9aecd8: mov             x1, NULL
    // 0x9aecdc: b               #0x9aece8
    // 0x9aece0: r0 = ColorExtension.toColor()
    //     0x9aece0: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9aece4: mov             x1, x0
    // 0x9aece8: ldur            x2, [fp, #-0x50]
    // 0x9aecec: ldur            x0, [fp, #-0x40]
    // 0x9aecf0: r16 = 16.000000
    //     0x9aecf0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9aecf4: ldr             x16, [x16, #0x188]
    // 0x9aecf8: stp             x16, x1, [SP]
    // 0x9aecfc: ldur            x1, [fp, #-0x58]
    // 0x9aed00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9aed00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9aed04: ldr             x4, [x4, #0x9b8]
    // 0x9aed08: r0 = copyWith()
    //     0x9aed08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9aed0c: stur            x0, [fp, #-0x58]
    // 0x9aed10: r0 = Text()
    //     0x9aed10: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9aed14: mov             x2, x0
    // 0x9aed18: ldur            x0, [fp, #-0x40]
    // 0x9aed1c: stur            x2, [fp, #-0x60]
    // 0x9aed20: StoreField: r2->field_b = r0
    //     0x9aed20: stur            w0, [x2, #0xb]
    // 0x9aed24: ldur            x0, [fp, #-0x58]
    // 0x9aed28: StoreField: r2->field_13 = r0
    //     0x9aed28: stur            w0, [x2, #0x13]
    // 0x9aed2c: ldur            x0, [fp, #-0x50]
    // 0x9aed30: LoadField: r1 = r0->field_b
    //     0x9aed30: ldur            w1, [x0, #0xb]
    // 0x9aed34: LoadField: r3 = r0->field_f
    //     0x9aed34: ldur            w3, [x0, #0xf]
    // 0x9aed38: DecompressPointer r3
    //     0x9aed38: add             x3, x3, HEAP, lsl #32
    // 0x9aed3c: LoadField: r4 = r3->field_b
    //     0x9aed3c: ldur            w4, [x3, #0xb]
    // 0x9aed40: r3 = LoadInt32Instr(r1)
    //     0x9aed40: sbfx            x3, x1, #1, #0x1f
    // 0x9aed44: stur            x3, [fp, #-0x48]
    // 0x9aed48: r1 = LoadInt32Instr(r4)
    //     0x9aed48: sbfx            x1, x4, #1, #0x1f
    // 0x9aed4c: cmp             x3, x1
    // 0x9aed50: b.ne            #0x9aed5c
    // 0x9aed54: mov             x1, x0
    // 0x9aed58: r0 = _growToNextCapacity()
    //     0x9aed58: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9aed5c: ldur            x2, [fp, #-0x50]
    // 0x9aed60: ldur            x3, [fp, #-0x48]
    // 0x9aed64: add             x0, x3, #1
    // 0x9aed68: lsl             x1, x0, #1
    // 0x9aed6c: StoreField: r2->field_b = r1
    //     0x9aed6c: stur            w1, [x2, #0xb]
    // 0x9aed70: LoadField: r1 = r2->field_f
    //     0x9aed70: ldur            w1, [x2, #0xf]
    // 0x9aed74: DecompressPointer r1
    //     0x9aed74: add             x1, x1, HEAP, lsl #32
    // 0x9aed78: ldur            x0, [fp, #-0x60]
    // 0x9aed7c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9aed7c: add             x25, x1, x3, lsl #2
    //     0x9aed80: add             x25, x25, #0xf
    //     0x9aed84: str             w0, [x25]
    //     0x9aed88: tbz             w0, #0, #0x9aeda4
    //     0x9aed8c: ldurb           w16, [x1, #-1]
    //     0x9aed90: ldurb           w17, [x0, #-1]
    //     0x9aed94: and             x16, x17, x16, lsr #2
    //     0x9aed98: tst             x16, HEAP, lsr #32
    //     0x9aed9c: b.eq            #0x9aeda4
    //     0x9aeda0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9aeda4: b               #0x9aeed0
    // 0x9aeda8: ldur            x0, [fp, #-8]
    // 0x9aedac: mov             x2, x3
    // 0x9aedb0: LoadField: r1 = r0->field_f
    //     0x9aedb0: ldur            w1, [x0, #0xf]
    // 0x9aedb4: DecompressPointer r1
    //     0x9aedb4: add             x1, x1, HEAP, lsl #32
    // 0x9aedb8: cmp             w1, NULL
    // 0x9aedbc: b.eq            #0x9af68c
    // 0x9aedc0: r0 = of()
    //     0x9aedc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9aedc4: LoadField: r1 = r0->field_87
    //     0x9aedc4: ldur            w1, [x0, #0x87]
    // 0x9aedc8: DecompressPointer r1
    //     0x9aedc8: add             x1, x1, HEAP, lsl #32
    // 0x9aedcc: LoadField: r0 = r1->field_2b
    //     0x9aedcc: ldur            w0, [x1, #0x2b]
    // 0x9aedd0: DecompressPointer r0
    //     0x9aedd0: add             x0, x0, HEAP, lsl #32
    // 0x9aedd4: ldur            x2, [fp, #-8]
    // 0x9aedd8: stur            x0, [fp, #-0x40]
    // 0x9aeddc: LoadField: r1 = r2->field_b
    //     0x9aeddc: ldur            w1, [x2, #0xb]
    // 0x9aede0: DecompressPointer r1
    //     0x9aede0: add             x1, x1, HEAP, lsl #32
    // 0x9aede4: cmp             w1, NULL
    // 0x9aede8: b.eq            #0x9af690
    // 0x9aedec: LoadField: r3 = r1->field_b
    //     0x9aedec: ldur            w3, [x1, #0xb]
    // 0x9aedf0: DecompressPointer r3
    //     0x9aedf0: add             x3, x3, HEAP, lsl #32
    // 0x9aedf4: LoadField: r1 = r3->field_b
    //     0x9aedf4: ldur            w1, [x3, #0xb]
    // 0x9aedf8: DecompressPointer r1
    //     0x9aedf8: add             x1, x1, HEAP, lsl #32
    // 0x9aedfc: cmp             w1, NULL
    // 0x9aee00: b.ne            #0x9aee0c
    // 0x9aee04: r1 = Null
    //     0x9aee04: mov             x1, NULL
    // 0x9aee08: b               #0x9aee14
    // 0x9aee0c: r0 = ColorExtension.toColor()
    //     0x9aee0c: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9aee10: mov             x1, x0
    // 0x9aee14: ldur            x0, [fp, #-0x50]
    // 0x9aee18: r16 = 16.000000
    //     0x9aee18: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9aee1c: ldr             x16, [x16, #0x188]
    // 0x9aee20: stp             x16, x1, [SP]
    // 0x9aee24: ldur            x1, [fp, #-0x40]
    // 0x9aee28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9aee28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9aee2c: ldr             x4, [x4, #0x9b8]
    // 0x9aee30: r0 = copyWith()
    //     0x9aee30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9aee34: stur            x0, [fp, #-0x40]
    // 0x9aee38: r0 = Text()
    //     0x9aee38: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9aee3c: mov             x2, x0
    // 0x9aee40: r0 = "00"
    //     0x9aee40: add             x0, PP, #0x26, lsl #12  ; [pp+0x261d8] "00"
    //     0x9aee44: ldr             x0, [x0, #0x1d8]
    // 0x9aee48: stur            x2, [fp, #-0x58]
    // 0x9aee4c: StoreField: r2->field_b = r0
    //     0x9aee4c: stur            w0, [x2, #0xb]
    // 0x9aee50: ldur            x0, [fp, #-0x40]
    // 0x9aee54: StoreField: r2->field_13 = r0
    //     0x9aee54: stur            w0, [x2, #0x13]
    // 0x9aee58: ldur            x0, [fp, #-0x50]
    // 0x9aee5c: LoadField: r1 = r0->field_b
    //     0x9aee5c: ldur            w1, [x0, #0xb]
    // 0x9aee60: LoadField: r3 = r0->field_f
    //     0x9aee60: ldur            w3, [x0, #0xf]
    // 0x9aee64: DecompressPointer r3
    //     0x9aee64: add             x3, x3, HEAP, lsl #32
    // 0x9aee68: LoadField: r4 = r3->field_b
    //     0x9aee68: ldur            w4, [x3, #0xb]
    // 0x9aee6c: r3 = LoadInt32Instr(r1)
    //     0x9aee6c: sbfx            x3, x1, #1, #0x1f
    // 0x9aee70: stur            x3, [fp, #-0x48]
    // 0x9aee74: r1 = LoadInt32Instr(r4)
    //     0x9aee74: sbfx            x1, x4, #1, #0x1f
    // 0x9aee78: cmp             x3, x1
    // 0x9aee7c: b.ne            #0x9aee88
    // 0x9aee80: mov             x1, x0
    // 0x9aee84: r0 = _growToNextCapacity()
    //     0x9aee84: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9aee88: ldur            x2, [fp, #-0x50]
    // 0x9aee8c: ldur            x3, [fp, #-0x48]
    // 0x9aee90: add             x0, x3, #1
    // 0x9aee94: lsl             x1, x0, #1
    // 0x9aee98: StoreField: r2->field_b = r1
    //     0x9aee98: stur            w1, [x2, #0xb]
    // 0x9aee9c: LoadField: r1 = r2->field_f
    //     0x9aee9c: ldur            w1, [x2, #0xf]
    // 0x9aeea0: DecompressPointer r1
    //     0x9aeea0: add             x1, x1, HEAP, lsl #32
    // 0x9aeea4: ldur            x0, [fp, #-0x58]
    // 0x9aeea8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9aeea8: add             x25, x1, x3, lsl #2
    //     0x9aeeac: add             x25, x25, #0xf
    //     0x9aeeb0: str             w0, [x25]
    //     0x9aeeb4: tbz             w0, #0, #0x9aeed0
    //     0x9aeeb8: ldurb           w16, [x1, #-1]
    //     0x9aeebc: ldurb           w17, [x0, #-1]
    //     0x9aeec0: and             x16, x17, x16, lsr #2
    //     0x9aeec4: tst             x16, HEAP, lsr #32
    //     0x9aeec8: b.eq            #0x9aeed0
    //     0x9aeecc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9aeed0: ldur            x0, [fp, #-8]
    // 0x9aeed4: LoadField: r1 = r0->field_f
    //     0x9aeed4: ldur            w1, [x0, #0xf]
    // 0x9aeed8: DecompressPointer r1
    //     0x9aeed8: add             x1, x1, HEAP, lsl #32
    // 0x9aeedc: cmp             w1, NULL
    // 0x9aeee0: b.eq            #0x9af694
    // 0x9aeee4: r0 = of()
    //     0x9aeee4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9aeee8: LoadField: r1 = r0->field_87
    //     0x9aeee8: ldur            w1, [x0, #0x87]
    // 0x9aeeec: DecompressPointer r1
    //     0x9aeeec: add             x1, x1, HEAP, lsl #32
    // 0x9aeef0: LoadField: r0 = r1->field_2b
    //     0x9aeef0: ldur            w0, [x1, #0x2b]
    // 0x9aeef4: DecompressPointer r0
    //     0x9aeef4: add             x0, x0, HEAP, lsl #32
    // 0x9aeef8: ldur            x2, [fp, #-8]
    // 0x9aeefc: stur            x0, [fp, #-0x40]
    // 0x9aef00: LoadField: r1 = r2->field_b
    //     0x9aef00: ldur            w1, [x2, #0xb]
    // 0x9aef04: DecompressPointer r1
    //     0x9aef04: add             x1, x1, HEAP, lsl #32
    // 0x9aef08: cmp             w1, NULL
    // 0x9aef0c: b.eq            #0x9af698
    // 0x9aef10: LoadField: r3 = r1->field_b
    //     0x9aef10: ldur            w3, [x1, #0xb]
    // 0x9aef14: DecompressPointer r3
    //     0x9aef14: add             x3, x3, HEAP, lsl #32
    // 0x9aef18: LoadField: r1 = r3->field_b
    //     0x9aef18: ldur            w1, [x3, #0xb]
    // 0x9aef1c: DecompressPointer r1
    //     0x9aef1c: add             x1, x1, HEAP, lsl #32
    // 0x9aef20: cmp             w1, NULL
    // 0x9aef24: b.ne            #0x9aef30
    // 0x9aef28: r1 = Null
    //     0x9aef28: mov             x1, NULL
    // 0x9aef2c: b               #0x9aef38
    // 0x9aef30: r0 = ColorExtension.toColor()
    //     0x9aef30: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9aef34: mov             x1, x0
    // 0x9aef38: ldur            x0, [fp, #-0x50]
    // 0x9aef3c: r16 = 12.000000
    //     0x9aef3c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9aef40: ldr             x16, [x16, #0x9e8]
    // 0x9aef44: stp             x16, x1, [SP]
    // 0x9aef48: ldur            x1, [fp, #-0x40]
    // 0x9aef4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9aef4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9aef50: ldr             x4, [x4, #0x9b8]
    // 0x9aef54: r0 = copyWith()
    //     0x9aef54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9aef58: stur            x0, [fp, #-0x40]
    // 0x9aef5c: r0 = Text()
    //     0x9aef5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9aef60: mov             x2, x0
    // 0x9aef64: r0 = "M"
    //     0x9aef64: add             x0, PP, #0x49, lsl #12  ; [pp+0x49780] "M"
    //     0x9aef68: ldr             x0, [x0, #0x780]
    // 0x9aef6c: stur            x2, [fp, #-0x58]
    // 0x9aef70: StoreField: r2->field_b = r0
    //     0x9aef70: stur            w0, [x2, #0xb]
    // 0x9aef74: ldur            x0, [fp, #-0x40]
    // 0x9aef78: StoreField: r2->field_13 = r0
    //     0x9aef78: stur            w0, [x2, #0x13]
    // 0x9aef7c: ldur            x0, [fp, #-0x50]
    // 0x9aef80: LoadField: r1 = r0->field_b
    //     0x9aef80: ldur            w1, [x0, #0xb]
    // 0x9aef84: LoadField: r3 = r0->field_f
    //     0x9aef84: ldur            w3, [x0, #0xf]
    // 0x9aef88: DecompressPointer r3
    //     0x9aef88: add             x3, x3, HEAP, lsl #32
    // 0x9aef8c: LoadField: r4 = r3->field_b
    //     0x9aef8c: ldur            w4, [x3, #0xb]
    // 0x9aef90: r3 = LoadInt32Instr(r1)
    //     0x9aef90: sbfx            x3, x1, #1, #0x1f
    // 0x9aef94: stur            x3, [fp, #-0x48]
    // 0x9aef98: r1 = LoadInt32Instr(r4)
    //     0x9aef98: sbfx            x1, x4, #1, #0x1f
    // 0x9aef9c: cmp             x3, x1
    // 0x9aefa0: b.ne            #0x9aefac
    // 0x9aefa4: mov             x1, x0
    // 0x9aefa8: r0 = _growToNextCapacity()
    //     0x9aefa8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9aefac: ldur            x4, [fp, #-8]
    // 0x9aefb0: ldur            x2, [fp, #-0x50]
    // 0x9aefb4: ldur            x3, [fp, #-0x48]
    // 0x9aefb8: add             x0, x3, #1
    // 0x9aefbc: lsl             x1, x0, #1
    // 0x9aefc0: StoreField: r2->field_b = r1
    //     0x9aefc0: stur            w1, [x2, #0xb]
    // 0x9aefc4: LoadField: r1 = r2->field_f
    //     0x9aefc4: ldur            w1, [x2, #0xf]
    // 0x9aefc8: DecompressPointer r1
    //     0x9aefc8: add             x1, x1, HEAP, lsl #32
    // 0x9aefcc: ldur            x0, [fp, #-0x58]
    // 0x9aefd0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9aefd0: add             x25, x1, x3, lsl #2
    //     0x9aefd4: add             x25, x25, #0xf
    //     0x9aefd8: str             w0, [x25]
    //     0x9aefdc: tbz             w0, #0, #0x9aeff8
    //     0x9aefe0: ldurb           w16, [x1, #-1]
    //     0x9aefe4: ldurb           w17, [x0, #-1]
    //     0x9aefe8: and             x16, x17, x16, lsr #2
    //     0x9aefec: tst             x16, HEAP, lsr #32
    //     0x9aeff0: b.eq            #0x9aeff8
    //     0x9aeff4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9aeff8: r0 = Column()
    //     0x9aeff8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9aeffc: mov             x2, x0
    // 0x9af000: r0 = Instance_Axis
    //     0x9af000: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9af004: stur            x2, [fp, #-0x40]
    // 0x9af008: StoreField: r2->field_f = r0
    //     0x9af008: stur            w0, [x2, #0xf]
    // 0x9af00c: r3 = Instance_MainAxisAlignment
    //     0x9af00c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9af010: ldr             x3, [x3, #0xab0]
    // 0x9af014: StoreField: r2->field_13 = r3
    //     0x9af014: stur            w3, [x2, #0x13]
    // 0x9af018: r4 = Instance_MainAxisSize
    //     0x9af018: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9af01c: ldr             x4, [x4, #0xa10]
    // 0x9af020: ArrayStore: r2[0] = r4  ; List_4
    //     0x9af020: stur            w4, [x2, #0x17]
    // 0x9af024: r5 = Instance_CrossAxisAlignment
    //     0x9af024: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9af028: ldr             x5, [x5, #0xa18]
    // 0x9af02c: StoreField: r2->field_1b = r5
    //     0x9af02c: stur            w5, [x2, #0x1b]
    // 0x9af030: r6 = Instance_VerticalDirection
    //     0x9af030: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9af034: ldr             x6, [x6, #0xa20]
    // 0x9af038: StoreField: r2->field_23 = r6
    //     0x9af038: stur            w6, [x2, #0x23]
    // 0x9af03c: r7 = Instance_Clip
    //     0x9af03c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9af040: ldr             x7, [x7, #0x38]
    // 0x9af044: StoreField: r2->field_2b = r7
    //     0x9af044: stur            w7, [x2, #0x2b]
    // 0x9af048: StoreField: r2->field_2f = rZR
    //     0x9af048: stur            xzr, [x2, #0x2f]
    // 0x9af04c: ldur            x1, [fp, #-0x50]
    // 0x9af050: StoreField: r2->field_b = r1
    //     0x9af050: stur            w1, [x2, #0xb]
    // 0x9af054: ldur            x8, [fp, #-8]
    // 0x9af058: LoadField: r1 = r8->field_f
    //     0x9af058: ldur            w1, [x8, #0xf]
    // 0x9af05c: DecompressPointer r1
    //     0x9af05c: add             x1, x1, HEAP, lsl #32
    // 0x9af060: cmp             w1, NULL
    // 0x9af064: b.eq            #0x9af69c
    // 0x9af068: r0 = of()
    //     0x9af068: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9af06c: LoadField: r1 = r0->field_87
    //     0x9af06c: ldur            w1, [x0, #0x87]
    // 0x9af070: DecompressPointer r1
    //     0x9af070: add             x1, x1, HEAP, lsl #32
    // 0x9af074: LoadField: r0 = r1->field_7
    //     0x9af074: ldur            w0, [x1, #7]
    // 0x9af078: DecompressPointer r0
    //     0x9af078: add             x0, x0, HEAP, lsl #32
    // 0x9af07c: ldur            x2, [fp, #-8]
    // 0x9af080: stur            x0, [fp, #-0x50]
    // 0x9af084: LoadField: r1 = r2->field_b
    //     0x9af084: ldur            w1, [x2, #0xb]
    // 0x9af088: DecompressPointer r1
    //     0x9af088: add             x1, x1, HEAP, lsl #32
    // 0x9af08c: cmp             w1, NULL
    // 0x9af090: b.eq            #0x9af6a0
    // 0x9af094: LoadField: r3 = r1->field_b
    //     0x9af094: ldur            w3, [x1, #0xb]
    // 0x9af098: DecompressPointer r3
    //     0x9af098: add             x3, x3, HEAP, lsl #32
    // 0x9af09c: LoadField: r1 = r3->field_b
    //     0x9af09c: ldur            w1, [x3, #0xb]
    // 0x9af0a0: DecompressPointer r1
    //     0x9af0a0: add             x1, x1, HEAP, lsl #32
    // 0x9af0a4: cmp             w1, NULL
    // 0x9af0a8: b.ne            #0x9af0b4
    // 0x9af0ac: r1 = Null
    //     0x9af0ac: mov             x1, NULL
    // 0x9af0b0: b               #0x9af0bc
    // 0x9af0b4: r0 = ColorExtension.toColor()
    //     0x9af0b4: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9af0b8: mov             x1, x0
    // 0x9af0bc: ldur            x0, [fp, #-0x10]
    // 0x9af0c0: r16 = 16.000000
    //     0x9af0c0: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9af0c4: ldr             x16, [x16, #0x188]
    // 0x9af0c8: stp             x16, x1, [SP]
    // 0x9af0cc: ldur            x1, [fp, #-0x50]
    // 0x9af0d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9af0d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9af0d4: ldr             x4, [x4, #0x9b8]
    // 0x9af0d8: r0 = copyWith()
    //     0x9af0d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9af0dc: stur            x0, [fp, #-0x50]
    // 0x9af0e0: r0 = Text()
    //     0x9af0e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9af0e4: mov             x1, x0
    // 0x9af0e8: r0 = ":"
    //     0x9af0e8: ldr             x0, [PP, #0xd50]  ; [pp+0xd50] ":"
    // 0x9af0ec: stur            x1, [fp, #-0x58]
    // 0x9af0f0: StoreField: r1->field_b = r0
    //     0x9af0f0: stur            w0, [x1, #0xb]
    // 0x9af0f4: ldur            x0, [fp, #-0x50]
    // 0x9af0f8: StoreField: r1->field_13 = r0
    //     0x9af0f8: stur            w0, [x1, #0x13]
    // 0x9af0fc: r0 = Padding()
    //     0x9af0fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x9af100: mov             x3, x0
    // 0x9af104: r0 = Instance_EdgeInsets
    //     0x9af104: add             x0, PP, #0x5b, lsl #12  ; [pp+0x5bb20] Obj!EdgeInsets@d57771
    //     0x9af108: ldr             x0, [x0, #0xb20]
    // 0x9af10c: stur            x3, [fp, #-0x50]
    // 0x9af110: StoreField: r3->field_f = r0
    //     0x9af110: stur            w0, [x3, #0xf]
    // 0x9af114: ldur            x0, [fp, #-0x58]
    // 0x9af118: StoreField: r3->field_b = r0
    //     0x9af118: stur            w0, [x3, #0xb]
    // 0x9af11c: ldur            x0, [fp, #-0x10]
    // 0x9af120: LoadField: r4 = r0->field_13
    //     0x9af120: ldur            x4, [x0, #0x13]
    // 0x9af124: stur            x4, [fp, #-0x48]
    // 0x9af128: cmp             x4, #0xa
    // 0x9af12c: b.ge            #0x9af180
    // 0x9af130: r1 = Null
    //     0x9af130: mov             x1, NULL
    // 0x9af134: r2 = 6
    //     0x9af134: movz            x2, #0x6
    // 0x9af138: r0 = AllocateArray()
    //     0x9af138: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9af13c: mov             x2, x0
    // 0x9af140: r16 = " 0"
    //     0x9af140: add             x16, PP, #8, lsl #12  ; [pp+0x8df0] " 0"
    //     0x9af144: ldr             x16, [x16, #0xdf0]
    // 0x9af148: StoreField: r2->field_f = r16
    //     0x9af148: stur            w16, [x2, #0xf]
    // 0x9af14c: ldur            x3, [fp, #-0x48]
    // 0x9af150: r0 = BoxInt64Instr(r3)
    //     0x9af150: sbfiz           x0, x3, #1, #0x1f
    //     0x9af154: cmp             x3, x0, asr #1
    //     0x9af158: b.eq            #0x9af164
    //     0x9af15c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9af160: stur            x3, [x0, #7]
    // 0x9af164: StoreField: r2->field_13 = r0
    //     0x9af164: stur            w0, [x2, #0x13]
    // 0x9af168: r16 = " "
    //     0x9af168: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9af16c: ArrayStore: r2[0] = r16  ; List_4
    //     0x9af16c: stur            w16, [x2, #0x17]
    // 0x9af170: str             x2, [SP]
    // 0x9af174: r0 = _interpolate()
    //     0x9af174: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9af178: mov             x2, x0
    // 0x9af17c: b               #0x9af1cc
    // 0x9af180: mov             x3, x4
    // 0x9af184: r1 = Null
    //     0x9af184: mov             x1, NULL
    // 0x9af188: r2 = 6
    //     0x9af188: movz            x2, #0x6
    // 0x9af18c: r0 = AllocateArray()
    //     0x9af18c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9af190: mov             x2, x0
    // 0x9af194: r16 = " "
    //     0x9af194: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9af198: StoreField: r2->field_f = r16
    //     0x9af198: stur            w16, [x2, #0xf]
    // 0x9af19c: ldur            x3, [fp, #-0x48]
    // 0x9af1a0: r0 = BoxInt64Instr(r3)
    //     0x9af1a0: sbfiz           x0, x3, #1, #0x1f
    //     0x9af1a4: cmp             x3, x0, asr #1
    //     0x9af1a8: b.eq            #0x9af1b4
    //     0x9af1ac: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9af1b0: stur            x3, [x0, #7]
    // 0x9af1b4: StoreField: r2->field_13 = r0
    //     0x9af1b4: stur            w0, [x2, #0x13]
    // 0x9af1b8: r16 = " "
    //     0x9af1b8: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x9af1bc: ArrayStore: r2[0] = r16  ; List_4
    //     0x9af1bc: stur            w16, [x2, #0x17]
    // 0x9af1c0: str             x2, [SP]
    // 0x9af1c4: r0 = _interpolate()
    //     0x9af1c4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x9af1c8: mov             x2, x0
    // 0x9af1cc: ldur            x0, [fp, #-8]
    // 0x9af1d0: stur            x2, [fp, #-0x10]
    // 0x9af1d4: LoadField: r1 = r0->field_f
    //     0x9af1d4: ldur            w1, [x0, #0xf]
    // 0x9af1d8: DecompressPointer r1
    //     0x9af1d8: add             x1, x1, HEAP, lsl #32
    // 0x9af1dc: cmp             w1, NULL
    // 0x9af1e0: b.eq            #0x9af6a4
    // 0x9af1e4: r0 = of()
    //     0x9af1e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9af1e8: LoadField: r1 = r0->field_87
    //     0x9af1e8: ldur            w1, [x0, #0x87]
    // 0x9af1ec: DecompressPointer r1
    //     0x9af1ec: add             x1, x1, HEAP, lsl #32
    // 0x9af1f0: LoadField: r0 = r1->field_2b
    //     0x9af1f0: ldur            w0, [x1, #0x2b]
    // 0x9af1f4: DecompressPointer r0
    //     0x9af1f4: add             x0, x0, HEAP, lsl #32
    // 0x9af1f8: ldur            x2, [fp, #-8]
    // 0x9af1fc: stur            x0, [fp, #-0x58]
    // 0x9af200: LoadField: r1 = r2->field_b
    //     0x9af200: ldur            w1, [x2, #0xb]
    // 0x9af204: DecompressPointer r1
    //     0x9af204: add             x1, x1, HEAP, lsl #32
    // 0x9af208: cmp             w1, NULL
    // 0x9af20c: b.eq            #0x9af6a8
    // 0x9af210: LoadField: r3 = r1->field_b
    //     0x9af210: ldur            w3, [x1, #0xb]
    // 0x9af214: DecompressPointer r3
    //     0x9af214: add             x3, x3, HEAP, lsl #32
    // 0x9af218: LoadField: r1 = r3->field_b
    //     0x9af218: ldur            w1, [x3, #0xb]
    // 0x9af21c: DecompressPointer r1
    //     0x9af21c: add             x1, x1, HEAP, lsl #32
    // 0x9af220: cmp             w1, NULL
    // 0x9af224: b.ne            #0x9af234
    // 0x9af228: mov             x0, x2
    // 0x9af22c: r1 = Null
    //     0x9af22c: mov             x1, NULL
    // 0x9af230: b               #0x9af240
    // 0x9af234: r0 = ColorExtension.toColor()
    //     0x9af234: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9af238: mov             x1, x0
    // 0x9af23c: ldur            x0, [fp, #-8]
    // 0x9af240: ldur            x2, [fp, #-0x10]
    // 0x9af244: r16 = 16.000000
    //     0x9af244: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x9af248: ldr             x16, [x16, #0x188]
    // 0x9af24c: stp             x16, x1, [SP]
    // 0x9af250: ldur            x1, [fp, #-0x58]
    // 0x9af254: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9af254: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9af258: ldr             x4, [x4, #0x9b8]
    // 0x9af25c: r0 = copyWith()
    //     0x9af25c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9af260: stur            x0, [fp, #-0x58]
    // 0x9af264: r0 = Text()
    //     0x9af264: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9af268: mov             x2, x0
    // 0x9af26c: ldur            x0, [fp, #-0x10]
    // 0x9af270: stur            x2, [fp, #-0x60]
    // 0x9af274: StoreField: r2->field_b = r0
    //     0x9af274: stur            w0, [x2, #0xb]
    // 0x9af278: ldur            x0, [fp, #-0x58]
    // 0x9af27c: StoreField: r2->field_13 = r0
    //     0x9af27c: stur            w0, [x2, #0x13]
    // 0x9af280: ldur            x0, [fp, #-8]
    // 0x9af284: LoadField: r1 = r0->field_f
    //     0x9af284: ldur            w1, [x0, #0xf]
    // 0x9af288: DecompressPointer r1
    //     0x9af288: add             x1, x1, HEAP, lsl #32
    // 0x9af28c: cmp             w1, NULL
    // 0x9af290: b.eq            #0x9af6ac
    // 0x9af294: r0 = of()
    //     0x9af294: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x9af298: LoadField: r1 = r0->field_87
    //     0x9af298: ldur            w1, [x0, #0x87]
    // 0x9af29c: DecompressPointer r1
    //     0x9af29c: add             x1, x1, HEAP, lsl #32
    // 0x9af2a0: LoadField: r0 = r1->field_2b
    //     0x9af2a0: ldur            w0, [x1, #0x2b]
    // 0x9af2a4: DecompressPointer r0
    //     0x9af2a4: add             x0, x0, HEAP, lsl #32
    // 0x9af2a8: ldur            x1, [fp, #-8]
    // 0x9af2ac: stur            x0, [fp, #-0x10]
    // 0x9af2b0: LoadField: r2 = r1->field_b
    //     0x9af2b0: ldur            w2, [x1, #0xb]
    // 0x9af2b4: DecompressPointer r2
    //     0x9af2b4: add             x2, x2, HEAP, lsl #32
    // 0x9af2b8: cmp             w2, NULL
    // 0x9af2bc: b.eq            #0x9af6b0
    // 0x9af2c0: LoadField: r1 = r2->field_b
    //     0x9af2c0: ldur            w1, [x2, #0xb]
    // 0x9af2c4: DecompressPointer r1
    //     0x9af2c4: add             x1, x1, HEAP, lsl #32
    // 0x9af2c8: LoadField: r2 = r1->field_b
    //     0x9af2c8: ldur            w2, [x1, #0xb]
    // 0x9af2cc: DecompressPointer r2
    //     0x9af2cc: add             x2, x2, HEAP, lsl #32
    // 0x9af2d0: cmp             w2, NULL
    // 0x9af2d4: b.ne            #0x9af2e0
    // 0x9af2d8: r1 = Null
    //     0x9af2d8: mov             x1, NULL
    // 0x9af2dc: b               #0x9af2ec
    // 0x9af2e0: mov             x1, x2
    // 0x9af2e4: r0 = ColorExtension.toColor()
    //     0x9af2e4: bl              #0x9af70c  ; [package:customer_app/app/core/extension/extension_function.dart] ::ColorExtension.toColor
    // 0x9af2e8: mov             x1, x0
    // 0x9af2ec: ldur            x7, [fp, #-0x30]
    // 0x9af2f0: ldur            d0, [fp, #-0x70]
    // 0x9af2f4: ldur            x6, [fp, #-0x18]
    // 0x9af2f8: ldur            x5, [fp, #-0x20]
    // 0x9af2fc: ldur            x4, [fp, #-0x38]
    // 0x9af300: ldur            x3, [fp, #-0x40]
    // 0x9af304: ldur            x2, [fp, #-0x50]
    // 0x9af308: ldur            x0, [fp, #-0x60]
    // 0x9af30c: ldur            d1, [fp, #-0x68]
    // 0x9af310: r16 = 12.000000
    //     0x9af310: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x9af314: ldr             x16, [x16, #0x9e8]
    // 0x9af318: stp             x16, x1, [SP]
    // 0x9af31c: ldur            x1, [fp, #-0x10]
    // 0x9af320: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x9af320: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x9af324: ldr             x4, [x4, #0x9b8]
    // 0x9af328: r0 = copyWith()
    //     0x9af328: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x9af32c: stur            x0, [fp, #-8]
    // 0x9af330: r0 = Text()
    //     0x9af330: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x9af334: mov             x3, x0
    // 0x9af338: r0 = "S"
    //     0x9af338: add             x0, PP, #0x49, lsl #12  ; [pp+0x49788] "S"
    //     0x9af33c: ldr             x0, [x0, #0x788]
    // 0x9af340: stur            x3, [fp, #-0x10]
    // 0x9af344: StoreField: r3->field_b = r0
    //     0x9af344: stur            w0, [x3, #0xb]
    // 0x9af348: ldur            x0, [fp, #-8]
    // 0x9af34c: StoreField: r3->field_13 = r0
    //     0x9af34c: stur            w0, [x3, #0x13]
    // 0x9af350: r1 = Null
    //     0x9af350: mov             x1, NULL
    // 0x9af354: r2 = 4
    //     0x9af354: movz            x2, #0x4
    // 0x9af358: r0 = AllocateArray()
    //     0x9af358: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9af35c: mov             x2, x0
    // 0x9af360: ldur            x0, [fp, #-0x60]
    // 0x9af364: stur            x2, [fp, #-8]
    // 0x9af368: StoreField: r2->field_f = r0
    //     0x9af368: stur            w0, [x2, #0xf]
    // 0x9af36c: ldur            x0, [fp, #-0x10]
    // 0x9af370: StoreField: r2->field_13 = r0
    //     0x9af370: stur            w0, [x2, #0x13]
    // 0x9af374: r1 = <Widget>
    //     0x9af374: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9af378: r0 = AllocateGrowableArray()
    //     0x9af378: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9af37c: mov             x1, x0
    // 0x9af380: ldur            x0, [fp, #-8]
    // 0x9af384: stur            x1, [fp, #-0x10]
    // 0x9af388: StoreField: r1->field_f = r0
    //     0x9af388: stur            w0, [x1, #0xf]
    // 0x9af38c: r0 = 4
    //     0x9af38c: movz            x0, #0x4
    // 0x9af390: StoreField: r1->field_b = r0
    //     0x9af390: stur            w0, [x1, #0xb]
    // 0x9af394: r0 = Column()
    //     0x9af394: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9af398: mov             x3, x0
    // 0x9af39c: r0 = Instance_Axis
    //     0x9af39c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9af3a0: stur            x3, [fp, #-8]
    // 0x9af3a4: StoreField: r3->field_f = r0
    //     0x9af3a4: stur            w0, [x3, #0xf]
    // 0x9af3a8: r4 = Instance_MainAxisAlignment
    //     0x9af3a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9af3ac: ldr             x4, [x4, #0xab0]
    // 0x9af3b0: StoreField: r3->field_13 = r4
    //     0x9af3b0: stur            w4, [x3, #0x13]
    // 0x9af3b4: r5 = Instance_MainAxisSize
    //     0x9af3b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9af3b8: ldr             x5, [x5, #0xa10]
    // 0x9af3bc: ArrayStore: r3[0] = r5  ; List_4
    //     0x9af3bc: stur            w5, [x3, #0x17]
    // 0x9af3c0: r6 = Instance_CrossAxisAlignment
    //     0x9af3c0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9af3c4: ldr             x6, [x6, #0xa18]
    // 0x9af3c8: StoreField: r3->field_1b = r6
    //     0x9af3c8: stur            w6, [x3, #0x1b]
    // 0x9af3cc: r7 = Instance_VerticalDirection
    //     0x9af3cc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9af3d0: ldr             x7, [x7, #0xa20]
    // 0x9af3d4: StoreField: r3->field_23 = r7
    //     0x9af3d4: stur            w7, [x3, #0x23]
    // 0x9af3d8: r8 = Instance_Clip
    //     0x9af3d8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9af3dc: ldr             x8, [x8, #0x38]
    // 0x9af3e0: StoreField: r3->field_2b = r8
    //     0x9af3e0: stur            w8, [x3, #0x2b]
    // 0x9af3e4: StoreField: r3->field_2f = rZR
    //     0x9af3e4: stur            xzr, [x3, #0x2f]
    // 0x9af3e8: ldur            x1, [fp, #-0x10]
    // 0x9af3ec: StoreField: r3->field_b = r1
    //     0x9af3ec: stur            w1, [x3, #0xb]
    // 0x9af3f0: r1 = Null
    //     0x9af3f0: mov             x1, NULL
    // 0x9af3f4: r2 = 16
    //     0x9af3f4: movz            x2, #0x10
    // 0x9af3f8: r0 = AllocateArray()
    //     0x9af3f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9af3fc: mov             x2, x0
    // 0x9af400: ldur            x0, [fp, #-0x18]
    // 0x9af404: stur            x2, [fp, #-0x10]
    // 0x9af408: StoreField: r2->field_f = r0
    //     0x9af408: stur            w0, [x2, #0xf]
    // 0x9af40c: ldur            x0, [fp, #-0x20]
    // 0x9af410: StoreField: r2->field_13 = r0
    //     0x9af410: stur            w0, [x2, #0x13]
    // 0x9af414: r16 = Instance_SizedBox
    //     0x9af414: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x9af418: ldr             x16, [x16, #0xb20]
    // 0x9af41c: ArrayStore: r2[0] = r16  ; List_4
    //     0x9af41c: stur            w16, [x2, #0x17]
    // 0x9af420: ldur            x0, [fp, #-0x38]
    // 0x9af424: StoreField: r2->field_1b = r0
    //     0x9af424: stur            w0, [x2, #0x1b]
    // 0x9af428: ldur            x0, [fp, #-0x40]
    // 0x9af42c: StoreField: r2->field_1f = r0
    //     0x9af42c: stur            w0, [x2, #0x1f]
    // 0x9af430: r16 = Instance_SizedBox
    //     0x9af430: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x9af434: ldr             x16, [x16, #0xb20]
    // 0x9af438: StoreField: r2->field_23 = r16
    //     0x9af438: stur            w16, [x2, #0x23]
    // 0x9af43c: ldur            x0, [fp, #-0x50]
    // 0x9af440: StoreField: r2->field_27 = r0
    //     0x9af440: stur            w0, [x2, #0x27]
    // 0x9af444: ldur            x0, [fp, #-8]
    // 0x9af448: StoreField: r2->field_2b = r0
    //     0x9af448: stur            w0, [x2, #0x2b]
    // 0x9af44c: r1 = <Widget>
    //     0x9af44c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9af450: r0 = AllocateGrowableArray()
    //     0x9af450: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9af454: mov             x1, x0
    // 0x9af458: ldur            x0, [fp, #-0x10]
    // 0x9af45c: stur            x1, [fp, #-8]
    // 0x9af460: StoreField: r1->field_f = r0
    //     0x9af460: stur            w0, [x1, #0xf]
    // 0x9af464: r0 = 16
    //     0x9af464: movz            x0, #0x10
    // 0x9af468: StoreField: r1->field_b = r0
    //     0x9af468: stur            w0, [x1, #0xb]
    // 0x9af46c: r0 = Row()
    //     0x9af46c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x9af470: mov             x1, x0
    // 0x9af474: r0 = Instance_Axis
    //     0x9af474: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x9af478: stur            x1, [fp, #-0x18]
    // 0x9af47c: StoreField: r1->field_f = r0
    //     0x9af47c: stur            w0, [x1, #0xf]
    // 0x9af480: r0 = Instance_MainAxisAlignment
    //     0x9af480: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9af484: ldr             x0, [x0, #0xab0]
    // 0x9af488: StoreField: r1->field_13 = r0
    //     0x9af488: stur            w0, [x1, #0x13]
    // 0x9af48c: r2 = Instance_MainAxisSize
    //     0x9af48c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x9af490: ldr             x2, [x2, #0xdd0]
    // 0x9af494: ArrayStore: r1[0] = r2  ; List_4
    //     0x9af494: stur            w2, [x1, #0x17]
    // 0x9af498: r2 = Instance_CrossAxisAlignment
    //     0x9af498: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9af49c: ldr             x2, [x2, #0xa18]
    // 0x9af4a0: StoreField: r1->field_1b = r2
    //     0x9af4a0: stur            w2, [x1, #0x1b]
    // 0x9af4a4: r3 = Instance_VerticalDirection
    //     0x9af4a4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9af4a8: ldr             x3, [x3, #0xa20]
    // 0x9af4ac: StoreField: r1->field_23 = r3
    //     0x9af4ac: stur            w3, [x1, #0x23]
    // 0x9af4b0: r4 = Instance_Clip
    //     0x9af4b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9af4b4: ldr             x4, [x4, #0x38]
    // 0x9af4b8: StoreField: r1->field_2b = r4
    //     0x9af4b8: stur            w4, [x1, #0x2b]
    // 0x9af4bc: StoreField: r1->field_2f = rZR
    //     0x9af4bc: stur            xzr, [x1, #0x2f]
    // 0x9af4c0: ldur            x5, [fp, #-8]
    // 0x9af4c4: StoreField: r1->field_b = r5
    //     0x9af4c4: stur            w5, [x1, #0xb]
    // 0x9af4c8: ldur            d0, [fp, #-0x68]
    // 0x9af4cc: r5 = inline_Allocate_Double()
    //     0x9af4cc: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0x9af4d0: add             x5, x5, #0x10
    //     0x9af4d4: cmp             x6, x5
    //     0x9af4d8: b.ls            #0x9af6b4
    //     0x9af4dc: str             x5, [THR, #0x50]  ; THR::top
    //     0x9af4e0: sub             x5, x5, #0xf
    //     0x9af4e4: movz            x6, #0xe15c
    //     0x9af4e8: movk            x6, #0x3, lsl #16
    //     0x9af4ec: stur            x6, [x5, #-1]
    // 0x9af4f0: StoreField: r5->field_7 = d0
    //     0x9af4f0: stur            d0, [x5, #7]
    // 0x9af4f4: ldur            d0, [fp, #-0x70]
    // 0x9af4f8: stur            x5, [fp, #-0x10]
    // 0x9af4fc: r6 = inline_Allocate_Double()
    //     0x9af4fc: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x9af500: add             x6, x6, #0x10
    //     0x9af504: cmp             x7, x6
    //     0x9af508: b.ls            #0x9af6e0
    //     0x9af50c: str             x6, [THR, #0x50]  ; THR::top
    //     0x9af510: sub             x6, x6, #0xf
    //     0x9af514: movz            x7, #0xe15c
    //     0x9af518: movk            x7, #0x3, lsl #16
    //     0x9af51c: stur            x7, [x6, #-1]
    // 0x9af520: StoreField: r6->field_7 = d0
    //     0x9af520: stur            d0, [x6, #7]
    // 0x9af524: stur            x6, [fp, #-8]
    // 0x9af528: r0 = Container()
    //     0x9af528: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x9af52c: stur            x0, [fp, #-0x20]
    // 0x9af530: ldur            x16, [fp, #-0x28]
    // 0x9af534: ldur            lr, [fp, #-0x10]
    // 0x9af538: stp             lr, x16, [SP, #0x10]
    // 0x9af53c: ldur            x16, [fp, #-8]
    // 0x9af540: ldur            lr, [fp, #-0x18]
    // 0x9af544: stp             lr, x16, [SP]
    // 0x9af548: mov             x1, x0
    // 0x9af54c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x1, height, 0x3, width, 0x2, null]
    //     0x9af54c: add             x4, PP, #0x48, lsl #12  ; [pp+0x485d0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0x9af550: ldr             x4, [x4, #0x5d0]
    // 0x9af554: r0 = Container()
    //     0x9af554: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9af558: r1 = Null
    //     0x9af558: mov             x1, NULL
    // 0x9af55c: r2 = 6
    //     0x9af55c: movz            x2, #0x6
    // 0x9af560: r0 = AllocateArray()
    //     0x9af560: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9af564: mov             x2, x0
    // 0x9af568: ldur            x0, [fp, #-0x30]
    // 0x9af56c: stur            x2, [fp, #-8]
    // 0x9af570: StoreField: r2->field_f = r0
    //     0x9af570: stur            w0, [x2, #0xf]
    // 0x9af574: r16 = Instance_SizedBox
    //     0x9af574: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x9af578: ldr             x16, [x16, #0x8f0]
    // 0x9af57c: StoreField: r2->field_13 = r16
    //     0x9af57c: stur            w16, [x2, #0x13]
    // 0x9af580: ldur            x0, [fp, #-0x20]
    // 0x9af584: ArrayStore: r2[0] = r0  ; List_4
    //     0x9af584: stur            w0, [x2, #0x17]
    // 0x9af588: r1 = <Widget>
    //     0x9af588: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9af58c: r0 = AllocateGrowableArray()
    //     0x9af58c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9af590: mov             x1, x0
    // 0x9af594: ldur            x0, [fp, #-8]
    // 0x9af598: stur            x1, [fp, #-0x10]
    // 0x9af59c: StoreField: r1->field_f = r0
    //     0x9af59c: stur            w0, [x1, #0xf]
    // 0x9af5a0: r0 = 6
    //     0x9af5a0: movz            x0, #0x6
    // 0x9af5a4: StoreField: r1->field_b = r0
    //     0x9af5a4: stur            w0, [x1, #0xb]
    // 0x9af5a8: r0 = Column()
    //     0x9af5a8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x9af5ac: r1 = Instance_Axis
    //     0x9af5ac: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x9af5b0: StoreField: r0->field_f = r1
    //     0x9af5b0: stur            w1, [x0, #0xf]
    // 0x9af5b4: r1 = Instance_MainAxisAlignment
    //     0x9af5b4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x9af5b8: ldr             x1, [x1, #0xab0]
    // 0x9af5bc: StoreField: r0->field_13 = r1
    //     0x9af5bc: stur            w1, [x0, #0x13]
    // 0x9af5c0: r1 = Instance_MainAxisSize
    //     0x9af5c0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x9af5c4: ldr             x1, [x1, #0xa10]
    // 0x9af5c8: ArrayStore: r0[0] = r1  ; List_4
    //     0x9af5c8: stur            w1, [x0, #0x17]
    // 0x9af5cc: r1 = Instance_CrossAxisAlignment
    //     0x9af5cc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x9af5d0: ldr             x1, [x1, #0xa18]
    // 0x9af5d4: StoreField: r0->field_1b = r1
    //     0x9af5d4: stur            w1, [x0, #0x1b]
    // 0x9af5d8: r1 = Instance_VerticalDirection
    //     0x9af5d8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x9af5dc: ldr             x1, [x1, #0xa20]
    // 0x9af5e0: StoreField: r0->field_23 = r1
    //     0x9af5e0: stur            w1, [x0, #0x23]
    // 0x9af5e4: r1 = Instance_Clip
    //     0x9af5e4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x9af5e8: ldr             x1, [x1, #0x38]
    // 0x9af5ec: StoreField: r0->field_2b = r1
    //     0x9af5ec: stur            w1, [x0, #0x2b]
    // 0x9af5f0: StoreField: r0->field_2f = rZR
    //     0x9af5f0: stur            xzr, [x0, #0x2f]
    // 0x9af5f4: ldur            x1, [fp, #-0x10]
    // 0x9af5f8: StoreField: r0->field_b = r1
    //     0x9af5f8: stur            w1, [x0, #0xb]
    // 0x9af5fc: LeaveFrame
    //     0x9af5fc: mov             SP, fp
    //     0x9af600: ldp             fp, lr, [SP], #0x10
    // 0x9af604: ret
    //     0x9af604: ret             
    // 0x9af608: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9af608: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9af60c: b               #0x9ae184
    // 0x9af610: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9af610: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0x9af614: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af614: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af618: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af618: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af61c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af61c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af620: SaveReg d0
    //     0x9af620: str             q0, [SP, #-0x10]!
    // 0x9af624: stp             x0, x1, [SP, #-0x10]!
    // 0x9af628: r0 = AllocateDouble()
    //     0x9af628: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9af62c: mov             x2, x0
    // 0x9af630: ldp             x0, x1, [SP], #0x10
    // 0x9af634: RestoreReg d0
    //     0x9af634: ldr             q0, [SP], #0x10
    // 0x9af638: b               #0x9ae3c8
    // 0x9af63c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af63c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af640: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9af640: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0x9af644: r0 = NullCastErrorSharedWithFPURegs()
    //     0x9af644: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0x9af648: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af648: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af64c: SaveReg d0
    //     0x9af64c: str             q0, [SP, #-0x10]!
    // 0x9af650: SaveReg r1
    //     0x9af650: str             x1, [SP, #-8]!
    // 0x9af654: r0 = AllocateDouble()
    //     0x9af654: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9af658: RestoreReg r1
    //     0x9af658: ldr             x1, [SP], #8
    // 0x9af65c: RestoreReg d0
    //     0x9af65c: ldr             q0, [SP], #0x10
    // 0x9af660: b               #0x9ae640
    // 0x9af664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af664: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af668: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af66c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af66c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af670: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af670: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af674: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af674: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af678: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af678: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af67c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af67c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af680: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af680: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af684: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af684: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af688: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af688: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af68c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af68c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af690: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af690: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af694: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af694: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af698: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af698: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af69c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af69c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af6a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af6a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af6a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af6a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af6a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af6a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af6ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af6ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af6b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9af6b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9af6b4: SaveReg d0
    //     0x9af6b4: str             q0, [SP, #-0x10]!
    // 0x9af6b8: stp             x3, x4, [SP, #-0x10]!
    // 0x9af6bc: stp             x1, x2, [SP, #-0x10]!
    // 0x9af6c0: SaveReg r0
    //     0x9af6c0: str             x0, [SP, #-8]!
    // 0x9af6c4: r0 = AllocateDouble()
    //     0x9af6c4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9af6c8: mov             x5, x0
    // 0x9af6cc: RestoreReg r0
    //     0x9af6cc: ldr             x0, [SP], #8
    // 0x9af6d0: ldp             x1, x2, [SP], #0x10
    // 0x9af6d4: ldp             x3, x4, [SP], #0x10
    // 0x9af6d8: RestoreReg d0
    //     0x9af6d8: ldr             q0, [SP], #0x10
    // 0x9af6dc: b               #0x9af4f0
    // 0x9af6e0: SaveReg d0
    //     0x9af6e0: str             q0, [SP, #-0x10]!
    // 0x9af6e4: stp             x4, x5, [SP, #-0x10]!
    // 0x9af6e8: stp             x2, x3, [SP, #-0x10]!
    // 0x9af6ec: stp             x0, x1, [SP, #-0x10]!
    // 0x9af6f0: r0 = AllocateDouble()
    //     0x9af6f0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x9af6f4: mov             x6, x0
    // 0x9af6f8: ldp             x0, x1, [SP], #0x10
    // 0x9af6fc: ldp             x2, x3, [SP], #0x10
    // 0x9af700: ldp             x4, x5, [SP], #0x10
    // 0x9af704: RestoreReg d0
    //     0x9af704: ldr             q0, [SP], #0x10
    // 0x9af708: b               #0x9af520
  }
}

// class id: 4300, size: 0x14, field offset: 0xc
//   const constructor, 
class SaleEventWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc79e88, size: 0x24
    // 0xc79e88: EnterFrame
    //     0xc79e88: stp             fp, lr, [SP, #-0x10]!
    //     0xc79e8c: mov             fp, SP
    // 0xc79e90: mov             x0, x1
    // 0xc79e94: r1 = <SaleEventWidget>
    //     0xc79e94: add             x1, PP, #0x49, lsl #12  ; [pp+0x492a8] TypeArguments: <SaleEventWidget>
    //     0xc79e98: ldr             x1, [x1, #0x2a8]
    // 0xc79e9c: r0 = _SaleEventWidgetState()
    //     0xc79e9c: bl              #0xc79eac  ; Allocate_SaleEventWidgetStateStub -> _SaleEventWidgetState (size=0x14)
    // 0xc79ea0: LeaveFrame
    //     0xc79ea0: mov             SP, fp
    //     0xc79ea4: ldp             fp, lr, [SP], #0x10
    // 0xc79ea8: ret
    //     0xc79ea8: ret             
  }
}
