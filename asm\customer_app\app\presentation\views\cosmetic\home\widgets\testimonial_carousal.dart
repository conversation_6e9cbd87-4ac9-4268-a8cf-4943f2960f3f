// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart

// class id: 1049285, size: 0x8
class :: {
}

// class id: 3420, size: 0x24, field offset: 0x14
class _TestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xaf3c40, size: 0x7e8
    // 0xaf3c40: EnterFrame
    //     0xaf3c40: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3c44: mov             fp, SP
    // 0xaf3c48: AllocStack(0x80)
    //     0xaf3c48: sub             SP, SP, #0x80
    // 0xaf3c4c: SetupParameters(_TestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaf3c4c: mov             x0, x1
    //     0xaf3c50: stur            x1, [fp, #-8]
    //     0xaf3c54: mov             x1, x2
    //     0xaf3c58: stur            x2, [fp, #-0x10]
    // 0xaf3c5c: CheckStackOverflow
    //     0xaf3c5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3c60: cmp             SP, x16
    //     0xaf3c64: b.ls            #0xaf43e8
    // 0xaf3c68: r1 = 1
    //     0xaf3c68: movz            x1, #0x1
    // 0xaf3c6c: r0 = AllocateContext()
    //     0xaf3c6c: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf3c70: mov             x3, x0
    // 0xaf3c74: ldur            x0, [fp, #-8]
    // 0xaf3c78: stur            x3, [fp, #-0x20]
    // 0xaf3c7c: StoreField: r3->field_f = r0
    //     0xaf3c7c: stur            w0, [x3, #0xf]
    // 0xaf3c80: LoadField: r1 = r0->field_b
    //     0xaf3c80: ldur            w1, [x0, #0xb]
    // 0xaf3c84: DecompressPointer r1
    //     0xaf3c84: add             x1, x1, HEAP, lsl #32
    // 0xaf3c88: cmp             w1, NULL
    // 0xaf3c8c: b.eq            #0xaf43f0
    // 0xaf3c90: LoadField: r2 = r1->field_13
    //     0xaf3c90: ldur            w2, [x1, #0x13]
    // 0xaf3c94: DecompressPointer r2
    //     0xaf3c94: add             x2, x2, HEAP, lsl #32
    // 0xaf3c98: cmp             w2, NULL
    // 0xaf3c9c: b.ne            #0xaf3ca8
    // 0xaf3ca0: r1 = Null
    //     0xaf3ca0: mov             x1, NULL
    // 0xaf3ca4: b               #0xaf3cb0
    // 0xaf3ca8: LoadField: r1 = r2->field_7
    //     0xaf3ca8: ldur            w1, [x2, #7]
    // 0xaf3cac: DecompressPointer r1
    //     0xaf3cac: add             x1, x1, HEAP, lsl #32
    // 0xaf3cb0: cmp             w1, NULL
    // 0xaf3cb4: b.ne            #0xaf3cc0
    // 0xaf3cb8: r1 = Instance_TitleAlignment
    //     0xaf3cb8: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaf3cbc: ldr             x1, [x1, #0x518]
    // 0xaf3cc0: r16 = Instance_TitleAlignment
    //     0xaf3cc0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xaf3cc4: ldr             x16, [x16, #0x520]
    // 0xaf3cc8: cmp             w1, w16
    // 0xaf3ccc: b.ne            #0xaf3cdc
    // 0xaf3cd0: r4 = Instance_CrossAxisAlignment
    //     0xaf3cd0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xaf3cd4: ldr             x4, [x4, #0xc68]
    // 0xaf3cd8: b               #0xaf3d00
    // 0xaf3cdc: r16 = Instance_TitleAlignment
    //     0xaf3cdc: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaf3ce0: ldr             x16, [x16, #0x518]
    // 0xaf3ce4: cmp             w1, w16
    // 0xaf3ce8: b.ne            #0xaf3cf8
    // 0xaf3cec: r4 = Instance_CrossAxisAlignment
    //     0xaf3cec: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaf3cf0: ldr             x4, [x4, #0x890]
    // 0xaf3cf4: b               #0xaf3d00
    // 0xaf3cf8: r4 = Instance_CrossAxisAlignment
    //     0xaf3cf8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf3cfc: ldr             x4, [x4, #0xa18]
    // 0xaf3d00: stur            x4, [fp, #-0x18]
    // 0xaf3d04: r1 = <Widget>
    //     0xaf3d04: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf3d08: r2 = 0
    //     0xaf3d08: movz            x2, #0
    // 0xaf3d0c: r0 = _GrowableList()
    //     0xaf3d0c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xaf3d10: mov             x2, x0
    // 0xaf3d14: ldur            x1, [fp, #-8]
    // 0xaf3d18: stur            x2, [fp, #-0x28]
    // 0xaf3d1c: LoadField: r0 = r1->field_b
    //     0xaf3d1c: ldur            w0, [x1, #0xb]
    // 0xaf3d20: DecompressPointer r0
    //     0xaf3d20: add             x0, x0, HEAP, lsl #32
    // 0xaf3d24: cmp             w0, NULL
    // 0xaf3d28: b.eq            #0xaf43f4
    // 0xaf3d2c: LoadField: r3 = r0->field_f
    //     0xaf3d2c: ldur            w3, [x0, #0xf]
    // 0xaf3d30: DecompressPointer r3
    //     0xaf3d30: add             x3, x3, HEAP, lsl #32
    // 0xaf3d34: LoadField: r0 = r3->field_7
    //     0xaf3d34: ldur            w0, [x3, #7]
    // 0xaf3d38: cbz             w0, #0xaf3ed4
    // 0xaf3d3c: r0 = LoadClassIdInstr(r3)
    //     0xaf3d3c: ldur            x0, [x3, #-1]
    //     0xaf3d40: ubfx            x0, x0, #0xc, #0x14
    // 0xaf3d44: str             x3, [SP]
    // 0xaf3d48: r0 = GDT[cid_x0 + -0x1000]()
    //     0xaf3d48: sub             lr, x0, #1, lsl #12
    //     0xaf3d4c: ldr             lr, [x21, lr, lsl #3]
    //     0xaf3d50: blr             lr
    // 0xaf3d54: mov             x2, x0
    // 0xaf3d58: ldur            x0, [fp, #-8]
    // 0xaf3d5c: stur            x2, [fp, #-0x38]
    // 0xaf3d60: LoadField: r1 = r0->field_b
    //     0xaf3d60: ldur            w1, [x0, #0xb]
    // 0xaf3d64: DecompressPointer r1
    //     0xaf3d64: add             x1, x1, HEAP, lsl #32
    // 0xaf3d68: cmp             w1, NULL
    // 0xaf3d6c: b.eq            #0xaf43f8
    // 0xaf3d70: LoadField: r3 = r1->field_13
    //     0xaf3d70: ldur            w3, [x1, #0x13]
    // 0xaf3d74: DecompressPointer r3
    //     0xaf3d74: add             x3, x3, HEAP, lsl #32
    // 0xaf3d78: cmp             w3, NULL
    // 0xaf3d7c: b.ne            #0xaf3d88
    // 0xaf3d80: r1 = Null
    //     0xaf3d80: mov             x1, NULL
    // 0xaf3d84: b               #0xaf3d90
    // 0xaf3d88: LoadField: r1 = r3->field_7
    //     0xaf3d88: ldur            w1, [x3, #7]
    // 0xaf3d8c: DecompressPointer r1
    //     0xaf3d8c: add             x1, x1, HEAP, lsl #32
    // 0xaf3d90: cmp             w1, NULL
    // 0xaf3d94: b.ne            #0xaf3da0
    // 0xaf3d98: r1 = Instance_TitleAlignment
    //     0xaf3d98: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaf3d9c: ldr             x1, [x1, #0x518]
    // 0xaf3da0: r16 = Instance_TitleAlignment
    //     0xaf3da0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xaf3da4: ldr             x16, [x16, #0x520]
    // 0xaf3da8: cmp             w1, w16
    // 0xaf3dac: b.ne            #0xaf3db8
    // 0xaf3db0: r4 = Instance_TextAlign
    //     0xaf3db0: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xaf3db4: b               #0xaf3dd4
    // 0xaf3db8: r16 = Instance_TitleAlignment
    //     0xaf3db8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xaf3dbc: ldr             x16, [x16, #0x518]
    // 0xaf3dc0: cmp             w1, w16
    // 0xaf3dc4: b.ne            #0xaf3dd0
    // 0xaf3dc8: r4 = Instance_TextAlign
    //     0xaf3dc8: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xaf3dcc: b               #0xaf3dd4
    // 0xaf3dd0: r4 = Instance_TextAlign
    //     0xaf3dd0: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xaf3dd4: ldur            x3, [fp, #-0x28]
    // 0xaf3dd8: ldur            x1, [fp, #-0x10]
    // 0xaf3ddc: stur            x4, [fp, #-0x30]
    // 0xaf3de0: r0 = of()
    //     0xaf3de0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf3de4: LoadField: r1 = r0->field_87
    //     0xaf3de4: ldur            w1, [x0, #0x87]
    // 0xaf3de8: DecompressPointer r1
    //     0xaf3de8: add             x1, x1, HEAP, lsl #32
    // 0xaf3dec: LoadField: r0 = r1->field_7
    //     0xaf3dec: ldur            w0, [x1, #7]
    // 0xaf3df0: DecompressPointer r0
    //     0xaf3df0: add             x0, x0, HEAP, lsl #32
    // 0xaf3df4: r16 = Instance_Color
    //     0xaf3df4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf3df8: r30 = 32.000000
    //     0xaf3df8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xaf3dfc: ldr             lr, [lr, #0x848]
    // 0xaf3e00: stp             lr, x16, [SP]
    // 0xaf3e04: mov             x1, x0
    // 0xaf3e08: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaf3e08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaf3e0c: ldr             x4, [x4, #0x9b8]
    // 0xaf3e10: r0 = copyWith()
    //     0xaf3e10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf3e14: stur            x0, [fp, #-0x40]
    // 0xaf3e18: r0 = Text()
    //     0xaf3e18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf3e1c: mov             x1, x0
    // 0xaf3e20: ldur            x0, [fp, #-0x38]
    // 0xaf3e24: stur            x1, [fp, #-0x48]
    // 0xaf3e28: StoreField: r1->field_b = r0
    //     0xaf3e28: stur            w0, [x1, #0xb]
    // 0xaf3e2c: ldur            x0, [fp, #-0x40]
    // 0xaf3e30: StoreField: r1->field_13 = r0
    //     0xaf3e30: stur            w0, [x1, #0x13]
    // 0xaf3e34: ldur            x0, [fp, #-0x30]
    // 0xaf3e38: StoreField: r1->field_1b = r0
    //     0xaf3e38: stur            w0, [x1, #0x1b]
    // 0xaf3e3c: r0 = Padding()
    //     0xaf3e3c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf3e40: mov             x2, x0
    // 0xaf3e44: r0 = Instance_EdgeInsets
    //     0xaf3e44: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xaf3e48: ldr             x0, [x0, #0x78]
    // 0xaf3e4c: stur            x2, [fp, #-0x30]
    // 0xaf3e50: StoreField: r2->field_f = r0
    //     0xaf3e50: stur            w0, [x2, #0xf]
    // 0xaf3e54: ldur            x0, [fp, #-0x48]
    // 0xaf3e58: StoreField: r2->field_b = r0
    //     0xaf3e58: stur            w0, [x2, #0xb]
    // 0xaf3e5c: ldur            x0, [fp, #-0x28]
    // 0xaf3e60: LoadField: r1 = r0->field_b
    //     0xaf3e60: ldur            w1, [x0, #0xb]
    // 0xaf3e64: LoadField: r3 = r0->field_f
    //     0xaf3e64: ldur            w3, [x0, #0xf]
    // 0xaf3e68: DecompressPointer r3
    //     0xaf3e68: add             x3, x3, HEAP, lsl #32
    // 0xaf3e6c: LoadField: r4 = r3->field_b
    //     0xaf3e6c: ldur            w4, [x3, #0xb]
    // 0xaf3e70: r3 = LoadInt32Instr(r1)
    //     0xaf3e70: sbfx            x3, x1, #1, #0x1f
    // 0xaf3e74: stur            x3, [fp, #-0x50]
    // 0xaf3e78: r1 = LoadInt32Instr(r4)
    //     0xaf3e78: sbfx            x1, x4, #1, #0x1f
    // 0xaf3e7c: cmp             x3, x1
    // 0xaf3e80: b.ne            #0xaf3e8c
    // 0xaf3e84: mov             x1, x0
    // 0xaf3e88: r0 = _growToNextCapacity()
    //     0xaf3e88: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf3e8c: ldur            x2, [fp, #-0x28]
    // 0xaf3e90: ldur            x3, [fp, #-0x50]
    // 0xaf3e94: add             x0, x3, #1
    // 0xaf3e98: lsl             x1, x0, #1
    // 0xaf3e9c: StoreField: r2->field_b = r1
    //     0xaf3e9c: stur            w1, [x2, #0xb]
    // 0xaf3ea0: LoadField: r1 = r2->field_f
    //     0xaf3ea0: ldur            w1, [x2, #0xf]
    // 0xaf3ea4: DecompressPointer r1
    //     0xaf3ea4: add             x1, x1, HEAP, lsl #32
    // 0xaf3ea8: ldur            x0, [fp, #-0x30]
    // 0xaf3eac: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf3eac: add             x25, x1, x3, lsl #2
    //     0xaf3eb0: add             x25, x25, #0xf
    //     0xaf3eb4: str             w0, [x25]
    //     0xaf3eb8: tbz             w0, #0, #0xaf3ed4
    //     0xaf3ebc: ldurb           w16, [x1, #-1]
    //     0xaf3ec0: ldurb           w17, [x0, #-1]
    //     0xaf3ec4: and             x16, x17, x16, lsr #2
    //     0xaf3ec8: tst             x16, HEAP, lsr #32
    //     0xaf3ecc: b.eq            #0xaf3ed4
    //     0xaf3ed0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf3ed4: ldur            x0, [fp, #-8]
    // 0xaf3ed8: ldur            x1, [fp, #-0x10]
    // 0xaf3edc: r0 = of()
    //     0xaf3edc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf3ee0: LoadField: r1 = r0->field_5b
    //     0xaf3ee0: ldur            w1, [x0, #0x5b]
    // 0xaf3ee4: DecompressPointer r1
    //     0xaf3ee4: add             x1, x1, HEAP, lsl #32
    // 0xaf3ee8: stur            x1, [fp, #-0x30]
    // 0xaf3eec: r0 = BoxDecoration()
    //     0xaf3eec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf3ef0: mov             x1, x0
    // 0xaf3ef4: ldur            x0, [fp, #-0x30]
    // 0xaf3ef8: stur            x1, [fp, #-0x38]
    // 0xaf3efc: StoreField: r1->field_7 = r0
    //     0xaf3efc: stur            w0, [x1, #7]
    // 0xaf3f00: r0 = Instance_BorderRadius
    //     0xaf3f00: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xaf3f04: ldr             x0, [x0, #0x460]
    // 0xaf3f08: StoreField: r1->field_13 = r0
    //     0xaf3f08: stur            w0, [x1, #0x13]
    // 0xaf3f0c: r0 = Instance_BoxShape
    //     0xaf3f0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf3f10: ldr             x0, [x0, #0x80]
    // 0xaf3f14: StoreField: r1->field_23 = r0
    //     0xaf3f14: stur            w0, [x1, #0x23]
    // 0xaf3f18: ldur            x2, [fp, #-8]
    // 0xaf3f1c: LoadField: r3 = r2->field_b
    //     0xaf3f1c: ldur            w3, [x2, #0xb]
    // 0xaf3f20: DecompressPointer r3
    //     0xaf3f20: add             x3, x3, HEAP, lsl #32
    // 0xaf3f24: cmp             w3, NULL
    // 0xaf3f28: b.eq            #0xaf43fc
    // 0xaf3f2c: LoadField: r4 = r3->field_2f
    //     0xaf3f2c: ldur            w4, [x3, #0x2f]
    // 0xaf3f30: DecompressPointer r4
    //     0xaf3f30: add             x4, x4, HEAP, lsl #32
    // 0xaf3f34: cmp             w4, NULL
    // 0xaf3f38: b.ne            #0xaf3f44
    // 0xaf3f3c: r4 = Null
    //     0xaf3f3c: mov             x4, NULL
    // 0xaf3f40: b               #0xaf3f50
    // 0xaf3f44: LoadField: r3 = r4->field_7
    //     0xaf3f44: ldur            w3, [x4, #7]
    // 0xaf3f48: DecompressPointer r3
    //     0xaf3f48: add             x3, x3, HEAP, lsl #32
    // 0xaf3f4c: mov             x4, x3
    // 0xaf3f50: ldur            x3, [fp, #-0x28]
    // 0xaf3f54: str             x4, [SP]
    // 0xaf3f58: r0 = _interpolateSingle()
    //     0xaf3f58: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xaf3f5c: ldur            x1, [fp, #-0x10]
    // 0xaf3f60: stur            x0, [fp, #-0x30]
    // 0xaf3f64: r0 = of()
    //     0xaf3f64: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf3f68: LoadField: r1 = r0->field_87
    //     0xaf3f68: ldur            w1, [x0, #0x87]
    // 0xaf3f6c: DecompressPointer r1
    //     0xaf3f6c: add             x1, x1, HEAP, lsl #32
    // 0xaf3f70: LoadField: r0 = r1->field_2b
    //     0xaf3f70: ldur            w0, [x1, #0x2b]
    // 0xaf3f74: DecompressPointer r0
    //     0xaf3f74: add             x0, x0, HEAP, lsl #32
    // 0xaf3f78: r16 = 16.000000
    //     0xaf3f78: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xaf3f7c: ldr             x16, [x16, #0x188]
    // 0xaf3f80: r30 = Instance_Color
    //     0xaf3f80: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf3f84: stp             lr, x16, [SP]
    // 0xaf3f88: mov             x1, x0
    // 0xaf3f8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf3f8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf3f90: ldr             x4, [x4, #0xaa0]
    // 0xaf3f94: r0 = copyWith()
    //     0xaf3f94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf3f98: stur            x0, [fp, #-0x40]
    // 0xaf3f9c: r0 = Text()
    //     0xaf3f9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf3fa0: mov             x1, x0
    // 0xaf3fa4: ldur            x0, [fp, #-0x30]
    // 0xaf3fa8: stur            x1, [fp, #-0x48]
    // 0xaf3fac: StoreField: r1->field_b = r0
    //     0xaf3fac: stur            w0, [x1, #0xb]
    // 0xaf3fb0: ldur            x0, [fp, #-0x40]
    // 0xaf3fb4: StoreField: r1->field_13 = r0
    //     0xaf3fb4: stur            w0, [x1, #0x13]
    // 0xaf3fb8: r0 = Center()
    //     0xaf3fb8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaf3fbc: mov             x1, x0
    // 0xaf3fc0: r0 = Instance_Alignment
    //     0xaf3fc0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaf3fc4: ldr             x0, [x0, #0xb10]
    // 0xaf3fc8: stur            x1, [fp, #-0x30]
    // 0xaf3fcc: StoreField: r1->field_f = r0
    //     0xaf3fcc: stur            w0, [x1, #0xf]
    // 0xaf3fd0: ldur            x0, [fp, #-0x48]
    // 0xaf3fd4: StoreField: r1->field_b = r0
    //     0xaf3fd4: stur            w0, [x1, #0xb]
    // 0xaf3fd8: r0 = Container()
    //     0xaf3fd8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf3fdc: stur            x0, [fp, #-0x40]
    // 0xaf3fe0: r16 = 40.000000
    //     0xaf3fe0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xaf3fe4: ldr             x16, [x16, #8]
    // 0xaf3fe8: r30 = 110.000000
    //     0xaf3fe8: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xaf3fec: ldr             lr, [lr, #0x770]
    // 0xaf3ff0: stp             lr, x16, [SP, #0x10]
    // 0xaf3ff4: ldur            x16, [fp, #-0x38]
    // 0xaf3ff8: ldur            lr, [fp, #-0x30]
    // 0xaf3ffc: stp             lr, x16, [SP]
    // 0xaf4000: mov             x1, x0
    // 0xaf4004: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xaf4004: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xaf4008: ldr             x4, [x4, #0x8c0]
    // 0xaf400c: r0 = Container()
    //     0xaf400c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf4010: r0 = InkWell()
    //     0xaf4010: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xaf4014: mov             x3, x0
    // 0xaf4018: ldur            x0, [fp, #-0x40]
    // 0xaf401c: stur            x3, [fp, #-0x30]
    // 0xaf4020: StoreField: r3->field_b = r0
    //     0xaf4020: stur            w0, [x3, #0xb]
    // 0xaf4024: ldur            x2, [fp, #-0x20]
    // 0xaf4028: r1 = Function '<anonymous closure>':.
    //     0xaf4028: add             x1, PP, #0x58, lsl #12  ; [pp+0x58060] AnonymousClosure: (0xaf6200), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xaf3c40)
    //     0xaf402c: ldr             x1, [x1, #0x60]
    // 0xaf4030: r0 = AllocateClosure()
    //     0xaf4030: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf4034: mov             x1, x0
    // 0xaf4038: ldur            x0, [fp, #-0x30]
    // 0xaf403c: StoreField: r0->field_f = r1
    //     0xaf403c: stur            w1, [x0, #0xf]
    // 0xaf4040: r1 = true
    //     0xaf4040: add             x1, NULL, #0x20  ; true
    // 0xaf4044: StoreField: r0->field_43 = r1
    //     0xaf4044: stur            w1, [x0, #0x43]
    // 0xaf4048: r2 = Instance_BoxShape
    //     0xaf4048: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf404c: ldr             x2, [x2, #0x80]
    // 0xaf4050: StoreField: r0->field_47 = r2
    //     0xaf4050: stur            w2, [x0, #0x47]
    // 0xaf4054: StoreField: r0->field_6f = r1
    //     0xaf4054: stur            w1, [x0, #0x6f]
    // 0xaf4058: r2 = false
    //     0xaf4058: add             x2, NULL, #0x30  ; false
    // 0xaf405c: StoreField: r0->field_73 = r2
    //     0xaf405c: stur            w2, [x0, #0x73]
    // 0xaf4060: StoreField: r0->field_83 = r1
    //     0xaf4060: stur            w1, [x0, #0x83]
    // 0xaf4064: StoreField: r0->field_7b = r2
    //     0xaf4064: stur            w2, [x0, #0x7b]
    // 0xaf4068: r0 = Padding()
    //     0xaf4068: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf406c: mov             x2, x0
    // 0xaf4070: r0 = Instance_EdgeInsets
    //     0xaf4070: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0xaf4074: ldr             x0, [x0, #0xf30]
    // 0xaf4078: stur            x2, [fp, #-0x38]
    // 0xaf407c: StoreField: r2->field_f = r0
    //     0xaf407c: stur            w0, [x2, #0xf]
    // 0xaf4080: ldur            x0, [fp, #-0x30]
    // 0xaf4084: StoreField: r2->field_b = r0
    //     0xaf4084: stur            w0, [x2, #0xb]
    // 0xaf4088: ldur            x0, [fp, #-0x28]
    // 0xaf408c: LoadField: r1 = r0->field_b
    //     0xaf408c: ldur            w1, [x0, #0xb]
    // 0xaf4090: LoadField: r3 = r0->field_f
    //     0xaf4090: ldur            w3, [x0, #0xf]
    // 0xaf4094: DecompressPointer r3
    //     0xaf4094: add             x3, x3, HEAP, lsl #32
    // 0xaf4098: LoadField: r4 = r3->field_b
    //     0xaf4098: ldur            w4, [x3, #0xb]
    // 0xaf409c: r3 = LoadInt32Instr(r1)
    //     0xaf409c: sbfx            x3, x1, #1, #0x1f
    // 0xaf40a0: stur            x3, [fp, #-0x50]
    // 0xaf40a4: r1 = LoadInt32Instr(r4)
    //     0xaf40a4: sbfx            x1, x4, #1, #0x1f
    // 0xaf40a8: cmp             x3, x1
    // 0xaf40ac: b.ne            #0xaf40b8
    // 0xaf40b0: mov             x1, x0
    // 0xaf40b4: r0 = _growToNextCapacity()
    //     0xaf40b4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf40b8: ldur            x4, [fp, #-8]
    // 0xaf40bc: ldur            x3, [fp, #-0x28]
    // 0xaf40c0: ldur            x2, [fp, #-0x50]
    // 0xaf40c4: add             x0, x2, #1
    // 0xaf40c8: lsl             x1, x0, #1
    // 0xaf40cc: StoreField: r3->field_b = r1
    //     0xaf40cc: stur            w1, [x3, #0xb]
    // 0xaf40d0: LoadField: r1 = r3->field_f
    //     0xaf40d0: ldur            w1, [x3, #0xf]
    // 0xaf40d4: DecompressPointer r1
    //     0xaf40d4: add             x1, x1, HEAP, lsl #32
    // 0xaf40d8: ldur            x0, [fp, #-0x38]
    // 0xaf40dc: ArrayStore: r1[r2] = r0  ; List_4
    //     0xaf40dc: add             x25, x1, x2, lsl #2
    //     0xaf40e0: add             x25, x25, #0xf
    //     0xaf40e4: str             w0, [x25]
    //     0xaf40e8: tbz             w0, #0, #0xaf4104
    //     0xaf40ec: ldurb           w16, [x1, #-1]
    //     0xaf40f0: ldurb           w17, [x0, #-1]
    //     0xaf40f4: and             x16, x17, x16, lsr #2
    //     0xaf40f8: tst             x16, HEAP, lsr #32
    //     0xaf40fc: b.eq            #0xaf4104
    //     0xaf4100: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4104: ArrayLoad: r2 = r4[0]  ; List_8
    //     0xaf4104: ldur            x2, [x4, #0x17]
    // 0xaf4108: mov             x1, x4
    // 0xaf410c: r0 = _calculateCardHeight()
    //     0xaf410c: bl              #0xa643cc  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_calculateCardHeight
    // 0xaf4110: ldur            x0, [fp, #-8]
    // 0xaf4114: stur            d0, [fp, #-0x60]
    // 0xaf4118: LoadField: r1 = r0->field_b
    //     0xaf4118: ldur            w1, [x0, #0xb]
    // 0xaf411c: DecompressPointer r1
    //     0xaf411c: add             x1, x1, HEAP, lsl #32
    // 0xaf4120: cmp             w1, NULL
    // 0xaf4124: b.eq            #0xaf4400
    // 0xaf4128: LoadField: r2 = r1->field_b
    //     0xaf4128: ldur            w2, [x1, #0xb]
    // 0xaf412c: DecompressPointer r2
    //     0xaf412c: add             x2, x2, HEAP, lsl #32
    // 0xaf4130: cmp             w2, NULL
    // 0xaf4134: b.ne            #0xaf4140
    // 0xaf4138: r4 = Null
    //     0xaf4138: mov             x4, NULL
    // 0xaf413c: b               #0xaf4148
    // 0xaf4140: LoadField: r1 = r2->field_b
    //     0xaf4140: ldur            w1, [x2, #0xb]
    // 0xaf4144: mov             x4, x1
    // 0xaf4148: ldur            x3, [fp, #-0x28]
    // 0xaf414c: stur            x4, [fp, #-0x38]
    // 0xaf4150: LoadField: r5 = r0->field_13
    //     0xaf4150: ldur            w5, [x0, #0x13]
    // 0xaf4154: DecompressPointer r5
    //     0xaf4154: add             x5, x5, HEAP, lsl #32
    // 0xaf4158: r16 = Sentinel
    //     0xaf4158: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf415c: cmp             w5, w16
    // 0xaf4160: b.eq            #0xaf4404
    // 0xaf4164: ldur            x2, [fp, #-0x20]
    // 0xaf4168: stur            x5, [fp, #-0x30]
    // 0xaf416c: r1 = Function '<anonymous closure>':.
    //     0xaf416c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58068] AnonymousClosure: (0xaf60f0), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xaf3c40)
    //     0xaf4170: ldr             x1, [x1, #0x68]
    // 0xaf4174: r0 = AllocateClosure()
    //     0xaf4174: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf4178: ldur            x2, [fp, #-0x20]
    // 0xaf417c: r1 = Function '<anonymous closure>':.
    //     0xaf417c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58070] AnonymousClosure: (0xaf444c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xaf3c40)
    //     0xaf4180: ldr             x1, [x1, #0x70]
    // 0xaf4184: stur            x0, [fp, #-0x20]
    // 0xaf4188: r0 = AllocateClosure()
    //     0xaf4188: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf418c: stur            x0, [fp, #-0x40]
    // 0xaf4190: r0 = PageView()
    //     0xaf4190: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xaf4194: stur            x0, [fp, #-0x48]
    // 0xaf4198: r16 = Instance_BouncingScrollPhysics
    //     0xaf4198: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xaf419c: ldr             x16, [x16, #0x890]
    // 0xaf41a0: ldur            lr, [fp, #-0x30]
    // 0xaf41a4: stp             lr, x16, [SP]
    // 0xaf41a8: mov             x1, x0
    // 0xaf41ac: ldur            x2, [fp, #-0x40]
    // 0xaf41b0: ldur            x3, [fp, #-0x38]
    // 0xaf41b4: ldur            x5, [fp, #-0x20]
    // 0xaf41b8: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xaf41b8: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xaf41bc: ldr             x4, [x4, #0xe40]
    // 0xaf41c0: r0 = PageView.builder()
    //     0xaf41c0: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xaf41c4: ldur            d0, [fp, #-0x60]
    // 0xaf41c8: r0 = inline_Allocate_Double()
    //     0xaf41c8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xaf41cc: add             x0, x0, #0x10
    //     0xaf41d0: cmp             x1, x0
    //     0xaf41d4: b.ls            #0xaf4410
    //     0xaf41d8: str             x0, [THR, #0x50]  ; THR::top
    //     0xaf41dc: sub             x0, x0, #0xf
    //     0xaf41e0: movz            x1, #0xe15c
    //     0xaf41e4: movk            x1, #0x3, lsl #16
    //     0xaf41e8: stur            x1, [x0, #-1]
    // 0xaf41ec: StoreField: r0->field_7 = d0
    //     0xaf41ec: stur            d0, [x0, #7]
    // 0xaf41f0: stur            x0, [fp, #-0x20]
    // 0xaf41f4: r0 = SizedBox()
    //     0xaf41f4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf41f8: mov             x2, x0
    // 0xaf41fc: ldur            x0, [fp, #-0x20]
    // 0xaf4200: stur            x2, [fp, #-0x30]
    // 0xaf4204: StoreField: r2->field_13 = r0
    //     0xaf4204: stur            w0, [x2, #0x13]
    // 0xaf4208: ldur            x0, [fp, #-0x48]
    // 0xaf420c: StoreField: r2->field_b = r0
    //     0xaf420c: stur            w0, [x2, #0xb]
    // 0xaf4210: ldur            x0, [fp, #-0x28]
    // 0xaf4214: LoadField: r1 = r0->field_b
    //     0xaf4214: ldur            w1, [x0, #0xb]
    // 0xaf4218: LoadField: r3 = r0->field_f
    //     0xaf4218: ldur            w3, [x0, #0xf]
    // 0xaf421c: DecompressPointer r3
    //     0xaf421c: add             x3, x3, HEAP, lsl #32
    // 0xaf4220: LoadField: r4 = r3->field_b
    //     0xaf4220: ldur            w4, [x3, #0xb]
    // 0xaf4224: r3 = LoadInt32Instr(r1)
    //     0xaf4224: sbfx            x3, x1, #1, #0x1f
    // 0xaf4228: stur            x3, [fp, #-0x50]
    // 0xaf422c: r1 = LoadInt32Instr(r4)
    //     0xaf422c: sbfx            x1, x4, #1, #0x1f
    // 0xaf4230: cmp             x3, x1
    // 0xaf4234: b.ne            #0xaf4240
    // 0xaf4238: mov             x1, x0
    // 0xaf423c: r0 = _growToNextCapacity()
    //     0xaf423c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf4240: ldur            x4, [fp, #-8]
    // 0xaf4244: ldur            x2, [fp, #-0x28]
    // 0xaf4248: ldur            x3, [fp, #-0x50]
    // 0xaf424c: add             x0, x3, #1
    // 0xaf4250: lsl             x1, x0, #1
    // 0xaf4254: StoreField: r2->field_b = r1
    //     0xaf4254: stur            w1, [x2, #0xb]
    // 0xaf4258: LoadField: r1 = r2->field_f
    //     0xaf4258: ldur            w1, [x2, #0xf]
    // 0xaf425c: DecompressPointer r1
    //     0xaf425c: add             x1, x1, HEAP, lsl #32
    // 0xaf4260: ldur            x0, [fp, #-0x30]
    // 0xaf4264: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf4264: add             x25, x1, x3, lsl #2
    //     0xaf4268: add             x25, x25, #0xf
    //     0xaf426c: str             w0, [x25]
    //     0xaf4270: tbz             w0, #0, #0xaf428c
    //     0xaf4274: ldurb           w16, [x1, #-1]
    //     0xaf4278: ldurb           w17, [x0, #-1]
    //     0xaf427c: and             x16, x17, x16, lsr #2
    //     0xaf4280: tst             x16, HEAP, lsr #32
    //     0xaf4284: b.eq            #0xaf428c
    //     0xaf4288: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf428c: LoadField: r0 = r4->field_b
    //     0xaf428c: ldur            w0, [x4, #0xb]
    // 0xaf4290: DecompressPointer r0
    //     0xaf4290: add             x0, x0, HEAP, lsl #32
    // 0xaf4294: cmp             w0, NULL
    // 0xaf4298: b.eq            #0xaf4420
    // 0xaf429c: LoadField: r1 = r0->field_b
    //     0xaf429c: ldur            w1, [x0, #0xb]
    // 0xaf42a0: DecompressPointer r1
    //     0xaf42a0: add             x1, x1, HEAP, lsl #32
    // 0xaf42a4: cmp             w1, NULL
    // 0xaf42a8: b.eq            #0xaf4424
    // 0xaf42ac: LoadField: r0 = r1->field_b
    //     0xaf42ac: ldur            w0, [x1, #0xb]
    // 0xaf42b0: r1 = LoadInt32Instr(r0)
    //     0xaf42b0: sbfx            x1, x0, #1, #0x1f
    // 0xaf42b4: cmp             x1, #1
    // 0xaf42b8: b.le            #0xaf4388
    // 0xaf42bc: r3 = LoadInt32Instr(r0)
    //     0xaf42bc: sbfx            x3, x0, #1, #0x1f
    // 0xaf42c0: stur            x3, [fp, #-0x58]
    // 0xaf42c4: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xaf42c4: ldur            x0, [x4, #0x17]
    // 0xaf42c8: ldur            x1, [fp, #-0x10]
    // 0xaf42cc: stur            x0, [fp, #-0x50]
    // 0xaf42d0: r0 = of()
    //     0xaf42d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf42d4: LoadField: r1 = r0->field_5b
    //     0xaf42d4: ldur            w1, [x0, #0x5b]
    // 0xaf42d8: DecompressPointer r1
    //     0xaf42d8: add             x1, x1, HEAP, lsl #32
    // 0xaf42dc: stur            x1, [fp, #-8]
    // 0xaf42e0: r0 = CarouselIndicator()
    //     0xaf42e0: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xaf42e4: mov             x2, x0
    // 0xaf42e8: ldur            x0, [fp, #-0x58]
    // 0xaf42ec: stur            x2, [fp, #-0x10]
    // 0xaf42f0: StoreField: r2->field_b = r0
    //     0xaf42f0: stur            x0, [x2, #0xb]
    // 0xaf42f4: ldur            x0, [fp, #-0x50]
    // 0xaf42f8: StoreField: r2->field_13 = r0
    //     0xaf42f8: stur            x0, [x2, #0x13]
    // 0xaf42fc: ldur            x0, [fp, #-8]
    // 0xaf4300: StoreField: r2->field_1b = r0
    //     0xaf4300: stur            w0, [x2, #0x1b]
    // 0xaf4304: r0 = Instance_Color
    //     0xaf4304: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xaf4308: ldr             x0, [x0, #0x90]
    // 0xaf430c: StoreField: r2->field_1f = r0
    //     0xaf430c: stur            w0, [x2, #0x1f]
    // 0xaf4310: ldur            x0, [fp, #-0x28]
    // 0xaf4314: LoadField: r1 = r0->field_b
    //     0xaf4314: ldur            w1, [x0, #0xb]
    // 0xaf4318: LoadField: r3 = r0->field_f
    //     0xaf4318: ldur            w3, [x0, #0xf]
    // 0xaf431c: DecompressPointer r3
    //     0xaf431c: add             x3, x3, HEAP, lsl #32
    // 0xaf4320: LoadField: r4 = r3->field_b
    //     0xaf4320: ldur            w4, [x3, #0xb]
    // 0xaf4324: r3 = LoadInt32Instr(r1)
    //     0xaf4324: sbfx            x3, x1, #1, #0x1f
    // 0xaf4328: stur            x3, [fp, #-0x50]
    // 0xaf432c: r1 = LoadInt32Instr(r4)
    //     0xaf432c: sbfx            x1, x4, #1, #0x1f
    // 0xaf4330: cmp             x3, x1
    // 0xaf4334: b.ne            #0xaf4340
    // 0xaf4338: mov             x1, x0
    // 0xaf433c: r0 = _growToNextCapacity()
    //     0xaf433c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf4340: ldur            x2, [fp, #-0x28]
    // 0xaf4344: ldur            x3, [fp, #-0x50]
    // 0xaf4348: add             x0, x3, #1
    // 0xaf434c: lsl             x1, x0, #1
    // 0xaf4350: StoreField: r2->field_b = r1
    //     0xaf4350: stur            w1, [x2, #0xb]
    // 0xaf4354: LoadField: r1 = r2->field_f
    //     0xaf4354: ldur            w1, [x2, #0xf]
    // 0xaf4358: DecompressPointer r1
    //     0xaf4358: add             x1, x1, HEAP, lsl #32
    // 0xaf435c: ldur            x0, [fp, #-0x10]
    // 0xaf4360: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf4360: add             x25, x1, x3, lsl #2
    //     0xaf4364: add             x25, x25, #0xf
    //     0xaf4368: str             w0, [x25]
    //     0xaf436c: tbz             w0, #0, #0xaf4388
    //     0xaf4370: ldurb           w16, [x1, #-1]
    //     0xaf4374: ldurb           w17, [x0, #-1]
    //     0xaf4378: and             x16, x17, x16, lsr #2
    //     0xaf437c: tst             x16, HEAP, lsr #32
    //     0xaf4380: b.eq            #0xaf4388
    //     0xaf4384: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4388: ldur            x0, [fp, #-0x18]
    // 0xaf438c: r0 = Column()
    //     0xaf438c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf4390: r1 = Instance_Axis
    //     0xaf4390: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf4394: StoreField: r0->field_f = r1
    //     0xaf4394: stur            w1, [x0, #0xf]
    // 0xaf4398: r1 = Instance_MainAxisAlignment
    //     0xaf4398: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf439c: ldr             x1, [x1, #0xa08]
    // 0xaf43a0: StoreField: r0->field_13 = r1
    //     0xaf43a0: stur            w1, [x0, #0x13]
    // 0xaf43a4: r1 = Instance_MainAxisSize
    //     0xaf43a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf43a8: ldr             x1, [x1, #0xa10]
    // 0xaf43ac: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf43ac: stur            w1, [x0, #0x17]
    // 0xaf43b0: ldur            x1, [fp, #-0x18]
    // 0xaf43b4: StoreField: r0->field_1b = r1
    //     0xaf43b4: stur            w1, [x0, #0x1b]
    // 0xaf43b8: r1 = Instance_VerticalDirection
    //     0xaf43b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf43bc: ldr             x1, [x1, #0xa20]
    // 0xaf43c0: StoreField: r0->field_23 = r1
    //     0xaf43c0: stur            w1, [x0, #0x23]
    // 0xaf43c4: r1 = Instance_Clip
    //     0xaf43c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf43c8: ldr             x1, [x1, #0x38]
    // 0xaf43cc: StoreField: r0->field_2b = r1
    //     0xaf43cc: stur            w1, [x0, #0x2b]
    // 0xaf43d0: StoreField: r0->field_2f = rZR
    //     0xaf43d0: stur            xzr, [x0, #0x2f]
    // 0xaf43d4: ldur            x1, [fp, #-0x28]
    // 0xaf43d8: StoreField: r0->field_b = r1
    //     0xaf43d8: stur            w1, [x0, #0xb]
    // 0xaf43dc: LeaveFrame
    //     0xaf43dc: mov             SP, fp
    //     0xaf43e0: ldp             fp, lr, [SP], #0x10
    // 0xaf43e4: ret
    //     0xaf43e4: ret             
    // 0xaf43e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf43e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf43ec: b               #0xaf3c68
    // 0xaf43f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf43f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf43f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf43f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf43f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf43f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf43fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf43fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf4400: r0 = NullCastErrorSharedWithFPURegs()
    //     0xaf4400: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xaf4404: r9 = _pageController
    //     0xaf4404: add             x9, PP, #0x58, lsl #12  ; [pp+0x58078] Field <_TestimonialCarouselState@1469365284._pageController@1469365284>: late (offset: 0x14)
    //     0xaf4408: ldr             x9, [x9, #0x78]
    // 0xaf440c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xaf440c: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xaf4410: SaveReg d0
    //     0xaf4410: str             q0, [SP, #-0x10]!
    // 0xaf4414: r0 = AllocateDouble()
    //     0xaf4414: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xaf4418: RestoreReg d0
    //     0xaf4418: ldr             q0, [SP], #0x10
    // 0xaf441c: b               #0xaf41ec
    // 0xaf4420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf4420: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf4424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf4424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf444c, size: 0x94
    // 0xaf444c: EnterFrame
    //     0xaf444c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf4450: mov             fp, SP
    // 0xaf4454: AllocStack(0x8)
    //     0xaf4454: sub             SP, SP, #8
    // 0xaf4458: SetupParameters()
    //     0xaf4458: ldr             x0, [fp, #0x20]
    //     0xaf445c: ldur            w1, [x0, #0x17]
    //     0xaf4460: add             x1, x1, HEAP, lsl #32
    // 0xaf4464: CheckStackOverflow
    //     0xaf4464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf4468: cmp             SP, x16
    //     0xaf446c: b.ls            #0xaf44d4
    // 0xaf4470: LoadField: r0 = r1->field_f
    //     0xaf4470: ldur            w0, [x1, #0xf]
    // 0xaf4474: DecompressPointer r0
    //     0xaf4474: add             x0, x0, HEAP, lsl #32
    // 0xaf4478: stur            x0, [fp, #-8]
    // 0xaf447c: LoadField: r1 = r0->field_b
    //     0xaf447c: ldur            w1, [x0, #0xb]
    // 0xaf4480: DecompressPointer r1
    //     0xaf4480: add             x1, x1, HEAP, lsl #32
    // 0xaf4484: cmp             w1, NULL
    // 0xaf4488: b.eq            #0xaf44dc
    // 0xaf448c: LoadField: r2 = r1->field_b
    //     0xaf448c: ldur            w2, [x1, #0xb]
    // 0xaf4490: DecompressPointer r2
    //     0xaf4490: add             x2, x2, HEAP, lsl #32
    // 0xaf4494: cmp             w2, NULL
    // 0xaf4498: b.ne            #0xaf44b0
    // 0xaf449c: r1 = <Entity>
    //     0xaf449c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xaf44a0: ldr             x1, [x1, #0xb68]
    // 0xaf44a4: r2 = 0
    //     0xaf44a4: movz            x2, #0
    // 0xaf44a8: r0 = AllocateArray()
    //     0xaf44a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf44ac: mov             x2, x0
    // 0xaf44b0: ldr             x0, [fp, #0x10]
    // 0xaf44b4: r3 = LoadInt32Instr(r0)
    //     0xaf44b4: sbfx            x3, x0, #1, #0x1f
    //     0xaf44b8: tbz             w0, #0, #0xaf44c0
    //     0xaf44bc: ldur            x3, [x0, #7]
    // 0xaf44c0: ldur            x1, [fp, #-8]
    // 0xaf44c4: r0 = _testimonialWidget()
    //     0xaf44c4: bl              #0xaf44e0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget
    // 0xaf44c8: LeaveFrame
    //     0xaf44c8: mov             SP, fp
    //     0xaf44cc: ldp             fp, lr, [SP], #0x10
    // 0xaf44d0: ret
    //     0xaf44d0: ret             
    // 0xaf44d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf44d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf44d8: b               #0xaf4470
    // 0xaf44dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf44dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialWidget(/* No info */) {
    // ** addr: 0xaf44e0, size: 0x1060
    // 0xaf44e0: EnterFrame
    //     0xaf44e0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf44e4: mov             fp, SP
    // 0xaf44e8: AllocStack(0xa0)
    //     0xaf44e8: sub             SP, SP, #0xa0
    // 0xaf44ec: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xaf44ec: stur            x1, [fp, #-8]
    //     0xaf44f0: stur            x2, [fp, #-0x10]
    //     0xaf44f4: stur            x3, [fp, #-0x18]
    // 0xaf44f8: CheckStackOverflow
    //     0xaf44f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf44fc: cmp             SP, x16
    //     0xaf4500: b.ls            #0xaf5508
    // 0xaf4504: r1 = 2
    //     0xaf4504: movz            x1, #0x2
    // 0xaf4508: r0 = AllocateContext()
    //     0xaf4508: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf450c: mov             x3, x0
    // 0xaf4510: ldur            x2, [fp, #-8]
    // 0xaf4514: stur            x3, [fp, #-0x28]
    // 0xaf4518: StoreField: r3->field_f = r2
    //     0xaf4518: stur            w2, [x3, #0xf]
    // 0xaf451c: ldur            x4, [fp, #-0x18]
    // 0xaf4520: r0 = BoxInt64Instr(r4)
    //     0xaf4520: sbfiz           x0, x4, #1, #0x1f
    //     0xaf4524: cmp             x4, x0, asr #1
    //     0xaf4528: b.eq            #0xaf4534
    //     0xaf452c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf4530: stur            x4, [x0, #7]
    // 0xaf4534: mov             x4, x0
    // 0xaf4538: ldur            x1, [fp, #-0x10]
    // 0xaf453c: stur            x4, [fp, #-0x20]
    // 0xaf4540: r0 = LoadClassIdInstr(r1)
    //     0xaf4540: ldur            x0, [x1, #-1]
    //     0xaf4544: ubfx            x0, x0, #0xc, #0x14
    // 0xaf4548: stp             x4, x1, [SP]
    // 0xaf454c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf454c: sub             lr, x0, #0xb7
    //     0xaf4550: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4554: blr             lr
    // 0xaf4558: stur            x0, [fp, #-0x40]
    // 0xaf455c: LoadField: r1 = r0->field_ab
    //     0xaf455c: ldur            w1, [x0, #0xab]
    // 0xaf4560: DecompressPointer r1
    //     0xaf4560: add             x1, x1, HEAP, lsl #32
    // 0xaf4564: cmp             w1, NULL
    // 0xaf4568: b.ne            #0xaf4574
    // 0xaf456c: r1 = Null
    //     0xaf456c: mov             x1, NULL
    // 0xaf4570: b               #0xaf4588
    // 0xaf4574: LoadField: r2 = r1->field_b
    //     0xaf4574: ldur            w2, [x1, #0xb]
    // 0xaf4578: cbnz            w2, #0xaf4584
    // 0xaf457c: r1 = false
    //     0xaf457c: add             x1, NULL, #0x30  ; false
    // 0xaf4580: b               #0xaf4588
    // 0xaf4584: r1 = true
    //     0xaf4584: add             x1, NULL, #0x20  ; true
    // 0xaf4588: cmp             w1, NULL
    // 0xaf458c: b.ne            #0xaf4598
    // 0xaf4590: r4 = false
    //     0xaf4590: add             x4, NULL, #0x30  ; false
    // 0xaf4594: b               #0xaf459c
    // 0xaf4598: mov             x4, x1
    // 0xaf459c: ldur            x3, [fp, #-8]
    // 0xaf45a0: stur            x4, [fp, #-0x38]
    // 0xaf45a4: LoadField: r5 = r3->field_1f
    //     0xaf45a4: ldur            w5, [x3, #0x1f]
    // 0xaf45a8: DecompressPointer r5
    //     0xaf45a8: add             x5, x5, HEAP, lsl #32
    // 0xaf45ac: mov             x1, x5
    // 0xaf45b0: ldur            x2, [fp, #-0x20]
    // 0xaf45b4: stur            x5, [fp, #-0x30]
    // 0xaf45b8: r0 = _getValueOrData()
    //     0xaf45b8: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xaf45bc: mov             x1, x0
    // 0xaf45c0: ldur            x0, [fp, #-0x30]
    // 0xaf45c4: LoadField: r2 = r0->field_f
    //     0xaf45c4: ldur            w2, [x0, #0xf]
    // 0xaf45c8: DecompressPointer r2
    //     0xaf45c8: add             x2, x2, HEAP, lsl #32
    // 0xaf45cc: cmp             w2, w1
    // 0xaf45d0: b.ne            #0xaf45dc
    // 0xaf45d4: r0 = Null
    //     0xaf45d4: mov             x0, NULL
    // 0xaf45d8: b               #0xaf45e0
    // 0xaf45dc: mov             x0, x1
    // 0xaf45e0: cmp             w0, NULL
    // 0xaf45e4: b.ne            #0xaf4624
    // 0xaf45e8: r1 = <bool>
    //     0xaf45e8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xaf45ec: r0 = RxBool()
    //     0xaf45ec: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xaf45f0: mov             x2, x0
    // 0xaf45f4: r0 = Sentinel
    //     0xaf45f4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf45f8: stur            x2, [fp, #-0x30]
    // 0xaf45fc: StoreField: r2->field_13 = r0
    //     0xaf45fc: stur            w0, [x2, #0x13]
    // 0xaf4600: r0 = true
    //     0xaf4600: add             x0, NULL, #0x20  ; true
    // 0xaf4604: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf4604: stur            w0, [x2, #0x17]
    // 0xaf4608: mov             x1, x2
    // 0xaf460c: r0 = RxNotifier()
    //     0xaf460c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xaf4610: ldur            x0, [fp, #-0x30]
    // 0xaf4614: r1 = false
    //     0xaf4614: add             x1, NULL, #0x30  ; false
    // 0xaf4618: StoreField: r0->field_13 = r1
    //     0xaf4618: stur            w1, [x0, #0x13]
    // 0xaf461c: mov             x4, x0
    // 0xaf4620: b               #0xaf462c
    // 0xaf4624: r1 = false
    //     0xaf4624: add             x1, NULL, #0x30  ; false
    // 0xaf4628: mov             x4, x0
    // 0xaf462c: ldur            x3, [fp, #-0x10]
    // 0xaf4630: ldur            x2, [fp, #-0x28]
    // 0xaf4634: mov             x0, x4
    // 0xaf4638: stur            x4, [fp, #-0x30]
    // 0xaf463c: StoreField: r2->field_13 = r0
    //     0xaf463c: stur            w0, [x2, #0x13]
    //     0xaf4640: ldurb           w16, [x2, #-1]
    //     0xaf4644: ldurb           w17, [x0, #-1]
    //     0xaf4648: and             x16, x17, x16, lsr #2
    //     0xaf464c: tst             x16, HEAP, lsr #32
    //     0xaf4650: b.eq            #0xaf4658
    //     0xaf4654: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xaf4658: r0 = Radius()
    //     0xaf4658: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf465c: d0 = 12.000000
    //     0xaf465c: fmov            d0, #12.00000000
    // 0xaf4660: stur            x0, [fp, #-0x48]
    // 0xaf4664: StoreField: r0->field_7 = d0
    //     0xaf4664: stur            d0, [x0, #7]
    // 0xaf4668: StoreField: r0->field_f = d0
    //     0xaf4668: stur            d0, [x0, #0xf]
    // 0xaf466c: r0 = BorderRadius()
    //     0xaf466c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf4670: mov             x1, x0
    // 0xaf4674: ldur            x0, [fp, #-0x48]
    // 0xaf4678: stur            x1, [fp, #-0x50]
    // 0xaf467c: StoreField: r1->field_7 = r0
    //     0xaf467c: stur            w0, [x1, #7]
    // 0xaf4680: StoreField: r1->field_b = r0
    //     0xaf4680: stur            w0, [x1, #0xb]
    // 0xaf4684: StoreField: r1->field_f = r0
    //     0xaf4684: stur            w0, [x1, #0xf]
    // 0xaf4688: StoreField: r1->field_13 = r0
    //     0xaf4688: stur            w0, [x1, #0x13]
    // 0xaf468c: r0 = RoundedRectangleBorder()
    //     0xaf468c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaf4690: mov             x1, x0
    // 0xaf4694: ldur            x0, [fp, #-0x50]
    // 0xaf4698: stur            x1, [fp, #-0x48]
    // 0xaf469c: StoreField: r1->field_b = r0
    //     0xaf469c: stur            w0, [x1, #0xb]
    // 0xaf46a0: r0 = Instance_BorderSide
    //     0xaf46a0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaf46a4: ldr             x0, [x0, #0xe20]
    // 0xaf46a8: StoreField: r1->field_7 = r0
    //     0xaf46a8: stur            w0, [x1, #7]
    // 0xaf46ac: ldur            x2, [fp, #-0x10]
    // 0xaf46b0: r0 = LoadClassIdInstr(r2)
    //     0xaf46b0: ldur            x0, [x2, #-1]
    //     0xaf46b4: ubfx            x0, x0, #0xc, #0x14
    // 0xaf46b8: ldur            x16, [fp, #-0x20]
    // 0xaf46bc: stp             x16, x2, [SP]
    // 0xaf46c0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf46c0: sub             lr, x0, #0xb7
    //     0xaf46c4: ldr             lr, [x21, lr, lsl #3]
    //     0xaf46c8: blr             lr
    // 0xaf46cc: LoadField: r1 = r0->field_97
    //     0xaf46cc: ldur            w1, [x0, #0x97]
    // 0xaf46d0: DecompressPointer r1
    //     0xaf46d0: add             x1, x1, HEAP, lsl #32
    // 0xaf46d4: cmp             w1, NULL
    // 0xaf46d8: b.ne            #0xaf46e0
    // 0xaf46dc: r1 = ""
    //     0xaf46dc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf46e0: ldur            x2, [fp, #-8]
    // 0xaf46e4: ldur            x0, [fp, #-0x10]
    // 0xaf46e8: r0 = capitalizeFirstWord()
    //     0xaf46e8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xaf46ec: mov             x2, x0
    // 0xaf46f0: ldur            x0, [fp, #-8]
    // 0xaf46f4: stur            x2, [fp, #-0x50]
    // 0xaf46f8: LoadField: r1 = r0->field_f
    //     0xaf46f8: ldur            w1, [x0, #0xf]
    // 0xaf46fc: DecompressPointer r1
    //     0xaf46fc: add             x1, x1, HEAP, lsl #32
    // 0xaf4700: cmp             w1, NULL
    // 0xaf4704: b.eq            #0xaf5510
    // 0xaf4708: r0 = of()
    //     0xaf4708: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf470c: LoadField: r1 = r0->field_87
    //     0xaf470c: ldur            w1, [x0, #0x87]
    // 0xaf4710: DecompressPointer r1
    //     0xaf4710: add             x1, x1, HEAP, lsl #32
    // 0xaf4714: LoadField: r0 = r1->field_7
    //     0xaf4714: ldur            w0, [x1, #7]
    // 0xaf4718: DecompressPointer r0
    //     0xaf4718: add             x0, x0, HEAP, lsl #32
    // 0xaf471c: r16 = 32.000000
    //     0xaf471c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xaf4720: ldr             x16, [x16, #0x848]
    // 0xaf4724: r30 = Instance_Color
    //     0xaf4724: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf4728: stp             lr, x16, [SP]
    // 0xaf472c: mov             x1, x0
    // 0xaf4730: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf4730: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf4734: ldr             x4, [x4, #0xaa0]
    // 0xaf4738: r0 = copyWith()
    //     0xaf4738: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf473c: stur            x0, [fp, #-0x58]
    // 0xaf4740: r0 = Text()
    //     0xaf4740: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf4744: mov             x3, x0
    // 0xaf4748: ldur            x0, [fp, #-0x50]
    // 0xaf474c: stur            x3, [fp, #-0x60]
    // 0xaf4750: StoreField: r3->field_b = r0
    //     0xaf4750: stur            w0, [x3, #0xb]
    // 0xaf4754: ldur            x0, [fp, #-0x58]
    // 0xaf4758: StoreField: r3->field_13 = r0
    //     0xaf4758: stur            w0, [x3, #0x13]
    // 0xaf475c: r1 = <Widget>
    //     0xaf475c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf4760: r2 = 18
    //     0xaf4760: movz            x2, #0x12
    // 0xaf4764: r0 = AllocateArray()
    //     0xaf4764: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf4768: stur            x0, [fp, #-0x50]
    // 0xaf476c: r16 = Instance_Icon
    //     0xaf476c: add             x16, PP, #0x52, lsl #12  ; [pp+0x520d0] Obj!Icon@d66371
    //     0xaf4770: ldr             x16, [x16, #0xd0]
    // 0xaf4774: StoreField: r0->field_f = r16
    //     0xaf4774: stur            w16, [x0, #0xf]
    // 0xaf4778: r16 = Instance_SizedBox
    //     0xaf4778: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xaf477c: ldr             x16, [x16, #0xe98]
    // 0xaf4780: StoreField: r0->field_13 = r16
    //     0xaf4780: stur            w16, [x0, #0x13]
    // 0xaf4784: ldur            x2, [fp, #-8]
    // 0xaf4788: LoadField: r1 = r2->field_f
    //     0xaf4788: ldur            w1, [x2, #0xf]
    // 0xaf478c: DecompressPointer r1
    //     0xaf478c: add             x1, x1, HEAP, lsl #32
    // 0xaf4790: cmp             w1, NULL
    // 0xaf4794: b.eq            #0xaf5514
    // 0xaf4798: r0 = of()
    //     0xaf4798: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf479c: LoadField: r1 = r0->field_87
    //     0xaf479c: ldur            w1, [x0, #0x87]
    // 0xaf47a0: DecompressPointer r1
    //     0xaf47a0: add             x1, x1, HEAP, lsl #32
    // 0xaf47a4: LoadField: r0 = r1->field_2b
    //     0xaf47a4: ldur            w0, [x1, #0x2b]
    // 0xaf47a8: DecompressPointer r0
    //     0xaf47a8: add             x0, x0, HEAP, lsl #32
    // 0xaf47ac: stur            x0, [fp, #-0x58]
    // 0xaf47b0: r1 = Instance_Color
    //     0xaf47b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf47b4: d0 = 0.700000
    //     0xaf47b4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaf47b8: ldr             d0, [x17, #0xf48]
    // 0xaf47bc: r0 = withOpacity()
    //     0xaf47bc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf47c0: r16 = 14.000000
    //     0xaf47c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf47c4: ldr             x16, [x16, #0x1d8]
    // 0xaf47c8: stp             x0, x16, [SP]
    // 0xaf47cc: ldur            x1, [fp, #-0x58]
    // 0xaf47d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf47d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf47d4: ldr             x4, [x4, #0xaa0]
    // 0xaf47d8: r0 = copyWith()
    //     0xaf47d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf47dc: stur            x0, [fp, #-0x58]
    // 0xaf47e0: r0 = Text()
    //     0xaf47e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf47e4: mov             x1, x0
    // 0xaf47e8: r0 = "Verified Buyer"
    //     0xaf47e8: add             x0, PP, #0x52, lsl #12  ; [pp+0x520d8] "Verified Buyer"
    //     0xaf47ec: ldr             x0, [x0, #0xd8]
    // 0xaf47f0: StoreField: r1->field_b = r0
    //     0xaf47f0: stur            w0, [x1, #0xb]
    // 0xaf47f4: ldur            x0, [fp, #-0x58]
    // 0xaf47f8: StoreField: r1->field_13 = r0
    //     0xaf47f8: stur            w0, [x1, #0x13]
    // 0xaf47fc: mov             x0, x1
    // 0xaf4800: ldur            x1, [fp, #-0x50]
    // 0xaf4804: ArrayStore: r1[2] = r0  ; List_4
    //     0xaf4804: add             x25, x1, #0x17
    //     0xaf4808: str             w0, [x25]
    //     0xaf480c: tbz             w0, #0, #0xaf4828
    //     0xaf4810: ldurb           w16, [x1, #-1]
    //     0xaf4814: ldurb           w17, [x0, #-1]
    //     0xaf4818: and             x16, x17, x16, lsr #2
    //     0xaf481c: tst             x16, HEAP, lsr #32
    //     0xaf4820: b.eq            #0xaf4828
    //     0xaf4824: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4828: r0 = Container()
    //     0xaf4828: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf482c: stur            x0, [fp, #-0x58]
    // 0xaf4830: r16 = 5.000000
    //     0xaf4830: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xaf4834: ldr             x16, [x16, #0xcf0]
    // 0xaf4838: str             x16, [SP]
    // 0xaf483c: mov             x1, x0
    // 0xaf4840: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xaf4840: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xaf4844: ldr             x4, [x4, #0xe0]
    // 0xaf4848: r0 = Container()
    //     0xaf4848: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf484c: ldur            x1, [fp, #-0x50]
    // 0xaf4850: ldur            x0, [fp, #-0x58]
    // 0xaf4854: ArrayStore: r1[3] = r0  ; List_4
    //     0xaf4854: add             x25, x1, #0x1b
    //     0xaf4858: str             w0, [x25]
    //     0xaf485c: tbz             w0, #0, #0xaf4878
    //     0xaf4860: ldurb           w16, [x1, #-1]
    //     0xaf4864: ldurb           w17, [x0, #-1]
    //     0xaf4868: and             x16, x17, x16, lsr #2
    //     0xaf486c: tst             x16, HEAP, lsr #32
    //     0xaf4870: b.eq            #0xaf4878
    //     0xaf4874: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4878: ldur            x1, [fp, #-0x10]
    // 0xaf487c: r0 = LoadClassIdInstr(r1)
    //     0xaf487c: ldur            x0, [x1, #-1]
    //     0xaf4880: ubfx            x0, x0, #0xc, #0x14
    // 0xaf4884: ldur            x16, [fp, #-0x20]
    // 0xaf4888: stp             x16, x1, [SP]
    // 0xaf488c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf488c: sub             lr, x0, #0xb7
    //     0xaf4890: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4894: blr             lr
    // 0xaf4898: LoadField: r1 = r0->field_a3
    //     0xaf4898: ldur            w1, [x0, #0xa3]
    // 0xaf489c: DecompressPointer r1
    //     0xaf489c: add             x1, x1, HEAP, lsl #32
    // 0xaf48a0: cmp             w1, NULL
    // 0xaf48a4: b.eq            #0xaf490c
    // 0xaf48a8: ldur            x1, [fp, #-0x10]
    // 0xaf48ac: r0 = LoadClassIdInstr(r1)
    //     0xaf48ac: ldur            x0, [x1, #-1]
    //     0xaf48b0: ubfx            x0, x0, #0xc, #0x14
    // 0xaf48b4: ldur            x16, [fp, #-0x20]
    // 0xaf48b8: stp             x16, x1, [SP]
    // 0xaf48bc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf48bc: sub             lr, x0, #0xb7
    //     0xaf48c0: ldr             lr, [x21, lr, lsl #3]
    //     0xaf48c4: blr             lr
    // 0xaf48c8: LoadField: r1 = r0->field_a3
    //     0xaf48c8: ldur            w1, [x0, #0xa3]
    // 0xaf48cc: DecompressPointer r1
    //     0xaf48cc: add             x1, x1, HEAP, lsl #32
    // 0xaf48d0: cmp             w1, NULL
    // 0xaf48d4: b.ne            #0xaf48e0
    // 0xaf48d8: r0 = Null
    //     0xaf48d8: mov             x0, NULL
    // 0xaf48dc: b               #0xaf48f8
    // 0xaf48e0: LoadField: r0 = r1->field_7
    //     0xaf48e0: ldur            w0, [x1, #7]
    // 0xaf48e4: cbnz            w0, #0xaf48f0
    // 0xaf48e8: r1 = false
    //     0xaf48e8: add             x1, NULL, #0x30  ; false
    // 0xaf48ec: b               #0xaf48f4
    // 0xaf48f0: r1 = true
    //     0xaf48f0: add             x1, NULL, #0x20  ; true
    // 0xaf48f4: mov             x0, x1
    // 0xaf48f8: cmp             w0, NULL
    // 0xaf48fc: b.ne            #0xaf4904
    // 0xaf4900: r0 = false
    //     0xaf4900: add             x0, NULL, #0x30  ; false
    // 0xaf4904: mov             x3, x0
    // 0xaf4908: b               #0xaf4910
    // 0xaf490c: r3 = false
    //     0xaf490c: add             x3, NULL, #0x30  ; false
    // 0xaf4910: ldur            x2, [fp, #-8]
    // 0xaf4914: ldur            x0, [fp, #-0x10]
    // 0xaf4918: stur            x3, [fp, #-0x58]
    // 0xaf491c: LoadField: r1 = r2->field_f
    //     0xaf491c: ldur            w1, [x2, #0xf]
    // 0xaf4920: DecompressPointer r1
    //     0xaf4920: add             x1, x1, HEAP, lsl #32
    // 0xaf4924: cmp             w1, NULL
    // 0xaf4928: b.eq            #0xaf5518
    // 0xaf492c: r0 = of()
    //     0xaf492c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf4930: LoadField: r1 = r0->field_5b
    //     0xaf4930: ldur            w1, [x0, #0x5b]
    // 0xaf4934: DecompressPointer r1
    //     0xaf4934: add             x1, x1, HEAP, lsl #32
    // 0xaf4938: stur            x1, [fp, #-0x68]
    // 0xaf493c: r0 = BoxDecoration()
    //     0xaf493c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf4940: mov             x1, x0
    // 0xaf4944: ldur            x0, [fp, #-0x68]
    // 0xaf4948: stur            x1, [fp, #-0x70]
    // 0xaf494c: StoreField: r1->field_7 = r0
    //     0xaf494c: stur            w0, [x1, #7]
    // 0xaf4950: r0 = Instance_BoxShape
    //     0xaf4950: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xaf4954: ldr             x0, [x0, #0x970]
    // 0xaf4958: StoreField: r1->field_23 = r0
    //     0xaf4958: stur            w0, [x1, #0x23]
    // 0xaf495c: r0 = Container()
    //     0xaf495c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf4960: stur            x0, [fp, #-0x68]
    // 0xaf4964: r16 = Instance_EdgeInsets
    //     0xaf4964: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xaf4968: ldr             x16, [x16, #0x108]
    // 0xaf496c: r30 = 5.000000
    //     0xaf496c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xaf4970: ldr             lr, [lr, #0xcf0]
    // 0xaf4974: stp             lr, x16, [SP, #0x10]
    // 0xaf4978: r16 = 5.000000
    //     0xaf4978: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xaf497c: ldr             x16, [x16, #0xcf0]
    // 0xaf4980: ldur            lr, [fp, #-0x70]
    // 0xaf4984: stp             lr, x16, [SP]
    // 0xaf4988: mov             x1, x0
    // 0xaf498c: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xaf498c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xaf4990: ldr             x4, [x4, #0x118]
    // 0xaf4994: r0 = Container()
    //     0xaf4994: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf4998: r0 = Visibility()
    //     0xaf4998: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xaf499c: mov             x1, x0
    // 0xaf49a0: ldur            x0, [fp, #-0x68]
    // 0xaf49a4: StoreField: r1->field_b = r0
    //     0xaf49a4: stur            w0, [x1, #0xb]
    // 0xaf49a8: r0 = Instance_SizedBox
    //     0xaf49a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xaf49ac: StoreField: r1->field_f = r0
    //     0xaf49ac: stur            w0, [x1, #0xf]
    // 0xaf49b0: ldur            x0, [fp, #-0x58]
    // 0xaf49b4: StoreField: r1->field_13 = r0
    //     0xaf49b4: stur            w0, [x1, #0x13]
    // 0xaf49b8: r2 = false
    //     0xaf49b8: add             x2, NULL, #0x30  ; false
    // 0xaf49bc: ArrayStore: r1[0] = r2  ; List_4
    //     0xaf49bc: stur            w2, [x1, #0x17]
    // 0xaf49c0: StoreField: r1->field_1b = r2
    //     0xaf49c0: stur            w2, [x1, #0x1b]
    // 0xaf49c4: StoreField: r1->field_1f = r2
    //     0xaf49c4: stur            w2, [x1, #0x1f]
    // 0xaf49c8: StoreField: r1->field_23 = r2
    //     0xaf49c8: stur            w2, [x1, #0x23]
    // 0xaf49cc: StoreField: r1->field_27 = r2
    //     0xaf49cc: stur            w2, [x1, #0x27]
    // 0xaf49d0: StoreField: r1->field_2b = r2
    //     0xaf49d0: stur            w2, [x1, #0x2b]
    // 0xaf49d4: mov             x0, x1
    // 0xaf49d8: ldur            x1, [fp, #-0x50]
    // 0xaf49dc: ArrayStore: r1[4] = r0  ; List_4
    //     0xaf49dc: add             x25, x1, #0x1f
    //     0xaf49e0: str             w0, [x25]
    //     0xaf49e4: tbz             w0, #0, #0xaf4a00
    //     0xaf49e8: ldurb           w16, [x1, #-1]
    //     0xaf49ec: ldurb           w17, [x0, #-1]
    //     0xaf49f0: and             x16, x17, x16, lsr #2
    //     0xaf49f4: tst             x16, HEAP, lsr #32
    //     0xaf49f8: b.eq            #0xaf4a00
    //     0xaf49fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4a00: ldur            x1, [fp, #-0x10]
    // 0xaf4a04: r0 = LoadClassIdInstr(r1)
    //     0xaf4a04: ldur            x0, [x1, #-1]
    //     0xaf4a08: ubfx            x0, x0, #0xc, #0x14
    // 0xaf4a0c: ldur            x16, [fp, #-0x20]
    // 0xaf4a10: stp             x16, x1, [SP]
    // 0xaf4a14: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf4a14: sub             lr, x0, #0xb7
    //     0xaf4a18: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4a1c: blr             lr
    // 0xaf4a20: LoadField: r1 = r0->field_a3
    //     0xaf4a20: ldur            w1, [x0, #0xa3]
    // 0xaf4a24: DecompressPointer r1
    //     0xaf4a24: add             x1, x1, HEAP, lsl #32
    // 0xaf4a28: cmp             w1, NULL
    // 0xaf4a2c: b.ne            #0xaf4a38
    // 0xaf4a30: r3 = ""
    //     0xaf4a30: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf4a34: b               #0xaf4a3c
    // 0xaf4a38: mov             x3, x1
    // 0xaf4a3c: ldur            x2, [fp, #-8]
    // 0xaf4a40: ldur            x0, [fp, #-0x10]
    // 0xaf4a44: stur            x3, [fp, #-0x58]
    // 0xaf4a48: LoadField: r1 = r2->field_f
    //     0xaf4a48: ldur            w1, [x2, #0xf]
    // 0xaf4a4c: DecompressPointer r1
    //     0xaf4a4c: add             x1, x1, HEAP, lsl #32
    // 0xaf4a50: cmp             w1, NULL
    // 0xaf4a54: b.eq            #0xaf551c
    // 0xaf4a58: r0 = of()
    //     0xaf4a58: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf4a5c: LoadField: r1 = r0->field_87
    //     0xaf4a5c: ldur            w1, [x0, #0x87]
    // 0xaf4a60: DecompressPointer r1
    //     0xaf4a60: add             x1, x1, HEAP, lsl #32
    // 0xaf4a64: LoadField: r0 = r1->field_2b
    //     0xaf4a64: ldur            w0, [x1, #0x2b]
    // 0xaf4a68: DecompressPointer r0
    //     0xaf4a68: add             x0, x0, HEAP, lsl #32
    // 0xaf4a6c: stur            x0, [fp, #-0x68]
    // 0xaf4a70: r1 = Instance_Color
    //     0xaf4a70: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf4a74: d0 = 0.700000
    //     0xaf4a74: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaf4a78: ldr             d0, [x17, #0xf48]
    // 0xaf4a7c: r0 = withOpacity()
    //     0xaf4a7c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf4a80: r16 = 14.000000
    //     0xaf4a80: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf4a84: ldr             x16, [x16, #0x1d8]
    // 0xaf4a88: stp             x0, x16, [SP]
    // 0xaf4a8c: ldur            x1, [fp, #-0x68]
    // 0xaf4a90: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf4a90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf4a94: ldr             x4, [x4, #0xaa0]
    // 0xaf4a98: r0 = copyWith()
    //     0xaf4a98: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf4a9c: stur            x0, [fp, #-0x68]
    // 0xaf4aa0: r0 = Text()
    //     0xaf4aa0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf4aa4: mov             x1, x0
    // 0xaf4aa8: ldur            x0, [fp, #-0x58]
    // 0xaf4aac: StoreField: r1->field_b = r0
    //     0xaf4aac: stur            w0, [x1, #0xb]
    // 0xaf4ab0: ldur            x0, [fp, #-0x68]
    // 0xaf4ab4: StoreField: r1->field_13 = r0
    //     0xaf4ab4: stur            w0, [x1, #0x13]
    // 0xaf4ab8: mov             x0, x1
    // 0xaf4abc: ldur            x1, [fp, #-0x50]
    // 0xaf4ac0: ArrayStore: r1[5] = r0  ; List_4
    //     0xaf4ac0: add             x25, x1, #0x23
    //     0xaf4ac4: str             w0, [x25]
    //     0xaf4ac8: tbz             w0, #0, #0xaf4ae4
    //     0xaf4acc: ldurb           w16, [x1, #-1]
    //     0xaf4ad0: ldurb           w17, [x0, #-1]
    //     0xaf4ad4: and             x16, x17, x16, lsr #2
    //     0xaf4ad8: tst             x16, HEAP, lsr #32
    //     0xaf4adc: b.eq            #0xaf4ae4
    //     0xaf4ae0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4ae4: r0 = Container()
    //     0xaf4ae4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf4ae8: stur            x0, [fp, #-0x58]
    // 0xaf4aec: r16 = 5.000000
    //     0xaf4aec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xaf4af0: ldr             x16, [x16, #0xcf0]
    // 0xaf4af4: str             x16, [SP]
    // 0xaf4af8: mov             x1, x0
    // 0xaf4afc: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xaf4afc: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xaf4b00: ldr             x4, [x4, #0xe0]
    // 0xaf4b04: r0 = Container()
    //     0xaf4b04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf4b08: ldur            x1, [fp, #-0x50]
    // 0xaf4b0c: ldur            x0, [fp, #-0x58]
    // 0xaf4b10: ArrayStore: r1[6] = r0  ; List_4
    //     0xaf4b10: add             x25, x1, #0x27
    //     0xaf4b14: str             w0, [x25]
    //     0xaf4b18: tbz             w0, #0, #0xaf4b34
    //     0xaf4b1c: ldurb           w16, [x1, #-1]
    //     0xaf4b20: ldurb           w17, [x0, #-1]
    //     0xaf4b24: and             x16, x17, x16, lsr #2
    //     0xaf4b28: tst             x16, HEAP, lsr #32
    //     0xaf4b2c: b.eq            #0xaf4b34
    //     0xaf4b30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4b34: ldur            x0, [fp, #-8]
    // 0xaf4b38: LoadField: r1 = r0->field_f
    //     0xaf4b38: ldur            w1, [x0, #0xf]
    // 0xaf4b3c: DecompressPointer r1
    //     0xaf4b3c: add             x1, x1, HEAP, lsl #32
    // 0xaf4b40: cmp             w1, NULL
    // 0xaf4b44: b.eq            #0xaf5520
    // 0xaf4b48: r0 = of()
    //     0xaf4b48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf4b4c: LoadField: r1 = r0->field_5b
    //     0xaf4b4c: ldur            w1, [x0, #0x5b]
    // 0xaf4b50: DecompressPointer r1
    //     0xaf4b50: add             x1, x1, HEAP, lsl #32
    // 0xaf4b54: stur            x1, [fp, #-0x58]
    // 0xaf4b58: r0 = BoxDecoration()
    //     0xaf4b58: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf4b5c: mov             x1, x0
    // 0xaf4b60: ldur            x0, [fp, #-0x58]
    // 0xaf4b64: stur            x1, [fp, #-0x68]
    // 0xaf4b68: StoreField: r1->field_7 = r0
    //     0xaf4b68: stur            w0, [x1, #7]
    // 0xaf4b6c: r0 = Instance_BoxShape
    //     0xaf4b6c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xaf4b70: ldr             x0, [x0, #0x970]
    // 0xaf4b74: StoreField: r1->field_23 = r0
    //     0xaf4b74: stur            w0, [x1, #0x23]
    // 0xaf4b78: r0 = Container()
    //     0xaf4b78: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf4b7c: stur            x0, [fp, #-0x58]
    // 0xaf4b80: r16 = Instance_EdgeInsets
    //     0xaf4b80: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xaf4b84: ldr             x16, [x16, #0x108]
    // 0xaf4b88: r30 = 5.000000
    //     0xaf4b88: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xaf4b8c: ldr             lr, [lr, #0xcf0]
    // 0xaf4b90: stp             lr, x16, [SP, #0x10]
    // 0xaf4b94: r16 = 5.000000
    //     0xaf4b94: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xaf4b98: ldr             x16, [x16, #0xcf0]
    // 0xaf4b9c: ldur            lr, [fp, #-0x68]
    // 0xaf4ba0: stp             lr, x16, [SP]
    // 0xaf4ba4: mov             x1, x0
    // 0xaf4ba8: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xaf4ba8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xaf4bac: ldr             x4, [x4, #0x118]
    // 0xaf4bb0: r0 = Container()
    //     0xaf4bb0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf4bb4: ldur            x1, [fp, #-0x50]
    // 0xaf4bb8: ldur            x0, [fp, #-0x58]
    // 0xaf4bbc: ArrayStore: r1[7] = r0  ; List_4
    //     0xaf4bbc: add             x25, x1, #0x2b
    //     0xaf4bc0: str             w0, [x25]
    //     0xaf4bc4: tbz             w0, #0, #0xaf4be0
    //     0xaf4bc8: ldurb           w16, [x1, #-1]
    //     0xaf4bcc: ldurb           w17, [x0, #-1]
    //     0xaf4bd0: and             x16, x17, x16, lsr #2
    //     0xaf4bd4: tst             x16, HEAP, lsr #32
    //     0xaf4bd8: b.eq            #0xaf4be0
    //     0xaf4bdc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4be0: ldur            x1, [fp, #-0x10]
    // 0xaf4be4: r0 = LoadClassIdInstr(r1)
    //     0xaf4be4: ldur            x0, [x1, #-1]
    //     0xaf4be8: ubfx            x0, x0, #0xc, #0x14
    // 0xaf4bec: ldur            x16, [fp, #-0x20]
    // 0xaf4bf0: stp             x16, x1, [SP]
    // 0xaf4bf4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf4bf4: sub             lr, x0, #0xb7
    //     0xaf4bf8: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4bfc: blr             lr
    // 0xaf4c00: LoadField: r1 = r0->field_a7
    //     0xaf4c00: ldur            w1, [x0, #0xa7]
    // 0xaf4c04: DecompressPointer r1
    //     0xaf4c04: add             x1, x1, HEAP, lsl #32
    // 0xaf4c08: cmp             w1, NULL
    // 0xaf4c0c: b.ne            #0xaf4c18
    // 0xaf4c10: r4 = ""
    //     0xaf4c10: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf4c14: b               #0xaf4c1c
    // 0xaf4c18: mov             x4, x1
    // 0xaf4c1c: ldur            x2, [fp, #-8]
    // 0xaf4c20: ldur            x0, [fp, #-0x10]
    // 0xaf4c24: ldur            x3, [fp, #-0x50]
    // 0xaf4c28: stur            x4, [fp, #-0x58]
    // 0xaf4c2c: LoadField: r1 = r2->field_f
    //     0xaf4c2c: ldur            w1, [x2, #0xf]
    // 0xaf4c30: DecompressPointer r1
    //     0xaf4c30: add             x1, x1, HEAP, lsl #32
    // 0xaf4c34: cmp             w1, NULL
    // 0xaf4c38: b.eq            #0xaf5524
    // 0xaf4c3c: r0 = of()
    //     0xaf4c3c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf4c40: LoadField: r1 = r0->field_87
    //     0xaf4c40: ldur            w1, [x0, #0x87]
    // 0xaf4c44: DecompressPointer r1
    //     0xaf4c44: add             x1, x1, HEAP, lsl #32
    // 0xaf4c48: LoadField: r0 = r1->field_2b
    //     0xaf4c48: ldur            w0, [x1, #0x2b]
    // 0xaf4c4c: DecompressPointer r0
    //     0xaf4c4c: add             x0, x0, HEAP, lsl #32
    // 0xaf4c50: stur            x0, [fp, #-0x68]
    // 0xaf4c54: r1 = Instance_Color
    //     0xaf4c54: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf4c58: d0 = 0.700000
    //     0xaf4c58: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaf4c5c: ldr             d0, [x17, #0xf48]
    // 0xaf4c60: r0 = withOpacity()
    //     0xaf4c60: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf4c64: r16 = 14.000000
    //     0xaf4c64: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf4c68: ldr             x16, [x16, #0x1d8]
    // 0xaf4c6c: stp             x0, x16, [SP]
    // 0xaf4c70: ldur            x1, [fp, #-0x68]
    // 0xaf4c74: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf4c74: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf4c78: ldr             x4, [x4, #0xaa0]
    // 0xaf4c7c: r0 = copyWith()
    //     0xaf4c7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf4c80: stur            x0, [fp, #-0x68]
    // 0xaf4c84: r0 = Text()
    //     0xaf4c84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf4c88: mov             x1, x0
    // 0xaf4c8c: ldur            x0, [fp, #-0x58]
    // 0xaf4c90: StoreField: r1->field_b = r0
    //     0xaf4c90: stur            w0, [x1, #0xb]
    // 0xaf4c94: ldur            x0, [fp, #-0x68]
    // 0xaf4c98: StoreField: r1->field_13 = r0
    //     0xaf4c98: stur            w0, [x1, #0x13]
    // 0xaf4c9c: mov             x0, x1
    // 0xaf4ca0: ldur            x1, [fp, #-0x50]
    // 0xaf4ca4: ArrayStore: r1[8] = r0  ; List_4
    //     0xaf4ca4: add             x25, x1, #0x2f
    //     0xaf4ca8: str             w0, [x25]
    //     0xaf4cac: tbz             w0, #0, #0xaf4cc8
    //     0xaf4cb0: ldurb           w16, [x1, #-1]
    //     0xaf4cb4: ldurb           w17, [x0, #-1]
    //     0xaf4cb8: and             x16, x17, x16, lsr #2
    //     0xaf4cbc: tst             x16, HEAP, lsr #32
    //     0xaf4cc0: b.eq            #0xaf4cc8
    //     0xaf4cc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf4cc8: r1 = <Widget>
    //     0xaf4cc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf4ccc: r0 = AllocateGrowableArray()
    //     0xaf4ccc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf4cd0: mov             x1, x0
    // 0xaf4cd4: ldur            x0, [fp, #-0x50]
    // 0xaf4cd8: stur            x1, [fp, #-0x58]
    // 0xaf4cdc: StoreField: r1->field_f = r0
    //     0xaf4cdc: stur            w0, [x1, #0xf]
    // 0xaf4ce0: r0 = 18
    //     0xaf4ce0: movz            x0, #0x12
    // 0xaf4ce4: StoreField: r1->field_b = r0
    //     0xaf4ce4: stur            w0, [x1, #0xb]
    // 0xaf4ce8: r0 = Row()
    //     0xaf4ce8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf4cec: mov             x2, x0
    // 0xaf4cf0: r1 = Instance_Axis
    //     0xaf4cf0: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf4cf4: stur            x2, [fp, #-0x50]
    // 0xaf4cf8: StoreField: r2->field_f = r1
    //     0xaf4cf8: stur            w1, [x2, #0xf]
    // 0xaf4cfc: r3 = Instance_MainAxisAlignment
    //     0xaf4cfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf4d00: ldr             x3, [x3, #0xa08]
    // 0xaf4d04: StoreField: r2->field_13 = r3
    //     0xaf4d04: stur            w3, [x2, #0x13]
    // 0xaf4d08: r4 = Instance_MainAxisSize
    //     0xaf4d08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf4d0c: ldr             x4, [x4, #0xa10]
    // 0xaf4d10: ArrayStore: r2[0] = r4  ; List_4
    //     0xaf4d10: stur            w4, [x2, #0x17]
    // 0xaf4d14: r0 = Instance_CrossAxisAlignment
    //     0xaf4d14: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf4d18: ldr             x0, [x0, #0xa18]
    // 0xaf4d1c: StoreField: r2->field_1b = r0
    //     0xaf4d1c: stur            w0, [x2, #0x1b]
    // 0xaf4d20: r5 = Instance_VerticalDirection
    //     0xaf4d20: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf4d24: ldr             x5, [x5, #0xa20]
    // 0xaf4d28: StoreField: r2->field_23 = r5
    //     0xaf4d28: stur            w5, [x2, #0x23]
    // 0xaf4d2c: r6 = Instance_Clip
    //     0xaf4d2c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf4d30: ldr             x6, [x6, #0x38]
    // 0xaf4d34: StoreField: r2->field_2b = r6
    //     0xaf4d34: stur            w6, [x2, #0x2b]
    // 0xaf4d38: StoreField: r2->field_2f = rZR
    //     0xaf4d38: stur            xzr, [x2, #0x2f]
    // 0xaf4d3c: ldur            x0, [fp, #-0x58]
    // 0xaf4d40: StoreField: r2->field_b = r0
    //     0xaf4d40: stur            w0, [x2, #0xb]
    // 0xaf4d44: ldur            x7, [fp, #-0x10]
    // 0xaf4d48: r0 = LoadClassIdInstr(r7)
    //     0xaf4d48: ldur            x0, [x7, #-1]
    //     0xaf4d4c: ubfx            x0, x0, #0xc, #0x14
    // 0xaf4d50: ldur            x16, [fp, #-0x20]
    // 0xaf4d54: stp             x16, x7, [SP]
    // 0xaf4d58: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf4d58: sub             lr, x0, #0xb7
    //     0xaf4d5c: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4d60: blr             lr
    // 0xaf4d64: LoadField: r1 = r0->field_9b
    //     0xaf4d64: ldur            w1, [x0, #0x9b]
    // 0xaf4d68: DecompressPointer r1
    //     0xaf4d68: add             x1, x1, HEAP, lsl #32
    // 0xaf4d6c: cmp             w1, NULL
    // 0xaf4d70: b.ne            #0xaf4d7c
    // 0xaf4d74: r1 = "0.0"
    //     0xaf4d74: add             x1, PP, #0xe, lsl #12  ; [pp+0xe628] "0.0"
    //     0xaf4d78: ldr             x1, [x1, #0x628]
    // 0xaf4d7c: ldur            x0, [fp, #-0x10]
    // 0xaf4d80: r0 = parse()
    //     0xaf4d80: bl              #0x64333c  ; [dart:core] double::parse
    // 0xaf4d84: ldur            x0, [fp, #-0x10]
    // 0xaf4d88: stur            d0, [fp, #-0x80]
    // 0xaf4d8c: r1 = LoadClassIdInstr(r0)
    //     0xaf4d8c: ldur            x1, [x0, #-1]
    //     0xaf4d90: ubfx            x1, x1, #0xc, #0x14
    // 0xaf4d94: ldur            x16, [fp, #-0x20]
    // 0xaf4d98: stp             x16, x0, [SP]
    // 0xaf4d9c: mov             x0, x1
    // 0xaf4da0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xaf4da0: sub             lr, x0, #0xb7
    //     0xaf4da4: ldr             lr, [x21, lr, lsl #3]
    //     0xaf4da8: blr             lr
    // 0xaf4dac: LoadField: r1 = r0->field_9b
    //     0xaf4dac: ldur            w1, [x0, #0x9b]
    // 0xaf4db0: DecompressPointer r1
    //     0xaf4db0: add             x1, x1, HEAP, lsl #32
    // 0xaf4db4: cmp             w1, NULL
    // 0xaf4db8: b.ne            #0xaf4dc0
    // 0xaf4dbc: r1 = "0"
    //     0xaf4dbc: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xaf4dc0: ldur            x2, [fp, #-0x40]
    // 0xaf4dc4: ldur            d0, [fp, #-0x80]
    // 0xaf4dc8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf4dc8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf4dcc: r0 = parse()
    //     0xaf4dcc: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xaf4dd0: stur            x0, [fp, #-0x18]
    // 0xaf4dd4: r0 = RatingWidget()
    //     0xaf4dd4: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xaf4dd8: mov             x3, x0
    // 0xaf4ddc: r0 = Instance_Icon
    //     0xaf4ddc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xaf4de0: ldr             x0, [x0, #0x190]
    // 0xaf4de4: stur            x3, [fp, #-0x10]
    // 0xaf4de8: StoreField: r3->field_7 = r0
    //     0xaf4de8: stur            w0, [x3, #7]
    // 0xaf4dec: r0 = Instance_Icon
    //     0xaf4dec: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xaf4df0: ldr             x0, [x0, #0x198]
    // 0xaf4df4: StoreField: r3->field_b = r0
    //     0xaf4df4: stur            w0, [x3, #0xb]
    // 0xaf4df8: r0 = Instance_Icon
    //     0xaf4df8: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xaf4dfc: ldr             x0, [x0, #0x1a0]
    // 0xaf4e00: StoreField: r3->field_f = r0
    //     0xaf4e00: stur            w0, [x3, #0xf]
    // 0xaf4e04: r1 = Function '<anonymous closure>':.
    //     0xaf4e04: add             x1, PP, #0x58, lsl #12  ; [pp+0x58080] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xaf4e08: ldr             x1, [x1, #0x80]
    // 0xaf4e0c: r2 = Null
    //     0xaf4e0c: mov             x2, NULL
    // 0xaf4e10: r0 = AllocateClosure()
    //     0xaf4e10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf4e14: stur            x0, [fp, #-0x20]
    // 0xaf4e18: r0 = RatingBar()
    //     0xaf4e18: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xaf4e1c: mov             x2, x0
    // 0xaf4e20: ldur            x0, [fp, #-0x20]
    // 0xaf4e24: stur            x2, [fp, #-0x58]
    // 0xaf4e28: StoreField: r2->field_b = r0
    //     0xaf4e28: stur            w0, [x2, #0xb]
    // 0xaf4e2c: r0 = true
    //     0xaf4e2c: add             x0, NULL, #0x20  ; true
    // 0xaf4e30: StoreField: r2->field_1f = r0
    //     0xaf4e30: stur            w0, [x2, #0x1f]
    // 0xaf4e34: r1 = Instance_Axis
    //     0xaf4e34: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf4e38: StoreField: r2->field_23 = r1
    //     0xaf4e38: stur            w1, [x2, #0x23]
    // 0xaf4e3c: StoreField: r2->field_27 = r0
    //     0xaf4e3c: stur            w0, [x2, #0x27]
    // 0xaf4e40: d0 = 2.000000
    //     0xaf4e40: fmov            d0, #2.00000000
    // 0xaf4e44: StoreField: r2->field_2b = d0
    //     0xaf4e44: stur            d0, [x2, #0x2b]
    // 0xaf4e48: StoreField: r2->field_33 = r0
    //     0xaf4e48: stur            w0, [x2, #0x33]
    // 0xaf4e4c: ldur            d0, [fp, #-0x80]
    // 0xaf4e50: StoreField: r2->field_37 = d0
    //     0xaf4e50: stur            d0, [x2, #0x37]
    // 0xaf4e54: ldur            x1, [fp, #-0x18]
    // 0xaf4e58: StoreField: r2->field_3f = r1
    //     0xaf4e58: stur            x1, [x2, #0x3f]
    // 0xaf4e5c: r1 = Instance_EdgeInsets
    //     0xaf4e5c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xaf4e60: StoreField: r2->field_47 = r1
    //     0xaf4e60: stur            w1, [x2, #0x47]
    // 0xaf4e64: d0 = 18.000000
    //     0xaf4e64: fmov            d0, #18.00000000
    // 0xaf4e68: StoreField: r2->field_4b = d0
    //     0xaf4e68: stur            d0, [x2, #0x4b]
    // 0xaf4e6c: StoreField: r2->field_53 = rZR
    //     0xaf4e6c: stur            xzr, [x2, #0x53]
    // 0xaf4e70: r1 = false
    //     0xaf4e70: add             x1, NULL, #0x30  ; false
    // 0xaf4e74: StoreField: r2->field_5b = r1
    //     0xaf4e74: stur            w1, [x2, #0x5b]
    // 0xaf4e78: StoreField: r2->field_5f = r1
    //     0xaf4e78: stur            w1, [x2, #0x5f]
    // 0xaf4e7c: ldur            x1, [fp, #-0x10]
    // 0xaf4e80: StoreField: r2->field_67 = r1
    //     0xaf4e80: stur            w1, [x2, #0x67]
    // 0xaf4e84: ldur            x3, [fp, #-0x40]
    // 0xaf4e88: LoadField: r1 = r3->field_9f
    //     0xaf4e88: ldur            w1, [x3, #0x9f]
    // 0xaf4e8c: DecompressPointer r1
    //     0xaf4e8c: add             x1, x1, HEAP, lsl #32
    // 0xaf4e90: cmp             w1, NULL
    // 0xaf4e94: b.ne            #0xaf4ea0
    // 0xaf4e98: r0 = Null
    //     0xaf4e98: mov             x0, NULL
    // 0xaf4e9c: b               #0xaf4ea4
    // 0xaf4ea0: r0 = trim()
    //     0xaf4ea0: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xaf4ea4: cmp             w0, NULL
    // 0xaf4ea8: b.ne            #0xaf4eb0
    // 0xaf4eac: r0 = ""
    //     0xaf4eac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf4eb0: ldur            x1, [fp, #-0x30]
    // 0xaf4eb4: stur            x0, [fp, #-0x10]
    // 0xaf4eb8: r0 = value()
    //     0xaf4eb8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf4ebc: tbnz            w0, #4, #0xaf4ec8
    // 0xaf4ec0: r0 = Null
    //     0xaf4ec0: mov             x0, NULL
    // 0xaf4ec4: b               #0xaf4ecc
    // 0xaf4ec8: r0 = 4
    //     0xaf4ec8: movz            x0, #0x4
    // 0xaf4ecc: ldur            x1, [fp, #-0x30]
    // 0xaf4ed0: stur            x0, [fp, #-0x20]
    // 0xaf4ed4: r0 = value()
    //     0xaf4ed4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf4ed8: tbnz            w0, #4, #0xaf4ee8
    // 0xaf4edc: r5 = Instance_TextOverflow
    //     0xaf4edc: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xaf4ee0: ldr             x5, [x5, #0x3a8]
    // 0xaf4ee4: b               #0xaf4ef0
    // 0xaf4ee8: r5 = Instance_TextOverflow
    //     0xaf4ee8: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xaf4eec: ldr             x5, [x5, #0xe10]
    // 0xaf4ef0: ldur            x4, [fp, #-8]
    // 0xaf4ef4: ldur            x3, [fp, #-0x40]
    // 0xaf4ef8: ldur            x2, [fp, #-0x10]
    // 0xaf4efc: ldur            x0, [fp, #-0x20]
    // 0xaf4f00: stur            x5, [fp, #-0x68]
    // 0xaf4f04: LoadField: r1 = r4->field_f
    //     0xaf4f04: ldur            w1, [x4, #0xf]
    // 0xaf4f08: DecompressPointer r1
    //     0xaf4f08: add             x1, x1, HEAP, lsl #32
    // 0xaf4f0c: cmp             w1, NULL
    // 0xaf4f10: b.eq            #0xaf5528
    // 0xaf4f14: r0 = of()
    //     0xaf4f14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf4f18: LoadField: r1 = r0->field_87
    //     0xaf4f18: ldur            w1, [x0, #0x87]
    // 0xaf4f1c: DecompressPointer r1
    //     0xaf4f1c: add             x1, x1, HEAP, lsl #32
    // 0xaf4f20: LoadField: r0 = r1->field_2b
    //     0xaf4f20: ldur            w0, [x1, #0x2b]
    // 0xaf4f24: DecompressPointer r0
    //     0xaf4f24: add             x0, x0, HEAP, lsl #32
    // 0xaf4f28: LoadField: r1 = r0->field_13
    //     0xaf4f28: ldur            w1, [x0, #0x13]
    // 0xaf4f2c: DecompressPointer r1
    //     0xaf4f2c: add             x1, x1, HEAP, lsl #32
    // 0xaf4f30: stur            x1, [fp, #-0x70]
    // 0xaf4f34: r0 = TextStyle()
    //     0xaf4f34: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xaf4f38: mov             x1, x0
    // 0xaf4f3c: r0 = true
    //     0xaf4f3c: add             x0, NULL, #0x20  ; true
    // 0xaf4f40: stur            x1, [fp, #-0x78]
    // 0xaf4f44: StoreField: r1->field_7 = r0
    //     0xaf4f44: stur            w0, [x1, #7]
    // 0xaf4f48: r2 = Instance_Color
    //     0xaf4f48: ldr             x2, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf4f4c: StoreField: r1->field_b = r2
    //     0xaf4f4c: stur            w2, [x1, #0xb]
    // 0xaf4f50: r2 = 12.000000
    //     0xaf4f50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf4f54: ldr             x2, [x2, #0x9e8]
    // 0xaf4f58: StoreField: r1->field_1f = r2
    //     0xaf4f58: stur            w2, [x1, #0x1f]
    // 0xaf4f5c: ldur            x2, [fp, #-0x70]
    // 0xaf4f60: StoreField: r1->field_13 = r2
    //     0xaf4f60: stur            w2, [x1, #0x13]
    // 0xaf4f64: r0 = Text()
    //     0xaf4f64: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf4f68: mov             x3, x0
    // 0xaf4f6c: ldur            x0, [fp, #-0x10]
    // 0xaf4f70: stur            x3, [fp, #-0x70]
    // 0xaf4f74: StoreField: r3->field_b = r0
    //     0xaf4f74: stur            w0, [x3, #0xb]
    // 0xaf4f78: ldur            x0, [fp, #-0x78]
    // 0xaf4f7c: StoreField: r3->field_13 = r0
    //     0xaf4f7c: stur            w0, [x3, #0x13]
    // 0xaf4f80: r0 = Instance_TextAlign
    //     0xaf4f80: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xaf4f84: StoreField: r3->field_1b = r0
    //     0xaf4f84: stur            w0, [x3, #0x1b]
    // 0xaf4f88: ldur            x0, [fp, #-0x68]
    // 0xaf4f8c: StoreField: r3->field_2b = r0
    //     0xaf4f8c: stur            w0, [x3, #0x2b]
    // 0xaf4f90: ldur            x0, [fp, #-0x20]
    // 0xaf4f94: StoreField: r3->field_37 = r0
    //     0xaf4f94: stur            w0, [x3, #0x37]
    // 0xaf4f98: r1 = Null
    //     0xaf4f98: mov             x1, NULL
    // 0xaf4f9c: r2 = 2
    //     0xaf4f9c: movz            x2, #0x2
    // 0xaf4fa0: r0 = AllocateArray()
    //     0xaf4fa0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf4fa4: mov             x2, x0
    // 0xaf4fa8: ldur            x0, [fp, #-0x70]
    // 0xaf4fac: stur            x2, [fp, #-0x10]
    // 0xaf4fb0: StoreField: r2->field_f = r0
    //     0xaf4fb0: stur            w0, [x2, #0xf]
    // 0xaf4fb4: r1 = <Widget>
    //     0xaf4fb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf4fb8: r0 = AllocateGrowableArray()
    //     0xaf4fb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf4fbc: mov             x2, x0
    // 0xaf4fc0: ldur            x0, [fp, #-0x10]
    // 0xaf4fc4: stur            x2, [fp, #-0x20]
    // 0xaf4fc8: StoreField: r2->field_f = r0
    //     0xaf4fc8: stur            w0, [x2, #0xf]
    // 0xaf4fcc: r0 = 2
    //     0xaf4fcc: movz            x0, #0x2
    // 0xaf4fd0: StoreField: r2->field_b = r0
    //     0xaf4fd0: stur            w0, [x2, #0xb]
    // 0xaf4fd4: ldur            x0, [fp, #-0x40]
    // 0xaf4fd8: LoadField: r1 = r0->field_9f
    //     0xaf4fd8: ldur            w1, [x0, #0x9f]
    // 0xaf4fdc: DecompressPointer r1
    //     0xaf4fdc: add             x1, x1, HEAP, lsl #32
    // 0xaf4fe0: cmp             w1, NULL
    // 0xaf4fe4: b.ne            #0xaf4ff0
    // 0xaf4fe8: r0 = Null
    //     0xaf4fe8: mov             x0, NULL
    // 0xaf4fec: b               #0xaf4ff4
    // 0xaf4ff0: r0 = trim()
    //     0xaf4ff0: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xaf4ff4: cmp             w0, NULL
    // 0xaf4ff8: b.ne            #0xaf5004
    // 0xaf4ffc: r1 = ""
    //     0xaf4ffc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf5000: b               #0xaf5008
    // 0xaf5004: mov             x1, x0
    // 0xaf5008: ldur            x0, [fp, #-8]
    // 0xaf500c: LoadField: r2 = r0->field_f
    //     0xaf500c: ldur            w2, [x0, #0xf]
    // 0xaf5010: DecompressPointer r2
    //     0xaf5010: add             x2, x2, HEAP, lsl #32
    // 0xaf5014: cmp             w2, NULL
    // 0xaf5018: b.eq            #0xaf552c
    // 0xaf501c: r0 = TextExceeds.textExceedsLines()
    //     0xaf501c: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xaf5020: tbnz            w0, #4, #0xaf51ac
    // 0xaf5024: ldur            x1, [fp, #-0x30]
    // 0xaf5028: r0 = value()
    //     0xaf5028: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf502c: tbnz            w0, #4, #0xaf503c
    // 0xaf5030: r3 = "Know Less"
    //     0xaf5030: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xaf5034: ldr             x3, [x3, #0x1d0]
    // 0xaf5038: b               #0xaf5044
    // 0xaf503c: r3 = "Know more"
    //     0xaf503c: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xaf5040: ldr             x3, [x3, #0x20]
    // 0xaf5044: ldur            x0, [fp, #-8]
    // 0xaf5048: ldur            x2, [fp, #-0x20]
    // 0xaf504c: stur            x3, [fp, #-0x10]
    // 0xaf5050: LoadField: r1 = r0->field_f
    //     0xaf5050: ldur            w1, [x0, #0xf]
    // 0xaf5054: DecompressPointer r1
    //     0xaf5054: add             x1, x1, HEAP, lsl #32
    // 0xaf5058: cmp             w1, NULL
    // 0xaf505c: b.eq            #0xaf5530
    // 0xaf5060: r0 = of()
    //     0xaf5060: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf5064: LoadField: r1 = r0->field_87
    //     0xaf5064: ldur            w1, [x0, #0x87]
    // 0xaf5068: DecompressPointer r1
    //     0xaf5068: add             x1, x1, HEAP, lsl #32
    // 0xaf506c: LoadField: r0 = r1->field_7
    //     0xaf506c: ldur            w0, [x1, #7]
    // 0xaf5070: DecompressPointer r0
    //     0xaf5070: add             x0, x0, HEAP, lsl #32
    // 0xaf5074: ldur            x2, [fp, #-8]
    // 0xaf5078: stur            x0, [fp, #-0x30]
    // 0xaf507c: LoadField: r1 = r2->field_f
    //     0xaf507c: ldur            w1, [x2, #0xf]
    // 0xaf5080: DecompressPointer r1
    //     0xaf5080: add             x1, x1, HEAP, lsl #32
    // 0xaf5084: cmp             w1, NULL
    // 0xaf5088: b.eq            #0xaf5534
    // 0xaf508c: r0 = of()
    //     0xaf508c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf5090: LoadField: r1 = r0->field_5b
    //     0xaf5090: ldur            w1, [x0, #0x5b]
    // 0xaf5094: DecompressPointer r1
    //     0xaf5094: add             x1, x1, HEAP, lsl #32
    // 0xaf5098: r16 = 12.000000
    //     0xaf5098: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf509c: ldr             x16, [x16, #0x9e8]
    // 0xaf50a0: stp             x1, x16, [SP, #8]
    // 0xaf50a4: r16 = Instance_TextDecoration
    //     0xaf50a4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xaf50a8: ldr             x16, [x16, #0x10]
    // 0xaf50ac: str             x16, [SP]
    // 0xaf50b0: ldur            x1, [fp, #-0x30]
    // 0xaf50b4: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xaf50b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xaf50b8: ldr             x4, [x4, #0xe38]
    // 0xaf50bc: r0 = copyWith()
    //     0xaf50bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf50c0: stur            x0, [fp, #-0x30]
    // 0xaf50c4: r0 = Text()
    //     0xaf50c4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf50c8: mov             x1, x0
    // 0xaf50cc: ldur            x0, [fp, #-0x10]
    // 0xaf50d0: stur            x1, [fp, #-0x68]
    // 0xaf50d4: StoreField: r1->field_b = r0
    //     0xaf50d4: stur            w0, [x1, #0xb]
    // 0xaf50d8: ldur            x0, [fp, #-0x30]
    // 0xaf50dc: StoreField: r1->field_13 = r0
    //     0xaf50dc: stur            w0, [x1, #0x13]
    // 0xaf50e0: r0 = Padding()
    //     0xaf50e0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf50e4: mov             x1, x0
    // 0xaf50e8: r0 = Instance_EdgeInsets
    //     0xaf50e8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xaf50ec: ldr             x0, [x0, #0x668]
    // 0xaf50f0: stur            x1, [fp, #-0x10]
    // 0xaf50f4: StoreField: r1->field_f = r0
    //     0xaf50f4: stur            w0, [x1, #0xf]
    // 0xaf50f8: ldur            x0, [fp, #-0x68]
    // 0xaf50fc: StoreField: r1->field_b = r0
    //     0xaf50fc: stur            w0, [x1, #0xb]
    // 0xaf5100: r0 = GestureDetector()
    //     0xaf5100: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaf5104: ldur            x2, [fp, #-0x28]
    // 0xaf5108: r1 = Function '<anonymous closure>':.
    //     0xaf5108: add             x1, PP, #0x58, lsl #12  ; [pp+0x58088] AnonymousClosure: (0xaf6064), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget (0xaf44e0)
    //     0xaf510c: ldr             x1, [x1, #0x88]
    // 0xaf5110: stur            x0, [fp, #-0x28]
    // 0xaf5114: r0 = AllocateClosure()
    //     0xaf5114: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5118: ldur            x16, [fp, #-0x10]
    // 0xaf511c: stp             x16, x0, [SP]
    // 0xaf5120: ldur            x1, [fp, #-0x28]
    // 0xaf5124: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaf5124: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaf5128: ldr             x4, [x4, #0xaf0]
    // 0xaf512c: r0 = GestureDetector()
    //     0xaf512c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaf5130: ldur            x0, [fp, #-0x20]
    // 0xaf5134: LoadField: r1 = r0->field_b
    //     0xaf5134: ldur            w1, [x0, #0xb]
    // 0xaf5138: LoadField: r2 = r0->field_f
    //     0xaf5138: ldur            w2, [x0, #0xf]
    // 0xaf513c: DecompressPointer r2
    //     0xaf513c: add             x2, x2, HEAP, lsl #32
    // 0xaf5140: LoadField: r3 = r2->field_b
    //     0xaf5140: ldur            w3, [x2, #0xb]
    // 0xaf5144: r2 = LoadInt32Instr(r1)
    //     0xaf5144: sbfx            x2, x1, #1, #0x1f
    // 0xaf5148: stur            x2, [fp, #-0x18]
    // 0xaf514c: r1 = LoadInt32Instr(r3)
    //     0xaf514c: sbfx            x1, x3, #1, #0x1f
    // 0xaf5150: cmp             x2, x1
    // 0xaf5154: b.ne            #0xaf5160
    // 0xaf5158: mov             x1, x0
    // 0xaf515c: r0 = _growToNextCapacity()
    //     0xaf515c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf5160: ldur            x2, [fp, #-0x20]
    // 0xaf5164: ldur            x3, [fp, #-0x18]
    // 0xaf5168: add             x0, x3, #1
    // 0xaf516c: lsl             x1, x0, #1
    // 0xaf5170: StoreField: r2->field_b = r1
    //     0xaf5170: stur            w1, [x2, #0xb]
    // 0xaf5174: LoadField: r1 = r2->field_f
    //     0xaf5174: ldur            w1, [x2, #0xf]
    // 0xaf5178: DecompressPointer r1
    //     0xaf5178: add             x1, x1, HEAP, lsl #32
    // 0xaf517c: ldur            x0, [fp, #-0x28]
    // 0xaf5180: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf5180: add             x25, x1, x3, lsl #2
    //     0xaf5184: add             x25, x25, #0xf
    //     0xaf5188: str             w0, [x25]
    //     0xaf518c: tbz             w0, #0, #0xaf51a8
    //     0xaf5190: ldurb           w16, [x1, #-1]
    //     0xaf5194: ldurb           w17, [x0, #-1]
    //     0xaf5198: and             x16, x17, x16, lsr #2
    //     0xaf519c: tst             x16, HEAP, lsr #32
    //     0xaf51a0: b.eq            #0xaf51a8
    //     0xaf51a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf51a8: b               #0xaf51b0
    // 0xaf51ac: ldur            x2, [fp, #-0x20]
    // 0xaf51b0: ldur            x4, [fp, #-0x38]
    // 0xaf51b4: ldur            x3, [fp, #-0x60]
    // 0xaf51b8: ldur            x1, [fp, #-0x50]
    // 0xaf51bc: ldur            x0, [fp, #-0x58]
    // 0xaf51c0: r0 = Column()
    //     0xaf51c0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf51c4: mov             x3, x0
    // 0xaf51c8: r0 = Instance_Axis
    //     0xaf51c8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf51cc: stur            x3, [fp, #-0x10]
    // 0xaf51d0: StoreField: r3->field_f = r0
    //     0xaf51d0: stur            w0, [x3, #0xf]
    // 0xaf51d4: r4 = Instance_MainAxisAlignment
    //     0xaf51d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf51d8: ldr             x4, [x4, #0xa08]
    // 0xaf51dc: StoreField: r3->field_13 = r4
    //     0xaf51dc: stur            w4, [x3, #0x13]
    // 0xaf51e0: r1 = Instance_MainAxisSize
    //     0xaf51e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf51e4: ldr             x1, [x1, #0xa10]
    // 0xaf51e8: ArrayStore: r3[0] = r1  ; List_4
    //     0xaf51e8: stur            w1, [x3, #0x17]
    // 0xaf51ec: r5 = Instance_CrossAxisAlignment
    //     0xaf51ec: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaf51f0: ldr             x5, [x5, #0x890]
    // 0xaf51f4: StoreField: r3->field_1b = r5
    //     0xaf51f4: stur            w5, [x3, #0x1b]
    // 0xaf51f8: r6 = Instance_VerticalDirection
    //     0xaf51f8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf51fc: ldr             x6, [x6, #0xa20]
    // 0xaf5200: StoreField: r3->field_23 = r6
    //     0xaf5200: stur            w6, [x3, #0x23]
    // 0xaf5204: r7 = Instance_Clip
    //     0xaf5204: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf5208: ldr             x7, [x7, #0x38]
    // 0xaf520c: StoreField: r3->field_2b = r7
    //     0xaf520c: stur            w7, [x3, #0x2b]
    // 0xaf5210: StoreField: r3->field_2f = rZR
    //     0xaf5210: stur            xzr, [x3, #0x2f]
    // 0xaf5214: ldur            x1, [fp, #-0x20]
    // 0xaf5218: StoreField: r3->field_b = r1
    //     0xaf5218: stur            w1, [x3, #0xb]
    // 0xaf521c: r1 = Null
    //     0xaf521c: mov             x1, NULL
    // 0xaf5220: r2 = 16
    //     0xaf5220: movz            x2, #0x10
    // 0xaf5224: r0 = AllocateArray()
    //     0xaf5224: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf5228: mov             x2, x0
    // 0xaf522c: ldur            x0, [fp, #-0x60]
    // 0xaf5230: stur            x2, [fp, #-0x20]
    // 0xaf5234: StoreField: r2->field_f = r0
    //     0xaf5234: stur            w0, [x2, #0xf]
    // 0xaf5238: r16 = Instance_SizedBox
    //     0xaf5238: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xaf523c: ldr             x16, [x16, #0xd8]
    // 0xaf5240: StoreField: r2->field_13 = r16
    //     0xaf5240: stur            w16, [x2, #0x13]
    // 0xaf5244: ldur            x0, [fp, #-0x50]
    // 0xaf5248: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf5248: stur            w0, [x2, #0x17]
    // 0xaf524c: r16 = Instance_SizedBox
    //     0xaf524c: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xaf5250: ldr             x16, [x16, #0xd8]
    // 0xaf5254: StoreField: r2->field_1b = r16
    //     0xaf5254: stur            w16, [x2, #0x1b]
    // 0xaf5258: ldur            x0, [fp, #-0x58]
    // 0xaf525c: StoreField: r2->field_1f = r0
    //     0xaf525c: stur            w0, [x2, #0x1f]
    // 0xaf5260: r16 = Instance_SizedBox
    //     0xaf5260: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xaf5264: ldr             x16, [x16, #0x9f0]
    // 0xaf5268: StoreField: r2->field_23 = r16
    //     0xaf5268: stur            w16, [x2, #0x23]
    // 0xaf526c: ldur            x0, [fp, #-0x10]
    // 0xaf5270: StoreField: r2->field_27 = r0
    //     0xaf5270: stur            w0, [x2, #0x27]
    // 0xaf5274: r16 = Instance_SizedBox
    //     0xaf5274: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xaf5278: ldr             x16, [x16, #0x9f0]
    // 0xaf527c: StoreField: r2->field_2b = r16
    //     0xaf527c: stur            w16, [x2, #0x2b]
    // 0xaf5280: r1 = <Widget>
    //     0xaf5280: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf5284: r0 = AllocateGrowableArray()
    //     0xaf5284: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf5288: mov             x3, x0
    // 0xaf528c: ldur            x0, [fp, #-0x20]
    // 0xaf5290: stur            x3, [fp, #-0x10]
    // 0xaf5294: StoreField: r3->field_f = r0
    //     0xaf5294: stur            w0, [x3, #0xf]
    // 0xaf5298: r0 = 16
    //     0xaf5298: movz            x0, #0x10
    // 0xaf529c: StoreField: r3->field_b = r0
    //     0xaf529c: stur            w0, [x3, #0xb]
    // 0xaf52a0: ldur            x0, [fp, #-0x38]
    // 0xaf52a4: tbnz            w0, #4, #0xaf53e4
    // 0xaf52a8: ldur            x2, [fp, #-0x40]
    // 0xaf52ac: LoadField: r0 = r2->field_ab
    //     0xaf52ac: ldur            w0, [x2, #0xab]
    // 0xaf52b0: DecompressPointer r0
    //     0xaf52b0: add             x0, x0, HEAP, lsl #32
    // 0xaf52b4: cmp             w0, NULL
    // 0xaf52b8: b.ne            #0xaf52c4
    // 0xaf52bc: r0 = Null
    //     0xaf52bc: mov             x0, NULL
    // 0xaf52c0: b               #0xaf52cc
    // 0xaf52c4: LoadField: r1 = r0->field_b
    //     0xaf52c4: ldur            w1, [x0, #0xb]
    // 0xaf52c8: mov             x0, x1
    // 0xaf52cc: cmp             w0, NULL
    // 0xaf52d0: b.ne            #0xaf52dc
    // 0xaf52d4: r0 = 0
    //     0xaf52d4: movz            x0, #0
    // 0xaf52d8: b               #0xaf52e4
    // 0xaf52dc: r1 = LoadInt32Instr(r0)
    //     0xaf52dc: sbfx            x1, x0, #1, #0x1f
    // 0xaf52e0: mov             x0, x1
    // 0xaf52e4: cmp             x0, #3
    // 0xaf52e8: b.gt            #0xaf5308
    // 0xaf52ec: ldur            x1, [fp, #-8]
    // 0xaf52f0: LoadField: r0 = r1->field_f
    //     0xaf52f0: ldur            w0, [x1, #0xf]
    // 0xaf52f4: DecompressPointer r0
    //     0xaf52f4: add             x0, x0, HEAP, lsl #32
    // 0xaf52f8: cmp             w0, NULL
    // 0xaf52fc: b.eq            #0xaf5538
    // 0xaf5300: r0 = _buildImagesRow()
    //     0xaf5300: bl              #0xaf5d60  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow
    // 0xaf5304: b               #0xaf5320
    // 0xaf5308: ldur            x1, [fp, #-8]
    // 0xaf530c: LoadField: r3 = r1->field_f
    //     0xaf530c: ldur            w3, [x1, #0xf]
    // 0xaf5310: DecompressPointer r3
    //     0xaf5310: add             x3, x3, HEAP, lsl #32
    // 0xaf5314: cmp             w3, NULL
    // 0xaf5318: b.eq            #0xaf553c
    // 0xaf531c: r0 = _buildImagesRowWithMore()
    //     0xaf531c: bl              #0xaf5540  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore
    // 0xaf5320: ldur            x1, [fp, #-0x10]
    // 0xaf5324: stur            x0, [fp, #-8]
    // 0xaf5328: r0 = SizedBox()
    //     0xaf5328: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf532c: mov             x1, x0
    // 0xaf5330: r0 = 120.000000
    //     0xaf5330: add             x0, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xaf5334: ldr             x0, [x0, #0x3a0]
    // 0xaf5338: stur            x1, [fp, #-0x20]
    // 0xaf533c: StoreField: r1->field_13 = r0
    //     0xaf533c: stur            w0, [x1, #0x13]
    // 0xaf5340: ldur            x0, [fp, #-8]
    // 0xaf5344: StoreField: r1->field_b = r0
    //     0xaf5344: stur            w0, [x1, #0xb]
    // 0xaf5348: r0 = Padding()
    //     0xaf5348: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf534c: mov             x2, x0
    // 0xaf5350: r0 = Instance_EdgeInsets
    //     0xaf5350: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xaf5354: ldr             x0, [x0, #0x858]
    // 0xaf5358: stur            x2, [fp, #-8]
    // 0xaf535c: StoreField: r2->field_f = r0
    //     0xaf535c: stur            w0, [x2, #0xf]
    // 0xaf5360: ldur            x0, [fp, #-0x20]
    // 0xaf5364: StoreField: r2->field_b = r0
    //     0xaf5364: stur            w0, [x2, #0xb]
    // 0xaf5368: ldur            x0, [fp, #-0x10]
    // 0xaf536c: LoadField: r1 = r0->field_b
    //     0xaf536c: ldur            w1, [x0, #0xb]
    // 0xaf5370: LoadField: r3 = r0->field_f
    //     0xaf5370: ldur            w3, [x0, #0xf]
    // 0xaf5374: DecompressPointer r3
    //     0xaf5374: add             x3, x3, HEAP, lsl #32
    // 0xaf5378: LoadField: r4 = r3->field_b
    //     0xaf5378: ldur            w4, [x3, #0xb]
    // 0xaf537c: r3 = LoadInt32Instr(r1)
    //     0xaf537c: sbfx            x3, x1, #1, #0x1f
    // 0xaf5380: stur            x3, [fp, #-0x18]
    // 0xaf5384: r1 = LoadInt32Instr(r4)
    //     0xaf5384: sbfx            x1, x4, #1, #0x1f
    // 0xaf5388: cmp             x3, x1
    // 0xaf538c: b.ne            #0xaf5398
    // 0xaf5390: mov             x1, x0
    // 0xaf5394: r0 = _growToNextCapacity()
    //     0xaf5394: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xaf5398: ldur            x2, [fp, #-0x10]
    // 0xaf539c: ldur            x3, [fp, #-0x18]
    // 0xaf53a0: add             x0, x3, #1
    // 0xaf53a4: lsl             x1, x0, #1
    // 0xaf53a8: StoreField: r2->field_b = r1
    //     0xaf53a8: stur            w1, [x2, #0xb]
    // 0xaf53ac: LoadField: r1 = r2->field_f
    //     0xaf53ac: ldur            w1, [x2, #0xf]
    // 0xaf53b0: DecompressPointer r1
    //     0xaf53b0: add             x1, x1, HEAP, lsl #32
    // 0xaf53b4: ldur            x0, [fp, #-8]
    // 0xaf53b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xaf53b8: add             x25, x1, x3, lsl #2
    //     0xaf53bc: add             x25, x25, #0xf
    //     0xaf53c0: str             w0, [x25]
    //     0xaf53c4: tbz             w0, #0, #0xaf53e0
    //     0xaf53c8: ldurb           w16, [x1, #-1]
    //     0xaf53cc: ldurb           w17, [x0, #-1]
    //     0xaf53d0: and             x16, x17, x16, lsr #2
    //     0xaf53d4: tst             x16, HEAP, lsr #32
    //     0xaf53d8: b.eq            #0xaf53e0
    //     0xaf53dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xaf53e0: b               #0xaf53e8
    // 0xaf53e4: mov             x2, x3
    // 0xaf53e8: ldur            x0, [fp, #-0x48]
    // 0xaf53ec: r0 = Column()
    //     0xaf53ec: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf53f0: mov             x1, x0
    // 0xaf53f4: r0 = Instance_Axis
    //     0xaf53f4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf53f8: stur            x1, [fp, #-8]
    // 0xaf53fc: StoreField: r1->field_f = r0
    //     0xaf53fc: stur            w0, [x1, #0xf]
    // 0xaf5400: r0 = Instance_MainAxisAlignment
    //     0xaf5400: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf5404: ldr             x0, [x0, #0xa08]
    // 0xaf5408: StoreField: r1->field_13 = r0
    //     0xaf5408: stur            w0, [x1, #0x13]
    // 0xaf540c: r0 = Instance_MainAxisSize
    //     0xaf540c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xaf5410: ldr             x0, [x0, #0xdd0]
    // 0xaf5414: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf5414: stur            w0, [x1, #0x17]
    // 0xaf5418: r0 = Instance_CrossAxisAlignment
    //     0xaf5418: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaf541c: ldr             x0, [x0, #0x890]
    // 0xaf5420: StoreField: r1->field_1b = r0
    //     0xaf5420: stur            w0, [x1, #0x1b]
    // 0xaf5424: r0 = Instance_VerticalDirection
    //     0xaf5424: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf5428: ldr             x0, [x0, #0xa20]
    // 0xaf542c: StoreField: r1->field_23 = r0
    //     0xaf542c: stur            w0, [x1, #0x23]
    // 0xaf5430: r0 = Instance_Clip
    //     0xaf5430: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf5434: ldr             x0, [x0, #0x38]
    // 0xaf5438: StoreField: r1->field_2b = r0
    //     0xaf5438: stur            w0, [x1, #0x2b]
    // 0xaf543c: StoreField: r1->field_2f = rZR
    //     0xaf543c: stur            xzr, [x1, #0x2f]
    // 0xaf5440: ldur            x0, [fp, #-0x10]
    // 0xaf5444: StoreField: r1->field_b = r0
    //     0xaf5444: stur            w0, [x1, #0xb]
    // 0xaf5448: r0 = Padding()
    //     0xaf5448: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf544c: mov             x1, x0
    // 0xaf5450: r0 = Instance_EdgeInsets
    //     0xaf5450: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xaf5454: ldr             x0, [x0, #0x560]
    // 0xaf5458: stur            x1, [fp, #-0x10]
    // 0xaf545c: StoreField: r1->field_f = r0
    //     0xaf545c: stur            w0, [x1, #0xf]
    // 0xaf5460: ldur            x0, [fp, #-8]
    // 0xaf5464: StoreField: r1->field_b = r0
    //     0xaf5464: stur            w0, [x1, #0xb]
    // 0xaf5468: r0 = Card()
    //     0xaf5468: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xaf546c: mov             x1, x0
    // 0xaf5470: r0 = 0.000000
    //     0xaf5470: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xaf5474: stur            x1, [fp, #-8]
    // 0xaf5478: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf5478: stur            w0, [x1, #0x17]
    // 0xaf547c: ldur            x0, [fp, #-0x48]
    // 0xaf5480: StoreField: r1->field_1b = r0
    //     0xaf5480: stur            w0, [x1, #0x1b]
    // 0xaf5484: r0 = true
    //     0xaf5484: add             x0, NULL, #0x20  ; true
    // 0xaf5488: StoreField: r1->field_1f = r0
    //     0xaf5488: stur            w0, [x1, #0x1f]
    // 0xaf548c: ldur            x2, [fp, #-0x10]
    // 0xaf5490: StoreField: r1->field_2f = r2
    //     0xaf5490: stur            w2, [x1, #0x2f]
    // 0xaf5494: StoreField: r1->field_2b = r0
    //     0xaf5494: stur            w0, [x1, #0x2b]
    // 0xaf5498: r0 = Instance__CardVariant
    //     0xaf5498: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xaf549c: ldr             x0, [x0, #0xa68]
    // 0xaf54a0: StoreField: r1->field_33 = r0
    //     0xaf54a0: stur            w0, [x1, #0x33]
    // 0xaf54a4: r0 = Container()
    //     0xaf54a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf54a8: stur            x0, [fp, #-0x10]
    // 0xaf54ac: r16 = Instance_EdgeInsets
    //     0xaf54ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xaf54b0: ldr             x16, [x16, #0x980]
    // 0xaf54b4: ldur            lr, [fp, #-8]
    // 0xaf54b8: stp             lr, x16, [SP]
    // 0xaf54bc: mov             x1, x0
    // 0xaf54c0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, padding, 0x1, null]
    //     0xaf54c0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36030] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "padding", 0x1, Null]
    //     0xaf54c4: ldr             x4, [x4, #0x30]
    // 0xaf54c8: r0 = Container()
    //     0xaf54c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf54cc: r0 = AnimatedContainer()
    //     0xaf54cc: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xaf54d0: stur            x0, [fp, #-8]
    // 0xaf54d4: r16 = Instance_Cubic
    //     0xaf54d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xaf54d8: ldr             x16, [x16, #0xaf8]
    // 0xaf54dc: str             x16, [SP]
    // 0xaf54e0: mov             x1, x0
    // 0xaf54e4: ldur            x2, [fp, #-0x10]
    // 0xaf54e8: r3 = Instance_Duration
    //     0xaf54e8: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xaf54ec: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xaf54ec: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xaf54f0: ldr             x4, [x4, #0xbc8]
    // 0xaf54f4: r0 = AnimatedContainer()
    //     0xaf54f4: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xaf54f8: ldur            x0, [fp, #-8]
    // 0xaf54fc: LeaveFrame
    //     0xaf54fc: mov             SP, fp
    //     0xaf5500: ldp             fp, lr, [SP], #0x10
    // 0xaf5504: ret
    //     0xaf5504: ret             
    // 0xaf5508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5508: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf550c: b               #0xaf4504
    // 0xaf5510: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5510: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5514: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5514: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5518: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5518: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf551c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf551c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5520: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5520: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5524: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5524: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5528: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5528: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf552c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf552c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5530: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5530: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5534: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5534: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf5538: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf5538: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf553c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf553c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildImagesRowWithMore(/* No info */) {
    // ** addr: 0xaf5540, size: 0x490
    // 0xaf5540: EnterFrame
    //     0xaf5540: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5544: mov             fp, SP
    // 0xaf5548: AllocStack(0x58)
    //     0xaf5548: sub             SP, SP, #0x58
    // 0xaf554c: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xaf554c: stur            x1, [fp, #-8]
    //     0xaf5550: stur            x2, [fp, #-0x10]
    //     0xaf5554: stur            x3, [fp, #-0x18]
    // 0xaf5558: CheckStackOverflow
    //     0xaf5558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf555c: cmp             SP, x16
    //     0xaf5560: b.ls            #0xaf59c8
    // 0xaf5564: r1 = 3
    //     0xaf5564: movz            x1, #0x3
    // 0xaf5568: r0 = AllocateContext()
    //     0xaf5568: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf556c: mov             x4, x0
    // 0xaf5570: ldur            x0, [fp, #-8]
    // 0xaf5574: stur            x4, [fp, #-0x20]
    // 0xaf5578: StoreField: r4->field_f = r0
    //     0xaf5578: stur            w0, [x4, #0xf]
    // 0xaf557c: ldur            x2, [fp, #-0x10]
    // 0xaf5580: StoreField: r4->field_13 = r2
    //     0xaf5580: stur            w2, [x4, #0x13]
    // 0xaf5584: ldur            x1, [fp, #-0x18]
    // 0xaf5588: ArrayStore: r4[0] = r1  ; List_4
    //     0xaf5588: stur            w1, [x4, #0x17]
    // 0xaf558c: mov             x1, x0
    // 0xaf5590: r3 = 0
    //     0xaf5590: movz            x3, #0
    // 0xaf5594: r0 = _buildImageThumbnail()
    //     0xaf5594: bl              #0xaf59d0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xaf5598: stur            x0, [fp, #-0x10]
    // 0xaf559c: r0 = Padding()
    //     0xaf559c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf55a0: mov             x1, x0
    // 0xaf55a4: r0 = Instance_EdgeInsets
    //     0xaf55a4: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xaf55a8: ldr             x0, [x0, #0x550]
    // 0xaf55ac: stur            x1, [fp, #-0x18]
    // 0xaf55b0: StoreField: r1->field_f = r0
    //     0xaf55b0: stur            w0, [x1, #0xf]
    // 0xaf55b4: ldur            x2, [fp, #-0x10]
    // 0xaf55b8: StoreField: r1->field_b = r2
    //     0xaf55b8: stur            w2, [x1, #0xb]
    // 0xaf55bc: r0 = GestureDetector()
    //     0xaf55bc: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaf55c0: ldur            x2, [fp, #-0x20]
    // 0xaf55c4: r1 = Function '<anonymous closure>':.
    //     0xaf55c4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58098] AnonymousClosure: (0xaf5d04), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xaf5540)
    //     0xaf55c8: ldr             x1, [x1, #0x98]
    // 0xaf55cc: stur            x0, [fp, #-0x10]
    // 0xaf55d0: r0 = AllocateClosure()
    //     0xaf55d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf55d4: ldur            x16, [fp, #-0x18]
    // 0xaf55d8: stp             x16, x0, [SP]
    // 0xaf55dc: ldur            x1, [fp, #-0x10]
    // 0xaf55e0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaf55e0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaf55e4: ldr             x4, [x4, #0xaf0]
    // 0xaf55e8: r0 = GestureDetector()
    //     0xaf55e8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaf55ec: ldur            x0, [fp, #-0x20]
    // 0xaf55f0: LoadField: r2 = r0->field_13
    //     0xaf55f0: ldur            w2, [x0, #0x13]
    // 0xaf55f4: DecompressPointer r2
    //     0xaf55f4: add             x2, x2, HEAP, lsl #32
    // 0xaf55f8: ldur            x1, [fp, #-8]
    // 0xaf55fc: r3 = 1
    //     0xaf55fc: movz            x3, #0x1
    // 0xaf5600: r0 = _buildImageThumbnail()
    //     0xaf5600: bl              #0xaf59d0  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xaf5604: stur            x0, [fp, #-8]
    // 0xaf5608: r0 = Padding()
    //     0xaf5608: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf560c: mov             x1, x0
    // 0xaf5610: r0 = Instance_EdgeInsets
    //     0xaf5610: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xaf5614: ldr             x0, [x0, #0x550]
    // 0xaf5618: stur            x1, [fp, #-0x18]
    // 0xaf561c: StoreField: r1->field_f = r0
    //     0xaf561c: stur            w0, [x1, #0xf]
    // 0xaf5620: ldur            x0, [fp, #-8]
    // 0xaf5624: StoreField: r1->field_b = r0
    //     0xaf5624: stur            w0, [x1, #0xb]
    // 0xaf5628: r0 = GestureDetector()
    //     0xaf5628: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaf562c: ldur            x2, [fp, #-0x20]
    // 0xaf5630: r1 = Function '<anonymous closure>':.
    //     0xaf5630: add             x1, PP, #0x58, lsl #12  ; [pp+0x580a0] AnonymousClosure: (0xaf5ca8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xaf5540)
    //     0xaf5634: ldr             x1, [x1, #0xa0]
    // 0xaf5638: stur            x0, [fp, #-8]
    // 0xaf563c: r0 = AllocateClosure()
    //     0xaf563c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5640: ldur            x16, [fp, #-0x18]
    // 0xaf5644: stp             x16, x0, [SP]
    // 0xaf5648: ldur            x1, [fp, #-8]
    // 0xaf564c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaf564c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaf5650: ldr             x4, [x4, #0xaf0]
    // 0xaf5654: r0 = GestureDetector()
    //     0xaf5654: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaf5658: r1 = Instance_Color
    //     0xaf5658: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf565c: d0 = 0.030000
    //     0xaf565c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xaf5660: ldr             d0, [x17, #0x238]
    // 0xaf5664: r0 = withOpacity()
    //     0xaf5664: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf5668: stur            x0, [fp, #-0x18]
    // 0xaf566c: r0 = Radius()
    //     0xaf566c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf5670: d0 = 10.000000
    //     0xaf5670: fmov            d0, #10.00000000
    // 0xaf5674: stur            x0, [fp, #-0x28]
    // 0xaf5678: StoreField: r0->field_7 = d0
    //     0xaf5678: stur            d0, [x0, #7]
    // 0xaf567c: StoreField: r0->field_f = d0
    //     0xaf567c: stur            d0, [x0, #0xf]
    // 0xaf5680: r0 = BorderRadius()
    //     0xaf5680: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf5684: mov             x1, x0
    // 0xaf5688: ldur            x0, [fp, #-0x28]
    // 0xaf568c: stur            x1, [fp, #-0x30]
    // 0xaf5690: StoreField: r1->field_7 = r0
    //     0xaf5690: stur            w0, [x1, #7]
    // 0xaf5694: StoreField: r1->field_b = r0
    //     0xaf5694: stur            w0, [x1, #0xb]
    // 0xaf5698: StoreField: r1->field_f = r0
    //     0xaf5698: stur            w0, [x1, #0xf]
    // 0xaf569c: StoreField: r1->field_13 = r0
    //     0xaf569c: stur            w0, [x1, #0x13]
    // 0xaf56a0: r0 = BoxDecoration()
    //     0xaf56a0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf56a4: mov             x3, x0
    // 0xaf56a8: ldur            x0, [fp, #-0x18]
    // 0xaf56ac: stur            x3, [fp, #-0x28]
    // 0xaf56b0: StoreField: r3->field_7 = r0
    //     0xaf56b0: stur            w0, [x3, #7]
    // 0xaf56b4: ldur            x0, [fp, #-0x30]
    // 0xaf56b8: StoreField: r3->field_13 = r0
    //     0xaf56b8: stur            w0, [x3, #0x13]
    // 0xaf56bc: r0 = Instance_BoxShape
    //     0xaf56bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf56c0: ldr             x0, [x0, #0x80]
    // 0xaf56c4: StoreField: r3->field_23 = r0
    //     0xaf56c4: stur            w0, [x3, #0x23]
    // 0xaf56c8: r1 = Null
    //     0xaf56c8: mov             x1, NULL
    // 0xaf56cc: r2 = 4
    //     0xaf56cc: movz            x2, #0x4
    // 0xaf56d0: r0 = AllocateArray()
    //     0xaf56d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf56d4: mov             x2, x0
    // 0xaf56d8: r16 = "+"
    //     0xaf56d8: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xaf56dc: StoreField: r2->field_f = r16
    //     0xaf56dc: stur            w16, [x2, #0xf]
    // 0xaf56e0: ldur            x3, [fp, #-0x20]
    // 0xaf56e4: LoadField: r0 = r3->field_13
    //     0xaf56e4: ldur            w0, [x3, #0x13]
    // 0xaf56e8: DecompressPointer r0
    //     0xaf56e8: add             x0, x0, HEAP, lsl #32
    // 0xaf56ec: LoadField: r1 = r0->field_ab
    //     0xaf56ec: ldur            w1, [x0, #0xab]
    // 0xaf56f0: DecompressPointer r1
    //     0xaf56f0: add             x1, x1, HEAP, lsl #32
    // 0xaf56f4: cmp             w1, NULL
    // 0xaf56f8: b.ne            #0xaf5704
    // 0xaf56fc: r0 = Null
    //     0xaf56fc: mov             x0, NULL
    // 0xaf5700: b               #0xaf5708
    // 0xaf5704: LoadField: r0 = r1->field_b
    //     0xaf5704: ldur            w0, [x1, #0xb]
    // 0xaf5708: cmp             w0, NULL
    // 0xaf570c: b.ne            #0xaf5718
    // 0xaf5710: r0 = 0
    //     0xaf5710: movz            x0, #0
    // 0xaf5714: b               #0xaf5720
    // 0xaf5718: r1 = LoadInt32Instr(r0)
    //     0xaf5718: sbfx            x1, x0, #1, #0x1f
    // 0xaf571c: mov             x0, x1
    // 0xaf5720: ldur            x5, [fp, #-0x10]
    // 0xaf5724: ldur            x4, [fp, #-8]
    // 0xaf5728: sub             x6, x0, #2
    // 0xaf572c: r0 = BoxInt64Instr(r6)
    //     0xaf572c: sbfiz           x0, x6, #1, #0x1f
    //     0xaf5730: cmp             x6, x0, asr #1
    //     0xaf5734: b.eq            #0xaf5740
    //     0xaf5738: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf573c: stur            x6, [x0, #7]
    // 0xaf5740: StoreField: r2->field_13 = r0
    //     0xaf5740: stur            w0, [x2, #0x13]
    // 0xaf5744: str             x2, [SP]
    // 0xaf5748: r0 = _interpolate()
    //     0xaf5748: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaf574c: ldur            x2, [fp, #-0x20]
    // 0xaf5750: stur            x0, [fp, #-0x18]
    // 0xaf5754: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xaf5754: ldur            w1, [x2, #0x17]
    // 0xaf5758: DecompressPointer r1
    //     0xaf5758: add             x1, x1, HEAP, lsl #32
    // 0xaf575c: r0 = of()
    //     0xaf575c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf5760: LoadField: r1 = r0->field_87
    //     0xaf5760: ldur            w1, [x0, #0x87]
    // 0xaf5764: DecompressPointer r1
    //     0xaf5764: add             x1, x1, HEAP, lsl #32
    // 0xaf5768: LoadField: r0 = r1->field_2b
    //     0xaf5768: ldur            w0, [x1, #0x2b]
    // 0xaf576c: DecompressPointer r0
    //     0xaf576c: add             x0, x0, HEAP, lsl #32
    // 0xaf5770: r16 = 12.000000
    //     0xaf5770: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf5774: ldr             x16, [x16, #0x9e8]
    // 0xaf5778: r30 = Instance_Color
    //     0xaf5778: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf577c: stp             lr, x16, [SP]
    // 0xaf5780: mov             x1, x0
    // 0xaf5784: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf5784: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf5788: ldr             x4, [x4, #0xaa0]
    // 0xaf578c: r0 = copyWith()
    //     0xaf578c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf5790: stur            x0, [fp, #-0x30]
    // 0xaf5794: r0 = Text()
    //     0xaf5794: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf5798: mov             x2, x0
    // 0xaf579c: ldur            x0, [fp, #-0x18]
    // 0xaf57a0: stur            x2, [fp, #-0x38]
    // 0xaf57a4: StoreField: r2->field_b = r0
    //     0xaf57a4: stur            w0, [x2, #0xb]
    // 0xaf57a8: ldur            x0, [fp, #-0x30]
    // 0xaf57ac: StoreField: r2->field_13 = r0
    //     0xaf57ac: stur            w0, [x2, #0x13]
    // 0xaf57b0: ldur            x0, [fp, #-0x20]
    // 0xaf57b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf57b4: ldur            w1, [x0, #0x17]
    // 0xaf57b8: DecompressPointer r1
    //     0xaf57b8: add             x1, x1, HEAP, lsl #32
    // 0xaf57bc: r0 = of()
    //     0xaf57bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf57c0: LoadField: r1 = r0->field_87
    //     0xaf57c0: ldur            w1, [x0, #0x87]
    // 0xaf57c4: DecompressPointer r1
    //     0xaf57c4: add             x1, x1, HEAP, lsl #32
    // 0xaf57c8: LoadField: r0 = r1->field_2b
    //     0xaf57c8: ldur            w0, [x1, #0x2b]
    // 0xaf57cc: DecompressPointer r0
    //     0xaf57cc: add             x0, x0, HEAP, lsl #32
    // 0xaf57d0: r16 = 12.000000
    //     0xaf57d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf57d4: ldr             x16, [x16, #0x9e8]
    // 0xaf57d8: r30 = Instance_Color
    //     0xaf57d8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf57dc: stp             lr, x16, [SP]
    // 0xaf57e0: mov             x1, x0
    // 0xaf57e4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf57e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf57e8: ldr             x4, [x4, #0xaa0]
    // 0xaf57ec: r0 = copyWith()
    //     0xaf57ec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf57f0: stur            x0, [fp, #-0x18]
    // 0xaf57f4: r0 = Text()
    //     0xaf57f4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf57f8: mov             x3, x0
    // 0xaf57fc: r0 = "Photos"
    //     0xaf57fc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52260] "Photos"
    //     0xaf5800: ldr             x0, [x0, #0x260]
    // 0xaf5804: stur            x3, [fp, #-0x30]
    // 0xaf5808: StoreField: r3->field_b = r0
    //     0xaf5808: stur            w0, [x3, #0xb]
    // 0xaf580c: ldur            x0, [fp, #-0x18]
    // 0xaf5810: StoreField: r3->field_13 = r0
    //     0xaf5810: stur            w0, [x3, #0x13]
    // 0xaf5814: r1 = Null
    //     0xaf5814: mov             x1, NULL
    // 0xaf5818: r2 = 4
    //     0xaf5818: movz            x2, #0x4
    // 0xaf581c: r0 = AllocateArray()
    //     0xaf581c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf5820: mov             x2, x0
    // 0xaf5824: ldur            x0, [fp, #-0x38]
    // 0xaf5828: stur            x2, [fp, #-0x18]
    // 0xaf582c: StoreField: r2->field_f = r0
    //     0xaf582c: stur            w0, [x2, #0xf]
    // 0xaf5830: ldur            x0, [fp, #-0x30]
    // 0xaf5834: StoreField: r2->field_13 = r0
    //     0xaf5834: stur            w0, [x2, #0x13]
    // 0xaf5838: r1 = <Widget>
    //     0xaf5838: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf583c: r0 = AllocateGrowableArray()
    //     0xaf583c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf5840: mov             x1, x0
    // 0xaf5844: ldur            x0, [fp, #-0x18]
    // 0xaf5848: stur            x1, [fp, #-0x30]
    // 0xaf584c: StoreField: r1->field_f = r0
    //     0xaf584c: stur            w0, [x1, #0xf]
    // 0xaf5850: r0 = 4
    //     0xaf5850: movz            x0, #0x4
    // 0xaf5854: StoreField: r1->field_b = r0
    //     0xaf5854: stur            w0, [x1, #0xb]
    // 0xaf5858: r0 = Column()
    //     0xaf5858: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf585c: mov             x1, x0
    // 0xaf5860: r0 = Instance_Axis
    //     0xaf5860: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf5864: stur            x1, [fp, #-0x18]
    // 0xaf5868: StoreField: r1->field_f = r0
    //     0xaf5868: stur            w0, [x1, #0xf]
    // 0xaf586c: r0 = Instance_MainAxisAlignment
    //     0xaf586c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xaf5870: ldr             x0, [x0, #0xab0]
    // 0xaf5874: StoreField: r1->field_13 = r0
    //     0xaf5874: stur            w0, [x1, #0x13]
    // 0xaf5878: r0 = Instance_MainAxisSize
    //     0xaf5878: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf587c: ldr             x0, [x0, #0xa10]
    // 0xaf5880: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf5880: stur            w0, [x1, #0x17]
    // 0xaf5884: r2 = Instance_CrossAxisAlignment
    //     0xaf5884: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf5888: ldr             x2, [x2, #0xa18]
    // 0xaf588c: StoreField: r1->field_1b = r2
    //     0xaf588c: stur            w2, [x1, #0x1b]
    // 0xaf5890: r3 = Instance_VerticalDirection
    //     0xaf5890: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf5894: ldr             x3, [x3, #0xa20]
    // 0xaf5898: StoreField: r1->field_23 = r3
    //     0xaf5898: stur            w3, [x1, #0x23]
    // 0xaf589c: r4 = Instance_Clip
    //     0xaf589c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf58a0: ldr             x4, [x4, #0x38]
    // 0xaf58a4: StoreField: r1->field_2b = r4
    //     0xaf58a4: stur            w4, [x1, #0x2b]
    // 0xaf58a8: StoreField: r1->field_2f = rZR
    //     0xaf58a8: stur            xzr, [x1, #0x2f]
    // 0xaf58ac: ldur            x5, [fp, #-0x30]
    // 0xaf58b0: StoreField: r1->field_b = r5
    //     0xaf58b0: stur            w5, [x1, #0xb]
    // 0xaf58b4: r0 = Container()
    //     0xaf58b4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf58b8: stur            x0, [fp, #-0x30]
    // 0xaf58bc: r16 = 64.000000
    //     0xaf58bc: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xaf58c0: ldr             x16, [x16, #0x838]
    // 0xaf58c4: r30 = 64.000000
    //     0xaf58c4: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xaf58c8: ldr             lr, [lr, #0x838]
    // 0xaf58cc: stp             lr, x16, [SP, #0x10]
    // 0xaf58d0: ldur            x16, [fp, #-0x28]
    // 0xaf58d4: ldur            lr, [fp, #-0x18]
    // 0xaf58d8: stp             lr, x16, [SP]
    // 0xaf58dc: mov             x1, x0
    // 0xaf58e0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xaf58e0: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xaf58e4: ldr             x4, [x4, #0x870]
    // 0xaf58e8: r0 = Container()
    //     0xaf58e8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf58ec: r0 = GestureDetector()
    //     0xaf58ec: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaf58f0: ldur            x2, [fp, #-0x20]
    // 0xaf58f4: r1 = Function '<anonymous closure>':.
    //     0xaf58f4: add             x1, PP, #0x58, lsl #12  ; [pp+0x580a8] AnonymousClosure: (0xaf5ae8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xaf5540)
    //     0xaf58f8: ldr             x1, [x1, #0xa8]
    // 0xaf58fc: stur            x0, [fp, #-0x18]
    // 0xaf5900: r0 = AllocateClosure()
    //     0xaf5900: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5904: ldur            x16, [fp, #-0x30]
    // 0xaf5908: stp             x16, x0, [SP]
    // 0xaf590c: ldur            x1, [fp, #-0x18]
    // 0xaf5910: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaf5910: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaf5914: ldr             x4, [x4, #0xaf0]
    // 0xaf5918: r0 = GestureDetector()
    //     0xaf5918: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaf591c: r1 = Null
    //     0xaf591c: mov             x1, NULL
    // 0xaf5920: r2 = 6
    //     0xaf5920: movz            x2, #0x6
    // 0xaf5924: r0 = AllocateArray()
    //     0xaf5924: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf5928: mov             x2, x0
    // 0xaf592c: ldur            x0, [fp, #-0x10]
    // 0xaf5930: stur            x2, [fp, #-0x20]
    // 0xaf5934: StoreField: r2->field_f = r0
    //     0xaf5934: stur            w0, [x2, #0xf]
    // 0xaf5938: ldur            x0, [fp, #-8]
    // 0xaf593c: StoreField: r2->field_13 = r0
    //     0xaf593c: stur            w0, [x2, #0x13]
    // 0xaf5940: ldur            x0, [fp, #-0x18]
    // 0xaf5944: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf5944: stur            w0, [x2, #0x17]
    // 0xaf5948: r1 = <Widget>
    //     0xaf5948: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf594c: r0 = AllocateGrowableArray()
    //     0xaf594c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf5950: mov             x1, x0
    // 0xaf5954: ldur            x0, [fp, #-0x20]
    // 0xaf5958: stur            x1, [fp, #-8]
    // 0xaf595c: StoreField: r1->field_f = r0
    //     0xaf595c: stur            w0, [x1, #0xf]
    // 0xaf5960: r0 = 6
    //     0xaf5960: movz            x0, #0x6
    // 0xaf5964: StoreField: r1->field_b = r0
    //     0xaf5964: stur            w0, [x1, #0xb]
    // 0xaf5968: r0 = Row()
    //     0xaf5968: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf596c: r1 = Instance_Axis
    //     0xaf596c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf5970: StoreField: r0->field_f = r1
    //     0xaf5970: stur            w1, [x0, #0xf]
    // 0xaf5974: r1 = Instance_MainAxisAlignment
    //     0xaf5974: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf5978: ldr             x1, [x1, #0xa08]
    // 0xaf597c: StoreField: r0->field_13 = r1
    //     0xaf597c: stur            w1, [x0, #0x13]
    // 0xaf5980: r1 = Instance_MainAxisSize
    //     0xaf5980: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf5984: ldr             x1, [x1, #0xa10]
    // 0xaf5988: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf5988: stur            w1, [x0, #0x17]
    // 0xaf598c: r1 = Instance_CrossAxisAlignment
    //     0xaf598c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf5990: ldr             x1, [x1, #0xa18]
    // 0xaf5994: StoreField: r0->field_1b = r1
    //     0xaf5994: stur            w1, [x0, #0x1b]
    // 0xaf5998: r1 = Instance_VerticalDirection
    //     0xaf5998: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf599c: ldr             x1, [x1, #0xa20]
    // 0xaf59a0: StoreField: r0->field_23 = r1
    //     0xaf59a0: stur            w1, [x0, #0x23]
    // 0xaf59a4: r1 = Instance_Clip
    //     0xaf59a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf59a8: ldr             x1, [x1, #0x38]
    // 0xaf59ac: StoreField: r0->field_2b = r1
    //     0xaf59ac: stur            w1, [x0, #0x2b]
    // 0xaf59b0: StoreField: r0->field_2f = rZR
    //     0xaf59b0: stur            xzr, [x0, #0x2f]
    // 0xaf59b4: ldur            x1, [fp, #-8]
    // 0xaf59b8: StoreField: r0->field_b = r1
    //     0xaf59b8: stur            w1, [x0, #0xb]
    // 0xaf59bc: LeaveFrame
    //     0xaf59bc: mov             SP, fp
    //     0xaf59c0: ldp             fp, lr, [SP], #0x10
    // 0xaf59c4: ret
    //     0xaf59c4: ret             
    // 0xaf59c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf59c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf59cc: b               #0xaf5564
  }
  _ _buildImageThumbnail(/* No info */) {
    // ** addr: 0xaf59d0, size: 0x118
    // 0xaf59d0: EnterFrame
    //     0xaf59d0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf59d4: mov             fp, SP
    // 0xaf59d8: AllocStack(0x40)
    //     0xaf59d8: sub             SP, SP, #0x40
    // 0xaf59dc: SetupParameters(dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2 */)
    //     0xaf59dc: mov             x0, x2
    //     0xaf59e0: mov             x2, x3
    // 0xaf59e4: CheckStackOverflow
    //     0xaf59e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf59e8: cmp             SP, x16
    //     0xaf59ec: b.ls            #0xaf5adc
    // 0xaf59f0: LoadField: r3 = r0->field_ab
    //     0xaf59f0: ldur            w3, [x0, #0xab]
    // 0xaf59f4: DecompressPointer r3
    //     0xaf59f4: add             x3, x3, HEAP, lsl #32
    // 0xaf59f8: cmp             w3, NULL
    // 0xaf59fc: b.ne            #0xaf5a08
    // 0xaf5a00: r0 = Null
    //     0xaf5a00: mov             x0, NULL
    // 0xaf5a04: b               #0xaf5a58
    // 0xaf5a08: LoadField: r0 = r3->field_b
    //     0xaf5a08: ldur            w0, [x3, #0xb]
    // 0xaf5a0c: r1 = LoadInt32Instr(r0)
    //     0xaf5a0c: sbfx            x1, x0, #1, #0x1f
    // 0xaf5a10: mov             x0, x1
    // 0xaf5a14: mov             x1, x2
    // 0xaf5a18: cmp             x1, x0
    // 0xaf5a1c: b.hs            #0xaf5ae4
    // 0xaf5a20: LoadField: r0 = r3->field_f
    //     0xaf5a20: ldur            w0, [x3, #0xf]
    // 0xaf5a24: DecompressPointer r0
    //     0xaf5a24: add             x0, x0, HEAP, lsl #32
    // 0xaf5a28: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xaf5a28: add             x16, x0, x2, lsl #2
    //     0xaf5a2c: ldur            w1, [x16, #0xf]
    // 0xaf5a30: DecompressPointer r1
    //     0xaf5a30: add             x1, x1, HEAP, lsl #32
    // 0xaf5a34: LoadField: r0 = r1->field_7
    //     0xaf5a34: ldur            w0, [x1, #7]
    // 0xaf5a38: DecompressPointer r0
    //     0xaf5a38: add             x0, x0, HEAP, lsl #32
    // 0xaf5a3c: cmp             w0, NULL
    // 0xaf5a40: b.ne            #0xaf5a4c
    // 0xaf5a44: r0 = Null
    //     0xaf5a44: mov             x0, NULL
    // 0xaf5a48: b               #0xaf5a58
    // 0xaf5a4c: LoadField: r1 = r0->field_b
    //     0xaf5a4c: ldur            w1, [x0, #0xb]
    // 0xaf5a50: DecompressPointer r1
    //     0xaf5a50: add             x1, x1, HEAP, lsl #32
    // 0xaf5a54: mov             x0, x1
    // 0xaf5a58: cmp             w0, NULL
    // 0xaf5a5c: b.ne            #0xaf5a64
    // 0xaf5a60: r0 = ""
    //     0xaf5a60: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf5a64: stur            x0, [fp, #-8]
    // 0xaf5a68: r1 = Function '<anonymous closure>':.
    //     0xaf5a68: add             x1, PP, #0x58, lsl #12  ; [pp+0x580b8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf5a6c: ldr             x1, [x1, #0xb8]
    // 0xaf5a70: r2 = Null
    //     0xaf5a70: mov             x2, NULL
    // 0xaf5a74: r0 = AllocateClosure()
    //     0xaf5a74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5a78: r1 = Function '<anonymous closure>':.
    //     0xaf5a78: add             x1, PP, #0x58, lsl #12  ; [pp+0x580c0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf5a7c: ldr             x1, [x1, #0xc0]
    // 0xaf5a80: r2 = Null
    //     0xaf5a80: mov             x2, NULL
    // 0xaf5a84: stur            x0, [fp, #-0x10]
    // 0xaf5a88: r0 = AllocateClosure()
    //     0xaf5a88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5a8c: stur            x0, [fp, #-0x18]
    // 0xaf5a90: r0 = CachedNetworkImage()
    //     0xaf5a90: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaf5a94: stur            x0, [fp, #-0x20]
    // 0xaf5a98: r16 = 64.000000
    //     0xaf5a98: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xaf5a9c: ldr             x16, [x16, #0x838]
    // 0xaf5aa0: r30 = 64.000000
    //     0xaf5aa0: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xaf5aa4: ldr             lr, [lr, #0x838]
    // 0xaf5aa8: stp             lr, x16, [SP, #0x10]
    // 0xaf5aac: ldur            x16, [fp, #-0x10]
    // 0xaf5ab0: ldur            lr, [fp, #-0x18]
    // 0xaf5ab4: stp             lr, x16, [SP]
    // 0xaf5ab8: mov             x1, x0
    // 0xaf5abc: ldur            x2, [fp, #-8]
    // 0xaf5ac0: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xaf5ac0: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xaf5ac4: ldr             x4, [x4, #0x388]
    // 0xaf5ac8: r0 = CachedNetworkImage()
    //     0xaf5ac8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaf5acc: ldur            x0, [fp, #-0x20]
    // 0xaf5ad0: LeaveFrame
    //     0xaf5ad0: mov             SP, fp
    //     0xaf5ad4: ldp             fp, lr, [SP], #0x10
    // 0xaf5ad8: ret
    //     0xaf5ad8: ret             
    // 0xaf5adc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5adc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5ae0: b               #0xaf59f0
    // 0xaf5ae4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf5ae4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf5ae8, size: 0x5c
    // 0xaf5ae8: EnterFrame
    //     0xaf5ae8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5aec: mov             fp, SP
    // 0xaf5af0: ldr             x0, [fp, #0x10]
    // 0xaf5af4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf5af4: ldur            w1, [x0, #0x17]
    // 0xaf5af8: DecompressPointer r1
    //     0xaf5af8: add             x1, x1, HEAP, lsl #32
    // 0xaf5afc: CheckStackOverflow
    //     0xaf5afc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5b00: cmp             SP, x16
    //     0xaf5b04: b.ls            #0xaf5b3c
    // 0xaf5b08: LoadField: r0 = r1->field_f
    //     0xaf5b08: ldur            w0, [x1, #0xf]
    // 0xaf5b0c: DecompressPointer r0
    //     0xaf5b0c: add             x0, x0, HEAP, lsl #32
    // 0xaf5b10: LoadField: r2 = r1->field_13
    //     0xaf5b10: ldur            w2, [x1, #0x13]
    // 0xaf5b14: DecompressPointer r2
    //     0xaf5b14: add             x2, x2, HEAP, lsl #32
    // 0xaf5b18: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xaf5b18: ldur            w5, [x1, #0x17]
    // 0xaf5b1c: DecompressPointer r5
    //     0xaf5b1c: add             x5, x5, HEAP, lsl #32
    // 0xaf5b20: mov             x1, x0
    // 0xaf5b24: r3 = 2
    //     0xaf5b24: movz            x3, #0x2
    // 0xaf5b28: r0 = _openImageViewer()
    //     0xaf5b28: bl              #0xaf5b44  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xaf5b2c: r0 = Null
    //     0xaf5b2c: mov             x0, NULL
    // 0xaf5b30: LeaveFrame
    //     0xaf5b30: mov             SP, fp
    //     0xaf5b34: ldp             fp, lr, [SP], #0x10
    // 0xaf5b38: ret
    //     0xaf5b38: ret             
    // 0xaf5b3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5b3c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5b40: b               #0xaf5b08
  }
  _ _openImageViewer(/* No info */) {
    // ** addr: 0xaf5b44, size: 0xd0
    // 0xaf5b44: EnterFrame
    //     0xaf5b44: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5b48: mov             fp, SP
    // 0xaf5b4c: AllocStack(0x38)
    //     0xaf5b4c: sub             SP, SP, #0x38
    // 0xaf5b50: SetupParameters(_TestimonialCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1, fp-0x18 */)
    //     0xaf5b50: mov             x0, x1
    //     0xaf5b54: mov             x1, x5
    //     0xaf5b58: stur            x2, [fp, #-8]
    //     0xaf5b5c: stur            x3, [fp, #-0x10]
    //     0xaf5b60: stur            x5, [fp, #-0x18]
    // 0xaf5b64: CheckStackOverflow
    //     0xaf5b64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5b68: cmp             SP, x16
    //     0xaf5b6c: b.ls            #0xaf5c0c
    // 0xaf5b70: r1 = 2
    //     0xaf5b70: movz            x1, #0x2
    // 0xaf5b74: r0 = AllocateContext()
    //     0xaf5b74: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf5b78: mov             x2, x0
    // 0xaf5b7c: ldur            x0, [fp, #-8]
    // 0xaf5b80: stur            x2, [fp, #-0x20]
    // 0xaf5b84: StoreField: r2->field_f = r0
    //     0xaf5b84: stur            w0, [x2, #0xf]
    // 0xaf5b88: ldur            x3, [fp, #-0x10]
    // 0xaf5b8c: r0 = BoxInt64Instr(r3)
    //     0xaf5b8c: sbfiz           x0, x3, #1, #0x1f
    //     0xaf5b90: cmp             x3, x0, asr #1
    //     0xaf5b94: b.eq            #0xaf5ba0
    //     0xaf5b98: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf5b9c: stur            x3, [x0, #7]
    // 0xaf5ba0: StoreField: r2->field_13 = r0
    //     0xaf5ba0: stur            w0, [x2, #0x13]
    // 0xaf5ba4: ldur            x1, [fp, #-0x18]
    // 0xaf5ba8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf5ba8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf5bac: r0 = of()
    //     0xaf5bac: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xaf5bb0: ldur            x2, [fp, #-0x20]
    // 0xaf5bb4: r1 = Function '<anonymous closure>':.
    //     0xaf5bb4: add             x1, PP, #0x58, lsl #12  ; [pp+0x580b0] AnonymousClosure: (0xaf5c14), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer (0xaf5b44)
    //     0xaf5bb8: ldr             x1, [x1, #0xb0]
    // 0xaf5bbc: stur            x0, [fp, #-8]
    // 0xaf5bc0: r0 = AllocateClosure()
    //     0xaf5bc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5bc4: r1 = Null
    //     0xaf5bc4: mov             x1, NULL
    // 0xaf5bc8: stur            x0, [fp, #-0x18]
    // 0xaf5bcc: r0 = MaterialPageRoute()
    //     0xaf5bcc: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xaf5bd0: mov             x1, x0
    // 0xaf5bd4: ldur            x2, [fp, #-0x18]
    // 0xaf5bd8: stur            x0, [fp, #-0x18]
    // 0xaf5bdc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaf5bdc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaf5be0: r0 = MaterialPageRoute()
    //     0xaf5be0: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xaf5be4: ldur            x16, [fp, #-8]
    // 0xaf5be8: stp             x16, NULL, [SP, #8]
    // 0xaf5bec: ldur            x16, [fp, #-0x18]
    // 0xaf5bf0: str             x16, [SP]
    // 0xaf5bf4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf5bf4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf5bf8: r0 = push()
    //     0xaf5bf8: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xaf5bfc: r0 = Null
    //     0xaf5bfc: mov             x0, NULL
    // 0xaf5c00: LeaveFrame
    //     0xaf5c00: mov             SP, fp
    //     0xaf5c04: ldp             fp, lr, [SP], #0x10
    // 0xaf5c08: ret
    //     0xaf5c08: ret             
    // 0xaf5c0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5c0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5c10: b               #0xaf5b70
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xaf5c14, size: 0x64
    // 0xaf5c14: EnterFrame
    //     0xaf5c14: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5c18: mov             fp, SP
    // 0xaf5c1c: AllocStack(0x10)
    //     0xaf5c1c: sub             SP, SP, #0x10
    // 0xaf5c20: SetupParameters()
    //     0xaf5c20: ldr             x0, [fp, #0x18]
    //     0xaf5c24: ldur            w1, [x0, #0x17]
    //     0xaf5c28: add             x1, x1, HEAP, lsl #32
    // 0xaf5c2c: LoadField: r0 = r1->field_13
    //     0xaf5c2c: ldur            w0, [x1, #0x13]
    // 0xaf5c30: DecompressPointer r0
    //     0xaf5c30: add             x0, x0, HEAP, lsl #32
    // 0xaf5c34: stur            x0, [fp, #-0x10]
    // 0xaf5c38: LoadField: r2 = r1->field_f
    //     0xaf5c38: ldur            w2, [x1, #0xf]
    // 0xaf5c3c: DecompressPointer r2
    //     0xaf5c3c: add             x2, x2, HEAP, lsl #32
    // 0xaf5c40: LoadField: r1 = r2->field_ab
    //     0xaf5c40: ldur            w1, [x2, #0xab]
    // 0xaf5c44: DecompressPointer r1
    //     0xaf5c44: add             x1, x1, HEAP, lsl #32
    // 0xaf5c48: stur            x1, [fp, #-8]
    // 0xaf5c4c: r0 = TestimonialMoreImagesWidget()
    //     0xaf5c4c: bl              #0xaf5c78  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xaf5c50: ldur            x1, [fp, #-8]
    // 0xaf5c54: StoreField: r0->field_b = r1
    //     0xaf5c54: stur            w1, [x0, #0xb]
    // 0xaf5c58: ldur            x1, [fp, #-0x10]
    // 0xaf5c5c: r2 = LoadInt32Instr(r1)
    //     0xaf5c5c: sbfx            x2, x1, #1, #0x1f
    //     0xaf5c60: tbz             w1, #0, #0xaf5c68
    //     0xaf5c64: ldur            x2, [x1, #7]
    // 0xaf5c68: StoreField: r0->field_f = r2
    //     0xaf5c68: stur            x2, [x0, #0xf]
    // 0xaf5c6c: LeaveFrame
    //     0xaf5c6c: mov             SP, fp
    //     0xaf5c70: ldp             fp, lr, [SP], #0x10
    // 0xaf5c74: ret
    //     0xaf5c74: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf5ca8, size: 0x5c
    // 0xaf5ca8: EnterFrame
    //     0xaf5ca8: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5cac: mov             fp, SP
    // 0xaf5cb0: ldr             x0, [fp, #0x10]
    // 0xaf5cb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf5cb4: ldur            w1, [x0, #0x17]
    // 0xaf5cb8: DecompressPointer r1
    //     0xaf5cb8: add             x1, x1, HEAP, lsl #32
    // 0xaf5cbc: CheckStackOverflow
    //     0xaf5cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5cc0: cmp             SP, x16
    //     0xaf5cc4: b.ls            #0xaf5cfc
    // 0xaf5cc8: LoadField: r0 = r1->field_f
    //     0xaf5cc8: ldur            w0, [x1, #0xf]
    // 0xaf5ccc: DecompressPointer r0
    //     0xaf5ccc: add             x0, x0, HEAP, lsl #32
    // 0xaf5cd0: LoadField: r2 = r1->field_13
    //     0xaf5cd0: ldur            w2, [x1, #0x13]
    // 0xaf5cd4: DecompressPointer r2
    //     0xaf5cd4: add             x2, x2, HEAP, lsl #32
    // 0xaf5cd8: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xaf5cd8: ldur            w5, [x1, #0x17]
    // 0xaf5cdc: DecompressPointer r5
    //     0xaf5cdc: add             x5, x5, HEAP, lsl #32
    // 0xaf5ce0: mov             x1, x0
    // 0xaf5ce4: r3 = 1
    //     0xaf5ce4: movz            x3, #0x1
    // 0xaf5ce8: r0 = _openImageViewer()
    //     0xaf5ce8: bl              #0xaf5b44  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xaf5cec: r0 = Null
    //     0xaf5cec: mov             x0, NULL
    // 0xaf5cf0: LeaveFrame
    //     0xaf5cf0: mov             SP, fp
    //     0xaf5cf4: ldp             fp, lr, [SP], #0x10
    // 0xaf5cf8: ret
    //     0xaf5cf8: ret             
    // 0xaf5cfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5cfc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5d00: b               #0xaf5cc8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf5d04, size: 0x5c
    // 0xaf5d04: EnterFrame
    //     0xaf5d04: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5d08: mov             fp, SP
    // 0xaf5d0c: ldr             x0, [fp, #0x10]
    // 0xaf5d10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf5d10: ldur            w1, [x0, #0x17]
    // 0xaf5d14: DecompressPointer r1
    //     0xaf5d14: add             x1, x1, HEAP, lsl #32
    // 0xaf5d18: CheckStackOverflow
    //     0xaf5d18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5d1c: cmp             SP, x16
    //     0xaf5d20: b.ls            #0xaf5d58
    // 0xaf5d24: LoadField: r0 = r1->field_f
    //     0xaf5d24: ldur            w0, [x1, #0xf]
    // 0xaf5d28: DecompressPointer r0
    //     0xaf5d28: add             x0, x0, HEAP, lsl #32
    // 0xaf5d2c: LoadField: r2 = r1->field_13
    //     0xaf5d2c: ldur            w2, [x1, #0x13]
    // 0xaf5d30: DecompressPointer r2
    //     0xaf5d30: add             x2, x2, HEAP, lsl #32
    // 0xaf5d34: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xaf5d34: ldur            w5, [x1, #0x17]
    // 0xaf5d38: DecompressPointer r5
    //     0xaf5d38: add             x5, x5, HEAP, lsl #32
    // 0xaf5d3c: mov             x1, x0
    // 0xaf5d40: r3 = 0
    //     0xaf5d40: movz            x3, #0
    // 0xaf5d44: r0 = _openImageViewer()
    //     0xaf5d44: bl              #0xaf5b44  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xaf5d48: r0 = Null
    //     0xaf5d48: mov             x0, NULL
    // 0xaf5d4c: LeaveFrame
    //     0xaf5d4c: mov             SP, fp
    //     0xaf5d50: ldp             fp, lr, [SP], #0x10
    // 0xaf5d54: ret
    //     0xaf5d54: ret             
    // 0xaf5d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5d58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5d5c: b               #0xaf5d24
  }
  _ _buildImagesRow(/* No info */) {
    // ** addr: 0xaf5d60, size: 0xdc
    // 0xaf5d60: EnterFrame
    //     0xaf5d60: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5d64: mov             fp, SP
    // 0xaf5d68: AllocStack(0x30)
    //     0xaf5d68: sub             SP, SP, #0x30
    // 0xaf5d6c: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xaf5d6c: stur            x1, [fp, #-8]
    //     0xaf5d70: stur            x2, [fp, #-0x10]
    // 0xaf5d74: CheckStackOverflow
    //     0xaf5d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5d78: cmp             SP, x16
    //     0xaf5d7c: b.ls            #0xaf5e34
    // 0xaf5d80: r1 = 2
    //     0xaf5d80: movz            x1, #0x2
    // 0xaf5d84: r0 = AllocateContext()
    //     0xaf5d84: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf5d88: mov             x1, x0
    // 0xaf5d8c: ldur            x0, [fp, #-8]
    // 0xaf5d90: StoreField: r1->field_f = r0
    //     0xaf5d90: stur            w0, [x1, #0xf]
    // 0xaf5d94: ldur            x0, [fp, #-0x10]
    // 0xaf5d98: StoreField: r1->field_13 = r0
    //     0xaf5d98: stur            w0, [x1, #0x13]
    // 0xaf5d9c: LoadField: r2 = r0->field_ab
    //     0xaf5d9c: ldur            w2, [x0, #0xab]
    // 0xaf5da0: DecompressPointer r2
    //     0xaf5da0: add             x2, x2, HEAP, lsl #32
    // 0xaf5da4: cmp             w2, NULL
    // 0xaf5da8: b.ne            #0xaf5db4
    // 0xaf5dac: r0 = Null
    //     0xaf5dac: mov             x0, NULL
    // 0xaf5db0: b               #0xaf5db8
    // 0xaf5db4: LoadField: r0 = r2->field_b
    //     0xaf5db4: ldur            w0, [x2, #0xb]
    // 0xaf5db8: cmp             w0, NULL
    // 0xaf5dbc: b.ne            #0xaf5dc8
    // 0xaf5dc0: r0 = 0
    //     0xaf5dc0: movz            x0, #0
    // 0xaf5dc4: b               #0xaf5dd0
    // 0xaf5dc8: r2 = LoadInt32Instr(r0)
    //     0xaf5dc8: sbfx            x2, x0, #1, #0x1f
    // 0xaf5dcc: mov             x0, x2
    // 0xaf5dd0: lsl             x3, x0, #1
    // 0xaf5dd4: mov             x2, x1
    // 0xaf5dd8: stur            x3, [fp, #-8]
    // 0xaf5ddc: r1 = Function '<anonymous closure>':.
    //     0xaf5ddc: add             x1, PP, #0x58, lsl #12  ; [pp+0x580c8] AnonymousClosure: (0xaf5e3c), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xaf5d60)
    //     0xaf5de0: ldr             x1, [x1, #0xc8]
    // 0xaf5de4: r0 = AllocateClosure()
    //     0xaf5de4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5de8: stur            x0, [fp, #-0x10]
    // 0xaf5dec: r0 = ListView()
    //     0xaf5dec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xaf5df0: stur            x0, [fp, #-0x18]
    // 0xaf5df4: r16 = true
    //     0xaf5df4: add             x16, NULL, #0x20  ; true
    // 0xaf5df8: r30 = Instance_Axis
    //     0xaf5df8: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf5dfc: stp             lr, x16, [SP, #8]
    // 0xaf5e00: r16 = Instance_NeverScrollableScrollPhysics
    //     0xaf5e00: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xaf5e04: ldr             x16, [x16, #0x1c8]
    // 0xaf5e08: str             x16, [SP]
    // 0xaf5e0c: mov             x1, x0
    // 0xaf5e10: ldur            x2, [fp, #-0x10]
    // 0xaf5e14: ldur            x3, [fp, #-8]
    // 0xaf5e18: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xaf5e18: add             x4, PP, #0x52, lsl #12  ; [pp+0x52200] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xaf5e1c: ldr             x4, [x4, #0x200]
    // 0xaf5e20: r0 = ListView.builder()
    //     0xaf5e20: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xaf5e24: ldur            x0, [fp, #-0x18]
    // 0xaf5e28: LeaveFrame
    //     0xaf5e28: mov             SP, fp
    //     0xaf5e2c: ldp             fp, lr, [SP], #0x10
    // 0xaf5e30: ret
    //     0xaf5e30: ret             
    // 0xaf5e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5e34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5e38: b               #0xaf5d80
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xaf5e3c, size: 0x1a0
    // 0xaf5e3c: EnterFrame
    //     0xaf5e3c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5e40: mov             fp, SP
    // 0xaf5e44: AllocStack(0x48)
    //     0xaf5e44: sub             SP, SP, #0x48
    // 0xaf5e48: SetupParameters()
    //     0xaf5e48: ldr             x0, [fp, #0x20]
    //     0xaf5e4c: ldur            w1, [x0, #0x17]
    //     0xaf5e50: add             x1, x1, HEAP, lsl #32
    //     0xaf5e54: stur            x1, [fp, #-8]
    // 0xaf5e58: CheckStackOverflow
    //     0xaf5e58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5e5c: cmp             SP, x16
    //     0xaf5e60: b.ls            #0xaf5fd0
    // 0xaf5e64: r1 = 2
    //     0xaf5e64: movz            x1, #0x2
    // 0xaf5e68: r0 = AllocateContext()
    //     0xaf5e68: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf5e6c: mov             x3, x0
    // 0xaf5e70: ldur            x0, [fp, #-8]
    // 0xaf5e74: stur            x3, [fp, #-0x10]
    // 0xaf5e78: StoreField: r3->field_b = r0
    //     0xaf5e78: stur            w0, [x3, #0xb]
    // 0xaf5e7c: ldr             x1, [fp, #0x18]
    // 0xaf5e80: StoreField: r3->field_f = r1
    //     0xaf5e80: stur            w1, [x3, #0xf]
    // 0xaf5e84: ldr             x1, [fp, #0x10]
    // 0xaf5e88: StoreField: r3->field_13 = r1
    //     0xaf5e88: stur            w1, [x3, #0x13]
    // 0xaf5e8c: LoadField: r2 = r0->field_13
    //     0xaf5e8c: ldur            w2, [x0, #0x13]
    // 0xaf5e90: DecompressPointer r2
    //     0xaf5e90: add             x2, x2, HEAP, lsl #32
    // 0xaf5e94: LoadField: r4 = r2->field_ab
    //     0xaf5e94: ldur            w4, [x2, #0xab]
    // 0xaf5e98: DecompressPointer r4
    //     0xaf5e98: add             x4, x4, HEAP, lsl #32
    // 0xaf5e9c: cmp             w4, NULL
    // 0xaf5ea0: b.ne            #0xaf5eac
    // 0xaf5ea4: r0 = Null
    //     0xaf5ea4: mov             x0, NULL
    // 0xaf5ea8: b               #0xaf5f08
    // 0xaf5eac: LoadField: r0 = r4->field_b
    //     0xaf5eac: ldur            w0, [x4, #0xb]
    // 0xaf5eb0: r2 = LoadInt32Instr(r1)
    //     0xaf5eb0: sbfx            x2, x1, #1, #0x1f
    //     0xaf5eb4: tbz             w1, #0, #0xaf5ebc
    //     0xaf5eb8: ldur            x2, [x1, #7]
    // 0xaf5ebc: r1 = LoadInt32Instr(r0)
    //     0xaf5ebc: sbfx            x1, x0, #1, #0x1f
    // 0xaf5ec0: mov             x0, x1
    // 0xaf5ec4: mov             x1, x2
    // 0xaf5ec8: cmp             x1, x0
    // 0xaf5ecc: b.hs            #0xaf5fd8
    // 0xaf5ed0: LoadField: r0 = r4->field_f
    //     0xaf5ed0: ldur            w0, [x4, #0xf]
    // 0xaf5ed4: DecompressPointer r0
    //     0xaf5ed4: add             x0, x0, HEAP, lsl #32
    // 0xaf5ed8: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xaf5ed8: add             x16, x0, x2, lsl #2
    //     0xaf5edc: ldur            w1, [x16, #0xf]
    // 0xaf5ee0: DecompressPointer r1
    //     0xaf5ee0: add             x1, x1, HEAP, lsl #32
    // 0xaf5ee4: LoadField: r0 = r1->field_7
    //     0xaf5ee4: ldur            w0, [x1, #7]
    // 0xaf5ee8: DecompressPointer r0
    //     0xaf5ee8: add             x0, x0, HEAP, lsl #32
    // 0xaf5eec: cmp             w0, NULL
    // 0xaf5ef0: b.ne            #0xaf5efc
    // 0xaf5ef4: r0 = Null
    //     0xaf5ef4: mov             x0, NULL
    // 0xaf5ef8: b               #0xaf5f08
    // 0xaf5efc: LoadField: r1 = r0->field_b
    //     0xaf5efc: ldur            w1, [x0, #0xb]
    // 0xaf5f00: DecompressPointer r1
    //     0xaf5f00: add             x1, x1, HEAP, lsl #32
    // 0xaf5f04: mov             x0, x1
    // 0xaf5f08: cmp             w0, NULL
    // 0xaf5f0c: b.ne            #0xaf5f14
    // 0xaf5f10: r0 = ""
    //     0xaf5f10: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf5f14: stur            x0, [fp, #-8]
    // 0xaf5f18: r1 = Function '<anonymous closure>':.
    //     0xaf5f18: add             x1, PP, #0x58, lsl #12  ; [pp+0x580d0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf5f1c: ldr             x1, [x1, #0xd0]
    // 0xaf5f20: r2 = Null
    //     0xaf5f20: mov             x2, NULL
    // 0xaf5f24: r0 = AllocateClosure()
    //     0xaf5f24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5f28: r1 = Function '<anonymous closure>':.
    //     0xaf5f28: add             x1, PP, #0x58, lsl #12  ; [pp+0x580d8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf5f2c: ldr             x1, [x1, #0xd8]
    // 0xaf5f30: r2 = Null
    //     0xaf5f30: mov             x2, NULL
    // 0xaf5f34: stur            x0, [fp, #-0x18]
    // 0xaf5f38: r0 = AllocateClosure()
    //     0xaf5f38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5f3c: stur            x0, [fp, #-0x20]
    // 0xaf5f40: r0 = CachedNetworkImage()
    //     0xaf5f40: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaf5f44: stur            x0, [fp, #-0x28]
    // 0xaf5f48: r16 = 64.000000
    //     0xaf5f48: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xaf5f4c: ldr             x16, [x16, #0x838]
    // 0xaf5f50: r30 = 64.000000
    //     0xaf5f50: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xaf5f54: ldr             lr, [lr, #0x838]
    // 0xaf5f58: stp             lr, x16, [SP, #0x10]
    // 0xaf5f5c: ldur            x16, [fp, #-0x18]
    // 0xaf5f60: ldur            lr, [fp, #-0x20]
    // 0xaf5f64: stp             lr, x16, [SP]
    // 0xaf5f68: mov             x1, x0
    // 0xaf5f6c: ldur            x2, [fp, #-8]
    // 0xaf5f70: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xaf5f70: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xaf5f74: ldr             x4, [x4, #0x388]
    // 0xaf5f78: r0 = CachedNetworkImage()
    //     0xaf5f78: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaf5f7c: r0 = GestureDetector()
    //     0xaf5f7c: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaf5f80: ldur            x2, [fp, #-0x10]
    // 0xaf5f84: r1 = Function '<anonymous closure>':.
    //     0xaf5f84: add             x1, PP, #0x58, lsl #12  ; [pp+0x580e0] AnonymousClosure: (0xaf5fdc), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xaf5d60)
    //     0xaf5f88: ldr             x1, [x1, #0xe0]
    // 0xaf5f8c: stur            x0, [fp, #-8]
    // 0xaf5f90: r0 = AllocateClosure()
    //     0xaf5f90: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf5f94: ldur            x16, [fp, #-0x28]
    // 0xaf5f98: stp             x16, x0, [SP]
    // 0xaf5f9c: ldur            x1, [fp, #-8]
    // 0xaf5fa0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xaf5fa0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xaf5fa4: ldr             x4, [x4, #0xaf0]
    // 0xaf5fa8: r0 = GestureDetector()
    //     0xaf5fa8: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaf5fac: r0 = Padding()
    //     0xaf5fac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf5fb0: r1 = Instance_EdgeInsets
    //     0xaf5fb0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xaf5fb4: ldr             x1, [x1, #0x550]
    // 0xaf5fb8: StoreField: r0->field_f = r1
    //     0xaf5fb8: stur            w1, [x0, #0xf]
    // 0xaf5fbc: ldur            x1, [fp, #-8]
    // 0xaf5fc0: StoreField: r0->field_b = r1
    //     0xaf5fc0: stur            w1, [x0, #0xb]
    // 0xaf5fc4: LeaveFrame
    //     0xaf5fc4: mov             SP, fp
    //     0xaf5fc8: ldp             fp, lr, [SP], #0x10
    // 0xaf5fcc: ret
    //     0xaf5fcc: ret             
    // 0xaf5fd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf5fd0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf5fd4: b               #0xaf5e64
    // 0xaf5fd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xaf5fd8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf5fdc, size: 0x88
    // 0xaf5fdc: EnterFrame
    //     0xaf5fdc: stp             fp, lr, [SP, #-0x10]!
    //     0xaf5fe0: mov             fp, SP
    // 0xaf5fe4: ldr             x0, [fp, #0x10]
    // 0xaf5fe8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf5fe8: ldur            w1, [x0, #0x17]
    // 0xaf5fec: DecompressPointer r1
    //     0xaf5fec: add             x1, x1, HEAP, lsl #32
    // 0xaf5ff0: CheckStackOverflow
    //     0xaf5ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf5ff4: cmp             SP, x16
    //     0xaf5ff8: b.ls            #0xaf605c
    // 0xaf5ffc: LoadField: r0 = r1->field_b
    //     0xaf5ffc: ldur            w0, [x1, #0xb]
    // 0xaf6000: DecompressPointer r0
    //     0xaf6000: add             x0, x0, HEAP, lsl #32
    // 0xaf6004: LoadField: r2 = r0->field_f
    //     0xaf6004: ldur            w2, [x0, #0xf]
    // 0xaf6008: DecompressPointer r2
    //     0xaf6008: add             x2, x2, HEAP, lsl #32
    // 0xaf600c: LoadField: r3 = r0->field_13
    //     0xaf600c: ldur            w3, [x0, #0x13]
    // 0xaf6010: DecompressPointer r3
    //     0xaf6010: add             x3, x3, HEAP, lsl #32
    // 0xaf6014: LoadField: r0 = r1->field_13
    //     0xaf6014: ldur            w0, [x1, #0x13]
    // 0xaf6018: DecompressPointer r0
    //     0xaf6018: add             x0, x0, HEAP, lsl #32
    // 0xaf601c: LoadField: r5 = r1->field_f
    //     0xaf601c: ldur            w5, [x1, #0xf]
    // 0xaf6020: DecompressPointer r5
    //     0xaf6020: add             x5, x5, HEAP, lsl #32
    // 0xaf6024: r1 = LoadInt32Instr(r0)
    //     0xaf6024: sbfx            x1, x0, #1, #0x1f
    //     0xaf6028: tbz             w0, #0, #0xaf6030
    //     0xaf602c: ldur            x1, [x0, #7]
    // 0xaf6030: mov             x16, x3
    // 0xaf6034: mov             x3, x2
    // 0xaf6038: mov             x2, x16
    // 0xaf603c: mov             x16, x1
    // 0xaf6040: mov             x1, x3
    // 0xaf6044: mov             x3, x16
    // 0xaf6048: r0 = _openImageViewer()
    //     0xaf6048: bl              #0xaf5b44  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xaf604c: r0 = Null
    //     0xaf604c: mov             x0, NULL
    // 0xaf6050: LeaveFrame
    //     0xaf6050: mov             SP, fp
    //     0xaf6054: ldp             fp, lr, [SP], #0x10
    // 0xaf6058: ret
    //     0xaf6058: ret             
    // 0xaf605c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf605c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf6060: b               #0xaf5ffc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf6064, size: 0x8c
    // 0xaf6064: EnterFrame
    //     0xaf6064: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6068: mov             fp, SP
    // 0xaf606c: AllocStack(0x10)
    //     0xaf606c: sub             SP, SP, #0x10
    // 0xaf6070: SetupParameters()
    //     0xaf6070: ldr             x0, [fp, #0x10]
    //     0xaf6074: ldur            w2, [x0, #0x17]
    //     0xaf6078: add             x2, x2, HEAP, lsl #32
    //     0xaf607c: stur            x2, [fp, #-0x10]
    // 0xaf6080: CheckStackOverflow
    //     0xaf6080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf6084: cmp             SP, x16
    //     0xaf6088: b.ls            #0xaf60e8
    // 0xaf608c: LoadField: r0 = r2->field_13
    //     0xaf608c: ldur            w0, [x2, #0x13]
    // 0xaf6090: DecompressPointer r0
    //     0xaf6090: add             x0, x0, HEAP, lsl #32
    // 0xaf6094: mov             x1, x0
    // 0xaf6098: stur            x0, [fp, #-8]
    // 0xaf609c: r0 = value()
    //     0xaf609c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xaf60a0: eor             x2, x0, #0x10
    // 0xaf60a4: ldur            x1, [fp, #-8]
    // 0xaf60a8: r0 = value=()
    //     0xaf60a8: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xaf60ac: ldur            x0, [fp, #-0x10]
    // 0xaf60b0: LoadField: r3 = r0->field_f
    //     0xaf60b0: ldur            w3, [x0, #0xf]
    // 0xaf60b4: DecompressPointer r3
    //     0xaf60b4: add             x3, x3, HEAP, lsl #32
    // 0xaf60b8: stur            x3, [fp, #-8]
    // 0xaf60bc: r1 = Function '<anonymous closure>':.
    //     0xaf60bc: add             x1, PP, #0x58, lsl #12  ; [pp+0x58090] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xaf60c0: ldr             x1, [x1, #0x90]
    // 0xaf60c4: r2 = Null
    //     0xaf60c4: mov             x2, NULL
    // 0xaf60c8: r0 = AllocateClosure()
    //     0xaf60c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf60cc: ldur            x1, [fp, #-8]
    // 0xaf60d0: mov             x2, x0
    // 0xaf60d4: r0 = setState()
    //     0xaf60d4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaf60d8: r0 = Null
    //     0xaf60d8: mov             x0, NULL
    // 0xaf60dc: LeaveFrame
    //     0xaf60dc: mov             SP, fp
    //     0xaf60e0: ldp             fp, lr, [SP], #0x10
    // 0xaf60e4: ret
    //     0xaf60e4: ret             
    // 0xaf60e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf60e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf60ec: b               #0xaf608c
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xaf60f0, size: 0x84
    // 0xaf60f0: EnterFrame
    //     0xaf60f0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf60f4: mov             fp, SP
    // 0xaf60f8: AllocStack(0x10)
    //     0xaf60f8: sub             SP, SP, #0x10
    // 0xaf60fc: SetupParameters()
    //     0xaf60fc: ldr             x0, [fp, #0x18]
    //     0xaf6100: ldur            w1, [x0, #0x17]
    //     0xaf6104: add             x1, x1, HEAP, lsl #32
    //     0xaf6108: stur            x1, [fp, #-8]
    // 0xaf610c: CheckStackOverflow
    //     0xaf610c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf6110: cmp             SP, x16
    //     0xaf6114: b.ls            #0xaf616c
    // 0xaf6118: r1 = 1
    //     0xaf6118: movz            x1, #0x1
    // 0xaf611c: r0 = AllocateContext()
    //     0xaf611c: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf6120: mov             x1, x0
    // 0xaf6124: ldur            x0, [fp, #-8]
    // 0xaf6128: StoreField: r1->field_b = r0
    //     0xaf6128: stur            w0, [x1, #0xb]
    // 0xaf612c: ldr             x2, [fp, #0x10]
    // 0xaf6130: StoreField: r1->field_f = r2
    //     0xaf6130: stur            w2, [x1, #0xf]
    // 0xaf6134: LoadField: r3 = r0->field_f
    //     0xaf6134: ldur            w3, [x0, #0xf]
    // 0xaf6138: DecompressPointer r3
    //     0xaf6138: add             x3, x3, HEAP, lsl #32
    // 0xaf613c: mov             x2, x1
    // 0xaf6140: stur            x3, [fp, #-0x10]
    // 0xaf6144: r1 = Function '<anonymous closure>':.
    //     0xaf6144: add             x1, PP, #0x58, lsl #12  ; [pp+0x580e8] AnonymousClosure: (0xaf6174), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xaf3c40)
    //     0xaf6148: ldr             x1, [x1, #0xe8]
    // 0xaf614c: r0 = AllocateClosure()
    //     0xaf614c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf6150: ldur            x1, [fp, #-0x10]
    // 0xaf6154: mov             x2, x0
    // 0xaf6158: r0 = setState()
    //     0xaf6158: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xaf615c: r0 = Null
    //     0xaf615c: mov             x0, NULL
    // 0xaf6160: LeaveFrame
    //     0xaf6160: mov             SP, fp
    //     0xaf6164: ldp             fp, lr, [SP], #0x10
    // 0xaf6168: ret
    //     0xaf6168: ret             
    // 0xaf616c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf616c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf6170: b               #0xaf6118
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf6174, size: 0x8c
    // 0xaf6174: EnterFrame
    //     0xaf6174: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6178: mov             fp, SP
    // 0xaf617c: AllocStack(0x8)
    //     0xaf617c: sub             SP, SP, #8
    // 0xaf6180: SetupParameters()
    //     0xaf6180: ldr             x0, [fp, #0x10]
    //     0xaf6184: ldur            w1, [x0, #0x17]
    //     0xaf6188: add             x1, x1, HEAP, lsl #32
    // 0xaf618c: CheckStackOverflow
    //     0xaf618c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf6190: cmp             SP, x16
    //     0xaf6194: b.ls            #0xaf61f8
    // 0xaf6198: LoadField: r0 = r1->field_b
    //     0xaf6198: ldur            w0, [x1, #0xb]
    // 0xaf619c: DecompressPointer r0
    //     0xaf619c: add             x0, x0, HEAP, lsl #32
    // 0xaf61a0: LoadField: r2 = r0->field_f
    //     0xaf61a0: ldur            w2, [x0, #0xf]
    // 0xaf61a4: DecompressPointer r2
    //     0xaf61a4: add             x2, x2, HEAP, lsl #32
    // 0xaf61a8: LoadField: r0 = r1->field_f
    //     0xaf61a8: ldur            w0, [x1, #0xf]
    // 0xaf61ac: DecompressPointer r0
    //     0xaf61ac: add             x0, x0, HEAP, lsl #32
    // 0xaf61b0: r1 = LoadInt32Instr(r0)
    //     0xaf61b0: sbfx            x1, x0, #1, #0x1f
    //     0xaf61b4: tbz             w0, #0, #0xaf61bc
    //     0xaf61b8: ldur            x1, [x0, #7]
    // 0xaf61bc: ArrayStore: r2[0] = r1  ; List_8
    //     0xaf61bc: stur            x1, [x2, #0x17]
    // 0xaf61c0: LoadField: r0 = r2->field_1f
    //     0xaf61c0: ldur            w0, [x2, #0x1f]
    // 0xaf61c4: DecompressPointer r0
    //     0xaf61c4: add             x0, x0, HEAP, lsl #32
    // 0xaf61c8: stur            x0, [fp, #-8]
    // 0xaf61cc: r1 = Function '<anonymous closure>':.
    //     0xaf61cc: add             x1, PP, #0x58, lsl #12  ; [pp+0x580f0] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xaf61d0: ldr             x1, [x1, #0xf0]
    // 0xaf61d4: r2 = Null
    //     0xaf61d4: mov             x2, NULL
    // 0xaf61d8: r0 = AllocateClosure()
    //     0xaf61d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf61dc: ldur            x1, [fp, #-8]
    // 0xaf61e0: mov             x2, x0
    // 0xaf61e4: r0 = forEach()
    //     0xaf61e4: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xaf61e8: r0 = Null
    //     0xaf61e8: mov             x0, NULL
    // 0xaf61ec: LeaveFrame
    //     0xaf61ec: mov             SP, fp
    //     0xaf61f0: ldp             fp, lr, [SP], #0x10
    // 0xaf61f4: ret
    //     0xaf61f4: ret             
    // 0xaf61f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf61f8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf61fc: b               #0xaf6198
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaf6200, size: 0xb4
    // 0xaf6200: EnterFrame
    //     0xaf6200: stp             fp, lr, [SP, #-0x10]!
    //     0xaf6204: mov             fp, SP
    // 0xaf6208: AllocStack(0x38)
    //     0xaf6208: sub             SP, SP, #0x38
    // 0xaf620c: SetupParameters()
    //     0xaf620c: ldr             x0, [fp, #0x10]
    //     0xaf6210: ldur            w1, [x0, #0x17]
    //     0xaf6214: add             x1, x1, HEAP, lsl #32
    // 0xaf6218: CheckStackOverflow
    //     0xaf6218: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf621c: cmp             SP, x16
    //     0xaf6220: b.ls            #0xaf62a8
    // 0xaf6224: LoadField: r0 = r1->field_f
    //     0xaf6224: ldur            w0, [x1, #0xf]
    // 0xaf6228: DecompressPointer r0
    //     0xaf6228: add             x0, x0, HEAP, lsl #32
    // 0xaf622c: LoadField: r1 = r0->field_b
    //     0xaf622c: ldur            w1, [x0, #0xb]
    // 0xaf6230: DecompressPointer r1
    //     0xaf6230: add             x1, x1, HEAP, lsl #32
    // 0xaf6234: cmp             w1, NULL
    // 0xaf6238: b.eq            #0xaf62b0
    // 0xaf623c: LoadField: r0 = r1->field_1b
    //     0xaf623c: ldur            w0, [x1, #0x1b]
    // 0xaf6240: DecompressPointer r0
    //     0xaf6240: add             x0, x0, HEAP, lsl #32
    // 0xaf6244: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xaf6244: ldur            w2, [x1, #0x17]
    // 0xaf6248: DecompressPointer r2
    //     0xaf6248: add             x2, x2, HEAP, lsl #32
    // 0xaf624c: LoadField: r3 = r1->field_f
    //     0xaf624c: ldur            w3, [x1, #0xf]
    // 0xaf6250: DecompressPointer r3
    //     0xaf6250: add             x3, x3, HEAP, lsl #32
    // 0xaf6254: LoadField: r4 = r1->field_23
    //     0xaf6254: ldur            w4, [x1, #0x23]
    // 0xaf6258: DecompressPointer r4
    //     0xaf6258: add             x4, x4, HEAP, lsl #32
    // 0xaf625c: LoadField: r5 = r1->field_1f
    //     0xaf625c: ldur            w5, [x1, #0x1f]
    // 0xaf6260: DecompressPointer r5
    //     0xaf6260: add             x5, x5, HEAP, lsl #32
    // 0xaf6264: LoadField: r6 = r1->field_2b
    //     0xaf6264: ldur            w6, [x1, #0x2b]
    // 0xaf6268: DecompressPointer r6
    //     0xaf6268: add             x6, x6, HEAP, lsl #32
    // 0xaf626c: LoadField: r7 = r1->field_27
    //     0xaf626c: ldur            w7, [x1, #0x27]
    // 0xaf6270: DecompressPointer r7
    //     0xaf6270: add             x7, x7, HEAP, lsl #32
    // 0xaf6274: stp             x0, x7, [SP, #0x28]
    // 0xaf6278: stp             x3, x2, [SP, #0x18]
    // 0xaf627c: stp             x5, x4, [SP, #8]
    // 0xaf6280: str             x6, [SP]
    // 0xaf6284: r4 = 0
    //     0xaf6284: movz            x4, #0
    // 0xaf6288: ldr             x0, [SP, #0x30]
    // 0xaf628c: r5 = UnlinkedCall_0x613b5c
    //     0xaf628c: add             x16, PP, #0x58, lsl #12  ; [pp+0x580f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xaf6290: ldp             x5, lr, [x16, #0xf8]
    // 0xaf6294: blr             lr
    // 0xaf6298: r0 = Null
    //     0xaf6298: mov             x0, NULL
    // 0xaf629c: LeaveFrame
    //     0xaf629c: mov             SP, fp
    //     0xaf62a0: ldp             fp, lr, [SP], #0x10
    // 0xaf62a4: ret
    //     0xaf62a4: ret             
    // 0xaf62a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf62a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf62ac: b               #0xaf6224
    // 0xaf62b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf62b0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4156, size: 0x34, field offset: 0xc
//   const constructor, 
class TestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dc18, size: 0x84
    // 0xc7dc18: EnterFrame
    //     0xc7dc18: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dc1c: mov             fp, SP
    // 0xc7dc20: AllocStack(0x18)
    //     0xc7dc20: sub             SP, SP, #0x18
    // 0xc7dc24: CheckStackOverflow
    //     0xc7dc24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7dc28: cmp             SP, x16
    //     0xc7dc2c: b.ls            #0xc7dc94
    // 0xc7dc30: r1 = <TestimonialCarousel>
    //     0xc7dc30: add             x1, PP, #0x48, lsl #12  ; [pp+0x48bc0] TypeArguments: <TestimonialCarousel>
    //     0xc7dc34: ldr             x1, [x1, #0xbc0]
    // 0xc7dc38: r0 = _TestimonialCarouselState()
    //     0xc7dc38: bl              #0xc7dc9c  ; Allocate_TestimonialCarouselStateStub -> _TestimonialCarouselState (size=0x24)
    // 0xc7dc3c: mov             x1, x0
    // 0xc7dc40: r0 = Sentinel
    //     0xc7dc40: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7dc44: stur            x1, [fp, #-8]
    // 0xc7dc48: StoreField: r1->field_13 = r0
    //     0xc7dc48: stur            w0, [x1, #0x13]
    // 0xc7dc4c: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7dc4c: stur            xzr, [x1, #0x17]
    // 0xc7dc50: r16 = <int, RxBool>
    //     0xc7dc50: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc7dc54: ldr             x16, [x16, #0x298]
    // 0xc7dc58: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7dc5c: stp             lr, x16, [SP]
    // 0xc7dc60: r0 = Map._fromLiteral()
    //     0xc7dc60: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7dc64: ldur            x1, [fp, #-8]
    // 0xc7dc68: StoreField: r1->field_1f = r0
    //     0xc7dc68: stur            w0, [x1, #0x1f]
    //     0xc7dc6c: ldurb           w16, [x1, #-1]
    //     0xc7dc70: ldurb           w17, [x0, #-1]
    //     0xc7dc74: and             x16, x17, x16, lsr #2
    //     0xc7dc78: tst             x16, HEAP, lsr #32
    //     0xc7dc7c: b.eq            #0xc7dc84
    //     0xc7dc80: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7dc84: mov             x0, x1
    // 0xc7dc88: LeaveFrame
    //     0xc7dc88: mov             SP, fp
    //     0xc7dc8c: ldp             fp, lr, [SP], #0x10
    // 0xc7dc90: ret
    //     0xc7dc90: ret             
    // 0xc7dc94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7dc94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7dc98: b               #0xc7dc30
  }
}
