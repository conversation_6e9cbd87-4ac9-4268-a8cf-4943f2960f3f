// lib: , url: package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart

// class id: 1049335, size: 0x8
class :: {
}

// class id: 3387, size: 0x30, field offset: 0x14
class _RatingReviewOnTapImageState extends State<dynamic> {

  late PageController _pageController; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0x93d47c, size: 0x110
    // 0x93d47c: EnterFrame
    //     0x93d47c: stp             fp, lr, [SP, #-0x10]!
    //     0x93d480: mov             fp, SP
    // 0x93d484: AllocStack(0x18)
    //     0x93d484: sub             SP, SP, #0x18
    // 0x93d488: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r2, fp-0x10 */)
    //     0x93d488: mov             x2, x1
    //     0x93d48c: stur            x1, [fp, #-0x10]
    // 0x93d490: CheckStackOverflow
    //     0x93d490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d494: cmp             SP, x16
    //     0x93d498: b.ls            #0x93d57c
    // 0x93d49c: LoadField: r0 = r2->field_b
    //     0x93d49c: ldur            w0, [x2, #0xb]
    // 0x93d4a0: DecompressPointer r0
    //     0x93d4a0: add             x0, x0, HEAP, lsl #32
    // 0x93d4a4: cmp             w0, NULL
    // 0x93d4a8: b.eq            #0x93d584
    // 0x93d4ac: LoadField: r1 = r0->field_f
    //     0x93d4ac: ldur            x1, [x0, #0xf]
    // 0x93d4b0: stur            x1, [fp, #-8]
    // 0x93d4b4: StoreField: r2->field_13 = r1
    //     0x93d4b4: stur            x1, [x2, #0x13]
    // 0x93d4b8: r0 = PageController()
    //     0x93d4b8: bl              #0x7f73b0  ; AllocatePageControllerStub -> PageController (size=0x54)
    // 0x93d4bc: mov             x2, x0
    // 0x93d4c0: ldur            x0, [fp, #-8]
    // 0x93d4c4: stur            x2, [fp, #-0x18]
    // 0x93d4c8: StoreField: r2->field_3f = r0
    //     0x93d4c8: stur            x0, [x2, #0x3f]
    // 0x93d4cc: r0 = true
    //     0x93d4cc: add             x0, NULL, #0x20  ; true
    // 0x93d4d0: StoreField: r2->field_47 = r0
    //     0x93d4d0: stur            w0, [x2, #0x47]
    // 0x93d4d4: d0 = 1.000000
    //     0x93d4d4: fmov            d0, #1.00000000
    // 0x93d4d8: StoreField: r2->field_4b = d0
    //     0x93d4d8: stur            d0, [x2, #0x4b]
    // 0x93d4dc: mov             x1, x2
    // 0x93d4e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x93d4e0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x93d4e4: r0 = ScrollController()
    //     0x93d4e4: bl              #0x6759d0  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::ScrollController
    // 0x93d4e8: ldur            x0, [fp, #-0x18]
    // 0x93d4ec: ldur            x3, [fp, #-0x10]
    // 0x93d4f0: StoreField: r3->field_1b = r0
    //     0x93d4f0: stur            w0, [x3, #0x1b]
    //     0x93d4f4: ldurb           w16, [x3, #-1]
    //     0x93d4f8: ldurb           w17, [x0, #-1]
    //     0x93d4fc: and             x16, x17, x16, lsr #2
    //     0x93d500: tst             x16, HEAP, lsr #32
    //     0x93d504: b.eq            #0x93d50c
    //     0x93d508: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x93d50c: LoadField: r0 = r3->field_1f
    //     0x93d50c: ldur            w0, [x3, #0x1f]
    // 0x93d510: DecompressPointer r0
    //     0x93d510: add             x0, x0, HEAP, lsl #32
    // 0x93d514: mov             x2, x3
    // 0x93d518: stur            x0, [fp, #-0x18]
    // 0x93d51c: r1 = Function '_onCollapseChanged@1512004270':.
    //     0x93d51c: add             x1, PP, #0x57, lsl #12  ; [pp+0x575a8] AnonymousClosure: (0x93d5b0), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x93d5e8)
    //     0x93d520: ldr             x1, [x1, #0x5a8]
    // 0x93d524: r0 = AllocateClosure()
    //     0x93d524: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93d528: ldur            x1, [fp, #-0x18]
    // 0x93d52c: mov             x2, x0
    // 0x93d530: r0 = addListener()
    //     0x93d530: bl              #0x7b8dac  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x93d534: ldur            x1, [fp, #-0x10]
    // 0x93d538: LoadField: r2 = r1->field_b
    //     0x93d538: ldur            w2, [x1, #0xb]
    // 0x93d53c: DecompressPointer r2
    //     0x93d53c: add             x2, x2, HEAP, lsl #32
    // 0x93d540: cmp             w2, NULL
    // 0x93d544: b.eq            #0x93d588
    // 0x93d548: LoadField: r0 = r2->field_1b
    //     0x93d548: ldur            w0, [x2, #0x1b]
    // 0x93d54c: DecompressPointer r0
    //     0x93d54c: add             x0, x0, HEAP, lsl #32
    // 0x93d550: StoreField: r1->field_27 = r0
    //     0x93d550: stur            w0, [x1, #0x27]
    //     0x93d554: ldurb           w16, [x1, #-1]
    //     0x93d558: ldurb           w17, [x0, #-1]
    //     0x93d55c: and             x16, x17, x16, lsr #2
    //     0x93d560: tst             x16, HEAP, lsr #32
    //     0x93d564: b.eq            #0x93d56c
    //     0x93d568: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x93d56c: r0 = Null
    //     0x93d56c: mov             x0, NULL
    // 0x93d570: LeaveFrame
    //     0x93d570: mov             SP, fp
    //     0x93d574: ldp             fp, lr, [SP], #0x10
    // 0x93d578: ret
    //     0x93d578: ret             
    // 0x93d57c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d57c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d580: b               #0x93d49c
    // 0x93d584: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d584: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93d588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93d588: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onCollapseChanged(dynamic) {
    // ** addr: 0x93d5b0, size: 0x38
    // 0x93d5b0: EnterFrame
    //     0x93d5b0: stp             fp, lr, [SP, #-0x10]!
    //     0x93d5b4: mov             fp, SP
    // 0x93d5b8: ldr             x0, [fp, #0x10]
    // 0x93d5bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x93d5bc: ldur            w1, [x0, #0x17]
    // 0x93d5c0: DecompressPointer r1
    //     0x93d5c0: add             x1, x1, HEAP, lsl #32
    // 0x93d5c4: CheckStackOverflow
    //     0x93d5c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d5c8: cmp             SP, x16
    //     0x93d5cc: b.ls            #0x93d5e0
    // 0x93d5d0: r0 = _onCollapseChanged()
    //     0x93d5d0: bl              #0x93d5e8  ; [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged
    // 0x93d5d4: LeaveFrame
    //     0x93d5d4: mov             SP, fp
    //     0x93d5d8: ldp             fp, lr, [SP], #0x10
    // 0x93d5dc: ret
    //     0x93d5dc: ret             
    // 0x93d5e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d5e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d5e4: b               #0x93d5d0
  }
  _ _onCollapseChanged(/* No info */) {
    // ** addr: 0x93d5e8, size: 0x98
    // 0x93d5e8: EnterFrame
    //     0x93d5e8: stp             fp, lr, [SP, #-0x10]!
    //     0x93d5ec: mov             fp, SP
    // 0x93d5f0: AllocStack(0x8)
    //     0x93d5f0: sub             SP, SP, #8
    // 0x93d5f4: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r1, fp-0x8 */)
    //     0x93d5f4: stur            x1, [fp, #-8]
    // 0x93d5f8: CheckStackOverflow
    //     0x93d5f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93d5fc: cmp             SP, x16
    //     0x93d600: b.ls            #0x93d678
    // 0x93d604: r1 = 1
    //     0x93d604: movz            x1, #0x1
    // 0x93d608: r0 = AllocateContext()
    //     0x93d608: bl              #0x16f6108  ; AllocateContextStub
    // 0x93d60c: mov             x1, x0
    // 0x93d610: ldur            x0, [fp, #-8]
    // 0x93d614: StoreField: r1->field_f = r0
    //     0x93d614: stur            w0, [x1, #0xf]
    // 0x93d618: LoadField: r2 = r0->field_1f
    //     0x93d618: ldur            w2, [x0, #0x1f]
    // 0x93d61c: DecompressPointer r2
    //     0x93d61c: add             x2, x2, HEAP, lsl #32
    // 0x93d620: LoadField: r3 = r2->field_27
    //     0x93d620: ldur            w3, [x2, #0x27]
    // 0x93d624: DecompressPointer r3
    //     0x93d624: add             x3, x3, HEAP, lsl #32
    // 0x93d628: tbnz            w3, #4, #0x93d64c
    // 0x93d62c: mov             x2, x1
    // 0x93d630: r1 = Function '<anonymous closure>':.
    //     0x93d630: add             x1, PP, #0x57, lsl #12  ; [pp+0x575b0] AnonymousClosure: (0x935b70), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0x93d634: ldr             x1, [x1, #0x5b0]
    // 0x93d638: r0 = AllocateClosure()
    //     0x93d638: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93d63c: ldur            x1, [fp, #-8]
    // 0x93d640: mov             x2, x0
    // 0x93d644: r0 = setState()
    //     0x93d644: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93d648: b               #0x93d668
    // 0x93d64c: mov             x2, x1
    // 0x93d650: r1 = Function '<anonymous closure>':.
    //     0x93d650: add             x1, PP, #0x57, lsl #12  ; [pp+0x575b8] AnonymousClosure: (0x935ab4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x935ad8)
    //     0x93d654: ldr             x1, [x1, #0x5b8]
    // 0x93d658: r0 = AllocateClosure()
    //     0x93d658: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93d65c: ldur            x1, [fp, #-8]
    // 0x93d660: mov             x2, x0
    // 0x93d664: r0 = setState()
    //     0x93d664: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x93d668: r0 = Null
    //     0x93d668: mov             x0, NULL
    // 0x93d66c: LeaveFrame
    //     0x93d66c: mov             SP, fp
    //     0x93d670: ldp             fp, lr, [SP], #0x10
    // 0x93d674: ret
    //     0x93d674: ret             
    // 0x93d678: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93d678: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93d67c: b               #0x93d604
  }
  _ build(/* No info */) {
    // ** addr: 0xb245a0, size: 0x22c4
    // 0xb245a0: EnterFrame
    //     0xb245a0: stp             fp, lr, [SP, #-0x10]!
    //     0xb245a4: mov             fp, SP
    // 0xb245a8: AllocStack(0x80)
    //     0xb245a8: sub             SP, SP, #0x80
    // 0xb245ac: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb245ac: mov             x0, x1
    //     0xb245b0: stur            x1, [fp, #-8]
    //     0xb245b4: stur            x2, [fp, #-0x10]
    // 0xb245b8: CheckStackOverflow
    //     0xb245b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb245bc: cmp             SP, x16
    //     0xb245c0: b.ls            #0xb26788
    // 0xb245c4: r1 = 2
    //     0xb245c4: movz            x1, #0x2
    // 0xb245c8: r0 = AllocateContext()
    //     0xb245c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb245cc: mov             x3, x0
    // 0xb245d0: ldur            x0, [fp, #-8]
    // 0xb245d4: stur            x3, [fp, #-0x20]
    // 0xb245d8: StoreField: r3->field_f = r0
    //     0xb245d8: stur            w0, [x3, #0xf]
    // 0xb245dc: ldur            x1, [fp, #-0x10]
    // 0xb245e0: StoreField: r3->field_13 = r1
    //     0xb245e0: stur            w1, [x3, #0x13]
    // 0xb245e4: LoadField: r1 = r0->field_b
    //     0xb245e4: ldur            w1, [x0, #0xb]
    // 0xb245e8: DecompressPointer r1
    //     0xb245e8: add             x1, x1, HEAP, lsl #32
    // 0xb245ec: cmp             w1, NULL
    // 0xb245f0: b.eq            #0xb26790
    // 0xb245f4: LoadField: r2 = r1->field_b
    //     0xb245f4: ldur            w2, [x1, #0xb]
    // 0xb245f8: DecompressPointer r2
    //     0xb245f8: add             x2, x2, HEAP, lsl #32
    // 0xb245fc: cmp             w2, NULL
    // 0xb24600: b.ne            #0xb2460c
    // 0xb24604: r4 = Null
    //     0xb24604: mov             x4, NULL
    // 0xb24608: b               #0xb2461c
    // 0xb2460c: LoadField: r1 = r2->field_1b
    //     0xb2460c: ldur            w1, [x2, #0x1b]
    // 0xb24610: DecompressPointer r1
    //     0xb24610: add             x1, x1, HEAP, lsl #32
    // 0xb24614: LoadField: r2 = r1->field_b
    //     0xb24614: ldur            w2, [x1, #0xb]
    // 0xb24618: mov             x4, x2
    // 0xb2461c: stur            x4, [fp, #-0x18]
    // 0xb24620: LoadField: r5 = r0->field_1b
    //     0xb24620: ldur            w5, [x0, #0x1b]
    // 0xb24624: DecompressPointer r5
    //     0xb24624: add             x5, x5, HEAP, lsl #32
    // 0xb24628: r16 = Sentinel
    //     0xb24628: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb2462c: cmp             w5, w16
    // 0xb24630: b.eq            #0xb26794
    // 0xb24634: mov             x2, x0
    // 0xb24638: stur            x5, [fp, #-0x10]
    // 0xb2463c: r1 = Function '_onPageChanged@1512004270':.
    //     0xb2463c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57518] AnonymousClosure: (0xb27550), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged (0xb2758c)
    //     0xb24640: ldr             x1, [x1, #0x518]
    // 0xb24644: r0 = AllocateClosure()
    //     0xb24644: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb24648: ldur            x2, [fp, #-0x20]
    // 0xb2464c: r1 = Function '<anonymous closure>':.
    //     0xb2464c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57520] AnonymousClosure: (0xb2727c), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb245a0)
    //     0xb24650: ldr             x1, [x1, #0x520]
    // 0xb24654: stur            x0, [fp, #-0x28]
    // 0xb24658: r0 = AllocateClosure()
    //     0xb24658: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2465c: stur            x0, [fp, #-0x30]
    // 0xb24660: r0 = PageView()
    //     0xb24660: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb24664: stur            x0, [fp, #-0x38]
    // 0xb24668: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb24668: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb2466c: ldr             x16, [x16, #0x1c8]
    // 0xb24670: ldur            lr, [fp, #-0x10]
    // 0xb24674: stp             lr, x16, [SP]
    // 0xb24678: mov             x1, x0
    // 0xb2467c: ldur            x2, [fp, #-0x30]
    // 0xb24680: ldur            x3, [fp, #-0x18]
    // 0xb24684: ldur            x5, [fp, #-0x28]
    // 0xb24688: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xb24688: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xb2468c: ldr             x4, [x4, #0xe40]
    // 0xb24690: r0 = PageView.builder()
    //     0xb24690: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb24694: r1 = Null
    //     0xb24694: mov             x1, NULL
    // 0xb24698: r2 = 2
    //     0xb24698: movz            x2, #0x2
    // 0xb2469c: r0 = AllocateArray()
    //     0xb2469c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb246a0: mov             x2, x0
    // 0xb246a4: ldur            x0, [fp, #-0x38]
    // 0xb246a8: stur            x2, [fp, #-0x10]
    // 0xb246ac: StoreField: r2->field_f = r0
    //     0xb246ac: stur            w0, [x2, #0xf]
    // 0xb246b0: r1 = <Widget>
    //     0xb246b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb246b4: r0 = AllocateGrowableArray()
    //     0xb246b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb246b8: mov             x2, x0
    // 0xb246bc: ldur            x0, [fp, #-0x10]
    // 0xb246c0: stur            x2, [fp, #-0x18]
    // 0xb246c4: StoreField: r2->field_f = r0
    //     0xb246c4: stur            w0, [x2, #0xf]
    // 0xb246c8: r0 = 2
    //     0xb246c8: movz            x0, #0x2
    // 0xb246cc: StoreField: r2->field_b = r0
    //     0xb246cc: stur            w0, [x2, #0xb]
    // 0xb246d0: ldur            x0, [fp, #-8]
    // 0xb246d4: LoadField: r1 = r0->field_13
    //     0xb246d4: ldur            x1, [x0, #0x13]
    // 0xb246d8: cmp             x1, #0
    // 0xb246dc: b.le            #0xb24838
    // 0xb246e0: ldur            x3, [fp, #-0x20]
    // 0xb246e4: LoadField: r1 = r3->field_13
    //     0xb246e4: ldur            w1, [x3, #0x13]
    // 0xb246e8: DecompressPointer r1
    //     0xb246e8: add             x1, x1, HEAP, lsl #32
    // 0xb246ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb246ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb246f0: r0 = _of()
    //     0xb246f0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb246f4: LoadField: r1 = r0->field_7
    //     0xb246f4: ldur            w1, [x0, #7]
    // 0xb246f8: DecompressPointer r1
    //     0xb246f8: add             x1, x1, HEAP, lsl #32
    // 0xb246fc: LoadField: d0 = r1->field_7
    //     0xb246fc: ldur            d0, [x1, #7]
    // 0xb24700: d1 = 0.300000
    //     0xb24700: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb24704: ldr             d1, [x17, #0x658]
    // 0xb24708: fmul            d2, d0, d1
    // 0xb2470c: stur            d2, [fp, #-0x58]
    // 0xb24710: r0 = Container()
    //     0xb24710: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb24714: stur            x0, [fp, #-0x10]
    // 0xb24718: r16 = Instance_Color
    //     0xb24718: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb2471c: ldr             x16, [x16, #0xf88]
    // 0xb24720: str             x16, [SP]
    // 0xb24724: mov             x1, x0
    // 0xb24728: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb24728: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb2472c: ldr             x4, [x4, #0xf40]
    // 0xb24730: r0 = Container()
    //     0xb24730: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb24734: r0 = GestureDetector()
    //     0xb24734: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb24738: ldur            x2, [fp, #-0x20]
    // 0xb2473c: r1 = Function '<anonymous closure>':.
    //     0xb2473c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57528] AnonymousClosure: (0xb2720c), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb245a0)
    //     0xb24740: ldr             x1, [x1, #0x528]
    // 0xb24744: stur            x0, [fp, #-0x28]
    // 0xb24748: r0 = AllocateClosure()
    //     0xb24748: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2474c: ldur            x16, [fp, #-0x10]
    // 0xb24750: stp             x16, x0, [SP]
    // 0xb24754: ldur            x1, [fp, #-0x28]
    // 0xb24758: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb24758: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb2475c: ldr             x4, [x4, #0xaf0]
    // 0xb24760: r0 = GestureDetector()
    //     0xb24760: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb24764: r1 = <StackParentData>
    //     0xb24764: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb24768: ldr             x1, [x1, #0x8e0]
    // 0xb2476c: r0 = Positioned()
    //     0xb2476c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb24770: mov             x2, x0
    // 0xb24774: r0 = 0.000000
    //     0xb24774: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb24778: stur            x2, [fp, #-0x10]
    // 0xb2477c: StoreField: r2->field_13 = r0
    //     0xb2477c: stur            w0, [x2, #0x13]
    // 0xb24780: ArrayStore: r2[0] = r0  ; List_4
    //     0xb24780: stur            w0, [x2, #0x17]
    // 0xb24784: StoreField: r2->field_1f = r0
    //     0xb24784: stur            w0, [x2, #0x1f]
    // 0xb24788: ldur            d0, [fp, #-0x58]
    // 0xb2478c: r1 = inline_Allocate_Double()
    //     0xb2478c: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xb24790: add             x1, x1, #0x10
    //     0xb24794: cmp             x3, x1
    //     0xb24798: b.ls            #0xb267a0
    //     0xb2479c: str             x1, [THR, #0x50]  ; THR::top
    //     0xb247a0: sub             x1, x1, #0xf
    //     0xb247a4: movz            x3, #0xe15c
    //     0xb247a8: movk            x3, #0x3, lsl #16
    //     0xb247ac: stur            x3, [x1, #-1]
    // 0xb247b0: StoreField: r1->field_7 = d0
    //     0xb247b0: stur            d0, [x1, #7]
    // 0xb247b4: StoreField: r2->field_23 = r1
    //     0xb247b4: stur            w1, [x2, #0x23]
    // 0xb247b8: ldur            x1, [fp, #-0x28]
    // 0xb247bc: StoreField: r2->field_b = r1
    //     0xb247bc: stur            w1, [x2, #0xb]
    // 0xb247c0: ldur            x3, [fp, #-0x18]
    // 0xb247c4: LoadField: r1 = r3->field_b
    //     0xb247c4: ldur            w1, [x3, #0xb]
    // 0xb247c8: LoadField: r4 = r3->field_f
    //     0xb247c8: ldur            w4, [x3, #0xf]
    // 0xb247cc: DecompressPointer r4
    //     0xb247cc: add             x4, x4, HEAP, lsl #32
    // 0xb247d0: LoadField: r5 = r4->field_b
    //     0xb247d0: ldur            w5, [x4, #0xb]
    // 0xb247d4: r4 = LoadInt32Instr(r1)
    //     0xb247d4: sbfx            x4, x1, #1, #0x1f
    // 0xb247d8: stur            x4, [fp, #-0x40]
    // 0xb247dc: r1 = LoadInt32Instr(r5)
    //     0xb247dc: sbfx            x1, x5, #1, #0x1f
    // 0xb247e0: cmp             x4, x1
    // 0xb247e4: b.ne            #0xb247f0
    // 0xb247e8: mov             x1, x3
    // 0xb247ec: r0 = _growToNextCapacity()
    //     0xb247ec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb247f0: ldur            x2, [fp, #-0x18]
    // 0xb247f4: ldur            x3, [fp, #-0x40]
    // 0xb247f8: add             x0, x3, #1
    // 0xb247fc: lsl             x1, x0, #1
    // 0xb24800: StoreField: r2->field_b = r1
    //     0xb24800: stur            w1, [x2, #0xb]
    // 0xb24804: LoadField: r1 = r2->field_f
    //     0xb24804: ldur            w1, [x2, #0xf]
    // 0xb24808: DecompressPointer r1
    //     0xb24808: add             x1, x1, HEAP, lsl #32
    // 0xb2480c: ldur            x0, [fp, #-0x10]
    // 0xb24810: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb24810: add             x25, x1, x3, lsl #2
    //     0xb24814: add             x25, x25, #0xf
    //     0xb24818: str             w0, [x25]
    //     0xb2481c: tbz             w0, #0, #0xb24838
    //     0xb24820: ldurb           w16, [x1, #-1]
    //     0xb24824: ldurb           w17, [x0, #-1]
    //     0xb24828: and             x16, x17, x16, lsr #2
    //     0xb2482c: tst             x16, HEAP, lsr #32
    //     0xb24830: b.eq            #0xb24838
    //     0xb24834: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb24838: ldur            x0, [fp, #-8]
    // 0xb2483c: LoadField: r1 = r0->field_13
    //     0xb2483c: ldur            x1, [x0, #0x13]
    // 0xb24840: LoadField: r3 = r0->field_b
    //     0xb24840: ldur            w3, [x0, #0xb]
    // 0xb24844: DecompressPointer r3
    //     0xb24844: add             x3, x3, HEAP, lsl #32
    // 0xb24848: cmp             w3, NULL
    // 0xb2484c: b.eq            #0xb267bc
    // 0xb24850: LoadField: r4 = r3->field_b
    //     0xb24850: ldur            w4, [x3, #0xb]
    // 0xb24854: DecompressPointer r4
    //     0xb24854: add             x4, x4, HEAP, lsl #32
    // 0xb24858: cmp             w4, NULL
    // 0xb2485c: b.ne            #0xb24868
    // 0xb24860: r3 = Null
    //     0xb24860: mov             x3, NULL
    // 0xb24864: b               #0xb24878
    // 0xb24868: LoadField: r3 = r4->field_1b
    //     0xb24868: ldur            w3, [x4, #0x1b]
    // 0xb2486c: DecompressPointer r3
    //     0xb2486c: add             x3, x3, HEAP, lsl #32
    // 0xb24870: LoadField: r4 = r3->field_b
    //     0xb24870: ldur            w4, [x3, #0xb]
    // 0xb24874: mov             x3, x4
    // 0xb24878: cmp             w3, NULL
    // 0xb2487c: b.ne            #0xb24888
    // 0xb24880: r3 = 0
    //     0xb24880: movz            x3, #0
    // 0xb24884: b               #0xb24890
    // 0xb24888: r4 = LoadInt32Instr(r3)
    //     0xb24888: sbfx            x4, x3, #1, #0x1f
    // 0xb2488c: mov             x3, x4
    // 0xb24890: sub             x4, x3, #1
    // 0xb24894: cmp             x1, x4
    // 0xb24898: b.ge            #0xb249f4
    // 0xb2489c: ldur            x3, [fp, #-0x20]
    // 0xb248a0: LoadField: r1 = r3->field_13
    //     0xb248a0: ldur            w1, [x3, #0x13]
    // 0xb248a4: DecompressPointer r1
    //     0xb248a4: add             x1, x1, HEAP, lsl #32
    // 0xb248a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb248a8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb248ac: r0 = _of()
    //     0xb248ac: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb248b0: LoadField: r1 = r0->field_7
    //     0xb248b0: ldur            w1, [x0, #7]
    // 0xb248b4: DecompressPointer r1
    //     0xb248b4: add             x1, x1, HEAP, lsl #32
    // 0xb248b8: LoadField: d0 = r1->field_7
    //     0xb248b8: ldur            d0, [x1, #7]
    // 0xb248bc: d1 = 0.300000
    //     0xb248bc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xb248c0: ldr             d1, [x17, #0x658]
    // 0xb248c4: fmul            d2, d0, d1
    // 0xb248c8: stur            d2, [fp, #-0x58]
    // 0xb248cc: r0 = Container()
    //     0xb248cc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb248d0: stur            x0, [fp, #-0x10]
    // 0xb248d4: r16 = Instance_Color
    //     0xb248d4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb248d8: ldr             x16, [x16, #0xf88]
    // 0xb248dc: str             x16, [SP]
    // 0xb248e0: mov             x1, x0
    // 0xb248e4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xb248e4: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xb248e8: ldr             x4, [x4, #0xf40]
    // 0xb248ec: r0 = Container()
    //     0xb248ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb248f0: r0 = GestureDetector()
    //     0xb248f0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb248f4: ldur            x2, [fp, #-0x20]
    // 0xb248f8: r1 = Function '<anonymous closure>':.
    //     0xb248f8: add             x1, PP, #0x57, lsl #12  ; [pp+0x57530] AnonymousClosure: (0xb27144), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb245a0)
    //     0xb248fc: ldr             x1, [x1, #0x530]
    // 0xb24900: stur            x0, [fp, #-0x28]
    // 0xb24904: r0 = AllocateClosure()
    //     0xb24904: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb24908: ldur            x16, [fp, #-0x10]
    // 0xb2490c: stp             x16, x0, [SP]
    // 0xb24910: ldur            x1, [fp, #-0x28]
    // 0xb24914: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb24914: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb24918: ldr             x4, [x4, #0xaf0]
    // 0xb2491c: r0 = GestureDetector()
    //     0xb2491c: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb24920: r1 = <StackParentData>
    //     0xb24920: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb24924: ldr             x1, [x1, #0x8e0]
    // 0xb24928: r0 = Positioned()
    //     0xb24928: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb2492c: mov             x2, x0
    // 0xb24930: r0 = 0.000000
    //     0xb24930: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb24934: stur            x2, [fp, #-0x10]
    // 0xb24938: ArrayStore: r2[0] = r0  ; List_4
    //     0xb24938: stur            w0, [x2, #0x17]
    // 0xb2493c: StoreField: r2->field_1b = r0
    //     0xb2493c: stur            w0, [x2, #0x1b]
    // 0xb24940: StoreField: r2->field_1f = r0
    //     0xb24940: stur            w0, [x2, #0x1f]
    // 0xb24944: ldur            d0, [fp, #-0x58]
    // 0xb24948: r1 = inline_Allocate_Double()
    //     0xb24948: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xb2494c: add             x1, x1, #0x10
    //     0xb24950: cmp             x3, x1
    //     0xb24954: b.ls            #0xb267c0
    //     0xb24958: str             x1, [THR, #0x50]  ; THR::top
    //     0xb2495c: sub             x1, x1, #0xf
    //     0xb24960: movz            x3, #0xe15c
    //     0xb24964: movk            x3, #0x3, lsl #16
    //     0xb24968: stur            x3, [x1, #-1]
    // 0xb2496c: StoreField: r1->field_7 = d0
    //     0xb2496c: stur            d0, [x1, #7]
    // 0xb24970: StoreField: r2->field_23 = r1
    //     0xb24970: stur            w1, [x2, #0x23]
    // 0xb24974: ldur            x1, [fp, #-0x28]
    // 0xb24978: StoreField: r2->field_b = r1
    //     0xb24978: stur            w1, [x2, #0xb]
    // 0xb2497c: ldur            x3, [fp, #-0x18]
    // 0xb24980: LoadField: r1 = r3->field_b
    //     0xb24980: ldur            w1, [x3, #0xb]
    // 0xb24984: LoadField: r4 = r3->field_f
    //     0xb24984: ldur            w4, [x3, #0xf]
    // 0xb24988: DecompressPointer r4
    //     0xb24988: add             x4, x4, HEAP, lsl #32
    // 0xb2498c: LoadField: r5 = r4->field_b
    //     0xb2498c: ldur            w5, [x4, #0xb]
    // 0xb24990: r4 = LoadInt32Instr(r1)
    //     0xb24990: sbfx            x4, x1, #1, #0x1f
    // 0xb24994: stur            x4, [fp, #-0x40]
    // 0xb24998: r1 = LoadInt32Instr(r5)
    //     0xb24998: sbfx            x1, x5, #1, #0x1f
    // 0xb2499c: cmp             x4, x1
    // 0xb249a0: b.ne            #0xb249ac
    // 0xb249a4: mov             x1, x3
    // 0xb249a8: r0 = _growToNextCapacity()
    //     0xb249a8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb249ac: ldur            x2, [fp, #-0x18]
    // 0xb249b0: ldur            x3, [fp, #-0x40]
    // 0xb249b4: add             x0, x3, #1
    // 0xb249b8: lsl             x1, x0, #1
    // 0xb249bc: StoreField: r2->field_b = r1
    //     0xb249bc: stur            w1, [x2, #0xb]
    // 0xb249c0: LoadField: r1 = r2->field_f
    //     0xb249c0: ldur            w1, [x2, #0xf]
    // 0xb249c4: DecompressPointer r1
    //     0xb249c4: add             x1, x1, HEAP, lsl #32
    // 0xb249c8: ldur            x0, [fp, #-0x10]
    // 0xb249cc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb249cc: add             x25, x1, x3, lsl #2
    //     0xb249d0: add             x25, x25, #0xf
    //     0xb249d4: str             w0, [x25]
    //     0xb249d8: tbz             w0, #0, #0xb249f4
    //     0xb249dc: ldurb           w16, [x1, #-1]
    //     0xb249e0: ldurb           w17, [x0, #-1]
    //     0xb249e4: and             x16, x17, x16, lsr #2
    //     0xb249e8: tst             x16, HEAP, lsr #32
    //     0xb249ec: b.eq            #0xb249f4
    //     0xb249f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb249f4: ldur            x0, [fp, #-8]
    // 0xb249f8: ldur            x3, [fp, #-0x20]
    // 0xb249fc: LoadField: r1 = r3->field_13
    //     0xb249fc: ldur            w1, [x3, #0x13]
    // 0xb24a00: DecompressPointer r1
    //     0xb24a00: add             x1, x1, HEAP, lsl #32
    // 0xb24a04: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb24a04: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb24a08: r0 = _of()
    //     0xb24a08: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb24a0c: LoadField: r1 = r0->field_7
    //     0xb24a0c: ldur            w1, [x0, #7]
    // 0xb24a10: DecompressPointer r1
    //     0xb24a10: add             x1, x1, HEAP, lsl #32
    // 0xb24a14: LoadField: d0 = r1->field_7
    //     0xb24a14: ldur            d0, [x1, #7]
    // 0xb24a18: d1 = 0.950000
    //     0xb24a18: add             x17, PP, #0x51, lsl #12  ; [pp+0x51e58] IMM: double(0.95) from 0x3fee666666666666
    //     0xb24a1c: ldr             d1, [x17, #0xe58]
    // 0xb24a20: fmul            d2, d0, d1
    // 0xb24a24: ldur            x0, [fp, #-8]
    // 0xb24a28: stur            d2, [fp, #-0x58]
    // 0xb24a2c: LoadField: r1 = r0->field_b
    //     0xb24a2c: ldur            w1, [x0, #0xb]
    // 0xb24a30: DecompressPointer r1
    //     0xb24a30: add             x1, x1, HEAP, lsl #32
    // 0xb24a34: cmp             w1, NULL
    // 0xb24a38: b.eq            #0xb267dc
    // 0xb24a3c: LoadField: r2 = r1->field_b
    //     0xb24a3c: ldur            w2, [x1, #0xb]
    // 0xb24a40: DecompressPointer r2
    //     0xb24a40: add             x2, x2, HEAP, lsl #32
    // 0xb24a44: cmp             w2, NULL
    // 0xb24a48: b.ne            #0xb24a54
    // 0xb24a4c: r1 = Null
    //     0xb24a4c: mov             x1, NULL
    // 0xb24a50: b               #0xb24a64
    // 0xb24a54: LoadField: r1 = r2->field_1b
    //     0xb24a54: ldur            w1, [x2, #0x1b]
    // 0xb24a58: DecompressPointer r1
    //     0xb24a58: add             x1, x1, HEAP, lsl #32
    // 0xb24a5c: LoadField: r2 = r1->field_b
    //     0xb24a5c: ldur            w2, [x1, #0xb]
    // 0xb24a60: mov             x1, x2
    // 0xb24a64: cmp             w1, NULL
    // 0xb24a68: b.ne            #0xb24a74
    // 0xb24a6c: r3 = 0
    //     0xb24a6c: movz            x3, #0
    // 0xb24a70: b               #0xb24a7c
    // 0xb24a74: r2 = LoadInt32Instr(r1)
    //     0xb24a74: sbfx            x2, x1, #1, #0x1f
    // 0xb24a78: mov             x3, x2
    // 0xb24a7c: ldur            x2, [fp, #-0x20]
    // 0xb24a80: stur            x3, [fp, #-0x40]
    // 0xb24a84: r1 = Function '<anonymous closure>':.
    //     0xb24a84: add             x1, PP, #0x57, lsl #12  ; [pp+0x57538] AnonymousClosure: (0xaaa17c), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xb24a88: ldr             x1, [x1, #0x538]
    // 0xb24a8c: r0 = AllocateClosure()
    //     0xb24a8c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb24a90: ldur            x2, [fp, #-0x40]
    // 0xb24a94: r1 = <Widget>
    //     0xb24a94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb24a98: stur            x0, [fp, #-0x10]
    // 0xb24a9c: r0 = _GrowableList()
    //     0xb24a9c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb24aa0: mov             x1, x0
    // 0xb24aa4: stur            x1, [fp, #-0x28]
    // 0xb24aa8: r2 = 0
    //     0xb24aa8: movz            x2, #0
    // 0xb24aac: stur            x2, [fp, #-0x40]
    // 0xb24ab0: CheckStackOverflow
    //     0xb24ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb24ab4: cmp             SP, x16
    //     0xb24ab8: b.ls            #0xb267e0
    // 0xb24abc: LoadField: r0 = r1->field_b
    //     0xb24abc: ldur            w0, [x1, #0xb]
    // 0xb24ac0: r3 = LoadInt32Instr(r0)
    //     0xb24ac0: sbfx            x3, x0, #1, #0x1f
    // 0xb24ac4: cmp             x2, x3
    // 0xb24ac8: b.ge            #0xb24b8c
    // 0xb24acc: lsl             x0, x2, #1
    // 0xb24ad0: ldur            x16, [fp, #-0x10]
    // 0xb24ad4: stp             x0, x16, [SP]
    // 0xb24ad8: ldur            x0, [fp, #-0x10]
    // 0xb24adc: ClosureCall
    //     0xb24adc: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb24ae0: ldur            x2, [x0, #0x1f]
    //     0xb24ae4: blr             x2
    // 0xb24ae8: mov             x3, x0
    // 0xb24aec: r2 = Null
    //     0xb24aec: mov             x2, NULL
    // 0xb24af0: r1 = Null
    //     0xb24af0: mov             x1, NULL
    // 0xb24af4: stur            x3, [fp, #-0x30]
    // 0xb24af8: r4 = 60
    //     0xb24af8: movz            x4, #0x3c
    // 0xb24afc: branchIfSmi(r0, 0xb24b08)
    //     0xb24afc: tbz             w0, #0, #0xb24b08
    // 0xb24b00: r4 = LoadClassIdInstr(r0)
    //     0xb24b00: ldur            x4, [x0, #-1]
    //     0xb24b04: ubfx            x4, x4, #0xc, #0x14
    // 0xb24b08: sub             x4, x4, #0xe60
    // 0xb24b0c: cmp             x4, #0x464
    // 0xb24b10: b.ls            #0xb24b28
    // 0xb24b14: r8 = Widget
    //     0xb24b14: add             x8, PP, #0x51, lsl #12  ; [pp+0x51e68] Type: Widget
    //     0xb24b18: ldr             x8, [x8, #0xe68]
    // 0xb24b1c: r3 = Null
    //     0xb24b1c: add             x3, PP, #0x57, lsl #12  ; [pp+0x57540] Null
    //     0xb24b20: ldr             x3, [x3, #0x540]
    // 0xb24b24: r0 = Widget()
    //     0xb24b24: bl              #0x657fb8  ; IsType_Widget_Stub
    // 0xb24b28: ldur            x3, [fp, #-0x28]
    // 0xb24b2c: LoadField: r0 = r3->field_b
    //     0xb24b2c: ldur            w0, [x3, #0xb]
    // 0xb24b30: r1 = LoadInt32Instr(r0)
    //     0xb24b30: sbfx            x1, x0, #1, #0x1f
    // 0xb24b34: mov             x0, x1
    // 0xb24b38: ldur            x1, [fp, #-0x40]
    // 0xb24b3c: cmp             x1, x0
    // 0xb24b40: b.hs            #0xb267e8
    // 0xb24b44: LoadField: r1 = r3->field_f
    //     0xb24b44: ldur            w1, [x3, #0xf]
    // 0xb24b48: DecompressPointer r1
    //     0xb24b48: add             x1, x1, HEAP, lsl #32
    // 0xb24b4c: ldur            x0, [fp, #-0x30]
    // 0xb24b50: ldur            x2, [fp, #-0x40]
    // 0xb24b54: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb24b54: add             x25, x1, x2, lsl #2
    //     0xb24b58: add             x25, x25, #0xf
    //     0xb24b5c: str             w0, [x25]
    //     0xb24b60: tbz             w0, #0, #0xb24b7c
    //     0xb24b64: ldurb           w16, [x1, #-1]
    //     0xb24b68: ldurb           w17, [x0, #-1]
    //     0xb24b6c: and             x16, x17, x16, lsr #2
    //     0xb24b70: tst             x16, HEAP, lsr #32
    //     0xb24b74: b.eq            #0xb24b7c
    //     0xb24b78: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb24b7c: add             x0, x2, #1
    // 0xb24b80: mov             x2, x0
    // 0xb24b84: mov             x1, x3
    // 0xb24b88: b               #0xb24aac
    // 0xb24b8c: ldur            d0, [fp, #-0x58]
    // 0xb24b90: mov             x3, x1
    // 0xb24b94: ldur            x1, [fp, #-0x18]
    // 0xb24b98: r0 = Row()
    //     0xb24b98: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb24b9c: mov             x1, x0
    // 0xb24ba0: r0 = Instance_Axis
    //     0xb24ba0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb24ba4: stur            x1, [fp, #-0x30]
    // 0xb24ba8: StoreField: r1->field_f = r0
    //     0xb24ba8: stur            w0, [x1, #0xf]
    // 0xb24bac: r2 = Instance_MainAxisAlignment
    //     0xb24bac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb24bb0: ldr             x2, [x2, #0xab0]
    // 0xb24bb4: StoreField: r1->field_13 = r2
    //     0xb24bb4: stur            w2, [x1, #0x13]
    // 0xb24bb8: r2 = Instance_MainAxisSize
    //     0xb24bb8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb24bbc: ldr             x2, [x2, #0xa10]
    // 0xb24bc0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb24bc0: stur            w2, [x1, #0x17]
    // 0xb24bc4: r3 = Instance_CrossAxisAlignment
    //     0xb24bc4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb24bc8: ldr             x3, [x3, #0xa18]
    // 0xb24bcc: StoreField: r1->field_1b = r3
    //     0xb24bcc: stur            w3, [x1, #0x1b]
    // 0xb24bd0: r4 = Instance_VerticalDirection
    //     0xb24bd0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb24bd4: ldr             x4, [x4, #0xa20]
    // 0xb24bd8: StoreField: r1->field_23 = r4
    //     0xb24bd8: stur            w4, [x1, #0x23]
    // 0xb24bdc: r5 = Instance_Clip
    //     0xb24bdc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb24be0: ldr             x5, [x5, #0x38]
    // 0xb24be4: StoreField: r1->field_2b = r5
    //     0xb24be4: stur            w5, [x1, #0x2b]
    // 0xb24be8: StoreField: r1->field_2f = rZR
    //     0xb24be8: stur            xzr, [x1, #0x2f]
    // 0xb24bec: ldur            x6, [fp, #-0x28]
    // 0xb24bf0: StoreField: r1->field_b = r6
    //     0xb24bf0: stur            w6, [x1, #0xb]
    // 0xb24bf4: ldur            d0, [fp, #-0x58]
    // 0xb24bf8: r6 = inline_Allocate_Double()
    //     0xb24bf8: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xb24bfc: add             x6, x6, #0x10
    //     0xb24c00: cmp             x7, x6
    //     0xb24c04: b.ls            #0xb267ec
    //     0xb24c08: str             x6, [THR, #0x50]  ; THR::top
    //     0xb24c0c: sub             x6, x6, #0xf
    //     0xb24c10: movz            x7, #0xe15c
    //     0xb24c14: movk            x7, #0x3, lsl #16
    //     0xb24c18: stur            x7, [x6, #-1]
    // 0xb24c1c: StoreField: r6->field_7 = d0
    //     0xb24c1c: stur            d0, [x6, #7]
    // 0xb24c20: stur            x6, [fp, #-0x10]
    // 0xb24c24: r0 = SizedBox()
    //     0xb24c24: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb24c28: mov             x2, x0
    // 0xb24c2c: ldur            x0, [fp, #-0x10]
    // 0xb24c30: stur            x2, [fp, #-0x28]
    // 0xb24c34: StoreField: r2->field_f = r0
    //     0xb24c34: stur            w0, [x2, #0xf]
    // 0xb24c38: ldur            x0, [fp, #-0x30]
    // 0xb24c3c: StoreField: r2->field_b = r0
    //     0xb24c3c: stur            w0, [x2, #0xb]
    // 0xb24c40: r1 = <StackParentData>
    //     0xb24c40: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb24c44: ldr             x1, [x1, #0x8e0]
    // 0xb24c48: r0 = Positioned()
    //     0xb24c48: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb24c4c: mov             x2, x0
    // 0xb24c50: r0 = 12.000000
    //     0xb24c50: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb24c54: ldr             x0, [x0, #0x9e8]
    // 0xb24c58: stur            x2, [fp, #-0x10]
    // 0xb24c5c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb24c5c: stur            w0, [x2, #0x17]
    // 0xb24c60: ldur            x1, [fp, #-0x28]
    // 0xb24c64: StoreField: r2->field_b = r1
    //     0xb24c64: stur            w1, [x2, #0xb]
    // 0xb24c68: ldur            x3, [fp, #-0x18]
    // 0xb24c6c: LoadField: r1 = r3->field_b
    //     0xb24c6c: ldur            w1, [x3, #0xb]
    // 0xb24c70: LoadField: r4 = r3->field_f
    //     0xb24c70: ldur            w4, [x3, #0xf]
    // 0xb24c74: DecompressPointer r4
    //     0xb24c74: add             x4, x4, HEAP, lsl #32
    // 0xb24c78: LoadField: r5 = r4->field_b
    //     0xb24c78: ldur            w5, [x4, #0xb]
    // 0xb24c7c: r4 = LoadInt32Instr(r1)
    //     0xb24c7c: sbfx            x4, x1, #1, #0x1f
    // 0xb24c80: stur            x4, [fp, #-0x40]
    // 0xb24c84: r1 = LoadInt32Instr(r5)
    //     0xb24c84: sbfx            x1, x5, #1, #0x1f
    // 0xb24c88: cmp             x4, x1
    // 0xb24c8c: b.ne            #0xb24c98
    // 0xb24c90: mov             x1, x3
    // 0xb24c94: r0 = _growToNextCapacity()
    //     0xb24c94: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb24c98: ldur            x2, [fp, #-0x18]
    // 0xb24c9c: ldur            x3, [fp, #-0x40]
    // 0xb24ca0: add             x0, x3, #1
    // 0xb24ca4: lsl             x1, x0, #1
    // 0xb24ca8: StoreField: r2->field_b = r1
    //     0xb24ca8: stur            w1, [x2, #0xb]
    // 0xb24cac: LoadField: r1 = r2->field_f
    //     0xb24cac: ldur            w1, [x2, #0xf]
    // 0xb24cb0: DecompressPointer r1
    //     0xb24cb0: add             x1, x1, HEAP, lsl #32
    // 0xb24cb4: ldur            x0, [fp, #-0x10]
    // 0xb24cb8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb24cb8: add             x25, x1, x3, lsl #2
    //     0xb24cbc: add             x25, x25, #0xf
    //     0xb24cc0: str             w0, [x25]
    //     0xb24cc4: tbz             w0, #0, #0xb24ce0
    //     0xb24cc8: ldurb           w16, [x1, #-1]
    //     0xb24ccc: ldurb           w17, [x0, #-1]
    //     0xb24cd0: and             x16, x17, x16, lsr #2
    //     0xb24cd4: tst             x16, HEAP, lsr #32
    //     0xb24cd8: b.eq            #0xb24ce0
    //     0xb24cdc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb24ce0: r0 = GestureDetector()
    //     0xb24ce0: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb24ce4: r1 = Function '<anonymous closure>':.
    //     0xb24ce4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57550] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb24ce8: ldr             x1, [x1, #0x550]
    // 0xb24cec: r2 = Null
    //     0xb24cec: mov             x2, NULL
    // 0xb24cf0: stur            x0, [fp, #-0x10]
    // 0xb24cf4: r0 = AllocateClosure()
    //     0xb24cf4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb24cf8: r16 = Instance_Icon
    //     0xb24cf8: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e88] Obj!Icon@d665f1
    //     0xb24cfc: ldr             x16, [x16, #0xe88]
    // 0xb24d00: stp             x16, x0, [SP]
    // 0xb24d04: ldur            x1, [fp, #-0x10]
    // 0xb24d08: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xb24d08: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xb24d0c: ldr             x4, [x4, #0xaf0]
    // 0xb24d10: r0 = GestureDetector()
    //     0xb24d10: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb24d14: r1 = <StackParentData>
    //     0xb24d14: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb24d18: ldr             x1, [x1, #0x8e0]
    // 0xb24d1c: r0 = Positioned()
    //     0xb24d1c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb24d20: mov             x2, x0
    // 0xb24d24: r0 = 24.000000
    //     0xb24d24: add             x0, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb24d28: ldr             x0, [x0, #0xba8]
    // 0xb24d2c: stur            x2, [fp, #-0x28]
    // 0xb24d30: ArrayStore: r2[0] = r0  ; List_4
    //     0xb24d30: stur            w0, [x2, #0x17]
    // 0xb24d34: r0 = 12.000000
    //     0xb24d34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb24d38: ldr             x0, [x0, #0x9e8]
    // 0xb24d3c: StoreField: r2->field_1b = r0
    //     0xb24d3c: stur            w0, [x2, #0x1b]
    // 0xb24d40: ldur            x0, [fp, #-0x10]
    // 0xb24d44: StoreField: r2->field_b = r0
    //     0xb24d44: stur            w0, [x2, #0xb]
    // 0xb24d48: ldur            x0, [fp, #-0x18]
    // 0xb24d4c: LoadField: r1 = r0->field_b
    //     0xb24d4c: ldur            w1, [x0, #0xb]
    // 0xb24d50: LoadField: r3 = r0->field_f
    //     0xb24d50: ldur            w3, [x0, #0xf]
    // 0xb24d54: DecompressPointer r3
    //     0xb24d54: add             x3, x3, HEAP, lsl #32
    // 0xb24d58: LoadField: r4 = r3->field_b
    //     0xb24d58: ldur            w4, [x3, #0xb]
    // 0xb24d5c: r3 = LoadInt32Instr(r1)
    //     0xb24d5c: sbfx            x3, x1, #1, #0x1f
    // 0xb24d60: stur            x3, [fp, #-0x40]
    // 0xb24d64: r1 = LoadInt32Instr(r4)
    //     0xb24d64: sbfx            x1, x4, #1, #0x1f
    // 0xb24d68: cmp             x3, x1
    // 0xb24d6c: b.ne            #0xb24d78
    // 0xb24d70: mov             x1, x0
    // 0xb24d74: r0 = _growToNextCapacity()
    //     0xb24d74: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb24d78: ldur            x4, [fp, #-8]
    // 0xb24d7c: ldur            x2, [fp, #-0x18]
    // 0xb24d80: ldur            x3, [fp, #-0x40]
    // 0xb24d84: add             x0, x3, #1
    // 0xb24d88: lsl             x1, x0, #1
    // 0xb24d8c: StoreField: r2->field_b = r1
    //     0xb24d8c: stur            w1, [x2, #0xb]
    // 0xb24d90: LoadField: r1 = r2->field_f
    //     0xb24d90: ldur            w1, [x2, #0xf]
    // 0xb24d94: DecompressPointer r1
    //     0xb24d94: add             x1, x1, HEAP, lsl #32
    // 0xb24d98: ldur            x0, [fp, #-0x28]
    // 0xb24d9c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb24d9c: add             x25, x1, x3, lsl #2
    //     0xb24da0: add             x25, x25, #0xf
    //     0xb24da4: str             w0, [x25]
    //     0xb24da8: tbz             w0, #0, #0xb24dc4
    //     0xb24dac: ldurb           w16, [x1, #-1]
    //     0xb24db0: ldurb           w17, [x0, #-1]
    //     0xb24db4: and             x16, x17, x16, lsr #2
    //     0xb24db8: tst             x16, HEAP, lsr #32
    //     0xb24dbc: b.eq            #0xb24dc4
    //     0xb24dc0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb24dc4: LoadField: r0 = r4->field_23
    //     0xb24dc4: ldur            w0, [x4, #0x23]
    // 0xb24dc8: DecompressPointer r0
    //     0xb24dc8: add             x0, x0, HEAP, lsl #32
    // 0xb24dcc: tbnz            w0, #4, #0xb2584c
    // 0xb24dd0: ldur            x0, [fp, #-0x20]
    // 0xb24dd4: LoadField: r1 = r0->field_13
    //     0xb24dd4: ldur            w1, [x0, #0x13]
    // 0xb24dd8: DecompressPointer r1
    //     0xb24dd8: add             x1, x1, HEAP, lsl #32
    // 0xb24ddc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb24ddc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb24de0: r0 = _of()
    //     0xb24de0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb24de4: LoadField: r1 = r0->field_7
    //     0xb24de4: ldur            w1, [x0, #7]
    // 0xb24de8: DecompressPointer r1
    //     0xb24de8: add             x1, x1, HEAP, lsl #32
    // 0xb24dec: LoadField: d1 = r1->field_7
    //     0xb24dec: ldur            d1, [x1, #7]
    // 0xb24df0: stur            d1, [fp, #-0x58]
    // 0xb24df4: r1 = Instance_Color
    //     0xb24df4: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb24df8: d0 = 0.700000
    //     0xb24df8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb24dfc: ldr             d0, [x17, #0xf48]
    // 0xb24e00: r0 = withOpacity()
    //     0xb24e00: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb24e04: stur            x0, [fp, #-0x10]
    // 0xb24e08: r0 = BoxDecoration()
    //     0xb24e08: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb24e0c: mov             x2, x0
    // 0xb24e10: ldur            x0, [fp, #-0x10]
    // 0xb24e14: stur            x2, [fp, #-0x28]
    // 0xb24e18: StoreField: r2->field_7 = r0
    //     0xb24e18: stur            w0, [x2, #7]
    // 0xb24e1c: r0 = Instance_BorderRadius
    //     0xb24e1c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3cbf8] Obj!BorderRadius@d5a201
    //     0xb24e20: ldr             x0, [x0, #0xbf8]
    // 0xb24e24: StoreField: r2->field_13 = r0
    //     0xb24e24: stur            w0, [x2, #0x13]
    // 0xb24e28: r0 = Instance_BoxShape
    //     0xb24e28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb24e2c: ldr             x0, [x0, #0x80]
    // 0xb24e30: StoreField: r2->field_23 = r0
    //     0xb24e30: stur            w0, [x2, #0x23]
    // 0xb24e34: r1 = Instance_Color
    //     0xb24e34: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb24e38: d0 = 0.500000
    //     0xb24e38: fmov            d0, #0.50000000
    // 0xb24e3c: r0 = withOpacity()
    //     0xb24e3c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb24e40: stur            x0, [fp, #-0x10]
    // 0xb24e44: r0 = BoxDecoration()
    //     0xb24e44: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb24e48: mov             x1, x0
    // 0xb24e4c: ldur            x0, [fp, #-0x10]
    // 0xb24e50: stur            x1, [fp, #-0x30]
    // 0xb24e54: StoreField: r1->field_7 = r0
    //     0xb24e54: stur            w0, [x1, #7]
    // 0xb24e58: r0 = Instance_BoxShape
    //     0xb24e58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb24e5c: ldr             x0, [x0, #0x970]
    // 0xb24e60: StoreField: r1->field_23 = r0
    //     0xb24e60: stur            w0, [x1, #0x23]
    // 0xb24e64: ldur            x2, [fp, #-8]
    // 0xb24e68: LoadField: r3 = r2->field_b
    //     0xb24e68: ldur            w3, [x2, #0xb]
    // 0xb24e6c: DecompressPointer r3
    //     0xb24e6c: add             x3, x3, HEAP, lsl #32
    // 0xb24e70: cmp             w3, NULL
    // 0xb24e74: b.eq            #0xb26818
    // 0xb24e78: LoadField: r4 = r3->field_b
    //     0xb24e78: ldur            w4, [x3, #0xb]
    // 0xb24e7c: DecompressPointer r4
    //     0xb24e7c: add             x4, x4, HEAP, lsl #32
    // 0xb24e80: cmp             w4, NULL
    // 0xb24e84: b.ne            #0xb24e90
    // 0xb24e88: r0 = Null
    //     0xb24e88: mov             x0, NULL
    // 0xb24e8c: b               #0xb24ecc
    // 0xb24e90: LoadField: r3 = r4->field_7
    //     0xb24e90: ldur            w3, [x4, #7]
    // 0xb24e94: DecompressPointer r3
    //     0xb24e94: add             x3, x3, HEAP, lsl #32
    // 0xb24e98: cmp             w3, NULL
    // 0xb24e9c: b.ne            #0xb24ea8
    // 0xb24ea0: r0 = Null
    //     0xb24ea0: mov             x0, NULL
    // 0xb24ea4: b               #0xb24ecc
    // 0xb24ea8: stp             xzr, x3, [SP]
    // 0xb24eac: r0 = []()
    //     0xb24eac: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb24eb0: r1 = LoadClassIdInstr(r0)
    //     0xb24eb0: ldur            x1, [x0, #-1]
    //     0xb24eb4: ubfx            x1, x1, #0xc, #0x14
    // 0xb24eb8: str             x0, [SP]
    // 0xb24ebc: mov             x0, x1
    // 0xb24ec0: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb24ec0: sub             lr, x0, #1, lsl #12
    //     0xb24ec4: ldr             lr, [x21, lr, lsl #3]
    //     0xb24ec8: blr             lr
    // 0xb24ecc: cmp             w0, NULL
    // 0xb24ed0: b.ne            #0xb24edc
    // 0xb24ed4: r3 = ""
    //     0xb24ed4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb24ed8: b               #0xb24ee0
    // 0xb24edc: mov             x3, x0
    // 0xb24ee0: ldur            x0, [fp, #-8]
    // 0xb24ee4: ldur            x2, [fp, #-0x20]
    // 0xb24ee8: stur            x3, [fp, #-0x10]
    // 0xb24eec: LoadField: r1 = r2->field_13
    //     0xb24eec: ldur            w1, [x2, #0x13]
    // 0xb24ef0: DecompressPointer r1
    //     0xb24ef0: add             x1, x1, HEAP, lsl #32
    // 0xb24ef4: r0 = of()
    //     0xb24ef4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb24ef8: LoadField: r1 = r0->field_87
    //     0xb24ef8: ldur            w1, [x0, #0x87]
    // 0xb24efc: DecompressPointer r1
    //     0xb24efc: add             x1, x1, HEAP, lsl #32
    // 0xb24f00: LoadField: r0 = r1->field_7
    //     0xb24f00: ldur            w0, [x1, #7]
    // 0xb24f04: DecompressPointer r0
    //     0xb24f04: add             x0, x0, HEAP, lsl #32
    // 0xb24f08: r16 = 16.000000
    //     0xb24f08: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb24f0c: ldr             x16, [x16, #0x188]
    // 0xb24f10: r30 = Instance_Color
    //     0xb24f10: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb24f14: stp             lr, x16, [SP]
    // 0xb24f18: mov             x1, x0
    // 0xb24f1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb24f1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb24f20: ldr             x4, [x4, #0xaa0]
    // 0xb24f24: r0 = copyWith()
    //     0xb24f24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb24f28: stur            x0, [fp, #-0x38]
    // 0xb24f2c: r0 = Text()
    //     0xb24f2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb24f30: mov             x1, x0
    // 0xb24f34: ldur            x0, [fp, #-0x10]
    // 0xb24f38: stur            x1, [fp, #-0x48]
    // 0xb24f3c: StoreField: r1->field_b = r0
    //     0xb24f3c: stur            w0, [x1, #0xb]
    // 0xb24f40: ldur            x0, [fp, #-0x38]
    // 0xb24f44: StoreField: r1->field_13 = r0
    //     0xb24f44: stur            w0, [x1, #0x13]
    // 0xb24f48: r0 = Center()
    //     0xb24f48: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb24f4c: mov             x1, x0
    // 0xb24f50: r0 = Instance_Alignment
    //     0xb24f50: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb24f54: ldr             x0, [x0, #0xb10]
    // 0xb24f58: stur            x1, [fp, #-0x10]
    // 0xb24f5c: StoreField: r1->field_f = r0
    //     0xb24f5c: stur            w0, [x1, #0xf]
    // 0xb24f60: ldur            x2, [fp, #-0x48]
    // 0xb24f64: StoreField: r1->field_b = r2
    //     0xb24f64: stur            w2, [x1, #0xb]
    // 0xb24f68: r0 = Container()
    //     0xb24f68: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb24f6c: stur            x0, [fp, #-0x38]
    // 0xb24f70: r16 = 34.000000
    //     0xb24f70: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb24f74: ldr             x16, [x16, #0x978]
    // 0xb24f78: r30 = 34.000000
    //     0xb24f78: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb24f7c: ldr             lr, [lr, #0x978]
    // 0xb24f80: stp             lr, x16, [SP, #0x18]
    // 0xb24f84: r16 = Instance_EdgeInsets
    //     0xb24f84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb24f88: ldr             x16, [x16, #0x980]
    // 0xb24f8c: ldur            lr, [fp, #-0x30]
    // 0xb24f90: stp             lr, x16, [SP, #8]
    // 0xb24f94: ldur            x16, [fp, #-0x10]
    // 0xb24f98: str             x16, [SP]
    // 0xb24f9c: mov             x1, x0
    // 0xb24fa0: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb24fa0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb24fa4: ldr             x4, [x4, #0x988]
    // 0xb24fa8: r0 = Container()
    //     0xb24fa8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb24fac: ldur            x0, [fp, #-8]
    // 0xb24fb0: LoadField: r1 = r0->field_b
    //     0xb24fb0: ldur            w1, [x0, #0xb]
    // 0xb24fb4: DecompressPointer r1
    //     0xb24fb4: add             x1, x1, HEAP, lsl #32
    // 0xb24fb8: cmp             w1, NULL
    // 0xb24fbc: b.eq            #0xb2681c
    // 0xb24fc0: LoadField: r2 = r1->field_b
    //     0xb24fc0: ldur            w2, [x1, #0xb]
    // 0xb24fc4: DecompressPointer r2
    //     0xb24fc4: add             x2, x2, HEAP, lsl #32
    // 0xb24fc8: cmp             w2, NULL
    // 0xb24fcc: b.ne            #0xb24fd8
    // 0xb24fd0: r1 = Null
    //     0xb24fd0: mov             x1, NULL
    // 0xb24fd4: b               #0xb24fe0
    // 0xb24fd8: LoadField: r1 = r2->field_7
    //     0xb24fd8: ldur            w1, [x2, #7]
    // 0xb24fdc: DecompressPointer r1
    //     0xb24fdc: add             x1, x1, HEAP, lsl #32
    // 0xb24fe0: cmp             w1, NULL
    // 0xb24fe4: b.ne            #0xb24ff0
    // 0xb24fe8: r3 = ""
    //     0xb24fe8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb24fec: b               #0xb24ff4
    // 0xb24ff0: mov             x3, x1
    // 0xb24ff4: ldur            x2, [fp, #-0x20]
    // 0xb24ff8: stur            x3, [fp, #-0x10]
    // 0xb24ffc: LoadField: r1 = r2->field_13
    //     0xb24ffc: ldur            w1, [x2, #0x13]
    // 0xb25000: DecompressPointer r1
    //     0xb25000: add             x1, x1, HEAP, lsl #32
    // 0xb25004: r0 = of()
    //     0xb25004: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb25008: LoadField: r1 = r0->field_87
    //     0xb25008: ldur            w1, [x0, #0x87]
    // 0xb2500c: DecompressPointer r1
    //     0xb2500c: add             x1, x1, HEAP, lsl #32
    // 0xb25010: LoadField: r0 = r1->field_7
    //     0xb25010: ldur            w0, [x1, #7]
    // 0xb25014: DecompressPointer r0
    //     0xb25014: add             x0, x0, HEAP, lsl #32
    // 0xb25018: r16 = 14.000000
    //     0xb25018: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb2501c: ldr             x16, [x16, #0x1d8]
    // 0xb25020: r30 = Instance_Color
    //     0xb25020: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb25024: stp             lr, x16, [SP]
    // 0xb25028: mov             x1, x0
    // 0xb2502c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb2502c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb25030: ldr             x4, [x4, #0xaa0]
    // 0xb25034: r0 = copyWith()
    //     0xb25034: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb25038: stur            x0, [fp, #-0x30]
    // 0xb2503c: r0 = Text()
    //     0xb2503c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb25040: mov             x2, x0
    // 0xb25044: ldur            x0, [fp, #-0x10]
    // 0xb25048: stur            x2, [fp, #-0x48]
    // 0xb2504c: StoreField: r2->field_b = r0
    //     0xb2504c: stur            w0, [x2, #0xb]
    // 0xb25050: ldur            x0, [fp, #-0x30]
    // 0xb25054: StoreField: r2->field_13 = r0
    //     0xb25054: stur            w0, [x2, #0x13]
    // 0xb25058: ldur            x0, [fp, #-8]
    // 0xb2505c: LoadField: r1 = r0->field_b
    //     0xb2505c: ldur            w1, [x0, #0xb]
    // 0xb25060: DecompressPointer r1
    //     0xb25060: add             x1, x1, HEAP, lsl #32
    // 0xb25064: cmp             w1, NULL
    // 0xb25068: b.eq            #0xb26820
    // 0xb2506c: LoadField: r3 = r1->field_b
    //     0xb2506c: ldur            w3, [x1, #0xb]
    // 0xb25070: DecompressPointer r3
    //     0xb25070: add             x3, x3, HEAP, lsl #32
    // 0xb25074: cmp             w3, NULL
    // 0xb25078: b.ne            #0xb25084
    // 0xb2507c: r1 = Null
    //     0xb2507c: mov             x1, NULL
    // 0xb25080: b               #0xb2508c
    // 0xb25084: LoadField: r1 = r3->field_1f
    //     0xb25084: ldur            w1, [x3, #0x1f]
    // 0xb25088: DecompressPointer r1
    //     0xb25088: add             x1, x1, HEAP, lsl #32
    // 0xb2508c: cmp             w1, NULL
    // 0xb25090: b.ne            #0xb2509c
    // 0xb25094: r4 = ""
    //     0xb25094: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25098: b               #0xb250a0
    // 0xb2509c: mov             x4, x1
    // 0xb250a0: ldur            x3, [fp, #-0x20]
    // 0xb250a4: stur            x4, [fp, #-0x10]
    // 0xb250a8: LoadField: r1 = r3->field_13
    //     0xb250a8: ldur            w1, [x3, #0x13]
    // 0xb250ac: DecompressPointer r1
    //     0xb250ac: add             x1, x1, HEAP, lsl #32
    // 0xb250b0: r0 = of()
    //     0xb250b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb250b4: LoadField: r1 = r0->field_87
    //     0xb250b4: ldur            w1, [x0, #0x87]
    // 0xb250b8: DecompressPointer r1
    //     0xb250b8: add             x1, x1, HEAP, lsl #32
    // 0xb250bc: LoadField: r0 = r1->field_33
    //     0xb250bc: ldur            w0, [x1, #0x33]
    // 0xb250c0: DecompressPointer r0
    //     0xb250c0: add             x0, x0, HEAP, lsl #32
    // 0xb250c4: stur            x0, [fp, #-0x30]
    // 0xb250c8: cmp             w0, NULL
    // 0xb250cc: b.ne            #0xb250d8
    // 0xb250d0: r4 = Null
    //     0xb250d0: mov             x4, NULL
    // 0xb250d4: b               #0xb25100
    // 0xb250d8: r1 = Instance_Color
    //     0xb250d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb250dc: d0 = 0.500000
    //     0xb250dc: fmov            d0, #0.50000000
    // 0xb250e0: r0 = withOpacity()
    //     0xb250e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb250e4: r16 = 10.000000
    //     0xb250e4: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb250e8: stp             x0, x16, [SP]
    // 0xb250ec: ldur            x1, [fp, #-0x30]
    // 0xb250f0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb250f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb250f4: ldr             x4, [x4, #0xaa0]
    // 0xb250f8: r0 = copyWith()
    //     0xb250f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb250fc: mov             x4, x0
    // 0xb25100: ldur            x1, [fp, #-8]
    // 0xb25104: ldur            x3, [fp, #-0x38]
    // 0xb25108: ldur            x0, [fp, #-0x48]
    // 0xb2510c: ldur            x2, [fp, #-0x10]
    // 0xb25110: stur            x4, [fp, #-0x30]
    // 0xb25114: r0 = Text()
    //     0xb25114: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb25118: mov             x1, x0
    // 0xb2511c: ldur            x0, [fp, #-0x10]
    // 0xb25120: stur            x1, [fp, #-0x50]
    // 0xb25124: StoreField: r1->field_b = r0
    //     0xb25124: stur            w0, [x1, #0xb]
    // 0xb25128: ldur            x0, [fp, #-0x30]
    // 0xb2512c: StoreField: r1->field_13 = r0
    //     0xb2512c: stur            w0, [x1, #0x13]
    // 0xb25130: r0 = Padding()
    //     0xb25130: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb25134: mov             x3, x0
    // 0xb25138: r0 = Instance_EdgeInsets
    //     0xb25138: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb2513c: ldr             x0, [x0, #0xe90]
    // 0xb25140: stur            x3, [fp, #-0x10]
    // 0xb25144: StoreField: r3->field_f = r0
    //     0xb25144: stur            w0, [x3, #0xf]
    // 0xb25148: ldur            x1, [fp, #-0x50]
    // 0xb2514c: StoreField: r3->field_b = r1
    //     0xb2514c: stur            w1, [x3, #0xb]
    // 0xb25150: r1 = Null
    //     0xb25150: mov             x1, NULL
    // 0xb25154: r2 = 4
    //     0xb25154: movz            x2, #0x4
    // 0xb25158: r0 = AllocateArray()
    //     0xb25158: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2515c: mov             x2, x0
    // 0xb25160: ldur            x0, [fp, #-0x48]
    // 0xb25164: stur            x2, [fp, #-0x30]
    // 0xb25168: StoreField: r2->field_f = r0
    //     0xb25168: stur            w0, [x2, #0xf]
    // 0xb2516c: ldur            x0, [fp, #-0x10]
    // 0xb25170: StoreField: r2->field_13 = r0
    //     0xb25170: stur            w0, [x2, #0x13]
    // 0xb25174: r1 = <Widget>
    //     0xb25174: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb25178: r0 = AllocateGrowableArray()
    //     0xb25178: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb2517c: mov             x1, x0
    // 0xb25180: ldur            x0, [fp, #-0x30]
    // 0xb25184: stur            x1, [fp, #-0x10]
    // 0xb25188: StoreField: r1->field_f = r0
    //     0xb25188: stur            w0, [x1, #0xf]
    // 0xb2518c: r2 = 4
    //     0xb2518c: movz            x2, #0x4
    // 0xb25190: StoreField: r1->field_b = r2
    //     0xb25190: stur            w2, [x1, #0xb]
    // 0xb25194: r0 = Column()
    //     0xb25194: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb25198: mov             x3, x0
    // 0xb2519c: r0 = Instance_Axis
    //     0xb2519c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb251a0: stur            x3, [fp, #-0x30]
    // 0xb251a4: StoreField: r3->field_f = r0
    //     0xb251a4: stur            w0, [x3, #0xf]
    // 0xb251a8: r4 = Instance_MainAxisAlignment
    //     0xb251a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb251ac: ldr             x4, [x4, #0xa08]
    // 0xb251b0: StoreField: r3->field_13 = r4
    //     0xb251b0: stur            w4, [x3, #0x13]
    // 0xb251b4: r5 = Instance_MainAxisSize
    //     0xb251b4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb251b8: ldr             x5, [x5, #0xa10]
    // 0xb251bc: ArrayStore: r3[0] = r5  ; List_4
    //     0xb251bc: stur            w5, [x3, #0x17]
    // 0xb251c0: r6 = Instance_CrossAxisAlignment
    //     0xb251c0: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb251c4: ldr             x6, [x6, #0x890]
    // 0xb251c8: StoreField: r3->field_1b = r6
    //     0xb251c8: stur            w6, [x3, #0x1b]
    // 0xb251cc: r7 = Instance_VerticalDirection
    //     0xb251cc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb251d0: ldr             x7, [x7, #0xa20]
    // 0xb251d4: StoreField: r3->field_23 = r7
    //     0xb251d4: stur            w7, [x3, #0x23]
    // 0xb251d8: r8 = Instance_Clip
    //     0xb251d8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb251dc: ldr             x8, [x8, #0x38]
    // 0xb251e0: StoreField: r3->field_2b = r8
    //     0xb251e0: stur            w8, [x3, #0x2b]
    // 0xb251e4: StoreField: r3->field_2f = rZR
    //     0xb251e4: stur            xzr, [x3, #0x2f]
    // 0xb251e8: ldur            x1, [fp, #-0x10]
    // 0xb251ec: StoreField: r3->field_b = r1
    //     0xb251ec: stur            w1, [x3, #0xb]
    // 0xb251f0: r1 = Null
    //     0xb251f0: mov             x1, NULL
    // 0xb251f4: r2 = 6
    //     0xb251f4: movz            x2, #0x6
    // 0xb251f8: r0 = AllocateArray()
    //     0xb251f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb251fc: mov             x2, x0
    // 0xb25200: ldur            x0, [fp, #-0x38]
    // 0xb25204: stur            x2, [fp, #-0x10]
    // 0xb25208: StoreField: r2->field_f = r0
    //     0xb25208: stur            w0, [x2, #0xf]
    // 0xb2520c: r16 = Instance_SizedBox
    //     0xb2520c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb25210: ldr             x16, [x16, #0x998]
    // 0xb25214: StoreField: r2->field_13 = r16
    //     0xb25214: stur            w16, [x2, #0x13]
    // 0xb25218: ldur            x0, [fp, #-0x30]
    // 0xb2521c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb2521c: stur            w0, [x2, #0x17]
    // 0xb25220: r1 = <Widget>
    //     0xb25220: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb25224: r0 = AllocateGrowableArray()
    //     0xb25224: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb25228: mov             x1, x0
    // 0xb2522c: ldur            x0, [fp, #-0x10]
    // 0xb25230: stur            x1, [fp, #-0x30]
    // 0xb25234: StoreField: r1->field_f = r0
    //     0xb25234: stur            w0, [x1, #0xf]
    // 0xb25238: r2 = 6
    //     0xb25238: movz            x2, #0x6
    // 0xb2523c: StoreField: r1->field_b = r2
    //     0xb2523c: stur            w2, [x1, #0xb]
    // 0xb25240: r0 = Row()
    //     0xb25240: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb25244: mov             x2, x0
    // 0xb25248: r1 = Instance_Axis
    //     0xb25248: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb2524c: stur            x2, [fp, #-0x10]
    // 0xb25250: StoreField: r2->field_f = r1
    //     0xb25250: stur            w1, [x2, #0xf]
    // 0xb25254: r3 = Instance_MainAxisAlignment
    //     0xb25254: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb25258: ldr             x3, [x3, #0xa08]
    // 0xb2525c: StoreField: r2->field_13 = r3
    //     0xb2525c: stur            w3, [x2, #0x13]
    // 0xb25260: r4 = Instance_MainAxisSize
    //     0xb25260: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb25264: ldr             x4, [x4, #0xa10]
    // 0xb25268: ArrayStore: r2[0] = r4  ; List_4
    //     0xb25268: stur            w4, [x2, #0x17]
    // 0xb2526c: r5 = Instance_CrossAxisAlignment
    //     0xb2526c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb25270: ldr             x5, [x5, #0xa18]
    // 0xb25274: StoreField: r2->field_1b = r5
    //     0xb25274: stur            w5, [x2, #0x1b]
    // 0xb25278: r6 = Instance_VerticalDirection
    //     0xb25278: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2527c: ldr             x6, [x6, #0xa20]
    // 0xb25280: StoreField: r2->field_23 = r6
    //     0xb25280: stur            w6, [x2, #0x23]
    // 0xb25284: r7 = Instance_Clip
    //     0xb25284: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb25288: ldr             x7, [x7, #0x38]
    // 0xb2528c: StoreField: r2->field_2b = r7
    //     0xb2528c: stur            w7, [x2, #0x2b]
    // 0xb25290: StoreField: r2->field_2f = rZR
    //     0xb25290: stur            xzr, [x2, #0x2f]
    // 0xb25294: ldur            x0, [fp, #-0x30]
    // 0xb25298: StoreField: r2->field_b = r0
    //     0xb25298: stur            w0, [x2, #0xb]
    // 0xb2529c: ldur            x8, [fp, #-8]
    // 0xb252a0: LoadField: r0 = r8->field_b
    //     0xb252a0: ldur            w0, [x8, #0xb]
    // 0xb252a4: DecompressPointer r0
    //     0xb252a4: add             x0, x0, HEAP, lsl #32
    // 0xb252a8: cmp             w0, NULL
    // 0xb252ac: b.eq            #0xb26824
    // 0xb252b0: LoadField: r9 = r0->field_b
    //     0xb252b0: ldur            w9, [x0, #0xb]
    // 0xb252b4: DecompressPointer r9
    //     0xb252b4: add             x9, x9, HEAP, lsl #32
    // 0xb252b8: cmp             w9, NULL
    // 0xb252bc: b.ne            #0xb252c8
    // 0xb252c0: r0 = Null
    //     0xb252c0: mov             x0, NULL
    // 0xb252c4: b               #0xb252fc
    // 0xb252c8: LoadField: r0 = r9->field_f
    //     0xb252c8: ldur            w0, [x9, #0xf]
    // 0xb252cc: DecompressPointer r0
    //     0xb252cc: add             x0, x0, HEAP, lsl #32
    // 0xb252d0: r9 = 60
    //     0xb252d0: movz            x9, #0x3c
    // 0xb252d4: branchIfSmi(r0, 0xb252e0)
    //     0xb252d4: tbz             w0, #0, #0xb252e0
    // 0xb252d8: r9 = LoadClassIdInstr(r0)
    //     0xb252d8: ldur            x9, [x0, #-1]
    //     0xb252dc: ubfx            x9, x9, #0xc, #0x14
    // 0xb252e0: str             x0, [SP]
    // 0xb252e4: mov             x0, x9
    // 0xb252e8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb252e8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb252ec: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb252ec: movz            x17, #0x2700
    //     0xb252f0: add             lr, x0, x17
    //     0xb252f4: ldr             lr, [x21, lr, lsl #3]
    //     0xb252f8: blr             lr
    // 0xb252fc: cmp             w0, NULL
    // 0xb25300: b.ne            #0xb2530c
    // 0xb25304: r1 = ""
    //     0xb25304: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25308: b               #0xb25310
    // 0xb2530c: mov             x1, x0
    // 0xb25310: r0 = parse()
    //     0xb25310: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb25314: mov             v1.16b, v0.16b
    // 0xb25318: d0 = 4.000000
    //     0xb25318: fmov            d0, #4.00000000
    // 0xb2531c: fcmp            d1, d0
    // 0xb25320: b.lt            #0xb25334
    // 0xb25324: r1 = Instance_Color
    //     0xb25324: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb25328: ldr             x1, [x1, #0x858]
    // 0xb2532c: d0 = 2.000000
    //     0xb2532c: fmov            d0, #2.00000000
    // 0xb25330: b               #0xb25474
    // 0xb25334: ldur            x1, [fp, #-8]
    // 0xb25338: LoadField: r0 = r1->field_b
    //     0xb25338: ldur            w0, [x1, #0xb]
    // 0xb2533c: DecompressPointer r0
    //     0xb2533c: add             x0, x0, HEAP, lsl #32
    // 0xb25340: cmp             w0, NULL
    // 0xb25344: b.eq            #0xb26828
    // 0xb25348: LoadField: r2 = r0->field_b
    //     0xb25348: ldur            w2, [x0, #0xb]
    // 0xb2534c: DecompressPointer r2
    //     0xb2534c: add             x2, x2, HEAP, lsl #32
    // 0xb25350: cmp             w2, NULL
    // 0xb25354: b.ne            #0xb25360
    // 0xb25358: r0 = Null
    //     0xb25358: mov             x0, NULL
    // 0xb2535c: b               #0xb25394
    // 0xb25360: LoadField: r0 = r2->field_f
    //     0xb25360: ldur            w0, [x2, #0xf]
    // 0xb25364: DecompressPointer r0
    //     0xb25364: add             x0, x0, HEAP, lsl #32
    // 0xb25368: r2 = 60
    //     0xb25368: movz            x2, #0x3c
    // 0xb2536c: branchIfSmi(r0, 0xb25378)
    //     0xb2536c: tbz             w0, #0, #0xb25378
    // 0xb25370: r2 = LoadClassIdInstr(r0)
    //     0xb25370: ldur            x2, [x0, #-1]
    //     0xb25374: ubfx            x2, x2, #0xc, #0x14
    // 0xb25378: str             x0, [SP]
    // 0xb2537c: mov             x0, x2
    // 0xb25380: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb25380: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb25384: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb25384: movz            x17, #0x2700
    //     0xb25388: add             lr, x0, x17
    //     0xb2538c: ldr             lr, [x21, lr, lsl #3]
    //     0xb25390: blr             lr
    // 0xb25394: cmp             w0, NULL
    // 0xb25398: b.ne            #0xb253a4
    // 0xb2539c: r1 = ""
    //     0xb2539c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb253a0: b               #0xb253a8
    // 0xb253a4: mov             x1, x0
    // 0xb253a8: r0 = parse()
    //     0xb253a8: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb253ac: d1 = 3.500000
    //     0xb253ac: fmov            d1, #3.50000000
    // 0xb253b0: fcmp            d0, d1
    // 0xb253b4: b.lt            #0xb253d4
    // 0xb253b8: r1 = Instance_Color
    //     0xb253b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb253bc: ldr             x1, [x1, #0x858]
    // 0xb253c0: d0 = 0.700000
    //     0xb253c0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb253c4: ldr             d0, [x17, #0xf48]
    // 0xb253c8: r0 = withOpacity()
    //     0xb253c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb253cc: d0 = 2.000000
    //     0xb253cc: fmov            d0, #2.00000000
    // 0xb253d0: b               #0xb25470
    // 0xb253d4: ldur            x1, [fp, #-8]
    // 0xb253d8: LoadField: r0 = r1->field_b
    //     0xb253d8: ldur            w0, [x1, #0xb]
    // 0xb253dc: DecompressPointer r0
    //     0xb253dc: add             x0, x0, HEAP, lsl #32
    // 0xb253e0: cmp             w0, NULL
    // 0xb253e4: b.eq            #0xb2682c
    // 0xb253e8: LoadField: r2 = r0->field_b
    //     0xb253e8: ldur            w2, [x0, #0xb]
    // 0xb253ec: DecompressPointer r2
    //     0xb253ec: add             x2, x2, HEAP, lsl #32
    // 0xb253f0: cmp             w2, NULL
    // 0xb253f4: b.ne            #0xb25400
    // 0xb253f8: r0 = Null
    //     0xb253f8: mov             x0, NULL
    // 0xb253fc: b               #0xb25434
    // 0xb25400: LoadField: r0 = r2->field_f
    //     0xb25400: ldur            w0, [x2, #0xf]
    // 0xb25404: DecompressPointer r0
    //     0xb25404: add             x0, x0, HEAP, lsl #32
    // 0xb25408: r2 = 60
    //     0xb25408: movz            x2, #0x3c
    // 0xb2540c: branchIfSmi(r0, 0xb25418)
    //     0xb2540c: tbz             w0, #0, #0xb25418
    // 0xb25410: r2 = LoadClassIdInstr(r0)
    //     0xb25410: ldur            x2, [x0, #-1]
    //     0xb25414: ubfx            x2, x2, #0xc, #0x14
    // 0xb25418: str             x0, [SP]
    // 0xb2541c: mov             x0, x2
    // 0xb25420: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb25420: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb25424: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb25424: movz            x17, #0x2700
    //     0xb25428: add             lr, x0, x17
    //     0xb2542c: ldr             lr, [x21, lr, lsl #3]
    //     0xb25430: blr             lr
    // 0xb25434: cmp             w0, NULL
    // 0xb25438: b.ne            #0xb25444
    // 0xb2543c: r1 = ""
    //     0xb2543c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25440: b               #0xb25448
    // 0xb25444: mov             x1, x0
    // 0xb25448: r0 = parse()
    //     0xb25448: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb2544c: mov             v1.16b, v0.16b
    // 0xb25450: d0 = 2.000000
    //     0xb25450: fmov            d0, #2.00000000
    // 0xb25454: fcmp            d1, d0
    // 0xb25458: b.lt            #0xb25468
    // 0xb2545c: r0 = Instance_Color
    //     0xb2545c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb25460: ldr             x0, [x0, #0x860]
    // 0xb25464: b               #0xb25470
    // 0xb25468: r0 = Instance_Color
    //     0xb25468: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb2546c: ldr             x0, [x0, #0x50]
    // 0xb25470: mov             x1, x0
    // 0xb25474: ldur            x0, [fp, #-8]
    // 0xb25478: stur            x1, [fp, #-0x30]
    // 0xb2547c: r0 = ColorFilter()
    //     0xb2547c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb25480: mov             x1, x0
    // 0xb25484: ldur            x0, [fp, #-0x30]
    // 0xb25488: stur            x1, [fp, #-0x38]
    // 0xb2548c: StoreField: r1->field_7 = r0
    //     0xb2548c: stur            w0, [x1, #7]
    // 0xb25490: r0 = Instance_BlendMode
    //     0xb25490: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb25494: ldr             x0, [x0, #0xb30]
    // 0xb25498: StoreField: r1->field_b = r0
    //     0xb25498: stur            w0, [x1, #0xb]
    // 0xb2549c: r2 = 1
    //     0xb2549c: movz            x2, #0x1
    // 0xb254a0: StoreField: r1->field_13 = r2
    //     0xb254a0: stur            x2, [x1, #0x13]
    // 0xb254a4: r0 = SvgPicture()
    //     0xb254a4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb254a8: stur            x0, [fp, #-0x30]
    // 0xb254ac: ldur            x16, [fp, #-0x38]
    // 0xb254b0: str             x16, [SP]
    // 0xb254b4: mov             x1, x0
    // 0xb254b8: r2 = "assets/images/green_star.svg"
    //     0xb254b8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb254bc: ldr             x2, [x2, #0x9a0]
    // 0xb254c0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb254c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb254c4: ldr             x4, [x4, #0xa38]
    // 0xb254c8: r0 = SvgPicture.asset()
    //     0xb254c8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb254cc: ldur            x1, [fp, #-8]
    // 0xb254d0: LoadField: r0 = r1->field_b
    //     0xb254d0: ldur            w0, [x1, #0xb]
    // 0xb254d4: DecompressPointer r0
    //     0xb254d4: add             x0, x0, HEAP, lsl #32
    // 0xb254d8: cmp             w0, NULL
    // 0xb254dc: b.eq            #0xb26830
    // 0xb254e0: LoadField: r2 = r0->field_b
    //     0xb254e0: ldur            w2, [x0, #0xb]
    // 0xb254e4: DecompressPointer r2
    //     0xb254e4: add             x2, x2, HEAP, lsl #32
    // 0xb254e8: cmp             w2, NULL
    // 0xb254ec: b.ne            #0xb254f8
    // 0xb254f0: r0 = Null
    //     0xb254f0: mov             x0, NULL
    // 0xb254f4: b               #0xb2552c
    // 0xb254f8: LoadField: r0 = r2->field_f
    //     0xb254f8: ldur            w0, [x2, #0xf]
    // 0xb254fc: DecompressPointer r0
    //     0xb254fc: add             x0, x0, HEAP, lsl #32
    // 0xb25500: r2 = 60
    //     0xb25500: movz            x2, #0x3c
    // 0xb25504: branchIfSmi(r0, 0xb25510)
    //     0xb25504: tbz             w0, #0, #0xb25510
    // 0xb25508: r2 = LoadClassIdInstr(r0)
    //     0xb25508: ldur            x2, [x0, #-1]
    //     0xb2550c: ubfx            x2, x2, #0xc, #0x14
    // 0xb25510: str             x0, [SP]
    // 0xb25514: mov             x0, x2
    // 0xb25518: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb25518: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb2551c: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb2551c: movz            x17, #0x2700
    //     0xb25520: add             lr, x0, x17
    //     0xb25524: ldr             lr, [x21, lr, lsl #3]
    //     0xb25528: blr             lr
    // 0xb2552c: cmp             w0, NULL
    // 0xb25530: b.ne            #0xb2553c
    // 0xb25534: r5 = ""
    //     0xb25534: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25538: b               #0xb25540
    // 0xb2553c: mov             x5, x0
    // 0xb25540: ldur            x3, [fp, #-0x20]
    // 0xb25544: ldur            x2, [fp, #-0x10]
    // 0xb25548: ldur            x0, [fp, #-0x30]
    // 0xb2554c: ldur            d0, [fp, #-0x58]
    // 0xb25550: ldur            x4, [fp, #-0x18]
    // 0xb25554: stur            x5, [fp, #-0x38]
    // 0xb25558: LoadField: r1 = r3->field_13
    //     0xb25558: ldur            w1, [x3, #0x13]
    // 0xb2555c: DecompressPointer r1
    //     0xb2555c: add             x1, x1, HEAP, lsl #32
    // 0xb25560: r0 = of()
    //     0xb25560: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb25564: LoadField: r1 = r0->field_87
    //     0xb25564: ldur            w1, [x0, #0x87]
    // 0xb25568: DecompressPointer r1
    //     0xb25568: add             x1, x1, HEAP, lsl #32
    // 0xb2556c: LoadField: r0 = r1->field_7
    //     0xb2556c: ldur            w0, [x1, #7]
    // 0xb25570: DecompressPointer r0
    //     0xb25570: add             x0, x0, HEAP, lsl #32
    // 0xb25574: r16 = 12.000000
    //     0xb25574: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb25578: ldr             x16, [x16, #0x9e8]
    // 0xb2557c: r30 = Instance_Color
    //     0xb2557c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb25580: stp             lr, x16, [SP]
    // 0xb25584: mov             x1, x0
    // 0xb25588: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb25588: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb2558c: ldr             x4, [x4, #0xaa0]
    // 0xb25590: r0 = copyWith()
    //     0xb25590: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb25594: stur            x0, [fp, #-0x48]
    // 0xb25598: r0 = Text()
    //     0xb25598: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb2559c: mov             x3, x0
    // 0xb255a0: ldur            x0, [fp, #-0x38]
    // 0xb255a4: stur            x3, [fp, #-0x50]
    // 0xb255a8: StoreField: r3->field_b = r0
    //     0xb255a8: stur            w0, [x3, #0xb]
    // 0xb255ac: ldur            x0, [fp, #-0x48]
    // 0xb255b0: StoreField: r3->field_13 = r0
    //     0xb255b0: stur            w0, [x3, #0x13]
    // 0xb255b4: r1 = Null
    //     0xb255b4: mov             x1, NULL
    // 0xb255b8: r2 = 6
    //     0xb255b8: movz            x2, #0x6
    // 0xb255bc: r0 = AllocateArray()
    //     0xb255bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb255c0: mov             x2, x0
    // 0xb255c4: ldur            x0, [fp, #-0x30]
    // 0xb255c8: stur            x2, [fp, #-0x38]
    // 0xb255cc: StoreField: r2->field_f = r0
    //     0xb255cc: stur            w0, [x2, #0xf]
    // 0xb255d0: r16 = Instance_SizedBox
    //     0xb255d0: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb255d4: ldr             x16, [x16, #0xe98]
    // 0xb255d8: StoreField: r2->field_13 = r16
    //     0xb255d8: stur            w16, [x2, #0x13]
    // 0xb255dc: ldur            x0, [fp, #-0x50]
    // 0xb255e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb255e0: stur            w0, [x2, #0x17]
    // 0xb255e4: r1 = <Widget>
    //     0xb255e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb255e8: r0 = AllocateGrowableArray()
    //     0xb255e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb255ec: mov             x1, x0
    // 0xb255f0: ldur            x0, [fp, #-0x38]
    // 0xb255f4: stur            x1, [fp, #-0x30]
    // 0xb255f8: StoreField: r1->field_f = r0
    //     0xb255f8: stur            w0, [x1, #0xf]
    // 0xb255fc: r2 = 6
    //     0xb255fc: movz            x2, #0x6
    // 0xb25600: StoreField: r1->field_b = r2
    //     0xb25600: stur            w2, [x1, #0xb]
    // 0xb25604: r0 = Row()
    //     0xb25604: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb25608: mov             x1, x0
    // 0xb2560c: r0 = Instance_Axis
    //     0xb2560c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb25610: stur            x1, [fp, #-0x38]
    // 0xb25614: StoreField: r1->field_f = r0
    //     0xb25614: stur            w0, [x1, #0xf]
    // 0xb25618: r2 = Instance_MainAxisAlignment
    //     0xb25618: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb2561c: ldr             x2, [x2, #0xa08]
    // 0xb25620: StoreField: r1->field_13 = r2
    //     0xb25620: stur            w2, [x1, #0x13]
    // 0xb25624: r3 = Instance_MainAxisSize
    //     0xb25624: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb25628: ldr             x3, [x3, #0xa10]
    // 0xb2562c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb2562c: stur            w3, [x1, #0x17]
    // 0xb25630: r4 = Instance_CrossAxisAlignment
    //     0xb25630: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb25634: ldr             x4, [x4, #0xa18]
    // 0xb25638: StoreField: r1->field_1b = r4
    //     0xb25638: stur            w4, [x1, #0x1b]
    // 0xb2563c: r5 = Instance_VerticalDirection
    //     0xb2563c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb25640: ldr             x5, [x5, #0xa20]
    // 0xb25644: StoreField: r1->field_23 = r5
    //     0xb25644: stur            w5, [x1, #0x23]
    // 0xb25648: r6 = Instance_Clip
    //     0xb25648: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2564c: ldr             x6, [x6, #0x38]
    // 0xb25650: StoreField: r1->field_2b = r6
    //     0xb25650: stur            w6, [x1, #0x2b]
    // 0xb25654: StoreField: r1->field_2f = rZR
    //     0xb25654: stur            xzr, [x1, #0x2f]
    // 0xb25658: ldur            x7, [fp, #-0x30]
    // 0xb2565c: StoreField: r1->field_b = r7
    //     0xb2565c: stur            w7, [x1, #0xb]
    // 0xb25660: r0 = Align()
    //     0xb25660: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb25664: mov             x3, x0
    // 0xb25668: r0 = Instance_Alignment
    //     0xb25668: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb2566c: ldr             x0, [x0, #0xa78]
    // 0xb25670: stur            x3, [fp, #-0x30]
    // 0xb25674: StoreField: r3->field_f = r0
    //     0xb25674: stur            w0, [x3, #0xf]
    // 0xb25678: ldur            x1, [fp, #-0x38]
    // 0xb2567c: StoreField: r3->field_b = r1
    //     0xb2567c: stur            w1, [x3, #0xb]
    // 0xb25680: r1 = Null
    //     0xb25680: mov             x1, NULL
    // 0xb25684: r2 = 4
    //     0xb25684: movz            x2, #0x4
    // 0xb25688: r0 = AllocateArray()
    //     0xb25688: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb2568c: mov             x2, x0
    // 0xb25690: ldur            x0, [fp, #-0x10]
    // 0xb25694: stur            x2, [fp, #-0x38]
    // 0xb25698: StoreField: r2->field_f = r0
    //     0xb25698: stur            w0, [x2, #0xf]
    // 0xb2569c: ldur            x0, [fp, #-0x30]
    // 0xb256a0: StoreField: r2->field_13 = r0
    //     0xb256a0: stur            w0, [x2, #0x13]
    // 0xb256a4: r1 = <Widget>
    //     0xb256a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb256a8: r0 = AllocateGrowableArray()
    //     0xb256a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb256ac: mov             x1, x0
    // 0xb256b0: ldur            x0, [fp, #-0x38]
    // 0xb256b4: stur            x1, [fp, #-0x10]
    // 0xb256b8: StoreField: r1->field_f = r0
    //     0xb256b8: stur            w0, [x1, #0xf]
    // 0xb256bc: r2 = 4
    //     0xb256bc: movz            x2, #0x4
    // 0xb256c0: StoreField: r1->field_b = r2
    //     0xb256c0: stur            w2, [x1, #0xb]
    // 0xb256c4: r0 = Row()
    //     0xb256c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb256c8: mov             x1, x0
    // 0xb256cc: r0 = Instance_Axis
    //     0xb256cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb256d0: stur            x1, [fp, #-0x30]
    // 0xb256d4: StoreField: r1->field_f = r0
    //     0xb256d4: stur            w0, [x1, #0xf]
    // 0xb256d8: r2 = Instance_MainAxisAlignment
    //     0xb256d8: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb256dc: ldr             x2, [x2, #0xa8]
    // 0xb256e0: StoreField: r1->field_13 = r2
    //     0xb256e0: stur            w2, [x1, #0x13]
    // 0xb256e4: r3 = Instance_MainAxisSize
    //     0xb256e4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb256e8: ldr             x3, [x3, #0xa10]
    // 0xb256ec: ArrayStore: r1[0] = r3  ; List_4
    //     0xb256ec: stur            w3, [x1, #0x17]
    // 0xb256f0: r4 = Instance_CrossAxisAlignment
    //     0xb256f0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb256f4: ldr             x4, [x4, #0xa18]
    // 0xb256f8: StoreField: r1->field_1b = r4
    //     0xb256f8: stur            w4, [x1, #0x1b]
    // 0xb256fc: r5 = Instance_VerticalDirection
    //     0xb256fc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb25700: ldr             x5, [x5, #0xa20]
    // 0xb25704: StoreField: r1->field_23 = r5
    //     0xb25704: stur            w5, [x1, #0x23]
    // 0xb25708: r6 = Instance_Clip
    //     0xb25708: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb2570c: ldr             x6, [x6, #0x38]
    // 0xb25710: StoreField: r1->field_2b = r6
    //     0xb25710: stur            w6, [x1, #0x2b]
    // 0xb25714: StoreField: r1->field_2f = rZR
    //     0xb25714: stur            xzr, [x1, #0x2f]
    // 0xb25718: ldur            x7, [fp, #-0x10]
    // 0xb2571c: StoreField: r1->field_b = r7
    //     0xb2571c: stur            w7, [x1, #0xb]
    // 0xb25720: r0 = Padding()
    //     0xb25720: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb25724: mov             x1, x0
    // 0xb25728: r0 = Instance_EdgeInsets
    //     0xb25728: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xb2572c: ldr             x0, [x0, #0xd0]
    // 0xb25730: stur            x1, [fp, #-0x10]
    // 0xb25734: StoreField: r1->field_f = r0
    //     0xb25734: stur            w0, [x1, #0xf]
    // 0xb25738: ldur            x0, [fp, #-0x30]
    // 0xb2573c: StoreField: r1->field_b = r0
    //     0xb2573c: stur            w0, [x1, #0xb]
    // 0xb25740: r0 = Container()
    //     0xb25740: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb25744: stur            x0, [fp, #-0x30]
    // 0xb25748: ldur            x16, [fp, #-0x28]
    // 0xb2574c: ldur            lr, [fp, #-0x10]
    // 0xb25750: stp             lr, x16, [SP]
    // 0xb25754: mov             x1, x0
    // 0xb25758: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb25758: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb2575c: ldr             x4, [x4, #0x88]
    // 0xb25760: r0 = Container()
    //     0xb25760: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb25764: ldur            d0, [fp, #-0x58]
    // 0xb25768: r0 = inline_Allocate_Double()
    //     0xb25768: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb2576c: add             x0, x0, #0x10
    //     0xb25770: cmp             x1, x0
    //     0xb25774: b.ls            #0xb26834
    //     0xb25778: str             x0, [THR, #0x50]  ; THR::top
    //     0xb2577c: sub             x0, x0, #0xf
    //     0xb25780: movz            x1, #0xe15c
    //     0xb25784: movk            x1, #0x3, lsl #16
    //     0xb25788: stur            x1, [x0, #-1]
    // 0xb2578c: StoreField: r0->field_7 = d0
    //     0xb2578c: stur            d0, [x0, #7]
    // 0xb25790: stur            x0, [fp, #-0x10]
    // 0xb25794: r0 = SizedBox()
    //     0xb25794: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb25798: mov             x2, x0
    // 0xb2579c: ldur            x0, [fp, #-0x10]
    // 0xb257a0: stur            x2, [fp, #-0x28]
    // 0xb257a4: StoreField: r2->field_f = r0
    //     0xb257a4: stur            w0, [x2, #0xf]
    // 0xb257a8: ldur            x0, [fp, #-0x30]
    // 0xb257ac: StoreField: r2->field_b = r0
    //     0xb257ac: stur            w0, [x2, #0xb]
    // 0xb257b0: r1 = <StackParentData>
    //     0xb257b0: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xb257b4: ldr             x1, [x1, #0x8e0]
    // 0xb257b8: r0 = Positioned()
    //     0xb257b8: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xb257bc: mov             x2, x0
    // 0xb257c0: r0 = 0.000000
    //     0xb257c0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb257c4: stur            x2, [fp, #-0x10]
    // 0xb257c8: StoreField: r2->field_1f = r0
    //     0xb257c8: stur            w0, [x2, #0x1f]
    // 0xb257cc: ldur            x0, [fp, #-0x28]
    // 0xb257d0: StoreField: r2->field_b = r0
    //     0xb257d0: stur            w0, [x2, #0xb]
    // 0xb257d4: ldur            x0, [fp, #-0x18]
    // 0xb257d8: LoadField: r1 = r0->field_b
    //     0xb257d8: ldur            w1, [x0, #0xb]
    // 0xb257dc: LoadField: r3 = r0->field_f
    //     0xb257dc: ldur            w3, [x0, #0xf]
    // 0xb257e0: DecompressPointer r3
    //     0xb257e0: add             x3, x3, HEAP, lsl #32
    // 0xb257e4: LoadField: r4 = r3->field_b
    //     0xb257e4: ldur            w4, [x3, #0xb]
    // 0xb257e8: r3 = LoadInt32Instr(r1)
    //     0xb257e8: sbfx            x3, x1, #1, #0x1f
    // 0xb257ec: stur            x3, [fp, #-0x40]
    // 0xb257f0: r1 = LoadInt32Instr(r4)
    //     0xb257f0: sbfx            x1, x4, #1, #0x1f
    // 0xb257f4: cmp             x3, x1
    // 0xb257f8: b.ne            #0xb25804
    // 0xb257fc: mov             x1, x0
    // 0xb25800: r0 = _growToNextCapacity()
    //     0xb25800: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb25804: ldur            x2, [fp, #-0x18]
    // 0xb25808: ldur            x3, [fp, #-0x40]
    // 0xb2580c: add             x0, x3, #1
    // 0xb25810: lsl             x1, x0, #1
    // 0xb25814: StoreField: r2->field_b = r1
    //     0xb25814: stur            w1, [x2, #0xb]
    // 0xb25818: LoadField: r1 = r2->field_f
    //     0xb25818: ldur            w1, [x2, #0xf]
    // 0xb2581c: DecompressPointer r1
    //     0xb2581c: add             x1, x1, HEAP, lsl #32
    // 0xb25820: ldur            x0, [fp, #-0x10]
    // 0xb25824: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb25824: add             x25, x1, x3, lsl #2
    //     0xb25828: add             x25, x25, #0xf
    //     0xb2582c: str             w0, [x25]
    //     0xb25830: tbz             w0, #0, #0xb2584c
    //     0xb25834: ldurb           w16, [x1, #-1]
    //     0xb25838: ldurb           w17, [x0, #-1]
    //     0xb2583c: and             x16, x17, x16, lsr #2
    //     0xb25840: tst             x16, HEAP, lsr #32
    //     0xb25844: b.eq            #0xb2584c
    //     0xb25848: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb2584c: ldur            x0, [fp, #-8]
    // 0xb25850: r0 = Stack()
    //     0xb25850: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb25854: mov             x1, x0
    // 0xb25858: r0 = Instance_Alignment
    //     0xb25858: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb2585c: ldr             x0, [x0, #0xce0]
    // 0xb25860: stur            x1, [fp, #-0x10]
    // 0xb25864: StoreField: r1->field_f = r0
    //     0xb25864: stur            w0, [x1, #0xf]
    // 0xb25868: r0 = Instance_StackFit
    //     0xb25868: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb2586c: ldr             x0, [x0, #0xfa8]
    // 0xb25870: ArrayStore: r1[0] = r0  ; List_4
    //     0xb25870: stur            w0, [x1, #0x17]
    // 0xb25874: r0 = Instance_Clip
    //     0xb25874: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb25878: ldr             x0, [x0, #0x7e0]
    // 0xb2587c: StoreField: r1->field_1b = r0
    //     0xb2587c: stur            w0, [x1, #0x1b]
    // 0xb25880: ldur            x0, [fp, #-0x18]
    // 0xb25884: StoreField: r1->field_b = r0
    //     0xb25884: stur            w0, [x1, #0xb]
    // 0xb25888: r0 = ColoredBox()
    //     0xb25888: bl              #0x8faaac  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xb2588c: mov             x2, x0
    // 0xb25890: r0 = Instance_Color
    //     0xb25890: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb25894: stur            x2, [fp, #-0x18]
    // 0xb25898: StoreField: r2->field_f = r0
    //     0xb25898: stur            w0, [x2, #0xf]
    // 0xb2589c: ldur            x1, [fp, #-0x10]
    // 0xb258a0: StoreField: r2->field_b = r1
    //     0xb258a0: stur            w1, [x2, #0xb]
    // 0xb258a4: r1 = <FlexParentData>
    //     0xb258a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb258a8: ldr             x1, [x1, #0xe00]
    // 0xb258ac: r0 = Expanded()
    //     0xb258ac: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb258b0: mov             x3, x0
    // 0xb258b4: r0 = 1
    //     0xb258b4: movz            x0, #0x1
    // 0xb258b8: stur            x3, [fp, #-0x10]
    // 0xb258bc: StoreField: r3->field_13 = r0
    //     0xb258bc: stur            x0, [x3, #0x13]
    // 0xb258c0: r1 = Instance_FlexFit
    //     0xb258c0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb258c4: ldr             x1, [x1, #0xe08]
    // 0xb258c8: StoreField: r3->field_1b = r1
    //     0xb258c8: stur            w1, [x3, #0x1b]
    // 0xb258cc: ldur            x1, [fp, #-0x18]
    // 0xb258d0: StoreField: r3->field_b = r1
    //     0xb258d0: stur            w1, [x3, #0xb]
    // 0xb258d4: r1 = <Widget>
    //     0xb258d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb258d8: r2 = 0
    //     0xb258d8: movz            x2, #0
    // 0xb258dc: r0 = _GrowableList()
    //     0xb258dc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb258e0: mov             x2, x0
    // 0xb258e4: ldur            x0, [fp, #-8]
    // 0xb258e8: stur            x2, [fp, #-0x18]
    // 0xb258ec: LoadField: r1 = r0->field_23
    //     0xb258ec: ldur            w1, [x0, #0x23]
    // 0xb258f0: DecompressPointer r1
    //     0xb258f0: add             x1, x1, HEAP, lsl #32
    // 0xb258f4: tbz             w1, #4, #0xb26274
    // 0xb258f8: r1 = Instance_Color
    //     0xb258f8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb258fc: d0 = 0.050000
    //     0xb258fc: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0xb25900: r0 = withOpacity()
    //     0xb25900: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb25904: stur            x0, [fp, #-0x28]
    // 0xb25908: r0 = BoxDecoration()
    //     0xb25908: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb2590c: mov             x1, x0
    // 0xb25910: ldur            x0, [fp, #-0x28]
    // 0xb25914: stur            x1, [fp, #-0x30]
    // 0xb25918: StoreField: r1->field_7 = r0
    //     0xb25918: stur            w0, [x1, #7]
    // 0xb2591c: r0 = Instance_BoxShape
    //     0xb2591c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xb25920: ldr             x0, [x0, #0x970]
    // 0xb25924: StoreField: r1->field_23 = r0
    //     0xb25924: stur            w0, [x1, #0x23]
    // 0xb25928: ldur            x0, [fp, #-8]
    // 0xb2592c: LoadField: r2 = r0->field_b
    //     0xb2592c: ldur            w2, [x0, #0xb]
    // 0xb25930: DecompressPointer r2
    //     0xb25930: add             x2, x2, HEAP, lsl #32
    // 0xb25934: cmp             w2, NULL
    // 0xb25938: b.eq            #0xb26844
    // 0xb2593c: LoadField: r3 = r2->field_b
    //     0xb2593c: ldur            w3, [x2, #0xb]
    // 0xb25940: DecompressPointer r3
    //     0xb25940: add             x3, x3, HEAP, lsl #32
    // 0xb25944: cmp             w3, NULL
    // 0xb25948: b.ne            #0xb25954
    // 0xb2594c: r0 = Null
    //     0xb2594c: mov             x0, NULL
    // 0xb25950: b               #0xb25990
    // 0xb25954: LoadField: r2 = r3->field_7
    //     0xb25954: ldur            w2, [x3, #7]
    // 0xb25958: DecompressPointer r2
    //     0xb25958: add             x2, x2, HEAP, lsl #32
    // 0xb2595c: cmp             w2, NULL
    // 0xb25960: b.ne            #0xb2596c
    // 0xb25964: r0 = Null
    //     0xb25964: mov             x0, NULL
    // 0xb25968: b               #0xb25990
    // 0xb2596c: stp             xzr, x2, [SP]
    // 0xb25970: r0 = []()
    //     0xb25970: bl              #0x61e00c  ; [dart:core] _StringBase::[]
    // 0xb25974: r1 = LoadClassIdInstr(r0)
    //     0xb25974: ldur            x1, [x0, #-1]
    //     0xb25978: ubfx            x1, x1, #0xc, #0x14
    // 0xb2597c: str             x0, [SP]
    // 0xb25980: mov             x0, x1
    // 0xb25984: r0 = GDT[cid_x0 + -0x1000]()
    //     0xb25984: sub             lr, x0, #1, lsl #12
    //     0xb25988: ldr             lr, [x21, lr, lsl #3]
    //     0xb2598c: blr             lr
    // 0xb25990: cmp             w0, NULL
    // 0xb25994: b.ne            #0xb259a0
    // 0xb25998: r3 = ""
    //     0xb25998: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2599c: b               #0xb259a4
    // 0xb259a0: mov             x3, x0
    // 0xb259a4: ldur            x0, [fp, #-8]
    // 0xb259a8: ldur            x2, [fp, #-0x20]
    // 0xb259ac: stur            x3, [fp, #-0x28]
    // 0xb259b0: LoadField: r1 = r2->field_13
    //     0xb259b0: ldur            w1, [x2, #0x13]
    // 0xb259b4: DecompressPointer r1
    //     0xb259b4: add             x1, x1, HEAP, lsl #32
    // 0xb259b8: r0 = of()
    //     0xb259b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb259bc: LoadField: r1 = r0->field_87
    //     0xb259bc: ldur            w1, [x0, #0x87]
    // 0xb259c0: DecompressPointer r1
    //     0xb259c0: add             x1, x1, HEAP, lsl #32
    // 0xb259c4: LoadField: r0 = r1->field_7
    //     0xb259c4: ldur            w0, [x1, #7]
    // 0xb259c8: DecompressPointer r0
    //     0xb259c8: add             x0, x0, HEAP, lsl #32
    // 0xb259cc: stur            x0, [fp, #-0x38]
    // 0xb259d0: r1 = Instance_Color
    //     0xb259d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb259d4: d0 = 0.500000
    //     0xb259d4: fmov            d0, #0.50000000
    // 0xb259d8: r0 = withOpacity()
    //     0xb259d8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb259dc: r16 = 16.000000
    //     0xb259dc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb259e0: ldr             x16, [x16, #0x188]
    // 0xb259e4: stp             x0, x16, [SP]
    // 0xb259e8: ldur            x1, [fp, #-0x38]
    // 0xb259ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb259ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb259f0: ldr             x4, [x4, #0xaa0]
    // 0xb259f4: r0 = copyWith()
    //     0xb259f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb259f8: stur            x0, [fp, #-0x38]
    // 0xb259fc: r0 = Text()
    //     0xb259fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb25a00: mov             x1, x0
    // 0xb25a04: ldur            x0, [fp, #-0x28]
    // 0xb25a08: stur            x1, [fp, #-0x48]
    // 0xb25a0c: StoreField: r1->field_b = r0
    //     0xb25a0c: stur            w0, [x1, #0xb]
    // 0xb25a10: ldur            x0, [fp, #-0x38]
    // 0xb25a14: StoreField: r1->field_13 = r0
    //     0xb25a14: stur            w0, [x1, #0x13]
    // 0xb25a18: r0 = Center()
    //     0xb25a18: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb25a1c: mov             x1, x0
    // 0xb25a20: r0 = Instance_Alignment
    //     0xb25a20: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb25a24: ldr             x0, [x0, #0xb10]
    // 0xb25a28: stur            x1, [fp, #-0x28]
    // 0xb25a2c: StoreField: r1->field_f = r0
    //     0xb25a2c: stur            w0, [x1, #0xf]
    // 0xb25a30: ldur            x0, [fp, #-0x48]
    // 0xb25a34: StoreField: r1->field_b = r0
    //     0xb25a34: stur            w0, [x1, #0xb]
    // 0xb25a38: r0 = Container()
    //     0xb25a38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb25a3c: stur            x0, [fp, #-0x38]
    // 0xb25a40: r16 = 34.000000
    //     0xb25a40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb25a44: ldr             x16, [x16, #0x978]
    // 0xb25a48: r30 = 34.000000
    //     0xb25a48: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f978] 34
    //     0xb25a4c: ldr             lr, [lr, #0x978]
    // 0xb25a50: stp             lr, x16, [SP, #0x18]
    // 0xb25a54: r16 = Instance_EdgeInsets
    //     0xb25a54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb25a58: ldr             x16, [x16, #0x980]
    // 0xb25a5c: ldur            lr, [fp, #-0x30]
    // 0xb25a60: stp             lr, x16, [SP, #8]
    // 0xb25a64: ldur            x16, [fp, #-0x28]
    // 0xb25a68: str             x16, [SP]
    // 0xb25a6c: mov             x1, x0
    // 0xb25a70: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x1, padding, 0x3, width, 0x2, null]
    //     0xb25a70: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f988] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x1, "padding", 0x3, "width", 0x2, Null]
    //     0xb25a74: ldr             x4, [x4, #0x988]
    // 0xb25a78: r0 = Container()
    //     0xb25a78: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb25a7c: ldur            x0, [fp, #-8]
    // 0xb25a80: LoadField: r1 = r0->field_b
    //     0xb25a80: ldur            w1, [x0, #0xb]
    // 0xb25a84: DecompressPointer r1
    //     0xb25a84: add             x1, x1, HEAP, lsl #32
    // 0xb25a88: cmp             w1, NULL
    // 0xb25a8c: b.eq            #0xb26848
    // 0xb25a90: LoadField: r2 = r1->field_b
    //     0xb25a90: ldur            w2, [x1, #0xb]
    // 0xb25a94: DecompressPointer r2
    //     0xb25a94: add             x2, x2, HEAP, lsl #32
    // 0xb25a98: cmp             w2, NULL
    // 0xb25a9c: b.ne            #0xb25aa8
    // 0xb25aa0: r1 = Null
    //     0xb25aa0: mov             x1, NULL
    // 0xb25aa4: b               #0xb25ab0
    // 0xb25aa8: LoadField: r1 = r2->field_7
    //     0xb25aa8: ldur            w1, [x2, #7]
    // 0xb25aac: DecompressPointer r1
    //     0xb25aac: add             x1, x1, HEAP, lsl #32
    // 0xb25ab0: cmp             w1, NULL
    // 0xb25ab4: b.ne            #0xb25ac0
    // 0xb25ab8: r3 = ""
    //     0xb25ab8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25abc: b               #0xb25ac4
    // 0xb25ac0: mov             x3, x1
    // 0xb25ac4: ldur            x2, [fp, #-0x20]
    // 0xb25ac8: stur            x3, [fp, #-0x28]
    // 0xb25acc: LoadField: r1 = r2->field_13
    //     0xb25acc: ldur            w1, [x2, #0x13]
    // 0xb25ad0: DecompressPointer r1
    //     0xb25ad0: add             x1, x1, HEAP, lsl #32
    // 0xb25ad4: r0 = of()
    //     0xb25ad4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb25ad8: LoadField: r1 = r0->field_87
    //     0xb25ad8: ldur            w1, [x0, #0x87]
    // 0xb25adc: DecompressPointer r1
    //     0xb25adc: add             x1, x1, HEAP, lsl #32
    // 0xb25ae0: LoadField: r0 = r1->field_7
    //     0xb25ae0: ldur            w0, [x1, #7]
    // 0xb25ae4: DecompressPointer r0
    //     0xb25ae4: add             x0, x0, HEAP, lsl #32
    // 0xb25ae8: r16 = 14.000000
    //     0xb25ae8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb25aec: ldr             x16, [x16, #0x1d8]
    // 0xb25af0: r30 = Instance_Color
    //     0xb25af0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb25af4: stp             lr, x16, [SP]
    // 0xb25af8: mov             x1, x0
    // 0xb25afc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb25afc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb25b00: ldr             x4, [x4, #0xaa0]
    // 0xb25b04: r0 = copyWith()
    //     0xb25b04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb25b08: stur            x0, [fp, #-0x30]
    // 0xb25b0c: r0 = Text()
    //     0xb25b0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb25b10: mov             x2, x0
    // 0xb25b14: ldur            x0, [fp, #-0x28]
    // 0xb25b18: stur            x2, [fp, #-0x48]
    // 0xb25b1c: StoreField: r2->field_b = r0
    //     0xb25b1c: stur            w0, [x2, #0xb]
    // 0xb25b20: ldur            x0, [fp, #-0x30]
    // 0xb25b24: StoreField: r2->field_13 = r0
    //     0xb25b24: stur            w0, [x2, #0x13]
    // 0xb25b28: ldur            x0, [fp, #-8]
    // 0xb25b2c: LoadField: r1 = r0->field_b
    //     0xb25b2c: ldur            w1, [x0, #0xb]
    // 0xb25b30: DecompressPointer r1
    //     0xb25b30: add             x1, x1, HEAP, lsl #32
    // 0xb25b34: cmp             w1, NULL
    // 0xb25b38: b.eq            #0xb2684c
    // 0xb25b3c: LoadField: r3 = r1->field_b
    //     0xb25b3c: ldur            w3, [x1, #0xb]
    // 0xb25b40: DecompressPointer r3
    //     0xb25b40: add             x3, x3, HEAP, lsl #32
    // 0xb25b44: cmp             w3, NULL
    // 0xb25b48: b.ne            #0xb25b54
    // 0xb25b4c: r1 = Null
    //     0xb25b4c: mov             x1, NULL
    // 0xb25b50: b               #0xb25b5c
    // 0xb25b54: LoadField: r1 = r3->field_1f
    //     0xb25b54: ldur            w1, [x3, #0x1f]
    // 0xb25b58: DecompressPointer r1
    //     0xb25b58: add             x1, x1, HEAP, lsl #32
    // 0xb25b5c: cmp             w1, NULL
    // 0xb25b60: b.ne            #0xb25b6c
    // 0xb25b64: r4 = ""
    //     0xb25b64: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25b68: b               #0xb25b70
    // 0xb25b6c: mov             x4, x1
    // 0xb25b70: ldur            x3, [fp, #-0x20]
    // 0xb25b74: stur            x4, [fp, #-0x28]
    // 0xb25b78: LoadField: r1 = r3->field_13
    //     0xb25b78: ldur            w1, [x3, #0x13]
    // 0xb25b7c: DecompressPointer r1
    //     0xb25b7c: add             x1, x1, HEAP, lsl #32
    // 0xb25b80: r0 = of()
    //     0xb25b80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb25b84: LoadField: r1 = r0->field_87
    //     0xb25b84: ldur            w1, [x0, #0x87]
    // 0xb25b88: DecompressPointer r1
    //     0xb25b88: add             x1, x1, HEAP, lsl #32
    // 0xb25b8c: LoadField: r0 = r1->field_33
    //     0xb25b8c: ldur            w0, [x1, #0x33]
    // 0xb25b90: DecompressPointer r0
    //     0xb25b90: add             x0, x0, HEAP, lsl #32
    // 0xb25b94: stur            x0, [fp, #-0x30]
    // 0xb25b98: cmp             w0, NULL
    // 0xb25b9c: b.ne            #0xb25ba8
    // 0xb25ba0: r4 = Null
    //     0xb25ba0: mov             x4, NULL
    // 0xb25ba4: b               #0xb25bd0
    // 0xb25ba8: r1 = Instance_Color
    //     0xb25ba8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb25bac: d0 = 0.500000
    //     0xb25bac: fmov            d0, #0.50000000
    // 0xb25bb0: r0 = withOpacity()
    //     0xb25bb0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb25bb4: r16 = 10.000000
    //     0xb25bb4: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb25bb8: stp             x0, x16, [SP]
    // 0xb25bbc: ldur            x1, [fp, #-0x30]
    // 0xb25bc0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb25bc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb25bc4: ldr             x4, [x4, #0xaa0]
    // 0xb25bc8: r0 = copyWith()
    //     0xb25bc8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb25bcc: mov             x4, x0
    // 0xb25bd0: ldur            x1, [fp, #-8]
    // 0xb25bd4: ldur            x3, [fp, #-0x38]
    // 0xb25bd8: ldur            x0, [fp, #-0x48]
    // 0xb25bdc: ldur            x2, [fp, #-0x28]
    // 0xb25be0: stur            x4, [fp, #-0x30]
    // 0xb25be4: r0 = Text()
    //     0xb25be4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb25be8: mov             x1, x0
    // 0xb25bec: ldur            x0, [fp, #-0x28]
    // 0xb25bf0: stur            x1, [fp, #-0x50]
    // 0xb25bf4: StoreField: r1->field_b = r0
    //     0xb25bf4: stur            w0, [x1, #0xb]
    // 0xb25bf8: ldur            x0, [fp, #-0x30]
    // 0xb25bfc: StoreField: r1->field_13 = r0
    //     0xb25bfc: stur            w0, [x1, #0x13]
    // 0xb25c00: r0 = Padding()
    //     0xb25c00: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb25c04: mov             x3, x0
    // 0xb25c08: r0 = Instance_EdgeInsets
    //     0xb25c08: add             x0, PP, #0x51, lsl #12  ; [pp+0x51e90] Obj!EdgeInsets@d58b21
    //     0xb25c0c: ldr             x0, [x0, #0xe90]
    // 0xb25c10: stur            x3, [fp, #-0x28]
    // 0xb25c14: StoreField: r3->field_f = r0
    //     0xb25c14: stur            w0, [x3, #0xf]
    // 0xb25c18: ldur            x0, [fp, #-0x50]
    // 0xb25c1c: StoreField: r3->field_b = r0
    //     0xb25c1c: stur            w0, [x3, #0xb]
    // 0xb25c20: r1 = Null
    //     0xb25c20: mov             x1, NULL
    // 0xb25c24: r2 = 4
    //     0xb25c24: movz            x2, #0x4
    // 0xb25c28: r0 = AllocateArray()
    //     0xb25c28: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb25c2c: mov             x2, x0
    // 0xb25c30: ldur            x0, [fp, #-0x48]
    // 0xb25c34: stur            x2, [fp, #-0x30]
    // 0xb25c38: StoreField: r2->field_f = r0
    //     0xb25c38: stur            w0, [x2, #0xf]
    // 0xb25c3c: ldur            x0, [fp, #-0x28]
    // 0xb25c40: StoreField: r2->field_13 = r0
    //     0xb25c40: stur            w0, [x2, #0x13]
    // 0xb25c44: r1 = <Widget>
    //     0xb25c44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb25c48: r0 = AllocateGrowableArray()
    //     0xb25c48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb25c4c: mov             x1, x0
    // 0xb25c50: ldur            x0, [fp, #-0x30]
    // 0xb25c54: stur            x1, [fp, #-0x28]
    // 0xb25c58: StoreField: r1->field_f = r0
    //     0xb25c58: stur            w0, [x1, #0xf]
    // 0xb25c5c: r2 = 4
    //     0xb25c5c: movz            x2, #0x4
    // 0xb25c60: StoreField: r1->field_b = r2
    //     0xb25c60: stur            w2, [x1, #0xb]
    // 0xb25c64: r0 = Column()
    //     0xb25c64: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb25c68: mov             x3, x0
    // 0xb25c6c: r0 = Instance_Axis
    //     0xb25c6c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb25c70: stur            x3, [fp, #-0x30]
    // 0xb25c74: StoreField: r3->field_f = r0
    //     0xb25c74: stur            w0, [x3, #0xf]
    // 0xb25c78: r4 = Instance_MainAxisAlignment
    //     0xb25c78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb25c7c: ldr             x4, [x4, #0xa08]
    // 0xb25c80: StoreField: r3->field_13 = r4
    //     0xb25c80: stur            w4, [x3, #0x13]
    // 0xb25c84: r5 = Instance_MainAxisSize
    //     0xb25c84: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb25c88: ldr             x5, [x5, #0xa10]
    // 0xb25c8c: ArrayStore: r3[0] = r5  ; List_4
    //     0xb25c8c: stur            w5, [x3, #0x17]
    // 0xb25c90: r1 = Instance_CrossAxisAlignment
    //     0xb25c90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb25c94: ldr             x1, [x1, #0x890]
    // 0xb25c98: StoreField: r3->field_1b = r1
    //     0xb25c98: stur            w1, [x3, #0x1b]
    // 0xb25c9c: r6 = Instance_VerticalDirection
    //     0xb25c9c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb25ca0: ldr             x6, [x6, #0xa20]
    // 0xb25ca4: StoreField: r3->field_23 = r6
    //     0xb25ca4: stur            w6, [x3, #0x23]
    // 0xb25ca8: r7 = Instance_Clip
    //     0xb25ca8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb25cac: ldr             x7, [x7, #0x38]
    // 0xb25cb0: StoreField: r3->field_2b = r7
    //     0xb25cb0: stur            w7, [x3, #0x2b]
    // 0xb25cb4: StoreField: r3->field_2f = rZR
    //     0xb25cb4: stur            xzr, [x3, #0x2f]
    // 0xb25cb8: ldur            x1, [fp, #-0x28]
    // 0xb25cbc: StoreField: r3->field_b = r1
    //     0xb25cbc: stur            w1, [x3, #0xb]
    // 0xb25cc0: r1 = Null
    //     0xb25cc0: mov             x1, NULL
    // 0xb25cc4: r2 = 6
    //     0xb25cc4: movz            x2, #0x6
    // 0xb25cc8: r0 = AllocateArray()
    //     0xb25cc8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb25ccc: mov             x2, x0
    // 0xb25cd0: ldur            x0, [fp, #-0x38]
    // 0xb25cd4: stur            x2, [fp, #-0x28]
    // 0xb25cd8: StoreField: r2->field_f = r0
    //     0xb25cd8: stur            w0, [x2, #0xf]
    // 0xb25cdc: r16 = Instance_SizedBox
    //     0xb25cdc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xb25ce0: ldr             x16, [x16, #0x998]
    // 0xb25ce4: StoreField: r2->field_13 = r16
    //     0xb25ce4: stur            w16, [x2, #0x13]
    // 0xb25ce8: ldur            x0, [fp, #-0x30]
    // 0xb25cec: ArrayStore: r2[0] = r0  ; List_4
    //     0xb25cec: stur            w0, [x2, #0x17]
    // 0xb25cf0: r1 = <Widget>
    //     0xb25cf0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb25cf4: r0 = AllocateGrowableArray()
    //     0xb25cf4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb25cf8: mov             x1, x0
    // 0xb25cfc: ldur            x0, [fp, #-0x28]
    // 0xb25d00: stur            x1, [fp, #-0x30]
    // 0xb25d04: StoreField: r1->field_f = r0
    //     0xb25d04: stur            w0, [x1, #0xf]
    // 0xb25d08: r2 = 6
    //     0xb25d08: movz            x2, #0x6
    // 0xb25d0c: StoreField: r1->field_b = r2
    //     0xb25d0c: stur            w2, [x1, #0xb]
    // 0xb25d10: r0 = Row()
    //     0xb25d10: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb25d14: mov             x2, x0
    // 0xb25d18: r1 = Instance_Axis
    //     0xb25d18: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb25d1c: stur            x2, [fp, #-0x28]
    // 0xb25d20: StoreField: r2->field_f = r1
    //     0xb25d20: stur            w1, [x2, #0xf]
    // 0xb25d24: r3 = Instance_MainAxisAlignment
    //     0xb25d24: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb25d28: ldr             x3, [x3, #0xa08]
    // 0xb25d2c: StoreField: r2->field_13 = r3
    //     0xb25d2c: stur            w3, [x2, #0x13]
    // 0xb25d30: r4 = Instance_MainAxisSize
    //     0xb25d30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb25d34: ldr             x4, [x4, #0xa10]
    // 0xb25d38: ArrayStore: r2[0] = r4  ; List_4
    //     0xb25d38: stur            w4, [x2, #0x17]
    // 0xb25d3c: r5 = Instance_CrossAxisAlignment
    //     0xb25d3c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb25d40: ldr             x5, [x5, #0xa18]
    // 0xb25d44: StoreField: r2->field_1b = r5
    //     0xb25d44: stur            w5, [x2, #0x1b]
    // 0xb25d48: r6 = Instance_VerticalDirection
    //     0xb25d48: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb25d4c: ldr             x6, [x6, #0xa20]
    // 0xb25d50: StoreField: r2->field_23 = r6
    //     0xb25d50: stur            w6, [x2, #0x23]
    // 0xb25d54: r7 = Instance_Clip
    //     0xb25d54: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb25d58: ldr             x7, [x7, #0x38]
    // 0xb25d5c: StoreField: r2->field_2b = r7
    //     0xb25d5c: stur            w7, [x2, #0x2b]
    // 0xb25d60: StoreField: r2->field_2f = rZR
    //     0xb25d60: stur            xzr, [x2, #0x2f]
    // 0xb25d64: ldur            x0, [fp, #-0x30]
    // 0xb25d68: StoreField: r2->field_b = r0
    //     0xb25d68: stur            w0, [x2, #0xb]
    // 0xb25d6c: ldur            x8, [fp, #-8]
    // 0xb25d70: LoadField: r0 = r8->field_b
    //     0xb25d70: ldur            w0, [x8, #0xb]
    // 0xb25d74: DecompressPointer r0
    //     0xb25d74: add             x0, x0, HEAP, lsl #32
    // 0xb25d78: cmp             w0, NULL
    // 0xb25d7c: b.eq            #0xb26850
    // 0xb25d80: LoadField: r9 = r0->field_b
    //     0xb25d80: ldur            w9, [x0, #0xb]
    // 0xb25d84: DecompressPointer r9
    //     0xb25d84: add             x9, x9, HEAP, lsl #32
    // 0xb25d88: cmp             w9, NULL
    // 0xb25d8c: b.ne            #0xb25d98
    // 0xb25d90: r0 = Null
    //     0xb25d90: mov             x0, NULL
    // 0xb25d94: b               #0xb25dcc
    // 0xb25d98: LoadField: r0 = r9->field_f
    //     0xb25d98: ldur            w0, [x9, #0xf]
    // 0xb25d9c: DecompressPointer r0
    //     0xb25d9c: add             x0, x0, HEAP, lsl #32
    // 0xb25da0: r9 = 60
    //     0xb25da0: movz            x9, #0x3c
    // 0xb25da4: branchIfSmi(r0, 0xb25db0)
    //     0xb25da4: tbz             w0, #0, #0xb25db0
    // 0xb25da8: r9 = LoadClassIdInstr(r0)
    //     0xb25da8: ldur            x9, [x0, #-1]
    //     0xb25dac: ubfx            x9, x9, #0xc, #0x14
    // 0xb25db0: str             x0, [SP]
    // 0xb25db4: mov             x0, x9
    // 0xb25db8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb25db8: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb25dbc: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb25dbc: movz            x17, #0x2700
    //     0xb25dc0: add             lr, x0, x17
    //     0xb25dc4: ldr             lr, [x21, lr, lsl #3]
    //     0xb25dc8: blr             lr
    // 0xb25dcc: cmp             w0, NULL
    // 0xb25dd0: b.ne            #0xb25ddc
    // 0xb25dd4: r1 = ""
    //     0xb25dd4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25dd8: b               #0xb25de0
    // 0xb25ddc: mov             x1, x0
    // 0xb25de0: r0 = parse()
    //     0xb25de0: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb25de4: mov             v1.16b, v0.16b
    // 0xb25de8: d0 = 4.000000
    //     0xb25de8: fmov            d0, #4.00000000
    // 0xb25dec: fcmp            d1, d0
    // 0xb25df0: b.lt            #0xb25e00
    // 0xb25df4: r1 = Instance_Color
    //     0xb25df4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb25df8: ldr             x1, [x1, #0x858]
    // 0xb25dfc: b               #0xb25f40
    // 0xb25e00: ldur            x1, [fp, #-8]
    // 0xb25e04: LoadField: r0 = r1->field_b
    //     0xb25e04: ldur            w0, [x1, #0xb]
    // 0xb25e08: DecompressPointer r0
    //     0xb25e08: add             x0, x0, HEAP, lsl #32
    // 0xb25e0c: cmp             w0, NULL
    // 0xb25e10: b.eq            #0xb26854
    // 0xb25e14: LoadField: r2 = r0->field_b
    //     0xb25e14: ldur            w2, [x0, #0xb]
    // 0xb25e18: DecompressPointer r2
    //     0xb25e18: add             x2, x2, HEAP, lsl #32
    // 0xb25e1c: cmp             w2, NULL
    // 0xb25e20: b.ne            #0xb25e2c
    // 0xb25e24: r0 = Null
    //     0xb25e24: mov             x0, NULL
    // 0xb25e28: b               #0xb25e60
    // 0xb25e2c: LoadField: r0 = r2->field_f
    //     0xb25e2c: ldur            w0, [x2, #0xf]
    // 0xb25e30: DecompressPointer r0
    //     0xb25e30: add             x0, x0, HEAP, lsl #32
    // 0xb25e34: r2 = 60
    //     0xb25e34: movz            x2, #0x3c
    // 0xb25e38: branchIfSmi(r0, 0xb25e44)
    //     0xb25e38: tbz             w0, #0, #0xb25e44
    // 0xb25e3c: r2 = LoadClassIdInstr(r0)
    //     0xb25e3c: ldur            x2, [x0, #-1]
    //     0xb25e40: ubfx            x2, x2, #0xc, #0x14
    // 0xb25e44: str             x0, [SP]
    // 0xb25e48: mov             x0, x2
    // 0xb25e4c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb25e4c: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb25e50: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb25e50: movz            x17, #0x2700
    //     0xb25e54: add             lr, x0, x17
    //     0xb25e58: ldr             lr, [x21, lr, lsl #3]
    //     0xb25e5c: blr             lr
    // 0xb25e60: cmp             w0, NULL
    // 0xb25e64: b.ne            #0xb25e70
    // 0xb25e68: r1 = ""
    //     0xb25e68: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25e6c: b               #0xb25e74
    // 0xb25e70: mov             x1, x0
    // 0xb25e74: r0 = parse()
    //     0xb25e74: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb25e78: mov             v1.16b, v0.16b
    // 0xb25e7c: d0 = 3.500000
    //     0xb25e7c: fmov            d0, #3.50000000
    // 0xb25e80: fcmp            d1, d0
    // 0xb25e84: b.lt            #0xb25ea0
    // 0xb25e88: r1 = Instance_Color
    //     0xb25e88: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb25e8c: ldr             x1, [x1, #0x858]
    // 0xb25e90: d0 = 0.700000
    //     0xb25e90: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb25e94: ldr             d0, [x17, #0xf48]
    // 0xb25e98: r0 = withOpacity()
    //     0xb25e98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb25e9c: b               #0xb25f3c
    // 0xb25ea0: ldur            x1, [fp, #-8]
    // 0xb25ea4: LoadField: r0 = r1->field_b
    //     0xb25ea4: ldur            w0, [x1, #0xb]
    // 0xb25ea8: DecompressPointer r0
    //     0xb25ea8: add             x0, x0, HEAP, lsl #32
    // 0xb25eac: cmp             w0, NULL
    // 0xb25eb0: b.eq            #0xb26858
    // 0xb25eb4: LoadField: r2 = r0->field_b
    //     0xb25eb4: ldur            w2, [x0, #0xb]
    // 0xb25eb8: DecompressPointer r2
    //     0xb25eb8: add             x2, x2, HEAP, lsl #32
    // 0xb25ebc: cmp             w2, NULL
    // 0xb25ec0: b.ne            #0xb25ecc
    // 0xb25ec4: r0 = Null
    //     0xb25ec4: mov             x0, NULL
    // 0xb25ec8: b               #0xb25f00
    // 0xb25ecc: LoadField: r0 = r2->field_f
    //     0xb25ecc: ldur            w0, [x2, #0xf]
    // 0xb25ed0: DecompressPointer r0
    //     0xb25ed0: add             x0, x0, HEAP, lsl #32
    // 0xb25ed4: r2 = 60
    //     0xb25ed4: movz            x2, #0x3c
    // 0xb25ed8: branchIfSmi(r0, 0xb25ee4)
    //     0xb25ed8: tbz             w0, #0, #0xb25ee4
    // 0xb25edc: r2 = LoadClassIdInstr(r0)
    //     0xb25edc: ldur            x2, [x0, #-1]
    //     0xb25ee0: ubfx            x2, x2, #0xc, #0x14
    // 0xb25ee4: str             x0, [SP]
    // 0xb25ee8: mov             x0, x2
    // 0xb25eec: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb25eec: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb25ef0: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb25ef0: movz            x17, #0x2700
    //     0xb25ef4: add             lr, x0, x17
    //     0xb25ef8: ldr             lr, [x21, lr, lsl #3]
    //     0xb25efc: blr             lr
    // 0xb25f00: cmp             w0, NULL
    // 0xb25f04: b.ne            #0xb25f10
    // 0xb25f08: r1 = ""
    //     0xb25f08: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb25f0c: b               #0xb25f14
    // 0xb25f10: mov             x1, x0
    // 0xb25f14: r0 = parse()
    //     0xb25f14: bl              #0x64333c  ; [dart:core] double::parse
    // 0xb25f18: mov             v1.16b, v0.16b
    // 0xb25f1c: d0 = 2.000000
    //     0xb25f1c: fmov            d0, #2.00000000
    // 0xb25f20: fcmp            d1, d0
    // 0xb25f24: b.lt            #0xb25f34
    // 0xb25f28: r0 = Instance_Color
    //     0xb25f28: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb25f2c: ldr             x0, [x0, #0x860]
    // 0xb25f30: b               #0xb25f3c
    // 0xb25f34: r0 = Instance_Color
    //     0xb25f34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb25f38: ldr             x0, [x0, #0x50]
    // 0xb25f3c: mov             x1, x0
    // 0xb25f40: ldur            x0, [fp, #-8]
    // 0xb25f44: stur            x1, [fp, #-0x30]
    // 0xb25f48: r0 = ColorFilter()
    //     0xb25f48: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb25f4c: mov             x1, x0
    // 0xb25f50: ldur            x0, [fp, #-0x30]
    // 0xb25f54: stur            x1, [fp, #-0x38]
    // 0xb25f58: StoreField: r1->field_7 = r0
    //     0xb25f58: stur            w0, [x1, #7]
    // 0xb25f5c: r0 = Instance_BlendMode
    //     0xb25f5c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb25f60: ldr             x0, [x0, #0xb30]
    // 0xb25f64: StoreField: r1->field_b = r0
    //     0xb25f64: stur            w0, [x1, #0xb]
    // 0xb25f68: r0 = 1
    //     0xb25f68: movz            x0, #0x1
    // 0xb25f6c: StoreField: r1->field_13 = r0
    //     0xb25f6c: stur            x0, [x1, #0x13]
    // 0xb25f70: r0 = SvgPicture()
    //     0xb25f70: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb25f74: stur            x0, [fp, #-0x30]
    // 0xb25f78: ldur            x16, [fp, #-0x38]
    // 0xb25f7c: str             x16, [SP]
    // 0xb25f80: mov             x1, x0
    // 0xb25f84: r2 = "assets/images/green_star.svg"
    //     0xb25f84: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb25f88: ldr             x2, [x2, #0x9a0]
    // 0xb25f8c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb25f8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb25f90: ldr             x4, [x4, #0xa38]
    // 0xb25f94: r0 = SvgPicture.asset()
    //     0xb25f94: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb25f98: ldur            x1, [fp, #-8]
    // 0xb25f9c: LoadField: r0 = r1->field_b
    //     0xb25f9c: ldur            w0, [x1, #0xb]
    // 0xb25fa0: DecompressPointer r0
    //     0xb25fa0: add             x0, x0, HEAP, lsl #32
    // 0xb25fa4: cmp             w0, NULL
    // 0xb25fa8: b.eq            #0xb2685c
    // 0xb25fac: LoadField: r2 = r0->field_b
    //     0xb25fac: ldur            w2, [x0, #0xb]
    // 0xb25fb0: DecompressPointer r2
    //     0xb25fb0: add             x2, x2, HEAP, lsl #32
    // 0xb25fb4: cmp             w2, NULL
    // 0xb25fb8: b.ne            #0xb25fc4
    // 0xb25fbc: r0 = Null
    //     0xb25fbc: mov             x0, NULL
    // 0xb25fc0: b               #0xb25ff8
    // 0xb25fc4: LoadField: r0 = r2->field_f
    //     0xb25fc4: ldur            w0, [x2, #0xf]
    // 0xb25fc8: DecompressPointer r0
    //     0xb25fc8: add             x0, x0, HEAP, lsl #32
    // 0xb25fcc: r2 = 60
    //     0xb25fcc: movz            x2, #0x3c
    // 0xb25fd0: branchIfSmi(r0, 0xb25fdc)
    //     0xb25fd0: tbz             w0, #0, #0xb25fdc
    // 0xb25fd4: r2 = LoadClassIdInstr(r0)
    //     0xb25fd4: ldur            x2, [x0, #-1]
    //     0xb25fd8: ubfx            x2, x2, #0xc, #0x14
    // 0xb25fdc: str             x0, [SP]
    // 0xb25fe0: mov             x0, x2
    // 0xb25fe4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb25fe4: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb25fe8: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb25fe8: movz            x17, #0x2700
    //     0xb25fec: add             lr, x0, x17
    //     0xb25ff0: ldr             lr, [x21, lr, lsl #3]
    //     0xb25ff4: blr             lr
    // 0xb25ff8: cmp             w0, NULL
    // 0xb25ffc: b.ne            #0xb26008
    // 0xb26000: r5 = ""
    //     0xb26000: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb26004: b               #0xb2600c
    // 0xb26008: mov             x5, x0
    // 0xb2600c: ldur            x3, [fp, #-0x20]
    // 0xb26010: ldur            x4, [fp, #-0x18]
    // 0xb26014: ldur            x2, [fp, #-0x28]
    // 0xb26018: ldur            x0, [fp, #-0x30]
    // 0xb2601c: stur            x5, [fp, #-0x38]
    // 0xb26020: LoadField: r1 = r3->field_13
    //     0xb26020: ldur            w1, [x3, #0x13]
    // 0xb26024: DecompressPointer r1
    //     0xb26024: add             x1, x1, HEAP, lsl #32
    // 0xb26028: r0 = of()
    //     0xb26028: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb2602c: LoadField: r1 = r0->field_87
    //     0xb2602c: ldur            w1, [x0, #0x87]
    // 0xb26030: DecompressPointer r1
    //     0xb26030: add             x1, x1, HEAP, lsl #32
    // 0xb26034: LoadField: r0 = r1->field_7
    //     0xb26034: ldur            w0, [x1, #7]
    // 0xb26038: DecompressPointer r0
    //     0xb26038: add             x0, x0, HEAP, lsl #32
    // 0xb2603c: r16 = 12.000000
    //     0xb2603c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb26040: ldr             x16, [x16, #0x9e8]
    // 0xb26044: r30 = Instance_Color
    //     0xb26044: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb26048: stp             lr, x16, [SP]
    // 0xb2604c: mov             x1, x0
    // 0xb26050: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb26050: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb26054: ldr             x4, [x4, #0xaa0]
    // 0xb26058: r0 = copyWith()
    //     0xb26058: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb2605c: stur            x0, [fp, #-0x48]
    // 0xb26060: r0 = Text()
    //     0xb26060: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb26064: mov             x3, x0
    // 0xb26068: ldur            x0, [fp, #-0x38]
    // 0xb2606c: stur            x3, [fp, #-0x50]
    // 0xb26070: StoreField: r3->field_b = r0
    //     0xb26070: stur            w0, [x3, #0xb]
    // 0xb26074: ldur            x0, [fp, #-0x48]
    // 0xb26078: StoreField: r3->field_13 = r0
    //     0xb26078: stur            w0, [x3, #0x13]
    // 0xb2607c: r1 = Null
    //     0xb2607c: mov             x1, NULL
    // 0xb26080: r2 = 6
    //     0xb26080: movz            x2, #0x6
    // 0xb26084: r0 = AllocateArray()
    //     0xb26084: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb26088: mov             x2, x0
    // 0xb2608c: ldur            x0, [fp, #-0x30]
    // 0xb26090: stur            x2, [fp, #-0x38]
    // 0xb26094: StoreField: r2->field_f = r0
    //     0xb26094: stur            w0, [x2, #0xf]
    // 0xb26098: r16 = Instance_SizedBox
    //     0xb26098: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xb2609c: ldr             x16, [x16, #0xe98]
    // 0xb260a0: StoreField: r2->field_13 = r16
    //     0xb260a0: stur            w16, [x2, #0x13]
    // 0xb260a4: ldur            x0, [fp, #-0x50]
    // 0xb260a8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb260a8: stur            w0, [x2, #0x17]
    // 0xb260ac: r1 = <Widget>
    //     0xb260ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb260b0: r0 = AllocateGrowableArray()
    //     0xb260b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb260b4: mov             x1, x0
    // 0xb260b8: ldur            x0, [fp, #-0x38]
    // 0xb260bc: stur            x1, [fp, #-0x30]
    // 0xb260c0: StoreField: r1->field_f = r0
    //     0xb260c0: stur            w0, [x1, #0xf]
    // 0xb260c4: r0 = 6
    //     0xb260c4: movz            x0, #0x6
    // 0xb260c8: StoreField: r1->field_b = r0
    //     0xb260c8: stur            w0, [x1, #0xb]
    // 0xb260cc: r0 = Row()
    //     0xb260cc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb260d0: mov             x3, x0
    // 0xb260d4: r0 = Instance_Axis
    //     0xb260d4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb260d8: stur            x3, [fp, #-0x38]
    // 0xb260dc: StoreField: r3->field_f = r0
    //     0xb260dc: stur            w0, [x3, #0xf]
    // 0xb260e0: r4 = Instance_MainAxisAlignment
    //     0xb260e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb260e4: ldr             x4, [x4, #0xa08]
    // 0xb260e8: StoreField: r3->field_13 = r4
    //     0xb260e8: stur            w4, [x3, #0x13]
    // 0xb260ec: r5 = Instance_MainAxisSize
    //     0xb260ec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb260f0: ldr             x5, [x5, #0xa10]
    // 0xb260f4: ArrayStore: r3[0] = r5  ; List_4
    //     0xb260f4: stur            w5, [x3, #0x17]
    // 0xb260f8: r6 = Instance_CrossAxisAlignment
    //     0xb260f8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb260fc: ldr             x6, [x6, #0xa18]
    // 0xb26100: StoreField: r3->field_1b = r6
    //     0xb26100: stur            w6, [x3, #0x1b]
    // 0xb26104: r7 = Instance_VerticalDirection
    //     0xb26104: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb26108: ldr             x7, [x7, #0xa20]
    // 0xb2610c: StoreField: r3->field_23 = r7
    //     0xb2610c: stur            w7, [x3, #0x23]
    // 0xb26110: r8 = Instance_Clip
    //     0xb26110: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb26114: ldr             x8, [x8, #0x38]
    // 0xb26118: StoreField: r3->field_2b = r8
    //     0xb26118: stur            w8, [x3, #0x2b]
    // 0xb2611c: StoreField: r3->field_2f = rZR
    //     0xb2611c: stur            xzr, [x3, #0x2f]
    // 0xb26120: ldur            x1, [fp, #-0x30]
    // 0xb26124: StoreField: r3->field_b = r1
    //     0xb26124: stur            w1, [x3, #0xb]
    // 0xb26128: r1 = Null
    //     0xb26128: mov             x1, NULL
    // 0xb2612c: r2 = 4
    //     0xb2612c: movz            x2, #0x4
    // 0xb26130: r0 = AllocateArray()
    //     0xb26130: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb26134: mov             x2, x0
    // 0xb26138: ldur            x0, [fp, #-0x28]
    // 0xb2613c: stur            x2, [fp, #-0x30]
    // 0xb26140: StoreField: r2->field_f = r0
    //     0xb26140: stur            w0, [x2, #0xf]
    // 0xb26144: ldur            x0, [fp, #-0x38]
    // 0xb26148: StoreField: r2->field_13 = r0
    //     0xb26148: stur            w0, [x2, #0x13]
    // 0xb2614c: r1 = <Widget>
    //     0xb2614c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb26150: r0 = AllocateGrowableArray()
    //     0xb26150: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb26154: mov             x1, x0
    // 0xb26158: ldur            x0, [fp, #-0x30]
    // 0xb2615c: stur            x1, [fp, #-0x28]
    // 0xb26160: StoreField: r1->field_f = r0
    //     0xb26160: stur            w0, [x1, #0xf]
    // 0xb26164: r2 = 4
    //     0xb26164: movz            x2, #0x4
    // 0xb26168: StoreField: r1->field_b = r2
    //     0xb26168: stur            w2, [x1, #0xb]
    // 0xb2616c: r0 = Row()
    //     0xb2616c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb26170: mov             x1, x0
    // 0xb26174: r0 = Instance_Axis
    //     0xb26174: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb26178: stur            x1, [fp, #-0x30]
    // 0xb2617c: StoreField: r1->field_f = r0
    //     0xb2617c: stur            w0, [x1, #0xf]
    // 0xb26180: r0 = Instance_MainAxisAlignment
    //     0xb26180: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb26184: ldr             x0, [x0, #0xa8]
    // 0xb26188: StoreField: r1->field_13 = r0
    //     0xb26188: stur            w0, [x1, #0x13]
    // 0xb2618c: r0 = Instance_MainAxisSize
    //     0xb2618c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb26190: ldr             x0, [x0, #0xa10]
    // 0xb26194: ArrayStore: r1[0] = r0  ; List_4
    //     0xb26194: stur            w0, [x1, #0x17]
    // 0xb26198: r2 = Instance_CrossAxisAlignment
    //     0xb26198: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb2619c: ldr             x2, [x2, #0xa18]
    // 0xb261a0: StoreField: r1->field_1b = r2
    //     0xb261a0: stur            w2, [x1, #0x1b]
    // 0xb261a4: r3 = Instance_VerticalDirection
    //     0xb261a4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb261a8: ldr             x3, [x3, #0xa20]
    // 0xb261ac: StoreField: r1->field_23 = r3
    //     0xb261ac: stur            w3, [x1, #0x23]
    // 0xb261b0: r4 = Instance_Clip
    //     0xb261b0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb261b4: ldr             x4, [x4, #0x38]
    // 0xb261b8: StoreField: r1->field_2b = r4
    //     0xb261b8: stur            w4, [x1, #0x2b]
    // 0xb261bc: StoreField: r1->field_2f = rZR
    //     0xb261bc: stur            xzr, [x1, #0x2f]
    // 0xb261c0: ldur            x5, [fp, #-0x28]
    // 0xb261c4: StoreField: r1->field_b = r5
    //     0xb261c4: stur            w5, [x1, #0xb]
    // 0xb261c8: r0 = Container()
    //     0xb261c8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb261cc: stur            x0, [fp, #-0x28]
    // 0xb261d0: r16 = Instance_BoxDecoration
    //     0xb261d0: add             x16, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb261d4: ldr             x16, [x16, #0x5a8]
    // 0xb261d8: r30 = Instance_EdgeInsets
    //     0xb261d8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb261dc: ldr             lr, [lr, #0x1f0]
    // 0xb261e0: stp             lr, x16, [SP, #8]
    // 0xb261e4: ldur            x16, [fp, #-0x30]
    // 0xb261e8: str             x16, [SP]
    // 0xb261ec: mov             x1, x0
    // 0xb261f0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb261f0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb261f4: ldr             x4, [x4, #0xb40]
    // 0xb261f8: r0 = Container()
    //     0xb261f8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb261fc: ldur            x0, [fp, #-0x18]
    // 0xb26200: LoadField: r1 = r0->field_b
    //     0xb26200: ldur            w1, [x0, #0xb]
    // 0xb26204: LoadField: r2 = r0->field_f
    //     0xb26204: ldur            w2, [x0, #0xf]
    // 0xb26208: DecompressPointer r2
    //     0xb26208: add             x2, x2, HEAP, lsl #32
    // 0xb2620c: LoadField: r3 = r2->field_b
    //     0xb2620c: ldur            w3, [x2, #0xb]
    // 0xb26210: r2 = LoadInt32Instr(r1)
    //     0xb26210: sbfx            x2, x1, #1, #0x1f
    // 0xb26214: stur            x2, [fp, #-0x40]
    // 0xb26218: r1 = LoadInt32Instr(r3)
    //     0xb26218: sbfx            x1, x3, #1, #0x1f
    // 0xb2621c: cmp             x2, x1
    // 0xb26220: b.ne            #0xb2622c
    // 0xb26224: mov             x1, x0
    // 0xb26228: r0 = _growToNextCapacity()
    //     0xb26228: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb2622c: ldur            x2, [fp, #-0x18]
    // 0xb26230: ldur            x3, [fp, #-0x40]
    // 0xb26234: add             x0, x3, #1
    // 0xb26238: lsl             x1, x0, #1
    // 0xb2623c: StoreField: r2->field_b = r1
    //     0xb2623c: stur            w1, [x2, #0xb]
    // 0xb26240: LoadField: r1 = r2->field_f
    //     0xb26240: ldur            w1, [x2, #0xf]
    // 0xb26244: DecompressPointer r1
    //     0xb26244: add             x1, x1, HEAP, lsl #32
    // 0xb26248: ldur            x0, [fp, #-0x28]
    // 0xb2624c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb2624c: add             x25, x1, x3, lsl #2
    //     0xb26250: add             x25, x25, #0xf
    //     0xb26254: str             w0, [x25]
    //     0xb26258: tbz             w0, #0, #0xb26274
    //     0xb2625c: ldurb           w16, [x1, #-1]
    //     0xb26260: ldurb           w17, [x0, #-1]
    //     0xb26264: and             x16, x17, x16, lsr #2
    //     0xb26268: tst             x16, HEAP, lsr #32
    //     0xb2626c: b.eq            #0xb26274
    //     0xb26270: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb26274: ldur            x0, [fp, #-8]
    // 0xb26278: LoadField: r1 = r0->field_b
    //     0xb26278: ldur            w1, [x0, #0xb]
    // 0xb2627c: DecompressPointer r1
    //     0xb2627c: add             x1, x1, HEAP, lsl #32
    // 0xb26280: cmp             w1, NULL
    // 0xb26284: b.eq            #0xb26860
    // 0xb26288: LoadField: r3 = r1->field_b
    //     0xb26288: ldur            w3, [x1, #0xb]
    // 0xb2628c: DecompressPointer r3
    //     0xb2628c: add             x3, x3, HEAP, lsl #32
    // 0xb26290: cmp             w3, NULL
    // 0xb26294: b.ne            #0xb262a0
    // 0xb26298: r1 = Null
    //     0xb26298: mov             x1, NULL
    // 0xb2629c: b               #0xb262a8
    // 0xb262a0: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb262a0: ldur            w1, [x3, #0x17]
    // 0xb262a4: DecompressPointer r1
    //     0xb262a4: add             x1, x1, HEAP, lsl #32
    // 0xb262a8: cmp             w1, NULL
    // 0xb262ac: b.ne            #0xb262b8
    // 0xb262b0: r4 = ""
    //     0xb262b0: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb262b4: b               #0xb262bc
    // 0xb262b8: mov             x4, x1
    // 0xb262bc: ldur            x3, [fp, #-0x20]
    // 0xb262c0: stur            x4, [fp, #-0x28]
    // 0xb262c4: LoadField: r1 = r3->field_13
    //     0xb262c4: ldur            w1, [x3, #0x13]
    // 0xb262c8: DecompressPointer r1
    //     0xb262c8: add             x1, x1, HEAP, lsl #32
    // 0xb262cc: r0 = of()
    //     0xb262cc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb262d0: LoadField: r1 = r0->field_87
    //     0xb262d0: ldur            w1, [x0, #0x87]
    // 0xb262d4: DecompressPointer r1
    //     0xb262d4: add             x1, x1, HEAP, lsl #32
    // 0xb262d8: LoadField: r0 = r1->field_2b
    //     0xb262d8: ldur            w0, [x1, #0x2b]
    // 0xb262dc: DecompressPointer r0
    //     0xb262dc: add             x0, x0, HEAP, lsl #32
    // 0xb262e0: LoadField: r1 = r0->field_13
    //     0xb262e0: ldur            w1, [x0, #0x13]
    // 0xb262e4: DecompressPointer r1
    //     0xb262e4: add             x1, x1, HEAP, lsl #32
    // 0xb262e8: r16 = Instance_Color
    //     0xb262e8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb262ec: stp             x16, x1, [SP]
    // 0xb262f0: r1 = Instance_TextStyle
    //     0xb262f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f9b0] Obj!TextStyle@d62871
    //     0xb262f4: ldr             x1, [x1, #0x9b0]
    // 0xb262f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontFamily, 0x1, null]
    //     0xb262f8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontFamily", 0x1, Null]
    //     0xb262fc: ldr             x4, [x4, #0x9b8]
    // 0xb26300: r0 = copyWith()
    //     0xb26300: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb26304: ldur            x2, [fp, #-0x20]
    // 0xb26308: stur            x0, [fp, #-0x30]
    // 0xb2630c: LoadField: r1 = r2->field_13
    //     0xb2630c: ldur            w1, [x2, #0x13]
    // 0xb26310: DecompressPointer r1
    //     0xb26310: add             x1, x1, HEAP, lsl #32
    // 0xb26314: r0 = of()
    //     0xb26314: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb26318: LoadField: r1 = r0->field_87
    //     0xb26318: ldur            w1, [x0, #0x87]
    // 0xb2631c: DecompressPointer r1
    //     0xb2631c: add             x1, x1, HEAP, lsl #32
    // 0xb26320: LoadField: r0 = r1->field_7
    //     0xb26320: ldur            w0, [x1, #7]
    // 0xb26324: DecompressPointer r0
    //     0xb26324: add             x0, x0, HEAP, lsl #32
    // 0xb26328: r16 = 12.000000
    //     0xb26328: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2632c: ldr             x16, [x16, #0x9e8]
    // 0xb26330: r30 = Instance_Color
    //     0xb26330: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb26334: stp             lr, x16, [SP, #8]
    // 0xb26338: r16 = Instance_FontWeight
    //     0xb26338: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb2633c: ldr             x16, [x16, #0x20]
    // 0xb26340: str             x16, [SP]
    // 0xb26344: mov             x1, x0
    // 0xb26348: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb26348: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb2634c: ldr             x4, [x4, #0xc48]
    // 0xb26350: r0 = copyWith()
    //     0xb26350: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb26354: ldur            x2, [fp, #-0x20]
    // 0xb26358: stur            x0, [fp, #-0x38]
    // 0xb2635c: LoadField: r1 = r2->field_13
    //     0xb2635c: ldur            w1, [x2, #0x13]
    // 0xb26360: DecompressPointer r1
    //     0xb26360: add             x1, x1, HEAP, lsl #32
    // 0xb26364: r0 = of()
    //     0xb26364: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb26368: LoadField: r1 = r0->field_87
    //     0xb26368: ldur            w1, [x0, #0x87]
    // 0xb2636c: DecompressPointer r1
    //     0xb2636c: add             x1, x1, HEAP, lsl #32
    // 0xb26370: LoadField: r0 = r1->field_7
    //     0xb26370: ldur            w0, [x1, #7]
    // 0xb26374: DecompressPointer r0
    //     0xb26374: add             x0, x0, HEAP, lsl #32
    // 0xb26378: r16 = 12.000000
    //     0xb26378: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb2637c: ldr             x16, [x16, #0x9e8]
    // 0xb26380: r30 = Instance_Color
    //     0xb26380: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb26384: stp             lr, x16, [SP, #8]
    // 0xb26388: r16 = Instance_FontWeight
    //     0xb26388: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb2638c: ldr             x16, [x16, #0x20]
    // 0xb26390: str             x16, [SP]
    // 0xb26394: mov             x1, x0
    // 0xb26398: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb26398: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb2639c: ldr             x4, [x4, #0xc48]
    // 0xb263a0: r0 = copyWith()
    //     0xb263a0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb263a4: mov             x1, x0
    // 0xb263a8: ldur            x0, [fp, #-8]
    // 0xb263ac: stur            x1, [fp, #-0x50]
    // 0xb263b0: LoadField: r2 = r0->field_1f
    //     0xb263b0: ldur            w2, [x0, #0x1f]
    // 0xb263b4: DecompressPointer r2
    //     0xb263b4: add             x2, x2, HEAP, lsl #32
    // 0xb263b8: stur            x2, [fp, #-0x48]
    // 0xb263bc: r0 = ReadMoreText()
    //     0xb263bc: bl              #0x99f824  ; AllocateReadMoreTextStub -> ReadMoreText (size=0x6c)
    // 0xb263c0: mov             x1, x0
    // 0xb263c4: ldur            x0, [fp, #-0x28]
    // 0xb263c8: stur            x1, [fp, #-8]
    // 0xb263cc: StoreField: r1->field_3f = r0
    //     0xb263cc: stur            w0, [x1, #0x3f]
    // 0xb263d0: ldur            x0, [fp, #-0x48]
    // 0xb263d4: StoreField: r1->field_b = r0
    //     0xb263d4: stur            w0, [x1, #0xb]
    // 0xb263d8: r0 = " Read Less"
    //     0xb263d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c0] " Read Less"
    //     0xb263dc: ldr             x0, [x0, #0x9c0]
    // 0xb263e0: StoreField: r1->field_43 = r0
    //     0xb263e0: stur            w0, [x1, #0x43]
    // 0xb263e4: r0 = "Read More"
    //     0xb263e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9c8] "Read More"
    //     0xb263e8: ldr             x0, [x0, #0x9c8]
    // 0xb263ec: StoreField: r1->field_47 = r0
    //     0xb263ec: stur            w0, [x1, #0x47]
    // 0xb263f0: r0 = 240
    //     0xb263f0: movz            x0, #0xf0
    // 0xb263f4: StoreField: r1->field_f = r0
    //     0xb263f4: stur            x0, [x1, #0xf]
    // 0xb263f8: r0 = 2
    //     0xb263f8: movz            x0, #0x2
    // 0xb263fc: ArrayStore: r1[0] = r0  ; List_8
    //     0xb263fc: stur            x0, [x1, #0x17]
    // 0xb26400: r0 = Instance_TrimMode
    //     0xb26400: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d0] Obj!TrimMode@d70201
    //     0xb26404: ldr             x0, [x0, #0x9d0]
    // 0xb26408: StoreField: r1->field_1f = r0
    //     0xb26408: stur            w0, [x1, #0x1f]
    // 0xb2640c: ldur            x0, [fp, #-0x30]
    // 0xb26410: StoreField: r1->field_4f = r0
    //     0xb26410: stur            w0, [x1, #0x4f]
    // 0xb26414: r0 = Instance_TextAlign
    //     0xb26414: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb26418: StoreField: r1->field_53 = r0
    //     0xb26418: stur            w0, [x1, #0x53]
    // 0xb2641c: ldur            x0, [fp, #-0x38]
    // 0xb26420: StoreField: r1->field_23 = r0
    //     0xb26420: stur            w0, [x1, #0x23]
    // 0xb26424: ldur            x0, [fp, #-0x50]
    // 0xb26428: StoreField: r1->field_27 = r0
    //     0xb26428: stur            w0, [x1, #0x27]
    // 0xb2642c: r0 = "… "
    //     0xb2642c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9d8] "… "
    //     0xb26430: ldr             x0, [x0, #0x9d8]
    // 0xb26434: StoreField: r1->field_3b = r0
    //     0xb26434: stur            w0, [x1, #0x3b]
    // 0xb26438: r0 = true
    //     0xb26438: add             x0, NULL, #0x20  ; true
    // 0xb2643c: StoreField: r1->field_37 = r0
    //     0xb2643c: stur            w0, [x1, #0x37]
    // 0xb26440: r0 = Container()
    //     0xb26440: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb26444: stur            x0, [fp, #-0x28]
    // 0xb26448: r16 = Instance_EdgeInsets
    //     0xb26448: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb2644c: ldr             x16, [x16, #0x668]
    // 0xb26450: r30 = Instance_BoxDecoration
    //     0xb26450: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xb26454: ldr             lr, [lr, #0x5a8]
    // 0xb26458: stp             lr, x16, [SP, #0x10]
    // 0xb2645c: r16 = Instance_Alignment
    //     0xb2645c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0xb26460: ldr             x16, [x16, #0xf98]
    // 0xb26464: ldur            lr, [fp, #-8]
    // 0xb26468: stp             lr, x16, [SP]
    // 0xb2646c: mov             x1, x0
    // 0xb26470: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x3, child, 0x4, decoration, 0x2, padding, 0x1, null]
    //     0xb26470: add             x4, PP, #0x51, lsl #12  ; [pp+0x51ea0] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x3, "child", 0x4, "decoration", 0x2, "padding", 0x1, Null]
    //     0xb26474: ldr             x4, [x4, #0xea0]
    // 0xb26478: r0 = Container()
    //     0xb26478: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb2647c: r0 = Padding()
    //     0xb2647c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb26480: mov             x2, x0
    // 0xb26484: r0 = Instance_EdgeInsets
    //     0xb26484: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a18] Obj!EdgeInsets@d58b51
    //     0xb26488: ldr             x0, [x0, #0xa18]
    // 0xb2648c: stur            x2, [fp, #-8]
    // 0xb26490: StoreField: r2->field_f = r0
    //     0xb26490: stur            w0, [x2, #0xf]
    // 0xb26494: ldur            x0, [fp, #-0x28]
    // 0xb26498: StoreField: r2->field_b = r0
    //     0xb26498: stur            w0, [x2, #0xb]
    // 0xb2649c: ldur            x0, [fp, #-0x18]
    // 0xb264a0: LoadField: r1 = r0->field_b
    //     0xb264a0: ldur            w1, [x0, #0xb]
    // 0xb264a4: LoadField: r3 = r0->field_f
    //     0xb264a4: ldur            w3, [x0, #0xf]
    // 0xb264a8: DecompressPointer r3
    //     0xb264a8: add             x3, x3, HEAP, lsl #32
    // 0xb264ac: LoadField: r4 = r3->field_b
    //     0xb264ac: ldur            w4, [x3, #0xb]
    // 0xb264b0: r3 = LoadInt32Instr(r1)
    //     0xb264b0: sbfx            x3, x1, #1, #0x1f
    // 0xb264b4: stur            x3, [fp, #-0x40]
    // 0xb264b8: r1 = LoadInt32Instr(r4)
    //     0xb264b8: sbfx            x1, x4, #1, #0x1f
    // 0xb264bc: cmp             x3, x1
    // 0xb264c0: b.ne            #0xb264cc
    // 0xb264c4: mov             x1, x0
    // 0xb264c8: r0 = _growToNextCapacity()
    //     0xb264c8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb264cc: ldur            x2, [fp, #-0x18]
    // 0xb264d0: ldur            x3, [fp, #-0x40]
    // 0xb264d4: add             x0, x3, #1
    // 0xb264d8: lsl             x1, x0, #1
    // 0xb264dc: StoreField: r2->field_b = r1
    //     0xb264dc: stur            w1, [x2, #0xb]
    // 0xb264e0: LoadField: r1 = r2->field_f
    //     0xb264e0: ldur            w1, [x2, #0xf]
    // 0xb264e4: DecompressPointer r1
    //     0xb264e4: add             x1, x1, HEAP, lsl #32
    // 0xb264e8: ldur            x0, [fp, #-8]
    // 0xb264ec: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb264ec: add             x25, x1, x3, lsl #2
    //     0xb264f0: add             x25, x25, #0xf
    //     0xb264f4: str             w0, [x25]
    //     0xb264f8: tbz             w0, #0, #0xb26514
    //     0xb264fc: ldurb           w16, [x1, #-1]
    //     0xb26500: ldurb           w17, [x0, #-1]
    //     0xb26504: and             x16, x17, x16, lsr #2
    //     0xb26508: tst             x16, HEAP, lsr #32
    //     0xb2650c: b.eq            #0xb26514
    //     0xb26510: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb26514: r0 = GestureDetector()
    //     0xb26514: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xb26518: ldur            x2, [fp, #-0x20]
    // 0xb2651c: r1 = Function '<anonymous closure>':.
    //     0xb2651c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57558] AnonymousClosure: (0xaaa130), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xc14408)
    //     0xb26520: ldr             x1, [x1, #0x558]
    // 0xb26524: stur            x0, [fp, #-8]
    // 0xb26528: r0 = AllocateClosure()
    //     0xb26528: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2652c: ldur            x2, [fp, #-0x20]
    // 0xb26530: r1 = Function '<anonymous closure>':.
    //     0xb26530: add             x1, PP, #0x57, lsl #12  ; [pp+0x57560] AnonymousClosure: (0xb26864), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::build (0xb245a0)
    //     0xb26534: ldr             x1, [x1, #0x560]
    // 0xb26538: stur            x0, [fp, #-0x20]
    // 0xb2653c: r0 = AllocateClosure()
    //     0xb2653c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb26540: ldur            x16, [fp, #-0x20]
    // 0xb26544: stp             x0, x16, [SP, #8]
    // 0xb26548: r16 = Instance_Icon
    //     0xb26548: add             x16, PP, #0x51, lsl #12  ; [pp+0x51eb8] Obj!Icon@d66531
    //     0xb2654c: ldr             x16, [x16, #0xeb8]
    // 0xb26550: str             x16, [SP]
    // 0xb26554: ldur            x1, [fp, #-8]
    // 0xb26558: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, onTap, 0x2, onTapDown, 0x1, null]
    //     0xb26558: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa20] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "onTap", 0x2, "onTapDown", 0x1, Null]
    //     0xb2655c: ldr             x4, [x4, #0xa20]
    // 0xb26560: r0 = GestureDetector()
    //     0xb26560: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xb26564: r0 = Align()
    //     0xb26564: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb26568: mov             x1, x0
    // 0xb2656c: r0 = Instance_Alignment
    //     0xb2656c: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb26570: ldr             x0, [x0, #0xa78]
    // 0xb26574: stur            x1, [fp, #-0x20]
    // 0xb26578: StoreField: r1->field_f = r0
    //     0xb26578: stur            w0, [x1, #0xf]
    // 0xb2657c: ldur            x0, [fp, #-8]
    // 0xb26580: StoreField: r1->field_b = r0
    //     0xb26580: stur            w0, [x1, #0xb]
    // 0xb26584: r0 = Padding()
    //     0xb26584: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb26588: mov             x2, x0
    // 0xb2658c: r0 = Instance_EdgeInsets
    //     0xb2658c: add             x0, PP, #0x51, lsl #12  ; [pp+0x51ec0] Obj!EdgeInsets@d58af1
    //     0xb26590: ldr             x0, [x0, #0xec0]
    // 0xb26594: stur            x2, [fp, #-8]
    // 0xb26598: StoreField: r2->field_f = r0
    //     0xb26598: stur            w0, [x2, #0xf]
    // 0xb2659c: ldur            x0, [fp, #-0x20]
    // 0xb265a0: StoreField: r2->field_b = r0
    //     0xb265a0: stur            w0, [x2, #0xb]
    // 0xb265a4: ldur            x0, [fp, #-0x18]
    // 0xb265a8: LoadField: r1 = r0->field_b
    //     0xb265a8: ldur            w1, [x0, #0xb]
    // 0xb265ac: LoadField: r3 = r0->field_f
    //     0xb265ac: ldur            w3, [x0, #0xf]
    // 0xb265b0: DecompressPointer r3
    //     0xb265b0: add             x3, x3, HEAP, lsl #32
    // 0xb265b4: LoadField: r4 = r3->field_b
    //     0xb265b4: ldur            w4, [x3, #0xb]
    // 0xb265b8: r3 = LoadInt32Instr(r1)
    //     0xb265b8: sbfx            x3, x1, #1, #0x1f
    // 0xb265bc: stur            x3, [fp, #-0x40]
    // 0xb265c0: r1 = LoadInt32Instr(r4)
    //     0xb265c0: sbfx            x1, x4, #1, #0x1f
    // 0xb265c4: cmp             x3, x1
    // 0xb265c8: b.ne            #0xb265d4
    // 0xb265cc: mov             x1, x0
    // 0xb265d0: r0 = _growToNextCapacity()
    //     0xb265d0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb265d4: ldur            x4, [fp, #-0x10]
    // 0xb265d8: ldur            x2, [fp, #-0x18]
    // 0xb265dc: ldur            x3, [fp, #-0x40]
    // 0xb265e0: add             x0, x3, #1
    // 0xb265e4: lsl             x1, x0, #1
    // 0xb265e8: StoreField: r2->field_b = r1
    //     0xb265e8: stur            w1, [x2, #0xb]
    // 0xb265ec: LoadField: r1 = r2->field_f
    //     0xb265ec: ldur            w1, [x2, #0xf]
    // 0xb265f0: DecompressPointer r1
    //     0xb265f0: add             x1, x1, HEAP, lsl #32
    // 0xb265f4: ldur            x0, [fp, #-8]
    // 0xb265f8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb265f8: add             x25, x1, x3, lsl #2
    //     0xb265fc: add             x25, x25, #0xf
    //     0xb26600: str             w0, [x25]
    //     0xb26604: tbz             w0, #0, #0xb26620
    //     0xb26608: ldurb           w16, [x1, #-1]
    //     0xb2660c: ldurb           w17, [x0, #-1]
    //     0xb26610: and             x16, x17, x16, lsr #2
    //     0xb26614: tst             x16, HEAP, lsr #32
    //     0xb26618: b.eq            #0xb26620
    //     0xb2661c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb26620: r0 = Column()
    //     0xb26620: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb26624: mov             x3, x0
    // 0xb26628: r0 = Instance_Axis
    //     0xb26628: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb2662c: stur            x3, [fp, #-8]
    // 0xb26630: StoreField: r3->field_f = r0
    //     0xb26630: stur            w0, [x3, #0xf]
    // 0xb26634: r4 = Instance_MainAxisAlignment
    //     0xb26634: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb26638: ldr             x4, [x4, #0xa08]
    // 0xb2663c: StoreField: r3->field_13 = r4
    //     0xb2663c: stur            w4, [x3, #0x13]
    // 0xb26640: r5 = Instance_MainAxisSize
    //     0xb26640: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb26644: ldr             x5, [x5, #0xa10]
    // 0xb26648: ArrayStore: r3[0] = r5  ; List_4
    //     0xb26648: stur            w5, [x3, #0x17]
    // 0xb2664c: r6 = Instance_CrossAxisAlignment
    //     0xb2664c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb26650: ldr             x6, [x6, #0xa18]
    // 0xb26654: StoreField: r3->field_1b = r6
    //     0xb26654: stur            w6, [x3, #0x1b]
    // 0xb26658: r7 = Instance_VerticalDirection
    //     0xb26658: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb2665c: ldr             x7, [x7, #0xa20]
    // 0xb26660: StoreField: r3->field_23 = r7
    //     0xb26660: stur            w7, [x3, #0x23]
    // 0xb26664: r8 = Instance_Clip
    //     0xb26664: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb26668: ldr             x8, [x8, #0x38]
    // 0xb2666c: StoreField: r3->field_2b = r8
    //     0xb2666c: stur            w8, [x3, #0x2b]
    // 0xb26670: StoreField: r3->field_2f = rZR
    //     0xb26670: stur            xzr, [x3, #0x2f]
    // 0xb26674: ldur            x1, [fp, #-0x18]
    // 0xb26678: StoreField: r3->field_b = r1
    //     0xb26678: stur            w1, [x3, #0xb]
    // 0xb2667c: r1 = Null
    //     0xb2667c: mov             x1, NULL
    // 0xb26680: r2 = 4
    //     0xb26680: movz            x2, #0x4
    // 0xb26684: r0 = AllocateArray()
    //     0xb26684: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb26688: mov             x2, x0
    // 0xb2668c: ldur            x0, [fp, #-0x10]
    // 0xb26690: stur            x2, [fp, #-0x18]
    // 0xb26694: StoreField: r2->field_f = r0
    //     0xb26694: stur            w0, [x2, #0xf]
    // 0xb26698: ldur            x0, [fp, #-8]
    // 0xb2669c: StoreField: r2->field_13 = r0
    //     0xb2669c: stur            w0, [x2, #0x13]
    // 0xb266a0: r1 = <Widget>
    //     0xb266a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb266a4: r0 = AllocateGrowableArray()
    //     0xb266a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb266a8: mov             x1, x0
    // 0xb266ac: ldur            x0, [fp, #-0x18]
    // 0xb266b0: stur            x1, [fp, #-8]
    // 0xb266b4: StoreField: r1->field_f = r0
    //     0xb266b4: stur            w0, [x1, #0xf]
    // 0xb266b8: r0 = 4
    //     0xb266b8: movz            x0, #0x4
    // 0xb266bc: StoreField: r1->field_b = r0
    //     0xb266bc: stur            w0, [x1, #0xb]
    // 0xb266c0: r0 = Column()
    //     0xb266c0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb266c4: mov             x1, x0
    // 0xb266c8: r0 = Instance_Axis
    //     0xb266c8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb266cc: stur            x1, [fp, #-0x10]
    // 0xb266d0: StoreField: r1->field_f = r0
    //     0xb266d0: stur            w0, [x1, #0xf]
    // 0xb266d4: r0 = Instance_MainAxisAlignment
    //     0xb266d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb266d8: ldr             x0, [x0, #0xa08]
    // 0xb266dc: StoreField: r1->field_13 = r0
    //     0xb266dc: stur            w0, [x1, #0x13]
    // 0xb266e0: r0 = Instance_MainAxisSize
    //     0xb266e0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb266e4: ldr             x0, [x0, #0xa10]
    // 0xb266e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb266e8: stur            w0, [x1, #0x17]
    // 0xb266ec: r0 = Instance_CrossAxisAlignment
    //     0xb266ec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb266f0: ldr             x0, [x0, #0xa18]
    // 0xb266f4: StoreField: r1->field_1b = r0
    //     0xb266f4: stur            w0, [x1, #0x1b]
    // 0xb266f8: r0 = Instance_VerticalDirection
    //     0xb266f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb266fc: ldr             x0, [x0, #0xa20]
    // 0xb26700: StoreField: r1->field_23 = r0
    //     0xb26700: stur            w0, [x1, #0x23]
    // 0xb26704: r0 = Instance_Clip
    //     0xb26704: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb26708: ldr             x0, [x0, #0x38]
    // 0xb2670c: StoreField: r1->field_2b = r0
    //     0xb2670c: stur            w0, [x1, #0x2b]
    // 0xb26710: StoreField: r1->field_2f = rZR
    //     0xb26710: stur            xzr, [x1, #0x2f]
    // 0xb26714: ldur            x0, [fp, #-8]
    // 0xb26718: StoreField: r1->field_b = r0
    //     0xb26718: stur            w0, [x1, #0xb]
    // 0xb2671c: r0 = SafeArea()
    //     0xb2671c: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xb26720: mov             x1, x0
    // 0xb26724: r0 = true
    //     0xb26724: add             x0, NULL, #0x20  ; true
    // 0xb26728: stur            x1, [fp, #-8]
    // 0xb2672c: StoreField: r1->field_b = r0
    //     0xb2672c: stur            w0, [x1, #0xb]
    // 0xb26730: StoreField: r1->field_f = r0
    //     0xb26730: stur            w0, [x1, #0xf]
    // 0xb26734: StoreField: r1->field_13 = r0
    //     0xb26734: stur            w0, [x1, #0x13]
    // 0xb26738: ArrayStore: r1[0] = r0  ; List_4
    //     0xb26738: stur            w0, [x1, #0x17]
    // 0xb2673c: r2 = Instance_EdgeInsets
    //     0xb2673c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb26740: StoreField: r1->field_1b = r2
    //     0xb26740: stur            w2, [x1, #0x1b]
    // 0xb26744: r2 = false
    //     0xb26744: add             x2, NULL, #0x30  ; false
    // 0xb26748: StoreField: r1->field_1f = r2
    //     0xb26748: stur            w2, [x1, #0x1f]
    // 0xb2674c: ldur            x3, [fp, #-0x10]
    // 0xb26750: StoreField: r1->field_23 = r3
    //     0xb26750: stur            w3, [x1, #0x23]
    // 0xb26754: r0 = Scaffold()
    //     0xb26754: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xb26758: ldur            x1, [fp, #-8]
    // 0xb2675c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb2675c: stur            w1, [x0, #0x17]
    // 0xb26760: r1 = Instance_Color
    //     0xb26760: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb26764: StoreField: r0->field_33 = r1
    //     0xb26764: stur            w1, [x0, #0x33]
    // 0xb26768: r1 = true
    //     0xb26768: add             x1, NULL, #0x20  ; true
    // 0xb2676c: StoreField: r0->field_43 = r1
    //     0xb2676c: stur            w1, [x0, #0x43]
    // 0xb26770: r1 = false
    //     0xb26770: add             x1, NULL, #0x30  ; false
    // 0xb26774: StoreField: r0->field_b = r1
    //     0xb26774: stur            w1, [x0, #0xb]
    // 0xb26778: StoreField: r0->field_f = r1
    //     0xb26778: stur            w1, [x0, #0xf]
    // 0xb2677c: LeaveFrame
    //     0xb2677c: mov             SP, fp
    //     0xb26780: ldp             fp, lr, [SP], #0x10
    // 0xb26784: ret
    //     0xb26784: ret             
    // 0xb26788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb26788: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2678c: b               #0xb245c4
    // 0xb26790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26790: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26794: r9 = _pageController
    //     0xb26794: add             x9, PP, #0x57, lsl #12  ; [pp+0x57568] Field <_RatingReviewOnTapImageState@1512004270._pageController@1512004270>: late (offset: 0x1c)
    //     0xb26798: ldr             x9, [x9, #0x568]
    // 0xb2679c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb2679c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb267a0: SaveReg d0
    //     0xb267a0: str             q0, [SP, #-0x10]!
    // 0xb267a4: stp             x0, x2, [SP, #-0x10]!
    // 0xb267a8: r0 = AllocateDouble()
    //     0xb267a8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb267ac: mov             x1, x0
    // 0xb267b0: ldp             x0, x2, [SP], #0x10
    // 0xb267b4: RestoreReg d0
    //     0xb267b4: ldr             q0, [SP], #0x10
    // 0xb267b8: b               #0xb247b0
    // 0xb267bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb267bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb267c0: SaveReg d0
    //     0xb267c0: str             q0, [SP, #-0x10]!
    // 0xb267c4: stp             x0, x2, [SP, #-0x10]!
    // 0xb267c8: r0 = AllocateDouble()
    //     0xb267c8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb267cc: mov             x1, x0
    // 0xb267d0: ldp             x0, x2, [SP], #0x10
    // 0xb267d4: RestoreReg d0
    //     0xb267d4: ldr             q0, [SP], #0x10
    // 0xb267d8: b               #0xb2496c
    // 0xb267dc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb267dc: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb267e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb267e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb267e4: b               #0xb24abc
    // 0xb267e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb267e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb267ec: SaveReg d0
    //     0xb267ec: str             q0, [SP, #-0x10]!
    // 0xb267f0: stp             x4, x5, [SP, #-0x10]!
    // 0xb267f4: stp             x2, x3, [SP, #-0x10]!
    // 0xb267f8: stp             x0, x1, [SP, #-0x10]!
    // 0xb267fc: r0 = AllocateDouble()
    //     0xb267fc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb26800: mov             x6, x0
    // 0xb26804: ldp             x0, x1, [SP], #0x10
    // 0xb26808: ldp             x2, x3, [SP], #0x10
    // 0xb2680c: ldp             x4, x5, [SP], #0x10
    // 0xb26810: RestoreReg d0
    //     0xb26810: ldr             q0, [SP], #0x10
    // 0xb26814: b               #0xb24c1c
    // 0xb26818: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26818: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2681c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2681c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26820: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26820: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26824: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26824: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26828: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb26828: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb2682c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2682c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26830: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26834: SaveReg d0
    //     0xb26834: str             q0, [SP, #-0x10]!
    // 0xb26838: r0 = AllocateDouble()
    //     0xb26838: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb2683c: RestoreReg d0
    //     0xb2683c: ldr             q0, [SP], #0x10
    // 0xb26840: b               #0xb2578c
    // 0xb26844: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26844: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26848: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26848: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2684c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2684c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26850: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26850: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26854: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26854: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26858: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26858: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2685c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb2685c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb26860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26860: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb26864, size: 0xa8
    // 0xb26864: EnterFrame
    //     0xb26864: stp             fp, lr, [SP, #-0x10]!
    //     0xb26868: mov             fp, SP
    // 0xb2686c: ldr             x0, [fp, #0x10]
    // 0xb26870: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb26870: ldur            w1, [x0, #0x17]
    // 0xb26874: DecompressPointer r1
    //     0xb26874: add             x1, x1, HEAP, lsl #32
    // 0xb26878: CheckStackOverflow
    //     0xb26878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2687c: cmp             SP, x16
    //     0xb26880: b.ls            #0xb26900
    // 0xb26884: LoadField: r0 = r1->field_f
    //     0xb26884: ldur            w0, [x1, #0xf]
    // 0xb26888: DecompressPointer r0
    //     0xb26888: add             x0, x0, HEAP, lsl #32
    // 0xb2688c: LoadField: r3 = r0->field_2b
    //     0xb2688c: ldur            w3, [x0, #0x2b]
    // 0xb26890: DecompressPointer r3
    //     0xb26890: add             x3, x3, HEAP, lsl #32
    // 0xb26894: cmp             w3, NULL
    // 0xb26898: b.eq            #0xb268f0
    // 0xb2689c: LoadField: r2 = r1->field_13
    //     0xb2689c: ldur            w2, [x1, #0x13]
    // 0xb268a0: DecompressPointer r2
    //     0xb268a0: add             x2, x2, HEAP, lsl #32
    // 0xb268a4: LoadField: r1 = r0->field_b
    //     0xb268a4: ldur            w1, [x0, #0xb]
    // 0xb268a8: DecompressPointer r1
    //     0xb268a8: add             x1, x1, HEAP, lsl #32
    // 0xb268ac: cmp             w1, NULL
    // 0xb268b0: b.eq            #0xb26908
    // 0xb268b4: LoadField: r4 = r1->field_b
    //     0xb268b4: ldur            w4, [x1, #0xb]
    // 0xb268b8: DecompressPointer r4
    //     0xb268b8: add             x4, x4, HEAP, lsl #32
    // 0xb268bc: cmp             w4, NULL
    // 0xb268c0: b.ne            #0xb268cc
    // 0xb268c4: r1 = Null
    //     0xb268c4: mov             x1, NULL
    // 0xb268c8: b               #0xb268d4
    // 0xb268cc: LoadField: r1 = r4->field_b
    //     0xb268cc: ldur            w1, [x4, #0xb]
    // 0xb268d0: DecompressPointer r1
    //     0xb268d0: add             x1, x1, HEAP, lsl #32
    // 0xb268d4: cmp             w1, NULL
    // 0xb268d8: b.ne            #0xb268e4
    // 0xb268dc: r5 = ""
    //     0xb268dc: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb268e0: b               #0xb268e8
    // 0xb268e4: mov             x5, x1
    // 0xb268e8: mov             x1, x0
    // 0xb268ec: r0 = showMenuItem()
    //     0xb268ec: bl              #0xb2690c  ; [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem
    // 0xb268f0: r0 = Null
    //     0xb268f0: mov             x0, NULL
    // 0xb268f4: LeaveFrame
    //     0xb268f4: mov             SP, fp
    //     0xb268f8: ldp             fp, lr, [SP], #0x10
    // 0xb268fc: ret
    //     0xb268fc: ret             
    // 0xb26900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb26900: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb26904: b               #0xb26884
    // 0xb26908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb26908: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ showMenuItem(/* No info */) {
    // ** addr: 0xb2690c, size: 0x744
    // 0xb2690c: EnterFrame
    //     0xb2690c: stp             fp, lr, [SP, #-0x10]!
    //     0xb26910: mov             fp, SP
    // 0xb26914: AllocStack(0xa0)
    //     0xb26914: sub             SP, SP, #0xa0
    // 0xb26918: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0xb26918: mov             x0, x1
    //     0xb2691c: stur            x1, [fp, #-8]
    //     0xb26920: mov             x1, x2
    //     0xb26924: stur            x2, [fp, #-0x10]
    //     0xb26928: mov             x2, x5
    //     0xb2692c: stur            x3, [fp, #-0x18]
    //     0xb26930: stur            x5, [fp, #-0x20]
    // 0xb26934: CheckStackOverflow
    //     0xb26934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb26938: cmp             SP, x16
    //     0xb2693c: b.ls            #0xb26fc8
    // 0xb26940: r1 = 2
    //     0xb26940: movz            x1, #0x2
    // 0xb26944: r0 = AllocateContext()
    //     0xb26944: bl              #0x16f6108  ; AllocateContextStub
    // 0xb26948: mov             x4, x0
    // 0xb2694c: ldur            x3, [fp, #-8]
    // 0xb26950: stur            x4, [fp, #-0x28]
    // 0xb26954: StoreField: r4->field_f = r3
    //     0xb26954: stur            w3, [x4, #0xf]
    // 0xb26958: ldur            x2, [fp, #-0x20]
    // 0xb2695c: StoreField: r4->field_13 = r2
    //     0xb2695c: stur            w2, [x4, #0x13]
    // 0xb26960: LoadField: r1 = r3->field_27
    //     0xb26960: ldur            w1, [x3, #0x27]
    // 0xb26964: DecompressPointer r1
    //     0xb26964: add             x1, x1, HEAP, lsl #32
    // 0xb26968: r0 = LoadClassIdInstr(r1)
    //     0xb26968: ldur            x0, [x1, #-1]
    //     0xb2696c: ubfx            x0, x0, #0xc, #0x14
    // 0xb26970: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb26970: sub             lr, x0, #0xfe
    //     0xb26974: ldr             lr, [x21, lr, lsl #3]
    //     0xb26978: blr             lr
    // 0xb2697c: r1 = 60
    //     0xb2697c: movz            x1, #0x3c
    // 0xb26980: branchIfSmi(r0, 0xb2698c)
    //     0xb26980: tbz             w0, #0, #0xb2698c
    // 0xb26984: r1 = LoadClassIdInstr(r0)
    //     0xb26984: ldur            x1, [x0, #-1]
    //     0xb26988: ubfx            x1, x1, #0xc, #0x14
    // 0xb2698c: r16 = true
    //     0xb2698c: add             x16, NULL, #0x20  ; true
    // 0xb26990: stp             x16, x0, [SP]
    // 0xb26994: mov             x0, x1
    // 0xb26998: mov             lr, x0
    // 0xb2699c: ldr             lr, [x21, lr, lsl #3]
    // 0xb269a0: blr             lr
    // 0xb269a4: tbnz            w0, #4, #0xb269b0
    // 0xb269a8: d0 = 100.000000
    //     0xb269a8: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb269ac: b               #0xb269b8
    // 0xb269b0: d0 = 120.000000
    //     0xb269b0: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2fa38] IMM: double(120) from 0x405e000000000000
    //     0xb269b4: ldr             d0, [x17, #0xa38]
    // 0xb269b8: ldur            x0, [fp, #-0x18]
    // 0xb269bc: stur            d0, [fp, #-0x58]
    // 0xb269c0: r0 = BoxConstraints()
    //     0xb269c0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xb269c4: stur            x0, [fp, #-0x20]
    // 0xb269c8: StoreField: r0->field_7 = rZR
    //     0xb269c8: stur            xzr, [x0, #7]
    // 0xb269cc: ldur            d0, [fp, #-0x58]
    // 0xb269d0: StoreField: r0->field_f = d0
    //     0xb269d0: stur            d0, [x0, #0xf]
    // 0xb269d4: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb269d4: stur            xzr, [x0, #0x17]
    // 0xb269d8: d0 = inf
    //     0xb269d8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xb269dc: StoreField: r0->field_1f = d0
    //     0xb269dc: stur            d0, [x0, #0x1f]
    // 0xb269e0: ldur            x1, [fp, #-0x18]
    // 0xb269e4: cmp             w1, NULL
    // 0xb269e8: b.ne            #0xb269f4
    // 0xb269ec: r2 = Null
    //     0xb269ec: mov             x2, NULL
    // 0xb269f0: b               #0xb26a20
    // 0xb269f4: LoadField: d0 = r1->field_7
    //     0xb269f4: ldur            d0, [x1, #7]
    // 0xb269f8: r2 = inline_Allocate_Double()
    //     0xb269f8: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb269fc: add             x2, x2, #0x10
    //     0xb26a00: cmp             x3, x2
    //     0xb26a04: b.ls            #0xb26fd0
    //     0xb26a08: str             x2, [THR, #0x50]  ; THR::top
    //     0xb26a0c: sub             x2, x2, #0xf
    //     0xb26a10: movz            x3, #0xe15c
    //     0xb26a14: movk            x3, #0x3, lsl #16
    //     0xb26a18: stur            x3, [x2, #-1]
    // 0xb26a1c: StoreField: r2->field_7 = d0
    //     0xb26a1c: stur            d0, [x2, #7]
    // 0xb26a20: cmp             w2, NULL
    // 0xb26a24: b.ne            #0xb26a30
    // 0xb26a28: d0 = 0.000000
    //     0xb26a28: eor             v0.16b, v0.16b, v0.16b
    // 0xb26a2c: b               #0xb26a34
    // 0xb26a30: LoadField: d0 = r2->field_7
    //     0xb26a30: ldur            d0, [x2, #7]
    // 0xb26a34: stur            d0, [fp, #-0x70]
    // 0xb26a38: cmp             w1, NULL
    // 0xb26a3c: b.ne            #0xb26a48
    // 0xb26a40: r2 = Null
    //     0xb26a40: mov             x2, NULL
    // 0xb26a44: b               #0xb26a74
    // 0xb26a48: LoadField: d1 = r1->field_f
    //     0xb26a48: ldur            d1, [x1, #0xf]
    // 0xb26a4c: r2 = inline_Allocate_Double()
    //     0xb26a4c: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb26a50: add             x2, x2, #0x10
    //     0xb26a54: cmp             x3, x2
    //     0xb26a58: b.ls            #0xb26fec
    //     0xb26a5c: str             x2, [THR, #0x50]  ; THR::top
    //     0xb26a60: sub             x2, x2, #0xf
    //     0xb26a64: movz            x3, #0xe15c
    //     0xb26a68: movk            x3, #0x3, lsl #16
    //     0xb26a6c: stur            x3, [x2, #-1]
    // 0xb26a70: StoreField: r2->field_7 = d1
    //     0xb26a70: stur            d1, [x2, #7]
    // 0xb26a74: cmp             w2, NULL
    // 0xb26a78: b.ne            #0xb26a84
    // 0xb26a7c: d2 = 0.000000
    //     0xb26a7c: eor             v2.16b, v2.16b, v2.16b
    // 0xb26a80: b               #0xb26a8c
    // 0xb26a84: LoadField: d1 = r2->field_7
    //     0xb26a84: ldur            d1, [x2, #7]
    // 0xb26a88: mov             v2.16b, v1.16b
    // 0xb26a8c: d1 = 50.000000
    //     0xb26a8c: ldr             d1, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0xb26a90: fsub            d3, d2, d1
    // 0xb26a94: stur            d3, [fp, #-0x68]
    // 0xb26a98: cmp             w1, NULL
    // 0xb26a9c: b.ne            #0xb26aa8
    // 0xb26aa0: r2 = Null
    //     0xb26aa0: mov             x2, NULL
    // 0xb26aa4: b               #0xb26ad4
    // 0xb26aa8: LoadField: d2 = r1->field_7
    //     0xb26aa8: ldur            d2, [x1, #7]
    // 0xb26aac: r2 = inline_Allocate_Double()
    //     0xb26aac: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xb26ab0: add             x2, x2, #0x10
    //     0xb26ab4: cmp             x3, x2
    //     0xb26ab8: b.ls            #0xb27008
    //     0xb26abc: str             x2, [THR, #0x50]  ; THR::top
    //     0xb26ac0: sub             x2, x2, #0xf
    //     0xb26ac4: movz            x3, #0xe15c
    //     0xb26ac8: movk            x3, #0x3, lsl #16
    //     0xb26acc: stur            x3, [x2, #-1]
    // 0xb26ad0: StoreField: r2->field_7 = d2
    //     0xb26ad0: stur            d2, [x2, #7]
    // 0xb26ad4: cmp             w2, NULL
    // 0xb26ad8: b.ne            #0xb26ae4
    // 0xb26adc: d2 = 0.000000
    //     0xb26adc: eor             v2.16b, v2.16b, v2.16b
    // 0xb26ae0: b               #0xb26ae8
    // 0xb26ae4: LoadField: d2 = r2->field_7
    //     0xb26ae4: ldur            d2, [x2, #7]
    // 0xb26ae8: fadd            d4, d2, d1
    // 0xb26aec: stur            d4, [fp, #-0x60]
    // 0xb26af0: cmp             w1, NULL
    // 0xb26af4: b.ne            #0xb26b00
    // 0xb26af8: r1 = Null
    //     0xb26af8: mov             x1, NULL
    // 0xb26afc: b               #0xb26b2c
    // 0xb26b00: LoadField: d1 = r1->field_f
    //     0xb26b00: ldur            d1, [x1, #0xf]
    // 0xb26b04: r1 = inline_Allocate_Double()
    //     0xb26b04: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xb26b08: add             x1, x1, #0x10
    //     0xb26b0c: cmp             x2, x1
    //     0xb26b10: b.ls            #0xb2702c
    //     0xb26b14: str             x1, [THR, #0x50]  ; THR::top
    //     0xb26b18: sub             x1, x1, #0xf
    //     0xb26b1c: movz            x2, #0xe15c
    //     0xb26b20: movk            x2, #0x3, lsl #16
    //     0xb26b24: stur            x2, [x1, #-1]
    // 0xb26b28: StoreField: r1->field_7 = d1
    //     0xb26b28: stur            d1, [x1, #7]
    // 0xb26b2c: cmp             w1, NULL
    // 0xb26b30: b.ne            #0xb26b3c
    // 0xb26b34: d1 = 0.000000
    //     0xb26b34: eor             v1.16b, v1.16b, v1.16b
    // 0xb26b38: b               #0xb26b40
    // 0xb26b3c: LoadField: d1 = r1->field_7
    //     0xb26b3c: ldur            d1, [x1, #7]
    // 0xb26b40: ldur            x1, [fp, #-8]
    // 0xb26b44: ldur            x2, [fp, #-0x28]
    // 0xb26b48: stur            d1, [fp, #-0x58]
    // 0xb26b4c: r0 = RelativeRect()
    //     0xb26b4c: bl              #0x9abaf4  ; AllocateRelativeRectStub -> RelativeRect (size=0x28)
    // 0xb26b50: mov             x3, x0
    // 0xb26b54: ldur            d0, [fp, #-0x70]
    // 0xb26b58: stur            x3, [fp, #-0x18]
    // 0xb26b5c: StoreField: r3->field_7 = d0
    //     0xb26b5c: stur            d0, [x3, #7]
    // 0xb26b60: ldur            d0, [fp, #-0x68]
    // 0xb26b64: StoreField: r3->field_f = d0
    //     0xb26b64: stur            d0, [x3, #0xf]
    // 0xb26b68: ldur            d0, [fp, #-0x60]
    // 0xb26b6c: ArrayStore: r3[0] = d0  ; List_8
    //     0xb26b6c: stur            d0, [x3, #0x17]
    // 0xb26b70: ldur            d0, [fp, #-0x58]
    // 0xb26b74: StoreField: r3->field_1f = d0
    //     0xb26b74: stur            d0, [x3, #0x1f]
    // 0xb26b78: ldur            x4, [fp, #-8]
    // 0xb26b7c: LoadField: r1 = r4->field_27
    //     0xb26b7c: ldur            w1, [x4, #0x27]
    // 0xb26b80: DecompressPointer r1
    //     0xb26b80: add             x1, x1, HEAP, lsl #32
    // 0xb26b84: ldur            x5, [fp, #-0x28]
    // 0xb26b88: LoadField: r2 = r5->field_13
    //     0xb26b88: ldur            w2, [x5, #0x13]
    // 0xb26b8c: DecompressPointer r2
    //     0xb26b8c: add             x2, x2, HEAP, lsl #32
    // 0xb26b90: r0 = LoadClassIdInstr(r1)
    //     0xb26b90: ldur            x0, [x1, #-1]
    //     0xb26b94: ubfx            x0, x0, #0xc, #0x14
    // 0xb26b98: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb26b98: sub             lr, x0, #0xfe
    //     0xb26b9c: ldr             lr, [x21, lr, lsl #3]
    //     0xb26ba0: blr             lr
    // 0xb26ba4: r1 = 60
    //     0xb26ba4: movz            x1, #0x3c
    // 0xb26ba8: branchIfSmi(r0, 0xb26bb4)
    //     0xb26ba8: tbz             w0, #0, #0xb26bb4
    // 0xb26bac: r1 = LoadClassIdInstr(r0)
    //     0xb26bac: ldur            x1, [x0, #-1]
    //     0xb26bb0: ubfx            x1, x1, #0xc, #0x14
    // 0xb26bb4: r16 = true
    //     0xb26bb4: add             x16, NULL, #0x20  ; true
    // 0xb26bb8: stp             x16, x0, [SP]
    // 0xb26bbc: mov             x0, x1
    // 0xb26bc0: mov             lr, x0
    // 0xb26bc4: ldr             lr, [x21, lr, lsl #3]
    // 0xb26bc8: blr             lr
    // 0xb26bcc: tbnz            w0, #4, #0xb26bd8
    // 0xb26bd0: r4 = Null
    //     0xb26bd0: mov             x4, NULL
    // 0xb26bd4: b               #0xb26bec
    // 0xb26bd8: ldur            x2, [fp, #-0x28]
    // 0xb26bdc: r1 = Function '<anonymous closure>':.
    //     0xb26bdc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57570] AnonymousClosure: (0xb270e4), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xb2690c)
    //     0xb26be0: ldr             x1, [x1, #0x570]
    // 0xb26be4: r0 = AllocateClosure()
    //     0xb26be4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb26be8: mov             x4, x0
    // 0xb26bec: ldur            x0, [fp, #-8]
    // 0xb26bf0: ldur            x3, [fp, #-0x28]
    // 0xb26bf4: stur            x4, [fp, #-0x30]
    // 0xb26bf8: r1 = <Widget>
    //     0xb26bf8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb26bfc: r2 = 0
    //     0xb26bfc: movz            x2, #0
    // 0xb26c00: r0 = _GrowableList()
    //     0xb26c00: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb26c04: mov             x4, x0
    // 0xb26c08: ldur            x3, [fp, #-8]
    // 0xb26c0c: stur            x4, [fp, #-0x38]
    // 0xb26c10: LoadField: r1 = r3->field_27
    //     0xb26c10: ldur            w1, [x3, #0x27]
    // 0xb26c14: DecompressPointer r1
    //     0xb26c14: add             x1, x1, HEAP, lsl #32
    // 0xb26c18: ldur            x5, [fp, #-0x28]
    // 0xb26c1c: LoadField: r2 = r5->field_13
    //     0xb26c1c: ldur            w2, [x5, #0x13]
    // 0xb26c20: DecompressPointer r2
    //     0xb26c20: add             x2, x2, HEAP, lsl #32
    // 0xb26c24: r0 = LoadClassIdInstr(r1)
    //     0xb26c24: ldur            x0, [x1, #-1]
    //     0xb26c28: ubfx            x0, x0, #0xc, #0x14
    // 0xb26c2c: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb26c2c: sub             lr, x0, #0xfe
    //     0xb26c30: ldr             lr, [x21, lr, lsl #3]
    //     0xb26c34: blr             lr
    // 0xb26c38: r1 = 60
    //     0xb26c38: movz            x1, #0x3c
    // 0xb26c3c: branchIfSmi(r0, 0xb26c48)
    //     0xb26c3c: tbz             w0, #0, #0xb26c48
    // 0xb26c40: r1 = LoadClassIdInstr(r0)
    //     0xb26c40: ldur            x1, [x0, #-1]
    //     0xb26c44: ubfx            x1, x1, #0xc, #0x14
    // 0xb26c48: r16 = true
    //     0xb26c48: add             x16, NULL, #0x20  ; true
    // 0xb26c4c: stp             x16, x0, [SP]
    // 0xb26c50: mov             x0, x1
    // 0xb26c54: mov             lr, x0
    // 0xb26c58: ldr             lr, [x21, lr, lsl #3]
    // 0xb26c5c: blr             lr
    // 0xb26c60: tbnz            w0, #4, #0xb26cc4
    // 0xb26c64: ldur            x0, [fp, #-0x38]
    // 0xb26c68: LoadField: r1 = r0->field_b
    //     0xb26c68: ldur            w1, [x0, #0xb]
    // 0xb26c6c: LoadField: r2 = r0->field_f
    //     0xb26c6c: ldur            w2, [x0, #0xf]
    // 0xb26c70: DecompressPointer r2
    //     0xb26c70: add             x2, x2, HEAP, lsl #32
    // 0xb26c74: LoadField: r3 = r2->field_b
    //     0xb26c74: ldur            w3, [x2, #0xb]
    // 0xb26c78: r2 = LoadInt32Instr(r1)
    //     0xb26c78: sbfx            x2, x1, #1, #0x1f
    // 0xb26c7c: stur            x2, [fp, #-0x40]
    // 0xb26c80: r1 = LoadInt32Instr(r3)
    //     0xb26c80: sbfx            x1, x3, #1, #0x1f
    // 0xb26c84: cmp             x2, x1
    // 0xb26c88: b.ne            #0xb26c94
    // 0xb26c8c: mov             x1, x0
    // 0xb26c90: r0 = _growToNextCapacity()
    //     0xb26c90: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb26c94: ldur            x0, [fp, #-0x38]
    // 0xb26c98: ldur            x1, [fp, #-0x40]
    // 0xb26c9c: add             x2, x1, #1
    // 0xb26ca0: lsl             x3, x2, #1
    // 0xb26ca4: StoreField: r0->field_b = r3
    //     0xb26ca4: stur            w3, [x0, #0xb]
    // 0xb26ca8: LoadField: r2 = r0->field_f
    //     0xb26ca8: ldur            w2, [x0, #0xf]
    // 0xb26cac: DecompressPointer r2
    //     0xb26cac: add             x2, x2, HEAP, lsl #32
    // 0xb26cb0: add             x3, x2, x1, lsl #2
    // 0xb26cb4: r16 = Instance_Icon
    //     0xb26cb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa48] Obj!Icon@d65eb1
    //     0xb26cb8: ldr             x16, [x16, #0xa48]
    // 0xb26cbc: StoreField: r3->field_f = r16
    //     0xb26cbc: stur            w16, [x3, #0xf]
    // 0xb26cc0: b               #0xb26cc8
    // 0xb26cc4: ldur            x0, [fp, #-0x38]
    // 0xb26cc8: LoadField: r1 = r0->field_b
    //     0xb26cc8: ldur            w1, [x0, #0xb]
    // 0xb26ccc: LoadField: r2 = r0->field_f
    //     0xb26ccc: ldur            w2, [x0, #0xf]
    // 0xb26cd0: DecompressPointer r2
    //     0xb26cd0: add             x2, x2, HEAP, lsl #32
    // 0xb26cd4: LoadField: r3 = r2->field_b
    //     0xb26cd4: ldur            w3, [x2, #0xb]
    // 0xb26cd8: r2 = LoadInt32Instr(r1)
    //     0xb26cd8: sbfx            x2, x1, #1, #0x1f
    // 0xb26cdc: stur            x2, [fp, #-0x40]
    // 0xb26ce0: r1 = LoadInt32Instr(r3)
    //     0xb26ce0: sbfx            x1, x3, #1, #0x1f
    // 0xb26ce4: cmp             x2, x1
    // 0xb26ce8: b.ne            #0xb26cf4
    // 0xb26cec: mov             x1, x0
    // 0xb26cf0: r0 = _growToNextCapacity()
    //     0xb26cf0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb26cf4: ldur            x1, [fp, #-8]
    // 0xb26cf8: ldur            x4, [fp, #-0x28]
    // 0xb26cfc: ldur            x3, [fp, #-0x38]
    // 0xb26d00: ldur            x0, [fp, #-0x40]
    // 0xb26d04: add             x2, x0, #1
    // 0xb26d08: lsl             x5, x2, #1
    // 0xb26d0c: StoreField: r3->field_b = r5
    //     0xb26d0c: stur            w5, [x3, #0xb]
    // 0xb26d10: LoadField: r2 = r3->field_f
    //     0xb26d10: ldur            w2, [x3, #0xf]
    // 0xb26d14: DecompressPointer r2
    //     0xb26d14: add             x2, x2, HEAP, lsl #32
    // 0xb26d18: add             x5, x2, x0, lsl #2
    // 0xb26d1c: r16 = Instance_SizedBox
    //     0xb26d1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb26d20: ldr             x16, [x16, #0xa50]
    // 0xb26d24: StoreField: r5->field_f = r16
    //     0xb26d24: stur            w16, [x5, #0xf]
    // 0xb26d28: LoadField: r0 = r1->field_27
    //     0xb26d28: ldur            w0, [x1, #0x27]
    // 0xb26d2c: DecompressPointer r0
    //     0xb26d2c: add             x0, x0, HEAP, lsl #32
    // 0xb26d30: LoadField: r2 = r4->field_13
    //     0xb26d30: ldur            w2, [x4, #0x13]
    // 0xb26d34: DecompressPointer r2
    //     0xb26d34: add             x2, x2, HEAP, lsl #32
    // 0xb26d38: r1 = LoadClassIdInstr(r0)
    //     0xb26d38: ldur            x1, [x0, #-1]
    //     0xb26d3c: ubfx            x1, x1, #0xc, #0x14
    // 0xb26d40: mov             x16, x0
    // 0xb26d44: mov             x0, x1
    // 0xb26d48: mov             x1, x16
    // 0xb26d4c: r0 = GDT[cid_x0 + -0xfe]()
    //     0xb26d4c: sub             lr, x0, #0xfe
    //     0xb26d50: ldr             lr, [x21, lr, lsl #3]
    //     0xb26d54: blr             lr
    // 0xb26d58: r1 = 60
    //     0xb26d58: movz            x1, #0x3c
    // 0xb26d5c: branchIfSmi(r0, 0xb26d68)
    //     0xb26d5c: tbz             w0, #0, #0xb26d68
    // 0xb26d60: r1 = LoadClassIdInstr(r0)
    //     0xb26d60: ldur            x1, [x0, #-1]
    //     0xb26d64: ubfx            x1, x1, #0xc, #0x14
    // 0xb26d68: r16 = true
    //     0xb26d68: add             x16, NULL, #0x20  ; true
    // 0xb26d6c: stp             x16, x0, [SP]
    // 0xb26d70: mov             x0, x1
    // 0xb26d74: mov             lr, x0
    // 0xb26d78: ldr             lr, [x21, lr, lsl #3]
    // 0xb26d7c: blr             lr
    // 0xb26d80: tbnz            w0, #4, #0xb26d90
    // 0xb26d84: r0 = "Flagged"
    //     0xb26d84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa58] "Flagged"
    //     0xb26d88: ldr             x0, [x0, #0xa58]
    // 0xb26d8c: b               #0xb26d98
    // 0xb26d90: r0 = "Flag as abusive"
    //     0xb26d90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa60] "Flag as abusive"
    //     0xb26d94: ldr             x0, [x0, #0xa60]
    // 0xb26d98: ldur            x1, [fp, #-0x10]
    // 0xb26d9c: stur            x0, [fp, #-8]
    // 0xb26da0: r0 = of()
    //     0xb26da0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb26da4: LoadField: r1 = r0->field_87
    //     0xb26da4: ldur            w1, [x0, #0x87]
    // 0xb26da8: DecompressPointer r1
    //     0xb26da8: add             x1, x1, HEAP, lsl #32
    // 0xb26dac: LoadField: r0 = r1->field_33
    //     0xb26dac: ldur            w0, [x1, #0x33]
    // 0xb26db0: DecompressPointer r0
    //     0xb26db0: add             x0, x0, HEAP, lsl #32
    // 0xb26db4: cmp             w0, NULL
    // 0xb26db8: b.ne            #0xb26dc4
    // 0xb26dbc: r2 = Null
    //     0xb26dbc: mov             x2, NULL
    // 0xb26dc0: b               #0xb26de8
    // 0xb26dc4: r16 = 12.000000
    //     0xb26dc4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb26dc8: ldr             x16, [x16, #0x9e8]
    // 0xb26dcc: r30 = Instance_Color
    //     0xb26dcc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb26dd0: stp             lr, x16, [SP]
    // 0xb26dd4: mov             x1, x0
    // 0xb26dd8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb26dd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb26ddc: ldr             x4, [x4, #0xaa0]
    // 0xb26de0: r0 = copyWith()
    //     0xb26de0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb26de4: mov             x2, x0
    // 0xb26de8: ldur            x1, [fp, #-0x38]
    // 0xb26dec: ldur            x0, [fp, #-8]
    // 0xb26df0: stur            x2, [fp, #-0x48]
    // 0xb26df4: r0 = Text()
    //     0xb26df4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb26df8: mov             x2, x0
    // 0xb26dfc: ldur            x0, [fp, #-8]
    // 0xb26e00: stur            x2, [fp, #-0x50]
    // 0xb26e04: StoreField: r2->field_b = r0
    //     0xb26e04: stur            w0, [x2, #0xb]
    // 0xb26e08: ldur            x0, [fp, #-0x48]
    // 0xb26e0c: StoreField: r2->field_13 = r0
    //     0xb26e0c: stur            w0, [x2, #0x13]
    // 0xb26e10: ldur            x0, [fp, #-0x38]
    // 0xb26e14: LoadField: r1 = r0->field_b
    //     0xb26e14: ldur            w1, [x0, #0xb]
    // 0xb26e18: LoadField: r3 = r0->field_f
    //     0xb26e18: ldur            w3, [x0, #0xf]
    // 0xb26e1c: DecompressPointer r3
    //     0xb26e1c: add             x3, x3, HEAP, lsl #32
    // 0xb26e20: LoadField: r4 = r3->field_b
    //     0xb26e20: ldur            w4, [x3, #0xb]
    // 0xb26e24: r3 = LoadInt32Instr(r1)
    //     0xb26e24: sbfx            x3, x1, #1, #0x1f
    // 0xb26e28: stur            x3, [fp, #-0x40]
    // 0xb26e2c: r1 = LoadInt32Instr(r4)
    //     0xb26e2c: sbfx            x1, x4, #1, #0x1f
    // 0xb26e30: cmp             x3, x1
    // 0xb26e34: b.ne            #0xb26e40
    // 0xb26e38: mov             x1, x0
    // 0xb26e3c: r0 = _growToNextCapacity()
    //     0xb26e3c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb26e40: ldur            x4, [fp, #-0x30]
    // 0xb26e44: ldur            x2, [fp, #-0x38]
    // 0xb26e48: ldur            x3, [fp, #-0x40]
    // 0xb26e4c: add             x0, x3, #1
    // 0xb26e50: lsl             x1, x0, #1
    // 0xb26e54: StoreField: r2->field_b = r1
    //     0xb26e54: stur            w1, [x2, #0xb]
    // 0xb26e58: LoadField: r1 = r2->field_f
    //     0xb26e58: ldur            w1, [x2, #0xf]
    // 0xb26e5c: DecompressPointer r1
    //     0xb26e5c: add             x1, x1, HEAP, lsl #32
    // 0xb26e60: ldur            x0, [fp, #-0x50]
    // 0xb26e64: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb26e64: add             x25, x1, x3, lsl #2
    //     0xb26e68: add             x25, x25, #0xf
    //     0xb26e6c: str             w0, [x25]
    //     0xb26e70: tbz             w0, #0, #0xb26e8c
    //     0xb26e74: ldurb           w16, [x1, #-1]
    //     0xb26e78: ldurb           w17, [x0, #-1]
    //     0xb26e7c: and             x16, x17, x16, lsr #2
    //     0xb26e80: tst             x16, HEAP, lsr #32
    //     0xb26e84: b.eq            #0xb26e8c
    //     0xb26e88: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb26e8c: r0 = Row()
    //     0xb26e8c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb26e90: mov             x2, x0
    // 0xb26e94: r0 = Instance_Axis
    //     0xb26e94: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb26e98: stur            x2, [fp, #-8]
    // 0xb26e9c: StoreField: r2->field_f = r0
    //     0xb26e9c: stur            w0, [x2, #0xf]
    // 0xb26ea0: r0 = Instance_MainAxisAlignment
    //     0xb26ea0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb26ea4: ldr             x0, [x0, #0xa08]
    // 0xb26ea8: StoreField: r2->field_13 = r0
    //     0xb26ea8: stur            w0, [x2, #0x13]
    // 0xb26eac: r0 = Instance_MainAxisSize
    //     0xb26eac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb26eb0: ldr             x0, [x0, #0xa10]
    // 0xb26eb4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb26eb4: stur            w0, [x2, #0x17]
    // 0xb26eb8: r0 = Instance_CrossAxisAlignment
    //     0xb26eb8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb26ebc: ldr             x0, [x0, #0xa18]
    // 0xb26ec0: StoreField: r2->field_1b = r0
    //     0xb26ec0: stur            w0, [x2, #0x1b]
    // 0xb26ec4: r0 = Instance_VerticalDirection
    //     0xb26ec4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb26ec8: ldr             x0, [x0, #0xa20]
    // 0xb26ecc: StoreField: r2->field_23 = r0
    //     0xb26ecc: stur            w0, [x2, #0x23]
    // 0xb26ed0: r0 = Instance_Clip
    //     0xb26ed0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb26ed4: ldr             x0, [x0, #0x38]
    // 0xb26ed8: StoreField: r2->field_2b = r0
    //     0xb26ed8: stur            w0, [x2, #0x2b]
    // 0xb26edc: StoreField: r2->field_2f = rZR
    //     0xb26edc: stur            xzr, [x2, #0x2f]
    // 0xb26ee0: ldur            x0, [fp, #-0x38]
    // 0xb26ee4: StoreField: r2->field_b = r0
    //     0xb26ee4: stur            w0, [x2, #0xb]
    // 0xb26ee8: r1 = <String>
    //     0xb26ee8: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb26eec: r0 = PopupMenuItem()
    //     0xb26eec: bl              #0x9abca4  ; AllocatePopupMenuItemStub -> PopupMenuItem<X0> (size=0x38)
    // 0xb26ef0: mov             x3, x0
    // 0xb26ef4: r0 = "flag"
    //     0xb26ef4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb26ef8: ldr             x0, [x0, #0xa68]
    // 0xb26efc: stur            x3, [fp, #-0x38]
    // 0xb26f00: StoreField: r3->field_f = r0
    //     0xb26f00: stur            w0, [x3, #0xf]
    // 0xb26f04: ldur            x0, [fp, #-0x30]
    // 0xb26f08: StoreField: r3->field_13 = r0
    //     0xb26f08: stur            w0, [x3, #0x13]
    // 0xb26f0c: r0 = true
    //     0xb26f0c: add             x0, NULL, #0x20  ; true
    // 0xb26f10: ArrayStore: r3[0] = r0  ; List_4
    //     0xb26f10: stur            w0, [x3, #0x17]
    // 0xb26f14: d0 = 25.000000
    //     0xb26f14: fmov            d0, #25.00000000
    // 0xb26f18: StoreField: r3->field_1b = d0
    //     0xb26f18: stur            d0, [x3, #0x1b]
    // 0xb26f1c: ldur            x0, [fp, #-8]
    // 0xb26f20: StoreField: r3->field_33 = r0
    //     0xb26f20: stur            w0, [x3, #0x33]
    // 0xb26f24: r1 = Null
    //     0xb26f24: mov             x1, NULL
    // 0xb26f28: r2 = 2
    //     0xb26f28: movz            x2, #0x2
    // 0xb26f2c: r0 = AllocateArray()
    //     0xb26f2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb26f30: mov             x2, x0
    // 0xb26f34: ldur            x0, [fp, #-0x38]
    // 0xb26f38: stur            x2, [fp, #-8]
    // 0xb26f3c: StoreField: r2->field_f = r0
    //     0xb26f3c: stur            w0, [x2, #0xf]
    // 0xb26f40: r1 = <PopupMenuEntry<String>>
    //     0xb26f40: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa70] TypeArguments: <PopupMenuEntry<String>>
    //     0xb26f44: ldr             x1, [x1, #0xa70]
    // 0xb26f48: r0 = AllocateGrowableArray()
    //     0xb26f48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb26f4c: mov             x1, x0
    // 0xb26f50: ldur            x0, [fp, #-8]
    // 0xb26f54: StoreField: r1->field_f = r0
    //     0xb26f54: stur            w0, [x1, #0xf]
    // 0xb26f58: r0 = 2
    //     0xb26f58: movz            x0, #0x2
    // 0xb26f5c: StoreField: r1->field_b = r0
    //     0xb26f5c: stur            w0, [x1, #0xb]
    // 0xb26f60: r16 = <String>
    //     0xb26f60: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb26f64: ldur            lr, [fp, #-0x10]
    // 0xb26f68: stp             lr, x16, [SP, #0x20]
    // 0xb26f6c: ldur            x16, [fp, #-0x18]
    // 0xb26f70: stp             x16, x1, [SP, #0x10]
    // 0xb26f74: r16 = Instance_Color
    //     0xb26f74: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb26f78: ldur            lr, [fp, #-0x20]
    // 0xb26f7c: stp             lr, x16, [SP]
    // 0xb26f80: r4 = const [0x1, 0x5, 0x5, 0x3, color, 0x3, constraints, 0x4, null]
    //     0xb26f80: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fa78] List(9) [0x1, 0x5, 0x5, 0x3, "color", 0x3, "constraints", 0x4, Null]
    //     0xb26f84: ldr             x4, [x4, #0xa78]
    // 0xb26f88: r0 = showMenu()
    //     0xb26f88: bl              #0x9ab6c4  ; [package:flutter/src/material/popup_menu.dart] ::showMenu
    // 0xb26f8c: ldur            x2, [fp, #-0x28]
    // 0xb26f90: r1 = Function '<anonymous closure>':.
    //     0xb26f90: add             x1, PP, #0x57, lsl #12  ; [pp+0x57578] AnonymousClosure: (0xb27050), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xb2690c)
    //     0xb26f94: ldr             x1, [x1, #0x578]
    // 0xb26f98: stur            x0, [fp, #-8]
    // 0xb26f9c: r0 = AllocateClosure()
    //     0xb26f9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb26fa0: r16 = <Null?>
    //     0xb26fa0: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xb26fa4: ldur            lr, [fp, #-8]
    // 0xb26fa8: stp             lr, x16, [SP, #8]
    // 0xb26fac: str             x0, [SP]
    // 0xb26fb0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb26fb0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb26fb4: r0 = then()
    //     0xb26fb4: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xb26fb8: r0 = Null
    //     0xb26fb8: mov             x0, NULL
    // 0xb26fbc: LeaveFrame
    //     0xb26fbc: mov             SP, fp
    //     0xb26fc0: ldp             fp, lr, [SP], #0x10
    // 0xb26fc4: ret
    //     0xb26fc4: ret             
    // 0xb26fc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb26fc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb26fcc: b               #0xb26940
    // 0xb26fd0: SaveReg d0
    //     0xb26fd0: str             q0, [SP, #-0x10]!
    // 0xb26fd4: stp             x0, x1, [SP, #-0x10]!
    // 0xb26fd8: r0 = AllocateDouble()
    //     0xb26fd8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb26fdc: mov             x2, x0
    // 0xb26fe0: ldp             x0, x1, [SP], #0x10
    // 0xb26fe4: RestoreReg d0
    //     0xb26fe4: ldr             q0, [SP], #0x10
    // 0xb26fe8: b               #0xb26a1c
    // 0xb26fec: stp             q0, q1, [SP, #-0x20]!
    // 0xb26ff0: stp             x0, x1, [SP, #-0x10]!
    // 0xb26ff4: r0 = AllocateDouble()
    //     0xb26ff4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb26ff8: mov             x2, x0
    // 0xb26ffc: ldp             x0, x1, [SP], #0x10
    // 0xb27000: ldp             q0, q1, [SP], #0x20
    // 0xb27004: b               #0xb26a70
    // 0xb27008: stp             q2, q3, [SP, #-0x20]!
    // 0xb2700c: stp             q0, q1, [SP, #-0x20]!
    // 0xb27010: stp             x0, x1, [SP, #-0x10]!
    // 0xb27014: r0 = AllocateDouble()
    //     0xb27014: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb27018: mov             x2, x0
    // 0xb2701c: ldp             x0, x1, [SP], #0x10
    // 0xb27020: ldp             q0, q1, [SP], #0x20
    // 0xb27024: ldp             q2, q3, [SP], #0x20
    // 0xb27028: b               #0xb26ad0
    // 0xb2702c: stp             q3, q4, [SP, #-0x20]!
    // 0xb27030: stp             q0, q1, [SP, #-0x20]!
    // 0xb27034: SaveReg r0
    //     0xb27034: str             x0, [SP, #-8]!
    // 0xb27038: r0 = AllocateDouble()
    //     0xb27038: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb2703c: mov             x1, x0
    // 0xb27040: RestoreReg r0
    //     0xb27040: ldr             x0, [SP], #8
    // 0xb27044: ldp             q0, q1, [SP], #0x20
    // 0xb27048: ldp             q3, q4, [SP], #0x20
    // 0xb2704c: b               #0xb26b28
  }
  [closure] Null <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb27050, size: 0x94
    // 0xb27050: EnterFrame
    //     0xb27050: stp             fp, lr, [SP, #-0x10]!
    //     0xb27054: mov             fp, SP
    // 0xb27058: AllocStack(0x20)
    //     0xb27058: sub             SP, SP, #0x20
    // 0xb2705c: SetupParameters()
    //     0xb2705c: ldr             x0, [fp, #0x18]
    //     0xb27060: ldur            w2, [x0, #0x17]
    //     0xb27064: add             x2, x2, HEAP, lsl #32
    //     0xb27068: stur            x2, [fp, #-8]
    // 0xb2706c: CheckStackOverflow
    //     0xb2706c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb27070: cmp             SP, x16
    //     0xb27074: b.ls            #0xb270dc
    // 0xb27078: ldr             x0, [fp, #0x10]
    // 0xb2707c: r1 = LoadClassIdInstr(r0)
    //     0xb2707c: ldur            x1, [x0, #-1]
    //     0xb27080: ubfx            x1, x1, #0xc, #0x14
    // 0xb27084: r16 = "flag"
    //     0xb27084: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa68] "flag"
    //     0xb27088: ldr             x16, [x16, #0xa68]
    // 0xb2708c: stp             x16, x0, [SP]
    // 0xb27090: mov             x0, x1
    // 0xb27094: mov             lr, x0
    // 0xb27098: ldr             lr, [x21, lr, lsl #3]
    // 0xb2709c: blr             lr
    // 0xb270a0: tbnz            w0, #4, #0xb270cc
    // 0xb270a4: ldur            x2, [fp, #-8]
    // 0xb270a8: LoadField: r0 = r2->field_f
    //     0xb270a8: ldur            w0, [x2, #0xf]
    // 0xb270ac: DecompressPointer r0
    //     0xb270ac: add             x0, x0, HEAP, lsl #32
    // 0xb270b0: stur            x0, [fp, #-0x10]
    // 0xb270b4: r1 = Function '<anonymous closure>':.
    //     0xb270b4: add             x1, PP, #0x57, lsl #12  ; [pp+0x57580] AnonymousClosure: (0xaa97e4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xb270b8: ldr             x1, [x1, #0x580]
    // 0xb270bc: r0 = AllocateClosure()
    //     0xb270bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb270c0: ldur            x1, [fp, #-0x10]
    // 0xb270c4: mov             x2, x0
    // 0xb270c8: r0 = setState()
    //     0xb270c8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb270cc: r0 = Null
    //     0xb270cc: mov             x0, NULL
    // 0xb270d0: LeaveFrame
    //     0xb270d0: mov             SP, fp
    //     0xb270d4: ldp             fp, lr, [SP], #0x10
    // 0xb270d8: ret
    //     0xb270d8: ret             
    // 0xb270dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb270dc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb270e0: b               #0xb27078
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb270e4, size: 0x60
    // 0xb270e4: EnterFrame
    //     0xb270e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb270e8: mov             fp, SP
    // 0xb270ec: AllocStack(0x8)
    //     0xb270ec: sub             SP, SP, #8
    // 0xb270f0: SetupParameters()
    //     0xb270f0: ldr             x0, [fp, #0x10]
    //     0xb270f4: ldur            w2, [x0, #0x17]
    //     0xb270f8: add             x2, x2, HEAP, lsl #32
    // 0xb270fc: CheckStackOverflow
    //     0xb270fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb27100: cmp             SP, x16
    //     0xb27104: b.ls            #0xb2713c
    // 0xb27108: LoadField: r0 = r2->field_f
    //     0xb27108: ldur            w0, [x2, #0xf]
    // 0xb2710c: DecompressPointer r0
    //     0xb2710c: add             x0, x0, HEAP, lsl #32
    // 0xb27110: stur            x0, [fp, #-8]
    // 0xb27114: r1 = Function '<anonymous closure>':.
    //     0xb27114: add             x1, PP, #0x57, lsl #12  ; [pp+0x57588] AnonymousClosure: (0xaa97e4), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::showMenuItem (0xaa992c)
    //     0xb27118: ldr             x1, [x1, #0x588]
    // 0xb2711c: r0 = AllocateClosure()
    //     0xb2711c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb27120: ldur            x1, [fp, #-8]
    // 0xb27124: mov             x2, x0
    // 0xb27128: r0 = setState()
    //     0xb27128: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb2712c: r0 = Null
    //     0xb2712c: mov             x0, NULL
    // 0xb27130: LeaveFrame
    //     0xb27130: mov             SP, fp
    //     0xb27134: ldp             fp, lr, [SP], #0x10
    // 0xb27138: ret
    //     0xb27138: ret             
    // 0xb2713c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb2713c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb27140: b               #0xb27108
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb27144, size: 0xc8
    // 0xb27144: EnterFrame
    //     0xb27144: stp             fp, lr, [SP, #-0x10]!
    //     0xb27148: mov             fp, SP
    // 0xb2714c: ldr             x0, [fp, #0x10]
    // 0xb27150: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb27150: ldur            w1, [x0, #0x17]
    // 0xb27154: DecompressPointer r1
    //     0xb27154: add             x1, x1, HEAP, lsl #32
    // 0xb27158: CheckStackOverflow
    //     0xb27158: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2715c: cmp             SP, x16
    //     0xb27160: b.ls            #0xb271f4
    // 0xb27164: LoadField: r0 = r1->field_f
    //     0xb27164: ldur            w0, [x1, #0xf]
    // 0xb27168: DecompressPointer r0
    //     0xb27168: add             x0, x0, HEAP, lsl #32
    // 0xb2716c: LoadField: r1 = r0->field_13
    //     0xb2716c: ldur            x1, [x0, #0x13]
    // 0xb27170: LoadField: r2 = r0->field_b
    //     0xb27170: ldur            w2, [x0, #0xb]
    // 0xb27174: DecompressPointer r2
    //     0xb27174: add             x2, x2, HEAP, lsl #32
    // 0xb27178: cmp             w2, NULL
    // 0xb2717c: b.eq            #0xb271fc
    // 0xb27180: LoadField: r3 = r2->field_b
    //     0xb27180: ldur            w3, [x2, #0xb]
    // 0xb27184: DecompressPointer r3
    //     0xb27184: add             x3, x3, HEAP, lsl #32
    // 0xb27188: cmp             w3, NULL
    // 0xb2718c: b.ne            #0xb27198
    // 0xb27190: r2 = Null
    //     0xb27190: mov             x2, NULL
    // 0xb27194: b               #0xb271a8
    // 0xb27198: LoadField: r2 = r3->field_1b
    //     0xb27198: ldur            w2, [x3, #0x1b]
    // 0xb2719c: DecompressPointer r2
    //     0xb2719c: add             x2, x2, HEAP, lsl #32
    // 0xb271a0: LoadField: r3 = r2->field_b
    //     0xb271a0: ldur            w3, [x2, #0xb]
    // 0xb271a4: mov             x2, x3
    // 0xb271a8: cmp             w2, NULL
    // 0xb271ac: b.ne            #0xb271b8
    // 0xb271b0: r2 = 0
    //     0xb271b0: movz            x2, #0
    // 0xb271b4: b               #0xb271c0
    // 0xb271b8: r3 = LoadInt32Instr(r2)
    //     0xb271b8: sbfx            x3, x2, #1, #0x1f
    // 0xb271bc: mov             x2, x3
    // 0xb271c0: sub             x3, x2, #1
    // 0xb271c4: cmp             x1, x3
    // 0xb271c8: b.ge            #0xb271e4
    // 0xb271cc: LoadField: r1 = r0->field_1b
    //     0xb271cc: ldur            w1, [x0, #0x1b]
    // 0xb271d0: DecompressPointer r1
    //     0xb271d0: add             x1, x1, HEAP, lsl #32
    // 0xb271d4: r16 = Sentinel
    //     0xb271d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb271d8: cmp             w1, w16
    // 0xb271dc: b.eq            #0xb27200
    // 0xb271e0: r0 = nextPage()
    //     0xb271e0: bl              #0xaa5ed0  ; [package:flutter/src/widgets/page_view.dart] PageController::nextPage
    // 0xb271e4: r0 = Null
    //     0xb271e4: mov             x0, NULL
    // 0xb271e8: LeaveFrame
    //     0xb271e8: mov             SP, fp
    //     0xb271ec: ldp             fp, lr, [SP], #0x10
    // 0xb271f0: ret
    //     0xb271f0: ret             
    // 0xb271f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb271f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb271f8: b               #0xb27164
    // 0xb271fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb271fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb27200: r9 = _pageController
    //     0xb27200: add             x9, PP, #0x57, lsl #12  ; [pp+0x57568] Field <_RatingReviewOnTapImageState@1512004270._pageController@1512004270>: late (offset: 0x1c)
    //     0xb27204: ldr             x9, [x9, #0x568]
    // 0xb27208: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb27208: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb2720c, size: 0x70
    // 0xb2720c: EnterFrame
    //     0xb2720c: stp             fp, lr, [SP, #-0x10]!
    //     0xb27210: mov             fp, SP
    // 0xb27214: ldr             x0, [fp, #0x10]
    // 0xb27218: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb27218: ldur            w1, [x0, #0x17]
    // 0xb2721c: DecompressPointer r1
    //     0xb2721c: add             x1, x1, HEAP, lsl #32
    // 0xb27220: CheckStackOverflow
    //     0xb27220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb27224: cmp             SP, x16
    //     0xb27228: b.ls            #0xb27268
    // 0xb2722c: LoadField: r0 = r1->field_f
    //     0xb2722c: ldur            w0, [x1, #0xf]
    // 0xb27230: DecompressPointer r0
    //     0xb27230: add             x0, x0, HEAP, lsl #32
    // 0xb27234: LoadField: r1 = r0->field_13
    //     0xb27234: ldur            x1, [x0, #0x13]
    // 0xb27238: cmp             x1, #0
    // 0xb2723c: b.le            #0xb27258
    // 0xb27240: LoadField: r1 = r0->field_1b
    //     0xb27240: ldur            w1, [x0, #0x1b]
    // 0xb27244: DecompressPointer r1
    //     0xb27244: add             x1, x1, HEAP, lsl #32
    // 0xb27248: r16 = Sentinel
    //     0xb27248: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb2724c: cmp             w1, w16
    // 0xb27250: b.eq            #0xb27270
    // 0xb27254: r0 = previousPage()
    //     0xb27254: bl              #0xaa6010  ; [package:flutter/src/widgets/page_view.dart] PageController::previousPage
    // 0xb27258: r0 = Null
    //     0xb27258: mov             x0, NULL
    // 0xb2725c: LeaveFrame
    //     0xb2725c: mov             SP, fp
    //     0xb27260: ldp             fp, lr, [SP], #0x10
    // 0xb27264: ret
    //     0xb27264: ret             
    // 0xb27268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb27268: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb2726c: b               #0xb2722c
    // 0xb27270: r9 = _pageController
    //     0xb27270: add             x9, PP, #0x57, lsl #12  ; [pp+0x57568] Field <_RatingReviewOnTapImageState@1512004270._pageController@1512004270>: late (offset: 0x1c)
    //     0xb27274: ldr             x9, [x9, #0x568]
    // 0xb27278: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb27278: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb2727c, size: 0x2d4
    // 0xb2727c: EnterFrame
    //     0xb2727c: stp             fp, lr, [SP, #-0x10]!
    //     0xb27280: mov             fp, SP
    // 0xb27284: AllocStack(0x50)
    //     0xb27284: sub             SP, SP, #0x50
    // 0xb27288: SetupParameters()
    //     0xb27288: ldr             x0, [fp, #0x20]
    //     0xb2728c: ldur            w2, [x0, #0x17]
    //     0xb27290: add             x2, x2, HEAP, lsl #32
    //     0xb27294: stur            x2, [fp, #-8]
    // 0xb27298: CheckStackOverflow
    //     0xb27298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb2729c: cmp             SP, x16
    //     0xb272a0: b.ls            #0xb27530
    // 0xb272a4: LoadField: r0 = r2->field_f
    //     0xb272a4: ldur            w0, [x2, #0xf]
    // 0xb272a8: DecompressPointer r0
    //     0xb272a8: add             x0, x0, HEAP, lsl #32
    // 0xb272ac: LoadField: r1 = r0->field_b
    //     0xb272ac: ldur            w1, [x0, #0xb]
    // 0xb272b0: DecompressPointer r1
    //     0xb272b0: add             x1, x1, HEAP, lsl #32
    // 0xb272b4: cmp             w1, NULL
    // 0xb272b8: b.eq            #0xb27538
    // 0xb272bc: LoadField: r0 = r1->field_b
    //     0xb272bc: ldur            w0, [x1, #0xb]
    // 0xb272c0: DecompressPointer r0
    //     0xb272c0: add             x0, x0, HEAP, lsl #32
    // 0xb272c4: cmp             w0, NULL
    // 0xb272c8: b.ne            #0xb272d8
    // 0xb272cc: ldr             x3, [fp, #0x10]
    // 0xb272d0: r0 = Null
    //     0xb272d0: mov             x0, NULL
    // 0xb272d4: b               #0xb27324
    // 0xb272d8: ldr             x3, [fp, #0x10]
    // 0xb272dc: LoadField: r4 = r0->field_1b
    //     0xb272dc: ldur            w4, [x0, #0x1b]
    // 0xb272e0: DecompressPointer r4
    //     0xb272e0: add             x4, x4, HEAP, lsl #32
    // 0xb272e4: LoadField: r0 = r4->field_b
    //     0xb272e4: ldur            w0, [x4, #0xb]
    // 0xb272e8: r5 = LoadInt32Instr(r3)
    //     0xb272e8: sbfx            x5, x3, #1, #0x1f
    //     0xb272ec: tbz             w3, #0, #0xb272f4
    //     0xb272f0: ldur            x5, [x3, #7]
    // 0xb272f4: r1 = LoadInt32Instr(r0)
    //     0xb272f4: sbfx            x1, x0, #1, #0x1f
    // 0xb272f8: mov             x0, x1
    // 0xb272fc: mov             x1, x5
    // 0xb27300: cmp             x1, x0
    // 0xb27304: b.hs            #0xb2753c
    // 0xb27308: LoadField: r0 = r4->field_f
    //     0xb27308: ldur            w0, [x4, #0xf]
    // 0xb2730c: DecompressPointer r0
    //     0xb2730c: add             x0, x0, HEAP, lsl #32
    // 0xb27310: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb27310: add             x16, x0, x5, lsl #2
    //     0xb27314: ldur            w1, [x16, #0xf]
    // 0xb27318: DecompressPointer r1
    //     0xb27318: add             x1, x1, HEAP, lsl #32
    // 0xb2731c: LoadField: r0 = r1->field_f
    //     0xb2731c: ldur            w0, [x1, #0xf]
    // 0xb27320: DecompressPointer r0
    //     0xb27320: add             x0, x0, HEAP, lsl #32
    // 0xb27324: r1 = LoadClassIdInstr(r0)
    //     0xb27324: ldur            x1, [x0, #-1]
    //     0xb27328: ubfx            x1, x1, #0xc, #0x14
    // 0xb2732c: r16 = "image"
    //     0xb2732c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0xb27330: stp             x16, x0, [SP]
    // 0xb27334: mov             x0, x1
    // 0xb27338: mov             lr, x0
    // 0xb2733c: ldr             lr, [x21, lr, lsl #3]
    // 0xb27340: blr             lr
    // 0xb27344: tbnz            w0, #4, #0xb27480
    // 0xb27348: ldur            x0, [fp, #-8]
    // 0xb2734c: LoadField: r1 = r0->field_f
    //     0xb2734c: ldur            w1, [x0, #0xf]
    // 0xb27350: DecompressPointer r1
    //     0xb27350: add             x1, x1, HEAP, lsl #32
    // 0xb27354: LoadField: r0 = r1->field_23
    //     0xb27354: ldur            w0, [x1, #0x23]
    // 0xb27358: DecompressPointer r0
    //     0xb27358: add             x0, x0, HEAP, lsl #32
    // 0xb2735c: tbnz            w0, #4, #0xb2736c
    // 0xb27360: r3 = Instance_BoxFit
    //     0xb27360: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb27364: ldr             x3, [x3, #0x118]
    // 0xb27368: b               #0xb27374
    // 0xb2736c: r3 = Instance_BoxFit
    //     0xb2736c: add             x3, PP, #0x51, lsl #12  ; [pp+0x51f38] Obj!BoxFit@d738e1
    //     0xb27370: ldr             x3, [x3, #0xf38]
    // 0xb27374: stur            x3, [fp, #-0x18]
    // 0xb27378: LoadField: r0 = r1->field_b
    //     0xb27378: ldur            w0, [x1, #0xb]
    // 0xb2737c: DecompressPointer r0
    //     0xb2737c: add             x0, x0, HEAP, lsl #32
    // 0xb27380: cmp             w0, NULL
    // 0xb27384: b.eq            #0xb27540
    // 0xb27388: LoadField: r1 = r0->field_b
    //     0xb27388: ldur            w1, [x0, #0xb]
    // 0xb2738c: DecompressPointer r1
    //     0xb2738c: add             x1, x1, HEAP, lsl #32
    // 0xb27390: cmp             w1, NULL
    // 0xb27394: b.ne            #0xb273a0
    // 0xb27398: r0 = Null
    //     0xb27398: mov             x0, NULL
    // 0xb2739c: b               #0xb273ec
    // 0xb273a0: ldr             x2, [fp, #0x10]
    // 0xb273a4: LoadField: r4 = r1->field_1b
    //     0xb273a4: ldur            w4, [x1, #0x1b]
    // 0xb273a8: DecompressPointer r4
    //     0xb273a8: add             x4, x4, HEAP, lsl #32
    // 0xb273ac: LoadField: r0 = r4->field_b
    //     0xb273ac: ldur            w0, [x4, #0xb]
    // 0xb273b0: r5 = LoadInt32Instr(r2)
    //     0xb273b0: sbfx            x5, x2, #1, #0x1f
    //     0xb273b4: tbz             w2, #0, #0xb273bc
    //     0xb273b8: ldur            x5, [x2, #7]
    // 0xb273bc: r1 = LoadInt32Instr(r0)
    //     0xb273bc: sbfx            x1, x0, #1, #0x1f
    // 0xb273c0: mov             x0, x1
    // 0xb273c4: mov             x1, x5
    // 0xb273c8: cmp             x1, x0
    // 0xb273cc: b.hs            #0xb27544
    // 0xb273d0: LoadField: r0 = r4->field_f
    //     0xb273d0: ldur            w0, [x4, #0xf]
    // 0xb273d4: DecompressPointer r0
    //     0xb273d4: add             x0, x0, HEAP, lsl #32
    // 0xb273d8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb273d8: add             x16, x0, x5, lsl #2
    //     0xb273dc: ldur            w1, [x16, #0xf]
    // 0xb273e0: DecompressPointer r1
    //     0xb273e0: add             x1, x1, HEAP, lsl #32
    // 0xb273e4: LoadField: r0 = r1->field_13
    //     0xb273e4: ldur            w0, [x1, #0x13]
    // 0xb273e8: DecompressPointer r0
    //     0xb273e8: add             x0, x0, HEAP, lsl #32
    // 0xb273ec: cmp             w0, NULL
    // 0xb273f0: b.ne            #0xb273f8
    // 0xb273f4: r0 = ""
    //     0xb273f4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb273f8: stur            x0, [fp, #-0x10]
    // 0xb273fc: r1 = Function '<anonymous closure>':.
    //     0xb273fc: add             x1, PP, #0x57, lsl #12  ; [pp+0x57590] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb27400: ldr             x1, [x1, #0x590]
    // 0xb27404: r2 = Null
    //     0xb27404: mov             x2, NULL
    // 0xb27408: r0 = AllocateClosure()
    //     0xb27408: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb2740c: r1 = Function '<anonymous closure>':.
    //     0xb2740c: add             x1, PP, #0x57, lsl #12  ; [pp+0x57598] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb27410: ldr             x1, [x1, #0x598]
    // 0xb27414: r2 = Null
    //     0xb27414: mov             x2, NULL
    // 0xb27418: stur            x0, [fp, #-0x20]
    // 0xb2741c: r0 = AllocateClosure()
    //     0xb2741c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb27420: stur            x0, [fp, #-0x28]
    // 0xb27424: r0 = CachedNetworkImage()
    //     0xb27424: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb27428: stur            x0, [fp, #-0x30]
    // 0xb2742c: ldur            x16, [fp, #-0x18]
    // 0xb27430: r30 = inf
    //     0xb27430: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xb27434: ldr             lr, [lr, #0x9f8]
    // 0xb27438: stp             lr, x16, [SP, #0x10]
    // 0xb2743c: ldur            x16, [fp, #-0x20]
    // 0xb27440: ldur            lr, [fp, #-0x28]
    // 0xb27444: stp             lr, x16, [SP]
    // 0xb27448: mov             x1, x0
    // 0xb2744c: ldur            x2, [fp, #-0x10]
    // 0xb27450: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xb27450: add             x4, PP, #0x54, lsl #12  ; [pp+0x54f70] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xb27454: ldr             x4, [x4, #0xf70]
    // 0xb27458: r0 = CachedNetworkImage()
    //     0xb27458: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb2745c: r0 = Center()
    //     0xb2745c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb27460: mov             x1, x0
    // 0xb27464: r0 = Instance_Alignment
    //     0xb27464: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb27468: ldr             x0, [x0, #0xb10]
    // 0xb2746c: StoreField: r1->field_f = r0
    //     0xb2746c: stur            w0, [x1, #0xf]
    // 0xb27470: ldur            x0, [fp, #-0x30]
    // 0xb27474: StoreField: r1->field_b = r0
    //     0xb27474: stur            w0, [x1, #0xb]
    // 0xb27478: mov             x0, x1
    // 0xb2747c: b               #0xb27524
    // 0xb27480: ldr             x2, [fp, #0x10]
    // 0xb27484: ldur            x0, [fp, #-8]
    // 0xb27488: LoadField: r1 = r0->field_f
    //     0xb27488: ldur            w1, [x0, #0xf]
    // 0xb2748c: DecompressPointer r1
    //     0xb2748c: add             x1, x1, HEAP, lsl #32
    // 0xb27490: LoadField: r0 = r1->field_b
    //     0xb27490: ldur            w0, [x1, #0xb]
    // 0xb27494: DecompressPointer r0
    //     0xb27494: add             x0, x0, HEAP, lsl #32
    // 0xb27498: cmp             w0, NULL
    // 0xb2749c: b.eq            #0xb27548
    // 0xb274a0: LoadField: r1 = r0->field_b
    //     0xb274a0: ldur            w1, [x0, #0xb]
    // 0xb274a4: DecompressPointer r1
    //     0xb274a4: add             x1, x1, HEAP, lsl #32
    // 0xb274a8: cmp             w1, NULL
    // 0xb274ac: b.ne            #0xb274b8
    // 0xb274b0: r0 = Null
    //     0xb274b0: mov             x0, NULL
    // 0xb274b4: b               #0xb27500
    // 0xb274b8: LoadField: r3 = r1->field_1b
    //     0xb274b8: ldur            w3, [x1, #0x1b]
    // 0xb274bc: DecompressPointer r3
    //     0xb274bc: add             x3, x3, HEAP, lsl #32
    // 0xb274c0: LoadField: r0 = r3->field_b
    //     0xb274c0: ldur            w0, [x3, #0xb]
    // 0xb274c4: r4 = LoadInt32Instr(r2)
    //     0xb274c4: sbfx            x4, x2, #1, #0x1f
    //     0xb274c8: tbz             w2, #0, #0xb274d0
    //     0xb274cc: ldur            x4, [x2, #7]
    // 0xb274d0: r1 = LoadInt32Instr(r0)
    //     0xb274d0: sbfx            x1, x0, #1, #0x1f
    // 0xb274d4: mov             x0, x1
    // 0xb274d8: mov             x1, x4
    // 0xb274dc: cmp             x1, x0
    // 0xb274e0: b.hs            #0xb2754c
    // 0xb274e4: LoadField: r0 = r3->field_f
    //     0xb274e4: ldur            w0, [x3, #0xf]
    // 0xb274e8: DecompressPointer r0
    //     0xb274e8: add             x0, x0, HEAP, lsl #32
    // 0xb274ec: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb274ec: add             x16, x0, x4, lsl #2
    //     0xb274f0: ldur            w1, [x16, #0xf]
    // 0xb274f4: DecompressPointer r1
    //     0xb274f4: add             x1, x1, HEAP, lsl #32
    // 0xb274f8: LoadField: r0 = r1->field_13
    //     0xb274f8: ldur            w0, [x1, #0x13]
    // 0xb274fc: DecompressPointer r0
    //     0xb274fc: add             x0, x0, HEAP, lsl #32
    // 0xb27500: cmp             w0, NULL
    // 0xb27504: b.ne            #0xb2750c
    // 0xb27508: r0 = ""
    //     0xb27508: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb2750c: stur            x0, [fp, #-8]
    // 0xb27510: r0 = VideoPlayerWidget()
    //     0xb27510: bl              #0xb157d4  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0xb27514: ldur            x1, [fp, #-8]
    // 0xb27518: StoreField: r0->field_b = r1
    //     0xb27518: stur            w1, [x0, #0xb]
    // 0xb2751c: r1 = true
    //     0xb2751c: add             x1, NULL, #0x20  ; true
    // 0xb27520: StoreField: r0->field_f = r1
    //     0xb27520: stur            w1, [x0, #0xf]
    // 0xb27524: LeaveFrame
    //     0xb27524: mov             SP, fp
    //     0xb27528: ldp             fp, lr, [SP], #0x10
    // 0xb2752c: ret
    //     0xb2752c: ret             
    // 0xb27530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb27530: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb27534: b               #0xb272a4
    // 0xb27538: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb27538: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2753c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2753c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb27540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb27540: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb27544: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb27544: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb27548: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb27548: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb2754c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb2754c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void _onPageChanged(dynamic, int) {
    // ** addr: 0xb27550, size: 0x3c
    // 0xb27550: EnterFrame
    //     0xb27550: stp             fp, lr, [SP, #-0x10]!
    //     0xb27554: mov             fp, SP
    // 0xb27558: ldr             x0, [fp, #0x18]
    // 0xb2755c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb2755c: ldur            w1, [x0, #0x17]
    // 0xb27560: DecompressPointer r1
    //     0xb27560: add             x1, x1, HEAP, lsl #32
    // 0xb27564: CheckStackOverflow
    //     0xb27564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb27568: cmp             SP, x16
    //     0xb2756c: b.ls            #0xb27584
    // 0xb27570: ldr             x2, [fp, #0x10]
    // 0xb27574: r0 = _onPageChanged()
    //     0xb27574: bl              #0xb2758c  ; [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged
    // 0xb27578: LeaveFrame
    //     0xb27578: mov             SP, fp
    //     0xb2757c: ldp             fp, lr, [SP], #0x10
    // 0xb27580: ret
    //     0xb27580: ret             
    // 0xb27584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb27584: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb27588: b               #0xb27570
  }
  _ _onPageChanged(/* No info */) {
    // ** addr: 0xb2758c, size: 0x70
    // 0xb2758c: EnterFrame
    //     0xb2758c: stp             fp, lr, [SP, #-0x10]!
    //     0xb27590: mov             fp, SP
    // 0xb27594: AllocStack(0x10)
    //     0xb27594: sub             SP, SP, #0x10
    // 0xb27598: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb27598: stur            x1, [fp, #-8]
    //     0xb2759c: stur            x2, [fp, #-0x10]
    // 0xb275a0: CheckStackOverflow
    //     0xb275a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb275a4: cmp             SP, x16
    //     0xb275a8: b.ls            #0xb275f4
    // 0xb275ac: r1 = 2
    //     0xb275ac: movz            x1, #0x2
    // 0xb275b0: r0 = AllocateContext()
    //     0xb275b0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb275b4: mov             x1, x0
    // 0xb275b8: ldur            x0, [fp, #-8]
    // 0xb275bc: StoreField: r1->field_f = r0
    //     0xb275bc: stur            w0, [x1, #0xf]
    // 0xb275c0: ldur            x2, [fp, #-0x10]
    // 0xb275c4: StoreField: r1->field_13 = r2
    //     0xb275c4: stur            w2, [x1, #0x13]
    // 0xb275c8: mov             x2, x1
    // 0xb275cc: r1 = Function '<anonymous closure>':.
    //     0xb275cc: add             x1, PP, #0x57, lsl #12  ; [pp+0x575a0] AnonymousClosure: (0xaaa7d0), in [package:customer_app/app/presentation/views/line/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onPageChanged (0xaaa838)
    //     0xb275d0: ldr             x1, [x1, #0x5a0]
    // 0xb275d4: r0 = AllocateClosure()
    //     0xb275d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb275d8: ldur            x1, [fp, #-8]
    // 0xb275dc: mov             x2, x0
    // 0xb275e0: r0 = setState()
    //     0xb275e0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb275e4: r0 = Null
    //     0xb275e4: mov             x0, NULL
    // 0xb275e8: LeaveFrame
    //     0xb275e8: mov             SP, fp
    //     0xb275ec: ldp             fp, lr, [SP], #0x10
    // 0xb275f0: ret
    //     0xb275f0: ret             
    // 0xb275f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb275f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb275f8: b               #0xb275ac
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87844, size: 0x8c
    // 0xc87844: EnterFrame
    //     0xc87844: stp             fp, lr, [SP, #-0x10]!
    //     0xc87848: mov             fp, SP
    // 0xc8784c: AllocStack(0x10)
    //     0xc8784c: sub             SP, SP, #0x10
    // 0xc87850: SetupParameters(_RatingReviewOnTapImageState this /* r1 => r2, fp-0x8 */)
    //     0xc87850: mov             x2, x1
    //     0xc87854: stur            x1, [fp, #-8]
    // 0xc87858: CheckStackOverflow
    //     0xc87858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc8785c: cmp             SP, x16
    //     0xc87860: b.ls            #0xc878bc
    // 0xc87864: LoadField: r1 = r2->field_1b
    //     0xc87864: ldur            w1, [x2, #0x1b]
    // 0xc87868: DecompressPointer r1
    //     0xc87868: add             x1, x1, HEAP, lsl #32
    // 0xc8786c: r16 = Sentinel
    //     0xc8786c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87870: cmp             w1, w16
    // 0xc87874: b.eq            #0xc878c4
    // 0xc87878: r0 = dispose()
    //     0xc87878: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc8787c: ldur            x2, [fp, #-8]
    // 0xc87880: LoadField: r0 = r2->field_1f
    //     0xc87880: ldur            w0, [x2, #0x1f]
    // 0xc87884: DecompressPointer r0
    //     0xc87884: add             x0, x0, HEAP, lsl #32
    // 0xc87888: stur            x0, [fp, #-0x10]
    // 0xc8788c: r1 = Function '_onCollapseChanged@1512004270':.
    //     0xc8788c: add             x1, PP, #0x57, lsl #12  ; [pp+0x575a8] AnonymousClosure: (0x93d5b0), in [package:customer_app/app/presentation/views/cosmetic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_onCollapseChanged (0x93d5e8)
    //     0xc87890: ldr             x1, [x1, #0x5a8]
    // 0xc87894: r0 = AllocateClosure()
    //     0xc87894: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc87898: ldur            x1, [fp, #-0x10]
    // 0xc8789c: mov             x2, x0
    // 0xc878a0: r0 = removeListener()
    //     0xc878a0: bl              #0x7b91b8  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc878a4: ldur            x1, [fp, #-0x10]
    // 0xc878a8: r0 = dispose()
    //     0xc878a8: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc878ac: r0 = Null
    //     0xc878ac: mov             x0, NULL
    // 0xc878b0: LeaveFrame
    //     0xc878b0: mov             SP, fp
    //     0xc878b4: ldp             fp, lr, [SP], #0x10
    // 0xc878b8: ret
    //     0xc878b8: ret             
    // 0xc878bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc878bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc878c0: b               #0xc87864
    // 0xc878c4: r9 = _pageController
    //     0xc878c4: add             x9, PP, #0x57, lsl #12  ; [pp+0x57568] Field <_RatingReviewOnTapImageState@1512004270._pageController@1512004270>: late (offset: 0x1c)
    //     0xc878c8: ldr             x9, [x9, #0x568]
    // 0xc878cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc878cc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4125, size: 0x20, field offset: 0xc
//   const constructor, 
class RatingReviewOnTapImage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e460, size: 0x48
    // 0xc7e460: EnterFrame
    //     0xc7e460: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e464: mov             fp, SP
    // 0xc7e468: AllocStack(0x8)
    //     0xc7e468: sub             SP, SP, #8
    // 0xc7e46c: CheckStackOverflow
    //     0xc7e46c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7e470: cmp             SP, x16
    //     0xc7e474: b.ls            #0xc7e4a0
    // 0xc7e478: r1 = <RatingReviewOnTapImage>
    //     0xc7e478: add             x1, PP, #0x48, lsl #12  ; [pp+0x48ac0] TypeArguments: <RatingReviewOnTapImage>
    //     0xc7e47c: ldr             x1, [x1, #0xac0]
    // 0xc7e480: r0 = _RatingReviewOnTapImageState()
    //     0xc7e480: bl              #0xc7e4a8  ; Allocate_RatingReviewOnTapImageStateStub -> _RatingReviewOnTapImageState (size=0x30)
    // 0xc7e484: mov             x1, x0
    // 0xc7e488: stur            x0, [fp, #-8]
    // 0xc7e48c: r0 = _RatingReviewOnTapImageState()
    //     0xc7e48c: bl              #0xc7cb88  ; [package:customer_app/app/presentation/views/basic/rating_review/widgets/rating_review_on_tap_image.dart] _RatingReviewOnTapImageState::_RatingReviewOnTapImageState
    // 0xc7e490: ldur            x0, [fp, #-8]
    // 0xc7e494: LeaveFrame
    //     0xc7e494: mov             SP, fp
    //     0xc7e498: ldp             fp, lr, [SP], #0x10
    // 0xc7e49c: ret
    //     0xc7e49c: ret             
    // 0xc7e4a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7e4a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7e4a4: b               #0xc7e478
  }
}
