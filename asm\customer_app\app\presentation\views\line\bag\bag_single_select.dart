// lib: , url: package:customer_app/app/presentation/views/line/bag/bag_single_select.dart

// class id: 1049465, size: 0x8
class :: {
}

// class id: 3292, size: 0x1c, field offset: 0x14
class _BagSingleSelectState extends State<dynamic> {

  late String value; // offset: 0x14
  late String customisedValue; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xba28a0, size: 0x90c
    // 0xba28a0: EnterFrame
    //     0xba28a0: stp             fp, lr, [SP, #-0x10]!
    //     0xba28a4: mov             fp, SP
    // 0xba28a8: AllocStack(0x50)
    //     0xba28a8: sub             SP, SP, #0x50
    // 0xba28ac: SetupParameters(_BagSingleSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xba28ac: mov             x0, x1
    //     0xba28b0: stur            x1, [fp, #-8]
    //     0xba28b4: mov             x1, x2
    //     0xba28b8: stur            x2, [fp, #-0x10]
    // 0xba28bc: CheckStackOverflow
    //     0xba28bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xba28c0: cmp             SP, x16
    //     0xba28c4: b.ls            #0xba3178
    // 0xba28c8: LoadField: r2 = r0->field_b
    //     0xba28c8: ldur            w2, [x0, #0xb]
    // 0xba28cc: DecompressPointer r2
    //     0xba28cc: add             x2, x2, HEAP, lsl #32
    // 0xba28d0: cmp             w2, NULL
    // 0xba28d4: b.eq            #0xba3180
    // 0xba28d8: LoadField: r3 = r2->field_b
    //     0xba28d8: ldur            w3, [x2, #0xb]
    // 0xba28dc: DecompressPointer r3
    //     0xba28dc: add             x3, x3, HEAP, lsl #32
    // 0xba28e0: cmp             w3, NULL
    // 0xba28e4: b.ne            #0xba28f0
    // 0xba28e8: r2 = Null
    //     0xba28e8: mov             x2, NULL
    // 0xba28ec: b               #0xba291c
    // 0xba28f0: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xba28f0: ldur            w2, [x3, #0x17]
    // 0xba28f4: DecompressPointer r2
    //     0xba28f4: add             x2, x2, HEAP, lsl #32
    // 0xba28f8: cmp             w2, NULL
    // 0xba28fc: b.ne            #0xba2908
    // 0xba2900: r2 = Null
    //     0xba2900: mov             x2, NULL
    // 0xba2904: b               #0xba291c
    // 0xba2908: LoadField: r3 = r2->field_7
    //     0xba2908: ldur            w3, [x2, #7]
    // 0xba290c: cbnz            w3, #0xba2918
    // 0xba2910: r2 = false
    //     0xba2910: add             x2, NULL, #0x30  ; false
    // 0xba2914: b               #0xba291c
    // 0xba2918: r2 = true
    //     0xba2918: add             x2, NULL, #0x20  ; true
    // 0xba291c: cmp             w2, NULL
    // 0xba2920: b.ne            #0xba296c
    // 0xba2924: mov             x3, x0
    // 0xba2928: r4 = 6
    //     0xba2928: movz            x4, #0x6
    // 0xba292c: r2 = 4
    //     0xba292c: movz            x2, #0x4
    // 0xba2930: r7 = Instance_CrossAxisAlignment
    //     0xba2930: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba2934: ldr             x7, [x7, #0xa18]
    // 0xba2938: r5 = Instance_MainAxisAlignment
    //     0xba2938: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba293c: ldr             x5, [x5, #0xa08]
    // 0xba2940: r6 = Instance_MainAxisSize
    //     0xba2940: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba2944: ldr             x6, [x6, #0xa10]
    // 0xba2948: r1 = Instance_Axis
    //     0xba2948: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba294c: r8 = Instance_VerticalDirection
    //     0xba294c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba2950: ldr             x8, [x8, #0xa20]
    // 0xba2954: r0 = Instance__DeferringMouseCursor
    //     0xba2954: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2958: r9 = Instance_Clip
    //     0xba2958: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba295c: ldr             x9, [x9, #0x38]
    // 0xba2960: d1 = 0.500000
    //     0xba2960: fmov            d1, #0.50000000
    // 0xba2964: d0 = inf
    //     0xba2964: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba2968: b               #0xba2d94
    // 0xba296c: tbnz            w2, #4, #0xba2d50
    // 0xba2970: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba2970: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba2974: ldr             x0, [x0, #0x1c80]
    //     0xba2978: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba297c: cmp             w0, w16
    //     0xba2980: b.ne            #0xba298c
    //     0xba2984: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xba2988: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xba298c: r0 = GetNavigation.size()
    //     0xba298c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba2990: LoadField: d0 = r0->field_7
    //     0xba2990: ldur            d0, [x0, #7]
    // 0xba2994: d1 = 0.500000
    //     0xba2994: fmov            d1, #0.50000000
    // 0xba2998: fmul            d2, d0, d1
    // 0xba299c: stur            d2, [fp, #-0x40]
    // 0xba29a0: r0 = BoxConstraints()
    //     0xba29a0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xba29a4: stur            x0, [fp, #-0x20]
    // 0xba29a8: StoreField: r0->field_7 = rZR
    //     0xba29a8: stur            xzr, [x0, #7]
    // 0xba29ac: ldur            d0, [fp, #-0x40]
    // 0xba29b0: StoreField: r0->field_f = d0
    //     0xba29b0: stur            d0, [x0, #0xf]
    // 0xba29b4: ArrayStore: r0[0] = rZR  ; List_8
    //     0xba29b4: stur            xzr, [x0, #0x17]
    // 0xba29b8: d0 = inf
    //     0xba29b8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba29bc: StoreField: r0->field_1f = d0
    //     0xba29bc: stur            d0, [x0, #0x1f]
    // 0xba29c0: ldur            x2, [fp, #-8]
    // 0xba29c4: LoadField: r1 = r2->field_b
    //     0xba29c4: ldur            w1, [x2, #0xb]
    // 0xba29c8: DecompressPointer r1
    //     0xba29c8: add             x1, x1, HEAP, lsl #32
    // 0xba29cc: cmp             w1, NULL
    // 0xba29d0: b.eq            #0xba3184
    // 0xba29d4: LoadField: r3 = r1->field_b
    //     0xba29d4: ldur            w3, [x1, #0xb]
    // 0xba29d8: DecompressPointer r3
    //     0xba29d8: add             x3, x3, HEAP, lsl #32
    // 0xba29dc: cmp             w3, NULL
    // 0xba29e0: b.ne            #0xba29ec
    // 0xba29e4: r1 = Null
    //     0xba29e4: mov             x1, NULL
    // 0xba29e8: b               #0xba29f4
    // 0xba29ec: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xba29ec: ldur            w1, [x3, #0x17]
    // 0xba29f0: DecompressPointer r1
    //     0xba29f0: add             x1, x1, HEAP, lsl #32
    // 0xba29f4: cmp             w1, NULL
    // 0xba29f8: b.ne            #0xba2a04
    // 0xba29fc: r3 = ""
    //     0xba29fc: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba2a00: b               #0xba2a08
    // 0xba2a04: mov             x3, x1
    // 0xba2a08: ldur            x1, [fp, #-0x10]
    // 0xba2a0c: stur            x3, [fp, #-0x18]
    // 0xba2a10: r0 = of()
    //     0xba2a10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba2a14: LoadField: r1 = r0->field_87
    //     0xba2a14: ldur            w1, [x0, #0x87]
    // 0xba2a18: DecompressPointer r1
    //     0xba2a18: add             x1, x1, HEAP, lsl #32
    // 0xba2a1c: LoadField: r0 = r1->field_2b
    //     0xba2a1c: ldur            w0, [x1, #0x2b]
    // 0xba2a20: DecompressPointer r0
    //     0xba2a20: add             x0, x0, HEAP, lsl #32
    // 0xba2a24: stur            x0, [fp, #-0x28]
    // 0xba2a28: r1 = Instance_Color
    //     0xba2a28: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2a2c: d0 = 0.700000
    //     0xba2a2c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2a30: ldr             d0, [x17, #0xf48]
    // 0xba2a34: r0 = withOpacity()
    //     0xba2a34: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2a38: r16 = 12.000000
    //     0xba2a38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2a3c: ldr             x16, [x16, #0x9e8]
    // 0xba2a40: stp             x0, x16, [SP]
    // 0xba2a44: ldur            x1, [fp, #-0x28]
    // 0xba2a48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2a48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2a4c: ldr             x4, [x4, #0xaa0]
    // 0xba2a50: r0 = copyWith()
    //     0xba2a50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba2a54: stur            x0, [fp, #-0x28]
    // 0xba2a58: r0 = TextSpan()
    //     0xba2a58: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2a5c: mov             x3, x0
    // 0xba2a60: ldur            x0, [fp, #-0x18]
    // 0xba2a64: stur            x3, [fp, #-0x30]
    // 0xba2a68: StoreField: r3->field_b = r0
    //     0xba2a68: stur            w0, [x3, #0xb]
    // 0xba2a6c: r0 = Instance__DeferringMouseCursor
    //     0xba2a6c: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2a70: ArrayStore: r3[0] = r0  ; List_4
    //     0xba2a70: stur            w0, [x3, #0x17]
    // 0xba2a74: ldur            x1, [fp, #-0x28]
    // 0xba2a78: StoreField: r3->field_7 = r1
    //     0xba2a78: stur            w1, [x3, #7]
    // 0xba2a7c: r1 = Null
    //     0xba2a7c: mov             x1, NULL
    // 0xba2a80: r2 = 6
    //     0xba2a80: movz            x2, #0x6
    // 0xba2a84: r0 = AllocateArray()
    //     0xba2a84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2a88: r16 = " : "
    //     0xba2a88: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xba2a8c: ldr             x16, [x16, #0x680]
    // 0xba2a90: StoreField: r0->field_f = r16
    //     0xba2a90: stur            w16, [x0, #0xf]
    // 0xba2a94: ldur            x1, [fp, #-8]
    // 0xba2a98: LoadField: r2 = r1->field_13
    //     0xba2a98: ldur            w2, [x1, #0x13]
    // 0xba2a9c: DecompressPointer r2
    //     0xba2a9c: add             x2, x2, HEAP, lsl #32
    // 0xba2aa0: r16 = Sentinel
    //     0xba2aa0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xba2aa4: cmp             w2, w16
    // 0xba2aa8: b.eq            #0xba3188
    // 0xba2aac: StoreField: r0->field_13 = r2
    //     0xba2aac: stur            w2, [x0, #0x13]
    // 0xba2ab0: r16 = " "
    //     0xba2ab0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba2ab4: ArrayStore: r0[0] = r16  ; List_4
    //     0xba2ab4: stur            w16, [x0, #0x17]
    // 0xba2ab8: str             x0, [SP]
    // 0xba2abc: r0 = _interpolate()
    //     0xba2abc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba2ac0: ldur            x1, [fp, #-0x10]
    // 0xba2ac4: stur            x0, [fp, #-0x18]
    // 0xba2ac8: r0 = of()
    //     0xba2ac8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba2acc: LoadField: r1 = r0->field_87
    //     0xba2acc: ldur            w1, [x0, #0x87]
    // 0xba2ad0: DecompressPointer r1
    //     0xba2ad0: add             x1, x1, HEAP, lsl #32
    // 0xba2ad4: LoadField: r0 = r1->field_7
    //     0xba2ad4: ldur            w0, [x1, #7]
    // 0xba2ad8: DecompressPointer r0
    //     0xba2ad8: add             x0, x0, HEAP, lsl #32
    // 0xba2adc: stur            x0, [fp, #-0x28]
    // 0xba2ae0: r1 = Instance_Color
    //     0xba2ae0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2ae4: d0 = 0.700000
    //     0xba2ae4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2ae8: ldr             d0, [x17, #0xf48]
    // 0xba2aec: r0 = withOpacity()
    //     0xba2aec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2af0: r16 = 12.000000
    //     0xba2af0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2af4: ldr             x16, [x16, #0x9e8]
    // 0xba2af8: stp             x0, x16, [SP]
    // 0xba2afc: ldur            x1, [fp, #-0x28]
    // 0xba2b00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2b00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2b04: ldr             x4, [x4, #0xaa0]
    // 0xba2b08: r0 = copyWith()
    //     0xba2b08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba2b0c: stur            x0, [fp, #-0x28]
    // 0xba2b10: r0 = TextSpan()
    //     0xba2b10: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2b14: mov             x3, x0
    // 0xba2b18: ldur            x0, [fp, #-0x18]
    // 0xba2b1c: stur            x3, [fp, #-0x38]
    // 0xba2b20: StoreField: r3->field_b = r0
    //     0xba2b20: stur            w0, [x3, #0xb]
    // 0xba2b24: r0 = Instance__DeferringMouseCursor
    //     0xba2b24: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2b28: ArrayStore: r3[0] = r0  ; List_4
    //     0xba2b28: stur            w0, [x3, #0x17]
    // 0xba2b2c: ldur            x1, [fp, #-0x28]
    // 0xba2b30: StoreField: r3->field_7 = r1
    //     0xba2b30: stur            w1, [x3, #7]
    // 0xba2b34: r1 = Null
    //     0xba2b34: mov             x1, NULL
    // 0xba2b38: r2 = 4
    //     0xba2b38: movz            x2, #0x4
    // 0xba2b3c: r0 = AllocateArray()
    //     0xba2b3c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2b40: mov             x2, x0
    // 0xba2b44: ldur            x0, [fp, #-0x30]
    // 0xba2b48: stur            x2, [fp, #-0x18]
    // 0xba2b4c: StoreField: r2->field_f = r0
    //     0xba2b4c: stur            w0, [x2, #0xf]
    // 0xba2b50: ldur            x0, [fp, #-0x38]
    // 0xba2b54: StoreField: r2->field_13 = r0
    //     0xba2b54: stur            w0, [x2, #0x13]
    // 0xba2b58: r1 = <InlineSpan>
    //     0xba2b58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xba2b5c: ldr             x1, [x1, #0xe40]
    // 0xba2b60: r0 = AllocateGrowableArray()
    //     0xba2b60: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba2b64: mov             x1, x0
    // 0xba2b68: ldur            x0, [fp, #-0x18]
    // 0xba2b6c: stur            x1, [fp, #-0x28]
    // 0xba2b70: StoreField: r1->field_f = r0
    //     0xba2b70: stur            w0, [x1, #0xf]
    // 0xba2b74: r2 = 4
    //     0xba2b74: movz            x2, #0x4
    // 0xba2b78: StoreField: r1->field_b = r2
    //     0xba2b78: stur            w2, [x1, #0xb]
    // 0xba2b7c: r0 = TextSpan()
    //     0xba2b7c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2b80: mov             x1, x0
    // 0xba2b84: ldur            x0, [fp, #-0x28]
    // 0xba2b88: stur            x1, [fp, #-0x18]
    // 0xba2b8c: StoreField: r1->field_f = r0
    //     0xba2b8c: stur            w0, [x1, #0xf]
    // 0xba2b90: r0 = Instance__DeferringMouseCursor
    //     0xba2b90: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2b94: ArrayStore: r1[0] = r0  ; List_4
    //     0xba2b94: stur            w0, [x1, #0x17]
    // 0xba2b98: r0 = RichText()
    //     0xba2b98: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba2b9c: mov             x1, x0
    // 0xba2ba0: ldur            x2, [fp, #-0x18]
    // 0xba2ba4: stur            x0, [fp, #-0x18]
    // 0xba2ba8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba2ba8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba2bac: r0 = RichText()
    //     0xba2bac: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba2bb0: r0 = ConstrainedBox()
    //     0xba2bb0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xba2bb4: mov             x1, x0
    // 0xba2bb8: ldur            x0, [fp, #-0x20]
    // 0xba2bbc: stur            x1, [fp, #-0x28]
    // 0xba2bc0: StoreField: r1->field_f = r0
    //     0xba2bc0: stur            w0, [x1, #0xf]
    // 0xba2bc4: ldur            x0, [fp, #-0x18]
    // 0xba2bc8: StoreField: r1->field_b = r0
    //     0xba2bc8: stur            w0, [x1, #0xb]
    // 0xba2bcc: ldur            x3, [fp, #-8]
    // 0xba2bd0: LoadField: r0 = r3->field_b
    //     0xba2bd0: ldur            w0, [x3, #0xb]
    // 0xba2bd4: DecompressPointer r0
    //     0xba2bd4: add             x0, x0, HEAP, lsl #32
    // 0xba2bd8: cmp             w0, NULL
    // 0xba2bdc: b.eq            #0xba3194
    // 0xba2be0: LoadField: r2 = r0->field_b
    //     0xba2be0: ldur            w2, [x0, #0xb]
    // 0xba2be4: DecompressPointer r2
    //     0xba2be4: add             x2, x2, HEAP, lsl #32
    // 0xba2be8: cmp             w2, NULL
    // 0xba2bec: b.ne            #0xba2bf8
    // 0xba2bf0: r0 = Null
    //     0xba2bf0: mov             x0, NULL
    // 0xba2bf4: b               #0xba2c24
    // 0xba2bf8: LoadField: r0 = r2->field_2b
    //     0xba2bf8: ldur            w0, [x2, #0x2b]
    // 0xba2bfc: DecompressPointer r0
    //     0xba2bfc: add             x0, x0, HEAP, lsl #32
    // 0xba2c00: r2 = LoadClassIdInstr(r0)
    //     0xba2c00: ldur            x2, [x0, #-1]
    //     0xba2c04: ubfx            x2, x2, #0xc, #0x14
    // 0xba2c08: str             x0, [SP]
    // 0xba2c0c: mov             x0, x2
    // 0xba2c10: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xba2c10: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xba2c14: r0 = GDT[cid_x0 + 0x2700]()
    //     0xba2c14: movz            x17, #0x2700
    //     0xba2c18: add             lr, x0, x17
    //     0xba2c1c: ldr             lr, [x21, lr, lsl #3]
    //     0xba2c20: blr             lr
    // 0xba2c24: cmp             w0, NULL
    // 0xba2c28: b.ne            #0xba2c34
    // 0xba2c2c: r2 = " "
    //     0xba2c2c: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba2c30: b               #0xba2c38
    // 0xba2c34: mov             x2, x0
    // 0xba2c38: ldur            x0, [fp, #-0x28]
    // 0xba2c3c: ldur            x1, [fp, #-0x10]
    // 0xba2c40: stur            x2, [fp, #-0x18]
    // 0xba2c44: r0 = of()
    //     0xba2c44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba2c48: LoadField: r1 = r0->field_87
    //     0xba2c48: ldur            w1, [x0, #0x87]
    // 0xba2c4c: DecompressPointer r1
    //     0xba2c4c: add             x1, x1, HEAP, lsl #32
    // 0xba2c50: LoadField: r0 = r1->field_2b
    //     0xba2c50: ldur            w0, [x1, #0x2b]
    // 0xba2c54: DecompressPointer r0
    //     0xba2c54: add             x0, x0, HEAP, lsl #32
    // 0xba2c58: stur            x0, [fp, #-0x20]
    // 0xba2c5c: r1 = Instance_Color
    //     0xba2c5c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2c60: d0 = 0.700000
    //     0xba2c60: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2c64: ldr             d0, [x17, #0xf48]
    // 0xba2c68: r0 = withOpacity()
    //     0xba2c68: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2c6c: r16 = 12.000000
    //     0xba2c6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2c70: ldr             x16, [x16, #0x9e8]
    // 0xba2c74: stp             x0, x16, [SP]
    // 0xba2c78: ldur            x1, [fp, #-0x20]
    // 0xba2c7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2c7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2c80: ldr             x4, [x4, #0xaa0]
    // 0xba2c84: r0 = copyWith()
    //     0xba2c84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba2c88: stur            x0, [fp, #-0x20]
    // 0xba2c8c: r0 = Text()
    //     0xba2c8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba2c90: mov             x3, x0
    // 0xba2c94: ldur            x0, [fp, #-0x18]
    // 0xba2c98: stur            x3, [fp, #-0x30]
    // 0xba2c9c: StoreField: r3->field_b = r0
    //     0xba2c9c: stur            w0, [x3, #0xb]
    // 0xba2ca0: ldur            x0, [fp, #-0x20]
    // 0xba2ca4: StoreField: r3->field_13 = r0
    //     0xba2ca4: stur            w0, [x3, #0x13]
    // 0xba2ca8: r1 = Null
    //     0xba2ca8: mov             x1, NULL
    // 0xba2cac: r2 = 6
    //     0xba2cac: movz            x2, #0x6
    // 0xba2cb0: r0 = AllocateArray()
    //     0xba2cb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2cb4: mov             x2, x0
    // 0xba2cb8: ldur            x0, [fp, #-0x28]
    // 0xba2cbc: stur            x2, [fp, #-0x18]
    // 0xba2cc0: StoreField: r2->field_f = r0
    //     0xba2cc0: stur            w0, [x2, #0xf]
    // 0xba2cc4: r16 = Instance_Spacer
    //     0xba2cc4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba2cc8: ldr             x16, [x16, #0xf0]
    // 0xba2ccc: StoreField: r2->field_13 = r16
    //     0xba2ccc: stur            w16, [x2, #0x13]
    // 0xba2cd0: ldur            x0, [fp, #-0x30]
    // 0xba2cd4: ArrayStore: r2[0] = r0  ; List_4
    //     0xba2cd4: stur            w0, [x2, #0x17]
    // 0xba2cd8: r1 = <Widget>
    //     0xba2cd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba2cdc: r0 = AllocateGrowableArray()
    //     0xba2cdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba2ce0: mov             x1, x0
    // 0xba2ce4: ldur            x0, [fp, #-0x18]
    // 0xba2ce8: stur            x1, [fp, #-0x20]
    // 0xba2cec: StoreField: r1->field_f = r0
    //     0xba2cec: stur            w0, [x1, #0xf]
    // 0xba2cf0: r4 = 6
    //     0xba2cf0: movz            x4, #0x6
    // 0xba2cf4: StoreField: r1->field_b = r4
    //     0xba2cf4: stur            w4, [x1, #0xb]
    // 0xba2cf8: r0 = Row()
    //     0xba2cf8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba2cfc: r1 = Instance_Axis
    //     0xba2cfc: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba2d00: StoreField: r0->field_f = r1
    //     0xba2d00: stur            w1, [x0, #0xf]
    // 0xba2d04: r5 = Instance_MainAxisAlignment
    //     0xba2d04: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba2d08: ldr             x5, [x5, #0xa08]
    // 0xba2d0c: StoreField: r0->field_13 = r5
    //     0xba2d0c: stur            w5, [x0, #0x13]
    // 0xba2d10: r6 = Instance_MainAxisSize
    //     0xba2d10: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba2d14: ldr             x6, [x6, #0xa10]
    // 0xba2d18: ArrayStore: r0[0] = r6  ; List_4
    //     0xba2d18: stur            w6, [x0, #0x17]
    // 0xba2d1c: r7 = Instance_CrossAxisAlignment
    //     0xba2d1c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba2d20: ldr             x7, [x7, #0xa18]
    // 0xba2d24: StoreField: r0->field_1b = r7
    //     0xba2d24: stur            w7, [x0, #0x1b]
    // 0xba2d28: r8 = Instance_VerticalDirection
    //     0xba2d28: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba2d2c: ldr             x8, [x8, #0xa20]
    // 0xba2d30: StoreField: r0->field_23 = r8
    //     0xba2d30: stur            w8, [x0, #0x23]
    // 0xba2d34: r9 = Instance_Clip
    //     0xba2d34: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba2d38: ldr             x9, [x9, #0x38]
    // 0xba2d3c: StoreField: r0->field_2b = r9
    //     0xba2d3c: stur            w9, [x0, #0x2b]
    // 0xba2d40: StoreField: r0->field_2f = rZR
    //     0xba2d40: stur            xzr, [x0, #0x2f]
    // 0xba2d44: ldur            x1, [fp, #-0x20]
    // 0xba2d48: StoreField: r0->field_b = r1
    //     0xba2d48: stur            w1, [x0, #0xb]
    // 0xba2d4c: b               #0xba316c
    // 0xba2d50: mov             x3, x0
    // 0xba2d54: r4 = 6
    //     0xba2d54: movz            x4, #0x6
    // 0xba2d58: r2 = 4
    //     0xba2d58: movz            x2, #0x4
    // 0xba2d5c: r7 = Instance_CrossAxisAlignment
    //     0xba2d5c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba2d60: ldr             x7, [x7, #0xa18]
    // 0xba2d64: r5 = Instance_MainAxisAlignment
    //     0xba2d64: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba2d68: ldr             x5, [x5, #0xa08]
    // 0xba2d6c: r6 = Instance_MainAxisSize
    //     0xba2d6c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba2d70: ldr             x6, [x6, #0xa10]
    // 0xba2d74: r1 = Instance_Axis
    //     0xba2d74: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba2d78: r8 = Instance_VerticalDirection
    //     0xba2d78: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba2d7c: ldr             x8, [x8, #0xa20]
    // 0xba2d80: r0 = Instance__DeferringMouseCursor
    //     0xba2d80: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2d84: r9 = Instance_Clip
    //     0xba2d84: add             x9, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba2d88: ldr             x9, [x9, #0x38]
    // 0xba2d8c: d1 = 0.500000
    //     0xba2d8c: fmov            d1, #0.50000000
    // 0xba2d90: d0 = inf
    //     0xba2d90: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba2d94: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xba2d94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xba2d98: ldr             x0, [x0, #0x1c80]
    //     0xba2d9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xba2da0: cmp             w0, w16
    //     0xba2da4: b.ne            #0xba2db0
    //     0xba2da8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xba2dac: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xba2db0: r0 = GetNavigation.size()
    //     0xba2db0: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xba2db4: LoadField: d0 = r0->field_7
    //     0xba2db4: ldur            d0, [x0, #7]
    // 0xba2db8: d1 = 0.500000
    //     0xba2db8: fmov            d1, #0.50000000
    // 0xba2dbc: fmul            d2, d0, d1
    // 0xba2dc0: stur            d2, [fp, #-0x40]
    // 0xba2dc4: r0 = BoxConstraints()
    //     0xba2dc4: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xba2dc8: stur            x0, [fp, #-0x20]
    // 0xba2dcc: StoreField: r0->field_7 = rZR
    //     0xba2dcc: stur            xzr, [x0, #7]
    // 0xba2dd0: ldur            d0, [fp, #-0x40]
    // 0xba2dd4: StoreField: r0->field_f = d0
    //     0xba2dd4: stur            d0, [x0, #0xf]
    // 0xba2dd8: ArrayStore: r0[0] = rZR  ; List_8
    //     0xba2dd8: stur            xzr, [x0, #0x17]
    // 0xba2ddc: d0 = inf
    //     0xba2ddc: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xba2de0: StoreField: r0->field_1f = d0
    //     0xba2de0: stur            d0, [x0, #0x1f]
    // 0xba2de4: ldur            x2, [fp, #-8]
    // 0xba2de8: LoadField: r1 = r2->field_b
    //     0xba2de8: ldur            w1, [x2, #0xb]
    // 0xba2dec: DecompressPointer r1
    //     0xba2dec: add             x1, x1, HEAP, lsl #32
    // 0xba2df0: cmp             w1, NULL
    // 0xba2df4: b.eq            #0xba3198
    // 0xba2df8: LoadField: r3 = r1->field_f
    //     0xba2df8: ldur            w3, [x1, #0xf]
    // 0xba2dfc: DecompressPointer r3
    //     0xba2dfc: add             x3, x3, HEAP, lsl #32
    // 0xba2e00: cmp             w3, NULL
    // 0xba2e04: b.ne            #0xba2e10
    // 0xba2e08: r1 = Null
    //     0xba2e08: mov             x1, NULL
    // 0xba2e0c: b               #0xba2e18
    // 0xba2e10: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xba2e10: ldur            w1, [x3, #0x17]
    // 0xba2e14: DecompressPointer r1
    //     0xba2e14: add             x1, x1, HEAP, lsl #32
    // 0xba2e18: cmp             w1, NULL
    // 0xba2e1c: b.ne            #0xba2e28
    // 0xba2e20: r3 = ""
    //     0xba2e20: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xba2e24: b               #0xba2e2c
    // 0xba2e28: mov             x3, x1
    // 0xba2e2c: ldur            x1, [fp, #-0x10]
    // 0xba2e30: stur            x3, [fp, #-0x18]
    // 0xba2e34: r0 = of()
    //     0xba2e34: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba2e38: LoadField: r1 = r0->field_87
    //     0xba2e38: ldur            w1, [x0, #0x87]
    // 0xba2e3c: DecompressPointer r1
    //     0xba2e3c: add             x1, x1, HEAP, lsl #32
    // 0xba2e40: LoadField: r0 = r1->field_2b
    //     0xba2e40: ldur            w0, [x1, #0x2b]
    // 0xba2e44: DecompressPointer r0
    //     0xba2e44: add             x0, x0, HEAP, lsl #32
    // 0xba2e48: stur            x0, [fp, #-0x28]
    // 0xba2e4c: r1 = Instance_Color
    //     0xba2e4c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2e50: d0 = 0.700000
    //     0xba2e50: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2e54: ldr             d0, [x17, #0xf48]
    // 0xba2e58: r0 = withOpacity()
    //     0xba2e58: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2e5c: r16 = 12.000000
    //     0xba2e5c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2e60: ldr             x16, [x16, #0x9e8]
    // 0xba2e64: stp             x0, x16, [SP]
    // 0xba2e68: ldur            x1, [fp, #-0x28]
    // 0xba2e6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2e6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2e70: ldr             x4, [x4, #0xaa0]
    // 0xba2e74: r0 = copyWith()
    //     0xba2e74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba2e78: stur            x0, [fp, #-0x28]
    // 0xba2e7c: r0 = TextSpan()
    //     0xba2e7c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2e80: mov             x3, x0
    // 0xba2e84: ldur            x0, [fp, #-0x18]
    // 0xba2e88: stur            x3, [fp, #-0x30]
    // 0xba2e8c: StoreField: r3->field_b = r0
    //     0xba2e8c: stur            w0, [x3, #0xb]
    // 0xba2e90: r0 = Instance__DeferringMouseCursor
    //     0xba2e90: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2e94: ArrayStore: r3[0] = r0  ; List_4
    //     0xba2e94: stur            w0, [x3, #0x17]
    // 0xba2e98: ldur            x1, [fp, #-0x28]
    // 0xba2e9c: StoreField: r3->field_7 = r1
    //     0xba2e9c: stur            w1, [x3, #7]
    // 0xba2ea0: r1 = Null
    //     0xba2ea0: mov             x1, NULL
    // 0xba2ea4: r2 = 6
    //     0xba2ea4: movz            x2, #0x6
    // 0xba2ea8: r0 = AllocateArray()
    //     0xba2ea8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2eac: r16 = " : "
    //     0xba2eac: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a680] " : "
    //     0xba2eb0: ldr             x16, [x16, #0x680]
    // 0xba2eb4: StoreField: r0->field_f = r16
    //     0xba2eb4: stur            w16, [x0, #0xf]
    // 0xba2eb8: ldur            x1, [fp, #-8]
    // 0xba2ebc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xba2ebc: ldur            w2, [x1, #0x17]
    // 0xba2ec0: DecompressPointer r2
    //     0xba2ec0: add             x2, x2, HEAP, lsl #32
    // 0xba2ec4: r16 = Sentinel
    //     0xba2ec4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xba2ec8: cmp             w2, w16
    // 0xba2ecc: b.eq            #0xba319c
    // 0xba2ed0: StoreField: r0->field_13 = r2
    //     0xba2ed0: stur            w2, [x0, #0x13]
    // 0xba2ed4: r16 = " "
    //     0xba2ed4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba2ed8: ArrayStore: r0[0] = r16  ; List_4
    //     0xba2ed8: stur            w16, [x0, #0x17]
    // 0xba2edc: str             x0, [SP]
    // 0xba2ee0: r0 = _interpolate()
    //     0xba2ee0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xba2ee4: ldur            x1, [fp, #-0x10]
    // 0xba2ee8: stur            x0, [fp, #-0x18]
    // 0xba2eec: r0 = of()
    //     0xba2eec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba2ef0: LoadField: r1 = r0->field_87
    //     0xba2ef0: ldur            w1, [x0, #0x87]
    // 0xba2ef4: DecompressPointer r1
    //     0xba2ef4: add             x1, x1, HEAP, lsl #32
    // 0xba2ef8: LoadField: r0 = r1->field_7
    //     0xba2ef8: ldur            w0, [x1, #7]
    // 0xba2efc: DecompressPointer r0
    //     0xba2efc: add             x0, x0, HEAP, lsl #32
    // 0xba2f00: stur            x0, [fp, #-0x28]
    // 0xba2f04: r1 = Instance_Color
    //     0xba2f04: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba2f08: d0 = 0.700000
    //     0xba2f08: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba2f0c: ldr             d0, [x17, #0xf48]
    // 0xba2f10: r0 = withOpacity()
    //     0xba2f10: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba2f14: r16 = 12.000000
    //     0xba2f14: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba2f18: ldr             x16, [x16, #0x9e8]
    // 0xba2f1c: stp             x0, x16, [SP]
    // 0xba2f20: ldur            x1, [fp, #-0x28]
    // 0xba2f24: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba2f24: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba2f28: ldr             x4, [x4, #0xaa0]
    // 0xba2f2c: r0 = copyWith()
    //     0xba2f2c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba2f30: stur            x0, [fp, #-0x28]
    // 0xba2f34: r0 = TextSpan()
    //     0xba2f34: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2f38: mov             x3, x0
    // 0xba2f3c: ldur            x0, [fp, #-0x18]
    // 0xba2f40: stur            x3, [fp, #-0x38]
    // 0xba2f44: StoreField: r3->field_b = r0
    //     0xba2f44: stur            w0, [x3, #0xb]
    // 0xba2f48: r0 = Instance__DeferringMouseCursor
    //     0xba2f48: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2f4c: ArrayStore: r3[0] = r0  ; List_4
    //     0xba2f4c: stur            w0, [x3, #0x17]
    // 0xba2f50: ldur            x1, [fp, #-0x28]
    // 0xba2f54: StoreField: r3->field_7 = r1
    //     0xba2f54: stur            w1, [x3, #7]
    // 0xba2f58: r1 = Null
    //     0xba2f58: mov             x1, NULL
    // 0xba2f5c: r2 = 4
    //     0xba2f5c: movz            x2, #0x4
    // 0xba2f60: r0 = AllocateArray()
    //     0xba2f60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba2f64: mov             x2, x0
    // 0xba2f68: ldur            x0, [fp, #-0x30]
    // 0xba2f6c: stur            x2, [fp, #-0x18]
    // 0xba2f70: StoreField: r2->field_f = r0
    //     0xba2f70: stur            w0, [x2, #0xf]
    // 0xba2f74: ldur            x0, [fp, #-0x38]
    // 0xba2f78: StoreField: r2->field_13 = r0
    //     0xba2f78: stur            w0, [x2, #0x13]
    // 0xba2f7c: r1 = <InlineSpan>
    //     0xba2f7c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xba2f80: ldr             x1, [x1, #0xe40]
    // 0xba2f84: r0 = AllocateGrowableArray()
    //     0xba2f84: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba2f88: mov             x1, x0
    // 0xba2f8c: ldur            x0, [fp, #-0x18]
    // 0xba2f90: stur            x1, [fp, #-0x28]
    // 0xba2f94: StoreField: r1->field_f = r0
    //     0xba2f94: stur            w0, [x1, #0xf]
    // 0xba2f98: r0 = 4
    //     0xba2f98: movz            x0, #0x4
    // 0xba2f9c: StoreField: r1->field_b = r0
    //     0xba2f9c: stur            w0, [x1, #0xb]
    // 0xba2fa0: r0 = TextSpan()
    //     0xba2fa0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xba2fa4: mov             x1, x0
    // 0xba2fa8: ldur            x0, [fp, #-0x28]
    // 0xba2fac: stur            x1, [fp, #-0x18]
    // 0xba2fb0: StoreField: r1->field_f = r0
    //     0xba2fb0: stur            w0, [x1, #0xf]
    // 0xba2fb4: r0 = Instance__DeferringMouseCursor
    //     0xba2fb4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xba2fb8: ArrayStore: r1[0] = r0  ; List_4
    //     0xba2fb8: stur            w0, [x1, #0x17]
    // 0xba2fbc: r0 = RichText()
    //     0xba2fbc: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xba2fc0: mov             x1, x0
    // 0xba2fc4: ldur            x2, [fp, #-0x18]
    // 0xba2fc8: stur            x0, [fp, #-0x18]
    // 0xba2fcc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xba2fcc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xba2fd0: r0 = RichText()
    //     0xba2fd0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xba2fd4: r0 = ConstrainedBox()
    //     0xba2fd4: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xba2fd8: mov             x1, x0
    // 0xba2fdc: ldur            x0, [fp, #-0x20]
    // 0xba2fe0: stur            x1, [fp, #-0x28]
    // 0xba2fe4: StoreField: r1->field_f = r0
    //     0xba2fe4: stur            w0, [x1, #0xf]
    // 0xba2fe8: ldur            x0, [fp, #-0x18]
    // 0xba2fec: StoreField: r1->field_b = r0
    //     0xba2fec: stur            w0, [x1, #0xb]
    // 0xba2ff0: ldur            x0, [fp, #-8]
    // 0xba2ff4: LoadField: r2 = r0->field_b
    //     0xba2ff4: ldur            w2, [x0, #0xb]
    // 0xba2ff8: DecompressPointer r2
    //     0xba2ff8: add             x2, x2, HEAP, lsl #32
    // 0xba2ffc: cmp             w2, NULL
    // 0xba3000: b.eq            #0xba31a8
    // 0xba3004: LoadField: r0 = r2->field_f
    //     0xba3004: ldur            w0, [x2, #0xf]
    // 0xba3008: DecompressPointer r0
    //     0xba3008: add             x0, x0, HEAP, lsl #32
    // 0xba300c: cmp             w0, NULL
    // 0xba3010: b.ne            #0xba301c
    // 0xba3014: r0 = Null
    //     0xba3014: mov             x0, NULL
    // 0xba3018: b               #0xba3044
    // 0xba301c: LoadField: r2 = r0->field_2b
    //     0xba301c: ldur            w2, [x0, #0x2b]
    // 0xba3020: DecompressPointer r2
    //     0xba3020: add             x2, x2, HEAP, lsl #32
    // 0xba3024: r0 = LoadClassIdInstr(r2)
    //     0xba3024: ldur            x0, [x2, #-1]
    //     0xba3028: ubfx            x0, x0, #0xc, #0x14
    // 0xba302c: str             x2, [SP]
    // 0xba3030: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xba3030: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xba3034: r0 = GDT[cid_x0 + 0x2700]()
    //     0xba3034: movz            x17, #0x2700
    //     0xba3038: add             lr, x0, x17
    //     0xba303c: ldr             lr, [x21, lr, lsl #3]
    //     0xba3040: blr             lr
    // 0xba3044: cmp             w0, NULL
    // 0xba3048: b.ne            #0xba3054
    // 0xba304c: r2 = " "
    //     0xba304c: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xba3050: b               #0xba3058
    // 0xba3054: mov             x2, x0
    // 0xba3058: ldur            x0, [fp, #-0x28]
    // 0xba305c: ldur            x1, [fp, #-0x10]
    // 0xba3060: stur            x2, [fp, #-8]
    // 0xba3064: r0 = of()
    //     0xba3064: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xba3068: LoadField: r1 = r0->field_87
    //     0xba3068: ldur            w1, [x0, #0x87]
    // 0xba306c: DecompressPointer r1
    //     0xba306c: add             x1, x1, HEAP, lsl #32
    // 0xba3070: LoadField: r0 = r1->field_2b
    //     0xba3070: ldur            w0, [x1, #0x2b]
    // 0xba3074: DecompressPointer r0
    //     0xba3074: add             x0, x0, HEAP, lsl #32
    // 0xba3078: stur            x0, [fp, #-0x10]
    // 0xba307c: r1 = Instance_Color
    //     0xba307c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xba3080: d0 = 0.700000
    //     0xba3080: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xba3084: ldr             d0, [x17, #0xf48]
    // 0xba3088: r0 = withOpacity()
    //     0xba3088: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xba308c: r16 = 12.000000
    //     0xba308c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xba3090: ldr             x16, [x16, #0x9e8]
    // 0xba3094: stp             x0, x16, [SP]
    // 0xba3098: ldur            x1, [fp, #-0x10]
    // 0xba309c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xba309c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xba30a0: ldr             x4, [x4, #0xaa0]
    // 0xba30a4: r0 = copyWith()
    //     0xba30a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xba30a8: stur            x0, [fp, #-0x10]
    // 0xba30ac: r0 = Text()
    //     0xba30ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xba30b0: mov             x3, x0
    // 0xba30b4: ldur            x0, [fp, #-8]
    // 0xba30b8: stur            x3, [fp, #-0x18]
    // 0xba30bc: StoreField: r3->field_b = r0
    //     0xba30bc: stur            w0, [x3, #0xb]
    // 0xba30c0: ldur            x0, [fp, #-0x10]
    // 0xba30c4: StoreField: r3->field_13 = r0
    //     0xba30c4: stur            w0, [x3, #0x13]
    // 0xba30c8: r1 = Null
    //     0xba30c8: mov             x1, NULL
    // 0xba30cc: r2 = 6
    //     0xba30cc: movz            x2, #0x6
    // 0xba30d0: r0 = AllocateArray()
    //     0xba30d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xba30d4: mov             x2, x0
    // 0xba30d8: ldur            x0, [fp, #-0x28]
    // 0xba30dc: stur            x2, [fp, #-8]
    // 0xba30e0: StoreField: r2->field_f = r0
    //     0xba30e0: stur            w0, [x2, #0xf]
    // 0xba30e4: r16 = Instance_Spacer
    //     0xba30e4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xba30e8: ldr             x16, [x16, #0xf0]
    // 0xba30ec: StoreField: r2->field_13 = r16
    //     0xba30ec: stur            w16, [x2, #0x13]
    // 0xba30f0: ldur            x0, [fp, #-0x18]
    // 0xba30f4: ArrayStore: r2[0] = r0  ; List_4
    //     0xba30f4: stur            w0, [x2, #0x17]
    // 0xba30f8: r1 = <Widget>
    //     0xba30f8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xba30fc: r0 = AllocateGrowableArray()
    //     0xba30fc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xba3100: mov             x1, x0
    // 0xba3104: ldur            x0, [fp, #-8]
    // 0xba3108: stur            x1, [fp, #-0x10]
    // 0xba310c: StoreField: r1->field_f = r0
    //     0xba310c: stur            w0, [x1, #0xf]
    // 0xba3110: r0 = 6
    //     0xba3110: movz            x0, #0x6
    // 0xba3114: StoreField: r1->field_b = r0
    //     0xba3114: stur            w0, [x1, #0xb]
    // 0xba3118: r0 = Row()
    //     0xba3118: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xba311c: r1 = Instance_Axis
    //     0xba311c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xba3120: StoreField: r0->field_f = r1
    //     0xba3120: stur            w1, [x0, #0xf]
    // 0xba3124: r1 = Instance_MainAxisAlignment
    //     0xba3124: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xba3128: ldr             x1, [x1, #0xa08]
    // 0xba312c: StoreField: r0->field_13 = r1
    //     0xba312c: stur            w1, [x0, #0x13]
    // 0xba3130: r1 = Instance_MainAxisSize
    //     0xba3130: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xba3134: ldr             x1, [x1, #0xa10]
    // 0xba3138: ArrayStore: r0[0] = r1  ; List_4
    //     0xba3138: stur            w1, [x0, #0x17]
    // 0xba313c: r1 = Instance_CrossAxisAlignment
    //     0xba313c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xba3140: ldr             x1, [x1, #0xa18]
    // 0xba3144: StoreField: r0->field_1b = r1
    //     0xba3144: stur            w1, [x0, #0x1b]
    // 0xba3148: r1 = Instance_VerticalDirection
    //     0xba3148: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xba314c: ldr             x1, [x1, #0xa20]
    // 0xba3150: StoreField: r0->field_23 = r1
    //     0xba3150: stur            w1, [x0, #0x23]
    // 0xba3154: r1 = Instance_Clip
    //     0xba3154: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xba3158: ldr             x1, [x1, #0x38]
    // 0xba315c: StoreField: r0->field_2b = r1
    //     0xba315c: stur            w1, [x0, #0x2b]
    // 0xba3160: StoreField: r0->field_2f = rZR
    //     0xba3160: stur            xzr, [x0, #0x2f]
    // 0xba3164: ldur            x1, [fp, #-0x10]
    // 0xba3168: StoreField: r0->field_b = r1
    //     0xba3168: stur            w1, [x0, #0xb]
    // 0xba316c: LeaveFrame
    //     0xba316c: mov             SP, fp
    //     0xba3170: ldp             fp, lr, [SP], #0x10
    // 0xba3174: ret
    //     0xba3174: ret             
    // 0xba3178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xba3178: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xba317c: b               #0xba28c8
    // 0xba3180: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3180: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba3184: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3184: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba3188: r9 = value
    //     0xba3188: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a698] Field <<EMAIL>>: late (offset: 0x14)
    //     0xba318c: ldr             x9, [x9, #0x698]
    // 0xba3190: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xba3190: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xba3194: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3194: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba3198: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba3198: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xba319c: r9 = customisedValue
    //     0xba319c: add             x9, PP, #0x6a, lsl #12  ; [pp+0x6a6a0] Field <<EMAIL>>: late (offset: 0x18)
    //     0xba31a0: ldr             x9, [x9, #0x6a0]
    // 0xba31a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xba31a4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xba31a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xba31a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4035, size: 0x14, field offset: 0xc
//   const constructor, 
class BagSingleSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7ff10, size: 0x30
    // 0xc7ff10: EnterFrame
    //     0xc7ff10: stp             fp, lr, [SP, #-0x10]!
    //     0xc7ff14: mov             fp, SP
    // 0xc7ff18: mov             x0, x1
    // 0xc7ff1c: r1 = <BagSingleSelect>
    //     0xc7ff1c: add             x1, PP, #0x61, lsl #12  ; [pp+0x61d28] TypeArguments: <BagSingleSelect>
    //     0xc7ff20: ldr             x1, [x1, #0xd28]
    // 0xc7ff24: r0 = _BagSingleSelectState()
    //     0xc7ff24: bl              #0xc7ff40  ; Allocate_BagSingleSelectStateStub -> _BagSingleSelectState (size=0x1c)
    // 0xc7ff28: r1 = Sentinel
    //     0xc7ff28: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7ff2c: StoreField: r0->field_13 = r1
    //     0xc7ff2c: stur            w1, [x0, #0x13]
    // 0xc7ff30: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7ff30: stur            w1, [x0, #0x17]
    // 0xc7ff34: LeaveFrame
    //     0xc7ff34: mov             SP, fp
    //     0xc7ff38: ldp             fp, lr, [SP], #0x10
    // 0xc7ff3c: ret
    //     0xc7ff3c: ret             
  }
}
