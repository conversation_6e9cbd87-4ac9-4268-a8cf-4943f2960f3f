// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_bag_accordion.dart

// class id: 1049364, size: 0x8
class :: {
}

// class id: 3368, size: 0x14, field offset: 0x14
class _CheckoutBagAccordionState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb43e44, size: 0x1360
    // 0xb43e44: EnterFrame
    //     0xb43e44: stp             fp, lr, [SP, #-0x10]!
    //     0xb43e48: mov             fp, SP
    // 0xb43e4c: AllocStack(0x98)
    //     0xb43e4c: sub             SP, SP, #0x98
    // 0xb43e50: SetupParameters(_CheckoutBagAccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb43e50: mov             x0, x1
    //     0xb43e54: stur            x1, [fp, #-8]
    //     0xb43e58: mov             x1, x2
    //     0xb43e5c: stur            x2, [fp, #-0x10]
    // 0xb43e60: CheckStackOverflow
    //     0xb43e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb43e64: cmp             SP, x16
    //     0xb43e68: b.ls            #0xb45174
    // 0xb43e6c: r1 = 1
    //     0xb43e6c: movz            x1, #0x1
    // 0xb43e70: r0 = AllocateContext()
    //     0xb43e70: bl              #0x16f6108  ; AllocateContextStub
    // 0xb43e74: mov             x3, x0
    // 0xb43e78: ldur            x0, [fp, #-8]
    // 0xb43e7c: stur            x3, [fp, #-0x18]
    // 0xb43e80: StoreField: r3->field_f = r0
    //     0xb43e80: stur            w0, [x3, #0xf]
    // 0xb43e84: r1 = Null
    //     0xb43e84: mov             x1, NULL
    // 0xb43e88: r2 = 4
    //     0xb43e88: movz            x2, #0x4
    // 0xb43e8c: r0 = AllocateArray()
    //     0xb43e8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb43e90: r16 = "Bag "
    //     0xb43e90: add             x16, PP, #0x34, lsl #12  ; [pp+0x346a8] "Bag "
    //     0xb43e94: ldr             x16, [x16, #0x6a8]
    // 0xb43e98: StoreField: r0->field_f = r16
    //     0xb43e98: stur            w16, [x0, #0xf]
    // 0xb43e9c: ldur            x1, [fp, #-8]
    // 0xb43ea0: LoadField: r2 = r1->field_b
    //     0xb43ea0: ldur            w2, [x1, #0xb]
    // 0xb43ea4: DecompressPointer r2
    //     0xb43ea4: add             x2, x2, HEAP, lsl #32
    // 0xb43ea8: cmp             w2, NULL
    // 0xb43eac: b.eq            #0xb4517c
    // 0xb43eb0: LoadField: r3 = r2->field_b
    //     0xb43eb0: ldur            w3, [x2, #0xb]
    // 0xb43eb4: DecompressPointer r3
    //     0xb43eb4: add             x3, x3, HEAP, lsl #32
    // 0xb43eb8: LoadField: r2 = r3->field_b
    //     0xb43eb8: ldur            w2, [x3, #0xb]
    // 0xb43ebc: DecompressPointer r2
    //     0xb43ebc: add             x2, x2, HEAP, lsl #32
    // 0xb43ec0: cmp             w2, NULL
    // 0xb43ec4: b.ne            #0xb43ed0
    // 0xb43ec8: r2 = Null
    //     0xb43ec8: mov             x2, NULL
    // 0xb43ecc: b               #0xb43ef0
    // 0xb43ed0: LoadField: r3 = r2->field_f
    //     0xb43ed0: ldur            w3, [x2, #0xf]
    // 0xb43ed4: DecompressPointer r3
    //     0xb43ed4: add             x3, x3, HEAP, lsl #32
    // 0xb43ed8: cmp             w3, NULL
    // 0xb43edc: b.ne            #0xb43ee8
    // 0xb43ee0: r2 = Null
    //     0xb43ee0: mov             x2, NULL
    // 0xb43ee4: b               #0xb43ef0
    // 0xb43ee8: LoadField: r2 = r3->field_7
    //     0xb43ee8: ldur            w2, [x3, #7]
    // 0xb43eec: DecompressPointer r2
    //     0xb43eec: add             x2, x2, HEAP, lsl #32
    // 0xb43ef0: cmp             w2, NULL
    // 0xb43ef4: b.ne            #0xb43efc
    // 0xb43ef8: r2 = ""
    //     0xb43ef8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb43efc: StoreField: r0->field_13 = r2
    //     0xb43efc: stur            w2, [x0, #0x13]
    // 0xb43f00: str             x0, [SP]
    // 0xb43f04: r0 = _interpolate()
    //     0xb43f04: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb43f08: ldur            x1, [fp, #-0x10]
    // 0xb43f0c: stur            x0, [fp, #-0x20]
    // 0xb43f10: r0 = of()
    //     0xb43f10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb43f14: LoadField: r1 = r0->field_87
    //     0xb43f14: ldur            w1, [x0, #0x87]
    // 0xb43f18: DecompressPointer r1
    //     0xb43f18: add             x1, x1, HEAP, lsl #32
    // 0xb43f1c: LoadField: r0 = r1->field_7
    //     0xb43f1c: ldur            w0, [x1, #7]
    // 0xb43f20: DecompressPointer r0
    //     0xb43f20: add             x0, x0, HEAP, lsl #32
    // 0xb43f24: stur            x0, [fp, #-0x28]
    // 0xb43f28: r1 = Instance_Color
    //     0xb43f28: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb43f2c: d0 = 0.700000
    //     0xb43f2c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb43f30: ldr             d0, [x17, #0xf48]
    // 0xb43f34: r0 = withOpacity()
    //     0xb43f34: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb43f38: r16 = 14.000000
    //     0xb43f38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb43f3c: ldr             x16, [x16, #0x1d8]
    // 0xb43f40: stp             x0, x16, [SP]
    // 0xb43f44: ldur            x1, [fp, #-0x28]
    // 0xb43f48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb43f48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb43f4c: ldr             x4, [x4, #0xaa0]
    // 0xb43f50: r0 = copyWith()
    //     0xb43f50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb43f54: stur            x0, [fp, #-0x28]
    // 0xb43f58: r0 = Text()
    //     0xb43f58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb43f5c: mov             x3, x0
    // 0xb43f60: ldur            x0, [fp, #-0x20]
    // 0xb43f64: stur            x3, [fp, #-0x30]
    // 0xb43f68: StoreField: r3->field_b = r0
    //     0xb43f68: stur            w0, [x3, #0xb]
    // 0xb43f6c: ldur            x0, [fp, #-0x28]
    // 0xb43f70: StoreField: r3->field_13 = r0
    //     0xb43f70: stur            w0, [x3, #0x13]
    // 0xb43f74: r1 = <Widget>
    //     0xb43f74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb43f78: r2 = 0
    //     0xb43f78: movz            x2, #0
    // 0xb43f7c: r0 = _GrowableList()
    //     0xb43f7c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb43f80: mov             x2, x0
    // 0xb43f84: ldur            x0, [fp, #-8]
    // 0xb43f88: stur            x2, [fp, #-0x28]
    // 0xb43f8c: LoadField: r1 = r0->field_b
    //     0xb43f8c: ldur            w1, [x0, #0xb]
    // 0xb43f90: DecompressPointer r1
    //     0xb43f90: add             x1, x1, HEAP, lsl #32
    // 0xb43f94: cmp             w1, NULL
    // 0xb43f98: b.eq            #0xb45180
    // 0xb43f9c: LoadField: r3 = r1->field_b
    //     0xb43f9c: ldur            w3, [x1, #0xb]
    // 0xb43fa0: DecompressPointer r3
    //     0xb43fa0: add             x3, x3, HEAP, lsl #32
    // 0xb43fa4: LoadField: r1 = r3->field_b
    //     0xb43fa4: ldur            w1, [x3, #0xb]
    // 0xb43fa8: DecompressPointer r1
    //     0xb43fa8: add             x1, x1, HEAP, lsl #32
    // 0xb43fac: cmp             w1, NULL
    // 0xb43fb0: b.eq            #0xb4474c
    // 0xb43fb4: LoadField: r3 = r1->field_43
    //     0xb43fb4: ldur            w3, [x1, #0x43]
    // 0xb43fb8: DecompressPointer r3
    //     0xb43fb8: add             x3, x3, HEAP, lsl #32
    // 0xb43fbc: cmp             w3, NULL
    // 0xb43fc0: b.eq            #0xb4474c
    // 0xb43fc4: cmp             w3, NULL
    // 0xb43fc8: b.ne            #0xb43fd4
    // 0xb43fcc: r1 = Null
    //     0xb43fcc: mov             x1, NULL
    // 0xb43fd0: b               #0xb43fdc
    // 0xb43fd4: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb43fd4: ldur            w1, [x3, #0x17]
    // 0xb43fd8: DecompressPointer r1
    //     0xb43fd8: add             x1, x1, HEAP, lsl #32
    // 0xb43fdc: cmp             w1, NULL
    // 0xb43fe0: b.ne            #0xb43fec
    // 0xb43fe4: r3 = false
    //     0xb43fe4: add             x3, NULL, #0x30  ; false
    // 0xb43fe8: b               #0xb43ff0
    // 0xb43fec: mov             x3, x1
    // 0xb43ff0: stur            x3, [fp, #-0x20]
    // 0xb43ff4: r1 = Instance_Color
    //     0xb43ff4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb43ff8: d0 = 0.070000
    //     0xb43ff8: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb43ffc: ldr             d0, [x17, #0x5f8]
    // 0xb44000: r0 = withOpacity()
    //     0xb44000: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb44004: r16 = 1.000000
    //     0xb44004: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb44008: str             x16, [SP]
    // 0xb4400c: mov             x2, x0
    // 0xb44010: r1 = Null
    //     0xb44010: mov             x1, NULL
    // 0xb44014: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb44014: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb44018: ldr             x4, [x4, #0x108]
    // 0xb4401c: r0 = Border.all()
    //     0xb4401c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb44020: stur            x0, [fp, #-0x38]
    // 0xb44024: r0 = BoxDecoration()
    //     0xb44024: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb44028: mov             x2, x0
    // 0xb4402c: ldur            x0, [fp, #-0x38]
    // 0xb44030: stur            x2, [fp, #-0x40]
    // 0xb44034: StoreField: r2->field_f = r0
    //     0xb44034: stur            w0, [x2, #0xf]
    // 0xb44038: r0 = Instance_LinearGradient
    //     0xb44038: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xb4403c: ldr             x0, [x0, #0x660]
    // 0xb44040: StoreField: r2->field_1b = r0
    //     0xb44040: stur            w0, [x2, #0x1b]
    // 0xb44044: r0 = Instance_BoxShape
    //     0xb44044: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb44048: ldr             x0, [x0, #0x80]
    // 0xb4404c: StoreField: r2->field_23 = r0
    //     0xb4404c: stur            w0, [x2, #0x23]
    // 0xb44050: ldur            x1, [fp, #-0x10]
    // 0xb44054: r0 = of()
    //     0xb44054: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb44058: LoadField: r1 = r0->field_87
    //     0xb44058: ldur            w1, [x0, #0x87]
    // 0xb4405c: DecompressPointer r1
    //     0xb4405c: add             x1, x1, HEAP, lsl #32
    // 0xb44060: LoadField: r0 = r1->field_7
    //     0xb44060: ldur            w0, [x1, #7]
    // 0xb44064: DecompressPointer r0
    //     0xb44064: add             x0, x0, HEAP, lsl #32
    // 0xb44068: r16 = 12.000000
    //     0xb44068: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb4406c: ldr             x16, [x16, #0x9e8]
    // 0xb44070: r30 = Instance_Color
    //     0xb44070: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb44074: stp             lr, x16, [SP]
    // 0xb44078: mov             x1, x0
    // 0xb4407c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb4407c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb44080: ldr             x4, [x4, #0xaa0]
    // 0xb44084: r0 = copyWith()
    //     0xb44084: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb44088: stur            x0, [fp, #-0x38]
    // 0xb4408c: r0 = Text()
    //     0xb4408c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb44090: mov             x1, x0
    // 0xb44094: r0 = "Free"
    //     0xb44094: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb44098: ldr             x0, [x0, #0x668]
    // 0xb4409c: stur            x1, [fp, #-0x48]
    // 0xb440a0: StoreField: r1->field_b = r0
    //     0xb440a0: stur            w0, [x1, #0xb]
    // 0xb440a4: ldur            x2, [fp, #-0x38]
    // 0xb440a8: StoreField: r1->field_13 = r2
    //     0xb440a8: stur            w2, [x1, #0x13]
    // 0xb440ac: r0 = Center()
    //     0xb440ac: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb440b0: mov             x1, x0
    // 0xb440b4: r0 = Instance_Alignment
    //     0xb440b4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb440b8: ldr             x0, [x0, #0xb10]
    // 0xb440bc: stur            x1, [fp, #-0x38]
    // 0xb440c0: StoreField: r1->field_f = r0
    //     0xb440c0: stur            w0, [x1, #0xf]
    // 0xb440c4: ldur            x2, [fp, #-0x48]
    // 0xb440c8: StoreField: r1->field_b = r2
    //     0xb440c8: stur            w2, [x1, #0xb]
    // 0xb440cc: r0 = RotatedBox()
    //     0xb440cc: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xb440d0: mov             x1, x0
    // 0xb440d4: r0 = -1
    //     0xb440d4: movn            x0, #0
    // 0xb440d8: stur            x1, [fp, #-0x48]
    // 0xb440dc: StoreField: r1->field_f = r0
    //     0xb440dc: stur            x0, [x1, #0xf]
    // 0xb440e0: ldur            x2, [fp, #-0x38]
    // 0xb440e4: StoreField: r1->field_b = r2
    //     0xb440e4: stur            w2, [x1, #0xb]
    // 0xb440e8: r0 = Container()
    //     0xb440e8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb440ec: stur            x0, [fp, #-0x38]
    // 0xb440f0: r16 = 24.000000
    //     0xb440f0: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb440f4: ldr             x16, [x16, #0xba8]
    // 0xb440f8: r30 = 56.000000
    //     0xb440f8: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb440fc: ldr             lr, [lr, #0xb78]
    // 0xb44100: stp             lr, x16, [SP, #0x10]
    // 0xb44104: r16 = Instance_Color
    //     0xb44104: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb44108: ldr             x16, [x16, #0x858]
    // 0xb4410c: ldur            lr, [fp, #-0x48]
    // 0xb44110: stp             lr, x16, [SP]
    // 0xb44114: mov             x1, x0
    // 0xb44118: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xb44118: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb4411c: ldr             x4, [x4, #0x670]
    // 0xb44120: r0 = Container()
    //     0xb44120: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb44124: ldur            x0, [fp, #-8]
    // 0xb44128: LoadField: r1 = r0->field_b
    //     0xb44128: ldur            w1, [x0, #0xb]
    // 0xb4412c: DecompressPointer r1
    //     0xb4412c: add             x1, x1, HEAP, lsl #32
    // 0xb44130: cmp             w1, NULL
    // 0xb44134: b.eq            #0xb45184
    // 0xb44138: LoadField: r2 = r1->field_b
    //     0xb44138: ldur            w2, [x1, #0xb]
    // 0xb4413c: DecompressPointer r2
    //     0xb4413c: add             x2, x2, HEAP, lsl #32
    // 0xb44140: LoadField: r1 = r2->field_b
    //     0xb44140: ldur            w1, [x2, #0xb]
    // 0xb44144: DecompressPointer r1
    //     0xb44144: add             x1, x1, HEAP, lsl #32
    // 0xb44148: cmp             w1, NULL
    // 0xb4414c: b.ne            #0xb44158
    // 0xb44150: r1 = Null
    //     0xb44150: mov             x1, NULL
    // 0xb44154: b               #0xb44178
    // 0xb44158: LoadField: r2 = r1->field_43
    //     0xb44158: ldur            w2, [x1, #0x43]
    // 0xb4415c: DecompressPointer r2
    //     0xb4415c: add             x2, x2, HEAP, lsl #32
    // 0xb44160: cmp             w2, NULL
    // 0xb44164: b.ne            #0xb44170
    // 0xb44168: r1 = Null
    //     0xb44168: mov             x1, NULL
    // 0xb4416c: b               #0xb44178
    // 0xb44170: LoadField: r1 = r2->field_7
    //     0xb44170: ldur            w1, [x2, #7]
    // 0xb44174: DecompressPointer r1
    //     0xb44174: add             x1, x1, HEAP, lsl #32
    // 0xb44178: cmp             w1, NULL
    // 0xb4417c: b.ne            #0xb44188
    // 0xb44180: r2 = ""
    //     0xb44180: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb44184: b               #0xb4418c
    // 0xb44188: mov             x2, x1
    // 0xb4418c: stur            x2, [fp, #-0x48]
    // 0xb44190: r0 = CachedNetworkImage()
    //     0xb44190: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb44194: stur            x0, [fp, #-0x50]
    // 0xb44198: r16 = 56.000000
    //     0xb44198: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb4419c: ldr             x16, [x16, #0xb78]
    // 0xb441a0: r30 = 56.000000
    //     0xb441a0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb441a4: ldr             lr, [lr, #0xb78]
    // 0xb441a8: stp             lr, x16, [SP, #8]
    // 0xb441ac: r16 = Instance_BoxFit
    //     0xb441ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb441b0: ldr             x16, [x16, #0x118]
    // 0xb441b4: str             x16, [SP]
    // 0xb441b8: mov             x1, x0
    // 0xb441bc: ldur            x2, [fp, #-0x48]
    // 0xb441c0: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb441c0: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb441c4: ldr             x4, [x4, #0xb40]
    // 0xb441c8: r0 = CachedNetworkImage()
    //     0xb441c8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb441cc: ldur            x0, [fp, #-8]
    // 0xb441d0: LoadField: r1 = r0->field_b
    //     0xb441d0: ldur            w1, [x0, #0xb]
    // 0xb441d4: DecompressPointer r1
    //     0xb441d4: add             x1, x1, HEAP, lsl #32
    // 0xb441d8: cmp             w1, NULL
    // 0xb441dc: b.eq            #0xb45188
    // 0xb441e0: LoadField: r2 = r1->field_b
    //     0xb441e0: ldur            w2, [x1, #0xb]
    // 0xb441e4: DecompressPointer r2
    //     0xb441e4: add             x2, x2, HEAP, lsl #32
    // 0xb441e8: LoadField: r1 = r2->field_b
    //     0xb441e8: ldur            w1, [x2, #0xb]
    // 0xb441ec: DecompressPointer r1
    //     0xb441ec: add             x1, x1, HEAP, lsl #32
    // 0xb441f0: cmp             w1, NULL
    // 0xb441f4: b.ne            #0xb44200
    // 0xb441f8: r1 = Null
    //     0xb441f8: mov             x1, NULL
    // 0xb441fc: b               #0xb44220
    // 0xb44200: LoadField: r2 = r1->field_43
    //     0xb44200: ldur            w2, [x1, #0x43]
    // 0xb44204: DecompressPointer r2
    //     0xb44204: add             x2, x2, HEAP, lsl #32
    // 0xb44208: cmp             w2, NULL
    // 0xb4420c: b.ne            #0xb44218
    // 0xb44210: r1 = Null
    //     0xb44210: mov             x1, NULL
    // 0xb44214: b               #0xb44220
    // 0xb44218: LoadField: r1 = r2->field_b
    //     0xb44218: ldur            w1, [x2, #0xb]
    // 0xb4421c: DecompressPointer r1
    //     0xb4421c: add             x1, x1, HEAP, lsl #32
    // 0xb44220: cmp             w1, NULL
    // 0xb44224: b.ne            #0xb44230
    // 0xb44228: r2 = ""
    //     0xb44228: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb4422c: b               #0xb44234
    // 0xb44230: mov             x2, x1
    // 0xb44234: ldur            x1, [fp, #-0x10]
    // 0xb44238: stur            x2, [fp, #-0x48]
    // 0xb4423c: r0 = of()
    //     0xb4423c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb44240: LoadField: r1 = r0->field_87
    //     0xb44240: ldur            w1, [x0, #0x87]
    // 0xb44244: DecompressPointer r1
    //     0xb44244: add             x1, x1, HEAP, lsl #32
    // 0xb44248: LoadField: r0 = r1->field_7
    //     0xb44248: ldur            w0, [x1, #7]
    // 0xb4424c: DecompressPointer r0
    //     0xb4424c: add             x0, x0, HEAP, lsl #32
    // 0xb44250: r16 = 12.000000
    //     0xb44250: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb44254: ldr             x16, [x16, #0x9e8]
    // 0xb44258: r30 = Instance_Color
    //     0xb44258: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb4425c: stp             lr, x16, [SP]
    // 0xb44260: mov             x1, x0
    // 0xb44264: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb44264: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb44268: ldr             x4, [x4, #0xaa0]
    // 0xb4426c: r0 = copyWith()
    //     0xb4426c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb44270: stur            x0, [fp, #-0x58]
    // 0xb44274: r0 = Text()
    //     0xb44274: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb44278: mov             x1, x0
    // 0xb4427c: ldur            x0, [fp, #-0x48]
    // 0xb44280: stur            x1, [fp, #-0x60]
    // 0xb44284: StoreField: r1->field_b = r0
    //     0xb44284: stur            w0, [x1, #0xb]
    // 0xb44288: ldur            x0, [fp, #-0x58]
    // 0xb4428c: StoreField: r1->field_13 = r0
    //     0xb4428c: stur            w0, [x1, #0x13]
    // 0xb44290: r0 = Instance_TextOverflow
    //     0xb44290: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb44294: ldr             x0, [x0, #0xe10]
    // 0xb44298: StoreField: r1->field_2b = r0
    //     0xb44298: stur            w0, [x1, #0x2b]
    // 0xb4429c: r2 = 2
    //     0xb4429c: movz            x2, #0x2
    // 0xb442a0: StoreField: r1->field_37 = r2
    //     0xb442a0: stur            w2, [x1, #0x37]
    // 0xb442a4: r0 = SizedBox()
    //     0xb442a4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb442a8: mov             x2, x0
    // 0xb442ac: r0 = 150.000000
    //     0xb442ac: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb442b0: ldr             x0, [x0, #0x690]
    // 0xb442b4: stur            x2, [fp, #-0x48]
    // 0xb442b8: StoreField: r2->field_f = r0
    //     0xb442b8: stur            w0, [x2, #0xf]
    // 0xb442bc: ldur            x1, [fp, #-0x60]
    // 0xb442c0: StoreField: r2->field_b = r1
    //     0xb442c0: stur            w1, [x2, #0xb]
    // 0xb442c4: ldur            x1, [fp, #-0x10]
    // 0xb442c8: r0 = of()
    //     0xb442c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb442cc: LoadField: r1 = r0->field_87
    //     0xb442cc: ldur            w1, [x0, #0x87]
    // 0xb442d0: DecompressPointer r1
    //     0xb442d0: add             x1, x1, HEAP, lsl #32
    // 0xb442d4: LoadField: r0 = r1->field_2b
    //     0xb442d4: ldur            w0, [x1, #0x2b]
    // 0xb442d8: DecompressPointer r0
    //     0xb442d8: add             x0, x0, HEAP, lsl #32
    // 0xb442dc: r16 = 12.000000
    //     0xb442dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb442e0: ldr             x16, [x16, #0x9e8]
    // 0xb442e4: r30 = Instance_Color
    //     0xb442e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb442e8: ldr             lr, [lr, #0x858]
    // 0xb442ec: stp             lr, x16, [SP]
    // 0xb442f0: mov             x1, x0
    // 0xb442f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb442f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb442f8: ldr             x4, [x4, #0xaa0]
    // 0xb442fc: r0 = copyWith()
    //     0xb442fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb44300: stur            x0, [fp, #-0x58]
    // 0xb44304: r0 = Text()
    //     0xb44304: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb44308: mov             x2, x0
    // 0xb4430c: r0 = "Free"
    //     0xb4430c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb44310: ldr             x0, [x0, #0x668]
    // 0xb44314: stur            x2, [fp, #-0x60]
    // 0xb44318: StoreField: r2->field_b = r0
    //     0xb44318: stur            w0, [x2, #0xb]
    // 0xb4431c: ldur            x1, [fp, #-0x58]
    // 0xb44320: StoreField: r2->field_13 = r1
    //     0xb44320: stur            w1, [x2, #0x13]
    // 0xb44324: ldur            x3, [fp, #-8]
    // 0xb44328: LoadField: r1 = r3->field_b
    //     0xb44328: ldur            w1, [x3, #0xb]
    // 0xb4432c: DecompressPointer r1
    //     0xb4432c: add             x1, x1, HEAP, lsl #32
    // 0xb44330: cmp             w1, NULL
    // 0xb44334: b.eq            #0xb4518c
    // 0xb44338: LoadField: r4 = r1->field_b
    //     0xb44338: ldur            w4, [x1, #0xb]
    // 0xb4433c: DecompressPointer r4
    //     0xb4433c: add             x4, x4, HEAP, lsl #32
    // 0xb44340: LoadField: r1 = r4->field_b
    //     0xb44340: ldur            w1, [x4, #0xb]
    // 0xb44344: DecompressPointer r1
    //     0xb44344: add             x1, x1, HEAP, lsl #32
    // 0xb44348: cmp             w1, NULL
    // 0xb4434c: b.ne            #0xb44358
    // 0xb44350: r1 = Null
    //     0xb44350: mov             x1, NULL
    // 0xb44354: b               #0xb44378
    // 0xb44358: LoadField: r4 = r1->field_43
    //     0xb44358: ldur            w4, [x1, #0x43]
    // 0xb4435c: DecompressPointer r4
    //     0xb4435c: add             x4, x4, HEAP, lsl #32
    // 0xb44360: cmp             w4, NULL
    // 0xb44364: b.ne            #0xb44370
    // 0xb44368: r1 = Null
    //     0xb44368: mov             x1, NULL
    // 0xb4436c: b               #0xb44378
    // 0xb44370: LoadField: r1 = r4->field_13
    //     0xb44370: ldur            w1, [x4, #0x13]
    // 0xb44374: DecompressPointer r1
    //     0xb44374: add             x1, x1, HEAP, lsl #32
    // 0xb44378: cmp             w1, NULL
    // 0xb4437c: b.ne            #0xb44388
    // 0xb44380: r9 = ""
    //     0xb44380: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb44384: b               #0xb4438c
    // 0xb44388: mov             x9, x1
    // 0xb4438c: ldur            x7, [fp, #-0x28]
    // 0xb44390: ldur            x8, [fp, #-0x20]
    // 0xb44394: ldur            x6, [fp, #-0x38]
    // 0xb44398: ldur            x5, [fp, #-0x50]
    // 0xb4439c: ldur            x4, [fp, #-0x48]
    // 0xb443a0: ldur            x1, [fp, #-0x10]
    // 0xb443a4: stur            x9, [fp, #-0x58]
    // 0xb443a8: r0 = of()
    //     0xb443a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb443ac: LoadField: r1 = r0->field_87
    //     0xb443ac: ldur            w1, [x0, #0x87]
    // 0xb443b0: DecompressPointer r1
    //     0xb443b0: add             x1, x1, HEAP, lsl #32
    // 0xb443b4: LoadField: r0 = r1->field_2b
    //     0xb443b4: ldur            w0, [x1, #0x2b]
    // 0xb443b8: DecompressPointer r0
    //     0xb443b8: add             x0, x0, HEAP, lsl #32
    // 0xb443bc: r16 = 12.000000
    //     0xb443bc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb443c0: ldr             x16, [x16, #0x9e8]
    // 0xb443c4: r30 = Instance_TextDecoration
    //     0xb443c4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb443c8: ldr             lr, [lr, #0xe30]
    // 0xb443cc: stp             lr, x16, [SP]
    // 0xb443d0: mov             x1, x0
    // 0xb443d4: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb443d4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb443d8: ldr             x4, [x4, #0x698]
    // 0xb443dc: r0 = copyWith()
    //     0xb443dc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb443e0: stur            x0, [fp, #-0x68]
    // 0xb443e4: r0 = Text()
    //     0xb443e4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb443e8: mov             x3, x0
    // 0xb443ec: ldur            x0, [fp, #-0x58]
    // 0xb443f0: stur            x3, [fp, #-0x70]
    // 0xb443f4: StoreField: r3->field_b = r0
    //     0xb443f4: stur            w0, [x3, #0xb]
    // 0xb443f8: ldur            x0, [fp, #-0x68]
    // 0xb443fc: StoreField: r3->field_13 = r0
    //     0xb443fc: stur            w0, [x3, #0x13]
    // 0xb44400: r1 = Null
    //     0xb44400: mov             x1, NULL
    // 0xb44404: r2 = 6
    //     0xb44404: movz            x2, #0x6
    // 0xb44408: r0 = AllocateArray()
    //     0xb44408: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb4440c: mov             x2, x0
    // 0xb44410: ldur            x0, [fp, #-0x60]
    // 0xb44414: stur            x2, [fp, #-0x58]
    // 0xb44418: StoreField: r2->field_f = r0
    //     0xb44418: stur            w0, [x2, #0xf]
    // 0xb4441c: r16 = Instance_SizedBox
    //     0xb4441c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb44420: ldr             x16, [x16, #0xa50]
    // 0xb44424: StoreField: r2->field_13 = r16
    //     0xb44424: stur            w16, [x2, #0x13]
    // 0xb44428: ldur            x0, [fp, #-0x70]
    // 0xb4442c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb4442c: stur            w0, [x2, #0x17]
    // 0xb44430: r1 = <Widget>
    //     0xb44430: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb44434: r0 = AllocateGrowableArray()
    //     0xb44434: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb44438: mov             x1, x0
    // 0xb4443c: ldur            x0, [fp, #-0x58]
    // 0xb44440: stur            x1, [fp, #-0x60]
    // 0xb44444: StoreField: r1->field_f = r0
    //     0xb44444: stur            w0, [x1, #0xf]
    // 0xb44448: r2 = 6
    //     0xb44448: movz            x2, #0x6
    // 0xb4444c: StoreField: r1->field_b = r2
    //     0xb4444c: stur            w2, [x1, #0xb]
    // 0xb44450: r0 = Row()
    //     0xb44450: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb44454: mov             x3, x0
    // 0xb44458: r0 = Instance_Axis
    //     0xb44458: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb4445c: stur            x3, [fp, #-0x58]
    // 0xb44460: StoreField: r3->field_f = r0
    //     0xb44460: stur            w0, [x3, #0xf]
    // 0xb44464: r4 = Instance_MainAxisAlignment
    //     0xb44464: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb44468: ldr             x4, [x4, #0xa08]
    // 0xb4446c: StoreField: r3->field_13 = r4
    //     0xb4446c: stur            w4, [x3, #0x13]
    // 0xb44470: r5 = Instance_MainAxisSize
    //     0xb44470: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb44474: ldr             x5, [x5, #0xa10]
    // 0xb44478: ArrayStore: r3[0] = r5  ; List_4
    //     0xb44478: stur            w5, [x3, #0x17]
    // 0xb4447c: r6 = Instance_CrossAxisAlignment
    //     0xb4447c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb44480: ldr             x6, [x6, #0xa18]
    // 0xb44484: StoreField: r3->field_1b = r6
    //     0xb44484: stur            w6, [x3, #0x1b]
    // 0xb44488: r7 = Instance_VerticalDirection
    //     0xb44488: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb4448c: ldr             x7, [x7, #0xa20]
    // 0xb44490: StoreField: r3->field_23 = r7
    //     0xb44490: stur            w7, [x3, #0x23]
    // 0xb44494: r8 = Instance_Clip
    //     0xb44494: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb44498: ldr             x8, [x8, #0x38]
    // 0xb4449c: StoreField: r3->field_2b = r8
    //     0xb4449c: stur            w8, [x3, #0x2b]
    // 0xb444a0: StoreField: r3->field_2f = rZR
    //     0xb444a0: stur            xzr, [x3, #0x2f]
    // 0xb444a4: ldur            x1, [fp, #-0x60]
    // 0xb444a8: StoreField: r3->field_b = r1
    //     0xb444a8: stur            w1, [x3, #0xb]
    // 0xb444ac: r1 = Null
    //     0xb444ac: mov             x1, NULL
    // 0xb444b0: r2 = 6
    //     0xb444b0: movz            x2, #0x6
    // 0xb444b4: r0 = AllocateArray()
    //     0xb444b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb444b8: mov             x2, x0
    // 0xb444bc: ldur            x0, [fp, #-0x48]
    // 0xb444c0: stur            x2, [fp, #-0x60]
    // 0xb444c4: StoreField: r2->field_f = r0
    //     0xb444c4: stur            w0, [x2, #0xf]
    // 0xb444c8: r16 = Instance_SizedBox
    //     0xb444c8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb444cc: ldr             x16, [x16, #0xc70]
    // 0xb444d0: StoreField: r2->field_13 = r16
    //     0xb444d0: stur            w16, [x2, #0x13]
    // 0xb444d4: ldur            x0, [fp, #-0x58]
    // 0xb444d8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb444d8: stur            w0, [x2, #0x17]
    // 0xb444dc: r1 = <Widget>
    //     0xb444dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb444e0: r0 = AllocateGrowableArray()
    //     0xb444e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb444e4: mov             x1, x0
    // 0xb444e8: ldur            x0, [fp, #-0x60]
    // 0xb444ec: stur            x1, [fp, #-0x48]
    // 0xb444f0: StoreField: r1->field_f = r0
    //     0xb444f0: stur            w0, [x1, #0xf]
    // 0xb444f4: r2 = 6
    //     0xb444f4: movz            x2, #0x6
    // 0xb444f8: StoreField: r1->field_b = r2
    //     0xb444f8: stur            w2, [x1, #0xb]
    // 0xb444fc: r0 = Column()
    //     0xb444fc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb44500: mov             x1, x0
    // 0xb44504: r0 = Instance_Axis
    //     0xb44504: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb44508: stur            x1, [fp, #-0x58]
    // 0xb4450c: StoreField: r1->field_f = r0
    //     0xb4450c: stur            w0, [x1, #0xf]
    // 0xb44510: r2 = Instance_MainAxisAlignment
    //     0xb44510: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb44514: ldr             x2, [x2, #0xa08]
    // 0xb44518: StoreField: r1->field_13 = r2
    //     0xb44518: stur            w2, [x1, #0x13]
    // 0xb4451c: r3 = Instance_MainAxisSize
    //     0xb4451c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb44520: ldr             x3, [x3, #0xa10]
    // 0xb44524: ArrayStore: r1[0] = r3  ; List_4
    //     0xb44524: stur            w3, [x1, #0x17]
    // 0xb44528: r4 = Instance_CrossAxisAlignment
    //     0xb44528: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb4452c: ldr             x4, [x4, #0x890]
    // 0xb44530: StoreField: r1->field_1b = r4
    //     0xb44530: stur            w4, [x1, #0x1b]
    // 0xb44534: r5 = Instance_VerticalDirection
    //     0xb44534: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb44538: ldr             x5, [x5, #0xa20]
    // 0xb4453c: StoreField: r1->field_23 = r5
    //     0xb4453c: stur            w5, [x1, #0x23]
    // 0xb44540: r6 = Instance_Clip
    //     0xb44540: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb44544: ldr             x6, [x6, #0x38]
    // 0xb44548: StoreField: r1->field_2b = r6
    //     0xb44548: stur            w6, [x1, #0x2b]
    // 0xb4454c: StoreField: r1->field_2f = rZR
    //     0xb4454c: stur            xzr, [x1, #0x2f]
    // 0xb44550: ldur            x7, [fp, #-0x48]
    // 0xb44554: StoreField: r1->field_b = r7
    //     0xb44554: stur            w7, [x1, #0xb]
    // 0xb44558: r0 = Padding()
    //     0xb44558: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb4455c: mov             x2, x0
    // 0xb44560: r0 = Instance_EdgeInsets
    //     0xb44560: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb44564: ldr             x0, [x0, #0xa78]
    // 0xb44568: stur            x2, [fp, #-0x48]
    // 0xb4456c: StoreField: r2->field_f = r0
    //     0xb4456c: stur            w0, [x2, #0xf]
    // 0xb44570: ldur            x1, [fp, #-0x58]
    // 0xb44574: StoreField: r2->field_b = r1
    //     0xb44574: stur            w1, [x2, #0xb]
    // 0xb44578: r1 = <FlexParentData>
    //     0xb44578: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb4457c: ldr             x1, [x1, #0xe00]
    // 0xb44580: r0 = Expanded()
    //     0xb44580: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb44584: mov             x3, x0
    // 0xb44588: r0 = 1
    //     0xb44588: movz            x0, #0x1
    // 0xb4458c: stur            x3, [fp, #-0x58]
    // 0xb44590: StoreField: r3->field_13 = r0
    //     0xb44590: stur            x0, [x3, #0x13]
    // 0xb44594: r4 = Instance_FlexFit
    //     0xb44594: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb44598: ldr             x4, [x4, #0xe08]
    // 0xb4459c: StoreField: r3->field_1b = r4
    //     0xb4459c: stur            w4, [x3, #0x1b]
    // 0xb445a0: ldur            x1, [fp, #-0x48]
    // 0xb445a4: StoreField: r3->field_b = r1
    //     0xb445a4: stur            w1, [x3, #0xb]
    // 0xb445a8: r1 = Null
    //     0xb445a8: mov             x1, NULL
    // 0xb445ac: r2 = 6
    //     0xb445ac: movz            x2, #0x6
    // 0xb445b0: r0 = AllocateArray()
    //     0xb445b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb445b4: mov             x2, x0
    // 0xb445b8: ldur            x0, [fp, #-0x38]
    // 0xb445bc: stur            x2, [fp, #-0x48]
    // 0xb445c0: StoreField: r2->field_f = r0
    //     0xb445c0: stur            w0, [x2, #0xf]
    // 0xb445c4: ldur            x0, [fp, #-0x50]
    // 0xb445c8: StoreField: r2->field_13 = r0
    //     0xb445c8: stur            w0, [x2, #0x13]
    // 0xb445cc: ldur            x0, [fp, #-0x58]
    // 0xb445d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb445d0: stur            w0, [x2, #0x17]
    // 0xb445d4: r1 = <Widget>
    //     0xb445d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb445d8: r0 = AllocateGrowableArray()
    //     0xb445d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb445dc: mov             x1, x0
    // 0xb445e0: ldur            x0, [fp, #-0x48]
    // 0xb445e4: stur            x1, [fp, #-0x38]
    // 0xb445e8: StoreField: r1->field_f = r0
    //     0xb445e8: stur            w0, [x1, #0xf]
    // 0xb445ec: r2 = 6
    //     0xb445ec: movz            x2, #0x6
    // 0xb445f0: StoreField: r1->field_b = r2
    //     0xb445f0: stur            w2, [x1, #0xb]
    // 0xb445f4: r0 = Row()
    //     0xb445f4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb445f8: mov             x1, x0
    // 0xb445fc: r0 = Instance_Axis
    //     0xb445fc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb44600: stur            x1, [fp, #-0x48]
    // 0xb44604: StoreField: r1->field_f = r0
    //     0xb44604: stur            w0, [x1, #0xf]
    // 0xb44608: r2 = Instance_MainAxisAlignment
    //     0xb44608: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb4460c: ldr             x2, [x2, #0xa08]
    // 0xb44610: StoreField: r1->field_13 = r2
    //     0xb44610: stur            w2, [x1, #0x13]
    // 0xb44614: r3 = Instance_MainAxisSize
    //     0xb44614: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb44618: ldr             x3, [x3, #0xa10]
    // 0xb4461c: ArrayStore: r1[0] = r3  ; List_4
    //     0xb4461c: stur            w3, [x1, #0x17]
    // 0xb44620: r4 = Instance_CrossAxisAlignment
    //     0xb44620: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb44624: ldr             x4, [x4, #0xa18]
    // 0xb44628: StoreField: r1->field_1b = r4
    //     0xb44628: stur            w4, [x1, #0x1b]
    // 0xb4462c: r5 = Instance_VerticalDirection
    //     0xb4462c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb44630: ldr             x5, [x5, #0xa20]
    // 0xb44634: StoreField: r1->field_23 = r5
    //     0xb44634: stur            w5, [x1, #0x23]
    // 0xb44638: r6 = Instance_Clip
    //     0xb44638: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb4463c: ldr             x6, [x6, #0x38]
    // 0xb44640: StoreField: r1->field_2b = r6
    //     0xb44640: stur            w6, [x1, #0x2b]
    // 0xb44644: StoreField: r1->field_2f = rZR
    //     0xb44644: stur            xzr, [x1, #0x2f]
    // 0xb44648: ldur            x7, [fp, #-0x38]
    // 0xb4464c: StoreField: r1->field_b = r7
    //     0xb4464c: stur            w7, [x1, #0xb]
    // 0xb44650: r0 = Container()
    //     0xb44650: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb44654: stur            x0, [fp, #-0x38]
    // 0xb44658: ldur            x16, [fp, #-0x40]
    // 0xb4465c: ldur            lr, [fp, #-0x48]
    // 0xb44660: stp             lr, x16, [SP]
    // 0xb44664: mov             x1, x0
    // 0xb44668: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb44668: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb4466c: ldr             x4, [x4, #0x88]
    // 0xb44670: r0 = Container()
    //     0xb44670: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb44674: r0 = Padding()
    //     0xb44674: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb44678: mov             x1, x0
    // 0xb4467c: r0 = Instance_EdgeInsets
    //     0xb4467c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb44680: ldr             x0, [x0, #0x778]
    // 0xb44684: stur            x1, [fp, #-0x40]
    // 0xb44688: StoreField: r1->field_f = r0
    //     0xb44688: stur            w0, [x1, #0xf]
    // 0xb4468c: ldur            x2, [fp, #-0x38]
    // 0xb44690: StoreField: r1->field_b = r2
    //     0xb44690: stur            w2, [x1, #0xb]
    // 0xb44694: r0 = Visibility()
    //     0xb44694: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb44698: mov             x2, x0
    // 0xb4469c: ldur            x0, [fp, #-0x40]
    // 0xb446a0: stur            x2, [fp, #-0x38]
    // 0xb446a4: StoreField: r2->field_b = r0
    //     0xb446a4: stur            w0, [x2, #0xb]
    // 0xb446a8: r0 = Instance_SizedBox
    //     0xb446a8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb446ac: StoreField: r2->field_f = r0
    //     0xb446ac: stur            w0, [x2, #0xf]
    // 0xb446b0: ldur            x1, [fp, #-0x20]
    // 0xb446b4: StoreField: r2->field_13 = r1
    //     0xb446b4: stur            w1, [x2, #0x13]
    // 0xb446b8: r3 = false
    //     0xb446b8: add             x3, NULL, #0x30  ; false
    // 0xb446bc: ArrayStore: r2[0] = r3  ; List_4
    //     0xb446bc: stur            w3, [x2, #0x17]
    // 0xb446c0: StoreField: r2->field_1b = r3
    //     0xb446c0: stur            w3, [x2, #0x1b]
    // 0xb446c4: StoreField: r2->field_1f = r3
    //     0xb446c4: stur            w3, [x2, #0x1f]
    // 0xb446c8: StoreField: r2->field_23 = r3
    //     0xb446c8: stur            w3, [x2, #0x23]
    // 0xb446cc: StoreField: r2->field_27 = r3
    //     0xb446cc: stur            w3, [x2, #0x27]
    // 0xb446d0: StoreField: r2->field_2b = r3
    //     0xb446d0: stur            w3, [x2, #0x2b]
    // 0xb446d4: ldur            x4, [fp, #-0x28]
    // 0xb446d8: LoadField: r1 = r4->field_b
    //     0xb446d8: ldur            w1, [x4, #0xb]
    // 0xb446dc: LoadField: r5 = r4->field_f
    //     0xb446dc: ldur            w5, [x4, #0xf]
    // 0xb446e0: DecompressPointer r5
    //     0xb446e0: add             x5, x5, HEAP, lsl #32
    // 0xb446e4: LoadField: r6 = r5->field_b
    //     0xb446e4: ldur            w6, [x5, #0xb]
    // 0xb446e8: r5 = LoadInt32Instr(r1)
    //     0xb446e8: sbfx            x5, x1, #1, #0x1f
    // 0xb446ec: stur            x5, [fp, #-0x78]
    // 0xb446f0: r1 = LoadInt32Instr(r6)
    //     0xb446f0: sbfx            x1, x6, #1, #0x1f
    // 0xb446f4: cmp             x5, x1
    // 0xb446f8: b.ne            #0xb44704
    // 0xb446fc: mov             x1, x4
    // 0xb44700: r0 = _growToNextCapacity()
    //     0xb44700: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb44704: ldur            x2, [fp, #-0x28]
    // 0xb44708: ldur            x3, [fp, #-0x78]
    // 0xb4470c: add             x0, x3, #1
    // 0xb44710: lsl             x1, x0, #1
    // 0xb44714: StoreField: r2->field_b = r1
    //     0xb44714: stur            w1, [x2, #0xb]
    // 0xb44718: LoadField: r1 = r2->field_f
    //     0xb44718: ldur            w1, [x2, #0xf]
    // 0xb4471c: DecompressPointer r1
    //     0xb4471c: add             x1, x1, HEAP, lsl #32
    // 0xb44720: ldur            x0, [fp, #-0x38]
    // 0xb44724: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb44724: add             x25, x1, x3, lsl #2
    //     0xb44728: add             x25, x25, #0xf
    //     0xb4472c: str             w0, [x25]
    //     0xb44730: tbz             w0, #0, #0xb4474c
    //     0xb44734: ldurb           w16, [x1, #-1]
    //     0xb44738: ldurb           w17, [x0, #-1]
    //     0xb4473c: and             x16, x17, x16, lsr #2
    //     0xb44740: tst             x16, HEAP, lsr #32
    //     0xb44744: b.eq            #0xb4474c
    //     0xb44748: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb4474c: ldur            x0, [fp, #-8]
    // 0xb44750: LoadField: r1 = r0->field_b
    //     0xb44750: ldur            w1, [x0, #0xb]
    // 0xb44754: DecompressPointer r1
    //     0xb44754: add             x1, x1, HEAP, lsl #32
    // 0xb44758: cmp             w1, NULL
    // 0xb4475c: b.eq            #0xb45190
    // 0xb44760: LoadField: r3 = r1->field_b
    //     0xb44760: ldur            w3, [x1, #0xb]
    // 0xb44764: DecompressPointer r3
    //     0xb44764: add             x3, x3, HEAP, lsl #32
    // 0xb44768: LoadField: r1 = r3->field_b
    //     0xb44768: ldur            w1, [x3, #0xb]
    // 0xb4476c: DecompressPointer r1
    //     0xb4476c: add             x1, x1, HEAP, lsl #32
    // 0xb44770: cmp             w1, NULL
    // 0xb44774: b.ne            #0xb44780
    // 0xb44778: mov             x3, x2
    // 0xb4477c: b               #0xb44f34
    // 0xb44780: LoadField: r3 = r1->field_43
    //     0xb44780: ldur            w3, [x1, #0x43]
    // 0xb44784: DecompressPointer r3
    //     0xb44784: add             x3, x3, HEAP, lsl #32
    // 0xb44788: cmp             w3, NULL
    // 0xb4478c: b.eq            #0xb44f30
    // 0xb44790: cmp             w3, NULL
    // 0xb44794: b.ne            #0xb447a0
    // 0xb44798: r1 = Null
    //     0xb44798: mov             x1, NULL
    // 0xb4479c: b               #0xb447a8
    // 0xb447a0: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xb447a0: ldur            w1, [x3, #0x17]
    // 0xb447a4: DecompressPointer r1
    //     0xb447a4: add             x1, x1, HEAP, lsl #32
    // 0xb447a8: cmp             w1, NULL
    // 0xb447ac: b.ne            #0xb447b4
    // 0xb447b0: r1 = false
    //     0xb447b0: add             x1, NULL, #0x30  ; false
    // 0xb447b4: eor             x3, x1, #0x10
    // 0xb447b8: stur            x3, [fp, #-0x20]
    // 0xb447bc: r1 = Instance_Color
    //     0xb447bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb447c0: d0 = 0.070000
    //     0xb447c0: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb447c4: ldr             d0, [x17, #0x5f8]
    // 0xb447c8: r0 = withOpacity()
    //     0xb447c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb447cc: r16 = 1.000000
    //     0xb447cc: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xb447d0: str             x16, [SP]
    // 0xb447d4: mov             x2, x0
    // 0xb447d8: r1 = Null
    //     0xb447d8: mov             x1, NULL
    // 0xb447dc: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xb447dc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xb447e0: ldr             x4, [x4, #0x108]
    // 0xb447e4: r0 = Border.all()
    //     0xb447e4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb447e8: stur            x0, [fp, #-0x38]
    // 0xb447ec: r0 = BoxDecoration()
    //     0xb447ec: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb447f0: mov             x2, x0
    // 0xb447f4: ldur            x0, [fp, #-0x38]
    // 0xb447f8: stur            x2, [fp, #-0x40]
    // 0xb447fc: StoreField: r2->field_f = r0
    //     0xb447fc: stur            w0, [x2, #0xf]
    // 0xb44800: r0 = Instance_BoxShape
    //     0xb44800: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb44804: ldr             x0, [x0, #0x80]
    // 0xb44808: StoreField: r2->field_23 = r0
    //     0xb44808: stur            w0, [x2, #0x23]
    // 0xb4480c: ldur            x1, [fp, #-0x10]
    // 0xb44810: r0 = of()
    //     0xb44810: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb44814: LoadField: r1 = r0->field_5b
    //     0xb44814: ldur            w1, [x0, #0x5b]
    // 0xb44818: DecompressPointer r1
    //     0xb44818: add             x1, x1, HEAP, lsl #32
    // 0xb4481c: r0 = LoadClassIdInstr(r1)
    //     0xb4481c: ldur            x0, [x1, #-1]
    //     0xb44820: ubfx            x0, x0, #0xc, #0x14
    // 0xb44824: d0 = 0.400000
    //     0xb44824: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb44828: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb44828: sub             lr, x0, #0xffa
    //     0xb4482c: ldr             lr, [x21, lr, lsl #3]
    //     0xb44830: blr             lr
    // 0xb44834: ldur            x1, [fp, #-0x10]
    // 0xb44838: stur            x0, [fp, #-0x38]
    // 0xb4483c: r0 = of()
    //     0xb4483c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb44840: LoadField: r1 = r0->field_87
    //     0xb44840: ldur            w1, [x0, #0x87]
    // 0xb44844: DecompressPointer r1
    //     0xb44844: add             x1, x1, HEAP, lsl #32
    // 0xb44848: LoadField: r0 = r1->field_7
    //     0xb44848: ldur            w0, [x1, #7]
    // 0xb4484c: DecompressPointer r0
    //     0xb4484c: add             x0, x0, HEAP, lsl #32
    // 0xb44850: r16 = 12.000000
    //     0xb44850: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb44854: ldr             x16, [x16, #0x9e8]
    // 0xb44858: r30 = Instance_Color
    //     0xb44858: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb4485c: stp             lr, x16, [SP]
    // 0xb44860: mov             x1, x0
    // 0xb44864: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb44864: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb44868: ldr             x4, [x4, #0xaa0]
    // 0xb4486c: r0 = copyWith()
    //     0xb4486c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb44870: stur            x0, [fp, #-0x48]
    // 0xb44874: r0 = Text()
    //     0xb44874: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb44878: mov             x1, x0
    // 0xb4487c: r0 = "Free"
    //     0xb4487c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb44880: ldr             x0, [x0, #0x668]
    // 0xb44884: stur            x1, [fp, #-0x50]
    // 0xb44888: StoreField: r1->field_b = r0
    //     0xb44888: stur            w0, [x1, #0xb]
    // 0xb4488c: ldur            x2, [fp, #-0x48]
    // 0xb44890: StoreField: r1->field_13 = r2
    //     0xb44890: stur            w2, [x1, #0x13]
    // 0xb44894: r0 = Center()
    //     0xb44894: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb44898: mov             x1, x0
    // 0xb4489c: r0 = Instance_Alignment
    //     0xb4489c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb448a0: ldr             x0, [x0, #0xb10]
    // 0xb448a4: stur            x1, [fp, #-0x48]
    // 0xb448a8: StoreField: r1->field_f = r0
    //     0xb448a8: stur            w0, [x1, #0xf]
    // 0xb448ac: ldur            x0, [fp, #-0x50]
    // 0xb448b0: StoreField: r1->field_b = r0
    //     0xb448b0: stur            w0, [x1, #0xb]
    // 0xb448b4: r0 = RotatedBox()
    //     0xb448b4: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xb448b8: mov             x1, x0
    // 0xb448bc: r0 = -1
    //     0xb448bc: movn            x0, #0
    // 0xb448c0: stur            x1, [fp, #-0x50]
    // 0xb448c4: StoreField: r1->field_f = r0
    //     0xb448c4: stur            x0, [x1, #0xf]
    // 0xb448c8: ldur            x0, [fp, #-0x48]
    // 0xb448cc: StoreField: r1->field_b = r0
    //     0xb448cc: stur            w0, [x1, #0xb]
    // 0xb448d0: r0 = Container()
    //     0xb448d0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb448d4: stur            x0, [fp, #-0x48]
    // 0xb448d8: r16 = 24.000000
    //     0xb448d8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xb448dc: ldr             x16, [x16, #0xba8]
    // 0xb448e0: r30 = 56.000000
    //     0xb448e0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb448e4: ldr             lr, [lr, #0xb78]
    // 0xb448e8: stp             lr, x16, [SP, #0x10]
    // 0xb448ec: ldur            x16, [fp, #-0x38]
    // 0xb448f0: ldur            lr, [fp, #-0x50]
    // 0xb448f4: stp             lr, x16, [SP]
    // 0xb448f8: mov             x1, x0
    // 0xb448fc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xb448fc: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xb44900: ldr             x4, [x4, #0x670]
    // 0xb44904: r0 = Container()
    //     0xb44904: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb44908: ldur            x0, [fp, #-8]
    // 0xb4490c: LoadField: r1 = r0->field_b
    //     0xb4490c: ldur            w1, [x0, #0xb]
    // 0xb44910: DecompressPointer r1
    //     0xb44910: add             x1, x1, HEAP, lsl #32
    // 0xb44914: cmp             w1, NULL
    // 0xb44918: b.eq            #0xb45194
    // 0xb4491c: LoadField: r2 = r1->field_b
    //     0xb4491c: ldur            w2, [x1, #0xb]
    // 0xb44920: DecompressPointer r2
    //     0xb44920: add             x2, x2, HEAP, lsl #32
    // 0xb44924: LoadField: r1 = r2->field_b
    //     0xb44924: ldur            w1, [x2, #0xb]
    // 0xb44928: DecompressPointer r1
    //     0xb44928: add             x1, x1, HEAP, lsl #32
    // 0xb4492c: cmp             w1, NULL
    // 0xb44930: b.ne            #0xb4493c
    // 0xb44934: r1 = Null
    //     0xb44934: mov             x1, NULL
    // 0xb44938: b               #0xb4495c
    // 0xb4493c: LoadField: r2 = r1->field_43
    //     0xb4493c: ldur            w2, [x1, #0x43]
    // 0xb44940: DecompressPointer r2
    //     0xb44940: add             x2, x2, HEAP, lsl #32
    // 0xb44944: cmp             w2, NULL
    // 0xb44948: b.ne            #0xb44954
    // 0xb4494c: r1 = Null
    //     0xb4494c: mov             x1, NULL
    // 0xb44950: b               #0xb4495c
    // 0xb44954: LoadField: r1 = r2->field_7
    //     0xb44954: ldur            w1, [x2, #7]
    // 0xb44958: DecompressPointer r1
    //     0xb44958: add             x1, x1, HEAP, lsl #32
    // 0xb4495c: cmp             w1, NULL
    // 0xb44960: b.ne            #0xb4496c
    // 0xb44964: r2 = ""
    //     0xb44964: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb44968: b               #0xb44970
    // 0xb4496c: mov             x2, x1
    // 0xb44970: stur            x2, [fp, #-0x38]
    // 0xb44974: r0 = CachedNetworkImage()
    //     0xb44974: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb44978: stur            x0, [fp, #-0x50]
    // 0xb4497c: r16 = 56.000000
    //     0xb4497c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb44980: ldr             x16, [x16, #0xb78]
    // 0xb44984: r30 = 56.000000
    //     0xb44984: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xb44988: ldr             lr, [lr, #0xb78]
    // 0xb4498c: stp             lr, x16, [SP, #8]
    // 0xb44990: r16 = Instance_BoxFit
    //     0xb44990: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xb44994: ldr             x16, [x16, #0x118]
    // 0xb44998: str             x16, [SP]
    // 0xb4499c: mov             x1, x0
    // 0xb449a0: ldur            x2, [fp, #-0x38]
    // 0xb449a4: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xb449a4: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xb449a8: ldr             x4, [x4, #0xb40]
    // 0xb449ac: r0 = CachedNetworkImage()
    //     0xb449ac: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb449b0: ldur            x0, [fp, #-8]
    // 0xb449b4: LoadField: r1 = r0->field_b
    //     0xb449b4: ldur            w1, [x0, #0xb]
    // 0xb449b8: DecompressPointer r1
    //     0xb449b8: add             x1, x1, HEAP, lsl #32
    // 0xb449bc: cmp             w1, NULL
    // 0xb449c0: b.eq            #0xb45198
    // 0xb449c4: LoadField: r2 = r1->field_b
    //     0xb449c4: ldur            w2, [x1, #0xb]
    // 0xb449c8: DecompressPointer r2
    //     0xb449c8: add             x2, x2, HEAP, lsl #32
    // 0xb449cc: LoadField: r1 = r2->field_b
    //     0xb449cc: ldur            w1, [x2, #0xb]
    // 0xb449d0: DecompressPointer r1
    //     0xb449d0: add             x1, x1, HEAP, lsl #32
    // 0xb449d4: cmp             w1, NULL
    // 0xb449d8: b.ne            #0xb449e4
    // 0xb449dc: r1 = Null
    //     0xb449dc: mov             x1, NULL
    // 0xb449e0: b               #0xb44a04
    // 0xb449e4: LoadField: r2 = r1->field_43
    //     0xb449e4: ldur            w2, [x1, #0x43]
    // 0xb449e8: DecompressPointer r2
    //     0xb449e8: add             x2, x2, HEAP, lsl #32
    // 0xb449ec: cmp             w2, NULL
    // 0xb449f0: b.ne            #0xb449fc
    // 0xb449f4: r1 = Null
    //     0xb449f4: mov             x1, NULL
    // 0xb449f8: b               #0xb44a04
    // 0xb449fc: LoadField: r1 = r2->field_b
    //     0xb449fc: ldur            w1, [x2, #0xb]
    // 0xb44a00: DecompressPointer r1
    //     0xb44a00: add             x1, x1, HEAP, lsl #32
    // 0xb44a04: cmp             w1, NULL
    // 0xb44a08: b.ne            #0xb44a14
    // 0xb44a0c: r2 = ""
    //     0xb44a0c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb44a10: b               #0xb44a18
    // 0xb44a14: mov             x2, x1
    // 0xb44a18: ldur            x1, [fp, #-0x10]
    // 0xb44a1c: stur            x2, [fp, #-0x38]
    // 0xb44a20: r0 = of()
    //     0xb44a20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb44a24: LoadField: r1 = r0->field_87
    //     0xb44a24: ldur            w1, [x0, #0x87]
    // 0xb44a28: DecompressPointer r1
    //     0xb44a28: add             x1, x1, HEAP, lsl #32
    // 0xb44a2c: LoadField: r0 = r1->field_7
    //     0xb44a2c: ldur            w0, [x1, #7]
    // 0xb44a30: DecompressPointer r0
    //     0xb44a30: add             x0, x0, HEAP, lsl #32
    // 0xb44a34: r16 = 12.000000
    //     0xb44a34: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb44a38: ldr             x16, [x16, #0x9e8]
    // 0xb44a3c: r30 = Instance_Color
    //     0xb44a3c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb44a40: stp             lr, x16, [SP]
    // 0xb44a44: mov             x1, x0
    // 0xb44a48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb44a48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb44a4c: ldr             x4, [x4, #0xaa0]
    // 0xb44a50: r0 = copyWith()
    //     0xb44a50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb44a54: stur            x0, [fp, #-0x58]
    // 0xb44a58: r0 = Text()
    //     0xb44a58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb44a5c: mov             x1, x0
    // 0xb44a60: ldur            x0, [fp, #-0x38]
    // 0xb44a64: stur            x1, [fp, #-0x60]
    // 0xb44a68: StoreField: r1->field_b = r0
    //     0xb44a68: stur            w0, [x1, #0xb]
    // 0xb44a6c: ldur            x0, [fp, #-0x58]
    // 0xb44a70: StoreField: r1->field_13 = r0
    //     0xb44a70: stur            w0, [x1, #0x13]
    // 0xb44a74: r0 = Instance_TextOverflow
    //     0xb44a74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb44a78: ldr             x0, [x0, #0xe10]
    // 0xb44a7c: StoreField: r1->field_2b = r0
    //     0xb44a7c: stur            w0, [x1, #0x2b]
    // 0xb44a80: r0 = 2
    //     0xb44a80: movz            x0, #0x2
    // 0xb44a84: StoreField: r1->field_37 = r0
    //     0xb44a84: stur            w0, [x1, #0x37]
    // 0xb44a88: r0 = SizedBox()
    //     0xb44a88: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb44a8c: mov             x2, x0
    // 0xb44a90: r0 = 150.000000
    //     0xb44a90: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xb44a94: ldr             x0, [x0, #0x690]
    // 0xb44a98: stur            x2, [fp, #-0x38]
    // 0xb44a9c: StoreField: r2->field_f = r0
    //     0xb44a9c: stur            w0, [x2, #0xf]
    // 0xb44aa0: ldur            x0, [fp, #-0x60]
    // 0xb44aa4: StoreField: r2->field_b = r0
    //     0xb44aa4: stur            w0, [x2, #0xb]
    // 0xb44aa8: ldur            x1, [fp, #-0x10]
    // 0xb44aac: r0 = of()
    //     0xb44aac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb44ab0: LoadField: r1 = r0->field_87
    //     0xb44ab0: ldur            w1, [x0, #0x87]
    // 0xb44ab4: DecompressPointer r1
    //     0xb44ab4: add             x1, x1, HEAP, lsl #32
    // 0xb44ab8: LoadField: r0 = r1->field_2b
    //     0xb44ab8: ldur            w0, [x1, #0x2b]
    // 0xb44abc: DecompressPointer r0
    //     0xb44abc: add             x0, x0, HEAP, lsl #32
    // 0xb44ac0: r16 = 12.000000
    //     0xb44ac0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb44ac4: ldr             x16, [x16, #0x9e8]
    // 0xb44ac8: r30 = Instance_Color
    //     0xb44ac8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb44acc: stp             lr, x16, [SP]
    // 0xb44ad0: mov             x1, x0
    // 0xb44ad4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb44ad4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb44ad8: ldr             x4, [x4, #0xaa0]
    // 0xb44adc: r0 = copyWith()
    //     0xb44adc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb44ae0: stur            x0, [fp, #-0x58]
    // 0xb44ae4: r0 = Text()
    //     0xb44ae4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb44ae8: mov             x2, x0
    // 0xb44aec: r0 = "Free"
    //     0xb44aec: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xb44af0: ldr             x0, [x0, #0x668]
    // 0xb44af4: stur            x2, [fp, #-0x60]
    // 0xb44af8: StoreField: r2->field_b = r0
    //     0xb44af8: stur            w0, [x2, #0xb]
    // 0xb44afc: ldur            x0, [fp, #-0x58]
    // 0xb44b00: StoreField: r2->field_13 = r0
    //     0xb44b00: stur            w0, [x2, #0x13]
    // 0xb44b04: ldur            x0, [fp, #-8]
    // 0xb44b08: LoadField: r1 = r0->field_b
    //     0xb44b08: ldur            w1, [x0, #0xb]
    // 0xb44b0c: DecompressPointer r1
    //     0xb44b0c: add             x1, x1, HEAP, lsl #32
    // 0xb44b10: cmp             w1, NULL
    // 0xb44b14: b.eq            #0xb4519c
    // 0xb44b18: LoadField: r3 = r1->field_b
    //     0xb44b18: ldur            w3, [x1, #0xb]
    // 0xb44b1c: DecompressPointer r3
    //     0xb44b1c: add             x3, x3, HEAP, lsl #32
    // 0xb44b20: LoadField: r1 = r3->field_b
    //     0xb44b20: ldur            w1, [x3, #0xb]
    // 0xb44b24: DecompressPointer r1
    //     0xb44b24: add             x1, x1, HEAP, lsl #32
    // 0xb44b28: cmp             w1, NULL
    // 0xb44b2c: b.ne            #0xb44b38
    // 0xb44b30: r1 = Null
    //     0xb44b30: mov             x1, NULL
    // 0xb44b34: b               #0xb44b58
    // 0xb44b38: LoadField: r3 = r1->field_43
    //     0xb44b38: ldur            w3, [x1, #0x43]
    // 0xb44b3c: DecompressPointer r3
    //     0xb44b3c: add             x3, x3, HEAP, lsl #32
    // 0xb44b40: cmp             w3, NULL
    // 0xb44b44: b.ne            #0xb44b50
    // 0xb44b48: r1 = Null
    //     0xb44b48: mov             x1, NULL
    // 0xb44b4c: b               #0xb44b58
    // 0xb44b50: LoadField: r1 = r3->field_13
    //     0xb44b50: ldur            w1, [x3, #0x13]
    // 0xb44b54: DecompressPointer r1
    //     0xb44b54: add             x1, x1, HEAP, lsl #32
    // 0xb44b58: cmp             w1, NULL
    // 0xb44b5c: b.ne            #0xb44b68
    // 0xb44b60: r8 = ""
    //     0xb44b60: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb44b64: b               #0xb44b6c
    // 0xb44b68: mov             x8, x1
    // 0xb44b6c: ldur            x6, [fp, #-0x28]
    // 0xb44b70: ldur            x7, [fp, #-0x20]
    // 0xb44b74: ldur            x5, [fp, #-0x48]
    // 0xb44b78: ldur            x4, [fp, #-0x50]
    // 0xb44b7c: ldur            x3, [fp, #-0x38]
    // 0xb44b80: ldur            x1, [fp, #-0x10]
    // 0xb44b84: stur            x8, [fp, #-0x58]
    // 0xb44b88: r0 = of()
    //     0xb44b88: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb44b8c: LoadField: r1 = r0->field_87
    //     0xb44b8c: ldur            w1, [x0, #0x87]
    // 0xb44b90: DecompressPointer r1
    //     0xb44b90: add             x1, x1, HEAP, lsl #32
    // 0xb44b94: LoadField: r0 = r1->field_2b
    //     0xb44b94: ldur            w0, [x1, #0x2b]
    // 0xb44b98: DecompressPointer r0
    //     0xb44b98: add             x0, x0, HEAP, lsl #32
    // 0xb44b9c: r16 = 12.000000
    //     0xb44b9c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb44ba0: ldr             x16, [x16, #0x9e8]
    // 0xb44ba4: r30 = Instance_TextDecoration
    //     0xb44ba4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb44ba8: ldr             lr, [lr, #0xe30]
    // 0xb44bac: stp             lr, x16, [SP]
    // 0xb44bb0: mov             x1, x0
    // 0xb44bb4: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xb44bb4: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xb44bb8: ldr             x4, [x4, #0x698]
    // 0xb44bbc: r0 = copyWith()
    //     0xb44bbc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb44bc0: stur            x0, [fp, #-0x10]
    // 0xb44bc4: r0 = Text()
    //     0xb44bc4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb44bc8: mov             x3, x0
    // 0xb44bcc: ldur            x0, [fp, #-0x58]
    // 0xb44bd0: stur            x3, [fp, #-0x68]
    // 0xb44bd4: StoreField: r3->field_b = r0
    //     0xb44bd4: stur            w0, [x3, #0xb]
    // 0xb44bd8: ldur            x0, [fp, #-0x10]
    // 0xb44bdc: StoreField: r3->field_13 = r0
    //     0xb44bdc: stur            w0, [x3, #0x13]
    // 0xb44be0: r1 = Null
    //     0xb44be0: mov             x1, NULL
    // 0xb44be4: r2 = 6
    //     0xb44be4: movz            x2, #0x6
    // 0xb44be8: r0 = AllocateArray()
    //     0xb44be8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb44bec: mov             x2, x0
    // 0xb44bf0: ldur            x0, [fp, #-0x60]
    // 0xb44bf4: stur            x2, [fp, #-0x10]
    // 0xb44bf8: StoreField: r2->field_f = r0
    //     0xb44bf8: stur            w0, [x2, #0xf]
    // 0xb44bfc: r16 = Instance_SizedBox
    //     0xb44bfc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xb44c00: ldr             x16, [x16, #0xa50]
    // 0xb44c04: StoreField: r2->field_13 = r16
    //     0xb44c04: stur            w16, [x2, #0x13]
    // 0xb44c08: ldur            x0, [fp, #-0x68]
    // 0xb44c0c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb44c0c: stur            w0, [x2, #0x17]
    // 0xb44c10: r1 = <Widget>
    //     0xb44c10: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb44c14: r0 = AllocateGrowableArray()
    //     0xb44c14: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb44c18: mov             x1, x0
    // 0xb44c1c: ldur            x0, [fp, #-0x10]
    // 0xb44c20: stur            x1, [fp, #-0x58]
    // 0xb44c24: StoreField: r1->field_f = r0
    //     0xb44c24: stur            w0, [x1, #0xf]
    // 0xb44c28: r2 = 6
    //     0xb44c28: movz            x2, #0x6
    // 0xb44c2c: StoreField: r1->field_b = r2
    //     0xb44c2c: stur            w2, [x1, #0xb]
    // 0xb44c30: r0 = Row()
    //     0xb44c30: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb44c34: mov             x3, x0
    // 0xb44c38: r0 = Instance_Axis
    //     0xb44c38: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb44c3c: stur            x3, [fp, #-0x10]
    // 0xb44c40: StoreField: r3->field_f = r0
    //     0xb44c40: stur            w0, [x3, #0xf]
    // 0xb44c44: r4 = Instance_MainAxisAlignment
    //     0xb44c44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb44c48: ldr             x4, [x4, #0xa08]
    // 0xb44c4c: StoreField: r3->field_13 = r4
    //     0xb44c4c: stur            w4, [x3, #0x13]
    // 0xb44c50: r5 = Instance_MainAxisSize
    //     0xb44c50: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb44c54: ldr             x5, [x5, #0xa10]
    // 0xb44c58: ArrayStore: r3[0] = r5  ; List_4
    //     0xb44c58: stur            w5, [x3, #0x17]
    // 0xb44c5c: r6 = Instance_CrossAxisAlignment
    //     0xb44c5c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb44c60: ldr             x6, [x6, #0xa18]
    // 0xb44c64: StoreField: r3->field_1b = r6
    //     0xb44c64: stur            w6, [x3, #0x1b]
    // 0xb44c68: r7 = Instance_VerticalDirection
    //     0xb44c68: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb44c6c: ldr             x7, [x7, #0xa20]
    // 0xb44c70: StoreField: r3->field_23 = r7
    //     0xb44c70: stur            w7, [x3, #0x23]
    // 0xb44c74: r8 = Instance_Clip
    //     0xb44c74: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb44c78: ldr             x8, [x8, #0x38]
    // 0xb44c7c: StoreField: r3->field_2b = r8
    //     0xb44c7c: stur            w8, [x3, #0x2b]
    // 0xb44c80: StoreField: r3->field_2f = rZR
    //     0xb44c80: stur            xzr, [x3, #0x2f]
    // 0xb44c84: ldur            x1, [fp, #-0x58]
    // 0xb44c88: StoreField: r3->field_b = r1
    //     0xb44c88: stur            w1, [x3, #0xb]
    // 0xb44c8c: r1 = Null
    //     0xb44c8c: mov             x1, NULL
    // 0xb44c90: r2 = 6
    //     0xb44c90: movz            x2, #0x6
    // 0xb44c94: r0 = AllocateArray()
    //     0xb44c94: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb44c98: mov             x2, x0
    // 0xb44c9c: ldur            x0, [fp, #-0x38]
    // 0xb44ca0: stur            x2, [fp, #-0x58]
    // 0xb44ca4: StoreField: r2->field_f = r0
    //     0xb44ca4: stur            w0, [x2, #0xf]
    // 0xb44ca8: r16 = Instance_SizedBox
    //     0xb44ca8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb44cac: ldr             x16, [x16, #0xc70]
    // 0xb44cb0: StoreField: r2->field_13 = r16
    //     0xb44cb0: stur            w16, [x2, #0x13]
    // 0xb44cb4: ldur            x0, [fp, #-0x10]
    // 0xb44cb8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb44cb8: stur            w0, [x2, #0x17]
    // 0xb44cbc: r1 = <Widget>
    //     0xb44cbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb44cc0: r0 = AllocateGrowableArray()
    //     0xb44cc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb44cc4: mov             x1, x0
    // 0xb44cc8: ldur            x0, [fp, #-0x58]
    // 0xb44ccc: stur            x1, [fp, #-0x10]
    // 0xb44cd0: StoreField: r1->field_f = r0
    //     0xb44cd0: stur            w0, [x1, #0xf]
    // 0xb44cd4: r2 = 6
    //     0xb44cd4: movz            x2, #0x6
    // 0xb44cd8: StoreField: r1->field_b = r2
    //     0xb44cd8: stur            w2, [x1, #0xb]
    // 0xb44cdc: r0 = Column()
    //     0xb44cdc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb44ce0: mov             x1, x0
    // 0xb44ce4: r0 = Instance_Axis
    //     0xb44ce4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb44ce8: stur            x1, [fp, #-0x38]
    // 0xb44cec: StoreField: r1->field_f = r0
    //     0xb44cec: stur            w0, [x1, #0xf]
    // 0xb44cf0: r2 = Instance_MainAxisAlignment
    //     0xb44cf0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb44cf4: ldr             x2, [x2, #0xa08]
    // 0xb44cf8: StoreField: r1->field_13 = r2
    //     0xb44cf8: stur            w2, [x1, #0x13]
    // 0xb44cfc: r3 = Instance_MainAxisSize
    //     0xb44cfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb44d00: ldr             x3, [x3, #0xa10]
    // 0xb44d04: ArrayStore: r1[0] = r3  ; List_4
    //     0xb44d04: stur            w3, [x1, #0x17]
    // 0xb44d08: r4 = Instance_CrossAxisAlignment
    //     0xb44d08: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb44d0c: ldr             x4, [x4, #0x890]
    // 0xb44d10: StoreField: r1->field_1b = r4
    //     0xb44d10: stur            w4, [x1, #0x1b]
    // 0xb44d14: r4 = Instance_VerticalDirection
    //     0xb44d14: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb44d18: ldr             x4, [x4, #0xa20]
    // 0xb44d1c: StoreField: r1->field_23 = r4
    //     0xb44d1c: stur            w4, [x1, #0x23]
    // 0xb44d20: r5 = Instance_Clip
    //     0xb44d20: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb44d24: ldr             x5, [x5, #0x38]
    // 0xb44d28: StoreField: r1->field_2b = r5
    //     0xb44d28: stur            w5, [x1, #0x2b]
    // 0xb44d2c: StoreField: r1->field_2f = rZR
    //     0xb44d2c: stur            xzr, [x1, #0x2f]
    // 0xb44d30: ldur            x6, [fp, #-0x10]
    // 0xb44d34: StoreField: r1->field_b = r6
    //     0xb44d34: stur            w6, [x1, #0xb]
    // 0xb44d38: r0 = Padding()
    //     0xb44d38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb44d3c: mov             x2, x0
    // 0xb44d40: r0 = Instance_EdgeInsets
    //     0xb44d40: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xb44d44: ldr             x0, [x0, #0xa78]
    // 0xb44d48: stur            x2, [fp, #-0x10]
    // 0xb44d4c: StoreField: r2->field_f = r0
    //     0xb44d4c: stur            w0, [x2, #0xf]
    // 0xb44d50: ldur            x0, [fp, #-0x38]
    // 0xb44d54: StoreField: r2->field_b = r0
    //     0xb44d54: stur            w0, [x2, #0xb]
    // 0xb44d58: r1 = <FlexParentData>
    //     0xb44d58: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb44d5c: ldr             x1, [x1, #0xe00]
    // 0xb44d60: r0 = Expanded()
    //     0xb44d60: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb44d64: mov             x3, x0
    // 0xb44d68: r0 = 1
    //     0xb44d68: movz            x0, #0x1
    // 0xb44d6c: stur            x3, [fp, #-0x38]
    // 0xb44d70: StoreField: r3->field_13 = r0
    //     0xb44d70: stur            x0, [x3, #0x13]
    // 0xb44d74: r0 = Instance_FlexFit
    //     0xb44d74: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb44d78: ldr             x0, [x0, #0xe08]
    // 0xb44d7c: StoreField: r3->field_1b = r0
    //     0xb44d7c: stur            w0, [x3, #0x1b]
    // 0xb44d80: ldur            x0, [fp, #-0x10]
    // 0xb44d84: StoreField: r3->field_b = r0
    //     0xb44d84: stur            w0, [x3, #0xb]
    // 0xb44d88: r1 = Null
    //     0xb44d88: mov             x1, NULL
    // 0xb44d8c: r2 = 6
    //     0xb44d8c: movz            x2, #0x6
    // 0xb44d90: r0 = AllocateArray()
    //     0xb44d90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb44d94: mov             x2, x0
    // 0xb44d98: ldur            x0, [fp, #-0x48]
    // 0xb44d9c: stur            x2, [fp, #-0x10]
    // 0xb44da0: StoreField: r2->field_f = r0
    //     0xb44da0: stur            w0, [x2, #0xf]
    // 0xb44da4: ldur            x0, [fp, #-0x50]
    // 0xb44da8: StoreField: r2->field_13 = r0
    //     0xb44da8: stur            w0, [x2, #0x13]
    // 0xb44dac: ldur            x0, [fp, #-0x38]
    // 0xb44db0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb44db0: stur            w0, [x2, #0x17]
    // 0xb44db4: r1 = <Widget>
    //     0xb44db4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb44db8: r0 = AllocateGrowableArray()
    //     0xb44db8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb44dbc: mov             x1, x0
    // 0xb44dc0: ldur            x0, [fp, #-0x10]
    // 0xb44dc4: stur            x1, [fp, #-0x38]
    // 0xb44dc8: StoreField: r1->field_f = r0
    //     0xb44dc8: stur            w0, [x1, #0xf]
    // 0xb44dcc: r0 = 6
    //     0xb44dcc: movz            x0, #0x6
    // 0xb44dd0: StoreField: r1->field_b = r0
    //     0xb44dd0: stur            w0, [x1, #0xb]
    // 0xb44dd4: r0 = Row()
    //     0xb44dd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb44dd8: mov             x1, x0
    // 0xb44ddc: r0 = Instance_Axis
    //     0xb44ddc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb44de0: stur            x1, [fp, #-0x10]
    // 0xb44de4: StoreField: r1->field_f = r0
    //     0xb44de4: stur            w0, [x1, #0xf]
    // 0xb44de8: r0 = Instance_MainAxisAlignment
    //     0xb44de8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb44dec: ldr             x0, [x0, #0xa08]
    // 0xb44df0: StoreField: r1->field_13 = r0
    //     0xb44df0: stur            w0, [x1, #0x13]
    // 0xb44df4: r2 = Instance_MainAxisSize
    //     0xb44df4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb44df8: ldr             x2, [x2, #0xa10]
    // 0xb44dfc: ArrayStore: r1[0] = r2  ; List_4
    //     0xb44dfc: stur            w2, [x1, #0x17]
    // 0xb44e00: r3 = Instance_CrossAxisAlignment
    //     0xb44e00: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb44e04: ldr             x3, [x3, #0xa18]
    // 0xb44e08: StoreField: r1->field_1b = r3
    //     0xb44e08: stur            w3, [x1, #0x1b]
    // 0xb44e0c: r4 = Instance_VerticalDirection
    //     0xb44e0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb44e10: ldr             x4, [x4, #0xa20]
    // 0xb44e14: StoreField: r1->field_23 = r4
    //     0xb44e14: stur            w4, [x1, #0x23]
    // 0xb44e18: r5 = Instance_Clip
    //     0xb44e18: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb44e1c: ldr             x5, [x5, #0x38]
    // 0xb44e20: StoreField: r1->field_2b = r5
    //     0xb44e20: stur            w5, [x1, #0x2b]
    // 0xb44e24: StoreField: r1->field_2f = rZR
    //     0xb44e24: stur            xzr, [x1, #0x2f]
    // 0xb44e28: ldur            x6, [fp, #-0x38]
    // 0xb44e2c: StoreField: r1->field_b = r6
    //     0xb44e2c: stur            w6, [x1, #0xb]
    // 0xb44e30: r0 = Container()
    //     0xb44e30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb44e34: stur            x0, [fp, #-0x38]
    // 0xb44e38: ldur            x16, [fp, #-0x40]
    // 0xb44e3c: ldur            lr, [fp, #-0x10]
    // 0xb44e40: stp             lr, x16, [SP]
    // 0xb44e44: mov             x1, x0
    // 0xb44e48: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb44e48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb44e4c: ldr             x4, [x4, #0x88]
    // 0xb44e50: r0 = Container()
    //     0xb44e50: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb44e54: r0 = Padding()
    //     0xb44e54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb44e58: mov             x1, x0
    // 0xb44e5c: r0 = Instance_EdgeInsets
    //     0xb44e5c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xb44e60: ldr             x0, [x0, #0x778]
    // 0xb44e64: stur            x1, [fp, #-0x10]
    // 0xb44e68: StoreField: r1->field_f = r0
    //     0xb44e68: stur            w0, [x1, #0xf]
    // 0xb44e6c: ldur            x0, [fp, #-0x38]
    // 0xb44e70: StoreField: r1->field_b = r0
    //     0xb44e70: stur            w0, [x1, #0xb]
    // 0xb44e74: r0 = Visibility()
    //     0xb44e74: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb44e78: mov             x2, x0
    // 0xb44e7c: ldur            x0, [fp, #-0x10]
    // 0xb44e80: stur            x2, [fp, #-0x38]
    // 0xb44e84: StoreField: r2->field_b = r0
    //     0xb44e84: stur            w0, [x2, #0xb]
    // 0xb44e88: r0 = Instance_SizedBox
    //     0xb44e88: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb44e8c: StoreField: r2->field_f = r0
    //     0xb44e8c: stur            w0, [x2, #0xf]
    // 0xb44e90: ldur            x0, [fp, #-0x20]
    // 0xb44e94: StoreField: r2->field_13 = r0
    //     0xb44e94: stur            w0, [x2, #0x13]
    // 0xb44e98: r0 = false
    //     0xb44e98: add             x0, NULL, #0x30  ; false
    // 0xb44e9c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb44e9c: stur            w0, [x2, #0x17]
    // 0xb44ea0: StoreField: r2->field_1b = r0
    //     0xb44ea0: stur            w0, [x2, #0x1b]
    // 0xb44ea4: StoreField: r2->field_1f = r0
    //     0xb44ea4: stur            w0, [x2, #0x1f]
    // 0xb44ea8: StoreField: r2->field_23 = r0
    //     0xb44ea8: stur            w0, [x2, #0x23]
    // 0xb44eac: StoreField: r2->field_27 = r0
    //     0xb44eac: stur            w0, [x2, #0x27]
    // 0xb44eb0: StoreField: r2->field_2b = r0
    //     0xb44eb0: stur            w0, [x2, #0x2b]
    // 0xb44eb4: ldur            x3, [fp, #-0x28]
    // 0xb44eb8: LoadField: r1 = r3->field_b
    //     0xb44eb8: ldur            w1, [x3, #0xb]
    // 0xb44ebc: LoadField: r4 = r3->field_f
    //     0xb44ebc: ldur            w4, [x3, #0xf]
    // 0xb44ec0: DecompressPointer r4
    //     0xb44ec0: add             x4, x4, HEAP, lsl #32
    // 0xb44ec4: LoadField: r5 = r4->field_b
    //     0xb44ec4: ldur            w5, [x4, #0xb]
    // 0xb44ec8: r4 = LoadInt32Instr(r1)
    //     0xb44ec8: sbfx            x4, x1, #1, #0x1f
    // 0xb44ecc: stur            x4, [fp, #-0x78]
    // 0xb44ed0: r1 = LoadInt32Instr(r5)
    //     0xb44ed0: sbfx            x1, x5, #1, #0x1f
    // 0xb44ed4: cmp             x4, x1
    // 0xb44ed8: b.ne            #0xb44ee4
    // 0xb44edc: mov             x1, x3
    // 0xb44ee0: r0 = _growToNextCapacity()
    //     0xb44ee0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb44ee4: ldur            x3, [fp, #-0x28]
    // 0xb44ee8: ldur            x2, [fp, #-0x78]
    // 0xb44eec: add             x0, x2, #1
    // 0xb44ef0: lsl             x1, x0, #1
    // 0xb44ef4: StoreField: r3->field_b = r1
    //     0xb44ef4: stur            w1, [x3, #0xb]
    // 0xb44ef8: LoadField: r1 = r3->field_f
    //     0xb44ef8: ldur            w1, [x3, #0xf]
    // 0xb44efc: DecompressPointer r1
    //     0xb44efc: add             x1, x1, HEAP, lsl #32
    // 0xb44f00: ldur            x0, [fp, #-0x38]
    // 0xb44f04: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb44f04: add             x25, x1, x2, lsl #2
    //     0xb44f08: add             x25, x25, #0xf
    //     0xb44f0c: str             w0, [x25]
    //     0xb44f10: tbz             w0, #0, #0xb44f2c
    //     0xb44f14: ldurb           w16, [x1, #-1]
    //     0xb44f18: ldurb           w17, [x0, #-1]
    //     0xb44f1c: and             x16, x17, x16, lsr #2
    //     0xb44f20: tst             x16, HEAP, lsr #32
    //     0xb44f24: b.eq            #0xb44f2c
    //     0xb44f28: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb44f2c: b               #0xb44f34
    // 0xb44f30: mov             x3, x2
    // 0xb44f34: ldur            x0, [fp, #-8]
    // 0xb44f38: LoadField: r1 = r0->field_b
    //     0xb44f38: ldur            w1, [x0, #0xb]
    // 0xb44f3c: DecompressPointer r1
    //     0xb44f3c: add             x1, x1, HEAP, lsl #32
    // 0xb44f40: cmp             w1, NULL
    // 0xb44f44: b.eq            #0xb451a0
    // 0xb44f48: LoadField: r0 = r1->field_b
    //     0xb44f48: ldur            w0, [x1, #0xb]
    // 0xb44f4c: DecompressPointer r0
    //     0xb44f4c: add             x0, x0, HEAP, lsl #32
    // 0xb44f50: LoadField: r1 = r0->field_b
    //     0xb44f50: ldur            w1, [x0, #0xb]
    // 0xb44f54: DecompressPointer r1
    //     0xb44f54: add             x1, x1, HEAP, lsl #32
    // 0xb44f58: cmp             w1, NULL
    // 0xb44f5c: b.ne            #0xb44f68
    // 0xb44f60: r0 = Null
    //     0xb44f60: mov             x0, NULL
    // 0xb44f64: b               #0xb44f8c
    // 0xb44f68: LoadField: r0 = r1->field_f
    //     0xb44f68: ldur            w0, [x1, #0xf]
    // 0xb44f6c: DecompressPointer r0
    //     0xb44f6c: add             x0, x0, HEAP, lsl #32
    // 0xb44f70: cmp             w0, NULL
    // 0xb44f74: b.ne            #0xb44f80
    // 0xb44f78: r0 = Null
    //     0xb44f78: mov             x0, NULL
    // 0xb44f7c: b               #0xb44f8c
    // 0xb44f80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb44f80: ldur            w1, [x0, #0x17]
    // 0xb44f84: DecompressPointer r1
    //     0xb44f84: add             x1, x1, HEAP, lsl #32
    // 0xb44f88: mov             x0, x1
    // 0xb44f8c: cmp             w0, NULL
    // 0xb44f90: b.ne            #0xb44fa4
    // 0xb44f94: r1 = <BEntities>
    //     0xb44f94: add             x1, PP, #0x23, lsl #12  ; [pp+0x23130] TypeArguments: <BEntities>
    //     0xb44f98: ldr             x1, [x1, #0x130]
    // 0xb44f9c: r2 = 0
    //     0xb44f9c: movz            x2, #0
    // 0xb44fa0: r0 = AllocateArray()
    //     0xb44fa0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb44fa4: ldur            x1, [fp, #-0x28]
    // 0xb44fa8: r2 = LoadClassIdInstr(r0)
    //     0xb44fa8: ldur            x2, [x0, #-1]
    //     0xb44fac: ubfx            x2, x2, #0xc, #0x14
    // 0xb44fb0: str             x0, [SP]
    // 0xb44fb4: mov             x0, x2
    // 0xb44fb8: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb44fb8: movz            x17, #0xc898
    //     0xb44fbc: add             lr, x0, x17
    //     0xb44fc0: ldr             lr, [x21, lr, lsl #3]
    //     0xb44fc4: blr             lr
    // 0xb44fc8: ldur            x2, [fp, #-0x18]
    // 0xb44fcc: r1 = Function '<anonymous closure>':.
    //     0xb44fcc: add             x1, PP, #0x56, lsl #12  ; [pp+0x567c0] AnonymousClosure: (0xb45244), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_bag_accordion.dart] _CheckoutBagAccordionState::build (0xb43e44)
    //     0xb44fd0: ldr             x1, [x1, #0x7c0]
    // 0xb44fd4: stur            x0, [fp, #-8]
    // 0xb44fd8: r0 = AllocateClosure()
    //     0xb44fd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb44fdc: stur            x0, [fp, #-0x10]
    // 0xb44fe0: r0 = ListView()
    //     0xb44fe0: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb44fe4: stur            x0, [fp, #-0x20]
    // 0xb44fe8: r16 = true
    //     0xb44fe8: add             x16, NULL, #0x20  ; true
    // 0xb44fec: r30 = false
    //     0xb44fec: add             lr, NULL, #0x30  ; false
    // 0xb44ff0: stp             lr, x16, [SP, #8]
    // 0xb44ff4: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb44ff4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb44ff8: ldr             x16, [x16, #0x1c8]
    // 0xb44ffc: str             x16, [SP]
    // 0xb45000: mov             x1, x0
    // 0xb45004: ldur            x2, [fp, #-0x10]
    // 0xb45008: ldur            x3, [fp, #-8]
    // 0xb4500c: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, primary, 0x4, shrinkWrap, 0x3, null]
    //     0xb4500c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fd8] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "primary", 0x4, "shrinkWrap", 0x3, Null]
    //     0xb45010: ldr             x4, [x4, #0xfd8]
    // 0xb45014: r0 = ListView.builder()
    //     0xb45014: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xb45018: ldur            x0, [fp, #-0x28]
    // 0xb4501c: LoadField: r1 = r0->field_b
    //     0xb4501c: ldur            w1, [x0, #0xb]
    // 0xb45020: LoadField: r2 = r0->field_f
    //     0xb45020: ldur            w2, [x0, #0xf]
    // 0xb45024: DecompressPointer r2
    //     0xb45024: add             x2, x2, HEAP, lsl #32
    // 0xb45028: LoadField: r3 = r2->field_b
    //     0xb45028: ldur            w3, [x2, #0xb]
    // 0xb4502c: r2 = LoadInt32Instr(r1)
    //     0xb4502c: sbfx            x2, x1, #1, #0x1f
    // 0xb45030: stur            x2, [fp, #-0x78]
    // 0xb45034: r1 = LoadInt32Instr(r3)
    //     0xb45034: sbfx            x1, x3, #1, #0x1f
    // 0xb45038: cmp             x2, x1
    // 0xb4503c: b.ne            #0xb45048
    // 0xb45040: mov             x1, x0
    // 0xb45044: r0 = _growToNextCapacity()
    //     0xb45044: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb45048: ldur            x4, [fp, #-0x30]
    // 0xb4504c: ldur            x2, [fp, #-0x28]
    // 0xb45050: ldur            x3, [fp, #-0x78]
    // 0xb45054: add             x0, x3, #1
    // 0xb45058: lsl             x1, x0, #1
    // 0xb4505c: StoreField: r2->field_b = r1
    //     0xb4505c: stur            w1, [x2, #0xb]
    // 0xb45060: LoadField: r1 = r2->field_f
    //     0xb45060: ldur            w1, [x2, #0xf]
    // 0xb45064: DecompressPointer r1
    //     0xb45064: add             x1, x1, HEAP, lsl #32
    // 0xb45068: ldur            x0, [fp, #-0x20]
    // 0xb4506c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb4506c: add             x25, x1, x3, lsl #2
    //     0xb45070: add             x25, x25, #0xf
    //     0xb45074: str             w0, [x25]
    //     0xb45078: tbz             w0, #0, #0xb45094
    //     0xb4507c: ldurb           w16, [x1, #-1]
    //     0xb45080: ldurb           w17, [x0, #-1]
    //     0xb45084: and             x16, x17, x16, lsr #2
    //     0xb45088: tst             x16, HEAP, lsr #32
    //     0xb4508c: b.eq            #0xb45094
    //     0xb45090: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb45094: r0 = Column()
    //     0xb45094: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb45098: mov             x1, x0
    // 0xb4509c: r0 = Instance_Axis
    //     0xb4509c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb450a0: stur            x1, [fp, #-8]
    // 0xb450a4: StoreField: r1->field_f = r0
    //     0xb450a4: stur            w0, [x1, #0xf]
    // 0xb450a8: r0 = Instance_MainAxisAlignment
    //     0xb450a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb450ac: ldr             x0, [x0, #0xa08]
    // 0xb450b0: StoreField: r1->field_13 = r0
    //     0xb450b0: stur            w0, [x1, #0x13]
    // 0xb450b4: r0 = Instance_MainAxisSize
    //     0xb450b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb450b8: ldr             x0, [x0, #0xa10]
    // 0xb450bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb450bc: stur            w0, [x1, #0x17]
    // 0xb450c0: r0 = Instance_CrossAxisAlignment
    //     0xb450c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb450c4: ldr             x0, [x0, #0xa18]
    // 0xb450c8: StoreField: r1->field_1b = r0
    //     0xb450c8: stur            w0, [x1, #0x1b]
    // 0xb450cc: r0 = Instance_VerticalDirection
    //     0xb450cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb450d0: ldr             x0, [x0, #0xa20]
    // 0xb450d4: StoreField: r1->field_23 = r0
    //     0xb450d4: stur            w0, [x1, #0x23]
    // 0xb450d8: r0 = Instance_Clip
    //     0xb450d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb450dc: ldr             x0, [x0, #0x38]
    // 0xb450e0: StoreField: r1->field_2b = r0
    //     0xb450e0: stur            w0, [x1, #0x2b]
    // 0xb450e4: StoreField: r1->field_2f = rZR
    //     0xb450e4: stur            xzr, [x1, #0x2f]
    // 0xb450e8: ldur            x0, [fp, #-0x28]
    // 0xb450ec: StoreField: r1->field_b = r0
    //     0xb450ec: stur            w0, [x1, #0xb]
    // 0xb450f0: r0 = Container()
    //     0xb450f0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb450f4: mov             x1, x0
    // 0xb450f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb450f8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb450fc: r0 = Container()
    //     0xb450fc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb45100: r0 = Accordion()
    //     0xb45100: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xb45104: mov             x3, x0
    // 0xb45108: ldur            x0, [fp, #-0x30]
    // 0xb4510c: stur            x3, [fp, #-0x10]
    // 0xb45110: StoreField: r3->field_b = r0
    //     0xb45110: stur            w0, [x3, #0xb]
    // 0xb45114: ldur            x0, [fp, #-8]
    // 0xb45118: StoreField: r3->field_13 = r0
    //     0xb45118: stur            w0, [x3, #0x13]
    // 0xb4511c: r0 = false
    //     0xb4511c: add             x0, NULL, #0x30  ; false
    // 0xb45120: ArrayStore: r3[0] = r0  ; List_4
    //     0xb45120: stur            w0, [x3, #0x17]
    // 0xb45124: d0 = 25.000000
    //     0xb45124: fmov            d0, #25.00000000
    // 0xb45128: StoreField: r3->field_1b = d0
    //     0xb45128: stur            d0, [x3, #0x1b]
    // 0xb4512c: r0 = true
    //     0xb4512c: add             x0, NULL, #0x20  ; true
    // 0xb45130: StoreField: r3->field_23 = r0
    //     0xb45130: stur            w0, [x3, #0x23]
    // 0xb45134: ldur            x2, [fp, #-0x18]
    // 0xb45138: r1 = Function '<anonymous closure>':.
    //     0xb45138: add             x1, PP, #0x56, lsl #12  ; [pp+0x567c8] AnonymousClosure: (0xb451c8), in [package:customer_app/app/presentation/views/glass/checkout_variants/widgets/checkout_bag_accordion.dart] _CheckoutBagAccordionState::build (0xb43e44)
    //     0xb4513c: ldr             x1, [x1, #0x7c8]
    // 0xb45140: r0 = AllocateClosure()
    //     0xb45140: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb45144: mov             x1, x0
    // 0xb45148: ldur            x0, [fp, #-0x10]
    // 0xb4514c: StoreField: r0->field_3b = r1
    //     0xb4514c: stur            w1, [x0, #0x3b]
    // 0xb45150: r0 = Padding()
    //     0xb45150: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb45154: r1 = Instance_EdgeInsets
    //     0xb45154: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb45158: ldr             x1, [x1, #0x668]
    // 0xb4515c: StoreField: r0->field_f = r1
    //     0xb4515c: stur            w1, [x0, #0xf]
    // 0xb45160: ldur            x1, [fp, #-0x10]
    // 0xb45164: StoreField: r0->field_b = r1
    //     0xb45164: stur            w1, [x0, #0xb]
    // 0xb45168: LeaveFrame
    //     0xb45168: mov             SP, fp
    //     0xb4516c: ldp             fp, lr, [SP], #0x10
    // 0xb45170: ret
    //     0xb45170: ret             
    // 0xb45174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45174: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45178: b               #0xb43e6c
    // 0xb4517c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4517c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45180: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45180: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45184: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45184: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45188: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45188: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4518c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4518c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45190: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45190: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45194: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45194: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb45198: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45198: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4519c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb4519c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb451a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb451a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb451c8, size: 0x7c
    // 0xb451c8: EnterFrame
    //     0xb451c8: stp             fp, lr, [SP, #-0x10]!
    //     0xb451cc: mov             fp, SP
    // 0xb451d0: AllocStack(0x8)
    //     0xb451d0: sub             SP, SP, #8
    // 0xb451d4: SetupParameters()
    //     0xb451d4: ldr             x0, [fp, #0x10]
    //     0xb451d8: ldur            w1, [x0, #0x17]
    //     0xb451dc: add             x1, x1, HEAP, lsl #32
    // 0xb451e0: CheckStackOverflow
    //     0xb451e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb451e4: cmp             SP, x16
    //     0xb451e8: b.ls            #0xb45238
    // 0xb451ec: LoadField: r0 = r1->field_f
    //     0xb451ec: ldur            w0, [x1, #0xf]
    // 0xb451f0: DecompressPointer r0
    //     0xb451f0: add             x0, x0, HEAP, lsl #32
    // 0xb451f4: LoadField: r1 = r0->field_b
    //     0xb451f4: ldur            w1, [x0, #0xb]
    // 0xb451f8: DecompressPointer r1
    //     0xb451f8: add             x1, x1, HEAP, lsl #32
    // 0xb451fc: cmp             w1, NULL
    // 0xb45200: b.eq            #0xb45240
    // 0xb45204: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb45204: ldur            w0, [x1, #0x17]
    // 0xb45208: DecompressPointer r0
    //     0xb45208: add             x0, x0, HEAP, lsl #32
    // 0xb4520c: str             x0, [SP]
    // 0xb45210: r4 = 0
    //     0xb45210: movz            x4, #0
    // 0xb45214: ldr             x0, [SP]
    // 0xb45218: r16 = UnlinkedCall_0x613b5c
    //     0xb45218: add             x16, PP, #0x56, lsl #12  ; [pp+0x567d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb4521c: add             x16, x16, #0x7d0
    // 0xb45220: ldp             x5, lr, [x16]
    // 0xb45224: blr             lr
    // 0xb45228: r0 = Null
    //     0xb45228: mov             x0, NULL
    // 0xb4522c: LeaveFrame
    //     0xb4522c: mov             SP, fp
    //     0xb45230: ldp             fp, lr, [SP], #0x10
    // 0xb45234: ret
    //     0xb45234: ret             
    // 0xb45238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb4523c: b               #0xb451ec
    // 0xb45240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45240: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb45244, size: 0x10c
    // 0xb45244: EnterFrame
    //     0xb45244: stp             fp, lr, [SP, #-0x10]!
    //     0xb45248: mov             fp, SP
    // 0xb4524c: AllocStack(0x8)
    //     0xb4524c: sub             SP, SP, #8
    // 0xb45250: SetupParameters()
    //     0xb45250: ldr             x0, [fp, #0x20]
    //     0xb45254: ldur            w1, [x0, #0x17]
    //     0xb45258: add             x1, x1, HEAP, lsl #32
    // 0xb4525c: CheckStackOverflow
    //     0xb4525c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb45260: cmp             SP, x16
    //     0xb45264: b.ls            #0xb45340
    // 0xb45268: LoadField: r2 = r1->field_f
    //     0xb45268: ldur            w2, [x1, #0xf]
    // 0xb4526c: DecompressPointer r2
    //     0xb4526c: add             x2, x2, HEAP, lsl #32
    // 0xb45270: stur            x2, [fp, #-8]
    // 0xb45274: LoadField: r0 = r2->field_b
    //     0xb45274: ldur            w0, [x2, #0xb]
    // 0xb45278: DecompressPointer r0
    //     0xb45278: add             x0, x0, HEAP, lsl #32
    // 0xb4527c: cmp             w0, NULL
    // 0xb45280: b.eq            #0xb45348
    // 0xb45284: LoadField: r1 = r0->field_b
    //     0xb45284: ldur            w1, [x0, #0xb]
    // 0xb45288: DecompressPointer r1
    //     0xb45288: add             x1, x1, HEAP, lsl #32
    // 0xb4528c: LoadField: r0 = r1->field_b
    //     0xb4528c: ldur            w0, [x1, #0xb]
    // 0xb45290: DecompressPointer r0
    //     0xb45290: add             x0, x0, HEAP, lsl #32
    // 0xb45294: cmp             w0, NULL
    // 0xb45298: b.ne            #0xb452a4
    // 0xb4529c: r0 = Null
    //     0xb4529c: mov             x0, NULL
    // 0xb452a0: b               #0xb45310
    // 0xb452a4: LoadField: r1 = r0->field_f
    //     0xb452a4: ldur            w1, [x0, #0xf]
    // 0xb452a8: DecompressPointer r1
    //     0xb452a8: add             x1, x1, HEAP, lsl #32
    // 0xb452ac: cmp             w1, NULL
    // 0xb452b0: b.ne            #0xb452bc
    // 0xb452b4: r0 = Null
    //     0xb452b4: mov             x0, NULL
    // 0xb452b8: b               #0xb45310
    // 0xb452bc: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb452bc: ldur            w3, [x1, #0x17]
    // 0xb452c0: DecompressPointer r3
    //     0xb452c0: add             x3, x3, HEAP, lsl #32
    // 0xb452c4: cmp             w3, NULL
    // 0xb452c8: b.ne            #0xb452d4
    // 0xb452cc: r0 = Null
    //     0xb452cc: mov             x0, NULL
    // 0xb452d0: b               #0xb45310
    // 0xb452d4: ldr             x0, [fp, #0x10]
    // 0xb452d8: LoadField: r1 = r3->field_b
    //     0xb452d8: ldur            w1, [x3, #0xb]
    // 0xb452dc: r4 = LoadInt32Instr(r0)
    //     0xb452dc: sbfx            x4, x0, #1, #0x1f
    //     0xb452e0: tbz             w0, #0, #0xb452e8
    //     0xb452e4: ldur            x4, [x0, #7]
    // 0xb452e8: r0 = LoadInt32Instr(r1)
    //     0xb452e8: sbfx            x0, x1, #1, #0x1f
    // 0xb452ec: mov             x1, x4
    // 0xb452f0: cmp             x1, x0
    // 0xb452f4: b.hs            #0xb4534c
    // 0xb452f8: LoadField: r0 = r3->field_f
    //     0xb452f8: ldur            w0, [x3, #0xf]
    // 0xb452fc: DecompressPointer r0
    //     0xb452fc: add             x0, x0, HEAP, lsl #32
    // 0xb45300: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb45300: add             x16, x0, x4, lsl #2
    //     0xb45304: ldur            w1, [x16, #0xf]
    // 0xb45308: DecompressPointer r1
    //     0xb45308: add             x1, x1, HEAP, lsl #32
    // 0xb4530c: mov             x0, x1
    // 0xb45310: cmp             w0, NULL
    // 0xb45314: b.ne            #0xb45324
    // 0xb45318: r0 = BEntities()
    //     0xb45318: bl              #0x9fc7d0  ; AllocateBEntitiesStub -> BEntities (size=0x5c)
    // 0xb4531c: mov             x2, x0
    // 0xb45320: b               #0xb45328
    // 0xb45324: mov             x2, x0
    // 0xb45328: ldur            x1, [fp, #-8]
    // 0xb4532c: ldr             x3, [fp, #0x18]
    // 0xb45330: r0 = lineThemeBagItem()
    //     0xb45330: bl              #0xa07044  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_bag_accordion.dart] _CheckoutBagAccordionState::lineThemeBagItem
    // 0xb45334: LeaveFrame
    //     0xb45334: mov             SP, fp
    //     0xb45338: ldp             fp, lr, [SP], #0x10
    // 0xb4533c: ret
    //     0xb4533c: ret             
    // 0xb45340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb45340: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb45344: b               #0xb45268
    // 0xb45348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb45348: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb4534c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb4534c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4106, size: 0x1c, field offset: 0xc
//   const constructor, 
class CheckoutBagAccordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e978, size: 0x24
    // 0xc7e978: EnterFrame
    //     0xc7e978: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e97c: mov             fp, SP
    // 0xc7e980: mov             x0, x1
    // 0xc7e984: r1 = <CheckoutBagAccordion>
    //     0xc7e984: add             x1, PP, #0x48, lsl #12  ; [pp+0x48a08] TypeArguments: <CheckoutBagAccordion>
    //     0xc7e988: ldr             x1, [x1, #0xa08]
    // 0xc7e98c: r0 = _CheckoutBagAccordionState()
    //     0xc7e98c: bl              #0xc7e99c  ; Allocate_CheckoutBagAccordionStateStub -> _CheckoutBagAccordionState (size=0x14)
    // 0xc7e990: LeaveFrame
    //     0xc7e990: mov             SP, fp
    //     0xc7e994: ldp             fp, lr, [SP], #0x10
    // 0xc7e998: ret
    //     0xc7e998: ret             
  }
}
