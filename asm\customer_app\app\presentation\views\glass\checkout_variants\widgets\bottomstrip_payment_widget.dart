// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variants/widgets/bottomstrip_payment_widget.dart

// class id: 1049361, size: 0x8
class :: {
}

// class id: 3371, size: 0x14, field offset: 0x14
class BottomStripPaymentStripState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x93e0d0, size: 0x144
    // 0x93e0d0: EnterFrame
    //     0x93e0d0: stp             fp, lr, [SP, #-0x10]!
    //     0x93e0d4: mov             fp, SP
    // 0x93e0d8: AllocStack(0x18)
    //     0x93e0d8: sub             SP, SP, #0x18
    // 0x93e0dc: SetupParameters(BottomStripPaymentStripState this /* r1 => r1, fp-0x8 */)
    //     0x93e0dc: stur            x1, [fp, #-8]
    // 0x93e0e0: CheckStackOverflow
    //     0x93e0e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93e0e4: cmp             SP, x16
    //     0x93e0e8: b.ls            #0x93e204
    // 0x93e0ec: r1 = 1
    //     0x93e0ec: movz            x1, #0x1
    // 0x93e0f0: r0 = AllocateContext()
    //     0x93e0f0: bl              #0x16f6108  ; AllocateContextStub
    // 0x93e0f4: mov             x1, x0
    // 0x93e0f8: ldur            x0, [fp, #-8]
    // 0x93e0fc: StoreField: r1->field_f = r0
    //     0x93e0fc: stur            w0, [x1, #0xf]
    // 0x93e100: LoadField: r2 = r0->field_b
    //     0x93e100: ldur            w2, [x0, #0xb]
    // 0x93e104: DecompressPointer r2
    //     0x93e104: add             x2, x2, HEAP, lsl #32
    // 0x93e108: cmp             w2, NULL
    // 0x93e10c: b.eq            #0x93e20c
    // 0x93e110: r0 = LoadStaticField(0x878)
    //     0x93e110: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x93e114: ldr             x0, [x0, #0x10f0]
    // 0x93e118: cmp             w0, NULL
    // 0x93e11c: b.eq            #0x93e210
    // 0x93e120: LoadField: r3 = r0->field_53
    //     0x93e120: ldur            w3, [x0, #0x53]
    // 0x93e124: DecompressPointer r3
    //     0x93e124: add             x3, x3, HEAP, lsl #32
    // 0x93e128: stur            x3, [fp, #-0x10]
    // 0x93e12c: LoadField: r0 = r3->field_7
    //     0x93e12c: ldur            w0, [x3, #7]
    // 0x93e130: DecompressPointer r0
    //     0x93e130: add             x0, x0, HEAP, lsl #32
    // 0x93e134: mov             x2, x1
    // 0x93e138: stur            x0, [fp, #-8]
    // 0x93e13c: r1 = Function '<anonymous closure>':.
    //     0x93e13c: add             x1, PP, #0x56, lsl #12  ; [pp+0x565d0] AnonymousClosure: (0x903138), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/bottomstrip_payment_widget.dart] BottomStripPaymentStripState::initState (0x945ba8)
    //     0x93e140: ldr             x1, [x1, #0x5d0]
    // 0x93e144: r0 = AllocateClosure()
    //     0x93e144: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x93e148: ldur            x2, [fp, #-8]
    // 0x93e14c: mov             x3, x0
    // 0x93e150: r1 = Null
    //     0x93e150: mov             x1, NULL
    // 0x93e154: stur            x3, [fp, #-8]
    // 0x93e158: cmp             w2, NULL
    // 0x93e15c: b.eq            #0x93e17c
    // 0x93e160: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x93e160: ldur            w4, [x2, #0x17]
    // 0x93e164: DecompressPointer r4
    //     0x93e164: add             x4, x4, HEAP, lsl #32
    // 0x93e168: r8 = X0
    //     0x93e168: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x93e16c: LoadField: r9 = r4->field_7
    //     0x93e16c: ldur            x9, [x4, #7]
    // 0x93e170: r3 = Null
    //     0x93e170: add             x3, PP, #0x56, lsl #12  ; [pp+0x565d8] Null
    //     0x93e174: ldr             x3, [x3, #0x5d8]
    // 0x93e178: blr             x9
    // 0x93e17c: ldur            x0, [fp, #-0x10]
    // 0x93e180: LoadField: r1 = r0->field_b
    //     0x93e180: ldur            w1, [x0, #0xb]
    // 0x93e184: LoadField: r2 = r0->field_f
    //     0x93e184: ldur            w2, [x0, #0xf]
    // 0x93e188: DecompressPointer r2
    //     0x93e188: add             x2, x2, HEAP, lsl #32
    // 0x93e18c: LoadField: r3 = r2->field_b
    //     0x93e18c: ldur            w3, [x2, #0xb]
    // 0x93e190: r2 = LoadInt32Instr(r1)
    //     0x93e190: sbfx            x2, x1, #1, #0x1f
    // 0x93e194: stur            x2, [fp, #-0x18]
    // 0x93e198: r1 = LoadInt32Instr(r3)
    //     0x93e198: sbfx            x1, x3, #1, #0x1f
    // 0x93e19c: cmp             x2, x1
    // 0x93e1a0: b.ne            #0x93e1ac
    // 0x93e1a4: mov             x1, x0
    // 0x93e1a8: r0 = _growToNextCapacity()
    //     0x93e1a8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x93e1ac: ldur            x2, [fp, #-0x10]
    // 0x93e1b0: ldur            x3, [fp, #-0x18]
    // 0x93e1b4: add             x4, x3, #1
    // 0x93e1b8: lsl             x5, x4, #1
    // 0x93e1bc: StoreField: r2->field_b = r5
    //     0x93e1bc: stur            w5, [x2, #0xb]
    // 0x93e1c0: LoadField: r1 = r2->field_f
    //     0x93e1c0: ldur            w1, [x2, #0xf]
    // 0x93e1c4: DecompressPointer r1
    //     0x93e1c4: add             x1, x1, HEAP, lsl #32
    // 0x93e1c8: ldur            x0, [fp, #-8]
    // 0x93e1cc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x93e1cc: add             x25, x1, x3, lsl #2
    //     0x93e1d0: add             x25, x25, #0xf
    //     0x93e1d4: str             w0, [x25]
    //     0x93e1d8: tbz             w0, #0, #0x93e1f4
    //     0x93e1dc: ldurb           w16, [x1, #-1]
    //     0x93e1e0: ldurb           w17, [x0, #-1]
    //     0x93e1e4: and             x16, x17, x16, lsr #2
    //     0x93e1e8: tst             x16, HEAP, lsr #32
    //     0x93e1ec: b.eq            #0x93e1f4
    //     0x93e1f0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x93e1f4: r0 = Null
    //     0x93e1f4: mov             x0, NULL
    // 0x93e1f8: LeaveFrame
    //     0x93e1f8: mov             SP, fp
    //     0x93e1fc: ldp             fp, lr, [SP], #0x10
    // 0x93e200: ret
    //     0x93e200: ret             
    // 0x93e204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93e204: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93e208: b               #0x93e0ec
    // 0x93e20c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93e20c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x93e210: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x93e210: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4109, size: 0x14, field offset: 0xc
//   const constructor, 
class BottomStripPaymentWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7e8a0, size: 0x24
    // 0xc7e8a0: EnterFrame
    //     0xc7e8a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc7e8a4: mov             fp, SP
    // 0xc7e8a8: mov             x0, x1
    // 0xc7e8ac: r1 = <BottomStripPaymentWidget>
    //     0xc7e8ac: add             x1, PP, #0x48, lsl #12  ; [pp+0x489d0] TypeArguments: <BottomStripPaymentWidget>
    //     0xc7e8b0: ldr             x1, [x1, #0x9d0]
    // 0xc7e8b4: r0 = BottomStripPaymentStripState()
    //     0xc7e8b4: bl              #0xc7e8c4  ; AllocateBottomStripPaymentStripStateStub -> BottomStripPaymentStripState (size=0x14)
    // 0xc7e8b8: LeaveFrame
    //     0xc7e8b8: mov             SP, fp
    //     0xc7e8bc: ldp             fp, lr, [SP], #0x10
    // 0xc7e8c0: ret
    //     0xc7e8c0: ret             
  }
}
