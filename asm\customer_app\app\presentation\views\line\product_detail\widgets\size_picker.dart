// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/size_picker.dart

// class id: 1049573, size: 0x8
class :: {
}

// class id: 3212, size: 0x1c, field offset: 0x14
class _SizePickerState extends State<dynamic> {

  [closure] bool <anonymous closure>(dynamic, WidgetEntity?) {
    // ** addr: 0xa9d774, size: 0x98
    // 0xa9d774: EnterFrame
    //     0xa9d774: stp             fp, lr, [SP, #-0x10]!
    //     0xa9d778: mov             fp, SP
    // 0xa9d77c: AllocStack(0x10)
    //     0xa9d77c: sub             SP, SP, #0x10
    // 0xa9d780: SetupParameters()
    //     0xa9d780: ldr             x0, [fp, #0x18]
    //     0xa9d784: ldur            w1, [x0, #0x17]
    //     0xa9d788: add             x1, x1, HEAP, lsl #32
    // 0xa9d78c: CheckStackOverflow
    //     0xa9d78c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa9d790: cmp             SP, x16
    //     0xa9d794: b.ls            #0xa9d800
    // 0xa9d798: ldr             x0, [fp, #0x10]
    // 0xa9d79c: cmp             w0, NULL
    // 0xa9d7a0: b.ne            #0xa9d7ac
    // 0xa9d7a4: r0 = Null
    //     0xa9d7a4: mov             x0, NULL
    // 0xa9d7a8: b               #0xa9d7b8
    // 0xa9d7ac: LoadField: r2 = r0->field_37
    //     0xa9d7ac: ldur            w2, [x0, #0x37]
    // 0xa9d7b0: DecompressPointer r2
    //     0xa9d7b0: add             x2, x2, HEAP, lsl #32
    // 0xa9d7b4: mov             x0, x2
    // 0xa9d7b8: LoadField: r2 = r1->field_f
    //     0xa9d7b8: ldur            w2, [x1, #0xf]
    // 0xa9d7bc: DecompressPointer r2
    //     0xa9d7bc: add             x2, x2, HEAP, lsl #32
    // 0xa9d7c0: LoadField: r1 = r2->field_b
    //     0xa9d7c0: ldur            w1, [x2, #0xb]
    // 0xa9d7c4: DecompressPointer r1
    //     0xa9d7c4: add             x1, x1, HEAP, lsl #32
    // 0xa9d7c8: cmp             w1, NULL
    // 0xa9d7cc: b.eq            #0xa9d808
    // 0xa9d7d0: LoadField: r2 = r1->field_13
    //     0xa9d7d0: ldur            w2, [x1, #0x13]
    // 0xa9d7d4: DecompressPointer r2
    //     0xa9d7d4: add             x2, x2, HEAP, lsl #32
    // 0xa9d7d8: r1 = LoadClassIdInstr(r0)
    //     0xa9d7d8: ldur            x1, [x0, #-1]
    //     0xa9d7dc: ubfx            x1, x1, #0xc, #0x14
    // 0xa9d7e0: stp             x2, x0, [SP]
    // 0xa9d7e4: mov             x0, x1
    // 0xa9d7e8: mov             lr, x0
    // 0xa9d7ec: ldr             lr, [x21, lr, lsl #3]
    // 0xa9d7f0: blr             lr
    // 0xa9d7f4: LeaveFrame
    //     0xa9d7f4: mov             SP, fp
    //     0xa9d7f8: ldp             fp, lr, [SP], #0x10
    // 0xa9d7fc: ret
    //     0xa9d7fc: ret             
    // 0xa9d800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa9d800: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa9d804: b               #0xa9d798
    // 0xa9d808: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa9d808: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] DropdownMenuItem<WidgetEntity> <anonymous closure>(dynamic, WidgetEntity) {
    // ** addr: 0xb9428c, size: 0x14c
    // 0xb9428c: EnterFrame
    //     0xb9428c: stp             fp, lr, [SP, #-0x10]!
    //     0xb94290: mov             fp, SP
    // 0xb94294: AllocStack(0x28)
    //     0xb94294: sub             SP, SP, #0x28
    // 0xb94298: SetupParameters()
    //     0xb94298: ldr             x0, [fp, #0x18]
    //     0xb9429c: ldur            w1, [x0, #0x17]
    //     0xb942a0: add             x1, x1, HEAP, lsl #32
    // 0xb942a4: CheckStackOverflow
    //     0xb942a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb942a8: cmp             SP, x16
    //     0xb942ac: b.ls            #0xb943d0
    // 0xb942b0: ldr             x0, [fp, #0x10]
    // 0xb942b4: LoadField: r2 = r0->field_33
    //     0xb942b4: ldur            w2, [x0, #0x33]
    // 0xb942b8: DecompressPointer r2
    //     0xb942b8: add             x2, x2, HEAP, lsl #32
    // 0xb942bc: cmp             w2, NULL
    // 0xb942c0: b.ne            #0xb942c8
    // 0xb942c4: r2 = ""
    //     0xb942c4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb942c8: stur            x2, [fp, #-8]
    // 0xb942cc: LoadField: r3 = r1->field_13
    //     0xb942cc: ldur            w3, [x1, #0x13]
    // 0xb942d0: DecompressPointer r3
    //     0xb942d0: add             x3, x3, HEAP, lsl #32
    // 0xb942d4: mov             x1, x3
    // 0xb942d8: r0 = of()
    //     0xb942d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb942dc: LoadField: r1 = r0->field_87
    //     0xb942dc: ldur            w1, [x0, #0x87]
    // 0xb942e0: DecompressPointer r1
    //     0xb942e0: add             x1, x1, HEAP, lsl #32
    // 0xb942e4: LoadField: r0 = r1->field_7
    //     0xb942e4: ldur            w0, [x1, #7]
    // 0xb942e8: DecompressPointer r0
    //     0xb942e8: add             x0, x0, HEAP, lsl #32
    // 0xb942ec: stur            x0, [fp, #-0x10]
    // 0xb942f0: r1 = Instance_Color
    //     0xb942f0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb942f4: d0 = 0.700000
    //     0xb942f4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb942f8: ldr             d0, [x17, #0xf48]
    // 0xb942fc: r0 = withOpacity()
    //     0xb942fc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb94300: r16 = 14.000000
    //     0xb94300: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb94304: ldr             x16, [x16, #0x1d8]
    // 0xb94308: stp             x0, x16, [SP]
    // 0xb9430c: ldur            x1, [fp, #-0x10]
    // 0xb94310: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb94310: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb94314: ldr             x4, [x4, #0xaa0]
    // 0xb94318: r0 = copyWith()
    //     0xb94318: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb9431c: stur            x0, [fp, #-0x10]
    // 0xb94320: r0 = Text()
    //     0xb94320: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb94324: mov             x1, x0
    // 0xb94328: ldur            x0, [fp, #-8]
    // 0xb9432c: stur            x1, [fp, #-0x18]
    // 0xb94330: StoreField: r1->field_b = r0
    //     0xb94330: stur            w0, [x1, #0xb]
    // 0xb94334: ldur            x0, [fp, #-0x10]
    // 0xb94338: StoreField: r1->field_13 = r0
    //     0xb94338: stur            w0, [x1, #0x13]
    // 0xb9433c: r0 = Instance_TextOverflow
    //     0xb9433c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb94340: ldr             x0, [x0, #0xe10]
    // 0xb94344: StoreField: r1->field_2b = r0
    //     0xb94344: stur            w0, [x1, #0x2b]
    // 0xb94348: r0 = Center()
    //     0xb94348: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb9434c: mov             x1, x0
    // 0xb94350: r0 = Instance_Alignment
    //     0xb94350: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb94354: ldr             x0, [x0, #0xb10]
    // 0xb94358: stur            x1, [fp, #-8]
    // 0xb9435c: StoreField: r1->field_f = r0
    //     0xb9435c: stur            w0, [x1, #0xf]
    // 0xb94360: ldur            x0, [fp, #-0x18]
    // 0xb94364: StoreField: r1->field_b = r0
    //     0xb94364: stur            w0, [x1, #0xb]
    // 0xb94368: r0 = SizedBox()
    //     0xb94368: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb9436c: mov             x2, x0
    // 0xb94370: r0 = 140.000000
    //     0xb94370: add             x0, PP, #0x52, lsl #12  ; [pp+0x523c8] 140
    //     0xb94374: ldr             x0, [x0, #0x3c8]
    // 0xb94378: stur            x2, [fp, #-0x10]
    // 0xb9437c: StoreField: r2->field_f = r0
    //     0xb9437c: stur            w0, [x2, #0xf]
    // 0xb94380: r0 = 44.000000
    //     0xb94380: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xb94384: ldr             x0, [x0, #0xad8]
    // 0xb94388: StoreField: r2->field_13 = r0
    //     0xb94388: stur            w0, [x2, #0x13]
    // 0xb9438c: ldur            x0, [fp, #-8]
    // 0xb94390: StoreField: r2->field_b = r0
    //     0xb94390: stur            w0, [x2, #0xb]
    // 0xb94394: r1 = <WidgetEntity>
    //     0xb94394: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0xb94398: ldr             x1, [x1, #0x878]
    // 0xb9439c: r0 = DropdownMenuItem()
    //     0xb9439c: bl              #0x9ba75c  ; AllocateDropdownMenuItemStub -> DropdownMenuItem<X0> (size=0x24)
    // 0xb943a0: ldr             x1, [fp, #0x10]
    // 0xb943a4: StoreField: r0->field_1b = r1
    //     0xb943a4: stur            w1, [x0, #0x1b]
    // 0xb943a8: r1 = true
    //     0xb943a8: add             x1, NULL, #0x20  ; true
    // 0xb943ac: StoreField: r0->field_1f = r1
    //     0xb943ac: stur            w1, [x0, #0x1f]
    // 0xb943b0: r1 = Instance_AlignmentDirectional
    //     0xb943b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] Obj!AlignmentDirectional@d5a601
    //     0xb943b4: ldr             x1, [x1, #0xb70]
    // 0xb943b8: StoreField: r0->field_f = r1
    //     0xb943b8: stur            w1, [x0, #0xf]
    // 0xb943bc: ldur            x1, [fp, #-0x10]
    // 0xb943c0: StoreField: r0->field_b = r1
    //     0xb943c0: stur            w1, [x0, #0xb]
    // 0xb943c4: LeaveFrame
    //     0xb943c4: mov             SP, fp
    //     0xb943c8: ldp             fp, lr, [SP], #0x10
    // 0xb943cc: ret
    //     0xb943cc: ret             
    // 0xb943d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb943d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb943d4: b               #0xb942b0
  }
  _ build(/* No info */) {
    // ** addr: 0xc0ec90, size: 0x7a8
    // 0xc0ec90: EnterFrame
    //     0xc0ec90: stp             fp, lr, [SP, #-0x10]!
    //     0xc0ec94: mov             fp, SP
    // 0xc0ec98: AllocStack(0x58)
    //     0xc0ec98: sub             SP, SP, #0x58
    // 0xc0ec9c: SetupParameters(_SizePickerState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc0ec9c: stur            x1, [fp, #-8]
    //     0xc0eca0: stur            x2, [fp, #-0x10]
    // 0xc0eca4: CheckStackOverflow
    //     0xc0eca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0eca8: cmp             SP, x16
    //     0xc0ecac: b.ls            #0xc0f414
    // 0xc0ecb0: r1 = 2
    //     0xc0ecb0: movz            x1, #0x2
    // 0xc0ecb4: r0 = AllocateContext()
    //     0xc0ecb4: bl              #0x16f6108  ; AllocateContextStub
    // 0xc0ecb8: mov             x1, x0
    // 0xc0ecbc: ldur            x0, [fp, #-8]
    // 0xc0ecc0: stur            x1, [fp, #-0x18]
    // 0xc0ecc4: StoreField: r1->field_f = r0
    //     0xc0ecc4: stur            w0, [x1, #0xf]
    // 0xc0ecc8: ldur            x2, [fp, #-0x10]
    // 0xc0eccc: StoreField: r1->field_13 = r2
    //     0xc0eccc: stur            w2, [x1, #0x13]
    // 0xc0ecd0: LoadField: r2 = r0->field_b
    //     0xc0ecd0: ldur            w2, [x0, #0xb]
    // 0xc0ecd4: DecompressPointer r2
    //     0xc0ecd4: add             x2, x2, HEAP, lsl #32
    // 0xc0ecd8: cmp             w2, NULL
    // 0xc0ecdc: b.eq            #0xc0f41c
    // 0xc0ece0: LoadField: r3 = r2->field_b
    //     0xc0ece0: ldur            w3, [x2, #0xb]
    // 0xc0ece4: DecompressPointer r3
    //     0xc0ece4: add             x3, x3, HEAP, lsl #32
    // 0xc0ece8: LoadField: r2 = r3->field_1b
    //     0xc0ece8: ldur            w2, [x3, #0x1b]
    // 0xc0ecec: DecompressPointer r2
    //     0xc0ecec: add             x2, x2, HEAP, lsl #32
    // 0xc0ecf0: cmp             w2, NULL
    // 0xc0ecf4: b.ne            #0xc0ed00
    // 0xc0ecf8: r2 = Null
    //     0xc0ecf8: mov             x2, NULL
    // 0xc0ecfc: b               #0xc0ed58
    // 0xc0ed00: r16 = <WidgetEntity?>
    //     0xc0ed00: add             x16, PP, #0x31, lsl #12  ; [pp+0x31e28] TypeArguments: <WidgetEntity?>
    //     0xc0ed04: ldr             x16, [x16, #0xe28]
    // 0xc0ed08: stp             x2, x16, [SP]
    // 0xc0ed0c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc0ed0c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc0ed10: r0 = cast()
    //     0xc0ed10: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0xc0ed14: ldur            x2, [fp, #-0x18]
    // 0xc0ed18: r1 = Function '<anonymous closure>':.
    //     0xc0ed18: add             x1, PP, #0x52, lsl #12  ; [pp+0x52360] AnonymousClosure: (0xa9d774), in [package:customer_app/app/presentation/views/line/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xc0ec90)
    //     0xc0ed1c: ldr             x1, [x1, #0x360]
    // 0xc0ed20: stur            x0, [fp, #-0x10]
    // 0xc0ed24: r0 = AllocateClosure()
    //     0xc0ed24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0ed28: r1 = Function '<anonymous closure>':.
    //     0xc0ed28: add             x1, PP, #0x52, lsl #12  ; [pp+0x52368] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc0ed2c: ldr             x1, [x1, #0x368]
    // 0xc0ed30: r2 = Null
    //     0xc0ed30: mov             x2, NULL
    // 0xc0ed34: stur            x0, [fp, #-0x20]
    // 0xc0ed38: r0 = AllocateClosure()
    //     0xc0ed38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0ed3c: str             x0, [SP]
    // 0xc0ed40: ldur            x1, [fp, #-0x10]
    // 0xc0ed44: ldur            x2, [fp, #-0x20]
    // 0xc0ed48: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xc0ed48: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xc0ed4c: ldr             x4, [x4, #0xb48]
    // 0xc0ed50: r0 = firstWhere()
    //     0xc0ed50: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0xc0ed54: mov             x2, x0
    // 0xc0ed58: cmp             w2, NULL
    // 0xc0ed5c: b.eq            #0xc0edec
    // 0xc0ed60: ldur            x0, [fp, #-8]
    // 0xc0ed64: LoadField: r1 = r0->field_b
    //     0xc0ed64: ldur            w1, [x0, #0xb]
    // 0xc0ed68: DecompressPointer r1
    //     0xc0ed68: add             x1, x1, HEAP, lsl #32
    // 0xc0ed6c: cmp             w1, NULL
    // 0xc0ed70: b.eq            #0xc0f420
    // 0xc0ed74: LoadField: r3 = r1->field_b
    //     0xc0ed74: ldur            w3, [x1, #0xb]
    // 0xc0ed78: DecompressPointer r3
    //     0xc0ed78: add             x3, x3, HEAP, lsl #32
    // 0xc0ed7c: LoadField: r1 = r3->field_1b
    //     0xc0ed7c: ldur            w1, [x3, #0x1b]
    // 0xc0ed80: DecompressPointer r1
    //     0xc0ed80: add             x1, x1, HEAP, lsl #32
    // 0xc0ed84: cmp             w1, NULL
    // 0xc0ed88: b.ne            #0xc0ed98
    // 0xc0ed8c: mov             x2, x0
    // 0xc0ed90: r1 = Null
    //     0xc0ed90: mov             x1, NULL
    // 0xc0ed94: b               #0xc0edc0
    // 0xc0ed98: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0ed98: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0ed9c: r0 = indexOf()
    //     0xc0ed9c: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xc0eda0: mov             x2, x0
    // 0xc0eda4: r0 = BoxInt64Instr(r2)
    //     0xc0eda4: sbfiz           x0, x2, #1, #0x1f
    //     0xc0eda8: cmp             x2, x0, asr #1
    //     0xc0edac: b.eq            #0xc0edb8
    //     0xc0edb0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc0edb4: stur            x2, [x0, #7]
    // 0xc0edb8: mov             x1, x0
    // 0xc0edbc: ldur            x2, [fp, #-8]
    // 0xc0edc0: mov             x0, x1
    // 0xc0edc4: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0edc4: stur            w0, [x2, #0x17]
    //     0xc0edc8: tbz             w0, #0, #0xc0ede4
    //     0xc0edcc: ldurb           w16, [x2, #-1]
    //     0xc0edd0: ldurb           w17, [x0, #-1]
    //     0xc0edd4: and             x16, x17, x16, lsr #2
    //     0xc0edd8: tst             x16, HEAP, lsr #32
    //     0xc0eddc: b.eq            #0xc0ede4
    //     0xc0ede0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc0ede4: mov             x0, x1
    // 0xc0ede8: b               #0xc0edf8
    // 0xc0edec: ldur            x2, [fp, #-8]
    // 0xc0edf0: ArrayStore: r2[0] = rZR  ; List_4
    //     0xc0edf0: stur            wzr, [x2, #0x17]
    // 0xc0edf4: r0 = 0
    //     0xc0edf4: movz            x0, #0
    // 0xc0edf8: LoadField: r1 = r2->field_b
    //     0xc0edf8: ldur            w1, [x2, #0xb]
    // 0xc0edfc: DecompressPointer r1
    //     0xc0edfc: add             x1, x1, HEAP, lsl #32
    // 0xc0ee00: cmp             w1, NULL
    // 0xc0ee04: b.eq            #0xc0f424
    // 0xc0ee08: LoadField: r3 = r1->field_b
    //     0xc0ee08: ldur            w3, [x1, #0xb]
    // 0xc0ee0c: DecompressPointer r3
    //     0xc0ee0c: add             x3, x3, HEAP, lsl #32
    // 0xc0ee10: LoadField: r4 = r3->field_1b
    //     0xc0ee10: ldur            w4, [x3, #0x1b]
    // 0xc0ee14: DecompressPointer r4
    //     0xc0ee14: add             x4, x4, HEAP, lsl #32
    // 0xc0ee18: cmp             w4, NULL
    // 0xc0ee1c: b.ne            #0xc0ee28
    // 0xc0ee20: r0 = Null
    //     0xc0ee20: mov             x0, NULL
    // 0xc0ee24: b               #0xc0ee78
    // 0xc0ee28: cmp             w0, NULL
    // 0xc0ee2c: b.ne            #0xc0ee38
    // 0xc0ee30: r3 = 0
    //     0xc0ee30: movz            x3, #0
    // 0xc0ee34: b               #0xc0ee48
    // 0xc0ee38: r1 = LoadInt32Instr(r0)
    //     0xc0ee38: sbfx            x1, x0, #1, #0x1f
    //     0xc0ee3c: tbz             w0, #0, #0xc0ee44
    //     0xc0ee40: ldur            x1, [x0, #7]
    // 0xc0ee44: mov             x3, x1
    // 0xc0ee48: LoadField: r0 = r4->field_b
    //     0xc0ee48: ldur            w0, [x4, #0xb]
    // 0xc0ee4c: r1 = LoadInt32Instr(r0)
    //     0xc0ee4c: sbfx            x1, x0, #1, #0x1f
    // 0xc0ee50: mov             x0, x1
    // 0xc0ee54: mov             x1, x3
    // 0xc0ee58: cmp             x1, x0
    // 0xc0ee5c: b.hs            #0xc0f428
    // 0xc0ee60: LoadField: r0 = r4->field_f
    //     0xc0ee60: ldur            w0, [x4, #0xf]
    // 0xc0ee64: DecompressPointer r0
    //     0xc0ee64: add             x0, x0, HEAP, lsl #32
    // 0xc0ee68: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xc0ee68: add             x16, x0, x3, lsl #2
    //     0xc0ee6c: ldur            w1, [x16, #0xf]
    // 0xc0ee70: DecompressPointer r1
    //     0xc0ee70: add             x1, x1, HEAP, lsl #32
    // 0xc0ee74: mov             x0, x1
    // 0xc0ee78: cmp             w0, NULL
    // 0xc0ee7c: b.ne            #0xc0ee84
    // 0xc0ee80: r0 = WidgetEntity()
    //     0xc0ee80: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xc0ee84: ldur            x3, [fp, #-8]
    // 0xc0ee88: StoreField: r3->field_13 = r0
    //     0xc0ee88: stur            w0, [x3, #0x13]
    //     0xc0ee8c: ldurb           w16, [x3, #-1]
    //     0xc0ee90: ldurb           w17, [x0, #-1]
    //     0xc0ee94: and             x16, x17, x16, lsr #2
    //     0xc0ee98: tst             x16, HEAP, lsr #32
    //     0xc0ee9c: b.eq            #0xc0eea4
    //     0xc0eea0: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc0eea4: r1 = <Widget>
    //     0xc0eea4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc0eea8: r2 = 0
    //     0xc0eea8: movz            x2, #0
    // 0xc0eeac: r0 = _GrowableList()
    //     0xc0eeac: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0eeb0: mov             x2, x0
    // 0xc0eeb4: ldur            x1, [fp, #-8]
    // 0xc0eeb8: stur            x2, [fp, #-0x10]
    // 0xc0eebc: LoadField: r0 = r1->field_b
    //     0xc0eebc: ldur            w0, [x1, #0xb]
    // 0xc0eec0: DecompressPointer r0
    //     0xc0eec0: add             x0, x0, HEAP, lsl #32
    // 0xc0eec4: cmp             w0, NULL
    // 0xc0eec8: b.eq            #0xc0f42c
    // 0xc0eecc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc0eecc: ldur            w3, [x0, #0x17]
    // 0xc0eed0: DecompressPointer r3
    //     0xc0eed0: add             x3, x3, HEAP, lsl #32
    // 0xc0eed4: r0 = LoadClassIdInstr(r3)
    //     0xc0eed4: ldur            x0, [x3, #-1]
    //     0xc0eed8: ubfx            x0, x0, #0xc, #0x14
    // 0xc0eedc: r16 = "size"
    //     0xc0eedc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xc0eee0: ldr             x16, [x16, #0x9c0]
    // 0xc0eee4: stp             x16, x3, [SP]
    // 0xc0eee8: mov             lr, x0
    // 0xc0eeec: ldr             lr, [x21, lr, lsl #3]
    // 0xc0eef0: blr             lr
    // 0xc0eef4: tbnz            w0, #4, #0xc0efe8
    // 0xc0eef8: ldur            x2, [fp, #-0x18]
    // 0xc0eefc: ldur            x0, [fp, #-0x10]
    // 0xc0ef00: LoadField: r1 = r2->field_13
    //     0xc0ef00: ldur            w1, [x2, #0x13]
    // 0xc0ef04: DecompressPointer r1
    //     0xc0ef04: add             x1, x1, HEAP, lsl #32
    // 0xc0ef08: r0 = of()
    //     0xc0ef08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0ef0c: LoadField: r1 = r0->field_87
    //     0xc0ef0c: ldur            w1, [x0, #0x87]
    // 0xc0ef10: DecompressPointer r1
    //     0xc0ef10: add             x1, x1, HEAP, lsl #32
    // 0xc0ef14: LoadField: r0 = r1->field_7
    //     0xc0ef14: ldur            w0, [x1, #7]
    // 0xc0ef18: DecompressPointer r0
    //     0xc0ef18: add             x0, x0, HEAP, lsl #32
    // 0xc0ef1c: r16 = 12.000000
    //     0xc0ef1c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0ef20: ldr             x16, [x16, #0x9e8]
    // 0xc0ef24: r30 = Instance_Color
    //     0xc0ef24: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0ef28: stp             lr, x16, [SP]
    // 0xc0ef2c: mov             x1, x0
    // 0xc0ef30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0ef30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0ef34: ldr             x4, [x4, #0xaa0]
    // 0xc0ef38: r0 = copyWith()
    //     0xc0ef38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0ef3c: stur            x0, [fp, #-0x20]
    // 0xc0ef40: r0 = Text()
    //     0xc0ef40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0ef44: mov             x2, x0
    // 0xc0ef48: r0 = "Select Size"
    //     0xc0ef48: add             x0, PP, #0x52, lsl #12  ; [pp+0x52370] "Select Size"
    //     0xc0ef4c: ldr             x0, [x0, #0x370]
    // 0xc0ef50: stur            x2, [fp, #-0x30]
    // 0xc0ef54: StoreField: r2->field_b = r0
    //     0xc0ef54: stur            w0, [x2, #0xb]
    // 0xc0ef58: ldur            x0, [fp, #-0x20]
    // 0xc0ef5c: StoreField: r2->field_13 = r0
    //     0xc0ef5c: stur            w0, [x2, #0x13]
    // 0xc0ef60: ldur            x0, [fp, #-0x10]
    // 0xc0ef64: LoadField: r1 = r0->field_b
    //     0xc0ef64: ldur            w1, [x0, #0xb]
    // 0xc0ef68: LoadField: r3 = r0->field_f
    //     0xc0ef68: ldur            w3, [x0, #0xf]
    // 0xc0ef6c: DecompressPointer r3
    //     0xc0ef6c: add             x3, x3, HEAP, lsl #32
    // 0xc0ef70: LoadField: r4 = r3->field_b
    //     0xc0ef70: ldur            w4, [x3, #0xb]
    // 0xc0ef74: r3 = LoadInt32Instr(r1)
    //     0xc0ef74: sbfx            x3, x1, #1, #0x1f
    // 0xc0ef78: stur            x3, [fp, #-0x28]
    // 0xc0ef7c: r1 = LoadInt32Instr(r4)
    //     0xc0ef7c: sbfx            x1, x4, #1, #0x1f
    // 0xc0ef80: cmp             x3, x1
    // 0xc0ef84: b.ne            #0xc0ef90
    // 0xc0ef88: mov             x1, x0
    // 0xc0ef8c: r0 = _growToNextCapacity()
    //     0xc0ef8c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0ef90: ldur            x2, [fp, #-0x10]
    // 0xc0ef94: ldur            x3, [fp, #-0x28]
    // 0xc0ef98: add             x4, x3, #1
    // 0xc0ef9c: lsl             x0, x4, #1
    // 0xc0efa0: StoreField: r2->field_b = r0
    //     0xc0efa0: stur            w0, [x2, #0xb]
    // 0xc0efa4: LoadField: r5 = r2->field_f
    //     0xc0efa4: ldur            w5, [x2, #0xf]
    // 0xc0efa8: DecompressPointer r5
    //     0xc0efa8: add             x5, x5, HEAP, lsl #32
    // 0xc0efac: mov             x1, x5
    // 0xc0efb0: ldur            x0, [fp, #-0x30]
    // 0xc0efb4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0efb4: add             x25, x1, x3, lsl #2
    //     0xc0efb8: add             x25, x25, #0xf
    //     0xc0efbc: str             w0, [x25]
    //     0xc0efc0: tbz             w0, #0, #0xc0efdc
    //     0xc0efc4: ldurb           w16, [x1, #-1]
    //     0xc0efc8: ldurb           w17, [x0, #-1]
    //     0xc0efcc: and             x16, x17, x16, lsr #2
    //     0xc0efd0: tst             x16, HEAP, lsr #32
    //     0xc0efd4: b.eq            #0xc0efdc
    //     0xc0efd8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0efdc: mov             x3, x4
    // 0xc0efe0: mov             x0, x5
    // 0xc0efe4: b               #0xc0f0d4
    // 0xc0efe8: ldur            x0, [fp, #-0x18]
    // 0xc0efec: ldur            x2, [fp, #-0x10]
    // 0xc0eff0: LoadField: r1 = r0->field_13
    //     0xc0eff0: ldur            w1, [x0, #0x13]
    // 0xc0eff4: DecompressPointer r1
    //     0xc0eff4: add             x1, x1, HEAP, lsl #32
    // 0xc0eff8: r0 = of()
    //     0xc0eff8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0effc: LoadField: r1 = r0->field_87
    //     0xc0effc: ldur            w1, [x0, #0x87]
    // 0xc0f000: DecompressPointer r1
    //     0xc0f000: add             x1, x1, HEAP, lsl #32
    // 0xc0f004: LoadField: r0 = r1->field_7
    //     0xc0f004: ldur            w0, [x1, #7]
    // 0xc0f008: DecompressPointer r0
    //     0xc0f008: add             x0, x0, HEAP, lsl #32
    // 0xc0f00c: r16 = 12.000000
    //     0xc0f00c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xc0f010: ldr             x16, [x16, #0x9e8]
    // 0xc0f014: r30 = Instance_Color
    //     0xc0f014: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0f018: stp             lr, x16, [SP]
    // 0xc0f01c: mov             x1, x0
    // 0xc0f020: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0f020: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc0f024: ldr             x4, [x4, #0xaa0]
    // 0xc0f028: r0 = copyWith()
    //     0xc0f028: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc0f02c: stur            x0, [fp, #-0x20]
    // 0xc0f030: r0 = Text()
    //     0xc0f030: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc0f034: mov             x2, x0
    // 0xc0f038: r0 = "Select Variant"
    //     0xc0f038: add             x0, PP, #0x52, lsl #12  ; [pp+0x52378] "Select Variant"
    //     0xc0f03c: ldr             x0, [x0, #0x378]
    // 0xc0f040: stur            x2, [fp, #-0x30]
    // 0xc0f044: StoreField: r2->field_b = r0
    //     0xc0f044: stur            w0, [x2, #0xb]
    // 0xc0f048: ldur            x0, [fp, #-0x20]
    // 0xc0f04c: StoreField: r2->field_13 = r0
    //     0xc0f04c: stur            w0, [x2, #0x13]
    // 0xc0f050: ldur            x0, [fp, #-0x10]
    // 0xc0f054: LoadField: r1 = r0->field_b
    //     0xc0f054: ldur            w1, [x0, #0xb]
    // 0xc0f058: LoadField: r3 = r0->field_f
    //     0xc0f058: ldur            w3, [x0, #0xf]
    // 0xc0f05c: DecompressPointer r3
    //     0xc0f05c: add             x3, x3, HEAP, lsl #32
    // 0xc0f060: LoadField: r4 = r3->field_b
    //     0xc0f060: ldur            w4, [x3, #0xb]
    // 0xc0f064: r3 = LoadInt32Instr(r1)
    //     0xc0f064: sbfx            x3, x1, #1, #0x1f
    // 0xc0f068: stur            x3, [fp, #-0x28]
    // 0xc0f06c: r1 = LoadInt32Instr(r4)
    //     0xc0f06c: sbfx            x1, x4, #1, #0x1f
    // 0xc0f070: cmp             x3, x1
    // 0xc0f074: b.ne            #0xc0f080
    // 0xc0f078: mov             x1, x0
    // 0xc0f07c: r0 = _growToNextCapacity()
    //     0xc0f07c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0f080: ldur            x2, [fp, #-0x10]
    // 0xc0f084: ldur            x3, [fp, #-0x28]
    // 0xc0f088: add             x4, x3, #1
    // 0xc0f08c: lsl             x0, x4, #1
    // 0xc0f090: StoreField: r2->field_b = r0
    //     0xc0f090: stur            w0, [x2, #0xb]
    // 0xc0f094: LoadField: r5 = r2->field_f
    //     0xc0f094: ldur            w5, [x2, #0xf]
    // 0xc0f098: DecompressPointer r5
    //     0xc0f098: add             x5, x5, HEAP, lsl #32
    // 0xc0f09c: mov             x1, x5
    // 0xc0f0a0: ldur            x0, [fp, #-0x30]
    // 0xc0f0a4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0f0a4: add             x25, x1, x3, lsl #2
    //     0xc0f0a8: add             x25, x25, #0xf
    //     0xc0f0ac: str             w0, [x25]
    //     0xc0f0b0: tbz             w0, #0, #0xc0f0cc
    //     0xc0f0b4: ldurb           w16, [x1, #-1]
    //     0xc0f0b8: ldurb           w17, [x0, #-1]
    //     0xc0f0bc: and             x16, x17, x16, lsr #2
    //     0xc0f0c0: tst             x16, HEAP, lsr #32
    //     0xc0f0c4: b.eq            #0xc0f0cc
    //     0xc0f0c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0f0cc: mov             x3, x4
    // 0xc0f0d0: mov             x0, x5
    // 0xc0f0d4: stur            x3, [fp, #-0x28]
    // 0xc0f0d8: LoadField: r1 = r0->field_b
    //     0xc0f0d8: ldur            w1, [x0, #0xb]
    // 0xc0f0dc: r0 = LoadInt32Instr(r1)
    //     0xc0f0dc: sbfx            x0, x1, #1, #0x1f
    // 0xc0f0e0: cmp             x3, x0
    // 0xc0f0e4: b.ne            #0xc0f0f0
    // 0xc0f0e8: mov             x1, x2
    // 0xc0f0ec: r0 = _growToNextCapacity()
    //     0xc0f0ec: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0f0f0: ldur            x5, [fp, #-8]
    // 0xc0f0f4: ldur            x4, [fp, #-0x18]
    // 0xc0f0f8: ldur            x2, [fp, #-0x10]
    // 0xc0f0fc: ldur            x3, [fp, #-0x28]
    // 0xc0f100: add             x0, x3, #1
    // 0xc0f104: lsl             x1, x0, #1
    // 0xc0f108: StoreField: r2->field_b = r1
    //     0xc0f108: stur            w1, [x2, #0xb]
    // 0xc0f10c: mov             x1, x3
    // 0xc0f110: cmp             x1, x0
    // 0xc0f114: b.hs            #0xc0f430
    // 0xc0f118: LoadField: r0 = r2->field_f
    //     0xc0f118: ldur            w0, [x2, #0xf]
    // 0xc0f11c: DecompressPointer r0
    //     0xc0f11c: add             x0, x0, HEAP, lsl #32
    // 0xc0f120: add             x1, x0, x3, lsl #2
    // 0xc0f124: r16 = Instance_SizedBox
    //     0xc0f124: add             x16, PP, #0x40, lsl #12  ; [pp+0x40328] Obj!SizedBox@d67f21
    //     0xc0f128: ldr             x16, [x16, #0x328]
    // 0xc0f12c: StoreField: r1->field_f = r16
    //     0xc0f12c: stur            w16, [x1, #0xf]
    // 0xc0f130: LoadField: r1 = r4->field_13
    //     0xc0f130: ldur            w1, [x4, #0x13]
    // 0xc0f134: DecompressPointer r1
    //     0xc0f134: add             x1, x1, HEAP, lsl #32
    // 0xc0f138: r0 = of()
    //     0xc0f138: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0f13c: LoadField: r1 = r0->field_5b
    //     0xc0f13c: ldur            w1, [x0, #0x5b]
    // 0xc0f140: DecompressPointer r1
    //     0xc0f140: add             x1, x1, HEAP, lsl #32
    // 0xc0f144: r0 = LoadClassIdInstr(r1)
    //     0xc0f144: ldur            x0, [x1, #-1]
    //     0xc0f148: ubfx            x0, x0, #0xc, #0x14
    // 0xc0f14c: d0 = 0.100000
    //     0xc0f14c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc0f150: r0 = GDT[cid_x0 + -0xffa]()
    //     0xc0f150: sub             lr, x0, #0xffa
    //     0xc0f154: ldr             lr, [x21, lr, lsl #3]
    //     0xc0f158: blr             lr
    // 0xc0f15c: r16 = 1.000000
    //     0xc0f15c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc0f160: str             x16, [SP]
    // 0xc0f164: mov             x2, x0
    // 0xc0f168: r1 = Null
    //     0xc0f168: mov             x1, NULL
    // 0xc0f16c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xc0f16c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xc0f170: ldr             x4, [x4, #0x108]
    // 0xc0f174: r0 = Border.all()
    //     0xc0f174: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc0f178: stur            x0, [fp, #-0x20]
    // 0xc0f17c: r0 = BoxDecoration()
    //     0xc0f17c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc0f180: mov             x2, x0
    // 0xc0f184: r0 = Instance_Color
    //     0xc0f184: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0f188: stur            x2, [fp, #-0x30]
    // 0xc0f18c: StoreField: r2->field_7 = r0
    //     0xc0f18c: stur            w0, [x2, #7]
    // 0xc0f190: ldur            x0, [fp, #-0x20]
    // 0xc0f194: StoreField: r2->field_f = r0
    //     0xc0f194: stur            w0, [x2, #0xf]
    // 0xc0f198: r0 = Instance_BoxShape
    //     0xc0f198: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0f19c: ldr             x0, [x0, #0x80]
    // 0xc0f1a0: StoreField: r2->field_23 = r0
    //     0xc0f1a0: stur            w0, [x2, #0x23]
    // 0xc0f1a4: ldur            x0, [fp, #-8]
    // 0xc0f1a8: LoadField: r5 = r0->field_13
    //     0xc0f1a8: ldur            w5, [x0, #0x13]
    // 0xc0f1ac: DecompressPointer r5
    //     0xc0f1ac: add             x5, x5, HEAP, lsl #32
    // 0xc0f1b0: ldur            x3, [fp, #-0x18]
    // 0xc0f1b4: stur            x5, [fp, #-0x20]
    // 0xc0f1b8: LoadField: r1 = r3->field_13
    //     0xc0f1b8: ldur            w1, [x3, #0x13]
    // 0xc0f1bc: DecompressPointer r1
    //     0xc0f1bc: add             x1, x1, HEAP, lsl #32
    // 0xc0f1c0: r0 = of()
    //     0xc0f1c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc0f1c4: LoadField: r3 = r0->field_5b
    //     0xc0f1c4: ldur            w3, [x0, #0x5b]
    // 0xc0f1c8: DecompressPointer r3
    //     0xc0f1c8: add             x3, x3, HEAP, lsl #32
    // 0xc0f1cc: ldur            x0, [fp, #-8]
    // 0xc0f1d0: stur            x3, [fp, #-0x38]
    // 0xc0f1d4: LoadField: r1 = r0->field_b
    //     0xc0f1d4: ldur            w1, [x0, #0xb]
    // 0xc0f1d8: DecompressPointer r1
    //     0xc0f1d8: add             x1, x1, HEAP, lsl #32
    // 0xc0f1dc: cmp             w1, NULL
    // 0xc0f1e0: b.eq            #0xc0f434
    // 0xc0f1e4: LoadField: r0 = r1->field_b
    //     0xc0f1e4: ldur            w0, [x1, #0xb]
    // 0xc0f1e8: DecompressPointer r0
    //     0xc0f1e8: add             x0, x0, HEAP, lsl #32
    // 0xc0f1ec: LoadField: r4 = r0->field_1b
    //     0xc0f1ec: ldur            w4, [x0, #0x1b]
    // 0xc0f1f0: DecompressPointer r4
    //     0xc0f1f0: add             x4, x4, HEAP, lsl #32
    // 0xc0f1f4: stur            x4, [fp, #-8]
    // 0xc0f1f8: cmp             w4, NULL
    // 0xc0f1fc: b.ne            #0xc0f208
    // 0xc0f200: r3 = Null
    //     0xc0f200: mov             x3, NULL
    // 0xc0f204: b               #0xc0f244
    // 0xc0f208: ldur            x2, [fp, #-0x18]
    // 0xc0f20c: r1 = Function '<anonymous closure>':.
    //     0xc0f20c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52380] AnonymousClosure: (0xb9428c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xc0ec90)
    //     0xc0f210: ldr             x1, [x1, #0x380]
    // 0xc0f214: r0 = AllocateClosure()
    //     0xc0f214: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0f218: r16 = <DropdownMenuItem<WidgetEntity>>
    //     0xc0f218: add             x16, PP, #0x52, lsl #12  ; [pp+0x52388] TypeArguments: <DropdownMenuItem<WidgetEntity>>
    //     0xc0f21c: ldr             x16, [x16, #0x388]
    // 0xc0f220: ldur            lr, [fp, #-8]
    // 0xc0f224: stp             lr, x16, [SP, #8]
    // 0xc0f228: str             x0, [SP]
    // 0xc0f22c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc0f22c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc0f230: r0 = map()
    //     0xc0f230: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xc0f234: mov             x1, x0
    // 0xc0f238: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc0f238: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc0f23c: r0 = toList()
    //     0xc0f23c: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xc0f240: mov             x3, x0
    // 0xc0f244: ldur            x0, [fp, #-0x10]
    // 0xc0f248: ldur            x2, [fp, #-0x18]
    // 0xc0f24c: stur            x3, [fp, #-8]
    // 0xc0f250: r1 = Function '<anonymous closure>':.
    //     0xc0f250: add             x1, PP, #0x52, lsl #12  ; [pp+0x52390] AnonymousClosure: (0xc0f438), in [package:customer_app/app/presentation/views/line/product_detail/widgets/size_picker.dart] _SizePickerState::build (0xc0ec90)
    //     0xc0f254: ldr             x1, [x1, #0x390]
    // 0xc0f258: r0 = AllocateClosure()
    //     0xc0f258: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc0f25c: r1 = <WidgetEntity>
    //     0xc0f25c: add             x1, PP, #0x30, lsl #12  ; [pp+0x30878] TypeArguments: <WidgetEntity>
    //     0xc0f260: ldr             x1, [x1, #0x878]
    // 0xc0f264: stur            x0, [fp, #-0x18]
    // 0xc0f268: r0 = DropdownButton()
    //     0xc0f268: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xc0f26c: stur            x0, [fp, #-0x40]
    // 0xc0f270: r16 = Instance_Color
    //     0xc0f270: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc0f274: r30 = Instance_Icon
    //     0xc0f274: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3e530] Obj!Icon@d66771
    //     0xc0f278: ldr             lr, [lr, #0x530]
    // 0xc0f27c: stp             lr, x16, [SP, #8]
    // 0xc0f280: ldur            x16, [fp, #-0x38]
    // 0xc0f284: str             x16, [SP]
    // 0xc0f288: mov             x1, x0
    // 0xc0f28c: ldur            x2, [fp, #-8]
    // 0xc0f290: ldur            x3, [fp, #-0x18]
    // 0xc0f294: ldur            x5, [fp, #-0x20]
    // 0xc0f298: r4 = const [0, 0x7, 0x3, 0x4, dropdownColor, 0x4, icon, 0x5, iconEnabledColor, 0x6, null]
    //     0xc0f298: add             x4, PP, #0x52, lsl #12  ; [pp+0x52398] List(11) [0, 0x7, 0x3, 0x4, "dropdownColor", 0x4, "icon", 0x5, "iconEnabledColor", 0x6, Null]
    //     0xc0f29c: ldr             x4, [x4, #0x398]
    // 0xc0f2a0: r0 = DropdownButton()
    //     0xc0f2a0: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xc0f2a4: r0 = DropdownButtonHideUnderline()
    //     0xc0f2a4: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xc0f2a8: mov             x1, x0
    // 0xc0f2ac: ldur            x0, [fp, #-0x40]
    // 0xc0f2b0: stur            x1, [fp, #-8]
    // 0xc0f2b4: StoreField: r1->field_b = r0
    //     0xc0f2b4: stur            w0, [x1, #0xb]
    // 0xc0f2b8: r0 = Container()
    //     0xc0f2b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0f2bc: stur            x0, [fp, #-0x18]
    // 0xc0f2c0: r16 = Instance_Alignment
    //     0xc0f2c0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc0f2c4: ldr             x16, [x16, #0xb10]
    // 0xc0f2c8: r30 = 44.000000
    //     0xc0f2c8: add             lr, PP, #0x37, lsl #12  ; [pp+0x37ad8] 44
    //     0xc0f2cc: ldr             lr, [lr, #0xad8]
    // 0xc0f2d0: stp             lr, x16, [SP, #8]
    // 0xc0f2d4: ldur            x16, [fp, #-8]
    // 0xc0f2d8: str             x16, [SP]
    // 0xc0f2dc: mov             x1, x0
    // 0xc0f2e0: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, height, 0x2, null]
    //     0xc0f2e0: add             x4, PP, #0x52, lsl #12  ; [pp+0x523a0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "height", 0x2, Null]
    //     0xc0f2e4: ldr             x4, [x4, #0x3a0]
    // 0xc0f2e8: r0 = Container()
    //     0xc0f2e8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0f2ec: r0 = Container()
    //     0xc0f2ec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0f2f0: stur            x0, [fp, #-8]
    // 0xc0f2f4: r16 = Instance_AlignmentDirectional
    //     0xc0f2f4: add             x16, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xc0f2f8: ldr             x16, [x16, #0x3a8]
    // 0xc0f2fc: ldur            lr, [fp, #-0x30]
    // 0xc0f300: stp             lr, x16, [SP, #8]
    // 0xc0f304: ldur            x16, [fp, #-0x18]
    // 0xc0f308: str             x16, [SP]
    // 0xc0f30c: mov             x1, x0
    // 0xc0f310: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, decoration, 0x2, null]
    //     0xc0f310: add             x4, PP, #0x52, lsl #12  ; [pp+0x523b0] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "decoration", 0x2, Null]
    //     0xc0f314: ldr             x4, [x4, #0x3b0]
    // 0xc0f318: r0 = Container()
    //     0xc0f318: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc0f31c: ldur            x0, [fp, #-0x10]
    // 0xc0f320: LoadField: r1 = r0->field_b
    //     0xc0f320: ldur            w1, [x0, #0xb]
    // 0xc0f324: LoadField: r2 = r0->field_f
    //     0xc0f324: ldur            w2, [x0, #0xf]
    // 0xc0f328: DecompressPointer r2
    //     0xc0f328: add             x2, x2, HEAP, lsl #32
    // 0xc0f32c: LoadField: r3 = r2->field_b
    //     0xc0f32c: ldur            w3, [x2, #0xb]
    // 0xc0f330: r2 = LoadInt32Instr(r1)
    //     0xc0f330: sbfx            x2, x1, #1, #0x1f
    // 0xc0f334: stur            x2, [fp, #-0x28]
    // 0xc0f338: r1 = LoadInt32Instr(r3)
    //     0xc0f338: sbfx            x1, x3, #1, #0x1f
    // 0xc0f33c: cmp             x2, x1
    // 0xc0f340: b.ne            #0xc0f34c
    // 0xc0f344: mov             x1, x0
    // 0xc0f348: r0 = _growToNextCapacity()
    //     0xc0f348: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc0f34c: ldur            x2, [fp, #-0x10]
    // 0xc0f350: ldur            x3, [fp, #-0x28]
    // 0xc0f354: add             x0, x3, #1
    // 0xc0f358: lsl             x1, x0, #1
    // 0xc0f35c: StoreField: r2->field_b = r1
    //     0xc0f35c: stur            w1, [x2, #0xb]
    // 0xc0f360: LoadField: r1 = r2->field_f
    //     0xc0f360: ldur            w1, [x2, #0xf]
    // 0xc0f364: DecompressPointer r1
    //     0xc0f364: add             x1, x1, HEAP, lsl #32
    // 0xc0f368: ldur            x0, [fp, #-8]
    // 0xc0f36c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc0f36c: add             x25, x1, x3, lsl #2
    //     0xc0f370: add             x25, x25, #0xf
    //     0xc0f374: str             w0, [x25]
    //     0xc0f378: tbz             w0, #0, #0xc0f394
    //     0xc0f37c: ldurb           w16, [x1, #-1]
    //     0xc0f380: ldurb           w17, [x0, #-1]
    //     0xc0f384: and             x16, x17, x16, lsr #2
    //     0xc0f388: tst             x16, HEAP, lsr #32
    //     0xc0f38c: b.eq            #0xc0f394
    //     0xc0f390: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc0f394: r0 = Column()
    //     0xc0f394: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc0f398: mov             x1, x0
    // 0xc0f39c: r0 = Instance_Axis
    //     0xc0f39c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc0f3a0: stur            x1, [fp, #-8]
    // 0xc0f3a4: StoreField: r1->field_f = r0
    //     0xc0f3a4: stur            w0, [x1, #0xf]
    // 0xc0f3a8: r0 = Instance_MainAxisAlignment
    //     0xc0f3a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc0f3ac: ldr             x0, [x0, #0xa08]
    // 0xc0f3b0: StoreField: r1->field_13 = r0
    //     0xc0f3b0: stur            w0, [x1, #0x13]
    // 0xc0f3b4: r0 = Instance_MainAxisSize
    //     0xc0f3b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc0f3b8: ldr             x0, [x0, #0xa10]
    // 0xc0f3bc: ArrayStore: r1[0] = r0  ; List_4
    //     0xc0f3bc: stur            w0, [x1, #0x17]
    // 0xc0f3c0: r0 = Instance_CrossAxisAlignment
    //     0xc0f3c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc0f3c4: ldr             x0, [x0, #0x890]
    // 0xc0f3c8: StoreField: r1->field_1b = r0
    //     0xc0f3c8: stur            w0, [x1, #0x1b]
    // 0xc0f3cc: r0 = Instance_VerticalDirection
    //     0xc0f3cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc0f3d0: ldr             x0, [x0, #0xa20]
    // 0xc0f3d4: StoreField: r1->field_23 = r0
    //     0xc0f3d4: stur            w0, [x1, #0x23]
    // 0xc0f3d8: r0 = Instance_Clip
    //     0xc0f3d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0f3dc: ldr             x0, [x0, #0x38]
    // 0xc0f3e0: StoreField: r1->field_2b = r0
    //     0xc0f3e0: stur            w0, [x1, #0x2b]
    // 0xc0f3e4: StoreField: r1->field_2f = rZR
    //     0xc0f3e4: stur            xzr, [x1, #0x2f]
    // 0xc0f3e8: ldur            x0, [fp, #-0x10]
    // 0xc0f3ec: StoreField: r1->field_b = r0
    //     0xc0f3ec: stur            w0, [x1, #0xb]
    // 0xc0f3f0: r0 = Padding()
    //     0xc0f3f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0f3f4: r1 = Instance_EdgeInsets
    //     0xc0f3f4: add             x1, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xc0f3f8: ldr             x1, [x1, #0x240]
    // 0xc0f3fc: StoreField: r0->field_f = r1
    //     0xc0f3fc: stur            w1, [x0, #0xf]
    // 0xc0f400: ldur            x1, [fp, #-8]
    // 0xc0f404: StoreField: r0->field_b = r1
    //     0xc0f404: stur            w1, [x0, #0xb]
    // 0xc0f408: LeaveFrame
    //     0xc0f408: mov             SP, fp
    //     0xc0f40c: ldp             fp, lr, [SP], #0x10
    // 0xc0f410: ret
    //     0xc0f410: ret             
    // 0xc0f414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0f414: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0f418: b               #0xc0ecb0
    // 0xc0f41c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0f41c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0f420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0f420: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0f424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0f424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0f428: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc0f428: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc0f42c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0f42c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0f430: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc0f430: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc0f434: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0f434: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, WidgetEntity?) {
    // ** addr: 0xc0f438, size: 0x174
    // 0xc0f438: EnterFrame
    //     0xc0f438: stp             fp, lr, [SP, #-0x10]!
    //     0xc0f43c: mov             fp, SP
    // 0xc0f440: AllocStack(0x28)
    //     0xc0f440: sub             SP, SP, #0x28
    // 0xc0f444: SetupParameters()
    //     0xc0f444: ldr             x0, [fp, #0x18]
    //     0xc0f448: ldur            w1, [x0, #0x17]
    //     0xc0f44c: add             x1, x1, HEAP, lsl #32
    //     0xc0f450: stur            x1, [fp, #-0x18]
    // 0xc0f454: CheckStackOverflow
    //     0xc0f454: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0f458: cmp             SP, x16
    //     0xc0f45c: b.ls            #0xc0f59c
    // 0xc0f460: LoadField: r2 = r1->field_f
    //     0xc0f460: ldur            w2, [x1, #0xf]
    // 0xc0f464: DecompressPointer r2
    //     0xc0f464: add             x2, x2, HEAP, lsl #32
    // 0xc0f468: ldr             x0, [fp, #0x10]
    // 0xc0f46c: stur            x2, [fp, #-0x10]
    // 0xc0f470: StoreField: r2->field_13 = r0
    //     0xc0f470: stur            w0, [x2, #0x13]
    //     0xc0f474: ldurb           w16, [x2, #-1]
    //     0xc0f478: ldurb           w17, [x0, #-1]
    //     0xc0f47c: and             x16, x17, x16, lsr #2
    //     0xc0f480: tst             x16, HEAP, lsr #32
    //     0xc0f484: b.eq            #0xc0f48c
    //     0xc0f488: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc0f48c: LoadField: r0 = r2->field_b
    //     0xc0f48c: ldur            w0, [x2, #0xb]
    // 0xc0f490: DecompressPointer r0
    //     0xc0f490: add             x0, x0, HEAP, lsl #32
    // 0xc0f494: cmp             w0, NULL
    // 0xc0f498: b.eq            #0xc0f5a4
    // 0xc0f49c: LoadField: r3 = r0->field_b
    //     0xc0f49c: ldur            w3, [x0, #0xb]
    // 0xc0f4a0: DecompressPointer r3
    //     0xc0f4a0: add             x3, x3, HEAP, lsl #32
    // 0xc0f4a4: LoadField: r0 = r3->field_1b
    //     0xc0f4a4: ldur            w0, [x3, #0x1b]
    // 0xc0f4a8: DecompressPointer r0
    //     0xc0f4a8: add             x0, x0, HEAP, lsl #32
    // 0xc0f4ac: stur            x0, [fp, #-8]
    // 0xc0f4b0: cmp             w0, NULL
    // 0xc0f4b4: b.ne            #0xc0f4c0
    // 0xc0f4b8: r0 = Null
    //     0xc0f4b8: mov             x0, NULL
    // 0xc0f4bc: b               #0xc0f508
    // 0xc0f4c0: ldr             x3, [fp, #0x10]
    // 0xc0f4c4: cmp             w3, NULL
    // 0xc0f4c8: b.ne            #0xc0f4d8
    // 0xc0f4cc: r0 = WidgetEntity()
    //     0xc0f4cc: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xc0f4d0: mov             x2, x0
    // 0xc0f4d4: b               #0xc0f4dc
    // 0xc0f4d8: ldr             x2, [fp, #0x10]
    // 0xc0f4dc: ldur            x1, [fp, #-8]
    // 0xc0f4e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0f4e0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc0f4e4: r0 = indexOf()
    //     0xc0f4e4: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xc0f4e8: mov             x2, x0
    // 0xc0f4ec: r0 = BoxInt64Instr(r2)
    //     0xc0f4ec: sbfiz           x0, x2, #1, #0x1f
    //     0xc0f4f0: cmp             x2, x0, asr #1
    //     0xc0f4f4: b.eq            #0xc0f500
    //     0xc0f4f8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc0f4fc: stur            x2, [x0, #7]
    // 0xc0f500: ldur            x1, [fp, #-0x18]
    // 0xc0f504: ldur            x2, [fp, #-0x10]
    // 0xc0f508: ldr             x3, [fp, #0x10]
    // 0xc0f50c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0f50c: stur            w0, [x2, #0x17]
    //     0xc0f510: tbz             w0, #0, #0xc0f52c
    //     0xc0f514: ldurb           w16, [x2, #-1]
    //     0xc0f518: ldurb           w17, [x0, #-1]
    //     0xc0f51c: and             x16, x17, x16, lsr #2
    //     0xc0f520: tst             x16, HEAP, lsr #32
    //     0xc0f524: b.eq            #0xc0f52c
    //     0xc0f528: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xc0f52c: LoadField: r0 = r1->field_f
    //     0xc0f52c: ldur            w0, [x1, #0xf]
    // 0xc0f530: DecompressPointer r0
    //     0xc0f530: add             x0, x0, HEAP, lsl #32
    // 0xc0f534: LoadField: r1 = r0->field_b
    //     0xc0f534: ldur            w1, [x0, #0xb]
    // 0xc0f538: DecompressPointer r1
    //     0xc0f538: add             x1, x1, HEAP, lsl #32
    // 0xc0f53c: cmp             w1, NULL
    // 0xc0f540: b.eq            #0xc0f5a8
    // 0xc0f544: cmp             w3, NULL
    // 0xc0f548: b.ne            #0xc0f554
    // 0xc0f54c: r0 = Null
    //     0xc0f54c: mov             x0, NULL
    // 0xc0f550: b               #0xc0f55c
    // 0xc0f554: LoadField: r0 = r3->field_37
    //     0xc0f554: ldur            w0, [x3, #0x37]
    // 0xc0f558: DecompressPointer r0
    //     0xc0f558: add             x0, x0, HEAP, lsl #32
    // 0xc0f55c: cmp             w0, NULL
    // 0xc0f560: b.ne            #0xc0f568
    // 0xc0f564: r0 = ""
    //     0xc0f564: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0f568: LoadField: r2 = r1->field_f
    //     0xc0f568: ldur            w2, [x1, #0xf]
    // 0xc0f56c: DecompressPointer r2
    //     0xc0f56c: add             x2, x2, HEAP, lsl #32
    // 0xc0f570: stp             x0, x2, [SP]
    // 0xc0f574: r4 = 0
    //     0xc0f574: movz            x4, #0
    // 0xc0f578: ldr             x0, [SP, #8]
    // 0xc0f57c: r16 = UnlinkedCall_0x613b5c
    //     0xc0f57c: add             x16, PP, #0x52, lsl #12  ; [pp+0x523b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc0f580: add             x16, x16, #0x3b8
    // 0xc0f584: ldp             x5, lr, [x16]
    // 0xc0f588: blr             lr
    // 0xc0f58c: r0 = Null
    //     0xc0f58c: mov             x0, NULL
    // 0xc0f590: LeaveFrame
    //     0xc0f590: mov             SP, fp
    //     0xc0f594: ldp             fp, lr, [SP], #0x10
    // 0xc0f598: ret
    //     0xc0f598: ret             
    // 0xc0f59c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0f59c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc0f5a0: b               #0xc0f460
    // 0xc0f5a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0f5a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0f5a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0f5a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3960, size: 0x1c, field offset: 0xc
//   const constructor, 
class SizePicker extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81358, size: 0x28
    // 0xc81358: EnterFrame
    //     0xc81358: stp             fp, lr, [SP, #-0x10]!
    //     0xc8135c: mov             fp, SP
    // 0xc81360: mov             x0, x1
    // 0xc81364: r1 = <SizePicker>
    //     0xc81364: add             x1, PP, #0x48, lsl #12  ; [pp+0x48270] TypeArguments: <SizePicker>
    //     0xc81368: ldr             x1, [x1, #0x270]
    // 0xc8136c: r0 = _SizePickerState()
    //     0xc8136c: bl              #0xc81380  ; Allocate_SizePickerStateStub -> _SizePickerState (size=0x1c)
    // 0xc81370: ArrayStore: r0[0] = rZR  ; List_4
    //     0xc81370: stur            wzr, [x0, #0x17]
    // 0xc81374: LeaveFrame
    //     0xc81374: mov             SP, fp
    //     0xc81378: ldp             fp, lr, [SP], #0x10
    // 0xc8137c: ret
    //     0xc8137c: ret             
  }
}
