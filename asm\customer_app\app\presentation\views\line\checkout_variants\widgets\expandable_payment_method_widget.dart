// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart

// class id: 1049495, size: 0x8
class :: {
}

// class id: 3268, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x78dacc, size: 0x98
    // 0x78dacc: EnterFrame
    //     0x78dacc: stp             fp, lr, [SP, #-0x10]!
    //     0x78dad0: mov             fp, SP
    // 0x78dad4: AllocStack(0x10)
    //     0x78dad4: sub             SP, SP, #0x10
    // 0x78dad8: SetupParameters(__ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x78dad8: stur            x1, [fp, #-8]
    //     0x78dadc: stur            x2, [fp, #-0x10]
    // 0x78dae0: CheckStackOverflow
    //     0x78dae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x78dae4: cmp             SP, x16
    //     0x78dae8: b.ls            #0x78db58
    // 0x78daec: r0 = Ticker()
    //     0x78daec: bl              #0x78af14  ; AllocateTickerStub -> Ticker (size=0x1c)
    // 0x78daf0: mov             x1, x0
    // 0x78daf4: r0 = false
    //     0x78daf4: add             x0, NULL, #0x30  ; false
    // 0x78daf8: StoreField: r1->field_b = r0
    //     0x78daf8: stur            w0, [x1, #0xb]
    // 0x78dafc: ldur            x0, [fp, #-0x10]
    // 0x78db00: StoreField: r1->field_13 = r0
    //     0x78db00: stur            w0, [x1, #0x13]
    // 0x78db04: mov             x0, x1
    // 0x78db08: ldur            x2, [fp, #-8]
    // 0x78db0c: StoreField: r2->field_13 = r0
    //     0x78db0c: stur            w0, [x2, #0x13]
    //     0x78db10: ldurb           w16, [x2, #-1]
    //     0x78db14: ldurb           w17, [x0, #-1]
    //     0x78db18: and             x16, x17, x16, lsr #2
    //     0x78db1c: tst             x16, HEAP, lsr #32
    //     0x78db20: b.eq            #0x78db28
    //     0x78db24: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x78db28: mov             x1, x2
    // 0x78db2c: r0 = _updateTickerModeNotifier()
    //     0x78db2c: bl              #0x78db84  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] __ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x78db30: ldur            x1, [fp, #-8]
    // 0x78db34: r0 = _updateTicker()
    //     0x78db34: bl              #0x78abc0  ; [package:flutter/src/material/input_decorator.dart] __HelperErrorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x78db38: ldur            x1, [fp, #-8]
    // 0x78db3c: LoadField: r0 = r1->field_13
    //     0x78db3c: ldur            w0, [x1, #0x13]
    // 0x78db40: DecompressPointer r0
    //     0x78db40: add             x0, x0, HEAP, lsl #32
    // 0x78db44: cmp             w0, NULL
    // 0x78db48: b.eq            #0x78db60
    // 0x78db4c: LeaveFrame
    //     0x78db4c: mov             SP, fp
    //     0x78db50: ldp             fp, lr, [SP], #0x10
    // 0x78db54: ret
    //     0x78db54: ret             
    // 0x78db58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x78db58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x78db5c: b               #0x78daec
    // 0x78db60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x78db60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x78db84, size: 0x124
    // 0x78db84: EnterFrame
    //     0x78db84: stp             fp, lr, [SP, #-0x10]!
    //     0x78db88: mov             fp, SP
    // 0x78db8c: AllocStack(0x18)
    //     0x78db8c: sub             SP, SP, #0x18
    // 0x78db90: SetupParameters(__ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x78db90: mov             x2, x1
    //     0x78db94: stur            x1, [fp, #-8]
    // 0x78db98: CheckStackOverflow
    //     0x78db98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x78db9c: cmp             SP, x16
    //     0x78dba0: b.ls            #0x78dc9c
    // 0x78dba4: LoadField: r1 = r2->field_f
    //     0x78dba4: ldur            w1, [x2, #0xf]
    // 0x78dba8: DecompressPointer r1
    //     0x78dba8: add             x1, x1, HEAP, lsl #32
    // 0x78dbac: cmp             w1, NULL
    // 0x78dbb0: b.eq            #0x78dca4
    // 0x78dbb4: r0 = getNotifier()
    //     0x78dbb4: bl              #0x78ae54  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x78dbb8: mov             x3, x0
    // 0x78dbbc: ldur            x0, [fp, #-8]
    // 0x78dbc0: stur            x3, [fp, #-0x18]
    // 0x78dbc4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x78dbc4: ldur            w4, [x0, #0x17]
    // 0x78dbc8: DecompressPointer r4
    //     0x78dbc8: add             x4, x4, HEAP, lsl #32
    // 0x78dbcc: stur            x4, [fp, #-0x10]
    // 0x78dbd0: cmp             w3, w4
    // 0x78dbd4: b.ne            #0x78dbe8
    // 0x78dbd8: r0 = Null
    //     0x78dbd8: mov             x0, NULL
    // 0x78dbdc: LeaveFrame
    //     0x78dbdc: mov             SP, fp
    //     0x78dbe0: ldp             fp, lr, [SP], #0x10
    // 0x78dbe4: ret
    //     0x78dbe4: ret             
    // 0x78dbe8: cmp             w4, NULL
    // 0x78dbec: b.eq            #0x78dc30
    // 0x78dbf0: mov             x2, x0
    // 0x78dbf4: r1 = Function '_updateTicker@356311458':.
    //     0x78dbf4: add             x1, PP, #0x54, lsl #12  ; [pp+0x544b0] AnonymousClosure: (0x78dca8), in [package:flutter/src/material/input_decorator.dart] __HelperErrorState&State&SingleTickerProviderStateMixin::_updateTicker (0x78abc0)
    //     0x78dbf8: ldr             x1, [x1, #0x4b0]
    // 0x78dbfc: r0 = AllocateClosure()
    //     0x78dbfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x78dc00: ldur            x1, [fp, #-0x10]
    // 0x78dc04: r2 = LoadClassIdInstr(r1)
    //     0x78dc04: ldur            x2, [x1, #-1]
    //     0x78dc08: ubfx            x2, x2, #0xc, #0x14
    // 0x78dc0c: mov             x16, x0
    // 0x78dc10: mov             x0, x2
    // 0x78dc14: mov             x2, x16
    // 0x78dc18: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0x78dc18: movz            x17, #0xdc2b
    //     0x78dc1c: add             lr, x0, x17
    //     0x78dc20: ldr             lr, [x21, lr, lsl #3]
    //     0x78dc24: blr             lr
    // 0x78dc28: ldur            x0, [fp, #-8]
    // 0x78dc2c: ldur            x3, [fp, #-0x18]
    // 0x78dc30: mov             x2, x0
    // 0x78dc34: r1 = Function '_updateTicker@356311458':.
    //     0x78dc34: add             x1, PP, #0x54, lsl #12  ; [pp+0x544b0] AnonymousClosure: (0x78dca8), in [package:flutter/src/material/input_decorator.dart] __HelperErrorState&State&SingleTickerProviderStateMixin::_updateTicker (0x78abc0)
    //     0x78dc38: ldr             x1, [x1, #0x4b0]
    // 0x78dc3c: r0 = AllocateClosure()
    //     0x78dc3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x78dc40: ldur            x3, [fp, #-0x18]
    // 0x78dc44: r1 = LoadClassIdInstr(r3)
    //     0x78dc44: ldur            x1, [x3, #-1]
    //     0x78dc48: ubfx            x1, x1, #0xc, #0x14
    // 0x78dc4c: mov             x2, x0
    // 0x78dc50: mov             x0, x1
    // 0x78dc54: mov             x1, x3
    // 0x78dc58: r0 = GDT[cid_x0 + 0xdc71]()
    //     0x78dc58: movz            x17, #0xdc71
    //     0x78dc5c: add             lr, x0, x17
    //     0x78dc60: ldr             lr, [x21, lr, lsl #3]
    //     0x78dc64: blr             lr
    // 0x78dc68: ldur            x0, [fp, #-0x18]
    // 0x78dc6c: ldur            x1, [fp, #-8]
    // 0x78dc70: ArrayStore: r1[0] = r0  ; List_4
    //     0x78dc70: stur            w0, [x1, #0x17]
    //     0x78dc74: ldurb           w16, [x1, #-1]
    //     0x78dc78: ldurb           w17, [x0, #-1]
    //     0x78dc7c: and             x16, x17, x16, lsr #2
    //     0x78dc80: tst             x16, HEAP, lsr #32
    //     0x78dc84: b.eq            #0x78dc8c
    //     0x78dc88: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x78dc8c: r0 = Null
    //     0x78dc8c: mov             x0, NULL
    // 0x78dc90: LeaveFrame
    //     0x78dc90: mov             SP, fp
    //     0x78dc94: ldp             fp, lr, [SP], #0x10
    // 0x78dc98: ret
    //     0x78dc98: ret             
    // 0x78dc9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x78dc9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x78dca0: b               #0x78dba4
    // 0x78dca4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x78dca4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTicker(dynamic) {
    // ** addr: 0x78dca8, size: 0x38
    // 0x78dca8: EnterFrame
    //     0x78dca8: stp             fp, lr, [SP, #-0x10]!
    //     0x78dcac: mov             fp, SP
    // 0x78dcb0: ldr             x0, [fp, #0x10]
    // 0x78dcb4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x78dcb4: ldur            w1, [x0, #0x17]
    // 0x78dcb8: DecompressPointer r1
    //     0x78dcb8: add             x1, x1, HEAP, lsl #32
    // 0x78dcbc: CheckStackOverflow
    //     0x78dcbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x78dcc0: cmp             SP, x16
    //     0x78dcc4: b.ls            #0x78dcd8
    // 0x78dcc8: r0 = _updateTicker()
    //     0x78dcc8: bl              #0x78abc0  ; [package:flutter/src/material/input_decorator.dart] __HelperErrorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x78dccc: LeaveFrame
    //     0x78dccc: mov             SP, fp
    //     0x78dcd0: ldp             fp, lr, [SP], #0x10
    // 0x78dcd4: ret
    //     0x78dcd4: ret             
    // 0x78dcd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x78dcd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x78dcdc: b               #0x78dcc8
  }
  _ activate(/* No info */) {
    // ** addr: 0x7f53dc, size: 0x48
    // 0x7f53dc: EnterFrame
    //     0x7f53dc: stp             fp, lr, [SP, #-0x10]!
    //     0x7f53e0: mov             fp, SP
    // 0x7f53e4: AllocStack(0x8)
    //     0x7f53e4: sub             SP, SP, #8
    // 0x7f53e8: SetupParameters(__ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0x7f53e8: mov             x0, x1
    //     0x7f53ec: stur            x1, [fp, #-8]
    // 0x7f53f0: CheckStackOverflow
    //     0x7f53f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f53f4: cmp             SP, x16
    //     0x7f53f8: b.ls            #0x7f541c
    // 0x7f53fc: mov             x1, x0
    // 0x7f5400: r0 = _updateTickerModeNotifier()
    //     0x7f5400: bl              #0x78db84  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] __ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x7f5404: ldur            x1, [fp, #-8]
    // 0x7f5408: r0 = _updateTicker()
    //     0x7f5408: bl              #0x78abc0  ; [package:flutter/src/material/input_decorator.dart] __HelperErrorState&State&SingleTickerProviderStateMixin::_updateTicker
    // 0x7f540c: r0 = Null
    //     0x7f540c: mov             x0, NULL
    // 0x7f5410: LeaveFrame
    //     0x7f5410: mov             SP, fp
    //     0x7f5414: ldp             fp, lr, [SP], #0x10
    // 0x7f5418: ret
    //     0x7f5418: ret             
    // 0x7f541c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f541c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5420: b               #0x7f53fc
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc880b0, size: 0x94
    // 0xc880b0: EnterFrame
    //     0xc880b0: stp             fp, lr, [SP, #-0x10]!
    //     0xc880b4: mov             fp, SP
    // 0xc880b8: AllocStack(0x10)
    //     0xc880b8: sub             SP, SP, #0x10
    // 0xc880bc: SetupParameters(__ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc880bc: mov             x0, x1
    //     0xc880c0: stur            x1, [fp, #-0x10]
    // 0xc880c4: CheckStackOverflow
    //     0xc880c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc880c8: cmp             SP, x16
    //     0xc880cc: b.ls            #0xc8813c
    // 0xc880d0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc880d0: ldur            w3, [x0, #0x17]
    // 0xc880d4: DecompressPointer r3
    //     0xc880d4: add             x3, x3, HEAP, lsl #32
    // 0xc880d8: stur            x3, [fp, #-8]
    // 0xc880dc: cmp             w3, NULL
    // 0xc880e0: b.ne            #0xc880ec
    // 0xc880e4: mov             x1, x0
    // 0xc880e8: b               #0xc88128
    // 0xc880ec: mov             x2, x0
    // 0xc880f0: r1 = Function '_updateTicker@356311458':.
    //     0xc880f0: add             x1, PP, #0x54, lsl #12  ; [pp+0x544b0] AnonymousClosure: (0x78dca8), in [package:flutter/src/material/input_decorator.dart] __HelperErrorState&State&SingleTickerProviderStateMixin::_updateTicker (0x78abc0)
    //     0xc880f4: ldr             x1, [x1, #0x4b0]
    // 0xc880f8: r0 = AllocateClosure()
    //     0xc880f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc880fc: ldur            x1, [fp, #-8]
    // 0xc88100: r2 = LoadClassIdInstr(r1)
    //     0xc88100: ldur            x2, [x1, #-1]
    //     0xc88104: ubfx            x2, x2, #0xc, #0x14
    // 0xc88108: mov             x16, x0
    // 0xc8810c: mov             x0, x2
    // 0xc88110: mov             x2, x16
    // 0xc88114: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0xc88114: movz            x17, #0xdc2b
    //     0xc88118: add             lr, x0, x17
    //     0xc8811c: ldr             lr, [x21, lr, lsl #3]
    //     0xc88120: blr             lr
    // 0xc88124: ldur            x1, [fp, #-0x10]
    // 0xc88128: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc88128: stur            NULL, [x1, #0x17]
    // 0xc8812c: r0 = Null
    //     0xc8812c: mov             x0, NULL
    // 0xc88130: LeaveFrame
    //     0xc88130: mov             SP, fp
    //     0xc88134: ldp             fp, lr, [SP], #0x10
    // 0xc88138: ret
    //     0xc88138: ret             
    // 0xc8813c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8813c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88140: b               #0xc880d0
  }
}

// class id: 3269, size: 0x34, field offset: 0x1c
class _ExpandablePaymentMethodWidgetState extends __ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin {

  late AnimationController _animationController; // offset: 0x28
  late String selectedPaymentMethod; // offset: 0x20
  late int paymentSelectedIndex; // offset: 0x24
  late String selectedPaymentMode; // offset: 0x1c

  _ didUpdateWidget(/* No info */) {
    // ** addr: 0x8062b4, size: 0x304
    // 0x8062b4: EnterFrame
    //     0x8062b4: stp             fp, lr, [SP, #-0x10]!
    //     0x8062b8: mov             fp, SP
    // 0x8062bc: AllocStack(0x30)
    //     0x8062bc: sub             SP, SP, #0x30
    // 0x8062c0: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8062c0: mov             x4, x1
    //     0x8062c4: mov             x3, x2
    //     0x8062c8: stur            x1, [fp, #-8]
    //     0x8062cc: stur            x2, [fp, #-0x10]
    // 0x8062d0: CheckStackOverflow
    //     0x8062d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8062d4: cmp             SP, x16
    //     0x8062d8: b.ls            #0x806590
    // 0x8062dc: mov             x0, x3
    // 0x8062e0: r2 = Null
    //     0x8062e0: mov             x2, NULL
    // 0x8062e4: r1 = Null
    //     0x8062e4: mov             x1, NULL
    // 0x8062e8: r4 = 60
    //     0x8062e8: movz            x4, #0x3c
    // 0x8062ec: branchIfSmi(r0, 0x8062f8)
    //     0x8062ec: tbz             w0, #0, #0x8062f8
    // 0x8062f0: r4 = LoadClassIdInstr(r0)
    //     0x8062f0: ldur            x4, [x0, #-1]
    //     0x8062f4: ubfx            x4, x4, #0xc, #0x14
    // 0x8062f8: cmp             x4, #0xfad
    // 0x8062fc: b.eq            #0x806314
    // 0x806300: r8 = ExpandablePaymentMethodWidget
    //     0x806300: add             x8, PP, #0x54, lsl #12  ; [pp+0x54650] Type: ExpandablePaymentMethodWidget
    //     0x806304: ldr             x8, [x8, #0x650]
    // 0x806308: r3 = Null
    //     0x806308: add             x3, PP, #0x54, lsl #12  ; [pp+0x54658] Null
    //     0x80630c: ldr             x3, [x3, #0x658]
    // 0x806310: r0 = ExpandablePaymentMethodWidget()
    //     0x806310: bl              #0x78db64  ; IsType_ExpandablePaymentMethodWidget_Stub
    // 0x806314: ldur            x3, [fp, #-8]
    // 0x806318: LoadField: r2 = r3->field_7
    //     0x806318: ldur            w2, [x3, #7]
    // 0x80631c: DecompressPointer r2
    //     0x80631c: add             x2, x2, HEAP, lsl #32
    // 0x806320: ldur            x0, [fp, #-0x10]
    // 0x806324: r1 = Null
    //     0x806324: mov             x1, NULL
    // 0x806328: cmp             w2, NULL
    // 0x80632c: b.eq            #0x806350
    // 0x806330: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x806330: ldur            w4, [x2, #0x17]
    // 0x806334: DecompressPointer r4
    //     0x806334: add             x4, x4, HEAP, lsl #32
    // 0x806338: r8 = X0 bound StatefulWidget
    //     0x806338: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2c7a0] TypeParameter: X0 bound StatefulWidget
    //     0x80633c: ldr             x8, [x8, #0x7a0]
    // 0x806340: LoadField: r9 = r4->field_7
    //     0x806340: ldur            x9, [x4, #7]
    // 0x806344: r3 = Null
    //     0x806344: add             x3, PP, #0x54, lsl #12  ; [pp+0x54668] Null
    //     0x806348: ldr             x3, [x3, #0x668]
    // 0x80634c: blr             x9
    // 0x806350: ldur            x1, [fp, #-0x10]
    // 0x806354: LoadField: r2 = r1->field_13
    //     0x806354: ldur            w2, [x1, #0x13]
    // 0x806358: DecompressPointer r2
    //     0x806358: add             x2, x2, HEAP, lsl #32
    // 0x80635c: ldur            x3, [fp, #-8]
    // 0x806360: stur            x2, [fp, #-0x18]
    // 0x806364: LoadField: r0 = r3->field_b
    //     0x806364: ldur            w0, [x3, #0xb]
    // 0x806368: DecompressPointer r0
    //     0x806368: add             x0, x0, HEAP, lsl #32
    // 0x80636c: cmp             w0, NULL
    // 0x806370: b.eq            #0x806598
    // 0x806374: LoadField: r4 = r0->field_13
    //     0x806374: ldur            w4, [x0, #0x13]
    // 0x806378: DecompressPointer r4
    //     0x806378: add             x4, x4, HEAP, lsl #32
    // 0x80637c: r0 = LoadClassIdInstr(r2)
    //     0x80637c: ldur            x0, [x2, #-1]
    //     0x806380: ubfx            x0, x0, #0xc, #0x14
    // 0x806384: stp             x4, x2, [SP]
    // 0x806388: mov             lr, x0
    // 0x80638c: ldr             lr, [x21, lr, lsl #3]
    // 0x806390: blr             lr
    // 0x806394: eor             x1, x0, #0x10
    // 0x806398: ldur            x2, [fp, #-0x10]
    // 0x80639c: stur            x1, [fp, #-0x20]
    // 0x8063a0: LoadField: r0 = r2->field_b
    //     0x8063a0: ldur            w0, [x2, #0xb]
    // 0x8063a4: DecompressPointer r0
    //     0x8063a4: add             x0, x0, HEAP, lsl #32
    // 0x8063a8: LoadField: r3 = r0->field_b
    //     0x8063a8: ldur            w3, [x0, #0xb]
    // 0x8063ac: DecompressPointer r3
    //     0x8063ac: add             x3, x3, HEAP, lsl #32
    // 0x8063b0: cmp             w3, NULL
    // 0x8063b4: b.eq            #0x8063c8
    // 0x8063b8: LoadField: r0 = r3->field_27
    //     0x8063b8: ldur            w0, [x3, #0x27]
    // 0x8063bc: DecompressPointer r0
    //     0x8063bc: add             x0, x0, HEAP, lsl #32
    // 0x8063c0: cmp             w0, NULL
    // 0x8063c4: b.eq            #0x8063c8
    // 0x8063c8: ldur            x3, [fp, #-8]
    // 0x8063cc: LoadField: r0 = r3->field_b
    //     0x8063cc: ldur            w0, [x3, #0xb]
    // 0x8063d0: DecompressPointer r0
    //     0x8063d0: add             x0, x0, HEAP, lsl #32
    // 0x8063d4: cmp             w0, NULL
    // 0x8063d8: b.eq            #0x80659c
    // 0x8063dc: LoadField: r4 = r0->field_b
    //     0x8063dc: ldur            w4, [x0, #0xb]
    // 0x8063e0: DecompressPointer r4
    //     0x8063e0: add             x4, x4, HEAP, lsl #32
    // 0x8063e4: LoadField: r5 = r4->field_b
    //     0x8063e4: ldur            w5, [x4, #0xb]
    // 0x8063e8: DecompressPointer r5
    //     0x8063e8: add             x5, x5, HEAP, lsl #32
    // 0x8063ec: cmp             w5, NULL
    // 0x8063f0: b.eq            #0x806404
    // 0x8063f4: LoadField: r4 = r5->field_27
    //     0x8063f4: ldur            w4, [x5, #0x27]
    // 0x8063f8: DecompressPointer r4
    //     0x8063f8: add             x4, x4, HEAP, lsl #32
    // 0x8063fc: cmp             w4, NULL
    // 0x806400: b.eq            #0x806404
    // 0x806404: ldur            x4, [fp, #-0x18]
    // 0x806408: LoadField: r5 = r0->field_13
    //     0x806408: ldur            w5, [x0, #0x13]
    // 0x80640c: DecompressPointer r5
    //     0x80640c: add             x5, x5, HEAP, lsl #32
    // 0x806410: r0 = LoadClassIdInstr(r4)
    //     0x806410: ldur            x0, [x4, #-1]
    //     0x806414: ubfx            x0, x0, #0xc, #0x14
    // 0x806418: stp             x5, x4, [SP]
    // 0x80641c: mov             lr, x0
    // 0x806420: ldr             lr, [x21, lr, lsl #3]
    // 0x806424: blr             lr
    // 0x806428: tbz             w0, #4, #0x806434
    // 0x80642c: ldur            x2, [fp, #-8]
    // 0x806430: b               #0x8064ac
    // 0x806434: ldur            x2, [fp, #-8]
    // 0x806438: ldur            x1, [fp, #-0x10]
    // 0x80643c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x80643c: ldur            w0, [x1, #0x17]
    // 0x806440: DecompressPointer r0
    //     0x806440: add             x0, x0, HEAP, lsl #32
    // 0x806444: LoadField: r3 = r2->field_b
    //     0x806444: ldur            w3, [x2, #0xb]
    // 0x806448: DecompressPointer r3
    //     0x806448: add             x3, x3, HEAP, lsl #32
    // 0x80644c: cmp             w3, NULL
    // 0x806450: b.eq            #0x8065a0
    // 0x806454: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x806454: ldur            w4, [x3, #0x17]
    // 0x806458: DecompressPointer r4
    //     0x806458: add             x4, x4, HEAP, lsl #32
    // 0x80645c: r3 = LoadClassIdInstr(r0)
    //     0x80645c: ldur            x3, [x0, #-1]
    //     0x806460: ubfx            x3, x3, #0xc, #0x14
    // 0x806464: stp             x4, x0, [SP]
    // 0x806468: mov             x0, x3
    // 0x80646c: mov             lr, x0
    // 0x806470: ldr             lr, [x21, lr, lsl #3]
    // 0x806474: blr             lr
    // 0x806478: tbz             w0, #4, #0x806484
    // 0x80647c: ldur            x2, [fp, #-8]
    // 0x806480: b               #0x8064ac
    // 0x806484: ldur            x2, [fp, #-8]
    // 0x806488: ldur            x0, [fp, #-0x10]
    // 0x80648c: LoadField: r1 = r0->field_1b
    //     0x80648c: ldur            x1, [x0, #0x1b]
    // 0x806490: LoadField: r0 = r2->field_b
    //     0x806490: ldur            w0, [x2, #0xb]
    // 0x806494: DecompressPointer r0
    //     0x806494: add             x0, x0, HEAP, lsl #32
    // 0x806498: cmp             w0, NULL
    // 0x80649c: b.eq            #0x8065a4
    // 0x8064a0: LoadField: r3 = r0->field_1b
    //     0x8064a0: ldur            x3, [x0, #0x1b]
    // 0x8064a4: cmp             x1, x3
    // 0x8064a8: b.eq            #0x80653c
    // 0x8064ac: LoadField: r1 = r2->field_b
    //     0x8064ac: ldur            w1, [x2, #0xb]
    // 0x8064b0: DecompressPointer r1
    //     0x8064b0: add             x1, x1, HEAP, lsl #32
    // 0x8064b4: cmp             w1, NULL
    // 0x8064b8: b.eq            #0x8065a8
    // 0x8064bc: LoadField: r0 = r1->field_13
    //     0x8064bc: ldur            w0, [x1, #0x13]
    // 0x8064c0: DecompressPointer r0
    //     0x8064c0: add             x0, x0, HEAP, lsl #32
    // 0x8064c4: StoreField: r2->field_1b = r0
    //     0x8064c4: stur            w0, [x2, #0x1b]
    //     0x8064c8: ldurb           w16, [x2, #-1]
    //     0x8064cc: ldurb           w17, [x0, #-1]
    //     0x8064d0: and             x16, x17, x16, lsr #2
    //     0x8064d4: tst             x16, HEAP, lsr #32
    //     0x8064d8: b.eq            #0x8064e0
    //     0x8064dc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x8064e0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8064e0: ldur            w0, [x1, #0x17]
    // 0x8064e4: DecompressPointer r0
    //     0x8064e4: add             x0, x0, HEAP, lsl #32
    // 0x8064e8: StoreField: r2->field_1f = r0
    //     0x8064e8: stur            w0, [x2, #0x1f]
    //     0x8064ec: ldurb           w16, [x2, #-1]
    //     0x8064f0: ldurb           w17, [x0, #-1]
    //     0x8064f4: and             x16, x17, x16, lsr #2
    //     0x8064f8: tst             x16, HEAP, lsr #32
    //     0x8064fc: b.eq            #0x806504
    //     0x806500: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x806504: LoadField: r3 = r1->field_1b
    //     0x806504: ldur            x3, [x1, #0x1b]
    // 0x806508: r0 = BoxInt64Instr(r3)
    //     0x806508: sbfiz           x0, x3, #1, #0x1f
    //     0x80650c: cmp             x3, x0, asr #1
    //     0x806510: b.eq            #0x80651c
    //     0x806514: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x806518: stur            x3, [x0, #7]
    // 0x80651c: StoreField: r2->field_23 = r0
    //     0x80651c: stur            w0, [x2, #0x23]
    //     0x806520: tbz             w0, #0, #0x80653c
    //     0x806524: ldurb           w16, [x2, #-1]
    //     0x806528: ldurb           w17, [x0, #-1]
    //     0x80652c: and             x16, x17, x16, lsr #2
    //     0x806530: tst             x16, HEAP, lsr #32
    //     0x806534: b.eq            #0x80653c
    //     0x806538: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x80653c: ldur            x0, [fp, #-0x20]
    // 0x806540: tbnz            w0, #4, #0x80655c
    // 0x806544: LoadField: r0 = r2->field_2f
    //     0x806544: ldur            w0, [x2, #0x2f]
    // 0x806548: DecompressPointer r0
    //     0x806548: add             x0, x0, HEAP, lsl #32
    // 0x80654c: tbz             w0, #4, #0x80655c
    // 0x806550: mov             x1, x2
    // 0x806554: r0 = _initializePaymentModes()
    //     0x806554: bl              #0x8065b8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_initializePaymentModes
    // 0x806558: b               #0x806580
    // 0x80655c: LoadField: r1 = r2->field_1b
    //     0x80655c: ldur            w1, [x2, #0x1b]
    // 0x806560: DecompressPointer r1
    //     0x806560: add             x1, x1, HEAP, lsl #32
    // 0x806564: r16 = Sentinel
    //     0x806564: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806568: cmp             w1, w16
    // 0x80656c: b.eq            #0x8065ac
    // 0x806570: LoadField: r3 = r1->field_7
    //     0x806570: ldur            w3, [x1, #7]
    // 0x806574: cbz             w3, #0x806580
    // 0x806578: r1 = true
    //     0x806578: add             x1, NULL, #0x20  ; true
    // 0x80657c: StoreField: r2->field_2f = r1
    //     0x80657c: stur            w1, [x2, #0x2f]
    // 0x806580: r0 = Null
    //     0x806580: mov             x0, NULL
    // 0x806584: LeaveFrame
    //     0x806584: mov             SP, fp
    //     0x806588: ldp             fp, lr, [SP], #0x10
    // 0x80658c: ret
    //     0x80658c: ret             
    // 0x806590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806590: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806594: b               #0x8062dc
    // 0x806598: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806598: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x80659c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80659c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8065a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8065a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8065a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8065a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8065a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8065a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8065ac: r9 = selectedPaymentMode
    //     0x8065ac: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x8065b0: ldr             x9, [x9, #0x560]
    // 0x8065b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8065b4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _initializePaymentModes(/* No info */) {
    // ** addr: 0x8065b8, size: 0x3cc
    // 0x8065b8: EnterFrame
    //     0x8065b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8065bc: mov             fp, SP
    // 0x8065c0: AllocStack(0x28)
    //     0x8065c0: sub             SP, SP, #0x28
    // 0x8065c4: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x8065c4: stur            x1, [fp, #-8]
    // 0x8065c8: CheckStackOverflow
    //     0x8065c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8065cc: cmp             SP, x16
    //     0x8065d0: b.ls            #0x806954
    // 0x8065d4: r1 = 1
    //     0x8065d4: movz            x1, #0x1
    // 0x8065d8: r0 = AllocateContext()
    //     0x8065d8: bl              #0x16f6108  ; AllocateContextStub
    // 0x8065dc: mov             x2, x0
    // 0x8065e0: ldur            x1, [fp, #-8]
    // 0x8065e4: stur            x2, [fp, #-0x10]
    // 0x8065e8: StoreField: r2->field_f = r1
    //     0x8065e8: stur            w1, [x2, #0xf]
    // 0x8065ec: LoadField: r0 = r1->field_1b
    //     0x8065ec: ldur            w0, [x1, #0x1b]
    // 0x8065f0: DecompressPointer r0
    //     0x8065f0: add             x0, x0, HEAP, lsl #32
    // 0x8065f4: r16 = Sentinel
    //     0x8065f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8065f8: cmp             w0, w16
    // 0x8065fc: b.eq            #0x80695c
    // 0x806600: LoadField: r3 = r0->field_7
    //     0x806600: ldur            w3, [x0, #7]
    // 0x806604: cbz             w3, #0x806944
    // 0x806608: r3 = true
    //     0x806608: add             x3, NULL, #0x20  ; true
    // 0x80660c: StoreField: r1->field_2f = r3
    //     0x80660c: stur            w3, [x1, #0x2f]
    // 0x806610: r3 = LoadClassIdInstr(r0)
    //     0x806610: ldur            x3, [x0, #-1]
    //     0x806614: ubfx            x3, x3, #0xc, #0x14
    // 0x806618: r16 = "online"
    //     0x806618: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0x80661c: ldr             x16, [x16, #0xa50]
    // 0x806620: stp             x16, x0, [SP]
    // 0x806624: mov             x0, x3
    // 0x806628: mov             lr, x0
    // 0x80662c: ldr             lr, [x21, lr, lsl #3]
    // 0x806630: blr             lr
    // 0x806634: tbz             w0, #4, #0x80666c
    // 0x806638: ldur            x1, [fp, #-8]
    // 0x80663c: LoadField: r0 = r1->field_1b
    //     0x80663c: ldur            w0, [x1, #0x1b]
    // 0x806640: DecompressPointer r0
    //     0x806640: add             x0, x0, HEAP, lsl #32
    // 0x806644: r2 = LoadClassIdInstr(r0)
    //     0x806644: ldur            x2, [x0, #-1]
    //     0x806648: ubfx            x2, x2, #0xc, #0x14
    // 0x80664c: r16 = "partial-cod"
    //     0x80664c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0x806650: ldr             x16, [x16, #0x830]
    // 0x806654: stp             x16, x0, [SP]
    // 0x806658: mov             x0, x2
    // 0x80665c: mov             lr, x0
    // 0x806660: ldr             lr, [x21, lr, lsl #3]
    // 0x806664: blr             lr
    // 0x806668: tbnz            w0, #4, #0x8068e4
    // 0x80666c: ldur            x3, [fp, #-8]
    // 0x806670: LoadField: r0 = r3->field_1f
    //     0x806670: ldur            w0, [x3, #0x1f]
    // 0x806674: DecompressPointer r0
    //     0x806674: add             x0, x0, HEAP, lsl #32
    // 0x806678: r16 = Sentinel
    //     0x806678: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x80667c: cmp             w0, w16
    // 0x806680: b.eq            #0x806968
    // 0x806684: LoadField: r1 = r0->field_7
    //     0x806684: ldur            w1, [x0, #7]
    // 0x806688: cbnz            w1, #0x806798
    // 0x80668c: LoadField: r0 = r3->field_b
    //     0x80668c: ldur            w0, [x3, #0xb]
    // 0x806690: DecompressPointer r0
    //     0x806690: add             x0, x0, HEAP, lsl #32
    // 0x806694: cmp             w0, NULL
    // 0x806698: b.eq            #0x806974
    // 0x80669c: LoadField: r2 = r0->field_b
    //     0x80669c: ldur            w2, [x0, #0xb]
    // 0x8066a0: DecompressPointer r2
    //     0x8066a0: add             x2, x2, HEAP, lsl #32
    // 0x8066a4: LoadField: r0 = r2->field_b
    //     0x8066a4: ldur            w0, [x2, #0xb]
    // 0x8066a8: DecompressPointer r0
    //     0x8066a8: add             x0, x0, HEAP, lsl #32
    // 0x8066ac: cmp             w0, NULL
    // 0x8066b0: b.ne            #0x8066bc
    // 0x8066b4: r2 = Null
    //     0x8066b4: mov             x2, NULL
    // 0x8066b8: b               #0x8066e8
    // 0x8066bc: LoadField: r2 = r0->field_27
    //     0x8066bc: ldur            w2, [x0, #0x27]
    // 0x8066c0: DecompressPointer r2
    //     0x8066c0: add             x2, x2, HEAP, lsl #32
    // 0x8066c4: cmp             w2, NULL
    // 0x8066c8: b.ne            #0x8066d4
    // 0x8066cc: r2 = Null
    //     0x8066cc: mov             x2, NULL
    // 0x8066d0: b               #0x8066e8
    // 0x8066d4: LoadField: r4 = r2->field_b
    //     0x8066d4: ldur            w4, [x2, #0xb]
    // 0x8066d8: cbnz            w4, #0x8066e4
    // 0x8066dc: r2 = false
    //     0x8066dc: add             x2, NULL, #0x30  ; false
    // 0x8066e0: b               #0x8066e8
    // 0x8066e4: r2 = true
    //     0x8066e4: add             x2, NULL, #0x20  ; true
    // 0x8066e8: cmp             w2, NULL
    // 0x8066ec: b.eq            #0x806798
    // 0x8066f0: tbnz            w2, #4, #0x806798
    // 0x8066f4: cmp             w0, NULL
    // 0x8066f8: b.ne            #0x806704
    // 0x8066fc: r0 = Null
    //     0x8066fc: mov             x0, NULL
    // 0x806700: b               #0x80674c
    // 0x806704: LoadField: r2 = r0->field_27
    //     0x806704: ldur            w2, [x0, #0x27]
    // 0x806708: DecompressPointer r2
    //     0x806708: add             x2, x2, HEAP, lsl #32
    // 0x80670c: cmp             w2, NULL
    // 0x806710: b.ne            #0x80671c
    // 0x806714: r0 = Null
    //     0x806714: mov             x0, NULL
    // 0x806718: b               #0x80674c
    // 0x80671c: LoadField: r0 = r2->field_b
    //     0x80671c: ldur            w0, [x2, #0xb]
    // 0x806720: r1 = LoadInt32Instr(r0)
    //     0x806720: sbfx            x1, x0, #1, #0x1f
    // 0x806724: mov             x0, x1
    // 0x806728: r1 = 0
    //     0x806728: movz            x1, #0
    // 0x80672c: cmp             x1, x0
    // 0x806730: b.hs            #0x806978
    // 0x806734: LoadField: r0 = r2->field_f
    //     0x806734: ldur            w0, [x2, #0xf]
    // 0x806738: DecompressPointer r0
    //     0x806738: add             x0, x0, HEAP, lsl #32
    // 0x80673c: LoadField: r1 = r0->field_f
    //     0x80673c: ldur            w1, [x0, #0xf]
    // 0x806740: DecompressPointer r1
    //     0x806740: add             x1, x1, HEAP, lsl #32
    // 0x806744: LoadField: r0 = r1->field_f
    //     0x806744: ldur            w0, [x1, #0xf]
    // 0x806748: DecompressPointer r0
    //     0x806748: add             x0, x0, HEAP, lsl #32
    // 0x80674c: cmp             w0, NULL
    // 0x806750: b.ne            #0x806758
    // 0x806754: r0 = ""
    //     0x806754: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x806758: StoreField: r3->field_1f = r0
    //     0x806758: stur            w0, [x3, #0x1f]
    //     0x80675c: ldurb           w16, [x3, #-1]
    //     0x806760: ldurb           w17, [x0, #-1]
    //     0x806764: and             x16, x17, x16, lsr #2
    //     0x806768: tst             x16, HEAP, lsr #32
    //     0x80676c: b.eq            #0x806774
    //     0x806770: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x806774: StoreField: r3->field_23 = rZR
    //     0x806774: stur            wzr, [x3, #0x23]
    // 0x806778: ldur            x2, [fp, #-0x10]
    // 0x80677c: r1 = Function '<anonymous closure>':.
    //     0x80677c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54678] AnonymousClosure: (0x806ec4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_initializePaymentModes (0x8065b8)
    //     0x806780: ldr             x1, [x1, #0x678]
    // 0x806784: r0 = AllocateClosure()
    //     0x806784: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x806788: mov             x2, x0
    // 0x80678c: r1 = <Null?>
    //     0x80678c: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x806790: r0 = Future.microtask()
    //     0x806790: bl              #0x801434  ; [dart:async] Future::Future.microtask
    // 0x806794: b               #0x806944
    // 0x806798: cbz             w1, #0x806944
    // 0x80679c: LoadField: r0 = r3->field_b
    //     0x80679c: ldur            w0, [x3, #0xb]
    // 0x8067a0: DecompressPointer r0
    //     0x8067a0: add             x0, x0, HEAP, lsl #32
    // 0x8067a4: cmp             w0, NULL
    // 0x8067a8: b.eq            #0x80697c
    // 0x8067ac: LoadField: r1 = r0->field_b
    //     0x8067ac: ldur            w1, [x0, #0xb]
    // 0x8067b0: DecompressPointer r1
    //     0x8067b0: add             x1, x1, HEAP, lsl #32
    // 0x8067b4: LoadField: r0 = r1->field_b
    //     0x8067b4: ldur            w0, [x1, #0xb]
    // 0x8067b8: DecompressPointer r0
    //     0x8067b8: add             x0, x0, HEAP, lsl #32
    // 0x8067bc: cmp             w0, NULL
    // 0x8067c0: b.ne            #0x8067cc
    // 0x8067c4: r0 = Null
    //     0x8067c4: mov             x0, NULL
    // 0x8067c8: b               #0x8067d8
    // 0x8067cc: LoadField: r1 = r0->field_27
    //     0x8067cc: ldur            w1, [x0, #0x27]
    // 0x8067d0: DecompressPointer r1
    //     0x8067d0: add             x1, x1, HEAP, lsl #32
    // 0x8067d4: mov             x0, x1
    // 0x8067d8: cmp             w0, NULL
    // 0x8067dc: b.ne            #0x8067f0
    // 0x8067e0: r1 = <PaymentOptions>
    //     0x8067e0: add             x1, PP, #0x22, lsl #12  ; [pp+0x225a8] TypeArguments: <PaymentOptions>
    //     0x8067e4: ldr             x1, [x1, #0x5a8]
    // 0x8067e8: r2 = 0
    //     0x8067e8: movz            x2, #0
    // 0x8067ec: r0 = _GrowableList()
    //     0x8067ec: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x8067f0: ldur            x2, [fp, #-0x10]
    // 0x8067f4: stur            x0, [fp, #-0x18]
    // 0x8067f8: r1 = Function '<anonymous closure>':.
    //     0x8067f8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54680] AnonymousClosure: (0x806d3c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_initializePaymentModes (0x8065b8)
    //     0x8067fc: ldr             x1, [x1, #0x680]
    // 0x806800: r0 = AllocateClosure()
    //     0x806800: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x806804: ldur            x1, [fp, #-0x18]
    // 0x806808: mov             x2, x0
    // 0x80680c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x80680c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x806810: r0 = indexWhere()
    //     0x806810: bl              #0x806984  ; [dart:collection] ListBase::indexWhere
    // 0x806814: mov             x2, x0
    // 0x806818: cmn             x2, #1
    // 0x80681c: b.eq            #0x80685c
    // 0x806820: ldur            x3, [fp, #-8]
    // 0x806824: r0 = BoxInt64Instr(r2)
    //     0x806824: sbfiz           x0, x2, #1, #0x1f
    //     0x806828: cmp             x2, x0, asr #1
    //     0x80682c: b.eq            #0x806838
    //     0x806830: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x806834: stur            x2, [x0, #7]
    // 0x806838: StoreField: r3->field_23 = r0
    //     0x806838: stur            w0, [x3, #0x23]
    //     0x80683c: tbz             w0, #0, #0x806858
    //     0x806840: ldurb           w16, [x3, #-1]
    //     0x806844: ldurb           w17, [x0, #-1]
    //     0x806848: and             x16, x17, x16, lsr #2
    //     0x80684c: tst             x16, HEAP, lsr #32
    //     0x806850: b.eq            #0x806858
    //     0x806854: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x806858: b               #0x806944
    // 0x80685c: ldur            x3, [fp, #-8]
    // 0x806860: ldur            x2, [fp, #-0x18]
    // 0x806864: LoadField: r0 = r2->field_b
    //     0x806864: ldur            w0, [x2, #0xb]
    // 0x806868: r1 = LoadInt32Instr(r0)
    //     0x806868: sbfx            x1, x0, #1, #0x1f
    // 0x80686c: cbz             x1, #0x806944
    // 0x806870: mov             x0, x1
    // 0x806874: r1 = 0
    //     0x806874: movz            x1, #0
    // 0x806878: cmp             x1, x0
    // 0x80687c: b.hs            #0x806980
    // 0x806880: LoadField: r0 = r2->field_f
    //     0x806880: ldur            w0, [x2, #0xf]
    // 0x806884: DecompressPointer r0
    //     0x806884: add             x0, x0, HEAP, lsl #32
    // 0x806888: LoadField: r1 = r0->field_f
    //     0x806888: ldur            w1, [x0, #0xf]
    // 0x80688c: DecompressPointer r1
    //     0x80688c: add             x1, x1, HEAP, lsl #32
    // 0x806890: LoadField: r0 = r1->field_f
    //     0x806890: ldur            w0, [x1, #0xf]
    // 0x806894: DecompressPointer r0
    //     0x806894: add             x0, x0, HEAP, lsl #32
    // 0x806898: cmp             w0, NULL
    // 0x80689c: b.ne            #0x8068a4
    // 0x8068a0: r0 = ""
    //     0x8068a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8068a4: StoreField: r3->field_1f = r0
    //     0x8068a4: stur            w0, [x3, #0x1f]
    //     0x8068a8: ldurb           w16, [x3, #-1]
    //     0x8068ac: ldurb           w17, [x0, #-1]
    //     0x8068b0: and             x16, x17, x16, lsr #2
    //     0x8068b4: tst             x16, HEAP, lsr #32
    //     0x8068b8: b.eq            #0x8068c0
    //     0x8068bc: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x8068c0: StoreField: r3->field_23 = rZR
    //     0x8068c0: stur            wzr, [x3, #0x23]
    // 0x8068c4: ldur            x2, [fp, #-0x10]
    // 0x8068c8: r1 = Function '<anonymous closure>':.
    //     0x8068c8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54688] AnonymousClosure: (0x806bc8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_initializePaymentModes (0x8065b8)
    //     0x8068cc: ldr             x1, [x1, #0x688]
    // 0x8068d0: r0 = AllocateClosure()
    //     0x8068d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8068d4: mov             x2, x0
    // 0x8068d8: r1 = <Null?>
    //     0x8068d8: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x8068dc: r0 = Future.microtask()
    //     0x8068dc: bl              #0x801434  ; [dart:async] Future::Future.microtask
    // 0x8068e0: b               #0x806944
    // 0x8068e4: ldur            x3, [fp, #-8]
    // 0x8068e8: LoadField: r0 = r3->field_1b
    //     0x8068e8: ldur            w0, [x3, #0x1b]
    // 0x8068ec: DecompressPointer r0
    //     0x8068ec: add             x0, x0, HEAP, lsl #32
    // 0x8068f0: r1 = LoadClassIdInstr(r0)
    //     0x8068f0: ldur            x1, [x0, #-1]
    //     0x8068f4: ubfx            x1, x1, #0xc, #0x14
    // 0x8068f8: r16 = "cod"
    //     0x8068f8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0x8068fc: ldr             x16, [x16, #0xa28]
    // 0x806900: stp             x16, x0, [SP]
    // 0x806904: mov             x0, x1
    // 0x806908: mov             lr, x0
    // 0x80690c: ldr             lr, [x21, lr, lsl #3]
    // 0x806910: blr             lr
    // 0x806914: tbnz            w0, #4, #0x806944
    // 0x806918: ldur            x0, [fp, #-8]
    // 0x80691c: r1 = ""
    //     0x80691c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x806920: StoreField: r0->field_1f = r1
    //     0x806920: stur            w1, [x0, #0x1f]
    // 0x806924: StoreField: r0->field_23 = rZR
    //     0x806924: stur            wzr, [x0, #0x23]
    // 0x806928: ldur            x2, [fp, #-0x10]
    // 0x80692c: r1 = Function '<anonymous closure>':.
    //     0x80692c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54690] AnonymousClosure: (0x806a8c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_initializePaymentModes (0x8065b8)
    //     0x806930: ldr             x1, [x1, #0x690]
    // 0x806934: r0 = AllocateClosure()
    //     0x806934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x806938: mov             x2, x0
    // 0x80693c: r1 = <Null?>
    //     0x80693c: ldr             x1, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x806940: r0 = Future.microtask()
    //     0x806940: bl              #0x801434  ; [dart:async] Future::Future.microtask
    // 0x806944: r0 = Null
    //     0x806944: mov             x0, NULL
    // 0x806948: LeaveFrame
    //     0x806948: mov             SP, fp
    //     0x80694c: ldp             fp, lr, [SP], #0x10
    // 0x806950: ret
    //     0x806950: ret             
    // 0x806954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806954: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806958: b               #0x8065d4
    // 0x80695c: r9 = selectedPaymentMode
    //     0x80695c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x806960: ldr             x9, [x9, #0x560]
    // 0x806964: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806964: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806968: r9 = selectedPaymentMethod
    //     0x806968: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0x80696c: ldr             x9, [x9, #0x550]
    // 0x806970: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806970: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806974: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806974: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806978: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x806978: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x80697c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80697c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806980: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x806980: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x806a8c, size: 0x13c
    // 0x806a8c: EnterFrame
    //     0x806a8c: stp             fp, lr, [SP, #-0x10]!
    //     0x806a90: mov             fp, SP
    // 0x806a94: AllocStack(0x30)
    //     0x806a94: sub             SP, SP, #0x30
    // 0x806a98: SetupParameters()
    //     0x806a98: ldr             x0, [fp, #0x10]
    //     0x806a9c: ldur            w1, [x0, #0x17]
    //     0x806aa0: add             x1, x1, HEAP, lsl #32
    //     0x806aa4: stur            x1, [fp, #-8]
    // 0x806aa8: CheckStackOverflow
    //     0x806aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806aac: cmp             SP, x16
    //     0x806ab0: b.ls            #0x806b94
    // 0x806ab4: LoadField: r0 = r1->field_f
    //     0x806ab4: ldur            w0, [x1, #0xf]
    // 0x806ab8: DecompressPointer r0
    //     0x806ab8: add             x0, x0, HEAP, lsl #32
    // 0x806abc: LoadField: r2 = r0->field_b
    //     0x806abc: ldur            w2, [x0, #0xb]
    // 0x806ac0: DecompressPointer r2
    //     0x806ac0: add             x2, x2, HEAP, lsl #32
    // 0x806ac4: cmp             w2, NULL
    // 0x806ac8: b.eq            #0x806b9c
    // 0x806acc: LoadField: r3 = r0->field_1b
    //     0x806acc: ldur            w3, [x0, #0x1b]
    // 0x806ad0: DecompressPointer r3
    //     0x806ad0: add             x3, x3, HEAP, lsl #32
    // 0x806ad4: r16 = Sentinel
    //     0x806ad4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806ad8: cmp             w3, w16
    // 0x806adc: b.eq            #0x806ba0
    // 0x806ae0: LoadField: r4 = r0->field_23
    //     0x806ae0: ldur            w4, [x0, #0x23]
    // 0x806ae4: DecompressPointer r4
    //     0x806ae4: add             x4, x4, HEAP, lsl #32
    // 0x806ae8: r16 = Sentinel
    //     0x806ae8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806aec: cmp             w4, w16
    // 0x806af0: b.eq            #0x806bac
    // 0x806af4: LoadField: r0 = r2->field_f
    //     0x806af4: ldur            w0, [x2, #0xf]
    // 0x806af8: DecompressPointer r0
    //     0x806af8: add             x0, x0, HEAP, lsl #32
    // 0x806afc: stp             x3, x0, [SP, #0x18]
    // 0x806b00: r16 = ""
    //     0x806b00: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x806b04: stp             x4, x16, [SP, #8]
    // 0x806b08: r16 = true
    //     0x806b08: add             x16, NULL, #0x20  ; true
    // 0x806b0c: str             x16, [SP]
    // 0x806b10: r4 = 0
    //     0x806b10: movz            x4, #0
    // 0x806b14: ldr             x0, [SP, #0x20]
    // 0x806b18: r16 = UnlinkedCall_0x613b5c
    //     0x806b18: add             x16, PP, #0x54, lsl #12  ; [pp+0x54698] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x806b1c: add             x16, x16, #0x698
    // 0x806b20: ldp             x5, lr, [x16]
    // 0x806b24: blr             lr
    // 0x806b28: ldur            x0, [fp, #-8]
    // 0x806b2c: LoadField: r1 = r0->field_f
    //     0x806b2c: ldur            w1, [x0, #0xf]
    // 0x806b30: DecompressPointer r1
    //     0x806b30: add             x1, x1, HEAP, lsl #32
    // 0x806b34: LoadField: r0 = r1->field_b
    //     0x806b34: ldur            w0, [x1, #0xb]
    // 0x806b38: DecompressPointer r0
    //     0x806b38: add             x0, x0, HEAP, lsl #32
    // 0x806b3c: cmp             w0, NULL
    // 0x806b40: b.eq            #0x806bb8
    // 0x806b44: LoadField: r2 = r1->field_1b
    //     0x806b44: ldur            w2, [x1, #0x1b]
    // 0x806b48: DecompressPointer r2
    //     0x806b48: add             x2, x2, HEAP, lsl #32
    // 0x806b4c: r16 = Sentinel
    //     0x806b4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806b50: cmp             w2, w16
    // 0x806b54: b.eq            #0x806bbc
    // 0x806b58: LoadField: r1 = r0->field_23
    //     0x806b58: ldur            w1, [x0, #0x23]
    // 0x806b5c: DecompressPointer r1
    //     0x806b5c: add             x1, x1, HEAP, lsl #32
    // 0x806b60: stp             x2, x1, [SP, #8]
    // 0x806b64: r16 = ""
    //     0x806b64: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x806b68: str             x16, [SP]
    // 0x806b6c: r4 = 0
    //     0x806b6c: movz            x4, #0
    // 0x806b70: ldr             x0, [SP, #0x10]
    // 0x806b74: r16 = UnlinkedCall_0x613b5c
    //     0x806b74: add             x16, PP, #0x54, lsl #12  ; [pp+0x546a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x806b78: add             x16, x16, #0x6a8
    // 0x806b7c: ldp             x5, lr, [x16]
    // 0x806b80: blr             lr
    // 0x806b84: r0 = Null
    //     0x806b84: mov             x0, NULL
    // 0x806b88: LeaveFrame
    //     0x806b88: mov             SP, fp
    //     0x806b8c: ldp             fp, lr, [SP], #0x10
    // 0x806b90: ret
    //     0x806b90: ret             
    // 0x806b94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806b94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806b98: b               #0x806ab4
    // 0x806b9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806b9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806ba0: r9 = selectedPaymentMode
    //     0x806ba0: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x806ba4: ldr             x9, [x9, #0x560]
    // 0x806ba8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806ba8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806bac: r9 = paymentSelectedIndex
    //     0x806bac: add             x9, PP, #0x54, lsl #12  ; [pp+0x54558] Field <<EMAIL>>: late (offset: 0x24)
    //     0x806bb0: ldr             x9, [x9, #0x558]
    // 0x806bb4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806bb4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806bb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806bb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806bbc: r9 = selectedPaymentMode
    //     0x806bbc: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x806bc0: ldr             x9, [x9, #0x560]
    // 0x806bc4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806bc4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x806bc8, size: 0x174
    // 0x806bc8: EnterFrame
    //     0x806bc8: stp             fp, lr, [SP, #-0x10]!
    //     0x806bcc: mov             fp, SP
    // 0x806bd0: AllocStack(0x30)
    //     0x806bd0: sub             SP, SP, #0x30
    // 0x806bd4: SetupParameters()
    //     0x806bd4: ldr             x0, [fp, #0x10]
    //     0x806bd8: ldur            w1, [x0, #0x17]
    //     0x806bdc: add             x1, x1, HEAP, lsl #32
    //     0x806be0: stur            x1, [fp, #-8]
    // 0x806be4: CheckStackOverflow
    //     0x806be4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806be8: cmp             SP, x16
    //     0x806bec: b.ls            #0x806cf0
    // 0x806bf0: LoadField: r0 = r1->field_f
    //     0x806bf0: ldur            w0, [x1, #0xf]
    // 0x806bf4: DecompressPointer r0
    //     0x806bf4: add             x0, x0, HEAP, lsl #32
    // 0x806bf8: LoadField: r2 = r0->field_b
    //     0x806bf8: ldur            w2, [x0, #0xb]
    // 0x806bfc: DecompressPointer r2
    //     0x806bfc: add             x2, x2, HEAP, lsl #32
    // 0x806c00: cmp             w2, NULL
    // 0x806c04: b.eq            #0x806cf8
    // 0x806c08: LoadField: r3 = r0->field_1b
    //     0x806c08: ldur            w3, [x0, #0x1b]
    // 0x806c0c: DecompressPointer r3
    //     0x806c0c: add             x3, x3, HEAP, lsl #32
    // 0x806c10: r16 = Sentinel
    //     0x806c10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806c14: cmp             w3, w16
    // 0x806c18: b.eq            #0x806cfc
    // 0x806c1c: LoadField: r4 = r0->field_1f
    //     0x806c1c: ldur            w4, [x0, #0x1f]
    // 0x806c20: DecompressPointer r4
    //     0x806c20: add             x4, x4, HEAP, lsl #32
    // 0x806c24: r16 = Sentinel
    //     0x806c24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806c28: cmp             w4, w16
    // 0x806c2c: b.eq            #0x806d08
    // 0x806c30: LoadField: r5 = r0->field_23
    //     0x806c30: ldur            w5, [x0, #0x23]
    // 0x806c34: DecompressPointer r5
    //     0x806c34: add             x5, x5, HEAP, lsl #32
    // 0x806c38: r16 = Sentinel
    //     0x806c38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806c3c: cmp             w5, w16
    // 0x806c40: b.eq            #0x806d14
    // 0x806c44: LoadField: r0 = r2->field_f
    //     0x806c44: ldur            w0, [x2, #0xf]
    // 0x806c48: DecompressPointer r0
    //     0x806c48: add             x0, x0, HEAP, lsl #32
    // 0x806c4c: stp             x3, x0, [SP, #0x18]
    // 0x806c50: stp             x5, x4, [SP, #8]
    // 0x806c54: r16 = true
    //     0x806c54: add             x16, NULL, #0x20  ; true
    // 0x806c58: str             x16, [SP]
    // 0x806c5c: r4 = 0
    //     0x806c5c: movz            x4, #0
    // 0x806c60: ldr             x0, [SP, #0x20]
    // 0x806c64: r16 = UnlinkedCall_0x613b5c
    //     0x806c64: add             x16, PP, #0x54, lsl #12  ; [pp+0x546b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x806c68: add             x16, x16, #0x6b8
    // 0x806c6c: ldp             x5, lr, [x16]
    // 0x806c70: blr             lr
    // 0x806c74: ldur            x0, [fp, #-8]
    // 0x806c78: LoadField: r1 = r0->field_f
    //     0x806c78: ldur            w1, [x0, #0xf]
    // 0x806c7c: DecompressPointer r1
    //     0x806c7c: add             x1, x1, HEAP, lsl #32
    // 0x806c80: LoadField: r0 = r1->field_b
    //     0x806c80: ldur            w0, [x1, #0xb]
    // 0x806c84: DecompressPointer r0
    //     0x806c84: add             x0, x0, HEAP, lsl #32
    // 0x806c88: cmp             w0, NULL
    // 0x806c8c: b.eq            #0x806d20
    // 0x806c90: LoadField: r2 = r1->field_1b
    //     0x806c90: ldur            w2, [x1, #0x1b]
    // 0x806c94: DecompressPointer r2
    //     0x806c94: add             x2, x2, HEAP, lsl #32
    // 0x806c98: r16 = Sentinel
    //     0x806c98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806c9c: cmp             w2, w16
    // 0x806ca0: b.eq            #0x806d24
    // 0x806ca4: LoadField: r3 = r1->field_1f
    //     0x806ca4: ldur            w3, [x1, #0x1f]
    // 0x806ca8: DecompressPointer r3
    //     0x806ca8: add             x3, x3, HEAP, lsl #32
    // 0x806cac: r16 = Sentinel
    //     0x806cac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806cb0: cmp             w3, w16
    // 0x806cb4: b.eq            #0x806d30
    // 0x806cb8: LoadField: r1 = r0->field_23
    //     0x806cb8: ldur            w1, [x0, #0x23]
    // 0x806cbc: DecompressPointer r1
    //     0x806cbc: add             x1, x1, HEAP, lsl #32
    // 0x806cc0: stp             x2, x1, [SP, #8]
    // 0x806cc4: str             x3, [SP]
    // 0x806cc8: r4 = 0
    //     0x806cc8: movz            x4, #0
    // 0x806ccc: ldr             x0, [SP, #0x10]
    // 0x806cd0: r16 = UnlinkedCall_0x613b5c
    //     0x806cd0: add             x16, PP, #0x54, lsl #12  ; [pp+0x546c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x806cd4: add             x16, x16, #0x6c8
    // 0x806cd8: ldp             x5, lr, [x16]
    // 0x806cdc: blr             lr
    // 0x806ce0: r0 = Null
    //     0x806ce0: mov             x0, NULL
    // 0x806ce4: LeaveFrame
    //     0x806ce4: mov             SP, fp
    //     0x806ce8: ldp             fp, lr, [SP], #0x10
    // 0x806cec: ret
    //     0x806cec: ret             
    // 0x806cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806cf0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806cf4: b               #0x806bf0
    // 0x806cf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806cf8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806cfc: r9 = selectedPaymentMode
    //     0x806cfc: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x806d00: ldr             x9, [x9, #0x560]
    // 0x806d04: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806d04: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806d08: r9 = selectedPaymentMethod
    //     0x806d08: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0x806d0c: ldr             x9, [x9, #0x550]
    // 0x806d10: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806d10: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806d14: r9 = paymentSelectedIndex
    //     0x806d14: add             x9, PP, #0x54, lsl #12  ; [pp+0x54558] Field <<EMAIL>>: late (offset: 0x24)
    //     0x806d18: ldr             x9, [x9, #0x558]
    // 0x806d1c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806d1c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806d20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806d20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806d24: r9 = selectedPaymentMode
    //     0x806d24: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x806d28: ldr             x9, [x9, #0x560]
    // 0x806d2c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806d2c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x806d30: r9 = selectedPaymentMethod
    //     0x806d30: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0x806d34: ldr             x9, [x9, #0x550]
    // 0x806d38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806d38: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, PaymentOptions) {
    // ** addr: 0x806d3c, size: 0x84
    // 0x806d3c: EnterFrame
    //     0x806d3c: stp             fp, lr, [SP, #-0x10]!
    //     0x806d40: mov             fp, SP
    // 0x806d44: AllocStack(0x10)
    //     0x806d44: sub             SP, SP, #0x10
    // 0x806d48: SetupParameters()
    //     0x806d48: ldr             x0, [fp, #0x18]
    //     0x806d4c: ldur            w1, [x0, #0x17]
    //     0x806d50: add             x1, x1, HEAP, lsl #32
    // 0x806d54: CheckStackOverflow
    //     0x806d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806d58: cmp             SP, x16
    //     0x806d5c: b.ls            #0x806dac
    // 0x806d60: ldr             x0, [fp, #0x10]
    // 0x806d64: LoadField: r2 = r0->field_f
    //     0x806d64: ldur            w2, [x0, #0xf]
    // 0x806d68: DecompressPointer r2
    //     0x806d68: add             x2, x2, HEAP, lsl #32
    // 0x806d6c: LoadField: r0 = r1->field_f
    //     0x806d6c: ldur            w0, [x1, #0xf]
    // 0x806d70: DecompressPointer r0
    //     0x806d70: add             x0, x0, HEAP, lsl #32
    // 0x806d74: LoadField: r1 = r0->field_1f
    //     0x806d74: ldur            w1, [x0, #0x1f]
    // 0x806d78: DecompressPointer r1
    //     0x806d78: add             x1, x1, HEAP, lsl #32
    // 0x806d7c: r16 = Sentinel
    //     0x806d7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806d80: cmp             w1, w16
    // 0x806d84: b.eq            #0x806db4
    // 0x806d88: r0 = LoadClassIdInstr(r2)
    //     0x806d88: ldur            x0, [x2, #-1]
    //     0x806d8c: ubfx            x0, x0, #0xc, #0x14
    // 0x806d90: stp             x1, x2, [SP]
    // 0x806d94: mov             lr, x0
    // 0x806d98: ldr             lr, [x21, lr, lsl #3]
    // 0x806d9c: blr             lr
    // 0x806da0: LeaveFrame
    //     0x806da0: mov             SP, fp
    //     0x806da4: ldp             fp, lr, [SP], #0x10
    // 0x806da8: ret
    //     0x806da8: ret             
    // 0x806dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806dac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806db0: b               #0x806d60
    // 0x806db4: r9 = selectedPaymentMethod
    //     0x806db4: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0x806db8: ldr             x9, [x9, #0x550]
    // 0x806dbc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x806dbc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x806ec4, size: 0x174
    // 0x806ec4: EnterFrame
    //     0x806ec4: stp             fp, lr, [SP, #-0x10]!
    //     0x806ec8: mov             fp, SP
    // 0x806ecc: AllocStack(0x30)
    //     0x806ecc: sub             SP, SP, #0x30
    // 0x806ed0: SetupParameters()
    //     0x806ed0: ldr             x0, [fp, #0x10]
    //     0x806ed4: ldur            w1, [x0, #0x17]
    //     0x806ed8: add             x1, x1, HEAP, lsl #32
    //     0x806edc: stur            x1, [fp, #-8]
    // 0x806ee0: CheckStackOverflow
    //     0x806ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x806ee4: cmp             SP, x16
    //     0x806ee8: b.ls            #0x806fec
    // 0x806eec: LoadField: r0 = r1->field_f
    //     0x806eec: ldur            w0, [x1, #0xf]
    // 0x806ef0: DecompressPointer r0
    //     0x806ef0: add             x0, x0, HEAP, lsl #32
    // 0x806ef4: LoadField: r2 = r0->field_b
    //     0x806ef4: ldur            w2, [x0, #0xb]
    // 0x806ef8: DecompressPointer r2
    //     0x806ef8: add             x2, x2, HEAP, lsl #32
    // 0x806efc: cmp             w2, NULL
    // 0x806f00: b.eq            #0x806ff4
    // 0x806f04: LoadField: r3 = r0->field_1b
    //     0x806f04: ldur            w3, [x0, #0x1b]
    // 0x806f08: DecompressPointer r3
    //     0x806f08: add             x3, x3, HEAP, lsl #32
    // 0x806f0c: r16 = Sentinel
    //     0x806f0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806f10: cmp             w3, w16
    // 0x806f14: b.eq            #0x806ff8
    // 0x806f18: LoadField: r4 = r0->field_1f
    //     0x806f18: ldur            w4, [x0, #0x1f]
    // 0x806f1c: DecompressPointer r4
    //     0x806f1c: add             x4, x4, HEAP, lsl #32
    // 0x806f20: r16 = Sentinel
    //     0x806f20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806f24: cmp             w4, w16
    // 0x806f28: b.eq            #0x807004
    // 0x806f2c: LoadField: r5 = r0->field_23
    //     0x806f2c: ldur            w5, [x0, #0x23]
    // 0x806f30: DecompressPointer r5
    //     0x806f30: add             x5, x5, HEAP, lsl #32
    // 0x806f34: r16 = Sentinel
    //     0x806f34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806f38: cmp             w5, w16
    // 0x806f3c: b.eq            #0x807010
    // 0x806f40: LoadField: r0 = r2->field_f
    //     0x806f40: ldur            w0, [x2, #0xf]
    // 0x806f44: DecompressPointer r0
    //     0x806f44: add             x0, x0, HEAP, lsl #32
    // 0x806f48: stp             x3, x0, [SP, #0x18]
    // 0x806f4c: stp             x5, x4, [SP, #8]
    // 0x806f50: r16 = true
    //     0x806f50: add             x16, NULL, #0x20  ; true
    // 0x806f54: str             x16, [SP]
    // 0x806f58: r4 = 0
    //     0x806f58: movz            x4, #0
    // 0x806f5c: ldr             x0, [SP, #0x20]
    // 0x806f60: r16 = UnlinkedCall_0x613b5c
    //     0x806f60: add             x16, PP, #0x54, lsl #12  ; [pp+0x546d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x806f64: add             x16, x16, #0x6d8
    // 0x806f68: ldp             x5, lr, [x16]
    // 0x806f6c: blr             lr
    // 0x806f70: ldur            x0, [fp, #-8]
    // 0x806f74: LoadField: r1 = r0->field_f
    //     0x806f74: ldur            w1, [x0, #0xf]
    // 0x806f78: DecompressPointer r1
    //     0x806f78: add             x1, x1, HEAP, lsl #32
    // 0x806f7c: LoadField: r0 = r1->field_b
    //     0x806f7c: ldur            w0, [x1, #0xb]
    // 0x806f80: DecompressPointer r0
    //     0x806f80: add             x0, x0, HEAP, lsl #32
    // 0x806f84: cmp             w0, NULL
    // 0x806f88: b.eq            #0x80701c
    // 0x806f8c: LoadField: r2 = r1->field_1b
    //     0x806f8c: ldur            w2, [x1, #0x1b]
    // 0x806f90: DecompressPointer r2
    //     0x806f90: add             x2, x2, HEAP, lsl #32
    // 0x806f94: r16 = Sentinel
    //     0x806f94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806f98: cmp             w2, w16
    // 0x806f9c: b.eq            #0x807020
    // 0x806fa0: LoadField: r3 = r1->field_1f
    //     0x806fa0: ldur            w3, [x1, #0x1f]
    // 0x806fa4: DecompressPointer r3
    //     0x806fa4: add             x3, x3, HEAP, lsl #32
    // 0x806fa8: r16 = Sentinel
    //     0x806fa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x806fac: cmp             w3, w16
    // 0x806fb0: b.eq            #0x80702c
    // 0x806fb4: LoadField: r1 = r0->field_23
    //     0x806fb4: ldur            w1, [x0, #0x23]
    // 0x806fb8: DecompressPointer r1
    //     0x806fb8: add             x1, x1, HEAP, lsl #32
    // 0x806fbc: stp             x2, x1, [SP, #8]
    // 0x806fc0: str             x3, [SP]
    // 0x806fc4: r4 = 0
    //     0x806fc4: movz            x4, #0
    // 0x806fc8: ldr             x0, [SP, #0x10]
    // 0x806fcc: r16 = UnlinkedCall_0x613b5c
    //     0x806fcc: add             x16, PP, #0x54, lsl #12  ; [pp+0x546e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x806fd0: add             x16, x16, #0x6e8
    // 0x806fd4: ldp             x5, lr, [x16]
    // 0x806fd8: blr             lr
    // 0x806fdc: r0 = Null
    //     0x806fdc: mov             x0, NULL
    // 0x806fe0: LeaveFrame
    //     0x806fe0: mov             SP, fp
    //     0x806fe4: ldp             fp, lr, [SP], #0x10
    // 0x806fe8: ret
    //     0x806fe8: ret             
    // 0x806fec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x806fec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x806ff0: b               #0x806eec
    // 0x806ff4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x806ff4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x806ff8: r9 = selectedPaymentMode
    //     0x806ff8: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x806ffc: ldr             x9, [x9, #0x560]
    // 0x807000: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x807000: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x807004: r9 = selectedPaymentMethod
    //     0x807004: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0x807008: ldr             x9, [x9, #0x550]
    // 0x80700c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x80700c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x807010: r9 = paymentSelectedIndex
    //     0x807010: add             x9, PP, #0x54, lsl #12  ; [pp+0x54558] Field <<EMAIL>>: late (offset: 0x24)
    //     0x807014: ldr             x9, [x9, #0x558]
    // 0x807018: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x807018: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x80701c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x80701c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x807020: r9 = selectedPaymentMode
    //     0x807020: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0x807024: ldr             x9, [x9, #0x560]
    // 0x807028: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x807028: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x80702c: r9 = selectedPaymentMethod
    //     0x80702c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0x807030: ldr             x9, [x9, #0x550]
    // 0x807034: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x807034: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x948444, size: 0x120
    // 0x948444: EnterFrame
    //     0x948444: stp             fp, lr, [SP, #-0x10]!
    //     0x948448: mov             fp, SP
    // 0x94844c: AllocStack(0x18)
    //     0x94844c: sub             SP, SP, #0x18
    // 0x948450: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r2, fp-0x8 */)
    //     0x948450: mov             x2, x1
    //     0x948454: stur            x1, [fp, #-8]
    // 0x948458: CheckStackOverflow
    //     0x948458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94845c: cmp             SP, x16
    //     0x948460: b.ls            #0x948558
    // 0x948464: LoadField: r1 = r2->field_b
    //     0x948464: ldur            w1, [x2, #0xb]
    // 0x948468: DecompressPointer r1
    //     0x948468: add             x1, x1, HEAP, lsl #32
    // 0x94846c: cmp             w1, NULL
    // 0x948470: b.eq            #0x948560
    // 0x948474: LoadField: r0 = r1->field_13
    //     0x948474: ldur            w0, [x1, #0x13]
    // 0x948478: DecompressPointer r0
    //     0x948478: add             x0, x0, HEAP, lsl #32
    // 0x94847c: StoreField: r2->field_1b = r0
    //     0x94847c: stur            w0, [x2, #0x1b]
    //     0x948480: ldurb           w16, [x2, #-1]
    //     0x948484: ldurb           w17, [x0, #-1]
    //     0x948488: and             x16, x17, x16, lsr #2
    //     0x94848c: tst             x16, HEAP, lsr #32
    //     0x948490: b.eq            #0x948498
    //     0x948494: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x948498: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x948498: ldur            w0, [x1, #0x17]
    // 0x94849c: DecompressPointer r0
    //     0x94849c: add             x0, x0, HEAP, lsl #32
    // 0x9484a0: StoreField: r2->field_1f = r0
    //     0x9484a0: stur            w0, [x2, #0x1f]
    //     0x9484a4: ldurb           w16, [x2, #-1]
    //     0x9484a8: ldurb           w17, [x0, #-1]
    //     0x9484ac: and             x16, x17, x16, lsr #2
    //     0x9484b0: tst             x16, HEAP, lsr #32
    //     0x9484b4: b.eq            #0x9484bc
    //     0x9484b8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9484bc: LoadField: r3 = r1->field_1b
    //     0x9484bc: ldur            x3, [x1, #0x1b]
    // 0x9484c0: r0 = BoxInt64Instr(r3)
    //     0x9484c0: sbfiz           x0, x3, #1, #0x1f
    //     0x9484c4: cmp             x3, x0, asr #1
    //     0x9484c8: b.eq            #0x9484d4
    //     0x9484cc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9484d0: stur            x3, [x0, #7]
    // 0x9484d4: StoreField: r2->field_23 = r0
    //     0x9484d4: stur            w0, [x2, #0x23]
    //     0x9484d8: tbz             w0, #0, #0x9484f4
    //     0x9484dc: ldurb           w16, [x2, #-1]
    //     0x9484e0: ldurb           w17, [x0, #-1]
    //     0x9484e4: and             x16, x17, x16, lsr #2
    //     0x9484e8: tst             x16, HEAP, lsr #32
    //     0x9484ec: b.eq            #0x9484f4
    //     0x9484f0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x9484f4: r1 = <double>
    //     0x9484f4: ldr             x1, [PP, #0x3fd8]  ; [pp+0x3fd8] TypeArguments: <double>
    // 0x9484f8: r0 = AnimationController()
    //     0x9484f8: bl              #0x66951c  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0x9484fc: stur            x0, [fp, #-0x10]
    // 0x948500: r16 = Instance_Duration
    //     0x948500: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0x948504: ldr             x16, [x16, #0xf00]
    // 0x948508: str             x16, [SP]
    // 0x94850c: mov             x1, x0
    // 0x948510: ldur            x2, [fp, #-8]
    // 0x948514: r4 = const [0, 0x3, 0x1, 0x2, duration, 0x2, null]
    //     0x948514: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e4c0] List(7) [0, 0x3, 0x1, 0x2, "duration", 0x2, Null]
    //     0x948518: ldr             x4, [x4, #0x4c0]
    // 0x94851c: r0 = AnimationController()
    //     0x94851c: bl              #0x6cc000  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0x948520: ldur            x0, [fp, #-0x10]
    // 0x948524: ldur            x1, [fp, #-8]
    // 0x948528: StoreField: r1->field_27 = r0
    //     0x948528: stur            w0, [x1, #0x27]
    //     0x94852c: ldurb           w16, [x1, #-1]
    //     0x948530: ldurb           w17, [x0, #-1]
    //     0x948534: and             x16, x17, x16, lsr #2
    //     0x948538: tst             x16, HEAP, lsr #32
    //     0x94853c: b.eq            #0x948544
    //     0x948540: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x948544: r0 = _initializePaymentModes()
    //     0x948544: bl              #0x8065b8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_initializePaymentModes
    // 0x948548: r0 = Null
    //     0x948548: mov             x0, NULL
    // 0x94854c: LeaveFrame
    //     0x94854c: mov             SP, fp
    //     0x948550: ldp             fp, lr, [SP], #0x10
    // 0x948554: ret
    //     0x948554: ret             
    // 0x948558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x948558: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94855c: b               #0x948464
    // 0x948560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x948560: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbbc6e8, size: 0x324
    // 0xbbc6e8: EnterFrame
    //     0xbbc6e8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc6ec: mov             fp, SP
    // 0xbbc6f0: AllocStack(0x40)
    //     0xbbc6f0: sub             SP, SP, #0x40
    // 0xbbc6f4: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbbc6f4: mov             x0, x1
    //     0xbbc6f8: stur            x1, [fp, #-8]
    //     0xbbc6fc: mov             x1, x2
    //     0xbbc700: stur            x2, [fp, #-0x10]
    // 0xbbc704: CheckStackOverflow
    //     0xbbc704: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbc708: cmp             SP, x16
    //     0xbbc70c: b.ls            #0xbbca00
    // 0xbbc710: r1 = 1
    //     0xbbc710: movz            x1, #0x1
    // 0xbbc714: r0 = AllocateContext()
    //     0xbbc714: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbc718: mov             x2, x0
    // 0xbbc71c: ldur            x0, [fp, #-8]
    // 0xbbc720: stur            x2, [fp, #-0x18]
    // 0xbbc724: StoreField: r2->field_f = r0
    //     0xbbc724: stur            w0, [x2, #0xf]
    // 0xbbc728: ldur            x1, [fp, #-0x10]
    // 0xbbc72c: r0 = of()
    //     0xbbc72c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbc730: LoadField: r1 = r0->field_87
    //     0xbbc730: ldur            w1, [x0, #0x87]
    // 0xbbc734: DecompressPointer r1
    //     0xbbc734: add             x1, x1, HEAP, lsl #32
    // 0xbbc738: LoadField: r0 = r1->field_7
    //     0xbbc738: ldur            w0, [x1, #7]
    // 0xbbc73c: DecompressPointer r0
    //     0xbbc73c: add             x0, x0, HEAP, lsl #32
    // 0xbbc740: r16 = Instance_Color
    //     0xbbc740: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbc744: r30 = 14.000000
    //     0xbbc744: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbc748: ldr             lr, [lr, #0x1d8]
    // 0xbbc74c: stp             lr, x16, [SP]
    // 0xbbc750: mov             x1, x0
    // 0xbbc754: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbc754: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbc758: ldr             x4, [x4, #0x9b8]
    // 0xbbc75c: r0 = copyWith()
    //     0xbbc75c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbc760: stur            x0, [fp, #-0x10]
    // 0xbbc764: r0 = Text()
    //     0xbbc764: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbc768: mov             x1, x0
    // 0xbbc76c: r0 = "Payment Method"
    //     0xbbc76c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34110] "Payment Method"
    //     0xbbc770: ldr             x0, [x0, #0x110]
    // 0xbbc774: stur            x1, [fp, #-0x20]
    // 0xbbc778: StoreField: r1->field_b = r0
    //     0xbbc778: stur            w0, [x1, #0xb]
    // 0xbbc77c: ldur            x0, [fp, #-0x10]
    // 0xbbc780: StoreField: r1->field_13 = r0
    //     0xbbc780: stur            w0, [x1, #0x13]
    // 0xbbc784: r0 = SvgPicture()
    //     0xbbc784: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbbc788: stur            x0, [fp, #-0x10]
    // 0xbbc78c: r16 = "return order"
    //     0xbbc78c: add             x16, PP, #0x38, lsl #12  ; [pp+0x38c78] "return order"
    //     0xbbc790: ldr             x16, [x16, #0xc78]
    // 0xbbc794: r30 = Instance_BoxFit
    //     0xbbc794: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbbc798: ldr             lr, [lr, #0xb18]
    // 0xbbc79c: stp             lr, x16, [SP]
    // 0xbbc7a0: mov             x1, x0
    // 0xbbc7a4: r2 = "assets/images/secure_icon.svg"
    //     0xbbc7a4: add             x2, PP, #0x38, lsl #12  ; [pp+0x38c80] "assets/images/secure_icon.svg"
    //     0xbbc7a8: ldr             x2, [x2, #0xc80]
    // 0xbbc7ac: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbbc7ac: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbbc7b0: ldr             x4, [x4, #0xb28]
    // 0xbbc7b4: r0 = SvgPicture.asset()
    //     0xbbc7b4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbbc7b8: r1 = Null
    //     0xbbc7b8: mov             x1, NULL
    // 0xbbc7bc: r2 = 4
    //     0xbbc7bc: movz            x2, #0x4
    // 0xbbc7c0: r0 = AllocateArray()
    //     0xbbc7c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbc7c4: mov             x2, x0
    // 0xbbc7c8: ldur            x0, [fp, #-0x20]
    // 0xbbc7cc: stur            x2, [fp, #-0x28]
    // 0xbbc7d0: StoreField: r2->field_f = r0
    //     0xbbc7d0: stur            w0, [x2, #0xf]
    // 0xbbc7d4: ldur            x0, [fp, #-0x10]
    // 0xbbc7d8: StoreField: r2->field_13 = r0
    //     0xbbc7d8: stur            w0, [x2, #0x13]
    // 0xbbc7dc: r1 = <Widget>
    //     0xbbc7dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbc7e0: r0 = AllocateGrowableArray()
    //     0xbbc7e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbc7e4: mov             x1, x0
    // 0xbbc7e8: ldur            x0, [fp, #-0x28]
    // 0xbbc7ec: stur            x1, [fp, #-0x10]
    // 0xbbc7f0: StoreField: r1->field_f = r0
    //     0xbbc7f0: stur            w0, [x1, #0xf]
    // 0xbbc7f4: r0 = 4
    //     0xbbc7f4: movz            x0, #0x4
    // 0xbbc7f8: StoreField: r1->field_b = r0
    //     0xbbc7f8: stur            w0, [x1, #0xb]
    // 0xbbc7fc: r0 = Row()
    //     0xbbc7fc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbc800: mov             x1, x0
    // 0xbbc804: r0 = Instance_Axis
    //     0xbbc804: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbc808: stur            x1, [fp, #-0x20]
    // 0xbbc80c: StoreField: r1->field_f = r0
    //     0xbbc80c: stur            w0, [x1, #0xf]
    // 0xbbc810: r0 = Instance_MainAxisAlignment
    //     0xbbc810: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbbc814: ldr             x0, [x0, #0xa8]
    // 0xbbc818: StoreField: r1->field_13 = r0
    //     0xbbc818: stur            w0, [x1, #0x13]
    // 0xbbc81c: r0 = Instance_MainAxisSize
    //     0xbbc81c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbc820: ldr             x0, [x0, #0xa10]
    // 0xbbc824: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbc824: stur            w0, [x1, #0x17]
    // 0xbbc828: r2 = Instance_CrossAxisAlignment
    //     0xbbc828: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbc82c: ldr             x2, [x2, #0xa18]
    // 0xbbc830: StoreField: r1->field_1b = r2
    //     0xbbc830: stur            w2, [x1, #0x1b]
    // 0xbbc834: r3 = Instance_VerticalDirection
    //     0xbbc834: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbc838: ldr             x3, [x3, #0xa20]
    // 0xbbc83c: StoreField: r1->field_23 = r3
    //     0xbbc83c: stur            w3, [x1, #0x23]
    // 0xbbc840: r4 = Instance_Clip
    //     0xbbc840: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbc844: ldr             x4, [x4, #0x38]
    // 0xbbc848: StoreField: r1->field_2b = r4
    //     0xbbc848: stur            w4, [x1, #0x2b]
    // 0xbbc84c: StoreField: r1->field_2f = rZR
    //     0xbbc84c: stur            xzr, [x1, #0x2f]
    // 0xbbc850: ldur            x5, [fp, #-0x10]
    // 0xbbc854: StoreField: r1->field_b = r5
    //     0xbbc854: stur            w5, [x1, #0xb]
    // 0xbbc858: r0 = Padding()
    //     0xbbc858: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbc85c: mov             x3, x0
    // 0xbbc860: r0 = Instance_EdgeInsets
    //     0xbbc860: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbc864: ldr             x0, [x0, #0x1f0]
    // 0xbbc868: stur            x3, [fp, #-0x10]
    // 0xbbc86c: StoreField: r3->field_f = r0
    //     0xbbc86c: stur            w0, [x3, #0xf]
    // 0xbbc870: ldur            x0, [fp, #-0x20]
    // 0xbbc874: StoreField: r3->field_b = r0
    //     0xbbc874: stur            w0, [x3, #0xb]
    // 0xbbc878: ldur            x0, [fp, #-8]
    // 0xbbc87c: LoadField: r1 = r0->field_b
    //     0xbbc87c: ldur            w1, [x0, #0xb]
    // 0xbbc880: DecompressPointer r1
    //     0xbbc880: add             x1, x1, HEAP, lsl #32
    // 0xbbc884: cmp             w1, NULL
    // 0xbbc888: b.eq            #0xbbca08
    // 0xbbc88c: LoadField: r0 = r1->field_b
    //     0xbbc88c: ldur            w0, [x1, #0xb]
    // 0xbbc890: DecompressPointer r0
    //     0xbbc890: add             x0, x0, HEAP, lsl #32
    // 0xbbc894: LoadField: r1 = r0->field_b
    //     0xbbc894: ldur            w1, [x0, #0xb]
    // 0xbbc898: DecompressPointer r1
    //     0xbbc898: add             x1, x1, HEAP, lsl #32
    // 0xbbc89c: cmp             w1, NULL
    // 0xbbc8a0: b.ne            #0xbbc8ac
    // 0xbbc8a4: r0 = Null
    //     0xbbc8a4: mov             x0, NULL
    // 0xbbc8a8: b               #0xbbc8e0
    // 0xbbc8ac: LoadField: r0 = r1->field_1f
    //     0xbbc8ac: ldur            w0, [x1, #0x1f]
    // 0xbbc8b0: DecompressPointer r0
    //     0xbbc8b0: add             x0, x0, HEAP, lsl #32
    // 0xbbc8b4: cmp             w0, NULL
    // 0xbbc8b8: b.ne            #0xbbc8c4
    // 0xbbc8bc: r0 = Null
    //     0xbbc8bc: mov             x0, NULL
    // 0xbbc8c0: b               #0xbbc8e0
    // 0xbbc8c4: LoadField: r1 = r0->field_7
    //     0xbbc8c4: ldur            w1, [x0, #7]
    // 0xbbc8c8: DecompressPointer r1
    //     0xbbc8c8: add             x1, x1, HEAP, lsl #32
    // 0xbbc8cc: cmp             w1, NULL
    // 0xbbc8d0: b.ne            #0xbbc8dc
    // 0xbbc8d4: r0 = Null
    //     0xbbc8d4: mov             x0, NULL
    // 0xbbc8d8: b               #0xbbc8e0
    // 0xbbc8dc: LoadField: r0 = r1->field_b
    //     0xbbc8dc: ldur            w0, [x1, #0xb]
    // 0xbbc8e0: cmp             w0, NULL
    // 0xbbc8e4: b.ne            #0xbbc8f0
    // 0xbbc8e8: r0 = 0
    //     0xbbc8e8: movz            x0, #0
    // 0xbbc8ec: b               #0xbbc8f8
    // 0xbbc8f0: r1 = LoadInt32Instr(r0)
    //     0xbbc8f0: sbfx            x1, x0, #1, #0x1f
    // 0xbbc8f4: mov             x0, x1
    // 0xbbc8f8: lsl             x4, x0, #1
    // 0xbbc8fc: ldur            x2, [fp, #-0x18]
    // 0xbbc900: stur            x4, [fp, #-8]
    // 0xbbc904: r1 = Function '<anonymous closure>':.
    //     0xbbc904: add             x1, PP, #0x54, lsl #12  ; [pp+0x544b8] AnonymousClosure: (0xbbca0c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::build (0xbbc6e8)
    //     0xbbc908: ldr             x1, [x1, #0x4b8]
    // 0xbbc90c: r0 = AllocateClosure()
    //     0xbbc90c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbc910: stur            x0, [fp, #-0x18]
    // 0xbbc914: r0 = ListView()
    //     0xbbc914: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbbc918: stur            x0, [fp, #-0x20]
    // 0xbbc91c: r16 = Instance_EdgeInsets
    //     0xbbc91c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbbc920: ldr             x16, [x16, #0x668]
    // 0xbbc924: r30 = true
    //     0xbbc924: add             lr, NULL, #0x20  ; true
    // 0xbbc928: stp             lr, x16, [SP, #8]
    // 0xbbc92c: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbbc92c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbbc930: ldr             x16, [x16, #0x1c8]
    // 0xbbc934: str             x16, [SP]
    // 0xbbc938: mov             x1, x0
    // 0xbbc93c: ldur            x2, [fp, #-0x18]
    // 0xbbc940: ldur            x3, [fp, #-8]
    // 0xbbc944: r4 = const [0, 0x6, 0x3, 0x3, padding, 0x3, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xbbc944: add             x4, PP, #0x54, lsl #12  ; [pp+0x544c0] List(11) [0, 0x6, 0x3, 0x3, "padding", 0x3, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbbc948: ldr             x4, [x4, #0x4c0]
    // 0xbbc94c: r0 = ListView.builder()
    //     0xbbc94c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbbc950: r1 = Null
    //     0xbbc950: mov             x1, NULL
    // 0xbbc954: r2 = 6
    //     0xbbc954: movz            x2, #0x6
    // 0xbbc958: r0 = AllocateArray()
    //     0xbbc958: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbc95c: mov             x2, x0
    // 0xbbc960: ldur            x0, [fp, #-0x10]
    // 0xbbc964: stur            x2, [fp, #-8]
    // 0xbbc968: StoreField: r2->field_f = r0
    //     0xbbc968: stur            w0, [x2, #0xf]
    // 0xbbc96c: r16 = Instance_SizedBox
    //     0xbbc96c: add             x16, PP, #0x54, lsl #12  ; [pp+0x544c8] Obj!SizedBox@d67f61
    //     0xbbc970: ldr             x16, [x16, #0x4c8]
    // 0xbbc974: StoreField: r2->field_13 = r16
    //     0xbbc974: stur            w16, [x2, #0x13]
    // 0xbbc978: ldur            x0, [fp, #-0x20]
    // 0xbbc97c: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbc97c: stur            w0, [x2, #0x17]
    // 0xbbc980: r1 = <Widget>
    //     0xbbc980: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbc984: r0 = AllocateGrowableArray()
    //     0xbbc984: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbc988: mov             x1, x0
    // 0xbbc98c: ldur            x0, [fp, #-8]
    // 0xbbc990: stur            x1, [fp, #-0x10]
    // 0xbbc994: StoreField: r1->field_f = r0
    //     0xbbc994: stur            w0, [x1, #0xf]
    // 0xbbc998: r0 = 6
    //     0xbbc998: movz            x0, #0x6
    // 0xbbc99c: StoreField: r1->field_b = r0
    //     0xbbc99c: stur            w0, [x1, #0xb]
    // 0xbbc9a0: r0 = Column()
    //     0xbbc9a0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbc9a4: r1 = Instance_Axis
    //     0xbbc9a4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbc9a8: StoreField: r0->field_f = r1
    //     0xbbc9a8: stur            w1, [x0, #0xf]
    // 0xbbc9ac: r1 = Instance_MainAxisAlignment
    //     0xbbc9ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbc9b0: ldr             x1, [x1, #0xa08]
    // 0xbbc9b4: StoreField: r0->field_13 = r1
    //     0xbbc9b4: stur            w1, [x0, #0x13]
    // 0xbbc9b8: r1 = Instance_MainAxisSize
    //     0xbbc9b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbc9bc: ldr             x1, [x1, #0xa10]
    // 0xbbc9c0: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbc9c0: stur            w1, [x0, #0x17]
    // 0xbbc9c4: r1 = Instance_CrossAxisAlignment
    //     0xbbc9c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbc9c8: ldr             x1, [x1, #0xa18]
    // 0xbbc9cc: StoreField: r0->field_1b = r1
    //     0xbbc9cc: stur            w1, [x0, #0x1b]
    // 0xbbc9d0: r1 = Instance_VerticalDirection
    //     0xbbc9d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbc9d4: ldr             x1, [x1, #0xa20]
    // 0xbbc9d8: StoreField: r0->field_23 = r1
    //     0xbbc9d8: stur            w1, [x0, #0x23]
    // 0xbbc9dc: r1 = Instance_Clip
    //     0xbbc9dc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbc9e0: ldr             x1, [x1, #0x38]
    // 0xbbc9e4: StoreField: r0->field_2b = r1
    //     0xbbc9e4: stur            w1, [x0, #0x2b]
    // 0xbbc9e8: StoreField: r0->field_2f = rZR
    //     0xbbc9e8: stur            xzr, [x0, #0x2f]
    // 0xbbc9ec: ldur            x1, [fp, #-0x10]
    // 0xbbc9f0: StoreField: r0->field_b = r1
    //     0xbbc9f0: stur            w1, [x0, #0xb]
    // 0xbbc9f4: LeaveFrame
    //     0xbbc9f4: mov             SP, fp
    //     0xbbc9f8: ldp             fp, lr, [SP], #0x10
    // 0xbbc9fc: ret
    //     0xbbc9fc: ret             
    // 0xbbca00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbca00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbca04: b               #0xbbc710
    // 0xbbca08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbca08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbbca0c, size: 0x988
    // 0xbbca0c: EnterFrame
    //     0xbbca0c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbca10: mov             fp, SP
    // 0xbbca14: AllocStack(0x60)
    //     0xbbca14: sub             SP, SP, #0x60
    // 0xbbca18: SetupParameters()
    //     0xbbca18: ldr             x0, [fp, #0x20]
    //     0xbbca1c: ldur            w2, [x0, #0x17]
    //     0xbbca20: add             x2, x2, HEAP, lsl #32
    //     0xbbca24: stur            x2, [fp, #-0x18]
    // 0xbbca28: CheckStackOverflow
    //     0xbbca28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbca2c: cmp             SP, x16
    //     0xbbca30: b.ls            #0xbbd384
    // 0xbbca34: LoadField: r0 = r2->field_f
    //     0xbbca34: ldur            w0, [x2, #0xf]
    // 0xbbca38: DecompressPointer r0
    //     0xbbca38: add             x0, x0, HEAP, lsl #32
    // 0xbbca3c: LoadField: r1 = r0->field_b
    //     0xbbca3c: ldur            w1, [x0, #0xb]
    // 0xbbca40: DecompressPointer r1
    //     0xbbca40: add             x1, x1, HEAP, lsl #32
    // 0xbbca44: cmp             w1, NULL
    // 0xbbca48: b.eq            #0xbbd38c
    // 0xbbca4c: LoadField: r0 = r1->field_b
    //     0xbbca4c: ldur            w0, [x1, #0xb]
    // 0xbbca50: DecompressPointer r0
    //     0xbbca50: add             x0, x0, HEAP, lsl #32
    // 0xbbca54: LoadField: r1 = r0->field_b
    //     0xbbca54: ldur            w1, [x0, #0xb]
    // 0xbbca58: DecompressPointer r1
    //     0xbbca58: add             x1, x1, HEAP, lsl #32
    // 0xbbca5c: cmp             w1, NULL
    // 0xbbca60: b.ne            #0xbbca70
    // 0xbbca64: ldr             x4, [fp, #0x10]
    // 0xbbca68: r0 = Null
    //     0xbbca68: mov             x0, NULL
    // 0xbbca6c: b               #0xbbcae8
    // 0xbbca70: LoadField: r0 = r1->field_1f
    //     0xbbca70: ldur            w0, [x1, #0x1f]
    // 0xbbca74: DecompressPointer r0
    //     0xbbca74: add             x0, x0, HEAP, lsl #32
    // 0xbbca78: cmp             w0, NULL
    // 0xbbca7c: b.ne            #0xbbca8c
    // 0xbbca80: ldr             x4, [fp, #0x10]
    // 0xbbca84: r0 = Null
    //     0xbbca84: mov             x0, NULL
    // 0xbbca88: b               #0xbbcae8
    // 0xbbca8c: LoadField: r3 = r0->field_7
    //     0xbbca8c: ldur            w3, [x0, #7]
    // 0xbbca90: DecompressPointer r3
    //     0xbbca90: add             x3, x3, HEAP, lsl #32
    // 0xbbca94: cmp             w3, NULL
    // 0xbbca98: b.ne            #0xbbcaa8
    // 0xbbca9c: ldr             x4, [fp, #0x10]
    // 0xbbcaa0: r0 = Null
    //     0xbbcaa0: mov             x0, NULL
    // 0xbbcaa4: b               #0xbbcae8
    // 0xbbcaa8: ldr             x4, [fp, #0x10]
    // 0xbbcaac: LoadField: r0 = r3->field_b
    //     0xbbcaac: ldur            w0, [x3, #0xb]
    // 0xbbcab0: r5 = LoadInt32Instr(r4)
    //     0xbbcab0: sbfx            x5, x4, #1, #0x1f
    //     0xbbcab4: tbz             w4, #0, #0xbbcabc
    //     0xbbcab8: ldur            x5, [x4, #7]
    // 0xbbcabc: r1 = LoadInt32Instr(r0)
    //     0xbbcabc: sbfx            x1, x0, #1, #0x1f
    // 0xbbcac0: mov             x0, x1
    // 0xbbcac4: mov             x1, x5
    // 0xbbcac8: cmp             x1, x0
    // 0xbbcacc: b.hs            #0xbbd390
    // 0xbbcad0: LoadField: r0 = r3->field_f
    //     0xbbcad0: ldur            w0, [x3, #0xf]
    // 0xbbcad4: DecompressPointer r0
    //     0xbbcad4: add             x0, x0, HEAP, lsl #32
    // 0xbbcad8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbbcad8: add             x16, x0, x5, lsl #2
    //     0xbbcadc: ldur            w1, [x16, #0xf]
    // 0xbbcae0: DecompressPointer r1
    //     0xbbcae0: add             x1, x1, HEAP, lsl #32
    // 0xbbcae4: mov             x0, x1
    // 0xbbcae8: stur            x0, [fp, #-0x10]
    // 0xbbcaec: cmp             w0, NULL
    // 0xbbcaf0: b.ne            #0xbbcafc
    // 0xbbcaf4: r1 = Null
    //     0xbbcaf4: mov             x1, NULL
    // 0xbbcaf8: b               #0xbbcb04
    // 0xbbcafc: LoadField: r1 = r0->field_b
    //     0xbbcafc: ldur            w1, [x0, #0xb]
    // 0xbbcb00: DecompressPointer r1
    //     0xbbcb00: add             x1, x1, HEAP, lsl #32
    // 0xbbcb04: cmp             w1, NULL
    // 0xbbcb08: b.ne            #0xbbcb14
    // 0xbbcb0c: r3 = ""
    //     0xbbcb0c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbcb10: b               #0xbbcb18
    // 0xbbcb14: mov             x3, x1
    // 0xbbcb18: ldr             x1, [fp, #0x18]
    // 0xbbcb1c: stur            x3, [fp, #-8]
    // 0xbbcb20: r0 = of()
    //     0xbbcb20: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbcb24: LoadField: r1 = r0->field_87
    //     0xbbcb24: ldur            w1, [x0, #0x87]
    // 0xbbcb28: DecompressPointer r1
    //     0xbbcb28: add             x1, x1, HEAP, lsl #32
    // 0xbbcb2c: LoadField: r0 = r1->field_7
    //     0xbbcb2c: ldur            w0, [x1, #7]
    // 0xbbcb30: DecompressPointer r0
    //     0xbbcb30: add             x0, x0, HEAP, lsl #32
    // 0xbbcb34: stur            x0, [fp, #-0x20]
    // 0xbbcb38: r1 = Instance_Color
    //     0xbbcb38: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbcb3c: d0 = 0.700000
    //     0xbbcb3c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbbcb40: ldr             d0, [x17, #0xf48]
    // 0xbbcb44: r0 = withOpacity()
    //     0xbbcb44: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbcb48: r16 = 14.000000
    //     0xbbcb48: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbcb4c: ldr             x16, [x16, #0x1d8]
    // 0xbbcb50: stp             x0, x16, [SP]
    // 0xbbcb54: ldur            x1, [fp, #-0x20]
    // 0xbbcb58: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbcb58: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbcb5c: ldr             x4, [x4, #0xaa0]
    // 0xbbcb60: r0 = copyWith()
    //     0xbbcb60: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbcb64: stur            x0, [fp, #-0x20]
    // 0xbbcb68: r0 = Text()
    //     0xbbcb68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbcb6c: mov             x1, x0
    // 0xbbcb70: ldur            x0, [fp, #-8]
    // 0xbbcb74: stur            x1, [fp, #-0x28]
    // 0xbbcb78: StoreField: r1->field_b = r0
    //     0xbbcb78: stur            w0, [x1, #0xb]
    // 0xbbcb7c: ldur            x0, [fp, #-0x20]
    // 0xbbcb80: StoreField: r1->field_13 = r0
    //     0xbbcb80: stur            w0, [x1, #0x13]
    // 0xbbcb84: ldur            x2, [fp, #-0x10]
    // 0xbbcb88: cmp             w2, NULL
    // 0xbbcb8c: b.ne            #0xbbcb98
    // 0xbbcb90: r0 = Null
    //     0xbbcb90: mov             x0, NULL
    // 0xbbcb94: b               #0xbbcba0
    // 0xbbcb98: LoadField: r0 = r2->field_7
    //     0xbbcb98: ldur            w0, [x2, #7]
    // 0xbbcb9c: DecompressPointer r0
    //     0xbbcb9c: add             x0, x0, HEAP, lsl #32
    // 0xbbcba0: r3 = LoadClassIdInstr(r0)
    //     0xbbcba0: ldur            x3, [x0, #-1]
    //     0xbbcba4: ubfx            x3, x3, #0xc, #0x14
    // 0xbbcba8: r16 = "partial-cod"
    //     0xbbcba8: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbbcbac: ldr             x16, [x16, #0x830]
    // 0xbbcbb0: stp             x16, x0, [SP]
    // 0xbbcbb4: mov             x0, x3
    // 0xbbcbb8: mov             lr, x0
    // 0xbbcbbc: ldr             lr, [x21, lr, lsl #3]
    // 0xbbcbc0: blr             lr
    // 0xbbcbc4: r1 = Instance_Color
    //     0xbbcbc4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb18] Obj!Color@d6ad71
    //     0xbbcbc8: ldr             x1, [x1, #0xb18]
    // 0xbbcbcc: d0 = 0.080000
    //     0xbbcbcc: add             x17, PP, #0x27, lsl #12  ; [pp+0x27798] IMM: double(0.08) from 0x3fb47ae147ae147b
    //     0xbbcbd0: ldr             d0, [x17, #0x798]
    // 0xbbcbd4: stur            x0, [fp, #-8]
    // 0xbbcbd8: r0 = withOpacity()
    //     0xbbcbd8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbcbdc: stur            x0, [fp, #-0x20]
    // 0xbbcbe0: r0 = BoxDecoration()
    //     0xbbcbe0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbcbe4: mov             x2, x0
    // 0xbbcbe8: ldur            x0, [fp, #-0x20]
    // 0xbbcbec: stur            x2, [fp, #-0x30]
    // 0xbbcbf0: StoreField: r2->field_7 = r0
    //     0xbbcbf0: stur            w0, [x2, #7]
    // 0xbbcbf4: r0 = Instance_BorderRadius
    //     0xbbcbf4: add             x0, PP, #0x39, lsl #12  ; [pp+0x39798] Obj!BorderRadius@d5a341
    //     0xbbcbf8: ldr             x0, [x0, #0x798]
    // 0xbbcbfc: StoreField: r2->field_13 = r0
    //     0xbbcbfc: stur            w0, [x2, #0x13]
    // 0xbbcc00: r3 = Instance_BoxShape
    //     0xbbcc00: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbcc04: ldr             x3, [x3, #0x80]
    // 0xbbcc08: StoreField: r2->field_23 = r3
    //     0xbbcc08: stur            w3, [x2, #0x23]
    // 0xbbcc0c: ldr             x1, [fp, #0x18]
    // 0xbbcc10: r0 = of()
    //     0xbbcc10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbcc14: LoadField: r1 = r0->field_87
    //     0xbbcc14: ldur            w1, [x0, #0x87]
    // 0xbbcc18: DecompressPointer r1
    //     0xbbcc18: add             x1, x1, HEAP, lsl #32
    // 0xbbcc1c: LoadField: r0 = r1->field_7
    //     0xbbcc1c: ldur            w0, [x1, #7]
    // 0xbbcc20: DecompressPointer r0
    //     0xbbcc20: add             x0, x0, HEAP, lsl #32
    // 0xbbcc24: r16 = 12.000000
    //     0xbbcc24: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbcc28: ldr             x16, [x16, #0x9e8]
    // 0xbbcc2c: r30 = Instance_Color
    //     0xbbcc2c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbbcc30: ldr             lr, [lr, #0x858]
    // 0xbbcc34: stp             lr, x16, [SP]
    // 0xbbcc38: mov             x1, x0
    // 0xbbcc3c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbcc3c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbcc40: ldr             x4, [x4, #0xaa0]
    // 0xbbcc44: r0 = copyWith()
    //     0xbbcc44: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbcc48: stur            x0, [fp, #-0x20]
    // 0xbbcc4c: r0 = Text()
    //     0xbbcc4c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbcc50: mov             x1, x0
    // 0xbbcc54: r0 = "New"
    //     0xbbcc54: add             x0, PP, #0x54, lsl #12  ; [pp+0x544d0] "New"
    //     0xbbcc58: ldr             x0, [x0, #0x4d0]
    // 0xbbcc5c: stur            x1, [fp, #-0x38]
    // 0xbbcc60: StoreField: r1->field_b = r0
    //     0xbbcc60: stur            w0, [x1, #0xb]
    // 0xbbcc64: ldur            x0, [fp, #-0x20]
    // 0xbbcc68: StoreField: r1->field_13 = r0
    //     0xbbcc68: stur            w0, [x1, #0x13]
    // 0xbbcc6c: r0 = Padding()
    //     0xbbcc6c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbcc70: mov             x1, x0
    // 0xbbcc74: r0 = Instance_EdgeInsets
    //     0xbbcc74: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xbbcc78: ldr             x0, [x0, #0xdb0]
    // 0xbbcc7c: stur            x1, [fp, #-0x20]
    // 0xbbcc80: StoreField: r1->field_f = r0
    //     0xbbcc80: stur            w0, [x1, #0xf]
    // 0xbbcc84: ldur            x2, [fp, #-0x38]
    // 0xbbcc88: StoreField: r1->field_b = r2
    //     0xbbcc88: stur            w2, [x1, #0xb]
    // 0xbbcc8c: r0 = Container()
    //     0xbbcc8c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbcc90: stur            x0, [fp, #-0x38]
    // 0xbbcc94: ldur            x16, [fp, #-0x30]
    // 0xbbcc98: ldur            lr, [fp, #-0x20]
    // 0xbbcc9c: stp             lr, x16, [SP]
    // 0xbbcca0: mov             x1, x0
    // 0xbbcca4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbbcca4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbbcca8: ldr             x4, [x4, #0x88]
    // 0xbbccac: r0 = Container()
    //     0xbbccac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbccb0: r0 = Padding()
    //     0xbbccb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbccb4: mov             x1, x0
    // 0xbbccb8: r0 = Instance_EdgeInsets
    //     0xbbccb8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbbccbc: ldr             x0, [x0, #0xa78]
    // 0xbbccc0: stur            x1, [fp, #-0x20]
    // 0xbbccc4: StoreField: r1->field_f = r0
    //     0xbbccc4: stur            w0, [x1, #0xf]
    // 0xbbccc8: ldur            x0, [fp, #-0x38]
    // 0xbbcccc: StoreField: r1->field_b = r0
    //     0xbbcccc: stur            w0, [x1, #0xb]
    // 0xbbccd0: r0 = Visibility()
    //     0xbbccd0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbbccd4: mov             x1, x0
    // 0xbbccd8: ldur            x0, [fp, #-0x20]
    // 0xbbccdc: stur            x1, [fp, #-0x30]
    // 0xbbcce0: StoreField: r1->field_b = r0
    //     0xbbcce0: stur            w0, [x1, #0xb]
    // 0xbbcce4: r2 = Instance_SizedBox
    //     0xbbcce4: ldr             x2, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbbcce8: StoreField: r1->field_f = r2
    //     0xbbcce8: stur            w2, [x1, #0xf]
    // 0xbbccec: ldur            x0, [fp, #-8]
    // 0xbbccf0: StoreField: r1->field_13 = r0
    //     0xbbccf0: stur            w0, [x1, #0x13]
    // 0xbbccf4: r3 = false
    //     0xbbccf4: add             x3, NULL, #0x30  ; false
    // 0xbbccf8: ArrayStore: r1[0] = r3  ; List_4
    //     0xbbccf8: stur            w3, [x1, #0x17]
    // 0xbbccfc: StoreField: r1->field_1b = r3
    //     0xbbccfc: stur            w3, [x1, #0x1b]
    // 0xbbcd00: StoreField: r1->field_1f = r3
    //     0xbbcd00: stur            w3, [x1, #0x1f]
    // 0xbbcd04: StoreField: r1->field_23 = r3
    //     0xbbcd04: stur            w3, [x1, #0x23]
    // 0xbbcd08: StoreField: r1->field_27 = r3
    //     0xbbcd08: stur            w3, [x1, #0x27]
    // 0xbbcd0c: StoreField: r1->field_2b = r3
    //     0xbbcd0c: stur            w3, [x1, #0x2b]
    // 0xbbcd10: ldur            x4, [fp, #-0x10]
    // 0xbbcd14: cmp             w4, NULL
    // 0xbbcd18: b.ne            #0xbbcd24
    // 0xbbcd1c: r0 = Null
    //     0xbbcd1c: mov             x0, NULL
    // 0xbbcd20: b               #0xbbcd50
    // 0xbbcd24: LoadField: r0 = r4->field_f
    //     0xbbcd24: ldur            w0, [x4, #0xf]
    // 0xbbcd28: DecompressPointer r0
    //     0xbbcd28: add             x0, x0, HEAP, lsl #32
    // 0xbbcd2c: cmp             w0, NULL
    // 0xbbcd30: b.ne            #0xbbcd3c
    // 0xbbcd34: r0 = Null
    //     0xbbcd34: mov             x0, NULL
    // 0xbbcd38: b               #0xbbcd50
    // 0xbbcd3c: LoadField: r5 = r0->field_7
    //     0xbbcd3c: ldur            w5, [x0, #7]
    // 0xbbcd40: cbnz            w5, #0xbbcd4c
    // 0xbbcd44: r0 = false
    //     0xbbcd44: add             x0, NULL, #0x30  ; false
    // 0xbbcd48: b               #0xbbcd50
    // 0xbbcd4c: r0 = true
    //     0xbbcd4c: add             x0, NULL, #0x20  ; true
    // 0xbbcd50: cmp             w0, NULL
    // 0xbbcd54: b.ne            #0xbbcd60
    // 0xbbcd58: r5 = false
    //     0xbbcd58: add             x5, NULL, #0x30  ; false
    // 0xbbcd5c: b               #0xbbcd64
    // 0xbbcd60: mov             x5, x0
    // 0xbbcd64: stur            x5, [fp, #-8]
    // 0xbbcd68: cmp             w4, NULL
    // 0xbbcd6c: b.ne            #0xbbcd78
    // 0xbbcd70: r0 = Null
    //     0xbbcd70: mov             x0, NULL
    // 0xbbcd74: b               #0xbbcd80
    // 0xbbcd78: LoadField: r0 = r4->field_7
    //     0xbbcd78: ldur            w0, [x4, #7]
    // 0xbbcd7c: DecompressPointer r0
    //     0xbbcd7c: add             x0, x0, HEAP, lsl #32
    // 0xbbcd80: cmp             w0, NULL
    // 0xbbcd84: b.ne            #0xbbcd8c
    // 0xbbcd88: r0 = ""
    //     0xbbcd88: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbcd8c: r6 = LoadClassIdInstr(r0)
    //     0xbbcd8c: ldur            x6, [x0, #-1]
    //     0xbbcd90: ubfx            x6, x6, #0xc, #0x14
    // 0xbbcd94: r16 = "cod"
    //     0xbbcd94: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xbbcd98: ldr             x16, [x16, #0xa28]
    // 0xbbcd9c: stp             x16, x0, [SP]
    // 0xbbcda0: mov             x0, x6
    // 0xbbcda4: mov             lr, x0
    // 0xbbcda8: ldr             lr, [x21, lr, lsl #3]
    // 0xbbcdac: blr             lr
    // 0xbbcdb0: tbnz            w0, #4, #0xbbcde0
    // 0xbbcdb4: ldr             x1, [fp, #0x18]
    // 0xbbcdb8: r0 = of()
    //     0xbbcdb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbcdbc: LoadField: r1 = r0->field_5b
    //     0xbbcdbc: ldur            w1, [x0, #0x5b]
    // 0xbbcdc0: DecompressPointer r1
    //     0xbbcdc0: add             x1, x1, HEAP, lsl #32
    // 0xbbcdc4: r0 = LoadClassIdInstr(r1)
    //     0xbbcdc4: ldur            x0, [x1, #-1]
    //     0xbbcdc8: ubfx            x0, x0, #0xc, #0x14
    // 0xbbcdcc: d0 = 0.100000
    //     0xbbcdcc: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbbcdd0: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbbcdd0: sub             lr, x0, #0xffa
    //     0xbbcdd4: ldr             lr, [x21, lr, lsl #3]
    //     0xbbcdd8: blr             lr
    // 0xbbcddc: b               #0xbbcde8
    // 0xbbcde0: r0 = Instance_Color
    //     0xbbcde0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbbcde4: ldr             x0, [x0, #0x858]
    // 0xbbcde8: ldur            x2, [fp, #-0x10]
    // 0xbbcdec: stur            x0, [fp, #-0x20]
    // 0xbbcdf0: r0 = BoxDecoration()
    //     0xbbcdf0: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbcdf4: mov             x2, x0
    // 0xbbcdf8: ldur            x0, [fp, #-0x20]
    // 0xbbcdfc: stur            x2, [fp, #-0x38]
    // 0xbbce00: StoreField: r2->field_7 = r0
    //     0xbbce00: stur            w0, [x2, #7]
    // 0xbbce04: r0 = Instance_BorderRadius
    //     0xbbce04: add             x0, PP, #0x39, lsl #12  ; [pp+0x39798] Obj!BorderRadius@d5a341
    //     0xbbce08: ldr             x0, [x0, #0x798]
    // 0xbbce0c: StoreField: r2->field_13 = r0
    //     0xbbce0c: stur            w0, [x2, #0x13]
    // 0xbbce10: r0 = Instance_BoxShape
    //     0xbbce10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbce14: ldr             x0, [x0, #0x80]
    // 0xbbce18: StoreField: r2->field_23 = r0
    //     0xbbce18: stur            w0, [x2, #0x23]
    // 0xbbce1c: ldur            x0, [fp, #-0x10]
    // 0xbbce20: cmp             w0, NULL
    // 0xbbce24: b.ne            #0xbbce30
    // 0xbbce28: r1 = Null
    //     0xbbce28: mov             x1, NULL
    // 0xbbce2c: b               #0xbbce38
    // 0xbbce30: LoadField: r1 = r0->field_f
    //     0xbbce30: ldur            w1, [x0, #0xf]
    // 0xbbce34: DecompressPointer r1
    //     0xbbce34: add             x1, x1, HEAP, lsl #32
    // 0xbbce38: cmp             w1, NULL
    // 0xbbce3c: b.ne            #0xbbce48
    // 0xbbce40: r3 = ""
    //     0xbbce40: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbce44: b               #0xbbce4c
    // 0xbbce48: mov             x3, x1
    // 0xbbce4c: ldr             x1, [fp, #0x18]
    // 0xbbce50: stur            x3, [fp, #-0x20]
    // 0xbbce54: r0 = of()
    //     0xbbce54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbce58: LoadField: r1 = r0->field_87
    //     0xbbce58: ldur            w1, [x0, #0x87]
    // 0xbbce5c: DecompressPointer r1
    //     0xbbce5c: add             x1, x1, HEAP, lsl #32
    // 0xbbce60: LoadField: r2 = r1->field_7
    //     0xbbce60: ldur            w2, [x1, #7]
    // 0xbbce64: DecompressPointer r2
    //     0xbbce64: add             x2, x2, HEAP, lsl #32
    // 0xbbce68: ldur            x1, [fp, #-0x10]
    // 0xbbce6c: stur            x2, [fp, #-0x40]
    // 0xbbce70: cmp             w1, NULL
    // 0xbbce74: b.ne            #0xbbce80
    // 0xbbce78: r0 = Null
    //     0xbbce78: mov             x0, NULL
    // 0xbbce7c: b               #0xbbce88
    // 0xbbce80: LoadField: r0 = r1->field_7
    //     0xbbce80: ldur            w0, [x1, #7]
    // 0xbbce84: DecompressPointer r0
    //     0xbbce84: add             x0, x0, HEAP, lsl #32
    // 0xbbce88: cmp             w0, NULL
    // 0xbbce8c: b.ne            #0xbbce94
    // 0xbbce90: r0 = ""
    //     0xbbce90: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbce94: r3 = LoadClassIdInstr(r0)
    //     0xbbce94: ldur            x3, [x0, #-1]
    //     0xbbce98: ubfx            x3, x3, #0xc, #0x14
    // 0xbbce9c: r16 = "cod"
    //     0xbbce9c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xbbcea0: ldr             x16, [x16, #0xa28]
    // 0xbbcea4: stp             x16, x0, [SP]
    // 0xbbcea8: mov             x0, x3
    // 0xbbceac: mov             lr, x0
    // 0xbbceb0: ldr             lr, [x21, lr, lsl #3]
    // 0xbbceb4: blr             lr
    // 0xbbceb8: tbnz            w0, #4, #0xbbcec4
    // 0xbbcebc: r1 = Instance_Color
    //     0xbbcebc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbcec0: b               #0xbbcec8
    // 0xbbcec4: r1 = Instance_Color
    //     0xbbcec4: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbbcec8: ldur            x2, [fp, #-0x10]
    // 0xbbcecc: ldur            x5, [fp, #-0x28]
    // 0xbbced0: ldur            x3, [fp, #-0x30]
    // 0xbbced4: ldur            x4, [fp, #-8]
    // 0xbbced8: ldur            x0, [fp, #-0x20]
    // 0xbbcedc: r16 = 12.000000
    //     0xbbcedc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbcee0: ldr             x16, [x16, #0x9e8]
    // 0xbbcee4: stp             x1, x16, [SP]
    // 0xbbcee8: ldur            x1, [fp, #-0x40]
    // 0xbbceec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbceec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbcef0: ldr             x4, [x4, #0xaa0]
    // 0xbbcef4: r0 = copyWith()
    //     0xbbcef4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbcef8: stur            x0, [fp, #-0x40]
    // 0xbbcefc: r0 = Text()
    //     0xbbcefc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbcf00: mov             x1, x0
    // 0xbbcf04: ldur            x0, [fp, #-0x20]
    // 0xbbcf08: stur            x1, [fp, #-0x48]
    // 0xbbcf0c: StoreField: r1->field_b = r0
    //     0xbbcf0c: stur            w0, [x1, #0xb]
    // 0xbbcf10: ldur            x0, [fp, #-0x40]
    // 0xbbcf14: StoreField: r1->field_13 = r0
    //     0xbbcf14: stur            w0, [x1, #0x13]
    // 0xbbcf18: r0 = Padding()
    //     0xbbcf18: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbcf1c: mov             x1, x0
    // 0xbbcf20: r0 = Instance_EdgeInsets
    //     0xbbcf20: add             x0, PP, #0x38, lsl #12  ; [pp+0x38db0] Obj!EdgeInsets@d58101
    //     0xbbcf24: ldr             x0, [x0, #0xdb0]
    // 0xbbcf28: stur            x1, [fp, #-0x20]
    // 0xbbcf2c: StoreField: r1->field_f = r0
    //     0xbbcf2c: stur            w0, [x1, #0xf]
    // 0xbbcf30: ldur            x0, [fp, #-0x48]
    // 0xbbcf34: StoreField: r1->field_b = r0
    //     0xbbcf34: stur            w0, [x1, #0xb]
    // 0xbbcf38: r0 = Container()
    //     0xbbcf38: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbcf3c: stur            x0, [fp, #-0x40]
    // 0xbbcf40: ldur            x16, [fp, #-0x38]
    // 0xbbcf44: ldur            lr, [fp, #-0x20]
    // 0xbbcf48: stp             lr, x16, [SP]
    // 0xbbcf4c: mov             x1, x0
    // 0xbbcf50: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbbcf50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbbcf54: ldr             x4, [x4, #0x88]
    // 0xbbcf58: r0 = Container()
    //     0xbbcf58: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbcf5c: r0 = Visibility()
    //     0xbbcf5c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xbbcf60: mov             x3, x0
    // 0xbbcf64: ldur            x0, [fp, #-0x40]
    // 0xbbcf68: stur            x3, [fp, #-0x20]
    // 0xbbcf6c: StoreField: r3->field_b = r0
    //     0xbbcf6c: stur            w0, [x3, #0xb]
    // 0xbbcf70: r0 = Instance_SizedBox
    //     0xbbcf70: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbbcf74: StoreField: r3->field_f = r0
    //     0xbbcf74: stur            w0, [x3, #0xf]
    // 0xbbcf78: ldur            x0, [fp, #-8]
    // 0xbbcf7c: StoreField: r3->field_13 = r0
    //     0xbbcf7c: stur            w0, [x3, #0x13]
    // 0xbbcf80: r0 = false
    //     0xbbcf80: add             x0, NULL, #0x30  ; false
    // 0xbbcf84: ArrayStore: r3[0] = r0  ; List_4
    //     0xbbcf84: stur            w0, [x3, #0x17]
    // 0xbbcf88: StoreField: r3->field_1b = r0
    //     0xbbcf88: stur            w0, [x3, #0x1b]
    // 0xbbcf8c: StoreField: r3->field_1f = r0
    //     0xbbcf8c: stur            w0, [x3, #0x1f]
    // 0xbbcf90: StoreField: r3->field_23 = r0
    //     0xbbcf90: stur            w0, [x3, #0x23]
    // 0xbbcf94: StoreField: r3->field_27 = r0
    //     0xbbcf94: stur            w0, [x3, #0x27]
    // 0xbbcf98: StoreField: r3->field_2b = r0
    //     0xbbcf98: stur            w0, [x3, #0x2b]
    // 0xbbcf9c: r1 = Null
    //     0xbbcf9c: mov             x1, NULL
    // 0xbbcfa0: r2 = 8
    //     0xbbcfa0: movz            x2, #0x8
    // 0xbbcfa4: r0 = AllocateArray()
    //     0xbbcfa4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbcfa8: mov             x2, x0
    // 0xbbcfac: ldur            x0, [fp, #-0x28]
    // 0xbbcfb0: stur            x2, [fp, #-8]
    // 0xbbcfb4: StoreField: r2->field_f = r0
    //     0xbbcfb4: stur            w0, [x2, #0xf]
    // 0xbbcfb8: ldur            x0, [fp, #-0x30]
    // 0xbbcfbc: StoreField: r2->field_13 = r0
    //     0xbbcfbc: stur            w0, [x2, #0x13]
    // 0xbbcfc0: r16 = Instance_Spacer
    //     0xbbcfc0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbbcfc4: ldr             x16, [x16, #0xf0]
    // 0xbbcfc8: ArrayStore: r2[0] = r16  ; List_4
    //     0xbbcfc8: stur            w16, [x2, #0x17]
    // 0xbbcfcc: ldur            x0, [fp, #-0x20]
    // 0xbbcfd0: StoreField: r2->field_1b = r0
    //     0xbbcfd0: stur            w0, [x2, #0x1b]
    // 0xbbcfd4: r1 = <Widget>
    //     0xbbcfd4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbcfd8: r0 = AllocateGrowableArray()
    //     0xbbcfd8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbcfdc: mov             x1, x0
    // 0xbbcfe0: ldur            x0, [fp, #-8]
    // 0xbbcfe4: stur            x1, [fp, #-0x20]
    // 0xbbcfe8: StoreField: r1->field_f = r0
    //     0xbbcfe8: stur            w0, [x1, #0xf]
    // 0xbbcfec: r0 = 8
    //     0xbbcfec: movz            x0, #0x8
    // 0xbbcff0: StoreField: r1->field_b = r0
    //     0xbbcff0: stur            w0, [x1, #0xb]
    // 0xbbcff4: r0 = Row()
    //     0xbbcff4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbcff8: mov             x3, x0
    // 0xbbcffc: r0 = Instance_Axis
    //     0xbbcffc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbd000: stur            x3, [fp, #-8]
    // 0xbbd004: StoreField: r3->field_f = r0
    //     0xbbd004: stur            w0, [x3, #0xf]
    // 0xbbd008: r0 = Instance_MainAxisAlignment
    //     0xbbd008: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbd00c: ldr             x0, [x0, #0xa08]
    // 0xbbd010: StoreField: r3->field_13 = r0
    //     0xbbd010: stur            w0, [x3, #0x13]
    // 0xbbd014: r4 = Instance_MainAxisSize
    //     0xbbd014: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbd018: ldr             x4, [x4, #0xa10]
    // 0xbbd01c: ArrayStore: r3[0] = r4  ; List_4
    //     0xbbd01c: stur            w4, [x3, #0x17]
    // 0xbbd020: r5 = Instance_CrossAxisAlignment
    //     0xbbd020: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbd024: ldr             x5, [x5, #0xa18]
    // 0xbbd028: StoreField: r3->field_1b = r5
    //     0xbbd028: stur            w5, [x3, #0x1b]
    // 0xbbd02c: r6 = Instance_VerticalDirection
    //     0xbbd02c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbd030: ldr             x6, [x6, #0xa20]
    // 0xbbd034: StoreField: r3->field_23 = r6
    //     0xbbd034: stur            w6, [x3, #0x23]
    // 0xbbd038: r7 = Instance_Clip
    //     0xbbd038: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbd03c: ldr             x7, [x7, #0x38]
    // 0xbbd040: StoreField: r3->field_2b = r7
    //     0xbbd040: stur            w7, [x3, #0x2b]
    // 0xbbd044: StoreField: r3->field_2f = rZR
    //     0xbbd044: stur            xzr, [x3, #0x2f]
    // 0xbbd048: ldur            x1, [fp, #-0x20]
    // 0xbbd04c: StoreField: r3->field_b = r1
    //     0xbbd04c: stur            w1, [x3, #0xb]
    // 0xbbd050: r1 = Null
    //     0xbbd050: mov             x1, NULL
    // 0xbbd054: r2 = 2
    //     0xbbd054: movz            x2, #0x2
    // 0xbbd058: r0 = AllocateArray()
    //     0xbbd058: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbd05c: mov             x2, x0
    // 0xbbd060: ldur            x0, [fp, #-8]
    // 0xbbd064: stur            x2, [fp, #-0x20]
    // 0xbbd068: StoreField: r2->field_f = r0
    //     0xbbd068: stur            w0, [x2, #0xf]
    // 0xbbd06c: r1 = <Widget>
    //     0xbbd06c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbd070: r0 = AllocateGrowableArray()
    //     0xbbd070: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbd074: mov             x1, x0
    // 0xbbd078: ldur            x0, [fp, #-0x20]
    // 0xbbd07c: stur            x1, [fp, #-8]
    // 0xbbd080: StoreField: r1->field_f = r0
    //     0xbbd080: stur            w0, [x1, #0xf]
    // 0xbbd084: r0 = 2
    //     0xbbd084: movz            x0, #0x2
    // 0xbbd088: StoreField: r1->field_b = r0
    //     0xbbd088: stur            w0, [x1, #0xb]
    // 0xbbd08c: ldur            x2, [fp, #-0x10]
    // 0xbbd090: cmp             w2, NULL
    // 0xbbd094: b.ne            #0xbbd0a0
    // 0xbbd098: r0 = Null
    //     0xbbd098: mov             x0, NULL
    // 0xbbd09c: b               #0xbbd0a8
    // 0xbbd0a0: LoadField: r0 = r2->field_7
    //     0xbbd0a0: ldur            w0, [x2, #7]
    // 0xbbd0a4: DecompressPointer r0
    //     0xbbd0a4: add             x0, x0, HEAP, lsl #32
    // 0xbbd0a8: r3 = LoadClassIdInstr(r0)
    //     0xbbd0a8: ldur            x3, [x0, #-1]
    //     0xbbd0ac: ubfx            x3, x3, #0xc, #0x14
    // 0xbbd0b0: r16 = "online"
    //     0xbbd0b0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xbbd0b4: ldr             x16, [x16, #0xa50]
    // 0xbbd0b8: stp             x16, x0, [SP]
    // 0xbbd0bc: mov             x0, x3
    // 0xbbd0c0: mov             lr, x0
    // 0xbbd0c4: ldr             lr, [x21, lr, lsl #3]
    // 0xbbd0c8: blr             lr
    // 0xbbd0cc: tbnz            w0, #4, #0xbbd16c
    // 0xbbd0d0: ldur            x1, [fp, #-0x18]
    // 0xbbd0d4: ldur            x0, [fp, #-8]
    // 0xbbd0d8: LoadField: r2 = r1->field_f
    //     0xbbd0d8: ldur            w2, [x1, #0xf]
    // 0xbbd0dc: DecompressPointer r2
    //     0xbbd0dc: add             x2, x2, HEAP, lsl #32
    // 0xbbd0e0: mov             x1, x2
    // 0xbbd0e4: r0 = _buildOnlinePaymentSection()
    //     0xbbd0e4: bl              #0xbbf0b4  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildOnlinePaymentSection
    // 0xbbd0e8: mov             x2, x0
    // 0xbbd0ec: ldur            x0, [fp, #-8]
    // 0xbbd0f0: stur            x2, [fp, #-0x20]
    // 0xbbd0f4: LoadField: r1 = r0->field_b
    //     0xbbd0f4: ldur            w1, [x0, #0xb]
    // 0xbbd0f8: LoadField: r3 = r0->field_f
    //     0xbbd0f8: ldur            w3, [x0, #0xf]
    // 0xbbd0fc: DecompressPointer r3
    //     0xbbd0fc: add             x3, x3, HEAP, lsl #32
    // 0xbbd100: LoadField: r4 = r3->field_b
    //     0xbbd100: ldur            w4, [x3, #0xb]
    // 0xbbd104: r3 = LoadInt32Instr(r1)
    //     0xbbd104: sbfx            x3, x1, #1, #0x1f
    // 0xbbd108: stur            x3, [fp, #-0x50]
    // 0xbbd10c: r1 = LoadInt32Instr(r4)
    //     0xbbd10c: sbfx            x1, x4, #1, #0x1f
    // 0xbbd110: cmp             x3, x1
    // 0xbbd114: b.ne            #0xbbd120
    // 0xbbd118: mov             x1, x0
    // 0xbbd11c: r0 = _growToNextCapacity()
    //     0xbbd11c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbd120: ldur            x2, [fp, #-8]
    // 0xbbd124: ldur            x3, [fp, #-0x50]
    // 0xbbd128: add             x0, x3, #1
    // 0xbbd12c: lsl             x1, x0, #1
    // 0xbbd130: StoreField: r2->field_b = r1
    //     0xbbd130: stur            w1, [x2, #0xb]
    // 0xbbd134: LoadField: r1 = r2->field_f
    //     0xbbd134: ldur            w1, [x2, #0xf]
    // 0xbbd138: DecompressPointer r1
    //     0xbbd138: add             x1, x1, HEAP, lsl #32
    // 0xbbd13c: ldur            x0, [fp, #-0x20]
    // 0xbbd140: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbd140: add             x25, x1, x3, lsl #2
    //     0xbbd144: add             x25, x25, #0xf
    //     0xbbd148: str             w0, [x25]
    //     0xbbd14c: tbz             w0, #0, #0xbbd168
    //     0xbbd150: ldurb           w16, [x1, #-1]
    //     0xbbd154: ldurb           w17, [x0, #-1]
    //     0xbbd158: and             x16, x17, x16, lsr #2
    //     0xbbd15c: tst             x16, HEAP, lsr #32
    //     0xbbd160: b.eq            #0xbbd168
    //     0xbbd164: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbbd168: b               #0xbbd304
    // 0xbbd16c: ldur            x1, [fp, #-0x18]
    // 0xbbd170: ldur            x3, [fp, #-0x10]
    // 0xbbd174: ldur            x2, [fp, #-8]
    // 0xbbd178: cmp             w3, NULL
    // 0xbbd17c: b.ne            #0xbbd188
    // 0xbbd180: r0 = Null
    //     0xbbd180: mov             x0, NULL
    // 0xbbd184: b               #0xbbd190
    // 0xbbd188: LoadField: r0 = r3->field_7
    //     0xbbd188: ldur            w0, [x3, #7]
    // 0xbbd18c: DecompressPointer r0
    //     0xbbd18c: add             x0, x0, HEAP, lsl #32
    // 0xbbd190: r4 = LoadClassIdInstr(r0)
    //     0xbbd190: ldur            x4, [x0, #-1]
    //     0xbbd194: ubfx            x4, x4, #0xc, #0x14
    // 0xbbd198: r16 = "cod"
    //     0xbbd198: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xbbd19c: ldr             x16, [x16, #0xa28]
    // 0xbbd1a0: stp             x16, x0, [SP]
    // 0xbbd1a4: mov             x0, x4
    // 0xbbd1a8: mov             lr, x0
    // 0xbbd1ac: ldr             lr, [x21, lr, lsl #3]
    // 0xbbd1b0: blr             lr
    // 0xbbd1b4: tbnz            w0, #4, #0xbbd26c
    // 0xbbd1b8: ldr             x1, [fp, #0x10]
    // 0xbbd1bc: ldur            x0, [fp, #-0x18]
    // 0xbbd1c0: ldur            x4, [fp, #-8]
    // 0xbbd1c4: LoadField: r2 = r0->field_f
    //     0xbbd1c4: ldur            w2, [x0, #0xf]
    // 0xbbd1c8: DecompressPointer r2
    //     0xbbd1c8: add             x2, x2, HEAP, lsl #32
    // 0xbbd1cc: r3 = LoadInt32Instr(r1)
    //     0xbbd1cc: sbfx            x3, x1, #1, #0x1f
    //     0xbbd1d0: tbz             w1, #0, #0xbbd1d8
    //     0xbbd1d4: ldur            x3, [x1, #7]
    // 0xbbd1d8: mov             x1, x2
    // 0xbbd1dc: ldur            x2, [fp, #-0x10]
    // 0xbbd1e0: r0 = _buildCodPaymentSection()
    //     0xbbd1e0: bl              #0xbbea74  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildCodPaymentSection
    // 0xbbd1e4: mov             x2, x0
    // 0xbbd1e8: ldur            x0, [fp, #-8]
    // 0xbbd1ec: stur            x2, [fp, #-0x20]
    // 0xbbd1f0: LoadField: r1 = r0->field_b
    //     0xbbd1f0: ldur            w1, [x0, #0xb]
    // 0xbbd1f4: LoadField: r3 = r0->field_f
    //     0xbbd1f4: ldur            w3, [x0, #0xf]
    // 0xbbd1f8: DecompressPointer r3
    //     0xbbd1f8: add             x3, x3, HEAP, lsl #32
    // 0xbbd1fc: LoadField: r4 = r3->field_b
    //     0xbbd1fc: ldur            w4, [x3, #0xb]
    // 0xbbd200: r3 = LoadInt32Instr(r1)
    //     0xbbd200: sbfx            x3, x1, #1, #0x1f
    // 0xbbd204: stur            x3, [fp, #-0x50]
    // 0xbbd208: r1 = LoadInt32Instr(r4)
    //     0xbbd208: sbfx            x1, x4, #1, #0x1f
    // 0xbbd20c: cmp             x3, x1
    // 0xbbd210: b.ne            #0xbbd21c
    // 0xbbd214: mov             x1, x0
    // 0xbbd218: r0 = _growToNextCapacity()
    //     0xbbd218: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbd21c: ldur            x3, [fp, #-8]
    // 0xbbd220: ldur            x2, [fp, #-0x50]
    // 0xbbd224: add             x0, x2, #1
    // 0xbbd228: lsl             x1, x0, #1
    // 0xbbd22c: StoreField: r3->field_b = r1
    //     0xbbd22c: stur            w1, [x3, #0xb]
    // 0xbbd230: LoadField: r1 = r3->field_f
    //     0xbbd230: ldur            w1, [x3, #0xf]
    // 0xbbd234: DecompressPointer r1
    //     0xbbd234: add             x1, x1, HEAP, lsl #32
    // 0xbbd238: ldur            x0, [fp, #-0x20]
    // 0xbbd23c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xbbd23c: add             x25, x1, x2, lsl #2
    //     0xbbd240: add             x25, x25, #0xf
    //     0xbbd244: str             w0, [x25]
    //     0xbbd248: tbz             w0, #0, #0xbbd264
    //     0xbbd24c: ldurb           w16, [x1, #-1]
    //     0xbbd250: ldurb           w17, [x0, #-1]
    //     0xbbd254: and             x16, x17, x16, lsr #2
    //     0xbbd258: tst             x16, HEAP, lsr #32
    //     0xbbd25c: b.eq            #0xbbd264
    //     0xbbd260: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbbd264: mov             x2, x3
    // 0xbbd268: b               #0xbbd304
    // 0xbbd26c: ldur            x0, [fp, #-0x18]
    // 0xbbd270: ldur            x3, [fp, #-8]
    // 0xbbd274: LoadField: r1 = r0->field_f
    //     0xbbd274: ldur            w1, [x0, #0xf]
    // 0xbbd278: DecompressPointer r1
    //     0xbbd278: add             x1, x1, HEAP, lsl #32
    // 0xbbd27c: ldur            x2, [fp, #-0x10]
    // 0xbbd280: r0 = _buildPartialCodPaymentSection()
    //     0xbbd280: bl              #0xbbd394  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildPartialCodPaymentSection
    // 0xbbd284: mov             x2, x0
    // 0xbbd288: ldur            x0, [fp, #-8]
    // 0xbbd28c: stur            x2, [fp, #-0x10]
    // 0xbbd290: LoadField: r1 = r0->field_b
    //     0xbbd290: ldur            w1, [x0, #0xb]
    // 0xbbd294: LoadField: r3 = r0->field_f
    //     0xbbd294: ldur            w3, [x0, #0xf]
    // 0xbbd298: DecompressPointer r3
    //     0xbbd298: add             x3, x3, HEAP, lsl #32
    // 0xbbd29c: LoadField: r4 = r3->field_b
    //     0xbbd29c: ldur            w4, [x3, #0xb]
    // 0xbbd2a0: r3 = LoadInt32Instr(r1)
    //     0xbbd2a0: sbfx            x3, x1, #1, #0x1f
    // 0xbbd2a4: stur            x3, [fp, #-0x50]
    // 0xbbd2a8: r1 = LoadInt32Instr(r4)
    //     0xbbd2a8: sbfx            x1, x4, #1, #0x1f
    // 0xbbd2ac: cmp             x3, x1
    // 0xbbd2b0: b.ne            #0xbbd2bc
    // 0xbbd2b4: mov             x1, x0
    // 0xbbd2b8: r0 = _growToNextCapacity()
    //     0xbbd2b8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbbd2bc: ldur            x2, [fp, #-8]
    // 0xbbd2c0: ldur            x3, [fp, #-0x50]
    // 0xbbd2c4: add             x0, x3, #1
    // 0xbbd2c8: lsl             x1, x0, #1
    // 0xbbd2cc: StoreField: r2->field_b = r1
    //     0xbbd2cc: stur            w1, [x2, #0xb]
    // 0xbbd2d0: LoadField: r1 = r2->field_f
    //     0xbbd2d0: ldur            w1, [x2, #0xf]
    // 0xbbd2d4: DecompressPointer r1
    //     0xbbd2d4: add             x1, x1, HEAP, lsl #32
    // 0xbbd2d8: ldur            x0, [fp, #-0x10]
    // 0xbbd2dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbbd2dc: add             x25, x1, x3, lsl #2
    //     0xbbd2e0: add             x25, x25, #0xf
    //     0xbbd2e4: str             w0, [x25]
    //     0xbbd2e8: tbz             w0, #0, #0xbbd304
    //     0xbbd2ec: ldurb           w16, [x1, #-1]
    //     0xbbd2f0: ldurb           w17, [x0, #-1]
    //     0xbbd2f4: and             x16, x17, x16, lsr #2
    //     0xbbd2f8: tst             x16, HEAP, lsr #32
    //     0xbbd2fc: b.eq            #0xbbd304
    //     0xbbd300: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbbd304: r0 = Column()
    //     0xbbd304: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbd308: mov             x1, x0
    // 0xbbd30c: r0 = Instance_Axis
    //     0xbbd30c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbd310: stur            x1, [fp, #-0x10]
    // 0xbbd314: StoreField: r1->field_f = r0
    //     0xbbd314: stur            w0, [x1, #0xf]
    // 0xbbd318: r0 = Instance_MainAxisAlignment
    //     0xbbd318: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbd31c: ldr             x0, [x0, #0xa08]
    // 0xbbd320: StoreField: r1->field_13 = r0
    //     0xbbd320: stur            w0, [x1, #0x13]
    // 0xbbd324: r0 = Instance_MainAxisSize
    //     0xbbd324: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbd328: ldr             x0, [x0, #0xa10]
    // 0xbbd32c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbd32c: stur            w0, [x1, #0x17]
    // 0xbbd330: r0 = Instance_CrossAxisAlignment
    //     0xbbd330: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbd334: ldr             x0, [x0, #0xa18]
    // 0xbbd338: StoreField: r1->field_1b = r0
    //     0xbbd338: stur            w0, [x1, #0x1b]
    // 0xbbd33c: r0 = Instance_VerticalDirection
    //     0xbbd33c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbd340: ldr             x0, [x0, #0xa20]
    // 0xbbd344: StoreField: r1->field_23 = r0
    //     0xbbd344: stur            w0, [x1, #0x23]
    // 0xbbd348: r0 = Instance_Clip
    //     0xbbd348: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbd34c: ldr             x0, [x0, #0x38]
    // 0xbbd350: StoreField: r1->field_2b = r0
    //     0xbbd350: stur            w0, [x1, #0x2b]
    // 0xbbd354: StoreField: r1->field_2f = rZR
    //     0xbbd354: stur            xzr, [x1, #0x2f]
    // 0xbbd358: ldur            x0, [fp, #-8]
    // 0xbbd35c: StoreField: r1->field_b = r0
    //     0xbbd35c: stur            w0, [x1, #0xb]
    // 0xbbd360: r0 = Padding()
    //     0xbbd360: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbd364: r1 = Instance_EdgeInsets
    //     0xbbd364: add             x1, PP, #0x54, lsl #12  ; [pp+0x544d8] Obj!EdgeInsets@d580d1
    //     0xbbd368: ldr             x1, [x1, #0x4d8]
    // 0xbbd36c: StoreField: r0->field_f = r1
    //     0xbbd36c: stur            w1, [x0, #0xf]
    // 0xbbd370: ldur            x1, [fp, #-0x10]
    // 0xbbd374: StoreField: r0->field_b = r1
    //     0xbbd374: stur            w1, [x0, #0xb]
    // 0xbbd378: LeaveFrame
    //     0xbbd378: mov             SP, fp
    //     0xbbd37c: ldp             fp, lr, [SP], #0x10
    // 0xbbd380: ret
    //     0xbbd380: ret             
    // 0xbbd384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbd384: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbd388: b               #0xbbca34
    // 0xbbd38c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbd38c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbd390: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbd390: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildPartialCodPaymentSection(/* No info */) {
    // ** addr: 0xbbd394, size: 0xa34
    // 0xbbd394: EnterFrame
    //     0xbbd394: stp             fp, lr, [SP, #-0x10]!
    //     0xbbd398: mov             fp, SP
    // 0xbbd39c: AllocStack(0x60)
    //     0xbbd39c: sub             SP, SP, #0x60
    // 0xbbd3a0: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbbd3a0: mov             x0, x1
    //     0xbbd3a4: stur            x1, [fp, #-8]
    //     0xbbd3a8: stur            x2, [fp, #-0x10]
    // 0xbbd3ac: CheckStackOverflow
    //     0xbbd3ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbd3b0: cmp             SP, x16
    //     0xbbd3b4: b.ls            #0xbbdd94
    // 0xbbd3b8: r1 = 1
    //     0xbbd3b8: movz            x1, #0x1
    // 0xbbd3bc: r0 = AllocateContext()
    //     0xbbd3bc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbd3c0: ldur            x2, [fp, #-8]
    // 0xbbd3c4: stur            x0, [fp, #-0x18]
    // 0xbbd3c8: StoreField: r0->field_f = r2
    //     0xbbd3c8: stur            w2, [x0, #0xf]
    // 0xbbd3cc: LoadField: r1 = r2->field_f
    //     0xbbd3cc: ldur            w1, [x2, #0xf]
    // 0xbbd3d0: DecompressPointer r1
    //     0xbbd3d0: add             x1, x1, HEAP, lsl #32
    // 0xbbd3d4: cmp             w1, NULL
    // 0xbbd3d8: b.eq            #0xbbdd9c
    // 0xbbd3dc: r0 = of()
    //     0xbbd3dc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbd3e0: LoadField: r1 = r0->field_5b
    //     0xbbd3e0: ldur            w1, [x0, #0x5b]
    // 0xbbd3e4: DecompressPointer r1
    //     0xbbd3e4: add             x1, x1, HEAP, lsl #32
    // 0xbbd3e8: r0 = LoadClassIdInstr(r1)
    //     0xbbd3e8: ldur            x0, [x1, #-1]
    //     0xbbd3ec: ubfx            x0, x0, #0xc, #0x14
    // 0xbbd3f0: d0 = 0.030000
    //     0xbbd3f0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xbbd3f4: ldr             d0, [x17, #0x238]
    // 0xbbd3f8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbbd3f8: sub             lr, x0, #0xffa
    //     0xbbd3fc: ldr             lr, [x21, lr, lsl #3]
    //     0xbbd400: blr             lr
    // 0xbbd404: mov             x2, x0
    // 0xbbd408: r1 = Null
    //     0xbbd408: mov             x1, NULL
    // 0xbbd40c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbd40c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbd410: r0 = Border.all()
    //     0xbbd410: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbbd414: stur            x0, [fp, #-0x20]
    // 0xbbd418: r0 = BoxDecoration()
    //     0xbbd418: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbd41c: mov             x1, x0
    // 0xbbd420: ldur            x0, [fp, #-0x20]
    // 0xbbd424: stur            x1, [fp, #-0x28]
    // 0xbbd428: StoreField: r1->field_f = r0
    //     0xbbd428: stur            w0, [x1, #0xf]
    // 0xbbd42c: r0 = Instance_BoxShape
    //     0xbbd42c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbd430: ldr             x0, [x0, #0x80]
    // 0xbbd434: StoreField: r1->field_23 = r0
    //     0xbbd434: stur            w0, [x1, #0x23]
    // 0xbbd438: r0 = Image()
    //     0xbbd438: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xbbd43c: mov             x1, x0
    // 0xbbd440: r2 = "assets/images/pay_advance.png"
    //     0xbbd440: add             x2, PP, #0x54, lsl #12  ; [pp+0x544e0] "assets/images/pay_advance.png"
    //     0xbbd444: ldr             x2, [x2, #0x4e0]
    // 0xbbd448: stur            x0, [fp, #-0x20]
    // 0xbbd44c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbd44c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbd450: r0 = Image.asset()
    //     0xbbd450: bl              #0xa20f60  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xbbd454: ldur            x0, [fp, #-0x10]
    // 0xbbd458: cmp             w0, NULL
    // 0xbbd45c: b.ne            #0xbbd468
    // 0xbbd460: r0 = Null
    //     0xbbd460: mov             x0, NULL
    // 0xbbd464: b               #0xbbd474
    // 0xbbd468: LoadField: r1 = r0->field_13
    //     0xbbd468: ldur            w1, [x0, #0x13]
    // 0xbbd46c: DecompressPointer r1
    //     0xbbd46c: add             x1, x1, HEAP, lsl #32
    // 0xbbd470: mov             x0, x1
    // 0xbbd474: cmp             w0, NULL
    // 0xbbd478: b.ne            #0xbbd484
    // 0xbbd47c: r3 = ""
    //     0xbbd47c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbd480: b               #0xbbd488
    // 0xbbd484: mov             x3, x0
    // 0xbbd488: ldur            x2, [fp, #-8]
    // 0xbbd48c: ldur            x0, [fp, #-0x20]
    // 0xbbd490: stur            x3, [fp, #-0x10]
    // 0xbbd494: LoadField: r1 = r2->field_f
    //     0xbbd494: ldur            w1, [x2, #0xf]
    // 0xbbd498: DecompressPointer r1
    //     0xbbd498: add             x1, x1, HEAP, lsl #32
    // 0xbbd49c: cmp             w1, NULL
    // 0xbbd4a0: b.eq            #0xbbdda0
    // 0xbbd4a4: r0 = of()
    //     0xbbd4a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbd4a8: LoadField: r1 = r0->field_87
    //     0xbbd4a8: ldur            w1, [x0, #0x87]
    // 0xbbd4ac: DecompressPointer r1
    //     0xbbd4ac: add             x1, x1, HEAP, lsl #32
    // 0xbbd4b0: LoadField: r0 = r1->field_2b
    //     0xbbd4b0: ldur            w0, [x1, #0x2b]
    // 0xbbd4b4: DecompressPointer r0
    //     0xbbd4b4: add             x0, x0, HEAP, lsl #32
    // 0xbbd4b8: stur            x0, [fp, #-0x30]
    // 0xbbd4bc: r1 = Instance_Color
    //     0xbbd4bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbd4c0: d0 = 0.700000
    //     0xbbd4c0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbbd4c4: ldr             d0, [x17, #0xf48]
    // 0xbbd4c8: r0 = withOpacity()
    //     0xbbd4c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbd4cc: r16 = 14.000000
    //     0xbbd4cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbd4d0: ldr             x16, [x16, #0x1d8]
    // 0xbbd4d4: stp             x0, x16, [SP]
    // 0xbbd4d8: ldur            x1, [fp, #-0x30]
    // 0xbbd4dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbd4dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbd4e0: ldr             x4, [x4, #0xaa0]
    // 0xbbd4e4: r0 = copyWith()
    //     0xbbd4e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbd4e8: stur            x0, [fp, #-0x30]
    // 0xbbd4ec: r0 = TextSpan()
    //     0xbbd4ec: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbbd4f0: mov             x1, x0
    // 0xbbd4f4: ldur            x0, [fp, #-0x10]
    // 0xbbd4f8: stur            x1, [fp, #-0x38]
    // 0xbbd4fc: StoreField: r1->field_b = r0
    //     0xbbd4fc: stur            w0, [x1, #0xb]
    // 0xbbd500: r0 = Instance__DeferringMouseCursor
    //     0xbbd500: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbbd504: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbd504: stur            w0, [x1, #0x17]
    // 0xbbd508: ldur            x2, [fp, #-0x30]
    // 0xbbd50c: StoreField: r1->field_7 = r2
    //     0xbbd50c: stur            w2, [x1, #7]
    // 0xbbd510: r0 = TapGestureRecognizer()
    //     0xbbd510: bl              #0x7ce314  ; AllocateTapGestureRecognizerStub -> TapGestureRecognizer (size=0x88)
    // 0xbbd514: stur            x0, [fp, #-0x10]
    // 0xbbd518: r16 = -1.000000
    //     0xbbd518: ldr             x16, [PP, #0x5bc0]  ; [pp+0x5bc0] -1
    // 0xbbd51c: stp             x16, NULL, [SP]
    // 0xbbd520: mov             x1, x0
    // 0xbbd524: r4 = const [0, 0x3, 0x2, 0x1, postAcceptSlopTolerance, 0x2, supportedDevices, 0x1, null]
    //     0xbbd524: add             x4, PP, #0x47, lsl #12  ; [pp+0x47c80] List(9) [0, 0x3, 0x2, 0x1, "postAcceptSlopTolerance", 0x2, "supportedDevices", 0x1, Null]
    //     0xbbd528: ldr             x4, [x4, #0xc80]
    // 0xbbd52c: r0 = BaseTapGestureRecognizer()
    //     0xbbd52c: bl              #0x7ce238  ; [package:flutter/src/gestures/tap.dart] BaseTapGestureRecognizer::BaseTapGestureRecognizer
    // 0xbbd530: ldur            x2, [fp, #-0x18]
    // 0xbbd534: r1 = Function '<anonymous closure>':.
    //     0xbbd534: add             x1, PP, #0x54, lsl #12  ; [pp+0x544e8] AnonymousClosure: (0xbbe9f8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildPartialCodPaymentSection (0xbbd394)
    //     0xbbd538: ldr             x1, [x1, #0x4e8]
    // 0xbbd53c: r0 = AllocateClosure()
    //     0xbbd53c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbd540: ldur            x2, [fp, #-0x10]
    // 0xbbd544: StoreField: r2->field_5f = r0
    //     0xbbd544: stur            w0, [x2, #0x5f]
    //     0xbbd548: ldurb           w16, [x2, #-1]
    //     0xbbd54c: ldurb           w17, [x0, #-1]
    //     0xbbd550: and             x16, x17, x16, lsr #2
    //     0xbbd554: tst             x16, HEAP, lsr #32
    //     0xbbd558: b.eq            #0xbbd560
    //     0xbbd55c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbbd560: ldur            x0, [fp, #-8]
    // 0xbbd564: LoadField: r1 = r0->field_f
    //     0xbbd564: ldur            w1, [x0, #0xf]
    // 0xbbd568: DecompressPointer r1
    //     0xbbd568: add             x1, x1, HEAP, lsl #32
    // 0xbbd56c: cmp             w1, NULL
    // 0xbbd570: b.eq            #0xbbdda4
    // 0xbbd574: r0 = of()
    //     0xbbd574: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbd578: LoadField: r1 = r0->field_87
    //     0xbbd578: ldur            w1, [x0, #0x87]
    // 0xbbd57c: DecompressPointer r1
    //     0xbbd57c: add             x1, x1, HEAP, lsl #32
    // 0xbbd580: LoadField: r0 = r1->field_7
    //     0xbbd580: ldur            w0, [x1, #7]
    // 0xbbd584: DecompressPointer r0
    //     0xbbd584: add             x0, x0, HEAP, lsl #32
    // 0xbbd588: r16 = 12.000000
    //     0xbbd588: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbd58c: ldr             x16, [x16, #0x9e8]
    // 0xbbd590: r30 = Instance_Color
    //     0xbbd590: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbd594: stp             lr, x16, [SP, #8]
    // 0xbbd598: r16 = Instance_TextDecoration
    //     0xbbd598: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xbbd59c: ldr             x16, [x16, #0x10]
    // 0xbbd5a0: str             x16, [SP]
    // 0xbbd5a4: mov             x1, x0
    // 0xbbd5a8: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xbbd5a8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xbbd5ac: ldr             x4, [x4, #0xe38]
    // 0xbbd5b0: r0 = copyWith()
    //     0xbbd5b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbd5b4: stur            x0, [fp, #-0x30]
    // 0xbbd5b8: r0 = TextSpan()
    //     0xbbd5b8: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbbd5bc: mov             x3, x0
    // 0xbbd5c0: r0 = "Know More"
    //     0xbbd5c0: add             x0, PP, #0x42, lsl #12  ; [pp+0x42f00] "Know More"
    //     0xbbd5c4: ldr             x0, [x0, #0xf00]
    // 0xbbd5c8: stur            x3, [fp, #-0x40]
    // 0xbbd5cc: StoreField: r3->field_b = r0
    //     0xbbd5cc: stur            w0, [x3, #0xb]
    // 0xbbd5d0: ldur            x0, [fp, #-0x10]
    // 0xbbd5d4: StoreField: r3->field_13 = r0
    //     0xbbd5d4: stur            w0, [x3, #0x13]
    // 0xbbd5d8: r0 = Instance_SystemMouseCursor
    //     0xbbd5d8: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bfe0] Obj!SystemMouseCursor@d645c1
    //     0xbbd5dc: ldr             x0, [x0, #0xfe0]
    // 0xbbd5e0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbbd5e0: stur            w0, [x3, #0x17]
    // 0xbbd5e4: ldur            x0, [fp, #-0x30]
    // 0xbbd5e8: StoreField: r3->field_7 = r0
    //     0xbbd5e8: stur            w0, [x3, #7]
    // 0xbbd5ec: r1 = Null
    //     0xbbd5ec: mov             x1, NULL
    // 0xbbd5f0: r2 = 6
    //     0xbbd5f0: movz            x2, #0x6
    // 0xbbd5f4: r0 = AllocateArray()
    //     0xbbd5f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbd5f8: mov             x2, x0
    // 0xbbd5fc: ldur            x0, [fp, #-0x38]
    // 0xbbd600: stur            x2, [fp, #-0x10]
    // 0xbbd604: StoreField: r2->field_f = r0
    //     0xbbd604: stur            w0, [x2, #0xf]
    // 0xbbd608: r16 = Instance_TextSpan
    //     0xbbd608: add             x16, PP, #0x54, lsl #12  ; [pp+0x54358] Obj!TextSpan@d65581
    //     0xbbd60c: ldr             x16, [x16, #0x358]
    // 0xbbd610: StoreField: r2->field_13 = r16
    //     0xbbd610: stur            w16, [x2, #0x13]
    // 0xbbd614: ldur            x0, [fp, #-0x40]
    // 0xbbd618: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbd618: stur            w0, [x2, #0x17]
    // 0xbbd61c: r1 = <InlineSpan>
    //     0xbbd61c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbbd620: ldr             x1, [x1, #0xe40]
    // 0xbbd624: r0 = AllocateGrowableArray()
    //     0xbbd624: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbd628: mov             x1, x0
    // 0xbbd62c: ldur            x0, [fp, #-0x10]
    // 0xbbd630: stur            x1, [fp, #-0x30]
    // 0xbbd634: StoreField: r1->field_f = r0
    //     0xbbd634: stur            w0, [x1, #0xf]
    // 0xbbd638: r0 = 6
    //     0xbbd638: movz            x0, #0x6
    // 0xbbd63c: StoreField: r1->field_b = r0
    //     0xbbd63c: stur            w0, [x1, #0xb]
    // 0xbbd640: r0 = TextSpan()
    //     0xbbd640: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbbd644: mov             x1, x0
    // 0xbbd648: ldur            x0, [fp, #-0x30]
    // 0xbbd64c: stur            x1, [fp, #-0x10]
    // 0xbbd650: StoreField: r1->field_f = r0
    //     0xbbd650: stur            w0, [x1, #0xf]
    // 0xbbd654: r0 = Instance__DeferringMouseCursor
    //     0xbbd654: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbbd658: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbd658: stur            w0, [x1, #0x17]
    // 0xbbd65c: r0 = RichText()
    //     0xbbd65c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbbd660: mov             x1, x0
    // 0xbbd664: ldur            x2, [fp, #-0x10]
    // 0xbbd668: stur            x0, [fp, #-0x10]
    // 0xbbd66c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbd66c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbd670: r0 = RichText()
    //     0xbbd670: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbbd674: r1 = <FlexParentData>
    //     0xbbd674: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbbd678: ldr             x1, [x1, #0xe00]
    // 0xbbd67c: r0 = Expanded()
    //     0xbbd67c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbbd680: mov             x2, x0
    // 0xbbd684: r0 = 1
    //     0xbbd684: movz            x0, #0x1
    // 0xbbd688: stur            x2, [fp, #-0x30]
    // 0xbbd68c: StoreField: r2->field_13 = r0
    //     0xbbd68c: stur            x0, [x2, #0x13]
    // 0xbbd690: r0 = Instance_FlexFit
    //     0xbbd690: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbbd694: ldr             x0, [x0, #0xe08]
    // 0xbbd698: StoreField: r2->field_1b = r0
    //     0xbbd698: stur            w0, [x2, #0x1b]
    // 0xbbd69c: ldur            x0, [fp, #-0x10]
    // 0xbbd6a0: StoreField: r2->field_b = r0
    //     0xbbd6a0: stur            w0, [x2, #0xb]
    // 0xbbd6a4: r1 = <double>
    //     0xbbd6a4: ldr             x1, [PP, #0x3fd8]  ; [pp+0x3fd8] TypeArguments: <double>
    // 0xbbd6a8: r0 = Tween()
    //     0xbbd6a8: bl              #0x7c5da0  ; AllocateTweenStub -> Tween<X0> (size=0x14)
    // 0xbbd6ac: mov             x1, x0
    // 0xbbd6b0: r0 = 0.000000
    //     0xbbd6b0: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbbd6b4: StoreField: r1->field_b = r0
    //     0xbbd6b4: stur            w0, [x1, #0xb]
    // 0xbbd6b8: r0 = -0.500000
    //     0xbbd6b8: add             x0, PP, #0x54, lsl #12  ; [pp+0x544f0] -0.5
    //     0xbbd6bc: ldr             x0, [x0, #0x4f0]
    // 0xbbd6c0: StoreField: r1->field_f = r0
    //     0xbbd6c0: stur            w0, [x1, #0xf]
    // 0xbbd6c4: ldur            x0, [fp, #-8]
    // 0xbbd6c8: LoadField: r2 = r0->field_27
    //     0xbbd6c8: ldur            w2, [x0, #0x27]
    // 0xbbd6cc: DecompressPointer r2
    //     0xbbd6cc: add             x2, x2, HEAP, lsl #32
    // 0xbbd6d0: r16 = Sentinel
    //     0xbbd6d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbd6d4: cmp             w2, w16
    // 0xbbd6d8: b.eq            #0xbbdda8
    // 0xbbd6dc: r0 = animate()
    //     0xbbd6dc: bl              #0x7c5c04  ; [package:flutter/src/animation/tween.dart] Animatable::animate
    // 0xbbd6e0: ldur            x2, [fp, #-8]
    // 0xbbd6e4: stur            x0, [fp, #-0x10]
    // 0xbbd6e8: LoadField: r1 = r2->field_f
    //     0xbbd6e8: ldur            w1, [x2, #0xf]
    // 0xbbd6ec: DecompressPointer r1
    //     0xbbd6ec: add             x1, x1, HEAP, lsl #32
    // 0xbbd6f0: cmp             w1, NULL
    // 0xbbd6f4: b.eq            #0xbbddb4
    // 0xbbd6f8: r0 = of()
    //     0xbbd6f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbd6fc: LoadField: r1 = r0->field_5b
    //     0xbbd6fc: ldur            w1, [x0, #0x5b]
    // 0xbbd700: DecompressPointer r1
    //     0xbbd700: add             x1, x1, HEAP, lsl #32
    // 0xbbd704: stur            x1, [fp, #-0x38]
    // 0xbbd708: r0 = Icon()
    //     0xbbd708: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbbd70c: mov             x1, x0
    // 0xbbd710: r0 = Instance_IconData
    //     0xbbd710: add             x0, PP, #0x53, lsl #12  ; [pp+0x53448] Obj!IconData@d553a1
    //     0xbbd714: ldr             x0, [x0, #0x448]
    // 0xbbd718: stur            x1, [fp, #-0x40]
    // 0xbbd71c: StoreField: r1->field_b = r0
    //     0xbbd71c: stur            w0, [x1, #0xb]
    // 0xbbd720: r0 = 25.000000
    //     0xbbd720: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0xbbd724: ldr             x0, [x0, #0x98]
    // 0xbbd728: StoreField: r1->field_f = r0
    //     0xbbd728: stur            w0, [x1, #0xf]
    // 0xbbd72c: ldur            x0, [fp, #-0x38]
    // 0xbbd730: StoreField: r1->field_23 = r0
    //     0xbbd730: stur            w0, [x1, #0x23]
    // 0xbbd734: r0 = RotationTransition()
    //     0xbbd734: bl              #0xbbddd4  ; AllocateRotationTransitionStub -> RotationTransition (size=0x20)
    // 0xbbd738: mov             x3, x0
    // 0xbbd73c: r0 = Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@359170175': static.
    //     0xbbd73c: add             x0, PP, #0x54, lsl #12  ; [pp+0x544f8] Closure: (double) => Matrix4 from Function '_handleTurnsMatrix@359170175': static. (0x7fa737bbe768)
    //     0xbbd740: ldr             x0, [x0, #0x4f8]
    // 0xbbd744: stur            x3, [fp, #-0x38]
    // 0xbbd748: StoreField: r3->field_f = r0
    //     0xbbd748: stur            w0, [x3, #0xf]
    // 0xbbd74c: r0 = Instance_Alignment
    //     0xbbd74c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbbd750: ldr             x0, [x0, #0xb10]
    // 0xbbd754: StoreField: r3->field_13 = r0
    //     0xbbd754: stur            w0, [x3, #0x13]
    // 0xbbd758: ldur            x1, [fp, #-0x40]
    // 0xbbd75c: StoreField: r3->field_1b = r1
    //     0xbbd75c: stur            w1, [x3, #0x1b]
    // 0xbbd760: ldur            x1, [fp, #-0x10]
    // 0xbbd764: StoreField: r3->field_b = r1
    //     0xbbd764: stur            w1, [x3, #0xb]
    // 0xbbd768: r1 = Null
    //     0xbbd768: mov             x1, NULL
    // 0xbbd76c: r2 = 8
    //     0xbbd76c: movz            x2, #0x8
    // 0xbbd770: r0 = AllocateArray()
    //     0xbbd770: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbd774: mov             x2, x0
    // 0xbbd778: ldur            x0, [fp, #-0x20]
    // 0xbbd77c: stur            x2, [fp, #-0x10]
    // 0xbbd780: StoreField: r2->field_f = r0
    //     0xbbd780: stur            w0, [x2, #0xf]
    // 0xbbd784: r16 = Instance_SizedBox
    //     0xbbd784: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbbd788: ldr             x16, [x16, #0xb20]
    // 0xbbd78c: StoreField: r2->field_13 = r16
    //     0xbbd78c: stur            w16, [x2, #0x13]
    // 0xbbd790: ldur            x0, [fp, #-0x30]
    // 0xbbd794: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbd794: stur            w0, [x2, #0x17]
    // 0xbbd798: ldur            x0, [fp, #-0x38]
    // 0xbbd79c: StoreField: r2->field_1b = r0
    //     0xbbd79c: stur            w0, [x2, #0x1b]
    // 0xbbd7a0: r1 = <Widget>
    //     0xbbd7a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbd7a4: r0 = AllocateGrowableArray()
    //     0xbbd7a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbd7a8: mov             x1, x0
    // 0xbbd7ac: ldur            x0, [fp, #-0x10]
    // 0xbbd7b0: stur            x1, [fp, #-0x20]
    // 0xbbd7b4: StoreField: r1->field_f = r0
    //     0xbbd7b4: stur            w0, [x1, #0xf]
    // 0xbbd7b8: r2 = 8
    //     0xbbd7b8: movz            x2, #0x8
    // 0xbbd7bc: StoreField: r1->field_b = r2
    //     0xbbd7bc: stur            w2, [x1, #0xb]
    // 0xbbd7c0: r0 = Row()
    //     0xbbd7c0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbd7c4: mov             x1, x0
    // 0xbbd7c8: r0 = Instance_Axis
    //     0xbbd7c8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbd7cc: stur            x1, [fp, #-0x10]
    // 0xbbd7d0: StoreField: r1->field_f = r0
    //     0xbbd7d0: stur            w0, [x1, #0xf]
    // 0xbbd7d4: r0 = Instance_MainAxisAlignment
    //     0xbbd7d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbd7d8: ldr             x0, [x0, #0xa08]
    // 0xbbd7dc: StoreField: r1->field_13 = r0
    //     0xbbd7dc: stur            w0, [x1, #0x13]
    // 0xbbd7e0: r2 = Instance_MainAxisSize
    //     0xbbd7e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbd7e4: ldr             x2, [x2, #0xa10]
    // 0xbbd7e8: ArrayStore: r1[0] = r2  ; List_4
    //     0xbbd7e8: stur            w2, [x1, #0x17]
    // 0xbbd7ec: r3 = Instance_CrossAxisAlignment
    //     0xbbd7ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbd7f0: ldr             x3, [x3, #0xa18]
    // 0xbbd7f4: StoreField: r1->field_1b = r3
    //     0xbbd7f4: stur            w3, [x1, #0x1b]
    // 0xbbd7f8: r4 = Instance_VerticalDirection
    //     0xbbd7f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbd7fc: ldr             x4, [x4, #0xa20]
    // 0xbbd800: StoreField: r1->field_23 = r4
    //     0xbbd800: stur            w4, [x1, #0x23]
    // 0xbbd804: r5 = Instance_Clip
    //     0xbbd804: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbd808: ldr             x5, [x5, #0x38]
    // 0xbbd80c: StoreField: r1->field_2b = r5
    //     0xbbd80c: stur            w5, [x1, #0x2b]
    // 0xbbd810: StoreField: r1->field_2f = rZR
    //     0xbbd810: stur            xzr, [x1, #0x2f]
    // 0xbbd814: ldur            x6, [fp, #-0x20]
    // 0xbbd818: StoreField: r1->field_b = r6
    //     0xbbd818: stur            w6, [x1, #0xb]
    // 0xbbd81c: r0 = Padding()
    //     0xbbd81c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbd820: mov             x1, x0
    // 0xbbd824: r0 = Instance_EdgeInsets
    //     0xbbd824: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbd828: ldr             x0, [x0, #0x1f0]
    // 0xbbd82c: stur            x1, [fp, #-0x20]
    // 0xbbd830: StoreField: r1->field_f = r0
    //     0xbbd830: stur            w0, [x1, #0xf]
    // 0xbbd834: ldur            x0, [fp, #-0x10]
    // 0xbbd838: StoreField: r1->field_b = r0
    //     0xbbd838: stur            w0, [x1, #0xb]
    // 0xbbd83c: r0 = InkWell()
    //     0xbbd83c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbd840: mov             x3, x0
    // 0xbbd844: ldur            x0, [fp, #-0x20]
    // 0xbbd848: stur            x3, [fp, #-0x10]
    // 0xbbd84c: StoreField: r3->field_b = r0
    //     0xbbd84c: stur            w0, [x3, #0xb]
    // 0xbbd850: ldur            x2, [fp, #-8]
    // 0xbbd854: r1 = Function '_toggleAccordion@**********':.
    //     0xbbd854: add             x1, PP, #0x54, lsl #12  ; [pp+0x54500] AnonymousClosure: (0xbbe5ec), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_toggleAccordion (0xbbe624)
    //     0xbbd858: ldr             x1, [x1, #0x500]
    // 0xbbd85c: r0 = AllocateClosure()
    //     0xbbd85c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbd860: mov             x1, x0
    // 0xbbd864: ldur            x0, [fp, #-0x10]
    // 0xbbd868: StoreField: r0->field_f = r1
    //     0xbbd868: stur            w1, [x0, #0xf]
    // 0xbbd86c: r1 = true
    //     0xbbd86c: add             x1, NULL, #0x20  ; true
    // 0xbbd870: StoreField: r0->field_43 = r1
    //     0xbbd870: stur            w1, [x0, #0x43]
    // 0xbbd874: r2 = Instance_BoxShape
    //     0xbbd874: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbd878: ldr             x2, [x2, #0x80]
    // 0xbbd87c: StoreField: r0->field_47 = r2
    //     0xbbd87c: stur            w2, [x0, #0x47]
    // 0xbbd880: StoreField: r0->field_6f = r1
    //     0xbbd880: stur            w1, [x0, #0x6f]
    // 0xbbd884: r2 = false
    //     0xbbd884: add             x2, NULL, #0x30  ; false
    // 0xbbd888: StoreField: r0->field_73 = r2
    //     0xbbd888: stur            w2, [x0, #0x73]
    // 0xbbd88c: StoreField: r0->field_83 = r1
    //     0xbbd88c: stur            w1, [x0, #0x83]
    // 0xbbd890: StoreField: r0->field_7b = r2
    //     0xbbd890: stur            w2, [x0, #0x7b]
    // 0xbbd894: ldur            x2, [fp, #-8]
    // 0xbbd898: LoadField: r1 = r2->field_2b
    //     0xbbd898: ldur            w1, [x2, #0x2b]
    // 0xbbd89c: DecompressPointer r1
    //     0xbbd89c: add             x1, x1, HEAP, lsl #32
    // 0xbbd8a0: tbnz            w1, #4, #0xbbdc3c
    // 0xbbd8a4: LoadField: r1 = r2->field_f
    //     0xbbd8a4: ldur            w1, [x2, #0xf]
    // 0xbbd8a8: DecompressPointer r1
    //     0xbbd8a8: add             x1, x1, HEAP, lsl #32
    // 0xbbd8ac: cmp             w1, NULL
    // 0xbbd8b0: b.eq            #0xbbddb8
    // 0xbbd8b4: r0 = of()
    //     0xbbd8b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbd8b8: LoadField: r1 = r0->field_5b
    //     0xbbd8b8: ldur            w1, [x0, #0x5b]
    // 0xbbd8bc: DecompressPointer r1
    //     0xbbd8bc: add             x1, x1, HEAP, lsl #32
    // 0xbbd8c0: r0 = LoadClassIdInstr(r1)
    //     0xbbd8c0: ldur            x0, [x1, #-1]
    //     0xbbd8c4: ubfx            x0, x0, #0xc, #0x14
    // 0xbbd8c8: d0 = 0.100000
    //     0xbbd8c8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbbd8cc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbbd8cc: sub             lr, x0, #0xffa
    //     0xbbd8d0: ldr             lr, [x21, lr, lsl #3]
    //     0xbbd8d4: blr             lr
    // 0xbbd8d8: stur            x0, [fp, #-0x20]
    // 0xbbd8dc: r0 = Divider()
    //     0xbbd8dc: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbbd8e0: mov             x1, x0
    // 0xbbd8e4: ldur            x0, [fp, #-0x20]
    // 0xbbd8e8: stur            x1, [fp, #-0x30]
    // 0xbbd8ec: StoreField: r1->field_1f = r0
    //     0xbbd8ec: stur            w0, [x1, #0x1f]
    // 0xbbd8f0: r0 = Padding()
    //     0xbbd8f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbd8f4: mov             x2, x0
    // 0xbbd8f8: r0 = Instance_EdgeInsets
    //     0xbbd8f8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54508] Obj!EdgeInsets@d59571
    //     0xbbd8fc: ldr             x0, [x0, #0x508]
    // 0xbbd900: stur            x2, [fp, #-0x20]
    // 0xbbd904: StoreField: r2->field_f = r0
    //     0xbbd904: stur            w0, [x2, #0xf]
    // 0xbbd908: ldur            x0, [fp, #-0x30]
    // 0xbbd90c: StoreField: r2->field_b = r0
    //     0xbbd90c: stur            w0, [x2, #0xb]
    // 0xbbd910: ldur            x0, [fp, #-8]
    // 0xbbd914: LoadField: r1 = r0->field_f
    //     0xbbd914: ldur            w1, [x0, #0xf]
    // 0xbbd918: DecompressPointer r1
    //     0xbbd918: add             x1, x1, HEAP, lsl #32
    // 0xbbd91c: cmp             w1, NULL
    // 0xbbd920: b.eq            #0xbbddbc
    // 0xbbd924: r0 = of()
    //     0xbbd924: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbd928: LoadField: r1 = r0->field_87
    //     0xbbd928: ldur            w1, [x0, #0x87]
    // 0xbbd92c: DecompressPointer r1
    //     0xbbd92c: add             x1, x1, HEAP, lsl #32
    // 0xbbd930: LoadField: r0 = r1->field_7
    //     0xbbd930: ldur            w0, [x1, #7]
    // 0xbbd934: DecompressPointer r0
    //     0xbbd934: add             x0, x0, HEAP, lsl #32
    // 0xbbd938: r16 = Instance_Color
    //     0xbbd938: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbd93c: r30 = 12.000000
    //     0xbbd93c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbd940: ldr             lr, [lr, #0x9e8]
    // 0xbbd944: stp             lr, x16, [SP]
    // 0xbbd948: mov             x1, x0
    // 0xbbd94c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbd94c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbd950: ldr             x4, [x4, #0x9b8]
    // 0xbbd954: r0 = copyWith()
    //     0xbbd954: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbd958: stur            x0, [fp, #-0x30]
    // 0xbbd95c: r0 = TextSpan()
    //     0xbbd95c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbbd960: mov             x2, x0
    // 0xbbd964: r0 = "Select online payment method"
    //     0xbbd964: add             x0, PP, #0x54, lsl #12  ; [pp+0x54510] "Select online payment method"
    //     0xbbd968: ldr             x0, [x0, #0x510]
    // 0xbbd96c: stur            x2, [fp, #-0x38]
    // 0xbbd970: StoreField: r2->field_b = r0
    //     0xbbd970: stur            w0, [x2, #0xb]
    // 0xbbd974: r0 = Instance__DeferringMouseCursor
    //     0xbbd974: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbbd978: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbd978: stur            w0, [x2, #0x17]
    // 0xbbd97c: ldur            x1, [fp, #-0x30]
    // 0xbbd980: StoreField: r2->field_7 = r1
    //     0xbbd980: stur            w1, [x2, #7]
    // 0xbbd984: ldur            x3, [fp, #-8]
    // 0xbbd988: LoadField: r1 = r3->field_f
    //     0xbbd988: ldur            w1, [x3, #0xf]
    // 0xbbd98c: DecompressPointer r1
    //     0xbbd98c: add             x1, x1, HEAP, lsl #32
    // 0xbbd990: cmp             w1, NULL
    // 0xbbd994: b.eq            #0xbbddc0
    // 0xbbd998: r0 = of()
    //     0xbbd998: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbd99c: LoadField: r1 = r0->field_87
    //     0xbbd99c: ldur            w1, [x0, #0x87]
    // 0xbbd9a0: DecompressPointer r1
    //     0xbbd9a0: add             x1, x1, HEAP, lsl #32
    // 0xbbd9a4: LoadField: r0 = r1->field_7
    //     0xbbd9a4: ldur            w0, [x1, #7]
    // 0xbbd9a8: DecompressPointer r0
    //     0xbbd9a8: add             x0, x0, HEAP, lsl #32
    // 0xbbd9ac: r16 = Instance_Color
    //     0xbbd9ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbbd9b0: ldr             x16, [x16, #0x50]
    // 0xbbd9b4: r30 = 12.000000
    //     0xbbd9b4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbd9b8: ldr             lr, [lr, #0x9e8]
    // 0xbbd9bc: stp             lr, x16, [SP]
    // 0xbbd9c0: mov             x1, x0
    // 0xbbd9c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbd9c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbd9c8: ldr             x4, [x4, #0x9b8]
    // 0xbbd9cc: r0 = copyWith()
    //     0xbbd9cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbd9d0: stur            x0, [fp, #-0x30]
    // 0xbbd9d4: r0 = TextSpan()
    //     0xbbd9d4: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbbd9d8: mov             x3, x0
    // 0xbbd9dc: r0 = "*"
    //     0xbbd9dc: add             x0, PP, #0x33, lsl #12  ; [pp+0x337a8] "*"
    //     0xbbd9e0: ldr             x0, [x0, #0x7a8]
    // 0xbbd9e4: stur            x3, [fp, #-0x40]
    // 0xbbd9e8: StoreField: r3->field_b = r0
    //     0xbbd9e8: stur            w0, [x3, #0xb]
    // 0xbbd9ec: r0 = Instance__DeferringMouseCursor
    //     0xbbd9ec: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbbd9f0: ArrayStore: r3[0] = r0  ; List_4
    //     0xbbd9f0: stur            w0, [x3, #0x17]
    // 0xbbd9f4: ldur            x1, [fp, #-0x30]
    // 0xbbd9f8: StoreField: r3->field_7 = r1
    //     0xbbd9f8: stur            w1, [x3, #7]
    // 0xbbd9fc: r1 = Null
    //     0xbbd9fc: mov             x1, NULL
    // 0xbbda00: r2 = 4
    //     0xbbda00: movz            x2, #0x4
    // 0xbbda04: r0 = AllocateArray()
    //     0xbbda04: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbda08: mov             x2, x0
    // 0xbbda0c: ldur            x0, [fp, #-0x38]
    // 0xbbda10: stur            x2, [fp, #-0x30]
    // 0xbbda14: StoreField: r2->field_f = r0
    //     0xbbda14: stur            w0, [x2, #0xf]
    // 0xbbda18: ldur            x0, [fp, #-0x40]
    // 0xbbda1c: StoreField: r2->field_13 = r0
    //     0xbbda1c: stur            w0, [x2, #0x13]
    // 0xbbda20: r1 = <InlineSpan>
    //     0xbbda20: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xbbda24: ldr             x1, [x1, #0xe40]
    // 0xbbda28: r0 = AllocateGrowableArray()
    //     0xbbda28: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbda2c: mov             x1, x0
    // 0xbbda30: ldur            x0, [fp, #-0x30]
    // 0xbbda34: stur            x1, [fp, #-0x38]
    // 0xbbda38: StoreField: r1->field_f = r0
    //     0xbbda38: stur            w0, [x1, #0xf]
    // 0xbbda3c: r2 = 4
    //     0xbbda3c: movz            x2, #0x4
    // 0xbbda40: StoreField: r1->field_b = r2
    //     0xbbda40: stur            w2, [x1, #0xb]
    // 0xbbda44: r0 = TextSpan()
    //     0xbbda44: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xbbda48: mov             x1, x0
    // 0xbbda4c: ldur            x0, [fp, #-0x38]
    // 0xbbda50: stur            x1, [fp, #-0x30]
    // 0xbbda54: StoreField: r1->field_f = r0
    //     0xbbda54: stur            w0, [x1, #0xf]
    // 0xbbda58: r0 = Instance__DeferringMouseCursor
    //     0xbbda58: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xbbda5c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbda5c: stur            w0, [x1, #0x17]
    // 0xbbda60: r0 = RichText()
    //     0xbbda60: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xbbda64: mov             x1, x0
    // 0xbbda68: ldur            x2, [fp, #-0x30]
    // 0xbbda6c: stur            x0, [fp, #-0x30]
    // 0xbbda70: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbda70: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbda74: r0 = RichText()
    //     0xbbda74: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xbbda78: r0 = Padding()
    //     0xbbda78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbda7c: mov             x3, x0
    // 0xbbda80: r0 = Instance_EdgeInsets
    //     0xbbda80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbbda84: ldr             x0, [x0, #0x668]
    // 0xbbda88: stur            x3, [fp, #-0x38]
    // 0xbbda8c: StoreField: r3->field_f = r0
    //     0xbbda8c: stur            w0, [x3, #0xf]
    // 0xbbda90: ldur            x1, [fp, #-0x30]
    // 0xbbda94: StoreField: r3->field_b = r1
    //     0xbbda94: stur            w1, [x3, #0xb]
    // 0xbbda98: ldur            x1, [fp, #-8]
    // 0xbbda9c: LoadField: r2 = r1->field_b
    //     0xbbda9c: ldur            w2, [x1, #0xb]
    // 0xbbdaa0: DecompressPointer r2
    //     0xbbdaa0: add             x2, x2, HEAP, lsl #32
    // 0xbbdaa4: cmp             w2, NULL
    // 0xbbdaa8: b.eq            #0xbbddc4
    // 0xbbdaac: LoadField: r1 = r2->field_b
    //     0xbbdaac: ldur            w1, [x2, #0xb]
    // 0xbbdab0: DecompressPointer r1
    //     0xbbdab0: add             x1, x1, HEAP, lsl #32
    // 0xbbdab4: LoadField: r2 = r1->field_b
    //     0xbbdab4: ldur            w2, [x1, #0xb]
    // 0xbbdab8: DecompressPointer r2
    //     0xbbdab8: add             x2, x2, HEAP, lsl #32
    // 0xbbdabc: cmp             w2, NULL
    // 0xbbdac0: b.ne            #0xbbdacc
    // 0xbbdac4: r1 = Null
    //     0xbbdac4: mov             x1, NULL
    // 0xbbdac8: b               #0xbbdaec
    // 0xbbdacc: LoadField: r1 = r2->field_27
    //     0xbbdacc: ldur            w1, [x2, #0x27]
    // 0xbbdad0: DecompressPointer r1
    //     0xbbdad0: add             x1, x1, HEAP, lsl #32
    // 0xbbdad4: cmp             w1, NULL
    // 0xbbdad8: b.ne            #0xbbdae4
    // 0xbbdadc: r1 = Null
    //     0xbbdadc: mov             x1, NULL
    // 0xbbdae0: b               #0xbbdaec
    // 0xbbdae4: LoadField: r2 = r1->field_b
    //     0xbbdae4: ldur            w2, [x1, #0xb]
    // 0xbbdae8: mov             x1, x2
    // 0xbbdaec: cmp             w1, NULL
    // 0xbbdaf0: b.ne            #0xbbdafc
    // 0xbbdaf4: r5 = 0
    //     0xbbdaf4: movz            x5, #0
    // 0xbbdaf8: b               #0xbbdb04
    // 0xbbdafc: r2 = LoadInt32Instr(r1)
    //     0xbbdafc: sbfx            x2, x1, #1, #0x1f
    // 0xbbdb00: mov             x5, x2
    // 0xbbdb04: ldur            x4, [fp, #-0x20]
    // 0xbbdb08: ldur            x2, [fp, #-0x18]
    // 0xbbdb0c: stur            x5, [fp, #-0x48]
    // 0xbbdb10: r1 = Function '<anonymous closure>':.
    //     0xbbdb10: add             x1, PP, #0x54, lsl #12  ; [pp+0x54518] AnonymousClosure: (0xbbdebc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildPartialCodPaymentSection (0xbbd394)
    //     0xbbdb14: ldr             x1, [x1, #0x518]
    // 0xbbdb18: r0 = AllocateClosure()
    //     0xbbdb18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbdb1c: ldur            x2, [fp, #-0x18]
    // 0xbbdb20: r1 = Function '<anonymous closure>':.
    //     0xbbdb20: add             x1, PP, #0x54, lsl #12  ; [pp+0x54520] AnonymousClosure: (0xbbdde0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildPartialCodPaymentSection (0xbbd394)
    //     0xbbdb24: ldr             x1, [x1, #0x520]
    // 0xbbdb28: stur            x0, [fp, #-8]
    // 0xbbdb2c: r0 = AllocateClosure()
    //     0xbbdb2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbdb30: stur            x0, [fp, #-0x18]
    // 0xbbdb34: r0 = ListView()
    //     0xbbdb34: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbbdb38: stur            x0, [fp, #-0x30]
    // 0xbbdb3c: r16 = true
    //     0xbbdb3c: add             x16, NULL, #0x20  ; true
    // 0xbbdb40: r30 = false
    //     0xbbdb40: add             lr, NULL, #0x30  ; false
    // 0xbbdb44: stp             lr, x16, [SP]
    // 0xbbdb48: mov             x1, x0
    // 0xbbdb4c: ldur            x2, [fp, #-8]
    // 0xbbdb50: ldur            x3, [fp, #-0x48]
    // 0xbbdb54: ldur            x5, [fp, #-0x18]
    // 0xbbdb58: r4 = const [0, 0x6, 0x2, 0x4, primary, 0x5, shrinkWrap, 0x4, null]
    //     0xbbdb58: add             x4, PP, #0x38, lsl #12  ; [pp+0x38c98] List(9) [0, 0x6, 0x2, 0x4, "primary", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbbdb5c: ldr             x4, [x4, #0xc98]
    // 0xbbdb60: r0 = ListView.separated()
    //     0xbbdb60: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbbdb64: r0 = Padding()
    //     0xbbdb64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbdb68: mov             x3, x0
    // 0xbbdb6c: r0 = Instance_EdgeInsets
    //     0xbbdb6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xbbdb70: ldr             x0, [x0, #0x668]
    // 0xbbdb74: stur            x3, [fp, #-8]
    // 0xbbdb78: StoreField: r3->field_f = r0
    //     0xbbdb78: stur            w0, [x3, #0xf]
    // 0xbbdb7c: ldur            x0, [fp, #-0x30]
    // 0xbbdb80: StoreField: r3->field_b = r0
    //     0xbbdb80: stur            w0, [x3, #0xb]
    // 0xbbdb84: r1 = Null
    //     0xbbdb84: mov             x1, NULL
    // 0xbbdb88: r2 = 8
    //     0xbbdb88: movz            x2, #0x8
    // 0xbbdb8c: r0 = AllocateArray()
    //     0xbbdb8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbdb90: mov             x2, x0
    // 0xbbdb94: ldur            x0, [fp, #-0x20]
    // 0xbbdb98: stur            x2, [fp, #-0x18]
    // 0xbbdb9c: StoreField: r2->field_f = r0
    //     0xbbdb9c: stur            w0, [x2, #0xf]
    // 0xbbdba0: ldur            x0, [fp, #-0x38]
    // 0xbbdba4: StoreField: r2->field_13 = r0
    //     0xbbdba4: stur            w0, [x2, #0x13]
    // 0xbbdba8: ldur            x0, [fp, #-8]
    // 0xbbdbac: ArrayStore: r2[0] = r0  ; List_4
    //     0xbbdbac: stur            w0, [x2, #0x17]
    // 0xbbdbb0: r16 = Instance_SizedBox
    //     0xbbdbb0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xbbdbb4: ldr             x16, [x16, #0x8f0]
    // 0xbbdbb8: StoreField: r2->field_1b = r16
    //     0xbbdbb8: stur            w16, [x2, #0x1b]
    // 0xbbdbbc: r1 = <Widget>
    //     0xbbdbbc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbdbc0: r0 = AllocateGrowableArray()
    //     0xbbdbc0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbdbc4: mov             x1, x0
    // 0xbbdbc8: ldur            x0, [fp, #-0x18]
    // 0xbbdbcc: stur            x1, [fp, #-8]
    // 0xbbdbd0: StoreField: r1->field_f = r0
    //     0xbbdbd0: stur            w0, [x1, #0xf]
    // 0xbbdbd4: r0 = 8
    //     0xbbdbd4: movz            x0, #0x8
    // 0xbbdbd8: StoreField: r1->field_b = r0
    //     0xbbdbd8: stur            w0, [x1, #0xb]
    // 0xbbdbdc: r0 = Column()
    //     0xbbdbdc: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbdbe0: mov             x1, x0
    // 0xbbdbe4: r0 = Instance_Axis
    //     0xbbdbe4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbdbe8: StoreField: r1->field_f = r0
    //     0xbbdbe8: stur            w0, [x1, #0xf]
    // 0xbbdbec: r2 = Instance_MainAxisAlignment
    //     0xbbdbec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbdbf0: ldr             x2, [x2, #0xa08]
    // 0xbbdbf4: StoreField: r1->field_13 = r2
    //     0xbbdbf4: stur            w2, [x1, #0x13]
    // 0xbbdbf8: r3 = Instance_MainAxisSize
    //     0xbbdbf8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbdbfc: ldr             x3, [x3, #0xa10]
    // 0xbbdc00: ArrayStore: r1[0] = r3  ; List_4
    //     0xbbdc00: stur            w3, [x1, #0x17]
    // 0xbbdc04: r4 = Instance_CrossAxisAlignment
    //     0xbbdc04: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbbdc08: ldr             x4, [x4, #0x890]
    // 0xbbdc0c: StoreField: r1->field_1b = r4
    //     0xbbdc0c: stur            w4, [x1, #0x1b]
    // 0xbbdc10: r4 = Instance_VerticalDirection
    //     0xbbdc10: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbdc14: ldr             x4, [x4, #0xa20]
    // 0xbbdc18: StoreField: r1->field_23 = r4
    //     0xbbdc18: stur            w4, [x1, #0x23]
    // 0xbbdc1c: r5 = Instance_Clip
    //     0xbbdc1c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbdc20: ldr             x5, [x5, #0x38]
    // 0xbbdc24: StoreField: r1->field_2b = r5
    //     0xbbdc24: stur            w5, [x1, #0x2b]
    // 0xbbdc28: StoreField: r1->field_2f = rZR
    //     0xbbdc28: stur            xzr, [x1, #0x2f]
    // 0xbbdc2c: ldur            x6, [fp, #-8]
    // 0xbbdc30: StoreField: r1->field_b = r6
    //     0xbbdc30: stur            w6, [x1, #0xb]
    // 0xbbdc34: mov             x6, x1
    // 0xbbdc38: b               #0xbbdc64
    // 0xbbdc3c: r2 = Instance_MainAxisAlignment
    //     0xbbdc3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbdc40: ldr             x2, [x2, #0xa08]
    // 0xbbdc44: r3 = Instance_MainAxisSize
    //     0xbbdc44: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbdc48: ldr             x3, [x3, #0xa10]
    // 0xbbdc4c: r4 = Instance_VerticalDirection
    //     0xbbdc4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbdc50: ldr             x4, [x4, #0xa20]
    // 0xbbdc54: r0 = Instance_Axis
    //     0xbbdc54: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbdc58: r5 = Instance_Clip
    //     0xbbdc58: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbdc5c: ldr             x5, [x5, #0x38]
    // 0xbbdc60: r6 = Instance_SizedBox
    //     0xbbdc60: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbbdc64: ldur            x1, [fp, #-0x10]
    // 0xbbdc68: stur            x6, [fp, #-8]
    // 0xbbdc6c: r0 = AnimatedSize()
    //     0xbbdc6c: bl              #0xbbddc8  ; AllocateAnimatedSizeStub -> AnimatedSize (size=0x28)
    // 0xbbdc70: mov             x3, x0
    // 0xbbdc74: ldur            x0, [fp, #-8]
    // 0xbbdc78: stur            x3, [fp, #-0x18]
    // 0xbbdc7c: StoreField: r3->field_b = r0
    //     0xbbdc7c: stur            w0, [x3, #0xb]
    // 0xbbdc80: r0 = Instance_Alignment
    //     0xbbdc80: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbbdc84: ldr             x0, [x0, #0xb10]
    // 0xbbdc88: StoreField: r3->field_f = r0
    //     0xbbdc88: stur            w0, [x3, #0xf]
    // 0xbbdc8c: r0 = Instance_Cubic
    //     0xbbdc8c: add             x0, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0xbbdc90: ldr             x0, [x0, #0x2b0]
    // 0xbbdc94: StoreField: r3->field_13 = r0
    //     0xbbdc94: stur            w0, [x3, #0x13]
    // 0xbbdc98: r0 = Instance_Duration
    //     0xbbdc98: ldr             x0, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xbbdc9c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbbdc9c: stur            w0, [x3, #0x17]
    // 0xbbdca0: r0 = Instance_Clip
    //     0xbbdca0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbbdca4: ldr             x0, [x0, #0x7e0]
    // 0xbbdca8: StoreField: r3->field_1f = r0
    //     0xbbdca8: stur            w0, [x3, #0x1f]
    // 0xbbdcac: r1 = Null
    //     0xbbdcac: mov             x1, NULL
    // 0xbbdcb0: r2 = 4
    //     0xbbdcb0: movz            x2, #0x4
    // 0xbbdcb4: r0 = AllocateArray()
    //     0xbbdcb4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbdcb8: mov             x2, x0
    // 0xbbdcbc: ldur            x0, [fp, #-0x10]
    // 0xbbdcc0: stur            x2, [fp, #-8]
    // 0xbbdcc4: StoreField: r2->field_f = r0
    //     0xbbdcc4: stur            w0, [x2, #0xf]
    // 0xbbdcc8: ldur            x0, [fp, #-0x18]
    // 0xbbdccc: StoreField: r2->field_13 = r0
    //     0xbbdccc: stur            w0, [x2, #0x13]
    // 0xbbdcd0: r1 = <Widget>
    //     0xbbdcd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbdcd4: r0 = AllocateGrowableArray()
    //     0xbbdcd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbdcd8: mov             x1, x0
    // 0xbbdcdc: ldur            x0, [fp, #-8]
    // 0xbbdce0: stur            x1, [fp, #-0x10]
    // 0xbbdce4: StoreField: r1->field_f = r0
    //     0xbbdce4: stur            w0, [x1, #0xf]
    // 0xbbdce8: r0 = 4
    //     0xbbdce8: movz            x0, #0x4
    // 0xbbdcec: StoreField: r1->field_b = r0
    //     0xbbdcec: stur            w0, [x1, #0xb]
    // 0xbbdcf0: r0 = Column()
    //     0xbbdcf0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbdcf4: mov             x1, x0
    // 0xbbdcf8: r0 = Instance_Axis
    //     0xbbdcf8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbdcfc: stur            x1, [fp, #-8]
    // 0xbbdd00: StoreField: r1->field_f = r0
    //     0xbbdd00: stur            w0, [x1, #0xf]
    // 0xbbdd04: r0 = Instance_MainAxisAlignment
    //     0xbbdd04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbdd08: ldr             x0, [x0, #0xa08]
    // 0xbbdd0c: StoreField: r1->field_13 = r0
    //     0xbbdd0c: stur            w0, [x1, #0x13]
    // 0xbbdd10: r0 = Instance_MainAxisSize
    //     0xbbdd10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbdd14: ldr             x0, [x0, #0xa10]
    // 0xbbdd18: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbdd18: stur            w0, [x1, #0x17]
    // 0xbbdd1c: r0 = Instance_CrossAxisAlignment
    //     0xbbdd1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbdd20: ldr             x0, [x0, #0xa18]
    // 0xbbdd24: StoreField: r1->field_1b = r0
    //     0xbbdd24: stur            w0, [x1, #0x1b]
    // 0xbbdd28: r0 = Instance_VerticalDirection
    //     0xbbdd28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbdd2c: ldr             x0, [x0, #0xa20]
    // 0xbbdd30: StoreField: r1->field_23 = r0
    //     0xbbdd30: stur            w0, [x1, #0x23]
    // 0xbbdd34: r0 = Instance_Clip
    //     0xbbdd34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbdd38: ldr             x0, [x0, #0x38]
    // 0xbbdd3c: StoreField: r1->field_2b = r0
    //     0xbbdd3c: stur            w0, [x1, #0x2b]
    // 0xbbdd40: StoreField: r1->field_2f = rZR
    //     0xbbdd40: stur            xzr, [x1, #0x2f]
    // 0xbbdd44: ldur            x0, [fp, #-0x10]
    // 0xbbdd48: StoreField: r1->field_b = r0
    //     0xbbdd48: stur            w0, [x1, #0xb]
    // 0xbbdd4c: r0 = Container()
    //     0xbbdd4c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbdd50: stur            x0, [fp, #-0x10]
    // 0xbbdd54: ldur            x16, [fp, #-0x28]
    // 0xbbdd58: ldur            lr, [fp, #-8]
    // 0xbbdd5c: stp             lr, x16, [SP]
    // 0xbbdd60: mov             x1, x0
    // 0xbbdd64: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbbdd64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbbdd68: ldr             x4, [x4, #0x88]
    // 0xbbdd6c: r0 = Container()
    //     0xbbdd6c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbdd70: r0 = Padding()
    //     0xbbdd70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbdd74: r1 = Instance_EdgeInsets
    //     0xbbdd74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbbdd78: ldr             x1, [x1, #0xa00]
    // 0xbbdd7c: StoreField: r0->field_f = r1
    //     0xbbdd7c: stur            w1, [x0, #0xf]
    // 0xbbdd80: ldur            x1, [fp, #-0x10]
    // 0xbbdd84: StoreField: r0->field_b = r1
    //     0xbbdd84: stur            w1, [x0, #0xb]
    // 0xbbdd88: LeaveFrame
    //     0xbbdd88: mov             SP, fp
    //     0xbbdd8c: ldp             fp, lr, [SP], #0x10
    // 0xbbdd90: ret
    //     0xbbdd90: ret             
    // 0xbbdd94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbdd94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbdd98: b               #0xbbd3b8
    // 0xbbdd9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbdd9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbdda0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbdda0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbdda4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbdda4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbdda8: r9 = _animationController
    //     0xbbdda8: add             x9, PP, #0x54, lsl #12  ; [pp+0x54528] Field <_ExpandablePaymentMethodWidgetState@**********._animationController@**********>: late (offset: 0x28)
    //     0xbbddac: ldr             x9, [x9, #0x528]
    // 0xbbddb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbddb0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbddb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbddb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbddb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbddb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbddbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbddbc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbddc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbddc0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbddc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbddc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbbdde0, size: 0x44
    // 0xbbdde0: EnterFrame
    //     0xbbdde0: stp             fp, lr, [SP, #-0x10]!
    //     0xbbdde4: mov             fp, SP
    // 0xbbdde8: ldr             x0, [fp, #0x20]
    // 0xbbddec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbddec: ldur            w1, [x0, #0x17]
    // 0xbbddf0: DecompressPointer r1
    //     0xbbddf0: add             x1, x1, HEAP, lsl #32
    // 0xbbddf4: CheckStackOverflow
    //     0xbbddf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbddf8: cmp             SP, x16
    //     0xbbddfc: b.ls            #0xbbde1c
    // 0xbbde00: LoadField: r0 = r1->field_f
    //     0xbbde00: ldur            w0, [x1, #0xf]
    // 0xbbde04: DecompressPointer r0
    //     0xbbde04: add             x0, x0, HEAP, lsl #32
    // 0xbbde08: mov             x1, x0
    // 0xbbde0c: r0 = _buildDottedDivider()
    //     0xbbde0c: bl              #0xbbde24  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildDottedDivider
    // 0xbbde10: LeaveFrame
    //     0xbbde10: mov             SP, fp
    //     0xbbde14: ldp             fp, lr, [SP], #0x10
    // 0xbbde18: ret
    //     0xbbde18: ret             
    // 0xbbde1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbde1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbde20: b               #0xbbde00
  }
  _ _buildDottedDivider(/* No info */) {
    // ** addr: 0xbbde24, size: 0x98
    // 0xbbde24: EnterFrame
    //     0xbbde24: stp             fp, lr, [SP, #-0x10]!
    //     0xbbde28: mov             fp, SP
    // 0xbbde2c: AllocStack(0x20)
    //     0xbbde2c: sub             SP, SP, #0x20
    // 0xbbde30: CheckStackOverflow
    //     0xbbde30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbde34: cmp             SP, x16
    //     0xbbde38: b.ls            #0xbbdeb0
    // 0xbbde3c: LoadField: r0 = r1->field_f
    //     0xbbde3c: ldur            w0, [x1, #0xf]
    // 0xbbde40: DecompressPointer r0
    //     0xbbde40: add             x0, x0, HEAP, lsl #32
    // 0xbbde44: cmp             w0, NULL
    // 0xbbde48: b.eq            #0xbbdeb8
    // 0xbbde4c: mov             x1, x0
    // 0xbbde50: r0 = of()
    //     0xbbde50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbde54: LoadField: r1 = r0->field_5b
    //     0xbbde54: ldur            w1, [x0, #0x5b]
    // 0xbbde58: DecompressPointer r1
    //     0xbbde58: add             x1, x1, HEAP, lsl #32
    // 0xbbde5c: r0 = LoadClassIdInstr(r1)
    //     0xbbde5c: ldur            x0, [x1, #-1]
    //     0xbbde60: ubfx            x0, x0, #0xc, #0x14
    // 0xbbde64: d0 = 0.100000
    //     0xbbde64: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbbde68: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbbde68: sub             lr, x0, #0xffa
    //     0xbbde6c: ldr             lr, [x21, lr, lsl #3]
    //     0xbbde70: blr             lr
    // 0xbbde74: stur            x0, [fp, #-8]
    // 0xbbde78: r0 = Container()
    //     0xbbde78: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbde7c: stur            x0, [fp, #-0x10]
    // 0xbbde80: r16 = 0.300000
    //     0xbbde80: add             x16, PP, #0x54, lsl #12  ; [pp+0x54530] 0.3
    //     0xbbde84: ldr             x16, [x16, #0x530]
    // 0xbbde88: ldur            lr, [fp, #-8]
    // 0xbbde8c: stp             lr, x16, [SP]
    // 0xbbde90: mov             x1, x0
    // 0xbbde94: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, height, 0x1, null]
    //     0xbbde94: add             x4, PP, #0x40, lsl #12  ; [pp+0x40750] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "height", 0x1, Null]
    //     0xbbde98: ldr             x4, [x4, #0x750]
    // 0xbbde9c: r0 = Container()
    //     0xbbde9c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbdea0: ldur            x0, [fp, #-0x10]
    // 0xbbdea4: LeaveFrame
    //     0xbbdea4: mov             SP, fp
    //     0xbbdea8: ldp             fp, lr, [SP], #0x10
    // 0xbbdeac: ret
    //     0xbbdeac: ret             
    // 0xbbdeb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbdeb0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbdeb4: b               #0xbbde3c
    // 0xbbdeb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbdeb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbbdebc, size: 0x110
    // 0xbbdebc: EnterFrame
    //     0xbbdebc: stp             fp, lr, [SP, #-0x10]!
    //     0xbbdec0: mov             fp, SP
    // 0xbbdec4: AllocStack(0x8)
    //     0xbbdec4: sub             SP, SP, #8
    // 0xbbdec8: SetupParameters()
    //     0xbbdec8: ldr             x0, [fp, #0x20]
    //     0xbbdecc: ldur            w1, [x0, #0x17]
    //     0xbbded0: add             x1, x1, HEAP, lsl #32
    // 0xbbded4: CheckStackOverflow
    //     0xbbded4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbded8: cmp             SP, x16
    //     0xbbdedc: b.ls            #0xbbdfbc
    // 0xbbdee0: LoadField: r2 = r1->field_f
    //     0xbbdee0: ldur            w2, [x1, #0xf]
    // 0xbbdee4: DecompressPointer r2
    //     0xbbdee4: add             x2, x2, HEAP, lsl #32
    // 0xbbdee8: LoadField: r0 = r2->field_b
    //     0xbbdee8: ldur            w0, [x2, #0xb]
    // 0xbbdeec: DecompressPointer r0
    //     0xbbdeec: add             x0, x0, HEAP, lsl #32
    // 0xbbdef0: cmp             w0, NULL
    // 0xbbdef4: b.eq            #0xbbdfc4
    // 0xbbdef8: LoadField: r1 = r0->field_b
    //     0xbbdef8: ldur            w1, [x0, #0xb]
    // 0xbbdefc: DecompressPointer r1
    //     0xbbdefc: add             x1, x1, HEAP, lsl #32
    // 0xbbdf00: LoadField: r0 = r1->field_b
    //     0xbbdf00: ldur            w0, [x1, #0xb]
    // 0xbbdf04: DecompressPointer r0
    //     0xbbdf04: add             x0, x0, HEAP, lsl #32
    // 0xbbdf08: cmp             w0, NULL
    // 0xbbdf0c: b.ne            #0xbbdf1c
    // 0xbbdf10: ldr             x4, [fp, #0x10]
    // 0xbbdf14: r0 = Null
    //     0xbbdf14: mov             x0, NULL
    // 0xbbdf18: b               #0xbbdf78
    // 0xbbdf1c: LoadField: r3 = r0->field_27
    //     0xbbdf1c: ldur            w3, [x0, #0x27]
    // 0xbbdf20: DecompressPointer r3
    //     0xbbdf20: add             x3, x3, HEAP, lsl #32
    // 0xbbdf24: cmp             w3, NULL
    // 0xbbdf28: b.ne            #0xbbdf38
    // 0xbbdf2c: ldr             x4, [fp, #0x10]
    // 0xbbdf30: r0 = Null
    //     0xbbdf30: mov             x0, NULL
    // 0xbbdf34: b               #0xbbdf78
    // 0xbbdf38: ldr             x4, [fp, #0x10]
    // 0xbbdf3c: LoadField: r0 = r3->field_b
    //     0xbbdf3c: ldur            w0, [x3, #0xb]
    // 0xbbdf40: r5 = LoadInt32Instr(r4)
    //     0xbbdf40: sbfx            x5, x4, #1, #0x1f
    //     0xbbdf44: tbz             w4, #0, #0xbbdf4c
    //     0xbbdf48: ldur            x5, [x4, #7]
    // 0xbbdf4c: r1 = LoadInt32Instr(r0)
    //     0xbbdf4c: sbfx            x1, x0, #1, #0x1f
    // 0xbbdf50: mov             x0, x1
    // 0xbbdf54: mov             x1, x5
    // 0xbbdf58: cmp             x1, x0
    // 0xbbdf5c: b.hs            #0xbbdfc8
    // 0xbbdf60: LoadField: r0 = r3->field_f
    //     0xbbdf60: ldur            w0, [x3, #0xf]
    // 0xbbdf64: DecompressPointer r0
    //     0xbbdf64: add             x0, x0, HEAP, lsl #32
    // 0xbbdf68: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbbdf68: add             x16, x0, x5, lsl #2
    //     0xbbdf6c: ldur            w1, [x16, #0xf]
    // 0xbbdf70: DecompressPointer r1
    //     0xbbdf70: add             x1, x1, HEAP, lsl #32
    // 0xbbdf74: mov             x0, x1
    // 0xbbdf78: r3 = LoadInt32Instr(r4)
    //     0xbbdf78: sbfx            x3, x4, #1, #0x1f
    //     0xbbdf7c: tbz             w4, #0, #0xbbdf84
    //     0xbbdf80: ldur            x3, [x4, #7]
    // 0xbbdf84: mov             x1, x2
    // 0xbbdf88: mov             x2, x0
    // 0xbbdf8c: ldr             x5, [fp, #0x18]
    // 0xbbdf90: r0 = partialCodPaymentMethodCard()
    //     0xbbdf90: bl              #0xbbdfcc  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard
    // 0xbbdf94: stur            x0, [fp, #-8]
    // 0xbbdf98: r0 = Padding()
    //     0xbbdf98: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbdf9c: r1 = Instance_EdgeInsets
    //     0xbbdf9c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xbbdfa0: ldr             x1, [x1, #0xb00]
    // 0xbbdfa4: StoreField: r0->field_f = r1
    //     0xbbdfa4: stur            w1, [x0, #0xf]
    // 0xbbdfa8: ldur            x1, [fp, #-8]
    // 0xbbdfac: StoreField: r0->field_b = r1
    //     0xbbdfac: stur            w1, [x0, #0xb]
    // 0xbbdfb0: LeaveFrame
    //     0xbbdfb0: mov             SP, fp
    //     0xbbdfb4: ldp             fp, lr, [SP], #0x10
    // 0xbbdfb8: ret
    //     0xbbdfb8: ret             
    // 0xbbdfbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbdfbc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbdfc0: b               #0xbbdee0
    // 0xbbdfc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbdfc4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbdfc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbdfc8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ partialCodPaymentMethodCard(/* No info */) {
    // ** addr: 0xbbdfcc, size: 0x3e4
    // 0xbbdfcc: EnterFrame
    //     0xbbdfcc: stp             fp, lr, [SP, #-0x10]!
    //     0xbbdfd0: mov             fp, SP
    // 0xbbdfd4: AllocStack(0x60)
    //     0xbbdfd4: sub             SP, SP, #0x60
    // 0xbbdfd8: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xbbdfd8: mov             x0, x1
    //     0xbbdfdc: stur            x1, [fp, #-8]
    //     0xbbdfe0: mov             x1, x5
    //     0xbbdfe4: stur            x2, [fp, #-0x10]
    //     0xbbdfe8: stur            x3, [fp, #-0x18]
    //     0xbbdfec: stur            x5, [fp, #-0x20]
    // 0xbbdff0: CheckStackOverflow
    //     0xbbdff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbdff4: cmp             SP, x16
    //     0xbbdff8: b.ls            #0xbbe384
    // 0xbbdffc: r1 = 3
    //     0xbbdffc: movz            x1, #0x3
    // 0xbbe000: r0 = AllocateContext()
    //     0xbbe000: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbe004: mov             x4, x0
    // 0xbbe008: ldur            x3, [fp, #-8]
    // 0xbbe00c: stur            x4, [fp, #-0x30]
    // 0xbbe010: StoreField: r4->field_f = r3
    //     0xbbe010: stur            w3, [x4, #0xf]
    // 0xbbe014: ldur            x5, [fp, #-0x10]
    // 0xbbe018: StoreField: r4->field_13 = r5
    //     0xbbe018: stur            w5, [x4, #0x13]
    // 0xbbe01c: ldur            x6, [fp, #-0x18]
    // 0xbbe020: r0 = BoxInt64Instr(r6)
    //     0xbbe020: sbfiz           x0, x6, #1, #0x1f
    //     0xbbe024: cmp             x6, x0, asr #1
    //     0xbbe028: b.eq            #0xbbe034
    //     0xbbe02c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbe030: stur            x6, [x0, #7]
    // 0xbbe034: ArrayStore: r4[0] = r0  ; List_4
    //     0xbbe034: stur            w0, [x4, #0x17]
    // 0xbbe038: cmp             w5, NULL
    // 0xbbe03c: b.ne            #0xbbe048
    // 0xbbe040: r0 = Null
    //     0xbbe040: mov             x0, NULL
    // 0xbbe044: b               #0xbbe050
    // 0xbbe048: LoadField: r0 = r5->field_7
    //     0xbbe048: ldur            w0, [x5, #7]
    // 0xbbe04c: DecompressPointer r0
    //     0xbbe04c: add             x0, x0, HEAP, lsl #32
    // 0xbbe050: cmp             w0, NULL
    // 0xbbe054: b.ne            #0xbbe05c
    // 0xbbe058: r0 = ""
    //     0xbbe058: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbe05c: stur            x0, [fp, #-0x28]
    // 0xbbe060: r1 = Function '<anonymous closure>':.
    //     0xbbe060: add             x1, PP, #0x54, lsl #12  ; [pp+0x54538] AnonymousClosure: (0xbbe5e0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard (0xbbdfcc)
    //     0xbbe064: ldr             x1, [x1, #0x538]
    // 0xbbe068: r2 = Null
    //     0xbbe068: mov             x2, NULL
    // 0xbbe06c: r0 = AllocateClosure()
    //     0xbbe06c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbe070: stur            x0, [fp, #-0x38]
    // 0xbbe074: r0 = CachedNetworkImage()
    //     0xbbe074: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbbe078: stur            x0, [fp, #-0x40]
    // 0xbbe07c: r16 = 24.000000
    //     0xbbe07c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbbe080: ldr             x16, [x16, #0xba8]
    // 0xbbe084: r30 = 24.000000
    //     0xbbe084: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbbe088: ldr             lr, [lr, #0xba8]
    // 0xbbe08c: stp             lr, x16, [SP, #8]
    // 0xbbe090: ldur            x16, [fp, #-0x38]
    // 0xbbe094: str             x16, [SP]
    // 0xbbe098: mov             x1, x0
    // 0xbbe09c: ldur            x2, [fp, #-0x28]
    // 0xbbe0a0: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, height, 0x2, width, 0x3, null]
    //     0xbbe0a0: add             x4, PP, #0x54, lsl #12  ; [pp+0x54540] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xbbe0a4: ldr             x4, [x4, #0x540]
    // 0xbbe0a8: r0 = CachedNetworkImage()
    //     0xbbe0a8: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbbe0ac: ldur            x0, [fp, #-0x10]
    // 0xbbe0b0: cmp             w0, NULL
    // 0xbbe0b4: b.ne            #0xbbe0c0
    // 0xbbe0b8: r1 = Null
    //     0xbbe0b8: mov             x1, NULL
    // 0xbbe0bc: b               #0xbbe0c8
    // 0xbbe0c0: LoadField: r1 = r0->field_b
    //     0xbbe0c0: ldur            w1, [x0, #0xb]
    // 0xbbe0c4: DecompressPointer r1
    //     0xbbe0c4: add             x1, x1, HEAP, lsl #32
    // 0xbbe0c8: cmp             w1, NULL
    // 0xbbe0cc: b.ne            #0xbbe0d8
    // 0xbbe0d0: r3 = ""
    //     0xbbe0d0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbe0d4: b               #0xbbe0dc
    // 0xbbe0d8: mov             x3, x1
    // 0xbbe0dc: ldur            x2, [fp, #-8]
    // 0xbbe0e0: ldur            x1, [fp, #-0x20]
    // 0xbbe0e4: stur            x3, [fp, #-0x28]
    // 0xbbe0e8: r0 = of()
    //     0xbbe0e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbe0ec: LoadField: r1 = r0->field_87
    //     0xbbe0ec: ldur            w1, [x0, #0x87]
    // 0xbbe0f0: DecompressPointer r1
    //     0xbbe0f0: add             x1, x1, HEAP, lsl #32
    // 0xbbe0f4: LoadField: r0 = r1->field_2b
    //     0xbbe0f4: ldur            w0, [x1, #0x2b]
    // 0xbbe0f8: DecompressPointer r0
    //     0xbbe0f8: add             x0, x0, HEAP, lsl #32
    // 0xbbe0fc: stur            x0, [fp, #-0x38]
    // 0xbbe100: r1 = Instance_Color
    //     0xbbe100: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbe104: d0 = 0.700000
    //     0xbbe104: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbbe108: ldr             d0, [x17, #0xf48]
    // 0xbbe10c: r0 = withOpacity()
    //     0xbbe10c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbe110: r16 = 14.000000
    //     0xbbe110: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbe114: ldr             x16, [x16, #0x1d8]
    // 0xbbe118: stp             x16, x0, [SP]
    // 0xbbe11c: ldur            x1, [fp, #-0x38]
    // 0xbbe120: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbe120: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbe124: ldr             x4, [x4, #0x9b8]
    // 0xbbe128: r0 = copyWith()
    //     0xbbe128: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbe12c: stur            x0, [fp, #-0x38]
    // 0xbbe130: r0 = Text()
    //     0xbbe130: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbe134: mov             x1, x0
    // 0xbbe138: ldur            x0, [fp, #-0x28]
    // 0xbbe13c: stur            x1, [fp, #-0x48]
    // 0xbbe140: StoreField: r1->field_b = r0
    //     0xbbe140: stur            w0, [x1, #0xb]
    // 0xbbe144: ldur            x0, [fp, #-0x38]
    // 0xbbe148: StoreField: r1->field_13 = r0
    //     0xbbe148: stur            w0, [x1, #0x13]
    // 0xbbe14c: r0 = Padding()
    //     0xbbe14c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbe150: mov             x1, x0
    // 0xbbe154: r0 = Instance_EdgeInsets
    //     0xbbe154: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbbe158: ldr             x0, [x0, #0xa78]
    // 0xbbe15c: stur            x1, [fp, #-0x28]
    // 0xbbe160: StoreField: r1->field_f = r0
    //     0xbbe160: stur            w0, [x1, #0xf]
    // 0xbbe164: ldur            x0, [fp, #-0x48]
    // 0xbbe168: StoreField: r1->field_b = r0
    //     0xbbe168: stur            w0, [x1, #0xb]
    // 0xbbe16c: ldur            x2, [fp, #-8]
    // 0xbbe170: LoadField: r0 = r2->field_1f
    //     0xbbe170: ldur            w0, [x2, #0x1f]
    // 0xbbe174: DecompressPointer r0
    //     0xbbe174: add             x0, x0, HEAP, lsl #32
    // 0xbbe178: r16 = Sentinel
    //     0xbbe178: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbe17c: cmp             w0, w16
    // 0xbbe180: b.eq            #0xbbe38c
    // 0xbbe184: ldur            x3, [fp, #-0x10]
    // 0xbbe188: cmp             w3, NULL
    // 0xbbe18c: b.ne            #0xbbe198
    // 0xbbe190: r3 = Null
    //     0xbbe190: mov             x3, NULL
    // 0xbbe194: b               #0xbbe1a4
    // 0xbbe198: LoadField: r4 = r3->field_f
    //     0xbbe198: ldur            w4, [x3, #0xf]
    // 0xbbe19c: DecompressPointer r4
    //     0xbbe19c: add             x4, x4, HEAP, lsl #32
    // 0xbbe1a0: mov             x3, x4
    // 0xbbe1a4: r4 = LoadClassIdInstr(r0)
    //     0xbbe1a4: ldur            x4, [x0, #-1]
    //     0xbbe1a8: ubfx            x4, x4, #0xc, #0x14
    // 0xbbe1ac: stp             x3, x0, [SP]
    // 0xbbe1b0: mov             x0, x4
    // 0xbbe1b4: mov             lr, x0
    // 0xbbe1b8: ldr             lr, [x21, lr, lsl #3]
    // 0xbbe1bc: blr             lr
    // 0xbbe1c0: tbnz            w0, #4, #0xbbe228
    // 0xbbe1c4: ldur            x0, [fp, #-8]
    // 0xbbe1c8: ldur            x1, [fp, #-0x18]
    // 0xbbe1cc: LoadField: r2 = r0->field_23
    //     0xbbe1cc: ldur            w2, [x0, #0x23]
    // 0xbbe1d0: DecompressPointer r2
    //     0xbbe1d0: add             x2, x2, HEAP, lsl #32
    // 0xbbe1d4: r16 = Sentinel
    //     0xbbe1d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbe1d8: cmp             w2, w16
    // 0xbbe1dc: b.eq            #0xbbe398
    // 0xbbe1e0: r3 = LoadInt32Instr(r2)
    //     0xbbe1e0: sbfx            x3, x2, #1, #0x1f
    //     0xbbe1e4: tbz             w2, #0, #0xbbe1ec
    //     0xbbe1e8: ldur            x3, [x2, #7]
    // 0xbbe1ec: cmp             x3, x1
    // 0xbbe1f0: b.ne            #0xbbe228
    // 0xbbe1f4: LoadField: r1 = r0->field_1b
    //     0xbbe1f4: ldur            w1, [x0, #0x1b]
    // 0xbbe1f8: DecompressPointer r1
    //     0xbbe1f8: add             x1, x1, HEAP, lsl #32
    // 0xbbe1fc: r16 = Sentinel
    //     0xbbe1fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbe200: cmp             w1, w16
    // 0xbbe204: b.eq            #0xbbe3a4
    // 0xbbe208: r16 = "partial-cod"
    //     0xbbe208: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbbe20c: ldr             x16, [x16, #0x830]
    // 0xbbe210: stp             x1, x16, [SP]
    // 0xbbe214: r0 = ==()
    //     0xbbe214: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbbe218: tbnz            w0, #4, #0xbbe228
    // 0xbbe21c: r3 = Instance_IconData
    //     0xbbe21c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xbbe220: ldr             x3, [x3, #0x30]
    // 0xbbe224: b               #0xbbe230
    // 0xbbe228: r3 = Instance_IconData
    //     0xbbe228: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xbbe22c: ldr             x3, [x3, #0x38]
    // 0xbbe230: ldur            x2, [fp, #-0x40]
    // 0xbbe234: ldur            x0, [fp, #-0x28]
    // 0xbbe238: ldur            x1, [fp, #-0x20]
    // 0xbbe23c: stur            x3, [fp, #-8]
    // 0xbbe240: r0 = of()
    //     0xbbe240: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbe244: LoadField: r1 = r0->field_5b
    //     0xbbe244: ldur            w1, [x0, #0x5b]
    // 0xbbe248: DecompressPointer r1
    //     0xbbe248: add             x1, x1, HEAP, lsl #32
    // 0xbbe24c: stur            x1, [fp, #-0x10]
    // 0xbbe250: r0 = Icon()
    //     0xbbe250: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbbe254: mov             x3, x0
    // 0xbbe258: ldur            x0, [fp, #-8]
    // 0xbbe25c: stur            x3, [fp, #-0x20]
    // 0xbbe260: StoreField: r3->field_b = r0
    //     0xbbe260: stur            w0, [x3, #0xb]
    // 0xbbe264: ldur            x0, [fp, #-0x10]
    // 0xbbe268: StoreField: r3->field_23 = r0
    //     0xbbe268: stur            w0, [x3, #0x23]
    // 0xbbe26c: r1 = Null
    //     0xbbe26c: mov             x1, NULL
    // 0xbbe270: r2 = 8
    //     0xbbe270: movz            x2, #0x8
    // 0xbbe274: r0 = AllocateArray()
    //     0xbbe274: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbe278: mov             x2, x0
    // 0xbbe27c: ldur            x0, [fp, #-0x40]
    // 0xbbe280: stur            x2, [fp, #-8]
    // 0xbbe284: StoreField: r2->field_f = r0
    //     0xbbe284: stur            w0, [x2, #0xf]
    // 0xbbe288: ldur            x0, [fp, #-0x28]
    // 0xbbe28c: StoreField: r2->field_13 = r0
    //     0xbbe28c: stur            w0, [x2, #0x13]
    // 0xbbe290: r16 = Instance_Spacer
    //     0xbbe290: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbbe294: ldr             x16, [x16, #0xf0]
    // 0xbbe298: ArrayStore: r2[0] = r16  ; List_4
    //     0xbbe298: stur            w16, [x2, #0x17]
    // 0xbbe29c: ldur            x0, [fp, #-0x20]
    // 0xbbe2a0: StoreField: r2->field_1b = r0
    //     0xbbe2a0: stur            w0, [x2, #0x1b]
    // 0xbbe2a4: r1 = <Widget>
    //     0xbbe2a4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbe2a8: r0 = AllocateGrowableArray()
    //     0xbbe2a8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbe2ac: mov             x1, x0
    // 0xbbe2b0: ldur            x0, [fp, #-8]
    // 0xbbe2b4: stur            x1, [fp, #-0x10]
    // 0xbbe2b8: StoreField: r1->field_f = r0
    //     0xbbe2b8: stur            w0, [x1, #0xf]
    // 0xbbe2bc: r0 = 8
    //     0xbbe2bc: movz            x0, #0x8
    // 0xbbe2c0: StoreField: r1->field_b = r0
    //     0xbbe2c0: stur            w0, [x1, #0xb]
    // 0xbbe2c4: r0 = Row()
    //     0xbbe2c4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbe2c8: mov             x1, x0
    // 0xbbe2cc: r0 = Instance_Axis
    //     0xbbe2cc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbe2d0: stur            x1, [fp, #-8]
    // 0xbbe2d4: StoreField: r1->field_f = r0
    //     0xbbe2d4: stur            w0, [x1, #0xf]
    // 0xbbe2d8: r0 = Instance_MainAxisAlignment
    //     0xbbe2d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbbe2dc: ldr             x0, [x0, #0xa8]
    // 0xbbe2e0: StoreField: r1->field_13 = r0
    //     0xbbe2e0: stur            w0, [x1, #0x13]
    // 0xbbe2e4: r0 = Instance_MainAxisSize
    //     0xbbe2e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbe2e8: ldr             x0, [x0, #0xa10]
    // 0xbbe2ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbe2ec: stur            w0, [x1, #0x17]
    // 0xbbe2f0: r0 = Instance_CrossAxisAlignment
    //     0xbbe2f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbe2f4: ldr             x0, [x0, #0xa18]
    // 0xbbe2f8: StoreField: r1->field_1b = r0
    //     0xbbe2f8: stur            w0, [x1, #0x1b]
    // 0xbbe2fc: r0 = Instance_VerticalDirection
    //     0xbbe2fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbe300: ldr             x0, [x0, #0xa20]
    // 0xbbe304: StoreField: r1->field_23 = r0
    //     0xbbe304: stur            w0, [x1, #0x23]
    // 0xbbe308: r0 = Instance_Clip
    //     0xbbe308: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbe30c: ldr             x0, [x0, #0x38]
    // 0xbbe310: StoreField: r1->field_2b = r0
    //     0xbbe310: stur            w0, [x1, #0x2b]
    // 0xbbe314: StoreField: r1->field_2f = rZR
    //     0xbbe314: stur            xzr, [x1, #0x2f]
    // 0xbbe318: ldur            x0, [fp, #-0x10]
    // 0xbbe31c: StoreField: r1->field_b = r0
    //     0xbbe31c: stur            w0, [x1, #0xb]
    // 0xbbe320: r0 = InkWell()
    //     0xbbe320: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbe324: mov             x3, x0
    // 0xbbe328: ldur            x0, [fp, #-8]
    // 0xbbe32c: stur            x3, [fp, #-0x10]
    // 0xbbe330: StoreField: r3->field_b = r0
    //     0xbbe330: stur            w0, [x3, #0xb]
    // 0xbbe334: ldur            x2, [fp, #-0x30]
    // 0xbbe338: r1 = Function '<anonymous closure>':.
    //     0xbbe338: add             x1, PP, #0x54, lsl #12  ; [pp+0x54548] AnonymousClosure: (0xbbe3b0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard (0xbbdfcc)
    //     0xbbe33c: ldr             x1, [x1, #0x548]
    // 0xbbe340: r0 = AllocateClosure()
    //     0xbbe340: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbe344: mov             x1, x0
    // 0xbbe348: ldur            x0, [fp, #-0x10]
    // 0xbbe34c: StoreField: r0->field_f = r1
    //     0xbbe34c: stur            w1, [x0, #0xf]
    // 0xbbe350: r1 = true
    //     0xbbe350: add             x1, NULL, #0x20  ; true
    // 0xbbe354: StoreField: r0->field_43 = r1
    //     0xbbe354: stur            w1, [x0, #0x43]
    // 0xbbe358: r2 = Instance_BoxShape
    //     0xbbe358: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbe35c: ldr             x2, [x2, #0x80]
    // 0xbbe360: StoreField: r0->field_47 = r2
    //     0xbbe360: stur            w2, [x0, #0x47]
    // 0xbbe364: StoreField: r0->field_6f = r1
    //     0xbbe364: stur            w1, [x0, #0x6f]
    // 0xbbe368: r2 = false
    //     0xbbe368: add             x2, NULL, #0x30  ; false
    // 0xbbe36c: StoreField: r0->field_73 = r2
    //     0xbbe36c: stur            w2, [x0, #0x73]
    // 0xbbe370: StoreField: r0->field_83 = r1
    //     0xbbe370: stur            w1, [x0, #0x83]
    // 0xbbe374: StoreField: r0->field_7b = r2
    //     0xbbe374: stur            w2, [x0, #0x7b]
    // 0xbbe378: LeaveFrame
    //     0xbbe378: mov             SP, fp
    //     0xbbe37c: ldp             fp, lr, [SP], #0x10
    // 0xbbe380: ret
    //     0xbbe380: ret             
    // 0xbbe384: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbe384: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbe388: b               #0xbbdffc
    // 0xbbe38c: r9 = selectedPaymentMethod
    //     0xbbe38c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0xbbe390: ldr             x9, [x9, #0x550]
    // 0xbbe394: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbe394: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbe398: r9 = paymentSelectedIndex
    //     0xbbe398: add             x9, PP, #0x54, lsl #12  ; [pp+0x54558] Field <<EMAIL>>: late (offset: 0x24)
    //     0xbbe39c: ldr             x9, [x9, #0x558]
    // 0xbbe3a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbe3a0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbe3a4: r9 = selectedPaymentMode
    //     0xbbe3a4: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xbbe3a8: ldr             x9, [x9, #0x560]
    // 0xbbe3ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbe3ac: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbe3b0, size: 0x174
    // 0xbbe3b0: EnterFrame
    //     0xbbe3b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbbe3b4: mov             fp, SP
    // 0xbbe3b8: AllocStack(0x38)
    //     0xbbe3b8: sub             SP, SP, #0x38
    // 0xbbe3bc: SetupParameters()
    //     0xbbe3bc: ldr             x0, [fp, #0x10]
    //     0xbbe3c0: ldur            w3, [x0, #0x17]
    //     0xbbe3c4: add             x3, x3, HEAP, lsl #32
    //     0xbbe3c8: stur            x3, [fp, #-0x10]
    // 0xbbe3cc: CheckStackOverflow
    //     0xbbe3cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbe3d0: cmp             SP, x16
    //     0xbbe3d4: b.ls            #0xbbe514
    // 0xbbe3d8: LoadField: r0 = r3->field_f
    //     0xbbe3d8: ldur            w0, [x3, #0xf]
    // 0xbbe3dc: DecompressPointer r0
    //     0xbbe3dc: add             x0, x0, HEAP, lsl #32
    // 0xbbe3e0: mov             x2, x3
    // 0xbbe3e4: stur            x0, [fp, #-8]
    // 0xbbe3e8: r1 = Function '<anonymous closure>':.
    //     0xbbe3e8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54568] AnonymousClosure: (0xbbe524), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard (0xbbdfcc)
    //     0xbbe3ec: ldr             x1, [x1, #0x568]
    // 0xbbe3f0: r0 = AllocateClosure()
    //     0xbbe3f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbe3f4: ldur            x1, [fp, #-8]
    // 0xbbe3f8: mov             x2, x0
    // 0xbbe3fc: r0 = setState()
    //     0xbbe3fc: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbbe400: ldur            x0, [fp, #-0x10]
    // 0xbbe404: LoadField: r1 = r0->field_f
    //     0xbbe404: ldur            w1, [x0, #0xf]
    // 0xbbe408: DecompressPointer r1
    //     0xbbe408: add             x1, x1, HEAP, lsl #32
    // 0xbbe40c: LoadField: r2 = r1->field_b
    //     0xbbe40c: ldur            w2, [x1, #0xb]
    // 0xbbe410: DecompressPointer r2
    //     0xbbe410: add             x2, x2, HEAP, lsl #32
    // 0xbbe414: cmp             w2, NULL
    // 0xbbe418: b.eq            #0xbbe51c
    // 0xbbe41c: LoadField: r1 = r0->field_13
    //     0xbbe41c: ldur            w1, [x0, #0x13]
    // 0xbbe420: DecompressPointer r1
    //     0xbbe420: add             x1, x1, HEAP, lsl #32
    // 0xbbe424: cmp             w1, NULL
    // 0xbbe428: b.ne            #0xbbe434
    // 0xbbe42c: r1 = Null
    //     0xbbe42c: mov             x1, NULL
    // 0xbbe430: b               #0xbbe440
    // 0xbbe434: LoadField: r3 = r1->field_f
    //     0xbbe434: ldur            w3, [x1, #0xf]
    // 0xbbe438: DecompressPointer r3
    //     0xbbe438: add             x3, x3, HEAP, lsl #32
    // 0xbbe43c: mov             x1, x3
    // 0xbbe440: cmp             w1, NULL
    // 0xbbe444: b.ne            #0xbbe44c
    // 0xbbe448: r1 = ""
    //     0xbbe448: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbe44c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbbe44c: ldur            w3, [x0, #0x17]
    // 0xbbe450: DecompressPointer r3
    //     0xbbe450: add             x3, x3, HEAP, lsl #32
    // 0xbbe454: LoadField: r4 = r2->field_f
    //     0xbbe454: ldur            w4, [x2, #0xf]
    // 0xbbe458: DecompressPointer r4
    //     0xbbe458: add             x4, x4, HEAP, lsl #32
    // 0xbbe45c: r16 = "partial-cod"
    //     0xbbe45c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbbe460: ldr             x16, [x16, #0x830]
    // 0xbbe464: stp             x16, x4, [SP, #0x18]
    // 0xbbe468: stp             x3, x1, [SP, #8]
    // 0xbbe46c: r16 = true
    //     0xbbe46c: add             x16, NULL, #0x20  ; true
    // 0xbbe470: str             x16, [SP]
    // 0xbbe474: r4 = 0
    //     0xbbe474: movz            x4, #0
    // 0xbbe478: ldr             x0, [SP, #0x20]
    // 0xbbe47c: r16 = UnlinkedCall_0x613b5c
    //     0xbbe47c: add             x16, PP, #0x54, lsl #12  ; [pp+0x54570] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbe480: add             x16, x16, #0x570
    // 0xbbe484: ldp             x5, lr, [x16]
    // 0xbbe488: blr             lr
    // 0xbbe48c: ldur            x0, [fp, #-0x10]
    // 0xbbe490: LoadField: r1 = r0->field_f
    //     0xbbe490: ldur            w1, [x0, #0xf]
    // 0xbbe494: DecompressPointer r1
    //     0xbbe494: add             x1, x1, HEAP, lsl #32
    // 0xbbe498: LoadField: r2 = r1->field_b
    //     0xbbe498: ldur            w2, [x1, #0xb]
    // 0xbbe49c: DecompressPointer r2
    //     0xbbe49c: add             x2, x2, HEAP, lsl #32
    // 0xbbe4a0: cmp             w2, NULL
    // 0xbbe4a4: b.eq            #0xbbe520
    // 0xbbe4a8: LoadField: r1 = r0->field_13
    //     0xbbe4a8: ldur            w1, [x0, #0x13]
    // 0xbbe4ac: DecompressPointer r1
    //     0xbbe4ac: add             x1, x1, HEAP, lsl #32
    // 0xbbe4b0: cmp             w1, NULL
    // 0xbbe4b4: b.ne            #0xbbe4c0
    // 0xbbe4b8: r0 = Null
    //     0xbbe4b8: mov             x0, NULL
    // 0xbbe4bc: b               #0xbbe4c8
    // 0xbbe4c0: LoadField: r0 = r1->field_f
    //     0xbbe4c0: ldur            w0, [x1, #0xf]
    // 0xbbe4c4: DecompressPointer r0
    //     0xbbe4c4: add             x0, x0, HEAP, lsl #32
    // 0xbbe4c8: cmp             w0, NULL
    // 0xbbe4cc: b.ne            #0xbbe4d4
    // 0xbbe4d0: r0 = ""
    //     0xbbe4d0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbe4d4: LoadField: r1 = r2->field_23
    //     0xbbe4d4: ldur            w1, [x2, #0x23]
    // 0xbbe4d8: DecompressPointer r1
    //     0xbbe4d8: add             x1, x1, HEAP, lsl #32
    // 0xbbe4dc: r16 = "partial-cod"
    //     0xbbe4dc: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbbe4e0: ldr             x16, [x16, #0x830]
    // 0xbbe4e4: stp             x16, x1, [SP, #8]
    // 0xbbe4e8: str             x0, [SP]
    // 0xbbe4ec: r4 = 0
    //     0xbbe4ec: movz            x4, #0
    // 0xbbe4f0: ldr             x0, [SP, #0x10]
    // 0xbbe4f4: r16 = UnlinkedCall_0x613b5c
    //     0xbbe4f4: add             x16, PP, #0x54, lsl #12  ; [pp+0x54580] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbe4f8: add             x16, x16, #0x580
    // 0xbbe4fc: ldp             x5, lr, [x16]
    // 0xbbe500: blr             lr
    // 0xbbe504: r0 = Null
    //     0xbbe504: mov             x0, NULL
    // 0xbbe508: LeaveFrame
    //     0xbbe508: mov             SP, fp
    //     0xbbe50c: ldp             fp, lr, [SP], #0x10
    // 0xbbe510: ret
    //     0xbbe510: ret             
    // 0xbbe514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbe514: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbe518: b               #0xbbe3d8
    // 0xbbe51c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbe51c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbe520: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbe520: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbe524, size: 0xbc
    // 0xbbe524: EnterFrame
    //     0xbbe524: stp             fp, lr, [SP, #-0x10]!
    //     0xbbe528: mov             fp, SP
    // 0xbbe52c: r1 = "partial-cod"
    //     0xbbe52c: add             x1, PP, #0x3c, lsl #12  ; [pp+0x3c830] "partial-cod"
    //     0xbbe530: ldr             x1, [x1, #0x830]
    // 0xbbe534: ldr             x2, [fp, #0x10]
    // 0xbbe538: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbbe538: ldur            w3, [x2, #0x17]
    // 0xbbe53c: DecompressPointer r3
    //     0xbbe53c: add             x3, x3, HEAP, lsl #32
    // 0xbbe540: LoadField: r2 = r3->field_f
    //     0xbbe540: ldur            w2, [x3, #0xf]
    // 0xbbe544: DecompressPointer r2
    //     0xbbe544: add             x2, x2, HEAP, lsl #32
    // 0xbbe548: StoreField: r2->field_1b = r1
    //     0xbbe548: stur            w1, [x2, #0x1b]
    // 0xbbe54c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbbe54c: ldur            w0, [x3, #0x17]
    // 0xbbe550: DecompressPointer r0
    //     0xbbe550: add             x0, x0, HEAP, lsl #32
    // 0xbbe554: StoreField: r2->field_23 = r0
    //     0xbbe554: stur            w0, [x2, #0x23]
    //     0xbbe558: tbz             w0, #0, #0xbbe574
    //     0xbbe55c: ldurb           w16, [x2, #-1]
    //     0xbbe560: ldurb           w17, [x0, #-1]
    //     0xbbe564: and             x16, x17, x16, lsr #2
    //     0xbbe568: tst             x16, HEAP, lsr #32
    //     0xbbe56c: b.eq            #0xbbe574
    //     0xbbe570: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbbe574: LoadField: r1 = r3->field_13
    //     0xbbe574: ldur            w1, [x3, #0x13]
    // 0xbbe578: DecompressPointer r1
    //     0xbbe578: add             x1, x1, HEAP, lsl #32
    // 0xbbe57c: cmp             w1, NULL
    // 0xbbe580: b.ne            #0xbbe58c
    // 0xbbe584: r1 = Null
    //     0xbbe584: mov             x1, NULL
    // 0xbbe588: b               #0xbbe598
    // 0xbbe58c: LoadField: r3 = r1->field_f
    //     0xbbe58c: ldur            w3, [x1, #0xf]
    // 0xbbe590: DecompressPointer r3
    //     0xbbe590: add             x3, x3, HEAP, lsl #32
    // 0xbbe594: mov             x1, x3
    // 0xbbe598: cmp             w1, NULL
    // 0xbbe59c: b.ne            #0xbbe5a8
    // 0xbbe5a0: r0 = ""
    //     0xbbe5a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbe5a4: b               #0xbbe5ac
    // 0xbbe5a8: mov             x0, x1
    // 0xbbe5ac: r1 = true
    //     0xbbe5ac: add             x1, NULL, #0x20  ; true
    // 0xbbe5b0: StoreField: r2->field_1f = r0
    //     0xbbe5b0: stur            w0, [x2, #0x1f]
    //     0xbbe5b4: ldurb           w16, [x2, #-1]
    //     0xbbe5b8: ldurb           w17, [x0, #-1]
    //     0xbbe5bc: and             x16, x17, x16, lsr #2
    //     0xbbe5c0: tst             x16, HEAP, lsr #32
    //     0xbbe5c4: b.eq            #0xbbe5cc
    //     0xbbe5c8: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbbe5cc: StoreField: r2->field_2f = r1
    //     0xbbe5cc: stur            w1, [x2, #0x2f]
    // 0xbbe5d0: r0 = Null
    //     0xbbe5d0: mov             x0, NULL
    // 0xbbe5d4: LeaveFrame
    //     0xbbe5d4: mov             SP, fp
    //     0xbbe5d8: ldp             fp, lr, [SP], #0x10
    // 0xbbe5dc: ret
    //     0xbbe5dc: ret             
  }
  [closure] Icon <anonymous closure>(dynamic, BuildContext, String, Object) {
    // ** addr: 0xbbe5e0, size: 0xc
    // 0xbbe5e0: r0 = Instance_Icon
    //     0xbbe5e0: add             x0, PP, #0x54, lsl #12  ; [pp+0x54590] Obj!Icon@d667f1
    //     0xbbe5e4: ldr             x0, [x0, #0x590]
    // 0xbbe5e8: ret
    //     0xbbe5e8: ret             
  }
  [closure] void _toggleAccordion(dynamic) {
    // ** addr: 0xbbe5ec, size: 0x38
    // 0xbbe5ec: EnterFrame
    //     0xbbe5ec: stp             fp, lr, [SP, #-0x10]!
    //     0xbbe5f0: mov             fp, SP
    // 0xbbe5f4: ldr             x0, [fp, #0x10]
    // 0xbbe5f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbe5f8: ldur            w1, [x0, #0x17]
    // 0xbbe5fc: DecompressPointer r1
    //     0xbbe5fc: add             x1, x1, HEAP, lsl #32
    // 0xbbe600: CheckStackOverflow
    //     0xbbe600: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbe604: cmp             SP, x16
    //     0xbbe608: b.ls            #0xbbe61c
    // 0xbbe60c: r0 = _toggleAccordion()
    //     0xbbe60c: bl              #0xbbe624  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_toggleAccordion
    // 0xbbe610: LeaveFrame
    //     0xbbe610: mov             SP, fp
    //     0xbbe614: ldp             fp, lr, [SP], #0x10
    // 0xbbe618: ret
    //     0xbbe618: ret             
    // 0xbbe61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbe61c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbe620: b               #0xbbe60c
  }
  _ _toggleAccordion(/* No info */) {
    // ** addr: 0xbbe624, size: 0x118
    // 0xbbe624: EnterFrame
    //     0xbbe624: stp             fp, lr, [SP, #-0x10]!
    //     0xbbe628: mov             fp, SP
    // 0xbbe62c: AllocStack(0x20)
    //     0xbbe62c: sub             SP, SP, #0x20
    // 0xbbe630: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xbbe630: stur            x1, [fp, #-8]
    // 0xbbe634: CheckStackOverflow
    //     0xbbe634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbe638: cmp             SP, x16
    //     0xbbe63c: b.ls            #0xbbe718
    // 0xbbe640: r1 = 1
    //     0xbbe640: movz            x1, #0x1
    // 0xbbe644: r0 = AllocateContext()
    //     0xbbe644: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbe648: mov             x1, x0
    // 0xbbe64c: ldur            x0, [fp, #-8]
    // 0xbbe650: StoreField: r1->field_f = r0
    //     0xbbe650: stur            w0, [x1, #0xf]
    // 0xbbe654: mov             x2, x1
    // 0xbbe658: r1 = Function '<anonymous closure>':.
    //     0xbbe658: add             x1, PP, #0x54, lsl #12  ; [pp+0x54598] AnonymousClosure: (0xbbe73c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_toggleAccordion (0xbbe624)
    //     0xbbe65c: ldr             x1, [x1, #0x598]
    // 0xbbe660: r0 = AllocateClosure()
    //     0xbbe660: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbe664: ldur            x1, [fp, #-8]
    // 0xbbe668: mov             x2, x0
    // 0xbbe66c: r0 = setState()
    //     0xbbe66c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbbe670: ldur            x0, [fp, #-8]
    // 0xbbe674: LoadField: r1 = r0->field_2b
    //     0xbbe674: ldur            w1, [x0, #0x2b]
    // 0xbbe678: DecompressPointer r1
    //     0xbbe678: add             x1, x1, HEAP, lsl #32
    // 0xbbe67c: tbnz            w1, #4, #0xbbe6a0
    // 0xbbe680: LoadField: r1 = r0->field_27
    //     0xbbe680: ldur            w1, [x0, #0x27]
    // 0xbbe684: DecompressPointer r1
    //     0xbbe684: add             x1, x1, HEAP, lsl #32
    // 0xbbe688: r16 = Sentinel
    //     0xbbe688: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbe68c: cmp             w1, w16
    // 0xbbe690: b.eq            #0xbbe720
    // 0xbbe694: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbbe694: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbbe698: r0 = forward()
    //     0xbbe698: bl              #0x79fb6c  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xbbe69c: b               #0xbbe6bc
    // 0xbbe6a0: LoadField: r1 = r0->field_27
    //     0xbbe6a0: ldur            w1, [x0, #0x27]
    // 0xbbe6a4: DecompressPointer r1
    //     0xbbe6a4: add             x1, x1, HEAP, lsl #32
    // 0xbbe6a8: r16 = Sentinel
    //     0xbbe6a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbe6ac: cmp             w1, w16
    // 0xbbe6b0: b.eq            #0xbbe72c
    // 0xbbe6b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbbe6b4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbbe6b8: r0 = reverse()
    //     0xbbe6b8: bl              #0x7f7508  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xbbe6bc: ldur            x0, [fp, #-8]
    // 0xbbe6c0: LoadField: r1 = r0->field_b
    //     0xbbe6c0: ldur            w1, [x0, #0xb]
    // 0xbbe6c4: DecompressPointer r1
    //     0xbbe6c4: add             x1, x1, HEAP, lsl #32
    // 0xbbe6c8: cmp             w1, NULL
    // 0xbbe6cc: b.eq            #0xbbe738
    // 0xbbe6d0: LoadField: r0 = r1->field_23
    //     0xbbe6d0: ldur            w0, [x1, #0x23]
    // 0xbbe6d4: DecompressPointer r0
    //     0xbbe6d4: add             x0, x0, HEAP, lsl #32
    // 0xbbe6d8: r16 = "partial_cod_accordion_click"
    //     0xbbe6d8: add             x16, PP, #0x54, lsl #12  ; [pp+0x545a0] "partial_cod_accordion_click"
    //     0xbbe6dc: ldr             x16, [x16, #0x5a0]
    // 0xbbe6e0: stp             x16, x0, [SP, #8]
    // 0xbbe6e4: r16 = "Partial-Cod Accordion Click"
    //     0xbbe6e4: add             x16, PP, #0x54, lsl #12  ; [pp+0x545a8] "Partial-Cod Accordion Click"
    //     0xbbe6e8: ldr             x16, [x16, #0x5a8]
    // 0xbbe6ec: str             x16, [SP]
    // 0xbbe6f0: r4 = 0
    //     0xbbe6f0: movz            x4, #0
    // 0xbbe6f4: ldr             x0, [SP, #0x10]
    // 0xbbe6f8: r16 = UnlinkedCall_0x613b5c
    //     0xbbe6f8: add             x16, PP, #0x54, lsl #12  ; [pp+0x545b0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbe6fc: add             x16, x16, #0x5b0
    // 0xbbe700: ldp             x5, lr, [x16]
    // 0xbbe704: blr             lr
    // 0xbbe708: r0 = Null
    //     0xbbe708: mov             x0, NULL
    // 0xbbe70c: LeaveFrame
    //     0xbbe70c: mov             SP, fp
    //     0xbbe710: ldp             fp, lr, [SP], #0x10
    // 0xbbe714: ret
    //     0xbbe714: ret             
    // 0xbbe718: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbe718: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbe71c: b               #0xbbe640
    // 0xbbe720: r9 = _animationController
    //     0xbbe720: add             x9, PP, #0x54, lsl #12  ; [pp+0x54528] Field <_ExpandablePaymentMethodWidgetState@**********._animationController@**********>: late (offset: 0x28)
    //     0xbbe724: ldr             x9, [x9, #0x528]
    // 0xbbe728: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbe728: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbe72c: r9 = _animationController
    //     0xbbe72c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54528] Field <_ExpandablePaymentMethodWidgetState@**********._animationController@**********>: late (offset: 0x28)
    //     0xbbe730: ldr             x9, [x9, #0x528]
    // 0xbbe734: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbe734: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbe738: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbe738: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbe73c, size: 0x2c
    // 0xbbe73c: ldr             x1, [SP]
    // 0xbbe740: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbbe740: ldur            w2, [x1, #0x17]
    // 0xbbe744: DecompressPointer r2
    //     0xbbe744: add             x2, x2, HEAP, lsl #32
    // 0xbbe748: LoadField: r1 = r2->field_f
    //     0xbbe748: ldur            w1, [x2, #0xf]
    // 0xbbe74c: DecompressPointer r1
    //     0xbbe74c: add             x1, x1, HEAP, lsl #32
    // 0xbbe750: LoadField: r2 = r1->field_2b
    //     0xbbe750: ldur            w2, [x1, #0x2b]
    // 0xbbe754: DecompressPointer r2
    //     0xbbe754: add             x2, x2, HEAP, lsl #32
    // 0xbbe758: eor             x3, x2, #0x10
    // 0xbbe75c: StoreField: r1->field_2b = r3
    //     0xbbe75c: stur            w3, [x1, #0x2b]
    // 0xbbe760: r0 = Null
    //     0xbbe760: mov             x0, NULL
    // 0xbbe764: ret
    //     0xbbe764: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbe9f8, size: 0x7c
    // 0xbbe9f8: EnterFrame
    //     0xbbe9f8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbe9fc: mov             fp, SP
    // 0xbbea00: AllocStack(0x8)
    //     0xbbea00: sub             SP, SP, #8
    // 0xbbea04: SetupParameters()
    //     0xbbea04: ldr             x0, [fp, #0x10]
    //     0xbbea08: ldur            w1, [x0, #0x17]
    //     0xbbea0c: add             x1, x1, HEAP, lsl #32
    // 0xbbea10: CheckStackOverflow
    //     0xbbea10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbea14: cmp             SP, x16
    //     0xbbea18: b.ls            #0xbbea68
    // 0xbbea1c: LoadField: r0 = r1->field_f
    //     0xbbea1c: ldur            w0, [x1, #0xf]
    // 0xbbea20: DecompressPointer r0
    //     0xbbea20: add             x0, x0, HEAP, lsl #32
    // 0xbbea24: LoadField: r1 = r0->field_b
    //     0xbbea24: ldur            w1, [x0, #0xb]
    // 0xbbea28: DecompressPointer r1
    //     0xbbea28: add             x1, x1, HEAP, lsl #32
    // 0xbbea2c: cmp             w1, NULL
    // 0xbbea30: b.eq            #0xbbea70
    // 0xbbea34: LoadField: r0 = r1->field_27
    //     0xbbea34: ldur            w0, [x1, #0x27]
    // 0xbbea38: DecompressPointer r0
    //     0xbbea38: add             x0, x0, HEAP, lsl #32
    // 0xbbea3c: str             x0, [SP]
    // 0xbbea40: r4 = 0
    //     0xbbea40: movz            x4, #0
    // 0xbbea44: ldr             x0, [SP]
    // 0xbbea48: r16 = UnlinkedCall_0x613b5c
    //     0xbbea48: add             x16, PP, #0x54, lsl #12  ; [pp+0x545c0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbea4c: add             x16, x16, #0x5c0
    // 0xbbea50: ldp             x5, lr, [x16]
    // 0xbbea54: blr             lr
    // 0xbbea58: r0 = Null
    //     0xbbea58: mov             x0, NULL
    // 0xbbea5c: LeaveFrame
    //     0xbbea5c: mov             SP, fp
    //     0xbbea60: ldp             fp, lr, [SP], #0x10
    // 0xbbea64: ret
    //     0xbbea64: ret             
    // 0xbbea68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbea68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbea6c: b               #0xbbea1c
    // 0xbbea70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbea70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildCodPaymentSection(/* No info */) {
    // ** addr: 0xbbea74, size: 0x130
    // 0xbbea74: EnterFrame
    //     0xbbea74: stp             fp, lr, [SP, #-0x10]!
    //     0xbbea78: mov             fp, SP
    // 0xbbea7c: AllocStack(0x38)
    //     0xbbea7c: sub             SP, SP, #0x38
    // 0xbbea80: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbbea80: mov             x0, x1
    //     0xbbea84: stur            x1, [fp, #-8]
    //     0xbbea88: stur            x2, [fp, #-0x10]
    //     0xbbea8c: stur            x3, [fp, #-0x18]
    // 0xbbea90: CheckStackOverflow
    //     0xbbea90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbea94: cmp             SP, x16
    //     0xbbea98: b.ls            #0xbbeb94
    // 0xbbea9c: LoadField: r1 = r0->field_f
    //     0xbbea9c: ldur            w1, [x0, #0xf]
    // 0xbbeaa0: DecompressPointer r1
    //     0xbbeaa0: add             x1, x1, HEAP, lsl #32
    // 0xbbeaa4: cmp             w1, NULL
    // 0xbbeaa8: b.eq            #0xbbeb9c
    // 0xbbeaac: r0 = of()
    //     0xbbeaac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbeab0: LoadField: r1 = r0->field_5b
    //     0xbbeab0: ldur            w1, [x0, #0x5b]
    // 0xbbeab4: DecompressPointer r1
    //     0xbbeab4: add             x1, x1, HEAP, lsl #32
    // 0xbbeab8: r0 = LoadClassIdInstr(r1)
    //     0xbbeab8: ldur            x0, [x1, #-1]
    //     0xbbeabc: ubfx            x0, x0, #0xc, #0x14
    // 0xbbeac0: d0 = 0.030000
    //     0xbbeac0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xbbeac4: ldr             d0, [x17, #0x238]
    // 0xbbeac8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbbeac8: sub             lr, x0, #0xffa
    //     0xbbeacc: ldr             lr, [x21, lr, lsl #3]
    //     0xbbead0: blr             lr
    // 0xbbead4: mov             x2, x0
    // 0xbbead8: r1 = Null
    //     0xbbead8: mov             x1, NULL
    // 0xbbeadc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbeadc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbeae0: r0 = Border.all()
    //     0xbbeae0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbbeae4: stur            x0, [fp, #-0x20]
    // 0xbbeae8: r0 = BoxDecoration()
    //     0xbbeae8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbeaec: mov             x4, x0
    // 0xbbeaf0: ldur            x0, [fp, #-0x20]
    // 0xbbeaf4: stur            x4, [fp, #-0x28]
    // 0xbbeaf8: StoreField: r4->field_f = r0
    //     0xbbeaf8: stur            w0, [x4, #0xf]
    // 0xbbeafc: r0 = Instance_BoxShape
    //     0xbbeafc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbeb00: ldr             x0, [x0, #0x80]
    // 0xbbeb04: StoreField: r4->field_23 = r0
    //     0xbbeb04: stur            w0, [x4, #0x23]
    // 0xbbeb08: ldur            x1, [fp, #-8]
    // 0xbbeb0c: LoadField: r5 = r1->field_f
    //     0xbbeb0c: ldur            w5, [x1, #0xf]
    // 0xbbeb10: DecompressPointer r5
    //     0xbbeb10: add             x5, x5, HEAP, lsl #32
    // 0xbbeb14: cmp             w5, NULL
    // 0xbbeb18: b.eq            #0xbbeba0
    // 0xbbeb1c: ldur            x2, [fp, #-0x10]
    // 0xbbeb20: ldur            x3, [fp, #-0x18]
    // 0xbbeb24: r0 = codPaymentMethodCard()
    //     0xbbeb24: bl              #0xbbeba4  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::codPaymentMethodCard
    // 0xbbeb28: stur            x0, [fp, #-8]
    // 0xbbeb2c: r0 = Padding()
    //     0xbbeb2c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbeb30: mov             x1, x0
    // 0xbbeb34: r0 = Instance_EdgeInsets
    //     0xbbeb34: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbeb38: ldr             x0, [x0, #0x1f0]
    // 0xbbeb3c: stur            x1, [fp, #-0x10]
    // 0xbbeb40: StoreField: r1->field_f = r0
    //     0xbbeb40: stur            w0, [x1, #0xf]
    // 0xbbeb44: ldur            x0, [fp, #-8]
    // 0xbbeb48: StoreField: r1->field_b = r0
    //     0xbbeb48: stur            w0, [x1, #0xb]
    // 0xbbeb4c: r0 = Container()
    //     0xbbeb4c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbeb50: stur            x0, [fp, #-8]
    // 0xbbeb54: ldur            x16, [fp, #-0x28]
    // 0xbbeb58: ldur            lr, [fp, #-0x10]
    // 0xbbeb5c: stp             lr, x16, [SP]
    // 0xbbeb60: mov             x1, x0
    // 0xbbeb64: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbbeb64: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbbeb68: ldr             x4, [x4, #0x88]
    // 0xbbeb6c: r0 = Container()
    //     0xbbeb6c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbeb70: r0 = Padding()
    //     0xbbeb70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbeb74: r1 = Instance_EdgeInsets
    //     0xbbeb74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbbeb78: ldr             x1, [x1, #0xa00]
    // 0xbbeb7c: StoreField: r0->field_f = r1
    //     0xbbeb7c: stur            w1, [x0, #0xf]
    // 0xbbeb80: ldur            x1, [fp, #-8]
    // 0xbbeb84: StoreField: r0->field_b = r1
    //     0xbbeb84: stur            w1, [x0, #0xb]
    // 0xbbeb88: LeaveFrame
    //     0xbbeb88: mov             SP, fp
    //     0xbbeb8c: ldp             fp, lr, [SP], #0x10
    // 0xbbeb90: ret
    //     0xbbeb90: ret             
    // 0xbbeb94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbeb94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbeb98: b               #0xbbea9c
    // 0xbbeb9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbeb9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbeba0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbeba0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ codPaymentMethodCard(/* No info */) {
    // ** addr: 0xbbeba4, size: 0x304
    // 0xbbeba4: EnterFrame
    //     0xbbeba4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbeba8: mov             fp, SP
    // 0xbbebac: AllocStack(0x50)
    //     0xbbebac: sub             SP, SP, #0x50
    // 0xbbebb0: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xbbebb0: mov             x0, x1
    //     0xbbebb4: stur            x1, [fp, #-8]
    //     0xbbebb8: mov             x1, x5
    //     0xbbebbc: stur            x2, [fp, #-0x10]
    //     0xbbebc0: stur            x3, [fp, #-0x18]
    //     0xbbebc4: stur            x5, [fp, #-0x20]
    // 0xbbebc8: CheckStackOverflow
    //     0xbbebc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbebcc: cmp             SP, x16
    //     0xbbebd0: b.ls            #0xbbee94
    // 0xbbebd4: r1 = 3
    //     0xbbebd4: movz            x1, #0x3
    // 0xbbebd8: r0 = AllocateContext()
    //     0xbbebd8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbebdc: mov             x3, x0
    // 0xbbebe0: ldur            x2, [fp, #-8]
    // 0xbbebe4: stur            x3, [fp, #-0x28]
    // 0xbbebe8: StoreField: r3->field_f = r2
    //     0xbbebe8: stur            w2, [x3, #0xf]
    // 0xbbebec: ldur            x4, [fp, #-0x10]
    // 0xbbebf0: StoreField: r3->field_13 = r4
    //     0xbbebf0: stur            w4, [x3, #0x13]
    // 0xbbebf4: ldur            x5, [fp, #-0x18]
    // 0xbbebf8: r0 = BoxInt64Instr(r5)
    //     0xbbebf8: sbfiz           x0, x5, #1, #0x1f
    //     0xbbebfc: cmp             x5, x0, asr #1
    //     0xbbec00: b.eq            #0xbbec0c
    //     0xbbec04: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbec08: stur            x5, [x0, #7]
    // 0xbbec0c: ArrayStore: r3[0] = r0  ; List_4
    //     0xbbec0c: stur            w0, [x3, #0x17]
    // 0xbbec10: r0 = Image()
    //     0xbbec10: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0xbbec14: mov             x1, x0
    // 0xbbec18: r2 = "assets/images/cod.png"
    //     0xbbec18: add             x2, PP, #0x54, lsl #12  ; [pp+0x545d0] "assets/images/cod.png"
    //     0xbbec1c: ldr             x2, [x2, #0x5d0]
    // 0xbbec20: stur            x0, [fp, #-0x30]
    // 0xbbec24: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbec24: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbec28: r0 = Image.asset()
    //     0xbbec28: bl              #0xa20f60  ; [package:flutter/src/widgets/image.dart] Image::Image.asset
    // 0xbbec2c: ldur            x0, [fp, #-0x10]
    // 0xbbec30: cmp             w0, NULL
    // 0xbbec34: b.ne            #0xbbec40
    // 0xbbec38: r0 = Null
    //     0xbbec38: mov             x0, NULL
    // 0xbbec3c: b               #0xbbec4c
    // 0xbbec40: LoadField: r1 = r0->field_b
    //     0xbbec40: ldur            w1, [x0, #0xb]
    // 0xbbec44: DecompressPointer r1
    //     0xbbec44: add             x1, x1, HEAP, lsl #32
    // 0xbbec48: mov             x0, x1
    // 0xbbec4c: cmp             w0, NULL
    // 0xbbec50: b.ne            #0xbbec5c
    // 0xbbec54: r2 = ""
    //     0xbbec54: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbec58: b               #0xbbec60
    // 0xbbec5c: mov             x2, x0
    // 0xbbec60: ldur            x0, [fp, #-8]
    // 0xbbec64: ldur            x1, [fp, #-0x20]
    // 0xbbec68: stur            x2, [fp, #-0x10]
    // 0xbbec6c: r0 = of()
    //     0xbbec6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbec70: LoadField: r1 = r0->field_87
    //     0xbbec70: ldur            w1, [x0, #0x87]
    // 0xbbec74: DecompressPointer r1
    //     0xbbec74: add             x1, x1, HEAP, lsl #32
    // 0xbbec78: LoadField: r0 = r1->field_2b
    //     0xbbec78: ldur            w0, [x1, #0x2b]
    // 0xbbec7c: DecompressPointer r0
    //     0xbbec7c: add             x0, x0, HEAP, lsl #32
    // 0xbbec80: stur            x0, [fp, #-0x38]
    // 0xbbec84: r1 = Instance_Color
    //     0xbbec84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbec88: d0 = 0.700000
    //     0xbbec88: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbbec8c: ldr             d0, [x17, #0xf48]
    // 0xbbec90: r0 = withOpacity()
    //     0xbbec90: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbec94: r16 = 14.000000
    //     0xbbec94: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbec98: ldr             x16, [x16, #0x1d8]
    // 0xbbec9c: stp             x16, x0, [SP]
    // 0xbbeca0: ldur            x1, [fp, #-0x38]
    // 0xbbeca4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbeca4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbeca8: ldr             x4, [x4, #0x9b8]
    // 0xbbecac: r0 = copyWith()
    //     0xbbecac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbecb0: stur            x0, [fp, #-0x38]
    // 0xbbecb4: r0 = Text()
    //     0xbbecb4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbecb8: mov             x1, x0
    // 0xbbecbc: ldur            x0, [fp, #-0x10]
    // 0xbbecc0: stur            x1, [fp, #-0x40]
    // 0xbbecc4: StoreField: r1->field_b = r0
    //     0xbbecc4: stur            w0, [x1, #0xb]
    // 0xbbecc8: ldur            x0, [fp, #-0x38]
    // 0xbbeccc: StoreField: r1->field_13 = r0
    //     0xbbeccc: stur            w0, [x1, #0x13]
    // 0xbbecd0: r0 = Padding()
    //     0xbbecd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbecd4: mov             x1, x0
    // 0xbbecd8: r0 = Instance_EdgeInsets
    //     0xbbecd8: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbbecdc: ldr             x0, [x0, #0xa78]
    // 0xbbece0: stur            x1, [fp, #-0x10]
    // 0xbbece4: StoreField: r1->field_f = r0
    //     0xbbece4: stur            w0, [x1, #0xf]
    // 0xbbece8: ldur            x0, [fp, #-0x40]
    // 0xbbecec: StoreField: r1->field_b = r0
    //     0xbbecec: stur            w0, [x1, #0xb]
    // 0xbbecf0: ldur            x0, [fp, #-8]
    // 0xbbecf4: LoadField: r2 = r0->field_1b
    //     0xbbecf4: ldur            w2, [x0, #0x1b]
    // 0xbbecf8: DecompressPointer r2
    //     0xbbecf8: add             x2, x2, HEAP, lsl #32
    // 0xbbecfc: r16 = Sentinel
    //     0xbbecfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbed00: cmp             w2, w16
    // 0xbbed04: b.eq            #0xbbee9c
    // 0xbbed08: r0 = LoadClassIdInstr(r2)
    //     0xbbed08: ldur            x0, [x2, #-1]
    //     0xbbed0c: ubfx            x0, x0, #0xc, #0x14
    // 0xbbed10: r16 = "cod"
    //     0xbbed10: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0xbbed14: ldr             x16, [x16, #0xa28]
    // 0xbbed18: stp             x16, x2, [SP]
    // 0xbbed1c: mov             lr, x0
    // 0xbbed20: ldr             lr, [x21, lr, lsl #3]
    // 0xbbed24: blr             lr
    // 0xbbed28: tbnz            w0, #4, #0xbbed38
    // 0xbbed2c: r3 = Instance_IconData
    //     0xbbed2c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xbbed30: ldr             x3, [x3, #0x30]
    // 0xbbed34: b               #0xbbed40
    // 0xbbed38: r3 = Instance_IconData
    //     0xbbed38: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xbbed3c: ldr             x3, [x3, #0x38]
    // 0xbbed40: ldur            x2, [fp, #-0x30]
    // 0xbbed44: ldur            x0, [fp, #-0x10]
    // 0xbbed48: ldur            x1, [fp, #-0x20]
    // 0xbbed4c: stur            x3, [fp, #-8]
    // 0xbbed50: r0 = of()
    //     0xbbed50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbed54: LoadField: r1 = r0->field_5b
    //     0xbbed54: ldur            w1, [x0, #0x5b]
    // 0xbbed58: DecompressPointer r1
    //     0xbbed58: add             x1, x1, HEAP, lsl #32
    // 0xbbed5c: stur            x1, [fp, #-0x20]
    // 0xbbed60: r0 = Icon()
    //     0xbbed60: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbbed64: mov             x3, x0
    // 0xbbed68: ldur            x0, [fp, #-8]
    // 0xbbed6c: stur            x3, [fp, #-0x38]
    // 0xbbed70: StoreField: r3->field_b = r0
    //     0xbbed70: stur            w0, [x3, #0xb]
    // 0xbbed74: ldur            x0, [fp, #-0x20]
    // 0xbbed78: StoreField: r3->field_23 = r0
    //     0xbbed78: stur            w0, [x3, #0x23]
    // 0xbbed7c: r1 = Null
    //     0xbbed7c: mov             x1, NULL
    // 0xbbed80: r2 = 8
    //     0xbbed80: movz            x2, #0x8
    // 0xbbed84: r0 = AllocateArray()
    //     0xbbed84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbed88: mov             x2, x0
    // 0xbbed8c: ldur            x0, [fp, #-0x30]
    // 0xbbed90: stur            x2, [fp, #-8]
    // 0xbbed94: StoreField: r2->field_f = r0
    //     0xbbed94: stur            w0, [x2, #0xf]
    // 0xbbed98: ldur            x0, [fp, #-0x10]
    // 0xbbed9c: StoreField: r2->field_13 = r0
    //     0xbbed9c: stur            w0, [x2, #0x13]
    // 0xbbeda0: r16 = Instance_Spacer
    //     0xbbeda0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbbeda4: ldr             x16, [x16, #0xf0]
    // 0xbbeda8: ArrayStore: r2[0] = r16  ; List_4
    //     0xbbeda8: stur            w16, [x2, #0x17]
    // 0xbbedac: ldur            x0, [fp, #-0x38]
    // 0xbbedb0: StoreField: r2->field_1b = r0
    //     0xbbedb0: stur            w0, [x2, #0x1b]
    // 0xbbedb4: r1 = <Widget>
    //     0xbbedb4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbedb8: r0 = AllocateGrowableArray()
    //     0xbbedb8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbedbc: mov             x1, x0
    // 0xbbedc0: ldur            x0, [fp, #-8]
    // 0xbbedc4: stur            x1, [fp, #-0x10]
    // 0xbbedc8: StoreField: r1->field_f = r0
    //     0xbbedc8: stur            w0, [x1, #0xf]
    // 0xbbedcc: r0 = 8
    //     0xbbedcc: movz            x0, #0x8
    // 0xbbedd0: StoreField: r1->field_b = r0
    //     0xbbedd0: stur            w0, [x1, #0xb]
    // 0xbbedd4: r0 = Row()
    //     0xbbedd4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbedd8: mov             x1, x0
    // 0xbbeddc: r0 = Instance_Axis
    //     0xbbeddc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbede0: stur            x1, [fp, #-8]
    // 0xbbede4: StoreField: r1->field_f = r0
    //     0xbbede4: stur            w0, [x1, #0xf]
    // 0xbbede8: r0 = Instance_MainAxisAlignment
    //     0xbbede8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbbedec: ldr             x0, [x0, #0xa8]
    // 0xbbedf0: StoreField: r1->field_13 = r0
    //     0xbbedf0: stur            w0, [x1, #0x13]
    // 0xbbedf4: r0 = Instance_MainAxisSize
    //     0xbbedf4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbedf8: ldr             x0, [x0, #0xa10]
    // 0xbbedfc: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbedfc: stur            w0, [x1, #0x17]
    // 0xbbee00: r0 = Instance_CrossAxisAlignment
    //     0xbbee00: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbee04: ldr             x0, [x0, #0xa18]
    // 0xbbee08: StoreField: r1->field_1b = r0
    //     0xbbee08: stur            w0, [x1, #0x1b]
    // 0xbbee0c: r0 = Instance_VerticalDirection
    //     0xbbee0c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbee10: ldr             x0, [x0, #0xa20]
    // 0xbbee14: StoreField: r1->field_23 = r0
    //     0xbbee14: stur            w0, [x1, #0x23]
    // 0xbbee18: r0 = Instance_Clip
    //     0xbbee18: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbee1c: ldr             x0, [x0, #0x38]
    // 0xbbee20: StoreField: r1->field_2b = r0
    //     0xbbee20: stur            w0, [x1, #0x2b]
    // 0xbbee24: StoreField: r1->field_2f = rZR
    //     0xbbee24: stur            xzr, [x1, #0x2f]
    // 0xbbee28: ldur            x0, [fp, #-0x10]
    // 0xbbee2c: StoreField: r1->field_b = r0
    //     0xbbee2c: stur            w0, [x1, #0xb]
    // 0xbbee30: r0 = InkWell()
    //     0xbbee30: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbee34: mov             x3, x0
    // 0xbbee38: ldur            x0, [fp, #-8]
    // 0xbbee3c: stur            x3, [fp, #-0x10]
    // 0xbbee40: StoreField: r3->field_b = r0
    //     0xbbee40: stur            w0, [x3, #0xb]
    // 0xbbee44: ldur            x2, [fp, #-0x28]
    // 0xbbee48: r1 = Function '<anonymous closure>':.
    //     0xbbee48: add             x1, PP, #0x54, lsl #12  ; [pp+0x545d8] AnonymousClosure: (0xbbeea8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::codPaymentMethodCard (0xbbeba4)
    //     0xbbee4c: ldr             x1, [x1, #0x5d8]
    // 0xbbee50: r0 = AllocateClosure()
    //     0xbbee50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbee54: mov             x1, x0
    // 0xbbee58: ldur            x0, [fp, #-0x10]
    // 0xbbee5c: StoreField: r0->field_f = r1
    //     0xbbee5c: stur            w1, [x0, #0xf]
    // 0xbbee60: r1 = true
    //     0xbbee60: add             x1, NULL, #0x20  ; true
    // 0xbbee64: StoreField: r0->field_43 = r1
    //     0xbbee64: stur            w1, [x0, #0x43]
    // 0xbbee68: r2 = Instance_BoxShape
    //     0xbbee68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbee6c: ldr             x2, [x2, #0x80]
    // 0xbbee70: StoreField: r0->field_47 = r2
    //     0xbbee70: stur            w2, [x0, #0x47]
    // 0xbbee74: StoreField: r0->field_6f = r1
    //     0xbbee74: stur            w1, [x0, #0x6f]
    // 0xbbee78: r2 = false
    //     0xbbee78: add             x2, NULL, #0x30  ; false
    // 0xbbee7c: StoreField: r0->field_73 = r2
    //     0xbbee7c: stur            w2, [x0, #0x73]
    // 0xbbee80: StoreField: r0->field_83 = r1
    //     0xbbee80: stur            w1, [x0, #0x83]
    // 0xbbee84: StoreField: r0->field_7b = r2
    //     0xbbee84: stur            w2, [x0, #0x7b]
    // 0xbbee88: LeaveFrame
    //     0xbbee88: mov             SP, fp
    //     0xbbee8c: ldp             fp, lr, [SP], #0x10
    // 0xbbee90: ret
    //     0xbbee90: ret             
    // 0xbbee94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbee94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbee98: b               #0xbbebd4
    // 0xbbee9c: r9 = selectedPaymentMode
    //     0xbbee9c: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xbbeea0: ldr             x9, [x9, #0x560]
    // 0xbbeea4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbeea4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbeea8, size: 0x154
    // 0xbbeea8: EnterFrame
    //     0xbbeea8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbeeac: mov             fp, SP
    // 0xbbeeb0: AllocStack(0x38)
    //     0xbbeeb0: sub             SP, SP, #0x38
    // 0xbbeeb4: SetupParameters()
    //     0xbbeeb4: ldr             x0, [fp, #0x10]
    //     0xbbeeb8: ldur            w3, [x0, #0x17]
    //     0xbbeebc: add             x3, x3, HEAP, lsl #32
    //     0xbbeec0: stur            x3, [fp, #-0x10]
    // 0xbbeec4: CheckStackOverflow
    //     0xbbeec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbeec8: cmp             SP, x16
    //     0xbbeecc: b.ls            #0xbbefec
    // 0xbbeed0: LoadField: r0 = r3->field_f
    //     0xbbeed0: ldur            w0, [x3, #0xf]
    // 0xbbeed4: DecompressPointer r0
    //     0xbbeed4: add             x0, x0, HEAP, lsl #32
    // 0xbbeed8: mov             x2, x3
    // 0xbbeedc: stur            x0, [fp, #-8]
    // 0xbbeee0: r1 = Function '<anonymous closure>':.
    //     0xbbeee0: add             x1, PP, #0x54, lsl #12  ; [pp+0x545e0] AnonymousClosure: (0xbbeffc), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::codPaymentMethodCard (0xbbeba4)
    //     0xbbeee4: ldr             x1, [x1, #0x5e0]
    // 0xbbeee8: r0 = AllocateClosure()
    //     0xbbeee8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbeeec: ldur            x1, [fp, #-8]
    // 0xbbeef0: mov             x2, x0
    // 0xbbeef4: r0 = setState()
    //     0xbbeef4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbbeef8: ldur            x0, [fp, #-0x10]
    // 0xbbeefc: LoadField: r1 = r0->field_f
    //     0xbbeefc: ldur            w1, [x0, #0xf]
    // 0xbbef00: DecompressPointer r1
    //     0xbbef00: add             x1, x1, HEAP, lsl #32
    // 0xbbef04: LoadField: r2 = r1->field_b
    //     0xbbef04: ldur            w2, [x1, #0xb]
    // 0xbbef08: DecompressPointer r2
    //     0xbbef08: add             x2, x2, HEAP, lsl #32
    // 0xbbef0c: cmp             w2, NULL
    // 0xbbef10: b.eq            #0xbbeff4
    // 0xbbef14: LoadField: r1 = r0->field_13
    //     0xbbef14: ldur            w1, [x0, #0x13]
    // 0xbbef18: DecompressPointer r1
    //     0xbbef18: add             x1, x1, HEAP, lsl #32
    // 0xbbef1c: cmp             w1, NULL
    // 0xbbef20: b.ne            #0xbbef2c
    // 0xbbef24: r1 = Null
    //     0xbbef24: mov             x1, NULL
    // 0xbbef28: b               #0xbbef38
    // 0xbbef2c: LoadField: r3 = r1->field_7
    //     0xbbef2c: ldur            w3, [x1, #7]
    // 0xbbef30: DecompressPointer r3
    //     0xbbef30: add             x3, x3, HEAP, lsl #32
    // 0xbbef34: mov             x1, x3
    // 0xbbef38: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbbef38: ldur            w3, [x0, #0x17]
    // 0xbbef3c: DecompressPointer r3
    //     0xbbef3c: add             x3, x3, HEAP, lsl #32
    // 0xbbef40: LoadField: r4 = r2->field_f
    //     0xbbef40: ldur            w4, [x2, #0xf]
    // 0xbbef44: DecompressPointer r4
    //     0xbbef44: add             x4, x4, HEAP, lsl #32
    // 0xbbef48: stp             x1, x4, [SP, #0x18]
    // 0xbbef4c: r16 = ""
    //     0xbbef4c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbef50: stp             x3, x16, [SP, #8]
    // 0xbbef54: r16 = true
    //     0xbbef54: add             x16, NULL, #0x20  ; true
    // 0xbbef58: str             x16, [SP]
    // 0xbbef5c: r4 = 0
    //     0xbbef5c: movz            x4, #0
    // 0xbbef60: ldr             x0, [SP, #0x20]
    // 0xbbef64: r16 = UnlinkedCall_0x613b5c
    //     0xbbef64: add             x16, PP, #0x54, lsl #12  ; [pp+0x545e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbef68: add             x16, x16, #0x5e8
    // 0xbbef6c: ldp             x5, lr, [x16]
    // 0xbbef70: blr             lr
    // 0xbbef74: ldur            x0, [fp, #-0x10]
    // 0xbbef78: LoadField: r1 = r0->field_f
    //     0xbbef78: ldur            w1, [x0, #0xf]
    // 0xbbef7c: DecompressPointer r1
    //     0xbbef7c: add             x1, x1, HEAP, lsl #32
    // 0xbbef80: LoadField: r2 = r1->field_b
    //     0xbbef80: ldur            w2, [x1, #0xb]
    // 0xbbef84: DecompressPointer r2
    //     0xbbef84: add             x2, x2, HEAP, lsl #32
    // 0xbbef88: cmp             w2, NULL
    // 0xbbef8c: b.eq            #0xbbeff8
    // 0xbbef90: LoadField: r1 = r0->field_13
    //     0xbbef90: ldur            w1, [x0, #0x13]
    // 0xbbef94: DecompressPointer r1
    //     0xbbef94: add             x1, x1, HEAP, lsl #32
    // 0xbbef98: cmp             w1, NULL
    // 0xbbef9c: b.ne            #0xbbefa8
    // 0xbbefa0: r0 = Null
    //     0xbbefa0: mov             x0, NULL
    // 0xbbefa4: b               #0xbbefb0
    // 0xbbefa8: LoadField: r0 = r1->field_7
    //     0xbbefa8: ldur            w0, [x1, #7]
    // 0xbbefac: DecompressPointer r0
    //     0xbbefac: add             x0, x0, HEAP, lsl #32
    // 0xbbefb0: LoadField: r1 = r2->field_23
    //     0xbbefb0: ldur            w1, [x2, #0x23]
    // 0xbbefb4: DecompressPointer r1
    //     0xbbefb4: add             x1, x1, HEAP, lsl #32
    // 0xbbefb8: stp             x0, x1, [SP, #8]
    // 0xbbefbc: r16 = ""
    //     0xbbefbc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbefc0: str             x16, [SP]
    // 0xbbefc4: r4 = 0
    //     0xbbefc4: movz            x4, #0
    // 0xbbefc8: ldr             x0, [SP, #0x10]
    // 0xbbefcc: r16 = UnlinkedCall_0x613b5c
    //     0xbbefcc: add             x16, PP, #0x54, lsl #12  ; [pp+0x545f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbefd0: add             x16, x16, #0x5f8
    // 0xbbefd4: ldp             x5, lr, [x16]
    // 0xbbefd8: blr             lr
    // 0xbbefdc: r0 = Null
    //     0xbbefdc: mov             x0, NULL
    // 0xbbefe0: LeaveFrame
    //     0xbbefe0: mov             SP, fp
    //     0xbbefe4: ldp             fp, lr, [SP], #0x10
    // 0xbbefe8: ret
    //     0xbbefe8: ret             
    // 0xbbefec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbefec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbeff0: b               #0xbbeed0
    // 0xbbeff4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbeff4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbeff8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbeff8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbeffc, size: 0xb8
    // 0xbbeffc: EnterFrame
    //     0xbbeffc: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf000: mov             fp, SP
    // 0xbbf004: ldr             x1, [fp, #0x10]
    // 0xbbf008: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbbf008: ldur            w2, [x1, #0x17]
    // 0xbbf00c: DecompressPointer r2
    //     0xbbf00c: add             x2, x2, HEAP, lsl #32
    // 0xbbf010: LoadField: r1 = r2->field_f
    //     0xbbf010: ldur            w1, [x2, #0xf]
    // 0xbbf014: DecompressPointer r1
    //     0xbbf014: add             x1, x1, HEAP, lsl #32
    // 0xbbf018: LoadField: r3 = r2->field_13
    //     0xbbf018: ldur            w3, [x2, #0x13]
    // 0xbbf01c: DecompressPointer r3
    //     0xbbf01c: add             x3, x3, HEAP, lsl #32
    // 0xbbf020: cmp             w3, NULL
    // 0xbbf024: b.ne            #0xbbf030
    // 0xbbf028: r3 = Null
    //     0xbbf028: mov             x3, NULL
    // 0xbbf02c: b               #0xbbf03c
    // 0xbbf030: LoadField: r4 = r3->field_7
    //     0xbbf030: ldur            w4, [x3, #7]
    // 0xbbf034: DecompressPointer r4
    //     0xbbf034: add             x4, x4, HEAP, lsl #32
    // 0xbbf038: mov             x3, x4
    // 0xbbf03c: cmp             w3, NULL
    // 0xbbf040: b.ne            #0xbbf04c
    // 0xbbf044: r0 = ""
    //     0xbbf044: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbf048: b               #0xbbf050
    // 0xbbf04c: mov             x0, x3
    // 0xbbf050: r4 = ""
    //     0xbbf050: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbf054: r3 = true
    //     0xbbf054: add             x3, NULL, #0x20  ; true
    // 0xbbf058: StoreField: r1->field_1b = r0
    //     0xbbf058: stur            w0, [x1, #0x1b]
    //     0xbbf05c: ldurb           w16, [x1, #-1]
    //     0xbbf060: ldurb           w17, [x0, #-1]
    //     0xbbf064: and             x16, x17, x16, lsr #2
    //     0xbbf068: tst             x16, HEAP, lsr #32
    //     0xbbf06c: b.eq            #0xbbf074
    //     0xbbf070: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xbbf074: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xbbf074: ldur            w0, [x2, #0x17]
    // 0xbbf078: DecompressPointer r0
    //     0xbbf078: add             x0, x0, HEAP, lsl #32
    // 0xbbf07c: StoreField: r1->field_23 = r0
    //     0xbbf07c: stur            w0, [x1, #0x23]
    //     0xbbf080: tbz             w0, #0, #0xbbf09c
    //     0xbbf084: ldurb           w16, [x1, #-1]
    //     0xbbf088: ldurb           w17, [x0, #-1]
    //     0xbbf08c: and             x16, x17, x16, lsr #2
    //     0xbbf090: tst             x16, HEAP, lsr #32
    //     0xbbf094: b.eq            #0xbbf09c
    //     0xbbf098: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xbbf09c: StoreField: r1->field_1f = r4
    //     0xbbf09c: stur            w4, [x1, #0x1f]
    // 0xbbf0a0: StoreField: r1->field_2f = r3
    //     0xbbf0a0: stur            w3, [x1, #0x2f]
    // 0xbbf0a4: r0 = Null
    //     0xbbf0a4: mov             x0, NULL
    // 0xbbf0a8: LeaveFrame
    //     0xbbf0a8: mov             SP, fp
    //     0xbbf0ac: ldp             fp, lr, [SP], #0x10
    // 0xbbf0b0: ret
    //     0xbbf0b0: ret             
  }
  _ _buildOnlinePaymentSection(/* No info */) {
    // ** addr: 0xbbf0b4, size: 0x1c4
    // 0xbbf0b4: EnterFrame
    //     0xbbf0b4: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf0b8: mov             fp, SP
    // 0xbbf0bc: AllocStack(0x38)
    //     0xbbf0bc: sub             SP, SP, #0x38
    // 0xbbf0c0: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xbbf0c0: stur            x1, [fp, #-8]
    // 0xbbf0c4: CheckStackOverflow
    //     0xbbf0c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf0c8: cmp             SP, x16
    //     0xbbf0cc: b.ls            #0xbbf268
    // 0xbbf0d0: r1 = 1
    //     0xbbf0d0: movz            x1, #0x1
    // 0xbbf0d4: r0 = AllocateContext()
    //     0xbbf0d4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbf0d8: mov             x2, x0
    // 0xbbf0dc: ldur            x0, [fp, #-8]
    // 0xbbf0e0: stur            x2, [fp, #-0x10]
    // 0xbbf0e4: StoreField: r2->field_f = r0
    //     0xbbf0e4: stur            w0, [x2, #0xf]
    // 0xbbf0e8: LoadField: r1 = r0->field_f
    //     0xbbf0e8: ldur            w1, [x0, #0xf]
    // 0xbbf0ec: DecompressPointer r1
    //     0xbbf0ec: add             x1, x1, HEAP, lsl #32
    // 0xbbf0f0: cmp             w1, NULL
    // 0xbbf0f4: b.eq            #0xbbf270
    // 0xbbf0f8: r0 = of()
    //     0xbbf0f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbf0fc: LoadField: r1 = r0->field_5b
    //     0xbbf0fc: ldur            w1, [x0, #0x5b]
    // 0xbbf100: DecompressPointer r1
    //     0xbbf100: add             x1, x1, HEAP, lsl #32
    // 0xbbf104: r0 = LoadClassIdInstr(r1)
    //     0xbbf104: ldur            x0, [x1, #-1]
    //     0xbbf108: ubfx            x0, x0, #0xc, #0x14
    // 0xbbf10c: d0 = 0.030000
    //     0xbbf10c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xbbf110: ldr             d0, [x17, #0x238]
    // 0xbbf114: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbbf114: sub             lr, x0, #0xffa
    //     0xbbf118: ldr             lr, [x21, lr, lsl #3]
    //     0xbbf11c: blr             lr
    // 0xbbf120: mov             x2, x0
    // 0xbbf124: r1 = Null
    //     0xbbf124: mov             x1, NULL
    // 0xbbf128: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbf128: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbf12c: r0 = Border.all()
    //     0xbbf12c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbbf130: stur            x0, [fp, #-0x18]
    // 0xbbf134: r0 = BoxDecoration()
    //     0xbbf134: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbf138: mov             x3, x0
    // 0xbbf13c: ldur            x0, [fp, #-0x18]
    // 0xbbf140: stur            x3, [fp, #-0x28]
    // 0xbbf144: StoreField: r3->field_f = r0
    //     0xbbf144: stur            w0, [x3, #0xf]
    // 0xbbf148: r0 = Instance_BoxShape
    //     0xbbf148: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbf14c: ldr             x0, [x0, #0x80]
    // 0xbbf150: StoreField: r3->field_23 = r0
    //     0xbbf150: stur            w0, [x3, #0x23]
    // 0xbbf154: ldur            x0, [fp, #-8]
    // 0xbbf158: LoadField: r1 = r0->field_b
    //     0xbbf158: ldur            w1, [x0, #0xb]
    // 0xbbf15c: DecompressPointer r1
    //     0xbbf15c: add             x1, x1, HEAP, lsl #32
    // 0xbbf160: cmp             w1, NULL
    // 0xbbf164: b.eq            #0xbbf274
    // 0xbbf168: LoadField: r0 = r1->field_b
    //     0xbbf168: ldur            w0, [x1, #0xb]
    // 0xbbf16c: DecompressPointer r0
    //     0xbbf16c: add             x0, x0, HEAP, lsl #32
    // 0xbbf170: LoadField: r1 = r0->field_b
    //     0xbbf170: ldur            w1, [x0, #0xb]
    // 0xbbf174: DecompressPointer r1
    //     0xbbf174: add             x1, x1, HEAP, lsl #32
    // 0xbbf178: cmp             w1, NULL
    // 0xbbf17c: b.ne            #0xbbf188
    // 0xbbf180: r0 = Null
    //     0xbbf180: mov             x0, NULL
    // 0xbbf184: b               #0xbbf1a8
    // 0xbbf188: LoadField: r0 = r1->field_27
    //     0xbbf188: ldur            w0, [x1, #0x27]
    // 0xbbf18c: DecompressPointer r0
    //     0xbbf18c: add             x0, x0, HEAP, lsl #32
    // 0xbbf190: cmp             w0, NULL
    // 0xbbf194: b.ne            #0xbbf1a0
    // 0xbbf198: r0 = Null
    //     0xbbf198: mov             x0, NULL
    // 0xbbf19c: b               #0xbbf1a8
    // 0xbbf1a0: LoadField: r1 = r0->field_b
    //     0xbbf1a0: ldur            w1, [x0, #0xb]
    // 0xbbf1a4: mov             x0, x1
    // 0xbbf1a8: cmp             w0, NULL
    // 0xbbf1ac: b.ne            #0xbbf1b8
    // 0xbbf1b0: r0 = 0
    //     0xbbf1b0: movz            x0, #0
    // 0xbbf1b4: b               #0xbbf1c0
    // 0xbbf1b8: r1 = LoadInt32Instr(r0)
    //     0xbbf1b8: sbfx            x1, x0, #1, #0x1f
    // 0xbbf1bc: mov             x0, x1
    // 0xbbf1c0: ldur            x2, [fp, #-0x10]
    // 0xbbf1c4: stur            x0, [fp, #-0x20]
    // 0xbbf1c8: r1 = Function '<anonymous closure>':.
    //     0xbbf1c8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54608] AnonymousClosure: (0xbbf278), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildOnlinePaymentSection (0xbbf0b4)
    //     0xbbf1cc: ldr             x1, [x1, #0x608]
    // 0xbbf1d0: r0 = AllocateClosure()
    //     0xbbf1d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbf1d4: ldur            x2, [fp, #-0x10]
    // 0xbbf1d8: r1 = Function '<anonymous closure>':.
    //     0xbbf1d8: add             x1, PP, #0x54, lsl #12  ; [pp+0x54610] AnonymousClosure: (0xbbdde0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_buildPartialCodPaymentSection (0xbbd394)
    //     0xbbf1dc: ldr             x1, [x1, #0x610]
    // 0xbbf1e0: stur            x0, [fp, #-8]
    // 0xbbf1e4: r0 = AllocateClosure()
    //     0xbbf1e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbf1e8: stur            x0, [fp, #-0x10]
    // 0xbbf1ec: r0 = ListView()
    //     0xbbf1ec: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbbf1f0: stur            x0, [fp, #-0x18]
    // 0xbbf1f4: r16 = true
    //     0xbbf1f4: add             x16, NULL, #0x20  ; true
    // 0xbbf1f8: r30 = Instance_NeverScrollableScrollPhysics
    //     0xbbf1f8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbbf1fc: ldr             lr, [lr, #0x1c8]
    // 0xbbf200: stp             lr, x16, [SP]
    // 0xbbf204: mov             x1, x0
    // 0xbbf208: ldur            x2, [fp, #-8]
    // 0xbbf20c: ldur            x3, [fp, #-0x20]
    // 0xbbf210: ldur            x5, [fp, #-0x10]
    // 0xbbf214: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x5, shrinkWrap, 0x4, null]
    //     0xbbf214: add             x4, PP, #0x40, lsl #12  ; [pp+0x40738] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x5, "shrinkWrap", 0x4, Null]
    //     0xbbf218: ldr             x4, [x4, #0x738]
    // 0xbbf21c: r0 = ListView.separated()
    //     0xbbf21c: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbbf220: r0 = Container()
    //     0xbbf220: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbf224: stur            x0, [fp, #-8]
    // 0xbbf228: ldur            x16, [fp, #-0x28]
    // 0xbbf22c: ldur            lr, [fp, #-0x18]
    // 0xbbf230: stp             lr, x16, [SP]
    // 0xbbf234: mov             x1, x0
    // 0xbbf238: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbbf238: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbbf23c: ldr             x4, [x4, #0x88]
    // 0xbbf240: r0 = Container()
    //     0xbbf240: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbf244: r0 = Padding()
    //     0xbbf244: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbf248: r1 = Instance_EdgeInsets
    //     0xbbf248: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xbbf24c: ldr             x1, [x1, #0xa00]
    // 0xbbf250: StoreField: r0->field_f = r1
    //     0xbbf250: stur            w1, [x0, #0xf]
    // 0xbbf254: ldur            x1, [fp, #-8]
    // 0xbbf258: StoreField: r0->field_b = r1
    //     0xbbf258: stur            w1, [x0, #0xb]
    // 0xbbf25c: LeaveFrame
    //     0xbbf25c: mov             SP, fp
    //     0xbbf260: ldp             fp, lr, [SP], #0x10
    // 0xbbf264: ret
    //     0xbbf264: ret             
    // 0xbbf268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbf268: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbf26c: b               #0xbbf0d0
    // 0xbbf270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf270: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbf274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf274: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbbf278, size: 0x110
    // 0xbbf278: EnterFrame
    //     0xbbf278: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf27c: mov             fp, SP
    // 0xbbf280: AllocStack(0x8)
    //     0xbbf280: sub             SP, SP, #8
    // 0xbbf284: SetupParameters()
    //     0xbbf284: ldr             x0, [fp, #0x20]
    //     0xbbf288: ldur            w1, [x0, #0x17]
    //     0xbbf28c: add             x1, x1, HEAP, lsl #32
    // 0xbbf290: CheckStackOverflow
    //     0xbbf290: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf294: cmp             SP, x16
    //     0xbbf298: b.ls            #0xbbf378
    // 0xbbf29c: LoadField: r2 = r1->field_f
    //     0xbbf29c: ldur            w2, [x1, #0xf]
    // 0xbbf2a0: DecompressPointer r2
    //     0xbbf2a0: add             x2, x2, HEAP, lsl #32
    // 0xbbf2a4: LoadField: r0 = r2->field_b
    //     0xbbf2a4: ldur            w0, [x2, #0xb]
    // 0xbbf2a8: DecompressPointer r0
    //     0xbbf2a8: add             x0, x0, HEAP, lsl #32
    // 0xbbf2ac: cmp             w0, NULL
    // 0xbbf2b0: b.eq            #0xbbf380
    // 0xbbf2b4: LoadField: r1 = r0->field_b
    //     0xbbf2b4: ldur            w1, [x0, #0xb]
    // 0xbbf2b8: DecompressPointer r1
    //     0xbbf2b8: add             x1, x1, HEAP, lsl #32
    // 0xbbf2bc: LoadField: r0 = r1->field_b
    //     0xbbf2bc: ldur            w0, [x1, #0xb]
    // 0xbbf2c0: DecompressPointer r0
    //     0xbbf2c0: add             x0, x0, HEAP, lsl #32
    // 0xbbf2c4: cmp             w0, NULL
    // 0xbbf2c8: b.ne            #0xbbf2d8
    // 0xbbf2cc: ldr             x4, [fp, #0x10]
    // 0xbbf2d0: r0 = Null
    //     0xbbf2d0: mov             x0, NULL
    // 0xbbf2d4: b               #0xbbf334
    // 0xbbf2d8: LoadField: r3 = r0->field_27
    //     0xbbf2d8: ldur            w3, [x0, #0x27]
    // 0xbbf2dc: DecompressPointer r3
    //     0xbbf2dc: add             x3, x3, HEAP, lsl #32
    // 0xbbf2e0: cmp             w3, NULL
    // 0xbbf2e4: b.ne            #0xbbf2f4
    // 0xbbf2e8: ldr             x4, [fp, #0x10]
    // 0xbbf2ec: r0 = Null
    //     0xbbf2ec: mov             x0, NULL
    // 0xbbf2f0: b               #0xbbf334
    // 0xbbf2f4: ldr             x4, [fp, #0x10]
    // 0xbbf2f8: LoadField: r0 = r3->field_b
    //     0xbbf2f8: ldur            w0, [x3, #0xb]
    // 0xbbf2fc: r5 = LoadInt32Instr(r4)
    //     0xbbf2fc: sbfx            x5, x4, #1, #0x1f
    //     0xbbf300: tbz             w4, #0, #0xbbf308
    //     0xbbf304: ldur            x5, [x4, #7]
    // 0xbbf308: r1 = LoadInt32Instr(r0)
    //     0xbbf308: sbfx            x1, x0, #1, #0x1f
    // 0xbbf30c: mov             x0, x1
    // 0xbbf310: mov             x1, x5
    // 0xbbf314: cmp             x1, x0
    // 0xbbf318: b.hs            #0xbbf384
    // 0xbbf31c: LoadField: r0 = r3->field_f
    //     0xbbf31c: ldur            w0, [x3, #0xf]
    // 0xbbf320: DecompressPointer r0
    //     0xbbf320: add             x0, x0, HEAP, lsl #32
    // 0xbbf324: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xbbf324: add             x16, x0, x5, lsl #2
    //     0xbbf328: ldur            w1, [x16, #0xf]
    // 0xbbf32c: DecompressPointer r1
    //     0xbbf32c: add             x1, x1, HEAP, lsl #32
    // 0xbbf330: mov             x0, x1
    // 0xbbf334: r3 = LoadInt32Instr(r4)
    //     0xbbf334: sbfx            x3, x4, #1, #0x1f
    //     0xbbf338: tbz             w4, #0, #0xbbf340
    //     0xbbf33c: ldur            x3, [x4, #7]
    // 0xbbf340: mov             x1, x2
    // 0xbbf344: mov             x2, x0
    // 0xbbf348: ldr             x5, [fp, #0x18]
    // 0xbbf34c: r0 = onlinePaymentMethodCard()
    //     0xbbf34c: bl              #0xbbf388  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::onlinePaymentMethodCard
    // 0xbbf350: stur            x0, [fp, #-8]
    // 0xbbf354: r0 = Padding()
    //     0xbbf354: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbf358: r1 = Instance_EdgeInsets
    //     0xbbf358: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbbf35c: ldr             x1, [x1, #0x1f0]
    // 0xbbf360: StoreField: r0->field_f = r1
    //     0xbbf360: stur            w1, [x0, #0xf]
    // 0xbbf364: ldur            x1, [fp, #-8]
    // 0xbbf368: StoreField: r0->field_b = r1
    //     0xbbf368: stur            w1, [x0, #0xb]
    // 0xbbf36c: LeaveFrame
    //     0xbbf36c: mov             SP, fp
    //     0xbbf370: ldp             fp, lr, [SP], #0x10
    // 0xbbf374: ret
    //     0xbbf374: ret             
    // 0xbbf378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbf378: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbf37c: b               #0xbbf29c
    // 0xbbf380: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf380: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbf384: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbbf384: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ onlinePaymentMethodCard(/* No info */) {
    // ** addr: 0xbbf388, size: 0x3e4
    // 0xbbf388: EnterFrame
    //     0xbbf388: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf38c: mov             fp, SP
    // 0xbbf390: AllocStack(0x60)
    //     0xbbf390: sub             SP, SP, #0x60
    // 0xbbf394: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xbbf394: mov             x0, x1
    //     0xbbf398: stur            x1, [fp, #-8]
    //     0xbbf39c: mov             x1, x5
    //     0xbbf3a0: stur            x2, [fp, #-0x10]
    //     0xbbf3a4: stur            x3, [fp, #-0x18]
    //     0xbbf3a8: stur            x5, [fp, #-0x20]
    // 0xbbf3ac: CheckStackOverflow
    //     0xbbf3ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf3b0: cmp             SP, x16
    //     0xbbf3b4: b.ls            #0xbbf740
    // 0xbbf3b8: r1 = 3
    //     0xbbf3b8: movz            x1, #0x3
    // 0xbbf3bc: r0 = AllocateContext()
    //     0xbbf3bc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbf3c0: mov             x4, x0
    // 0xbbf3c4: ldur            x3, [fp, #-8]
    // 0xbbf3c8: stur            x4, [fp, #-0x30]
    // 0xbbf3cc: StoreField: r4->field_f = r3
    //     0xbbf3cc: stur            w3, [x4, #0xf]
    // 0xbbf3d0: ldur            x5, [fp, #-0x10]
    // 0xbbf3d4: StoreField: r4->field_13 = r5
    //     0xbbf3d4: stur            w5, [x4, #0x13]
    // 0xbbf3d8: ldur            x6, [fp, #-0x18]
    // 0xbbf3dc: r0 = BoxInt64Instr(r6)
    //     0xbbf3dc: sbfiz           x0, x6, #1, #0x1f
    //     0xbbf3e0: cmp             x6, x0, asr #1
    //     0xbbf3e4: b.eq            #0xbbf3f0
    //     0xbbf3e8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbbf3ec: stur            x6, [x0, #7]
    // 0xbbf3f0: ArrayStore: r4[0] = r0  ; List_4
    //     0xbbf3f0: stur            w0, [x4, #0x17]
    // 0xbbf3f4: cmp             w5, NULL
    // 0xbbf3f8: b.ne            #0xbbf404
    // 0xbbf3fc: r0 = Null
    //     0xbbf3fc: mov             x0, NULL
    // 0xbbf400: b               #0xbbf40c
    // 0xbbf404: LoadField: r0 = r5->field_7
    //     0xbbf404: ldur            w0, [x5, #7]
    // 0xbbf408: DecompressPointer r0
    //     0xbbf408: add             x0, x0, HEAP, lsl #32
    // 0xbbf40c: cmp             w0, NULL
    // 0xbbf410: b.ne            #0xbbf418
    // 0xbbf414: r0 = ""
    //     0xbbf414: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbf418: stur            x0, [fp, #-0x28]
    // 0xbbf41c: r1 = Function '<anonymous closure>':.
    //     0xbbf41c: add             x1, PP, #0x54, lsl #12  ; [pp+0x54618] AnonymousClosure: (0xbbe5e0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::partialCodPaymentMethodCard (0xbbdfcc)
    //     0xbbf420: ldr             x1, [x1, #0x618]
    // 0xbbf424: r2 = Null
    //     0xbbf424: mov             x2, NULL
    // 0xbbf428: r0 = AllocateClosure()
    //     0xbbf428: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbf42c: stur            x0, [fp, #-0x38]
    // 0xbbf430: r0 = CachedNetworkImage()
    //     0xbbf430: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbbf434: stur            x0, [fp, #-0x40]
    // 0xbbf438: r16 = 24.000000
    //     0xbbf438: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbbf43c: ldr             x16, [x16, #0xba8]
    // 0xbbf440: r30 = 24.000000
    //     0xbbf440: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbbf444: ldr             lr, [lr, #0xba8]
    // 0xbbf448: stp             lr, x16, [SP, #8]
    // 0xbbf44c: ldur            x16, [fp, #-0x38]
    // 0xbbf450: str             x16, [SP]
    // 0xbbf454: mov             x1, x0
    // 0xbbf458: ldur            x2, [fp, #-0x28]
    // 0xbbf45c: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, height, 0x2, width, 0x3, null]
    //     0xbbf45c: add             x4, PP, #0x54, lsl #12  ; [pp+0x54540] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0xbbf460: ldr             x4, [x4, #0x540]
    // 0xbbf464: r0 = CachedNetworkImage()
    //     0xbbf464: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbbf468: ldur            x0, [fp, #-0x10]
    // 0xbbf46c: cmp             w0, NULL
    // 0xbbf470: b.ne            #0xbbf47c
    // 0xbbf474: r1 = Null
    //     0xbbf474: mov             x1, NULL
    // 0xbbf478: b               #0xbbf484
    // 0xbbf47c: LoadField: r1 = r0->field_b
    //     0xbbf47c: ldur            w1, [x0, #0xb]
    // 0xbbf480: DecompressPointer r1
    //     0xbbf480: add             x1, x1, HEAP, lsl #32
    // 0xbbf484: cmp             w1, NULL
    // 0xbbf488: b.ne            #0xbbf494
    // 0xbbf48c: r3 = ""
    //     0xbbf48c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbf490: b               #0xbbf498
    // 0xbbf494: mov             x3, x1
    // 0xbbf498: ldur            x2, [fp, #-8]
    // 0xbbf49c: ldur            x1, [fp, #-0x20]
    // 0xbbf4a0: stur            x3, [fp, #-0x28]
    // 0xbbf4a4: r0 = of()
    //     0xbbf4a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbf4a8: LoadField: r1 = r0->field_87
    //     0xbbf4a8: ldur            w1, [x0, #0x87]
    // 0xbbf4ac: DecompressPointer r1
    //     0xbbf4ac: add             x1, x1, HEAP, lsl #32
    // 0xbbf4b0: LoadField: r0 = r1->field_2b
    //     0xbbf4b0: ldur            w0, [x1, #0x2b]
    // 0xbbf4b4: DecompressPointer r0
    //     0xbbf4b4: add             x0, x0, HEAP, lsl #32
    // 0xbbf4b8: stur            x0, [fp, #-0x38]
    // 0xbbf4bc: r1 = Instance_Color
    //     0xbbf4bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbf4c0: d0 = 0.700000
    //     0xbbf4c0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbbf4c4: ldr             d0, [x17, #0xf48]
    // 0xbbf4c8: r0 = withOpacity()
    //     0xbbf4c8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbf4cc: r16 = 14.000000
    //     0xbbf4cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbf4d0: ldr             x16, [x16, #0x1d8]
    // 0xbbf4d4: stp             x16, x0, [SP]
    // 0xbbf4d8: ldur            x1, [fp, #-0x38]
    // 0xbbf4dc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbf4dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbf4e0: ldr             x4, [x4, #0x9b8]
    // 0xbbf4e4: r0 = copyWith()
    //     0xbbf4e4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbf4e8: stur            x0, [fp, #-0x38]
    // 0xbbf4ec: r0 = Text()
    //     0xbbf4ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbf4f0: mov             x1, x0
    // 0xbbf4f4: ldur            x0, [fp, #-0x28]
    // 0xbbf4f8: stur            x1, [fp, #-0x48]
    // 0xbbf4fc: StoreField: r1->field_b = r0
    //     0xbbf4fc: stur            w0, [x1, #0xb]
    // 0xbbf500: ldur            x0, [fp, #-0x38]
    // 0xbbf504: StoreField: r1->field_13 = r0
    //     0xbbf504: stur            w0, [x1, #0x13]
    // 0xbbf508: r0 = Padding()
    //     0xbbf508: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbf50c: mov             x1, x0
    // 0xbbf510: r0 = Instance_EdgeInsets
    //     0xbbf510: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbbf514: ldr             x0, [x0, #0xa78]
    // 0xbbf518: stur            x1, [fp, #-0x28]
    // 0xbbf51c: StoreField: r1->field_f = r0
    //     0xbbf51c: stur            w0, [x1, #0xf]
    // 0xbbf520: ldur            x0, [fp, #-0x48]
    // 0xbbf524: StoreField: r1->field_b = r0
    //     0xbbf524: stur            w0, [x1, #0xb]
    // 0xbbf528: ldur            x2, [fp, #-8]
    // 0xbbf52c: LoadField: r0 = r2->field_1f
    //     0xbbf52c: ldur            w0, [x2, #0x1f]
    // 0xbbf530: DecompressPointer r0
    //     0xbbf530: add             x0, x0, HEAP, lsl #32
    // 0xbbf534: r16 = Sentinel
    //     0xbbf534: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbf538: cmp             w0, w16
    // 0xbbf53c: b.eq            #0xbbf748
    // 0xbbf540: ldur            x3, [fp, #-0x10]
    // 0xbbf544: cmp             w3, NULL
    // 0xbbf548: b.ne            #0xbbf554
    // 0xbbf54c: r3 = Null
    //     0xbbf54c: mov             x3, NULL
    // 0xbbf550: b               #0xbbf560
    // 0xbbf554: LoadField: r4 = r3->field_f
    //     0xbbf554: ldur            w4, [x3, #0xf]
    // 0xbbf558: DecompressPointer r4
    //     0xbbf558: add             x4, x4, HEAP, lsl #32
    // 0xbbf55c: mov             x3, x4
    // 0xbbf560: r4 = LoadClassIdInstr(r0)
    //     0xbbf560: ldur            x4, [x0, #-1]
    //     0xbbf564: ubfx            x4, x4, #0xc, #0x14
    // 0xbbf568: stp             x3, x0, [SP]
    // 0xbbf56c: mov             x0, x4
    // 0xbbf570: mov             lr, x0
    // 0xbbf574: ldr             lr, [x21, lr, lsl #3]
    // 0xbbf578: blr             lr
    // 0xbbf57c: tbnz            w0, #4, #0xbbf5e4
    // 0xbbf580: ldur            x0, [fp, #-8]
    // 0xbbf584: ldur            x1, [fp, #-0x18]
    // 0xbbf588: LoadField: r2 = r0->field_23
    //     0xbbf588: ldur            w2, [x0, #0x23]
    // 0xbbf58c: DecompressPointer r2
    //     0xbbf58c: add             x2, x2, HEAP, lsl #32
    // 0xbbf590: r16 = Sentinel
    //     0xbbf590: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbf594: cmp             w2, w16
    // 0xbbf598: b.eq            #0xbbf754
    // 0xbbf59c: r3 = LoadInt32Instr(r2)
    //     0xbbf59c: sbfx            x3, x2, #1, #0x1f
    //     0xbbf5a0: tbz             w2, #0, #0xbbf5a8
    //     0xbbf5a4: ldur            x3, [x2, #7]
    // 0xbbf5a8: cmp             x3, x1
    // 0xbbf5ac: b.ne            #0xbbf5e4
    // 0xbbf5b0: LoadField: r1 = r0->field_1b
    //     0xbbf5b0: ldur            w1, [x0, #0x1b]
    // 0xbbf5b4: DecompressPointer r1
    //     0xbbf5b4: add             x1, x1, HEAP, lsl #32
    // 0xbbf5b8: r16 = Sentinel
    //     0xbbf5b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbf5bc: cmp             w1, w16
    // 0xbbf5c0: b.eq            #0xbbf760
    // 0xbbf5c4: r16 = "online"
    //     0xbbf5c4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xbbf5c8: ldr             x16, [x16, #0xa50]
    // 0xbbf5cc: stp             x1, x16, [SP]
    // 0xbbf5d0: r0 = ==()
    //     0xbbf5d0: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbbf5d4: tbnz            w0, #4, #0xbbf5e4
    // 0xbbf5d8: r3 = Instance_IconData
    //     0xbbf5d8: add             x3, PP, #0x34, lsl #12  ; [pp+0x34030] Obj!IconData@d55581
    //     0xbbf5dc: ldr             x3, [x3, #0x30]
    // 0xbbf5e0: b               #0xbbf5ec
    // 0xbbf5e4: r3 = Instance_IconData
    //     0xbbf5e4: add             x3, PP, #0x34, lsl #12  ; [pp+0x34038] Obj!IconData@d55561
    //     0xbbf5e8: ldr             x3, [x3, #0x38]
    // 0xbbf5ec: ldur            x2, [fp, #-0x40]
    // 0xbbf5f0: ldur            x0, [fp, #-0x28]
    // 0xbbf5f4: ldur            x1, [fp, #-0x20]
    // 0xbbf5f8: stur            x3, [fp, #-8]
    // 0xbbf5fc: r0 = of()
    //     0xbbf5fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbf600: LoadField: r1 = r0->field_5b
    //     0xbbf600: ldur            w1, [x0, #0x5b]
    // 0xbbf604: DecompressPointer r1
    //     0xbbf604: add             x1, x1, HEAP, lsl #32
    // 0xbbf608: stur            x1, [fp, #-0x10]
    // 0xbbf60c: r0 = Icon()
    //     0xbbf60c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbbf610: mov             x3, x0
    // 0xbbf614: ldur            x0, [fp, #-8]
    // 0xbbf618: stur            x3, [fp, #-0x20]
    // 0xbbf61c: StoreField: r3->field_b = r0
    //     0xbbf61c: stur            w0, [x3, #0xb]
    // 0xbbf620: ldur            x0, [fp, #-0x10]
    // 0xbbf624: StoreField: r3->field_23 = r0
    //     0xbbf624: stur            w0, [x3, #0x23]
    // 0xbbf628: r1 = Null
    //     0xbbf628: mov             x1, NULL
    // 0xbbf62c: r2 = 8
    //     0xbbf62c: movz            x2, #0x8
    // 0xbbf630: r0 = AllocateArray()
    //     0xbbf630: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbf634: mov             x2, x0
    // 0xbbf638: ldur            x0, [fp, #-0x40]
    // 0xbbf63c: stur            x2, [fp, #-8]
    // 0xbbf640: StoreField: r2->field_f = r0
    //     0xbbf640: stur            w0, [x2, #0xf]
    // 0xbbf644: ldur            x0, [fp, #-0x28]
    // 0xbbf648: StoreField: r2->field_13 = r0
    //     0xbbf648: stur            w0, [x2, #0x13]
    // 0xbbf64c: r16 = Instance_Spacer
    //     0xbbf64c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xbbf650: ldr             x16, [x16, #0xf0]
    // 0xbbf654: ArrayStore: r2[0] = r16  ; List_4
    //     0xbbf654: stur            w16, [x2, #0x17]
    // 0xbbf658: ldur            x0, [fp, #-0x20]
    // 0xbbf65c: StoreField: r2->field_1b = r0
    //     0xbbf65c: stur            w0, [x2, #0x1b]
    // 0xbbf660: r1 = <Widget>
    //     0xbbf660: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbf664: r0 = AllocateGrowableArray()
    //     0xbbf664: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbf668: mov             x1, x0
    // 0xbbf66c: ldur            x0, [fp, #-8]
    // 0xbbf670: stur            x1, [fp, #-0x10]
    // 0xbbf674: StoreField: r1->field_f = r0
    //     0xbbf674: stur            w0, [x1, #0xf]
    // 0xbbf678: r0 = 8
    //     0xbbf678: movz            x0, #0x8
    // 0xbbf67c: StoreField: r1->field_b = r0
    //     0xbbf67c: stur            w0, [x1, #0xb]
    // 0xbbf680: r0 = Row()
    //     0xbbf680: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbf684: mov             x1, x0
    // 0xbbf688: r0 = Instance_Axis
    //     0xbbf688: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbf68c: stur            x1, [fp, #-8]
    // 0xbbf690: StoreField: r1->field_f = r0
    //     0xbbf690: stur            w0, [x1, #0xf]
    // 0xbbf694: r0 = Instance_MainAxisAlignment
    //     0xbbf694: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbbf698: ldr             x0, [x0, #0xa8]
    // 0xbbf69c: StoreField: r1->field_13 = r0
    //     0xbbf69c: stur            w0, [x1, #0x13]
    // 0xbbf6a0: r0 = Instance_MainAxisSize
    //     0xbbf6a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbf6a4: ldr             x0, [x0, #0xa10]
    // 0xbbf6a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbf6a8: stur            w0, [x1, #0x17]
    // 0xbbf6ac: r0 = Instance_CrossAxisAlignment
    //     0xbbf6ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbf6b0: ldr             x0, [x0, #0xa18]
    // 0xbbf6b4: StoreField: r1->field_1b = r0
    //     0xbbf6b4: stur            w0, [x1, #0x1b]
    // 0xbbf6b8: r0 = Instance_VerticalDirection
    //     0xbbf6b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbf6bc: ldr             x0, [x0, #0xa20]
    // 0xbbf6c0: StoreField: r1->field_23 = r0
    //     0xbbf6c0: stur            w0, [x1, #0x23]
    // 0xbbf6c4: r0 = Instance_Clip
    //     0xbbf6c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbf6c8: ldr             x0, [x0, #0x38]
    // 0xbbf6cc: StoreField: r1->field_2b = r0
    //     0xbbf6cc: stur            w0, [x1, #0x2b]
    // 0xbbf6d0: StoreField: r1->field_2f = rZR
    //     0xbbf6d0: stur            xzr, [x1, #0x2f]
    // 0xbbf6d4: ldur            x0, [fp, #-0x10]
    // 0xbbf6d8: StoreField: r1->field_b = r0
    //     0xbbf6d8: stur            w0, [x1, #0xb]
    // 0xbbf6dc: r0 = InkWell()
    //     0xbbf6dc: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbf6e0: mov             x3, x0
    // 0xbbf6e4: ldur            x0, [fp, #-8]
    // 0xbbf6e8: stur            x3, [fp, #-0x10]
    // 0xbbf6ec: StoreField: r3->field_b = r0
    //     0xbbf6ec: stur            w0, [x3, #0xb]
    // 0xbbf6f0: ldur            x2, [fp, #-0x30]
    // 0xbbf6f4: r1 = Function '<anonymous closure>':.
    //     0xbbf6f4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54620] AnonymousClosure: (0xbbf76c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::onlinePaymentMethodCard (0xbbf388)
    //     0xbbf6f8: ldr             x1, [x1, #0x620]
    // 0xbbf6fc: r0 = AllocateClosure()
    //     0xbbf6fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbf700: mov             x1, x0
    // 0xbbf704: ldur            x0, [fp, #-0x10]
    // 0xbbf708: StoreField: r0->field_f = r1
    //     0xbbf708: stur            w1, [x0, #0xf]
    // 0xbbf70c: r1 = true
    //     0xbbf70c: add             x1, NULL, #0x20  ; true
    // 0xbbf710: StoreField: r0->field_43 = r1
    //     0xbbf710: stur            w1, [x0, #0x43]
    // 0xbbf714: r2 = Instance_BoxShape
    //     0xbbf714: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbf718: ldr             x2, [x2, #0x80]
    // 0xbbf71c: StoreField: r0->field_47 = r2
    //     0xbbf71c: stur            w2, [x0, #0x47]
    // 0xbbf720: StoreField: r0->field_6f = r1
    //     0xbbf720: stur            w1, [x0, #0x6f]
    // 0xbbf724: r2 = false
    //     0xbbf724: add             x2, NULL, #0x30  ; false
    // 0xbbf728: StoreField: r0->field_73 = r2
    //     0xbbf728: stur            w2, [x0, #0x73]
    // 0xbbf72c: StoreField: r0->field_83 = r1
    //     0xbbf72c: stur            w1, [x0, #0x83]
    // 0xbbf730: StoreField: r0->field_7b = r2
    //     0xbbf730: stur            w2, [x0, #0x7b]
    // 0xbbf734: LeaveFrame
    //     0xbbf734: mov             SP, fp
    //     0xbbf738: ldp             fp, lr, [SP], #0x10
    // 0xbbf73c: ret
    //     0xbbf73c: ret             
    // 0xbbf740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbf740: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbf744: b               #0xbbf3b8
    // 0xbbf748: r9 = selectedPaymentMethod
    //     0xbbf748: add             x9, PP, #0x54, lsl #12  ; [pp+0x54550] Field <<EMAIL>>: late (offset: 0x20)
    //     0xbbf74c: ldr             x9, [x9, #0x550]
    // 0xbbf750: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbf750: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbf754: r9 = paymentSelectedIndex
    //     0xbbf754: add             x9, PP, #0x54, lsl #12  ; [pp+0x54558] Field <<EMAIL>>: late (offset: 0x24)
    //     0xbbf758: ldr             x9, [x9, #0x558]
    // 0xbbf75c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbf75c: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbf760: r9 = selectedPaymentMode
    //     0xbbf760: add             x9, PP, #0x54, lsl #12  ; [pp+0x54560] Field <<EMAIL>>: late (offset: 0x1c)
    //     0xbbf764: ldr             x9, [x9, #0x560]
    // 0xbbf768: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbf768: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbf76c, size: 0x174
    // 0xbbf76c: EnterFrame
    //     0xbbf76c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf770: mov             fp, SP
    // 0xbbf774: AllocStack(0x38)
    //     0xbbf774: sub             SP, SP, #0x38
    // 0xbbf778: SetupParameters()
    //     0xbbf778: ldr             x0, [fp, #0x10]
    //     0xbbf77c: ldur            w3, [x0, #0x17]
    //     0xbbf780: add             x3, x3, HEAP, lsl #32
    //     0xbbf784: stur            x3, [fp, #-0x10]
    // 0xbbf788: CheckStackOverflow
    //     0xbbf788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbf78c: cmp             SP, x16
    //     0xbbf790: b.ls            #0xbbf8d0
    // 0xbbf794: LoadField: r0 = r3->field_f
    //     0xbbf794: ldur            w0, [x3, #0xf]
    // 0xbbf798: DecompressPointer r0
    //     0xbbf798: add             x0, x0, HEAP, lsl #32
    // 0xbbf79c: mov             x2, x3
    // 0xbbf7a0: stur            x0, [fp, #-8]
    // 0xbbf7a4: r1 = Function '<anonymous closure>':.
    //     0xbbf7a4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54628] AnonymousClosure: (0xbbf8e0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::onlinePaymentMethodCard (0xbbf388)
    //     0xbbf7a8: ldr             x1, [x1, #0x628]
    // 0xbbf7ac: r0 = AllocateClosure()
    //     0xbbf7ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbf7b0: ldur            x1, [fp, #-8]
    // 0xbbf7b4: mov             x2, x0
    // 0xbbf7b8: r0 = setState()
    //     0xbbf7b8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbbf7bc: ldur            x0, [fp, #-0x10]
    // 0xbbf7c0: LoadField: r1 = r0->field_f
    //     0xbbf7c0: ldur            w1, [x0, #0xf]
    // 0xbbf7c4: DecompressPointer r1
    //     0xbbf7c4: add             x1, x1, HEAP, lsl #32
    // 0xbbf7c8: LoadField: r2 = r1->field_b
    //     0xbbf7c8: ldur            w2, [x1, #0xb]
    // 0xbbf7cc: DecompressPointer r2
    //     0xbbf7cc: add             x2, x2, HEAP, lsl #32
    // 0xbbf7d0: cmp             w2, NULL
    // 0xbbf7d4: b.eq            #0xbbf8d8
    // 0xbbf7d8: LoadField: r1 = r0->field_13
    //     0xbbf7d8: ldur            w1, [x0, #0x13]
    // 0xbbf7dc: DecompressPointer r1
    //     0xbbf7dc: add             x1, x1, HEAP, lsl #32
    // 0xbbf7e0: cmp             w1, NULL
    // 0xbbf7e4: b.ne            #0xbbf7f0
    // 0xbbf7e8: r1 = Null
    //     0xbbf7e8: mov             x1, NULL
    // 0xbbf7ec: b               #0xbbf7fc
    // 0xbbf7f0: LoadField: r3 = r1->field_f
    //     0xbbf7f0: ldur            w3, [x1, #0xf]
    // 0xbbf7f4: DecompressPointer r3
    //     0xbbf7f4: add             x3, x3, HEAP, lsl #32
    // 0xbbf7f8: mov             x1, x3
    // 0xbbf7fc: cmp             w1, NULL
    // 0xbbf800: b.ne            #0xbbf808
    // 0xbbf804: r1 = ""
    //     0xbbf804: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbf808: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xbbf808: ldur            w3, [x0, #0x17]
    // 0xbbf80c: DecompressPointer r3
    //     0xbbf80c: add             x3, x3, HEAP, lsl #32
    // 0xbbf810: LoadField: r4 = r2->field_f
    //     0xbbf810: ldur            w4, [x2, #0xf]
    // 0xbbf814: DecompressPointer r4
    //     0xbbf814: add             x4, x4, HEAP, lsl #32
    // 0xbbf818: r16 = "online"
    //     0xbbf818: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xbbf81c: ldr             x16, [x16, #0xa50]
    // 0xbbf820: stp             x16, x4, [SP, #0x18]
    // 0xbbf824: stp             x3, x1, [SP, #8]
    // 0xbbf828: r16 = true
    //     0xbbf828: add             x16, NULL, #0x20  ; true
    // 0xbbf82c: str             x16, [SP]
    // 0xbbf830: r4 = 0
    //     0xbbf830: movz            x4, #0
    // 0xbbf834: ldr             x0, [SP, #0x20]
    // 0xbbf838: r16 = UnlinkedCall_0x613b5c
    //     0xbbf838: add             x16, PP, #0x54, lsl #12  ; [pp+0x54630] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbf83c: add             x16, x16, #0x630
    // 0xbbf840: ldp             x5, lr, [x16]
    // 0xbbf844: blr             lr
    // 0xbbf848: ldur            x0, [fp, #-0x10]
    // 0xbbf84c: LoadField: r1 = r0->field_f
    //     0xbbf84c: ldur            w1, [x0, #0xf]
    // 0xbbf850: DecompressPointer r1
    //     0xbbf850: add             x1, x1, HEAP, lsl #32
    // 0xbbf854: LoadField: r2 = r1->field_b
    //     0xbbf854: ldur            w2, [x1, #0xb]
    // 0xbbf858: DecompressPointer r2
    //     0xbbf858: add             x2, x2, HEAP, lsl #32
    // 0xbbf85c: cmp             w2, NULL
    // 0xbbf860: b.eq            #0xbbf8dc
    // 0xbbf864: LoadField: r1 = r0->field_13
    //     0xbbf864: ldur            w1, [x0, #0x13]
    // 0xbbf868: DecompressPointer r1
    //     0xbbf868: add             x1, x1, HEAP, lsl #32
    // 0xbbf86c: cmp             w1, NULL
    // 0xbbf870: b.ne            #0xbbf87c
    // 0xbbf874: r0 = Null
    //     0xbbf874: mov             x0, NULL
    // 0xbbf878: b               #0xbbf884
    // 0xbbf87c: LoadField: r0 = r1->field_f
    //     0xbbf87c: ldur            w0, [x1, #0xf]
    // 0xbbf880: DecompressPointer r0
    //     0xbbf880: add             x0, x0, HEAP, lsl #32
    // 0xbbf884: cmp             w0, NULL
    // 0xbbf888: b.ne            #0xbbf890
    // 0xbbf88c: r0 = ""
    //     0xbbf88c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbf890: LoadField: r1 = r2->field_23
    //     0xbbf890: ldur            w1, [x2, #0x23]
    // 0xbbf894: DecompressPointer r1
    //     0xbbf894: add             x1, x1, HEAP, lsl #32
    // 0xbbf898: r16 = "online"
    //     0xbbf898: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xbbf89c: ldr             x16, [x16, #0xa50]
    // 0xbbf8a0: stp             x16, x1, [SP, #8]
    // 0xbbf8a4: str             x0, [SP]
    // 0xbbf8a8: r4 = 0
    //     0xbbf8a8: movz            x4, #0
    // 0xbbf8ac: ldr             x0, [SP, #0x10]
    // 0xbbf8b0: r16 = UnlinkedCall_0x613b5c
    //     0xbbf8b0: add             x16, PP, #0x54, lsl #12  ; [pp+0x54640] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbbf8b4: add             x16, x16, #0x640
    // 0xbbf8b8: ldp             x5, lr, [x16]
    // 0xbbf8bc: blr             lr
    // 0xbbf8c0: r0 = Null
    //     0xbbf8c0: mov             x0, NULL
    // 0xbbf8c4: LeaveFrame
    //     0xbbf8c4: mov             SP, fp
    //     0xbbf8c8: ldp             fp, lr, [SP], #0x10
    // 0xbbf8cc: ret
    //     0xbbf8cc: ret             
    // 0xbbf8d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbf8d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbf8d4: b               #0xbbf794
    // 0xbbf8d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf8d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbf8dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbf8dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbf8e0, size: 0xbc
    // 0xbbf8e0: EnterFrame
    //     0xbbf8e0: stp             fp, lr, [SP, #-0x10]!
    //     0xbbf8e4: mov             fp, SP
    // 0xbbf8e8: r1 = "online"
    //     0xbbf8e8: add             x1, PP, #0x24, lsl #12  ; [pp+0x24a50] "online"
    //     0xbbf8ec: ldr             x1, [x1, #0xa50]
    // 0xbbf8f0: ldr             x2, [fp, #0x10]
    // 0xbbf8f4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbbf8f4: ldur            w3, [x2, #0x17]
    // 0xbbf8f8: DecompressPointer r3
    //     0xbbf8f8: add             x3, x3, HEAP, lsl #32
    // 0xbbf8fc: LoadField: r2 = r3->field_f
    //     0xbbf8fc: ldur            w2, [x3, #0xf]
    // 0xbbf900: DecompressPointer r2
    //     0xbbf900: add             x2, x2, HEAP, lsl #32
    // 0xbbf904: StoreField: r2->field_1b = r1
    //     0xbbf904: stur            w1, [x2, #0x1b]
    // 0xbbf908: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xbbf908: ldur            w0, [x3, #0x17]
    // 0xbbf90c: DecompressPointer r0
    //     0xbbf90c: add             x0, x0, HEAP, lsl #32
    // 0xbbf910: StoreField: r2->field_23 = r0
    //     0xbbf910: stur            w0, [x2, #0x23]
    //     0xbbf914: tbz             w0, #0, #0xbbf930
    //     0xbbf918: ldurb           w16, [x2, #-1]
    //     0xbbf91c: ldurb           w17, [x0, #-1]
    //     0xbbf920: and             x16, x17, x16, lsr #2
    //     0xbbf924: tst             x16, HEAP, lsr #32
    //     0xbbf928: b.eq            #0xbbf930
    //     0xbbf92c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbbf930: LoadField: r1 = r3->field_13
    //     0xbbf930: ldur            w1, [x3, #0x13]
    // 0xbbf934: DecompressPointer r1
    //     0xbbf934: add             x1, x1, HEAP, lsl #32
    // 0xbbf938: cmp             w1, NULL
    // 0xbbf93c: b.ne            #0xbbf948
    // 0xbbf940: r1 = Null
    //     0xbbf940: mov             x1, NULL
    // 0xbbf944: b               #0xbbf954
    // 0xbbf948: LoadField: r3 = r1->field_f
    //     0xbbf948: ldur            w3, [x1, #0xf]
    // 0xbbf94c: DecompressPointer r3
    //     0xbbf94c: add             x3, x3, HEAP, lsl #32
    // 0xbbf950: mov             x1, x3
    // 0xbbf954: cmp             w1, NULL
    // 0xbbf958: b.ne            #0xbbf964
    // 0xbbf95c: r0 = ""
    //     0xbbf95c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbbf960: b               #0xbbf968
    // 0xbbf964: mov             x0, x1
    // 0xbbf968: r1 = true
    //     0xbbf968: add             x1, NULL, #0x20  ; true
    // 0xbbf96c: StoreField: r2->field_1f = r0
    //     0xbbf96c: stur            w0, [x2, #0x1f]
    //     0xbbf970: ldurb           w16, [x2, #-1]
    //     0xbbf974: ldurb           w17, [x0, #-1]
    //     0xbbf978: and             x16, x17, x16, lsr #2
    //     0xbbf97c: tst             x16, HEAP, lsr #32
    //     0xbbf980: b.eq            #0xbbf988
    //     0xbbf984: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbbf988: StoreField: r2->field_2f = r1
    //     0xbbf988: stur            w1, [x2, #0x2f]
    // 0xbbf98c: r0 = Null
    //     0xbbf98c: mov             x0, NULL
    // 0xbbf990: LeaveFrame
    //     0xbbf990: mov             SP, fp
    //     0xbbf994: ldp             fp, lr, [SP], #0x10
    // 0xbbf998: ret
    //     0xbbf998: ret             
  }
  _ _ExpandablePaymentMethodWidgetState(/* No info */) {
    // ** addr: 0xc804c0, size: 0x84
    // 0xc804c0: EnterFrame
    //     0xc804c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc804c4: mov             fp, SP
    // 0xc804c8: AllocStack(0x8)
    //     0xc804c8: sub             SP, SP, #8
    // 0xc804cc: r2 = Sentinel
    //     0xc804cc: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc804d0: r0 = false
    //     0xc804d0: add             x0, NULL, #0x30  ; false
    // 0xc804d4: CheckStackOverflow
    //     0xc804d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc804d8: cmp             SP, x16
    //     0xc804dc: b.ls            #0xc8053c
    // 0xc804e0: StoreField: r1->field_1b = r2
    //     0xc804e0: stur            w2, [x1, #0x1b]
    // 0xc804e4: StoreField: r1->field_1f = r2
    //     0xc804e4: stur            w2, [x1, #0x1f]
    // 0xc804e8: StoreField: r1->field_23 = r2
    //     0xc804e8: stur            w2, [x1, #0x23]
    // 0xc804ec: StoreField: r1->field_27 = r2
    //     0xc804ec: stur            w2, [x1, #0x27]
    // 0xc804f0: StoreField: r1->field_2b = r0
    //     0xc804f0: stur            w0, [x1, #0x2b]
    // 0xc804f4: StoreField: r1->field_2f = r0
    //     0xc804f4: stur            w0, [x1, #0x2f]
    // 0xc804f8: r1 = <bool>
    //     0xc804f8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xc804fc: r0 = RxBool()
    //     0xc804fc: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xc80500: mov             x2, x0
    // 0xc80504: r0 = Sentinel
    //     0xc80504: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80508: stur            x2, [fp, #-8]
    // 0xc8050c: StoreField: r2->field_13 = r0
    //     0xc8050c: stur            w0, [x2, #0x13]
    // 0xc80510: r0 = true
    //     0xc80510: add             x0, NULL, #0x20  ; true
    // 0xc80514: ArrayStore: r2[0] = r0  ; List_4
    //     0xc80514: stur            w0, [x2, #0x17]
    // 0xc80518: mov             x1, x2
    // 0xc8051c: r0 = RxNotifier()
    //     0xc8051c: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xc80520: ldur            x1, [fp, #-8]
    // 0xc80524: r2 = false
    //     0xc80524: add             x2, NULL, #0x30  ; false
    // 0xc80528: StoreField: r1->field_13 = r2
    //     0xc80528: stur            w2, [x1, #0x13]
    // 0xc8052c: r0 = Null
    //     0xc8052c: mov             x0, NULL
    // 0xc80530: LeaveFrame
    //     0xc80530: mov             SP, fp
    //     0xc80534: ldp             fp, lr, [SP], #0x10
    // 0xc80538: ret
    //     0xc80538: ret             
    // 0xc8053c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8053c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80540: b               #0xc804e0
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc8804c, size: 0x64
    // 0xc8804c: EnterFrame
    //     0xc8804c: stp             fp, lr, [SP, #-0x10]!
    //     0xc88050: mov             fp, SP
    // 0xc88054: AllocStack(0x8)
    //     0xc88054: sub             SP, SP, #8
    // 0xc88058: SetupParameters(_ExpandablePaymentMethodWidgetState this /* r1 => r0, fp-0x8 */)
    //     0xc88058: mov             x0, x1
    //     0xc8805c: stur            x1, [fp, #-8]
    // 0xc88060: CheckStackOverflow
    //     0xc88060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88064: cmp             SP, x16
    //     0xc88068: b.ls            #0xc8809c
    // 0xc8806c: LoadField: r1 = r0->field_27
    //     0xc8806c: ldur            w1, [x0, #0x27]
    // 0xc88070: DecompressPointer r1
    //     0xc88070: add             x1, x1, HEAP, lsl #32
    // 0xc88074: r16 = Sentinel
    //     0xc88074: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88078: cmp             w1, w16
    // 0xc8807c: b.eq            #0xc880a4
    // 0xc88080: r0 = dispose()
    //     0xc88080: bl              #0x75e058  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xc88084: ldur            x1, [fp, #-8]
    // 0xc88088: r0 = dispose()
    //     0xc88088: bl              #0xc880b0  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] __ExpandablePaymentMethodWidgetState&State&SingleTickerProviderStateMixin::dispose
    // 0xc8808c: r0 = Null
    //     0xc8808c: mov             x0, NULL
    // 0xc88090: LeaveFrame
    //     0xc88090: mov             SP, fp
    //     0xc88094: ldp             fp, lr, [SP], #0x10
    // 0xc88098: ret
    //     0xc88098: ret             
    // 0xc8809c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc8809c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc880a0: b               #0xc8806c
    // 0xc880a4: r9 = _animationController
    //     0xc880a4: add             x9, PP, #0x54, lsl #12  ; [pp+0x54528] Field <_ExpandablePaymentMethodWidgetState@**********._animationController@**********>: late (offset: 0x28)
    //     0xc880a8: ldr             x9, [x9, #0x528]
    // 0xc880ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc880ac: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4013, size: 0x2c, field offset: 0xc
//   const constructor, 
class ExpandablePaymentMethodWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80478, size: 0x48
    // 0xc80478: EnterFrame
    //     0xc80478: stp             fp, lr, [SP, #-0x10]!
    //     0xc8047c: mov             fp, SP
    // 0xc80480: AllocStack(0x8)
    //     0xc80480: sub             SP, SP, #8
    // 0xc80484: CheckStackOverflow
    //     0xc80484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80488: cmp             SP, x16
    //     0xc8048c: b.ls            #0xc804b8
    // 0xc80490: r1 = <ExpandablePaymentMethodWidget>
    //     0xc80490: add             x1, PP, #0x48, lsl #12  ; [pp+0x48690] TypeArguments: <ExpandablePaymentMethodWidget>
    //     0xc80494: ldr             x1, [x1, #0x690]
    // 0xc80498: r0 = _ExpandablePaymentMethodWidgetState()
    //     0xc80498: bl              #0xc80544  ; Allocate_ExpandablePaymentMethodWidgetStateStub -> _ExpandablePaymentMethodWidgetState (size=0x34)
    // 0xc8049c: mov             x1, x0
    // 0xc804a0: stur            x0, [fp, #-8]
    // 0xc804a4: r0 = _ExpandablePaymentMethodWidgetState()
    //     0xc804a4: bl              #0xc804c0  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/expandable_payment_method_widget.dart] _ExpandablePaymentMethodWidgetState::_ExpandablePaymentMethodWidgetState
    // 0xc804a8: ldur            x0, [fp, #-8]
    // 0xc804ac: LeaveFrame
    //     0xc804ac: mov             SP, fp
    //     0xc804b0: ldp             fp, lr, [SP], #0x10
    // 0xc804b4: ret
    //     0xc804b4: ret             
    // 0xc804b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc804b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc804bc: b               #0xc80490
  }
}
