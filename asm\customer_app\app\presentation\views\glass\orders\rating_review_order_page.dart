// lib: , url: package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart

// class id: 1049417, size: 0x8
class :: {
}

// class id: 4562, size: 0x14, field offset: 0x14
//   const constructor, 
class RatingReviewOrderPage extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x136164c, size: 0x64
    // 0x136164c: EnterFrame
    //     0x136164c: stp             fp, lr, [SP, #-0x10]!
    //     0x1361650: mov             fp, SP
    // 0x1361654: AllocStack(0x18)
    //     0x1361654: sub             SP, SP, #0x18
    // 0x1361658: SetupParameters(RatingReviewOrderPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1361658: stur            x1, [fp, #-8]
    //     0x136165c: stur            x2, [fp, #-0x10]
    // 0x1361660: r1 = 2
    //     0x1361660: movz            x1, #0x2
    // 0x1361664: r0 = AllocateContext()
    //     0x1361664: bl              #0x16f6108  ; AllocateContextStub
    // 0x1361668: mov             x1, x0
    // 0x136166c: ldur            x0, [fp, #-8]
    // 0x1361670: stur            x1, [fp, #-0x18]
    // 0x1361674: StoreField: r1->field_f = r0
    //     0x1361674: stur            w0, [x1, #0xf]
    // 0x1361678: ldur            x0, [fp, #-0x10]
    // 0x136167c: StoreField: r1->field_13 = r0
    //     0x136167c: stur            w0, [x1, #0x13]
    // 0x1361680: r0 = Obx()
    //     0x1361680: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1361684: ldur            x2, [fp, #-0x18]
    // 0x1361688: r1 = Function '<anonymous closure>':.
    //     0x1361688: add             x1, PP, #0x40, lsl #12  ; [pp+0x40020] AnonymousClosure: (0x13616b0), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::bottomNavigationBar (0x136164c)
    //     0x136168c: ldr             x1, [x1, #0x20]
    // 0x1361690: stur            x0, [fp, #-8]
    // 0x1361694: r0 = AllocateClosure()
    //     0x1361694: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1361698: mov             x1, x0
    // 0x136169c: ldur            x0, [fp, #-8]
    // 0x13616a0: StoreField: r0->field_b = r1
    //     0x13616a0: stur            w1, [x0, #0xb]
    // 0x13616a4: LeaveFrame
    //     0x13616a4: mov             SP, fp
    //     0x13616a8: ldp             fp, lr, [SP], #0x10
    // 0x13616ac: ret
    //     0x13616ac: ret             
  }
  [closure] Wrap <anonymous closure>(dynamic) {
    // ** addr: 0x13616b0, size: 0x6c0
    // 0x13616b0: EnterFrame
    //     0x13616b0: stp             fp, lr, [SP, #-0x10]!
    //     0x13616b4: mov             fp, SP
    // 0x13616b8: AllocStack(0x50)
    //     0x13616b8: sub             SP, SP, #0x50
    // 0x13616bc: SetupParameters()
    //     0x13616bc: ldr             x0, [fp, #0x10]
    //     0x13616c0: ldur            w2, [x0, #0x17]
    //     0x13616c4: add             x2, x2, HEAP, lsl #32
    //     0x13616c8: stur            x2, [fp, #-8]
    // 0x13616cc: CheckStackOverflow
    //     0x13616cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13616d0: cmp             SP, x16
    //     0x13616d4: b.ls            #0x1361d68
    // 0x13616d8: r16 = <EdgeInsets>
    //     0x13616d8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13616dc: ldr             x16, [x16, #0xda0]
    // 0x13616e0: r30 = Instance_EdgeInsets
    //     0x13616e0: add             lr, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0x13616e4: ldr             lr, [lr, #0x670]
    // 0x13616e8: stp             lr, x16, [SP]
    // 0x13616ec: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13616ec: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13616f0: r0 = all()
    //     0x13616f0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13616f4: ldur            x2, [fp, #-8]
    // 0x13616f8: stur            x0, [fp, #-0x10]
    // 0x13616fc: LoadField: r1 = r2->field_f
    //     0x13616fc: ldur            w1, [x2, #0xf]
    // 0x1361700: DecompressPointer r1
    //     0x1361700: add             x1, x1, HEAP, lsl #32
    // 0x1361704: r0 = controller()
    //     0x1361704: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1361708: LoadField: r1 = r0->field_c3
    //     0x1361708: ldur            w1, [x0, #0xc3]
    // 0x136170c: DecompressPointer r1
    //     0x136170c: add             x1, x1, HEAP, lsl #32
    // 0x1361710: r0 = value()
    //     0x1361710: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361714: LoadField: d0 = r0->field_7
    //     0x1361714: ldur            d0, [x0, #7]
    // 0x1361718: d1 = 0.000000
    //     0x1361718: eor             v1.16b, v1.16b, v1.16b
    // 0x136171c: fcmp            d0, d1
    // 0x1361720: b.eq            #0x136190c
    // 0x1361724: ldur            x2, [fp, #-8]
    // 0x1361728: LoadField: r1 = r2->field_f
    //     0x1361728: ldur            w1, [x2, #0xf]
    // 0x136172c: DecompressPointer r1
    //     0x136172c: add             x1, x1, HEAP, lsl #32
    // 0x1361730: r0 = controller()
    //     0x1361730: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1361734: LoadField: r1 = r0->field_53
    //     0x1361734: ldur            w1, [x0, #0x53]
    // 0x1361738: DecompressPointer r1
    //     0x1361738: add             x1, x1, HEAP, lsl #32
    // 0x136173c: r0 = value()
    //     0x136173c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361740: LoadField: r1 = r0->field_3f
    //     0x1361740: ldur            w1, [x0, #0x3f]
    // 0x1361744: DecompressPointer r1
    //     0x1361744: add             x1, x1, HEAP, lsl #32
    // 0x1361748: cmp             w1, NULL
    // 0x136174c: b.ne            #0x1361758
    // 0x1361750: r0 = Null
    //     0x1361750: mov             x0, NULL
    // 0x1361754: b               #0x136177c
    // 0x1361758: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1361758: ldur            w0, [x1, #0x17]
    // 0x136175c: DecompressPointer r0
    //     0x136175c: add             x0, x0, HEAP, lsl #32
    // 0x1361760: cmp             w0, NULL
    // 0x1361764: b.ne            #0x1361770
    // 0x1361768: r0 = Null
    //     0x1361768: mov             x0, NULL
    // 0x136176c: b               #0x136177c
    // 0x1361770: LoadField: r1 = r0->field_7
    //     0x1361770: ldur            w1, [x0, #7]
    // 0x1361774: DecompressPointer r1
    //     0x1361774: add             x1, x1, HEAP, lsl #32
    // 0x1361778: mov             x0, x1
    // 0x136177c: cmp             w0, NULL
    // 0x1361780: b.ne            #0x136178c
    // 0x1361784: r0 = 0
    //     0x1361784: movz            x0, #0
    // 0x1361788: b               #0x136179c
    // 0x136178c: r1 = LoadInt32Instr(r0)
    //     0x136178c: sbfx            x1, x0, #1, #0x1f
    //     0x1361790: tbz             w0, #0, #0x1361798
    //     0x1361794: ldur            x1, [x0, #7]
    // 0x1361798: mov             x0, x1
    // 0x136179c: ldur            x2, [fp, #-8]
    // 0x13617a0: stur            x0, [fp, #-0x18]
    // 0x13617a4: LoadField: r1 = r2->field_f
    //     0x13617a4: ldur            w1, [x2, #0xf]
    // 0x13617a8: DecompressPointer r1
    //     0x13617a8: add             x1, x1, HEAP, lsl #32
    // 0x13617ac: r0 = controller()
    //     0x13617ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13617b0: LoadField: r1 = r0->field_53
    //     0x13617b0: ldur            w1, [x0, #0x53]
    // 0x13617b4: DecompressPointer r1
    //     0x13617b4: add             x1, x1, HEAP, lsl #32
    // 0x13617b8: r0 = value()
    //     0x13617b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13617bc: LoadField: r1 = r0->field_3f
    //     0x13617bc: ldur            w1, [x0, #0x3f]
    // 0x13617c0: DecompressPointer r1
    //     0x13617c0: add             x1, x1, HEAP, lsl #32
    // 0x13617c4: cmp             w1, NULL
    // 0x13617c8: b.ne            #0x13617d4
    // 0x13617cc: r0 = Null
    //     0x13617cc: mov             x0, NULL
    // 0x13617d0: b               #0x13617f8
    // 0x13617d4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13617d4: ldur            w0, [x1, #0x17]
    // 0x13617d8: DecompressPointer r0
    //     0x13617d8: add             x0, x0, HEAP, lsl #32
    // 0x13617dc: cmp             w0, NULL
    // 0x13617e0: b.ne            #0x13617ec
    // 0x13617e4: r0 = Null
    //     0x13617e4: mov             x0, NULL
    // 0x13617e8: b               #0x13617f8
    // 0x13617ec: LoadField: r1 = r0->field_b
    //     0x13617ec: ldur            w1, [x0, #0xb]
    // 0x13617f0: DecompressPointer r1
    //     0x13617f0: add             x1, x1, HEAP, lsl #32
    // 0x13617f4: mov             x0, x1
    // 0x13617f8: cmp             w0, NULL
    // 0x13617fc: b.ne            #0x1361808
    // 0x1361800: r0 = 0
    //     0x1361800: movz            x0, #0
    // 0x1361804: b               #0x1361818
    // 0x1361808: r1 = LoadInt32Instr(r0)
    //     0x1361808: sbfx            x1, x0, #1, #0x1f
    //     0x136180c: tbz             w0, #0, #0x1361814
    //     0x1361810: ldur            x1, [x0, #7]
    // 0x1361814: mov             x0, x1
    // 0x1361818: ldur            x2, [fp, #-8]
    // 0x136181c: stur            x0, [fp, #-0x20]
    // 0x1361820: LoadField: r1 = r2->field_f
    //     0x1361820: ldur            w1, [x2, #0xf]
    // 0x1361824: DecompressPointer r1
    //     0x1361824: add             x1, x1, HEAP, lsl #32
    // 0x1361828: r0 = controller()
    //     0x1361828: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x136182c: LoadField: r1 = r0->field_53
    //     0x136182c: ldur            w1, [x0, #0x53]
    // 0x1361830: DecompressPointer r1
    //     0x1361830: add             x1, x1, HEAP, lsl #32
    // 0x1361834: r0 = value()
    //     0x1361834: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361838: LoadField: r1 = r0->field_3f
    //     0x1361838: ldur            w1, [x0, #0x3f]
    // 0x136183c: DecompressPointer r1
    //     0x136183c: add             x1, x1, HEAP, lsl #32
    // 0x1361840: cmp             w1, NULL
    // 0x1361844: b.ne            #0x1361850
    // 0x1361848: r0 = Null
    //     0x1361848: mov             x0, NULL
    // 0x136184c: b               #0x1361874
    // 0x1361850: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1361850: ldur            w0, [x1, #0x17]
    // 0x1361854: DecompressPointer r0
    //     0x1361854: add             x0, x0, HEAP, lsl #32
    // 0x1361858: cmp             w0, NULL
    // 0x136185c: b.ne            #0x1361868
    // 0x1361860: r0 = Null
    //     0x1361860: mov             x0, NULL
    // 0x1361864: b               #0x1361874
    // 0x1361868: LoadField: r1 = r0->field_f
    //     0x1361868: ldur            w1, [x0, #0xf]
    // 0x136186c: DecompressPointer r1
    //     0x136186c: add             x1, x1, HEAP, lsl #32
    // 0x1361870: mov             x0, x1
    // 0x1361874: cmp             w0, NULL
    // 0x1361878: b.ne            #0x1361884
    // 0x136187c: r0 = 0
    //     0x136187c: movz            x0, #0
    // 0x1361880: b               #0x1361894
    // 0x1361884: r1 = LoadInt32Instr(r0)
    //     0x1361884: sbfx            x1, x0, #1, #0x1f
    //     0x1361888: tbz             w0, #0, #0x1361890
    //     0x136188c: ldur            x1, [x0, #7]
    // 0x1361890: mov             x0, x1
    // 0x1361894: stur            x0, [fp, #-0x28]
    // 0x1361898: r0 = Color()
    //     0x1361898: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x136189c: mov             x1, x0
    // 0x13618a0: r0 = Instance_ColorSpace
    //     0x13618a0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x13618a4: StoreField: r1->field_27 = r0
    //     0x13618a4: stur            w0, [x1, #0x27]
    // 0x13618a8: d0 = 1.000000
    //     0x13618a8: fmov            d0, #1.00000000
    // 0x13618ac: StoreField: r1->field_7 = d0
    //     0x13618ac: stur            d0, [x1, #7]
    // 0x13618b0: ldur            x0, [fp, #-0x18]
    // 0x13618b4: ubfx            x0, x0, #0, #0x20
    // 0x13618b8: and             w2, w0, #0xff
    // 0x13618bc: ubfx            x2, x2, #0, #0x20
    // 0x13618c0: scvtf           d0, x2
    // 0x13618c4: d1 = 255.000000
    //     0x13618c4: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x13618c8: fdiv            d2, d0, d1
    // 0x13618cc: StoreField: r1->field_f = d2
    //     0x13618cc: stur            d2, [x1, #0xf]
    // 0x13618d0: ldur            x0, [fp, #-0x20]
    // 0x13618d4: ubfx            x0, x0, #0, #0x20
    // 0x13618d8: and             w2, w0, #0xff
    // 0x13618dc: ubfx            x2, x2, #0, #0x20
    // 0x13618e0: scvtf           d0, x2
    // 0x13618e4: fdiv            d2, d0, d1
    // 0x13618e8: ArrayStore: r1[0] = d2  ; List_8
    //     0x13618e8: stur            d2, [x1, #0x17]
    // 0x13618ec: ldur            x0, [fp, #-0x28]
    // 0x13618f0: ubfx            x0, x0, #0, #0x20
    // 0x13618f4: and             w2, w0, #0xff
    // 0x13618f8: ubfx            x2, x2, #0, #0x20
    // 0x13618fc: scvtf           d0, x2
    // 0x1361900: fdiv            d2, d0, d1
    // 0x1361904: StoreField: r1->field_1f = d2
    //     0x1361904: stur            d2, [x1, #0x1f]
    // 0x1361908: b               #0x1361afc
    // 0x136190c: ldur            x2, [fp, #-8]
    // 0x1361910: r0 = Instance_ColorSpace
    //     0x1361910: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1361914: d1 = 255.000000
    //     0x1361914: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1361918: LoadField: r1 = r2->field_f
    //     0x1361918: ldur            w1, [x2, #0xf]
    // 0x136191c: DecompressPointer r1
    //     0x136191c: add             x1, x1, HEAP, lsl #32
    // 0x1361920: r0 = controller()
    //     0x1361920: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1361924: LoadField: r1 = r0->field_53
    //     0x1361924: ldur            w1, [x0, #0x53]
    // 0x1361928: DecompressPointer r1
    //     0x1361928: add             x1, x1, HEAP, lsl #32
    // 0x136192c: r0 = value()
    //     0x136192c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361930: LoadField: r1 = r0->field_3f
    //     0x1361930: ldur            w1, [x0, #0x3f]
    // 0x1361934: DecompressPointer r1
    //     0x1361934: add             x1, x1, HEAP, lsl #32
    // 0x1361938: cmp             w1, NULL
    // 0x136193c: b.ne            #0x1361948
    // 0x1361940: r0 = Null
    //     0x1361940: mov             x0, NULL
    // 0x1361944: b               #0x136196c
    // 0x1361948: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1361948: ldur            w0, [x1, #0x17]
    // 0x136194c: DecompressPointer r0
    //     0x136194c: add             x0, x0, HEAP, lsl #32
    // 0x1361950: cmp             w0, NULL
    // 0x1361954: b.ne            #0x1361960
    // 0x1361958: r0 = Null
    //     0x1361958: mov             x0, NULL
    // 0x136195c: b               #0x136196c
    // 0x1361960: LoadField: r1 = r0->field_7
    //     0x1361960: ldur            w1, [x0, #7]
    // 0x1361964: DecompressPointer r1
    //     0x1361964: add             x1, x1, HEAP, lsl #32
    // 0x1361968: mov             x0, x1
    // 0x136196c: cmp             w0, NULL
    // 0x1361970: b.ne            #0x136197c
    // 0x1361974: r0 = 0
    //     0x1361974: movz            x0, #0
    // 0x1361978: b               #0x136198c
    // 0x136197c: r1 = LoadInt32Instr(r0)
    //     0x136197c: sbfx            x1, x0, #1, #0x1f
    //     0x1361980: tbz             w0, #0, #0x1361988
    //     0x1361984: ldur            x1, [x0, #7]
    // 0x1361988: mov             x0, x1
    // 0x136198c: ldur            x2, [fp, #-8]
    // 0x1361990: stur            x0, [fp, #-0x18]
    // 0x1361994: LoadField: r1 = r2->field_f
    //     0x1361994: ldur            w1, [x2, #0xf]
    // 0x1361998: DecompressPointer r1
    //     0x1361998: add             x1, x1, HEAP, lsl #32
    // 0x136199c: r0 = controller()
    //     0x136199c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13619a0: LoadField: r1 = r0->field_53
    //     0x13619a0: ldur            w1, [x0, #0x53]
    // 0x13619a4: DecompressPointer r1
    //     0x13619a4: add             x1, x1, HEAP, lsl #32
    // 0x13619a8: r0 = value()
    //     0x13619a8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13619ac: LoadField: r1 = r0->field_3f
    //     0x13619ac: ldur            w1, [x0, #0x3f]
    // 0x13619b0: DecompressPointer r1
    //     0x13619b0: add             x1, x1, HEAP, lsl #32
    // 0x13619b4: cmp             w1, NULL
    // 0x13619b8: b.ne            #0x13619c4
    // 0x13619bc: r0 = Null
    //     0x13619bc: mov             x0, NULL
    // 0x13619c0: b               #0x13619e8
    // 0x13619c4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x13619c4: ldur            w0, [x1, #0x17]
    // 0x13619c8: DecompressPointer r0
    //     0x13619c8: add             x0, x0, HEAP, lsl #32
    // 0x13619cc: cmp             w0, NULL
    // 0x13619d0: b.ne            #0x13619dc
    // 0x13619d4: r0 = Null
    //     0x13619d4: mov             x0, NULL
    // 0x13619d8: b               #0x13619e8
    // 0x13619dc: LoadField: r1 = r0->field_b
    //     0x13619dc: ldur            w1, [x0, #0xb]
    // 0x13619e0: DecompressPointer r1
    //     0x13619e0: add             x1, x1, HEAP, lsl #32
    // 0x13619e4: mov             x0, x1
    // 0x13619e8: cmp             w0, NULL
    // 0x13619ec: b.ne            #0x13619f8
    // 0x13619f0: r0 = 0
    //     0x13619f0: movz            x0, #0
    // 0x13619f4: b               #0x1361a08
    // 0x13619f8: r1 = LoadInt32Instr(r0)
    //     0x13619f8: sbfx            x1, x0, #1, #0x1f
    //     0x13619fc: tbz             w0, #0, #0x1361a04
    //     0x1361a00: ldur            x1, [x0, #7]
    // 0x1361a04: mov             x0, x1
    // 0x1361a08: ldur            x2, [fp, #-8]
    // 0x1361a0c: stur            x0, [fp, #-0x20]
    // 0x1361a10: LoadField: r1 = r2->field_f
    //     0x1361a10: ldur            w1, [x2, #0xf]
    // 0x1361a14: DecompressPointer r1
    //     0x1361a14: add             x1, x1, HEAP, lsl #32
    // 0x1361a18: r0 = controller()
    //     0x1361a18: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1361a1c: LoadField: r1 = r0->field_53
    //     0x1361a1c: ldur            w1, [x0, #0x53]
    // 0x1361a20: DecompressPointer r1
    //     0x1361a20: add             x1, x1, HEAP, lsl #32
    // 0x1361a24: r0 = value()
    //     0x1361a24: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1361a28: LoadField: r1 = r0->field_3f
    //     0x1361a28: ldur            w1, [x0, #0x3f]
    // 0x1361a2c: DecompressPointer r1
    //     0x1361a2c: add             x1, x1, HEAP, lsl #32
    // 0x1361a30: cmp             w1, NULL
    // 0x1361a34: b.ne            #0x1361a40
    // 0x1361a38: r0 = Null
    //     0x1361a38: mov             x0, NULL
    // 0x1361a3c: b               #0x1361a64
    // 0x1361a40: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1361a40: ldur            w0, [x1, #0x17]
    // 0x1361a44: DecompressPointer r0
    //     0x1361a44: add             x0, x0, HEAP, lsl #32
    // 0x1361a48: cmp             w0, NULL
    // 0x1361a4c: b.ne            #0x1361a58
    // 0x1361a50: r0 = Null
    //     0x1361a50: mov             x0, NULL
    // 0x1361a54: b               #0x1361a64
    // 0x1361a58: LoadField: r1 = r0->field_f
    //     0x1361a58: ldur            w1, [x0, #0xf]
    // 0x1361a5c: DecompressPointer r1
    //     0x1361a5c: add             x1, x1, HEAP, lsl #32
    // 0x1361a60: mov             x0, x1
    // 0x1361a64: cmp             w0, NULL
    // 0x1361a68: b.ne            #0x1361a74
    // 0x1361a6c: r0 = 0
    //     0x1361a6c: movz            x0, #0
    // 0x1361a70: b               #0x1361a84
    // 0x1361a74: r1 = LoadInt32Instr(r0)
    //     0x1361a74: sbfx            x1, x0, #1, #0x1f
    //     0x1361a78: tbz             w0, #0, #0x1361a80
    //     0x1361a7c: ldur            x1, [x0, #7]
    // 0x1361a80: mov             x0, x1
    // 0x1361a84: stur            x0, [fp, #-0x28]
    // 0x1361a88: r0 = Color()
    //     0x1361a88: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0x1361a8c: mov             x1, x0
    // 0x1361a90: r0 = Instance_ColorSpace
    //     0x1361a90: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0x1361a94: StoreField: r1->field_27 = r0
    //     0x1361a94: stur            w0, [x1, #0x27]
    // 0x1361a98: d0 = 0.300000
    //     0x1361a98: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x1361a9c: ldr             d0, [x17, #0x658]
    // 0x1361aa0: StoreField: r1->field_7 = d0
    //     0x1361aa0: stur            d0, [x1, #7]
    // 0x1361aa4: ldur            x0, [fp, #-0x18]
    // 0x1361aa8: ubfx            x0, x0, #0, #0x20
    // 0x1361aac: and             w2, w0, #0xff
    // 0x1361ab0: ubfx            x2, x2, #0, #0x20
    // 0x1361ab4: scvtf           d0, x2
    // 0x1361ab8: d1 = 255.000000
    //     0x1361ab8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0x1361abc: fdiv            d2, d0, d1
    // 0x1361ac0: StoreField: r1->field_f = d2
    //     0x1361ac0: stur            d2, [x1, #0xf]
    // 0x1361ac4: ldur            x0, [fp, #-0x20]
    // 0x1361ac8: ubfx            x0, x0, #0, #0x20
    // 0x1361acc: and             w2, w0, #0xff
    // 0x1361ad0: ubfx            x2, x2, #0, #0x20
    // 0x1361ad4: scvtf           d0, x2
    // 0x1361ad8: fdiv            d2, d0, d1
    // 0x1361adc: ArrayStore: r1[0] = d2  ; List_8
    //     0x1361adc: stur            d2, [x1, #0x17]
    // 0x1361ae0: ldur            x0, [fp, #-0x28]
    // 0x1361ae4: ubfx            x0, x0, #0, #0x20
    // 0x1361ae8: and             w2, w0, #0xff
    // 0x1361aec: ubfx            x2, x2, #0, #0x20
    // 0x1361af0: scvtf           d0, x2
    // 0x1361af4: fdiv            d2, d0, d1
    // 0x1361af8: StoreField: r1->field_1f = d2
    //     0x1361af8: stur            d2, [x1, #0x1f]
    // 0x1361afc: ldur            x2, [fp, #-8]
    // 0x1361b00: ldur            x0, [fp, #-0x10]
    // 0x1361b04: r16 = <Color>
    //     0x1361b04: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1361b08: ldr             x16, [x16, #0xf80]
    // 0x1361b0c: stp             x1, x16, [SP]
    // 0x1361b10: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1361b10: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1361b14: r0 = all()
    //     0x1361b14: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1361b18: stur            x0, [fp, #-0x30]
    // 0x1361b1c: r0 = Radius()
    //     0x1361b1c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1361b20: d0 = 30.000000
    //     0x1361b20: fmov            d0, #30.00000000
    // 0x1361b24: stur            x0, [fp, #-0x38]
    // 0x1361b28: StoreField: r0->field_7 = d0
    //     0x1361b28: stur            d0, [x0, #7]
    // 0x1361b2c: StoreField: r0->field_f = d0
    //     0x1361b2c: stur            d0, [x0, #0xf]
    // 0x1361b30: r0 = BorderRadius()
    //     0x1361b30: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1361b34: mov             x1, x0
    // 0x1361b38: ldur            x0, [fp, #-0x38]
    // 0x1361b3c: stur            x1, [fp, #-0x40]
    // 0x1361b40: StoreField: r1->field_7 = r0
    //     0x1361b40: stur            w0, [x1, #7]
    // 0x1361b44: StoreField: r1->field_b = r0
    //     0x1361b44: stur            w0, [x1, #0xb]
    // 0x1361b48: StoreField: r1->field_f = r0
    //     0x1361b48: stur            w0, [x1, #0xf]
    // 0x1361b4c: StoreField: r1->field_13 = r0
    //     0x1361b4c: stur            w0, [x1, #0x13]
    // 0x1361b50: r0 = RoundedRectangleBorder()
    //     0x1361b50: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x1361b54: mov             x1, x0
    // 0x1361b58: ldur            x0, [fp, #-0x40]
    // 0x1361b5c: StoreField: r1->field_b = r0
    //     0x1361b5c: stur            w0, [x1, #0xb]
    // 0x1361b60: r0 = Instance_BorderSide
    //     0x1361b60: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x1361b64: ldr             x0, [x0, #0xe20]
    // 0x1361b68: StoreField: r1->field_7 = r0
    //     0x1361b68: stur            w0, [x1, #7]
    // 0x1361b6c: r16 = <RoundedRectangleBorder>
    //     0x1361b6c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1361b70: ldr             x16, [x16, #0xf78]
    // 0x1361b74: stp             x1, x16, [SP]
    // 0x1361b78: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1361b78: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1361b7c: r0 = all()
    //     0x1361b7c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1361b80: stur            x0, [fp, #-0x38]
    // 0x1361b84: r0 = ButtonStyle()
    //     0x1361b84: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1361b88: mov             x1, x0
    // 0x1361b8c: ldur            x0, [fp, #-0x30]
    // 0x1361b90: stur            x1, [fp, #-0x40]
    // 0x1361b94: StoreField: r1->field_b = r0
    //     0x1361b94: stur            w0, [x1, #0xb]
    // 0x1361b98: ldur            x0, [fp, #-0x10]
    // 0x1361b9c: StoreField: r1->field_23 = r0
    //     0x1361b9c: stur            w0, [x1, #0x23]
    // 0x1361ba0: ldur            x0, [fp, #-0x38]
    // 0x1361ba4: StoreField: r1->field_43 = r0
    //     0x1361ba4: stur            w0, [x1, #0x43]
    // 0x1361ba8: r0 = TextButtonThemeData()
    //     0x1361ba8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x1361bac: mov             x2, x0
    // 0x1361bb0: ldur            x0, [fp, #-0x40]
    // 0x1361bb4: stur            x2, [fp, #-0x10]
    // 0x1361bb8: StoreField: r2->field_7 = r0
    //     0x1361bb8: stur            w0, [x2, #7]
    // 0x1361bbc: ldur            x0, [fp, #-8]
    // 0x1361bc0: LoadField: r1 = r0->field_13
    //     0x1361bc0: ldur            w1, [x0, #0x13]
    // 0x1361bc4: DecompressPointer r1
    //     0x1361bc4: add             x1, x1, HEAP, lsl #32
    // 0x1361bc8: r0 = of()
    //     0x1361bc8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1361bcc: LoadField: r1 = r0->field_87
    //     0x1361bcc: ldur            w1, [x0, #0x87]
    // 0x1361bd0: DecompressPointer r1
    //     0x1361bd0: add             x1, x1, HEAP, lsl #32
    // 0x1361bd4: LoadField: r0 = r1->field_7
    //     0x1361bd4: ldur            w0, [x1, #7]
    // 0x1361bd8: DecompressPointer r0
    //     0x1361bd8: add             x0, x0, HEAP, lsl #32
    // 0x1361bdc: r16 = 16.000000
    //     0x1361bdc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1361be0: ldr             x16, [x16, #0x188]
    // 0x1361be4: r30 = Instance_Color
    //     0x1361be4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x1361be8: stp             lr, x16, [SP]
    // 0x1361bec: mov             x1, x0
    // 0x1361bf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1361bf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1361bf4: ldr             x4, [x4, #0xaa0]
    // 0x1361bf8: r0 = copyWith()
    //     0x1361bf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1361bfc: stur            x0, [fp, #-0x30]
    // 0x1361c00: r0 = Text()
    //     0x1361c00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1361c04: mov             x3, x0
    // 0x1361c08: r0 = "Submit"
    //     0x1361c08: add             x0, PP, #0x40, lsl #12  ; [pp+0x40028] "Submit"
    //     0x1361c0c: ldr             x0, [x0, #0x28]
    // 0x1361c10: stur            x3, [fp, #-0x38]
    // 0x1361c14: StoreField: r3->field_b = r0
    //     0x1361c14: stur            w0, [x3, #0xb]
    // 0x1361c18: ldur            x0, [fp, #-0x30]
    // 0x1361c1c: StoreField: r3->field_13 = r0
    //     0x1361c1c: stur            w0, [x3, #0x13]
    // 0x1361c20: ldur            x2, [fp, #-8]
    // 0x1361c24: r1 = Function '<anonymous closure>':.
    //     0x1361c24: add             x1, PP, #0x40, lsl #12  ; [pp+0x40030] AnonymousClosure: (0x131efa4), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::bottomNavigationBar (0x1369680)
    //     0x1361c28: ldr             x1, [x1, #0x30]
    // 0x1361c2c: r0 = AllocateClosure()
    //     0x1361c2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1361c30: stur            x0, [fp, #-8]
    // 0x1361c34: r0 = TextButton()
    //     0x1361c34: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1361c38: mov             x1, x0
    // 0x1361c3c: ldur            x0, [fp, #-8]
    // 0x1361c40: stur            x1, [fp, #-0x30]
    // 0x1361c44: StoreField: r1->field_b = r0
    //     0x1361c44: stur            w0, [x1, #0xb]
    // 0x1361c48: r0 = false
    //     0x1361c48: add             x0, NULL, #0x30  ; false
    // 0x1361c4c: StoreField: r1->field_27 = r0
    //     0x1361c4c: stur            w0, [x1, #0x27]
    // 0x1361c50: r0 = true
    //     0x1361c50: add             x0, NULL, #0x20  ; true
    // 0x1361c54: StoreField: r1->field_2f = r0
    //     0x1361c54: stur            w0, [x1, #0x2f]
    // 0x1361c58: ldur            x0, [fp, #-0x38]
    // 0x1361c5c: StoreField: r1->field_37 = r0
    //     0x1361c5c: stur            w0, [x1, #0x37]
    // 0x1361c60: r0 = Instance_ValueKey
    //     0x1361c60: add             x0, PP, #0x40, lsl #12  ; [pp+0x40038] Obj!ValueKey<String>@d5b321
    //     0x1361c64: ldr             x0, [x0, #0x38]
    // 0x1361c68: StoreField: r1->field_7 = r0
    //     0x1361c68: stur            w0, [x1, #7]
    // 0x1361c6c: r0 = TextButtonTheme()
    //     0x1361c6c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1361c70: mov             x1, x0
    // 0x1361c74: ldur            x0, [fp, #-0x10]
    // 0x1361c78: stur            x1, [fp, #-8]
    // 0x1361c7c: StoreField: r1->field_f = r0
    //     0x1361c7c: stur            w0, [x1, #0xf]
    // 0x1361c80: ldur            x0, [fp, #-0x30]
    // 0x1361c84: StoreField: r1->field_b = r0
    //     0x1361c84: stur            w0, [x1, #0xb]
    // 0x1361c88: r0 = SizedBox()
    //     0x1361c88: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1361c8c: mov             x1, x0
    // 0x1361c90: r0 = inf
    //     0x1361c90: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x1361c94: ldr             x0, [x0, #0x9f8]
    // 0x1361c98: stur            x1, [fp, #-0x10]
    // 0x1361c9c: StoreField: r1->field_f = r0
    //     0x1361c9c: stur            w0, [x1, #0xf]
    // 0x1361ca0: ldur            x0, [fp, #-8]
    // 0x1361ca4: StoreField: r1->field_b = r0
    //     0x1361ca4: stur            w0, [x1, #0xb]
    // 0x1361ca8: r0 = Padding()
    //     0x1361ca8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1361cac: mov             x3, x0
    // 0x1361cb0: r0 = Instance_EdgeInsets
    //     0x1361cb0: add             x0, PP, #0x36, lsl #12  ; [pp+0x366d8] Obj!EdgeInsets@d59a81
    //     0x1361cb4: ldr             x0, [x0, #0x6d8]
    // 0x1361cb8: stur            x3, [fp, #-8]
    // 0x1361cbc: StoreField: r3->field_f = r0
    //     0x1361cbc: stur            w0, [x3, #0xf]
    // 0x1361cc0: ldur            x0, [fp, #-0x10]
    // 0x1361cc4: StoreField: r3->field_b = r0
    //     0x1361cc4: stur            w0, [x3, #0xb]
    // 0x1361cc8: r1 = Null
    //     0x1361cc8: mov             x1, NULL
    // 0x1361ccc: r2 = 4
    //     0x1361ccc: movz            x2, #0x4
    // 0x1361cd0: r0 = AllocateArray()
    //     0x1361cd0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1361cd4: stur            x0, [fp, #-0x10]
    // 0x1361cd8: r16 = Instance_Padding
    //     0x1361cd8: add             x16, PP, #0x36, lsl #12  ; [pp+0x366e0] Obj!Padding@d68561
    //     0x1361cdc: ldr             x16, [x16, #0x6e0]
    // 0x1361ce0: StoreField: r0->field_f = r16
    //     0x1361ce0: stur            w16, [x0, #0xf]
    // 0x1361ce4: ldur            x1, [fp, #-8]
    // 0x1361ce8: StoreField: r0->field_13 = r1
    //     0x1361ce8: stur            w1, [x0, #0x13]
    // 0x1361cec: r1 = <Widget>
    //     0x1361cec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1361cf0: r0 = AllocateGrowableArray()
    //     0x1361cf0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1361cf4: mov             x1, x0
    // 0x1361cf8: ldur            x0, [fp, #-0x10]
    // 0x1361cfc: stur            x1, [fp, #-8]
    // 0x1361d00: StoreField: r1->field_f = r0
    //     0x1361d00: stur            w0, [x1, #0xf]
    // 0x1361d04: r0 = 4
    //     0x1361d04: movz            x0, #0x4
    // 0x1361d08: StoreField: r1->field_b = r0
    //     0x1361d08: stur            w0, [x1, #0xb]
    // 0x1361d0c: r0 = Wrap()
    //     0x1361d0c: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x1361d10: r1 = Instance_Axis
    //     0x1361d10: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1361d14: StoreField: r0->field_f = r1
    //     0x1361d14: stur            w1, [x0, #0xf]
    // 0x1361d18: r1 = Instance_WrapAlignment
    //     0x1361d18: add             x1, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x1361d1c: ldr             x1, [x1, #0x6e8]
    // 0x1361d20: StoreField: r0->field_13 = r1
    //     0x1361d20: stur            w1, [x0, #0x13]
    // 0x1361d24: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1361d24: stur            xzr, [x0, #0x17]
    // 0x1361d28: StoreField: r0->field_1f = r1
    //     0x1361d28: stur            w1, [x0, #0x1f]
    // 0x1361d2c: StoreField: r0->field_23 = rZR
    //     0x1361d2c: stur            xzr, [x0, #0x23]
    // 0x1361d30: r1 = Instance_WrapCrossAlignment
    //     0x1361d30: add             x1, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x1361d34: ldr             x1, [x1, #0x6f0]
    // 0x1361d38: StoreField: r0->field_2b = r1
    //     0x1361d38: stur            w1, [x0, #0x2b]
    // 0x1361d3c: r1 = Instance_VerticalDirection
    //     0x1361d3c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1361d40: ldr             x1, [x1, #0xa20]
    // 0x1361d44: StoreField: r0->field_33 = r1
    //     0x1361d44: stur            w1, [x0, #0x33]
    // 0x1361d48: r1 = Instance_Clip
    //     0x1361d48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1361d4c: ldr             x1, [x1, #0x38]
    // 0x1361d50: StoreField: r0->field_37 = r1
    //     0x1361d50: stur            w1, [x0, #0x37]
    // 0x1361d54: ldur            x1, [fp, #-8]
    // 0x1361d58: StoreField: r0->field_b = r1
    //     0x1361d58: stur            w1, [x0, #0xb]
    // 0x1361d5c: LeaveFrame
    //     0x1361d5c: mov             SP, fp
    //     0x1361d60: ldp             fp, lr, [SP], #0x10
    // 0x1361d64: ret
    //     0x1361d64: ret             
    // 0x1361d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1361d68: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1361d6c: b               #0x13616d8
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0x14086c8, size: 0x54
    // 0x14086c8: EnterFrame
    //     0x14086c8: stp             fp, lr, [SP, #-0x10]!
    //     0x14086cc: mov             fp, SP
    // 0x14086d0: ldr             x0, [fp, #0x18]
    // 0x14086d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14086d4: ldur            w1, [x0, #0x17]
    // 0x14086d8: DecompressPointer r1
    //     0x14086d8: add             x1, x1, HEAP, lsl #32
    // 0x14086dc: CheckStackOverflow
    //     0x14086dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14086e0: cmp             SP, x16
    //     0x14086e4: b.ls            #0x1408714
    // 0x14086e8: LoadField: r0 = r1->field_f
    //     0x14086e8: ldur            w0, [x1, #0xf]
    // 0x14086ec: DecompressPointer r0
    //     0x14086ec: add             x0, x0, HEAP, lsl #32
    // 0x14086f0: mov             x1, x0
    // 0x14086f4: r0 = controller()
    //     0x14086f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14086f8: mov             x1, x0
    // 0x14086fc: ldr             x2, [fp, #0x10]
    // 0x1408700: r0 = validateRemark()
    //     0x1408700: bl              #0x140e6d8  ; [package:customer_app/app/presentation/controllers/orders/rating_review_order_controller.dart] RatingReviewOrderController::validateRemark
    // 0x1408704: r0 = Null
    //     0x1408704: mov             x0, NULL
    // 0x1408708: LeaveFrame
    //     0x1408708: mov             SP, fp
    //     0x140870c: ldp             fp, lr, [SP], #0x10
    // 0x1408710: ret
    //     0x1408710: ret             
    // 0x1408714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1408714: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1408718: b               #0x14086e8
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x140871c, size: 0x1510
    // 0x140871c: EnterFrame
    //     0x140871c: stp             fp, lr, [SP, #-0x10]!
    //     0x1408720: mov             fp, SP
    // 0x1408724: AllocStack(0xd0)
    //     0x1408724: sub             SP, SP, #0xd0
    // 0x1408728: SetupParameters()
    //     0x1408728: ldr             x0, [fp, #0x10]
    //     0x140872c: ldur            w2, [x0, #0x17]
    //     0x1408730: add             x2, x2, HEAP, lsl #32
    //     0x1408734: stur            x2, [fp, #-8]
    // 0x1408738: CheckStackOverflow
    //     0x1408738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140873c: cmp             SP, x16
    //     0x1408740: b.ls            #0x1409c14
    // 0x1408744: r0 = Obx()
    //     0x1408744: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1408748: ldur            x2, [fp, #-8]
    // 0x140874c: r1 = Function '<anonymous closure>':.
    //     0x140874c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40050] AnonymousClosure: (0x140e2e4), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14f0f10)
    //     0x1408750: ldr             x1, [x1, #0x50]
    // 0x1408754: stur            x0, [fp, #-0x10]
    // 0x1408758: r0 = AllocateClosure()
    //     0x1408758: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140875c: mov             x1, x0
    // 0x1408760: ldur            x0, [fp, #-0x10]
    // 0x1408764: StoreField: r0->field_b = r1
    //     0x1408764: stur            w1, [x0, #0xb]
    // 0x1408768: ldur            x2, [fp, #-8]
    // 0x140876c: LoadField: r1 = r2->field_13
    //     0x140876c: ldur            w1, [x2, #0x13]
    // 0x1408770: DecompressPointer r1
    //     0x1408770: add             x1, x1, HEAP, lsl #32
    // 0x1408774: r0 = of()
    //     0x1408774: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1408778: LoadField: r1 = r0->field_5b
    //     0x1408778: ldur            w1, [x0, #0x5b]
    // 0x140877c: DecompressPointer r1
    //     0x140877c: add             x1, x1, HEAP, lsl #32
    // 0x1408780: r0 = LoadClassIdInstr(r1)
    //     0x1408780: ldur            x0, [x1, #-1]
    //     0x1408784: ubfx            x0, x0, #0xc, #0x14
    // 0x1408788: d0 = 0.030000
    //     0x1408788: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x140878c: ldr             d0, [x17, #0x238]
    // 0x1408790: r0 = GDT[cid_x0 + -0xffa]()
    //     0x1408790: sub             lr, x0, #0xffa
    //     0x1408794: ldr             lr, [x21, lr, lsl #3]
    //     0x1408798: blr             lr
    // 0x140879c: stur            x0, [fp, #-0x18]
    // 0x14087a0: r0 = Radius()
    //     0x14087a0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14087a4: d0 = 15.000000
    //     0x14087a4: fmov            d0, #15.00000000
    // 0x14087a8: stur            x0, [fp, #-0x20]
    // 0x14087ac: StoreField: r0->field_7 = d0
    //     0x14087ac: stur            d0, [x0, #7]
    // 0x14087b0: StoreField: r0->field_f = d0
    //     0x14087b0: stur            d0, [x0, #0xf]
    // 0x14087b4: r0 = BorderRadius()
    //     0x14087b4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14087b8: mov             x1, x0
    // 0x14087bc: ldur            x0, [fp, #-0x20]
    // 0x14087c0: stur            x1, [fp, #-0x28]
    // 0x14087c4: StoreField: r1->field_7 = r0
    //     0x14087c4: stur            w0, [x1, #7]
    // 0x14087c8: StoreField: r1->field_b = r0
    //     0x14087c8: stur            w0, [x1, #0xb]
    // 0x14087cc: StoreField: r1->field_f = r0
    //     0x14087cc: stur            w0, [x1, #0xf]
    // 0x14087d0: StoreField: r1->field_13 = r0
    //     0x14087d0: stur            w0, [x1, #0x13]
    // 0x14087d4: r0 = BoxDecoration()
    //     0x14087d4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x14087d8: mov             x1, x0
    // 0x14087dc: ldur            x0, [fp, #-0x18]
    // 0x14087e0: stur            x1, [fp, #-0x20]
    // 0x14087e4: StoreField: r1->field_7 = r0
    //     0x14087e4: stur            w0, [x1, #7]
    // 0x14087e8: ldur            x0, [fp, #-0x28]
    // 0x14087ec: StoreField: r1->field_13 = r0
    //     0x14087ec: stur            w0, [x1, #0x13]
    // 0x14087f0: r0 = Instance_BoxShape
    //     0x14087f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x14087f4: ldr             x0, [x0, #0x80]
    // 0x14087f8: StoreField: r1->field_23 = r0
    //     0x14087f8: stur            w0, [x1, #0x23]
    // 0x14087fc: r0 = Radius()
    //     0x14087fc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1408800: d0 = 15.000000
    //     0x1408800: fmov            d0, #15.00000000
    // 0x1408804: stur            x0, [fp, #-0x18]
    // 0x1408808: StoreField: r0->field_7 = d0
    //     0x1408808: stur            d0, [x0, #7]
    // 0x140880c: StoreField: r0->field_f = d0
    //     0x140880c: stur            d0, [x0, #0xf]
    // 0x1408810: r0 = BorderRadius()
    //     0x1408810: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1408814: mov             x2, x0
    // 0x1408818: ldur            x0, [fp, #-0x18]
    // 0x140881c: stur            x2, [fp, #-0x28]
    // 0x1408820: StoreField: r2->field_7 = r0
    //     0x1408820: stur            w0, [x2, #7]
    // 0x1408824: StoreField: r2->field_b = r0
    //     0x1408824: stur            w0, [x2, #0xb]
    // 0x1408828: StoreField: r2->field_f = r0
    //     0x1408828: stur            w0, [x2, #0xf]
    // 0x140882c: StoreField: r2->field_13 = r0
    //     0x140882c: stur            w0, [x2, #0x13]
    // 0x1408830: ldur            x0, [fp, #-8]
    // 0x1408834: LoadField: r1 = r0->field_f
    //     0x1408834: ldur            w1, [x0, #0xf]
    // 0x1408838: DecompressPointer r1
    //     0x1408838: add             x1, x1, HEAP, lsl #32
    // 0x140883c: r0 = controller()
    //     0x140883c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1408840: LoadField: r1 = r0->field_4b
    //     0x1408840: ldur            w1, [x0, #0x4b]
    // 0x1408844: DecompressPointer r1
    //     0x1408844: add             x1, x1, HEAP, lsl #32
    // 0x1408848: r0 = value()
    //     0x1408848: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140884c: LoadField: r1 = r0->field_b
    //     0x140884c: ldur            w1, [x0, #0xb]
    // 0x1408850: DecompressPointer r1
    //     0x1408850: add             x1, x1, HEAP, lsl #32
    // 0x1408854: cmp             w1, NULL
    // 0x1408858: b.ne            #0x1408864
    // 0x140885c: r0 = Null
    //     0x140885c: mov             x0, NULL
    // 0x1408860: b               #0x140886c
    // 0x1408864: LoadField: r0 = r1->field_f
    //     0x1408864: ldur            w0, [x1, #0xf]
    // 0x1408868: DecompressPointer r0
    //     0x1408868: add             x0, x0, HEAP, lsl #32
    // 0x140886c: cmp             w0, NULL
    // 0x1408870: b.ne            #0x140887c
    // 0x1408874: r4 = ""
    //     0x1408874: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1408878: b               #0x1408880
    // 0x140887c: mov             x4, x0
    // 0x1408880: ldur            x3, [fp, #-8]
    // 0x1408884: ldur            x0, [fp, #-0x28]
    // 0x1408888: stur            x4, [fp, #-0x18]
    // 0x140888c: r1 = Function '<anonymous closure>':.
    //     0x140888c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40058] AnonymousClosure: (0x9d9cb4), in [package:customer_app/app/presentation/views/line/post_order/order_detail/order_detail_view.dart] OrderDetailView::body (0x1506b64)
    //     0x1408890: ldr             x1, [x1, #0x58]
    // 0x1408894: r2 = Null
    //     0x1408894: mov             x2, NULL
    // 0x1408898: r0 = AllocateClosure()
    //     0x1408898: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140889c: stur            x0, [fp, #-0x30]
    // 0x14088a0: r0 = CachedNetworkImage()
    //     0x14088a0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x14088a4: stur            x0, [fp, #-0x38]
    // 0x14088a8: r16 = 70.000000
    //     0x14088a8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33748] 70
    //     0x14088ac: ldr             x16, [x16, #0x748]
    // 0x14088b0: r30 = 70.000000
    //     0x14088b0: add             lr, PP, #0x33, lsl #12  ; [pp+0x33748] 70
    //     0x14088b4: ldr             lr, [lr, #0x748]
    // 0x14088b8: stp             lr, x16, [SP, #0x10]
    // 0x14088bc: ldur            x16, [fp, #-0x30]
    // 0x14088c0: r30 = Instance_BoxFit
    //     0x14088c0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x14088c4: ldr             lr, [lr, #0x118]
    // 0x14088c8: stp             lr, x16, [SP]
    // 0x14088cc: mov             x1, x0
    // 0x14088d0: ldur            x2, [fp, #-0x18]
    // 0x14088d4: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x4, fit, 0x5, height, 0x2, width, 0x3, null]
    //     0x14088d4: add             x4, PP, #0x40, lsl #12  ; [pp+0x40060] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x4, "fit", 0x5, "height", 0x2, "width", 0x3, Null]
    //     0x14088d8: ldr             x4, [x4, #0x60]
    // 0x14088dc: r0 = CachedNetworkImage()
    //     0x14088dc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x14088e0: r0 = ClipRRect()
    //     0x14088e0: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x14088e4: mov             x2, x0
    // 0x14088e8: ldur            x0, [fp, #-0x28]
    // 0x14088ec: stur            x2, [fp, #-0x18]
    // 0x14088f0: StoreField: r2->field_f = r0
    //     0x14088f0: stur            w0, [x2, #0xf]
    // 0x14088f4: r0 = Instance_Clip
    //     0x14088f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x14088f8: ldr             x0, [x0, #0x138]
    // 0x14088fc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14088fc: stur            w0, [x2, #0x17]
    // 0x1408900: ldur            x0, [fp, #-0x38]
    // 0x1408904: StoreField: r2->field_b = r0
    //     0x1408904: stur            w0, [x2, #0xb]
    // 0x1408908: ldur            x0, [fp, #-8]
    // 0x140890c: LoadField: r1 = r0->field_f
    //     0x140890c: ldur            w1, [x0, #0xf]
    // 0x1408910: DecompressPointer r1
    //     0x1408910: add             x1, x1, HEAP, lsl #32
    // 0x1408914: r0 = controller()
    //     0x1408914: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1408918: LoadField: r1 = r0->field_4b
    //     0x1408918: ldur            w1, [x0, #0x4b]
    // 0x140891c: DecompressPointer r1
    //     0x140891c: add             x1, x1, HEAP, lsl #32
    // 0x1408920: r0 = value()
    //     0x1408920: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1408924: LoadField: r1 = r0->field_b
    //     0x1408924: ldur            w1, [x0, #0xb]
    // 0x1408928: DecompressPointer r1
    //     0x1408928: add             x1, x1, HEAP, lsl #32
    // 0x140892c: cmp             w1, NULL
    // 0x1408930: b.ne            #0x140893c
    // 0x1408934: r0 = Null
    //     0x1408934: mov             x0, NULL
    // 0x1408938: b               #0x1408944
    // 0x140893c: LoadField: r0 = r1->field_13
    //     0x140893c: ldur            w0, [x1, #0x13]
    // 0x1408940: DecompressPointer r0
    //     0x1408940: add             x0, x0, HEAP, lsl #32
    // 0x1408944: cmp             w0, NULL
    // 0x1408948: b.ne            #0x1408954
    // 0x140894c: r3 = ""
    //     0x140894c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1408950: b               #0x1408958
    // 0x1408954: mov             x3, x0
    // 0x1408958: ldur            x2, [fp, #-8]
    // 0x140895c: ldur            x0, [fp, #-0x18]
    // 0x1408960: stur            x3, [fp, #-0x28]
    // 0x1408964: LoadField: r1 = r2->field_13
    //     0x1408964: ldur            w1, [x2, #0x13]
    // 0x1408968: DecompressPointer r1
    //     0x1408968: add             x1, x1, HEAP, lsl #32
    // 0x140896c: r0 = of()
    //     0x140896c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1408970: LoadField: r1 = r0->field_87
    //     0x1408970: ldur            w1, [x0, #0x87]
    // 0x1408974: DecompressPointer r1
    //     0x1408974: add             x1, x1, HEAP, lsl #32
    // 0x1408978: LoadField: r0 = r1->field_7
    //     0x1408978: ldur            w0, [x1, #7]
    // 0x140897c: DecompressPointer r0
    //     0x140897c: add             x0, x0, HEAP, lsl #32
    // 0x1408980: r16 = 14.000000
    //     0x1408980: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x1408984: ldr             x16, [x16, #0x1d8]
    // 0x1408988: r30 = Instance_Color
    //     0x1408988: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140898c: stp             lr, x16, [SP]
    // 0x1408990: mov             x1, x0
    // 0x1408994: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1408994: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1408998: ldr             x4, [x4, #0xaa0]
    // 0x140899c: r0 = copyWith()
    //     0x140899c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14089a0: stur            x0, [fp, #-0x30]
    // 0x14089a4: r0 = Text()
    //     0x14089a4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x14089a8: mov             x1, x0
    // 0x14089ac: ldur            x0, [fp, #-0x28]
    // 0x14089b0: stur            x1, [fp, #-0x38]
    // 0x14089b4: StoreField: r1->field_b = r0
    //     0x14089b4: stur            w0, [x1, #0xb]
    // 0x14089b8: ldur            x0, [fp, #-0x30]
    // 0x14089bc: StoreField: r1->field_13 = r0
    //     0x14089bc: stur            w0, [x1, #0x13]
    // 0x14089c0: r0 = Instance_TextOverflow
    //     0x14089c0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x14089c4: ldr             x0, [x0, #0xe10]
    // 0x14089c8: StoreField: r1->field_2b = r0
    //     0x14089c8: stur            w0, [x1, #0x2b]
    // 0x14089cc: r2 = 4
    //     0x14089cc: movz            x2, #0x4
    // 0x14089d0: StoreField: r1->field_37 = r2
    //     0x14089d0: stur            w2, [x1, #0x37]
    // 0x14089d4: r0 = Padding()
    //     0x14089d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14089d8: mov             x2, x0
    // 0x14089dc: r0 = Instance_EdgeInsets
    //     0x14089dc: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0x14089e0: ldr             x0, [x0, #0x778]
    // 0x14089e4: stur            x2, [fp, #-0x28]
    // 0x14089e8: StoreField: r2->field_f = r0
    //     0x14089e8: stur            w0, [x2, #0xf]
    // 0x14089ec: ldur            x0, [fp, #-0x38]
    // 0x14089f0: StoreField: r2->field_b = r0
    //     0x14089f0: stur            w0, [x2, #0xb]
    // 0x14089f4: ldur            x0, [fp, #-8]
    // 0x14089f8: LoadField: r1 = r0->field_f
    //     0x14089f8: ldur            w1, [x0, #0xf]
    // 0x14089fc: DecompressPointer r1
    //     0x14089fc: add             x1, x1, HEAP, lsl #32
    // 0x1408a00: r0 = controller()
    //     0x1408a00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1408a04: LoadField: r1 = r0->field_c3
    //     0x1408a04: ldur            w1, [x0, #0xc3]
    // 0x1408a08: DecompressPointer r1
    //     0x1408a08: add             x1, x1, HEAP, lsl #32
    // 0x1408a0c: r0 = value()
    //     0x1408a0c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1408a10: ldur            x2, [fp, #-8]
    // 0x1408a14: r1 = Function '<anonymous closure>':.
    //     0x1408a14: add             x1, PP, #0x40, lsl #12  ; [pp+0x40068] AnonymousClosure: (0x140de64), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x1408a18: ldr             x1, [x1, #0x68]
    // 0x1408a1c: stur            x0, [fp, #-0x30]
    // 0x1408a20: r0 = AllocateClosure()
    //     0x1408a20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1408a24: stur            x0, [fp, #-0x38]
    // 0x1408a28: r0 = RatingBar()
    //     0x1408a28: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0x1408a2c: mov             x3, x0
    // 0x1408a30: ldur            x0, [fp, #-0x38]
    // 0x1408a34: stur            x3, [fp, #-0x40]
    // 0x1408a38: StoreField: r3->field_b = r0
    //     0x1408a38: stur            w0, [x3, #0xb]
    // 0x1408a3c: r0 = false
    //     0x1408a3c: add             x0, NULL, #0x30  ; false
    // 0x1408a40: StoreField: r3->field_1f = r0
    //     0x1408a40: stur            w0, [x3, #0x1f]
    // 0x1408a44: r4 = Instance_Axis
    //     0x1408a44: ldr             x4, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1408a48: StoreField: r3->field_23 = r4
    //     0x1408a48: stur            w4, [x3, #0x23]
    // 0x1408a4c: r5 = true
    //     0x1408a4c: add             x5, NULL, #0x20  ; true
    // 0x1408a50: StoreField: r3->field_27 = r5
    //     0x1408a50: stur            w5, [x3, #0x27]
    // 0x1408a54: d0 = 2.000000
    //     0x1408a54: fmov            d0, #2.00000000
    // 0x1408a58: StoreField: r3->field_2b = d0
    //     0x1408a58: stur            d0, [x3, #0x2b]
    // 0x1408a5c: StoreField: r3->field_33 = r0
    //     0x1408a5c: stur            w0, [x3, #0x33]
    // 0x1408a60: ldur            x1, [fp, #-0x30]
    // 0x1408a64: LoadField: d0 = r1->field_7
    //     0x1408a64: ldur            d0, [x1, #7]
    // 0x1408a68: StoreField: r3->field_37 = d0
    //     0x1408a68: stur            d0, [x3, #0x37]
    // 0x1408a6c: r1 = 5
    //     0x1408a6c: movz            x1, #0x5
    // 0x1408a70: StoreField: r3->field_3f = r1
    //     0x1408a70: stur            x1, [x3, #0x3f]
    // 0x1408a74: r1 = Instance_EdgeInsets
    //     0x1408a74: add             x1, PP, #0x36, lsl #12  ; [pp+0x36a68] Obj!EdgeInsets@d572f1
    //     0x1408a78: ldr             x1, [x1, #0xa68]
    // 0x1408a7c: StoreField: r3->field_47 = r1
    //     0x1408a7c: stur            w1, [x3, #0x47]
    // 0x1408a80: d0 = 30.000000
    //     0x1408a80: fmov            d0, #30.00000000
    // 0x1408a84: StoreField: r3->field_4b = d0
    //     0x1408a84: stur            d0, [x3, #0x4b]
    // 0x1408a88: d0 = 1.000000
    //     0x1408a88: fmov            d0, #1.00000000
    // 0x1408a8c: StoreField: r3->field_53 = d0
    //     0x1408a8c: stur            d0, [x3, #0x53]
    // 0x1408a90: StoreField: r3->field_5b = r0
    //     0x1408a90: stur            w0, [x3, #0x5b]
    // 0x1408a94: StoreField: r3->field_5f = r0
    //     0x1408a94: stur            w0, [x3, #0x5f]
    // 0x1408a98: ldur            x2, [fp, #-8]
    // 0x1408a9c: r1 = Function '<anonymous closure>':.
    //     0x1408a9c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40070] AnonymousClosure: (0x140dcc0), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x1408aa0: ldr             x1, [x1, #0x70]
    // 0x1408aa4: r0 = AllocateClosure()
    //     0x1408aa4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1408aa8: mov             x1, x0
    // 0x1408aac: ldur            x0, [fp, #-0x40]
    // 0x1408ab0: StoreField: r0->field_63 = r1
    //     0x1408ab0: stur            w1, [x0, #0x63]
    // 0x1408ab4: r1 = Null
    //     0x1408ab4: mov             x1, NULL
    // 0x1408ab8: r2 = 4
    //     0x1408ab8: movz            x2, #0x4
    // 0x1408abc: r0 = AllocateArray()
    //     0x1408abc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1408ac0: mov             x2, x0
    // 0x1408ac4: ldur            x0, [fp, #-0x28]
    // 0x1408ac8: stur            x2, [fp, #-0x30]
    // 0x1408acc: StoreField: r2->field_f = r0
    //     0x1408acc: stur            w0, [x2, #0xf]
    // 0x1408ad0: ldur            x0, [fp, #-0x40]
    // 0x1408ad4: StoreField: r2->field_13 = r0
    //     0x1408ad4: stur            w0, [x2, #0x13]
    // 0x1408ad8: r1 = <Widget>
    //     0x1408ad8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1408adc: r0 = AllocateGrowableArray()
    //     0x1408adc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1408ae0: mov             x1, x0
    // 0x1408ae4: ldur            x0, [fp, #-0x30]
    // 0x1408ae8: stur            x1, [fp, #-0x28]
    // 0x1408aec: StoreField: r1->field_f = r0
    //     0x1408aec: stur            w0, [x1, #0xf]
    // 0x1408af0: r2 = 4
    //     0x1408af0: movz            x2, #0x4
    // 0x1408af4: StoreField: r1->field_b = r2
    //     0x1408af4: stur            w2, [x1, #0xb]
    // 0x1408af8: r0 = Column()
    //     0x1408af8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1408afc: mov             x1, x0
    // 0x1408b00: r0 = Instance_Axis
    //     0x1408b00: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1408b04: stur            x1, [fp, #-0x30]
    // 0x1408b08: StoreField: r1->field_f = r0
    //     0x1408b08: stur            w0, [x1, #0xf]
    // 0x1408b0c: r2 = Instance_MainAxisAlignment
    //     0x1408b0c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1408b10: ldr             x2, [x2, #0xa08]
    // 0x1408b14: StoreField: r1->field_13 = r2
    //     0x1408b14: stur            w2, [x1, #0x13]
    // 0x1408b18: r3 = Instance_MainAxisSize
    //     0x1408b18: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1408b1c: ldr             x3, [x3, #0xa10]
    // 0x1408b20: ArrayStore: r1[0] = r3  ; List_4
    //     0x1408b20: stur            w3, [x1, #0x17]
    // 0x1408b24: r4 = Instance_CrossAxisAlignment
    //     0x1408b24: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1408b28: ldr             x4, [x4, #0x890]
    // 0x1408b2c: StoreField: r1->field_1b = r4
    //     0x1408b2c: stur            w4, [x1, #0x1b]
    // 0x1408b30: r5 = Instance_VerticalDirection
    //     0x1408b30: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1408b34: ldr             x5, [x5, #0xa20]
    // 0x1408b38: StoreField: r1->field_23 = r5
    //     0x1408b38: stur            w5, [x1, #0x23]
    // 0x1408b3c: r6 = Instance_Clip
    //     0x1408b3c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1408b40: ldr             x6, [x6, #0x38]
    // 0x1408b44: StoreField: r1->field_2b = r6
    //     0x1408b44: stur            w6, [x1, #0x2b]
    // 0x1408b48: StoreField: r1->field_2f = rZR
    //     0x1408b48: stur            xzr, [x1, #0x2f]
    // 0x1408b4c: ldur            x7, [fp, #-0x28]
    // 0x1408b50: StoreField: r1->field_b = r7
    //     0x1408b50: stur            w7, [x1, #0xb]
    // 0x1408b54: r0 = Padding()
    //     0x1408b54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1408b58: mov             x2, x0
    // 0x1408b5c: r0 = Instance_EdgeInsets
    //     0x1408b5c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0x1408b60: ldr             x0, [x0, #0xa78]
    // 0x1408b64: stur            x2, [fp, #-0x28]
    // 0x1408b68: StoreField: r2->field_f = r0
    //     0x1408b68: stur            w0, [x2, #0xf]
    // 0x1408b6c: ldur            x0, [fp, #-0x30]
    // 0x1408b70: StoreField: r2->field_b = r0
    //     0x1408b70: stur            w0, [x2, #0xb]
    // 0x1408b74: r1 = <FlexParentData>
    //     0x1408b74: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1408b78: ldr             x1, [x1, #0xe00]
    // 0x1408b7c: r0 = Expanded()
    //     0x1408b7c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1408b80: mov             x3, x0
    // 0x1408b84: r0 = 1
    //     0x1408b84: movz            x0, #0x1
    // 0x1408b88: stur            x3, [fp, #-0x30]
    // 0x1408b8c: StoreField: r3->field_13 = r0
    //     0x1408b8c: stur            x0, [x3, #0x13]
    // 0x1408b90: r4 = Instance_FlexFit
    //     0x1408b90: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1408b94: ldr             x4, [x4, #0xe08]
    // 0x1408b98: StoreField: r3->field_1b = r4
    //     0x1408b98: stur            w4, [x3, #0x1b]
    // 0x1408b9c: ldur            x1, [fp, #-0x28]
    // 0x1408ba0: StoreField: r3->field_b = r1
    //     0x1408ba0: stur            w1, [x3, #0xb]
    // 0x1408ba4: r1 = Null
    //     0x1408ba4: mov             x1, NULL
    // 0x1408ba8: r2 = 4
    //     0x1408ba8: movz            x2, #0x4
    // 0x1408bac: r0 = AllocateArray()
    //     0x1408bac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1408bb0: mov             x2, x0
    // 0x1408bb4: ldur            x0, [fp, #-0x18]
    // 0x1408bb8: stur            x2, [fp, #-0x28]
    // 0x1408bbc: StoreField: r2->field_f = r0
    //     0x1408bbc: stur            w0, [x2, #0xf]
    // 0x1408bc0: ldur            x0, [fp, #-0x30]
    // 0x1408bc4: StoreField: r2->field_13 = r0
    //     0x1408bc4: stur            w0, [x2, #0x13]
    // 0x1408bc8: r1 = <Widget>
    //     0x1408bc8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1408bcc: r0 = AllocateGrowableArray()
    //     0x1408bcc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1408bd0: mov             x1, x0
    // 0x1408bd4: ldur            x0, [fp, #-0x28]
    // 0x1408bd8: stur            x1, [fp, #-0x18]
    // 0x1408bdc: StoreField: r1->field_f = r0
    //     0x1408bdc: stur            w0, [x1, #0xf]
    // 0x1408be0: r2 = 4
    //     0x1408be0: movz            x2, #0x4
    // 0x1408be4: StoreField: r1->field_b = r2
    //     0x1408be4: stur            w2, [x1, #0xb]
    // 0x1408be8: r0 = Row()
    //     0x1408be8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1408bec: mov             x1, x0
    // 0x1408bf0: r0 = Instance_Axis
    //     0x1408bf0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1408bf4: stur            x1, [fp, #-0x28]
    // 0x1408bf8: StoreField: r1->field_f = r0
    //     0x1408bf8: stur            w0, [x1, #0xf]
    // 0x1408bfc: r2 = Instance_MainAxisAlignment
    //     0x1408bfc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1408c00: ldr             x2, [x2, #0xa08]
    // 0x1408c04: StoreField: r1->field_13 = r2
    //     0x1408c04: stur            w2, [x1, #0x13]
    // 0x1408c08: r3 = Instance_MainAxisSize
    //     0x1408c08: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1408c0c: ldr             x3, [x3, #0xa10]
    // 0x1408c10: ArrayStore: r1[0] = r3  ; List_4
    //     0x1408c10: stur            w3, [x1, #0x17]
    // 0x1408c14: r4 = Instance_CrossAxisAlignment
    //     0x1408c14: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1408c18: ldr             x4, [x4, #0x890]
    // 0x1408c1c: StoreField: r1->field_1b = r4
    //     0x1408c1c: stur            w4, [x1, #0x1b]
    // 0x1408c20: r5 = Instance_VerticalDirection
    //     0x1408c20: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1408c24: ldr             x5, [x5, #0xa20]
    // 0x1408c28: StoreField: r1->field_23 = r5
    //     0x1408c28: stur            w5, [x1, #0x23]
    // 0x1408c2c: r6 = Instance_Clip
    //     0x1408c2c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1408c30: ldr             x6, [x6, #0x38]
    // 0x1408c34: StoreField: r1->field_2b = r6
    //     0x1408c34: stur            w6, [x1, #0x2b]
    // 0x1408c38: StoreField: r1->field_2f = rZR
    //     0x1408c38: stur            xzr, [x1, #0x2f]
    // 0x1408c3c: ldur            x7, [fp, #-0x18]
    // 0x1408c40: StoreField: r1->field_b = r7
    //     0x1408c40: stur            w7, [x1, #0xb]
    // 0x1408c44: r0 = Padding()
    //     0x1408c44: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1408c48: mov             x1, x0
    // 0x1408c4c: r0 = Instance_EdgeInsets
    //     0x1408c4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1408c50: ldr             x0, [x0, #0x1f0]
    // 0x1408c54: stur            x1, [fp, #-0x18]
    // 0x1408c58: StoreField: r1->field_f = r0
    //     0x1408c58: stur            w0, [x1, #0xf]
    // 0x1408c5c: ldur            x0, [fp, #-0x28]
    // 0x1408c60: StoreField: r1->field_b = r0
    //     0x1408c60: stur            w0, [x1, #0xb]
    // 0x1408c64: r0 = Container()
    //     0x1408c64: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1408c68: stur            x0, [fp, #-0x28]
    // 0x1408c6c: ldur            x16, [fp, #-0x20]
    // 0x1408c70: ldur            lr, [fp, #-0x18]
    // 0x1408c74: stp             lr, x16, [SP]
    // 0x1408c78: mov             x1, x0
    // 0x1408c7c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x1408c7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x1408c80: ldr             x4, [x4, #0x88]
    // 0x1408c84: r0 = Container()
    //     0x1408c84: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1408c88: r0 = Padding()
    //     0x1408c88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1408c8c: mov             x3, x0
    // 0x1408c90: r0 = Instance_EdgeInsets
    //     0x1408c90: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f30] Obj!EdgeInsets@d57b31
    //     0x1408c94: ldr             x0, [x0, #0xf30]
    // 0x1408c98: stur            x3, [fp, #-0x18]
    // 0x1408c9c: StoreField: r3->field_f = r0
    //     0x1408c9c: stur            w0, [x3, #0xf]
    // 0x1408ca0: ldur            x0, [fp, #-0x28]
    // 0x1408ca4: StoreField: r3->field_b = r0
    //     0x1408ca4: stur            w0, [x3, #0xb]
    // 0x1408ca8: r1 = Null
    //     0x1408ca8: mov             x1, NULL
    // 0x1408cac: r2 = 4
    //     0x1408cac: movz            x2, #0x4
    // 0x1408cb0: r0 = AllocateArray()
    //     0x1408cb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1408cb4: stur            x0, [fp, #-0x20]
    // 0x1408cb8: r16 = "Purchased on: "
    //     0x1408cb8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36a80] "Purchased on: "
    //     0x1408cbc: ldr             x16, [x16, #0xa80]
    // 0x1408cc0: StoreField: r0->field_f = r16
    //     0x1408cc0: stur            w16, [x0, #0xf]
    // 0x1408cc4: ldur            x2, [fp, #-8]
    // 0x1408cc8: LoadField: r1 = r2->field_f
    //     0x1408cc8: ldur            w1, [x2, #0xf]
    // 0x1408ccc: DecompressPointer r1
    //     0x1408ccc: add             x1, x1, HEAP, lsl #32
    // 0x1408cd0: r0 = controller()
    //     0x1408cd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1408cd4: LoadField: r1 = r0->field_4b
    //     0x1408cd4: ldur            w1, [x0, #0x4b]
    // 0x1408cd8: DecompressPointer r1
    //     0x1408cd8: add             x1, x1, HEAP, lsl #32
    // 0x1408cdc: r0 = value()
    //     0x1408cdc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1408ce0: LoadField: r1 = r0->field_b
    //     0x1408ce0: ldur            w1, [x0, #0xb]
    // 0x1408ce4: DecompressPointer r1
    //     0x1408ce4: add             x1, x1, HEAP, lsl #32
    // 0x1408ce8: cmp             w1, NULL
    // 0x1408cec: b.ne            #0x1408cf8
    // 0x1408cf0: r0 = Null
    //     0x1408cf0: mov             x0, NULL
    // 0x1408cf4: b               #0x1408d00
    // 0x1408cf8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1408cf8: ldur            w0, [x1, #0x17]
    // 0x1408cfc: DecompressPointer r0
    //     0x1408cfc: add             x0, x0, HEAP, lsl #32
    // 0x1408d00: cmp             w0, NULL
    // 0x1408d04: b.ne            #0x1408d0c
    // 0x1408d08: r0 = ""
    //     0x1408d08: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1408d0c: ldur            x2, [fp, #-8]
    // 0x1408d10: ldur            x1, [fp, #-0x20]
    // 0x1408d14: ArrayStore: r1[1] = r0  ; List_4
    //     0x1408d14: add             x25, x1, #0x13
    //     0x1408d18: str             w0, [x25]
    //     0x1408d1c: tbz             w0, #0, #0x1408d38
    //     0x1408d20: ldurb           w16, [x1, #-1]
    //     0x1408d24: ldurb           w17, [x0, #-1]
    //     0x1408d28: and             x16, x17, x16, lsr #2
    //     0x1408d2c: tst             x16, HEAP, lsr #32
    //     0x1408d30: b.eq            #0x1408d38
    //     0x1408d34: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1408d38: ldur            x16, [fp, #-0x20]
    // 0x1408d3c: str             x16, [SP]
    // 0x1408d40: r0 = _interpolate()
    //     0x1408d40: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1408d44: ldur            x2, [fp, #-8]
    // 0x1408d48: stur            x0, [fp, #-0x20]
    // 0x1408d4c: LoadField: r1 = r2->field_13
    //     0x1408d4c: ldur            w1, [x2, #0x13]
    // 0x1408d50: DecompressPointer r1
    //     0x1408d50: add             x1, x1, HEAP, lsl #32
    // 0x1408d54: r0 = of()
    //     0x1408d54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1408d58: LoadField: r1 = r0->field_87
    //     0x1408d58: ldur            w1, [x0, #0x87]
    // 0x1408d5c: DecompressPointer r1
    //     0x1408d5c: add             x1, x1, HEAP, lsl #32
    // 0x1408d60: LoadField: r0 = r1->field_2b
    //     0x1408d60: ldur            w0, [x1, #0x2b]
    // 0x1408d64: DecompressPointer r0
    //     0x1408d64: add             x0, x0, HEAP, lsl #32
    // 0x1408d68: stur            x0, [fp, #-0x28]
    // 0x1408d6c: r1 = Instance_Color
    //     0x1408d6c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1408d70: d0 = 0.400000
    //     0x1408d70: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1408d74: r0 = withOpacity()
    //     0x1408d74: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1408d78: r16 = 12.000000
    //     0x1408d78: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1408d7c: ldr             x16, [x16, #0x9e8]
    // 0x1408d80: stp             x0, x16, [SP]
    // 0x1408d84: ldur            x1, [fp, #-0x28]
    // 0x1408d88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1408d88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1408d8c: ldr             x4, [x4, #0xaa0]
    // 0x1408d90: r0 = copyWith()
    //     0x1408d90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1408d94: stur            x0, [fp, #-0x28]
    // 0x1408d98: r0 = Text()
    //     0x1408d98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1408d9c: mov             x1, x0
    // 0x1408da0: ldur            x0, [fp, #-0x20]
    // 0x1408da4: stur            x1, [fp, #-0x30]
    // 0x1408da8: StoreField: r1->field_b = r0
    //     0x1408da8: stur            w0, [x1, #0xb]
    // 0x1408dac: ldur            x0, [fp, #-0x28]
    // 0x1408db0: StoreField: r1->field_13 = r0
    //     0x1408db0: stur            w0, [x1, #0x13]
    // 0x1408db4: r0 = Padding()
    //     0x1408db4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1408db8: mov             x2, x0
    // 0x1408dbc: r0 = Instance_EdgeInsets
    //     0x1408dbc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x1408dc0: ldr             x0, [x0, #0x668]
    // 0x1408dc4: stur            x2, [fp, #-0x20]
    // 0x1408dc8: StoreField: r2->field_f = r0
    //     0x1408dc8: stur            w0, [x2, #0xf]
    // 0x1408dcc: ldur            x1, [fp, #-0x30]
    // 0x1408dd0: StoreField: r2->field_b = r1
    //     0x1408dd0: stur            w1, [x2, #0xb]
    // 0x1408dd4: ldur            x3, [fp, #-8]
    // 0x1408dd8: LoadField: r1 = r3->field_13
    //     0x1408dd8: ldur            w1, [x3, #0x13]
    // 0x1408ddc: DecompressPointer r1
    //     0x1408ddc: add             x1, x1, HEAP, lsl #32
    // 0x1408de0: r0 = of()
    //     0x1408de0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1408de4: LoadField: r1 = r0->field_87
    //     0x1408de4: ldur            w1, [x0, #0x87]
    // 0x1408de8: DecompressPointer r1
    //     0x1408de8: add             x1, x1, HEAP, lsl #32
    // 0x1408dec: LoadField: r0 = r1->field_7
    //     0x1408dec: ldur            w0, [x1, #7]
    // 0x1408df0: DecompressPointer r0
    //     0x1408df0: add             x0, x0, HEAP, lsl #32
    // 0x1408df4: r16 = 16.000000
    //     0x1408df4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1408df8: ldr             x16, [x16, #0x188]
    // 0x1408dfc: r30 = Instance_Color
    //     0x1408dfc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1408e00: stp             lr, x16, [SP]
    // 0x1408e04: mov             x1, x0
    // 0x1408e08: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1408e08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1408e0c: ldr             x4, [x4, #0xaa0]
    // 0x1408e10: r0 = copyWith()
    //     0x1408e10: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1408e14: stur            x0, [fp, #-0x28]
    // 0x1408e18: r0 = Text()
    //     0x1408e18: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1408e1c: mov             x2, x0
    // 0x1408e20: r0 = "Share photos & video :"
    //     0x1408e20: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a88] "Share photos & video :"
    //     0x1408e24: ldr             x0, [x0, #0xa88]
    // 0x1408e28: stur            x2, [fp, #-0x30]
    // 0x1408e2c: StoreField: r2->field_b = r0
    //     0x1408e2c: stur            w0, [x2, #0xb]
    // 0x1408e30: ldur            x0, [fp, #-0x28]
    // 0x1408e34: StoreField: r2->field_13 = r0
    //     0x1408e34: stur            w0, [x2, #0x13]
    // 0x1408e38: ldur            x0, [fp, #-8]
    // 0x1408e3c: LoadField: r1 = r0->field_13
    //     0x1408e3c: ldur            w1, [x0, #0x13]
    // 0x1408e40: DecompressPointer r1
    //     0x1408e40: add             x1, x1, HEAP, lsl #32
    // 0x1408e44: r0 = of()
    //     0x1408e44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1408e48: LoadField: r1 = r0->field_87
    //     0x1408e48: ldur            w1, [x0, #0x87]
    // 0x1408e4c: DecompressPointer r1
    //     0x1408e4c: add             x1, x1, HEAP, lsl #32
    // 0x1408e50: LoadField: r0 = r1->field_2b
    //     0x1408e50: ldur            w0, [x1, #0x2b]
    // 0x1408e54: DecompressPointer r0
    //     0x1408e54: add             x0, x0, HEAP, lsl #32
    // 0x1408e58: stur            x0, [fp, #-0x28]
    // 0x1408e5c: r1 = Instance_Color
    //     0x1408e5c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1408e60: d0 = 0.500000
    //     0x1408e60: fmov            d0, #0.50000000
    // 0x1408e64: r0 = withOpacity()
    //     0x1408e64: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1408e68: r16 = 12.000000
    //     0x1408e68: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1408e6c: ldr             x16, [x16, #0x9e8]
    // 0x1408e70: stp             x0, x16, [SP]
    // 0x1408e74: ldur            x1, [fp, #-0x28]
    // 0x1408e78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1408e78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1408e7c: ldr             x4, [x4, #0xaa0]
    // 0x1408e80: r0 = copyWith()
    //     0x1408e80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1408e84: stur            x0, [fp, #-0x28]
    // 0x1408e88: r0 = Text()
    //     0x1408e88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1408e8c: mov             x1, x0
    // 0x1408e90: r0 = "Capture and add your product experience like Unboxing, Product Usage, etc. (max 100 MB)"
    //     0x1408e90: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a90] "Capture and add your product experience like Unboxing, Product Usage, etc. (max 100 MB)"
    //     0x1408e94: ldr             x0, [x0, #0xa90]
    // 0x1408e98: stur            x1, [fp, #-0x38]
    // 0x1408e9c: StoreField: r1->field_b = r0
    //     0x1408e9c: stur            w0, [x1, #0xb]
    // 0x1408ea0: ldur            x0, [fp, #-0x28]
    // 0x1408ea4: StoreField: r1->field_13 = r0
    //     0x1408ea4: stur            w0, [x1, #0x13]
    // 0x1408ea8: r0 = Padding()
    //     0x1408ea8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1408eac: mov             x3, x0
    // 0x1408eb0: r0 = Instance_EdgeInsets
    //     0x1408eb0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x1408eb4: ldr             x0, [x0, #0x770]
    // 0x1408eb8: stur            x3, [fp, #-0x28]
    // 0x1408ebc: StoreField: r3->field_f = r0
    //     0x1408ebc: stur            w0, [x3, #0xf]
    // 0x1408ec0: ldur            x0, [fp, #-0x38]
    // 0x1408ec4: StoreField: r3->field_b = r0
    //     0x1408ec4: stur            w0, [x3, #0xb]
    // 0x1408ec8: r1 = Null
    //     0x1408ec8: mov             x1, NULL
    // 0x1408ecc: r2 = 4
    //     0x1408ecc: movz            x2, #0x4
    // 0x1408ed0: r0 = AllocateArray()
    //     0x1408ed0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1408ed4: mov             x2, x0
    // 0x1408ed8: ldur            x0, [fp, #-0x30]
    // 0x1408edc: stur            x2, [fp, #-0x38]
    // 0x1408ee0: StoreField: r2->field_f = r0
    //     0x1408ee0: stur            w0, [x2, #0xf]
    // 0x1408ee4: ldur            x0, [fp, #-0x28]
    // 0x1408ee8: StoreField: r2->field_13 = r0
    //     0x1408ee8: stur            w0, [x2, #0x13]
    // 0x1408eec: r1 = <Widget>
    //     0x1408eec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1408ef0: r0 = AllocateGrowableArray()
    //     0x1408ef0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1408ef4: mov             x1, x0
    // 0x1408ef8: ldur            x0, [fp, #-0x38]
    // 0x1408efc: stur            x1, [fp, #-0x28]
    // 0x1408f00: StoreField: r1->field_f = r0
    //     0x1408f00: stur            w0, [x1, #0xf]
    // 0x1408f04: r2 = 4
    //     0x1408f04: movz            x2, #0x4
    // 0x1408f08: StoreField: r1->field_b = r2
    //     0x1408f08: stur            w2, [x1, #0xb]
    // 0x1408f0c: r0 = Column()
    //     0x1408f0c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1408f10: mov             x1, x0
    // 0x1408f14: r0 = Instance_Axis
    //     0x1408f14: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1408f18: stur            x1, [fp, #-0x30]
    // 0x1408f1c: StoreField: r1->field_f = r0
    //     0x1408f1c: stur            w0, [x1, #0xf]
    // 0x1408f20: r2 = Instance_MainAxisAlignment
    //     0x1408f20: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1408f24: ldr             x2, [x2, #0xa08]
    // 0x1408f28: StoreField: r1->field_13 = r2
    //     0x1408f28: stur            w2, [x1, #0x13]
    // 0x1408f2c: r3 = Instance_MainAxisSize
    //     0x1408f2c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1408f30: ldr             x3, [x3, #0xa10]
    // 0x1408f34: ArrayStore: r1[0] = r3  ; List_4
    //     0x1408f34: stur            w3, [x1, #0x17]
    // 0x1408f38: r4 = Instance_CrossAxisAlignment
    //     0x1408f38: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1408f3c: ldr             x4, [x4, #0x890]
    // 0x1408f40: StoreField: r1->field_1b = r4
    //     0x1408f40: stur            w4, [x1, #0x1b]
    // 0x1408f44: r5 = Instance_VerticalDirection
    //     0x1408f44: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1408f48: ldr             x5, [x5, #0xa20]
    // 0x1408f4c: StoreField: r1->field_23 = r5
    //     0x1408f4c: stur            w5, [x1, #0x23]
    // 0x1408f50: r6 = Instance_Clip
    //     0x1408f50: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1408f54: ldr             x6, [x6, #0x38]
    // 0x1408f58: StoreField: r1->field_2b = r6
    //     0x1408f58: stur            w6, [x1, #0x2b]
    // 0x1408f5c: StoreField: r1->field_2f = rZR
    //     0x1408f5c: stur            xzr, [x1, #0x2f]
    // 0x1408f60: ldur            x7, [fp, #-0x28]
    // 0x1408f64: StoreField: r1->field_b = r7
    //     0x1408f64: stur            w7, [x1, #0xb]
    // 0x1408f68: r0 = Padding()
    //     0x1408f68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1408f6c: mov             x2, x0
    // 0x1408f70: r0 = Instance_EdgeInsets
    //     0x1408f70: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0x1408f74: ldr             x0, [x0, #0xa98]
    // 0x1408f78: stur            x2, [fp, #-0x28]
    // 0x1408f7c: StoreField: r2->field_f = r0
    //     0x1408f7c: stur            w0, [x2, #0xf]
    // 0x1408f80: ldur            x1, [fp, #-0x30]
    // 0x1408f84: StoreField: r2->field_b = r1
    //     0x1408f84: stur            w1, [x2, #0xb]
    // 0x1408f88: ldur            x3, [fp, #-8]
    // 0x1408f8c: LoadField: r1 = r3->field_f
    //     0x1408f8c: ldur            w1, [x3, #0xf]
    // 0x1408f90: DecompressPointer r1
    //     0x1408f90: add             x1, x1, HEAP, lsl #32
    // 0x1408f94: r0 = controller()
    //     0x1408f94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1408f98: LoadField: r1 = r0->field_97
    //     0x1408f98: ldur            w1, [x0, #0x97]
    // 0x1408f9c: DecompressPointer r1
    //     0x1408f9c: add             x1, x1, HEAP, lsl #32
    // 0x1408fa0: r0 = value()
    //     0x1408fa0: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1408fa4: r1 = LoadClassIdInstr(r0)
    //     0x1408fa4: ldur            x1, [x0, #-1]
    //     0x1408fa8: ubfx            x1, x1, #0xc, #0x14
    // 0x1408fac: str             x0, [SP]
    // 0x1408fb0: mov             x0, x1
    // 0x1408fb4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1408fb4: movz            x17, #0xc898
    //     0x1408fb8: add             lr, x0, x17
    //     0x1408fbc: ldr             lr, [x21, lr, lsl #3]
    //     0x1408fc0: blr             lr
    // 0x1408fc4: cbnz            w0, #0x140926c
    // 0x1408fc8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1408fc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1408fcc: ldr             x0, [x0, #0x1c80]
    //     0x1408fd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1408fd4: cmp             w0, w16
    //     0x1408fd8: b.ne            #0x1408fe4
    //     0x1408fdc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1408fe0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1408fe4: r0 = GetNavigation.size()
    //     0x1408fe4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x1408fe8: LoadField: d1 = r0->field_7
    //     0x1408fe8: ldur            d1, [x0, #7]
    // 0x1408fec: stur            d1, [fp, #-0x80]
    // 0x1408ff0: r1 = Instance_Color
    //     0x1408ff0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1408ff4: d0 = 0.050000
    //     0x1408ff4: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x1408ff8: r0 = withOpacity()
    //     0x1408ff8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1408ffc: stur            x0, [fp, #-0x30]
    // 0x1409000: r0 = Radius()
    //     0x1409000: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1409004: d0 = 15.000000
    //     0x1409004: fmov            d0, #15.00000000
    // 0x1409008: stur            x0, [fp, #-0x38]
    // 0x140900c: StoreField: r0->field_7 = d0
    //     0x140900c: stur            d0, [x0, #7]
    // 0x1409010: StoreField: r0->field_f = d0
    //     0x1409010: stur            d0, [x0, #0xf]
    // 0x1409014: r0 = BorderRadius()
    //     0x1409014: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1409018: mov             x1, x0
    // 0x140901c: ldur            x0, [fp, #-0x38]
    // 0x1409020: stur            x1, [fp, #-0x40]
    // 0x1409024: StoreField: r1->field_7 = r0
    //     0x1409024: stur            w0, [x1, #7]
    // 0x1409028: StoreField: r1->field_b = r0
    //     0x1409028: stur            w0, [x1, #0xb]
    // 0x140902c: StoreField: r1->field_f = r0
    //     0x140902c: stur            w0, [x1, #0xf]
    // 0x1409030: StoreField: r1->field_13 = r0
    //     0x1409030: stur            w0, [x1, #0x13]
    // 0x1409034: r0 = BoxDecoration()
    //     0x1409034: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1409038: mov             x2, x0
    // 0x140903c: ldur            x0, [fp, #-0x30]
    // 0x1409040: stur            x2, [fp, #-0x38]
    // 0x1409044: StoreField: r2->field_7 = r0
    //     0x1409044: stur            w0, [x2, #7]
    // 0x1409048: ldur            x0, [fp, #-0x40]
    // 0x140904c: StoreField: r2->field_13 = r0
    //     0x140904c: stur            w0, [x2, #0x13]
    // 0x1409050: r0 = Instance_BoxShape
    //     0x1409050: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1409054: ldr             x0, [x0, #0x80]
    // 0x1409058: StoreField: r2->field_23 = r0
    //     0x1409058: stur            w0, [x2, #0x23]
    // 0x140905c: r1 = Instance_MaterialColor
    //     0x140905c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x1409060: ldr             x1, [x1, #0xdc0]
    // 0x1409064: d0 = 0.500000
    //     0x1409064: fmov            d0, #0.50000000
    // 0x1409068: r0 = withOpacity()
    //     0x1409068: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140906c: r1 = Instance_Color
    //     0x140906c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1409070: d0 = 0.200000
    //     0x1409070: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0x1409074: stur            x0, [fp, #-0x30]
    // 0x1409078: r0 = withOpacity()
    //     0x1409078: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140907c: stur            x0, [fp, #-0x40]
    // 0x1409080: r0 = Icon()
    //     0x1409080: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x1409084: mov             x3, x0
    // 0x1409088: r0 = Instance_IconData
    //     0x1409088: add             x0, PP, #0x36, lsl #12  ; [pp+0x36aa0] Obj!IconData@d555c1
    //     0x140908c: ldr             x0, [x0, #0xaa0]
    // 0x1409090: stur            x3, [fp, #-0x48]
    // 0x1409094: StoreField: r3->field_b = r0
    //     0x1409094: stur            w0, [x3, #0xb]
    // 0x1409098: r0 = 25.000000
    //     0x1409098: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x140909c: ldr             x0, [x0, #0x98]
    // 0x14090a0: StoreField: r3->field_f = r0
    //     0x14090a0: stur            w0, [x3, #0xf]
    // 0x14090a4: ldur            x0, [fp, #-0x40]
    // 0x14090a8: StoreField: r3->field_23 = r0
    //     0x14090a8: stur            w0, [x3, #0x23]
    // 0x14090ac: r1 = Null
    //     0x14090ac: mov             x1, NULL
    // 0x14090b0: r2 = 4
    //     0x14090b0: movz            x2, #0x4
    // 0x14090b4: r0 = AllocateArray()
    //     0x14090b4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14090b8: mov             x2, x0
    // 0x14090bc: ldur            x0, [fp, #-0x48]
    // 0x14090c0: stur            x2, [fp, #-0x40]
    // 0x14090c4: StoreField: r2->field_f = r0
    //     0x14090c4: stur            w0, [x2, #0xf]
    // 0x14090c8: r16 = Instance_Icon
    //     0x14090c8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36aa8] Obj!Icon@d66331
    //     0x14090cc: ldr             x16, [x16, #0xaa8]
    // 0x14090d0: StoreField: r2->field_13 = r16
    //     0x14090d0: stur            w16, [x2, #0x13]
    // 0x14090d4: r1 = <Widget>
    //     0x14090d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14090d8: r0 = AllocateGrowableArray()
    //     0x14090d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14090dc: mov             x1, x0
    // 0x14090e0: ldur            x0, [fp, #-0x40]
    // 0x14090e4: stur            x1, [fp, #-0x48]
    // 0x14090e8: StoreField: r1->field_f = r0
    //     0x14090e8: stur            w0, [x1, #0xf]
    // 0x14090ec: r0 = 4
    //     0x14090ec: movz            x0, #0x4
    // 0x14090f0: StoreField: r1->field_b = r0
    //     0x14090f0: stur            w0, [x1, #0xb]
    // 0x14090f4: r0 = Stack()
    //     0x14090f4: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x14090f8: mov             x1, x0
    // 0x14090fc: r0 = Instance_Alignment
    //     0x14090fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa28] Obj!Alignment@d5a721
    //     0x1409100: ldr             x0, [x0, #0xa28]
    // 0x1409104: stur            x1, [fp, #-0x40]
    // 0x1409108: StoreField: r1->field_f = r0
    //     0x1409108: stur            w0, [x1, #0xf]
    // 0x140910c: r0 = Instance_StackFit
    //     0x140910c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x1409110: ldr             x0, [x0, #0xfa8]
    // 0x1409114: ArrayStore: r1[0] = r0  ; List_4
    //     0x1409114: stur            w0, [x1, #0x17]
    // 0x1409118: r0 = Instance_Clip
    //     0x1409118: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x140911c: ldr             x0, [x0, #0x7e0]
    // 0x1409120: StoreField: r1->field_1b = r0
    //     0x1409120: stur            w0, [x1, #0x1b]
    // 0x1409124: ldur            x2, [fp, #-0x48]
    // 0x1409128: StoreField: r1->field_b = r2
    //     0x1409128: stur            w2, [x1, #0xb]
    // 0x140912c: r0 = Center()
    //     0x140912c: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1409130: mov             x1, x0
    // 0x1409134: r0 = Instance_Alignment
    //     0x1409134: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1409138: ldr             x0, [x0, #0xb10]
    // 0x140913c: stur            x1, [fp, #-0x48]
    // 0x1409140: StoreField: r1->field_f = r0
    //     0x1409140: stur            w0, [x1, #0xf]
    // 0x1409144: ldur            x0, [fp, #-0x40]
    // 0x1409148: StoreField: r1->field_b = r0
    //     0x1409148: stur            w0, [x1, #0xb]
    // 0x140914c: r0 = DottedBorder()
    //     0x140914c: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0x1409150: stur            x0, [fp, #-0x40]
    // 0x1409154: r16 = Instance_BorderType
    //     0x1409154: add             x16, PP, #0x40, lsl #12  ; [pp+0x40078] Obj!BorderType@d750a1
    //     0x1409158: ldr             x16, [x16, #0x78]
    // 0x140915c: r30 = Instance_Radius
    //     0x140915c: add             lr, PP, #0x40, lsl #12  ; [pp+0x40080] Obj!Radius@d6bee1
    //     0x1409160: ldr             lr, [lr, #0x80]
    // 0x1409164: stp             lr, x16, [SP]
    // 0x1409168: mov             x1, x0
    // 0x140916c: ldur            x2, [fp, #-0x48]
    // 0x1409170: ldur            x3, [fp, #-0x30]
    // 0x1409174: r5 = const [5.0, 5.0]
    //     0x1409174: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0x1409178: ldr             x5, [x5, #0xab0]
    // 0x140917c: d0 = 1.000000
    //     0x140917c: fmov            d0, #1.00000000
    // 0x1409180: r4 = const [0, 0x7, 0x2, 0x5, borderType, 0x5, radius, 0x6, null]
    //     0x1409180: add             x4, PP, #0x40, lsl #12  ; [pp+0x40088] List(9) [0, 0x7, 0x2, 0x5, "borderType", 0x5, "radius", 0x6, Null]
    //     0x1409184: ldr             x4, [x4, #0x88]
    // 0x1409188: r0 = DottedBorder()
    //     0x1409188: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0x140918c: ldur            d0, [fp, #-0x80]
    // 0x1409190: r0 = inline_Allocate_Double()
    //     0x1409190: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x1409194: add             x0, x0, #0x10
    //     0x1409198: cmp             x1, x0
    //     0x140919c: b.ls            #0x1409c1c
    //     0x14091a0: str             x0, [THR, #0x50]  ; THR::top
    //     0x14091a4: sub             x0, x0, #0xf
    //     0x14091a8: movz            x1, #0xe15c
    //     0x14091ac: movk            x1, #0x3, lsl #16
    //     0x14091b0: stur            x1, [x0, #-1]
    // 0x14091b4: StoreField: r0->field_7 = d0
    //     0x14091b4: stur            d0, [x0, #7]
    // 0x14091b8: stur            x0, [fp, #-0x30]
    // 0x14091bc: r0 = Container()
    //     0x14091bc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x14091c0: stur            x0, [fp, #-0x48]
    // 0x14091c4: ldur            x16, [fp, #-0x30]
    // 0x14091c8: r30 = 60.000000
    //     0x14091c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14091cc: ldr             lr, [lr, #0x110]
    // 0x14091d0: stp             lr, x16, [SP, #0x10]
    // 0x14091d4: ldur            x16, [fp, #-0x38]
    // 0x14091d8: ldur            lr, [fp, #-0x40]
    // 0x14091dc: stp             lr, x16, [SP]
    // 0x14091e0: mov             x1, x0
    // 0x14091e4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0x14091e4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0x14091e8: ldr             x4, [x4, #0x870]
    // 0x14091ec: r0 = Container()
    //     0x14091ec: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x14091f0: r0 = Padding()
    //     0x14091f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14091f4: mov             x1, x0
    // 0x14091f8: r0 = Instance_EdgeInsets
    //     0x14091f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x14091fc: ldr             x0, [x0, #0xdf8]
    // 0x1409200: stur            x1, [fp, #-0x30]
    // 0x1409204: StoreField: r1->field_f = r0
    //     0x1409204: stur            w0, [x1, #0xf]
    // 0x1409208: ldur            x0, [fp, #-0x48]
    // 0x140920c: StoreField: r1->field_b = r0
    //     0x140920c: stur            w0, [x1, #0xb]
    // 0x1409210: r0 = InkWell()
    //     0x1409210: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1409214: mov             x3, x0
    // 0x1409218: ldur            x0, [fp, #-0x30]
    // 0x140921c: stur            x3, [fp, #-0x38]
    // 0x1409220: StoreField: r3->field_b = r0
    //     0x1409220: stur            w0, [x3, #0xb]
    // 0x1409224: ldur            x2, [fp, #-8]
    // 0x1409228: r1 = Function '<anonymous closure>':.
    //     0x1409228: add             x1, PP, #0x40, lsl #12  ; [pp+0x40090] AnonymousClosure: (0x140dbb0), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140922c: ldr             x1, [x1, #0x90]
    // 0x1409230: r0 = AllocateClosure()
    //     0x1409230: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1409234: mov             x1, x0
    // 0x1409238: ldur            x0, [fp, #-0x38]
    // 0x140923c: StoreField: r0->field_f = r1
    //     0x140923c: stur            w1, [x0, #0xf]
    // 0x1409240: r1 = true
    //     0x1409240: add             x1, NULL, #0x20  ; true
    // 0x1409244: StoreField: r0->field_43 = r1
    //     0x1409244: stur            w1, [x0, #0x43]
    // 0x1409248: r2 = Instance_BoxShape
    //     0x1409248: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x140924c: ldr             x2, [x2, #0x80]
    // 0x1409250: StoreField: r0->field_47 = r2
    //     0x1409250: stur            w2, [x0, #0x47]
    // 0x1409254: StoreField: r0->field_6f = r1
    //     0x1409254: stur            w1, [x0, #0x6f]
    // 0x1409258: r3 = false
    //     0x1409258: add             x3, NULL, #0x30  ; false
    // 0x140925c: StoreField: r0->field_73 = r3
    //     0x140925c: stur            w3, [x0, #0x73]
    // 0x1409260: StoreField: r0->field_83 = r1
    //     0x1409260: stur            w1, [x0, #0x83]
    // 0x1409264: StoreField: r0->field_7b = r3
    //     0x1409264: stur            w3, [x0, #0x7b]
    // 0x1409268: b               #0x14093d8
    // 0x140926c: ldur            x0, [fp, #-8]
    // 0x1409270: r3 = false
    //     0x1409270: add             x3, NULL, #0x30  ; false
    // 0x1409274: r2 = Instance_BoxShape
    //     0x1409274: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1409278: ldr             x2, [x2, #0x80]
    // 0x140927c: LoadField: r1 = r0->field_f
    //     0x140927c: ldur            w1, [x0, #0xf]
    // 0x1409280: DecompressPointer r1
    //     0x1409280: add             x1, x1, HEAP, lsl #32
    // 0x1409284: r0 = controller()
    //     0x1409284: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1409288: LoadField: r1 = r0->field_97
    //     0x1409288: ldur            w1, [x0, #0x97]
    // 0x140928c: DecompressPointer r1
    //     0x140928c: add             x1, x1, HEAP, lsl #32
    // 0x1409290: r0 = value()
    //     0x1409290: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1409294: r1 = LoadClassIdInstr(r0)
    //     0x1409294: ldur            x1, [x0, #-1]
    //     0x1409298: ubfx            x1, x1, #0xc, #0x14
    // 0x140929c: str             x0, [SP]
    // 0x14092a0: mov             x0, x1
    // 0x14092a4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14092a4: movz            x17, #0xc898
    //     0x14092a8: add             lr, x0, x17
    //     0x14092ac: ldr             lr, [x21, lr, lsl #3]
    //     0x14092b0: blr             lr
    // 0x14092b4: r1 = LoadInt32Instr(r0)
    //     0x14092b4: sbfx            x1, x0, #1, #0x1f
    //     0x14092b8: tbz             w0, #0, #0x14092c0
    //     0x14092bc: ldur            x1, [x0, #7]
    // 0x14092c0: cmp             x1, #4
    // 0x14092c4: b.ge            #0x140931c
    // 0x14092c8: ldur            x2, [fp, #-8]
    // 0x14092cc: LoadField: r1 = r2->field_f
    //     0x14092cc: ldur            w1, [x2, #0xf]
    // 0x14092d0: DecompressPointer r1
    //     0x14092d0: add             x1, x1, HEAP, lsl #32
    // 0x14092d4: r0 = controller()
    //     0x14092d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14092d8: LoadField: r1 = r0->field_97
    //     0x14092d8: ldur            w1, [x0, #0x97]
    // 0x14092dc: DecompressPointer r1
    //     0x14092dc: add             x1, x1, HEAP, lsl #32
    // 0x14092e0: r0 = value()
    //     0x14092e0: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x14092e4: r1 = LoadClassIdInstr(r0)
    //     0x14092e4: ldur            x1, [x0, #-1]
    //     0x14092e8: ubfx            x1, x1, #0xc, #0x14
    // 0x14092ec: str             x0, [SP]
    // 0x14092f0: mov             x0, x1
    // 0x14092f4: r0 = GDT[cid_x0 + 0xc898]()
    //     0x14092f4: movz            x17, #0xc898
    //     0x14092f8: add             lr, x0, x17
    //     0x14092fc: ldr             lr, [x21, lr, lsl #3]
    //     0x1409300: blr             lr
    // 0x1409304: r1 = LoadInt32Instr(r0)
    //     0x1409304: sbfx            x1, x0, #1, #0x1f
    //     0x1409308: tbz             w0, #0, #0x1409310
    //     0x140930c: ldur            x1, [x0, #7]
    // 0x1409310: add             x0, x1, #1
    // 0x1409314: mov             x3, x0
    // 0x1409318: b               #0x1409368
    // 0x140931c: ldur            x2, [fp, #-8]
    // 0x1409320: LoadField: r1 = r2->field_f
    //     0x1409320: ldur            w1, [x2, #0xf]
    // 0x1409324: DecompressPointer r1
    //     0x1409324: add             x1, x1, HEAP, lsl #32
    // 0x1409328: r0 = controller()
    //     0x1409328: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140932c: LoadField: r1 = r0->field_97
    //     0x140932c: ldur            w1, [x0, #0x97]
    // 0x1409330: DecompressPointer r1
    //     0x1409330: add             x1, x1, HEAP, lsl #32
    // 0x1409334: r0 = value()
    //     0x1409334: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1409338: r1 = LoadClassIdInstr(r0)
    //     0x1409338: ldur            x1, [x0, #-1]
    //     0x140933c: ubfx            x1, x1, #0xc, #0x14
    // 0x1409340: str             x0, [SP]
    // 0x1409344: mov             x0, x1
    // 0x1409348: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1409348: movz            x17, #0xc898
    //     0x140934c: add             lr, x0, x17
    //     0x1409350: ldr             lr, [x21, lr, lsl #3]
    //     0x1409354: blr             lr
    // 0x1409358: r1 = LoadInt32Instr(r0)
    //     0x1409358: sbfx            x1, x0, #1, #0x1f
    //     0x140935c: tbz             w0, #0, #0x1409364
    //     0x1409360: ldur            x1, [x0, #7]
    // 0x1409364: mov             x3, x1
    // 0x1409368: stur            x3, [fp, #-0x50]
    // 0x140936c: r1 = Function '<anonymous closure>':.
    //     0x140936c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40098] AnonymousClosure: (0x9a411c), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::proofFieldWidget (0x9a4128)
    //     0x1409370: ldr             x1, [x1, #0x98]
    // 0x1409374: r2 = Null
    //     0x1409374: mov             x2, NULL
    // 0x1409378: r0 = AllocateClosure()
    //     0x1409378: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140937c: ldur            x2, [fp, #-8]
    // 0x1409380: r1 = Function '<anonymous closure>':.
    //     0x1409380: add             x1, PP, #0x40, lsl #12  ; [pp+0x400a0] AnonymousClosure: (0x1409c2c), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14f0f10)
    //     0x1409384: ldr             x1, [x1, #0xa0]
    // 0x1409388: stur            x0, [fp, #-0x30]
    // 0x140938c: r0 = AllocateClosure()
    //     0x140938c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1409390: stur            x0, [fp, #-0x38]
    // 0x1409394: r0 = ListView()
    //     0x1409394: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x1409398: stur            x0, [fp, #-0x40]
    // 0x140939c: r16 = Instance_BouncingScrollPhysics
    //     0x140939c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0x14093a0: ldr             x16, [x16, #0x890]
    // 0x14093a4: r30 = true
    //     0x14093a4: add             lr, NULL, #0x20  ; true
    // 0x14093a8: stp             lr, x16, [SP, #0x10]
    // 0x14093ac: r16 = true
    //     0x14093ac: add             x16, NULL, #0x20  ; true
    // 0x14093b0: r30 = Instance_Axis
    //     0x14093b0: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14093b4: stp             lr, x16, [SP]
    // 0x14093b8: mov             x1, x0
    // 0x14093bc: ldur            x2, [fp, #-0x38]
    // 0x14093c0: ldur            x3, [fp, #-0x50]
    // 0x14093c4: ldur            x5, [fp, #-0x30]
    // 0x14093c8: r4 = const [0, 0x8, 0x4, 0x4, physics, 0x4, primary, 0x6, scrollDirection, 0x7, shrinkWrap, 0x5, null]
    //     0x14093c8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36ad0] List(13) [0, 0x8, 0x4, 0x4, "physics", 0x4, "primary", 0x6, "scrollDirection", 0x7, "shrinkWrap", 0x5, Null]
    //     0x14093cc: ldr             x4, [x4, #0xad0]
    // 0x14093d0: r0 = ListView.separated()
    //     0x14093d0: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x14093d4: ldur            x0, [fp, #-0x40]
    // 0x14093d8: ldur            x2, [fp, #-8]
    // 0x14093dc: stur            x0, [fp, #-0x30]
    // 0x14093e0: r0 = SizedBox()
    //     0x14093e0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x14093e4: mov             x1, x0
    // 0x14093e8: r0 = 60.000000
    //     0x14093e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x14093ec: ldr             x0, [x0, #0x110]
    // 0x14093f0: stur            x1, [fp, #-0x38]
    // 0x14093f4: StoreField: r1->field_13 = r0
    //     0x14093f4: stur            w0, [x1, #0x13]
    // 0x14093f8: ldur            x0, [fp, #-0x30]
    // 0x14093fc: StoreField: r1->field_b = r0
    //     0x14093fc: stur            w0, [x1, #0xb]
    // 0x1409400: r0 = Padding()
    //     0x1409400: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1409404: mov             x2, x0
    // 0x1409408: r0 = Instance_EdgeInsets
    //     0x1409408: add             x0, PP, #0x34, lsl #12  ; [pp+0x340b8] Obj!EdgeInsets@d57981
    //     0x140940c: ldr             x0, [x0, #0xb8]
    // 0x1409410: stur            x2, [fp, #-0x30]
    // 0x1409414: StoreField: r2->field_f = r0
    //     0x1409414: stur            w0, [x2, #0xf]
    // 0x1409418: ldur            x0, [fp, #-0x38]
    // 0x140941c: StoreField: r2->field_b = r0
    //     0x140941c: stur            w0, [x2, #0xb]
    // 0x1409420: ldur            x0, [fp, #-8]
    // 0x1409424: LoadField: r1 = r0->field_13
    //     0x1409424: ldur            w1, [x0, #0x13]
    // 0x1409428: DecompressPointer r1
    //     0x1409428: add             x1, x1, HEAP, lsl #32
    // 0x140942c: r0 = of()
    //     0x140942c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1409430: LoadField: r1 = r0->field_87
    //     0x1409430: ldur            w1, [x0, #0x87]
    // 0x1409434: DecompressPointer r1
    //     0x1409434: add             x1, x1, HEAP, lsl #32
    // 0x1409438: LoadField: r0 = r1->field_7
    //     0x1409438: ldur            w0, [x1, #7]
    // 0x140943c: DecompressPointer r0
    //     0x140943c: add             x0, x0, HEAP, lsl #32
    // 0x1409440: r16 = 16.000000
    //     0x1409440: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x1409444: ldr             x16, [x16, #0x188]
    // 0x1409448: r30 = Instance_Color
    //     0x1409448: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140944c: stp             lr, x16, [SP]
    // 0x1409450: mov             x1, x0
    // 0x1409454: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1409454: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1409458: ldr             x4, [x4, #0xaa0]
    // 0x140945c: r0 = copyWith()
    //     0x140945c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1409460: stur            x0, [fp, #-0x38]
    // 0x1409464: r0 = Text()
    //     0x1409464: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1409468: mov             x1, x0
    // 0x140946c: r0 = "Write a review :"
    //     0x140946c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ad8] "Write a review :"
    //     0x1409470: ldr             x0, [x0, #0xad8]
    // 0x1409474: stur            x1, [fp, #-0x40]
    // 0x1409478: StoreField: r1->field_b = r0
    //     0x1409478: stur            w0, [x1, #0xb]
    // 0x140947c: ldur            x0, [fp, #-0x38]
    // 0x1409480: StoreField: r1->field_13 = r0
    //     0x1409480: stur            w0, [x1, #0x13]
    // 0x1409484: r0 = Padding()
    //     0x1409484: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1409488: mov             x2, x0
    // 0x140948c: r0 = Instance_EdgeInsets
    //     0x140948c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x1409490: ldr             x0, [x0, #0x668]
    // 0x1409494: stur            x2, [fp, #-0x38]
    // 0x1409498: StoreField: r2->field_f = r0
    //     0x1409498: stur            w0, [x2, #0xf]
    // 0x140949c: ldur            x0, [fp, #-0x40]
    // 0x14094a0: StoreField: r2->field_b = r0
    //     0x14094a0: stur            w0, [x2, #0xb]
    // 0x14094a4: ldur            x0, [fp, #-8]
    // 0x14094a8: LoadField: r1 = r0->field_f
    //     0x14094a8: ldur            w1, [x0, #0xf]
    // 0x14094ac: DecompressPointer r1
    //     0x14094ac: add             x1, x1, HEAP, lsl #32
    // 0x14094b0: r0 = controller()
    //     0x14094b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14094b4: LoadField: r1 = r0->field_8b
    //     0x14094b4: ldur            w1, [x0, #0x8b]
    // 0x14094b8: DecompressPointer r1
    //     0x14094b8: add             x1, x1, HEAP, lsl #32
    // 0x14094bc: stur            x1, [fp, #-0x40]
    // 0x14094c0: r0 = LengthLimitingTextInputFormatter()
    //     0x14094c0: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0x14094c4: mov             x3, x0
    // 0x14094c8: r0 = 4000
    //     0x14094c8: movz            x0, #0xfa0
    // 0x14094cc: stur            x3, [fp, #-0x48]
    // 0x14094d0: StoreField: r3->field_7 = r0
    //     0x14094d0: stur            w0, [x3, #7]
    // 0x14094d4: r1 = Null
    //     0x14094d4: mov             x1, NULL
    // 0x14094d8: r2 = 2
    //     0x14094d8: movz            x2, #0x2
    // 0x14094dc: r0 = AllocateArray()
    //     0x14094dc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14094e0: mov             x2, x0
    // 0x14094e4: ldur            x0, [fp, #-0x48]
    // 0x14094e8: stur            x2, [fp, #-0x58]
    // 0x14094ec: StoreField: r2->field_f = r0
    //     0x14094ec: stur            w0, [x2, #0xf]
    // 0x14094f0: r1 = <TextInputFormatter>
    //     0x14094f0: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0x14094f4: ldr             x1, [x1, #0x7b0]
    // 0x14094f8: r0 = AllocateGrowableArray()
    //     0x14094f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14094fc: mov             x2, x0
    // 0x1409500: ldur            x0, [fp, #-0x58]
    // 0x1409504: stur            x2, [fp, #-0x48]
    // 0x1409508: StoreField: r2->field_f = r0
    //     0x1409508: stur            w0, [x2, #0xf]
    // 0x140950c: r0 = 2
    //     0x140950c: movz            x0, #0x2
    // 0x1409510: StoreField: r2->field_b = r0
    //     0x1409510: stur            w0, [x2, #0xb]
    // 0x1409514: ldur            x0, [fp, #-8]
    // 0x1409518: LoadField: r1 = r0->field_13
    //     0x1409518: ldur            w1, [x0, #0x13]
    // 0x140951c: DecompressPointer r1
    //     0x140951c: add             x1, x1, HEAP, lsl #32
    // 0x1409520: r0 = of()
    //     0x1409520: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1409524: LoadField: r1 = r0->field_87
    //     0x1409524: ldur            w1, [x0, #0x87]
    // 0x1409528: DecompressPointer r1
    //     0x1409528: add             x1, x1, HEAP, lsl #32
    // 0x140952c: LoadField: r0 = r1->field_2b
    //     0x140952c: ldur            w0, [x1, #0x2b]
    // 0x1409530: DecompressPointer r0
    //     0x1409530: add             x0, x0, HEAP, lsl #32
    // 0x1409534: ldur            x2, [fp, #-8]
    // 0x1409538: stur            x0, [fp, #-0x58]
    // 0x140953c: LoadField: r1 = r2->field_13
    //     0x140953c: ldur            w1, [x2, #0x13]
    // 0x1409540: DecompressPointer r1
    //     0x1409540: add             x1, x1, HEAP, lsl #32
    // 0x1409544: r0 = of()
    //     0x1409544: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1409548: LoadField: r1 = r0->field_5b
    //     0x1409548: ldur            w1, [x0, #0x5b]
    // 0x140954c: DecompressPointer r1
    //     0x140954c: add             x1, x1, HEAP, lsl #32
    // 0x1409550: str             x1, [SP]
    // 0x1409554: ldur            x1, [fp, #-0x58]
    // 0x1409558: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x1409558: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x140955c: ldr             x4, [x4, #0xf40]
    // 0x1409560: r0 = copyWith()
    //     0x1409560: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1409564: ldur            x2, [fp, #-8]
    // 0x1409568: stur            x0, [fp, #-0x58]
    // 0x140956c: LoadField: r1 = r2->field_f
    //     0x140956c: ldur            w1, [x2, #0xf]
    // 0x1409570: DecompressPointer r1
    //     0x1409570: add             x1, x1, HEAP, lsl #32
    // 0x1409574: r0 = controller()
    //     0x1409574: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1409578: LoadField: r2 = r0->field_8f
    //     0x1409578: ldur            w2, [x0, #0x8f]
    // 0x140957c: DecompressPointer r2
    //     0x140957c: add             x2, x2, HEAP, lsl #32
    // 0x1409580: ldur            x0, [fp, #-8]
    // 0x1409584: stur            x2, [fp, #-0x60]
    // 0x1409588: LoadField: r1 = r0->field_13
    //     0x1409588: ldur            w1, [x0, #0x13]
    // 0x140958c: DecompressPointer r1
    //     0x140958c: add             x1, x1, HEAP, lsl #32
    // 0x1409590: r0 = getTextFormFieldInputDecorationCircleForGlass()
    //     0x1409590: bl              #0xb3fdb0  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecorationCircleForGlass
    // 0x1409594: ldur            x2, [fp, #-8]
    // 0x1409598: stur            x0, [fp, #-0x68]
    // 0x140959c: LoadField: r1 = r2->field_13
    //     0x140959c: ldur            w1, [x2, #0x13]
    // 0x14095a0: DecompressPointer r1
    //     0x14095a0: add             x1, x1, HEAP, lsl #32
    // 0x14095a4: r0 = of()
    //     0x14095a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14095a8: LoadField: r1 = r0->field_87
    //     0x14095a8: ldur            w1, [x0, #0x87]
    // 0x14095ac: DecompressPointer r1
    //     0x14095ac: add             x1, x1, HEAP, lsl #32
    // 0x14095b0: LoadField: r0 = r1->field_2b
    //     0x14095b0: ldur            w0, [x1, #0x2b]
    // 0x14095b4: DecompressPointer r0
    //     0x14095b4: add             x0, x0, HEAP, lsl #32
    // 0x14095b8: stur            x0, [fp, #-0x70]
    // 0x14095bc: r1 = Instance_Color
    //     0x14095bc: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x14095c0: d0 = 0.400000
    //     0x14095c0: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x14095c4: r0 = withOpacity()
    //     0x14095c4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x14095c8: r16 = 12.000000
    //     0x14095c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x14095cc: ldr             x16, [x16, #0x9e8]
    // 0x14095d0: stp             x16, x0, [SP]
    // 0x14095d4: ldur            x1, [fp, #-0x70]
    // 0x14095d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x14095d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x14095dc: ldr             x4, [x4, #0x9b8]
    // 0x14095e0: r0 = copyWith()
    //     0x14095e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x14095e4: ldur            x2, [fp, #-8]
    // 0x14095e8: stur            x0, [fp, #-0x70]
    // 0x14095ec: LoadField: r1 = r2->field_13
    //     0x14095ec: ldur            w1, [x2, #0x13]
    // 0x14095f0: DecompressPointer r1
    //     0x14095f0: add             x1, x1, HEAP, lsl #32
    // 0x14095f4: r0 = of()
    //     0x14095f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14095f8: LoadField: r1 = r0->field_87
    //     0x14095f8: ldur            w1, [x0, #0x87]
    // 0x14095fc: DecompressPointer r1
    //     0x14095fc: add             x1, x1, HEAP, lsl #32
    // 0x1409600: LoadField: r0 = r1->field_2b
    //     0x1409600: ldur            w0, [x1, #0x2b]
    // 0x1409604: DecompressPointer r0
    //     0x1409604: add             x0, x0, HEAP, lsl #32
    // 0x1409608: r16 = Instance_Color
    //     0x1409608: add             x16, PP, #0x33, lsl #12  ; [pp+0x337c0] Obj!Color@d6b0d1
    //     0x140960c: ldr             x16, [x16, #0x7c0]
    // 0x1409610: r30 = 12.000000
    //     0x1409610: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1409614: ldr             lr, [lr, #0x9e8]
    // 0x1409618: stp             lr, x16, [SP]
    // 0x140961c: mov             x1, x0
    // 0x1409620: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x1409620: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x1409624: ldr             x4, [x4, #0x9b8]
    // 0x1409628: r0 = copyWith()
    //     0x1409628: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x140962c: r16 = "How you use the product. Things that are great about it. Things that aren’t great about it."
    //     0x140962c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36ae0] "How you use the product. Things that are great about it. Things that aren’t great about it."
    //     0x1409630: ldr             x16, [x16, #0xae0]
    // 0x1409634: ldur            lr, [fp, #-0x70]
    // 0x1409638: stp             lr, x16, [SP, #8]
    // 0x140963c: str             x0, [SP]
    // 0x1409640: ldur            x1, [fp, #-0x68]
    // 0x1409644: r4 = const [0, 0x4, 0x3, 0x1, errorStyle, 0x3, hintStyle, 0x2, hintText, 0x1, null]
    //     0x1409644: add             x4, PP, #0x33, lsl #12  ; [pp+0x33fe8] List(11) [0, 0x4, 0x3, 0x1, "errorStyle", 0x3, "hintStyle", 0x2, "hintText", 0x1, Null]
    //     0x1409648: ldr             x4, [x4, #0xfe8]
    // 0x140964c: r0 = copyWith()
    //     0x140964c: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0x1409650: ldur            x2, [fp, #-8]
    // 0x1409654: r1 = Function '<anonymous closure>':.
    //     0x1409654: add             x1, PP, #0x40, lsl #12  ; [pp+0x400a8] AnonymousClosure: (0x14086c8), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14f0f10)
    //     0x1409658: ldr             x1, [x1, #0xa8]
    // 0x140965c: stur            x0, [fp, #-0x68]
    // 0x1409660: r0 = AllocateClosure()
    //     0x1409660: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1409664: r1 = <String>
    //     0x1409664: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0x1409668: stur            x0, [fp, #-0x70]
    // 0x140966c: r0 = TextFormField()
    //     0x140966c: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0x1409670: stur            x0, [fp, #-0x78]
    // 0x1409674: r16 = false
    //     0x1409674: add             x16, NULL, #0x30  ; false
    // 0x1409678: r30 = Instance_AutovalidateMode
    //     0x1409678: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0x140967c: ldr             lr, [lr, #0x7e8]
    // 0x1409680: stp             lr, x16, [SP, #0x40]
    // 0x1409684: ldur            x16, [fp, #-0x48]
    // 0x1409688: r30 = Instance_TextInputType
    //     0x1409688: add             lr, PP, #0x33, lsl #12  ; [pp+0x337f0] Obj!TextInputType@d55b61
    //     0x140968c: ldr             lr, [lr, #0x7f0]
    // 0x1409690: stp             lr, x16, [SP, #0x30]
    // 0x1409694: r16 = Instance_TextInputAction
    //     0x1409694: ldr             x16, [PP, #0x70c0]  ; [pp+0x70c0] Obj!TextInputAction@d728e1
    // 0x1409698: r30 = 6
    //     0x1409698: movz            lr, #0x6
    // 0x140969c: stp             lr, x16, [SP, #0x20]
    // 0x14096a0: r16 = 10
    //     0x14096a0: movz            x16, #0xa
    // 0x14096a4: ldur            lr, [fp, #-0x58]
    // 0x14096a8: stp             lr, x16, [SP, #0x10]
    // 0x14096ac: ldur            x16, [fp, #-0x60]
    // 0x14096b0: ldur            lr, [fp, #-0x70]
    // 0x14096b4: stp             lr, x16, [SP]
    // 0x14096b8: mov             x1, x0
    // 0x14096bc: ldur            x2, [fp, #-0x68]
    // 0x14096c0: r4 = const [0, 0xc, 0xa, 0x2, autovalidateMode, 0x3, controller, 0xa, enableSuggestions, 0x2, inputFormatters, 0x4, keyboardType, 0x5, maxLines, 0x8, minLines, 0x7, onChanged, 0xb, style, 0x9, textInputAction, 0x6, null]
    //     0x14096c0: add             x4, PP, #0x36, lsl #12  ; [pp+0x36af0] List(25) [0, 0xc, 0xa, 0x2, "autovalidateMode", 0x3, "controller", 0xa, "enableSuggestions", 0x2, "inputFormatters", 0x4, "keyboardType", 0x5, "maxLines", 0x8, "minLines", 0x7, "onChanged", 0xb, "style", 0x9, "textInputAction", 0x6, Null]
    //     0x14096c4: ldr             x4, [x4, #0xaf0]
    // 0x14096c8: r0 = TextFormField()
    //     0x14096c8: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0x14096cc: r0 = Form()
    //     0x14096cc: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0x14096d0: mov             x1, x0
    // 0x14096d4: ldur            x0, [fp, #-0x78]
    // 0x14096d8: stur            x1, [fp, #-0x48]
    // 0x14096dc: StoreField: r1->field_b = r0
    //     0x14096dc: stur            w0, [x1, #0xb]
    // 0x14096e0: r0 = Instance_AutovalidateMode
    //     0x14096e0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0x14096e4: ldr             x0, [x0, #0x800]
    // 0x14096e8: StoreField: r1->field_23 = r0
    //     0x14096e8: stur            w0, [x1, #0x23]
    // 0x14096ec: ldur            x0, [fp, #-0x40]
    // 0x14096f0: StoreField: r1->field_7 = r0
    //     0x14096f0: stur            w0, [x1, #7]
    // 0x14096f4: r0 = Padding()
    //     0x14096f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x14096f8: mov             x2, x0
    // 0x14096fc: r0 = Instance_EdgeInsets
    //     0x14096fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe18] Obj!EdgeInsets@d57ef1
    //     0x1409700: ldr             x0, [x0, #0xe18]
    // 0x1409704: stur            x2, [fp, #-0x40]
    // 0x1409708: StoreField: r2->field_f = r0
    //     0x1409708: stur            w0, [x2, #0xf]
    // 0x140970c: ldur            x0, [fp, #-0x48]
    // 0x1409710: StoreField: r2->field_b = r0
    //     0x1409710: stur            w0, [x2, #0xb]
    // 0x1409714: ldur            x0, [fp, #-8]
    // 0x1409718: LoadField: r1 = r0->field_f
    //     0x1409718: ldur            w1, [x0, #0xf]
    // 0x140971c: DecompressPointer r1
    //     0x140971c: add             x1, x1, HEAP, lsl #32
    // 0x1409720: r0 = controller()
    //     0x1409720: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1409724: LoadField: r1 = r0->field_4b
    //     0x1409724: ldur            w1, [x0, #0x4b]
    // 0x1409728: DecompressPointer r1
    //     0x1409728: add             x1, x1, HEAP, lsl #32
    // 0x140972c: r0 = value()
    //     0x140972c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1409730: LoadField: r1 = r0->field_b
    //     0x1409730: ldur            w1, [x0, #0xb]
    // 0x1409734: DecompressPointer r1
    //     0x1409734: add             x1, x1, HEAP, lsl #32
    // 0x1409738: cmp             w1, NULL
    // 0x140973c: b.ne            #0x1409748
    // 0x1409740: r0 = Null
    //     0x1409740: mov             x0, NULL
    // 0x1409744: b               #0x1409790
    // 0x1409748: LoadField: r0 = r1->field_1f
    //     0x1409748: ldur            w0, [x1, #0x1f]
    // 0x140974c: DecompressPointer r0
    //     0x140974c: add             x0, x0, HEAP, lsl #32
    // 0x1409750: cmp             w0, NULL
    // 0x1409754: b.ne            #0x1409760
    // 0x1409758: r0 = Null
    //     0x1409758: mov             x0, NULL
    // 0x140975c: b               #0x1409790
    // 0x1409760: LoadField: r1 = r0->field_2f
    //     0x1409760: ldur            w1, [x0, #0x2f]
    // 0x1409764: DecompressPointer r1
    //     0x1409764: add             x1, x1, HEAP, lsl #32
    // 0x1409768: cmp             w1, NULL
    // 0x140976c: b.ne            #0x1409778
    // 0x1409770: r0 = Null
    //     0x1409770: mov             x0, NULL
    // 0x1409774: b               #0x1409790
    // 0x1409778: LoadField: r0 = r1->field_7
    //     0x1409778: ldur            w0, [x1, #7]
    // 0x140977c: cbnz            w0, #0x1409788
    // 0x1409780: r1 = false
    //     0x1409780: add             x1, NULL, #0x30  ; false
    // 0x1409784: b               #0x140978c
    // 0x1409788: r1 = true
    //     0x1409788: add             x1, NULL, #0x20  ; true
    // 0x140978c: mov             x0, x1
    // 0x1409790: cmp             w0, NULL
    // 0x1409794: b.ne            #0x14097a0
    // 0x1409798: r2 = false
    //     0x1409798: add             x2, NULL, #0x30  ; false
    // 0x140979c: b               #0x14097a4
    // 0x14097a0: mov             x2, x0
    // 0x14097a4: ldur            x0, [fp, #-8]
    // 0x14097a8: stur            x2, [fp, #-0x48]
    // 0x14097ac: LoadField: r1 = r0->field_13
    //     0x14097ac: ldur            w1, [x0, #0x13]
    // 0x14097b0: DecompressPointer r1
    //     0x14097b0: add             x1, x1, HEAP, lsl #32
    // 0x14097b4: r0 = of()
    //     0x14097b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x14097b8: LoadField: r1 = r0->field_5b
    //     0x14097b8: ldur            w1, [x0, #0x5b]
    // 0x14097bc: DecompressPointer r1
    //     0x14097bc: add             x1, x1, HEAP, lsl #32
    // 0x14097c0: r0 = LoadClassIdInstr(r1)
    //     0x14097c0: ldur            x0, [x1, #-1]
    //     0x14097c4: ubfx            x0, x0, #0xc, #0x14
    // 0x14097c8: d0 = 0.030000
    //     0x14097c8: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x14097cc: ldr             d0, [x17, #0x238]
    // 0x14097d0: r0 = GDT[cid_x0 + -0xffa]()
    //     0x14097d0: sub             lr, x0, #0xffa
    //     0x14097d4: ldr             lr, [x21, lr, lsl #3]
    //     0x14097d8: blr             lr
    // 0x14097dc: stur            x0, [fp, #-0x58]
    // 0x14097e0: r0 = Radius()
    //     0x14097e0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x14097e4: d0 = 20.000000
    //     0x14097e4: fmov            d0, #20.00000000
    // 0x14097e8: stur            x0, [fp, #-0x60]
    // 0x14097ec: StoreField: r0->field_7 = d0
    //     0x14097ec: stur            d0, [x0, #7]
    // 0x14097f0: StoreField: r0->field_f = d0
    //     0x14097f0: stur            d0, [x0, #0xf]
    // 0x14097f4: r0 = BorderRadius()
    //     0x14097f4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x14097f8: mov             x1, x0
    // 0x14097fc: ldur            x0, [fp, #-0x60]
    // 0x1409800: stur            x1, [fp, #-0x68]
    // 0x1409804: StoreField: r1->field_7 = r0
    //     0x1409804: stur            w0, [x1, #7]
    // 0x1409808: StoreField: r1->field_b = r0
    //     0x1409808: stur            w0, [x1, #0xb]
    // 0x140980c: StoreField: r1->field_f = r0
    //     0x140980c: stur            w0, [x1, #0xf]
    // 0x1409810: StoreField: r1->field_13 = r0
    //     0x1409810: stur            w0, [x1, #0x13]
    // 0x1409814: r0 = BoxDecoration()
    //     0x1409814: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1409818: mov             x2, x0
    // 0x140981c: ldur            x0, [fp, #-0x58]
    // 0x1409820: stur            x2, [fp, #-0x60]
    // 0x1409824: StoreField: r2->field_7 = r0
    //     0x1409824: stur            w0, [x2, #7]
    // 0x1409828: ldur            x0, [fp, #-0x68]
    // 0x140982c: StoreField: r2->field_13 = r0
    //     0x140982c: stur            w0, [x2, #0x13]
    // 0x1409830: r0 = Instance_BoxShape
    //     0x1409830: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1409834: ldr             x0, [x0, #0x80]
    // 0x1409838: StoreField: r2->field_23 = r0
    //     0x1409838: stur            w0, [x2, #0x23]
    // 0x140983c: r1 = Instance_Color
    //     0x140983c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1409840: d0 = 0.500000
    //     0x1409840: fmov            d0, #0.50000000
    // 0x1409844: r0 = withOpacity()
    //     0x1409844: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1409848: stur            x0, [fp, #-0x58]
    // 0x140984c: r0 = Icon()
    //     0x140984c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x1409850: mov             x2, x0
    // 0x1409854: r0 = Instance_IconData
    //     0x1409854: add             x0, PP, #0x36, lsl #12  ; [pp+0x36af8] Obj!IconData@d55421
    //     0x1409858: ldr             x0, [x0, #0xaf8]
    // 0x140985c: stur            x2, [fp, #-0x68]
    // 0x1409860: StoreField: r2->field_b = r0
    //     0x1409860: stur            w0, [x2, #0xb]
    // 0x1409864: ldur            x0, [fp, #-0x58]
    // 0x1409868: StoreField: r2->field_23 = r0
    //     0x1409868: stur            w0, [x2, #0x23]
    // 0x140986c: ldur            x0, [fp, #-8]
    // 0x1409870: LoadField: r1 = r0->field_f
    //     0x1409870: ldur            w1, [x0, #0xf]
    // 0x1409874: DecompressPointer r1
    //     0x1409874: add             x1, x1, HEAP, lsl #32
    // 0x1409878: r0 = controller()
    //     0x1409878: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140987c: LoadField: r1 = r0->field_4b
    //     0x140987c: ldur            w1, [x0, #0x4b]
    // 0x1409880: DecompressPointer r1
    //     0x1409880: add             x1, x1, HEAP, lsl #32
    // 0x1409884: r0 = value()
    //     0x1409884: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1409888: LoadField: r1 = r0->field_b
    //     0x1409888: ldur            w1, [x0, #0xb]
    // 0x140988c: DecompressPointer r1
    //     0x140988c: add             x1, x1, HEAP, lsl #32
    // 0x1409890: cmp             w1, NULL
    // 0x1409894: b.ne            #0x14098a0
    // 0x1409898: r0 = Null
    //     0x1409898: mov             x0, NULL
    // 0x140989c: b               #0x14098c4
    // 0x14098a0: LoadField: r0 = r1->field_1f
    //     0x14098a0: ldur            w0, [x1, #0x1f]
    // 0x14098a4: DecompressPointer r0
    //     0x14098a4: add             x0, x0, HEAP, lsl #32
    // 0x14098a8: cmp             w0, NULL
    // 0x14098ac: b.ne            #0x14098b8
    // 0x14098b0: r0 = Null
    //     0x14098b0: mov             x0, NULL
    // 0x14098b4: b               #0x14098c4
    // 0x14098b8: LoadField: r1 = r0->field_2f
    //     0x14098b8: ldur            w1, [x0, #0x2f]
    // 0x14098bc: DecompressPointer r1
    //     0x14098bc: add             x1, x1, HEAP, lsl #32
    // 0x14098c0: mov             x0, x1
    // 0x14098c4: cmp             w0, NULL
    // 0x14098c8: b.ne            #0x14098d4
    // 0x14098cc: r10 = ""
    //     0x14098cc: ldr             x10, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x14098d0: b               #0x14098d8
    // 0x14098d4: mov             x10, x0
    // 0x14098d8: ldur            x1, [fp, #-8]
    // 0x14098dc: ldur            x9, [fp, #-0x10]
    // 0x14098e0: ldur            x8, [fp, #-0x18]
    // 0x14098e4: ldur            x7, [fp, #-0x20]
    // 0x14098e8: ldur            x6, [fp, #-0x28]
    // 0x14098ec: ldur            x5, [fp, #-0x30]
    // 0x14098f0: ldur            x4, [fp, #-0x38]
    // 0x14098f4: ldur            x3, [fp, #-0x40]
    // 0x14098f8: ldur            x2, [fp, #-0x48]
    // 0x14098fc: ldur            x0, [fp, #-0x68]
    // 0x1409900: stur            x10, [fp, #-0x58]
    // 0x1409904: LoadField: r11 = r1->field_13
    //     0x1409904: ldur            w11, [x1, #0x13]
    // 0x1409908: DecompressPointer r11
    //     0x1409908: add             x11, x11, HEAP, lsl #32
    // 0x140990c: mov             x1, x11
    // 0x1409910: r0 = of()
    //     0x1409910: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1409914: LoadField: r1 = r0->field_87
    //     0x1409914: ldur            w1, [x0, #0x87]
    // 0x1409918: DecompressPointer r1
    //     0x1409918: add             x1, x1, HEAP, lsl #32
    // 0x140991c: LoadField: r0 = r1->field_2b
    //     0x140991c: ldur            w0, [x1, #0x2b]
    // 0x1409920: DecompressPointer r0
    //     0x1409920: add             x0, x0, HEAP, lsl #32
    // 0x1409924: stur            x0, [fp, #-8]
    // 0x1409928: r1 = Instance_Color
    //     0x1409928: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140992c: d0 = 0.500000
    //     0x140992c: fmov            d0, #0.50000000
    // 0x1409930: r0 = withOpacity()
    //     0x1409930: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1409934: r16 = 12.000000
    //     0x1409934: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x1409938: ldr             x16, [x16, #0x9e8]
    // 0x140993c: stp             x0, x16, [SP]
    // 0x1409940: ldur            x1, [fp, #-8]
    // 0x1409944: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x1409944: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x1409948: ldr             x4, [x4, #0xaa0]
    // 0x140994c: r0 = copyWith()
    //     0x140994c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x1409950: stur            x0, [fp, #-8]
    // 0x1409954: r0 = Text()
    //     0x1409954: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x1409958: mov             x2, x0
    // 0x140995c: ldur            x0, [fp, #-0x58]
    // 0x1409960: stur            x2, [fp, #-0x70]
    // 0x1409964: StoreField: r2->field_b = r0
    //     0x1409964: stur            w0, [x2, #0xb]
    // 0x1409968: ldur            x0, [fp, #-8]
    // 0x140996c: StoreField: r2->field_13 = r0
    //     0x140996c: stur            w0, [x2, #0x13]
    // 0x1409970: r1 = <FlexParentData>
    //     0x1409970: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1409974: ldr             x1, [x1, #0xe00]
    // 0x1409978: r0 = Expanded()
    //     0x1409978: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x140997c: mov             x3, x0
    // 0x1409980: r0 = 1
    //     0x1409980: movz            x0, #0x1
    // 0x1409984: stur            x3, [fp, #-8]
    // 0x1409988: StoreField: r3->field_13 = r0
    //     0x1409988: stur            x0, [x3, #0x13]
    // 0x140998c: r0 = Instance_FlexFit
    //     0x140998c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1409990: ldr             x0, [x0, #0xe08]
    // 0x1409994: StoreField: r3->field_1b = r0
    //     0x1409994: stur            w0, [x3, #0x1b]
    // 0x1409998: ldur            x0, [fp, #-0x70]
    // 0x140999c: StoreField: r3->field_b = r0
    //     0x140999c: stur            w0, [x3, #0xb]
    // 0x14099a0: r1 = Null
    //     0x14099a0: mov             x1, NULL
    // 0x14099a4: r2 = 6
    //     0x14099a4: movz            x2, #0x6
    // 0x14099a8: r0 = AllocateArray()
    //     0x14099a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14099ac: mov             x2, x0
    // 0x14099b0: ldur            x0, [fp, #-0x68]
    // 0x14099b4: stur            x2, [fp, #-0x58]
    // 0x14099b8: StoreField: r2->field_f = r0
    //     0x14099b8: stur            w0, [x2, #0xf]
    // 0x14099bc: r16 = Instance_SizedBox
    //     0x14099bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x14099c0: ldr             x16, [x16, #0xb20]
    // 0x14099c4: StoreField: r2->field_13 = r16
    //     0x14099c4: stur            w16, [x2, #0x13]
    // 0x14099c8: ldur            x0, [fp, #-8]
    // 0x14099cc: ArrayStore: r2[0] = r0  ; List_4
    //     0x14099cc: stur            w0, [x2, #0x17]
    // 0x14099d0: r1 = <Widget>
    //     0x14099d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14099d4: r0 = AllocateGrowableArray()
    //     0x14099d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14099d8: mov             x1, x0
    // 0x14099dc: ldur            x0, [fp, #-0x58]
    // 0x14099e0: stur            x1, [fp, #-8]
    // 0x14099e4: StoreField: r1->field_f = r0
    //     0x14099e4: stur            w0, [x1, #0xf]
    // 0x14099e8: r0 = 6
    //     0x14099e8: movz            x0, #0x6
    // 0x14099ec: StoreField: r1->field_b = r0
    //     0x14099ec: stur            w0, [x1, #0xb]
    // 0x14099f0: r0 = Row()
    //     0x14099f0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x14099f4: mov             x1, x0
    // 0x14099f8: r0 = Instance_Axis
    //     0x14099f8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x14099fc: stur            x1, [fp, #-0x58]
    // 0x1409a00: StoreField: r1->field_f = r0
    //     0x1409a00: stur            w0, [x1, #0xf]
    // 0x1409a04: r0 = Instance_MainAxisAlignment
    //     0x1409a04: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1409a08: ldr             x0, [x0, #0xa08]
    // 0x1409a0c: StoreField: r1->field_13 = r0
    //     0x1409a0c: stur            w0, [x1, #0x13]
    // 0x1409a10: r2 = Instance_MainAxisSize
    //     0x1409a10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1409a14: ldr             x2, [x2, #0xa10]
    // 0x1409a18: ArrayStore: r1[0] = r2  ; List_4
    //     0x1409a18: stur            w2, [x1, #0x17]
    // 0x1409a1c: r3 = Instance_CrossAxisAlignment
    //     0x1409a1c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1409a20: ldr             x3, [x3, #0xa18]
    // 0x1409a24: StoreField: r1->field_1b = r3
    //     0x1409a24: stur            w3, [x1, #0x1b]
    // 0x1409a28: r3 = Instance_VerticalDirection
    //     0x1409a28: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1409a2c: ldr             x3, [x3, #0xa20]
    // 0x1409a30: StoreField: r1->field_23 = r3
    //     0x1409a30: stur            w3, [x1, #0x23]
    // 0x1409a34: r4 = Instance_Clip
    //     0x1409a34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1409a38: ldr             x4, [x4, #0x38]
    // 0x1409a3c: StoreField: r1->field_2b = r4
    //     0x1409a3c: stur            w4, [x1, #0x2b]
    // 0x1409a40: StoreField: r1->field_2f = rZR
    //     0x1409a40: stur            xzr, [x1, #0x2f]
    // 0x1409a44: ldur            x5, [fp, #-8]
    // 0x1409a48: StoreField: r1->field_b = r5
    //     0x1409a48: stur            w5, [x1, #0xb]
    // 0x1409a4c: r0 = Container()
    //     0x1409a4c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1409a50: stur            x0, [fp, #-8]
    // 0x1409a54: r16 = Instance_EdgeInsets
    //     0x1409a54: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x1409a58: ldr             x16, [x16, #0x980]
    // 0x1409a5c: ldur            lr, [fp, #-0x60]
    // 0x1409a60: stp             lr, x16, [SP, #8]
    // 0x1409a64: ldur            x16, [fp, #-0x58]
    // 0x1409a68: str             x16, [SP]
    // 0x1409a6c: mov             x1, x0
    // 0x1409a70: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0x1409a70: add             x4, PP, #0x36, lsl #12  ; [pp+0x36610] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    //     0x1409a74: ldr             x4, [x4, #0x610]
    // 0x1409a78: r0 = Container()
    //     0x1409a78: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1409a7c: r0 = Padding()
    //     0x1409a7c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1409a80: mov             x1, x0
    // 0x1409a84: r0 = Instance_EdgeInsets
    //     0x1409a84: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a98] Obj!EdgeInsets@d57f21
    //     0x1409a88: ldr             x0, [x0, #0xa98]
    // 0x1409a8c: stur            x1, [fp, #-0x58]
    // 0x1409a90: StoreField: r1->field_f = r0
    //     0x1409a90: stur            w0, [x1, #0xf]
    // 0x1409a94: ldur            x0, [fp, #-8]
    // 0x1409a98: StoreField: r1->field_b = r0
    //     0x1409a98: stur            w0, [x1, #0xb]
    // 0x1409a9c: r0 = Visibility()
    //     0x1409a9c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1409aa0: mov             x3, x0
    // 0x1409aa4: ldur            x0, [fp, #-0x58]
    // 0x1409aa8: stur            x3, [fp, #-8]
    // 0x1409aac: StoreField: r3->field_b = r0
    //     0x1409aac: stur            w0, [x3, #0xb]
    // 0x1409ab0: r0 = Instance_SizedBox
    //     0x1409ab0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1409ab4: StoreField: r3->field_f = r0
    //     0x1409ab4: stur            w0, [x3, #0xf]
    // 0x1409ab8: ldur            x0, [fp, #-0x48]
    // 0x1409abc: StoreField: r3->field_13 = r0
    //     0x1409abc: stur            w0, [x3, #0x13]
    // 0x1409ac0: r0 = false
    //     0x1409ac0: add             x0, NULL, #0x30  ; false
    // 0x1409ac4: ArrayStore: r3[0] = r0  ; List_4
    //     0x1409ac4: stur            w0, [x3, #0x17]
    // 0x1409ac8: StoreField: r3->field_1b = r0
    //     0x1409ac8: stur            w0, [x3, #0x1b]
    // 0x1409acc: StoreField: r3->field_1f = r0
    //     0x1409acc: stur            w0, [x3, #0x1f]
    // 0x1409ad0: StoreField: r3->field_23 = r0
    //     0x1409ad0: stur            w0, [x3, #0x23]
    // 0x1409ad4: StoreField: r3->field_27 = r0
    //     0x1409ad4: stur            w0, [x3, #0x27]
    // 0x1409ad8: StoreField: r3->field_2b = r0
    //     0x1409ad8: stur            w0, [x3, #0x2b]
    // 0x1409adc: r1 = Null
    //     0x1409adc: mov             x1, NULL
    // 0x1409ae0: r2 = 16
    //     0x1409ae0: movz            x2, #0x10
    // 0x1409ae4: r0 = AllocateArray()
    //     0x1409ae4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1409ae8: mov             x2, x0
    // 0x1409aec: ldur            x0, [fp, #-0x10]
    // 0x1409af0: stur            x2, [fp, #-0x48]
    // 0x1409af4: StoreField: r2->field_f = r0
    //     0x1409af4: stur            w0, [x2, #0xf]
    // 0x1409af8: ldur            x0, [fp, #-0x18]
    // 0x1409afc: StoreField: r2->field_13 = r0
    //     0x1409afc: stur            w0, [x2, #0x13]
    // 0x1409b00: ldur            x0, [fp, #-0x20]
    // 0x1409b04: ArrayStore: r2[0] = r0  ; List_4
    //     0x1409b04: stur            w0, [x2, #0x17]
    // 0x1409b08: ldur            x0, [fp, #-0x28]
    // 0x1409b0c: StoreField: r2->field_1b = r0
    //     0x1409b0c: stur            w0, [x2, #0x1b]
    // 0x1409b10: ldur            x0, [fp, #-0x30]
    // 0x1409b14: StoreField: r2->field_1f = r0
    //     0x1409b14: stur            w0, [x2, #0x1f]
    // 0x1409b18: ldur            x0, [fp, #-0x38]
    // 0x1409b1c: StoreField: r2->field_23 = r0
    //     0x1409b1c: stur            w0, [x2, #0x23]
    // 0x1409b20: ldur            x0, [fp, #-0x40]
    // 0x1409b24: StoreField: r2->field_27 = r0
    //     0x1409b24: stur            w0, [x2, #0x27]
    // 0x1409b28: ldur            x0, [fp, #-8]
    // 0x1409b2c: StoreField: r2->field_2b = r0
    //     0x1409b2c: stur            w0, [x2, #0x2b]
    // 0x1409b30: r1 = <Widget>
    //     0x1409b30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1409b34: r0 = AllocateGrowableArray()
    //     0x1409b34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1409b38: mov             x1, x0
    // 0x1409b3c: ldur            x0, [fp, #-0x48]
    // 0x1409b40: stur            x1, [fp, #-8]
    // 0x1409b44: StoreField: r1->field_f = r0
    //     0x1409b44: stur            w0, [x1, #0xf]
    // 0x1409b48: r0 = 16
    //     0x1409b48: movz            x0, #0x10
    // 0x1409b4c: StoreField: r1->field_b = r0
    //     0x1409b4c: stur            w0, [x1, #0xb]
    // 0x1409b50: r0 = Column()
    //     0x1409b50: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x1409b54: mov             x1, x0
    // 0x1409b58: r0 = Instance_Axis
    //     0x1409b58: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1409b5c: stur            x1, [fp, #-0x10]
    // 0x1409b60: StoreField: r1->field_f = r0
    //     0x1409b60: stur            w0, [x1, #0xf]
    // 0x1409b64: r2 = Instance_MainAxisAlignment
    //     0x1409b64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1409b68: ldr             x2, [x2, #0xa08]
    // 0x1409b6c: StoreField: r1->field_13 = r2
    //     0x1409b6c: stur            w2, [x1, #0x13]
    // 0x1409b70: r2 = Instance_MainAxisSize
    //     0x1409b70: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1409b74: ldr             x2, [x2, #0xa10]
    // 0x1409b78: ArrayStore: r1[0] = r2  ; List_4
    //     0x1409b78: stur            w2, [x1, #0x17]
    // 0x1409b7c: r2 = Instance_CrossAxisAlignment
    //     0x1409b7c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x1409b80: ldr             x2, [x2, #0x890]
    // 0x1409b84: StoreField: r1->field_1b = r2
    //     0x1409b84: stur            w2, [x1, #0x1b]
    // 0x1409b88: r2 = Instance_VerticalDirection
    //     0x1409b88: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1409b8c: ldr             x2, [x2, #0xa20]
    // 0x1409b90: StoreField: r1->field_23 = r2
    //     0x1409b90: stur            w2, [x1, #0x23]
    // 0x1409b94: r2 = Instance_Clip
    //     0x1409b94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1409b98: ldr             x2, [x2, #0x38]
    // 0x1409b9c: StoreField: r1->field_2b = r2
    //     0x1409b9c: stur            w2, [x1, #0x2b]
    // 0x1409ba0: StoreField: r1->field_2f = rZR
    //     0x1409ba0: stur            xzr, [x1, #0x2f]
    // 0x1409ba4: ldur            x2, [fp, #-8]
    // 0x1409ba8: StoreField: r1->field_b = r2
    //     0x1409ba8: stur            w2, [x1, #0xb]
    // 0x1409bac: r0 = SingleChildScrollView()
    //     0x1409bac: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x1409bb0: mov             x1, x0
    // 0x1409bb4: r0 = Instance_Axis
    //     0x1409bb4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1409bb8: stur            x1, [fp, #-8]
    // 0x1409bbc: StoreField: r1->field_b = r0
    //     0x1409bbc: stur            w0, [x1, #0xb]
    // 0x1409bc0: r0 = false
    //     0x1409bc0: add             x0, NULL, #0x30  ; false
    // 0x1409bc4: StoreField: r1->field_f = r0
    //     0x1409bc4: stur            w0, [x1, #0xf]
    // 0x1409bc8: ldur            x0, [fp, #-0x10]
    // 0x1409bcc: StoreField: r1->field_23 = r0
    //     0x1409bcc: stur            w0, [x1, #0x23]
    // 0x1409bd0: r0 = Instance_DragStartBehavior
    //     0x1409bd0: ldr             x0, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x1409bd4: StoreField: r1->field_27 = r0
    //     0x1409bd4: stur            w0, [x1, #0x27]
    // 0x1409bd8: r0 = Instance_Clip
    //     0x1409bd8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x1409bdc: ldr             x0, [x0, #0x7e0]
    // 0x1409be0: StoreField: r1->field_2b = r0
    //     0x1409be0: stur            w0, [x1, #0x2b]
    // 0x1409be4: r0 = Instance_HitTestBehavior
    //     0x1409be4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x1409be8: ldr             x0, [x0, #0x288]
    // 0x1409bec: StoreField: r1->field_2f = r0
    //     0x1409bec: stur            w0, [x1, #0x2f]
    // 0x1409bf0: r0 = Padding()
    //     0x1409bf0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1409bf4: r1 = Instance_EdgeInsets
    //     0x1409bf4: add             x1, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0x1409bf8: ldr             x1, [x1, #0xb00]
    // 0x1409bfc: StoreField: r0->field_f = r1
    //     0x1409bfc: stur            w1, [x0, #0xf]
    // 0x1409c00: ldur            x1, [fp, #-8]
    // 0x1409c04: StoreField: r0->field_b = r1
    //     0x1409c04: stur            w1, [x0, #0xb]
    // 0x1409c08: LeaveFrame
    //     0x1409c08: mov             SP, fp
    //     0x1409c0c: ldp             fp, lr, [SP], #0x10
    // 0x1409c10: ret
    //     0x1409c10: ret             
    // 0x1409c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1409c14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1409c18: b               #0x1408744
    // 0x1409c1c: SaveReg d0
    //     0x1409c1c: str             q0, [SP, #-0x10]!
    // 0x1409c20: r0 = AllocateDouble()
    //     0x1409c20: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x1409c24: RestoreReg d0
    //     0x1409c24: ldr             q0, [SP], #0x10
    // 0x1409c28: b               #0x14091b4
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x1409c2c, size: 0xd00
    // 0x1409c2c: EnterFrame
    //     0x1409c2c: stp             fp, lr, [SP, #-0x10]!
    //     0x1409c30: mov             fp, SP
    // 0x1409c34: AllocStack(0x60)
    //     0x1409c34: sub             SP, SP, #0x60
    // 0x1409c38: SetupParameters()
    //     0x1409c38: ldr             x0, [fp, #0x20]
    //     0x1409c3c: ldur            w1, [x0, #0x17]
    //     0x1409c40: add             x1, x1, HEAP, lsl #32
    //     0x1409c44: stur            x1, [fp, #-8]
    // 0x1409c48: CheckStackOverflow
    //     0x1409c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1409c4c: cmp             SP, x16
    //     0x1409c50: b.ls            #0x140a924
    // 0x1409c54: r1 = 1
    //     0x1409c54: movz            x1, #0x1
    // 0x1409c58: r0 = AllocateContext()
    //     0x1409c58: bl              #0x16f6108  ; AllocateContextStub
    // 0x1409c5c: mov             x2, x0
    // 0x1409c60: ldur            x0, [fp, #-8]
    // 0x1409c64: stur            x2, [fp, #-0x10]
    // 0x1409c68: StoreField: r2->field_b = r0
    //     0x1409c68: stur            w0, [x2, #0xb]
    // 0x1409c6c: ldr             x3, [fp, #0x10]
    // 0x1409c70: StoreField: r2->field_f = r3
    //     0x1409c70: stur            w3, [x2, #0xf]
    // 0x1409c74: LoadField: r1 = r0->field_f
    //     0x1409c74: ldur            w1, [x0, #0xf]
    // 0x1409c78: DecompressPointer r1
    //     0x1409c78: add             x1, x1, HEAP, lsl #32
    // 0x1409c7c: r0 = controller()
    //     0x1409c7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1409c80: LoadField: r1 = r0->field_97
    //     0x1409c80: ldur            w1, [x0, #0x97]
    // 0x1409c84: DecompressPointer r1
    //     0x1409c84: add             x1, x1, HEAP, lsl #32
    // 0x1409c88: r0 = value()
    //     0x1409c88: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1409c8c: r1 = LoadClassIdInstr(r0)
    //     0x1409c8c: ldur            x1, [x0, #-1]
    //     0x1409c90: ubfx            x1, x1, #0xc, #0x14
    // 0x1409c94: str             x0, [SP]
    // 0x1409c98: mov             x0, x1
    // 0x1409c9c: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1409c9c: movz            x17, #0xc898
    //     0x1409ca0: add             lr, x0, x17
    //     0x1409ca4: ldr             lr, [x21, lr, lsl #3]
    //     0x1409ca8: blr             lr
    // 0x1409cac: mov             x1, x0
    // 0x1409cb0: ldr             x0, [fp, #0x10]
    // 0x1409cb4: r2 = LoadInt32Instr(r0)
    //     0x1409cb4: sbfx            x2, x0, #1, #0x1f
    //     0x1409cb8: tbz             w0, #0, #0x1409cc0
    //     0x1409cbc: ldur            x2, [x0, #7]
    // 0x1409cc0: r0 = LoadInt32Instr(r1)
    //     0x1409cc0: sbfx            x0, x1, #1, #0x1f
    //     0x1409cc4: tbz             w1, #0, #0x1409ccc
    //     0x1409cc8: ldur            x0, [x1, #7]
    // 0x1409ccc: cmp             x2, x0
    // 0x1409cd0: b.ne            #0x1409f10
    // 0x1409cd4: ldur            x0, [fp, #-8]
    // 0x1409cd8: LoadField: r1 = r0->field_f
    //     0x1409cd8: ldur            w1, [x0, #0xf]
    // 0x1409cdc: DecompressPointer r1
    //     0x1409cdc: add             x1, x1, HEAP, lsl #32
    // 0x1409ce0: r0 = controller()
    //     0x1409ce0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1409ce4: LoadField: r1 = r0->field_97
    //     0x1409ce4: ldur            w1, [x0, #0x97]
    // 0x1409ce8: DecompressPointer r1
    //     0x1409ce8: add             x1, x1, HEAP, lsl #32
    // 0x1409cec: r0 = value()
    //     0x1409cec: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1409cf0: r1 = LoadClassIdInstr(r0)
    //     0x1409cf0: ldur            x1, [x0, #-1]
    //     0x1409cf4: ubfx            x1, x1, #0xc, #0x14
    // 0x1409cf8: str             x0, [SP]
    // 0x1409cfc: mov             x0, x1
    // 0x1409d00: r0 = GDT[cid_x0 + 0xc898]()
    //     0x1409d00: movz            x17, #0xc898
    //     0x1409d04: add             lr, x0, x17
    //     0x1409d08: ldr             lr, [x21, lr, lsl #3]
    //     0x1409d0c: blr             lr
    // 0x1409d10: r1 = LoadInt32Instr(r0)
    //     0x1409d10: sbfx            x1, x0, #1, #0x1f
    //     0x1409d14: tbz             w0, #0, #0x1409d1c
    //     0x1409d18: ldur            x1, [x0, #7]
    // 0x1409d1c: cmp             x1, #4
    // 0x1409d20: b.ge            #0x1409ef8
    // 0x1409d24: r1 = Instance_Color
    //     0x1409d24: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1409d28: d0 = 0.050000
    //     0x1409d28: ldr             d0, [PP, #0x5780]  ; [pp+0x5780] IMM: double(0.05) from 0x3fa999999999999a
    // 0x1409d2c: r0 = withOpacity()
    //     0x1409d2c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1409d30: stur            x0, [fp, #-0x18]
    // 0x1409d34: r0 = Radius()
    //     0x1409d34: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1409d38: d0 = 15.000000
    //     0x1409d38: fmov            d0, #15.00000000
    // 0x1409d3c: stur            x0, [fp, #-0x20]
    // 0x1409d40: StoreField: r0->field_7 = d0
    //     0x1409d40: stur            d0, [x0, #7]
    // 0x1409d44: StoreField: r0->field_f = d0
    //     0x1409d44: stur            d0, [x0, #0xf]
    // 0x1409d48: r0 = BorderRadius()
    //     0x1409d48: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1409d4c: mov             x1, x0
    // 0x1409d50: ldur            x0, [fp, #-0x20]
    // 0x1409d54: stur            x1, [fp, #-0x28]
    // 0x1409d58: StoreField: r1->field_7 = r0
    //     0x1409d58: stur            w0, [x1, #7]
    // 0x1409d5c: StoreField: r1->field_b = r0
    //     0x1409d5c: stur            w0, [x1, #0xb]
    // 0x1409d60: StoreField: r1->field_f = r0
    //     0x1409d60: stur            w0, [x1, #0xf]
    // 0x1409d64: StoreField: r1->field_13 = r0
    //     0x1409d64: stur            w0, [x1, #0x13]
    // 0x1409d68: r0 = BoxDecoration()
    //     0x1409d68: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1409d6c: mov             x2, x0
    // 0x1409d70: ldur            x0, [fp, #-0x18]
    // 0x1409d74: stur            x2, [fp, #-0x20]
    // 0x1409d78: StoreField: r2->field_7 = r0
    //     0x1409d78: stur            w0, [x2, #7]
    // 0x1409d7c: ldur            x0, [fp, #-0x28]
    // 0x1409d80: StoreField: r2->field_13 = r0
    //     0x1409d80: stur            w0, [x2, #0x13]
    // 0x1409d84: r0 = Instance_BoxShape
    //     0x1409d84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1409d88: ldr             x0, [x0, #0x80]
    // 0x1409d8c: StoreField: r2->field_23 = r0
    //     0x1409d8c: stur            w0, [x2, #0x23]
    // 0x1409d90: r1 = Instance_MaterialColor
    //     0x1409d90: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x1409d94: ldr             x1, [x1, #0xdc0]
    // 0x1409d98: d0 = 0.500000
    //     0x1409d98: fmov            d0, #0.50000000
    // 0x1409d9c: r0 = withOpacity()
    //     0x1409d9c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1409da0: r1 = Instance_Color
    //     0x1409da0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1409da4: d0 = 0.500000
    //     0x1409da4: fmov            d0, #0.50000000
    // 0x1409da8: stur            x0, [fp, #-0x18]
    // 0x1409dac: r0 = withOpacity()
    //     0x1409dac: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1409db0: stur            x0, [fp, #-0x28]
    // 0x1409db4: r0 = Icon()
    //     0x1409db4: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0x1409db8: mov             x1, x0
    // 0x1409dbc: r0 = Instance_IconData
    //     0x1409dbc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b10] Obj!IconData@d55801
    //     0x1409dc0: ldr             x0, [x0, #0xb10]
    // 0x1409dc4: stur            x1, [fp, #-0x30]
    // 0x1409dc8: StoreField: r1->field_b = r0
    //     0x1409dc8: stur            w0, [x1, #0xb]
    // 0x1409dcc: r0 = 25.000000
    //     0x1409dcc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f098] 25
    //     0x1409dd0: ldr             x0, [x0, #0x98]
    // 0x1409dd4: StoreField: r1->field_f = r0
    //     0x1409dd4: stur            w0, [x1, #0xf]
    // 0x1409dd8: ldur            x0, [fp, #-0x28]
    // 0x1409ddc: StoreField: r1->field_23 = r0
    //     0x1409ddc: stur            w0, [x1, #0x23]
    // 0x1409de0: r0 = Center()
    //     0x1409de0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x1409de4: mov             x1, x0
    // 0x1409de8: r0 = Instance_Alignment
    //     0x1409de8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1409dec: ldr             x0, [x0, #0xb10]
    // 0x1409df0: stur            x1, [fp, #-0x28]
    // 0x1409df4: StoreField: r1->field_f = r0
    //     0x1409df4: stur            w0, [x1, #0xf]
    // 0x1409df8: ldur            x0, [fp, #-0x30]
    // 0x1409dfc: StoreField: r1->field_b = r0
    //     0x1409dfc: stur            w0, [x1, #0xb]
    // 0x1409e00: r0 = DottedBorder()
    //     0x1409e00: bl              #0x9f8894  ; AllocateDottedBorderStub -> DottedBorder (size=0x3c)
    // 0x1409e04: stur            x0, [fp, #-0x30]
    // 0x1409e08: r16 = Instance_BorderType
    //     0x1409e08: add             x16, PP, #0x40, lsl #12  ; [pp+0x40078] Obj!BorderType@d750a1
    //     0x1409e0c: ldr             x16, [x16, #0x78]
    // 0x1409e10: r30 = Instance_Radius
    //     0x1409e10: add             lr, PP, #0x40, lsl #12  ; [pp+0x40080] Obj!Radius@d6bee1
    //     0x1409e14: ldr             lr, [lr, #0x80]
    // 0x1409e18: stp             lr, x16, [SP]
    // 0x1409e1c: mov             x1, x0
    // 0x1409e20: ldur            x2, [fp, #-0x28]
    // 0x1409e24: ldur            x3, [fp, #-0x18]
    // 0x1409e28: r5 = const [5.0, 5.0]
    //     0x1409e28: add             x5, PP, #0x36, lsl #12  ; [pp+0x36ab0] List<double>(2)
    //     0x1409e2c: ldr             x5, [x5, #0xab0]
    // 0x1409e30: d0 = 1.000000
    //     0x1409e30: fmov            d0, #1.00000000
    // 0x1409e34: r4 = const [0, 0x7, 0x2, 0x5, borderType, 0x5, radius, 0x6, null]
    //     0x1409e34: add             x4, PP, #0x40, lsl #12  ; [pp+0x40088] List(9) [0, 0x7, 0x2, 0x5, "borderType", 0x5, "radius", 0x6, Null]
    //     0x1409e38: ldr             x4, [x4, #0x88]
    // 0x1409e3c: r0 = DottedBorder()
    //     0x1409e3c: bl              #0x9f8704  ; [package:dotted_border/dotted_border.dart] DottedBorder::DottedBorder
    // 0x1409e40: r0 = Container()
    //     0x1409e40: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1409e44: stur            x0, [fp, #-0x18]
    // 0x1409e48: r16 = 57.000000
    //     0x1409e48: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x1409e4c: ldr             x16, [x16, #0xb18]
    // 0x1409e50: r30 = 57.000000
    //     0x1409e50: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x1409e54: ldr             lr, [lr, #0xb18]
    // 0x1409e58: stp             lr, x16, [SP, #0x10]
    // 0x1409e5c: ldur            x16, [fp, #-0x20]
    // 0x1409e60: ldur            lr, [fp, #-0x30]
    // 0x1409e64: stp             lr, x16, [SP]
    // 0x1409e68: mov             x1, x0
    // 0x1409e6c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0x1409e6c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0x1409e70: ldr             x4, [x4, #0x8c0]
    // 0x1409e74: r0 = Container()
    //     0x1409e74: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1409e78: r0 = Padding()
    //     0x1409e78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1409e7c: r3 = Instance_EdgeInsets
    //     0x1409e7c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x1409e80: ldr             x3, [x3, #0xdf8]
    // 0x1409e84: stur            x0, [fp, #-0x20]
    // 0x1409e88: StoreField: r0->field_f = r3
    //     0x1409e88: stur            w3, [x0, #0xf]
    // 0x1409e8c: ldur            x1, [fp, #-0x18]
    // 0x1409e90: StoreField: r0->field_b = r1
    //     0x1409e90: stur            w1, [x0, #0xb]
    // 0x1409e94: r0 = InkWell()
    //     0x1409e94: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x1409e98: mov             x3, x0
    // 0x1409e9c: ldur            x0, [fp, #-0x20]
    // 0x1409ea0: stur            x3, [fp, #-0x18]
    // 0x1409ea4: StoreField: r3->field_b = r0
    //     0x1409ea4: stur            w0, [x3, #0xb]
    // 0x1409ea8: ldur            x2, [fp, #-0x10]
    // 0x1409eac: r1 = Function '<anonymous closure>':.
    //     0x1409eac: add             x1, PP, #0x40, lsl #12  ; [pp+0x400b0] AnonymousClosure: (0x140bcc4), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x1409eb0: ldr             x1, [x1, #0xb0]
    // 0x1409eb4: r0 = AllocateClosure()
    //     0x1409eb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1409eb8: mov             x1, x0
    // 0x1409ebc: ldur            x0, [fp, #-0x18]
    // 0x1409ec0: StoreField: r0->field_f = r1
    //     0x1409ec0: stur            w1, [x0, #0xf]
    // 0x1409ec4: r1 = true
    //     0x1409ec4: add             x1, NULL, #0x20  ; true
    // 0x1409ec8: StoreField: r0->field_43 = r1
    //     0x1409ec8: stur            w1, [x0, #0x43]
    // 0x1409ecc: r2 = Instance_BoxShape
    //     0x1409ecc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1409ed0: ldr             x2, [x2, #0x80]
    // 0x1409ed4: StoreField: r0->field_47 = r2
    //     0x1409ed4: stur            w2, [x0, #0x47]
    // 0x1409ed8: StoreField: r0->field_6f = r1
    //     0x1409ed8: stur            w1, [x0, #0x6f]
    // 0x1409edc: r2 = false
    //     0x1409edc: add             x2, NULL, #0x30  ; false
    // 0x1409ee0: StoreField: r0->field_73 = r2
    //     0x1409ee0: stur            w2, [x0, #0x73]
    // 0x1409ee4: StoreField: r0->field_83 = r1
    //     0x1409ee4: stur            w1, [x0, #0x83]
    // 0x1409ee8: StoreField: r0->field_7b = r2
    //     0x1409ee8: stur            w2, [x0, #0x7b]
    // 0x1409eec: LeaveFrame
    //     0x1409eec: mov             SP, fp
    //     0x1409ef0: ldp             fp, lr, [SP], #0x10
    // 0x1409ef4: ret
    //     0x1409ef4: ret             
    // 0x1409ef8: r3 = Instance_EdgeInsets
    //     0x1409ef8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x1409efc: ldr             x3, [x3, #0xdf8]
    // 0x1409f00: r0 = Instance_Alignment
    //     0x1409f00: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1409f04: ldr             x0, [x0, #0xb10]
    // 0x1409f08: d0 = 15.000000
    //     0x1409f08: fmov            d0, #15.00000000
    // 0x1409f0c: b               #0x1409f24
    // 0x1409f10: r3 = Instance_EdgeInsets
    //     0x1409f10: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x1409f14: ldr             x3, [x3, #0xdf8]
    // 0x1409f18: r0 = Instance_Alignment
    //     0x1409f18: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x1409f1c: ldr             x0, [x0, #0xb10]
    // 0x1409f20: d0 = 15.000000
    //     0x1409f20: fmov            d0, #15.00000000
    // 0x1409f24: ldur            x4, [fp, #-8]
    // 0x1409f28: ldur            x5, [fp, #-0x10]
    // 0x1409f2c: r1 = <Widget>
    //     0x1409f2c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1409f30: r2 = 0
    //     0x1409f30: movz            x2, #0
    // 0x1409f34: r0 = _GrowableList()
    //     0x1409f34: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x1409f38: mov             x2, x0
    // 0x1409f3c: ldur            x0, [fp, #-8]
    // 0x1409f40: stur            x2, [fp, #-0x18]
    // 0x1409f44: LoadField: r1 = r0->field_f
    //     0x1409f44: ldur            w1, [x0, #0xf]
    // 0x1409f48: DecompressPointer r1
    //     0x1409f48: add             x1, x1, HEAP, lsl #32
    // 0x1409f4c: r0 = controller()
    //     0x1409f4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1409f50: LoadField: r1 = r0->field_97
    //     0x1409f50: ldur            w1, [x0, #0x97]
    // 0x1409f54: DecompressPointer r1
    //     0x1409f54: add             x1, x1, HEAP, lsl #32
    // 0x1409f58: ldur            x2, [fp, #-0x10]
    // 0x1409f5c: LoadField: r0 = r2->field_f
    //     0x1409f5c: ldur            w0, [x2, #0xf]
    // 0x1409f60: DecompressPointer r0
    //     0x1409f60: add             x0, x0, HEAP, lsl #32
    // 0x1409f64: stur            x0, [fp, #-0x20]
    // 0x1409f68: r0 = value()
    //     0x1409f68: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x1409f6c: r1 = LoadClassIdInstr(r0)
    //     0x1409f6c: ldur            x1, [x0, #-1]
    //     0x1409f70: ubfx            x1, x1, #0xc, #0x14
    // 0x1409f74: ldur            x16, [fp, #-0x20]
    // 0x1409f78: stp             x16, x0, [SP]
    // 0x1409f7c: mov             x0, x1
    // 0x1409f80: r0 = GDT[cid_x0 + -0xb7]()
    //     0x1409f80: sub             lr, x0, #0xb7
    //     0x1409f84: ldr             lr, [x21, lr, lsl #3]
    //     0x1409f88: blr             lr
    // 0x1409f8c: LoadField: r1 = r0->field_f
    //     0x1409f8c: ldur            w1, [x0, #0xf]
    // 0x1409f90: DecompressPointer r1
    //     0x1409f90: add             x1, x1, HEAP, lsl #32
    // 0x1409f94: cmp             w1, NULL
    // 0x1409f98: b.ne            #0x140a310
    // 0x1409f9c: ldur            x0, [fp, #-8]
    // 0x1409fa0: ldur            x2, [fp, #-0x10]
    // 0x1409fa4: r0 = Radius()
    //     0x1409fa4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1409fa8: d0 = 15.000000
    //     0x1409fa8: fmov            d0, #15.00000000
    // 0x1409fac: stur            x0, [fp, #-0x20]
    // 0x1409fb0: StoreField: r0->field_7 = d0
    //     0x1409fb0: stur            d0, [x0, #7]
    // 0x1409fb4: StoreField: r0->field_f = d0
    //     0x1409fb4: stur            d0, [x0, #0xf]
    // 0x1409fb8: r0 = BorderRadius()
    //     0x1409fb8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1409fbc: mov             x2, x0
    // 0x1409fc0: ldur            x0, [fp, #-0x20]
    // 0x1409fc4: stur            x2, [fp, #-0x28]
    // 0x1409fc8: StoreField: r2->field_7 = r0
    //     0x1409fc8: stur            w0, [x2, #7]
    // 0x1409fcc: StoreField: r2->field_b = r0
    //     0x1409fcc: stur            w0, [x2, #0xb]
    // 0x1409fd0: StoreField: r2->field_f = r0
    //     0x1409fd0: stur            w0, [x2, #0xf]
    // 0x1409fd4: StoreField: r2->field_13 = r0
    //     0x1409fd4: stur            w0, [x2, #0x13]
    // 0x1409fd8: ldur            x0, [fp, #-8]
    // 0x1409fdc: LoadField: r1 = r0->field_f
    //     0x1409fdc: ldur            w1, [x0, #0xf]
    // 0x1409fe0: DecompressPointer r1
    //     0x1409fe0: add             x1, x1, HEAP, lsl #32
    // 0x1409fe4: r0 = controller()
    //     0x1409fe4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1409fe8: LoadField: r1 = r0->field_97
    //     0x1409fe8: ldur            w1, [x0, #0x97]
    // 0x1409fec: DecompressPointer r1
    //     0x1409fec: add             x1, x1, HEAP, lsl #32
    // 0x1409ff0: ldur            x2, [fp, #-0x10]
    // 0x1409ff4: LoadField: r0 = r2->field_f
    //     0x1409ff4: ldur            w0, [x2, #0xf]
    // 0x1409ff8: DecompressPointer r0
    //     0x1409ff8: add             x0, x0, HEAP, lsl #32
    // 0x1409ffc: stur            x0, [fp, #-0x20]
    // 0x140a000: r0 = value()
    //     0x140a000: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a004: r1 = LoadClassIdInstr(r0)
    //     0x140a004: ldur            x1, [x0, #-1]
    //     0x140a008: ubfx            x1, x1, #0xc, #0x14
    // 0x140a00c: ldur            x16, [fp, #-0x20]
    // 0x140a010: stp             x16, x0, [SP]
    // 0x140a014: mov             x0, x1
    // 0x140a018: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a018: sub             lr, x0, #0xb7
    //     0x140a01c: ldr             lr, [x21, lr, lsl #3]
    //     0x140a020: blr             lr
    // 0x140a024: LoadField: r1 = r0->field_13
    //     0x140a024: ldur            w1, [x0, #0x13]
    // 0x140a028: DecompressPointer r1
    //     0x140a028: add             x1, x1, HEAP, lsl #32
    // 0x140a02c: cmp             w1, NULL
    // 0x140a030: b.ne            #0x140a040
    // 0x140a034: r4 = 0
    //     0x140a034: movz            x4, #0
    // 0x140a038: r0 = AllocateUint8Array()
    //     0x140a038: bl              #0x16f6e7c  ; AllocateUint8ArrayStub
    // 0x140a03c: mov             x1, x0
    // 0x140a040: ldur            x0, [fp, #-8]
    // 0x140a044: ldur            x2, [fp, #-0x10]
    // 0x140a048: stur            x1, [fp, #-0x20]
    // 0x140a04c: r0 = Image()
    //     0x140a04c: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x140a050: mov             x1, x0
    // 0x140a054: ldur            x2, [fp, #-0x20]
    // 0x140a058: d0 = 57.000000
    //     0x140a058: add             x17, PP, #0x36, lsl #12  ; [pp+0x36b28] IMM: double(57) from 0x404c800000000000
    //     0x140a05c: ldr             d0, [x17, #0xb28]
    // 0x140a060: d1 = 57.000000
    //     0x140a060: add             x17, PP, #0x36, lsl #12  ; [pp+0x36b28] IMM: double(57) from 0x404c800000000000
    //     0x140a064: ldr             d1, [x17, #0xb28]
    // 0x140a068: stur            x0, [fp, #-0x20]
    // 0x140a06c: r0 = Image.memory()
    //     0x140a06c: bl              #0x9a52d0  ; [package:flutter/src/widgets/image.dart] Image::Image.memory
    // 0x140a070: r1 = Null
    //     0x140a070: mov             x1, NULL
    // 0x140a074: r2 = 2
    //     0x140a074: movz            x2, #0x2
    // 0x140a078: r0 = AllocateArray()
    //     0x140a078: bl              #0x16f7198  ; AllocateArrayStub
    // 0x140a07c: mov             x2, x0
    // 0x140a080: ldur            x0, [fp, #-0x20]
    // 0x140a084: stur            x2, [fp, #-0x30]
    // 0x140a088: StoreField: r2->field_f = r0
    //     0x140a088: stur            w0, [x2, #0xf]
    // 0x140a08c: r1 = <Widget>
    //     0x140a08c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x140a090: r0 = AllocateGrowableArray()
    //     0x140a090: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x140a094: mov             x2, x0
    // 0x140a098: ldur            x0, [fp, #-0x30]
    // 0x140a09c: stur            x2, [fp, #-0x20]
    // 0x140a0a0: StoreField: r2->field_f = r0
    //     0x140a0a0: stur            w0, [x2, #0xf]
    // 0x140a0a4: r0 = 2
    //     0x140a0a4: movz            x0, #0x2
    // 0x140a0a8: StoreField: r2->field_b = r0
    //     0x140a0a8: stur            w0, [x2, #0xb]
    // 0x140a0ac: ldur            x0, [fp, #-8]
    // 0x140a0b0: LoadField: r1 = r0->field_f
    //     0x140a0b0: ldur            w1, [x0, #0xf]
    // 0x140a0b4: DecompressPointer r1
    //     0x140a0b4: add             x1, x1, HEAP, lsl #32
    // 0x140a0b8: r0 = controller()
    //     0x140a0b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a0bc: LoadField: r1 = r0->field_97
    //     0x140a0bc: ldur            w1, [x0, #0x97]
    // 0x140a0c0: DecompressPointer r1
    //     0x140a0c0: add             x1, x1, HEAP, lsl #32
    // 0x140a0c4: ldur            x2, [fp, #-0x10]
    // 0x140a0c8: LoadField: r0 = r2->field_f
    //     0x140a0c8: ldur            w0, [x2, #0xf]
    // 0x140a0cc: DecompressPointer r0
    //     0x140a0cc: add             x0, x0, HEAP, lsl #32
    // 0x140a0d0: stur            x0, [fp, #-0x30]
    // 0x140a0d4: r0 = value()
    //     0x140a0d4: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a0d8: r1 = LoadClassIdInstr(r0)
    //     0x140a0d8: ldur            x1, [x0, #-1]
    //     0x140a0dc: ubfx            x1, x1, #0xc, #0x14
    // 0x140a0e0: ldur            x16, [fp, #-0x30]
    // 0x140a0e4: stp             x16, x0, [SP]
    // 0x140a0e8: mov             x0, x1
    // 0x140a0ec: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a0ec: sub             lr, x0, #0xb7
    //     0x140a0f0: ldr             lr, [x21, lr, lsl #3]
    //     0x140a0f4: blr             lr
    // 0x140a0f8: LoadField: r1 = r0->field_b
    //     0x140a0f8: ldur            w1, [x0, #0xb]
    // 0x140a0fc: DecompressPointer r1
    //     0x140a0fc: add             x1, x1, HEAP, lsl #32
    // 0x140a100: r0 = LoadClassIdInstr(r1)
    //     0x140a100: ldur            x0, [x1, #-1]
    //     0x140a104: ubfx            x0, x0, #0xc, #0x14
    // 0x140a108: r16 = "video_local"
    //     0x140a108: add             x16, PP, #0x36, lsl #12  ; [pp+0x369c0] "video_local"
    //     0x140a10c: ldr             x16, [x16, #0x9c0]
    // 0x140a110: stp             x16, x1, [SP]
    // 0x140a114: mov             lr, x0
    // 0x140a118: ldr             lr, [x21, lr, lsl #3]
    // 0x140a11c: blr             lr
    // 0x140a120: tbnz            w0, #4, #0x140a228
    // 0x140a124: ldur            x0, [fp, #-0x20]
    // 0x140a128: r1 = Instance_Color
    //     0x140a128: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x140a12c: d0 = 0.300000
    //     0x140a12c: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x140a130: ldr             d0, [x17, #0x658]
    // 0x140a134: r0 = withOpacity()
    //     0x140a134: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x140a138: stur            x0, [fp, #-0x30]
    // 0x140a13c: r0 = BoxDecoration()
    //     0x140a13c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x140a140: mov             x1, x0
    // 0x140a144: ldur            x0, [fp, #-0x30]
    // 0x140a148: stur            x1, [fp, #-0x38]
    // 0x140a14c: StoreField: r1->field_7 = r0
    //     0x140a14c: stur            w0, [x1, #7]
    // 0x140a150: r0 = Instance_BoxShape
    //     0x140a150: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x140a154: ldr             x0, [x0, #0x970]
    // 0x140a158: StoreField: r1->field_23 = r0
    //     0x140a158: stur            w0, [x1, #0x23]
    // 0x140a15c: r0 = Container()
    //     0x140a15c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x140a160: stur            x0, [fp, #-0x30]
    // 0x140a164: ldur            x16, [fp, #-0x38]
    // 0x140a168: r30 = Instance_EdgeInsets
    //     0x140a168: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b30] Obj!EdgeInsets@d57711
    //     0x140a16c: ldr             lr, [lr, #0xb30]
    // 0x140a170: stp             lr, x16, [SP, #8]
    // 0x140a174: r16 = Instance_Icon
    //     0x140a174: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b38] Obj!Icon@d66431
    //     0x140a178: ldr             x16, [x16, #0xb38]
    // 0x140a17c: str             x16, [SP]
    // 0x140a180: mov             x1, x0
    // 0x140a184: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0x140a184: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0x140a188: ldr             x4, [x4, #0xb40]
    // 0x140a18c: r0 = Container()
    //     0x140a18c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x140a190: r1 = <StackParentData>
    //     0x140a190: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x140a194: ldr             x1, [x1, #0x8e0]
    // 0x140a198: r0 = Positioned()
    //     0x140a198: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x140a19c: mov             x2, x0
    // 0x140a1a0: ldur            x0, [fp, #-0x30]
    // 0x140a1a4: stur            x2, [fp, #-0x38]
    // 0x140a1a8: StoreField: r2->field_b = r0
    //     0x140a1a8: stur            w0, [x2, #0xb]
    // 0x140a1ac: ldur            x0, [fp, #-0x20]
    // 0x140a1b0: LoadField: r1 = r0->field_b
    //     0x140a1b0: ldur            w1, [x0, #0xb]
    // 0x140a1b4: LoadField: r3 = r0->field_f
    //     0x140a1b4: ldur            w3, [x0, #0xf]
    // 0x140a1b8: DecompressPointer r3
    //     0x140a1b8: add             x3, x3, HEAP, lsl #32
    // 0x140a1bc: LoadField: r4 = r3->field_b
    //     0x140a1bc: ldur            w4, [x3, #0xb]
    // 0x140a1c0: r3 = LoadInt32Instr(r1)
    //     0x140a1c0: sbfx            x3, x1, #1, #0x1f
    // 0x140a1c4: stur            x3, [fp, #-0x40]
    // 0x140a1c8: r1 = LoadInt32Instr(r4)
    //     0x140a1c8: sbfx            x1, x4, #1, #0x1f
    // 0x140a1cc: cmp             x3, x1
    // 0x140a1d0: b.ne            #0x140a1dc
    // 0x140a1d4: mov             x1, x0
    // 0x140a1d8: r0 = _growToNextCapacity()
    //     0x140a1d8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140a1dc: ldur            x2, [fp, #-0x20]
    // 0x140a1e0: ldur            x3, [fp, #-0x40]
    // 0x140a1e4: add             x0, x3, #1
    // 0x140a1e8: lsl             x1, x0, #1
    // 0x140a1ec: StoreField: r2->field_b = r1
    //     0x140a1ec: stur            w1, [x2, #0xb]
    // 0x140a1f0: LoadField: r1 = r2->field_f
    //     0x140a1f0: ldur            w1, [x2, #0xf]
    // 0x140a1f4: DecompressPointer r1
    //     0x140a1f4: add             x1, x1, HEAP, lsl #32
    // 0x140a1f8: ldur            x0, [fp, #-0x38]
    // 0x140a1fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140a1fc: add             x25, x1, x3, lsl #2
    //     0x140a200: add             x25, x25, #0xf
    //     0x140a204: str             w0, [x25]
    //     0x140a208: tbz             w0, #0, #0x140a224
    //     0x140a20c: ldurb           w16, [x1, #-1]
    //     0x140a210: ldurb           w17, [x0, #-1]
    //     0x140a214: and             x16, x17, x16, lsr #2
    //     0x140a218: tst             x16, HEAP, lsr #32
    //     0x140a21c: b.eq            #0x140a224
    //     0x140a220: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140a224: b               #0x140a22c
    // 0x140a228: ldur            x2, [fp, #-0x20]
    // 0x140a22c: ldur            x1, [fp, #-0x18]
    // 0x140a230: ldur            x0, [fp, #-0x28]
    // 0x140a234: r0 = Stack()
    //     0x140a234: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x140a238: mov             x1, x0
    // 0x140a23c: r0 = Instance_Alignment
    //     0x140a23c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x140a240: ldr             x0, [x0, #0xb10]
    // 0x140a244: stur            x1, [fp, #-0x30]
    // 0x140a248: StoreField: r1->field_f = r0
    //     0x140a248: stur            w0, [x1, #0xf]
    // 0x140a24c: r0 = Instance_StackFit
    //     0x140a24c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x140a250: ldr             x0, [x0, #0xfa8]
    // 0x140a254: ArrayStore: r1[0] = r0  ; List_4
    //     0x140a254: stur            w0, [x1, #0x17]
    // 0x140a258: r2 = Instance_Clip
    //     0x140a258: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x140a25c: ldr             x2, [x2, #0x7e0]
    // 0x140a260: StoreField: r1->field_1b = r2
    //     0x140a260: stur            w2, [x1, #0x1b]
    // 0x140a264: ldur            x3, [fp, #-0x20]
    // 0x140a268: StoreField: r1->field_b = r3
    //     0x140a268: stur            w3, [x1, #0xb]
    // 0x140a26c: r0 = ClipRRect()
    //     0x140a26c: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x140a270: mov             x2, x0
    // 0x140a274: ldur            x0, [fp, #-0x28]
    // 0x140a278: stur            x2, [fp, #-0x20]
    // 0x140a27c: StoreField: r2->field_f = r0
    //     0x140a27c: stur            w0, [x2, #0xf]
    // 0x140a280: r0 = Instance_Clip
    //     0x140a280: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x140a284: ldr             x0, [x0, #0x138]
    // 0x140a288: ArrayStore: r2[0] = r0  ; List_4
    //     0x140a288: stur            w0, [x2, #0x17]
    // 0x140a28c: ldur            x1, [fp, #-0x30]
    // 0x140a290: StoreField: r2->field_b = r1
    //     0x140a290: stur            w1, [x2, #0xb]
    // 0x140a294: ldur            x3, [fp, #-0x18]
    // 0x140a298: LoadField: r1 = r3->field_b
    //     0x140a298: ldur            w1, [x3, #0xb]
    // 0x140a29c: LoadField: r4 = r3->field_f
    //     0x140a29c: ldur            w4, [x3, #0xf]
    // 0x140a2a0: DecompressPointer r4
    //     0x140a2a0: add             x4, x4, HEAP, lsl #32
    // 0x140a2a4: LoadField: r5 = r4->field_b
    //     0x140a2a4: ldur            w5, [x4, #0xb]
    // 0x140a2a8: r4 = LoadInt32Instr(r1)
    //     0x140a2a8: sbfx            x4, x1, #1, #0x1f
    // 0x140a2ac: stur            x4, [fp, #-0x40]
    // 0x140a2b0: r1 = LoadInt32Instr(r5)
    //     0x140a2b0: sbfx            x1, x5, #1, #0x1f
    // 0x140a2b4: cmp             x4, x1
    // 0x140a2b8: b.ne            #0x140a2c4
    // 0x140a2bc: mov             x1, x3
    // 0x140a2c0: r0 = _growToNextCapacity()
    //     0x140a2c0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140a2c4: ldur            x2, [fp, #-0x18]
    // 0x140a2c8: ldur            x3, [fp, #-0x40]
    // 0x140a2cc: add             x0, x3, #1
    // 0x140a2d0: lsl             x1, x0, #1
    // 0x140a2d4: StoreField: r2->field_b = r1
    //     0x140a2d4: stur            w1, [x2, #0xb]
    // 0x140a2d8: LoadField: r1 = r2->field_f
    //     0x140a2d8: ldur            w1, [x2, #0xf]
    // 0x140a2dc: DecompressPointer r1
    //     0x140a2dc: add             x1, x1, HEAP, lsl #32
    // 0x140a2e0: ldur            x0, [fp, #-0x20]
    // 0x140a2e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140a2e4: add             x25, x1, x3, lsl #2
    //     0x140a2e8: add             x25, x25, #0xf
    //     0x140a2ec: str             w0, [x25]
    //     0x140a2f0: tbz             w0, #0, #0x140a30c
    //     0x140a2f4: ldurb           w16, [x1, #-1]
    //     0x140a2f8: ldurb           w17, [x0, #-1]
    //     0x140a2fc: and             x16, x17, x16, lsr #2
    //     0x140a300: tst             x16, HEAP, lsr #32
    //     0x140a304: b.eq            #0x140a30c
    //     0x140a308: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140a30c: b               #0x140a314
    // 0x140a310: ldur            x2, [fp, #-0x18]
    // 0x140a314: ldur            x3, [fp, #-8]
    // 0x140a318: ldur            x0, [fp, #-0x10]
    // 0x140a31c: LoadField: r1 = r3->field_f
    //     0x140a31c: ldur            w1, [x3, #0xf]
    // 0x140a320: DecompressPointer r1
    //     0x140a320: add             x1, x1, HEAP, lsl #32
    // 0x140a324: r0 = controller()
    //     0x140a324: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a328: LoadField: r1 = r0->field_97
    //     0x140a328: ldur            w1, [x0, #0x97]
    // 0x140a32c: DecompressPointer r1
    //     0x140a32c: add             x1, x1, HEAP, lsl #32
    // 0x140a330: ldur            x2, [fp, #-0x10]
    // 0x140a334: LoadField: r0 = r2->field_f
    //     0x140a334: ldur            w0, [x2, #0xf]
    // 0x140a338: DecompressPointer r0
    //     0x140a338: add             x0, x0, HEAP, lsl #32
    // 0x140a33c: stur            x0, [fp, #-0x20]
    // 0x140a340: r0 = value()
    //     0x140a340: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a344: r1 = LoadClassIdInstr(r0)
    //     0x140a344: ldur            x1, [x0, #-1]
    //     0x140a348: ubfx            x1, x1, #0xc, #0x14
    // 0x140a34c: ldur            x16, [fp, #-0x20]
    // 0x140a350: stp             x16, x0, [SP]
    // 0x140a354: mov             x0, x1
    // 0x140a358: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a358: sub             lr, x0, #0xb7
    //     0x140a35c: ldr             lr, [x21, lr, lsl #3]
    //     0x140a360: blr             lr
    // 0x140a364: LoadField: r1 = r0->field_f
    //     0x140a364: ldur            w1, [x0, #0xf]
    // 0x140a368: DecompressPointer r1
    //     0x140a368: add             x1, x1, HEAP, lsl #32
    // 0x140a36c: cmp             w1, NULL
    // 0x140a370: b.eq            #0x140a4fc
    // 0x140a374: ldur            x0, [fp, #-8]
    // 0x140a378: ldur            x2, [fp, #-0x10]
    // 0x140a37c: LoadField: r1 = r0->field_f
    //     0x140a37c: ldur            w1, [x0, #0xf]
    // 0x140a380: DecompressPointer r1
    //     0x140a380: add             x1, x1, HEAP, lsl #32
    // 0x140a384: r0 = controller()
    //     0x140a384: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a388: LoadField: r1 = r0->field_97
    //     0x140a388: ldur            w1, [x0, #0x97]
    // 0x140a38c: DecompressPointer r1
    //     0x140a38c: add             x1, x1, HEAP, lsl #32
    // 0x140a390: ldur            x2, [fp, #-0x10]
    // 0x140a394: LoadField: r0 = r2->field_f
    //     0x140a394: ldur            w0, [x2, #0xf]
    // 0x140a398: DecompressPointer r0
    //     0x140a398: add             x0, x0, HEAP, lsl #32
    // 0x140a39c: stur            x0, [fp, #-0x20]
    // 0x140a3a0: r0 = value()
    //     0x140a3a0: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a3a4: r1 = LoadClassIdInstr(r0)
    //     0x140a3a4: ldur            x1, [x0, #-1]
    //     0x140a3a8: ubfx            x1, x1, #0xc, #0x14
    // 0x140a3ac: ldur            x16, [fp, #-0x20]
    // 0x140a3b0: stp             x16, x0, [SP]
    // 0x140a3b4: mov             x0, x1
    // 0x140a3b8: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a3b8: sub             lr, x0, #0xb7
    //     0x140a3bc: ldr             lr, [x21, lr, lsl #3]
    //     0x140a3c0: blr             lr
    // 0x140a3c4: LoadField: r1 = r0->field_b
    //     0x140a3c4: ldur            w1, [x0, #0xb]
    // 0x140a3c8: DecompressPointer r1
    //     0x140a3c8: add             x1, x1, HEAP, lsl #32
    // 0x140a3cc: r0 = LoadClassIdInstr(r1)
    //     0x140a3cc: ldur            x0, [x1, #-1]
    //     0x140a3d0: ubfx            x0, x0, #0xc, #0x14
    // 0x140a3d4: r16 = "video"
    //     0x140a3d4: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb50] "video"
    //     0x140a3d8: ldr             x16, [x16, #0xb50]
    // 0x140a3dc: stp             x16, x1, [SP]
    // 0x140a3e0: mov             lr, x0
    // 0x140a3e4: ldr             lr, [x21, lr, lsl #3]
    // 0x140a3e8: blr             lr
    // 0x140a3ec: tbnz            w0, #4, #0x140a4f4
    // 0x140a3f0: ldur            x0, [fp, #-8]
    // 0x140a3f4: ldur            x2, [fp, #-0x10]
    // 0x140a3f8: LoadField: r1 = r0->field_f
    //     0x140a3f8: ldur            w1, [x0, #0xf]
    // 0x140a3fc: DecompressPointer r1
    //     0x140a3fc: add             x1, x1, HEAP, lsl #32
    // 0x140a400: r0 = controller()
    //     0x140a400: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a404: LoadField: r1 = r0->field_97
    //     0x140a404: ldur            w1, [x0, #0x97]
    // 0x140a408: DecompressPointer r1
    //     0x140a408: add             x1, x1, HEAP, lsl #32
    // 0x140a40c: ldur            x2, [fp, #-0x10]
    // 0x140a410: LoadField: r0 = r2->field_f
    //     0x140a410: ldur            w0, [x2, #0xf]
    // 0x140a414: DecompressPointer r0
    //     0x140a414: add             x0, x0, HEAP, lsl #32
    // 0x140a418: stur            x0, [fp, #-0x20]
    // 0x140a41c: r0 = value()
    //     0x140a41c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a420: r1 = LoadClassIdInstr(r0)
    //     0x140a420: ldur            x1, [x0, #-1]
    //     0x140a424: ubfx            x1, x1, #0xc, #0x14
    // 0x140a428: ldur            x16, [fp, #-0x20]
    // 0x140a42c: stp             x16, x0, [SP]
    // 0x140a430: mov             x0, x1
    // 0x140a434: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a434: sub             lr, x0, #0xb7
    //     0x140a438: ldr             lr, [x21, lr, lsl #3]
    //     0x140a43c: blr             lr
    // 0x140a440: LoadField: r1 = r0->field_f
    //     0x140a440: ldur            w1, [x0, #0xf]
    // 0x140a444: DecompressPointer r1
    //     0x140a444: add             x1, x1, HEAP, lsl #32
    // 0x140a448: cmp             w1, NULL
    // 0x140a44c: b.ne            #0x140a458
    // 0x140a450: r0 = ""
    //     0x140a450: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140a454: b               #0x140a45c
    // 0x140a458: mov             x0, x1
    // 0x140a45c: ldur            x1, [fp, #-0x18]
    // 0x140a460: stur            x0, [fp, #-0x20]
    // 0x140a464: r0 = VideoPlayerWidget()
    //     0x140a464: bl              #0xa971e0  ; AllocateVideoPlayerWidgetStub -> VideoPlayerWidget (size=0x14)
    // 0x140a468: mov             x2, x0
    // 0x140a46c: ldur            x0, [fp, #-0x20]
    // 0x140a470: stur            x2, [fp, #-0x28]
    // 0x140a474: StoreField: r2->field_b = r0
    //     0x140a474: stur            w0, [x2, #0xb]
    // 0x140a478: ldur            x0, [fp, #-0x18]
    // 0x140a47c: LoadField: r1 = r0->field_b
    //     0x140a47c: ldur            w1, [x0, #0xb]
    // 0x140a480: LoadField: r3 = r0->field_f
    //     0x140a480: ldur            w3, [x0, #0xf]
    // 0x140a484: DecompressPointer r3
    //     0x140a484: add             x3, x3, HEAP, lsl #32
    // 0x140a488: LoadField: r4 = r3->field_b
    //     0x140a488: ldur            w4, [x3, #0xb]
    // 0x140a48c: r3 = LoadInt32Instr(r1)
    //     0x140a48c: sbfx            x3, x1, #1, #0x1f
    // 0x140a490: stur            x3, [fp, #-0x40]
    // 0x140a494: r1 = LoadInt32Instr(r4)
    //     0x140a494: sbfx            x1, x4, #1, #0x1f
    // 0x140a498: cmp             x3, x1
    // 0x140a49c: b.ne            #0x140a4a8
    // 0x140a4a0: mov             x1, x0
    // 0x140a4a4: r0 = _growToNextCapacity()
    //     0x140a4a4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140a4a8: ldur            x2, [fp, #-0x18]
    // 0x140a4ac: ldur            x3, [fp, #-0x40]
    // 0x140a4b0: add             x0, x3, #1
    // 0x140a4b4: lsl             x1, x0, #1
    // 0x140a4b8: StoreField: r2->field_b = r1
    //     0x140a4b8: stur            w1, [x2, #0xb]
    // 0x140a4bc: LoadField: r1 = r2->field_f
    //     0x140a4bc: ldur            w1, [x2, #0xf]
    // 0x140a4c0: DecompressPointer r1
    //     0x140a4c0: add             x1, x1, HEAP, lsl #32
    // 0x140a4c4: ldur            x0, [fp, #-0x28]
    // 0x140a4c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140a4c8: add             x25, x1, x3, lsl #2
    //     0x140a4cc: add             x25, x25, #0xf
    //     0x140a4d0: str             w0, [x25]
    //     0x140a4d4: tbz             w0, #0, #0x140a4f0
    //     0x140a4d8: ldurb           w16, [x1, #-1]
    //     0x140a4dc: ldurb           w17, [x0, #-1]
    //     0x140a4e0: and             x16, x17, x16, lsr #2
    //     0x140a4e4: tst             x16, HEAP, lsr #32
    //     0x140a4e8: b.eq            #0x140a4f0
    //     0x140a4ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140a4f0: b               #0x140a500
    // 0x140a4f4: ldur            x2, [fp, #-0x18]
    // 0x140a4f8: b               #0x140a500
    // 0x140a4fc: ldur            x2, [fp, #-0x18]
    // 0x140a500: ldur            x3, [fp, #-8]
    // 0x140a504: ldur            x0, [fp, #-0x10]
    // 0x140a508: LoadField: r1 = r3->field_f
    //     0x140a508: ldur            w1, [x3, #0xf]
    // 0x140a50c: DecompressPointer r1
    //     0x140a50c: add             x1, x1, HEAP, lsl #32
    // 0x140a510: r0 = controller()
    //     0x140a510: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a514: LoadField: r1 = r0->field_97
    //     0x140a514: ldur            w1, [x0, #0x97]
    // 0x140a518: DecompressPointer r1
    //     0x140a518: add             x1, x1, HEAP, lsl #32
    // 0x140a51c: ldur            x2, [fp, #-0x10]
    // 0x140a520: LoadField: r0 = r2->field_f
    //     0x140a520: ldur            w0, [x2, #0xf]
    // 0x140a524: DecompressPointer r0
    //     0x140a524: add             x0, x0, HEAP, lsl #32
    // 0x140a528: stur            x0, [fp, #-0x20]
    // 0x140a52c: r0 = value()
    //     0x140a52c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a530: r1 = LoadClassIdInstr(r0)
    //     0x140a530: ldur            x1, [x0, #-1]
    //     0x140a534: ubfx            x1, x1, #0xc, #0x14
    // 0x140a538: ldur            x16, [fp, #-0x20]
    // 0x140a53c: stp             x16, x0, [SP]
    // 0x140a540: mov             x0, x1
    // 0x140a544: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a544: sub             lr, x0, #0xb7
    //     0x140a548: ldr             lr, [x21, lr, lsl #3]
    //     0x140a54c: blr             lr
    // 0x140a550: LoadField: r1 = r0->field_f
    //     0x140a550: ldur            w1, [x0, #0xf]
    // 0x140a554: DecompressPointer r1
    //     0x140a554: add             x1, x1, HEAP, lsl #32
    // 0x140a558: cmp             w1, NULL
    // 0x140a55c: b.eq            #0x140a770
    // 0x140a560: ldur            x0, [fp, #-8]
    // 0x140a564: ldur            x2, [fp, #-0x10]
    // 0x140a568: LoadField: r1 = r0->field_f
    //     0x140a568: ldur            w1, [x0, #0xf]
    // 0x140a56c: DecompressPointer r1
    //     0x140a56c: add             x1, x1, HEAP, lsl #32
    // 0x140a570: r0 = controller()
    //     0x140a570: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a574: LoadField: r1 = r0->field_97
    //     0x140a574: ldur            w1, [x0, #0x97]
    // 0x140a578: DecompressPointer r1
    //     0x140a578: add             x1, x1, HEAP, lsl #32
    // 0x140a57c: ldur            x2, [fp, #-0x10]
    // 0x140a580: LoadField: r0 = r2->field_f
    //     0x140a580: ldur            w0, [x2, #0xf]
    // 0x140a584: DecompressPointer r0
    //     0x140a584: add             x0, x0, HEAP, lsl #32
    // 0x140a588: stur            x0, [fp, #-0x20]
    // 0x140a58c: r0 = value()
    //     0x140a58c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a590: r1 = LoadClassIdInstr(r0)
    //     0x140a590: ldur            x1, [x0, #-1]
    //     0x140a594: ubfx            x1, x1, #0xc, #0x14
    // 0x140a598: ldur            x16, [fp, #-0x20]
    // 0x140a59c: stp             x16, x0, [SP]
    // 0x140a5a0: mov             x0, x1
    // 0x140a5a4: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a5a4: sub             lr, x0, #0xb7
    //     0x140a5a8: ldr             lr, [x21, lr, lsl #3]
    //     0x140a5ac: blr             lr
    // 0x140a5b0: LoadField: r1 = r0->field_b
    //     0x140a5b0: ldur            w1, [x0, #0xb]
    // 0x140a5b4: DecompressPointer r1
    //     0x140a5b4: add             x1, x1, HEAP, lsl #32
    // 0x140a5b8: r0 = LoadClassIdInstr(r1)
    //     0x140a5b8: ldur            x0, [x1, #-1]
    //     0x140a5bc: ubfx            x0, x0, #0xc, #0x14
    // 0x140a5c0: r16 = "image"
    //     0x140a5c0: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x140a5c4: stp             x16, x1, [SP]
    // 0x140a5c8: mov             lr, x0
    // 0x140a5cc: ldr             lr, [x21, lr, lsl #3]
    // 0x140a5d0: blr             lr
    // 0x140a5d4: tbnz            w0, #4, #0x140a768
    // 0x140a5d8: ldur            x0, [fp, #-8]
    // 0x140a5dc: ldur            x2, [fp, #-0x10]
    // 0x140a5e0: r0 = Radius()
    //     0x140a5e0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x140a5e4: d0 = 15.000000
    //     0x140a5e4: fmov            d0, #15.00000000
    // 0x140a5e8: stur            x0, [fp, #-0x20]
    // 0x140a5ec: StoreField: r0->field_7 = d0
    //     0x140a5ec: stur            d0, [x0, #7]
    // 0x140a5f0: StoreField: r0->field_f = d0
    //     0x140a5f0: stur            d0, [x0, #0xf]
    // 0x140a5f4: r0 = BorderRadius()
    //     0x140a5f4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x140a5f8: mov             x2, x0
    // 0x140a5fc: ldur            x0, [fp, #-0x20]
    // 0x140a600: stur            x2, [fp, #-0x28]
    // 0x140a604: StoreField: r2->field_7 = r0
    //     0x140a604: stur            w0, [x2, #7]
    // 0x140a608: StoreField: r2->field_b = r0
    //     0x140a608: stur            w0, [x2, #0xb]
    // 0x140a60c: StoreField: r2->field_f = r0
    //     0x140a60c: stur            w0, [x2, #0xf]
    // 0x140a610: StoreField: r2->field_13 = r0
    //     0x140a610: stur            w0, [x2, #0x13]
    // 0x140a614: ldur            x0, [fp, #-8]
    // 0x140a618: LoadField: r1 = r0->field_f
    //     0x140a618: ldur            w1, [x0, #0xf]
    // 0x140a61c: DecompressPointer r1
    //     0x140a61c: add             x1, x1, HEAP, lsl #32
    // 0x140a620: r0 = controller()
    //     0x140a620: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140a624: LoadField: r1 = r0->field_97
    //     0x140a624: ldur            w1, [x0, #0x97]
    // 0x140a628: DecompressPointer r1
    //     0x140a628: add             x1, x1, HEAP, lsl #32
    // 0x140a62c: ldur            x2, [fp, #-0x10]
    // 0x140a630: LoadField: r0 = r2->field_f
    //     0x140a630: ldur            w0, [x2, #0xf]
    // 0x140a634: DecompressPointer r0
    //     0x140a634: add             x0, x0, HEAP, lsl #32
    // 0x140a638: stur            x0, [fp, #-8]
    // 0x140a63c: r0 = value()
    //     0x140a63c: bl              #0x1512eec  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxList::value
    // 0x140a640: r1 = LoadClassIdInstr(r0)
    //     0x140a640: ldur            x1, [x0, #-1]
    //     0x140a644: ubfx            x1, x1, #0xc, #0x14
    // 0x140a648: ldur            x16, [fp, #-8]
    // 0x140a64c: stp             x16, x0, [SP]
    // 0x140a650: mov             x0, x1
    // 0x140a654: r0 = GDT[cid_x0 + -0xb7]()
    //     0x140a654: sub             lr, x0, #0xb7
    //     0x140a658: ldr             lr, [x21, lr, lsl #3]
    //     0x140a65c: blr             lr
    // 0x140a660: LoadField: r1 = r0->field_f
    //     0x140a660: ldur            w1, [x0, #0xf]
    // 0x140a664: DecompressPointer r1
    //     0x140a664: add             x1, x1, HEAP, lsl #32
    // 0x140a668: cmp             w1, NULL
    // 0x140a66c: b.ne            #0x140a678
    // 0x140a670: r2 = ""
    //     0x140a670: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x140a674: b               #0x140a67c
    // 0x140a678: mov             x2, x1
    // 0x140a67c: ldur            x1, [fp, #-0x18]
    // 0x140a680: ldur            x0, [fp, #-0x28]
    // 0x140a684: stur            x2, [fp, #-8]
    // 0x140a688: r0 = Image()
    //     0x140a688: bl              #0x8022c0  ; AllocateImageStub -> Image (size=0x58)
    // 0x140a68c: stur            x0, [fp, #-0x20]
    // 0x140a690: r16 = 57.000000
    //     0x140a690: add             x16, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x140a694: ldr             x16, [x16, #0xb18]
    // 0x140a698: r30 = 57.000000
    //     0x140a698: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b18] 57
    //     0x140a69c: ldr             lr, [lr, #0xb18]
    // 0x140a6a0: stp             lr, x16, [SP, #8]
    // 0x140a6a4: r16 = Instance_BoxFit
    //     0x140a6a4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x140a6a8: ldr             x16, [x16, #0x118]
    // 0x140a6ac: str             x16, [SP]
    // 0x140a6b0: mov             x1, x0
    // 0x140a6b4: ldur            x2, [fp, #-8]
    // 0x140a6b8: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x2, width, 0x3, null]
    //     0x140a6b8: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b48] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x2, "width", 0x3, Null]
    //     0x140a6bc: ldr             x4, [x4, #0xb48]
    // 0x140a6c0: r0 = Image.network()
    //     0x140a6c0: bl              #0x802090  ; [package:flutter/src/widgets/image.dart] Image::Image.network
    // 0x140a6c4: r0 = ClipRRect()
    //     0x140a6c4: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x140a6c8: mov             x2, x0
    // 0x140a6cc: ldur            x0, [fp, #-0x28]
    // 0x140a6d0: stur            x2, [fp, #-8]
    // 0x140a6d4: StoreField: r2->field_f = r0
    //     0x140a6d4: stur            w0, [x2, #0xf]
    // 0x140a6d8: r0 = Instance_Clip
    //     0x140a6d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x140a6dc: ldr             x0, [x0, #0x138]
    // 0x140a6e0: ArrayStore: r2[0] = r0  ; List_4
    //     0x140a6e0: stur            w0, [x2, #0x17]
    // 0x140a6e4: ldur            x0, [fp, #-0x20]
    // 0x140a6e8: StoreField: r2->field_b = r0
    //     0x140a6e8: stur            w0, [x2, #0xb]
    // 0x140a6ec: ldur            x0, [fp, #-0x18]
    // 0x140a6f0: LoadField: r1 = r0->field_b
    //     0x140a6f0: ldur            w1, [x0, #0xb]
    // 0x140a6f4: LoadField: r3 = r0->field_f
    //     0x140a6f4: ldur            w3, [x0, #0xf]
    // 0x140a6f8: DecompressPointer r3
    //     0x140a6f8: add             x3, x3, HEAP, lsl #32
    // 0x140a6fc: LoadField: r4 = r3->field_b
    //     0x140a6fc: ldur            w4, [x3, #0xb]
    // 0x140a700: r3 = LoadInt32Instr(r1)
    //     0x140a700: sbfx            x3, x1, #1, #0x1f
    // 0x140a704: stur            x3, [fp, #-0x40]
    // 0x140a708: r1 = LoadInt32Instr(r4)
    //     0x140a708: sbfx            x1, x4, #1, #0x1f
    // 0x140a70c: cmp             x3, x1
    // 0x140a710: b.ne            #0x140a71c
    // 0x140a714: mov             x1, x0
    // 0x140a718: r0 = _growToNextCapacity()
    //     0x140a718: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140a71c: ldur            x2, [fp, #-0x18]
    // 0x140a720: ldur            x3, [fp, #-0x40]
    // 0x140a724: add             x0, x3, #1
    // 0x140a728: lsl             x1, x0, #1
    // 0x140a72c: StoreField: r2->field_b = r1
    //     0x140a72c: stur            w1, [x2, #0xb]
    // 0x140a730: LoadField: r1 = r2->field_f
    //     0x140a730: ldur            w1, [x2, #0xf]
    // 0x140a734: DecompressPointer r1
    //     0x140a734: add             x1, x1, HEAP, lsl #32
    // 0x140a738: ldur            x0, [fp, #-8]
    // 0x140a73c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140a73c: add             x25, x1, x3, lsl #2
    //     0x140a740: add             x25, x25, #0xf
    //     0x140a744: str             w0, [x25]
    //     0x140a748: tbz             w0, #0, #0x140a764
    //     0x140a74c: ldurb           w16, [x1, #-1]
    //     0x140a750: ldurb           w17, [x0, #-1]
    //     0x140a754: and             x16, x17, x16, lsr #2
    //     0x140a758: tst             x16, HEAP, lsr #32
    //     0x140a75c: b.eq            #0x140a764
    //     0x140a760: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140a764: b               #0x140a774
    // 0x140a768: ldur            x2, [fp, #-0x18]
    // 0x140a76c: b               #0x140a774
    // 0x140a770: ldur            x2, [fp, #-0x18]
    // 0x140a774: ldr             x1, [fp, #0x18]
    // 0x140a778: r0 = of()
    //     0x140a778: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140a77c: LoadField: r2 = r0->field_5b
    //     0x140a77c: ldur            w2, [x0, #0x5b]
    // 0x140a780: DecompressPointer r2
    //     0x140a780: add             x2, x2, HEAP, lsl #32
    // 0x140a784: r16 = 1.000000
    //     0x140a784: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x140a788: str             x16, [SP]
    // 0x140a78c: r1 = Null
    //     0x140a78c: mov             x1, NULL
    // 0x140a790: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0x140a790: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0x140a794: ldr             x4, [x4, #0x108]
    // 0x140a798: r0 = Border.all()
    //     0x140a798: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0x140a79c: ldr             x1, [fp, #0x18]
    // 0x140a7a0: stur            x0, [fp, #-8]
    // 0x140a7a4: r0 = of()
    //     0x140a7a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x140a7a8: LoadField: r1 = r0->field_5b
    //     0x140a7a8: ldur            w1, [x0, #0x5b]
    // 0x140a7ac: DecompressPointer r1
    //     0x140a7ac: add             x1, x1, HEAP, lsl #32
    // 0x140a7b0: stur            x1, [fp, #-0x20]
    // 0x140a7b4: r0 = BoxDecoration()
    //     0x140a7b4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x140a7b8: mov             x1, x0
    // 0x140a7bc: ldur            x0, [fp, #-0x20]
    // 0x140a7c0: stur            x1, [fp, #-0x28]
    // 0x140a7c4: StoreField: r1->field_7 = r0
    //     0x140a7c4: stur            w0, [x1, #7]
    // 0x140a7c8: ldur            x0, [fp, #-8]
    // 0x140a7cc: StoreField: r1->field_f = r0
    //     0x140a7cc: stur            w0, [x1, #0xf]
    // 0x140a7d0: r0 = Instance_BoxShape
    //     0x140a7d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0x140a7d4: ldr             x0, [x0, #0x970]
    // 0x140a7d8: StoreField: r1->field_23 = r0
    //     0x140a7d8: stur            w0, [x1, #0x23]
    // 0x140a7dc: r0 = Container()
    //     0x140a7dc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x140a7e0: stur            x0, [fp, #-8]
    // 0x140a7e4: ldur            x16, [fp, #-0x28]
    // 0x140a7e8: r30 = Instance_Icon
    //     0x140a7e8: add             lr, PP, #0x36, lsl #12  ; [pp+0x36b50] Obj!Icon@d669b1
    //     0x140a7ec: ldr             lr, [lr, #0xb50]
    // 0x140a7f0: stp             lr, x16, [SP]
    // 0x140a7f4: mov             x1, x0
    // 0x140a7f8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0x140a7f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0x140a7fc: ldr             x4, [x4, #0x88]
    // 0x140a800: r0 = Container()
    //     0x140a800: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x140a804: r0 = GestureDetector()
    //     0x140a804: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0x140a808: ldur            x2, [fp, #-0x10]
    // 0x140a80c: r1 = Function '<anonymous closure>':.
    //     0x140a80c: add             x1, PP, #0x40, lsl #12  ; [pp+0x400b8] AnonymousClosure: (0x140a92c), in [package:customer_app/app/presentation/views/line/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x1506a98)
    //     0x140a810: ldr             x1, [x1, #0xb8]
    // 0x140a814: stur            x0, [fp, #-0x10]
    // 0x140a818: r0 = AllocateClosure()
    //     0x140a818: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140a81c: ldur            x16, [fp, #-8]
    // 0x140a820: stp             x16, x0, [SP]
    // 0x140a824: ldur            x1, [fp, #-0x10]
    // 0x140a828: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0x140a828: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0x140a82c: ldr             x4, [x4, #0xaf0]
    // 0x140a830: r0 = GestureDetector()
    //     0x140a830: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0x140a834: r1 = <StackParentData>
    //     0x140a834: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0x140a838: ldr             x1, [x1, #0x8e0]
    // 0x140a83c: r0 = Positioned()
    //     0x140a83c: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0x140a840: mov             x2, x0
    // 0x140a844: ldur            x0, [fp, #-0x10]
    // 0x140a848: stur            x2, [fp, #-8]
    // 0x140a84c: StoreField: r2->field_b = r0
    //     0x140a84c: stur            w0, [x2, #0xb]
    // 0x140a850: ldur            x0, [fp, #-0x18]
    // 0x140a854: LoadField: r1 = r0->field_b
    //     0x140a854: ldur            w1, [x0, #0xb]
    // 0x140a858: LoadField: r3 = r0->field_f
    //     0x140a858: ldur            w3, [x0, #0xf]
    // 0x140a85c: DecompressPointer r3
    //     0x140a85c: add             x3, x3, HEAP, lsl #32
    // 0x140a860: LoadField: r4 = r3->field_b
    //     0x140a860: ldur            w4, [x3, #0xb]
    // 0x140a864: r3 = LoadInt32Instr(r1)
    //     0x140a864: sbfx            x3, x1, #1, #0x1f
    // 0x140a868: stur            x3, [fp, #-0x40]
    // 0x140a86c: r1 = LoadInt32Instr(r4)
    //     0x140a86c: sbfx            x1, x4, #1, #0x1f
    // 0x140a870: cmp             x3, x1
    // 0x140a874: b.ne            #0x140a880
    // 0x140a878: mov             x1, x0
    // 0x140a87c: r0 = _growToNextCapacity()
    //     0x140a87c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140a880: ldur            x2, [fp, #-0x18]
    // 0x140a884: ldur            x3, [fp, #-0x40]
    // 0x140a888: add             x0, x3, #1
    // 0x140a88c: lsl             x1, x0, #1
    // 0x140a890: StoreField: r2->field_b = r1
    //     0x140a890: stur            w1, [x2, #0xb]
    // 0x140a894: LoadField: r1 = r2->field_f
    //     0x140a894: ldur            w1, [x2, #0xf]
    // 0x140a898: DecompressPointer r1
    //     0x140a898: add             x1, x1, HEAP, lsl #32
    // 0x140a89c: ldur            x0, [fp, #-8]
    // 0x140a8a0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140a8a0: add             x25, x1, x3, lsl #2
    //     0x140a8a4: add             x25, x25, #0xf
    //     0x140a8a8: str             w0, [x25]
    //     0x140a8ac: tbz             w0, #0, #0x140a8c8
    //     0x140a8b0: ldurb           w16, [x1, #-1]
    //     0x140a8b4: ldurb           w17, [x0, #-1]
    //     0x140a8b8: and             x16, x17, x16, lsr #2
    //     0x140a8bc: tst             x16, HEAP, lsr #32
    //     0x140a8c0: b.eq            #0x140a8c8
    //     0x140a8c4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140a8c8: r0 = Stack()
    //     0x140a8c8: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0x140a8cc: mov             x1, x0
    // 0x140a8d0: r0 = Instance_Alignment
    //     0x140a8d0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0x140a8d4: ldr             x0, [x0, #0x950]
    // 0x140a8d8: stur            x1, [fp, #-8]
    // 0x140a8dc: StoreField: r1->field_f = r0
    //     0x140a8dc: stur            w0, [x1, #0xf]
    // 0x140a8e0: r0 = Instance_StackFit
    //     0x140a8e0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0x140a8e4: ldr             x0, [x0, #0xfa8]
    // 0x140a8e8: ArrayStore: r1[0] = r0  ; List_4
    //     0x140a8e8: stur            w0, [x1, #0x17]
    // 0x140a8ec: r0 = Instance_Clip
    //     0x140a8ec: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x140a8f0: ldr             x0, [x0, #0x7e0]
    // 0x140a8f4: StoreField: r1->field_1b = r0
    //     0x140a8f4: stur            w0, [x1, #0x1b]
    // 0x140a8f8: ldur            x0, [fp, #-0x18]
    // 0x140a8fc: StoreField: r1->field_b = r0
    //     0x140a8fc: stur            w0, [x1, #0xb]
    // 0x140a900: r0 = Padding()
    //     0x140a900: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x140a904: r1 = Instance_EdgeInsets
    //     0x140a904: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdf8] Obj!EdgeInsets@d599c1
    //     0x140a908: ldr             x1, [x1, #0xdf8]
    // 0x140a90c: StoreField: r0->field_f = r1
    //     0x140a90c: stur            w1, [x0, #0xf]
    // 0x140a910: ldur            x1, [fp, #-8]
    // 0x140a914: StoreField: r0->field_b = r1
    //     0x140a914: stur            w1, [x0, #0xb]
    // 0x140a918: LeaveFrame
    //     0x140a918: mov             SP, fp
    //     0x140a91c: ldp             fp, lr, [SP], #0x10
    // 0x140a920: ret
    //     0x140a920: ret             
    // 0x140a924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140a924: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140a928: b               #0x1409c54
  }
  [closure] SizedBox <anonymous closure>(dynamic) {
    // ** addr: 0x140e2e4, size: 0x164
    // 0x140e2e4: EnterFrame
    //     0x140e2e4: stp             fp, lr, [SP, #-0x10]!
    //     0x140e2e8: mov             fp, SP
    // 0x140e2ec: AllocStack(0x20)
    //     0x140e2ec: sub             SP, SP, #0x20
    // 0x140e2f0: SetupParameters()
    //     0x140e2f0: ldr             x0, [fp, #0x10]
    //     0x140e2f4: ldur            w2, [x0, #0x17]
    //     0x140e2f8: add             x2, x2, HEAP, lsl #32
    //     0x140e2fc: stur            x2, [fp, #-8]
    // 0x140e300: CheckStackOverflow
    //     0x140e300: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e304: cmp             SP, x16
    //     0x140e308: b.ls            #0x140e43c
    // 0x140e30c: LoadField: r1 = r2->field_f
    //     0x140e30c: ldur            w1, [x2, #0xf]
    // 0x140e310: DecompressPointer r1
    //     0x140e310: add             x1, x1, HEAP, lsl #32
    // 0x140e314: r0 = controller()
    //     0x140e314: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e318: LoadField: r1 = r0->field_db
    //     0x140e318: ldur            w1, [x0, #0xdb]
    // 0x140e31c: DecompressPointer r1
    //     0x140e31c: add             x1, x1, HEAP, lsl #32
    // 0x140e320: r0 = value()
    //     0x140e320: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140e324: tbnz            w0, #4, #0x140e42c
    // 0x140e328: ldur            x2, [fp, #-8]
    // 0x140e32c: LoadField: r1 = r2->field_f
    //     0x140e32c: ldur            w1, [x2, #0xf]
    // 0x140e330: DecompressPointer r1
    //     0x140e330: add             x1, x1, HEAP, lsl #32
    // 0x140e334: r0 = controller()
    //     0x140e334: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e338: LoadField: r1 = r0->field_db
    //     0x140e338: ldur            w1, [x0, #0xdb]
    // 0x140e33c: DecompressPointer r1
    //     0x140e33c: add             x1, x1, HEAP, lsl #32
    // 0x140e340: r2 = false
    //     0x140e340: add             x2, NULL, #0x30  ; false
    // 0x140e344: r0 = value=()
    //     0x140e344: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x140e348: r0 = LoadStaticField(0x878)
    //     0x140e348: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x140e34c: ldr             x0, [x0, #0x10f0]
    // 0x140e350: cmp             w0, NULL
    // 0x140e354: b.eq            #0x140e444
    // 0x140e358: LoadField: r3 = r0->field_53
    //     0x140e358: ldur            w3, [x0, #0x53]
    // 0x140e35c: DecompressPointer r3
    //     0x140e35c: add             x3, x3, HEAP, lsl #32
    // 0x140e360: stur            x3, [fp, #-0x18]
    // 0x140e364: LoadField: r0 = r3->field_7
    //     0x140e364: ldur            w0, [x3, #7]
    // 0x140e368: DecompressPointer r0
    //     0x140e368: add             x0, x0, HEAP, lsl #32
    // 0x140e36c: ldur            x2, [fp, #-8]
    // 0x140e370: stur            x0, [fp, #-0x10]
    // 0x140e374: r1 = Function '<anonymous closure>':.
    //     0x140e374: add             x1, PP, #0x40, lsl #12  ; [pp+0x400c0] AnonymousClosure: (0x140e448), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14f0f10)
    //     0x140e378: ldr             x1, [x1, #0xc0]
    // 0x140e37c: r0 = AllocateClosure()
    //     0x140e37c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140e380: ldur            x2, [fp, #-0x10]
    // 0x140e384: mov             x3, x0
    // 0x140e388: r1 = Null
    //     0x140e388: mov             x1, NULL
    // 0x140e38c: stur            x3, [fp, #-8]
    // 0x140e390: cmp             w2, NULL
    // 0x140e394: b.eq            #0x140e3b4
    // 0x140e398: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x140e398: ldur            w4, [x2, #0x17]
    // 0x140e39c: DecompressPointer r4
    //     0x140e39c: add             x4, x4, HEAP, lsl #32
    // 0x140e3a0: r8 = X0
    //     0x140e3a0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x140e3a4: LoadField: r9 = r4->field_7
    //     0x140e3a4: ldur            x9, [x4, #7]
    // 0x140e3a8: r3 = Null
    //     0x140e3a8: add             x3, PP, #0x40, lsl #12  ; [pp+0x400c8] Null
    //     0x140e3ac: ldr             x3, [x3, #0xc8]
    // 0x140e3b0: blr             x9
    // 0x140e3b4: ldur            x0, [fp, #-0x18]
    // 0x140e3b8: LoadField: r1 = r0->field_b
    //     0x140e3b8: ldur            w1, [x0, #0xb]
    // 0x140e3bc: LoadField: r2 = r0->field_f
    //     0x140e3bc: ldur            w2, [x0, #0xf]
    // 0x140e3c0: DecompressPointer r2
    //     0x140e3c0: add             x2, x2, HEAP, lsl #32
    // 0x140e3c4: LoadField: r3 = r2->field_b
    //     0x140e3c4: ldur            w3, [x2, #0xb]
    // 0x140e3c8: r2 = LoadInt32Instr(r1)
    //     0x140e3c8: sbfx            x2, x1, #1, #0x1f
    // 0x140e3cc: stur            x2, [fp, #-0x20]
    // 0x140e3d0: r1 = LoadInt32Instr(r3)
    //     0x140e3d0: sbfx            x1, x3, #1, #0x1f
    // 0x140e3d4: cmp             x2, x1
    // 0x140e3d8: b.ne            #0x140e3e4
    // 0x140e3dc: mov             x1, x0
    // 0x140e3e0: r0 = _growToNextCapacity()
    //     0x140e3e0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x140e3e4: ldur            x2, [fp, #-0x18]
    // 0x140e3e8: ldur            x3, [fp, #-0x20]
    // 0x140e3ec: add             x4, x3, #1
    // 0x140e3f0: lsl             x5, x4, #1
    // 0x140e3f4: StoreField: r2->field_b = r5
    //     0x140e3f4: stur            w5, [x2, #0xb]
    // 0x140e3f8: LoadField: r1 = r2->field_f
    //     0x140e3f8: ldur            w1, [x2, #0xf]
    // 0x140e3fc: DecompressPointer r1
    //     0x140e3fc: add             x1, x1, HEAP, lsl #32
    // 0x140e400: ldur            x0, [fp, #-8]
    // 0x140e404: ArrayStore: r1[r3] = r0  ; List_4
    //     0x140e404: add             x25, x1, x3, lsl #2
    //     0x140e408: add             x25, x25, #0xf
    //     0x140e40c: str             w0, [x25]
    //     0x140e410: tbz             w0, #0, #0x140e42c
    //     0x140e414: ldurb           w16, [x1, #-1]
    //     0x140e418: ldurb           w17, [x0, #-1]
    //     0x140e41c: and             x16, x17, x16, lsr #2
    //     0x140e420: tst             x16, HEAP, lsr #32
    //     0x140e424: b.eq            #0x140e42c
    //     0x140e428: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x140e42c: r0 = Instance_SizedBox
    //     0x140e42c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x140e430: LeaveFrame
    //     0x140e430: mov             SP, fp
    //     0x140e434: ldp             fp, lr, [SP], #0x10
    // 0x140e438: ret
    //     0x140e438: ret             
    // 0x140e43c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e43c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e440: b               #0x140e30c
    // 0x140e444: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x140e444: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x140e448, size: 0x48
    // 0x140e448: EnterFrame
    //     0x140e448: stp             fp, lr, [SP, #-0x10]!
    //     0x140e44c: mov             fp, SP
    // 0x140e450: ldr             x0, [fp, #0x18]
    // 0x140e454: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x140e454: ldur            w1, [x0, #0x17]
    // 0x140e458: DecompressPointer r1
    //     0x140e458: add             x1, x1, HEAP, lsl #32
    // 0x140e45c: CheckStackOverflow
    //     0x140e45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e460: cmp             SP, x16
    //     0x140e464: b.ls            #0x140e488
    // 0x140e468: LoadField: r0 = r1->field_f
    //     0x140e468: ldur            w0, [x1, #0xf]
    // 0x140e46c: DecompressPointer r0
    //     0x140e46c: add             x0, x0, HEAP, lsl #32
    // 0x140e470: mov             x1, x0
    // 0x140e474: r0 = _showRatingSuccessBottomSheet()
    //     0x140e474: bl              #0x140e490  ; [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::_showRatingSuccessBottomSheet
    // 0x140e478: r0 = Null
    //     0x140e478: mov             x0, NULL
    // 0x140e47c: LeaveFrame
    //     0x140e47c: mov             SP, fp
    //     0x140e480: ldp             fp, lr, [SP], #0x10
    // 0x140e484: ret
    //     0x140e484: ret             
    // 0x140e488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e488: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e48c: b               #0x140e468
  }
  _ _showRatingSuccessBottomSheet(/* No info */) {
    // ** addr: 0x140e490, size: 0xc0
    // 0x140e490: EnterFrame
    //     0x140e490: stp             fp, lr, [SP, #-0x10]!
    //     0x140e494: mov             fp, SP
    // 0x140e498: AllocStack(0x38)
    //     0x140e498: sub             SP, SP, #0x38
    // 0x140e49c: SetupParameters(RatingReviewOrderPage this /* r1 => r1, fp-0x8 */)
    //     0x140e49c: stur            x1, [fp, #-8]
    // 0x140e4a0: CheckStackOverflow
    //     0x140e4a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e4a4: cmp             SP, x16
    //     0x140e4a8: b.ls            #0x140e544
    // 0x140e4ac: r1 = 1
    //     0x140e4ac: movz            x1, #0x1
    // 0x140e4b0: r0 = AllocateContext()
    //     0x140e4b0: bl              #0x16f6108  ; AllocateContextStub
    // 0x140e4b4: ldur            x1, [fp, #-8]
    // 0x140e4b8: stur            x0, [fp, #-0x10]
    // 0x140e4bc: StoreField: r0->field_f = r1
    //     0x140e4bc: stur            w1, [x0, #0xf]
    // 0x140e4c0: r0 = controller()
    //     0x140e4c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e4c4: mov             x1, x0
    // 0x140e4c8: r0 = false
    //     0x140e4c8: add             x0, NULL, #0x30  ; false
    // 0x140e4cc: StoreField: r1->field_c7 = r0
    //     0x140e4cc: stur            w0, [x1, #0xc7]
    // 0x140e4d0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x140e4d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x140e4d4: ldr             x0, [x0, #0x1c80]
    //     0x140e4d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x140e4dc: cmp             w0, w16
    //     0x140e4e0: b.ne            #0x140e4ec
    //     0x140e4e4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x140e4e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x140e4ec: r0 = GetNavigation.context()
    //     0x140e4ec: bl              #0x8a54d0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.context
    // 0x140e4f0: stur            x0, [fp, #-8]
    // 0x140e4f4: cmp             w0, NULL
    // 0x140e4f8: b.eq            #0x140e54c
    // 0x140e4fc: ldur            x2, [fp, #-0x10]
    // 0x140e500: r1 = Function '<anonymous closure>':.
    //     0x140e500: add             x1, PP, #0x40, lsl #12  ; [pp+0x400d8] AnonymousClosure: (0x140e550), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::_showRatingSuccessBottomSheet (0x140e490)
    //     0x140e504: ldr             x1, [x1, #0xd8]
    // 0x140e508: r0 = AllocateClosure()
    //     0x140e508: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140e50c: stp             x0, NULL, [SP, #0x18]
    // 0x140e510: ldur            x16, [fp, #-8]
    // 0x140e514: r30 = true
    //     0x140e514: add             lr, NULL, #0x20  ; true
    // 0x140e518: stp             lr, x16, [SP, #8]
    // 0x140e51c: r16 = Instance_Color
    //     0x140e51c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0x140e520: ldr             x16, [x16, #0xf88]
    // 0x140e524: str             x16, [SP]
    // 0x140e528: r4 = const [0x1, 0x4, 0x4, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, null]
    //     0x140e528: add             x4, PP, #0x36, lsl #12  ; [pp+0x36be8] List(9) [0x1, 0x4, 0x4, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, Null]
    //     0x140e52c: ldr             x4, [x4, #0xbe8]
    // 0x140e530: r0 = showModalBottomSheet()
    //     0x140e530: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x140e534: r0 = Null
    //     0x140e534: mov             x0, NULL
    // 0x140e538: LeaveFrame
    //     0x140e538: mov             SP, fp
    //     0x140e53c: ldp             fp, lr, [SP], #0x10
    // 0x140e540: ret
    //     0x140e540: ret             
    // 0x140e544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e544: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e548: b               #0x140e4ac
    // 0x140e54c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x140e54c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] RatingReviewSuccessBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x140e550, size: 0x110
    // 0x140e550: EnterFrame
    //     0x140e550: stp             fp, lr, [SP, #-0x10]!
    //     0x140e554: mov             fp, SP
    // 0x140e558: AllocStack(0x30)
    //     0x140e558: sub             SP, SP, #0x30
    // 0x140e55c: SetupParameters()
    //     0x140e55c: ldr             x0, [fp, #0x18]
    //     0x140e560: ldur            w2, [x0, #0x17]
    //     0x140e564: add             x2, x2, HEAP, lsl #32
    //     0x140e568: stur            x2, [fp, #-8]
    // 0x140e56c: CheckStackOverflow
    //     0x140e56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x140e570: cmp             SP, x16
    //     0x140e574: b.ls            #0x140e658
    // 0x140e578: LoadField: r1 = r2->field_f
    //     0x140e578: ldur            w1, [x2, #0xf]
    // 0x140e57c: DecompressPointer r1
    //     0x140e57c: add             x1, x1, HEAP, lsl #32
    // 0x140e580: r0 = controller()
    //     0x140e580: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e584: LoadField: r1 = r0->field_4b
    //     0x140e584: ldur            w1, [x0, #0x4b]
    // 0x140e588: DecompressPointer r1
    //     0x140e588: add             x1, x1, HEAP, lsl #32
    // 0x140e58c: r0 = value()
    //     0x140e58c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140e590: LoadField: r1 = r0->field_b
    //     0x140e590: ldur            w1, [x0, #0xb]
    // 0x140e594: DecompressPointer r1
    //     0x140e594: add             x1, x1, HEAP, lsl #32
    // 0x140e598: cmp             w1, NULL
    // 0x140e59c: b.ne            #0x140e5a8
    // 0x140e5a0: r0 = Null
    //     0x140e5a0: mov             x0, NULL
    // 0x140e5a4: b               #0x140e5e0
    // 0x140e5a8: LoadField: r0 = r1->field_1f
    //     0x140e5a8: ldur            w0, [x1, #0x1f]
    // 0x140e5ac: DecompressPointer r0
    //     0x140e5ac: add             x0, x0, HEAP, lsl #32
    // 0x140e5b0: cmp             w0, NULL
    // 0x140e5b4: b.ne            #0x140e5c0
    // 0x140e5b8: r0 = Null
    //     0x140e5b8: mov             x0, NULL
    // 0x140e5bc: b               #0x140e5e0
    // 0x140e5c0: LoadField: r1 = r0->field_13
    //     0x140e5c0: ldur            w1, [x0, #0x13]
    // 0x140e5c4: DecompressPointer r1
    //     0x140e5c4: add             x1, x1, HEAP, lsl #32
    // 0x140e5c8: cmp             w1, NULL
    // 0x140e5cc: b.ne            #0x140e5d8
    // 0x140e5d0: r0 = Null
    //     0x140e5d0: mov             x0, NULL
    // 0x140e5d4: b               #0x140e5e0
    // 0x140e5d8: stp             x1, NULL, [SP]
    // 0x140e5dc: r0 = _Double.fromInteger()
    //     0x140e5dc: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x140e5e0: cmp             w0, NULL
    // 0x140e5e4: b.ne            #0x140e5f0
    // 0x140e5e8: d0 = 0.000000
    //     0x140e5e8: eor             v0.16b, v0.16b, v0.16b
    // 0x140e5ec: b               #0x140e5f4
    // 0x140e5f0: LoadField: d0 = r0->field_7
    //     0x140e5f0: ldur            d0, [x0, #7]
    // 0x140e5f4: ldur            x2, [fp, #-8]
    // 0x140e5f8: stur            d0, [fp, #-0x20]
    // 0x140e5fc: LoadField: r1 = r2->field_f
    //     0x140e5fc: ldur            w1, [x2, #0xf]
    // 0x140e600: DecompressPointer r1
    //     0x140e600: add             x1, x1, HEAP, lsl #32
    // 0x140e604: r0 = controller()
    //     0x140e604: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x140e608: LoadField: r1 = r0->field_4b
    //     0x140e608: ldur            w1, [x0, #0x4b]
    // 0x140e60c: DecompressPointer r1
    //     0x140e60c: add             x1, x1, HEAP, lsl #32
    // 0x140e610: r0 = value()
    //     0x140e610: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x140e614: stur            x0, [fp, #-0x10]
    // 0x140e618: r0 = RatingReviewSuccessBottomSheet()
    //     0x140e618: bl              #0x140e25c  ; AllocateRatingReviewSuccessBottomSheetStub -> RatingReviewSuccessBottomSheet (size=0x1c)
    // 0x140e61c: ldur            d0, [fp, #-0x20]
    // 0x140e620: stur            x0, [fp, #-0x18]
    // 0x140e624: StoreField: r0->field_b = d0
    //     0x140e624: stur            d0, [x0, #0xb]
    // 0x140e628: ldur            x1, [fp, #-0x10]
    // 0x140e62c: StoreField: r0->field_13 = r1
    //     0x140e62c: stur            w1, [x0, #0x13]
    // 0x140e630: ldur            x2, [fp, #-8]
    // 0x140e634: r1 = Function '<anonymous closure>':.
    //     0x140e634: add             x1, PP, #0x40, lsl #12  ; [pp+0x400e0] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x140e638: ldr             x1, [x1, #0xe0]
    // 0x140e63c: r0 = AllocateClosure()
    //     0x140e63c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x140e640: mov             x1, x0
    // 0x140e644: ldur            x0, [fp, #-0x18]
    // 0x140e648: ArrayStore: r0[0] = r1  ; List_4
    //     0x140e648: stur            w1, [x0, #0x17]
    // 0x140e64c: LeaveFrame
    //     0x140e64c: mov             SP, fp
    //     0x140e650: ldp             fp, lr, [SP], #0x10
    // 0x140e654: ret
    //     0x140e654: ret             
    // 0x140e658: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x140e658: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x140e65c: b               #0x140e578
  }
  _ body(/* No info */) {
    // ** addr: 0x14f0f10, size: 0x94
    // 0x14f0f10: EnterFrame
    //     0x14f0f10: stp             fp, lr, [SP, #-0x10]!
    //     0x14f0f14: mov             fp, SP
    // 0x14f0f18: AllocStack(0x18)
    //     0x14f0f18: sub             SP, SP, #0x18
    // 0x14f0f1c: SetupParameters(RatingReviewOrderPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14f0f1c: mov             x0, x1
    //     0x14f0f20: stur            x1, [fp, #-8]
    //     0x14f0f24: stur            x2, [fp, #-0x10]
    // 0x14f0f28: r1 = 2
    //     0x14f0f28: movz            x1, #0x2
    // 0x14f0f2c: r0 = AllocateContext()
    //     0x14f0f2c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14f0f30: ldur            x2, [fp, #-8]
    // 0x14f0f34: stur            x0, [fp, #-0x18]
    // 0x14f0f38: StoreField: r0->field_f = r2
    //     0x14f0f38: stur            w2, [x0, #0xf]
    // 0x14f0f3c: ldur            x1, [fp, #-0x10]
    // 0x14f0f40: StoreField: r0->field_13 = r1
    //     0x14f0f40: stur            w1, [x0, #0x13]
    // 0x14f0f44: r0 = Obx()
    //     0x14f0f44: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14f0f48: ldur            x2, [fp, #-0x18]
    // 0x14f0f4c: r1 = Function '<anonymous closure>':.
    //     0x14f0f4c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40040] AnonymousClosure: (0x140871c), in [package:customer_app/app/presentation/views/glass/orders/rating_review_order_page.dart] RatingReviewOrderPage::body (0x14f0f10)
    //     0x14f0f50: ldr             x1, [x1, #0x40]
    // 0x14f0f54: stur            x0, [fp, #-0x10]
    // 0x14f0f58: r0 = AllocateClosure()
    //     0x14f0f58: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f0f5c: mov             x1, x0
    // 0x14f0f60: ldur            x0, [fp, #-0x10]
    // 0x14f0f64: StoreField: r0->field_b = r1
    //     0x14f0f64: stur            w1, [x0, #0xb]
    // 0x14f0f68: r0 = WillPopScope()
    //     0x14f0f68: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14f0f6c: mov             x3, x0
    // 0x14f0f70: ldur            x0, [fp, #-0x10]
    // 0x14f0f74: stur            x3, [fp, #-0x18]
    // 0x14f0f78: StoreField: r3->field_b = r0
    //     0x14f0f78: stur            w0, [x3, #0xb]
    // 0x14f0f7c: ldur            x2, [fp, #-8]
    // 0x14f0f80: r1 = Function '_onBackPress@1603236570':.
    //     0x14f0f80: add             x1, PP, #0x40, lsl #12  ; [pp+0x40048] AnonymousClosure: (0x14f0fa4), in [package:customer_app/app/presentation/views/basic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_onBackPress (0x14071f8)
    //     0x14f0f84: ldr             x1, [x1, #0x48]
    // 0x14f0f88: r0 = AllocateClosure()
    //     0x14f0f88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14f0f8c: mov             x1, x0
    // 0x14f0f90: ldur            x0, [fp, #-0x18]
    // 0x14f0f94: StoreField: r0->field_f = r1
    //     0x14f0f94: stur            w1, [x0, #0xf]
    // 0x14f0f98: LeaveFrame
    //     0x14f0f98: mov             SP, fp
    //     0x14f0f9c: ldp             fp, lr, [SP], #0x10
    // 0x14f0fa0: ret
    //     0x14f0fa0: ret             
  }
  [closure] Future<bool> _onBackPress(dynamic) {
    // ** addr: 0x14f0fa4, size: 0x38
    // 0x14f0fa4: EnterFrame
    //     0x14f0fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x14f0fa8: mov             fp, SP
    // 0x14f0fac: ldr             x0, [fp, #0x10]
    // 0x14f0fb0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14f0fb0: ldur            w1, [x0, #0x17]
    // 0x14f0fb4: DecompressPointer r1
    //     0x14f0fb4: add             x1, x1, HEAP, lsl #32
    // 0x14f0fb8: CheckStackOverflow
    //     0x14f0fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14f0fbc: cmp             SP, x16
    //     0x14f0fc0: b.ls            #0x14f0fd4
    // 0x14f0fc4: r0 = _onBackPress()
    //     0x14f0fc4: bl              #0x14071f8  ; [package:customer_app/app/presentation/views/basic/orders/rating_review_order_page.dart] RatingReviewOrderPage::_onBackPress
    // 0x14f0fc8: LeaveFrame
    //     0x14f0fc8: mov             SP, fp
    //     0x14f0fcc: ldp             fp, lr, [SP], #0x10
    // 0x14f0fd0: ret
    //     0x14f0fd0: ret             
    // 0x14f0fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14f0fd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14f0fd8: b               #0x14f0fc4
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e348c, size: 0x1bc
    // 0x15e348c: EnterFrame
    //     0x15e348c: stp             fp, lr, [SP, #-0x10]!
    //     0x15e3490: mov             fp, SP
    // 0x15e3494: AllocStack(0x30)
    //     0x15e3494: sub             SP, SP, #0x30
    // 0x15e3498: SetupParameters(RatingReviewOrderPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15e3498: mov             x0, x1
    //     0x15e349c: stur            x1, [fp, #-8]
    //     0x15e34a0: mov             x1, x2
    //     0x15e34a4: stur            x2, [fp, #-0x10]
    // 0x15e34a8: CheckStackOverflow
    //     0x15e34a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e34ac: cmp             SP, x16
    //     0x15e34b0: b.ls            #0x15e3640
    // 0x15e34b4: r1 = 1
    //     0x15e34b4: movz            x1, #0x1
    // 0x15e34b8: r0 = AllocateContext()
    //     0x15e34b8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e34bc: mov             x2, x0
    // 0x15e34c0: ldur            x0, [fp, #-8]
    // 0x15e34c4: stur            x2, [fp, #-0x18]
    // 0x15e34c8: StoreField: r2->field_f = r0
    //     0x15e34c8: stur            w0, [x2, #0xf]
    // 0x15e34cc: ldur            x1, [fp, #-0x10]
    // 0x15e34d0: r0 = of()
    //     0x15e34d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e34d4: LoadField: r1 = r0->field_87
    //     0x15e34d4: ldur            w1, [x0, #0x87]
    // 0x15e34d8: DecompressPointer r1
    //     0x15e34d8: add             x1, x1, HEAP, lsl #32
    // 0x15e34dc: LoadField: r0 = r1->field_7
    //     0x15e34dc: ldur            w0, [x1, #7]
    // 0x15e34e0: DecompressPointer r0
    //     0x15e34e0: add             x0, x0, HEAP, lsl #32
    // 0x15e34e4: r16 = Instance_Color
    //     0x15e34e4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15e34e8: r30 = 16.000000
    //     0x15e34e8: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15e34ec: ldr             lr, [lr, #0x188]
    // 0x15e34f0: stp             lr, x16, [SP]
    // 0x15e34f4: mov             x1, x0
    // 0x15e34f8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x15e34f8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x15e34fc: ldr             x4, [x4, #0x9b8]
    // 0x15e3500: r0 = copyWith()
    //     0x15e3500: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15e3504: stur            x0, [fp, #-8]
    // 0x15e3508: r0 = Text()
    //     0x15e3508: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15e350c: mov             x2, x0
    // 0x15e3510: r0 = "Review Product"
    //     0x15e3510: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c20] "Review Product"
    //     0x15e3514: ldr             x0, [x0, #0xc20]
    // 0x15e3518: stur            x2, [fp, #-0x20]
    // 0x15e351c: StoreField: r2->field_b = r0
    //     0x15e351c: stur            w0, [x2, #0xb]
    // 0x15e3520: ldur            x0, [fp, #-8]
    // 0x15e3524: StoreField: r2->field_13 = r0
    //     0x15e3524: stur            w0, [x2, #0x13]
    // 0x15e3528: ldur            x1, [fp, #-0x10]
    // 0x15e352c: r0 = of()
    //     0x15e352c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e3530: LoadField: r1 = r0->field_5b
    //     0x15e3530: ldur            w1, [x0, #0x5b]
    // 0x15e3534: DecompressPointer r1
    //     0x15e3534: add             x1, x1, HEAP, lsl #32
    // 0x15e3538: stur            x1, [fp, #-8]
    // 0x15e353c: r0 = ColorFilter()
    //     0x15e353c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e3540: mov             x1, x0
    // 0x15e3544: ldur            x0, [fp, #-8]
    // 0x15e3548: stur            x1, [fp, #-0x10]
    // 0x15e354c: StoreField: r1->field_7 = r0
    //     0x15e354c: stur            w0, [x1, #7]
    // 0x15e3550: r0 = Instance_BlendMode
    //     0x15e3550: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e3554: ldr             x0, [x0, #0xb30]
    // 0x15e3558: StoreField: r1->field_b = r0
    //     0x15e3558: stur            w0, [x1, #0xb]
    // 0x15e355c: r0 = 1
    //     0x15e355c: movz            x0, #0x1
    // 0x15e3560: StoreField: r1->field_13 = r0
    //     0x15e3560: stur            x0, [x1, #0x13]
    // 0x15e3564: r0 = SvgPicture()
    //     0x15e3564: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e3568: stur            x0, [fp, #-8]
    // 0x15e356c: ldur            x16, [fp, #-0x10]
    // 0x15e3570: str             x16, [SP]
    // 0x15e3574: mov             x1, x0
    // 0x15e3578: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e3578: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e357c: ldr             x2, [x2, #0xa40]
    // 0x15e3580: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e3580: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e3584: ldr             x4, [x4, #0xa38]
    // 0x15e3588: r0 = SvgPicture.asset()
    //     0x15e3588: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e358c: r0 = Align()
    //     0x15e358c: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e3590: mov             x1, x0
    // 0x15e3594: r0 = Instance_Alignment
    //     0x15e3594: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e3598: ldr             x0, [x0, #0xb10]
    // 0x15e359c: stur            x1, [fp, #-0x10]
    // 0x15e35a0: StoreField: r1->field_f = r0
    //     0x15e35a0: stur            w0, [x1, #0xf]
    // 0x15e35a4: r0 = 1.000000
    //     0x15e35a4: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e35a8: StoreField: r1->field_13 = r0
    //     0x15e35a8: stur            w0, [x1, #0x13]
    // 0x15e35ac: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e35ac: stur            w0, [x1, #0x17]
    // 0x15e35b0: ldur            x0, [fp, #-8]
    // 0x15e35b4: StoreField: r1->field_b = r0
    //     0x15e35b4: stur            w0, [x1, #0xb]
    // 0x15e35b8: r0 = InkWell()
    //     0x15e35b8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e35bc: mov             x3, x0
    // 0x15e35c0: ldur            x0, [fp, #-0x10]
    // 0x15e35c4: stur            x3, [fp, #-8]
    // 0x15e35c8: StoreField: r3->field_b = r0
    //     0x15e35c8: stur            w0, [x3, #0xb]
    // 0x15e35cc: ldur            x2, [fp, #-0x18]
    // 0x15e35d0: r1 = Function '<anonymous closure>':.
    //     0x15e35d0: add             x1, PP, #0x40, lsl #12  ; [pp+0x400e8] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15e35d4: ldr             x1, [x1, #0xe8]
    // 0x15e35d8: r0 = AllocateClosure()
    //     0x15e35d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e35dc: ldur            x2, [fp, #-8]
    // 0x15e35e0: StoreField: r2->field_f = r0
    //     0x15e35e0: stur            w0, [x2, #0xf]
    // 0x15e35e4: r0 = true
    //     0x15e35e4: add             x0, NULL, #0x20  ; true
    // 0x15e35e8: StoreField: r2->field_43 = r0
    //     0x15e35e8: stur            w0, [x2, #0x43]
    // 0x15e35ec: r1 = Instance_BoxShape
    //     0x15e35ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e35f0: ldr             x1, [x1, #0x80]
    // 0x15e35f4: StoreField: r2->field_47 = r1
    //     0x15e35f4: stur            w1, [x2, #0x47]
    // 0x15e35f8: StoreField: r2->field_6f = r0
    //     0x15e35f8: stur            w0, [x2, #0x6f]
    // 0x15e35fc: r1 = false
    //     0x15e35fc: add             x1, NULL, #0x30  ; false
    // 0x15e3600: StoreField: r2->field_73 = r1
    //     0x15e3600: stur            w1, [x2, #0x73]
    // 0x15e3604: StoreField: r2->field_83 = r0
    //     0x15e3604: stur            w0, [x2, #0x83]
    // 0x15e3608: StoreField: r2->field_7b = r1
    //     0x15e3608: stur            w1, [x2, #0x7b]
    // 0x15e360c: r0 = AppBar()
    //     0x15e360c: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e3610: stur            x0, [fp, #-0x10]
    // 0x15e3614: ldur            x16, [fp, #-0x20]
    // 0x15e3618: str             x16, [SP]
    // 0x15e361c: mov             x1, x0
    // 0x15e3620: ldur            x2, [fp, #-8]
    // 0x15e3624: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e3624: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e3628: ldr             x4, [x4, #0xf00]
    // 0x15e362c: r0 = AppBar()
    //     0x15e362c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e3630: ldur            x0, [fp, #-0x10]
    // 0x15e3634: LeaveFrame
    //     0x15e3634: mov             SP, fp
    //     0x15e3638: ldp             fp, lr, [SP], #0x10
    // 0x15e363c: ret
    //     0x15e363c: ret             
    // 0x15e3640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e3640: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e3644: b               #0x15e34b4
  }
}
