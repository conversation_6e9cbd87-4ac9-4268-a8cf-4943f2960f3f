// lib: , url: package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart

// class id: 1049294, size: 0x8
class :: {
}

// class id: 4596, size: 0x14, field offset: 0x14
//   const constructor, 
class OrdersView extends BaseView<dynamic> {

  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x85fe84, size: 0x44
    // 0x85fe84: EnterFrame
    //     0x85fe84: stp             fp, lr, [SP, #-0x10]!
    //     0x85fe88: mov             fp, SP
    // 0x85fe8c: AllocStack(0x8)
    //     0x85fe8c: sub             SP, SP, #8
    // 0x85fe90: CheckStackOverflow
    //     0x85fe90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85fe94: cmp             SP, x16
    //     0x85fe98: b.ls            #0x85fec0
    // 0x85fe9c: r0 = Container()
    //     0x85fe9c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x85fea0: mov             x1, x0
    // 0x85fea4: stur            x0, [fp, #-8]
    // 0x85fea8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x85fea8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x85feac: r0 = Container()
    //     0x85feac: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x85feb0: ldur            x0, [fp, #-8]
    // 0x85feb4: LeaveFrame
    //     0x85feb4: mov             SP, fp
    //     0x85feb8: ldp             fp, lr, [SP], #0x10
    // 0x85febc: ret
    //     0x85febc: ret             
    // 0x85fec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85fec0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85fec4: b               #0x85fe9c
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x85fec8, size: 0x400
    // 0x85fec8: EnterFrame
    //     0x85fec8: stp             fp, lr, [SP, #-0x10]!
    //     0x85fecc: mov             fp, SP
    // 0x85fed0: AllocStack(0x58)
    //     0x85fed0: sub             SP, SP, #0x58
    // 0x85fed4: SetupParameters()
    //     0x85fed4: ldr             x0, [fp, #0x10]
    //     0x85fed8: ldur            w2, [x0, #0x17]
    //     0x85fedc: add             x2, x2, HEAP, lsl #32
    //     0x85fee0: stur            x2, [fp, #-8]
    // 0x85fee4: CheckStackOverflow
    //     0x85fee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85fee8: cmp             SP, x16
    //     0x85feec: b.ls            #0x8602c0
    // 0x85fef0: LoadField: r1 = r2->field_f
    //     0x85fef0: ldur            w1, [x2, #0xf]
    // 0x85fef4: DecompressPointer r1
    //     0x85fef4: add             x1, x1, HEAP, lsl #32
    // 0x85fef8: r0 = controller()
    //     0x85fef8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x85fefc: LoadField: r1 = r0->field_53
    //     0x85fefc: ldur            w1, [x0, #0x53]
    // 0x85ff00: DecompressPointer r1
    //     0x85ff00: add             x1, x1, HEAP, lsl #32
    // 0x85ff04: r0 = value()
    //     0x85ff04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x85ff08: LoadField: r1 = r0->field_b
    //     0x85ff08: ldur            w1, [x0, #0xb]
    // 0x85ff0c: DecompressPointer r1
    //     0x85ff0c: add             x1, x1, HEAP, lsl #32
    // 0x85ff10: cmp             w1, NULL
    // 0x85ff14: b.eq            #0x85ff40
    // 0x85ff18: ldur            x2, [fp, #-8]
    // 0x85ff1c: LoadField: r1 = r2->field_f
    //     0x85ff1c: ldur            w1, [x2, #0xf]
    // 0x85ff20: DecompressPointer r1
    //     0x85ff20: add             x1, x1, HEAP, lsl #32
    // 0x85ff24: r0 = controller()
    //     0x85ff24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x85ff28: LoadField: r1 = r0->field_63
    //     0x85ff28: ldur            w1, [x0, #0x63]
    // 0x85ff2c: DecompressPointer r1
    //     0x85ff2c: add             x1, x1, HEAP, lsl #32
    // 0x85ff30: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x85ff30: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x85ff34: r0 = toList()
    //     0x85ff34: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x85ff38: LoadField: r1 = r0->field_b
    //     0x85ff38: ldur            w1, [x0, #0xb]
    // 0x85ff3c: cbnz            w1, #0x85ffb8
    // 0x85ff40: ldur            x2, [fp, #-8]
    // 0x85ff44: LoadField: r1 = r2->field_f
    //     0x85ff44: ldur            w1, [x2, #0xf]
    // 0x85ff48: DecompressPointer r1
    //     0x85ff48: add             x1, x1, HEAP, lsl #32
    // 0x85ff4c: r0 = controller()
    //     0x85ff4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x85ff50: LoadField: r1 = r0->field_53
    //     0x85ff50: ldur            w1, [x0, #0x53]
    // 0x85ff54: DecompressPointer r1
    //     0x85ff54: add             x1, x1, HEAP, lsl #32
    // 0x85ff58: r0 = value()
    //     0x85ff58: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x85ff5c: LoadField: r1 = r0->field_b
    //     0x85ff5c: ldur            w1, [x0, #0xb]
    // 0x85ff60: DecompressPointer r1
    //     0x85ff60: add             x1, x1, HEAP, lsl #32
    // 0x85ff64: cmp             w1, NULL
    // 0x85ff68: b.ne            #0x85ff88
    // 0x85ff6c: r0 = Container()
    //     0x85ff6c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x85ff70: mov             x1, x0
    // 0x85ff74: stur            x0, [fp, #-0x10]
    // 0x85ff78: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x85ff78: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x85ff7c: r0 = Container()
    //     0x85ff7c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x85ff80: ldur            x0, [fp, #-0x10]
    // 0x85ff84: b               #0x85ff90
    // 0x85ff88: r0 = Instance_EmptyBagWidget
    //     0x85ff88: add             x0, PP, #0x36, lsl #12  ; [pp+0x36d38] Obj!EmptyBagWidget@d66d71
    //     0x85ff8c: ldr             x0, [x0, #0xd38]
    // 0x85ff90: stur            x0, [fp, #-0x10]
    // 0x85ff94: r0 = Center()
    //     0x85ff94: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x85ff98: mov             x1, x0
    // 0x85ff9c: r0 = Instance_Alignment
    //     0x85ff9c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x85ffa0: ldr             x0, [x0, #0xb10]
    // 0x85ffa4: StoreField: r1->field_f = r0
    //     0x85ffa4: stur            w0, [x1, #0xf]
    // 0x85ffa8: ldur            x0, [fp, #-0x10]
    // 0x85ffac: StoreField: r1->field_b = r0
    //     0x85ffac: stur            w0, [x1, #0xb]
    // 0x85ffb0: mov             x0, x1
    // 0x85ffb4: b               #0x8602b4
    // 0x85ffb8: ldur            x2, [fp, #-8]
    // 0x85ffbc: LoadField: r1 = r2->field_13
    //     0x85ffbc: ldur            w1, [x2, #0x13]
    // 0x85ffc0: DecompressPointer r1
    //     0x85ffc0: add             x1, x1, HEAP, lsl #32
    // 0x85ffc4: r0 = of()
    //     0x85ffc4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x85ffc8: LoadField: r1 = r0->field_87
    //     0x85ffc8: ldur            w1, [x0, #0x87]
    // 0x85ffcc: DecompressPointer r1
    //     0x85ffcc: add             x1, x1, HEAP, lsl #32
    // 0x85ffd0: LoadField: r0 = r1->field_7
    //     0x85ffd0: ldur            w0, [x1, #7]
    // 0x85ffd4: DecompressPointer r0
    //     0x85ffd4: add             x0, x0, HEAP, lsl #32
    // 0x85ffd8: r16 = 16.000000
    //     0x85ffd8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x85ffdc: ldr             x16, [x16, #0x188]
    // 0x85ffe0: r30 = Instance_Color
    //     0x85ffe0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x85ffe4: stp             lr, x16, [SP]
    // 0x85ffe8: mov             x1, x0
    // 0x85ffec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x85ffec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x85fff0: ldr             x4, [x4, #0xaa0]
    // 0x85fff4: r0 = copyWith()
    //     0x85fff4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x85fff8: stur            x0, [fp, #-0x10]
    // 0x85fffc: r0 = Text()
    //     0x85fffc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x860000: mov             x3, x0
    // 0x860004: r0 = "All Orders"
    //     0x860004: add             x0, PP, #0x40, lsl #12  ; [pp+0x40168] "All Orders"
    //     0x860008: ldr             x0, [x0, #0x168]
    // 0x86000c: stur            x3, [fp, #-0x18]
    // 0x860010: StoreField: r3->field_b = r0
    //     0x860010: stur            w0, [x3, #0xb]
    // 0x860014: ldur            x0, [fp, #-0x10]
    // 0x860018: StoreField: r3->field_13 = r0
    //     0x860018: stur            w0, [x3, #0x13]
    // 0x86001c: r1 = Null
    //     0x86001c: mov             x1, NULL
    // 0x860020: r2 = 2
    //     0x860020: movz            x2, #0x2
    // 0x860024: r0 = AllocateArray()
    //     0x860024: bl              #0x16f7198  ; AllocateArrayStub
    // 0x860028: mov             x2, x0
    // 0x86002c: ldur            x0, [fp, #-0x18]
    // 0x860030: stur            x2, [fp, #-0x10]
    // 0x860034: StoreField: r2->field_f = r0
    //     0x860034: stur            w0, [x2, #0xf]
    // 0x860038: r1 = <Widget>
    //     0x860038: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x86003c: r0 = AllocateGrowableArray()
    //     0x86003c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x860040: mov             x1, x0
    // 0x860044: ldur            x0, [fp, #-0x10]
    // 0x860048: stur            x1, [fp, #-0x18]
    // 0x86004c: StoreField: r1->field_f = r0
    //     0x86004c: stur            w0, [x1, #0xf]
    // 0x860050: r0 = 2
    //     0x860050: movz            x0, #0x2
    // 0x860054: StoreField: r1->field_b = r0
    //     0x860054: stur            w0, [x1, #0xb]
    // 0x860058: r0 = Column()
    //     0x860058: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x86005c: mov             x1, x0
    // 0x860060: r0 = Instance_Axis
    //     0x860060: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x860064: stur            x1, [fp, #-0x10]
    // 0x860068: StoreField: r1->field_f = r0
    //     0x860068: stur            w0, [x1, #0xf]
    // 0x86006c: r2 = Instance_MainAxisAlignment
    //     0x86006c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x860070: ldr             x2, [x2, #0xa08]
    // 0x860074: StoreField: r1->field_13 = r2
    //     0x860074: stur            w2, [x1, #0x13]
    // 0x860078: r3 = Instance_MainAxisSize
    //     0x860078: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x86007c: ldr             x3, [x3, #0xa10]
    // 0x860080: ArrayStore: r1[0] = r3  ; List_4
    //     0x860080: stur            w3, [x1, #0x17]
    // 0x860084: r4 = Instance_CrossAxisAlignment
    //     0x860084: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x860088: ldr             x4, [x4, #0x890]
    // 0x86008c: StoreField: r1->field_1b = r4
    //     0x86008c: stur            w4, [x1, #0x1b]
    // 0x860090: r4 = Instance_VerticalDirection
    //     0x860090: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x860094: ldr             x4, [x4, #0xa20]
    // 0x860098: StoreField: r1->field_23 = r4
    //     0x860098: stur            w4, [x1, #0x23]
    // 0x86009c: r5 = Instance_Clip
    //     0x86009c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x8600a0: ldr             x5, [x5, #0x38]
    // 0x8600a4: StoreField: r1->field_2b = r5
    //     0x8600a4: stur            w5, [x1, #0x2b]
    // 0x8600a8: StoreField: r1->field_2f = rZR
    //     0x8600a8: stur            xzr, [x1, #0x2f]
    // 0x8600ac: ldur            x6, [fp, #-0x18]
    // 0x8600b0: StoreField: r1->field_b = r6
    //     0x8600b0: stur            w6, [x1, #0xb]
    // 0x8600b4: r0 = Align()
    //     0x8600b4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x8600b8: mov             x2, x0
    // 0x8600bc: r0 = Instance_Alignment
    //     0x8600bc: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f98] Obj!Alignment@d5a7a1
    //     0x8600c0: ldr             x0, [x0, #0xf98]
    // 0x8600c4: stur            x2, [fp, #-0x18]
    // 0x8600c8: StoreField: r2->field_f = r0
    //     0x8600c8: stur            w0, [x2, #0xf]
    // 0x8600cc: ldur            x0, [fp, #-0x10]
    // 0x8600d0: StoreField: r2->field_b = r0
    //     0x8600d0: stur            w0, [x2, #0xb]
    // 0x8600d4: r1 = Instance_Color
    //     0x8600d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8600d8: d0 = 0.100000
    //     0x8600d8: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x8600dc: r0 = withOpacity()
    //     0x8600dc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8600e0: stur            x0, [fp, #-0x10]
    // 0x8600e4: r0 = Divider()
    //     0x8600e4: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x8600e8: mov             x2, x0
    // 0x8600ec: ldur            x0, [fp, #-0x10]
    // 0x8600f0: stur            x2, [fp, #-0x20]
    // 0x8600f4: StoreField: r2->field_1f = r0
    //     0x8600f4: stur            w0, [x2, #0x1f]
    // 0x8600f8: ldur            x0, [fp, #-8]
    // 0x8600fc: LoadField: r1 = r0->field_f
    //     0x8600fc: ldur            w1, [x0, #0xf]
    // 0x860100: DecompressPointer r1
    //     0x860100: add             x1, x1, HEAP, lsl #32
    // 0x860104: r0 = controller()
    //     0x860104: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x860108: LoadField: r1 = r0->field_63
    //     0x860108: ldur            w1, [x0, #0x63]
    // 0x86010c: DecompressPointer r1
    //     0x86010c: add             x1, x1, HEAP, lsl #32
    // 0x860110: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x860110: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x860114: r0 = toList()
    //     0x860114: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x860118: LoadField: r1 = r0->field_b
    //     0x860118: ldur            w1, [x0, #0xb]
    // 0x86011c: r3 = LoadInt32Instr(r1)
    //     0x86011c: sbfx            x3, x1, #1, #0x1f
    // 0x860120: ldur            x2, [fp, #-8]
    // 0x860124: stur            x3, [fp, #-0x28]
    // 0x860128: r1 = Function '<anonymous closure>':.
    //     0x860128: add             x1, PP, #0x43, lsl #12  ; [pp+0x43110] AnonymousClosure: (0x8b5808), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::body (0x14a879c)
    //     0x86012c: ldr             x1, [x1, #0x110]
    // 0x860130: r0 = AllocateClosure()
    //     0x860130: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x860134: r1 = Function '<anonymous closure>':.
    //     0x860134: add             x1, PP, #0x43, lsl #12  ; [pp+0x43118] AnonymousClosure: (0x85fe84), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::body (0x14a879c)
    //     0x860138: ldr             x1, [x1, #0x118]
    // 0x86013c: r2 = Null
    //     0x86013c: mov             x2, NULL
    // 0x860140: stur            x0, [fp, #-0x10]
    // 0x860144: r0 = AllocateClosure()
    //     0x860144: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x860148: stur            x0, [fp, #-0x30]
    // 0x86014c: r0 = ListView()
    //     0x86014c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0x860150: stur            x0, [fp, #-0x38]
    // 0x860154: r16 = Instance_Axis
    //     0x860154: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x860158: r30 = true
    //     0x860158: add             lr, NULL, #0x20  ; true
    // 0x86015c: stp             lr, x16, [SP, #0x10]
    // 0x860160: r16 = Instance_ClampingScrollPhysics
    //     0x860160: add             x16, PP, #0x36, lsl #12  ; [pp+0x36d58] Obj!ClampingScrollPhysics@d558d1
    //     0x860164: ldr             x16, [x16, #0xd58]
    // 0x860168: r30 = true
    //     0x860168: add             lr, NULL, #0x20  ; true
    // 0x86016c: stp             lr, x16, [SP]
    // 0x860170: mov             x1, x0
    // 0x860174: ldur            x2, [fp, #-0x10]
    // 0x860178: ldur            x3, [fp, #-0x28]
    // 0x86017c: ldur            x5, [fp, #-0x30]
    // 0x860180: r4 = const [0, 0x8, 0x4, 0x4, physics, 0x6, primary, 0x7, scrollDirection, 0x4, shrinkWrap, 0x5, null]
    //     0x860180: add             x4, PP, #0x36, lsl #12  ; [pp+0x36d60] List(13) [0, 0x8, 0x4, 0x4, "physics", 0x6, "primary", 0x7, "scrollDirection", 0x4, "shrinkWrap", 0x5, Null]
    //     0x860184: ldr             x4, [x4, #0xd60]
    // 0x860188: r0 = ListView.separated()
    //     0x860188: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0x86018c: r1 = Null
    //     0x86018c: mov             x1, NULL
    // 0x860190: r2 = 10
    //     0x860190: movz            x2, #0xa
    // 0x860194: r0 = AllocateArray()
    //     0x860194: bl              #0x16f7198  ; AllocateArrayStub
    // 0x860198: mov             x2, x0
    // 0x86019c: ldur            x0, [fp, #-0x18]
    // 0x8601a0: stur            x2, [fp, #-0x10]
    // 0x8601a4: StoreField: r2->field_f = r0
    //     0x8601a4: stur            w0, [x2, #0xf]
    // 0x8601a8: r16 = Instance_SizedBox
    //     0x8601a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x8601ac: ldr             x16, [x16, #0x8f0]
    // 0x8601b0: StoreField: r2->field_13 = r16
    //     0x8601b0: stur            w16, [x2, #0x13]
    // 0x8601b4: ldur            x0, [fp, #-0x20]
    // 0x8601b8: ArrayStore: r2[0] = r0  ; List_4
    //     0x8601b8: stur            w0, [x2, #0x17]
    // 0x8601bc: r16 = Instance_SizedBox
    //     0x8601bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x8601c0: ldr             x16, [x16, #0x8f0]
    // 0x8601c4: StoreField: r2->field_1b = r16
    //     0x8601c4: stur            w16, [x2, #0x1b]
    // 0x8601c8: ldur            x0, [fp, #-0x38]
    // 0x8601cc: StoreField: r2->field_1f = r0
    //     0x8601cc: stur            w0, [x2, #0x1f]
    // 0x8601d0: r1 = <Widget>
    //     0x8601d0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x8601d4: r0 = AllocateGrowableArray()
    //     0x8601d4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x8601d8: mov             x1, x0
    // 0x8601dc: ldur            x0, [fp, #-0x10]
    // 0x8601e0: stur            x1, [fp, #-0x18]
    // 0x8601e4: StoreField: r1->field_f = r0
    //     0x8601e4: stur            w0, [x1, #0xf]
    // 0x8601e8: r0 = 10
    //     0x8601e8: movz            x0, #0xa
    // 0x8601ec: StoreField: r1->field_b = r0
    //     0x8601ec: stur            w0, [x1, #0xb]
    // 0x8601f0: r0 = Column()
    //     0x8601f0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x8601f4: mov             x1, x0
    // 0x8601f8: r0 = Instance_Axis
    //     0x8601f8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x8601fc: stur            x1, [fp, #-0x10]
    // 0x860200: StoreField: r1->field_f = r0
    //     0x860200: stur            w0, [x1, #0xf]
    // 0x860204: r0 = Instance_MainAxisAlignment
    //     0x860204: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x860208: ldr             x0, [x0, #0xa08]
    // 0x86020c: StoreField: r1->field_13 = r0
    //     0x86020c: stur            w0, [x1, #0x13]
    // 0x860210: r0 = Instance_MainAxisSize
    //     0x860210: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x860214: ldr             x0, [x0, #0xa10]
    // 0x860218: ArrayStore: r1[0] = r0  ; List_4
    //     0x860218: stur            w0, [x1, #0x17]
    // 0x86021c: r0 = Instance_CrossAxisAlignment
    //     0x86021c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x860220: ldr             x0, [x0, #0xa18]
    // 0x860224: StoreField: r1->field_1b = r0
    //     0x860224: stur            w0, [x1, #0x1b]
    // 0x860228: r0 = Instance_VerticalDirection
    //     0x860228: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x86022c: ldr             x0, [x0, #0xa20]
    // 0x860230: StoreField: r1->field_23 = r0
    //     0x860230: stur            w0, [x1, #0x23]
    // 0x860234: r0 = Instance_Clip
    //     0x860234: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x860238: ldr             x0, [x0, #0x38]
    // 0x86023c: StoreField: r1->field_2b = r0
    //     0x86023c: stur            w0, [x1, #0x2b]
    // 0x860240: StoreField: r1->field_2f = rZR
    //     0x860240: stur            xzr, [x1, #0x2f]
    // 0x860244: ldur            x0, [fp, #-0x18]
    // 0x860248: StoreField: r1->field_b = r0
    //     0x860248: stur            w0, [x1, #0xb]
    // 0x86024c: r0 = Padding()
    //     0x86024c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x860250: mov             x3, x0
    // 0x860254: r0 = Instance_EdgeInsets
    //     0x860254: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x860258: ldr             x0, [x0, #0x1f0]
    // 0x86025c: stur            x3, [fp, #-0x18]
    // 0x860260: StoreField: r3->field_f = r0
    //     0x860260: stur            w0, [x3, #0xf]
    // 0x860264: ldur            x0, [fp, #-0x10]
    // 0x860268: StoreField: r3->field_b = r0
    //     0x860268: stur            w0, [x3, #0xb]
    // 0x86026c: ldur            x2, [fp, #-8]
    // 0x860270: r1 = Function '<anonymous closure>':.
    //     0x860270: add             x1, PP, #0x43, lsl #12  ; [pp+0x43120] AnonymousClosure: (0x8a41fc), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x860274: ldr             x1, [x1, #0x120]
    // 0x860278: r0 = AllocateClosure()
    //     0x860278: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x86027c: ldur            x2, [fp, #-8]
    // 0x860280: r1 = Function '<anonymous closure>':.
    //     0x860280: add             x1, PP, #0x43, lsl #12  ; [pp+0x43128] AnonymousClosure: (0x8a3de0), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x860284: ldr             x1, [x1, #0x128]
    // 0x860288: stur            x0, [fp, #-8]
    // 0x86028c: r0 = AllocateClosure()
    //     0x86028c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x860290: stur            x0, [fp, #-0x10]
    // 0x860294: r0 = PagingView()
    //     0x860294: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x860298: mov             x1, x0
    // 0x86029c: ldur            x2, [fp, #-0x18]
    // 0x8602a0: ldur            x3, [fp, #-0x10]
    // 0x8602a4: ldur            x5, [fp, #-8]
    // 0x8602a8: stur            x0, [fp, #-8]
    // 0x8602ac: r0 = PagingView()
    //     0x8602ac: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x8602b0: ldur            x0, [fp, #-8]
    // 0x8602b4: LeaveFrame
    //     0x8602b4: mov             SP, fp
    //     0x8602b8: ldp             fp, lr, [SP], #0x10
    // 0x8602bc: ret
    //     0x8602bc: ret             
    // 0x8602c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8602c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8602c4: b               #0x85fef0
  }
  [closure] OrderItemCard <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x8b5808, size: 0x1c4
    // 0x8b5808: EnterFrame
    //     0x8b5808: stp             fp, lr, [SP, #-0x10]!
    //     0x8b580c: mov             fp, SP
    // 0x8b5810: AllocStack(0x28)
    //     0x8b5810: sub             SP, SP, #0x28
    // 0x8b5814: SetupParameters()
    //     0x8b5814: ldr             x0, [fp, #0x20]
    //     0x8b5818: ldur            w2, [x0, #0x17]
    //     0x8b581c: add             x2, x2, HEAP, lsl #32
    //     0x8b5820: stur            x2, [fp, #-0x10]
    // 0x8b5824: CheckStackOverflow
    //     0x8b5824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5828: cmp             SP, x16
    //     0x8b582c: b.ls            #0x8b59c0
    // 0x8b5830: LoadField: r0 = r2->field_f
    //     0x8b5830: ldur            w0, [x2, #0xf]
    // 0x8b5834: DecompressPointer r0
    //     0x8b5834: add             x0, x0, HEAP, lsl #32
    // 0x8b5838: mov             x1, x0
    // 0x8b583c: stur            x0, [fp, #-8]
    // 0x8b5840: r0 = controller()
    //     0x8b5840: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5844: LoadField: r1 = r0->field_63
    //     0x8b5844: ldur            w1, [x0, #0x63]
    // 0x8b5848: DecompressPointer r1
    //     0x8b5848: add             x1, x1, HEAP, lsl #32
    // 0x8b584c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8b584c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8b5850: r0 = toList()
    //     0x8b5850: bl              #0x78afa0  ; [dart:collection] ListBase::toList
    // 0x8b5854: mov             x2, x0
    // 0x8b5858: LoadField: r0 = r2->field_b
    //     0x8b5858: ldur            w0, [x2, #0xb]
    // 0x8b585c: ldr             x1, [fp, #0x10]
    // 0x8b5860: r3 = LoadInt32Instr(r1)
    //     0x8b5860: sbfx            x3, x1, #1, #0x1f
    //     0x8b5864: tbz             w1, #0, #0x8b586c
    //     0x8b5868: ldur            x3, [x1, #7]
    // 0x8b586c: r1 = LoadInt32Instr(r0)
    //     0x8b586c: sbfx            x1, x0, #1, #0x1f
    // 0x8b5870: mov             x0, x1
    // 0x8b5874: mov             x1, x3
    // 0x8b5878: cmp             x1, x0
    // 0x8b587c: b.hs            #0x8b59c8
    // 0x8b5880: LoadField: r0 = r2->field_f
    //     0x8b5880: ldur            w0, [x2, #0xf]
    // 0x8b5884: DecompressPointer r0
    //     0x8b5884: add             x0, x0, HEAP, lsl #32
    // 0x8b5888: ArrayLoad: r4 = r0[r3]  ; Unknown_4
    //     0x8b5888: add             x16, x0, x3, lsl #2
    //     0x8b588c: ldur            w4, [x16, #0xf]
    // 0x8b5890: DecompressPointer r4
    //     0x8b5890: add             x4, x4, HEAP, lsl #32
    // 0x8b5894: mov             x0, x4
    // 0x8b5898: stur            x4, [fp, #-0x18]
    // 0x8b589c: r2 = Null
    //     0x8b589c: mov             x2, NULL
    // 0x8b58a0: r1 = Null
    //     0x8b58a0: mov             x1, NULL
    // 0x8b58a4: r4 = LoadClassIdInstr(r0)
    //     0x8b58a4: ldur            x4, [x0, #-1]
    //     0x8b58a8: ubfx            x4, x4, #0xc, #0x14
    // 0x8b58ac: r17 = 5137
    //     0x8b58ac: movz            x17, #0x1411
    // 0x8b58b0: cmp             x4, x17
    // 0x8b58b4: b.eq            #0x8b58cc
    // 0x8b58b8: r8 = OrderData
    //     0x8b58b8: add             x8, PP, #0x36, lsl #12  ; [pp+0x36d98] Type: OrderData
    //     0x8b58bc: ldr             x8, [x8, #0xd98]
    // 0x8b58c0: r3 = Null
    //     0x8b58c0: add             x3, PP, #0x43, lsl #12  ; [pp+0x43130] Null
    //     0x8b58c4: ldr             x3, [x3, #0x130]
    // 0x8b58c8: r0 = DefaultTypeTest()
    //     0x8b58c8: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x8b58cc: ldur            x2, [fp, #-0x10]
    // 0x8b58d0: LoadField: r0 = r2->field_f
    //     0x8b58d0: ldur            w0, [x2, #0xf]
    // 0x8b58d4: DecompressPointer r0
    //     0x8b58d4: add             x0, x0, HEAP, lsl #32
    // 0x8b58d8: stur            x0, [fp, #-0x20]
    // 0x8b58dc: r0 = OrderItemCard()
    //     0x8b58dc: bl              #0x8b59cc  ; AllocateOrderItemCardStub -> OrderItemCard (size=0x2c)
    // 0x8b58e0: mov             x3, x0
    // 0x8b58e4: ldur            x0, [fp, #-0x18]
    // 0x8b58e8: stur            x3, [fp, #-0x28]
    // 0x8b58ec: StoreField: r3->field_b = r0
    //     0x8b58ec: stur            w0, [x3, #0xb]
    // 0x8b58f0: ldur            x2, [fp, #-8]
    // 0x8b58f4: r1 = Function 'cancelOrder':.
    //     0x8b58f4: add             x1, PP, #0x43, lsl #12  ; [pp+0x43140] AnonymousClosure: (0x8b6d54), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrder (0x8b66f4)
    //     0x8b58f8: ldr             x1, [x1, #0x140]
    // 0x8b58fc: r0 = AllocateClosure()
    //     0x8b58fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5900: mov             x1, x0
    // 0x8b5904: ldur            x0, [fp, #-0x28]
    // 0x8b5908: StoreField: r0->field_f = r1
    //     0x8b5908: stur            w1, [x0, #0xf]
    // 0x8b590c: ldur            x2, [fp, #-8]
    // 0x8b5910: r1 = Function 'cancelOrderWithFreeProduct':.
    //     0x8b5910: add             x1, PP, #0x43, lsl #12  ; [pp+0x43148] AnonymousClosure: (0x8b61fc), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x8b6240)
    //     0x8b5914: ldr             x1, [x1, #0x148]
    // 0x8b5918: r0 = AllocateClosure()
    //     0x8b5918: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b591c: mov             x1, x0
    // 0x8b5920: ldur            x0, [fp, #-0x28]
    // 0x8b5924: StoreField: r0->field_13 = r1
    //     0x8b5924: stur            w1, [x0, #0x13]
    // 0x8b5928: ldur            x2, [fp, #-8]
    // 0x8b592c: r1 = Function 'openProductDetail':.
    //     0x8b592c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43150] AnonymousClosure: (0x8b5f90), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::openProductDetail (0x8b5fdc)
    //     0x8b5930: ldr             x1, [x1, #0x150]
    // 0x8b5934: r0 = AllocateClosure()
    //     0x8b5934: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5938: mov             x1, x0
    // 0x8b593c: ldur            x0, [fp, #-0x28]
    // 0x8b5940: StoreField: r0->field_1f = r1
    //     0x8b5940: stur            w1, [x0, #0x1f]
    // 0x8b5944: ldur            x2, [fp, #-0x20]
    // 0x8b5948: r1 = Function 'cancelReturn':.
    //     0x8b5948: add             x1, PP, #0x43, lsl #12  ; [pp+0x43158] AnonymousClosure: (0x8b5de4), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelReturn (0x8b5e28)
    //     0x8b594c: ldr             x1, [x1, #0x158]
    // 0x8b5950: r0 = AllocateClosure()
    //     0x8b5950: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5954: mov             x1, x0
    // 0x8b5958: ldur            x0, [fp, #-0x28]
    // 0x8b595c: ArrayStore: r0[0] = r1  ; List_4
    //     0x8b595c: stur            w1, [x0, #0x17]
    // 0x8b5960: ldur            x2, [fp, #-0x20]
    // 0x8b5964: r1 = Function 'cancelExchange':.
    //     0x8b5964: add             x1, PP, #0x43, lsl #12  ; [pp+0x43160] AnonymousClosure: (0x8b5bcc), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelExchange (0x8b5c34)
    //     0x8b5968: ldr             x1, [x1, #0x160]
    // 0x8b596c: r0 = AllocateClosure()
    //     0x8b596c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5970: mov             x1, x0
    // 0x8b5974: ldur            x0, [fp, #-0x28]
    // 0x8b5978: StoreField: r0->field_1b = r1
    //     0x8b5978: stur            w1, [x0, #0x1b]
    // 0x8b597c: ldur            x2, [fp, #-0x10]
    // 0x8b5980: r1 = Function '<anonymous closure>':.
    //     0x8b5980: add             x1, PP, #0x43, lsl #12  ; [pp+0x43168] AnonymousClosure: (0x8adc64), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8b5984: ldr             x1, [x1, #0x168]
    // 0x8b5988: r0 = AllocateClosure()
    //     0x8b5988: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b598c: mov             x1, x0
    // 0x8b5990: ldur            x0, [fp, #-0x28]
    // 0x8b5994: StoreField: r0->field_23 = r1
    //     0x8b5994: stur            w1, [x0, #0x23]
    // 0x8b5998: ldur            x2, [fp, #-0x10]
    // 0x8b599c: r1 = Function '<anonymous closure>':.
    //     0x8b599c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43170] AnonymousClosure: (0x8b59fc), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::body (0x14a879c)
    //     0x8b59a0: ldr             x1, [x1, #0x170]
    // 0x8b59a4: r0 = AllocateClosure()
    //     0x8b59a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b59a8: mov             x1, x0
    // 0x8b59ac: ldur            x0, [fp, #-0x28]
    // 0x8b59b0: StoreField: r0->field_27 = r1
    //     0x8b59b0: stur            w1, [x0, #0x27]
    // 0x8b59b4: LeaveFrame
    //     0x8b59b4: mov             SP, fp
    //     0x8b59b8: ldp             fp, lr, [SP], #0x10
    // 0x8b59bc: ret
    //     0x8b59bc: ret             
    // 0x8b59c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b59c0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b59c4: b               #0x8b5830
    // 0x8b59c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8b59c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, double, Items, String) {
    // ** addr: 0x8b59fc, size: 0x1d0
    // 0x8b59fc: EnterFrame
    //     0x8b59fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5a00: mov             fp, SP
    // 0x8b5a04: AllocStack(0x28)
    //     0x8b5a04: sub             SP, SP, #0x28
    // 0x8b5a08: SetupParameters()
    //     0x8b5a08: ldr             x0, [fp, #0x28]
    //     0x8b5a0c: ldur            w2, [x0, #0x17]
    //     0x8b5a10: add             x2, x2, HEAP, lsl #32
    //     0x8b5a14: stur            x2, [fp, #-8]
    // 0x8b5a18: CheckStackOverflow
    //     0x8b5a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5a1c: cmp             SP, x16
    //     0x8b5a20: b.ls            #0x8b5bc4
    // 0x8b5a24: LoadField: r1 = r2->field_f
    //     0x8b5a24: ldur            w1, [x2, #0xf]
    // 0x8b5a28: DecompressPointer r1
    //     0x8b5a28: add             x1, x1, HEAP, lsl #32
    // 0x8b5a2c: r0 = controller()
    //     0x8b5a2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5a30: mov             x1, x0
    // 0x8b5a34: ldr             x0, [fp, #0x10]
    // 0x8b5a38: StoreField: r1->field_77 = r0
    //     0x8b5a38: stur            w0, [x1, #0x77]
    //     0x8b5a3c: ldurb           w16, [x1, #-1]
    //     0x8b5a40: ldurb           w17, [x0, #-1]
    //     0x8b5a44: and             x16, x17, x16, lsr #2
    //     0x8b5a48: tst             x16, HEAP, lsr #32
    //     0x8b5a4c: b.eq            #0x8b5a54
    //     0x8b5a50: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8b5a54: ldr             x1, [fp, #0x10]
    // 0x8b5a58: r0 = LoadClassIdInstr(r1)
    //     0x8b5a58: ldur            x0, [x1, #-1]
    //     0x8b5a5c: ubfx            x0, x0, #0xc, #0x14
    // 0x8b5a60: r16 = "stars"
    //     0x8b5a60: add             x16, PP, #0xe, lsl #12  ; [pp+0xeb28] "stars"
    //     0x8b5a64: ldr             x16, [x16, #0xb28]
    // 0x8b5a68: stp             x16, x1, [SP]
    // 0x8b5a6c: mov             lr, x0
    // 0x8b5a70: ldr             lr, [x21, lr, lsl #3]
    // 0x8b5a74: blr             lr
    // 0x8b5a78: tbz             w0, #4, #0x8b5b40
    // 0x8b5a7c: ldr             x1, [fp, #0x18]
    // 0x8b5a80: ldr             x0, [fp, #0x10]
    // 0x8b5a84: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b5a84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b5a88: ldr             x0, [x0, #0x1c80]
    //     0x8b5a8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b5a90: cmp             w0, w16
    //     0x8b5a94: b.ne            #0x8b5aa0
    //     0x8b5a98: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b5a9c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b5aa0: r1 = Null
    //     0x8b5aa0: mov             x1, NULL
    // 0x8b5aa4: r2 = 8
    //     0x8b5aa4: movz            x2, #0x8
    // 0x8b5aa8: r0 = AllocateArray()
    //     0x8b5aa8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8b5aac: r16 = "order_id"
    //     0x8b5aac: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x8b5ab0: ldr             x16, [x16, #0xa38]
    // 0x8b5ab4: StoreField: r0->field_f = r16
    //     0x8b5ab4: stur            w16, [x0, #0xf]
    // 0x8b5ab8: ldr             x2, [fp, #0x18]
    // 0x8b5abc: LoadField: r1 = r2->field_7
    //     0x8b5abc: ldur            w1, [x2, #7]
    // 0x8b5ac0: DecompressPointer r1
    //     0x8b5ac0: add             x1, x1, HEAP, lsl #32
    // 0x8b5ac4: StoreField: r0->field_13 = r1
    //     0x8b5ac4: stur            w1, [x0, #0x13]
    // 0x8b5ac8: r16 = "coming_from"
    //     0x8b5ac8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x8b5acc: ldr             x16, [x16, #0x328]
    // 0x8b5ad0: ArrayStore: r0[0] = r16  ; List_4
    //     0x8b5ad0: stur            w16, [x0, #0x17]
    // 0x8b5ad4: ldr             x1, [fp, #0x10]
    // 0x8b5ad8: StoreField: r0->field_1b = r1
    //     0x8b5ad8: stur            w1, [x0, #0x1b]
    // 0x8b5adc: r16 = <String, String?>
    //     0x8b5adc: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x8b5ae0: ldr             x16, [x16, #0x3c8]
    // 0x8b5ae4: stp             x0, x16, [SP]
    // 0x8b5ae8: r0 = Map._fromLiteral()
    //     0x8b5ae8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x8b5aec: r16 = "/rating_review_for_order"
    //     0x8b5aec: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9b8] "/rating_review_for_order"
    //     0x8b5af0: ldr             x16, [x16, #0x9b8]
    // 0x8b5af4: stp             x16, NULL, [SP, #8]
    // 0x8b5af8: str             x0, [SP]
    // 0x8b5afc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x8b5afc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x8b5b00: ldr             x4, [x4, #0x438]
    // 0x8b5b04: r0 = GetNavigation.toNamed()
    //     0x8b5b04: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x8b5b08: stur            x0, [fp, #-0x10]
    // 0x8b5b0c: cmp             w0, NULL
    // 0x8b5b10: b.eq            #0x8b5bb4
    // 0x8b5b14: ldur            x2, [fp, #-8]
    // 0x8b5b18: r1 = Function '<anonymous closure>':.
    //     0x8b5b18: add             x1, PP, #0x43, lsl #12  ; [pp+0x43178] AnonymousClosure: (0x8adbf4), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::body (0x15069cc)
    //     0x8b5b1c: ldr             x1, [x1, #0x178]
    // 0x8b5b20: r0 = AllocateClosure()
    //     0x8b5b20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5b24: r16 = <Null?>
    //     0x8b5b24: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x8b5b28: ldur            lr, [fp, #-0x10]
    // 0x8b5b2c: stp             lr, x16, [SP, #8]
    // 0x8b5b30: str             x0, [SP]
    // 0x8b5b34: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8b5b34: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8b5b38: r0 = then()
    //     0x8b5b38: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x8b5b3c: b               #0x8b5bb4
    // 0x8b5b40: ldr             x3, [fp, #0x20]
    // 0x8b5b44: ldr             x2, [fp, #0x18]
    // 0x8b5b48: ldur            x0, [fp, #-8]
    // 0x8b5b4c: LoadField: r1 = r0->field_f
    //     0x8b5b4c: ldur            w1, [x0, #0xf]
    // 0x8b5b50: DecompressPointer r1
    //     0x8b5b50: add             x1, x1, HEAP, lsl #32
    // 0x8b5b54: r0 = controller()
    //     0x8b5b54: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5b58: mov             x1, x0
    // 0x8b5b5c: ldr             x0, [fp, #0x18]
    // 0x8b5b60: StoreField: r1->field_8b = r0
    //     0x8b5b60: stur            w0, [x1, #0x8b]
    //     0x8b5b64: ldurb           w16, [x1, #-1]
    //     0x8b5b68: ldurb           w17, [x0, #-1]
    //     0x8b5b6c: and             x16, x17, x16, lsr #2
    //     0x8b5b70: tst             x16, HEAP, lsr #32
    //     0x8b5b74: b.eq            #0x8b5b7c
    //     0x8b5b78: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8b5b7c: ldur            x0, [fp, #-8]
    // 0x8b5b80: LoadField: r1 = r0->field_f
    //     0x8b5b80: ldur            w1, [x0, #0xf]
    // 0x8b5b84: DecompressPointer r1
    //     0x8b5b84: add             x1, x1, HEAP, lsl #32
    // 0x8b5b88: r0 = controller()
    //     0x8b5b88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5b8c: mov             x1, x0
    // 0x8b5b90: ldr             x0, [fp, #0x20]
    // 0x8b5b94: LoadField: d0 = r0->field_7
    //     0x8b5b94: ldur            d0, [x0, #7]
    // 0x8b5b98: StoreField: r1->field_8f = d0
    //     0x8b5b98: stur            d0, [x1, #0x8f]
    // 0x8b5b9c: ldur            x0, [fp, #-8]
    // 0x8b5ba0: LoadField: r1 = r0->field_f
    //     0x8b5ba0: ldur            w1, [x0, #0xf]
    // 0x8b5ba4: DecompressPointer r1
    //     0x8b5ba4: add             x1, x1, HEAP, lsl #32
    // 0x8b5ba8: r0 = controller()
    //     0x8b5ba8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5bac: mov             x1, x0
    // 0x8b5bb0: r0 = createReview()
    //     0x8b5bb0: bl              #0x8aa48c  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::createReview
    // 0x8b5bb4: r0 = Null
    //     0x8b5bb4: mov             x0, NULL
    // 0x8b5bb8: LeaveFrame
    //     0x8b5bb8: mov             SP, fp
    //     0x8b5bbc: ldp             fp, lr, [SP], #0x10
    // 0x8b5bc0: ret
    //     0x8b5bc0: ret             
    // 0x8b5bc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5bc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5bc8: b               #0x8b5a24
  }
  [closure] void cancelExchange(dynamic, BuildContext, CancelExchangeRequestWidget, String, bool, bool, bool, String, String, String) {
    // ** addr: 0x8b5bcc, size: 0x68
    // 0x8b5bcc: EnterFrame
    //     0x8b5bcc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5bd0: mov             fp, SP
    // 0x8b5bd4: AllocStack(0x20)
    //     0x8b5bd4: sub             SP, SP, #0x20
    // 0x8b5bd8: SetupParameters()
    //     0x8b5bd8: ldr             x0, [fp, #0x58]
    //     0x8b5bdc: ldur            w1, [x0, #0x17]
    //     0x8b5be0: add             x1, x1, HEAP, lsl #32
    // 0x8b5be4: CheckStackOverflow
    //     0x8b5be4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5be8: cmp             SP, x16
    //     0x8b5bec: b.ls            #0x8b5c2c
    // 0x8b5bf0: ldr             x16, [fp, #0x28]
    // 0x8b5bf4: ldr             lr, [fp, #0x20]
    // 0x8b5bf8: stp             lr, x16, [SP, #0x10]
    // 0x8b5bfc: ldr             x16, [fp, #0x18]
    // 0x8b5c00: ldr             lr, [fp, #0x10]
    // 0x8b5c04: stp             lr, x16, [SP]
    // 0x8b5c08: ldr             x2, [fp, #0x50]
    // 0x8b5c0c: ldr             x3, [fp, #0x48]
    // 0x8b5c10: ldr             x5, [fp, #0x40]
    // 0x8b5c14: ldr             x6, [fp, #0x38]
    // 0x8b5c18: ldr             x7, [fp, #0x30]
    // 0x8b5c1c: r0 = cancelExchange()
    //     0x8b5c1c: bl              #0x8b5c34  ; [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelExchange
    // 0x8b5c20: LeaveFrame
    //     0x8b5c20: mov             SP, fp
    //     0x8b5c24: ldp             fp, lr, [SP], #0x10
    // 0x8b5c28: ret
    //     0x8b5c28: ret             
    // 0x8b5c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5c2c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5c30: b               #0x8b5bf0
  }
  _ cancelExchange(/* No info */) {
    // ** addr: 0x8b5c34, size: 0xe4
    // 0x8b5c34: EnterFrame
    //     0x8b5c34: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5c38: mov             fp, SP
    // 0x8b5c3c: AllocStack(0x60)
    //     0x8b5c3c: sub             SP, SP, #0x60
    // 0x8b5c40: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r7, fp-0x28 */, dynamic _ /* r7 => r6, fp-0x30 */)
    //     0x8b5c40: stur            x6, [fp, #-0x28]
    //     0x8b5c44: mov             x16, x7
    //     0x8b5c48: mov             x7, x6
    //     0x8b5c4c: mov             x6, x16
    //     0x8b5c50: stur            x1, [fp, #-8]
    //     0x8b5c54: stur            x2, [fp, #-0x10]
    //     0x8b5c58: stur            x3, [fp, #-0x18]
    //     0x8b5c5c: stur            x5, [fp, #-0x20]
    //     0x8b5c60: stur            x6, [fp, #-0x30]
    // 0x8b5c64: CheckStackOverflow
    //     0x8b5c64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5c68: cmp             SP, x16
    //     0x8b5c6c: b.ls            #0x8b5d10
    // 0x8b5c70: r1 = 3
    //     0x8b5c70: movz            x1, #0x3
    // 0x8b5c74: r0 = AllocateContext()
    //     0x8b5c74: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b5c78: ldur            x1, [fp, #-8]
    // 0x8b5c7c: stur            x0, [fp, #-0x38]
    // 0x8b5c80: StoreField: r0->field_f = r1
    //     0x8b5c80: stur            w1, [x0, #0xf]
    // 0x8b5c84: ldur            x2, [fp, #-0x18]
    // 0x8b5c88: StoreField: r0->field_13 = r2
    //     0x8b5c88: stur            w2, [x0, #0x13]
    // 0x8b5c8c: ldur            x2, [fp, #-0x20]
    // 0x8b5c90: ArrayStore: r0[0] = r2  ; List_4
    //     0x8b5c90: stur            w2, [x0, #0x17]
    // 0x8b5c94: r0 = controller()
    //     0x8b5c94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5c98: mov             x1, x0
    // 0x8b5c9c: ldur            x0, [fp, #-0x38]
    // 0x8b5ca0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8b5ca0: ldur            w2, [x0, #0x17]
    // 0x8b5ca4: DecompressPointer r2
    //     0x8b5ca4: add             x2, x2, HEAP, lsl #32
    // 0x8b5ca8: ldr             x16, [fp, #0x20]
    // 0x8b5cac: stp             x2, x16, [SP]
    // 0x8b5cb0: ldr             x2, [fp, #0x18]
    // 0x8b5cb4: ldr             x3, [fp, #0x10]
    // 0x8b5cb8: ldr             x5, [fp, #0x28]
    // 0x8b5cbc: ldur            x6, [fp, #-0x30]
    // 0x8b5cc0: ldur            x7, [fp, #-0x28]
    // 0x8b5cc4: r0 = exchangeCancelRequestCTAPostEvent()
    //     0x8b5cc4: bl              #0x8ae250  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::exchangeCancelRequestCTAPostEvent
    // 0x8b5cc8: ldur            x2, [fp, #-0x38]
    // 0x8b5ccc: r1 = Function '<anonymous closure>':.
    //     0x8b5ccc: add             x1, PP, #0x43, lsl #12  ; [pp+0x43180] AnonymousClosure: (0x8b5d18), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelExchange (0x8b5c34)
    //     0x8b5cd0: ldr             x1, [x1, #0x180]
    // 0x8b5cd4: r0 = AllocateClosure()
    //     0x8b5cd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5cd8: stp             x0, NULL, [SP, #0x18]
    // 0x8b5cdc: ldur            x16, [fp, #-0x10]
    // 0x8b5ce0: r30 = true
    //     0x8b5ce0: add             lr, NULL, #0x20  ; true
    // 0x8b5ce4: stp             lr, x16, [SP, #8]
    // 0x8b5ce8: r16 = Instance_RoundedRectangleBorder
    //     0x8b5ce8: add             x16, PP, #0x42, lsl #12  ; [pp+0x42a80] Obj!RoundedRectangleBorder@d5abb1
    //     0x8b5cec: ldr             x16, [x16, #0xa80]
    // 0x8b5cf0: str             x16, [SP]
    // 0x8b5cf4: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x8b5cf4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x8b5cf8: ldr             x4, [x4, #0xb20]
    // 0x8b5cfc: r0 = showModalBottomSheet()
    //     0x8b5cfc: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8b5d00: r0 = Null
    //     0x8b5d00: mov             x0, NULL
    // 0x8b5d04: LeaveFrame
    //     0x8b5d04: mov             SP, fp
    //     0x8b5d08: ldp             fp, lr, [SP], #0x10
    // 0x8b5d0c: ret
    //     0x8b5d0c: ret             
    // 0x8b5d10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5d10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5d14: b               #0x8b5c70
  }
  [closure] CancelExchangeBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8b5d18, size: 0x9c
    // 0x8b5d18: EnterFrame
    //     0x8b5d18: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5d1c: mov             fp, SP
    // 0x8b5d20: AllocStack(0x20)
    //     0x8b5d20: sub             SP, SP, #0x20
    // 0x8b5d24: SetupParameters()
    //     0x8b5d24: ldr             x0, [fp, #0x18]
    //     0x8b5d28: ldur            w1, [x0, #0x17]
    //     0x8b5d2c: add             x1, x1, HEAP, lsl #32
    // 0x8b5d30: CheckStackOverflow
    //     0x8b5d30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5d34: cmp             SP, x16
    //     0x8b5d38: b.ls            #0x8b5dac
    // 0x8b5d3c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8b5d3c: ldur            w0, [x1, #0x17]
    // 0x8b5d40: DecompressPointer r0
    //     0x8b5d40: add             x0, x0, HEAP, lsl #32
    // 0x8b5d44: stur            x0, [fp, #-0x10]
    // 0x8b5d48: LoadField: r2 = r1->field_13
    //     0x8b5d48: ldur            w2, [x1, #0x13]
    // 0x8b5d4c: DecompressPointer r2
    //     0x8b5d4c: add             x2, x2, HEAP, lsl #32
    // 0x8b5d50: stur            x2, [fp, #-8]
    // 0x8b5d54: LoadField: r3 = r1->field_f
    //     0x8b5d54: ldur            w3, [x1, #0xf]
    // 0x8b5d58: DecompressPointer r3
    //     0x8b5d58: add             x3, x3, HEAP, lsl #32
    // 0x8b5d5c: mov             x1, x3
    // 0x8b5d60: r0 = controller()
    //     0x8b5d60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5d64: stur            x0, [fp, #-0x18]
    // 0x8b5d68: r0 = CancelExchangeBottomSheet()
    //     0x8b5d68: bl              #0x8b5db4  ; AllocateCancelExchangeBottomSheetStub -> CancelExchangeBottomSheet (size=0x18)
    // 0x8b5d6c: mov             x3, x0
    // 0x8b5d70: ldur            x0, [fp, #-8]
    // 0x8b5d74: stur            x3, [fp, #-0x20]
    // 0x8b5d78: StoreField: r3->field_b = r0
    //     0x8b5d78: stur            w0, [x3, #0xb]
    // 0x8b5d7c: ldur            x2, [fp, #-0x18]
    // 0x8b5d80: r1 = Function 'cancelExchange':.
    //     0x8b5d80: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e18] AnonymousClosure: (0x8afdc4), in [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::cancelExchange (0x8afe00)
    //     0x8b5d84: ldr             x1, [x1, #0xe18]
    // 0x8b5d88: r0 = AllocateClosure()
    //     0x8b5d88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5d8c: mov             x1, x0
    // 0x8b5d90: ldur            x0, [fp, #-0x20]
    // 0x8b5d94: StoreField: r0->field_f = r1
    //     0x8b5d94: stur            w1, [x0, #0xf]
    // 0x8b5d98: ldur            x1, [fp, #-0x10]
    // 0x8b5d9c: StoreField: r0->field_13 = r1
    //     0x8b5d9c: stur            w1, [x0, #0x13]
    // 0x8b5da0: LeaveFrame
    //     0x8b5da0: mov             SP, fp
    //     0x8b5da4: ldp             fp, lr, [SP], #0x10
    // 0x8b5da8: ret
    //     0x8b5da8: ret             
    // 0x8b5dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5dac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5db0: b               #0x8b5d3c
  }
  [closure] void cancelReturn(dynamic, BuildContext, CancelReturnRequestWidget?, String) {
    // ** addr: 0x8b5de4, size: 0x44
    // 0x8b5de4: EnterFrame
    //     0x8b5de4: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5de8: mov             fp, SP
    // 0x8b5dec: ldr             x0, [fp, #0x28]
    // 0x8b5df0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b5df0: ldur            w1, [x0, #0x17]
    // 0x8b5df4: DecompressPointer r1
    //     0x8b5df4: add             x1, x1, HEAP, lsl #32
    // 0x8b5df8: CheckStackOverflow
    //     0x8b5df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5dfc: cmp             SP, x16
    //     0x8b5e00: b.ls            #0x8b5e20
    // 0x8b5e04: ldr             x2, [fp, #0x20]
    // 0x8b5e08: ldr             x3, [fp, #0x18]
    // 0x8b5e0c: ldr             x5, [fp, #0x10]
    // 0x8b5e10: r0 = cancelReturn()
    //     0x8b5e10: bl              #0x8b5e28  ; [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelReturn
    // 0x8b5e14: LeaveFrame
    //     0x8b5e14: mov             SP, fp
    //     0x8b5e18: ldp             fp, lr, [SP], #0x10
    // 0x8b5e1c: ret
    //     0x8b5e1c: ret             
    // 0x8b5e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5e20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5e24: b               #0x8b5e04
  }
  _ cancelReturn(/* No info */) {
    // ** addr: 0x8b5e28, size: 0x9c
    // 0x8b5e28: EnterFrame
    //     0x8b5e28: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5e2c: mov             fp, SP
    // 0x8b5e30: AllocStack(0x48)
    //     0x8b5e30: sub             SP, SP, #0x48
    // 0x8b5e34: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x8b5e34: stur            x1, [fp, #-8]
    //     0x8b5e38: stur            x2, [fp, #-0x10]
    //     0x8b5e3c: stur            x3, [fp, #-0x18]
    //     0x8b5e40: stur            x5, [fp, #-0x20]
    // 0x8b5e44: CheckStackOverflow
    //     0x8b5e44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5e48: cmp             SP, x16
    //     0x8b5e4c: b.ls            #0x8b5ebc
    // 0x8b5e50: r1 = 3
    //     0x8b5e50: movz            x1, #0x3
    // 0x8b5e54: r0 = AllocateContext()
    //     0x8b5e54: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b5e58: mov             x1, x0
    // 0x8b5e5c: ldur            x0, [fp, #-8]
    // 0x8b5e60: StoreField: r1->field_f = r0
    //     0x8b5e60: stur            w0, [x1, #0xf]
    // 0x8b5e64: ldur            x0, [fp, #-0x18]
    // 0x8b5e68: StoreField: r1->field_13 = r0
    //     0x8b5e68: stur            w0, [x1, #0x13]
    // 0x8b5e6c: ldur            x0, [fp, #-0x20]
    // 0x8b5e70: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b5e70: stur            w0, [x1, #0x17]
    // 0x8b5e74: mov             x2, x1
    // 0x8b5e78: r1 = Function '<anonymous closure>':.
    //     0x8b5e78: add             x1, PP, #0x43, lsl #12  ; [pp+0x43188] AnonymousClosure: (0x8b5ec4), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelReturn (0x8b5e28)
    //     0x8b5e7c: ldr             x1, [x1, #0x188]
    // 0x8b5e80: r0 = AllocateClosure()
    //     0x8b5e80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5e84: stp             x0, NULL, [SP, #0x18]
    // 0x8b5e88: ldur            x16, [fp, #-0x10]
    // 0x8b5e8c: r30 = true
    //     0x8b5e8c: add             lr, NULL, #0x20  ; true
    // 0x8b5e90: stp             lr, x16, [SP, #8]
    // 0x8b5e94: r16 = Instance_RoundedRectangleBorder
    //     0x8b5e94: add             x16, PP, #0x42, lsl #12  ; [pp+0x42a80] Obj!RoundedRectangleBorder@d5abb1
    //     0x8b5e98: ldr             x16, [x16, #0xa80]
    // 0x8b5e9c: str             x16, [SP]
    // 0x8b5ea0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x8b5ea0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x8b5ea4: ldr             x4, [x4, #0xb20]
    // 0x8b5ea8: r0 = showModalBottomSheet()
    //     0x8b5ea8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8b5eac: r0 = Null
    //     0x8b5eac: mov             x0, NULL
    // 0x8b5eb0: LeaveFrame
    //     0x8b5eb0: mov             SP, fp
    //     0x8b5eb4: ldp             fp, lr, [SP], #0x10
    // 0x8b5eb8: ret
    //     0x8b5eb8: ret             
    // 0x8b5ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5ebc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5ec0: b               #0x8b5e50
  }
  [closure] CancelReturnOrderBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8b5ec4, size: 0x9c
    // 0x8b5ec4: EnterFrame
    //     0x8b5ec4: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5ec8: mov             fp, SP
    // 0x8b5ecc: AllocStack(0x20)
    //     0x8b5ecc: sub             SP, SP, #0x20
    // 0x8b5ed0: SetupParameters()
    //     0x8b5ed0: ldr             x0, [fp, #0x18]
    //     0x8b5ed4: ldur            w1, [x0, #0x17]
    //     0x8b5ed8: add             x1, x1, HEAP, lsl #32
    // 0x8b5edc: CheckStackOverflow
    //     0x8b5edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5ee0: cmp             SP, x16
    //     0x8b5ee4: b.ls            #0x8b5f58
    // 0x8b5ee8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8b5ee8: ldur            w0, [x1, #0x17]
    // 0x8b5eec: DecompressPointer r0
    //     0x8b5eec: add             x0, x0, HEAP, lsl #32
    // 0x8b5ef0: stur            x0, [fp, #-0x10]
    // 0x8b5ef4: LoadField: r2 = r1->field_13
    //     0x8b5ef4: ldur            w2, [x1, #0x13]
    // 0x8b5ef8: DecompressPointer r2
    //     0x8b5ef8: add             x2, x2, HEAP, lsl #32
    // 0x8b5efc: stur            x2, [fp, #-8]
    // 0x8b5f00: LoadField: r3 = r1->field_f
    //     0x8b5f00: ldur            w3, [x1, #0xf]
    // 0x8b5f04: DecompressPointer r3
    //     0x8b5f04: add             x3, x3, HEAP, lsl #32
    // 0x8b5f08: mov             x1, x3
    // 0x8b5f0c: r0 = controller()
    //     0x8b5f0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b5f10: stur            x0, [fp, #-0x18]
    // 0x8b5f14: r0 = CancelReturnOrderBottomSheet()
    //     0x8b5f14: bl              #0x8b5f60  ; AllocateCancelReturnOrderBottomSheetStub -> CancelReturnOrderBottomSheet (size=0x18)
    // 0x8b5f18: mov             x3, x0
    // 0x8b5f1c: ldur            x0, [fp, #-8]
    // 0x8b5f20: stur            x3, [fp, #-0x20]
    // 0x8b5f24: StoreField: r3->field_b = r0
    //     0x8b5f24: stur            w0, [x3, #0xb]
    // 0x8b5f28: ldur            x2, [fp, #-0x18]
    // 0x8b5f2c: r1 = Function 'cancelReturn':.
    //     0x8b5f2c: add             x1, PP, #0x36, lsl #12  ; [pp+0x36e48] AnonymousClosure: (0x8b07bc), in [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::cancelReturn (0x8b07f8)
    //     0x8b5f30: ldr             x1, [x1, #0xe48]
    // 0x8b5f34: r0 = AllocateClosure()
    //     0x8b5f34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b5f38: mov             x1, x0
    // 0x8b5f3c: ldur            x0, [fp, #-0x20]
    // 0x8b5f40: StoreField: r0->field_f = r1
    //     0x8b5f40: stur            w1, [x0, #0xf]
    // 0x8b5f44: ldur            x1, [fp, #-0x10]
    // 0x8b5f48: StoreField: r0->field_13 = r1
    //     0x8b5f48: stur            w1, [x0, #0x13]
    // 0x8b5f4c: LeaveFrame
    //     0x8b5f4c: mov             SP, fp
    //     0x8b5f50: ldp             fp, lr, [SP], #0x10
    // 0x8b5f54: ret
    //     0x8b5f54: ret             
    // 0x8b5f58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5f58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5f5c: b               #0x8b5ee8
  }
  [closure] void openProductDetail(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x8b5f90, size: 0x4c
    // 0x8b5f90: EnterFrame
    //     0x8b5f90: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5f94: mov             fp, SP
    // 0x8b5f98: ldr             x0, [fp, #0x38]
    // 0x8b5f9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b5f9c: ldur            w1, [x0, #0x17]
    // 0x8b5fa0: DecompressPointer r1
    //     0x8b5fa0: add             x1, x1, HEAP, lsl #32
    // 0x8b5fa4: CheckStackOverflow
    //     0x8b5fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b5fa8: cmp             SP, x16
    //     0x8b5fac: b.ls            #0x8b5fd4
    // 0x8b5fb0: ldr             x2, [fp, #0x30]
    // 0x8b5fb4: ldr             x3, [fp, #0x28]
    // 0x8b5fb8: ldr             x5, [fp, #0x20]
    // 0x8b5fbc: ldr             x6, [fp, #0x18]
    // 0x8b5fc0: ldr             x7, [fp, #0x10]
    // 0x8b5fc4: r0 = openProductDetail()
    //     0x8b5fc4: bl              #0x8b5fdc  ; [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::openProductDetail
    // 0x8b5fc8: LeaveFrame
    //     0x8b5fc8: mov             SP, fp
    //     0x8b5fcc: ldp             fp, lr, [SP], #0x10
    // 0x8b5fd0: ret
    //     0x8b5fd0: ret             
    // 0x8b5fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b5fd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b5fd8: b               #0x8b5fb0
  }
  _ openProductDetail(/* No info */) {
    // ** addr: 0x8b5fdc, size: 0x220
    // 0x8b5fdc: EnterFrame
    //     0x8b5fdc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b5fe0: mov             fp, SP
    // 0x8b5fe4: AllocStack(0x48)
    //     0x8b5fe4: sub             SP, SP, #0x48
    // 0x8b5fe8: SetupParameters(OrdersView this /* r1 => r8, fp-0x8 */, dynamic _ /* r2 => r7, fp-0x10 */, dynamic _ /* r3 => r6, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r4, fp-0x28 */, dynamic _ /* r7 => r3, fp-0x30 */)
    //     0x8b5fe8: mov             x8, x1
    //     0x8b5fec: mov             x4, x6
    //     0x8b5ff0: stur            x6, [fp, #-0x28]
    //     0x8b5ff4: mov             x6, x3
    //     0x8b5ff8: stur            x3, [fp, #-0x18]
    //     0x8b5ffc: mov             x3, x7
    //     0x8b6000: stur            x7, [fp, #-0x30]
    //     0x8b6004: mov             x7, x2
    //     0x8b6008: stur            x1, [fp, #-8]
    //     0x8b600c: stur            x2, [fp, #-0x10]
    //     0x8b6010: stur            x5, [fp, #-0x20]
    // 0x8b6014: CheckStackOverflow
    //     0x8b6014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6018: cmp             SP, x16
    //     0x8b601c: b.ls            #0x8b61f4
    // 0x8b6020: mov             x0, x5
    // 0x8b6024: r2 = Null
    //     0x8b6024: mov             x2, NULL
    // 0x8b6028: r1 = Null
    //     0x8b6028: mov             x1, NULL
    // 0x8b602c: r4 = 60
    //     0x8b602c: movz            x4, #0x3c
    // 0x8b6030: branchIfSmi(r0, 0x8b603c)
    //     0x8b6030: tbz             w0, #0, #0x8b603c
    // 0x8b6034: r4 = LoadClassIdInstr(r0)
    //     0x8b6034: ldur            x4, [x0, #-1]
    //     0x8b6038: ubfx            x4, x4, #0xc, #0x14
    // 0x8b603c: cmp             x4, #0x3f
    // 0x8b6040: b.eq            #0x8b6054
    // 0x8b6044: r8 = bool
    //     0x8b6044: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x8b6048: r3 = Null
    //     0x8b6048: add             x3, PP, #0x43, lsl #12  ; [pp+0x43190] Null
    //     0x8b604c: ldr             x3, [x3, #0x190]
    // 0x8b6050: r0 = bool()
    //     0x8b6050: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x8b6054: ldur            x0, [fp, #-0x20]
    // 0x8b6058: tbz             w0, #4, #0x8b609c
    // 0x8b605c: ldur            x3, [fp, #-0x28]
    // 0x8b6060: mov             x0, x3
    // 0x8b6064: r2 = Null
    //     0x8b6064: mov             x2, NULL
    // 0x8b6068: r1 = Null
    //     0x8b6068: mov             x1, NULL
    // 0x8b606c: r4 = 60
    //     0x8b606c: movz            x4, #0x3c
    // 0x8b6070: branchIfSmi(r0, 0x8b607c)
    //     0x8b6070: tbz             w0, #0, #0x8b607c
    // 0x8b6074: r4 = LoadClassIdInstr(r0)
    //     0x8b6074: ldur            x4, [x0, #-1]
    //     0x8b6078: ubfx            x4, x4, #0xc, #0x14
    // 0x8b607c: cmp             x4, #0x3f
    // 0x8b6080: b.eq            #0x8b6094
    // 0x8b6084: r8 = bool
    //     0x8b6084: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x8b6088: r3 = Null
    //     0x8b6088: add             x3, PP, #0x43, lsl #12  ; [pp+0x431a0] Null
    //     0x8b608c: ldr             x3, [x3, #0x1a0]
    // 0x8b6090: r0 = bool()
    //     0x8b6090: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x8b6094: ldur            x0, [fp, #-0x28]
    // 0x8b6098: tbnz            w0, #4, #0x8b613c
    // 0x8b609c: ldur            x0, [fp, #-0x30]
    // 0x8b60a0: ldur            x1, [fp, #-8]
    // 0x8b60a4: r0 = controller()
    //     0x8b60a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b60a8: mov             x3, x0
    // 0x8b60ac: ldur            x0, [fp, #-0x30]
    // 0x8b60b0: r2 = Null
    //     0x8b60b0: mov             x2, NULL
    // 0x8b60b4: r1 = Null
    //     0x8b60b4: mov             x1, NULL
    // 0x8b60b8: stur            x3, [fp, #-8]
    // 0x8b60bc: r4 = 60
    //     0x8b60bc: movz            x4, #0x3c
    // 0x8b60c0: branchIfSmi(r0, 0x8b60cc)
    //     0x8b60c0: tbz             w0, #0, #0x8b60cc
    // 0x8b60c4: r4 = LoadClassIdInstr(r0)
    //     0x8b60c4: ldur            x4, [x0, #-1]
    //     0x8b60c8: ubfx            x4, x4, #0xc, #0x14
    // 0x8b60cc: sub             x4, x4, #0x5e
    // 0x8b60d0: cmp             x4, #1
    // 0x8b60d4: b.ls            #0x8b60e8
    // 0x8b60d8: r8 = String?
    //     0x8b60d8: ldr             x8, [PP, #0x100]  ; [pp+0x100] Type: String?
    // 0x8b60dc: r3 = Null
    //     0x8b60dc: add             x3, PP, #0x43, lsl #12  ; [pp+0x431b0] Null
    //     0x8b60e0: ldr             x3, [x3, #0x1b0]
    // 0x8b60e4: r0 = String?()
    //     0x8b60e4: bl              #0x61bb08  ; IsType_String?_Stub
    // 0x8b60e8: r0 = EventData()
    //     0x8b60e8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b60ec: mov             x1, x0
    // 0x8b60f0: r0 = "home_page"
    //     0x8b60f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0x8b60f4: ldr             x0, [x0, #0xe60]
    // 0x8b60f8: stur            x1, [fp, #-0x20]
    // 0x8b60fc: StoreField: r1->field_13 = r0
    //     0x8b60fc: stur            w0, [x1, #0x13]
    // 0x8b6100: ldur            x0, [fp, #-0x30]
    // 0x8b6104: StoreField: r1->field_77 = r0
    //     0x8b6104: stur            w0, [x1, #0x77]
    // 0x8b6108: r0 = "order_list"
    //     0x8b6108: add             x0, PP, #0x36, lsl #12  ; [pp+0x36e98] "order_list"
    //     0x8b610c: ldr             x0, [x0, #0xe98]
    // 0x8b6110: StoreField: r1->field_87 = r0
    //     0x8b6110: stur            w0, [x1, #0x87]
    // 0x8b6114: r0 = EventsRequest()
    //     0x8b6114: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b6118: mov             x1, x0
    // 0x8b611c: r0 = "return_exchange_order_again"
    //     0x8b611c: add             x0, PP, #0x36, lsl #12  ; [pp+0x362a0] "return_exchange_order_again"
    //     0x8b6120: ldr             x0, [x0, #0x2a0]
    // 0x8b6124: StoreField: r1->field_7 = r0
    //     0x8b6124: stur            w0, [x1, #7]
    // 0x8b6128: ldur            x0, [fp, #-0x20]
    // 0x8b612c: StoreField: r1->field_b = r0
    //     0x8b612c: stur            w0, [x1, #0xb]
    // 0x8b6130: mov             x2, x1
    // 0x8b6134: ldur            x1, [fp, #-8]
    // 0x8b6138: r0 = postEvents()
    //     0x8b6138: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b613c: ldur            x1, [fp, #-0x10]
    // 0x8b6140: ldur            x0, [fp, #-0x18]
    // 0x8b6144: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b6144: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b6148: ldr             x0, [x0, #0x1c80]
    //     0x8b614c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b6150: cmp             w0, w16
    //     0x8b6154: b.ne            #0x8b6160
    //     0x8b6158: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b615c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b6160: r1 = Null
    //     0x8b6160: mov             x1, NULL
    // 0x8b6164: r2 = 16
    //     0x8b6164: movz            x2, #0x10
    // 0x8b6168: r0 = AllocateArray()
    //     0x8b6168: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8b616c: r16 = "short_id"
    //     0x8b616c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb488] "short_id"
    //     0x8b6170: ldr             x16, [x16, #0x488]
    // 0x8b6174: StoreField: r0->field_f = r16
    //     0x8b6174: stur            w16, [x0, #0xf]
    // 0x8b6178: ldur            x1, [fp, #-0x10]
    // 0x8b617c: StoreField: r0->field_13 = r1
    //     0x8b617c: stur            w1, [x0, #0x13]
    // 0x8b6180: r16 = "sku_id"
    //     0x8b6180: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x8b6184: ldr             x16, [x16, #0x498]
    // 0x8b6188: ArrayStore: r0[0] = r16  ; List_4
    //     0x8b6188: stur            w16, [x0, #0x17]
    // 0x8b618c: ldur            x1, [fp, #-0x18]
    // 0x8b6190: StoreField: r0->field_1b = r1
    //     0x8b6190: stur            w1, [x0, #0x1b]
    // 0x8b6194: r16 = "screenSource"
    //     0x8b6194: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x8b6198: ldr             x16, [x16, #0x450]
    // 0x8b619c: StoreField: r0->field_1f = r16
    //     0x8b619c: stur            w16, [x0, #0x1f]
    // 0x8b61a0: StoreField: r0->field_23 = rNULL
    //     0x8b61a0: stur            NULL, [x0, #0x23]
    // 0x8b61a4: r16 = "previousScreenSource"
    //     0x8b61a4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x8b61a8: ldr             x16, [x16, #0x448]
    // 0x8b61ac: StoreField: r0->field_27 = r16
    //     0x8b61ac: stur            w16, [x0, #0x27]
    // 0x8b61b0: r16 = "order_page"
    //     0x8b61b0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b61b4: ldr             x16, [x16, #0x710]
    // 0x8b61b8: StoreField: r0->field_2b = r16
    //     0x8b61b8: stur            w16, [x0, #0x2b]
    // 0x8b61bc: r16 = <String, dynamic>
    //     0x8b61bc: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x8b61c0: stp             x0, x16, [SP]
    // 0x8b61c4: r0 = Map._fromLiteral()
    //     0x8b61c4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x8b61c8: r16 = "/product-detail"
    //     0x8b61c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x8b61cc: ldr             x16, [x16, #0x4a8]
    // 0x8b61d0: stp             x16, NULL, [SP, #8]
    // 0x8b61d4: str             x0, [SP]
    // 0x8b61d8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x8b61d8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x8b61dc: ldr             x4, [x4, #0x438]
    // 0x8b61e0: r0 = GetNavigation.toNamed()
    //     0x8b61e0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x8b61e4: r0 = Null
    //     0x8b61e4: mov             x0, NULL
    // 0x8b61e8: LeaveFrame
    //     0x8b61e8: mov             SP, fp
    //     0x8b61ec: ldp             fp, lr, [SP], #0x10
    // 0x8b61f0: ret
    //     0x8b61f0: ret             
    // 0x8b61f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b61f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b61f8: b               #0x8b6020
  }
  [closure] void cancelOrderWithFreeProduct(dynamic, Items?, String, BuildContext) {
    // ** addr: 0x8b61fc, size: 0x44
    // 0x8b61fc: EnterFrame
    //     0x8b61fc: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6200: mov             fp, SP
    // 0x8b6204: ldr             x0, [fp, #0x28]
    // 0x8b6208: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b6208: ldur            w1, [x0, #0x17]
    // 0x8b620c: DecompressPointer r1
    //     0x8b620c: add             x1, x1, HEAP, lsl #32
    // 0x8b6210: CheckStackOverflow
    //     0x8b6210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6214: cmp             SP, x16
    //     0x8b6218: b.ls            #0x8b6238
    // 0x8b621c: ldr             x2, [fp, #0x20]
    // 0x8b6220: ldr             x3, [fp, #0x18]
    // 0x8b6224: ldr             x5, [fp, #0x10]
    // 0x8b6228: r0 = cancelOrderWithFreeProduct()
    //     0x8b6228: bl              #0x8b6240  ; [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct
    // 0x8b622c: LeaveFrame
    //     0x8b622c: mov             SP, fp
    //     0x8b6230: ldp             fp, lr, [SP], #0x10
    // 0x8b6234: ret
    //     0x8b6234: ret             
    // 0x8b6238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b623c: b               #0x8b621c
  }
  _ cancelOrderWithFreeProduct(/* No info */) {
    // ** addr: 0x8b6240, size: 0x148
    // 0x8b6240: EnterFrame
    //     0x8b6240: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6244: mov             fp, SP
    // 0x8b6248: AllocStack(0x60)
    //     0x8b6248: sub             SP, SP, #0x60
    // 0x8b624c: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0x8b624c: stur            x1, [fp, #-8]
    //     0x8b6250: stur            x2, [fp, #-0x10]
    //     0x8b6254: stur            x3, [fp, #-0x18]
    //     0x8b6258: stur            x5, [fp, #-0x20]
    // 0x8b625c: CheckStackOverflow
    //     0x8b625c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6260: cmp             SP, x16
    //     0x8b6264: b.ls            #0x8b6380
    // 0x8b6268: r1 = 2
    //     0x8b6268: movz            x1, #0x2
    // 0x8b626c: r0 = AllocateContext()
    //     0x8b626c: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b6270: mov             x2, x0
    // 0x8b6274: ldur            x0, [fp, #-8]
    // 0x8b6278: stur            x2, [fp, #-0x28]
    // 0x8b627c: StoreField: r2->field_f = r0
    //     0x8b627c: stur            w0, [x2, #0xf]
    // 0x8b6280: ldur            x1, [fp, #-0x10]
    // 0x8b6284: StoreField: r2->field_13 = r1
    //     0x8b6284: stur            w1, [x2, #0x13]
    // 0x8b6288: mov             x1, x0
    // 0x8b628c: r0 = controller()
    //     0x8b628c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b6290: stur            x0, [fp, #-0x10]
    // 0x8b6294: r0 = EventData()
    //     0x8b6294: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b6298: mov             x1, x0
    // 0x8b629c: r0 = "order_page"
    //     0x8b629c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b62a0: ldr             x0, [x0, #0x710]
    // 0x8b62a4: stur            x1, [fp, #-0x30]
    // 0x8b62a8: StoreField: r1->field_13 = r0
    //     0x8b62a8: stur            w0, [x1, #0x13]
    // 0x8b62ac: ldur            x2, [fp, #-0x18]
    // 0x8b62b0: StoreField: r1->field_77 = r2
    //     0x8b62b0: stur            w2, [x1, #0x77]
    // 0x8b62b4: r0 = EventsRequest()
    //     0x8b62b4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b62b8: mov             x1, x0
    // 0x8b62bc: r0 = "cancel_order_clicked"
    //     0x8b62bc: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e8] "cancel_order_clicked"
    //     0x8b62c0: ldr             x0, [x0, #0xe8]
    // 0x8b62c4: StoreField: r1->field_7 = r0
    //     0x8b62c4: stur            w0, [x1, #7]
    // 0x8b62c8: ldur            x0, [fp, #-0x30]
    // 0x8b62cc: StoreField: r1->field_b = r0
    //     0x8b62cc: stur            w0, [x1, #0xb]
    // 0x8b62d0: mov             x2, x1
    // 0x8b62d4: ldur            x1, [fp, #-0x10]
    // 0x8b62d8: r0 = postEvents()
    //     0x8b62d8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b62dc: ldur            x1, [fp, #-8]
    // 0x8b62e0: r0 = controller()
    //     0x8b62e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b62e4: stur            x0, [fp, #-8]
    // 0x8b62e8: r0 = EventData()
    //     0x8b62e8: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b62ec: mov             x1, x0
    // 0x8b62f0: r0 = "order_page"
    //     0x8b62f0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b62f4: ldr             x0, [x0, #0x710]
    // 0x8b62f8: stur            x1, [fp, #-0x10]
    // 0x8b62fc: StoreField: r1->field_13 = r0
    //     0x8b62fc: stur            w0, [x1, #0x13]
    // 0x8b6300: ldur            x0, [fp, #-0x18]
    // 0x8b6304: StoreField: r1->field_77 = r0
    //     0x8b6304: stur            w0, [x1, #0x77]
    // 0x8b6308: r0 = EventsRequest()
    //     0x8b6308: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b630c: mov             x1, x0
    // 0x8b6310: r0 = "cancel_free_gift_popup_opened"
    //     0x8b6310: add             x0, PP, #0x36, lsl #12  ; [pp+0x36ea0] "cancel_free_gift_popup_opened"
    //     0x8b6314: ldr             x0, [x0, #0xea0]
    // 0x8b6318: StoreField: r1->field_7 = r0
    //     0x8b6318: stur            w0, [x1, #7]
    // 0x8b631c: ldur            x0, [fp, #-0x10]
    // 0x8b6320: StoreField: r1->field_b = r0
    //     0x8b6320: stur            w0, [x1, #0xb]
    // 0x8b6324: mov             x2, x1
    // 0x8b6328: ldur            x1, [fp, #-8]
    // 0x8b632c: r0 = postEvents()
    //     0x8b632c: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b6330: ldur            x2, [fp, #-0x28]
    // 0x8b6334: r1 = Function '<anonymous closure>':.
    //     0x8b6334: add             x1, PP, #0x43, lsl #12  ; [pp+0x431c0] AnonymousClosure: (0x8b6388), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x8b6240)
    //     0x8b6338: ldr             x1, [x1, #0x1c0]
    // 0x8b633c: r0 = AllocateClosure()
    //     0x8b633c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b6340: stp             x0, NULL, [SP, #0x20]
    // 0x8b6344: ldur            x16, [fp, #-0x20]
    // 0x8b6348: r30 = Instance_Color
    //     0x8b6348: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x8b634c: ldr             lr, [lr, #0x90]
    // 0x8b6350: stp             lr, x16, [SP, #0x10]
    // 0x8b6354: r16 = Instance_RoundedRectangleBorder
    //     0x8b6354: add             x16, PP, #0x42, lsl #12  ; [pp+0x42f58] Obj!RoundedRectangleBorder@d5abd1
    //     0x8b6358: ldr             x16, [x16, #0xf58]
    // 0x8b635c: r30 = true
    //     0x8b635c: add             lr, NULL, #0x20  ; true
    // 0x8b6360: stp             lr, x16, [SP]
    // 0x8b6364: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x2, isScrollControlled, 0x4, shape, 0x3, null]
    //     0x8b6364: add             x4, PP, #0x34, lsl #12  ; [pp+0x345f0] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x2, "isScrollControlled", 0x4, "shape", 0x3, Null]
    //     0x8b6368: ldr             x4, [x4, #0x5f0]
    // 0x8b636c: r0 = showModalBottomSheet()
    //     0x8b636c: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8b6370: r0 = Null
    //     0x8b6370: mov             x0, NULL
    // 0x8b6374: LeaveFrame
    //     0x8b6374: mov             SP, fp
    //     0x8b6378: ldp             fp, lr, [SP], #0x10
    // 0x8b637c: ret
    //     0x8b637c: ret             
    // 0x8b6380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6380: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6384: b               #0x8b6268
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8b6388, size: 0x1b0
    // 0x8b6388: EnterFrame
    //     0x8b6388: stp             fp, lr, [SP, #-0x10]!
    //     0x8b638c: mov             fp, SP
    // 0x8b6390: AllocStack(0x28)
    //     0x8b6390: sub             SP, SP, #0x28
    // 0x8b6394: SetupParameters()
    //     0x8b6394: ldr             x0, [fp, #0x18]
    //     0x8b6398: ldur            w1, [x0, #0x17]
    //     0x8b639c: add             x1, x1, HEAP, lsl #32
    //     0x8b63a0: stur            x1, [fp, #-8]
    // 0x8b63a4: CheckStackOverflow
    //     0x8b63a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b63a8: cmp             SP, x16
    //     0x8b63ac: b.ls            #0x8b6530
    // 0x8b63b0: r1 = 1
    //     0x8b63b0: movz            x1, #0x1
    // 0x8b63b4: r0 = AllocateContext()
    //     0x8b63b4: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b63b8: mov             x1, x0
    // 0x8b63bc: ldur            x0, [fp, #-8]
    // 0x8b63c0: stur            x1, [fp, #-0x10]
    // 0x8b63c4: StoreField: r1->field_b = r0
    //     0x8b63c4: stur            w0, [x1, #0xb]
    // 0x8b63c8: ldr             x2, [fp, #0x10]
    // 0x8b63cc: StoreField: r1->field_f = r2
    //     0x8b63cc: stur            w2, [x1, #0xf]
    // 0x8b63d0: LoadField: r2 = r0->field_13
    //     0x8b63d0: ldur            w2, [x0, #0x13]
    // 0x8b63d4: DecompressPointer r2
    //     0x8b63d4: add             x2, x2, HEAP, lsl #32
    // 0x8b63d8: cmp             w2, NULL
    // 0x8b63dc: b.ne            #0x8b63e8
    // 0x8b63e0: d0 = inf
    //     0x8b63e0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b63e4: b               #0x8b6454
    // 0x8b63e8: LoadField: r3 = r2->field_7f
    //     0x8b63e8: ldur            w3, [x2, #0x7f]
    // 0x8b63ec: DecompressPointer r3
    //     0x8b63ec: add             x3, x3, HEAP, lsl #32
    // 0x8b63f0: cmp             w3, NULL
    // 0x8b63f4: b.eq            #0x8b6450
    // 0x8b63f8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b63f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b63fc: ldr             x0, [x0, #0x1c80]
    //     0x8b6400: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b6404: cmp             w0, w16
    //     0x8b6408: b.ne            #0x8b6414
    //     0x8b640c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b6410: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b6414: r0 = GetNavigation.size()
    //     0x8b6414: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b6418: LoadField: d0 = r0->field_f
    //     0x8b6418: ldur            d0, [x0, #0xf]
    // 0x8b641c: d1 = 0.430000
    //     0x8b641c: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x8b6420: ldr             d1, [x17, #0xb8]
    // 0x8b6424: fmul            d2, d0, d1
    // 0x8b6428: stur            d2, [fp, #-0x28]
    // 0x8b642c: r0 = BoxConstraints()
    //     0x8b642c: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b6430: StoreField: r0->field_7 = rZR
    //     0x8b6430: stur            xzr, [x0, #7]
    // 0x8b6434: d0 = inf
    //     0x8b6434: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b6438: StoreField: r0->field_f = d0
    //     0x8b6438: stur            d0, [x0, #0xf]
    // 0x8b643c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b643c: stur            xzr, [x0, #0x17]
    // 0x8b6440: ldur            d0, [fp, #-0x28]
    // 0x8b6444: StoreField: r0->field_1f = d0
    //     0x8b6444: stur            d0, [x0, #0x1f]
    // 0x8b6448: mov             x3, x0
    // 0x8b644c: b               #0x8b64a8
    // 0x8b6450: d0 = inf
    //     0x8b6450: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b6454: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b6454: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b6458: ldr             x0, [x0, #0x1c80]
    //     0x8b645c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b6460: cmp             w0, w16
    //     0x8b6464: b.ne            #0x8b6470
    //     0x8b6468: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b646c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b6470: r0 = GetNavigation.size()
    //     0x8b6470: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b6474: LoadField: d0 = r0->field_f
    //     0x8b6474: ldur            d0, [x0, #0xf]
    // 0x8b6478: d1 = 0.300000
    //     0x8b6478: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x8b647c: ldr             d1, [x17, #0x658]
    // 0x8b6480: fmul            d2, d0, d1
    // 0x8b6484: stur            d2, [fp, #-0x28]
    // 0x8b6488: r0 = BoxConstraints()
    //     0x8b6488: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b648c: StoreField: r0->field_7 = rZR
    //     0x8b648c: stur            xzr, [x0, #7]
    // 0x8b6490: d0 = inf
    //     0x8b6490: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b6494: StoreField: r0->field_f = d0
    //     0x8b6494: stur            d0, [x0, #0xf]
    // 0x8b6498: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b6498: stur            xzr, [x0, #0x17]
    // 0x8b649c: ldur            d0, [fp, #-0x28]
    // 0x8b64a0: StoreField: r0->field_1f = d0
    //     0x8b64a0: stur            d0, [x0, #0x1f]
    // 0x8b64a4: mov             x3, x0
    // 0x8b64a8: ldur            x0, [fp, #-8]
    // 0x8b64ac: stur            x3, [fp, #-0x20]
    // 0x8b64b0: LoadField: r4 = r0->field_13
    //     0x8b64b0: ldur            w4, [x0, #0x13]
    // 0x8b64b4: DecompressPointer r4
    //     0x8b64b4: add             x4, x4, HEAP, lsl #32
    // 0x8b64b8: ldur            x2, [fp, #-0x10]
    // 0x8b64bc: stur            x4, [fp, #-0x18]
    // 0x8b64c0: r1 = Function '<anonymous closure>':.
    //     0x8b64c0: add             x1, PP, #0x43, lsl #12  ; [pp+0x431c8] AnonymousClosure: (0x8b6568), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrderWithFreeProduct (0x8b6240)
    //     0x8b64c4: ldr             x1, [x1, #0x1c8]
    // 0x8b64c8: r0 = AllocateClosure()
    //     0x8b64c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b64cc: stur            x0, [fp, #-8]
    // 0x8b64d0: r0 = CancelReturnOrderWithFreeProductBottomSheet()
    //     0x8b64d0: bl              #0x8b6538  ; AllocateCancelReturnOrderWithFreeProductBottomSheetStub -> CancelReturnOrderWithFreeProductBottomSheet (size=0x28)
    // 0x8b64d4: mov             x1, x0
    // 0x8b64d8: ldur            x0, [fp, #-8]
    // 0x8b64dc: stur            x1, [fp, #-0x10]
    // 0x8b64e0: StoreField: r1->field_b = r0
    //     0x8b64e0: stur            w0, [x1, #0xb]
    // 0x8b64e4: ldur            x0, [fp, #-0x18]
    // 0x8b64e8: StoreField: r1->field_1b = r0
    //     0x8b64e8: stur            w0, [x1, #0x1b]
    // 0x8b64ec: r0 = "Confirm Cancellation"
    //     0x8b64ec: add             x0, PP, #0x36, lsl #12  ; [pp+0x360c8] "Confirm Cancellation"
    //     0x8b64f0: ldr             x0, [x0, #0xc8]
    // 0x8b64f4: StoreField: r1->field_13 = r0
    //     0x8b64f4: stur            w0, [x1, #0x13]
    // 0x8b64f8: r0 = "On cancellation of this product the free gift will also be cancelled"
    //     0x8b64f8: add             x0, PP, #0x36, lsl #12  ; [pp+0x360d0] "On cancellation of this product the free gift will also be cancelled"
    //     0x8b64fc: ldr             x0, [x0, #0xd0]
    // 0x8b6500: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b6500: stur            w0, [x1, #0x17]
    // 0x8b6504: r0 = "cancel_order"
    //     0x8b6504: add             x0, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0x8b6508: ldr             x0, [x0, #0x98]
    // 0x8b650c: StoreField: r1->field_f = r0
    //     0x8b650c: stur            w0, [x1, #0xf]
    // 0x8b6510: r0 = ConstrainedBox()
    //     0x8b6510: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x8b6514: ldur            x1, [fp, #-0x20]
    // 0x8b6518: StoreField: r0->field_f = r1
    //     0x8b6518: stur            w1, [x0, #0xf]
    // 0x8b651c: ldur            x1, [fp, #-0x10]
    // 0x8b6520: StoreField: r0->field_b = r1
    //     0x8b6520: stur            w1, [x0, #0xb]
    // 0x8b6524: LeaveFrame
    //     0x8b6524: mov             SP, fp
    //     0x8b6528: ldp             fp, lr, [SP], #0x10
    // 0x8b652c: ret
    //     0x8b652c: ret             
    // 0x8b6530: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6530: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6534: b               #0x8b63b0
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x8b6568, size: 0x18c
    // 0x8b6568: EnterFrame
    //     0x8b6568: stp             fp, lr, [SP, #-0x10]!
    //     0x8b656c: mov             fp, SP
    // 0x8b6570: AllocStack(0x20)
    //     0x8b6570: sub             SP, SP, #0x20
    // 0x8b6574: SetupParameters()
    //     0x8b6574: ldr             x0, [fp, #0x10]
    //     0x8b6578: ldur            w1, [x0, #0x17]
    //     0x8b657c: add             x1, x1, HEAP, lsl #32
    //     0x8b6580: stur            x1, [fp, #-8]
    // 0x8b6584: CheckStackOverflow
    //     0x8b6584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6588: cmp             SP, x16
    //     0x8b658c: b.ls            #0x8b66ec
    // 0x8b6590: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b6590: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b6594: ldr             x0, [x0, #0x1c80]
    //     0x8b6598: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b659c: cmp             w0, w16
    //     0x8b65a0: b.ne            #0x8b65ac
    //     0x8b65a4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b65a8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b65ac: str             NULL, [SP]
    // 0x8b65b0: r4 = const [0x1, 0, 0, 0, null]
    //     0x8b65b0: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x8b65b4: r0 = GetNavigation.back()
    //     0x8b65b4: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x8b65b8: ldur            x0, [fp, #-8]
    // 0x8b65bc: LoadField: r4 = r0->field_b
    //     0x8b65bc: ldur            w4, [x0, #0xb]
    // 0x8b65c0: DecompressPointer r4
    //     0x8b65c0: add             x4, x4, HEAP, lsl #32
    // 0x8b65c4: stur            x4, [fp, #-0x10]
    // 0x8b65c8: LoadField: r1 = r4->field_f
    //     0x8b65c8: ldur            w1, [x4, #0xf]
    // 0x8b65cc: DecompressPointer r1
    //     0x8b65cc: add             x1, x1, HEAP, lsl #32
    // 0x8b65d0: LoadField: r2 = r4->field_13
    //     0x8b65d0: ldur            w2, [x4, #0x13]
    // 0x8b65d4: DecompressPointer r2
    //     0x8b65d4: add             x2, x2, HEAP, lsl #32
    // 0x8b65d8: cmp             w2, NULL
    // 0x8b65dc: b.ne            #0x8b65e8
    // 0x8b65e0: r3 = Null
    //     0x8b65e0: mov             x3, NULL
    // 0x8b65e4: b               #0x8b65f0
    // 0x8b65e8: LoadField: r3 = r2->field_37
    //     0x8b65e8: ldur            w3, [x2, #0x37]
    // 0x8b65ec: DecompressPointer r3
    //     0x8b65ec: add             x3, x3, HEAP, lsl #32
    // 0x8b65f0: cmp             w2, NULL
    // 0x8b65f4: b.ne            #0x8b6600
    // 0x8b65f8: r5 = Null
    //     0x8b65f8: mov             x5, NULL
    // 0x8b65fc: b               #0x8b6608
    // 0x8b6600: LoadField: r5 = r2->field_7
    //     0x8b6600: ldur            w5, [x2, #7]
    // 0x8b6604: DecompressPointer r5
    //     0x8b6604: add             x5, x5, HEAP, lsl #32
    // 0x8b6608: cmp             w5, NULL
    // 0x8b660c: b.ne            #0x8b6614
    // 0x8b6610: r5 = ""
    //     0x8b6610: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b6614: LoadField: r6 = r0->field_f
    //     0x8b6614: ldur            w6, [x0, #0xf]
    // 0x8b6618: DecompressPointer r6
    //     0x8b6618: add             x6, x6, HEAP, lsl #32
    // 0x8b661c: cmp             w2, NULL
    // 0x8b6620: b.ne            #0x8b662c
    // 0x8b6624: r0 = Null
    //     0x8b6624: mov             x0, NULL
    // 0x8b6628: b               #0x8b6634
    // 0x8b662c: LoadField: r0 = r2->field_2f
    //     0x8b662c: ldur            w0, [x2, #0x2f]
    // 0x8b6630: DecompressPointer r0
    //     0x8b6630: add             x0, x0, HEAP, lsl #32
    // 0x8b6634: cmp             w0, NULL
    // 0x8b6638: b.ne            #0x8b6640
    // 0x8b663c: r0 = ""
    //     0x8b663c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b6640: mov             x2, x3
    // 0x8b6644: mov             x3, x5
    // 0x8b6648: mov             x5, x6
    // 0x8b664c: mov             x6, x0
    // 0x8b6650: r0 = cancelOrder()
    //     0x8b6650: bl              #0x8b66f4  ; [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrder
    // 0x8b6654: ldur            x0, [fp, #-0x10]
    // 0x8b6658: LoadField: r1 = r0->field_f
    //     0x8b6658: ldur            w1, [x0, #0xf]
    // 0x8b665c: DecompressPointer r1
    //     0x8b665c: add             x1, x1, HEAP, lsl #32
    // 0x8b6660: r0 = controller()
    //     0x8b6660: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b6664: mov             x1, x0
    // 0x8b6668: ldur            x0, [fp, #-0x10]
    // 0x8b666c: stur            x1, [fp, #-0x18]
    // 0x8b6670: LoadField: r2 = r0->field_13
    //     0x8b6670: ldur            w2, [x0, #0x13]
    // 0x8b6674: DecompressPointer r2
    //     0x8b6674: add             x2, x2, HEAP, lsl #32
    // 0x8b6678: cmp             w2, NULL
    // 0x8b667c: b.ne            #0x8b6688
    // 0x8b6680: r0 = Null
    //     0x8b6680: mov             x0, NULL
    // 0x8b6684: b               #0x8b6690
    // 0x8b6688: LoadField: r0 = r2->field_7
    //     0x8b6688: ldur            w0, [x2, #7]
    // 0x8b668c: DecompressPointer r0
    //     0x8b668c: add             x0, x0, HEAP, lsl #32
    // 0x8b6690: cmp             w0, NULL
    // 0x8b6694: b.ne            #0x8b669c
    // 0x8b6698: r0 = ""
    //     0x8b6698: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b669c: stur            x0, [fp, #-8]
    // 0x8b66a0: r0 = EventData()
    //     0x8b66a0: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b66a4: mov             x1, x0
    // 0x8b66a8: ldur            x0, [fp, #-8]
    // 0x8b66ac: stur            x1, [fp, #-0x10]
    // 0x8b66b0: StoreField: r1->field_77 = r0
    //     0x8b66b0: stur            w0, [x1, #0x77]
    // 0x8b66b4: r0 = EventsRequest()
    //     0x8b66b4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b66b8: mov             x1, x0
    // 0x8b66bc: r0 = "cancel_free_gift_continue_clicked"
    //     0x8b66bc: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e0] "cancel_free_gift_continue_clicked"
    //     0x8b66c0: ldr             x0, [x0, #0xe0]
    // 0x8b66c4: StoreField: r1->field_7 = r0
    //     0x8b66c4: stur            w0, [x1, #7]
    // 0x8b66c8: ldur            x0, [fp, #-0x10]
    // 0x8b66cc: StoreField: r1->field_b = r0
    //     0x8b66cc: stur            w0, [x1, #0xb]
    // 0x8b66d0: mov             x2, x1
    // 0x8b66d4: ldur            x1, [fp, #-0x18]
    // 0x8b66d8: r0 = postEvents()
    //     0x8b66d8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b66dc: r0 = Null
    //     0x8b66dc: mov             x0, NULL
    // 0x8b66e0: LeaveFrame
    //     0x8b66e0: mov             SP, fp
    //     0x8b66e4: ldp             fp, lr, [SP], #0x10
    // 0x8b66e8: ret
    //     0x8b66e8: ret             
    // 0x8b66ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b66ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b66f0: b               #0x8b6590
  }
  _ cancelOrder(/* No info */) {
    // ** addr: 0x8b66f4, size: 0x110
    // 0x8b66f4: EnterFrame
    //     0x8b66f4: stp             fp, lr, [SP, #-0x10]!
    //     0x8b66f8: mov             fp, SP
    // 0x8b66fc: AllocStack(0x60)
    //     0x8b66fc: sub             SP, SP, #0x60
    // 0x8b6700: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */)
    //     0x8b6700: stur            x1, [fp, #-8]
    //     0x8b6704: stur            x2, [fp, #-0x10]
    //     0x8b6708: stur            x3, [fp, #-0x18]
    //     0x8b670c: stur            x5, [fp, #-0x20]
    //     0x8b6710: stur            x6, [fp, #-0x28]
    // 0x8b6714: CheckStackOverflow
    //     0x8b6714: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6718: cmp             SP, x16
    //     0x8b671c: b.ls            #0x8b67fc
    // 0x8b6720: r1 = 4
    //     0x8b6720: movz            x1, #0x4
    // 0x8b6724: r0 = AllocateContext()
    //     0x8b6724: bl              #0x16f6108  ; AllocateContextStub
    // 0x8b6728: ldur            x1, [fp, #-8]
    // 0x8b672c: stur            x0, [fp, #-0x30]
    // 0x8b6730: StoreField: r0->field_f = r1
    //     0x8b6730: stur            w1, [x0, #0xf]
    // 0x8b6734: ldur            x2, [fp, #-0x10]
    // 0x8b6738: StoreField: r0->field_13 = r2
    //     0x8b6738: stur            w2, [x0, #0x13]
    // 0x8b673c: ldur            x2, [fp, #-0x18]
    // 0x8b6740: ArrayStore: r0[0] = r2  ; List_4
    //     0x8b6740: stur            w2, [x0, #0x17]
    // 0x8b6744: ldur            x2, [fp, #-0x28]
    // 0x8b6748: StoreField: r0->field_1b = r2
    //     0x8b6748: stur            w2, [x0, #0x1b]
    // 0x8b674c: r0 = controller()
    //     0x8b674c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b6750: ldur            x2, [fp, #-0x30]
    // 0x8b6754: stur            x0, [fp, #-0x10]
    // 0x8b6758: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x8b6758: ldur            w1, [x2, #0x17]
    // 0x8b675c: DecompressPointer r1
    //     0x8b675c: add             x1, x1, HEAP, lsl #32
    // 0x8b6760: stur            x1, [fp, #-8]
    // 0x8b6764: r0 = EventData()
    //     0x8b6764: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x8b6768: mov             x1, x0
    // 0x8b676c: r0 = "order_page"
    //     0x8b676c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x8b6770: ldr             x0, [x0, #0x710]
    // 0x8b6774: stur            x1, [fp, #-0x18]
    // 0x8b6778: StoreField: r1->field_13 = r0
    //     0x8b6778: stur            w0, [x1, #0x13]
    // 0x8b677c: ldur            x0, [fp, #-8]
    // 0x8b6780: StoreField: r1->field_77 = r0
    //     0x8b6780: stur            w0, [x1, #0x77]
    // 0x8b6784: r0 = EventsRequest()
    //     0x8b6784: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x8b6788: mov             x1, x0
    // 0x8b678c: r0 = "cancel_order_clicked"
    //     0x8b678c: add             x0, PP, #0x36, lsl #12  ; [pp+0x360e8] "cancel_order_clicked"
    //     0x8b6790: ldr             x0, [x0, #0xe8]
    // 0x8b6794: StoreField: r1->field_7 = r0
    //     0x8b6794: stur            w0, [x1, #7]
    // 0x8b6798: ldur            x0, [fp, #-0x18]
    // 0x8b679c: StoreField: r1->field_b = r0
    //     0x8b679c: stur            w0, [x1, #0xb]
    // 0x8b67a0: mov             x2, x1
    // 0x8b67a4: ldur            x1, [fp, #-0x10]
    // 0x8b67a8: r0 = postEvents()
    //     0x8b67a8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x8b67ac: ldur            x2, [fp, #-0x30]
    // 0x8b67b0: r1 = Function '<anonymous closure>':.
    //     0x8b67b0: add             x1, PP, #0x43, lsl #12  ; [pp+0x431d0] AnonymousClosure: (0x8b6804), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrder (0x8b66f4)
    //     0x8b67b4: ldr             x1, [x1, #0x1d0]
    // 0x8b67b8: r0 = AllocateClosure()
    //     0x8b67b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b67bc: stp             x0, NULL, [SP, #0x20]
    // 0x8b67c0: ldur            x16, [fp, #-0x20]
    // 0x8b67c4: r30 = Instance_RoundedRectangleBorder
    //     0x8b67c4: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x8b67c8: ldr             lr, [lr, #0xc78]
    // 0x8b67cc: stp             lr, x16, [SP, #0x10]
    // 0x8b67d0: r16 = true
    //     0x8b67d0: add             x16, NULL, #0x20  ; true
    // 0x8b67d4: r30 = Instance_Color
    //     0x8b67d4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x8b67d8: ldr             lr, [lr, #0x90]
    // 0x8b67dc: stp             lr, x16, [SP]
    // 0x8b67e0: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x4, isScrollControlled, 0x3, shape, 0x2, null]
    //     0x8b67e0: add             x4, PP, #0x42, lsl #12  ; [pp+0x42f70] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x4, "isScrollControlled", 0x3, "shape", 0x2, Null]
    //     0x8b67e4: ldr             x4, [x4, #0xf70]
    // 0x8b67e8: r0 = showModalBottomSheet()
    //     0x8b67e8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x8b67ec: r0 = Null
    //     0x8b67ec: mov             x0, NULL
    // 0x8b67f0: LeaveFrame
    //     0x8b67f0: mov             SP, fp
    //     0x8b67f4: ldp             fp, lr, [SP], #0x10
    // 0x8b67f8: ret
    //     0x8b67f8: ret             
    // 0x8b67fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b67fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6800: b               #0x8b6720
  }
  [closure] ConstrainedBox <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x8b6804, size: 0x550
    // 0x8b6804: EnterFrame
    //     0x8b6804: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6808: mov             fp, SP
    // 0x8b680c: AllocStack(0x68)
    //     0x8b680c: sub             SP, SP, #0x68
    // 0x8b6810: SetupParameters()
    //     0x8b6810: ldr             x0, [fp, #0x18]
    //     0x8b6814: ldur            w2, [x0, #0x17]
    //     0x8b6818: add             x2, x2, HEAP, lsl #32
    //     0x8b681c: stur            x2, [fp, #-8]
    // 0x8b6820: CheckStackOverflow
    //     0x8b6820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6824: cmp             SP, x16
    //     0x8b6828: b.ls            #0x8b6d4c
    // 0x8b682c: LoadField: r0 = r2->field_13
    //     0x8b682c: ldur            w0, [x2, #0x13]
    // 0x8b6830: DecompressPointer r0
    //     0x8b6830: add             x0, x0, HEAP, lsl #32
    // 0x8b6834: cmp             w0, NULL
    // 0x8b6838: b.eq            #0x8b6948
    // 0x8b683c: LoadField: r1 = r0->field_13
    //     0x8b683c: ldur            w1, [x0, #0x13]
    // 0x8b6840: DecompressPointer r1
    //     0x8b6840: add             x1, x1, HEAP, lsl #32
    // 0x8b6844: cmp             w1, NULL
    // 0x8b6848: b.ne            #0x8b6898
    // 0x8b684c: LoadField: r1 = r0->field_7
    //     0x8b684c: ldur            w1, [x0, #7]
    // 0x8b6850: DecompressPointer r1
    //     0x8b6850: add             x1, x1, HEAP, lsl #32
    // 0x8b6854: cmp             w1, NULL
    // 0x8b6858: b.eq            #0x8b6890
    // 0x8b685c: LoadField: r0 = r2->field_1b
    //     0x8b685c: ldur            w0, [x2, #0x1b]
    // 0x8b6860: DecompressPointer r0
    //     0x8b6860: add             x0, x0, HEAP, lsl #32
    // 0x8b6864: r1 = LoadClassIdInstr(r0)
    //     0x8b6864: ldur            x1, [x0, #-1]
    //     0x8b6868: ubfx            x1, x1, #0xc, #0x14
    // 0x8b686c: r16 = "cod"
    //     0x8b686c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24a28] "cod"
    //     0x8b6870: ldr             x16, [x16, #0xa28]
    // 0x8b6874: stp             x16, x0, [SP]
    // 0x8b6878: mov             x0, x1
    // 0x8b687c: mov             lr, x0
    // 0x8b6880: ldr             lr, [x21, lr, lsl #3]
    // 0x8b6884: blr             lr
    // 0x8b6888: eor             x1, x0, #0x10
    // 0x8b688c: tbz             w1, #4, #0x8b689c
    // 0x8b6890: d0 = inf
    //     0x8b6890: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b6894: b               #0x8b68f4
    // 0x8b6898: tbnz            w1, #4, #0x8b68f0
    // 0x8b689c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b689c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b68a0: ldr             x0, [x0, #0x1c80]
    //     0x8b68a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b68a8: cmp             w0, w16
    //     0x8b68ac: b.ne            #0x8b68b8
    //     0x8b68b0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b68b4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b68b8: r0 = GetNavigation.size()
    //     0x8b68b8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b68bc: LoadField: d0 = r0->field_f
    //     0x8b68bc: ldur            d0, [x0, #0xf]
    // 0x8b68c0: d1 = 0.800000
    //     0x8b68c0: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x8b68c4: ldr             d1, [x17, #0xb28]
    // 0x8b68c8: fmul            d2, d0, d1
    // 0x8b68cc: stur            d2, [fp, #-0x58]
    // 0x8b68d0: r0 = BoxConstraints()
    //     0x8b68d0: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b68d4: StoreField: r0->field_7 = rZR
    //     0x8b68d4: stur            xzr, [x0, #7]
    // 0x8b68d8: d0 = inf
    //     0x8b68d8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b68dc: StoreField: r0->field_f = d0
    //     0x8b68dc: stur            d0, [x0, #0xf]
    // 0x8b68e0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b68e0: stur            xzr, [x0, #0x17]
    // 0x8b68e4: ldur            d0, [fp, #-0x58]
    // 0x8b68e8: StoreField: r0->field_1f = d0
    //     0x8b68e8: stur            d0, [x0, #0x1f]
    // 0x8b68ec: b               #0x8b699c
    // 0x8b68f0: d0 = inf
    //     0x8b68f0: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b68f4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b68f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b68f8: ldr             x0, [x0, #0x1c80]
    //     0x8b68fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b6900: cmp             w0, w16
    //     0x8b6904: b.ne            #0x8b6910
    //     0x8b6908: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b690c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b6910: r0 = GetNavigation.size()
    //     0x8b6910: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b6914: LoadField: d0 = r0->field_f
    //     0x8b6914: ldur            d0, [x0, #0xf]
    // 0x8b6918: d1 = 0.430000
    //     0x8b6918: add             x17, PP, #0x36, lsl #12  ; [pp+0x360b8] IMM: double(0.43) from 0x3fdb851eb851eb85
    //     0x8b691c: ldr             d1, [x17, #0xb8]
    // 0x8b6920: fmul            d2, d0, d1
    // 0x8b6924: stur            d2, [fp, #-0x58]
    // 0x8b6928: r0 = BoxConstraints()
    //     0x8b6928: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b692c: StoreField: r0->field_7 = rZR
    //     0x8b692c: stur            xzr, [x0, #7]
    // 0x8b6930: d0 = inf
    //     0x8b6930: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b6934: StoreField: r0->field_f = d0
    //     0x8b6934: stur            d0, [x0, #0xf]
    // 0x8b6938: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b6938: stur            xzr, [x0, #0x17]
    // 0x8b693c: ldur            d0, [fp, #-0x58]
    // 0x8b6940: StoreField: r0->field_1f = d0
    //     0x8b6940: stur            d0, [x0, #0x1f]
    // 0x8b6944: b               #0x8b699c
    // 0x8b6948: d0 = inf
    //     0x8b6948: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b694c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x8b694c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8b6950: ldr             x0, [x0, #0x1c80]
    //     0x8b6954: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8b6958: cmp             w0, w16
    //     0x8b695c: b.ne            #0x8b6968
    //     0x8b6960: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x8b6964: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8b6968: r0 = GetNavigation.size()
    //     0x8b6968: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x8b696c: LoadField: d0 = r0->field_f
    //     0x8b696c: ldur            d0, [x0, #0xf]
    // 0x8b6970: d1 = 0.300000
    //     0x8b6970: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0x8b6974: ldr             d1, [x17, #0x658]
    // 0x8b6978: fmul            d2, d0, d1
    // 0x8b697c: stur            d2, [fp, #-0x58]
    // 0x8b6980: r0 = BoxConstraints()
    //     0x8b6980: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x8b6984: StoreField: r0->field_7 = rZR
    //     0x8b6984: stur            xzr, [x0, #7]
    // 0x8b6988: d0 = inf
    //     0x8b6988: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x8b698c: StoreField: r0->field_f = d0
    //     0x8b698c: stur            d0, [x0, #0xf]
    // 0x8b6990: ArrayStore: r0[0] = rZR  ; List_8
    //     0x8b6990: stur            xzr, [x0, #0x17]
    // 0x8b6994: ldur            d0, [fp, #-0x58]
    // 0x8b6998: StoreField: r0->field_1f = d0
    //     0x8b6998: stur            d0, [x0, #0x1f]
    // 0x8b699c: ldur            x2, [fp, #-8]
    // 0x8b69a0: stur            x0, [fp, #-0x10]
    // 0x8b69a4: LoadField: r1 = r2->field_f
    //     0x8b69a4: ldur            w1, [x2, #0xf]
    // 0x8b69a8: DecompressPointer r1
    //     0x8b69a8: add             x1, x1, HEAP, lsl #32
    // 0x8b69ac: r0 = controller()
    //     0x8b69ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x8b69b0: LoadField: r1 = r0->field_67
    //     0x8b69b0: ldur            w1, [x0, #0x67]
    // 0x8b69b4: DecompressPointer r1
    //     0x8b69b4: add             x1, x1, HEAP, lsl #32
    // 0x8b69b8: r0 = value()
    //     0x8b69b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x8b69bc: cmp             w0, NULL
    // 0x8b69c0: b.ne            #0x8b69cc
    // 0x8b69c4: r3 = Null
    //     0x8b69c4: mov             x3, NULL
    // 0x8b69c8: b               #0x8b69d8
    // 0x8b69cc: LoadField: r1 = r0->field_2b
    //     0x8b69cc: ldur            w1, [x0, #0x2b]
    // 0x8b69d0: DecompressPointer r1
    //     0x8b69d0: add             x1, x1, HEAP, lsl #32
    // 0x8b69d4: mov             x3, x1
    // 0x8b69d8: ldur            x0, [fp, #-8]
    // 0x8b69dc: stur            x3, [fp, #-0x18]
    // 0x8b69e0: r1 = Null
    //     0x8b69e0: mov             x1, NULL
    // 0x8b69e4: r2 = 4
    //     0x8b69e4: movz            x2, #0x4
    // 0x8b69e8: r0 = AllocateArray()
    //     0x8b69e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8b69ec: mov             x1, x0
    // 0x8b69f0: ldur            x0, [fp, #-0x18]
    // 0x8b69f4: StoreField: r1->field_f = r0
    //     0x8b69f4: stur            w0, [x1, #0xf]
    // 0x8b69f8: r16 = " has accepted your order, it is already under process"
    //     0x8b69f8: add             x16, PP, #0x36, lsl #12  ; [pp+0x360f8] " has accepted your order, it is already under process"
    //     0x8b69fc: ldr             x16, [x16, #0xf8]
    // 0x8b6a00: StoreField: r1->field_13 = r16
    //     0x8b6a00: stur            w16, [x1, #0x13]
    // 0x8b6a04: str             x1, [SP]
    // 0x8b6a08: r0 = _interpolate()
    //     0x8b6a08: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8b6a0c: mov             x1, x0
    // 0x8b6a10: ldur            x2, [fp, #-8]
    // 0x8b6a14: stur            x1, [fp, #-0x18]
    // 0x8b6a18: LoadField: r0 = r2->field_13
    //     0x8b6a18: ldur            w0, [x2, #0x13]
    // 0x8b6a1c: DecompressPointer r0
    //     0x8b6a1c: add             x0, x0, HEAP, lsl #32
    // 0x8b6a20: cmp             w0, NULL
    // 0x8b6a24: b.eq            #0x8b6acc
    // 0x8b6a28: LoadField: r3 = r0->field_b
    //     0x8b6a28: ldur            w3, [x0, #0xb]
    // 0x8b6a2c: DecompressPointer r3
    //     0x8b6a2c: add             x3, x3, HEAP, lsl #32
    // 0x8b6a30: cmp             w3, NULL
    // 0x8b6a34: b.eq            #0x8b6acc
    // 0x8b6a38: r0 = 60
    //     0x8b6a38: movz            x0, #0x3c
    // 0x8b6a3c: branchIfSmi(r3, 0x8b6a48)
    //     0x8b6a3c: tbz             w3, #0, #0x8b6a48
    // 0x8b6a40: r0 = LoadClassIdInstr(r3)
    //     0x8b6a40: ldur            x0, [x3, #-1]
    //     0x8b6a44: ubfx            x0, x0, #0xc, #0x14
    // 0x8b6a48: stp             xzr, x3, [SP]
    // 0x8b6a4c: mov             lr, x0
    // 0x8b6a50: ldr             lr, [x21, lr, lsl #3]
    // 0x8b6a54: blr             lr
    // 0x8b6a58: tbz             w0, #4, #0x8b6acc
    // 0x8b6a5c: ldur            x0, [fp, #-8]
    // 0x8b6a60: r1 = Null
    //     0x8b6a60: mov             x1, NULL
    // 0x8b6a64: r2 = 6
    //     0x8b6a64: movz            x2, #0x6
    // 0x8b6a68: r0 = AllocateArray()
    //     0x8b6a68: bl              #0x16f7198  ; AllocateArrayStub
    // 0x8b6a6c: r16 = "Cancel ("
    //     0x8b6a6c: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fde8] "Cancel ("
    //     0x8b6a70: ldr             x16, [x16, #0xde8]
    // 0x8b6a74: StoreField: r0->field_f = r16
    //     0x8b6a74: stur            w16, [x0, #0xf]
    // 0x8b6a78: ldur            x2, [fp, #-8]
    // 0x8b6a7c: LoadField: r1 = r2->field_13
    //     0x8b6a7c: ldur            w1, [x2, #0x13]
    // 0x8b6a80: DecompressPointer r1
    //     0x8b6a80: add             x1, x1, HEAP, lsl #32
    // 0x8b6a84: cmp             w1, NULL
    // 0x8b6a88: b.ne            #0x8b6a94
    // 0x8b6a8c: r1 = Null
    //     0x8b6a8c: mov             x1, NULL
    // 0x8b6a90: b               #0x8b6aa0
    // 0x8b6a94: LoadField: r3 = r1->field_23
    //     0x8b6a94: ldur            w3, [x1, #0x23]
    // 0x8b6a98: DecompressPointer r3
    //     0x8b6a98: add             x3, x3, HEAP, lsl #32
    // 0x8b6a9c: mov             x1, x3
    // 0x8b6aa0: cmp             w1, NULL
    // 0x8b6aa4: b.ne            #0x8b6aac
    // 0x8b6aa8: r1 = ""
    //     0x8b6aa8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x8b6aac: StoreField: r0->field_13 = r1
    //     0x8b6aac: stur            w1, [x0, #0x13]
    // 0x8b6ab0: r16 = " Charge)"
    //     0x8b6ab0: add             x16, PP, #0x3f, lsl #12  ; [pp+0x3fdf0] " Charge)"
    //     0x8b6ab4: ldr             x16, [x16, #0xdf0]
    // 0x8b6ab8: ArrayStore: r0[0] = r16  ; List_4
    //     0x8b6ab8: stur            w16, [x0, #0x17]
    // 0x8b6abc: str             x0, [SP]
    // 0x8b6ac0: r0 = _interpolate()
    //     0x8b6ac0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x8b6ac4: mov             x4, x0
    // 0x8b6ac8: b               #0x8b6ad4
    // 0x8b6acc: r4 = "Cancel Order"
    //     0x8b6acc: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fdf8] "Cancel Order"
    //     0x8b6ad0: ldr             x4, [x4, #0xdf8]
    // 0x8b6ad4: ldur            x2, [fp, #-8]
    // 0x8b6ad8: ldur            x3, [fp, #-0x10]
    // 0x8b6adc: ldur            x0, [fp, #-0x18]
    // 0x8b6ae0: stur            x4, [fp, #-0x30]
    // 0x8b6ae4: LoadField: r5 = r2->field_13
    //     0x8b6ae4: ldur            w5, [x2, #0x13]
    // 0x8b6ae8: DecompressPointer r5
    //     0x8b6ae8: add             x5, x5, HEAP, lsl #32
    // 0x8b6aec: stur            x5, [fp, #-0x28]
    // 0x8b6af0: LoadField: r6 = r2->field_1b
    //     0x8b6af0: ldur            w6, [x2, #0x1b]
    // 0x8b6af4: DecompressPointer r6
    //     0x8b6af4: add             x6, x6, HEAP, lsl #32
    // 0x8b6af8: ldr             x1, [fp, #0x10]
    // 0x8b6afc: stur            x6, [fp, #-0x20]
    // 0x8b6b00: r0 = of()
    //     0x8b6b00: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x8b6b04: LoadField: r1 = r0->field_87
    //     0x8b6b04: ldur            w1, [x0, #0x87]
    // 0x8b6b08: DecompressPointer r1
    //     0x8b6b08: add             x1, x1, HEAP, lsl #32
    // 0x8b6b0c: LoadField: r0 = r1->field_2b
    //     0x8b6b0c: ldur            w0, [x1, #0x2b]
    // 0x8b6b10: DecompressPointer r0
    //     0x8b6b10: add             x0, x0, HEAP, lsl #32
    // 0x8b6b14: stur            x0, [fp, #-0x38]
    // 0x8b6b18: r1 = Instance_Color
    //     0x8b6b18: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x8b6b1c: d0 = 0.400000
    //     0x8b6b1c: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x8b6b20: r0 = withOpacity()
    //     0x8b6b20: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x8b6b24: r16 = 12.000000
    //     0x8b6b24: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x8b6b28: ldr             x16, [x16, #0x9e8]
    // 0x8b6b2c: stp             x0, x16, [SP]
    // 0x8b6b30: ldur            x1, [fp, #-0x38]
    // 0x8b6b34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x8b6b34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x8b6b38: ldr             x4, [x4, #0xaa0]
    // 0x8b6b3c: r0 = copyWith()
    //     0x8b6b3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x8b6b40: stur            x0, [fp, #-0x38]
    // 0x8b6b44: r0 = Radius()
    //     0x8b6b44: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x8b6b48: d0 = 12.000000
    //     0x8b6b48: fmov            d0, #12.00000000
    // 0x8b6b4c: stur            x0, [fp, #-0x40]
    // 0x8b6b50: StoreField: r0->field_7 = d0
    //     0x8b6b50: stur            d0, [x0, #7]
    // 0x8b6b54: StoreField: r0->field_f = d0
    //     0x8b6b54: stur            d0, [x0, #0xf]
    // 0x8b6b58: r0 = BorderRadius()
    //     0x8b6b58: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x8b6b5c: mov             x1, x0
    // 0x8b6b60: ldur            x0, [fp, #-0x40]
    // 0x8b6b64: stur            x1, [fp, #-0x48]
    // 0x8b6b68: StoreField: r1->field_7 = r0
    //     0x8b6b68: stur            w0, [x1, #7]
    // 0x8b6b6c: StoreField: r1->field_b = r0
    //     0x8b6b6c: stur            w0, [x1, #0xb]
    // 0x8b6b70: StoreField: r1->field_f = r0
    //     0x8b6b70: stur            w0, [x1, #0xf]
    // 0x8b6b74: StoreField: r1->field_13 = r0
    //     0x8b6b74: stur            w0, [x1, #0x13]
    // 0x8b6b78: r0 = OutlineInputBorder()
    //     0x8b6b78: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x8b6b7c: mov             x1, x0
    // 0x8b6b80: ldur            x0, [fp, #-0x48]
    // 0x8b6b84: stur            x1, [fp, #-0x40]
    // 0x8b6b88: StoreField: r1->field_13 = r0
    //     0x8b6b88: stur            w0, [x1, #0x13]
    // 0x8b6b8c: d0 = 4.000000
    //     0x8b6b8c: fmov            d0, #4.00000000
    // 0x8b6b90: StoreField: r1->field_b = d0
    //     0x8b6b90: stur            d0, [x1, #0xb]
    // 0x8b6b94: r0 = Instance_BorderSide
    //     0x8b6b94: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x8b6b98: ldr             x0, [x0, #0x118]
    // 0x8b6b9c: StoreField: r1->field_7 = r0
    //     0x8b6b9c: stur            w0, [x1, #7]
    // 0x8b6ba0: r0 = Radius()
    //     0x8b6ba0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x8b6ba4: d0 = 12.000000
    //     0x8b6ba4: fmov            d0, #12.00000000
    // 0x8b6ba8: stur            x0, [fp, #-0x48]
    // 0x8b6bac: StoreField: r0->field_7 = d0
    //     0x8b6bac: stur            d0, [x0, #7]
    // 0x8b6bb0: StoreField: r0->field_f = d0
    //     0x8b6bb0: stur            d0, [x0, #0xf]
    // 0x8b6bb4: r0 = BorderRadius()
    //     0x8b6bb4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x8b6bb8: mov             x1, x0
    // 0x8b6bbc: ldur            x0, [fp, #-0x48]
    // 0x8b6bc0: stur            x1, [fp, #-0x50]
    // 0x8b6bc4: StoreField: r1->field_7 = r0
    //     0x8b6bc4: stur            w0, [x1, #7]
    // 0x8b6bc8: StoreField: r1->field_b = r0
    //     0x8b6bc8: stur            w0, [x1, #0xb]
    // 0x8b6bcc: StoreField: r1->field_f = r0
    //     0x8b6bcc: stur            w0, [x1, #0xf]
    // 0x8b6bd0: StoreField: r1->field_13 = r0
    //     0x8b6bd0: stur            w0, [x1, #0x13]
    // 0x8b6bd4: r0 = OutlineInputBorder()
    //     0x8b6bd4: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x8b6bd8: mov             x1, x0
    // 0x8b6bdc: ldur            x0, [fp, #-0x50]
    // 0x8b6be0: stur            x1, [fp, #-0x48]
    // 0x8b6be4: StoreField: r1->field_13 = r0
    //     0x8b6be4: stur            w0, [x1, #0x13]
    // 0x8b6be8: d0 = 4.000000
    //     0x8b6be8: fmov            d0, #4.00000000
    // 0x8b6bec: StoreField: r1->field_b = d0
    //     0x8b6bec: stur            d0, [x1, #0xb]
    // 0x8b6bf0: r0 = Instance_BorderSide
    //     0x8b6bf0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x8b6bf4: ldr             x0, [x0, #0x118]
    // 0x8b6bf8: StoreField: r1->field_7 = r0
    //     0x8b6bf8: stur            w0, [x1, #7]
    // 0x8b6bfc: r0 = InputDecoration()
    //     0x8b6bfc: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0x8b6c00: mov             x1, x0
    // 0x8b6c04: r0 = "Mention the reason"
    //     0x8b6c04: add             x0, PP, #0x36, lsl #12  ; [pp+0x36120] "Mention the reason"
    //     0x8b6c08: ldr             x0, [x0, #0x120]
    // 0x8b6c0c: stur            x1, [fp, #-0x50]
    // 0x8b6c10: StoreField: r1->field_13 = r0
    //     0x8b6c10: stur            w0, [x1, #0x13]
    // 0x8b6c14: ldur            x0, [fp, #-0x38]
    // 0x8b6c18: ArrayStore: r1[0] = r0  ; List_4
    //     0x8b6c18: stur            w0, [x1, #0x17]
    // 0x8b6c1c: r0 = true
    //     0x8b6c1c: add             x0, NULL, #0x20  ; true
    // 0x8b6c20: StoreField: r1->field_47 = r0
    //     0x8b6c20: stur            w0, [x1, #0x47]
    // 0x8b6c24: StoreField: r1->field_4b = r0
    //     0x8b6c24: stur            w0, [x1, #0x4b]
    // 0x8b6c28: ldur            x2, [fp, #-0x48]
    // 0x8b6c2c: StoreField: r1->field_c3 = r2
    //     0x8b6c2c: stur            w2, [x1, #0xc3]
    // 0x8b6c30: ldur            x2, [fp, #-0x40]
    // 0x8b6c34: StoreField: r1->field_cf = r2
    //     0x8b6c34: stur            w2, [x1, #0xcf]
    // 0x8b6c38: StoreField: r1->field_d7 = r0
    //     0x8b6c38: stur            w0, [x1, #0xd7]
    // 0x8b6c3c: r0 = Radius()
    //     0x8b6c3c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x8b6c40: d0 = 30.000000
    //     0x8b6c40: fmov            d0, #30.00000000
    // 0x8b6c44: stur            x0, [fp, #-0x38]
    // 0x8b6c48: StoreField: r0->field_7 = d0
    //     0x8b6c48: stur            d0, [x0, #7]
    // 0x8b6c4c: StoreField: r0->field_f = d0
    //     0x8b6c4c: stur            d0, [x0, #0xf]
    // 0x8b6c50: r0 = BorderRadius()
    //     0x8b6c50: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x8b6c54: mov             x2, x0
    // 0x8b6c58: ldur            x0, [fp, #-0x38]
    // 0x8b6c5c: stur            x2, [fp, #-0x40]
    // 0x8b6c60: StoreField: r2->field_7 = r0
    //     0x8b6c60: stur            w0, [x2, #7]
    // 0x8b6c64: StoreField: r2->field_b = r0
    //     0x8b6c64: stur            w0, [x2, #0xb]
    // 0x8b6c68: StoreField: r2->field_f = r0
    //     0x8b6c68: stur            w0, [x2, #0xf]
    // 0x8b6c6c: StoreField: r2->field_13 = r0
    //     0x8b6c6c: stur            w0, [x2, #0x13]
    // 0x8b6c70: ldr             x1, [fp, #0x10]
    // 0x8b6c74: r0 = of()
    //     0x8b6c74: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x8b6c78: LoadField: r1 = r0->field_5b
    //     0x8b6c78: ldur            w1, [x0, #0x5b]
    // 0x8b6c7c: DecompressPointer r1
    //     0x8b6c7c: add             x1, x1, HEAP, lsl #32
    // 0x8b6c80: stur            x1, [fp, #-0x38]
    // 0x8b6c84: r0 = BorderSide()
    //     0x8b6c84: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x8b6c88: mov             x1, x0
    // 0x8b6c8c: ldur            x0, [fp, #-0x38]
    // 0x8b6c90: stur            x1, [fp, #-0x48]
    // 0x8b6c94: StoreField: r1->field_7 = r0
    //     0x8b6c94: stur            w0, [x1, #7]
    // 0x8b6c98: d0 = 1.000000
    //     0x8b6c98: fmov            d0, #1.00000000
    // 0x8b6c9c: StoreField: r1->field_b = d0
    //     0x8b6c9c: stur            d0, [x1, #0xb]
    // 0x8b6ca0: r0 = Instance_BorderStyle
    //     0x8b6ca0: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x8b6ca4: ldr             x0, [x0, #0xf68]
    // 0x8b6ca8: StoreField: r1->field_13 = r0
    //     0x8b6ca8: stur            w0, [x1, #0x13]
    // 0x8b6cac: d0 = -1.000000
    //     0x8b6cac: fmov            d0, #-1.00000000
    // 0x8b6cb0: ArrayStore: r1[0] = d0  ; List_8
    //     0x8b6cb0: stur            d0, [x1, #0x17]
    // 0x8b6cb4: r0 = CancelOrderConfirmBottomSheet()
    //     0x8b6cb4: bl              #0x8b1964  ; AllocateCancelOrderConfirmBottomSheetStub -> CancelOrderConfirmBottomSheet (size=0x34)
    // 0x8b6cb8: mov             x3, x0
    // 0x8b6cbc: ldur            x0, [fp, #-0x28]
    // 0x8b6cc0: stur            x3, [fp, #-0x38]
    // 0x8b6cc4: StoreField: r3->field_b = r0
    //     0x8b6cc4: stur            w0, [x3, #0xb]
    // 0x8b6cc8: ldur            x0, [fp, #-0x18]
    // 0x8b6ccc: StoreField: r3->field_f = r0
    //     0x8b6ccc: stur            w0, [x3, #0xf]
    // 0x8b6cd0: r0 = "Do you want to cancel this order\?"
    //     0x8b6cd0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36128] "Do you want to cancel this order\?"
    //     0x8b6cd4: ldr             x0, [x0, #0x128]
    // 0x8b6cd8: StoreField: r3->field_13 = r0
    //     0x8b6cd8: stur            w0, [x3, #0x13]
    // 0x8b6cdc: ldur            x0, [fp, #-0x30]
    // 0x8b6ce0: ArrayStore: r3[0] = r0  ; List_4
    //     0x8b6ce0: stur            w0, [x3, #0x17]
    // 0x8b6ce4: ldur            x2, [fp, #-8]
    // 0x8b6ce8: r1 = Function '<anonymous closure>':.
    //     0x8b6ce8: add             x1, PP, #0x43, lsl #12  ; [pp+0x431d8] AnonymousClosure: (0x8b19a0), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::cancelOrder (0x8b12cc)
    //     0x8b6cec: ldr             x1, [x1, #0x1d8]
    // 0x8b6cf0: r0 = AllocateClosure()
    //     0x8b6cf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x8b6cf4: mov             x1, x0
    // 0x8b6cf8: ldur            x0, [fp, #-0x38]
    // 0x8b6cfc: StoreField: r0->field_1b = r1
    //     0x8b6cfc: stur            w1, [x0, #0x1b]
    // 0x8b6d00: ldur            x1, [fp, #-0x40]
    // 0x8b6d04: StoreField: r0->field_1f = r1
    //     0x8b6d04: stur            w1, [x0, #0x1f]
    // 0x8b6d08: ldur            x1, [fp, #-0x50]
    // 0x8b6d0c: StoreField: r0->field_23 = r1
    //     0x8b6d0c: stur            w1, [x0, #0x23]
    // 0x8b6d10: ldur            x1, [fp, #-0x48]
    // 0x8b6d14: StoreField: r0->field_27 = r1
    //     0x8b6d14: stur            w1, [x0, #0x27]
    // 0x8b6d18: r1 = "Go, Back"
    //     0x8b6d18: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fe08] "Go, Back"
    //     0x8b6d1c: ldr             x1, [x1, #0xe08]
    // 0x8b6d20: StoreField: r0->field_2b = r1
    //     0x8b6d20: stur            w1, [x0, #0x2b]
    // 0x8b6d24: ldur            x1, [fp, #-0x20]
    // 0x8b6d28: StoreField: r0->field_2f = r1
    //     0x8b6d28: stur            w1, [x0, #0x2f]
    // 0x8b6d2c: r0 = ConstrainedBox()
    //     0x8b6d2c: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x8b6d30: ldur            x1, [fp, #-0x10]
    // 0x8b6d34: StoreField: r0->field_f = r1
    //     0x8b6d34: stur            w1, [x0, #0xf]
    // 0x8b6d38: ldur            x1, [fp, #-0x38]
    // 0x8b6d3c: StoreField: r0->field_b = r1
    //     0x8b6d3c: stur            w1, [x0, #0xb]
    // 0x8b6d40: LeaveFrame
    //     0x8b6d40: mov             SP, fp
    //     0x8b6d44: ldp             fp, lr, [SP], #0x10
    // 0x8b6d48: ret
    //     0x8b6d48: ret             
    // 0x8b6d4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6d4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6d50: b               #0x8b682c
  }
  [closure] void cancelOrder(dynamic, OrderCancellationPopup?, String, BuildContext, String) {
    // ** addr: 0x8b6d54, size: 0x48
    // 0x8b6d54: EnterFrame
    //     0x8b6d54: stp             fp, lr, [SP, #-0x10]!
    //     0x8b6d58: mov             fp, SP
    // 0x8b6d5c: ldr             x0, [fp, #0x30]
    // 0x8b6d60: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8b6d60: ldur            w1, [x0, #0x17]
    // 0x8b6d64: DecompressPointer r1
    //     0x8b6d64: add             x1, x1, HEAP, lsl #32
    // 0x8b6d68: CheckStackOverflow
    //     0x8b6d68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8b6d6c: cmp             SP, x16
    //     0x8b6d70: b.ls            #0x8b6d94
    // 0x8b6d74: ldr             x2, [fp, #0x28]
    // 0x8b6d78: ldr             x3, [fp, #0x20]
    // 0x8b6d7c: ldr             x5, [fp, #0x18]
    // 0x8b6d80: ldr             x6, [fp, #0x10]
    // 0x8b6d84: r0 = cancelOrder()
    //     0x8b6d84: bl              #0x8b66f4  ; [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::cancelOrder
    // 0x8b6d88: LeaveFrame
    //     0x8b6d88: mov             SP, fp
    //     0x8b6d8c: ldp             fp, lr, [SP], #0x10
    // 0x8b6d90: ret
    //     0x8b6d90: ret             
    // 0x8b6d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8b6d94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8b6d98: b               #0x8b6d74
  }
  _ body(/* No info */) {
    // ** addr: 0x14a879c, size: 0x94
    // 0x14a879c: EnterFrame
    //     0x14a879c: stp             fp, lr, [SP, #-0x10]!
    //     0x14a87a0: mov             fp, SP
    // 0x14a87a4: AllocStack(0x18)
    //     0x14a87a4: sub             SP, SP, #0x18
    // 0x14a87a8: SetupParameters(OrdersView this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14a87a8: mov             x0, x1
    //     0x14a87ac: stur            x1, [fp, #-8]
    //     0x14a87b0: stur            x2, [fp, #-0x10]
    // 0x14a87b4: r1 = 2
    //     0x14a87b4: movz            x1, #0x2
    // 0x14a87b8: r0 = AllocateContext()
    //     0x14a87b8: bl              #0x16f6108  ; AllocateContextStub
    // 0x14a87bc: ldur            x2, [fp, #-8]
    // 0x14a87c0: stur            x0, [fp, #-0x18]
    // 0x14a87c4: StoreField: r0->field_f = r2
    //     0x14a87c4: stur            w2, [x0, #0xf]
    // 0x14a87c8: ldur            x1, [fp, #-0x10]
    // 0x14a87cc: StoreField: r0->field_13 = r1
    //     0x14a87cc: stur            w1, [x0, #0x13]
    // 0x14a87d0: r0 = Obx()
    //     0x14a87d0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14a87d4: ldur            x2, [fp, #-0x18]
    // 0x14a87d8: r1 = Function '<anonymous closure>':.
    //     0x14a87d8: add             x1, PP, #0x43, lsl #12  ; [pp+0x43100] AnonymousClosure: (0x85fec8), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::body (0x14a879c)
    //     0x14a87dc: ldr             x1, [x1, #0x100]
    // 0x14a87e0: stur            x0, [fp, #-0x10]
    // 0x14a87e4: r0 = AllocateClosure()
    //     0x14a87e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a87e8: mov             x1, x0
    // 0x14a87ec: ldur            x0, [fp, #-0x10]
    // 0x14a87f0: StoreField: r0->field_b = r1
    //     0x14a87f0: stur            w1, [x0, #0xb]
    // 0x14a87f4: r0 = WillPopScope()
    //     0x14a87f4: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14a87f8: mov             x3, x0
    // 0x14a87fc: ldur            x0, [fp, #-0x10]
    // 0x14a8800: stur            x3, [fp, #-0x18]
    // 0x14a8804: StoreField: r3->field_b = r0
    //     0x14a8804: stur            w0, [x3, #0xb]
    // 0x14a8808: ldur            x2, [fp, #-8]
    // 0x14a880c: r1 = Function '_onBackPress@1474418457':.
    //     0x14a880c: add             x1, PP, #0x43, lsl #12  ; [pp+0x43108] AnonymousClosure: (0x14a8830), in [package:customer_app/app/presentation/views/basic/orders/orders_view.dart] OrdersView::_onBackPress (0x14057f0)
    //     0x14a8810: ldr             x1, [x1, #0x108]
    // 0x14a8814: r0 = AllocateClosure()
    //     0x14a8814: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14a8818: mov             x1, x0
    // 0x14a881c: ldur            x0, [fp, #-0x18]
    // 0x14a8820: StoreField: r0->field_f = r1
    //     0x14a8820: stur            w1, [x0, #0xf]
    // 0x14a8824: LeaveFrame
    //     0x14a8824: mov             SP, fp
    //     0x14a8828: ldp             fp, lr, [SP], #0x10
    // 0x14a882c: ret
    //     0x14a882c: ret             
  }
  [closure] Future<bool> _onBackPress(dynamic) {
    // ** addr: 0x14a8830, size: 0x38
    // 0x14a8830: EnterFrame
    //     0x14a8830: stp             fp, lr, [SP, #-0x10]!
    //     0x14a8834: mov             fp, SP
    // 0x14a8838: ldr             x0, [fp, #0x10]
    // 0x14a883c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14a883c: ldur            w1, [x0, #0x17]
    // 0x14a8840: DecompressPointer r1
    //     0x14a8840: add             x1, x1, HEAP, lsl #32
    // 0x14a8844: CheckStackOverflow
    //     0x14a8844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14a8848: cmp             SP, x16
    //     0x14a884c: b.ls            #0x14a8860
    // 0x14a8850: r0 = _onBackPress()
    //     0x14a8850: bl              #0x14057f0  ; [package:customer_app/app/presentation/views/basic/orders/orders_view.dart] OrdersView::_onBackPress
    // 0x14a8854: LeaveFrame
    //     0x14a8854: mov             SP, fp
    //     0x14a8858: ldp             fp, lr, [SP], #0x10
    // 0x14a885c: ret
    //     0x14a885c: ret             
    // 0x14a8860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14a8860: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14a8864: b               #0x14a8850
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15dbd94, size: 0x2b4
    // 0x15dbd94: EnterFrame
    //     0x15dbd94: stp             fp, lr, [SP, #-0x10]!
    //     0x15dbd98: mov             fp, SP
    // 0x15dbd9c: AllocStack(0x30)
    //     0x15dbd9c: sub             SP, SP, #0x30
    // 0x15dbda0: SetupParameters(OrdersView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15dbda0: stur            x1, [fp, #-8]
    //     0x15dbda4: stur            x2, [fp, #-0x10]
    // 0x15dbda8: CheckStackOverflow
    //     0x15dbda8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dbdac: cmp             SP, x16
    //     0x15dbdb0: b.ls            #0x15dc040
    // 0x15dbdb4: r1 = 2
    //     0x15dbdb4: movz            x1, #0x2
    // 0x15dbdb8: r0 = AllocateContext()
    //     0x15dbdb8: bl              #0x16f6108  ; AllocateContextStub
    // 0x15dbdbc: ldur            x1, [fp, #-8]
    // 0x15dbdc0: stur            x0, [fp, #-0x18]
    // 0x15dbdc4: StoreField: r0->field_f = r1
    //     0x15dbdc4: stur            w1, [x0, #0xf]
    // 0x15dbdc8: ldur            x2, [fp, #-0x10]
    // 0x15dbdcc: StoreField: r0->field_13 = r2
    //     0x15dbdcc: stur            w2, [x0, #0x13]
    // 0x15dbdd0: r0 = Obx()
    //     0x15dbdd0: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15dbdd4: ldur            x2, [fp, #-0x18]
    // 0x15dbdd8: r1 = Function '<anonymous closure>':.
    //     0x15dbdd8: add             x1, PP, #0x43, lsl #12  ; [pp+0x431e0] AnonymousClosure: (0x15d3254), in [package:customer_app/app/presentation/views/glass/profile/profile_view.dart] ProfileView::appBar (0x15e5558)
    //     0x15dbddc: ldr             x1, [x1, #0x1e0]
    // 0x15dbde0: stur            x0, [fp, #-0x10]
    // 0x15dbde4: r0 = AllocateClosure()
    //     0x15dbde4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dbde8: mov             x1, x0
    // 0x15dbdec: ldur            x0, [fp, #-0x10]
    // 0x15dbdf0: StoreField: r0->field_b = r1
    //     0x15dbdf0: stur            w1, [x0, #0xb]
    // 0x15dbdf4: ldur            x1, [fp, #-8]
    // 0x15dbdf8: r0 = controller()
    //     0x15dbdf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dbdfc: LoadField: r1 = r0->field_7f
    //     0x15dbdfc: ldur            w1, [x0, #0x7f]
    // 0x15dbe00: DecompressPointer r1
    //     0x15dbe00: add             x1, x1, HEAP, lsl #32
    // 0x15dbe04: r0 = value()
    //     0x15dbe04: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dbe08: tbnz            w0, #4, #0x15dbea0
    // 0x15dbe0c: ldur            x2, [fp, #-0x18]
    // 0x15dbe10: LoadField: r1 = r2->field_13
    //     0x15dbe10: ldur            w1, [x2, #0x13]
    // 0x15dbe14: DecompressPointer r1
    //     0x15dbe14: add             x1, x1, HEAP, lsl #32
    // 0x15dbe18: r0 = of()
    //     0x15dbe18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dbe1c: LoadField: r1 = r0->field_5b
    //     0x15dbe1c: ldur            w1, [x0, #0x5b]
    // 0x15dbe20: DecompressPointer r1
    //     0x15dbe20: add             x1, x1, HEAP, lsl #32
    // 0x15dbe24: stur            x1, [fp, #-8]
    // 0x15dbe28: r0 = ColorFilter()
    //     0x15dbe28: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dbe2c: mov             x1, x0
    // 0x15dbe30: ldur            x0, [fp, #-8]
    // 0x15dbe34: stur            x1, [fp, #-0x20]
    // 0x15dbe38: StoreField: r1->field_7 = r0
    //     0x15dbe38: stur            w0, [x1, #7]
    // 0x15dbe3c: r0 = Instance_BlendMode
    //     0x15dbe3c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dbe40: ldr             x0, [x0, #0xb30]
    // 0x15dbe44: StoreField: r1->field_b = r0
    //     0x15dbe44: stur            w0, [x1, #0xb]
    // 0x15dbe48: r2 = 1
    //     0x15dbe48: movz            x2, #0x1
    // 0x15dbe4c: StoreField: r1->field_13 = r2
    //     0x15dbe4c: stur            x2, [x1, #0x13]
    // 0x15dbe50: r0 = SvgPicture()
    //     0x15dbe50: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dbe54: stur            x0, [fp, #-8]
    // 0x15dbe58: ldur            x16, [fp, #-0x20]
    // 0x15dbe5c: str             x16, [SP]
    // 0x15dbe60: mov             x1, x0
    // 0x15dbe64: r2 = "assets/images/search.svg"
    //     0x15dbe64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15dbe68: ldr             x2, [x2, #0xa30]
    // 0x15dbe6c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dbe6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dbe70: ldr             x4, [x4, #0xa38]
    // 0x15dbe74: r0 = SvgPicture.asset()
    //     0x15dbe74: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dbe78: r0 = Align()
    //     0x15dbe78: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dbe7c: r3 = Instance_Alignment
    //     0x15dbe7c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dbe80: ldr             x3, [x3, #0xb10]
    // 0x15dbe84: StoreField: r0->field_f = r3
    //     0x15dbe84: stur            w3, [x0, #0xf]
    // 0x15dbe88: r4 = 1.000000
    //     0x15dbe88: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dbe8c: StoreField: r0->field_13 = r4
    //     0x15dbe8c: stur            w4, [x0, #0x13]
    // 0x15dbe90: ArrayStore: r0[0] = r4  ; List_4
    //     0x15dbe90: stur            w4, [x0, #0x17]
    // 0x15dbe94: ldur            x1, [fp, #-8]
    // 0x15dbe98: StoreField: r0->field_b = r1
    //     0x15dbe98: stur            w1, [x0, #0xb]
    // 0x15dbe9c: b               #0x15dbf50
    // 0x15dbea0: ldur            x5, [fp, #-0x18]
    // 0x15dbea4: r4 = 1.000000
    //     0x15dbea4: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dbea8: r0 = Instance_BlendMode
    //     0x15dbea8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dbeac: ldr             x0, [x0, #0xb30]
    // 0x15dbeb0: r3 = Instance_Alignment
    //     0x15dbeb0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dbeb4: ldr             x3, [x3, #0xb10]
    // 0x15dbeb8: r2 = 1
    //     0x15dbeb8: movz            x2, #0x1
    // 0x15dbebc: LoadField: r1 = r5->field_13
    //     0x15dbebc: ldur            w1, [x5, #0x13]
    // 0x15dbec0: DecompressPointer r1
    //     0x15dbec0: add             x1, x1, HEAP, lsl #32
    // 0x15dbec4: r0 = of()
    //     0x15dbec4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dbec8: LoadField: r1 = r0->field_5b
    //     0x15dbec8: ldur            w1, [x0, #0x5b]
    // 0x15dbecc: DecompressPointer r1
    //     0x15dbecc: add             x1, x1, HEAP, lsl #32
    // 0x15dbed0: stur            x1, [fp, #-8]
    // 0x15dbed4: r0 = ColorFilter()
    //     0x15dbed4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dbed8: mov             x1, x0
    // 0x15dbedc: ldur            x0, [fp, #-8]
    // 0x15dbee0: stur            x1, [fp, #-0x20]
    // 0x15dbee4: StoreField: r1->field_7 = r0
    //     0x15dbee4: stur            w0, [x1, #7]
    // 0x15dbee8: r0 = Instance_BlendMode
    //     0x15dbee8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dbeec: ldr             x0, [x0, #0xb30]
    // 0x15dbef0: StoreField: r1->field_b = r0
    //     0x15dbef0: stur            w0, [x1, #0xb]
    // 0x15dbef4: r0 = 1
    //     0x15dbef4: movz            x0, #0x1
    // 0x15dbef8: StoreField: r1->field_13 = r0
    //     0x15dbef8: stur            x0, [x1, #0x13]
    // 0x15dbefc: r0 = SvgPicture()
    //     0x15dbefc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dbf00: stur            x0, [fp, #-8]
    // 0x15dbf04: ldur            x16, [fp, #-0x20]
    // 0x15dbf08: str             x16, [SP]
    // 0x15dbf0c: mov             x1, x0
    // 0x15dbf10: r2 = "assets/images/appbar_arrow.svg"
    //     0x15dbf10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15dbf14: ldr             x2, [x2, #0xa40]
    // 0x15dbf18: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15dbf18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15dbf1c: ldr             x4, [x4, #0xa38]
    // 0x15dbf20: r0 = SvgPicture.asset()
    //     0x15dbf20: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dbf24: r0 = Align()
    //     0x15dbf24: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15dbf28: mov             x1, x0
    // 0x15dbf2c: r0 = Instance_Alignment
    //     0x15dbf2c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15dbf30: ldr             x0, [x0, #0xb10]
    // 0x15dbf34: StoreField: r1->field_f = r0
    //     0x15dbf34: stur            w0, [x1, #0xf]
    // 0x15dbf38: r0 = 1.000000
    //     0x15dbf38: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15dbf3c: StoreField: r1->field_13 = r0
    //     0x15dbf3c: stur            w0, [x1, #0x13]
    // 0x15dbf40: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dbf40: stur            w0, [x1, #0x17]
    // 0x15dbf44: ldur            x0, [fp, #-8]
    // 0x15dbf48: StoreField: r1->field_b = r0
    //     0x15dbf48: stur            w0, [x1, #0xb]
    // 0x15dbf4c: mov             x0, x1
    // 0x15dbf50: stur            x0, [fp, #-8]
    // 0x15dbf54: r0 = InkWell()
    //     0x15dbf54: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dbf58: mov             x3, x0
    // 0x15dbf5c: ldur            x0, [fp, #-8]
    // 0x15dbf60: stur            x3, [fp, #-0x20]
    // 0x15dbf64: StoreField: r3->field_b = r0
    //     0x15dbf64: stur            w0, [x3, #0xb]
    // 0x15dbf68: ldur            x2, [fp, #-0x18]
    // 0x15dbf6c: r1 = Function '<anonymous closure>':.
    //     0x15dbf6c: add             x1, PP, #0x43, lsl #12  ; [pp+0x431e8] AnonymousClosure: (0x15d3188), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15dbf70: ldr             x1, [x1, #0x1e8]
    // 0x15dbf74: r0 = AllocateClosure()
    //     0x15dbf74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dbf78: ldur            x2, [fp, #-0x20]
    // 0x15dbf7c: StoreField: r2->field_f = r0
    //     0x15dbf7c: stur            w0, [x2, #0xf]
    // 0x15dbf80: r0 = true
    //     0x15dbf80: add             x0, NULL, #0x20  ; true
    // 0x15dbf84: StoreField: r2->field_43 = r0
    //     0x15dbf84: stur            w0, [x2, #0x43]
    // 0x15dbf88: r1 = Instance_BoxShape
    //     0x15dbf88: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dbf8c: ldr             x1, [x1, #0x80]
    // 0x15dbf90: StoreField: r2->field_47 = r1
    //     0x15dbf90: stur            w1, [x2, #0x47]
    // 0x15dbf94: StoreField: r2->field_6f = r0
    //     0x15dbf94: stur            w0, [x2, #0x6f]
    // 0x15dbf98: r1 = false
    //     0x15dbf98: add             x1, NULL, #0x30  ; false
    // 0x15dbf9c: StoreField: r2->field_73 = r1
    //     0x15dbf9c: stur            w1, [x2, #0x73]
    // 0x15dbfa0: StoreField: r2->field_83 = r0
    //     0x15dbfa0: stur            w0, [x2, #0x83]
    // 0x15dbfa4: StoreField: r2->field_7b = r1
    //     0x15dbfa4: stur            w1, [x2, #0x7b]
    // 0x15dbfa8: r0 = Obx()
    //     0x15dbfa8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15dbfac: ldur            x2, [fp, #-0x18]
    // 0x15dbfb0: r1 = Function '<anonymous closure>':.
    //     0x15dbfb0: add             x1, PP, #0x43, lsl #12  ; [pp+0x431f0] AnonymousClosure: (0x15dc048), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::appBar (0x15dbd94)
    //     0x15dbfb4: ldr             x1, [x1, #0x1f0]
    // 0x15dbfb8: stur            x0, [fp, #-8]
    // 0x15dbfbc: r0 = AllocateClosure()
    //     0x15dbfbc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dbfc0: mov             x1, x0
    // 0x15dbfc4: ldur            x0, [fp, #-8]
    // 0x15dbfc8: StoreField: r0->field_b = r1
    //     0x15dbfc8: stur            w1, [x0, #0xb]
    // 0x15dbfcc: r1 = Null
    //     0x15dbfcc: mov             x1, NULL
    // 0x15dbfd0: r2 = 2
    //     0x15dbfd0: movz            x2, #0x2
    // 0x15dbfd4: r0 = AllocateArray()
    //     0x15dbfd4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15dbfd8: mov             x2, x0
    // 0x15dbfdc: ldur            x0, [fp, #-8]
    // 0x15dbfe0: stur            x2, [fp, #-0x18]
    // 0x15dbfe4: StoreField: r2->field_f = r0
    //     0x15dbfe4: stur            w0, [x2, #0xf]
    // 0x15dbfe8: r1 = <Widget>
    //     0x15dbfe8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15dbfec: r0 = AllocateGrowableArray()
    //     0x15dbfec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15dbff0: mov             x1, x0
    // 0x15dbff4: ldur            x0, [fp, #-0x18]
    // 0x15dbff8: stur            x1, [fp, #-8]
    // 0x15dbffc: StoreField: r1->field_f = r0
    //     0x15dbffc: stur            w0, [x1, #0xf]
    // 0x15dc000: r0 = 2
    //     0x15dc000: movz            x0, #0x2
    // 0x15dc004: StoreField: r1->field_b = r0
    //     0x15dc004: stur            w0, [x1, #0xb]
    // 0x15dc008: r0 = AppBar()
    //     0x15dc008: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15dc00c: stur            x0, [fp, #-0x18]
    // 0x15dc010: ldur            x16, [fp, #-0x10]
    // 0x15dc014: ldur            lr, [fp, #-8]
    // 0x15dc018: stp             lr, x16, [SP]
    // 0x15dc01c: mov             x1, x0
    // 0x15dc020: ldur            x2, [fp, #-0x20]
    // 0x15dc024: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15dc024: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15dc028: ldr             x4, [x4, #0xa58]
    // 0x15dc02c: r0 = AppBar()
    //     0x15dc02c: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15dc030: ldur            x0, [fp, #-0x18]
    // 0x15dc034: LeaveFrame
    //     0x15dc034: mov             SP, fp
    //     0x15dc038: ldp             fp, lr, [SP], #0x10
    // 0x15dc03c: ret
    //     0x15dc03c: ret             
    // 0x15dc040: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dc040: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dc044: b               #0x15dbdb4
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15dc048, size: 0x2fc
    // 0x15dc048: EnterFrame
    //     0x15dc048: stp             fp, lr, [SP, #-0x10]!
    //     0x15dc04c: mov             fp, SP
    // 0x15dc050: AllocStack(0x58)
    //     0x15dc050: sub             SP, SP, #0x58
    // 0x15dc054: SetupParameters()
    //     0x15dc054: ldr             x0, [fp, #0x10]
    //     0x15dc058: ldur            w2, [x0, #0x17]
    //     0x15dc05c: add             x2, x2, HEAP, lsl #32
    //     0x15dc060: stur            x2, [fp, #-8]
    // 0x15dc064: CheckStackOverflow
    //     0x15dc064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dc068: cmp             SP, x16
    //     0x15dc06c: b.ls            #0x15dc33c
    // 0x15dc070: LoadField: r1 = r2->field_f
    //     0x15dc070: ldur            w1, [x2, #0xf]
    // 0x15dc074: DecompressPointer r1
    //     0x15dc074: add             x1, x1, HEAP, lsl #32
    // 0x15dc078: r0 = controller()
    //     0x15dc078: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dc07c: LoadField: r1 = r0->field_6b
    //     0x15dc07c: ldur            w1, [x0, #0x6b]
    // 0x15dc080: DecompressPointer r1
    //     0x15dc080: add             x1, x1, HEAP, lsl #32
    // 0x15dc084: r0 = value()
    //     0x15dc084: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dc088: LoadField: r1 = r0->field_1f
    //     0x15dc088: ldur            w1, [x0, #0x1f]
    // 0x15dc08c: DecompressPointer r1
    //     0x15dc08c: add             x1, x1, HEAP, lsl #32
    // 0x15dc090: cmp             w1, NULL
    // 0x15dc094: b.ne            #0x15dc0a0
    // 0x15dc098: r0 = Null
    //     0x15dc098: mov             x0, NULL
    // 0x15dc09c: b               #0x15dc0a8
    // 0x15dc0a0: LoadField: r0 = r1->field_7
    //     0x15dc0a0: ldur            w0, [x1, #7]
    // 0x15dc0a4: DecompressPointer r0
    //     0x15dc0a4: add             x0, x0, HEAP, lsl #32
    // 0x15dc0a8: cmp             w0, NULL
    // 0x15dc0ac: b.ne            #0x15dc0b8
    // 0x15dc0b0: r0 = false
    //     0x15dc0b0: add             x0, NULL, #0x30  ; false
    // 0x15dc0b4: b               #0x15dc2a4
    // 0x15dc0b8: tbnz            w0, #4, #0x15dc2a0
    // 0x15dc0bc: ldur            x2, [fp, #-8]
    // 0x15dc0c0: LoadField: r1 = r2->field_f
    //     0x15dc0c0: ldur            w1, [x2, #0xf]
    // 0x15dc0c4: DecompressPointer r1
    //     0x15dc0c4: add             x1, x1, HEAP, lsl #32
    // 0x15dc0c8: r0 = controller()
    //     0x15dc0c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dc0cc: LoadField: r1 = r0->field_83
    //     0x15dc0cc: ldur            w1, [x0, #0x83]
    // 0x15dc0d0: DecompressPointer r1
    //     0x15dc0d0: add             x1, x1, HEAP, lsl #32
    // 0x15dc0d4: r0 = value()
    //     0x15dc0d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dc0d8: ldur            x2, [fp, #-8]
    // 0x15dc0dc: stur            x0, [fp, #-0x10]
    // 0x15dc0e0: LoadField: r1 = r2->field_13
    //     0x15dc0e0: ldur            w1, [x2, #0x13]
    // 0x15dc0e4: DecompressPointer r1
    //     0x15dc0e4: add             x1, x1, HEAP, lsl #32
    // 0x15dc0e8: r0 = of()
    //     0x15dc0e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dc0ec: LoadField: r2 = r0->field_5b
    //     0x15dc0ec: ldur            w2, [x0, #0x5b]
    // 0x15dc0f0: DecompressPointer r2
    //     0x15dc0f0: add             x2, x2, HEAP, lsl #32
    // 0x15dc0f4: ldur            x0, [fp, #-8]
    // 0x15dc0f8: stur            x2, [fp, #-0x18]
    // 0x15dc0fc: LoadField: r1 = r0->field_f
    //     0x15dc0fc: ldur            w1, [x0, #0xf]
    // 0x15dc100: DecompressPointer r1
    //     0x15dc100: add             x1, x1, HEAP, lsl #32
    // 0x15dc104: r0 = controller()
    //     0x15dc104: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dc108: LoadField: r1 = r0->field_87
    //     0x15dc108: ldur            w1, [x0, #0x87]
    // 0x15dc10c: DecompressPointer r1
    //     0x15dc10c: add             x1, x1, HEAP, lsl #32
    // 0x15dc110: r0 = value()
    //     0x15dc110: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dc114: cmp             w0, NULL
    // 0x15dc118: r16 = true
    //     0x15dc118: add             x16, NULL, #0x20  ; true
    // 0x15dc11c: r17 = false
    //     0x15dc11c: add             x17, NULL, #0x30  ; false
    // 0x15dc120: csel            x2, x16, x17, ne
    // 0x15dc124: ldur            x0, [fp, #-8]
    // 0x15dc128: stur            x2, [fp, #-0x20]
    // 0x15dc12c: LoadField: r1 = r0->field_f
    //     0x15dc12c: ldur            w1, [x0, #0xf]
    // 0x15dc130: DecompressPointer r1
    //     0x15dc130: add             x1, x1, HEAP, lsl #32
    // 0x15dc134: r0 = controller()
    //     0x15dc134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15dc138: LoadField: r1 = r0->field_87
    //     0x15dc138: ldur            w1, [x0, #0x87]
    // 0x15dc13c: DecompressPointer r1
    //     0x15dc13c: add             x1, x1, HEAP, lsl #32
    // 0x15dc140: r0 = value()
    //     0x15dc140: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15dc144: str             x0, [SP]
    // 0x15dc148: r0 = _interpolateSingle()
    //     0x15dc148: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15dc14c: ldur            x2, [fp, #-8]
    // 0x15dc150: stur            x0, [fp, #-0x28]
    // 0x15dc154: LoadField: r1 = r2->field_13
    //     0x15dc154: ldur            w1, [x2, #0x13]
    // 0x15dc158: DecompressPointer r1
    //     0x15dc158: add             x1, x1, HEAP, lsl #32
    // 0x15dc15c: r0 = of()
    //     0x15dc15c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dc160: LoadField: r1 = r0->field_87
    //     0x15dc160: ldur            w1, [x0, #0x87]
    // 0x15dc164: DecompressPointer r1
    //     0x15dc164: add             x1, x1, HEAP, lsl #32
    // 0x15dc168: LoadField: r0 = r1->field_27
    //     0x15dc168: ldur            w0, [x1, #0x27]
    // 0x15dc16c: DecompressPointer r0
    //     0x15dc16c: add             x0, x0, HEAP, lsl #32
    // 0x15dc170: r16 = Instance_Color
    //     0x15dc170: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15dc174: str             x16, [SP]
    // 0x15dc178: mov             x1, x0
    // 0x15dc17c: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15dc17c: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15dc180: ldr             x4, [x4, #0xf40]
    // 0x15dc184: r0 = copyWith()
    //     0x15dc184: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15dc188: stur            x0, [fp, #-0x30]
    // 0x15dc18c: r0 = Text()
    //     0x15dc18c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15dc190: mov             x2, x0
    // 0x15dc194: ldur            x0, [fp, #-0x28]
    // 0x15dc198: stur            x2, [fp, #-0x38]
    // 0x15dc19c: StoreField: r2->field_b = r0
    //     0x15dc19c: stur            w0, [x2, #0xb]
    // 0x15dc1a0: ldur            x0, [fp, #-0x30]
    // 0x15dc1a4: StoreField: r2->field_13 = r0
    //     0x15dc1a4: stur            w0, [x2, #0x13]
    // 0x15dc1a8: ldur            x0, [fp, #-8]
    // 0x15dc1ac: LoadField: r1 = r0->field_13
    //     0x15dc1ac: ldur            w1, [x0, #0x13]
    // 0x15dc1b0: DecompressPointer r1
    //     0x15dc1b0: add             x1, x1, HEAP, lsl #32
    // 0x15dc1b4: r0 = of()
    //     0x15dc1b4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15dc1b8: LoadField: r1 = r0->field_5b
    //     0x15dc1b8: ldur            w1, [x0, #0x5b]
    // 0x15dc1bc: DecompressPointer r1
    //     0x15dc1bc: add             x1, x1, HEAP, lsl #32
    // 0x15dc1c0: stur            x1, [fp, #-0x28]
    // 0x15dc1c4: r0 = ColorFilter()
    //     0x15dc1c4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15dc1c8: mov             x1, x0
    // 0x15dc1cc: ldur            x0, [fp, #-0x28]
    // 0x15dc1d0: stur            x1, [fp, #-0x30]
    // 0x15dc1d4: StoreField: r1->field_7 = r0
    //     0x15dc1d4: stur            w0, [x1, #7]
    // 0x15dc1d8: r0 = Instance_BlendMode
    //     0x15dc1d8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15dc1dc: ldr             x0, [x0, #0xb30]
    // 0x15dc1e0: StoreField: r1->field_b = r0
    //     0x15dc1e0: stur            w0, [x1, #0xb]
    // 0x15dc1e4: r0 = 1
    //     0x15dc1e4: movz            x0, #0x1
    // 0x15dc1e8: StoreField: r1->field_13 = r0
    //     0x15dc1e8: stur            x0, [x1, #0x13]
    // 0x15dc1ec: r0 = SvgPicture()
    //     0x15dc1ec: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15dc1f0: stur            x0, [fp, #-0x28]
    // 0x15dc1f4: r16 = Instance_BoxFit
    //     0x15dc1f4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15dc1f8: ldr             x16, [x16, #0xb18]
    // 0x15dc1fc: r30 = 24.000000
    //     0x15dc1fc: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15dc200: ldr             lr, [lr, #0xba8]
    // 0x15dc204: stp             lr, x16, [SP, #0x10]
    // 0x15dc208: r16 = 24.000000
    //     0x15dc208: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15dc20c: ldr             x16, [x16, #0xba8]
    // 0x15dc210: ldur            lr, [fp, #-0x30]
    // 0x15dc214: stp             lr, x16, [SP]
    // 0x15dc218: mov             x1, x0
    // 0x15dc21c: r2 = "assets/images/shopping_bag.svg"
    //     0x15dc21c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15dc220: ldr             x2, [x2, #0xa60]
    // 0x15dc224: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15dc224: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15dc228: ldr             x4, [x4, #0xa68]
    // 0x15dc22c: r0 = SvgPicture.asset()
    //     0x15dc22c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15dc230: r0 = Badge()
    //     0x15dc230: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15dc234: mov             x1, x0
    // 0x15dc238: ldur            x0, [fp, #-0x18]
    // 0x15dc23c: stur            x1, [fp, #-0x30]
    // 0x15dc240: StoreField: r1->field_b = r0
    //     0x15dc240: stur            w0, [x1, #0xb]
    // 0x15dc244: ldur            x0, [fp, #-0x38]
    // 0x15dc248: StoreField: r1->field_27 = r0
    //     0x15dc248: stur            w0, [x1, #0x27]
    // 0x15dc24c: ldur            x0, [fp, #-0x20]
    // 0x15dc250: StoreField: r1->field_2b = r0
    //     0x15dc250: stur            w0, [x1, #0x2b]
    // 0x15dc254: ldur            x0, [fp, #-0x28]
    // 0x15dc258: StoreField: r1->field_2f = r0
    //     0x15dc258: stur            w0, [x1, #0x2f]
    // 0x15dc25c: r0 = Visibility()
    //     0x15dc25c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15dc260: mov             x1, x0
    // 0x15dc264: ldur            x0, [fp, #-0x30]
    // 0x15dc268: StoreField: r1->field_b = r0
    //     0x15dc268: stur            w0, [x1, #0xb]
    // 0x15dc26c: r0 = Instance_SizedBox
    //     0x15dc26c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15dc270: StoreField: r1->field_f = r0
    //     0x15dc270: stur            w0, [x1, #0xf]
    // 0x15dc274: ldur            x0, [fp, #-0x10]
    // 0x15dc278: StoreField: r1->field_13 = r0
    //     0x15dc278: stur            w0, [x1, #0x13]
    // 0x15dc27c: r0 = false
    //     0x15dc27c: add             x0, NULL, #0x30  ; false
    // 0x15dc280: ArrayStore: r1[0] = r0  ; List_4
    //     0x15dc280: stur            w0, [x1, #0x17]
    // 0x15dc284: StoreField: r1->field_1b = r0
    //     0x15dc284: stur            w0, [x1, #0x1b]
    // 0x15dc288: StoreField: r1->field_1f = r0
    //     0x15dc288: stur            w0, [x1, #0x1f]
    // 0x15dc28c: StoreField: r1->field_23 = r0
    //     0x15dc28c: stur            w0, [x1, #0x23]
    // 0x15dc290: StoreField: r1->field_27 = r0
    //     0x15dc290: stur            w0, [x1, #0x27]
    // 0x15dc294: StoreField: r1->field_2b = r0
    //     0x15dc294: stur            w0, [x1, #0x2b]
    // 0x15dc298: mov             x0, x1
    // 0x15dc29c: b               #0x15dc2bc
    // 0x15dc2a0: r0 = false
    //     0x15dc2a0: add             x0, NULL, #0x30  ; false
    // 0x15dc2a4: r0 = Container()
    //     0x15dc2a4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15dc2a8: mov             x1, x0
    // 0x15dc2ac: stur            x0, [fp, #-0x10]
    // 0x15dc2b0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15dc2b0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15dc2b4: r0 = Container()
    //     0x15dc2b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15dc2b8: ldur            x0, [fp, #-0x10]
    // 0x15dc2bc: stur            x0, [fp, #-0x10]
    // 0x15dc2c0: r0 = InkWell()
    //     0x15dc2c0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15dc2c4: mov             x3, x0
    // 0x15dc2c8: ldur            x0, [fp, #-0x10]
    // 0x15dc2cc: stur            x3, [fp, #-0x18]
    // 0x15dc2d0: StoreField: r3->field_b = r0
    //     0x15dc2d0: stur            w0, [x3, #0xb]
    // 0x15dc2d4: ldur            x2, [fp, #-8]
    // 0x15dc2d8: r1 = Function '<anonymous closure>':.
    //     0x15dc2d8: add             x1, PP, #0x43, lsl #12  ; [pp+0x431f8] AnonymousClosure: (0x15dc344), in [package:customer_app/app/presentation/views/cosmetic/orders/orders_view.dart] OrdersView::appBar (0x15dbd94)
    //     0x15dc2dc: ldr             x1, [x1, #0x1f8]
    // 0x15dc2e0: r0 = AllocateClosure()
    //     0x15dc2e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dc2e4: mov             x1, x0
    // 0x15dc2e8: ldur            x0, [fp, #-0x18]
    // 0x15dc2ec: StoreField: r0->field_f = r1
    //     0x15dc2ec: stur            w1, [x0, #0xf]
    // 0x15dc2f0: r1 = true
    //     0x15dc2f0: add             x1, NULL, #0x20  ; true
    // 0x15dc2f4: StoreField: r0->field_43 = r1
    //     0x15dc2f4: stur            w1, [x0, #0x43]
    // 0x15dc2f8: r2 = Instance_BoxShape
    //     0x15dc2f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15dc2fc: ldr             x2, [x2, #0x80]
    // 0x15dc300: StoreField: r0->field_47 = r2
    //     0x15dc300: stur            w2, [x0, #0x47]
    // 0x15dc304: StoreField: r0->field_6f = r1
    //     0x15dc304: stur            w1, [x0, #0x6f]
    // 0x15dc308: r2 = false
    //     0x15dc308: add             x2, NULL, #0x30  ; false
    // 0x15dc30c: StoreField: r0->field_73 = r2
    //     0x15dc30c: stur            w2, [x0, #0x73]
    // 0x15dc310: StoreField: r0->field_83 = r1
    //     0x15dc310: stur            w1, [x0, #0x83]
    // 0x15dc314: StoreField: r0->field_7b = r2
    //     0x15dc314: stur            w2, [x0, #0x7b]
    // 0x15dc318: r0 = Padding()
    //     0x15dc318: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15dc31c: r1 = Instance_EdgeInsets
    //     0x15dc31c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15dc320: ldr             x1, [x1, #0xa78]
    // 0x15dc324: StoreField: r0->field_f = r1
    //     0x15dc324: stur            w1, [x0, #0xf]
    // 0x15dc328: ldur            x1, [fp, #-0x18]
    // 0x15dc32c: StoreField: r0->field_b = r1
    //     0x15dc32c: stur            w1, [x0, #0xb]
    // 0x15dc330: LeaveFrame
    //     0x15dc330: mov             SP, fp
    //     0x15dc334: ldp             fp, lr, [SP], #0x10
    // 0x15dc338: ret
    //     0x15dc338: ret             
    // 0x15dc33c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dc33c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dc340: b               #0x15dc070
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15dc344, size: 0xdc
    // 0x15dc344: EnterFrame
    //     0x15dc344: stp             fp, lr, [SP, #-0x10]!
    //     0x15dc348: mov             fp, SP
    // 0x15dc34c: AllocStack(0x28)
    //     0x15dc34c: sub             SP, SP, #0x28
    // 0x15dc350: SetupParameters()
    //     0x15dc350: ldr             x0, [fp, #0x10]
    //     0x15dc354: ldur            w2, [x0, #0x17]
    //     0x15dc358: add             x2, x2, HEAP, lsl #32
    //     0x15dc35c: stur            x2, [fp, #-8]
    // 0x15dc360: CheckStackOverflow
    //     0x15dc360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15dc364: cmp             SP, x16
    //     0x15dc368: b.ls            #0x15dc418
    // 0x15dc36c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15dc36c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15dc370: ldr             x0, [x0, #0x1c80]
    //     0x15dc374: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15dc378: cmp             w0, w16
    //     0x15dc37c: b.ne            #0x15dc388
    //     0x15dc380: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15dc384: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15dc388: r1 = Null
    //     0x15dc388: mov             x1, NULL
    // 0x15dc38c: r2 = 4
    //     0x15dc38c: movz            x2, #0x4
    // 0x15dc390: r0 = AllocateArray()
    //     0x15dc390: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15dc394: r16 = "previousScreenSource"
    //     0x15dc394: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x15dc398: ldr             x16, [x16, #0x448]
    // 0x15dc39c: StoreField: r0->field_f = r16
    //     0x15dc39c: stur            w16, [x0, #0xf]
    // 0x15dc3a0: r16 = "order_page"
    //     0x15dc3a0: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d710] "order_page"
    //     0x15dc3a4: ldr             x16, [x16, #0x710]
    // 0x15dc3a8: StoreField: r0->field_13 = r16
    //     0x15dc3a8: stur            w16, [x0, #0x13]
    // 0x15dc3ac: r16 = <String, String>
    //     0x15dc3ac: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x15dc3b0: ldr             x16, [x16, #0x788]
    // 0x15dc3b4: stp             x0, x16, [SP]
    // 0x15dc3b8: r0 = Map._fromLiteral()
    //     0x15dc3b8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x15dc3bc: r16 = "/bag"
    //     0x15dc3bc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x15dc3c0: ldr             x16, [x16, #0x468]
    // 0x15dc3c4: stp             x16, NULL, [SP, #8]
    // 0x15dc3c8: str             x0, [SP]
    // 0x15dc3cc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x15dc3cc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x15dc3d0: ldr             x4, [x4, #0x438]
    // 0x15dc3d4: r0 = GetNavigation.toNamed()
    //     0x15dc3d4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15dc3d8: stur            x0, [fp, #-0x10]
    // 0x15dc3dc: cmp             w0, NULL
    // 0x15dc3e0: b.eq            #0x15dc408
    // 0x15dc3e4: ldur            x2, [fp, #-8]
    // 0x15dc3e8: r1 = Function '<anonymous closure>':.
    //     0x15dc3e8: add             x1, PP, #0x43, lsl #12  ; [pp+0x43200] AnonymousClosure: (0x15d2d68), in [package:customer_app/app/presentation/views/line/orders/orders_view.dart] OrdersView::appBar (0x15eaca0)
    //     0x15dc3ec: ldr             x1, [x1, #0x200]
    // 0x15dc3f0: r0 = AllocateClosure()
    //     0x15dc3f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15dc3f4: ldur            x16, [fp, #-0x10]
    // 0x15dc3f8: stp             x16, NULL, [SP, #8]
    // 0x15dc3fc: str             x0, [SP]
    // 0x15dc400: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x15dc400: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x15dc404: r0 = then()
    //     0x15dc404: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x15dc408: r0 = Null
    //     0x15dc408: mov             x0, NULL
    // 0x15dc40c: LeaveFrame
    //     0x15dc40c: mov             SP, fp
    //     0x15dc410: ldp             fp, lr, [SP], #0x10
    // 0x15dc414: ret
    //     0x15dc414: ret             
    // 0x15dc418: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15dc418: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15dc41c: b               #0x15dc36c
  }
}
