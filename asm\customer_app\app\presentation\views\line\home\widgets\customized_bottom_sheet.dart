// lib: , url: package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart

// class id: 1049524, size: 0x8
class :: {
}

// class id: 3247, size: 0x14, field offset: 0x14
class _CustomizedBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbe1674, size: 0xdb0
    // 0xbe1674: EnterFrame
    //     0xbe1674: stp             fp, lr, [SP, #-0x10]!
    //     0xbe1678: mov             fp, SP
    // 0xbe167c: AllocStack(0x70)
    //     0xbe167c: sub             SP, SP, #0x70
    // 0xbe1680: SetupParameters(_CustomizedBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbe1680: stur            x1, [fp, #-8]
    //     0xbe1684: stur            x2, [fp, #-0x10]
    // 0xbe1688: CheckStackOverflow
    //     0xbe1688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe168c: cmp             SP, x16
    //     0xbe1690: b.ls            #0xbe2408
    // 0xbe1694: r1 = 2
    //     0xbe1694: movz            x1, #0x2
    // 0xbe1698: r0 = AllocateContext()
    //     0xbe1698: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe169c: mov             x1, x0
    // 0xbe16a0: ldur            x0, [fp, #-8]
    // 0xbe16a4: stur            x1, [fp, #-0x18]
    // 0xbe16a8: StoreField: r1->field_f = r0
    //     0xbe16a8: stur            w0, [x1, #0xf]
    // 0xbe16ac: ldur            x2, [fp, #-0x10]
    // 0xbe16b0: StoreField: r1->field_13 = r2
    //     0xbe16b0: stur            w2, [x1, #0x13]
    // 0xbe16b4: LoadField: r2 = r0->field_b
    //     0xbe16b4: ldur            w2, [x0, #0xb]
    // 0xbe16b8: DecompressPointer r2
    //     0xbe16b8: add             x2, x2, HEAP, lsl #32
    // 0xbe16bc: cmp             w2, NULL
    // 0xbe16c0: b.eq            #0xbe2410
    // 0xbe16c4: LoadField: r3 = r2->field_b
    //     0xbe16c4: ldur            w3, [x2, #0xb]
    // 0xbe16c8: DecompressPointer r3
    //     0xbe16c8: add             x3, x3, HEAP, lsl #32
    // 0xbe16cc: LoadField: r2 = r3->field_b
    //     0xbe16cc: ldur            w2, [x3, #0xb]
    // 0xbe16d0: DecompressPointer r2
    //     0xbe16d0: add             x2, x2, HEAP, lsl #32
    // 0xbe16d4: cmp             w2, NULL
    // 0xbe16d8: b.ne            #0xbe16e4
    // 0xbe16dc: r2 = Null
    //     0xbe16dc: mov             x2, NULL
    // 0xbe16e0: b               #0xbe16f0
    // 0xbe16e4: LoadField: r3 = r2->field_23
    //     0xbe16e4: ldur            w3, [x2, #0x23]
    // 0xbe16e8: DecompressPointer r3
    //     0xbe16e8: add             x3, x3, HEAP, lsl #32
    // 0xbe16ec: mov             x2, x3
    // 0xbe16f0: cmp             w2, NULL
    // 0xbe16f4: b.ne            #0xbe16fc
    // 0xbe16f8: r2 = ""
    //     0xbe16f8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe16fc: stur            x2, [fp, #-0x10]
    // 0xbe1700: r0 = InitLateStaticField(0xd58) // [package:customer_app/app/core/values/app_theme_data.dart] ::appThemeData
    //     0xbe1700: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe1704: ldr             x0, [x0, #0x1ab0]
    //     0xbe1708: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe170c: cmp             w0, w16
    //     0xbe1710: b.ne            #0xbe1720
    //     0xbe1714: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e060] Field <::.appThemeData>: static late final (offset: 0xd58)
    //     0xbe1718: ldr             x2, [x2, #0x60]
    //     0xbe171c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe1720: LoadField: r2 = r0->field_87
    //     0xbe1720: ldur            w2, [x0, #0x87]
    // 0xbe1724: DecompressPointer r2
    //     0xbe1724: add             x2, x2, HEAP, lsl #32
    // 0xbe1728: stur            x2, [fp, #-0x28]
    // 0xbe172c: LoadField: r0 = r2->field_27
    //     0xbe172c: ldur            w0, [x2, #0x27]
    // 0xbe1730: DecompressPointer r0
    //     0xbe1730: add             x0, x0, HEAP, lsl #32
    // 0xbe1734: stur            x0, [fp, #-0x20]
    // 0xbe1738: r16 = 16.000000
    //     0xbe1738: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe173c: ldr             x16, [x16, #0x188]
    // 0xbe1740: r30 = Instance_Color
    //     0xbe1740: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbe1744: stp             lr, x16, [SP]
    // 0xbe1748: mov             x1, x0
    // 0xbe174c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe174c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe1750: ldr             x4, [x4, #0xaa0]
    // 0xbe1754: r0 = copyWith()
    //     0xbe1754: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe1758: stur            x0, [fp, #-0x30]
    // 0xbe175c: r0 = Text()
    //     0xbe175c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe1760: mov             x2, x0
    // 0xbe1764: ldur            x0, [fp, #-0x10]
    // 0xbe1768: stur            x2, [fp, #-0x38]
    // 0xbe176c: StoreField: r2->field_b = r0
    //     0xbe176c: stur            w0, [x2, #0xb]
    // 0xbe1770: ldur            x0, [fp, #-0x30]
    // 0xbe1774: StoreField: r2->field_13 = r0
    //     0xbe1774: stur            w0, [x2, #0x13]
    // 0xbe1778: r0 = 4
    //     0xbe1778: movz            x0, #0x4
    // 0xbe177c: StoreField: r2->field_37 = r0
    //     0xbe177c: stur            w0, [x2, #0x37]
    // 0xbe1780: r1 = <FlexParentData>
    //     0xbe1780: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbe1784: ldr             x1, [x1, #0xe00]
    // 0xbe1788: r0 = Expanded()
    //     0xbe1788: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbe178c: mov             x1, x0
    // 0xbe1790: r0 = 1
    //     0xbe1790: movz            x0, #0x1
    // 0xbe1794: stur            x1, [fp, #-0x10]
    // 0xbe1798: StoreField: r1->field_13 = r0
    //     0xbe1798: stur            x0, [x1, #0x13]
    // 0xbe179c: r2 = Instance_FlexFit
    //     0xbe179c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbe17a0: ldr             x2, [x2, #0xe08]
    // 0xbe17a4: StoreField: r1->field_1b = r2
    //     0xbe17a4: stur            w2, [x1, #0x1b]
    // 0xbe17a8: ldur            x3, [fp, #-0x38]
    // 0xbe17ac: StoreField: r1->field_b = r3
    //     0xbe17ac: stur            w3, [x1, #0xb]
    // 0xbe17b0: r0 = InkWell()
    //     0xbe17b0: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbe17b4: mov             x3, x0
    // 0xbe17b8: r0 = Instance_Icon
    //     0xbe17b8: add             x0, PP, #0x37, lsl #12  ; [pp+0x372b8] Obj!Icon@d65d31
    //     0xbe17bc: ldr             x0, [x0, #0x2b8]
    // 0xbe17c0: stur            x3, [fp, #-0x30]
    // 0xbe17c4: StoreField: r3->field_b = r0
    //     0xbe17c4: stur            w0, [x3, #0xb]
    // 0xbe17c8: r1 = Function '<anonymous closure>':.
    //     0xbe17c8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c188] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbe17cc: ldr             x1, [x1, #0x188]
    // 0xbe17d0: r2 = Null
    //     0xbe17d0: mov             x2, NULL
    // 0xbe17d4: r0 = AllocateClosure()
    //     0xbe17d4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe17d8: mov             x1, x0
    // 0xbe17dc: ldur            x0, [fp, #-0x30]
    // 0xbe17e0: StoreField: r0->field_f = r1
    //     0xbe17e0: stur            w1, [x0, #0xf]
    // 0xbe17e4: r3 = true
    //     0xbe17e4: add             x3, NULL, #0x20  ; true
    // 0xbe17e8: StoreField: r0->field_43 = r3
    //     0xbe17e8: stur            w3, [x0, #0x43]
    // 0xbe17ec: r1 = Instance_BoxShape
    //     0xbe17ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbe17f0: ldr             x1, [x1, #0x80]
    // 0xbe17f4: StoreField: r0->field_47 = r1
    //     0xbe17f4: stur            w1, [x0, #0x47]
    // 0xbe17f8: StoreField: r0->field_6f = r3
    //     0xbe17f8: stur            w3, [x0, #0x6f]
    // 0xbe17fc: r4 = false
    //     0xbe17fc: add             x4, NULL, #0x30  ; false
    // 0xbe1800: StoreField: r0->field_73 = r4
    //     0xbe1800: stur            w4, [x0, #0x73]
    // 0xbe1804: StoreField: r0->field_83 = r3
    //     0xbe1804: stur            w3, [x0, #0x83]
    // 0xbe1808: StoreField: r0->field_7b = r4
    //     0xbe1808: stur            w4, [x0, #0x7b]
    // 0xbe180c: r1 = Null
    //     0xbe180c: mov             x1, NULL
    // 0xbe1810: r2 = 4
    //     0xbe1810: movz            x2, #0x4
    // 0xbe1814: r0 = AllocateArray()
    //     0xbe1814: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe1818: mov             x2, x0
    // 0xbe181c: ldur            x0, [fp, #-0x10]
    // 0xbe1820: stur            x2, [fp, #-0x38]
    // 0xbe1824: StoreField: r2->field_f = r0
    //     0xbe1824: stur            w0, [x2, #0xf]
    // 0xbe1828: ldur            x0, [fp, #-0x30]
    // 0xbe182c: StoreField: r2->field_13 = r0
    //     0xbe182c: stur            w0, [x2, #0x13]
    // 0xbe1830: r1 = <Widget>
    //     0xbe1830: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe1834: r0 = AllocateGrowableArray()
    //     0xbe1834: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe1838: mov             x1, x0
    // 0xbe183c: ldur            x0, [fp, #-0x38]
    // 0xbe1840: stur            x1, [fp, #-0x10]
    // 0xbe1844: StoreField: r1->field_f = r0
    //     0xbe1844: stur            w0, [x1, #0xf]
    // 0xbe1848: r0 = 4
    //     0xbe1848: movz            x0, #0x4
    // 0xbe184c: StoreField: r1->field_b = r0
    //     0xbe184c: stur            w0, [x1, #0xb]
    // 0xbe1850: r0 = Row()
    //     0xbe1850: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe1854: mov             x1, x0
    // 0xbe1858: r0 = Instance_Axis
    //     0xbe1858: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe185c: stur            x1, [fp, #-0x30]
    // 0xbe1860: StoreField: r1->field_f = r0
    //     0xbe1860: stur            w0, [x1, #0xf]
    // 0xbe1864: r2 = Instance_MainAxisAlignment
    //     0xbe1864: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbe1868: ldr             x2, [x2, #0xa8]
    // 0xbe186c: StoreField: r1->field_13 = r2
    //     0xbe186c: stur            w2, [x1, #0x13]
    // 0xbe1870: r2 = Instance_MainAxisSize
    //     0xbe1870: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe1874: ldr             x2, [x2, #0xa10]
    // 0xbe1878: ArrayStore: r1[0] = r2  ; List_4
    //     0xbe1878: stur            w2, [x1, #0x17]
    // 0xbe187c: r3 = Instance_CrossAxisAlignment
    //     0xbe187c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe1880: ldr             x3, [x3, #0xa18]
    // 0xbe1884: StoreField: r1->field_1b = r3
    //     0xbe1884: stur            w3, [x1, #0x1b]
    // 0xbe1888: r4 = Instance_VerticalDirection
    //     0xbe1888: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe188c: ldr             x4, [x4, #0xa20]
    // 0xbe1890: StoreField: r1->field_23 = r4
    //     0xbe1890: stur            w4, [x1, #0x23]
    // 0xbe1894: r5 = Instance_Clip
    //     0xbe1894: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe1898: ldr             x5, [x5, #0x38]
    // 0xbe189c: StoreField: r1->field_2b = r5
    //     0xbe189c: stur            w5, [x1, #0x2b]
    // 0xbe18a0: StoreField: r1->field_2f = rZR
    //     0xbe18a0: stur            xzr, [x1, #0x2f]
    // 0xbe18a4: ldur            x6, [fp, #-0x10]
    // 0xbe18a8: StoreField: r1->field_b = r6
    //     0xbe18a8: stur            w6, [x1, #0xb]
    // 0xbe18ac: r16 = <EdgeInsets>
    //     0xbe18ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbe18b0: ldr             x16, [x16, #0xda0]
    // 0xbe18b4: r30 = Instance_EdgeInsets
    //     0xbe18b4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbe18b8: ldr             lr, [lr, #0x1f0]
    // 0xbe18bc: stp             lr, x16, [SP]
    // 0xbe18c0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe18c0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe18c4: r0 = all()
    //     0xbe18c4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe18c8: stur            x0, [fp, #-0x10]
    // 0xbe18cc: r16 = <Color>
    //     0xbe18cc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe18d0: ldr             x16, [x16, #0xf80]
    // 0xbe18d4: r30 = Instance_Color
    //     0xbe18d4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe18d8: stp             lr, x16, [SP]
    // 0xbe18dc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe18dc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe18e0: r0 = all()
    //     0xbe18e0: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe18e4: mov             x1, x0
    // 0xbe18e8: ldur            x0, [fp, #-8]
    // 0xbe18ec: stur            x1, [fp, #-0x38]
    // 0xbe18f0: LoadField: r2 = r0->field_b
    //     0xbe18f0: ldur            w2, [x0, #0xb]
    // 0xbe18f4: DecompressPointer r2
    //     0xbe18f4: add             x2, x2, HEAP, lsl #32
    // 0xbe18f8: cmp             w2, NULL
    // 0xbe18fc: b.eq            #0xbe2414
    // 0xbe1900: LoadField: r3 = r2->field_33
    //     0xbe1900: ldur            w3, [x2, #0x33]
    // 0xbe1904: DecompressPointer r3
    //     0xbe1904: add             x3, x3, HEAP, lsl #32
    // 0xbe1908: LoadField: r2 = r3->field_3f
    //     0xbe1908: ldur            w2, [x3, #0x3f]
    // 0xbe190c: DecompressPointer r2
    //     0xbe190c: add             x2, x2, HEAP, lsl #32
    // 0xbe1910: cmp             w2, NULL
    // 0xbe1914: b.ne            #0xbe1920
    // 0xbe1918: r3 = Null
    //     0xbe1918: mov             x3, NULL
    // 0xbe191c: b               #0xbe1944
    // 0xbe1920: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbe1920: ldur            w3, [x2, #0x17]
    // 0xbe1924: DecompressPointer r3
    //     0xbe1924: add             x3, x3, HEAP, lsl #32
    // 0xbe1928: cmp             w3, NULL
    // 0xbe192c: b.ne            #0xbe1938
    // 0xbe1930: r3 = Null
    //     0xbe1930: mov             x3, NULL
    // 0xbe1934: b               #0xbe1944
    // 0xbe1938: LoadField: r4 = r3->field_7
    //     0xbe1938: ldur            w4, [x3, #7]
    // 0xbe193c: DecompressPointer r4
    //     0xbe193c: add             x4, x4, HEAP, lsl #32
    // 0xbe1940: mov             x3, x4
    // 0xbe1944: cmp             w3, NULL
    // 0xbe1948: b.ne            #0xbe1954
    // 0xbe194c: r3 = 0
    //     0xbe194c: movz            x3, #0
    // 0xbe1950: b               #0xbe1964
    // 0xbe1954: r4 = LoadInt32Instr(r3)
    //     0xbe1954: sbfx            x4, x3, #1, #0x1f
    //     0xbe1958: tbz             w3, #0, #0xbe1960
    //     0xbe195c: ldur            x4, [x3, #7]
    // 0xbe1960: mov             x3, x4
    // 0xbe1964: stur            x3, [fp, #-0x50]
    // 0xbe1968: cmp             w2, NULL
    // 0xbe196c: b.ne            #0xbe1978
    // 0xbe1970: r4 = Null
    //     0xbe1970: mov             x4, NULL
    // 0xbe1974: b               #0xbe199c
    // 0xbe1978: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbe1978: ldur            w4, [x2, #0x17]
    // 0xbe197c: DecompressPointer r4
    //     0xbe197c: add             x4, x4, HEAP, lsl #32
    // 0xbe1980: cmp             w4, NULL
    // 0xbe1984: b.ne            #0xbe1990
    // 0xbe1988: r4 = Null
    //     0xbe1988: mov             x4, NULL
    // 0xbe198c: b               #0xbe199c
    // 0xbe1990: LoadField: r5 = r4->field_b
    //     0xbe1990: ldur            w5, [x4, #0xb]
    // 0xbe1994: DecompressPointer r5
    //     0xbe1994: add             x5, x5, HEAP, lsl #32
    // 0xbe1998: mov             x4, x5
    // 0xbe199c: cmp             w4, NULL
    // 0xbe19a0: b.ne            #0xbe19ac
    // 0xbe19a4: r4 = 0
    //     0xbe19a4: movz            x4, #0
    // 0xbe19a8: b               #0xbe19bc
    // 0xbe19ac: r5 = LoadInt32Instr(r4)
    //     0xbe19ac: sbfx            x5, x4, #1, #0x1f
    //     0xbe19b0: tbz             w4, #0, #0xbe19b8
    //     0xbe19b4: ldur            x5, [x4, #7]
    // 0xbe19b8: mov             x4, x5
    // 0xbe19bc: stur            x4, [fp, #-0x48]
    // 0xbe19c0: cmp             w2, NULL
    // 0xbe19c4: b.ne            #0xbe19d0
    // 0xbe19c8: r2 = Null
    //     0xbe19c8: mov             x2, NULL
    // 0xbe19cc: b               #0xbe19f0
    // 0xbe19d0: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xbe19d0: ldur            w5, [x2, #0x17]
    // 0xbe19d4: DecompressPointer r5
    //     0xbe19d4: add             x5, x5, HEAP, lsl #32
    // 0xbe19d8: cmp             w5, NULL
    // 0xbe19dc: b.ne            #0xbe19e8
    // 0xbe19e0: r2 = Null
    //     0xbe19e0: mov             x2, NULL
    // 0xbe19e4: b               #0xbe19f0
    // 0xbe19e8: LoadField: r2 = r5->field_f
    //     0xbe19e8: ldur            w2, [x5, #0xf]
    // 0xbe19ec: DecompressPointer r2
    //     0xbe19ec: add             x2, x2, HEAP, lsl #32
    // 0xbe19f0: cmp             w2, NULL
    // 0xbe19f4: b.ne            #0xbe1a00
    // 0xbe19f8: r6 = 0
    //     0xbe19f8: movz            x6, #0
    // 0xbe19fc: b               #0xbe1a10
    // 0xbe1a00: r5 = LoadInt32Instr(r2)
    //     0xbe1a00: sbfx            x5, x2, #1, #0x1f
    //     0xbe1a04: tbz             w2, #0, #0xbe1a0c
    //     0xbe1a08: ldur            x5, [x2, #7]
    // 0xbe1a0c: mov             x6, x5
    // 0xbe1a10: ldur            x5, [fp, #-0x28]
    // 0xbe1a14: ldur            x2, [fp, #-0x10]
    // 0xbe1a18: stur            x6, [fp, #-0x40]
    // 0xbe1a1c: r0 = Color()
    //     0xbe1a1c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe1a20: mov             x1, x0
    // 0xbe1a24: r0 = Instance_ColorSpace
    //     0xbe1a24: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe1a28: stur            x1, [fp, #-0x58]
    // 0xbe1a2c: StoreField: r1->field_27 = r0
    //     0xbe1a2c: stur            w0, [x1, #0x27]
    // 0xbe1a30: d0 = 1.000000
    //     0xbe1a30: fmov            d0, #1.00000000
    // 0xbe1a34: StoreField: r1->field_7 = d0
    //     0xbe1a34: stur            d0, [x1, #7]
    // 0xbe1a38: ldur            x2, [fp, #-0x50]
    // 0xbe1a3c: ubfx            x2, x2, #0, #0x20
    // 0xbe1a40: and             w3, w2, #0xff
    // 0xbe1a44: ubfx            x3, x3, #0, #0x20
    // 0xbe1a48: scvtf           d1, x3
    // 0xbe1a4c: d2 = 255.000000
    //     0xbe1a4c: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe1a50: fdiv            d3, d1, d2
    // 0xbe1a54: StoreField: r1->field_f = d3
    //     0xbe1a54: stur            d3, [x1, #0xf]
    // 0xbe1a58: ldur            x2, [fp, #-0x48]
    // 0xbe1a5c: ubfx            x2, x2, #0, #0x20
    // 0xbe1a60: and             w3, w2, #0xff
    // 0xbe1a64: ubfx            x3, x3, #0, #0x20
    // 0xbe1a68: scvtf           d1, x3
    // 0xbe1a6c: fdiv            d3, d1, d2
    // 0xbe1a70: ArrayStore: r1[0] = d3  ; List_8
    //     0xbe1a70: stur            d3, [x1, #0x17]
    // 0xbe1a74: ldur            x2, [fp, #-0x40]
    // 0xbe1a78: ubfx            x2, x2, #0, #0x20
    // 0xbe1a7c: and             w3, w2, #0xff
    // 0xbe1a80: ubfx            x3, x3, #0, #0x20
    // 0xbe1a84: scvtf           d1, x3
    // 0xbe1a88: fdiv            d3, d1, d2
    // 0xbe1a8c: StoreField: r1->field_1f = d3
    //     0xbe1a8c: stur            d3, [x1, #0x1f]
    // 0xbe1a90: r0 = BorderSide()
    //     0xbe1a90: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbe1a94: mov             x1, x0
    // 0xbe1a98: ldur            x0, [fp, #-0x58]
    // 0xbe1a9c: stur            x1, [fp, #-0x60]
    // 0xbe1aa0: StoreField: r1->field_7 = r0
    //     0xbe1aa0: stur            w0, [x1, #7]
    // 0xbe1aa4: d0 = 1.000000
    //     0xbe1aa4: fmov            d0, #1.00000000
    // 0xbe1aa8: StoreField: r1->field_b = d0
    //     0xbe1aa8: stur            d0, [x1, #0xb]
    // 0xbe1aac: r0 = Instance_BorderStyle
    //     0xbe1aac: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbe1ab0: ldr             x0, [x0, #0xf68]
    // 0xbe1ab4: StoreField: r1->field_13 = r0
    //     0xbe1ab4: stur            w0, [x1, #0x13]
    // 0xbe1ab8: d1 = -1.000000
    //     0xbe1ab8: fmov            d1, #-1.00000000
    // 0xbe1abc: ArrayStore: r1[0] = d1  ; List_8
    //     0xbe1abc: stur            d1, [x1, #0x17]
    // 0xbe1ac0: r0 = RoundedRectangleBorder()
    //     0xbe1ac0: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbe1ac4: mov             x1, x0
    // 0xbe1ac8: r0 = Instance_BorderRadius
    //     0xbe1ac8: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbe1acc: ldr             x0, [x0, #0xf70]
    // 0xbe1ad0: StoreField: r1->field_b = r0
    //     0xbe1ad0: stur            w0, [x1, #0xb]
    // 0xbe1ad4: ldur            x2, [fp, #-0x60]
    // 0xbe1ad8: StoreField: r1->field_7 = r2
    //     0xbe1ad8: stur            w2, [x1, #7]
    // 0xbe1adc: r16 = <RoundedRectangleBorder>
    //     0xbe1adc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbe1ae0: ldr             x16, [x16, #0xf78]
    // 0xbe1ae4: stp             x1, x16, [SP]
    // 0xbe1ae8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe1ae8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe1aec: r0 = all()
    //     0xbe1aec: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe1af0: stur            x0, [fp, #-0x58]
    // 0xbe1af4: r0 = ButtonStyle()
    //     0xbe1af4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbe1af8: mov             x1, x0
    // 0xbe1afc: ldur            x0, [fp, #-0x38]
    // 0xbe1b00: stur            x1, [fp, #-0x60]
    // 0xbe1b04: StoreField: r1->field_b = r0
    //     0xbe1b04: stur            w0, [x1, #0xb]
    // 0xbe1b08: ldur            x0, [fp, #-0x10]
    // 0xbe1b0c: StoreField: r1->field_23 = r0
    //     0xbe1b0c: stur            w0, [x1, #0x23]
    // 0xbe1b10: ldur            x0, [fp, #-0x58]
    // 0xbe1b14: StoreField: r1->field_43 = r0
    //     0xbe1b14: stur            w0, [x1, #0x43]
    // 0xbe1b18: r0 = TextButtonThemeData()
    //     0xbe1b18: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbe1b1c: mov             x1, x0
    // 0xbe1b20: ldur            x0, [fp, #-0x60]
    // 0xbe1b24: stur            x1, [fp, #-0x38]
    // 0xbe1b28: StoreField: r1->field_7 = r0
    //     0xbe1b28: stur            w0, [x1, #7]
    // 0xbe1b2c: ldur            x0, [fp, #-0x28]
    // 0xbe1b30: LoadField: r2 = r0->field_7
    //     0xbe1b30: ldur            w2, [x0, #7]
    // 0xbe1b34: DecompressPointer r2
    //     0xbe1b34: add             x2, x2, HEAP, lsl #32
    // 0xbe1b38: ldur            x0, [fp, #-8]
    // 0xbe1b3c: stur            x2, [fp, #-0x10]
    // 0xbe1b40: LoadField: r3 = r0->field_b
    //     0xbe1b40: ldur            w3, [x0, #0xb]
    // 0xbe1b44: DecompressPointer r3
    //     0xbe1b44: add             x3, x3, HEAP, lsl #32
    // 0xbe1b48: cmp             w3, NULL
    // 0xbe1b4c: b.eq            #0xbe2418
    // 0xbe1b50: LoadField: r4 = r3->field_33
    //     0xbe1b50: ldur            w4, [x3, #0x33]
    // 0xbe1b54: DecompressPointer r4
    //     0xbe1b54: add             x4, x4, HEAP, lsl #32
    // 0xbe1b58: LoadField: r3 = r4->field_3f
    //     0xbe1b58: ldur            w3, [x4, #0x3f]
    // 0xbe1b5c: DecompressPointer r3
    //     0xbe1b5c: add             x3, x3, HEAP, lsl #32
    // 0xbe1b60: cmp             w3, NULL
    // 0xbe1b64: b.ne            #0xbe1b70
    // 0xbe1b68: r4 = Null
    //     0xbe1b68: mov             x4, NULL
    // 0xbe1b6c: b               #0xbe1b94
    // 0xbe1b70: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xbe1b70: ldur            w4, [x3, #0x17]
    // 0xbe1b74: DecompressPointer r4
    //     0xbe1b74: add             x4, x4, HEAP, lsl #32
    // 0xbe1b78: cmp             w4, NULL
    // 0xbe1b7c: b.ne            #0xbe1b88
    // 0xbe1b80: r4 = Null
    //     0xbe1b80: mov             x4, NULL
    // 0xbe1b84: b               #0xbe1b94
    // 0xbe1b88: LoadField: r5 = r4->field_7
    //     0xbe1b88: ldur            w5, [x4, #7]
    // 0xbe1b8c: DecompressPointer r5
    //     0xbe1b8c: add             x5, x5, HEAP, lsl #32
    // 0xbe1b90: mov             x4, x5
    // 0xbe1b94: cmp             w4, NULL
    // 0xbe1b98: b.ne            #0xbe1ba4
    // 0xbe1b9c: r4 = 0
    //     0xbe1b9c: movz            x4, #0
    // 0xbe1ba0: b               #0xbe1bb4
    // 0xbe1ba4: r5 = LoadInt32Instr(r4)
    //     0xbe1ba4: sbfx            x5, x4, #1, #0x1f
    //     0xbe1ba8: tbz             w4, #0, #0xbe1bb0
    //     0xbe1bac: ldur            x5, [x4, #7]
    // 0xbe1bb0: mov             x4, x5
    // 0xbe1bb4: stur            x4, [fp, #-0x50]
    // 0xbe1bb8: cmp             w3, NULL
    // 0xbe1bbc: b.ne            #0xbe1bc8
    // 0xbe1bc0: r5 = Null
    //     0xbe1bc0: mov             x5, NULL
    // 0xbe1bc4: b               #0xbe1bec
    // 0xbe1bc8: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xbe1bc8: ldur            w5, [x3, #0x17]
    // 0xbe1bcc: DecompressPointer r5
    //     0xbe1bcc: add             x5, x5, HEAP, lsl #32
    // 0xbe1bd0: cmp             w5, NULL
    // 0xbe1bd4: b.ne            #0xbe1be0
    // 0xbe1bd8: r5 = Null
    //     0xbe1bd8: mov             x5, NULL
    // 0xbe1bdc: b               #0xbe1bec
    // 0xbe1be0: LoadField: r6 = r5->field_b
    //     0xbe1be0: ldur            w6, [x5, #0xb]
    // 0xbe1be4: DecompressPointer r6
    //     0xbe1be4: add             x6, x6, HEAP, lsl #32
    // 0xbe1be8: mov             x5, x6
    // 0xbe1bec: cmp             w5, NULL
    // 0xbe1bf0: b.ne            #0xbe1bfc
    // 0xbe1bf4: r5 = 0
    //     0xbe1bf4: movz            x5, #0
    // 0xbe1bf8: b               #0xbe1c0c
    // 0xbe1bfc: r6 = LoadInt32Instr(r5)
    //     0xbe1bfc: sbfx            x6, x5, #1, #0x1f
    //     0xbe1c00: tbz             w5, #0, #0xbe1c08
    //     0xbe1c04: ldur            x6, [x5, #7]
    // 0xbe1c08: mov             x5, x6
    // 0xbe1c0c: stur            x5, [fp, #-0x48]
    // 0xbe1c10: cmp             w3, NULL
    // 0xbe1c14: b.ne            #0xbe1c20
    // 0xbe1c18: r3 = Null
    //     0xbe1c18: mov             x3, NULL
    // 0xbe1c1c: b               #0xbe1c40
    // 0xbe1c20: ArrayLoad: r6 = r3[0]  ; List_4
    //     0xbe1c20: ldur            w6, [x3, #0x17]
    // 0xbe1c24: DecompressPointer r6
    //     0xbe1c24: add             x6, x6, HEAP, lsl #32
    // 0xbe1c28: cmp             w6, NULL
    // 0xbe1c2c: b.ne            #0xbe1c38
    // 0xbe1c30: r3 = Null
    //     0xbe1c30: mov             x3, NULL
    // 0xbe1c34: b               #0xbe1c40
    // 0xbe1c38: LoadField: r3 = r6->field_f
    //     0xbe1c38: ldur            w3, [x6, #0xf]
    // 0xbe1c3c: DecompressPointer r3
    //     0xbe1c3c: add             x3, x3, HEAP, lsl #32
    // 0xbe1c40: cmp             w3, NULL
    // 0xbe1c44: b.ne            #0xbe1c50
    // 0xbe1c48: r3 = 0
    //     0xbe1c48: movz            x3, #0
    // 0xbe1c4c: b               #0xbe1c60
    // 0xbe1c50: r6 = LoadInt32Instr(r3)
    //     0xbe1c50: sbfx            x6, x3, #1, #0x1f
    //     0xbe1c54: tbz             w3, #0, #0xbe1c5c
    //     0xbe1c58: ldur            x6, [x3, #7]
    // 0xbe1c5c: mov             x3, x6
    // 0xbe1c60: stur            x3, [fp, #-0x40]
    // 0xbe1c64: r0 = Color()
    //     0xbe1c64: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe1c68: mov             x1, x0
    // 0xbe1c6c: r0 = Instance_ColorSpace
    //     0xbe1c6c: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe1c70: StoreField: r1->field_27 = r0
    //     0xbe1c70: stur            w0, [x1, #0x27]
    // 0xbe1c74: d0 = 1.000000
    //     0xbe1c74: fmov            d0, #1.00000000
    // 0xbe1c78: StoreField: r1->field_7 = d0
    //     0xbe1c78: stur            d0, [x1, #7]
    // 0xbe1c7c: ldur            x2, [fp, #-0x50]
    // 0xbe1c80: ubfx            x2, x2, #0, #0x20
    // 0xbe1c84: and             w3, w2, #0xff
    // 0xbe1c88: ubfx            x3, x3, #0, #0x20
    // 0xbe1c8c: scvtf           d1, x3
    // 0xbe1c90: d2 = 255.000000
    //     0xbe1c90: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe1c94: fdiv            d3, d1, d2
    // 0xbe1c98: StoreField: r1->field_f = d3
    //     0xbe1c98: stur            d3, [x1, #0xf]
    // 0xbe1c9c: ldur            x2, [fp, #-0x48]
    // 0xbe1ca0: ubfx            x2, x2, #0, #0x20
    // 0xbe1ca4: and             w3, w2, #0xff
    // 0xbe1ca8: ubfx            x3, x3, #0, #0x20
    // 0xbe1cac: scvtf           d1, x3
    // 0xbe1cb0: fdiv            d3, d1, d2
    // 0xbe1cb4: ArrayStore: r1[0] = d3  ; List_8
    //     0xbe1cb4: stur            d3, [x1, #0x17]
    // 0xbe1cb8: ldur            x2, [fp, #-0x40]
    // 0xbe1cbc: ubfx            x2, x2, #0, #0x20
    // 0xbe1cc0: and             w3, w2, #0xff
    // 0xbe1cc4: ubfx            x3, x3, #0, #0x20
    // 0xbe1cc8: scvtf           d1, x3
    // 0xbe1ccc: fdiv            d3, d1, d2
    // 0xbe1cd0: StoreField: r1->field_1f = d3
    //     0xbe1cd0: stur            d3, [x1, #0x1f]
    // 0xbe1cd4: r16 = 16.000000
    //     0xbe1cd4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe1cd8: ldr             x16, [x16, #0x188]
    // 0xbe1cdc: stp             x1, x16, [SP]
    // 0xbe1ce0: ldur            x1, [fp, #-0x10]
    // 0xbe1ce4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe1ce4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe1ce8: ldr             x4, [x4, #0xaa0]
    // 0xbe1cec: r0 = copyWith()
    //     0xbe1cec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe1cf0: stur            x0, [fp, #-0x10]
    // 0xbe1cf4: r0 = Text()
    //     0xbe1cf4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe1cf8: mov             x3, x0
    // 0xbe1cfc: r0 = "SKIP"
    //     0xbe1cfc: add             x0, PP, #0x5c, lsl #12  ; [pp+0x5c190] "SKIP"
    //     0xbe1d00: ldr             x0, [x0, #0x190]
    // 0xbe1d04: stur            x3, [fp, #-0x28]
    // 0xbe1d08: StoreField: r3->field_b = r0
    //     0xbe1d08: stur            w0, [x3, #0xb]
    // 0xbe1d0c: ldur            x0, [fp, #-0x10]
    // 0xbe1d10: StoreField: r3->field_13 = r0
    //     0xbe1d10: stur            w0, [x3, #0x13]
    // 0xbe1d14: ldur            x2, [fp, #-0x18]
    // 0xbe1d18: r1 = Function '<anonymous closure>':.
    //     0xbe1d18: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c198] AnonymousClosure: (0xbe2738), in [package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xbe1674)
    //     0xbe1d1c: ldr             x1, [x1, #0x198]
    // 0xbe1d20: r0 = AllocateClosure()
    //     0xbe1d20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe1d24: stur            x0, [fp, #-0x10]
    // 0xbe1d28: r0 = TextButton()
    //     0xbe1d28: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbe1d2c: mov             x1, x0
    // 0xbe1d30: ldur            x0, [fp, #-0x10]
    // 0xbe1d34: stur            x1, [fp, #-0x58]
    // 0xbe1d38: StoreField: r1->field_b = r0
    //     0xbe1d38: stur            w0, [x1, #0xb]
    // 0xbe1d3c: r0 = false
    //     0xbe1d3c: add             x0, NULL, #0x30  ; false
    // 0xbe1d40: StoreField: r1->field_27 = r0
    //     0xbe1d40: stur            w0, [x1, #0x27]
    // 0xbe1d44: r2 = true
    //     0xbe1d44: add             x2, NULL, #0x20  ; true
    // 0xbe1d48: StoreField: r1->field_2f = r2
    //     0xbe1d48: stur            w2, [x1, #0x2f]
    // 0xbe1d4c: ldur            x3, [fp, #-0x28]
    // 0xbe1d50: StoreField: r1->field_37 = r3
    //     0xbe1d50: stur            w3, [x1, #0x37]
    // 0xbe1d54: r0 = TextButtonTheme()
    //     0xbe1d54: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbe1d58: mov             x2, x0
    // 0xbe1d5c: ldur            x0, [fp, #-0x38]
    // 0xbe1d60: stur            x2, [fp, #-0x10]
    // 0xbe1d64: StoreField: r2->field_f = r0
    //     0xbe1d64: stur            w0, [x2, #0xf]
    // 0xbe1d68: ldur            x0, [fp, #-0x58]
    // 0xbe1d6c: StoreField: r2->field_b = r0
    //     0xbe1d6c: stur            w0, [x2, #0xb]
    // 0xbe1d70: r1 = <FlexParentData>
    //     0xbe1d70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbe1d74: ldr             x1, [x1, #0xe00]
    // 0xbe1d78: r0 = Expanded()
    //     0xbe1d78: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbe1d7c: mov             x1, x0
    // 0xbe1d80: r0 = 1
    //     0xbe1d80: movz            x0, #0x1
    // 0xbe1d84: stur            x1, [fp, #-0x28]
    // 0xbe1d88: StoreField: r1->field_13 = r0
    //     0xbe1d88: stur            x0, [x1, #0x13]
    // 0xbe1d8c: r2 = Instance_FlexFit
    //     0xbe1d8c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbe1d90: ldr             x2, [x2, #0xe08]
    // 0xbe1d94: StoreField: r1->field_1b = r2
    //     0xbe1d94: stur            w2, [x1, #0x1b]
    // 0xbe1d98: ldur            x3, [fp, #-0x10]
    // 0xbe1d9c: StoreField: r1->field_b = r3
    //     0xbe1d9c: stur            w3, [x1, #0xb]
    // 0xbe1da0: r16 = <EdgeInsets>
    //     0xbe1da0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbe1da4: ldr             x16, [x16, #0xda0]
    // 0xbe1da8: r30 = Instance_EdgeInsets
    //     0xbe1da8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbe1dac: ldr             lr, [lr, #0x1f0]
    // 0xbe1db0: stp             lr, x16, [SP]
    // 0xbe1db4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe1db4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe1db8: r0 = all()
    //     0xbe1db8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe1dbc: mov             x1, x0
    // 0xbe1dc0: ldur            x0, [fp, #-8]
    // 0xbe1dc4: stur            x1, [fp, #-0x10]
    // 0xbe1dc8: LoadField: r2 = r0->field_b
    //     0xbe1dc8: ldur            w2, [x0, #0xb]
    // 0xbe1dcc: DecompressPointer r2
    //     0xbe1dcc: add             x2, x2, HEAP, lsl #32
    // 0xbe1dd0: cmp             w2, NULL
    // 0xbe1dd4: b.eq            #0xbe241c
    // 0xbe1dd8: LoadField: r3 = r2->field_33
    //     0xbe1dd8: ldur            w3, [x2, #0x33]
    // 0xbe1ddc: DecompressPointer r3
    //     0xbe1ddc: add             x3, x3, HEAP, lsl #32
    // 0xbe1de0: LoadField: r2 = r3->field_3f
    //     0xbe1de0: ldur            w2, [x3, #0x3f]
    // 0xbe1de4: DecompressPointer r2
    //     0xbe1de4: add             x2, x2, HEAP, lsl #32
    // 0xbe1de8: cmp             w2, NULL
    // 0xbe1dec: b.ne            #0xbe1df8
    // 0xbe1df0: r3 = Null
    //     0xbe1df0: mov             x3, NULL
    // 0xbe1df4: b               #0xbe1e1c
    // 0xbe1df8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbe1df8: ldur            w3, [x2, #0x17]
    // 0xbe1dfc: DecompressPointer r3
    //     0xbe1dfc: add             x3, x3, HEAP, lsl #32
    // 0xbe1e00: cmp             w3, NULL
    // 0xbe1e04: b.ne            #0xbe1e10
    // 0xbe1e08: r3 = Null
    //     0xbe1e08: mov             x3, NULL
    // 0xbe1e0c: b               #0xbe1e1c
    // 0xbe1e10: LoadField: r4 = r3->field_7
    //     0xbe1e10: ldur            w4, [x3, #7]
    // 0xbe1e14: DecompressPointer r4
    //     0xbe1e14: add             x4, x4, HEAP, lsl #32
    // 0xbe1e18: mov             x3, x4
    // 0xbe1e1c: cmp             w3, NULL
    // 0xbe1e20: b.ne            #0xbe1e2c
    // 0xbe1e24: r3 = 0
    //     0xbe1e24: movz            x3, #0
    // 0xbe1e28: b               #0xbe1e3c
    // 0xbe1e2c: r4 = LoadInt32Instr(r3)
    //     0xbe1e2c: sbfx            x4, x3, #1, #0x1f
    //     0xbe1e30: tbz             w3, #0, #0xbe1e38
    //     0xbe1e34: ldur            x4, [x3, #7]
    // 0xbe1e38: mov             x3, x4
    // 0xbe1e3c: stur            x3, [fp, #-0x50]
    // 0xbe1e40: cmp             w2, NULL
    // 0xbe1e44: b.ne            #0xbe1e50
    // 0xbe1e48: r4 = Null
    //     0xbe1e48: mov             x4, NULL
    // 0xbe1e4c: b               #0xbe1e74
    // 0xbe1e50: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbe1e50: ldur            w4, [x2, #0x17]
    // 0xbe1e54: DecompressPointer r4
    //     0xbe1e54: add             x4, x4, HEAP, lsl #32
    // 0xbe1e58: cmp             w4, NULL
    // 0xbe1e5c: b.ne            #0xbe1e68
    // 0xbe1e60: r4 = Null
    //     0xbe1e60: mov             x4, NULL
    // 0xbe1e64: b               #0xbe1e74
    // 0xbe1e68: LoadField: r5 = r4->field_b
    //     0xbe1e68: ldur            w5, [x4, #0xb]
    // 0xbe1e6c: DecompressPointer r5
    //     0xbe1e6c: add             x5, x5, HEAP, lsl #32
    // 0xbe1e70: mov             x4, x5
    // 0xbe1e74: cmp             w4, NULL
    // 0xbe1e78: b.ne            #0xbe1e84
    // 0xbe1e7c: r4 = 0
    //     0xbe1e7c: movz            x4, #0
    // 0xbe1e80: b               #0xbe1e94
    // 0xbe1e84: r5 = LoadInt32Instr(r4)
    //     0xbe1e84: sbfx            x5, x4, #1, #0x1f
    //     0xbe1e88: tbz             w4, #0, #0xbe1e90
    //     0xbe1e8c: ldur            x5, [x4, #7]
    // 0xbe1e90: mov             x4, x5
    // 0xbe1e94: stur            x4, [fp, #-0x48]
    // 0xbe1e98: cmp             w2, NULL
    // 0xbe1e9c: b.ne            #0xbe1ea8
    // 0xbe1ea0: r2 = Null
    //     0xbe1ea0: mov             x2, NULL
    // 0xbe1ea4: b               #0xbe1ec8
    // 0xbe1ea8: ArrayLoad: r5 = r2[0]  ; List_4
    //     0xbe1ea8: ldur            w5, [x2, #0x17]
    // 0xbe1eac: DecompressPointer r5
    //     0xbe1eac: add             x5, x5, HEAP, lsl #32
    // 0xbe1eb0: cmp             w5, NULL
    // 0xbe1eb4: b.ne            #0xbe1ec0
    // 0xbe1eb8: r2 = Null
    //     0xbe1eb8: mov             x2, NULL
    // 0xbe1ebc: b               #0xbe1ec8
    // 0xbe1ec0: LoadField: r2 = r5->field_f
    //     0xbe1ec0: ldur            w2, [x5, #0xf]
    // 0xbe1ec4: DecompressPointer r2
    //     0xbe1ec4: add             x2, x2, HEAP, lsl #32
    // 0xbe1ec8: cmp             w2, NULL
    // 0xbe1ecc: b.ne            #0xbe1ed8
    // 0xbe1ed0: r2 = 0
    //     0xbe1ed0: movz            x2, #0
    // 0xbe1ed4: b               #0xbe1ee8
    // 0xbe1ed8: r5 = LoadInt32Instr(r2)
    //     0xbe1ed8: sbfx            x5, x2, #1, #0x1f
    //     0xbe1edc: tbz             w2, #0, #0xbe1ee4
    //     0xbe1ee0: ldur            x5, [x2, #7]
    // 0xbe1ee4: mov             x2, x5
    // 0xbe1ee8: stur            x2, [fp, #-0x40]
    // 0xbe1eec: r0 = Color()
    //     0xbe1eec: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe1ef0: mov             x1, x0
    // 0xbe1ef4: r0 = Instance_ColorSpace
    //     0xbe1ef4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe1ef8: StoreField: r1->field_27 = r0
    //     0xbe1ef8: stur            w0, [x1, #0x27]
    // 0xbe1efc: d0 = 1.000000
    //     0xbe1efc: fmov            d0, #1.00000000
    // 0xbe1f00: StoreField: r1->field_7 = d0
    //     0xbe1f00: stur            d0, [x1, #7]
    // 0xbe1f04: ldur            x2, [fp, #-0x50]
    // 0xbe1f08: ubfx            x2, x2, #0, #0x20
    // 0xbe1f0c: and             w3, w2, #0xff
    // 0xbe1f10: ubfx            x3, x3, #0, #0x20
    // 0xbe1f14: scvtf           d1, x3
    // 0xbe1f18: d2 = 255.000000
    //     0xbe1f18: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe1f1c: fdiv            d3, d1, d2
    // 0xbe1f20: StoreField: r1->field_f = d3
    //     0xbe1f20: stur            d3, [x1, #0xf]
    // 0xbe1f24: ldur            x2, [fp, #-0x48]
    // 0xbe1f28: ubfx            x2, x2, #0, #0x20
    // 0xbe1f2c: and             w3, w2, #0xff
    // 0xbe1f30: ubfx            x3, x3, #0, #0x20
    // 0xbe1f34: scvtf           d1, x3
    // 0xbe1f38: fdiv            d3, d1, d2
    // 0xbe1f3c: ArrayStore: r1[0] = d3  ; List_8
    //     0xbe1f3c: stur            d3, [x1, #0x17]
    // 0xbe1f40: ldur            x2, [fp, #-0x40]
    // 0xbe1f44: ubfx            x2, x2, #0, #0x20
    // 0xbe1f48: and             w3, w2, #0xff
    // 0xbe1f4c: ubfx            x3, x3, #0, #0x20
    // 0xbe1f50: scvtf           d1, x3
    // 0xbe1f54: fdiv            d3, d1, d2
    // 0xbe1f58: StoreField: r1->field_1f = d3
    //     0xbe1f58: stur            d3, [x1, #0x1f]
    // 0xbe1f5c: r16 = <Color>
    //     0xbe1f5c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbe1f60: ldr             x16, [x16, #0xf80]
    // 0xbe1f64: stp             x1, x16, [SP]
    // 0xbe1f68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe1f68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe1f6c: r0 = all()
    //     0xbe1f6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe1f70: mov             x1, x0
    // 0xbe1f74: ldur            x0, [fp, #-8]
    // 0xbe1f78: stur            x1, [fp, #-0x38]
    // 0xbe1f7c: LoadField: r2 = r0->field_b
    //     0xbe1f7c: ldur            w2, [x0, #0xb]
    // 0xbe1f80: DecompressPointer r2
    //     0xbe1f80: add             x2, x2, HEAP, lsl #32
    // 0xbe1f84: cmp             w2, NULL
    // 0xbe1f88: b.eq            #0xbe2420
    // 0xbe1f8c: LoadField: r0 = r2->field_33
    //     0xbe1f8c: ldur            w0, [x2, #0x33]
    // 0xbe1f90: DecompressPointer r0
    //     0xbe1f90: add             x0, x0, HEAP, lsl #32
    // 0xbe1f94: LoadField: r2 = r0->field_3f
    //     0xbe1f94: ldur            w2, [x0, #0x3f]
    // 0xbe1f98: DecompressPointer r2
    //     0xbe1f98: add             x2, x2, HEAP, lsl #32
    // 0xbe1f9c: cmp             w2, NULL
    // 0xbe1fa0: b.ne            #0xbe1fac
    // 0xbe1fa4: r0 = Null
    //     0xbe1fa4: mov             x0, NULL
    // 0xbe1fa8: b               #0xbe1fd0
    // 0xbe1fac: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xbe1fac: ldur            w0, [x2, #0x17]
    // 0xbe1fb0: DecompressPointer r0
    //     0xbe1fb0: add             x0, x0, HEAP, lsl #32
    // 0xbe1fb4: cmp             w0, NULL
    // 0xbe1fb8: b.ne            #0xbe1fc4
    // 0xbe1fbc: r0 = Null
    //     0xbe1fbc: mov             x0, NULL
    // 0xbe1fc0: b               #0xbe1fd0
    // 0xbe1fc4: LoadField: r3 = r0->field_7
    //     0xbe1fc4: ldur            w3, [x0, #7]
    // 0xbe1fc8: DecompressPointer r3
    //     0xbe1fc8: add             x3, x3, HEAP, lsl #32
    // 0xbe1fcc: mov             x0, x3
    // 0xbe1fd0: cmp             w0, NULL
    // 0xbe1fd4: b.ne            #0xbe1fe0
    // 0xbe1fd8: r0 = 0
    //     0xbe1fd8: movz            x0, #0
    // 0xbe1fdc: b               #0xbe1ff0
    // 0xbe1fe0: r3 = LoadInt32Instr(r0)
    //     0xbe1fe0: sbfx            x3, x0, #1, #0x1f
    //     0xbe1fe4: tbz             w0, #0, #0xbe1fec
    //     0xbe1fe8: ldur            x3, [x0, #7]
    // 0xbe1fec: mov             x0, x3
    // 0xbe1ff0: stur            x0, [fp, #-0x50]
    // 0xbe1ff4: cmp             w2, NULL
    // 0xbe1ff8: b.ne            #0xbe2004
    // 0xbe1ffc: r3 = Null
    //     0xbe1ffc: mov             x3, NULL
    // 0xbe2000: b               #0xbe2028
    // 0xbe2004: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xbe2004: ldur            w3, [x2, #0x17]
    // 0xbe2008: DecompressPointer r3
    //     0xbe2008: add             x3, x3, HEAP, lsl #32
    // 0xbe200c: cmp             w3, NULL
    // 0xbe2010: b.ne            #0xbe201c
    // 0xbe2014: r3 = Null
    //     0xbe2014: mov             x3, NULL
    // 0xbe2018: b               #0xbe2028
    // 0xbe201c: LoadField: r4 = r3->field_b
    //     0xbe201c: ldur            w4, [x3, #0xb]
    // 0xbe2020: DecompressPointer r4
    //     0xbe2020: add             x4, x4, HEAP, lsl #32
    // 0xbe2024: mov             x3, x4
    // 0xbe2028: cmp             w3, NULL
    // 0xbe202c: b.ne            #0xbe2038
    // 0xbe2030: r3 = 0
    //     0xbe2030: movz            x3, #0
    // 0xbe2034: b               #0xbe2048
    // 0xbe2038: r4 = LoadInt32Instr(r3)
    //     0xbe2038: sbfx            x4, x3, #1, #0x1f
    //     0xbe203c: tbz             w3, #0, #0xbe2044
    //     0xbe2040: ldur            x4, [x3, #7]
    // 0xbe2044: mov             x3, x4
    // 0xbe2048: stur            x3, [fp, #-0x48]
    // 0xbe204c: cmp             w2, NULL
    // 0xbe2050: b.ne            #0xbe205c
    // 0xbe2054: r2 = Null
    //     0xbe2054: mov             x2, NULL
    // 0xbe2058: b               #0xbe207c
    // 0xbe205c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xbe205c: ldur            w4, [x2, #0x17]
    // 0xbe2060: DecompressPointer r4
    //     0xbe2060: add             x4, x4, HEAP, lsl #32
    // 0xbe2064: cmp             w4, NULL
    // 0xbe2068: b.ne            #0xbe2074
    // 0xbe206c: r2 = Null
    //     0xbe206c: mov             x2, NULL
    // 0xbe2070: b               #0xbe207c
    // 0xbe2074: LoadField: r2 = r4->field_f
    //     0xbe2074: ldur            w2, [x4, #0xf]
    // 0xbe2078: DecompressPointer r2
    //     0xbe2078: add             x2, x2, HEAP, lsl #32
    // 0xbe207c: cmp             w2, NULL
    // 0xbe2080: b.ne            #0xbe208c
    // 0xbe2084: r6 = 0
    //     0xbe2084: movz            x6, #0
    // 0xbe2088: b               #0xbe209c
    // 0xbe208c: r4 = LoadInt32Instr(r2)
    //     0xbe208c: sbfx            x4, x2, #1, #0x1f
    //     0xbe2090: tbz             w2, #0, #0xbe2098
    //     0xbe2094: ldur            x4, [x2, #7]
    // 0xbe2098: mov             x6, x4
    // 0xbe209c: ldur            x5, [fp, #-0x30]
    // 0xbe20a0: ldur            x4, [fp, #-0x28]
    // 0xbe20a4: ldur            x2, [fp, #-0x10]
    // 0xbe20a8: stur            x6, [fp, #-0x40]
    // 0xbe20ac: r0 = Color()
    //     0xbe20ac: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbe20b0: mov             x1, x0
    // 0xbe20b4: r0 = Instance_ColorSpace
    //     0xbe20b4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbe20b8: stur            x1, [fp, #-8]
    // 0xbe20bc: StoreField: r1->field_27 = r0
    //     0xbe20bc: stur            w0, [x1, #0x27]
    // 0xbe20c0: d0 = 1.000000
    //     0xbe20c0: fmov            d0, #1.00000000
    // 0xbe20c4: StoreField: r1->field_7 = d0
    //     0xbe20c4: stur            d0, [x1, #7]
    // 0xbe20c8: ldur            x0, [fp, #-0x50]
    // 0xbe20cc: ubfx            x0, x0, #0, #0x20
    // 0xbe20d0: and             w2, w0, #0xff
    // 0xbe20d4: ubfx            x2, x2, #0, #0x20
    // 0xbe20d8: scvtf           d1, x2
    // 0xbe20dc: d2 = 255.000000
    //     0xbe20dc: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbe20e0: fdiv            d3, d1, d2
    // 0xbe20e4: StoreField: r1->field_f = d3
    //     0xbe20e4: stur            d3, [x1, #0xf]
    // 0xbe20e8: ldur            x0, [fp, #-0x48]
    // 0xbe20ec: ubfx            x0, x0, #0, #0x20
    // 0xbe20f0: and             w2, w0, #0xff
    // 0xbe20f4: ubfx            x2, x2, #0, #0x20
    // 0xbe20f8: scvtf           d1, x2
    // 0xbe20fc: fdiv            d3, d1, d2
    // 0xbe2100: ArrayStore: r1[0] = d3  ; List_8
    //     0xbe2100: stur            d3, [x1, #0x17]
    // 0xbe2104: ldur            x0, [fp, #-0x40]
    // 0xbe2108: ubfx            x0, x0, #0, #0x20
    // 0xbe210c: and             w2, w0, #0xff
    // 0xbe2110: ubfx            x2, x2, #0, #0x20
    // 0xbe2114: scvtf           d1, x2
    // 0xbe2118: fdiv            d3, d1, d2
    // 0xbe211c: StoreField: r1->field_1f = d3
    //     0xbe211c: stur            d3, [x1, #0x1f]
    // 0xbe2120: r0 = BorderSide()
    //     0xbe2120: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbe2124: mov             x1, x0
    // 0xbe2128: ldur            x0, [fp, #-8]
    // 0xbe212c: stur            x1, [fp, #-0x58]
    // 0xbe2130: StoreField: r1->field_7 = r0
    //     0xbe2130: stur            w0, [x1, #7]
    // 0xbe2134: d0 = 1.000000
    //     0xbe2134: fmov            d0, #1.00000000
    // 0xbe2138: StoreField: r1->field_b = d0
    //     0xbe2138: stur            d0, [x1, #0xb]
    // 0xbe213c: r0 = Instance_BorderStyle
    //     0xbe213c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbe2140: ldr             x0, [x0, #0xf68]
    // 0xbe2144: StoreField: r1->field_13 = r0
    //     0xbe2144: stur            w0, [x1, #0x13]
    // 0xbe2148: d0 = -1.000000
    //     0xbe2148: fmov            d0, #-1.00000000
    // 0xbe214c: ArrayStore: r1[0] = d0  ; List_8
    //     0xbe214c: stur            d0, [x1, #0x17]
    // 0xbe2150: r0 = RoundedRectangleBorder()
    //     0xbe2150: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbe2154: mov             x1, x0
    // 0xbe2158: r0 = Instance_BorderRadius
    //     0xbe2158: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbe215c: ldr             x0, [x0, #0xf70]
    // 0xbe2160: StoreField: r1->field_b = r0
    //     0xbe2160: stur            w0, [x1, #0xb]
    // 0xbe2164: ldur            x0, [fp, #-0x58]
    // 0xbe2168: StoreField: r1->field_7 = r0
    //     0xbe2168: stur            w0, [x1, #7]
    // 0xbe216c: r16 = <RoundedRectangleBorder>
    //     0xbe216c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbe2170: ldr             x16, [x16, #0xf78]
    // 0xbe2174: stp             x1, x16, [SP]
    // 0xbe2178: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbe2178: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbe217c: r0 = all()
    //     0xbe217c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbe2180: stur            x0, [fp, #-8]
    // 0xbe2184: r0 = ButtonStyle()
    //     0xbe2184: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbe2188: mov             x1, x0
    // 0xbe218c: ldur            x0, [fp, #-0x38]
    // 0xbe2190: stur            x1, [fp, #-0x58]
    // 0xbe2194: StoreField: r1->field_b = r0
    //     0xbe2194: stur            w0, [x1, #0xb]
    // 0xbe2198: ldur            x0, [fp, #-0x10]
    // 0xbe219c: StoreField: r1->field_23 = r0
    //     0xbe219c: stur            w0, [x1, #0x23]
    // 0xbe21a0: ldur            x0, [fp, #-8]
    // 0xbe21a4: StoreField: r1->field_43 = r0
    //     0xbe21a4: stur            w0, [x1, #0x43]
    // 0xbe21a8: r0 = TextButtonThemeData()
    //     0xbe21a8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbe21ac: mov             x2, x0
    // 0xbe21b0: ldur            x0, [fp, #-0x58]
    // 0xbe21b4: stur            x2, [fp, #-8]
    // 0xbe21b8: StoreField: r2->field_7 = r0
    //     0xbe21b8: stur            w0, [x2, #7]
    // 0xbe21bc: r16 = 16.000000
    //     0xbe21bc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbe21c0: ldr             x16, [x16, #0x188]
    // 0xbe21c4: r30 = Instance_Color
    //     0xbe21c4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbe21c8: stp             lr, x16, [SP]
    // 0xbe21cc: ldur            x1, [fp, #-0x20]
    // 0xbe21d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbe21d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbe21d4: ldr             x4, [x4, #0xaa0]
    // 0xbe21d8: r0 = copyWith()
    //     0xbe21d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbe21dc: stur            x0, [fp, #-0x10]
    // 0xbe21e0: r0 = Text()
    //     0xbe21e0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbe21e4: mov             x3, x0
    // 0xbe21e8: r0 = "YES,CONTINUE"
    //     0xbe21e8: add             x0, PP, #0x5c, lsl #12  ; [pp+0x5c1a0] "YES,CONTINUE"
    //     0xbe21ec: ldr             x0, [x0, #0x1a0]
    // 0xbe21f0: stur            x3, [fp, #-0x20]
    // 0xbe21f4: StoreField: r3->field_b = r0
    //     0xbe21f4: stur            w0, [x3, #0xb]
    // 0xbe21f8: ldur            x0, [fp, #-0x10]
    // 0xbe21fc: StoreField: r3->field_13 = r0
    //     0xbe21fc: stur            w0, [x3, #0x13]
    // 0xbe2200: ldur            x2, [fp, #-0x18]
    // 0xbe2204: r1 = Function '<anonymous closure>':.
    //     0xbe2204: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c1a8] AnonymousClosure: (0xbe2424), in [package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xbe1674)
    //     0xbe2208: ldr             x1, [x1, #0x1a8]
    // 0xbe220c: r0 = AllocateClosure()
    //     0xbe220c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe2210: stur            x0, [fp, #-0x10]
    // 0xbe2214: r0 = TextButton()
    //     0xbe2214: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbe2218: mov             x1, x0
    // 0xbe221c: ldur            x0, [fp, #-0x10]
    // 0xbe2220: stur            x1, [fp, #-0x18]
    // 0xbe2224: StoreField: r1->field_b = r0
    //     0xbe2224: stur            w0, [x1, #0xb]
    // 0xbe2228: r0 = false
    //     0xbe2228: add             x0, NULL, #0x30  ; false
    // 0xbe222c: StoreField: r1->field_27 = r0
    //     0xbe222c: stur            w0, [x1, #0x27]
    // 0xbe2230: r0 = true
    //     0xbe2230: add             x0, NULL, #0x20  ; true
    // 0xbe2234: StoreField: r1->field_2f = r0
    //     0xbe2234: stur            w0, [x1, #0x2f]
    // 0xbe2238: ldur            x0, [fp, #-0x20]
    // 0xbe223c: StoreField: r1->field_37 = r0
    //     0xbe223c: stur            w0, [x1, #0x37]
    // 0xbe2240: r0 = TextButtonTheme()
    //     0xbe2240: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbe2244: mov             x2, x0
    // 0xbe2248: ldur            x0, [fp, #-8]
    // 0xbe224c: stur            x2, [fp, #-0x10]
    // 0xbe2250: StoreField: r2->field_f = r0
    //     0xbe2250: stur            w0, [x2, #0xf]
    // 0xbe2254: ldur            x0, [fp, #-0x18]
    // 0xbe2258: StoreField: r2->field_b = r0
    //     0xbe2258: stur            w0, [x2, #0xb]
    // 0xbe225c: r1 = <FlexParentData>
    //     0xbe225c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbe2260: ldr             x1, [x1, #0xe00]
    // 0xbe2264: r0 = Expanded()
    //     0xbe2264: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbe2268: mov             x3, x0
    // 0xbe226c: r0 = 1
    //     0xbe226c: movz            x0, #0x1
    // 0xbe2270: stur            x3, [fp, #-8]
    // 0xbe2274: StoreField: r3->field_13 = r0
    //     0xbe2274: stur            x0, [x3, #0x13]
    // 0xbe2278: r0 = Instance_FlexFit
    //     0xbe2278: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbe227c: ldr             x0, [x0, #0xe08]
    // 0xbe2280: StoreField: r3->field_1b = r0
    //     0xbe2280: stur            w0, [x3, #0x1b]
    // 0xbe2284: ldur            x0, [fp, #-0x10]
    // 0xbe2288: StoreField: r3->field_b = r0
    //     0xbe2288: stur            w0, [x3, #0xb]
    // 0xbe228c: r1 = Null
    //     0xbe228c: mov             x1, NULL
    // 0xbe2290: r2 = 6
    //     0xbe2290: movz            x2, #0x6
    // 0xbe2294: r0 = AllocateArray()
    //     0xbe2294: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe2298: mov             x2, x0
    // 0xbe229c: ldur            x0, [fp, #-0x28]
    // 0xbe22a0: stur            x2, [fp, #-0x10]
    // 0xbe22a4: StoreField: r2->field_f = r0
    //     0xbe22a4: stur            w0, [x2, #0xf]
    // 0xbe22a8: r16 = Instance_SizedBox
    //     0xbe22a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f998] Obj!SizedBox@d67e21
    //     0xbe22ac: ldr             x16, [x16, #0x998]
    // 0xbe22b0: StoreField: r2->field_13 = r16
    //     0xbe22b0: stur            w16, [x2, #0x13]
    // 0xbe22b4: ldur            x0, [fp, #-8]
    // 0xbe22b8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe22b8: stur            w0, [x2, #0x17]
    // 0xbe22bc: r1 = <Widget>
    //     0xbe22bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe22c0: r0 = AllocateGrowableArray()
    //     0xbe22c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe22c4: mov             x1, x0
    // 0xbe22c8: ldur            x0, [fp, #-0x10]
    // 0xbe22cc: stur            x1, [fp, #-8]
    // 0xbe22d0: StoreField: r1->field_f = r0
    //     0xbe22d0: stur            w0, [x1, #0xf]
    // 0xbe22d4: r2 = 6
    //     0xbe22d4: movz            x2, #0x6
    // 0xbe22d8: StoreField: r1->field_b = r2
    //     0xbe22d8: stur            w2, [x1, #0xb]
    // 0xbe22dc: r0 = Row()
    //     0xbe22dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbe22e0: mov             x3, x0
    // 0xbe22e4: r0 = Instance_Axis
    //     0xbe22e4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbe22e8: stur            x3, [fp, #-0x10]
    // 0xbe22ec: StoreField: r3->field_f = r0
    //     0xbe22ec: stur            w0, [x3, #0xf]
    // 0xbe22f0: r0 = Instance_MainAxisAlignment
    //     0xbe22f0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbe22f4: ldr             x0, [x0, #0xd10]
    // 0xbe22f8: StoreField: r3->field_13 = r0
    //     0xbe22f8: stur            w0, [x3, #0x13]
    // 0xbe22fc: r0 = Instance_MainAxisSize
    //     0xbe22fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbe2300: ldr             x0, [x0, #0xa10]
    // 0xbe2304: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe2304: stur            w0, [x3, #0x17]
    // 0xbe2308: r0 = Instance_CrossAxisAlignment
    //     0xbe2308: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbe230c: ldr             x0, [x0, #0xa18]
    // 0xbe2310: StoreField: r3->field_1b = r0
    //     0xbe2310: stur            w0, [x3, #0x1b]
    // 0xbe2314: r0 = Instance_VerticalDirection
    //     0xbe2314: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe2318: ldr             x0, [x0, #0xa20]
    // 0xbe231c: StoreField: r3->field_23 = r0
    //     0xbe231c: stur            w0, [x3, #0x23]
    // 0xbe2320: r4 = Instance_Clip
    //     0xbe2320: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe2324: ldr             x4, [x4, #0x38]
    // 0xbe2328: StoreField: r3->field_2b = r4
    //     0xbe2328: stur            w4, [x3, #0x2b]
    // 0xbe232c: StoreField: r3->field_2f = rZR
    //     0xbe232c: stur            xzr, [x3, #0x2f]
    // 0xbe2330: ldur            x1, [fp, #-8]
    // 0xbe2334: StoreField: r3->field_b = r1
    //     0xbe2334: stur            w1, [x3, #0xb]
    // 0xbe2338: r1 = Null
    //     0xbe2338: mov             x1, NULL
    // 0xbe233c: r2 = 6
    //     0xbe233c: movz            x2, #0x6
    // 0xbe2340: r0 = AllocateArray()
    //     0xbe2340: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe2344: mov             x2, x0
    // 0xbe2348: ldur            x0, [fp, #-0x30]
    // 0xbe234c: stur            x2, [fp, #-8]
    // 0xbe2350: StoreField: r2->field_f = r0
    //     0xbe2350: stur            w0, [x2, #0xf]
    // 0xbe2354: r16 = Instance_SizedBox
    //     0xbe2354: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xbe2358: ldr             x16, [x16, #0x9f0]
    // 0xbe235c: StoreField: r2->field_13 = r16
    //     0xbe235c: stur            w16, [x2, #0x13]
    // 0xbe2360: ldur            x0, [fp, #-0x10]
    // 0xbe2364: ArrayStore: r2[0] = r0  ; List_4
    //     0xbe2364: stur            w0, [x2, #0x17]
    // 0xbe2368: r1 = <Widget>
    //     0xbe2368: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbe236c: r0 = AllocateGrowableArray()
    //     0xbe236c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbe2370: mov             x1, x0
    // 0xbe2374: ldur            x0, [fp, #-8]
    // 0xbe2378: stur            x1, [fp, #-0x10]
    // 0xbe237c: StoreField: r1->field_f = r0
    //     0xbe237c: stur            w0, [x1, #0xf]
    // 0xbe2380: r0 = 6
    //     0xbe2380: movz            x0, #0x6
    // 0xbe2384: StoreField: r1->field_b = r0
    //     0xbe2384: stur            w0, [x1, #0xb]
    // 0xbe2388: r0 = Column()
    //     0xbe2388: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbe238c: mov             x1, x0
    // 0xbe2390: r0 = Instance_Axis
    //     0xbe2390: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbe2394: stur            x1, [fp, #-8]
    // 0xbe2398: StoreField: r1->field_f = r0
    //     0xbe2398: stur            w0, [x1, #0xf]
    // 0xbe239c: r0 = Instance_MainAxisAlignment
    //     0xbe239c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbe23a0: ldr             x0, [x0, #0xa08]
    // 0xbe23a4: StoreField: r1->field_13 = r0
    //     0xbe23a4: stur            w0, [x1, #0x13]
    // 0xbe23a8: r0 = Instance_MainAxisSize
    //     0xbe23a8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbe23ac: ldr             x0, [x0, #0xdd0]
    // 0xbe23b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbe23b0: stur            w0, [x1, #0x17]
    // 0xbe23b4: r0 = Instance_CrossAxisAlignment
    //     0xbe23b4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbe23b8: ldr             x0, [x0, #0x890]
    // 0xbe23bc: StoreField: r1->field_1b = r0
    //     0xbe23bc: stur            w0, [x1, #0x1b]
    // 0xbe23c0: r0 = Instance_VerticalDirection
    //     0xbe23c0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbe23c4: ldr             x0, [x0, #0xa20]
    // 0xbe23c8: StoreField: r1->field_23 = r0
    //     0xbe23c8: stur            w0, [x1, #0x23]
    // 0xbe23cc: r0 = Instance_Clip
    //     0xbe23cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbe23d0: ldr             x0, [x0, #0x38]
    // 0xbe23d4: StoreField: r1->field_2b = r0
    //     0xbe23d4: stur            w0, [x1, #0x2b]
    // 0xbe23d8: StoreField: r1->field_2f = rZR
    //     0xbe23d8: stur            xzr, [x1, #0x2f]
    // 0xbe23dc: ldur            x0, [fp, #-0x10]
    // 0xbe23e0: StoreField: r1->field_b = r0
    //     0xbe23e0: stur            w0, [x1, #0xb]
    // 0xbe23e4: r0 = Padding()
    //     0xbe23e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbe23e8: r1 = Instance_EdgeInsets
    //     0xbe23e8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbe23ec: ldr             x1, [x1, #0x1f0]
    // 0xbe23f0: StoreField: r0->field_f = r1
    //     0xbe23f0: stur            w1, [x0, #0xf]
    // 0xbe23f4: ldur            x1, [fp, #-8]
    // 0xbe23f8: StoreField: r0->field_b = r1
    //     0xbe23f8: stur            w1, [x0, #0xb]
    // 0xbe23fc: LeaveFrame
    //     0xbe23fc: mov             SP, fp
    //     0xbe2400: ldp             fp, lr, [SP], #0x10
    // 0xbe2404: ret
    //     0xbe2404: ret             
    // 0xbe2408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe2408: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe240c: b               #0xbe1694
    // 0xbe2410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2410: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2414: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2414: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2418: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe241c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe241c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2420: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbe2424, size: 0x314
    // 0xbe2424: EnterFrame
    //     0xbe2424: stp             fp, lr, [SP, #-0x10]!
    //     0xbe2428: mov             fp, SP
    // 0xbe242c: AllocStack(0x28)
    //     0xbe242c: sub             SP, SP, #0x28
    // 0xbe2430: SetupParameters()
    //     0xbe2430: ldr             x0, [fp, #0x10]
    //     0xbe2434: ldur            w2, [x0, #0x17]
    //     0xbe2438: add             x2, x2, HEAP, lsl #32
    //     0xbe243c: stur            x2, [fp, #-8]
    // 0xbe2440: CheckStackOverflow
    //     0xbe2440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe2444: cmp             SP, x16
    //     0xbe2448: b.ls            #0xbe272c
    // 0xbe244c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe244c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe2450: ldr             x0, [x0, #0x1c80]
    //     0xbe2454: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe2458: cmp             w0, w16
    //     0xbe245c: b.ne            #0xbe2468
    //     0xbe2460: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe2464: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe2468: str             NULL, [SP]
    // 0xbe246c: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe246c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe2470: r0 = GetNavigation.back()
    //     0xbe2470: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbe2474: r1 = Null
    //     0xbe2474: mov             x1, NULL
    // 0xbe2478: r2 = 44
    //     0xbe2478: movz            x2, #0x2c
    // 0xbe247c: r0 = AllocateArray()
    //     0xbe247c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe2480: mov             x2, x0
    // 0xbe2484: r16 = "customizedResponse"
    //     0xbe2484: add             x16, PP, #0x30, lsl #12  ; [pp+0x30038] "customizedResponse"
    //     0xbe2488: ldr             x16, [x16, #0x38]
    // 0xbe248c: StoreField: r2->field_f = r16
    //     0xbe248c: stur            w16, [x2, #0xf]
    // 0xbe2490: ldur            x3, [fp, #-8]
    // 0xbe2494: LoadField: r0 = r3->field_f
    //     0xbe2494: ldur            w0, [x3, #0xf]
    // 0xbe2498: DecompressPointer r0
    //     0xbe2498: add             x0, x0, HEAP, lsl #32
    // 0xbe249c: LoadField: r4 = r0->field_b
    //     0xbe249c: ldur            w4, [x0, #0xb]
    // 0xbe24a0: DecompressPointer r4
    //     0xbe24a0: add             x4, x4, HEAP, lsl #32
    // 0xbe24a4: cmp             w4, NULL
    // 0xbe24a8: b.eq            #0xbe2734
    // 0xbe24ac: LoadField: r0 = r4->field_b
    //     0xbe24ac: ldur            w0, [x4, #0xb]
    // 0xbe24b0: DecompressPointer r0
    //     0xbe24b0: add             x0, x0, HEAP, lsl #32
    // 0xbe24b4: StoreField: r2->field_13 = r0
    //     0xbe24b4: stur            w0, [x2, #0x13]
    // 0xbe24b8: r16 = "productId"
    //     0xbe24b8: ldr             x16, [PP, #0x3970]  ; [pp+0x3970] "productId"
    // 0xbe24bc: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe24bc: stur            w16, [x2, #0x17]
    // 0xbe24c0: LoadField: r0 = r4->field_f
    //     0xbe24c0: ldur            w0, [x4, #0xf]
    // 0xbe24c4: DecompressPointer r0
    //     0xbe24c4: add             x0, x0, HEAP, lsl #32
    // 0xbe24c8: StoreField: r2->field_1b = r0
    //     0xbe24c8: stur            w0, [x2, #0x1b]
    // 0xbe24cc: r16 = "skuId"
    //     0xbe24cc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30040] "skuId"
    //     0xbe24d0: ldr             x16, [x16, #0x40]
    // 0xbe24d4: StoreField: r2->field_1f = r16
    //     0xbe24d4: stur            w16, [x2, #0x1f]
    // 0xbe24d8: LoadField: r0 = r4->field_13
    //     0xbe24d8: ldur            w0, [x4, #0x13]
    // 0xbe24dc: DecompressPointer r0
    //     0xbe24dc: add             x0, x0, HEAP, lsl #32
    // 0xbe24e0: StoreField: r2->field_23 = r0
    //     0xbe24e0: stur            w0, [x2, #0x23]
    // 0xbe24e4: r16 = "customisedId"
    //     0xbe24e4: add             x16, PP, #0x30, lsl #12  ; [pp+0x30048] "customisedId"
    //     0xbe24e8: ldr             x16, [x16, #0x48]
    // 0xbe24ec: StoreField: r2->field_27 = r16
    //     0xbe24ec: stur            w16, [x2, #0x27]
    // 0xbe24f0: r16 = ""
    //     0xbe24f0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe24f4: StoreField: r2->field_2b = r16
    //     0xbe24f4: stur            w16, [x2, #0x2b]
    // 0xbe24f8: r16 = "addTypeValue"
    //     0xbe24f8: add             x16, PP, #0x30, lsl #12  ; [pp+0x30050] "addTypeValue"
    //     0xbe24fc: ldr             x16, [x16, #0x50]
    // 0xbe2500: StoreField: r2->field_2f = r16
    //     0xbe2500: stur            w16, [x2, #0x2f]
    // 0xbe2504: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xbe2504: ldur            x5, [x4, #0x17]
    // 0xbe2508: r0 = BoxInt64Instr(r5)
    //     0xbe2508: sbfiz           x0, x5, #1, #0x1f
    //     0xbe250c: cmp             x5, x0, asr #1
    //     0xbe2510: b.eq            #0xbe251c
    //     0xbe2514: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe2518: stur            x5, [x0, #7]
    // 0xbe251c: mov             x1, x2
    // 0xbe2520: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe2520: add             x25, x1, #0x33
    //     0xbe2524: str             w0, [x25]
    //     0xbe2528: tbz             w0, #0, #0xbe2544
    //     0xbe252c: ldurb           w16, [x1, #-1]
    //     0xbe2530: ldurb           w17, [x0, #-1]
    //     0xbe2534: and             x16, x17, x16, lsr #2
    //     0xbe2538: tst             x16, HEAP, lsr #32
    //     0xbe253c: b.eq            #0xbe2544
    //     0xbe2540: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe2544: r16 = "sellingPrice"
    //     0xbe2544: add             x16, PP, #0x30, lsl #12  ; [pp+0x30058] "sellingPrice"
    //     0xbe2548: ldr             x16, [x16, #0x58]
    // 0xbe254c: StoreField: r2->field_37 = r16
    //     0xbe254c: stur            w16, [x2, #0x37]
    // 0xbe2550: LoadField: r5 = r4->field_27
    //     0xbe2550: ldur            x5, [x4, #0x27]
    // 0xbe2554: r0 = BoxInt64Instr(r5)
    //     0xbe2554: sbfiz           x0, x5, #1, #0x1f
    //     0xbe2558: cmp             x5, x0, asr #1
    //     0xbe255c: b.eq            #0xbe2568
    //     0xbe2560: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe2564: stur            x5, [x0, #7]
    // 0xbe2568: mov             x1, x2
    // 0xbe256c: ArrayStore: r1[11] = r0  ; List_4
    //     0xbe256c: add             x25, x1, #0x3b
    //     0xbe2570: str             w0, [x25]
    //     0xbe2574: tbz             w0, #0, #0xbe2590
    //     0xbe2578: ldurb           w16, [x1, #-1]
    //     0xbe257c: ldurb           w17, [x0, #-1]
    //     0xbe2580: and             x16, x17, x16, lsr #2
    //     0xbe2584: tst             x16, HEAP, lsr #32
    //     0xbe2588: b.eq            #0xbe2590
    //     0xbe258c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe2590: r16 = "comingFrom"
    //     0xbe2590: add             x16, PP, #0x30, lsl #12  ; [pp+0x30060] "comingFrom"
    //     0xbe2594: ldr             x16, [x16, #0x60]
    // 0xbe2598: StoreField: r2->field_3f = r16
    //     0xbe2598: stur            w16, [x2, #0x3f]
    // 0xbe259c: LoadField: r0 = r4->field_2f
    //     0xbe259c: ldur            w0, [x4, #0x2f]
    // 0xbe25a0: DecompressPointer r0
    //     0xbe25a0: add             x0, x0, HEAP, lsl #32
    // 0xbe25a4: mov             x1, x2
    // 0xbe25a8: ArrayStore: r1[13] = r0  ; List_4
    //     0xbe25a8: add             x25, x1, #0x43
    //     0xbe25ac: str             w0, [x25]
    //     0xbe25b0: tbz             w0, #0, #0xbe25cc
    //     0xbe25b4: ldurb           w16, [x1, #-1]
    //     0xbe25b8: ldurb           w17, [x0, #-1]
    //     0xbe25bc: and             x16, x17, x16, lsr #2
    //     0xbe25c0: tst             x16, HEAP, lsr #32
    //     0xbe25c4: b.eq            #0xbe25cc
    //     0xbe25c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe25cc: r16 = "skuData"
    //     0xbe25cc: add             x16, PP, #0x30, lsl #12  ; [pp+0x30068] "skuData"
    //     0xbe25d0: ldr             x16, [x16, #0x68]
    // 0xbe25d4: StoreField: r2->field_47 = r16
    //     0xbe25d4: stur            w16, [x2, #0x47]
    // 0xbe25d8: LoadField: r0 = r4->field_37
    //     0xbe25d8: ldur            w0, [x4, #0x37]
    // 0xbe25dc: DecompressPointer r0
    //     0xbe25dc: add             x0, x0, HEAP, lsl #32
    // 0xbe25e0: mov             x1, x2
    // 0xbe25e4: ArrayStore: r1[15] = r0  ; List_4
    //     0xbe25e4: add             x25, x1, #0x4b
    //     0xbe25e8: str             w0, [x25]
    //     0xbe25ec: tbz             w0, #0, #0xbe2608
    //     0xbe25f0: ldurb           w16, [x1, #-1]
    //     0xbe25f4: ldurb           w17, [x0, #-1]
    //     0xbe25f8: and             x16, x17, x16, lsr #2
    //     0xbe25fc: tst             x16, HEAP, lsr #32
    //     0xbe2600: b.eq            #0xbe2608
    //     0xbe2604: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe2608: r16 = "productTitle"
    //     0xbe2608: add             x16, PP, #0x30, lsl #12  ; [pp+0x30070] "productTitle"
    //     0xbe260c: ldr             x16, [x16, #0x70]
    // 0xbe2610: StoreField: r2->field_4f = r16
    //     0xbe2610: stur            w16, [x2, #0x4f]
    // 0xbe2614: LoadField: r0 = r4->field_3b
    //     0xbe2614: ldur            w0, [x4, #0x3b]
    // 0xbe2618: DecompressPointer r0
    //     0xbe2618: add             x0, x0, HEAP, lsl #32
    // 0xbe261c: mov             x1, x2
    // 0xbe2620: ArrayStore: r1[17] = r0  ; List_4
    //     0xbe2620: add             x25, x1, #0x53
    //     0xbe2624: str             w0, [x25]
    //     0xbe2628: tbz             w0, #0, #0xbe2644
    //     0xbe262c: ldurb           w16, [x1, #-1]
    //     0xbe2630: ldurb           w17, [x0, #-1]
    //     0xbe2634: and             x16, x17, x16, lsr #2
    //     0xbe2638: tst             x16, HEAP, lsr #32
    //     0xbe263c: b.eq            #0xbe2644
    //     0xbe2640: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe2644: r16 = "productImageUrl"
    //     0xbe2644: add             x16, PP, #0x30, lsl #12  ; [pp+0x30078] "productImageUrl"
    //     0xbe2648: ldr             x16, [x16, #0x78]
    // 0xbe264c: StoreField: r2->field_57 = r16
    //     0xbe264c: stur            w16, [x2, #0x57]
    // 0xbe2650: LoadField: r0 = r4->field_3f
    //     0xbe2650: ldur            w0, [x4, #0x3f]
    // 0xbe2654: DecompressPointer r0
    //     0xbe2654: add             x0, x0, HEAP, lsl #32
    // 0xbe2658: mov             x1, x2
    // 0xbe265c: ArrayStore: r1[19] = r0  ; List_4
    //     0xbe265c: add             x25, x1, #0x5b
    //     0xbe2660: str             w0, [x25]
    //     0xbe2664: tbz             w0, #0, #0xbe2680
    //     0xbe2668: ldurb           w16, [x1, #-1]
    //     0xbe266c: ldurb           w17, [x0, #-1]
    //     0xbe2670: and             x16, x17, x16, lsr #2
    //     0xbe2674: tst             x16, HEAP, lsr #32
    //     0xbe2678: b.eq            #0xbe2680
    //     0xbe267c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe2680: r16 = "checkout_variant_response"
    //     0xbe2680: add             x16, PP, #0x30, lsl #12  ; [pp+0x30080] "checkout_variant_response"
    //     0xbe2684: ldr             x16, [x16, #0x80]
    // 0xbe2688: StoreField: r2->field_5f = r16
    //     0xbe2688: stur            w16, [x2, #0x5f]
    // 0xbe268c: LoadField: r0 = r4->field_4b
    //     0xbe268c: ldur            w0, [x4, #0x4b]
    // 0xbe2690: DecompressPointer r0
    //     0xbe2690: add             x0, x0, HEAP, lsl #32
    // 0xbe2694: mov             x1, x2
    // 0xbe2698: ArrayStore: r1[21] = r0  ; List_4
    //     0xbe2698: add             x25, x1, #0x63
    //     0xbe269c: str             w0, [x25]
    //     0xbe26a0: tbz             w0, #0, #0xbe26bc
    //     0xbe26a4: ldurb           w16, [x1, #-1]
    //     0xbe26a8: ldurb           w17, [x0, #-1]
    //     0xbe26ac: and             x16, x17, x16, lsr #2
    //     0xbe26b0: tst             x16, HEAP, lsr #32
    //     0xbe26b4: b.eq            #0xbe26bc
    //     0xbe26b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe26bc: r16 = <String, Object?>
    //     0xbe26bc: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xbe26c0: ldr             x16, [x16, #0xc28]
    // 0xbe26c4: stp             x2, x16, [SP]
    // 0xbe26c8: r0 = Map._fromLiteral()
    //     0xbe26c8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe26cc: r16 = "/customization"
    //     0xbe26cc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8a8] "/customization"
    //     0xbe26d0: ldr             x16, [x16, #0x8a8]
    // 0xbe26d4: stp             x16, NULL, [SP, #8]
    // 0xbe26d8: str             x0, [SP]
    // 0xbe26dc: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe26dc: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe26e0: ldr             x4, [x4, #0x438]
    // 0xbe26e4: r0 = GetNavigation.toNamed()
    //     0xbe26e4: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe26e8: stur            x0, [fp, #-0x10]
    // 0xbe26ec: cmp             w0, NULL
    // 0xbe26f0: b.eq            #0xbe271c
    // 0xbe26f4: ldur            x2, [fp, #-8]
    // 0xbe26f8: r1 = Function '<anonymous closure>':.
    //     0xbe26f8: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c1b0] AnonymousClosure: (0xa47358), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xbe26fc: ldr             x1, [x1, #0x1b0]
    // 0xbe2700: r0 = AllocateClosure()
    //     0xbe2700: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe2704: r16 = <Null?>
    //     0xbe2704: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xbe2708: ldur            lr, [fp, #-0x10]
    // 0xbe270c: stp             lr, x16, [SP, #8]
    // 0xbe2710: str             x0, [SP]
    // 0xbe2714: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbe2714: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbe2718: r0 = then()
    //     0xbe2718: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xbe271c: r0 = Null
    //     0xbe271c: mov             x0, NULL
    // 0xbe2720: LeaveFrame
    //     0xbe2720: mov             SP, fp
    //     0xbe2724: ldp             fp, lr, [SP], #0x10
    // 0xbe2728: ret
    //     0xbe2728: ret             
    // 0xbe272c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe272c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe2730: b               #0xbe244c
    // 0xbe2734: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2734: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbe2738, size: 0x750
    // 0xbe2738: EnterFrame
    //     0xbe2738: stp             fp, lr, [SP, #-0x10]!
    //     0xbe273c: mov             fp, SP
    // 0xbe2740: AllocStack(0x78)
    //     0xbe2740: sub             SP, SP, #0x78
    // 0xbe2744: SetupParameters()
    //     0xbe2744: ldr             x0, [fp, #0x10]
    //     0xbe2748: ldur            w1, [x0, #0x17]
    //     0xbe274c: add             x1, x1, HEAP, lsl #32
    //     0xbe2750: stur            x1, [fp, #-8]
    // 0xbe2754: CheckStackOverflow
    //     0xbe2754: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe2758: cmp             SP, x16
    //     0xbe275c: b.ls            #0xbe2e5c
    // 0xbe2760: r1 = 1
    //     0xbe2760: movz            x1, #0x1
    // 0xbe2764: r0 = AllocateContext()
    //     0xbe2764: bl              #0x16f6108  ; AllocateContextStub
    // 0xbe2768: mov             x1, x0
    // 0xbe276c: ldur            x0, [fp, #-8]
    // 0xbe2770: stur            x1, [fp, #-0x10]
    // 0xbe2774: StoreField: r1->field_b = r0
    //     0xbe2774: stur            w0, [x1, #0xb]
    // 0xbe2778: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe2778: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe277c: ldr             x0, [x0, #0x1c80]
    //     0xbe2780: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe2784: cmp             w0, w16
    //     0xbe2788: b.ne            #0xbe2794
    //     0xbe278c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe2790: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe2794: str             NULL, [SP]
    // 0xbe2798: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe2798: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe279c: r0 = GetNavigation.back()
    //     0xbe279c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbe27a0: ldur            x2, [fp, #-0x10]
    // 0xbe27a4: r0 = Sentinel
    //     0xbe27a4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe27a8: StoreField: r2->field_f = r0
    //     0xbe27a8: stur            w0, [x2, #0xf]
    // 0xbe27ac: ldur            x0, [fp, #-8]
    // 0xbe27b0: LoadField: r1 = r0->field_f
    //     0xbe27b0: ldur            w1, [x0, #0xf]
    // 0xbe27b4: DecompressPointer r1
    //     0xbe27b4: add             x1, x1, HEAP, lsl #32
    // 0xbe27b8: LoadField: r3 = r1->field_b
    //     0xbe27b8: ldur            w3, [x1, #0xb]
    // 0xbe27bc: DecompressPointer r3
    //     0xbe27bc: add             x3, x3, HEAP, lsl #32
    // 0xbe27c0: cmp             w3, NULL
    // 0xbe27c4: b.eq            #0xbe2e64
    // 0xbe27c8: LoadField: r1 = r3->field_23
    //     0xbe27c8: ldur            w1, [x3, #0x23]
    // 0xbe27cc: DecompressPointer r1
    //     0xbe27cc: add             x1, x1, HEAP, lsl #32
    // 0xbe27d0: r16 = "home_page"
    //     0xbe27d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ee60] "home_page"
    //     0xbe27d4: ldr             x16, [x16, #0xe60]
    // 0xbe27d8: stp             x16, x1, [SP]
    // 0xbe27dc: r0 = ==()
    //     0xbe27dc: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbe27e0: tbnz            w0, #4, #0xbe282c
    // 0xbe27e4: ldur            x2, [fp, #-0x10]
    // 0xbe27e8: r16 = <HomeController>
    //     0xbe27e8: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xbe27ec: ldr             x16, [x16, #0xf98]
    // 0xbe27f0: str             x16, [SP]
    // 0xbe27f4: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe27f4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe27f8: r0 = Inst.find()
    //     0xbe27f8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbe27fc: mov             x1, x0
    // 0xbe2800: ldur            x2, [fp, #-0x10]
    // 0xbe2804: StoreField: r2->field_f = r0
    //     0xbe2804: stur            w0, [x2, #0xf]
    //     0xbe2808: ldurb           w16, [x2, #-1]
    //     0xbe280c: ldurb           w17, [x0, #-1]
    //     0xbe2810: and             x16, x17, x16, lsr #2
    //     0xbe2814: tst             x16, HEAP, lsr #32
    //     0xbe2818: b.eq            #0xbe2820
    //     0xbe281c: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbe2820: r3 = "product_card"
    //     0xbe2820: add             x3, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xbe2824: ldr             x3, [x3, #0xc28]
    // 0xbe2828: b               #0xbe298c
    // 0xbe282c: ldur            x0, [fp, #-8]
    // 0xbe2830: ldur            x2, [fp, #-0x10]
    // 0xbe2834: LoadField: r1 = r0->field_f
    //     0xbe2834: ldur            w1, [x0, #0xf]
    // 0xbe2838: DecompressPointer r1
    //     0xbe2838: add             x1, x1, HEAP, lsl #32
    // 0xbe283c: LoadField: r3 = r1->field_b
    //     0xbe283c: ldur            w3, [x1, #0xb]
    // 0xbe2840: DecompressPointer r3
    //     0xbe2840: add             x3, x3, HEAP, lsl #32
    // 0xbe2844: cmp             w3, NULL
    // 0xbe2848: b.eq            #0xbe2e68
    // 0xbe284c: LoadField: r1 = r3->field_23
    //     0xbe284c: ldur            w1, [x3, #0x23]
    // 0xbe2850: DecompressPointer r1
    //     0xbe2850: add             x1, x1, HEAP, lsl #32
    // 0xbe2854: r16 = "collection_page"
    //     0xbe2854: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c118] "collection_page"
    //     0xbe2858: ldr             x16, [x16, #0x118]
    // 0xbe285c: stp             x16, x1, [SP]
    // 0xbe2860: r0 = ==()
    //     0xbe2860: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbe2864: tbnz            w0, #4, #0xbe28b4
    // 0xbe2868: ldur            x2, [fp, #-0x10]
    // 0xbe286c: r16 = <CollectionsController>
    //     0xbe286c: add             x16, PP, #0xd, lsl #12  ; [pp+0xdb00] TypeArguments: <CollectionsController>
    //     0xbe2870: ldr             x16, [x16, #0xb00]
    // 0xbe2874: str             x16, [SP]
    // 0xbe2878: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe2878: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe287c: r0 = Inst.find()
    //     0xbe287c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbe2880: mov             x1, x0
    // 0xbe2884: ldur            x2, [fp, #-0x10]
    // 0xbe2888: StoreField: r2->field_f = r0
    //     0xbe2888: stur            w0, [x2, #0xf]
    //     0xbe288c: ldurb           w16, [x2, #-1]
    //     0xbe2890: ldurb           w17, [x0, #-1]
    //     0xbe2894: and             x16, x17, x16, lsr #2
    //     0xbe2898: tst             x16, HEAP, lsr #32
    //     0xbe289c: b.eq            #0xbe28a4
    //     0xbe28a0: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbe28a4: mov             x0, x1
    // 0xbe28a8: r1 = "product_card"
    //     0xbe28a8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xbe28ac: ldr             x1, [x1, #0xc28]
    // 0xbe28b0: b               #0xbe2984
    // 0xbe28b4: ldur            x0, [fp, #-8]
    // 0xbe28b8: ldur            x2, [fp, #-0x10]
    // 0xbe28bc: LoadField: r1 = r0->field_f
    //     0xbe28bc: ldur            w1, [x0, #0xf]
    // 0xbe28c0: DecompressPointer r1
    //     0xbe28c0: add             x1, x1, HEAP, lsl #32
    // 0xbe28c4: LoadField: r3 = r1->field_b
    //     0xbe28c4: ldur            w3, [x1, #0xb]
    // 0xbe28c8: DecompressPointer r3
    //     0xbe28c8: add             x3, x3, HEAP, lsl #32
    // 0xbe28cc: cmp             w3, NULL
    // 0xbe28d0: b.eq            #0xbe2e6c
    // 0xbe28d4: LoadField: r1 = r3->field_23
    //     0xbe28d4: ldur            w1, [x3, #0x23]
    // 0xbe28d8: DecompressPointer r1
    //     0xbe28d8: add             x1, x1, HEAP, lsl #32
    // 0xbe28dc: r16 = "product_page"
    //     0xbe28dc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe28e0: ldr             x16, [x16, #0x480]
    // 0xbe28e4: stp             x16, x1, [SP]
    // 0xbe28e8: r0 = ==()
    //     0xbe28e8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbe28ec: tbnz            w0, #4, #0xbe293c
    // 0xbe28f0: ldur            x2, [fp, #-0x10]
    // 0xbe28f4: r16 = <ProductDetailController>
    //     0xbe28f4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0xbe28f8: ldr             x16, [x16, #0xde0]
    // 0xbe28fc: str             x16, [SP]
    // 0xbe2900: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe2900: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe2904: r0 = Inst.find()
    //     0xbe2904: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbe2908: mov             x1, x0
    // 0xbe290c: ldur            x2, [fp, #-0x10]
    // 0xbe2910: StoreField: r2->field_f = r0
    //     0xbe2910: stur            w0, [x2, #0xf]
    //     0xbe2914: ldurb           w16, [x2, #-1]
    //     0xbe2918: ldurb           w17, [x0, #-1]
    //     0xbe291c: and             x16, x17, x16, lsr #2
    //     0xbe2920: tst             x16, HEAP, lsr #32
    //     0xbe2924: b.eq            #0xbe292c
    //     0xbe2928: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbe292c: mov             x0, x1
    // 0xbe2930: r1 = "product_page"
    //     0xbe2930: add             x1, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe2934: ldr             x1, [x1, #0x480]
    // 0xbe2938: b               #0xbe2984
    // 0xbe293c: ldur            x2, [fp, #-0x10]
    // 0xbe2940: r16 = <HomeController>
    //     0xbe2940: add             x16, PP, #0xa, lsl #12  ; [pp+0xaf98] TypeArguments: <HomeController>
    //     0xbe2944: ldr             x16, [x16, #0xf98]
    // 0xbe2948: str             x16, [SP]
    // 0xbe294c: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe294c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe2950: r0 = Inst.find()
    //     0xbe2950: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbe2954: mov             x1, x0
    // 0xbe2958: ldur            x2, [fp, #-0x10]
    // 0xbe295c: StoreField: r2->field_f = r0
    //     0xbe295c: stur            w0, [x2, #0xf]
    //     0xbe2960: ldurb           w16, [x2, #-1]
    //     0xbe2964: ldurb           w17, [x0, #-1]
    //     0xbe2968: and             x16, x17, x16, lsr #2
    //     0xbe296c: tst             x16, HEAP, lsr #32
    //     0xbe2970: b.eq            #0xbe2978
    //     0xbe2974: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xbe2978: mov             x0, x1
    // 0xbe297c: r1 = "product_card"
    //     0xbe297c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53c28] "product_card"
    //     0xbe2980: ldr             x1, [x1, #0xc28]
    // 0xbe2984: mov             x3, x1
    // 0xbe2988: mov             x1, x0
    // 0xbe298c: ldur            x0, [fp, #-8]
    // 0xbe2990: stur            x3, [fp, #-0x18]
    // 0xbe2994: str             x1, [SP]
    // 0xbe2998: r4 = 0
    //     0xbe2998: movz            x4, #0
    // 0xbe299c: ldr             x0, [SP]
    // 0xbe29a0: r5 = UnlinkedCall_0x613b5c
    //     0xbe29a0: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c1b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe29a4: ldp             x5, lr, [x16, #0x1b8]
    // 0xbe29a8: blr             lr
    // 0xbe29ac: mov             x3, x0
    // 0xbe29b0: ldur            x2, [fp, #-8]
    // 0xbe29b4: stur            x3, [fp, #-0x28]
    // 0xbe29b8: LoadField: r0 = r2->field_f
    //     0xbe29b8: ldur            w0, [x2, #0xf]
    // 0xbe29bc: DecompressPointer r0
    //     0xbe29bc: add             x0, x0, HEAP, lsl #32
    // 0xbe29c0: LoadField: r1 = r0->field_b
    //     0xbe29c0: ldur            w1, [x0, #0xb]
    // 0xbe29c4: DecompressPointer r1
    //     0xbe29c4: add             x1, x1, HEAP, lsl #32
    // 0xbe29c8: cmp             w1, NULL
    // 0xbe29cc: b.eq            #0xbe2e70
    // 0xbe29d0: LoadField: r4 = r1->field_f
    //     0xbe29d0: ldur            w4, [x1, #0xf]
    // 0xbe29d4: DecompressPointer r4
    //     0xbe29d4: add             x4, x4, HEAP, lsl #32
    // 0xbe29d8: stur            x4, [fp, #-0x20]
    // 0xbe29dc: LoadField: r5 = r1->field_27
    //     0xbe29dc: ldur            x5, [x1, #0x27]
    // 0xbe29e0: r0 = BoxInt64Instr(r5)
    //     0xbe29e0: sbfiz           x0, x5, #1, #0x1f
    //     0xbe29e4: cmp             x5, x0, asr #1
    //     0xbe29e8: b.eq            #0xbe29f4
    //     0xbe29ec: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe29f0: stur            x5, [x0, #7]
    // 0xbe29f4: stp             x0, NULL, [SP]
    // 0xbe29f8: r0 = _Double.fromInteger()
    //     0xbe29f8: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbe29fc: mov             x3, x0
    // 0xbe2a00: ldur            x0, [fp, #-0x20]
    // 0xbe2a04: r2 = Null
    //     0xbe2a04: mov             x2, NULL
    // 0xbe2a08: r1 = Null
    //     0xbe2a08: mov             x1, NULL
    // 0xbe2a0c: stur            x3, [fp, #-0x30]
    // 0xbe2a10: r4 = LoadClassIdInstr(r0)
    //     0xbe2a10: ldur            x4, [x0, #-1]
    //     0xbe2a14: ubfx            x4, x4, #0xc, #0x14
    // 0xbe2a18: sub             x4, x4, #0x5e
    // 0xbe2a1c: cmp             x4, #1
    // 0xbe2a20: b.ls            #0xbe2a34
    // 0xbe2a24: r8 = String
    //     0xbe2a24: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0xbe2a28: r3 = Null
    //     0xbe2a28: add             x3, PP, #0x5c, lsl #12  ; [pp+0x5c1c8] Null
    //     0xbe2a2c: ldr             x3, [x3, #0x1c8]
    // 0xbe2a30: r0 = String()
    //     0xbe2a30: bl              #0x16fbe18  ; IsType_String_Stub
    // 0xbe2a34: ldur            x16, [fp, #-0x20]
    // 0xbe2a38: stp             x16, NULL, [SP, #0x18]
    // 0xbe2a3c: r16 = "Product"
    //     0xbe2a3c: add             x16, PP, #0x56, lsl #12  ; [pp+0x563e0] "Product"
    //     0xbe2a40: ldr             x16, [x16, #0x3e0]
    // 0xbe2a44: r30 = "INR"
    //     0xbe2a44: add             lr, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0xbe2a48: ldr             lr, [lr, #0x4c0]
    // 0xbe2a4c: stp             lr, x16, [SP, #8]
    // 0xbe2a50: ldur            x16, [fp, #-0x30]
    // 0xbe2a54: str             x16, [SP]
    // 0xbe2a58: ldur            x1, [fp, #-0x28]
    // 0xbe2a5c: r4 = const [0, 0x6, 0x5, 0x1, content, 0x1, currency, 0x4, id, 0x2, price, 0x5, type, 0x3, null]
    //     0xbe2a5c: add             x4, PP, #0x56, lsl #12  ; [pp+0x563e8] List(15) [0, 0x6, 0x5, 0x1, "content", 0x1, "currency", 0x4, "id", 0x2, "price", 0x5, "type", 0x3, Null]
    //     0xbe2a60: ldr             x4, [x4, #0x3e8]
    // 0xbe2a64: r0 = logAddToCart()
    //     0xbe2a64: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0xbe2a68: ldur            x1, [fp, #-8]
    // 0xbe2a6c: LoadField: r0 = r1->field_f
    //     0xbe2a6c: ldur            w0, [x1, #0xf]
    // 0xbe2a70: DecompressPointer r0
    //     0xbe2a70: add             x0, x0, HEAP, lsl #32
    // 0xbe2a74: LoadField: r2 = r0->field_b
    //     0xbe2a74: ldur            w2, [x0, #0xb]
    // 0xbe2a78: DecompressPointer r2
    //     0xbe2a78: add             x2, x2, HEAP, lsl #32
    // 0xbe2a7c: cmp             w2, NULL
    // 0xbe2a80: b.eq            #0xbe2e74
    // 0xbe2a84: LoadField: r0 = r2->field_2f
    //     0xbe2a84: ldur            w0, [x2, #0x2f]
    // 0xbe2a88: DecompressPointer r0
    //     0xbe2a88: add             x0, x0, HEAP, lsl #32
    // 0xbe2a8c: r2 = LoadClassIdInstr(r0)
    //     0xbe2a8c: ldur            x2, [x0, #-1]
    //     0xbe2a90: ubfx            x2, x2, #0xc, #0x14
    // 0xbe2a94: r16 = "add_to_bag"
    //     0xbe2a94: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xbe2a98: ldr             x16, [x16, #0xa38]
    // 0xbe2a9c: stp             x16, x0, [SP]
    // 0xbe2aa0: mov             x0, x2
    // 0xbe2aa4: mov             lr, x0
    // 0xbe2aa8: ldr             lr, [x21, lr, lsl #3]
    // 0xbe2aac: blr             lr
    // 0xbe2ab0: tbnz            w0, #4, #0xbe2c38
    // 0xbe2ab4: ldur            x2, [fp, #-0x10]
    // 0xbe2ab8: LoadField: r3 = r2->field_f
    //     0xbe2ab8: ldur            w3, [x2, #0xf]
    // 0xbe2abc: DecompressPointer r3
    //     0xbe2abc: add             x3, x3, HEAP, lsl #32
    // 0xbe2ac0: stur            x3, [fp, #-0x38]
    // 0xbe2ac4: r16 = Sentinel
    //     0xbe2ac4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe2ac8: cmp             w3, w16
    // 0xbe2acc: b.eq            #0xbe2e2c
    // 0xbe2ad0: ldur            x4, [fp, #-8]
    // 0xbe2ad4: ldur            x5, [fp, #-0x18]
    // 0xbe2ad8: LoadField: r0 = r4->field_f
    //     0xbe2ad8: ldur            w0, [x4, #0xf]
    // 0xbe2adc: DecompressPointer r0
    //     0xbe2adc: add             x0, x0, HEAP, lsl #32
    // 0xbe2ae0: LoadField: r1 = r0->field_b
    //     0xbe2ae0: ldur            w1, [x0, #0xb]
    // 0xbe2ae4: DecompressPointer r1
    //     0xbe2ae4: add             x1, x1, HEAP, lsl #32
    // 0xbe2ae8: cmp             w1, NULL
    // 0xbe2aec: b.eq            #0xbe2e78
    // 0xbe2af0: LoadField: r6 = r1->field_23
    //     0xbe2af0: ldur            w6, [x1, #0x23]
    // 0xbe2af4: DecompressPointer r6
    //     0xbe2af4: add             x6, x6, HEAP, lsl #32
    // 0xbe2af8: stur            x6, [fp, #-0x30]
    // 0xbe2afc: LoadField: r7 = r1->field_f
    //     0xbe2afc: ldur            w7, [x1, #0xf]
    // 0xbe2b00: DecompressPointer r7
    //     0xbe2b00: add             x7, x7, HEAP, lsl #32
    // 0xbe2b04: stur            x7, [fp, #-0x28]
    // 0xbe2b08: LoadField: r8 = r1->field_13
    //     0xbe2b08: ldur            w8, [x1, #0x13]
    // 0xbe2b0c: DecompressPointer r8
    //     0xbe2b0c: add             x8, x8, HEAP, lsl #32
    // 0xbe2b10: stur            x8, [fp, #-0x20]
    // 0xbe2b14: LoadField: r9 = r1->field_27
    //     0xbe2b14: ldur            x9, [x1, #0x27]
    // 0xbe2b18: r0 = BoxInt64Instr(r9)
    //     0xbe2b18: sbfiz           x0, x9, #1, #0x1f
    //     0xbe2b1c: cmp             x9, x0, asr #1
    //     0xbe2b20: b.eq            #0xbe2b2c
    //     0xbe2b24: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe2b28: stur            x9, [x0, #7]
    // 0xbe2b2c: stp             x0, NULL, [SP]
    // 0xbe2b30: r0 = _Double.fromInteger()
    //     0xbe2b30: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbe2b34: stur            x0, [fp, #-0x40]
    // 0xbe2b38: r0 = EventData()
    //     0xbe2b38: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xbe2b3c: mov             x1, x0
    // 0xbe2b40: ldur            x0, [fp, #-0x30]
    // 0xbe2b44: stur            x1, [fp, #-0x48]
    // 0xbe2b48: StoreField: r1->field_13 = r0
    //     0xbe2b48: stur            w0, [x1, #0x13]
    // 0xbe2b4c: ldur            x0, [fp, #-0x40]
    // 0xbe2b50: StoreField: r1->field_2f = r0
    //     0xbe2b50: stur            w0, [x1, #0x2f]
    // 0xbe2b54: ldur            x0, [fp, #-0x28]
    // 0xbe2b58: StoreField: r1->field_33 = r0
    //     0xbe2b58: stur            w0, [x1, #0x33]
    // 0xbe2b5c: ldur            x2, [fp, #-0x18]
    // 0xbe2b60: StoreField: r1->field_3b = r2
    //     0xbe2b60: stur            w2, [x1, #0x3b]
    // 0xbe2b64: StoreField: r1->field_87 = r2
    //     0xbe2b64: stur            w2, [x1, #0x87]
    // 0xbe2b68: ldur            x0, [fp, #-0x20]
    // 0xbe2b6c: StoreField: r1->field_8f = r0
    //     0xbe2b6c: stur            w0, [x1, #0x8f]
    // 0xbe2b70: r0 = EventsRequest()
    //     0xbe2b70: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xbe2b74: mov             x1, x0
    // 0xbe2b78: r0 = "add_to_bag_clicked"
    //     0xbe2b78: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fec8] "add_to_bag_clicked"
    //     0xbe2b7c: ldr             x0, [x0, #0xec8]
    // 0xbe2b80: StoreField: r1->field_7 = r0
    //     0xbe2b80: stur            w0, [x1, #7]
    // 0xbe2b84: ldur            x0, [fp, #-0x48]
    // 0xbe2b88: StoreField: r1->field_b = r0
    //     0xbe2b88: stur            w0, [x1, #0xb]
    // 0xbe2b8c: ldur            x16, [fp, #-0x38]
    // 0xbe2b90: stp             x1, x16, [SP]
    // 0xbe2b94: r0 = postEvents()
    //     0xbe2b94: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xbe2b98: ldur            x3, [fp, #-0x10]
    // 0xbe2b9c: LoadField: r0 = r3->field_f
    //     0xbe2b9c: ldur            w0, [x3, #0xf]
    // 0xbe2ba0: DecompressPointer r0
    //     0xbe2ba0: add             x0, x0, HEAP, lsl #32
    // 0xbe2ba4: stur            x0, [fp, #-0x30]
    // 0xbe2ba8: r16 = Sentinel
    //     0xbe2ba8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe2bac: cmp             w0, w16
    // 0xbe2bb0: b.eq            #0xbe2e3c
    // 0xbe2bb4: ldur            x4, [fp, #-8]
    // 0xbe2bb8: LoadField: r1 = r4->field_f
    //     0xbe2bb8: ldur            w1, [x4, #0xf]
    // 0xbe2bbc: DecompressPointer r1
    //     0xbe2bbc: add             x1, x1, HEAP, lsl #32
    // 0xbe2bc0: LoadField: r2 = r1->field_b
    //     0xbe2bc0: ldur            w2, [x1, #0xb]
    // 0xbe2bc4: DecompressPointer r2
    //     0xbe2bc4: add             x2, x2, HEAP, lsl #32
    // 0xbe2bc8: cmp             w2, NULL
    // 0xbe2bcc: b.eq            #0xbe2e7c
    // 0xbe2bd0: LoadField: r1 = r2->field_f
    //     0xbe2bd0: ldur            w1, [x2, #0xf]
    // 0xbe2bd4: DecompressPointer r1
    //     0xbe2bd4: add             x1, x1, HEAP, lsl #32
    // 0xbe2bd8: stur            x1, [fp, #-0x28]
    // 0xbe2bdc: LoadField: r3 = r2->field_13
    //     0xbe2bdc: ldur            w3, [x2, #0x13]
    // 0xbe2be0: DecompressPointer r3
    //     0xbe2be0: add             x3, x3, HEAP, lsl #32
    // 0xbe2be4: stur            x3, [fp, #-0x20]
    // 0xbe2be8: ArrayLoad: r4 = r2[0]  ; List_8
    //     0xbe2be8: ldur            x4, [x2, #0x17]
    // 0xbe2bec: stur            x4, [fp, #-0x50]
    // 0xbe2bf0: r0 = AddToBagRequest()
    //     0xbe2bf0: bl              #0x9c67ec  ; AllocateAddToBagRequestStub -> AddToBagRequest (size=0x1c)
    // 0xbe2bf4: mov             x1, x0
    // 0xbe2bf8: ldur            x0, [fp, #-0x28]
    // 0xbe2bfc: StoreField: r1->field_7 = r0
    //     0xbe2bfc: stur            w0, [x1, #7]
    // 0xbe2c00: ldur            x0, [fp, #-0x20]
    // 0xbe2c04: StoreField: r1->field_b = r0
    //     0xbe2c04: stur            w0, [x1, #0xb]
    // 0xbe2c08: ldur            x0, [fp, #-0x50]
    // 0xbe2c0c: StoreField: r1->field_f = r0
    //     0xbe2c0c: stur            x0, [x1, #0xf]
    // 0xbe2c10: ldur            x16, [fp, #-0x30]
    // 0xbe2c14: stp             x1, x16, [SP, #8]
    // 0xbe2c18: r16 = false
    //     0xbe2c18: add             x16, NULL, #0x30  ; false
    // 0xbe2c1c: str             x16, [SP]
    // 0xbe2c20: r4 = 0
    //     0xbe2c20: movz            x4, #0
    // 0xbe2c24: ldr             x0, [SP, #0x10]
    // 0xbe2c28: r5 = UnlinkedCall_0x613b5c
    //     0xbe2c28: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c1d8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe2c2c: ldp             x5, lr, [x16, #0x1d8]
    // 0xbe2c30: blr             lr
    // 0xbe2c34: b               #0xbe2e1c
    // 0xbe2c38: ldur            x4, [fp, #-8]
    // 0xbe2c3c: ldur            x3, [fp, #-0x10]
    // 0xbe2c40: ldur            x2, [fp, #-0x18]
    // 0xbe2c44: LoadField: r5 = r3->field_f
    //     0xbe2c44: ldur            w5, [x3, #0xf]
    // 0xbe2c48: DecompressPointer r5
    //     0xbe2c48: add             x5, x5, HEAP, lsl #32
    // 0xbe2c4c: stur            x5, [fp, #-0x38]
    // 0xbe2c50: r16 = Sentinel
    //     0xbe2c50: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe2c54: cmp             w5, w16
    // 0xbe2c58: b.eq            #0xbe2e4c
    // 0xbe2c5c: LoadField: r0 = r4->field_f
    //     0xbe2c5c: ldur            w0, [x4, #0xf]
    // 0xbe2c60: DecompressPointer r0
    //     0xbe2c60: add             x0, x0, HEAP, lsl #32
    // 0xbe2c64: LoadField: r1 = r0->field_b
    //     0xbe2c64: ldur            w1, [x0, #0xb]
    // 0xbe2c68: DecompressPointer r1
    //     0xbe2c68: add             x1, x1, HEAP, lsl #32
    // 0xbe2c6c: cmp             w1, NULL
    // 0xbe2c70: b.eq            #0xbe2e80
    // 0xbe2c74: LoadField: r6 = r1->field_23
    //     0xbe2c74: ldur            w6, [x1, #0x23]
    // 0xbe2c78: DecompressPointer r6
    //     0xbe2c78: add             x6, x6, HEAP, lsl #32
    // 0xbe2c7c: stur            x6, [fp, #-0x30]
    // 0xbe2c80: LoadField: r7 = r1->field_f
    //     0xbe2c80: ldur            w7, [x1, #0xf]
    // 0xbe2c84: DecompressPointer r7
    //     0xbe2c84: add             x7, x7, HEAP, lsl #32
    // 0xbe2c88: stur            x7, [fp, #-0x28]
    // 0xbe2c8c: LoadField: r8 = r1->field_13
    //     0xbe2c8c: ldur            w8, [x1, #0x13]
    // 0xbe2c90: DecompressPointer r8
    //     0xbe2c90: add             x8, x8, HEAP, lsl #32
    // 0xbe2c94: stur            x8, [fp, #-0x20]
    // 0xbe2c98: LoadField: r9 = r1->field_27
    //     0xbe2c98: ldur            x9, [x1, #0x27]
    // 0xbe2c9c: r0 = BoxInt64Instr(r9)
    //     0xbe2c9c: sbfiz           x0, x9, #1, #0x1f
    //     0xbe2ca0: cmp             x9, x0, asr #1
    //     0xbe2ca4: b.eq            #0xbe2cb0
    //     0xbe2ca8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe2cac: stur            x9, [x0, #7]
    // 0xbe2cb0: stp             x0, NULL, [SP]
    // 0xbe2cb4: r0 = _Double.fromInteger()
    //     0xbe2cb4: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbe2cb8: stur            x0, [fp, #-0x40]
    // 0xbe2cbc: r0 = EventData()
    //     0xbe2cbc: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0xbe2cc0: mov             x1, x0
    // 0xbe2cc4: ldur            x0, [fp, #-0x30]
    // 0xbe2cc8: stur            x1, [fp, #-0x48]
    // 0xbe2ccc: StoreField: r1->field_13 = r0
    //     0xbe2ccc: stur            w0, [x1, #0x13]
    // 0xbe2cd0: ldur            x0, [fp, #-0x40]
    // 0xbe2cd4: StoreField: r1->field_2f = r0
    //     0xbe2cd4: stur            w0, [x1, #0x2f]
    // 0xbe2cd8: ldur            x0, [fp, #-0x28]
    // 0xbe2cdc: StoreField: r1->field_33 = r0
    //     0xbe2cdc: stur            w0, [x1, #0x33]
    // 0xbe2ce0: ldur            x0, [fp, #-0x18]
    // 0xbe2ce4: StoreField: r1->field_3b = r0
    //     0xbe2ce4: stur            w0, [x1, #0x3b]
    // 0xbe2ce8: StoreField: r1->field_87 = r0
    //     0xbe2ce8: stur            w0, [x1, #0x87]
    // 0xbe2cec: ldur            x0, [fp, #-0x20]
    // 0xbe2cf0: StoreField: r1->field_8f = r0
    //     0xbe2cf0: stur            w0, [x1, #0x8f]
    // 0xbe2cf4: r0 = EventsRequest()
    //     0xbe2cf4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0xbe2cf8: mov             x1, x0
    // 0xbe2cfc: r0 = "buy_now_clicked"
    //     0xbe2cfc: add             x0, PP, #0x31, lsl #12  ; [pp+0x31be0] "buy_now_clicked"
    //     0xbe2d00: ldr             x0, [x0, #0xbe0]
    // 0xbe2d04: StoreField: r1->field_7 = r0
    //     0xbe2d04: stur            w0, [x1, #7]
    // 0xbe2d08: ldur            x0, [fp, #-0x48]
    // 0xbe2d0c: StoreField: r1->field_b = r0
    //     0xbe2d0c: stur            w0, [x1, #0xb]
    // 0xbe2d10: ldur            x16, [fp, #-0x38]
    // 0xbe2d14: stp             x1, x16, [SP]
    // 0xbe2d18: r0 = postEvents()
    //     0xbe2d18: bl              #0x86086c  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0xbe2d1c: ldur            x0, [fp, #-8]
    // 0xbe2d20: LoadField: r1 = r0->field_f
    //     0xbe2d20: ldur            w1, [x0, #0xf]
    // 0xbe2d24: DecompressPointer r1
    //     0xbe2d24: add             x1, x1, HEAP, lsl #32
    // 0xbe2d28: LoadField: r2 = r1->field_b
    //     0xbe2d28: ldur            w2, [x1, #0xb]
    // 0xbe2d2c: DecompressPointer r2
    //     0xbe2d2c: add             x2, x2, HEAP, lsl #32
    // 0xbe2d30: cmp             w2, NULL
    // 0xbe2d34: b.eq            #0xbe2e84
    // 0xbe2d38: LoadField: r1 = r2->field_43
    //     0xbe2d38: ldur            w1, [x2, #0x43]
    // 0xbe2d3c: DecompressPointer r1
    //     0xbe2d3c: add             x1, x1, HEAP, lsl #32
    // 0xbe2d40: cmp             w1, NULL
    // 0xbe2d44: b.ne            #0xbe2d50
    // 0xbe2d48: r1 = Null
    //     0xbe2d48: mov             x1, NULL
    // 0xbe2d4c: b               #0xbe2d64
    // 0xbe2d50: LoadField: r2 = r1->field_7
    //     0xbe2d50: ldur            w2, [x1, #7]
    // 0xbe2d54: cbnz            w2, #0xbe2d60
    // 0xbe2d58: r1 = false
    //     0xbe2d58: add             x1, NULL, #0x30  ; false
    // 0xbe2d5c: b               #0xbe2d64
    // 0xbe2d60: r1 = true
    //     0xbe2d60: add             x1, NULL, #0x20  ; true
    // 0xbe2d64: cmp             w1, NULL
    // 0xbe2d68: b.eq            #0xbe2db8
    // 0xbe2d6c: tbnz            w1, #4, #0xbe2db8
    // 0xbe2d70: LoadField: r3 = r0->field_13
    //     0xbe2d70: ldur            w3, [x0, #0x13]
    // 0xbe2d74: DecompressPointer r3
    //     0xbe2d74: add             x3, x3, HEAP, lsl #32
    // 0xbe2d78: ldur            x2, [fp, #-0x10]
    // 0xbe2d7c: stur            x3, [fp, #-0x18]
    // 0xbe2d80: r1 = Function '<anonymous closure>':.
    //     0xbe2d80: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c1e8] AnonymousClosure: (0xbe3844), in [package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xbe1674)
    //     0xbe2d84: ldr             x1, [x1, #0x1e8]
    // 0xbe2d88: r0 = AllocateClosure()
    //     0xbe2d88: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe2d8c: stp             x0, NULL, [SP, #0x18]
    // 0xbe2d90: ldur            x16, [fp, #-0x18]
    // 0xbe2d94: r30 = true
    //     0xbe2d94: add             lr, NULL, #0x20  ; true
    // 0xbe2d98: stp             lr, x16, [SP, #8]
    // 0xbe2d9c: r16 = Instance_RoundedRectangleBorder
    //     0xbe2d9c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbe2da0: ldr             x16, [x16, #0xd68]
    // 0xbe2da4: str             x16, [SP]
    // 0xbe2da8: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0xbe2da8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0xbe2dac: ldr             x4, [x4, #0xb20]
    // 0xbe2db0: r0 = showModalBottomSheet()
    //     0xbe2db0: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xbe2db4: b               #0xbe2e1c
    // 0xbe2db8: r16 = PreferenceManager
    //     0xbe2db8: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xbe2dbc: ldr             x16, [x16, #0x878]
    // 0xbe2dc0: str             x16, [SP]
    // 0xbe2dc4: r0 = toString()
    //     0xbe2dc4: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xbe2dc8: r16 = <PreferenceManager>
    //     0xbe2dc8: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xbe2dcc: ldr             x16, [x16, #0x880]
    // 0xbe2dd0: stp             x0, x16, [SP]
    // 0xbe2dd4: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xbe2dd4: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xbe2dd8: r0 = Inst.find()
    //     0xbe2dd8: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbe2ddc: mov             x1, x0
    // 0xbe2de0: r2 = "token"
    //     0xbe2de0: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xbe2de4: ldr             x2, [x2, #0x958]
    // 0xbe2de8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbe2de8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbe2dec: r0 = getString()
    //     0xbe2dec: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xbe2df0: ldur            x2, [fp, #-0x10]
    // 0xbe2df4: r1 = Function '<anonymous closure>':.
    //     0xbe2df4: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c1f0] AnonymousClosure: (0xbe2e88), in [package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xbe1674)
    //     0xbe2df8: ldr             x1, [x1, #0x1f0]
    // 0xbe2dfc: stur            x0, [fp, #-8]
    // 0xbe2e00: r0 = AllocateClosure()
    //     0xbe2e00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe2e04: r16 = <Null?>
    //     0xbe2e04: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xbe2e08: ldur            lr, [fp, #-8]
    // 0xbe2e0c: stp             lr, x16, [SP, #8]
    // 0xbe2e10: str             x0, [SP]
    // 0xbe2e14: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbe2e14: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbe2e18: r0 = then()
    //     0xbe2e18: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xbe2e1c: r0 = Null
    //     0xbe2e1c: mov             x0, NULL
    // 0xbe2e20: LeaveFrame
    //     0xbe2e20: mov             SP, fp
    //     0xbe2e24: ldp             fp, lr, [SP], #0x10
    // 0xbe2e28: ret
    //     0xbe2e28: ret             
    // 0xbe2e2c: r16 = "controller"
    //     0xbe2e2c: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe2e30: str             x16, [SP]
    // 0xbe2e34: r0 = _throwLocalNotInitialized()
    //     0xbe2e34: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe2e38: brk             #0
    // 0xbe2e3c: r16 = "controller"
    //     0xbe2e3c: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe2e40: str             x16, [SP]
    // 0xbe2e44: r0 = _throwLocalNotInitialized()
    //     0xbe2e44: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe2e48: brk             #0
    // 0xbe2e4c: r16 = "controller"
    //     0xbe2e4c: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe2e50: str             x16, [SP]
    // 0xbe2e54: r0 = _throwLocalNotInitialized()
    //     0xbe2e54: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe2e58: brk             #0
    // 0xbe2e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe2e5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe2e60: b               #0xbe2760
    // 0xbe2e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe2e84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe2e84: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xbe2e88, size: 0x9bc
    // 0xbe2e88: EnterFrame
    //     0xbe2e88: stp             fp, lr, [SP, #-0x10]!
    //     0xbe2e8c: mov             fp, SP
    // 0xbe2e90: AllocStack(0x50)
    //     0xbe2e90: sub             SP, SP, #0x50
    // 0xbe2e94: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xbe2e94: stur            NULL, [fp, #-8]
    //     0xbe2e98: movz            x0, #0
    //     0xbe2e9c: add             x1, fp, w0, sxtw #2
    //     0xbe2ea0: ldr             x1, [x1, #0x18]
    //     0xbe2ea4: add             x2, fp, w0, sxtw #2
    //     0xbe2ea8: ldr             x2, [x2, #0x10]
    //     0xbe2eac: stur            x2, [fp, #-0x18]
    //     0xbe2eb0: ldur            w3, [x1, #0x17]
    //     0xbe2eb4: add             x3, x3, HEAP, lsl #32
    //     0xbe2eb8: stur            x3, [fp, #-0x10]
    // 0xbe2ebc: CheckStackOverflow
    //     0xbe2ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe2ec0: cmp             SP, x16
    //     0xbe2ec4: b.ls            #0xbe3828
    // 0xbe2ec8: InitAsync() -> Future<Null?>
    //     0xbe2ec8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xbe2ecc: bl              #0x6326e0  ; InitAsyncStub
    // 0xbe2ed0: ldur            x0, [fp, #-0x18]
    // 0xbe2ed4: r1 = LoadClassIdInstr(r0)
    //     0xbe2ed4: ldur            x1, [x0, #-1]
    //     0xbe2ed8: ubfx            x1, x1, #0xc, #0x14
    // 0xbe2edc: r16 = ""
    //     0xbe2edc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe2ee0: stp             x16, x0, [SP]
    // 0xbe2ee4: mov             x0, x1
    // 0xbe2ee8: mov             lr, x0
    // 0xbe2eec: ldr             lr, [x21, lr, lsl #3]
    // 0xbe2ef0: blr             lr
    // 0xbe2ef4: tbz             w0, #4, #0xbe35b8
    // 0xbe2ef8: ldur            x2, [fp, #-0x10]
    // 0xbe2efc: LoadField: r3 = r2->field_b
    //     0xbe2efc: ldur            w3, [x2, #0xb]
    // 0xbe2f00: DecompressPointer r3
    //     0xbe2f00: add             x3, x3, HEAP, lsl #32
    // 0xbe2f04: stur            x3, [fp, #-0x28]
    // 0xbe2f08: LoadField: r0 = r3->field_f
    //     0xbe2f08: ldur            w0, [x3, #0xf]
    // 0xbe2f0c: DecompressPointer r0
    //     0xbe2f0c: add             x0, x0, HEAP, lsl #32
    // 0xbe2f10: LoadField: r1 = r0->field_b
    //     0xbe2f10: ldur            w1, [x0, #0xb]
    // 0xbe2f14: DecompressPointer r1
    //     0xbe2f14: add             x1, x1, HEAP, lsl #32
    // 0xbe2f18: cmp             w1, NULL
    // 0xbe2f1c: b.eq            #0xbe3830
    // 0xbe2f20: LoadField: r4 = r1->field_f
    //     0xbe2f20: ldur            w4, [x1, #0xf]
    // 0xbe2f24: DecompressPointer r4
    //     0xbe2f24: add             x4, x4, HEAP, lsl #32
    // 0xbe2f28: stur            x4, [fp, #-0x18]
    // 0xbe2f2c: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xbe2f2c: ldur            x5, [x1, #0x17]
    // 0xbe2f30: stur            x5, [fp, #-0x20]
    // 0xbe2f34: LoadField: r0 = r1->field_27
    //     0xbe2f34: ldur            x0, [x1, #0x27]
    // 0xbe2f38: mul             x6, x0, x5
    // 0xbe2f3c: r0 = BoxInt64Instr(r6)
    //     0xbe2f3c: sbfiz           x0, x6, #1, #0x1f
    //     0xbe2f40: cmp             x6, x0, asr #1
    //     0xbe2f44: b.eq            #0xbe2f50
    //     0xbe2f48: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe2f4c: stur            x6, [x0, #7]
    // 0xbe2f50: stp             x0, NULL, [SP]
    // 0xbe2f54: r0 = _Double.fromInteger()
    //     0xbe2f54: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbe2f58: stur            x0, [fp, #-0x30]
    // 0xbe2f5c: r0 = CheckoutEventData()
    //     0xbe2f5c: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xbe2f60: mov             x2, x0
    // 0xbe2f64: ldur            x0, [fp, #-0x30]
    // 0xbe2f68: stur            x2, [fp, #-0x38]
    // 0xbe2f6c: StoreField: r2->field_7 = r0
    //     0xbe2f6c: stur            w0, [x2, #7]
    // 0xbe2f70: ldur            x3, [fp, #-0x20]
    // 0xbe2f74: r0 = BoxInt64Instr(r3)
    //     0xbe2f74: sbfiz           x0, x3, #1, #0x1f
    //     0xbe2f78: cmp             x3, x0, asr #1
    //     0xbe2f7c: b.eq            #0xbe2f88
    //     0xbe2f80: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe2f84: stur            x3, [x0, #7]
    // 0xbe2f88: StoreField: r2->field_b = r0
    //     0xbe2f88: stur            w0, [x2, #0xb]
    // 0xbe2f8c: ldur            x0, [fp, #-0x18]
    // 0xbe2f90: StoreField: r2->field_f = r0
    //     0xbe2f90: stur            w0, [x2, #0xf]
    // 0xbe2f94: ldur            x0, [fp, #-0x10]
    // 0xbe2f98: LoadField: r1 = r0->field_f
    //     0xbe2f98: ldur            w1, [x0, #0xf]
    // 0xbe2f9c: DecompressPointer r1
    //     0xbe2f9c: add             x1, x1, HEAP, lsl #32
    // 0xbe2fa0: r16 = Sentinel
    //     0xbe2fa0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe2fa4: cmp             w1, w16
    // 0xbe2fa8: b.eq            #0xbe37c8
    // 0xbe2fac: str             x1, [SP]
    // 0xbe2fb0: r4 = 0
    //     0xbe2fb0: movz            x4, #0
    // 0xbe2fb4: ldr             x0, [SP]
    // 0xbe2fb8: r5 = UnlinkedCall_0x613b5c
    //     0xbe2fb8: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c1f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe2fbc: ldp             x5, lr, [x16, #0x1f8]
    // 0xbe2fc0: blr             lr
    // 0xbe2fc4: LoadField: r1 = r0->field_1b
    //     0xbe2fc4: ldur            w1, [x0, #0x1b]
    // 0xbe2fc8: DecompressPointer r1
    //     0xbe2fc8: add             x1, x1, HEAP, lsl #32
    // 0xbe2fcc: cmp             w1, NULL
    // 0xbe2fd0: b.ne            #0xbe2fdc
    // 0xbe2fd4: r0 = Null
    //     0xbe2fd4: mov             x0, NULL
    // 0xbe2fd8: b               #0xbe2ff4
    // 0xbe2fdc: LoadField: r0 = r1->field_b
    //     0xbe2fdc: ldur            w0, [x1, #0xb]
    // 0xbe2fe0: cbz             w0, #0xbe2fec
    // 0xbe2fe4: r1 = false
    //     0xbe2fe4: add             x1, NULL, #0x30  ; false
    // 0xbe2fe8: b               #0xbe2ff0
    // 0xbe2fec: r1 = true
    //     0xbe2fec: add             x1, NULL, #0x20  ; true
    // 0xbe2ff0: mov             x0, x1
    // 0xbe2ff4: cmp             w0, NULL
    // 0xbe2ff8: b.eq            #0xbe3000
    // 0xbe2ffc: tbz             w0, #4, #0xbe30a8
    // 0xbe3000: ldur            x0, [fp, #-0x10]
    // 0xbe3004: LoadField: r1 = r0->field_f
    //     0xbe3004: ldur            w1, [x0, #0xf]
    // 0xbe3008: DecompressPointer r1
    //     0xbe3008: add             x1, x1, HEAP, lsl #32
    // 0xbe300c: r16 = Sentinel
    //     0xbe300c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe3010: cmp             w1, w16
    // 0xbe3014: b.eq            #0xbe37d8
    // 0xbe3018: str             x1, [SP]
    // 0xbe301c: r4 = 0
    //     0xbe301c: movz            x4, #0
    // 0xbe3020: ldr             x0, [SP]
    // 0xbe3024: r16 = UnlinkedCall_0x613b5c
    //     0xbe3024: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c208] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe3028: add             x16, x16, #0x208
    // 0xbe302c: ldp             x5, lr, [x16]
    // 0xbe3030: blr             lr
    // 0xbe3034: LoadField: r1 = r0->field_1b
    //     0xbe3034: ldur            w1, [x0, #0x1b]
    // 0xbe3038: DecompressPointer r1
    //     0xbe3038: add             x1, x1, HEAP, lsl #32
    // 0xbe303c: cmp             w1, NULL
    // 0xbe3040: b.ne            #0xbe304c
    // 0xbe3044: r0 = Null
    //     0xbe3044: mov             x0, NULL
    // 0xbe3048: b               #0xbe3090
    // 0xbe304c: r0 = first()
    //     0xbe304c: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xbe3050: cmp             w0, NULL
    // 0xbe3054: b.ne            #0xbe3060
    // 0xbe3058: r0 = Null
    //     0xbe3058: mov             x0, NULL
    // 0xbe305c: b               #0xbe3090
    // 0xbe3060: LoadField: r1 = r0->field_13
    //     0xbe3060: ldur            w1, [x0, #0x13]
    // 0xbe3064: DecompressPointer r1
    //     0xbe3064: add             x1, x1, HEAP, lsl #32
    // 0xbe3068: cmp             w1, NULL
    // 0xbe306c: b.ne            #0xbe3078
    // 0xbe3070: r0 = Null
    //     0xbe3070: mov             x0, NULL
    // 0xbe3074: b               #0xbe3090
    // 0xbe3078: LoadField: r0 = r1->field_7
    //     0xbe3078: ldur            w0, [x1, #7]
    // 0xbe307c: cbz             w0, #0xbe3088
    // 0xbe3080: r1 = false
    //     0xbe3080: add             x1, NULL, #0x30  ; false
    // 0xbe3084: b               #0xbe308c
    // 0xbe3088: r1 = true
    //     0xbe3088: add             x1, NULL, #0x20  ; true
    // 0xbe308c: mov             x0, x1
    // 0xbe3090: cmp             w0, NULL
    // 0xbe3094: b.ne            #0xbe30a4
    // 0xbe3098: ldur            x2, [fp, #-0x10]
    // 0xbe309c: ldur            x0, [fp, #-0x28]
    // 0xbe30a0: b               #0xbe3338
    // 0xbe30a4: tbnz            w0, #4, #0xbe3330
    // 0xbe30a8: ldur            x0, [fp, #-0x10]
    // 0xbe30ac: ldur            x1, [fp, #-0x28]
    // 0xbe30b0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe30b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe30b4: ldr             x0, [x0, #0x1c80]
    //     0xbe30b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe30bc: cmp             w0, w16
    //     0xbe30c0: b.ne            #0xbe30cc
    //     0xbe30c4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe30c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe30cc: r1 = Null
    //     0xbe30cc: mov             x1, NULL
    // 0xbe30d0: r2 = 40
    //     0xbe30d0: movz            x2, #0x28
    // 0xbe30d4: r0 = AllocateArray()
    //     0xbe30d4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe30d8: mov             x2, x0
    // 0xbe30dc: stur            x2, [fp, #-0x18]
    // 0xbe30e0: r16 = "couponCode"
    //     0xbe30e0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xbe30e4: ldr             x16, [x16, #0x310]
    // 0xbe30e8: StoreField: r2->field_f = r16
    //     0xbe30e8: stur            w16, [x2, #0xf]
    // 0xbe30ec: r16 = ""
    //     0xbe30ec: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe30f0: StoreField: r2->field_13 = r16
    //     0xbe30f0: stur            w16, [x2, #0x13]
    // 0xbe30f4: r16 = "product_id"
    //     0xbe30f4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xbe30f8: ldr             x16, [x16, #0x9b8]
    // 0xbe30fc: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe30fc: stur            w16, [x2, #0x17]
    // 0xbe3100: ldur            x0, [fp, #-0x28]
    // 0xbe3104: LoadField: r1 = r0->field_f
    //     0xbe3104: ldur            w1, [x0, #0xf]
    // 0xbe3108: DecompressPointer r1
    //     0xbe3108: add             x1, x1, HEAP, lsl #32
    // 0xbe310c: LoadField: r3 = r1->field_b
    //     0xbe310c: ldur            w3, [x1, #0xb]
    // 0xbe3110: DecompressPointer r3
    //     0xbe3110: add             x3, x3, HEAP, lsl #32
    // 0xbe3114: cmp             w3, NULL
    // 0xbe3118: b.eq            #0xbe3834
    // 0xbe311c: LoadField: r0 = r3->field_f
    //     0xbe311c: ldur            w0, [x3, #0xf]
    // 0xbe3120: DecompressPointer r0
    //     0xbe3120: add             x0, x0, HEAP, lsl #32
    // 0xbe3124: StoreField: r2->field_1b = r0
    //     0xbe3124: stur            w0, [x2, #0x1b]
    // 0xbe3128: r16 = "sku_id"
    //     0xbe3128: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xbe312c: ldr             x16, [x16, #0x498]
    // 0xbe3130: StoreField: r2->field_1f = r16
    //     0xbe3130: stur            w16, [x2, #0x1f]
    // 0xbe3134: LoadField: r0 = r3->field_13
    //     0xbe3134: ldur            w0, [x3, #0x13]
    // 0xbe3138: DecompressPointer r0
    //     0xbe3138: add             x0, x0, HEAP, lsl #32
    // 0xbe313c: StoreField: r2->field_23 = r0
    //     0xbe313c: stur            w0, [x2, #0x23]
    // 0xbe3140: r16 = "quantity"
    //     0xbe3140: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbe3144: ldr             x16, [x16, #0x428]
    // 0xbe3148: StoreField: r2->field_27 = r16
    //     0xbe3148: stur            w16, [x2, #0x27]
    // 0xbe314c: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbe314c: ldur            x4, [x3, #0x17]
    // 0xbe3150: r0 = BoxInt64Instr(r4)
    //     0xbe3150: sbfiz           x0, x4, #1, #0x1f
    //     0xbe3154: cmp             x4, x0, asr #1
    //     0xbe3158: b.eq            #0xbe3164
    //     0xbe315c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe3160: stur            x4, [x0, #7]
    // 0xbe3164: mov             x1, x2
    // 0xbe3168: ArrayStore: r1[7] = r0  ; List_4
    //     0xbe3168: add             x25, x1, #0x2b
    //     0xbe316c: str             w0, [x25]
    //     0xbe3170: tbz             w0, #0, #0xbe318c
    //     0xbe3174: ldurb           w16, [x1, #-1]
    //     0xbe3178: ldurb           w17, [x0, #-1]
    //     0xbe317c: and             x16, x17, x16, lsr #2
    //     0xbe3180: tst             x16, HEAP, lsr #32
    //     0xbe3184: b.eq            #0xbe318c
    //     0xbe3188: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe318c: r16 = "checkout_event_data"
    //     0xbe318c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xbe3190: ldr             x16, [x16, #0xd50]
    // 0xbe3194: StoreField: r2->field_2f = r16
    //     0xbe3194: stur            w16, [x2, #0x2f]
    // 0xbe3198: mov             x1, x2
    // 0xbe319c: ldur            x0, [fp, #-0x38]
    // 0xbe31a0: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe31a0: add             x25, x1, #0x33
    //     0xbe31a4: str             w0, [x25]
    //     0xbe31a8: tbz             w0, #0, #0xbe31c4
    //     0xbe31ac: ldurb           w16, [x1, #-1]
    //     0xbe31b0: ldurb           w17, [x0, #-1]
    //     0xbe31b4: and             x16, x17, x16, lsr #2
    //     0xbe31b8: tst             x16, HEAP, lsr #32
    //     0xbe31bc: b.eq            #0xbe31c4
    //     0xbe31c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe31c4: r16 = "previousScreenSource"
    //     0xbe31c4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xbe31c8: ldr             x16, [x16, #0x448]
    // 0xbe31cc: StoreField: r2->field_37 = r16
    //     0xbe31cc: stur            w16, [x2, #0x37]
    // 0xbe31d0: r16 = "product_page"
    //     0xbe31d0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe31d4: ldr             x16, [x16, #0x480]
    // 0xbe31d8: StoreField: r2->field_3b = r16
    //     0xbe31d8: stur            w16, [x2, #0x3b]
    // 0xbe31dc: r16 = "is_skipped_address"
    //     0xbe31dc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xbe31e0: ldr             x16, [x16, #0xb80]
    // 0xbe31e4: StoreField: r2->field_3f = r16
    //     0xbe31e4: stur            w16, [x2, #0x3f]
    // 0xbe31e8: r16 = true
    //     0xbe31e8: add             x16, NULL, #0x20  ; true
    // 0xbe31ec: StoreField: r2->field_43 = r16
    //     0xbe31ec: stur            w16, [x2, #0x43]
    // 0xbe31f0: r16 = "coming_from"
    //     0xbe31f0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xbe31f4: ldr             x16, [x16, #0x328]
    // 0xbe31f8: StoreField: r2->field_47 = r16
    //     0xbe31f8: stur            w16, [x2, #0x47]
    // 0xbe31fc: LoadField: r0 = r3->field_2f
    //     0xbe31fc: ldur            w0, [x3, #0x2f]
    // 0xbe3200: DecompressPointer r0
    //     0xbe3200: add             x0, x0, HEAP, lsl #32
    // 0xbe3204: mov             x1, x2
    // 0xbe3208: ArrayStore: r1[15] = r0  ; List_4
    //     0xbe3208: add             x25, x1, #0x4b
    //     0xbe320c: str             w0, [x25]
    //     0xbe3210: tbz             w0, #0, #0xbe322c
    //     0xbe3214: ldurb           w16, [x1, #-1]
    //     0xbe3218: ldurb           w17, [x0, #-1]
    //     0xbe321c: and             x16, x17, x16, lsr #2
    //     0xbe3220: tst             x16, HEAP, lsr #32
    //     0xbe3224: b.eq            #0xbe322c
    //     0xbe3228: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe322c: r16 = "checkout_id"
    //     0xbe322c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xbe3230: ldr             x16, [x16, #0xb88]
    // 0xbe3234: StoreField: r2->field_4f = r16
    //     0xbe3234: stur            w16, [x2, #0x4f]
    // 0xbe3238: ldur            x0, [fp, #-0x10]
    // 0xbe323c: LoadField: r1 = r0->field_f
    //     0xbe323c: ldur            w1, [x0, #0xf]
    // 0xbe3240: DecompressPointer r1
    //     0xbe3240: add             x1, x1, HEAP, lsl #32
    // 0xbe3244: r16 = Sentinel
    //     0xbe3244: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe3248: cmp             w1, w16
    // 0xbe324c: b.eq            #0xbe37e8
    // 0xbe3250: str             x1, [SP]
    // 0xbe3254: r4 = 0
    //     0xbe3254: movz            x4, #0
    // 0xbe3258: ldr             x0, [SP]
    // 0xbe325c: r16 = UnlinkedCall_0x613b5c
    //     0xbe325c: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c218] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe3260: add             x16, x16, #0x218
    // 0xbe3264: ldp             x5, lr, [x16]
    // 0xbe3268: blr             lr
    // 0xbe326c: ldur            x1, [fp, #-0x18]
    // 0xbe3270: ArrayStore: r1[17] = r0  ; List_4
    //     0xbe3270: add             x25, x1, #0x53
    //     0xbe3274: str             w0, [x25]
    //     0xbe3278: tbz             w0, #0, #0xbe3294
    //     0xbe327c: ldurb           w16, [x1, #-1]
    //     0xbe3280: ldurb           w17, [x0, #-1]
    //     0xbe3284: and             x16, x17, x16, lsr #2
    //     0xbe3288: tst             x16, HEAP, lsr #32
    //     0xbe328c: b.eq            #0xbe3294
    //     0xbe3290: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3294: ldur            x1, [fp, #-0x18]
    // 0xbe3298: r16 = "user_data"
    //     0xbe3298: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xbe329c: ldr             x16, [x16, #0x58]
    // 0xbe32a0: StoreField: r1->field_57 = r16
    //     0xbe32a0: stur            w16, [x1, #0x57]
    // 0xbe32a4: ldur            x2, [fp, #-0x10]
    // 0xbe32a8: LoadField: r0 = r2->field_f
    //     0xbe32a8: ldur            w0, [x2, #0xf]
    // 0xbe32ac: DecompressPointer r0
    //     0xbe32ac: add             x0, x0, HEAP, lsl #32
    // 0xbe32b0: r16 = Sentinel
    //     0xbe32b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe32b4: cmp             w0, w16
    // 0xbe32b8: b.eq            #0xbe37f8
    // 0xbe32bc: str             x0, [SP]
    // 0xbe32c0: r4 = 0
    //     0xbe32c0: movz            x4, #0
    // 0xbe32c4: ldr             x0, [SP]
    // 0xbe32c8: r16 = UnlinkedCall_0x613b5c
    //     0xbe32c8: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c228] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe32cc: add             x16, x16, #0x228
    // 0xbe32d0: ldp             x5, lr, [x16]
    // 0xbe32d4: blr             lr
    // 0xbe32d8: ldur            x1, [fp, #-0x18]
    // 0xbe32dc: ArrayStore: r1[19] = r0  ; List_4
    //     0xbe32dc: add             x25, x1, #0x5b
    //     0xbe32e0: str             w0, [x25]
    //     0xbe32e4: tbz             w0, #0, #0xbe3300
    //     0xbe32e8: ldurb           w16, [x1, #-1]
    //     0xbe32ec: ldurb           w17, [x0, #-1]
    //     0xbe32f0: and             x16, x17, x16, lsr #2
    //     0xbe32f4: tst             x16, HEAP, lsr #32
    //     0xbe32f8: b.eq            #0xbe3300
    //     0xbe32fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3300: r16 = <String, dynamic>
    //     0xbe3300: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xbe3304: ldur            lr, [fp, #-0x18]
    // 0xbe3308: stp             lr, x16, [SP]
    // 0xbe330c: r0 = Map._fromLiteral()
    //     0xbe330c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe3310: r16 = "/checkout_request_address_page"
    //     0xbe3310: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xbe3314: ldr             x16, [x16, #0x9e8]
    // 0xbe3318: stp             x16, NULL, [SP, #8]
    // 0xbe331c: str             x0, [SP]
    // 0xbe3320: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe3320: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe3324: ldr             x4, [x4, #0x438]
    // 0xbe3328: r0 = GetNavigation.toNamed()
    //     0xbe3328: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe332c: b               #0xbe37c0
    // 0xbe3330: ldur            x2, [fp, #-0x10]
    // 0xbe3334: ldur            x0, [fp, #-0x28]
    // 0xbe3338: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe3338: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe333c: ldr             x0, [x0, #0x1c80]
    //     0xbe3340: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe3344: cmp             w0, w16
    //     0xbe3348: b.ne            #0xbe3354
    //     0xbe334c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe3350: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe3354: r1 = Null
    //     0xbe3354: mov             x1, NULL
    // 0xbe3358: r2 = 40
    //     0xbe3358: movz            x2, #0x28
    // 0xbe335c: r0 = AllocateArray()
    //     0xbe335c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe3360: mov             x2, x0
    // 0xbe3364: stur            x2, [fp, #-0x18]
    // 0xbe3368: r16 = "couponCode"
    //     0xbe3368: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xbe336c: ldr             x16, [x16, #0x310]
    // 0xbe3370: StoreField: r2->field_f = r16
    //     0xbe3370: stur            w16, [x2, #0xf]
    // 0xbe3374: r16 = ""
    //     0xbe3374: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe3378: StoreField: r2->field_13 = r16
    //     0xbe3378: stur            w16, [x2, #0x13]
    // 0xbe337c: r16 = "product_id"
    //     0xbe337c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xbe3380: ldr             x16, [x16, #0x9b8]
    // 0xbe3384: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe3384: stur            w16, [x2, #0x17]
    // 0xbe3388: ldur            x0, [fp, #-0x28]
    // 0xbe338c: LoadField: r1 = r0->field_f
    //     0xbe338c: ldur            w1, [x0, #0xf]
    // 0xbe3390: DecompressPointer r1
    //     0xbe3390: add             x1, x1, HEAP, lsl #32
    // 0xbe3394: LoadField: r3 = r1->field_b
    //     0xbe3394: ldur            w3, [x1, #0xb]
    // 0xbe3398: DecompressPointer r3
    //     0xbe3398: add             x3, x3, HEAP, lsl #32
    // 0xbe339c: cmp             w3, NULL
    // 0xbe33a0: b.eq            #0xbe3838
    // 0xbe33a4: LoadField: r0 = r3->field_f
    //     0xbe33a4: ldur            w0, [x3, #0xf]
    // 0xbe33a8: DecompressPointer r0
    //     0xbe33a8: add             x0, x0, HEAP, lsl #32
    // 0xbe33ac: StoreField: r2->field_1b = r0
    //     0xbe33ac: stur            w0, [x2, #0x1b]
    // 0xbe33b0: r16 = "sku_id"
    //     0xbe33b0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xbe33b4: ldr             x16, [x16, #0x498]
    // 0xbe33b8: StoreField: r2->field_1f = r16
    //     0xbe33b8: stur            w16, [x2, #0x1f]
    // 0xbe33bc: LoadField: r0 = r3->field_13
    //     0xbe33bc: ldur            w0, [x3, #0x13]
    // 0xbe33c0: DecompressPointer r0
    //     0xbe33c0: add             x0, x0, HEAP, lsl #32
    // 0xbe33c4: StoreField: r2->field_23 = r0
    //     0xbe33c4: stur            w0, [x2, #0x23]
    // 0xbe33c8: r16 = "quantity"
    //     0xbe33c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbe33cc: ldr             x16, [x16, #0x428]
    // 0xbe33d0: StoreField: r2->field_27 = r16
    //     0xbe33d0: stur            w16, [x2, #0x27]
    // 0xbe33d4: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbe33d4: ldur            x4, [x3, #0x17]
    // 0xbe33d8: r0 = BoxInt64Instr(r4)
    //     0xbe33d8: sbfiz           x0, x4, #1, #0x1f
    //     0xbe33dc: cmp             x4, x0, asr #1
    //     0xbe33e0: b.eq            #0xbe33ec
    //     0xbe33e4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe33e8: stur            x4, [x0, #7]
    // 0xbe33ec: mov             x1, x2
    // 0xbe33f0: ArrayStore: r1[7] = r0  ; List_4
    //     0xbe33f0: add             x25, x1, #0x2b
    //     0xbe33f4: str             w0, [x25]
    //     0xbe33f8: tbz             w0, #0, #0xbe3414
    //     0xbe33fc: ldurb           w16, [x1, #-1]
    //     0xbe3400: ldurb           w17, [x0, #-1]
    //     0xbe3404: and             x16, x17, x16, lsr #2
    //     0xbe3408: tst             x16, HEAP, lsr #32
    //     0xbe340c: b.eq            #0xbe3414
    //     0xbe3410: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3414: r16 = "checkout_event_data"
    //     0xbe3414: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xbe3418: ldr             x16, [x16, #0xd50]
    // 0xbe341c: StoreField: r2->field_2f = r16
    //     0xbe341c: stur            w16, [x2, #0x2f]
    // 0xbe3420: mov             x1, x2
    // 0xbe3424: ldur            x0, [fp, #-0x38]
    // 0xbe3428: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe3428: add             x25, x1, #0x33
    //     0xbe342c: str             w0, [x25]
    //     0xbe3430: tbz             w0, #0, #0xbe344c
    //     0xbe3434: ldurb           w16, [x1, #-1]
    //     0xbe3438: ldurb           w17, [x0, #-1]
    //     0xbe343c: and             x16, x17, x16, lsr #2
    //     0xbe3440: tst             x16, HEAP, lsr #32
    //     0xbe3444: b.eq            #0xbe344c
    //     0xbe3448: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe344c: r16 = "previousScreenSource"
    //     0xbe344c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xbe3450: ldr             x16, [x16, #0x448]
    // 0xbe3454: StoreField: r2->field_37 = r16
    //     0xbe3454: stur            w16, [x2, #0x37]
    // 0xbe3458: r16 = "product_page"
    //     0xbe3458: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe345c: ldr             x16, [x16, #0x480]
    // 0xbe3460: StoreField: r2->field_3b = r16
    //     0xbe3460: stur            w16, [x2, #0x3b]
    // 0xbe3464: r16 = "is_skipped_address"
    //     0xbe3464: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xbe3468: ldr             x16, [x16, #0xb80]
    // 0xbe346c: StoreField: r2->field_3f = r16
    //     0xbe346c: stur            w16, [x2, #0x3f]
    // 0xbe3470: r16 = true
    //     0xbe3470: add             x16, NULL, #0x20  ; true
    // 0xbe3474: StoreField: r2->field_43 = r16
    //     0xbe3474: stur            w16, [x2, #0x43]
    // 0xbe3478: r16 = "coming_from"
    //     0xbe3478: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xbe347c: ldr             x16, [x16, #0x328]
    // 0xbe3480: StoreField: r2->field_47 = r16
    //     0xbe3480: stur            w16, [x2, #0x47]
    // 0xbe3484: LoadField: r0 = r3->field_2f
    //     0xbe3484: ldur            w0, [x3, #0x2f]
    // 0xbe3488: DecompressPointer r0
    //     0xbe3488: add             x0, x0, HEAP, lsl #32
    // 0xbe348c: mov             x1, x2
    // 0xbe3490: ArrayStore: r1[15] = r0  ; List_4
    //     0xbe3490: add             x25, x1, #0x4b
    //     0xbe3494: str             w0, [x25]
    //     0xbe3498: tbz             w0, #0, #0xbe34b4
    //     0xbe349c: ldurb           w16, [x1, #-1]
    //     0xbe34a0: ldurb           w17, [x0, #-1]
    //     0xbe34a4: and             x16, x17, x16, lsr #2
    //     0xbe34a8: tst             x16, HEAP, lsr #32
    //     0xbe34ac: b.eq            #0xbe34b4
    //     0xbe34b0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe34b4: r16 = "checkout_id"
    //     0xbe34b4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xbe34b8: ldr             x16, [x16, #0xb88]
    // 0xbe34bc: StoreField: r2->field_4f = r16
    //     0xbe34bc: stur            w16, [x2, #0x4f]
    // 0xbe34c0: ldur            x0, [fp, #-0x10]
    // 0xbe34c4: LoadField: r1 = r0->field_f
    //     0xbe34c4: ldur            w1, [x0, #0xf]
    // 0xbe34c8: DecompressPointer r1
    //     0xbe34c8: add             x1, x1, HEAP, lsl #32
    // 0xbe34cc: r16 = Sentinel
    //     0xbe34cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe34d0: cmp             w1, w16
    // 0xbe34d4: b.eq            #0xbe3808
    // 0xbe34d8: str             x1, [SP]
    // 0xbe34dc: r4 = 0
    //     0xbe34dc: movz            x4, #0
    // 0xbe34e0: ldr             x0, [SP]
    // 0xbe34e4: r16 = UnlinkedCall_0x613b5c
    //     0xbe34e4: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c238] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe34e8: add             x16, x16, #0x238
    // 0xbe34ec: ldp             x5, lr, [x16]
    // 0xbe34f0: blr             lr
    // 0xbe34f4: ldur            x1, [fp, #-0x18]
    // 0xbe34f8: ArrayStore: r1[17] = r0  ; List_4
    //     0xbe34f8: add             x25, x1, #0x53
    //     0xbe34fc: str             w0, [x25]
    //     0xbe3500: tbz             w0, #0, #0xbe351c
    //     0xbe3504: ldurb           w16, [x1, #-1]
    //     0xbe3508: ldurb           w17, [x0, #-1]
    //     0xbe350c: and             x16, x17, x16, lsr #2
    //     0xbe3510: tst             x16, HEAP, lsr #32
    //     0xbe3514: b.eq            #0xbe351c
    //     0xbe3518: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe351c: ldur            x1, [fp, #-0x18]
    // 0xbe3520: r16 = "user_data"
    //     0xbe3520: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xbe3524: ldr             x16, [x16, #0x58]
    // 0xbe3528: StoreField: r1->field_57 = r16
    //     0xbe3528: stur            w16, [x1, #0x57]
    // 0xbe352c: ldur            x0, [fp, #-0x10]
    // 0xbe3530: LoadField: r2 = r0->field_f
    //     0xbe3530: ldur            w2, [x0, #0xf]
    // 0xbe3534: DecompressPointer r2
    //     0xbe3534: add             x2, x2, HEAP, lsl #32
    // 0xbe3538: r16 = Sentinel
    //     0xbe3538: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe353c: cmp             w2, w16
    // 0xbe3540: b.eq            #0xbe3818
    // 0xbe3544: str             x2, [SP]
    // 0xbe3548: r4 = 0
    //     0xbe3548: movz            x4, #0
    // 0xbe354c: ldr             x0, [SP]
    // 0xbe3550: r16 = UnlinkedCall_0x613b5c
    //     0xbe3550: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c248] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe3554: add             x16, x16, #0x248
    // 0xbe3558: ldp             x5, lr, [x16]
    // 0xbe355c: blr             lr
    // 0xbe3560: ldur            x1, [fp, #-0x18]
    // 0xbe3564: ArrayStore: r1[19] = r0  ; List_4
    //     0xbe3564: add             x25, x1, #0x5b
    //     0xbe3568: str             w0, [x25]
    //     0xbe356c: tbz             w0, #0, #0xbe3588
    //     0xbe3570: ldurb           w16, [x1, #-1]
    //     0xbe3574: ldurb           w17, [x0, #-1]
    //     0xbe3578: and             x16, x17, x16, lsr #2
    //     0xbe357c: tst             x16, HEAP, lsr #32
    //     0xbe3580: b.eq            #0xbe3588
    //     0xbe3584: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3588: r16 = <String, dynamic>
    //     0xbe3588: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xbe358c: ldur            lr, [fp, #-0x18]
    // 0xbe3590: stp             lr, x16, [SP]
    // 0xbe3594: r0 = Map._fromLiteral()
    //     0xbe3594: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe3598: r16 = "/checkout_order_summary_page"
    //     0xbe3598: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xbe359c: ldr             x16, [x16, #0x9d8]
    // 0xbe35a0: stp             x16, NULL, [SP, #8]
    // 0xbe35a4: str             x0, [SP]
    // 0xbe35a8: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe35a8: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe35ac: ldr             x4, [x4, #0x438]
    // 0xbe35b0: r0 = GetNavigation.toNamed()
    //     0xbe35b0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe35b4: b               #0xbe37c0
    // 0xbe35b8: ldur            x0, [fp, #-0x10]
    // 0xbe35bc: LoadField: r2 = r0->field_b
    //     0xbe35bc: ldur            w2, [x0, #0xb]
    // 0xbe35c0: DecompressPointer r2
    //     0xbe35c0: add             x2, x2, HEAP, lsl #32
    // 0xbe35c4: stur            x2, [fp, #-0x18]
    // 0xbe35c8: LoadField: r0 = r2->field_f
    //     0xbe35c8: ldur            w0, [x2, #0xf]
    // 0xbe35cc: DecompressPointer r0
    //     0xbe35cc: add             x0, x0, HEAP, lsl #32
    // 0xbe35d0: LoadField: r1 = r0->field_b
    //     0xbe35d0: ldur            w1, [x0, #0xb]
    // 0xbe35d4: DecompressPointer r1
    //     0xbe35d4: add             x1, x1, HEAP, lsl #32
    // 0xbe35d8: cmp             w1, NULL
    // 0xbe35dc: b.eq            #0xbe383c
    // 0xbe35e0: LoadField: r3 = r1->field_f
    //     0xbe35e0: ldur            w3, [x1, #0xf]
    // 0xbe35e4: DecompressPointer r3
    //     0xbe35e4: add             x3, x3, HEAP, lsl #32
    // 0xbe35e8: stur            x3, [fp, #-0x10]
    // 0xbe35ec: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xbe35ec: ldur            x4, [x1, #0x17]
    // 0xbe35f0: stur            x4, [fp, #-0x20]
    // 0xbe35f4: LoadField: r0 = r1->field_27
    //     0xbe35f4: ldur            x0, [x1, #0x27]
    // 0xbe35f8: mul             x5, x0, x4
    // 0xbe35fc: r0 = BoxInt64Instr(r5)
    //     0xbe35fc: sbfiz           x0, x5, #1, #0x1f
    //     0xbe3600: cmp             x5, x0, asr #1
    //     0xbe3604: b.eq            #0xbe3610
    //     0xbe3608: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe360c: stur            x5, [x0, #7]
    // 0xbe3610: stp             x0, NULL, [SP]
    // 0xbe3614: r0 = _Double.fromInteger()
    //     0xbe3614: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbe3618: stur            x0, [fp, #-0x28]
    // 0xbe361c: r0 = CheckoutEventData()
    //     0xbe361c: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xbe3620: mov             x2, x0
    // 0xbe3624: ldur            x0, [fp, #-0x28]
    // 0xbe3628: stur            x2, [fp, #-0x30]
    // 0xbe362c: StoreField: r2->field_7 = r0
    //     0xbe362c: stur            w0, [x2, #7]
    // 0xbe3630: ldur            x3, [fp, #-0x20]
    // 0xbe3634: r0 = BoxInt64Instr(r3)
    //     0xbe3634: sbfiz           x0, x3, #1, #0x1f
    //     0xbe3638: cmp             x3, x0, asr #1
    //     0xbe363c: b.eq            #0xbe3648
    //     0xbe3640: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe3644: stur            x3, [x0, #7]
    // 0xbe3648: StoreField: r2->field_b = r0
    //     0xbe3648: stur            w0, [x2, #0xb]
    // 0xbe364c: ldur            x0, [fp, #-0x10]
    // 0xbe3650: StoreField: r2->field_f = r0
    //     0xbe3650: stur            w0, [x2, #0xf]
    // 0xbe3654: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe3654: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe3658: ldr             x0, [x0, #0x1c80]
    //     0xbe365c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe3660: cmp             w0, w16
    //     0xbe3664: b.ne            #0xbe3670
    //     0xbe3668: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe366c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe3670: r1 = Null
    //     0xbe3670: mov             x1, NULL
    // 0xbe3674: r2 = 28
    //     0xbe3674: movz            x2, #0x1c
    // 0xbe3678: r0 = AllocateArray()
    //     0xbe3678: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe367c: mov             x2, x0
    // 0xbe3680: r16 = "previousScreenSource"
    //     0xbe3680: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xbe3684: ldr             x16, [x16, #0x448]
    // 0xbe3688: StoreField: r2->field_f = r16
    //     0xbe3688: stur            w16, [x2, #0xf]
    // 0xbe368c: r16 = "product_page"
    //     0xbe368c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe3690: ldr             x16, [x16, #0x480]
    // 0xbe3694: StoreField: r2->field_13 = r16
    //     0xbe3694: stur            w16, [x2, #0x13]
    // 0xbe3698: r16 = "checkout_event_data"
    //     0xbe3698: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xbe369c: ldr             x16, [x16, #0xd50]
    // 0xbe36a0: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe36a0: stur            w16, [x2, #0x17]
    // 0xbe36a4: ldur            x0, [fp, #-0x30]
    // 0xbe36a8: StoreField: r2->field_1b = r0
    //     0xbe36a8: stur            w0, [x2, #0x1b]
    // 0xbe36ac: r16 = "product_id"
    //     0xbe36ac: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xbe36b0: ldr             x16, [x16, #0x9b8]
    // 0xbe36b4: StoreField: r2->field_1f = r16
    //     0xbe36b4: stur            w16, [x2, #0x1f]
    // 0xbe36b8: ldur            x0, [fp, #-0x18]
    // 0xbe36bc: LoadField: r1 = r0->field_f
    //     0xbe36bc: ldur            w1, [x0, #0xf]
    // 0xbe36c0: DecompressPointer r1
    //     0xbe36c0: add             x1, x1, HEAP, lsl #32
    // 0xbe36c4: LoadField: r3 = r1->field_b
    //     0xbe36c4: ldur            w3, [x1, #0xb]
    // 0xbe36c8: DecompressPointer r3
    //     0xbe36c8: add             x3, x3, HEAP, lsl #32
    // 0xbe36cc: cmp             w3, NULL
    // 0xbe36d0: b.eq            #0xbe3840
    // 0xbe36d4: LoadField: r0 = r3->field_f
    //     0xbe36d4: ldur            w0, [x3, #0xf]
    // 0xbe36d8: DecompressPointer r0
    //     0xbe36d8: add             x0, x0, HEAP, lsl #32
    // 0xbe36dc: StoreField: r2->field_23 = r0
    //     0xbe36dc: stur            w0, [x2, #0x23]
    // 0xbe36e0: r16 = "sku_id"
    //     0xbe36e0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xbe36e4: ldr             x16, [x16, #0x498]
    // 0xbe36e8: StoreField: r2->field_27 = r16
    //     0xbe36e8: stur            w16, [x2, #0x27]
    // 0xbe36ec: LoadField: r0 = r3->field_13
    //     0xbe36ec: ldur            w0, [x3, #0x13]
    // 0xbe36f0: DecompressPointer r0
    //     0xbe36f0: add             x0, x0, HEAP, lsl #32
    // 0xbe36f4: StoreField: r2->field_2b = r0
    //     0xbe36f4: stur            w0, [x2, #0x2b]
    // 0xbe36f8: r16 = "quantity"
    //     0xbe36f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbe36fc: ldr             x16, [x16, #0x428]
    // 0xbe3700: StoreField: r2->field_2f = r16
    //     0xbe3700: stur            w16, [x2, #0x2f]
    // 0xbe3704: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbe3704: ldur            x4, [x3, #0x17]
    // 0xbe3708: r0 = BoxInt64Instr(r4)
    //     0xbe3708: sbfiz           x0, x4, #1, #0x1f
    //     0xbe370c: cmp             x4, x0, asr #1
    //     0xbe3710: b.eq            #0xbe371c
    //     0xbe3714: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe3718: stur            x4, [x0, #7]
    // 0xbe371c: mov             x1, x2
    // 0xbe3720: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe3720: add             x25, x1, #0x33
    //     0xbe3724: str             w0, [x25]
    //     0xbe3728: tbz             w0, #0, #0xbe3744
    //     0xbe372c: ldurb           w16, [x1, #-1]
    //     0xbe3730: ldurb           w17, [x0, #-1]
    //     0xbe3734: and             x16, x17, x16, lsr #2
    //     0xbe3738: tst             x16, HEAP, lsr #32
    //     0xbe373c: b.eq            #0xbe3744
    //     0xbe3740: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3744: r16 = "coming_from"
    //     0xbe3744: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xbe3748: ldr             x16, [x16, #0x328]
    // 0xbe374c: StoreField: r2->field_37 = r16
    //     0xbe374c: stur            w16, [x2, #0x37]
    // 0xbe3750: LoadField: r0 = r3->field_2f
    //     0xbe3750: ldur            w0, [x3, #0x2f]
    // 0xbe3754: DecompressPointer r0
    //     0xbe3754: add             x0, x0, HEAP, lsl #32
    // 0xbe3758: mov             x1, x2
    // 0xbe375c: ArrayStore: r1[11] = r0  ; List_4
    //     0xbe375c: add             x25, x1, #0x3b
    //     0xbe3760: str             w0, [x25]
    //     0xbe3764: tbz             w0, #0, #0xbe3780
    //     0xbe3768: ldurb           w16, [x1, #-1]
    //     0xbe376c: ldurb           w17, [x0, #-1]
    //     0xbe3770: and             x16, x17, x16, lsr #2
    //     0xbe3774: tst             x16, HEAP, lsr #32
    //     0xbe3778: b.eq            #0xbe3780
    //     0xbe377c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3780: r16 = "is_skipped_address"
    //     0xbe3780: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xbe3784: ldr             x16, [x16, #0xb80]
    // 0xbe3788: StoreField: r2->field_3f = r16
    //     0xbe3788: stur            w16, [x2, #0x3f]
    // 0xbe378c: r16 = true
    //     0xbe378c: add             x16, NULL, #0x20  ; true
    // 0xbe3790: StoreField: r2->field_43 = r16
    //     0xbe3790: stur            w16, [x2, #0x43]
    // 0xbe3794: r16 = <String, Object?>
    //     0xbe3794: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xbe3798: ldr             x16, [x16, #0xc28]
    // 0xbe379c: stp             x2, x16, [SP]
    // 0xbe37a0: r0 = Map._fromLiteral()
    //     0xbe37a0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe37a4: r16 = "/checkout_request_number_page"
    //     0xbe37a4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xbe37a8: ldr             x16, [x16, #0x9f8]
    // 0xbe37ac: stp             x16, NULL, [SP, #8]
    // 0xbe37b0: str             x0, [SP]
    // 0xbe37b4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe37b4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe37b8: ldr             x4, [x4, #0x438]
    // 0xbe37bc: r0 = GetNavigation.toNamed()
    //     0xbe37bc: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe37c0: r0 = Null
    //     0xbe37c0: mov             x0, NULL
    // 0xbe37c4: r0 = ReturnAsyncNotFuture()
    //     0xbe37c4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xbe37c8: r16 = "controller"
    //     0xbe37c8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe37cc: str             x16, [SP]
    // 0xbe37d0: r0 = _throwLocalNotInitialized()
    //     0xbe37d0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe37d4: brk             #0
    // 0xbe37d8: r16 = "controller"
    //     0xbe37d8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe37dc: str             x16, [SP]
    // 0xbe37e0: r0 = _throwLocalNotInitialized()
    //     0xbe37e0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe37e4: brk             #0
    // 0xbe37e8: r16 = "controller"
    //     0xbe37e8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe37ec: str             x16, [SP]
    // 0xbe37f0: r0 = _throwLocalNotInitialized()
    //     0xbe37f0: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe37f4: brk             #0
    // 0xbe37f8: r16 = "controller"
    //     0xbe37f8: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe37fc: str             x16, [SP]
    // 0xbe3800: r0 = _throwLocalNotInitialized()
    //     0xbe3800: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe3804: brk             #0
    // 0xbe3808: r16 = "controller"
    //     0xbe3808: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe380c: str             x16, [SP]
    // 0xbe3810: r0 = _throwLocalNotInitialized()
    //     0xbe3810: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe3814: brk             #0
    // 0xbe3818: r16 = "controller"
    //     0xbe3818: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe381c: str             x16, [SP]
    // 0xbe3820: r0 = _throwLocalNotInitialized()
    //     0xbe3820: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe3824: brk             #0
    // 0xbe3828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe3828: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe382c: b               #0xbe2ec8
    // 0xbe3830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe3830: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe3834: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe3834: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe3838: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe3838: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe383c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe383c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe3840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe3840: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SingleExchangeProductBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xbe3844, size: 0x194
    // 0xbe3844: EnterFrame
    //     0xbe3844: stp             fp, lr, [SP, #-0x10]!
    //     0xbe3848: mov             fp, SP
    // 0xbe384c: AllocStack(0x48)
    //     0xbe384c: sub             SP, SP, #0x48
    // 0xbe3850: SetupParameters()
    //     0xbe3850: ldr             x0, [fp, #0x18]
    //     0xbe3854: ldur            w2, [x0, #0x17]
    //     0xbe3858: add             x2, x2, HEAP, lsl #32
    //     0xbe385c: stur            x2, [fp, #-0x30]
    // 0xbe3860: CheckStackOverflow
    //     0xbe3860: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe3864: cmp             SP, x16
    //     0xbe3868: b.ls            #0xbe39c8
    // 0xbe386c: LoadField: r3 = r2->field_b
    //     0xbe386c: ldur            w3, [x2, #0xb]
    // 0xbe3870: DecompressPointer r3
    //     0xbe3870: add             x3, x3, HEAP, lsl #32
    // 0xbe3874: stur            x3, [fp, #-0x28]
    // 0xbe3878: LoadField: r0 = r3->field_f
    //     0xbe3878: ldur            w0, [x3, #0xf]
    // 0xbe387c: DecompressPointer r0
    //     0xbe387c: add             x0, x0, HEAP, lsl #32
    // 0xbe3880: LoadField: r1 = r0->field_b
    //     0xbe3880: ldur            w1, [x0, #0xb]
    // 0xbe3884: DecompressPointer r1
    //     0xbe3884: add             x1, x1, HEAP, lsl #32
    // 0xbe3888: cmp             w1, NULL
    // 0xbe388c: b.eq            #0xbe39d0
    // 0xbe3890: LoadField: r4 = r1->field_47
    //     0xbe3890: ldur            w4, [x1, #0x47]
    // 0xbe3894: DecompressPointer r4
    //     0xbe3894: add             x4, x4, HEAP, lsl #32
    // 0xbe3898: stur            x4, [fp, #-0x20]
    // 0xbe389c: LoadField: r5 = r1->field_37
    //     0xbe389c: ldur            w5, [x1, #0x37]
    // 0xbe38a0: DecompressPointer r5
    //     0xbe38a0: add             x5, x5, HEAP, lsl #32
    // 0xbe38a4: stur            x5, [fp, #-0x18]
    // 0xbe38a8: LoadField: r6 = r1->field_3b
    //     0xbe38a8: ldur            w6, [x1, #0x3b]
    // 0xbe38ac: DecompressPointer r6
    //     0xbe38ac: add             x6, x6, HEAP, lsl #32
    // 0xbe38b0: stur            x6, [fp, #-0x10]
    // 0xbe38b4: LoadField: r7 = r1->field_3f
    //     0xbe38b4: ldur            w7, [x1, #0x3f]
    // 0xbe38b8: DecompressPointer r7
    //     0xbe38b8: add             x7, x7, HEAP, lsl #32
    // 0xbe38bc: stur            x7, [fp, #-8]
    // 0xbe38c0: ArrayLoad: r8 = r1[0]  ; List_8
    //     0xbe38c0: ldur            x8, [x1, #0x17]
    // 0xbe38c4: r0 = BoxInt64Instr(r8)
    //     0xbe38c4: sbfiz           x0, x8, #1, #0x1f
    //     0xbe38c8: cmp             x8, x0, asr #1
    //     0xbe38cc: b.eq            #0xbe38d8
    //     0xbe38d0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe38d4: stur            x8, [x0, #7]
    // 0xbe38d8: r1 = 60
    //     0xbe38d8: movz            x1, #0x3c
    // 0xbe38dc: branchIfSmi(r0, 0xbe38e8)
    //     0xbe38dc: tbz             w0, #0, #0xbe38e8
    // 0xbe38e0: r1 = LoadClassIdInstr(r0)
    //     0xbe38e0: ldur            x1, [x0, #-1]
    //     0xbe38e4: ubfx            x1, x1, #0xc, #0x14
    // 0xbe38e8: str             x0, [SP]
    // 0xbe38ec: mov             x0, x1
    // 0xbe38f0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xbe38f0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xbe38f4: r0 = GDT[cid_x0 + 0x2700]()
    //     0xbe38f4: movz            x17, #0x2700
    //     0xbe38f8: add             lr, x0, x17
    //     0xbe38fc: ldr             lr, [x21, lr, lsl #3]
    //     0xbe3900: blr             lr
    // 0xbe3904: mov             x1, x0
    // 0xbe3908: ldur            x0, [fp, #-0x28]
    // 0xbe390c: stur            x1, [fp, #-0x38]
    // 0xbe3910: LoadField: r2 = r0->field_f
    //     0xbe3910: ldur            w2, [x0, #0xf]
    // 0xbe3914: DecompressPointer r2
    //     0xbe3914: add             x2, x2, HEAP, lsl #32
    // 0xbe3918: LoadField: r0 = r2->field_b
    //     0xbe3918: ldur            w0, [x2, #0xb]
    // 0xbe391c: DecompressPointer r0
    //     0xbe391c: add             x0, x0, HEAP, lsl #32
    // 0xbe3920: cmp             w0, NULL
    // 0xbe3924: b.eq            #0xbe39d4
    // 0xbe3928: LoadField: r2 = r0->field_33
    //     0xbe3928: ldur            w2, [x0, #0x33]
    // 0xbe392c: DecompressPointer r2
    //     0xbe392c: add             x2, x2, HEAP, lsl #32
    // 0xbe3930: stur            x2, [fp, #-0x28]
    // 0xbe3934: r0 = SingleExchangeProductBottomSheet()
    //     0xbe3934: bl              #0xbe39d8  ; AllocateSingleExchangeProductBottomSheetStub -> SingleExchangeProductBottomSheet (size=0x34)
    // 0xbe3938: mov             x3, x0
    // 0xbe393c: ldur            x0, [fp, #-0x20]
    // 0xbe3940: stur            x3, [fp, #-0x40]
    // 0xbe3944: StoreField: r3->field_b = r0
    //     0xbe3944: stur            w0, [x3, #0xb]
    // 0xbe3948: ldur            x0, [fp, #-0x18]
    // 0xbe394c: StoreField: r3->field_f = r0
    //     0xbe394c: stur            w0, [x3, #0xf]
    // 0xbe3950: ldur            x0, [fp, #-0x10]
    // 0xbe3954: ArrayStore: r3[0] = r0  ; List_4
    //     0xbe3954: stur            w0, [x3, #0x17]
    // 0xbe3958: ldur            x0, [fp, #-8]
    // 0xbe395c: StoreField: r3->field_13 = r0
    //     0xbe395c: stur            w0, [x3, #0x13]
    // 0xbe3960: ldur            x0, [fp, #-0x38]
    // 0xbe3964: StoreField: r3->field_1b = r0
    //     0xbe3964: stur            w0, [x3, #0x1b]
    // 0xbe3968: ldur            x2, [fp, #-0x30]
    // 0xbe396c: r1 = Function '<anonymous closure>':.
    //     0xbe396c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c258] AnonymousClosure: (0xbe3ad0), in [package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xbe1674)
    //     0xbe3970: ldr             x1, [x1, #0x258]
    // 0xbe3974: r0 = AllocateClosure()
    //     0xbe3974: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe3978: mov             x1, x0
    // 0xbe397c: ldur            x0, [fp, #-0x40]
    // 0xbe3980: StoreField: r0->field_1f = r1
    //     0xbe3980: stur            w1, [x0, #0x1f]
    // 0xbe3984: ldur            x2, [fp, #-0x30]
    // 0xbe3988: r1 = Function '<anonymous closure>':.
    //     0xbe3988: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c260] AnonymousClosure: (0xbe3a04), in [package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xbe1674)
    //     0xbe398c: ldr             x1, [x1, #0x260]
    // 0xbe3990: r0 = AllocateClosure()
    //     0xbe3990: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe3994: mov             x1, x0
    // 0xbe3998: ldur            x0, [fp, #-0x40]
    // 0xbe399c: StoreField: r0->field_23 = r1
    //     0xbe399c: stur            w1, [x0, #0x23]
    // 0xbe39a0: r1 = const []
    //     0xbe39a0: add             x1, PP, #0x56, lsl #12  ; [pp+0x56490] List<ProductCustomisation>(0)
    //     0xbe39a4: ldr             x1, [x1, #0x490]
    // 0xbe39a8: StoreField: r0->field_2b = r1
    //     0xbe39a8: stur            w1, [x0, #0x2b]
    // 0xbe39ac: r1 = ""
    //     0xbe39ac: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe39b0: StoreField: r0->field_27 = r1
    //     0xbe39b0: stur            w1, [x0, #0x27]
    // 0xbe39b4: ldur            x1, [fp, #-0x28]
    // 0xbe39b8: StoreField: r0->field_2f = r1
    //     0xbe39b8: stur            w1, [x0, #0x2f]
    // 0xbe39bc: LeaveFrame
    //     0xbe39bc: mov             SP, fp
    //     0xbe39c0: ldp             fp, lr, [SP], #0x10
    // 0xbe39c4: ret
    //     0xbe39c4: ret             
    // 0xbe39c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe39c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe39cc: b               #0xbe386c
    // 0xbe39d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe39d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe39d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe39d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xbe3a04, size: 0xcc
    // 0xbe3a04: EnterFrame
    //     0xbe3a04: stp             fp, lr, [SP, #-0x10]!
    //     0xbe3a08: mov             fp, SP
    // 0xbe3a0c: AllocStack(0x20)
    //     0xbe3a0c: sub             SP, SP, #0x20
    // 0xbe3a10: SetupParameters()
    //     0xbe3a10: ldr             x0, [fp, #0x10]
    //     0xbe3a14: ldur            w2, [x0, #0x17]
    //     0xbe3a18: add             x2, x2, HEAP, lsl #32
    //     0xbe3a1c: stur            x2, [fp, #-8]
    // 0xbe3a20: CheckStackOverflow
    //     0xbe3a20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe3a24: cmp             SP, x16
    //     0xbe3a28: b.ls            #0xbe3ac8
    // 0xbe3a2c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe3a2c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe3a30: ldr             x0, [x0, #0x1c80]
    //     0xbe3a34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe3a38: cmp             w0, w16
    //     0xbe3a3c: b.ne            #0xbe3a48
    //     0xbe3a40: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe3a44: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe3a48: str             NULL, [SP]
    // 0xbe3a4c: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe3a4c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe3a50: r0 = GetNavigation.back()
    //     0xbe3a50: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbe3a54: r16 = PreferenceManager
    //     0xbe3a54: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xbe3a58: ldr             x16, [x16, #0x878]
    // 0xbe3a5c: str             x16, [SP]
    // 0xbe3a60: r0 = toString()
    //     0xbe3a60: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xbe3a64: r16 = <PreferenceManager>
    //     0xbe3a64: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xbe3a68: ldr             x16, [x16, #0x880]
    // 0xbe3a6c: stp             x0, x16, [SP]
    // 0xbe3a70: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xbe3a70: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xbe3a74: r0 = Inst.find()
    //     0xbe3a74: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbe3a78: mov             x1, x0
    // 0xbe3a7c: r2 = "token"
    //     0xbe3a7c: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xbe3a80: ldr             x2, [x2, #0x958]
    // 0xbe3a84: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbe3a84: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbe3a88: r0 = getString()
    //     0xbe3a88: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xbe3a8c: ldur            x2, [fp, #-8]
    // 0xbe3a90: r1 = Function '<anonymous closure>':.
    //     0xbe3a90: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c268] AnonymousClosure: (0xa48ad4), in [package:customer_app/app/presentation/views/glass/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xb61650)
    //     0xbe3a94: ldr             x1, [x1, #0x268]
    // 0xbe3a98: stur            x0, [fp, #-8]
    // 0xbe3a9c: r0 = AllocateClosure()
    //     0xbe3a9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe3aa0: r16 = <Null?>
    //     0xbe3aa0: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xbe3aa4: ldur            lr, [fp, #-8]
    // 0xbe3aa8: stp             lr, x16, [SP, #8]
    // 0xbe3aac: str             x0, [SP]
    // 0xbe3ab0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbe3ab0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbe3ab4: r0 = then()
    //     0xbe3ab4: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xbe3ab8: r0 = Null
    //     0xbe3ab8: mov             x0, NULL
    // 0xbe3abc: LeaveFrame
    //     0xbe3abc: mov             SP, fp
    //     0xbe3ac0: ldp             fp, lr, [SP], #0x10
    // 0xbe3ac4: ret
    //     0xbe3ac4: ret             
    // 0xbe3ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe3ac8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe3acc: b               #0xbe3a2c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xbe3ad0, size: 0xcc
    // 0xbe3ad0: EnterFrame
    //     0xbe3ad0: stp             fp, lr, [SP, #-0x10]!
    //     0xbe3ad4: mov             fp, SP
    // 0xbe3ad8: AllocStack(0x20)
    //     0xbe3ad8: sub             SP, SP, #0x20
    // 0xbe3adc: SetupParameters()
    //     0xbe3adc: ldr             x0, [fp, #0x10]
    //     0xbe3ae0: ldur            w2, [x0, #0x17]
    //     0xbe3ae4: add             x2, x2, HEAP, lsl #32
    //     0xbe3ae8: stur            x2, [fp, #-8]
    // 0xbe3aec: CheckStackOverflow
    //     0xbe3aec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe3af0: cmp             SP, x16
    //     0xbe3af4: b.ls            #0xbe3b94
    // 0xbe3af8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe3af8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe3afc: ldr             x0, [x0, #0x1c80]
    //     0xbe3b00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe3b04: cmp             w0, w16
    //     0xbe3b08: b.ne            #0xbe3b14
    //     0xbe3b0c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe3b10: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe3b14: str             NULL, [SP]
    // 0xbe3b18: r4 = const [0x1, 0, 0, 0, null]
    //     0xbe3b18: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbe3b1c: r0 = GetNavigation.back()
    //     0xbe3b1c: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbe3b20: r16 = PreferenceManager
    //     0xbe3b20: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0xbe3b24: ldr             x16, [x16, #0x878]
    // 0xbe3b28: str             x16, [SP]
    // 0xbe3b2c: r0 = toString()
    //     0xbe3b2c: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0xbe3b30: r16 = <PreferenceManager>
    //     0xbe3b30: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0xbe3b34: ldr             x16, [x16, #0x880]
    // 0xbe3b38: stp             x0, x16, [SP]
    // 0xbe3b3c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0xbe3b3c: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0xbe3b40: r0 = Inst.find()
    //     0xbe3b40: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0xbe3b44: mov             x1, x0
    // 0xbe3b48: r2 = "token"
    //     0xbe3b48: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0xbe3b4c: ldr             x2, [x2, #0x958]
    // 0xbe3b50: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbe3b50: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbe3b54: r0 = getString()
    //     0xbe3b54: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0xbe3b58: ldur            x2, [fp, #-8]
    // 0xbe3b5c: r1 = Function '<anonymous closure>':.
    //     0xbe3b5c: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c270] AnonymousClosure: (0xbe3b9c), in [package:customer_app/app/presentation/views/line/home/<USER>/customized_bottom_sheet.dart] _CustomizedBottomSheetState::build (0xbe1674)
    //     0xbe3b60: ldr             x1, [x1, #0x270]
    // 0xbe3b64: stur            x0, [fp, #-8]
    // 0xbe3b68: r0 = AllocateClosure()
    //     0xbe3b68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbe3b6c: r16 = <Null?>
    //     0xbe3b6c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0xbe3b70: ldur            lr, [fp, #-8]
    // 0xbe3b74: stp             lr, x16, [SP, #8]
    // 0xbe3b78: str             x0, [SP]
    // 0xbe3b7c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbe3b7c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbe3b80: r0 = then()
    //     0xbe3b80: bl              #0x167b220  ; [dart:async] _Future::then
    // 0xbe3b84: r0 = Null
    //     0xbe3b84: mov             x0, NULL
    // 0xbe3b88: LeaveFrame
    //     0xbe3b88: mov             SP, fp
    //     0xbe3b8c: ldp             fp, lr, [SP], #0x10
    // 0xbe3b90: ret
    //     0xbe3b90: ret             
    // 0xbe3b94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe3b94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe3b98: b               #0xbe3af8
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0xbe3b9c, size: 0x9f8
    // 0xbe3b9c: EnterFrame
    //     0xbe3b9c: stp             fp, lr, [SP, #-0x10]!
    //     0xbe3ba0: mov             fp, SP
    // 0xbe3ba4: AllocStack(0x50)
    //     0xbe3ba4: sub             SP, SP, #0x50
    // 0xbe3ba8: SetupParameters(_CustomizedBottomSheetState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xbe3ba8: stur            NULL, [fp, #-8]
    //     0xbe3bac: movz            x0, #0
    //     0xbe3bb0: add             x1, fp, w0, sxtw #2
    //     0xbe3bb4: ldr             x1, [x1, #0x18]
    //     0xbe3bb8: add             x2, fp, w0, sxtw #2
    //     0xbe3bbc: ldr             x2, [x2, #0x10]
    //     0xbe3bc0: stur            x2, [fp, #-0x18]
    //     0xbe3bc4: ldur            w3, [x1, #0x17]
    //     0xbe3bc8: add             x3, x3, HEAP, lsl #32
    //     0xbe3bcc: stur            x3, [fp, #-0x10]
    // 0xbe3bd0: CheckStackOverflow
    //     0xbe3bd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbe3bd4: cmp             SP, x16
    //     0xbe3bd8: b.ls            #0xbe4578
    // 0xbe3bdc: InitAsync() -> Future<Null?>
    //     0xbe3bdc: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0xbe3be0: bl              #0x6326e0  ; InitAsyncStub
    // 0xbe3be4: ldur            x0, [fp, #-0x18]
    // 0xbe3be8: r1 = LoadClassIdInstr(r0)
    //     0xbe3be8: ldur            x1, [x0, #-1]
    //     0xbe3bec: ubfx            x1, x1, #0xc, #0x14
    // 0xbe3bf0: r16 = ""
    //     0xbe3bf0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe3bf4: stp             x16, x0, [SP]
    // 0xbe3bf8: mov             x0, x1
    // 0xbe3bfc: mov             lr, x0
    // 0xbe3c00: ldr             lr, [x21, lr, lsl #3]
    // 0xbe3c04: blr             lr
    // 0xbe3c08: tbz             w0, #4, #0xbe42d0
    // 0xbe3c0c: ldur            x2, [fp, #-0x10]
    // 0xbe3c10: LoadField: r3 = r2->field_b
    //     0xbe3c10: ldur            w3, [x2, #0xb]
    // 0xbe3c14: DecompressPointer r3
    //     0xbe3c14: add             x3, x3, HEAP, lsl #32
    // 0xbe3c18: stur            x3, [fp, #-0x28]
    // 0xbe3c1c: LoadField: r0 = r3->field_f
    //     0xbe3c1c: ldur            w0, [x3, #0xf]
    // 0xbe3c20: DecompressPointer r0
    //     0xbe3c20: add             x0, x0, HEAP, lsl #32
    // 0xbe3c24: LoadField: r1 = r0->field_b
    //     0xbe3c24: ldur            w1, [x0, #0xb]
    // 0xbe3c28: DecompressPointer r1
    //     0xbe3c28: add             x1, x1, HEAP, lsl #32
    // 0xbe3c2c: cmp             w1, NULL
    // 0xbe3c30: b.eq            #0xbe4580
    // 0xbe3c34: LoadField: r4 = r1->field_f
    //     0xbe3c34: ldur            w4, [x1, #0xf]
    // 0xbe3c38: DecompressPointer r4
    //     0xbe3c38: add             x4, x4, HEAP, lsl #32
    // 0xbe3c3c: stur            x4, [fp, #-0x18]
    // 0xbe3c40: ArrayLoad: r5 = r1[0]  ; List_8
    //     0xbe3c40: ldur            x5, [x1, #0x17]
    // 0xbe3c44: stur            x5, [fp, #-0x20]
    // 0xbe3c48: LoadField: r0 = r1->field_27
    //     0xbe3c48: ldur            x0, [x1, #0x27]
    // 0xbe3c4c: mul             x6, x0, x5
    // 0xbe3c50: r0 = BoxInt64Instr(r6)
    //     0xbe3c50: sbfiz           x0, x6, #1, #0x1f
    //     0xbe3c54: cmp             x6, x0, asr #1
    //     0xbe3c58: b.eq            #0xbe3c64
    //     0xbe3c5c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe3c60: stur            x6, [x0, #7]
    // 0xbe3c64: stp             x0, NULL, [SP]
    // 0xbe3c68: r0 = _Double.fromInteger()
    //     0xbe3c68: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbe3c6c: stur            x0, [fp, #-0x30]
    // 0xbe3c70: r0 = CheckoutEventData()
    //     0xbe3c70: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xbe3c74: mov             x2, x0
    // 0xbe3c78: ldur            x0, [fp, #-0x30]
    // 0xbe3c7c: stur            x2, [fp, #-0x38]
    // 0xbe3c80: StoreField: r2->field_7 = r0
    //     0xbe3c80: stur            w0, [x2, #7]
    // 0xbe3c84: ldur            x3, [fp, #-0x20]
    // 0xbe3c88: r0 = BoxInt64Instr(r3)
    //     0xbe3c88: sbfiz           x0, x3, #1, #0x1f
    //     0xbe3c8c: cmp             x3, x0, asr #1
    //     0xbe3c90: b.eq            #0xbe3c9c
    //     0xbe3c94: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe3c98: stur            x3, [x0, #7]
    // 0xbe3c9c: StoreField: r2->field_b = r0
    //     0xbe3c9c: stur            w0, [x2, #0xb]
    // 0xbe3ca0: ldur            x0, [fp, #-0x18]
    // 0xbe3ca4: StoreField: r2->field_f = r0
    //     0xbe3ca4: stur            w0, [x2, #0xf]
    // 0xbe3ca8: ldur            x0, [fp, #-0x10]
    // 0xbe3cac: LoadField: r1 = r0->field_f
    //     0xbe3cac: ldur            w1, [x0, #0xf]
    // 0xbe3cb0: DecompressPointer r1
    //     0xbe3cb0: add             x1, x1, HEAP, lsl #32
    // 0xbe3cb4: r16 = Sentinel
    //     0xbe3cb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe3cb8: cmp             w1, w16
    // 0xbe3cbc: b.eq            #0xbe4518
    // 0xbe3cc0: str             x1, [SP]
    // 0xbe3cc4: r4 = 0
    //     0xbe3cc4: movz            x4, #0
    // 0xbe3cc8: ldr             x0, [SP]
    // 0xbe3ccc: r16 = UnlinkedCall_0x613b5c
    //     0xbe3ccc: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c278] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe3cd0: add             x16, x16, #0x278
    // 0xbe3cd4: ldp             x5, lr, [x16]
    // 0xbe3cd8: blr             lr
    // 0xbe3cdc: LoadField: r1 = r0->field_1b
    //     0xbe3cdc: ldur            w1, [x0, #0x1b]
    // 0xbe3ce0: DecompressPointer r1
    //     0xbe3ce0: add             x1, x1, HEAP, lsl #32
    // 0xbe3ce4: cmp             w1, NULL
    // 0xbe3ce8: b.ne            #0xbe3cf4
    // 0xbe3cec: r0 = Null
    //     0xbe3cec: mov             x0, NULL
    // 0xbe3cf0: b               #0xbe3d0c
    // 0xbe3cf4: LoadField: r0 = r1->field_b
    //     0xbe3cf4: ldur            w0, [x1, #0xb]
    // 0xbe3cf8: cbz             w0, #0xbe3d04
    // 0xbe3cfc: r1 = false
    //     0xbe3cfc: add             x1, NULL, #0x30  ; false
    // 0xbe3d00: b               #0xbe3d08
    // 0xbe3d04: r1 = true
    //     0xbe3d04: add             x1, NULL, #0x20  ; true
    // 0xbe3d08: mov             x0, x1
    // 0xbe3d0c: cmp             w0, NULL
    // 0xbe3d10: b.eq            #0xbe3d18
    // 0xbe3d14: tbz             w0, #4, #0xbe3dc0
    // 0xbe3d18: ldur            x0, [fp, #-0x10]
    // 0xbe3d1c: LoadField: r1 = r0->field_f
    //     0xbe3d1c: ldur            w1, [x0, #0xf]
    // 0xbe3d20: DecompressPointer r1
    //     0xbe3d20: add             x1, x1, HEAP, lsl #32
    // 0xbe3d24: r16 = Sentinel
    //     0xbe3d24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe3d28: cmp             w1, w16
    // 0xbe3d2c: b.eq            #0xbe4528
    // 0xbe3d30: str             x1, [SP]
    // 0xbe3d34: r4 = 0
    //     0xbe3d34: movz            x4, #0
    // 0xbe3d38: ldr             x0, [SP]
    // 0xbe3d3c: r16 = UnlinkedCall_0x613b5c
    //     0xbe3d3c: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c288] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe3d40: add             x16, x16, #0x288
    // 0xbe3d44: ldp             x5, lr, [x16]
    // 0xbe3d48: blr             lr
    // 0xbe3d4c: LoadField: r1 = r0->field_1b
    //     0xbe3d4c: ldur            w1, [x0, #0x1b]
    // 0xbe3d50: DecompressPointer r1
    //     0xbe3d50: add             x1, x1, HEAP, lsl #32
    // 0xbe3d54: cmp             w1, NULL
    // 0xbe3d58: b.ne            #0xbe3d64
    // 0xbe3d5c: r0 = Null
    //     0xbe3d5c: mov             x0, NULL
    // 0xbe3d60: b               #0xbe3da8
    // 0xbe3d64: r0 = first()
    //     0xbe3d64: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0xbe3d68: cmp             w0, NULL
    // 0xbe3d6c: b.ne            #0xbe3d78
    // 0xbe3d70: r0 = Null
    //     0xbe3d70: mov             x0, NULL
    // 0xbe3d74: b               #0xbe3da8
    // 0xbe3d78: LoadField: r1 = r0->field_13
    //     0xbe3d78: ldur            w1, [x0, #0x13]
    // 0xbe3d7c: DecompressPointer r1
    //     0xbe3d7c: add             x1, x1, HEAP, lsl #32
    // 0xbe3d80: cmp             w1, NULL
    // 0xbe3d84: b.ne            #0xbe3d90
    // 0xbe3d88: r0 = Null
    //     0xbe3d88: mov             x0, NULL
    // 0xbe3d8c: b               #0xbe3da8
    // 0xbe3d90: LoadField: r0 = r1->field_7
    //     0xbe3d90: ldur            w0, [x1, #7]
    // 0xbe3d94: cbz             w0, #0xbe3da0
    // 0xbe3d98: r1 = false
    //     0xbe3d98: add             x1, NULL, #0x30  ; false
    // 0xbe3d9c: b               #0xbe3da4
    // 0xbe3da0: r1 = true
    //     0xbe3da0: add             x1, NULL, #0x20  ; true
    // 0xbe3da4: mov             x0, x1
    // 0xbe3da8: cmp             w0, NULL
    // 0xbe3dac: b.ne            #0xbe3dbc
    // 0xbe3db0: ldur            x2, [fp, #-0x10]
    // 0xbe3db4: ldur            x0, [fp, #-0x28]
    // 0xbe3db8: b               #0xbe4050
    // 0xbe3dbc: tbnz            w0, #4, #0xbe4048
    // 0xbe3dc0: ldur            x0, [fp, #-0x10]
    // 0xbe3dc4: ldur            x1, [fp, #-0x28]
    // 0xbe3dc8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe3dc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe3dcc: ldr             x0, [x0, #0x1c80]
    //     0xbe3dd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe3dd4: cmp             w0, w16
    //     0xbe3dd8: b.ne            #0xbe3de4
    //     0xbe3ddc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe3de0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe3de4: r1 = Null
    //     0xbe3de4: mov             x1, NULL
    // 0xbe3de8: r2 = 40
    //     0xbe3de8: movz            x2, #0x28
    // 0xbe3dec: r0 = AllocateArray()
    //     0xbe3dec: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe3df0: mov             x2, x0
    // 0xbe3df4: stur            x2, [fp, #-0x18]
    // 0xbe3df8: r16 = "couponCode"
    //     0xbe3df8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xbe3dfc: ldr             x16, [x16, #0x310]
    // 0xbe3e00: StoreField: r2->field_f = r16
    //     0xbe3e00: stur            w16, [x2, #0xf]
    // 0xbe3e04: r16 = ""
    //     0xbe3e04: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe3e08: StoreField: r2->field_13 = r16
    //     0xbe3e08: stur            w16, [x2, #0x13]
    // 0xbe3e0c: r16 = "product_id"
    //     0xbe3e0c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xbe3e10: ldr             x16, [x16, #0x9b8]
    // 0xbe3e14: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe3e14: stur            w16, [x2, #0x17]
    // 0xbe3e18: ldur            x0, [fp, #-0x28]
    // 0xbe3e1c: LoadField: r1 = r0->field_f
    //     0xbe3e1c: ldur            w1, [x0, #0xf]
    // 0xbe3e20: DecompressPointer r1
    //     0xbe3e20: add             x1, x1, HEAP, lsl #32
    // 0xbe3e24: LoadField: r3 = r1->field_b
    //     0xbe3e24: ldur            w3, [x1, #0xb]
    // 0xbe3e28: DecompressPointer r3
    //     0xbe3e28: add             x3, x3, HEAP, lsl #32
    // 0xbe3e2c: cmp             w3, NULL
    // 0xbe3e30: b.eq            #0xbe4584
    // 0xbe3e34: LoadField: r0 = r3->field_f
    //     0xbe3e34: ldur            w0, [x3, #0xf]
    // 0xbe3e38: DecompressPointer r0
    //     0xbe3e38: add             x0, x0, HEAP, lsl #32
    // 0xbe3e3c: StoreField: r2->field_1b = r0
    //     0xbe3e3c: stur            w0, [x2, #0x1b]
    // 0xbe3e40: r16 = "sku_id"
    //     0xbe3e40: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xbe3e44: ldr             x16, [x16, #0x498]
    // 0xbe3e48: StoreField: r2->field_1f = r16
    //     0xbe3e48: stur            w16, [x2, #0x1f]
    // 0xbe3e4c: LoadField: r0 = r3->field_13
    //     0xbe3e4c: ldur            w0, [x3, #0x13]
    // 0xbe3e50: DecompressPointer r0
    //     0xbe3e50: add             x0, x0, HEAP, lsl #32
    // 0xbe3e54: StoreField: r2->field_23 = r0
    //     0xbe3e54: stur            w0, [x2, #0x23]
    // 0xbe3e58: r16 = "quantity"
    //     0xbe3e58: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbe3e5c: ldr             x16, [x16, #0x428]
    // 0xbe3e60: StoreField: r2->field_27 = r16
    //     0xbe3e60: stur            w16, [x2, #0x27]
    // 0xbe3e64: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbe3e64: ldur            x4, [x3, #0x17]
    // 0xbe3e68: r0 = BoxInt64Instr(r4)
    //     0xbe3e68: sbfiz           x0, x4, #1, #0x1f
    //     0xbe3e6c: cmp             x4, x0, asr #1
    //     0xbe3e70: b.eq            #0xbe3e7c
    //     0xbe3e74: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe3e78: stur            x4, [x0, #7]
    // 0xbe3e7c: mov             x1, x2
    // 0xbe3e80: ArrayStore: r1[7] = r0  ; List_4
    //     0xbe3e80: add             x25, x1, #0x2b
    //     0xbe3e84: str             w0, [x25]
    //     0xbe3e88: tbz             w0, #0, #0xbe3ea4
    //     0xbe3e8c: ldurb           w16, [x1, #-1]
    //     0xbe3e90: ldurb           w17, [x0, #-1]
    //     0xbe3e94: and             x16, x17, x16, lsr #2
    //     0xbe3e98: tst             x16, HEAP, lsr #32
    //     0xbe3e9c: b.eq            #0xbe3ea4
    //     0xbe3ea0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3ea4: r16 = "checkout_event_data"
    //     0xbe3ea4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xbe3ea8: ldr             x16, [x16, #0xd50]
    // 0xbe3eac: StoreField: r2->field_2f = r16
    //     0xbe3eac: stur            w16, [x2, #0x2f]
    // 0xbe3eb0: mov             x1, x2
    // 0xbe3eb4: ldur            x0, [fp, #-0x38]
    // 0xbe3eb8: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe3eb8: add             x25, x1, #0x33
    //     0xbe3ebc: str             w0, [x25]
    //     0xbe3ec0: tbz             w0, #0, #0xbe3edc
    //     0xbe3ec4: ldurb           w16, [x1, #-1]
    //     0xbe3ec8: ldurb           w17, [x0, #-1]
    //     0xbe3ecc: and             x16, x17, x16, lsr #2
    //     0xbe3ed0: tst             x16, HEAP, lsr #32
    //     0xbe3ed4: b.eq            #0xbe3edc
    //     0xbe3ed8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3edc: r16 = "previousScreenSource"
    //     0xbe3edc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xbe3ee0: ldr             x16, [x16, #0x448]
    // 0xbe3ee4: StoreField: r2->field_37 = r16
    //     0xbe3ee4: stur            w16, [x2, #0x37]
    // 0xbe3ee8: r16 = "product_page"
    //     0xbe3ee8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe3eec: ldr             x16, [x16, #0x480]
    // 0xbe3ef0: StoreField: r2->field_3b = r16
    //     0xbe3ef0: stur            w16, [x2, #0x3b]
    // 0xbe3ef4: r16 = "is_skipped_address"
    //     0xbe3ef4: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xbe3ef8: ldr             x16, [x16, #0xb80]
    // 0xbe3efc: StoreField: r2->field_3f = r16
    //     0xbe3efc: stur            w16, [x2, #0x3f]
    // 0xbe3f00: r16 = true
    //     0xbe3f00: add             x16, NULL, #0x20  ; true
    // 0xbe3f04: StoreField: r2->field_43 = r16
    //     0xbe3f04: stur            w16, [x2, #0x43]
    // 0xbe3f08: r16 = "coming_from"
    //     0xbe3f08: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xbe3f0c: ldr             x16, [x16, #0x328]
    // 0xbe3f10: StoreField: r2->field_47 = r16
    //     0xbe3f10: stur            w16, [x2, #0x47]
    // 0xbe3f14: LoadField: r0 = r3->field_2f
    //     0xbe3f14: ldur            w0, [x3, #0x2f]
    // 0xbe3f18: DecompressPointer r0
    //     0xbe3f18: add             x0, x0, HEAP, lsl #32
    // 0xbe3f1c: mov             x1, x2
    // 0xbe3f20: ArrayStore: r1[15] = r0  ; List_4
    //     0xbe3f20: add             x25, x1, #0x4b
    //     0xbe3f24: str             w0, [x25]
    //     0xbe3f28: tbz             w0, #0, #0xbe3f44
    //     0xbe3f2c: ldurb           w16, [x1, #-1]
    //     0xbe3f30: ldurb           w17, [x0, #-1]
    //     0xbe3f34: and             x16, x17, x16, lsr #2
    //     0xbe3f38: tst             x16, HEAP, lsr #32
    //     0xbe3f3c: b.eq            #0xbe3f44
    //     0xbe3f40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3f44: r16 = "checkout_id"
    //     0xbe3f44: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xbe3f48: ldr             x16, [x16, #0xb88]
    // 0xbe3f4c: StoreField: r2->field_4f = r16
    //     0xbe3f4c: stur            w16, [x2, #0x4f]
    // 0xbe3f50: ldur            x0, [fp, #-0x10]
    // 0xbe3f54: LoadField: r1 = r0->field_f
    //     0xbe3f54: ldur            w1, [x0, #0xf]
    // 0xbe3f58: DecompressPointer r1
    //     0xbe3f58: add             x1, x1, HEAP, lsl #32
    // 0xbe3f5c: r16 = Sentinel
    //     0xbe3f5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe3f60: cmp             w1, w16
    // 0xbe3f64: b.eq            #0xbe4538
    // 0xbe3f68: str             x1, [SP]
    // 0xbe3f6c: r4 = 0
    //     0xbe3f6c: movz            x4, #0
    // 0xbe3f70: ldr             x0, [SP]
    // 0xbe3f74: r16 = UnlinkedCall_0x613b5c
    //     0xbe3f74: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c298] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe3f78: add             x16, x16, #0x298
    // 0xbe3f7c: ldp             x5, lr, [x16]
    // 0xbe3f80: blr             lr
    // 0xbe3f84: ldur            x1, [fp, #-0x18]
    // 0xbe3f88: ArrayStore: r1[17] = r0  ; List_4
    //     0xbe3f88: add             x25, x1, #0x53
    //     0xbe3f8c: str             w0, [x25]
    //     0xbe3f90: tbz             w0, #0, #0xbe3fac
    //     0xbe3f94: ldurb           w16, [x1, #-1]
    //     0xbe3f98: ldurb           w17, [x0, #-1]
    //     0xbe3f9c: and             x16, x17, x16, lsr #2
    //     0xbe3fa0: tst             x16, HEAP, lsr #32
    //     0xbe3fa4: b.eq            #0xbe3fac
    //     0xbe3fa8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe3fac: ldur            x1, [fp, #-0x18]
    // 0xbe3fb0: r16 = "user_data"
    //     0xbe3fb0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xbe3fb4: ldr             x16, [x16, #0x58]
    // 0xbe3fb8: StoreField: r1->field_57 = r16
    //     0xbe3fb8: stur            w16, [x1, #0x57]
    // 0xbe3fbc: ldur            x2, [fp, #-0x10]
    // 0xbe3fc0: LoadField: r0 = r2->field_f
    //     0xbe3fc0: ldur            w0, [x2, #0xf]
    // 0xbe3fc4: DecompressPointer r0
    //     0xbe3fc4: add             x0, x0, HEAP, lsl #32
    // 0xbe3fc8: r16 = Sentinel
    //     0xbe3fc8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe3fcc: cmp             w0, w16
    // 0xbe3fd0: b.eq            #0xbe4548
    // 0xbe3fd4: str             x0, [SP]
    // 0xbe3fd8: r4 = 0
    //     0xbe3fd8: movz            x4, #0
    // 0xbe3fdc: ldr             x0, [SP]
    // 0xbe3fe0: r16 = UnlinkedCall_0x613b5c
    //     0xbe3fe0: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c2a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe3fe4: add             x16, x16, #0x2a8
    // 0xbe3fe8: ldp             x5, lr, [x16]
    // 0xbe3fec: blr             lr
    // 0xbe3ff0: ldur            x1, [fp, #-0x18]
    // 0xbe3ff4: ArrayStore: r1[19] = r0  ; List_4
    //     0xbe3ff4: add             x25, x1, #0x5b
    //     0xbe3ff8: str             w0, [x25]
    //     0xbe3ffc: tbz             w0, #0, #0xbe4018
    //     0xbe4000: ldurb           w16, [x1, #-1]
    //     0xbe4004: ldurb           w17, [x0, #-1]
    //     0xbe4008: and             x16, x17, x16, lsr #2
    //     0xbe400c: tst             x16, HEAP, lsr #32
    //     0xbe4010: b.eq            #0xbe4018
    //     0xbe4014: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe4018: r16 = <String, dynamic>
    //     0xbe4018: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xbe401c: ldur            lr, [fp, #-0x18]
    // 0xbe4020: stp             lr, x16, [SP]
    // 0xbe4024: r0 = Map._fromLiteral()
    //     0xbe4024: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe4028: r16 = "/checkout_request_address_page"
    //     0xbe4028: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0xbe402c: ldr             x16, [x16, #0x9e8]
    // 0xbe4030: stp             x16, NULL, [SP, #8]
    // 0xbe4034: str             x0, [SP]
    // 0xbe4038: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe4038: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe403c: ldr             x4, [x4, #0x438]
    // 0xbe4040: r0 = GetNavigation.toNamed()
    //     0xbe4040: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe4044: b               #0xbe4510
    // 0xbe4048: ldur            x2, [fp, #-0x10]
    // 0xbe404c: ldur            x0, [fp, #-0x28]
    // 0xbe4050: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe4050: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe4054: ldr             x0, [x0, #0x1c80]
    //     0xbe4058: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe405c: cmp             w0, w16
    //     0xbe4060: b.ne            #0xbe406c
    //     0xbe4064: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe4068: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe406c: r1 = Null
    //     0xbe406c: mov             x1, NULL
    // 0xbe4070: r2 = 40
    //     0xbe4070: movz            x2, #0x28
    // 0xbe4074: r0 = AllocateArray()
    //     0xbe4074: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe4078: mov             x2, x0
    // 0xbe407c: stur            x2, [fp, #-0x18]
    // 0xbe4080: r16 = "couponCode"
    //     0xbe4080: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xbe4084: ldr             x16, [x16, #0x310]
    // 0xbe4088: StoreField: r2->field_f = r16
    //     0xbe4088: stur            w16, [x2, #0xf]
    // 0xbe408c: r16 = ""
    //     0xbe408c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe4090: StoreField: r2->field_13 = r16
    //     0xbe4090: stur            w16, [x2, #0x13]
    // 0xbe4094: r16 = "product_id"
    //     0xbe4094: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xbe4098: ldr             x16, [x16, #0x9b8]
    // 0xbe409c: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe409c: stur            w16, [x2, #0x17]
    // 0xbe40a0: ldur            x0, [fp, #-0x28]
    // 0xbe40a4: LoadField: r1 = r0->field_f
    //     0xbe40a4: ldur            w1, [x0, #0xf]
    // 0xbe40a8: DecompressPointer r1
    //     0xbe40a8: add             x1, x1, HEAP, lsl #32
    // 0xbe40ac: LoadField: r3 = r1->field_b
    //     0xbe40ac: ldur            w3, [x1, #0xb]
    // 0xbe40b0: DecompressPointer r3
    //     0xbe40b0: add             x3, x3, HEAP, lsl #32
    // 0xbe40b4: cmp             w3, NULL
    // 0xbe40b8: b.eq            #0xbe4588
    // 0xbe40bc: LoadField: r0 = r3->field_f
    //     0xbe40bc: ldur            w0, [x3, #0xf]
    // 0xbe40c0: DecompressPointer r0
    //     0xbe40c0: add             x0, x0, HEAP, lsl #32
    // 0xbe40c4: StoreField: r2->field_1b = r0
    //     0xbe40c4: stur            w0, [x2, #0x1b]
    // 0xbe40c8: r16 = "sku_id"
    //     0xbe40c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xbe40cc: ldr             x16, [x16, #0x498]
    // 0xbe40d0: StoreField: r2->field_1f = r16
    //     0xbe40d0: stur            w16, [x2, #0x1f]
    // 0xbe40d4: LoadField: r0 = r3->field_13
    //     0xbe40d4: ldur            w0, [x3, #0x13]
    // 0xbe40d8: DecompressPointer r0
    //     0xbe40d8: add             x0, x0, HEAP, lsl #32
    // 0xbe40dc: StoreField: r2->field_23 = r0
    //     0xbe40dc: stur            w0, [x2, #0x23]
    // 0xbe40e0: r16 = "quantity"
    //     0xbe40e0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbe40e4: ldr             x16, [x16, #0x428]
    // 0xbe40e8: StoreField: r2->field_27 = r16
    //     0xbe40e8: stur            w16, [x2, #0x27]
    // 0xbe40ec: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbe40ec: ldur            x4, [x3, #0x17]
    // 0xbe40f0: r0 = BoxInt64Instr(r4)
    //     0xbe40f0: sbfiz           x0, x4, #1, #0x1f
    //     0xbe40f4: cmp             x4, x0, asr #1
    //     0xbe40f8: b.eq            #0xbe4104
    //     0xbe40fc: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe4100: stur            x4, [x0, #7]
    // 0xbe4104: mov             x1, x2
    // 0xbe4108: ArrayStore: r1[7] = r0  ; List_4
    //     0xbe4108: add             x25, x1, #0x2b
    //     0xbe410c: str             w0, [x25]
    //     0xbe4110: tbz             w0, #0, #0xbe412c
    //     0xbe4114: ldurb           w16, [x1, #-1]
    //     0xbe4118: ldurb           w17, [x0, #-1]
    //     0xbe411c: and             x16, x17, x16, lsr #2
    //     0xbe4120: tst             x16, HEAP, lsr #32
    //     0xbe4124: b.eq            #0xbe412c
    //     0xbe4128: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe412c: r16 = "checkout_event_data"
    //     0xbe412c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xbe4130: ldr             x16, [x16, #0xd50]
    // 0xbe4134: StoreField: r2->field_2f = r16
    //     0xbe4134: stur            w16, [x2, #0x2f]
    // 0xbe4138: mov             x1, x2
    // 0xbe413c: ldur            x0, [fp, #-0x38]
    // 0xbe4140: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe4140: add             x25, x1, #0x33
    //     0xbe4144: str             w0, [x25]
    //     0xbe4148: tbz             w0, #0, #0xbe4164
    //     0xbe414c: ldurb           w16, [x1, #-1]
    //     0xbe4150: ldurb           w17, [x0, #-1]
    //     0xbe4154: and             x16, x17, x16, lsr #2
    //     0xbe4158: tst             x16, HEAP, lsr #32
    //     0xbe415c: b.eq            #0xbe4164
    //     0xbe4160: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe4164: r16 = "previousScreenSource"
    //     0xbe4164: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xbe4168: ldr             x16, [x16, #0x448]
    // 0xbe416c: StoreField: r2->field_37 = r16
    //     0xbe416c: stur            w16, [x2, #0x37]
    // 0xbe4170: r16 = "product_page"
    //     0xbe4170: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe4174: ldr             x16, [x16, #0x480]
    // 0xbe4178: StoreField: r2->field_3b = r16
    //     0xbe4178: stur            w16, [x2, #0x3b]
    // 0xbe417c: r16 = "is_skipped_address"
    //     0xbe417c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xbe4180: ldr             x16, [x16, #0xb80]
    // 0xbe4184: StoreField: r2->field_3f = r16
    //     0xbe4184: stur            w16, [x2, #0x3f]
    // 0xbe4188: r16 = true
    //     0xbe4188: add             x16, NULL, #0x20  ; true
    // 0xbe418c: StoreField: r2->field_43 = r16
    //     0xbe418c: stur            w16, [x2, #0x43]
    // 0xbe4190: r16 = "coming_from"
    //     0xbe4190: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xbe4194: ldr             x16, [x16, #0x328]
    // 0xbe4198: StoreField: r2->field_47 = r16
    //     0xbe4198: stur            w16, [x2, #0x47]
    // 0xbe419c: LoadField: r0 = r3->field_2f
    //     0xbe419c: ldur            w0, [x3, #0x2f]
    // 0xbe41a0: DecompressPointer r0
    //     0xbe41a0: add             x0, x0, HEAP, lsl #32
    // 0xbe41a4: mov             x1, x2
    // 0xbe41a8: ArrayStore: r1[15] = r0  ; List_4
    //     0xbe41a8: add             x25, x1, #0x4b
    //     0xbe41ac: str             w0, [x25]
    //     0xbe41b0: tbz             w0, #0, #0xbe41cc
    //     0xbe41b4: ldurb           w16, [x1, #-1]
    //     0xbe41b8: ldurb           w17, [x0, #-1]
    //     0xbe41bc: and             x16, x17, x16, lsr #2
    //     0xbe41c0: tst             x16, HEAP, lsr #32
    //     0xbe41c4: b.eq            #0xbe41cc
    //     0xbe41c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe41cc: r16 = "checkout_id"
    //     0xbe41cc: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0xbe41d0: ldr             x16, [x16, #0xb88]
    // 0xbe41d4: StoreField: r2->field_4f = r16
    //     0xbe41d4: stur            w16, [x2, #0x4f]
    // 0xbe41d8: ldur            x0, [fp, #-0x10]
    // 0xbe41dc: LoadField: r1 = r0->field_f
    //     0xbe41dc: ldur            w1, [x0, #0xf]
    // 0xbe41e0: DecompressPointer r1
    //     0xbe41e0: add             x1, x1, HEAP, lsl #32
    // 0xbe41e4: r16 = Sentinel
    //     0xbe41e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe41e8: cmp             w1, w16
    // 0xbe41ec: b.eq            #0xbe4558
    // 0xbe41f0: str             x1, [SP]
    // 0xbe41f4: r4 = 0
    //     0xbe41f4: movz            x4, #0
    // 0xbe41f8: ldr             x0, [SP]
    // 0xbe41fc: r16 = UnlinkedCall_0x613b5c
    //     0xbe41fc: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c2b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe4200: add             x16, x16, #0x2b8
    // 0xbe4204: ldp             x5, lr, [x16]
    // 0xbe4208: blr             lr
    // 0xbe420c: ldur            x1, [fp, #-0x18]
    // 0xbe4210: ArrayStore: r1[17] = r0  ; List_4
    //     0xbe4210: add             x25, x1, #0x53
    //     0xbe4214: str             w0, [x25]
    //     0xbe4218: tbz             w0, #0, #0xbe4234
    //     0xbe421c: ldurb           w16, [x1, #-1]
    //     0xbe4220: ldurb           w17, [x0, #-1]
    //     0xbe4224: and             x16, x17, x16, lsr #2
    //     0xbe4228: tst             x16, HEAP, lsr #32
    //     0xbe422c: b.eq            #0xbe4234
    //     0xbe4230: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe4234: ldur            x1, [fp, #-0x18]
    // 0xbe4238: r16 = "user_data"
    //     0xbe4238: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0xbe423c: ldr             x16, [x16, #0x58]
    // 0xbe4240: StoreField: r1->field_57 = r16
    //     0xbe4240: stur            w16, [x1, #0x57]
    // 0xbe4244: ldur            x0, [fp, #-0x10]
    // 0xbe4248: LoadField: r2 = r0->field_f
    //     0xbe4248: ldur            w2, [x0, #0xf]
    // 0xbe424c: DecompressPointer r2
    //     0xbe424c: add             x2, x2, HEAP, lsl #32
    // 0xbe4250: r16 = Sentinel
    //     0xbe4250: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbe4254: cmp             w2, w16
    // 0xbe4258: b.eq            #0xbe4568
    // 0xbe425c: str             x2, [SP]
    // 0xbe4260: r4 = 0
    //     0xbe4260: movz            x4, #0
    // 0xbe4264: ldr             x0, [SP]
    // 0xbe4268: r16 = UnlinkedCall_0x613b5c
    //     0xbe4268: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c2c8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbe426c: add             x16, x16, #0x2c8
    // 0xbe4270: ldp             x5, lr, [x16]
    // 0xbe4274: blr             lr
    // 0xbe4278: ldur            x1, [fp, #-0x18]
    // 0xbe427c: ArrayStore: r1[19] = r0  ; List_4
    //     0xbe427c: add             x25, x1, #0x5b
    //     0xbe4280: str             w0, [x25]
    //     0xbe4284: tbz             w0, #0, #0xbe42a0
    //     0xbe4288: ldurb           w16, [x1, #-1]
    //     0xbe428c: ldurb           w17, [x0, #-1]
    //     0xbe4290: and             x16, x17, x16, lsr #2
    //     0xbe4294: tst             x16, HEAP, lsr #32
    //     0xbe4298: b.eq            #0xbe42a0
    //     0xbe429c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe42a0: r16 = <String, dynamic>
    //     0xbe42a0: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0xbe42a4: ldur            lr, [fp, #-0x18]
    // 0xbe42a8: stp             lr, x16, [SP]
    // 0xbe42ac: r0 = Map._fromLiteral()
    //     0xbe42ac: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe42b0: r16 = "/checkout_order_summary_page"
    //     0xbe42b0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0xbe42b4: ldr             x16, [x16, #0x9d8]
    // 0xbe42b8: stp             x16, NULL, [SP, #8]
    // 0xbe42bc: str             x0, [SP]
    // 0xbe42c0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe42c0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe42c4: ldr             x4, [x4, #0x438]
    // 0xbe42c8: r0 = GetNavigation.toNamed()
    //     0xbe42c8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe42cc: b               #0xbe4510
    // 0xbe42d0: ldur            x0, [fp, #-0x10]
    // 0xbe42d4: LoadField: r2 = r0->field_b
    //     0xbe42d4: ldur            w2, [x0, #0xb]
    // 0xbe42d8: DecompressPointer r2
    //     0xbe42d8: add             x2, x2, HEAP, lsl #32
    // 0xbe42dc: stur            x2, [fp, #-0x18]
    // 0xbe42e0: LoadField: r0 = r2->field_f
    //     0xbe42e0: ldur            w0, [x2, #0xf]
    // 0xbe42e4: DecompressPointer r0
    //     0xbe42e4: add             x0, x0, HEAP, lsl #32
    // 0xbe42e8: LoadField: r1 = r0->field_b
    //     0xbe42e8: ldur            w1, [x0, #0xb]
    // 0xbe42ec: DecompressPointer r1
    //     0xbe42ec: add             x1, x1, HEAP, lsl #32
    // 0xbe42f0: cmp             w1, NULL
    // 0xbe42f4: b.eq            #0xbe458c
    // 0xbe42f8: LoadField: r3 = r1->field_f
    //     0xbe42f8: ldur            w3, [x1, #0xf]
    // 0xbe42fc: DecompressPointer r3
    //     0xbe42fc: add             x3, x3, HEAP, lsl #32
    // 0xbe4300: stur            x3, [fp, #-0x10]
    // 0xbe4304: ArrayLoad: r4 = r1[0]  ; List_8
    //     0xbe4304: ldur            x4, [x1, #0x17]
    // 0xbe4308: stur            x4, [fp, #-0x20]
    // 0xbe430c: LoadField: r0 = r1->field_27
    //     0xbe430c: ldur            x0, [x1, #0x27]
    // 0xbe4310: mul             x5, x0, x4
    // 0xbe4314: r0 = BoxInt64Instr(r5)
    //     0xbe4314: sbfiz           x0, x5, #1, #0x1f
    //     0xbe4318: cmp             x5, x0, asr #1
    //     0xbe431c: b.eq            #0xbe4328
    //     0xbe4320: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe4324: stur            x5, [x0, #7]
    // 0xbe4328: stp             x0, NULL, [SP]
    // 0xbe432c: r0 = _Double.fromInteger()
    //     0xbe432c: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0xbe4330: stur            x0, [fp, #-0x28]
    // 0xbe4334: r0 = CheckoutEventData()
    //     0xbe4334: bl              #0xa192d0  ; AllocateCheckoutEventDataStub -> CheckoutEventData (size=0x18)
    // 0xbe4338: mov             x2, x0
    // 0xbe433c: ldur            x0, [fp, #-0x28]
    // 0xbe4340: stur            x2, [fp, #-0x30]
    // 0xbe4344: StoreField: r2->field_7 = r0
    //     0xbe4344: stur            w0, [x2, #7]
    // 0xbe4348: ldur            x3, [fp, #-0x20]
    // 0xbe434c: r0 = BoxInt64Instr(r3)
    //     0xbe434c: sbfiz           x0, x3, #1, #0x1f
    //     0xbe4350: cmp             x3, x0, asr #1
    //     0xbe4354: b.eq            #0xbe4360
    //     0xbe4358: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe435c: stur            x3, [x0, #7]
    // 0xbe4360: StoreField: r2->field_b = r0
    //     0xbe4360: stur            w0, [x2, #0xb]
    // 0xbe4364: ldur            x0, [fp, #-0x10]
    // 0xbe4368: StoreField: r2->field_f = r0
    //     0xbe4368: stur            w0, [x2, #0xf]
    // 0xbe436c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbe436c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbe4370: ldr             x0, [x0, #0x1c80]
    //     0xbe4374: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbe4378: cmp             w0, w16
    //     0xbe437c: b.ne            #0xbe4388
    //     0xbe4380: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbe4384: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbe4388: r1 = Null
    //     0xbe4388: mov             x1, NULL
    // 0xbe438c: r2 = 32
    //     0xbe438c: movz            x2, #0x20
    // 0xbe4390: r0 = AllocateArray()
    //     0xbe4390: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbe4394: mov             x2, x0
    // 0xbe4398: r16 = "couponCode"
    //     0xbe4398: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0xbe439c: ldr             x16, [x16, #0x310]
    // 0xbe43a0: StoreField: r2->field_f = r16
    //     0xbe43a0: stur            w16, [x2, #0xf]
    // 0xbe43a4: r16 = ""
    //     0xbe43a4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbe43a8: StoreField: r2->field_13 = r16
    //     0xbe43a8: stur            w16, [x2, #0x13]
    // 0xbe43ac: r16 = "product_id"
    //     0xbe43ac: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0xbe43b0: ldr             x16, [x16, #0x9b8]
    // 0xbe43b4: ArrayStore: r2[0] = r16  ; List_4
    //     0xbe43b4: stur            w16, [x2, #0x17]
    // 0xbe43b8: ldur            x0, [fp, #-0x18]
    // 0xbe43bc: LoadField: r1 = r0->field_f
    //     0xbe43bc: ldur            w1, [x0, #0xf]
    // 0xbe43c0: DecompressPointer r1
    //     0xbe43c0: add             x1, x1, HEAP, lsl #32
    // 0xbe43c4: LoadField: r3 = r1->field_b
    //     0xbe43c4: ldur            w3, [x1, #0xb]
    // 0xbe43c8: DecompressPointer r3
    //     0xbe43c8: add             x3, x3, HEAP, lsl #32
    // 0xbe43cc: cmp             w3, NULL
    // 0xbe43d0: b.eq            #0xbe4590
    // 0xbe43d4: LoadField: r0 = r3->field_f
    //     0xbe43d4: ldur            w0, [x3, #0xf]
    // 0xbe43d8: DecompressPointer r0
    //     0xbe43d8: add             x0, x0, HEAP, lsl #32
    // 0xbe43dc: StoreField: r2->field_1b = r0
    //     0xbe43dc: stur            w0, [x2, #0x1b]
    // 0xbe43e0: r16 = "sku_id"
    //     0xbe43e0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0xbe43e4: ldr             x16, [x16, #0x498]
    // 0xbe43e8: StoreField: r2->field_1f = r16
    //     0xbe43e8: stur            w16, [x2, #0x1f]
    // 0xbe43ec: LoadField: r0 = r3->field_13
    //     0xbe43ec: ldur            w0, [x3, #0x13]
    // 0xbe43f0: DecompressPointer r0
    //     0xbe43f0: add             x0, x0, HEAP, lsl #32
    // 0xbe43f4: StoreField: r2->field_23 = r0
    //     0xbe43f4: stur            w0, [x2, #0x23]
    // 0xbe43f8: r16 = "quantity"
    //     0xbe43f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0xbe43fc: ldr             x16, [x16, #0x428]
    // 0xbe4400: StoreField: r2->field_27 = r16
    //     0xbe4400: stur            w16, [x2, #0x27]
    // 0xbe4404: ArrayLoad: r4 = r3[0]  ; List_8
    //     0xbe4404: ldur            x4, [x3, #0x17]
    // 0xbe4408: r0 = BoxInt64Instr(r4)
    //     0xbe4408: sbfiz           x0, x4, #1, #0x1f
    //     0xbe440c: cmp             x4, x0, asr #1
    //     0xbe4410: b.eq            #0xbe441c
    //     0xbe4414: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbe4418: stur            x4, [x0, #7]
    // 0xbe441c: mov             x1, x2
    // 0xbe4420: ArrayStore: r1[7] = r0  ; List_4
    //     0xbe4420: add             x25, x1, #0x2b
    //     0xbe4424: str             w0, [x25]
    //     0xbe4428: tbz             w0, #0, #0xbe4444
    //     0xbe442c: ldurb           w16, [x1, #-1]
    //     0xbe4430: ldurb           w17, [x0, #-1]
    //     0xbe4434: and             x16, x17, x16, lsr #2
    //     0xbe4438: tst             x16, HEAP, lsr #32
    //     0xbe443c: b.eq            #0xbe4444
    //     0xbe4440: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe4444: r16 = "checkout_event_data"
    //     0xbe4444: add             x16, PP, #0x11, lsl #12  ; [pp+0x11d50] "checkout_event_data"
    //     0xbe4448: ldr             x16, [x16, #0xd50]
    // 0xbe444c: StoreField: r2->field_2f = r16
    //     0xbe444c: stur            w16, [x2, #0x2f]
    // 0xbe4450: mov             x1, x2
    // 0xbe4454: ldur            x0, [fp, #-0x30]
    // 0xbe4458: ArrayStore: r1[9] = r0  ; List_4
    //     0xbe4458: add             x25, x1, #0x33
    //     0xbe445c: str             w0, [x25]
    //     0xbe4460: tbz             w0, #0, #0xbe447c
    //     0xbe4464: ldurb           w16, [x1, #-1]
    //     0xbe4468: ldurb           w17, [x0, #-1]
    //     0xbe446c: and             x16, x17, x16, lsr #2
    //     0xbe4470: tst             x16, HEAP, lsr #32
    //     0xbe4474: b.eq            #0xbe447c
    //     0xbe4478: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe447c: r16 = "previousScreenSource"
    //     0xbe447c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0xbe4480: ldr             x16, [x16, #0x448]
    // 0xbe4484: StoreField: r2->field_37 = r16
    //     0xbe4484: stur            w16, [x2, #0x37]
    // 0xbe4488: r16 = "product_page"
    //     0xbe4488: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xbe448c: ldr             x16, [x16, #0x480]
    // 0xbe4490: StoreField: r2->field_3b = r16
    //     0xbe4490: stur            w16, [x2, #0x3b]
    // 0xbe4494: r16 = "coming_from"
    //     0xbe4494: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0xbe4498: ldr             x16, [x16, #0x328]
    // 0xbe449c: StoreField: r2->field_3f = r16
    //     0xbe449c: stur            w16, [x2, #0x3f]
    // 0xbe44a0: LoadField: r0 = r3->field_2f
    //     0xbe44a0: ldur            w0, [x3, #0x2f]
    // 0xbe44a4: DecompressPointer r0
    //     0xbe44a4: add             x0, x0, HEAP, lsl #32
    // 0xbe44a8: mov             x1, x2
    // 0xbe44ac: ArrayStore: r1[13] = r0  ; List_4
    //     0xbe44ac: add             x25, x1, #0x43
    //     0xbe44b0: str             w0, [x25]
    //     0xbe44b4: tbz             w0, #0, #0xbe44d0
    //     0xbe44b8: ldurb           w16, [x1, #-1]
    //     0xbe44bc: ldurb           w17, [x0, #-1]
    //     0xbe44c0: and             x16, x17, x16, lsr #2
    //     0xbe44c4: tst             x16, HEAP, lsr #32
    //     0xbe44c8: b.eq            #0xbe44d0
    //     0xbe44cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbe44d0: r16 = "is_skipped_address"
    //     0xbe44d0: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0xbe44d4: ldr             x16, [x16, #0xb80]
    // 0xbe44d8: StoreField: r2->field_47 = r16
    //     0xbe44d8: stur            w16, [x2, #0x47]
    // 0xbe44dc: r16 = true
    //     0xbe44dc: add             x16, NULL, #0x20  ; true
    // 0xbe44e0: StoreField: r2->field_4b = r16
    //     0xbe44e0: stur            w16, [x2, #0x4b]
    // 0xbe44e4: r16 = <String, Object?>
    //     0xbe44e4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0xbe44e8: ldr             x16, [x16, #0xc28]
    // 0xbe44ec: stp             x2, x16, [SP]
    // 0xbe44f0: r0 = Map._fromLiteral()
    //     0xbe44f0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xbe44f4: r16 = "/checkout_request_number_page"
    //     0xbe44f4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0xbe44f8: ldr             x16, [x16, #0x9f8]
    // 0xbe44fc: stp             x16, NULL, [SP, #8]
    // 0xbe4500: str             x0, [SP]
    // 0xbe4504: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0xbe4504: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0xbe4508: ldr             x4, [x4, #0x438]
    // 0xbe450c: r0 = GetNavigation.toNamed()
    //     0xbe450c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0xbe4510: r0 = Null
    //     0xbe4510: mov             x0, NULL
    // 0xbe4514: r0 = ReturnAsyncNotFuture()
    //     0xbe4514: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0xbe4518: r16 = "controller"
    //     0xbe4518: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe451c: str             x16, [SP]
    // 0xbe4520: r0 = _throwLocalNotInitialized()
    //     0xbe4520: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe4524: brk             #0
    // 0xbe4528: r16 = "controller"
    //     0xbe4528: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe452c: str             x16, [SP]
    // 0xbe4530: r0 = _throwLocalNotInitialized()
    //     0xbe4530: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe4534: brk             #0
    // 0xbe4538: r16 = "controller"
    //     0xbe4538: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe453c: str             x16, [SP]
    // 0xbe4540: r0 = _throwLocalNotInitialized()
    //     0xbe4540: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe4544: brk             #0
    // 0xbe4548: r16 = "controller"
    //     0xbe4548: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe454c: str             x16, [SP]
    // 0xbe4550: r0 = _throwLocalNotInitialized()
    //     0xbe4550: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe4554: brk             #0
    // 0xbe4558: r16 = "controller"
    //     0xbe4558: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe455c: str             x16, [SP]
    // 0xbe4560: r0 = _throwLocalNotInitialized()
    //     0xbe4560: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe4564: brk             #0
    // 0xbe4568: r16 = "controller"
    //     0xbe4568: ldr             x16, [PP, #0x1518]  ; [pp+0x1518] "controller"
    // 0xbe456c: str             x16, [SP]
    // 0xbe4570: r0 = _throwLocalNotInitialized()
    //     0xbe4570: bl              #0x63871c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0xbe4574: brk             #0
    // 0xbe4578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbe4578: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbe457c: b               #0xbe3bdc
    // 0xbe4580: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4580: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe4584: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4584: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe4588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4588: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe458c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe458c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbe4590: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbe4590: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3993, size: 0x50, field offset: 0xc
//   const constructor, 
class CustomizedBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80a70, size: 0x24
    // 0xc80a70: EnterFrame
    //     0xc80a70: stp             fp, lr, [SP, #-0x10]!
    //     0xc80a74: mov             fp, SP
    // 0xc80a78: mov             x0, x1
    // 0xc80a7c: r1 = <CustomizedBottomSheet>
    //     0xc80a7c: add             x1, PP, #0x49, lsl #12  ; [pp+0x493e0] TypeArguments: <CustomizedBottomSheet>
    //     0xc80a80: ldr             x1, [x1, #0x3e0]
    // 0xc80a84: r0 = _CustomizedBottomSheetState()
    //     0xc80a84: bl              #0xc80a94  ; Allocate_CustomizedBottomSheetStateStub -> _CustomizedBottomSheetState (size=0x14)
    // 0xc80a88: LeaveFrame
    //     0xc80a88: mov             SP, fp
    //     0xc80a8c: ldp             fp, lr, [SP], #0x10
    // 0xc80a90: ret
    //     0xc80a90: ret             
  }
}
