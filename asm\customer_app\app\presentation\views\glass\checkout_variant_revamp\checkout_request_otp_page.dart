// lib: , url: package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart

// class id: 1049356, size: 0x8
class :: {
}

// class id: 4576, size: 0x14, field offset: 0x14
//   const constructor, 
class CheckoutRequestOtpPage extends BaseView<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x12eaccc, size: 0x18c
    // 0x12eaccc: EnterFrame
    //     0x12eaccc: stp             fp, lr, [SP, #-0x10]!
    //     0x12eacd0: mov             fp, SP
    // 0x12eacd4: AllocStack(0x40)
    //     0x12eacd4: sub             SP, SP, #0x40
    // 0x12eacd8: SetupParameters()
    //     0x12eacd8: ldr             x0, [fp, #0x10]
    //     0x12eacdc: ldur            w2, [x0, #0x17]
    //     0x12eace0: add             x2, x2, HEAP, lsl #32
    //     0x12eace4: stur            x2, [fp, #-8]
    // 0x12eace8: CheckStackOverflow
    //     0x12eace8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12eacec: cmp             SP, x16
    //     0x12eacf0: b.ls            #0x12eae50
    // 0x12eacf4: LoadField: r1 = r2->field_f
    //     0x12eacf4: ldur            w1, [x2, #0xf]
    // 0x12eacf8: DecompressPointer r1
    //     0x12eacf8: add             x1, x1, HEAP, lsl #32
    // 0x12eacfc: r0 = controller()
    //     0x12eacfc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12ead00: mov             x2, x0
    // 0x12ead04: ldur            x0, [fp, #-8]
    // 0x12ead08: stur            x2, [fp, #-0x10]
    // 0x12ead0c: LoadField: r1 = r0->field_f
    //     0x12ead0c: ldur            w1, [x0, #0xf]
    // 0x12ead10: DecompressPointer r1
    //     0x12ead10: add             x1, x1, HEAP, lsl #32
    // 0x12ead14: r0 = controller()
    //     0x12ead14: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12ead18: LoadField: r1 = r0->field_ab
    //     0x12ead18: ldur            w1, [x0, #0xab]
    // 0x12ead1c: DecompressPointer r1
    //     0x12ead1c: add             x1, x1, HEAP, lsl #32
    // 0x12ead20: r0 = value()
    //     0x12ead20: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12ead24: mov             x2, x0
    // 0x12ead28: ldur            x0, [fp, #-8]
    // 0x12ead2c: stur            x2, [fp, #-0x18]
    // 0x12ead30: LoadField: r1 = r0->field_f
    //     0x12ead30: ldur            w1, [x0, #0xf]
    // 0x12ead34: DecompressPointer r1
    //     0x12ead34: add             x1, x1, HEAP, lsl #32
    // 0x12ead38: r0 = controller()
    //     0x12ead38: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12ead3c: LoadField: r3 = r0->field_f3
    //     0x12ead3c: ldur            w3, [x0, #0xf3]
    // 0x12ead40: DecompressPointer r3
    //     0x12ead40: add             x3, x3, HEAP, lsl #32
    // 0x12ead44: ldur            x1, [fp, #-0x10]
    // 0x12ead48: ldur            x2, [fp, #-0x18]
    // 0x12ead4c: r0 = verifyOtp()
    //     0x12ead4c: bl              #0x12ebf14  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::verifyOtp
    // 0x12ead50: ldur            x0, [fp, #-8]
    // 0x12ead54: LoadField: r1 = r0->field_f
    //     0x12ead54: ldur            w1, [x0, #0xf]
    // 0x12ead58: DecompressPointer r1
    //     0x12ead58: add             x1, x1, HEAP, lsl #32
    // 0x12ead5c: r0 = controller()
    //     0x12ead5c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12ead60: mov             x2, x0
    // 0x12ead64: ldur            x0, [fp, #-8]
    // 0x12ead68: stur            x2, [fp, #-0x10]
    // 0x12ead6c: LoadField: r1 = r0->field_f
    //     0x12ead6c: ldur            w1, [x0, #0xf]
    // 0x12ead70: DecompressPointer r1
    //     0x12ead70: add             x1, x1, HEAP, lsl #32
    // 0x12ead74: r0 = controller()
    //     0x12ead74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12ead78: LoadField: r1 = r0->field_83
    //     0x12ead78: ldur            w1, [x0, #0x83]
    // 0x12ead7c: DecompressPointer r1
    //     0x12ead7c: add             x1, x1, HEAP, lsl #32
    // 0x12ead80: r0 = value()
    //     0x12ead80: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12ead84: r16 = "request_otp"
    //     0x12ead84: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d630] "request_otp"
    //     0x12ead88: ldr             x16, [x16, #0x630]
    // 0x12ead8c: r30 = "landing_page"
    //     0x12ead8c: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x12ead90: ldr             lr, [lr, #0x638]
    // 0x12ead94: stp             lr, x16, [SP, #0x18]
    // 0x12ead98: r16 = "page_cta"
    //     0x12ead98: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c780] "page_cta"
    //     0x12ead9c: ldr             x16, [x16, #0x780]
    // 0x12eada0: r30 = "continue"
    //     0x12eada0: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d640] "continue"
    //     0x12eada4: ldr             lr, [lr, #0x640]
    // 0x12eada8: stp             lr, x16, [SP, #8]
    // 0x12eadac: str             x0, [SP]
    // 0x12eadb0: ldur            x1, [fp, #-0x10]
    // 0x12eadb4: r2 = "checkout_widget_fill"
    //     0x12eadb4: add             x2, PP, #0x3d, lsl #12  ; [pp+0x3d5c8] "checkout_widget_fill"
    //     0x12eadb8: ldr             x2, [x2, #0x5c8]
    // 0x12eadbc: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, orderId, 0x6, pageId, 0x2, pageType, 0x3, null]
    //     0x12eadbc: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d648] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "orderId", 0x6, "pageId", 0x2, "pageType", 0x3, Null]
    //     0x12eadc0: ldr             x4, [x4, #0x648]
    // 0x12eadc4: r0 = checkoutPostEvent()
    //     0x12eadc4: bl              #0x12eb500  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::checkoutPostEvent
    // 0x12eadc8: ldur            x0, [fp, #-8]
    // 0x12eadcc: LoadField: r1 = r0->field_f
    //     0x12eadcc: ldur            w1, [x0, #0xf]
    // 0x12eadd0: DecompressPointer r1
    //     0x12eadd0: add             x1, x1, HEAP, lsl #32
    // 0x12eadd4: r0 = controller()
    //     0x12eadd4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12eadd8: mov             x2, x0
    // 0x12eaddc: ldur            x0, [fp, #-8]
    // 0x12eade0: stur            x2, [fp, #-0x10]
    // 0x12eade4: LoadField: r1 = r0->field_f
    //     0x12eade4: ldur            w1, [x0, #0xf]
    // 0x12eade8: DecompressPointer r1
    //     0x12eade8: add             x1, x1, HEAP, lsl #32
    // 0x12eadec: r0 = controller()
    //     0x12eadec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12eadf0: LoadField: r1 = r0->field_83
    //     0x12eadf0: ldur            w1, [x0, #0x83]
    // 0x12eadf4: DecompressPointer r1
    //     0x12eadf4: add             x1, x1, HEAP, lsl #32
    // 0x12eadf8: r0 = value()
    //     0x12eadf8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12eadfc: r16 = "request_otp"
    //     0x12eadfc: add             x16, PP, #0x3d, lsl #12  ; [pp+0x3d630] "request_otp"
    //     0x12eae00: ldr             x16, [x16, #0x630]
    // 0x12eae04: r30 = "landing_page"
    //     0x12eae04: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d638] "landing_page"
    //     0x12eae08: ldr             lr, [lr, #0x638]
    // 0x12eae0c: stp             lr, x16, [SP, #0x18]
    // 0x12eae10: r16 = "page_cta"
    //     0x12eae10: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c780] "page_cta"
    //     0x12eae14: ldr             x16, [x16, #0x780]
    // 0x12eae18: r30 = "continue"
    //     0x12eae18: add             lr, PP, #0x3d, lsl #12  ; [pp+0x3d640] "continue"
    //     0x12eae1c: ldr             lr, [lr, #0x640]
    // 0x12eae20: stp             lr, x16, [SP, #8]
    // 0x12eae24: str             x0, [SP]
    // 0x12eae28: ldur            x1, [fp, #-0x10]
    // 0x12eae2c: r2 = "checkout_cta_clicked"
    //     0x12eae2c: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c7b0] "checkout_cta_clicked"
    //     0x12eae30: ldr             x2, [x2, #0x7b0]
    // 0x12eae34: r4 = const [0, 0x7, 0x5, 0x2, ctaName, 0x5, ctaType, 0x4, orderId, 0x6, pageId, 0x2, pageType, 0x3, null]
    //     0x12eae34: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d648] List(15) [0, 0x7, 0x5, 0x2, "ctaName", 0x5, "ctaType", 0x4, "orderId", 0x6, "pageId", 0x2, "pageType", 0x3, Null]
    //     0x12eae38: ldr             x4, [x4, #0x648]
    // 0x12eae3c: r0 = checkoutPostEvent()
    //     0x12eae3c: bl              #0x12eb500  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::checkoutPostEvent
    // 0x12eae40: r0 = Null
    //     0x12eae40: mov             x0, NULL
    // 0x12eae44: LeaveFrame
    //     0x12eae44: mov             SP, fp
    //     0x12eae48: ldp             fp, lr, [SP], #0x10
    // 0x12eae4c: ret
    //     0x12eae4c: ret             
    // 0x12eae50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12eae50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12eae54: b               #0x12eacf4
  }
  [closure] SafeArea <anonymous closure>(dynamic) {
    // ** addr: 0x12eae58, size: 0x6a8
    // 0x12eae58: EnterFrame
    //     0x12eae58: stp             fp, lr, [SP, #-0x10]!
    //     0x12eae5c: mov             fp, SP
    // 0x12eae60: AllocStack(0x60)
    //     0x12eae60: sub             SP, SP, #0x60
    // 0x12eae64: SetupParameters()
    //     0x12eae64: ldr             x0, [fp, #0x10]
    //     0x12eae68: ldur            w2, [x0, #0x17]
    //     0x12eae6c: add             x2, x2, HEAP, lsl #32
    //     0x12eae70: stur            x2, [fp, #-8]
    // 0x12eae74: CheckStackOverflow
    //     0x12eae74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12eae78: cmp             SP, x16
    //     0x12eae7c: b.ls            #0x12eb4b4
    // 0x12eae80: LoadField: r1 = r2->field_13
    //     0x12eae80: ldur            w1, [x2, #0x13]
    // 0x12eae84: DecompressPointer r1
    //     0x12eae84: add             x1, x1, HEAP, lsl #32
    // 0x12eae88: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x12eae88: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x12eae8c: r0 = _of()
    //     0x12eae8c: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x12eae90: LoadField: r1 = r0->field_23
    //     0x12eae90: ldur            w1, [x0, #0x23]
    // 0x12eae94: DecompressPointer r1
    //     0x12eae94: add             x1, x1, HEAP, lsl #32
    // 0x12eae98: LoadField: d0 = r1->field_1f
    //     0x12eae98: ldur            d0, [x1, #0x1f]
    // 0x12eae9c: stur            d0, [fp, #-0x40]
    // 0x12eaea0: r0 = EdgeInsets()
    //     0x12eaea0: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x12eaea4: stur            x0, [fp, #-0x10]
    // 0x12eaea8: StoreField: r0->field_7 = rZR
    //     0x12eaea8: stur            xzr, [x0, #7]
    // 0x12eaeac: StoreField: r0->field_f = rZR
    //     0x12eaeac: stur            xzr, [x0, #0xf]
    // 0x12eaeb0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x12eaeb0: stur            xzr, [x0, #0x17]
    // 0x12eaeb4: ldur            d0, [fp, #-0x40]
    // 0x12eaeb8: StoreField: r0->field_1f = d0
    //     0x12eaeb8: stur            d0, [x0, #0x1f]
    // 0x12eaebc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x12eaebc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x12eaec0: ldr             x0, [x0, #0x1c80]
    //     0x12eaec4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x12eaec8: cmp             w0, w16
    //     0x12eaecc: b.ne            #0x12eaed8
    //     0x12eaed0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x12eaed4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x12eaed8: r0 = GetNavigation.width()
    //     0x12eaed8: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0x12eaedc: ldur            x2, [fp, #-8]
    // 0x12eaee0: stur            d0, [fp, #-0x40]
    // 0x12eaee4: LoadField: r1 = r2->field_13
    //     0x12eaee4: ldur            w1, [x2, #0x13]
    // 0x12eaee8: DecompressPointer r1
    //     0x12eaee8: add             x1, x1, HEAP, lsl #32
    // 0x12eaeec: r0 = of()
    //     0x12eaeec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12eaef0: LoadField: r1 = r0->field_5b
    //     0x12eaef0: ldur            w1, [x0, #0x5b]
    // 0x12eaef4: DecompressPointer r1
    //     0x12eaef4: add             x1, x1, HEAP, lsl #32
    // 0x12eaef8: r0 = LoadClassIdInstr(r1)
    //     0x12eaef8: ldur            x0, [x1, #-1]
    //     0x12eaefc: ubfx            x0, x0, #0xc, #0x14
    // 0x12eaf00: r2 = 40
    //     0x12eaf00: movz            x2, #0x28
    // 0x12eaf04: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x12eaf04: sub             lr, x0, #0xfe7
    //     0x12eaf08: ldr             lr, [x21, lr, lsl #3]
    //     0x12eaf0c: blr             lr
    // 0x12eaf10: stur            x0, [fp, #-0x18]
    // 0x12eaf14: r0 = BorderSide()
    //     0x12eaf14: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x12eaf18: mov             x1, x0
    // 0x12eaf1c: ldur            x0, [fp, #-0x18]
    // 0x12eaf20: stur            x1, [fp, #-0x20]
    // 0x12eaf24: StoreField: r1->field_7 = r0
    //     0x12eaf24: stur            w0, [x1, #7]
    // 0x12eaf28: d0 = 2.000000
    //     0x12eaf28: fmov            d0, #2.00000000
    // 0x12eaf2c: StoreField: r1->field_b = d0
    //     0x12eaf2c: stur            d0, [x1, #0xb]
    // 0x12eaf30: r0 = Instance_BorderStyle
    //     0x12eaf30: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x12eaf34: ldr             x0, [x0, #0xf68]
    // 0x12eaf38: StoreField: r1->field_13 = r0
    //     0x12eaf38: stur            w0, [x1, #0x13]
    // 0x12eaf3c: d0 = -1.000000
    //     0x12eaf3c: fmov            d0, #-1.00000000
    // 0x12eaf40: ArrayStore: r1[0] = d0  ; List_8
    //     0x12eaf40: stur            d0, [x1, #0x17]
    // 0x12eaf44: r0 = Border()
    //     0x12eaf44: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0x12eaf48: mov             x1, x0
    // 0x12eaf4c: ldur            x0, [fp, #-0x20]
    // 0x12eaf50: stur            x1, [fp, #-0x18]
    // 0x12eaf54: StoreField: r1->field_7 = r0
    //     0x12eaf54: stur            w0, [x1, #7]
    // 0x12eaf58: r0 = Instance_BorderSide
    //     0x12eaf58: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x12eaf5c: ldr             x0, [x0, #0xe20]
    // 0x12eaf60: StoreField: r1->field_b = r0
    //     0x12eaf60: stur            w0, [x1, #0xb]
    // 0x12eaf64: StoreField: r1->field_f = r0
    //     0x12eaf64: stur            w0, [x1, #0xf]
    // 0x12eaf68: StoreField: r1->field_13 = r0
    //     0x12eaf68: stur            w0, [x1, #0x13]
    // 0x12eaf6c: r0 = BoxDecoration()
    //     0x12eaf6c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x12eaf70: mov             x1, x0
    // 0x12eaf74: ldur            x0, [fp, #-0x18]
    // 0x12eaf78: stur            x1, [fp, #-0x20]
    // 0x12eaf7c: StoreField: r1->field_f = r0
    //     0x12eaf7c: stur            w0, [x1, #0xf]
    // 0x12eaf80: r0 = Instance_BoxShape
    //     0x12eaf80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x12eaf84: ldr             x0, [x0, #0x80]
    // 0x12eaf88: StoreField: r1->field_23 = r0
    //     0x12eaf88: stur            w0, [x1, #0x23]
    // 0x12eaf8c: r16 = <EdgeInsets>
    //     0x12eaf8c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x12eaf90: ldr             x16, [x16, #0xda0]
    // 0x12eaf94: r30 = Instance_EdgeInsets
    //     0x12eaf94: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x12eaf98: ldr             lr, [lr, #0x1f0]
    // 0x12eaf9c: stp             lr, x16, [SP]
    // 0x12eafa0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12eafa0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12eafa4: r0 = all()
    //     0x12eafa4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12eafa8: ldur            x2, [fp, #-8]
    // 0x12eafac: stur            x0, [fp, #-0x18]
    // 0x12eafb0: LoadField: r1 = r2->field_f
    //     0x12eafb0: ldur            w1, [x2, #0xf]
    // 0x12eafb4: DecompressPointer r1
    //     0x12eafb4: add             x1, x1, HEAP, lsl #32
    // 0x12eafb8: r0 = controller()
    //     0x12eafb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12eafbc: LoadField: r1 = r0->field_d3
    //     0x12eafbc: ldur            w1, [x0, #0xd3]
    // 0x12eafc0: DecompressPointer r1
    //     0x12eafc0: add             x1, x1, HEAP, lsl #32
    // 0x12eafc4: r0 = value()
    //     0x12eafc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12eafc8: tbnz            w0, #4, #0x12eafe8
    // 0x12eafcc: ldur            x2, [fp, #-8]
    // 0x12eafd0: LoadField: r1 = r2->field_13
    //     0x12eafd0: ldur            w1, [x2, #0x13]
    // 0x12eafd4: DecompressPointer r1
    //     0x12eafd4: add             x1, x1, HEAP, lsl #32
    // 0x12eafd8: r0 = of()
    //     0x12eafd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12eafdc: LoadField: r1 = r0->field_5b
    //     0x12eafdc: ldur            w1, [x0, #0x5b]
    // 0x12eafe0: DecompressPointer r1
    //     0x12eafe0: add             x1, x1, HEAP, lsl #32
    // 0x12eafe4: b               #0x12eaff0
    // 0x12eafe8: r1 = Instance_MaterialColor
    //     0x12eafe8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0x12eafec: ldr             x1, [x1, #0xdc0]
    // 0x12eaff0: ldur            x2, [fp, #-8]
    // 0x12eaff4: ldur            x0, [fp, #-0x18]
    // 0x12eaff8: r16 = <Color>
    //     0x12eaff8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x12eaffc: ldr             x16, [x16, #0xf80]
    // 0x12eb000: stp             x1, x16, [SP]
    // 0x12eb004: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12eb004: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12eb008: r0 = all()
    //     0x12eb008: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12eb00c: stur            x0, [fp, #-0x28]
    // 0x12eb010: r0 = Radius()
    //     0x12eb010: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x12eb014: d0 = 20.000000
    //     0x12eb014: fmov            d0, #20.00000000
    // 0x12eb018: stur            x0, [fp, #-0x30]
    // 0x12eb01c: StoreField: r0->field_7 = d0
    //     0x12eb01c: stur            d0, [x0, #7]
    // 0x12eb020: StoreField: r0->field_f = d0
    //     0x12eb020: stur            d0, [x0, #0xf]
    // 0x12eb024: r0 = BorderRadius()
    //     0x12eb024: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x12eb028: mov             x1, x0
    // 0x12eb02c: ldur            x0, [fp, #-0x30]
    // 0x12eb030: stur            x1, [fp, #-0x38]
    // 0x12eb034: StoreField: r1->field_7 = r0
    //     0x12eb034: stur            w0, [x1, #7]
    // 0x12eb038: StoreField: r1->field_b = r0
    //     0x12eb038: stur            w0, [x1, #0xb]
    // 0x12eb03c: StoreField: r1->field_f = r0
    //     0x12eb03c: stur            w0, [x1, #0xf]
    // 0x12eb040: StoreField: r1->field_13 = r0
    //     0x12eb040: stur            w0, [x1, #0x13]
    // 0x12eb044: r0 = RoundedRectangleBorder()
    //     0x12eb044: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x12eb048: mov             x1, x0
    // 0x12eb04c: ldur            x0, [fp, #-0x38]
    // 0x12eb050: StoreField: r1->field_b = r0
    //     0x12eb050: stur            w0, [x1, #0xb]
    // 0x12eb054: r0 = Instance_BorderSide
    //     0x12eb054: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x12eb058: ldr             x0, [x0, #0xe20]
    // 0x12eb05c: StoreField: r1->field_7 = r0
    //     0x12eb05c: stur            w0, [x1, #7]
    // 0x12eb060: r16 = <RoundedRectangleBorder>
    //     0x12eb060: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x12eb064: ldr             x16, [x16, #0xf78]
    // 0x12eb068: stp             x1, x16, [SP]
    // 0x12eb06c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x12eb06c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x12eb070: r0 = all()
    //     0x12eb070: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x12eb074: stur            x0, [fp, #-0x30]
    // 0x12eb078: r0 = ButtonStyle()
    //     0x12eb078: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x12eb07c: mov             x1, x0
    // 0x12eb080: ldur            x0, [fp, #-0x28]
    // 0x12eb084: stur            x1, [fp, #-0x38]
    // 0x12eb088: StoreField: r1->field_b = r0
    //     0x12eb088: stur            w0, [x1, #0xb]
    // 0x12eb08c: ldur            x0, [fp, #-0x18]
    // 0x12eb090: StoreField: r1->field_23 = r0
    //     0x12eb090: stur            w0, [x1, #0x23]
    // 0x12eb094: ldur            x0, [fp, #-0x30]
    // 0x12eb098: StoreField: r1->field_43 = r0
    //     0x12eb098: stur            w0, [x1, #0x43]
    // 0x12eb09c: r0 = TextButtonThemeData()
    //     0x12eb09c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x12eb0a0: mov             x2, x0
    // 0x12eb0a4: ldur            x0, [fp, #-0x38]
    // 0x12eb0a8: stur            x2, [fp, #-0x18]
    // 0x12eb0ac: StoreField: r2->field_7 = r0
    //     0x12eb0ac: stur            w0, [x2, #7]
    // 0x12eb0b0: ldur            x0, [fp, #-8]
    // 0x12eb0b4: LoadField: r1 = r0->field_f
    //     0x12eb0b4: ldur            w1, [x0, #0xf]
    // 0x12eb0b8: DecompressPointer r1
    //     0x12eb0b8: add             x1, x1, HEAP, lsl #32
    // 0x12eb0bc: r0 = controller()
    //     0x12eb0bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x12eb0c0: LoadField: r1 = r0->field_d3
    //     0x12eb0c0: ldur            w1, [x0, #0xd3]
    // 0x12eb0c4: DecompressPointer r1
    //     0x12eb0c4: add             x1, x1, HEAP, lsl #32
    // 0x12eb0c8: r0 = value()
    //     0x12eb0c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x12eb0cc: tbnz            w0, #4, #0x12eb0e8
    // 0x12eb0d0: ldur            x2, [fp, #-8]
    // 0x12eb0d4: r1 = Function '<anonymous closure>':.
    //     0x12eb0d4: add             x1, PP, #0x41, lsl #12  ; [pp+0x412d0] AnonymousClosure: (0x12eaccc), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::bottomNavigationBar (0x135dbc0)
    //     0x12eb0d8: ldr             x1, [x1, #0x2d0]
    // 0x12eb0dc: r0 = AllocateClosure()
    //     0x12eb0dc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12eb0e0: mov             x4, x0
    // 0x12eb0e4: b               #0x12eb0ec
    // 0x12eb0e8: r4 = Null
    //     0x12eb0e8: mov             x4, NULL
    // 0x12eb0ec: ldur            x2, [fp, #-8]
    // 0x12eb0f0: ldur            x3, [fp, #-0x10]
    // 0x12eb0f4: ldur            d0, [fp, #-0x40]
    // 0x12eb0f8: ldur            x0, [fp, #-0x18]
    // 0x12eb0fc: stur            x4, [fp, #-0x28]
    // 0x12eb100: LoadField: r1 = r2->field_13
    //     0x12eb100: ldur            w1, [x2, #0x13]
    // 0x12eb104: DecompressPointer r1
    //     0x12eb104: add             x1, x1, HEAP, lsl #32
    // 0x12eb108: r0 = of()
    //     0x12eb108: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12eb10c: LoadField: r1 = r0->field_87
    //     0x12eb10c: ldur            w1, [x0, #0x87]
    // 0x12eb110: DecompressPointer r1
    //     0x12eb110: add             x1, x1, HEAP, lsl #32
    // 0x12eb114: LoadField: r0 = r1->field_7
    //     0x12eb114: ldur            w0, [x1, #7]
    // 0x12eb118: DecompressPointer r0
    //     0x12eb118: add             x0, x0, HEAP, lsl #32
    // 0x12eb11c: r16 = 14.000000
    //     0x12eb11c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x12eb120: ldr             x16, [x16, #0x1d8]
    // 0x12eb124: r30 = Instance_Color
    //     0x12eb124: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x12eb128: stp             lr, x16, [SP]
    // 0x12eb12c: mov             x1, x0
    // 0x12eb130: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12eb130: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12eb134: ldr             x4, [x4, #0xaa0]
    // 0x12eb138: r0 = copyWith()
    //     0x12eb138: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12eb13c: stur            x0, [fp, #-0x30]
    // 0x12eb140: r0 = Text()
    //     0x12eb140: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12eb144: mov             x1, x0
    // 0x12eb148: r0 = "Continue"
    //     0x12eb148: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0x12eb14c: ldr             x0, [x0, #0xfe0]
    // 0x12eb150: stur            x1, [fp, #-0x38]
    // 0x12eb154: StoreField: r1->field_b = r0
    //     0x12eb154: stur            w0, [x1, #0xb]
    // 0x12eb158: ldur            x0, [fp, #-0x30]
    // 0x12eb15c: StoreField: r1->field_13 = r0
    //     0x12eb15c: stur            w0, [x1, #0x13]
    // 0x12eb160: r0 = TextButton()
    //     0x12eb160: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x12eb164: mov             x1, x0
    // 0x12eb168: ldur            x0, [fp, #-0x28]
    // 0x12eb16c: stur            x1, [fp, #-0x30]
    // 0x12eb170: StoreField: r1->field_b = r0
    //     0x12eb170: stur            w0, [x1, #0xb]
    // 0x12eb174: r0 = false
    //     0x12eb174: add             x0, NULL, #0x30  ; false
    // 0x12eb178: StoreField: r1->field_27 = r0
    //     0x12eb178: stur            w0, [x1, #0x27]
    // 0x12eb17c: r2 = true
    //     0x12eb17c: add             x2, NULL, #0x20  ; true
    // 0x12eb180: StoreField: r1->field_2f = r2
    //     0x12eb180: stur            w2, [x1, #0x2f]
    // 0x12eb184: ldur            x3, [fp, #-0x38]
    // 0x12eb188: StoreField: r1->field_37 = r3
    //     0x12eb188: stur            w3, [x1, #0x37]
    // 0x12eb18c: r0 = TextButtonTheme()
    //     0x12eb18c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x12eb190: mov             x1, x0
    // 0x12eb194: ldur            x0, [fp, #-0x18]
    // 0x12eb198: stur            x1, [fp, #-0x28]
    // 0x12eb19c: StoreField: r1->field_f = r0
    //     0x12eb19c: stur            w0, [x1, #0xf]
    // 0x12eb1a0: ldur            x0, [fp, #-0x30]
    // 0x12eb1a4: StoreField: r1->field_b = r0
    //     0x12eb1a4: stur            w0, [x1, #0xb]
    // 0x12eb1a8: ldur            d0, [fp, #-0x40]
    // 0x12eb1ac: r0 = inline_Allocate_Double()
    //     0x12eb1ac: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x12eb1b0: add             x0, x0, #0x10
    //     0x12eb1b4: cmp             x2, x0
    //     0x12eb1b8: b.ls            #0x12eb4bc
    //     0x12eb1bc: str             x0, [THR, #0x50]  ; THR::top
    //     0x12eb1c0: sub             x0, x0, #0xf
    //     0x12eb1c4: movz            x2, #0xe15c
    //     0x12eb1c8: movk            x2, #0x3, lsl #16
    //     0x12eb1cc: stur            x2, [x0, #-1]
    // 0x12eb1d0: StoreField: r0->field_7 = d0
    //     0x12eb1d0: stur            d0, [x0, #7]
    // 0x12eb1d4: stur            x0, [fp, #-0x18]
    // 0x12eb1d8: r0 = Container()
    //     0x12eb1d8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12eb1dc: stur            x0, [fp, #-0x30]
    // 0x12eb1e0: r16 = Instance_EdgeInsets
    //     0x12eb1e0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe48] Obj!EdgeInsets@d58f11
    //     0x12eb1e4: ldr             x16, [x16, #0xe48]
    // 0x12eb1e8: ldur            lr, [fp, #-0x18]
    // 0x12eb1ec: stp             lr, x16, [SP, #0x10]
    // 0x12eb1f0: ldur            x16, [fp, #-0x20]
    // 0x12eb1f4: ldur            lr, [fp, #-0x28]
    // 0x12eb1f8: stp             lr, x16, [SP]
    // 0x12eb1fc: mov             x1, x0
    // 0x12eb200: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0x12eb200: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0x12eb204: ldr             x4, [x4, #0x18]
    // 0x12eb208: r0 = Container()
    //     0x12eb208: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12eb20c: r0 = GetNavigation.size()
    //     0x12eb20c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x12eb210: LoadField: d0 = r0->field_7
    //     0x12eb210: ldur            d0, [x0, #7]
    // 0x12eb214: ldur            x0, [fp, #-8]
    // 0x12eb218: stur            d0, [fp, #-0x40]
    // 0x12eb21c: LoadField: r1 = r0->field_13
    //     0x12eb21c: ldur            w1, [x0, #0x13]
    // 0x12eb220: DecompressPointer r1
    //     0x12eb220: add             x1, x1, HEAP, lsl #32
    // 0x12eb224: r0 = of()
    //     0x12eb224: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x12eb228: LoadField: r1 = r0->field_87
    //     0x12eb228: ldur            w1, [x0, #0x87]
    // 0x12eb22c: DecompressPointer r1
    //     0x12eb22c: add             x1, x1, HEAP, lsl #32
    // 0x12eb230: LoadField: r0 = r1->field_2b
    //     0x12eb230: ldur            w0, [x1, #0x2b]
    // 0x12eb234: DecompressPointer r0
    //     0x12eb234: add             x0, x0, HEAP, lsl #32
    // 0x12eb238: stur            x0, [fp, #-8]
    // 0x12eb23c: r1 = Instance_Color
    //     0x12eb23c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x12eb240: d0 = 0.700000
    //     0x12eb240: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x12eb244: ldr             d0, [x17, #0xf48]
    // 0x12eb248: r0 = withOpacity()
    //     0x12eb248: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x12eb24c: r16 = 10.000000
    //     0x12eb24c: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0x12eb250: stp             x0, x16, [SP]
    // 0x12eb254: ldur            x1, [fp, #-8]
    // 0x12eb258: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x12eb258: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x12eb25c: ldr             x4, [x4, #0xaa0]
    // 0x12eb260: r0 = copyWith()
    //     0x12eb260: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x12eb264: stur            x0, [fp, #-8]
    // 0x12eb268: r0 = Text()
    //     0x12eb268: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x12eb26c: mov             x1, x0
    // 0x12eb270: r0 = "Powered By"
    //     0x12eb270: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c750] "Powered By"
    //     0x12eb274: ldr             x0, [x0, #0x750]
    // 0x12eb278: stur            x1, [fp, #-0x18]
    // 0x12eb27c: StoreField: r1->field_b = r0
    //     0x12eb27c: stur            w0, [x1, #0xb]
    // 0x12eb280: ldur            x0, [fp, #-8]
    // 0x12eb284: StoreField: r1->field_13 = r0
    //     0x12eb284: stur            w0, [x1, #0x13]
    // 0x12eb288: r0 = SvgPicture()
    //     0x12eb288: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x12eb28c: stur            x0, [fp, #-8]
    // 0x12eb290: r16 = 20.000000
    //     0x12eb290: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0x12eb294: ldr             x16, [x16, #0xac8]
    // 0x12eb298: str             x16, [SP]
    // 0x12eb29c: mov             x1, x0
    // 0x12eb2a0: r2 = "assets/images/shopdeck.svg"
    //     0x12eb2a0: add             x2, PP, #0x3c, lsl #12  ; [pp+0x3c758] "assets/images/shopdeck.svg"
    //     0x12eb2a4: ldr             x2, [x2, #0x758]
    // 0x12eb2a8: r4 = const [0, 0x3, 0x1, 0x2, height, 0x2, null]
    //     0x12eb2a8: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c760] List(7) [0, 0x3, 0x1, 0x2, "height", 0x2, Null]
    //     0x12eb2ac: ldr             x4, [x4, #0x760]
    // 0x12eb2b0: r0 = SvgPicture.asset()
    //     0x12eb2b0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x12eb2b4: r1 = Null
    //     0x12eb2b4: mov             x1, NULL
    // 0x12eb2b8: r2 = 4
    //     0x12eb2b8: movz            x2, #0x4
    // 0x12eb2bc: r0 = AllocateArray()
    //     0x12eb2bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12eb2c0: mov             x2, x0
    // 0x12eb2c4: ldur            x0, [fp, #-0x18]
    // 0x12eb2c8: stur            x2, [fp, #-0x20]
    // 0x12eb2cc: StoreField: r2->field_f = r0
    //     0x12eb2cc: stur            w0, [x2, #0xf]
    // 0x12eb2d0: ldur            x0, [fp, #-8]
    // 0x12eb2d4: StoreField: r2->field_13 = r0
    //     0x12eb2d4: stur            w0, [x2, #0x13]
    // 0x12eb2d8: r1 = <Widget>
    //     0x12eb2d8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12eb2dc: r0 = AllocateGrowableArray()
    //     0x12eb2dc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12eb2e0: mov             x1, x0
    // 0x12eb2e4: ldur            x0, [fp, #-0x20]
    // 0x12eb2e8: stur            x1, [fp, #-8]
    // 0x12eb2ec: StoreField: r1->field_f = r0
    //     0x12eb2ec: stur            w0, [x1, #0xf]
    // 0x12eb2f0: r2 = 4
    //     0x12eb2f0: movz            x2, #0x4
    // 0x12eb2f4: StoreField: r1->field_b = r2
    //     0x12eb2f4: stur            w2, [x1, #0xb]
    // 0x12eb2f8: r0 = Row()
    //     0x12eb2f8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x12eb2fc: mov             x1, x0
    // 0x12eb300: r0 = Instance_Axis
    //     0x12eb300: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x12eb304: stur            x1, [fp, #-0x18]
    // 0x12eb308: StoreField: r1->field_f = r0
    //     0x12eb308: stur            w0, [x1, #0xf]
    // 0x12eb30c: r0 = Instance_MainAxisAlignment
    //     0x12eb30c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12eb310: ldr             x0, [x0, #0xa08]
    // 0x12eb314: StoreField: r1->field_13 = r0
    //     0x12eb314: stur            w0, [x1, #0x13]
    // 0x12eb318: r2 = Instance_MainAxisSize
    //     0x12eb318: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x12eb31c: ldr             x2, [x2, #0xdd0]
    // 0x12eb320: ArrayStore: r1[0] = r2  ; List_4
    //     0x12eb320: stur            w2, [x1, #0x17]
    // 0x12eb324: r3 = Instance_CrossAxisAlignment
    //     0x12eb324: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12eb328: ldr             x3, [x3, #0xa18]
    // 0x12eb32c: StoreField: r1->field_1b = r3
    //     0x12eb32c: stur            w3, [x1, #0x1b]
    // 0x12eb330: r4 = Instance_VerticalDirection
    //     0x12eb330: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12eb334: ldr             x4, [x4, #0xa20]
    // 0x12eb338: StoreField: r1->field_23 = r4
    //     0x12eb338: stur            w4, [x1, #0x23]
    // 0x12eb33c: r5 = Instance_Clip
    //     0x12eb33c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12eb340: ldr             x5, [x5, #0x38]
    // 0x12eb344: StoreField: r1->field_2b = r5
    //     0x12eb344: stur            w5, [x1, #0x2b]
    // 0x12eb348: StoreField: r1->field_2f = rZR
    //     0x12eb348: stur            xzr, [x1, #0x2f]
    // 0x12eb34c: ldur            x6, [fp, #-8]
    // 0x12eb350: StoreField: r1->field_b = r6
    //     0x12eb350: stur            w6, [x1, #0xb]
    // 0x12eb354: ldur            d0, [fp, #-0x40]
    // 0x12eb358: r6 = inline_Allocate_Double()
    //     0x12eb358: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x12eb35c: add             x6, x6, #0x10
    //     0x12eb360: cmp             x7, x6
    //     0x12eb364: b.ls            #0x12eb4d4
    //     0x12eb368: str             x6, [THR, #0x50]  ; THR::top
    //     0x12eb36c: sub             x6, x6, #0xf
    //     0x12eb370: movz            x7, #0xe15c
    //     0x12eb374: movk            x7, #0x3, lsl #16
    //     0x12eb378: stur            x7, [x6, #-1]
    // 0x12eb37c: StoreField: r6->field_7 = d0
    //     0x12eb37c: stur            d0, [x6, #7]
    // 0x12eb380: stur            x6, [fp, #-8]
    // 0x12eb384: r0 = Container()
    //     0x12eb384: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x12eb388: stur            x0, [fp, #-0x20]
    // 0x12eb38c: r16 = Instance_Alignment
    //     0x12eb38c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x12eb390: ldr             x16, [x16, #0xb10]
    // 0x12eb394: ldur            lr, [fp, #-8]
    // 0x12eb398: stp             lr, x16, [SP, #0x10]
    // 0x12eb39c: r16 = Instance_BoxDecoration
    //     0x12eb39c: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3c768] Obj!BoxDecoration@d64c81
    //     0x12eb3a0: ldr             x16, [x16, #0x768]
    // 0x12eb3a4: ldur            lr, [fp, #-0x18]
    // 0x12eb3a8: stp             lr, x16, [SP]
    // 0x12eb3ac: mov             x1, x0
    // 0x12eb3b0: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x1, child, 0x4, decoration, 0x3, width, 0x2, null]
    //     0x12eb3b0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c770] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x1, "child", 0x4, "decoration", 0x3, "width", 0x2, Null]
    //     0x12eb3b4: ldr             x4, [x4, #0x770]
    // 0x12eb3b8: r0 = Container()
    //     0x12eb3b8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x12eb3bc: r1 = Null
    //     0x12eb3bc: mov             x1, NULL
    // 0x12eb3c0: r2 = 4
    //     0x12eb3c0: movz            x2, #0x4
    // 0x12eb3c4: r0 = AllocateArray()
    //     0x12eb3c4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12eb3c8: mov             x2, x0
    // 0x12eb3cc: ldur            x0, [fp, #-0x30]
    // 0x12eb3d0: stur            x2, [fp, #-8]
    // 0x12eb3d4: StoreField: r2->field_f = r0
    //     0x12eb3d4: stur            w0, [x2, #0xf]
    // 0x12eb3d8: ldur            x0, [fp, #-0x20]
    // 0x12eb3dc: StoreField: r2->field_13 = r0
    //     0x12eb3dc: stur            w0, [x2, #0x13]
    // 0x12eb3e0: r1 = <Widget>
    //     0x12eb3e0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x12eb3e4: r0 = AllocateGrowableArray()
    //     0x12eb3e4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12eb3e8: mov             x1, x0
    // 0x12eb3ec: ldur            x0, [fp, #-8]
    // 0x12eb3f0: stur            x1, [fp, #-0x18]
    // 0x12eb3f4: StoreField: r1->field_f = r0
    //     0x12eb3f4: stur            w0, [x1, #0xf]
    // 0x12eb3f8: r0 = 4
    //     0x12eb3f8: movz            x0, #0x4
    // 0x12eb3fc: StoreField: r1->field_b = r0
    //     0x12eb3fc: stur            w0, [x1, #0xb]
    // 0x12eb400: r0 = Column()
    //     0x12eb400: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x12eb404: mov             x1, x0
    // 0x12eb408: r0 = Instance_Axis
    //     0x12eb408: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x12eb40c: stur            x1, [fp, #-8]
    // 0x12eb410: StoreField: r1->field_f = r0
    //     0x12eb410: stur            w0, [x1, #0xf]
    // 0x12eb414: r0 = Instance_MainAxisAlignment
    //     0x12eb414: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x12eb418: ldr             x0, [x0, #0xa08]
    // 0x12eb41c: StoreField: r1->field_13 = r0
    //     0x12eb41c: stur            w0, [x1, #0x13]
    // 0x12eb420: r0 = Instance_MainAxisSize
    //     0x12eb420: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x12eb424: ldr             x0, [x0, #0xdd0]
    // 0x12eb428: ArrayStore: r1[0] = r0  ; List_4
    //     0x12eb428: stur            w0, [x1, #0x17]
    // 0x12eb42c: r0 = Instance_CrossAxisAlignment
    //     0x12eb42c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x12eb430: ldr             x0, [x0, #0xa18]
    // 0x12eb434: StoreField: r1->field_1b = r0
    //     0x12eb434: stur            w0, [x1, #0x1b]
    // 0x12eb438: r0 = Instance_VerticalDirection
    //     0x12eb438: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x12eb43c: ldr             x0, [x0, #0xa20]
    // 0x12eb440: StoreField: r1->field_23 = r0
    //     0x12eb440: stur            w0, [x1, #0x23]
    // 0x12eb444: r0 = Instance_Clip
    //     0x12eb444: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x12eb448: ldr             x0, [x0, #0x38]
    // 0x12eb44c: StoreField: r1->field_2b = r0
    //     0x12eb44c: stur            w0, [x1, #0x2b]
    // 0x12eb450: StoreField: r1->field_2f = rZR
    //     0x12eb450: stur            xzr, [x1, #0x2f]
    // 0x12eb454: ldur            x0, [fp, #-0x18]
    // 0x12eb458: StoreField: r1->field_b = r0
    //     0x12eb458: stur            w0, [x1, #0xb]
    // 0x12eb45c: r0 = Padding()
    //     0x12eb45c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x12eb460: mov             x1, x0
    // 0x12eb464: ldur            x0, [fp, #-0x10]
    // 0x12eb468: stur            x1, [fp, #-0x18]
    // 0x12eb46c: StoreField: r1->field_f = r0
    //     0x12eb46c: stur            w0, [x1, #0xf]
    // 0x12eb470: ldur            x0, [fp, #-8]
    // 0x12eb474: StoreField: r1->field_b = r0
    //     0x12eb474: stur            w0, [x1, #0xb]
    // 0x12eb478: r0 = SafeArea()
    //     0x12eb478: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x12eb47c: r1 = true
    //     0x12eb47c: add             x1, NULL, #0x20  ; true
    // 0x12eb480: StoreField: r0->field_b = r1
    //     0x12eb480: stur            w1, [x0, #0xb]
    // 0x12eb484: StoreField: r0->field_f = r1
    //     0x12eb484: stur            w1, [x0, #0xf]
    // 0x12eb488: StoreField: r0->field_13 = r1
    //     0x12eb488: stur            w1, [x0, #0x13]
    // 0x12eb48c: ArrayStore: r0[0] = r1  ; List_4
    //     0x12eb48c: stur            w1, [x0, #0x17]
    // 0x12eb490: r1 = Instance_EdgeInsets
    //     0x12eb490: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x12eb494: StoreField: r0->field_1b = r1
    //     0x12eb494: stur            w1, [x0, #0x1b]
    // 0x12eb498: r1 = false
    //     0x12eb498: add             x1, NULL, #0x30  ; false
    // 0x12eb49c: StoreField: r0->field_1f = r1
    //     0x12eb49c: stur            w1, [x0, #0x1f]
    // 0x12eb4a0: ldur            x1, [fp, #-0x18]
    // 0x12eb4a4: StoreField: r0->field_23 = r1
    //     0x12eb4a4: stur            w1, [x0, #0x23]
    // 0x12eb4a8: LeaveFrame
    //     0x12eb4a8: mov             SP, fp
    //     0x12eb4ac: ldp             fp, lr, [SP], #0x10
    // 0x12eb4b0: ret
    //     0x12eb4b0: ret             
    // 0x12eb4b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12eb4b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12eb4b8: b               #0x12eae80
    // 0x12eb4bc: SaveReg d0
    //     0x12eb4bc: str             q0, [SP, #-0x10]!
    // 0x12eb4c0: SaveReg r1
    //     0x12eb4c0: str             x1, [SP, #-8]!
    // 0x12eb4c4: r0 = AllocateDouble()
    //     0x12eb4c4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12eb4c8: RestoreReg r1
    //     0x12eb4c8: ldr             x1, [SP], #8
    // 0x12eb4cc: RestoreReg d0
    //     0x12eb4cc: ldr             q0, [SP], #0x10
    // 0x12eb4d0: b               #0x12eb1d0
    // 0x12eb4d4: SaveReg d0
    //     0x12eb4d4: str             q0, [SP, #-0x10]!
    // 0x12eb4d8: stp             x4, x5, [SP, #-0x10]!
    // 0x12eb4dc: stp             x2, x3, [SP, #-0x10]!
    // 0x12eb4e0: stp             x0, x1, [SP, #-0x10]!
    // 0x12eb4e4: r0 = AllocateDouble()
    //     0x12eb4e4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x12eb4e8: mov             x6, x0
    // 0x12eb4ec: ldp             x0, x1, [SP], #0x10
    // 0x12eb4f0: ldp             x2, x3, [SP], #0x10
    // 0x12eb4f4: ldp             x4, x5, [SP], #0x10
    // 0x12eb4f8: RestoreReg d0
    //     0x12eb4f8: ldr             q0, [SP], #0x10
    // 0x12eb4fc: b               #0x12eb37c
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x135dbc0, size: 0x64
    // 0x135dbc0: EnterFrame
    //     0x135dbc0: stp             fp, lr, [SP, #-0x10]!
    //     0x135dbc4: mov             fp, SP
    // 0x135dbc8: AllocStack(0x18)
    //     0x135dbc8: sub             SP, SP, #0x18
    // 0x135dbcc: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x135dbcc: stur            x1, [fp, #-8]
    //     0x135dbd0: stur            x2, [fp, #-0x10]
    // 0x135dbd4: r1 = 2
    //     0x135dbd4: movz            x1, #0x2
    // 0x135dbd8: r0 = AllocateContext()
    //     0x135dbd8: bl              #0x16f6108  ; AllocateContextStub
    // 0x135dbdc: mov             x1, x0
    // 0x135dbe0: ldur            x0, [fp, #-8]
    // 0x135dbe4: stur            x1, [fp, #-0x18]
    // 0x135dbe8: StoreField: r1->field_f = r0
    //     0x135dbe8: stur            w0, [x1, #0xf]
    // 0x135dbec: ldur            x0, [fp, #-0x10]
    // 0x135dbf0: StoreField: r1->field_13 = r0
    //     0x135dbf0: stur            w0, [x1, #0x13]
    // 0x135dbf4: r0 = Obx()
    //     0x135dbf4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x135dbf8: ldur            x2, [fp, #-0x18]
    // 0x135dbfc: r1 = Function '<anonymous closure>':.
    //     0x135dbfc: add             x1, PP, #0x41, lsl #12  ; [pp+0x412c8] AnonymousClosure: (0x12eae58), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::bottomNavigationBar (0x135dbc0)
    //     0x135dc00: ldr             x1, [x1, #0x2c8]
    // 0x135dc04: stur            x0, [fp, #-8]
    // 0x135dc08: r0 = AllocateClosure()
    //     0x135dc08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x135dc0c: mov             x1, x0
    // 0x135dc10: ldur            x0, [fp, #-8]
    // 0x135dc14: StoreField: r0->field_b = r1
    //     0x135dc14: stur            w1, [x0, #0xb]
    // 0x135dc18: LeaveFrame
    //     0x135dc18: mov             SP, fp
    //     0x135dc1c: ldp             fp, lr, [SP], #0x10
    // 0x135dc20: ret
    //     0x135dc20: ret             
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x1394cd0, size: 0xcc
    // 0x1394cd0: EnterFrame
    //     0x1394cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x1394cd4: mov             fp, SP
    // 0x1394cd8: AllocStack(0x18)
    //     0x1394cd8: sub             SP, SP, #0x18
    // 0x1394cdc: SetupParameters(CheckoutRequestOtpPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1394cdc: stur            NULL, [fp, #-8]
    //     0x1394ce0: movz            x0, #0
    //     0x1394ce4: add             x1, fp, w0, sxtw #2
    //     0x1394ce8: ldr             x1, [x1, #0x18]
    //     0x1394cec: add             x2, fp, w0, sxtw #2
    //     0x1394cf0: ldr             x2, [x2, #0x10]
    //     0x1394cf4: stur            x2, [fp, #-0x18]
    //     0x1394cf8: ldur            w3, [x1, #0x17]
    //     0x1394cfc: add             x3, x3, HEAP, lsl #32
    //     0x1394d00: stur            x3, [fp, #-0x10]
    // 0x1394d04: CheckStackOverflow
    //     0x1394d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1394d08: cmp             SP, x16
    //     0x1394d0c: b.ls            #0x1394d94
    // 0x1394d10: InitAsync() -> Future<Null?>
    //     0x1394d10: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x1394d14: bl              #0x6326e0  ; InitAsyncStub
    // 0x1394d18: ldur            x0, [fp, #-0x10]
    // 0x1394d1c: LoadField: r1 = r0->field_f
    //     0x1394d1c: ldur            w1, [x0, #0xf]
    // 0x1394d20: DecompressPointer r1
    //     0x1394d20: add             x1, x1, HEAP, lsl #32
    // 0x1394d24: r0 = controller()
    //     0x1394d24: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1394d28: LoadField: r1 = r0->field_ab
    //     0x1394d28: ldur            w1, [x0, #0xab]
    // 0x1394d2c: DecompressPointer r1
    //     0x1394d2c: add             x1, x1, HEAP, lsl #32
    // 0x1394d30: ldur            x2, [fp, #-0x18]
    // 0x1394d34: r0 = value=()
    //     0x1394d34: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1394d38: ldur            x0, [fp, #-0x10]
    // 0x1394d3c: LoadField: r1 = r0->field_f
    //     0x1394d3c: ldur            w1, [x0, #0xf]
    // 0x1394d40: DecompressPointer r1
    //     0x1394d40: add             x1, x1, HEAP, lsl #32
    // 0x1394d44: r0 = controller()
    //     0x1394d44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1394d48: LoadField: r1 = r0->field_fb
    //     0x1394d48: ldur            w1, [x0, #0xfb]
    // 0x1394d4c: DecompressPointer r1
    //     0x1394d4c: add             x1, x1, HEAP, lsl #32
    // 0x1394d50: r2 = true
    //     0x1394d50: add             x2, NULL, #0x20  ; true
    // 0x1394d54: r0 = value=()
    //     0x1394d54: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x1394d58: ldur            x0, [fp, #-0x10]
    // 0x1394d5c: LoadField: r1 = r0->field_f
    //     0x1394d5c: ldur            w1, [x0, #0xf]
    // 0x1394d60: DecompressPointer r1
    //     0x1394d60: add             x1, x1, HEAP, lsl #32
    // 0x1394d64: r0 = controller()
    //     0x1394d64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1394d68: mov             x1, x0
    // 0x1394d6c: ldur            x2, [fp, #-0x18]
    // 0x1394d70: r0 = updatePrefPhoneNumber()
    //     0x1394d70: bl              #0x139513c  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::updatePrefPhoneNumber
    // 0x1394d74: ldur            x0, [fp, #-0x10]
    // 0x1394d78: LoadField: r1 = r0->field_f
    //     0x1394d78: ldur            w1, [x0, #0xf]
    // 0x1394d7c: DecompressPointer r1
    //     0x1394d7c: add             x1, x1, HEAP, lsl #32
    // 0x1394d80: r0 = controller()
    //     0x1394d80: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1394d84: mov             x1, x0
    // 0x1394d88: r0 = getOtp()
    //     0x1394d88: bl              #0x1394f64  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_otp_controller.dart] CheckoutRequestOtpController::getOtp
    // 0x1394d8c: r0 = Null
    //     0x1394d8c: mov             x0, NULL
    // 0x1394d90: r0 = ReturnAsyncNotFuture()
    //     0x1394d90: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1394d94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1394d94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1394d98: b               #0x1394d10
  }
  [closure] AnimatedPadding <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1394de8, size: 0xe0
    // 0x1394de8: EnterFrame
    //     0x1394de8: stp             fp, lr, [SP, #-0x10]!
    //     0x1394dec: mov             fp, SP
    // 0x1394df0: AllocStack(0x28)
    //     0x1394df0: sub             SP, SP, #0x28
    // 0x1394df4: SetupParameters()
    //     0x1394df4: ldr             x0, [fp, #0x18]
    //     0x1394df8: ldur            w2, [x0, #0x17]
    //     0x1394dfc: add             x2, x2, HEAP, lsl #32
    //     0x1394e00: stur            x2, [fp, #-8]
    // 0x1394e04: CheckStackOverflow
    //     0x1394e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1394e08: cmp             SP, x16
    //     0x1394e0c: b.ls            #0x1394ec0
    // 0x1394e10: ldr             x1, [fp, #0x10]
    // 0x1394e14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1394e14: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1394e18: r0 = _of()
    //     0x1394e18: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x1394e1c: LoadField: r1 = r0->field_23
    //     0x1394e1c: ldur            w1, [x0, #0x23]
    // 0x1394e20: DecompressPointer r1
    //     0x1394e20: add             x1, x1, HEAP, lsl #32
    // 0x1394e24: LoadField: d0 = r1->field_1f
    //     0x1394e24: ldur            d0, [x1, #0x1f]
    // 0x1394e28: stur            d0, [fp, #-0x28]
    // 0x1394e2c: r0 = EdgeInsets()
    //     0x1394e2c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1394e30: stur            x0, [fp, #-0x18]
    // 0x1394e34: StoreField: r0->field_7 = rZR
    //     0x1394e34: stur            xzr, [x0, #7]
    // 0x1394e38: StoreField: r0->field_f = rZR
    //     0x1394e38: stur            xzr, [x0, #0xf]
    // 0x1394e3c: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1394e3c: stur            xzr, [x0, #0x17]
    // 0x1394e40: ldur            d0, [fp, #-0x28]
    // 0x1394e44: StoreField: r0->field_1f = d0
    //     0x1394e44: stur            d0, [x0, #0x1f]
    // 0x1394e48: ldur            x2, [fp, #-8]
    // 0x1394e4c: LoadField: r1 = r2->field_13
    //     0x1394e4c: ldur            w1, [x2, #0x13]
    // 0x1394e50: DecompressPointer r1
    //     0x1394e50: add             x1, x1, HEAP, lsl #32
    // 0x1394e54: stur            x1, [fp, #-0x10]
    // 0x1394e58: r0 = EditPhoneBottomSheet()
    //     0x1394e58: bl              #0x1394ec8  ; AllocateEditPhoneBottomSheetStub -> EditPhoneBottomSheet (size=0x14)
    // 0x1394e5c: mov             x3, x0
    // 0x1394e60: ldur            x0, [fp, #-0x10]
    // 0x1394e64: stur            x3, [fp, #-0x20]
    // 0x1394e68: StoreField: r3->field_b = r0
    //     0x1394e68: stur            w0, [x3, #0xb]
    // 0x1394e6c: ldur            x2, [fp, #-8]
    // 0x1394e70: r1 = Function '<anonymous closure>':.
    //     0x1394e70: add             x1, PP, #0x41, lsl #12  ; [pp+0x41318] AnonymousClosure: (0x1394cd0), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet (0x1394ed4)
    //     0x1394e74: ldr             x1, [x1, #0x318]
    // 0x1394e78: r0 = AllocateClosure()
    //     0x1394e78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1394e7c: mov             x1, x0
    // 0x1394e80: ldur            x0, [fp, #-0x20]
    // 0x1394e84: StoreField: r0->field_f = r1
    //     0x1394e84: stur            w1, [x0, #0xf]
    // 0x1394e88: r0 = AnimatedPadding()
    //     0x1394e88: bl              #0x12a22b8  ; AllocateAnimatedPaddingStub -> AnimatedPadding (size=0x20)
    // 0x1394e8c: ldur            x1, [fp, #-0x18]
    // 0x1394e90: ArrayStore: r0[0] = r1  ; List_4
    //     0x1394e90: stur            w1, [x0, #0x17]
    // 0x1394e94: ldur            x1, [fp, #-0x20]
    // 0x1394e98: StoreField: r0->field_1b = r1
    //     0x1394e98: stur            w1, [x0, #0x1b]
    // 0x1394e9c: r1 = Instance_Cubic
    //     0x1394e9c: add             x1, PP, #0x36, lsl #12  ; [pp+0x362b0] Obj!Cubic@d5b591
    //     0x1394ea0: ldr             x1, [x1, #0x2b0]
    // 0x1394ea4: StoreField: r0->field_b = r1
    //     0x1394ea4: stur            w1, [x0, #0xb]
    // 0x1394ea8: r1 = Instance_Duration
    //     0x1394ea8: add             x1, PP, #0xa, lsl #12  ; [pp+0xaf00] Obj!Duration@d776f1
    //     0x1394eac: ldr             x1, [x1, #0xf00]
    // 0x1394eb0: StoreField: r0->field_f = r1
    //     0x1394eb0: stur            w1, [x0, #0xf]
    // 0x1394eb4: LeaveFrame
    //     0x1394eb4: mov             SP, fp
    //     0x1394eb8: ldp             fp, lr, [SP], #0x10
    // 0x1394ebc: ret
    //     0x1394ebc: ret             
    // 0x1394ec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1394ec0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1394ec4: b               #0x1394e10
  }
  _ _showEditPhoneBottomSheet(/* No info */) {
    // ** addr: 0x1394ed4, size: 0x90
    // 0x1394ed4: EnterFrame
    //     0x1394ed4: stp             fp, lr, [SP, #-0x10]!
    //     0x1394ed8: mov             fp, SP
    // 0x1394edc: AllocStack(0x40)
    //     0x1394edc: sub             SP, SP, #0x40
    // 0x1394ee0: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x1394ee0: stur            x1, [fp, #-8]
    //     0x1394ee4: stur            x2, [fp, #-0x10]
    //     0x1394ee8: stur            x3, [fp, #-0x18]
    // 0x1394eec: CheckStackOverflow
    //     0x1394eec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1394ef0: cmp             SP, x16
    //     0x1394ef4: b.ls            #0x1394f5c
    // 0x1394ef8: r1 = 2
    //     0x1394ef8: movz            x1, #0x2
    // 0x1394efc: r0 = AllocateContext()
    //     0x1394efc: bl              #0x16f6108  ; AllocateContextStub
    // 0x1394f00: mov             x1, x0
    // 0x1394f04: ldur            x0, [fp, #-8]
    // 0x1394f08: StoreField: r1->field_f = r0
    //     0x1394f08: stur            w0, [x1, #0xf]
    // 0x1394f0c: ldur            x0, [fp, #-0x18]
    // 0x1394f10: StoreField: r1->field_13 = r0
    //     0x1394f10: stur            w0, [x1, #0x13]
    // 0x1394f14: mov             x2, x1
    // 0x1394f18: r1 = Function '<anonymous closure>':.
    //     0x1394f18: add             x1, PP, #0x41, lsl #12  ; [pp+0x41310] AnonymousClosure: (0x1394de8), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet (0x1394ed4)
    //     0x1394f1c: ldr             x1, [x1, #0x310]
    // 0x1394f20: r0 = AllocateClosure()
    //     0x1394f20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1394f24: stp             x0, NULL, [SP, #0x18]
    // 0x1394f28: ldur            x16, [fp, #-0x10]
    // 0x1394f2c: r30 = true
    //     0x1394f2c: add             lr, NULL, #0x20  ; true
    // 0x1394f30: stp             lr, x16, [SP, #8]
    // 0x1394f34: r16 = Instance_RoundedRectangleBorder
    //     0x1394f34: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x1394f38: ldr             x16, [x16, #0xc78]
    // 0x1394f3c: str             x16, [SP]
    // 0x1394f40: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x1394f40: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x1394f44: ldr             x4, [x4, #0xb20]
    // 0x1394f48: r0 = showModalBottomSheet()
    //     0x1394f48: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x1394f4c: r0 = Null
    //     0x1394f4c: mov             x0, NULL
    // 0x1394f50: LeaveFrame
    //     0x1394f50: mov             SP, fp
    //     0x1394f54: ldp             fp, lr, [SP], #0x10
    // 0x1394f58: ret
    //     0x1394f58: ret             
    // 0x1394f5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1394f5c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1394f60: b               #0x1394ef8
  }
  _ body(/* No info */) {
    // ** addr: 0x14d7c24, size: 0x218
    // 0x14d7c24: EnterFrame
    //     0x14d7c24: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7c28: mov             fp, SP
    // 0x14d7c2c: AllocStack(0x30)
    //     0x14d7c2c: sub             SP, SP, #0x30
    // 0x14d7c30: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x14d7c30: stur            x1, [fp, #-8]
    //     0x14d7c34: stur            x2, [fp, #-0x10]
    // 0x14d7c38: CheckStackOverflow
    //     0x14d7c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7c3c: cmp             SP, x16
    //     0x14d7c40: b.ls            #0x14d7e34
    // 0x14d7c44: r1 = 2
    //     0x14d7c44: movz            x1, #0x2
    // 0x14d7c48: r0 = AllocateContext()
    //     0x14d7c48: bl              #0x16f6108  ; AllocateContextStub
    // 0x14d7c4c: ldur            x1, [fp, #-8]
    // 0x14d7c50: stur            x0, [fp, #-0x18]
    // 0x14d7c54: StoreField: r0->field_f = r1
    //     0x14d7c54: stur            w1, [x0, #0xf]
    // 0x14d7c58: ldur            x2, [fp, #-0x10]
    // 0x14d7c5c: StoreField: r0->field_13 = r2
    //     0x14d7c5c: stur            w2, [x0, #0x13]
    // 0x14d7c60: r0 = Obx()
    //     0x14d7c60: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14d7c64: ldur            x2, [fp, #-0x18]
    // 0x14d7c68: r1 = Function '<anonymous closure>':.
    //     0x14d7c68: add             x1, PP, #0x41, lsl #12  ; [pp+0x412d8] AnonymousClosure: (0x14d7fa8), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x14d7c24)
    //     0x14d7c6c: ldr             x1, [x1, #0x2d8]
    // 0x14d7c70: stur            x0, [fp, #-0x10]
    // 0x14d7c74: r0 = AllocateClosure()
    //     0x14d7c74: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7c78: mov             x1, x0
    // 0x14d7c7c: ldur            x0, [fp, #-0x10]
    // 0x14d7c80: StoreField: r0->field_b = r1
    //     0x14d7c80: stur            w1, [x0, #0xb]
    // 0x14d7c84: ldur            x1, [fp, #-8]
    // 0x14d7c88: r0 = controller()
    //     0x14d7c88: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d7c8c: LoadField: r1 = r0->field_ab
    //     0x14d7c8c: ldur            w1, [x0, #0xab]
    // 0x14d7c90: DecompressPointer r1
    //     0x14d7c90: add             x1, x1, HEAP, lsl #32
    // 0x14d7c94: r0 = value()
    //     0x14d7c94: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7c98: ldur            x2, [fp, #-0x18]
    // 0x14d7c9c: r1 = Function '<anonymous closure>':.
    //     0x14d7c9c: add             x1, PP, #0x41, lsl #12  ; [pp+0x412e0] AnonymousClosure: (0x13954a8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x14d7ca0: ldr             x1, [x1, #0x2e0]
    // 0x14d7ca4: stur            x0, [fp, #-0x20]
    // 0x14d7ca8: r0 = AllocateClosure()
    //     0x14d7ca8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7cac: stur            x0, [fp, #-0x28]
    // 0x14d7cb0: r0 = CheckoutOtpWidget()
    //     0x14d7cb0: bl              #0x13a7b14  ; AllocateCheckoutOtpWidgetStub -> CheckoutOtpWidget (size=0x20)
    // 0x14d7cb4: mov             x3, x0
    // 0x14d7cb8: ldur            x0, [fp, #-0x28]
    // 0x14d7cbc: stur            x3, [fp, #-0x30]
    // 0x14d7cc0: StoreField: r3->field_b = r0
    //     0x14d7cc0: stur            w0, [x3, #0xb]
    // 0x14d7cc4: ldur            x2, [fp, #-0x18]
    // 0x14d7cc8: r1 = Function '<anonymous closure>':.
    //     0x14d7cc8: add             x1, PP, #0x41, lsl #12  ; [pp+0x412e8] AnonymousClosure: (0x13951a4), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x1500d5c)
    //     0x14d7ccc: ldr             x1, [x1, #0x2e8]
    // 0x14d7cd0: r0 = AllocateClosure()
    //     0x14d7cd0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7cd4: mov             x1, x0
    // 0x14d7cd8: ldur            x0, [fp, #-0x30]
    // 0x14d7cdc: StoreField: r0->field_f = r1
    //     0x14d7cdc: stur            w1, [x0, #0xf]
    // 0x14d7ce0: r1 = Function '<anonymous closure>':.
    //     0x14d7ce0: add             x1, PP, #0x41, lsl #12  ; [pp+0x412f0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x14d7ce4: ldr             x1, [x1, #0x2f0]
    // 0x14d7ce8: r2 = Null
    //     0x14d7ce8: mov             x2, NULL
    // 0x14d7cec: r0 = AllocateClosure()
    //     0x14d7cec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7cf0: mov             x1, x0
    // 0x14d7cf4: ldur            x0, [fp, #-0x30]
    // 0x14d7cf8: StoreField: r0->field_13 = r1
    //     0x14d7cf8: stur            w1, [x0, #0x13]
    // 0x14d7cfc: ldur            x1, [fp, #-0x20]
    // 0x14d7d00: StoreField: r0->field_1b = r1
    //     0x14d7d00: stur            w1, [x0, #0x1b]
    // 0x14d7d04: ldur            x2, [fp, #-0x18]
    // 0x14d7d08: r1 = Function '<anonymous closure>':.
    //     0x14d7d08: add             x1, PP, #0x41, lsl #12  ; [pp+0x412f8] AnonymousClosure: (0x14d7f24), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::body (0x14d7c24)
    //     0x14d7d0c: ldr             x1, [x1, #0x2f8]
    // 0x14d7d10: r0 = AllocateClosure()
    //     0x14d7d10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7d14: mov             x1, x0
    // 0x14d7d18: ldur            x0, [fp, #-0x30]
    // 0x14d7d1c: ArrayStore: r0[0] = r1  ; List_4
    //     0x14d7d1c: stur            w1, [x0, #0x17]
    // 0x14d7d20: r1 = Null
    //     0x14d7d20: mov             x1, NULL
    // 0x14d7d24: r2 = 4
    //     0x14d7d24: movz            x2, #0x4
    // 0x14d7d28: r0 = AllocateArray()
    //     0x14d7d28: bl              #0x16f7198  ; AllocateArrayStub
    // 0x14d7d2c: mov             x2, x0
    // 0x14d7d30: ldur            x0, [fp, #-0x10]
    // 0x14d7d34: stur            x2, [fp, #-0x18]
    // 0x14d7d38: StoreField: r2->field_f = r0
    //     0x14d7d38: stur            w0, [x2, #0xf]
    // 0x14d7d3c: ldur            x0, [fp, #-0x30]
    // 0x14d7d40: StoreField: r2->field_13 = r0
    //     0x14d7d40: stur            w0, [x2, #0x13]
    // 0x14d7d44: r1 = <Widget>
    //     0x14d7d44: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x14d7d48: r0 = AllocateGrowableArray()
    //     0x14d7d48: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x14d7d4c: mov             x1, x0
    // 0x14d7d50: ldur            x0, [fp, #-0x18]
    // 0x14d7d54: stur            x1, [fp, #-0x10]
    // 0x14d7d58: StoreField: r1->field_f = r0
    //     0x14d7d58: stur            w0, [x1, #0xf]
    // 0x14d7d5c: r0 = 4
    //     0x14d7d5c: movz            x0, #0x4
    // 0x14d7d60: StoreField: r1->field_b = r0
    //     0x14d7d60: stur            w0, [x1, #0xb]
    // 0x14d7d64: r0 = Column()
    //     0x14d7d64: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x14d7d68: mov             x1, x0
    // 0x14d7d6c: r0 = Instance_Axis
    //     0x14d7d6c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x14d7d70: stur            x1, [fp, #-0x18]
    // 0x14d7d74: StoreField: r1->field_f = r0
    //     0x14d7d74: stur            w0, [x1, #0xf]
    // 0x14d7d78: r0 = Instance_MainAxisAlignment
    //     0x14d7d78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x14d7d7c: ldr             x0, [x0, #0xa08]
    // 0x14d7d80: StoreField: r1->field_13 = r0
    //     0x14d7d80: stur            w0, [x1, #0x13]
    // 0x14d7d84: r0 = Instance_MainAxisSize
    //     0x14d7d84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x14d7d88: ldr             x0, [x0, #0xa10]
    // 0x14d7d8c: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d7d8c: stur            w0, [x1, #0x17]
    // 0x14d7d90: r0 = Instance_CrossAxisAlignment
    //     0x14d7d90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x14d7d94: ldr             x0, [x0, #0xa18]
    // 0x14d7d98: StoreField: r1->field_1b = r0
    //     0x14d7d98: stur            w0, [x1, #0x1b]
    // 0x14d7d9c: r0 = Instance_VerticalDirection
    //     0x14d7d9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x14d7da0: ldr             x0, [x0, #0xa20]
    // 0x14d7da4: StoreField: r1->field_23 = r0
    //     0x14d7da4: stur            w0, [x1, #0x23]
    // 0x14d7da8: r0 = Instance_Clip
    //     0x14d7da8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x14d7dac: ldr             x0, [x0, #0x38]
    // 0x14d7db0: StoreField: r1->field_2b = r0
    //     0x14d7db0: stur            w0, [x1, #0x2b]
    // 0x14d7db4: StoreField: r1->field_2f = rZR
    //     0x14d7db4: stur            xzr, [x1, #0x2f]
    // 0x14d7db8: ldur            x0, [fp, #-0x10]
    // 0x14d7dbc: StoreField: r1->field_b = r0
    //     0x14d7dbc: stur            w0, [x1, #0xb]
    // 0x14d7dc0: r0 = SafeArea()
    //     0x14d7dc0: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0x14d7dc4: mov             x1, x0
    // 0x14d7dc8: r0 = true
    //     0x14d7dc8: add             x0, NULL, #0x20  ; true
    // 0x14d7dcc: stur            x1, [fp, #-0x10]
    // 0x14d7dd0: StoreField: r1->field_b = r0
    //     0x14d7dd0: stur            w0, [x1, #0xb]
    // 0x14d7dd4: StoreField: r1->field_f = r0
    //     0x14d7dd4: stur            w0, [x1, #0xf]
    // 0x14d7dd8: StoreField: r1->field_13 = r0
    //     0x14d7dd8: stur            w0, [x1, #0x13]
    // 0x14d7ddc: ArrayStore: r1[0] = r0  ; List_4
    //     0x14d7ddc: stur            w0, [x1, #0x17]
    // 0x14d7de0: r0 = Instance_EdgeInsets
    //     0x14d7de0: ldr             x0, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0x14d7de4: StoreField: r1->field_1b = r0
    //     0x14d7de4: stur            w0, [x1, #0x1b]
    // 0x14d7de8: r0 = false
    //     0x14d7de8: add             x0, NULL, #0x30  ; false
    // 0x14d7dec: StoreField: r1->field_1f = r0
    //     0x14d7dec: stur            w0, [x1, #0x1f]
    // 0x14d7df0: ldur            x0, [fp, #-0x18]
    // 0x14d7df4: StoreField: r1->field_23 = r0
    //     0x14d7df4: stur            w0, [x1, #0x23]
    // 0x14d7df8: r0 = WillPopScope()
    //     0x14d7df8: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14d7dfc: mov             x3, x0
    // 0x14d7e00: ldur            x0, [fp, #-0x10]
    // 0x14d7e04: stur            x3, [fp, #-0x18]
    // 0x14d7e08: StoreField: r3->field_b = r0
    //     0x14d7e08: stur            w0, [x3, #0xb]
    // 0x14d7e0c: ldur            x2, [fp, #-8]
    // 0x14d7e10: r1 = Function 'getBack':.
    //     0x14d7e10: add             x1, PP, #0x41, lsl #12  ; [pp+0x41300] AnonymousClosure: (0x14d7e3c), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack (0x14d7e74)
    //     0x14d7e14: ldr             x1, [x1, #0x300]
    // 0x14d7e18: r0 = AllocateClosure()
    //     0x14d7e18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7e1c: mov             x1, x0
    // 0x14d7e20: ldur            x0, [fp, #-0x18]
    // 0x14d7e24: StoreField: r0->field_f = r1
    //     0x14d7e24: stur            w1, [x0, #0xf]
    // 0x14d7e28: LeaveFrame
    //     0x14d7e28: mov             SP, fp
    //     0x14d7e2c: ldp             fp, lr, [SP], #0x10
    // 0x14d7e30: ret
    //     0x14d7e30: ret             
    // 0x14d7e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7e34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7e38: b               #0x14d7c44
  }
  [closure] Future<bool> getBack(dynamic) {
    // ** addr: 0x14d7e3c, size: 0x38
    // 0x14d7e3c: EnterFrame
    //     0x14d7e3c: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7e40: mov             fp, SP
    // 0x14d7e44: ldr             x0, [fp, #0x10]
    // 0x14d7e48: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x14d7e48: ldur            w1, [x0, #0x17]
    // 0x14d7e4c: DecompressPointer r1
    //     0x14d7e4c: add             x1, x1, HEAP, lsl #32
    // 0x14d7e50: CheckStackOverflow
    //     0x14d7e50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7e54: cmp             SP, x16
    //     0x14d7e58: b.ls            #0x14d7e6c
    // 0x14d7e5c: r0 = getBack()
    //     0x14d7e5c: bl              #0x14d7e74  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack
    // 0x14d7e60: LeaveFrame
    //     0x14d7e60: mov             SP, fp
    //     0x14d7e64: ldp             fp, lr, [SP], #0x10
    // 0x14d7e68: ret
    //     0x14d7e68: ret             
    // 0x14d7e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7e6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7e70: b               #0x14d7e5c
  }
  _ getBack(/* No info */) {
    // ** addr: 0x14d7e74, size: 0xb0
    // 0x14d7e74: EnterFrame
    //     0x14d7e74: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7e78: mov             fp, SP
    // 0x14d7e7c: AllocStack(0x8)
    //     0x14d7e7c: sub             SP, SP, #8
    // 0x14d7e80: CheckStackOverflow
    //     0x14d7e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7e84: cmp             SP, x16
    //     0x14d7e88: b.ls            #0x14d7f1c
    // 0x14d7e8c: r2 = false
    //     0x14d7e8c: add             x2, NULL, #0x30  ; false
    // 0x14d7e90: r0 = showLoading()
    //     0x14d7e90: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x14d7e94: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x14d7e94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d7e98: ldr             x0, [x0, #0x1c80]
    //     0x14d7e9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d7ea0: cmp             w0, w16
    //     0x14d7ea4: b.ne            #0x14d7eb0
    //     0x14d7ea8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x14d7eac: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x14d7eb0: r1 = Function '<anonymous closure>':.
    //     0x14d7eb0: add             x1, PP, #0x41, lsl #12  ; [pp+0x41308] AnonymousClosure: (0x1394804), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack (0x13949e8)
    //     0x14d7eb4: ldr             x1, [x1, #0x308]
    // 0x14d7eb8: r2 = Null
    //     0x14d7eb8: mov             x2, NULL
    // 0x14d7ebc: r0 = AllocateClosure()
    //     0x14d7ebc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14d7ec0: mov             x1, x0
    // 0x14d7ec4: r0 = GetNavigation.until()
    //     0x14d7ec4: bl              #0x12f9dc4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.until
    // 0x14d7ec8: r1 = <bool>
    //     0x14d7ec8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x14d7ecc: r0 = _Future()
    //     0x14d7ecc: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x14d7ed0: stur            x0, [fp, #-8]
    // 0x14d7ed4: StoreField: r0->field_b = rZR
    //     0x14d7ed4: stur            xzr, [x0, #0xb]
    // 0x14d7ed8: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x14d7ed8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x14d7edc: ldr             x0, [x0, #0x778]
    //     0x14d7ee0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x14d7ee4: cmp             w0, w16
    //     0x14d7ee8: b.ne            #0x14d7ef4
    //     0x14d7eec: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x14d7ef0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x14d7ef4: mov             x1, x0
    // 0x14d7ef8: ldur            x0, [fp, #-8]
    // 0x14d7efc: StoreField: r0->field_13 = r1
    //     0x14d7efc: stur            w1, [x0, #0x13]
    // 0x14d7f00: mov             x1, x0
    // 0x14d7f04: r2 = false
    //     0x14d7f04: add             x2, NULL, #0x30  ; false
    // 0x14d7f08: r0 = _asyncComplete()
    //     0x14d7f08: bl              #0x618bf0  ; [dart:async] _Future::_asyncComplete
    // 0x14d7f0c: ldur            x0, [fp, #-8]
    // 0x14d7f10: LeaveFrame
    //     0x14d7f10: mov             SP, fp
    //     0x14d7f14: ldp             fp, lr, [SP], #0x10
    // 0x14d7f18: ret
    //     0x14d7f18: ret             
    // 0x14d7f1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7f1c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7f20: b               #0x14d7e8c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x14d7f24, size: 0x84
    // 0x14d7f24: EnterFrame
    //     0x14d7f24: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7f28: mov             fp, SP
    // 0x14d7f2c: AllocStack(0x10)
    //     0x14d7f2c: sub             SP, SP, #0x10
    // 0x14d7f30: SetupParameters()
    //     0x14d7f30: ldr             x0, [fp, #0x10]
    //     0x14d7f34: ldur            w2, [x0, #0x17]
    //     0x14d7f38: add             x2, x2, HEAP, lsl #32
    //     0x14d7f3c: stur            x2, [fp, #-0x10]
    // 0x14d7f40: CheckStackOverflow
    //     0x14d7f40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7f44: cmp             SP, x16
    //     0x14d7f48: b.ls            #0x14d7fa0
    // 0x14d7f4c: LoadField: r0 = r2->field_13
    //     0x14d7f4c: ldur            w0, [x2, #0x13]
    // 0x14d7f50: DecompressPointer r0
    //     0x14d7f50: add             x0, x0, HEAP, lsl #32
    // 0x14d7f54: stur            x0, [fp, #-8]
    // 0x14d7f58: LoadField: r1 = r2->field_f
    //     0x14d7f58: ldur            w1, [x2, #0xf]
    // 0x14d7f5c: DecompressPointer r1
    //     0x14d7f5c: add             x1, x1, HEAP, lsl #32
    // 0x14d7f60: r0 = controller()
    //     0x14d7f60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d7f64: LoadField: r1 = r0->field_ab
    //     0x14d7f64: ldur            w1, [x0, #0xab]
    // 0x14d7f68: DecompressPointer r1
    //     0x14d7f68: add             x1, x1, HEAP, lsl #32
    // 0x14d7f6c: r0 = value()
    //     0x14d7f6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7f70: mov             x1, x0
    // 0x14d7f74: ldur            x0, [fp, #-0x10]
    // 0x14d7f78: LoadField: r2 = r0->field_f
    //     0x14d7f78: ldur            w2, [x0, #0xf]
    // 0x14d7f7c: DecompressPointer r2
    //     0x14d7f7c: add             x2, x2, HEAP, lsl #32
    // 0x14d7f80: mov             x3, x1
    // 0x14d7f84: mov             x1, x2
    // 0x14d7f88: ldur            x2, [fp, #-8]
    // 0x14d7f8c: r0 = _showEditPhoneBottomSheet()
    //     0x14d7f8c: bl              #0x1394ed4  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::_showEditPhoneBottomSheet
    // 0x14d7f90: r0 = Null
    //     0x14d7f90: mov             x0, NULL
    // 0x14d7f94: LeaveFrame
    //     0x14d7f94: mov             SP, fp
    //     0x14d7f98: ldp             fp, lr, [SP], #0x10
    // 0x14d7f9c: ret
    //     0x14d7f9c: ret             
    // 0x14d7fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d7fa0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d7fa4: b               #0x14d7f4c
  }
  [closure] CheckoutBreadCrumb <anonymous closure>(dynamic) {
    // ** addr: 0x14d7fa8, size: 0x64
    // 0x14d7fa8: EnterFrame
    //     0x14d7fa8: stp             fp, lr, [SP, #-0x10]!
    //     0x14d7fac: mov             fp, SP
    // 0x14d7fb0: AllocStack(0x8)
    //     0x14d7fb0: sub             SP, SP, #8
    // 0x14d7fb4: SetupParameters()
    //     0x14d7fb4: ldr             x0, [fp, #0x10]
    //     0x14d7fb8: ldur            w1, [x0, #0x17]
    //     0x14d7fbc: add             x1, x1, HEAP, lsl #32
    // 0x14d7fc0: CheckStackOverflow
    //     0x14d7fc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x14d7fc4: cmp             SP, x16
    //     0x14d7fc8: b.ls            #0x14d8004
    // 0x14d7fcc: LoadField: r0 = r1->field_f
    //     0x14d7fcc: ldur            w0, [x1, #0xf]
    // 0x14d7fd0: DecompressPointer r0
    //     0x14d7fd0: add             x0, x0, HEAP, lsl #32
    // 0x14d7fd4: mov             x1, x0
    // 0x14d7fd8: r0 = controller()
    //     0x14d7fd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x14d7fdc: LoadField: r1 = r0->field_97
    //     0x14d7fdc: ldur            w1, [x0, #0x97]
    // 0x14d7fe0: DecompressPointer r1
    //     0x14d7fe0: add             x1, x1, HEAP, lsl #32
    // 0x14d7fe4: r0 = value()
    //     0x14d7fe4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x14d7fe8: stur            x0, [fp, #-8]
    // 0x14d7fec: r0 = CheckoutBreadCrumb()
    //     0x14d7fec: bl              #0x13939f4  ; AllocateCheckoutBreadCrumbStub -> CheckoutBreadCrumb (size=0x10)
    // 0x14d7ff0: ldur            x1, [fp, #-8]
    // 0x14d7ff4: StoreField: r0->field_b = r1
    //     0x14d7ff4: stur            w1, [x0, #0xb]
    // 0x14d7ff8: LeaveFrame
    //     0x14d7ff8: mov             SP, fp
    //     0x14d7ffc: ldp             fp, lr, [SP], #0x10
    // 0x14d8000: ret
    //     0x14d8000: ret             
    // 0x14d8004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x14d8004: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x14d8008: b               #0x14d7fcc
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15e0924, size: 0x250
    // 0x15e0924: EnterFrame
    //     0x15e0924: stp             fp, lr, [SP, #-0x10]!
    //     0x15e0928: mov             fp, SP
    // 0x15e092c: AllocStack(0x28)
    //     0x15e092c: sub             SP, SP, #0x28
    // 0x15e0930: SetupParameters(CheckoutRequestOtpPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15e0930: stur            x1, [fp, #-8]
    //     0x15e0934: stur            x2, [fp, #-0x10]
    // 0x15e0938: CheckStackOverflow
    //     0x15e0938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e093c: cmp             SP, x16
    //     0x15e0940: b.ls            #0x15e0b6c
    // 0x15e0944: r1 = 2
    //     0x15e0944: movz            x1, #0x2
    // 0x15e0948: r0 = AllocateContext()
    //     0x15e0948: bl              #0x16f6108  ; AllocateContextStub
    // 0x15e094c: ldur            x1, [fp, #-8]
    // 0x15e0950: stur            x0, [fp, #-0x18]
    // 0x15e0954: StoreField: r0->field_f = r1
    //     0x15e0954: stur            w1, [x0, #0xf]
    // 0x15e0958: ldur            x2, [fp, #-0x10]
    // 0x15e095c: StoreField: r0->field_13 = r2
    //     0x15e095c: stur            w2, [x0, #0x13]
    // 0x15e0960: r0 = Obx()
    //     0x15e0960: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15e0964: ldur            x2, [fp, #-0x18]
    // 0x15e0968: r1 = Function '<anonymous closure>':.
    //     0x15e0968: add             x1, PP, #0x41, lsl #12  ; [pp+0x41320] AnonymousClosure: (0x15cd5f8), in [package:customer_app/app/presentation/views/line/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::appBar (0x15e8698)
    //     0x15e096c: ldr             x1, [x1, #0x320]
    // 0x15e0970: stur            x0, [fp, #-0x10]
    // 0x15e0974: r0 = AllocateClosure()
    //     0x15e0974: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e0978: mov             x1, x0
    // 0x15e097c: ldur            x0, [fp, #-0x10]
    // 0x15e0980: StoreField: r0->field_b = r1
    //     0x15e0980: stur            w1, [x0, #0xb]
    // 0x15e0984: ldur            x1, [fp, #-8]
    // 0x15e0988: r0 = controller()
    //     0x15e0988: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e098c: LoadField: r1 = r0->field_a3
    //     0x15e098c: ldur            w1, [x0, #0xa3]
    // 0x15e0990: DecompressPointer r1
    //     0x15e0990: add             x1, x1, HEAP, lsl #32
    // 0x15e0994: r0 = value()
    //     0x15e0994: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e0998: tbnz            w0, #4, #0x15e0a30
    // 0x15e099c: ldur            x2, [fp, #-0x18]
    // 0x15e09a0: LoadField: r1 = r2->field_13
    //     0x15e09a0: ldur            w1, [x2, #0x13]
    // 0x15e09a4: DecompressPointer r1
    //     0x15e09a4: add             x1, x1, HEAP, lsl #32
    // 0x15e09a8: r0 = of()
    //     0x15e09a8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e09ac: LoadField: r1 = r0->field_5b
    //     0x15e09ac: ldur            w1, [x0, #0x5b]
    // 0x15e09b0: DecompressPointer r1
    //     0x15e09b0: add             x1, x1, HEAP, lsl #32
    // 0x15e09b4: stur            x1, [fp, #-8]
    // 0x15e09b8: r0 = ColorFilter()
    //     0x15e09b8: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e09bc: mov             x1, x0
    // 0x15e09c0: ldur            x0, [fp, #-8]
    // 0x15e09c4: stur            x1, [fp, #-0x20]
    // 0x15e09c8: StoreField: r1->field_7 = r0
    //     0x15e09c8: stur            w0, [x1, #7]
    // 0x15e09cc: r0 = Instance_BlendMode
    //     0x15e09cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e09d0: ldr             x0, [x0, #0xb30]
    // 0x15e09d4: StoreField: r1->field_b = r0
    //     0x15e09d4: stur            w0, [x1, #0xb]
    // 0x15e09d8: r2 = 1
    //     0x15e09d8: movz            x2, #0x1
    // 0x15e09dc: StoreField: r1->field_13 = r2
    //     0x15e09dc: stur            x2, [x1, #0x13]
    // 0x15e09e0: r0 = SvgPicture()
    //     0x15e09e0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e09e4: stur            x0, [fp, #-8]
    // 0x15e09e8: ldur            x16, [fp, #-0x20]
    // 0x15e09ec: str             x16, [SP]
    // 0x15e09f0: mov             x1, x0
    // 0x15e09f4: r2 = "assets/images/search.svg"
    //     0x15e09f4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15e09f8: ldr             x2, [x2, #0xa30]
    // 0x15e09fc: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e09fc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e0a00: ldr             x4, [x4, #0xa38]
    // 0x15e0a04: r0 = SvgPicture.asset()
    //     0x15e0a04: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e0a08: r0 = Align()
    //     0x15e0a08: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e0a0c: r3 = Instance_Alignment
    //     0x15e0a0c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e0a10: ldr             x3, [x3, #0xb10]
    // 0x15e0a14: StoreField: r0->field_f = r3
    //     0x15e0a14: stur            w3, [x0, #0xf]
    // 0x15e0a18: r4 = 1.000000
    //     0x15e0a18: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e0a1c: StoreField: r0->field_13 = r4
    //     0x15e0a1c: stur            w4, [x0, #0x13]
    // 0x15e0a20: ArrayStore: r0[0] = r4  ; List_4
    //     0x15e0a20: stur            w4, [x0, #0x17]
    // 0x15e0a24: ldur            x1, [fp, #-8]
    // 0x15e0a28: StoreField: r0->field_b = r1
    //     0x15e0a28: stur            w1, [x0, #0xb]
    // 0x15e0a2c: b               #0x15e0ae0
    // 0x15e0a30: ldur            x5, [fp, #-0x18]
    // 0x15e0a34: r4 = 1.000000
    //     0x15e0a34: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e0a38: r0 = Instance_BlendMode
    //     0x15e0a38: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e0a3c: ldr             x0, [x0, #0xb30]
    // 0x15e0a40: r3 = Instance_Alignment
    //     0x15e0a40: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e0a44: ldr             x3, [x3, #0xb10]
    // 0x15e0a48: r2 = 1
    //     0x15e0a48: movz            x2, #0x1
    // 0x15e0a4c: LoadField: r1 = r5->field_13
    //     0x15e0a4c: ldur            w1, [x5, #0x13]
    // 0x15e0a50: DecompressPointer r1
    //     0x15e0a50: add             x1, x1, HEAP, lsl #32
    // 0x15e0a54: r0 = of()
    //     0x15e0a54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15e0a58: LoadField: r1 = r0->field_5b
    //     0x15e0a58: ldur            w1, [x0, #0x5b]
    // 0x15e0a5c: DecompressPointer r1
    //     0x15e0a5c: add             x1, x1, HEAP, lsl #32
    // 0x15e0a60: stur            x1, [fp, #-8]
    // 0x15e0a64: r0 = ColorFilter()
    //     0x15e0a64: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15e0a68: mov             x1, x0
    // 0x15e0a6c: ldur            x0, [fp, #-8]
    // 0x15e0a70: stur            x1, [fp, #-0x20]
    // 0x15e0a74: StoreField: r1->field_7 = r0
    //     0x15e0a74: stur            w0, [x1, #7]
    // 0x15e0a78: r0 = Instance_BlendMode
    //     0x15e0a78: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15e0a7c: ldr             x0, [x0, #0xb30]
    // 0x15e0a80: StoreField: r1->field_b = r0
    //     0x15e0a80: stur            w0, [x1, #0xb]
    // 0x15e0a84: r0 = 1
    //     0x15e0a84: movz            x0, #0x1
    // 0x15e0a88: StoreField: r1->field_13 = r0
    //     0x15e0a88: stur            x0, [x1, #0x13]
    // 0x15e0a8c: r0 = SvgPicture()
    //     0x15e0a8c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15e0a90: stur            x0, [fp, #-8]
    // 0x15e0a94: ldur            x16, [fp, #-0x20]
    // 0x15e0a98: str             x16, [SP]
    // 0x15e0a9c: mov             x1, x0
    // 0x15e0aa0: r2 = "assets/images/appbar_arrow.svg"
    //     0x15e0aa0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15e0aa4: ldr             x2, [x2, #0xa40]
    // 0x15e0aa8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15e0aa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15e0aac: ldr             x4, [x4, #0xa38]
    // 0x15e0ab0: r0 = SvgPicture.asset()
    //     0x15e0ab0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15e0ab4: r0 = Align()
    //     0x15e0ab4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15e0ab8: mov             x1, x0
    // 0x15e0abc: r0 = Instance_Alignment
    //     0x15e0abc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15e0ac0: ldr             x0, [x0, #0xb10]
    // 0x15e0ac4: StoreField: r1->field_f = r0
    //     0x15e0ac4: stur            w0, [x1, #0xf]
    // 0x15e0ac8: r0 = 1.000000
    //     0x15e0ac8: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15e0acc: StoreField: r1->field_13 = r0
    //     0x15e0acc: stur            w0, [x1, #0x13]
    // 0x15e0ad0: ArrayStore: r1[0] = r0  ; List_4
    //     0x15e0ad0: stur            w0, [x1, #0x17]
    // 0x15e0ad4: ldur            x0, [fp, #-8]
    // 0x15e0ad8: StoreField: r1->field_b = r0
    //     0x15e0ad8: stur            w0, [x1, #0xb]
    // 0x15e0adc: mov             x0, x1
    // 0x15e0ae0: stur            x0, [fp, #-8]
    // 0x15e0ae4: r0 = InkWell()
    //     0x15e0ae4: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15e0ae8: mov             x3, x0
    // 0x15e0aec: ldur            x0, [fp, #-8]
    // 0x15e0af0: stur            x3, [fp, #-0x20]
    // 0x15e0af4: StoreField: r3->field_b = r0
    //     0x15e0af4: stur            w0, [x3, #0xb]
    // 0x15e0af8: ldur            x2, [fp, #-0x18]
    // 0x15e0afc: r1 = Function '<anonymous closure>':.
    //     0x15e0afc: add             x1, PP, #0x41, lsl #12  ; [pp+0x41328] AnonymousClosure: (0x15e0b74), in [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::appBar (0x15e0924)
    //     0x15e0b00: ldr             x1, [x1, #0x328]
    // 0x15e0b04: r0 = AllocateClosure()
    //     0x15e0b04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15e0b08: ldur            x2, [fp, #-0x20]
    // 0x15e0b0c: StoreField: r2->field_f = r0
    //     0x15e0b0c: stur            w0, [x2, #0xf]
    // 0x15e0b10: r0 = true
    //     0x15e0b10: add             x0, NULL, #0x20  ; true
    // 0x15e0b14: StoreField: r2->field_43 = r0
    //     0x15e0b14: stur            w0, [x2, #0x43]
    // 0x15e0b18: r1 = Instance_BoxShape
    //     0x15e0b18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15e0b1c: ldr             x1, [x1, #0x80]
    // 0x15e0b20: StoreField: r2->field_47 = r1
    //     0x15e0b20: stur            w1, [x2, #0x47]
    // 0x15e0b24: StoreField: r2->field_6f = r0
    //     0x15e0b24: stur            w0, [x2, #0x6f]
    // 0x15e0b28: r1 = false
    //     0x15e0b28: add             x1, NULL, #0x30  ; false
    // 0x15e0b2c: StoreField: r2->field_73 = r1
    //     0x15e0b2c: stur            w1, [x2, #0x73]
    // 0x15e0b30: StoreField: r2->field_83 = r0
    //     0x15e0b30: stur            w0, [x2, #0x83]
    // 0x15e0b34: StoreField: r2->field_7b = r1
    //     0x15e0b34: stur            w1, [x2, #0x7b]
    // 0x15e0b38: r0 = AppBar()
    //     0x15e0b38: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15e0b3c: stur            x0, [fp, #-8]
    // 0x15e0b40: ldur            x16, [fp, #-0x10]
    // 0x15e0b44: str             x16, [SP]
    // 0x15e0b48: mov             x1, x0
    // 0x15e0b4c: ldur            x2, [fp, #-0x20]
    // 0x15e0b50: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15e0b50: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15e0b54: ldr             x4, [x4, #0xf00]
    // 0x15e0b58: r0 = AppBar()
    //     0x15e0b58: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15e0b5c: ldur            x0, [fp, #-8]
    // 0x15e0b60: LeaveFrame
    //     0x15e0b60: mov             SP, fp
    //     0x15e0b64: ldp             fp, lr, [SP], #0x10
    // 0x15e0b68: ret
    //     0x15e0b68: ret             
    // 0x15e0b6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e0b6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e0b70: b               #0x15e0944
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15e0b74, size: 0xc8
    // 0x15e0b74: EnterFrame
    //     0x15e0b74: stp             fp, lr, [SP, #-0x10]!
    //     0x15e0b78: mov             fp, SP
    // 0x15e0b7c: AllocStack(0x18)
    //     0x15e0b7c: sub             SP, SP, #0x18
    // 0x15e0b80: SetupParameters()
    //     0x15e0b80: ldr             x0, [fp, #0x10]
    //     0x15e0b84: ldur            w3, [x0, #0x17]
    //     0x15e0b88: add             x3, x3, HEAP, lsl #32
    //     0x15e0b8c: stur            x3, [fp, #-8]
    // 0x15e0b90: CheckStackOverflow
    //     0x15e0b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15e0b94: cmp             SP, x16
    //     0x15e0b98: b.ls            #0x15e0c34
    // 0x15e0b9c: LoadField: r1 = r3->field_f
    //     0x15e0b9c: ldur            w1, [x3, #0xf]
    // 0x15e0ba0: DecompressPointer r1
    //     0x15e0ba0: add             x1, x1, HEAP, lsl #32
    // 0x15e0ba4: r2 = false
    //     0x15e0ba4: add             x2, NULL, #0x30  ; false
    // 0x15e0ba8: r0 = showLoading()
    //     0x15e0ba8: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e0bac: ldur            x0, [fp, #-8]
    // 0x15e0bb0: LoadField: r1 = r0->field_f
    //     0x15e0bb0: ldur            w1, [x0, #0xf]
    // 0x15e0bb4: DecompressPointer r1
    //     0x15e0bb4: add             x1, x1, HEAP, lsl #32
    // 0x15e0bb8: r0 = controller()
    //     0x15e0bb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15e0bbc: LoadField: r1 = r0->field_a3
    //     0x15e0bbc: ldur            w1, [x0, #0xa3]
    // 0x15e0bc0: DecompressPointer r1
    //     0x15e0bc0: add             x1, x1, HEAP, lsl #32
    // 0x15e0bc4: r0 = value()
    //     0x15e0bc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15e0bc8: tbnz            w0, #4, #0x15e0c00
    // 0x15e0bcc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x15e0bcc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x15e0bd0: ldr             x0, [x0, #0x1c80]
    //     0x15e0bd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x15e0bd8: cmp             w0, w16
    //     0x15e0bdc: b.ne            #0x15e0be8
    //     0x15e0be0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x15e0be4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x15e0be8: r16 = "/search"
    //     0x15e0be8: add             x16, PP, #0xd, lsl #12  ; [pp+0xd838] "/search"
    //     0x15e0bec: ldr             x16, [x16, #0x838]
    // 0x15e0bf0: stp             x16, NULL, [SP]
    // 0x15e0bf4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x15e0bf4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x15e0bf8: r0 = GetNavigation.toNamed()
    //     0x15e0bf8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x15e0bfc: b               #0x15e0c24
    // 0x15e0c00: ldur            x0, [fp, #-8]
    // 0x15e0c04: LoadField: r1 = r0->field_f
    //     0x15e0c04: ldur            w1, [x0, #0xf]
    // 0x15e0c08: DecompressPointer r1
    //     0x15e0c08: add             x1, x1, HEAP, lsl #32
    // 0x15e0c0c: r2 = false
    //     0x15e0c0c: add             x2, NULL, #0x30  ; false
    // 0x15e0c10: r0 = showLoading()
    //     0x15e0c10: bl              #0x9cd3ac  ; [package:customer_app/app/core/base/base_view.dart] BaseView::showLoading
    // 0x15e0c14: ldur            x0, [fp, #-8]
    // 0x15e0c18: LoadField: r1 = r0->field_f
    //     0x15e0c18: ldur            w1, [x0, #0xf]
    // 0x15e0c1c: DecompressPointer r1
    //     0x15e0c1c: add             x1, x1, HEAP, lsl #32
    // 0x15e0c20: r0 = getBack()
    //     0x15e0c20: bl              #0x14d7e74  ; [package:customer_app/app/presentation/views/glass/checkout_variant_revamp/checkout_request_otp_page.dart] CheckoutRequestOtpPage::getBack
    // 0x15e0c24: r0 = Null
    //     0x15e0c24: mov             x0, NULL
    // 0x15e0c28: LeaveFrame
    //     0x15e0c28: mov             SP, fp
    //     0x15e0c2c: ldp             fp, lr, [SP], #0x10
    // 0x15e0c30: ret
    //     0x15e0c30: ret             
    // 0x15e0c34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15e0c34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15e0c38: b               #0x15e0b9c
  }
}
