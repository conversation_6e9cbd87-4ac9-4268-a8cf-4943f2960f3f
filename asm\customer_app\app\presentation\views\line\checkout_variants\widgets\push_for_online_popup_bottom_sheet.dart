// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/push_for_online_popup_bottom_sheet.dart

// class id: 1049500, size: 0x8
class :: {
}

// class id: 3262, size: 0x14, field offset: 0x14
class _PartialCodPopupBottomSheet extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbd35b0, size: 0xcac
    // 0xbd35b0: EnterFrame
    //     0xbd35b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbd35b4: mov             fp, SP
    // 0xbd35b8: AllocStack(0x88)
    //     0xbd35b8: sub             SP, SP, #0x88
    // 0xbd35bc: SetupParameters(_PartialCodPopupBottomSheet this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbd35bc: mov             x0, x1
    //     0xbd35c0: stur            x1, [fp, #-8]
    //     0xbd35c4: mov             x1, x2
    //     0xbd35c8: stur            x2, [fp, #-0x10]
    // 0xbd35cc: CheckStackOverflow
    //     0xbd35cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd35d0: cmp             SP, x16
    //     0xbd35d4: b.ls            #0xbd421c
    // 0xbd35d8: r1 = 1
    //     0xbd35d8: movz            x1, #0x1
    // 0xbd35dc: r0 = AllocateContext()
    //     0xbd35dc: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd35e0: mov             x1, x0
    // 0xbd35e4: ldur            x0, [fp, #-8]
    // 0xbd35e8: stur            x1, [fp, #-0x18]
    // 0xbd35ec: StoreField: r1->field_f = r0
    //     0xbd35ec: stur            w0, [x1, #0xf]
    // 0xbd35f0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbd35f0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd35f4: ldr             x0, [x0, #0x1c80]
    //     0xbd35f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd35fc: cmp             w0, w16
    //     0xbd3600: b.ne            #0xbd360c
    //     0xbd3604: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbd3608: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd360c: r0 = GetNavigation.size()
    //     0xbd360c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbd3610: LoadField: d0 = r0->field_f
    //     0xbd3610: ldur            d0, [x0, #0xf]
    // 0xbd3614: d1 = 0.300000
    //     0xbd3614: add             x17, PP, #0x27, lsl #12  ; [pp+0x27658] IMM: double(0.3) from 0x3fd3333333333333
    //     0xbd3618: ldr             d1, [x17, #0x658]
    // 0xbd361c: fmul            d2, d0, d1
    // 0xbd3620: ldur            x0, [fp, #-8]
    // 0xbd3624: stur            d2, [fp, #-0x70]
    // 0xbd3628: LoadField: r1 = r0->field_b
    //     0xbd3628: ldur            w1, [x0, #0xb]
    // 0xbd362c: DecompressPointer r1
    //     0xbd362c: add             x1, x1, HEAP, lsl #32
    // 0xbd3630: cmp             w1, NULL
    // 0xbd3634: b.eq            #0xbd4224
    // 0xbd3638: LoadField: r2 = r1->field_b
    //     0xbd3638: ldur            w2, [x1, #0xb]
    // 0xbd363c: DecompressPointer r2
    //     0xbd363c: add             x2, x2, HEAP, lsl #32
    // 0xbd3640: LoadField: r1 = r2->field_7
    //     0xbd3640: ldur            w1, [x2, #7]
    // 0xbd3644: DecompressPointer r1
    //     0xbd3644: add             x1, x1, HEAP, lsl #32
    // 0xbd3648: cmp             w1, NULL
    // 0xbd364c: b.ne            #0xbd3658
    // 0xbd3650: r2 = ""
    //     0xbd3650: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd3654: b               #0xbd365c
    // 0xbd3658: mov             x2, x1
    // 0xbd365c: ldur            x1, [fp, #-0x10]
    // 0xbd3660: stur            x2, [fp, #-0x20]
    // 0xbd3664: r0 = of()
    //     0xbd3664: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd3668: LoadField: r1 = r0->field_87
    //     0xbd3668: ldur            w1, [x0, #0x87]
    // 0xbd366c: DecompressPointer r1
    //     0xbd366c: add             x1, x1, HEAP, lsl #32
    // 0xbd3670: LoadField: r0 = r1->field_27
    //     0xbd3670: ldur            w0, [x1, #0x27]
    // 0xbd3674: DecompressPointer r0
    //     0xbd3674: add             x0, x0, HEAP, lsl #32
    // 0xbd3678: stur            x0, [fp, #-0x28]
    // 0xbd367c: r1 = Instance_Color
    //     0xbd367c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd3680: d0 = 0.700000
    //     0xbd3680: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd3684: ldr             d0, [x17, #0xf48]
    // 0xbd3688: r0 = withOpacity()
    //     0xbd3688: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd368c: r16 = 21.000000
    //     0xbd368c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xbd3690: ldr             x16, [x16, #0x9b0]
    // 0xbd3694: stp             x16, x0, [SP]
    // 0xbd3698: ldur            x1, [fp, #-0x28]
    // 0xbd369c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbd369c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbd36a0: ldr             x4, [x4, #0x9b8]
    // 0xbd36a4: r0 = copyWith()
    //     0xbd36a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd36a8: stur            x0, [fp, #-0x28]
    // 0xbd36ac: r0 = Text()
    //     0xbd36ac: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd36b0: mov             x2, x0
    // 0xbd36b4: ldur            x0, [fp, #-0x20]
    // 0xbd36b8: stur            x2, [fp, #-0x30]
    // 0xbd36bc: StoreField: r2->field_b = r0
    //     0xbd36bc: stur            w0, [x2, #0xb]
    // 0xbd36c0: ldur            x0, [fp, #-0x28]
    // 0xbd36c4: StoreField: r2->field_13 = r0
    //     0xbd36c4: stur            w0, [x2, #0x13]
    // 0xbd36c8: ldur            x0, [fp, #-8]
    // 0xbd36cc: LoadField: r1 = r0->field_b
    //     0xbd36cc: ldur            w1, [x0, #0xb]
    // 0xbd36d0: DecompressPointer r1
    //     0xbd36d0: add             x1, x1, HEAP, lsl #32
    // 0xbd36d4: cmp             w1, NULL
    // 0xbd36d8: b.eq            #0xbd4228
    // 0xbd36dc: LoadField: r3 = r1->field_b
    //     0xbd36dc: ldur            w3, [x1, #0xb]
    // 0xbd36e0: DecompressPointer r3
    //     0xbd36e0: add             x3, x3, HEAP, lsl #32
    // 0xbd36e4: LoadField: r1 = r3->field_b
    //     0xbd36e4: ldur            w1, [x3, #0xb]
    // 0xbd36e8: DecompressPointer r1
    //     0xbd36e8: add             x1, x1, HEAP, lsl #32
    // 0xbd36ec: cmp             w1, NULL
    // 0xbd36f0: b.ne            #0xbd36fc
    // 0xbd36f4: r3 = ""
    //     0xbd36f4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd36f8: b               #0xbd3700
    // 0xbd36fc: mov             x3, x1
    // 0xbd3700: ldur            x1, [fp, #-0x10]
    // 0xbd3704: stur            x3, [fp, #-0x20]
    // 0xbd3708: r0 = of()
    //     0xbd3708: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd370c: LoadField: r1 = r0->field_87
    //     0xbd370c: ldur            w1, [x0, #0x87]
    // 0xbd3710: DecompressPointer r1
    //     0xbd3710: add             x1, x1, HEAP, lsl #32
    // 0xbd3714: LoadField: r0 = r1->field_2f
    //     0xbd3714: ldur            w0, [x1, #0x2f]
    // 0xbd3718: DecompressPointer r0
    //     0xbd3718: add             x0, x0, HEAP, lsl #32
    // 0xbd371c: cmp             w0, NULL
    // 0xbd3720: b.ne            #0xbd372c
    // 0xbd3724: r3 = Null
    //     0xbd3724: mov             x3, NULL
    // 0xbd3728: b               #0xbd3750
    // 0xbd372c: r16 = 14.000000
    //     0xbd372c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbd3730: ldr             x16, [x16, #0x1d8]
    // 0xbd3734: r30 = Instance_Color
    //     0xbd3734: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd3738: stp             lr, x16, [SP]
    // 0xbd373c: mov             x1, x0
    // 0xbd3740: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd3740: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd3744: ldr             x4, [x4, #0xaa0]
    // 0xbd3748: r0 = copyWith()
    //     0xbd3748: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd374c: mov             x3, x0
    // 0xbd3750: ldur            x1, [fp, #-8]
    // 0xbd3754: ldur            d0, [fp, #-0x70]
    // 0xbd3758: ldur            x0, [fp, #-0x30]
    // 0xbd375c: ldur            x2, [fp, #-0x20]
    // 0xbd3760: stur            x3, [fp, #-0x28]
    // 0xbd3764: r0 = Text()
    //     0xbd3764: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd3768: mov             x1, x0
    // 0xbd376c: ldur            x0, [fp, #-0x20]
    // 0xbd3770: stur            x1, [fp, #-0x38]
    // 0xbd3774: StoreField: r1->field_b = r0
    //     0xbd3774: stur            w0, [x1, #0xb]
    // 0xbd3778: ldur            x0, [fp, #-0x28]
    // 0xbd377c: StoreField: r1->field_13 = r0
    //     0xbd377c: stur            w0, [x1, #0x13]
    // 0xbd3780: r0 = Padding()
    //     0xbd3780: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd3784: mov             x3, x0
    // 0xbd3788: r0 = Instance_EdgeInsets
    //     0xbd3788: add             x0, PP, #0x54, lsl #12  ; [pp+0x542d8] Obj!EdgeInsets@d58251
    //     0xbd378c: ldr             x0, [x0, #0x2d8]
    // 0xbd3790: stur            x3, [fp, #-0x20]
    // 0xbd3794: StoreField: r3->field_f = r0
    //     0xbd3794: stur            w0, [x3, #0xf]
    // 0xbd3798: ldur            x0, [fp, #-0x38]
    // 0xbd379c: StoreField: r3->field_b = r0
    //     0xbd379c: stur            w0, [x3, #0xb]
    // 0xbd37a0: r1 = Null
    //     0xbd37a0: mov             x1, NULL
    // 0xbd37a4: r2 = 4
    //     0xbd37a4: movz            x2, #0x4
    // 0xbd37a8: r0 = AllocateArray()
    //     0xbd37a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd37ac: stur            x0, [fp, #-0x28]
    // 0xbd37b0: r16 = Instance_Padding
    //     0xbd37b0: add             x16, PP, #0x54, lsl #12  ; [pp+0x542e0] Obj!Padding@d68481
    //     0xbd37b4: ldr             x16, [x16, #0x2e0]
    // 0xbd37b8: StoreField: r0->field_f = r16
    //     0xbd37b8: stur            w16, [x0, #0xf]
    // 0xbd37bc: ldur            x1, [fp, #-0x20]
    // 0xbd37c0: StoreField: r0->field_13 = r1
    //     0xbd37c0: stur            w1, [x0, #0x13]
    // 0xbd37c4: r1 = <Widget>
    //     0xbd37c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd37c8: r0 = AllocateGrowableArray()
    //     0xbd37c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd37cc: mov             x1, x0
    // 0xbd37d0: ldur            x0, [fp, #-0x28]
    // 0xbd37d4: stur            x1, [fp, #-0x20]
    // 0xbd37d8: StoreField: r1->field_f = r0
    //     0xbd37d8: stur            w0, [x1, #0xf]
    // 0xbd37dc: r2 = 4
    //     0xbd37dc: movz            x2, #0x4
    // 0xbd37e0: StoreField: r1->field_b = r2
    //     0xbd37e0: stur            w2, [x1, #0xb]
    // 0xbd37e4: r0 = Row()
    //     0xbd37e4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd37e8: mov             x1, x0
    // 0xbd37ec: r0 = Instance_Axis
    //     0xbd37ec: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd37f0: stur            x1, [fp, #-0x28]
    // 0xbd37f4: StoreField: r1->field_f = r0
    //     0xbd37f4: stur            w0, [x1, #0xf]
    // 0xbd37f8: r2 = Instance_MainAxisAlignment
    //     0xbd37f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd37fc: ldr             x2, [x2, #0xa08]
    // 0xbd3800: StoreField: r1->field_13 = r2
    //     0xbd3800: stur            w2, [x1, #0x13]
    // 0xbd3804: r2 = Instance_MainAxisSize
    //     0xbd3804: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbd3808: ldr             x2, [x2, #0xdd0]
    // 0xbd380c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd380c: stur            w2, [x1, #0x17]
    // 0xbd3810: r2 = Instance_CrossAxisAlignment
    //     0xbd3810: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd3814: ldr             x2, [x2, #0xa18]
    // 0xbd3818: StoreField: r1->field_1b = r2
    //     0xbd3818: stur            w2, [x1, #0x1b]
    // 0xbd381c: r3 = Instance_VerticalDirection
    //     0xbd381c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd3820: ldr             x3, [x3, #0xa20]
    // 0xbd3824: StoreField: r1->field_23 = r3
    //     0xbd3824: stur            w3, [x1, #0x23]
    // 0xbd3828: r4 = Instance_Clip
    //     0xbd3828: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd382c: ldr             x4, [x4, #0x38]
    // 0xbd3830: StoreField: r1->field_2b = r4
    //     0xbd3830: stur            w4, [x1, #0x2b]
    // 0xbd3834: StoreField: r1->field_2f = rZR
    //     0xbd3834: stur            xzr, [x1, #0x2f]
    // 0xbd3838: ldur            x5, [fp, #-0x20]
    // 0xbd383c: StoreField: r1->field_b = r5
    //     0xbd383c: stur            w5, [x1, #0xb]
    // 0xbd3840: r0 = Container()
    //     0xbd3840: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbd3844: stur            x0, [fp, #-0x20]
    // 0xbd3848: r16 = 55.000000
    //     0xbd3848: add             x16, PP, #0x53, lsl #12  ; [pp+0x539b8] 55
    //     0xbd384c: ldr             x16, [x16, #0x9b8]
    // 0xbd3850: r30 = Instance_Color
    //     0xbd3850: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbd3854: ldr             lr, [lr, #0x858]
    // 0xbd3858: stp             lr, x16, [SP, #8]
    // 0xbd385c: ldur            x16, [fp, #-0x28]
    // 0xbd3860: str             x16, [SP]
    // 0xbd3864: mov             x1, x0
    // 0xbd3868: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, height, 0x1, null]
    //     0xbd3868: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d50] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "height", 0x1, Null]
    //     0xbd386c: ldr             x4, [x4, #0xd50]
    // 0xbd3870: r0 = Container()
    //     0xbd3870: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbd3874: r0 = SvgPicture()
    //     0xbd3874: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd3878: stur            x0, [fp, #-0x28]
    // 0xbd387c: r16 = Instance_BoxFit
    //     0xbd387c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd3880: ldr             x16, [x16, #0xb18]
    // 0xbd3884: str             x16, [SP]
    // 0xbd3888: mov             x1, x0
    // 0xbd388c: r2 = "assets/images/icons/phonepe.svg"
    //     0xbd388c: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e00] "assets/images/icons/phonepe.svg"
    //     0xbd3890: ldr             x2, [x2, #0xe00]
    // 0xbd3894: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbd3894: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbd3898: ldr             x4, [x4, #0xb0]
    // 0xbd389c: r0 = SvgPicture.asset()
    //     0xbd389c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd38a0: r0 = SvgPicture()
    //     0xbd38a0: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd38a4: stur            x0, [fp, #-0x38]
    // 0xbd38a8: r16 = Instance_BoxFit
    //     0xbd38a8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd38ac: ldr             x16, [x16, #0xb18]
    // 0xbd38b0: str             x16, [SP]
    // 0xbd38b4: mov             x1, x0
    // 0xbd38b8: r2 = "assets/images/icons/google-pay.svg"
    //     0xbd38b8: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e08] "assets/images/icons/google-pay.svg"
    //     0xbd38bc: ldr             x2, [x2, #0xe08]
    // 0xbd38c0: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbd38c0: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbd38c4: ldr             x4, [x4, #0xb0]
    // 0xbd38c8: r0 = SvgPicture.asset()
    //     0xbd38c8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd38cc: r0 = SvgPicture()
    //     0xbd38cc: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd38d0: stur            x0, [fp, #-0x40]
    // 0xbd38d4: r16 = Instance_BoxFit
    //     0xbd38d4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd38d8: ldr             x16, [x16, #0xb18]
    // 0xbd38dc: str             x16, [SP]
    // 0xbd38e0: mov             x1, x0
    // 0xbd38e4: r2 = "assets/images/icons/paytm.svg"
    //     0xbd38e4: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e10] "assets/images/icons/paytm.svg"
    //     0xbd38e8: ldr             x2, [x2, #0xe10]
    // 0xbd38ec: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbd38ec: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbd38f0: ldr             x4, [x4, #0xb0]
    // 0xbd38f4: r0 = SvgPicture.asset()
    //     0xbd38f4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd38f8: r0 = SvgPicture()
    //     0xbd38f8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd38fc: stur            x0, [fp, #-0x48]
    // 0xbd3900: r16 = Instance_BoxFit
    //     0xbd3900: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd3904: ldr             x16, [x16, #0xb18]
    // 0xbd3908: str             x16, [SP]
    // 0xbd390c: mov             x1, x0
    // 0xbd3910: r2 = "assets/images/icons/upi.svg"
    //     0xbd3910: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e18] "assets/images/icons/upi.svg"
    //     0xbd3914: ldr             x2, [x2, #0xe18]
    // 0xbd3918: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbd3918: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbd391c: ldr             x4, [x4, #0xb0]
    // 0xbd3920: r0 = SvgPicture.asset()
    //     0xbd3920: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd3924: r0 = SvgPicture()
    //     0xbd3924: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd3928: stur            x0, [fp, #-0x50]
    // 0xbd392c: r16 = Instance_BoxFit
    //     0xbd392c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd3930: ldr             x16, [x16, #0xb18]
    // 0xbd3934: str             x16, [SP]
    // 0xbd3938: mov             x1, x0
    // 0xbd393c: r2 = "assets/images/icons/rupay.svg"
    //     0xbd393c: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e20] "assets/images/icons/rupay.svg"
    //     0xbd3940: ldr             x2, [x2, #0xe20]
    // 0xbd3944: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbd3944: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbd3948: ldr             x4, [x4, #0xb0]
    // 0xbd394c: r0 = SvgPicture.asset()
    //     0xbd394c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd3950: r0 = SvgPicture()
    //     0xbd3950: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd3954: stur            x0, [fp, #-0x58]
    // 0xbd3958: r16 = Instance_BoxFit
    //     0xbd3958: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd395c: ldr             x16, [x16, #0xb18]
    // 0xbd3960: str             x16, [SP]
    // 0xbd3964: mov             x1, x0
    // 0xbd3968: r2 = "assets/images/icons/mastercard.svg"
    //     0xbd3968: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e28] "assets/images/icons/mastercard.svg"
    //     0xbd396c: ldr             x2, [x2, #0xe28]
    // 0xbd3970: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbd3970: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbd3974: ldr             x4, [x4, #0xb0]
    // 0xbd3978: r0 = SvgPicture.asset()
    //     0xbd3978: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd397c: r0 = SvgPicture()
    //     0xbd397c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbd3980: stur            x0, [fp, #-0x60]
    // 0xbd3984: r16 = Instance_BoxFit
    //     0xbd3984: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbd3988: ldr             x16, [x16, #0xb18]
    // 0xbd398c: str             x16, [SP]
    // 0xbd3990: mov             x1, x0
    // 0xbd3994: r2 = "assets/images/icons/visa.svg"
    //     0xbd3994: add             x2, PP, #0x53, lsl #12  ; [pp+0x53e30] "assets/images/icons/visa.svg"
    //     0xbd3998: ldr             x2, [x2, #0xe30]
    // 0xbd399c: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xbd399c: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xbd39a0: ldr             x4, [x4, #0xb0]
    // 0xbd39a4: r0 = SvgPicture.asset()
    //     0xbd39a4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbd39a8: r1 = Null
    //     0xbd39a8: mov             x1, NULL
    // 0xbd39ac: r2 = 14
    //     0xbd39ac: movz            x2, #0xe
    // 0xbd39b0: r0 = AllocateArray()
    //     0xbd39b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd39b4: mov             x2, x0
    // 0xbd39b8: ldur            x0, [fp, #-0x28]
    // 0xbd39bc: stur            x2, [fp, #-0x68]
    // 0xbd39c0: StoreField: r2->field_f = r0
    //     0xbd39c0: stur            w0, [x2, #0xf]
    // 0xbd39c4: ldur            x0, [fp, #-0x38]
    // 0xbd39c8: StoreField: r2->field_13 = r0
    //     0xbd39c8: stur            w0, [x2, #0x13]
    // 0xbd39cc: ldur            x0, [fp, #-0x40]
    // 0xbd39d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd39d0: stur            w0, [x2, #0x17]
    // 0xbd39d4: ldur            x0, [fp, #-0x48]
    // 0xbd39d8: StoreField: r2->field_1b = r0
    //     0xbd39d8: stur            w0, [x2, #0x1b]
    // 0xbd39dc: ldur            x0, [fp, #-0x50]
    // 0xbd39e0: StoreField: r2->field_1f = r0
    //     0xbd39e0: stur            w0, [x2, #0x1f]
    // 0xbd39e4: ldur            x0, [fp, #-0x58]
    // 0xbd39e8: StoreField: r2->field_23 = r0
    //     0xbd39e8: stur            w0, [x2, #0x23]
    // 0xbd39ec: ldur            x0, [fp, #-0x60]
    // 0xbd39f0: StoreField: r2->field_27 = r0
    //     0xbd39f0: stur            w0, [x2, #0x27]
    // 0xbd39f4: r1 = <Widget>
    //     0xbd39f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd39f8: r0 = AllocateGrowableArray()
    //     0xbd39f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd39fc: mov             x1, x0
    // 0xbd3a00: ldur            x0, [fp, #-0x68]
    // 0xbd3a04: stur            x1, [fp, #-0x28]
    // 0xbd3a08: StoreField: r1->field_f = r0
    //     0xbd3a08: stur            w0, [x1, #0xf]
    // 0xbd3a0c: r0 = 14
    //     0xbd3a0c: movz            x0, #0xe
    // 0xbd3a10: StoreField: r1->field_b = r0
    //     0xbd3a10: stur            w0, [x1, #0xb]
    // 0xbd3a14: r0 = Row()
    //     0xbd3a14: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd3a18: mov             x1, x0
    // 0xbd3a1c: r0 = Instance_Axis
    //     0xbd3a1c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd3a20: stur            x1, [fp, #-0x38]
    // 0xbd3a24: StoreField: r1->field_f = r0
    //     0xbd3a24: stur            w0, [x1, #0xf]
    // 0xbd3a28: r2 = Instance_MainAxisAlignment
    //     0xbd3a28: add             x2, PP, #0x33, lsl #12  ; [pp+0x33f28] Obj!MainAxisAlignment@d734a1
    //     0xbd3a2c: ldr             x2, [x2, #0xf28]
    // 0xbd3a30: StoreField: r1->field_13 = r2
    //     0xbd3a30: stur            w2, [x1, #0x13]
    // 0xbd3a34: r2 = Instance_MainAxisSize
    //     0xbd3a34: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd3a38: ldr             x2, [x2, #0xa10]
    // 0xbd3a3c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd3a3c: stur            w2, [x1, #0x17]
    // 0xbd3a40: r3 = Instance_CrossAxisAlignment
    //     0xbd3a40: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd3a44: ldr             x3, [x3, #0xa18]
    // 0xbd3a48: StoreField: r1->field_1b = r3
    //     0xbd3a48: stur            w3, [x1, #0x1b]
    // 0xbd3a4c: r4 = Instance_VerticalDirection
    //     0xbd3a4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd3a50: ldr             x4, [x4, #0xa20]
    // 0xbd3a54: StoreField: r1->field_23 = r4
    //     0xbd3a54: stur            w4, [x1, #0x23]
    // 0xbd3a58: r5 = Instance_Clip
    //     0xbd3a58: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd3a5c: ldr             x5, [x5, #0x38]
    // 0xbd3a60: StoreField: r1->field_2b = r5
    //     0xbd3a60: stur            w5, [x1, #0x2b]
    // 0xbd3a64: StoreField: r1->field_2f = rZR
    //     0xbd3a64: stur            xzr, [x1, #0x2f]
    // 0xbd3a68: ldur            x6, [fp, #-0x28]
    // 0xbd3a6c: StoreField: r1->field_b = r6
    //     0xbd3a6c: stur            w6, [x1, #0xb]
    // 0xbd3a70: r0 = Padding()
    //     0xbd3a70: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd3a74: mov             x2, x0
    // 0xbd3a78: r0 = Instance_EdgeInsets
    //     0xbd3a78: add             x0, PP, #0x3d, lsl #12  ; [pp+0x3d1c0] Obj!EdgeInsets@d57471
    //     0xbd3a7c: ldr             x0, [x0, #0x1c0]
    // 0xbd3a80: stur            x2, [fp, #-0x28]
    // 0xbd3a84: StoreField: r2->field_f = r0
    //     0xbd3a84: stur            w0, [x2, #0xf]
    // 0xbd3a88: ldur            x0, [fp, #-0x38]
    // 0xbd3a8c: StoreField: r2->field_b = r0
    //     0xbd3a8c: stur            w0, [x2, #0xb]
    // 0xbd3a90: r1 = Instance_Color
    //     0xbd3a90: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd3a94: d0 = 0.100000
    //     0xbd3a94: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbd3a98: r0 = withOpacity()
    //     0xbd3a98: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd3a9c: stur            x0, [fp, #-0x38]
    // 0xbd3aa0: r0 = Divider()
    //     0xbd3aa0: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0xbd3aa4: mov             x3, x0
    // 0xbd3aa8: ldur            x0, [fp, #-0x38]
    // 0xbd3aac: stur            x3, [fp, #-0x40]
    // 0xbd3ab0: StoreField: r3->field_1f = r0
    //     0xbd3ab0: stur            w0, [x3, #0x1f]
    // 0xbd3ab4: r1 = Null
    //     0xbd3ab4: mov             x1, NULL
    // 0xbd3ab8: r2 = 10
    //     0xbd3ab8: movz            x2, #0xa
    // 0xbd3abc: r0 = AllocateArray()
    //     0xbd3abc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd3ac0: mov             x2, x0
    // 0xbd3ac4: ldur            x0, [fp, #-0x30]
    // 0xbd3ac8: stur            x2, [fp, #-0x38]
    // 0xbd3acc: StoreField: r2->field_f = r0
    //     0xbd3acc: stur            w0, [x2, #0xf]
    // 0xbd3ad0: r16 = Instance_SizedBox
    //     0xbd3ad0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbd3ad4: ldr             x16, [x16, #0x578]
    // 0xbd3ad8: StoreField: r2->field_13 = r16
    //     0xbd3ad8: stur            w16, [x2, #0x13]
    // 0xbd3adc: ldur            x0, [fp, #-0x20]
    // 0xbd3ae0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd3ae0: stur            w0, [x2, #0x17]
    // 0xbd3ae4: ldur            x0, [fp, #-0x28]
    // 0xbd3ae8: StoreField: r2->field_1b = r0
    //     0xbd3ae8: stur            w0, [x2, #0x1b]
    // 0xbd3aec: ldur            x0, [fp, #-0x40]
    // 0xbd3af0: StoreField: r2->field_1f = r0
    //     0xbd3af0: stur            w0, [x2, #0x1f]
    // 0xbd3af4: r1 = <Widget>
    //     0xbd3af4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd3af8: r0 = AllocateGrowableArray()
    //     0xbd3af8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd3afc: mov             x1, x0
    // 0xbd3b00: ldur            x0, [fp, #-0x38]
    // 0xbd3b04: stur            x1, [fp, #-0x20]
    // 0xbd3b08: StoreField: r1->field_f = r0
    //     0xbd3b08: stur            w0, [x1, #0xf]
    // 0xbd3b0c: r0 = 10
    //     0xbd3b0c: movz            x0, #0xa
    // 0xbd3b10: StoreField: r1->field_b = r0
    //     0xbd3b10: stur            w0, [x1, #0xb]
    // 0xbd3b14: r0 = ListView()
    //     0xbd3b14: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbd3b18: stur            x0, [fp, #-0x28]
    // 0xbd3b1c: r16 = Instance_BouncingScrollPhysics
    //     0xbd3b1c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xbd3b20: ldr             x16, [x16, #0x890]
    // 0xbd3b24: str             x16, [SP]
    // 0xbd3b28: mov             x1, x0
    // 0xbd3b2c: ldur            x2, [fp, #-0x20]
    // 0xbd3b30: r4 = const [0, 0x3, 0x1, 0x2, physics, 0x2, null]
    //     0xbd3b30: add             x4, PP, #0x54, lsl #12  ; [pp+0x542e8] List(7) [0, 0x3, 0x1, 0x2, "physics", 0x2, Null]
    //     0xbd3b34: ldr             x4, [x4, #0x2e8]
    // 0xbd3b38: r0 = ListView()
    //     0xbd3b38: bl              #0x9df350  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView
    // 0xbd3b3c: ldur            d0, [fp, #-0x70]
    // 0xbd3b40: r0 = inline_Allocate_Double()
    //     0xbd3b40: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xbd3b44: add             x0, x0, #0x10
    //     0xbd3b48: cmp             x1, x0
    //     0xbd3b4c: b.ls            #0xbd422c
    //     0xbd3b50: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd3b54: sub             x0, x0, #0xf
    //     0xbd3b58: movz            x1, #0xe15c
    //     0xbd3b5c: movk            x1, #0x3, lsl #16
    //     0xbd3b60: stur            x1, [x0, #-1]
    // 0xbd3b64: StoreField: r0->field_7 = d0
    //     0xbd3b64: stur            d0, [x0, #7]
    // 0xbd3b68: stur            x0, [fp, #-0x20]
    // 0xbd3b6c: r0 = SizedBox()
    //     0xbd3b6c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbd3b70: mov             x1, x0
    // 0xbd3b74: ldur            x0, [fp, #-0x20]
    // 0xbd3b78: stur            x1, [fp, #-0x30]
    // 0xbd3b7c: StoreField: r1->field_13 = r0
    //     0xbd3b7c: stur            w0, [x1, #0x13]
    // 0xbd3b80: ldur            x0, [fp, #-0x28]
    // 0xbd3b84: StoreField: r1->field_b = r0
    //     0xbd3b84: stur            w0, [x1, #0xb]
    // 0xbd3b88: r0 = Padding()
    //     0xbd3b88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd3b8c: mov             x1, x0
    // 0xbd3b90: r0 = Instance_EdgeInsets
    //     0xbd3b90: add             x0, PP, #0x27, lsl #12  ; [pp+0x27b08] Obj!EdgeInsets@d570b1
    //     0xbd3b94: ldr             x0, [x0, #0xb08]
    // 0xbd3b98: stur            x1, [fp, #-0x20]
    // 0xbd3b9c: StoreField: r1->field_f = r0
    //     0xbd3b9c: stur            w0, [x1, #0xf]
    // 0xbd3ba0: ldur            x0, [fp, #-0x30]
    // 0xbd3ba4: StoreField: r1->field_b = r0
    //     0xbd3ba4: stur            w0, [x1, #0xb]
    // 0xbd3ba8: r0 = GetNavigation.size()
    //     0xbd3ba8: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbd3bac: LoadField: d0 = r0->field_7
    //     0xbd3bac: ldur            d0, [x0, #7]
    // 0xbd3bb0: d1 = 0.940000
    //     0xbd3bb0: add             x17, PP, #0x54, lsl #12  ; [pp+0x542f0] IMM: double(0.94) from 0x3fee147ae147ae14
    //     0xbd3bb4: ldr             d1, [x17, #0x2f0]
    // 0xbd3bb8: fmul            d2, d0, d1
    // 0xbd3bbc: stur            d2, [fp, #-0x70]
    // 0xbd3bc0: r16 = <EdgeInsets>
    //     0xbd3bc0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbd3bc4: ldr             x16, [x16, #0xda0]
    // 0xbd3bc8: r30 = Instance_EdgeInsets
    //     0xbd3bc8: add             lr, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xbd3bcc: ldr             lr, [lr, #0x50]
    // 0xbd3bd0: stp             lr, x16, [SP]
    // 0xbd3bd4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbd3bd4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbd3bd8: r0 = all()
    //     0xbd3bd8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbd3bdc: ldur            x1, [fp, #-0x10]
    // 0xbd3be0: stur            x0, [fp, #-0x28]
    // 0xbd3be4: r0 = of()
    //     0xbd3be4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd3be8: LoadField: r1 = r0->field_5b
    //     0xbd3be8: ldur            w1, [x0, #0x5b]
    // 0xbd3bec: DecompressPointer r1
    //     0xbd3bec: add             x1, x1, HEAP, lsl #32
    // 0xbd3bf0: r0 = LoadClassIdInstr(r1)
    //     0xbd3bf0: ldur            x0, [x1, #-1]
    //     0xbd3bf4: ubfx            x0, x0, #0xc, #0x14
    // 0xbd3bf8: d0 = 0.400000
    //     0xbd3bf8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbd3bfc: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbd3bfc: sub             lr, x0, #0xffa
    //     0xbd3c00: ldr             lr, [x21, lr, lsl #3]
    //     0xbd3c04: blr             lr
    // 0xbd3c08: stur            x0, [fp, #-0x30]
    // 0xbd3c0c: r0 = BorderSide()
    //     0xbd3c0c: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbd3c10: mov             x1, x0
    // 0xbd3c14: ldur            x0, [fp, #-0x30]
    // 0xbd3c18: stur            x1, [fp, #-0x38]
    // 0xbd3c1c: StoreField: r1->field_7 = r0
    //     0xbd3c1c: stur            w0, [x1, #7]
    // 0xbd3c20: d0 = 1.000000
    //     0xbd3c20: fmov            d0, #1.00000000
    // 0xbd3c24: StoreField: r1->field_b = d0
    //     0xbd3c24: stur            d0, [x1, #0xb]
    // 0xbd3c28: r0 = Instance_BorderStyle
    //     0xbd3c28: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbd3c2c: ldr             x0, [x0, #0xf68]
    // 0xbd3c30: StoreField: r1->field_13 = r0
    //     0xbd3c30: stur            w0, [x1, #0x13]
    // 0xbd3c34: d0 = -1.000000
    //     0xbd3c34: fmov            d0, #-1.00000000
    // 0xbd3c38: ArrayStore: r1[0] = d0  ; List_8
    //     0xbd3c38: stur            d0, [x1, #0x17]
    // 0xbd3c3c: r0 = RoundedRectangleBorder()
    //     0xbd3c3c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbd3c40: mov             x1, x0
    // 0xbd3c44: r0 = Instance_BorderRadius
    //     0xbd3c44: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbd3c48: ldr             x0, [x0, #0xf70]
    // 0xbd3c4c: StoreField: r1->field_b = r0
    //     0xbd3c4c: stur            w0, [x1, #0xb]
    // 0xbd3c50: ldur            x0, [fp, #-0x38]
    // 0xbd3c54: StoreField: r1->field_7 = r0
    //     0xbd3c54: stur            w0, [x1, #7]
    // 0xbd3c58: r16 = <RoundedRectangleBorder>
    //     0xbd3c58: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbd3c5c: ldr             x16, [x16, #0xf78]
    // 0xbd3c60: stp             x1, x16, [SP]
    // 0xbd3c64: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbd3c64: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbd3c68: r0 = all()
    //     0xbd3c68: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbd3c6c: stur            x0, [fp, #-0x30]
    // 0xbd3c70: r0 = ButtonStyle()
    //     0xbd3c70: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbd3c74: mov             x1, x0
    // 0xbd3c78: ldur            x0, [fp, #-0x28]
    // 0xbd3c7c: stur            x1, [fp, #-0x38]
    // 0xbd3c80: StoreField: r1->field_23 = r0
    //     0xbd3c80: stur            w0, [x1, #0x23]
    // 0xbd3c84: ldur            x0, [fp, #-0x30]
    // 0xbd3c88: StoreField: r1->field_43 = r0
    //     0xbd3c88: stur            w0, [x1, #0x43]
    // 0xbd3c8c: r0 = TextButtonThemeData()
    //     0xbd3c8c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbd3c90: mov             x2, x0
    // 0xbd3c94: ldur            x0, [fp, #-0x38]
    // 0xbd3c98: stur            x2, [fp, #-0x28]
    // 0xbd3c9c: StoreField: r2->field_7 = r0
    //     0xbd3c9c: stur            w0, [x2, #7]
    // 0xbd3ca0: ldur            x1, [fp, #-0x10]
    // 0xbd3ca4: r0 = of()
    //     0xbd3ca4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd3ca8: LoadField: r1 = r0->field_87
    //     0xbd3ca8: ldur            w1, [x0, #0x87]
    // 0xbd3cac: DecompressPointer r1
    //     0xbd3cac: add             x1, x1, HEAP, lsl #32
    // 0xbd3cb0: LoadField: r0 = r1->field_7
    //     0xbd3cb0: ldur            w0, [x1, #7]
    // 0xbd3cb4: DecompressPointer r0
    //     0xbd3cb4: add             x0, x0, HEAP, lsl #32
    // 0xbd3cb8: ldur            x1, [fp, #-0x10]
    // 0xbd3cbc: stur            x0, [fp, #-0x30]
    // 0xbd3cc0: r0 = of()
    //     0xbd3cc0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd3cc4: LoadField: r1 = r0->field_5b
    //     0xbd3cc4: ldur            w1, [x0, #0x5b]
    // 0xbd3cc8: DecompressPointer r1
    //     0xbd3cc8: add             x1, x1, HEAP, lsl #32
    // 0xbd3ccc: r16 = 16.000000
    //     0xbd3ccc: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd3cd0: ldr             x16, [x16, #0x188]
    // 0xbd3cd4: stp             x1, x16, [SP]
    // 0xbd3cd8: ldur            x1, [fp, #-0x30]
    // 0xbd3cdc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd3cdc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd3ce0: ldr             x4, [x4, #0xaa0]
    // 0xbd3ce4: r0 = copyWith()
    //     0xbd3ce4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd3ce8: stur            x0, [fp, #-0x30]
    // 0xbd3cec: r0 = Text()
    //     0xbd3cec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd3cf0: mov             x3, x0
    // 0xbd3cf4: r0 = "NOT NOW"
    //     0xbd3cf4: add             x0, PP, #0x54, lsl #12  ; [pp+0x542f8] "NOT NOW"
    //     0xbd3cf8: ldr             x0, [x0, #0x2f8]
    // 0xbd3cfc: stur            x3, [fp, #-0x38]
    // 0xbd3d00: StoreField: r3->field_b = r0
    //     0xbd3d00: stur            w0, [x3, #0xb]
    // 0xbd3d04: ldur            x0, [fp, #-0x30]
    // 0xbd3d08: StoreField: r3->field_13 = r0
    //     0xbd3d08: stur            w0, [x3, #0x13]
    // 0xbd3d0c: ldur            x2, [fp, #-0x18]
    // 0xbd3d10: r1 = Function '<anonymous closure>':.
    //     0xbd3d10: add             x1, PP, #0x54, lsl #12  ; [pp+0x54300] AnonymousClosure: (0xbd4328), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/push_for_online_popup_bottom_sheet.dart] _PartialCodPopupBottomSheet::build (0xbd35b0)
    //     0xbd3d14: ldr             x1, [x1, #0x300]
    // 0xbd3d18: r0 = AllocateClosure()
    //     0xbd3d18: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd3d1c: stur            x0, [fp, #-0x30]
    // 0xbd3d20: r0 = TextButton()
    //     0xbd3d20: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbd3d24: mov             x1, x0
    // 0xbd3d28: ldur            x0, [fp, #-0x30]
    // 0xbd3d2c: stur            x1, [fp, #-0x40]
    // 0xbd3d30: StoreField: r1->field_b = r0
    //     0xbd3d30: stur            w0, [x1, #0xb]
    // 0xbd3d34: r0 = false
    //     0xbd3d34: add             x0, NULL, #0x30  ; false
    // 0xbd3d38: StoreField: r1->field_27 = r0
    //     0xbd3d38: stur            w0, [x1, #0x27]
    // 0xbd3d3c: r2 = true
    //     0xbd3d3c: add             x2, NULL, #0x20  ; true
    // 0xbd3d40: StoreField: r1->field_2f = r2
    //     0xbd3d40: stur            w2, [x1, #0x2f]
    // 0xbd3d44: ldur            x3, [fp, #-0x38]
    // 0xbd3d48: StoreField: r1->field_37 = r3
    //     0xbd3d48: stur            w3, [x1, #0x37]
    // 0xbd3d4c: r0 = TextButtonTheme()
    //     0xbd3d4c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbd3d50: mov             x2, x0
    // 0xbd3d54: ldur            x0, [fp, #-0x28]
    // 0xbd3d58: stur            x2, [fp, #-0x30]
    // 0xbd3d5c: StoreField: r2->field_f = r0
    //     0xbd3d5c: stur            w0, [x2, #0xf]
    // 0xbd3d60: ldur            x0, [fp, #-0x40]
    // 0xbd3d64: StoreField: r2->field_b = r0
    //     0xbd3d64: stur            w0, [x2, #0xb]
    // 0xbd3d68: r1 = <FlexParentData>
    //     0xbd3d68: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbd3d6c: ldr             x1, [x1, #0xe00]
    // 0xbd3d70: r0 = Expanded()
    //     0xbd3d70: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbd3d74: mov             x2, x0
    // 0xbd3d78: r0 = 1
    //     0xbd3d78: movz            x0, #0x1
    // 0xbd3d7c: stur            x2, [fp, #-0x28]
    // 0xbd3d80: StoreField: r2->field_13 = r0
    //     0xbd3d80: stur            x0, [x2, #0x13]
    // 0xbd3d84: r0 = Instance_FlexFit
    //     0xbd3d84: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbd3d88: ldr             x0, [x0, #0xe08]
    // 0xbd3d8c: StoreField: r2->field_1b = r0
    //     0xbd3d8c: stur            w0, [x2, #0x1b]
    // 0xbd3d90: ldur            x1, [fp, #-0x30]
    // 0xbd3d94: StoreField: r2->field_b = r1
    //     0xbd3d94: stur            w1, [x2, #0xb]
    // 0xbd3d98: ldur            x1, [fp, #-0x10]
    // 0xbd3d9c: r0 = of()
    //     0xbd3d9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd3da0: r17 = 307
    //     0xbd3da0: movz            x17, #0x133
    // 0xbd3da4: ldr             w3, [x0, x17]
    // 0xbd3da8: DecompressPointer r3
    //     0xbd3da8: add             x3, x3, HEAP, lsl #32
    // 0xbd3dac: stur            x3, [fp, #-0x30]
    // 0xbd3db0: r1 = Null
    //     0xbd3db0: mov             x1, NULL
    // 0xbd3db4: r2 = 6
    //     0xbd3db4: movz            x2, #0x6
    // 0xbd3db8: r0 = AllocateArray()
    //     0xbd3db8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd3dbc: r16 = "Pay Only "
    //     0xbd3dbc: add             x16, PP, #0x3c, lsl #12  ; [pp+0x3ca78] "Pay Only "
    //     0xbd3dc0: ldr             x16, [x16, #0xa78]
    // 0xbd3dc4: StoreField: r0->field_f = r16
    //     0xbd3dc4: stur            w16, [x0, #0xf]
    // 0xbd3dc8: ldur            x1, [fp, #-8]
    // 0xbd3dcc: LoadField: r2 = r1->field_b
    //     0xbd3dcc: ldur            w2, [x1, #0xb]
    // 0xbd3dd0: DecompressPointer r2
    //     0xbd3dd0: add             x2, x2, HEAP, lsl #32
    // 0xbd3dd4: cmp             w2, NULL
    // 0xbd3dd8: b.eq            #0xbd423c
    // 0xbd3ddc: LoadField: r3 = r2->field_b
    //     0xbd3ddc: ldur            w3, [x2, #0xb]
    // 0xbd3de0: DecompressPointer r3
    //     0xbd3de0: add             x3, x3, HEAP, lsl #32
    // 0xbd3de4: LoadField: r2 = r3->field_1b
    //     0xbd3de4: ldur            w2, [x3, #0x1b]
    // 0xbd3de8: DecompressPointer r2
    //     0xbd3de8: add             x2, x2, HEAP, lsl #32
    // 0xbd3dec: StoreField: r0->field_13 = r2
    //     0xbd3dec: stur            w2, [x0, #0x13]
    // 0xbd3df0: r16 = " "
    //     0xbd3df0: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xbd3df4: ArrayStore: r0[0] = r16  ; List_4
    //     0xbd3df4: stur            w16, [x0, #0x17]
    // 0xbd3df8: str             x0, [SP]
    // 0xbd3dfc: r0 = _interpolate()
    //     0xbd3dfc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd3e00: ldur            x1, [fp, #-0x10]
    // 0xbd3e04: stur            x0, [fp, #-0x38]
    // 0xbd3e08: r0 = of()
    //     0xbd3e08: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd3e0c: LoadField: r1 = r0->field_87
    //     0xbd3e0c: ldur            w1, [x0, #0x87]
    // 0xbd3e10: DecompressPointer r1
    //     0xbd3e10: add             x1, x1, HEAP, lsl #32
    // 0xbd3e14: LoadField: r0 = r1->field_7
    //     0xbd3e14: ldur            w0, [x1, #7]
    // 0xbd3e18: DecompressPointer r0
    //     0xbd3e18: add             x0, x0, HEAP, lsl #32
    // 0xbd3e1c: r16 = 16.000000
    //     0xbd3e1c: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd3e20: ldr             x16, [x16, #0x188]
    // 0xbd3e24: r30 = Instance_Color
    //     0xbd3e24: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd3e28: stp             lr, x16, [SP]
    // 0xbd3e2c: mov             x1, x0
    // 0xbd3e30: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd3e30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd3e34: ldr             x4, [x4, #0xaa0]
    // 0xbd3e38: r0 = copyWith()
    //     0xbd3e38: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd3e3c: stur            x0, [fp, #-0x40]
    // 0xbd3e40: r0 = Text()
    //     0xbd3e40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd3e44: mov             x1, x0
    // 0xbd3e48: ldur            x0, [fp, #-0x38]
    // 0xbd3e4c: stur            x1, [fp, #-0x48]
    // 0xbd3e50: StoreField: r1->field_b = r0
    //     0xbd3e50: stur            w0, [x1, #0xb]
    // 0xbd3e54: ldur            x0, [fp, #-0x40]
    // 0xbd3e58: StoreField: r1->field_13 = r0
    //     0xbd3e58: stur            w0, [x1, #0x13]
    // 0xbd3e5c: ldur            x0, [fp, #-8]
    // 0xbd3e60: LoadField: r2 = r0->field_b
    //     0xbd3e60: ldur            w2, [x0, #0xb]
    // 0xbd3e64: DecompressPointer r2
    //     0xbd3e64: add             x2, x2, HEAP, lsl #32
    // 0xbd3e68: cmp             w2, NULL
    // 0xbd3e6c: b.eq            #0xbd4240
    // 0xbd3e70: LoadField: r0 = r2->field_b
    //     0xbd3e70: ldur            w0, [x2, #0xb]
    // 0xbd3e74: DecompressPointer r0
    //     0xbd3e74: add             x0, x0, HEAP, lsl #32
    // 0xbd3e78: LoadField: r2 = r0->field_13
    //     0xbd3e78: ldur            w2, [x0, #0x13]
    // 0xbd3e7c: DecompressPointer r2
    //     0xbd3e7c: add             x2, x2, HEAP, lsl #32
    // 0xbd3e80: str             x2, [SP]
    // 0xbd3e84: r0 = _interpolateSingle()
    //     0xbd3e84: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xbd3e88: ldur            x1, [fp, #-0x10]
    // 0xbd3e8c: stur            x0, [fp, #-8]
    // 0xbd3e90: r0 = of()
    //     0xbd3e90: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd3e94: LoadField: r1 = r0->field_87
    //     0xbd3e94: ldur            w1, [x0, #0x87]
    // 0xbd3e98: DecompressPointer r1
    //     0xbd3e98: add             x1, x1, HEAP, lsl #32
    // 0xbd3e9c: LoadField: r0 = r1->field_7
    //     0xbd3e9c: ldur            w0, [x1, #7]
    // 0xbd3ea0: DecompressPointer r0
    //     0xbd3ea0: add             x0, x0, HEAP, lsl #32
    // 0xbd3ea4: r16 = 16.000000
    //     0xbd3ea4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbd3ea8: ldr             x16, [x16, #0x188]
    // 0xbd3eac: r30 = Instance_TextDecoration
    //     0xbd3eac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbd3eb0: ldr             lr, [lr, #0xe30]
    // 0xbd3eb4: stp             lr, x16, [SP, #8]
    // 0xbd3eb8: r16 = Instance_Color
    //     0xbd3eb8: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd3ebc: str             x16, [SP]
    // 0xbd3ec0: mov             x1, x0
    // 0xbd3ec4: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x2, fontSize, 0x1, null]
    //     0xbd3ec4: add             x4, PP, #0x54, lsl #12  ; [pp+0x54308] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbd3ec8: ldr             x4, [x4, #0x308]
    // 0xbd3ecc: r0 = copyWith()
    //     0xbd3ecc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd3ed0: stur            x0, [fp, #-0x10]
    // 0xbd3ed4: r0 = Text()
    //     0xbd3ed4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd3ed8: mov             x3, x0
    // 0xbd3edc: ldur            x0, [fp, #-8]
    // 0xbd3ee0: stur            x3, [fp, #-0x38]
    // 0xbd3ee4: StoreField: r3->field_b = r0
    //     0xbd3ee4: stur            w0, [x3, #0xb]
    // 0xbd3ee8: ldur            x0, [fp, #-0x10]
    // 0xbd3eec: StoreField: r3->field_13 = r0
    //     0xbd3eec: stur            w0, [x3, #0x13]
    // 0xbd3ef0: r1 = Null
    //     0xbd3ef0: mov             x1, NULL
    // 0xbd3ef4: r2 = 4
    //     0xbd3ef4: movz            x2, #0x4
    // 0xbd3ef8: r0 = AllocateArray()
    //     0xbd3ef8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd3efc: mov             x2, x0
    // 0xbd3f00: ldur            x0, [fp, #-0x48]
    // 0xbd3f04: stur            x2, [fp, #-8]
    // 0xbd3f08: StoreField: r2->field_f = r0
    //     0xbd3f08: stur            w0, [x2, #0xf]
    // 0xbd3f0c: ldur            x0, [fp, #-0x38]
    // 0xbd3f10: StoreField: r2->field_13 = r0
    //     0xbd3f10: stur            w0, [x2, #0x13]
    // 0xbd3f14: r1 = <Widget>
    //     0xbd3f14: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd3f18: r0 = AllocateGrowableArray()
    //     0xbd3f18: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd3f1c: mov             x1, x0
    // 0xbd3f20: ldur            x0, [fp, #-8]
    // 0xbd3f24: stur            x1, [fp, #-0x10]
    // 0xbd3f28: StoreField: r1->field_f = r0
    //     0xbd3f28: stur            w0, [x1, #0xf]
    // 0xbd3f2c: r2 = 4
    //     0xbd3f2c: movz            x2, #0x4
    // 0xbd3f30: StoreField: r1->field_b = r2
    //     0xbd3f30: stur            w2, [x1, #0xb]
    // 0xbd3f34: r0 = Row()
    //     0xbd3f34: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd3f38: mov             x3, x0
    // 0xbd3f3c: r0 = Instance_Axis
    //     0xbd3f3c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd3f40: stur            x3, [fp, #-8]
    // 0xbd3f44: StoreField: r3->field_f = r0
    //     0xbd3f44: stur            w0, [x3, #0xf]
    // 0xbd3f48: r1 = Instance_MainAxisAlignment
    //     0xbd3f48: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xbd3f4c: ldr             x1, [x1, #0xab0]
    // 0xbd3f50: StoreField: r3->field_13 = r1
    //     0xbd3f50: stur            w1, [x3, #0x13]
    // 0xbd3f54: r4 = Instance_MainAxisSize
    //     0xbd3f54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd3f58: ldr             x4, [x4, #0xa10]
    // 0xbd3f5c: ArrayStore: r3[0] = r4  ; List_4
    //     0xbd3f5c: stur            w4, [x3, #0x17]
    // 0xbd3f60: r5 = Instance_CrossAxisAlignment
    //     0xbd3f60: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd3f64: ldr             x5, [x5, #0xa18]
    // 0xbd3f68: StoreField: r3->field_1b = r5
    //     0xbd3f68: stur            w5, [x3, #0x1b]
    // 0xbd3f6c: r6 = Instance_VerticalDirection
    //     0xbd3f6c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd3f70: ldr             x6, [x6, #0xa20]
    // 0xbd3f74: StoreField: r3->field_23 = r6
    //     0xbd3f74: stur            w6, [x3, #0x23]
    // 0xbd3f78: r7 = Instance_Clip
    //     0xbd3f78: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd3f7c: ldr             x7, [x7, #0x38]
    // 0xbd3f80: StoreField: r3->field_2b = r7
    //     0xbd3f80: stur            w7, [x3, #0x2b]
    // 0xbd3f84: StoreField: r3->field_2f = rZR
    //     0xbd3f84: stur            xzr, [x3, #0x2f]
    // 0xbd3f88: ldur            x1, [fp, #-0x10]
    // 0xbd3f8c: StoreField: r3->field_b = r1
    //     0xbd3f8c: stur            w1, [x3, #0xb]
    // 0xbd3f90: ldur            x2, [fp, #-0x18]
    // 0xbd3f94: r1 = Function '<anonymous closure>':.
    //     0xbd3f94: add             x1, PP, #0x54, lsl #12  ; [pp+0x54310] AnonymousClosure: (0xbd427c), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/push_for_online_popup_bottom_sheet.dart] _PartialCodPopupBottomSheet::build (0xbd35b0)
    //     0xbd3f98: ldr             x1, [x1, #0x310]
    // 0xbd3f9c: r0 = AllocateClosure()
    //     0xbd3f9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd3fa0: stur            x0, [fp, #-0x10]
    // 0xbd3fa4: r0 = TextButton()
    //     0xbd3fa4: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbd3fa8: mov             x1, x0
    // 0xbd3fac: ldur            x0, [fp, #-0x10]
    // 0xbd3fb0: stur            x1, [fp, #-0x18]
    // 0xbd3fb4: StoreField: r1->field_b = r0
    //     0xbd3fb4: stur            w0, [x1, #0xb]
    // 0xbd3fb8: r0 = false
    //     0xbd3fb8: add             x0, NULL, #0x30  ; false
    // 0xbd3fbc: StoreField: r1->field_27 = r0
    //     0xbd3fbc: stur            w0, [x1, #0x27]
    // 0xbd3fc0: r0 = true
    //     0xbd3fc0: add             x0, NULL, #0x20  ; true
    // 0xbd3fc4: StoreField: r1->field_2f = r0
    //     0xbd3fc4: stur            w0, [x1, #0x2f]
    // 0xbd3fc8: ldur            x0, [fp, #-8]
    // 0xbd3fcc: StoreField: r1->field_37 = r0
    //     0xbd3fcc: stur            w0, [x1, #0x37]
    // 0xbd3fd0: r0 = TextButtonTheme()
    //     0xbd3fd0: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbd3fd4: mov             x2, x0
    // 0xbd3fd8: ldur            x0, [fp, #-0x30]
    // 0xbd3fdc: stur            x2, [fp, #-8]
    // 0xbd3fe0: StoreField: r2->field_f = r0
    //     0xbd3fe0: stur            w0, [x2, #0xf]
    // 0xbd3fe4: ldur            x0, [fp, #-0x18]
    // 0xbd3fe8: StoreField: r2->field_b = r0
    //     0xbd3fe8: stur            w0, [x2, #0xb]
    // 0xbd3fec: r1 = <FlexParentData>
    //     0xbd3fec: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbd3ff0: ldr             x1, [x1, #0xe00]
    // 0xbd3ff4: r0 = Expanded()
    //     0xbd3ff4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbd3ff8: mov             x3, x0
    // 0xbd3ffc: r0 = 2
    //     0xbd3ffc: movz            x0, #0x2
    // 0xbd4000: stur            x3, [fp, #-0x10]
    // 0xbd4004: StoreField: r3->field_13 = r0
    //     0xbd4004: stur            x0, [x3, #0x13]
    // 0xbd4008: r0 = Instance_FlexFit
    //     0xbd4008: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbd400c: ldr             x0, [x0, #0xe08]
    // 0xbd4010: StoreField: r3->field_1b = r0
    //     0xbd4010: stur            w0, [x3, #0x1b]
    // 0xbd4014: ldur            x0, [fp, #-8]
    // 0xbd4018: StoreField: r3->field_b = r0
    //     0xbd4018: stur            w0, [x3, #0xb]
    // 0xbd401c: r1 = Null
    //     0xbd401c: mov             x1, NULL
    // 0xbd4020: r2 = 6
    //     0xbd4020: movz            x2, #0x6
    // 0xbd4024: r0 = AllocateArray()
    //     0xbd4024: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd4028: mov             x2, x0
    // 0xbd402c: ldur            x0, [fp, #-0x28]
    // 0xbd4030: stur            x2, [fp, #-8]
    // 0xbd4034: StoreField: r2->field_f = r0
    //     0xbd4034: stur            w0, [x2, #0xf]
    // 0xbd4038: r16 = Instance_SizedBox
    //     0xbd4038: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbd403c: ldr             x16, [x16, #0xb20]
    // 0xbd4040: StoreField: r2->field_13 = r16
    //     0xbd4040: stur            w16, [x2, #0x13]
    // 0xbd4044: ldur            x0, [fp, #-0x10]
    // 0xbd4048: ArrayStore: r2[0] = r0  ; List_4
    //     0xbd4048: stur            w0, [x2, #0x17]
    // 0xbd404c: r1 = <Widget>
    //     0xbd404c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd4050: r0 = AllocateGrowableArray()
    //     0xbd4050: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd4054: mov             x1, x0
    // 0xbd4058: ldur            x0, [fp, #-8]
    // 0xbd405c: stur            x1, [fp, #-0x10]
    // 0xbd4060: StoreField: r1->field_f = r0
    //     0xbd4060: stur            w0, [x1, #0xf]
    // 0xbd4064: r0 = 6
    //     0xbd4064: movz            x0, #0x6
    // 0xbd4068: StoreField: r1->field_b = r0
    //     0xbd4068: stur            w0, [x1, #0xb]
    // 0xbd406c: r0 = Row()
    //     0xbd406c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd4070: mov             x1, x0
    // 0xbd4074: r0 = Instance_Axis
    //     0xbd4074: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd4078: stur            x1, [fp, #-0x18]
    // 0xbd407c: StoreField: r1->field_f = r0
    //     0xbd407c: stur            w0, [x1, #0xf]
    // 0xbd4080: r0 = Instance_MainAxisAlignment
    //     0xbd4080: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xbd4084: ldr             x0, [x0, #0xd10]
    // 0xbd4088: StoreField: r1->field_13 = r0
    //     0xbd4088: stur            w0, [x1, #0x13]
    // 0xbd408c: r0 = Instance_MainAxisSize
    //     0xbd408c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd4090: ldr             x0, [x0, #0xa10]
    // 0xbd4094: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd4094: stur            w0, [x1, #0x17]
    // 0xbd4098: r0 = Instance_CrossAxisAlignment
    //     0xbd4098: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd409c: ldr             x0, [x0, #0xa18]
    // 0xbd40a0: StoreField: r1->field_1b = r0
    //     0xbd40a0: stur            w0, [x1, #0x1b]
    // 0xbd40a4: r0 = Instance_VerticalDirection
    //     0xbd40a4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd40a8: ldr             x0, [x0, #0xa20]
    // 0xbd40ac: StoreField: r1->field_23 = r0
    //     0xbd40ac: stur            w0, [x1, #0x23]
    // 0xbd40b0: r0 = Instance_Clip
    //     0xbd40b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd40b4: ldr             x0, [x0, #0x38]
    // 0xbd40b8: StoreField: r1->field_2b = r0
    //     0xbd40b8: stur            w0, [x1, #0x2b]
    // 0xbd40bc: StoreField: r1->field_2f = rZR
    //     0xbd40bc: stur            xzr, [x1, #0x2f]
    // 0xbd40c0: ldur            x0, [fp, #-0x10]
    // 0xbd40c4: StoreField: r1->field_b = r0
    //     0xbd40c4: stur            w0, [x1, #0xb]
    // 0xbd40c8: ldur            d0, [fp, #-0x70]
    // 0xbd40cc: r0 = inline_Allocate_Double()
    //     0xbd40cc: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbd40d0: add             x0, x0, #0x10
    //     0xbd40d4: cmp             x2, x0
    //     0xbd40d8: b.ls            #0xbd4244
    //     0xbd40dc: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd40e0: sub             x0, x0, #0xf
    //     0xbd40e4: movz            x2, #0xe15c
    //     0xbd40e8: movk            x2, #0x3, lsl #16
    //     0xbd40ec: stur            x2, [x0, #-1]
    // 0xbd40f0: StoreField: r0->field_7 = d0
    //     0xbd40f0: stur            d0, [x0, #7]
    // 0xbd40f4: stur            x0, [fp, #-8]
    // 0xbd40f8: r0 = SizedBox()
    //     0xbd40f8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbd40fc: mov             x1, x0
    // 0xbd4100: ldur            x0, [fp, #-8]
    // 0xbd4104: stur            x1, [fp, #-0x10]
    // 0xbd4108: StoreField: r1->field_f = r0
    //     0xbd4108: stur            w0, [x1, #0xf]
    // 0xbd410c: r0 = 60.000000
    //     0xbd410c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0xbd4110: ldr             x0, [x0, #0x110]
    // 0xbd4114: StoreField: r1->field_13 = r0
    //     0xbd4114: stur            w0, [x1, #0x13]
    // 0xbd4118: ldur            x0, [fp, #-0x18]
    // 0xbd411c: StoreField: r1->field_b = r0
    //     0xbd411c: stur            w0, [x1, #0xb]
    // 0xbd4120: r0 = Container()
    //     0xbd4120: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbd4124: stur            x0, [fp, #-8]
    // 0xbd4128: r16 = Instance_EdgeInsets
    //     0xbd4128: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f0d0] Obj!EdgeInsets@d56f31
    //     0xbd412c: ldr             x16, [x16, #0xd0]
    // 0xbd4130: r30 = Instance_Color
    //     0xbd4130: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbd4134: stp             lr, x16, [SP, #8]
    // 0xbd4138: ldur            x16, [fp, #-0x10]
    // 0xbd413c: str             x16, [SP]
    // 0xbd4140: mov             x1, x0
    // 0xbd4144: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, padding, 0x1, null]
    //     0xbd4144: add             x4, PP, #0x38, lsl #12  ; [pp+0x38d98] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "padding", 0x1, Null]
    //     0xbd4148: ldr             x4, [x4, #0xd98]
    // 0xbd414c: r0 = Container()
    //     0xbd414c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbd4150: r1 = <StackParentData>
    //     0xbd4150: add             x1, PP, #0x33, lsl #12  ; [pp+0x338e0] TypeArguments: <StackParentData>
    //     0xbd4154: ldr             x1, [x1, #0x8e0]
    // 0xbd4158: r0 = Positioned()
    //     0xbd4158: bl              #0x98810c  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xbd415c: mov             x3, x0
    // 0xbd4160: r0 = 0.000000
    //     0xbd4160: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xbd4164: stur            x3, [fp, #-0x10]
    // 0xbd4168: StoreField: r3->field_13 = r0
    //     0xbd4168: stur            w0, [x3, #0x13]
    // 0xbd416c: StoreField: r3->field_1b = r0
    //     0xbd416c: stur            w0, [x3, #0x1b]
    // 0xbd4170: StoreField: r3->field_1f = r0
    //     0xbd4170: stur            w0, [x3, #0x1f]
    // 0xbd4174: ldur            x0, [fp, #-8]
    // 0xbd4178: StoreField: r3->field_b = r0
    //     0xbd4178: stur            w0, [x3, #0xb]
    // 0xbd417c: r1 = Null
    //     0xbd417c: mov             x1, NULL
    // 0xbd4180: r2 = 4
    //     0xbd4180: movz            x2, #0x4
    // 0xbd4184: r0 = AllocateArray()
    //     0xbd4184: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd4188: mov             x2, x0
    // 0xbd418c: ldur            x0, [fp, #-0x20]
    // 0xbd4190: stur            x2, [fp, #-8]
    // 0xbd4194: StoreField: r2->field_f = r0
    //     0xbd4194: stur            w0, [x2, #0xf]
    // 0xbd4198: ldur            x0, [fp, #-0x10]
    // 0xbd419c: StoreField: r2->field_13 = r0
    //     0xbd419c: stur            w0, [x2, #0x13]
    // 0xbd41a0: r1 = <Widget>
    //     0xbd41a0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd41a4: r0 = AllocateGrowableArray()
    //     0xbd41a4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd41a8: mov             x1, x0
    // 0xbd41ac: ldur            x0, [fp, #-8]
    // 0xbd41b0: stur            x1, [fp, #-0x10]
    // 0xbd41b4: StoreField: r1->field_f = r0
    //     0xbd41b4: stur            w0, [x1, #0xf]
    // 0xbd41b8: r0 = 4
    //     0xbd41b8: movz            x0, #0x4
    // 0xbd41bc: StoreField: r1->field_b = r0
    //     0xbd41bc: stur            w0, [x1, #0xb]
    // 0xbd41c0: r0 = Stack()
    //     0xbd41c0: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xbd41c4: mov             x1, x0
    // 0xbd41c8: r0 = Instance_AlignmentDirectional
    //     0xbd41c8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd08] Obj!AlignmentDirectional@d5a5e1
    //     0xbd41cc: ldr             x0, [x0, #0xd08]
    // 0xbd41d0: stur            x1, [fp, #-8]
    // 0xbd41d4: StoreField: r1->field_f = r0
    //     0xbd41d4: stur            w0, [x1, #0xf]
    // 0xbd41d8: r0 = Instance_StackFit
    //     0xbd41d8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xbd41dc: ldr             x0, [x0, #0xfa8]
    // 0xbd41e0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbd41e0: stur            w0, [x1, #0x17]
    // 0xbd41e4: r0 = Instance_Clip
    //     0xbd41e4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbd41e8: ldr             x0, [x0, #0x7e0]
    // 0xbd41ec: StoreField: r1->field_1b = r0
    //     0xbd41ec: stur            w0, [x1, #0x1b]
    // 0xbd41f0: ldur            x0, [fp, #-0x10]
    // 0xbd41f4: StoreField: r1->field_b = r0
    //     0xbd41f4: stur            w0, [x1, #0xb]
    // 0xbd41f8: r0 = Padding()
    //     0xbd41f8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd41fc: r1 = Instance_EdgeInsets
    //     0xbd41fc: add             x1, PP, #0x38, lsl #12  ; [pp+0x38dc0] Obj!EdgeInsets@d58221
    //     0xbd4200: ldr             x1, [x1, #0xdc0]
    // 0xbd4204: StoreField: r0->field_f = r1
    //     0xbd4204: stur            w1, [x0, #0xf]
    // 0xbd4208: ldur            x1, [fp, #-8]
    // 0xbd420c: StoreField: r0->field_b = r1
    //     0xbd420c: stur            w1, [x0, #0xb]
    // 0xbd4210: LeaveFrame
    //     0xbd4210: mov             SP, fp
    //     0xbd4214: ldp             fp, lr, [SP], #0x10
    // 0xbd4218: ret
    //     0xbd4218: ret             
    // 0xbd421c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd421c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd4220: b               #0xbd35d8
    // 0xbd4224: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbd4224: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbd4228: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd4228: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd422c: SaveReg d0
    //     0xbd422c: str             q0, [SP, #-0x10]!
    // 0xbd4230: r0 = AllocateDouble()
    //     0xbd4230: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbd4234: RestoreReg d0
    //     0xbd4234: ldr             q0, [SP], #0x10
    // 0xbd4238: b               #0xbd3b64
    // 0xbd423c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd423c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd4240: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd4240: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbd4244: SaveReg d0
    //     0xbd4244: str             q0, [SP, #-0x10]!
    // 0xbd4248: SaveReg r1
    //     0xbd4248: str             x1, [SP, #-8]!
    // 0xbd424c: r0 = AllocateDouble()
    //     0xbd424c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbd4250: RestoreReg r1
    //     0xbd4250: ldr             x1, [SP], #8
    // 0xbd4254: RestoreReg d0
    //     0xbd4254: ldr             q0, [SP], #0x10
    // 0xbd4258: b               #0xbd40f0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd427c, size: 0xac
    // 0xbd427c: EnterFrame
    //     0xbd427c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd4280: mov             fp, SP
    // 0xbd4284: AllocStack(0x10)
    //     0xbd4284: sub             SP, SP, #0x10
    // 0xbd4288: SetupParameters()
    //     0xbd4288: ldr             x0, [fp, #0x10]
    //     0xbd428c: ldur            w1, [x0, #0x17]
    //     0xbd4290: add             x1, x1, HEAP, lsl #32
    //     0xbd4294: stur            x1, [fp, #-8]
    // 0xbd4298: CheckStackOverflow
    //     0xbd4298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd429c: cmp             SP, x16
    //     0xbd42a0: b.ls            #0xbd431c
    // 0xbd42a4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbd42a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd42a8: ldr             x0, [x0, #0x1c80]
    //     0xbd42ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd42b0: cmp             w0, w16
    //     0xbd42b4: b.ne            #0xbd42c0
    //     0xbd42b8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbd42bc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd42c0: str             NULL, [SP]
    // 0xbd42c4: r4 = const [0x1, 0, 0, 0, null]
    //     0xbd42c4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbd42c8: r0 = GetNavigation.back()
    //     0xbd42c8: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbd42cc: ldur            x0, [fp, #-8]
    // 0xbd42d0: LoadField: r1 = r0->field_f
    //     0xbd42d0: ldur            w1, [x0, #0xf]
    // 0xbd42d4: DecompressPointer r1
    //     0xbd42d4: add             x1, x1, HEAP, lsl #32
    // 0xbd42d8: LoadField: r0 = r1->field_b
    //     0xbd42d8: ldur            w0, [x1, #0xb]
    // 0xbd42dc: DecompressPointer r0
    //     0xbd42dc: add             x0, x0, HEAP, lsl #32
    // 0xbd42e0: cmp             w0, NULL
    // 0xbd42e4: b.eq            #0xbd4324
    // 0xbd42e8: LoadField: r1 = r0->field_f
    //     0xbd42e8: ldur            w1, [x0, #0xf]
    // 0xbd42ec: DecompressPointer r1
    //     0xbd42ec: add             x1, x1, HEAP, lsl #32
    // 0xbd42f0: str             x1, [SP]
    // 0xbd42f4: r4 = 0
    //     0xbd42f4: movz            x4, #0
    // 0xbd42f8: ldr             x0, [SP]
    // 0xbd42fc: r16 = UnlinkedCall_0x613b5c
    //     0xbd42fc: add             x16, PP, #0x54, lsl #12  ; [pp+0x54318] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd4300: add             x16, x16, #0x318
    // 0xbd4304: ldp             x5, lr, [x16]
    // 0xbd4308: blr             lr
    // 0xbd430c: r0 = Null
    //     0xbd430c: mov             x0, NULL
    // 0xbd4310: LeaveFrame
    //     0xbd4310: mov             SP, fp
    //     0xbd4314: ldp             fp, lr, [SP], #0x10
    // 0xbd4318: ret
    //     0xbd4318: ret             
    // 0xbd431c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd431c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd4320: b               #0xbd42a4
    // 0xbd4324: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd4324: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd4328, size: 0xac
    // 0xbd4328: EnterFrame
    //     0xbd4328: stp             fp, lr, [SP, #-0x10]!
    //     0xbd432c: mov             fp, SP
    // 0xbd4330: AllocStack(0x10)
    //     0xbd4330: sub             SP, SP, #0x10
    // 0xbd4334: SetupParameters()
    //     0xbd4334: ldr             x0, [fp, #0x10]
    //     0xbd4338: ldur            w1, [x0, #0x17]
    //     0xbd433c: add             x1, x1, HEAP, lsl #32
    //     0xbd4340: stur            x1, [fp, #-8]
    // 0xbd4344: CheckStackOverflow
    //     0xbd4344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd4348: cmp             SP, x16
    //     0xbd434c: b.ls            #0xbd43c8
    // 0xbd4350: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbd4350: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd4354: ldr             x0, [x0, #0x1c80]
    //     0xbd4358: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd435c: cmp             w0, w16
    //     0xbd4360: b.ne            #0xbd436c
    //     0xbd4364: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbd4368: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd436c: str             NULL, [SP]
    // 0xbd4370: r4 = const [0x1, 0, 0, 0, null]
    //     0xbd4370: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbd4374: r0 = GetNavigation.back()
    //     0xbd4374: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbd4378: ldur            x0, [fp, #-8]
    // 0xbd437c: LoadField: r1 = r0->field_f
    //     0xbd437c: ldur            w1, [x0, #0xf]
    // 0xbd4380: DecompressPointer r1
    //     0xbd4380: add             x1, x1, HEAP, lsl #32
    // 0xbd4384: LoadField: r0 = r1->field_b
    //     0xbd4384: ldur            w0, [x1, #0xb]
    // 0xbd4388: DecompressPointer r0
    //     0xbd4388: add             x0, x0, HEAP, lsl #32
    // 0xbd438c: cmp             w0, NULL
    // 0xbd4390: b.eq            #0xbd43d0
    // 0xbd4394: LoadField: r1 = r0->field_13
    //     0xbd4394: ldur            w1, [x0, #0x13]
    // 0xbd4398: DecompressPointer r1
    //     0xbd4398: add             x1, x1, HEAP, lsl #32
    // 0xbd439c: str             x1, [SP]
    // 0xbd43a0: r4 = 0
    //     0xbd43a0: movz            x4, #0
    // 0xbd43a4: ldr             x0, [SP]
    // 0xbd43a8: r16 = UnlinkedCall_0x613b5c
    //     0xbd43a8: add             x16, PP, #0x54, lsl #12  ; [pp+0x54328] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd43ac: add             x16, x16, #0x328
    // 0xbd43b0: ldp             x5, lr, [x16]
    // 0xbd43b4: blr             lr
    // 0xbd43b8: r0 = Null
    //     0xbd43b8: mov             x0, NULL
    // 0xbd43bc: LeaveFrame
    //     0xbd43bc: mov             SP, fp
    //     0xbd43c0: ldp             fp, lr, [SP], #0x10
    // 0xbd43c4: ret
    //     0xbd43c4: ret             
    // 0xbd43c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd43c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd43cc: b               #0xbd4350
    // 0xbd43d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd43d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4008, size: 0x18, field offset: 0xc
//   const constructor, 
class PushOnlinePopupBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc806e0, size: 0x24
    // 0xc806e0: EnterFrame
    //     0xc806e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc806e4: mov             fp, SP
    // 0xc806e8: mov             x0, x1
    // 0xc806ec: r1 = <PushOnlinePopupBottomSheet>
    //     0xc806ec: add             x1, PP, #0x48, lsl #12  ; [pp+0x48678] TypeArguments: <PushOnlinePopupBottomSheet>
    //     0xc806f0: ldr             x1, [x1, #0x678]
    // 0xc806f4: r0 = _PartialCodPopupBottomSheet()
    //     0xc806f4: bl              #0xc80704  ; Allocate_PartialCodPopupBottomSheetStub -> _PartialCodPopupBottomSheet (size=0x14)
    // 0xc806f8: LeaveFrame
    //     0xc806f8: mov             SP, fp
    //     0xc806fc: ldp             fp, lr, [SP], #0x10
    // 0xc80700: ret
    //     0xc80700: ret             
  }
}
