// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/customised_strip.dart

// class id: 1049225, size: 0x8
class :: {
}

// class id: 3467, size: 0x14, field offset: 0x14
class _CustomisedStripState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xab6124, size: 0x3f0
    // 0xab6124: EnterFrame
    //     0xab6124: stp             fp, lr, [SP, #-0x10]!
    //     0xab6128: mov             fp, SP
    // 0xab612c: AllocStack(0x50)
    //     0xab612c: sub             SP, SP, #0x50
    // 0xab6130: SetupParameters(_CustomisedStripState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xab6130: mov             x0, x1
    //     0xab6134: stur            x1, [fp, #-8]
    //     0xab6138: mov             x1, x2
    //     0xab613c: stur            x2, [fp, #-0x10]
    // 0xab6140: CheckStackOverflow
    //     0xab6140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6144: cmp             SP, x16
    //     0xab6148: b.ls            #0xab6500
    // 0xab614c: r1 = 1
    //     0xab614c: movz            x1, #0x1
    // 0xab6150: r0 = AllocateContext()
    //     0xab6150: bl              #0x16f6108  ; AllocateContextStub
    // 0xab6154: mov             x2, x0
    // 0xab6158: ldur            x0, [fp, #-8]
    // 0xab615c: stur            x2, [fp, #-0x18]
    // 0xab6160: StoreField: r2->field_f = r0
    //     0xab6160: stur            w0, [x2, #0xf]
    // 0xab6164: ldur            x1, [fp, #-0x10]
    // 0xab6168: r0 = of()
    //     0xab6168: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab616c: LoadField: r1 = r0->field_5b
    //     0xab616c: ldur            w1, [x0, #0x5b]
    // 0xab6170: DecompressPointer r1
    //     0xab6170: add             x1, x1, HEAP, lsl #32
    // 0xab6174: r0 = LoadClassIdInstr(r1)
    //     0xab6174: ldur            x0, [x1, #-1]
    //     0xab6178: ubfx            x0, x0, #0xc, #0x14
    // 0xab617c: d0 = 0.030000
    //     0xab617c: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xab6180: ldr             d0, [x17, #0x238]
    // 0xab6184: r0 = GDT[cid_x0 + -0xffa]()
    //     0xab6184: sub             lr, x0, #0xffa
    //     0xab6188: ldr             lr, [x21, lr, lsl #3]
    //     0xab618c: blr             lr
    // 0xab6190: stur            x0, [fp, #-0x20]
    // 0xab6194: r0 = Radius()
    //     0xab6194: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xab6198: d0 = 30.000000
    //     0xab6198: fmov            d0, #30.00000000
    // 0xab619c: stur            x0, [fp, #-0x28]
    // 0xab61a0: StoreField: r0->field_7 = d0
    //     0xab61a0: stur            d0, [x0, #7]
    // 0xab61a4: StoreField: r0->field_f = d0
    //     0xab61a4: stur            d0, [x0, #0xf]
    // 0xab61a8: r0 = BorderRadius()
    //     0xab61a8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xab61ac: mov             x1, x0
    // 0xab61b0: ldur            x0, [fp, #-0x28]
    // 0xab61b4: stur            x1, [fp, #-0x30]
    // 0xab61b8: StoreField: r1->field_7 = r0
    //     0xab61b8: stur            w0, [x1, #7]
    // 0xab61bc: StoreField: r1->field_b = r0
    //     0xab61bc: stur            w0, [x1, #0xb]
    // 0xab61c0: StoreField: r1->field_f = r0
    //     0xab61c0: stur            w0, [x1, #0xf]
    // 0xab61c4: StoreField: r1->field_13 = r0
    //     0xab61c4: stur            w0, [x1, #0x13]
    // 0xab61c8: r0 = BoxDecoration()
    //     0xab61c8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xab61cc: mov             x2, x0
    // 0xab61d0: ldur            x0, [fp, #-0x20]
    // 0xab61d4: stur            x2, [fp, #-0x28]
    // 0xab61d8: StoreField: r2->field_7 = r0
    //     0xab61d8: stur            w0, [x2, #7]
    // 0xab61dc: ldur            x0, [fp, #-0x30]
    // 0xab61e0: StoreField: r2->field_13 = r0
    //     0xab61e0: stur            w0, [x2, #0x13]
    // 0xab61e4: r0 = Instance_BoxShape
    //     0xab61e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xab61e8: ldr             x0, [x0, #0x80]
    // 0xab61ec: StoreField: r2->field_23 = r0
    //     0xab61ec: stur            w0, [x2, #0x23]
    // 0xab61f0: ldur            x1, [fp, #-0x10]
    // 0xab61f4: r0 = of()
    //     0xab61f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab61f8: LoadField: r1 = r0->field_87
    //     0xab61f8: ldur            w1, [x0, #0x87]
    // 0xab61fc: DecompressPointer r1
    //     0xab61fc: add             x1, x1, HEAP, lsl #32
    // 0xab6200: LoadField: r0 = r1->field_7
    //     0xab6200: ldur            w0, [x1, #7]
    // 0xab6204: DecompressPointer r0
    //     0xab6204: add             x0, x0, HEAP, lsl #32
    // 0xab6208: r16 = 16.000000
    //     0xab6208: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xab620c: ldr             x16, [x16, #0x188]
    // 0xab6210: r30 = Instance_Color
    //     0xab6210: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab6214: stp             lr, x16, [SP]
    // 0xab6218: mov             x1, x0
    // 0xab621c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab621c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab6220: ldr             x4, [x4, #0xaa0]
    // 0xab6224: r0 = copyWith()
    //     0xab6224: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab6228: stur            x0, [fp, #-0x20]
    // 0xab622c: r0 = Text()
    //     0xab622c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab6230: mov             x2, x0
    // 0xab6234: r0 = "Customisation"
    //     0xab6234: add             x0, PP, #0x56, lsl #12  ; [pp+0x56388] "Customisation"
    //     0xab6238: ldr             x0, [x0, #0x388]
    // 0xab623c: stur            x2, [fp, #-0x30]
    // 0xab6240: StoreField: r2->field_b = r0
    //     0xab6240: stur            w0, [x2, #0xb]
    // 0xab6244: ldur            x0, [fp, #-0x20]
    // 0xab6248: StoreField: r2->field_13 = r0
    //     0xab6248: stur            w0, [x2, #0x13]
    // 0xab624c: ldur            x3, [fp, #-8]
    // 0xab6250: LoadField: r0 = r3->field_b
    //     0xab6250: ldur            w0, [x3, #0xb]
    // 0xab6254: DecompressPointer r0
    //     0xab6254: add             x0, x0, HEAP, lsl #32
    // 0xab6258: cmp             w0, NULL
    // 0xab625c: b.eq            #0xab6508
    // 0xab6260: LoadField: r1 = r0->field_b
    //     0xab6260: ldur            w1, [x0, #0xb]
    // 0xab6264: DecompressPointer r1
    //     0xab6264: add             x1, x1, HEAP, lsl #32
    // 0xab6268: cmp             w1, NULL
    // 0xab626c: b.ne            #0xab6278
    // 0xab6270: r0 = Null
    //     0xab6270: mov             x0, NULL
    // 0xab6274: b               #0xab6290
    // 0xab6278: r0 = LoadClassIdInstr(r1)
    //     0xab6278: ldur            x0, [x1, #-1]
    //     0xab627c: ubfx            x0, x0, #0xc, #0x14
    // 0xab6280: r0 = GDT[cid_x0 + 0xe517]()
    //     0xab6280: movz            x17, #0xe517
    //     0xab6284: add             lr, x0, x17
    //     0xab6288: ldr             lr, [x21, lr, lsl #3]
    //     0xab628c: blr             lr
    // 0xab6290: cmp             w0, NULL
    // 0xab6294: b.ne            #0xab62a0
    // 0xab6298: ldur            x0, [fp, #-8]
    // 0xab629c: b               #0xab636c
    // 0xab62a0: tbnz            w0, #4, #0xab6368
    // 0xab62a4: ldur            x0, [fp, #-8]
    // 0xab62a8: LoadField: r1 = r0->field_b
    //     0xab62a8: ldur            w1, [x0, #0xb]
    // 0xab62ac: DecompressPointer r1
    //     0xab62ac: add             x1, x1, HEAP, lsl #32
    // 0xab62b0: cmp             w1, NULL
    // 0xab62b4: b.eq            #0xab650c
    // 0xab62b8: LoadField: r0 = r1->field_b
    //     0xab62b8: ldur            w0, [x1, #0xb]
    // 0xab62bc: DecompressPointer r0
    //     0xab62bc: add             x0, x0, HEAP, lsl #32
    // 0xab62c0: cmp             w0, NULL
    // 0xab62c4: b.ne            #0xab62d0
    // 0xab62c8: r0 = Null
    //     0xab62c8: mov             x0, NULL
    // 0xab62cc: b               #0xab62f0
    // 0xab62d0: r1 = LoadClassIdInstr(r0)
    //     0xab62d0: ldur            x1, [x0, #-1]
    //     0xab62d4: ubfx            x1, x1, #0xc, #0x14
    // 0xab62d8: str             x0, [SP]
    // 0xab62dc: mov             x0, x1
    // 0xab62e0: r0 = GDT[cid_x0 + 0xc898]()
    //     0xab62e0: movz            x17, #0xc898
    //     0xab62e4: add             lr, x0, x17
    //     0xab62e8: ldr             lr, [x21, lr, lsl #3]
    //     0xab62ec: blr             lr
    // 0xab62f0: cmp             w0, NULL
    // 0xab62f4: b.ne            #0xab6300
    // 0xab62f8: r0 = 0
    //     0xab62f8: movz            x0, #0
    // 0xab62fc: b               #0xab6308
    // 0xab6300: r1 = LoadInt32Instr(r0)
    //     0xab6300: sbfx            x1, x0, #1, #0x1f
    // 0xab6304: mov             x0, x1
    // 0xab6308: add             x2, x0, #1
    // 0xab630c: r0 = BoxInt64Instr(r2)
    //     0xab630c: sbfiz           x0, x2, #1, #0x1f
    //     0xab6310: cmp             x2, x0, asr #1
    //     0xab6314: b.eq            #0xab6320
    //     0xab6318: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab631c: stur            x2, [x0, #7]
    // 0xab6320: ldur            x2, [fp, #-0x18]
    // 0xab6324: r1 = Function '<anonymous closure>':.
    //     0xab6324: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ad78] AnonymousClosure: (0xab6eb0), in [package:customer_app/app/presentation/views/cosmetic/bag/customised_strip.dart] _CustomisedStripState::build (0xab6124)
    //     0xab6328: ldr             x1, [x1, #0xd78]
    // 0xab632c: stur            x0, [fp, #-0x20]
    // 0xab6330: r0 = AllocateClosure()
    //     0xab6330: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab6334: stur            x0, [fp, #-0x38]
    // 0xab6338: r0 = ListView()
    //     0xab6338: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xab633c: stur            x0, [fp, #-0x40]
    // 0xab6340: r16 = true
    //     0xab6340: add             x16, NULL, #0x20  ; true
    // 0xab6344: str             x16, [SP]
    // 0xab6348: mov             x1, x0
    // 0xab634c: ldur            x2, [fp, #-0x38]
    // 0xab6350: ldur            x3, [fp, #-0x20]
    // 0xab6354: r4 = const [0, 0x4, 0x1, 0x3, shrinkWrap, 0x3, null]
    //     0xab6354: add             x4, PP, #0x49, lsl #12  ; [pp+0x490f0] List(7) [0, 0x4, 0x1, 0x3, "shrinkWrap", 0x3, Null]
    //     0xab6358: ldr             x4, [x4, #0xf0]
    // 0xab635c: r0 = ListView.builder()
    //     0xab635c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xab6360: ldur            x2, [fp, #-0x40]
    // 0xab6364: b               #0xab6424
    // 0xab6368: ldur            x0, [fp, #-8]
    // 0xab636c: LoadField: r1 = r0->field_b
    //     0xab636c: ldur            w1, [x0, #0xb]
    // 0xab6370: DecompressPointer r1
    //     0xab6370: add             x1, x1, HEAP, lsl #32
    // 0xab6374: cmp             w1, NULL
    // 0xab6378: b.eq            #0xab6510
    // 0xab637c: LoadField: r0 = r1->field_f
    //     0xab637c: ldur            w0, [x1, #0xf]
    // 0xab6380: DecompressPointer r0
    //     0xab6380: add             x0, x0, HEAP, lsl #32
    // 0xab6384: cmp             w0, NULL
    // 0xab6388: b.ne            #0xab6394
    // 0xab638c: r0 = Null
    //     0xab638c: mov             x0, NULL
    // 0xab6390: b               #0xab63b0
    // 0xab6394: LoadField: r1 = r0->field_13
    //     0xab6394: ldur            w1, [x0, #0x13]
    // 0xab6398: DecompressPointer r1
    //     0xab6398: add             x1, x1, HEAP, lsl #32
    // 0xab639c: cmp             w1, NULL
    // 0xab63a0: b.ne            #0xab63ac
    // 0xab63a4: r0 = Null
    //     0xab63a4: mov             x0, NULL
    // 0xab63a8: b               #0xab63b0
    // 0xab63ac: LoadField: r0 = r1->field_b
    //     0xab63ac: ldur            w0, [x1, #0xb]
    // 0xab63b0: cmp             w0, NULL
    // 0xab63b4: b.ne            #0xab63c0
    // 0xab63b8: r0 = 0
    //     0xab63b8: movz            x0, #0
    // 0xab63bc: b               #0xab63c8
    // 0xab63c0: r1 = LoadInt32Instr(r0)
    //     0xab63c0: sbfx            x1, x0, #1, #0x1f
    // 0xab63c4: mov             x0, x1
    // 0xab63c8: add             x2, x0, #1
    // 0xab63cc: r0 = BoxInt64Instr(r2)
    //     0xab63cc: sbfiz           x0, x2, #1, #0x1f
    //     0xab63d0: cmp             x2, x0, asr #1
    //     0xab63d4: b.eq            #0xab63e0
    //     0xab63d8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xab63dc: stur            x2, [x0, #7]
    // 0xab63e0: ldur            x2, [fp, #-0x18]
    // 0xab63e4: r1 = Function '<anonymous closure>':.
    //     0xab63e4: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5ad80] AnonymousClosure: (0xab6514), in [package:customer_app/app/presentation/views/cosmetic/bag/customised_strip.dart] _CustomisedStripState::build (0xab6124)
    //     0xab63e8: ldr             x1, [x1, #0xd80]
    // 0xab63ec: stur            x0, [fp, #-8]
    // 0xab63f0: r0 = AllocateClosure()
    //     0xab63f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xab63f4: stur            x0, [fp, #-0x18]
    // 0xab63f8: r0 = ListView()
    //     0xab63f8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xab63fc: stur            x0, [fp, #-0x20]
    // 0xab6400: r16 = true
    //     0xab6400: add             x16, NULL, #0x20  ; true
    // 0xab6404: str             x16, [SP]
    // 0xab6408: mov             x1, x0
    // 0xab640c: ldur            x2, [fp, #-0x18]
    // 0xab6410: ldur            x3, [fp, #-8]
    // 0xab6414: r4 = const [0, 0x4, 0x1, 0x3, shrinkWrap, 0x3, null]
    //     0xab6414: add             x4, PP, #0x49, lsl #12  ; [pp+0x490f0] List(7) [0, 0x4, 0x1, 0x3, "shrinkWrap", 0x3, Null]
    //     0xab6418: ldr             x4, [x4, #0xf0]
    // 0xab641c: r0 = ListView.builder()
    //     0xab641c: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xab6420: ldur            x2, [fp, #-0x20]
    // 0xab6424: ldur            x0, [fp, #-0x30]
    // 0xab6428: ldur            x1, [fp, #-0x10]
    // 0xab642c: stur            x2, [fp, #-8]
    // 0xab6430: r0 = of()
    //     0xab6430: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab6434: LoadField: r1 = r0->field_87
    //     0xab6434: ldur            w1, [x0, #0x87]
    // 0xab6438: DecompressPointer r1
    //     0xab6438: add             x1, x1, HEAP, lsl #32
    // 0xab643c: LoadField: r0 = r1->field_7
    //     0xab643c: ldur            w0, [x1, #7]
    // 0xab6440: DecompressPointer r0
    //     0xab6440: add             x0, x0, HEAP, lsl #32
    // 0xab6444: r16 = 14.000000
    //     0xab6444: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab6448: ldr             x16, [x16, #0x1d8]
    // 0xab644c: r30 = Instance_Color
    //     0xab644c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab6450: stp             lr, x16, [SP]
    // 0xab6454: mov             x1, x0
    // 0xab6458: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab6458: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab645c: ldr             x4, [x4, #0xaa0]
    // 0xab6460: r0 = copyWith()
    //     0xab6460: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab6464: r0 = Accordion()
    //     0xab6464: bl              #0xa06e8c  ; AllocateAccordionStub -> Accordion (size=0x40)
    // 0xab6468: mov             x1, x0
    // 0xab646c: ldur            x0, [fp, #-0x30]
    // 0xab6470: stur            x1, [fp, #-0x10]
    // 0xab6474: StoreField: r1->field_b = r0
    //     0xab6474: stur            w0, [x1, #0xb]
    // 0xab6478: ldur            x0, [fp, #-8]
    // 0xab647c: StoreField: r1->field_13 = r0
    //     0xab647c: stur            w0, [x1, #0x13]
    // 0xab6480: r0 = false
    //     0xab6480: add             x0, NULL, #0x30  ; false
    // 0xab6484: ArrayStore: r1[0] = r0  ; List_4
    //     0xab6484: stur            w0, [x1, #0x17]
    // 0xab6488: d0 = 24.000000
    //     0xab6488: fmov            d0, #24.00000000
    // 0xab648c: StoreField: r1->field_1b = d0
    //     0xab648c: stur            d0, [x1, #0x1b]
    // 0xab6490: r0 = true
    //     0xab6490: add             x0, NULL, #0x20  ; true
    // 0xab6494: StoreField: r1->field_23 = r0
    //     0xab6494: stur            w0, [x1, #0x23]
    // 0xab6498: r0 = Padding()
    //     0xab6498: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab649c: mov             x1, x0
    // 0xab64a0: r0 = Instance_EdgeInsets
    //     0xab64a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab64a4: ldr             x0, [x0, #0x668]
    // 0xab64a8: stur            x1, [fp, #-8]
    // 0xab64ac: StoreField: r1->field_f = r0
    //     0xab64ac: stur            w0, [x1, #0xf]
    // 0xab64b0: ldur            x0, [fp, #-0x10]
    // 0xab64b4: StoreField: r1->field_b = r0
    //     0xab64b4: stur            w0, [x1, #0xb]
    // 0xab64b8: r0 = Container()
    //     0xab64b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xab64bc: stur            x0, [fp, #-0x10]
    // 0xab64c0: ldur            x16, [fp, #-0x28]
    // 0xab64c4: ldur            lr, [fp, #-8]
    // 0xab64c8: stp             lr, x16, [SP]
    // 0xab64cc: mov             x1, x0
    // 0xab64d0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xab64d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xab64d4: ldr             x4, [x4, #0x88]
    // 0xab64d8: r0 = Container()
    //     0xab64d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xab64dc: r0 = Padding()
    //     0xab64dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab64e0: r1 = Instance_EdgeInsets
    //     0xab64e0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xab64e4: ldr             x1, [x1, #0x668]
    // 0xab64e8: StoreField: r0->field_f = r1
    //     0xab64e8: stur            w1, [x0, #0xf]
    // 0xab64ec: ldur            x1, [fp, #-0x10]
    // 0xab64f0: StoreField: r0->field_b = r1
    //     0xab64f0: stur            w1, [x0, #0xb]
    // 0xab64f4: LeaveFrame
    //     0xab64f4: mov             SP, fp
    //     0xab64f8: ldp             fp, lr, [SP], #0x10
    // 0xab64fc: ret
    //     0xab64fc: ret             
    // 0xab6500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6500: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6504: b               #0xab614c
    // 0xab6508: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6508: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab650c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab650c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6510: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6510: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xab6514, size: 0x96c
    // 0xab6514: EnterFrame
    //     0xab6514: stp             fp, lr, [SP, #-0x10]!
    //     0xab6518: mov             fp, SP
    // 0xab651c: AllocStack(0x40)
    //     0xab651c: sub             SP, SP, #0x40
    // 0xab6520: SetupParameters()
    //     0xab6520: ldr             x0, [fp, #0x20]
    //     0xab6524: ldur            w1, [x0, #0x17]
    //     0xab6528: add             x1, x1, HEAP, lsl #32
    //     0xab652c: stur            x1, [fp, #-8]
    // 0xab6530: CheckStackOverflow
    //     0xab6530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6534: cmp             SP, x16
    //     0xab6538: b.ls            #0xab6e44
    // 0xab653c: r0 = Container()
    //     0xab653c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xab6540: mov             x1, x0
    // 0xab6544: stur            x0, [fp, #-0x10]
    // 0xab6548: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab6548: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab654c: r0 = Container()
    //     0xab654c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xab6550: ldur            x0, [fp, #-8]
    // 0xab6554: LoadField: r1 = r0->field_f
    //     0xab6554: ldur            w1, [x0, #0xf]
    // 0xab6558: DecompressPointer r1
    //     0xab6558: add             x1, x1, HEAP, lsl #32
    // 0xab655c: LoadField: r2 = r1->field_b
    //     0xab655c: ldur            w2, [x1, #0xb]
    // 0xab6560: DecompressPointer r2
    //     0xab6560: add             x2, x2, HEAP, lsl #32
    // 0xab6564: cmp             w2, NULL
    // 0xab6568: b.eq            #0xab6e4c
    // 0xab656c: LoadField: r1 = r2->field_f
    //     0xab656c: ldur            w1, [x2, #0xf]
    // 0xab6570: DecompressPointer r1
    //     0xab6570: add             x1, x1, HEAP, lsl #32
    // 0xab6574: cmp             w1, NULL
    // 0xab6578: b.ne            #0xab6584
    // 0xab657c: r4 = Null
    //     0xab657c: mov             x4, NULL
    // 0xab6580: b               #0xab65a8
    // 0xab6584: LoadField: r3 = r1->field_13
    //     0xab6584: ldur            w3, [x1, #0x13]
    // 0xab6588: DecompressPointer r3
    //     0xab6588: add             x3, x3, HEAP, lsl #32
    // 0xab658c: cmp             w3, NULL
    // 0xab6590: b.ne            #0xab659c
    // 0xab6594: r3 = Null
    //     0xab6594: mov             x3, NULL
    // 0xab6598: b               #0xab65a4
    // 0xab659c: LoadField: r4 = r3->field_b
    //     0xab659c: ldur            w4, [x3, #0xb]
    // 0xab65a0: mov             x3, x4
    // 0xab65a4: mov             x4, x3
    // 0xab65a8: ldr             x3, [fp, #0x10]
    // 0xab65ac: cmp             w4, w3
    // 0xab65b0: b.ne            #0xab6838
    // 0xab65b4: ldr             x1, [fp, #0x18]
    // 0xab65b8: r0 = of()
    //     0xab65b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab65bc: LoadField: r1 = r0->field_87
    //     0xab65bc: ldur            w1, [x0, #0x87]
    // 0xab65c0: DecompressPointer r1
    //     0xab65c0: add             x1, x1, HEAP, lsl #32
    // 0xab65c4: LoadField: r0 = r1->field_7
    //     0xab65c4: ldur            w0, [x1, #7]
    // 0xab65c8: DecompressPointer r0
    //     0xab65c8: add             x0, x0, HEAP, lsl #32
    // 0xab65cc: r16 = 14.000000
    //     0xab65cc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab65d0: ldr             x16, [x16, #0x1d8]
    // 0xab65d4: r30 = Instance_Color
    //     0xab65d4: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab65d8: stp             lr, x16, [SP]
    // 0xab65dc: mov             x1, x0
    // 0xab65e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab65e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab65e4: ldr             x4, [x4, #0xaa0]
    // 0xab65e8: r0 = copyWith()
    //     0xab65e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab65ec: stur            x0, [fp, #-0x18]
    // 0xab65f0: r0 = Text()
    //     0xab65f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab65f4: mov             x1, x0
    // 0xab65f8: r0 = "Total"
    //     0xab65f8: add             x0, PP, #0x54, lsl #12  ; [pp+0x54bc8] "Total"
    //     0xab65fc: ldr             x0, [x0, #0xbc8]
    // 0xab6600: stur            x1, [fp, #-0x20]
    // 0xab6604: StoreField: r1->field_b = r0
    //     0xab6604: stur            w0, [x1, #0xb]
    // 0xab6608: ldur            x0, [fp, #-0x18]
    // 0xab660c: StoreField: r1->field_13 = r0
    //     0xab660c: stur            w0, [x1, #0x13]
    // 0xab6610: ldur            x4, [fp, #-8]
    // 0xab6614: LoadField: r0 = r4->field_f
    //     0xab6614: ldur            w0, [x4, #0xf]
    // 0xab6618: DecompressPointer r0
    //     0xab6618: add             x0, x0, HEAP, lsl #32
    // 0xab661c: LoadField: r2 = r0->field_b
    //     0xab661c: ldur            w2, [x0, #0xb]
    // 0xab6620: DecompressPointer r2
    //     0xab6620: add             x2, x2, HEAP, lsl #32
    // 0xab6624: cmp             w2, NULL
    // 0xab6628: b.eq            #0xab6e50
    // 0xab662c: LoadField: r0 = r2->field_13
    //     0xab662c: ldur            w0, [x2, #0x13]
    // 0xab6630: DecompressPointer r0
    //     0xab6630: add             x0, x0, HEAP, lsl #32
    // 0xab6634: r2 = LoadClassIdInstr(r0)
    //     0xab6634: ldur            x2, [x0, #-1]
    //     0xab6638: ubfx            x2, x2, #0xc, #0x14
    // 0xab663c: str             x0, [SP]
    // 0xab6640: mov             x0, x2
    // 0xab6644: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xab6644: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xab6648: r0 = GDT[cid_x0 + 0x2700]()
    //     0xab6648: movz            x17, #0x2700
    //     0xab664c: add             lr, x0, x17
    //     0xab6650: ldr             lr, [x21, lr, lsl #3]
    //     0xab6654: blr             lr
    // 0xab6658: ldr             x1, [fp, #0x18]
    // 0xab665c: stur            x0, [fp, #-0x18]
    // 0xab6660: r0 = of()
    //     0xab6660: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab6664: LoadField: r1 = r0->field_87
    //     0xab6664: ldur            w1, [x0, #0x87]
    // 0xab6668: DecompressPointer r1
    //     0xab6668: add             x1, x1, HEAP, lsl #32
    // 0xab666c: LoadField: r0 = r1->field_7
    //     0xab666c: ldur            w0, [x1, #7]
    // 0xab6670: DecompressPointer r0
    //     0xab6670: add             x0, x0, HEAP, lsl #32
    // 0xab6674: r16 = 14.000000
    //     0xab6674: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab6678: ldr             x16, [x16, #0x1d8]
    // 0xab667c: r30 = Instance_Color
    //     0xab667c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab6680: stp             lr, x16, [SP]
    // 0xab6684: mov             x1, x0
    // 0xab6688: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xab6688: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xab668c: ldr             x4, [x4, #0xaa0]
    // 0xab6690: r0 = copyWith()
    //     0xab6690: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab6694: stur            x0, [fp, #-0x28]
    // 0xab6698: r0 = Text()
    //     0xab6698: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab669c: mov             x3, x0
    // 0xab66a0: ldur            x0, [fp, #-0x18]
    // 0xab66a4: stur            x3, [fp, #-0x30]
    // 0xab66a8: StoreField: r3->field_b = r0
    //     0xab66a8: stur            w0, [x3, #0xb]
    // 0xab66ac: ldur            x0, [fp, #-0x28]
    // 0xab66b0: StoreField: r3->field_13 = r0
    //     0xab66b0: stur            w0, [x3, #0x13]
    // 0xab66b4: r1 = Null
    //     0xab66b4: mov             x1, NULL
    // 0xab66b8: r2 = 6
    //     0xab66b8: movz            x2, #0x6
    // 0xab66bc: r0 = AllocateArray()
    //     0xab66bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab66c0: mov             x2, x0
    // 0xab66c4: ldur            x0, [fp, #-0x20]
    // 0xab66c8: stur            x2, [fp, #-0x18]
    // 0xab66cc: StoreField: r2->field_f = r0
    //     0xab66cc: stur            w0, [x2, #0xf]
    // 0xab66d0: r16 = Instance_Spacer
    //     0xab66d0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xab66d4: ldr             x16, [x16, #0xf0]
    // 0xab66d8: StoreField: r2->field_13 = r16
    //     0xab66d8: stur            w16, [x2, #0x13]
    // 0xab66dc: ldur            x0, [fp, #-0x30]
    // 0xab66e0: ArrayStore: r2[0] = r0  ; List_4
    //     0xab66e0: stur            w0, [x2, #0x17]
    // 0xab66e4: r1 = <Widget>
    //     0xab66e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab66e8: r0 = AllocateGrowableArray()
    //     0xab66e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab66ec: mov             x1, x0
    // 0xab66f0: ldur            x0, [fp, #-0x18]
    // 0xab66f4: stur            x1, [fp, #-0x20]
    // 0xab66f8: StoreField: r1->field_f = r0
    //     0xab66f8: stur            w0, [x1, #0xf]
    // 0xab66fc: r0 = 6
    //     0xab66fc: movz            x0, #0x6
    // 0xab6700: StoreField: r1->field_b = r0
    //     0xab6700: stur            w0, [x1, #0xb]
    // 0xab6704: r0 = Row()
    //     0xab6704: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab6708: mov             x3, x0
    // 0xab670c: r0 = Instance_Axis
    //     0xab670c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab6710: stur            x3, [fp, #-0x18]
    // 0xab6714: StoreField: r3->field_f = r0
    //     0xab6714: stur            w0, [x3, #0xf]
    // 0xab6718: r0 = Instance_MainAxisAlignment
    //     0xab6718: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab671c: ldr             x0, [x0, #0xa08]
    // 0xab6720: StoreField: r3->field_13 = r0
    //     0xab6720: stur            w0, [x3, #0x13]
    // 0xab6724: r4 = Instance_MainAxisSize
    //     0xab6724: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab6728: ldr             x4, [x4, #0xa10]
    // 0xab672c: ArrayStore: r3[0] = r4  ; List_4
    //     0xab672c: stur            w4, [x3, #0x17]
    // 0xab6730: r5 = Instance_CrossAxisAlignment
    //     0xab6730: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab6734: ldr             x5, [x5, #0xa18]
    // 0xab6738: StoreField: r3->field_1b = r5
    //     0xab6738: stur            w5, [x3, #0x1b]
    // 0xab673c: r6 = Instance_VerticalDirection
    //     0xab673c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab6740: ldr             x6, [x6, #0xa20]
    // 0xab6744: StoreField: r3->field_23 = r6
    //     0xab6744: stur            w6, [x3, #0x23]
    // 0xab6748: r7 = Instance_Clip
    //     0xab6748: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab674c: ldr             x7, [x7, #0x38]
    // 0xab6750: StoreField: r3->field_2b = r7
    //     0xab6750: stur            w7, [x3, #0x2b]
    // 0xab6754: StoreField: r3->field_2f = rZR
    //     0xab6754: stur            xzr, [x3, #0x2f]
    // 0xab6758: ldur            x1, [fp, #-0x20]
    // 0xab675c: StoreField: r3->field_b = r1
    //     0xab675c: stur            w1, [x3, #0xb]
    // 0xab6760: r1 = Null
    //     0xab6760: mov             x1, NULL
    // 0xab6764: r2 = 4
    //     0xab6764: movz            x2, #0x4
    // 0xab6768: r0 = AllocateArray()
    //     0xab6768: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab676c: stur            x0, [fp, #-0x20]
    // 0xab6770: r16 = Instance_Divider
    //     0xab6770: add             x16, PP, #0x57, lsl #12  ; [pp+0x57260] Obj!Divider@d66cd1
    //     0xab6774: ldr             x16, [x16, #0x260]
    // 0xab6778: StoreField: r0->field_f = r16
    //     0xab6778: stur            w16, [x0, #0xf]
    // 0xab677c: ldur            x1, [fp, #-0x18]
    // 0xab6780: StoreField: r0->field_13 = r1
    //     0xab6780: stur            w1, [x0, #0x13]
    // 0xab6784: r1 = <Widget>
    //     0xab6784: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab6788: r0 = AllocateGrowableArray()
    //     0xab6788: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab678c: mov             x1, x0
    // 0xab6790: ldur            x0, [fp, #-0x20]
    // 0xab6794: stur            x1, [fp, #-0x18]
    // 0xab6798: StoreField: r1->field_f = r0
    //     0xab6798: stur            w0, [x1, #0xf]
    // 0xab679c: r0 = 4
    //     0xab679c: movz            x0, #0x4
    // 0xab67a0: StoreField: r1->field_b = r0
    //     0xab67a0: stur            w0, [x1, #0xb]
    // 0xab67a4: r0 = Column()
    //     0xab67a4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xab67a8: mov             x1, x0
    // 0xab67ac: r0 = Instance_Axis
    //     0xab67ac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xab67b0: stur            x1, [fp, #-0x20]
    // 0xab67b4: StoreField: r1->field_f = r0
    //     0xab67b4: stur            w0, [x1, #0xf]
    // 0xab67b8: r0 = Instance_MainAxisAlignment
    //     0xab67b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab67bc: ldr             x0, [x0, #0xa08]
    // 0xab67c0: StoreField: r1->field_13 = r0
    //     0xab67c0: stur            w0, [x1, #0x13]
    // 0xab67c4: r0 = Instance_MainAxisSize
    //     0xab67c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab67c8: ldr             x0, [x0, #0xa10]
    // 0xab67cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xab67cc: stur            w0, [x1, #0x17]
    // 0xab67d0: r0 = Instance_CrossAxisAlignment
    //     0xab67d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab67d4: ldr             x0, [x0, #0xa18]
    // 0xab67d8: StoreField: r1->field_1b = r0
    //     0xab67d8: stur            w0, [x1, #0x1b]
    // 0xab67dc: r0 = Instance_VerticalDirection
    //     0xab67dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab67e0: ldr             x0, [x0, #0xa20]
    // 0xab67e4: StoreField: r1->field_23 = r0
    //     0xab67e4: stur            w0, [x1, #0x23]
    // 0xab67e8: r0 = Instance_Clip
    //     0xab67e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab67ec: ldr             x0, [x0, #0x38]
    // 0xab67f0: StoreField: r1->field_2b = r0
    //     0xab67f0: stur            w0, [x1, #0x2b]
    // 0xab67f4: StoreField: r1->field_2f = rZR
    //     0xab67f4: stur            xzr, [x1, #0x2f]
    // 0xab67f8: ldur            x0, [fp, #-0x18]
    // 0xab67fc: StoreField: r1->field_b = r0
    //     0xab67fc: stur            w0, [x1, #0xb]
    // 0xab6800: r0 = Padding()
    //     0xab6800: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab6804: mov             x1, x0
    // 0xab6808: r0 = Instance_EdgeInsets
    //     0xab6808: add             x0, PP, #0x57, lsl #12  ; [pp+0x57268] Obj!EdgeInsets@d58ca1
    //     0xab680c: ldr             x0, [x0, #0x268]
    // 0xab6810: stur            x1, [fp, #-0x18]
    // 0xab6814: StoreField: r1->field_f = r0
    //     0xab6814: stur            w0, [x1, #0xf]
    // 0xab6818: ldur            x0, [fp, #-0x20]
    // 0xab681c: StoreField: r1->field_b = r0
    //     0xab681c: stur            w0, [x1, #0xb]
    // 0xab6820: r0 = IntrinsicHeight()
    //     0xab6820: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xab6824: mov             x1, x0
    // 0xab6828: ldur            x0, [fp, #-0x18]
    // 0xab682c: StoreField: r1->field_b = r0
    //     0xab682c: stur            w0, [x1, #0xb]
    // 0xab6830: mov             x0, x1
    // 0xab6834: b               #0xab6e38
    // 0xab6838: mov             x4, x0
    // 0xab683c: cmp             w1, NULL
    // 0xab6840: b.ne            #0xab684c
    // 0xab6844: r0 = Null
    //     0xab6844: mov             x0, NULL
    // 0xab6848: b               #0xab68a4
    // 0xab684c: LoadField: r5 = r1->field_13
    //     0xab684c: ldur            w5, [x1, #0x13]
    // 0xab6850: DecompressPointer r5
    //     0xab6850: add             x5, x5, HEAP, lsl #32
    // 0xab6854: cmp             w5, NULL
    // 0xab6858: b.ne            #0xab6864
    // 0xab685c: r0 = Null
    //     0xab685c: mov             x0, NULL
    // 0xab6860: b               #0xab68a4
    // 0xab6864: LoadField: r0 = r5->field_b
    //     0xab6864: ldur            w0, [x5, #0xb]
    // 0xab6868: r6 = LoadInt32Instr(r3)
    //     0xab6868: sbfx            x6, x3, #1, #0x1f
    //     0xab686c: tbz             w3, #0, #0xab6874
    //     0xab6870: ldur            x6, [x3, #7]
    // 0xab6874: r1 = LoadInt32Instr(r0)
    //     0xab6874: sbfx            x1, x0, #1, #0x1f
    // 0xab6878: mov             x0, x1
    // 0xab687c: mov             x1, x6
    // 0xab6880: cmp             x1, x0
    // 0xab6884: b.hs            #0xab6e54
    // 0xab6888: LoadField: r0 = r5->field_f
    //     0xab6888: ldur            w0, [x5, #0xf]
    // 0xab688c: DecompressPointer r0
    //     0xab688c: add             x0, x0, HEAP, lsl #32
    // 0xab6890: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xab6890: add             x16, x0, x6, lsl #2
    //     0xab6894: ldur            w1, [x16, #0xf]
    // 0xab6898: DecompressPointer r1
    //     0xab6898: add             x1, x1, HEAP, lsl #32
    // 0xab689c: LoadField: r0 = r1->field_f
    //     0xab689c: ldur            w0, [x1, #0xf]
    // 0xab68a0: DecompressPointer r0
    //     0xab68a0: add             x0, x0, HEAP, lsl #32
    // 0xab68a4: r16 = Instance_CustomisationType
    //     0xab68a4: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xab68a8: ldr             x16, [x16, #0x660]
    // 0xab68ac: cmp             w0, w16
    // 0xab68b0: b.ne            #0xab69c0
    // 0xab68b4: LoadField: r0 = r2->field_b
    //     0xab68b4: ldur            w0, [x2, #0xb]
    // 0xab68b8: DecompressPointer r0
    //     0xab68b8: add             x0, x0, HEAP, lsl #32
    // 0xab68bc: cmp             w0, NULL
    // 0xab68c0: b.ne            #0xab68d0
    // 0xab68c4: mov             x1, x4
    // 0xab68c8: r2 = Null
    //     0xab68c8: mov             x2, NULL
    // 0xab68cc: b               #0xab68f4
    // 0xab68d0: r1 = LoadClassIdInstr(r0)
    //     0xab68d0: ldur            x1, [x0, #-1]
    //     0xab68d4: ubfx            x1, x1, #0xc, #0x14
    // 0xab68d8: stp             x3, x0, [SP]
    // 0xab68dc: mov             x0, x1
    // 0xab68e0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab68e0: sub             lr, x0, #0xb7
    //     0xab68e4: ldr             lr, [x21, lr, lsl #3]
    //     0xab68e8: blr             lr
    // 0xab68ec: mov             x2, x0
    // 0xab68f0: ldur            x1, [fp, #-8]
    // 0xab68f4: stur            x2, [fp, #-0x20]
    // 0xab68f8: LoadField: r0 = r1->field_f
    //     0xab68f8: ldur            w0, [x1, #0xf]
    // 0xab68fc: DecompressPointer r0
    //     0xab68fc: add             x0, x0, HEAP, lsl #32
    // 0xab6900: LoadField: r1 = r0->field_b
    //     0xab6900: ldur            w1, [x0, #0xb]
    // 0xab6904: DecompressPointer r1
    //     0xab6904: add             x1, x1, HEAP, lsl #32
    // 0xab6908: cmp             w1, NULL
    // 0xab690c: b.eq            #0xab6e58
    // 0xab6910: LoadField: r0 = r1->field_f
    //     0xab6910: ldur            w0, [x1, #0xf]
    // 0xab6914: DecompressPointer r0
    //     0xab6914: add             x0, x0, HEAP, lsl #32
    // 0xab6918: cmp             w0, NULL
    // 0xab691c: b.ne            #0xab6928
    // 0xab6920: r0 = Null
    //     0xab6920: mov             x0, NULL
    // 0xab6924: b               #0xab6980
    // 0xab6928: LoadField: r3 = r0->field_13
    //     0xab6928: ldur            w3, [x0, #0x13]
    // 0xab692c: DecompressPointer r3
    //     0xab692c: add             x3, x3, HEAP, lsl #32
    // 0xab6930: cmp             w3, NULL
    // 0xab6934: b.ne            #0xab6940
    // 0xab6938: r0 = Null
    //     0xab6938: mov             x0, NULL
    // 0xab693c: b               #0xab6980
    // 0xab6940: ldr             x4, [fp, #0x10]
    // 0xab6944: LoadField: r0 = r3->field_b
    //     0xab6944: ldur            w0, [x3, #0xb]
    // 0xab6948: r5 = LoadInt32Instr(r4)
    //     0xab6948: sbfx            x5, x4, #1, #0x1f
    //     0xab694c: tbz             w4, #0, #0xab6954
    //     0xab6950: ldur            x5, [x4, #7]
    // 0xab6954: r1 = LoadInt32Instr(r0)
    //     0xab6954: sbfx            x1, x0, #1, #0x1f
    // 0xab6958: mov             x0, x1
    // 0xab695c: mov             x1, x5
    // 0xab6960: cmp             x1, x0
    // 0xab6964: b.hs            #0xab6e5c
    // 0xab6968: LoadField: r0 = r3->field_f
    //     0xab6968: ldur            w0, [x3, #0xf]
    // 0xab696c: DecompressPointer r0
    //     0xab696c: add             x0, x0, HEAP, lsl #32
    // 0xab6970: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab6970: add             x16, x0, x5, lsl #2
    //     0xab6974: ldur            w1, [x16, #0xf]
    // 0xab6978: DecompressPointer r1
    //     0xab6978: add             x1, x1, HEAP, lsl #32
    // 0xab697c: mov             x0, x1
    // 0xab6980: stur            x0, [fp, #-0x18]
    // 0xab6984: r0 = BagMultiSelect()
    //     0xab6984: bl              #0xab6ea4  ; AllocateBagMultiSelectStub -> BagMultiSelect (size=0x14)
    // 0xab6988: mov             x1, x0
    // 0xab698c: ldur            x0, [fp, #-0x20]
    // 0xab6990: stur            x1, [fp, #-0x28]
    // 0xab6994: StoreField: r1->field_b = r0
    //     0xab6994: stur            w0, [x1, #0xb]
    // 0xab6998: ldur            x0, [fp, #-0x18]
    // 0xab699c: StoreField: r1->field_f = r0
    //     0xab699c: stur            w0, [x1, #0xf]
    // 0xab69a0: r0 = Padding()
    //     0xab69a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab69a4: r3 = Instance_EdgeInsets
    //     0xab69a4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab69a8: ldr             x3, [x3, #0x668]
    // 0xab69ac: StoreField: r0->field_f = r3
    //     0xab69ac: stur            w3, [x0, #0xf]
    // 0xab69b0: ldur            x1, [fp, #-0x28]
    // 0xab69b4: StoreField: r0->field_b = r1
    //     0xab69b4: stur            w1, [x0, #0xb]
    // 0xab69b8: mov             x1, x0
    // 0xab69bc: b               #0xab6e34
    // 0xab69c0: mov             x1, x4
    // 0xab69c4: mov             x4, x3
    // 0xab69c8: r3 = Instance_EdgeInsets
    //     0xab69c8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab69cc: ldr             x3, [x3, #0x668]
    // 0xab69d0: r16 = Instance_CustomisationType
    //     0xab69d0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xab69d4: ldr             x16, [x16, #0x670]
    // 0xab69d8: cmp             w0, w16
    // 0xab69dc: b.ne            #0xab6ae8
    // 0xab69e0: LoadField: r0 = r2->field_b
    //     0xab69e0: ldur            w0, [x2, #0xb]
    // 0xab69e4: DecompressPointer r0
    //     0xab69e4: add             x0, x0, HEAP, lsl #32
    // 0xab69e8: cmp             w0, NULL
    // 0xab69ec: b.ne            #0xab69f8
    // 0xab69f0: r2 = Null
    //     0xab69f0: mov             x2, NULL
    // 0xab69f4: b               #0xab6a1c
    // 0xab69f8: r2 = LoadClassIdInstr(r0)
    //     0xab69f8: ldur            x2, [x0, #-1]
    //     0xab69fc: ubfx            x2, x2, #0xc, #0x14
    // 0xab6a00: stp             x4, x0, [SP]
    // 0xab6a04: mov             x0, x2
    // 0xab6a08: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab6a08: sub             lr, x0, #0xb7
    //     0xab6a0c: ldr             lr, [x21, lr, lsl #3]
    //     0xab6a10: blr             lr
    // 0xab6a14: mov             x2, x0
    // 0xab6a18: ldur            x1, [fp, #-8]
    // 0xab6a1c: stur            x2, [fp, #-0x20]
    // 0xab6a20: LoadField: r0 = r1->field_f
    //     0xab6a20: ldur            w0, [x1, #0xf]
    // 0xab6a24: DecompressPointer r0
    //     0xab6a24: add             x0, x0, HEAP, lsl #32
    // 0xab6a28: LoadField: r1 = r0->field_b
    //     0xab6a28: ldur            w1, [x0, #0xb]
    // 0xab6a2c: DecompressPointer r1
    //     0xab6a2c: add             x1, x1, HEAP, lsl #32
    // 0xab6a30: cmp             w1, NULL
    // 0xab6a34: b.eq            #0xab6e60
    // 0xab6a38: LoadField: r0 = r1->field_f
    //     0xab6a38: ldur            w0, [x1, #0xf]
    // 0xab6a3c: DecompressPointer r0
    //     0xab6a3c: add             x0, x0, HEAP, lsl #32
    // 0xab6a40: cmp             w0, NULL
    // 0xab6a44: b.ne            #0xab6a50
    // 0xab6a48: r0 = Null
    //     0xab6a48: mov             x0, NULL
    // 0xab6a4c: b               #0xab6aa8
    // 0xab6a50: LoadField: r3 = r0->field_13
    //     0xab6a50: ldur            w3, [x0, #0x13]
    // 0xab6a54: DecompressPointer r3
    //     0xab6a54: add             x3, x3, HEAP, lsl #32
    // 0xab6a58: cmp             w3, NULL
    // 0xab6a5c: b.ne            #0xab6a68
    // 0xab6a60: r0 = Null
    //     0xab6a60: mov             x0, NULL
    // 0xab6a64: b               #0xab6aa8
    // 0xab6a68: ldr             x4, [fp, #0x10]
    // 0xab6a6c: LoadField: r0 = r3->field_b
    //     0xab6a6c: ldur            w0, [x3, #0xb]
    // 0xab6a70: r5 = LoadInt32Instr(r4)
    //     0xab6a70: sbfx            x5, x4, #1, #0x1f
    //     0xab6a74: tbz             w4, #0, #0xab6a7c
    //     0xab6a78: ldur            x5, [x4, #7]
    // 0xab6a7c: r1 = LoadInt32Instr(r0)
    //     0xab6a7c: sbfx            x1, x0, #1, #0x1f
    // 0xab6a80: mov             x0, x1
    // 0xab6a84: mov             x1, x5
    // 0xab6a88: cmp             x1, x0
    // 0xab6a8c: b.hs            #0xab6e64
    // 0xab6a90: LoadField: r0 = r3->field_f
    //     0xab6a90: ldur            w0, [x3, #0xf]
    // 0xab6a94: DecompressPointer r0
    //     0xab6a94: add             x0, x0, HEAP, lsl #32
    // 0xab6a98: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab6a98: add             x16, x0, x5, lsl #2
    //     0xab6a9c: ldur            w1, [x16, #0xf]
    // 0xab6aa0: DecompressPointer r1
    //     0xab6aa0: add             x1, x1, HEAP, lsl #32
    // 0xab6aa4: mov             x0, x1
    // 0xab6aa8: stur            x0, [fp, #-0x18]
    // 0xab6aac: r0 = BagSingleSelect()
    //     0xab6aac: bl              #0xab6e98  ; AllocateBagSingleSelectStub -> BagSingleSelect (size=0x14)
    // 0xab6ab0: mov             x1, x0
    // 0xab6ab4: ldur            x0, [fp, #-0x20]
    // 0xab6ab8: stur            x1, [fp, #-0x28]
    // 0xab6abc: StoreField: r1->field_b = r0
    //     0xab6abc: stur            w0, [x1, #0xb]
    // 0xab6ac0: ldur            x0, [fp, #-0x18]
    // 0xab6ac4: StoreField: r1->field_f = r0
    //     0xab6ac4: stur            w0, [x1, #0xf]
    // 0xab6ac8: r0 = Padding()
    //     0xab6ac8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab6acc: r3 = Instance_EdgeInsets
    //     0xab6acc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab6ad0: ldr             x3, [x3, #0x668]
    // 0xab6ad4: StoreField: r0->field_f = r3
    //     0xab6ad4: stur            w3, [x0, #0xf]
    // 0xab6ad8: ldur            x1, [fp, #-0x28]
    // 0xab6adc: StoreField: r0->field_b = r1
    //     0xab6adc: stur            w1, [x0, #0xb]
    // 0xab6ae0: mov             x1, x0
    // 0xab6ae4: b               #0xab6e34
    // 0xab6ae8: r16 = Instance_CustomisationType
    //     0xab6ae8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xab6aec: ldr             x16, [x16, #0x650]
    // 0xab6af0: cmp             w0, w16
    // 0xab6af4: b.ne            #0xab6c00
    // 0xab6af8: LoadField: r0 = r2->field_b
    //     0xab6af8: ldur            w0, [x2, #0xb]
    // 0xab6afc: DecompressPointer r0
    //     0xab6afc: add             x0, x0, HEAP, lsl #32
    // 0xab6b00: cmp             w0, NULL
    // 0xab6b04: b.ne            #0xab6b10
    // 0xab6b08: r2 = Null
    //     0xab6b08: mov             x2, NULL
    // 0xab6b0c: b               #0xab6b34
    // 0xab6b10: r2 = LoadClassIdInstr(r0)
    //     0xab6b10: ldur            x2, [x0, #-1]
    //     0xab6b14: ubfx            x2, x2, #0xc, #0x14
    // 0xab6b18: stp             x4, x0, [SP]
    // 0xab6b1c: mov             x0, x2
    // 0xab6b20: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab6b20: sub             lr, x0, #0xb7
    //     0xab6b24: ldr             lr, [x21, lr, lsl #3]
    //     0xab6b28: blr             lr
    // 0xab6b2c: mov             x2, x0
    // 0xab6b30: ldur            x1, [fp, #-8]
    // 0xab6b34: stur            x2, [fp, #-0x20]
    // 0xab6b38: LoadField: r0 = r1->field_f
    //     0xab6b38: ldur            w0, [x1, #0xf]
    // 0xab6b3c: DecompressPointer r0
    //     0xab6b3c: add             x0, x0, HEAP, lsl #32
    // 0xab6b40: LoadField: r1 = r0->field_b
    //     0xab6b40: ldur            w1, [x0, #0xb]
    // 0xab6b44: DecompressPointer r1
    //     0xab6b44: add             x1, x1, HEAP, lsl #32
    // 0xab6b48: cmp             w1, NULL
    // 0xab6b4c: b.eq            #0xab6e68
    // 0xab6b50: LoadField: r0 = r1->field_f
    //     0xab6b50: ldur            w0, [x1, #0xf]
    // 0xab6b54: DecompressPointer r0
    //     0xab6b54: add             x0, x0, HEAP, lsl #32
    // 0xab6b58: cmp             w0, NULL
    // 0xab6b5c: b.ne            #0xab6b68
    // 0xab6b60: r0 = Null
    //     0xab6b60: mov             x0, NULL
    // 0xab6b64: b               #0xab6bc0
    // 0xab6b68: LoadField: r3 = r0->field_13
    //     0xab6b68: ldur            w3, [x0, #0x13]
    // 0xab6b6c: DecompressPointer r3
    //     0xab6b6c: add             x3, x3, HEAP, lsl #32
    // 0xab6b70: cmp             w3, NULL
    // 0xab6b74: b.ne            #0xab6b80
    // 0xab6b78: r0 = Null
    //     0xab6b78: mov             x0, NULL
    // 0xab6b7c: b               #0xab6bc0
    // 0xab6b80: ldr             x4, [fp, #0x10]
    // 0xab6b84: LoadField: r0 = r3->field_b
    //     0xab6b84: ldur            w0, [x3, #0xb]
    // 0xab6b88: r5 = LoadInt32Instr(r4)
    //     0xab6b88: sbfx            x5, x4, #1, #0x1f
    //     0xab6b8c: tbz             w4, #0, #0xab6b94
    //     0xab6b90: ldur            x5, [x4, #7]
    // 0xab6b94: r1 = LoadInt32Instr(r0)
    //     0xab6b94: sbfx            x1, x0, #1, #0x1f
    // 0xab6b98: mov             x0, x1
    // 0xab6b9c: mov             x1, x5
    // 0xab6ba0: cmp             x1, x0
    // 0xab6ba4: b.hs            #0xab6e6c
    // 0xab6ba8: LoadField: r0 = r3->field_f
    //     0xab6ba8: ldur            w0, [x3, #0xf]
    // 0xab6bac: DecompressPointer r0
    //     0xab6bac: add             x0, x0, HEAP, lsl #32
    // 0xab6bb0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab6bb0: add             x16, x0, x5, lsl #2
    //     0xab6bb4: ldur            w1, [x16, #0xf]
    // 0xab6bb8: DecompressPointer r1
    //     0xab6bb8: add             x1, x1, HEAP, lsl #32
    // 0xab6bbc: mov             x0, x1
    // 0xab6bc0: stur            x0, [fp, #-0x18]
    // 0xab6bc4: r0 = BagImages()
    //     0xab6bc4: bl              #0xab6e8c  ; AllocateBagImagesStub -> BagImages (size=0x14)
    // 0xab6bc8: mov             x1, x0
    // 0xab6bcc: ldur            x0, [fp, #-0x20]
    // 0xab6bd0: stur            x1, [fp, #-0x28]
    // 0xab6bd4: StoreField: r1->field_b = r0
    //     0xab6bd4: stur            w0, [x1, #0xb]
    // 0xab6bd8: ldur            x0, [fp, #-0x18]
    // 0xab6bdc: StoreField: r1->field_f = r0
    //     0xab6bdc: stur            w0, [x1, #0xf]
    // 0xab6be0: r0 = Padding()
    //     0xab6be0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab6be4: r3 = Instance_EdgeInsets
    //     0xab6be4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab6be8: ldr             x3, [x3, #0x668]
    // 0xab6bec: StoreField: r0->field_f = r3
    //     0xab6bec: stur            w3, [x0, #0xf]
    // 0xab6bf0: ldur            x1, [fp, #-0x28]
    // 0xab6bf4: StoreField: r0->field_b = r1
    //     0xab6bf4: stur            w1, [x0, #0xb]
    // 0xab6bf8: mov             x1, x0
    // 0xab6bfc: b               #0xab6e34
    // 0xab6c00: r16 = Instance_CustomisationType
    //     0xab6c00: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xab6c04: ldr             x16, [x16, #0x680]
    // 0xab6c08: cmp             w0, w16
    // 0xab6c0c: b.ne            #0xab6d18
    // 0xab6c10: LoadField: r0 = r2->field_b
    //     0xab6c10: ldur            w0, [x2, #0xb]
    // 0xab6c14: DecompressPointer r0
    //     0xab6c14: add             x0, x0, HEAP, lsl #32
    // 0xab6c18: cmp             w0, NULL
    // 0xab6c1c: b.ne            #0xab6c28
    // 0xab6c20: r2 = Null
    //     0xab6c20: mov             x2, NULL
    // 0xab6c24: b               #0xab6c4c
    // 0xab6c28: r2 = LoadClassIdInstr(r0)
    //     0xab6c28: ldur            x2, [x0, #-1]
    //     0xab6c2c: ubfx            x2, x2, #0xc, #0x14
    // 0xab6c30: stp             x4, x0, [SP]
    // 0xab6c34: mov             x0, x2
    // 0xab6c38: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab6c38: sub             lr, x0, #0xb7
    //     0xab6c3c: ldr             lr, [x21, lr, lsl #3]
    //     0xab6c40: blr             lr
    // 0xab6c44: mov             x2, x0
    // 0xab6c48: ldur            x1, [fp, #-8]
    // 0xab6c4c: stur            x2, [fp, #-0x20]
    // 0xab6c50: LoadField: r0 = r1->field_f
    //     0xab6c50: ldur            w0, [x1, #0xf]
    // 0xab6c54: DecompressPointer r0
    //     0xab6c54: add             x0, x0, HEAP, lsl #32
    // 0xab6c58: LoadField: r1 = r0->field_b
    //     0xab6c58: ldur            w1, [x0, #0xb]
    // 0xab6c5c: DecompressPointer r1
    //     0xab6c5c: add             x1, x1, HEAP, lsl #32
    // 0xab6c60: cmp             w1, NULL
    // 0xab6c64: b.eq            #0xab6e70
    // 0xab6c68: LoadField: r0 = r1->field_f
    //     0xab6c68: ldur            w0, [x1, #0xf]
    // 0xab6c6c: DecompressPointer r0
    //     0xab6c6c: add             x0, x0, HEAP, lsl #32
    // 0xab6c70: cmp             w0, NULL
    // 0xab6c74: b.ne            #0xab6c80
    // 0xab6c78: r0 = Null
    //     0xab6c78: mov             x0, NULL
    // 0xab6c7c: b               #0xab6cd8
    // 0xab6c80: LoadField: r3 = r0->field_13
    //     0xab6c80: ldur            w3, [x0, #0x13]
    // 0xab6c84: DecompressPointer r3
    //     0xab6c84: add             x3, x3, HEAP, lsl #32
    // 0xab6c88: cmp             w3, NULL
    // 0xab6c8c: b.ne            #0xab6c98
    // 0xab6c90: r0 = Null
    //     0xab6c90: mov             x0, NULL
    // 0xab6c94: b               #0xab6cd8
    // 0xab6c98: ldr             x4, [fp, #0x10]
    // 0xab6c9c: LoadField: r0 = r3->field_b
    //     0xab6c9c: ldur            w0, [x3, #0xb]
    // 0xab6ca0: r5 = LoadInt32Instr(r4)
    //     0xab6ca0: sbfx            x5, x4, #1, #0x1f
    //     0xab6ca4: tbz             w4, #0, #0xab6cac
    //     0xab6ca8: ldur            x5, [x4, #7]
    // 0xab6cac: r1 = LoadInt32Instr(r0)
    //     0xab6cac: sbfx            x1, x0, #1, #0x1f
    // 0xab6cb0: mov             x0, x1
    // 0xab6cb4: mov             x1, x5
    // 0xab6cb8: cmp             x1, x0
    // 0xab6cbc: b.hs            #0xab6e74
    // 0xab6cc0: LoadField: r0 = r3->field_f
    //     0xab6cc0: ldur            w0, [x3, #0xf]
    // 0xab6cc4: DecompressPointer r0
    //     0xab6cc4: add             x0, x0, HEAP, lsl #32
    // 0xab6cc8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab6cc8: add             x16, x0, x5, lsl #2
    //     0xab6ccc: ldur            w1, [x16, #0xf]
    // 0xab6cd0: DecompressPointer r1
    //     0xab6cd0: add             x1, x1, HEAP, lsl #32
    // 0xab6cd4: mov             x0, x1
    // 0xab6cd8: stur            x0, [fp, #-0x18]
    // 0xab6cdc: r0 = BagText()
    //     0xab6cdc: bl              #0xab6e80  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xab6ce0: mov             x1, x0
    // 0xab6ce4: ldur            x0, [fp, #-0x20]
    // 0xab6ce8: stur            x1, [fp, #-0x28]
    // 0xab6cec: StoreField: r1->field_b = r0
    //     0xab6cec: stur            w0, [x1, #0xb]
    // 0xab6cf0: ldur            x0, [fp, #-0x18]
    // 0xab6cf4: StoreField: r1->field_f = r0
    //     0xab6cf4: stur            w0, [x1, #0xf]
    // 0xab6cf8: r0 = Padding()
    //     0xab6cf8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab6cfc: r3 = Instance_EdgeInsets
    //     0xab6cfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab6d00: ldr             x3, [x3, #0x668]
    // 0xab6d04: StoreField: r0->field_f = r3
    //     0xab6d04: stur            w3, [x0, #0xf]
    // 0xab6d08: ldur            x1, [fp, #-0x28]
    // 0xab6d0c: StoreField: r0->field_b = r1
    //     0xab6d0c: stur            w1, [x0, #0xb]
    // 0xab6d10: mov             x1, x0
    // 0xab6d14: b               #0xab6e34
    // 0xab6d18: r16 = Instance_CustomisationType
    //     0xab6d18: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xab6d1c: ldr             x16, [x16, #0x690]
    // 0xab6d20: cmp             w0, w16
    // 0xab6d24: b.ne            #0xab6e30
    // 0xab6d28: LoadField: r0 = r2->field_b
    //     0xab6d28: ldur            w0, [x2, #0xb]
    // 0xab6d2c: DecompressPointer r0
    //     0xab6d2c: add             x0, x0, HEAP, lsl #32
    // 0xab6d30: cmp             w0, NULL
    // 0xab6d34: b.ne            #0xab6d44
    // 0xab6d38: mov             x0, x1
    // 0xab6d3c: r2 = Null
    //     0xab6d3c: mov             x2, NULL
    // 0xab6d40: b               #0xab6d68
    // 0xab6d44: r2 = LoadClassIdInstr(r0)
    //     0xab6d44: ldur            x2, [x0, #-1]
    //     0xab6d48: ubfx            x2, x2, #0xc, #0x14
    // 0xab6d4c: stp             x4, x0, [SP]
    // 0xab6d50: mov             x0, x2
    // 0xab6d54: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab6d54: sub             lr, x0, #0xb7
    //     0xab6d58: ldr             lr, [x21, lr, lsl #3]
    //     0xab6d5c: blr             lr
    // 0xab6d60: mov             x2, x0
    // 0xab6d64: ldur            x0, [fp, #-8]
    // 0xab6d68: stur            x2, [fp, #-0x18]
    // 0xab6d6c: LoadField: r1 = r0->field_f
    //     0xab6d6c: ldur            w1, [x0, #0xf]
    // 0xab6d70: DecompressPointer r1
    //     0xab6d70: add             x1, x1, HEAP, lsl #32
    // 0xab6d74: LoadField: r0 = r1->field_b
    //     0xab6d74: ldur            w0, [x1, #0xb]
    // 0xab6d78: DecompressPointer r0
    //     0xab6d78: add             x0, x0, HEAP, lsl #32
    // 0xab6d7c: cmp             w0, NULL
    // 0xab6d80: b.eq            #0xab6e78
    // 0xab6d84: LoadField: r1 = r0->field_f
    //     0xab6d84: ldur            w1, [x0, #0xf]
    // 0xab6d88: DecompressPointer r1
    //     0xab6d88: add             x1, x1, HEAP, lsl #32
    // 0xab6d8c: cmp             w1, NULL
    // 0xab6d90: b.ne            #0xab6d9c
    // 0xab6d94: r0 = Null
    //     0xab6d94: mov             x0, NULL
    // 0xab6d98: b               #0xab6df0
    // 0xab6d9c: LoadField: r3 = r1->field_13
    //     0xab6d9c: ldur            w3, [x1, #0x13]
    // 0xab6da0: DecompressPointer r3
    //     0xab6da0: add             x3, x3, HEAP, lsl #32
    // 0xab6da4: cmp             w3, NULL
    // 0xab6da8: b.ne            #0xab6db4
    // 0xab6dac: r0 = Null
    //     0xab6dac: mov             x0, NULL
    // 0xab6db0: b               #0xab6df0
    // 0xab6db4: ldr             x0, [fp, #0x10]
    // 0xab6db8: LoadField: r1 = r3->field_b
    //     0xab6db8: ldur            w1, [x3, #0xb]
    // 0xab6dbc: r4 = LoadInt32Instr(r0)
    //     0xab6dbc: sbfx            x4, x0, #1, #0x1f
    //     0xab6dc0: tbz             w0, #0, #0xab6dc8
    //     0xab6dc4: ldur            x4, [x0, #7]
    // 0xab6dc8: r0 = LoadInt32Instr(r1)
    //     0xab6dc8: sbfx            x0, x1, #1, #0x1f
    // 0xab6dcc: mov             x1, x4
    // 0xab6dd0: cmp             x1, x0
    // 0xab6dd4: b.hs            #0xab6e7c
    // 0xab6dd8: LoadField: r0 = r3->field_f
    //     0xab6dd8: ldur            w0, [x3, #0xf]
    // 0xab6ddc: DecompressPointer r0
    //     0xab6ddc: add             x0, x0, HEAP, lsl #32
    // 0xab6de0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xab6de0: add             x16, x0, x4, lsl #2
    //     0xab6de4: ldur            w1, [x16, #0xf]
    // 0xab6de8: DecompressPointer r1
    //     0xab6de8: add             x1, x1, HEAP, lsl #32
    // 0xab6dec: mov             x0, x1
    // 0xab6df0: stur            x0, [fp, #-8]
    // 0xab6df4: r0 = BagText()
    //     0xab6df4: bl              #0xab6e80  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xab6df8: mov             x1, x0
    // 0xab6dfc: ldur            x0, [fp, #-0x18]
    // 0xab6e00: stur            x1, [fp, #-0x20]
    // 0xab6e04: StoreField: r1->field_b = r0
    //     0xab6e04: stur            w0, [x1, #0xb]
    // 0xab6e08: ldur            x0, [fp, #-8]
    // 0xab6e0c: StoreField: r1->field_f = r0
    //     0xab6e0c: stur            w0, [x1, #0xf]
    // 0xab6e10: r0 = Padding()
    //     0xab6e10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab6e14: r1 = Instance_EdgeInsets
    //     0xab6e14: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab6e18: ldr             x1, [x1, #0x668]
    // 0xab6e1c: StoreField: r0->field_f = r1
    //     0xab6e1c: stur            w1, [x0, #0xf]
    // 0xab6e20: ldur            x1, [fp, #-0x20]
    // 0xab6e24: StoreField: r0->field_b = r1
    //     0xab6e24: stur            w1, [x0, #0xb]
    // 0xab6e28: mov             x1, x0
    // 0xab6e2c: b               #0xab6e34
    // 0xab6e30: ldur            x1, [fp, #-0x10]
    // 0xab6e34: mov             x0, x1
    // 0xab6e38: LeaveFrame
    //     0xab6e38: mov             SP, fp
    //     0xab6e3c: ldp             fp, lr, [SP], #0x10
    // 0xab6e40: ret
    //     0xab6e40: ret             
    // 0xab6e44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab6e44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab6e48: b               #0xab653c
    // 0xab6e4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6e4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6e54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab6e54: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab6e58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6e58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6e5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab6e5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab6e60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6e60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6e64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab6e64: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab6e68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6e68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6e6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab6e6c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab6e70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6e70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6e74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab6e74: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab6e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab6e78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab6e7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab6e7c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xab6eb0, size: 0x9ec
    // 0xab6eb0: EnterFrame
    //     0xab6eb0: stp             fp, lr, [SP, #-0x10]!
    //     0xab6eb4: mov             fp, SP
    // 0xab6eb8: AllocStack(0x40)
    //     0xab6eb8: sub             SP, SP, #0x40
    // 0xab6ebc: SetupParameters()
    //     0xab6ebc: ldr             x0, [fp, #0x20]
    //     0xab6ec0: ldur            w1, [x0, #0x17]
    //     0xab6ec4: add             x1, x1, HEAP, lsl #32
    //     0xab6ec8: stur            x1, [fp, #-8]
    // 0xab6ecc: CheckStackOverflow
    //     0xab6ecc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xab6ed0: cmp             SP, x16
    //     0xab6ed4: b.ls            #0xab784c
    // 0xab6ed8: r0 = Container()
    //     0xab6ed8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xab6edc: mov             x1, x0
    // 0xab6ee0: stur            x0, [fp, #-0x10]
    // 0xab6ee4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xab6ee4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xab6ee8: r0 = Container()
    //     0xab6ee8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xab6eec: ldur            x1, [fp, #-8]
    // 0xab6ef0: LoadField: r0 = r1->field_f
    //     0xab6ef0: ldur            w0, [x1, #0xf]
    // 0xab6ef4: DecompressPointer r0
    //     0xab6ef4: add             x0, x0, HEAP, lsl #32
    // 0xab6ef8: LoadField: r2 = r0->field_b
    //     0xab6ef8: ldur            w2, [x0, #0xb]
    // 0xab6efc: DecompressPointer r2
    //     0xab6efc: add             x2, x2, HEAP, lsl #32
    // 0xab6f00: cmp             w2, NULL
    // 0xab6f04: b.eq            #0xab7854
    // 0xab6f08: LoadField: r0 = r2->field_b
    //     0xab6f08: ldur            w0, [x2, #0xb]
    // 0xab6f0c: DecompressPointer r0
    //     0xab6f0c: add             x0, x0, HEAP, lsl #32
    // 0xab6f10: cmp             w0, NULL
    // 0xab6f14: b.ne            #0xab6f20
    // 0xab6f18: r0 = Null
    //     0xab6f18: mov             x0, NULL
    // 0xab6f1c: b               #0xab6f40
    // 0xab6f20: r2 = LoadClassIdInstr(r0)
    //     0xab6f20: ldur            x2, [x0, #-1]
    //     0xab6f24: ubfx            x2, x2, #0xc, #0x14
    // 0xab6f28: str             x0, [SP]
    // 0xab6f2c: mov             x0, x2
    // 0xab6f30: r0 = GDT[cid_x0 + 0xc898]()
    //     0xab6f30: movz            x17, #0xc898
    //     0xab6f34: add             lr, x0, x17
    //     0xab6f38: ldr             lr, [x21, lr, lsl #3]
    //     0xab6f3c: blr             lr
    // 0xab6f40: ldr             x1, [fp, #0x10]
    // 0xab6f44: cmp             w0, w1
    // 0xab6f48: b.ne            #0xab71d4
    // 0xab6f4c: ldur            x0, [fp, #-8]
    // 0xab6f50: ldr             x1, [fp, #0x18]
    // 0xab6f54: r0 = of()
    //     0xab6f54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab6f58: LoadField: r1 = r0->field_87
    //     0xab6f58: ldur            w1, [x0, #0x87]
    // 0xab6f5c: DecompressPointer r1
    //     0xab6f5c: add             x1, x1, HEAP, lsl #32
    // 0xab6f60: LoadField: r0 = r1->field_7
    //     0xab6f60: ldur            w0, [x1, #7]
    // 0xab6f64: DecompressPointer r0
    //     0xab6f64: add             x0, x0, HEAP, lsl #32
    // 0xab6f68: r16 = Instance_Color
    //     0xab6f68: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab6f6c: r30 = 14.000000
    //     0xab6f6c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab6f70: ldr             lr, [lr, #0x1d8]
    // 0xab6f74: stp             lr, x16, [SP]
    // 0xab6f78: mov             x1, x0
    // 0xab6f7c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab6f7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab6f80: ldr             x4, [x4, #0x9b8]
    // 0xab6f84: r0 = copyWith()
    //     0xab6f84: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab6f88: stur            x0, [fp, #-0x18]
    // 0xab6f8c: r0 = Text()
    //     0xab6f8c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab6f90: mov             x1, x0
    // 0xab6f94: r0 = "Total"
    //     0xab6f94: add             x0, PP, #0x54, lsl #12  ; [pp+0x54bc8] "Total"
    //     0xab6f98: ldr             x0, [x0, #0xbc8]
    // 0xab6f9c: stur            x1, [fp, #-0x20]
    // 0xab6fa0: StoreField: r1->field_b = r0
    //     0xab6fa0: stur            w0, [x1, #0xb]
    // 0xab6fa4: ldur            x0, [fp, #-0x18]
    // 0xab6fa8: StoreField: r1->field_13 = r0
    //     0xab6fa8: stur            w0, [x1, #0x13]
    // 0xab6fac: ldur            x2, [fp, #-8]
    // 0xab6fb0: LoadField: r0 = r2->field_f
    //     0xab6fb0: ldur            w0, [x2, #0xf]
    // 0xab6fb4: DecompressPointer r0
    //     0xab6fb4: add             x0, x0, HEAP, lsl #32
    // 0xab6fb8: LoadField: r2 = r0->field_b
    //     0xab6fb8: ldur            w2, [x0, #0xb]
    // 0xab6fbc: DecompressPointer r2
    //     0xab6fbc: add             x2, x2, HEAP, lsl #32
    // 0xab6fc0: cmp             w2, NULL
    // 0xab6fc4: b.eq            #0xab7858
    // 0xab6fc8: LoadField: r0 = r2->field_13
    //     0xab6fc8: ldur            w0, [x2, #0x13]
    // 0xab6fcc: DecompressPointer r0
    //     0xab6fcc: add             x0, x0, HEAP, lsl #32
    // 0xab6fd0: r2 = LoadClassIdInstr(r0)
    //     0xab6fd0: ldur            x2, [x0, #-1]
    //     0xab6fd4: ubfx            x2, x2, #0xc, #0x14
    // 0xab6fd8: str             x0, [SP]
    // 0xab6fdc: mov             x0, x2
    // 0xab6fe0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xab6fe0: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xab6fe4: r0 = GDT[cid_x0 + 0x2700]()
    //     0xab6fe4: movz            x17, #0x2700
    //     0xab6fe8: add             lr, x0, x17
    //     0xab6fec: ldr             lr, [x21, lr, lsl #3]
    //     0xab6ff0: blr             lr
    // 0xab6ff4: ldr             x1, [fp, #0x18]
    // 0xab6ff8: stur            x0, [fp, #-0x18]
    // 0xab6ffc: r0 = of()
    //     0xab6ffc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xab7000: LoadField: r1 = r0->field_87
    //     0xab7000: ldur            w1, [x0, #0x87]
    // 0xab7004: DecompressPointer r1
    //     0xab7004: add             x1, x1, HEAP, lsl #32
    // 0xab7008: LoadField: r0 = r1->field_7
    //     0xab7008: ldur            w0, [x1, #7]
    // 0xab700c: DecompressPointer r0
    //     0xab700c: add             x0, x0, HEAP, lsl #32
    // 0xab7010: r16 = Instance_Color
    //     0xab7010: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xab7014: r30 = 14.000000
    //     0xab7014: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xab7018: ldr             lr, [lr, #0x1d8]
    // 0xab701c: stp             lr, x16, [SP]
    // 0xab7020: mov             x1, x0
    // 0xab7024: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xab7024: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xab7028: ldr             x4, [x4, #0x9b8]
    // 0xab702c: r0 = copyWith()
    //     0xab702c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xab7030: stur            x0, [fp, #-0x28]
    // 0xab7034: r0 = Text()
    //     0xab7034: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xab7038: mov             x3, x0
    // 0xab703c: ldur            x0, [fp, #-0x18]
    // 0xab7040: stur            x3, [fp, #-0x30]
    // 0xab7044: StoreField: r3->field_b = r0
    //     0xab7044: stur            w0, [x3, #0xb]
    // 0xab7048: ldur            x0, [fp, #-0x28]
    // 0xab704c: StoreField: r3->field_13 = r0
    //     0xab704c: stur            w0, [x3, #0x13]
    // 0xab7050: r1 = Null
    //     0xab7050: mov             x1, NULL
    // 0xab7054: r2 = 6
    //     0xab7054: movz            x2, #0x6
    // 0xab7058: r0 = AllocateArray()
    //     0xab7058: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab705c: mov             x2, x0
    // 0xab7060: ldur            x0, [fp, #-0x20]
    // 0xab7064: stur            x2, [fp, #-0x18]
    // 0xab7068: StoreField: r2->field_f = r0
    //     0xab7068: stur            w0, [x2, #0xf]
    // 0xab706c: r16 = Instance_Spacer
    //     0xab706c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xab7070: ldr             x16, [x16, #0xf0]
    // 0xab7074: StoreField: r2->field_13 = r16
    //     0xab7074: stur            w16, [x2, #0x13]
    // 0xab7078: ldur            x0, [fp, #-0x30]
    // 0xab707c: ArrayStore: r2[0] = r0  ; List_4
    //     0xab707c: stur            w0, [x2, #0x17]
    // 0xab7080: r1 = <Widget>
    //     0xab7080: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab7084: r0 = AllocateGrowableArray()
    //     0xab7084: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab7088: mov             x1, x0
    // 0xab708c: ldur            x0, [fp, #-0x18]
    // 0xab7090: stur            x1, [fp, #-0x20]
    // 0xab7094: StoreField: r1->field_f = r0
    //     0xab7094: stur            w0, [x1, #0xf]
    // 0xab7098: r0 = 6
    //     0xab7098: movz            x0, #0x6
    // 0xab709c: StoreField: r1->field_b = r0
    //     0xab709c: stur            w0, [x1, #0xb]
    // 0xab70a0: r0 = Row()
    //     0xab70a0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xab70a4: mov             x3, x0
    // 0xab70a8: r0 = Instance_Axis
    //     0xab70a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xab70ac: stur            x3, [fp, #-0x18]
    // 0xab70b0: StoreField: r3->field_f = r0
    //     0xab70b0: stur            w0, [x3, #0xf]
    // 0xab70b4: r0 = Instance_MainAxisAlignment
    //     0xab70b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab70b8: ldr             x0, [x0, #0xa08]
    // 0xab70bc: StoreField: r3->field_13 = r0
    //     0xab70bc: stur            w0, [x3, #0x13]
    // 0xab70c0: r4 = Instance_MainAxisSize
    //     0xab70c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab70c4: ldr             x4, [x4, #0xa10]
    // 0xab70c8: ArrayStore: r3[0] = r4  ; List_4
    //     0xab70c8: stur            w4, [x3, #0x17]
    // 0xab70cc: r5 = Instance_CrossAxisAlignment
    //     0xab70cc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab70d0: ldr             x5, [x5, #0xa18]
    // 0xab70d4: StoreField: r3->field_1b = r5
    //     0xab70d4: stur            w5, [x3, #0x1b]
    // 0xab70d8: r6 = Instance_VerticalDirection
    //     0xab70d8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab70dc: ldr             x6, [x6, #0xa20]
    // 0xab70e0: StoreField: r3->field_23 = r6
    //     0xab70e0: stur            w6, [x3, #0x23]
    // 0xab70e4: r7 = Instance_Clip
    //     0xab70e4: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab70e8: ldr             x7, [x7, #0x38]
    // 0xab70ec: StoreField: r3->field_2b = r7
    //     0xab70ec: stur            w7, [x3, #0x2b]
    // 0xab70f0: StoreField: r3->field_2f = rZR
    //     0xab70f0: stur            xzr, [x3, #0x2f]
    // 0xab70f4: ldur            x1, [fp, #-0x20]
    // 0xab70f8: StoreField: r3->field_b = r1
    //     0xab70f8: stur            w1, [x3, #0xb]
    // 0xab70fc: r1 = Null
    //     0xab70fc: mov             x1, NULL
    // 0xab7100: r2 = 4
    //     0xab7100: movz            x2, #0x4
    // 0xab7104: r0 = AllocateArray()
    //     0xab7104: bl              #0x16f7198  ; AllocateArrayStub
    // 0xab7108: stur            x0, [fp, #-0x20]
    // 0xab710c: r16 = Instance_Divider
    //     0xab710c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57260] Obj!Divider@d66cd1
    //     0xab7110: ldr             x16, [x16, #0x260]
    // 0xab7114: StoreField: r0->field_f = r16
    //     0xab7114: stur            w16, [x0, #0xf]
    // 0xab7118: ldur            x1, [fp, #-0x18]
    // 0xab711c: StoreField: r0->field_13 = r1
    //     0xab711c: stur            w1, [x0, #0x13]
    // 0xab7120: r1 = <Widget>
    //     0xab7120: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xab7124: r0 = AllocateGrowableArray()
    //     0xab7124: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xab7128: mov             x1, x0
    // 0xab712c: ldur            x0, [fp, #-0x20]
    // 0xab7130: stur            x1, [fp, #-0x18]
    // 0xab7134: StoreField: r1->field_f = r0
    //     0xab7134: stur            w0, [x1, #0xf]
    // 0xab7138: r0 = 4
    //     0xab7138: movz            x0, #0x4
    // 0xab713c: StoreField: r1->field_b = r0
    //     0xab713c: stur            w0, [x1, #0xb]
    // 0xab7140: r0 = Column()
    //     0xab7140: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xab7144: mov             x1, x0
    // 0xab7148: r0 = Instance_Axis
    //     0xab7148: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xab714c: stur            x1, [fp, #-0x20]
    // 0xab7150: StoreField: r1->field_f = r0
    //     0xab7150: stur            w0, [x1, #0xf]
    // 0xab7154: r0 = Instance_MainAxisAlignment
    //     0xab7154: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xab7158: ldr             x0, [x0, #0xa08]
    // 0xab715c: StoreField: r1->field_13 = r0
    //     0xab715c: stur            w0, [x1, #0x13]
    // 0xab7160: r0 = Instance_MainAxisSize
    //     0xab7160: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xab7164: ldr             x0, [x0, #0xa10]
    // 0xab7168: ArrayStore: r1[0] = r0  ; List_4
    //     0xab7168: stur            w0, [x1, #0x17]
    // 0xab716c: r0 = Instance_CrossAxisAlignment
    //     0xab716c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xab7170: ldr             x0, [x0, #0xa18]
    // 0xab7174: StoreField: r1->field_1b = r0
    //     0xab7174: stur            w0, [x1, #0x1b]
    // 0xab7178: r0 = Instance_VerticalDirection
    //     0xab7178: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xab717c: ldr             x0, [x0, #0xa20]
    // 0xab7180: StoreField: r1->field_23 = r0
    //     0xab7180: stur            w0, [x1, #0x23]
    // 0xab7184: r0 = Instance_Clip
    //     0xab7184: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xab7188: ldr             x0, [x0, #0x38]
    // 0xab718c: StoreField: r1->field_2b = r0
    //     0xab718c: stur            w0, [x1, #0x2b]
    // 0xab7190: StoreField: r1->field_2f = rZR
    //     0xab7190: stur            xzr, [x1, #0x2f]
    // 0xab7194: ldur            x0, [fp, #-0x18]
    // 0xab7198: StoreField: r1->field_b = r0
    //     0xab7198: stur            w0, [x1, #0xb]
    // 0xab719c: r0 = Padding()
    //     0xab719c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab71a0: mov             x1, x0
    // 0xab71a4: r0 = Instance_EdgeInsets
    //     0xab71a4: add             x0, PP, #0x57, lsl #12  ; [pp+0x57268] Obj!EdgeInsets@d58ca1
    //     0xab71a8: ldr             x0, [x0, #0x268]
    // 0xab71ac: stur            x1, [fp, #-0x18]
    // 0xab71b0: StoreField: r1->field_f = r0
    //     0xab71b0: stur            w0, [x1, #0xf]
    // 0xab71b4: ldur            x0, [fp, #-0x20]
    // 0xab71b8: StoreField: r1->field_b = r0
    //     0xab71b8: stur            w0, [x1, #0xb]
    // 0xab71bc: r0 = IntrinsicHeight()
    //     0xab71bc: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xab71c0: mov             x1, x0
    // 0xab71c4: ldur            x0, [fp, #-0x18]
    // 0xab71c8: StoreField: r1->field_b = r0
    //     0xab71c8: stur            w0, [x1, #0xb]
    // 0xab71cc: mov             x0, x1
    // 0xab71d0: b               #0xab7840
    // 0xab71d4: ldur            x2, [fp, #-8]
    // 0xab71d8: LoadField: r0 = r2->field_f
    //     0xab71d8: ldur            w0, [x2, #0xf]
    // 0xab71dc: DecompressPointer r0
    //     0xab71dc: add             x0, x0, HEAP, lsl #32
    // 0xab71e0: LoadField: r3 = r0->field_b
    //     0xab71e0: ldur            w3, [x0, #0xb]
    // 0xab71e4: DecompressPointer r3
    //     0xab71e4: add             x3, x3, HEAP, lsl #32
    // 0xab71e8: cmp             w3, NULL
    // 0xab71ec: b.eq            #0xab785c
    // 0xab71f0: LoadField: r0 = r3->field_b
    //     0xab71f0: ldur            w0, [x3, #0xb]
    // 0xab71f4: DecompressPointer r0
    //     0xab71f4: add             x0, x0, HEAP, lsl #32
    // 0xab71f8: cmp             w0, NULL
    // 0xab71fc: b.ne            #0xab7208
    // 0xab7200: r0 = Null
    //     0xab7200: mov             x0, NULL
    // 0xab7204: b               #0xab7230
    // 0xab7208: r3 = LoadClassIdInstr(r0)
    //     0xab7208: ldur            x3, [x0, #-1]
    //     0xab720c: ubfx            x3, x3, #0xc, #0x14
    // 0xab7210: stp             x1, x0, [SP]
    // 0xab7214: mov             x0, x3
    // 0xab7218: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab7218: sub             lr, x0, #0xb7
    //     0xab721c: ldr             lr, [x21, lr, lsl #3]
    //     0xab7220: blr             lr
    // 0xab7224: LoadField: r1 = r0->field_f
    //     0xab7224: ldur            w1, [x0, #0xf]
    // 0xab7228: DecompressPointer r1
    //     0xab7228: add             x1, x1, HEAP, lsl #32
    // 0xab722c: mov             x0, x1
    // 0xab7230: r16 = Instance_CustomisationType
    //     0xab7230: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0xab7234: ldr             x16, [x16, #0x660]
    // 0xab7238: cmp             w0, w16
    // 0xab723c: b.ne            #0xab7368
    // 0xab7240: ldur            x1, [fp, #-8]
    // 0xab7244: LoadField: r0 = r1->field_f
    //     0xab7244: ldur            w0, [x1, #0xf]
    // 0xab7248: DecompressPointer r0
    //     0xab7248: add             x0, x0, HEAP, lsl #32
    // 0xab724c: LoadField: r2 = r0->field_b
    //     0xab724c: ldur            w2, [x0, #0xb]
    // 0xab7250: DecompressPointer r2
    //     0xab7250: add             x2, x2, HEAP, lsl #32
    // 0xab7254: cmp             w2, NULL
    // 0xab7258: b.eq            #0xab7860
    // 0xab725c: LoadField: r0 = r2->field_b
    //     0xab725c: ldur            w0, [x2, #0xb]
    // 0xab7260: DecompressPointer r0
    //     0xab7260: add             x0, x0, HEAP, lsl #32
    // 0xab7264: cmp             w0, NULL
    // 0xab7268: b.ne            #0xab7274
    // 0xab726c: r2 = Null
    //     0xab726c: mov             x2, NULL
    // 0xab7270: b               #0xab729c
    // 0xab7274: r2 = LoadClassIdInstr(r0)
    //     0xab7274: ldur            x2, [x0, #-1]
    //     0xab7278: ubfx            x2, x2, #0xc, #0x14
    // 0xab727c: ldr             x16, [fp, #0x10]
    // 0xab7280: stp             x16, x0, [SP]
    // 0xab7284: mov             x0, x2
    // 0xab7288: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab7288: sub             lr, x0, #0xb7
    //     0xab728c: ldr             lr, [x21, lr, lsl #3]
    //     0xab7290: blr             lr
    // 0xab7294: mov             x2, x0
    // 0xab7298: ldur            x1, [fp, #-8]
    // 0xab729c: stur            x2, [fp, #-0x20]
    // 0xab72a0: LoadField: r0 = r1->field_f
    //     0xab72a0: ldur            w0, [x1, #0xf]
    // 0xab72a4: DecompressPointer r0
    //     0xab72a4: add             x0, x0, HEAP, lsl #32
    // 0xab72a8: LoadField: r1 = r0->field_b
    //     0xab72a8: ldur            w1, [x0, #0xb]
    // 0xab72ac: DecompressPointer r1
    //     0xab72ac: add             x1, x1, HEAP, lsl #32
    // 0xab72b0: cmp             w1, NULL
    // 0xab72b4: b.eq            #0xab7864
    // 0xab72b8: LoadField: r0 = r1->field_f
    //     0xab72b8: ldur            w0, [x1, #0xf]
    // 0xab72bc: DecompressPointer r0
    //     0xab72bc: add             x0, x0, HEAP, lsl #32
    // 0xab72c0: cmp             w0, NULL
    // 0xab72c4: b.ne            #0xab72d0
    // 0xab72c8: r0 = Null
    //     0xab72c8: mov             x0, NULL
    // 0xab72cc: b               #0xab7328
    // 0xab72d0: LoadField: r3 = r0->field_13
    //     0xab72d0: ldur            w3, [x0, #0x13]
    // 0xab72d4: DecompressPointer r3
    //     0xab72d4: add             x3, x3, HEAP, lsl #32
    // 0xab72d8: cmp             w3, NULL
    // 0xab72dc: b.ne            #0xab72e8
    // 0xab72e0: r0 = Null
    //     0xab72e0: mov             x0, NULL
    // 0xab72e4: b               #0xab7328
    // 0xab72e8: ldr             x4, [fp, #0x10]
    // 0xab72ec: LoadField: r0 = r3->field_b
    //     0xab72ec: ldur            w0, [x3, #0xb]
    // 0xab72f0: r5 = LoadInt32Instr(r4)
    //     0xab72f0: sbfx            x5, x4, #1, #0x1f
    //     0xab72f4: tbz             w4, #0, #0xab72fc
    //     0xab72f8: ldur            x5, [x4, #7]
    // 0xab72fc: r1 = LoadInt32Instr(r0)
    //     0xab72fc: sbfx            x1, x0, #1, #0x1f
    // 0xab7300: mov             x0, x1
    // 0xab7304: mov             x1, x5
    // 0xab7308: cmp             x1, x0
    // 0xab730c: b.hs            #0xab7868
    // 0xab7310: LoadField: r0 = r3->field_f
    //     0xab7310: ldur            w0, [x3, #0xf]
    // 0xab7314: DecompressPointer r0
    //     0xab7314: add             x0, x0, HEAP, lsl #32
    // 0xab7318: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab7318: add             x16, x0, x5, lsl #2
    //     0xab731c: ldur            w1, [x16, #0xf]
    // 0xab7320: DecompressPointer r1
    //     0xab7320: add             x1, x1, HEAP, lsl #32
    // 0xab7324: mov             x0, x1
    // 0xab7328: stur            x0, [fp, #-0x18]
    // 0xab732c: r0 = BagMultiSelect()
    //     0xab732c: bl              #0xab6ea4  ; AllocateBagMultiSelectStub -> BagMultiSelect (size=0x14)
    // 0xab7330: mov             x1, x0
    // 0xab7334: ldur            x0, [fp, #-0x20]
    // 0xab7338: stur            x1, [fp, #-0x28]
    // 0xab733c: StoreField: r1->field_b = r0
    //     0xab733c: stur            w0, [x1, #0xb]
    // 0xab7340: ldur            x0, [fp, #-0x18]
    // 0xab7344: StoreField: r1->field_f = r0
    //     0xab7344: stur            w0, [x1, #0xf]
    // 0xab7348: r0 = Padding()
    //     0xab7348: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab734c: r2 = Instance_EdgeInsets
    //     0xab734c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab7350: ldr             x2, [x2, #0x668]
    // 0xab7354: StoreField: r0->field_f = r2
    //     0xab7354: stur            w2, [x0, #0xf]
    // 0xab7358: ldur            x1, [fp, #-0x28]
    // 0xab735c: StoreField: r0->field_b = r1
    //     0xab735c: stur            w1, [x0, #0xb]
    // 0xab7360: mov             x1, x0
    // 0xab7364: b               #0xab783c
    // 0xab7368: ldr             x4, [fp, #0x10]
    // 0xab736c: ldur            x1, [fp, #-8]
    // 0xab7370: r2 = Instance_EdgeInsets
    //     0xab7370: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab7374: ldr             x2, [x2, #0x668]
    // 0xab7378: r16 = Instance_CustomisationType
    //     0xab7378: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0xab737c: ldr             x16, [x16, #0x670]
    // 0xab7380: cmp             w0, w16
    // 0xab7384: b.ne            #0xab74a8
    // 0xab7388: LoadField: r0 = r1->field_f
    //     0xab7388: ldur            w0, [x1, #0xf]
    // 0xab738c: DecompressPointer r0
    //     0xab738c: add             x0, x0, HEAP, lsl #32
    // 0xab7390: LoadField: r3 = r0->field_b
    //     0xab7390: ldur            w3, [x0, #0xb]
    // 0xab7394: DecompressPointer r3
    //     0xab7394: add             x3, x3, HEAP, lsl #32
    // 0xab7398: cmp             w3, NULL
    // 0xab739c: b.eq            #0xab786c
    // 0xab73a0: LoadField: r0 = r3->field_b
    //     0xab73a0: ldur            w0, [x3, #0xb]
    // 0xab73a4: DecompressPointer r0
    //     0xab73a4: add             x0, x0, HEAP, lsl #32
    // 0xab73a8: cmp             w0, NULL
    // 0xab73ac: b.ne            #0xab73b8
    // 0xab73b0: r2 = Null
    //     0xab73b0: mov             x2, NULL
    // 0xab73b4: b               #0xab73dc
    // 0xab73b8: r3 = LoadClassIdInstr(r0)
    //     0xab73b8: ldur            x3, [x0, #-1]
    //     0xab73bc: ubfx            x3, x3, #0xc, #0x14
    // 0xab73c0: stp             x4, x0, [SP]
    // 0xab73c4: mov             x0, x3
    // 0xab73c8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab73c8: sub             lr, x0, #0xb7
    //     0xab73cc: ldr             lr, [x21, lr, lsl #3]
    //     0xab73d0: blr             lr
    // 0xab73d4: mov             x2, x0
    // 0xab73d8: ldur            x1, [fp, #-8]
    // 0xab73dc: stur            x2, [fp, #-0x20]
    // 0xab73e0: LoadField: r0 = r1->field_f
    //     0xab73e0: ldur            w0, [x1, #0xf]
    // 0xab73e4: DecompressPointer r0
    //     0xab73e4: add             x0, x0, HEAP, lsl #32
    // 0xab73e8: LoadField: r1 = r0->field_b
    //     0xab73e8: ldur            w1, [x0, #0xb]
    // 0xab73ec: DecompressPointer r1
    //     0xab73ec: add             x1, x1, HEAP, lsl #32
    // 0xab73f0: cmp             w1, NULL
    // 0xab73f4: b.eq            #0xab7870
    // 0xab73f8: LoadField: r0 = r1->field_f
    //     0xab73f8: ldur            w0, [x1, #0xf]
    // 0xab73fc: DecompressPointer r0
    //     0xab73fc: add             x0, x0, HEAP, lsl #32
    // 0xab7400: cmp             w0, NULL
    // 0xab7404: b.ne            #0xab7410
    // 0xab7408: r0 = Null
    //     0xab7408: mov             x0, NULL
    // 0xab740c: b               #0xab7468
    // 0xab7410: LoadField: r3 = r0->field_13
    //     0xab7410: ldur            w3, [x0, #0x13]
    // 0xab7414: DecompressPointer r3
    //     0xab7414: add             x3, x3, HEAP, lsl #32
    // 0xab7418: cmp             w3, NULL
    // 0xab741c: b.ne            #0xab7428
    // 0xab7420: r0 = Null
    //     0xab7420: mov             x0, NULL
    // 0xab7424: b               #0xab7468
    // 0xab7428: ldr             x4, [fp, #0x10]
    // 0xab742c: LoadField: r0 = r3->field_b
    //     0xab742c: ldur            w0, [x3, #0xb]
    // 0xab7430: r5 = LoadInt32Instr(r4)
    //     0xab7430: sbfx            x5, x4, #1, #0x1f
    //     0xab7434: tbz             w4, #0, #0xab743c
    //     0xab7438: ldur            x5, [x4, #7]
    // 0xab743c: r1 = LoadInt32Instr(r0)
    //     0xab743c: sbfx            x1, x0, #1, #0x1f
    // 0xab7440: mov             x0, x1
    // 0xab7444: mov             x1, x5
    // 0xab7448: cmp             x1, x0
    // 0xab744c: b.hs            #0xab7874
    // 0xab7450: LoadField: r0 = r3->field_f
    //     0xab7450: ldur            w0, [x3, #0xf]
    // 0xab7454: DecompressPointer r0
    //     0xab7454: add             x0, x0, HEAP, lsl #32
    // 0xab7458: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab7458: add             x16, x0, x5, lsl #2
    //     0xab745c: ldur            w1, [x16, #0xf]
    // 0xab7460: DecompressPointer r1
    //     0xab7460: add             x1, x1, HEAP, lsl #32
    // 0xab7464: mov             x0, x1
    // 0xab7468: stur            x0, [fp, #-0x18]
    // 0xab746c: r0 = BagSingleSelect()
    //     0xab746c: bl              #0xab6e98  ; AllocateBagSingleSelectStub -> BagSingleSelect (size=0x14)
    // 0xab7470: mov             x1, x0
    // 0xab7474: ldur            x0, [fp, #-0x20]
    // 0xab7478: stur            x1, [fp, #-0x28]
    // 0xab747c: StoreField: r1->field_b = r0
    //     0xab747c: stur            w0, [x1, #0xb]
    // 0xab7480: ldur            x0, [fp, #-0x18]
    // 0xab7484: StoreField: r1->field_f = r0
    //     0xab7484: stur            w0, [x1, #0xf]
    // 0xab7488: r0 = Padding()
    //     0xab7488: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab748c: r2 = Instance_EdgeInsets
    //     0xab748c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab7490: ldr             x2, [x2, #0x668]
    // 0xab7494: StoreField: r0->field_f = r2
    //     0xab7494: stur            w2, [x0, #0xf]
    // 0xab7498: ldur            x1, [fp, #-0x28]
    // 0xab749c: StoreField: r0->field_b = r1
    //     0xab749c: stur            w1, [x0, #0xb]
    // 0xab74a0: mov             x1, x0
    // 0xab74a4: b               #0xab783c
    // 0xab74a8: r16 = Instance_CustomisationType
    //     0xab74a8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0xab74ac: ldr             x16, [x16, #0x650]
    // 0xab74b0: cmp             w0, w16
    // 0xab74b4: b.ne            #0xab75d8
    // 0xab74b8: LoadField: r0 = r1->field_f
    //     0xab74b8: ldur            w0, [x1, #0xf]
    // 0xab74bc: DecompressPointer r0
    //     0xab74bc: add             x0, x0, HEAP, lsl #32
    // 0xab74c0: LoadField: r3 = r0->field_b
    //     0xab74c0: ldur            w3, [x0, #0xb]
    // 0xab74c4: DecompressPointer r3
    //     0xab74c4: add             x3, x3, HEAP, lsl #32
    // 0xab74c8: cmp             w3, NULL
    // 0xab74cc: b.eq            #0xab7878
    // 0xab74d0: LoadField: r0 = r3->field_b
    //     0xab74d0: ldur            w0, [x3, #0xb]
    // 0xab74d4: DecompressPointer r0
    //     0xab74d4: add             x0, x0, HEAP, lsl #32
    // 0xab74d8: cmp             w0, NULL
    // 0xab74dc: b.ne            #0xab74e8
    // 0xab74e0: r2 = Null
    //     0xab74e0: mov             x2, NULL
    // 0xab74e4: b               #0xab750c
    // 0xab74e8: r3 = LoadClassIdInstr(r0)
    //     0xab74e8: ldur            x3, [x0, #-1]
    //     0xab74ec: ubfx            x3, x3, #0xc, #0x14
    // 0xab74f0: stp             x4, x0, [SP]
    // 0xab74f4: mov             x0, x3
    // 0xab74f8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab74f8: sub             lr, x0, #0xb7
    //     0xab74fc: ldr             lr, [x21, lr, lsl #3]
    //     0xab7500: blr             lr
    // 0xab7504: mov             x2, x0
    // 0xab7508: ldur            x1, [fp, #-8]
    // 0xab750c: stur            x2, [fp, #-0x20]
    // 0xab7510: LoadField: r0 = r1->field_f
    //     0xab7510: ldur            w0, [x1, #0xf]
    // 0xab7514: DecompressPointer r0
    //     0xab7514: add             x0, x0, HEAP, lsl #32
    // 0xab7518: LoadField: r1 = r0->field_b
    //     0xab7518: ldur            w1, [x0, #0xb]
    // 0xab751c: DecompressPointer r1
    //     0xab751c: add             x1, x1, HEAP, lsl #32
    // 0xab7520: cmp             w1, NULL
    // 0xab7524: b.eq            #0xab787c
    // 0xab7528: LoadField: r0 = r1->field_f
    //     0xab7528: ldur            w0, [x1, #0xf]
    // 0xab752c: DecompressPointer r0
    //     0xab752c: add             x0, x0, HEAP, lsl #32
    // 0xab7530: cmp             w0, NULL
    // 0xab7534: b.ne            #0xab7540
    // 0xab7538: r0 = Null
    //     0xab7538: mov             x0, NULL
    // 0xab753c: b               #0xab7598
    // 0xab7540: LoadField: r3 = r0->field_13
    //     0xab7540: ldur            w3, [x0, #0x13]
    // 0xab7544: DecompressPointer r3
    //     0xab7544: add             x3, x3, HEAP, lsl #32
    // 0xab7548: cmp             w3, NULL
    // 0xab754c: b.ne            #0xab7558
    // 0xab7550: r0 = Null
    //     0xab7550: mov             x0, NULL
    // 0xab7554: b               #0xab7598
    // 0xab7558: ldr             x4, [fp, #0x10]
    // 0xab755c: LoadField: r0 = r3->field_b
    //     0xab755c: ldur            w0, [x3, #0xb]
    // 0xab7560: r5 = LoadInt32Instr(r4)
    //     0xab7560: sbfx            x5, x4, #1, #0x1f
    //     0xab7564: tbz             w4, #0, #0xab756c
    //     0xab7568: ldur            x5, [x4, #7]
    // 0xab756c: r1 = LoadInt32Instr(r0)
    //     0xab756c: sbfx            x1, x0, #1, #0x1f
    // 0xab7570: mov             x0, x1
    // 0xab7574: mov             x1, x5
    // 0xab7578: cmp             x1, x0
    // 0xab757c: b.hs            #0xab7880
    // 0xab7580: LoadField: r0 = r3->field_f
    //     0xab7580: ldur            w0, [x3, #0xf]
    // 0xab7584: DecompressPointer r0
    //     0xab7584: add             x0, x0, HEAP, lsl #32
    // 0xab7588: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab7588: add             x16, x0, x5, lsl #2
    //     0xab758c: ldur            w1, [x16, #0xf]
    // 0xab7590: DecompressPointer r1
    //     0xab7590: add             x1, x1, HEAP, lsl #32
    // 0xab7594: mov             x0, x1
    // 0xab7598: stur            x0, [fp, #-0x18]
    // 0xab759c: r0 = BagImages()
    //     0xab759c: bl              #0xab6e8c  ; AllocateBagImagesStub -> BagImages (size=0x14)
    // 0xab75a0: mov             x1, x0
    // 0xab75a4: ldur            x0, [fp, #-0x20]
    // 0xab75a8: stur            x1, [fp, #-0x28]
    // 0xab75ac: StoreField: r1->field_b = r0
    //     0xab75ac: stur            w0, [x1, #0xb]
    // 0xab75b0: ldur            x0, [fp, #-0x18]
    // 0xab75b4: StoreField: r1->field_f = r0
    //     0xab75b4: stur            w0, [x1, #0xf]
    // 0xab75b8: r0 = Padding()
    //     0xab75b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab75bc: r2 = Instance_EdgeInsets
    //     0xab75bc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab75c0: ldr             x2, [x2, #0x668]
    // 0xab75c4: StoreField: r0->field_f = r2
    //     0xab75c4: stur            w2, [x0, #0xf]
    // 0xab75c8: ldur            x1, [fp, #-0x28]
    // 0xab75cc: StoreField: r0->field_b = r1
    //     0xab75cc: stur            w1, [x0, #0xb]
    // 0xab75d0: mov             x1, x0
    // 0xab75d4: b               #0xab783c
    // 0xab75d8: r16 = Instance_CustomisationType
    //     0xab75d8: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0xab75dc: ldr             x16, [x16, #0x680]
    // 0xab75e0: cmp             w0, w16
    // 0xab75e4: b.ne            #0xab7708
    // 0xab75e8: LoadField: r0 = r1->field_f
    //     0xab75e8: ldur            w0, [x1, #0xf]
    // 0xab75ec: DecompressPointer r0
    //     0xab75ec: add             x0, x0, HEAP, lsl #32
    // 0xab75f0: LoadField: r3 = r0->field_b
    //     0xab75f0: ldur            w3, [x0, #0xb]
    // 0xab75f4: DecompressPointer r3
    //     0xab75f4: add             x3, x3, HEAP, lsl #32
    // 0xab75f8: cmp             w3, NULL
    // 0xab75fc: b.eq            #0xab7884
    // 0xab7600: LoadField: r0 = r3->field_b
    //     0xab7600: ldur            w0, [x3, #0xb]
    // 0xab7604: DecompressPointer r0
    //     0xab7604: add             x0, x0, HEAP, lsl #32
    // 0xab7608: cmp             w0, NULL
    // 0xab760c: b.ne            #0xab7618
    // 0xab7610: r2 = Null
    //     0xab7610: mov             x2, NULL
    // 0xab7614: b               #0xab763c
    // 0xab7618: r3 = LoadClassIdInstr(r0)
    //     0xab7618: ldur            x3, [x0, #-1]
    //     0xab761c: ubfx            x3, x3, #0xc, #0x14
    // 0xab7620: stp             x4, x0, [SP]
    // 0xab7624: mov             x0, x3
    // 0xab7628: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab7628: sub             lr, x0, #0xb7
    //     0xab762c: ldr             lr, [x21, lr, lsl #3]
    //     0xab7630: blr             lr
    // 0xab7634: mov             x2, x0
    // 0xab7638: ldur            x1, [fp, #-8]
    // 0xab763c: stur            x2, [fp, #-0x20]
    // 0xab7640: LoadField: r0 = r1->field_f
    //     0xab7640: ldur            w0, [x1, #0xf]
    // 0xab7644: DecompressPointer r0
    //     0xab7644: add             x0, x0, HEAP, lsl #32
    // 0xab7648: LoadField: r1 = r0->field_b
    //     0xab7648: ldur            w1, [x0, #0xb]
    // 0xab764c: DecompressPointer r1
    //     0xab764c: add             x1, x1, HEAP, lsl #32
    // 0xab7650: cmp             w1, NULL
    // 0xab7654: b.eq            #0xab7888
    // 0xab7658: LoadField: r0 = r1->field_f
    //     0xab7658: ldur            w0, [x1, #0xf]
    // 0xab765c: DecompressPointer r0
    //     0xab765c: add             x0, x0, HEAP, lsl #32
    // 0xab7660: cmp             w0, NULL
    // 0xab7664: b.ne            #0xab7670
    // 0xab7668: r0 = Null
    //     0xab7668: mov             x0, NULL
    // 0xab766c: b               #0xab76c8
    // 0xab7670: LoadField: r3 = r0->field_13
    //     0xab7670: ldur            w3, [x0, #0x13]
    // 0xab7674: DecompressPointer r3
    //     0xab7674: add             x3, x3, HEAP, lsl #32
    // 0xab7678: cmp             w3, NULL
    // 0xab767c: b.ne            #0xab7688
    // 0xab7680: r0 = Null
    //     0xab7680: mov             x0, NULL
    // 0xab7684: b               #0xab76c8
    // 0xab7688: ldr             x4, [fp, #0x10]
    // 0xab768c: LoadField: r0 = r3->field_b
    //     0xab768c: ldur            w0, [x3, #0xb]
    // 0xab7690: r5 = LoadInt32Instr(r4)
    //     0xab7690: sbfx            x5, x4, #1, #0x1f
    //     0xab7694: tbz             w4, #0, #0xab769c
    //     0xab7698: ldur            x5, [x4, #7]
    // 0xab769c: r1 = LoadInt32Instr(r0)
    //     0xab769c: sbfx            x1, x0, #1, #0x1f
    // 0xab76a0: mov             x0, x1
    // 0xab76a4: mov             x1, x5
    // 0xab76a8: cmp             x1, x0
    // 0xab76ac: b.hs            #0xab788c
    // 0xab76b0: LoadField: r0 = r3->field_f
    //     0xab76b0: ldur            w0, [x3, #0xf]
    // 0xab76b4: DecompressPointer r0
    //     0xab76b4: add             x0, x0, HEAP, lsl #32
    // 0xab76b8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xab76b8: add             x16, x0, x5, lsl #2
    //     0xab76bc: ldur            w1, [x16, #0xf]
    // 0xab76c0: DecompressPointer r1
    //     0xab76c0: add             x1, x1, HEAP, lsl #32
    // 0xab76c4: mov             x0, x1
    // 0xab76c8: stur            x0, [fp, #-0x18]
    // 0xab76cc: r0 = BagText()
    //     0xab76cc: bl              #0xab6e80  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xab76d0: mov             x1, x0
    // 0xab76d4: ldur            x0, [fp, #-0x20]
    // 0xab76d8: stur            x1, [fp, #-0x28]
    // 0xab76dc: StoreField: r1->field_b = r0
    //     0xab76dc: stur            w0, [x1, #0xb]
    // 0xab76e0: ldur            x0, [fp, #-0x18]
    // 0xab76e4: StoreField: r1->field_f = r0
    //     0xab76e4: stur            w0, [x1, #0xf]
    // 0xab76e8: r0 = Padding()
    //     0xab76e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab76ec: r2 = Instance_EdgeInsets
    //     0xab76ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab76f0: ldr             x2, [x2, #0x668]
    // 0xab76f4: StoreField: r0->field_f = r2
    //     0xab76f4: stur            w2, [x0, #0xf]
    // 0xab76f8: ldur            x1, [fp, #-0x28]
    // 0xab76fc: StoreField: r0->field_b = r1
    //     0xab76fc: stur            w1, [x0, #0xb]
    // 0xab7700: mov             x1, x0
    // 0xab7704: b               #0xab783c
    // 0xab7708: r16 = Instance_CustomisationType
    //     0xab7708: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0xab770c: ldr             x16, [x16, #0x690]
    // 0xab7710: cmp             w0, w16
    // 0xab7714: b.ne            #0xab7838
    // 0xab7718: LoadField: r0 = r1->field_f
    //     0xab7718: ldur            w0, [x1, #0xf]
    // 0xab771c: DecompressPointer r0
    //     0xab771c: add             x0, x0, HEAP, lsl #32
    // 0xab7720: LoadField: r3 = r0->field_b
    //     0xab7720: ldur            w3, [x0, #0xb]
    // 0xab7724: DecompressPointer r3
    //     0xab7724: add             x3, x3, HEAP, lsl #32
    // 0xab7728: cmp             w3, NULL
    // 0xab772c: b.eq            #0xab7890
    // 0xab7730: LoadField: r0 = r3->field_b
    //     0xab7730: ldur            w0, [x3, #0xb]
    // 0xab7734: DecompressPointer r0
    //     0xab7734: add             x0, x0, HEAP, lsl #32
    // 0xab7738: cmp             w0, NULL
    // 0xab773c: b.ne            #0xab774c
    // 0xab7740: mov             x0, x1
    // 0xab7744: r2 = Null
    //     0xab7744: mov             x2, NULL
    // 0xab7748: b               #0xab7770
    // 0xab774c: r3 = LoadClassIdInstr(r0)
    //     0xab774c: ldur            x3, [x0, #-1]
    //     0xab7750: ubfx            x3, x3, #0xc, #0x14
    // 0xab7754: stp             x4, x0, [SP]
    // 0xab7758: mov             x0, x3
    // 0xab775c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xab775c: sub             lr, x0, #0xb7
    //     0xab7760: ldr             lr, [x21, lr, lsl #3]
    //     0xab7764: blr             lr
    // 0xab7768: mov             x2, x0
    // 0xab776c: ldur            x0, [fp, #-8]
    // 0xab7770: stur            x2, [fp, #-0x18]
    // 0xab7774: LoadField: r1 = r0->field_f
    //     0xab7774: ldur            w1, [x0, #0xf]
    // 0xab7778: DecompressPointer r1
    //     0xab7778: add             x1, x1, HEAP, lsl #32
    // 0xab777c: LoadField: r0 = r1->field_b
    //     0xab777c: ldur            w0, [x1, #0xb]
    // 0xab7780: DecompressPointer r0
    //     0xab7780: add             x0, x0, HEAP, lsl #32
    // 0xab7784: cmp             w0, NULL
    // 0xab7788: b.eq            #0xab7894
    // 0xab778c: LoadField: r1 = r0->field_f
    //     0xab778c: ldur            w1, [x0, #0xf]
    // 0xab7790: DecompressPointer r1
    //     0xab7790: add             x1, x1, HEAP, lsl #32
    // 0xab7794: cmp             w1, NULL
    // 0xab7798: b.ne            #0xab77a4
    // 0xab779c: r0 = Null
    //     0xab779c: mov             x0, NULL
    // 0xab77a0: b               #0xab77f8
    // 0xab77a4: LoadField: r3 = r1->field_13
    //     0xab77a4: ldur            w3, [x1, #0x13]
    // 0xab77a8: DecompressPointer r3
    //     0xab77a8: add             x3, x3, HEAP, lsl #32
    // 0xab77ac: cmp             w3, NULL
    // 0xab77b0: b.ne            #0xab77bc
    // 0xab77b4: r0 = Null
    //     0xab77b4: mov             x0, NULL
    // 0xab77b8: b               #0xab77f8
    // 0xab77bc: ldr             x0, [fp, #0x10]
    // 0xab77c0: LoadField: r1 = r3->field_b
    //     0xab77c0: ldur            w1, [x3, #0xb]
    // 0xab77c4: r4 = LoadInt32Instr(r0)
    //     0xab77c4: sbfx            x4, x0, #1, #0x1f
    //     0xab77c8: tbz             w0, #0, #0xab77d0
    //     0xab77cc: ldur            x4, [x0, #7]
    // 0xab77d0: r0 = LoadInt32Instr(r1)
    //     0xab77d0: sbfx            x0, x1, #1, #0x1f
    // 0xab77d4: mov             x1, x4
    // 0xab77d8: cmp             x1, x0
    // 0xab77dc: b.hs            #0xab7898
    // 0xab77e0: LoadField: r0 = r3->field_f
    //     0xab77e0: ldur            w0, [x3, #0xf]
    // 0xab77e4: DecompressPointer r0
    //     0xab77e4: add             x0, x0, HEAP, lsl #32
    // 0xab77e8: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xab77e8: add             x16, x0, x4, lsl #2
    //     0xab77ec: ldur            w1, [x16, #0xf]
    // 0xab77f0: DecompressPointer r1
    //     0xab77f0: add             x1, x1, HEAP, lsl #32
    // 0xab77f4: mov             x0, x1
    // 0xab77f8: stur            x0, [fp, #-8]
    // 0xab77fc: r0 = BagText()
    //     0xab77fc: bl              #0xab6e80  ; AllocateBagTextStub -> BagText (size=0x14)
    // 0xab7800: mov             x1, x0
    // 0xab7804: ldur            x0, [fp, #-0x18]
    // 0xab7808: stur            x1, [fp, #-0x20]
    // 0xab780c: StoreField: r1->field_b = r0
    //     0xab780c: stur            w0, [x1, #0xb]
    // 0xab7810: ldur            x0, [fp, #-8]
    // 0xab7814: StoreField: r1->field_f = r0
    //     0xab7814: stur            w0, [x1, #0xf]
    // 0xab7818: r0 = Padding()
    //     0xab7818: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xab781c: r1 = Instance_EdgeInsets
    //     0xab781c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xab7820: ldr             x1, [x1, #0x668]
    // 0xab7824: StoreField: r0->field_f = r1
    //     0xab7824: stur            w1, [x0, #0xf]
    // 0xab7828: ldur            x1, [fp, #-0x20]
    // 0xab782c: StoreField: r0->field_b = r1
    //     0xab782c: stur            w1, [x0, #0xb]
    // 0xab7830: mov             x1, x0
    // 0xab7834: b               #0xab783c
    // 0xab7838: ldur            x1, [fp, #-0x10]
    // 0xab783c: mov             x0, x1
    // 0xab7840: LeaveFrame
    //     0xab7840: mov             SP, fp
    //     0xab7844: ldp             fp, lr, [SP], #0x10
    // 0xab7848: ret
    //     0xab7848: ret             
    // 0xab784c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xab784c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xab7850: b               #0xab6ed8
    // 0xab7854: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7854: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7858: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7858: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab785c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab785c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7860: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7860: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7864: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7864: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7868: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab7868: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab786c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab786c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7870: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7874: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab7874: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab7878: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7878: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab787c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab787c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7880: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab7880: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab7884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7884: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7888: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7888: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab788c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab788c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xab7890: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7890: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xab7894: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xab7898: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xab7898: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4200, size: 0x18, field offset: 0xc
//   const constructor, 
class CustomisedStrip extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7cf98, size: 0x24
    // 0xc7cf98: EnterFrame
    //     0xc7cf98: stp             fp, lr, [SP, #-0x10]!
    //     0xc7cf9c: mov             fp, SP
    // 0xc7cfa0: mov             x0, x1
    // 0xc7cfa4: r1 = <CustomisedStrip>
    //     0xc7cfa4: add             x1, PP, #0x49, lsl #12  ; [pp+0x49168] TypeArguments: <CustomisedStrip>
    //     0xc7cfa8: ldr             x1, [x1, #0x168]
    // 0xc7cfac: r0 = _CustomisedStripState()
    //     0xc7cfac: bl              #0xc7cfbc  ; Allocate_CustomisedStripStateStub -> _CustomisedStripState (size=0x14)
    // 0xc7cfb0: LeaveFrame
    //     0xc7cfb0: mov             SP, fp
    //     0xc7cfb4: ldp             fp, lr, [SP], #0x10
    // 0xc7cfb8: ret
    //     0xc7cfb8: ret             
  }
}
