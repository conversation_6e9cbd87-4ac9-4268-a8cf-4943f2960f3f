// lib: , url: package:customer_app/app/presentation/views/glass/orders/order_detail_accordion.dart

// class id: 1049414, size: 0x8
class :: {
}

// class id: 3325, size: 0x18, field offset: 0x14
class _OrderDetailAccordionState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb78130, size: 0x418
    // 0xb78130: EnterFrame
    //     0xb78130: stp             fp, lr, [SP, #-0x10]!
    //     0xb78134: mov             fp, SP
    // 0xb78138: AllocStack(0x48)
    //     0xb78138: sub             SP, SP, #0x48
    // 0xb7813c: SetupParameters(_OrderDetailAccordionState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb7813c: mov             x0, x1
    //     0xb78140: stur            x1, [fp, #-8]
    //     0xb78144: mov             x1, x2
    //     0xb78148: stur            x2, [fp, #-0x10]
    // 0xb7814c: CheckStackOverflow
    //     0xb7814c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb78150: cmp             SP, x16
    //     0xb78154: b.ls            #0xb78538
    // 0xb78158: r1 = 1
    //     0xb78158: movz            x1, #0x1
    // 0xb7815c: r0 = AllocateContext()
    //     0xb7815c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb78160: mov             x2, x0
    // 0xb78164: ldur            x0, [fp, #-8]
    // 0xb78168: stur            x2, [fp, #-0x18]
    // 0xb7816c: StoreField: r2->field_f = r0
    //     0xb7816c: stur            w0, [x2, #0xf]
    // 0xb78170: ldur            x1, [fp, #-0x10]
    // 0xb78174: r0 = of()
    //     0xb78174: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb78178: LoadField: r1 = r0->field_5b
    //     0xb78178: ldur            w1, [x0, #0x5b]
    // 0xb7817c: DecompressPointer r1
    //     0xb7817c: add             x1, x1, HEAP, lsl #32
    // 0xb78180: r0 = LoadClassIdInstr(r1)
    //     0xb78180: ldur            x0, [x1, #-1]
    //     0xb78184: ubfx            x0, x0, #0xc, #0x14
    // 0xb78188: d0 = 0.030000
    //     0xb78188: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb7818c: ldr             d0, [x17, #0x238]
    // 0xb78190: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb78190: sub             lr, x0, #0xffa
    //     0xb78194: ldr             lr, [x21, lr, lsl #3]
    //     0xb78198: blr             lr
    // 0xb7819c: stur            x0, [fp, #-0x20]
    // 0xb781a0: r0 = Radius()
    //     0xb781a0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb781a4: d0 = 10.000000
    //     0xb781a4: fmov            d0, #10.00000000
    // 0xb781a8: stur            x0, [fp, #-0x28]
    // 0xb781ac: StoreField: r0->field_7 = d0
    //     0xb781ac: stur            d0, [x0, #7]
    // 0xb781b0: StoreField: r0->field_f = d0
    //     0xb781b0: stur            d0, [x0, #0xf]
    // 0xb781b4: r0 = BorderRadius()
    //     0xb781b4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb781b8: mov             x1, x0
    // 0xb781bc: ldur            x0, [fp, #-0x28]
    // 0xb781c0: stur            x1, [fp, #-0x30]
    // 0xb781c4: StoreField: r1->field_7 = r0
    //     0xb781c4: stur            w0, [x1, #7]
    // 0xb781c8: StoreField: r1->field_b = r0
    //     0xb781c8: stur            w0, [x1, #0xb]
    // 0xb781cc: StoreField: r1->field_f = r0
    //     0xb781cc: stur            w0, [x1, #0xf]
    // 0xb781d0: StoreField: r1->field_13 = r0
    //     0xb781d0: stur            w0, [x1, #0x13]
    // 0xb781d4: r0 = RoundedRectangleBorder()
    //     0xb781d4: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb781d8: mov             x2, x0
    // 0xb781dc: ldur            x0, [fp, #-0x30]
    // 0xb781e0: stur            x2, [fp, #-0x38]
    // 0xb781e4: StoreField: r2->field_b = r0
    //     0xb781e4: stur            w0, [x2, #0xb]
    // 0xb781e8: r0 = Instance_BorderSide
    //     0xb781e8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb781ec: ldr             x0, [x0, #0xe20]
    // 0xb781f0: StoreField: r2->field_7 = r0
    //     0xb781f0: stur            w0, [x2, #7]
    // 0xb781f4: ldur            x0, [fp, #-8]
    // 0xb781f8: LoadField: r1 = r0->field_b
    //     0xb781f8: ldur            w1, [x0, #0xb]
    // 0xb781fc: DecompressPointer r1
    //     0xb781fc: add             x1, x1, HEAP, lsl #32
    // 0xb78200: cmp             w1, NULL
    // 0xb78204: b.eq            #0xb78540
    // 0xb78208: LoadField: r3 = r1->field_b
    //     0xb78208: ldur            w3, [x1, #0xb]
    // 0xb7820c: DecompressPointer r3
    //     0xb7820c: add             x3, x3, HEAP, lsl #32
    // 0xb78210: stur            x3, [fp, #-0x30]
    // 0xb78214: LoadField: r1 = r0->field_13
    //     0xb78214: ldur            w1, [x0, #0x13]
    // 0xb78218: DecompressPointer r1
    //     0xb78218: add             x1, x1, HEAP, lsl #32
    // 0xb7821c: tbnz            w1, #4, #0xb7822c
    // 0xb78220: r4 = Instance_IconData
    //     0xb78220: add             x4, PP, #0x53, lsl #12  ; [pp+0x53440] Obj!IconData@d55621
    //     0xb78224: ldr             x4, [x4, #0x440]
    // 0xb78228: b               #0xb78234
    // 0xb7822c: r4 = Instance_IconData
    //     0xb7822c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53448] Obj!IconData@d553a1
    //     0xb78230: ldr             x4, [x4, #0x448]
    // 0xb78234: stur            x4, [fp, #-0x28]
    // 0xb78238: tbnz            w1, #4, #0xb78254
    // 0xb7823c: ldur            x1, [fp, #-0x10]
    // 0xb78240: r0 = of()
    //     0xb78240: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb78244: LoadField: r1 = r0->field_5b
    //     0xb78244: ldur            w1, [x0, #0x5b]
    // 0xb78248: DecompressPointer r1
    //     0xb78248: add             x1, x1, HEAP, lsl #32
    // 0xb7824c: mov             x3, x1
    // 0xb78250: b               #0xb78268
    // 0xb78254: ldur            x1, [fp, #-0x10]
    // 0xb78258: r0 = of()
    //     0xb78258: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7825c: LoadField: r1 = r0->field_5b
    //     0xb7825c: ldur            w1, [x0, #0x5b]
    // 0xb78260: DecompressPointer r1
    //     0xb78260: add             x1, x1, HEAP, lsl #32
    // 0xb78264: mov             x3, x1
    // 0xb78268: ldur            x0, [fp, #-8]
    // 0xb7826c: ldur            x1, [fp, #-0x30]
    // 0xb78270: ldur            x2, [fp, #-0x28]
    // 0xb78274: stur            x3, [fp, #-0x10]
    // 0xb78278: r0 = Icon()
    //     0xb78278: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xb7827c: mov             x1, x0
    // 0xb78280: ldur            x0, [fp, #-0x28]
    // 0xb78284: stur            x1, [fp, #-0x40]
    // 0xb78288: StoreField: r1->field_b = r0
    //     0xb78288: stur            w0, [x1, #0xb]
    // 0xb7828c: r0 = 32.000000
    //     0xb7828c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb78290: ldr             x0, [x0, #0x848]
    // 0xb78294: StoreField: r1->field_f = r0
    //     0xb78294: stur            w0, [x1, #0xf]
    // 0xb78298: ldur            x0, [fp, #-0x10]
    // 0xb7829c: StoreField: r1->field_23 = r0
    //     0xb7829c: stur            w0, [x1, #0x23]
    // 0xb782a0: ldur            x0, [fp, #-8]
    // 0xb782a4: LoadField: r2 = r0->field_b
    //     0xb782a4: ldur            w2, [x0, #0xb]
    // 0xb782a8: DecompressPointer r2
    //     0xb782a8: add             x2, x2, HEAP, lsl #32
    // 0xb782ac: stur            x2, [fp, #-0x10]
    // 0xb782b0: cmp             w2, NULL
    // 0xb782b4: b.eq            #0xb78544
    // 0xb782b8: r0 = RichTextIcon()
    //     0xb782b8: bl              #0xb78568  ; AllocateRichTextIconStub -> RichTextIcon (size=0x14)
    // 0xb782bc: mov             x1, x0
    // 0xb782c0: ldur            x0, [fp, #-0x40]
    // 0xb782c4: stur            x1, [fp, #-8]
    // 0xb782c8: StoreField: r1->field_b = r0
    //     0xb782c8: stur            w0, [x1, #0xb]
    // 0xb782cc: r0 = ""
    //     0xb782cc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb782d0: StoreField: r1->field_f = r0
    //     0xb782d0: stur            w0, [x1, #0xf]
    // 0xb782d4: r0 = ListTile()
    //     0xb782d4: bl              #0x98bcd8  ; AllocateListTileStub -> ListTile (size=0xa0)
    // 0xb782d8: mov             x3, x0
    // 0xb782dc: ldur            x0, [fp, #-0x30]
    // 0xb782e0: stur            x3, [fp, #-0x28]
    // 0xb782e4: StoreField: r3->field_f = r0
    //     0xb782e4: stur            w0, [x3, #0xf]
    // 0xb782e8: ldur            x0, [fp, #-8]
    // 0xb782ec: ArrayStore: r3[0] = r0  ; List_4
    //     0xb782ec: stur            w0, [x3, #0x17]
    // 0xb782f0: r0 = Instance_EdgeInsets
    //     0xb782f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb782f4: ldr             x0, [x0, #0x668]
    // 0xb782f8: StoreField: r3->field_47 = r0
    //     0xb782f8: stur            w0, [x3, #0x47]
    // 0xb782fc: r0 = true
    //     0xb782fc: add             x0, NULL, #0x20  ; true
    // 0xb78300: StoreField: r3->field_4b = r0
    //     0xb78300: stur            w0, [x3, #0x4b]
    // 0xb78304: ldur            x2, [fp, #-0x18]
    // 0xb78308: r1 = Function '<anonymous closure>':.
    //     0xb78308: add             x1, PP, #0x55, lsl #12  ; [pp+0x55aa0] AnonymousClosure: (0xb78574), in [package:customer_app/app/presentation/views/glass/orders/order_detail_accordion.dart] _OrderDetailAccordionState::build (0xb78130)
    //     0xb7830c: ldr             x1, [x1, #0xaa0]
    // 0xb78310: r0 = AllocateClosure()
    //     0xb78310: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb78314: mov             x1, x0
    // 0xb78318: ldur            x0, [fp, #-0x28]
    // 0xb7831c: StoreField: r0->field_4f = r1
    //     0xb7831c: stur            w1, [x0, #0x4f]
    // 0xb78320: r1 = false
    //     0xb78320: add             x1, NULL, #0x30  ; false
    // 0xb78324: StoreField: r0->field_5f = r1
    //     0xb78324: stur            w1, [x0, #0x5f]
    // 0xb78328: StoreField: r0->field_73 = r1
    //     0xb78328: stur            w1, [x0, #0x73]
    // 0xb7832c: r3 = true
    //     0xb7832c: add             x3, NULL, #0x20  ; true
    // 0xb78330: StoreField: r0->field_97 = r3
    //     0xb78330: stur            w3, [x0, #0x97]
    // 0xb78334: r1 = Null
    //     0xb78334: mov             x1, NULL
    // 0xb78338: r2 = 2
    //     0xb78338: movz            x2, #0x2
    // 0xb7833c: r0 = AllocateArray()
    //     0xb7833c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb78340: mov             x2, x0
    // 0xb78344: ldur            x0, [fp, #-0x28]
    // 0xb78348: stur            x2, [fp, #-8]
    // 0xb7834c: StoreField: r2->field_f = r0
    //     0xb7834c: stur            w0, [x2, #0xf]
    // 0xb78350: r1 = <Widget>
    //     0xb78350: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb78354: r0 = AllocateGrowableArray()
    //     0xb78354: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb78358: mov             x2, x0
    // 0xb7835c: ldur            x0, [fp, #-8]
    // 0xb78360: stur            x2, [fp, #-0x18]
    // 0xb78364: StoreField: r2->field_f = r0
    //     0xb78364: stur            w0, [x2, #0xf]
    // 0xb78368: r0 = 2
    //     0xb78368: movz            x0, #0x2
    // 0xb7836c: StoreField: r2->field_b = r0
    //     0xb7836c: stur            w0, [x2, #0xb]
    // 0xb78370: ldur            x0, [fp, #-0x10]
    // 0xb78374: LoadField: r1 = r0->field_1f
    //     0xb78374: ldur            w1, [x0, #0x1f]
    // 0xb78378: DecompressPointer r1
    //     0xb78378: add             x1, x1, HEAP, lsl #32
    // 0xb7837c: tbnz            w1, #4, #0xb783d4
    // 0xb78380: LoadField: r3 = r0->field_f
    //     0xb78380: ldur            w3, [x0, #0xf]
    // 0xb78384: DecompressPointer r3
    //     0xb78384: add             x3, x3, HEAP, lsl #32
    // 0xb78388: mov             x1, x2
    // 0xb7838c: stur            x3, [fp, #-8]
    // 0xb78390: r0 = _growToNextCapacity()
    //     0xb78390: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb78394: ldur            x2, [fp, #-0x18]
    // 0xb78398: r0 = 4
    //     0xb78398: movz            x0, #0x4
    // 0xb7839c: StoreField: r2->field_b = r0
    //     0xb7839c: stur            w0, [x2, #0xb]
    // 0xb783a0: LoadField: r1 = r2->field_f
    //     0xb783a0: ldur            w1, [x2, #0xf]
    // 0xb783a4: DecompressPointer r1
    //     0xb783a4: add             x1, x1, HEAP, lsl #32
    // 0xb783a8: ldur            x0, [fp, #-8]
    // 0xb783ac: ArrayStore: r1[1] = r0  ; List_4
    //     0xb783ac: add             x25, x1, #0x13
    //     0xb783b0: str             w0, [x25]
    //     0xb783b4: tbz             w0, #0, #0xb783d0
    //     0xb783b8: ldurb           w16, [x1, #-1]
    //     0xb783bc: ldurb           w17, [x0, #-1]
    //     0xb783c0: and             x16, x17, x16, lsr #2
    //     0xb783c4: tst             x16, HEAP, lsr #32
    //     0xb783c8: b.eq            #0xb783d0
    //     0xb783cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb783d0: b               #0xb78460
    // 0xb783d4: r0 = Container()
    //     0xb783d4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb783d8: mov             x1, x0
    // 0xb783dc: stur            x0, [fp, #-8]
    // 0xb783e0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb783e0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb783e4: r0 = Container()
    //     0xb783e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb783e8: ldur            x0, [fp, #-0x18]
    // 0xb783ec: LoadField: r1 = r0->field_b
    //     0xb783ec: ldur            w1, [x0, #0xb]
    // 0xb783f0: LoadField: r2 = r0->field_f
    //     0xb783f0: ldur            w2, [x0, #0xf]
    // 0xb783f4: DecompressPointer r2
    //     0xb783f4: add             x2, x2, HEAP, lsl #32
    // 0xb783f8: LoadField: r3 = r2->field_b
    //     0xb783f8: ldur            w3, [x2, #0xb]
    // 0xb783fc: r2 = LoadInt32Instr(r1)
    //     0xb783fc: sbfx            x2, x1, #1, #0x1f
    // 0xb78400: stur            x2, [fp, #-0x48]
    // 0xb78404: r1 = LoadInt32Instr(r3)
    //     0xb78404: sbfx            x1, x3, #1, #0x1f
    // 0xb78408: cmp             x2, x1
    // 0xb7840c: b.ne            #0xb78418
    // 0xb78410: mov             x1, x0
    // 0xb78414: r0 = _growToNextCapacity()
    //     0xb78414: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb78418: ldur            x2, [fp, #-0x18]
    // 0xb7841c: ldur            x3, [fp, #-0x48]
    // 0xb78420: add             x0, x3, #1
    // 0xb78424: lsl             x1, x0, #1
    // 0xb78428: StoreField: r2->field_b = r1
    //     0xb78428: stur            w1, [x2, #0xb]
    // 0xb7842c: LoadField: r1 = r2->field_f
    //     0xb7842c: ldur            w1, [x2, #0xf]
    // 0xb78430: DecompressPointer r1
    //     0xb78430: add             x1, x1, HEAP, lsl #32
    // 0xb78434: ldur            x0, [fp, #-8]
    // 0xb78438: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb78438: add             x25, x1, x3, lsl #2
    //     0xb7843c: add             x25, x25, #0xf
    //     0xb78440: str             w0, [x25]
    //     0xb78444: tbz             w0, #0, #0xb78460
    //     0xb78448: ldurb           w16, [x1, #-1]
    //     0xb7844c: ldurb           w17, [x0, #-1]
    //     0xb78450: and             x16, x17, x16, lsr #2
    //     0xb78454: tst             x16, HEAP, lsr #32
    //     0xb78458: b.eq            #0xb78460
    //     0xb7845c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb78460: ldur            x1, [fp, #-0x20]
    // 0xb78464: ldur            x0, [fp, #-0x38]
    // 0xb78468: r0 = Column()
    //     0xb78468: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb7846c: mov             x1, x0
    // 0xb78470: r0 = Instance_Axis
    //     0xb78470: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb78474: stur            x1, [fp, #-8]
    // 0xb78478: StoreField: r1->field_f = r0
    //     0xb78478: stur            w0, [x1, #0xf]
    // 0xb7847c: r0 = Instance_MainAxisAlignment
    //     0xb7847c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb78480: ldr             x0, [x0, #0xa08]
    // 0xb78484: StoreField: r1->field_13 = r0
    //     0xb78484: stur            w0, [x1, #0x13]
    // 0xb78488: r0 = Instance_MainAxisSize
    //     0xb78488: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7848c: ldr             x0, [x0, #0xa10]
    // 0xb78490: ArrayStore: r1[0] = r0  ; List_4
    //     0xb78490: stur            w0, [x1, #0x17]
    // 0xb78494: r0 = Instance_CrossAxisAlignment
    //     0xb78494: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb78498: ldr             x0, [x0, #0xa18]
    // 0xb7849c: StoreField: r1->field_1b = r0
    //     0xb7849c: stur            w0, [x1, #0x1b]
    // 0xb784a0: r0 = Instance_VerticalDirection
    //     0xb784a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb784a4: ldr             x0, [x0, #0xa20]
    // 0xb784a8: StoreField: r1->field_23 = r0
    //     0xb784a8: stur            w0, [x1, #0x23]
    // 0xb784ac: r0 = Instance_Clip
    //     0xb784ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb784b0: ldr             x0, [x0, #0x38]
    // 0xb784b4: StoreField: r1->field_2b = r0
    //     0xb784b4: stur            w0, [x1, #0x2b]
    // 0xb784b8: StoreField: r1->field_2f = rZR
    //     0xb784b8: stur            xzr, [x1, #0x2f]
    // 0xb784bc: ldur            x0, [fp, #-0x18]
    // 0xb784c0: StoreField: r1->field_b = r0
    //     0xb784c0: stur            w0, [x1, #0xb]
    // 0xb784c4: r0 = Card()
    //     0xb784c4: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb784c8: mov             x1, x0
    // 0xb784cc: ldur            x0, [fp, #-0x20]
    // 0xb784d0: stur            x1, [fp, #-0x10]
    // 0xb784d4: StoreField: r1->field_b = r0
    //     0xb784d4: stur            w0, [x1, #0xb]
    // 0xb784d8: r0 = 0.000000
    //     0xb784d8: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb784dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb784dc: stur            w0, [x1, #0x17]
    // 0xb784e0: ldur            x0, [fp, #-0x38]
    // 0xb784e4: StoreField: r1->field_1b = r0
    //     0xb784e4: stur            w0, [x1, #0x1b]
    // 0xb784e8: r0 = true
    //     0xb784e8: add             x0, NULL, #0x20  ; true
    // 0xb784ec: StoreField: r1->field_1f = r0
    //     0xb784ec: stur            w0, [x1, #0x1f]
    // 0xb784f0: r2 = Instance_Clip
    //     0xb784f0: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb784f4: ldr             x2, [x2, #0xb50]
    // 0xb784f8: StoreField: r1->field_23 = r2
    //     0xb784f8: stur            w2, [x1, #0x23]
    // 0xb784fc: ldur            x2, [fp, #-8]
    // 0xb78500: StoreField: r1->field_2f = r2
    //     0xb78500: stur            w2, [x1, #0x2f]
    // 0xb78504: StoreField: r1->field_2b = r0
    //     0xb78504: stur            w0, [x1, #0x2b]
    // 0xb78508: r0 = Instance__CardVariant
    //     0xb78508: add             x0, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb7850c: ldr             x0, [x0, #0xa68]
    // 0xb78510: StoreField: r1->field_33 = r0
    //     0xb78510: stur            w0, [x1, #0x33]
    // 0xb78514: r0 = Padding()
    //     0xb78514: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb78518: r1 = Instance_EdgeInsets
    //     0xb78518: add             x1, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xb7851c: ldr             x1, [x1, #0x668]
    // 0xb78520: StoreField: r0->field_f = r1
    //     0xb78520: stur            w1, [x0, #0xf]
    // 0xb78524: ldur            x1, [fp, #-0x10]
    // 0xb78528: StoreField: r0->field_b = r1
    //     0xb78528: stur            w1, [x0, #0xb]
    // 0xb7852c: LeaveFrame
    //     0xb7852c: mov             SP, fp
    //     0xb78530: ldp             fp, lr, [SP], #0x10
    // 0xb78534: ret
    //     0xb78534: ret             
    // 0xb78538: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb78538: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7853c: b               #0xb78158
    // 0xb78540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb78540: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb78544: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb78544: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb78574, size: 0x60
    // 0xb78574: EnterFrame
    //     0xb78574: stp             fp, lr, [SP, #-0x10]!
    //     0xb78578: mov             fp, SP
    // 0xb7857c: AllocStack(0x8)
    //     0xb7857c: sub             SP, SP, #8
    // 0xb78580: SetupParameters()
    //     0xb78580: ldr             x0, [fp, #0x10]
    //     0xb78584: ldur            w2, [x0, #0x17]
    //     0xb78588: add             x2, x2, HEAP, lsl #32
    // 0xb7858c: CheckStackOverflow
    //     0xb7858c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb78590: cmp             SP, x16
    //     0xb78594: b.ls            #0xb785cc
    // 0xb78598: LoadField: r0 = r2->field_f
    //     0xb78598: ldur            w0, [x2, #0xf]
    // 0xb7859c: DecompressPointer r0
    //     0xb7859c: add             x0, x0, HEAP, lsl #32
    // 0xb785a0: stur            x0, [fp, #-8]
    // 0xb785a4: r1 = Function '<anonymous closure>':.
    //     0xb785a4: add             x1, PP, #0x55, lsl #12  ; [pp+0x55aa8] AnonymousClosure: (0xb785d4), in [package:customer_app/app/presentation/views/glass/orders/order_detail_accordion.dart] _OrderDetailAccordionState::build (0xb78130)
    //     0xb785a8: ldr             x1, [x1, #0xaa8]
    // 0xb785ac: r0 = AllocateClosure()
    //     0xb785ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb785b0: ldur            x1, [fp, #-8]
    // 0xb785b4: mov             x2, x0
    // 0xb785b8: r0 = setState()
    //     0xb785b8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb785bc: r0 = Null
    //     0xb785bc: mov             x0, NULL
    // 0xb785c0: LeaveFrame
    //     0xb785c0: mov             SP, fp
    //     0xb785c4: ldp             fp, lr, [SP], #0x10
    // 0xb785c8: ret
    //     0xb785c8: ret             
    // 0xb785cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb785cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb785d0: b               #0xb78598
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb785d4, size: 0x94
    // 0xb785d4: EnterFrame
    //     0xb785d4: stp             fp, lr, [SP, #-0x10]!
    //     0xb785d8: mov             fp, SP
    // 0xb785dc: AllocStack(0x10)
    //     0xb785dc: sub             SP, SP, #0x10
    // 0xb785e0: SetupParameters()
    //     0xb785e0: ldr             x0, [fp, #0x10]
    //     0xb785e4: ldur            w1, [x0, #0x17]
    //     0xb785e8: add             x1, x1, HEAP, lsl #32
    // 0xb785ec: CheckStackOverflow
    //     0xb785ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb785f0: cmp             SP, x16
    //     0xb785f4: b.ls            #0xb7865c
    // 0xb785f8: LoadField: r0 = r1->field_f
    //     0xb785f8: ldur            w0, [x1, #0xf]
    // 0xb785fc: DecompressPointer r0
    //     0xb785fc: add             x0, x0, HEAP, lsl #32
    // 0xb78600: LoadField: r1 = r0->field_13
    //     0xb78600: ldur            w1, [x0, #0x13]
    // 0xb78604: DecompressPointer r1
    //     0xb78604: add             x1, x1, HEAP, lsl #32
    // 0xb78608: eor             x2, x1, #0x10
    // 0xb7860c: StoreField: r0->field_13 = r2
    //     0xb7860c: stur            w2, [x0, #0x13]
    // 0xb78610: LoadField: r1 = r0->field_b
    //     0xb78610: ldur            w1, [x0, #0xb]
    // 0xb78614: DecompressPointer r1
    //     0xb78614: add             x1, x1, HEAP, lsl #32
    // 0xb78618: cmp             w1, NULL
    // 0xb7861c: b.eq            #0xb78664
    // 0xb78620: LoadField: r0 = r1->field_1f
    //     0xb78620: ldur            w0, [x1, #0x1f]
    // 0xb78624: DecompressPointer r0
    //     0xb78624: add             x0, x0, HEAP, lsl #32
    // 0xb78628: LoadField: r2 = r1->field_33
    //     0xb78628: ldur            w2, [x1, #0x33]
    // 0xb7862c: DecompressPointer r2
    //     0xb7862c: add             x2, x2, HEAP, lsl #32
    // 0xb78630: stp             x0, x2, [SP]
    // 0xb78634: r4 = 0
    //     0xb78634: movz            x4, #0
    // 0xb78638: ldr             x0, [SP, #8]
    // 0xb7863c: r16 = UnlinkedCall_0x613b5c
    //     0xb7863c: add             x16, PP, #0x55, lsl #12  ; [pp+0x55ab0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb78640: add             x16, x16, #0xab0
    // 0xb78644: ldp             x5, lr, [x16]
    // 0xb78648: blr             lr
    // 0xb7864c: r0 = Null
    //     0xb7864c: mov             x0, NULL
    // 0xb78650: LeaveFrame
    //     0xb78650: mov             SP, fp
    //     0xb78654: ldp             fp, lr, [SP], #0x10
    // 0xb78658: ret
    //     0xb78658: ret             
    // 0xb7865c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7865c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb78660: b               #0xb785f8
    // 0xb78664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb78664: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4067, size: 0x38, field offset: 0xc
//   const constructor, 
class OrderDetailAccordion extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f60c, size: 0x2c
    // 0xc7f60c: EnterFrame
    //     0xc7f60c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f610: mov             fp, SP
    // 0xc7f614: mov             x0, x1
    // 0xc7f618: r1 = <OrderDetailAccordion>
    //     0xc7f618: add             x1, PP, #0x48, lsl #12  ; [pp+0x48800] TypeArguments: <OrderDetailAccordion>
    //     0xc7f61c: ldr             x1, [x1, #0x800]
    // 0xc7f620: r0 = _OrderDetailAccordionState()
    //     0xc7f620: bl              #0xc7f638  ; Allocate_OrderDetailAccordionStateStub -> _OrderDetailAccordionState (size=0x18)
    // 0xc7f624: r1 = false
    //     0xc7f624: add             x1, NULL, #0x30  ; false
    // 0xc7f628: StoreField: r0->field_13 = r1
    //     0xc7f628: stur            w1, [x0, #0x13]
    // 0xc7f62c: LeaveFrame
    //     0xc7f62c: mov             SP, fp
    //     0xc7f630: ldp             fp, lr, [SP], #0x10
    // 0xc7f634: ret
    //     0xc7f634: ret             
  }
  const _ OrderDetailAccordion(/* No info */) {
    // ** addr: 0x141d504, size: 0x98
    // 0x141d504: EnterFrame
    //     0x141d504: stp             fp, lr, [SP, #-0x10]!
    //     0x141d508: mov             fp, SP
    // 0x141d50c: r7 = false
    //     0x141d50c: add             x7, NULL, #0x30  ; false
    // 0x141d510: r4 = true
    //     0x141d510: add             x4, NULL, #0x20  ; true
    // 0x141d514: mov             x0, x6
    // 0x141d518: mov             x16, x5
    // 0x141d51c: mov             x5, x1
    // 0x141d520: mov             x1, x16
    // 0x141d524: StoreField: r5->field_b = r0
    //     0x141d524: stur            w0, [x5, #0xb]
    //     0x141d528: ldurb           w16, [x5, #-1]
    //     0x141d52c: ldurb           w17, [x0, #-1]
    //     0x141d530: and             x16, x17, x16, lsr #2
    //     0x141d534: tst             x16, HEAP, lsr #32
    //     0x141d538: b.eq            #0x141d540
    //     0x141d53c: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x141d540: mov             x0, x2
    // 0x141d544: StoreField: r5->field_f = r0
    //     0x141d544: stur            w0, [x5, #0xf]
    //     0x141d548: ldurb           w16, [x5, #-1]
    //     0x141d54c: ldurb           w17, [x0, #-1]
    //     0x141d550: and             x16, x17, x16, lsr #2
    //     0x141d554: tst             x16, HEAP, lsr #32
    //     0x141d558: b.eq            #0x141d560
    //     0x141d55c: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x141d560: StoreField: r5->field_13 = r7
    //     0x141d560: stur            w7, [x5, #0x13]
    // 0x141d564: StoreField: r5->field_1b = r4
    //     0x141d564: stur            w4, [x5, #0x1b]
    // 0x141d568: StoreField: r5->field_1f = r3
    //     0x141d568: stur            w3, [x5, #0x1f]
    // 0x141d56c: mov             x0, x1
    // 0x141d570: StoreField: r5->field_33 = r0
    //     0x141d570: stur            w0, [x5, #0x33]
    //     0x141d574: ldurb           w16, [x5, #-1]
    //     0x141d578: ldurb           w17, [x0, #-1]
    //     0x141d57c: and             x16, x17, x16, lsr #2
    //     0x141d580: tst             x16, HEAP, lsr #32
    //     0x141d584: b.eq            #0x141d58c
    //     0x141d588: bl              #0x16f5908  ; WriteBarrierWrappersStub
    // 0x141d58c: r0 = Null
    //     0x141d58c: mov             x0, NULL
    // 0x141d590: LeaveFrame
    //     0x141d590: mov             SP, fp
    //     0x141d594: ldp             fp, lr, [SP], #0x10
    // 0x141d598: ret
    //     0x141d598: ret             
  }
}
