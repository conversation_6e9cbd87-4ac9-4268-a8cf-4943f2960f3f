// lib: , url: package:customer_app/app/presentation/views/line/main/main_page.dart

// class id: 1049533, size: 0x8
class :: {
}

// class id: 3239, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __MainPageState&State&SingleTickerProviderStateMixin extends State<dynamic>
     with SingleTickerProviderStateMixin<X0 bound StatefulWidget> {

  _ activate(/* No info */) {
    // ** addr: 0x7f5424, size: 0x30
    // 0x7f5424: EnterFrame
    //     0x7f5424: stp             fp, lr, [SP, #-0x10]!
    //     0x7f5428: mov             fp, SP
    // 0x7f542c: CheckStackOverflow
    //     0x7f542c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f5430: cmp             SP, x16
    //     0x7f5434: b.ls            #0x7f544c
    // 0x7f5438: r0 = _updateTickerModeNotifier()
    //     0x7f5438: bl              #0x7f5474  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] __MainPageState&State&SingleTickerProviderStateMixin::_updateTickerModeNotifier
    // 0x7f543c: r0 = Null
    //     0x7f543c: mov             x0, NULL
    // 0x7f5440: LeaveFrame
    //     0x7f5440: mov             SP, fp
    //     0x7f5444: ldp             fp, lr, [SP], #0x10
    // 0x7f5448: ret
    //     0x7f5448: ret             
    // 0x7f544c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f544c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5450: b               #0x7f5438
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x7f5474, size: 0x124
    // 0x7f5474: EnterFrame
    //     0x7f5474: stp             fp, lr, [SP, #-0x10]!
    //     0x7f5478: mov             fp, SP
    // 0x7f547c: AllocStack(0x18)
    //     0x7f547c: sub             SP, SP, #0x18
    // 0x7f5480: SetupParameters(__MainPageState&State&SingleTickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x7f5480: mov             x2, x1
    //     0x7f5484: stur            x1, [fp, #-8]
    // 0x7f5488: CheckStackOverflow
    //     0x7f5488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7f548c: cmp             SP, x16
    //     0x7f5490: b.ls            #0x7f558c
    // 0x7f5494: LoadField: r1 = r2->field_f
    //     0x7f5494: ldur            w1, [x2, #0xf]
    // 0x7f5498: DecompressPointer r1
    //     0x7f5498: add             x1, x1, HEAP, lsl #32
    // 0x7f549c: cmp             w1, NULL
    // 0x7f54a0: b.eq            #0x7f5594
    // 0x7f54a4: r0 = getNotifier()
    //     0x7f54a4: bl              #0x78ae54  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x7f54a8: mov             x3, x0
    // 0x7f54ac: ldur            x0, [fp, #-8]
    // 0x7f54b0: stur            x3, [fp, #-0x18]
    // 0x7f54b4: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x7f54b4: ldur            w4, [x0, #0x17]
    // 0x7f54b8: DecompressPointer r4
    //     0x7f54b8: add             x4, x4, HEAP, lsl #32
    // 0x7f54bc: stur            x4, [fp, #-0x10]
    // 0x7f54c0: cmp             w3, w4
    // 0x7f54c4: b.ne            #0x7f54d8
    // 0x7f54c8: r0 = Null
    //     0x7f54c8: mov             x0, NULL
    // 0x7f54cc: LeaveFrame
    //     0x7f54cc: mov             SP, fp
    //     0x7f54d0: ldp             fp, lr, [SP], #0x10
    // 0x7f54d4: ret
    //     0x7f54d4: ret             
    // 0x7f54d8: cmp             w4, NULL
    // 0x7f54dc: b.eq            #0x7f5520
    // 0x7f54e0: mov             x2, x0
    // 0x7f54e4: r1 = Function '_updateTicker@356311458':.
    //     0x7f54e4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c728] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f54e8: ldr             x1, [x1, #0x728]
    // 0x7f54ec: r0 = AllocateClosure()
    //     0x7f54ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f54f0: ldur            x1, [fp, #-0x10]
    // 0x7f54f4: r2 = LoadClassIdInstr(r1)
    //     0x7f54f4: ldur            x2, [x1, #-1]
    //     0x7f54f8: ubfx            x2, x2, #0xc, #0x14
    // 0x7f54fc: mov             x16, x0
    // 0x7f5500: mov             x0, x2
    // 0x7f5504: mov             x2, x16
    // 0x7f5508: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0x7f5508: movz            x17, #0xdc2b
    //     0x7f550c: add             lr, x0, x17
    //     0x7f5510: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5514: blr             lr
    // 0x7f5518: ldur            x0, [fp, #-8]
    // 0x7f551c: ldur            x3, [fp, #-0x18]
    // 0x7f5520: mov             x2, x0
    // 0x7f5524: r1 = Function '_updateTicker@356311458':.
    //     0x7f5524: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c728] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x7f5528: ldr             x1, [x1, #0x728]
    // 0x7f552c: r0 = AllocateClosure()
    //     0x7f552c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x7f5530: ldur            x3, [fp, #-0x18]
    // 0x7f5534: r1 = LoadClassIdInstr(r3)
    //     0x7f5534: ldur            x1, [x3, #-1]
    //     0x7f5538: ubfx            x1, x1, #0xc, #0x14
    // 0x7f553c: mov             x2, x0
    // 0x7f5540: mov             x0, x1
    // 0x7f5544: mov             x1, x3
    // 0x7f5548: r0 = GDT[cid_x0 + 0xdc71]()
    //     0x7f5548: movz            x17, #0xdc71
    //     0x7f554c: add             lr, x0, x17
    //     0x7f5550: ldr             lr, [x21, lr, lsl #3]
    //     0x7f5554: blr             lr
    // 0x7f5558: ldur            x0, [fp, #-0x18]
    // 0x7f555c: ldur            x1, [fp, #-8]
    // 0x7f5560: ArrayStore: r1[0] = r0  ; List_4
    //     0x7f5560: stur            w0, [x1, #0x17]
    //     0x7f5564: ldurb           w16, [x1, #-1]
    //     0x7f5568: ldurb           w17, [x0, #-1]
    //     0x7f556c: and             x16, x17, x16, lsr #2
    //     0x7f5570: tst             x16, HEAP, lsr #32
    //     0x7f5574: b.eq            #0x7f557c
    //     0x7f5578: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x7f557c: r0 = Null
    //     0x7f557c: mov             x0, NULL
    // 0x7f5580: LeaveFrame
    //     0x7f5580: mov             SP, fp
    //     0x7f5584: ldp             fp, lr, [SP], #0x10
    // 0x7f5588: ret
    //     0x7f5588: ret             
    // 0x7f558c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7f558c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7f5590: b               #0x7f5494
    // 0x7f5594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7f5594: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc8834c, size: 0x94
    // 0xc8834c: EnterFrame
    //     0xc8834c: stp             fp, lr, [SP, #-0x10]!
    //     0xc88350: mov             fp, SP
    // 0xc88354: AllocStack(0x10)
    //     0xc88354: sub             SP, SP, #0x10
    // 0xc88358: SetupParameters(__MainPageState&State&SingleTickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc88358: mov             x0, x1
    //     0xc8835c: stur            x1, [fp, #-0x10]
    // 0xc88360: CheckStackOverflow
    //     0xc88360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88364: cmp             SP, x16
    //     0xc88368: b.ls            #0xc883d8
    // 0xc8836c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc8836c: ldur            w3, [x0, #0x17]
    // 0xc88370: DecompressPointer r3
    //     0xc88370: add             x3, x3, HEAP, lsl #32
    // 0xc88374: stur            x3, [fp, #-8]
    // 0xc88378: cmp             w3, NULL
    // 0xc8837c: b.ne            #0xc88388
    // 0xc88380: mov             x1, x0
    // 0xc88384: b               #0xc883c4
    // 0xc88388: mov             x2, x0
    // 0xc8838c: r1 = Function '_updateTicker@356311458':.
    //     0xc8838c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c728] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xc88390: ldr             x1, [x1, #0x728]
    // 0xc88394: r0 = AllocateClosure()
    //     0xc88394: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc88398: ldur            x1, [fp, #-8]
    // 0xc8839c: r2 = LoadClassIdInstr(r1)
    //     0xc8839c: ldur            x2, [x1, #-1]
    //     0xc883a0: ubfx            x2, x2, #0xc, #0x14
    // 0xc883a4: mov             x16, x0
    // 0xc883a8: mov             x0, x2
    // 0xc883ac: mov             x2, x16
    // 0xc883b0: r0 = GDT[cid_x0 + 0xdc2b]()
    //     0xc883b0: movz            x17, #0xdc2b
    //     0xc883b4: add             lr, x0, x17
    //     0xc883b8: ldr             lr, [x21, lr, lsl #3]
    //     0xc883bc: blr             lr
    // 0xc883c0: ldur            x1, [fp, #-0x10]
    // 0xc883c4: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc883c4: stur            NULL, [x1, #0x17]
    // 0xc883c8: r0 = Null
    //     0xc883c8: mov             x0, NULL
    // 0xc883cc: LeaveFrame
    //     0xc883cc: mov             SP, fp
    //     0xc883d0: ldp             fp, lr, [SP], #0x10
    // 0xc883d4: ret
    //     0xc883d4: ret             
    // 0xc883d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc883d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc883dc: b               #0xc8836c
  }
}

// class id: 3240, size: 0x54, field offset: 0x1c
class _MainPageState extends __MainPageState&State&SingleTickerProviderStateMixin {

  static late final List<Widget> _pages; // offset: 0xe78

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x915664, size: 0x24
    // 0x915664: r1 = 1
    //     0x915664: movz            x1, #0x1
    // 0x915668: ldr             x2, [SP]
    // 0x91566c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x91566c: ldur            w3, [x2, #0x17]
    // 0x915670: DecompressPointer r3
    //     0x915670: add             x3, x3, HEAP, lsl #32
    // 0x915674: LoadField: r2 = r3->field_f
    //     0x915674: ldur            w2, [x3, #0xf]
    // 0x915678: DecompressPointer r2
    //     0x915678: add             x2, x2, HEAP, lsl #32
    // 0x91567c: StoreField: r2->field_1b = r1
    //     0x91567c: stur            x1, [x2, #0x1b]
    // 0x915680: r0 = Null
    //     0x915680: mov             x0, NULL
    // 0x915684: ret
    //     0x915684: ret             
  }
  _ openUrlInApp(/* No info */) {
    // ** addr: 0x915688, size: 0x758
    // 0x915688: EnterFrame
    //     0x915688: stp             fp, lr, [SP, #-0x10]!
    //     0x91568c: mov             fp, SP
    // 0x915690: AllocStack(0x40)
    //     0x915690: sub             SP, SP, #0x40
    // 0x915694: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0x915694: stur            x1, [fp, #-8]
    // 0x915698: CheckStackOverflow
    //     0x915698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91569c: cmp             SP, x16
    //     0x9156a0: b.ls            #0x915dc8
    // 0x9156a4: r1 = 1
    //     0x9156a4: movz            x1, #0x1
    // 0x9156a8: r0 = AllocateContext()
    //     0x9156a8: bl              #0x16f6108  ; AllocateContextStub
    // 0x9156ac: mov             x3, x0
    // 0x9156b0: ldur            x2, [fp, #-8]
    // 0x9156b4: stur            x3, [fp, #-0x10]
    // 0x9156b8: StoreField: r3->field_f = r2
    //     0x9156b8: stur            w2, [x3, #0xf]
    // 0x9156bc: LoadField: r1 = r2->field_23
    //     0x9156bc: ldur            w1, [x2, #0x23]
    // 0x9156c0: DecompressPointer r1
    //     0x9156c0: add             x1, x1, HEAP, lsl #32
    // 0x9156c4: cmp             w1, NULL
    // 0x9156c8: b.ne            #0x9156d4
    // 0x9156cc: r0 = Null
    //     0x9156cc: mov             x0, NULL
    // 0x9156d0: b               #0x915714
    // 0x9156d4: r0 = LoadClassIdInstr(r1)
    //     0x9156d4: ldur            x0, [x1, #-1]
    //     0x9156d8: ubfx            x0, x0, #0xc, #0x14
    // 0x9156dc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9156dc: sub             lr, x0, #1, lsl #12
    //     0x9156e0: ldr             lr, [x21, lr, lsl #3]
    //     0x9156e4: blr             lr
    // 0x9156e8: r1 = LoadClassIdInstr(r0)
    //     0x9156e8: ldur            x1, [x0, #-1]
    //     0x9156ec: ubfx            x1, x1, #0xc, #0x14
    // 0x9156f0: mov             x16, x0
    // 0x9156f4: mov             x0, x1
    // 0x9156f8: mov             x1, x16
    // 0x9156fc: r2 = "/catalogue/"
    //     0x9156fc: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da58] "/catalogue/"
    //     0x915700: ldr             x2, [x2, #0xa58]
    // 0x915704: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x915704: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x915708: r0 = GDT[cid_x0 + -0xffe]()
    //     0x915708: sub             lr, x0, #0xffe
    //     0x91570c: ldr             lr, [x21, lr, lsl #3]
    //     0x915710: blr             lr
    // 0x915714: cmp             w0, NULL
    // 0x915718: b.ne            #0x915724
    // 0x91571c: ldur            x2, [fp, #-8]
    // 0x915720: b               #0x91593c
    // 0x915724: tbnz            w0, #4, #0x915938
    // 0x915728: ldur            x2, [fp, #-8]
    // 0x91572c: LoadField: r1 = r2->field_23
    //     0x91572c: ldur            w1, [x2, #0x23]
    // 0x915730: DecompressPointer r1
    //     0x915730: add             x1, x1, HEAP, lsl #32
    // 0x915734: cmp             w1, NULL
    // 0x915738: b.ne            #0x915744
    // 0x91573c: r0 = Null
    //     0x91573c: mov             x0, NULL
    // 0x915740: b               #0x915758
    // 0x915744: r0 = LoadClassIdInstr(r1)
    //     0x915744: ldur            x0, [x1, #-1]
    //     0x915748: ubfx            x0, x0, #0xc, #0x14
    // 0x91574c: r0 = GDT[cid_x0 + -0xea5]()
    //     0x91574c: sub             lr, x0, #0xea5
    //     0x915750: ldr             lr, [x21, lr, lsl #3]
    //     0x915754: blr             lr
    // 0x915758: stur            x0, [fp, #-0x18]
    // 0x91575c: cmp             w0, NULL
    // 0x915760: b.ne            #0x91576c
    // 0x915764: r2 = Null
    //     0x915764: mov             x2, NULL
    // 0x915768: b               #0x91579c
    // 0x91576c: mov             x1, x0
    // 0x915770: r2 = "catalogue"
    //     0x915770: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da60] "catalogue"
    //     0x915774: ldr             x2, [x2, #0xa60]
    // 0x915778: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x915778: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91577c: r0 = indexOf()
    //     0x91577c: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x915780: mov             x2, x0
    // 0x915784: r0 = BoxInt64Instr(r2)
    //     0x915784: sbfiz           x0, x2, #1, #0x1f
    //     0x915788: cmp             x2, x0, asr #1
    //     0x91578c: b.eq            #0x915798
    //     0x915790: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x915794: stur            x2, [x0, #7]
    // 0x915798: mov             x2, x0
    // 0x91579c: cmp             w2, NULL
    // 0x9157a0: b.eq            #0x915878
    // 0x9157a4: ldur            x3, [fp, #-0x18]
    // 0x9157a8: cmp             w3, NULL
    // 0x9157ac: b.ne            #0x9157b8
    // 0x9157b0: r0 = Null
    //     0x9157b0: mov             x0, NULL
    // 0x9157b4: b               #0x9157ec
    // 0x9157b8: r0 = LoadInt32Instr(r2)
    //     0x9157b8: sbfx            x0, x2, #1, #0x1f
    //     0x9157bc: tbz             w2, #0, #0x9157c4
    //     0x9157c0: ldur            x0, [x2, #7]
    // 0x9157c4: add             x4, x0, #2
    // 0x9157c8: LoadField: r0 = r3->field_b
    //     0x9157c8: ldur            w0, [x3, #0xb]
    // 0x9157cc: r1 = LoadInt32Instr(r0)
    //     0x9157cc: sbfx            x1, x0, #1, #0x1f
    // 0x9157d0: mov             x0, x1
    // 0x9157d4: mov             x1, x4
    // 0x9157d8: cmp             x1, x0
    // 0x9157dc: b.hs            #0x915dd0
    // 0x9157e0: ArrayLoad: r0 = r3[r4]  ; Unknown_4
    //     0x9157e0: add             x16, x3, x4, lsl #2
    //     0x9157e4: ldur            w0, [x16, #0xf]
    // 0x9157e8: DecompressPointer r0
    //     0x9157e8: add             x0, x0, HEAP, lsl #32
    // 0x9157ec: ldur            x4, [fp, #-8]
    // 0x9157f0: StoreField: r4->field_37 = r0
    //     0x9157f0: stur            w0, [x4, #0x37]
    //     0x9157f4: tbz             w0, #0, #0x915810
    //     0x9157f8: ldurb           w16, [x4, #-1]
    //     0x9157fc: ldurb           w17, [x0, #-1]
    //     0x915800: and             x16, x17, x16, lsr #2
    //     0x915804: tst             x16, HEAP, lsr #32
    //     0x915808: b.eq            #0x915810
    //     0x91580c: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x915810: cmp             w3, NULL
    // 0x915814: b.ne            #0x915820
    // 0x915818: r0 = Null
    //     0x915818: mov             x0, NULL
    // 0x91581c: b               #0x915854
    // 0x915820: r0 = LoadInt32Instr(r2)
    //     0x915820: sbfx            x0, x2, #1, #0x1f
    //     0x915824: tbz             w2, #0, #0x91582c
    //     0x915828: ldur            x0, [x2, #7]
    // 0x91582c: add             x2, x0, #1
    // 0x915830: LoadField: r0 = r3->field_b
    //     0x915830: ldur            w0, [x3, #0xb]
    // 0x915834: r1 = LoadInt32Instr(r0)
    //     0x915834: sbfx            x1, x0, #1, #0x1f
    // 0x915838: mov             x0, x1
    // 0x91583c: mov             x1, x2
    // 0x915840: cmp             x1, x0
    // 0x915844: b.hs            #0x915dd4
    // 0x915848: ArrayLoad: r0 = r3[r2]  ; Unknown_4
    //     0x915848: add             x16, x3, x2, lsl #2
    //     0x91584c: ldur            w0, [x16, #0xf]
    // 0x915850: DecompressPointer r0
    //     0x915850: add             x0, x0, HEAP, lsl #32
    // 0x915854: StoreField: r4->field_33 = r0
    //     0x915854: stur            w0, [x4, #0x33]
    //     0x915858: tbz             w0, #0, #0x915874
    //     0x91585c: ldurb           w16, [x4, #-1]
    //     0x915860: ldurb           w17, [x0, #-1]
    //     0x915864: and             x16, x17, x16, lsr #2
    //     0x915868: tst             x16, HEAP, lsr #32
    //     0x91586c: b.eq            #0x915874
    //     0x915870: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x915874: b               #0x91587c
    // 0x915878: ldur            x4, [fp, #-8]
    // 0x91587c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x91587c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x915880: ldr             x0, [x0, #0x1c80]
    //     0x915884: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x915888: cmp             w0, w16
    //     0x91588c: b.ne            #0x915898
    //     0x915890: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x915894: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x915898: r1 = Null
    //     0x915898: mov             x1, NULL
    // 0x91589c: r2 = 16
    //     0x91589c: movz            x2, #0x10
    // 0x9158a0: r0 = AllocateArray()
    //     0x9158a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9158a4: r16 = "short_id"
    //     0x9158a4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb488] "short_id"
    //     0x9158a8: ldr             x16, [x16, #0x488]
    // 0x9158ac: StoreField: r0->field_f = r16
    //     0x9158ac: stur            w16, [x0, #0xf]
    // 0x9158b0: ldur            x2, [fp, #-8]
    // 0x9158b4: LoadField: r1 = r2->field_33
    //     0x9158b4: ldur            w1, [x2, #0x33]
    // 0x9158b8: DecompressPointer r1
    //     0x9158b8: add             x1, x1, HEAP, lsl #32
    // 0x9158bc: StoreField: r0->field_13 = r1
    //     0x9158bc: stur            w1, [x0, #0x13]
    // 0x9158c0: r16 = "sku_id"
    //     0x9158c0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x9158c4: ldr             x16, [x16, #0x498]
    // 0x9158c8: ArrayStore: r0[0] = r16  ; List_4
    //     0x9158c8: stur            w16, [x0, #0x17]
    // 0x9158cc: LoadField: r1 = r2->field_37
    //     0x9158cc: ldur            w1, [x2, #0x37]
    // 0x9158d0: DecompressPointer r1
    //     0x9158d0: add             x1, x1, HEAP, lsl #32
    // 0x9158d4: StoreField: r0->field_1b = r1
    //     0x9158d4: stur            w1, [x0, #0x1b]
    // 0x9158d8: r16 = "previousScreenSource"
    //     0x9158d8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x9158dc: ldr             x16, [x16, #0x448]
    // 0x9158e0: StoreField: r0->field_1f = r16
    //     0x9158e0: stur            w16, [x0, #0x1f]
    // 0x9158e4: r16 = "Launch page"
    //     0x9158e4: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2da68] "Launch page"
    //     0x9158e8: ldr             x16, [x16, #0xa68]
    // 0x9158ec: StoreField: r0->field_23 = r16
    //     0x9158ec: stur            w16, [x0, #0x23]
    // 0x9158f0: r16 = "screenSource"
    //     0x9158f0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x9158f4: ldr             x16, [x16, #0x450]
    // 0x9158f8: StoreField: r0->field_27 = r16
    //     0x9158f8: stur            w16, [x0, #0x27]
    // 0x9158fc: r16 = "App Launching"
    //     0x9158fc: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2da70] "App Launching"
    //     0x915900: ldr             x16, [x16, #0xa70]
    // 0x915904: StoreField: r0->field_2b = r16
    //     0x915904: stur            w16, [x0, #0x2b]
    // 0x915908: r16 = <String, String?>
    //     0x915908: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x91590c: ldr             x16, [x16, #0x3c8]
    // 0x915910: stp             x0, x16, [SP]
    // 0x915914: r0 = Map._fromLiteral()
    //     0x915914: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x915918: r16 = "/product-detail"
    //     0x915918: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x91591c: ldr             x16, [x16, #0x4a8]
    // 0x915920: stp             x16, NULL, [SP, #8]
    // 0x915924: str             x0, [SP]
    // 0x915928: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x915928: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x91592c: ldr             x4, [x4, #0x438]
    // 0x915930: r0 = GetNavigation.toNamed()
    //     0x915930: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x915934: b               #0x915db8
    // 0x915938: ldur            x2, [fp, #-8]
    // 0x91593c: LoadField: r1 = r2->field_23
    //     0x91593c: ldur            w1, [x2, #0x23]
    // 0x915940: DecompressPointer r1
    //     0x915940: add             x1, x1, HEAP, lsl #32
    // 0x915944: cmp             w1, NULL
    // 0x915948: b.ne            #0x915954
    // 0x91594c: r0 = Null
    //     0x91594c: mov             x0, NULL
    // 0x915950: b               #0x915994
    // 0x915954: r0 = LoadClassIdInstr(r1)
    //     0x915954: ldur            x0, [x1, #-1]
    //     0x915958: ubfx            x0, x0, #0xc, #0x14
    // 0x91595c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x91595c: sub             lr, x0, #1, lsl #12
    //     0x915960: ldr             lr, [x21, lr, lsl #3]
    //     0x915964: blr             lr
    // 0x915968: r1 = LoadClassIdInstr(r0)
    //     0x915968: ldur            x1, [x0, #-1]
    //     0x91596c: ubfx            x1, x1, #0xc, #0x14
    // 0x915970: mov             x16, x0
    // 0x915974: mov             x0, x1
    // 0x915978: mov             x1, x16
    // 0x91597c: r2 = "/collection/"
    //     0x91597c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da78] "/collection/"
    //     0x915980: ldr             x2, [x2, #0xa78]
    // 0x915984: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x915984: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x915988: r0 = GDT[cid_x0 + -0xffe]()
    //     0x915988: sub             lr, x0, #0xffe
    //     0x91598c: ldr             lr, [x21, lr, lsl #3]
    //     0x915990: blr             lr
    // 0x915994: cmp             w0, NULL
    // 0x915998: b.ne            #0x9159a4
    // 0x91599c: ldur            x2, [fp, #-8]
    // 0x9159a0: b               #0x915a9c
    // 0x9159a4: tbnz            w0, #4, #0x915a98
    // 0x9159a8: ldur            x2, [fp, #-8]
    // 0x9159ac: LoadField: r1 = r2->field_23
    //     0x9159ac: ldur            w1, [x2, #0x23]
    // 0x9159b0: DecompressPointer r1
    //     0x9159b0: add             x1, x1, HEAP, lsl #32
    // 0x9159b4: cmp             w1, NULL
    // 0x9159b8: b.ne            #0x9159c4
    // 0x9159bc: r0 = Null
    //     0x9159bc: mov             x0, NULL
    // 0x9159c0: b               #0x9159d8
    // 0x9159c4: r0 = LoadClassIdInstr(r1)
    //     0x9159c4: ldur            x0, [x1, #-1]
    //     0x9159c8: ubfx            x0, x0, #0xc, #0x14
    // 0x9159cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x9159cc: sub             lr, x0, #1, lsl #12
    //     0x9159d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9159d4: blr             lr
    // 0x9159d8: stur            x0, [fp, #-0x18]
    // 0x9159dc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x9159dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9159e0: ldr             x0, [x0, #0x1c80]
    //     0x9159e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9159e8: cmp             w0, w16
    //     0x9159ec: b.ne            #0x9159f8
    //     0x9159f0: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x9159f4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9159f8: r1 = Null
    //     0x9159f8: mov             x1, NULL
    // 0x9159fc: r2 = 12
    //     0x9159fc: movz            x2, #0xc
    // 0x915a00: r0 = AllocateArray()
    //     0x915a00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x915a04: stur            x0, [fp, #-0x20]
    // 0x915a08: r16 = "link"
    //     0x915a08: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0x915a0c: StoreField: r0->field_f = r16
    //     0x915a0c: stur            w16, [x0, #0xf]
    // 0x915a10: ldur            x16, [fp, #-0x18]
    // 0x915a14: str             x16, [SP]
    // 0x915a18: r0 = _interpolateSingle()
    //     0x915a18: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x915a1c: ldur            x1, [fp, #-0x20]
    // 0x915a20: ArrayStore: r1[1] = r0  ; List_4
    //     0x915a20: add             x25, x1, #0x13
    //     0x915a24: str             w0, [x25]
    //     0x915a28: tbz             w0, #0, #0x915a44
    //     0x915a2c: ldurb           w16, [x1, #-1]
    //     0x915a30: ldurb           w17, [x0, #-1]
    //     0x915a34: and             x16, x17, x16, lsr #2
    //     0x915a38: tst             x16, HEAP, lsr #32
    //     0x915a3c: b.eq            #0x915a44
    //     0x915a40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x915a44: ldur            x0, [fp, #-0x20]
    // 0x915a48: r16 = "previousScreenSource"
    //     0x915a48: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x915a4c: ldr             x16, [x16, #0x448]
    // 0x915a50: ArrayStore: r0[0] = r16  ; List_4
    //     0x915a50: stur            w16, [x0, #0x17]
    // 0x915a54: StoreField: r0->field_1b = rNULL
    //     0x915a54: stur            NULL, [x0, #0x1b]
    // 0x915a58: r16 = "screenSource"
    //     0x915a58: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x915a5c: ldr             x16, [x16, #0x450]
    // 0x915a60: StoreField: r0->field_1f = r16
    //     0x915a60: stur            w16, [x0, #0x1f]
    // 0x915a64: StoreField: r0->field_23 = rNULL
    //     0x915a64: stur            NULL, [x0, #0x23]
    // 0x915a68: r16 = <String, String?>
    //     0x915a68: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] TypeArguments: <String, String?>
    //     0x915a6c: ldr             x16, [x16, #0x3c8]
    // 0x915a70: stp             x0, x16, [SP]
    // 0x915a74: r0 = Map._fromLiteral()
    //     0x915a74: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x915a78: r16 = "/collection"
    //     0x915a78: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x915a7c: ldr             x16, [x16, #0x458]
    // 0x915a80: stp             x16, NULL, [SP, #8]
    // 0x915a84: str             x0, [SP]
    // 0x915a88: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x915a88: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x915a8c: ldr             x4, [x4, #0x438]
    // 0x915a90: r0 = GetNavigation.toNamed()
    //     0x915a90: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x915a94: b               #0x915db8
    // 0x915a98: ldur            x2, [fp, #-8]
    // 0x915a9c: LoadField: r1 = r2->field_23
    //     0x915a9c: ldur            w1, [x2, #0x23]
    // 0x915aa0: DecompressPointer r1
    //     0x915aa0: add             x1, x1, HEAP, lsl #32
    // 0x915aa4: cmp             w1, NULL
    // 0x915aa8: b.ne            #0x915ab4
    // 0x915aac: r0 = Null
    //     0x915aac: mov             x0, NULL
    // 0x915ab0: b               #0x915af4
    // 0x915ab4: r0 = LoadClassIdInstr(r1)
    //     0x915ab4: ldur            x0, [x1, #-1]
    //     0x915ab8: ubfx            x0, x0, #0xc, #0x14
    // 0x915abc: r0 = GDT[cid_x0 + -0x1000]()
    //     0x915abc: sub             lr, x0, #1, lsl #12
    //     0x915ac0: ldr             lr, [x21, lr, lsl #3]
    //     0x915ac4: blr             lr
    // 0x915ac8: r1 = LoadClassIdInstr(r0)
    //     0x915ac8: ldur            x1, [x0, #-1]
    //     0x915acc: ubfx            x1, x1, #0xc, #0x14
    // 0x915ad0: mov             x16, x0
    // 0x915ad4: mov             x0, x1
    // 0x915ad8: mov             x1, x16
    // 0x915adc: r2 = "/bag"
    //     0x915adc: add             x2, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x915ae0: ldr             x2, [x2, #0x468]
    // 0x915ae4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x915ae4: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x915ae8: r0 = GDT[cid_x0 + -0xffe]()
    //     0x915ae8: sub             lr, x0, #0xffe
    //     0x915aec: ldr             lr, [x21, lr, lsl #3]
    //     0x915af0: blr             lr
    // 0x915af4: cmp             w0, NULL
    // 0x915af8: b.ne            #0x915b04
    // 0x915afc: ldur            x2, [fp, #-8]
    // 0x915b00: b               #0x915d2c
    // 0x915b04: tbnz            w0, #4, #0x915d28
    // 0x915b08: ldur            x2, [fp, #-8]
    // 0x915b0c: LoadField: r1 = r2->field_23
    //     0x915b0c: ldur            w1, [x2, #0x23]
    // 0x915b10: DecompressPointer r1
    //     0x915b10: add             x1, x1, HEAP, lsl #32
    // 0x915b14: cmp             w1, NULL
    // 0x915b18: b.ne            #0x915b24
    // 0x915b1c: r0 = Null
    //     0x915b1c: mov             x0, NULL
    // 0x915b20: b               #0x915b64
    // 0x915b24: r0 = LoadClassIdInstr(r1)
    //     0x915b24: ldur            x0, [x1, #-1]
    //     0x915b28: ubfx            x0, x0, #0xc, #0x14
    // 0x915b2c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x915b2c: sub             lr, x0, #1, lsl #12
    //     0x915b30: ldr             lr, [x21, lr, lsl #3]
    //     0x915b34: blr             lr
    // 0x915b38: mov             x1, x0
    // 0x915b3c: r2 = "/"
    //     0x915b3c: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x915b40: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x915b40: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x915b44: r0 = lastIndexOf()
    //     0x915b44: bl              #0x6394bc  ; [dart:core] _StringBase::lastIndexOf
    // 0x915b48: mov             x2, x0
    // 0x915b4c: r0 = BoxInt64Instr(r2)
    //     0x915b4c: sbfiz           x0, x2, #1, #0x1f
    //     0x915b50: cmp             x2, x0, asr #1
    //     0x915b54: b.eq            #0x915b60
    //     0x915b58: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x915b5c: stur            x2, [x0, #7]
    // 0x915b60: ldur            x2, [fp, #-8]
    // 0x915b64: StoreField: r2->field_2b = r0
    //     0x915b64: stur            w0, [x2, #0x2b]
    //     0x915b68: tbz             w0, #0, #0x915b84
    //     0x915b6c: ldurb           w16, [x2, #-1]
    //     0x915b70: ldurb           w17, [x0, #-1]
    //     0x915b74: and             x16, x17, x16, lsr #2
    //     0x915b78: tst             x16, HEAP, lsr #32
    //     0x915b7c: b.eq            #0x915b84
    //     0x915b80: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x915b84: LoadField: r1 = r2->field_23
    //     0x915b84: ldur            w1, [x2, #0x23]
    // 0x915b88: DecompressPointer r1
    //     0x915b88: add             x1, x1, HEAP, lsl #32
    // 0x915b8c: cmp             w1, NULL
    // 0x915b90: b.ne            #0x915b9c
    // 0x915b94: r0 = Null
    //     0x915b94: mov             x0, NULL
    // 0x915b98: b               #0x915c1c
    // 0x915b9c: r0 = LoadClassIdInstr(r1)
    //     0x915b9c: ldur            x0, [x1, #-1]
    //     0x915ba0: ubfx            x0, x0, #0xc, #0x14
    // 0x915ba4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x915ba4: sub             lr, x0, #1, lsl #12
    //     0x915ba8: ldr             lr, [x21, lr, lsl #3]
    //     0x915bac: blr             lr
    // 0x915bb0: mov             x2, x0
    // 0x915bb4: ldur            x3, [fp, #-8]
    // 0x915bb8: LoadField: r0 = r3->field_2b
    //     0x915bb8: ldur            w0, [x3, #0x2b]
    // 0x915bbc: DecompressPointer r0
    //     0x915bbc: add             x0, x0, HEAP, lsl #32
    // 0x915bc0: cmp             w0, NULL
    // 0x915bc4: b.eq            #0x915dd8
    // 0x915bc8: r1 = LoadInt32Instr(r0)
    //     0x915bc8: sbfx            x1, x0, #1, #0x1f
    //     0x915bcc: tbz             w0, #0, #0x915bd4
    //     0x915bd0: ldur            x1, [x0, #7]
    // 0x915bd4: sub             x4, x1, #1
    // 0x915bd8: r0 = BoxInt64Instr(r4)
    //     0x915bd8: sbfiz           x0, x4, #1, #0x1f
    //     0x915bdc: cmp             x4, x0, asr #1
    //     0x915be0: b.eq            #0x915bec
    //     0x915be4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x915be8: stur            x4, [x0, #7]
    // 0x915bec: str             x0, [SP]
    // 0x915bf0: mov             x1, x2
    // 0x915bf4: r2 = "/"
    //     0x915bf4: ldr             x2, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x915bf8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x915bf8: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x915bfc: r0 = lastIndexOf()
    //     0x915bfc: bl              #0x6394bc  ; [dart:core] _StringBase::lastIndexOf
    // 0x915c00: mov             x2, x0
    // 0x915c04: r0 = BoxInt64Instr(r2)
    //     0x915c04: sbfiz           x0, x2, #1, #0x1f
    //     0x915c08: cmp             x2, x0, asr #1
    //     0x915c0c: b.eq            #0x915c18
    //     0x915c10: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x915c14: stur            x2, [x0, #7]
    // 0x915c18: ldur            x2, [fp, #-8]
    // 0x915c1c: StoreField: r2->field_2f = r0
    //     0x915c1c: stur            w0, [x2, #0x2f]
    //     0x915c20: tbz             w0, #0, #0x915c3c
    //     0x915c24: ldurb           w16, [x2, #-1]
    //     0x915c28: ldurb           w17, [x0, #-1]
    //     0x915c2c: and             x16, x17, x16, lsr #2
    //     0x915c30: tst             x16, HEAP, lsr #32
    //     0x915c34: b.eq            #0x915c3c
    //     0x915c38: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x915c3c: LoadField: r1 = r2->field_23
    //     0x915c3c: ldur            w1, [x2, #0x23]
    // 0x915c40: DecompressPointer r1
    //     0x915c40: add             x1, x1, HEAP, lsl #32
    // 0x915c44: cmp             w1, NULL
    // 0x915c48: b.eq            #0x915cf4
    // 0x915c4c: r0 = LoadClassIdInstr(r1)
    //     0x915c4c: ldur            x0, [x1, #-1]
    //     0x915c50: ubfx            x0, x0, #0xc, #0x14
    // 0x915c54: r0 = GDT[cid_x0 + -0x1000]()
    //     0x915c54: sub             lr, x0, #1, lsl #12
    //     0x915c58: ldr             lr, [x21, lr, lsl #3]
    //     0x915c5c: blr             lr
    // 0x915c60: mov             x3, x0
    // 0x915c64: ldur            x2, [fp, #-8]
    // 0x915c68: stur            x3, [fp, #-0x18]
    // 0x915c6c: LoadField: r0 = r2->field_2f
    //     0x915c6c: ldur            w0, [x2, #0x2f]
    // 0x915c70: DecompressPointer r0
    //     0x915c70: add             x0, x0, HEAP, lsl #32
    // 0x915c74: cmp             w0, NULL
    // 0x915c78: b.eq            #0x915ddc
    // 0x915c7c: r1 = LoadInt32Instr(r0)
    //     0x915c7c: sbfx            x1, x0, #1, #0x1f
    //     0x915c80: tbz             w0, #0, #0x915c88
    //     0x915c84: ldur            x1, [x0, #7]
    // 0x915c88: add             x4, x1, #1
    // 0x915c8c: stur            x4, [fp, #-0x28]
    // 0x915c90: LoadField: r1 = r2->field_23
    //     0x915c90: ldur            w1, [x2, #0x23]
    // 0x915c94: DecompressPointer r1
    //     0x915c94: add             x1, x1, HEAP, lsl #32
    // 0x915c98: cmp             w1, NULL
    // 0x915c9c: b.ne            #0x915cac
    // 0x915ca0: mov             x2, x4
    // 0x915ca4: r3 = Null
    //     0x915ca4: mov             x3, NULL
    // 0x915ca8: b               #0x915ccc
    // 0x915cac: r0 = LoadClassIdInstr(r1)
    //     0x915cac: ldur            x0, [x1, #-1]
    //     0x915cb0: ubfx            x0, x0, #0xc, #0x14
    // 0x915cb4: r0 = GDT[cid_x0 + -0x1000]()
    //     0x915cb4: sub             lr, x0, #1, lsl #12
    //     0x915cb8: ldr             lr, [x21, lr, lsl #3]
    //     0x915cbc: blr             lr
    // 0x915cc0: LoadField: r1 = r0->field_7
    //     0x915cc0: ldur            w1, [x0, #7]
    // 0x915cc4: mov             x3, x1
    // 0x915cc8: ldur            x2, [fp, #-0x28]
    // 0x915ccc: r0 = BoxInt64Instr(r2)
    //     0x915ccc: sbfiz           x0, x2, #1, #0x1f
    //     0x915cd0: cmp             x2, x0, asr #1
    //     0x915cd4: b.eq            #0x915ce0
    //     0x915cd8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x915cdc: stur            x2, [x0, #7]
    // 0x915ce0: str             x3, [SP]
    // 0x915ce4: ldur            x1, [fp, #-0x18]
    // 0x915ce8: mov             x2, x0
    // 0x915cec: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x915cec: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x915cf0: r0 = substring()
    //     0x915cf0: bl              #0x61f5c0  ; [dart:core] _StringBase::substring
    // 0x915cf4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x915cf4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x915cf8: ldr             x0, [x0, #0x1c80]
    //     0x915cfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x915d00: cmp             w0, w16
    //     0x915d04: b.ne            #0x915d10
    //     0x915d08: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x915d0c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x915d10: r16 = "/bag"
    //     0x915d10: add             x16, PP, #0xb, lsl #12  ; [pp+0xb468] "/bag"
    //     0x915d14: ldr             x16, [x16, #0x468]
    // 0x915d18: stp             x16, NULL, [SP]
    // 0x915d1c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x915d1c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x915d20: r0 = GetNavigation.toNamed()
    //     0x915d20: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x915d24: b               #0x915db8
    // 0x915d28: ldur            x2, [fp, #-8]
    // 0x915d2c: LoadField: r1 = r2->field_23
    //     0x915d2c: ldur            w1, [x2, #0x23]
    // 0x915d30: DecompressPointer r1
    //     0x915d30: add             x1, x1, HEAP, lsl #32
    // 0x915d34: cmp             w1, NULL
    // 0x915d38: b.ne            #0x915d44
    // 0x915d3c: r0 = Null
    //     0x915d3c: mov             x0, NULL
    // 0x915d40: b               #0x915d84
    // 0x915d44: r0 = LoadClassIdInstr(r1)
    //     0x915d44: ldur            x0, [x1, #-1]
    //     0x915d48: ubfx            x0, x0, #0xc, #0x14
    // 0x915d4c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x915d4c: sub             lr, x0, #1, lsl #12
    //     0x915d50: ldr             lr, [x21, lr, lsl #3]
    //     0x915d54: blr             lr
    // 0x915d58: r1 = LoadClassIdInstr(r0)
    //     0x915d58: ldur            x1, [x0, #-1]
    //     0x915d5c: ubfx            x1, x1, #0xc, #0x14
    // 0x915d60: mov             x16, x0
    // 0x915d64: mov             x0, x1
    // 0x915d68: mov             x1, x16
    // 0x915d6c: r2 = "/orders"
    //     0x915d6c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2da80] "/orders"
    //     0x915d70: ldr             x2, [x2, #0xa80]
    // 0x915d74: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x915d74: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x915d78: r0 = GDT[cid_x0 + -0xffe]()
    //     0x915d78: sub             lr, x0, #0xffe
    //     0x915d7c: ldr             lr, [x21, lr, lsl #3]
    //     0x915d80: blr             lr
    // 0x915d84: cmp             w0, NULL
    // 0x915d88: b.eq            #0x915db8
    // 0x915d8c: tbnz            w0, #4, #0x915db8
    // 0x915d90: ldur            x2, [fp, #-0x10]
    // 0x915d94: r1 = Function '<anonymous closure>':.
    //     0x915d94: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2da88] AnonymousClosure: (0x915664), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openUrlInApp (0x915688)
    //     0x915d98: ldr             x1, [x1, #0xa88]
    // 0x915d9c: r0 = AllocateClosure()
    //     0x915d9c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x915da0: ldur            x1, [fp, #-8]
    // 0x915da4: mov             x2, x0
    // 0x915da8: r0 = setState()
    //     0x915da8: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x915dac: ldur            x1, [fp, #-8]
    // 0x915db0: r2 = 2
    //     0x915db0: movz            x2, #0x2
    // 0x915db4: r0 = _onItemTapped()
    //     0x915db4: bl              #0x915de0  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x915db8: r0 = Null
    //     0x915db8: mov             x0, NULL
    // 0x915dbc: LeaveFrame
    //     0x915dbc: mov             SP, fp
    //     0x915dc0: ldp             fp, lr, [SP], #0x10
    // 0x915dc4: ret
    //     0x915dc4: ret             
    // 0x915dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x915dc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x915dcc: b               #0x9156a4
    // 0x915dd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x915dd0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x915dd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x915dd4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x915dd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x915dd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x915ddc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x915ddc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _onItemTapped(/* No info */) {
    // ** addr: 0x915de0, size: 0x70
    // 0x915de0: EnterFrame
    //     0x915de0: stp             fp, lr, [SP, #-0x10]!
    //     0x915de4: mov             fp, SP
    // 0x915de8: AllocStack(0x10)
    //     0x915de8: sub             SP, SP, #0x10
    // 0x915dec: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x915dec: stur            x1, [fp, #-8]
    //     0x915df0: stur            x2, [fp, #-0x10]
    // 0x915df4: CheckStackOverflow
    //     0x915df4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x915df8: cmp             SP, x16
    //     0x915dfc: b.ls            #0x915e48
    // 0x915e00: r1 = 2
    //     0x915e00: movz            x1, #0x2
    // 0x915e04: r0 = AllocateContext()
    //     0x915e04: bl              #0x16f6108  ; AllocateContextStub
    // 0x915e08: mov             x1, x0
    // 0x915e0c: ldur            x0, [fp, #-8]
    // 0x915e10: StoreField: r1->field_f = r0
    //     0x915e10: stur            w0, [x1, #0xf]
    // 0x915e14: ldur            x2, [fp, #-0x10]
    // 0x915e18: StoreField: r1->field_13 = r2
    //     0x915e18: stur            w2, [x1, #0x13]
    // 0x915e1c: mov             x2, x1
    // 0x915e20: r1 = Function '<anonymous closure>':.
    //     0x915e20: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbf0] AnonymousClosure: (0x915e8c), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped (0x915de0)
    //     0x915e24: ldr             x1, [x1, #0xbf0]
    // 0x915e28: r0 = AllocateClosure()
    //     0x915e28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x915e2c: ldur            x1, [fp, #-8]
    // 0x915e30: mov             x2, x0
    // 0x915e34: r0 = setState()
    //     0x915e34: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x915e38: r0 = Null
    //     0x915e38: mov             x0, NULL
    // 0x915e3c: LeaveFrame
    //     0x915e3c: mov             SP, fp
    //     0x915e40: ldp             fp, lr, [SP], #0x10
    // 0x915e44: ret
    //     0x915e44: ret             
    // 0x915e48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x915e48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x915e4c: b               #0x915e00
  }
  [closure] void _onItemTapped(dynamic, int) {
    // ** addr: 0x915e50, size: 0x3c
    // 0x915e50: EnterFrame
    //     0x915e50: stp             fp, lr, [SP, #-0x10]!
    //     0x915e54: mov             fp, SP
    // 0x915e58: ldr             x0, [fp, #0x18]
    // 0x915e5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x915e5c: ldur            w1, [x0, #0x17]
    // 0x915e60: DecompressPointer r1
    //     0x915e60: add             x1, x1, HEAP, lsl #32
    // 0x915e64: CheckStackOverflow
    //     0x915e64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x915e68: cmp             SP, x16
    //     0x915e6c: b.ls            #0x915e84
    // 0x915e70: ldr             x2, [fp, #0x10]
    // 0x915e74: r0 = _onItemTapped()
    //     0x915e74: bl              #0x915de0  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x915e78: LeaveFrame
    //     0x915e78: mov             SP, fp
    //     0x915e7c: ldp             fp, lr, [SP], #0x10
    // 0x915e80: ret
    //     0x915e80: ret             
    // 0x915e84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x915e84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x915e88: b               #0x915e70
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x915e8c, size: 0x334
    // 0x915e8c: EnterFrame
    //     0x915e8c: stp             fp, lr, [SP, #-0x10]!
    //     0x915e90: mov             fp, SP
    // 0x915e94: AllocStack(0x38)
    //     0x915e94: sub             SP, SP, #0x38
    // 0x915e98: SetupParameters()
    //     0x915e98: ldr             x0, [fp, #0x10]
    //     0x915e9c: ldur            w2, [x0, #0x17]
    //     0x915ea0: add             x2, x2, HEAP, lsl #32
    //     0x915ea4: stur            x2, [fp, #-8]
    // 0x915ea8: CheckStackOverflow
    //     0x915ea8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x915eac: cmp             SP, x16
    //     0x915eb0: b.ls            #0x9161b0
    // 0x915eb4: LoadField: r0 = r2->field_f
    //     0x915eb4: ldur            w0, [x2, #0xf]
    // 0x915eb8: DecompressPointer r0
    //     0x915eb8: add             x0, x0, HEAP, lsl #32
    // 0x915ebc: LoadField: r1 = r2->field_13
    //     0x915ebc: ldur            w1, [x2, #0x13]
    // 0x915ec0: DecompressPointer r1
    //     0x915ec0: add             x1, x1, HEAP, lsl #32
    // 0x915ec4: r3 = LoadInt32Instr(r1)
    //     0x915ec4: sbfx            x3, x1, #1, #0x1f
    //     0x915ec8: tbz             w1, #0, #0x915ed0
    //     0x915ecc: ldur            x3, [x1, #7]
    // 0x915ed0: StoreField: r0->field_1b = r3
    //     0x915ed0: stur            x3, [x0, #0x1b]
    // 0x915ed4: cbnz            x3, #0x915fbc
    // 0x915ed8: r0 = InitLateStaticField(0xe78) // [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_pages
    //     0x915ed8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x915edc: ldr             x0, [x0, #0x1cf0]
    //     0x915ee0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x915ee4: cmp             w0, w16
    //     0x915ee8: b.ne            #0x915ef8
    //     0x915eec: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Field <_MainPageState@1712469706._pages@1712469706>: static late final (offset: 0xe78)
    //     0x915ef0: ldr             x2, [x2, #0xb08]
    //     0x915ef4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x915ef8: mov             x2, x0
    // 0x915efc: LoadField: r0 = r2->field_b
    //     0x915efc: ldur            w0, [x2, #0xb]
    // 0x915f00: r1 = LoadInt32Instr(r0)
    //     0x915f00: sbfx            x1, x0, #1, #0x1f
    // 0x915f04: mov             x0, x1
    // 0x915f08: r1 = 0
    //     0x915f08: movz            x1, #0
    // 0x915f0c: cmp             x1, x0
    // 0x915f10: b.hs            #0x9161b8
    // 0x915f14: LoadField: r0 = r2->field_f
    //     0x915f14: ldur            w0, [x2, #0xf]
    // 0x915f18: DecompressPointer r0
    //     0x915f18: add             x0, x0, HEAP, lsl #32
    // 0x915f1c: LoadField: r3 = r0->field_f
    //     0x915f1c: ldur            w3, [x0, #0xf]
    // 0x915f20: DecompressPointer r3
    //     0x915f20: add             x3, x3, HEAP, lsl #32
    // 0x915f24: mov             x0, x3
    // 0x915f28: stur            x3, [fp, #-0x10]
    // 0x915f2c: r2 = Null
    //     0x915f2c: mov             x2, NULL
    // 0x915f30: r1 = Null
    //     0x915f30: mov             x1, NULL
    // 0x915f34: r4 = 60
    //     0x915f34: movz            x4, #0x3c
    // 0x915f38: branchIfSmi(r0, 0x915f44)
    //     0x915f38: tbz             w0, #0, #0x915f44
    // 0x915f3c: r4 = LoadClassIdInstr(r0)
    //     0x915f3c: ldur            x4, [x0, #-1]
    //     0x915f40: ubfx            x4, x4, #0xc, #0x14
    // 0x915f44: r17 = 4533
    //     0x915f44: movz            x17, #0x11b5
    // 0x915f48: cmp             x4, x17
    // 0x915f4c: b.eq            #0x915f64
    // 0x915f50: r8 = HomePage
    //     0x915f50: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cbf8] Type: HomePage
    //     0x915f54: ldr             x8, [x8, #0xbf8]
    // 0x915f58: r3 = Null
    //     0x915f58: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cc00] Null
    //     0x915f5c: ldr             x3, [x3, #0xc00]
    // 0x915f60: r0 = DefaultTypeTest()
    //     0x915f60: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x915f64: r0 = LoadStaticField(0xcb0)
    //     0x915f64: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x915f68: ldr             x0, [x0, #0x1960]
    // 0x915f6c: cmp             w0, NULL
    // 0x915f70: b.ne            #0x915f88
    // 0x915f74: r0 = Instance_GetInstance
    //     0x915f74: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x915f78: ldr             x0, [x0, #0x900]
    // 0x915f7c: StoreStaticField(0xcb0, r0)
    //     0x915f7c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x915f80: str             x0, [x1, #0x1960]
    // 0x915f84: b               #0x915f90
    // 0x915f88: r0 = Instance_GetInstance
    //     0x915f88: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x915f8c: ldr             x0, [x0, #0x900]
    // 0x915f90: ldur            x1, [fp, #-0x10]
    // 0x915f94: LoadField: r2 = r1->field_b
    //     0x915f94: ldur            w2, [x1, #0xb]
    // 0x915f98: DecompressPointer r2
    //     0x915f98: add             x2, x2, HEAP, lsl #32
    // 0x915f9c: r16 = Instance_GetInstance
    //     0x915f9c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x915fa0: ldr             x16, [x16, #0x900]
    // 0x915fa4: stp             x16, x2, [SP, #8]
    // 0x915fa8: str             NULL, [SP]
    // 0x915fac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x915fac: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x915fb0: r0 = find()
    //     0x915fb0: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x915fb4: mov             x1, x0
    // 0x915fb8: r0 = onRefreshPage()
    //     0x915fb8: bl              #0x908a78  ; [package:customer_app/app/presentation/controllers/home/<USER>
    // 0x915fbc: ldur            x2, [fp, #-8]
    // 0x915fc0: LoadField: r0 = r2->field_13
    //     0x915fc0: ldur            w0, [x2, #0x13]
    // 0x915fc4: DecompressPointer r0
    //     0x915fc4: add             x0, x0, HEAP, lsl #32
    // 0x915fc8: r1 = LoadInt32Instr(r0)
    //     0x915fc8: sbfx            x1, x0, #1, #0x1f
    //     0x915fcc: tbz             w0, #0, #0x915fd4
    //     0x915fd0: ldur            x1, [x0, #7]
    // 0x915fd4: cmp             x1, #1
    // 0x915fd8: b.ne            #0x916154
    // 0x915fdc: r0 = InitLateStaticField(0xe78) // [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_pages
    //     0x915fdc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x915fe0: ldr             x0, [x0, #0x1cf0]
    //     0x915fe4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x915fe8: cmp             w0, w16
    //     0x915fec: b.ne            #0x915ffc
    //     0x915ff0: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Field <_MainPageState@1712469706._pages@1712469706>: static late final (offset: 0xe78)
    //     0x915ff4: ldr             x2, [x2, #0xb08]
    //     0x915ff8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x915ffc: mov             x2, x0
    // 0x916000: LoadField: r0 = r2->field_b
    //     0x916000: ldur            w0, [x2, #0xb]
    // 0x916004: r1 = LoadInt32Instr(r0)
    //     0x916004: sbfx            x1, x0, #1, #0x1f
    // 0x916008: mov             x0, x1
    // 0x91600c: r1 = 1
    //     0x91600c: movz            x1, #0x1
    // 0x916010: cmp             x1, x0
    // 0x916014: b.hs            #0x9161bc
    // 0x916018: LoadField: r0 = r2->field_f
    //     0x916018: ldur            w0, [x2, #0xf]
    // 0x91601c: DecompressPointer r0
    //     0x91601c: add             x0, x0, HEAP, lsl #32
    // 0x916020: LoadField: r3 = r0->field_13
    //     0x916020: ldur            w3, [x0, #0x13]
    // 0x916024: DecompressPointer r3
    //     0x916024: add             x3, x3, HEAP, lsl #32
    // 0x916028: mov             x0, x3
    // 0x91602c: stur            x3, [fp, #-0x10]
    // 0x916030: r2 = Null
    //     0x916030: mov             x2, NULL
    // 0x916034: r1 = Null
    //     0x916034: mov             x1, NULL
    // 0x916038: r4 = 60
    //     0x916038: movz            x4, #0x3c
    // 0x91603c: branchIfSmi(r0, 0x916048)
    //     0x91603c: tbz             w0, #0, #0x916048
    // 0x916040: r4 = LoadClassIdInstr(r0)
    //     0x916040: ldur            x4, [x0, #-1]
    //     0x916044: ubfx            x4, x4, #0xc, #0x14
    // 0x916048: r17 = 4530
    //     0x916048: movz            x17, #0x11b2
    // 0x91604c: cmp             x4, x17
    // 0x916050: b.eq            #0x916068
    // 0x916054: r8 = OrdersView
    //     0x916054: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0x916058: ldr             x8, [x8, #0xb48]
    // 0x91605c: r3 = Null
    //     0x91605c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cc10] Null
    //     0x916060: ldr             x3, [x3, #0xc10]
    // 0x916064: r0 = DefaultTypeTest()
    //     0x916064: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x916068: r0 = LoadStaticField(0xcb0)
    //     0x916068: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91606c: ldr             x0, [x0, #0x1960]
    // 0x916070: cmp             w0, NULL
    // 0x916074: b.ne            #0x916088
    // 0x916078: r0 = Instance_GetInstance
    //     0x916078: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x91607c: ldr             x0, [x0, #0x900]
    // 0x916080: StoreStaticField(0xcb0, r0)
    //     0x916080: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x916084: str             x0, [x1, #0x1960]
    // 0x916088: ldur            x2, [fp, #-8]
    // 0x91608c: ldur            x0, [fp, #-0x10]
    // 0x916090: LoadField: r1 = r0->field_b
    //     0x916090: ldur            w1, [x0, #0xb]
    // 0x916094: DecompressPointer r1
    //     0x916094: add             x1, x1, HEAP, lsl #32
    // 0x916098: r16 = Instance_GetInstance
    //     0x916098: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x91609c: ldr             x16, [x16, #0x900]
    // 0x9160a0: stp             x16, x1, [SP, #8]
    // 0x9160a4: str             NULL, [SP]
    // 0x9160a8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9160a8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9160ac: r0 = find()
    //     0x9160ac: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x9160b0: mov             x1, x0
    // 0x9160b4: r0 = getBagCount()
    //     0x9160b4: bl              #0x8a92ac  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getBagCount
    // 0x9160b8: ldur            x0, [fp, #-8]
    // 0x9160bc: LoadField: r1 = r0->field_f
    //     0x9160bc: ldur            w1, [x0, #0xf]
    // 0x9160c0: DecompressPointer r1
    //     0x9160c0: add             x1, x1, HEAP, lsl #32
    // 0x9160c4: LoadField: r2 = r1->field_3f
    //     0x9160c4: ldur            w2, [x1, #0x3f]
    // 0x9160c8: DecompressPointer r2
    //     0x9160c8: add             x2, x2, HEAP, lsl #32
    // 0x9160cc: LoadField: r1 = r2->field_4b
    //     0x9160cc: ldur            w1, [x2, #0x4b]
    // 0x9160d0: DecompressPointer r1
    //     0x9160d0: add             x1, x1, HEAP, lsl #32
    // 0x9160d4: r16 = ""
    //     0x9160d4: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x9160d8: str             x16, [SP]
    // 0x9160dc: r2 = "token"
    //     0x9160dc: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x9160e0: ldr             x2, [x2, #0x958]
    // 0x9160e4: r4 = const [0, 0x3, 0x1, 0x2, defaultValue, 0x2, null]
    //     0x9160e4: add             x4, PP, #0x11, lsl #12  ; [pp+0x11f48] List(7) [0, 0x3, 0x1, 0x2, "defaultValue", 0x2, Null]
    //     0x9160e8: ldr             x4, [x4, #0xf48]
    // 0x9160ec: r0 = getString()
    //     0x9160ec: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x9160f0: stur            x0, [fp, #-0x18]
    // 0x9160f4: LoadField: r3 = r0->field_7
    //     0x9160f4: ldur            w3, [x0, #7]
    // 0x9160f8: DecompressPointer r3
    //     0x9160f8: add             x3, x3, HEAP, lsl #32
    // 0x9160fc: ldur            x2, [fp, #-8]
    // 0x916100: stur            x3, [fp, #-0x10]
    // 0x916104: r1 = Function '<anonymous closure>':.
    //     0x916104: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cc20] AnonymousClosure: (0x9163bc), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped (0x915de0)
    //     0x916108: ldr             x1, [x1, #0xc20]
    // 0x91610c: r0 = AllocateClosure()
    //     0x91610c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x916110: ldur            x2, [fp, #-0x10]
    // 0x916114: mov             x3, x0
    // 0x916118: r1 = Null
    //     0x916118: mov             x1, NULL
    // 0x91611c: stur            x3, [fp, #-0x10]
    // 0x916120: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0x916120: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0x916124: ldr             x8, [x8, #0xce8]
    // 0x916128: LoadField: r9 = r8->field_7
    //     0x916128: ldur            x9, [x8, #7]
    // 0x91612c: r3 = Null
    //     0x91612c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cc28] Null
    //     0x916130: ldr             x3, [x3, #0xc28]
    // 0x916134: blr             x9
    // 0x916138: ldur            x16, [fp, #-0x18]
    // 0x91613c: stp             x16, NULL, [SP, #0x10]
    // 0x916140: ldur            x16, [fp, #-0x10]
    // 0x916144: stp             NULL, x16, [SP]
    // 0x916148: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0x916148: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0x91614c: r0 = then()
    //     0x91614c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x916150: b               #0x9161a0
    // 0x916154: cmp             x1, #2
    // 0x916158: b.ne            #0x9161a0
    // 0x91615c: ldur            x2, [fp, #-8]
    // 0x916160: LoadField: r0 = r2->field_f
    //     0x916160: ldur            w0, [x2, #0xf]
    // 0x916164: DecompressPointer r0
    //     0x916164: add             x0, x0, HEAP, lsl #32
    // 0x916168: LoadField: r1 = r0->field_4b
    //     0x916168: ldur            w1, [x0, #0x4b]
    // 0x91616c: DecompressPointer r1
    //     0x91616c: add             x1, x1, HEAP, lsl #32
    // 0x916170: r0 = getConnectivityType()
    //     0x916170: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x916174: ldur            x2, [fp, #-8]
    // 0x916178: r1 = Function '<anonymous closure>':.
    //     0x916178: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cc38] AnonymousClosure: (0x9161c0), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped (0x915de0)
    //     0x91617c: ldr             x1, [x1, #0xc38]
    // 0x916180: stur            x0, [fp, #-8]
    // 0x916184: r0 = AllocateClosure()
    //     0x916184: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x916188: r16 = <Null?>
    //     0x916188: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x91618c: ldur            lr, [fp, #-8]
    // 0x916190: stp             lr, x16, [SP, #8]
    // 0x916194: str             x0, [SP]
    // 0x916198: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x916198: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91619c: r0 = then()
    //     0x91619c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x9161a0: r0 = Null
    //     0x9161a0: mov             x0, NULL
    // 0x9161a4: LeaveFrame
    //     0x9161a4: mov             SP, fp
    //     0x9161a8: ldp             fp, lr, [SP], #0x10
    // 0x9161ac: ret
    //     0x9161ac: ret             
    // 0x9161b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9161b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9161b4: b               #0x915eb4
    // 0x9161b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9161b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9161bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9161bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x9161c0, size: 0x1fc
    // 0x9161c0: EnterFrame
    //     0x9161c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9161c4: mov             fp, SP
    // 0x9161c8: AllocStack(0x28)
    //     0x9161c8: sub             SP, SP, #0x28
    // 0x9161cc: SetupParameters()
    //     0x9161cc: ldr             x0, [fp, #0x18]
    //     0x9161d0: ldur            w1, [x0, #0x17]
    //     0x9161d4: add             x1, x1, HEAP, lsl #32
    // 0x9161d8: CheckStackOverflow
    //     0x9161d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9161dc: cmp             SP, x16
    //     0x9161e0: b.ls            #0x9163ac
    // 0x9161e4: LoadField: r0 = r1->field_f
    //     0x9161e4: ldur            w0, [x1, #0xf]
    // 0x9161e8: DecompressPointer r0
    //     0x9161e8: add             x0, x0, HEAP, lsl #32
    // 0x9161ec: ldr             x1, [fp, #0x10]
    // 0x9161f0: StoreField: r0->field_4f = r1
    //     0x9161f0: stur            w1, [x0, #0x4f]
    // 0x9161f4: tbnz            w1, #4, #0x91639c
    // 0x9161f8: r0 = InitLateStaticField(0xe78) // [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_pages
    //     0x9161f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9161fc: ldr             x0, [x0, #0x1cf0]
    //     0x916200: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x916204: cmp             w0, w16
    //     0x916208: b.ne            #0x916218
    //     0x91620c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Field <_MainPageState@1712469706._pages@1712469706>: static late final (offset: 0xe78)
    //     0x916210: ldr             x2, [x2, #0xb08]
    //     0x916214: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x916218: mov             x3, x0
    // 0x91621c: stur            x3, [fp, #-0x10]
    // 0x916220: LoadField: r0 = r3->field_b
    //     0x916220: ldur            w0, [x3, #0xb]
    // 0x916224: r1 = LoadInt32Instr(r0)
    //     0x916224: sbfx            x1, x0, #1, #0x1f
    // 0x916228: mov             x0, x1
    // 0x91622c: r1 = 2
    //     0x91622c: movz            x1, #0x2
    // 0x916230: cmp             x1, x0
    // 0x916234: b.hs            #0x9163b4
    // 0x916238: LoadField: r0 = r3->field_f
    //     0x916238: ldur            w0, [x3, #0xf]
    // 0x91623c: DecompressPointer r0
    //     0x91623c: add             x0, x0, HEAP, lsl #32
    // 0x916240: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x916240: ldur            w4, [x0, #0x17]
    // 0x916244: DecompressPointer r4
    //     0x916244: add             x4, x4, HEAP, lsl #32
    // 0x916248: mov             x0, x4
    // 0x91624c: stur            x4, [fp, #-8]
    // 0x916250: r2 = Null
    //     0x916250: mov             x2, NULL
    // 0x916254: r1 = Null
    //     0x916254: mov             x1, NULL
    // 0x916258: r4 = 60
    //     0x916258: movz            x4, #0x3c
    // 0x91625c: branchIfSmi(r0, 0x916268)
    //     0x91625c: tbz             w0, #0, #0x916268
    // 0x916260: r4 = LoadClassIdInstr(r0)
    //     0x916260: ldur            x4, [x0, #-1]
    //     0x916264: ubfx            x4, x4, #0xc, #0x14
    // 0x916268: r17 = 4520
    //     0x916268: movz            x17, #0x11a8
    // 0x91626c: cmp             x4, x17
    // 0x916270: b.eq            #0x916288
    // 0x916274: r8 = ProfileView
    //     0x916274: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cc40] Type: ProfileView
    //     0x916278: ldr             x8, [x8, #0xc40]
    // 0x91627c: r3 = Null
    //     0x91627c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cc48] Null
    //     0x916280: ldr             x3, [x3, #0xc48]
    // 0x916284: r0 = DefaultTypeTest()
    //     0x916284: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x916288: r0 = LoadStaticField(0xcb0)
    //     0x916288: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91628c: ldr             x0, [x0, #0x1960]
    // 0x916290: cmp             w0, NULL
    // 0x916294: b.ne            #0x9162ac
    // 0x916298: r0 = Instance_GetInstance
    //     0x916298: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x91629c: ldr             x0, [x0, #0x900]
    // 0x9162a0: StoreStaticField(0xcb0, r0)
    //     0x9162a0: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x9162a4: str             x0, [x1, #0x1960]
    // 0x9162a8: b               #0x9162b4
    // 0x9162ac: r0 = Instance_GetInstance
    //     0x9162ac: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x9162b0: ldr             x0, [x0, #0x900]
    // 0x9162b4: ldur            x1, [fp, #-0x10]
    // 0x9162b8: ldur            x2, [fp, #-8]
    // 0x9162bc: LoadField: r3 = r2->field_b
    //     0x9162bc: ldur            w3, [x2, #0xb]
    // 0x9162c0: DecompressPointer r3
    //     0x9162c0: add             x3, x3, HEAP, lsl #32
    // 0x9162c4: r16 = Instance_GetInstance
    //     0x9162c4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x9162c8: ldr             x16, [x16, #0x900]
    // 0x9162cc: stp             x16, x3, [SP, #8]
    // 0x9162d0: str             NULL, [SP]
    // 0x9162d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9162d4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9162d8: r0 = find()
    //     0x9162d8: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x9162dc: mov             x1, x0
    // 0x9162e0: r0 = checkLoginStatus()
    //     0x9162e0: bl              #0x913d8c  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::checkLoginStatus
    // 0x9162e4: ldur            x2, [fp, #-0x10]
    // 0x9162e8: LoadField: r0 = r2->field_b
    //     0x9162e8: ldur            w0, [x2, #0xb]
    // 0x9162ec: r1 = LoadInt32Instr(r0)
    //     0x9162ec: sbfx            x1, x0, #1, #0x1f
    // 0x9162f0: mov             x0, x1
    // 0x9162f4: r1 = 2
    //     0x9162f4: movz            x1, #0x2
    // 0x9162f8: cmp             x1, x0
    // 0x9162fc: b.hs            #0x9163b8
    // 0x916300: LoadField: r0 = r2->field_f
    //     0x916300: ldur            w0, [x2, #0xf]
    // 0x916304: DecompressPointer r0
    //     0x916304: add             x0, x0, HEAP, lsl #32
    // 0x916308: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x916308: ldur            w3, [x0, #0x17]
    // 0x91630c: DecompressPointer r3
    //     0x91630c: add             x3, x3, HEAP, lsl #32
    // 0x916310: mov             x0, x3
    // 0x916314: stur            x3, [fp, #-8]
    // 0x916318: r2 = Null
    //     0x916318: mov             x2, NULL
    // 0x91631c: r1 = Null
    //     0x91631c: mov             x1, NULL
    // 0x916320: r4 = 60
    //     0x916320: movz            x4, #0x3c
    // 0x916324: branchIfSmi(r0, 0x916330)
    //     0x916324: tbz             w0, #0, #0x916330
    // 0x916328: r4 = LoadClassIdInstr(r0)
    //     0x916328: ldur            x4, [x0, #-1]
    //     0x91632c: ubfx            x4, x4, #0xc, #0x14
    // 0x916330: r17 = 4520
    //     0x916330: movz            x17, #0x11a8
    // 0x916334: cmp             x4, x17
    // 0x916338: b.eq            #0x916350
    // 0x91633c: r8 = ProfileView
    //     0x91633c: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cc40] Type: ProfileView
    //     0x916340: ldr             x8, [x8, #0xc40]
    // 0x916344: r3 = Null
    //     0x916344: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cc58] Null
    //     0x916348: ldr             x3, [x3, #0xc58]
    // 0x91634c: r0 = DefaultTypeTest()
    //     0x91634c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x916350: r0 = LoadStaticField(0xcb0)
    //     0x916350: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x916354: ldr             x0, [x0, #0x1960]
    // 0x916358: cmp             w0, NULL
    // 0x91635c: b.ne            #0x916370
    // 0x916360: r0 = Instance_GetInstance
    //     0x916360: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x916364: ldr             x0, [x0, #0x900]
    // 0x916368: StoreStaticField(0xcb0, r0)
    //     0x916368: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x91636c: str             x0, [x1, #0x1960]
    // 0x916370: ldur            x0, [fp, #-8]
    // 0x916374: LoadField: r1 = r0->field_b
    //     0x916374: ldur            w1, [x0, #0xb]
    // 0x916378: DecompressPointer r1
    //     0x916378: add             x1, x1, HEAP, lsl #32
    // 0x91637c: r16 = Instance_GetInstance
    //     0x91637c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x916380: ldr             x16, [x16, #0x900]
    // 0x916384: stp             x16, x1, [SP, #8]
    // 0x916388: str             NULL, [SP]
    // 0x91638c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91638c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x916390: r0 = find()
    //     0x916390: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x916394: mov             x1, x0
    // 0x916398: r0 = getBagCount()
    //     0x916398: bl              #0x9134f0  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::getBagCount
    // 0x91639c: r0 = Null
    //     0x91639c: mov             x0, NULL
    // 0x9163a0: LeaveFrame
    //     0x9163a0: mov             SP, fp
    //     0x9163a4: ldp             fp, lr, [SP], #0x10
    // 0x9163a8: ret
    //     0x9163a8: ret             
    // 0x9163ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9163ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9163b0: b               #0x9161e4
    // 0x9163b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9163b4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9163b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9163b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Set<Set<dynamic>> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x9163bc, size: 0x1c8
    // 0x9163bc: EnterFrame
    //     0x9163bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9163c0: mov             fp, SP
    // 0x9163c4: AllocStack(0x30)
    //     0x9163c4: sub             SP, SP, #0x30
    // 0x9163c8: SetupParameters()
    //     0x9163c8: ldr             x0, [fp, #0x18]
    //     0x9163cc: ldur            w2, [x0, #0x17]
    //     0x9163d0: add             x2, x2, HEAP, lsl #32
    //     0x9163d4: stur            x2, [fp, #-8]
    // 0x9163d8: CheckStackOverflow
    //     0x9163d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9163dc: cmp             SP, x16
    //     0x9163e0: b.ls            #0x91657c
    // 0x9163e4: r1 = <Set>
    //     0x9163e4: add             x1, PP, #0x24, lsl #12  ; [pp+0x24550] TypeArguments: <Set>
    //     0x9163e8: ldr             x1, [x1, #0x550]
    // 0x9163ec: r0 = _Set()
    //     0x9163ec: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x9163f0: mov             x1, x0
    // 0x9163f4: r0 = _Uint32List
    //     0x9163f4: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x9163f8: stur            x1, [fp, #-0x10]
    // 0x9163fc: StoreField: r1->field_1b = r0
    //     0x9163fc: stur            w0, [x1, #0x1b]
    // 0x916400: StoreField: r1->field_b = rZR
    //     0x916400: stur            wzr, [x1, #0xb]
    // 0x916404: r2 = const []
    //     0x916404: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x916408: StoreField: r1->field_f = r2
    //     0x916408: stur            w2, [x1, #0xf]
    // 0x91640c: StoreField: r1->field_13 = rZR
    //     0x91640c: stur            wzr, [x1, #0x13]
    // 0x916410: ArrayStore: r1[0] = rZR  ; List_4
    //     0x916410: stur            wzr, [x1, #0x17]
    // 0x916414: ldr             x16, [fp, #0x10]
    // 0x916418: str             x16, [SP]
    // 0x91641c: r4 = 0
    //     0x91641c: movz            x4, #0
    // 0x916420: ldr             x0, [SP]
    // 0x916424: r16 = UnlinkedCall_0x613b5c
    //     0x916424: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cc68] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x916428: add             x16, x16, #0xc68
    // 0x91642c: ldp             x5, lr, [x16]
    // 0x916430: blr             lr
    // 0x916434: mov             x3, x0
    // 0x916438: r2 = Null
    //     0x916438: mov             x2, NULL
    // 0x91643c: r1 = Null
    //     0x91643c: mov             x1, NULL
    // 0x916440: stur            x3, [fp, #-0x18]
    // 0x916444: r4 = 60
    //     0x916444: movz            x4, #0x3c
    // 0x916448: branchIfSmi(r0, 0x916454)
    //     0x916448: tbz             w0, #0, #0x916454
    // 0x91644c: r4 = LoadClassIdInstr(r0)
    //     0x91644c: ldur            x4, [x0, #-1]
    //     0x916450: ubfx            x4, x4, #0xc, #0x14
    // 0x916454: cmp             x4, #0x3f
    // 0x916458: b.eq            #0x91646c
    // 0x91645c: r8 = bool
    //     0x91645c: ldr             x8, [PP, #0x25f0]  ; [pp+0x25f0] Type: bool
    // 0x916460: r3 = Null
    //     0x916460: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cc78] Null
    //     0x916464: ldr             x3, [x3, #0xc78]
    // 0x916468: r0 = bool()
    //     0x916468: bl              #0x16fbdf8  ; IsType_bool_Stub
    // 0x91646c: ldur            x0, [fp, #-0x18]
    // 0x916470: tbnz            w0, #4, #0x9164d0
    // 0x916474: ldur            x2, [fp, #-8]
    // 0x916478: r1 = Null
    //     0x916478: mov             x1, NULL
    // 0x91647c: r0 = _Set()
    //     0x91647c: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x916480: mov             x2, x0
    // 0x916484: r0 = _Uint32List
    //     0x916484: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x916488: stur            x2, [fp, #-0x18]
    // 0x91648c: StoreField: r2->field_1b = r0
    //     0x91648c: stur            w0, [x2, #0x1b]
    // 0x916490: StoreField: r2->field_b = rZR
    //     0x916490: stur            wzr, [x2, #0xb]
    // 0x916494: r3 = const []
    //     0x916494: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x916498: StoreField: r2->field_f = r3
    //     0x916498: stur            w3, [x2, #0xf]
    // 0x91649c: StoreField: r2->field_13 = rZR
    //     0x91649c: stur            wzr, [x2, #0x13]
    // 0x9164a0: ArrayStore: r2[0] = rZR  ; List_4
    //     0x9164a0: stur            wzr, [x2, #0x17]
    // 0x9164a4: ldur            x4, [fp, #-8]
    // 0x9164a8: LoadField: r1 = r4->field_f
    //     0x9164a8: ldur            w1, [x4, #0xf]
    // 0x9164ac: DecompressPointer r1
    //     0x9164ac: add             x1, x1, HEAP, lsl #32
    // 0x9164b0: r0 = openLoginAwait()
    //     0x9164b0: bl              #0x916584  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openLoginAwait
    // 0x9164b4: ldur            x1, [fp, #-0x18]
    // 0x9164b8: mov             x2, x0
    // 0x9164bc: r0 = add()
    //     0x9164bc: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x9164c0: ldur            x1, [fp, #-0x10]
    // 0x9164c4: ldur            x2, [fp, #-0x18]
    // 0x9164c8: r0 = add()
    //     0x9164c8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x9164cc: b               #0x91656c
    // 0x9164d0: ldur            x4, [fp, #-8]
    // 0x9164d4: r0 = _Uint32List
    //     0x9164d4: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x9164d8: r3 = const []
    //     0x9164d8: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x9164dc: r1 = <Future<Null?>>
    //     0x9164dc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cc88] TypeArguments: <Future<Null?>>
    //     0x9164e0: ldr             x1, [x1, #0xc88]
    // 0x9164e4: r0 = _Set()
    //     0x9164e4: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x9164e8: mov             x2, x0
    // 0x9164ec: r0 = _Uint32List
    //     0x9164ec: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x9164f0: stur            x2, [fp, #-0x18]
    // 0x9164f4: StoreField: r2->field_1b = r0
    //     0x9164f4: stur            w0, [x2, #0x1b]
    // 0x9164f8: StoreField: r2->field_b = rZR
    //     0x9164f8: stur            wzr, [x2, #0xb]
    // 0x9164fc: r0 = const []
    //     0x9164fc: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x916500: StoreField: r2->field_f = r0
    //     0x916500: stur            w0, [x2, #0xf]
    // 0x916504: StoreField: r2->field_13 = rZR
    //     0x916504: stur            wzr, [x2, #0x13]
    // 0x916508: ArrayStore: r2[0] = rZR  ; List_4
    //     0x916508: stur            wzr, [x2, #0x17]
    // 0x91650c: ldur            x0, [fp, #-8]
    // 0x916510: LoadField: r1 = r0->field_f
    //     0x916510: ldur            w1, [x0, #0xf]
    // 0x916514: DecompressPointer r1
    //     0x916514: add             x1, x1, HEAP, lsl #32
    // 0x916518: LoadField: r3 = r1->field_4b
    //     0x916518: ldur            w3, [x1, #0x4b]
    // 0x91651c: DecompressPointer r3
    //     0x91651c: add             x3, x3, HEAP, lsl #32
    // 0x916520: mov             x1, x3
    // 0x916524: r0 = getConnectivityType()
    //     0x916524: bl              #0x8a43bc  ; [package:customer_app/app/network/connection_controller.dart] ConnectionController::getConnectivityType
    // 0x916528: ldur            x2, [fp, #-8]
    // 0x91652c: r1 = Function '<anonymous closure>':.
    //     0x91652c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cc90] AnonymousClosure: (0x916698), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped (0x915de0)
    //     0x916530: ldr             x1, [x1, #0xc90]
    // 0x916534: stur            x0, [fp, #-8]
    // 0x916538: r0 = AllocateClosure()
    //     0x916538: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x91653c: r16 = <Null?>
    //     0x91653c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x916540: ldur            lr, [fp, #-8]
    // 0x916544: stp             lr, x16, [SP, #8]
    // 0x916548: str             x0, [SP]
    // 0x91654c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91654c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x916550: r0 = then()
    //     0x916550: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x916554: ldur            x1, [fp, #-0x18]
    // 0x916558: mov             x2, x0
    // 0x91655c: r0 = add()
    //     0x91655c: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x916560: ldur            x1, [fp, #-0x10]
    // 0x916564: ldur            x2, [fp, #-0x18]
    // 0x916568: r0 = add()
    //     0x916568: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x91656c: ldur            x0, [fp, #-0x10]
    // 0x916570: LeaveFrame
    //     0x916570: mov             SP, fp
    //     0x916574: ldp             fp, lr, [SP], #0x10
    // 0x916578: ret
    //     0x916578: ret             
    // 0x91657c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91657c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x916580: b               #0x9163e4
  }
  _ openLoginAwait(/* No info */) async {
    // ** addr: 0x916584, size: 0x114
    // 0x916584: EnterFrame
    //     0x916584: stp             fp, lr, [SP, #-0x10]!
    //     0x916588: mov             fp, SP
    // 0x91658c: AllocStack(0x28)
    //     0x91658c: sub             SP, SP, #0x28
    // 0x916590: SetupParameters(_MainPageState this /* r1 => r1, fp-0x10 */)
    //     0x916590: stur            NULL, [fp, #-8]
    //     0x916594: stur            x1, [fp, #-0x10]
    // 0x916598: CheckStackOverflow
    //     0x916598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91659c: cmp             SP, x16
    //     0x9165a0: b.ls            #0x91668c
    // 0x9165a4: InitAsync() -> Future
    //     0x9165a4: mov             x0, NULL
    //     0x9165a8: bl              #0x6326e0  ; InitAsyncStub
    // 0x9165ac: r0 = InitLateStaticField(0xe78) // [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_pages
    //     0x9165ac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9165b0: ldr             x0, [x0, #0x1cf0]
    //     0x9165b4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9165b8: cmp             w0, w16
    //     0x9165bc: b.ne            #0x9165cc
    //     0x9165c0: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Field <_MainPageState@1712469706._pages@1712469706>: static late final (offset: 0xe78)
    //     0x9165c4: ldr             x2, [x2, #0xb08]
    //     0x9165c8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9165cc: mov             x2, x0
    // 0x9165d0: LoadField: r0 = r2->field_b
    //     0x9165d0: ldur            w0, [x2, #0xb]
    // 0x9165d4: r1 = LoadInt32Instr(r0)
    //     0x9165d4: sbfx            x1, x0, #1, #0x1f
    // 0x9165d8: mov             x0, x1
    // 0x9165dc: r1 = 1
    //     0x9165dc: movz            x1, #0x1
    // 0x9165e0: cmp             x1, x0
    // 0x9165e4: b.hs            #0x916694
    // 0x9165e8: LoadField: r0 = r2->field_f
    //     0x9165e8: ldur            w0, [x2, #0xf]
    // 0x9165ec: DecompressPointer r0
    //     0x9165ec: add             x0, x0, HEAP, lsl #32
    // 0x9165f0: LoadField: r3 = r0->field_13
    //     0x9165f0: ldur            w3, [x0, #0x13]
    // 0x9165f4: DecompressPointer r3
    //     0x9165f4: add             x3, x3, HEAP, lsl #32
    // 0x9165f8: mov             x0, x3
    // 0x9165fc: stur            x3, [fp, #-0x10]
    // 0x916600: r2 = Null
    //     0x916600: mov             x2, NULL
    // 0x916604: r1 = Null
    //     0x916604: mov             x1, NULL
    // 0x916608: r4 = 60
    //     0x916608: movz            x4, #0x3c
    // 0x91660c: branchIfSmi(r0, 0x916618)
    //     0x91660c: tbz             w0, #0, #0x916618
    // 0x916610: r4 = LoadClassIdInstr(r0)
    //     0x916610: ldur            x4, [x0, #-1]
    //     0x916614: ubfx            x4, x4, #0xc, #0x14
    // 0x916618: r17 = 4530
    //     0x916618: movz            x17, #0x11b2
    // 0x91661c: cmp             x4, x17
    // 0x916620: b.eq            #0x916638
    // 0x916624: r8 = OrdersView
    //     0x916624: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0x916628: ldr             x8, [x8, #0xb48]
    // 0x91662c: r3 = Null
    //     0x91662c: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d700] Null
    //     0x916630: ldr             x3, [x3, #0x700]
    // 0x916634: r0 = DefaultTypeTest()
    //     0x916634: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x916638: r0 = LoadStaticField(0xcb0)
    //     0x916638: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91663c: ldr             x0, [x0, #0x1960]
    // 0x916640: cmp             w0, NULL
    // 0x916644: b.ne            #0x916658
    // 0x916648: r0 = Instance_GetInstance
    //     0x916648: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x91664c: ldr             x0, [x0, #0x900]
    // 0x916650: StoreStaticField(0xcb0, r0)
    //     0x916650: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x916654: str             x0, [x1, #0x1960]
    // 0x916658: ldur            x0, [fp, #-0x10]
    // 0x91665c: LoadField: r1 = r0->field_b
    //     0x91665c: ldur            w1, [x0, #0xb]
    // 0x916660: DecompressPointer r1
    //     0x916660: add             x1, x1, HEAP, lsl #32
    // 0x916664: r16 = Instance_GetInstance
    //     0x916664: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x916668: ldr             x16, [x16, #0x900]
    // 0x91666c: stp             x16, x1, [SP, #8]
    // 0x916670: str             NULL, [SP]
    // 0x916674: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x916674: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x916678: r0 = find()
    //     0x916678: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x91667c: mov             x1, x0
    // 0x916680: r0 = openLoginAwait()
    //     0x916680: bl              #0x8a55a0  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::openLoginAwait
    // 0x916684: r0 = Null
    //     0x916684: mov             x0, NULL
    // 0x916688: r0 = ReturnAsyncNotFuture()
    //     0x916688: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x91668c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91668c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x916690: b               #0x9165a4
    // 0x916694: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x916694: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, bool) {
    // ** addr: 0x916698, size: 0x200
    // 0x916698: EnterFrame
    //     0x916698: stp             fp, lr, [SP, #-0x10]!
    //     0x91669c: mov             fp, SP
    // 0x9166a0: AllocStack(0x28)
    //     0x9166a0: sub             SP, SP, #0x28
    // 0x9166a4: SetupParameters()
    //     0x9166a4: ldr             x0, [fp, #0x18]
    //     0x9166a8: ldur            w1, [x0, #0x17]
    //     0x9166ac: add             x1, x1, HEAP, lsl #32
    // 0x9166b0: CheckStackOverflow
    //     0x9166b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9166b4: cmp             SP, x16
    //     0x9166b8: b.ls            #0x916888
    // 0x9166bc: LoadField: r0 = r1->field_f
    //     0x9166bc: ldur            w0, [x1, #0xf]
    // 0x9166c0: DecompressPointer r0
    //     0x9166c0: add             x0, x0, HEAP, lsl #32
    // 0x9166c4: ldr             x1, [fp, #0x10]
    // 0x9166c8: StoreField: r0->field_4f = r1
    //     0x9166c8: stur            w1, [x0, #0x4f]
    // 0x9166cc: tbnz            w1, #4, #0x916878
    // 0x9166d0: r0 = InitLateStaticField(0xe78) // [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_pages
    //     0x9166d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9166d4: ldr             x0, [x0, #0x1cf0]
    //     0x9166d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9166dc: cmp             w0, w16
    //     0x9166e0: b.ne            #0x9166f0
    //     0x9166e4: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Field <_MainPageState@1712469706._pages@1712469706>: static late final (offset: 0xe78)
    //     0x9166e8: ldr             x2, [x2, #0xb08]
    //     0x9166ec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x9166f0: mov             x3, x0
    // 0x9166f4: stur            x3, [fp, #-0x10]
    // 0x9166f8: LoadField: r0 = r3->field_b
    //     0x9166f8: ldur            w0, [x3, #0xb]
    // 0x9166fc: r1 = LoadInt32Instr(r0)
    //     0x9166fc: sbfx            x1, x0, #1, #0x1f
    // 0x916700: mov             x0, x1
    // 0x916704: r1 = 1
    //     0x916704: movz            x1, #0x1
    // 0x916708: cmp             x1, x0
    // 0x91670c: b.hs            #0x916890
    // 0x916710: LoadField: r0 = r3->field_f
    //     0x916710: ldur            w0, [x3, #0xf]
    // 0x916714: DecompressPointer r0
    //     0x916714: add             x0, x0, HEAP, lsl #32
    // 0x916718: LoadField: r4 = r0->field_13
    //     0x916718: ldur            w4, [x0, #0x13]
    // 0x91671c: DecompressPointer r4
    //     0x91671c: add             x4, x4, HEAP, lsl #32
    // 0x916720: mov             x0, x4
    // 0x916724: stur            x4, [fp, #-8]
    // 0x916728: r2 = Null
    //     0x916728: mov             x2, NULL
    // 0x91672c: r1 = Null
    //     0x91672c: mov             x1, NULL
    // 0x916730: r4 = 60
    //     0x916730: movz            x4, #0x3c
    // 0x916734: branchIfSmi(r0, 0x916740)
    //     0x916734: tbz             w0, #0, #0x916740
    // 0x916738: r4 = LoadClassIdInstr(r0)
    //     0x916738: ldur            x4, [x0, #-1]
    //     0x91673c: ubfx            x4, x4, #0xc, #0x14
    // 0x916740: r17 = 4530
    //     0x916740: movz            x17, #0x11b2
    // 0x916744: cmp             x4, x17
    // 0x916748: b.eq            #0x916760
    // 0x91674c: r8 = OrdersView
    //     0x91674c: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0x916750: ldr             x8, [x8, #0xb48]
    // 0x916754: r3 = Null
    //     0x916754: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cc98] Null
    //     0x916758: ldr             x3, [x3, #0xc98]
    // 0x91675c: r0 = DefaultTypeTest()
    //     0x91675c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x916760: r0 = LoadStaticField(0xcb0)
    //     0x916760: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x916764: ldr             x0, [x0, #0x1960]
    // 0x916768: cmp             w0, NULL
    // 0x91676c: b.ne            #0x916784
    // 0x916770: r0 = Instance_GetInstance
    //     0x916770: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x916774: ldr             x0, [x0, #0x900]
    // 0x916778: StoreStaticField(0xcb0, r0)
    //     0x916778: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x91677c: str             x0, [x1, #0x1960]
    // 0x916780: b               #0x91678c
    // 0x916784: r0 = Instance_GetInstance
    //     0x916784: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x916788: ldr             x0, [x0, #0x900]
    // 0x91678c: ldur            x1, [fp, #-0x10]
    // 0x916790: ldur            x2, [fp, #-8]
    // 0x916794: LoadField: r3 = r2->field_b
    //     0x916794: ldur            w3, [x2, #0xb]
    // 0x916798: DecompressPointer r3
    //     0x916798: add             x3, x3, HEAP, lsl #32
    // 0x91679c: r16 = Instance_GetInstance
    //     0x91679c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x9167a0: ldr             x16, [x16, #0x900]
    // 0x9167a4: stp             x16, x3, [SP, #8]
    // 0x9167a8: str             NULL, [SP]
    // 0x9167ac: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9167ac: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9167b0: r0 = find()
    //     0x9167b0: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x9167b4: LoadField: r1 = r0->field_4f
    //     0x9167b4: ldur            w1, [x0, #0x4f]
    // 0x9167b8: DecompressPointer r1
    //     0x9167b8: add             x1, x1, HEAP, lsl #32
    // 0x9167bc: r0 = initRefresh()
    //     0x9167bc: bl              #0x8aa040  ; [package:customer_app/app/core/base/paging_controller.dart] PagingController::initRefresh
    // 0x9167c0: ldur            x2, [fp, #-0x10]
    // 0x9167c4: LoadField: r0 = r2->field_b
    //     0x9167c4: ldur            w0, [x2, #0xb]
    // 0x9167c8: r1 = LoadInt32Instr(r0)
    //     0x9167c8: sbfx            x1, x0, #1, #0x1f
    // 0x9167cc: mov             x0, x1
    // 0x9167d0: r1 = 1
    //     0x9167d0: movz            x1, #0x1
    // 0x9167d4: cmp             x1, x0
    // 0x9167d8: b.hs            #0x916894
    // 0x9167dc: LoadField: r0 = r2->field_f
    //     0x9167dc: ldur            w0, [x2, #0xf]
    // 0x9167e0: DecompressPointer r0
    //     0x9167e0: add             x0, x0, HEAP, lsl #32
    // 0x9167e4: LoadField: r3 = r0->field_13
    //     0x9167e4: ldur            w3, [x0, #0x13]
    // 0x9167e8: DecompressPointer r3
    //     0x9167e8: add             x3, x3, HEAP, lsl #32
    // 0x9167ec: mov             x0, x3
    // 0x9167f0: stur            x3, [fp, #-8]
    // 0x9167f4: r2 = Null
    //     0x9167f4: mov             x2, NULL
    // 0x9167f8: r1 = Null
    //     0x9167f8: mov             x1, NULL
    // 0x9167fc: r4 = 60
    //     0x9167fc: movz            x4, #0x3c
    // 0x916800: branchIfSmi(r0, 0x91680c)
    //     0x916800: tbz             w0, #0, #0x91680c
    // 0x916804: r4 = LoadClassIdInstr(r0)
    //     0x916804: ldur            x4, [x0, #-1]
    //     0x916808: ubfx            x4, x4, #0xc, #0x14
    // 0x91680c: r17 = 4530
    //     0x91680c: movz            x17, #0x11b2
    // 0x916810: cmp             x4, x17
    // 0x916814: b.eq            #0x91682c
    // 0x916818: r8 = OrdersView
    //     0x916818: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0x91681c: ldr             x8, [x8, #0xb48]
    // 0x916820: r3 = Null
    //     0x916820: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cca8] Null
    //     0x916824: ldr             x3, [x3, #0xca8]
    // 0x916828: r0 = DefaultTypeTest()
    //     0x916828: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x91682c: r0 = LoadStaticField(0xcb0)
    //     0x91682c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x916830: ldr             x0, [x0, #0x1960]
    // 0x916834: cmp             w0, NULL
    // 0x916838: b.ne            #0x91684c
    // 0x91683c: r0 = Instance_GetInstance
    //     0x91683c: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x916840: ldr             x0, [x0, #0x900]
    // 0x916844: StoreStaticField(0xcb0, r0)
    //     0x916844: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x916848: str             x0, [x1, #0x1960]
    // 0x91684c: ldur            x0, [fp, #-8]
    // 0x916850: LoadField: r1 = r0->field_b
    //     0x916850: ldur            w1, [x0, #0xb]
    // 0x916854: DecompressPointer r1
    //     0x916854: add             x1, x1, HEAP, lsl #32
    // 0x916858: r16 = Instance_GetInstance
    //     0x916858: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x91685c: ldr             x16, [x16, #0x900]
    // 0x916860: stp             x16, x1, [SP, #8]
    // 0x916864: str             NULL, [SP]
    // 0x916868: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x916868: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91686c: r0 = find()
    //     0x91686c: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x916870: mov             x1, x0
    // 0x916874: r0 = getOrders()
    //     0x916874: bl              #0x8a5968  ; [package:customer_app/app/presentation/controllers/orders/orders_controller.dart] OrdersController::getOrders
    // 0x916878: r0 = Null
    //     0x916878: mov             x0, NULL
    // 0x91687c: LeaveFrame
    //     0x91687c: mov             SP, fp
    //     0x916880: ldp             fp, lr, [SP], #0x10
    // 0x916884: ret
    //     0x916884: ret             
    // 0x916888: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x916888: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91688c: b               #0x9166bc
    // 0x916890: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x916890: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x916894: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x916894: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  static List<Widget> _pages() {
    // ** addr: 0x916898, size: 0x74
    // 0x916898: EnterFrame
    //     0x916898: stp             fp, lr, [SP, #-0x10]!
    //     0x91689c: mov             fp, SP
    // 0x9168a0: AllocStack(0x8)
    //     0x9168a0: sub             SP, SP, #8
    // 0x9168a4: r0 = 8
    //     0x9168a4: movz            x0, #0x8
    // 0x9168a8: mov             x2, x0
    // 0x9168ac: r1 = Null
    //     0x9168ac: mov             x1, NULL
    // 0x9168b0: r0 = AllocateArray()
    //     0x9168b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x9168b4: stur            x0, [fp, #-8]
    // 0x9168b8: r16 = Instance_HomePage
    //     0x9168b8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe130] Obj!HomePage@d66f81
    //     0x9168bc: ldr             x16, [x16, #0x130]
    // 0x9168c0: StoreField: r0->field_f = r16
    //     0x9168c0: stur            w16, [x0, #0xf]
    // 0x9168c4: r16 = Instance_OrdersView
    //     0x9168c4: add             x16, PP, #0xe, lsl #12  ; [pp+0xe108] Obj!OrdersView@d66f41
    //     0x9168c8: ldr             x16, [x16, #0x108]
    // 0x9168cc: StoreField: r0->field_13 = r16
    //     0x9168cc: stur            w16, [x0, #0x13]
    // 0x9168d0: r16 = Instance_ProfileView
    //     0x9168d0: add             x16, PP, #0xe, lsl #12  ; [pp+0xe110] Obj!ProfileView@d66e21
    //     0x9168d4: ldr             x16, [x16, #0x110]
    // 0x9168d8: ArrayStore: r0[0] = r16  ; List_4
    //     0x9168d8: stur            w16, [x0, #0x17]
    // 0x9168dc: r16 = Instance_BrowseView
    //     0x9168dc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe0e0] Obj!BrowseView@d67121
    //     0x9168e0: ldr             x16, [x16, #0xe0]
    // 0x9168e4: StoreField: r0->field_1b = r16
    //     0x9168e4: stur            w16, [x0, #0x1b]
    // 0x9168e8: r1 = <Widget>
    //     0x9168e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x9168ec: r0 = AllocateGrowableArray()
    //     0x9168ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x9168f0: ldur            x1, [fp, #-8]
    // 0x9168f4: StoreField: r0->field_f = r1
    //     0x9168f4: stur            w1, [x0, #0xf]
    // 0x9168f8: r1 = 8
    //     0x9168f8: movz            x1, #0x8
    // 0x9168fc: StoreField: r0->field_b = r1
    //     0x9168fc: stur            w1, [x0, #0xb]
    // 0x916900: LeaveFrame
    //     0x916900: mov             SP, fp
    //     0x916904: ldp             fp, lr, [SP], #0x10
    // 0x916908: ret
    //     0x916908: ret             
  }
  _ initState(/* No info */) {
    // ** addr: 0x949d84, size: 0x194
    // 0x949d84: EnterFrame
    //     0x949d84: stp             fp, lr, [SP, #-0x10]!
    //     0x949d88: mov             fp, SP
    // 0x949d8c: AllocStack(0x20)
    //     0x949d8c: sub             SP, SP, #0x20
    // 0x949d90: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0x949d90: stur            x1, [fp, #-8]
    // 0x949d94: CheckStackOverflow
    //     0x949d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x949d98: cmp             SP, x16
    //     0x949d9c: b.ls            #0x949f10
    // 0x949da0: r1 = 1
    //     0x949da0: movz            x1, #0x1
    // 0x949da4: r0 = AllocateContext()
    //     0x949da4: bl              #0x16f6108  ; AllocateContextStub
    // 0x949da8: ldur            x1, [fp, #-8]
    // 0x949dac: stur            x0, [fp, #-0x10]
    // 0x949db0: StoreField: r0->field_f = r1
    //     0x949db0: stur            w1, [x0, #0xf]
    // 0x949db4: r0 = InitLateStaticField(0xbec) // [package:app_links/src/app_links.dart] AppLinks::_instance
    //     0x949db4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x949db8: ldr             x0, [x0, #0x17d8]
    //     0x949dbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x949dc0: cmp             w0, w16
    //     0x949dc4: b.ne            #0x949dd4
    //     0x949dc8: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d7e8] Field <AppLinks._instance@597120238>: static late final (offset: 0xbec)
    //     0x949dcc: ldr             x2, [x2, #0x7e8]
    //     0x949dd0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x949dd4: mov             x1, x0
    // 0x949dd8: ldur            x2, [fp, #-8]
    // 0x949ddc: StoreField: r2->field_43 = r0
    //     0x949ddc: stur            w0, [x2, #0x43]
    //     0x949de0: ldurb           w16, [x2, #-1]
    //     0x949de4: ldurb           w17, [x0, #-1]
    //     0x949de8: and             x16, x17, x16, lsr #2
    //     0x949dec: tst             x16, HEAP, lsr #32
    //     0x949df0: b.eq            #0x949df8
    //     0x949df4: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0x949df8: r0 = uriLinkStream()
    //     0x949df8: bl              #0x914518  ; [package:app_links/src/app_links.dart] AppLinks::uriLinkStream
    // 0x949dfc: ldur            x2, [fp, #-0x10]
    // 0x949e00: r1 = Function '<anonymous closure>':.
    //     0x949e00: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7f0] AnonymousClosure: (0x949f18), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::initState (0x949d84)
    //     0x949e04: ldr             x1, [x1, #0x7f0]
    // 0x949e08: stur            x0, [fp, #-0x10]
    // 0x949e0c: r0 = AllocateClosure()
    //     0x949e0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x949e10: ldur            x1, [fp, #-0x10]
    // 0x949e14: mov             x2, x0
    // 0x949e18: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x949e18: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x949e1c: r0 = listen()
    //     0x949e1c: bl              #0x163918c  ; [dart:async] _StreamImpl::listen
    // 0x949e20: ldur            x1, [fp, #-8]
    // 0x949e24: StoreField: r1->field_47 = r0
    //     0x949e24: stur            w0, [x1, #0x47]
    //     0x949e28: ldurb           w16, [x1, #-1]
    //     0x949e2c: ldurb           w17, [x0, #-1]
    //     0x949e30: and             x16, x17, x16, lsr #2
    //     0x949e34: tst             x16, HEAP, lsr #32
    //     0x949e38: b.eq            #0x949e40
    //     0x949e3c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x949e40: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x949e40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x949e44: ldr             x0, [x0, #0x1c80]
    //     0x949e48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x949e4c: cmp             w0, w16
    //     0x949e50: b.ne            #0x949e5c
    //     0x949e54: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x949e58: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x949e5c: r0 = GetNavigation.arguments()
    //     0x949e5c: bl              #0x68b4c8  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.arguments
    // 0x949e60: cmp             w0, NULL
    // 0x949e64: b.eq            #0x949ee4
    // 0x949e68: ldur            x1, [fp, #-8]
    // 0x949e6c: r16 = "selected_bottom_index"
    //     0x949e6c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb478] "selected_bottom_index"
    //     0x949e70: ldr             x16, [x16, #0x478]
    // 0x949e74: stp             x16, x0, [SP]
    // 0x949e78: r4 = 0
    //     0x949e78: movz            x4, #0
    // 0x949e7c: ldr             x0, [SP, #8]
    // 0x949e80: r16 = UnlinkedCall_0x613b5c
    //     0x949e80: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d7f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x949e84: add             x16, x16, #0x7f8
    // 0x949e88: ldp             x5, lr, [x16]
    // 0x949e8c: blr             lr
    // 0x949e90: mov             x3, x0
    // 0x949e94: r2 = Null
    //     0x949e94: mov             x2, NULL
    // 0x949e98: r1 = Null
    //     0x949e98: mov             x1, NULL
    // 0x949e9c: stur            x3, [fp, #-0x10]
    // 0x949ea0: branchIfSmi(r0, 0x949ec8)
    //     0x949ea0: tbz             w0, #0, #0x949ec8
    // 0x949ea4: r4 = LoadClassIdInstr(r0)
    //     0x949ea4: ldur            x4, [x0, #-1]
    //     0x949ea8: ubfx            x4, x4, #0xc, #0x14
    // 0x949eac: sub             x4, x4, #0x3c
    // 0x949eb0: cmp             x4, #1
    // 0x949eb4: b.ls            #0x949ec8
    // 0x949eb8: r8 = int
    //     0x949eb8: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x949ebc: r3 = Null
    //     0x949ebc: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d808] Null
    //     0x949ec0: ldr             x3, [x3, #0x808]
    // 0x949ec4: r0 = int()
    //     0x949ec4: bl              #0x16fc548  ; IsType_int_Stub
    // 0x949ec8: ldur            x0, [fp, #-0x10]
    // 0x949ecc: r1 = LoadInt32Instr(r0)
    //     0x949ecc: sbfx            x1, x0, #1, #0x1f
    //     0x949ed0: tbz             w0, #0, #0x949ed8
    //     0x949ed4: ldur            x1, [x0, #7]
    // 0x949ed8: ldur            x0, [fp, #-8]
    // 0x949edc: StoreField: r0->field_1b = r1
    //     0x949edc: stur            x1, [x0, #0x1b]
    // 0x949ee0: b               #0x949ee8
    // 0x949ee4: ldur            x0, [fp, #-8]
    // 0x949ee8: LoadField: r1 = r0->field_1b
    //     0x949ee8: ldur            x1, [x0, #0x1b]
    // 0x949eec: cmp             x1, #1
    // 0x949ef0: b.ne            #0x949f00
    // 0x949ef4: mov             x1, x0
    // 0x949ef8: r2 = 2
    //     0x949ef8: movz            x2, #0x2
    // 0x949efc: r0 = _onItemTapped()
    //     0x949efc: bl              #0x915de0  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped
    // 0x949f00: r0 = Null
    //     0x949f00: mov             x0, NULL
    // 0x949f04: LeaveFrame
    //     0x949f04: mov             SP, fp
    //     0x949f08: ldp             fp, lr, [SP], #0x10
    // 0x949f0c: ret
    //     0x949f0c: ret             
    // 0x949f10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949f10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949f14: b               #0x949da0
  }
  [closure] void <anonymous closure>(dynamic, Uri) {
    // ** addr: 0x949f18, size: 0xc4
    // 0x949f18: EnterFrame
    //     0x949f18: stp             fp, lr, [SP, #-0x10]!
    //     0x949f1c: mov             fp, SP
    // 0x949f20: AllocStack(0x10)
    //     0x949f20: sub             SP, SP, #0x10
    // 0x949f24: SetupParameters()
    //     0x949f24: ldr             x0, [fp, #0x18]
    //     0x949f28: ldur            w1, [x0, #0x17]
    //     0x949f2c: add             x1, x1, HEAP, lsl #32
    //     0x949f30: stur            x1, [fp, #-8]
    // 0x949f34: CheckStackOverflow
    //     0x949f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x949f38: cmp             SP, x16
    //     0x949f3c: b.ls            #0x949fd4
    // 0x949f40: r0 = InitLateStaticField(0x678) // [package:flutter/src/foundation/print.dart] ::debugPrint
    //     0x949f40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x949f44: ldr             x0, [x0, #0xcf0]
    //     0x949f48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x949f4c: cmp             w0, w16
    //     0x949f50: b.ne            #0x949f5c
    //     0x949f54: ldr             x2, [PP, #0x880]  ; [pp+0x880] Field <::.debugPrint>: static late (offset: 0x678)
    //     0x949f58: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x949f5c: r1 = Null
    //     0x949f5c: mov             x1, NULL
    // 0x949f60: r2 = 4
    //     0x949f60: movz            x2, #0x4
    // 0x949f64: r0 = AllocateArray()
    //     0x949f64: bl              #0x16f7198  ; AllocateArrayStub
    // 0x949f68: r16 = "onAppLink: "
    //     0x949f68: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d818] "onAppLink: "
    //     0x949f6c: ldr             x16, [x16, #0x818]
    // 0x949f70: StoreField: r0->field_f = r16
    //     0x949f70: stur            w16, [x0, #0xf]
    // 0x949f74: ldr             x1, [fp, #0x10]
    // 0x949f78: StoreField: r0->field_13 = r1
    //     0x949f78: stur            w1, [x0, #0x13]
    // 0x949f7c: str             x0, [SP]
    // 0x949f80: r0 = _interpolate()
    //     0x949f80: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x949f84: str             NULL, [SP]
    // 0x949f88: mov             x1, x0
    // 0x949f8c: r4 = const [0, 0x2, 0x1, 0x1, wrapWidth, 0x1, null]
    //     0x949f8c: ldr             x4, [PP, #0x890]  ; [pp+0x890] List(7) [0, 0x2, 0x1, 0x1, "wrapWidth", 0x1, Null]
    // 0x949f90: r0 = debugPrintThrottled()
    //     0x949f90: bl              #0x6368ec  ; [package:flutter/src/foundation/print.dart] ::debugPrintThrottled
    // 0x949f94: ldur            x0, [fp, #-8]
    // 0x949f98: LoadField: r1 = r0->field_f
    //     0x949f98: ldur            w1, [x0, #0xf]
    // 0x949f9c: DecompressPointer r1
    //     0x949f9c: add             x1, x1, HEAP, lsl #32
    // 0x949fa0: ldr             x0, [fp, #0x10]
    // 0x949fa4: StoreField: r1->field_23 = r0
    //     0x949fa4: stur            w0, [x1, #0x23]
    //     0x949fa8: ldurb           w16, [x1, #-1]
    //     0x949fac: ldurb           w17, [x0, #-1]
    //     0x949fb0: and             x16, x17, x16, lsr #2
    //     0x949fb4: tst             x16, HEAP, lsr #32
    //     0x949fb8: b.eq            #0x949fc0
    //     0x949fbc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x949fc0: r0 = _handleInitialUri()
    //     0x949fc0: bl              #0x949fdc  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_handleInitialUri
    // 0x949fc4: r0 = Null
    //     0x949fc4: mov             x0, NULL
    // 0x949fc8: LeaveFrame
    //     0x949fc8: mov             SP, fp
    //     0x949fcc: ldp             fp, lr, [SP], #0x10
    // 0x949fd0: ret
    //     0x949fd0: ret             
    // 0x949fd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949fd4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949fd8: b               #0x949f40
  }
  _ _handleInitialUri(/* No info */) async {
    // ** addr: 0x949fdc, size: 0x298
    // 0x949fdc: EnterFrame
    //     0x949fdc: stp             fp, lr, [SP, #-0x10]!
    //     0x949fe0: mov             fp, SP
    // 0x949fe4: AllocStack(0xb8)
    //     0x949fe4: sub             SP, SP, #0xb8
    // 0x949fe8: SetupParameters(_MainPageState this /* r1 => r1, fp-0x70 */)
    //     0x949fe8: stur            NULL, [fp, #-8]
    //     0x949fec: stur            x1, [fp, #-0x70]
    // 0x949ff0: CheckStackOverflow
    //     0x949ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x949ff4: cmp             SP, x16
    //     0x949ff8: b.ls            #0x94a26c
    // 0x949ffc: r1 = 2
    //     0x949ffc: movz            x1, #0x2
    // 0x94a000: r0 = AllocateContext()
    //     0x94a000: bl              #0x16f6108  ; AllocateContextStub
    // 0x94a004: mov             x2, x0
    // 0x94a008: ldur            x1, [fp, #-0x70]
    // 0x94a00c: stur            x2, [fp, #-0x78]
    // 0x94a010: StoreField: r2->field_f = r1
    //     0x94a010: stur            w1, [x2, #0xf]
    // 0x94a014: InitAsync() -> Future<void?>
    //     0x94a014: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x94a018: bl              #0x6326e0  ; InitAsyncStub
    // 0x94a01c: ldur            x0, [fp, #-0x70]
    // 0x94a020: LoadField: r2 = r0->field_23
    //     0x94a020: ldur            w2, [x0, #0x23]
    // 0x94a024: DecompressPointer r2
    //     0x94a024: add             x2, x2, HEAP, lsl #32
    // 0x94a028: cmp             w2, NULL
    // 0x94a02c: b.ne            #0x94a03c
    // 0x94a030: mov             x1, x0
    // 0x94a034: r0 = setDefaultUtmParameters()
    //     0x94a034: bl              #0x916c2c  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::setDefaultUtmParameters
    // 0x94a038: b               #0x94a044
    // 0x94a03c: ldur            x1, [fp, #-0x70]
    // 0x94a040: r0 = setUtmParameters()
    //     0x94a040: bl              #0x91690c  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::setUtmParameters
    // 0x94a044: ldur            x2, [fp, #-0x70]
    // 0x94a048: LoadField: r0 = r2->field_f
    //     0x94a048: ldur            w0, [x2, #0xf]
    // 0x94a04c: DecompressPointer r0
    //     0x94a04c: add             x0, x0, HEAP, lsl #32
    // 0x94a050: cmp             w0, NULL
    // 0x94a054: b.ne            #0x94a060
    // 0x94a058: r0 = Null
    //     0x94a058: mov             x0, NULL
    // 0x94a05c: r0 = ReturnAsyncNotFuture()
    //     0x94a05c: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x94a060: LoadField: r3 = r2->field_23
    //     0x94a060: ldur            w3, [x2, #0x23]
    // 0x94a064: DecompressPointer r3
    //     0x94a064: add             x3, x3, HEAP, lsl #32
    // 0x94a068: stur            x3, [fp, #-0x80]
    // 0x94a06c: cmp             w3, NULL
    // 0x94a070: b.eq            #0x94a254
    // 0x94a074: r0 = LoadClassIdInstr(r3)
    //     0x94a074: ldur            x0, [x3, #-1]
    //     0x94a078: ubfx            x0, x0, #0xc, #0x14
    // 0x94a07c: mov             x1, x3
    // 0x94a080: r0 = GDT[cid_x0 + -0xf32]()
    //     0x94a080: sub             lr, x0, #0xf32
    //     0x94a084: ldr             lr, [x21, lr, lsl #3]
    //     0x94a088: blr             lr
    // 0x94a08c: tbnz            w0, #4, #0x94a19c
    // 0x94a090: ldur            x2, [fp, #-0x70]
    // 0x94a094: LoadField: r3 = r2->field_23
    //     0x94a094: ldur            w3, [x2, #0x23]
    // 0x94a098: DecompressPointer r3
    //     0x94a098: add             x3, x3, HEAP, lsl #32
    // 0x94a09c: stur            x3, [fp, #-0x80]
    // 0x94a0a0: cmp             w3, NULL
    // 0x94a0a4: b.ne            #0x94a0b4
    // 0x94a0a8: mov             x3, x2
    // 0x94a0ac: r5 = Null
    //     0x94a0ac: mov             x5, NULL
    // 0x94a0b0: b               #0x94a0d4
    // 0x94a0b4: r0 = LoadClassIdInstr(r3)
    //     0x94a0b4: ldur            x0, [x3, #-1]
    //     0x94a0b8: ubfx            x0, x0, #0xc, #0x14
    // 0x94a0bc: mov             x1, x3
    // 0x94a0c0: r0 = GDT[cid_x0 + -0xfd1]()
    //     0x94a0c0: sub             lr, x0, #0xfd1
    //     0x94a0c4: ldr             lr, [x21, lr, lsl #3]
    //     0x94a0c8: blr             lr
    // 0x94a0cc: mov             x5, x0
    // 0x94a0d0: ldur            x3, [fp, #-0x70]
    // 0x94a0d4: ldur            x4, [fp, #-0x78]
    // 0x94a0d8: mov             x0, x5
    // 0x94a0dc: stur            x5, [fp, #-0x88]
    // 0x94a0e0: StoreField: r4->field_13 = r0
    //     0x94a0e0: stur            w0, [x4, #0x13]
    //     0x94a0e4: ldurb           w16, [x4, #-1]
    //     0x94a0e8: ldurb           w17, [x0, #-1]
    //     0x94a0ec: and             x16, x17, x16, lsr #2
    //     0x94a0f0: tst             x16, HEAP, lsr #32
    //     0x94a0f4: b.eq            #0x94a0fc
    //     0x94a0f8: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x94a0fc: LoadField: r0 = r3->field_3f
    //     0x94a0fc: ldur            w0, [x3, #0x3f]
    // 0x94a100: DecompressPointer r0
    //     0x94a100: add             x0, x0, HEAP, lsl #32
    // 0x94a104: LoadField: r6 = r0->field_4b
    //     0x94a104: ldur            w6, [x0, #0x4b]
    // 0x94a108: DecompressPointer r6
    //     0x94a108: add             x6, x6, HEAP, lsl #32
    // 0x94a10c: stur            x6, [fp, #-0x80]
    // 0x94a110: r16 = ""
    //     0x94a110: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94a114: str             x16, [SP]
    // 0x94a118: mov             x1, x6
    // 0x94a11c: r2 = "token"
    //     0x94a11c: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x94a120: ldr             x2, [x2, #0x958]
    // 0x94a124: r4 = const [0, 0x3, 0x1, 0x2, defaultValue, 0x2, null]
    //     0x94a124: add             x4, PP, #0x11, lsl #12  ; [pp+0x11f48] List(7) [0, 0x3, 0x1, 0x2, "defaultValue", 0x2, Null]
    //     0x94a128: ldr             x4, [x4, #0xf48]
    // 0x94a12c: r0 = getString()
    //     0x94a12c: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x94a130: ldur            x2, [fp, #-0x78]
    // 0x94a134: r1 = Function '<anonymous closure>':.
    //     0x94a134: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d820] AnonymousClosure: (0x94a274), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_handleInitialUri (0x949fdc)
    //     0x94a138: ldr             x1, [x1, #0x820]
    // 0x94a13c: stur            x0, [fp, #-0x80]
    // 0x94a140: r0 = AllocateClosure()
    //     0x94a140: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94a144: mov             x4, x0
    // 0x94a148: ldur            x3, [fp, #-0x80]
    // 0x94a14c: stur            x4, [fp, #-0x98]
    // 0x94a150: LoadField: r5 = r3->field_7
    //     0x94a150: ldur            w5, [x3, #7]
    // 0x94a154: DecompressPointer r5
    //     0x94a154: add             x5, x5, HEAP, lsl #32
    // 0x94a158: mov             x0, x4
    // 0x94a15c: mov             x2, x5
    // 0x94a160: stur            x5, [fp, #-0x90]
    // 0x94a164: r1 = Null
    //     0x94a164: mov             x1, NULL
    // 0x94a168: r8 = (dynamic this, X0) => FutureOr<Y0>
    //     0x94a168: add             x8, PP, #0x11, lsl #12  ; [pp+0x11ce8] FunctionType: (dynamic this, X0) => FutureOr<Y0>
    //     0x94a16c: ldr             x8, [x8, #0xce8]
    // 0x94a170: LoadField: r9 = r8->field_7
    //     0x94a170: ldur            x9, [x8, #7]
    // 0x94a174: r3 = Null
    //     0x94a174: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d828] Null
    //     0x94a178: ldr             x3, [x3, #0x828]
    // 0x94a17c: blr             x9
    // 0x94a180: ldur            x16, [fp, #-0x80]
    // 0x94a184: stp             x16, NULL, [SP, #0x10]
    // 0x94a188: ldur            x16, [fp, #-0x98]
    // 0x94a18c: stp             NULL, x16, [SP]
    // 0x94a190: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0x94a190: ldr             x4, [PP, #0x580]  ; [pp+0x580] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0x94a194: r0 = then()
    //     0x94a194: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x94a198: b               #0x94a254
    // 0x94a19c: ldur            x1, [fp, #-0x70]
    // 0x94a1a0: r0 = openUrlInApp()
    //     0x94a1a0: bl              #0x915688  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x94a1a4: b               #0x94a254
    // 0x94a1a8: sub             SP, fp, #0xb8
    // 0x94a1ac: mov             x4, x0
    // 0x94a1b0: mov             x3, x1
    // 0x94a1b4: stur            x0, [fp, #-0x78]
    // 0x94a1b8: stur            x1, [fp, #-0x80]
    // 0x94a1bc: r0 = 60
    //     0x94a1bc: movz            x0, #0x3c
    // 0x94a1c0: branchIfSmi(r4, 0x94a1cc)
    //     0x94a1c0: tbz             w4, #0, #0x94a1cc
    // 0x94a1c4: r0 = LoadClassIdInstr(r4)
    //     0x94a1c4: ldur            x0, [x4, #-1]
    //     0x94a1c8: ubfx            x0, x0, #0xc, #0x14
    // 0x94a1cc: cmp             x0, #0x6ee
    // 0x94a1d0: b.ne            #0x94a1dc
    // 0x94a1d4: r0 = Null
    //     0x94a1d4: mov             x0, NULL
    // 0x94a1d8: r0 = ReturnAsyncNotFuture()
    //     0x94a1d8: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x94a1dc: mov             x0, x4
    // 0x94a1e0: r2 = Null
    //     0x94a1e0: mov             x2, NULL
    // 0x94a1e4: r1 = Null
    //     0x94a1e4: mov             x1, NULL
    // 0x94a1e8: cmp             w0, NULL
    // 0x94a1ec: b.eq            #0x94a228
    // 0x94a1f0: branchIfSmi(r0, 0x94a228)
    //     0x94a1f0: tbz             w0, #0, #0x94a228
    // 0x94a1f4: r3 = LoadClassIdInstr(r0)
    //     0x94a1f4: ldur            x3, [x0, #-1]
    //     0x94a1f8: ubfx            x3, x3, #0xc, #0x14
    // 0x94a1fc: sub             x3, x3, #0xcf
    // 0x94a200: cmp             x3, #1
    // 0x94a204: b.ls            #0x94a230
    // 0x94a208: cmp             x3, #0x217
    // 0x94a20c: b.eq            #0x94a230
    // 0x94a210: sub             x3, x3, #0x36f
    // 0x94a214: cmp             x3, #1
    // 0x94a218: b.ls            #0x94a230
    // 0x94a21c: r17 = 5658
    //     0x94a21c: movz            x17, #0x161a
    // 0x94a220: cmp             x3, x17
    // 0x94a224: b.eq            #0x94a230
    // 0x94a228: r0 = false
    //     0x94a228: add             x0, NULL, #0x30  ; false
    // 0x94a22c: b               #0x94a234
    // 0x94a230: r0 = true
    //     0x94a230: add             x0, NULL, #0x20  ; true
    // 0x94a234: tbnz            w0, #4, #0x94a25c
    // 0x94a238: ldur            x0, [fp, #-0x70]
    // 0x94a23c: LoadField: r1 = r0->field_f
    //     0x94a23c: ldur            w1, [x0, #0xf]
    // 0x94a240: DecompressPointer r1
    //     0x94a240: add             x1, x1, HEAP, lsl #32
    // 0x94a244: cmp             w1, NULL
    // 0x94a248: b.ne            #0x94a254
    // 0x94a24c: r0 = Null
    //     0x94a24c: mov             x0, NULL
    // 0x94a250: r0 = ReturnAsyncNotFuture()
    //     0x94a250: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x94a254: r0 = Null
    //     0x94a254: mov             x0, NULL
    // 0x94a258: r0 = ReturnAsyncNotFuture()
    //     0x94a258: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x94a25c: ldur            x0, [fp, #-0x78]
    // 0x94a260: ldur            x1, [fp, #-0x80]
    // 0x94a264: r0 = ReThrow()
    //     0x94a264: bl              #0x16f53f4  ; ReThrowStub
    // 0x94a268: brk             #0
    // 0x94a26c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a26c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a270: b               #0x949ffc
  }
  [closure] Future<Set<Set<void>>> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x94a274, size: 0x320
    // 0x94a274: EnterFrame
    //     0x94a274: stp             fp, lr, [SP, #-0x10]!
    //     0x94a278: mov             fp, SP
    // 0x94a27c: AllocStack(0x48)
    //     0x94a27c: sub             SP, SP, #0x48
    // 0x94a280: SetupParameters(_MainPageState this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x94a280: stur            NULL, [fp, #-8]
    //     0x94a284: movz            x0, #0
    //     0x94a288: add             x1, fp, w0, sxtw #2
    //     0x94a28c: ldr             x1, [x1, #0x18]
    //     0x94a290: add             x2, fp, w0, sxtw #2
    //     0x94a294: ldr             x2, [x2, #0x10]
    //     0x94a298: stur            x2, [fp, #-0x18]
    //     0x94a29c: ldur            w3, [x1, #0x17]
    //     0x94a2a0: add             x3, x3, HEAP, lsl #32
    //     0x94a2a4: stur            x3, [fp, #-0x10]
    // 0x94a2a8: CheckStackOverflow
    //     0x94a2a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94a2ac: cmp             SP, x16
    //     0x94a2b0: b.ls            #0x94a58c
    // 0x94a2b4: InitAsync() -> Future<Set<Set<void?>>>
    //     0x94a2b4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d720] TypeArguments: <Set<Set<void?>>>
    //     0x94a2b8: ldr             x0, [x0, #0x720]
    //     0x94a2bc: bl              #0x6326e0  ; InitAsyncStub
    // 0x94a2c0: r1 = <Set<void?>>
    //     0x94a2c0: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d730] TypeArguments: <Set<void?>>
    //     0x94a2c4: ldr             x1, [x1, #0x730]
    // 0x94a2c8: r0 = _Set()
    //     0x94a2c8: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x94a2cc: mov             x2, x0
    // 0x94a2d0: r1 = _Uint32List
    //     0x94a2d0: ldr             x1, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a2d4: stur            x2, [fp, #-0x20]
    // 0x94a2d8: StoreField: r2->field_1b = r1
    //     0x94a2d8: stur            w1, [x2, #0x1b]
    // 0x94a2dc: StoreField: r2->field_b = rZR
    //     0x94a2dc: stur            wzr, [x2, #0xb]
    // 0x94a2e0: r3 = const []
    //     0x94a2e0: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a2e4: StoreField: r2->field_f = r3
    //     0x94a2e4: stur            w3, [x2, #0xf]
    // 0x94a2e8: StoreField: r2->field_13 = rZR
    //     0x94a2e8: stur            wzr, [x2, #0x13]
    // 0x94a2ec: ArrayStore: r2[0] = rZR  ; List_4
    //     0x94a2ec: stur            wzr, [x2, #0x17]
    // 0x94a2f0: ldur            x0, [fp, #-0x18]
    // 0x94a2f4: r4 = 60
    //     0x94a2f4: movz            x4, #0x3c
    // 0x94a2f8: branchIfSmi(r0, 0x94a304)
    //     0x94a2f8: tbz             w0, #0, #0x94a304
    // 0x94a2fc: r4 = LoadClassIdInstr(r0)
    //     0x94a2fc: ldur            x4, [x0, #-1]
    //     0x94a300: ubfx            x4, x4, #0xc, #0x14
    // 0x94a304: r16 = ""
    //     0x94a304: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94a308: stp             x16, x0, [SP]
    // 0x94a30c: mov             x0, x4
    // 0x94a310: mov             lr, x0
    // 0x94a314: ldr             lr, [x21, lr, lsl #3]
    // 0x94a318: blr             lr
    // 0x94a31c: tbz             w0, #4, #0x94a37c
    // 0x94a320: ldur            x0, [fp, #-0x10]
    // 0x94a324: r1 = <void?>
    //     0x94a324: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x94a328: r0 = _Set()
    //     0x94a328: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x94a32c: mov             x2, x0
    // 0x94a330: r0 = _Uint32List
    //     0x94a330: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a334: stur            x2, [fp, #-0x18]
    // 0x94a338: StoreField: r2->field_1b = r0
    //     0x94a338: stur            w0, [x2, #0x1b]
    // 0x94a33c: StoreField: r2->field_b = rZR
    //     0x94a33c: stur            wzr, [x2, #0xb]
    // 0x94a340: r3 = const []
    //     0x94a340: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a344: StoreField: r2->field_f = r3
    //     0x94a344: stur            w3, [x2, #0xf]
    // 0x94a348: StoreField: r2->field_13 = rZR
    //     0x94a348: stur            wzr, [x2, #0x13]
    // 0x94a34c: ArrayStore: r2[0] = rZR  ; List_4
    //     0x94a34c: stur            wzr, [x2, #0x17]
    // 0x94a350: ldur            x4, [fp, #-0x10]
    // 0x94a354: LoadField: r1 = r4->field_f
    //     0x94a354: ldur            w1, [x4, #0xf]
    // 0x94a358: DecompressPointer r1
    //     0x94a358: add             x1, x1, HEAP, lsl #32
    // 0x94a35c: r0 = openUrlInApp()
    //     0x94a35c: bl              #0x915688  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x94a360: ldur            x1, [fp, #-0x18]
    // 0x94a364: r2 = Null
    //     0x94a364: mov             x2, NULL
    // 0x94a368: r0 = add()
    //     0x94a368: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a36c: ldur            x1, [fp, #-0x20]
    // 0x94a370: ldur            x2, [fp, #-0x18]
    // 0x94a374: r0 = add()
    //     0x94a374: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a378: b               #0x94a584
    // 0x94a37c: ldur            x4, [fp, #-0x10]
    // 0x94a380: r0 = _Uint32List
    //     0x94a380: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a384: r3 = const []
    //     0x94a384: ldr             x3, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a388: r1 = <Set<void?>>
    //     0x94a388: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d730] TypeArguments: <Set<void?>>
    //     0x94a38c: ldr             x1, [x1, #0x730]
    // 0x94a390: r0 = _Set()
    //     0x94a390: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x94a394: mov             x4, x0
    // 0x94a398: r3 = _Uint32List
    //     0x94a398: ldr             x3, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a39c: stur            x4, [fp, #-0x28]
    // 0x94a3a0: StoreField: r4->field_1b = r3
    //     0x94a3a0: stur            w3, [x4, #0x1b]
    // 0x94a3a4: StoreField: r4->field_b = rZR
    //     0x94a3a4: stur            wzr, [x4, #0xb]
    // 0x94a3a8: r5 = const []
    //     0x94a3a8: ldr             x5, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a3ac: StoreField: r4->field_f = r5
    //     0x94a3ac: stur            w5, [x4, #0xf]
    // 0x94a3b0: StoreField: r4->field_13 = rZR
    //     0x94a3b0: stur            wzr, [x4, #0x13]
    // 0x94a3b4: ArrayStore: r4[0] = rZR  ; List_4
    //     0x94a3b4: stur            wzr, [x4, #0x17]
    // 0x94a3b8: ldur            x6, [fp, #-0x10]
    // 0x94a3bc: LoadField: r7 = r6->field_13
    //     0x94a3bc: ldur            w7, [x6, #0x13]
    // 0x94a3c0: DecompressPointer r7
    //     0x94a3c0: add             x7, x7, HEAP, lsl #32
    // 0x94a3c4: stur            x7, [fp, #-0x18]
    // 0x94a3c8: cmp             w7, NULL
    // 0x94a3cc: b.ne            #0x94a3d8
    // 0x94a3d0: r0 = Null
    //     0x94a3d0: mov             x0, NULL
    // 0x94a3d4: b               #0x94a41c
    // 0x94a3d8: r0 = LoadClassIdInstr(r7)
    //     0x94a3d8: ldur            x0, [x7, #-1]
    //     0x94a3dc: ubfx            x0, x0, #0xc, #0x14
    // 0x94a3e0: mov             x1, x7
    // 0x94a3e4: r2 = "auto_login_otp"
    //     0x94a3e4: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d838] "auto_login_otp"
    //     0x94a3e8: ldr             x2, [x2, #0x838]
    // 0x94a3ec: r0 = GDT[cid_x0 + -0xfe]()
    //     0x94a3ec: sub             lr, x0, #0xfe
    //     0x94a3f0: ldr             lr, [x21, lr, lsl #3]
    //     0x94a3f4: blr             lr
    // 0x94a3f8: cmp             w0, NULL
    // 0x94a3fc: b.ne            #0x94a408
    // 0x94a400: r0 = Null
    //     0x94a400: mov             x0, NULL
    // 0x94a404: b               #0x94a41c
    // 0x94a408: LoadField: r1 = r0->field_7
    //     0x94a408: ldur            w1, [x0, #7]
    // 0x94a40c: cbnz            w1, #0x94a418
    // 0x94a410: r0 = false
    //     0x94a410: add             x0, NULL, #0x30  ; false
    // 0x94a414: b               #0x94a41c
    // 0x94a418: r0 = true
    //     0x94a418: add             x0, NULL, #0x20  ; true
    // 0x94a41c: cmp             w0, NULL
    // 0x94a420: b.ne            #0x94a434
    // 0x94a424: ldur            x3, [fp, #-0x10]
    // 0x94a428: r0 = _Uint32List
    //     0x94a428: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a42c: r2 = const []
    //     0x94a42c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a430: b               #0x94a524
    // 0x94a434: tbnz            w0, #4, #0x94a518
    // 0x94a438: ldur            x0, [fp, #-0x10]
    // 0x94a43c: ldur            x2, [fp, #-0x18]
    // 0x94a440: r1 = <void?>
    //     0x94a440: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x94a444: r0 = _Set()
    //     0x94a444: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x94a448: mov             x3, x0
    // 0x94a44c: r0 = _Uint32List
    //     0x94a44c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a450: stur            x3, [fp, #-0x38]
    // 0x94a454: StoreField: r3->field_1b = r0
    //     0x94a454: stur            w0, [x3, #0x1b]
    // 0x94a458: StoreField: r3->field_b = rZR
    //     0x94a458: stur            wzr, [x3, #0xb]
    // 0x94a45c: r2 = const []
    //     0x94a45c: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a460: StoreField: r3->field_f = r2
    //     0x94a460: stur            w2, [x3, #0xf]
    // 0x94a464: StoreField: r3->field_13 = rZR
    //     0x94a464: stur            wzr, [x3, #0x13]
    // 0x94a468: ArrayStore: r3[0] = rZR  ; List_4
    //     0x94a468: stur            wzr, [x3, #0x17]
    // 0x94a46c: ldur            x4, [fp, #-0x10]
    // 0x94a470: LoadField: r0 = r4->field_f
    //     0x94a470: ldur            w0, [x4, #0xf]
    // 0x94a474: DecompressPointer r0
    //     0x94a474: add             x0, x0, HEAP, lsl #32
    // 0x94a478: LoadField: r5 = r0->field_3b
    //     0x94a478: ldur            w5, [x0, #0x3b]
    // 0x94a47c: DecompressPointer r5
    //     0x94a47c: add             x5, x5, HEAP, lsl #32
    // 0x94a480: ldur            x1, [fp, #-0x18]
    // 0x94a484: stur            x5, [fp, #-0x30]
    // 0x94a488: cmp             w1, NULL
    // 0x94a48c: b.ne            #0x94a498
    // 0x94a490: r0 = Null
    //     0x94a490: mov             x0, NULL
    // 0x94a494: b               #0x94a4b4
    // 0x94a498: r0 = LoadClassIdInstr(r1)
    //     0x94a498: ldur            x0, [x1, #-1]
    //     0x94a49c: ubfx            x0, x0, #0xc, #0x14
    // 0x94a4a0: r2 = "auto_login_otp"
    //     0x94a4a0: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2d838] "auto_login_otp"
    //     0x94a4a4: ldr             x2, [x2, #0x838]
    // 0x94a4a8: r0 = GDT[cid_x0 + -0xfe]()
    //     0x94a4a8: sub             lr, x0, #0xfe
    //     0x94a4ac: ldr             lr, [x21, lr, lsl #3]
    //     0x94a4b0: blr             lr
    // 0x94a4b4: cmp             w0, NULL
    // 0x94a4b8: b.ne            #0x94a4c4
    // 0x94a4bc: r2 = ""
    //     0x94a4bc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94a4c0: b               #0x94a4c8
    // 0x94a4c4: mov             x2, x0
    // 0x94a4c8: ldur            x0, [fp, #-0x10]
    // 0x94a4cc: ldur            x1, [fp, #-0x30]
    // 0x94a4d0: r0 = verifyAutoLoginOtp()
    //     0x94a4d0: bl              #0x916fa0  ; [package:customer_app/app/presentation/controllers/profile/profile_controller.dart] ProfileController::verifyAutoLoginOtp
    // 0x94a4d4: mov             x1, x0
    // 0x94a4d8: stur            x1, [fp, #-0x18]
    // 0x94a4dc: r0 = Await()
    //     0x94a4dc: bl              #0x63248c  ; AwaitStub
    // 0x94a4e0: ldur            x1, [fp, #-0x38]
    // 0x94a4e4: mov             x2, x0
    // 0x94a4e8: r0 = add()
    //     0x94a4e8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a4ec: ldur            x3, [fp, #-0x10]
    // 0x94a4f0: LoadField: r1 = r3->field_f
    //     0x94a4f0: ldur            w1, [x3, #0xf]
    // 0x94a4f4: DecompressPointer r1
    //     0x94a4f4: add             x1, x1, HEAP, lsl #32
    // 0x94a4f8: r0 = openUrlInApp()
    //     0x94a4f8: bl              #0x915688  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x94a4fc: ldur            x1, [fp, #-0x38]
    // 0x94a500: r2 = Null
    //     0x94a500: mov             x2, NULL
    // 0x94a504: r0 = add()
    //     0x94a504: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a508: ldur            x1, [fp, #-0x28]
    // 0x94a50c: ldur            x2, [fp, #-0x38]
    // 0x94a510: r0 = add()
    //     0x94a510: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a514: b               #0x94a578
    // 0x94a518: ldur            x3, [fp, #-0x10]
    // 0x94a51c: r0 = _Uint32List
    //     0x94a51c: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a520: r2 = const []
    //     0x94a520: ldr             x2, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a524: r1 = <void?>
    //     0x94a524: ldr             x1, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    // 0x94a528: r0 = _Set()
    //     0x94a528: bl              #0x649598  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x94a52c: mov             x2, x0
    // 0x94a530: r0 = _Uint32List
    //     0x94a530: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] _Uint32List(1) [0x0]
    // 0x94a534: stur            x2, [fp, #-0x18]
    // 0x94a538: StoreField: r2->field_1b = r0
    //     0x94a538: stur            w0, [x2, #0x1b]
    // 0x94a53c: StoreField: r2->field_b = rZR
    //     0x94a53c: stur            wzr, [x2, #0xb]
    // 0x94a540: r0 = const []
    //     0x94a540: ldr             x0, [PP, #0x1778]  ; [pp+0x1778] List(0) []
    // 0x94a544: StoreField: r2->field_f = r0
    //     0x94a544: stur            w0, [x2, #0xf]
    // 0x94a548: StoreField: r2->field_13 = rZR
    //     0x94a548: stur            wzr, [x2, #0x13]
    // 0x94a54c: ArrayStore: r2[0] = rZR  ; List_4
    //     0x94a54c: stur            wzr, [x2, #0x17]
    // 0x94a550: ldur            x0, [fp, #-0x10]
    // 0x94a554: LoadField: r1 = r0->field_f
    //     0x94a554: ldur            w1, [x0, #0xf]
    // 0x94a558: DecompressPointer r1
    //     0x94a558: add             x1, x1, HEAP, lsl #32
    // 0x94a55c: r0 = openUrlInApp()
    //     0x94a55c: bl              #0x915688  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::openUrlInApp
    // 0x94a560: ldur            x1, [fp, #-0x18]
    // 0x94a564: r2 = Null
    //     0x94a564: mov             x2, NULL
    // 0x94a568: r0 = add()
    //     0x94a568: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a56c: ldur            x1, [fp, #-0x28]
    // 0x94a570: ldur            x2, [fp, #-0x18]
    // 0x94a574: r0 = add()
    //     0x94a574: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a578: ldur            x1, [fp, #-0x20]
    // 0x94a57c: ldur            x2, [fp, #-0x28]
    // 0x94a580: r0 = add()
    //     0x94a580: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x94a584: ldur            x0, [fp, #-0x20]
    // 0x94a588: r0 = ReturnAsyncNotFuture()
    //     0x94a588: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x94a58c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94a58c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94a590: b               #0x94a2b4
  }
  _ build(/* No info */) {
    // ** addr: 0xbf37c4, size: 0x58
    // 0xbf37c4: EnterFrame
    //     0xbf37c4: stp             fp, lr, [SP, #-0x10]!
    //     0xbf37c8: mov             fp, SP
    // 0xbf37cc: AllocStack(0x10)
    //     0xbf37cc: sub             SP, SP, #0x10
    // 0xbf37d0: SetupParameters(_MainPageState this /* r1 => r1, fp-0x8 */)
    //     0xbf37d0: stur            x1, [fp, #-8]
    // 0xbf37d4: r1 = 1
    //     0xbf37d4: movz            x1, #0x1
    // 0xbf37d8: r0 = AllocateContext()
    //     0xbf37d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf37dc: mov             x1, x0
    // 0xbf37e0: ldur            x0, [fp, #-8]
    // 0xbf37e4: stur            x1, [fp, #-0x10]
    // 0xbf37e8: StoreField: r1->field_f = r0
    //     0xbf37e8: stur            w0, [x1, #0xf]
    // 0xbf37ec: r0 = Obx()
    //     0xbf37ec: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0xbf37f0: ldur            x2, [fp, #-0x10]
    // 0xbf37f4: r1 = Function '<anonymous closure>':.
    //     0xbf37f4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb00] AnonymousClosure: (0xbf381c), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::build (0xbf37c4)
    //     0xbf37f8: ldr             x1, [x1, #0xb00]
    // 0xbf37fc: stur            x0, [fp, #-8]
    // 0xbf3800: r0 = AllocateClosure()
    //     0xbf3800: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf3804: mov             x1, x0
    // 0xbf3808: ldur            x0, [fp, #-8]
    // 0xbf380c: StoreField: r0->field_b = r1
    //     0xbf380c: stur            w1, [x0, #0xb]
    // 0xbf3810: LeaveFrame
    //     0xbf3810: mov             SP, fp
    //     0xbf3814: ldp             fp, lr, [SP], #0x10
    // 0xbf3818: ret
    //     0xbf3818: ret             
  }
  [closure] Theme <anonymous closure>(dynamic) {
    // ** addr: 0xbf381c, size: 0x130
    // 0xbf381c: EnterFrame
    //     0xbf381c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3820: mov             fp, SP
    // 0xbf3824: AllocStack(0x20)
    //     0xbf3824: sub             SP, SP, #0x20
    // 0xbf3828: SetupParameters()
    //     0xbf3828: ldr             x0, [fp, #0x10]
    //     0xbf382c: ldur            w2, [x0, #0x17]
    //     0xbf3830: add             x2, x2, HEAP, lsl #32
    //     0xbf3834: stur            x2, [fp, #-8]
    // 0xbf3838: CheckStackOverflow
    //     0xbf3838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf383c: cmp             SP, x16
    //     0xbf3840: b.ls            #0xbf3940
    // 0xbf3844: LoadField: r0 = r2->field_f
    //     0xbf3844: ldur            w0, [x2, #0xf]
    // 0xbf3848: DecompressPointer r0
    //     0xbf3848: add             x0, x0, HEAP, lsl #32
    // 0xbf384c: LoadField: r1 = r0->field_3f
    //     0xbf384c: ldur            w1, [x0, #0x3f]
    // 0xbf3850: DecompressPointer r1
    //     0xbf3850: add             x1, x1, HEAP, lsl #32
    // 0xbf3854: r0 = appConfigResponse()
    //     0xbf3854: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0xbf3858: stur            x0, [fp, #-0x10]
    // 0xbf385c: r0 = InitLateStaticField(0xe78) // [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_pages
    //     0xbf385c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf3860: ldr             x0, [x0, #0x1cf0]
    //     0xbf3864: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbf3868: cmp             w0, w16
    //     0xbf386c: b.ne            #0xbf387c
    //     0xbf3870: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Field <_MainPageState@1712469706._pages@1712469706>: static late final (offset: 0xe78)
    //     0xbf3874: ldr             x2, [x2, #0xb08]
    //     0xbf3878: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbf387c: mov             x2, x0
    // 0xbf3880: ldur            x0, [fp, #-8]
    // 0xbf3884: LoadField: r3 = r0->field_f
    //     0xbf3884: ldur            w3, [x0, #0xf]
    // 0xbf3888: DecompressPointer r3
    //     0xbf3888: add             x3, x3, HEAP, lsl #32
    // 0xbf388c: stur            x3, [fp, #-0x18]
    // 0xbf3890: LoadField: r4 = r3->field_1b
    //     0xbf3890: ldur            x4, [x3, #0x1b]
    // 0xbf3894: LoadField: r0 = r2->field_b
    //     0xbf3894: ldur            w0, [x2, #0xb]
    // 0xbf3898: r1 = LoadInt32Instr(r0)
    //     0xbf3898: sbfx            x1, x0, #1, #0x1f
    // 0xbf389c: mov             x0, x1
    // 0xbf38a0: mov             x1, x4
    // 0xbf38a4: cmp             x1, x0
    // 0xbf38a8: b.hs            #0xbf3948
    // 0xbf38ac: LoadField: r0 = r2->field_f
    //     0xbf38ac: ldur            w0, [x2, #0xf]
    // 0xbf38b0: DecompressPointer r0
    //     0xbf38b0: add             x0, x0, HEAP, lsl #32
    // 0xbf38b4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xbf38b4: add             x16, x0, x4, lsl #2
    //     0xbf38b8: ldur            w1, [x16, #0xf]
    // 0xbf38bc: DecompressPointer r1
    //     0xbf38bc: add             x1, x1, HEAP, lsl #32
    // 0xbf38c0: stur            x1, [fp, #-8]
    // 0xbf38c4: r0 = Center()
    //     0xbf38c4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbf38c8: mov             x2, x0
    // 0xbf38cc: r0 = Instance_Alignment
    //     0xbf38cc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbf38d0: ldr             x0, [x0, #0xb10]
    // 0xbf38d4: stur            x2, [fp, #-0x20]
    // 0xbf38d8: StoreField: r2->field_f = r0
    //     0xbf38d8: stur            w0, [x2, #0xf]
    // 0xbf38dc: ldur            x0, [fp, #-8]
    // 0xbf38e0: StoreField: r2->field_b = r0
    //     0xbf38e0: stur            w0, [x2, #0xb]
    // 0xbf38e4: ldur            x1, [fp, #-0x18]
    // 0xbf38e8: r0 = buildBottomNavigationMenu()
    //     0xbf38e8: bl              #0xbf394c  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::buildBottomNavigationMenu
    // 0xbf38ec: stur            x0, [fp, #-8]
    // 0xbf38f0: r0 = Scaffold()
    //     0xbf38f0: bl              #0x7f8618  ; AllocateScaffoldStub -> Scaffold (size=0x4c)
    // 0xbf38f4: mov             x1, x0
    // 0xbf38f8: ldur            x0, [fp, #-0x20]
    // 0xbf38fc: stur            x1, [fp, #-0x18]
    // 0xbf3900: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf3900: stur            w0, [x1, #0x17]
    // 0xbf3904: ldur            x0, [fp, #-8]
    // 0xbf3908: StoreField: r1->field_37 = r0
    //     0xbf3908: stur            w0, [x1, #0x37]
    // 0xbf390c: r0 = true
    //     0xbf390c: add             x0, NULL, #0x20  ; true
    // 0xbf3910: StoreField: r1->field_43 = r0
    //     0xbf3910: stur            w0, [x1, #0x43]
    // 0xbf3914: r0 = false
    //     0xbf3914: add             x0, NULL, #0x30  ; false
    // 0xbf3918: StoreField: r1->field_b = r0
    //     0xbf3918: stur            w0, [x1, #0xb]
    // 0xbf391c: StoreField: r1->field_f = r0
    //     0xbf391c: stur            w0, [x1, #0xf]
    // 0xbf3920: r0 = Theme()
    //     0xbf3920: bl              #0x796f30  ; AllocateThemeStub -> Theme (size=0x14)
    // 0xbf3924: ldur            x1, [fp, #-0x10]
    // 0xbf3928: StoreField: r0->field_b = r1
    //     0xbf3928: stur            w1, [x0, #0xb]
    // 0xbf392c: ldur            x1, [fp, #-0x18]
    // 0xbf3930: StoreField: r0->field_f = r1
    //     0xbf3930: stur            w1, [x0, #0xf]
    // 0xbf3934: LeaveFrame
    //     0xbf3934: mov             SP, fp
    //     0xbf3938: ldp             fp, lr, [SP], #0x10
    // 0xbf393c: ret
    //     0xbf393c: ret             
    // 0xbf3940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf3940: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf3944: b               #0xbf3844
    // 0xbf3948: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf3948: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ buildBottomNavigationMenu(/* No info */) {
    // ** addr: 0xbf394c, size: 0x14f4
    // 0xbf394c: EnterFrame
    //     0xbf394c: stp             fp, lr, [SP, #-0x10]!
    //     0xbf3950: mov             fp, SP
    // 0xbf3954: AllocStack(0x70)
    //     0xbf3954: sub             SP, SP, #0x70
    // 0xbf3958: SetupParameters(_MainPageState this /* r1 => r2, fp-0x10 */)
    //     0xbf3958: mov             x2, x1
    //     0xbf395c: stur            x1, [fp, #-0x10]
    // 0xbf3960: CheckStackOverflow
    //     0xbf3960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf3964: cmp             SP, x16
    //     0xbf3968: b.ls            #0xbf4e20
    // 0xbf396c: LoadField: r0 = r2->field_1b
    //     0xbf396c: ldur            x0, [x2, #0x1b]
    // 0xbf3970: stur            x0, [fp, #-8]
    // 0xbf3974: r0 = SvgPicture()
    //     0xbf3974: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf3978: stur            x0, [fp, #-0x18]
    // 0xbf397c: r16 = "home"
    //     0xbf397c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb470] "home"
    //     0xbf3980: ldr             x16, [x16, #0x470]
    // 0xbf3984: r30 = Instance_BoxFit
    //     0xbf3984: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf3988: ldr             lr, [lr, #0xb18]
    // 0xbf398c: stp             lr, x16, [SP]
    // 0xbf3990: mov             x1, x0
    // 0xbf3994: r2 = "assets/images/home.svg"
    //     0xbf3994: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb20] "assets/images/home.svg"
    //     0xbf3998: ldr             x2, [x2, #0xb20]
    // 0xbf399c: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf399c: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf39a0: ldr             x4, [x4, #0xb28]
    // 0xbf39a4: r0 = SvgPicture.asset()
    //     0xbf39a4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf39a8: ldur            x2, [fp, #-0x10]
    // 0xbf39ac: LoadField: r0 = r2->field_3f
    //     0xbf39ac: ldur            w0, [x2, #0x3f]
    // 0xbf39b0: DecompressPointer r0
    //     0xbf39b0: add             x0, x0, HEAP, lsl #32
    // 0xbf39b4: LoadField: r1 = r0->field_57
    //     0xbf39b4: ldur            w1, [x0, #0x57]
    // 0xbf39b8: DecompressPointer r1
    //     0xbf39b8: add             x1, x1, HEAP, lsl #32
    // 0xbf39bc: r0 = value()
    //     0xbf39bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf39c0: LoadField: r1 = r0->field_b
    //     0xbf39c0: ldur            w1, [x0, #0xb]
    // 0xbf39c4: DecompressPointer r1
    //     0xbf39c4: add             x1, x1, HEAP, lsl #32
    // 0xbf39c8: cmp             w1, NULL
    // 0xbf39cc: b.ne            #0xbf39d8
    // 0xbf39d0: r0 = Null
    //     0xbf39d0: mov             x0, NULL
    // 0xbf39d4: b               #0xbf3a10
    // 0xbf39d8: LoadField: r0 = r1->field_57
    //     0xbf39d8: ldur            w0, [x1, #0x57]
    // 0xbf39dc: DecompressPointer r0
    //     0xbf39dc: add             x0, x0, HEAP, lsl #32
    // 0xbf39e0: cmp             w0, NULL
    // 0xbf39e4: b.ne            #0xbf39f0
    // 0xbf39e8: r0 = Null
    //     0xbf39e8: mov             x0, NULL
    // 0xbf39ec: b               #0xbf3a10
    // 0xbf39f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf39f0: ldur            w1, [x0, #0x17]
    // 0xbf39f4: DecompressPointer r1
    //     0xbf39f4: add             x1, x1, HEAP, lsl #32
    // 0xbf39f8: cmp             w1, NULL
    // 0xbf39fc: b.ne            #0xbf3a08
    // 0xbf3a00: r0 = Null
    //     0xbf3a00: mov             x0, NULL
    // 0xbf3a04: b               #0xbf3a10
    // 0xbf3a08: LoadField: r0 = r1->field_7
    //     0xbf3a08: ldur            w0, [x1, #7]
    // 0xbf3a0c: DecompressPointer r0
    //     0xbf3a0c: add             x0, x0, HEAP, lsl #32
    // 0xbf3a10: cmp             w0, NULL
    // 0xbf3a14: b.ne            #0xbf3a20
    // 0xbf3a18: r0 = 0
    //     0xbf3a18: movz            x0, #0
    // 0xbf3a1c: b               #0xbf3a30
    // 0xbf3a20: r1 = LoadInt32Instr(r0)
    //     0xbf3a20: sbfx            x1, x0, #1, #0x1f
    //     0xbf3a24: tbz             w0, #0, #0xbf3a2c
    //     0xbf3a28: ldur            x1, [x0, #7]
    // 0xbf3a2c: mov             x0, x1
    // 0xbf3a30: ldur            x2, [fp, #-0x10]
    // 0xbf3a34: stur            x0, [fp, #-0x20]
    // 0xbf3a38: LoadField: r1 = r2->field_3f
    //     0xbf3a38: ldur            w1, [x2, #0x3f]
    // 0xbf3a3c: DecompressPointer r1
    //     0xbf3a3c: add             x1, x1, HEAP, lsl #32
    // 0xbf3a40: LoadField: r3 = r1->field_57
    //     0xbf3a40: ldur            w3, [x1, #0x57]
    // 0xbf3a44: DecompressPointer r3
    //     0xbf3a44: add             x3, x3, HEAP, lsl #32
    // 0xbf3a48: mov             x1, x3
    // 0xbf3a4c: r0 = value()
    //     0xbf3a4c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf3a50: LoadField: r1 = r0->field_b
    //     0xbf3a50: ldur            w1, [x0, #0xb]
    // 0xbf3a54: DecompressPointer r1
    //     0xbf3a54: add             x1, x1, HEAP, lsl #32
    // 0xbf3a58: cmp             w1, NULL
    // 0xbf3a5c: b.ne            #0xbf3a68
    // 0xbf3a60: r0 = Null
    //     0xbf3a60: mov             x0, NULL
    // 0xbf3a64: b               #0xbf3aa0
    // 0xbf3a68: LoadField: r0 = r1->field_57
    //     0xbf3a68: ldur            w0, [x1, #0x57]
    // 0xbf3a6c: DecompressPointer r0
    //     0xbf3a6c: add             x0, x0, HEAP, lsl #32
    // 0xbf3a70: cmp             w0, NULL
    // 0xbf3a74: b.ne            #0xbf3a80
    // 0xbf3a78: r0 = Null
    //     0xbf3a78: mov             x0, NULL
    // 0xbf3a7c: b               #0xbf3aa0
    // 0xbf3a80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf3a80: ldur            w1, [x0, #0x17]
    // 0xbf3a84: DecompressPointer r1
    //     0xbf3a84: add             x1, x1, HEAP, lsl #32
    // 0xbf3a88: cmp             w1, NULL
    // 0xbf3a8c: b.ne            #0xbf3a98
    // 0xbf3a90: r0 = Null
    //     0xbf3a90: mov             x0, NULL
    // 0xbf3a94: b               #0xbf3aa0
    // 0xbf3a98: LoadField: r0 = r1->field_b
    //     0xbf3a98: ldur            w0, [x1, #0xb]
    // 0xbf3a9c: DecompressPointer r0
    //     0xbf3a9c: add             x0, x0, HEAP, lsl #32
    // 0xbf3aa0: cmp             w0, NULL
    // 0xbf3aa4: b.ne            #0xbf3ab0
    // 0xbf3aa8: r0 = 0
    //     0xbf3aa8: movz            x0, #0
    // 0xbf3aac: b               #0xbf3ac0
    // 0xbf3ab0: r1 = LoadInt32Instr(r0)
    //     0xbf3ab0: sbfx            x1, x0, #1, #0x1f
    //     0xbf3ab4: tbz             w0, #0, #0xbf3abc
    //     0xbf3ab8: ldur            x1, [x0, #7]
    // 0xbf3abc: mov             x0, x1
    // 0xbf3ac0: ldur            x2, [fp, #-0x10]
    // 0xbf3ac4: stur            x0, [fp, #-0x28]
    // 0xbf3ac8: LoadField: r1 = r2->field_3f
    //     0xbf3ac8: ldur            w1, [x2, #0x3f]
    // 0xbf3acc: DecompressPointer r1
    //     0xbf3acc: add             x1, x1, HEAP, lsl #32
    // 0xbf3ad0: LoadField: r3 = r1->field_57
    //     0xbf3ad0: ldur            w3, [x1, #0x57]
    // 0xbf3ad4: DecompressPointer r3
    //     0xbf3ad4: add             x3, x3, HEAP, lsl #32
    // 0xbf3ad8: mov             x1, x3
    // 0xbf3adc: r0 = value()
    //     0xbf3adc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf3ae0: LoadField: r1 = r0->field_b
    //     0xbf3ae0: ldur            w1, [x0, #0xb]
    // 0xbf3ae4: DecompressPointer r1
    //     0xbf3ae4: add             x1, x1, HEAP, lsl #32
    // 0xbf3ae8: cmp             w1, NULL
    // 0xbf3aec: b.ne            #0xbf3af8
    // 0xbf3af0: r0 = Null
    //     0xbf3af0: mov             x0, NULL
    // 0xbf3af4: b               #0xbf3b30
    // 0xbf3af8: LoadField: r0 = r1->field_57
    //     0xbf3af8: ldur            w0, [x1, #0x57]
    // 0xbf3afc: DecompressPointer r0
    //     0xbf3afc: add             x0, x0, HEAP, lsl #32
    // 0xbf3b00: cmp             w0, NULL
    // 0xbf3b04: b.ne            #0xbf3b10
    // 0xbf3b08: r0 = Null
    //     0xbf3b08: mov             x0, NULL
    // 0xbf3b0c: b               #0xbf3b30
    // 0xbf3b10: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf3b10: ldur            w1, [x0, #0x17]
    // 0xbf3b14: DecompressPointer r1
    //     0xbf3b14: add             x1, x1, HEAP, lsl #32
    // 0xbf3b18: cmp             w1, NULL
    // 0xbf3b1c: b.ne            #0xbf3b28
    // 0xbf3b20: r0 = Null
    //     0xbf3b20: mov             x0, NULL
    // 0xbf3b24: b               #0xbf3b30
    // 0xbf3b28: LoadField: r0 = r1->field_f
    //     0xbf3b28: ldur            w0, [x1, #0xf]
    // 0xbf3b2c: DecompressPointer r0
    //     0xbf3b2c: add             x0, x0, HEAP, lsl #32
    // 0xbf3b30: cmp             w0, NULL
    // 0xbf3b34: b.ne            #0xbf3b40
    // 0xbf3b38: r1 = 0
    //     0xbf3b38: movz            x1, #0
    // 0xbf3b3c: b               #0xbf3b4c
    // 0xbf3b40: r1 = LoadInt32Instr(r0)
    //     0xbf3b40: sbfx            x1, x0, #1, #0x1f
    //     0xbf3b44: tbz             w0, #0, #0xbf3b4c
    //     0xbf3b48: ldur            x1, [x0, #7]
    // 0xbf3b4c: ldur            x2, [fp, #-0x10]
    // 0xbf3b50: ldur            x0, [fp, #-0x18]
    // 0xbf3b54: stur            x1, [fp, #-0x30]
    // 0xbf3b58: r0 = Color()
    //     0xbf3b58: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbf3b5c: mov             x1, x0
    // 0xbf3b60: r0 = Instance_ColorSpace
    //     0xbf3b60: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbf3b64: stur            x1, [fp, #-0x38]
    // 0xbf3b68: StoreField: r1->field_27 = r0
    //     0xbf3b68: stur            w0, [x1, #0x27]
    // 0xbf3b6c: d0 = 1.000000
    //     0xbf3b6c: fmov            d0, #1.00000000
    // 0xbf3b70: StoreField: r1->field_7 = d0
    //     0xbf3b70: stur            d0, [x1, #7]
    // 0xbf3b74: ldur            x2, [fp, #-0x20]
    // 0xbf3b78: ubfx            x2, x2, #0, #0x20
    // 0xbf3b7c: and             w3, w2, #0xff
    // 0xbf3b80: ubfx            x3, x3, #0, #0x20
    // 0xbf3b84: scvtf           d1, x3
    // 0xbf3b88: d2 = 255.000000
    //     0xbf3b88: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbf3b8c: fdiv            d3, d1, d2
    // 0xbf3b90: StoreField: r1->field_f = d3
    //     0xbf3b90: stur            d3, [x1, #0xf]
    // 0xbf3b94: ldur            x2, [fp, #-0x28]
    // 0xbf3b98: ubfx            x2, x2, #0, #0x20
    // 0xbf3b9c: and             w3, w2, #0xff
    // 0xbf3ba0: ubfx            x3, x3, #0, #0x20
    // 0xbf3ba4: scvtf           d1, x3
    // 0xbf3ba8: fdiv            d3, d1, d2
    // 0xbf3bac: ArrayStore: r1[0] = d3  ; List_8
    //     0xbf3bac: stur            d3, [x1, #0x17]
    // 0xbf3bb0: ldur            x2, [fp, #-0x30]
    // 0xbf3bb4: ubfx            x2, x2, #0, #0x20
    // 0xbf3bb8: and             w3, w2, #0xff
    // 0xbf3bbc: ubfx            x3, x3, #0, #0x20
    // 0xbf3bc0: scvtf           d1, x3
    // 0xbf3bc4: fdiv            d3, d1, d2
    // 0xbf3bc8: StoreField: r1->field_1f = d3
    //     0xbf3bc8: stur            d3, [x1, #0x1f]
    // 0xbf3bcc: r0 = ColorFilter()
    //     0xbf3bcc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbf3bd0: mov             x1, x0
    // 0xbf3bd4: ldur            x0, [fp, #-0x38]
    // 0xbf3bd8: stur            x1, [fp, #-0x40]
    // 0xbf3bdc: StoreField: r1->field_7 = r0
    //     0xbf3bdc: stur            w0, [x1, #7]
    // 0xbf3be0: r0 = Instance_BlendMode
    //     0xbf3be0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbf3be4: ldr             x0, [x0, #0xb30]
    // 0xbf3be8: StoreField: r1->field_b = r0
    //     0xbf3be8: stur            w0, [x1, #0xb]
    // 0xbf3bec: r2 = 1
    //     0xbf3bec: movz            x2, #0x1
    // 0xbf3bf0: StoreField: r1->field_13 = r2
    //     0xbf3bf0: stur            x2, [x1, #0x13]
    // 0xbf3bf4: r0 = SvgPicture()
    //     0xbf3bf4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf3bf8: stur            x0, [fp, #-0x38]
    // 0xbf3bfc: r16 = "home"
    //     0xbf3bfc: add             x16, PP, #0xb, lsl #12  ; [pp+0xb470] "home"
    //     0xbf3c00: ldr             x16, [x16, #0x470]
    // 0xbf3c04: r30 = Instance_BoxFit
    //     0xbf3c04: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf3c08: ldr             lr, [lr, #0xb18]
    // 0xbf3c0c: stp             lr, x16, [SP, #8]
    // 0xbf3c10: ldur            x16, [fp, #-0x40]
    // 0xbf3c14: str             x16, [SP]
    // 0xbf3c18: mov             x1, x0
    // 0xbf3c1c: r2 = "assets/images/home.svg"
    //     0xbf3c1c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb20] "assets/images/home.svg"
    //     0xbf3c20: ldr             x2, [x2, #0xb20]
    // 0xbf3c24: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf3c24: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf3c28: ldr             x4, [x4, #0xb38]
    // 0xbf3c2c: r0 = SvgPicture.asset()
    //     0xbf3c2c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf3c30: r0 = BottomNavigationBarItem()
    //     0xbf3c30: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xbf3c34: mov             x2, x0
    // 0xbf3c38: ldur            x0, [fp, #-0x18]
    // 0xbf3c3c: stur            x2, [fp, #-0x40]
    // 0xbf3c40: StoreField: r2->field_b = r0
    //     0xbf3c40: stur            w0, [x2, #0xb]
    // 0xbf3c44: r0 = "Home"
    //     0xbf3c44: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb40] "Home"
    //     0xbf3c48: ldr             x0, [x0, #0xb40]
    // 0xbf3c4c: StoreField: r2->field_13 = r0
    //     0xbf3c4c: stur            w0, [x2, #0x13]
    // 0xbf3c50: ldur            x0, [fp, #-0x38]
    // 0xbf3c54: StoreField: r2->field_f = r0
    //     0xbf3c54: stur            w0, [x2, #0xf]
    // 0xbf3c58: ldur            x0, [fp, #-0x10]
    // 0xbf3c5c: LoadField: r1 = r0->field_3f
    //     0xbf3c5c: ldur            w1, [x0, #0x3f]
    // 0xbf3c60: DecompressPointer r1
    //     0xbf3c60: add             x1, x1, HEAP, lsl #32
    // 0xbf3c64: LoadField: r3 = r1->field_57
    //     0xbf3c64: ldur            w3, [x1, #0x57]
    // 0xbf3c68: DecompressPointer r3
    //     0xbf3c68: add             x3, x3, HEAP, lsl #32
    // 0xbf3c6c: mov             x1, x3
    // 0xbf3c70: r0 = value()
    //     0xbf3c70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf3c74: LoadField: r1 = r0->field_b
    //     0xbf3c74: ldur            w1, [x0, #0xb]
    // 0xbf3c78: DecompressPointer r1
    //     0xbf3c78: add             x1, x1, HEAP, lsl #32
    // 0xbf3c7c: cmp             w1, NULL
    // 0xbf3c80: b.ne            #0xbf3c8c
    // 0xbf3c84: r0 = Null
    //     0xbf3c84: mov             x0, NULL
    // 0xbf3c88: b               #0xbf3cc4
    // 0xbf3c8c: LoadField: r0 = r1->field_57
    //     0xbf3c8c: ldur            w0, [x1, #0x57]
    // 0xbf3c90: DecompressPointer r0
    //     0xbf3c90: add             x0, x0, HEAP, lsl #32
    // 0xbf3c94: cmp             w0, NULL
    // 0xbf3c98: b.ne            #0xbf3ca4
    // 0xbf3c9c: r0 = Null
    //     0xbf3c9c: mov             x0, NULL
    // 0xbf3ca0: b               #0xbf3cc4
    // 0xbf3ca4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf3ca4: ldur            w1, [x0, #0x17]
    // 0xbf3ca8: DecompressPointer r1
    //     0xbf3ca8: add             x1, x1, HEAP, lsl #32
    // 0xbf3cac: cmp             w1, NULL
    // 0xbf3cb0: b.ne            #0xbf3cbc
    // 0xbf3cb4: r0 = Null
    //     0xbf3cb4: mov             x0, NULL
    // 0xbf3cb8: b               #0xbf3cc4
    // 0xbf3cbc: LoadField: r0 = r1->field_7
    //     0xbf3cbc: ldur            w0, [x1, #7]
    // 0xbf3cc0: DecompressPointer r0
    //     0xbf3cc0: add             x0, x0, HEAP, lsl #32
    // 0xbf3cc4: cmp             w0, NULL
    // 0xbf3cc8: b.ne            #0xbf3cd4
    // 0xbf3ccc: r0 = 0
    //     0xbf3ccc: movz            x0, #0
    // 0xbf3cd0: b               #0xbf3ce4
    // 0xbf3cd4: r1 = LoadInt32Instr(r0)
    //     0xbf3cd4: sbfx            x1, x0, #1, #0x1f
    //     0xbf3cd8: tbz             w0, #0, #0xbf3ce0
    //     0xbf3cdc: ldur            x1, [x0, #7]
    // 0xbf3ce0: mov             x0, x1
    // 0xbf3ce4: ldur            x2, [fp, #-0x10]
    // 0xbf3ce8: stur            x0, [fp, #-0x20]
    // 0xbf3cec: LoadField: r1 = r2->field_3f
    //     0xbf3cec: ldur            w1, [x2, #0x3f]
    // 0xbf3cf0: DecompressPointer r1
    //     0xbf3cf0: add             x1, x1, HEAP, lsl #32
    // 0xbf3cf4: LoadField: r3 = r1->field_57
    //     0xbf3cf4: ldur            w3, [x1, #0x57]
    // 0xbf3cf8: DecompressPointer r3
    //     0xbf3cf8: add             x3, x3, HEAP, lsl #32
    // 0xbf3cfc: mov             x1, x3
    // 0xbf3d00: r0 = value()
    //     0xbf3d00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf3d04: LoadField: r1 = r0->field_b
    //     0xbf3d04: ldur            w1, [x0, #0xb]
    // 0xbf3d08: DecompressPointer r1
    //     0xbf3d08: add             x1, x1, HEAP, lsl #32
    // 0xbf3d0c: cmp             w1, NULL
    // 0xbf3d10: b.ne            #0xbf3d1c
    // 0xbf3d14: r0 = Null
    //     0xbf3d14: mov             x0, NULL
    // 0xbf3d18: b               #0xbf3d54
    // 0xbf3d1c: LoadField: r0 = r1->field_57
    //     0xbf3d1c: ldur            w0, [x1, #0x57]
    // 0xbf3d20: DecompressPointer r0
    //     0xbf3d20: add             x0, x0, HEAP, lsl #32
    // 0xbf3d24: cmp             w0, NULL
    // 0xbf3d28: b.ne            #0xbf3d34
    // 0xbf3d2c: r0 = Null
    //     0xbf3d2c: mov             x0, NULL
    // 0xbf3d30: b               #0xbf3d54
    // 0xbf3d34: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf3d34: ldur            w1, [x0, #0x17]
    // 0xbf3d38: DecompressPointer r1
    //     0xbf3d38: add             x1, x1, HEAP, lsl #32
    // 0xbf3d3c: cmp             w1, NULL
    // 0xbf3d40: b.ne            #0xbf3d4c
    // 0xbf3d44: r0 = Null
    //     0xbf3d44: mov             x0, NULL
    // 0xbf3d48: b               #0xbf3d54
    // 0xbf3d4c: LoadField: r0 = r1->field_b
    //     0xbf3d4c: ldur            w0, [x1, #0xb]
    // 0xbf3d50: DecompressPointer r0
    //     0xbf3d50: add             x0, x0, HEAP, lsl #32
    // 0xbf3d54: cmp             w0, NULL
    // 0xbf3d58: b.ne            #0xbf3d64
    // 0xbf3d5c: r0 = 0
    //     0xbf3d5c: movz            x0, #0
    // 0xbf3d60: b               #0xbf3d74
    // 0xbf3d64: r1 = LoadInt32Instr(r0)
    //     0xbf3d64: sbfx            x1, x0, #1, #0x1f
    //     0xbf3d68: tbz             w0, #0, #0xbf3d70
    //     0xbf3d6c: ldur            x1, [x0, #7]
    // 0xbf3d70: mov             x0, x1
    // 0xbf3d74: ldur            x2, [fp, #-0x10]
    // 0xbf3d78: stur            x0, [fp, #-0x28]
    // 0xbf3d7c: LoadField: r1 = r2->field_3f
    //     0xbf3d7c: ldur            w1, [x2, #0x3f]
    // 0xbf3d80: DecompressPointer r1
    //     0xbf3d80: add             x1, x1, HEAP, lsl #32
    // 0xbf3d84: LoadField: r3 = r1->field_57
    //     0xbf3d84: ldur            w3, [x1, #0x57]
    // 0xbf3d88: DecompressPointer r3
    //     0xbf3d88: add             x3, x3, HEAP, lsl #32
    // 0xbf3d8c: mov             x1, x3
    // 0xbf3d90: r0 = value()
    //     0xbf3d90: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf3d94: LoadField: r1 = r0->field_b
    //     0xbf3d94: ldur            w1, [x0, #0xb]
    // 0xbf3d98: DecompressPointer r1
    //     0xbf3d98: add             x1, x1, HEAP, lsl #32
    // 0xbf3d9c: cmp             w1, NULL
    // 0xbf3da0: b.ne            #0xbf3dac
    // 0xbf3da4: r0 = Null
    //     0xbf3da4: mov             x0, NULL
    // 0xbf3da8: b               #0xbf3de4
    // 0xbf3dac: LoadField: r0 = r1->field_57
    //     0xbf3dac: ldur            w0, [x1, #0x57]
    // 0xbf3db0: DecompressPointer r0
    //     0xbf3db0: add             x0, x0, HEAP, lsl #32
    // 0xbf3db4: cmp             w0, NULL
    // 0xbf3db8: b.ne            #0xbf3dc4
    // 0xbf3dbc: r0 = Null
    //     0xbf3dbc: mov             x0, NULL
    // 0xbf3dc0: b               #0xbf3de4
    // 0xbf3dc4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf3dc4: ldur            w1, [x0, #0x17]
    // 0xbf3dc8: DecompressPointer r1
    //     0xbf3dc8: add             x1, x1, HEAP, lsl #32
    // 0xbf3dcc: cmp             w1, NULL
    // 0xbf3dd0: b.ne            #0xbf3ddc
    // 0xbf3dd4: r0 = Null
    //     0xbf3dd4: mov             x0, NULL
    // 0xbf3dd8: b               #0xbf3de4
    // 0xbf3ddc: LoadField: r0 = r1->field_f
    //     0xbf3ddc: ldur            w0, [x1, #0xf]
    // 0xbf3de0: DecompressPointer r0
    //     0xbf3de0: add             x0, x0, HEAP, lsl #32
    // 0xbf3de4: cmp             w0, NULL
    // 0xbf3de8: b.ne            #0xbf3df4
    // 0xbf3dec: r0 = 0
    //     0xbf3dec: movz            x0, #0
    // 0xbf3df0: b               #0xbf3e04
    // 0xbf3df4: r1 = LoadInt32Instr(r0)
    //     0xbf3df4: sbfx            x1, x0, #1, #0x1f
    //     0xbf3df8: tbz             w0, #0, #0xbf3e00
    //     0xbf3dfc: ldur            x1, [x0, #7]
    // 0xbf3e00: mov             x0, x1
    // 0xbf3e04: stur            x0, [fp, #-0x30]
    // 0xbf3e08: r0 = Color()
    //     0xbf3e08: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbf3e0c: mov             x1, x0
    // 0xbf3e10: r0 = Instance_ColorSpace
    //     0xbf3e10: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbf3e14: stur            x1, [fp, #-0x18]
    // 0xbf3e18: StoreField: r1->field_27 = r0
    //     0xbf3e18: stur            w0, [x1, #0x27]
    // 0xbf3e1c: d0 = 1.000000
    //     0xbf3e1c: fmov            d0, #1.00000000
    // 0xbf3e20: StoreField: r1->field_7 = d0
    //     0xbf3e20: stur            d0, [x1, #7]
    // 0xbf3e24: ldur            x2, [fp, #-0x20]
    // 0xbf3e28: ubfx            x2, x2, #0, #0x20
    // 0xbf3e2c: and             w3, w2, #0xff
    // 0xbf3e30: ubfx            x3, x3, #0, #0x20
    // 0xbf3e34: scvtf           d1, x3
    // 0xbf3e38: d2 = 255.000000
    //     0xbf3e38: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbf3e3c: fdiv            d3, d1, d2
    // 0xbf3e40: StoreField: r1->field_f = d3
    //     0xbf3e40: stur            d3, [x1, #0xf]
    // 0xbf3e44: ldur            x2, [fp, #-0x28]
    // 0xbf3e48: ubfx            x2, x2, #0, #0x20
    // 0xbf3e4c: and             w3, w2, #0xff
    // 0xbf3e50: ubfx            x3, x3, #0, #0x20
    // 0xbf3e54: scvtf           d1, x3
    // 0xbf3e58: fdiv            d3, d1, d2
    // 0xbf3e5c: ArrayStore: r1[0] = d3  ; List_8
    //     0xbf3e5c: stur            d3, [x1, #0x17]
    // 0xbf3e60: ldur            x2, [fp, #-0x30]
    // 0xbf3e64: ubfx            x2, x2, #0, #0x20
    // 0xbf3e68: and             w3, w2, #0xff
    // 0xbf3e6c: ubfx            x3, x3, #0, #0x20
    // 0xbf3e70: scvtf           d1, x3
    // 0xbf3e74: fdiv            d3, d1, d2
    // 0xbf3e78: StoreField: r1->field_1f = d3
    //     0xbf3e78: stur            d3, [x1, #0x1f]
    // 0xbf3e7c: r0 = InitLateStaticField(0xe78) // [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_pages
    //     0xbf3e7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf3e80: ldr             x0, [x0, #0x1cf0]
    //     0xbf3e84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbf3e88: cmp             w0, w16
    //     0xbf3e8c: b.ne            #0xbf3e9c
    //     0xbf3e90: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb08] Field <_MainPageState@1712469706._pages@1712469706>: static late final (offset: 0xe78)
    //     0xbf3e94: ldr             x2, [x2, #0xb08]
    //     0xbf3e98: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbf3e9c: mov             x3, x0
    // 0xbf3ea0: stur            x3, [fp, #-0x48]
    // 0xbf3ea4: LoadField: r0 = r3->field_b
    //     0xbf3ea4: ldur            w0, [x3, #0xb]
    // 0xbf3ea8: r1 = LoadInt32Instr(r0)
    //     0xbf3ea8: sbfx            x1, x0, #1, #0x1f
    // 0xbf3eac: mov             x0, x1
    // 0xbf3eb0: r1 = 1
    //     0xbf3eb0: movz            x1, #0x1
    // 0xbf3eb4: cmp             x1, x0
    // 0xbf3eb8: b.hs            #0xbf4e28
    // 0xbf3ebc: LoadField: r0 = r3->field_f
    //     0xbf3ebc: ldur            w0, [x3, #0xf]
    // 0xbf3ec0: DecompressPointer r0
    //     0xbf3ec0: add             x0, x0, HEAP, lsl #32
    // 0xbf3ec4: LoadField: r4 = r0->field_13
    //     0xbf3ec4: ldur            w4, [x0, #0x13]
    // 0xbf3ec8: DecompressPointer r4
    //     0xbf3ec8: add             x4, x4, HEAP, lsl #32
    // 0xbf3ecc: mov             x0, x4
    // 0xbf3ed0: stur            x4, [fp, #-0x38]
    // 0xbf3ed4: r2 = Null
    //     0xbf3ed4: mov             x2, NULL
    // 0xbf3ed8: r1 = Null
    //     0xbf3ed8: mov             x1, NULL
    // 0xbf3edc: r4 = 60
    //     0xbf3edc: movz            x4, #0x3c
    // 0xbf3ee0: branchIfSmi(r0, 0xbf3eec)
    //     0xbf3ee0: tbz             w0, #0, #0xbf3eec
    // 0xbf3ee4: r4 = LoadClassIdInstr(r0)
    //     0xbf3ee4: ldur            x4, [x0, #-1]
    //     0xbf3ee8: ubfx            x4, x4, #0xc, #0x14
    // 0xbf3eec: r17 = 4530
    //     0xbf3eec: movz            x17, #0x11b2
    // 0xbf3ef0: cmp             x4, x17
    // 0xbf3ef4: b.eq            #0xbf3f0c
    // 0xbf3ef8: r8 = OrdersView
    //     0xbf3ef8: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0xbf3efc: ldr             x8, [x8, #0xb48]
    // 0xbf3f00: r3 = Null
    //     0xbf3f00: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb50] Null
    //     0xbf3f04: ldr             x3, [x3, #0xb50]
    // 0xbf3f08: r0 = DefaultTypeTest()
    //     0xbf3f08: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xbf3f0c: r0 = LoadStaticField(0xcb0)
    //     0xbf3f0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf3f10: ldr             x0, [x0, #0x1960]
    // 0xbf3f14: cmp             w0, NULL
    // 0xbf3f18: b.ne            #0xbf3f30
    // 0xbf3f1c: r0 = Instance_GetInstance
    //     0xbf3f1c: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf3f20: ldr             x0, [x0, #0x900]
    // 0xbf3f24: StoreStaticField(0xcb0, r0)
    //     0xbf3f24: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xbf3f28: str             x0, [x1, #0x1960]
    // 0xbf3f2c: b               #0xbf3f38
    // 0xbf3f30: r0 = Instance_GetInstance
    //     0xbf3f30: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf3f34: ldr             x0, [x0, #0x900]
    // 0xbf3f38: ldur            x1, [fp, #-0x48]
    // 0xbf3f3c: ldur            x2, [fp, #-0x38]
    // 0xbf3f40: LoadField: r3 = r2->field_b
    //     0xbf3f40: ldur            w3, [x2, #0xb]
    // 0xbf3f44: DecompressPointer r3
    //     0xbf3f44: add             x3, x3, HEAP, lsl #32
    // 0xbf3f48: r16 = Instance_GetInstance
    //     0xbf3f48: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf3f4c: ldr             x16, [x16, #0x900]
    // 0xbf3f50: stp             x16, x3, [SP, #8]
    // 0xbf3f54: str             NULL, [SP]
    // 0xbf3f58: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf3f58: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf3f5c: r0 = find()
    //     0xbf3f5c: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0xbf3f60: LoadField: r1 = r0->field_5b
    //     0xbf3f60: ldur            w1, [x0, #0x5b]
    // 0xbf3f64: DecompressPointer r1
    //     0xbf3f64: add             x1, x1, HEAP, lsl #32
    // 0xbf3f68: r0 = value()
    //     0xbf3f68: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf3f6c: ldur            x3, [fp, #-0x48]
    // 0xbf3f70: LoadField: r0 = r3->field_b
    //     0xbf3f70: ldur            w0, [x3, #0xb]
    // 0xbf3f74: r1 = LoadInt32Instr(r0)
    //     0xbf3f74: sbfx            x1, x0, #1, #0x1f
    // 0xbf3f78: mov             x0, x1
    // 0xbf3f7c: r1 = 1
    //     0xbf3f7c: movz            x1, #0x1
    // 0xbf3f80: cmp             x1, x0
    // 0xbf3f84: b.hs            #0xbf4e2c
    // 0xbf3f88: LoadField: r0 = r3->field_f
    //     0xbf3f88: ldur            w0, [x3, #0xf]
    // 0xbf3f8c: DecompressPointer r0
    //     0xbf3f8c: add             x0, x0, HEAP, lsl #32
    // 0xbf3f90: LoadField: r4 = r0->field_13
    //     0xbf3f90: ldur            w4, [x0, #0x13]
    // 0xbf3f94: DecompressPointer r4
    //     0xbf3f94: add             x4, x4, HEAP, lsl #32
    // 0xbf3f98: mov             x0, x4
    // 0xbf3f9c: stur            x4, [fp, #-0x38]
    // 0xbf3fa0: r2 = Null
    //     0xbf3fa0: mov             x2, NULL
    // 0xbf3fa4: r1 = Null
    //     0xbf3fa4: mov             x1, NULL
    // 0xbf3fa8: r4 = 60
    //     0xbf3fa8: movz            x4, #0x3c
    // 0xbf3fac: branchIfSmi(r0, 0xbf3fb8)
    //     0xbf3fac: tbz             w0, #0, #0xbf3fb8
    // 0xbf3fb0: r4 = LoadClassIdInstr(r0)
    //     0xbf3fb0: ldur            x4, [x0, #-1]
    //     0xbf3fb4: ubfx            x4, x4, #0xc, #0x14
    // 0xbf3fb8: r17 = 4530
    //     0xbf3fb8: movz            x17, #0x11b2
    // 0xbf3fbc: cmp             x4, x17
    // 0xbf3fc0: b.eq            #0xbf3fd8
    // 0xbf3fc4: r8 = OrdersView
    //     0xbf3fc4: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0xbf3fc8: ldr             x8, [x8, #0xb48]
    // 0xbf3fcc: r3 = Null
    //     0xbf3fcc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb60] Null
    //     0xbf3fd0: ldr             x3, [x3, #0xb60]
    // 0xbf3fd4: r0 = DefaultTypeTest()
    //     0xbf3fd4: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xbf3fd8: r0 = LoadStaticField(0xcb0)
    //     0xbf3fd8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf3fdc: ldr             x0, [x0, #0x1960]
    // 0xbf3fe0: cmp             w0, NULL
    // 0xbf3fe4: b.ne            #0xbf3ffc
    // 0xbf3fe8: r0 = Instance_GetInstance
    //     0xbf3fe8: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf3fec: ldr             x0, [x0, #0x900]
    // 0xbf3ff0: StoreStaticField(0xcb0, r0)
    //     0xbf3ff0: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xbf3ff4: str             x0, [x1, #0x1960]
    // 0xbf3ff8: b               #0xbf4004
    // 0xbf3ffc: r0 = Instance_GetInstance
    //     0xbf3ffc: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf4000: ldr             x0, [x0, #0x900]
    // 0xbf4004: ldur            x3, [fp, #-0x10]
    // 0xbf4008: ldur            x2, [fp, #-0x18]
    // 0xbf400c: ldur            x1, [fp, #-0x38]
    // 0xbf4010: LoadField: r4 = r1->field_b
    //     0xbf4010: ldur            w4, [x1, #0xb]
    // 0xbf4014: DecompressPointer r4
    //     0xbf4014: add             x4, x4, HEAP, lsl #32
    // 0xbf4018: r16 = Instance_GetInstance
    //     0xbf4018: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf401c: ldr             x16, [x16, #0x900]
    // 0xbf4020: stp             x16, x4, [SP, #8]
    // 0xbf4024: str             NULL, [SP]
    // 0xbf4028: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf4028: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf402c: r0 = find()
    //     0xbf402c: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0xbf4030: LoadField: r1 = r0->field_5b
    //     0xbf4030: ldur            w1, [x0, #0x5b]
    // 0xbf4034: DecompressPointer r1
    //     0xbf4034: add             x1, x1, HEAP, lsl #32
    // 0xbf4038: r0 = value()
    //     0xbf4038: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf403c: ldur            x2, [fp, #-0x10]
    // 0xbf4040: LoadField: r1 = r2->field_f
    //     0xbf4040: ldur            w1, [x2, #0xf]
    // 0xbf4044: DecompressPointer r1
    //     0xbf4044: add             x1, x1, HEAP, lsl #32
    // 0xbf4048: cmp             w1, NULL
    // 0xbf404c: b.eq            #0xbf4e30
    // 0xbf4050: r0 = of()
    //     0xbf4050: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf4054: LoadField: r1 = r0->field_87
    //     0xbf4054: ldur            w1, [x0, #0x87]
    // 0xbf4058: DecompressPointer r1
    //     0xbf4058: add             x1, x1, HEAP, lsl #32
    // 0xbf405c: LoadField: r0 = r1->field_27
    //     0xbf405c: ldur            w0, [x1, #0x27]
    // 0xbf4060: DecompressPointer r0
    //     0xbf4060: add             x0, x0, HEAP, lsl #32
    // 0xbf4064: r16 = Instance_Color
    //     0xbf4064: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbf4068: str             x16, [SP]
    // 0xbf406c: mov             x1, x0
    // 0xbf4070: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xbf4070: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xbf4074: ldr             x4, [x4, #0xf40]
    // 0xbf4078: r0 = copyWith()
    //     0xbf4078: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf407c: stur            x0, [fp, #-0x38]
    // 0xbf4080: r0 = Text()
    //     0xbf4080: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf4084: mov             x1, x0
    // 0xbf4088: r0 = ""
    //     0xbf4088: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf408c: stur            x1, [fp, #-0x50]
    // 0xbf4090: StoreField: r1->field_b = r0
    //     0xbf4090: stur            w0, [x1, #0xb]
    // 0xbf4094: ldur            x2, [fp, #-0x38]
    // 0xbf4098: StoreField: r1->field_13 = r2
    //     0xbf4098: stur            w2, [x1, #0x13]
    // 0xbf409c: r0 = SvgPicture()
    //     0xbf409c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf40a0: stur            x0, [fp, #-0x38]
    // 0xbf40a4: r16 = "user"
    //     0xbf40a4: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb70] "user"
    //     0xbf40a8: ldr             x16, [x16, #0xb70]
    // 0xbf40ac: r30 = Instance_BoxFit
    //     0xbf40ac: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf40b0: ldr             lr, [lr, #0xb18]
    // 0xbf40b4: stp             lr, x16, [SP]
    // 0xbf40b8: mov             x1, x0
    // 0xbf40bc: r2 = "assets/images/user.svg"
    //     0xbf40bc: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb78] "assets/images/user.svg"
    //     0xbf40c0: ldr             x2, [x2, #0xb78]
    // 0xbf40c4: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf40c4: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf40c8: ldr             x4, [x4, #0xb28]
    // 0xbf40cc: r0 = SvgPicture.asset()
    //     0xbf40cc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf40d0: r0 = Badge()
    //     0xbf40d0: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0xbf40d4: mov             x2, x0
    // 0xbf40d8: ldur            x0, [fp, #-0x18]
    // 0xbf40dc: stur            x2, [fp, #-0x58]
    // 0xbf40e0: StoreField: r2->field_b = r0
    //     0xbf40e0: stur            w0, [x2, #0xb]
    // 0xbf40e4: ldur            x0, [fp, #-0x50]
    // 0xbf40e8: StoreField: r2->field_27 = r0
    //     0xbf40e8: stur            w0, [x2, #0x27]
    // 0xbf40ec: r0 = false
    //     0xbf40ec: add             x0, NULL, #0x30  ; false
    // 0xbf40f0: StoreField: r2->field_2b = r0
    //     0xbf40f0: stur            w0, [x2, #0x2b]
    // 0xbf40f4: ldur            x1, [fp, #-0x38]
    // 0xbf40f8: StoreField: r2->field_2f = r1
    //     0xbf40f8: stur            w1, [x2, #0x2f]
    // 0xbf40fc: ldur            x3, [fp, #-0x10]
    // 0xbf4100: LoadField: r1 = r3->field_3f
    //     0xbf4100: ldur            w1, [x3, #0x3f]
    // 0xbf4104: DecompressPointer r1
    //     0xbf4104: add             x1, x1, HEAP, lsl #32
    // 0xbf4108: LoadField: r4 = r1->field_57
    //     0xbf4108: ldur            w4, [x1, #0x57]
    // 0xbf410c: DecompressPointer r4
    //     0xbf410c: add             x4, x4, HEAP, lsl #32
    // 0xbf4110: mov             x1, x4
    // 0xbf4114: r0 = value()
    //     0xbf4114: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4118: LoadField: r1 = r0->field_b
    //     0xbf4118: ldur            w1, [x0, #0xb]
    // 0xbf411c: DecompressPointer r1
    //     0xbf411c: add             x1, x1, HEAP, lsl #32
    // 0xbf4120: cmp             w1, NULL
    // 0xbf4124: b.ne            #0xbf4130
    // 0xbf4128: r0 = Null
    //     0xbf4128: mov             x0, NULL
    // 0xbf412c: b               #0xbf4168
    // 0xbf4130: LoadField: r0 = r1->field_57
    //     0xbf4130: ldur            w0, [x1, #0x57]
    // 0xbf4134: DecompressPointer r0
    //     0xbf4134: add             x0, x0, HEAP, lsl #32
    // 0xbf4138: cmp             w0, NULL
    // 0xbf413c: b.ne            #0xbf4148
    // 0xbf4140: r0 = Null
    //     0xbf4140: mov             x0, NULL
    // 0xbf4144: b               #0xbf4168
    // 0xbf4148: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4148: ldur            w1, [x0, #0x17]
    // 0xbf414c: DecompressPointer r1
    //     0xbf414c: add             x1, x1, HEAP, lsl #32
    // 0xbf4150: cmp             w1, NULL
    // 0xbf4154: b.ne            #0xbf4160
    // 0xbf4158: r0 = Null
    //     0xbf4158: mov             x0, NULL
    // 0xbf415c: b               #0xbf4168
    // 0xbf4160: LoadField: r0 = r1->field_7
    //     0xbf4160: ldur            w0, [x1, #7]
    // 0xbf4164: DecompressPointer r0
    //     0xbf4164: add             x0, x0, HEAP, lsl #32
    // 0xbf4168: cmp             w0, NULL
    // 0xbf416c: b.ne            #0xbf4178
    // 0xbf4170: r0 = 0
    //     0xbf4170: movz            x0, #0
    // 0xbf4174: b               #0xbf4188
    // 0xbf4178: r1 = LoadInt32Instr(r0)
    //     0xbf4178: sbfx            x1, x0, #1, #0x1f
    //     0xbf417c: tbz             w0, #0, #0xbf4184
    //     0xbf4180: ldur            x1, [x0, #7]
    // 0xbf4184: mov             x0, x1
    // 0xbf4188: ldur            x2, [fp, #-0x10]
    // 0xbf418c: stur            x0, [fp, #-0x20]
    // 0xbf4190: LoadField: r1 = r2->field_3f
    //     0xbf4190: ldur            w1, [x2, #0x3f]
    // 0xbf4194: DecompressPointer r1
    //     0xbf4194: add             x1, x1, HEAP, lsl #32
    // 0xbf4198: LoadField: r3 = r1->field_57
    //     0xbf4198: ldur            w3, [x1, #0x57]
    // 0xbf419c: DecompressPointer r3
    //     0xbf419c: add             x3, x3, HEAP, lsl #32
    // 0xbf41a0: mov             x1, x3
    // 0xbf41a4: r0 = value()
    //     0xbf41a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf41a8: LoadField: r1 = r0->field_b
    //     0xbf41a8: ldur            w1, [x0, #0xb]
    // 0xbf41ac: DecompressPointer r1
    //     0xbf41ac: add             x1, x1, HEAP, lsl #32
    // 0xbf41b0: cmp             w1, NULL
    // 0xbf41b4: b.ne            #0xbf41c0
    // 0xbf41b8: r0 = Null
    //     0xbf41b8: mov             x0, NULL
    // 0xbf41bc: b               #0xbf41f8
    // 0xbf41c0: LoadField: r0 = r1->field_57
    //     0xbf41c0: ldur            w0, [x1, #0x57]
    // 0xbf41c4: DecompressPointer r0
    //     0xbf41c4: add             x0, x0, HEAP, lsl #32
    // 0xbf41c8: cmp             w0, NULL
    // 0xbf41cc: b.ne            #0xbf41d8
    // 0xbf41d0: r0 = Null
    //     0xbf41d0: mov             x0, NULL
    // 0xbf41d4: b               #0xbf41f8
    // 0xbf41d8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf41d8: ldur            w1, [x0, #0x17]
    // 0xbf41dc: DecompressPointer r1
    //     0xbf41dc: add             x1, x1, HEAP, lsl #32
    // 0xbf41e0: cmp             w1, NULL
    // 0xbf41e4: b.ne            #0xbf41f0
    // 0xbf41e8: r0 = Null
    //     0xbf41e8: mov             x0, NULL
    // 0xbf41ec: b               #0xbf41f8
    // 0xbf41f0: LoadField: r0 = r1->field_b
    //     0xbf41f0: ldur            w0, [x1, #0xb]
    // 0xbf41f4: DecompressPointer r0
    //     0xbf41f4: add             x0, x0, HEAP, lsl #32
    // 0xbf41f8: cmp             w0, NULL
    // 0xbf41fc: b.ne            #0xbf4208
    // 0xbf4200: r0 = 0
    //     0xbf4200: movz            x0, #0
    // 0xbf4204: b               #0xbf4218
    // 0xbf4208: r1 = LoadInt32Instr(r0)
    //     0xbf4208: sbfx            x1, x0, #1, #0x1f
    //     0xbf420c: tbz             w0, #0, #0xbf4214
    //     0xbf4210: ldur            x1, [x0, #7]
    // 0xbf4214: mov             x0, x1
    // 0xbf4218: ldur            x2, [fp, #-0x10]
    // 0xbf421c: stur            x0, [fp, #-0x28]
    // 0xbf4220: LoadField: r1 = r2->field_3f
    //     0xbf4220: ldur            w1, [x2, #0x3f]
    // 0xbf4224: DecompressPointer r1
    //     0xbf4224: add             x1, x1, HEAP, lsl #32
    // 0xbf4228: LoadField: r3 = r1->field_57
    //     0xbf4228: ldur            w3, [x1, #0x57]
    // 0xbf422c: DecompressPointer r3
    //     0xbf422c: add             x3, x3, HEAP, lsl #32
    // 0xbf4230: mov             x1, x3
    // 0xbf4234: r0 = value()
    //     0xbf4234: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4238: LoadField: r1 = r0->field_b
    //     0xbf4238: ldur            w1, [x0, #0xb]
    // 0xbf423c: DecompressPointer r1
    //     0xbf423c: add             x1, x1, HEAP, lsl #32
    // 0xbf4240: cmp             w1, NULL
    // 0xbf4244: b.ne            #0xbf4250
    // 0xbf4248: r0 = Null
    //     0xbf4248: mov             x0, NULL
    // 0xbf424c: b               #0xbf4288
    // 0xbf4250: LoadField: r0 = r1->field_57
    //     0xbf4250: ldur            w0, [x1, #0x57]
    // 0xbf4254: DecompressPointer r0
    //     0xbf4254: add             x0, x0, HEAP, lsl #32
    // 0xbf4258: cmp             w0, NULL
    // 0xbf425c: b.ne            #0xbf4268
    // 0xbf4260: r0 = Null
    //     0xbf4260: mov             x0, NULL
    // 0xbf4264: b               #0xbf4288
    // 0xbf4268: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4268: ldur            w1, [x0, #0x17]
    // 0xbf426c: DecompressPointer r1
    //     0xbf426c: add             x1, x1, HEAP, lsl #32
    // 0xbf4270: cmp             w1, NULL
    // 0xbf4274: b.ne            #0xbf4280
    // 0xbf4278: r0 = Null
    //     0xbf4278: mov             x0, NULL
    // 0xbf427c: b               #0xbf4288
    // 0xbf4280: LoadField: r0 = r1->field_f
    //     0xbf4280: ldur            w0, [x1, #0xf]
    // 0xbf4284: DecompressPointer r0
    //     0xbf4284: add             x0, x0, HEAP, lsl #32
    // 0xbf4288: cmp             w0, NULL
    // 0xbf428c: b.ne            #0xbf4298
    // 0xbf4290: r1 = 0
    //     0xbf4290: movz            x1, #0
    // 0xbf4294: b               #0xbf42a4
    // 0xbf4298: r1 = LoadInt32Instr(r0)
    //     0xbf4298: sbfx            x1, x0, #1, #0x1f
    //     0xbf429c: tbz             w0, #0, #0xbf42a4
    //     0xbf42a0: ldur            x1, [x0, #7]
    // 0xbf42a4: ldur            x0, [fp, #-0x48]
    // 0xbf42a8: stur            x1, [fp, #-0x30]
    // 0xbf42ac: r0 = Color()
    //     0xbf42ac: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbf42b0: mov             x4, x0
    // 0xbf42b4: r3 = Instance_ColorSpace
    //     0xbf42b4: ldr             x3, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbf42b8: stur            x4, [fp, #-0x38]
    // 0xbf42bc: StoreField: r4->field_27 = r3
    //     0xbf42bc: stur            w3, [x4, #0x27]
    // 0xbf42c0: d0 = 1.000000
    //     0xbf42c0: fmov            d0, #1.00000000
    // 0xbf42c4: StoreField: r4->field_7 = d0
    //     0xbf42c4: stur            d0, [x4, #7]
    // 0xbf42c8: ldur            x0, [fp, #-0x20]
    // 0xbf42cc: ubfx            x0, x0, #0, #0x20
    // 0xbf42d0: and             w1, w0, #0xff
    // 0xbf42d4: ubfx            x1, x1, #0, #0x20
    // 0xbf42d8: scvtf           d1, x1
    // 0xbf42dc: d2 = 255.000000
    //     0xbf42dc: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbf42e0: fdiv            d3, d1, d2
    // 0xbf42e4: StoreField: r4->field_f = d3
    //     0xbf42e4: stur            d3, [x4, #0xf]
    // 0xbf42e8: ldur            x0, [fp, #-0x28]
    // 0xbf42ec: ubfx            x0, x0, #0, #0x20
    // 0xbf42f0: and             w1, w0, #0xff
    // 0xbf42f4: ubfx            x1, x1, #0, #0x20
    // 0xbf42f8: scvtf           d1, x1
    // 0xbf42fc: fdiv            d3, d1, d2
    // 0xbf4300: ArrayStore: r4[0] = d3  ; List_8
    //     0xbf4300: stur            d3, [x4, #0x17]
    // 0xbf4304: ldur            x0, [fp, #-0x30]
    // 0xbf4308: ubfx            x0, x0, #0, #0x20
    // 0xbf430c: and             w1, w0, #0xff
    // 0xbf4310: ubfx            x1, x1, #0, #0x20
    // 0xbf4314: scvtf           d1, x1
    // 0xbf4318: fdiv            d3, d1, d2
    // 0xbf431c: StoreField: r4->field_1f = d3
    //     0xbf431c: stur            d3, [x4, #0x1f]
    // 0xbf4320: ldur            x5, [fp, #-0x48]
    // 0xbf4324: LoadField: r0 = r5->field_b
    //     0xbf4324: ldur            w0, [x5, #0xb]
    // 0xbf4328: r1 = LoadInt32Instr(r0)
    //     0xbf4328: sbfx            x1, x0, #1, #0x1f
    // 0xbf432c: mov             x0, x1
    // 0xbf4330: r1 = 1
    //     0xbf4330: movz            x1, #0x1
    // 0xbf4334: cmp             x1, x0
    // 0xbf4338: b.hs            #0xbf4e34
    // 0xbf433c: LoadField: r0 = r5->field_f
    //     0xbf433c: ldur            w0, [x5, #0xf]
    // 0xbf4340: DecompressPointer r0
    //     0xbf4340: add             x0, x0, HEAP, lsl #32
    // 0xbf4344: LoadField: r6 = r0->field_13
    //     0xbf4344: ldur            w6, [x0, #0x13]
    // 0xbf4348: DecompressPointer r6
    //     0xbf4348: add             x6, x6, HEAP, lsl #32
    // 0xbf434c: mov             x0, x6
    // 0xbf4350: stur            x6, [fp, #-0x18]
    // 0xbf4354: r2 = Null
    //     0xbf4354: mov             x2, NULL
    // 0xbf4358: r1 = Null
    //     0xbf4358: mov             x1, NULL
    // 0xbf435c: r4 = 60
    //     0xbf435c: movz            x4, #0x3c
    // 0xbf4360: branchIfSmi(r0, 0xbf436c)
    //     0xbf4360: tbz             w0, #0, #0xbf436c
    // 0xbf4364: r4 = LoadClassIdInstr(r0)
    //     0xbf4364: ldur            x4, [x0, #-1]
    //     0xbf4368: ubfx            x4, x4, #0xc, #0x14
    // 0xbf436c: r17 = 4530
    //     0xbf436c: movz            x17, #0x11b2
    // 0xbf4370: cmp             x4, x17
    // 0xbf4374: b.eq            #0xbf438c
    // 0xbf4378: r8 = OrdersView
    //     0xbf4378: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0xbf437c: ldr             x8, [x8, #0xb48]
    // 0xbf4380: r3 = Null
    //     0xbf4380: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb80] Null
    //     0xbf4384: ldr             x3, [x3, #0xb80]
    // 0xbf4388: r0 = DefaultTypeTest()
    //     0xbf4388: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xbf438c: ldur            x1, [fp, #-0x18]
    // 0xbf4390: r0 = controller()
    //     0xbf4390: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0xbf4394: mov             x1, x0
    // 0xbf4398: r0 = appConfigResponse()
    //     0xbf4398: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0xbf439c: ldur            x2, [fp, #-0x48]
    // 0xbf43a0: LoadField: r0 = r2->field_b
    //     0xbf43a0: ldur            w0, [x2, #0xb]
    // 0xbf43a4: r1 = LoadInt32Instr(r0)
    //     0xbf43a4: sbfx            x1, x0, #1, #0x1f
    // 0xbf43a8: mov             x0, x1
    // 0xbf43ac: r1 = 1
    //     0xbf43ac: movz            x1, #0x1
    // 0xbf43b0: cmp             x1, x0
    // 0xbf43b4: b.hs            #0xbf4e38
    // 0xbf43b8: LoadField: r0 = r2->field_f
    //     0xbf43b8: ldur            w0, [x2, #0xf]
    // 0xbf43bc: DecompressPointer r0
    //     0xbf43bc: add             x0, x0, HEAP, lsl #32
    // 0xbf43c0: LoadField: r3 = r0->field_13
    //     0xbf43c0: ldur            w3, [x0, #0x13]
    // 0xbf43c4: DecompressPointer r3
    //     0xbf43c4: add             x3, x3, HEAP, lsl #32
    // 0xbf43c8: mov             x0, x3
    // 0xbf43cc: stur            x3, [fp, #-0x18]
    // 0xbf43d0: r2 = Null
    //     0xbf43d0: mov             x2, NULL
    // 0xbf43d4: r1 = Null
    //     0xbf43d4: mov             x1, NULL
    // 0xbf43d8: r4 = 60
    //     0xbf43d8: movz            x4, #0x3c
    // 0xbf43dc: branchIfSmi(r0, 0xbf43e8)
    //     0xbf43dc: tbz             w0, #0, #0xbf43e8
    // 0xbf43e0: r4 = LoadClassIdInstr(r0)
    //     0xbf43e0: ldur            x4, [x0, #-1]
    //     0xbf43e4: ubfx            x4, x4, #0xc, #0x14
    // 0xbf43e8: r17 = 4530
    //     0xbf43e8: movz            x17, #0x11b2
    // 0xbf43ec: cmp             x4, x17
    // 0xbf43f0: b.eq            #0xbf4408
    // 0xbf43f4: r8 = OrdersView
    //     0xbf43f4: add             x8, PP, #0x2c, lsl #12  ; [pp+0x2cb48] Type: OrdersView
    //     0xbf43f8: ldr             x8, [x8, #0xb48]
    // 0xbf43fc: r3 = Null
    //     0xbf43fc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb90] Null
    //     0xbf4400: ldr             x3, [x3, #0xb90]
    // 0xbf4404: r0 = DefaultTypeTest()
    //     0xbf4404: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0xbf4408: r0 = LoadStaticField(0xcb0)
    //     0xbf4408: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf440c: ldr             x0, [x0, #0x1960]
    // 0xbf4410: cmp             w0, NULL
    // 0xbf4414: b.ne            #0xbf4428
    // 0xbf4418: r0 = Instance_GetInstance
    //     0xbf4418: add             x0, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf441c: ldr             x0, [x0, #0x900]
    // 0xbf4420: StoreStaticField(0xcb0, r0)
    //     0xbf4420: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xbf4424: str             x0, [x1, #0x1960]
    // 0xbf4428: ldur            x2, [fp, #-0x10]
    // 0xbf442c: ldur            x0, [fp, #-0x18]
    // 0xbf4430: LoadField: r1 = r0->field_b
    //     0xbf4430: ldur            w1, [x0, #0xb]
    // 0xbf4434: DecompressPointer r1
    //     0xbf4434: add             x1, x1, HEAP, lsl #32
    // 0xbf4438: r16 = Instance_GetInstance
    //     0xbf4438: add             x16, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0xbf443c: ldr             x16, [x16, #0x900]
    // 0xbf4440: stp             x16, x1, [SP, #8]
    // 0xbf4444: str             NULL, [SP]
    // 0xbf4448: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbf4448: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbf444c: r0 = find()
    //     0xbf444c: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0xbf4450: LoadField: r1 = r0->field_5b
    //     0xbf4450: ldur            w1, [x0, #0x5b]
    // 0xbf4454: DecompressPointer r1
    //     0xbf4454: add             x1, x1, HEAP, lsl #32
    // 0xbf4458: r0 = value()
    //     0xbf4458: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf445c: ldur            x2, [fp, #-0x10]
    // 0xbf4460: LoadField: r1 = r2->field_f
    //     0xbf4460: ldur            w1, [x2, #0xf]
    // 0xbf4464: DecompressPointer r1
    //     0xbf4464: add             x1, x1, HEAP, lsl #32
    // 0xbf4468: cmp             w1, NULL
    // 0xbf446c: b.eq            #0xbf4e3c
    // 0xbf4470: r0 = of()
    //     0xbf4470: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf4474: LoadField: r1 = r0->field_87
    //     0xbf4474: ldur            w1, [x0, #0x87]
    // 0xbf4478: DecompressPointer r1
    //     0xbf4478: add             x1, x1, HEAP, lsl #32
    // 0xbf447c: LoadField: r0 = r1->field_27
    //     0xbf447c: ldur            w0, [x1, #0x27]
    // 0xbf4480: DecompressPointer r0
    //     0xbf4480: add             x0, x0, HEAP, lsl #32
    // 0xbf4484: r16 = Instance_Color
    //     0xbf4484: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbf4488: str             x16, [SP]
    // 0xbf448c: mov             x1, x0
    // 0xbf4490: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xbf4490: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xbf4494: ldr             x4, [x4, #0xf40]
    // 0xbf4498: r0 = copyWith()
    //     0xbf4498: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf449c: stur            x0, [fp, #-0x18]
    // 0xbf44a0: r0 = Text()
    //     0xbf44a0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf44a4: mov             x2, x0
    // 0xbf44a8: r0 = ""
    //     0xbf44a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf44ac: stur            x2, [fp, #-0x48]
    // 0xbf44b0: StoreField: r2->field_b = r0
    //     0xbf44b0: stur            w0, [x2, #0xb]
    // 0xbf44b4: ldur            x0, [fp, #-0x18]
    // 0xbf44b8: StoreField: r2->field_13 = r0
    //     0xbf44b8: stur            w0, [x2, #0x13]
    // 0xbf44bc: ldur            x0, [fp, #-0x10]
    // 0xbf44c0: LoadField: r1 = r0->field_3f
    //     0xbf44c0: ldur            w1, [x0, #0x3f]
    // 0xbf44c4: DecompressPointer r1
    //     0xbf44c4: add             x1, x1, HEAP, lsl #32
    // 0xbf44c8: LoadField: r3 = r1->field_57
    //     0xbf44c8: ldur            w3, [x1, #0x57]
    // 0xbf44cc: DecompressPointer r3
    //     0xbf44cc: add             x3, x3, HEAP, lsl #32
    // 0xbf44d0: mov             x1, x3
    // 0xbf44d4: r0 = value()
    //     0xbf44d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf44d8: LoadField: r1 = r0->field_b
    //     0xbf44d8: ldur            w1, [x0, #0xb]
    // 0xbf44dc: DecompressPointer r1
    //     0xbf44dc: add             x1, x1, HEAP, lsl #32
    // 0xbf44e0: cmp             w1, NULL
    // 0xbf44e4: b.ne            #0xbf44f0
    // 0xbf44e8: r0 = Null
    //     0xbf44e8: mov             x0, NULL
    // 0xbf44ec: b               #0xbf4528
    // 0xbf44f0: LoadField: r0 = r1->field_57
    //     0xbf44f0: ldur            w0, [x1, #0x57]
    // 0xbf44f4: DecompressPointer r0
    //     0xbf44f4: add             x0, x0, HEAP, lsl #32
    // 0xbf44f8: cmp             w0, NULL
    // 0xbf44fc: b.ne            #0xbf4508
    // 0xbf4500: r0 = Null
    //     0xbf4500: mov             x0, NULL
    // 0xbf4504: b               #0xbf4528
    // 0xbf4508: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4508: ldur            w1, [x0, #0x17]
    // 0xbf450c: DecompressPointer r1
    //     0xbf450c: add             x1, x1, HEAP, lsl #32
    // 0xbf4510: cmp             w1, NULL
    // 0xbf4514: b.ne            #0xbf4520
    // 0xbf4518: r0 = Null
    //     0xbf4518: mov             x0, NULL
    // 0xbf451c: b               #0xbf4528
    // 0xbf4520: LoadField: r0 = r1->field_7
    //     0xbf4520: ldur            w0, [x1, #7]
    // 0xbf4524: DecompressPointer r0
    //     0xbf4524: add             x0, x0, HEAP, lsl #32
    // 0xbf4528: cmp             w0, NULL
    // 0xbf452c: b.ne            #0xbf4538
    // 0xbf4530: r0 = 0
    //     0xbf4530: movz            x0, #0
    // 0xbf4534: b               #0xbf4548
    // 0xbf4538: r1 = LoadInt32Instr(r0)
    //     0xbf4538: sbfx            x1, x0, #1, #0x1f
    //     0xbf453c: tbz             w0, #0, #0xbf4544
    //     0xbf4540: ldur            x1, [x0, #7]
    // 0xbf4544: mov             x0, x1
    // 0xbf4548: ldur            x2, [fp, #-0x10]
    // 0xbf454c: stur            x0, [fp, #-0x20]
    // 0xbf4550: LoadField: r1 = r2->field_3f
    //     0xbf4550: ldur            w1, [x2, #0x3f]
    // 0xbf4554: DecompressPointer r1
    //     0xbf4554: add             x1, x1, HEAP, lsl #32
    // 0xbf4558: LoadField: r3 = r1->field_57
    //     0xbf4558: ldur            w3, [x1, #0x57]
    // 0xbf455c: DecompressPointer r3
    //     0xbf455c: add             x3, x3, HEAP, lsl #32
    // 0xbf4560: mov             x1, x3
    // 0xbf4564: r0 = value()
    //     0xbf4564: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4568: LoadField: r1 = r0->field_b
    //     0xbf4568: ldur            w1, [x0, #0xb]
    // 0xbf456c: DecompressPointer r1
    //     0xbf456c: add             x1, x1, HEAP, lsl #32
    // 0xbf4570: cmp             w1, NULL
    // 0xbf4574: b.ne            #0xbf4580
    // 0xbf4578: r0 = Null
    //     0xbf4578: mov             x0, NULL
    // 0xbf457c: b               #0xbf45b8
    // 0xbf4580: LoadField: r0 = r1->field_57
    //     0xbf4580: ldur            w0, [x1, #0x57]
    // 0xbf4584: DecompressPointer r0
    //     0xbf4584: add             x0, x0, HEAP, lsl #32
    // 0xbf4588: cmp             w0, NULL
    // 0xbf458c: b.ne            #0xbf4598
    // 0xbf4590: r0 = Null
    //     0xbf4590: mov             x0, NULL
    // 0xbf4594: b               #0xbf45b8
    // 0xbf4598: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4598: ldur            w1, [x0, #0x17]
    // 0xbf459c: DecompressPointer r1
    //     0xbf459c: add             x1, x1, HEAP, lsl #32
    // 0xbf45a0: cmp             w1, NULL
    // 0xbf45a4: b.ne            #0xbf45b0
    // 0xbf45a8: r0 = Null
    //     0xbf45a8: mov             x0, NULL
    // 0xbf45ac: b               #0xbf45b8
    // 0xbf45b0: LoadField: r0 = r1->field_b
    //     0xbf45b0: ldur            w0, [x1, #0xb]
    // 0xbf45b4: DecompressPointer r0
    //     0xbf45b4: add             x0, x0, HEAP, lsl #32
    // 0xbf45b8: cmp             w0, NULL
    // 0xbf45bc: b.ne            #0xbf45c8
    // 0xbf45c0: r0 = 0
    //     0xbf45c0: movz            x0, #0
    // 0xbf45c4: b               #0xbf45d8
    // 0xbf45c8: r1 = LoadInt32Instr(r0)
    //     0xbf45c8: sbfx            x1, x0, #1, #0x1f
    //     0xbf45cc: tbz             w0, #0, #0xbf45d4
    //     0xbf45d0: ldur            x1, [x0, #7]
    // 0xbf45d4: mov             x0, x1
    // 0xbf45d8: ldur            x2, [fp, #-0x10]
    // 0xbf45dc: stur            x0, [fp, #-0x28]
    // 0xbf45e0: LoadField: r1 = r2->field_3f
    //     0xbf45e0: ldur            w1, [x2, #0x3f]
    // 0xbf45e4: DecompressPointer r1
    //     0xbf45e4: add             x1, x1, HEAP, lsl #32
    // 0xbf45e8: LoadField: r3 = r1->field_57
    //     0xbf45e8: ldur            w3, [x1, #0x57]
    // 0xbf45ec: DecompressPointer r3
    //     0xbf45ec: add             x3, x3, HEAP, lsl #32
    // 0xbf45f0: mov             x1, x3
    // 0xbf45f4: r0 = value()
    //     0xbf45f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf45f8: LoadField: r1 = r0->field_b
    //     0xbf45f8: ldur            w1, [x0, #0xb]
    // 0xbf45fc: DecompressPointer r1
    //     0xbf45fc: add             x1, x1, HEAP, lsl #32
    // 0xbf4600: cmp             w1, NULL
    // 0xbf4604: b.ne            #0xbf4610
    // 0xbf4608: r0 = Null
    //     0xbf4608: mov             x0, NULL
    // 0xbf460c: b               #0xbf4648
    // 0xbf4610: LoadField: r0 = r1->field_57
    //     0xbf4610: ldur            w0, [x1, #0x57]
    // 0xbf4614: DecompressPointer r0
    //     0xbf4614: add             x0, x0, HEAP, lsl #32
    // 0xbf4618: cmp             w0, NULL
    // 0xbf461c: b.ne            #0xbf4628
    // 0xbf4620: r0 = Null
    //     0xbf4620: mov             x0, NULL
    // 0xbf4624: b               #0xbf4648
    // 0xbf4628: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4628: ldur            w1, [x0, #0x17]
    // 0xbf462c: DecompressPointer r1
    //     0xbf462c: add             x1, x1, HEAP, lsl #32
    // 0xbf4630: cmp             w1, NULL
    // 0xbf4634: b.ne            #0xbf4640
    // 0xbf4638: r0 = Null
    //     0xbf4638: mov             x0, NULL
    // 0xbf463c: b               #0xbf4648
    // 0xbf4640: LoadField: r0 = r1->field_f
    //     0xbf4640: ldur            w0, [x1, #0xf]
    // 0xbf4644: DecompressPointer r0
    //     0xbf4644: add             x0, x0, HEAP, lsl #32
    // 0xbf4648: cmp             w0, NULL
    // 0xbf464c: b.ne            #0xbf4658
    // 0xbf4650: r4 = 0
    //     0xbf4650: movz            x4, #0
    // 0xbf4654: b               #0xbf4668
    // 0xbf4658: r1 = LoadInt32Instr(r0)
    //     0xbf4658: sbfx            x1, x0, #1, #0x1f
    //     0xbf465c: tbz             w0, #0, #0xbf4664
    //     0xbf4660: ldur            x1, [x0, #7]
    // 0xbf4664: mov             x4, x1
    // 0xbf4668: ldur            x2, [fp, #-0x10]
    // 0xbf466c: ldur            x3, [fp, #-0x58]
    // 0xbf4670: ldur            x1, [fp, #-0x38]
    // 0xbf4674: ldur            x0, [fp, #-0x48]
    // 0xbf4678: stur            x4, [fp, #-0x30]
    // 0xbf467c: r0 = Color()
    //     0xbf467c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbf4680: mov             x1, x0
    // 0xbf4684: r0 = Instance_ColorSpace
    //     0xbf4684: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbf4688: stur            x1, [fp, #-0x18]
    // 0xbf468c: StoreField: r1->field_27 = r0
    //     0xbf468c: stur            w0, [x1, #0x27]
    // 0xbf4690: d0 = 1.000000
    //     0xbf4690: fmov            d0, #1.00000000
    // 0xbf4694: StoreField: r1->field_7 = d0
    //     0xbf4694: stur            d0, [x1, #7]
    // 0xbf4698: ldur            x2, [fp, #-0x20]
    // 0xbf469c: ubfx            x2, x2, #0, #0x20
    // 0xbf46a0: and             w3, w2, #0xff
    // 0xbf46a4: ubfx            x3, x3, #0, #0x20
    // 0xbf46a8: scvtf           d1, x3
    // 0xbf46ac: d2 = 255.000000
    //     0xbf46ac: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbf46b0: fdiv            d3, d1, d2
    // 0xbf46b4: StoreField: r1->field_f = d3
    //     0xbf46b4: stur            d3, [x1, #0xf]
    // 0xbf46b8: ldur            x2, [fp, #-0x28]
    // 0xbf46bc: ubfx            x2, x2, #0, #0x20
    // 0xbf46c0: and             w3, w2, #0xff
    // 0xbf46c4: ubfx            x3, x3, #0, #0x20
    // 0xbf46c8: scvtf           d1, x3
    // 0xbf46cc: fdiv            d3, d1, d2
    // 0xbf46d0: ArrayStore: r1[0] = d3  ; List_8
    //     0xbf46d0: stur            d3, [x1, #0x17]
    // 0xbf46d4: ldur            x2, [fp, #-0x30]
    // 0xbf46d8: ubfx            x2, x2, #0, #0x20
    // 0xbf46dc: and             w3, w2, #0xff
    // 0xbf46e0: ubfx            x3, x3, #0, #0x20
    // 0xbf46e4: scvtf           d1, x3
    // 0xbf46e8: fdiv            d3, d1, d2
    // 0xbf46ec: StoreField: r1->field_1f = d3
    //     0xbf46ec: stur            d3, [x1, #0x1f]
    // 0xbf46f0: r0 = ColorFilter()
    //     0xbf46f0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbf46f4: mov             x1, x0
    // 0xbf46f8: ldur            x0, [fp, #-0x18]
    // 0xbf46fc: stur            x1, [fp, #-0x50]
    // 0xbf4700: StoreField: r1->field_7 = r0
    //     0xbf4700: stur            w0, [x1, #7]
    // 0xbf4704: r0 = Instance_BlendMode
    //     0xbf4704: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbf4708: ldr             x0, [x0, #0xb30]
    // 0xbf470c: StoreField: r1->field_b = r0
    //     0xbf470c: stur            w0, [x1, #0xb]
    // 0xbf4710: r2 = 1
    //     0xbf4710: movz            x2, #0x1
    // 0xbf4714: StoreField: r1->field_13 = r2
    //     0xbf4714: stur            x2, [x1, #0x13]
    // 0xbf4718: r0 = SvgPicture()
    //     0xbf4718: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf471c: stur            x0, [fp, #-0x18]
    // 0xbf4720: r16 = "user"
    //     0xbf4720: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb70] "user"
    //     0xbf4724: ldr             x16, [x16, #0xb70]
    // 0xbf4728: r30 = Instance_BoxFit
    //     0xbf4728: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf472c: ldr             lr, [lr, #0xb18]
    // 0xbf4730: stp             lr, x16, [SP, #8]
    // 0xbf4734: ldur            x16, [fp, #-0x50]
    // 0xbf4738: str             x16, [SP]
    // 0xbf473c: mov             x1, x0
    // 0xbf4740: r2 = "assets/images/user.svg"
    //     0xbf4740: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb78] "assets/images/user.svg"
    //     0xbf4744: ldr             x2, [x2, #0xb78]
    // 0xbf4748: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf4748: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf474c: ldr             x4, [x4, #0xb38]
    // 0xbf4750: r0 = SvgPicture.asset()
    //     0xbf4750: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf4754: r0 = Badge()
    //     0xbf4754: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0xbf4758: mov             x1, x0
    // 0xbf475c: ldur            x0, [fp, #-0x38]
    // 0xbf4760: stur            x1, [fp, #-0x50]
    // 0xbf4764: StoreField: r1->field_b = r0
    //     0xbf4764: stur            w0, [x1, #0xb]
    // 0xbf4768: ldur            x0, [fp, #-0x48]
    // 0xbf476c: StoreField: r1->field_27 = r0
    //     0xbf476c: stur            w0, [x1, #0x27]
    // 0xbf4770: r0 = false
    //     0xbf4770: add             x0, NULL, #0x30  ; false
    // 0xbf4774: StoreField: r1->field_2b = r0
    //     0xbf4774: stur            w0, [x1, #0x2b]
    // 0xbf4778: ldur            x0, [fp, #-0x18]
    // 0xbf477c: StoreField: r1->field_2f = r0
    //     0xbf477c: stur            w0, [x1, #0x2f]
    // 0xbf4780: r0 = BottomNavigationBarItem()
    //     0xbf4780: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xbf4784: mov             x1, x0
    // 0xbf4788: ldur            x0, [fp, #-0x58]
    // 0xbf478c: stur            x1, [fp, #-0x18]
    // 0xbf4790: StoreField: r1->field_b = r0
    //     0xbf4790: stur            w0, [x1, #0xb]
    // 0xbf4794: r0 = "Order"
    //     0xbf4794: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cba0] "Order"
    //     0xbf4798: ldr             x0, [x0, #0xba0]
    // 0xbf479c: StoreField: r1->field_13 = r0
    //     0xbf479c: stur            w0, [x1, #0x13]
    // 0xbf47a0: r0 = "1"
    //     0xbf47a0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cba8] "1"
    //     0xbf47a4: ldr             x0, [x0, #0xba8]
    // 0xbf47a8: StoreField: r1->field_1b = r0
    //     0xbf47a8: stur            w0, [x1, #0x1b]
    // 0xbf47ac: ldur            x0, [fp, #-0x50]
    // 0xbf47b0: StoreField: r1->field_f = r0
    //     0xbf47b0: stur            w0, [x1, #0xf]
    // 0xbf47b4: r0 = SvgPicture()
    //     0xbf47b4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf47b8: stur            x0, [fp, #-0x38]
    // 0xbf47bc: r16 = "profile"
    //     0xbf47bc: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbb0] "profile"
    //     0xbf47c0: ldr             x16, [x16, #0xbb0]
    // 0xbf47c4: r30 = Instance_BoxFit
    //     0xbf47c4: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf47c8: ldr             lr, [lr, #0xb18]
    // 0xbf47cc: stp             lr, x16, [SP]
    // 0xbf47d0: mov             x1, x0
    // 0xbf47d4: r2 = "assets/images/profile_circle.svg"
    //     0xbf47d4: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbb8] "assets/images/profile_circle.svg"
    //     0xbf47d8: ldr             x2, [x2, #0xbb8]
    // 0xbf47dc: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf47dc: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf47e0: ldr             x4, [x4, #0xb28]
    // 0xbf47e4: r0 = SvgPicture.asset()
    //     0xbf47e4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf47e8: ldur            x2, [fp, #-0x10]
    // 0xbf47ec: LoadField: r0 = r2->field_3f
    //     0xbf47ec: ldur            w0, [x2, #0x3f]
    // 0xbf47f0: DecompressPointer r0
    //     0xbf47f0: add             x0, x0, HEAP, lsl #32
    // 0xbf47f4: LoadField: r1 = r0->field_57
    //     0xbf47f4: ldur            w1, [x0, #0x57]
    // 0xbf47f8: DecompressPointer r1
    //     0xbf47f8: add             x1, x1, HEAP, lsl #32
    // 0xbf47fc: r0 = value()
    //     0xbf47fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4800: LoadField: r1 = r0->field_b
    //     0xbf4800: ldur            w1, [x0, #0xb]
    // 0xbf4804: DecompressPointer r1
    //     0xbf4804: add             x1, x1, HEAP, lsl #32
    // 0xbf4808: cmp             w1, NULL
    // 0xbf480c: b.ne            #0xbf4818
    // 0xbf4810: r0 = Null
    //     0xbf4810: mov             x0, NULL
    // 0xbf4814: b               #0xbf4850
    // 0xbf4818: LoadField: r0 = r1->field_57
    //     0xbf4818: ldur            w0, [x1, #0x57]
    // 0xbf481c: DecompressPointer r0
    //     0xbf481c: add             x0, x0, HEAP, lsl #32
    // 0xbf4820: cmp             w0, NULL
    // 0xbf4824: b.ne            #0xbf4830
    // 0xbf4828: r0 = Null
    //     0xbf4828: mov             x0, NULL
    // 0xbf482c: b               #0xbf4850
    // 0xbf4830: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4830: ldur            w1, [x0, #0x17]
    // 0xbf4834: DecompressPointer r1
    //     0xbf4834: add             x1, x1, HEAP, lsl #32
    // 0xbf4838: cmp             w1, NULL
    // 0xbf483c: b.ne            #0xbf4848
    // 0xbf4840: r0 = Null
    //     0xbf4840: mov             x0, NULL
    // 0xbf4844: b               #0xbf4850
    // 0xbf4848: LoadField: r0 = r1->field_7
    //     0xbf4848: ldur            w0, [x1, #7]
    // 0xbf484c: DecompressPointer r0
    //     0xbf484c: add             x0, x0, HEAP, lsl #32
    // 0xbf4850: cmp             w0, NULL
    // 0xbf4854: b.ne            #0xbf4860
    // 0xbf4858: r0 = 0
    //     0xbf4858: movz            x0, #0
    // 0xbf485c: b               #0xbf4870
    // 0xbf4860: r1 = LoadInt32Instr(r0)
    //     0xbf4860: sbfx            x1, x0, #1, #0x1f
    //     0xbf4864: tbz             w0, #0, #0xbf486c
    //     0xbf4868: ldur            x1, [x0, #7]
    // 0xbf486c: mov             x0, x1
    // 0xbf4870: ldur            x2, [fp, #-0x10]
    // 0xbf4874: stur            x0, [fp, #-0x20]
    // 0xbf4878: LoadField: r1 = r2->field_3f
    //     0xbf4878: ldur            w1, [x2, #0x3f]
    // 0xbf487c: DecompressPointer r1
    //     0xbf487c: add             x1, x1, HEAP, lsl #32
    // 0xbf4880: LoadField: r3 = r1->field_57
    //     0xbf4880: ldur            w3, [x1, #0x57]
    // 0xbf4884: DecompressPointer r3
    //     0xbf4884: add             x3, x3, HEAP, lsl #32
    // 0xbf4888: mov             x1, x3
    // 0xbf488c: r0 = value()
    //     0xbf488c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4890: LoadField: r1 = r0->field_b
    //     0xbf4890: ldur            w1, [x0, #0xb]
    // 0xbf4894: DecompressPointer r1
    //     0xbf4894: add             x1, x1, HEAP, lsl #32
    // 0xbf4898: cmp             w1, NULL
    // 0xbf489c: b.ne            #0xbf48a8
    // 0xbf48a0: r0 = Null
    //     0xbf48a0: mov             x0, NULL
    // 0xbf48a4: b               #0xbf48e0
    // 0xbf48a8: LoadField: r0 = r1->field_57
    //     0xbf48a8: ldur            w0, [x1, #0x57]
    // 0xbf48ac: DecompressPointer r0
    //     0xbf48ac: add             x0, x0, HEAP, lsl #32
    // 0xbf48b0: cmp             w0, NULL
    // 0xbf48b4: b.ne            #0xbf48c0
    // 0xbf48b8: r0 = Null
    //     0xbf48b8: mov             x0, NULL
    // 0xbf48bc: b               #0xbf48e0
    // 0xbf48c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf48c0: ldur            w1, [x0, #0x17]
    // 0xbf48c4: DecompressPointer r1
    //     0xbf48c4: add             x1, x1, HEAP, lsl #32
    // 0xbf48c8: cmp             w1, NULL
    // 0xbf48cc: b.ne            #0xbf48d8
    // 0xbf48d0: r0 = Null
    //     0xbf48d0: mov             x0, NULL
    // 0xbf48d4: b               #0xbf48e0
    // 0xbf48d8: LoadField: r0 = r1->field_b
    //     0xbf48d8: ldur            w0, [x1, #0xb]
    // 0xbf48dc: DecompressPointer r0
    //     0xbf48dc: add             x0, x0, HEAP, lsl #32
    // 0xbf48e0: cmp             w0, NULL
    // 0xbf48e4: b.ne            #0xbf48f0
    // 0xbf48e8: r0 = 0
    //     0xbf48e8: movz            x0, #0
    // 0xbf48ec: b               #0xbf4900
    // 0xbf48f0: r1 = LoadInt32Instr(r0)
    //     0xbf48f0: sbfx            x1, x0, #1, #0x1f
    //     0xbf48f4: tbz             w0, #0, #0xbf48fc
    //     0xbf48f8: ldur            x1, [x0, #7]
    // 0xbf48fc: mov             x0, x1
    // 0xbf4900: ldur            x2, [fp, #-0x10]
    // 0xbf4904: stur            x0, [fp, #-0x28]
    // 0xbf4908: LoadField: r1 = r2->field_3f
    //     0xbf4908: ldur            w1, [x2, #0x3f]
    // 0xbf490c: DecompressPointer r1
    //     0xbf490c: add             x1, x1, HEAP, lsl #32
    // 0xbf4910: LoadField: r3 = r1->field_57
    //     0xbf4910: ldur            w3, [x1, #0x57]
    // 0xbf4914: DecompressPointer r3
    //     0xbf4914: add             x3, x3, HEAP, lsl #32
    // 0xbf4918: mov             x1, x3
    // 0xbf491c: r0 = value()
    //     0xbf491c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4920: LoadField: r1 = r0->field_b
    //     0xbf4920: ldur            w1, [x0, #0xb]
    // 0xbf4924: DecompressPointer r1
    //     0xbf4924: add             x1, x1, HEAP, lsl #32
    // 0xbf4928: cmp             w1, NULL
    // 0xbf492c: b.ne            #0xbf4938
    // 0xbf4930: r0 = Null
    //     0xbf4930: mov             x0, NULL
    // 0xbf4934: b               #0xbf4970
    // 0xbf4938: LoadField: r0 = r1->field_57
    //     0xbf4938: ldur            w0, [x1, #0x57]
    // 0xbf493c: DecompressPointer r0
    //     0xbf493c: add             x0, x0, HEAP, lsl #32
    // 0xbf4940: cmp             w0, NULL
    // 0xbf4944: b.ne            #0xbf4950
    // 0xbf4948: r0 = Null
    //     0xbf4948: mov             x0, NULL
    // 0xbf494c: b               #0xbf4970
    // 0xbf4950: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4950: ldur            w1, [x0, #0x17]
    // 0xbf4954: DecompressPointer r1
    //     0xbf4954: add             x1, x1, HEAP, lsl #32
    // 0xbf4958: cmp             w1, NULL
    // 0xbf495c: b.ne            #0xbf4968
    // 0xbf4960: r0 = Null
    //     0xbf4960: mov             x0, NULL
    // 0xbf4964: b               #0xbf4970
    // 0xbf4968: LoadField: r0 = r1->field_f
    //     0xbf4968: ldur            w0, [x1, #0xf]
    // 0xbf496c: DecompressPointer r0
    //     0xbf496c: add             x0, x0, HEAP, lsl #32
    // 0xbf4970: cmp             w0, NULL
    // 0xbf4974: b.ne            #0xbf4980
    // 0xbf4978: r1 = 0
    //     0xbf4978: movz            x1, #0
    // 0xbf497c: b               #0xbf498c
    // 0xbf4980: r1 = LoadInt32Instr(r0)
    //     0xbf4980: sbfx            x1, x0, #1, #0x1f
    //     0xbf4984: tbz             w0, #0, #0xbf498c
    //     0xbf4988: ldur            x1, [x0, #7]
    // 0xbf498c: ldur            x2, [fp, #-0x10]
    // 0xbf4990: ldur            x0, [fp, #-0x38]
    // 0xbf4994: stur            x1, [fp, #-0x30]
    // 0xbf4998: r0 = Color()
    //     0xbf4998: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbf499c: mov             x1, x0
    // 0xbf49a0: r0 = Instance_ColorSpace
    //     0xbf49a0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbf49a4: stur            x1, [fp, #-0x48]
    // 0xbf49a8: StoreField: r1->field_27 = r0
    //     0xbf49a8: stur            w0, [x1, #0x27]
    // 0xbf49ac: d0 = 1.000000
    //     0xbf49ac: fmov            d0, #1.00000000
    // 0xbf49b0: StoreField: r1->field_7 = d0
    //     0xbf49b0: stur            d0, [x1, #7]
    // 0xbf49b4: ldur            x2, [fp, #-0x20]
    // 0xbf49b8: ubfx            x2, x2, #0, #0x20
    // 0xbf49bc: and             w3, w2, #0xff
    // 0xbf49c0: ubfx            x3, x3, #0, #0x20
    // 0xbf49c4: scvtf           d1, x3
    // 0xbf49c8: d2 = 255.000000
    //     0xbf49c8: ldr             d2, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbf49cc: fdiv            d3, d1, d2
    // 0xbf49d0: StoreField: r1->field_f = d3
    //     0xbf49d0: stur            d3, [x1, #0xf]
    // 0xbf49d4: ldur            x2, [fp, #-0x28]
    // 0xbf49d8: ubfx            x2, x2, #0, #0x20
    // 0xbf49dc: and             w3, w2, #0xff
    // 0xbf49e0: ubfx            x3, x3, #0, #0x20
    // 0xbf49e4: scvtf           d1, x3
    // 0xbf49e8: fdiv            d3, d1, d2
    // 0xbf49ec: ArrayStore: r1[0] = d3  ; List_8
    //     0xbf49ec: stur            d3, [x1, #0x17]
    // 0xbf49f0: ldur            x2, [fp, #-0x30]
    // 0xbf49f4: ubfx            x2, x2, #0, #0x20
    // 0xbf49f8: and             w3, w2, #0xff
    // 0xbf49fc: ubfx            x3, x3, #0, #0x20
    // 0xbf4a00: scvtf           d1, x3
    // 0xbf4a04: fdiv            d3, d1, d2
    // 0xbf4a08: StoreField: r1->field_1f = d3
    //     0xbf4a08: stur            d3, [x1, #0x1f]
    // 0xbf4a0c: r0 = ColorFilter()
    //     0xbf4a0c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbf4a10: mov             x1, x0
    // 0xbf4a14: ldur            x0, [fp, #-0x48]
    // 0xbf4a18: stur            x1, [fp, #-0x50]
    // 0xbf4a1c: StoreField: r1->field_7 = r0
    //     0xbf4a1c: stur            w0, [x1, #7]
    // 0xbf4a20: r0 = Instance_BlendMode
    //     0xbf4a20: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbf4a24: ldr             x0, [x0, #0xb30]
    // 0xbf4a28: StoreField: r1->field_b = r0
    //     0xbf4a28: stur            w0, [x1, #0xb]
    // 0xbf4a2c: r2 = 1
    //     0xbf4a2c: movz            x2, #0x1
    // 0xbf4a30: StoreField: r1->field_13 = r2
    //     0xbf4a30: stur            x2, [x1, #0x13]
    // 0xbf4a34: r0 = SvgPicture()
    //     0xbf4a34: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf4a38: stur            x0, [fp, #-0x48]
    // 0xbf4a3c: r16 = "profile"
    //     0xbf4a3c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbb0] "profile"
    //     0xbf4a40: ldr             x16, [x16, #0xbb0]
    // 0xbf4a44: r30 = Instance_BoxFit
    //     0xbf4a44: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf4a48: ldr             lr, [lr, #0xb18]
    // 0xbf4a4c: stp             lr, x16, [SP, #8]
    // 0xbf4a50: ldur            x16, [fp, #-0x50]
    // 0xbf4a54: str             x16, [SP]
    // 0xbf4a58: mov             x1, x0
    // 0xbf4a5c: r2 = "assets/images/profile_circle.svg"
    //     0xbf4a5c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbb8] "assets/images/profile_circle.svg"
    //     0xbf4a60: ldr             x2, [x2, #0xbb8]
    // 0xbf4a64: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf4a64: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf4a68: ldr             x4, [x4, #0xb38]
    // 0xbf4a6c: r0 = SvgPicture.asset()
    //     0xbf4a6c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf4a70: r0 = BottomNavigationBarItem()
    //     0xbf4a70: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xbf4a74: mov             x1, x0
    // 0xbf4a78: ldur            x0, [fp, #-0x38]
    // 0xbf4a7c: stur            x1, [fp, #-0x50]
    // 0xbf4a80: StoreField: r1->field_b = r0
    //     0xbf4a80: stur            w0, [x1, #0xb]
    // 0xbf4a84: r0 = "Profile"
    //     0xbf4a84: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cbc0] "Profile"
    //     0xbf4a88: ldr             x0, [x0, #0xbc0]
    // 0xbf4a8c: StoreField: r1->field_13 = r0
    //     0xbf4a8c: stur            w0, [x1, #0x13]
    // 0xbf4a90: ldur            x0, [fp, #-0x48]
    // 0xbf4a94: StoreField: r1->field_f = r0
    //     0xbf4a94: stur            w0, [x1, #0xf]
    // 0xbf4a98: r0 = SvgPicture()
    //     0xbf4a98: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf4a9c: stur            x0, [fp, #-0x38]
    // 0xbf4aa0: r16 = "menu"
    //     0xbf4aa0: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbc8] "menu"
    //     0xbf4aa4: ldr             x16, [x16, #0xbc8]
    // 0xbf4aa8: r30 = Instance_BoxFit
    //     0xbf4aa8: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf4aac: ldr             lr, [lr, #0xb18]
    // 0xbf4ab0: stp             lr, x16, [SP]
    // 0xbf4ab4: mov             x1, x0
    // 0xbf4ab8: r2 = "assets/images/menu.svg"
    //     0xbf4ab8: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbd0] "assets/images/menu.svg"
    //     0xbf4abc: ldr             x2, [x2, #0xbd0]
    // 0xbf4ac0: r4 = const [0, 0x4, 0x2, 0x2, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf4ac0: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb28] List(9) [0, 0x4, 0x2, 0x2, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf4ac4: ldr             x4, [x4, #0xb28]
    // 0xbf4ac8: r0 = SvgPicture.asset()
    //     0xbf4ac8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf4acc: ldur            x2, [fp, #-0x10]
    // 0xbf4ad0: LoadField: r0 = r2->field_3f
    //     0xbf4ad0: ldur            w0, [x2, #0x3f]
    // 0xbf4ad4: DecompressPointer r0
    //     0xbf4ad4: add             x0, x0, HEAP, lsl #32
    // 0xbf4ad8: LoadField: r1 = r0->field_57
    //     0xbf4ad8: ldur            w1, [x0, #0x57]
    // 0xbf4adc: DecompressPointer r1
    //     0xbf4adc: add             x1, x1, HEAP, lsl #32
    // 0xbf4ae0: r0 = value()
    //     0xbf4ae0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4ae4: LoadField: r1 = r0->field_b
    //     0xbf4ae4: ldur            w1, [x0, #0xb]
    // 0xbf4ae8: DecompressPointer r1
    //     0xbf4ae8: add             x1, x1, HEAP, lsl #32
    // 0xbf4aec: cmp             w1, NULL
    // 0xbf4af0: b.ne            #0xbf4afc
    // 0xbf4af4: r0 = Null
    //     0xbf4af4: mov             x0, NULL
    // 0xbf4af8: b               #0xbf4b34
    // 0xbf4afc: LoadField: r0 = r1->field_57
    //     0xbf4afc: ldur            w0, [x1, #0x57]
    // 0xbf4b00: DecompressPointer r0
    //     0xbf4b00: add             x0, x0, HEAP, lsl #32
    // 0xbf4b04: cmp             w0, NULL
    // 0xbf4b08: b.ne            #0xbf4b14
    // 0xbf4b0c: r0 = Null
    //     0xbf4b0c: mov             x0, NULL
    // 0xbf4b10: b               #0xbf4b34
    // 0xbf4b14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4b14: ldur            w1, [x0, #0x17]
    // 0xbf4b18: DecompressPointer r1
    //     0xbf4b18: add             x1, x1, HEAP, lsl #32
    // 0xbf4b1c: cmp             w1, NULL
    // 0xbf4b20: b.ne            #0xbf4b2c
    // 0xbf4b24: r0 = Null
    //     0xbf4b24: mov             x0, NULL
    // 0xbf4b28: b               #0xbf4b34
    // 0xbf4b2c: LoadField: r0 = r1->field_7
    //     0xbf4b2c: ldur            w0, [x1, #7]
    // 0xbf4b30: DecompressPointer r0
    //     0xbf4b30: add             x0, x0, HEAP, lsl #32
    // 0xbf4b34: cmp             w0, NULL
    // 0xbf4b38: b.ne            #0xbf4b44
    // 0xbf4b3c: r0 = 0
    //     0xbf4b3c: movz            x0, #0
    // 0xbf4b40: b               #0xbf4b54
    // 0xbf4b44: r1 = LoadInt32Instr(r0)
    //     0xbf4b44: sbfx            x1, x0, #1, #0x1f
    //     0xbf4b48: tbz             w0, #0, #0xbf4b50
    //     0xbf4b4c: ldur            x1, [x0, #7]
    // 0xbf4b50: mov             x0, x1
    // 0xbf4b54: ldur            x2, [fp, #-0x10]
    // 0xbf4b58: stur            x0, [fp, #-0x20]
    // 0xbf4b5c: LoadField: r1 = r2->field_3f
    //     0xbf4b5c: ldur            w1, [x2, #0x3f]
    // 0xbf4b60: DecompressPointer r1
    //     0xbf4b60: add             x1, x1, HEAP, lsl #32
    // 0xbf4b64: LoadField: r3 = r1->field_57
    //     0xbf4b64: ldur            w3, [x1, #0x57]
    // 0xbf4b68: DecompressPointer r3
    //     0xbf4b68: add             x3, x3, HEAP, lsl #32
    // 0xbf4b6c: mov             x1, x3
    // 0xbf4b70: r0 = value()
    //     0xbf4b70: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4b74: LoadField: r1 = r0->field_b
    //     0xbf4b74: ldur            w1, [x0, #0xb]
    // 0xbf4b78: DecompressPointer r1
    //     0xbf4b78: add             x1, x1, HEAP, lsl #32
    // 0xbf4b7c: cmp             w1, NULL
    // 0xbf4b80: b.ne            #0xbf4b8c
    // 0xbf4b84: r0 = Null
    //     0xbf4b84: mov             x0, NULL
    // 0xbf4b88: b               #0xbf4bc4
    // 0xbf4b8c: LoadField: r0 = r1->field_57
    //     0xbf4b8c: ldur            w0, [x1, #0x57]
    // 0xbf4b90: DecompressPointer r0
    //     0xbf4b90: add             x0, x0, HEAP, lsl #32
    // 0xbf4b94: cmp             w0, NULL
    // 0xbf4b98: b.ne            #0xbf4ba4
    // 0xbf4b9c: r0 = Null
    //     0xbf4b9c: mov             x0, NULL
    // 0xbf4ba0: b               #0xbf4bc4
    // 0xbf4ba4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4ba4: ldur            w1, [x0, #0x17]
    // 0xbf4ba8: DecompressPointer r1
    //     0xbf4ba8: add             x1, x1, HEAP, lsl #32
    // 0xbf4bac: cmp             w1, NULL
    // 0xbf4bb0: b.ne            #0xbf4bbc
    // 0xbf4bb4: r0 = Null
    //     0xbf4bb4: mov             x0, NULL
    // 0xbf4bb8: b               #0xbf4bc4
    // 0xbf4bbc: LoadField: r0 = r1->field_b
    //     0xbf4bbc: ldur            w0, [x1, #0xb]
    // 0xbf4bc0: DecompressPointer r0
    //     0xbf4bc0: add             x0, x0, HEAP, lsl #32
    // 0xbf4bc4: cmp             w0, NULL
    // 0xbf4bc8: b.ne            #0xbf4bd4
    // 0xbf4bcc: r0 = 0
    //     0xbf4bcc: movz            x0, #0
    // 0xbf4bd0: b               #0xbf4be4
    // 0xbf4bd4: r1 = LoadInt32Instr(r0)
    //     0xbf4bd4: sbfx            x1, x0, #1, #0x1f
    //     0xbf4bd8: tbz             w0, #0, #0xbf4be0
    //     0xbf4bdc: ldur            x1, [x0, #7]
    // 0xbf4be0: mov             x0, x1
    // 0xbf4be4: ldur            x2, [fp, #-0x10]
    // 0xbf4be8: stur            x0, [fp, #-0x28]
    // 0xbf4bec: LoadField: r1 = r2->field_3f
    //     0xbf4bec: ldur            w1, [x2, #0x3f]
    // 0xbf4bf0: DecompressPointer r1
    //     0xbf4bf0: add             x1, x1, HEAP, lsl #32
    // 0xbf4bf4: LoadField: r3 = r1->field_57
    //     0xbf4bf4: ldur            w3, [x1, #0x57]
    // 0xbf4bf8: DecompressPointer r3
    //     0xbf4bf8: add             x3, x3, HEAP, lsl #32
    // 0xbf4bfc: mov             x1, x3
    // 0xbf4c00: r0 = value()
    //     0xbf4c00: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xbf4c04: LoadField: r1 = r0->field_b
    //     0xbf4c04: ldur            w1, [x0, #0xb]
    // 0xbf4c08: DecompressPointer r1
    //     0xbf4c08: add             x1, x1, HEAP, lsl #32
    // 0xbf4c0c: cmp             w1, NULL
    // 0xbf4c10: b.ne            #0xbf4c1c
    // 0xbf4c14: r0 = Null
    //     0xbf4c14: mov             x0, NULL
    // 0xbf4c18: b               #0xbf4c54
    // 0xbf4c1c: LoadField: r0 = r1->field_57
    //     0xbf4c1c: ldur            w0, [x1, #0x57]
    // 0xbf4c20: DecompressPointer r0
    //     0xbf4c20: add             x0, x0, HEAP, lsl #32
    // 0xbf4c24: cmp             w0, NULL
    // 0xbf4c28: b.ne            #0xbf4c34
    // 0xbf4c2c: r0 = Null
    //     0xbf4c2c: mov             x0, NULL
    // 0xbf4c30: b               #0xbf4c54
    // 0xbf4c34: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbf4c34: ldur            w1, [x0, #0x17]
    // 0xbf4c38: DecompressPointer r1
    //     0xbf4c38: add             x1, x1, HEAP, lsl #32
    // 0xbf4c3c: cmp             w1, NULL
    // 0xbf4c40: b.ne            #0xbf4c4c
    // 0xbf4c44: r0 = Null
    //     0xbf4c44: mov             x0, NULL
    // 0xbf4c48: b               #0xbf4c54
    // 0xbf4c4c: LoadField: r0 = r1->field_f
    //     0xbf4c4c: ldur            w0, [x1, #0xf]
    // 0xbf4c50: DecompressPointer r0
    //     0xbf4c50: add             x0, x0, HEAP, lsl #32
    // 0xbf4c54: cmp             w0, NULL
    // 0xbf4c58: b.ne            #0xbf4c64
    // 0xbf4c5c: r4 = 0
    //     0xbf4c5c: movz            x4, #0
    // 0xbf4c60: b               #0xbf4c74
    // 0xbf4c64: r1 = LoadInt32Instr(r0)
    //     0xbf4c64: sbfx            x1, x0, #1, #0x1f
    //     0xbf4c68: tbz             w0, #0, #0xbf4c70
    //     0xbf4c6c: ldur            x1, [x0, #7]
    // 0xbf4c70: mov             x4, x1
    // 0xbf4c74: ldur            x3, [fp, #-0x40]
    // 0xbf4c78: ldur            x2, [fp, #-0x18]
    // 0xbf4c7c: ldur            x1, [fp, #-0x50]
    // 0xbf4c80: ldur            x0, [fp, #-0x38]
    // 0xbf4c84: stur            x4, [fp, #-0x30]
    // 0xbf4c88: r0 = Color()
    //     0xbf4c88: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xbf4c8c: mov             x1, x0
    // 0xbf4c90: r0 = Instance_ColorSpace
    //     0xbf4c90: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xbf4c94: stur            x1, [fp, #-0x48]
    // 0xbf4c98: StoreField: r1->field_27 = r0
    //     0xbf4c98: stur            w0, [x1, #0x27]
    // 0xbf4c9c: d0 = 1.000000
    //     0xbf4c9c: fmov            d0, #1.00000000
    // 0xbf4ca0: StoreField: r1->field_7 = d0
    //     0xbf4ca0: stur            d0, [x1, #7]
    // 0xbf4ca4: ldur            x0, [fp, #-0x20]
    // 0xbf4ca8: ubfx            x0, x0, #0, #0x20
    // 0xbf4cac: and             w2, w0, #0xff
    // 0xbf4cb0: ubfx            x2, x2, #0, #0x20
    // 0xbf4cb4: scvtf           d0, x2
    // 0xbf4cb8: d1 = 255.000000
    //     0xbf4cb8: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xbf4cbc: fdiv            d2, d0, d1
    // 0xbf4cc0: StoreField: r1->field_f = d2
    //     0xbf4cc0: stur            d2, [x1, #0xf]
    // 0xbf4cc4: ldur            x0, [fp, #-0x28]
    // 0xbf4cc8: ubfx            x0, x0, #0, #0x20
    // 0xbf4ccc: and             w2, w0, #0xff
    // 0xbf4cd0: ubfx            x2, x2, #0, #0x20
    // 0xbf4cd4: scvtf           d0, x2
    // 0xbf4cd8: fdiv            d2, d0, d1
    // 0xbf4cdc: ArrayStore: r1[0] = d2  ; List_8
    //     0xbf4cdc: stur            d2, [x1, #0x17]
    // 0xbf4ce0: ldur            x0, [fp, #-0x30]
    // 0xbf4ce4: ubfx            x0, x0, #0, #0x20
    // 0xbf4ce8: and             w2, w0, #0xff
    // 0xbf4cec: ubfx            x2, x2, #0, #0x20
    // 0xbf4cf0: scvtf           d0, x2
    // 0xbf4cf4: fdiv            d2, d0, d1
    // 0xbf4cf8: StoreField: r1->field_1f = d2
    //     0xbf4cf8: stur            d2, [x1, #0x1f]
    // 0xbf4cfc: r0 = ColorFilter()
    //     0xbf4cfc: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xbf4d00: mov             x1, x0
    // 0xbf4d04: ldur            x0, [fp, #-0x48]
    // 0xbf4d08: stur            x1, [fp, #-0x58]
    // 0xbf4d0c: StoreField: r1->field_7 = r0
    //     0xbf4d0c: stur            w0, [x1, #7]
    // 0xbf4d10: r0 = Instance_BlendMode
    //     0xbf4d10: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xbf4d14: ldr             x0, [x0, #0xb30]
    // 0xbf4d18: StoreField: r1->field_b = r0
    //     0xbf4d18: stur            w0, [x1, #0xb]
    // 0xbf4d1c: r0 = 1
    //     0xbf4d1c: movz            x0, #0x1
    // 0xbf4d20: StoreField: r1->field_13 = r0
    //     0xbf4d20: stur            x0, [x1, #0x13]
    // 0xbf4d24: r0 = SvgPicture()
    //     0xbf4d24: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbf4d28: stur            x0, [fp, #-0x48]
    // 0xbf4d2c: r16 = "menu"
    //     0xbf4d2c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cbc8] "menu"
    //     0xbf4d30: ldr             x16, [x16, #0xbc8]
    // 0xbf4d34: r30 = Instance_BoxFit
    //     0xbf4d34: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xbf4d38: ldr             lr, [lr, #0xb18]
    // 0xbf4d3c: stp             lr, x16, [SP, #8]
    // 0xbf4d40: ldur            x16, [fp, #-0x58]
    // 0xbf4d44: str             x16, [SP]
    // 0xbf4d48: mov             x1, x0
    // 0xbf4d4c: r2 = "assets/images/menu.svg"
    //     0xbf4d4c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cbd0] "assets/images/menu.svg"
    //     0xbf4d50: ldr             x2, [x2, #0xbd0]
    // 0xbf4d54: r4 = const [0, 0x5, 0x3, 0x2, colorFilter, 0x4, fit, 0x3, semanticsLabel, 0x2, null]
    //     0xbf4d54: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2cb38] List(11) [0, 0x5, 0x3, 0x2, "colorFilter", 0x4, "fit", 0x3, "semanticsLabel", 0x2, Null]
    //     0xbf4d58: ldr             x4, [x4, #0xb38]
    // 0xbf4d5c: r0 = SvgPicture.asset()
    //     0xbf4d5c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbf4d60: r0 = BottomNavigationBarItem()
    //     0xbf4d60: bl              #0xa68914  ; AllocateBottomNavigationBarItemStub -> BottomNavigationBarItem (size=0x20)
    // 0xbf4d64: mov             x3, x0
    // 0xbf4d68: ldur            x0, [fp, #-0x38]
    // 0xbf4d6c: stur            x3, [fp, #-0x58]
    // 0xbf4d70: StoreField: r3->field_b = r0
    //     0xbf4d70: stur            w0, [x3, #0xb]
    // 0xbf4d74: r0 = "Browse"
    //     0xbf4d74: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cbd8] "Browse"
    //     0xbf4d78: ldr             x0, [x0, #0xbd8]
    // 0xbf4d7c: StoreField: r3->field_13 = r0
    //     0xbf4d7c: stur            w0, [x3, #0x13]
    // 0xbf4d80: ldur            x0, [fp, #-0x48]
    // 0xbf4d84: StoreField: r3->field_f = r0
    //     0xbf4d84: stur            w0, [x3, #0xf]
    // 0xbf4d88: r1 = Null
    //     0xbf4d88: mov             x1, NULL
    // 0xbf4d8c: r2 = 8
    //     0xbf4d8c: movz            x2, #0x8
    // 0xbf4d90: r0 = AllocateArray()
    //     0xbf4d90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf4d94: mov             x2, x0
    // 0xbf4d98: ldur            x0, [fp, #-0x40]
    // 0xbf4d9c: stur            x2, [fp, #-0x38]
    // 0xbf4da0: StoreField: r2->field_f = r0
    //     0xbf4da0: stur            w0, [x2, #0xf]
    // 0xbf4da4: ldur            x0, [fp, #-0x18]
    // 0xbf4da8: StoreField: r2->field_13 = r0
    //     0xbf4da8: stur            w0, [x2, #0x13]
    // 0xbf4dac: ldur            x0, [fp, #-0x50]
    // 0xbf4db0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf4db0: stur            w0, [x2, #0x17]
    // 0xbf4db4: ldur            x0, [fp, #-0x58]
    // 0xbf4db8: StoreField: r2->field_1b = r0
    //     0xbf4db8: stur            w0, [x2, #0x1b]
    // 0xbf4dbc: r1 = <BottomNavigationBarItem>
    //     0xbf4dbc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbe0] TypeArguments: <BottomNavigationBarItem>
    //     0xbf4dc0: ldr             x1, [x1, #0xbe0]
    // 0xbf4dc4: r0 = AllocateGrowableArray()
    //     0xbf4dc4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf4dc8: mov             x3, x0
    // 0xbf4dcc: ldur            x0, [fp, #-0x38]
    // 0xbf4dd0: stur            x3, [fp, #-0x18]
    // 0xbf4dd4: StoreField: r3->field_f = r0
    //     0xbf4dd4: stur            w0, [x3, #0xf]
    // 0xbf4dd8: r0 = 8
    //     0xbf4dd8: movz            x0, #0x8
    // 0xbf4ddc: StoreField: r3->field_b = r0
    //     0xbf4ddc: stur            w0, [x3, #0xb]
    // 0xbf4de0: ldur            x2, [fp, #-0x10]
    // 0xbf4de4: r1 = Function '_onItemTapped@1712469706':.
    //     0xbf4de4: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cbe8] AnonymousClosure: (0x915e50), in [package:customer_app/app/presentation/views/line/main/main_page.dart] _MainPageState::_onItemTapped (0x915de0)
    //     0xbf4de8: ldr             x1, [x1, #0xbe8]
    // 0xbf4dec: r0 = AllocateClosure()
    //     0xbf4dec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf4df0: stur            x0, [fp, #-0x10]
    // 0xbf4df4: r0 = BottomNavigationBar()
    //     0xbf4df4: bl              #0xa688fc  ; AllocateBottomNavigationBarStub -> BottomNavigationBar (size=0x70)
    // 0xbf4df8: mov             x1, x0
    // 0xbf4dfc: ldur            x2, [fp, #-8]
    // 0xbf4e00: ldur            x3, [fp, #-0x18]
    // 0xbf4e04: ldur            x5, [fp, #-0x10]
    // 0xbf4e08: stur            x0, [fp, #-0x10]
    // 0xbf4e0c: r0 = BottomNavigationBar()
    //     0xbf4e0c: bl              #0xa68858  ; [package:flutter/src/material/bottom_navigation_bar.dart] BottomNavigationBar::BottomNavigationBar
    // 0xbf4e10: ldur            x0, [fp, #-0x10]
    // 0xbf4e14: LeaveFrame
    //     0xbf4e14: mov             SP, fp
    //     0xbf4e18: ldp             fp, lr, [SP], #0x10
    // 0xbf4e1c: ret
    //     0xbf4e1c: ret             
    // 0xbf4e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf4e20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf4e24: b               #0xbf396c
    // 0xbf4e28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf4e28: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf4e2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf4e2c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf4e30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf4e30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf4e34: r0 = RangeErrorSharedWithFPURegs()
    //     0xbf4e34: bl              #0x16f7858  ; RangeErrorSharedWithFPURegsStub
    // 0xbf4e38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbf4e38: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xbf4e3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf4e3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc882c4, size: 0x88
    // 0xc882c4: EnterFrame
    //     0xc882c4: stp             fp, lr, [SP, #-0x10]!
    //     0xc882c8: mov             fp, SP
    // 0xc882cc: AllocStack(0x10)
    //     0xc882cc: sub             SP, SP, #0x10
    // 0xc882d0: SetupParameters(_MainPageState this /* r1 => r1, fp-0x10 */)
    //     0xc882d0: stur            x1, [fp, #-0x10]
    // 0xc882d4: CheckStackOverflow
    //     0xc882d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc882d8: cmp             SP, x16
    //     0xc882dc: b.ls            #0xc88344
    // 0xc882e0: LoadField: r0 = r1->field_3f
    //     0xc882e0: ldur            w0, [x1, #0x3f]
    // 0xc882e4: DecompressPointer r0
    //     0xc882e4: add             x0, x0, HEAP, lsl #32
    // 0xc882e8: LoadField: r2 = r0->field_4b
    //     0xc882e8: ldur            w2, [x0, #0x4b]
    // 0xc882ec: DecompressPointer r2
    //     0xc882ec: add             x2, x2, HEAP, lsl #32
    // 0xc882f0: stur            x2, [fp, #-8]
    // 0xc882f4: r0 = UtmData()
    //     0xc882f4: bl              #0x8939f0  ; AllocateUtmDataStub -> UtmData (size=0x1c)
    // 0xc882f8: ldur            x1, [fp, #-8]
    // 0xc882fc: mov             x2, x0
    // 0xc88300: r0 = setUtmData()
    //     0xc88300: bl              #0x916b50  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::setUtmData
    // 0xc88304: ldur            x2, [fp, #-0x10]
    // 0xc88308: LoadField: r1 = r2->field_47
    //     0xc88308: ldur            w1, [x2, #0x47]
    // 0xc8830c: DecompressPointer r1
    //     0xc8830c: add             x1, x1, HEAP, lsl #32
    // 0xc88310: cmp             w1, NULL
    // 0xc88314: b.eq            #0xc8832c
    // 0xc88318: r0 = LoadClassIdInstr(r1)
    //     0xc88318: ldur            x0, [x1, #-1]
    //     0xc8831c: ubfx            x0, x0, #0xc, #0x14
    // 0xc88320: r0 = GDT[cid_x0 + -0xe14]()
    //     0xc88320: sub             lr, x0, #0xe14
    //     0xc88324: ldr             lr, [x21, lr, lsl #3]
    //     0xc88328: blr             lr
    // 0xc8832c: ldur            x1, [fp, #-0x10]
    // 0xc88330: r0 = dispose()
    //     0xc88330: bl              #0xc8834c  ; [package:customer_app/app/presentation/views/line/main/main_page.dart] __MainPageState&State&SingleTickerProviderStateMixin::dispose
    // 0xc88334: r0 = Null
    //     0xc88334: mov             x0, NULL
    // 0xc88338: LeaveFrame
    //     0xc88338: mov             SP, fp
    //     0xc8833c: ldp             fp, lr, [SP], #0x10
    // 0xc88340: ret
    //     0xc88340: ret             
    // 0xc88344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88344: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc88348: b               #0xc882e0
  }
}

// class id: 3986, size: 0xc, field offset: 0xc
//   const constructor, 
class MainPage extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80cc0, size: 0x48
    // 0xc80cc0: EnterFrame
    //     0xc80cc0: stp             fp, lr, [SP, #-0x10]!
    //     0xc80cc4: mov             fp, SP
    // 0xc80cc8: AllocStack(0x8)
    //     0xc80cc8: sub             SP, SP, #8
    // 0xc80ccc: CheckStackOverflow
    //     0xc80ccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80cd0: cmp             SP, x16
    //     0xc80cd4: b.ls            #0xc80d00
    // 0xc80cd8: r1 = <MainPage>
    //     0xc80cd8: add             x1, PP, #0x2a, lsl #12  ; [pp+0x2ae70] TypeArguments: <MainPage>
    //     0xc80cdc: ldr             x1, [x1, #0xe70]
    // 0xc80ce0: r0 = _MainPageState()
    //     0xc80ce0: bl              #0xc80d08  ; Allocate_MainPageStateStub -> _MainPageState (size=0x54)
    // 0xc80ce4: mov             x1, x0
    // 0xc80ce8: stur            x0, [fp, #-8]
    // 0xc80cec: r0 = _MainPageState()
    //     0xc80cec: bl              #0xc7be60  ; [package:customer_app/app/presentation/views/basic/main/main_page.dart] _MainPageState::_MainPageState
    // 0xc80cf0: ldur            x0, [fp, #-8]
    // 0xc80cf4: LeaveFrame
    //     0xc80cf4: mov             SP, fp
    //     0xc80cf8: ldp             fp, lr, [SP], #0x10
    // 0xc80cfc: ret
    //     0xc80cfc: ret             
    // 0xc80d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80d00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc80d04: b               #0xc80cd8
  }
}
