// lib: , url: package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart

// class id: 1049163, size: 0x8
class :: {
}

// class id: 3510, size: 0x24, field offset: 0x14
class _TestimonialCarouselState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14

  _ build(/* No info */) {
    // ** addr: 0xa62b0c, size: 0x189c
    // 0xa62b0c: EnterFrame
    //     0xa62b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xa62b10: mov             fp, SP
    // 0xa62b14: AllocStack(0x90)
    //     0xa62b14: sub             SP, SP, #0x90
    // 0xa62b18: SetupParameters(_TestimonialCarouselState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa62b18: mov             x0, x1
    //     0xa62b1c: stur            x1, [fp, #-8]
    //     0xa62b20: mov             x1, x2
    //     0xa62b24: stur            x2, [fp, #-0x10]
    // 0xa62b28: CheckStackOverflow
    //     0xa62b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa62b2c: cmp             SP, x16
    //     0xa62b30: b.ls            #0xa6434c
    // 0xa62b34: r1 = 1
    //     0xa62b34: movz            x1, #0x1
    // 0xa62b38: r0 = AllocateContext()
    //     0xa62b38: bl              #0x16f6108  ; AllocateContextStub
    // 0xa62b3c: mov             x3, x0
    // 0xa62b40: ldur            x0, [fp, #-8]
    // 0xa62b44: stur            x3, [fp, #-0x20]
    // 0xa62b48: StoreField: r3->field_f = r0
    //     0xa62b48: stur            w0, [x3, #0xf]
    // 0xa62b4c: LoadField: r1 = r0->field_b
    //     0xa62b4c: ldur            w1, [x0, #0xb]
    // 0xa62b50: DecompressPointer r1
    //     0xa62b50: add             x1, x1, HEAP, lsl #32
    // 0xa62b54: cmp             w1, NULL
    // 0xa62b58: b.eq            #0xa64354
    // 0xa62b5c: LoadField: r2 = r1->field_13
    //     0xa62b5c: ldur            w2, [x1, #0x13]
    // 0xa62b60: DecompressPointer r2
    //     0xa62b60: add             x2, x2, HEAP, lsl #32
    // 0xa62b64: cmp             w2, NULL
    // 0xa62b68: b.ne            #0xa62b74
    // 0xa62b6c: r1 = Null
    //     0xa62b6c: mov             x1, NULL
    // 0xa62b70: b               #0xa62b7c
    // 0xa62b74: LoadField: r1 = r2->field_7
    //     0xa62b74: ldur            w1, [x2, #7]
    // 0xa62b78: DecompressPointer r1
    //     0xa62b78: add             x1, x1, HEAP, lsl #32
    // 0xa62b7c: cmp             w1, NULL
    // 0xa62b80: b.ne            #0xa62b8c
    // 0xa62b84: r1 = Instance_TitleAlignment
    //     0xa62b84: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xa62b88: ldr             x1, [x1, #0x518]
    // 0xa62b8c: r16 = Instance_TitleAlignment
    //     0xa62b8c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xa62b90: ldr             x16, [x16, #0x520]
    // 0xa62b94: cmp             w1, w16
    // 0xa62b98: b.ne            #0xa62ba8
    // 0xa62b9c: r4 = Instance_CrossAxisAlignment
    //     0xa62b9c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xa62ba0: ldr             x4, [x4, #0xc68]
    // 0xa62ba4: b               #0xa62bcc
    // 0xa62ba8: r16 = Instance_TitleAlignment
    //     0xa62ba8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xa62bac: ldr             x16, [x16, #0x518]
    // 0xa62bb0: cmp             w1, w16
    // 0xa62bb4: b.ne            #0xa62bc4
    // 0xa62bb8: r4 = Instance_CrossAxisAlignment
    //     0xa62bb8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa62bbc: ldr             x4, [x4, #0x890]
    // 0xa62bc0: b               #0xa62bcc
    // 0xa62bc4: r4 = Instance_CrossAxisAlignment
    //     0xa62bc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa62bc8: ldr             x4, [x4, #0xa18]
    // 0xa62bcc: stur            x4, [fp, #-0x18]
    // 0xa62bd0: r1 = <Widget>
    //     0xa62bd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa62bd4: r2 = 0
    //     0xa62bd4: movz            x2, #0
    // 0xa62bd8: r0 = _GrowableList()
    //     0xa62bd8: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa62bdc: mov             x3, x0
    // 0xa62be0: ldur            x0, [fp, #-8]
    // 0xa62be4: stur            x3, [fp, #-0x28]
    // 0xa62be8: LoadField: r1 = r0->field_b
    //     0xa62be8: ldur            w1, [x0, #0xb]
    // 0xa62bec: DecompressPointer r1
    //     0xa62bec: add             x1, x1, HEAP, lsl #32
    // 0xa62bf0: cmp             w1, NULL
    // 0xa62bf4: b.eq            #0xa64358
    // 0xa62bf8: LoadField: r2 = r1->field_13
    //     0xa62bf8: ldur            w2, [x1, #0x13]
    // 0xa62bfc: DecompressPointer r2
    //     0xa62bfc: add             x2, x2, HEAP, lsl #32
    // 0xa62c00: cmp             w2, NULL
    // 0xa62c04: b.ne            #0xa62c60
    // 0xa62c08: mov             x14, x3
    // 0xa62c0c: r7 = true
    //     0xa62c0c: add             x7, NULL, #0x20  ; true
    // 0xa62c10: r0 = Instance_BorderRadius
    //     0xa62c10: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa62c14: ldr             x0, [x0, #0xbe8]
    // 0xa62c18: r4 = Instance_EdgeInsets
    //     0xa62c18: add             x4, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa62c1c: ldr             x4, [x4, #0x898]
    // 0xa62c20: r5 = Instance_EdgeInsets
    //     0xa62c20: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xa62c24: ldr             x5, [x5, #0xd48]
    // 0xa62c28: r11 = Instance_MainAxisAlignment
    //     0xa62c28: add             x11, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa62c2c: ldr             x11, [x11, #0xd10]
    // 0xa62c30: r13 = Instance_EdgeInsets
    //     0xa62c30: add             x13, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xa62c34: ldr             x13, [x13, #0x78]
    // 0xa62c38: r9 = false
    //     0xa62c38: add             x9, NULL, #0x30  ; false
    // 0xa62c3c: r6 = Instance_SizedBox
    //     0xa62c3c: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa62c40: r8 = Instance_BoxShape
    //     0xa62c40: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa62c44: ldr             x8, [x8, #0x80]
    // 0xa62c48: r12 = Instance_CrossAxisAlignment
    //     0xa62c48: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa62c4c: ldr             x12, [x12, #0xa18]
    // 0xa62c50: r10 = Instance_Axis
    //     0xa62c50: ldr             x10, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa62c54: r3 = Instance_Alignment
    //     0xa62c54: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa62c58: ldr             x3, [x3, #0xb10]
    // 0xa62c5c: b               #0xa632a0
    // 0xa62c60: LoadField: r4 = r2->field_7
    //     0xa62c60: ldur            w4, [x2, #7]
    // 0xa62c64: DecompressPointer r4
    //     0xa62c64: add             x4, x4, HEAP, lsl #32
    // 0xa62c68: r16 = Instance_TitleAlignment
    //     0xa62c68: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xa62c6c: ldr             x16, [x16, #0x518]
    // 0xa62c70: cmp             w4, w16
    // 0xa62c74: b.ne            #0xa6324c
    // 0xa62c78: r1 = <Widget>
    //     0xa62c78: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa62c7c: r2 = 0
    //     0xa62c7c: movz            x2, #0
    // 0xa62c80: r0 = _GrowableList()
    //     0xa62c80: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xa62c84: mov             x2, x0
    // 0xa62c88: ldur            x0, [fp, #-8]
    // 0xa62c8c: stur            x2, [fp, #-0x38]
    // 0xa62c90: LoadField: r1 = r0->field_b
    //     0xa62c90: ldur            w1, [x0, #0xb]
    // 0xa62c94: DecompressPointer r1
    //     0xa62c94: add             x1, x1, HEAP, lsl #32
    // 0xa62c98: cmp             w1, NULL
    // 0xa62c9c: b.eq            #0xa6435c
    // 0xa62ca0: LoadField: r3 = r1->field_f
    //     0xa62ca0: ldur            w3, [x1, #0xf]
    // 0xa62ca4: DecompressPointer r3
    //     0xa62ca4: add             x3, x3, HEAP, lsl #32
    // 0xa62ca8: stur            x3, [fp, #-0x30]
    // 0xa62cac: LoadField: r1 = r3->field_7
    //     0xa62cac: ldur            w1, [x3, #7]
    // 0xa62cb0: cbz             w1, #0xa62dcc
    // 0xa62cb4: ldur            x1, [fp, #-0x10]
    // 0xa62cb8: r0 = of()
    //     0xa62cb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa62cbc: LoadField: r1 = r0->field_87
    //     0xa62cbc: ldur            w1, [x0, #0x87]
    // 0xa62cc0: DecompressPointer r1
    //     0xa62cc0: add             x1, x1, HEAP, lsl #32
    // 0xa62cc4: LoadField: r0 = r1->field_7
    //     0xa62cc4: ldur            w0, [x1, #7]
    // 0xa62cc8: DecompressPointer r0
    //     0xa62cc8: add             x0, x0, HEAP, lsl #32
    // 0xa62ccc: stur            x0, [fp, #-0x40]
    // 0xa62cd0: r1 = Instance_Color
    //     0xa62cd0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa62cd4: d0 = 0.700000
    //     0xa62cd4: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa62cd8: ldr             d0, [x17, #0xf48]
    // 0xa62cdc: r0 = withOpacity()
    //     0xa62cdc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa62ce0: r16 = 21.000000
    //     0xa62ce0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa62ce4: ldr             x16, [x16, #0x9b0]
    // 0xa62ce8: stp             x0, x16, [SP]
    // 0xa62cec: ldur            x1, [fp, #-0x40]
    // 0xa62cf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa62cf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa62cf4: ldr             x4, [x4, #0xaa0]
    // 0xa62cf8: r0 = copyWith()
    //     0xa62cf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa62cfc: stur            x0, [fp, #-0x40]
    // 0xa62d00: r0 = Text()
    //     0xa62d00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa62d04: mov             x2, x0
    // 0xa62d08: ldur            x0, [fp, #-0x30]
    // 0xa62d0c: stur            x2, [fp, #-0x48]
    // 0xa62d10: StoreField: r2->field_b = r0
    //     0xa62d10: stur            w0, [x2, #0xb]
    // 0xa62d14: ldur            x0, [fp, #-0x40]
    // 0xa62d18: StoreField: r2->field_13 = r0
    //     0xa62d18: stur            w0, [x2, #0x13]
    // 0xa62d1c: r0 = 4
    //     0xa62d1c: movz            x0, #0x4
    // 0xa62d20: StoreField: r2->field_37 = r0
    //     0xa62d20: stur            w0, [x2, #0x37]
    // 0xa62d24: r1 = <FlexParentData>
    //     0xa62d24: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa62d28: ldr             x1, [x1, #0xe00]
    // 0xa62d2c: r0 = Expanded()
    //     0xa62d2c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa62d30: mov             x2, x0
    // 0xa62d34: r0 = 1
    //     0xa62d34: movz            x0, #0x1
    // 0xa62d38: stur            x2, [fp, #-0x30]
    // 0xa62d3c: StoreField: r2->field_13 = r0
    //     0xa62d3c: stur            x0, [x2, #0x13]
    // 0xa62d40: r0 = Instance_FlexFit
    //     0xa62d40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa62d44: ldr             x0, [x0, #0xe08]
    // 0xa62d48: StoreField: r2->field_1b = r0
    //     0xa62d48: stur            w0, [x2, #0x1b]
    // 0xa62d4c: ldur            x0, [fp, #-0x48]
    // 0xa62d50: StoreField: r2->field_b = r0
    //     0xa62d50: stur            w0, [x2, #0xb]
    // 0xa62d54: ldur            x0, [fp, #-0x38]
    // 0xa62d58: LoadField: r1 = r0->field_b
    //     0xa62d58: ldur            w1, [x0, #0xb]
    // 0xa62d5c: LoadField: r3 = r0->field_f
    //     0xa62d5c: ldur            w3, [x0, #0xf]
    // 0xa62d60: DecompressPointer r3
    //     0xa62d60: add             x3, x3, HEAP, lsl #32
    // 0xa62d64: LoadField: r4 = r3->field_b
    //     0xa62d64: ldur            w4, [x3, #0xb]
    // 0xa62d68: r3 = LoadInt32Instr(r1)
    //     0xa62d68: sbfx            x3, x1, #1, #0x1f
    // 0xa62d6c: stur            x3, [fp, #-0x50]
    // 0xa62d70: r1 = LoadInt32Instr(r4)
    //     0xa62d70: sbfx            x1, x4, #1, #0x1f
    // 0xa62d74: cmp             x3, x1
    // 0xa62d78: b.ne            #0xa62d84
    // 0xa62d7c: mov             x1, x0
    // 0xa62d80: r0 = _growToNextCapacity()
    //     0xa62d80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa62d84: ldur            x2, [fp, #-0x38]
    // 0xa62d88: ldur            x3, [fp, #-0x50]
    // 0xa62d8c: add             x0, x3, #1
    // 0xa62d90: lsl             x1, x0, #1
    // 0xa62d94: StoreField: r2->field_b = r1
    //     0xa62d94: stur            w1, [x2, #0xb]
    // 0xa62d98: LoadField: r1 = r2->field_f
    //     0xa62d98: ldur            w1, [x2, #0xf]
    // 0xa62d9c: DecompressPointer r1
    //     0xa62d9c: add             x1, x1, HEAP, lsl #32
    // 0xa62da0: ldur            x0, [fp, #-0x30]
    // 0xa62da4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa62da4: add             x25, x1, x3, lsl #2
    //     0xa62da8: add             x25, x25, #0xf
    //     0xa62dac: str             w0, [x25]
    //     0xa62db0: tbz             w0, #0, #0xa62dcc
    //     0xa62db4: ldurb           w16, [x1, #-1]
    //     0xa62db8: ldurb           w17, [x0, #-1]
    //     0xa62dbc: and             x16, x17, x16, lsr #2
    //     0xa62dc0: tst             x16, HEAP, lsr #32
    //     0xa62dc4: b.eq            #0xa62dcc
    //     0xa62dc8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa62dcc: LoadField: r0 = r2->field_b
    //     0xa62dcc: ldur            w0, [x2, #0xb]
    // 0xa62dd0: LoadField: r1 = r2->field_f
    //     0xa62dd0: ldur            w1, [x2, #0xf]
    // 0xa62dd4: DecompressPointer r1
    //     0xa62dd4: add             x1, x1, HEAP, lsl #32
    // 0xa62dd8: LoadField: r3 = r1->field_b
    //     0xa62dd8: ldur            w3, [x1, #0xb]
    // 0xa62ddc: r4 = LoadInt32Instr(r0)
    //     0xa62ddc: sbfx            x4, x0, #1, #0x1f
    // 0xa62de0: stur            x4, [fp, #-0x50]
    // 0xa62de4: r0 = LoadInt32Instr(r3)
    //     0xa62de4: sbfx            x0, x3, #1, #0x1f
    // 0xa62de8: cmp             x4, x0
    // 0xa62dec: b.ne            #0xa62df8
    // 0xa62df0: mov             x1, x2
    // 0xa62df4: r0 = _growToNextCapacity()
    //     0xa62df4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa62df8: ldur            x2, [fp, #-8]
    // 0xa62dfc: ldur            x0, [fp, #-0x38]
    // 0xa62e00: ldur            x1, [fp, #-0x50]
    // 0xa62e04: add             x3, x1, #1
    // 0xa62e08: lsl             x4, x3, #1
    // 0xa62e0c: StoreField: r0->field_b = r4
    //     0xa62e0c: stur            w4, [x0, #0xb]
    // 0xa62e10: LoadField: r3 = r0->field_f
    //     0xa62e10: ldur            w3, [x0, #0xf]
    // 0xa62e14: DecompressPointer r3
    //     0xa62e14: add             x3, x3, HEAP, lsl #32
    // 0xa62e18: add             x4, x3, x1, lsl #2
    // 0xa62e1c: r16 = Instance_Spacer
    //     0xa62e1c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa62e20: ldr             x16, [x16, #0xf0]
    // 0xa62e24: StoreField: r4->field_f = r16
    //     0xa62e24: stur            w16, [x4, #0xf]
    // 0xa62e28: LoadField: r1 = r2->field_b
    //     0xa62e28: ldur            w1, [x2, #0xb]
    // 0xa62e2c: DecompressPointer r1
    //     0xa62e2c: add             x1, x1, HEAP, lsl #32
    // 0xa62e30: cmp             w1, NULL
    // 0xa62e34: b.eq            #0xa64360
    // 0xa62e38: LoadField: r3 = r1->field_2f
    //     0xa62e38: ldur            w3, [x1, #0x2f]
    // 0xa62e3c: DecompressPointer r3
    //     0xa62e3c: add             x3, x3, HEAP, lsl #32
    // 0xa62e40: cmp             w3, NULL
    // 0xa62e44: b.ne            #0xa62e50
    // 0xa62e48: r1 = Null
    //     0xa62e48: mov             x1, NULL
    // 0xa62e4c: b               #0xa62e7c
    // 0xa62e50: LoadField: r1 = r3->field_7
    //     0xa62e50: ldur            w1, [x3, #7]
    // 0xa62e54: DecompressPointer r1
    //     0xa62e54: add             x1, x1, HEAP, lsl #32
    // 0xa62e58: cmp             w1, NULL
    // 0xa62e5c: b.ne            #0xa62e68
    // 0xa62e60: r1 = Null
    //     0xa62e60: mov             x1, NULL
    // 0xa62e64: b               #0xa62e7c
    // 0xa62e68: LoadField: r3 = r1->field_7
    //     0xa62e68: ldur            w3, [x1, #7]
    // 0xa62e6c: cbnz            w3, #0xa62e78
    // 0xa62e70: r1 = false
    //     0xa62e70: add             x1, NULL, #0x30  ; false
    // 0xa62e74: b               #0xa62e7c
    // 0xa62e78: r1 = true
    //     0xa62e78: add             x1, NULL, #0x20  ; true
    // 0xa62e7c: cmp             w1, NULL
    // 0xa62e80: b.ne            #0xa62e8c
    // 0xa62e84: r3 = false
    //     0xa62e84: add             x3, NULL, #0x30  ; false
    // 0xa62e88: b               #0xa62e90
    // 0xa62e8c: mov             x3, x1
    // 0xa62e90: ldur            x1, [fp, #-0x10]
    // 0xa62e94: stur            x3, [fp, #-0x30]
    // 0xa62e98: r0 = of()
    //     0xa62e98: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa62e9c: LoadField: r2 = r0->field_5b
    //     0xa62e9c: ldur            w2, [x0, #0x5b]
    // 0xa62ea0: DecompressPointer r2
    //     0xa62ea0: add             x2, x2, HEAP, lsl #32
    // 0xa62ea4: r1 = Null
    //     0xa62ea4: mov             x1, NULL
    // 0xa62ea8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa62ea8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa62eac: r0 = Border.all()
    //     0xa62eac: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa62eb0: stur            x0, [fp, #-0x40]
    // 0xa62eb4: r0 = BoxDecoration()
    //     0xa62eb4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa62eb8: mov             x2, x0
    // 0xa62ebc: ldur            x0, [fp, #-0x40]
    // 0xa62ec0: stur            x2, [fp, #-0x48]
    // 0xa62ec4: StoreField: r2->field_f = r0
    //     0xa62ec4: stur            w0, [x2, #0xf]
    // 0xa62ec8: r0 = Instance_BorderRadius
    //     0xa62ec8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa62ecc: ldr             x0, [x0, #0xbe8]
    // 0xa62ed0: StoreField: r2->field_13 = r0
    //     0xa62ed0: stur            w0, [x2, #0x13]
    // 0xa62ed4: r0 = Instance_BoxShape
    //     0xa62ed4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa62ed8: ldr             x0, [x0, #0x80]
    // 0xa62edc: StoreField: r2->field_23 = r0
    //     0xa62edc: stur            w0, [x2, #0x23]
    // 0xa62ee0: ldur            x3, [fp, #-8]
    // 0xa62ee4: LoadField: r1 = r3->field_b
    //     0xa62ee4: ldur            w1, [x3, #0xb]
    // 0xa62ee8: DecompressPointer r1
    //     0xa62ee8: add             x1, x1, HEAP, lsl #32
    // 0xa62eec: cmp             w1, NULL
    // 0xa62ef0: b.eq            #0xa64364
    // 0xa62ef4: LoadField: r4 = r1->field_2f
    //     0xa62ef4: ldur            w4, [x1, #0x2f]
    // 0xa62ef8: DecompressPointer r4
    //     0xa62ef8: add             x4, x4, HEAP, lsl #32
    // 0xa62efc: cmp             w4, NULL
    // 0xa62f00: b.ne            #0xa62f0c
    // 0xa62f04: r1 = Null
    //     0xa62f04: mov             x1, NULL
    // 0xa62f08: b               #0xa62f14
    // 0xa62f0c: LoadField: r1 = r4->field_7
    //     0xa62f0c: ldur            w1, [x4, #7]
    // 0xa62f10: DecompressPointer r1
    //     0xa62f10: add             x1, x1, HEAP, lsl #32
    // 0xa62f14: cmp             w1, NULL
    // 0xa62f18: b.ne            #0xa62f24
    // 0xa62f1c: r6 = ""
    //     0xa62f1c: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa62f20: b               #0xa62f28
    // 0xa62f24: mov             x6, x1
    // 0xa62f28: ldur            x4, [fp, #-0x38]
    // 0xa62f2c: ldur            x5, [fp, #-0x30]
    // 0xa62f30: ldur            x1, [fp, #-0x10]
    // 0xa62f34: stur            x6, [fp, #-0x40]
    // 0xa62f38: r0 = of()
    //     0xa62f38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa62f3c: LoadField: r1 = r0->field_87
    //     0xa62f3c: ldur            w1, [x0, #0x87]
    // 0xa62f40: DecompressPointer r1
    //     0xa62f40: add             x1, x1, HEAP, lsl #32
    // 0xa62f44: LoadField: r0 = r1->field_7
    //     0xa62f44: ldur            w0, [x1, #7]
    // 0xa62f48: DecompressPointer r0
    //     0xa62f48: add             x0, x0, HEAP, lsl #32
    // 0xa62f4c: ldur            x1, [fp, #-0x10]
    // 0xa62f50: stur            x0, [fp, #-0x58]
    // 0xa62f54: r0 = of()
    //     0xa62f54: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa62f58: LoadField: r1 = r0->field_5b
    //     0xa62f58: ldur            w1, [x0, #0x5b]
    // 0xa62f5c: DecompressPointer r1
    //     0xa62f5c: add             x1, x1, HEAP, lsl #32
    // 0xa62f60: r0 = LoadClassIdInstr(r1)
    //     0xa62f60: ldur            x0, [x1, #-1]
    //     0xa62f64: ubfx            x0, x0, #0xc, #0x14
    // 0xa62f68: d0 = 0.700000
    //     0xa62f68: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa62f6c: ldr             d0, [x17, #0xf48]
    // 0xa62f70: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa62f70: sub             lr, x0, #0xffa
    //     0xa62f74: ldr             lr, [x21, lr, lsl #3]
    //     0xa62f78: blr             lr
    // 0xa62f7c: r16 = 14.000000
    //     0xa62f7c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa62f80: ldr             x16, [x16, #0x1d8]
    // 0xa62f84: stp             x0, x16, [SP]
    // 0xa62f88: ldur            x1, [fp, #-0x58]
    // 0xa62f8c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa62f8c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa62f90: ldr             x4, [x4, #0xaa0]
    // 0xa62f94: r0 = copyWith()
    //     0xa62f94: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa62f98: stur            x0, [fp, #-0x58]
    // 0xa62f9c: r0 = Text()
    //     0xa62f9c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa62fa0: mov             x1, x0
    // 0xa62fa4: ldur            x0, [fp, #-0x40]
    // 0xa62fa8: stur            x1, [fp, #-0x60]
    // 0xa62fac: StoreField: r1->field_b = r0
    //     0xa62fac: stur            w0, [x1, #0xb]
    // 0xa62fb0: ldur            x0, [fp, #-0x58]
    // 0xa62fb4: StoreField: r1->field_13 = r0
    //     0xa62fb4: stur            w0, [x1, #0x13]
    // 0xa62fb8: r0 = Center()
    //     0xa62fb8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa62fbc: r3 = Instance_Alignment
    //     0xa62fbc: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa62fc0: ldr             x3, [x3, #0xb10]
    // 0xa62fc4: stur            x0, [fp, #-0x40]
    // 0xa62fc8: StoreField: r0->field_f = r3
    //     0xa62fc8: stur            w3, [x0, #0xf]
    // 0xa62fcc: ldur            x1, [fp, #-0x60]
    // 0xa62fd0: StoreField: r0->field_b = r1
    //     0xa62fd0: stur            w1, [x0, #0xb]
    // 0xa62fd4: r0 = Padding()
    //     0xa62fd4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa62fd8: r4 = Instance_EdgeInsets
    //     0xa62fd8: add             x4, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa62fdc: ldr             x4, [x4, #0x898]
    // 0xa62fe0: stur            x0, [fp, #-0x58]
    // 0xa62fe4: StoreField: r0->field_f = r4
    //     0xa62fe4: stur            w4, [x0, #0xf]
    // 0xa62fe8: ldur            x1, [fp, #-0x40]
    // 0xa62fec: StoreField: r0->field_b = r1
    //     0xa62fec: stur            w1, [x0, #0xb]
    // 0xa62ff0: r0 = Container()
    //     0xa62ff0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa62ff4: stur            x0, [fp, #-0x40]
    // 0xa62ff8: r16 = 32.000000
    //     0xa62ff8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa62ffc: ldr             x16, [x16, #0x848]
    // 0xa63000: r30 = 90.000000
    //     0xa63000: add             lr, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xa63004: ldr             lr, [lr, #0xe68]
    // 0xa63008: stp             lr, x16, [SP, #0x10]
    // 0xa6300c: ldur            x16, [fp, #-0x48]
    // 0xa63010: ldur            lr, [fp, #-0x58]
    // 0xa63014: stp             lr, x16, [SP]
    // 0xa63018: mov             x1, x0
    // 0xa6301c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa6301c: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa63020: ldr             x4, [x4, #0x8c0]
    // 0xa63024: r0 = Container()
    //     0xa63024: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa63028: r0 = Padding()
    //     0xa63028: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa6302c: r5 = Instance_EdgeInsets
    //     0xa6302c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xa63030: ldr             x5, [x5, #0xd48]
    // 0xa63034: stur            x0, [fp, #-0x48]
    // 0xa63038: StoreField: r0->field_f = r5
    //     0xa63038: stur            w5, [x0, #0xf]
    // 0xa6303c: ldur            x1, [fp, #-0x40]
    // 0xa63040: StoreField: r0->field_b = r1
    //     0xa63040: stur            w1, [x0, #0xb]
    // 0xa63044: r0 = Visibility()
    //     0xa63044: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa63048: mov             x1, x0
    // 0xa6304c: ldur            x0, [fp, #-0x48]
    // 0xa63050: stur            x1, [fp, #-0x40]
    // 0xa63054: StoreField: r1->field_b = r0
    //     0xa63054: stur            w0, [x1, #0xb]
    // 0xa63058: r6 = Instance_SizedBox
    //     0xa63058: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa6305c: StoreField: r1->field_f = r6
    //     0xa6305c: stur            w6, [x1, #0xf]
    // 0xa63060: ldur            x0, [fp, #-0x30]
    // 0xa63064: StoreField: r1->field_13 = r0
    //     0xa63064: stur            w0, [x1, #0x13]
    // 0xa63068: r0 = false
    //     0xa63068: add             x0, NULL, #0x30  ; false
    // 0xa6306c: ArrayStore: r1[0] = r0  ; List_4
    //     0xa6306c: stur            w0, [x1, #0x17]
    // 0xa63070: StoreField: r1->field_1b = r0
    //     0xa63070: stur            w0, [x1, #0x1b]
    // 0xa63074: StoreField: r1->field_1f = r0
    //     0xa63074: stur            w0, [x1, #0x1f]
    // 0xa63078: StoreField: r1->field_23 = r0
    //     0xa63078: stur            w0, [x1, #0x23]
    // 0xa6307c: StoreField: r1->field_27 = r0
    //     0xa6307c: stur            w0, [x1, #0x27]
    // 0xa63080: StoreField: r1->field_2b = r0
    //     0xa63080: stur            w0, [x1, #0x2b]
    // 0xa63084: r0 = InkWell()
    //     0xa63084: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa63088: mov             x3, x0
    // 0xa6308c: ldur            x0, [fp, #-0x40]
    // 0xa63090: stur            x3, [fp, #-0x30]
    // 0xa63094: StoreField: r3->field_b = r0
    //     0xa63094: stur            w0, [x3, #0xb]
    // 0xa63098: ldur            x2, [fp, #-0x20]
    // 0xa6309c: r1 = Function '<anonymous closure>':.
    //     0xa6309c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a330] AnonymousClosure: (0xa66690), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xa62b0c)
    //     0xa630a0: ldr             x1, [x1, #0x330]
    // 0xa630a4: r0 = AllocateClosure()
    //     0xa630a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa630a8: mov             x1, x0
    // 0xa630ac: ldur            x0, [fp, #-0x30]
    // 0xa630b0: StoreField: r0->field_f = r1
    //     0xa630b0: stur            w1, [x0, #0xf]
    // 0xa630b4: r7 = true
    //     0xa630b4: add             x7, NULL, #0x20  ; true
    // 0xa630b8: StoreField: r0->field_43 = r7
    //     0xa630b8: stur            w7, [x0, #0x43]
    // 0xa630bc: r8 = Instance_BoxShape
    //     0xa630bc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa630c0: ldr             x8, [x8, #0x80]
    // 0xa630c4: StoreField: r0->field_47 = r8
    //     0xa630c4: stur            w8, [x0, #0x47]
    // 0xa630c8: StoreField: r0->field_6f = r7
    //     0xa630c8: stur            w7, [x0, #0x6f]
    // 0xa630cc: r9 = false
    //     0xa630cc: add             x9, NULL, #0x30  ; false
    // 0xa630d0: StoreField: r0->field_73 = r9
    //     0xa630d0: stur            w9, [x0, #0x73]
    // 0xa630d4: StoreField: r0->field_83 = r7
    //     0xa630d4: stur            w7, [x0, #0x83]
    // 0xa630d8: StoreField: r0->field_7b = r9
    //     0xa630d8: stur            w9, [x0, #0x7b]
    // 0xa630dc: ldur            x2, [fp, #-0x38]
    // 0xa630e0: LoadField: r1 = r2->field_b
    //     0xa630e0: ldur            w1, [x2, #0xb]
    // 0xa630e4: LoadField: r3 = r2->field_f
    //     0xa630e4: ldur            w3, [x2, #0xf]
    // 0xa630e8: DecompressPointer r3
    //     0xa630e8: add             x3, x3, HEAP, lsl #32
    // 0xa630ec: LoadField: r4 = r3->field_b
    //     0xa630ec: ldur            w4, [x3, #0xb]
    // 0xa630f0: r3 = LoadInt32Instr(r1)
    //     0xa630f0: sbfx            x3, x1, #1, #0x1f
    // 0xa630f4: stur            x3, [fp, #-0x50]
    // 0xa630f8: r1 = LoadInt32Instr(r4)
    //     0xa630f8: sbfx            x1, x4, #1, #0x1f
    // 0xa630fc: cmp             x3, x1
    // 0xa63100: b.ne            #0xa6310c
    // 0xa63104: mov             x1, x2
    // 0xa63108: r0 = _growToNextCapacity()
    //     0xa63108: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa6310c: ldur            x4, [fp, #-0x28]
    // 0xa63110: ldur            x2, [fp, #-0x38]
    // 0xa63114: ldur            x3, [fp, #-0x50]
    // 0xa63118: add             x0, x3, #1
    // 0xa6311c: lsl             x1, x0, #1
    // 0xa63120: StoreField: r2->field_b = r1
    //     0xa63120: stur            w1, [x2, #0xb]
    // 0xa63124: LoadField: r1 = r2->field_f
    //     0xa63124: ldur            w1, [x2, #0xf]
    // 0xa63128: DecompressPointer r1
    //     0xa63128: add             x1, x1, HEAP, lsl #32
    // 0xa6312c: ldur            x0, [fp, #-0x30]
    // 0xa63130: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa63130: add             x25, x1, x3, lsl #2
    //     0xa63134: add             x25, x25, #0xf
    //     0xa63138: str             w0, [x25]
    //     0xa6313c: tbz             w0, #0, #0xa63158
    //     0xa63140: ldurb           w16, [x1, #-1]
    //     0xa63144: ldurb           w17, [x0, #-1]
    //     0xa63148: and             x16, x17, x16, lsr #2
    //     0xa6314c: tst             x16, HEAP, lsr #32
    //     0xa63150: b.eq            #0xa63158
    //     0xa63154: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa63158: r0 = Row()
    //     0xa63158: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa6315c: r10 = Instance_Axis
    //     0xa6315c: ldr             x10, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa63160: stur            x0, [fp, #-0x30]
    // 0xa63164: StoreField: r0->field_f = r10
    //     0xa63164: stur            w10, [x0, #0xf]
    // 0xa63168: r11 = Instance_MainAxisAlignment
    //     0xa63168: add             x11, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa6316c: ldr             x11, [x11, #0xd10]
    // 0xa63170: StoreField: r0->field_13 = r11
    //     0xa63170: stur            w11, [x0, #0x13]
    // 0xa63174: r1 = Instance_MainAxisSize
    //     0xa63174: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa63178: ldr             x1, [x1, #0xa10]
    // 0xa6317c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa6317c: stur            w1, [x0, #0x17]
    // 0xa63180: r12 = Instance_CrossAxisAlignment
    //     0xa63180: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa63184: ldr             x12, [x12, #0xa18]
    // 0xa63188: StoreField: r0->field_1b = r12
    //     0xa63188: stur            w12, [x0, #0x1b]
    // 0xa6318c: r2 = Instance_VerticalDirection
    //     0xa6318c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa63190: ldr             x2, [x2, #0xa20]
    // 0xa63194: StoreField: r0->field_23 = r2
    //     0xa63194: stur            w2, [x0, #0x23]
    // 0xa63198: r3 = Instance_Clip
    //     0xa63198: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa6319c: ldr             x3, [x3, #0x38]
    // 0xa631a0: StoreField: r0->field_2b = r3
    //     0xa631a0: stur            w3, [x0, #0x2b]
    // 0xa631a4: StoreField: r0->field_2f = rZR
    //     0xa631a4: stur            xzr, [x0, #0x2f]
    // 0xa631a8: ldur            x4, [fp, #-0x38]
    // 0xa631ac: StoreField: r0->field_b = r4
    //     0xa631ac: stur            w4, [x0, #0xb]
    // 0xa631b0: r0 = Padding()
    //     0xa631b0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa631b4: r13 = Instance_EdgeInsets
    //     0xa631b4: add             x13, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xa631b8: ldr             x13, [x13, #0x78]
    // 0xa631bc: stur            x0, [fp, #-0x38]
    // 0xa631c0: StoreField: r0->field_f = r13
    //     0xa631c0: stur            w13, [x0, #0xf]
    // 0xa631c4: ldur            x1, [fp, #-0x30]
    // 0xa631c8: StoreField: r0->field_b = r1
    //     0xa631c8: stur            w1, [x0, #0xb]
    // 0xa631cc: ldur            x2, [fp, #-0x28]
    // 0xa631d0: LoadField: r1 = r2->field_b
    //     0xa631d0: ldur            w1, [x2, #0xb]
    // 0xa631d4: LoadField: r3 = r2->field_f
    //     0xa631d4: ldur            w3, [x2, #0xf]
    // 0xa631d8: DecompressPointer r3
    //     0xa631d8: add             x3, x3, HEAP, lsl #32
    // 0xa631dc: LoadField: r4 = r3->field_b
    //     0xa631dc: ldur            w4, [x3, #0xb]
    // 0xa631e0: r3 = LoadInt32Instr(r1)
    //     0xa631e0: sbfx            x3, x1, #1, #0x1f
    // 0xa631e4: stur            x3, [fp, #-0x50]
    // 0xa631e8: r1 = LoadInt32Instr(r4)
    //     0xa631e8: sbfx            x1, x4, #1, #0x1f
    // 0xa631ec: cmp             x3, x1
    // 0xa631f0: b.ne            #0xa631fc
    // 0xa631f4: mov             x1, x2
    // 0xa631f8: r0 = _growToNextCapacity()
    //     0xa631f8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa631fc: ldur            x14, [fp, #-0x28]
    // 0xa63200: ldur            x2, [fp, #-0x50]
    // 0xa63204: add             x0, x2, #1
    // 0xa63208: lsl             x1, x0, #1
    // 0xa6320c: StoreField: r14->field_b = r1
    //     0xa6320c: stur            w1, [x14, #0xb]
    // 0xa63210: LoadField: r1 = r14->field_f
    //     0xa63210: ldur            w1, [x14, #0xf]
    // 0xa63214: DecompressPointer r1
    //     0xa63214: add             x1, x1, HEAP, lsl #32
    // 0xa63218: ldur            x0, [fp, #-0x38]
    // 0xa6321c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa6321c: add             x25, x1, x2, lsl #2
    //     0xa63220: add             x25, x25, #0xf
    //     0xa63224: str             w0, [x25]
    //     0xa63228: tbz             w0, #0, #0xa63244
    //     0xa6322c: ldurb           w16, [x1, #-1]
    //     0xa63230: ldurb           w17, [x0, #-1]
    //     0xa63234: and             x16, x17, x16, lsr #2
    //     0xa63238: tst             x16, HEAP, lsr #32
    //     0xa6323c: b.eq            #0xa63244
    //     0xa63240: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa63244: mov             x3, x14
    // 0xa63248: b               #0xa63fd4
    // 0xa6324c: mov             x14, x3
    // 0xa63250: r7 = true
    //     0xa63250: add             x7, NULL, #0x20  ; true
    // 0xa63254: r0 = Instance_BorderRadius
    //     0xa63254: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa63258: ldr             x0, [x0, #0xbe8]
    // 0xa6325c: r4 = Instance_EdgeInsets
    //     0xa6325c: add             x4, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa63260: ldr             x4, [x4, #0x898]
    // 0xa63264: r5 = Instance_EdgeInsets
    //     0xa63264: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xa63268: ldr             x5, [x5, #0xd48]
    // 0xa6326c: r11 = Instance_MainAxisAlignment
    //     0xa6326c: add             x11, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa63270: ldr             x11, [x11, #0xd10]
    // 0xa63274: r13 = Instance_EdgeInsets
    //     0xa63274: add             x13, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xa63278: ldr             x13, [x13, #0x78]
    // 0xa6327c: r9 = false
    //     0xa6327c: add             x9, NULL, #0x30  ; false
    // 0xa63280: r6 = Instance_SizedBox
    //     0xa63280: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa63284: r8 = Instance_BoxShape
    //     0xa63284: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa63288: ldr             x8, [x8, #0x80]
    // 0xa6328c: r12 = Instance_CrossAxisAlignment
    //     0xa6328c: add             x12, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa63290: ldr             x12, [x12, #0xa18]
    // 0xa63294: r10 = Instance_Axis
    //     0xa63294: ldr             x10, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa63298: r3 = Instance_Alignment
    //     0xa63298: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa6329c: ldr             x3, [x3, #0xb10]
    // 0xa632a0: cmp             w2, NULL
    // 0xa632a4: b.ne            #0xa632c0
    // 0xa632a8: mov             x14, x13
    // 0xa632ac: mov             x13, x12
    // 0xa632b0: mov             x12, x11
    // 0xa632b4: mov             x11, x10
    // 0xa632b8: r10 = 6
    //     0xa632b8: movz            x10, #0x6
    // 0xa632bc: b               #0xa636c4
    // 0xa632c0: LoadField: r19 = r2->field_7
    //     0xa632c0: ldur            w19, [x2, #7]
    // 0xa632c4: DecompressPointer r19
    //     0xa632c4: add             x19, x19, HEAP, lsl #32
    // 0xa632c8: r16 = Instance_TitleAlignment
    //     0xa632c8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xa632cc: ldr             x16, [x16, #0x520]
    // 0xa632d0: cmp             w19, w16
    // 0xa632d4: b.ne            #0xa636b0
    // 0xa632d8: LoadField: r2 = r1->field_2f
    //     0xa632d8: ldur            w2, [x1, #0x2f]
    // 0xa632dc: DecompressPointer r2
    //     0xa632dc: add             x2, x2, HEAP, lsl #32
    // 0xa632e0: cmp             w2, NULL
    // 0xa632e4: b.ne            #0xa632f0
    // 0xa632e8: r1 = Null
    //     0xa632e8: mov             x1, NULL
    // 0xa632ec: b               #0xa6331c
    // 0xa632f0: LoadField: r1 = r2->field_7
    //     0xa632f0: ldur            w1, [x2, #7]
    // 0xa632f4: DecompressPointer r1
    //     0xa632f4: add             x1, x1, HEAP, lsl #32
    // 0xa632f8: cmp             w1, NULL
    // 0xa632fc: b.ne            #0xa63308
    // 0xa63300: r1 = Null
    //     0xa63300: mov             x1, NULL
    // 0xa63304: b               #0xa6331c
    // 0xa63308: LoadField: r2 = r1->field_7
    //     0xa63308: ldur            w2, [x1, #7]
    // 0xa6330c: cbnz            w2, #0xa63318
    // 0xa63310: r1 = false
    //     0xa63310: add             x1, NULL, #0x30  ; false
    // 0xa63314: b               #0xa6331c
    // 0xa63318: r1 = true
    //     0xa63318: add             x1, NULL, #0x20  ; true
    // 0xa6331c: cmp             w1, NULL
    // 0xa63320: b.ne            #0xa6332c
    // 0xa63324: r5 = false
    //     0xa63324: add             x5, NULL, #0x30  ; false
    // 0xa63328: b               #0xa63330
    // 0xa6332c: mov             x5, x1
    // 0xa63330: ldur            x2, [fp, #-8]
    // 0xa63334: ldur            x1, [fp, #-0x10]
    // 0xa63338: stur            x5, [fp, #-0x30]
    // 0xa6333c: r0 = of()
    //     0xa6333c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa63340: LoadField: r2 = r0->field_5b
    //     0xa63340: ldur            w2, [x0, #0x5b]
    // 0xa63344: DecompressPointer r2
    //     0xa63344: add             x2, x2, HEAP, lsl #32
    // 0xa63348: r1 = Null
    //     0xa63348: mov             x1, NULL
    // 0xa6334c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa6334c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa63350: r0 = Border.all()
    //     0xa63350: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa63354: stur            x0, [fp, #-0x38]
    // 0xa63358: r0 = BoxDecoration()
    //     0xa63358: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa6335c: mov             x2, x0
    // 0xa63360: ldur            x0, [fp, #-0x38]
    // 0xa63364: stur            x2, [fp, #-0x40]
    // 0xa63368: StoreField: r2->field_f = r0
    //     0xa63368: stur            w0, [x2, #0xf]
    // 0xa6336c: r0 = Instance_BorderRadius
    //     0xa6336c: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa63370: ldr             x0, [x0, #0xbe8]
    // 0xa63374: StoreField: r2->field_13 = r0
    //     0xa63374: stur            w0, [x2, #0x13]
    // 0xa63378: r0 = Instance_BoxShape
    //     0xa63378: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa6337c: ldr             x0, [x0, #0x80]
    // 0xa63380: StoreField: r2->field_23 = r0
    //     0xa63380: stur            w0, [x2, #0x23]
    // 0xa63384: ldur            x3, [fp, #-8]
    // 0xa63388: LoadField: r1 = r3->field_b
    //     0xa63388: ldur            w1, [x3, #0xb]
    // 0xa6338c: DecompressPointer r1
    //     0xa6338c: add             x1, x1, HEAP, lsl #32
    // 0xa63390: cmp             w1, NULL
    // 0xa63394: b.eq            #0xa64368
    // 0xa63398: LoadField: r4 = r1->field_2f
    //     0xa63398: ldur            w4, [x1, #0x2f]
    // 0xa6339c: DecompressPointer r4
    //     0xa6339c: add             x4, x4, HEAP, lsl #32
    // 0xa633a0: cmp             w4, NULL
    // 0xa633a4: b.ne            #0xa633b0
    // 0xa633a8: r1 = Null
    //     0xa633a8: mov             x1, NULL
    // 0xa633ac: b               #0xa633b8
    // 0xa633b0: LoadField: r1 = r4->field_7
    //     0xa633b0: ldur            w1, [x4, #7]
    // 0xa633b4: DecompressPointer r1
    //     0xa633b4: add             x1, x1, HEAP, lsl #32
    // 0xa633b8: cmp             w1, NULL
    // 0xa633bc: b.ne            #0xa633c8
    // 0xa633c0: r5 = ""
    //     0xa633c0: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa633c4: b               #0xa633cc
    // 0xa633c8: mov             x5, x1
    // 0xa633cc: ldur            x4, [fp, #-0x30]
    // 0xa633d0: ldur            x1, [fp, #-0x10]
    // 0xa633d4: stur            x5, [fp, #-0x38]
    // 0xa633d8: r0 = of()
    //     0xa633d8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa633dc: LoadField: r1 = r0->field_87
    //     0xa633dc: ldur            w1, [x0, #0x87]
    // 0xa633e0: DecompressPointer r1
    //     0xa633e0: add             x1, x1, HEAP, lsl #32
    // 0xa633e4: LoadField: r0 = r1->field_7
    //     0xa633e4: ldur            w0, [x1, #7]
    // 0xa633e8: DecompressPointer r0
    //     0xa633e8: add             x0, x0, HEAP, lsl #32
    // 0xa633ec: ldur            x1, [fp, #-0x10]
    // 0xa633f0: stur            x0, [fp, #-0x48]
    // 0xa633f4: r0 = of()
    //     0xa633f4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa633f8: LoadField: r1 = r0->field_5b
    //     0xa633f8: ldur            w1, [x0, #0x5b]
    // 0xa633fc: DecompressPointer r1
    //     0xa633fc: add             x1, x1, HEAP, lsl #32
    // 0xa63400: r0 = LoadClassIdInstr(r1)
    //     0xa63400: ldur            x0, [x1, #-1]
    //     0xa63404: ubfx            x0, x0, #0xc, #0x14
    // 0xa63408: d0 = 0.700000
    //     0xa63408: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa6340c: ldr             d0, [x17, #0xf48]
    // 0xa63410: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa63410: sub             lr, x0, #0xffa
    //     0xa63414: ldr             lr, [x21, lr, lsl #3]
    //     0xa63418: blr             lr
    // 0xa6341c: r16 = 14.000000
    //     0xa6341c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa63420: ldr             x16, [x16, #0x1d8]
    // 0xa63424: stp             x0, x16, [SP]
    // 0xa63428: ldur            x1, [fp, #-0x48]
    // 0xa6342c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa6342c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa63430: ldr             x4, [x4, #0xaa0]
    // 0xa63434: r0 = copyWith()
    //     0xa63434: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa63438: stur            x0, [fp, #-0x48]
    // 0xa6343c: r0 = Text()
    //     0xa6343c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa63440: mov             x1, x0
    // 0xa63444: ldur            x0, [fp, #-0x38]
    // 0xa63448: stur            x1, [fp, #-0x58]
    // 0xa6344c: StoreField: r1->field_b = r0
    //     0xa6344c: stur            w0, [x1, #0xb]
    // 0xa63450: ldur            x0, [fp, #-0x48]
    // 0xa63454: StoreField: r1->field_13 = r0
    //     0xa63454: stur            w0, [x1, #0x13]
    // 0xa63458: r0 = Center()
    //     0xa63458: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa6345c: r3 = Instance_Alignment
    //     0xa6345c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa63460: ldr             x3, [x3, #0xb10]
    // 0xa63464: stur            x0, [fp, #-0x38]
    // 0xa63468: StoreField: r0->field_f = r3
    //     0xa63468: stur            w3, [x0, #0xf]
    // 0xa6346c: ldur            x1, [fp, #-0x58]
    // 0xa63470: StoreField: r0->field_b = r1
    //     0xa63470: stur            w1, [x0, #0xb]
    // 0xa63474: r0 = Padding()
    //     0xa63474: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa63478: r4 = Instance_EdgeInsets
    //     0xa63478: add             x4, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa6347c: ldr             x4, [x4, #0x898]
    // 0xa63480: stur            x0, [fp, #-0x48]
    // 0xa63484: StoreField: r0->field_f = r4
    //     0xa63484: stur            w4, [x0, #0xf]
    // 0xa63488: ldur            x1, [fp, #-0x38]
    // 0xa6348c: StoreField: r0->field_b = r1
    //     0xa6348c: stur            w1, [x0, #0xb]
    // 0xa63490: r0 = Container()
    //     0xa63490: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa63494: stur            x0, [fp, #-0x38]
    // 0xa63498: r16 = 32.000000
    //     0xa63498: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa6349c: ldr             x16, [x16, #0x848]
    // 0xa634a0: r30 = 90.000000
    //     0xa634a0: add             lr, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xa634a4: ldr             lr, [lr, #0xe68]
    // 0xa634a8: stp             lr, x16, [SP, #0x10]
    // 0xa634ac: ldur            x16, [fp, #-0x40]
    // 0xa634b0: ldur            lr, [fp, #-0x48]
    // 0xa634b4: stp             lr, x16, [SP]
    // 0xa634b8: mov             x1, x0
    // 0xa634bc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa634bc: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa634c0: ldr             x4, [x4, #0x8c0]
    // 0xa634c4: r0 = Container()
    //     0xa634c4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa634c8: r0 = Visibility()
    //     0xa634c8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa634cc: mov             x1, x0
    // 0xa634d0: ldur            x0, [fp, #-0x38]
    // 0xa634d4: stur            x1, [fp, #-0x40]
    // 0xa634d8: StoreField: r1->field_b = r0
    //     0xa634d8: stur            w0, [x1, #0xb]
    // 0xa634dc: r6 = Instance_SizedBox
    //     0xa634dc: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa634e0: StoreField: r1->field_f = r6
    //     0xa634e0: stur            w6, [x1, #0xf]
    // 0xa634e4: ldur            x0, [fp, #-0x30]
    // 0xa634e8: StoreField: r1->field_13 = r0
    //     0xa634e8: stur            w0, [x1, #0x13]
    // 0xa634ec: r0 = false
    //     0xa634ec: add             x0, NULL, #0x30  ; false
    // 0xa634f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa634f0: stur            w0, [x1, #0x17]
    // 0xa634f4: StoreField: r1->field_1b = r0
    //     0xa634f4: stur            w0, [x1, #0x1b]
    // 0xa634f8: StoreField: r1->field_1f = r0
    //     0xa634f8: stur            w0, [x1, #0x1f]
    // 0xa634fc: StoreField: r1->field_23 = r0
    //     0xa634fc: stur            w0, [x1, #0x23]
    // 0xa63500: StoreField: r1->field_27 = r0
    //     0xa63500: stur            w0, [x1, #0x27]
    // 0xa63504: StoreField: r1->field_2b = r0
    //     0xa63504: stur            w0, [x1, #0x2b]
    // 0xa63508: r0 = InkWell()
    //     0xa63508: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa6350c: mov             x3, x0
    // 0xa63510: ldur            x0, [fp, #-0x40]
    // 0xa63514: stur            x3, [fp, #-0x30]
    // 0xa63518: StoreField: r3->field_b = r0
    //     0xa63518: stur            w0, [x3, #0xb]
    // 0xa6351c: ldur            x2, [fp, #-0x20]
    // 0xa63520: r1 = Function '<anonymous closure>':.
    //     0xa63520: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a338] AnonymousClosure: (0xa665d8), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xa62b0c)
    //     0xa63524: ldr             x1, [x1, #0x338]
    // 0xa63528: r0 = AllocateClosure()
    //     0xa63528: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa6352c: mov             x1, x0
    // 0xa63530: ldur            x0, [fp, #-0x30]
    // 0xa63534: StoreField: r0->field_f = r1
    //     0xa63534: stur            w1, [x0, #0xf]
    // 0xa63538: r7 = true
    //     0xa63538: add             x7, NULL, #0x20  ; true
    // 0xa6353c: StoreField: r0->field_43 = r7
    //     0xa6353c: stur            w7, [x0, #0x43]
    // 0xa63540: r8 = Instance_BoxShape
    //     0xa63540: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa63544: ldr             x8, [x8, #0x80]
    // 0xa63548: StoreField: r0->field_47 = r8
    //     0xa63548: stur            w8, [x0, #0x47]
    // 0xa6354c: StoreField: r0->field_6f = r7
    //     0xa6354c: stur            w7, [x0, #0x6f]
    // 0xa63550: r9 = false
    //     0xa63550: add             x9, NULL, #0x30  ; false
    // 0xa63554: StoreField: r0->field_73 = r9
    //     0xa63554: stur            w9, [x0, #0x73]
    // 0xa63558: StoreField: r0->field_83 = r7
    //     0xa63558: stur            w7, [x0, #0x83]
    // 0xa6355c: StoreField: r0->field_7b = r9
    //     0xa6355c: stur            w9, [x0, #0x7b]
    // 0xa63560: ldur            x2, [fp, #-8]
    // 0xa63564: LoadField: r1 = r2->field_b
    //     0xa63564: ldur            w1, [x2, #0xb]
    // 0xa63568: DecompressPointer r1
    //     0xa63568: add             x1, x1, HEAP, lsl #32
    // 0xa6356c: cmp             w1, NULL
    // 0xa63570: b.eq            #0xa6436c
    // 0xa63574: LoadField: r3 = r1->field_f
    //     0xa63574: ldur            w3, [x1, #0xf]
    // 0xa63578: DecompressPointer r3
    //     0xa63578: add             x3, x3, HEAP, lsl #32
    // 0xa6357c: ldur            x1, [fp, #-0x10]
    // 0xa63580: stur            x3, [fp, #-0x38]
    // 0xa63584: r0 = of()
    //     0xa63584: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa63588: LoadField: r1 = r0->field_87
    //     0xa63588: ldur            w1, [x0, #0x87]
    // 0xa6358c: DecompressPointer r1
    //     0xa6358c: add             x1, x1, HEAP, lsl #32
    // 0xa63590: LoadField: r0 = r1->field_27
    //     0xa63590: ldur            w0, [x1, #0x27]
    // 0xa63594: DecompressPointer r0
    //     0xa63594: add             x0, x0, HEAP, lsl #32
    // 0xa63598: stur            x0, [fp, #-0x40]
    // 0xa6359c: r1 = Instance_Color
    //     0xa6359c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa635a0: d0 = 0.700000
    //     0xa635a0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa635a4: ldr             d0, [x17, #0xf48]
    // 0xa635a8: r0 = withOpacity()
    //     0xa635a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa635ac: r16 = 21.000000
    //     0xa635ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa635b0: ldr             x16, [x16, #0x9b0]
    // 0xa635b4: stp             x0, x16, [SP]
    // 0xa635b8: ldur            x1, [fp, #-0x40]
    // 0xa635bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa635bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa635c0: ldr             x4, [x4, #0xaa0]
    // 0xa635c4: r0 = copyWith()
    //     0xa635c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa635c8: stur            x0, [fp, #-0x40]
    // 0xa635cc: r0 = Text()
    //     0xa635cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa635d0: mov             x3, x0
    // 0xa635d4: ldur            x0, [fp, #-0x38]
    // 0xa635d8: stur            x3, [fp, #-0x48]
    // 0xa635dc: StoreField: r3->field_b = r0
    //     0xa635dc: stur            w0, [x3, #0xb]
    // 0xa635e0: ldur            x0, [fp, #-0x40]
    // 0xa635e4: StoreField: r3->field_13 = r0
    //     0xa635e4: stur            w0, [x3, #0x13]
    // 0xa635e8: r1 = Null
    //     0xa635e8: mov             x1, NULL
    // 0xa635ec: r2 = 6
    //     0xa635ec: movz            x2, #0x6
    // 0xa635f0: r0 = AllocateArray()
    //     0xa635f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa635f4: mov             x2, x0
    // 0xa635f8: ldur            x0, [fp, #-0x30]
    // 0xa635fc: stur            x2, [fp, #-0x38]
    // 0xa63600: StoreField: r2->field_f = r0
    //     0xa63600: stur            w0, [x2, #0xf]
    // 0xa63604: r16 = Instance_Spacer
    //     0xa63604: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa63608: ldr             x16, [x16, #0xf0]
    // 0xa6360c: StoreField: r2->field_13 = r16
    //     0xa6360c: stur            w16, [x2, #0x13]
    // 0xa63610: ldur            x0, [fp, #-0x48]
    // 0xa63614: ArrayStore: r2[0] = r0  ; List_4
    //     0xa63614: stur            w0, [x2, #0x17]
    // 0xa63618: r1 = <Widget>
    //     0xa63618: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa6361c: r0 = AllocateGrowableArray()
    //     0xa6361c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa63620: mov             x1, x0
    // 0xa63624: ldur            x0, [fp, #-0x38]
    // 0xa63628: stur            x1, [fp, #-0x30]
    // 0xa6362c: StoreField: r1->field_f = r0
    //     0xa6362c: stur            w0, [x1, #0xf]
    // 0xa63630: r10 = 6
    //     0xa63630: movz            x10, #0x6
    // 0xa63634: StoreField: r1->field_b = r10
    //     0xa63634: stur            w10, [x1, #0xb]
    // 0xa63638: r0 = Row()
    //     0xa63638: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa6363c: r11 = Instance_Axis
    //     0xa6363c: ldr             x11, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa63640: stur            x0, [fp, #-0x38]
    // 0xa63644: StoreField: r0->field_f = r11
    //     0xa63644: stur            w11, [x0, #0xf]
    // 0xa63648: r12 = Instance_MainAxisAlignment
    //     0xa63648: add             x12, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa6364c: ldr             x12, [x12, #0xd10]
    // 0xa63650: StoreField: r0->field_13 = r12
    //     0xa63650: stur            w12, [x0, #0x13]
    // 0xa63654: r1 = Instance_MainAxisSize
    //     0xa63654: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa63658: ldr             x1, [x1, #0xa10]
    // 0xa6365c: ArrayStore: r0[0] = r1  ; List_4
    //     0xa6365c: stur            w1, [x0, #0x17]
    // 0xa63660: r13 = Instance_CrossAxisAlignment
    //     0xa63660: add             x13, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa63664: ldr             x13, [x13, #0xa18]
    // 0xa63668: StoreField: r0->field_1b = r13
    //     0xa63668: stur            w13, [x0, #0x1b]
    // 0xa6366c: r2 = Instance_VerticalDirection
    //     0xa6366c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa63670: ldr             x2, [x2, #0xa20]
    // 0xa63674: StoreField: r0->field_23 = r2
    //     0xa63674: stur            w2, [x0, #0x23]
    // 0xa63678: r3 = Instance_Clip
    //     0xa63678: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa6367c: ldr             x3, [x3, #0x38]
    // 0xa63680: StoreField: r0->field_2b = r3
    //     0xa63680: stur            w3, [x0, #0x2b]
    // 0xa63684: StoreField: r0->field_2f = rZR
    //     0xa63684: stur            xzr, [x0, #0x2f]
    // 0xa63688: ldur            x4, [fp, #-0x30]
    // 0xa6368c: StoreField: r0->field_b = r4
    //     0xa6368c: stur            w4, [x0, #0xb]
    // 0xa63690: r0 = Padding()
    //     0xa63690: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa63694: r14 = Instance_EdgeInsets
    //     0xa63694: add             x14, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xa63698: ldr             x14, [x14, #0x78]
    // 0xa6369c: StoreField: r0->field_f = r14
    //     0xa6369c: stur            w14, [x0, #0xf]
    // 0xa636a0: ldur            x1, [fp, #-0x38]
    // 0xa636a4: StoreField: r0->field_b = r1
    //     0xa636a4: stur            w1, [x0, #0xb]
    // 0xa636a8: mov             x2, x0
    // 0xa636ac: b               #0xa63f58
    // 0xa636b0: mov             x14, x13
    // 0xa636b4: mov             x13, x12
    // 0xa636b8: mov             x12, x11
    // 0xa636bc: mov             x11, x10
    // 0xa636c0: r10 = 6
    //     0xa636c0: movz            x10, #0x6
    // 0xa636c4: cmp             w2, NULL
    // 0xa636c8: b.ne            #0xa63704
    // 0xa636cc: mov             x2, x3
    // 0xa636d0: mov             x3, x0
    // 0xa636d4: mov             x0, x14
    // 0xa636d8: mov             x20, x13
    // 0xa636dc: r14 = Instance_MainAxisAlignment
    //     0xa636dc: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa636e0: ldr             x14, [x14, #0xa08]
    // 0xa636e4: r19 = Instance_MainAxisSize
    //     0xa636e4: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa636e8: ldr             x19, [x19, #0xa10]
    // 0xa636ec: r23 = Instance_VerticalDirection
    //     0xa636ec: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa636f0: ldr             x23, [x23, #0xa20]
    // 0xa636f4: r13 = Instance_Axis
    //     0xa636f4: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa636f8: r24 = Instance_Clip
    //     0xa636f8: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa636fc: ldr             x24, [x24, #0x38]
    // 0xa63700: b               #0xa63b4c
    // 0xa63704: LoadField: r19 = r2->field_7
    //     0xa63704: ldur            w19, [x2, #7]
    // 0xa63708: DecompressPointer r19
    //     0xa63708: add             x19, x19, HEAP, lsl #32
    // 0xa6370c: r16 = Instance_TitleAlignment
    //     0xa6370c: add             x16, PP, #0x24, lsl #12  ; [pp+0x24510] Obj!TitleAlignment@d75601
    //     0xa63710: ldr             x16, [x16, #0x510]
    // 0xa63714: cmp             w19, w16
    // 0xa63718: b.ne            #0xa63b18
    // 0xa6371c: ldur            x2, [fp, #-8]
    // 0xa63720: LoadField: r5 = r1->field_f
    //     0xa63720: ldur            w5, [x1, #0xf]
    // 0xa63724: DecompressPointer r5
    //     0xa63724: add             x5, x5, HEAP, lsl #32
    // 0xa63728: ldur            x1, [fp, #-0x10]
    // 0xa6372c: stur            x5, [fp, #-0x30]
    // 0xa63730: r0 = of()
    //     0xa63730: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa63734: LoadField: r1 = r0->field_87
    //     0xa63734: ldur            w1, [x0, #0x87]
    // 0xa63738: DecompressPointer r1
    //     0xa63738: add             x1, x1, HEAP, lsl #32
    // 0xa6373c: LoadField: r0 = r1->field_27
    //     0xa6373c: ldur            w0, [x1, #0x27]
    // 0xa63740: DecompressPointer r0
    //     0xa63740: add             x0, x0, HEAP, lsl #32
    // 0xa63744: stur            x0, [fp, #-0x38]
    // 0xa63748: r1 = Instance_Color
    //     0xa63748: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa6374c: d0 = 0.700000
    //     0xa6374c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa63750: ldr             d0, [x17, #0xf48]
    // 0xa63754: r0 = withOpacity()
    //     0xa63754: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa63758: r16 = 21.000000
    //     0xa63758: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa6375c: ldr             x16, [x16, #0x9b0]
    // 0xa63760: stp             x0, x16, [SP]
    // 0xa63764: ldur            x1, [fp, #-0x38]
    // 0xa63768: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa63768: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa6376c: ldr             x4, [x4, #0xaa0]
    // 0xa63770: r0 = copyWith()
    //     0xa63770: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa63774: stur            x0, [fp, #-0x38]
    // 0xa63778: r0 = Text()
    //     0xa63778: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa6377c: mov             x1, x0
    // 0xa63780: ldur            x0, [fp, #-0x30]
    // 0xa63784: stur            x1, [fp, #-0x40]
    // 0xa63788: StoreField: r1->field_b = r0
    //     0xa63788: stur            w0, [x1, #0xb]
    // 0xa6378c: ldur            x0, [fp, #-0x38]
    // 0xa63790: StoreField: r1->field_13 = r0
    //     0xa63790: stur            w0, [x1, #0x13]
    // 0xa63794: r0 = Padding()
    //     0xa63794: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa63798: mov             x2, x0
    // 0xa6379c: r0 = Instance_EdgeInsets
    //     0xa6379c: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xa637a0: ldr             x0, [x0, #0x78]
    // 0xa637a4: stur            x2, [fp, #-0x38]
    // 0xa637a8: StoreField: r2->field_f = r0
    //     0xa637a8: stur            w0, [x2, #0xf]
    // 0xa637ac: ldur            x0, [fp, #-0x40]
    // 0xa637b0: StoreField: r2->field_b = r0
    //     0xa637b0: stur            w0, [x2, #0xb]
    // 0xa637b4: ldur            x0, [fp, #-8]
    // 0xa637b8: LoadField: r1 = r0->field_b
    //     0xa637b8: ldur            w1, [x0, #0xb]
    // 0xa637bc: DecompressPointer r1
    //     0xa637bc: add             x1, x1, HEAP, lsl #32
    // 0xa637c0: cmp             w1, NULL
    // 0xa637c4: b.eq            #0xa64370
    // 0xa637c8: LoadField: r3 = r1->field_2f
    //     0xa637c8: ldur            w3, [x1, #0x2f]
    // 0xa637cc: DecompressPointer r3
    //     0xa637cc: add             x3, x3, HEAP, lsl #32
    // 0xa637d0: cmp             w3, NULL
    // 0xa637d4: b.ne            #0xa637e0
    // 0xa637d8: r1 = Null
    //     0xa637d8: mov             x1, NULL
    // 0xa637dc: b               #0xa6380c
    // 0xa637e0: LoadField: r1 = r3->field_7
    //     0xa637e0: ldur            w1, [x3, #7]
    // 0xa637e4: DecompressPointer r1
    //     0xa637e4: add             x1, x1, HEAP, lsl #32
    // 0xa637e8: cmp             w1, NULL
    // 0xa637ec: b.ne            #0xa637f8
    // 0xa637f0: r1 = Null
    //     0xa637f0: mov             x1, NULL
    // 0xa637f4: b               #0xa6380c
    // 0xa637f8: LoadField: r3 = r1->field_7
    //     0xa637f8: ldur            w3, [x1, #7]
    // 0xa637fc: cbnz            w3, #0xa63808
    // 0xa63800: r1 = false
    //     0xa63800: add             x1, NULL, #0x30  ; false
    // 0xa63804: b               #0xa6380c
    // 0xa63808: r1 = true
    //     0xa63808: add             x1, NULL, #0x20  ; true
    // 0xa6380c: cmp             w1, NULL
    // 0xa63810: b.ne            #0xa6381c
    // 0xa63814: r3 = false
    //     0xa63814: add             x3, NULL, #0x30  ; false
    // 0xa63818: b               #0xa63820
    // 0xa6381c: mov             x3, x1
    // 0xa63820: ldur            x1, [fp, #-0x10]
    // 0xa63824: stur            x3, [fp, #-0x30]
    // 0xa63828: r0 = of()
    //     0xa63828: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa6382c: LoadField: r2 = r0->field_5b
    //     0xa6382c: ldur            w2, [x0, #0x5b]
    // 0xa63830: DecompressPointer r2
    //     0xa63830: add             x2, x2, HEAP, lsl #32
    // 0xa63834: r1 = Null
    //     0xa63834: mov             x1, NULL
    // 0xa63838: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa63838: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa6383c: r0 = Border.all()
    //     0xa6383c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa63840: stur            x0, [fp, #-0x40]
    // 0xa63844: r0 = BoxDecoration()
    //     0xa63844: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa63848: mov             x2, x0
    // 0xa6384c: ldur            x0, [fp, #-0x40]
    // 0xa63850: stur            x2, [fp, #-0x48]
    // 0xa63854: StoreField: r2->field_f = r0
    //     0xa63854: stur            w0, [x2, #0xf]
    // 0xa63858: r3 = Instance_BorderRadius
    //     0xa63858: add             x3, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa6385c: ldr             x3, [x3, #0xbe8]
    // 0xa63860: StoreField: r2->field_13 = r3
    //     0xa63860: stur            w3, [x2, #0x13]
    // 0xa63864: r0 = Instance_BoxShape
    //     0xa63864: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa63868: ldr             x0, [x0, #0x80]
    // 0xa6386c: StoreField: r2->field_23 = r0
    //     0xa6386c: stur            w0, [x2, #0x23]
    // 0xa63870: ldur            x3, [fp, #-8]
    // 0xa63874: LoadField: r1 = r3->field_b
    //     0xa63874: ldur            w1, [x3, #0xb]
    // 0xa63878: DecompressPointer r1
    //     0xa63878: add             x1, x1, HEAP, lsl #32
    // 0xa6387c: cmp             w1, NULL
    // 0xa63880: b.eq            #0xa64374
    // 0xa63884: LoadField: r4 = r1->field_2f
    //     0xa63884: ldur            w4, [x1, #0x2f]
    // 0xa63888: DecompressPointer r4
    //     0xa63888: add             x4, x4, HEAP, lsl #32
    // 0xa6388c: cmp             w4, NULL
    // 0xa63890: b.ne            #0xa6389c
    // 0xa63894: r1 = Null
    //     0xa63894: mov             x1, NULL
    // 0xa63898: b               #0xa638a4
    // 0xa6389c: LoadField: r1 = r4->field_7
    //     0xa6389c: ldur            w1, [x4, #7]
    // 0xa638a0: DecompressPointer r1
    //     0xa638a0: add             x1, x1, HEAP, lsl #32
    // 0xa638a4: cmp             w1, NULL
    // 0xa638a8: b.ne            #0xa638b4
    // 0xa638ac: r6 = ""
    //     0xa638ac: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa638b0: b               #0xa638b8
    // 0xa638b4: mov             x6, x1
    // 0xa638b8: ldur            x4, [fp, #-0x38]
    // 0xa638bc: ldur            x5, [fp, #-0x30]
    // 0xa638c0: ldur            x1, [fp, #-0x10]
    // 0xa638c4: stur            x6, [fp, #-0x40]
    // 0xa638c8: r0 = of()
    //     0xa638c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa638cc: LoadField: r1 = r0->field_87
    //     0xa638cc: ldur            w1, [x0, #0x87]
    // 0xa638d0: DecompressPointer r1
    //     0xa638d0: add             x1, x1, HEAP, lsl #32
    // 0xa638d4: LoadField: r0 = r1->field_7
    //     0xa638d4: ldur            w0, [x1, #7]
    // 0xa638d8: DecompressPointer r0
    //     0xa638d8: add             x0, x0, HEAP, lsl #32
    // 0xa638dc: ldur            x1, [fp, #-0x10]
    // 0xa638e0: stur            x0, [fp, #-0x58]
    // 0xa638e4: r0 = of()
    //     0xa638e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa638e8: LoadField: r1 = r0->field_5b
    //     0xa638e8: ldur            w1, [x0, #0x5b]
    // 0xa638ec: DecompressPointer r1
    //     0xa638ec: add             x1, x1, HEAP, lsl #32
    // 0xa638f0: r0 = LoadClassIdInstr(r1)
    //     0xa638f0: ldur            x0, [x1, #-1]
    //     0xa638f4: ubfx            x0, x0, #0xc, #0x14
    // 0xa638f8: d0 = 0.700000
    //     0xa638f8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa638fc: ldr             d0, [x17, #0xf48]
    // 0xa63900: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa63900: sub             lr, x0, #0xffa
    //     0xa63904: ldr             lr, [x21, lr, lsl #3]
    //     0xa63908: blr             lr
    // 0xa6390c: r16 = 14.000000
    //     0xa6390c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa63910: ldr             x16, [x16, #0x1d8]
    // 0xa63914: stp             x0, x16, [SP]
    // 0xa63918: ldur            x1, [fp, #-0x58]
    // 0xa6391c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa6391c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa63920: ldr             x4, [x4, #0xaa0]
    // 0xa63924: r0 = copyWith()
    //     0xa63924: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa63928: stur            x0, [fp, #-0x58]
    // 0xa6392c: r0 = Text()
    //     0xa6392c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa63930: mov             x1, x0
    // 0xa63934: ldur            x0, [fp, #-0x40]
    // 0xa63938: stur            x1, [fp, #-0x60]
    // 0xa6393c: StoreField: r1->field_b = r0
    //     0xa6393c: stur            w0, [x1, #0xb]
    // 0xa63940: ldur            x0, [fp, #-0x58]
    // 0xa63944: StoreField: r1->field_13 = r0
    //     0xa63944: stur            w0, [x1, #0x13]
    // 0xa63948: r0 = Center()
    //     0xa63948: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa6394c: r2 = Instance_Alignment
    //     0xa6394c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa63950: ldr             x2, [x2, #0xb10]
    // 0xa63954: stur            x0, [fp, #-0x40]
    // 0xa63958: StoreField: r0->field_f = r2
    //     0xa63958: stur            w2, [x0, #0xf]
    // 0xa6395c: ldur            x1, [fp, #-0x60]
    // 0xa63960: StoreField: r0->field_b = r1
    //     0xa63960: stur            w1, [x0, #0xb]
    // 0xa63964: r0 = Padding()
    //     0xa63964: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa63968: r4 = Instance_EdgeInsets
    //     0xa63968: add             x4, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa6396c: ldr             x4, [x4, #0x898]
    // 0xa63970: stur            x0, [fp, #-0x58]
    // 0xa63974: StoreField: r0->field_f = r4
    //     0xa63974: stur            w4, [x0, #0xf]
    // 0xa63978: ldur            x1, [fp, #-0x40]
    // 0xa6397c: StoreField: r0->field_b = r1
    //     0xa6397c: stur            w1, [x0, #0xb]
    // 0xa63980: r0 = Container()
    //     0xa63980: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa63984: stur            x0, [fp, #-0x40]
    // 0xa63988: r16 = 32.000000
    //     0xa63988: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa6398c: ldr             x16, [x16, #0x848]
    // 0xa63990: r30 = 90.000000
    //     0xa63990: add             lr, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xa63994: ldr             lr, [lr, #0xe68]
    // 0xa63998: stp             lr, x16, [SP, #0x10]
    // 0xa6399c: ldur            x16, [fp, #-0x48]
    // 0xa639a0: ldur            lr, [fp, #-0x58]
    // 0xa639a4: stp             lr, x16, [SP]
    // 0xa639a8: mov             x1, x0
    // 0xa639ac: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa639ac: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa639b0: ldr             x4, [x4, #0x8c0]
    // 0xa639b4: r0 = Container()
    //     0xa639b4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa639b8: r0 = Padding()
    //     0xa639b8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa639bc: mov             x1, x0
    // 0xa639c0: r0 = Instance_EdgeInsets
    //     0xa639c0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xa639c4: ldr             x0, [x0, #0xa78]
    // 0xa639c8: stur            x1, [fp, #-0x48]
    // 0xa639cc: StoreField: r1->field_f = r0
    //     0xa639cc: stur            w0, [x1, #0xf]
    // 0xa639d0: ldur            x0, [fp, #-0x40]
    // 0xa639d4: StoreField: r1->field_b = r0
    //     0xa639d4: stur            w0, [x1, #0xb]
    // 0xa639d8: r0 = Visibility()
    //     0xa639d8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa639dc: mov             x1, x0
    // 0xa639e0: ldur            x0, [fp, #-0x48]
    // 0xa639e4: stur            x1, [fp, #-0x40]
    // 0xa639e8: StoreField: r1->field_b = r0
    //     0xa639e8: stur            w0, [x1, #0xb]
    // 0xa639ec: r6 = Instance_SizedBox
    //     0xa639ec: ldr             x6, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa639f0: StoreField: r1->field_f = r6
    //     0xa639f0: stur            w6, [x1, #0xf]
    // 0xa639f4: ldur            x0, [fp, #-0x30]
    // 0xa639f8: StoreField: r1->field_13 = r0
    //     0xa639f8: stur            w0, [x1, #0x13]
    // 0xa639fc: r0 = false
    //     0xa639fc: add             x0, NULL, #0x30  ; false
    // 0xa63a00: ArrayStore: r1[0] = r0  ; List_4
    //     0xa63a00: stur            w0, [x1, #0x17]
    // 0xa63a04: StoreField: r1->field_1b = r0
    //     0xa63a04: stur            w0, [x1, #0x1b]
    // 0xa63a08: StoreField: r1->field_1f = r0
    //     0xa63a08: stur            w0, [x1, #0x1f]
    // 0xa63a0c: StoreField: r1->field_23 = r0
    //     0xa63a0c: stur            w0, [x1, #0x23]
    // 0xa63a10: StoreField: r1->field_27 = r0
    //     0xa63a10: stur            w0, [x1, #0x27]
    // 0xa63a14: StoreField: r1->field_2b = r0
    //     0xa63a14: stur            w0, [x1, #0x2b]
    // 0xa63a18: r0 = InkWell()
    //     0xa63a18: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa63a1c: mov             x3, x0
    // 0xa63a20: ldur            x0, [fp, #-0x40]
    // 0xa63a24: stur            x3, [fp, #-0x30]
    // 0xa63a28: StoreField: r3->field_b = r0
    //     0xa63a28: stur            w0, [x3, #0xb]
    // 0xa63a2c: ldur            x2, [fp, #-0x20]
    // 0xa63a30: r1 = Function '<anonymous closure>':.
    //     0xa63a30: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a340] AnonymousClosure: (0xa66520), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xa62b0c)
    //     0xa63a34: ldr             x1, [x1, #0x340]
    // 0xa63a38: r0 = AllocateClosure()
    //     0xa63a38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa63a3c: mov             x1, x0
    // 0xa63a40: ldur            x0, [fp, #-0x30]
    // 0xa63a44: StoreField: r0->field_f = r1
    //     0xa63a44: stur            w1, [x0, #0xf]
    // 0xa63a48: r7 = true
    //     0xa63a48: add             x7, NULL, #0x20  ; true
    // 0xa63a4c: StoreField: r0->field_43 = r7
    //     0xa63a4c: stur            w7, [x0, #0x43]
    // 0xa63a50: r8 = Instance_BoxShape
    //     0xa63a50: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa63a54: ldr             x8, [x8, #0x80]
    // 0xa63a58: StoreField: r0->field_47 = r8
    //     0xa63a58: stur            w8, [x0, #0x47]
    // 0xa63a5c: StoreField: r0->field_6f = r7
    //     0xa63a5c: stur            w7, [x0, #0x6f]
    // 0xa63a60: r9 = false
    //     0xa63a60: add             x9, NULL, #0x30  ; false
    // 0xa63a64: StoreField: r0->field_73 = r9
    //     0xa63a64: stur            w9, [x0, #0x73]
    // 0xa63a68: StoreField: r0->field_83 = r7
    //     0xa63a68: stur            w7, [x0, #0x83]
    // 0xa63a6c: StoreField: r0->field_7b = r9
    //     0xa63a6c: stur            w9, [x0, #0x7b]
    // 0xa63a70: r1 = Null
    //     0xa63a70: mov             x1, NULL
    // 0xa63a74: r2 = 6
    //     0xa63a74: movz            x2, #0x6
    // 0xa63a78: r0 = AllocateArray()
    //     0xa63a78: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa63a7c: mov             x2, x0
    // 0xa63a80: ldur            x0, [fp, #-0x38]
    // 0xa63a84: stur            x2, [fp, #-0x40]
    // 0xa63a88: StoreField: r2->field_f = r0
    //     0xa63a88: stur            w0, [x2, #0xf]
    // 0xa63a8c: r16 = Instance_SizedBox
    //     0xa63a8c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xa63a90: ldr             x16, [x16, #0xc70]
    // 0xa63a94: StoreField: r2->field_13 = r16
    //     0xa63a94: stur            w16, [x2, #0x13]
    // 0xa63a98: ldur            x0, [fp, #-0x30]
    // 0xa63a9c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa63a9c: stur            w0, [x2, #0x17]
    // 0xa63aa0: r1 = <Widget>
    //     0xa63aa0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa63aa4: r0 = AllocateGrowableArray()
    //     0xa63aa4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa63aa8: mov             x1, x0
    // 0xa63aac: ldur            x0, [fp, #-0x40]
    // 0xa63ab0: stur            x1, [fp, #-0x30]
    // 0xa63ab4: StoreField: r1->field_f = r0
    //     0xa63ab4: stur            w0, [x1, #0xf]
    // 0xa63ab8: r10 = 6
    //     0xa63ab8: movz            x10, #0x6
    // 0xa63abc: StoreField: r1->field_b = r10
    //     0xa63abc: stur            w10, [x1, #0xb]
    // 0xa63ac0: r0 = Column()
    //     0xa63ac0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa63ac4: r13 = Instance_Axis
    //     0xa63ac4: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa63ac8: StoreField: r0->field_f = r13
    //     0xa63ac8: stur            w13, [x0, #0xf]
    // 0xa63acc: r14 = Instance_MainAxisAlignment
    //     0xa63acc: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa63ad0: ldr             x14, [x14, #0xa08]
    // 0xa63ad4: StoreField: r0->field_13 = r14
    //     0xa63ad4: stur            w14, [x0, #0x13]
    // 0xa63ad8: r19 = Instance_MainAxisSize
    //     0xa63ad8: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa63adc: ldr             x19, [x19, #0xa10]
    // 0xa63ae0: ArrayStore: r0[0] = r19  ; List_4
    //     0xa63ae0: stur            w19, [x0, #0x17]
    // 0xa63ae4: r20 = Instance_CrossAxisAlignment
    //     0xa63ae4: add             x20, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa63ae8: ldr             x20, [x20, #0xa18]
    // 0xa63aec: StoreField: r0->field_1b = r20
    //     0xa63aec: stur            w20, [x0, #0x1b]
    // 0xa63af0: r23 = Instance_VerticalDirection
    //     0xa63af0: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa63af4: ldr             x23, [x23, #0xa20]
    // 0xa63af8: StoreField: r0->field_23 = r23
    //     0xa63af8: stur            w23, [x0, #0x23]
    // 0xa63afc: r24 = Instance_Clip
    //     0xa63afc: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa63b00: ldr             x24, [x24, #0x38]
    // 0xa63b04: StoreField: r0->field_2b = r24
    //     0xa63b04: stur            w24, [x0, #0x2b]
    // 0xa63b08: StoreField: r0->field_2f = rZR
    //     0xa63b08: stur            xzr, [x0, #0x2f]
    // 0xa63b0c: ldur            x1, [fp, #-0x30]
    // 0xa63b10: StoreField: r0->field_b = r1
    //     0xa63b10: stur            w1, [x0, #0xb]
    // 0xa63b14: b               #0xa63f54
    // 0xa63b18: mov             x2, x3
    // 0xa63b1c: mov             x3, x0
    // 0xa63b20: mov             x0, x14
    // 0xa63b24: mov             x20, x13
    // 0xa63b28: r14 = Instance_MainAxisAlignment
    //     0xa63b28: add             x14, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa63b2c: ldr             x14, [x14, #0xa08]
    // 0xa63b30: r19 = Instance_MainAxisSize
    //     0xa63b30: add             x19, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa63b34: ldr             x19, [x19, #0xa10]
    // 0xa63b38: r23 = Instance_VerticalDirection
    //     0xa63b38: add             x23, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa63b3c: ldr             x23, [x23, #0xa20]
    // 0xa63b40: r13 = Instance_Axis
    //     0xa63b40: ldr             x13, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa63b44: r24 = Instance_Clip
    //     0xa63b44: add             x24, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa63b48: ldr             x24, [x24, #0x38]
    // 0xa63b4c: ldur            x25, [fp, #-8]
    // 0xa63b50: LoadField: r0 = r1->field_f
    //     0xa63b50: ldur            w0, [x1, #0xf]
    // 0xa63b54: DecompressPointer r0
    //     0xa63b54: add             x0, x0, HEAP, lsl #32
    // 0xa63b58: ldur            x1, [fp, #-0x10]
    // 0xa63b5c: stur            x0, [fp, #-0x30]
    // 0xa63b60: r0 = of()
    //     0xa63b60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa63b64: LoadField: r1 = r0->field_87
    //     0xa63b64: ldur            w1, [x0, #0x87]
    // 0xa63b68: DecompressPointer r1
    //     0xa63b68: add             x1, x1, HEAP, lsl #32
    // 0xa63b6c: LoadField: r0 = r1->field_27
    //     0xa63b6c: ldur            w0, [x1, #0x27]
    // 0xa63b70: DecompressPointer r0
    //     0xa63b70: add             x0, x0, HEAP, lsl #32
    // 0xa63b74: stur            x0, [fp, #-0x38]
    // 0xa63b78: r1 = Instance_Color
    //     0xa63b78: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa63b7c: d0 = 0.700000
    //     0xa63b7c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa63b80: ldr             d0, [x17, #0xf48]
    // 0xa63b84: r0 = withOpacity()
    //     0xa63b84: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa63b88: r16 = 21.000000
    //     0xa63b88: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa63b8c: ldr             x16, [x16, #0x9b0]
    // 0xa63b90: stp             x0, x16, [SP]
    // 0xa63b94: ldur            x1, [fp, #-0x38]
    // 0xa63b98: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa63b98: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa63b9c: ldr             x4, [x4, #0xaa0]
    // 0xa63ba0: r0 = copyWith()
    //     0xa63ba0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa63ba4: stur            x0, [fp, #-0x38]
    // 0xa63ba8: r0 = Text()
    //     0xa63ba8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa63bac: mov             x2, x0
    // 0xa63bb0: ldur            x0, [fp, #-0x30]
    // 0xa63bb4: stur            x2, [fp, #-0x40]
    // 0xa63bb8: StoreField: r2->field_b = r0
    //     0xa63bb8: stur            w0, [x2, #0xb]
    // 0xa63bbc: ldur            x0, [fp, #-0x38]
    // 0xa63bc0: StoreField: r2->field_13 = r0
    //     0xa63bc0: stur            w0, [x2, #0x13]
    // 0xa63bc4: ldur            x0, [fp, #-8]
    // 0xa63bc8: LoadField: r1 = r0->field_b
    //     0xa63bc8: ldur            w1, [x0, #0xb]
    // 0xa63bcc: DecompressPointer r1
    //     0xa63bcc: add             x1, x1, HEAP, lsl #32
    // 0xa63bd0: cmp             w1, NULL
    // 0xa63bd4: b.eq            #0xa64378
    // 0xa63bd8: LoadField: r3 = r1->field_2f
    //     0xa63bd8: ldur            w3, [x1, #0x2f]
    // 0xa63bdc: DecompressPointer r3
    //     0xa63bdc: add             x3, x3, HEAP, lsl #32
    // 0xa63be0: cmp             w3, NULL
    // 0xa63be4: b.ne            #0xa63bf0
    // 0xa63be8: r1 = Null
    //     0xa63be8: mov             x1, NULL
    // 0xa63bec: b               #0xa63c1c
    // 0xa63bf0: LoadField: r1 = r3->field_7
    //     0xa63bf0: ldur            w1, [x3, #7]
    // 0xa63bf4: DecompressPointer r1
    //     0xa63bf4: add             x1, x1, HEAP, lsl #32
    // 0xa63bf8: cmp             w1, NULL
    // 0xa63bfc: b.ne            #0xa63c08
    // 0xa63c00: r1 = Null
    //     0xa63c00: mov             x1, NULL
    // 0xa63c04: b               #0xa63c1c
    // 0xa63c08: LoadField: r3 = r1->field_7
    //     0xa63c08: ldur            w3, [x1, #7]
    // 0xa63c0c: cbnz            w3, #0xa63c18
    // 0xa63c10: r1 = false
    //     0xa63c10: add             x1, NULL, #0x30  ; false
    // 0xa63c14: b               #0xa63c1c
    // 0xa63c18: r1 = true
    //     0xa63c18: add             x1, NULL, #0x20  ; true
    // 0xa63c1c: cmp             w1, NULL
    // 0xa63c20: b.ne            #0xa63c2c
    // 0xa63c24: r3 = false
    //     0xa63c24: add             x3, NULL, #0x30  ; false
    // 0xa63c28: b               #0xa63c30
    // 0xa63c2c: mov             x3, x1
    // 0xa63c30: ldur            x1, [fp, #-0x10]
    // 0xa63c34: stur            x3, [fp, #-0x30]
    // 0xa63c38: r0 = of()
    //     0xa63c38: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa63c3c: LoadField: r2 = r0->field_5b
    //     0xa63c3c: ldur            w2, [x0, #0x5b]
    // 0xa63c40: DecompressPointer r2
    //     0xa63c40: add             x2, x2, HEAP, lsl #32
    // 0xa63c44: r1 = Null
    //     0xa63c44: mov             x1, NULL
    // 0xa63c48: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa63c48: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa63c4c: r0 = Border.all()
    //     0xa63c4c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa63c50: stur            x0, [fp, #-0x38]
    // 0xa63c54: r0 = BoxDecoration()
    //     0xa63c54: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa63c58: mov             x2, x0
    // 0xa63c5c: ldur            x0, [fp, #-0x38]
    // 0xa63c60: stur            x2, [fp, #-0x48]
    // 0xa63c64: StoreField: r2->field_f = r0
    //     0xa63c64: stur            w0, [x2, #0xf]
    // 0xa63c68: r0 = Instance_BorderRadius
    //     0xa63c68: add             x0, PP, #0x40, lsl #12  ; [pp+0x40be8] Obj!BorderRadius@d5a2c1
    //     0xa63c6c: ldr             x0, [x0, #0xbe8]
    // 0xa63c70: StoreField: r2->field_13 = r0
    //     0xa63c70: stur            w0, [x2, #0x13]
    // 0xa63c74: r0 = Instance_BoxShape
    //     0xa63c74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa63c78: ldr             x0, [x0, #0x80]
    // 0xa63c7c: StoreField: r2->field_23 = r0
    //     0xa63c7c: stur            w0, [x2, #0x23]
    // 0xa63c80: ldur            x3, [fp, #-8]
    // 0xa63c84: LoadField: r1 = r3->field_b
    //     0xa63c84: ldur            w1, [x3, #0xb]
    // 0xa63c88: DecompressPointer r1
    //     0xa63c88: add             x1, x1, HEAP, lsl #32
    // 0xa63c8c: cmp             w1, NULL
    // 0xa63c90: b.eq            #0xa6437c
    // 0xa63c94: LoadField: r4 = r1->field_2f
    //     0xa63c94: ldur            w4, [x1, #0x2f]
    // 0xa63c98: DecompressPointer r4
    //     0xa63c98: add             x4, x4, HEAP, lsl #32
    // 0xa63c9c: cmp             w4, NULL
    // 0xa63ca0: b.ne            #0xa63cac
    // 0xa63ca4: r1 = Null
    //     0xa63ca4: mov             x1, NULL
    // 0xa63ca8: b               #0xa63cb4
    // 0xa63cac: LoadField: r1 = r4->field_7
    //     0xa63cac: ldur            w1, [x4, #7]
    // 0xa63cb0: DecompressPointer r1
    //     0xa63cb0: add             x1, x1, HEAP, lsl #32
    // 0xa63cb4: cmp             w1, NULL
    // 0xa63cb8: b.ne            #0xa63cc4
    // 0xa63cbc: r6 = ""
    //     0xa63cbc: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa63cc0: b               #0xa63cc8
    // 0xa63cc4: mov             x6, x1
    // 0xa63cc8: ldur            x4, [fp, #-0x40]
    // 0xa63ccc: ldur            x5, [fp, #-0x30]
    // 0xa63cd0: ldur            x1, [fp, #-0x10]
    // 0xa63cd4: stur            x6, [fp, #-0x38]
    // 0xa63cd8: r0 = of()
    //     0xa63cd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa63cdc: LoadField: r1 = r0->field_87
    //     0xa63cdc: ldur            w1, [x0, #0x87]
    // 0xa63ce0: DecompressPointer r1
    //     0xa63ce0: add             x1, x1, HEAP, lsl #32
    // 0xa63ce4: LoadField: r0 = r1->field_7
    //     0xa63ce4: ldur            w0, [x1, #7]
    // 0xa63ce8: DecompressPointer r0
    //     0xa63ce8: add             x0, x0, HEAP, lsl #32
    // 0xa63cec: ldur            x1, [fp, #-0x10]
    // 0xa63cf0: stur            x0, [fp, #-0x58]
    // 0xa63cf4: r0 = of()
    //     0xa63cf4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa63cf8: LoadField: r1 = r0->field_5b
    //     0xa63cf8: ldur            w1, [x0, #0x5b]
    // 0xa63cfc: DecompressPointer r1
    //     0xa63cfc: add             x1, x1, HEAP, lsl #32
    // 0xa63d00: r0 = LoadClassIdInstr(r1)
    //     0xa63d00: ldur            x0, [x1, #-1]
    //     0xa63d04: ubfx            x0, x0, #0xc, #0x14
    // 0xa63d08: d0 = 0.700000
    //     0xa63d08: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa63d0c: ldr             d0, [x17, #0xf48]
    // 0xa63d10: r0 = GDT[cid_x0 + -0xffa]()
    //     0xa63d10: sub             lr, x0, #0xffa
    //     0xa63d14: ldr             lr, [x21, lr, lsl #3]
    //     0xa63d18: blr             lr
    // 0xa63d1c: r16 = 14.000000
    //     0xa63d1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xa63d20: ldr             x16, [x16, #0x1d8]
    // 0xa63d24: stp             x0, x16, [SP]
    // 0xa63d28: ldur            x1, [fp, #-0x58]
    // 0xa63d2c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa63d2c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa63d30: ldr             x4, [x4, #0xaa0]
    // 0xa63d34: r0 = copyWith()
    //     0xa63d34: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa63d38: stur            x0, [fp, #-0x58]
    // 0xa63d3c: r0 = Text()
    //     0xa63d3c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa63d40: mov             x1, x0
    // 0xa63d44: ldur            x0, [fp, #-0x38]
    // 0xa63d48: stur            x1, [fp, #-0x60]
    // 0xa63d4c: StoreField: r1->field_b = r0
    //     0xa63d4c: stur            w0, [x1, #0xb]
    // 0xa63d50: ldur            x0, [fp, #-0x58]
    // 0xa63d54: StoreField: r1->field_13 = r0
    //     0xa63d54: stur            w0, [x1, #0x13]
    // 0xa63d58: r0 = Center()
    //     0xa63d58: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa63d5c: mov             x1, x0
    // 0xa63d60: r0 = Instance_Alignment
    //     0xa63d60: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa63d64: ldr             x0, [x0, #0xb10]
    // 0xa63d68: stur            x1, [fp, #-0x38]
    // 0xa63d6c: StoreField: r1->field_f = r0
    //     0xa63d6c: stur            w0, [x1, #0xf]
    // 0xa63d70: ldur            x0, [fp, #-0x60]
    // 0xa63d74: StoreField: r1->field_b = r0
    //     0xa63d74: stur            w0, [x1, #0xb]
    // 0xa63d78: r0 = Padding()
    //     0xa63d78: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa63d7c: mov             x1, x0
    // 0xa63d80: r0 = Instance_EdgeInsets
    //     0xa63d80: add             x0, PP, #0x59, lsl #12  ; [pp+0x59898] Obj!EdgeInsets@d585e1
    //     0xa63d84: ldr             x0, [x0, #0x898]
    // 0xa63d88: stur            x1, [fp, #-0x58]
    // 0xa63d8c: StoreField: r1->field_f = r0
    //     0xa63d8c: stur            w0, [x1, #0xf]
    // 0xa63d90: ldur            x0, [fp, #-0x38]
    // 0xa63d94: StoreField: r1->field_b = r0
    //     0xa63d94: stur            w0, [x1, #0xb]
    // 0xa63d98: r0 = Container()
    //     0xa63d98: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa63d9c: stur            x0, [fp, #-0x38]
    // 0xa63da0: r16 = 32.000000
    //     0xa63da0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xa63da4: ldr             x16, [x16, #0x848]
    // 0xa63da8: r30 = 90.000000
    //     0xa63da8: add             lr, PP, #0x48, lsl #12  ; [pp+0x48e68] 90
    //     0xa63dac: ldr             lr, [lr, #0xe68]
    // 0xa63db0: stp             lr, x16, [SP, #0x10]
    // 0xa63db4: ldur            x16, [fp, #-0x48]
    // 0xa63db8: ldur            lr, [fp, #-0x58]
    // 0xa63dbc: stp             lr, x16, [SP]
    // 0xa63dc0: mov             x1, x0
    // 0xa63dc4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xa63dc4: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xa63dc8: ldr             x4, [x4, #0x8c0]
    // 0xa63dcc: r0 = Container()
    //     0xa63dcc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa63dd0: r0 = Padding()
    //     0xa63dd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa63dd4: mov             x1, x0
    // 0xa63dd8: r0 = Instance_EdgeInsets
    //     0xa63dd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xa63ddc: ldr             x0, [x0, #0xd48]
    // 0xa63de0: stur            x1, [fp, #-0x48]
    // 0xa63de4: StoreField: r1->field_f = r0
    //     0xa63de4: stur            w0, [x1, #0xf]
    // 0xa63de8: ldur            x0, [fp, #-0x38]
    // 0xa63dec: StoreField: r1->field_b = r0
    //     0xa63dec: stur            w0, [x1, #0xb]
    // 0xa63df0: r0 = Visibility()
    //     0xa63df0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa63df4: mov             x1, x0
    // 0xa63df8: ldur            x0, [fp, #-0x48]
    // 0xa63dfc: stur            x1, [fp, #-0x38]
    // 0xa63e00: StoreField: r1->field_b = r0
    //     0xa63e00: stur            w0, [x1, #0xb]
    // 0xa63e04: r0 = Instance_SizedBox
    //     0xa63e04: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa63e08: StoreField: r1->field_f = r0
    //     0xa63e08: stur            w0, [x1, #0xf]
    // 0xa63e0c: ldur            x0, [fp, #-0x30]
    // 0xa63e10: StoreField: r1->field_13 = r0
    //     0xa63e10: stur            w0, [x1, #0x13]
    // 0xa63e14: r0 = false
    //     0xa63e14: add             x0, NULL, #0x30  ; false
    // 0xa63e18: ArrayStore: r1[0] = r0  ; List_4
    //     0xa63e18: stur            w0, [x1, #0x17]
    // 0xa63e1c: StoreField: r1->field_1b = r0
    //     0xa63e1c: stur            w0, [x1, #0x1b]
    // 0xa63e20: StoreField: r1->field_1f = r0
    //     0xa63e20: stur            w0, [x1, #0x1f]
    // 0xa63e24: StoreField: r1->field_23 = r0
    //     0xa63e24: stur            w0, [x1, #0x23]
    // 0xa63e28: StoreField: r1->field_27 = r0
    //     0xa63e28: stur            w0, [x1, #0x27]
    // 0xa63e2c: StoreField: r1->field_2b = r0
    //     0xa63e2c: stur            w0, [x1, #0x2b]
    // 0xa63e30: r0 = InkWell()
    //     0xa63e30: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa63e34: mov             x3, x0
    // 0xa63e38: ldur            x0, [fp, #-0x38]
    // 0xa63e3c: stur            x3, [fp, #-0x30]
    // 0xa63e40: StoreField: r3->field_b = r0
    //     0xa63e40: stur            w0, [x3, #0xb]
    // 0xa63e44: ldur            x2, [fp, #-0x20]
    // 0xa63e48: r1 = Function '<anonymous closure>':.
    //     0xa63e48: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a348] AnonymousClosure: (0xa66468), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xa62b0c)
    //     0xa63e4c: ldr             x1, [x1, #0x348]
    // 0xa63e50: r0 = AllocateClosure()
    //     0xa63e50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa63e54: mov             x1, x0
    // 0xa63e58: ldur            x0, [fp, #-0x30]
    // 0xa63e5c: StoreField: r0->field_f = r1
    //     0xa63e5c: stur            w1, [x0, #0xf]
    // 0xa63e60: r1 = true
    //     0xa63e60: add             x1, NULL, #0x20  ; true
    // 0xa63e64: StoreField: r0->field_43 = r1
    //     0xa63e64: stur            w1, [x0, #0x43]
    // 0xa63e68: r2 = Instance_BoxShape
    //     0xa63e68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa63e6c: ldr             x2, [x2, #0x80]
    // 0xa63e70: StoreField: r0->field_47 = r2
    //     0xa63e70: stur            w2, [x0, #0x47]
    // 0xa63e74: StoreField: r0->field_6f = r1
    //     0xa63e74: stur            w1, [x0, #0x6f]
    // 0xa63e78: r2 = false
    //     0xa63e78: add             x2, NULL, #0x30  ; false
    // 0xa63e7c: StoreField: r0->field_73 = r2
    //     0xa63e7c: stur            w2, [x0, #0x73]
    // 0xa63e80: StoreField: r0->field_83 = r1
    //     0xa63e80: stur            w1, [x0, #0x83]
    // 0xa63e84: StoreField: r0->field_7b = r2
    //     0xa63e84: stur            w2, [x0, #0x7b]
    // 0xa63e88: r1 = Null
    //     0xa63e88: mov             x1, NULL
    // 0xa63e8c: r2 = 6
    //     0xa63e8c: movz            x2, #0x6
    // 0xa63e90: r0 = AllocateArray()
    //     0xa63e90: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa63e94: mov             x2, x0
    // 0xa63e98: ldur            x0, [fp, #-0x40]
    // 0xa63e9c: stur            x2, [fp, #-0x38]
    // 0xa63ea0: StoreField: r2->field_f = r0
    //     0xa63ea0: stur            w0, [x2, #0xf]
    // 0xa63ea4: r16 = Instance_Spacer
    //     0xa63ea4: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xa63ea8: ldr             x16, [x16, #0xf0]
    // 0xa63eac: StoreField: r2->field_13 = r16
    //     0xa63eac: stur            w16, [x2, #0x13]
    // 0xa63eb0: ldur            x0, [fp, #-0x30]
    // 0xa63eb4: ArrayStore: r2[0] = r0  ; List_4
    //     0xa63eb4: stur            w0, [x2, #0x17]
    // 0xa63eb8: r1 = <Widget>
    //     0xa63eb8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa63ebc: r0 = AllocateGrowableArray()
    //     0xa63ebc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa63ec0: mov             x1, x0
    // 0xa63ec4: ldur            x0, [fp, #-0x38]
    // 0xa63ec8: stur            x1, [fp, #-0x30]
    // 0xa63ecc: StoreField: r1->field_f = r0
    //     0xa63ecc: stur            w0, [x1, #0xf]
    // 0xa63ed0: r0 = 6
    //     0xa63ed0: movz            x0, #0x6
    // 0xa63ed4: StoreField: r1->field_b = r0
    //     0xa63ed4: stur            w0, [x1, #0xb]
    // 0xa63ed8: r0 = Row()
    //     0xa63ed8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa63edc: mov             x1, x0
    // 0xa63ee0: r0 = Instance_Axis
    //     0xa63ee0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa63ee4: stur            x1, [fp, #-0x38]
    // 0xa63ee8: StoreField: r1->field_f = r0
    //     0xa63ee8: stur            w0, [x1, #0xf]
    // 0xa63eec: r0 = Instance_MainAxisAlignment
    //     0xa63eec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd10] Obj!MainAxisAlignment@d73461
    //     0xa63ef0: ldr             x0, [x0, #0xd10]
    // 0xa63ef4: StoreField: r1->field_13 = r0
    //     0xa63ef4: stur            w0, [x1, #0x13]
    // 0xa63ef8: r0 = Instance_MainAxisSize
    //     0xa63ef8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa63efc: ldr             x0, [x0, #0xa10]
    // 0xa63f00: ArrayStore: r1[0] = r0  ; List_4
    //     0xa63f00: stur            w0, [x1, #0x17]
    // 0xa63f04: r2 = Instance_CrossAxisAlignment
    //     0xa63f04: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa63f08: ldr             x2, [x2, #0xa18]
    // 0xa63f0c: StoreField: r1->field_1b = r2
    //     0xa63f0c: stur            w2, [x1, #0x1b]
    // 0xa63f10: r2 = Instance_VerticalDirection
    //     0xa63f10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa63f14: ldr             x2, [x2, #0xa20]
    // 0xa63f18: StoreField: r1->field_23 = r2
    //     0xa63f18: stur            w2, [x1, #0x23]
    // 0xa63f1c: r3 = Instance_Clip
    //     0xa63f1c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa63f20: ldr             x3, [x3, #0x38]
    // 0xa63f24: StoreField: r1->field_2b = r3
    //     0xa63f24: stur            w3, [x1, #0x2b]
    // 0xa63f28: StoreField: r1->field_2f = rZR
    //     0xa63f28: stur            xzr, [x1, #0x2f]
    // 0xa63f2c: ldur            x4, [fp, #-0x30]
    // 0xa63f30: StoreField: r1->field_b = r4
    //     0xa63f30: stur            w4, [x1, #0xb]
    // 0xa63f34: r0 = Padding()
    //     0xa63f34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa63f38: mov             x1, x0
    // 0xa63f3c: r0 = Instance_EdgeInsets
    //     0xa63f3c: add             x0, PP, #0x55, lsl #12  ; [pp+0x55078] Obj!EdgeInsets@d58791
    //     0xa63f40: ldr             x0, [x0, #0x78]
    // 0xa63f44: StoreField: r1->field_f = r0
    //     0xa63f44: stur            w0, [x1, #0xf]
    // 0xa63f48: ldur            x0, [fp, #-0x38]
    // 0xa63f4c: StoreField: r1->field_b = r0
    //     0xa63f4c: stur            w0, [x1, #0xb]
    // 0xa63f50: mov             x0, x1
    // 0xa63f54: mov             x2, x0
    // 0xa63f58: ldur            x0, [fp, #-0x28]
    // 0xa63f5c: stur            x2, [fp, #-0x30]
    // 0xa63f60: LoadField: r1 = r0->field_b
    //     0xa63f60: ldur            w1, [x0, #0xb]
    // 0xa63f64: LoadField: r3 = r0->field_f
    //     0xa63f64: ldur            w3, [x0, #0xf]
    // 0xa63f68: DecompressPointer r3
    //     0xa63f68: add             x3, x3, HEAP, lsl #32
    // 0xa63f6c: LoadField: r4 = r3->field_b
    //     0xa63f6c: ldur            w4, [x3, #0xb]
    // 0xa63f70: r3 = LoadInt32Instr(r1)
    //     0xa63f70: sbfx            x3, x1, #1, #0x1f
    // 0xa63f74: stur            x3, [fp, #-0x50]
    // 0xa63f78: r1 = LoadInt32Instr(r4)
    //     0xa63f78: sbfx            x1, x4, #1, #0x1f
    // 0xa63f7c: cmp             x3, x1
    // 0xa63f80: b.ne            #0xa63f8c
    // 0xa63f84: mov             x1, x0
    // 0xa63f88: r0 = _growToNextCapacity()
    //     0xa63f88: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa63f8c: ldur            x3, [fp, #-0x28]
    // 0xa63f90: ldur            x2, [fp, #-0x50]
    // 0xa63f94: add             x0, x2, #1
    // 0xa63f98: lsl             x1, x0, #1
    // 0xa63f9c: StoreField: r3->field_b = r1
    //     0xa63f9c: stur            w1, [x3, #0xb]
    // 0xa63fa0: LoadField: r1 = r3->field_f
    //     0xa63fa0: ldur            w1, [x3, #0xf]
    // 0xa63fa4: DecompressPointer r1
    //     0xa63fa4: add             x1, x1, HEAP, lsl #32
    // 0xa63fa8: ldur            x0, [fp, #-0x30]
    // 0xa63fac: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa63fac: add             x25, x1, x2, lsl #2
    //     0xa63fb0: add             x25, x25, #0xf
    //     0xa63fb4: str             w0, [x25]
    //     0xa63fb8: tbz             w0, #0, #0xa63fd4
    //     0xa63fbc: ldurb           w16, [x1, #-1]
    //     0xa63fc0: ldurb           w17, [x0, #-1]
    //     0xa63fc4: and             x16, x17, x16, lsr #2
    //     0xa63fc8: tst             x16, HEAP, lsr #32
    //     0xa63fcc: b.eq            #0xa63fd4
    //     0xa63fd0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa63fd4: ldur            x0, [fp, #-8]
    // 0xa63fd8: ArrayLoad: r2 = r0[0]  ; List_8
    //     0xa63fd8: ldur            x2, [x0, #0x17]
    // 0xa63fdc: mov             x1, x0
    // 0xa63fe0: r0 = _calculateCardHeight()
    //     0xa63fe0: bl              #0xa643cc  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_calculateCardHeight
    // 0xa63fe4: ldur            x0, [fp, #-8]
    // 0xa63fe8: stur            d0, [fp, #-0x70]
    // 0xa63fec: LoadField: r1 = r0->field_b
    //     0xa63fec: ldur            w1, [x0, #0xb]
    // 0xa63ff0: DecompressPointer r1
    //     0xa63ff0: add             x1, x1, HEAP, lsl #32
    // 0xa63ff4: cmp             w1, NULL
    // 0xa63ff8: b.eq            #0xa64380
    // 0xa63ffc: LoadField: r2 = r1->field_b
    //     0xa63ffc: ldur            w2, [x1, #0xb]
    // 0xa64000: DecompressPointer r2
    //     0xa64000: add             x2, x2, HEAP, lsl #32
    // 0xa64004: cmp             w2, NULL
    // 0xa64008: b.ne            #0xa64014
    // 0xa6400c: r4 = Null
    //     0xa6400c: mov             x4, NULL
    // 0xa64010: b               #0xa6401c
    // 0xa64014: LoadField: r1 = r2->field_b
    //     0xa64014: ldur            w1, [x2, #0xb]
    // 0xa64018: mov             x4, x1
    // 0xa6401c: ldur            x3, [fp, #-0x28]
    // 0xa64020: stur            x4, [fp, #-0x38]
    // 0xa64024: LoadField: r5 = r0->field_13
    //     0xa64024: ldur            w5, [x0, #0x13]
    // 0xa64028: DecompressPointer r5
    //     0xa64028: add             x5, x5, HEAP, lsl #32
    // 0xa6402c: r16 = Sentinel
    //     0xa6402c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa64030: cmp             w5, w16
    // 0xa64034: b.eq            #0xa64384
    // 0xa64038: ldur            x2, [fp, #-0x20]
    // 0xa6403c: stur            x5, [fp, #-0x30]
    // 0xa64040: r1 = Function '<anonymous closure>':.
    //     0xa64040: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a350] AnonymousClosure: (0xa66358), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xa62b0c)
    //     0xa64044: ldr             x1, [x1, #0x350]
    // 0xa64048: r0 = AllocateClosure()
    //     0xa64048: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa6404c: ldur            x2, [fp, #-0x20]
    // 0xa64050: r1 = Function '<anonymous closure>':.
    //     0xa64050: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a358] AnonymousClosure: (0xa646a4), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xa62b0c)
    //     0xa64054: ldr             x1, [x1, #0x358]
    // 0xa64058: stur            x0, [fp, #-0x20]
    // 0xa6405c: r0 = AllocateClosure()
    //     0xa6405c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa64060: stur            x0, [fp, #-0x40]
    // 0xa64064: r0 = PageView()
    //     0xa64064: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xa64068: stur            x0, [fp, #-0x48]
    // 0xa6406c: r16 = Instance_BouncingScrollPhysics
    //     0xa6406c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xa64070: ldr             x16, [x16, #0x890]
    // 0xa64074: ldur            lr, [fp, #-0x30]
    // 0xa64078: stp             lr, x16, [SP]
    // 0xa6407c: mov             x1, x0
    // 0xa64080: ldur            x2, [fp, #-0x40]
    // 0xa64084: ldur            x3, [fp, #-0x38]
    // 0xa64088: ldur            x5, [fp, #-0x20]
    // 0xa6408c: r4 = const [0, 0x6, 0x2, 0x4, controller, 0x5, physics, 0x4, null]
    //     0xa6408c: add             x4, PP, #0x51, lsl #12  ; [pp+0x51e40] List(9) [0, 0x6, 0x2, 0x4, "controller", 0x5, "physics", 0x4, Null]
    //     0xa64090: ldr             x4, [x4, #0xe40]
    // 0xa64094: r0 = PageView.builder()
    //     0xa64094: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xa64098: ldur            d0, [fp, #-0x70]
    // 0xa6409c: r0 = inline_Allocate_Double()
    //     0xa6409c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa640a0: add             x0, x0, #0x10
    //     0xa640a4: cmp             x1, x0
    //     0xa640a8: b.ls            #0xa64390
    //     0xa640ac: str             x0, [THR, #0x50]  ; THR::top
    //     0xa640b0: sub             x0, x0, #0xf
    //     0xa640b4: movz            x1, #0xe15c
    //     0xa640b8: movk            x1, #0x3, lsl #16
    //     0xa640bc: stur            x1, [x0, #-1]
    // 0xa640c0: StoreField: r0->field_7 = d0
    //     0xa640c0: stur            d0, [x0, #7]
    // 0xa640c4: stur            x0, [fp, #-0x20]
    // 0xa640c8: r0 = SizedBox()
    //     0xa640c8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa640cc: mov             x2, x0
    // 0xa640d0: ldur            x0, [fp, #-0x20]
    // 0xa640d4: stur            x2, [fp, #-0x30]
    // 0xa640d8: StoreField: r2->field_13 = r0
    //     0xa640d8: stur            w0, [x2, #0x13]
    // 0xa640dc: ldur            x0, [fp, #-0x48]
    // 0xa640e0: StoreField: r2->field_b = r0
    //     0xa640e0: stur            w0, [x2, #0xb]
    // 0xa640e4: ldur            x0, [fp, #-0x28]
    // 0xa640e8: LoadField: r1 = r0->field_b
    //     0xa640e8: ldur            w1, [x0, #0xb]
    // 0xa640ec: LoadField: r3 = r0->field_f
    //     0xa640ec: ldur            w3, [x0, #0xf]
    // 0xa640f0: DecompressPointer r3
    //     0xa640f0: add             x3, x3, HEAP, lsl #32
    // 0xa640f4: LoadField: r4 = r3->field_b
    //     0xa640f4: ldur            w4, [x3, #0xb]
    // 0xa640f8: r3 = LoadInt32Instr(r1)
    //     0xa640f8: sbfx            x3, x1, #1, #0x1f
    // 0xa640fc: stur            x3, [fp, #-0x50]
    // 0xa64100: r1 = LoadInt32Instr(r4)
    //     0xa64100: sbfx            x1, x4, #1, #0x1f
    // 0xa64104: cmp             x3, x1
    // 0xa64108: b.ne            #0xa64114
    // 0xa6410c: mov             x1, x0
    // 0xa64110: r0 = _growToNextCapacity()
    //     0xa64110: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa64114: ldur            x4, [fp, #-8]
    // 0xa64118: ldur            x2, [fp, #-0x28]
    // 0xa6411c: ldur            x3, [fp, #-0x50]
    // 0xa64120: add             x0, x3, #1
    // 0xa64124: lsl             x1, x0, #1
    // 0xa64128: StoreField: r2->field_b = r1
    //     0xa64128: stur            w1, [x2, #0xb]
    // 0xa6412c: LoadField: r1 = r2->field_f
    //     0xa6412c: ldur            w1, [x2, #0xf]
    // 0xa64130: DecompressPointer r1
    //     0xa64130: add             x1, x1, HEAP, lsl #32
    // 0xa64134: ldur            x0, [fp, #-0x30]
    // 0xa64138: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa64138: add             x25, x1, x3, lsl #2
    //     0xa6413c: add             x25, x25, #0xf
    //     0xa64140: str             w0, [x25]
    //     0xa64144: tbz             w0, #0, #0xa64160
    //     0xa64148: ldurb           w16, [x1, #-1]
    //     0xa6414c: ldurb           w17, [x0, #-1]
    //     0xa64150: and             x16, x17, x16, lsr #2
    //     0xa64154: tst             x16, HEAP, lsr #32
    //     0xa64158: b.eq            #0xa64160
    //     0xa6415c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64160: LoadField: r0 = r4->field_b
    //     0xa64160: ldur            w0, [x4, #0xb]
    // 0xa64164: DecompressPointer r0
    //     0xa64164: add             x0, x0, HEAP, lsl #32
    // 0xa64168: cmp             w0, NULL
    // 0xa6416c: b.eq            #0xa643a0
    // 0xa64170: LoadField: r1 = r0->field_b
    //     0xa64170: ldur            w1, [x0, #0xb]
    // 0xa64174: DecompressPointer r1
    //     0xa64174: add             x1, x1, HEAP, lsl #32
    // 0xa64178: cmp             w1, NULL
    // 0xa6417c: b.eq            #0xa643a4
    // 0xa64180: LoadField: r0 = r1->field_b
    //     0xa64180: ldur            w0, [x1, #0xb]
    // 0xa64184: r1 = LoadInt32Instr(r0)
    //     0xa64184: sbfx            x1, x0, #1, #0x1f
    // 0xa64188: cmp             x1, #1
    // 0xa6418c: b.le            #0xa64260
    // 0xa64190: r3 = LoadInt32Instr(r0)
    //     0xa64190: sbfx            x3, x0, #1, #0x1f
    // 0xa64194: stur            x3, [fp, #-0x68]
    // 0xa64198: ArrayLoad: r0 = r4[0]  ; List_8
    //     0xa64198: ldur            x0, [x4, #0x17]
    // 0xa6419c: ldur            x1, [fp, #-0x10]
    // 0xa641a0: stur            x0, [fp, #-0x50]
    // 0xa641a4: r0 = of()
    //     0xa641a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa641a8: LoadField: r1 = r0->field_5b
    //     0xa641a8: ldur            w1, [x0, #0x5b]
    // 0xa641ac: DecompressPointer r1
    //     0xa641ac: add             x1, x1, HEAP, lsl #32
    // 0xa641b0: stur            x1, [fp, #-8]
    // 0xa641b4: r0 = CarouselIndicator()
    //     0xa641b4: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xa641b8: mov             x2, x0
    // 0xa641bc: ldur            x0, [fp, #-0x68]
    // 0xa641c0: stur            x2, [fp, #-0x10]
    // 0xa641c4: StoreField: r2->field_b = r0
    //     0xa641c4: stur            x0, [x2, #0xb]
    // 0xa641c8: ldur            x0, [fp, #-0x50]
    // 0xa641cc: StoreField: r2->field_13 = r0
    //     0xa641cc: stur            x0, [x2, #0x13]
    // 0xa641d0: ldur            x0, [fp, #-8]
    // 0xa641d4: StoreField: r2->field_1b = r0
    //     0xa641d4: stur            w0, [x2, #0x1b]
    // 0xa641d8: r0 = Instance_Color
    //     0xa641d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xa641dc: ldr             x0, [x0, #0x90]
    // 0xa641e0: StoreField: r2->field_1f = r0
    //     0xa641e0: stur            w0, [x2, #0x1f]
    // 0xa641e4: ldur            x0, [fp, #-0x28]
    // 0xa641e8: LoadField: r1 = r0->field_b
    //     0xa641e8: ldur            w1, [x0, #0xb]
    // 0xa641ec: LoadField: r3 = r0->field_f
    //     0xa641ec: ldur            w3, [x0, #0xf]
    // 0xa641f0: DecompressPointer r3
    //     0xa641f0: add             x3, x3, HEAP, lsl #32
    // 0xa641f4: LoadField: r4 = r3->field_b
    //     0xa641f4: ldur            w4, [x3, #0xb]
    // 0xa641f8: r3 = LoadInt32Instr(r1)
    //     0xa641f8: sbfx            x3, x1, #1, #0x1f
    // 0xa641fc: stur            x3, [fp, #-0x50]
    // 0xa64200: r1 = LoadInt32Instr(r4)
    //     0xa64200: sbfx            x1, x4, #1, #0x1f
    // 0xa64204: cmp             x3, x1
    // 0xa64208: b.ne            #0xa64214
    // 0xa6420c: mov             x1, x0
    // 0xa64210: r0 = _growToNextCapacity()
    //     0xa64210: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa64214: ldur            x2, [fp, #-0x28]
    // 0xa64218: ldur            x3, [fp, #-0x50]
    // 0xa6421c: add             x0, x3, #1
    // 0xa64220: lsl             x1, x0, #1
    // 0xa64224: StoreField: r2->field_b = r1
    //     0xa64224: stur            w1, [x2, #0xb]
    // 0xa64228: LoadField: r1 = r2->field_f
    //     0xa64228: ldur            w1, [x2, #0xf]
    // 0xa6422c: DecompressPointer r1
    //     0xa6422c: add             x1, x1, HEAP, lsl #32
    // 0xa64230: ldur            x0, [fp, #-0x10]
    // 0xa64234: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa64234: add             x25, x1, x3, lsl #2
    //     0xa64238: add             x25, x25, #0xf
    //     0xa6423c: str             w0, [x25]
    //     0xa64240: tbz             w0, #0, #0xa6425c
    //     0xa64244: ldurb           w16, [x1, #-1]
    //     0xa64248: ldurb           w17, [x0, #-1]
    //     0xa6424c: and             x16, x17, x16, lsr #2
    //     0xa64250: tst             x16, HEAP, lsr #32
    //     0xa64254: b.eq            #0xa6425c
    //     0xa64258: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa6425c: b               #0xa642ec
    // 0xa64260: r0 = Container()
    //     0xa64260: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa64264: mov             x1, x0
    // 0xa64268: stur            x0, [fp, #-8]
    // 0xa6426c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa6426c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa64270: r0 = Container()
    //     0xa64270: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa64274: ldur            x0, [fp, #-0x28]
    // 0xa64278: LoadField: r1 = r0->field_b
    //     0xa64278: ldur            w1, [x0, #0xb]
    // 0xa6427c: LoadField: r2 = r0->field_f
    //     0xa6427c: ldur            w2, [x0, #0xf]
    // 0xa64280: DecompressPointer r2
    //     0xa64280: add             x2, x2, HEAP, lsl #32
    // 0xa64284: LoadField: r3 = r2->field_b
    //     0xa64284: ldur            w3, [x2, #0xb]
    // 0xa64288: r2 = LoadInt32Instr(r1)
    //     0xa64288: sbfx            x2, x1, #1, #0x1f
    // 0xa6428c: stur            x2, [fp, #-0x50]
    // 0xa64290: r1 = LoadInt32Instr(r3)
    //     0xa64290: sbfx            x1, x3, #1, #0x1f
    // 0xa64294: cmp             x2, x1
    // 0xa64298: b.ne            #0xa642a4
    // 0xa6429c: mov             x1, x0
    // 0xa642a0: r0 = _growToNextCapacity()
    //     0xa642a0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa642a4: ldur            x2, [fp, #-0x28]
    // 0xa642a8: ldur            x3, [fp, #-0x50]
    // 0xa642ac: add             x0, x3, #1
    // 0xa642b0: lsl             x1, x0, #1
    // 0xa642b4: StoreField: r2->field_b = r1
    //     0xa642b4: stur            w1, [x2, #0xb]
    // 0xa642b8: LoadField: r1 = r2->field_f
    //     0xa642b8: ldur            w1, [x2, #0xf]
    // 0xa642bc: DecompressPointer r1
    //     0xa642bc: add             x1, x1, HEAP, lsl #32
    // 0xa642c0: ldur            x0, [fp, #-8]
    // 0xa642c4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa642c4: add             x25, x1, x3, lsl #2
    //     0xa642c8: add             x25, x25, #0xf
    //     0xa642cc: str             w0, [x25]
    //     0xa642d0: tbz             w0, #0, #0xa642ec
    //     0xa642d4: ldurb           w16, [x1, #-1]
    //     0xa642d8: ldurb           w17, [x0, #-1]
    //     0xa642dc: and             x16, x17, x16, lsr #2
    //     0xa642e0: tst             x16, HEAP, lsr #32
    //     0xa642e4: b.eq            #0xa642ec
    //     0xa642e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa642ec: ldur            x0, [fp, #-0x18]
    // 0xa642f0: r0 = Column()
    //     0xa642f0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa642f4: r1 = Instance_Axis
    //     0xa642f4: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa642f8: StoreField: r0->field_f = r1
    //     0xa642f8: stur            w1, [x0, #0xf]
    // 0xa642fc: r1 = Instance_MainAxisAlignment
    //     0xa642fc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa64300: ldr             x1, [x1, #0xa08]
    // 0xa64304: StoreField: r0->field_13 = r1
    //     0xa64304: stur            w1, [x0, #0x13]
    // 0xa64308: r1 = Instance_MainAxisSize
    //     0xa64308: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa6430c: ldr             x1, [x1, #0xa10]
    // 0xa64310: ArrayStore: r0[0] = r1  ; List_4
    //     0xa64310: stur            w1, [x0, #0x17]
    // 0xa64314: ldur            x1, [fp, #-0x18]
    // 0xa64318: StoreField: r0->field_1b = r1
    //     0xa64318: stur            w1, [x0, #0x1b]
    // 0xa6431c: r1 = Instance_VerticalDirection
    //     0xa6431c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa64320: ldr             x1, [x1, #0xa20]
    // 0xa64324: StoreField: r0->field_23 = r1
    //     0xa64324: stur            w1, [x0, #0x23]
    // 0xa64328: r1 = Instance_Clip
    //     0xa64328: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa6432c: ldr             x1, [x1, #0x38]
    // 0xa64330: StoreField: r0->field_2b = r1
    //     0xa64330: stur            w1, [x0, #0x2b]
    // 0xa64334: StoreField: r0->field_2f = rZR
    //     0xa64334: stur            xzr, [x0, #0x2f]
    // 0xa64338: ldur            x1, [fp, #-0x28]
    // 0xa6433c: StoreField: r0->field_b = r1
    //     0xa6433c: stur            w1, [x0, #0xb]
    // 0xa64340: LeaveFrame
    //     0xa64340: mov             SP, fp
    //     0xa64344: ldp             fp, lr, [SP], #0x10
    // 0xa64348: ret
    //     0xa64348: ret             
    // 0xa6434c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6434c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa64350: b               #0xa62b34
    // 0xa64354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64354: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64358: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64358: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6435c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6435c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64360: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64360: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64364: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64368: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6436c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6436c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64370: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64370: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64374: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64374: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64378: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64378: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6437c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6437c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64380: r0 = NullCastErrorSharedWithFPURegs()
    //     0xa64380: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xa64384: r9 = _pageController
    //     0xa64384: add             x9, PP, #0x5a, lsl #12  ; [pp+0x5a360] Field <_TestimonialCarouselState@1338259219._pageController@1338259219>: late (offset: 0x14)
    //     0xa64388: ldr             x9, [x9, #0x360]
    // 0xa6438c: r0 = LateInitializationErrorSharedWithFPURegs()
    //     0xa6438c: bl              #0x16f7c00  ; LateInitializationErrorSharedWithFPURegsStub
    // 0xa64390: SaveReg d0
    //     0xa64390: str             q0, [SP, #-0x10]!
    // 0xa64394: r0 = AllocateDouble()
    //     0xa64394: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa64398: RestoreReg d0
    //     0xa64398: ldr             q0, [SP], #0x10
    // 0xa6439c: b               #0xa640c0
    // 0xa643a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa643a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa643a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa643a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _calculateCardHeight(/* No info */) {
    // ** addr: 0xa643cc, size: 0x2d8
    // 0xa643cc: EnterFrame
    //     0xa643cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa643d0: mov             fp, SP
    // 0xa643d4: AllocStack(0x20)
    //     0xa643d4: sub             SP, SP, #0x20
    // 0xa643d8: SetupParameters(_TestimonialCarouselState this /* r1 => r3 */)
    //     0xa643d8: mov             x3, x1
    // 0xa643dc: CheckStackOverflow
    //     0xa643dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa643e0: cmp             SP, x16
    //     0xa643e4: b.ls            #0xa64660
    // 0xa643e8: LoadField: r0 = r3->field_b
    //     0xa643e8: ldur            w0, [x3, #0xb]
    // 0xa643ec: DecompressPointer r0
    //     0xa643ec: add             x0, x0, HEAP, lsl #32
    // 0xa643f0: cmp             w0, NULL
    // 0xa643f4: b.eq            #0xa64668
    // 0xa643f8: LoadField: r4 = r0->field_b
    //     0xa643f8: ldur            w4, [x0, #0xb]
    // 0xa643fc: DecompressPointer r4
    //     0xa643fc: add             x4, x4, HEAP, lsl #32
    // 0xa64400: cmp             w4, NULL
    // 0xa64404: b.ne            #0xa64410
    // 0xa64408: r0 = Null
    //     0xa64408: mov             x0, NULL
    // 0xa6440c: b               #0xa64414
    // 0xa64410: LoadField: r0 = r4->field_b
    //     0xa64410: ldur            w0, [x4, #0xb]
    // 0xa64414: cmp             w0, NULL
    // 0xa64418: b.ne            #0xa64424
    // 0xa6441c: r0 = 0
    //     0xa6441c: movz            x0, #0
    // 0xa64420: b               #0xa6442c
    // 0xa64424: r1 = LoadInt32Instr(r0)
    //     0xa64424: sbfx            x1, x0, #1, #0x1f
    // 0xa64428: mov             x0, x1
    // 0xa6442c: cmp             x2, x0
    // 0xa64430: b.lt            #0xa64444
    // 0xa64434: d0 = 400.000000
    //     0xa64434: ldr             d0, [PP, #0x5a28]  ; [pp+0x5a28] IMM: double(400) from 0x4079000000000000
    // 0xa64438: LeaveFrame
    //     0xa64438: mov             SP, fp
    //     0xa6443c: ldp             fp, lr, [SP], #0x10
    // 0xa64440: ret
    //     0xa64440: ret             
    // 0xa64444: cmp             w4, NULL
    // 0xa64448: b.eq            #0xa6466c
    // 0xa6444c: LoadField: r0 = r4->field_b
    //     0xa6444c: ldur            w0, [x4, #0xb]
    // 0xa64450: r1 = LoadInt32Instr(r0)
    //     0xa64450: sbfx            x1, x0, #1, #0x1f
    // 0xa64454: mov             x0, x1
    // 0xa64458: mov             x1, x2
    // 0xa6445c: cmp             x1, x0
    // 0xa64460: b.hs            #0xa64670
    // 0xa64464: LoadField: r5 = r4->field_f
    //     0xa64464: ldur            w5, [x4, #0xf]
    // 0xa64468: DecompressPointer r5
    //     0xa64468: add             x5, x5, HEAP, lsl #32
    // 0xa6446c: r0 = BoxInt64Instr(r2)
    //     0xa6446c: sbfiz           x0, x2, #1, #0x1f
    //     0xa64470: cmp             x2, x0, asr #1
    //     0xa64474: b.eq            #0xa64480
    //     0xa64478: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa6447c: stur            x2, [x0, #7]
    // 0xa64480: ArrayLoad: r4 = r5[r2]  ; Unknown_4
    //     0xa64480: add             x16, x5, x2, lsl #2
    //     0xa64484: ldur            w4, [x16, #0xf]
    // 0xa64488: DecompressPointer r4
    //     0xa64488: add             x4, x4, HEAP, lsl #32
    // 0xa6448c: stur            x4, [fp, #-0x18]
    // 0xa64490: LoadField: r1 = r4->field_ab
    //     0xa64490: ldur            w1, [x4, #0xab]
    // 0xa64494: DecompressPointer r1
    //     0xa64494: add             x1, x1, HEAP, lsl #32
    // 0xa64498: cmp             w1, NULL
    // 0xa6449c: b.ne            #0xa644a8
    // 0xa644a0: r1 = Null
    //     0xa644a0: mov             x1, NULL
    // 0xa644a4: b               #0xa644bc
    // 0xa644a8: LoadField: r2 = r1->field_b
    //     0xa644a8: ldur            w2, [x1, #0xb]
    // 0xa644ac: cbnz            w2, #0xa644b8
    // 0xa644b0: r1 = false
    //     0xa644b0: add             x1, NULL, #0x30  ; false
    // 0xa644b4: b               #0xa644bc
    // 0xa644b8: r1 = true
    //     0xa644b8: add             x1, NULL, #0x20  ; true
    // 0xa644bc: cmp             w1, NULL
    // 0xa644c0: b.ne            #0xa644cc
    // 0xa644c4: r5 = false
    //     0xa644c4: add             x5, NULL, #0x30  ; false
    // 0xa644c8: b               #0xa644d0
    // 0xa644cc: mov             x5, x1
    // 0xa644d0: stur            x5, [fp, #-0x10]
    // 0xa644d4: LoadField: r6 = r3->field_1f
    //     0xa644d4: ldur            w6, [x3, #0x1f]
    // 0xa644d8: DecompressPointer r6
    //     0xa644d8: add             x6, x6, HEAP, lsl #32
    // 0xa644dc: mov             x1, x6
    // 0xa644e0: mov             x2, x0
    // 0xa644e4: stur            x6, [fp, #-8]
    // 0xa644e8: r0 = _getValueOrData()
    //     0xa644e8: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa644ec: mov             x1, x0
    // 0xa644f0: ldur            x0, [fp, #-8]
    // 0xa644f4: LoadField: r2 = r0->field_f
    //     0xa644f4: ldur            w2, [x0, #0xf]
    // 0xa644f8: DecompressPointer r2
    //     0xa644f8: add             x2, x2, HEAP, lsl #32
    // 0xa644fc: cmp             w2, w1
    // 0xa64500: b.ne            #0xa64508
    // 0xa64504: r1 = Null
    //     0xa64504: mov             x1, NULL
    // 0xa64508: cmp             w1, NULL
    // 0xa6450c: b.ne            #0xa64518
    // 0xa64510: r0 = Null
    //     0xa64510: mov             x0, NULL
    // 0xa64514: b               #0xa6451c
    // 0xa64518: r0 = value()
    //     0xa64518: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa6451c: cmp             w0, NULL
    // 0xa64520: b.ne            #0xa6452c
    // 0xa64524: r2 = false
    //     0xa64524: add             x2, NULL, #0x30  ; false
    // 0xa64528: b               #0xa64530
    // 0xa6452c: mov             x2, x0
    // 0xa64530: ldur            x0, [fp, #-0x18]
    // 0xa64534: stur            x2, [fp, #-8]
    // 0xa64538: LoadField: r1 = r0->field_9f
    //     0xa64538: ldur            w1, [x0, #0x9f]
    // 0xa6453c: DecompressPointer r1
    //     0xa6453c: add             x1, x1, HEAP, lsl #32
    // 0xa64540: cmp             w1, NULL
    // 0xa64544: b.ne            #0xa64550
    // 0xa64548: r0 = Null
    //     0xa64548: mov             x0, NULL
    // 0xa6454c: b               #0xa6455c
    // 0xa64550: r0 = trim()
    //     0xa64550: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xa64554: LoadField: r1 = r0->field_7
    //     0xa64554: ldur            w1, [x0, #7]
    // 0xa64558: mov             x0, x1
    // 0xa6455c: cmp             w0, NULL
    // 0xa64560: b.ne            #0xa6456c
    // 0xa64564: r1 = 0
    //     0xa64564: movz            x1, #0
    // 0xa64568: b               #0xa64570
    // 0xa6456c: r1 = LoadInt32Instr(r0)
    //     0xa6456c: sbfx            x1, x0, #1, #0x1f
    // 0xa64570: ldur            x0, [fp, #-0x10]
    // 0xa64574: tbnz            w0, #4, #0xa64584
    // 0xa64578: d0 = 436.000000
    //     0xa64578: add             x17, PP, #0x52, lsl #12  ; [pp+0x52708] IMM: double(436) from 0x407b400000000000
    //     0xa6457c: ldr             d0, [x17, #0x708]
    // 0xa64580: b               #0xa64588
    // 0xa64584: d0 = 100.000000
    //     0xa64584: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xa64588: ldur            x0, [fp, #-8]
    // 0xa6458c: tbnz            w0, #4, #0xa645b0
    // 0xa64590: d2 = 40.000000
    //     0xa64590: ldr             d2, [PP, #0x5a38]  ; [pp+0x5a38] IMM: double(40) from 0x4044000000000000
    // 0xa64594: d1 = 20.000000
    //     0xa64594: fmov            d1, #20.00000000
    // 0xa64598: scvtf           d3, x1
    // 0xa6459c: fdiv            d4, d3, d2
    // 0xa645a0: fmul            d2, d4, d1
    // 0xa645a4: fadd            d1, d0, d2
    // 0xa645a8: mov             v0.16b, v1.16b
    // 0xa645ac: b               #0xa645bc
    // 0xa645b0: d1 = 60.000000
    //     0xa645b0: ldr             d1, [PP, #0x64a0]  ; [pp+0x64a0] IMM: double(60) from 0x404e000000000000
    // 0xa645b4: fadd            d2, d0, d1
    // 0xa645b8: mov             v0.16b, v2.16b
    // 0xa645bc: stur            d0, [fp, #-0x20]
    // 0xa645c0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xa645c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa645c4: ldr             x0, [x0, #0x1c80]
    //     0xa645c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa645cc: cmp             w0, w16
    //     0xa645d0: b.ne            #0xa645dc
    //     0xa645d4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xa645d8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xa645dc: r0 = GetNavigation.size()
    //     0xa645dc: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xa645e0: LoadField: d0 = r0->field_f
    //     0xa645e0: ldur            d0, [x0, #0xf]
    // 0xa645e4: d1 = 0.850000
    //     0xa645e4: add             x17, PP, #0x2f, lsl #12  ; [pp+0x2f878] IMM: double(0.85) from 0x3feb333333333333
    //     0xa645e8: ldr             d1, [x17, #0x878]
    // 0xa645ec: fmul            d2, d0, d1
    // 0xa645f0: ldur            d0, [fp, #-0x20]
    // 0xa645f4: r1 = inline_Allocate_Double()
    //     0xa645f4: ldp             x1, x0, [THR, #0x50]  ; THR::top
    //     0xa645f8: add             x1, x1, #0x10
    //     0xa645fc: cmp             x0, x1
    //     0xa64600: b.ls            #0xa64674
    //     0xa64604: str             x1, [THR, #0x50]  ; THR::top
    //     0xa64608: sub             x1, x1, #0xf
    //     0xa6460c: movz            x0, #0xe15c
    //     0xa64610: movk            x0, #0x3, lsl #16
    //     0xa64614: stur            x0, [x1, #-1]
    // 0xa64618: StoreField: r1->field_7 = d0
    //     0xa64618: stur            d0, [x1, #7]
    // 0xa6461c: r3 = inline_Allocate_Double()
    //     0xa6461c: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0xa64620: add             x3, x3, #0x10
    //     0xa64624: cmp             x0, x3
    //     0xa64628: b.ls            #0xa64688
    //     0xa6462c: str             x3, [THR, #0x50]  ; THR::top
    //     0xa64630: sub             x3, x3, #0xf
    //     0xa64634: movz            x0, #0xe15c
    //     0xa64638: movk            x0, #0x3, lsl #16
    //     0xa6463c: stur            x0, [x3, #-1]
    // 0xa64640: StoreField: r3->field_7 = d2
    //     0xa64640: stur            d2, [x3, #7]
    // 0xa64644: r2 = 300.000000
    //     0xa64644: add             x2, PP, #0x55, lsl #12  ; [pp+0x55bd8] 300
    //     0xa64648: ldr             x2, [x2, #0xbd8]
    // 0xa6464c: r0 = clamp()
    //     0xa6464c: bl              #0x748b58  ; [dart:core] _Double::clamp
    // 0xa64650: LoadField: d0 = r0->field_7
    //     0xa64650: ldur            d0, [x0, #7]
    // 0xa64654: LeaveFrame
    //     0xa64654: mov             SP, fp
    //     0xa64658: ldp             fp, lr, [SP], #0x10
    // 0xa6465c: ret
    //     0xa6465c: ret             
    // 0xa64660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa64660: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa64664: b               #0xa643e8
    // 0xa64668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64668: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6466c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6466c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa64670: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa64670: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa64674: stp             q0, q2, [SP, #-0x20]!
    // 0xa64678: r0 = AllocateDouble()
    //     0xa64678: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa6467c: mov             x1, x0
    // 0xa64680: ldp             q0, q2, [SP], #0x20
    // 0xa64684: b               #0xa64618
    // 0xa64688: SaveReg d2
    //     0xa64688: str             q2, [SP, #-0x10]!
    // 0xa6468c: SaveReg r1
    //     0xa6468c: str             x1, [SP, #-8]!
    // 0xa64690: r0 = AllocateDouble()
    //     0xa64690: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa64694: mov             x3, x0
    // 0xa64698: RestoreReg r1
    //     0xa64698: ldr             x1, [SP], #8
    // 0xa6469c: RestoreReg d2
    //     0xa6469c: ldr             q2, [SP], #0x10
    // 0xa646a0: b               #0xa64640
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa646a4, size: 0x94
    // 0xa646a4: EnterFrame
    //     0xa646a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa646a8: mov             fp, SP
    // 0xa646ac: AllocStack(0x8)
    //     0xa646ac: sub             SP, SP, #8
    // 0xa646b0: SetupParameters()
    //     0xa646b0: ldr             x0, [fp, #0x20]
    //     0xa646b4: ldur            w1, [x0, #0x17]
    //     0xa646b8: add             x1, x1, HEAP, lsl #32
    // 0xa646bc: CheckStackOverflow
    //     0xa646bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa646c0: cmp             SP, x16
    //     0xa646c4: b.ls            #0xa6472c
    // 0xa646c8: LoadField: r0 = r1->field_f
    //     0xa646c8: ldur            w0, [x1, #0xf]
    // 0xa646cc: DecompressPointer r0
    //     0xa646cc: add             x0, x0, HEAP, lsl #32
    // 0xa646d0: stur            x0, [fp, #-8]
    // 0xa646d4: LoadField: r1 = r0->field_b
    //     0xa646d4: ldur            w1, [x0, #0xb]
    // 0xa646d8: DecompressPointer r1
    //     0xa646d8: add             x1, x1, HEAP, lsl #32
    // 0xa646dc: cmp             w1, NULL
    // 0xa646e0: b.eq            #0xa64734
    // 0xa646e4: LoadField: r2 = r1->field_b
    //     0xa646e4: ldur            w2, [x1, #0xb]
    // 0xa646e8: DecompressPointer r2
    //     0xa646e8: add             x2, x2, HEAP, lsl #32
    // 0xa646ec: cmp             w2, NULL
    // 0xa646f0: b.ne            #0xa64708
    // 0xa646f4: r1 = <Entity>
    //     0xa646f4: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xa646f8: ldr             x1, [x1, #0xb68]
    // 0xa646fc: r2 = 0
    //     0xa646fc: movz            x2, #0
    // 0xa64700: r0 = AllocateArray()
    //     0xa64700: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa64704: mov             x2, x0
    // 0xa64708: ldr             x0, [fp, #0x10]
    // 0xa6470c: r3 = LoadInt32Instr(r0)
    //     0xa6470c: sbfx            x3, x0, #1, #0x1f
    //     0xa64710: tbz             w0, #0, #0xa64718
    //     0xa64714: ldur            x3, [x0, #7]
    // 0xa64718: ldur            x1, [fp, #-8]
    // 0xa6471c: r0 = _testimonialWidget()
    //     0xa6471c: bl              #0xa64738  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget
    // 0xa64720: LeaveFrame
    //     0xa64720: mov             SP, fp
    //     0xa64724: ldp             fp, lr, [SP], #0x10
    // 0xa64728: ret
    //     0xa64728: ret             
    // 0xa6472c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6472c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa64730: b               #0xa646c8
    // 0xa64734: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa64734: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _testimonialWidget(/* No info */) {
    // ** addr: 0xa64738, size: 0x1070
    // 0xa64738: EnterFrame
    //     0xa64738: stp             fp, lr, [SP, #-0x10]!
    //     0xa6473c: mov             fp, SP
    // 0xa64740: AllocStack(0xa0)
    //     0xa64740: sub             SP, SP, #0xa0
    // 0xa64744: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa64744: stur            x1, [fp, #-8]
    //     0xa64748: stur            x2, [fp, #-0x10]
    //     0xa6474c: stur            x3, [fp, #-0x18]
    // 0xa64750: CheckStackOverflow
    //     0xa64750: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa64754: cmp             SP, x16
    //     0xa64758: b.ls            #0xa6576c
    // 0xa6475c: r1 = 2
    //     0xa6475c: movz            x1, #0x2
    // 0xa64760: r0 = AllocateContext()
    //     0xa64760: bl              #0x16f6108  ; AllocateContextStub
    // 0xa64764: mov             x3, x0
    // 0xa64768: ldur            x2, [fp, #-8]
    // 0xa6476c: stur            x3, [fp, #-0x28]
    // 0xa64770: StoreField: r3->field_f = r2
    //     0xa64770: stur            w2, [x3, #0xf]
    // 0xa64774: ldur            x4, [fp, #-0x18]
    // 0xa64778: r0 = BoxInt64Instr(r4)
    //     0xa64778: sbfiz           x0, x4, #1, #0x1f
    //     0xa6477c: cmp             x4, x0, asr #1
    //     0xa64780: b.eq            #0xa6478c
    //     0xa64784: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa64788: stur            x4, [x0, #7]
    // 0xa6478c: mov             x4, x0
    // 0xa64790: ldur            x1, [fp, #-0x10]
    // 0xa64794: stur            x4, [fp, #-0x20]
    // 0xa64798: r0 = LoadClassIdInstr(r1)
    //     0xa64798: ldur            x0, [x1, #-1]
    //     0xa6479c: ubfx            x0, x0, #0xc, #0x14
    // 0xa647a0: stp             x4, x1, [SP]
    // 0xa647a4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa647a4: sub             lr, x0, #0xb7
    //     0xa647a8: ldr             lr, [x21, lr, lsl #3]
    //     0xa647ac: blr             lr
    // 0xa647b0: stur            x0, [fp, #-0x40]
    // 0xa647b4: LoadField: r1 = r0->field_ab
    //     0xa647b4: ldur            w1, [x0, #0xab]
    // 0xa647b8: DecompressPointer r1
    //     0xa647b8: add             x1, x1, HEAP, lsl #32
    // 0xa647bc: cmp             w1, NULL
    // 0xa647c0: b.ne            #0xa647cc
    // 0xa647c4: r1 = Null
    //     0xa647c4: mov             x1, NULL
    // 0xa647c8: b               #0xa647e0
    // 0xa647cc: LoadField: r2 = r1->field_b
    //     0xa647cc: ldur            w2, [x1, #0xb]
    // 0xa647d0: cbnz            w2, #0xa647dc
    // 0xa647d4: r1 = false
    //     0xa647d4: add             x1, NULL, #0x30  ; false
    // 0xa647d8: b               #0xa647e0
    // 0xa647dc: r1 = true
    //     0xa647dc: add             x1, NULL, #0x20  ; true
    // 0xa647e0: cmp             w1, NULL
    // 0xa647e4: b.ne            #0xa647f0
    // 0xa647e8: r4 = false
    //     0xa647e8: add             x4, NULL, #0x30  ; false
    // 0xa647ec: b               #0xa647f4
    // 0xa647f0: mov             x4, x1
    // 0xa647f4: ldur            x3, [fp, #-8]
    // 0xa647f8: stur            x4, [fp, #-0x38]
    // 0xa647fc: LoadField: r5 = r3->field_1f
    //     0xa647fc: ldur            w5, [x3, #0x1f]
    // 0xa64800: DecompressPointer r5
    //     0xa64800: add             x5, x5, HEAP, lsl #32
    // 0xa64804: mov             x1, x5
    // 0xa64808: ldur            x2, [fp, #-0x20]
    // 0xa6480c: stur            x5, [fp, #-0x30]
    // 0xa64810: r0 = _getValueOrData()
    //     0xa64810: bl              #0x16ef8e0  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xa64814: mov             x1, x0
    // 0xa64818: ldur            x0, [fp, #-0x30]
    // 0xa6481c: LoadField: r2 = r0->field_f
    //     0xa6481c: ldur            w2, [x0, #0xf]
    // 0xa64820: DecompressPointer r2
    //     0xa64820: add             x2, x2, HEAP, lsl #32
    // 0xa64824: cmp             w2, w1
    // 0xa64828: b.ne            #0xa64834
    // 0xa6482c: r0 = Null
    //     0xa6482c: mov             x0, NULL
    // 0xa64830: b               #0xa64838
    // 0xa64834: mov             x0, x1
    // 0xa64838: cmp             w0, NULL
    // 0xa6483c: b.ne            #0xa6487c
    // 0xa64840: r1 = <bool>
    //     0xa64840: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0xa64844: r0 = RxBool()
    //     0xa64844: bl              #0x949cb0  ; AllocateRxBoolStub -> RxBool (size=0x1c)
    // 0xa64848: mov             x2, x0
    // 0xa6484c: r0 = Sentinel
    //     0xa6484c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa64850: stur            x2, [fp, #-0x30]
    // 0xa64854: StoreField: r2->field_13 = r0
    //     0xa64854: stur            w0, [x2, #0x13]
    // 0xa64858: r0 = true
    //     0xa64858: add             x0, NULL, #0x20  ; true
    // 0xa6485c: ArrayStore: r2[0] = r0  ; List_4
    //     0xa6485c: stur            w0, [x2, #0x17]
    // 0xa64860: mov             x1, x2
    // 0xa64864: r0 = RxNotifier()
    //     0xa64864: bl              #0x949a70  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxNotifier::RxNotifier
    // 0xa64868: ldur            x0, [fp, #-0x30]
    // 0xa6486c: r1 = false
    //     0xa6486c: add             x1, NULL, #0x30  ; false
    // 0xa64870: StoreField: r0->field_13 = r1
    //     0xa64870: stur            w1, [x0, #0x13]
    // 0xa64874: mov             x5, x0
    // 0xa64878: b               #0xa64884
    // 0xa6487c: r1 = false
    //     0xa6487c: add             x1, NULL, #0x30  ; false
    // 0xa64880: mov             x5, x0
    // 0xa64884: ldur            x2, [fp, #-8]
    // 0xa64888: ldur            x4, [fp, #-0x10]
    // 0xa6488c: ldur            x3, [fp, #-0x28]
    // 0xa64890: mov             x0, x5
    // 0xa64894: stur            x5, [fp, #-0x30]
    // 0xa64898: StoreField: r3->field_13 = r0
    //     0xa64898: stur            w0, [x3, #0x13]
    //     0xa6489c: ldurb           w16, [x3, #-1]
    //     0xa648a0: ldurb           w17, [x0, #-1]
    //     0xa648a4: and             x16, x17, x16, lsr #2
    //     0xa648a8: tst             x16, HEAP, lsr #32
    //     0xa648ac: b.eq            #0xa648b4
    //     0xa648b0: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xa648b4: r0 = Radius()
    //     0xa648b4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa648b8: d0 = 12.000000
    //     0xa648b8: fmov            d0, #12.00000000
    // 0xa648bc: stur            x0, [fp, #-0x48]
    // 0xa648c0: StoreField: r0->field_7 = d0
    //     0xa648c0: stur            d0, [x0, #7]
    // 0xa648c4: StoreField: r0->field_f = d0
    //     0xa648c4: stur            d0, [x0, #0xf]
    // 0xa648c8: r0 = BorderRadius()
    //     0xa648c8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa648cc: mov             x2, x0
    // 0xa648d0: ldur            x0, [fp, #-0x48]
    // 0xa648d4: stur            x2, [fp, #-0x50]
    // 0xa648d8: StoreField: r2->field_7 = r0
    //     0xa648d8: stur            w0, [x2, #7]
    // 0xa648dc: StoreField: r2->field_b = r0
    //     0xa648dc: stur            w0, [x2, #0xb]
    // 0xa648e0: StoreField: r2->field_f = r0
    //     0xa648e0: stur            w0, [x2, #0xf]
    // 0xa648e4: StoreField: r2->field_13 = r0
    //     0xa648e4: stur            w0, [x2, #0x13]
    // 0xa648e8: ldur            x0, [fp, #-8]
    // 0xa648ec: LoadField: r1 = r0->field_f
    //     0xa648ec: ldur            w1, [x0, #0xf]
    // 0xa648f0: DecompressPointer r1
    //     0xa648f0: add             x1, x1, HEAP, lsl #32
    // 0xa648f4: cmp             w1, NULL
    // 0xa648f8: b.eq            #0xa65774
    // 0xa648fc: r0 = of()
    //     0xa648fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa64900: LoadField: r2 = r0->field_5b
    //     0xa64900: ldur            w2, [x0, #0x5b]
    // 0xa64904: DecompressPointer r2
    //     0xa64904: add             x2, x2, HEAP, lsl #32
    // 0xa64908: r1 = Null
    //     0xa64908: mov             x1, NULL
    // 0xa6490c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa6490c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa64910: r0 = Border.all()
    //     0xa64910: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa64914: stur            x0, [fp, #-0x48]
    // 0xa64918: r0 = BoxDecoration()
    //     0xa64918: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa6491c: mov             x1, x0
    // 0xa64920: ldur            x0, [fp, #-0x48]
    // 0xa64924: stur            x1, [fp, #-0x58]
    // 0xa64928: StoreField: r1->field_f = r0
    //     0xa64928: stur            w0, [x1, #0xf]
    // 0xa6492c: ldur            x0, [fp, #-0x50]
    // 0xa64930: StoreField: r1->field_13 = r0
    //     0xa64930: stur            w0, [x1, #0x13]
    // 0xa64934: r0 = Instance_BoxShape
    //     0xa64934: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa64938: ldr             x0, [x0, #0x80]
    // 0xa6493c: StoreField: r1->field_23 = r0
    //     0xa6493c: stur            w0, [x1, #0x23]
    // 0xa64940: ldur            x2, [fp, #-0x10]
    // 0xa64944: r0 = LoadClassIdInstr(r2)
    //     0xa64944: ldur            x0, [x2, #-1]
    //     0xa64948: ubfx            x0, x0, #0xc, #0x14
    // 0xa6494c: ldur            x16, [fp, #-0x20]
    // 0xa64950: stp             x16, x2, [SP]
    // 0xa64954: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa64954: sub             lr, x0, #0xb7
    //     0xa64958: ldr             lr, [x21, lr, lsl #3]
    //     0xa6495c: blr             lr
    // 0xa64960: LoadField: r1 = r0->field_97
    //     0xa64960: ldur            w1, [x0, #0x97]
    // 0xa64964: DecompressPointer r1
    //     0xa64964: add             x1, x1, HEAP, lsl #32
    // 0xa64968: cmp             w1, NULL
    // 0xa6496c: b.ne            #0xa64974
    // 0xa64970: r1 = ""
    //     0xa64970: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa64974: ldur            x2, [fp, #-8]
    // 0xa64978: ldur            x0, [fp, #-0x10]
    // 0xa6497c: r0 = capitalizeFirstWord()
    //     0xa6497c: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xa64980: mov             x2, x0
    // 0xa64984: ldur            x0, [fp, #-8]
    // 0xa64988: stur            x2, [fp, #-0x48]
    // 0xa6498c: LoadField: r1 = r0->field_f
    //     0xa6498c: ldur            w1, [x0, #0xf]
    // 0xa64990: DecompressPointer r1
    //     0xa64990: add             x1, x1, HEAP, lsl #32
    // 0xa64994: cmp             w1, NULL
    // 0xa64998: b.eq            #0xa65778
    // 0xa6499c: r0 = of()
    //     0xa6499c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa649a0: LoadField: r1 = r0->field_87
    //     0xa649a0: ldur            w1, [x0, #0x87]
    // 0xa649a4: DecompressPointer r1
    //     0xa649a4: add             x1, x1, HEAP, lsl #32
    // 0xa649a8: LoadField: r0 = r1->field_7
    //     0xa649a8: ldur            w0, [x1, #7]
    // 0xa649ac: DecompressPointer r0
    //     0xa649ac: add             x0, x0, HEAP, lsl #32
    // 0xa649b0: r16 = 21.000000
    //     0xa649b0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xa649b4: ldr             x16, [x16, #0x9b0]
    // 0xa649b8: r30 = Instance_Color
    //     0xa649b8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa649bc: stp             lr, x16, [SP]
    // 0xa649c0: mov             x1, x0
    // 0xa649c4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa649c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa649c8: ldr             x4, [x4, #0xaa0]
    // 0xa649cc: r0 = copyWith()
    //     0xa649cc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa649d0: stur            x0, [fp, #-0x50]
    // 0xa649d4: r0 = Text()
    //     0xa649d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa649d8: mov             x3, x0
    // 0xa649dc: ldur            x0, [fp, #-0x48]
    // 0xa649e0: stur            x3, [fp, #-0x60]
    // 0xa649e4: StoreField: r3->field_b = r0
    //     0xa649e4: stur            w0, [x3, #0xb]
    // 0xa649e8: ldur            x0, [fp, #-0x50]
    // 0xa649ec: StoreField: r3->field_13 = r0
    //     0xa649ec: stur            w0, [x3, #0x13]
    // 0xa649f0: r1 = <Widget>
    //     0xa649f0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa649f4: r2 = 18
    //     0xa649f4: movz            x2, #0x12
    // 0xa649f8: r0 = AllocateArray()
    //     0xa649f8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa649fc: stur            x0, [fp, #-0x48]
    // 0xa64a00: r16 = Instance_Icon
    //     0xa64a00: add             x16, PP, #0x52, lsl #12  ; [pp+0x520d0] Obj!Icon@d66371
    //     0xa64a04: ldr             x16, [x16, #0xd0]
    // 0xa64a08: StoreField: r0->field_f = r16
    //     0xa64a08: stur            w16, [x0, #0xf]
    // 0xa64a0c: r16 = Instance_SizedBox
    //     0xa64a0c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51e98] Obj!SizedBox@d67e61
    //     0xa64a10: ldr             x16, [x16, #0xe98]
    // 0xa64a14: StoreField: r0->field_13 = r16
    //     0xa64a14: stur            w16, [x0, #0x13]
    // 0xa64a18: ldur            x2, [fp, #-8]
    // 0xa64a1c: LoadField: r1 = r2->field_f
    //     0xa64a1c: ldur            w1, [x2, #0xf]
    // 0xa64a20: DecompressPointer r1
    //     0xa64a20: add             x1, x1, HEAP, lsl #32
    // 0xa64a24: cmp             w1, NULL
    // 0xa64a28: b.eq            #0xa6577c
    // 0xa64a2c: r0 = of()
    //     0xa64a2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa64a30: LoadField: r1 = r0->field_87
    //     0xa64a30: ldur            w1, [x0, #0x87]
    // 0xa64a34: DecompressPointer r1
    //     0xa64a34: add             x1, x1, HEAP, lsl #32
    // 0xa64a38: LoadField: r0 = r1->field_2b
    //     0xa64a38: ldur            w0, [x1, #0x2b]
    // 0xa64a3c: DecompressPointer r0
    //     0xa64a3c: add             x0, x0, HEAP, lsl #32
    // 0xa64a40: stur            x0, [fp, #-0x50]
    // 0xa64a44: r1 = Instance_Color
    //     0xa64a44: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa64a48: d0 = 0.400000
    //     0xa64a48: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa64a4c: r0 = withOpacity()
    //     0xa64a4c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa64a50: r16 = 12.000000
    //     0xa64a50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa64a54: ldr             x16, [x16, #0x9e8]
    // 0xa64a58: stp             x0, x16, [SP]
    // 0xa64a5c: ldur            x1, [fp, #-0x50]
    // 0xa64a60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa64a60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa64a64: ldr             x4, [x4, #0xaa0]
    // 0xa64a68: r0 = copyWith()
    //     0xa64a68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa64a6c: stur            x0, [fp, #-0x50]
    // 0xa64a70: r0 = Text()
    //     0xa64a70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa64a74: mov             x1, x0
    // 0xa64a78: r0 = "Verified Buyer"
    //     0xa64a78: add             x0, PP, #0x52, lsl #12  ; [pp+0x520d8] "Verified Buyer"
    //     0xa64a7c: ldr             x0, [x0, #0xd8]
    // 0xa64a80: StoreField: r1->field_b = r0
    //     0xa64a80: stur            w0, [x1, #0xb]
    // 0xa64a84: ldur            x0, [fp, #-0x50]
    // 0xa64a88: StoreField: r1->field_13 = r0
    //     0xa64a88: stur            w0, [x1, #0x13]
    // 0xa64a8c: mov             x0, x1
    // 0xa64a90: ldur            x1, [fp, #-0x48]
    // 0xa64a94: ArrayStore: r1[2] = r0  ; List_4
    //     0xa64a94: add             x25, x1, #0x17
    //     0xa64a98: str             w0, [x25]
    //     0xa64a9c: tbz             w0, #0, #0xa64ab8
    //     0xa64aa0: ldurb           w16, [x1, #-1]
    //     0xa64aa4: ldurb           w17, [x0, #-1]
    //     0xa64aa8: and             x16, x17, x16, lsr #2
    //     0xa64aac: tst             x16, HEAP, lsr #32
    //     0xa64ab0: b.eq            #0xa64ab8
    //     0xa64ab4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64ab8: r0 = Container()
    //     0xa64ab8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa64abc: stur            x0, [fp, #-0x50]
    // 0xa64ac0: r16 = 5.000000
    //     0xa64ac0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xa64ac4: ldr             x16, [x16, #0xcf0]
    // 0xa64ac8: str             x16, [SP]
    // 0xa64acc: mov             x1, x0
    // 0xa64ad0: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xa64ad0: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xa64ad4: ldr             x4, [x4, #0xe0]
    // 0xa64ad8: r0 = Container()
    //     0xa64ad8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa64adc: ldur            x1, [fp, #-0x48]
    // 0xa64ae0: ldur            x0, [fp, #-0x50]
    // 0xa64ae4: ArrayStore: r1[3] = r0  ; List_4
    //     0xa64ae4: add             x25, x1, #0x1b
    //     0xa64ae8: str             w0, [x25]
    //     0xa64aec: tbz             w0, #0, #0xa64b08
    //     0xa64af0: ldurb           w16, [x1, #-1]
    //     0xa64af4: ldurb           w17, [x0, #-1]
    //     0xa64af8: and             x16, x17, x16, lsr #2
    //     0xa64afc: tst             x16, HEAP, lsr #32
    //     0xa64b00: b.eq            #0xa64b08
    //     0xa64b04: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64b08: ldur            x1, [fp, #-0x10]
    // 0xa64b0c: r0 = LoadClassIdInstr(r1)
    //     0xa64b0c: ldur            x0, [x1, #-1]
    //     0xa64b10: ubfx            x0, x0, #0xc, #0x14
    // 0xa64b14: ldur            x16, [fp, #-0x20]
    // 0xa64b18: stp             x16, x1, [SP]
    // 0xa64b1c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa64b1c: sub             lr, x0, #0xb7
    //     0xa64b20: ldr             lr, [x21, lr, lsl #3]
    //     0xa64b24: blr             lr
    // 0xa64b28: LoadField: r1 = r0->field_a3
    //     0xa64b28: ldur            w1, [x0, #0xa3]
    // 0xa64b2c: DecompressPointer r1
    //     0xa64b2c: add             x1, x1, HEAP, lsl #32
    // 0xa64b30: cmp             w1, NULL
    // 0xa64b34: b.eq            #0xa64b9c
    // 0xa64b38: ldur            x1, [fp, #-0x10]
    // 0xa64b3c: r0 = LoadClassIdInstr(r1)
    //     0xa64b3c: ldur            x0, [x1, #-1]
    //     0xa64b40: ubfx            x0, x0, #0xc, #0x14
    // 0xa64b44: ldur            x16, [fp, #-0x20]
    // 0xa64b48: stp             x16, x1, [SP]
    // 0xa64b4c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa64b4c: sub             lr, x0, #0xb7
    //     0xa64b50: ldr             lr, [x21, lr, lsl #3]
    //     0xa64b54: blr             lr
    // 0xa64b58: LoadField: r1 = r0->field_a3
    //     0xa64b58: ldur            w1, [x0, #0xa3]
    // 0xa64b5c: DecompressPointer r1
    //     0xa64b5c: add             x1, x1, HEAP, lsl #32
    // 0xa64b60: cmp             w1, NULL
    // 0xa64b64: b.ne            #0xa64b70
    // 0xa64b68: r0 = Null
    //     0xa64b68: mov             x0, NULL
    // 0xa64b6c: b               #0xa64b88
    // 0xa64b70: LoadField: r0 = r1->field_7
    //     0xa64b70: ldur            w0, [x1, #7]
    // 0xa64b74: cbnz            w0, #0xa64b80
    // 0xa64b78: r1 = false
    //     0xa64b78: add             x1, NULL, #0x30  ; false
    // 0xa64b7c: b               #0xa64b84
    // 0xa64b80: r1 = true
    //     0xa64b80: add             x1, NULL, #0x20  ; true
    // 0xa64b84: mov             x0, x1
    // 0xa64b88: cmp             w0, NULL
    // 0xa64b8c: b.ne            #0xa64b94
    // 0xa64b90: r0 = false
    //     0xa64b90: add             x0, NULL, #0x30  ; false
    // 0xa64b94: mov             x3, x0
    // 0xa64b98: b               #0xa64ba0
    // 0xa64b9c: r3 = false
    //     0xa64b9c: add             x3, NULL, #0x30  ; false
    // 0xa64ba0: ldur            x2, [fp, #-8]
    // 0xa64ba4: ldur            x0, [fp, #-0x10]
    // 0xa64ba8: stur            x3, [fp, #-0x50]
    // 0xa64bac: LoadField: r1 = r2->field_f
    //     0xa64bac: ldur            w1, [x2, #0xf]
    // 0xa64bb0: DecompressPointer r1
    //     0xa64bb0: add             x1, x1, HEAP, lsl #32
    // 0xa64bb4: cmp             w1, NULL
    // 0xa64bb8: b.eq            #0xa65780
    // 0xa64bbc: r0 = of()
    //     0xa64bbc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa64bc0: LoadField: r1 = r0->field_5b
    //     0xa64bc0: ldur            w1, [x0, #0x5b]
    // 0xa64bc4: DecompressPointer r1
    //     0xa64bc4: add             x1, x1, HEAP, lsl #32
    // 0xa64bc8: stur            x1, [fp, #-0x68]
    // 0xa64bcc: r0 = BoxDecoration()
    //     0xa64bcc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa64bd0: mov             x1, x0
    // 0xa64bd4: ldur            x0, [fp, #-0x68]
    // 0xa64bd8: stur            x1, [fp, #-0x70]
    // 0xa64bdc: StoreField: r1->field_7 = r0
    //     0xa64bdc: stur            w0, [x1, #7]
    // 0xa64be0: r0 = Instance_BoxShape
    //     0xa64be0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xa64be4: ldr             x0, [x0, #0x970]
    // 0xa64be8: StoreField: r1->field_23 = r0
    //     0xa64be8: stur            w0, [x1, #0x23]
    // 0xa64bec: r0 = Container()
    //     0xa64bec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa64bf0: stur            x0, [fp, #-0x68]
    // 0xa64bf4: r16 = Instance_EdgeInsets
    //     0xa64bf4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xa64bf8: ldr             x16, [x16, #0x108]
    // 0xa64bfc: r30 = 5.000000
    //     0xa64bfc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xa64c00: ldr             lr, [lr, #0xcf0]
    // 0xa64c04: stp             lr, x16, [SP, #0x10]
    // 0xa64c08: r16 = 5.000000
    //     0xa64c08: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xa64c0c: ldr             x16, [x16, #0xcf0]
    // 0xa64c10: ldur            lr, [fp, #-0x70]
    // 0xa64c14: stp             lr, x16, [SP]
    // 0xa64c18: mov             x1, x0
    // 0xa64c1c: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xa64c1c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xa64c20: ldr             x4, [x4, #0x118]
    // 0xa64c24: r0 = Container()
    //     0xa64c24: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa64c28: r0 = Visibility()
    //     0xa64c28: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa64c2c: mov             x1, x0
    // 0xa64c30: ldur            x0, [fp, #-0x68]
    // 0xa64c34: StoreField: r1->field_b = r0
    //     0xa64c34: stur            w0, [x1, #0xb]
    // 0xa64c38: r0 = Instance_SizedBox
    //     0xa64c38: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa64c3c: StoreField: r1->field_f = r0
    //     0xa64c3c: stur            w0, [x1, #0xf]
    // 0xa64c40: ldur            x0, [fp, #-0x50]
    // 0xa64c44: StoreField: r1->field_13 = r0
    //     0xa64c44: stur            w0, [x1, #0x13]
    // 0xa64c48: r2 = false
    //     0xa64c48: add             x2, NULL, #0x30  ; false
    // 0xa64c4c: ArrayStore: r1[0] = r2  ; List_4
    //     0xa64c4c: stur            w2, [x1, #0x17]
    // 0xa64c50: StoreField: r1->field_1b = r2
    //     0xa64c50: stur            w2, [x1, #0x1b]
    // 0xa64c54: StoreField: r1->field_1f = r2
    //     0xa64c54: stur            w2, [x1, #0x1f]
    // 0xa64c58: StoreField: r1->field_23 = r2
    //     0xa64c58: stur            w2, [x1, #0x23]
    // 0xa64c5c: StoreField: r1->field_27 = r2
    //     0xa64c5c: stur            w2, [x1, #0x27]
    // 0xa64c60: StoreField: r1->field_2b = r2
    //     0xa64c60: stur            w2, [x1, #0x2b]
    // 0xa64c64: mov             x0, x1
    // 0xa64c68: ldur            x1, [fp, #-0x48]
    // 0xa64c6c: ArrayStore: r1[4] = r0  ; List_4
    //     0xa64c6c: add             x25, x1, #0x1f
    //     0xa64c70: str             w0, [x25]
    //     0xa64c74: tbz             w0, #0, #0xa64c90
    //     0xa64c78: ldurb           w16, [x1, #-1]
    //     0xa64c7c: ldurb           w17, [x0, #-1]
    //     0xa64c80: and             x16, x17, x16, lsr #2
    //     0xa64c84: tst             x16, HEAP, lsr #32
    //     0xa64c88: b.eq            #0xa64c90
    //     0xa64c8c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64c90: ldur            x1, [fp, #-0x10]
    // 0xa64c94: r0 = LoadClassIdInstr(r1)
    //     0xa64c94: ldur            x0, [x1, #-1]
    //     0xa64c98: ubfx            x0, x0, #0xc, #0x14
    // 0xa64c9c: ldur            x16, [fp, #-0x20]
    // 0xa64ca0: stp             x16, x1, [SP]
    // 0xa64ca4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa64ca4: sub             lr, x0, #0xb7
    //     0xa64ca8: ldr             lr, [x21, lr, lsl #3]
    //     0xa64cac: blr             lr
    // 0xa64cb0: LoadField: r1 = r0->field_a3
    //     0xa64cb0: ldur            w1, [x0, #0xa3]
    // 0xa64cb4: DecompressPointer r1
    //     0xa64cb4: add             x1, x1, HEAP, lsl #32
    // 0xa64cb8: cmp             w1, NULL
    // 0xa64cbc: b.ne            #0xa64cc8
    // 0xa64cc0: r3 = ""
    //     0xa64cc0: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa64cc4: b               #0xa64ccc
    // 0xa64cc8: mov             x3, x1
    // 0xa64ccc: ldur            x2, [fp, #-8]
    // 0xa64cd0: ldur            x0, [fp, #-0x10]
    // 0xa64cd4: stur            x3, [fp, #-0x50]
    // 0xa64cd8: LoadField: r1 = r2->field_f
    //     0xa64cd8: ldur            w1, [x2, #0xf]
    // 0xa64cdc: DecompressPointer r1
    //     0xa64cdc: add             x1, x1, HEAP, lsl #32
    // 0xa64ce0: cmp             w1, NULL
    // 0xa64ce4: b.eq            #0xa65784
    // 0xa64ce8: r0 = of()
    //     0xa64ce8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa64cec: LoadField: r1 = r0->field_87
    //     0xa64cec: ldur            w1, [x0, #0x87]
    // 0xa64cf0: DecompressPointer r1
    //     0xa64cf0: add             x1, x1, HEAP, lsl #32
    // 0xa64cf4: LoadField: r0 = r1->field_2b
    //     0xa64cf4: ldur            w0, [x1, #0x2b]
    // 0xa64cf8: DecompressPointer r0
    //     0xa64cf8: add             x0, x0, HEAP, lsl #32
    // 0xa64cfc: stur            x0, [fp, #-0x68]
    // 0xa64d00: r1 = Instance_Color
    //     0xa64d00: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa64d04: d0 = 0.400000
    //     0xa64d04: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa64d08: r0 = withOpacity()
    //     0xa64d08: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa64d0c: r16 = 12.000000
    //     0xa64d0c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa64d10: ldr             x16, [x16, #0x9e8]
    // 0xa64d14: stp             x0, x16, [SP]
    // 0xa64d18: ldur            x1, [fp, #-0x68]
    // 0xa64d1c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa64d1c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa64d20: ldr             x4, [x4, #0xaa0]
    // 0xa64d24: r0 = copyWith()
    //     0xa64d24: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa64d28: stur            x0, [fp, #-0x68]
    // 0xa64d2c: r0 = Text()
    //     0xa64d2c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa64d30: mov             x1, x0
    // 0xa64d34: ldur            x0, [fp, #-0x50]
    // 0xa64d38: StoreField: r1->field_b = r0
    //     0xa64d38: stur            w0, [x1, #0xb]
    // 0xa64d3c: ldur            x0, [fp, #-0x68]
    // 0xa64d40: StoreField: r1->field_13 = r0
    //     0xa64d40: stur            w0, [x1, #0x13]
    // 0xa64d44: mov             x0, x1
    // 0xa64d48: ldur            x1, [fp, #-0x48]
    // 0xa64d4c: ArrayStore: r1[5] = r0  ; List_4
    //     0xa64d4c: add             x25, x1, #0x23
    //     0xa64d50: str             w0, [x25]
    //     0xa64d54: tbz             w0, #0, #0xa64d70
    //     0xa64d58: ldurb           w16, [x1, #-1]
    //     0xa64d5c: ldurb           w17, [x0, #-1]
    //     0xa64d60: and             x16, x17, x16, lsr #2
    //     0xa64d64: tst             x16, HEAP, lsr #32
    //     0xa64d68: b.eq            #0xa64d70
    //     0xa64d6c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64d70: r0 = Container()
    //     0xa64d70: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa64d74: stur            x0, [fp, #-0x50]
    // 0xa64d78: r16 = 5.000000
    //     0xa64d78: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xa64d7c: ldr             x16, [x16, #0xcf0]
    // 0xa64d80: str             x16, [SP]
    // 0xa64d84: mov             x1, x0
    // 0xa64d88: r4 = const [0, 0x2, 0x1, 0x1, width, 0x1, null]
    //     0xa64d88: add             x4, PP, #0x52, lsl #12  ; [pp+0x520e0] List(7) [0, 0x2, 0x1, 0x1, "width", 0x1, Null]
    //     0xa64d8c: ldr             x4, [x4, #0xe0]
    // 0xa64d90: r0 = Container()
    //     0xa64d90: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa64d94: ldur            x1, [fp, #-0x48]
    // 0xa64d98: ldur            x0, [fp, #-0x50]
    // 0xa64d9c: ArrayStore: r1[6] = r0  ; List_4
    //     0xa64d9c: add             x25, x1, #0x27
    //     0xa64da0: str             w0, [x25]
    //     0xa64da4: tbz             w0, #0, #0xa64dc0
    //     0xa64da8: ldurb           w16, [x1, #-1]
    //     0xa64dac: ldurb           w17, [x0, #-1]
    //     0xa64db0: and             x16, x17, x16, lsr #2
    //     0xa64db4: tst             x16, HEAP, lsr #32
    //     0xa64db8: b.eq            #0xa64dc0
    //     0xa64dbc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64dc0: ldur            x0, [fp, #-8]
    // 0xa64dc4: LoadField: r1 = r0->field_f
    //     0xa64dc4: ldur            w1, [x0, #0xf]
    // 0xa64dc8: DecompressPointer r1
    //     0xa64dc8: add             x1, x1, HEAP, lsl #32
    // 0xa64dcc: cmp             w1, NULL
    // 0xa64dd0: b.eq            #0xa65788
    // 0xa64dd4: r0 = of()
    //     0xa64dd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa64dd8: LoadField: r1 = r0->field_5b
    //     0xa64dd8: ldur            w1, [x0, #0x5b]
    // 0xa64ddc: DecompressPointer r1
    //     0xa64ddc: add             x1, x1, HEAP, lsl #32
    // 0xa64de0: stur            x1, [fp, #-0x50]
    // 0xa64de4: r0 = BoxDecoration()
    //     0xa64de4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa64de8: mov             x1, x0
    // 0xa64dec: ldur            x0, [fp, #-0x50]
    // 0xa64df0: stur            x1, [fp, #-0x68]
    // 0xa64df4: StoreField: r1->field_7 = r0
    //     0xa64df4: stur            w0, [x1, #7]
    // 0xa64df8: r0 = Instance_BoxShape
    //     0xa64df8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f970] Obj!BoxShape@d73921
    //     0xa64dfc: ldr             x0, [x0, #0x970]
    // 0xa64e00: StoreField: r1->field_23 = r0
    //     0xa64e00: stur            w0, [x1, #0x23]
    // 0xa64e04: r0 = Container()
    //     0xa64e04: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa64e08: stur            x0, [fp, #-0x50]
    // 0xa64e0c: r16 = Instance_EdgeInsets
    //     0xa64e0c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52108] Obj!EdgeInsets@d579b1
    //     0xa64e10: ldr             x16, [x16, #0x108]
    // 0xa64e14: r30 = 5.000000
    //     0xa64e14: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xa64e18: ldr             lr, [lr, #0xcf0]
    // 0xa64e1c: stp             lr, x16, [SP, #0x10]
    // 0xa64e20: r16 = 5.000000
    //     0xa64e20: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fcf0] 5
    //     0xa64e24: ldr             x16, [x16, #0xcf0]
    // 0xa64e28: ldur            lr, [fp, #-0x68]
    // 0xa64e2c: stp             lr, x16, [SP]
    // 0xa64e30: mov             x1, x0
    // 0xa64e34: r4 = const [0, 0x5, 0x4, 0x1, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0xa64e34: add             x4, PP, #0x52, lsl #12  ; [pp+0x52118] List(13) [0, 0x5, 0x4, 0x1, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0xa64e38: ldr             x4, [x4, #0x118]
    // 0xa64e3c: r0 = Container()
    //     0xa64e3c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa64e40: ldur            x1, [fp, #-0x48]
    // 0xa64e44: ldur            x0, [fp, #-0x50]
    // 0xa64e48: ArrayStore: r1[7] = r0  ; List_4
    //     0xa64e48: add             x25, x1, #0x2b
    //     0xa64e4c: str             w0, [x25]
    //     0xa64e50: tbz             w0, #0, #0xa64e6c
    //     0xa64e54: ldurb           w16, [x1, #-1]
    //     0xa64e58: ldurb           w17, [x0, #-1]
    //     0xa64e5c: and             x16, x17, x16, lsr #2
    //     0xa64e60: tst             x16, HEAP, lsr #32
    //     0xa64e64: b.eq            #0xa64e6c
    //     0xa64e68: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64e6c: ldur            x1, [fp, #-0x10]
    // 0xa64e70: r0 = LoadClassIdInstr(r1)
    //     0xa64e70: ldur            x0, [x1, #-1]
    //     0xa64e74: ubfx            x0, x0, #0xc, #0x14
    // 0xa64e78: ldur            x16, [fp, #-0x20]
    // 0xa64e7c: stp             x16, x1, [SP]
    // 0xa64e80: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa64e80: sub             lr, x0, #0xb7
    //     0xa64e84: ldr             lr, [x21, lr, lsl #3]
    //     0xa64e88: blr             lr
    // 0xa64e8c: LoadField: r1 = r0->field_a7
    //     0xa64e8c: ldur            w1, [x0, #0xa7]
    // 0xa64e90: DecompressPointer r1
    //     0xa64e90: add             x1, x1, HEAP, lsl #32
    // 0xa64e94: cmp             w1, NULL
    // 0xa64e98: b.ne            #0xa64ea4
    // 0xa64e9c: r4 = ""
    //     0xa64e9c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa64ea0: b               #0xa64ea8
    // 0xa64ea4: mov             x4, x1
    // 0xa64ea8: ldur            x2, [fp, #-8]
    // 0xa64eac: ldur            x0, [fp, #-0x10]
    // 0xa64eb0: ldur            x3, [fp, #-0x48]
    // 0xa64eb4: stur            x4, [fp, #-0x50]
    // 0xa64eb8: LoadField: r1 = r2->field_f
    //     0xa64eb8: ldur            w1, [x2, #0xf]
    // 0xa64ebc: DecompressPointer r1
    //     0xa64ebc: add             x1, x1, HEAP, lsl #32
    // 0xa64ec0: cmp             w1, NULL
    // 0xa64ec4: b.eq            #0xa6578c
    // 0xa64ec8: r0 = of()
    //     0xa64ec8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa64ecc: LoadField: r1 = r0->field_87
    //     0xa64ecc: ldur            w1, [x0, #0x87]
    // 0xa64ed0: DecompressPointer r1
    //     0xa64ed0: add             x1, x1, HEAP, lsl #32
    // 0xa64ed4: LoadField: r0 = r1->field_2b
    //     0xa64ed4: ldur            w0, [x1, #0x2b]
    // 0xa64ed8: DecompressPointer r0
    //     0xa64ed8: add             x0, x0, HEAP, lsl #32
    // 0xa64edc: stur            x0, [fp, #-0x68]
    // 0xa64ee0: r1 = Instance_Color
    //     0xa64ee0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa64ee4: d0 = 0.400000
    //     0xa64ee4: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xa64ee8: r0 = withOpacity()
    //     0xa64ee8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa64eec: r16 = 12.000000
    //     0xa64eec: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa64ef0: ldr             x16, [x16, #0x9e8]
    // 0xa64ef4: stp             x0, x16, [SP]
    // 0xa64ef8: ldur            x1, [fp, #-0x68]
    // 0xa64efc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa64efc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa64f00: ldr             x4, [x4, #0xaa0]
    // 0xa64f04: r0 = copyWith()
    //     0xa64f04: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa64f08: stur            x0, [fp, #-0x68]
    // 0xa64f0c: r0 = Text()
    //     0xa64f0c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa64f10: mov             x1, x0
    // 0xa64f14: ldur            x0, [fp, #-0x50]
    // 0xa64f18: StoreField: r1->field_b = r0
    //     0xa64f18: stur            w0, [x1, #0xb]
    // 0xa64f1c: ldur            x0, [fp, #-0x68]
    // 0xa64f20: StoreField: r1->field_13 = r0
    //     0xa64f20: stur            w0, [x1, #0x13]
    // 0xa64f24: mov             x0, x1
    // 0xa64f28: ldur            x1, [fp, #-0x48]
    // 0xa64f2c: ArrayStore: r1[8] = r0  ; List_4
    //     0xa64f2c: add             x25, x1, #0x2f
    //     0xa64f30: str             w0, [x25]
    //     0xa64f34: tbz             w0, #0, #0xa64f50
    //     0xa64f38: ldurb           w16, [x1, #-1]
    //     0xa64f3c: ldurb           w17, [x0, #-1]
    //     0xa64f40: and             x16, x17, x16, lsr #2
    //     0xa64f44: tst             x16, HEAP, lsr #32
    //     0xa64f48: b.eq            #0xa64f50
    //     0xa64f4c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa64f50: r1 = <Widget>
    //     0xa64f50: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa64f54: r0 = AllocateGrowableArray()
    //     0xa64f54: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa64f58: mov             x1, x0
    // 0xa64f5c: ldur            x0, [fp, #-0x48]
    // 0xa64f60: stur            x1, [fp, #-0x50]
    // 0xa64f64: StoreField: r1->field_f = r0
    //     0xa64f64: stur            w0, [x1, #0xf]
    // 0xa64f68: r0 = 18
    //     0xa64f68: movz            x0, #0x12
    // 0xa64f6c: StoreField: r1->field_b = r0
    //     0xa64f6c: stur            w0, [x1, #0xb]
    // 0xa64f70: r0 = Row()
    //     0xa64f70: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa64f74: mov             x2, x0
    // 0xa64f78: r1 = Instance_Axis
    //     0xa64f78: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa64f7c: stur            x2, [fp, #-0x48]
    // 0xa64f80: StoreField: r2->field_f = r1
    //     0xa64f80: stur            w1, [x2, #0xf]
    // 0xa64f84: r3 = Instance_MainAxisAlignment
    //     0xa64f84: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa64f88: ldr             x3, [x3, #0xa08]
    // 0xa64f8c: StoreField: r2->field_13 = r3
    //     0xa64f8c: stur            w3, [x2, #0x13]
    // 0xa64f90: r4 = Instance_MainAxisSize
    //     0xa64f90: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa64f94: ldr             x4, [x4, #0xa10]
    // 0xa64f98: ArrayStore: r2[0] = r4  ; List_4
    //     0xa64f98: stur            w4, [x2, #0x17]
    // 0xa64f9c: r0 = Instance_CrossAxisAlignment
    //     0xa64f9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa64fa0: ldr             x0, [x0, #0xa18]
    // 0xa64fa4: StoreField: r2->field_1b = r0
    //     0xa64fa4: stur            w0, [x2, #0x1b]
    // 0xa64fa8: r5 = Instance_VerticalDirection
    //     0xa64fa8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa64fac: ldr             x5, [x5, #0xa20]
    // 0xa64fb0: StoreField: r2->field_23 = r5
    //     0xa64fb0: stur            w5, [x2, #0x23]
    // 0xa64fb4: r6 = Instance_Clip
    //     0xa64fb4: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa64fb8: ldr             x6, [x6, #0x38]
    // 0xa64fbc: StoreField: r2->field_2b = r6
    //     0xa64fbc: stur            w6, [x2, #0x2b]
    // 0xa64fc0: StoreField: r2->field_2f = rZR
    //     0xa64fc0: stur            xzr, [x2, #0x2f]
    // 0xa64fc4: ldur            x0, [fp, #-0x50]
    // 0xa64fc8: StoreField: r2->field_b = r0
    //     0xa64fc8: stur            w0, [x2, #0xb]
    // 0xa64fcc: ldur            x7, [fp, #-0x10]
    // 0xa64fd0: r0 = LoadClassIdInstr(r7)
    //     0xa64fd0: ldur            x0, [x7, #-1]
    //     0xa64fd4: ubfx            x0, x0, #0xc, #0x14
    // 0xa64fd8: ldur            x16, [fp, #-0x20]
    // 0xa64fdc: stp             x16, x7, [SP]
    // 0xa64fe0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa64fe0: sub             lr, x0, #0xb7
    //     0xa64fe4: ldr             lr, [x21, lr, lsl #3]
    //     0xa64fe8: blr             lr
    // 0xa64fec: LoadField: r1 = r0->field_9b
    //     0xa64fec: ldur            w1, [x0, #0x9b]
    // 0xa64ff0: DecompressPointer r1
    //     0xa64ff0: add             x1, x1, HEAP, lsl #32
    // 0xa64ff4: cmp             w1, NULL
    // 0xa64ff8: b.ne            #0xa65004
    // 0xa64ffc: r1 = "0.0"
    //     0xa64ffc: add             x1, PP, #0xe, lsl #12  ; [pp+0xe628] "0.0"
    //     0xa65000: ldr             x1, [x1, #0x628]
    // 0xa65004: ldur            x0, [fp, #-0x10]
    // 0xa65008: r0 = parse()
    //     0xa65008: bl              #0x64333c  ; [dart:core] double::parse
    // 0xa6500c: ldur            x0, [fp, #-0x10]
    // 0xa65010: stur            d0, [fp, #-0x80]
    // 0xa65014: r1 = LoadClassIdInstr(r0)
    //     0xa65014: ldur            x1, [x0, #-1]
    //     0xa65018: ubfx            x1, x1, #0xc, #0x14
    // 0xa6501c: ldur            x16, [fp, #-0x20]
    // 0xa65020: stp             x16, x0, [SP]
    // 0xa65024: mov             x0, x1
    // 0xa65028: r0 = GDT[cid_x0 + -0xb7]()
    //     0xa65028: sub             lr, x0, #0xb7
    //     0xa6502c: ldr             lr, [x21, lr, lsl #3]
    //     0xa65030: blr             lr
    // 0xa65034: LoadField: r1 = r0->field_9b
    //     0xa65034: ldur            w1, [x0, #0x9b]
    // 0xa65038: DecompressPointer r1
    //     0xa65038: add             x1, x1, HEAP, lsl #32
    // 0xa6503c: cmp             w1, NULL
    // 0xa65040: b.ne            #0xa65048
    // 0xa65044: r1 = "0"
    //     0xa65044: ldr             x1, [PP, #0x4340]  ; [pp+0x4340] "0"
    // 0xa65048: ldur            x2, [fp, #-0x40]
    // 0xa6504c: ldur            d0, [fp, #-0x80]
    // 0xa65050: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa65050: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa65054: r0 = parse()
    //     0xa65054: bl              #0x6255f0  ; [dart:core] int::parse
    // 0xa65058: stur            x0, [fp, #-0x18]
    // 0xa6505c: r0 = RatingWidget()
    //     0xa6505c: bl              #0x9b101c  ; AllocateRatingWidgetStub -> RatingWidget (size=0x14)
    // 0xa65060: mov             x3, x0
    // 0xa65064: r0 = Instance_Icon
    //     0xa65064: add             x0, PP, #0x52, lsl #12  ; [pp+0x52190] Obj!Icon@d65fb1
    //     0xa65068: ldr             x0, [x0, #0x190]
    // 0xa6506c: stur            x3, [fp, #-0x10]
    // 0xa65070: StoreField: r3->field_7 = r0
    //     0xa65070: stur            w0, [x3, #7]
    // 0xa65074: r0 = Instance_Icon
    //     0xa65074: add             x0, PP, #0x52, lsl #12  ; [pp+0x52198] Obj!Icon@d65f71
    //     0xa65078: ldr             x0, [x0, #0x198]
    // 0xa6507c: StoreField: r3->field_b = r0
    //     0xa6507c: stur            w0, [x3, #0xb]
    // 0xa65080: r0 = Instance_Icon
    //     0xa65080: add             x0, PP, #0x52, lsl #12  ; [pp+0x521a0] Obj!Icon@d65f31
    //     0xa65084: ldr             x0, [x0, #0x1a0]
    // 0xa65088: StoreField: r3->field_f = r0
    //     0xa65088: stur            w0, [x3, #0xf]
    // 0xa6508c: r1 = Function '<anonymous closure>':.
    //     0xa6508c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a368] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa65090: ldr             x1, [x1, #0x368]
    // 0xa65094: r2 = Null
    //     0xa65094: mov             x2, NULL
    // 0xa65098: r0 = AllocateClosure()
    //     0xa65098: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa6509c: stur            x0, [fp, #-0x20]
    // 0xa650a0: r0 = RatingBar()
    //     0xa650a0: bl              #0x9980ac  ; AllocateRatingBarStub -> RatingBar (size=0x6c)
    // 0xa650a4: mov             x2, x0
    // 0xa650a8: ldur            x0, [fp, #-0x20]
    // 0xa650ac: stur            x2, [fp, #-0x50]
    // 0xa650b0: StoreField: r2->field_b = r0
    //     0xa650b0: stur            w0, [x2, #0xb]
    // 0xa650b4: r0 = true
    //     0xa650b4: add             x0, NULL, #0x20  ; true
    // 0xa650b8: StoreField: r2->field_1f = r0
    //     0xa650b8: stur            w0, [x2, #0x1f]
    // 0xa650bc: r1 = Instance_Axis
    //     0xa650bc: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa650c0: StoreField: r2->field_23 = r1
    //     0xa650c0: stur            w1, [x2, #0x23]
    // 0xa650c4: StoreField: r2->field_27 = r0
    //     0xa650c4: stur            w0, [x2, #0x27]
    // 0xa650c8: d0 = 2.000000
    //     0xa650c8: fmov            d0, #2.00000000
    // 0xa650cc: StoreField: r2->field_2b = d0
    //     0xa650cc: stur            d0, [x2, #0x2b]
    // 0xa650d0: StoreField: r2->field_33 = r0
    //     0xa650d0: stur            w0, [x2, #0x33]
    // 0xa650d4: ldur            d0, [fp, #-0x80]
    // 0xa650d8: StoreField: r2->field_37 = d0
    //     0xa650d8: stur            d0, [x2, #0x37]
    // 0xa650dc: ldur            x1, [fp, #-0x18]
    // 0xa650e0: StoreField: r2->field_3f = r1
    //     0xa650e0: stur            x1, [x2, #0x3f]
    // 0xa650e4: r1 = Instance_EdgeInsets
    //     0xa650e4: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xa650e8: StoreField: r2->field_47 = r1
    //     0xa650e8: stur            w1, [x2, #0x47]
    // 0xa650ec: d0 = 18.000000
    //     0xa650ec: fmov            d0, #18.00000000
    // 0xa650f0: StoreField: r2->field_4b = d0
    //     0xa650f0: stur            d0, [x2, #0x4b]
    // 0xa650f4: StoreField: r2->field_53 = rZR
    //     0xa650f4: stur            xzr, [x2, #0x53]
    // 0xa650f8: r1 = false
    //     0xa650f8: add             x1, NULL, #0x30  ; false
    // 0xa650fc: StoreField: r2->field_5b = r1
    //     0xa650fc: stur            w1, [x2, #0x5b]
    // 0xa65100: StoreField: r2->field_5f = r1
    //     0xa65100: stur            w1, [x2, #0x5f]
    // 0xa65104: ldur            x1, [fp, #-0x10]
    // 0xa65108: StoreField: r2->field_67 = r1
    //     0xa65108: stur            w1, [x2, #0x67]
    // 0xa6510c: ldur            x3, [fp, #-0x40]
    // 0xa65110: LoadField: r1 = r3->field_9f
    //     0xa65110: ldur            w1, [x3, #0x9f]
    // 0xa65114: DecompressPointer r1
    //     0xa65114: add             x1, x1, HEAP, lsl #32
    // 0xa65118: cmp             w1, NULL
    // 0xa6511c: b.ne            #0xa65128
    // 0xa65120: r0 = Null
    //     0xa65120: mov             x0, NULL
    // 0xa65124: b               #0xa6512c
    // 0xa65128: r0 = trim()
    //     0xa65128: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xa6512c: cmp             w0, NULL
    // 0xa65130: b.ne            #0xa65138
    // 0xa65134: r0 = ""
    //     0xa65134: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa65138: ldur            x1, [fp, #-0x30]
    // 0xa6513c: stur            x0, [fp, #-0x10]
    // 0xa65140: r0 = value()
    //     0xa65140: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa65144: tbnz            w0, #4, #0xa65150
    // 0xa65148: r0 = Null
    //     0xa65148: mov             x0, NULL
    // 0xa6514c: b               #0xa65154
    // 0xa65150: r0 = 4
    //     0xa65150: movz            x0, #0x4
    // 0xa65154: ldur            x1, [fp, #-0x30]
    // 0xa65158: stur            x0, [fp, #-0x20]
    // 0xa6515c: r0 = value()
    //     0xa6515c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa65160: tbnz            w0, #4, #0xa65170
    // 0xa65164: r5 = Instance_TextOverflow
    //     0xa65164: add             x5, PP, #0x4b, lsl #12  ; [pp+0x4b3a8] Obj!TextOverflow@d73761
    //     0xa65168: ldr             x5, [x5, #0x3a8]
    // 0xa6516c: b               #0xa65178
    // 0xa65170: r5 = Instance_TextOverflow
    //     0xa65170: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xa65174: ldr             x5, [x5, #0xe10]
    // 0xa65178: ldur            x4, [fp, #-8]
    // 0xa6517c: ldur            x3, [fp, #-0x40]
    // 0xa65180: ldur            x2, [fp, #-0x10]
    // 0xa65184: ldur            x0, [fp, #-0x20]
    // 0xa65188: stur            x5, [fp, #-0x68]
    // 0xa6518c: LoadField: r1 = r4->field_f
    //     0xa6518c: ldur            w1, [x4, #0xf]
    // 0xa65190: DecompressPointer r1
    //     0xa65190: add             x1, x1, HEAP, lsl #32
    // 0xa65194: cmp             w1, NULL
    // 0xa65198: b.eq            #0xa65790
    // 0xa6519c: r0 = of()
    //     0xa6519c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa651a0: LoadField: r1 = r0->field_87
    //     0xa651a0: ldur            w1, [x0, #0x87]
    // 0xa651a4: DecompressPointer r1
    //     0xa651a4: add             x1, x1, HEAP, lsl #32
    // 0xa651a8: LoadField: r0 = r1->field_2b
    //     0xa651a8: ldur            w0, [x1, #0x2b]
    // 0xa651ac: DecompressPointer r0
    //     0xa651ac: add             x0, x0, HEAP, lsl #32
    // 0xa651b0: LoadField: r1 = r0->field_13
    //     0xa651b0: ldur            w1, [x0, #0x13]
    // 0xa651b4: DecompressPointer r1
    //     0xa651b4: add             x1, x1, HEAP, lsl #32
    // 0xa651b8: stur            x1, [fp, #-0x70]
    // 0xa651bc: r0 = TextStyle()
    //     0xa651bc: bl              #0x6b0a90  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xa651c0: mov             x1, x0
    // 0xa651c4: r0 = true
    //     0xa651c4: add             x0, NULL, #0x20  ; true
    // 0xa651c8: stur            x1, [fp, #-0x78]
    // 0xa651cc: StoreField: r1->field_7 = r0
    //     0xa651cc: stur            w0, [x1, #7]
    // 0xa651d0: r0 = Instance_Color
    //     0xa651d0: ldr             x0, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa651d4: StoreField: r1->field_b = r0
    //     0xa651d4: stur            w0, [x1, #0xb]
    // 0xa651d8: r0 = 12.000000
    //     0xa651d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa651dc: ldr             x0, [x0, #0x9e8]
    // 0xa651e0: StoreField: r1->field_1f = r0
    //     0xa651e0: stur            w0, [x1, #0x1f]
    // 0xa651e4: ldur            x0, [fp, #-0x70]
    // 0xa651e8: StoreField: r1->field_13 = r0
    //     0xa651e8: stur            w0, [x1, #0x13]
    // 0xa651ec: r0 = Text()
    //     0xa651ec: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa651f0: mov             x3, x0
    // 0xa651f4: ldur            x0, [fp, #-0x10]
    // 0xa651f8: stur            x3, [fp, #-0x70]
    // 0xa651fc: StoreField: r3->field_b = r0
    //     0xa651fc: stur            w0, [x3, #0xb]
    // 0xa65200: ldur            x0, [fp, #-0x78]
    // 0xa65204: StoreField: r3->field_13 = r0
    //     0xa65204: stur            w0, [x3, #0x13]
    // 0xa65208: r0 = Instance_TextAlign
    //     0xa65208: ldr             x0, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xa6520c: StoreField: r3->field_1b = r0
    //     0xa6520c: stur            w0, [x3, #0x1b]
    // 0xa65210: ldur            x0, [fp, #-0x68]
    // 0xa65214: StoreField: r3->field_2b = r0
    //     0xa65214: stur            w0, [x3, #0x2b]
    // 0xa65218: ldur            x0, [fp, #-0x20]
    // 0xa6521c: StoreField: r3->field_37 = r0
    //     0xa6521c: stur            w0, [x3, #0x37]
    // 0xa65220: r1 = Null
    //     0xa65220: mov             x1, NULL
    // 0xa65224: r2 = 2
    //     0xa65224: movz            x2, #0x2
    // 0xa65228: r0 = AllocateArray()
    //     0xa65228: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa6522c: mov             x2, x0
    // 0xa65230: ldur            x0, [fp, #-0x70]
    // 0xa65234: stur            x2, [fp, #-0x10]
    // 0xa65238: StoreField: r2->field_f = r0
    //     0xa65238: stur            w0, [x2, #0xf]
    // 0xa6523c: r1 = <Widget>
    //     0xa6523c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa65240: r0 = AllocateGrowableArray()
    //     0xa65240: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa65244: mov             x2, x0
    // 0xa65248: ldur            x0, [fp, #-0x10]
    // 0xa6524c: stur            x2, [fp, #-0x20]
    // 0xa65250: StoreField: r2->field_f = r0
    //     0xa65250: stur            w0, [x2, #0xf]
    // 0xa65254: r0 = 2
    //     0xa65254: movz            x0, #0x2
    // 0xa65258: StoreField: r2->field_b = r0
    //     0xa65258: stur            w0, [x2, #0xb]
    // 0xa6525c: ldur            x0, [fp, #-0x40]
    // 0xa65260: LoadField: r1 = r0->field_9f
    //     0xa65260: ldur            w1, [x0, #0x9f]
    // 0xa65264: DecompressPointer r1
    //     0xa65264: add             x1, x1, HEAP, lsl #32
    // 0xa65268: cmp             w1, NULL
    // 0xa6526c: b.ne            #0xa65278
    // 0xa65270: r0 = Null
    //     0xa65270: mov             x0, NULL
    // 0xa65274: b               #0xa6527c
    // 0xa65278: r0 = trim()
    //     0xa65278: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0xa6527c: cmp             w0, NULL
    // 0xa65280: b.ne            #0xa6528c
    // 0xa65284: r1 = ""
    //     0xa65284: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa65288: b               #0xa65290
    // 0xa6528c: mov             x1, x0
    // 0xa65290: ldur            x0, [fp, #-8]
    // 0xa65294: LoadField: r2 = r0->field_f
    //     0xa65294: ldur            w2, [x0, #0xf]
    // 0xa65298: DecompressPointer r2
    //     0xa65298: add             x2, x2, HEAP, lsl #32
    // 0xa6529c: cmp             w2, NULL
    // 0xa652a0: b.eq            #0xa65794
    // 0xa652a4: r0 = TextExceeds.textExceedsLines()
    //     0xa652a4: bl              #0xa5ca58  ; [package:customer_app/app/core/extension/extension_function.dart] ::TextExceeds.textExceedsLines
    // 0xa652a8: tbnz            w0, #4, #0xa65434
    // 0xa652ac: ldur            x1, [fp, #-0x30]
    // 0xa652b0: r0 = value()
    //     0xa652b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa652b4: tbnz            w0, #4, #0xa652c4
    // 0xa652b8: r3 = "Know Less"
    //     0xa652b8: add             x3, PP, #0x52, lsl #12  ; [pp+0x521d0] "Know Less"
    //     0xa652bc: ldr             x3, [x3, #0x1d0]
    // 0xa652c0: b               #0xa652cc
    // 0xa652c4: r3 = "Know more"
    //     0xa652c4: add             x3, PP, #0x36, lsl #12  ; [pp+0x36020] "Know more"
    //     0xa652c8: ldr             x3, [x3, #0x20]
    // 0xa652cc: ldur            x0, [fp, #-8]
    // 0xa652d0: ldur            x2, [fp, #-0x20]
    // 0xa652d4: stur            x3, [fp, #-0x10]
    // 0xa652d8: LoadField: r1 = r0->field_f
    //     0xa652d8: ldur            w1, [x0, #0xf]
    // 0xa652dc: DecompressPointer r1
    //     0xa652dc: add             x1, x1, HEAP, lsl #32
    // 0xa652e0: cmp             w1, NULL
    // 0xa652e4: b.eq            #0xa65798
    // 0xa652e8: r0 = of()
    //     0xa652e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa652ec: LoadField: r1 = r0->field_87
    //     0xa652ec: ldur            w1, [x0, #0x87]
    // 0xa652f0: DecompressPointer r1
    //     0xa652f0: add             x1, x1, HEAP, lsl #32
    // 0xa652f4: LoadField: r0 = r1->field_7
    //     0xa652f4: ldur            w0, [x1, #7]
    // 0xa652f8: DecompressPointer r0
    //     0xa652f8: add             x0, x0, HEAP, lsl #32
    // 0xa652fc: ldur            x2, [fp, #-8]
    // 0xa65300: stur            x0, [fp, #-0x30]
    // 0xa65304: LoadField: r1 = r2->field_f
    //     0xa65304: ldur            w1, [x2, #0xf]
    // 0xa65308: DecompressPointer r1
    //     0xa65308: add             x1, x1, HEAP, lsl #32
    // 0xa6530c: cmp             w1, NULL
    // 0xa65310: b.eq            #0xa6579c
    // 0xa65314: r0 = of()
    //     0xa65314: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa65318: LoadField: r1 = r0->field_5b
    //     0xa65318: ldur            w1, [x0, #0x5b]
    // 0xa6531c: DecompressPointer r1
    //     0xa6531c: add             x1, x1, HEAP, lsl #32
    // 0xa65320: r16 = 12.000000
    //     0xa65320: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa65324: ldr             x16, [x16, #0x9e8]
    // 0xa65328: stp             x1, x16, [SP, #8]
    // 0xa6532c: r16 = Instance_TextDecoration
    //     0xa6532c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xa65330: ldr             x16, [x16, #0x10]
    // 0xa65334: str             x16, [SP]
    // 0xa65338: ldur            x1, [fp, #-0x30]
    // 0xa6533c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, decoration, 0x3, fontSize, 0x1, null]
    //     0xa6533c: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe38] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "decoration", 0x3, "fontSize", 0x1, Null]
    //     0xa65340: ldr             x4, [x4, #0xe38]
    // 0xa65344: r0 = copyWith()
    //     0xa65344: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa65348: stur            x0, [fp, #-0x30]
    // 0xa6534c: r0 = Text()
    //     0xa6534c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa65350: mov             x1, x0
    // 0xa65354: ldur            x0, [fp, #-0x10]
    // 0xa65358: stur            x1, [fp, #-0x68]
    // 0xa6535c: StoreField: r1->field_b = r0
    //     0xa6535c: stur            w0, [x1, #0xb]
    // 0xa65360: ldur            x0, [fp, #-0x30]
    // 0xa65364: StoreField: r1->field_13 = r0
    //     0xa65364: stur            w0, [x1, #0x13]
    // 0xa65368: r0 = Padding()
    //     0xa65368: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa6536c: mov             x1, x0
    // 0xa65370: r0 = Instance_EdgeInsets
    //     0xa65370: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xa65374: ldr             x0, [x0, #0x668]
    // 0xa65378: stur            x1, [fp, #-0x10]
    // 0xa6537c: StoreField: r1->field_f = r0
    //     0xa6537c: stur            w0, [x1, #0xf]
    // 0xa65380: ldur            x0, [fp, #-0x68]
    // 0xa65384: StoreField: r1->field_b = r0
    //     0xa65384: stur            w0, [x1, #0xb]
    // 0xa65388: r0 = GestureDetector()
    //     0xa65388: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa6538c: ldur            x2, [fp, #-0x28]
    // 0xa65390: r1 = Function '<anonymous closure>':.
    //     0xa65390: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a370] AnonymousClosure: (0xa662cc), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_testimonialWidget (0xa64738)
    //     0xa65394: ldr             x1, [x1, #0x370]
    // 0xa65398: stur            x0, [fp, #-0x28]
    // 0xa6539c: r0 = AllocateClosure()
    //     0xa6539c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa653a0: ldur            x16, [fp, #-0x10]
    // 0xa653a4: stp             x16, x0, [SP]
    // 0xa653a8: ldur            x1, [fp, #-0x28]
    // 0xa653ac: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa653ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa653b0: ldr             x4, [x4, #0xaf0]
    // 0xa653b4: r0 = GestureDetector()
    //     0xa653b4: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa653b8: ldur            x0, [fp, #-0x20]
    // 0xa653bc: LoadField: r1 = r0->field_b
    //     0xa653bc: ldur            w1, [x0, #0xb]
    // 0xa653c0: LoadField: r2 = r0->field_f
    //     0xa653c0: ldur            w2, [x0, #0xf]
    // 0xa653c4: DecompressPointer r2
    //     0xa653c4: add             x2, x2, HEAP, lsl #32
    // 0xa653c8: LoadField: r3 = r2->field_b
    //     0xa653c8: ldur            w3, [x2, #0xb]
    // 0xa653cc: r2 = LoadInt32Instr(r1)
    //     0xa653cc: sbfx            x2, x1, #1, #0x1f
    // 0xa653d0: stur            x2, [fp, #-0x18]
    // 0xa653d4: r1 = LoadInt32Instr(r3)
    //     0xa653d4: sbfx            x1, x3, #1, #0x1f
    // 0xa653d8: cmp             x2, x1
    // 0xa653dc: b.ne            #0xa653e8
    // 0xa653e0: mov             x1, x0
    // 0xa653e4: r0 = _growToNextCapacity()
    //     0xa653e4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa653e8: ldur            x2, [fp, #-0x20]
    // 0xa653ec: ldur            x3, [fp, #-0x18]
    // 0xa653f0: add             x0, x3, #1
    // 0xa653f4: lsl             x1, x0, #1
    // 0xa653f8: StoreField: r2->field_b = r1
    //     0xa653f8: stur            w1, [x2, #0xb]
    // 0xa653fc: LoadField: r1 = r2->field_f
    //     0xa653fc: ldur            w1, [x2, #0xf]
    // 0xa65400: DecompressPointer r1
    //     0xa65400: add             x1, x1, HEAP, lsl #32
    // 0xa65404: ldur            x0, [fp, #-0x28]
    // 0xa65408: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa65408: add             x25, x1, x3, lsl #2
    //     0xa6540c: add             x25, x25, #0xf
    //     0xa65410: str             w0, [x25]
    //     0xa65414: tbz             w0, #0, #0xa65430
    //     0xa65418: ldurb           w16, [x1, #-1]
    //     0xa6541c: ldurb           w17, [x0, #-1]
    //     0xa65420: and             x16, x17, x16, lsr #2
    //     0xa65424: tst             x16, HEAP, lsr #32
    //     0xa65428: b.eq            #0xa65430
    //     0xa6542c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa65430: b               #0xa65438
    // 0xa65434: ldur            x2, [fp, #-0x20]
    // 0xa65438: ldur            x4, [fp, #-0x38]
    // 0xa6543c: ldur            x3, [fp, #-0x60]
    // 0xa65440: ldur            x1, [fp, #-0x48]
    // 0xa65444: ldur            x0, [fp, #-0x50]
    // 0xa65448: r0 = Column()
    //     0xa65448: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa6544c: mov             x3, x0
    // 0xa65450: r0 = Instance_Axis
    //     0xa65450: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa65454: stur            x3, [fp, #-0x10]
    // 0xa65458: StoreField: r3->field_f = r0
    //     0xa65458: stur            w0, [x3, #0xf]
    // 0xa6545c: r4 = Instance_MainAxisAlignment
    //     0xa6545c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa65460: ldr             x4, [x4, #0xa08]
    // 0xa65464: StoreField: r3->field_13 = r4
    //     0xa65464: stur            w4, [x3, #0x13]
    // 0xa65468: r5 = Instance_MainAxisSize
    //     0xa65468: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa6546c: ldr             x5, [x5, #0xa10]
    // 0xa65470: ArrayStore: r3[0] = r5  ; List_4
    //     0xa65470: stur            w5, [x3, #0x17]
    // 0xa65474: r6 = Instance_CrossAxisAlignment
    //     0xa65474: add             x6, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa65478: ldr             x6, [x6, #0x890]
    // 0xa6547c: StoreField: r3->field_1b = r6
    //     0xa6547c: stur            w6, [x3, #0x1b]
    // 0xa65480: r7 = Instance_VerticalDirection
    //     0xa65480: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa65484: ldr             x7, [x7, #0xa20]
    // 0xa65488: StoreField: r3->field_23 = r7
    //     0xa65488: stur            w7, [x3, #0x23]
    // 0xa6548c: r8 = Instance_Clip
    //     0xa6548c: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa65490: ldr             x8, [x8, #0x38]
    // 0xa65494: StoreField: r3->field_2b = r8
    //     0xa65494: stur            w8, [x3, #0x2b]
    // 0xa65498: StoreField: r3->field_2f = rZR
    //     0xa65498: stur            xzr, [x3, #0x2f]
    // 0xa6549c: ldur            x1, [fp, #-0x20]
    // 0xa654a0: StoreField: r3->field_b = r1
    //     0xa654a0: stur            w1, [x3, #0xb]
    // 0xa654a4: r1 = Null
    //     0xa654a4: mov             x1, NULL
    // 0xa654a8: r2 = 16
    //     0xa654a8: movz            x2, #0x10
    // 0xa654ac: r0 = AllocateArray()
    //     0xa654ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa654b0: mov             x2, x0
    // 0xa654b4: ldur            x0, [fp, #-0x60]
    // 0xa654b8: stur            x2, [fp, #-0x20]
    // 0xa654bc: StoreField: r2->field_f = r0
    //     0xa654bc: stur            w0, [x2, #0xf]
    // 0xa654c0: r16 = Instance_SizedBox
    //     0xa654c0: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xa654c4: ldr             x16, [x16, #0xd8]
    // 0xa654c8: StoreField: r2->field_13 = r16
    //     0xa654c8: stur            w16, [x2, #0x13]
    // 0xa654cc: ldur            x0, [fp, #-0x48]
    // 0xa654d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xa654d0: stur            w0, [x2, #0x17]
    // 0xa654d4: r16 = Instance_SizedBox
    //     0xa654d4: add             x16, PP, #0x55, lsl #12  ; [pp+0x550d8] Obj!SizedBox@d68001
    //     0xa654d8: ldr             x16, [x16, #0xd8]
    // 0xa654dc: StoreField: r2->field_1b = r16
    //     0xa654dc: stur            w16, [x2, #0x1b]
    // 0xa654e0: ldur            x0, [fp, #-0x50]
    // 0xa654e4: StoreField: r2->field_1f = r0
    //     0xa654e4: stur            w0, [x2, #0x1f]
    // 0xa654e8: r16 = Instance_SizedBox
    //     0xa654e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xa654ec: ldr             x16, [x16, #0x9f0]
    // 0xa654f0: StoreField: r2->field_23 = r16
    //     0xa654f0: stur            w16, [x2, #0x23]
    // 0xa654f4: ldur            x0, [fp, #-0x10]
    // 0xa654f8: StoreField: r2->field_27 = r0
    //     0xa654f8: stur            w0, [x2, #0x27]
    // 0xa654fc: r16 = Instance_SizedBox
    //     0xa654fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9f0] Obj!SizedBox@d67e81
    //     0xa65500: ldr             x16, [x16, #0x9f0]
    // 0xa65504: StoreField: r2->field_2b = r16
    //     0xa65504: stur            w16, [x2, #0x2b]
    // 0xa65508: r1 = <Widget>
    //     0xa65508: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa6550c: r0 = AllocateGrowableArray()
    //     0xa6550c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa65510: mov             x3, x0
    // 0xa65514: ldur            x0, [fp, #-0x20]
    // 0xa65518: stur            x3, [fp, #-0x10]
    // 0xa6551c: StoreField: r3->field_f = r0
    //     0xa6551c: stur            w0, [x3, #0xf]
    // 0xa65520: r0 = 16
    //     0xa65520: movz            x0, #0x10
    // 0xa65524: StoreField: r3->field_b = r0
    //     0xa65524: stur            w0, [x3, #0xb]
    // 0xa65528: ldur            x0, [fp, #-0x38]
    // 0xa6552c: tbnz            w0, #4, #0xa6566c
    // 0xa65530: ldur            x2, [fp, #-0x40]
    // 0xa65534: LoadField: r0 = r2->field_ab
    //     0xa65534: ldur            w0, [x2, #0xab]
    // 0xa65538: DecompressPointer r0
    //     0xa65538: add             x0, x0, HEAP, lsl #32
    // 0xa6553c: cmp             w0, NULL
    // 0xa65540: b.ne            #0xa6554c
    // 0xa65544: r0 = Null
    //     0xa65544: mov             x0, NULL
    // 0xa65548: b               #0xa65554
    // 0xa6554c: LoadField: r1 = r0->field_b
    //     0xa6554c: ldur            w1, [x0, #0xb]
    // 0xa65550: mov             x0, x1
    // 0xa65554: cmp             w0, NULL
    // 0xa65558: b.ne            #0xa65564
    // 0xa6555c: r0 = 0
    //     0xa6555c: movz            x0, #0
    // 0xa65560: b               #0xa6556c
    // 0xa65564: r1 = LoadInt32Instr(r0)
    //     0xa65564: sbfx            x1, x0, #1, #0x1f
    // 0xa65568: mov             x0, x1
    // 0xa6556c: cmp             x0, #3
    // 0xa65570: b.gt            #0xa65590
    // 0xa65574: ldur            x1, [fp, #-8]
    // 0xa65578: LoadField: r0 = r1->field_f
    //     0xa65578: ldur            w0, [x1, #0xf]
    // 0xa6557c: DecompressPointer r0
    //     0xa6557c: add             x0, x0, HEAP, lsl #32
    // 0xa65580: cmp             w0, NULL
    // 0xa65584: b.eq            #0xa657a0
    // 0xa65588: r0 = _buildImagesRow()
    //     0xa65588: bl              #0xa65fc8  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow
    // 0xa6558c: b               #0xa655a8
    // 0xa65590: ldur            x1, [fp, #-8]
    // 0xa65594: LoadField: r3 = r1->field_f
    //     0xa65594: ldur            w3, [x1, #0xf]
    // 0xa65598: DecompressPointer r3
    //     0xa65598: add             x3, x3, HEAP, lsl #32
    // 0xa6559c: cmp             w3, NULL
    // 0xa655a0: b.eq            #0xa657a4
    // 0xa655a4: r0 = _buildImagesRowWithMore()
    //     0xa655a4: bl              #0xa657a8  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore
    // 0xa655a8: ldur            x1, [fp, #-0x10]
    // 0xa655ac: stur            x0, [fp, #-8]
    // 0xa655b0: r0 = SizedBox()
    //     0xa655b0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa655b4: mov             x1, x0
    // 0xa655b8: r0 = 120.000000
    //     0xa655b8: add             x0, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xa655bc: ldr             x0, [x0, #0x3a0]
    // 0xa655c0: stur            x1, [fp, #-0x20]
    // 0xa655c4: StoreField: r1->field_13 = r0
    //     0xa655c4: stur            w0, [x1, #0x13]
    // 0xa655c8: ldur            x0, [fp, #-8]
    // 0xa655cc: StoreField: r1->field_b = r0
    //     0xa655cc: stur            w0, [x1, #0xb]
    // 0xa655d0: r0 = Padding()
    //     0xa655d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa655d4: mov             x2, x0
    // 0xa655d8: r0 = Instance_EdgeInsets
    //     0xa655d8: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xa655dc: ldr             x0, [x0, #0x858]
    // 0xa655e0: stur            x2, [fp, #-8]
    // 0xa655e4: StoreField: r2->field_f = r0
    //     0xa655e4: stur            w0, [x2, #0xf]
    // 0xa655e8: ldur            x0, [fp, #-0x20]
    // 0xa655ec: StoreField: r2->field_b = r0
    //     0xa655ec: stur            w0, [x2, #0xb]
    // 0xa655f0: ldur            x0, [fp, #-0x10]
    // 0xa655f4: LoadField: r1 = r0->field_b
    //     0xa655f4: ldur            w1, [x0, #0xb]
    // 0xa655f8: LoadField: r3 = r0->field_f
    //     0xa655f8: ldur            w3, [x0, #0xf]
    // 0xa655fc: DecompressPointer r3
    //     0xa655fc: add             x3, x3, HEAP, lsl #32
    // 0xa65600: LoadField: r4 = r3->field_b
    //     0xa65600: ldur            w4, [x3, #0xb]
    // 0xa65604: r3 = LoadInt32Instr(r1)
    //     0xa65604: sbfx            x3, x1, #1, #0x1f
    // 0xa65608: stur            x3, [fp, #-0x18]
    // 0xa6560c: r1 = LoadInt32Instr(r4)
    //     0xa6560c: sbfx            x1, x4, #1, #0x1f
    // 0xa65610: cmp             x3, x1
    // 0xa65614: b.ne            #0xa65620
    // 0xa65618: mov             x1, x0
    // 0xa6561c: r0 = _growToNextCapacity()
    //     0xa6561c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa65620: ldur            x2, [fp, #-0x10]
    // 0xa65624: ldur            x3, [fp, #-0x18]
    // 0xa65628: add             x0, x3, #1
    // 0xa6562c: lsl             x1, x0, #1
    // 0xa65630: StoreField: r2->field_b = r1
    //     0xa65630: stur            w1, [x2, #0xb]
    // 0xa65634: LoadField: r1 = r2->field_f
    //     0xa65634: ldur            w1, [x2, #0xf]
    // 0xa65638: DecompressPointer r1
    //     0xa65638: add             x1, x1, HEAP, lsl #32
    // 0xa6563c: ldur            x0, [fp, #-8]
    // 0xa65640: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa65640: add             x25, x1, x3, lsl #2
    //     0xa65644: add             x25, x25, #0xf
    //     0xa65648: str             w0, [x25]
    //     0xa6564c: tbz             w0, #0, #0xa65668
    //     0xa65650: ldurb           w16, [x1, #-1]
    //     0xa65654: ldurb           w17, [x0, #-1]
    //     0xa65658: and             x16, x17, x16, lsr #2
    //     0xa6565c: tst             x16, HEAP, lsr #32
    //     0xa65660: b.eq            #0xa65668
    //     0xa65664: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa65668: b               #0xa65670
    // 0xa6566c: mov             x2, x3
    // 0xa65670: r0 = Column()
    //     0xa65670: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa65674: mov             x1, x0
    // 0xa65678: r0 = Instance_Axis
    //     0xa65678: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa6567c: stur            x1, [fp, #-8]
    // 0xa65680: StoreField: r1->field_f = r0
    //     0xa65680: stur            w0, [x1, #0xf]
    // 0xa65684: r0 = Instance_MainAxisAlignment
    //     0xa65684: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa65688: ldr             x0, [x0, #0xa08]
    // 0xa6568c: StoreField: r1->field_13 = r0
    //     0xa6568c: stur            w0, [x1, #0x13]
    // 0xa65690: r0 = Instance_MainAxisSize
    //     0xa65690: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa65694: ldr             x0, [x0, #0xa10]
    // 0xa65698: ArrayStore: r1[0] = r0  ; List_4
    //     0xa65698: stur            w0, [x1, #0x17]
    // 0xa6569c: r0 = Instance_CrossAxisAlignment
    //     0xa6569c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xa656a0: ldr             x0, [x0, #0x890]
    // 0xa656a4: StoreField: r1->field_1b = r0
    //     0xa656a4: stur            w0, [x1, #0x1b]
    // 0xa656a8: r0 = Instance_VerticalDirection
    //     0xa656a8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa656ac: ldr             x0, [x0, #0xa20]
    // 0xa656b0: StoreField: r1->field_23 = r0
    //     0xa656b0: stur            w0, [x1, #0x23]
    // 0xa656b4: r0 = Instance_Clip
    //     0xa656b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa656b8: ldr             x0, [x0, #0x38]
    // 0xa656bc: StoreField: r1->field_2b = r0
    //     0xa656bc: stur            w0, [x1, #0x2b]
    // 0xa656c0: StoreField: r1->field_2f = rZR
    //     0xa656c0: stur            xzr, [x1, #0x2f]
    // 0xa656c4: ldur            x0, [fp, #-0x10]
    // 0xa656c8: StoreField: r1->field_b = r0
    //     0xa656c8: stur            w0, [x1, #0xb]
    // 0xa656cc: r0 = Padding()
    //     0xa656cc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa656d0: mov             x1, x0
    // 0xa656d4: r0 = Instance_EdgeInsets
    //     0xa656d4: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e560] Obj!EdgeInsets@d582e1
    //     0xa656d8: ldr             x0, [x0, #0x560]
    // 0xa656dc: stur            x1, [fp, #-0x10]
    // 0xa656e0: StoreField: r1->field_f = r0
    //     0xa656e0: stur            w0, [x1, #0xf]
    // 0xa656e4: ldur            x0, [fp, #-8]
    // 0xa656e8: StoreField: r1->field_b = r0
    //     0xa656e8: stur            w0, [x1, #0xb]
    // 0xa656ec: r0 = Container()
    //     0xa656ec: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa656f0: stur            x0, [fp, #-8]
    // 0xa656f4: ldur            x16, [fp, #-0x58]
    // 0xa656f8: ldur            lr, [fp, #-0x10]
    // 0xa656fc: stp             lr, x16, [SP]
    // 0xa65700: mov             x1, x0
    // 0xa65704: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xa65704: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xa65708: ldr             x4, [x4, #0x88]
    // 0xa6570c: r0 = Container()
    //     0xa6570c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa65710: r0 = Padding()
    //     0xa65710: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa65714: mov             x1, x0
    // 0xa65718: r0 = Instance_EdgeInsets
    //     0xa65718: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xa6571c: ldr             x0, [x0, #0x1f0]
    // 0xa65720: stur            x1, [fp, #-0x10]
    // 0xa65724: StoreField: r1->field_f = r0
    //     0xa65724: stur            w0, [x1, #0xf]
    // 0xa65728: ldur            x0, [fp, #-8]
    // 0xa6572c: StoreField: r1->field_b = r0
    //     0xa6572c: stur            w0, [x1, #0xb]
    // 0xa65730: r0 = AnimatedContainer()
    //     0xa65730: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xa65734: stur            x0, [fp, #-8]
    // 0xa65738: r16 = Instance_Cubic
    //     0xa65738: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xa6573c: ldr             x16, [x16, #0xaf8]
    // 0xa65740: str             x16, [SP]
    // 0xa65744: mov             x1, x0
    // 0xa65748: ldur            x2, [fp, #-0x10]
    // 0xa6574c: r3 = Instance_Duration
    //     0xa6574c: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xa65750: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xa65750: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xa65754: ldr             x4, [x4, #0xbc8]
    // 0xa65758: r0 = AnimatedContainer()
    //     0xa65758: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xa6575c: ldur            x0, [fp, #-8]
    // 0xa65760: LeaveFrame
    //     0xa65760: mov             SP, fp
    //     0xa65764: ldp             fp, lr, [SP], #0x10
    // 0xa65768: ret
    //     0xa65768: ret             
    // 0xa6576c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6576c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65770: b               #0xa6475c
    // 0xa65774: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65774: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa65778: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65778: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6577c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6577c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa65780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65780: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa65784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65784: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa65788: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65788: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6578c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6578c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa65790: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65790: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa65794: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65794: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa65798: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa65798: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa6579c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6579c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa657a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa657a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa657a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa657a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildImagesRowWithMore(/* No info */) {
    // ** addr: 0xa657a8, size: 0x490
    // 0xa657a8: EnterFrame
    //     0xa657a8: stp             fp, lr, [SP, #-0x10]!
    //     0xa657ac: mov             fp, SP
    // 0xa657b0: AllocStack(0x58)
    //     0xa657b0: sub             SP, SP, #0x58
    // 0xa657b4: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa657b4: stur            x1, [fp, #-8]
    //     0xa657b8: stur            x2, [fp, #-0x10]
    //     0xa657bc: stur            x3, [fp, #-0x18]
    // 0xa657c0: CheckStackOverflow
    //     0xa657c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa657c4: cmp             SP, x16
    //     0xa657c8: b.ls            #0xa65c30
    // 0xa657cc: r1 = 3
    //     0xa657cc: movz            x1, #0x3
    // 0xa657d0: r0 = AllocateContext()
    //     0xa657d0: bl              #0x16f6108  ; AllocateContextStub
    // 0xa657d4: mov             x4, x0
    // 0xa657d8: ldur            x0, [fp, #-8]
    // 0xa657dc: stur            x4, [fp, #-0x20]
    // 0xa657e0: StoreField: r4->field_f = r0
    //     0xa657e0: stur            w0, [x4, #0xf]
    // 0xa657e4: ldur            x2, [fp, #-0x10]
    // 0xa657e8: StoreField: r4->field_13 = r2
    //     0xa657e8: stur            w2, [x4, #0x13]
    // 0xa657ec: ldur            x1, [fp, #-0x18]
    // 0xa657f0: ArrayStore: r4[0] = r1  ; List_4
    //     0xa657f0: stur            w1, [x4, #0x17]
    // 0xa657f4: mov             x1, x0
    // 0xa657f8: r3 = 0
    //     0xa657f8: movz            x3, #0
    // 0xa657fc: r0 = _buildImageThumbnail()
    //     0xa657fc: bl              #0xa65c38  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xa65800: stur            x0, [fp, #-0x10]
    // 0xa65804: r0 = Padding()
    //     0xa65804: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa65808: mov             x1, x0
    // 0xa6580c: r0 = Instance_EdgeInsets
    //     0xa6580c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xa65810: ldr             x0, [x0, #0x550]
    // 0xa65814: stur            x1, [fp, #-0x18]
    // 0xa65818: StoreField: r1->field_f = r0
    //     0xa65818: stur            w0, [x1, #0xf]
    // 0xa6581c: ldur            x2, [fp, #-0x10]
    // 0xa65820: StoreField: r1->field_b = r2
    //     0xa65820: stur            w2, [x1, #0xb]
    // 0xa65824: r0 = GestureDetector()
    //     0xa65824: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa65828: ldur            x2, [fp, #-0x20]
    // 0xa6582c: r1 = Function '<anonymous closure>':.
    //     0xa6582c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a380] AnonymousClosure: (0xa65f6c), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xa657a8)
    //     0xa65830: ldr             x1, [x1, #0x380]
    // 0xa65834: stur            x0, [fp, #-0x10]
    // 0xa65838: r0 = AllocateClosure()
    //     0xa65838: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa6583c: ldur            x16, [fp, #-0x18]
    // 0xa65840: stp             x16, x0, [SP]
    // 0xa65844: ldur            x1, [fp, #-0x10]
    // 0xa65848: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa65848: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa6584c: ldr             x4, [x4, #0xaf0]
    // 0xa65850: r0 = GestureDetector()
    //     0xa65850: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa65854: ldur            x0, [fp, #-0x20]
    // 0xa65858: LoadField: r2 = r0->field_13
    //     0xa65858: ldur            w2, [x0, #0x13]
    // 0xa6585c: DecompressPointer r2
    //     0xa6585c: add             x2, x2, HEAP, lsl #32
    // 0xa65860: ldur            x1, [fp, #-8]
    // 0xa65864: r3 = 1
    //     0xa65864: movz            x3, #0x1
    // 0xa65868: r0 = _buildImageThumbnail()
    //     0xa65868: bl              #0xa65c38  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImageThumbnail
    // 0xa6586c: stur            x0, [fp, #-8]
    // 0xa65870: r0 = Padding()
    //     0xa65870: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa65874: mov             x1, x0
    // 0xa65878: r0 = Instance_EdgeInsets
    //     0xa65878: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xa6587c: ldr             x0, [x0, #0x550]
    // 0xa65880: stur            x1, [fp, #-0x18]
    // 0xa65884: StoreField: r1->field_f = r0
    //     0xa65884: stur            w0, [x1, #0xf]
    // 0xa65888: ldur            x0, [fp, #-8]
    // 0xa6588c: StoreField: r1->field_b = r0
    //     0xa6588c: stur            w0, [x1, #0xb]
    // 0xa65890: r0 = GestureDetector()
    //     0xa65890: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa65894: ldur            x2, [fp, #-0x20]
    // 0xa65898: r1 = Function '<anonymous closure>':.
    //     0xa65898: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a388] AnonymousClosure: (0xa65f10), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xa657a8)
    //     0xa6589c: ldr             x1, [x1, #0x388]
    // 0xa658a0: stur            x0, [fp, #-8]
    // 0xa658a4: r0 = AllocateClosure()
    //     0xa658a4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa658a8: ldur            x16, [fp, #-0x18]
    // 0xa658ac: stp             x16, x0, [SP]
    // 0xa658b0: ldur            x1, [fp, #-8]
    // 0xa658b4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa658b4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa658b8: ldr             x4, [x4, #0xaf0]
    // 0xa658bc: r0 = GestureDetector()
    //     0xa658bc: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa658c0: r1 = Instance_Color
    //     0xa658c0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa658c4: d0 = 0.030000
    //     0xa658c4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xa658c8: ldr             d0, [x17, #0x238]
    // 0xa658cc: r0 = withOpacity()
    //     0xa658cc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa658d0: stur            x0, [fp, #-0x18]
    // 0xa658d4: r0 = Radius()
    //     0xa658d4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xa658d8: d0 = 10.000000
    //     0xa658d8: fmov            d0, #10.00000000
    // 0xa658dc: stur            x0, [fp, #-0x28]
    // 0xa658e0: StoreField: r0->field_7 = d0
    //     0xa658e0: stur            d0, [x0, #7]
    // 0xa658e4: StoreField: r0->field_f = d0
    //     0xa658e4: stur            d0, [x0, #0xf]
    // 0xa658e8: r0 = BorderRadius()
    //     0xa658e8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xa658ec: mov             x1, x0
    // 0xa658f0: ldur            x0, [fp, #-0x28]
    // 0xa658f4: stur            x1, [fp, #-0x30]
    // 0xa658f8: StoreField: r1->field_7 = r0
    //     0xa658f8: stur            w0, [x1, #7]
    // 0xa658fc: StoreField: r1->field_b = r0
    //     0xa658fc: stur            w0, [x1, #0xb]
    // 0xa65900: StoreField: r1->field_f = r0
    //     0xa65900: stur            w0, [x1, #0xf]
    // 0xa65904: StoreField: r1->field_13 = r0
    //     0xa65904: stur            w0, [x1, #0x13]
    // 0xa65908: r0 = BoxDecoration()
    //     0xa65908: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa6590c: mov             x3, x0
    // 0xa65910: ldur            x0, [fp, #-0x18]
    // 0xa65914: stur            x3, [fp, #-0x28]
    // 0xa65918: StoreField: r3->field_7 = r0
    //     0xa65918: stur            w0, [x3, #7]
    // 0xa6591c: ldur            x0, [fp, #-0x30]
    // 0xa65920: StoreField: r3->field_13 = r0
    //     0xa65920: stur            w0, [x3, #0x13]
    // 0xa65924: r0 = Instance_BoxShape
    //     0xa65924: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa65928: ldr             x0, [x0, #0x80]
    // 0xa6592c: StoreField: r3->field_23 = r0
    //     0xa6592c: stur            w0, [x3, #0x23]
    // 0xa65930: r1 = Null
    //     0xa65930: mov             x1, NULL
    // 0xa65934: r2 = 4
    //     0xa65934: movz            x2, #0x4
    // 0xa65938: r0 = AllocateArray()
    //     0xa65938: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa6593c: mov             x2, x0
    // 0xa65940: r16 = "+"
    //     0xa65940: ldr             x16, [PP, #0x2f50]  ; [pp+0x2f50] "+"
    // 0xa65944: StoreField: r2->field_f = r16
    //     0xa65944: stur            w16, [x2, #0xf]
    // 0xa65948: ldur            x3, [fp, #-0x20]
    // 0xa6594c: LoadField: r0 = r3->field_13
    //     0xa6594c: ldur            w0, [x3, #0x13]
    // 0xa65950: DecompressPointer r0
    //     0xa65950: add             x0, x0, HEAP, lsl #32
    // 0xa65954: LoadField: r1 = r0->field_ab
    //     0xa65954: ldur            w1, [x0, #0xab]
    // 0xa65958: DecompressPointer r1
    //     0xa65958: add             x1, x1, HEAP, lsl #32
    // 0xa6595c: cmp             w1, NULL
    // 0xa65960: b.ne            #0xa6596c
    // 0xa65964: r0 = Null
    //     0xa65964: mov             x0, NULL
    // 0xa65968: b               #0xa65970
    // 0xa6596c: LoadField: r0 = r1->field_b
    //     0xa6596c: ldur            w0, [x1, #0xb]
    // 0xa65970: cmp             w0, NULL
    // 0xa65974: b.ne            #0xa65980
    // 0xa65978: r0 = 0
    //     0xa65978: movz            x0, #0
    // 0xa6597c: b               #0xa65988
    // 0xa65980: r1 = LoadInt32Instr(r0)
    //     0xa65980: sbfx            x1, x0, #1, #0x1f
    // 0xa65984: mov             x0, x1
    // 0xa65988: ldur            x5, [fp, #-0x10]
    // 0xa6598c: ldur            x4, [fp, #-8]
    // 0xa65990: sub             x6, x0, #2
    // 0xa65994: r0 = BoxInt64Instr(r6)
    //     0xa65994: sbfiz           x0, x6, #1, #0x1f
    //     0xa65998: cmp             x6, x0, asr #1
    //     0xa6599c: b.eq            #0xa659a8
    //     0xa659a0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa659a4: stur            x6, [x0, #7]
    // 0xa659a8: StoreField: r2->field_13 = r0
    //     0xa659a8: stur            w0, [x2, #0x13]
    // 0xa659ac: str             x2, [SP]
    // 0xa659b0: r0 = _interpolate()
    //     0xa659b0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xa659b4: ldur            x2, [fp, #-0x20]
    // 0xa659b8: stur            x0, [fp, #-0x18]
    // 0xa659bc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xa659bc: ldur            w1, [x2, #0x17]
    // 0xa659c0: DecompressPointer r1
    //     0xa659c0: add             x1, x1, HEAP, lsl #32
    // 0xa659c4: r0 = of()
    //     0xa659c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa659c8: LoadField: r1 = r0->field_87
    //     0xa659c8: ldur            w1, [x0, #0x87]
    // 0xa659cc: DecompressPointer r1
    //     0xa659cc: add             x1, x1, HEAP, lsl #32
    // 0xa659d0: LoadField: r0 = r1->field_2b
    //     0xa659d0: ldur            w0, [x1, #0x2b]
    // 0xa659d4: DecompressPointer r0
    //     0xa659d4: add             x0, x0, HEAP, lsl #32
    // 0xa659d8: r16 = 12.000000
    //     0xa659d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa659dc: ldr             x16, [x16, #0x9e8]
    // 0xa659e0: r30 = Instance_Color
    //     0xa659e0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa659e4: stp             lr, x16, [SP]
    // 0xa659e8: mov             x1, x0
    // 0xa659ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa659ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa659f0: ldr             x4, [x4, #0xaa0]
    // 0xa659f4: r0 = copyWith()
    //     0xa659f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa659f8: stur            x0, [fp, #-0x30]
    // 0xa659fc: r0 = Text()
    //     0xa659fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa65a00: mov             x2, x0
    // 0xa65a04: ldur            x0, [fp, #-0x18]
    // 0xa65a08: stur            x2, [fp, #-0x38]
    // 0xa65a0c: StoreField: r2->field_b = r0
    //     0xa65a0c: stur            w0, [x2, #0xb]
    // 0xa65a10: ldur            x0, [fp, #-0x30]
    // 0xa65a14: StoreField: r2->field_13 = r0
    //     0xa65a14: stur            w0, [x2, #0x13]
    // 0xa65a18: ldur            x0, [fp, #-0x20]
    // 0xa65a1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa65a1c: ldur            w1, [x0, #0x17]
    // 0xa65a20: DecompressPointer r1
    //     0xa65a20: add             x1, x1, HEAP, lsl #32
    // 0xa65a24: r0 = of()
    //     0xa65a24: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa65a28: LoadField: r1 = r0->field_87
    //     0xa65a28: ldur            w1, [x0, #0x87]
    // 0xa65a2c: DecompressPointer r1
    //     0xa65a2c: add             x1, x1, HEAP, lsl #32
    // 0xa65a30: LoadField: r0 = r1->field_2b
    //     0xa65a30: ldur            w0, [x1, #0x2b]
    // 0xa65a34: DecompressPointer r0
    //     0xa65a34: add             x0, x0, HEAP, lsl #32
    // 0xa65a38: r16 = 12.000000
    //     0xa65a38: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa65a3c: ldr             x16, [x16, #0x9e8]
    // 0xa65a40: r30 = Instance_Color
    //     0xa65a40: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa65a44: stp             lr, x16, [SP]
    // 0xa65a48: mov             x1, x0
    // 0xa65a4c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa65a4c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa65a50: ldr             x4, [x4, #0xaa0]
    // 0xa65a54: r0 = copyWith()
    //     0xa65a54: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa65a58: stur            x0, [fp, #-0x18]
    // 0xa65a5c: r0 = Text()
    //     0xa65a5c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa65a60: mov             x3, x0
    // 0xa65a64: r0 = "Photos"
    //     0xa65a64: add             x0, PP, #0x52, lsl #12  ; [pp+0x52260] "Photos"
    //     0xa65a68: ldr             x0, [x0, #0x260]
    // 0xa65a6c: stur            x3, [fp, #-0x30]
    // 0xa65a70: StoreField: r3->field_b = r0
    //     0xa65a70: stur            w0, [x3, #0xb]
    // 0xa65a74: ldur            x0, [fp, #-0x18]
    // 0xa65a78: StoreField: r3->field_13 = r0
    //     0xa65a78: stur            w0, [x3, #0x13]
    // 0xa65a7c: r1 = Null
    //     0xa65a7c: mov             x1, NULL
    // 0xa65a80: r2 = 4
    //     0xa65a80: movz            x2, #0x4
    // 0xa65a84: r0 = AllocateArray()
    //     0xa65a84: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa65a88: mov             x2, x0
    // 0xa65a8c: ldur            x0, [fp, #-0x38]
    // 0xa65a90: stur            x2, [fp, #-0x18]
    // 0xa65a94: StoreField: r2->field_f = r0
    //     0xa65a94: stur            w0, [x2, #0xf]
    // 0xa65a98: ldur            x0, [fp, #-0x30]
    // 0xa65a9c: StoreField: r2->field_13 = r0
    //     0xa65a9c: stur            w0, [x2, #0x13]
    // 0xa65aa0: r1 = <Widget>
    //     0xa65aa0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa65aa4: r0 = AllocateGrowableArray()
    //     0xa65aa4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa65aa8: mov             x1, x0
    // 0xa65aac: ldur            x0, [fp, #-0x18]
    // 0xa65ab0: stur            x1, [fp, #-0x30]
    // 0xa65ab4: StoreField: r1->field_f = r0
    //     0xa65ab4: stur            w0, [x1, #0xf]
    // 0xa65ab8: r0 = 4
    //     0xa65ab8: movz            x0, #0x4
    // 0xa65abc: StoreField: r1->field_b = r0
    //     0xa65abc: stur            w0, [x1, #0xb]
    // 0xa65ac0: r0 = Column()
    //     0xa65ac0: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa65ac4: mov             x1, x0
    // 0xa65ac8: r0 = Instance_Axis
    //     0xa65ac8: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa65acc: stur            x1, [fp, #-0x18]
    // 0xa65ad0: StoreField: r1->field_f = r0
    //     0xa65ad0: stur            w0, [x1, #0xf]
    // 0xa65ad4: r0 = Instance_MainAxisAlignment
    //     0xa65ad4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xa65ad8: ldr             x0, [x0, #0xab0]
    // 0xa65adc: StoreField: r1->field_13 = r0
    //     0xa65adc: stur            w0, [x1, #0x13]
    // 0xa65ae0: r0 = Instance_MainAxisSize
    //     0xa65ae0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa65ae4: ldr             x0, [x0, #0xa10]
    // 0xa65ae8: ArrayStore: r1[0] = r0  ; List_4
    //     0xa65ae8: stur            w0, [x1, #0x17]
    // 0xa65aec: r2 = Instance_CrossAxisAlignment
    //     0xa65aec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa65af0: ldr             x2, [x2, #0xa18]
    // 0xa65af4: StoreField: r1->field_1b = r2
    //     0xa65af4: stur            w2, [x1, #0x1b]
    // 0xa65af8: r3 = Instance_VerticalDirection
    //     0xa65af8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa65afc: ldr             x3, [x3, #0xa20]
    // 0xa65b00: StoreField: r1->field_23 = r3
    //     0xa65b00: stur            w3, [x1, #0x23]
    // 0xa65b04: r4 = Instance_Clip
    //     0xa65b04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa65b08: ldr             x4, [x4, #0x38]
    // 0xa65b0c: StoreField: r1->field_2b = r4
    //     0xa65b0c: stur            w4, [x1, #0x2b]
    // 0xa65b10: StoreField: r1->field_2f = rZR
    //     0xa65b10: stur            xzr, [x1, #0x2f]
    // 0xa65b14: ldur            x5, [fp, #-0x30]
    // 0xa65b18: StoreField: r1->field_b = r5
    //     0xa65b18: stur            w5, [x1, #0xb]
    // 0xa65b1c: r0 = Container()
    //     0xa65b1c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa65b20: stur            x0, [fp, #-0x30]
    // 0xa65b24: r16 = 64.000000
    //     0xa65b24: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa65b28: ldr             x16, [x16, #0x838]
    // 0xa65b2c: r30 = 64.000000
    //     0xa65b2c: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa65b30: ldr             lr, [lr, #0x838]
    // 0xa65b34: stp             lr, x16, [SP, #0x10]
    // 0xa65b38: ldur            x16, [fp, #-0x28]
    // 0xa65b3c: ldur            lr, [fp, #-0x18]
    // 0xa65b40: stp             lr, x16, [SP]
    // 0xa65b44: mov             x1, x0
    // 0xa65b48: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xa65b48: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xa65b4c: ldr             x4, [x4, #0x870]
    // 0xa65b50: r0 = Container()
    //     0xa65b50: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa65b54: r0 = GestureDetector()
    //     0xa65b54: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa65b58: ldur            x2, [fp, #-0x20]
    // 0xa65b5c: r1 = Function '<anonymous closure>':.
    //     0xa65b5c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a390] AnonymousClosure: (0xa65d50), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRowWithMore (0xa657a8)
    //     0xa65b60: ldr             x1, [x1, #0x390]
    // 0xa65b64: stur            x0, [fp, #-0x18]
    // 0xa65b68: r0 = AllocateClosure()
    //     0xa65b68: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa65b6c: ldur            x16, [fp, #-0x30]
    // 0xa65b70: stp             x16, x0, [SP]
    // 0xa65b74: ldur            x1, [fp, #-0x18]
    // 0xa65b78: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa65b78: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa65b7c: ldr             x4, [x4, #0xaf0]
    // 0xa65b80: r0 = GestureDetector()
    //     0xa65b80: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa65b84: r1 = Null
    //     0xa65b84: mov             x1, NULL
    // 0xa65b88: r2 = 6
    //     0xa65b88: movz            x2, #0x6
    // 0xa65b8c: r0 = AllocateArray()
    //     0xa65b8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa65b90: mov             x2, x0
    // 0xa65b94: ldur            x0, [fp, #-0x10]
    // 0xa65b98: stur            x2, [fp, #-0x20]
    // 0xa65b9c: StoreField: r2->field_f = r0
    //     0xa65b9c: stur            w0, [x2, #0xf]
    // 0xa65ba0: ldur            x0, [fp, #-8]
    // 0xa65ba4: StoreField: r2->field_13 = r0
    //     0xa65ba4: stur            w0, [x2, #0x13]
    // 0xa65ba8: ldur            x0, [fp, #-0x18]
    // 0xa65bac: ArrayStore: r2[0] = r0  ; List_4
    //     0xa65bac: stur            w0, [x2, #0x17]
    // 0xa65bb0: r1 = <Widget>
    //     0xa65bb0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa65bb4: r0 = AllocateGrowableArray()
    //     0xa65bb4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa65bb8: mov             x1, x0
    // 0xa65bbc: ldur            x0, [fp, #-0x20]
    // 0xa65bc0: stur            x1, [fp, #-8]
    // 0xa65bc4: StoreField: r1->field_f = r0
    //     0xa65bc4: stur            w0, [x1, #0xf]
    // 0xa65bc8: r0 = 6
    //     0xa65bc8: movz            x0, #0x6
    // 0xa65bcc: StoreField: r1->field_b = r0
    //     0xa65bcc: stur            w0, [x1, #0xb]
    // 0xa65bd0: r0 = Row()
    //     0xa65bd0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xa65bd4: r1 = Instance_Axis
    //     0xa65bd4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa65bd8: StoreField: r0->field_f = r1
    //     0xa65bd8: stur            w1, [x0, #0xf]
    // 0xa65bdc: r1 = Instance_MainAxisAlignment
    //     0xa65bdc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa65be0: ldr             x1, [x1, #0xa08]
    // 0xa65be4: StoreField: r0->field_13 = r1
    //     0xa65be4: stur            w1, [x0, #0x13]
    // 0xa65be8: r1 = Instance_MainAxisSize
    //     0xa65be8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa65bec: ldr             x1, [x1, #0xa10]
    // 0xa65bf0: ArrayStore: r0[0] = r1  ; List_4
    //     0xa65bf0: stur            w1, [x0, #0x17]
    // 0xa65bf4: r1 = Instance_CrossAxisAlignment
    //     0xa65bf4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa65bf8: ldr             x1, [x1, #0xa18]
    // 0xa65bfc: StoreField: r0->field_1b = r1
    //     0xa65bfc: stur            w1, [x0, #0x1b]
    // 0xa65c00: r1 = Instance_VerticalDirection
    //     0xa65c00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa65c04: ldr             x1, [x1, #0xa20]
    // 0xa65c08: StoreField: r0->field_23 = r1
    //     0xa65c08: stur            w1, [x0, #0x23]
    // 0xa65c0c: r1 = Instance_Clip
    //     0xa65c0c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa65c10: ldr             x1, [x1, #0x38]
    // 0xa65c14: StoreField: r0->field_2b = r1
    //     0xa65c14: stur            w1, [x0, #0x2b]
    // 0xa65c18: StoreField: r0->field_2f = rZR
    //     0xa65c18: stur            xzr, [x0, #0x2f]
    // 0xa65c1c: ldur            x1, [fp, #-8]
    // 0xa65c20: StoreField: r0->field_b = r1
    //     0xa65c20: stur            w1, [x0, #0xb]
    // 0xa65c24: LeaveFrame
    //     0xa65c24: mov             SP, fp
    //     0xa65c28: ldp             fp, lr, [SP], #0x10
    // 0xa65c2c: ret
    //     0xa65c2c: ret             
    // 0xa65c30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65c30: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65c34: b               #0xa657cc
  }
  _ _buildImageThumbnail(/* No info */) {
    // ** addr: 0xa65c38, size: 0x118
    // 0xa65c38: EnterFrame
    //     0xa65c38: stp             fp, lr, [SP, #-0x10]!
    //     0xa65c3c: mov             fp, SP
    // 0xa65c40: AllocStack(0x40)
    //     0xa65c40: sub             SP, SP, #0x40
    // 0xa65c44: SetupParameters(dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2 */)
    //     0xa65c44: mov             x0, x2
    //     0xa65c48: mov             x2, x3
    // 0xa65c4c: CheckStackOverflow
    //     0xa65c4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65c50: cmp             SP, x16
    //     0xa65c54: b.ls            #0xa65d44
    // 0xa65c58: LoadField: r3 = r0->field_ab
    //     0xa65c58: ldur            w3, [x0, #0xab]
    // 0xa65c5c: DecompressPointer r3
    //     0xa65c5c: add             x3, x3, HEAP, lsl #32
    // 0xa65c60: cmp             w3, NULL
    // 0xa65c64: b.ne            #0xa65c70
    // 0xa65c68: r0 = Null
    //     0xa65c68: mov             x0, NULL
    // 0xa65c6c: b               #0xa65cc0
    // 0xa65c70: LoadField: r0 = r3->field_b
    //     0xa65c70: ldur            w0, [x3, #0xb]
    // 0xa65c74: r1 = LoadInt32Instr(r0)
    //     0xa65c74: sbfx            x1, x0, #1, #0x1f
    // 0xa65c78: mov             x0, x1
    // 0xa65c7c: mov             x1, x2
    // 0xa65c80: cmp             x1, x0
    // 0xa65c84: b.hs            #0xa65d4c
    // 0xa65c88: LoadField: r0 = r3->field_f
    //     0xa65c88: ldur            w0, [x3, #0xf]
    // 0xa65c8c: DecompressPointer r0
    //     0xa65c8c: add             x0, x0, HEAP, lsl #32
    // 0xa65c90: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xa65c90: add             x16, x0, x2, lsl #2
    //     0xa65c94: ldur            w1, [x16, #0xf]
    // 0xa65c98: DecompressPointer r1
    //     0xa65c98: add             x1, x1, HEAP, lsl #32
    // 0xa65c9c: LoadField: r0 = r1->field_7
    //     0xa65c9c: ldur            w0, [x1, #7]
    // 0xa65ca0: DecompressPointer r0
    //     0xa65ca0: add             x0, x0, HEAP, lsl #32
    // 0xa65ca4: cmp             w0, NULL
    // 0xa65ca8: b.ne            #0xa65cb4
    // 0xa65cac: r0 = Null
    //     0xa65cac: mov             x0, NULL
    // 0xa65cb0: b               #0xa65cc0
    // 0xa65cb4: LoadField: r1 = r0->field_b
    //     0xa65cb4: ldur            w1, [x0, #0xb]
    // 0xa65cb8: DecompressPointer r1
    //     0xa65cb8: add             x1, x1, HEAP, lsl #32
    // 0xa65cbc: mov             x0, x1
    // 0xa65cc0: cmp             w0, NULL
    // 0xa65cc4: b.ne            #0xa65ccc
    // 0xa65cc8: r0 = ""
    //     0xa65cc8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa65ccc: stur            x0, [fp, #-8]
    // 0xa65cd0: r1 = Function '<anonymous closure>':.
    //     0xa65cd0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3a0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa65cd4: ldr             x1, [x1, #0x3a0]
    // 0xa65cd8: r2 = Null
    //     0xa65cd8: mov             x2, NULL
    // 0xa65cdc: r0 = AllocateClosure()
    //     0xa65cdc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa65ce0: r1 = Function '<anonymous closure>':.
    //     0xa65ce0: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3a8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa65ce4: ldr             x1, [x1, #0x3a8]
    // 0xa65ce8: r2 = Null
    //     0xa65ce8: mov             x2, NULL
    // 0xa65cec: stur            x0, [fp, #-0x10]
    // 0xa65cf0: r0 = AllocateClosure()
    //     0xa65cf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa65cf4: stur            x0, [fp, #-0x18]
    // 0xa65cf8: r0 = CachedNetworkImage()
    //     0xa65cf8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa65cfc: stur            x0, [fp, #-0x20]
    // 0xa65d00: r16 = 64.000000
    //     0xa65d00: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa65d04: ldr             x16, [x16, #0x838]
    // 0xa65d08: r30 = 64.000000
    //     0xa65d08: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa65d0c: ldr             lr, [lr, #0x838]
    // 0xa65d10: stp             lr, x16, [SP, #0x10]
    // 0xa65d14: ldur            x16, [fp, #-0x10]
    // 0xa65d18: ldur            lr, [fp, #-0x18]
    // 0xa65d1c: stp             lr, x16, [SP]
    // 0xa65d20: mov             x1, x0
    // 0xa65d24: ldur            x2, [fp, #-8]
    // 0xa65d28: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xa65d28: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xa65d2c: ldr             x4, [x4, #0x388]
    // 0xa65d30: r0 = CachedNetworkImage()
    //     0xa65d30: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa65d34: ldur            x0, [fp, #-0x20]
    // 0xa65d38: LeaveFrame
    //     0xa65d38: mov             SP, fp
    //     0xa65d3c: ldp             fp, lr, [SP], #0x10
    // 0xa65d40: ret
    //     0xa65d40: ret             
    // 0xa65d44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65d44: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65d48: b               #0xa65c58
    // 0xa65d4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa65d4c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa65d50, size: 0x5c
    // 0xa65d50: EnterFrame
    //     0xa65d50: stp             fp, lr, [SP, #-0x10]!
    //     0xa65d54: mov             fp, SP
    // 0xa65d58: ldr             x0, [fp, #0x10]
    // 0xa65d5c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa65d5c: ldur            w1, [x0, #0x17]
    // 0xa65d60: DecompressPointer r1
    //     0xa65d60: add             x1, x1, HEAP, lsl #32
    // 0xa65d64: CheckStackOverflow
    //     0xa65d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65d68: cmp             SP, x16
    //     0xa65d6c: b.ls            #0xa65da4
    // 0xa65d70: LoadField: r0 = r1->field_f
    //     0xa65d70: ldur            w0, [x1, #0xf]
    // 0xa65d74: DecompressPointer r0
    //     0xa65d74: add             x0, x0, HEAP, lsl #32
    // 0xa65d78: LoadField: r2 = r1->field_13
    //     0xa65d78: ldur            w2, [x1, #0x13]
    // 0xa65d7c: DecompressPointer r2
    //     0xa65d7c: add             x2, x2, HEAP, lsl #32
    // 0xa65d80: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xa65d80: ldur            w5, [x1, #0x17]
    // 0xa65d84: DecompressPointer r5
    //     0xa65d84: add             x5, x5, HEAP, lsl #32
    // 0xa65d88: mov             x1, x0
    // 0xa65d8c: r3 = 2
    //     0xa65d8c: movz            x3, #0x2
    // 0xa65d90: r0 = _openImageViewer()
    //     0xa65d90: bl              #0xa65dac  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xa65d94: r0 = Null
    //     0xa65d94: mov             x0, NULL
    // 0xa65d98: LeaveFrame
    //     0xa65d98: mov             SP, fp
    //     0xa65d9c: ldp             fp, lr, [SP], #0x10
    // 0xa65da0: ret
    //     0xa65da0: ret             
    // 0xa65da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65da4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65da8: b               #0xa65d70
  }
  _ _openImageViewer(/* No info */) {
    // ** addr: 0xa65dac, size: 0xd0
    // 0xa65dac: EnterFrame
    //     0xa65dac: stp             fp, lr, [SP, #-0x10]!
    //     0xa65db0: mov             fp, SP
    // 0xa65db4: AllocStack(0x38)
    //     0xa65db4: sub             SP, SP, #0x38
    // 0xa65db8: SetupParameters(_TestimonialCarouselState this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r1, fp-0x18 */)
    //     0xa65db8: mov             x0, x1
    //     0xa65dbc: mov             x1, x5
    //     0xa65dc0: stur            x2, [fp, #-8]
    //     0xa65dc4: stur            x3, [fp, #-0x10]
    //     0xa65dc8: stur            x5, [fp, #-0x18]
    // 0xa65dcc: CheckStackOverflow
    //     0xa65dcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65dd0: cmp             SP, x16
    //     0xa65dd4: b.ls            #0xa65e74
    // 0xa65dd8: r1 = 2
    //     0xa65dd8: movz            x1, #0x2
    // 0xa65ddc: r0 = AllocateContext()
    //     0xa65ddc: bl              #0x16f6108  ; AllocateContextStub
    // 0xa65de0: mov             x2, x0
    // 0xa65de4: ldur            x0, [fp, #-8]
    // 0xa65de8: stur            x2, [fp, #-0x20]
    // 0xa65dec: StoreField: r2->field_f = r0
    //     0xa65dec: stur            w0, [x2, #0xf]
    // 0xa65df0: ldur            x3, [fp, #-0x10]
    // 0xa65df4: r0 = BoxInt64Instr(r3)
    //     0xa65df4: sbfiz           x0, x3, #1, #0x1f
    //     0xa65df8: cmp             x3, x0, asr #1
    //     0xa65dfc: b.eq            #0xa65e08
    //     0xa65e00: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa65e04: stur            x3, [x0, #7]
    // 0xa65e08: StoreField: r2->field_13 = r0
    //     0xa65e08: stur            w0, [x2, #0x13]
    // 0xa65e0c: ldur            x1, [fp, #-0x18]
    // 0xa65e10: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa65e10: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa65e14: r0 = of()
    //     0xa65e14: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xa65e18: ldur            x2, [fp, #-0x20]
    // 0xa65e1c: r1 = Function '<anonymous closure>':.
    //     0xa65e1c: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a398] AnonymousClosure: (0xa65e7c), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer (0xa65dac)
    //     0xa65e20: ldr             x1, [x1, #0x398]
    // 0xa65e24: stur            x0, [fp, #-8]
    // 0xa65e28: r0 = AllocateClosure()
    //     0xa65e28: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa65e2c: r1 = Null
    //     0xa65e2c: mov             x1, NULL
    // 0xa65e30: stur            x0, [fp, #-0x18]
    // 0xa65e34: r0 = MaterialPageRoute()
    //     0xa65e34: bl              #0x8fefd8  ; AllocateMaterialPageRouteStub -> MaterialPageRoute<X0> (size=0xac)
    // 0xa65e38: mov             x1, x0
    // 0xa65e3c: ldur            x2, [fp, #-0x18]
    // 0xa65e40: stur            x0, [fp, #-0x18]
    // 0xa65e44: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa65e44: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa65e48: r0 = MaterialPageRoute()
    //     0xa65e48: bl              #0x8feed4  ; [package:flutter/src/material/page.dart] MaterialPageRoute::MaterialPageRoute
    // 0xa65e4c: ldur            x16, [fp, #-8]
    // 0xa65e50: stp             x16, NULL, [SP, #8]
    // 0xa65e54: ldur            x16, [fp, #-0x18]
    // 0xa65e58: str             x16, [SP]
    // 0xa65e5c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xa65e5c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xa65e60: r0 = push()
    //     0xa65e60: bl              #0x688940  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0xa65e64: r0 = Null
    //     0xa65e64: mov             x0, NULL
    // 0xa65e68: LeaveFrame
    //     0xa65e68: mov             SP, fp
    //     0xa65e6c: ldp             fp, lr, [SP], #0x10
    // 0xa65e70: ret
    //     0xa65e70: ret             
    // 0xa65e74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65e74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65e78: b               #0xa65dd8
  }
  [closure] TestimonialMoreImagesWidget <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xa65e7c, size: 0x64
    // 0xa65e7c: EnterFrame
    //     0xa65e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xa65e80: mov             fp, SP
    // 0xa65e84: AllocStack(0x10)
    //     0xa65e84: sub             SP, SP, #0x10
    // 0xa65e88: SetupParameters()
    //     0xa65e88: ldr             x0, [fp, #0x18]
    //     0xa65e8c: ldur            w1, [x0, #0x17]
    //     0xa65e90: add             x1, x1, HEAP, lsl #32
    // 0xa65e94: LoadField: r0 = r1->field_13
    //     0xa65e94: ldur            w0, [x1, #0x13]
    // 0xa65e98: DecompressPointer r0
    //     0xa65e98: add             x0, x0, HEAP, lsl #32
    // 0xa65e9c: stur            x0, [fp, #-0x10]
    // 0xa65ea0: LoadField: r2 = r1->field_f
    //     0xa65ea0: ldur            w2, [x1, #0xf]
    // 0xa65ea4: DecompressPointer r2
    //     0xa65ea4: add             x2, x2, HEAP, lsl #32
    // 0xa65ea8: LoadField: r1 = r2->field_ab
    //     0xa65ea8: ldur            w1, [x2, #0xab]
    // 0xa65eac: DecompressPointer r1
    //     0xa65eac: add             x1, x1, HEAP, lsl #32
    // 0xa65eb0: stur            x1, [fp, #-8]
    // 0xa65eb4: r0 = TestimonialMoreImagesWidget()
    //     0xa65eb4: bl              #0xa65ee0  ; AllocateTestimonialMoreImagesWidgetStub -> TestimonialMoreImagesWidget (size=0x1c)
    // 0xa65eb8: ldur            x1, [fp, #-8]
    // 0xa65ebc: StoreField: r0->field_b = r1
    //     0xa65ebc: stur            w1, [x0, #0xb]
    // 0xa65ec0: ldur            x1, [fp, #-0x10]
    // 0xa65ec4: r2 = LoadInt32Instr(r1)
    //     0xa65ec4: sbfx            x2, x1, #1, #0x1f
    //     0xa65ec8: tbz             w1, #0, #0xa65ed0
    //     0xa65ecc: ldur            x2, [x1, #7]
    // 0xa65ed0: StoreField: r0->field_f = r2
    //     0xa65ed0: stur            x2, [x0, #0xf]
    // 0xa65ed4: LeaveFrame
    //     0xa65ed4: mov             SP, fp
    //     0xa65ed8: ldp             fp, lr, [SP], #0x10
    // 0xa65edc: ret
    //     0xa65edc: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa65f10, size: 0x5c
    // 0xa65f10: EnterFrame
    //     0xa65f10: stp             fp, lr, [SP, #-0x10]!
    //     0xa65f14: mov             fp, SP
    // 0xa65f18: ldr             x0, [fp, #0x10]
    // 0xa65f1c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa65f1c: ldur            w1, [x0, #0x17]
    // 0xa65f20: DecompressPointer r1
    //     0xa65f20: add             x1, x1, HEAP, lsl #32
    // 0xa65f24: CheckStackOverflow
    //     0xa65f24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65f28: cmp             SP, x16
    //     0xa65f2c: b.ls            #0xa65f64
    // 0xa65f30: LoadField: r0 = r1->field_f
    //     0xa65f30: ldur            w0, [x1, #0xf]
    // 0xa65f34: DecompressPointer r0
    //     0xa65f34: add             x0, x0, HEAP, lsl #32
    // 0xa65f38: LoadField: r2 = r1->field_13
    //     0xa65f38: ldur            w2, [x1, #0x13]
    // 0xa65f3c: DecompressPointer r2
    //     0xa65f3c: add             x2, x2, HEAP, lsl #32
    // 0xa65f40: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xa65f40: ldur            w5, [x1, #0x17]
    // 0xa65f44: DecompressPointer r5
    //     0xa65f44: add             x5, x5, HEAP, lsl #32
    // 0xa65f48: mov             x1, x0
    // 0xa65f4c: r3 = 1
    //     0xa65f4c: movz            x3, #0x1
    // 0xa65f50: r0 = _openImageViewer()
    //     0xa65f50: bl              #0xa65dac  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xa65f54: r0 = Null
    //     0xa65f54: mov             x0, NULL
    // 0xa65f58: LeaveFrame
    //     0xa65f58: mov             SP, fp
    //     0xa65f5c: ldp             fp, lr, [SP], #0x10
    // 0xa65f60: ret
    //     0xa65f60: ret             
    // 0xa65f64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65f64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65f68: b               #0xa65f30
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa65f6c, size: 0x5c
    // 0xa65f6c: EnterFrame
    //     0xa65f6c: stp             fp, lr, [SP, #-0x10]!
    //     0xa65f70: mov             fp, SP
    // 0xa65f74: ldr             x0, [fp, #0x10]
    // 0xa65f78: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa65f78: ldur            w1, [x0, #0x17]
    // 0xa65f7c: DecompressPointer r1
    //     0xa65f7c: add             x1, x1, HEAP, lsl #32
    // 0xa65f80: CheckStackOverflow
    //     0xa65f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65f84: cmp             SP, x16
    //     0xa65f88: b.ls            #0xa65fc0
    // 0xa65f8c: LoadField: r0 = r1->field_f
    //     0xa65f8c: ldur            w0, [x1, #0xf]
    // 0xa65f90: DecompressPointer r0
    //     0xa65f90: add             x0, x0, HEAP, lsl #32
    // 0xa65f94: LoadField: r2 = r1->field_13
    //     0xa65f94: ldur            w2, [x1, #0x13]
    // 0xa65f98: DecompressPointer r2
    //     0xa65f98: add             x2, x2, HEAP, lsl #32
    // 0xa65f9c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xa65f9c: ldur            w5, [x1, #0x17]
    // 0xa65fa0: DecompressPointer r5
    //     0xa65fa0: add             x5, x5, HEAP, lsl #32
    // 0xa65fa4: mov             x1, x0
    // 0xa65fa8: r3 = 0
    //     0xa65fa8: movz            x3, #0
    // 0xa65fac: r0 = _openImageViewer()
    //     0xa65fac: bl              #0xa65dac  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xa65fb0: r0 = Null
    //     0xa65fb0: mov             x0, NULL
    // 0xa65fb4: LeaveFrame
    //     0xa65fb4: mov             SP, fp
    //     0xa65fb8: ldp             fp, lr, [SP], #0x10
    // 0xa65fbc: ret
    //     0xa65fbc: ret             
    // 0xa65fc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa65fc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa65fc4: b               #0xa65f8c
  }
  _ _buildImagesRow(/* No info */) {
    // ** addr: 0xa65fc8, size: 0xdc
    // 0xa65fc8: EnterFrame
    //     0xa65fc8: stp             fp, lr, [SP, #-0x10]!
    //     0xa65fcc: mov             fp, SP
    // 0xa65fd0: AllocStack(0x30)
    //     0xa65fd0: sub             SP, SP, #0x30
    // 0xa65fd4: SetupParameters(_TestimonialCarouselState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa65fd4: stur            x1, [fp, #-8]
    //     0xa65fd8: stur            x2, [fp, #-0x10]
    // 0xa65fdc: CheckStackOverflow
    //     0xa65fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa65fe0: cmp             SP, x16
    //     0xa65fe4: b.ls            #0xa6609c
    // 0xa65fe8: r1 = 2
    //     0xa65fe8: movz            x1, #0x2
    // 0xa65fec: r0 = AllocateContext()
    //     0xa65fec: bl              #0x16f6108  ; AllocateContextStub
    // 0xa65ff0: mov             x1, x0
    // 0xa65ff4: ldur            x0, [fp, #-8]
    // 0xa65ff8: StoreField: r1->field_f = r0
    //     0xa65ff8: stur            w0, [x1, #0xf]
    // 0xa65ffc: ldur            x0, [fp, #-0x10]
    // 0xa66000: StoreField: r1->field_13 = r0
    //     0xa66000: stur            w0, [x1, #0x13]
    // 0xa66004: LoadField: r2 = r0->field_ab
    //     0xa66004: ldur            w2, [x0, #0xab]
    // 0xa66008: DecompressPointer r2
    //     0xa66008: add             x2, x2, HEAP, lsl #32
    // 0xa6600c: cmp             w2, NULL
    // 0xa66010: b.ne            #0xa6601c
    // 0xa66014: r0 = Null
    //     0xa66014: mov             x0, NULL
    // 0xa66018: b               #0xa66020
    // 0xa6601c: LoadField: r0 = r2->field_b
    //     0xa6601c: ldur            w0, [x2, #0xb]
    // 0xa66020: cmp             w0, NULL
    // 0xa66024: b.ne            #0xa66030
    // 0xa66028: r0 = 0
    //     0xa66028: movz            x0, #0
    // 0xa6602c: b               #0xa66038
    // 0xa66030: r2 = LoadInt32Instr(r0)
    //     0xa66030: sbfx            x2, x0, #1, #0x1f
    // 0xa66034: mov             x0, x2
    // 0xa66038: lsl             x3, x0, #1
    // 0xa6603c: mov             x2, x1
    // 0xa66040: stur            x3, [fp, #-8]
    // 0xa66044: r1 = Function '<anonymous closure>':.
    //     0xa66044: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3b0] AnonymousClosure: (0xa660a4), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xa65fc8)
    //     0xa66048: ldr             x1, [x1, #0x3b0]
    // 0xa6604c: r0 = AllocateClosure()
    //     0xa6604c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66050: stur            x0, [fp, #-0x10]
    // 0xa66054: r0 = ListView()
    //     0xa66054: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xa66058: stur            x0, [fp, #-0x18]
    // 0xa6605c: r16 = true
    //     0xa6605c: add             x16, NULL, #0x20  ; true
    // 0xa66060: r30 = Instance_Axis
    //     0xa66060: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xa66064: stp             lr, x16, [SP, #8]
    // 0xa66068: r16 = Instance_NeverScrollableScrollPhysics
    //     0xa66068: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xa6606c: ldr             x16, [x16, #0x1c8]
    // 0xa66070: str             x16, [SP]
    // 0xa66074: mov             x1, x0
    // 0xa66078: ldur            x2, [fp, #-0x10]
    // 0xa6607c: ldur            x3, [fp, #-8]
    // 0xa66080: r4 = const [0, 0x6, 0x3, 0x3, physics, 0x5, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xa66080: add             x4, PP, #0x52, lsl #12  ; [pp+0x52200] List(11) [0, 0x6, 0x3, 0x3, "physics", 0x5, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xa66084: ldr             x4, [x4, #0x200]
    // 0xa66088: r0 = ListView.builder()
    //     0xa66088: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xa6608c: ldur            x0, [fp, #-0x18]
    // 0xa66090: LeaveFrame
    //     0xa66090: mov             SP, fp
    //     0xa66094: ldp             fp, lr, [SP], #0x10
    // 0xa66098: ret
    //     0xa66098: ret             
    // 0xa6609c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6609c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa660a0: b               #0xa65fe8
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa660a4, size: 0x1a0
    // 0xa660a4: EnterFrame
    //     0xa660a4: stp             fp, lr, [SP, #-0x10]!
    //     0xa660a8: mov             fp, SP
    // 0xa660ac: AllocStack(0x48)
    //     0xa660ac: sub             SP, SP, #0x48
    // 0xa660b0: SetupParameters()
    //     0xa660b0: ldr             x0, [fp, #0x20]
    //     0xa660b4: ldur            w1, [x0, #0x17]
    //     0xa660b8: add             x1, x1, HEAP, lsl #32
    //     0xa660bc: stur            x1, [fp, #-8]
    // 0xa660c0: CheckStackOverflow
    //     0xa660c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa660c4: cmp             SP, x16
    //     0xa660c8: b.ls            #0xa66238
    // 0xa660cc: r1 = 2
    //     0xa660cc: movz            x1, #0x2
    // 0xa660d0: r0 = AllocateContext()
    //     0xa660d0: bl              #0x16f6108  ; AllocateContextStub
    // 0xa660d4: mov             x3, x0
    // 0xa660d8: ldur            x0, [fp, #-8]
    // 0xa660dc: stur            x3, [fp, #-0x10]
    // 0xa660e0: StoreField: r3->field_b = r0
    //     0xa660e0: stur            w0, [x3, #0xb]
    // 0xa660e4: ldr             x1, [fp, #0x18]
    // 0xa660e8: StoreField: r3->field_f = r1
    //     0xa660e8: stur            w1, [x3, #0xf]
    // 0xa660ec: ldr             x1, [fp, #0x10]
    // 0xa660f0: StoreField: r3->field_13 = r1
    //     0xa660f0: stur            w1, [x3, #0x13]
    // 0xa660f4: LoadField: r2 = r0->field_13
    //     0xa660f4: ldur            w2, [x0, #0x13]
    // 0xa660f8: DecompressPointer r2
    //     0xa660f8: add             x2, x2, HEAP, lsl #32
    // 0xa660fc: LoadField: r4 = r2->field_ab
    //     0xa660fc: ldur            w4, [x2, #0xab]
    // 0xa66100: DecompressPointer r4
    //     0xa66100: add             x4, x4, HEAP, lsl #32
    // 0xa66104: cmp             w4, NULL
    // 0xa66108: b.ne            #0xa66114
    // 0xa6610c: r0 = Null
    //     0xa6610c: mov             x0, NULL
    // 0xa66110: b               #0xa66170
    // 0xa66114: LoadField: r0 = r4->field_b
    //     0xa66114: ldur            w0, [x4, #0xb]
    // 0xa66118: r2 = LoadInt32Instr(r1)
    //     0xa66118: sbfx            x2, x1, #1, #0x1f
    //     0xa6611c: tbz             w1, #0, #0xa66124
    //     0xa66120: ldur            x2, [x1, #7]
    // 0xa66124: r1 = LoadInt32Instr(r0)
    //     0xa66124: sbfx            x1, x0, #1, #0x1f
    // 0xa66128: mov             x0, x1
    // 0xa6612c: mov             x1, x2
    // 0xa66130: cmp             x1, x0
    // 0xa66134: b.hs            #0xa66240
    // 0xa66138: LoadField: r0 = r4->field_f
    //     0xa66138: ldur            w0, [x4, #0xf]
    // 0xa6613c: DecompressPointer r0
    //     0xa6613c: add             x0, x0, HEAP, lsl #32
    // 0xa66140: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xa66140: add             x16, x0, x2, lsl #2
    //     0xa66144: ldur            w1, [x16, #0xf]
    // 0xa66148: DecompressPointer r1
    //     0xa66148: add             x1, x1, HEAP, lsl #32
    // 0xa6614c: LoadField: r0 = r1->field_7
    //     0xa6614c: ldur            w0, [x1, #7]
    // 0xa66150: DecompressPointer r0
    //     0xa66150: add             x0, x0, HEAP, lsl #32
    // 0xa66154: cmp             w0, NULL
    // 0xa66158: b.ne            #0xa66164
    // 0xa6615c: r0 = Null
    //     0xa6615c: mov             x0, NULL
    // 0xa66160: b               #0xa66170
    // 0xa66164: LoadField: r1 = r0->field_b
    //     0xa66164: ldur            w1, [x0, #0xb]
    // 0xa66168: DecompressPointer r1
    //     0xa66168: add             x1, x1, HEAP, lsl #32
    // 0xa6616c: mov             x0, x1
    // 0xa66170: cmp             w0, NULL
    // 0xa66174: b.ne            #0xa6617c
    // 0xa66178: r0 = ""
    //     0xa66178: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa6617c: stur            x0, [fp, #-8]
    // 0xa66180: r1 = Function '<anonymous closure>':.
    //     0xa66180: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3b8] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa66184: ldr             x1, [x1, #0x3b8]
    // 0xa66188: r2 = Null
    //     0xa66188: mov             x2, NULL
    // 0xa6618c: r0 = AllocateClosure()
    //     0xa6618c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66190: r1 = Function '<anonymous closure>':.
    //     0xa66190: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3c0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa66194: ldr             x1, [x1, #0x3c0]
    // 0xa66198: r2 = Null
    //     0xa66198: mov             x2, NULL
    // 0xa6619c: stur            x0, [fp, #-0x18]
    // 0xa661a0: r0 = AllocateClosure()
    //     0xa661a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa661a4: stur            x0, [fp, #-0x20]
    // 0xa661a8: r0 = CachedNetworkImage()
    //     0xa661a8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa661ac: stur            x0, [fp, #-0x28]
    // 0xa661b0: r16 = 64.000000
    //     0xa661b0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa661b4: ldr             x16, [x16, #0x838]
    // 0xa661b8: r30 = 64.000000
    //     0xa661b8: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa661bc: ldr             lr, [lr, #0x838]
    // 0xa661c0: stp             lr, x16, [SP, #0x10]
    // 0xa661c4: ldur            x16, [fp, #-0x18]
    // 0xa661c8: ldur            lr, [fp, #-0x20]
    // 0xa661cc: stp             lr, x16, [SP]
    // 0xa661d0: mov             x1, x0
    // 0xa661d4: ldur            x2, [fp, #-8]
    // 0xa661d8: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xa661d8: add             x4, PP, #0x40, lsl #12  ; [pp+0x40388] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xa661dc: ldr             x4, [x4, #0x388]
    // 0xa661e0: r0 = CachedNetworkImage()
    //     0xa661e0: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa661e4: r0 = GestureDetector()
    //     0xa661e4: bl              #0x85c468  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xa661e8: ldur            x2, [fp, #-0x10]
    // 0xa661ec: r1 = Function '<anonymous closure>':.
    //     0xa661ec: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3c8] AnonymousClosure: (0xa66244), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_buildImagesRow (0xa65fc8)
    //     0xa661f0: ldr             x1, [x1, #0x3c8]
    // 0xa661f4: stur            x0, [fp, #-8]
    // 0xa661f8: r0 = AllocateClosure()
    //     0xa661f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa661fc: ldur            x16, [fp, #-0x28]
    // 0xa66200: stp             x16, x0, [SP]
    // 0xa66204: ldur            x1, [fp, #-8]
    // 0xa66208: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xa66208: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2faf0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xa6620c: ldr             x4, [x4, #0xaf0]
    // 0xa66210: r0 = GestureDetector()
    //     0xa66210: bl              #0x85bc28  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xa66214: r0 = Padding()
    //     0xa66214: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa66218: r1 = Instance_EdgeInsets
    //     0xa66218: add             x1, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!EdgeInsets@d587c1
    //     0xa6621c: ldr             x1, [x1, #0x550]
    // 0xa66220: StoreField: r0->field_f = r1
    //     0xa66220: stur            w1, [x0, #0xf]
    // 0xa66224: ldur            x1, [fp, #-8]
    // 0xa66228: StoreField: r0->field_b = r1
    //     0xa66228: stur            w1, [x0, #0xb]
    // 0xa6622c: LeaveFrame
    //     0xa6622c: mov             SP, fp
    //     0xa66230: ldp             fp, lr, [SP], #0x10
    // 0xa66234: ret
    //     0xa66234: ret             
    // 0xa66238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66238: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa6623c: b               #0xa660cc
    // 0xa66240: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa66240: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa66244, size: 0x88
    // 0xa66244: EnterFrame
    //     0xa66244: stp             fp, lr, [SP, #-0x10]!
    //     0xa66248: mov             fp, SP
    // 0xa6624c: ldr             x0, [fp, #0x10]
    // 0xa66250: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa66250: ldur            w1, [x0, #0x17]
    // 0xa66254: DecompressPointer r1
    //     0xa66254: add             x1, x1, HEAP, lsl #32
    // 0xa66258: CheckStackOverflow
    //     0xa66258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6625c: cmp             SP, x16
    //     0xa66260: b.ls            #0xa662c4
    // 0xa66264: LoadField: r0 = r1->field_b
    //     0xa66264: ldur            w0, [x1, #0xb]
    // 0xa66268: DecompressPointer r0
    //     0xa66268: add             x0, x0, HEAP, lsl #32
    // 0xa6626c: LoadField: r2 = r0->field_f
    //     0xa6626c: ldur            w2, [x0, #0xf]
    // 0xa66270: DecompressPointer r2
    //     0xa66270: add             x2, x2, HEAP, lsl #32
    // 0xa66274: LoadField: r3 = r0->field_13
    //     0xa66274: ldur            w3, [x0, #0x13]
    // 0xa66278: DecompressPointer r3
    //     0xa66278: add             x3, x3, HEAP, lsl #32
    // 0xa6627c: LoadField: r0 = r1->field_13
    //     0xa6627c: ldur            w0, [x1, #0x13]
    // 0xa66280: DecompressPointer r0
    //     0xa66280: add             x0, x0, HEAP, lsl #32
    // 0xa66284: LoadField: r5 = r1->field_f
    //     0xa66284: ldur            w5, [x1, #0xf]
    // 0xa66288: DecompressPointer r5
    //     0xa66288: add             x5, x5, HEAP, lsl #32
    // 0xa6628c: r1 = LoadInt32Instr(r0)
    //     0xa6628c: sbfx            x1, x0, #1, #0x1f
    //     0xa66290: tbz             w0, #0, #0xa66298
    //     0xa66294: ldur            x1, [x0, #7]
    // 0xa66298: mov             x16, x3
    // 0xa6629c: mov             x3, x2
    // 0xa662a0: mov             x2, x16
    // 0xa662a4: mov             x16, x1
    // 0xa662a8: mov             x1, x3
    // 0xa662ac: mov             x3, x16
    // 0xa662b0: r0 = _openImageViewer()
    //     0xa662b0: bl              #0xa65dac  ; [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::_openImageViewer
    // 0xa662b4: r0 = Null
    //     0xa662b4: mov             x0, NULL
    // 0xa662b8: LeaveFrame
    //     0xa662b8: mov             SP, fp
    //     0xa662bc: ldp             fp, lr, [SP], #0x10
    // 0xa662c0: ret
    //     0xa662c0: ret             
    // 0xa662c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa662c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa662c8: b               #0xa66264
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa662cc, size: 0x8c
    // 0xa662cc: EnterFrame
    //     0xa662cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa662d0: mov             fp, SP
    // 0xa662d4: AllocStack(0x10)
    //     0xa662d4: sub             SP, SP, #0x10
    // 0xa662d8: SetupParameters()
    //     0xa662d8: ldr             x0, [fp, #0x10]
    //     0xa662dc: ldur            w2, [x0, #0x17]
    //     0xa662e0: add             x2, x2, HEAP, lsl #32
    //     0xa662e4: stur            x2, [fp, #-0x10]
    // 0xa662e8: CheckStackOverflow
    //     0xa662e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa662ec: cmp             SP, x16
    //     0xa662f0: b.ls            #0xa66350
    // 0xa662f4: LoadField: r0 = r2->field_13
    //     0xa662f4: ldur            w0, [x2, #0x13]
    // 0xa662f8: DecompressPointer r0
    //     0xa662f8: add             x0, x0, HEAP, lsl #32
    // 0xa662fc: mov             x1, x0
    // 0xa66300: stur            x0, [fp, #-8]
    // 0xa66304: r0 = value()
    //     0xa66304: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0xa66308: eor             x2, x0, #0x10
    // 0xa6630c: ldur            x1, [fp, #-8]
    // 0xa66310: r0 = value=()
    //     0xa66310: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0xa66314: ldur            x0, [fp, #-0x10]
    // 0xa66318: LoadField: r3 = r0->field_f
    //     0xa66318: ldur            w3, [x0, #0xf]
    // 0xa6631c: DecompressPointer r3
    //     0xa6631c: add             x3, x3, HEAP, lsl #32
    // 0xa66320: stur            x3, [fp, #-8]
    // 0xa66324: r1 = Function '<anonymous closure>':.
    //     0xa66324: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a378] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xa66328: ldr             x1, [x1, #0x378]
    // 0xa6632c: r2 = Null
    //     0xa6632c: mov             x2, NULL
    // 0xa66330: r0 = AllocateClosure()
    //     0xa66330: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66334: ldur            x1, [fp, #-8]
    // 0xa66338: mov             x2, x0
    // 0xa6633c: r0 = setState()
    //     0xa6633c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa66340: r0 = Null
    //     0xa66340: mov             x0, NULL
    // 0xa66344: LeaveFrame
    //     0xa66344: mov             SP, fp
    //     0xa66348: ldp             fp, lr, [SP], #0x10
    // 0xa6634c: ret
    //     0xa6634c: ret             
    // 0xa66350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66350: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66354: b               #0xa662f4
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xa66358, size: 0x84
    // 0xa66358: EnterFrame
    //     0xa66358: stp             fp, lr, [SP, #-0x10]!
    //     0xa6635c: mov             fp, SP
    // 0xa66360: AllocStack(0x10)
    //     0xa66360: sub             SP, SP, #0x10
    // 0xa66364: SetupParameters()
    //     0xa66364: ldr             x0, [fp, #0x18]
    //     0xa66368: ldur            w1, [x0, #0x17]
    //     0xa6636c: add             x1, x1, HEAP, lsl #32
    //     0xa66370: stur            x1, [fp, #-8]
    // 0xa66374: CheckStackOverflow
    //     0xa66374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66378: cmp             SP, x16
    //     0xa6637c: b.ls            #0xa663d4
    // 0xa66380: r1 = 1
    //     0xa66380: movz            x1, #0x1
    // 0xa66384: r0 = AllocateContext()
    //     0xa66384: bl              #0x16f6108  ; AllocateContextStub
    // 0xa66388: mov             x1, x0
    // 0xa6638c: ldur            x0, [fp, #-8]
    // 0xa66390: StoreField: r1->field_b = r0
    //     0xa66390: stur            w0, [x1, #0xb]
    // 0xa66394: ldr             x2, [fp, #0x10]
    // 0xa66398: StoreField: r1->field_f = r2
    //     0xa66398: stur            w2, [x1, #0xf]
    // 0xa6639c: LoadField: r3 = r0->field_f
    //     0xa6639c: ldur            w3, [x0, #0xf]
    // 0xa663a0: DecompressPointer r3
    //     0xa663a0: add             x3, x3, HEAP, lsl #32
    // 0xa663a4: mov             x2, x1
    // 0xa663a8: stur            x3, [fp, #-0x10]
    // 0xa663ac: r1 = Function '<anonymous closure>':.
    //     0xa663ac: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3d0] AnonymousClosure: (0xa663dc), in [package:customer_app/app/presentation/views/basic/home/<USER>/testimonial_carousal.dart] _TestimonialCarouselState::build (0xa62b0c)
    //     0xa663b0: ldr             x1, [x1, #0x3d0]
    // 0xa663b4: r0 = AllocateClosure()
    //     0xa663b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa663b8: ldur            x1, [fp, #-0x10]
    // 0xa663bc: mov             x2, x0
    // 0xa663c0: r0 = setState()
    //     0xa663c0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa663c4: r0 = Null
    //     0xa663c4: mov             x0, NULL
    // 0xa663c8: LeaveFrame
    //     0xa663c8: mov             SP, fp
    //     0xa663cc: ldp             fp, lr, [SP], #0x10
    // 0xa663d0: ret
    //     0xa663d0: ret             
    // 0xa663d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa663d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa663d8: b               #0xa66380
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa663dc, size: 0x8c
    // 0xa663dc: EnterFrame
    //     0xa663dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa663e0: mov             fp, SP
    // 0xa663e4: AllocStack(0x8)
    //     0xa663e4: sub             SP, SP, #8
    // 0xa663e8: SetupParameters()
    //     0xa663e8: ldr             x0, [fp, #0x10]
    //     0xa663ec: ldur            w1, [x0, #0x17]
    //     0xa663f0: add             x1, x1, HEAP, lsl #32
    // 0xa663f4: CheckStackOverflow
    //     0xa663f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa663f8: cmp             SP, x16
    //     0xa663fc: b.ls            #0xa66460
    // 0xa66400: LoadField: r0 = r1->field_b
    //     0xa66400: ldur            w0, [x1, #0xb]
    // 0xa66404: DecompressPointer r0
    //     0xa66404: add             x0, x0, HEAP, lsl #32
    // 0xa66408: LoadField: r2 = r0->field_f
    //     0xa66408: ldur            w2, [x0, #0xf]
    // 0xa6640c: DecompressPointer r2
    //     0xa6640c: add             x2, x2, HEAP, lsl #32
    // 0xa66410: LoadField: r0 = r1->field_f
    //     0xa66410: ldur            w0, [x1, #0xf]
    // 0xa66414: DecompressPointer r0
    //     0xa66414: add             x0, x0, HEAP, lsl #32
    // 0xa66418: r1 = LoadInt32Instr(r0)
    //     0xa66418: sbfx            x1, x0, #1, #0x1f
    //     0xa6641c: tbz             w0, #0, #0xa66424
    //     0xa66420: ldur            x1, [x0, #7]
    // 0xa66424: ArrayStore: r2[0] = r1  ; List_8
    //     0xa66424: stur            x1, [x2, #0x17]
    // 0xa66428: LoadField: r0 = r2->field_1f
    //     0xa66428: ldur            w0, [x2, #0x1f]
    // 0xa6642c: DecompressPointer r0
    //     0xa6642c: add             x0, x0, HEAP, lsl #32
    // 0xa66430: stur            x0, [fp, #-8]
    // 0xa66434: r1 = Function '<anonymous closure>':.
    //     0xa66434: add             x1, PP, #0x5a, lsl #12  ; [pp+0x5a3d8] AnonymousClosure: (0xa5cd5c), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_testimonial_carousel.dart] _ProductTestimonialCarouselState::build (0xc06824)
    //     0xa66438: ldr             x1, [x1, #0x3d8]
    // 0xa6643c: r2 = Null
    //     0xa6643c: mov             x2, NULL
    // 0xa66440: r0 = AllocateClosure()
    //     0xa66440: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa66444: ldur            x1, [fp, #-8]
    // 0xa66448: mov             x2, x0
    // 0xa6644c: r0 = forEach()
    //     0xa6644c: bl              #0x16878f8  ; [dart:_compact_hash] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xa66450: r0 = Null
    //     0xa66450: mov             x0, NULL
    // 0xa66454: LeaveFrame
    //     0xa66454: mov             SP, fp
    //     0xa66458: ldp             fp, lr, [SP], #0x10
    // 0xa6645c: ret
    //     0xa6645c: ret             
    // 0xa66460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66460: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66464: b               #0xa66400
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa66468, size: 0xb8
    // 0xa66468: EnterFrame
    //     0xa66468: stp             fp, lr, [SP, #-0x10]!
    //     0xa6646c: mov             fp, SP
    // 0xa66470: AllocStack(0x38)
    //     0xa66470: sub             SP, SP, #0x38
    // 0xa66474: SetupParameters()
    //     0xa66474: ldr             x0, [fp, #0x10]
    //     0xa66478: ldur            w1, [x0, #0x17]
    //     0xa6647c: add             x1, x1, HEAP, lsl #32
    // 0xa66480: CheckStackOverflow
    //     0xa66480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa66484: cmp             SP, x16
    //     0xa66488: b.ls            #0xa66514
    // 0xa6648c: LoadField: r0 = r1->field_f
    //     0xa6648c: ldur            w0, [x1, #0xf]
    // 0xa66490: DecompressPointer r0
    //     0xa66490: add             x0, x0, HEAP, lsl #32
    // 0xa66494: LoadField: r1 = r0->field_b
    //     0xa66494: ldur            w1, [x0, #0xb]
    // 0xa66498: DecompressPointer r1
    //     0xa66498: add             x1, x1, HEAP, lsl #32
    // 0xa6649c: cmp             w1, NULL
    // 0xa664a0: b.eq            #0xa6651c
    // 0xa664a4: LoadField: r0 = r1->field_1b
    //     0xa664a4: ldur            w0, [x1, #0x1b]
    // 0xa664a8: DecompressPointer r0
    //     0xa664a8: add             x0, x0, HEAP, lsl #32
    // 0xa664ac: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa664ac: ldur            w2, [x1, #0x17]
    // 0xa664b0: DecompressPointer r2
    //     0xa664b0: add             x2, x2, HEAP, lsl #32
    // 0xa664b4: LoadField: r3 = r1->field_f
    //     0xa664b4: ldur            w3, [x1, #0xf]
    // 0xa664b8: DecompressPointer r3
    //     0xa664b8: add             x3, x3, HEAP, lsl #32
    // 0xa664bc: LoadField: r4 = r1->field_23
    //     0xa664bc: ldur            w4, [x1, #0x23]
    // 0xa664c0: DecompressPointer r4
    //     0xa664c0: add             x4, x4, HEAP, lsl #32
    // 0xa664c4: LoadField: r5 = r1->field_1f
    //     0xa664c4: ldur            w5, [x1, #0x1f]
    // 0xa664c8: DecompressPointer r5
    //     0xa664c8: add             x5, x5, HEAP, lsl #32
    // 0xa664cc: LoadField: r6 = r1->field_2b
    //     0xa664cc: ldur            w6, [x1, #0x2b]
    // 0xa664d0: DecompressPointer r6
    //     0xa664d0: add             x6, x6, HEAP, lsl #32
    // 0xa664d4: LoadField: r7 = r1->field_27
    //     0xa664d4: ldur            w7, [x1, #0x27]
    // 0xa664d8: DecompressPointer r7
    //     0xa664d8: add             x7, x7, HEAP, lsl #32
    // 0xa664dc: stp             x0, x7, [SP, #0x28]
    // 0xa664e0: stp             x3, x2, [SP, #0x18]
    // 0xa664e4: stp             x5, x4, [SP, #8]
    // 0xa664e8: str             x6, [SP]
    // 0xa664ec: r4 = 0
    //     0xa664ec: movz            x4, #0
    // 0xa664f0: ldr             x0, [SP, #0x30]
    // 0xa664f4: r16 = UnlinkedCall_0x613b5c
    //     0xa664f4: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5a3e0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa664f8: add             x16, x16, #0x3e0
    // 0xa664fc: ldp             x5, lr, [x16]
    // 0xa66500: blr             lr
    // 0xa66504: r0 = Null
    //     0xa66504: mov             x0, NULL
    // 0xa66508: LeaveFrame
    //     0xa66508: mov             SP, fp
    //     0xa6650c: ldp             fp, lr, [SP], #0x10
    // 0xa66510: ret
    //     0xa66510: ret             
    // 0xa66514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66514: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66518: b               #0xa6648c
    // 0xa6651c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6651c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa66520, size: 0xb8
    // 0xa66520: EnterFrame
    //     0xa66520: stp             fp, lr, [SP, #-0x10]!
    //     0xa66524: mov             fp, SP
    // 0xa66528: AllocStack(0x38)
    //     0xa66528: sub             SP, SP, #0x38
    // 0xa6652c: SetupParameters()
    //     0xa6652c: ldr             x0, [fp, #0x10]
    //     0xa66530: ldur            w1, [x0, #0x17]
    //     0xa66534: add             x1, x1, HEAP, lsl #32
    // 0xa66538: CheckStackOverflow
    //     0xa66538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa6653c: cmp             SP, x16
    //     0xa66540: b.ls            #0xa665cc
    // 0xa66544: LoadField: r0 = r1->field_f
    //     0xa66544: ldur            w0, [x1, #0xf]
    // 0xa66548: DecompressPointer r0
    //     0xa66548: add             x0, x0, HEAP, lsl #32
    // 0xa6654c: LoadField: r1 = r0->field_b
    //     0xa6654c: ldur            w1, [x0, #0xb]
    // 0xa66550: DecompressPointer r1
    //     0xa66550: add             x1, x1, HEAP, lsl #32
    // 0xa66554: cmp             w1, NULL
    // 0xa66558: b.eq            #0xa665d4
    // 0xa6655c: LoadField: r0 = r1->field_1b
    //     0xa6655c: ldur            w0, [x1, #0x1b]
    // 0xa66560: DecompressPointer r0
    //     0xa66560: add             x0, x0, HEAP, lsl #32
    // 0xa66564: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa66564: ldur            w2, [x1, #0x17]
    // 0xa66568: DecompressPointer r2
    //     0xa66568: add             x2, x2, HEAP, lsl #32
    // 0xa6656c: LoadField: r3 = r1->field_f
    //     0xa6656c: ldur            w3, [x1, #0xf]
    // 0xa66570: DecompressPointer r3
    //     0xa66570: add             x3, x3, HEAP, lsl #32
    // 0xa66574: LoadField: r4 = r1->field_23
    //     0xa66574: ldur            w4, [x1, #0x23]
    // 0xa66578: DecompressPointer r4
    //     0xa66578: add             x4, x4, HEAP, lsl #32
    // 0xa6657c: LoadField: r5 = r1->field_1f
    //     0xa6657c: ldur            w5, [x1, #0x1f]
    // 0xa66580: DecompressPointer r5
    //     0xa66580: add             x5, x5, HEAP, lsl #32
    // 0xa66584: LoadField: r6 = r1->field_2b
    //     0xa66584: ldur            w6, [x1, #0x2b]
    // 0xa66588: DecompressPointer r6
    //     0xa66588: add             x6, x6, HEAP, lsl #32
    // 0xa6658c: LoadField: r7 = r1->field_27
    //     0xa6658c: ldur            w7, [x1, #0x27]
    // 0xa66590: DecompressPointer r7
    //     0xa66590: add             x7, x7, HEAP, lsl #32
    // 0xa66594: stp             x0, x7, [SP, #0x28]
    // 0xa66598: stp             x3, x2, [SP, #0x18]
    // 0xa6659c: stp             x5, x4, [SP, #8]
    // 0xa665a0: str             x6, [SP]
    // 0xa665a4: r4 = 0
    //     0xa665a4: movz            x4, #0
    // 0xa665a8: ldr             x0, [SP, #0x30]
    // 0xa665ac: r16 = UnlinkedCall_0x613b5c
    //     0xa665ac: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5a3f0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa665b0: add             x16, x16, #0x3f0
    // 0xa665b4: ldp             x5, lr, [x16]
    // 0xa665b8: blr             lr
    // 0xa665bc: r0 = Null
    //     0xa665bc: mov             x0, NULL
    // 0xa665c0: LeaveFrame
    //     0xa665c0: mov             SP, fp
    //     0xa665c4: ldp             fp, lr, [SP], #0x10
    // 0xa665c8: ret
    //     0xa665c8: ret             
    // 0xa665cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa665cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa665d0: b               #0xa66544
    // 0xa665d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa665d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa665d8, size: 0xb8
    // 0xa665d8: EnterFrame
    //     0xa665d8: stp             fp, lr, [SP, #-0x10]!
    //     0xa665dc: mov             fp, SP
    // 0xa665e0: AllocStack(0x38)
    //     0xa665e0: sub             SP, SP, #0x38
    // 0xa665e4: SetupParameters()
    //     0xa665e4: ldr             x0, [fp, #0x10]
    //     0xa665e8: ldur            w1, [x0, #0x17]
    //     0xa665ec: add             x1, x1, HEAP, lsl #32
    // 0xa665f0: CheckStackOverflow
    //     0xa665f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa665f4: cmp             SP, x16
    //     0xa665f8: b.ls            #0xa66684
    // 0xa665fc: LoadField: r0 = r1->field_f
    //     0xa665fc: ldur            w0, [x1, #0xf]
    // 0xa66600: DecompressPointer r0
    //     0xa66600: add             x0, x0, HEAP, lsl #32
    // 0xa66604: LoadField: r1 = r0->field_b
    //     0xa66604: ldur            w1, [x0, #0xb]
    // 0xa66608: DecompressPointer r1
    //     0xa66608: add             x1, x1, HEAP, lsl #32
    // 0xa6660c: cmp             w1, NULL
    // 0xa66610: b.eq            #0xa6668c
    // 0xa66614: LoadField: r0 = r1->field_1b
    //     0xa66614: ldur            w0, [x1, #0x1b]
    // 0xa66618: DecompressPointer r0
    //     0xa66618: add             x0, x0, HEAP, lsl #32
    // 0xa6661c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa6661c: ldur            w2, [x1, #0x17]
    // 0xa66620: DecompressPointer r2
    //     0xa66620: add             x2, x2, HEAP, lsl #32
    // 0xa66624: LoadField: r3 = r1->field_f
    //     0xa66624: ldur            w3, [x1, #0xf]
    // 0xa66628: DecompressPointer r3
    //     0xa66628: add             x3, x3, HEAP, lsl #32
    // 0xa6662c: LoadField: r4 = r1->field_23
    //     0xa6662c: ldur            w4, [x1, #0x23]
    // 0xa66630: DecompressPointer r4
    //     0xa66630: add             x4, x4, HEAP, lsl #32
    // 0xa66634: LoadField: r5 = r1->field_1f
    //     0xa66634: ldur            w5, [x1, #0x1f]
    // 0xa66638: DecompressPointer r5
    //     0xa66638: add             x5, x5, HEAP, lsl #32
    // 0xa6663c: LoadField: r6 = r1->field_2b
    //     0xa6663c: ldur            w6, [x1, #0x2b]
    // 0xa66640: DecompressPointer r6
    //     0xa66640: add             x6, x6, HEAP, lsl #32
    // 0xa66644: LoadField: r7 = r1->field_27
    //     0xa66644: ldur            w7, [x1, #0x27]
    // 0xa66648: DecompressPointer r7
    //     0xa66648: add             x7, x7, HEAP, lsl #32
    // 0xa6664c: stp             x0, x7, [SP, #0x28]
    // 0xa66650: stp             x3, x2, [SP, #0x18]
    // 0xa66654: stp             x5, x4, [SP, #8]
    // 0xa66658: str             x6, [SP]
    // 0xa6665c: r4 = 0
    //     0xa6665c: movz            x4, #0
    // 0xa66660: ldr             x0, [SP, #0x30]
    // 0xa66664: r16 = UnlinkedCall_0x613b5c
    //     0xa66664: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5a400] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa66668: add             x16, x16, #0x400
    // 0xa6666c: ldp             x5, lr, [x16]
    // 0xa66670: blr             lr
    // 0xa66674: r0 = Null
    //     0xa66674: mov             x0, NULL
    // 0xa66678: LeaveFrame
    //     0xa66678: mov             SP, fp
    //     0xa6667c: ldp             fp, lr, [SP], #0x10
    // 0xa66680: ret
    //     0xa66680: ret             
    // 0xa66684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa66684: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66688: b               #0xa665fc
    // 0xa6668c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa6668c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa66690, size: 0xb8
    // 0xa66690: EnterFrame
    //     0xa66690: stp             fp, lr, [SP, #-0x10]!
    //     0xa66694: mov             fp, SP
    // 0xa66698: AllocStack(0x38)
    //     0xa66698: sub             SP, SP, #0x38
    // 0xa6669c: SetupParameters()
    //     0xa6669c: ldr             x0, [fp, #0x10]
    //     0xa666a0: ldur            w1, [x0, #0x17]
    //     0xa666a4: add             x1, x1, HEAP, lsl #32
    // 0xa666a8: CheckStackOverflow
    //     0xa666a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa666ac: cmp             SP, x16
    //     0xa666b0: b.ls            #0xa6673c
    // 0xa666b4: LoadField: r0 = r1->field_f
    //     0xa666b4: ldur            w0, [x1, #0xf]
    // 0xa666b8: DecompressPointer r0
    //     0xa666b8: add             x0, x0, HEAP, lsl #32
    // 0xa666bc: LoadField: r1 = r0->field_b
    //     0xa666bc: ldur            w1, [x0, #0xb]
    // 0xa666c0: DecompressPointer r1
    //     0xa666c0: add             x1, x1, HEAP, lsl #32
    // 0xa666c4: cmp             w1, NULL
    // 0xa666c8: b.eq            #0xa66744
    // 0xa666cc: LoadField: r0 = r1->field_1b
    //     0xa666cc: ldur            w0, [x1, #0x1b]
    // 0xa666d0: DecompressPointer r0
    //     0xa666d0: add             x0, x0, HEAP, lsl #32
    // 0xa666d4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa666d4: ldur            w2, [x1, #0x17]
    // 0xa666d8: DecompressPointer r2
    //     0xa666d8: add             x2, x2, HEAP, lsl #32
    // 0xa666dc: LoadField: r3 = r1->field_f
    //     0xa666dc: ldur            w3, [x1, #0xf]
    // 0xa666e0: DecompressPointer r3
    //     0xa666e0: add             x3, x3, HEAP, lsl #32
    // 0xa666e4: LoadField: r4 = r1->field_23
    //     0xa666e4: ldur            w4, [x1, #0x23]
    // 0xa666e8: DecompressPointer r4
    //     0xa666e8: add             x4, x4, HEAP, lsl #32
    // 0xa666ec: LoadField: r5 = r1->field_1f
    //     0xa666ec: ldur            w5, [x1, #0x1f]
    // 0xa666f0: DecompressPointer r5
    //     0xa666f0: add             x5, x5, HEAP, lsl #32
    // 0xa666f4: LoadField: r6 = r1->field_2b
    //     0xa666f4: ldur            w6, [x1, #0x2b]
    // 0xa666f8: DecompressPointer r6
    //     0xa666f8: add             x6, x6, HEAP, lsl #32
    // 0xa666fc: LoadField: r7 = r1->field_27
    //     0xa666fc: ldur            w7, [x1, #0x27]
    // 0xa66700: DecompressPointer r7
    //     0xa66700: add             x7, x7, HEAP, lsl #32
    // 0xa66704: stp             x0, x7, [SP, #0x28]
    // 0xa66708: stp             x3, x2, [SP, #0x18]
    // 0xa6670c: stp             x5, x4, [SP, #8]
    // 0xa66710: str             x6, [SP]
    // 0xa66714: r4 = 0
    //     0xa66714: movz            x4, #0
    // 0xa66718: ldr             x0, [SP, #0x30]
    // 0xa6671c: r16 = UnlinkedCall_0x613b5c
    //     0xa6671c: add             x16, PP, #0x5a, lsl #12  ; [pp+0x5a410] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xa66720: add             x16, x16, #0x410
    // 0xa66724: ldp             x5, lr, [x16]
    // 0xa66728: blr             lr
    // 0xa6672c: r0 = Null
    //     0xa6672c: mov             x0, NULL
    // 0xa66730: LeaveFrame
    //     0xa66730: mov             SP, fp
    //     0xa66734: ldp             fp, lr, [SP], #0x10
    // 0xa66738: ret
    //     0xa66738: ret             
    // 0xa6673c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa6673c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa66740: b               #0xa666b4
    // 0xa66744: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa66744: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4240, size: 0x34, field offset: 0xc
//   const constructor, 
class TestimonialCarousel extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7bd4c, size: 0x84
    // 0xc7bd4c: EnterFrame
    //     0xc7bd4c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7bd50: mov             fp, SP
    // 0xc7bd54: AllocStack(0x18)
    //     0xc7bd54: sub             SP, SP, #0x18
    // 0xc7bd58: CheckStackOverflow
    //     0xc7bd58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc7bd5c: cmp             SP, x16
    //     0xc7bd60: b.ls            #0xc7bdc8
    // 0xc7bd64: r1 = <TestimonialCarousel>
    //     0xc7bd64: add             x1, PP, #0x49, lsl #12  ; [pp+0x49028] TypeArguments: <TestimonialCarousel>
    //     0xc7bd68: ldr             x1, [x1, #0x28]
    // 0xc7bd6c: r0 = _TestimonialCarouselState()
    //     0xc7bd6c: bl              #0xc7bdd0  ; Allocate_TestimonialCarouselStateStub -> _TestimonialCarouselState (size=0x24)
    // 0xc7bd70: mov             x1, x0
    // 0xc7bd74: r0 = Sentinel
    //     0xc7bd74: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7bd78: stur            x1, [fp, #-8]
    // 0xc7bd7c: StoreField: r1->field_13 = r0
    //     0xc7bd7c: stur            w0, [x1, #0x13]
    // 0xc7bd80: ArrayStore: r1[0] = rZR  ; List_8
    //     0xc7bd80: stur            xzr, [x1, #0x17]
    // 0xc7bd84: r16 = <int, RxBool>
    //     0xc7bd84: add             x16, PP, #0x48, lsl #12  ; [pp+0x48298] TypeArguments: <int, RxBool>
    //     0xc7bd88: ldr             x16, [x16, #0x298]
    // 0xc7bd8c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc7bd90: stp             lr, x16, [SP]
    // 0xc7bd94: r0 = Map._fromLiteral()
    //     0xc7bd94: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0xc7bd98: ldur            x1, [fp, #-8]
    // 0xc7bd9c: StoreField: r1->field_1f = r0
    //     0xc7bd9c: stur            w0, [x1, #0x1f]
    //     0xc7bda0: ldurb           w16, [x1, #-1]
    //     0xc7bda4: ldurb           w17, [x0, #-1]
    //     0xc7bda8: and             x16, x17, x16, lsr #2
    //     0xc7bdac: tst             x16, HEAP, lsr #32
    //     0xc7bdb0: b.eq            #0xc7bdb8
    //     0xc7bdb4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xc7bdb8: mov             x0, x1
    // 0xc7bdbc: LeaveFrame
    //     0xc7bdbc: mov             SP, fp
    //     0xc7bdc0: ldp             fp, lr, [SP], #0x10
    // 0xc7bdc4: ret
    //     0xc7bdc4: ret             
    // 0xc7bdc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc7bdc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc7bdcc: b               #0xc7bd64
  }
}
