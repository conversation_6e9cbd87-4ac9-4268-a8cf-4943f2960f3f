// lib: , url: package:customer_app/app/presentation/views/line/exchange/cancel_exchange_order_bottom_sheet.dart

// class id: 1049512, size: 0x8
class :: {
}

// class id: 3253, size: 0x14, field offset: 0x14
class _CancelExchangeBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbdbc24, size: 0x884
    // 0xbdbc24: EnterFrame
    //     0xbdbc24: stp             fp, lr, [SP, #-0x10]!
    //     0xbdbc28: mov             fp, SP
    // 0xbdbc2c: AllocStack(0x60)
    //     0xbdbc2c: sub             SP, SP, #0x60
    // 0xbdbc30: SetupParameters(_CancelExchangeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbdbc30: mov             x0, x1
    //     0xbdbc34: stur            x1, [fp, #-8]
    //     0xbdbc38: mov             x1, x2
    //     0xbdbc3c: stur            x2, [fp, #-0x10]
    // 0xbdbc40: CheckStackOverflow
    //     0xbdbc40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdbc44: cmp             SP, x16
    //     0xbdbc48: b.ls            #0xbdc494
    // 0xbdbc4c: r1 = 2
    //     0xbdbc4c: movz            x1, #0x2
    // 0xbdbc50: r0 = AllocateContext()
    //     0xbdbc50: bl              #0x16f6108  ; AllocateContextStub
    // 0xbdbc54: mov             x2, x0
    // 0xbdbc58: ldur            x0, [fp, #-8]
    // 0xbdbc5c: stur            x2, [fp, #-0x20]
    // 0xbdbc60: StoreField: r2->field_f = r0
    //     0xbdbc60: stur            w0, [x2, #0xf]
    // 0xbdbc64: ldur            x1, [fp, #-0x10]
    // 0xbdbc68: StoreField: r2->field_13 = r1
    //     0xbdbc68: stur            w1, [x2, #0x13]
    // 0xbdbc6c: LoadField: r3 = r0->field_b
    //     0xbdbc6c: ldur            w3, [x0, #0xb]
    // 0xbdbc70: DecompressPointer r3
    //     0xbdbc70: add             x3, x3, HEAP, lsl #32
    // 0xbdbc74: cmp             w3, NULL
    // 0xbdbc78: b.eq            #0xbdc49c
    // 0xbdbc7c: LoadField: r4 = r3->field_b
    //     0xbdbc7c: ldur            w4, [x3, #0xb]
    // 0xbdbc80: DecompressPointer r4
    //     0xbdbc80: add             x4, x4, HEAP, lsl #32
    // 0xbdbc84: cmp             w4, NULL
    // 0xbdbc88: b.ne            #0xbdbc94
    // 0xbdbc8c: r3 = Null
    //     0xbdbc8c: mov             x3, NULL
    // 0xbdbc90: b               #0xbdbc9c
    // 0xbdbc94: LoadField: r3 = r4->field_7
    //     0xbdbc94: ldur            w3, [x4, #7]
    // 0xbdbc98: DecompressPointer r3
    //     0xbdbc98: add             x3, x3, HEAP, lsl #32
    // 0xbdbc9c: cmp             w3, NULL
    // 0xbdbca0: b.ne            #0xbdbca8
    // 0xbdbca4: r3 = ""
    //     0xbdbca4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdbca8: stur            x3, [fp, #-0x18]
    // 0xbdbcac: r0 = of()
    //     0xbdbcac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdbcb0: LoadField: r1 = r0->field_87
    //     0xbdbcb0: ldur            w1, [x0, #0x87]
    // 0xbdbcb4: DecompressPointer r1
    //     0xbdbcb4: add             x1, x1, HEAP, lsl #32
    // 0xbdbcb8: LoadField: r0 = r1->field_7
    //     0xbdbcb8: ldur            w0, [x1, #7]
    // 0xbdbcbc: DecompressPointer r0
    //     0xbdbcbc: add             x0, x0, HEAP, lsl #32
    // 0xbdbcc0: r16 = Instance_Color
    //     0xbdbcc0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbdbcc4: r30 = 16.000000
    //     0xbdbcc4: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xbdbcc8: ldr             lr, [lr, #0x188]
    // 0xbdbccc: stp             lr, x16, [SP]
    // 0xbdbcd0: mov             x1, x0
    // 0xbdbcd4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbdbcd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbdbcd8: ldr             x4, [x4, #0x9b8]
    // 0xbdbcdc: r0 = copyWith()
    //     0xbdbcdc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdbce0: stur            x0, [fp, #-0x10]
    // 0xbdbce4: r0 = Text()
    //     0xbdbce4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdbce8: mov             x1, x0
    // 0xbdbcec: ldur            x0, [fp, #-0x18]
    // 0xbdbcf0: stur            x1, [fp, #-0x28]
    // 0xbdbcf4: StoreField: r1->field_b = r0
    //     0xbdbcf4: stur            w0, [x1, #0xb]
    // 0xbdbcf8: ldur            x0, [fp, #-0x10]
    // 0xbdbcfc: StoreField: r1->field_13 = r0
    //     0xbdbcfc: stur            w0, [x1, #0x13]
    // 0xbdbd00: r0 = SvgPicture()
    //     0xbdbd00: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbdbd04: mov             x1, x0
    // 0xbdbd08: r2 = "assets/images/x.svg"
    //     0xbdbd08: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbdbd0c: ldr             x2, [x2, #0x5e8]
    // 0xbdbd10: stur            x0, [fp, #-0x10]
    // 0xbdbd14: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbdbd14: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbdbd18: r0 = SvgPicture.asset()
    //     0xbdbd18: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbdbd1c: r0 = InkWell()
    //     0xbdbd1c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbdbd20: mov             x3, x0
    // 0xbdbd24: ldur            x0, [fp, #-0x10]
    // 0xbdbd28: stur            x3, [fp, #-0x18]
    // 0xbdbd2c: StoreField: r3->field_b = r0
    //     0xbdbd2c: stur            w0, [x3, #0xb]
    // 0xbdbd30: ldur            x2, [fp, #-0x20]
    // 0xbdbd34: r1 = Function '<anonymous closure>':.
    //     0xbdbd34: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c160] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xbdbd38: ldr             x1, [x1, #0x160]
    // 0xbdbd3c: r0 = AllocateClosure()
    //     0xbdbd3c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdbd40: mov             x1, x0
    // 0xbdbd44: ldur            x0, [fp, #-0x18]
    // 0xbdbd48: StoreField: r0->field_f = r1
    //     0xbdbd48: stur            w1, [x0, #0xf]
    // 0xbdbd4c: r3 = true
    //     0xbdbd4c: add             x3, NULL, #0x20  ; true
    // 0xbdbd50: StoreField: r0->field_43 = r3
    //     0xbdbd50: stur            w3, [x0, #0x43]
    // 0xbdbd54: r1 = Instance_BoxShape
    //     0xbdbd54: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbdbd58: ldr             x1, [x1, #0x80]
    // 0xbdbd5c: StoreField: r0->field_47 = r1
    //     0xbdbd5c: stur            w1, [x0, #0x47]
    // 0xbdbd60: StoreField: r0->field_6f = r3
    //     0xbdbd60: stur            w3, [x0, #0x6f]
    // 0xbdbd64: r4 = false
    //     0xbdbd64: add             x4, NULL, #0x30  ; false
    // 0xbdbd68: StoreField: r0->field_73 = r4
    //     0xbdbd68: stur            w4, [x0, #0x73]
    // 0xbdbd6c: StoreField: r0->field_83 = r3
    //     0xbdbd6c: stur            w3, [x0, #0x83]
    // 0xbdbd70: StoreField: r0->field_7b = r4
    //     0xbdbd70: stur            w4, [x0, #0x7b]
    // 0xbdbd74: r1 = Null
    //     0xbdbd74: mov             x1, NULL
    // 0xbdbd78: r2 = 4
    //     0xbdbd78: movz            x2, #0x4
    // 0xbdbd7c: r0 = AllocateArray()
    //     0xbdbd7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdbd80: mov             x2, x0
    // 0xbdbd84: ldur            x0, [fp, #-0x28]
    // 0xbdbd88: stur            x2, [fp, #-0x10]
    // 0xbdbd8c: StoreField: r2->field_f = r0
    //     0xbdbd8c: stur            w0, [x2, #0xf]
    // 0xbdbd90: ldur            x0, [fp, #-0x18]
    // 0xbdbd94: StoreField: r2->field_13 = r0
    //     0xbdbd94: stur            w0, [x2, #0x13]
    // 0xbdbd98: r1 = <Widget>
    //     0xbdbd98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdbd9c: r0 = AllocateGrowableArray()
    //     0xbdbd9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdbda0: mov             x1, x0
    // 0xbdbda4: ldur            x0, [fp, #-0x10]
    // 0xbdbda8: stur            x1, [fp, #-0x18]
    // 0xbdbdac: StoreField: r1->field_f = r0
    //     0xbdbdac: stur            w0, [x1, #0xf]
    // 0xbdbdb0: r0 = 4
    //     0xbdbdb0: movz            x0, #0x4
    // 0xbdbdb4: StoreField: r1->field_b = r0
    //     0xbdbdb4: stur            w0, [x1, #0xb]
    // 0xbdbdb8: r0 = Row()
    //     0xbdbdb8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbdbdbc: mov             x2, x0
    // 0xbdbdc0: r0 = Instance_Axis
    //     0xbdbdc0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbdbdc4: stur            x2, [fp, #-0x28]
    // 0xbdbdc8: StoreField: r2->field_f = r0
    //     0xbdbdc8: stur            w0, [x2, #0xf]
    // 0xbdbdcc: r3 = Instance_MainAxisAlignment
    //     0xbdbdcc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbdbdd0: ldr             x3, [x3, #0xa8]
    // 0xbdbdd4: StoreField: r2->field_13 = r3
    //     0xbdbdd4: stur            w3, [x2, #0x13]
    // 0xbdbdd8: r4 = Instance_MainAxisSize
    //     0xbdbdd8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdbddc: ldr             x4, [x4, #0xa10]
    // 0xbdbde0: ArrayStore: r2[0] = r4  ; List_4
    //     0xbdbde0: stur            w4, [x2, #0x17]
    // 0xbdbde4: r1 = Instance_CrossAxisAlignment
    //     0xbdbde4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbdbde8: ldr             x1, [x1, #0xa18]
    // 0xbdbdec: StoreField: r2->field_1b = r1
    //     0xbdbdec: stur            w1, [x2, #0x1b]
    // 0xbdbdf0: r5 = Instance_VerticalDirection
    //     0xbdbdf0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdbdf4: ldr             x5, [x5, #0xa20]
    // 0xbdbdf8: StoreField: r2->field_23 = r5
    //     0xbdbdf8: stur            w5, [x2, #0x23]
    // 0xbdbdfc: r6 = Instance_Clip
    //     0xbdbdfc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdbe00: ldr             x6, [x6, #0x38]
    // 0xbdbe04: StoreField: r2->field_2b = r6
    //     0xbdbe04: stur            w6, [x2, #0x2b]
    // 0xbdbe08: StoreField: r2->field_2f = rZR
    //     0xbdbe08: stur            xzr, [x2, #0x2f]
    // 0xbdbe0c: ldur            x1, [fp, #-0x18]
    // 0xbdbe10: StoreField: r2->field_b = r1
    //     0xbdbe10: stur            w1, [x2, #0xb]
    // 0xbdbe14: ldur            x7, [fp, #-8]
    // 0xbdbe18: LoadField: r1 = r7->field_b
    //     0xbdbe18: ldur            w1, [x7, #0xb]
    // 0xbdbe1c: DecompressPointer r1
    //     0xbdbe1c: add             x1, x1, HEAP, lsl #32
    // 0xbdbe20: cmp             w1, NULL
    // 0xbdbe24: b.eq            #0xbdc4a0
    // 0xbdbe28: LoadField: r8 = r1->field_b
    //     0xbdbe28: ldur            w8, [x1, #0xb]
    // 0xbdbe2c: DecompressPointer r8
    //     0xbdbe2c: add             x8, x8, HEAP, lsl #32
    // 0xbdbe30: cmp             w8, NULL
    // 0xbdbe34: b.ne            #0xbdbe40
    // 0xbdbe38: r1 = Null
    //     0xbdbe38: mov             x1, NULL
    // 0xbdbe3c: b               #0xbdbe48
    // 0xbdbe40: LoadField: r1 = r8->field_f
    //     0xbdbe40: ldur            w1, [x8, #0xf]
    // 0xbdbe44: DecompressPointer r1
    //     0xbdbe44: add             x1, x1, HEAP, lsl #32
    // 0xbdbe48: cmp             w1, NULL
    // 0xbdbe4c: b.ne            #0xbdbe58
    // 0xbdbe50: r9 = ""
    //     0xbdbe50: ldr             x9, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdbe54: b               #0xbdbe5c
    // 0xbdbe58: mov             x9, x1
    // 0xbdbe5c: ldur            x8, [fp, #-0x20]
    // 0xbdbe60: stur            x9, [fp, #-0x10]
    // 0xbdbe64: LoadField: r1 = r8->field_13
    //     0xbdbe64: ldur            w1, [x8, #0x13]
    // 0xbdbe68: DecompressPointer r1
    //     0xbdbe68: add             x1, x1, HEAP, lsl #32
    // 0xbdbe6c: r0 = of()
    //     0xbdbe6c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdbe70: LoadField: r1 = r0->field_87
    //     0xbdbe70: ldur            w1, [x0, #0x87]
    // 0xbdbe74: DecompressPointer r1
    //     0xbdbe74: add             x1, x1, HEAP, lsl #32
    // 0xbdbe78: LoadField: r0 = r1->field_2b
    //     0xbdbe78: ldur            w0, [x1, #0x2b]
    // 0xbdbe7c: DecompressPointer r0
    //     0xbdbe7c: add             x0, x0, HEAP, lsl #32
    // 0xbdbe80: stur            x0, [fp, #-0x18]
    // 0xbdbe84: r1 = Instance_Color
    //     0xbdbe84: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbdbe88: d0 = 0.700000
    //     0xbdbe88: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbdbe8c: ldr             d0, [x17, #0xf48]
    // 0xbdbe90: r0 = withOpacity()
    //     0xbdbe90: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbdbe94: r16 = 12.000000
    //     0xbdbe94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbdbe98: ldr             x16, [x16, #0x9e8]
    // 0xbdbe9c: stp             x0, x16, [SP]
    // 0xbdbea0: ldur            x1, [fp, #-0x18]
    // 0xbdbea4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbdbea4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbdbea8: ldr             x4, [x4, #0xaa0]
    // 0xbdbeac: r0 = copyWith()
    //     0xbdbeac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdbeb0: stur            x0, [fp, #-0x18]
    // 0xbdbeb4: r0 = HtmlWidget()
    //     0xbdbeb4: bl              #0x98e434  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xbdbeb8: mov             x1, x0
    // 0xbdbebc: ldur            x0, [fp, #-0x10]
    // 0xbdbec0: stur            x1, [fp, #-0x30]
    // 0xbdbec4: StoreField: r1->field_1f = r0
    //     0xbdbec4: stur            w0, [x1, #0x1f]
    // 0xbdbec8: r0 = Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static.
    //     0xbdbec8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e0] Closure: () => WidgetFactory from Function '_getEnhancedWf@1222291604': static. (0x7fa737958770)
    //     0xbdbecc: ldr             x0, [x0, #0x1e0]
    // 0xbdbed0: StoreField: r1->field_23 = r0
    //     0xbdbed0: stur            w0, [x1, #0x23]
    // 0xbdbed4: r0 = Instance_ColumnMode
    //     0xbdbed4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1e8] Obj!ColumnMode@d54171
    //     0xbdbed8: ldr             x0, [x0, #0x1e8]
    // 0xbdbedc: StoreField: r1->field_3b = r0
    //     0xbdbedc: stur            w0, [x1, #0x3b]
    // 0xbdbee0: ldur            x0, [fp, #-0x18]
    // 0xbdbee4: StoreField: r1->field_3f = r0
    //     0xbdbee4: stur            w0, [x1, #0x3f]
    // 0xbdbee8: r16 = <EdgeInsets>
    //     0xbdbee8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbdbeec: ldr             x16, [x16, #0xda0]
    // 0xbdbef0: r30 = Instance_EdgeInsets
    //     0xbdbef0: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbdbef4: ldr             lr, [lr, #0x1f0]
    // 0xbdbef8: stp             lr, x16, [SP]
    // 0xbdbefc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdbefc: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdbf00: r0 = all()
    //     0xbdbf00: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdbf04: ldur            x2, [fp, #-0x20]
    // 0xbdbf08: stur            x0, [fp, #-0x10]
    // 0xbdbf0c: LoadField: r1 = r2->field_13
    //     0xbdbf0c: ldur            w1, [x2, #0x13]
    // 0xbdbf10: DecompressPointer r1
    //     0xbdbf10: add             x1, x1, HEAP, lsl #32
    // 0xbdbf14: r0 = of()
    //     0xbdbf14: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdbf18: LoadField: r1 = r0->field_5b
    //     0xbdbf18: ldur            w1, [x0, #0x5b]
    // 0xbdbf1c: DecompressPointer r1
    //     0xbdbf1c: add             x1, x1, HEAP, lsl #32
    // 0xbdbf20: stur            x1, [fp, #-0x18]
    // 0xbdbf24: r0 = BorderSide()
    //     0xbdbf24: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbdbf28: mov             x1, x0
    // 0xbdbf2c: ldur            x0, [fp, #-0x18]
    // 0xbdbf30: stur            x1, [fp, #-0x38]
    // 0xbdbf34: StoreField: r1->field_7 = r0
    //     0xbdbf34: stur            w0, [x1, #7]
    // 0xbdbf38: d0 = 1.000000
    //     0xbdbf38: fmov            d0, #1.00000000
    // 0xbdbf3c: StoreField: r1->field_b = d0
    //     0xbdbf3c: stur            d0, [x1, #0xb]
    // 0xbdbf40: r0 = Instance_BorderStyle
    //     0xbdbf40: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbdbf44: ldr             x0, [x0, #0xf68]
    // 0xbdbf48: StoreField: r1->field_13 = r0
    //     0xbdbf48: stur            w0, [x1, #0x13]
    // 0xbdbf4c: d0 = -1.000000
    //     0xbdbf4c: fmov            d0, #-1.00000000
    // 0xbdbf50: ArrayStore: r1[0] = d0  ; List_8
    //     0xbdbf50: stur            d0, [x1, #0x17]
    // 0xbdbf54: r0 = RoundedRectangleBorder()
    //     0xbdbf54: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbdbf58: mov             x1, x0
    // 0xbdbf5c: r0 = Instance_BorderRadius
    //     0xbdbf5c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbdbf60: ldr             x0, [x0, #0xf70]
    // 0xbdbf64: StoreField: r1->field_b = r0
    //     0xbdbf64: stur            w0, [x1, #0xb]
    // 0xbdbf68: ldur            x0, [fp, #-0x38]
    // 0xbdbf6c: StoreField: r1->field_7 = r0
    //     0xbdbf6c: stur            w0, [x1, #7]
    // 0xbdbf70: r16 = <RoundedRectangleBorder>
    //     0xbdbf70: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbdbf74: ldr             x16, [x16, #0xf78]
    // 0xbdbf78: stp             x1, x16, [SP]
    // 0xbdbf7c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdbf7c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdbf80: r0 = all()
    //     0xbdbf80: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdbf84: stur            x0, [fp, #-0x18]
    // 0xbdbf88: r0 = ButtonStyle()
    //     0xbdbf88: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbdbf8c: mov             x1, x0
    // 0xbdbf90: ldur            x0, [fp, #-0x10]
    // 0xbdbf94: stur            x1, [fp, #-0x38]
    // 0xbdbf98: StoreField: r1->field_23 = r0
    //     0xbdbf98: stur            w0, [x1, #0x23]
    // 0xbdbf9c: ldur            x0, [fp, #-0x18]
    // 0xbdbfa0: StoreField: r1->field_43 = r0
    //     0xbdbfa0: stur            w0, [x1, #0x43]
    // 0xbdbfa4: r0 = TextButtonThemeData()
    //     0xbdbfa4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbdbfa8: mov             x2, x0
    // 0xbdbfac: ldur            x0, [fp, #-0x38]
    // 0xbdbfb0: stur            x2, [fp, #-0x10]
    // 0xbdbfb4: StoreField: r2->field_7 = r0
    //     0xbdbfb4: stur            w0, [x2, #7]
    // 0xbdbfb8: ldur            x0, [fp, #-0x20]
    // 0xbdbfbc: LoadField: r1 = r0->field_13
    //     0xbdbfbc: ldur            w1, [x0, #0x13]
    // 0xbdbfc0: DecompressPointer r1
    //     0xbdbfc0: add             x1, x1, HEAP, lsl #32
    // 0xbdbfc4: r0 = of()
    //     0xbdbfc4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdbfc8: LoadField: r1 = r0->field_87
    //     0xbdbfc8: ldur            w1, [x0, #0x87]
    // 0xbdbfcc: DecompressPointer r1
    //     0xbdbfcc: add             x1, x1, HEAP, lsl #32
    // 0xbdbfd0: LoadField: r0 = r1->field_7
    //     0xbdbfd0: ldur            w0, [x1, #7]
    // 0xbdbfd4: DecompressPointer r0
    //     0xbdbfd4: add             x0, x0, HEAP, lsl #32
    // 0xbdbfd8: ldur            x2, [fp, #-0x20]
    // 0xbdbfdc: stur            x0, [fp, #-0x18]
    // 0xbdbfe0: LoadField: r1 = r2->field_13
    //     0xbdbfe0: ldur            w1, [x2, #0x13]
    // 0xbdbfe4: DecompressPointer r1
    //     0xbdbfe4: add             x1, x1, HEAP, lsl #32
    // 0xbdbfe8: r0 = of()
    //     0xbdbfe8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdbfec: LoadField: r1 = r0->field_5b
    //     0xbdbfec: ldur            w1, [x0, #0x5b]
    // 0xbdbff0: DecompressPointer r1
    //     0xbdbff0: add             x1, x1, HEAP, lsl #32
    // 0xbdbff4: r16 = 14.000000
    //     0xbdbff4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbdbff8: ldr             x16, [x16, #0x1d8]
    // 0xbdbffc: stp             x1, x16, [SP]
    // 0xbdc000: ldur            x1, [fp, #-0x18]
    // 0xbdc004: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbdc004: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbdc008: ldr             x4, [x4, #0xaa0]
    // 0xbdc00c: r0 = copyWith()
    //     0xbdc00c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdc010: stur            x0, [fp, #-0x18]
    // 0xbdc014: r0 = Text()
    //     0xbdc014: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdc018: mov             x3, x0
    // 0xbdc01c: r0 = "GO BACK"
    //     0xbdc01c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36138] "GO BACK"
    //     0xbdc020: ldr             x0, [x0, #0x138]
    // 0xbdc024: stur            x3, [fp, #-0x38]
    // 0xbdc028: StoreField: r3->field_b = r0
    //     0xbdc028: stur            w0, [x3, #0xb]
    // 0xbdc02c: ldur            x0, [fp, #-0x18]
    // 0xbdc030: StoreField: r3->field_13 = r0
    //     0xbdc030: stur            w0, [x3, #0x13]
    // 0xbdc034: r1 = Function '<anonymous closure>':.
    //     0xbdc034: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c168] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbdc038: ldr             x1, [x1, #0x168]
    // 0xbdc03c: r2 = Null
    //     0xbdc03c: mov             x2, NULL
    // 0xbdc040: r0 = AllocateClosure()
    //     0xbdc040: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdc044: stur            x0, [fp, #-0x18]
    // 0xbdc048: r0 = TextButton()
    //     0xbdc048: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbdc04c: mov             x1, x0
    // 0xbdc050: ldur            x0, [fp, #-0x18]
    // 0xbdc054: stur            x1, [fp, #-0x40]
    // 0xbdc058: StoreField: r1->field_b = r0
    //     0xbdc058: stur            w0, [x1, #0xb]
    // 0xbdc05c: r0 = false
    //     0xbdc05c: add             x0, NULL, #0x30  ; false
    // 0xbdc060: StoreField: r1->field_27 = r0
    //     0xbdc060: stur            w0, [x1, #0x27]
    // 0xbdc064: r2 = true
    //     0xbdc064: add             x2, NULL, #0x20  ; true
    // 0xbdc068: StoreField: r1->field_2f = r2
    //     0xbdc068: stur            w2, [x1, #0x2f]
    // 0xbdc06c: ldur            x3, [fp, #-0x38]
    // 0xbdc070: StoreField: r1->field_37 = r3
    //     0xbdc070: stur            w3, [x1, #0x37]
    // 0xbdc074: r0 = TextButtonTheme()
    //     0xbdc074: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbdc078: mov             x2, x0
    // 0xbdc07c: ldur            x0, [fp, #-0x10]
    // 0xbdc080: stur            x2, [fp, #-0x18]
    // 0xbdc084: StoreField: r2->field_f = r0
    //     0xbdc084: stur            w0, [x2, #0xf]
    // 0xbdc088: ldur            x0, [fp, #-0x40]
    // 0xbdc08c: StoreField: r2->field_b = r0
    //     0xbdc08c: stur            w0, [x2, #0xb]
    // 0xbdc090: r1 = <FlexParentData>
    //     0xbdc090: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbdc094: ldr             x1, [x1, #0xe00]
    // 0xbdc098: r0 = Flexible()
    //     0xbdc098: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbdc09c: mov             x1, x0
    // 0xbdc0a0: r0 = 1
    //     0xbdc0a0: movz            x0, #0x1
    // 0xbdc0a4: stur            x1, [fp, #-0x10]
    // 0xbdc0a8: StoreField: r1->field_13 = r0
    //     0xbdc0a8: stur            x0, [x1, #0x13]
    // 0xbdc0ac: r2 = Instance_FlexFit
    //     0xbdc0ac: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbdc0b0: ldr             x2, [x2, #0xe08]
    // 0xbdc0b4: StoreField: r1->field_1b = r2
    //     0xbdc0b4: stur            w2, [x1, #0x1b]
    // 0xbdc0b8: ldur            x3, [fp, #-0x18]
    // 0xbdc0bc: StoreField: r1->field_b = r3
    //     0xbdc0bc: stur            w3, [x1, #0xb]
    // 0xbdc0c0: r16 = <EdgeInsets>
    //     0xbdc0c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbdc0c4: ldr             x16, [x16, #0xda0]
    // 0xbdc0c8: r30 = Instance_EdgeInsets
    //     0xbdc0c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbdc0cc: ldr             lr, [lr, #0x1f0]
    // 0xbdc0d0: stp             lr, x16, [SP]
    // 0xbdc0d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdc0d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdc0d8: r0 = all()
    //     0xbdc0d8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdc0dc: ldur            x2, [fp, #-0x20]
    // 0xbdc0e0: stur            x0, [fp, #-0x18]
    // 0xbdc0e4: LoadField: r1 = r2->field_13
    //     0xbdc0e4: ldur            w1, [x2, #0x13]
    // 0xbdc0e8: DecompressPointer r1
    //     0xbdc0e8: add             x1, x1, HEAP, lsl #32
    // 0xbdc0ec: r0 = of()
    //     0xbdc0ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdc0f0: LoadField: r1 = r0->field_5b
    //     0xbdc0f0: ldur            w1, [x0, #0x5b]
    // 0xbdc0f4: DecompressPointer r1
    //     0xbdc0f4: add             x1, x1, HEAP, lsl #32
    // 0xbdc0f8: r16 = <Color>
    //     0xbdc0f8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbdc0fc: ldr             x16, [x16, #0xf80]
    // 0xbdc100: stp             x1, x16, [SP]
    // 0xbdc104: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdc104: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdc108: r0 = all()
    //     0xbdc108: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdc10c: stur            x0, [fp, #-0x38]
    // 0xbdc110: r16 = <RoundedRectangleBorder>
    //     0xbdc110: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbdc114: ldr             x16, [x16, #0xf78]
    // 0xbdc118: r30 = Instance_RoundedRectangleBorder
    //     0xbdc118: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbdc11c: ldr             lr, [lr, #0xd68]
    // 0xbdc120: stp             lr, x16, [SP]
    // 0xbdc124: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbdc124: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbdc128: r0 = all()
    //     0xbdc128: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbdc12c: stur            x0, [fp, #-0x40]
    // 0xbdc130: r0 = ButtonStyle()
    //     0xbdc130: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbdc134: mov             x1, x0
    // 0xbdc138: ldur            x0, [fp, #-0x38]
    // 0xbdc13c: stur            x1, [fp, #-0x48]
    // 0xbdc140: StoreField: r1->field_b = r0
    //     0xbdc140: stur            w0, [x1, #0xb]
    // 0xbdc144: ldur            x0, [fp, #-0x18]
    // 0xbdc148: StoreField: r1->field_23 = r0
    //     0xbdc148: stur            w0, [x1, #0x23]
    // 0xbdc14c: ldur            x0, [fp, #-0x40]
    // 0xbdc150: StoreField: r1->field_43 = r0
    //     0xbdc150: stur            w0, [x1, #0x43]
    // 0xbdc154: r0 = TextButtonThemeData()
    //     0xbdc154: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbdc158: mov             x1, x0
    // 0xbdc15c: ldur            x0, [fp, #-0x48]
    // 0xbdc160: stur            x1, [fp, #-0x18]
    // 0xbdc164: StoreField: r1->field_7 = r0
    //     0xbdc164: stur            w0, [x1, #7]
    // 0xbdc168: ldur            x0, [fp, #-8]
    // 0xbdc16c: LoadField: r2 = r0->field_b
    //     0xbdc16c: ldur            w2, [x0, #0xb]
    // 0xbdc170: DecompressPointer r2
    //     0xbdc170: add             x2, x2, HEAP, lsl #32
    // 0xbdc174: cmp             w2, NULL
    // 0xbdc178: b.eq            #0xbdc4a4
    // 0xbdc17c: LoadField: r0 = r2->field_b
    //     0xbdc17c: ldur            w0, [x2, #0xb]
    // 0xbdc180: DecompressPointer r0
    //     0xbdc180: add             x0, x0, HEAP, lsl #32
    // 0xbdc184: cmp             w0, NULL
    // 0xbdc188: b.ne            #0xbdc194
    // 0xbdc18c: r0 = Null
    //     0xbdc18c: mov             x0, NULL
    // 0xbdc190: b               #0xbdc1a0
    // 0xbdc194: LoadField: r2 = r0->field_b
    //     0xbdc194: ldur            w2, [x0, #0xb]
    // 0xbdc198: DecompressPointer r2
    //     0xbdc198: add             x2, x2, HEAP, lsl #32
    // 0xbdc19c: mov             x0, x2
    // 0xbdc1a0: cmp             w0, NULL
    // 0xbdc1a4: b.ne            #0xbdc1ac
    // 0xbdc1a8: r0 = ""
    //     0xbdc1a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbdc1ac: ldur            x2, [fp, #-0x20]
    // 0xbdc1b0: ldur            x5, [fp, #-0x28]
    // 0xbdc1b4: ldur            x4, [fp, #-0x30]
    // 0xbdc1b8: ldur            x3, [fp, #-0x10]
    // 0xbdc1bc: r6 = LoadClassIdInstr(r0)
    //     0xbdc1bc: ldur            x6, [x0, #-1]
    //     0xbdc1c0: ubfx            x6, x6, #0xc, #0x14
    // 0xbdc1c4: str             x0, [SP]
    // 0xbdc1c8: mov             x0, x6
    // 0xbdc1cc: r0 = GDT[cid_x0 + -0x1000]()
    //     0xbdc1cc: sub             lr, x0, #1, lsl #12
    //     0xbdc1d0: ldr             lr, [x21, lr, lsl #3]
    //     0xbdc1d4: blr             lr
    // 0xbdc1d8: ldur            x2, [fp, #-0x20]
    // 0xbdc1dc: stur            x0, [fp, #-8]
    // 0xbdc1e0: LoadField: r1 = r2->field_13
    //     0xbdc1e0: ldur            w1, [x2, #0x13]
    // 0xbdc1e4: DecompressPointer r1
    //     0xbdc1e4: add             x1, x1, HEAP, lsl #32
    // 0xbdc1e8: r0 = of()
    //     0xbdc1e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbdc1ec: LoadField: r1 = r0->field_87
    //     0xbdc1ec: ldur            w1, [x0, #0x87]
    // 0xbdc1f0: DecompressPointer r1
    //     0xbdc1f0: add             x1, x1, HEAP, lsl #32
    // 0xbdc1f4: LoadField: r0 = r1->field_7
    //     0xbdc1f4: ldur            w0, [x1, #7]
    // 0xbdc1f8: DecompressPointer r0
    //     0xbdc1f8: add             x0, x0, HEAP, lsl #32
    // 0xbdc1fc: r16 = 14.000000
    //     0xbdc1fc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbdc200: ldr             x16, [x16, #0x1d8]
    // 0xbdc204: r30 = Instance_Color
    //     0xbdc204: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbdc208: stp             lr, x16, [SP]
    // 0xbdc20c: mov             x1, x0
    // 0xbdc210: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbdc210: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbdc214: ldr             x4, [x4, #0xaa0]
    // 0xbdc218: r0 = copyWith()
    //     0xbdc218: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbdc21c: stur            x0, [fp, #-0x38]
    // 0xbdc220: r0 = Text()
    //     0xbdc220: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbdc224: mov             x3, x0
    // 0xbdc228: ldur            x0, [fp, #-8]
    // 0xbdc22c: stur            x3, [fp, #-0x40]
    // 0xbdc230: StoreField: r3->field_b = r0
    //     0xbdc230: stur            w0, [x3, #0xb]
    // 0xbdc234: ldur            x0, [fp, #-0x38]
    // 0xbdc238: StoreField: r3->field_13 = r0
    //     0xbdc238: stur            w0, [x3, #0x13]
    // 0xbdc23c: r0 = Instance_TextAlign
    //     0xbdc23c: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbdc240: StoreField: r3->field_1b = r0
    //     0xbdc240: stur            w0, [x3, #0x1b]
    // 0xbdc244: ldur            x2, [fp, #-0x20]
    // 0xbdc248: r1 = Function '<anonymous closure>':.
    //     0xbdc248: add             x1, PP, #0x5c, lsl #12  ; [pp+0x5c170] AnonymousClosure: (0xbdc4a8), in [package:customer_app/app/presentation/views/line/exchange/cancel_exchange_order_bottom_sheet.dart] _CancelExchangeBottomSheetState::build (0xbdbc24)
    //     0xbdc24c: ldr             x1, [x1, #0x170]
    // 0xbdc250: r0 = AllocateClosure()
    //     0xbdc250: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbdc254: stur            x0, [fp, #-8]
    // 0xbdc258: r0 = TextButton()
    //     0xbdc258: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbdc25c: mov             x1, x0
    // 0xbdc260: ldur            x0, [fp, #-8]
    // 0xbdc264: stur            x1, [fp, #-0x20]
    // 0xbdc268: StoreField: r1->field_b = r0
    //     0xbdc268: stur            w0, [x1, #0xb]
    // 0xbdc26c: r0 = false
    //     0xbdc26c: add             x0, NULL, #0x30  ; false
    // 0xbdc270: StoreField: r1->field_27 = r0
    //     0xbdc270: stur            w0, [x1, #0x27]
    // 0xbdc274: r0 = true
    //     0xbdc274: add             x0, NULL, #0x20  ; true
    // 0xbdc278: StoreField: r1->field_2f = r0
    //     0xbdc278: stur            w0, [x1, #0x2f]
    // 0xbdc27c: ldur            x0, [fp, #-0x40]
    // 0xbdc280: StoreField: r1->field_37 = r0
    //     0xbdc280: stur            w0, [x1, #0x37]
    // 0xbdc284: r0 = TextButtonTheme()
    //     0xbdc284: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbdc288: mov             x2, x0
    // 0xbdc28c: ldur            x0, [fp, #-0x18]
    // 0xbdc290: stur            x2, [fp, #-8]
    // 0xbdc294: StoreField: r2->field_f = r0
    //     0xbdc294: stur            w0, [x2, #0xf]
    // 0xbdc298: ldur            x0, [fp, #-0x20]
    // 0xbdc29c: StoreField: r2->field_b = r0
    //     0xbdc29c: stur            w0, [x2, #0xb]
    // 0xbdc2a0: r1 = <FlexParentData>
    //     0xbdc2a0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbdc2a4: ldr             x1, [x1, #0xe00]
    // 0xbdc2a8: r0 = Flexible()
    //     0xbdc2a8: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xbdc2ac: mov             x3, x0
    // 0xbdc2b0: r0 = 1
    //     0xbdc2b0: movz            x0, #0x1
    // 0xbdc2b4: stur            x3, [fp, #-0x18]
    // 0xbdc2b8: StoreField: r3->field_13 = r0
    //     0xbdc2b8: stur            x0, [x3, #0x13]
    // 0xbdc2bc: r0 = Instance_FlexFit
    //     0xbdc2bc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbdc2c0: ldr             x0, [x0, #0xe08]
    // 0xbdc2c4: StoreField: r3->field_1b = r0
    //     0xbdc2c4: stur            w0, [x3, #0x1b]
    // 0xbdc2c8: ldur            x0, [fp, #-8]
    // 0xbdc2cc: StoreField: r3->field_b = r0
    //     0xbdc2cc: stur            w0, [x3, #0xb]
    // 0xbdc2d0: r1 = Null
    //     0xbdc2d0: mov             x1, NULL
    // 0xbdc2d4: r2 = 6
    //     0xbdc2d4: movz            x2, #0x6
    // 0xbdc2d8: r0 = AllocateArray()
    //     0xbdc2d8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdc2dc: mov             x2, x0
    // 0xbdc2e0: ldur            x0, [fp, #-0x10]
    // 0xbdc2e4: stur            x2, [fp, #-8]
    // 0xbdc2e8: StoreField: r2->field_f = r0
    //     0xbdc2e8: stur            w0, [x2, #0xf]
    // 0xbdc2ec: r16 = Instance_SizedBox
    //     0xbdc2ec: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbdc2f0: ldr             x16, [x16, #0xb20]
    // 0xbdc2f4: StoreField: r2->field_13 = r16
    //     0xbdc2f4: stur            w16, [x2, #0x13]
    // 0xbdc2f8: ldur            x0, [fp, #-0x18]
    // 0xbdc2fc: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdc2fc: stur            w0, [x2, #0x17]
    // 0xbdc300: r1 = <Widget>
    //     0xbdc300: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdc304: r0 = AllocateGrowableArray()
    //     0xbdc304: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdc308: mov             x1, x0
    // 0xbdc30c: ldur            x0, [fp, #-8]
    // 0xbdc310: stur            x1, [fp, #-0x10]
    // 0xbdc314: StoreField: r1->field_f = r0
    //     0xbdc314: stur            w0, [x1, #0xf]
    // 0xbdc318: r0 = 6
    //     0xbdc318: movz            x0, #0x6
    // 0xbdc31c: StoreField: r1->field_b = r0
    //     0xbdc31c: stur            w0, [x1, #0xb]
    // 0xbdc320: r0 = Row()
    //     0xbdc320: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbdc324: mov             x1, x0
    // 0xbdc328: r0 = Instance_Axis
    //     0xbdc328: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbdc32c: stur            x1, [fp, #-8]
    // 0xbdc330: StoreField: r1->field_f = r0
    //     0xbdc330: stur            w0, [x1, #0xf]
    // 0xbdc334: r0 = Instance_MainAxisAlignment
    //     0xbdc334: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbdc338: ldr             x0, [x0, #0xa8]
    // 0xbdc33c: StoreField: r1->field_13 = r0
    //     0xbdc33c: stur            w0, [x1, #0x13]
    // 0xbdc340: r0 = Instance_MainAxisSize
    //     0xbdc340: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbdc344: ldr             x0, [x0, #0xa10]
    // 0xbdc348: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdc348: stur            w0, [x1, #0x17]
    // 0xbdc34c: r0 = Instance_CrossAxisAlignment
    //     0xbdc34c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbdc350: ldr             x0, [x0, #0x890]
    // 0xbdc354: StoreField: r1->field_1b = r0
    //     0xbdc354: stur            w0, [x1, #0x1b]
    // 0xbdc358: r2 = Instance_VerticalDirection
    //     0xbdc358: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdc35c: ldr             x2, [x2, #0xa20]
    // 0xbdc360: StoreField: r1->field_23 = r2
    //     0xbdc360: stur            w2, [x1, #0x23]
    // 0xbdc364: r3 = Instance_Clip
    //     0xbdc364: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdc368: ldr             x3, [x3, #0x38]
    // 0xbdc36c: StoreField: r1->field_2b = r3
    //     0xbdc36c: stur            w3, [x1, #0x2b]
    // 0xbdc370: StoreField: r1->field_2f = rZR
    //     0xbdc370: stur            xzr, [x1, #0x2f]
    // 0xbdc374: ldur            x4, [fp, #-0x10]
    // 0xbdc378: StoreField: r1->field_b = r4
    //     0xbdc378: stur            w4, [x1, #0xb]
    // 0xbdc37c: r0 = Padding()
    //     0xbdc37c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbdc380: mov             x3, x0
    // 0xbdc384: r0 = Instance_EdgeInsets
    //     0xbdc384: add             x0, PP, #0x40, lsl #12  ; [pp+0x40858] Obj!EdgeInsets@d57dd1
    //     0xbdc388: ldr             x0, [x0, #0x858]
    // 0xbdc38c: stur            x3, [fp, #-0x10]
    // 0xbdc390: StoreField: r3->field_f = r0
    //     0xbdc390: stur            w0, [x3, #0xf]
    // 0xbdc394: ldur            x0, [fp, #-8]
    // 0xbdc398: StoreField: r3->field_b = r0
    //     0xbdc398: stur            w0, [x3, #0xb]
    // 0xbdc39c: r1 = Null
    //     0xbdc39c: mov             x1, NULL
    // 0xbdc3a0: r2 = 8
    //     0xbdc3a0: movz            x2, #0x8
    // 0xbdc3a4: r0 = AllocateArray()
    //     0xbdc3a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbdc3a8: mov             x2, x0
    // 0xbdc3ac: ldur            x0, [fp, #-0x28]
    // 0xbdc3b0: stur            x2, [fp, #-8]
    // 0xbdc3b4: StoreField: r2->field_f = r0
    //     0xbdc3b4: stur            w0, [x2, #0xf]
    // 0xbdc3b8: r16 = Instance_SizedBox
    //     0xbdc3b8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbdc3bc: ldr             x16, [x16, #0x578]
    // 0xbdc3c0: StoreField: r2->field_13 = r16
    //     0xbdc3c0: stur            w16, [x2, #0x13]
    // 0xbdc3c4: ldur            x0, [fp, #-0x30]
    // 0xbdc3c8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbdc3c8: stur            w0, [x2, #0x17]
    // 0xbdc3cc: ldur            x0, [fp, #-0x10]
    // 0xbdc3d0: StoreField: r2->field_1b = r0
    //     0xbdc3d0: stur            w0, [x2, #0x1b]
    // 0xbdc3d4: r1 = <Widget>
    //     0xbdc3d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbdc3d8: r0 = AllocateGrowableArray()
    //     0xbdc3d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbdc3dc: mov             x1, x0
    // 0xbdc3e0: ldur            x0, [fp, #-8]
    // 0xbdc3e4: stur            x1, [fp, #-0x10]
    // 0xbdc3e8: StoreField: r1->field_f = r0
    //     0xbdc3e8: stur            w0, [x1, #0xf]
    // 0xbdc3ec: r0 = 8
    //     0xbdc3ec: movz            x0, #0x8
    // 0xbdc3f0: StoreField: r1->field_b = r0
    //     0xbdc3f0: stur            w0, [x1, #0xb]
    // 0xbdc3f4: r0 = Column()
    //     0xbdc3f4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbdc3f8: mov             x1, x0
    // 0xbdc3fc: r0 = Instance_Axis
    //     0xbdc3fc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbdc400: stur            x1, [fp, #-8]
    // 0xbdc404: StoreField: r1->field_f = r0
    //     0xbdc404: stur            w0, [x1, #0xf]
    // 0xbdc408: r0 = Instance_MainAxisAlignment
    //     0xbdc408: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbdc40c: ldr             x0, [x0, #0xa08]
    // 0xbdc410: StoreField: r1->field_13 = r0
    //     0xbdc410: stur            w0, [x1, #0x13]
    // 0xbdc414: r0 = Instance_MainAxisSize
    //     0xbdc414: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbdc418: ldr             x0, [x0, #0xdd0]
    // 0xbdc41c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbdc41c: stur            w0, [x1, #0x17]
    // 0xbdc420: r0 = Instance_CrossAxisAlignment
    //     0xbdc420: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbdc424: ldr             x0, [x0, #0x890]
    // 0xbdc428: StoreField: r1->field_1b = r0
    //     0xbdc428: stur            w0, [x1, #0x1b]
    // 0xbdc42c: r0 = Instance_VerticalDirection
    //     0xbdc42c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbdc430: ldr             x0, [x0, #0xa20]
    // 0xbdc434: StoreField: r1->field_23 = r0
    //     0xbdc434: stur            w0, [x1, #0x23]
    // 0xbdc438: r0 = Instance_Clip
    //     0xbdc438: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbdc43c: ldr             x0, [x0, #0x38]
    // 0xbdc440: StoreField: r1->field_2b = r0
    //     0xbdc440: stur            w0, [x1, #0x2b]
    // 0xbdc444: StoreField: r1->field_2f = rZR
    //     0xbdc444: stur            xzr, [x1, #0x2f]
    // 0xbdc448: ldur            x0, [fp, #-0x10]
    // 0xbdc44c: StoreField: r1->field_b = r0
    //     0xbdc44c: stur            w0, [x1, #0xb]
    // 0xbdc450: r0 = Container()
    //     0xbdc450: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbdc454: stur            x0, [fp, #-0x10]
    // 0xbdc458: r16 = Instance_BoxDecoration
    //     0xbdc458: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c148] Obj!BoxDecoration@d64bf1
    //     0xbdc45c: ldr             x16, [x16, #0x148]
    // 0xbdc460: r30 = Instance_EdgeInsets
    //     0xbdc460: add             lr, PP, #0x55, lsl #12  ; [pp+0x55a40] Obj!EdgeInsets@d58311
    //     0xbdc464: ldr             lr, [lr, #0xa40]
    // 0xbdc468: stp             lr, x16, [SP, #8]
    // 0xbdc46c: ldur            x16, [fp, #-8]
    // 0xbdc470: str             x16, [SP]
    // 0xbdc474: mov             x1, x0
    // 0xbdc478: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xbdc478: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xbdc47c: ldr             x4, [x4, #0xb40]
    // 0xbdc480: r0 = Container()
    //     0xbdc480: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbdc484: ldur            x0, [fp, #-0x10]
    // 0xbdc488: LeaveFrame
    //     0xbdc488: mov             SP, fp
    //     0xbdc48c: ldp             fp, lr, [SP], #0x10
    // 0xbdc490: ret
    //     0xbdc490: ret             
    // 0xbdc494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdc494: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdc498: b               #0xbdbc4c
    // 0xbdc49c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdc49c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdc4a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdc4a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbdc4a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdc4a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbdc4a8, size: 0xa8
    // 0xbdc4a8: EnterFrame
    //     0xbdc4a8: stp             fp, lr, [SP, #-0x10]!
    //     0xbdc4ac: mov             fp, SP
    // 0xbdc4b0: AllocStack(0x10)
    //     0xbdc4b0: sub             SP, SP, #0x10
    // 0xbdc4b4: SetupParameters()
    //     0xbdc4b4: ldr             x0, [fp, #0x10]
    //     0xbdc4b8: ldur            w1, [x0, #0x17]
    //     0xbdc4bc: add             x1, x1, HEAP, lsl #32
    // 0xbdc4c0: CheckStackOverflow
    //     0xbdc4c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbdc4c4: cmp             SP, x16
    //     0xbdc4c8: b.ls            #0xbdc544
    // 0xbdc4cc: LoadField: r0 = r1->field_f
    //     0xbdc4cc: ldur            w0, [x1, #0xf]
    // 0xbdc4d0: DecompressPointer r0
    //     0xbdc4d0: add             x0, x0, HEAP, lsl #32
    // 0xbdc4d4: LoadField: r1 = r0->field_b
    //     0xbdc4d4: ldur            w1, [x0, #0xb]
    // 0xbdc4d8: DecompressPointer r1
    //     0xbdc4d8: add             x1, x1, HEAP, lsl #32
    // 0xbdc4dc: cmp             w1, NULL
    // 0xbdc4e0: b.eq            #0xbdc54c
    // 0xbdc4e4: LoadField: r0 = r1->field_13
    //     0xbdc4e4: ldur            w0, [x1, #0x13]
    // 0xbdc4e8: DecompressPointer r0
    //     0xbdc4e8: add             x0, x0, HEAP, lsl #32
    // 0xbdc4ec: LoadField: r2 = r1->field_f
    //     0xbdc4ec: ldur            w2, [x1, #0xf]
    // 0xbdc4f0: DecompressPointer r2
    //     0xbdc4f0: add             x2, x2, HEAP, lsl #32
    // 0xbdc4f4: stp             x0, x2, [SP]
    // 0xbdc4f8: r4 = 0
    //     0xbdc4f8: movz            x4, #0
    // 0xbdc4fc: ldr             x0, [SP, #8]
    // 0xbdc500: r5 = UnlinkedCall_0x613b5c
    //     0xbdc500: add             x16, PP, #0x5c, lsl #12  ; [pp+0x5c178] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbdc504: ldp             x5, lr, [x16, #0x178]
    // 0xbdc508: blr             lr
    // 0xbdc50c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbdc50c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbdc510: ldr             x0, [x0, #0x1c80]
    //     0xbdc514: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbdc518: cmp             w0, w16
    //     0xbdc51c: b.ne            #0xbdc528
    //     0xbdc520: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbdc524: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbdc528: str             NULL, [SP]
    // 0xbdc52c: r4 = const [0x1, 0, 0, 0, null]
    //     0xbdc52c: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0xbdc530: r0 = GetNavigation.back()
    //     0xbdc530: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0xbdc534: r0 = Null
    //     0xbdc534: mov             x0, NULL
    // 0xbdc538: LeaveFrame
    //     0xbdc538: mov             SP, fp
    //     0xbdc53c: ldp             fp, lr, [SP], #0x10
    // 0xbdc540: ret
    //     0xbdc540: ret             
    // 0xbdc544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbdc544: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbdc548: b               #0xbdc4cc
    // 0xbdc54c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbdc54c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3999, size: 0x18, field offset: 0xc
//   const constructor, 
class CancelExchangeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc8092c, size: 0x24
    // 0xc8092c: EnterFrame
    //     0xc8092c: stp             fp, lr, [SP, #-0x10]!
    //     0xc80930: mov             fp, SP
    // 0xc80934: mov             x0, x1
    // 0xc80938: r1 = <CancelExchangeBottomSheet>
    //     0xc80938: add             x1, PP, #0x49, lsl #12  ; [pp+0x493c0] TypeArguments: <CancelExchangeBottomSheet>
    //     0xc8093c: ldr             x1, [x1, #0x3c0]
    // 0xc80940: r0 = _CancelExchangeBottomSheetState()
    //     0xc80940: bl              #0xc80950  ; Allocate_CancelExchangeBottomSheetStateStub -> _CancelExchangeBottomSheetState (size=0x14)
    // 0xc80944: LeaveFrame
    //     0xc80944: mov             SP, fp
    //     0xc80948: ldp             fp, lr, [SP], #0x10
    // 0xc8094c: ret
    //     0xc8094c: ret             
  }
}
