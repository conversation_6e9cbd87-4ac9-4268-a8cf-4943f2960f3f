// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart

// class id: 1049431, size: 0x8
class :: {
}

// class id: 3320, size: 0x24, field offset: 0x14
class _GroupCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14
  late PageController _imagePageController; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xb7b034, size: 0x13f4
    // 0xb7b034: EnterFrame
    //     0xb7b034: stp             fp, lr, [SP, #-0x10]!
    //     0xb7b038: mov             fp, SP
    // 0xb7b03c: AllocStack(0xa8)
    //     0xb7b03c: sub             SP, SP, #0xa8
    // 0xb7b040: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb7b040: mov             x0, x1
    //     0xb7b044: stur            x1, [fp, #-8]
    //     0xb7b048: mov             x1, x2
    //     0xb7b04c: stur            x2, [fp, #-0x10]
    // 0xb7b050: CheckStackOverflow
    //     0xb7b050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7b054: cmp             SP, x16
    //     0xb7b058: b.ls            #0xb7c3c4
    // 0xb7b05c: r1 = 1
    //     0xb7b05c: movz            x1, #0x1
    // 0xb7b060: r0 = AllocateContext()
    //     0xb7b060: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7b064: mov             x3, x0
    // 0xb7b068: ldur            x0, [fp, #-8]
    // 0xb7b06c: stur            x3, [fp, #-0x20]
    // 0xb7b070: StoreField: r3->field_f = r0
    //     0xb7b070: stur            w0, [x3, #0xf]
    // 0xb7b074: LoadField: r1 = r0->field_b
    //     0xb7b074: ldur            w1, [x0, #0xb]
    // 0xb7b078: DecompressPointer r1
    //     0xb7b078: add             x1, x1, HEAP, lsl #32
    // 0xb7b07c: cmp             w1, NULL
    // 0xb7b080: b.eq            #0xb7c3cc
    // 0xb7b084: LoadField: r2 = r1->field_1b
    //     0xb7b084: ldur            w2, [x1, #0x1b]
    // 0xb7b088: DecompressPointer r2
    //     0xb7b088: add             x2, x2, HEAP, lsl #32
    // 0xb7b08c: LoadField: r1 = r2->field_7
    //     0xb7b08c: ldur            w1, [x2, #7]
    // 0xb7b090: DecompressPointer r1
    //     0xb7b090: add             x1, x1, HEAP, lsl #32
    // 0xb7b094: cmp             w1, NULL
    // 0xb7b098: b.ne            #0xb7b0a4
    // 0xb7b09c: r1 = Instance_TitleAlignment
    //     0xb7b09c: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb7b0a0: ldr             x1, [x1, #0x518]
    // 0xb7b0a4: r16 = Instance_TitleAlignment
    //     0xb7b0a4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb7b0a8: ldr             x16, [x16, #0x520]
    // 0xb7b0ac: cmp             w1, w16
    // 0xb7b0b0: b.ne            #0xb7b0c0
    // 0xb7b0b4: r4 = Instance_CrossAxisAlignment
    //     0xb7b0b4: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb7b0b8: ldr             x4, [x4, #0xc68]
    // 0xb7b0bc: b               #0xb7b0e4
    // 0xb7b0c0: r16 = Instance_TitleAlignment
    //     0xb7b0c0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb7b0c4: ldr             x16, [x16, #0x518]
    // 0xb7b0c8: cmp             w1, w16
    // 0xb7b0cc: b.ne            #0xb7b0dc
    // 0xb7b0d0: r4 = Instance_CrossAxisAlignment
    //     0xb7b0d0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb7b0d4: ldr             x4, [x4, #0x890]
    // 0xb7b0d8: b               #0xb7b0e4
    // 0xb7b0dc: r4 = Instance_CrossAxisAlignment
    //     0xb7b0dc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7b0e0: ldr             x4, [x4, #0xa18]
    // 0xb7b0e4: stur            x4, [fp, #-0x18]
    // 0xb7b0e8: r1 = <Widget>
    //     0xb7b0e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7b0ec: r2 = 0
    //     0xb7b0ec: movz            x2, #0
    // 0xb7b0f0: r0 = _GrowableList()
    //     0xb7b0f0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xb7b0f4: mov             x2, x0
    // 0xb7b0f8: ldur            x0, [fp, #-8]
    // 0xb7b0fc: stur            x2, [fp, #-0x28]
    // 0xb7b100: LoadField: r1 = r0->field_b
    //     0xb7b100: ldur            w1, [x0, #0xb]
    // 0xb7b104: DecompressPointer r1
    //     0xb7b104: add             x1, x1, HEAP, lsl #32
    // 0xb7b108: cmp             w1, NULL
    // 0xb7b10c: b.eq            #0xb7c3d0
    // 0xb7b110: LoadField: r3 = r1->field_f
    //     0xb7b110: ldur            w3, [x1, #0xf]
    // 0xb7b114: DecompressPointer r3
    //     0xb7b114: add             x3, x3, HEAP, lsl #32
    // 0xb7b118: cmp             w3, NULL
    // 0xb7b11c: b.ne            #0xb7b128
    // 0xb7b120: r1 = Null
    //     0xb7b120: mov             x1, NULL
    // 0xb7b124: b               #0xb7b140
    // 0xb7b128: LoadField: r1 = r3->field_7
    //     0xb7b128: ldur            w1, [x3, #7]
    // 0xb7b12c: cbnz            w1, #0xb7b138
    // 0xb7b130: r4 = false
    //     0xb7b130: add             x4, NULL, #0x30  ; false
    // 0xb7b134: b               #0xb7b13c
    // 0xb7b138: r4 = true
    //     0xb7b138: add             x4, NULL, #0x20  ; true
    // 0xb7b13c: mov             x1, x4
    // 0xb7b140: cmp             w1, NULL
    // 0xb7b144: b.eq            #0xb7b378
    // 0xb7b148: tbnz            w1, #4, #0xb7b378
    // 0xb7b14c: cmp             w3, NULL
    // 0xb7b150: b.ne            #0xb7b15c
    // 0xb7b154: r1 = ""
    //     0xb7b154: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7b158: b               #0xb7b160
    // 0xb7b15c: mov             x1, x3
    // 0xb7b160: r0 = capitalizeFirstWord()
    //     0xb7b160: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb7b164: mov             x2, x0
    // 0xb7b168: ldur            x0, [fp, #-8]
    // 0xb7b16c: stur            x2, [fp, #-0x38]
    // 0xb7b170: LoadField: r1 = r0->field_b
    //     0xb7b170: ldur            w1, [x0, #0xb]
    // 0xb7b174: DecompressPointer r1
    //     0xb7b174: add             x1, x1, HEAP, lsl #32
    // 0xb7b178: cmp             w1, NULL
    // 0xb7b17c: b.eq            #0xb7c3d4
    // 0xb7b180: LoadField: r3 = r1->field_1b
    //     0xb7b180: ldur            w3, [x1, #0x1b]
    // 0xb7b184: DecompressPointer r3
    //     0xb7b184: add             x3, x3, HEAP, lsl #32
    // 0xb7b188: LoadField: r1 = r3->field_7
    //     0xb7b188: ldur            w1, [x3, #7]
    // 0xb7b18c: DecompressPointer r1
    //     0xb7b18c: add             x1, x1, HEAP, lsl #32
    // 0xb7b190: cmp             w1, NULL
    // 0xb7b194: b.ne            #0xb7b1a0
    // 0xb7b198: r1 = Instance_TitleAlignment
    //     0xb7b198: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb7b19c: ldr             x1, [x1, #0x518]
    // 0xb7b1a0: r16 = Instance_TitleAlignment
    //     0xb7b1a0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb7b1a4: ldr             x16, [x16, #0x520]
    // 0xb7b1a8: cmp             w1, w16
    // 0xb7b1ac: b.ne            #0xb7b1b8
    // 0xb7b1b0: r4 = Instance_TextAlign
    //     0xb7b1b0: ldr             x4, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb7b1b4: b               #0xb7b1d4
    // 0xb7b1b8: r16 = Instance_TitleAlignment
    //     0xb7b1b8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb7b1bc: ldr             x16, [x16, #0x518]
    // 0xb7b1c0: cmp             w1, w16
    // 0xb7b1c4: b.ne            #0xb7b1d0
    // 0xb7b1c8: r4 = Instance_TextAlign
    //     0xb7b1c8: ldr             x4, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb7b1cc: b               #0xb7b1d4
    // 0xb7b1d0: r4 = Instance_TextAlign
    //     0xb7b1d0: ldr             x4, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb7b1d4: ldur            x3, [fp, #-0x28]
    // 0xb7b1d8: ldur            x1, [fp, #-0x10]
    // 0xb7b1dc: stur            x4, [fp, #-0x30]
    // 0xb7b1e0: r0 = of()
    //     0xb7b1e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7b1e4: LoadField: r1 = r0->field_87
    //     0xb7b1e4: ldur            w1, [x0, #0x87]
    // 0xb7b1e8: DecompressPointer r1
    //     0xb7b1e8: add             x1, x1, HEAP, lsl #32
    // 0xb7b1ec: LoadField: r0 = r1->field_23
    //     0xb7b1ec: ldur            w0, [x1, #0x23]
    // 0xb7b1f0: DecompressPointer r0
    //     0xb7b1f0: add             x0, x0, HEAP, lsl #32
    // 0xb7b1f4: r16 = Instance_Color
    //     0xb7b1f4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7b1f8: r30 = 32.000000
    //     0xb7b1f8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb7b1fc: ldr             lr, [lr, #0x848]
    // 0xb7b200: stp             lr, x16, [SP]
    // 0xb7b204: mov             x1, x0
    // 0xb7b208: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb7b208: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb7b20c: ldr             x4, [x4, #0x9b8]
    // 0xb7b210: r0 = copyWith()
    //     0xb7b210: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7b214: stur            x0, [fp, #-0x40]
    // 0xb7b218: r0 = Text()
    //     0xb7b218: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7b21c: mov             x1, x0
    // 0xb7b220: ldur            x0, [fp, #-0x38]
    // 0xb7b224: stur            x1, [fp, #-0x48]
    // 0xb7b228: StoreField: r1->field_b = r0
    //     0xb7b228: stur            w0, [x1, #0xb]
    // 0xb7b22c: ldur            x0, [fp, #-0x40]
    // 0xb7b230: StoreField: r1->field_13 = r0
    //     0xb7b230: stur            w0, [x1, #0x13]
    // 0xb7b234: ldur            x0, [fp, #-0x30]
    // 0xb7b238: StoreField: r1->field_1b = r0
    //     0xb7b238: stur            w0, [x1, #0x1b]
    // 0xb7b23c: r0 = Padding()
    //     0xb7b23c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7b240: mov             x3, x0
    // 0xb7b244: r0 = Instance_EdgeInsets
    //     0xb7b244: add             x0, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xb7b248: ldr             x0, [x0, #0x100]
    // 0xb7b24c: stur            x3, [fp, #-0x30]
    // 0xb7b250: StoreField: r3->field_f = r0
    //     0xb7b250: stur            w0, [x3, #0xf]
    // 0xb7b254: ldur            x0, [fp, #-0x48]
    // 0xb7b258: StoreField: r3->field_b = r0
    //     0xb7b258: stur            w0, [x3, #0xb]
    // 0xb7b25c: r1 = Null
    //     0xb7b25c: mov             x1, NULL
    // 0xb7b260: r2 = 4
    //     0xb7b260: movz            x2, #0x4
    // 0xb7b264: r0 = AllocateArray()
    //     0xb7b264: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7b268: mov             x2, x0
    // 0xb7b26c: ldur            x0, [fp, #-0x30]
    // 0xb7b270: stur            x2, [fp, #-0x38]
    // 0xb7b274: StoreField: r2->field_f = r0
    //     0xb7b274: stur            w0, [x2, #0xf]
    // 0xb7b278: r16 = Instance_SizedBox
    //     0xb7b278: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb7b27c: ldr             x16, [x16, #0xc70]
    // 0xb7b280: StoreField: r2->field_13 = r16
    //     0xb7b280: stur            w16, [x2, #0x13]
    // 0xb7b284: r1 = <Widget>
    //     0xb7b284: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7b288: r0 = AllocateGrowableArray()
    //     0xb7b288: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7b28c: mov             x1, x0
    // 0xb7b290: ldur            x0, [fp, #-0x38]
    // 0xb7b294: stur            x1, [fp, #-0x30]
    // 0xb7b298: StoreField: r1->field_f = r0
    //     0xb7b298: stur            w0, [x1, #0xf]
    // 0xb7b29c: r2 = 4
    //     0xb7b29c: movz            x2, #0x4
    // 0xb7b2a0: StoreField: r1->field_b = r2
    //     0xb7b2a0: stur            w2, [x1, #0xb]
    // 0xb7b2a4: r0 = Column()
    //     0xb7b2a4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb7b2a8: mov             x2, x0
    // 0xb7b2ac: r0 = Instance_Axis
    //     0xb7b2ac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7b2b0: stur            x2, [fp, #-0x38]
    // 0xb7b2b4: StoreField: r2->field_f = r0
    //     0xb7b2b4: stur            w0, [x2, #0xf]
    // 0xb7b2b8: r3 = Instance_MainAxisAlignment
    //     0xb7b2b8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7b2bc: ldr             x3, [x3, #0xa08]
    // 0xb7b2c0: StoreField: r2->field_13 = r3
    //     0xb7b2c0: stur            w3, [x2, #0x13]
    // 0xb7b2c4: r4 = Instance_MainAxisSize
    //     0xb7b2c4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7b2c8: ldr             x4, [x4, #0xa10]
    // 0xb7b2cc: ArrayStore: r2[0] = r4  ; List_4
    //     0xb7b2cc: stur            w4, [x2, #0x17]
    // 0xb7b2d0: r5 = Instance_CrossAxisAlignment
    //     0xb7b2d0: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7b2d4: ldr             x5, [x5, #0xa18]
    // 0xb7b2d8: StoreField: r2->field_1b = r5
    //     0xb7b2d8: stur            w5, [x2, #0x1b]
    // 0xb7b2dc: r6 = Instance_VerticalDirection
    //     0xb7b2dc: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7b2e0: ldr             x6, [x6, #0xa20]
    // 0xb7b2e4: StoreField: r2->field_23 = r6
    //     0xb7b2e4: stur            w6, [x2, #0x23]
    // 0xb7b2e8: r7 = Instance_Clip
    //     0xb7b2e8: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7b2ec: ldr             x7, [x7, #0x38]
    // 0xb7b2f0: StoreField: r2->field_2b = r7
    //     0xb7b2f0: stur            w7, [x2, #0x2b]
    // 0xb7b2f4: StoreField: r2->field_2f = rZR
    //     0xb7b2f4: stur            xzr, [x2, #0x2f]
    // 0xb7b2f8: ldur            x1, [fp, #-0x30]
    // 0xb7b2fc: StoreField: r2->field_b = r1
    //     0xb7b2fc: stur            w1, [x2, #0xb]
    // 0xb7b300: ldur            x8, [fp, #-0x28]
    // 0xb7b304: LoadField: r1 = r8->field_b
    //     0xb7b304: ldur            w1, [x8, #0xb]
    // 0xb7b308: LoadField: r9 = r8->field_f
    //     0xb7b308: ldur            w9, [x8, #0xf]
    // 0xb7b30c: DecompressPointer r9
    //     0xb7b30c: add             x9, x9, HEAP, lsl #32
    // 0xb7b310: LoadField: r10 = r9->field_b
    //     0xb7b310: ldur            w10, [x9, #0xb]
    // 0xb7b314: r9 = LoadInt32Instr(r1)
    //     0xb7b314: sbfx            x9, x1, #1, #0x1f
    // 0xb7b318: stur            x9, [fp, #-0x50]
    // 0xb7b31c: r1 = LoadInt32Instr(r10)
    //     0xb7b31c: sbfx            x1, x10, #1, #0x1f
    // 0xb7b320: cmp             x9, x1
    // 0xb7b324: b.ne            #0xb7b330
    // 0xb7b328: mov             x1, x8
    // 0xb7b32c: r0 = _growToNextCapacity()
    //     0xb7b32c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7b330: ldur            x2, [fp, #-0x28]
    // 0xb7b334: ldur            x3, [fp, #-0x50]
    // 0xb7b338: add             x0, x3, #1
    // 0xb7b33c: lsl             x1, x0, #1
    // 0xb7b340: StoreField: r2->field_b = r1
    //     0xb7b340: stur            w1, [x2, #0xb]
    // 0xb7b344: LoadField: r1 = r2->field_f
    //     0xb7b344: ldur            w1, [x2, #0xf]
    // 0xb7b348: DecompressPointer r1
    //     0xb7b348: add             x1, x1, HEAP, lsl #32
    // 0xb7b34c: ldur            x0, [fp, #-0x38]
    // 0xb7b350: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7b350: add             x25, x1, x3, lsl #2
    //     0xb7b354: add             x25, x25, #0xf
    //     0xb7b358: str             w0, [x25]
    //     0xb7b35c: tbz             w0, #0, #0xb7b378
    //     0xb7b360: ldurb           w16, [x1, #-1]
    //     0xb7b364: ldurb           w17, [x0, #-1]
    //     0xb7b368: and             x16, x17, x16, lsr #2
    //     0xb7b36c: tst             x16, HEAP, lsr #32
    //     0xb7b370: b.eq            #0xb7b378
    //     0xb7b374: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7b378: ldur            x0, [fp, #-8]
    // 0xb7b37c: LoadField: r1 = r0->field_b
    //     0xb7b37c: ldur            w1, [x0, #0xb]
    // 0xb7b380: DecompressPointer r1
    //     0xb7b380: add             x1, x1, HEAP, lsl #32
    // 0xb7b384: cmp             w1, NULL
    // 0xb7b388: b.eq            #0xb7c3d8
    // 0xb7b38c: LoadField: r3 = r1->field_13
    //     0xb7b38c: ldur            w3, [x1, #0x13]
    // 0xb7b390: DecompressPointer r3
    //     0xb7b390: add             x3, x3, HEAP, lsl #32
    // 0xb7b394: cmp             w3, NULL
    // 0xb7b398: b.ne            #0xb7b3a4
    // 0xb7b39c: r1 = Null
    //     0xb7b39c: mov             x1, NULL
    // 0xb7b3a0: b               #0xb7b3d0
    // 0xb7b3a4: LoadField: r1 = r3->field_7
    //     0xb7b3a4: ldur            w1, [x3, #7]
    // 0xb7b3a8: DecompressPointer r1
    //     0xb7b3a8: add             x1, x1, HEAP, lsl #32
    // 0xb7b3ac: cmp             w1, NULL
    // 0xb7b3b0: b.ne            #0xb7b3bc
    // 0xb7b3b4: r1 = Null
    //     0xb7b3b4: mov             x1, NULL
    // 0xb7b3b8: b               #0xb7b3d0
    // 0xb7b3bc: LoadField: r3 = r1->field_7
    //     0xb7b3bc: ldur            w3, [x1, #7]
    // 0xb7b3c0: cbnz            w3, #0xb7b3cc
    // 0xb7b3c4: r1 = false
    //     0xb7b3c4: add             x1, NULL, #0x30  ; false
    // 0xb7b3c8: b               #0xb7b3d0
    // 0xb7b3cc: r1 = true
    //     0xb7b3cc: add             x1, NULL, #0x20  ; true
    // 0xb7b3d0: cmp             w1, NULL
    // 0xb7b3d4: b.ne            #0xb7b3e0
    // 0xb7b3d8: r3 = false
    //     0xb7b3d8: add             x3, NULL, #0x30  ; false
    // 0xb7b3dc: b               #0xb7b3e4
    // 0xb7b3e0: mov             x3, x1
    // 0xb7b3e4: ldur            x1, [fp, #-0x10]
    // 0xb7b3e8: stur            x3, [fp, #-0x30]
    // 0xb7b3ec: r0 = of()
    //     0xb7b3ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7b3f0: LoadField: r1 = r0->field_5b
    //     0xb7b3f0: ldur            w1, [x0, #0x5b]
    // 0xb7b3f4: DecompressPointer r1
    //     0xb7b3f4: add             x1, x1, HEAP, lsl #32
    // 0xb7b3f8: stur            x1, [fp, #-0x38]
    // 0xb7b3fc: r0 = BoxDecoration()
    //     0xb7b3fc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb7b400: mov             x2, x0
    // 0xb7b404: ldur            x0, [fp, #-0x38]
    // 0xb7b408: stur            x2, [fp, #-0x40]
    // 0xb7b40c: StoreField: r2->field_7 = r0
    //     0xb7b40c: stur            w0, [x2, #7]
    // 0xb7b410: r0 = Instance_BorderRadius
    //     0xb7b410: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb7b414: ldr             x0, [x0, #0x460]
    // 0xb7b418: StoreField: r2->field_13 = r0
    //     0xb7b418: stur            w0, [x2, #0x13]
    // 0xb7b41c: r0 = Instance_BoxShape
    //     0xb7b41c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb7b420: ldr             x0, [x0, #0x80]
    // 0xb7b424: StoreField: r2->field_23 = r0
    //     0xb7b424: stur            w0, [x2, #0x23]
    // 0xb7b428: ldur            x3, [fp, #-8]
    // 0xb7b42c: LoadField: r1 = r3->field_b
    //     0xb7b42c: ldur            w1, [x3, #0xb]
    // 0xb7b430: DecompressPointer r1
    //     0xb7b430: add             x1, x1, HEAP, lsl #32
    // 0xb7b434: cmp             w1, NULL
    // 0xb7b438: b.eq            #0xb7c3dc
    // 0xb7b43c: LoadField: r4 = r1->field_13
    //     0xb7b43c: ldur            w4, [x1, #0x13]
    // 0xb7b440: DecompressPointer r4
    //     0xb7b440: add             x4, x4, HEAP, lsl #32
    // 0xb7b444: cmp             w4, NULL
    // 0xb7b448: b.ne            #0xb7b454
    // 0xb7b44c: r1 = Null
    //     0xb7b44c: mov             x1, NULL
    // 0xb7b450: b               #0xb7b45c
    // 0xb7b454: LoadField: r1 = r4->field_7
    //     0xb7b454: ldur            w1, [x4, #7]
    // 0xb7b458: DecompressPointer r1
    //     0xb7b458: add             x1, x1, HEAP, lsl #32
    // 0xb7b45c: cmp             w1, NULL
    // 0xb7b460: b.ne            #0xb7b46c
    // 0xb7b464: r6 = ""
    //     0xb7b464: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7b468: b               #0xb7b470
    // 0xb7b46c: mov             x6, x1
    // 0xb7b470: ldur            x4, [fp, #-0x28]
    // 0xb7b474: ldur            x5, [fp, #-0x30]
    // 0xb7b478: ldur            x1, [fp, #-0x10]
    // 0xb7b47c: stur            x6, [fp, #-0x38]
    // 0xb7b480: r0 = of()
    //     0xb7b480: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7b484: LoadField: r1 = r0->field_87
    //     0xb7b484: ldur            w1, [x0, #0x87]
    // 0xb7b488: DecompressPointer r1
    //     0xb7b488: add             x1, x1, HEAP, lsl #32
    // 0xb7b48c: LoadField: r0 = r1->field_2b
    //     0xb7b48c: ldur            w0, [x1, #0x2b]
    // 0xb7b490: DecompressPointer r0
    //     0xb7b490: add             x0, x0, HEAP, lsl #32
    // 0xb7b494: r16 = 16.000000
    //     0xb7b494: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb7b498: ldr             x16, [x16, #0x188]
    // 0xb7b49c: r30 = Instance_Color
    //     0xb7b49c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7b4a0: stp             lr, x16, [SP]
    // 0xb7b4a4: mov             x1, x0
    // 0xb7b4a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7b4a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7b4ac: ldr             x4, [x4, #0xaa0]
    // 0xb7b4b0: r0 = copyWith()
    //     0xb7b4b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7b4b4: stur            x0, [fp, #-0x48]
    // 0xb7b4b8: r0 = Text()
    //     0xb7b4b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7b4bc: mov             x1, x0
    // 0xb7b4c0: ldur            x0, [fp, #-0x38]
    // 0xb7b4c4: stur            x1, [fp, #-0x58]
    // 0xb7b4c8: StoreField: r1->field_b = r0
    //     0xb7b4c8: stur            w0, [x1, #0xb]
    // 0xb7b4cc: ldur            x0, [fp, #-0x48]
    // 0xb7b4d0: StoreField: r1->field_13 = r0
    //     0xb7b4d0: stur            w0, [x1, #0x13]
    // 0xb7b4d4: r0 = Center()
    //     0xb7b4d4: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb7b4d8: mov             x1, x0
    // 0xb7b4dc: r0 = Instance_Alignment
    //     0xb7b4dc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb7b4e0: ldr             x0, [x0, #0xb10]
    // 0xb7b4e4: stur            x1, [fp, #-0x38]
    // 0xb7b4e8: StoreField: r1->field_f = r0
    //     0xb7b4e8: stur            w0, [x1, #0xf]
    // 0xb7b4ec: ldur            x0, [fp, #-0x58]
    // 0xb7b4f0: StoreField: r1->field_b = r0
    //     0xb7b4f0: stur            w0, [x1, #0xb]
    // 0xb7b4f4: r0 = Container()
    //     0xb7b4f4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7b4f8: stur            x0, [fp, #-0x48]
    // 0xb7b4fc: r16 = 40.000000
    //     0xb7b4fc: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb7b500: ldr             x16, [x16, #8]
    // 0xb7b504: r30 = 110.000000
    //     0xb7b504: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb7b508: ldr             lr, [lr, #0x770]
    // 0xb7b50c: stp             lr, x16, [SP, #0x10]
    // 0xb7b510: ldur            x16, [fp, #-0x40]
    // 0xb7b514: ldur            lr, [fp, #-0x38]
    // 0xb7b518: stp             lr, x16, [SP]
    // 0xb7b51c: mov             x1, x0
    // 0xb7b520: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb7b520: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb7b524: ldr             x4, [x4, #0x8c0]
    // 0xb7b528: r0 = Container()
    //     0xb7b528: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7b52c: r0 = InkWell()
    //     0xb7b52c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb7b530: mov             x3, x0
    // 0xb7b534: ldur            x0, [fp, #-0x48]
    // 0xb7b538: stur            x3, [fp, #-0x38]
    // 0xb7b53c: StoreField: r3->field_b = r0
    //     0xb7b53c: stur            w0, [x3, #0xb]
    // 0xb7b540: ldur            x2, [fp, #-0x20]
    // 0xb7b544: r1 = Function '<anonymous closure>':.
    //     0xb7b544: add             x1, PP, #0x55, lsl #12  ; [pp+0x558f0] AnonymousClosure: (0xb7f37c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xb7b034)
    //     0xb7b548: ldr             x1, [x1, #0x8f0]
    // 0xb7b54c: r0 = AllocateClosure()
    //     0xb7b54c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7b550: mov             x1, x0
    // 0xb7b554: ldur            x0, [fp, #-0x38]
    // 0xb7b558: StoreField: r0->field_f = r1
    //     0xb7b558: stur            w1, [x0, #0xf]
    // 0xb7b55c: r1 = true
    //     0xb7b55c: add             x1, NULL, #0x20  ; true
    // 0xb7b560: StoreField: r0->field_43 = r1
    //     0xb7b560: stur            w1, [x0, #0x43]
    // 0xb7b564: r2 = Instance_BoxShape
    //     0xb7b564: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb7b568: ldr             x2, [x2, #0x80]
    // 0xb7b56c: StoreField: r0->field_47 = r2
    //     0xb7b56c: stur            w2, [x0, #0x47]
    // 0xb7b570: StoreField: r0->field_6f = r1
    //     0xb7b570: stur            w1, [x0, #0x6f]
    // 0xb7b574: r3 = false
    //     0xb7b574: add             x3, NULL, #0x30  ; false
    // 0xb7b578: StoreField: r0->field_73 = r3
    //     0xb7b578: stur            w3, [x0, #0x73]
    // 0xb7b57c: StoreField: r0->field_83 = r1
    //     0xb7b57c: stur            w1, [x0, #0x83]
    // 0xb7b580: StoreField: r0->field_7b = r3
    //     0xb7b580: stur            w3, [x0, #0x7b]
    // 0xb7b584: r0 = Visibility()
    //     0xb7b584: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7b588: mov             x2, x0
    // 0xb7b58c: ldur            x0, [fp, #-0x38]
    // 0xb7b590: stur            x2, [fp, #-0x40]
    // 0xb7b594: StoreField: r2->field_b = r0
    //     0xb7b594: stur            w0, [x2, #0xb]
    // 0xb7b598: r0 = Instance_SizedBox
    //     0xb7b598: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7b59c: StoreField: r2->field_f = r0
    //     0xb7b59c: stur            w0, [x2, #0xf]
    // 0xb7b5a0: ldur            x1, [fp, #-0x30]
    // 0xb7b5a4: StoreField: r2->field_13 = r1
    //     0xb7b5a4: stur            w1, [x2, #0x13]
    // 0xb7b5a8: r3 = false
    //     0xb7b5a8: add             x3, NULL, #0x30  ; false
    // 0xb7b5ac: ArrayStore: r2[0] = r3  ; List_4
    //     0xb7b5ac: stur            w3, [x2, #0x17]
    // 0xb7b5b0: StoreField: r2->field_1b = r3
    //     0xb7b5b0: stur            w3, [x2, #0x1b]
    // 0xb7b5b4: StoreField: r2->field_1f = r3
    //     0xb7b5b4: stur            w3, [x2, #0x1f]
    // 0xb7b5b8: StoreField: r2->field_23 = r3
    //     0xb7b5b8: stur            w3, [x2, #0x23]
    // 0xb7b5bc: StoreField: r2->field_27 = r3
    //     0xb7b5bc: stur            w3, [x2, #0x27]
    // 0xb7b5c0: StoreField: r2->field_2b = r3
    //     0xb7b5c0: stur            w3, [x2, #0x2b]
    // 0xb7b5c4: ldur            x4, [fp, #-0x28]
    // 0xb7b5c8: LoadField: r1 = r4->field_b
    //     0xb7b5c8: ldur            w1, [x4, #0xb]
    // 0xb7b5cc: LoadField: r5 = r4->field_f
    //     0xb7b5cc: ldur            w5, [x4, #0xf]
    // 0xb7b5d0: DecompressPointer r5
    //     0xb7b5d0: add             x5, x5, HEAP, lsl #32
    // 0xb7b5d4: LoadField: r6 = r5->field_b
    //     0xb7b5d4: ldur            w6, [x5, #0xb]
    // 0xb7b5d8: r5 = LoadInt32Instr(r1)
    //     0xb7b5d8: sbfx            x5, x1, #1, #0x1f
    // 0xb7b5dc: stur            x5, [fp, #-0x50]
    // 0xb7b5e0: r1 = LoadInt32Instr(r6)
    //     0xb7b5e0: sbfx            x1, x6, #1, #0x1f
    // 0xb7b5e4: cmp             x5, x1
    // 0xb7b5e8: b.ne            #0xb7b5f4
    // 0xb7b5ec: mov             x1, x4
    // 0xb7b5f0: r0 = _growToNextCapacity()
    //     0xb7b5f0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7b5f4: ldur            x2, [fp, #-0x28]
    // 0xb7b5f8: ldur            x3, [fp, #-0x50]
    // 0xb7b5fc: add             x4, x3, #1
    // 0xb7b600: stur            x4, [fp, #-0x60]
    // 0xb7b604: lsl             x0, x4, #1
    // 0xb7b608: StoreField: r2->field_b = r0
    //     0xb7b608: stur            w0, [x2, #0xb]
    // 0xb7b60c: LoadField: r5 = r2->field_f
    //     0xb7b60c: ldur            w5, [x2, #0xf]
    // 0xb7b610: DecompressPointer r5
    //     0xb7b610: add             x5, x5, HEAP, lsl #32
    // 0xb7b614: mov             x1, x5
    // 0xb7b618: ldur            x0, [fp, #-0x40]
    // 0xb7b61c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7b61c: add             x25, x1, x3, lsl #2
    //     0xb7b620: add             x25, x25, #0xf
    //     0xb7b624: str             w0, [x25]
    //     0xb7b628: tbz             w0, #0, #0xb7b644
    //     0xb7b62c: ldurb           w16, [x1, #-1]
    //     0xb7b630: ldurb           w17, [x0, #-1]
    //     0xb7b634: and             x16, x17, x16, lsr #2
    //     0xb7b638: tst             x16, HEAP, lsr #32
    //     0xb7b63c: b.eq            #0xb7b644
    //     0xb7b640: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7b644: LoadField: r0 = r5->field_b
    //     0xb7b644: ldur            w0, [x5, #0xb]
    // 0xb7b648: r1 = LoadInt32Instr(r0)
    //     0xb7b648: sbfx            x1, x0, #1, #0x1f
    // 0xb7b64c: cmp             x4, x1
    // 0xb7b650: b.ne            #0xb7b65c
    // 0xb7b654: mov             x1, x2
    // 0xb7b658: r0 = _growToNextCapacity()
    //     0xb7b658: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7b65c: ldur            x2, [fp, #-8]
    // 0xb7b660: ldur            x1, [fp, #-0x28]
    // 0xb7b664: ldur            x0, [fp, #-0x60]
    // 0xb7b668: add             x3, x0, #1
    // 0xb7b66c: lsl             x4, x3, #1
    // 0xb7b670: StoreField: r1->field_b = r4
    //     0xb7b670: stur            w4, [x1, #0xb]
    // 0xb7b674: LoadField: r3 = r1->field_f
    //     0xb7b674: ldur            w3, [x1, #0xf]
    // 0xb7b678: DecompressPointer r3
    //     0xb7b678: add             x3, x3, HEAP, lsl #32
    // 0xb7b67c: add             x4, x3, x0, lsl #2
    // 0xb7b680: r16 = Instance_SizedBox
    //     0xb7b680: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb7b684: ldr             x16, [x16, #0x8f0]
    // 0xb7b688: StoreField: r4->field_f = r16
    //     0xb7b688: stur            w16, [x4, #0xf]
    // 0xb7b68c: LoadField: r0 = r2->field_b
    //     0xb7b68c: ldur            w0, [x2, #0xb]
    // 0xb7b690: DecompressPointer r0
    //     0xb7b690: add             x0, x0, HEAP, lsl #32
    // 0xb7b694: cmp             w0, NULL
    // 0xb7b698: b.eq            #0xb7c3e0
    // 0xb7b69c: LoadField: r3 = r0->field_2f
    //     0xb7b69c: ldur            w3, [x0, #0x2f]
    // 0xb7b6a0: DecompressPointer r3
    //     0xb7b6a0: add             x3, x3, HEAP, lsl #32
    // 0xb7b6a4: LoadField: r0 = r3->field_f
    //     0xb7b6a4: ldur            w0, [x3, #0xf]
    // 0xb7b6a8: DecompressPointer r0
    //     0xb7b6a8: add             x0, x0, HEAP, lsl #32
    // 0xb7b6ac: cmp             w0, NULL
    // 0xb7b6b0: r16 = true
    //     0xb7b6b0: add             x16, NULL, #0x20  ; true
    // 0xb7b6b4: r17 = false
    //     0xb7b6b4: add             x17, NULL, #0x30  ; false
    // 0xb7b6b8: csel            x4, x16, x17, ne
    // 0xb7b6bc: stur            x4, [fp, #-0x38]
    // 0xb7b6c0: LoadField: r0 = r3->field_13
    //     0xb7b6c0: ldur            w0, [x3, #0x13]
    // 0xb7b6c4: DecompressPointer r0
    //     0xb7b6c4: add             x0, x0, HEAP, lsl #32
    // 0xb7b6c8: stur            x0, [fp, #-0x30]
    // 0xb7b6cc: cmp             w0, NULL
    // 0xb7b6d0: b.ne            #0xb7b6dc
    // 0xb7b6d4: r3 = Null
    //     0xb7b6d4: mov             x3, NULL
    // 0xb7b6d8: b               #0xb7b6e4
    // 0xb7b6dc: LoadField: r3 = r0->field_7
    //     0xb7b6dc: ldur            w3, [x0, #7]
    // 0xb7b6e0: DecompressPointer r3
    //     0xb7b6e0: add             x3, x3, HEAP, lsl #32
    // 0xb7b6e4: cmp             w3, NULL
    // 0xb7b6e8: b.ne            #0xb7b6f4
    // 0xb7b6ec: r3 = 0
    //     0xb7b6ec: movz            x3, #0
    // 0xb7b6f0: b               #0xb7b704
    // 0xb7b6f4: r5 = LoadInt32Instr(r3)
    //     0xb7b6f4: sbfx            x5, x3, #1, #0x1f
    //     0xb7b6f8: tbz             w3, #0, #0xb7b700
    //     0xb7b6fc: ldur            x5, [x3, #7]
    // 0xb7b700: mov             x3, x5
    // 0xb7b704: stur            x3, [fp, #-0x68]
    // 0xb7b708: cmp             w0, NULL
    // 0xb7b70c: b.ne            #0xb7b718
    // 0xb7b710: r5 = Null
    //     0xb7b710: mov             x5, NULL
    // 0xb7b714: b               #0xb7b720
    // 0xb7b718: LoadField: r5 = r0->field_b
    //     0xb7b718: ldur            w5, [x0, #0xb]
    // 0xb7b71c: DecompressPointer r5
    //     0xb7b71c: add             x5, x5, HEAP, lsl #32
    // 0xb7b720: cmp             w5, NULL
    // 0xb7b724: b.ne            #0xb7b730
    // 0xb7b728: r5 = 0
    //     0xb7b728: movz            x5, #0
    // 0xb7b72c: b               #0xb7b740
    // 0xb7b730: r6 = LoadInt32Instr(r5)
    //     0xb7b730: sbfx            x6, x5, #1, #0x1f
    //     0xb7b734: tbz             w5, #0, #0xb7b73c
    //     0xb7b738: ldur            x6, [x5, #7]
    // 0xb7b73c: mov             x5, x6
    // 0xb7b740: stur            x5, [fp, #-0x60]
    // 0xb7b744: cmp             w0, NULL
    // 0xb7b748: b.ne            #0xb7b754
    // 0xb7b74c: r6 = Null
    //     0xb7b74c: mov             x6, NULL
    // 0xb7b750: b               #0xb7b75c
    // 0xb7b754: LoadField: r6 = r0->field_f
    //     0xb7b754: ldur            w6, [x0, #0xf]
    // 0xb7b758: DecompressPointer r6
    //     0xb7b758: add             x6, x6, HEAP, lsl #32
    // 0xb7b75c: cmp             w6, NULL
    // 0xb7b760: b.ne            #0xb7b76c
    // 0xb7b764: r6 = 0
    //     0xb7b764: movz            x6, #0
    // 0xb7b768: b               #0xb7b77c
    // 0xb7b76c: r7 = LoadInt32Instr(r6)
    //     0xb7b76c: sbfx            x7, x6, #1, #0x1f
    //     0xb7b770: tbz             w6, #0, #0xb7b778
    //     0xb7b774: ldur            x7, [x6, #7]
    // 0xb7b778: mov             x6, x7
    // 0xb7b77c: stur            x6, [fp, #-0x50]
    // 0xb7b780: r0 = Color()
    //     0xb7b780: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb7b784: mov             x1, x0
    // 0xb7b788: r0 = Instance_ColorSpace
    //     0xb7b788: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb7b78c: stur            x1, [fp, #-0x40]
    // 0xb7b790: StoreField: r1->field_27 = r0
    //     0xb7b790: stur            w0, [x1, #0x27]
    // 0xb7b794: d0 = 1.000000
    //     0xb7b794: fmov            d0, #1.00000000
    // 0xb7b798: StoreField: r1->field_7 = d0
    //     0xb7b798: stur            d0, [x1, #7]
    // 0xb7b79c: ldur            x2, [fp, #-0x68]
    // 0xb7b7a0: ubfx            x2, x2, #0, #0x20
    // 0xb7b7a4: and             w3, w2, #0xff
    // 0xb7b7a8: ubfx            x3, x3, #0, #0x20
    // 0xb7b7ac: scvtf           d0, x3
    // 0xb7b7b0: d1 = 255.000000
    //     0xb7b7b0: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb7b7b4: fdiv            d2, d0, d1
    // 0xb7b7b8: StoreField: r1->field_f = d2
    //     0xb7b7b8: stur            d2, [x1, #0xf]
    // 0xb7b7bc: ldur            x2, [fp, #-0x60]
    // 0xb7b7c0: ubfx            x2, x2, #0, #0x20
    // 0xb7b7c4: and             w3, w2, #0xff
    // 0xb7b7c8: ubfx            x3, x3, #0, #0x20
    // 0xb7b7cc: scvtf           d0, x3
    // 0xb7b7d0: fdiv            d2, d0, d1
    // 0xb7b7d4: ArrayStore: r1[0] = d2  ; List_8
    //     0xb7b7d4: stur            d2, [x1, #0x17]
    // 0xb7b7d8: ldur            x2, [fp, #-0x50]
    // 0xb7b7dc: ubfx            x2, x2, #0, #0x20
    // 0xb7b7e0: and             w3, w2, #0xff
    // 0xb7b7e4: ubfx            x3, x3, #0, #0x20
    // 0xb7b7e8: scvtf           d0, x3
    // 0xb7b7ec: fdiv            d2, d0, d1
    // 0xb7b7f0: StoreField: r1->field_1f = d2
    //     0xb7b7f0: stur            d2, [x1, #0x1f]
    // 0xb7b7f4: ldur            x2, [fp, #-0x30]
    // 0xb7b7f8: cmp             w2, NULL
    // 0xb7b7fc: b.ne            #0xb7b808
    // 0xb7b800: r3 = Null
    //     0xb7b800: mov             x3, NULL
    // 0xb7b804: b               #0xb7b810
    // 0xb7b808: LoadField: r3 = r2->field_7
    //     0xb7b808: ldur            w3, [x2, #7]
    // 0xb7b80c: DecompressPointer r3
    //     0xb7b80c: add             x3, x3, HEAP, lsl #32
    // 0xb7b810: cmp             w3, NULL
    // 0xb7b814: b.ne            #0xb7b820
    // 0xb7b818: r3 = 0
    //     0xb7b818: movz            x3, #0
    // 0xb7b81c: b               #0xb7b830
    // 0xb7b820: r4 = LoadInt32Instr(r3)
    //     0xb7b820: sbfx            x4, x3, #1, #0x1f
    //     0xb7b824: tbz             w3, #0, #0xb7b82c
    //     0xb7b828: ldur            x4, [x3, #7]
    // 0xb7b82c: mov             x3, x4
    // 0xb7b830: stur            x3, [fp, #-0x68]
    // 0xb7b834: cmp             w2, NULL
    // 0xb7b838: b.ne            #0xb7b844
    // 0xb7b83c: r4 = Null
    //     0xb7b83c: mov             x4, NULL
    // 0xb7b840: b               #0xb7b84c
    // 0xb7b844: LoadField: r4 = r2->field_b
    //     0xb7b844: ldur            w4, [x2, #0xb]
    // 0xb7b848: DecompressPointer r4
    //     0xb7b848: add             x4, x4, HEAP, lsl #32
    // 0xb7b84c: cmp             w4, NULL
    // 0xb7b850: b.ne            #0xb7b85c
    // 0xb7b854: r4 = 0
    //     0xb7b854: movz            x4, #0
    // 0xb7b858: b               #0xb7b86c
    // 0xb7b85c: r5 = LoadInt32Instr(r4)
    //     0xb7b85c: sbfx            x5, x4, #1, #0x1f
    //     0xb7b860: tbz             w4, #0, #0xb7b868
    //     0xb7b864: ldur            x5, [x4, #7]
    // 0xb7b868: mov             x4, x5
    // 0xb7b86c: stur            x4, [fp, #-0x60]
    // 0xb7b870: cmp             w2, NULL
    // 0xb7b874: b.ne            #0xb7b880
    // 0xb7b878: r2 = Null
    //     0xb7b878: mov             x2, NULL
    // 0xb7b87c: b               #0xb7b88c
    // 0xb7b880: LoadField: r5 = r2->field_f
    //     0xb7b880: ldur            w5, [x2, #0xf]
    // 0xb7b884: DecompressPointer r5
    //     0xb7b884: add             x5, x5, HEAP, lsl #32
    // 0xb7b888: mov             x2, x5
    // 0xb7b88c: cmp             w2, NULL
    // 0xb7b890: b.ne            #0xb7b89c
    // 0xb7b894: r7 = 0
    //     0xb7b894: movz            x7, #0
    // 0xb7b898: b               #0xb7b8ac
    // 0xb7b89c: r5 = LoadInt32Instr(r2)
    //     0xb7b89c: sbfx            x5, x2, #1, #0x1f
    //     0xb7b8a0: tbz             w2, #0, #0xb7b8a8
    //     0xb7b8a4: ldur            x5, [x2, #7]
    // 0xb7b8a8: mov             x7, x5
    // 0xb7b8ac: ldur            x5, [fp, #-8]
    // 0xb7b8b0: ldur            x2, [fp, #-0x28]
    // 0xb7b8b4: ldur            x6, [fp, #-0x38]
    // 0xb7b8b8: stur            x7, [fp, #-0x50]
    // 0xb7b8bc: r0 = Color()
    //     0xb7b8bc: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb7b8c0: mov             x3, x0
    // 0xb7b8c4: r0 = Instance_ColorSpace
    //     0xb7b8c4: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb7b8c8: stur            x3, [fp, #-0x30]
    // 0xb7b8cc: StoreField: r3->field_27 = r0
    //     0xb7b8cc: stur            w0, [x3, #0x27]
    // 0xb7b8d0: d0 = 0.700000
    //     0xb7b8d0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb7b8d4: ldr             d0, [x17, #0xf48]
    // 0xb7b8d8: StoreField: r3->field_7 = d0
    //     0xb7b8d8: stur            d0, [x3, #7]
    // 0xb7b8dc: ldur            x0, [fp, #-0x68]
    // 0xb7b8e0: ubfx            x0, x0, #0, #0x20
    // 0xb7b8e4: and             w1, w0, #0xff
    // 0xb7b8e8: ubfx            x1, x1, #0, #0x20
    // 0xb7b8ec: scvtf           d0, x1
    // 0xb7b8f0: d1 = 255.000000
    //     0xb7b8f0: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb7b8f4: fdiv            d2, d0, d1
    // 0xb7b8f8: StoreField: r3->field_f = d2
    //     0xb7b8f8: stur            d2, [x3, #0xf]
    // 0xb7b8fc: ldur            x0, [fp, #-0x60]
    // 0xb7b900: ubfx            x0, x0, #0, #0x20
    // 0xb7b904: and             w1, w0, #0xff
    // 0xb7b908: ubfx            x1, x1, #0, #0x20
    // 0xb7b90c: scvtf           d0, x1
    // 0xb7b910: fdiv            d2, d0, d1
    // 0xb7b914: ArrayStore: r3[0] = d2  ; List_8
    //     0xb7b914: stur            d2, [x3, #0x17]
    // 0xb7b918: ldur            x0, [fp, #-0x50]
    // 0xb7b91c: ubfx            x0, x0, #0, #0x20
    // 0xb7b920: and             w1, w0, #0xff
    // 0xb7b924: ubfx            x1, x1, #0, #0x20
    // 0xb7b928: scvtf           d0, x1
    // 0xb7b92c: fdiv            d2, d0, d1
    // 0xb7b930: StoreField: r3->field_1f = d2
    //     0xb7b930: stur            d2, [x3, #0x1f]
    // 0xb7b934: r1 = Null
    //     0xb7b934: mov             x1, NULL
    // 0xb7b938: r2 = 4
    //     0xb7b938: movz            x2, #0x4
    // 0xb7b93c: r0 = AllocateArray()
    //     0xb7b93c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7b940: mov             x2, x0
    // 0xb7b944: ldur            x0, [fp, #-0x40]
    // 0xb7b948: stur            x2, [fp, #-0x48]
    // 0xb7b94c: StoreField: r2->field_f = r0
    //     0xb7b94c: stur            w0, [x2, #0xf]
    // 0xb7b950: ldur            x0, [fp, #-0x30]
    // 0xb7b954: StoreField: r2->field_13 = r0
    //     0xb7b954: stur            w0, [x2, #0x13]
    // 0xb7b958: r1 = <Color>
    //     0xb7b958: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb7b95c: ldr             x1, [x1, #0xf80]
    // 0xb7b960: r0 = AllocateGrowableArray()
    //     0xb7b960: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7b964: mov             x1, x0
    // 0xb7b968: ldur            x0, [fp, #-0x48]
    // 0xb7b96c: stur            x1, [fp, #-0x30]
    // 0xb7b970: StoreField: r1->field_f = r0
    //     0xb7b970: stur            w0, [x1, #0xf]
    // 0xb7b974: r2 = 4
    //     0xb7b974: movz            x2, #0x4
    // 0xb7b978: StoreField: r1->field_b = r2
    //     0xb7b978: stur            w2, [x1, #0xb]
    // 0xb7b97c: r0 = LinearGradient()
    //     0xb7b97c: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb7b980: mov             x1, x0
    // 0xb7b984: r0 = Instance_Alignment
    //     0xb7b984: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb7b988: ldr             x0, [x0, #0xce0]
    // 0xb7b98c: stur            x1, [fp, #-0x40]
    // 0xb7b990: StoreField: r1->field_13 = r0
    //     0xb7b990: stur            w0, [x1, #0x13]
    // 0xb7b994: r0 = Instance_Alignment
    //     0xb7b994: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb7b998: ldr             x0, [x0, #0xce8]
    // 0xb7b99c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7b99c: stur            w0, [x1, #0x17]
    // 0xb7b9a0: r0 = Instance_TileMode
    //     0xb7b9a0: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb7b9a4: ldr             x0, [x0, #0xcf0]
    // 0xb7b9a8: StoreField: r1->field_1b = r0
    //     0xb7b9a8: stur            w0, [x1, #0x1b]
    // 0xb7b9ac: ldur            x0, [fp, #-0x30]
    // 0xb7b9b0: StoreField: r1->field_7 = r0
    //     0xb7b9b0: stur            w0, [x1, #7]
    // 0xb7b9b4: r0 = BoxDecoration()
    //     0xb7b9b4: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb7b9b8: mov             x2, x0
    // 0xb7b9bc: r0 = Instance_BorderRadius
    //     0xb7b9bc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb7b9c0: ldr             x0, [x0, #0xe10]
    // 0xb7b9c4: stur            x2, [fp, #-0x30]
    // 0xb7b9c8: StoreField: r2->field_13 = r0
    //     0xb7b9c8: stur            w0, [x2, #0x13]
    // 0xb7b9cc: ldur            x0, [fp, #-0x40]
    // 0xb7b9d0: StoreField: r2->field_1b = r0
    //     0xb7b9d0: stur            w0, [x2, #0x1b]
    // 0xb7b9d4: r0 = Instance_BoxShape
    //     0xb7b9d4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb7b9d8: ldr             x0, [x0, #0x80]
    // 0xb7b9dc: StoreField: r2->field_23 = r0
    //     0xb7b9dc: stur            w0, [x2, #0x23]
    // 0xb7b9e0: ldur            x1, [fp, #-0x10]
    // 0xb7b9e4: r0 = of()
    //     0xb7b9e4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7b9e8: LoadField: r1 = r0->field_87
    //     0xb7b9e8: ldur            w1, [x0, #0x87]
    // 0xb7b9ec: DecompressPointer r1
    //     0xb7b9ec: add             x1, x1, HEAP, lsl #32
    // 0xb7b9f0: LoadField: r0 = r1->field_7
    //     0xb7b9f0: ldur            w0, [x1, #7]
    // 0xb7b9f4: DecompressPointer r0
    //     0xb7b9f4: add             x0, x0, HEAP, lsl #32
    // 0xb7b9f8: r16 = 16.000000
    //     0xb7b9f8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb7b9fc: ldr             x16, [x16, #0x188]
    // 0xb7ba00: r30 = Instance_Color
    //     0xb7ba00: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7ba04: stp             lr, x16, [SP]
    // 0xb7ba08: mov             x1, x0
    // 0xb7ba0c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7ba0c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7ba10: ldr             x4, [x4, #0xaa0]
    // 0xb7ba14: r0 = copyWith()
    //     0xb7ba14: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7ba18: stur            x0, [fp, #-0x40]
    // 0xb7ba1c: r0 = TextSpan()
    //     0xb7ba1c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7ba20: mov             x2, x0
    // 0xb7ba24: r0 = "BUMPER OFFER\n"
    //     0xb7ba24: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xb7ba28: ldr             x0, [x0, #0x338]
    // 0xb7ba2c: stur            x2, [fp, #-0x48]
    // 0xb7ba30: StoreField: r2->field_b = r0
    //     0xb7ba30: stur            w0, [x2, #0xb]
    // 0xb7ba34: r0 = Instance__DeferringMouseCursor
    //     0xb7ba34: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7ba38: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7ba38: stur            w0, [x2, #0x17]
    // 0xb7ba3c: ldur            x1, [fp, #-0x40]
    // 0xb7ba40: StoreField: r2->field_7 = r1
    //     0xb7ba40: stur            w1, [x2, #7]
    // 0xb7ba44: ldur            x1, [fp, #-0x10]
    // 0xb7ba48: r0 = of()
    //     0xb7ba48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7ba4c: LoadField: r1 = r0->field_87
    //     0xb7ba4c: ldur            w1, [x0, #0x87]
    // 0xb7ba50: DecompressPointer r1
    //     0xb7ba50: add             x1, x1, HEAP, lsl #32
    // 0xb7ba54: LoadField: r0 = r1->field_2b
    //     0xb7ba54: ldur            w0, [x1, #0x2b]
    // 0xb7ba58: DecompressPointer r0
    //     0xb7ba58: add             x0, x0, HEAP, lsl #32
    // 0xb7ba5c: r16 = 12.000000
    //     0xb7ba5c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7ba60: ldr             x16, [x16, #0x9e8]
    // 0xb7ba64: r30 = Instance_Color
    //     0xb7ba64: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7ba68: stp             lr, x16, [SP]
    // 0xb7ba6c: mov             x1, x0
    // 0xb7ba70: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7ba70: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7ba74: ldr             x4, [x4, #0xaa0]
    // 0xb7ba78: r0 = copyWith()
    //     0xb7ba78: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7ba7c: stur            x0, [fp, #-0x40]
    // 0xb7ba80: r0 = TextSpan()
    //     0xb7ba80: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7ba84: mov             x3, x0
    // 0xb7ba88: r0 = "Unlocked from your last order"
    //     0xb7ba88: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xb7ba8c: ldr             x0, [x0, #0x340]
    // 0xb7ba90: stur            x3, [fp, #-0x58]
    // 0xb7ba94: StoreField: r3->field_b = r0
    //     0xb7ba94: stur            w0, [x3, #0xb]
    // 0xb7ba98: r0 = Instance__DeferringMouseCursor
    //     0xb7ba98: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7ba9c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7ba9c: stur            w0, [x3, #0x17]
    // 0xb7baa0: ldur            x1, [fp, #-0x40]
    // 0xb7baa4: StoreField: r3->field_7 = r1
    //     0xb7baa4: stur            w1, [x3, #7]
    // 0xb7baa8: r1 = Null
    //     0xb7baa8: mov             x1, NULL
    // 0xb7baac: r2 = 4
    //     0xb7baac: movz            x2, #0x4
    // 0xb7bab0: r0 = AllocateArray()
    //     0xb7bab0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7bab4: mov             x2, x0
    // 0xb7bab8: ldur            x0, [fp, #-0x48]
    // 0xb7babc: stur            x2, [fp, #-0x40]
    // 0xb7bac0: StoreField: r2->field_f = r0
    //     0xb7bac0: stur            w0, [x2, #0xf]
    // 0xb7bac4: ldur            x0, [fp, #-0x58]
    // 0xb7bac8: StoreField: r2->field_13 = r0
    //     0xb7bac8: stur            w0, [x2, #0x13]
    // 0xb7bacc: r1 = <InlineSpan>
    //     0xb7bacc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb7bad0: ldr             x1, [x1, #0xe40]
    // 0xb7bad4: r0 = AllocateGrowableArray()
    //     0xb7bad4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7bad8: mov             x1, x0
    // 0xb7badc: ldur            x0, [fp, #-0x40]
    // 0xb7bae0: stur            x1, [fp, #-0x48]
    // 0xb7bae4: StoreField: r1->field_f = r0
    //     0xb7bae4: stur            w0, [x1, #0xf]
    // 0xb7bae8: r2 = 4
    //     0xb7bae8: movz            x2, #0x4
    // 0xb7baec: StoreField: r1->field_b = r2
    //     0xb7baec: stur            w2, [x1, #0xb]
    // 0xb7baf0: r0 = TextSpan()
    //     0xb7baf0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7baf4: mov             x1, x0
    // 0xb7baf8: ldur            x0, [fp, #-0x48]
    // 0xb7bafc: stur            x1, [fp, #-0x40]
    // 0xb7bb00: StoreField: r1->field_f = r0
    //     0xb7bb00: stur            w0, [x1, #0xf]
    // 0xb7bb04: r0 = Instance__DeferringMouseCursor
    //     0xb7bb04: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7bb08: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7bb08: stur            w0, [x1, #0x17]
    // 0xb7bb0c: r0 = RichText()
    //     0xb7bb0c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb7bb10: mov             x1, x0
    // 0xb7bb14: ldur            x2, [fp, #-0x40]
    // 0xb7bb18: stur            x0, [fp, #-0x40]
    // 0xb7bb1c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb7bb1c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb7bb20: r0 = RichText()
    //     0xb7bb20: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb7bb24: ldur            x0, [fp, #-8]
    // 0xb7bb28: LoadField: r1 = r0->field_b
    //     0xb7bb28: ldur            w1, [x0, #0xb]
    // 0xb7bb2c: DecompressPointer r1
    //     0xb7bb2c: add             x1, x1, HEAP, lsl #32
    // 0xb7bb30: cmp             w1, NULL
    // 0xb7bb34: b.eq            #0xb7c3e4
    // 0xb7bb38: LoadField: r2 = r1->field_2f
    //     0xb7bb38: ldur            w2, [x1, #0x2f]
    // 0xb7bb3c: DecompressPointer r2
    //     0xb7bb3c: add             x2, x2, HEAP, lsl #32
    // 0xb7bb40: LoadField: r3 = r2->field_7
    //     0xb7bb40: ldur            w3, [x2, #7]
    // 0xb7bb44: DecompressPointer r3
    //     0xb7bb44: add             x3, x3, HEAP, lsl #32
    // 0xb7bb48: stur            x3, [fp, #-0x48]
    // 0xb7bb4c: r1 = Null
    //     0xb7bb4c: mov             x1, NULL
    // 0xb7bb50: r2 = 4
    //     0xb7bb50: movz            x2, #0x4
    // 0xb7bb54: r0 = AllocateArray()
    //     0xb7bb54: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7bb58: mov             x1, x0
    // 0xb7bb5c: ldur            x0, [fp, #-0x48]
    // 0xb7bb60: StoreField: r1->field_f = r0
    //     0xb7bb60: stur            w0, [x1, #0xf]
    // 0xb7bb64: r16 = "\n"
    //     0xb7bb64: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xb7bb68: StoreField: r1->field_13 = r16
    //     0xb7bb68: stur            w16, [x1, #0x13]
    // 0xb7bb6c: str             x1, [SP]
    // 0xb7bb70: r0 = _interpolate()
    //     0xb7bb70: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb7bb74: ldur            x1, [fp, #-0x10]
    // 0xb7bb78: stur            x0, [fp, #-0x48]
    // 0xb7bb7c: r0 = of()
    //     0xb7bb7c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7bb80: LoadField: r1 = r0->field_87
    //     0xb7bb80: ldur            w1, [x0, #0x87]
    // 0xb7bb84: DecompressPointer r1
    //     0xb7bb84: add             x1, x1, HEAP, lsl #32
    // 0xb7bb88: LoadField: r0 = r1->field_23
    //     0xb7bb88: ldur            w0, [x1, #0x23]
    // 0xb7bb8c: DecompressPointer r0
    //     0xb7bb8c: add             x0, x0, HEAP, lsl #32
    // 0xb7bb90: r16 = 32.000000
    //     0xb7bb90: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb7bb94: ldr             x16, [x16, #0x848]
    // 0xb7bb98: r30 = Instance_Color
    //     0xb7bb98: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7bb9c: stp             lr, x16, [SP]
    // 0xb7bba0: mov             x1, x0
    // 0xb7bba4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7bba4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7bba8: ldr             x4, [x4, #0xaa0]
    // 0xb7bbac: r0 = copyWith()
    //     0xb7bbac: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7bbb0: ldur            x1, [fp, #-0x10]
    // 0xb7bbb4: stur            x0, [fp, #-0x58]
    // 0xb7bbb8: r0 = of()
    //     0xb7bbb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7bbbc: LoadField: r1 = r0->field_87
    //     0xb7bbbc: ldur            w1, [x0, #0x87]
    // 0xb7bbc0: DecompressPointer r1
    //     0xb7bbc0: add             x1, x1, HEAP, lsl #32
    // 0xb7bbc4: LoadField: r0 = r1->field_2b
    //     0xb7bbc4: ldur            w0, [x1, #0x2b]
    // 0xb7bbc8: DecompressPointer r0
    //     0xb7bbc8: add             x0, x0, HEAP, lsl #32
    // 0xb7bbcc: r16 = Instance_Color
    //     0xb7bbcc: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7bbd0: r30 = 16.000000
    //     0xb7bbd0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb7bbd4: ldr             lr, [lr, #0x188]
    // 0xb7bbd8: stp             lr, x16, [SP]
    // 0xb7bbdc: mov             x1, x0
    // 0xb7bbe0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb7bbe0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb7bbe4: ldr             x4, [x4, #0x9b8]
    // 0xb7bbe8: r0 = copyWith()
    //     0xb7bbe8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7bbec: stur            x0, [fp, #-0x70]
    // 0xb7bbf0: r0 = TextSpan()
    //     0xb7bbf0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7bbf4: mov             x3, x0
    // 0xb7bbf8: r0 = "OFF"
    //     0xb7bbf8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xb7bbfc: ldr             x0, [x0, #0x348]
    // 0xb7bc00: stur            x3, [fp, #-0x78]
    // 0xb7bc04: StoreField: r3->field_b = r0
    //     0xb7bc04: stur            w0, [x3, #0xb]
    // 0xb7bc08: r0 = Instance__DeferringMouseCursor
    //     0xb7bc08: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7bc0c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7bc0c: stur            w0, [x3, #0x17]
    // 0xb7bc10: ldur            x1, [fp, #-0x70]
    // 0xb7bc14: StoreField: r3->field_7 = r1
    //     0xb7bc14: stur            w1, [x3, #7]
    // 0xb7bc18: r1 = Null
    //     0xb7bc18: mov             x1, NULL
    // 0xb7bc1c: r2 = 2
    //     0xb7bc1c: movz            x2, #0x2
    // 0xb7bc20: r0 = AllocateArray()
    //     0xb7bc20: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7bc24: mov             x2, x0
    // 0xb7bc28: ldur            x0, [fp, #-0x78]
    // 0xb7bc2c: stur            x2, [fp, #-0x70]
    // 0xb7bc30: StoreField: r2->field_f = r0
    //     0xb7bc30: stur            w0, [x2, #0xf]
    // 0xb7bc34: r1 = <InlineSpan>
    //     0xb7bc34: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb7bc38: ldr             x1, [x1, #0xe40]
    // 0xb7bc3c: r0 = AllocateGrowableArray()
    //     0xb7bc3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7bc40: mov             x1, x0
    // 0xb7bc44: ldur            x0, [fp, #-0x70]
    // 0xb7bc48: stur            x1, [fp, #-0x78]
    // 0xb7bc4c: StoreField: r1->field_f = r0
    //     0xb7bc4c: stur            w0, [x1, #0xf]
    // 0xb7bc50: r2 = 2
    //     0xb7bc50: movz            x2, #0x2
    // 0xb7bc54: StoreField: r1->field_b = r2
    //     0xb7bc54: stur            w2, [x1, #0xb]
    // 0xb7bc58: r0 = TextSpan()
    //     0xb7bc58: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7bc5c: mov             x1, x0
    // 0xb7bc60: ldur            x0, [fp, #-0x48]
    // 0xb7bc64: stur            x1, [fp, #-0x70]
    // 0xb7bc68: StoreField: r1->field_b = r0
    //     0xb7bc68: stur            w0, [x1, #0xb]
    // 0xb7bc6c: ldur            x0, [fp, #-0x78]
    // 0xb7bc70: StoreField: r1->field_f = r0
    //     0xb7bc70: stur            w0, [x1, #0xf]
    // 0xb7bc74: r0 = Instance__DeferringMouseCursor
    //     0xb7bc74: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7bc78: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7bc78: stur            w0, [x1, #0x17]
    // 0xb7bc7c: ldur            x0, [fp, #-0x58]
    // 0xb7bc80: StoreField: r1->field_7 = r0
    //     0xb7bc80: stur            w0, [x1, #7]
    // 0xb7bc84: r0 = RichText()
    //     0xb7bc84: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb7bc88: stur            x0, [fp, #-0x48]
    // 0xb7bc8c: r16 = Instance_TextAlign
    //     0xb7bc8c: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb7bc90: str             x16, [SP]
    // 0xb7bc94: mov             x1, x0
    // 0xb7bc98: ldur            x2, [fp, #-0x70]
    // 0xb7bc9c: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xb7bc9c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xb7bca0: ldr             x4, [x4, #0x350]
    // 0xb7bca4: r0 = RichText()
    //     0xb7bca4: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb7bca8: r1 = Null
    //     0xb7bca8: mov             x1, NULL
    // 0xb7bcac: r2 = 6
    //     0xb7bcac: movz            x2, #0x6
    // 0xb7bcb0: r0 = AllocateArray()
    //     0xb7bcb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7bcb4: mov             x2, x0
    // 0xb7bcb8: ldur            x0, [fp, #-0x40]
    // 0xb7bcbc: stur            x2, [fp, #-0x58]
    // 0xb7bcc0: StoreField: r2->field_f = r0
    //     0xb7bcc0: stur            w0, [x2, #0xf]
    // 0xb7bcc4: r16 = Instance_VerticalDivider
    //     0xb7bcc4: add             x16, PP, #0x49, lsl #12  ; [pp+0x49750] Obj!VerticalDivider@d66bb1
    //     0xb7bcc8: ldr             x16, [x16, #0x750]
    // 0xb7bccc: StoreField: r2->field_13 = r16
    //     0xb7bccc: stur            w16, [x2, #0x13]
    // 0xb7bcd0: ldur            x0, [fp, #-0x48]
    // 0xb7bcd4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7bcd4: stur            w0, [x2, #0x17]
    // 0xb7bcd8: r1 = <Widget>
    //     0xb7bcd8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7bcdc: r0 = AllocateGrowableArray()
    //     0xb7bcdc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7bce0: mov             x1, x0
    // 0xb7bce4: ldur            x0, [fp, #-0x58]
    // 0xb7bce8: stur            x1, [fp, #-0x40]
    // 0xb7bcec: StoreField: r1->field_f = r0
    //     0xb7bcec: stur            w0, [x1, #0xf]
    // 0xb7bcf0: r0 = 6
    //     0xb7bcf0: movz            x0, #0x6
    // 0xb7bcf4: StoreField: r1->field_b = r0
    //     0xb7bcf4: stur            w0, [x1, #0xb]
    // 0xb7bcf8: r0 = Row()
    //     0xb7bcf8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb7bcfc: mov             x1, x0
    // 0xb7bd00: r0 = Instance_Axis
    //     0xb7bd00: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7bd04: stur            x1, [fp, #-0x48]
    // 0xb7bd08: StoreField: r1->field_f = r0
    //     0xb7bd08: stur            w0, [x1, #0xf]
    // 0xb7bd0c: r2 = Instance_MainAxisAlignment
    //     0xb7bd0c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb7bd10: ldr             x2, [x2, #0xa8]
    // 0xb7bd14: StoreField: r1->field_13 = r2
    //     0xb7bd14: stur            w2, [x1, #0x13]
    // 0xb7bd18: r2 = Instance_MainAxisSize
    //     0xb7bd18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7bd1c: ldr             x2, [x2, #0xa10]
    // 0xb7bd20: ArrayStore: r1[0] = r2  ; List_4
    //     0xb7bd20: stur            w2, [x1, #0x17]
    // 0xb7bd24: r3 = Instance_CrossAxisAlignment
    //     0xb7bd24: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7bd28: ldr             x3, [x3, #0xa18]
    // 0xb7bd2c: StoreField: r1->field_1b = r3
    //     0xb7bd2c: stur            w3, [x1, #0x1b]
    // 0xb7bd30: r4 = Instance_VerticalDirection
    //     0xb7bd30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7bd34: ldr             x4, [x4, #0xa20]
    // 0xb7bd38: StoreField: r1->field_23 = r4
    //     0xb7bd38: stur            w4, [x1, #0x23]
    // 0xb7bd3c: r5 = Instance_Clip
    //     0xb7bd3c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7bd40: ldr             x5, [x5, #0x38]
    // 0xb7bd44: StoreField: r1->field_2b = r5
    //     0xb7bd44: stur            w5, [x1, #0x2b]
    // 0xb7bd48: StoreField: r1->field_2f = rZR
    //     0xb7bd48: stur            xzr, [x1, #0x2f]
    // 0xb7bd4c: ldur            x6, [fp, #-0x40]
    // 0xb7bd50: StoreField: r1->field_b = r6
    //     0xb7bd50: stur            w6, [x1, #0xb]
    // 0xb7bd54: r0 = Padding()
    //     0xb7bd54: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7bd58: mov             x1, x0
    // 0xb7bd5c: r0 = Instance_EdgeInsets
    //     0xb7bd5c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48e60] Obj!EdgeInsets@d58611
    //     0xb7bd60: ldr             x0, [x0, #0xe60]
    // 0xb7bd64: stur            x1, [fp, #-0x40]
    // 0xb7bd68: StoreField: r1->field_f = r0
    //     0xb7bd68: stur            w0, [x1, #0xf]
    // 0xb7bd6c: ldur            x0, [fp, #-0x48]
    // 0xb7bd70: StoreField: r1->field_b = r0
    //     0xb7bd70: stur            w0, [x1, #0xb]
    // 0xb7bd74: r0 = IntrinsicHeight()
    //     0xb7bd74: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xb7bd78: mov             x1, x0
    // 0xb7bd7c: ldur            x0, [fp, #-0x40]
    // 0xb7bd80: stur            x1, [fp, #-0x48]
    // 0xb7bd84: StoreField: r1->field_b = r0
    //     0xb7bd84: stur            w0, [x1, #0xb]
    // 0xb7bd88: r0 = Container()
    //     0xb7bd88: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7bd8c: stur            x0, [fp, #-0x40]
    // 0xb7bd90: r16 = 100.000000
    //     0xb7bd90: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb7bd94: ldur            lr, [fp, #-0x30]
    // 0xb7bd98: stp             lr, x16, [SP, #8]
    // 0xb7bd9c: ldur            x16, [fp, #-0x48]
    // 0xb7bda0: str             x16, [SP]
    // 0xb7bda4: mov             x1, x0
    // 0xb7bda8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb7bda8: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb7bdac: ldr             x4, [x4, #0xc78]
    // 0xb7bdb0: r0 = Container()
    //     0xb7bdb0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7bdb4: r1 = <Path>
    //     0xb7bdb4: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb7bdb8: ldr             x1, [x1, #0xd30]
    // 0xb7bdbc: r0 = MovieTicketClipper()
    //     0xb7bdbc: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xb7bdc0: stur            x0, [fp, #-0x30]
    // 0xb7bdc4: r0 = ClipPath()
    //     0xb7bdc4: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb7bdc8: mov             x1, x0
    // 0xb7bdcc: ldur            x0, [fp, #-0x30]
    // 0xb7bdd0: stur            x1, [fp, #-0x48]
    // 0xb7bdd4: StoreField: r1->field_f = r0
    //     0xb7bdd4: stur            w0, [x1, #0xf]
    // 0xb7bdd8: r0 = Instance_Clip
    //     0xb7bdd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb7bddc: ldr             x0, [x0, #0x138]
    // 0xb7bde0: StoreField: r1->field_13 = r0
    //     0xb7bde0: stur            w0, [x1, #0x13]
    // 0xb7bde4: ldur            x0, [fp, #-0x40]
    // 0xb7bde8: StoreField: r1->field_b = r0
    //     0xb7bde8: stur            w0, [x1, #0xb]
    // 0xb7bdec: r0 = Visibility()
    //     0xb7bdec: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7bdf0: mov             x2, x0
    // 0xb7bdf4: ldur            x0, [fp, #-0x48]
    // 0xb7bdf8: stur            x2, [fp, #-0x30]
    // 0xb7bdfc: StoreField: r2->field_b = r0
    //     0xb7bdfc: stur            w0, [x2, #0xb]
    // 0xb7be00: r0 = Instance_SizedBox
    //     0xb7be00: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7be04: StoreField: r2->field_f = r0
    //     0xb7be04: stur            w0, [x2, #0xf]
    // 0xb7be08: ldur            x0, [fp, #-0x38]
    // 0xb7be0c: StoreField: r2->field_13 = r0
    //     0xb7be0c: stur            w0, [x2, #0x13]
    // 0xb7be10: r0 = false
    //     0xb7be10: add             x0, NULL, #0x30  ; false
    // 0xb7be14: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7be14: stur            w0, [x2, #0x17]
    // 0xb7be18: StoreField: r2->field_1b = r0
    //     0xb7be18: stur            w0, [x2, #0x1b]
    // 0xb7be1c: StoreField: r2->field_1f = r0
    //     0xb7be1c: stur            w0, [x2, #0x1f]
    // 0xb7be20: StoreField: r2->field_23 = r0
    //     0xb7be20: stur            w0, [x2, #0x23]
    // 0xb7be24: StoreField: r2->field_27 = r0
    //     0xb7be24: stur            w0, [x2, #0x27]
    // 0xb7be28: StoreField: r2->field_2b = r0
    //     0xb7be28: stur            w0, [x2, #0x2b]
    // 0xb7be2c: ldur            x0, [fp, #-0x28]
    // 0xb7be30: LoadField: r1 = r0->field_b
    //     0xb7be30: ldur            w1, [x0, #0xb]
    // 0xb7be34: LoadField: r3 = r0->field_f
    //     0xb7be34: ldur            w3, [x0, #0xf]
    // 0xb7be38: DecompressPointer r3
    //     0xb7be38: add             x3, x3, HEAP, lsl #32
    // 0xb7be3c: LoadField: r4 = r3->field_b
    //     0xb7be3c: ldur            w4, [x3, #0xb]
    // 0xb7be40: r3 = LoadInt32Instr(r1)
    //     0xb7be40: sbfx            x3, x1, #1, #0x1f
    // 0xb7be44: stur            x3, [fp, #-0x50]
    // 0xb7be48: r1 = LoadInt32Instr(r4)
    //     0xb7be48: sbfx            x1, x4, #1, #0x1f
    // 0xb7be4c: cmp             x3, x1
    // 0xb7be50: b.ne            #0xb7be5c
    // 0xb7be54: mov             x1, x0
    // 0xb7be58: r0 = _growToNextCapacity()
    //     0xb7be58: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7be5c: ldur            x4, [fp, #-8]
    // 0xb7be60: ldur            x2, [fp, #-0x28]
    // 0xb7be64: ldur            x3, [fp, #-0x50]
    // 0xb7be68: add             x0, x3, #1
    // 0xb7be6c: lsl             x1, x0, #1
    // 0xb7be70: StoreField: r2->field_b = r1
    //     0xb7be70: stur            w1, [x2, #0xb]
    // 0xb7be74: LoadField: r1 = r2->field_f
    //     0xb7be74: ldur            w1, [x2, #0xf]
    // 0xb7be78: DecompressPointer r1
    //     0xb7be78: add             x1, x1, HEAP, lsl #32
    // 0xb7be7c: ldur            x0, [fp, #-0x30]
    // 0xb7be80: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7be80: add             x25, x1, x3, lsl #2
    //     0xb7be84: add             x25, x25, #0xf
    //     0xb7be88: str             w0, [x25]
    //     0xb7be8c: tbz             w0, #0, #0xb7bea8
    //     0xb7be90: ldurb           w16, [x1, #-1]
    //     0xb7be94: ldurb           w17, [x0, #-1]
    //     0xb7be98: and             x16, x17, x16, lsr #2
    //     0xb7be9c: tst             x16, HEAP, lsr #32
    //     0xb7bea0: b.eq            #0xb7bea8
    //     0xb7bea4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7bea8: ldur            x1, [fp, #-0x10]
    // 0xb7beac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb7beac: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb7beb0: r0 = _of()
    //     0xb7beb0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb7beb4: LoadField: r1 = r0->field_7
    //     0xb7beb4: ldur            w1, [x0, #7]
    // 0xb7beb8: DecompressPointer r1
    //     0xb7beb8: add             x1, x1, HEAP, lsl #32
    // 0xb7bebc: LoadField: d0 = r1->field_f
    //     0xb7bebc: ldur            d0, [x1, #0xf]
    // 0xb7bec0: d1 = 0.375000
    //     0xb7bec0: fmov            d1, #0.37500000
    // 0xb7bec4: fmul            d2, d0, d1
    // 0xb7bec8: ldur            x1, [fp, #-0x10]
    // 0xb7becc: stur            d2, [fp, #-0x80]
    // 0xb7bed0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb7bed0: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb7bed4: r0 = _of()
    //     0xb7bed4: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb7bed8: LoadField: r1 = r0->field_7
    //     0xb7bed8: ldur            w1, [x0, #7]
    // 0xb7bedc: DecompressPointer r1
    //     0xb7bedc: add             x1, x1, HEAP, lsl #32
    // 0xb7bee0: LoadField: d0 = r1->field_7
    //     0xb7bee0: ldur            d0, [x1, #7]
    // 0xb7bee4: ldur            x1, [fp, #-8]
    // 0xb7bee8: stur            d0, [fp, #-0x88]
    // 0xb7beec: LoadField: r0 = r1->field_b
    //     0xb7beec: ldur            w0, [x1, #0xb]
    // 0xb7bef0: DecompressPointer r0
    //     0xb7bef0: add             x0, x0, HEAP, lsl #32
    // 0xb7bef4: cmp             w0, NULL
    // 0xb7bef8: b.eq            #0xb7c3e8
    // 0xb7befc: LoadField: r2 = r0->field_b
    //     0xb7befc: ldur            w2, [x0, #0xb]
    // 0xb7bf00: DecompressPointer r2
    //     0xb7bf00: add             x2, x2, HEAP, lsl #32
    // 0xb7bf04: r0 = LoadClassIdInstr(r2)
    //     0xb7bf04: ldur            x0, [x2, #-1]
    //     0xb7bf08: ubfx            x0, x0, #0xc, #0x14
    // 0xb7bf0c: str             x2, [SP]
    // 0xb7bf10: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb7bf10: movz            x17, #0xc898
    //     0xb7bf14: add             lr, x0, x17
    //     0xb7bf18: ldr             lr, [x21, lr, lsl #3]
    //     0xb7bf1c: blr             lr
    // 0xb7bf20: mov             x3, x0
    // 0xb7bf24: ldur            x0, [fp, #-8]
    // 0xb7bf28: stur            x3, [fp, #-0x38]
    // 0xb7bf2c: LoadField: r4 = r0->field_13
    //     0xb7bf2c: ldur            w4, [x0, #0x13]
    // 0xb7bf30: DecompressPointer r4
    //     0xb7bf30: add             x4, x4, HEAP, lsl #32
    // 0xb7bf34: r16 = Sentinel
    //     0xb7bf34: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb7bf38: cmp             w4, w16
    // 0xb7bf3c: b.eq            #0xb7c3ec
    // 0xb7bf40: ldur            x2, [fp, #-0x20]
    // 0xb7bf44: stur            x4, [fp, #-0x30]
    // 0xb7bf48: r1 = Function '<anonymous closure>':.
    //     0xb7bf48: add             x1, PP, #0x55, lsl #12  ; [pp+0x558f8] AnonymousClosure: (0xb7f2f8), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xb7b034)
    //     0xb7bf4c: ldr             x1, [x1, #0x8f8]
    // 0xb7bf50: r0 = AllocateClosure()
    //     0xb7bf50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7bf54: ldur            x2, [fp, #-0x20]
    // 0xb7bf58: r1 = Function '<anonymous closure>':.
    //     0xb7bf58: add             x1, PP, #0x55, lsl #12  ; [pp+0x55900] AnonymousClosure: (0xb7c448), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::build (0xb7b034)
    //     0xb7bf5c: ldr             x1, [x1, #0x900]
    // 0xb7bf60: stur            x0, [fp, #-0x20]
    // 0xb7bf64: r0 = AllocateClosure()
    //     0xb7bf64: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7bf68: stur            x0, [fp, #-0x40]
    // 0xb7bf6c: r0 = PageView()
    //     0xb7bf6c: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb7bf70: stur            x0, [fp, #-0x48]
    // 0xb7bf74: r16 = Instance_BouncingScrollPhysics
    //     0xb7bf74: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb7bf78: ldr             x16, [x16, #0x890]
    // 0xb7bf7c: r30 = false
    //     0xb7bf7c: add             lr, NULL, #0x30  ; false
    // 0xb7bf80: stp             lr, x16, [SP, #8]
    // 0xb7bf84: ldur            x16, [fp, #-0x30]
    // 0xb7bf88: str             x16, [SP]
    // 0xb7bf8c: mov             x1, x0
    // 0xb7bf90: ldur            x2, [fp, #-0x40]
    // 0xb7bf94: ldur            x3, [fp, #-0x38]
    // 0xb7bf98: ldur            x5, [fp, #-0x20]
    // 0xb7bf9c: r4 = const [0, 0x7, 0x3, 0x4, controller, 0x6, padEnds, 0x5, physics, 0x4, null]
    //     0xb7bf9c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d20] List(11) [0, 0x7, 0x3, 0x4, "controller", 0x6, "padEnds", 0x5, "physics", 0x4, Null]
    //     0xb7bfa0: ldr             x4, [x4, #0xd20]
    // 0xb7bfa4: r0 = PageView.builder()
    //     0xb7bfa4: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb7bfa8: ldur            d0, [fp, #-0x88]
    // 0xb7bfac: r0 = inline_Allocate_Double()
    //     0xb7bfac: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb7bfb0: add             x0, x0, #0x10
    //     0xb7bfb4: cmp             x1, x0
    //     0xb7bfb8: b.ls            #0xb7c3f8
    //     0xb7bfbc: str             x0, [THR, #0x50]  ; THR::top
    //     0xb7bfc0: sub             x0, x0, #0xf
    //     0xb7bfc4: movz            x1, #0xe15c
    //     0xb7bfc8: movk            x1, #0x3, lsl #16
    //     0xb7bfcc: stur            x1, [x0, #-1]
    // 0xb7bfd0: StoreField: r0->field_7 = d0
    //     0xb7bfd0: stur            d0, [x0, #7]
    // 0xb7bfd4: stur            x0, [fp, #-0x20]
    // 0xb7bfd8: r0 = SizedBox()
    //     0xb7bfd8: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb7bfdc: mov             x2, x0
    // 0xb7bfe0: ldur            x0, [fp, #-0x20]
    // 0xb7bfe4: stur            x2, [fp, #-0x30]
    // 0xb7bfe8: StoreField: r2->field_f = r0
    //     0xb7bfe8: stur            w0, [x2, #0xf]
    // 0xb7bfec: ldur            d0, [fp, #-0x80]
    // 0xb7bff0: r0 = inline_Allocate_Double()
    //     0xb7bff0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb7bff4: add             x0, x0, #0x10
    //     0xb7bff8: cmp             x1, x0
    //     0xb7bffc: b.ls            #0xb7c408
    //     0xb7c000: str             x0, [THR, #0x50]  ; THR::top
    //     0xb7c004: sub             x0, x0, #0xf
    //     0xb7c008: movz            x1, #0xe15c
    //     0xb7c00c: movk            x1, #0x3, lsl #16
    //     0xb7c010: stur            x1, [x0, #-1]
    // 0xb7c014: StoreField: r0->field_7 = d0
    //     0xb7c014: stur            d0, [x0, #7]
    // 0xb7c018: StoreField: r2->field_13 = r0
    //     0xb7c018: stur            w0, [x2, #0x13]
    // 0xb7c01c: ldur            x0, [fp, #-0x48]
    // 0xb7c020: StoreField: r2->field_b = r0
    //     0xb7c020: stur            w0, [x2, #0xb]
    // 0xb7c024: ldur            x0, [fp, #-0x28]
    // 0xb7c028: LoadField: r1 = r0->field_b
    //     0xb7c028: ldur            w1, [x0, #0xb]
    // 0xb7c02c: LoadField: r3 = r0->field_f
    //     0xb7c02c: ldur            w3, [x0, #0xf]
    // 0xb7c030: DecompressPointer r3
    //     0xb7c030: add             x3, x3, HEAP, lsl #32
    // 0xb7c034: LoadField: r4 = r3->field_b
    //     0xb7c034: ldur            w4, [x3, #0xb]
    // 0xb7c038: r3 = LoadInt32Instr(r1)
    //     0xb7c038: sbfx            x3, x1, #1, #0x1f
    // 0xb7c03c: stur            x3, [fp, #-0x50]
    // 0xb7c040: r1 = LoadInt32Instr(r4)
    //     0xb7c040: sbfx            x1, x4, #1, #0x1f
    // 0xb7c044: cmp             x3, x1
    // 0xb7c048: b.ne            #0xb7c054
    // 0xb7c04c: mov             x1, x0
    // 0xb7c050: r0 = _growToNextCapacity()
    //     0xb7c050: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7c054: ldur            x4, [fp, #-8]
    // 0xb7c058: ldur            x2, [fp, #-0x28]
    // 0xb7c05c: ldur            x3, [fp, #-0x50]
    // 0xb7c060: add             x0, x3, #1
    // 0xb7c064: lsl             x1, x0, #1
    // 0xb7c068: StoreField: r2->field_b = r1
    //     0xb7c068: stur            w1, [x2, #0xb]
    // 0xb7c06c: LoadField: r1 = r2->field_f
    //     0xb7c06c: ldur            w1, [x2, #0xf]
    // 0xb7c070: DecompressPointer r1
    //     0xb7c070: add             x1, x1, HEAP, lsl #32
    // 0xb7c074: ldur            x0, [fp, #-0x30]
    // 0xb7c078: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7c078: add             x25, x1, x3, lsl #2
    //     0xb7c07c: add             x25, x25, #0xf
    //     0xb7c080: str             w0, [x25]
    //     0xb7c084: tbz             w0, #0, #0xb7c0a0
    //     0xb7c088: ldurb           w16, [x1, #-1]
    //     0xb7c08c: ldurb           w17, [x0, #-1]
    //     0xb7c090: and             x16, x17, x16, lsr #2
    //     0xb7c094: tst             x16, HEAP, lsr #32
    //     0xb7c098: b.eq            #0xb7c0a0
    //     0xb7c09c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7c0a0: LoadField: r0 = r4->field_b
    //     0xb7c0a0: ldur            w0, [x4, #0xb]
    // 0xb7c0a4: DecompressPointer r0
    //     0xb7c0a4: add             x0, x0, HEAP, lsl #32
    // 0xb7c0a8: cmp             w0, NULL
    // 0xb7c0ac: b.eq            #0xb7c420
    // 0xb7c0b0: LoadField: r1 = r0->field_b
    //     0xb7c0b0: ldur            w1, [x0, #0xb]
    // 0xb7c0b4: DecompressPointer r1
    //     0xb7c0b4: add             x1, x1, HEAP, lsl #32
    // 0xb7c0b8: r0 = LoadClassIdInstr(r1)
    //     0xb7c0b8: ldur            x0, [x1, #-1]
    //     0xb7c0bc: ubfx            x0, x0, #0xc, #0x14
    // 0xb7c0c0: str             x1, [SP]
    // 0xb7c0c4: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb7c0c4: movz            x17, #0xc898
    //     0xb7c0c8: add             lr, x0, x17
    //     0xb7c0cc: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c0d0: blr             lr
    // 0xb7c0d4: r1 = LoadInt32Instr(r0)
    //     0xb7c0d4: sbfx            x1, x0, #1, #0x1f
    // 0xb7c0d8: cmp             x1, #2
    // 0xb7c0dc: b.le            #0xb7c2b4
    // 0xb7c0e0: ldur            x2, [fp, #-8]
    // 0xb7c0e4: ldur            x1, [fp, #-0x28]
    // 0xb7c0e8: LoadField: r0 = r2->field_b
    //     0xb7c0e8: ldur            w0, [x2, #0xb]
    // 0xb7c0ec: DecompressPointer r0
    //     0xb7c0ec: add             x0, x0, HEAP, lsl #32
    // 0xb7c0f0: cmp             w0, NULL
    // 0xb7c0f4: b.eq            #0xb7c424
    // 0xb7c0f8: LoadField: r3 = r0->field_b
    //     0xb7c0f8: ldur            w3, [x0, #0xb]
    // 0xb7c0fc: DecompressPointer r3
    //     0xb7c0fc: add             x3, x3, HEAP, lsl #32
    // 0xb7c100: r0 = LoadClassIdInstr(r3)
    //     0xb7c100: ldur            x0, [x3, #-1]
    //     0xb7c104: ubfx            x0, x0, #0xc, #0x14
    // 0xb7c108: str             x3, [SP]
    // 0xb7c10c: r0 = GDT[cid_x0 + 0xc898]()
    //     0xb7c10c: movz            x17, #0xc898
    //     0xb7c110: add             lr, x0, x17
    //     0xb7c114: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c118: blr             lr
    // 0xb7c11c: mov             x2, x0
    // 0xb7c120: ldur            x0, [fp, #-8]
    // 0xb7c124: stur            x2, [fp, #-0x20]
    // 0xb7c128: LoadField: r3 = r0->field_1b
    //     0xb7c128: ldur            x3, [x0, #0x1b]
    // 0xb7c12c: ldur            x1, [fp, #-0x10]
    // 0xb7c130: stur            x3, [fp, #-0x50]
    // 0xb7c134: r0 = of()
    //     0xb7c134: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7c138: LoadField: r1 = r0->field_5b
    //     0xb7c138: ldur            w1, [x0, #0x5b]
    // 0xb7c13c: DecompressPointer r1
    //     0xb7c13c: add             x1, x1, HEAP, lsl #32
    // 0xb7c140: ldur            x0, [fp, #-0x20]
    // 0xb7c144: stur            x1, [fp, #-8]
    // 0xb7c148: r2 = LoadInt32Instr(r0)
    //     0xb7c148: sbfx            x2, x0, #1, #0x1f
    // 0xb7c14c: stur            x2, [fp, #-0x60]
    // 0xb7c150: r0 = CarouselIndicator()
    //     0xb7c150: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb7c154: mov             x3, x0
    // 0xb7c158: ldur            x0, [fp, #-0x60]
    // 0xb7c15c: stur            x3, [fp, #-0x10]
    // 0xb7c160: StoreField: r3->field_b = r0
    //     0xb7c160: stur            x0, [x3, #0xb]
    // 0xb7c164: ldur            x0, [fp, #-0x50]
    // 0xb7c168: StoreField: r3->field_13 = r0
    //     0xb7c168: stur            x0, [x3, #0x13]
    // 0xb7c16c: ldur            x0, [fp, #-8]
    // 0xb7c170: StoreField: r3->field_1b = r0
    //     0xb7c170: stur            w0, [x3, #0x1b]
    // 0xb7c174: r0 = Instance_Color
    //     0xb7c174: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb7c178: ldr             x0, [x0, #0x90]
    // 0xb7c17c: StoreField: r3->field_1f = r0
    //     0xb7c17c: stur            w0, [x3, #0x1f]
    // 0xb7c180: r1 = Null
    //     0xb7c180: mov             x1, NULL
    // 0xb7c184: r2 = 2
    //     0xb7c184: movz            x2, #0x2
    // 0xb7c188: r0 = AllocateArray()
    //     0xb7c188: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7c18c: mov             x2, x0
    // 0xb7c190: ldur            x0, [fp, #-0x10]
    // 0xb7c194: stur            x2, [fp, #-8]
    // 0xb7c198: StoreField: r2->field_f = r0
    //     0xb7c198: stur            w0, [x2, #0xf]
    // 0xb7c19c: r1 = <Widget>
    //     0xb7c19c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7c1a0: r0 = AllocateGrowableArray()
    //     0xb7c1a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7c1a4: mov             x1, x0
    // 0xb7c1a8: ldur            x0, [fp, #-8]
    // 0xb7c1ac: stur            x1, [fp, #-0x10]
    // 0xb7c1b0: StoreField: r1->field_f = r0
    //     0xb7c1b0: stur            w0, [x1, #0xf]
    // 0xb7c1b4: r0 = 2
    //     0xb7c1b4: movz            x0, #0x2
    // 0xb7c1b8: StoreField: r1->field_b = r0
    //     0xb7c1b8: stur            w0, [x1, #0xb]
    // 0xb7c1bc: r0 = Row()
    //     0xb7c1bc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb7c1c0: mov             x1, x0
    // 0xb7c1c4: r0 = Instance_Axis
    //     0xb7c1c4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7c1c8: stur            x1, [fp, #-8]
    // 0xb7c1cc: StoreField: r1->field_f = r0
    //     0xb7c1cc: stur            w0, [x1, #0xf]
    // 0xb7c1d0: r0 = Instance_MainAxisAlignment
    //     0xb7c1d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb7c1d4: ldr             x0, [x0, #0xab0]
    // 0xb7c1d8: StoreField: r1->field_13 = r0
    //     0xb7c1d8: stur            w0, [x1, #0x13]
    // 0xb7c1dc: r0 = Instance_MainAxisSize
    //     0xb7c1dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7c1e0: ldr             x0, [x0, #0xa10]
    // 0xb7c1e4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7c1e4: stur            w0, [x1, #0x17]
    // 0xb7c1e8: r0 = Instance_CrossAxisAlignment
    //     0xb7c1e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7c1ec: ldr             x0, [x0, #0xa18]
    // 0xb7c1f0: StoreField: r1->field_1b = r0
    //     0xb7c1f0: stur            w0, [x1, #0x1b]
    // 0xb7c1f4: r0 = Instance_VerticalDirection
    //     0xb7c1f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7c1f8: ldr             x0, [x0, #0xa20]
    // 0xb7c1fc: StoreField: r1->field_23 = r0
    //     0xb7c1fc: stur            w0, [x1, #0x23]
    // 0xb7c200: r2 = Instance_Clip
    //     0xb7c200: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7c204: ldr             x2, [x2, #0x38]
    // 0xb7c208: StoreField: r1->field_2b = r2
    //     0xb7c208: stur            w2, [x1, #0x2b]
    // 0xb7c20c: StoreField: r1->field_2f = rZR
    //     0xb7c20c: stur            xzr, [x1, #0x2f]
    // 0xb7c210: ldur            x3, [fp, #-0x10]
    // 0xb7c214: StoreField: r1->field_b = r3
    //     0xb7c214: stur            w3, [x1, #0xb]
    // 0xb7c218: r0 = Padding()
    //     0xb7c218: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7c21c: mov             x2, x0
    // 0xb7c220: r0 = Instance_EdgeInsets
    //     0xb7c220: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb7c224: ldr             x0, [x0, #0xa00]
    // 0xb7c228: stur            x2, [fp, #-0x10]
    // 0xb7c22c: StoreField: r2->field_f = r0
    //     0xb7c22c: stur            w0, [x2, #0xf]
    // 0xb7c230: ldur            x0, [fp, #-8]
    // 0xb7c234: StoreField: r2->field_b = r0
    //     0xb7c234: stur            w0, [x2, #0xb]
    // 0xb7c238: ldur            x0, [fp, #-0x28]
    // 0xb7c23c: LoadField: r1 = r0->field_b
    //     0xb7c23c: ldur            w1, [x0, #0xb]
    // 0xb7c240: LoadField: r3 = r0->field_f
    //     0xb7c240: ldur            w3, [x0, #0xf]
    // 0xb7c244: DecompressPointer r3
    //     0xb7c244: add             x3, x3, HEAP, lsl #32
    // 0xb7c248: LoadField: r4 = r3->field_b
    //     0xb7c248: ldur            w4, [x3, #0xb]
    // 0xb7c24c: r3 = LoadInt32Instr(r1)
    //     0xb7c24c: sbfx            x3, x1, #1, #0x1f
    // 0xb7c250: stur            x3, [fp, #-0x50]
    // 0xb7c254: r1 = LoadInt32Instr(r4)
    //     0xb7c254: sbfx            x1, x4, #1, #0x1f
    // 0xb7c258: cmp             x3, x1
    // 0xb7c25c: b.ne            #0xb7c268
    // 0xb7c260: mov             x1, x0
    // 0xb7c264: r0 = _growToNextCapacity()
    //     0xb7c264: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7c268: ldur            x2, [fp, #-0x28]
    // 0xb7c26c: ldur            x3, [fp, #-0x50]
    // 0xb7c270: add             x0, x3, #1
    // 0xb7c274: lsl             x1, x0, #1
    // 0xb7c278: StoreField: r2->field_b = r1
    //     0xb7c278: stur            w1, [x2, #0xb]
    // 0xb7c27c: LoadField: r1 = r2->field_f
    //     0xb7c27c: ldur            w1, [x2, #0xf]
    // 0xb7c280: DecompressPointer r1
    //     0xb7c280: add             x1, x1, HEAP, lsl #32
    // 0xb7c284: ldur            x0, [fp, #-0x10]
    // 0xb7c288: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7c288: add             x25, x1, x3, lsl #2
    //     0xb7c28c: add             x25, x25, #0xf
    //     0xb7c290: str             w0, [x25]
    //     0xb7c294: tbz             w0, #0, #0xb7c2b0
    //     0xb7c298: ldurb           w16, [x1, #-1]
    //     0xb7c29c: ldurb           w17, [x0, #-1]
    //     0xb7c2a0: and             x16, x17, x16, lsr #2
    //     0xb7c2a4: tst             x16, HEAP, lsr #32
    //     0xb7c2a8: b.eq            #0xb7c2b0
    //     0xb7c2ac: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7c2b0: b               #0xb7c344
    // 0xb7c2b4: ldur            x2, [fp, #-0x28]
    // 0xb7c2b8: r0 = Container()
    //     0xb7c2b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7c2bc: mov             x1, x0
    // 0xb7c2c0: stur            x0, [fp, #-8]
    // 0xb7c2c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb7c2c4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb7c2c8: r0 = Container()
    //     0xb7c2c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7c2cc: ldur            x0, [fp, #-0x28]
    // 0xb7c2d0: LoadField: r1 = r0->field_b
    //     0xb7c2d0: ldur            w1, [x0, #0xb]
    // 0xb7c2d4: LoadField: r2 = r0->field_f
    //     0xb7c2d4: ldur            w2, [x0, #0xf]
    // 0xb7c2d8: DecompressPointer r2
    //     0xb7c2d8: add             x2, x2, HEAP, lsl #32
    // 0xb7c2dc: LoadField: r3 = r2->field_b
    //     0xb7c2dc: ldur            w3, [x2, #0xb]
    // 0xb7c2e0: r2 = LoadInt32Instr(r1)
    //     0xb7c2e0: sbfx            x2, x1, #1, #0x1f
    // 0xb7c2e4: stur            x2, [fp, #-0x50]
    // 0xb7c2e8: r1 = LoadInt32Instr(r3)
    //     0xb7c2e8: sbfx            x1, x3, #1, #0x1f
    // 0xb7c2ec: cmp             x2, x1
    // 0xb7c2f0: b.ne            #0xb7c2fc
    // 0xb7c2f4: mov             x1, x0
    // 0xb7c2f8: r0 = _growToNextCapacity()
    //     0xb7c2f8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7c2fc: ldur            x2, [fp, #-0x28]
    // 0xb7c300: ldur            x3, [fp, #-0x50]
    // 0xb7c304: add             x0, x3, #1
    // 0xb7c308: lsl             x1, x0, #1
    // 0xb7c30c: StoreField: r2->field_b = r1
    //     0xb7c30c: stur            w1, [x2, #0xb]
    // 0xb7c310: LoadField: r1 = r2->field_f
    //     0xb7c310: ldur            w1, [x2, #0xf]
    // 0xb7c314: DecompressPointer r1
    //     0xb7c314: add             x1, x1, HEAP, lsl #32
    // 0xb7c318: ldur            x0, [fp, #-8]
    // 0xb7c31c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7c31c: add             x25, x1, x3, lsl #2
    //     0xb7c320: add             x25, x25, #0xf
    //     0xb7c324: str             w0, [x25]
    //     0xb7c328: tbz             w0, #0, #0xb7c344
    //     0xb7c32c: ldurb           w16, [x1, #-1]
    //     0xb7c330: ldurb           w17, [x0, #-1]
    //     0xb7c334: and             x16, x17, x16, lsr #2
    //     0xb7c338: tst             x16, HEAP, lsr #32
    //     0xb7c33c: b.eq            #0xb7c344
    //     0xb7c340: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7c344: ldur            x0, [fp, #-0x18]
    // 0xb7c348: r0 = Column()
    //     0xb7c348: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb7c34c: mov             x1, x0
    // 0xb7c350: r0 = Instance_Axis
    //     0xb7c350: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7c354: stur            x1, [fp, #-8]
    // 0xb7c358: StoreField: r1->field_f = r0
    //     0xb7c358: stur            w0, [x1, #0xf]
    // 0xb7c35c: r0 = Instance_MainAxisAlignment
    //     0xb7c35c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7c360: ldr             x0, [x0, #0xa08]
    // 0xb7c364: StoreField: r1->field_13 = r0
    //     0xb7c364: stur            w0, [x1, #0x13]
    // 0xb7c368: r0 = Instance_MainAxisSize
    //     0xb7c368: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb7c36c: ldr             x0, [x0, #0xdd0]
    // 0xb7c370: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7c370: stur            w0, [x1, #0x17]
    // 0xb7c374: ldur            x0, [fp, #-0x18]
    // 0xb7c378: StoreField: r1->field_1b = r0
    //     0xb7c378: stur            w0, [x1, #0x1b]
    // 0xb7c37c: r0 = Instance_VerticalDirection
    //     0xb7c37c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7c380: ldr             x0, [x0, #0xa20]
    // 0xb7c384: StoreField: r1->field_23 = r0
    //     0xb7c384: stur            w0, [x1, #0x23]
    // 0xb7c388: r0 = Instance_Clip
    //     0xb7c388: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7c38c: ldr             x0, [x0, #0x38]
    // 0xb7c390: StoreField: r1->field_2b = r0
    //     0xb7c390: stur            w0, [x1, #0x2b]
    // 0xb7c394: StoreField: r1->field_2f = rZR
    //     0xb7c394: stur            xzr, [x1, #0x2f]
    // 0xb7c398: ldur            x0, [fp, #-0x28]
    // 0xb7c39c: StoreField: r1->field_b = r0
    //     0xb7c39c: stur            w0, [x1, #0xb]
    // 0xb7c3a0: r0 = Padding()
    //     0xb7c3a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7c3a4: r1 = Instance_EdgeInsets
    //     0xb7c3a4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3edd8] Obj!EdgeInsets@d58f41
    //     0xb7c3a8: ldr             x1, [x1, #0xdd8]
    // 0xb7c3ac: StoreField: r0->field_f = r1
    //     0xb7c3ac: stur            w1, [x0, #0xf]
    // 0xb7c3b0: ldur            x1, [fp, #-8]
    // 0xb7c3b4: StoreField: r0->field_b = r1
    //     0xb7c3b4: stur            w1, [x0, #0xb]
    // 0xb7c3b8: LeaveFrame
    //     0xb7c3b8: mov             SP, fp
    //     0xb7c3bc: ldp             fp, lr, [SP], #0x10
    // 0xb7c3c0: ret
    //     0xb7c3c0: ret             
    // 0xb7c3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7c3c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7c3c8: b               #0xb7b05c
    // 0xb7c3cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c3cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c3d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c3d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c3d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c3d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c3d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c3d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c3dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c3dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c3e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c3e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c3e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c3e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c3e8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb7c3e8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb7c3ec: r9 = _pageController
    //     0xb7c3ec: add             x9, PP, #0x55, lsl #12  ; [pp+0x55908] Field <_GroupCarouselItemViewState@1616195414._pageController@1616195414>: late (offset: 0x14)
    //     0xb7c3f0: ldr             x9, [x9, #0x908]
    // 0xb7c3f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb7c3f4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb7c3f8: SaveReg d0
    //     0xb7c3f8: str             q0, [SP, #-0x10]!
    // 0xb7c3fc: r0 = AllocateDouble()
    //     0xb7c3fc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb7c400: RestoreReg d0
    //     0xb7c400: ldr             q0, [SP], #0x10
    // 0xb7c404: b               #0xb7bfd0
    // 0xb7c408: SaveReg d0
    //     0xb7c408: str             q0, [SP, #-0x10]!
    // 0xb7c40c: SaveReg r2
    //     0xb7c40c: str             x2, [SP, #-8]!
    // 0xb7c410: r0 = AllocateDouble()
    //     0xb7c410: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb7c414: RestoreReg r2
    //     0xb7c414: ldr             x2, [SP], #8
    // 0xb7c418: RestoreReg d0
    //     0xb7c418: ldr             q0, [SP], #0x10
    // 0xb7c41c: b               #0xb7c014
    // 0xb7c420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c420: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c424: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c424: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Stack <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb7c448, size: 0x4b0
    // 0xb7c448: EnterFrame
    //     0xb7c448: stp             fp, lr, [SP, #-0x10]!
    //     0xb7c44c: mov             fp, SP
    // 0xb7c450: AllocStack(0x68)
    //     0xb7c450: sub             SP, SP, #0x68
    // 0xb7c454: SetupParameters()
    //     0xb7c454: ldr             x0, [fp, #0x20]
    //     0xb7c458: ldur            w1, [x0, #0x17]
    //     0xb7c45c: add             x1, x1, HEAP, lsl #32
    //     0xb7c460: stur            x1, [fp, #-0x18]
    // 0xb7c464: CheckStackOverflow
    //     0xb7c464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7c468: cmp             SP, x16
    //     0xb7c46c: b.ls            #0xb7c8d4
    // 0xb7c470: LoadField: r0 = r1->field_f
    //     0xb7c470: ldur            w0, [x1, #0xf]
    // 0xb7c474: DecompressPointer r0
    //     0xb7c474: add             x0, x0, HEAP, lsl #32
    // 0xb7c478: LoadField: r2 = r0->field_1b
    //     0xb7c478: ldur            x2, [x0, #0x1b]
    // 0xb7c47c: ldr             x3, [fp, #0x10]
    // 0xb7c480: r4 = LoadInt32Instr(r3)
    //     0xb7c480: sbfx            x4, x3, #1, #0x1f
    //     0xb7c484: tbz             w3, #0, #0xb7c48c
    //     0xb7c488: ldur            x4, [x3, #7]
    // 0xb7c48c: stur            x4, [fp, #-0x10]
    // 0xb7c490: cmp             x4, x2
    // 0xb7c494: b.ne            #0xb7c700
    // 0xb7c498: LoadField: r2 = r0->field_b
    //     0xb7c498: ldur            w2, [x0, #0xb]
    // 0xb7c49c: DecompressPointer r2
    //     0xb7c49c: add             x2, x2, HEAP, lsl #32
    // 0xb7c4a0: stur            x2, [fp, #-8]
    // 0xb7c4a4: cmp             w2, NULL
    // 0xb7c4a8: b.eq            #0xb7c8dc
    // 0xb7c4ac: LoadField: r0 = r2->field_b
    //     0xb7c4ac: ldur            w0, [x2, #0xb]
    // 0xb7c4b0: DecompressPointer r0
    //     0xb7c4b0: add             x0, x0, HEAP, lsl #32
    // 0xb7c4b4: r5 = LoadClassIdInstr(r0)
    //     0xb7c4b4: ldur            x5, [x0, #-1]
    //     0xb7c4b8: ubfx            x5, x5, #0xc, #0x14
    // 0xb7c4bc: stp             x3, x0, [SP]
    // 0xb7c4c0: mov             x0, x5
    // 0xb7c4c4: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7c4c4: sub             lr, x0, #0xb7
    //     0xb7c4c8: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c4cc: blr             lr
    // 0xb7c4d0: cmp             w0, NULL
    // 0xb7c4d4: b.ne            #0xb7c4e0
    // 0xb7c4d8: r2 = Null
    //     0xb7c4d8: mov             x2, NULL
    // 0xb7c4dc: b               #0xb7c504
    // 0xb7c4e0: LoadField: r1 = r0->field_eb
    //     0xb7c4e0: ldur            w1, [x0, #0xeb]
    // 0xb7c4e4: DecompressPointer r1
    //     0xb7c4e4: add             x1, x1, HEAP, lsl #32
    // 0xb7c4e8: cmp             w1, NULL
    // 0xb7c4ec: b.ne            #0xb7c4f8
    // 0xb7c4f0: r0 = Null
    //     0xb7c4f0: mov             x0, NULL
    // 0xb7c4f4: b               #0xb7c500
    // 0xb7c4f8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb7c4f8: ldur            w0, [x1, #0x17]
    // 0xb7c4fc: DecompressPointer r0
    //     0xb7c4fc: add             x0, x0, HEAP, lsl #32
    // 0xb7c500: mov             x2, x0
    // 0xb7c504: ldur            x1, [fp, #-0x18]
    // 0xb7c508: stur            x2, [fp, #-0x20]
    // 0xb7c50c: LoadField: r0 = r1->field_f
    //     0xb7c50c: ldur            w0, [x1, #0xf]
    // 0xb7c510: DecompressPointer r0
    //     0xb7c510: add             x0, x0, HEAP, lsl #32
    // 0xb7c514: LoadField: r3 = r0->field_b
    //     0xb7c514: ldur            w3, [x0, #0xb]
    // 0xb7c518: DecompressPointer r3
    //     0xb7c518: add             x3, x3, HEAP, lsl #32
    // 0xb7c51c: cmp             w3, NULL
    // 0xb7c520: b.eq            #0xb7c8e0
    // 0xb7c524: LoadField: r0 = r3->field_b
    //     0xb7c524: ldur            w0, [x3, #0xb]
    // 0xb7c528: DecompressPointer r0
    //     0xb7c528: add             x0, x0, HEAP, lsl #32
    // 0xb7c52c: r3 = LoadClassIdInstr(r0)
    //     0xb7c52c: ldur            x3, [x0, #-1]
    //     0xb7c530: ubfx            x3, x3, #0xc, #0x14
    // 0xb7c534: ldr             x16, [fp, #0x10]
    // 0xb7c538: stp             x16, x0, [SP]
    // 0xb7c53c: mov             x0, x3
    // 0xb7c540: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7c540: sub             lr, x0, #0xb7
    //     0xb7c544: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c548: blr             lr
    // 0xb7c54c: cmp             w0, NULL
    // 0xb7c550: b.ne            #0xb7c55c
    // 0xb7c554: r2 = Null
    //     0xb7c554: mov             x2, NULL
    // 0xb7c558: b               #0xb7c580
    // 0xb7c55c: LoadField: r1 = r0->field_eb
    //     0xb7c55c: ldur            w1, [x0, #0xeb]
    // 0xb7c560: DecompressPointer r1
    //     0xb7c560: add             x1, x1, HEAP, lsl #32
    // 0xb7c564: cmp             w1, NULL
    // 0xb7c568: b.ne            #0xb7c574
    // 0xb7c56c: r0 = Null
    //     0xb7c56c: mov             x0, NULL
    // 0xb7c570: b               #0xb7c57c
    // 0xb7c574: LoadField: r0 = r1->field_23
    //     0xb7c574: ldur            w0, [x1, #0x23]
    // 0xb7c578: DecompressPointer r0
    //     0xb7c578: add             x0, x0, HEAP, lsl #32
    // 0xb7c57c: mov             x2, x0
    // 0xb7c580: ldur            x1, [fp, #-0x18]
    // 0xb7c584: stur            x2, [fp, #-0x28]
    // 0xb7c588: LoadField: r0 = r1->field_f
    //     0xb7c588: ldur            w0, [x1, #0xf]
    // 0xb7c58c: DecompressPointer r0
    //     0xb7c58c: add             x0, x0, HEAP, lsl #32
    // 0xb7c590: LoadField: r3 = r0->field_b
    //     0xb7c590: ldur            w3, [x0, #0xb]
    // 0xb7c594: DecompressPointer r3
    //     0xb7c594: add             x3, x3, HEAP, lsl #32
    // 0xb7c598: cmp             w3, NULL
    // 0xb7c59c: b.eq            #0xb7c8e4
    // 0xb7c5a0: LoadField: r0 = r3->field_b
    //     0xb7c5a0: ldur            w0, [x3, #0xb]
    // 0xb7c5a4: DecompressPointer r0
    //     0xb7c5a4: add             x0, x0, HEAP, lsl #32
    // 0xb7c5a8: r3 = LoadClassIdInstr(r0)
    //     0xb7c5a8: ldur            x3, [x0, #-1]
    //     0xb7c5ac: ubfx            x3, x3, #0xc, #0x14
    // 0xb7c5b0: ldr             x16, [fp, #0x10]
    // 0xb7c5b4: stp             x16, x0, [SP]
    // 0xb7c5b8: mov             x0, x3
    // 0xb7c5bc: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7c5bc: sub             lr, x0, #0xb7
    //     0xb7c5c0: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c5c4: blr             lr
    // 0xb7c5c8: cmp             w0, NULL
    // 0xb7c5cc: b.ne            #0xb7c5d8
    // 0xb7c5d0: r2 = Null
    //     0xb7c5d0: mov             x2, NULL
    // 0xb7c5d4: b               #0xb7c5e4
    // 0xb7c5d8: LoadField: r1 = r0->field_47
    //     0xb7c5d8: ldur            w1, [x0, #0x47]
    // 0xb7c5dc: DecompressPointer r1
    //     0xb7c5dc: add             x1, x1, HEAP, lsl #32
    // 0xb7c5e0: mov             x2, x1
    // 0xb7c5e4: ldur            x1, [fp, #-0x18]
    // 0xb7c5e8: stur            x2, [fp, #-0x30]
    // 0xb7c5ec: LoadField: r0 = r1->field_f
    //     0xb7c5ec: ldur            w0, [x1, #0xf]
    // 0xb7c5f0: DecompressPointer r0
    //     0xb7c5f0: add             x0, x0, HEAP, lsl #32
    // 0xb7c5f4: LoadField: r3 = r0->field_b
    //     0xb7c5f4: ldur            w3, [x0, #0xb]
    // 0xb7c5f8: DecompressPointer r3
    //     0xb7c5f8: add             x3, x3, HEAP, lsl #32
    // 0xb7c5fc: cmp             w3, NULL
    // 0xb7c600: b.eq            #0xb7c8e8
    // 0xb7c604: LoadField: r0 = r3->field_b
    //     0xb7c604: ldur            w0, [x3, #0xb]
    // 0xb7c608: DecompressPointer r0
    //     0xb7c608: add             x0, x0, HEAP, lsl #32
    // 0xb7c60c: r3 = LoadClassIdInstr(r0)
    //     0xb7c60c: ldur            x3, [x0, #-1]
    //     0xb7c610: ubfx            x3, x3, #0xc, #0x14
    // 0xb7c614: ldr             x16, [fp, #0x10]
    // 0xb7c618: stp             x16, x0, [SP]
    // 0xb7c61c: mov             x0, x3
    // 0xb7c620: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7c620: sub             lr, x0, #0xb7
    //     0xb7c624: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c628: blr             lr
    // 0xb7c62c: cmp             w0, NULL
    // 0xb7c630: b.ne            #0xb7c63c
    // 0xb7c634: r2 = Null
    //     0xb7c634: mov             x2, NULL
    // 0xb7c638: b               #0xb7c660
    // 0xb7c63c: LoadField: r1 = r0->field_eb
    //     0xb7c63c: ldur            w1, [x0, #0xeb]
    // 0xb7c640: DecompressPointer r1
    //     0xb7c640: add             x1, x1, HEAP, lsl #32
    // 0xb7c644: cmp             w1, NULL
    // 0xb7c648: b.ne            #0xb7c654
    // 0xb7c64c: r0 = Null
    //     0xb7c64c: mov             x0, NULL
    // 0xb7c650: b               #0xb7c65c
    // 0xb7c654: LoadField: r0 = r1->field_13
    //     0xb7c654: ldur            w0, [x1, #0x13]
    // 0xb7c658: DecompressPointer r0
    //     0xb7c658: add             x0, x0, HEAP, lsl #32
    // 0xb7c65c: mov             x2, x0
    // 0xb7c660: ldur            x1, [fp, #-0x18]
    // 0xb7c664: stur            x2, [fp, #-0x38]
    // 0xb7c668: LoadField: r0 = r1->field_f
    //     0xb7c668: ldur            w0, [x1, #0xf]
    // 0xb7c66c: DecompressPointer r0
    //     0xb7c66c: add             x0, x0, HEAP, lsl #32
    // 0xb7c670: LoadField: r3 = r0->field_b
    //     0xb7c670: ldur            w3, [x0, #0xb]
    // 0xb7c674: DecompressPointer r3
    //     0xb7c674: add             x3, x3, HEAP, lsl #32
    // 0xb7c678: cmp             w3, NULL
    // 0xb7c67c: b.eq            #0xb7c8ec
    // 0xb7c680: LoadField: r0 = r3->field_b
    //     0xb7c680: ldur            w0, [x3, #0xb]
    // 0xb7c684: DecompressPointer r0
    //     0xb7c684: add             x0, x0, HEAP, lsl #32
    // 0xb7c688: r3 = LoadClassIdInstr(r0)
    //     0xb7c688: ldur            x3, [x0, #-1]
    //     0xb7c68c: ubfx            x3, x3, #0xc, #0x14
    // 0xb7c690: ldr             x16, [fp, #0x10]
    // 0xb7c694: stp             x16, x0, [SP]
    // 0xb7c698: mov             x0, x3
    // 0xb7c69c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7c69c: sub             lr, x0, #0xb7
    //     0xb7c6a0: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c6a4: blr             lr
    // 0xb7c6a8: cmp             w0, NULL
    // 0xb7c6ac: b.ne            #0xb7c6b8
    // 0xb7c6b0: r1 = Null
    //     0xb7c6b0: mov             x1, NULL
    // 0xb7c6b4: b               #0xb7c6c0
    // 0xb7c6b8: LoadField: r1 = r0->field_e3
    //     0xb7c6b8: ldur            w1, [x0, #0xe3]
    // 0xb7c6bc: DecompressPointer r1
    //     0xb7c6bc: add             x1, x1, HEAP, lsl #32
    // 0xb7c6c0: ldur            x0, [fp, #-8]
    // 0xb7c6c4: LoadField: r2 = r0->field_33
    //     0xb7c6c4: ldur            w2, [x0, #0x33]
    // 0xb7c6c8: DecompressPointer r2
    //     0xb7c6c8: add             x2, x2, HEAP, lsl #32
    // 0xb7c6cc: ldur            x16, [fp, #-0x20]
    // 0xb7c6d0: stp             x16, x2, [SP, #0x20]
    // 0xb7c6d4: ldur            x16, [fp, #-0x28]
    // 0xb7c6d8: ldur            lr, [fp, #-0x30]
    // 0xb7c6dc: stp             lr, x16, [SP, #0x10]
    // 0xb7c6e0: ldur            x16, [fp, #-0x38]
    // 0xb7c6e4: stp             x1, x16, [SP]
    // 0xb7c6e8: r4 = 0
    //     0xb7c6e8: movz            x4, #0
    // 0xb7c6ec: ldr             x0, [SP, #0x28]
    // 0xb7c6f0: r16 = UnlinkedCall_0x613b5c
    //     0xb7c6f0: add             x16, PP, #0x55, lsl #12  ; [pp+0x55910] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb7c6f4: add             x16, x16, #0x910
    // 0xb7c6f8: ldp             x5, lr, [x16]
    // 0xb7c6fc: blr             lr
    // 0xb7c700: ldur            x1, [fp, #-0x18]
    // 0xb7c704: LoadField: r2 = r1->field_f
    //     0xb7c704: ldur            w2, [x1, #0xf]
    // 0xb7c708: DecompressPointer r2
    //     0xb7c708: add             x2, x2, HEAP, lsl #32
    // 0xb7c70c: stur            x2, [fp, #-8]
    // 0xb7c710: LoadField: r0 = r2->field_b
    //     0xb7c710: ldur            w0, [x2, #0xb]
    // 0xb7c714: DecompressPointer r0
    //     0xb7c714: add             x0, x0, HEAP, lsl #32
    // 0xb7c718: cmp             w0, NULL
    // 0xb7c71c: b.eq            #0xb7c8f0
    // 0xb7c720: LoadField: r3 = r0->field_b
    //     0xb7c720: ldur            w3, [x0, #0xb]
    // 0xb7c724: DecompressPointer r3
    //     0xb7c724: add             x3, x3, HEAP, lsl #32
    // 0xb7c728: r0 = LoadClassIdInstr(r3)
    //     0xb7c728: ldur            x0, [x3, #-1]
    //     0xb7c72c: ubfx            x0, x0, #0xc, #0x14
    // 0xb7c730: ldr             x16, [fp, #0x10]
    // 0xb7c734: stp             x16, x3, [SP]
    // 0xb7c738: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7c738: sub             lr, x0, #0xb7
    //     0xb7c73c: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c740: blr             lr
    // 0xb7c744: ldur            x1, [fp, #-8]
    // 0xb7c748: mov             x2, x0
    // 0xb7c74c: ldur            x3, [fp, #-0x10]
    // 0xb7c750: r0 = glassThemeSlider()
    //     0xb7c750: bl              #0xb7c8f8  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::glassThemeSlider
    // 0xb7c754: mov             x1, x0
    // 0xb7c758: ldur            x0, [fp, #-0x18]
    // 0xb7c75c: stur            x1, [fp, #-8]
    // 0xb7c760: LoadField: r2 = r0->field_f
    //     0xb7c760: ldur            w2, [x0, #0xf]
    // 0xb7c764: DecompressPointer r2
    //     0xb7c764: add             x2, x2, HEAP, lsl #32
    // 0xb7c768: LoadField: r0 = r2->field_b
    //     0xb7c768: ldur            w0, [x2, #0xb]
    // 0xb7c76c: DecompressPointer r0
    //     0xb7c76c: add             x0, x0, HEAP, lsl #32
    // 0xb7c770: cmp             w0, NULL
    // 0xb7c774: b.eq            #0xb7c8f4
    // 0xb7c778: LoadField: r2 = r0->field_b
    //     0xb7c778: ldur            w2, [x0, #0xb]
    // 0xb7c77c: DecompressPointer r2
    //     0xb7c77c: add             x2, x2, HEAP, lsl #32
    // 0xb7c780: r0 = LoadClassIdInstr(r2)
    //     0xb7c780: ldur            x0, [x2, #-1]
    //     0xb7c784: ubfx            x0, x0, #0xc, #0x14
    // 0xb7c788: ldr             x16, [fp, #0x10]
    // 0xb7c78c: stp             x16, x2, [SP]
    // 0xb7c790: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7c790: sub             lr, x0, #0xb7
    //     0xb7c794: ldr             lr, [x21, lr, lsl #3]
    //     0xb7c798: blr             lr
    // 0xb7c79c: cmp             w0, NULL
    // 0xb7c7a0: b.ne            #0xb7c7ac
    // 0xb7c7a4: r0 = Null
    //     0xb7c7a4: mov             x0, NULL
    // 0xb7c7a8: b               #0xb7c7bc
    // 0xb7c7ac: r17 = 315
    //     0xb7c7ac: movz            x17, #0x13b
    // 0xb7c7b0: ldr             w1, [x0, x17]
    // 0xb7c7b4: DecompressPointer r1
    //     0xb7c7b4: add             x1, x1, HEAP, lsl #32
    // 0xb7c7b8: mov             x0, x1
    // 0xb7c7bc: cmp             w0, NULL
    // 0xb7c7c0: b.ne            #0xb7c7cc
    // 0xb7c7c4: r1 = false
    //     0xb7c7c4: add             x1, NULL, #0x30  ; false
    // 0xb7c7c8: b               #0xb7c7d0
    // 0xb7c7cc: mov             x1, x0
    // 0xb7c7d0: ldur            x0, [fp, #-8]
    // 0xb7c7d4: stur            x1, [fp, #-0x18]
    // 0xb7c7d8: r0 = SvgPicture()
    //     0xb7c7d8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7c7dc: mov             x1, x0
    // 0xb7c7e0: r2 = "assets/images/free-gift-icon.svg"
    //     0xb7c7e0: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xb7c7e4: ldr             x2, [x2, #0xd40]
    // 0xb7c7e8: stur            x0, [fp, #-0x20]
    // 0xb7c7ec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb7c7ec: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb7c7f0: r0 = SvgPicture.asset()
    //     0xb7c7f0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7c7f4: r0 = Padding()
    //     0xb7c7f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7c7f8: mov             x1, x0
    // 0xb7c7fc: r0 = Instance_EdgeInsets
    //     0xb7c7fc: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xb7c800: ldr             x0, [x0, #0xd48]
    // 0xb7c804: stur            x1, [fp, #-0x28]
    // 0xb7c808: StoreField: r1->field_f = r0
    //     0xb7c808: stur            w0, [x1, #0xf]
    // 0xb7c80c: ldur            x0, [fp, #-0x20]
    // 0xb7c810: StoreField: r1->field_b = r0
    //     0xb7c810: stur            w0, [x1, #0xb]
    // 0xb7c814: r0 = Visibility()
    //     0xb7c814: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7c818: mov             x3, x0
    // 0xb7c81c: ldur            x0, [fp, #-0x28]
    // 0xb7c820: stur            x3, [fp, #-0x20]
    // 0xb7c824: StoreField: r3->field_b = r0
    //     0xb7c824: stur            w0, [x3, #0xb]
    // 0xb7c828: r0 = Instance_SizedBox
    //     0xb7c828: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7c82c: StoreField: r3->field_f = r0
    //     0xb7c82c: stur            w0, [x3, #0xf]
    // 0xb7c830: ldur            x0, [fp, #-0x18]
    // 0xb7c834: StoreField: r3->field_13 = r0
    //     0xb7c834: stur            w0, [x3, #0x13]
    // 0xb7c838: r0 = false
    //     0xb7c838: add             x0, NULL, #0x30  ; false
    // 0xb7c83c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7c83c: stur            w0, [x3, #0x17]
    // 0xb7c840: StoreField: r3->field_1b = r0
    //     0xb7c840: stur            w0, [x3, #0x1b]
    // 0xb7c844: StoreField: r3->field_1f = r0
    //     0xb7c844: stur            w0, [x3, #0x1f]
    // 0xb7c848: StoreField: r3->field_23 = r0
    //     0xb7c848: stur            w0, [x3, #0x23]
    // 0xb7c84c: StoreField: r3->field_27 = r0
    //     0xb7c84c: stur            w0, [x3, #0x27]
    // 0xb7c850: StoreField: r3->field_2b = r0
    //     0xb7c850: stur            w0, [x3, #0x2b]
    // 0xb7c854: r1 = Null
    //     0xb7c854: mov             x1, NULL
    // 0xb7c858: r2 = 4
    //     0xb7c858: movz            x2, #0x4
    // 0xb7c85c: r0 = AllocateArray()
    //     0xb7c85c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7c860: mov             x2, x0
    // 0xb7c864: ldur            x0, [fp, #-8]
    // 0xb7c868: stur            x2, [fp, #-0x18]
    // 0xb7c86c: StoreField: r2->field_f = r0
    //     0xb7c86c: stur            w0, [x2, #0xf]
    // 0xb7c870: ldur            x0, [fp, #-0x20]
    // 0xb7c874: StoreField: r2->field_13 = r0
    //     0xb7c874: stur            w0, [x2, #0x13]
    // 0xb7c878: r1 = <Widget>
    //     0xb7c878: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7c87c: r0 = AllocateGrowableArray()
    //     0xb7c87c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7c880: mov             x1, x0
    // 0xb7c884: ldur            x0, [fp, #-0x18]
    // 0xb7c888: stur            x1, [fp, #-8]
    // 0xb7c88c: StoreField: r1->field_f = r0
    //     0xb7c88c: stur            w0, [x1, #0xf]
    // 0xb7c890: r0 = 4
    //     0xb7c890: movz            x0, #0x4
    // 0xb7c894: StoreField: r1->field_b = r0
    //     0xb7c894: stur            w0, [x1, #0xb]
    // 0xb7c898: r0 = Stack()
    //     0xb7c898: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb7c89c: r1 = Instance_Alignment
    //     0xb7c89c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb7c8a0: ldr             x1, [x1, #0x950]
    // 0xb7c8a4: StoreField: r0->field_f = r1
    //     0xb7c8a4: stur            w1, [x0, #0xf]
    // 0xb7c8a8: r1 = Instance_StackFit
    //     0xb7c8a8: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb7c8ac: ldr             x1, [x1, #0xfa8]
    // 0xb7c8b0: ArrayStore: r0[0] = r1  ; List_4
    //     0xb7c8b0: stur            w1, [x0, #0x17]
    // 0xb7c8b4: r1 = Instance_Clip
    //     0xb7c8b4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb7c8b8: ldr             x1, [x1, #0x7e0]
    // 0xb7c8bc: StoreField: r0->field_1b = r1
    //     0xb7c8bc: stur            w1, [x0, #0x1b]
    // 0xb7c8c0: ldur            x1, [fp, #-8]
    // 0xb7c8c4: StoreField: r0->field_b = r1
    //     0xb7c8c4: stur            w1, [x0, #0xb]
    // 0xb7c8c8: LeaveFrame
    //     0xb7c8c8: mov             SP, fp
    //     0xb7c8cc: ldp             fp, lr, [SP], #0x10
    // 0xb7c8d0: ret
    //     0xb7c8d0: ret             
    // 0xb7c8d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7c8d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7c8d8: b               #0xb7c470
    // 0xb7c8dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c8dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c8e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c8e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c8e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c8e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c8e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c8e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c8ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c8ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c8f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c8f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7c8f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7c8f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ glassThemeSlider(/* No info */) {
    // ** addr: 0xb7c8f8, size: 0x1614
    // 0xb7c8f8: EnterFrame
    //     0xb7c8f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb7c8fc: mov             fp, SP
    // 0xb7c900: AllocStack(0x80)
    //     0xb7c900: sub             SP, SP, #0x80
    // 0xb7c904: SetupParameters(_GroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb7c904: stur            x1, [fp, #-8]
    //     0xb7c908: stur            x2, [fp, #-0x10]
    //     0xb7c90c: stur            x3, [fp, #-0x18]
    // 0xb7c910: CheckStackOverflow
    //     0xb7c910: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7c914: cmp             SP, x16
    //     0xb7c918: b.ls            #0xb7de4c
    // 0xb7c91c: r1 = 3
    //     0xb7c91c: movz            x1, #0x3
    // 0xb7c920: r0 = AllocateContext()
    //     0xb7c920: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7c924: mov             x3, x0
    // 0xb7c928: ldur            x2, [fp, #-8]
    // 0xb7c92c: stur            x3, [fp, #-0x20]
    // 0xb7c930: StoreField: r3->field_f = r2
    //     0xb7c930: stur            w2, [x3, #0xf]
    // 0xb7c934: ldur            x4, [fp, #-0x10]
    // 0xb7c938: StoreField: r3->field_13 = r4
    //     0xb7c938: stur            w4, [x3, #0x13]
    // 0xb7c93c: ldur            x5, [fp, #-0x18]
    // 0xb7c940: r0 = BoxInt64Instr(r5)
    //     0xb7c940: sbfiz           x0, x5, #1, #0x1f
    //     0xb7c944: cmp             x5, x0, asr #1
    //     0xb7c948: b.eq            #0xb7c954
    //     0xb7c94c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb7c950: stur            x5, [x0, #7]
    // 0xb7c954: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7c954: stur            w0, [x3, #0x17]
    // 0xb7c958: r0 = Radius()
    //     0xb7c958: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7c95c: d0 = 12.000000
    //     0xb7c95c: fmov            d0, #12.00000000
    // 0xb7c960: stur            x0, [fp, #-0x28]
    // 0xb7c964: StoreField: r0->field_7 = d0
    //     0xb7c964: stur            d0, [x0, #7]
    // 0xb7c968: StoreField: r0->field_f = d0
    //     0xb7c968: stur            d0, [x0, #0xf]
    // 0xb7c96c: r0 = BorderRadius()
    //     0xb7c96c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb7c970: mov             x1, x0
    // 0xb7c974: ldur            x0, [fp, #-0x28]
    // 0xb7c978: stur            x1, [fp, #-0x30]
    // 0xb7c97c: StoreField: r1->field_7 = r0
    //     0xb7c97c: stur            w0, [x1, #7]
    // 0xb7c980: StoreField: r1->field_b = r0
    //     0xb7c980: stur            w0, [x1, #0xb]
    // 0xb7c984: StoreField: r1->field_f = r0
    //     0xb7c984: stur            w0, [x1, #0xf]
    // 0xb7c988: StoreField: r1->field_13 = r0
    //     0xb7c988: stur            w0, [x1, #0x13]
    // 0xb7c98c: r0 = RoundedRectangleBorder()
    //     0xb7c98c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb7c990: mov             x3, x0
    // 0xb7c994: ldur            x0, [fp, #-0x30]
    // 0xb7c998: stur            x3, [fp, #-0x38]
    // 0xb7c99c: StoreField: r3->field_b = r0
    //     0xb7c99c: stur            w0, [x3, #0xb]
    // 0xb7c9a0: r0 = Instance_BorderSide
    //     0xb7c9a0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb7c9a4: ldr             x0, [x0, #0xe20]
    // 0xb7c9a8: StoreField: r3->field_7 = r0
    //     0xb7c9a8: stur            w0, [x3, #7]
    // 0xb7c9ac: ldur            x0, [fp, #-0x10]
    // 0xb7c9b0: cmp             w0, NULL
    // 0xb7c9b4: b.eq            #0xb7de54
    // 0xb7c9b8: LoadField: r1 = r0->field_e7
    //     0xb7c9b8: ldur            w1, [x0, #0xe7]
    // 0xb7c9bc: DecompressPointer r1
    //     0xb7c9bc: add             x1, x1, HEAP, lsl #32
    // 0xb7c9c0: cmp             w1, NULL
    // 0xb7c9c4: b.eq            #0xb7de58
    // 0xb7c9c8: LoadField: r0 = r1->field_b
    //     0xb7c9c8: ldur            w0, [x1, #0xb]
    // 0xb7c9cc: ldur            x4, [fp, #-8]
    // 0xb7c9d0: stur            x0, [fp, #-0x28]
    // 0xb7c9d4: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xb7c9d4: ldur            w5, [x4, #0x17]
    // 0xb7c9d8: DecompressPointer r5
    //     0xb7c9d8: add             x5, x5, HEAP, lsl #32
    // 0xb7c9dc: r16 = Sentinel
    //     0xb7c9dc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb7c9e0: cmp             w5, w16
    // 0xb7c9e4: b.eq            #0xb7de5c
    // 0xb7c9e8: ldur            x2, [fp, #-0x20]
    // 0xb7c9ec: stur            x5, [fp, #-0x10]
    // 0xb7c9f0: r1 = Function '<anonymous closure>':.
    //     0xb7c9f0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55920] AnonymousClosure: (0xa75c08), in [package:customer_app/app/presentation/views/line/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::lineThemeSlider (0xa75c38)
    //     0xb7c9f4: ldr             x1, [x1, #0x920]
    // 0xb7c9f8: r0 = AllocateClosure()
    //     0xb7c9f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7c9fc: ldur            x2, [fp, #-0x20]
    // 0xb7ca00: r1 = Function '<anonymous closure>':.
    //     0xb7ca00: add             x1, PP, #0x55, lsl #12  ; [pp+0x55928] AnonymousClosure: (0xb7e01c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::glassThemeSlider (0xb7c8f8)
    //     0xb7ca04: ldr             x1, [x1, #0x928]
    // 0xb7ca08: stur            x0, [fp, #-0x30]
    // 0xb7ca0c: r0 = AllocateClosure()
    //     0xb7ca0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7ca10: stur            x0, [fp, #-0x40]
    // 0xb7ca14: r0 = PageView()
    //     0xb7ca14: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb7ca18: stur            x0, [fp, #-0x48]
    // 0xb7ca1c: ldur            x16, [fp, #-0x10]
    // 0xb7ca20: str             x16, [SP]
    // 0xb7ca24: mov             x1, x0
    // 0xb7ca28: ldur            x2, [fp, #-0x40]
    // 0xb7ca2c: ldur            x3, [fp, #-0x28]
    // 0xb7ca30: ldur            x5, [fp, #-0x30]
    // 0xb7ca34: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xb7ca34: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xb7ca38: ldr             x4, [x4, #0xd60]
    // 0xb7ca3c: r0 = PageView.builder()
    //     0xb7ca3c: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb7ca40: r0 = AspectRatio()
    //     0xb7ca40: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb7ca44: d0 = 1.000000
    //     0xb7ca44: fmov            d0, #1.00000000
    // 0xb7ca48: stur            x0, [fp, #-0x10]
    // 0xb7ca4c: StoreField: r0->field_f = d0
    //     0xb7ca4c: stur            d0, [x0, #0xf]
    // 0xb7ca50: ldur            x1, [fp, #-0x48]
    // 0xb7ca54: StoreField: r0->field_b = r1
    //     0xb7ca54: stur            w1, [x0, #0xb]
    // 0xb7ca58: r0 = Card()
    //     0xb7ca58: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb7ca5c: mov             x2, x0
    // 0xb7ca60: r0 = 0.000000
    //     0xb7ca60: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb7ca64: stur            x2, [fp, #-0x28]
    // 0xb7ca68: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7ca68: stur            w0, [x2, #0x17]
    // 0xb7ca6c: ldur            x0, [fp, #-0x38]
    // 0xb7ca70: StoreField: r2->field_1b = r0
    //     0xb7ca70: stur            w0, [x2, #0x1b]
    // 0xb7ca74: r0 = true
    //     0xb7ca74: add             x0, NULL, #0x20  ; true
    // 0xb7ca78: StoreField: r2->field_1f = r0
    //     0xb7ca78: stur            w0, [x2, #0x1f]
    // 0xb7ca7c: ldur            x1, [fp, #-0x10]
    // 0xb7ca80: StoreField: r2->field_2f = r1
    //     0xb7ca80: stur            w1, [x2, #0x2f]
    // 0xb7ca84: StoreField: r2->field_2b = r0
    //     0xb7ca84: stur            w0, [x2, #0x2b]
    // 0xb7ca88: r1 = Instance__CardVariant
    //     0xb7ca88: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb7ca8c: ldr             x1, [x1, #0xa68]
    // 0xb7ca90: StoreField: r2->field_33 = r1
    //     0xb7ca90: stur            w1, [x2, #0x33]
    // 0xb7ca94: ldur            x3, [fp, #-0x20]
    // 0xb7ca98: LoadField: r1 = r3->field_13
    //     0xb7ca98: ldur            w1, [x3, #0x13]
    // 0xb7ca9c: DecompressPointer r1
    //     0xb7ca9c: add             x1, x1, HEAP, lsl #32
    // 0xb7caa0: cmp             w1, NULL
    // 0xb7caa4: b.eq            #0xb7de68
    // 0xb7caa8: LoadField: r4 = r1->field_53
    //     0xb7caa8: ldur            w4, [x1, #0x53]
    // 0xb7caac: DecompressPointer r4
    //     0xb7caac: add             x4, x4, HEAP, lsl #32
    // 0xb7cab0: cmp             w4, NULL
    // 0xb7cab4: b.ne            #0xb7cac0
    // 0xb7cab8: r1 = Null
    //     0xb7cab8: mov             x1, NULL
    // 0xb7cabc: b               #0xb7cac8
    // 0xb7cac0: LoadField: r1 = r4->field_b
    //     0xb7cac0: ldur            w1, [x4, #0xb]
    // 0xb7cac4: DecompressPointer r1
    //     0xb7cac4: add             x1, x1, HEAP, lsl #32
    // 0xb7cac8: cmp             w1, NULL
    // 0xb7cacc: b.ne            #0xb7cad4
    // 0xb7cad0: r1 = ""
    //     0xb7cad0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7cad4: ldur            x4, [fp, #-8]
    // 0xb7cad8: r0 = capitalizeFirstWord()
    //     0xb7cad8: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb7cadc: mov             x2, x0
    // 0xb7cae0: ldur            x0, [fp, #-8]
    // 0xb7cae4: stur            x2, [fp, #-0x10]
    // 0xb7cae8: LoadField: r1 = r0->field_f
    //     0xb7cae8: ldur            w1, [x0, #0xf]
    // 0xb7caec: DecompressPointer r1
    //     0xb7caec: add             x1, x1, HEAP, lsl #32
    // 0xb7caf0: cmp             w1, NULL
    // 0xb7caf4: b.eq            #0xb7de6c
    // 0xb7caf8: r0 = of()
    //     0xb7caf8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7cafc: LoadField: r1 = r0->field_87
    //     0xb7cafc: ldur            w1, [x0, #0x87]
    // 0xb7cb00: DecompressPointer r1
    //     0xb7cb00: add             x1, x1, HEAP, lsl #32
    // 0xb7cb04: LoadField: r0 = r1->field_2b
    //     0xb7cb04: ldur            w0, [x1, #0x2b]
    // 0xb7cb08: DecompressPointer r0
    //     0xb7cb08: add             x0, x0, HEAP, lsl #32
    // 0xb7cb0c: r16 = 14.000000
    //     0xb7cb0c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb7cb10: ldr             x16, [x16, #0x1d8]
    // 0xb7cb14: r30 = Instance_Color
    //     0xb7cb14: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7cb18: stp             lr, x16, [SP]
    // 0xb7cb1c: mov             x1, x0
    // 0xb7cb20: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7cb20: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7cb24: ldr             x4, [x4, #0xaa0]
    // 0xb7cb28: r0 = copyWith()
    //     0xb7cb28: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7cb2c: stur            x0, [fp, #-0x30]
    // 0xb7cb30: r0 = Text()
    //     0xb7cb30: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7cb34: mov             x1, x0
    // 0xb7cb38: ldur            x0, [fp, #-0x10]
    // 0xb7cb3c: stur            x1, [fp, #-0x38]
    // 0xb7cb40: StoreField: r1->field_b = r0
    //     0xb7cb40: stur            w0, [x1, #0xb]
    // 0xb7cb44: ldur            x0, [fp, #-0x30]
    // 0xb7cb48: StoreField: r1->field_13 = r0
    //     0xb7cb48: stur            w0, [x1, #0x13]
    // 0xb7cb4c: r0 = Instance_TextOverflow
    //     0xb7cb4c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb7cb50: ldr             x0, [x0, #0xe10]
    // 0xb7cb54: StoreField: r1->field_2b = r0
    //     0xb7cb54: stur            w0, [x1, #0x2b]
    // 0xb7cb58: r0 = 2
    //     0xb7cb58: movz            x0, #0x2
    // 0xb7cb5c: StoreField: r1->field_37 = r0
    //     0xb7cb5c: stur            w0, [x1, #0x37]
    // 0xb7cb60: r0 = Padding()
    //     0xb7cb60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7cb64: mov             x2, x0
    // 0xb7cb68: r1 = Instance_EdgeInsets
    //     0xb7cb68: add             x1, PP, #0x55, lsl #12  ; [pp+0x55930] Obj!EdgeInsets@d586a1
    //     0xb7cb6c: ldr             x1, [x1, #0x930]
    // 0xb7cb70: stur            x2, [fp, #-0x10]
    // 0xb7cb74: StoreField: r2->field_f = r1
    //     0xb7cb74: stur            w1, [x2, #0xf]
    // 0xb7cb78: ldur            x0, [fp, #-0x38]
    // 0xb7cb7c: StoreField: r2->field_b = r0
    //     0xb7cb7c: stur            w0, [x2, #0xb]
    // 0xb7cb80: ldur            x3, [fp, #-0x20]
    // 0xb7cb84: LoadField: r0 = r3->field_13
    //     0xb7cb84: ldur            w0, [x3, #0x13]
    // 0xb7cb88: DecompressPointer r0
    //     0xb7cb88: add             x0, x0, HEAP, lsl #32
    // 0xb7cb8c: cmp             w0, NULL
    // 0xb7cb90: b.eq            #0xb7de70
    // 0xb7cb94: LoadField: r4 = r0->field_e3
    //     0xb7cb94: ldur            w4, [x0, #0xe3]
    // 0xb7cb98: DecompressPointer r4
    //     0xb7cb98: add             x4, x4, HEAP, lsl #32
    // 0xb7cb9c: cmp             w4, NULL
    // 0xb7cba0: b.ne            #0xb7cbac
    // 0xb7cba4: r0 = Null
    //     0xb7cba4: mov             x0, NULL
    // 0xb7cba8: b               #0xb7cbb4
    // 0xb7cbac: LoadField: r0 = r4->field_7
    //     0xb7cbac: ldur            w0, [x4, #7]
    // 0xb7cbb0: DecompressPointer r0
    //     0xb7cbb0: add             x0, x0, HEAP, lsl #32
    // 0xb7cbb4: r4 = LoadClassIdInstr(r0)
    //     0xb7cbb4: ldur            x4, [x0, #-1]
    //     0xb7cbb8: ubfx            x4, x4, #0xc, #0x14
    // 0xb7cbbc: r16 = 0.000000
    //     0xb7cbbc: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb7cbc0: stp             x16, x0, [SP]
    // 0xb7cbc4: mov             x0, x4
    // 0xb7cbc8: mov             lr, x0
    // 0xb7cbcc: ldr             lr, [x21, lr, lsl #3]
    // 0xb7cbd0: blr             lr
    // 0xb7cbd4: eor             x1, x0, #0x10
    // 0xb7cbd8: ldur            x2, [fp, #-0x20]
    // 0xb7cbdc: stur            x1, [fp, #-0x30]
    // 0xb7cbe0: LoadField: r0 = r2->field_13
    //     0xb7cbe0: ldur            w0, [x2, #0x13]
    // 0xb7cbe4: DecompressPointer r0
    //     0xb7cbe4: add             x0, x0, HEAP, lsl #32
    // 0xb7cbe8: cmp             w0, NULL
    // 0xb7cbec: b.eq            #0xb7de74
    // 0xb7cbf0: LoadField: r3 = r0->field_e3
    //     0xb7cbf0: ldur            w3, [x0, #0xe3]
    // 0xb7cbf4: DecompressPointer r3
    //     0xb7cbf4: add             x3, x3, HEAP, lsl #32
    // 0xb7cbf8: cmp             w3, NULL
    // 0xb7cbfc: b.ne            #0xb7cc08
    // 0xb7cc00: r0 = Null
    //     0xb7cc00: mov             x0, NULL
    // 0xb7cc04: b               #0xb7cc10
    // 0xb7cc08: LoadField: r0 = r3->field_f
    //     0xb7cc08: ldur            w0, [x3, #0xf]
    // 0xb7cc0c: DecompressPointer r0
    //     0xb7cc0c: add             x0, x0, HEAP, lsl #32
    // 0xb7cc10: r3 = LoadClassIdInstr(r0)
    //     0xb7cc10: ldur            x3, [x0, #-1]
    //     0xb7cc14: ubfx            x3, x3, #0xc, #0x14
    // 0xb7cc18: r16 = "product_rating"
    //     0xb7cc18: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xb7cc1c: ldr             x16, [x16, #0xf20]
    // 0xb7cc20: stp             x16, x0, [SP]
    // 0xb7cc24: mov             x0, x3
    // 0xb7cc28: mov             lr, x0
    // 0xb7cc2c: ldr             lr, [x21, lr, lsl #3]
    // 0xb7cc30: blr             lr
    // 0xb7cc34: tbnz            w0, #4, #0xb7d120
    // 0xb7cc38: ldur            x2, [fp, #-0x20]
    // 0xb7cc3c: LoadField: r0 = r2->field_13
    //     0xb7cc3c: ldur            w0, [x2, #0x13]
    // 0xb7cc40: DecompressPointer r0
    //     0xb7cc40: add             x0, x0, HEAP, lsl #32
    // 0xb7cc44: cmp             w0, NULL
    // 0xb7cc48: b.eq            #0xb7de78
    // 0xb7cc4c: LoadField: r1 = r0->field_e3
    //     0xb7cc4c: ldur            w1, [x0, #0xe3]
    // 0xb7cc50: DecompressPointer r1
    //     0xb7cc50: add             x1, x1, HEAP, lsl #32
    // 0xb7cc54: cmp             w1, NULL
    // 0xb7cc58: b.ne            #0xb7cc64
    // 0xb7cc5c: r0 = Null
    //     0xb7cc5c: mov             x0, NULL
    // 0xb7cc60: b               #0xb7cc6c
    // 0xb7cc64: LoadField: r0 = r1->field_7
    //     0xb7cc64: ldur            w0, [x1, #7]
    // 0xb7cc68: DecompressPointer r0
    //     0xb7cc68: add             x0, x0, HEAP, lsl #32
    // 0xb7cc6c: cmp             w0, NULL
    // 0xb7cc70: r16 = true
    //     0xb7cc70: add             x16, NULL, #0x20  ; true
    // 0xb7cc74: r17 = false
    //     0xb7cc74: add             x17, NULL, #0x30  ; false
    // 0xb7cc78: csel            x3, x16, x17, ne
    // 0xb7cc7c: stur            x3, [fp, #-0x38]
    // 0xb7cc80: cmp             w1, NULL
    // 0xb7cc84: b.ne            #0xb7cc90
    // 0xb7cc88: r0 = Null
    //     0xb7cc88: mov             x0, NULL
    // 0xb7cc8c: b               #0xb7cc98
    // 0xb7cc90: LoadField: r0 = r1->field_7
    //     0xb7cc90: ldur            w0, [x1, #7]
    // 0xb7cc94: DecompressPointer r0
    //     0xb7cc94: add             x0, x0, HEAP, lsl #32
    // 0xb7cc98: cmp             w0, NULL
    // 0xb7cc9c: b.ne            #0xb7cca8
    // 0xb7cca0: d1 = 0.000000
    //     0xb7cca0: eor             v1.16b, v1.16b, v1.16b
    // 0xb7cca4: b               #0xb7ccb0
    // 0xb7cca8: LoadField: d0 = r0->field_7
    //     0xb7cca8: ldur            d0, [x0, #7]
    // 0xb7ccac: mov             v1.16b, v0.16b
    // 0xb7ccb0: d0 = 4.000000
    //     0xb7ccb0: fmov            d0, #4.00000000
    // 0xb7ccb4: fcmp            d1, d0
    // 0xb7ccb8: b.lt            #0xb7ccc8
    // 0xb7ccbc: r0 = Instance_Color
    //     0xb7ccbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb7ccc0: ldr             x0, [x0, #0x858]
    // 0xb7ccc4: b               #0xb7cd70
    // 0xb7ccc8: cmp             w1, NULL
    // 0xb7cccc: b.ne            #0xb7ccd8
    // 0xb7ccd0: r0 = Null
    //     0xb7ccd0: mov             x0, NULL
    // 0xb7ccd4: b               #0xb7cce0
    // 0xb7ccd8: LoadField: r0 = r1->field_7
    //     0xb7ccd8: ldur            w0, [x1, #7]
    // 0xb7ccdc: DecompressPointer r0
    //     0xb7ccdc: add             x0, x0, HEAP, lsl #32
    // 0xb7cce0: cmp             w0, NULL
    // 0xb7cce4: b.ne            #0xb7ccf0
    // 0xb7cce8: d1 = 0.000000
    //     0xb7cce8: eor             v1.16b, v1.16b, v1.16b
    // 0xb7ccec: b               #0xb7ccf8
    // 0xb7ccf0: LoadField: d0 = r0->field_7
    //     0xb7ccf0: ldur            d0, [x0, #7]
    // 0xb7ccf4: mov             v1.16b, v0.16b
    // 0xb7ccf8: d0 = 3.500000
    //     0xb7ccf8: fmov            d0, #3.50000000
    // 0xb7ccfc: fcmp            d1, d0
    // 0xb7cd00: b.lt            #0xb7cd1c
    // 0xb7cd04: r1 = Instance_Color
    //     0xb7cd04: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb7cd08: ldr             x1, [x1, #0x858]
    // 0xb7cd0c: d0 = 0.700000
    //     0xb7cd0c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb7cd10: ldr             d0, [x17, #0xf48]
    // 0xb7cd14: r0 = withOpacity()
    //     0xb7cd14: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb7cd18: b               #0xb7cd6c
    // 0xb7cd1c: cmp             w1, NULL
    // 0xb7cd20: b.ne            #0xb7cd2c
    // 0xb7cd24: r0 = Null
    //     0xb7cd24: mov             x0, NULL
    // 0xb7cd28: b               #0xb7cd34
    // 0xb7cd2c: LoadField: r0 = r1->field_7
    //     0xb7cd2c: ldur            w0, [x1, #7]
    // 0xb7cd30: DecompressPointer r0
    //     0xb7cd30: add             x0, x0, HEAP, lsl #32
    // 0xb7cd34: cmp             w0, NULL
    // 0xb7cd38: b.ne            #0xb7cd44
    // 0xb7cd3c: d1 = 0.000000
    //     0xb7cd3c: eor             v1.16b, v1.16b, v1.16b
    // 0xb7cd40: b               #0xb7cd4c
    // 0xb7cd44: LoadField: d0 = r0->field_7
    //     0xb7cd44: ldur            d0, [x0, #7]
    // 0xb7cd48: mov             v1.16b, v0.16b
    // 0xb7cd4c: d0 = 2.000000
    //     0xb7cd4c: fmov            d0, #2.00000000
    // 0xb7cd50: fcmp            d1, d0
    // 0xb7cd54: b.lt            #0xb7cd64
    // 0xb7cd58: r0 = Instance_Color
    //     0xb7cd58: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f860] Obj!Color@d6ad41
    //     0xb7cd5c: ldr             x0, [x0, #0x860]
    // 0xb7cd60: b               #0xb7cd6c
    // 0xb7cd64: r0 = Instance_Color
    //     0xb7cd64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xb7cd68: ldr             x0, [x0, #0x50]
    // 0xb7cd6c: ldur            x2, [fp, #-0x20]
    // 0xb7cd70: stur            x0, [fp, #-0x40]
    // 0xb7cd74: r0 = ColorFilter()
    //     0xb7cd74: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb7cd78: mov             x1, x0
    // 0xb7cd7c: ldur            x0, [fp, #-0x40]
    // 0xb7cd80: stur            x1, [fp, #-0x48]
    // 0xb7cd84: StoreField: r1->field_7 = r0
    //     0xb7cd84: stur            w0, [x1, #7]
    // 0xb7cd88: r0 = Instance_BlendMode
    //     0xb7cd88: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb7cd8c: ldr             x0, [x0, #0xb30]
    // 0xb7cd90: StoreField: r1->field_b = r0
    //     0xb7cd90: stur            w0, [x1, #0xb]
    // 0xb7cd94: r2 = 1
    //     0xb7cd94: movz            x2, #0x1
    // 0xb7cd98: StoreField: r1->field_13 = r2
    //     0xb7cd98: stur            x2, [x1, #0x13]
    // 0xb7cd9c: r0 = SvgPicture()
    //     0xb7cd9c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7cda0: stur            x0, [fp, #-0x40]
    // 0xb7cda4: ldur            x16, [fp, #-0x48]
    // 0xb7cda8: str             x16, [SP]
    // 0xb7cdac: mov             x1, x0
    // 0xb7cdb0: r2 = "assets/images/green_star.svg"
    //     0xb7cdb0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb7cdb4: ldr             x2, [x2, #0x9a0]
    // 0xb7cdb8: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb7cdb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb7cdbc: ldr             x4, [x4, #0xa38]
    // 0xb7cdc0: r0 = SvgPicture.asset()
    //     0xb7cdc0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7cdc4: ldur            x0, [fp, #-0x20]
    // 0xb7cdc8: LoadField: r1 = r0->field_13
    //     0xb7cdc8: ldur            w1, [x0, #0x13]
    // 0xb7cdcc: DecompressPointer r1
    //     0xb7cdcc: add             x1, x1, HEAP, lsl #32
    // 0xb7cdd0: cmp             w1, NULL
    // 0xb7cdd4: b.eq            #0xb7de7c
    // 0xb7cdd8: LoadField: r2 = r1->field_e3
    //     0xb7cdd8: ldur            w2, [x1, #0xe3]
    // 0xb7cddc: DecompressPointer r2
    //     0xb7cddc: add             x2, x2, HEAP, lsl #32
    // 0xb7cde0: cmp             w2, NULL
    // 0xb7cde4: b.ne            #0xb7cdf0
    // 0xb7cde8: r0 = Null
    //     0xb7cde8: mov             x0, NULL
    // 0xb7cdec: b               #0xb7ce10
    // 0xb7cdf0: LoadField: r1 = r2->field_7
    //     0xb7cdf0: ldur            w1, [x2, #7]
    // 0xb7cdf4: DecompressPointer r1
    //     0xb7cdf4: add             x1, x1, HEAP, lsl #32
    // 0xb7cdf8: cmp             w1, NULL
    // 0xb7cdfc: b.ne            #0xb7ce08
    // 0xb7ce00: r0 = Null
    //     0xb7ce00: mov             x0, NULL
    // 0xb7ce04: b               #0xb7ce10
    // 0xb7ce08: r2 = 1
    //     0xb7ce08: movz            x2, #0x1
    // 0xb7ce0c: r0 = toStringAsFixed()
    //     0xb7ce0c: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb7ce10: cmp             w0, NULL
    // 0xb7ce14: b.ne            #0xb7ce20
    // 0xb7ce18: r4 = ""
    //     0xb7ce18: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7ce1c: b               #0xb7ce24
    // 0xb7ce20: mov             x4, x0
    // 0xb7ce24: ldur            x3, [fp, #-8]
    // 0xb7ce28: ldur            x2, [fp, #-0x20]
    // 0xb7ce2c: ldur            x0, [fp, #-0x40]
    // 0xb7ce30: stur            x4, [fp, #-0x48]
    // 0xb7ce34: LoadField: r1 = r3->field_f
    //     0xb7ce34: ldur            w1, [x3, #0xf]
    // 0xb7ce38: DecompressPointer r1
    //     0xb7ce38: add             x1, x1, HEAP, lsl #32
    // 0xb7ce3c: cmp             w1, NULL
    // 0xb7ce40: b.eq            #0xb7de80
    // 0xb7ce44: r0 = of()
    //     0xb7ce44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7ce48: LoadField: r1 = r0->field_87
    //     0xb7ce48: ldur            w1, [x0, #0x87]
    // 0xb7ce4c: DecompressPointer r1
    //     0xb7ce4c: add             x1, x1, HEAP, lsl #32
    // 0xb7ce50: LoadField: r0 = r1->field_7
    //     0xb7ce50: ldur            w0, [x1, #7]
    // 0xb7ce54: DecompressPointer r0
    //     0xb7ce54: add             x0, x0, HEAP, lsl #32
    // 0xb7ce58: r16 = 12.000000
    //     0xb7ce58: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7ce5c: ldr             x16, [x16, #0x9e8]
    // 0xb7ce60: r30 = Instance_Color
    //     0xb7ce60: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7ce64: stp             lr, x16, [SP]
    // 0xb7ce68: mov             x1, x0
    // 0xb7ce6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7ce6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7ce70: ldr             x4, [x4, #0xaa0]
    // 0xb7ce74: r0 = copyWith()
    //     0xb7ce74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7ce78: stur            x0, [fp, #-0x50]
    // 0xb7ce7c: r0 = Text()
    //     0xb7ce7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7ce80: mov             x3, x0
    // 0xb7ce84: ldur            x0, [fp, #-0x48]
    // 0xb7ce88: stur            x3, [fp, #-0x58]
    // 0xb7ce8c: StoreField: r3->field_b = r0
    //     0xb7ce8c: stur            w0, [x3, #0xb]
    // 0xb7ce90: ldur            x0, [fp, #-0x50]
    // 0xb7ce94: StoreField: r3->field_13 = r0
    //     0xb7ce94: stur            w0, [x3, #0x13]
    // 0xb7ce98: r1 = Null
    //     0xb7ce98: mov             x1, NULL
    // 0xb7ce9c: r2 = 4
    //     0xb7ce9c: movz            x2, #0x4
    // 0xb7cea0: r0 = AllocateArray()
    //     0xb7cea0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7cea4: mov             x2, x0
    // 0xb7cea8: ldur            x0, [fp, #-0x40]
    // 0xb7ceac: stur            x2, [fp, #-0x48]
    // 0xb7ceb0: StoreField: r2->field_f = r0
    //     0xb7ceb0: stur            w0, [x2, #0xf]
    // 0xb7ceb4: ldur            x0, [fp, #-0x58]
    // 0xb7ceb8: StoreField: r2->field_13 = r0
    //     0xb7ceb8: stur            w0, [x2, #0x13]
    // 0xb7cebc: r1 = <Widget>
    //     0xb7cebc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7cec0: r0 = AllocateGrowableArray()
    //     0xb7cec0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7cec4: mov             x3, x0
    // 0xb7cec8: ldur            x0, [fp, #-0x48]
    // 0xb7cecc: stur            x3, [fp, #-0x50]
    // 0xb7ced0: StoreField: r3->field_f = r0
    //     0xb7ced0: stur            w0, [x3, #0xf]
    // 0xb7ced4: r0 = 4
    //     0xb7ced4: movz            x0, #0x4
    // 0xb7ced8: StoreField: r3->field_b = r0
    //     0xb7ced8: stur            w0, [x3, #0xb]
    // 0xb7cedc: ldur            x4, [fp, #-0x20]
    // 0xb7cee0: LoadField: r1 = r4->field_13
    //     0xb7cee0: ldur            w1, [x4, #0x13]
    // 0xb7cee4: DecompressPointer r1
    //     0xb7cee4: add             x1, x1, HEAP, lsl #32
    // 0xb7cee8: cmp             w1, NULL
    // 0xb7ceec: b.eq            #0xb7de84
    // 0xb7cef0: LoadField: r2 = r1->field_e3
    //     0xb7cef0: ldur            w2, [x1, #0xe3]
    // 0xb7cef4: DecompressPointer r2
    //     0xb7cef4: add             x2, x2, HEAP, lsl #32
    // 0xb7cef8: cmp             w2, NULL
    // 0xb7cefc: b.ne            #0xb7cf08
    // 0xb7cf00: mov             x2, x3
    // 0xb7cf04: b               #0xb7d078
    // 0xb7cf08: LoadField: r5 = r2->field_b
    //     0xb7cf08: ldur            w5, [x2, #0xb]
    // 0xb7cf0c: DecompressPointer r5
    //     0xb7cf0c: add             x5, x5, HEAP, lsl #32
    // 0xb7cf10: stur            x5, [fp, #-0x40]
    // 0xb7cf14: cmp             w5, NULL
    // 0xb7cf18: b.eq            #0xb7d074
    // 0xb7cf1c: r1 = Null
    //     0xb7cf1c: mov             x1, NULL
    // 0xb7cf20: r2 = 6
    //     0xb7cf20: movz            x2, #0x6
    // 0xb7cf24: r0 = AllocateArray()
    //     0xb7cf24: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7cf28: r16 = " | ("
    //     0xb7cf28: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xb7cf2c: ldr             x16, [x16, #0xd70]
    // 0xb7cf30: StoreField: r0->field_f = r16
    //     0xb7cf30: stur            w16, [x0, #0xf]
    // 0xb7cf34: ldur            x1, [fp, #-0x40]
    // 0xb7cf38: cmp             w1, NULL
    // 0xb7cf3c: b.ne            #0xb7cf48
    // 0xb7cf40: r3 = Null
    //     0xb7cf40: mov             x3, NULL
    // 0xb7cf44: b               #0xb7cf6c
    // 0xb7cf48: LoadField: d0 = r1->field_7
    //     0xb7cf48: ldur            d0, [x1, #7]
    // 0xb7cf4c: fcmp            d0, d0
    // 0xb7cf50: b.vs            #0xb7de88
    // 0xb7cf54: fcvtzs          x1, d0
    // 0xb7cf58: asr             x16, x1, #0x1e
    // 0xb7cf5c: cmp             x16, x1, asr #63
    // 0xb7cf60: b.ne            #0xb7de88
    // 0xb7cf64: lsl             x1, x1, #1
    // 0xb7cf68: mov             x3, x1
    // 0xb7cf6c: ldur            x2, [fp, #-8]
    // 0xb7cf70: ldur            x1, [fp, #-0x50]
    // 0xb7cf74: StoreField: r0->field_13 = r3
    //     0xb7cf74: stur            w3, [x0, #0x13]
    // 0xb7cf78: r16 = ")"
    //     0xb7cf78: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb7cf7c: ArrayStore: r0[0] = r16  ; List_4
    //     0xb7cf7c: stur            w16, [x0, #0x17]
    // 0xb7cf80: str             x0, [SP]
    // 0xb7cf84: r0 = _interpolate()
    //     0xb7cf84: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb7cf88: mov             x2, x0
    // 0xb7cf8c: ldur            x0, [fp, #-8]
    // 0xb7cf90: stur            x2, [fp, #-0x40]
    // 0xb7cf94: LoadField: r1 = r0->field_f
    //     0xb7cf94: ldur            w1, [x0, #0xf]
    // 0xb7cf98: DecompressPointer r1
    //     0xb7cf98: add             x1, x1, HEAP, lsl #32
    // 0xb7cf9c: cmp             w1, NULL
    // 0xb7cfa0: b.eq            #0xb7deb0
    // 0xb7cfa4: r0 = of()
    //     0xb7cfa4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7cfa8: LoadField: r1 = r0->field_87
    //     0xb7cfa8: ldur            w1, [x0, #0x87]
    // 0xb7cfac: DecompressPointer r1
    //     0xb7cfac: add             x1, x1, HEAP, lsl #32
    // 0xb7cfb0: LoadField: r0 = r1->field_2b
    //     0xb7cfb0: ldur            w0, [x1, #0x2b]
    // 0xb7cfb4: DecompressPointer r0
    //     0xb7cfb4: add             x0, x0, HEAP, lsl #32
    // 0xb7cfb8: r16 = 12.000000
    //     0xb7cfb8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7cfbc: ldr             x16, [x16, #0x9e8]
    // 0xb7cfc0: r30 = Instance_Color
    //     0xb7cfc0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7cfc4: stp             lr, x16, [SP]
    // 0xb7cfc8: mov             x1, x0
    // 0xb7cfcc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7cfcc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7cfd0: ldr             x4, [x4, #0xaa0]
    // 0xb7cfd4: r0 = copyWith()
    //     0xb7cfd4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7cfd8: stur            x0, [fp, #-0x48]
    // 0xb7cfdc: r0 = Text()
    //     0xb7cfdc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7cfe0: mov             x2, x0
    // 0xb7cfe4: ldur            x0, [fp, #-0x40]
    // 0xb7cfe8: stur            x2, [fp, #-0x58]
    // 0xb7cfec: StoreField: r2->field_b = r0
    //     0xb7cfec: stur            w0, [x2, #0xb]
    // 0xb7cff0: ldur            x0, [fp, #-0x48]
    // 0xb7cff4: StoreField: r2->field_13 = r0
    //     0xb7cff4: stur            w0, [x2, #0x13]
    // 0xb7cff8: ldur            x0, [fp, #-0x50]
    // 0xb7cffc: LoadField: r1 = r0->field_b
    //     0xb7cffc: ldur            w1, [x0, #0xb]
    // 0xb7d000: LoadField: r3 = r0->field_f
    //     0xb7d000: ldur            w3, [x0, #0xf]
    // 0xb7d004: DecompressPointer r3
    //     0xb7d004: add             x3, x3, HEAP, lsl #32
    // 0xb7d008: LoadField: r4 = r3->field_b
    //     0xb7d008: ldur            w4, [x3, #0xb]
    // 0xb7d00c: r3 = LoadInt32Instr(r1)
    //     0xb7d00c: sbfx            x3, x1, #1, #0x1f
    // 0xb7d010: stur            x3, [fp, #-0x18]
    // 0xb7d014: r1 = LoadInt32Instr(r4)
    //     0xb7d014: sbfx            x1, x4, #1, #0x1f
    // 0xb7d018: cmp             x3, x1
    // 0xb7d01c: b.ne            #0xb7d028
    // 0xb7d020: mov             x1, x0
    // 0xb7d024: r0 = _growToNextCapacity()
    //     0xb7d024: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb7d028: ldur            x2, [fp, #-0x50]
    // 0xb7d02c: ldur            x3, [fp, #-0x18]
    // 0xb7d030: add             x0, x3, #1
    // 0xb7d034: lsl             x1, x0, #1
    // 0xb7d038: StoreField: r2->field_b = r1
    //     0xb7d038: stur            w1, [x2, #0xb]
    // 0xb7d03c: LoadField: r1 = r2->field_f
    //     0xb7d03c: ldur            w1, [x2, #0xf]
    // 0xb7d040: DecompressPointer r1
    //     0xb7d040: add             x1, x1, HEAP, lsl #32
    // 0xb7d044: ldur            x0, [fp, #-0x58]
    // 0xb7d048: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb7d048: add             x25, x1, x3, lsl #2
    //     0xb7d04c: add             x25, x25, #0xf
    //     0xb7d050: str             w0, [x25]
    //     0xb7d054: tbz             w0, #0, #0xb7d070
    //     0xb7d058: ldurb           w16, [x1, #-1]
    //     0xb7d05c: ldurb           w17, [x0, #-1]
    //     0xb7d060: and             x16, x17, x16, lsr #2
    //     0xb7d064: tst             x16, HEAP, lsr #32
    //     0xb7d068: b.eq            #0xb7d070
    //     0xb7d06c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7d070: b               #0xb7d078
    // 0xb7d074: mov             x2, x3
    // 0xb7d078: ldur            x0, [fp, #-0x38]
    // 0xb7d07c: r0 = Row()
    //     0xb7d07c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb7d080: r2 = Instance_Axis
    //     0xb7d080: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7d084: stur            x0, [fp, #-0x40]
    // 0xb7d088: StoreField: r0->field_f = r2
    //     0xb7d088: stur            w2, [x0, #0xf]
    // 0xb7d08c: r1 = Instance_MainAxisAlignment
    //     0xb7d08c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7d090: ldr             x1, [x1, #0xa08]
    // 0xb7d094: StoreField: r0->field_13 = r1
    //     0xb7d094: stur            w1, [x0, #0x13]
    // 0xb7d098: r3 = Instance_MainAxisSize
    //     0xb7d098: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7d09c: ldr             x3, [x3, #0xa10]
    // 0xb7d0a0: ArrayStore: r0[0] = r3  ; List_4
    //     0xb7d0a0: stur            w3, [x0, #0x17]
    // 0xb7d0a4: r4 = Instance_CrossAxisAlignment
    //     0xb7d0a4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7d0a8: ldr             x4, [x4, #0xa18]
    // 0xb7d0ac: StoreField: r0->field_1b = r4
    //     0xb7d0ac: stur            w4, [x0, #0x1b]
    // 0xb7d0b0: r2 = Instance_VerticalDirection
    //     0xb7d0b0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7d0b4: ldr             x2, [x2, #0xa20]
    // 0xb7d0b8: StoreField: r0->field_23 = r2
    //     0xb7d0b8: stur            w2, [x0, #0x23]
    // 0xb7d0bc: r3 = Instance_Clip
    //     0xb7d0bc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7d0c0: ldr             x3, [x3, #0x38]
    // 0xb7d0c4: StoreField: r0->field_2b = r3
    //     0xb7d0c4: stur            w3, [x0, #0x2b]
    // 0xb7d0c8: StoreField: r0->field_2f = rZR
    //     0xb7d0c8: stur            xzr, [x0, #0x2f]
    // 0xb7d0cc: ldur            x4, [fp, #-0x50]
    // 0xb7d0d0: StoreField: r0->field_b = r4
    //     0xb7d0d0: stur            w4, [x0, #0xb]
    // 0xb7d0d4: r0 = Visibility()
    //     0xb7d0d4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7d0d8: mov             x1, x0
    // 0xb7d0dc: ldur            x0, [fp, #-0x40]
    // 0xb7d0e0: StoreField: r1->field_b = r0
    //     0xb7d0e0: stur            w0, [x1, #0xb]
    // 0xb7d0e4: r5 = Instance_SizedBox
    //     0xb7d0e4: ldr             x5, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7d0e8: StoreField: r1->field_f = r5
    //     0xb7d0e8: stur            w5, [x1, #0xf]
    // 0xb7d0ec: ldur            x0, [fp, #-0x38]
    // 0xb7d0f0: StoreField: r1->field_13 = r0
    //     0xb7d0f0: stur            w0, [x1, #0x13]
    // 0xb7d0f4: r6 = false
    //     0xb7d0f4: add             x6, NULL, #0x30  ; false
    // 0xb7d0f8: ArrayStore: r1[0] = r6  ; List_4
    //     0xb7d0f8: stur            w6, [x1, #0x17]
    // 0xb7d0fc: StoreField: r1->field_1b = r6
    //     0xb7d0fc: stur            w6, [x1, #0x1b]
    // 0xb7d100: StoreField: r1->field_1f = r6
    //     0xb7d100: stur            w6, [x1, #0x1f]
    // 0xb7d104: StoreField: r1->field_23 = r6
    //     0xb7d104: stur            w6, [x1, #0x23]
    // 0xb7d108: StoreField: r1->field_27 = r6
    //     0xb7d108: stur            w6, [x1, #0x27]
    // 0xb7d10c: StoreField: r1->field_2b = r6
    //     0xb7d10c: stur            w6, [x1, #0x2b]
    // 0xb7d110: mov             x0, x5
    // 0xb7d114: mov             x5, x1
    // 0xb7d118: mov             x2, x6
    // 0xb7d11c: b               #0xb7d444
    // 0xb7d120: ldur            x7, [fp, #-0x20]
    // 0xb7d124: r6 = false
    //     0xb7d124: add             x6, NULL, #0x30  ; false
    // 0xb7d128: r5 = Instance_SizedBox
    //     0xb7d128: ldr             x5, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7d12c: r4 = Instance_CrossAxisAlignment
    //     0xb7d12c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7d130: ldr             x4, [x4, #0xa18]
    // 0xb7d134: r3 = Instance_MainAxisSize
    //     0xb7d134: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7d138: ldr             x3, [x3, #0xa10]
    // 0xb7d13c: r2 = Instance_Axis
    //     0xb7d13c: ldr             x2, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7d140: r0 = Instance_BlendMode
    //     0xb7d140: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb7d144: ldr             x0, [x0, #0xb30]
    // 0xb7d148: LoadField: r1 = r7->field_13
    //     0xb7d148: ldur            w1, [x7, #0x13]
    // 0xb7d14c: DecompressPointer r1
    //     0xb7d14c: add             x1, x1, HEAP, lsl #32
    // 0xb7d150: cmp             w1, NULL
    // 0xb7d154: b.eq            #0xb7deb4
    // 0xb7d158: LoadField: r8 = r1->field_e3
    //     0xb7d158: ldur            w8, [x1, #0xe3]
    // 0xb7d15c: DecompressPointer r8
    //     0xb7d15c: add             x8, x8, HEAP, lsl #32
    // 0xb7d160: cmp             w8, NULL
    // 0xb7d164: b.ne            #0xb7d170
    // 0xb7d168: r1 = Null
    //     0xb7d168: mov             x1, NULL
    // 0xb7d16c: b               #0xb7d178
    // 0xb7d170: LoadField: r1 = r8->field_7
    //     0xb7d170: ldur            w1, [x8, #7]
    // 0xb7d174: DecompressPointer r1
    //     0xb7d174: add             x1, x1, HEAP, lsl #32
    // 0xb7d178: ldur            x8, [fp, #-8]
    // 0xb7d17c: cmp             w1, NULL
    // 0xb7d180: r16 = true
    //     0xb7d180: add             x16, NULL, #0x20  ; true
    // 0xb7d184: r17 = false
    //     0xb7d184: add             x17, NULL, #0x30  ; false
    // 0xb7d188: csel            x9, x16, x17, ne
    // 0xb7d18c: stur            x9, [fp, #-0x38]
    // 0xb7d190: LoadField: r1 = r8->field_f
    //     0xb7d190: ldur            w1, [x8, #0xf]
    // 0xb7d194: DecompressPointer r1
    //     0xb7d194: add             x1, x1, HEAP, lsl #32
    // 0xb7d198: cmp             w1, NULL
    // 0xb7d19c: b.eq            #0xb7deb8
    // 0xb7d1a0: r0 = of()
    //     0xb7d1a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d1a4: LoadField: r1 = r0->field_5b
    //     0xb7d1a4: ldur            w1, [x0, #0x5b]
    // 0xb7d1a8: DecompressPointer r1
    //     0xb7d1a8: add             x1, x1, HEAP, lsl #32
    // 0xb7d1ac: stur            x1, [fp, #-0x40]
    // 0xb7d1b0: r0 = ColorFilter()
    //     0xb7d1b0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb7d1b4: mov             x1, x0
    // 0xb7d1b8: ldur            x0, [fp, #-0x40]
    // 0xb7d1bc: stur            x1, [fp, #-0x48]
    // 0xb7d1c0: StoreField: r1->field_7 = r0
    //     0xb7d1c0: stur            w0, [x1, #7]
    // 0xb7d1c4: r0 = Instance_BlendMode
    //     0xb7d1c4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb7d1c8: ldr             x0, [x0, #0xb30]
    // 0xb7d1cc: StoreField: r1->field_b = r0
    //     0xb7d1cc: stur            w0, [x1, #0xb]
    // 0xb7d1d0: r2 = 1
    //     0xb7d1d0: movz            x2, #0x1
    // 0xb7d1d4: StoreField: r1->field_13 = r2
    //     0xb7d1d4: stur            x2, [x1, #0x13]
    // 0xb7d1d8: r0 = SvgPicture()
    //     0xb7d1d8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7d1dc: stur            x0, [fp, #-0x40]
    // 0xb7d1e0: ldur            x16, [fp, #-0x48]
    // 0xb7d1e4: str             x16, [SP]
    // 0xb7d1e8: mov             x1, x0
    // 0xb7d1ec: r2 = "assets/images/green_star.svg"
    //     0xb7d1ec: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb7d1f0: ldr             x2, [x2, #0x9a0]
    // 0xb7d1f4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb7d1f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb7d1f8: ldr             x4, [x4, #0xa38]
    // 0xb7d1fc: r0 = SvgPicture.asset()
    //     0xb7d1fc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7d200: ldur            x0, [fp, #-0x20]
    // 0xb7d204: LoadField: r1 = r0->field_13
    //     0xb7d204: ldur            w1, [x0, #0x13]
    // 0xb7d208: DecompressPointer r1
    //     0xb7d208: add             x1, x1, HEAP, lsl #32
    // 0xb7d20c: cmp             w1, NULL
    // 0xb7d210: b.eq            #0xb7debc
    // 0xb7d214: LoadField: r2 = r1->field_e3
    //     0xb7d214: ldur            w2, [x1, #0xe3]
    // 0xb7d218: DecompressPointer r2
    //     0xb7d218: add             x2, x2, HEAP, lsl #32
    // 0xb7d21c: cmp             w2, NULL
    // 0xb7d220: b.ne            #0xb7d22c
    // 0xb7d224: r0 = Null
    //     0xb7d224: mov             x0, NULL
    // 0xb7d228: b               #0xb7d24c
    // 0xb7d22c: LoadField: r1 = r2->field_7
    //     0xb7d22c: ldur            w1, [x2, #7]
    // 0xb7d230: DecompressPointer r1
    //     0xb7d230: add             x1, x1, HEAP, lsl #32
    // 0xb7d234: cmp             w1, NULL
    // 0xb7d238: b.ne            #0xb7d244
    // 0xb7d23c: r0 = Null
    //     0xb7d23c: mov             x0, NULL
    // 0xb7d240: b               #0xb7d24c
    // 0xb7d244: r2 = 1
    //     0xb7d244: movz            x2, #0x1
    // 0xb7d248: r0 = toStringAsFixed()
    //     0xb7d248: bl              #0x7c1ef8  ; [dart:core] _Double::toStringAsFixed
    // 0xb7d24c: cmp             w0, NULL
    // 0xb7d250: b.ne            #0xb7d25c
    // 0xb7d254: r4 = ""
    //     0xb7d254: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7d258: b               #0xb7d260
    // 0xb7d25c: mov             x4, x0
    // 0xb7d260: ldur            x2, [fp, #-8]
    // 0xb7d264: ldur            x3, [fp, #-0x38]
    // 0xb7d268: ldur            x0, [fp, #-0x40]
    // 0xb7d26c: stur            x4, [fp, #-0x48]
    // 0xb7d270: LoadField: r1 = r2->field_f
    //     0xb7d270: ldur            w1, [x2, #0xf]
    // 0xb7d274: DecompressPointer r1
    //     0xb7d274: add             x1, x1, HEAP, lsl #32
    // 0xb7d278: cmp             w1, NULL
    // 0xb7d27c: b.eq            #0xb7dec0
    // 0xb7d280: r0 = of()
    //     0xb7d280: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d284: LoadField: r1 = r0->field_87
    //     0xb7d284: ldur            w1, [x0, #0x87]
    // 0xb7d288: DecompressPointer r1
    //     0xb7d288: add             x1, x1, HEAP, lsl #32
    // 0xb7d28c: LoadField: r0 = r1->field_7
    //     0xb7d28c: ldur            w0, [x1, #7]
    // 0xb7d290: DecompressPointer r0
    //     0xb7d290: add             x0, x0, HEAP, lsl #32
    // 0xb7d294: r16 = 12.000000
    //     0xb7d294: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7d298: ldr             x16, [x16, #0x9e8]
    // 0xb7d29c: r30 = Instance_Color
    //     0xb7d29c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7d2a0: stp             lr, x16, [SP]
    // 0xb7d2a4: mov             x1, x0
    // 0xb7d2a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7d2a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7d2ac: ldr             x4, [x4, #0xaa0]
    // 0xb7d2b0: r0 = copyWith()
    //     0xb7d2b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7d2b4: stur            x0, [fp, #-0x50]
    // 0xb7d2b8: r0 = Text()
    //     0xb7d2b8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7d2bc: mov             x2, x0
    // 0xb7d2c0: ldur            x0, [fp, #-0x48]
    // 0xb7d2c4: stur            x2, [fp, #-0x58]
    // 0xb7d2c8: StoreField: r2->field_b = r0
    //     0xb7d2c8: stur            w0, [x2, #0xb]
    // 0xb7d2cc: ldur            x0, [fp, #-0x50]
    // 0xb7d2d0: StoreField: r2->field_13 = r0
    //     0xb7d2d0: stur            w0, [x2, #0x13]
    // 0xb7d2d4: ldur            x0, [fp, #-8]
    // 0xb7d2d8: LoadField: r1 = r0->field_f
    //     0xb7d2d8: ldur            w1, [x0, #0xf]
    // 0xb7d2dc: DecompressPointer r1
    //     0xb7d2dc: add             x1, x1, HEAP, lsl #32
    // 0xb7d2e0: cmp             w1, NULL
    // 0xb7d2e4: b.eq            #0xb7dec4
    // 0xb7d2e8: r0 = of()
    //     0xb7d2e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d2ec: LoadField: r1 = r0->field_87
    //     0xb7d2ec: ldur            w1, [x0, #0x87]
    // 0xb7d2f0: DecompressPointer r1
    //     0xb7d2f0: add             x1, x1, HEAP, lsl #32
    // 0xb7d2f4: LoadField: r0 = r1->field_2b
    //     0xb7d2f4: ldur            w0, [x1, #0x2b]
    // 0xb7d2f8: DecompressPointer r0
    //     0xb7d2f8: add             x0, x0, HEAP, lsl #32
    // 0xb7d2fc: ldur            x2, [fp, #-8]
    // 0xb7d300: stur            x0, [fp, #-0x48]
    // 0xb7d304: LoadField: r1 = r2->field_f
    //     0xb7d304: ldur            w1, [x2, #0xf]
    // 0xb7d308: DecompressPointer r1
    //     0xb7d308: add             x1, x1, HEAP, lsl #32
    // 0xb7d30c: cmp             w1, NULL
    // 0xb7d310: b.eq            #0xb7dec8
    // 0xb7d314: r0 = of()
    //     0xb7d314: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d318: LoadField: r1 = r0->field_5b
    //     0xb7d318: ldur            w1, [x0, #0x5b]
    // 0xb7d31c: DecompressPointer r1
    //     0xb7d31c: add             x1, x1, HEAP, lsl #32
    // 0xb7d320: r16 = 10.000000
    //     0xb7d320: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb7d324: stp             x1, x16, [SP]
    // 0xb7d328: ldur            x1, [fp, #-0x48]
    // 0xb7d32c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7d32c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7d330: ldr             x4, [x4, #0xaa0]
    // 0xb7d334: r0 = copyWith()
    //     0xb7d334: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7d338: stur            x0, [fp, #-0x48]
    // 0xb7d33c: r0 = Text()
    //     0xb7d33c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7d340: mov             x3, x0
    // 0xb7d344: r0 = " Brand Rating"
    //     0xb7d344: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xb7d348: ldr             x0, [x0, #0xd78]
    // 0xb7d34c: stur            x3, [fp, #-0x50]
    // 0xb7d350: StoreField: r3->field_b = r0
    //     0xb7d350: stur            w0, [x3, #0xb]
    // 0xb7d354: ldur            x0, [fp, #-0x48]
    // 0xb7d358: StoreField: r3->field_13 = r0
    //     0xb7d358: stur            w0, [x3, #0x13]
    // 0xb7d35c: r1 = Null
    //     0xb7d35c: mov             x1, NULL
    // 0xb7d360: r2 = 6
    //     0xb7d360: movz            x2, #0x6
    // 0xb7d364: r0 = AllocateArray()
    //     0xb7d364: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7d368: mov             x2, x0
    // 0xb7d36c: ldur            x0, [fp, #-0x40]
    // 0xb7d370: stur            x2, [fp, #-0x48]
    // 0xb7d374: StoreField: r2->field_f = r0
    //     0xb7d374: stur            w0, [x2, #0xf]
    // 0xb7d378: ldur            x0, [fp, #-0x58]
    // 0xb7d37c: StoreField: r2->field_13 = r0
    //     0xb7d37c: stur            w0, [x2, #0x13]
    // 0xb7d380: ldur            x0, [fp, #-0x50]
    // 0xb7d384: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7d384: stur            w0, [x2, #0x17]
    // 0xb7d388: r1 = <Widget>
    //     0xb7d388: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7d38c: r0 = AllocateGrowableArray()
    //     0xb7d38c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7d390: mov             x1, x0
    // 0xb7d394: ldur            x0, [fp, #-0x48]
    // 0xb7d398: stur            x1, [fp, #-0x40]
    // 0xb7d39c: StoreField: r1->field_f = r0
    //     0xb7d39c: stur            w0, [x1, #0xf]
    // 0xb7d3a0: r2 = 6
    //     0xb7d3a0: movz            x2, #0x6
    // 0xb7d3a4: StoreField: r1->field_b = r2
    //     0xb7d3a4: stur            w2, [x1, #0xb]
    // 0xb7d3a8: r0 = Row()
    //     0xb7d3a8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb7d3ac: mov             x1, x0
    // 0xb7d3b0: r0 = Instance_Axis
    //     0xb7d3b0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7d3b4: stur            x1, [fp, #-0x48]
    // 0xb7d3b8: StoreField: r1->field_f = r0
    //     0xb7d3b8: stur            w0, [x1, #0xf]
    // 0xb7d3bc: r0 = Instance_MainAxisAlignment
    //     0xb7d3bc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7d3c0: ldr             x0, [x0, #0xa08]
    // 0xb7d3c4: StoreField: r1->field_13 = r0
    //     0xb7d3c4: stur            w0, [x1, #0x13]
    // 0xb7d3c8: r2 = Instance_MainAxisSize
    //     0xb7d3c8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7d3cc: ldr             x2, [x2, #0xa10]
    // 0xb7d3d0: ArrayStore: r1[0] = r2  ; List_4
    //     0xb7d3d0: stur            w2, [x1, #0x17]
    // 0xb7d3d4: r2 = Instance_CrossAxisAlignment
    //     0xb7d3d4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7d3d8: ldr             x2, [x2, #0xa18]
    // 0xb7d3dc: StoreField: r1->field_1b = r2
    //     0xb7d3dc: stur            w2, [x1, #0x1b]
    // 0xb7d3e0: r2 = Instance_VerticalDirection
    //     0xb7d3e0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7d3e4: ldr             x2, [x2, #0xa20]
    // 0xb7d3e8: StoreField: r1->field_23 = r2
    //     0xb7d3e8: stur            w2, [x1, #0x23]
    // 0xb7d3ec: r3 = Instance_Clip
    //     0xb7d3ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7d3f0: ldr             x3, [x3, #0x38]
    // 0xb7d3f4: StoreField: r1->field_2b = r3
    //     0xb7d3f4: stur            w3, [x1, #0x2b]
    // 0xb7d3f8: StoreField: r1->field_2f = rZR
    //     0xb7d3f8: stur            xzr, [x1, #0x2f]
    // 0xb7d3fc: ldur            x4, [fp, #-0x40]
    // 0xb7d400: StoreField: r1->field_b = r4
    //     0xb7d400: stur            w4, [x1, #0xb]
    // 0xb7d404: r0 = Visibility()
    //     0xb7d404: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7d408: mov             x1, x0
    // 0xb7d40c: ldur            x0, [fp, #-0x48]
    // 0xb7d410: StoreField: r1->field_b = r0
    //     0xb7d410: stur            w0, [x1, #0xb]
    // 0xb7d414: r0 = Instance_SizedBox
    //     0xb7d414: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7d418: StoreField: r1->field_f = r0
    //     0xb7d418: stur            w0, [x1, #0xf]
    // 0xb7d41c: ldur            x2, [fp, #-0x38]
    // 0xb7d420: StoreField: r1->field_13 = r2
    //     0xb7d420: stur            w2, [x1, #0x13]
    // 0xb7d424: r2 = false
    //     0xb7d424: add             x2, NULL, #0x30  ; false
    // 0xb7d428: ArrayStore: r1[0] = r2  ; List_4
    //     0xb7d428: stur            w2, [x1, #0x17]
    // 0xb7d42c: StoreField: r1->field_1b = r2
    //     0xb7d42c: stur            w2, [x1, #0x1b]
    // 0xb7d430: StoreField: r1->field_1f = r2
    //     0xb7d430: stur            w2, [x1, #0x1f]
    // 0xb7d434: StoreField: r1->field_23 = r2
    //     0xb7d434: stur            w2, [x1, #0x23]
    // 0xb7d438: StoreField: r1->field_27 = r2
    //     0xb7d438: stur            w2, [x1, #0x27]
    // 0xb7d43c: StoreField: r1->field_2b = r2
    //     0xb7d43c: stur            w2, [x1, #0x2b]
    // 0xb7d440: mov             x5, x1
    // 0xb7d444: ldur            x1, [fp, #-8]
    // 0xb7d448: ldur            x3, [fp, #-0x20]
    // 0xb7d44c: ldur            x4, [fp, #-0x30]
    // 0xb7d450: stur            x5, [fp, #-0x38]
    // 0xb7d454: r0 = Visibility()
    //     0xb7d454: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7d458: mov             x3, x0
    // 0xb7d45c: ldur            x0, [fp, #-0x38]
    // 0xb7d460: stur            x3, [fp, #-0x40]
    // 0xb7d464: StoreField: r3->field_b = r0
    //     0xb7d464: stur            w0, [x3, #0xb]
    // 0xb7d468: r0 = Instance_SizedBox
    //     0xb7d468: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7d46c: StoreField: r3->field_f = r0
    //     0xb7d46c: stur            w0, [x3, #0xf]
    // 0xb7d470: ldur            x1, [fp, #-0x30]
    // 0xb7d474: StoreField: r3->field_13 = r1
    //     0xb7d474: stur            w1, [x3, #0x13]
    // 0xb7d478: r4 = false
    //     0xb7d478: add             x4, NULL, #0x30  ; false
    // 0xb7d47c: ArrayStore: r3[0] = r4  ; List_4
    //     0xb7d47c: stur            w4, [x3, #0x17]
    // 0xb7d480: StoreField: r3->field_1b = r4
    //     0xb7d480: stur            w4, [x3, #0x1b]
    // 0xb7d484: StoreField: r3->field_1f = r4
    //     0xb7d484: stur            w4, [x3, #0x1f]
    // 0xb7d488: StoreField: r3->field_23 = r4
    //     0xb7d488: stur            w4, [x3, #0x23]
    // 0xb7d48c: StoreField: r3->field_27 = r4
    //     0xb7d48c: stur            w4, [x3, #0x27]
    // 0xb7d490: StoreField: r3->field_2b = r4
    //     0xb7d490: stur            w4, [x3, #0x2b]
    // 0xb7d494: ldur            x5, [fp, #-0x20]
    // 0xb7d498: LoadField: r1 = r5->field_13
    //     0xb7d498: ldur            w1, [x5, #0x13]
    // 0xb7d49c: DecompressPointer r1
    //     0xb7d49c: add             x1, x1, HEAP, lsl #32
    // 0xb7d4a0: cmp             w1, NULL
    // 0xb7d4a4: b.eq            #0xb7decc
    // 0xb7d4a8: LoadField: r6 = r1->field_f3
    //     0xb7d4a8: ldur            w6, [x1, #0xf3]
    // 0xb7d4ac: DecompressPointer r6
    //     0xb7d4ac: add             x6, x6, HEAP, lsl #32
    // 0xb7d4b0: stur            x6, [fp, #-0x30]
    // 0xb7d4b4: r1 = Null
    //     0xb7d4b4: mov             x1, NULL
    // 0xb7d4b8: r2 = 4
    //     0xb7d4b8: movz            x2, #0x4
    // 0xb7d4bc: r0 = AllocateArray()
    //     0xb7d4bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7d4c0: mov             x1, x0
    // 0xb7d4c4: ldur            x0, [fp, #-0x30]
    // 0xb7d4c8: StoreField: r1->field_f = r0
    //     0xb7d4c8: stur            w0, [x1, #0xf]
    // 0xb7d4cc: r16 = " "
    //     0xb7d4cc: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb7d4d0: StoreField: r1->field_13 = r16
    //     0xb7d4d0: stur            w16, [x1, #0x13]
    // 0xb7d4d4: str             x1, [SP]
    // 0xb7d4d8: r0 = _interpolate()
    //     0xb7d4d8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb7d4dc: mov             x2, x0
    // 0xb7d4e0: ldur            x0, [fp, #-8]
    // 0xb7d4e4: stur            x2, [fp, #-0x30]
    // 0xb7d4e8: LoadField: r1 = r0->field_f
    //     0xb7d4e8: ldur            w1, [x0, #0xf]
    // 0xb7d4ec: DecompressPointer r1
    //     0xb7d4ec: add             x1, x1, HEAP, lsl #32
    // 0xb7d4f0: cmp             w1, NULL
    // 0xb7d4f4: b.eq            #0xb7ded0
    // 0xb7d4f8: r0 = of()
    //     0xb7d4f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d4fc: LoadField: r1 = r0->field_87
    //     0xb7d4fc: ldur            w1, [x0, #0x87]
    // 0xb7d500: DecompressPointer r1
    //     0xb7d500: add             x1, x1, HEAP, lsl #32
    // 0xb7d504: LoadField: r0 = r1->field_27
    //     0xb7d504: ldur            w0, [x1, #0x27]
    // 0xb7d508: DecompressPointer r0
    //     0xb7d508: add             x0, x0, HEAP, lsl #32
    // 0xb7d50c: r16 = 21.000000
    //     0xb7d50c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xb7d510: ldr             x16, [x16, #0x9b0]
    // 0xb7d514: r30 = Instance_Color
    //     0xb7d514: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7d518: stp             lr, x16, [SP]
    // 0xb7d51c: mov             x1, x0
    // 0xb7d520: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7d520: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7d524: ldr             x4, [x4, #0xaa0]
    // 0xb7d528: r0 = copyWith()
    //     0xb7d528: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7d52c: stur            x0, [fp, #-0x38]
    // 0xb7d530: r0 = Text()
    //     0xb7d530: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7d534: mov             x1, x0
    // 0xb7d538: ldur            x0, [fp, #-0x30]
    // 0xb7d53c: stur            x1, [fp, #-0x48]
    // 0xb7d540: StoreField: r1->field_b = r0
    //     0xb7d540: stur            w0, [x1, #0xb]
    // 0xb7d544: ldur            x0, [fp, #-0x38]
    // 0xb7d548: StoreField: r1->field_13 = r0
    //     0xb7d548: stur            w0, [x1, #0x13]
    // 0xb7d54c: r0 = WidgetSpan()
    //     0xb7d54c: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xb7d550: mov             x3, x0
    // 0xb7d554: ldur            x0, [fp, #-0x48]
    // 0xb7d558: stur            x3, [fp, #-0x38]
    // 0xb7d55c: StoreField: r3->field_13 = r0
    //     0xb7d55c: stur            w0, [x3, #0x13]
    // 0xb7d560: r0 = Instance_PlaceholderAlignment
    //     0xb7d560: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xb7d564: ldr             x0, [x0, #0xa0]
    // 0xb7d568: StoreField: r3->field_b = r0
    //     0xb7d568: stur            w0, [x3, #0xb]
    // 0xb7d56c: ldur            x0, [fp, #-0x20]
    // 0xb7d570: LoadField: r1 = r0->field_13
    //     0xb7d570: ldur            w1, [x0, #0x13]
    // 0xb7d574: DecompressPointer r1
    //     0xb7d574: add             x1, x1, HEAP, lsl #32
    // 0xb7d578: cmp             w1, NULL
    // 0xb7d57c: b.eq            #0xb7ded4
    // 0xb7d580: LoadField: r4 = r1->field_fb
    //     0xb7d580: ldur            w4, [x1, #0xfb]
    // 0xb7d584: DecompressPointer r4
    //     0xb7d584: add             x4, x4, HEAP, lsl #32
    // 0xb7d588: stur            x4, [fp, #-0x30]
    // 0xb7d58c: r1 = Null
    //     0xb7d58c: mov             x1, NULL
    // 0xb7d590: r2 = 4
    //     0xb7d590: movz            x2, #0x4
    // 0xb7d594: r0 = AllocateArray()
    //     0xb7d594: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7d598: mov             x1, x0
    // 0xb7d59c: ldur            x0, [fp, #-0x30]
    // 0xb7d5a0: StoreField: r1->field_f = r0
    //     0xb7d5a0: stur            w0, [x1, #0xf]
    // 0xb7d5a4: r16 = " "
    //     0xb7d5a4: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb7d5a8: StoreField: r1->field_13 = r16
    //     0xb7d5a8: stur            w16, [x1, #0x13]
    // 0xb7d5ac: str             x1, [SP]
    // 0xb7d5b0: r0 = _interpolate()
    //     0xb7d5b0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb7d5b4: mov             x2, x0
    // 0xb7d5b8: ldur            x0, [fp, #-8]
    // 0xb7d5bc: stur            x2, [fp, #-0x30]
    // 0xb7d5c0: LoadField: r1 = r0->field_f
    //     0xb7d5c0: ldur            w1, [x0, #0xf]
    // 0xb7d5c4: DecompressPointer r1
    //     0xb7d5c4: add             x1, x1, HEAP, lsl #32
    // 0xb7d5c8: cmp             w1, NULL
    // 0xb7d5cc: b.eq            #0xb7ded8
    // 0xb7d5d0: r0 = of()
    //     0xb7d5d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d5d4: LoadField: r1 = r0->field_87
    //     0xb7d5d4: ldur            w1, [x0, #0x87]
    // 0xb7d5d8: DecompressPointer r1
    //     0xb7d5d8: add             x1, x1, HEAP, lsl #32
    // 0xb7d5dc: LoadField: r0 = r1->field_2b
    //     0xb7d5dc: ldur            w0, [x1, #0x2b]
    // 0xb7d5e0: DecompressPointer r0
    //     0xb7d5e0: add             x0, x0, HEAP, lsl #32
    // 0xb7d5e4: stur            x0, [fp, #-0x48]
    // 0xb7d5e8: r1 = Instance_Color
    //     0xb7d5e8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7d5ec: d0 = 0.400000
    //     0xb7d5ec: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xb7d5f0: r0 = withOpacity()
    //     0xb7d5f0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb7d5f4: r16 = Instance_TextDecoration
    //     0xb7d5f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb7d5f8: ldr             x16, [x16, #0xe30]
    // 0xb7d5fc: r30 = 10.000000
    //     0xb7d5fc: ldr             lr, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb7d600: stp             lr, x16, [SP, #8]
    // 0xb7d604: str             x0, [SP]
    // 0xb7d608: ldur            x1, [fp, #-0x48]
    // 0xb7d60c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb7d60c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb7d610: ldr             x4, [x4, #0xb60]
    // 0xb7d614: r0 = copyWith()
    //     0xb7d614: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7d618: stur            x0, [fp, #-0x48]
    // 0xb7d61c: r0 = TextSpan()
    //     0xb7d61c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7d620: mov             x3, x0
    // 0xb7d624: ldur            x0, [fp, #-0x30]
    // 0xb7d628: stur            x3, [fp, #-0x50]
    // 0xb7d62c: StoreField: r3->field_b = r0
    //     0xb7d62c: stur            w0, [x3, #0xb]
    // 0xb7d630: r0 = Instance__DeferringMouseCursor
    //     0xb7d630: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7d634: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7d634: stur            w0, [x3, #0x17]
    // 0xb7d638: ldur            x1, [fp, #-0x48]
    // 0xb7d63c: StoreField: r3->field_7 = r1
    //     0xb7d63c: stur            w1, [x3, #7]
    // 0xb7d640: r1 = Null
    //     0xb7d640: mov             x1, NULL
    // 0xb7d644: r2 = 6
    //     0xb7d644: movz            x2, #0x6
    // 0xb7d648: r0 = AllocateArray()
    //     0xb7d648: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7d64c: stur            x0, [fp, #-0x30]
    // 0xb7d650: r16 = "| "
    //     0xb7d650: add             x16, PP, #0x55, lsl #12  ; [pp+0x55938] "| "
    //     0xb7d654: ldr             x16, [x16, #0x938]
    // 0xb7d658: StoreField: r0->field_f = r16
    //     0xb7d658: stur            w16, [x0, #0xf]
    // 0xb7d65c: ldur            x2, [fp, #-0x20]
    // 0xb7d660: LoadField: r1 = r2->field_13
    //     0xb7d660: ldur            w1, [x2, #0x13]
    // 0xb7d664: DecompressPointer r1
    //     0xb7d664: add             x1, x1, HEAP, lsl #32
    // 0xb7d668: cmp             w1, NULL
    // 0xb7d66c: b.eq            #0xb7dedc
    // 0xb7d670: LoadField: r3 = r1->field_5f
    //     0xb7d670: ldur            w3, [x1, #0x5f]
    // 0xb7d674: DecompressPointer r3
    //     0xb7d674: add             x3, x3, HEAP, lsl #32
    // 0xb7d678: stp             xzr, x3, [SP]
    // 0xb7d67c: r4 = 0
    //     0xb7d67c: movz            x4, #0
    // 0xb7d680: ldr             x0, [SP, #8]
    // 0xb7d684: r16 = UnlinkedCall_0x613b5c
    //     0xb7d684: add             x16, PP, #0x55, lsl #12  ; [pp+0x55940] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb7d688: add             x16, x16, #0x940
    // 0xb7d68c: ldp             x5, lr, [x16]
    // 0xb7d690: blr             lr
    // 0xb7d694: ldur            x1, [fp, #-0x30]
    // 0xb7d698: ArrayStore: r1[1] = r0  ; List_4
    //     0xb7d698: add             x25, x1, #0x13
    //     0xb7d69c: str             w0, [x25]
    //     0xb7d6a0: tbz             w0, #0, #0xb7d6bc
    //     0xb7d6a4: ldurb           w16, [x1, #-1]
    //     0xb7d6a8: ldurb           w17, [x0, #-1]
    //     0xb7d6ac: and             x16, x17, x16, lsr #2
    //     0xb7d6b0: tst             x16, HEAP, lsr #32
    //     0xb7d6b4: b.eq            #0xb7d6bc
    //     0xb7d6b8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb7d6bc: ldur            x0, [fp, #-0x30]
    // 0xb7d6c0: r16 = "% OFF"
    //     0xb7d6c0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d98] "% OFF"
    //     0xb7d6c4: ldr             x16, [x16, #0xd98]
    // 0xb7d6c8: ArrayStore: r0[0] = r16  ; List_4
    //     0xb7d6c8: stur            w16, [x0, #0x17]
    // 0xb7d6cc: str             x0, [SP]
    // 0xb7d6d0: r0 = _interpolate()
    //     0xb7d6d0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb7d6d4: mov             x2, x0
    // 0xb7d6d8: ldur            x0, [fp, #-8]
    // 0xb7d6dc: stur            x2, [fp, #-0x30]
    // 0xb7d6e0: LoadField: r1 = r0->field_f
    //     0xb7d6e0: ldur            w1, [x0, #0xf]
    // 0xb7d6e4: DecompressPointer r1
    //     0xb7d6e4: add             x1, x1, HEAP, lsl #32
    // 0xb7d6e8: cmp             w1, NULL
    // 0xb7d6ec: b.eq            #0xb7dee0
    // 0xb7d6f0: r0 = of()
    //     0xb7d6f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d6f4: LoadField: r1 = r0->field_87
    //     0xb7d6f4: ldur            w1, [x0, #0x87]
    // 0xb7d6f8: DecompressPointer r1
    //     0xb7d6f8: add             x1, x1, HEAP, lsl #32
    // 0xb7d6fc: LoadField: r0 = r1->field_2b
    //     0xb7d6fc: ldur            w0, [x1, #0x2b]
    // 0xb7d700: DecompressPointer r0
    //     0xb7d700: add             x0, x0, HEAP, lsl #32
    // 0xb7d704: r16 = Instance_Color
    //     0xb7d704: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xb7d708: ldr             x16, [x16, #0x858]
    // 0xb7d70c: r30 = 12.000000
    //     0xb7d70c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7d710: ldr             lr, [lr, #0x9e8]
    // 0xb7d714: stp             lr, x16, [SP]
    // 0xb7d718: mov             x1, x0
    // 0xb7d71c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb7d71c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb7d720: ldr             x4, [x4, #0x9b8]
    // 0xb7d724: r0 = copyWith()
    //     0xb7d724: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7d728: stur            x0, [fp, #-0x48]
    // 0xb7d72c: r0 = TextSpan()
    //     0xb7d72c: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7d730: mov             x3, x0
    // 0xb7d734: ldur            x0, [fp, #-0x30]
    // 0xb7d738: stur            x3, [fp, #-0x58]
    // 0xb7d73c: StoreField: r3->field_b = r0
    //     0xb7d73c: stur            w0, [x3, #0xb]
    // 0xb7d740: r0 = Instance__DeferringMouseCursor
    //     0xb7d740: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7d744: ArrayStore: r3[0] = r0  ; List_4
    //     0xb7d744: stur            w0, [x3, #0x17]
    // 0xb7d748: ldur            x1, [fp, #-0x48]
    // 0xb7d74c: StoreField: r3->field_7 = r1
    //     0xb7d74c: stur            w1, [x3, #7]
    // 0xb7d750: r1 = Null
    //     0xb7d750: mov             x1, NULL
    // 0xb7d754: r2 = 6
    //     0xb7d754: movz            x2, #0x6
    // 0xb7d758: r0 = AllocateArray()
    //     0xb7d758: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7d75c: mov             x2, x0
    // 0xb7d760: ldur            x0, [fp, #-0x38]
    // 0xb7d764: stur            x2, [fp, #-0x30]
    // 0xb7d768: StoreField: r2->field_f = r0
    //     0xb7d768: stur            w0, [x2, #0xf]
    // 0xb7d76c: ldur            x0, [fp, #-0x50]
    // 0xb7d770: StoreField: r2->field_13 = r0
    //     0xb7d770: stur            w0, [x2, #0x13]
    // 0xb7d774: ldur            x0, [fp, #-0x58]
    // 0xb7d778: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7d778: stur            w0, [x2, #0x17]
    // 0xb7d77c: r1 = <InlineSpan>
    //     0xb7d77c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb7d780: ldr             x1, [x1, #0xe40]
    // 0xb7d784: r0 = AllocateGrowableArray()
    //     0xb7d784: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7d788: mov             x1, x0
    // 0xb7d78c: ldur            x0, [fp, #-0x30]
    // 0xb7d790: stur            x1, [fp, #-0x38]
    // 0xb7d794: StoreField: r1->field_f = r0
    //     0xb7d794: stur            w0, [x1, #0xf]
    // 0xb7d798: r0 = 6
    //     0xb7d798: movz            x0, #0x6
    // 0xb7d79c: StoreField: r1->field_b = r0
    //     0xb7d79c: stur            w0, [x1, #0xb]
    // 0xb7d7a0: r0 = TextSpan()
    //     0xb7d7a0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb7d7a4: mov             x1, x0
    // 0xb7d7a8: ldur            x0, [fp, #-0x38]
    // 0xb7d7ac: stur            x1, [fp, #-0x30]
    // 0xb7d7b0: StoreField: r1->field_f = r0
    //     0xb7d7b0: stur            w0, [x1, #0xf]
    // 0xb7d7b4: r0 = Instance__DeferringMouseCursor
    //     0xb7d7b4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb7d7b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7d7b8: stur            w0, [x1, #0x17]
    // 0xb7d7bc: r0 = RichText()
    //     0xb7d7bc: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb7d7c0: mov             x1, x0
    // 0xb7d7c4: ldur            x2, [fp, #-0x30]
    // 0xb7d7c8: stur            x0, [fp, #-0x30]
    // 0xb7d7cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb7d7cc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb7d7d0: r0 = RichText()
    //     0xb7d7d0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb7d7d4: r0 = Padding()
    //     0xb7d7d4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7d7d8: mov             x1, x0
    // 0xb7d7dc: r0 = Instance_EdgeInsets
    //     0xb7d7dc: add             x0, PP, #0x55, lsl #12  ; [pp+0x55930] Obj!EdgeInsets@d586a1
    //     0xb7d7e0: ldr             x0, [x0, #0x930]
    // 0xb7d7e4: stur            x1, [fp, #-0x38]
    // 0xb7d7e8: StoreField: r1->field_f = r0
    //     0xb7d7e8: stur            w0, [x1, #0xf]
    // 0xb7d7ec: ldur            x0, [fp, #-0x30]
    // 0xb7d7f0: StoreField: r1->field_b = r0
    //     0xb7d7f0: stur            w0, [x1, #0xb]
    // 0xb7d7f4: ldur            x0, [fp, #-8]
    // 0xb7d7f8: LoadField: r2 = r0->field_b
    //     0xb7d7f8: ldur            w2, [x0, #0xb]
    // 0xb7d7fc: DecompressPointer r2
    //     0xb7d7fc: add             x2, x2, HEAP, lsl #32
    // 0xb7d800: cmp             w2, NULL
    // 0xb7d804: b.eq            #0xb7dee4
    // 0xb7d808: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xb7d808: ldur            w3, [x2, #0x17]
    // 0xb7d80c: DecompressPointer r3
    //     0xb7d80c: add             x3, x3, HEAP, lsl #32
    // 0xb7d810: LoadField: r2 = r3->field_1f
    //     0xb7d810: ldur            w2, [x3, #0x1f]
    // 0xb7d814: DecompressPointer r2
    //     0xb7d814: add             x2, x2, HEAP, lsl #32
    // 0xb7d818: cmp             w2, NULL
    // 0xb7d81c: b.ne            #0xb7d828
    // 0xb7d820: r2 = Null
    //     0xb7d820: mov             x2, NULL
    // 0xb7d824: b               #0xb7d834
    // 0xb7d828: LoadField: r4 = r2->field_7
    //     0xb7d828: ldur            w4, [x2, #7]
    // 0xb7d82c: DecompressPointer r4
    //     0xb7d82c: add             x4, x4, HEAP, lsl #32
    // 0xb7d830: mov             x2, x4
    // 0xb7d834: cmp             w2, NULL
    // 0xb7d838: b.ne            #0xb7d840
    // 0xb7d83c: r2 = false
    //     0xb7d83c: add             x2, NULL, #0x30  ; false
    // 0xb7d840: stur            x2, [fp, #-0x30]
    // 0xb7d844: LoadField: r4 = r3->field_3f
    //     0xb7d844: ldur            w4, [x3, #0x3f]
    // 0xb7d848: DecompressPointer r4
    //     0xb7d848: add             x4, x4, HEAP, lsl #32
    // 0xb7d84c: cmp             w4, NULL
    // 0xb7d850: b.ne            #0xb7d85c
    // 0xb7d854: r3 = Null
    //     0xb7d854: mov             x3, NULL
    // 0xb7d858: b               #0xb7d864
    // 0xb7d85c: LoadField: r3 = r4->field_23
    //     0xb7d85c: ldur            w3, [x4, #0x23]
    // 0xb7d860: DecompressPointer r3
    //     0xb7d860: add             x3, x3, HEAP, lsl #32
    // 0xb7d864: cmp             w3, NULL
    // 0xb7d868: b.eq            #0xb7dc84
    // 0xb7d86c: tbnz            w3, #4, #0xb7dc84
    // 0xb7d870: ldur            x3, [fp, #-0x20]
    // 0xb7d874: r16 = <Size?>
    //     0xb7d874: add             x16, PP, #0x27, lsl #12  ; [pp+0x27768] TypeArguments: <Size?>
    //     0xb7d878: ldr             x16, [x16, #0x768]
    // 0xb7d87c: r30 = Instance_Size
    //     0xb7d87c: add             lr, PP, #0x55, lsl #12  ; [pp+0x55950] Obj!Size@d6c241
    //     0xb7d880: ldr             lr, [lr, #0x950]
    // 0xb7d884: stp             lr, x16, [SP]
    // 0xb7d888: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7d888: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7d88c: r0 = all()
    //     0xb7d88c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7d890: ldur            x2, [fp, #-0x20]
    // 0xb7d894: stur            x0, [fp, #-0x48]
    // 0xb7d898: LoadField: r1 = r2->field_13
    //     0xb7d898: ldur            w1, [x2, #0x13]
    // 0xb7d89c: DecompressPointer r1
    //     0xb7d89c: add             x1, x1, HEAP, lsl #32
    // 0xb7d8a0: cmp             w1, NULL
    // 0xb7d8a4: b.eq            #0xb7dee8
    // 0xb7d8a8: r17 = 275
    //     0xb7d8a8: movz            x17, #0x113
    // 0xb7d8ac: ldr             w3, [x1, x17]
    // 0xb7d8b0: DecompressPointer r3
    //     0xb7d8b0: add             x3, x3, HEAP, lsl #32
    // 0xb7d8b4: cmp             w3, NULL
    // 0xb7d8b8: b.eq            #0xb7d8c0
    // 0xb7d8bc: tbnz            w3, #4, #0xb7d8e0
    // 0xb7d8c0: r16 = <Color>
    //     0xb7d8c0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb7d8c4: ldr             x16, [x16, #0xf80]
    // 0xb7d8c8: r30 = Instance_MaterialColor
    //     0xb7d8c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb7d8cc: ldr             lr, [lr, #0xdc0]
    // 0xb7d8d0: stp             lr, x16, [SP]
    // 0xb7d8d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7d8d4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7d8d8: r0 = all()
    //     0xb7d8d8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7d8dc: b               #0xb7d8f8
    // 0xb7d8e0: r16 = <Color>
    //     0xb7d8e0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb7d8e4: ldr             x16, [x16, #0xf80]
    // 0xb7d8e8: r30 = Instance_Color
    //     0xb7d8e8: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7d8ec: stp             lr, x16, [SP]
    // 0xb7d8f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7d8f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7d8f4: r0 = all()
    //     0xb7d8f4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7d8f8: ldur            x2, [fp, #-0x20]
    // 0xb7d8fc: stur            x0, [fp, #-0x50]
    // 0xb7d900: LoadField: r1 = r2->field_13
    //     0xb7d900: ldur            w1, [x2, #0x13]
    // 0xb7d904: DecompressPointer r1
    //     0xb7d904: add             x1, x1, HEAP, lsl #32
    // 0xb7d908: cmp             w1, NULL
    // 0xb7d90c: b.eq            #0xb7deec
    // 0xb7d910: r17 = 275
    //     0xb7d910: movz            x17, #0x113
    // 0xb7d914: ldr             w3, [x1, x17]
    // 0xb7d918: DecompressPointer r3
    //     0xb7d918: add             x3, x3, HEAP, lsl #32
    // 0xb7d91c: cmp             w3, NULL
    // 0xb7d920: b.eq            #0xb7d928
    // 0xb7d924: tbnz            w3, #4, #0xb7d950
    // 0xb7d928: r1 = Instance_MaterialColor
    //     0xb7d928: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xb7d92c: ldr             x1, [x1, #0xdc0]
    // 0xb7d930: d0 = 0.200000
    //     0xb7d930: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0xb7d934: r0 = withOpacity()
    //     0xb7d934: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb7d938: r16 = <Color>
    //     0xb7d938: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb7d93c: ldr             x16, [x16, #0xf80]
    // 0xb7d940: stp             x0, x16, [SP]
    // 0xb7d944: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7d944: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7d948: r0 = all()
    //     0xb7d948: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7d94c: b               #0xb7d968
    // 0xb7d950: r16 = <Color>
    //     0xb7d950: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb7d954: ldr             x16, [x16, #0xf80]
    // 0xb7d958: r30 = Instance_Color
    //     0xb7d958: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7d95c: stp             lr, x16, [SP]
    // 0xb7d960: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7d960: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7d964: r0 = all()
    //     0xb7d964: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7d968: ldur            x2, [fp, #-0x20]
    // 0xb7d96c: stur            x0, [fp, #-0x58]
    // 0xb7d970: LoadField: r1 = r2->field_13
    //     0xb7d970: ldur            w1, [x2, #0x13]
    // 0xb7d974: DecompressPointer r1
    //     0xb7d974: add             x1, x1, HEAP, lsl #32
    // 0xb7d978: cmp             w1, NULL
    // 0xb7d97c: b.eq            #0xb7def0
    // 0xb7d980: r17 = 275
    //     0xb7d980: movz            x17, #0x113
    // 0xb7d984: ldr             w3, [x1, x17]
    // 0xb7d988: DecompressPointer r3
    //     0xb7d988: add             x3, x3, HEAP, lsl #32
    // 0xb7d98c: cmp             w3, NULL
    // 0xb7d990: b.eq            #0xb7d998
    // 0xb7d994: tbnz            w3, #4, #0xb7d9f0
    // 0xb7d998: ldur            x3, [fp, #-8]
    // 0xb7d99c: LoadField: r1 = r3->field_f
    //     0xb7d99c: ldur            w1, [x3, #0xf]
    // 0xb7d9a0: DecompressPointer r1
    //     0xb7d9a0: add             x1, x1, HEAP, lsl #32
    // 0xb7d9a4: cmp             w1, NULL
    // 0xb7d9a8: b.eq            #0xb7def4
    // 0xb7d9ac: r0 = of()
    //     0xb7d9ac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7d9b0: LoadField: r1 = r0->field_5b
    //     0xb7d9b0: ldur            w1, [x0, #0x5b]
    // 0xb7d9b4: DecompressPointer r1
    //     0xb7d9b4: add             x1, x1, HEAP, lsl #32
    // 0xb7d9b8: stur            x1, [fp, #-0x60]
    // 0xb7d9bc: r0 = BorderSide()
    //     0xb7d9bc: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xb7d9c0: mov             x1, x0
    // 0xb7d9c4: ldur            x0, [fp, #-0x60]
    // 0xb7d9c8: StoreField: r1->field_7 = r0
    //     0xb7d9c8: stur            w0, [x1, #7]
    // 0xb7d9cc: d0 = 1.000000
    //     0xb7d9cc: fmov            d0, #1.00000000
    // 0xb7d9d0: StoreField: r1->field_b = d0
    //     0xb7d9d0: stur            d0, [x1, #0xb]
    // 0xb7d9d4: r0 = Instance_BorderStyle
    //     0xb7d9d4: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xb7d9d8: ldr             x0, [x0, #0xf68]
    // 0xb7d9dc: StoreField: r1->field_13 = r0
    //     0xb7d9dc: stur            w0, [x1, #0x13]
    // 0xb7d9e0: d0 = -1.000000
    //     0xb7d9e0: fmov            d0, #-1.00000000
    // 0xb7d9e4: ArrayStore: r1[0] = d0  ; List_8
    //     0xb7d9e4: stur            d0, [x1, #0x17]
    // 0xb7d9e8: mov             x4, x1
    // 0xb7d9ec: b               #0xb7d9f8
    // 0xb7d9f0: r4 = Instance_BorderSide
    //     0xb7d9f0: add             x4, PP, #0x55, lsl #12  ; [pp+0x55958] Obj!BorderSide@d62f31
    //     0xb7d9f4: ldr             x4, [x4, #0x958]
    // 0xb7d9f8: ldur            x2, [fp, #-0x20]
    // 0xb7d9fc: ldur            x3, [fp, #-0x48]
    // 0xb7da00: ldur            x1, [fp, #-0x50]
    // 0xb7da04: ldur            x0, [fp, #-0x58]
    // 0xb7da08: stur            x4, [fp, #-0x60]
    // 0xb7da0c: r0 = RoundedRectangleBorder()
    //     0xb7da0c: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb7da10: mov             x1, x0
    // 0xb7da14: r0 = Instance_BorderRadius
    //     0xb7da14: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb7da18: ldr             x0, [x0, #0x460]
    // 0xb7da1c: StoreField: r1->field_b = r0
    //     0xb7da1c: stur            w0, [x1, #0xb]
    // 0xb7da20: ldur            x0, [fp, #-0x60]
    // 0xb7da24: StoreField: r1->field_7 = r0
    //     0xb7da24: stur            w0, [x1, #7]
    // 0xb7da28: r16 = <RoundedRectangleBorder>
    //     0xb7da28: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb7da2c: ldr             x16, [x16, #0xf78]
    // 0xb7da30: stp             x1, x16, [SP]
    // 0xb7da34: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7da34: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7da38: r0 = all()
    //     0xb7da38: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7da3c: stur            x0, [fp, #-0x60]
    // 0xb7da40: r0 = ButtonStyle()
    //     0xb7da40: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb7da44: mov             x1, x0
    // 0xb7da48: ldur            x0, [fp, #-0x58]
    // 0xb7da4c: stur            x1, [fp, #-0x68]
    // 0xb7da50: StoreField: r1->field_b = r0
    //     0xb7da50: stur            w0, [x1, #0xb]
    // 0xb7da54: ldur            x0, [fp, #-0x50]
    // 0xb7da58: StoreField: r1->field_f = r0
    //     0xb7da58: stur            w0, [x1, #0xf]
    // 0xb7da5c: ldur            x0, [fp, #-0x48]
    // 0xb7da60: StoreField: r1->field_27 = r0
    //     0xb7da60: stur            w0, [x1, #0x27]
    // 0xb7da64: ldur            x0, [fp, #-0x60]
    // 0xb7da68: StoreField: r1->field_43 = r0
    //     0xb7da68: stur            w0, [x1, #0x43]
    // 0xb7da6c: r0 = TextButtonThemeData()
    //     0xb7da6c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb7da70: mov             x2, x0
    // 0xb7da74: ldur            x0, [fp, #-0x68]
    // 0xb7da78: stur            x2, [fp, #-0x48]
    // 0xb7da7c: StoreField: r2->field_7 = r0
    //     0xb7da7c: stur            w0, [x2, #7]
    // 0xb7da80: ldur            x0, [fp, #-0x20]
    // 0xb7da84: LoadField: r1 = r0->field_13
    //     0xb7da84: ldur            w1, [x0, #0x13]
    // 0xb7da88: DecompressPointer r1
    //     0xb7da88: add             x1, x1, HEAP, lsl #32
    // 0xb7da8c: cmp             w1, NULL
    // 0xb7da90: b.eq            #0xb7def8
    // 0xb7da94: r17 = 275
    //     0xb7da94: movz            x17, #0x113
    // 0xb7da98: ldr             w3, [x1, x17]
    // 0xb7da9c: DecompressPointer r3
    //     0xb7da9c: add             x3, x3, HEAP, lsl #32
    // 0xb7daa0: cmp             w3, NULL
    // 0xb7daa4: b.eq            #0xb7daac
    // 0xb7daa8: tbnz            w3, #4, #0xb7db64
    // 0xb7daac: LoadField: r3 = r1->field_f
    //     0xb7daac: ldur            w3, [x1, #0xf]
    // 0xb7dab0: DecompressPointer r3
    //     0xb7dab0: add             x3, x3, HEAP, lsl #32
    // 0xb7dab4: cmp             w3, NULL
    // 0xb7dab8: b.ne            #0xb7dac4
    // 0xb7dabc: r1 = ""
    //     0xb7dabc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7dac0: b               #0xb7dac8
    // 0xb7dac4: mov             x1, x3
    // 0xb7dac8: ldur            x3, [fp, #-8]
    // 0xb7dacc: r0 = capitalizeFirstWord()
    //     0xb7dacc: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb7dad0: mov             x2, x0
    // 0xb7dad4: ldur            x0, [fp, #-8]
    // 0xb7dad8: stur            x2, [fp, #-0x50]
    // 0xb7dadc: LoadField: r1 = r0->field_f
    //     0xb7dadc: ldur            w1, [x0, #0xf]
    // 0xb7dae0: DecompressPointer r1
    //     0xb7dae0: add             x1, x1, HEAP, lsl #32
    // 0xb7dae4: cmp             w1, NULL
    // 0xb7dae8: b.eq            #0xb7defc
    // 0xb7daec: r0 = of()
    //     0xb7daec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7daf0: LoadField: r1 = r0->field_87
    //     0xb7daf0: ldur            w1, [x0, #0x87]
    // 0xb7daf4: DecompressPointer r1
    //     0xb7daf4: add             x1, x1, HEAP, lsl #32
    // 0xb7daf8: LoadField: r0 = r1->field_7
    //     0xb7daf8: ldur            w0, [x1, #7]
    // 0xb7dafc: DecompressPointer r0
    //     0xb7dafc: add             x0, x0, HEAP, lsl #32
    // 0xb7db00: ldur            x2, [fp, #-8]
    // 0xb7db04: stur            x0, [fp, #-0x58]
    // 0xb7db08: LoadField: r1 = r2->field_f
    //     0xb7db08: ldur            w1, [x2, #0xf]
    // 0xb7db0c: DecompressPointer r1
    //     0xb7db0c: add             x1, x1, HEAP, lsl #32
    // 0xb7db10: cmp             w1, NULL
    // 0xb7db14: b.eq            #0xb7df00
    // 0xb7db18: r0 = of()
    //     0xb7db18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7db1c: LoadField: r1 = r0->field_5b
    //     0xb7db1c: ldur            w1, [x0, #0x5b]
    // 0xb7db20: DecompressPointer r1
    //     0xb7db20: add             x1, x1, HEAP, lsl #32
    // 0xb7db24: r16 = 15.000000
    //     0xb7db24: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xb7db28: ldr             x16, [x16, #0x480]
    // 0xb7db2c: stp             x1, x16, [SP]
    // 0xb7db30: ldur            x1, [fp, #-0x58]
    // 0xb7db34: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7db34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7db38: ldr             x4, [x4, #0xaa0]
    // 0xb7db3c: r0 = copyWith()
    //     0xb7db3c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7db40: stur            x0, [fp, #-0x58]
    // 0xb7db44: r0 = Text()
    //     0xb7db44: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7db48: mov             x1, x0
    // 0xb7db4c: ldur            x0, [fp, #-0x50]
    // 0xb7db50: StoreField: r1->field_b = r0
    //     0xb7db50: stur            w0, [x1, #0xb]
    // 0xb7db54: ldur            x0, [fp, #-0x58]
    // 0xb7db58: StoreField: r1->field_13 = r0
    //     0xb7db58: stur            w0, [x1, #0x13]
    // 0xb7db5c: mov             x3, x1
    // 0xb7db60: b               #0xb7dc1c
    // 0xb7db64: ldur            x2, [fp, #-8]
    // 0xb7db68: LoadField: r0 = r1->field_f
    //     0xb7db68: ldur            w0, [x1, #0xf]
    // 0xb7db6c: DecompressPointer r0
    //     0xb7db6c: add             x0, x0, HEAP, lsl #32
    // 0xb7db70: cmp             w0, NULL
    // 0xb7db74: b.ne            #0xb7db80
    // 0xb7db78: r1 = ""
    //     0xb7db78: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7db7c: b               #0xb7db84
    // 0xb7db80: mov             x1, x0
    // 0xb7db84: r0 = capitalizeFirstWord()
    //     0xb7db84: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb7db88: mov             x2, x0
    // 0xb7db8c: ldur            x0, [fp, #-8]
    // 0xb7db90: stur            x2, [fp, #-0x50]
    // 0xb7db94: LoadField: r1 = r0->field_f
    //     0xb7db94: ldur            w1, [x0, #0xf]
    // 0xb7db98: DecompressPointer r1
    //     0xb7db98: add             x1, x1, HEAP, lsl #32
    // 0xb7db9c: cmp             w1, NULL
    // 0xb7dba0: b.eq            #0xb7df04
    // 0xb7dba4: r0 = of()
    //     0xb7dba4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7dba8: LoadField: r1 = r0->field_87
    //     0xb7dba8: ldur            w1, [x0, #0x87]
    // 0xb7dbac: DecompressPointer r1
    //     0xb7dbac: add             x1, x1, HEAP, lsl #32
    // 0xb7dbb0: LoadField: r0 = r1->field_7
    //     0xb7dbb0: ldur            w0, [x1, #7]
    // 0xb7dbb4: DecompressPointer r0
    //     0xb7dbb4: add             x0, x0, HEAP, lsl #32
    // 0xb7dbb8: ldur            x1, [fp, #-8]
    // 0xb7dbbc: stur            x0, [fp, #-0x58]
    // 0xb7dbc0: LoadField: r2 = r1->field_f
    //     0xb7dbc0: ldur            w2, [x1, #0xf]
    // 0xb7dbc4: DecompressPointer r2
    //     0xb7dbc4: add             x2, x2, HEAP, lsl #32
    // 0xb7dbc8: cmp             w2, NULL
    // 0xb7dbcc: b.eq            #0xb7df08
    // 0xb7dbd0: mov             x1, x2
    // 0xb7dbd4: r0 = of()
    //     0xb7dbd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7dbd8: LoadField: r1 = r0->field_5b
    //     0xb7dbd8: ldur            w1, [x0, #0x5b]
    // 0xb7dbdc: DecompressPointer r1
    //     0xb7dbdc: add             x1, x1, HEAP, lsl #32
    // 0xb7dbe0: r16 = 15.000000
    //     0xb7dbe0: add             x16, PP, #0x41, lsl #12  ; [pp+0x41480] 15
    //     0xb7dbe4: ldr             x16, [x16, #0x480]
    // 0xb7dbe8: stp             x1, x16, [SP]
    // 0xb7dbec: ldur            x1, [fp, #-0x58]
    // 0xb7dbf0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7dbf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7dbf4: ldr             x4, [x4, #0xaa0]
    // 0xb7dbf8: r0 = copyWith()
    //     0xb7dbf8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7dbfc: stur            x0, [fp, #-8]
    // 0xb7dc00: r0 = Text()
    //     0xb7dc00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7dc04: mov             x1, x0
    // 0xb7dc08: ldur            x0, [fp, #-0x50]
    // 0xb7dc0c: StoreField: r1->field_b = r0
    //     0xb7dc0c: stur            w0, [x1, #0xb]
    // 0xb7dc10: ldur            x0, [fp, #-8]
    // 0xb7dc14: StoreField: r1->field_13 = r0
    //     0xb7dc14: stur            w0, [x1, #0x13]
    // 0xb7dc18: mov             x3, x1
    // 0xb7dc1c: ldur            x0, [fp, #-0x48]
    // 0xb7dc20: ldur            x2, [fp, #-0x20]
    // 0xb7dc24: stur            x3, [fp, #-8]
    // 0xb7dc28: r1 = Function '<anonymous closure>':.
    //     0xb7dc28: add             x1, PP, #0x55, lsl #12  ; [pp+0x55960] AnonymousClosure: (0xb7df0c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::glassThemeSlider (0xb7c8f8)
    //     0xb7dc2c: ldr             x1, [x1, #0x960]
    // 0xb7dc30: r0 = AllocateClosure()
    //     0xb7dc30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7dc34: stur            x0, [fp, #-0x20]
    // 0xb7dc38: r0 = TextButton()
    //     0xb7dc38: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb7dc3c: mov             x1, x0
    // 0xb7dc40: ldur            x0, [fp, #-0x20]
    // 0xb7dc44: stur            x1, [fp, #-0x50]
    // 0xb7dc48: StoreField: r1->field_b = r0
    //     0xb7dc48: stur            w0, [x1, #0xb]
    // 0xb7dc4c: r0 = false
    //     0xb7dc4c: add             x0, NULL, #0x30  ; false
    // 0xb7dc50: StoreField: r1->field_27 = r0
    //     0xb7dc50: stur            w0, [x1, #0x27]
    // 0xb7dc54: r2 = true
    //     0xb7dc54: add             x2, NULL, #0x20  ; true
    // 0xb7dc58: StoreField: r1->field_2f = r2
    //     0xb7dc58: stur            w2, [x1, #0x2f]
    // 0xb7dc5c: ldur            x2, [fp, #-8]
    // 0xb7dc60: StoreField: r1->field_37 = r2
    //     0xb7dc60: stur            w2, [x1, #0x37]
    // 0xb7dc64: r0 = TextButtonTheme()
    //     0xb7dc64: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb7dc68: mov             x1, x0
    // 0xb7dc6c: ldur            x0, [fp, #-0x48]
    // 0xb7dc70: StoreField: r1->field_f = r0
    //     0xb7dc70: stur            w0, [x1, #0xf]
    // 0xb7dc74: ldur            x0, [fp, #-0x50]
    // 0xb7dc78: StoreField: r1->field_b = r0
    //     0xb7dc78: stur            w0, [x1, #0xb]
    // 0xb7dc7c: mov             x5, x1
    // 0xb7dc80: b               #0xb7dc9c
    // 0xb7dc84: r0 = Container()
    //     0xb7dc84: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7dc88: mov             x1, x0
    // 0xb7dc8c: stur            x0, [fp, #-8]
    // 0xb7dc90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb7dc90: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb7dc94: r0 = Container()
    //     0xb7dc94: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7dc98: ldur            x5, [fp, #-8]
    // 0xb7dc9c: ldur            x4, [fp, #-0x28]
    // 0xb7dca0: ldur            x3, [fp, #-0x10]
    // 0xb7dca4: ldur            x2, [fp, #-0x40]
    // 0xb7dca8: ldur            x0, [fp, #-0x38]
    // 0xb7dcac: ldur            x1, [fp, #-0x30]
    // 0xb7dcb0: stur            x5, [fp, #-8]
    // 0xb7dcb4: r0 = Container()
    //     0xb7dcb4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7dcb8: stur            x0, [fp, #-0x20]
    // 0xb7dcbc: r16 = Instance_Alignment
    //     0xb7dcbc: add             x16, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xb7dcc0: ldr             x16, [x16, #0xcb0]
    // 0xb7dcc4: r30 = Instance_EdgeInsets
    //     0xb7dcc4: add             lr, PP, #0x55, lsl #12  ; [pp+0x55968] Obj!EdgeInsets@d58581
    //     0xb7dcc8: ldr             lr, [lr, #0x968]
    // 0xb7dccc: stp             lr, x16, [SP, #8]
    // 0xb7dcd0: ldur            x16, [fp, #-8]
    // 0xb7dcd4: str             x16, [SP]
    // 0xb7dcd8: mov             x1, x0
    // 0xb7dcdc: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, margin, 0x2, null]
    //     0xb7dcdc: add             x4, PP, #0x55, lsl #12  ; [pp+0x55970] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "margin", 0x2, Null]
    //     0xb7dce0: ldr             x4, [x4, #0x970]
    // 0xb7dce4: r0 = Container()
    //     0xb7dce4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7dce8: r0 = Visibility()
    //     0xb7dce8: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7dcec: mov             x2, x0
    // 0xb7dcf0: ldur            x0, [fp, #-0x20]
    // 0xb7dcf4: stur            x2, [fp, #-8]
    // 0xb7dcf8: StoreField: r2->field_b = r0
    //     0xb7dcf8: stur            w0, [x2, #0xb]
    // 0xb7dcfc: r0 = Instance_SizedBox
    //     0xb7dcfc: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7dd00: StoreField: r2->field_f = r0
    //     0xb7dd00: stur            w0, [x2, #0xf]
    // 0xb7dd04: ldur            x0, [fp, #-0x30]
    // 0xb7dd08: StoreField: r2->field_13 = r0
    //     0xb7dd08: stur            w0, [x2, #0x13]
    // 0xb7dd0c: r0 = false
    //     0xb7dd0c: add             x0, NULL, #0x30  ; false
    // 0xb7dd10: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7dd10: stur            w0, [x2, #0x17]
    // 0xb7dd14: StoreField: r2->field_1b = r0
    //     0xb7dd14: stur            w0, [x2, #0x1b]
    // 0xb7dd18: StoreField: r2->field_1f = r0
    //     0xb7dd18: stur            w0, [x2, #0x1f]
    // 0xb7dd1c: StoreField: r2->field_23 = r0
    //     0xb7dd1c: stur            w0, [x2, #0x23]
    // 0xb7dd20: StoreField: r2->field_27 = r0
    //     0xb7dd20: stur            w0, [x2, #0x27]
    // 0xb7dd24: StoreField: r2->field_2b = r0
    //     0xb7dd24: stur            w0, [x2, #0x2b]
    // 0xb7dd28: r1 = <FlexParentData>
    //     0xb7dd28: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb7dd2c: ldr             x1, [x1, #0xe00]
    // 0xb7dd30: r0 = Expanded()
    //     0xb7dd30: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb7dd34: mov             x3, x0
    // 0xb7dd38: r0 = 1
    //     0xb7dd38: movz            x0, #0x1
    // 0xb7dd3c: stur            x3, [fp, #-0x20]
    // 0xb7dd40: StoreField: r3->field_13 = r0
    //     0xb7dd40: stur            x0, [x3, #0x13]
    // 0xb7dd44: r0 = Instance_FlexFit
    //     0xb7dd44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb7dd48: ldr             x0, [x0, #0xe08]
    // 0xb7dd4c: StoreField: r3->field_1b = r0
    //     0xb7dd4c: stur            w0, [x3, #0x1b]
    // 0xb7dd50: ldur            x0, [fp, #-8]
    // 0xb7dd54: StoreField: r3->field_b = r0
    //     0xb7dd54: stur            w0, [x3, #0xb]
    // 0xb7dd58: r1 = Null
    //     0xb7dd58: mov             x1, NULL
    // 0xb7dd5c: r2 = 10
    //     0xb7dd5c: movz            x2, #0xa
    // 0xb7dd60: r0 = AllocateArray()
    //     0xb7dd60: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7dd64: mov             x2, x0
    // 0xb7dd68: ldur            x0, [fp, #-0x28]
    // 0xb7dd6c: stur            x2, [fp, #-8]
    // 0xb7dd70: StoreField: r2->field_f = r0
    //     0xb7dd70: stur            w0, [x2, #0xf]
    // 0xb7dd74: ldur            x0, [fp, #-0x10]
    // 0xb7dd78: StoreField: r2->field_13 = r0
    //     0xb7dd78: stur            w0, [x2, #0x13]
    // 0xb7dd7c: ldur            x0, [fp, #-0x40]
    // 0xb7dd80: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7dd80: stur            w0, [x2, #0x17]
    // 0xb7dd84: ldur            x0, [fp, #-0x38]
    // 0xb7dd88: StoreField: r2->field_1b = r0
    //     0xb7dd88: stur            w0, [x2, #0x1b]
    // 0xb7dd8c: ldur            x0, [fp, #-0x20]
    // 0xb7dd90: StoreField: r2->field_1f = r0
    //     0xb7dd90: stur            w0, [x2, #0x1f]
    // 0xb7dd94: r1 = <Widget>
    //     0xb7dd94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7dd98: r0 = AllocateGrowableArray()
    //     0xb7dd98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7dd9c: mov             x1, x0
    // 0xb7dda0: ldur            x0, [fp, #-8]
    // 0xb7dda4: stur            x1, [fp, #-0x10]
    // 0xb7dda8: StoreField: r1->field_f = r0
    //     0xb7dda8: stur            w0, [x1, #0xf]
    // 0xb7ddac: r0 = 10
    //     0xb7ddac: movz            x0, #0xa
    // 0xb7ddb0: StoreField: r1->field_b = r0
    //     0xb7ddb0: stur            w0, [x1, #0xb]
    // 0xb7ddb4: r0 = Column()
    //     0xb7ddb4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb7ddb8: mov             x1, x0
    // 0xb7ddbc: r0 = Instance_Axis
    //     0xb7ddbc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb7ddc0: stur            x1, [fp, #-8]
    // 0xb7ddc4: StoreField: r1->field_f = r0
    //     0xb7ddc4: stur            w0, [x1, #0xf]
    // 0xb7ddc8: r0 = Instance_MainAxisAlignment
    //     0xb7ddc8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7ddcc: ldr             x0, [x0, #0xa08]
    // 0xb7ddd0: StoreField: r1->field_13 = r0
    //     0xb7ddd0: stur            w0, [x1, #0x13]
    // 0xb7ddd4: r0 = Instance_MainAxisSize
    //     0xb7ddd4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb7ddd8: ldr             x0, [x0, #0xdd0]
    // 0xb7dddc: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7dddc: stur            w0, [x1, #0x17]
    // 0xb7dde0: r0 = Instance_CrossAxisAlignment
    //     0xb7dde0: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb7dde4: ldr             x0, [x0, #0x890]
    // 0xb7dde8: StoreField: r1->field_1b = r0
    //     0xb7dde8: stur            w0, [x1, #0x1b]
    // 0xb7ddec: r0 = Instance_VerticalDirection
    //     0xb7ddec: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7ddf0: ldr             x0, [x0, #0xa20]
    // 0xb7ddf4: StoreField: r1->field_23 = r0
    //     0xb7ddf4: stur            w0, [x1, #0x23]
    // 0xb7ddf8: r0 = Instance_Clip
    //     0xb7ddf8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7ddfc: ldr             x0, [x0, #0x38]
    // 0xb7de00: StoreField: r1->field_2b = r0
    //     0xb7de00: stur            w0, [x1, #0x2b]
    // 0xb7de04: StoreField: r1->field_2f = rZR
    //     0xb7de04: stur            xzr, [x1, #0x2f]
    // 0xb7de08: ldur            x0, [fp, #-0x10]
    // 0xb7de0c: StoreField: r1->field_b = r0
    //     0xb7de0c: stur            w0, [x1, #0xb]
    // 0xb7de10: r0 = AnimatedContainer()
    //     0xb7de10: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb7de14: stur            x0, [fp, #-0x10]
    // 0xb7de18: r16 = Instance_Cubic
    //     0xb7de18: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb7de1c: ldr             x16, [x16, #0xaf8]
    // 0xb7de20: str             x16, [SP]
    // 0xb7de24: mov             x1, x0
    // 0xb7de28: ldur            x2, [fp, #-8]
    // 0xb7de2c: r3 = Instance_Duration
    //     0xb7de2c: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb7de30: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb7de30: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb7de34: ldr             x4, [x4, #0xbc8]
    // 0xb7de38: r0 = AnimatedContainer()
    //     0xb7de38: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb7de3c: ldur            x0, [fp, #-0x10]
    // 0xb7de40: LeaveFrame
    //     0xb7de40: mov             SP, fp
    //     0xb7de44: ldp             fp, lr, [SP], #0x10
    // 0xb7de48: ret
    //     0xb7de48: ret             
    // 0xb7de4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7de4c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7de50: b               #0xb7c91c
    // 0xb7de54: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7de54: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7de58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7de58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7de5c: r9 = _imagePageController
    //     0xb7de5c: add             x9, PP, #0x55, lsl #12  ; [pp+0x55978] Field <_GroupCarouselItemViewState@1616195414._imagePageController@1616195414>: late (offset: 0x18)
    //     0xb7de60: ldr             x9, [x9, #0x978]
    // 0xb7de64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb7de64: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb7de68: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7de68: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7de6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7de6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7de70: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7de70: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7de74: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7de74: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7de78: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7de78: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7de7c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7de7c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7de80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7de80: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7de84: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7de84: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7de88: SaveReg d0
    //     0xb7de88: str             q0, [SP, #-0x10]!
    // 0xb7de8c: SaveReg r0
    //     0xb7de8c: str             x0, [SP, #-8]!
    // 0xb7de90: r0 = 74
    //     0xb7de90: movz            x0, #0x4a
    // 0xb7de94: r30 = DoubleToIntegerStub
    //     0xb7de94: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xb7de98: LoadField: r30 = r30->field_7
    //     0xb7de98: ldur            lr, [lr, #7]
    // 0xb7de9c: blr             lr
    // 0xb7dea0: mov             x1, x0
    // 0xb7dea4: RestoreReg r0
    //     0xb7dea4: ldr             x0, [SP], #8
    // 0xb7dea8: RestoreReg d0
    //     0xb7dea8: ldr             q0, [SP], #0x10
    // 0xb7deac: b               #0xb7cf68
    // 0xb7deb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7deb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7deb4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7deb4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7deb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7deb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7debc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7debc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7dec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7dec0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7dec4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7dec4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7dec8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7dec8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7decc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7decc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7ded0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7ded0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7ded4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7ded4: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7ded8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7ded8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7dedc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7dedc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7dee0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7dee0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7dee4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7dee4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7dee8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7dee8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7deec: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7deec: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7def0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7def0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7def4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7def4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7def8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7def8: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7defc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7defc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7df00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7df00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7df04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7df04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7df08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7df08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb7df0c, size: 0x110
    // 0xb7df0c: EnterFrame
    //     0xb7df0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb7df10: mov             fp, SP
    // 0xb7df14: AllocStack(0x20)
    //     0xb7df14: sub             SP, SP, #0x20
    // 0xb7df18: SetupParameters()
    //     0xb7df18: ldr             x0, [fp, #0x10]
    //     0xb7df1c: ldur            w1, [x0, #0x17]
    //     0xb7df20: add             x1, x1, HEAP, lsl #32
    //     0xb7df24: stur            x1, [fp, #-8]
    // 0xb7df28: CheckStackOverflow
    //     0xb7df28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7df2c: cmp             SP, x16
    //     0xb7df30: b.ls            #0xb7e008
    // 0xb7df34: LoadField: r0 = r1->field_13
    //     0xb7df34: ldur            w0, [x1, #0x13]
    // 0xb7df38: DecompressPointer r0
    //     0xb7df38: add             x0, x0, HEAP, lsl #32
    // 0xb7df3c: cmp             w0, NULL
    // 0xb7df40: b.eq            #0xb7e010
    // 0xb7df44: r17 = 275
    //     0xb7df44: movz            x17, #0x113
    // 0xb7df48: ldr             w2, [x0, x17]
    // 0xb7df4c: DecompressPointer r2
    //     0xb7df4c: add             x2, x2, HEAP, lsl #32
    // 0xb7df50: cmp             w2, NULL
    // 0xb7df54: b.eq            #0xb7df5c
    // 0xb7df58: tbz             w2, #4, #0xb7dff8
    // 0xb7df5c: LoadField: r0 = r1->field_f
    //     0xb7df5c: ldur            w0, [x1, #0xf]
    // 0xb7df60: DecompressPointer r0
    //     0xb7df60: add             x0, x0, HEAP, lsl #32
    // 0xb7df64: LoadField: r2 = r0->field_b
    //     0xb7df64: ldur            w2, [x0, #0xb]
    // 0xb7df68: DecompressPointer r2
    //     0xb7df68: add             x2, x2, HEAP, lsl #32
    // 0xb7df6c: cmp             w2, NULL
    // 0xb7df70: b.eq            #0xb7e014
    // 0xb7df74: LoadField: r0 = r2->field_37
    //     0xb7df74: ldur            w0, [x2, #0x37]
    // 0xb7df78: DecompressPointer r0
    //     0xb7df78: add             x0, x0, HEAP, lsl #32
    // 0xb7df7c: r16 = "add_to_bag"
    //     0xb7df7c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0xb7df80: ldr             x16, [x16, #0xa38]
    // 0xb7df84: stp             x16, x0, [SP]
    // 0xb7df88: r4 = 0
    //     0xb7df88: movz            x4, #0
    // 0xb7df8c: ldr             x0, [SP, #8]
    // 0xb7df90: r16 = UnlinkedCall_0x613b5c
    //     0xb7df90: add             x16, PP, #0x55, lsl #12  ; [pp+0x55980] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb7df94: add             x16, x16, #0x980
    // 0xb7df98: ldp             x5, lr, [x16]
    // 0xb7df9c: blr             lr
    // 0xb7dfa0: ldur            x0, [fp, #-8]
    // 0xb7dfa4: LoadField: r1 = r0->field_f
    //     0xb7dfa4: ldur            w1, [x0, #0xf]
    // 0xb7dfa8: DecompressPointer r1
    //     0xb7dfa8: add             x1, x1, HEAP, lsl #32
    // 0xb7dfac: LoadField: r2 = r1->field_b
    //     0xb7dfac: ldur            w2, [x1, #0xb]
    // 0xb7dfb0: DecompressPointer r2
    //     0xb7dfb0: add             x2, x2, HEAP, lsl #32
    // 0xb7dfb4: stur            x2, [fp, #-0x10]
    // 0xb7dfb8: cmp             w2, NULL
    // 0xb7dfbc: b.eq            #0xb7e018
    // 0xb7dfc0: LoadField: r1 = r0->field_13
    //     0xb7dfc0: ldur            w1, [x0, #0x13]
    // 0xb7dfc4: DecompressPointer r1
    //     0xb7dfc4: add             x1, x1, HEAP, lsl #32
    // 0xb7dfc8: cmp             w1, NULL
    // 0xb7dfcc: b.ne            #0xb7dfd8
    // 0xb7dfd0: r0 = WidgetEntity()
    //     0xb7dfd0: bl              #0xa749ec  ; AllocateWidgetEntityStub -> WidgetEntity (size=0x140)
    // 0xb7dfd4: mov             x1, x0
    // 0xb7dfd8: ldur            x0, [fp, #-0x10]
    // 0xb7dfdc: LoadField: r2 = r0->field_43
    //     0xb7dfdc: ldur            w2, [x0, #0x43]
    // 0xb7dfe0: DecompressPointer r2
    //     0xb7dfe0: add             x2, x2, HEAP, lsl #32
    // 0xb7dfe4: stp             x1, x2, [SP]
    // 0xb7dfe8: mov             x0, x2
    // 0xb7dfec: ClosureCall
    //     0xb7dfec: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xb7dff0: ldur            x2, [x0, #0x1f]
    //     0xb7dff4: blr             x2
    // 0xb7dff8: r0 = Null
    //     0xb7dff8: mov             x0, NULL
    // 0xb7dffc: LeaveFrame
    //     0xb7dffc: mov             SP, fp
    //     0xb7e000: ldp             fp, lr, [SP], #0x10
    // 0xb7e004: ret
    //     0xb7e004: ret             
    // 0xb7e008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7e008: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7e00c: b               #0xb7df34
    // 0xb7e010: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7e010: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7e014: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7e014: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7e018: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7e018: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] AnimatedContainer <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb7e01c, size: 0xc8
    // 0xb7e01c: EnterFrame
    //     0xb7e01c: stp             fp, lr, [SP, #-0x10]!
    //     0xb7e020: mov             fp, SP
    // 0xb7e024: AllocStack(0x20)
    //     0xb7e024: sub             SP, SP, #0x20
    // 0xb7e028: SetupParameters()
    //     0xb7e028: ldr             x0, [fp, #0x20]
    //     0xb7e02c: ldur            w1, [x0, #0x17]
    //     0xb7e030: add             x1, x1, HEAP, lsl #32
    // 0xb7e034: CheckStackOverflow
    //     0xb7e034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7e038: cmp             SP, x16
    //     0xb7e03c: b.ls            #0xb7e0d4
    // 0xb7e040: LoadField: r2 = r1->field_f
    //     0xb7e040: ldur            w2, [x1, #0xf]
    // 0xb7e044: DecompressPointer r2
    //     0xb7e044: add             x2, x2, HEAP, lsl #32
    // 0xb7e048: stur            x2, [fp, #-0x10]
    // 0xb7e04c: LoadField: r0 = r1->field_13
    //     0xb7e04c: ldur            w0, [x1, #0x13]
    // 0xb7e050: DecompressPointer r0
    //     0xb7e050: add             x0, x0, HEAP, lsl #32
    // 0xb7e054: cmp             w0, NULL
    // 0xb7e058: b.eq            #0xb7e0dc
    // 0xb7e05c: LoadField: r3 = r0->field_e7
    //     0xb7e05c: ldur            w3, [x0, #0xe7]
    // 0xb7e060: DecompressPointer r3
    //     0xb7e060: add             x3, x3, HEAP, lsl #32
    // 0xb7e064: stur            x3, [fp, #-8]
    // 0xb7e068: LoadField: r0 = r2->field_b
    //     0xb7e068: ldur            w0, [x2, #0xb]
    // 0xb7e06c: DecompressPointer r0
    //     0xb7e06c: add             x0, x0, HEAP, lsl #32
    // 0xb7e070: cmp             w0, NULL
    // 0xb7e074: b.eq            #0xb7e0e0
    // 0xb7e078: LoadField: r4 = r0->field_b
    //     0xb7e078: ldur            w4, [x0, #0xb]
    // 0xb7e07c: DecompressPointer r4
    //     0xb7e07c: add             x4, x4, HEAP, lsl #32
    // 0xb7e080: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb7e080: ldur            w0, [x1, #0x17]
    // 0xb7e084: DecompressPointer r0
    //     0xb7e084: add             x0, x0, HEAP, lsl #32
    // 0xb7e088: r1 = LoadClassIdInstr(r4)
    //     0xb7e088: ldur            x1, [x4, #-1]
    //     0xb7e08c: ubfx            x1, x1, #0xc, #0x14
    // 0xb7e090: stp             x0, x4, [SP]
    // 0xb7e094: mov             x0, x1
    // 0xb7e098: r0 = GDT[cid_x0 + -0xb7]()
    //     0xb7e098: sub             lr, x0, #0xb7
    //     0xb7e09c: ldr             lr, [x21, lr, lsl #3]
    //     0xb7e0a0: blr             lr
    // 0xb7e0a4: mov             x1, x0
    // 0xb7e0a8: ldr             x0, [fp, #0x10]
    // 0xb7e0ac: r5 = LoadInt32Instr(r0)
    //     0xb7e0ac: sbfx            x5, x0, #1, #0x1f
    //     0xb7e0b0: tbz             w0, #0, #0xb7e0b8
    //     0xb7e0b4: ldur            x5, [x0, #7]
    // 0xb7e0b8: mov             x3, x1
    // 0xb7e0bc: ldur            x1, [fp, #-0x10]
    // 0xb7e0c0: ldur            x2, [fp, #-8]
    // 0xb7e0c4: r0 = imageSlider()
    //     0xb7e0c4: bl              #0xb7e0e4  ; [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::imageSlider
    // 0xb7e0c8: LeaveFrame
    //     0xb7e0c8: mov             SP, fp
    //     0xb7e0cc: ldp             fp, lr, [SP], #0x10
    // 0xb7e0d0: ret
    //     0xb7e0d0: ret             
    // 0xb7e0d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7e0d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7e0d8: b               #0xb7e040
    // 0xb7e0dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7e0dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7e0e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7e0e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ imageSlider(/* No info */) {
    // ** addr: 0xb7e0e4, size: 0x112c
    // 0xb7e0e4: EnterFrame
    //     0xb7e0e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb7e0e8: mov             fp, SP
    // 0xb7e0ec: AllocStack(0x90)
    //     0xb7e0ec: sub             SP, SP, #0x90
    // 0xb7e0f0: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xb7e0f0: mov             x0, x1
    //     0xb7e0f4: stur            x1, [fp, #-8]
    //     0xb7e0f8: mov             x1, x5
    //     0xb7e0fc: stur            x2, [fp, #-0x10]
    //     0xb7e100: stur            x3, [fp, #-0x18]
    //     0xb7e104: stur            x5, [fp, #-0x20]
    // 0xb7e108: CheckStackOverflow
    //     0xb7e108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7e10c: cmp             SP, x16
    //     0xb7e110: b.ls            #0xb7f1c4
    // 0xb7e114: r1 = 2
    //     0xb7e114: movz            x1, #0x2
    // 0xb7e118: r0 = AllocateContext()
    //     0xb7e118: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7e11c: mov             x1, x0
    // 0xb7e120: ldur            x0, [fp, #-8]
    // 0xb7e124: stur            x1, [fp, #-0x28]
    // 0xb7e128: StoreField: r1->field_f = r0
    //     0xb7e128: stur            w0, [x1, #0xf]
    // 0xb7e12c: ldur            x2, [fp, #-0x18]
    // 0xb7e130: StoreField: r1->field_13 = r2
    //     0xb7e130: stur            w2, [x1, #0x13]
    // 0xb7e134: r0 = Radius()
    //     0xb7e134: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7e138: d0 = 10.000000
    //     0xb7e138: fmov            d0, #10.00000000
    // 0xb7e13c: stur            x0, [fp, #-0x18]
    // 0xb7e140: StoreField: r0->field_7 = d0
    //     0xb7e140: stur            d0, [x0, #7]
    // 0xb7e144: StoreField: r0->field_f = d0
    //     0xb7e144: stur            d0, [x0, #0xf]
    // 0xb7e148: r0 = BorderRadius()
    //     0xb7e148: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb7e14c: mov             x1, x0
    // 0xb7e150: ldur            x0, [fp, #-0x18]
    // 0xb7e154: stur            x1, [fp, #-0x30]
    // 0xb7e158: StoreField: r1->field_7 = r0
    //     0xb7e158: stur            w0, [x1, #7]
    // 0xb7e15c: StoreField: r1->field_b = r0
    //     0xb7e15c: stur            w0, [x1, #0xb]
    // 0xb7e160: StoreField: r1->field_f = r0
    //     0xb7e160: stur            w0, [x1, #0xf]
    // 0xb7e164: StoreField: r1->field_13 = r0
    //     0xb7e164: stur            w0, [x1, #0x13]
    // 0xb7e168: r0 = RoundedRectangleBorder()
    //     0xb7e168: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb7e16c: mov             x3, x0
    // 0xb7e170: ldur            x0, [fp, #-0x30]
    // 0xb7e174: stur            x3, [fp, #-0x18]
    // 0xb7e178: StoreField: r3->field_b = r0
    //     0xb7e178: stur            w0, [x3, #0xb]
    // 0xb7e17c: r4 = Instance_BorderSide
    //     0xb7e17c: add             x4, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb7e180: ldr             x4, [x4, #0xe20]
    // 0xb7e184: StoreField: r3->field_7 = r4
    //     0xb7e184: stur            w4, [x3, #7]
    // 0xb7e188: ldur            x2, [fp, #-0x10]
    // 0xb7e18c: cmp             w2, NULL
    // 0xb7e190: b.eq            #0xb7f1cc
    // 0xb7e194: LoadField: r0 = r2->field_b
    //     0xb7e194: ldur            w0, [x2, #0xb]
    // 0xb7e198: r1 = LoadInt32Instr(r0)
    //     0xb7e198: sbfx            x1, x0, #1, #0x1f
    // 0xb7e19c: mov             x0, x1
    // 0xb7e1a0: ldur            x1, [fp, #-0x20]
    // 0xb7e1a4: cmp             x1, x0
    // 0xb7e1a8: b.hs            #0xb7f1d0
    // 0xb7e1ac: LoadField: r0 = r2->field_f
    //     0xb7e1ac: ldur            w0, [x2, #0xf]
    // 0xb7e1b0: DecompressPointer r0
    //     0xb7e1b0: add             x0, x0, HEAP, lsl #32
    // 0xb7e1b4: ldur            x1, [fp, #-0x20]
    // 0xb7e1b8: ArrayLoad: r2 = r0[r1]  ; Unknown_4
    //     0xb7e1b8: add             x16, x0, x1, lsl #2
    //     0xb7e1bc: ldur            w2, [x16, #0xf]
    // 0xb7e1c0: DecompressPointer r2
    //     0xb7e1c0: add             x2, x2, HEAP, lsl #32
    // 0xb7e1c4: LoadField: r0 = r2->field_b
    //     0xb7e1c4: ldur            w0, [x2, #0xb]
    // 0xb7e1c8: DecompressPointer r0
    //     0xb7e1c8: add             x0, x0, HEAP, lsl #32
    // 0xb7e1cc: stur            x0, [fp, #-0x10]
    // 0xb7e1d0: cmp             w0, NULL
    // 0xb7e1d4: b.eq            #0xb7f1d4
    // 0xb7e1d8: r1 = Function '<anonymous closure>':.
    //     0xb7e1d8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55990] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb7e1dc: ldr             x1, [x1, #0x990]
    // 0xb7e1e0: r2 = Null
    //     0xb7e1e0: mov             x2, NULL
    // 0xb7e1e4: r0 = AllocateClosure()
    //     0xb7e1e4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7e1e8: r1 = Function '<anonymous closure>':.
    //     0xb7e1e8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55998] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb7e1ec: ldr             x1, [x1, #0x998]
    // 0xb7e1f0: r2 = Null
    //     0xb7e1f0: mov             x2, NULL
    // 0xb7e1f4: stur            x0, [fp, #-0x30]
    // 0xb7e1f8: r0 = AllocateClosure()
    //     0xb7e1f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7e1fc: stur            x0, [fp, #-0x38]
    // 0xb7e200: r0 = CachedNetworkImage()
    //     0xb7e200: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb7e204: stur            x0, [fp, #-0x40]
    // 0xb7e208: r16 = Instance_BoxFit
    //     0xb7e208: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb7e20c: ldr             x16, [x16, #0xb18]
    // 0xb7e210: ldur            lr, [fp, #-0x30]
    // 0xb7e214: stp             lr, x16, [SP, #8]
    // 0xb7e218: ldur            x16, [fp, #-0x38]
    // 0xb7e21c: str             x16, [SP]
    // 0xb7e220: mov             x1, x0
    // 0xb7e224: ldur            x2, [fp, #-0x10]
    // 0xb7e228: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb7e228: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb7e22c: ldr             x4, [x4, #0x638]
    // 0xb7e230: r0 = CachedNetworkImage()
    //     0xb7e230: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb7e234: r0 = Card()
    //     0xb7e234: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb7e238: mov             x1, x0
    // 0xb7e23c: r0 = Instance_Color
    //     0xb7e23c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb7e240: ldr             x0, [x0, #0xf88]
    // 0xb7e244: stur            x1, [fp, #-0x10]
    // 0xb7e248: StoreField: r1->field_b = r0
    //     0xb7e248: stur            w0, [x1, #0xb]
    // 0xb7e24c: r0 = 0.000000
    //     0xb7e24c: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb7e250: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7e250: stur            w0, [x1, #0x17]
    // 0xb7e254: ldur            x0, [fp, #-0x18]
    // 0xb7e258: StoreField: r1->field_1b = r0
    //     0xb7e258: stur            w0, [x1, #0x1b]
    // 0xb7e25c: r0 = true
    //     0xb7e25c: add             x0, NULL, #0x20  ; true
    // 0xb7e260: StoreField: r1->field_1f = r0
    //     0xb7e260: stur            w0, [x1, #0x1f]
    // 0xb7e264: r2 = Instance_EdgeInsets
    //     0xb7e264: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb7e268: StoreField: r1->field_27 = r2
    //     0xb7e268: stur            w2, [x1, #0x27]
    // 0xb7e26c: r2 = Instance_Clip
    //     0xb7e26c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb7e270: ldr             x2, [x2, #0xb50]
    // 0xb7e274: StoreField: r1->field_23 = r2
    //     0xb7e274: stur            w2, [x1, #0x23]
    // 0xb7e278: ldur            x2, [fp, #-0x40]
    // 0xb7e27c: StoreField: r1->field_2f = r2
    //     0xb7e27c: stur            w2, [x1, #0x2f]
    // 0xb7e280: StoreField: r1->field_2b = r0
    //     0xb7e280: stur            w0, [x1, #0x2b]
    // 0xb7e284: r2 = Instance__CardVariant
    //     0xb7e284: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb7e288: ldr             x2, [x2, #0xa68]
    // 0xb7e28c: StoreField: r1->field_33 = r2
    //     0xb7e28c: stur            w2, [x1, #0x33]
    // 0xb7e290: r0 = Center()
    //     0xb7e290: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb7e294: mov             x1, x0
    // 0xb7e298: r0 = Instance_Alignment
    //     0xb7e298: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb7e29c: ldr             x0, [x0, #0xb10]
    // 0xb7e2a0: stur            x1, [fp, #-0x30]
    // 0xb7e2a4: StoreField: r1->field_f = r0
    //     0xb7e2a4: stur            w0, [x1, #0xf]
    // 0xb7e2a8: ldur            x0, [fp, #-0x10]
    // 0xb7e2ac: StoreField: r1->field_b = r0
    //     0xb7e2ac: stur            w0, [x1, #0xb]
    // 0xb7e2b0: ldur            x2, [fp, #-0x28]
    // 0xb7e2b4: LoadField: r0 = r2->field_13
    //     0xb7e2b4: ldur            w0, [x2, #0x13]
    // 0xb7e2b8: DecompressPointer r0
    //     0xb7e2b8: add             x0, x0, HEAP, lsl #32
    // 0xb7e2bc: stur            x0, [fp, #-0x18]
    // 0xb7e2c0: cmp             w0, NULL
    // 0xb7e2c4: b.ne            #0xb7e2d0
    // 0xb7e2c8: r3 = Null
    //     0xb7e2c8: mov             x3, NULL
    // 0xb7e2cc: b               #0xb7e300
    // 0xb7e2d0: r17 = 295
    //     0xb7e2d0: movz            x17, #0x127
    // 0xb7e2d4: ldr             w3, [x0, x17]
    // 0xb7e2d8: DecompressPointer r3
    //     0xb7e2d8: add             x3, x3, HEAP, lsl #32
    // 0xb7e2dc: cmp             w3, NULL
    // 0xb7e2e0: b.ne            #0xb7e2ec
    // 0xb7e2e4: r3 = Null
    //     0xb7e2e4: mov             x3, NULL
    // 0xb7e2e8: b               #0xb7e300
    // 0xb7e2ec: LoadField: r4 = r3->field_7
    //     0xb7e2ec: ldur            w4, [x3, #7]
    // 0xb7e2f0: cbnz            w4, #0xb7e2fc
    // 0xb7e2f4: r3 = false
    //     0xb7e2f4: add             x3, NULL, #0x30  ; false
    // 0xb7e2f8: b               #0xb7e300
    // 0xb7e2fc: r3 = true
    //     0xb7e2fc: add             x3, NULL, #0x20  ; true
    // 0xb7e300: cmp             w3, NULL
    // 0xb7e304: b.ne            #0xb7e30c
    // 0xb7e308: r3 = false
    //     0xb7e308: add             x3, NULL, #0x30  ; false
    // 0xb7e30c: stur            x3, [fp, #-0x10]
    // 0xb7e310: cmp             w0, NULL
    // 0xb7e314: b.ne            #0xb7e320
    // 0xb7e318: r4 = Null
    //     0xb7e318: mov             x4, NULL
    // 0xb7e31c: b               #0xb7e32c
    // 0xb7e320: r17 = 271
    //     0xb7e320: movz            x17, #0x10f
    // 0xb7e324: ldr             w4, [x0, x17]
    // 0xb7e328: DecompressPointer r4
    //     0xb7e328: add             x4, x4, HEAP, lsl #32
    // 0xb7e32c: cmp             w4, NULL
    // 0xb7e330: b.ne            #0xb7e348
    // 0xb7e334: mov             x4, x2
    // 0xb7e338: r0 = Instance_Alignment
    //     0xb7e338: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb7e33c: ldr             x0, [x0, #0xfa0]
    // 0xb7e340: r2 = 4
    //     0xb7e340: movz            x2, #0x4
    // 0xb7e344: b               #0xb7e818
    // 0xb7e348: tbnz            w4, #4, #0xb7e808
    // 0xb7e34c: ldur            x4, [fp, #-8]
    // 0xb7e350: r0 = Radius()
    //     0xb7e350: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7e354: d0 = 8.000000
    //     0xb7e354: fmov            d0, #8.00000000
    // 0xb7e358: stur            x0, [fp, #-0x38]
    // 0xb7e35c: StoreField: r0->field_7 = d0
    //     0xb7e35c: stur            d0, [x0, #7]
    // 0xb7e360: StoreField: r0->field_f = d0
    //     0xb7e360: stur            d0, [x0, #0xf]
    // 0xb7e364: r0 = BorderRadius()
    //     0xb7e364: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb7e368: mov             x1, x0
    // 0xb7e36c: ldur            x0, [fp, #-0x38]
    // 0xb7e370: stur            x1, [fp, #-0x40]
    // 0xb7e374: StoreField: r1->field_7 = r0
    //     0xb7e374: stur            w0, [x1, #7]
    // 0xb7e378: StoreField: r1->field_b = r0
    //     0xb7e378: stur            w0, [x1, #0xb]
    // 0xb7e37c: StoreField: r1->field_f = r0
    //     0xb7e37c: stur            w0, [x1, #0xf]
    // 0xb7e380: StoreField: r1->field_13 = r0
    //     0xb7e380: stur            w0, [x1, #0x13]
    // 0xb7e384: ldur            x0, [fp, #-8]
    // 0xb7e388: LoadField: r2 = r0->field_b
    //     0xb7e388: ldur            w2, [x0, #0xb]
    // 0xb7e38c: DecompressPointer r2
    //     0xb7e38c: add             x2, x2, HEAP, lsl #32
    // 0xb7e390: cmp             w2, NULL
    // 0xb7e394: b.eq            #0xb7f1d8
    // 0xb7e398: LoadField: r3 = r2->field_2f
    //     0xb7e398: ldur            w3, [x2, #0x2f]
    // 0xb7e39c: DecompressPointer r3
    //     0xb7e39c: add             x3, x3, HEAP, lsl #32
    // 0xb7e3a0: LoadField: r2 = r3->field_13
    //     0xb7e3a0: ldur            w2, [x3, #0x13]
    // 0xb7e3a4: DecompressPointer r2
    //     0xb7e3a4: add             x2, x2, HEAP, lsl #32
    // 0xb7e3a8: stur            x2, [fp, #-0x38]
    // 0xb7e3ac: cmp             w2, NULL
    // 0xb7e3b0: b.ne            #0xb7e3bc
    // 0xb7e3b4: r3 = Null
    //     0xb7e3b4: mov             x3, NULL
    // 0xb7e3b8: b               #0xb7e3c4
    // 0xb7e3bc: LoadField: r3 = r2->field_7
    //     0xb7e3bc: ldur            w3, [x2, #7]
    // 0xb7e3c0: DecompressPointer r3
    //     0xb7e3c0: add             x3, x3, HEAP, lsl #32
    // 0xb7e3c4: cmp             w3, NULL
    // 0xb7e3c8: b.ne            #0xb7e3d4
    // 0xb7e3cc: r3 = 0
    //     0xb7e3cc: movz            x3, #0
    // 0xb7e3d0: b               #0xb7e3e4
    // 0xb7e3d4: r4 = LoadInt32Instr(r3)
    //     0xb7e3d4: sbfx            x4, x3, #1, #0x1f
    //     0xb7e3d8: tbz             w3, #0, #0xb7e3e0
    //     0xb7e3dc: ldur            x4, [x3, #7]
    // 0xb7e3e0: mov             x3, x4
    // 0xb7e3e4: stur            x3, [fp, #-0x50]
    // 0xb7e3e8: cmp             w2, NULL
    // 0xb7e3ec: b.ne            #0xb7e3f8
    // 0xb7e3f0: r4 = Null
    //     0xb7e3f0: mov             x4, NULL
    // 0xb7e3f4: b               #0xb7e400
    // 0xb7e3f8: LoadField: r4 = r2->field_b
    //     0xb7e3f8: ldur            w4, [x2, #0xb]
    // 0xb7e3fc: DecompressPointer r4
    //     0xb7e3fc: add             x4, x4, HEAP, lsl #32
    // 0xb7e400: cmp             w4, NULL
    // 0xb7e404: b.ne            #0xb7e410
    // 0xb7e408: r4 = 0
    //     0xb7e408: movz            x4, #0
    // 0xb7e40c: b               #0xb7e420
    // 0xb7e410: r5 = LoadInt32Instr(r4)
    //     0xb7e410: sbfx            x5, x4, #1, #0x1f
    //     0xb7e414: tbz             w4, #0, #0xb7e41c
    //     0xb7e418: ldur            x5, [x4, #7]
    // 0xb7e41c: mov             x4, x5
    // 0xb7e420: stur            x4, [fp, #-0x48]
    // 0xb7e424: cmp             w2, NULL
    // 0xb7e428: b.ne            #0xb7e434
    // 0xb7e42c: r5 = Null
    //     0xb7e42c: mov             x5, NULL
    // 0xb7e430: b               #0xb7e43c
    // 0xb7e434: LoadField: r5 = r2->field_f
    //     0xb7e434: ldur            w5, [x2, #0xf]
    // 0xb7e438: DecompressPointer r5
    //     0xb7e438: add             x5, x5, HEAP, lsl #32
    // 0xb7e43c: cmp             w5, NULL
    // 0xb7e440: b.ne            #0xb7e44c
    // 0xb7e444: r5 = 0
    //     0xb7e444: movz            x5, #0
    // 0xb7e448: b               #0xb7e45c
    // 0xb7e44c: r6 = LoadInt32Instr(r5)
    //     0xb7e44c: sbfx            x6, x5, #1, #0x1f
    //     0xb7e450: tbz             w5, #0, #0xb7e458
    //     0xb7e454: ldur            x6, [x5, #7]
    // 0xb7e458: mov             x5, x6
    // 0xb7e45c: stur            x5, [fp, #-0x20]
    // 0xb7e460: r0 = Color()
    //     0xb7e460: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb7e464: mov             x1, x0
    // 0xb7e468: r0 = Instance_ColorSpace
    //     0xb7e468: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb7e46c: stur            x1, [fp, #-0x58]
    // 0xb7e470: StoreField: r1->field_27 = r0
    //     0xb7e470: stur            w0, [x1, #0x27]
    // 0xb7e474: d0 = 1.000000
    //     0xb7e474: fmov            d0, #1.00000000
    // 0xb7e478: StoreField: r1->field_7 = d0
    //     0xb7e478: stur            d0, [x1, #7]
    // 0xb7e47c: ldur            x2, [fp, #-0x50]
    // 0xb7e480: ubfx            x2, x2, #0, #0x20
    // 0xb7e484: and             w3, w2, #0xff
    // 0xb7e488: ubfx            x3, x3, #0, #0x20
    // 0xb7e48c: scvtf           d0, x3
    // 0xb7e490: d1 = 255.000000
    //     0xb7e490: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb7e494: fdiv            d2, d0, d1
    // 0xb7e498: StoreField: r1->field_f = d2
    //     0xb7e498: stur            d2, [x1, #0xf]
    // 0xb7e49c: ldur            x2, [fp, #-0x48]
    // 0xb7e4a0: ubfx            x2, x2, #0, #0x20
    // 0xb7e4a4: and             w3, w2, #0xff
    // 0xb7e4a8: ubfx            x3, x3, #0, #0x20
    // 0xb7e4ac: scvtf           d0, x3
    // 0xb7e4b0: fdiv            d2, d0, d1
    // 0xb7e4b4: ArrayStore: r1[0] = d2  ; List_8
    //     0xb7e4b4: stur            d2, [x1, #0x17]
    // 0xb7e4b8: ldur            x2, [fp, #-0x20]
    // 0xb7e4bc: ubfx            x2, x2, #0, #0x20
    // 0xb7e4c0: and             w3, w2, #0xff
    // 0xb7e4c4: ubfx            x3, x3, #0, #0x20
    // 0xb7e4c8: scvtf           d0, x3
    // 0xb7e4cc: fdiv            d2, d0, d1
    // 0xb7e4d0: StoreField: r1->field_1f = d2
    //     0xb7e4d0: stur            d2, [x1, #0x1f]
    // 0xb7e4d4: ldur            x2, [fp, #-0x38]
    // 0xb7e4d8: cmp             w2, NULL
    // 0xb7e4dc: b.ne            #0xb7e4e8
    // 0xb7e4e0: r3 = Null
    //     0xb7e4e0: mov             x3, NULL
    // 0xb7e4e4: b               #0xb7e4f0
    // 0xb7e4e8: LoadField: r3 = r2->field_7
    //     0xb7e4e8: ldur            w3, [x2, #7]
    // 0xb7e4ec: DecompressPointer r3
    //     0xb7e4ec: add             x3, x3, HEAP, lsl #32
    // 0xb7e4f0: cmp             w3, NULL
    // 0xb7e4f4: b.ne            #0xb7e500
    // 0xb7e4f8: r3 = 0
    //     0xb7e4f8: movz            x3, #0
    // 0xb7e4fc: b               #0xb7e510
    // 0xb7e500: r4 = LoadInt32Instr(r3)
    //     0xb7e500: sbfx            x4, x3, #1, #0x1f
    //     0xb7e504: tbz             w3, #0, #0xb7e50c
    //     0xb7e508: ldur            x4, [x3, #7]
    // 0xb7e50c: mov             x3, x4
    // 0xb7e510: stur            x3, [fp, #-0x50]
    // 0xb7e514: cmp             w2, NULL
    // 0xb7e518: b.ne            #0xb7e524
    // 0xb7e51c: r4 = Null
    //     0xb7e51c: mov             x4, NULL
    // 0xb7e520: b               #0xb7e52c
    // 0xb7e524: LoadField: r4 = r2->field_b
    //     0xb7e524: ldur            w4, [x2, #0xb]
    // 0xb7e528: DecompressPointer r4
    //     0xb7e528: add             x4, x4, HEAP, lsl #32
    // 0xb7e52c: cmp             w4, NULL
    // 0xb7e530: b.ne            #0xb7e53c
    // 0xb7e534: r4 = 0
    //     0xb7e534: movz            x4, #0
    // 0xb7e538: b               #0xb7e54c
    // 0xb7e53c: r5 = LoadInt32Instr(r4)
    //     0xb7e53c: sbfx            x5, x4, #1, #0x1f
    //     0xb7e540: tbz             w4, #0, #0xb7e548
    //     0xb7e544: ldur            x5, [x4, #7]
    // 0xb7e548: mov             x4, x5
    // 0xb7e54c: stur            x4, [fp, #-0x48]
    // 0xb7e550: cmp             w2, NULL
    // 0xb7e554: b.ne            #0xb7e560
    // 0xb7e558: r2 = Null
    //     0xb7e558: mov             x2, NULL
    // 0xb7e55c: b               #0xb7e56c
    // 0xb7e560: LoadField: r5 = r2->field_f
    //     0xb7e560: ldur            w5, [x2, #0xf]
    // 0xb7e564: DecompressPointer r5
    //     0xb7e564: add             x5, x5, HEAP, lsl #32
    // 0xb7e568: mov             x2, x5
    // 0xb7e56c: cmp             w2, NULL
    // 0xb7e570: b.ne            #0xb7e57c
    // 0xb7e574: r6 = 0
    //     0xb7e574: movz            x6, #0
    // 0xb7e578: b               #0xb7e58c
    // 0xb7e57c: r5 = LoadInt32Instr(r2)
    //     0xb7e57c: sbfx            x5, x2, #1, #0x1f
    //     0xb7e580: tbz             w2, #0, #0xb7e588
    //     0xb7e584: ldur            x5, [x2, #7]
    // 0xb7e588: mov             x6, x5
    // 0xb7e58c: ldur            x5, [fp, #-0x18]
    // 0xb7e590: ldur            x2, [fp, #-0x40]
    // 0xb7e594: stur            x6, [fp, #-0x20]
    // 0xb7e598: r0 = Color()
    //     0xb7e598: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb7e59c: mov             x3, x0
    // 0xb7e5a0: r0 = Instance_ColorSpace
    //     0xb7e5a0: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb7e5a4: stur            x3, [fp, #-0x38]
    // 0xb7e5a8: StoreField: r3->field_27 = r0
    //     0xb7e5a8: stur            w0, [x3, #0x27]
    // 0xb7e5ac: d0 = 0.700000
    //     0xb7e5ac: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb7e5b0: ldr             d0, [x17, #0xf48]
    // 0xb7e5b4: StoreField: r3->field_7 = d0
    //     0xb7e5b4: stur            d0, [x3, #7]
    // 0xb7e5b8: ldur            x0, [fp, #-0x50]
    // 0xb7e5bc: ubfx            x0, x0, #0, #0x20
    // 0xb7e5c0: and             w1, w0, #0xff
    // 0xb7e5c4: ubfx            x1, x1, #0, #0x20
    // 0xb7e5c8: scvtf           d0, x1
    // 0xb7e5cc: d1 = 255.000000
    //     0xb7e5cc: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb7e5d0: fdiv            d2, d0, d1
    // 0xb7e5d4: StoreField: r3->field_f = d2
    //     0xb7e5d4: stur            d2, [x3, #0xf]
    // 0xb7e5d8: ldur            x0, [fp, #-0x48]
    // 0xb7e5dc: ubfx            x0, x0, #0, #0x20
    // 0xb7e5e0: and             w1, w0, #0xff
    // 0xb7e5e4: ubfx            x1, x1, #0, #0x20
    // 0xb7e5e8: scvtf           d0, x1
    // 0xb7e5ec: fdiv            d2, d0, d1
    // 0xb7e5f0: ArrayStore: r3[0] = d2  ; List_8
    //     0xb7e5f0: stur            d2, [x3, #0x17]
    // 0xb7e5f4: ldur            x0, [fp, #-0x20]
    // 0xb7e5f8: ubfx            x0, x0, #0, #0x20
    // 0xb7e5fc: and             w1, w0, #0xff
    // 0xb7e600: ubfx            x1, x1, #0, #0x20
    // 0xb7e604: scvtf           d0, x1
    // 0xb7e608: fdiv            d2, d0, d1
    // 0xb7e60c: StoreField: r3->field_1f = d2
    //     0xb7e60c: stur            d2, [x3, #0x1f]
    // 0xb7e610: r1 = Null
    //     0xb7e610: mov             x1, NULL
    // 0xb7e614: r2 = 4
    //     0xb7e614: movz            x2, #0x4
    // 0xb7e618: r0 = AllocateArray()
    //     0xb7e618: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7e61c: mov             x2, x0
    // 0xb7e620: ldur            x0, [fp, #-0x58]
    // 0xb7e624: stur            x2, [fp, #-0x60]
    // 0xb7e628: StoreField: r2->field_f = r0
    //     0xb7e628: stur            w0, [x2, #0xf]
    // 0xb7e62c: ldur            x0, [fp, #-0x38]
    // 0xb7e630: StoreField: r2->field_13 = r0
    //     0xb7e630: stur            w0, [x2, #0x13]
    // 0xb7e634: r1 = <Color>
    //     0xb7e634: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb7e638: ldr             x1, [x1, #0xf80]
    // 0xb7e63c: r0 = AllocateGrowableArray()
    //     0xb7e63c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7e640: mov             x1, x0
    // 0xb7e644: ldur            x0, [fp, #-0x60]
    // 0xb7e648: stur            x1, [fp, #-0x38]
    // 0xb7e64c: StoreField: r1->field_f = r0
    //     0xb7e64c: stur            w0, [x1, #0xf]
    // 0xb7e650: r2 = 4
    //     0xb7e650: movz            x2, #0x4
    // 0xb7e654: StoreField: r1->field_b = r2
    //     0xb7e654: stur            w2, [x1, #0xb]
    // 0xb7e658: r0 = LinearGradient()
    //     0xb7e658: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb7e65c: mov             x1, x0
    // 0xb7e660: r0 = Instance_Alignment
    //     0xb7e660: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb7e664: ldr             x0, [x0, #0xce0]
    // 0xb7e668: stur            x1, [fp, #-0x58]
    // 0xb7e66c: StoreField: r1->field_13 = r0
    //     0xb7e66c: stur            w0, [x1, #0x13]
    // 0xb7e670: r0 = Instance_Alignment
    //     0xb7e670: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb7e674: ldr             x0, [x0, #0xce8]
    // 0xb7e678: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7e678: stur            w0, [x1, #0x17]
    // 0xb7e67c: r0 = Instance_TileMode
    //     0xb7e67c: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb7e680: ldr             x0, [x0, #0xcf0]
    // 0xb7e684: StoreField: r1->field_1b = r0
    //     0xb7e684: stur            w0, [x1, #0x1b]
    // 0xb7e688: ldur            x0, [fp, #-0x38]
    // 0xb7e68c: StoreField: r1->field_7 = r0
    //     0xb7e68c: stur            w0, [x1, #7]
    // 0xb7e690: r0 = BoxDecoration()
    //     0xb7e690: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb7e694: mov             x2, x0
    // 0xb7e698: ldur            x0, [fp, #-0x40]
    // 0xb7e69c: stur            x2, [fp, #-0x38]
    // 0xb7e6a0: StoreField: r2->field_13 = r0
    //     0xb7e6a0: stur            w0, [x2, #0x13]
    // 0xb7e6a4: ldur            x0, [fp, #-0x58]
    // 0xb7e6a8: StoreField: r2->field_1b = r0
    //     0xb7e6a8: stur            w0, [x2, #0x1b]
    // 0xb7e6ac: r0 = Instance_BoxShape
    //     0xb7e6ac: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb7e6b0: ldr             x0, [x0, #0x80]
    // 0xb7e6b4: StoreField: r2->field_23 = r0
    //     0xb7e6b4: stur            w0, [x2, #0x23]
    // 0xb7e6b8: ldur            x1, [fp, #-0x18]
    // 0xb7e6bc: cmp             w1, NULL
    // 0xb7e6c0: b.ne            #0xb7e6cc
    // 0xb7e6c4: r1 = Null
    //     0xb7e6c4: mov             x1, NULL
    // 0xb7e6c8: b               #0xb7e6dc
    // 0xb7e6cc: r17 = 295
    //     0xb7e6cc: movz            x17, #0x127
    // 0xb7e6d0: ldr             w3, [x1, x17]
    // 0xb7e6d4: DecompressPointer r3
    //     0xb7e6d4: add             x3, x3, HEAP, lsl #32
    // 0xb7e6d8: mov             x1, x3
    // 0xb7e6dc: cmp             w1, NULL
    // 0xb7e6e0: b.ne            #0xb7e6ec
    // 0xb7e6e4: r4 = ""
    //     0xb7e6e4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7e6e8: b               #0xb7e6f0
    // 0xb7e6ec: mov             x4, x1
    // 0xb7e6f0: ldur            x3, [fp, #-8]
    // 0xb7e6f4: stur            x4, [fp, #-0x18]
    // 0xb7e6f8: LoadField: r1 = r3->field_f
    //     0xb7e6f8: ldur            w1, [x3, #0xf]
    // 0xb7e6fc: DecompressPointer r1
    //     0xb7e6fc: add             x1, x1, HEAP, lsl #32
    // 0xb7e700: cmp             w1, NULL
    // 0xb7e704: b.eq            #0xb7f1dc
    // 0xb7e708: r0 = of()
    //     0xb7e708: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7e70c: LoadField: r1 = r0->field_87
    //     0xb7e70c: ldur            w1, [x0, #0x87]
    // 0xb7e710: DecompressPointer r1
    //     0xb7e710: add             x1, x1, HEAP, lsl #32
    // 0xb7e714: LoadField: r0 = r1->field_27
    //     0xb7e714: ldur            w0, [x1, #0x27]
    // 0xb7e718: DecompressPointer r0
    //     0xb7e718: add             x0, x0, HEAP, lsl #32
    // 0xb7e71c: r16 = 14.000000
    //     0xb7e71c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb7e720: ldr             x16, [x16, #0x1d8]
    // 0xb7e724: r30 = Instance_Color
    //     0xb7e724: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7e728: stp             lr, x16, [SP, #8]
    // 0xb7e72c: r16 = Instance_FontWeight
    //     0xb7e72c: add             x16, PP, #0x13, lsl #12  ; [pp+0x13020] Obj!FontWeight@d68cc1
    //     0xb7e730: ldr             x16, [x16, #0x20]
    // 0xb7e734: str             x16, [SP]
    // 0xb7e738: mov             x1, x0
    // 0xb7e73c: r4 = const [0, 0x4, 0x3, 0x1, color, 0x2, fontSize, 0x1, fontWeight, 0x3, null]
    //     0xb7e73c: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3ec48] List(11) [0, 0x4, 0x3, 0x1, "color", 0x2, "fontSize", 0x1, "fontWeight", 0x3, Null]
    //     0xb7e740: ldr             x4, [x4, #0xc48]
    // 0xb7e744: r0 = copyWith()
    //     0xb7e744: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7e748: stur            x0, [fp, #-0x40]
    // 0xb7e74c: r0 = Text()
    //     0xb7e74c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7e750: mov             x1, x0
    // 0xb7e754: ldur            x0, [fp, #-0x18]
    // 0xb7e758: stur            x1, [fp, #-0x58]
    // 0xb7e75c: StoreField: r1->field_b = r0
    //     0xb7e75c: stur            w0, [x1, #0xb]
    // 0xb7e760: ldur            x0, [fp, #-0x40]
    // 0xb7e764: StoreField: r1->field_13 = r0
    //     0xb7e764: stur            w0, [x1, #0x13]
    // 0xb7e768: r0 = Instance_TextAlign
    //     0xb7e768: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb7e76c: StoreField: r1->field_1b = r0
    //     0xb7e76c: stur            w0, [x1, #0x1b]
    // 0xb7e770: r0 = Padding()
    //     0xb7e770: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7e774: mov             x1, x0
    // 0xb7e778: r0 = Instance_EdgeInsets
    //     0xb7e778: add             x0, PP, #0x27, lsl #12  ; [pp+0x27850] Obj!EdgeInsets@d57a71
    //     0xb7e77c: ldr             x0, [x0, #0x850]
    // 0xb7e780: stur            x1, [fp, #-0x18]
    // 0xb7e784: StoreField: r1->field_f = r0
    //     0xb7e784: stur            w0, [x1, #0xf]
    // 0xb7e788: ldur            x0, [fp, #-0x58]
    // 0xb7e78c: StoreField: r1->field_b = r0
    //     0xb7e78c: stur            w0, [x1, #0xb]
    // 0xb7e790: r0 = Container()
    //     0xb7e790: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7e794: stur            x0, [fp, #-0x40]
    // 0xb7e798: r16 = 20.000000
    //     0xb7e798: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb7e79c: ldr             x16, [x16, #0xac8]
    // 0xb7e7a0: r30 = 120.000000
    //     0xb7e7a0: add             lr, PP, #0x48, lsl #12  ; [pp+0x483a0] 120
    //     0xb7e7a4: ldr             lr, [lr, #0x3a0]
    // 0xb7e7a8: stp             lr, x16, [SP, #0x10]
    // 0xb7e7ac: ldur            x16, [fp, #-0x38]
    // 0xb7e7b0: ldur            lr, [fp, #-0x18]
    // 0xb7e7b4: stp             lr, x16, [SP]
    // 0xb7e7b8: mov             x1, x0
    // 0xb7e7bc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb7e7bc: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb7e7c0: ldr             x4, [x4, #0x8c0]
    // 0xb7e7c4: r0 = Container()
    //     0xb7e7c4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7e7c8: r0 = Align()
    //     0xb7e7c8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb7e7cc: mov             x1, x0
    // 0xb7e7d0: r0 = Instance_Alignment
    //     0xb7e7d0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb7e7d4: ldr             x0, [x0, #0xfa0]
    // 0xb7e7d8: stur            x1, [fp, #-0x18]
    // 0xb7e7dc: StoreField: r1->field_f = r0
    //     0xb7e7dc: stur            w0, [x1, #0xf]
    // 0xb7e7e0: ldur            x0, [fp, #-0x40]
    // 0xb7e7e4: StoreField: r1->field_b = r0
    //     0xb7e7e4: stur            w0, [x1, #0xb]
    // 0xb7e7e8: r0 = Padding()
    //     0xb7e7e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7e7ec: mov             x1, x0
    // 0xb7e7f0: r0 = Instance_EdgeInsets
    //     0xb7e7f0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e50] Obj!EdgeInsets@d58491
    //     0xb7e7f4: ldr             x0, [x0, #0xe50]
    // 0xb7e7f8: StoreField: r1->field_f = r0
    //     0xb7e7f8: stur            w0, [x1, #0xf]
    // 0xb7e7fc: ldur            x0, [fp, #-0x18]
    // 0xb7e800: StoreField: r1->field_b = r0
    //     0xb7e800: stur            w0, [x1, #0xb]
    // 0xb7e804: b               #0xb7eb44
    // 0xb7e808: r0 = Instance_Alignment
    //     0xb7e808: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb7e80c: ldr             x0, [x0, #0xfa0]
    // 0xb7e810: r2 = 4
    //     0xb7e810: movz            x2, #0x4
    // 0xb7e814: ldur            x4, [fp, #-0x28]
    // 0xb7e818: ldur            x3, [fp, #-8]
    // 0xb7e81c: LoadField: r1 = r3->field_f
    //     0xb7e81c: ldur            w1, [x3, #0xf]
    // 0xb7e820: DecompressPointer r1
    //     0xb7e820: add             x1, x1, HEAP, lsl #32
    // 0xb7e824: cmp             w1, NULL
    // 0xb7e828: b.eq            #0xb7f1e0
    // 0xb7e82c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb7e82c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb7e830: r0 = _of()
    //     0xb7e830: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb7e834: LoadField: r1 = r0->field_7
    //     0xb7e834: ldur            w1, [x0, #7]
    // 0xb7e838: DecompressPointer r1
    //     0xb7e838: add             x1, x1, HEAP, lsl #32
    // 0xb7e83c: LoadField: d0 = r1->field_7
    //     0xb7e83c: ldur            d0, [x1, #7]
    // 0xb7e840: d1 = 0.370000
    //     0xb7e840: add             x17, PP, #0x52, lsl #12  ; [pp+0x52e40] IMM: double(0.37) from 0x3fd7ae147ae147ae
    //     0xb7e844: ldr             d1, [x17, #0xe40]
    // 0xb7e848: fmul            d2, d0, d1
    // 0xb7e84c: stur            d2, [fp, #-0x70]
    // 0xb7e850: r1 = Instance_Color
    //     0xb7e850: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7e854: d0 = 0.900000
    //     0xb7e854: ldr             d0, [PP, #0x5a78]  ; [pp+0x5a78] IMM: double(0.9) from 0x3feccccccccccccd
    // 0xb7e858: r0 = withOpacity()
    //     0xb7e858: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb7e85c: stur            x0, [fp, #-0x18]
    // 0xb7e860: r0 = Radius()
    //     0xb7e860: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7e864: d0 = 4.000000
    //     0xb7e864: fmov            d0, #4.00000000
    // 0xb7e868: stur            x0, [fp, #-0x38]
    // 0xb7e86c: StoreField: r0->field_7 = d0
    //     0xb7e86c: stur            d0, [x0, #7]
    // 0xb7e870: StoreField: r0->field_f = d0
    //     0xb7e870: stur            d0, [x0, #0xf]
    // 0xb7e874: r0 = BorderRadius()
    //     0xb7e874: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb7e878: mov             x1, x0
    // 0xb7e87c: ldur            x0, [fp, #-0x38]
    // 0xb7e880: stur            x1, [fp, #-0x40]
    // 0xb7e884: StoreField: r1->field_7 = r0
    //     0xb7e884: stur            w0, [x1, #7]
    // 0xb7e888: StoreField: r1->field_b = r0
    //     0xb7e888: stur            w0, [x1, #0xb]
    // 0xb7e88c: StoreField: r1->field_f = r0
    //     0xb7e88c: stur            w0, [x1, #0xf]
    // 0xb7e890: StoreField: r1->field_13 = r0
    //     0xb7e890: stur            w0, [x1, #0x13]
    // 0xb7e894: r0 = BoxDecoration()
    //     0xb7e894: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb7e898: mov             x2, x0
    // 0xb7e89c: ldur            x0, [fp, #-0x18]
    // 0xb7e8a0: stur            x2, [fp, #-0x38]
    // 0xb7e8a4: StoreField: r2->field_7 = r0
    //     0xb7e8a4: stur            w0, [x2, #7]
    // 0xb7e8a8: ldur            x0, [fp, #-0x40]
    // 0xb7e8ac: StoreField: r2->field_13 = r0
    //     0xb7e8ac: stur            w0, [x2, #0x13]
    // 0xb7e8b0: r0 = Instance_BoxShape
    //     0xb7e8b0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb7e8b4: ldr             x0, [x0, #0x80]
    // 0xb7e8b8: StoreField: r2->field_23 = r0
    //     0xb7e8b8: stur            w0, [x2, #0x23]
    // 0xb7e8bc: ldur            x3, [fp, #-8]
    // 0xb7e8c0: LoadField: r1 = r3->field_f
    //     0xb7e8c0: ldur            w1, [x3, #0xf]
    // 0xb7e8c4: DecompressPointer r1
    //     0xb7e8c4: add             x1, x1, HEAP, lsl #32
    // 0xb7e8c8: cmp             w1, NULL
    // 0xb7e8cc: b.eq            #0xb7f1e4
    // 0xb7e8d0: r0 = of()
    //     0xb7e8d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7e8d4: LoadField: r1 = r0->field_5b
    //     0xb7e8d4: ldur            w1, [x0, #0x5b]
    // 0xb7e8d8: DecompressPointer r1
    //     0xb7e8d8: add             x1, x1, HEAP, lsl #32
    // 0xb7e8dc: stur            x1, [fp, #-0x18]
    // 0xb7e8e0: r0 = ColorFilter()
    //     0xb7e8e0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb7e8e4: mov             x1, x0
    // 0xb7e8e8: ldur            x0, [fp, #-0x18]
    // 0xb7e8ec: stur            x1, [fp, #-0x40]
    // 0xb7e8f0: StoreField: r1->field_7 = r0
    //     0xb7e8f0: stur            w0, [x1, #7]
    // 0xb7e8f4: r0 = Instance_BlendMode
    //     0xb7e8f4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb7e8f8: ldr             x0, [x0, #0xb30]
    // 0xb7e8fc: StoreField: r1->field_b = r0
    //     0xb7e8fc: stur            w0, [x1, #0xb]
    // 0xb7e900: r0 = 1
    //     0xb7e900: movz            x0, #0x1
    // 0xb7e904: StoreField: r1->field_13 = r0
    //     0xb7e904: stur            x0, [x1, #0x13]
    // 0xb7e908: r0 = SvgPicture()
    //     0xb7e908: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb7e90c: stur            x0, [fp, #-0x18]
    // 0xb7e910: ldur            x16, [fp, #-0x40]
    // 0xb7e914: str             x16, [SP]
    // 0xb7e918: mov             x1, x0
    // 0xb7e91c: r2 = "assets/images/bumper_coupon.svg"
    //     0xb7e91c: add             x2, PP, #0x52, lsl #12  ; [pp+0x52e48] "assets/images/bumper_coupon.svg"
    //     0xb7e920: ldr             x2, [x2, #0xe48]
    // 0xb7e924: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb7e924: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb7e928: ldr             x4, [x4, #0xa38]
    // 0xb7e92c: r0 = SvgPicture.asset()
    //     0xb7e92c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb7e930: ldur            x2, [fp, #-0x28]
    // 0xb7e934: LoadField: r0 = r2->field_13
    //     0xb7e934: ldur            w0, [x2, #0x13]
    // 0xb7e938: DecompressPointer r0
    //     0xb7e938: add             x0, x0, HEAP, lsl #32
    // 0xb7e93c: cmp             w0, NULL
    // 0xb7e940: b.ne            #0xb7e94c
    // 0xb7e944: r0 = Null
    //     0xb7e944: mov             x0, NULL
    // 0xb7e948: b               #0xb7e95c
    // 0xb7e94c: r17 = 295
    //     0xb7e94c: movz            x17, #0x127
    // 0xb7e950: ldr             w1, [x0, x17]
    // 0xb7e954: DecompressPointer r1
    //     0xb7e954: add             x1, x1, HEAP, lsl #32
    // 0xb7e958: mov             x0, x1
    // 0xb7e95c: cmp             w0, NULL
    // 0xb7e960: b.ne            #0xb7e96c
    // 0xb7e964: r4 = ""
    //     0xb7e964: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7e968: b               #0xb7e970
    // 0xb7e96c: mov             x4, x0
    // 0xb7e970: ldur            x3, [fp, #-8]
    // 0xb7e974: ldur            d0, [fp, #-0x70]
    // 0xb7e978: ldur            x0, [fp, #-0x18]
    // 0xb7e97c: stur            x4, [fp, #-0x40]
    // 0xb7e980: LoadField: r1 = r3->field_f
    //     0xb7e980: ldur            w1, [x3, #0xf]
    // 0xb7e984: DecompressPointer r1
    //     0xb7e984: add             x1, x1, HEAP, lsl #32
    // 0xb7e988: cmp             w1, NULL
    // 0xb7e98c: b.eq            #0xb7f1e8
    // 0xb7e990: r0 = of()
    //     0xb7e990: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7e994: LoadField: r1 = r0->field_87
    //     0xb7e994: ldur            w1, [x0, #0x87]
    // 0xb7e998: DecompressPointer r1
    //     0xb7e998: add             x1, x1, HEAP, lsl #32
    // 0xb7e99c: LoadField: r0 = r1->field_2b
    //     0xb7e99c: ldur            w0, [x1, #0x2b]
    // 0xb7e9a0: DecompressPointer r0
    //     0xb7e9a0: add             x0, x0, HEAP, lsl #32
    // 0xb7e9a4: r16 = 12.000000
    //     0xb7e9a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7e9a8: ldr             x16, [x16, #0x9e8]
    // 0xb7e9ac: r30 = Instance_Color
    //     0xb7e9ac: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7e9b0: stp             lr, x16, [SP]
    // 0xb7e9b4: mov             x1, x0
    // 0xb7e9b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7e9b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7e9bc: ldr             x4, [x4, #0xaa0]
    // 0xb7e9c0: r0 = copyWith()
    //     0xb7e9c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7e9c4: stur            x0, [fp, #-0x58]
    // 0xb7e9c8: r0 = Text()
    //     0xb7e9c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7e9cc: mov             x1, x0
    // 0xb7e9d0: ldur            x0, [fp, #-0x40]
    // 0xb7e9d4: stur            x1, [fp, #-0x60]
    // 0xb7e9d8: StoreField: r1->field_b = r0
    //     0xb7e9d8: stur            w0, [x1, #0xb]
    // 0xb7e9dc: ldur            x0, [fp, #-0x58]
    // 0xb7e9e0: StoreField: r1->field_13 = r0
    //     0xb7e9e0: stur            w0, [x1, #0x13]
    // 0xb7e9e4: r0 = Padding()
    //     0xb7e9e4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7e9e8: mov             x3, x0
    // 0xb7e9ec: r0 = Instance_EdgeInsets
    //     0xb7e9ec: add             x0, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xb7e9f0: ldr             x0, [x0, #0xc40]
    // 0xb7e9f4: stur            x3, [fp, #-0x40]
    // 0xb7e9f8: StoreField: r3->field_f = r0
    //     0xb7e9f8: stur            w0, [x3, #0xf]
    // 0xb7e9fc: ldur            x0, [fp, #-0x60]
    // 0xb7ea00: StoreField: r3->field_b = r0
    //     0xb7ea00: stur            w0, [x3, #0xb]
    // 0xb7ea04: r1 = Null
    //     0xb7ea04: mov             x1, NULL
    // 0xb7ea08: r2 = 4
    //     0xb7ea08: movz            x2, #0x4
    // 0xb7ea0c: r0 = AllocateArray()
    //     0xb7ea0c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7ea10: mov             x2, x0
    // 0xb7ea14: ldur            x0, [fp, #-0x18]
    // 0xb7ea18: stur            x2, [fp, #-0x58]
    // 0xb7ea1c: StoreField: r2->field_f = r0
    //     0xb7ea1c: stur            w0, [x2, #0xf]
    // 0xb7ea20: ldur            x0, [fp, #-0x40]
    // 0xb7ea24: StoreField: r2->field_13 = r0
    //     0xb7ea24: stur            w0, [x2, #0x13]
    // 0xb7ea28: r1 = <Widget>
    //     0xb7ea28: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7ea2c: r0 = AllocateGrowableArray()
    //     0xb7ea2c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7ea30: mov             x1, x0
    // 0xb7ea34: ldur            x0, [fp, #-0x58]
    // 0xb7ea38: stur            x1, [fp, #-0x18]
    // 0xb7ea3c: StoreField: r1->field_f = r0
    //     0xb7ea3c: stur            w0, [x1, #0xf]
    // 0xb7ea40: r0 = 4
    //     0xb7ea40: movz            x0, #0x4
    // 0xb7ea44: StoreField: r1->field_b = r0
    //     0xb7ea44: stur            w0, [x1, #0xb]
    // 0xb7ea48: r0 = Row()
    //     0xb7ea48: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb7ea4c: mov             x1, x0
    // 0xb7ea50: r0 = Instance_Axis
    //     0xb7ea50: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb7ea54: stur            x1, [fp, #-0x40]
    // 0xb7ea58: StoreField: r1->field_f = r0
    //     0xb7ea58: stur            w0, [x1, #0xf]
    // 0xb7ea5c: r0 = Instance_MainAxisAlignment
    //     0xb7ea5c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb7ea60: ldr             x0, [x0, #0xa08]
    // 0xb7ea64: StoreField: r1->field_13 = r0
    //     0xb7ea64: stur            w0, [x1, #0x13]
    // 0xb7ea68: r0 = Instance_MainAxisSize
    //     0xb7ea68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb7ea6c: ldr             x0, [x0, #0xa10]
    // 0xb7ea70: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7ea70: stur            w0, [x1, #0x17]
    // 0xb7ea74: r0 = Instance_CrossAxisAlignment
    //     0xb7ea74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb7ea78: ldr             x0, [x0, #0xa18]
    // 0xb7ea7c: StoreField: r1->field_1b = r0
    //     0xb7ea7c: stur            w0, [x1, #0x1b]
    // 0xb7ea80: r0 = Instance_VerticalDirection
    //     0xb7ea80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb7ea84: ldr             x0, [x0, #0xa20]
    // 0xb7ea88: StoreField: r1->field_23 = r0
    //     0xb7ea88: stur            w0, [x1, #0x23]
    // 0xb7ea8c: r0 = Instance_Clip
    //     0xb7ea8c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb7ea90: ldr             x0, [x0, #0x38]
    // 0xb7ea94: StoreField: r1->field_2b = r0
    //     0xb7ea94: stur            w0, [x1, #0x2b]
    // 0xb7ea98: StoreField: r1->field_2f = rZR
    //     0xb7ea98: stur            xzr, [x1, #0x2f]
    // 0xb7ea9c: ldur            x0, [fp, #-0x18]
    // 0xb7eaa0: StoreField: r1->field_b = r0
    //     0xb7eaa0: stur            w0, [x1, #0xb]
    // 0xb7eaa4: ldur            d0, [fp, #-0x70]
    // 0xb7eaa8: r0 = inline_Allocate_Double()
    //     0xb7eaa8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb7eaac: add             x0, x0, #0x10
    //     0xb7eab0: cmp             x2, x0
    //     0xb7eab4: b.ls            #0xb7f1ec
    //     0xb7eab8: str             x0, [THR, #0x50]  ; THR::top
    //     0xb7eabc: sub             x0, x0, #0xf
    //     0xb7eac0: movz            x2, #0xe15c
    //     0xb7eac4: movk            x2, #0x3, lsl #16
    //     0xb7eac8: stur            x2, [x0, #-1]
    // 0xb7eacc: StoreField: r0->field_7 = d0
    //     0xb7eacc: stur            d0, [x0, #7]
    // 0xb7ead0: stur            x0, [fp, #-0x18]
    // 0xb7ead4: r0 = Container()
    //     0xb7ead4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7ead8: stur            x0, [fp, #-0x58]
    // 0xb7eadc: r16 = 20.000000
    //     0xb7eadc: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ac8] 20
    //     0xb7eae0: ldr             x16, [x16, #0xac8]
    // 0xb7eae4: ldur            lr, [fp, #-0x18]
    // 0xb7eae8: stp             lr, x16, [SP, #0x10]
    // 0xb7eaec: ldur            x16, [fp, #-0x38]
    // 0xb7eaf0: ldur            lr, [fp, #-0x40]
    // 0xb7eaf4: stp             lr, x16, [SP]
    // 0xb7eaf8: mov             x1, x0
    // 0xb7eafc: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb7eafc: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb7eb00: ldr             x4, [x4, #0x8c0]
    // 0xb7eb04: r0 = Container()
    //     0xb7eb04: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7eb08: r0 = Padding()
    //     0xb7eb08: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7eb0c: mov             x1, x0
    // 0xb7eb10: r0 = Instance_EdgeInsets
    //     0xb7eb10: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f70] Obj!EdgeInsets@d57ad1
    //     0xb7eb14: ldr             x0, [x0, #0xf70]
    // 0xb7eb18: stur            x1, [fp, #-0x18]
    // 0xb7eb1c: StoreField: r1->field_f = r0
    //     0xb7eb1c: stur            w0, [x1, #0xf]
    // 0xb7eb20: ldur            x0, [fp, #-0x58]
    // 0xb7eb24: StoreField: r1->field_b = r0
    //     0xb7eb24: stur            w0, [x1, #0xb]
    // 0xb7eb28: r0 = Align()
    //     0xb7eb28: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb7eb2c: mov             x1, x0
    // 0xb7eb30: r0 = Instance_Alignment
    //     0xb7eb30: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xb7eb34: ldr             x0, [x0, #0xfa0]
    // 0xb7eb38: StoreField: r1->field_f = r0
    //     0xb7eb38: stur            w0, [x1, #0xf]
    // 0xb7eb3c: ldur            x0, [fp, #-0x18]
    // 0xb7eb40: StoreField: r1->field_b = r0
    //     0xb7eb40: stur            w0, [x1, #0xb]
    // 0xb7eb44: ldur            x2, [fp, #-0x28]
    // 0xb7eb48: ldur            x0, [fp, #-0x10]
    // 0xb7eb4c: stur            x1, [fp, #-0x18]
    // 0xb7eb50: r0 = Visibility()
    //     0xb7eb50: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7eb54: mov             x1, x0
    // 0xb7eb58: ldur            x0, [fp, #-0x18]
    // 0xb7eb5c: stur            x1, [fp, #-0x38]
    // 0xb7eb60: StoreField: r1->field_b = r0
    //     0xb7eb60: stur            w0, [x1, #0xb]
    // 0xb7eb64: r0 = Instance_SizedBox
    //     0xb7eb64: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7eb68: StoreField: r1->field_f = r0
    //     0xb7eb68: stur            w0, [x1, #0xf]
    // 0xb7eb6c: ldur            x2, [fp, #-0x10]
    // 0xb7eb70: StoreField: r1->field_13 = r2
    //     0xb7eb70: stur            w2, [x1, #0x13]
    // 0xb7eb74: r2 = false
    //     0xb7eb74: add             x2, NULL, #0x30  ; false
    // 0xb7eb78: ArrayStore: r1[0] = r2  ; List_4
    //     0xb7eb78: stur            w2, [x1, #0x17]
    // 0xb7eb7c: StoreField: r1->field_1b = r2
    //     0xb7eb7c: stur            w2, [x1, #0x1b]
    // 0xb7eb80: StoreField: r1->field_1f = r2
    //     0xb7eb80: stur            w2, [x1, #0x1f]
    // 0xb7eb84: StoreField: r1->field_23 = r2
    //     0xb7eb84: stur            w2, [x1, #0x23]
    // 0xb7eb88: StoreField: r1->field_27 = r2
    //     0xb7eb88: stur            w2, [x1, #0x27]
    // 0xb7eb8c: StoreField: r1->field_2b = r2
    //     0xb7eb8c: stur            w2, [x1, #0x2b]
    // 0xb7eb90: ldur            x3, [fp, #-0x28]
    // 0xb7eb94: LoadField: r4 = r3->field_13
    //     0xb7eb94: ldur            w4, [x3, #0x13]
    // 0xb7eb98: DecompressPointer r4
    //     0xb7eb98: add             x4, x4, HEAP, lsl #32
    // 0xb7eb9c: cmp             w4, NULL
    // 0xb7eba0: b.ne            #0xb7ebac
    // 0xb7eba4: r5 = Null
    //     0xb7eba4: mov             x5, NULL
    // 0xb7eba8: b               #0xb7ebdc
    // 0xb7ebac: r17 = 311
    //     0xb7ebac: movz            x17, #0x137
    // 0xb7ebb0: ldr             w5, [x4, x17]
    // 0xb7ebb4: DecompressPointer r5
    //     0xb7ebb4: add             x5, x5, HEAP, lsl #32
    // 0xb7ebb8: cmp             w5, NULL
    // 0xb7ebbc: b.ne            #0xb7ebc8
    // 0xb7ebc0: r5 = Null
    //     0xb7ebc0: mov             x5, NULL
    // 0xb7ebc4: b               #0xb7ebdc
    // 0xb7ebc8: LoadField: r6 = r5->field_7
    //     0xb7ebc8: ldur            w6, [x5, #7]
    // 0xb7ebcc: cbnz            w6, #0xb7ebd8
    // 0xb7ebd0: r5 = false
    //     0xb7ebd0: add             x5, NULL, #0x30  ; false
    // 0xb7ebd4: b               #0xb7ebdc
    // 0xb7ebd8: r5 = true
    //     0xb7ebd8: add             x5, NULL, #0x20  ; true
    // 0xb7ebdc: cmp             w5, NULL
    // 0xb7ebe0: b.ne            #0xb7ebe8
    // 0xb7ebe4: r5 = false
    //     0xb7ebe4: add             x5, NULL, #0x30  ; false
    // 0xb7ebe8: stur            x5, [fp, #-0x10]
    // 0xb7ebec: cmp             w4, NULL
    // 0xb7ebf0: b.ne            #0xb7ebfc
    // 0xb7ebf4: r4 = Null
    //     0xb7ebf4: mov             x4, NULL
    // 0xb7ebf8: b               #0xb7ec0c
    // 0xb7ebfc: r17 = 263
    //     0xb7ebfc: movz            x17, #0x107
    // 0xb7ec00: ldr             w6, [x4, x17]
    // 0xb7ec04: DecompressPointer r6
    //     0xb7ec04: add             x6, x6, HEAP, lsl #32
    // 0xb7ec08: mov             x4, x6
    // 0xb7ec0c: cmp             w4, NULL
    // 0xb7ec10: b.eq            #0xb7ec24
    // 0xb7ec14: tbnz            w4, #4, #0xb7ec24
    // 0xb7ec18: d0 = 38.000000
    //     0xb7ec18: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xb7ec1c: ldr             d0, [x17, #0xd10]
    // 0xb7ec20: b               #0xb7ec28
    // 0xb7ec24: d0 = 4.000000
    //     0xb7ec24: fmov            d0, #4.00000000
    // 0xb7ec28: stur            d0, [fp, #-0x70]
    // 0xb7ec2c: r0 = EdgeInsets()
    //     0xb7ec2c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb7ec30: d0 = 8.000000
    //     0xb7ec30: fmov            d0, #8.00000000
    // 0xb7ec34: stur            x0, [fp, #-0x18]
    // 0xb7ec38: StoreField: r0->field_7 = d0
    //     0xb7ec38: stur            d0, [x0, #7]
    // 0xb7ec3c: StoreField: r0->field_f = rZR
    //     0xb7ec3c: stur            xzr, [x0, #0xf]
    // 0xb7ec40: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb7ec40: stur            xzr, [x0, #0x17]
    // 0xb7ec44: ldur            d0, [fp, #-0x70]
    // 0xb7ec48: StoreField: r0->field_1f = d0
    //     0xb7ec48: stur            d0, [x0, #0x1f]
    // 0xb7ec4c: r16 = <EdgeInsets>
    //     0xb7ec4c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb7ec50: ldr             x16, [x16, #0xda0]
    // 0xb7ec54: r30 = Instance_EdgeInsets
    //     0xb7ec54: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb7ec58: ldr             lr, [lr, #0x668]
    // 0xb7ec5c: stp             lr, x16, [SP]
    // 0xb7ec60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7ec60: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7ec64: r0 = all()
    //     0xb7ec64: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7ec68: stur            x0, [fp, #-0x40]
    // 0xb7ec6c: r16 = <Color>
    //     0xb7ec6c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb7ec70: ldr             x16, [x16, #0xf80]
    // 0xb7ec74: r30 = Instance_Color
    //     0xb7ec74: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7ec78: stp             lr, x16, [SP]
    // 0xb7ec7c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7ec7c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7ec80: r0 = all()
    //     0xb7ec80: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7ec84: stur            x0, [fp, #-0x58]
    // 0xb7ec88: r0 = Radius()
    //     0xb7ec88: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7ec8c: d0 = 5.000000
    //     0xb7ec8c: fmov            d0, #5.00000000
    // 0xb7ec90: stur            x0, [fp, #-0x60]
    // 0xb7ec94: StoreField: r0->field_7 = d0
    //     0xb7ec94: stur            d0, [x0, #7]
    // 0xb7ec98: StoreField: r0->field_f = d0
    //     0xb7ec98: stur            d0, [x0, #0xf]
    // 0xb7ec9c: r0 = BorderRadius()
    //     0xb7ec9c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb7eca0: mov             x1, x0
    // 0xb7eca4: ldur            x0, [fp, #-0x60]
    // 0xb7eca8: stur            x1, [fp, #-0x68]
    // 0xb7ecac: StoreField: r1->field_7 = r0
    //     0xb7ecac: stur            w0, [x1, #7]
    // 0xb7ecb0: StoreField: r1->field_b = r0
    //     0xb7ecb0: stur            w0, [x1, #0xb]
    // 0xb7ecb4: StoreField: r1->field_f = r0
    //     0xb7ecb4: stur            w0, [x1, #0xf]
    // 0xb7ecb8: StoreField: r1->field_13 = r0
    //     0xb7ecb8: stur            w0, [x1, #0x13]
    // 0xb7ecbc: r0 = RoundedRectangleBorder()
    //     0xb7ecbc: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb7ecc0: mov             x1, x0
    // 0xb7ecc4: ldur            x0, [fp, #-0x68]
    // 0xb7ecc8: StoreField: r1->field_b = r0
    //     0xb7ecc8: stur            w0, [x1, #0xb]
    // 0xb7eccc: r0 = Instance_BorderSide
    //     0xb7eccc: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb7ecd0: ldr             x0, [x0, #0xe20]
    // 0xb7ecd4: StoreField: r1->field_7 = r0
    //     0xb7ecd4: stur            w0, [x1, #7]
    // 0xb7ecd8: r16 = <RoundedRectangleBorder>
    //     0xb7ecd8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb7ecdc: ldr             x16, [x16, #0xf78]
    // 0xb7ece0: stp             x1, x16, [SP]
    // 0xb7ece4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7ece4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7ece8: r0 = all()
    //     0xb7ece8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7ecec: stur            x0, [fp, #-0x60]
    // 0xb7ecf0: r0 = ButtonStyle()
    //     0xb7ecf0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb7ecf4: mov             x1, x0
    // 0xb7ecf8: ldur            x0, [fp, #-0x58]
    // 0xb7ecfc: stur            x1, [fp, #-0x68]
    // 0xb7ed00: StoreField: r1->field_b = r0
    //     0xb7ed00: stur            w0, [x1, #0xb]
    // 0xb7ed04: ldur            x0, [fp, #-0x40]
    // 0xb7ed08: StoreField: r1->field_23 = r0
    //     0xb7ed08: stur            w0, [x1, #0x23]
    // 0xb7ed0c: ldur            x0, [fp, #-0x60]
    // 0xb7ed10: StoreField: r1->field_43 = r0
    //     0xb7ed10: stur            w0, [x1, #0x43]
    // 0xb7ed14: r0 = TextButtonThemeData()
    //     0xb7ed14: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb7ed18: mov             x1, x0
    // 0xb7ed1c: ldur            x0, [fp, #-0x68]
    // 0xb7ed20: stur            x1, [fp, #-0x40]
    // 0xb7ed24: StoreField: r1->field_7 = r0
    //     0xb7ed24: stur            w0, [x1, #7]
    // 0xb7ed28: ldur            x2, [fp, #-0x28]
    // 0xb7ed2c: LoadField: r0 = r2->field_13
    //     0xb7ed2c: ldur            w0, [x2, #0x13]
    // 0xb7ed30: DecompressPointer r0
    //     0xb7ed30: add             x0, x0, HEAP, lsl #32
    // 0xb7ed34: cmp             w0, NULL
    // 0xb7ed38: b.ne            #0xb7ed44
    // 0xb7ed3c: r5 = Null
    //     0xb7ed3c: mov             x5, NULL
    // 0xb7ed40: b               #0xb7ed54
    // 0xb7ed44: r17 = 311
    //     0xb7ed44: movz            x17, #0x137
    // 0xb7ed48: ldr             w3, [x0, x17]
    // 0xb7ed4c: DecompressPointer r3
    //     0xb7ed4c: add             x3, x3, HEAP, lsl #32
    // 0xb7ed50: mov             x5, x3
    // 0xb7ed54: ldur            x4, [fp, #-8]
    // 0xb7ed58: ldur            x3, [fp, #-0x10]
    // 0xb7ed5c: ldur            x0, [fp, #-0x18]
    // 0xb7ed60: str             x5, [SP]
    // 0xb7ed64: r0 = _interpolateSingle()
    //     0xb7ed64: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb7ed68: mov             x2, x0
    // 0xb7ed6c: ldur            x0, [fp, #-8]
    // 0xb7ed70: stur            x2, [fp, #-0x58]
    // 0xb7ed74: LoadField: r1 = r0->field_f
    //     0xb7ed74: ldur            w1, [x0, #0xf]
    // 0xb7ed78: DecompressPointer r1
    //     0xb7ed78: add             x1, x1, HEAP, lsl #32
    // 0xb7ed7c: cmp             w1, NULL
    // 0xb7ed80: b.eq            #0xb7f204
    // 0xb7ed84: r0 = of()
    //     0xb7ed84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7ed88: LoadField: r1 = r0->field_87
    //     0xb7ed88: ldur            w1, [x0, #0x87]
    // 0xb7ed8c: DecompressPointer r1
    //     0xb7ed8c: add             x1, x1, HEAP, lsl #32
    // 0xb7ed90: LoadField: r0 = r1->field_2b
    //     0xb7ed90: ldur            w0, [x1, #0x2b]
    // 0xb7ed94: DecompressPointer r0
    //     0xb7ed94: add             x0, x0, HEAP, lsl #32
    // 0xb7ed98: r16 = 12.000000
    //     0xb7ed98: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7ed9c: ldr             x16, [x16, #0x9e8]
    // 0xb7eda0: r30 = Instance_Color
    //     0xb7eda0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb7eda4: stp             lr, x16, [SP]
    // 0xb7eda8: mov             x1, x0
    // 0xb7edac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7edac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7edb0: ldr             x4, [x4, #0xaa0]
    // 0xb7edb4: r0 = copyWith()
    //     0xb7edb4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7edb8: stur            x0, [fp, #-0x60]
    // 0xb7edbc: r0 = Text()
    //     0xb7edbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7edc0: mov             x3, x0
    // 0xb7edc4: ldur            x0, [fp, #-0x58]
    // 0xb7edc8: stur            x3, [fp, #-0x68]
    // 0xb7edcc: StoreField: r3->field_b = r0
    //     0xb7edcc: stur            w0, [x3, #0xb]
    // 0xb7edd0: ldur            x0, [fp, #-0x60]
    // 0xb7edd4: StoreField: r3->field_13 = r0
    //     0xb7edd4: stur            w0, [x3, #0x13]
    // 0xb7edd8: r1 = Function '<anonymous closure>':.
    //     0xb7edd8: add             x1, PP, #0x55, lsl #12  ; [pp+0x559a0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb7eddc: ldr             x1, [x1, #0x9a0]
    // 0xb7ede0: r2 = Null
    //     0xb7ede0: mov             x2, NULL
    // 0xb7ede4: r0 = AllocateClosure()
    //     0xb7ede4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7ede8: stur            x0, [fp, #-0x58]
    // 0xb7edec: r0 = TextButton()
    //     0xb7edec: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb7edf0: mov             x1, x0
    // 0xb7edf4: ldur            x0, [fp, #-0x58]
    // 0xb7edf8: stur            x1, [fp, #-0x60]
    // 0xb7edfc: StoreField: r1->field_b = r0
    //     0xb7edfc: stur            w0, [x1, #0xb]
    // 0xb7ee00: r0 = false
    //     0xb7ee00: add             x0, NULL, #0x30  ; false
    // 0xb7ee04: StoreField: r1->field_27 = r0
    //     0xb7ee04: stur            w0, [x1, #0x27]
    // 0xb7ee08: r2 = true
    //     0xb7ee08: add             x2, NULL, #0x20  ; true
    // 0xb7ee0c: StoreField: r1->field_2f = r2
    //     0xb7ee0c: stur            w2, [x1, #0x2f]
    // 0xb7ee10: ldur            x3, [fp, #-0x68]
    // 0xb7ee14: StoreField: r1->field_37 = r3
    //     0xb7ee14: stur            w3, [x1, #0x37]
    // 0xb7ee18: r0 = TextButtonTheme()
    //     0xb7ee18: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb7ee1c: mov             x1, x0
    // 0xb7ee20: ldur            x0, [fp, #-0x40]
    // 0xb7ee24: stur            x1, [fp, #-0x58]
    // 0xb7ee28: StoreField: r1->field_f = r0
    //     0xb7ee28: stur            w0, [x1, #0xf]
    // 0xb7ee2c: ldur            x0, [fp, #-0x60]
    // 0xb7ee30: StoreField: r1->field_b = r0
    //     0xb7ee30: stur            w0, [x1, #0xb]
    // 0xb7ee34: r0 = Padding()
    //     0xb7ee34: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7ee38: mov             x1, x0
    // 0xb7ee3c: ldur            x0, [fp, #-0x18]
    // 0xb7ee40: stur            x1, [fp, #-0x40]
    // 0xb7ee44: StoreField: r1->field_f = r0
    //     0xb7ee44: stur            w0, [x1, #0xf]
    // 0xb7ee48: ldur            x0, [fp, #-0x58]
    // 0xb7ee4c: StoreField: r1->field_b = r0
    //     0xb7ee4c: stur            w0, [x1, #0xb]
    // 0xb7ee50: r0 = Visibility()
    //     0xb7ee50: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb7ee54: mov             x2, x0
    // 0xb7ee58: ldur            x0, [fp, #-0x40]
    // 0xb7ee5c: stur            x2, [fp, #-0x18]
    // 0xb7ee60: StoreField: r2->field_b = r0
    //     0xb7ee60: stur            w0, [x2, #0xb]
    // 0xb7ee64: r0 = Instance_SizedBox
    //     0xb7ee64: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb7ee68: StoreField: r2->field_f = r0
    //     0xb7ee68: stur            w0, [x2, #0xf]
    // 0xb7ee6c: ldur            x0, [fp, #-0x10]
    // 0xb7ee70: StoreField: r2->field_13 = r0
    //     0xb7ee70: stur            w0, [x2, #0x13]
    // 0xb7ee74: r0 = false
    //     0xb7ee74: add             x0, NULL, #0x30  ; false
    // 0xb7ee78: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7ee78: stur            w0, [x2, #0x17]
    // 0xb7ee7c: StoreField: r2->field_1b = r0
    //     0xb7ee7c: stur            w0, [x2, #0x1b]
    // 0xb7ee80: StoreField: r2->field_1f = r0
    //     0xb7ee80: stur            w0, [x2, #0x1f]
    // 0xb7ee84: StoreField: r2->field_23 = r0
    //     0xb7ee84: stur            w0, [x2, #0x23]
    // 0xb7ee88: StoreField: r2->field_27 = r0
    //     0xb7ee88: stur            w0, [x2, #0x27]
    // 0xb7ee8c: StoreField: r2->field_2b = r0
    //     0xb7ee8c: stur            w0, [x2, #0x2b]
    // 0xb7ee90: ldur            x3, [fp, #-0x28]
    // 0xb7ee94: LoadField: r1 = r3->field_13
    //     0xb7ee94: ldur            w1, [x3, #0x13]
    // 0xb7ee98: DecompressPointer r1
    //     0xb7ee98: add             x1, x1, HEAP, lsl #32
    // 0xb7ee9c: cmp             w1, NULL
    // 0xb7eea0: b.ne            #0xb7eeac
    // 0xb7eea4: r1 = Null
    //     0xb7eea4: mov             x1, NULL
    // 0xb7eea8: b               #0xb7eebc
    // 0xb7eeac: r17 = 263
    //     0xb7eeac: movz            x17, #0x107
    // 0xb7eeb0: ldr             w4, [x1, x17]
    // 0xb7eeb4: DecompressPointer r4
    //     0xb7eeb4: add             x4, x4, HEAP, lsl #32
    // 0xb7eeb8: mov             x1, x4
    // 0xb7eebc: cmp             w1, NULL
    // 0xb7eec0: b.eq            #0xb7f060
    // 0xb7eec4: tbnz            w1, #4, #0xb7f060
    // 0xb7eec8: ldur            x4, [fp, #-8]
    // 0xb7eecc: LoadField: r1 = r4->field_f
    //     0xb7eecc: ldur            w1, [x4, #0xf]
    // 0xb7eed0: DecompressPointer r1
    //     0xb7eed0: add             x1, x1, HEAP, lsl #32
    // 0xb7eed4: cmp             w1, NULL
    // 0xb7eed8: b.eq            #0xb7f208
    // 0xb7eedc: r0 = of()
    //     0xb7eedc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7eee0: r17 = 307
    //     0xb7eee0: movz            x17, #0x133
    // 0xb7eee4: ldr             w1, [x0, x17]
    // 0xb7eee8: DecompressPointer r1
    //     0xb7eee8: add             x1, x1, HEAP, lsl #32
    // 0xb7eeec: stur            x1, [fp, #-0x10]
    // 0xb7eef0: r0 = Radius()
    //     0xb7eef0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb7eef4: d0 = 5.000000
    //     0xb7eef4: fmov            d0, #5.00000000
    // 0xb7eef8: stur            x0, [fp, #-0x40]
    // 0xb7eefc: StoreField: r0->field_7 = d0
    //     0xb7eefc: stur            d0, [x0, #7]
    // 0xb7ef00: StoreField: r0->field_f = d0
    //     0xb7ef00: stur            d0, [x0, #0xf]
    // 0xb7ef04: r0 = BorderRadius()
    //     0xb7ef04: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb7ef08: mov             x1, x0
    // 0xb7ef0c: ldur            x0, [fp, #-0x40]
    // 0xb7ef10: stur            x1, [fp, #-0x58]
    // 0xb7ef14: StoreField: r1->field_7 = r0
    //     0xb7ef14: stur            w0, [x1, #7]
    // 0xb7ef18: StoreField: r1->field_b = r0
    //     0xb7ef18: stur            w0, [x1, #0xb]
    // 0xb7ef1c: StoreField: r1->field_f = r0
    //     0xb7ef1c: stur            w0, [x1, #0xf]
    // 0xb7ef20: StoreField: r1->field_13 = r0
    //     0xb7ef20: stur            w0, [x1, #0x13]
    // 0xb7ef24: r0 = RoundedRectangleBorder()
    //     0xb7ef24: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb7ef28: mov             x1, x0
    // 0xb7ef2c: ldur            x0, [fp, #-0x58]
    // 0xb7ef30: StoreField: r1->field_b = r0
    //     0xb7ef30: stur            w0, [x1, #0xb]
    // 0xb7ef34: r0 = Instance_BorderSide
    //     0xb7ef34: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb7ef38: ldr             x0, [x0, #0xe20]
    // 0xb7ef3c: StoreField: r1->field_7 = r0
    //     0xb7ef3c: stur            w0, [x1, #7]
    // 0xb7ef40: r16 = <RoundedRectangleBorder>
    //     0xb7ef40: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb7ef44: ldr             x16, [x16, #0xf78]
    // 0xb7ef48: stp             x1, x16, [SP]
    // 0xb7ef4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb7ef4c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb7ef50: r0 = all()
    //     0xb7ef50: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb7ef54: stur            x0, [fp, #-0x40]
    // 0xb7ef58: r0 = ButtonStyle()
    //     0xb7ef58: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb7ef5c: mov             x2, x0
    // 0xb7ef60: ldur            x0, [fp, #-0x40]
    // 0xb7ef64: stur            x2, [fp, #-0x58]
    // 0xb7ef68: StoreField: r2->field_43 = r0
    //     0xb7ef68: stur            w0, [x2, #0x43]
    // 0xb7ef6c: ldur            x0, [fp, #-8]
    // 0xb7ef70: LoadField: r1 = r0->field_f
    //     0xb7ef70: ldur            w1, [x0, #0xf]
    // 0xb7ef74: DecompressPointer r1
    //     0xb7ef74: add             x1, x1, HEAP, lsl #32
    // 0xb7ef78: cmp             w1, NULL
    // 0xb7ef7c: b.eq            #0xb7f20c
    // 0xb7ef80: r0 = of()
    //     0xb7ef80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb7ef84: LoadField: r1 = r0->field_87
    //     0xb7ef84: ldur            w1, [x0, #0x87]
    // 0xb7ef88: DecompressPointer r1
    //     0xb7ef88: add             x1, x1, HEAP, lsl #32
    // 0xb7ef8c: LoadField: r0 = r1->field_2b
    //     0xb7ef8c: ldur            w0, [x1, #0x2b]
    // 0xb7ef90: DecompressPointer r0
    //     0xb7ef90: add             x0, x0, HEAP, lsl #32
    // 0xb7ef94: r16 = 12.000000
    //     0xb7ef94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb7ef98: ldr             x16, [x16, #0x9e8]
    // 0xb7ef9c: r30 = Instance_Color
    //     0xb7ef9c: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb7efa0: stp             lr, x16, [SP]
    // 0xb7efa4: mov             x1, x0
    // 0xb7efa8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb7efa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb7efac: ldr             x4, [x4, #0xaa0]
    // 0xb7efb0: r0 = copyWith()
    //     0xb7efb0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb7efb4: stur            x0, [fp, #-8]
    // 0xb7efb8: r0 = Text()
    //     0xb7efb8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb7efbc: mov             x3, x0
    // 0xb7efc0: r0 = "Customisable"
    //     0xb7efc0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb7efc4: ldr             x0, [x0, #0x970]
    // 0xb7efc8: stur            x3, [fp, #-0x40]
    // 0xb7efcc: StoreField: r3->field_b = r0
    //     0xb7efcc: stur            w0, [x3, #0xb]
    // 0xb7efd0: ldur            x0, [fp, #-8]
    // 0xb7efd4: StoreField: r3->field_13 = r0
    //     0xb7efd4: stur            w0, [x3, #0x13]
    // 0xb7efd8: r1 = Function '<anonymous closure>':.
    //     0xb7efd8: add             x1, PP, #0x55, lsl #12  ; [pp+0x559a8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb7efdc: ldr             x1, [x1, #0x9a8]
    // 0xb7efe0: r2 = Null
    //     0xb7efe0: mov             x2, NULL
    // 0xb7efe4: r0 = AllocateClosure()
    //     0xb7efe4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7efe8: stur            x0, [fp, #-8]
    // 0xb7efec: r0 = TextButton()
    //     0xb7efec: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb7eff0: mov             x1, x0
    // 0xb7eff4: ldur            x0, [fp, #-8]
    // 0xb7eff8: stur            x1, [fp, #-0x60]
    // 0xb7effc: StoreField: r1->field_b = r0
    //     0xb7effc: stur            w0, [x1, #0xb]
    // 0xb7f000: ldur            x0, [fp, #-0x58]
    // 0xb7f004: StoreField: r1->field_1b = r0
    //     0xb7f004: stur            w0, [x1, #0x1b]
    // 0xb7f008: r0 = false
    //     0xb7f008: add             x0, NULL, #0x30  ; false
    // 0xb7f00c: StoreField: r1->field_27 = r0
    //     0xb7f00c: stur            w0, [x1, #0x27]
    // 0xb7f010: r2 = true
    //     0xb7f010: add             x2, NULL, #0x20  ; true
    // 0xb7f014: StoreField: r1->field_2f = r2
    //     0xb7f014: stur            w2, [x1, #0x2f]
    // 0xb7f018: ldur            x3, [fp, #-0x40]
    // 0xb7f01c: StoreField: r1->field_37 = r3
    //     0xb7f01c: stur            w3, [x1, #0x37]
    // 0xb7f020: r0 = TextButtonTheme()
    //     0xb7f020: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb7f024: mov             x1, x0
    // 0xb7f028: ldur            x0, [fp, #-0x10]
    // 0xb7f02c: stur            x1, [fp, #-8]
    // 0xb7f030: StoreField: r1->field_f = r0
    //     0xb7f030: stur            w0, [x1, #0xf]
    // 0xb7f034: ldur            x0, [fp, #-0x60]
    // 0xb7f038: StoreField: r1->field_b = r0
    //     0xb7f038: stur            w0, [x1, #0xb]
    // 0xb7f03c: r0 = SizedBox()
    //     0xb7f03c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb7f040: mov             x1, x0
    // 0xb7f044: r0 = 30.000000
    //     0xb7f044: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb7f048: ldr             x0, [x0, #0x768]
    // 0xb7f04c: StoreField: r1->field_13 = r0
    //     0xb7f04c: stur            w0, [x1, #0x13]
    // 0xb7f050: ldur            x0, [fp, #-8]
    // 0xb7f054: StoreField: r1->field_b = r0
    //     0xb7f054: stur            w0, [x1, #0xb]
    // 0xb7f058: mov             x3, x1
    // 0xb7f05c: b               #0xb7f078
    // 0xb7f060: r0 = Container()
    //     0xb7f060: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb7f064: mov             x1, x0
    // 0xb7f068: stur            x0, [fp, #-8]
    // 0xb7f06c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb7f06c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb7f070: r0 = Container()
    //     0xb7f070: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb7f074: ldur            x3, [fp, #-8]
    // 0xb7f078: ldur            x2, [fp, #-0x30]
    // 0xb7f07c: ldur            x1, [fp, #-0x38]
    // 0xb7f080: ldur            x0, [fp, #-0x18]
    // 0xb7f084: stur            x3, [fp, #-8]
    // 0xb7f088: r0 = Padding()
    //     0xb7f088: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb7f08c: mov             x3, x0
    // 0xb7f090: r0 = Instance_EdgeInsets
    //     0xb7f090: add             x0, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb7f094: ldr             x0, [x0, #0xe68]
    // 0xb7f098: stur            x3, [fp, #-0x10]
    // 0xb7f09c: StoreField: r3->field_f = r0
    //     0xb7f09c: stur            w0, [x3, #0xf]
    // 0xb7f0a0: ldur            x0, [fp, #-8]
    // 0xb7f0a4: StoreField: r3->field_b = r0
    //     0xb7f0a4: stur            w0, [x3, #0xb]
    // 0xb7f0a8: r1 = Null
    //     0xb7f0a8: mov             x1, NULL
    // 0xb7f0ac: r2 = 8
    //     0xb7f0ac: movz            x2, #0x8
    // 0xb7f0b0: r0 = AllocateArray()
    //     0xb7f0b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb7f0b4: mov             x2, x0
    // 0xb7f0b8: ldur            x0, [fp, #-0x30]
    // 0xb7f0bc: stur            x2, [fp, #-8]
    // 0xb7f0c0: StoreField: r2->field_f = r0
    //     0xb7f0c0: stur            w0, [x2, #0xf]
    // 0xb7f0c4: ldur            x0, [fp, #-0x38]
    // 0xb7f0c8: StoreField: r2->field_13 = r0
    //     0xb7f0c8: stur            w0, [x2, #0x13]
    // 0xb7f0cc: ldur            x0, [fp, #-0x18]
    // 0xb7f0d0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb7f0d0: stur            w0, [x2, #0x17]
    // 0xb7f0d4: ldur            x0, [fp, #-0x10]
    // 0xb7f0d8: StoreField: r2->field_1b = r0
    //     0xb7f0d8: stur            w0, [x2, #0x1b]
    // 0xb7f0dc: r1 = <Widget>
    //     0xb7f0dc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb7f0e0: r0 = AllocateGrowableArray()
    //     0xb7f0e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb7f0e4: mov             x1, x0
    // 0xb7f0e8: ldur            x0, [fp, #-8]
    // 0xb7f0ec: stur            x1, [fp, #-0x10]
    // 0xb7f0f0: StoreField: r1->field_f = r0
    //     0xb7f0f0: stur            w0, [x1, #0xf]
    // 0xb7f0f4: r0 = 8
    //     0xb7f0f4: movz            x0, #0x8
    // 0xb7f0f8: StoreField: r1->field_b = r0
    //     0xb7f0f8: stur            w0, [x1, #0xb]
    // 0xb7f0fc: r0 = Stack()
    //     0xb7f0fc: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb7f100: mov             x1, x0
    // 0xb7f104: r0 = Instance_Alignment
    //     0xb7f104: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb7f108: ldr             x0, [x0, #0x5b8]
    // 0xb7f10c: stur            x1, [fp, #-8]
    // 0xb7f110: StoreField: r1->field_f = r0
    //     0xb7f110: stur            w0, [x1, #0xf]
    // 0xb7f114: r0 = Instance_StackFit
    //     0xb7f114: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb7f118: ldr             x0, [x0, #0xfa8]
    // 0xb7f11c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb7f11c: stur            w0, [x1, #0x17]
    // 0xb7f120: r0 = Instance_Clip
    //     0xb7f120: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb7f124: ldr             x0, [x0, #0x7e0]
    // 0xb7f128: StoreField: r1->field_1b = r0
    //     0xb7f128: stur            w0, [x1, #0x1b]
    // 0xb7f12c: ldur            x0, [fp, #-0x10]
    // 0xb7f130: StoreField: r1->field_b = r0
    //     0xb7f130: stur            w0, [x1, #0xb]
    // 0xb7f134: r0 = InkWell()
    //     0xb7f134: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb7f138: mov             x3, x0
    // 0xb7f13c: ldur            x0, [fp, #-8]
    // 0xb7f140: stur            x3, [fp, #-0x10]
    // 0xb7f144: StoreField: r3->field_b = r0
    //     0xb7f144: stur            w0, [x3, #0xb]
    // 0xb7f148: ldur            x2, [fp, #-0x28]
    // 0xb7f14c: r1 = Function '<anonymous closure>':.
    //     0xb7f14c: add             x1, PP, #0x55, lsl #12  ; [pp+0x559b0] AnonymousClosure: (0xb7f210), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/group_carousel_item_view.dart] _GroupCarouselItemViewState::imageSlider (0xb7e0e4)
    //     0xb7f150: ldr             x1, [x1, #0x9b0]
    // 0xb7f154: r0 = AllocateClosure()
    //     0xb7f154: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7f158: ldur            x2, [fp, #-0x10]
    // 0xb7f15c: StoreField: r2->field_f = r0
    //     0xb7f15c: stur            w0, [x2, #0xf]
    // 0xb7f160: r0 = true
    //     0xb7f160: add             x0, NULL, #0x20  ; true
    // 0xb7f164: StoreField: r2->field_43 = r0
    //     0xb7f164: stur            w0, [x2, #0x43]
    // 0xb7f168: r1 = Instance_BoxShape
    //     0xb7f168: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb7f16c: ldr             x1, [x1, #0x80]
    // 0xb7f170: StoreField: r2->field_47 = r1
    //     0xb7f170: stur            w1, [x2, #0x47]
    // 0xb7f174: StoreField: r2->field_6f = r0
    //     0xb7f174: stur            w0, [x2, #0x6f]
    // 0xb7f178: r1 = false
    //     0xb7f178: add             x1, NULL, #0x30  ; false
    // 0xb7f17c: StoreField: r2->field_73 = r1
    //     0xb7f17c: stur            w1, [x2, #0x73]
    // 0xb7f180: StoreField: r2->field_83 = r0
    //     0xb7f180: stur            w0, [x2, #0x83]
    // 0xb7f184: StoreField: r2->field_7b = r1
    //     0xb7f184: stur            w1, [x2, #0x7b]
    // 0xb7f188: r0 = AnimatedContainer()
    //     0xb7f188: bl              #0x9add88  ; AllocateAnimatedContainerStub -> AnimatedContainer (size=0x40)
    // 0xb7f18c: stur            x0, [fp, #-8]
    // 0xb7f190: r16 = Instance_Cubic
    //     0xb7f190: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eaf8] Obj!Cubic@d5b681
    //     0xb7f194: ldr             x16, [x16, #0xaf8]
    // 0xb7f198: str             x16, [SP]
    // 0xb7f19c: mov             x1, x0
    // 0xb7f1a0: ldur            x2, [fp, #-0x10]
    // 0xb7f1a4: r3 = Instance_Duration
    //     0xb7f1a4: ldr             x3, [PP, #0x4dc8]  ; [pp+0x4dc8] Obj!Duration@d77701
    // 0xb7f1a8: r4 = const [0, 0x4, 0x1, 0x3, curve, 0x3, null]
    //     0xb7f1a8: add             x4, PP, #0x52, lsl #12  ; [pp+0x52bc8] List(7) [0, 0x4, 0x1, 0x3, "curve", 0x3, Null]
    //     0xb7f1ac: ldr             x4, [x4, #0xbc8]
    // 0xb7f1b0: r0 = AnimatedContainer()
    //     0xb7f1b0: bl              #0x9adb28  ; [package:flutter/src/widgets/implicit_animations.dart] AnimatedContainer::AnimatedContainer
    // 0xb7f1b4: ldur            x0, [fp, #-8]
    // 0xb7f1b8: LeaveFrame
    //     0xb7f1b8: mov             SP, fp
    //     0xb7f1bc: ldp             fp, lr, [SP], #0x10
    // 0xb7f1c0: ret
    //     0xb7f1c0: ret             
    // 0xb7f1c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7f1c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7f1c8: b               #0xb7e114
    // 0xb7f1cc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb7f1cc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb7f1d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb7f1d0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb7f1d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f1d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7f1d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f1d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7f1dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f1dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7f1e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f1e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7f1e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f1e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7f1e8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xb7f1e8: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xb7f1ec: SaveReg d0
    //     0xb7f1ec: str             q0, [SP, #-0x10]!
    // 0xb7f1f0: SaveReg r1
    //     0xb7f1f0: str             x1, [SP, #-8]!
    // 0xb7f1f4: r0 = AllocateDouble()
    //     0xb7f1f4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb7f1f8: RestoreReg r1
    //     0xb7f1f8: ldr             x1, [SP], #8
    // 0xb7f1fc: RestoreReg d0
    //     0xb7f1fc: ldr             q0, [SP], #0x10
    // 0xb7f200: b               #0xb7eacc
    // 0xb7f204: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f204: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7f208: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f208: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb7f20c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f20c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb7f210, size: 0xe8
    // 0xb7f210: EnterFrame
    //     0xb7f210: stp             fp, lr, [SP, #-0x10]!
    //     0xb7f214: mov             fp, SP
    // 0xb7f218: AllocStack(0x38)
    //     0xb7f218: sub             SP, SP, #0x38
    // 0xb7f21c: SetupParameters()
    //     0xb7f21c: ldr             x0, [fp, #0x10]
    //     0xb7f220: ldur            w1, [x0, #0x17]
    //     0xb7f224: add             x1, x1, HEAP, lsl #32
    // 0xb7f228: CheckStackOverflow
    //     0xb7f228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7f22c: cmp             SP, x16
    //     0xb7f230: b.ls            #0xb7f2ec
    // 0xb7f234: LoadField: r0 = r1->field_f
    //     0xb7f234: ldur            w0, [x1, #0xf]
    // 0xb7f238: DecompressPointer r0
    //     0xb7f238: add             x0, x0, HEAP, lsl #32
    // 0xb7f23c: LoadField: r2 = r0->field_b
    //     0xb7f23c: ldur            w2, [x0, #0xb]
    // 0xb7f240: DecompressPointer r2
    //     0xb7f240: add             x2, x2, HEAP, lsl #32
    // 0xb7f244: cmp             w2, NULL
    // 0xb7f248: b.eq            #0xb7f2f4
    // 0xb7f24c: LoadField: r0 = r2->field_23
    //     0xb7f24c: ldur            w0, [x2, #0x23]
    // 0xb7f250: DecompressPointer r0
    //     0xb7f250: add             x0, x0, HEAP, lsl #32
    // 0xb7f254: cmp             w0, NULL
    // 0xb7f258: b.ne            #0xb7f260
    // 0xb7f25c: r0 = ""
    //     0xb7f25c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7f260: LoadField: r3 = r2->field_1f
    //     0xb7f260: ldur            w3, [x2, #0x1f]
    // 0xb7f264: DecompressPointer r3
    //     0xb7f264: add             x3, x3, HEAP, lsl #32
    // 0xb7f268: cmp             w3, NULL
    // 0xb7f26c: b.ne            #0xb7f274
    // 0xb7f270: r3 = ""
    //     0xb7f270: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7f274: LoadField: r4 = r2->field_27
    //     0xb7f274: ldur            w4, [x2, #0x27]
    // 0xb7f278: DecompressPointer r4
    //     0xb7f278: add             x4, x4, HEAP, lsl #32
    // 0xb7f27c: cmp             w4, NULL
    // 0xb7f280: b.ne            #0xb7f288
    // 0xb7f284: r4 = ""
    //     0xb7f284: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7f288: LoadField: r5 = r2->field_f
    //     0xb7f288: ldur            w5, [x2, #0xf]
    // 0xb7f28c: DecompressPointer r5
    //     0xb7f28c: add             x5, x5, HEAP, lsl #32
    // 0xb7f290: cmp             w5, NULL
    // 0xb7f294: b.ne            #0xb7f29c
    // 0xb7f298: r5 = ""
    //     0xb7f298: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb7f29c: LoadField: r6 = r1->field_13
    //     0xb7f29c: ldur            w6, [x1, #0x13]
    // 0xb7f2a0: DecompressPointer r6
    //     0xb7f2a0: add             x6, x6, HEAP, lsl #32
    // 0xb7f2a4: LoadField: r1 = r2->field_3f
    //     0xb7f2a4: ldur            w1, [x2, #0x3f]
    // 0xb7f2a8: DecompressPointer r1
    //     0xb7f2a8: add             x1, x1, HEAP, lsl #32
    // 0xb7f2ac: stp             x0, x1, [SP, #0x28]
    // 0xb7f2b0: r16 = "product_page"
    //     0xb7f2b0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb7f2b4: ldr             x16, [x16, #0x480]
    // 0xb7f2b8: stp             x16, x3, [SP, #0x18]
    // 0xb7f2bc: stp             x5, x4, [SP, #8]
    // 0xb7f2c0: str             x6, [SP]
    // 0xb7f2c4: r4 = 0
    //     0xb7f2c4: movz            x4, #0
    // 0xb7f2c8: ldr             x0, [SP, #0x30]
    // 0xb7f2cc: r16 = UnlinkedCall_0x613b5c
    //     0xb7f2cc: add             x16, PP, #0x55, lsl #12  ; [pp+0x559b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb7f2d0: add             x16, x16, #0x9b8
    // 0xb7f2d4: ldp             x5, lr, [x16]
    // 0xb7f2d8: blr             lr
    // 0xb7f2dc: r0 = Null
    //     0xb7f2dc: mov             x0, NULL
    // 0xb7f2e0: LeaveFrame
    //     0xb7f2e0: mov             SP, fp
    //     0xb7f2e4: ldp             fp, lr, [SP], #0x10
    // 0xb7f2e8: ret
    //     0xb7f2e8: ret             
    // 0xb7f2ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7f2ec: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7f2f0: b               #0xb7f234
    // 0xb7f2f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f2f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb7f2f8, size: 0x84
    // 0xb7f2f8: EnterFrame
    //     0xb7f2f8: stp             fp, lr, [SP, #-0x10]!
    //     0xb7f2fc: mov             fp, SP
    // 0xb7f300: AllocStack(0x10)
    //     0xb7f300: sub             SP, SP, #0x10
    // 0xb7f304: SetupParameters()
    //     0xb7f304: ldr             x0, [fp, #0x18]
    //     0xb7f308: ldur            w1, [x0, #0x17]
    //     0xb7f30c: add             x1, x1, HEAP, lsl #32
    //     0xb7f310: stur            x1, [fp, #-8]
    // 0xb7f314: CheckStackOverflow
    //     0xb7f314: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7f318: cmp             SP, x16
    //     0xb7f31c: b.ls            #0xb7f374
    // 0xb7f320: r1 = 1
    //     0xb7f320: movz            x1, #0x1
    // 0xb7f324: r0 = AllocateContext()
    //     0xb7f324: bl              #0x16f6108  ; AllocateContextStub
    // 0xb7f328: mov             x1, x0
    // 0xb7f32c: ldur            x0, [fp, #-8]
    // 0xb7f330: StoreField: r1->field_b = r0
    //     0xb7f330: stur            w0, [x1, #0xb]
    // 0xb7f334: ldr             x2, [fp, #0x10]
    // 0xb7f338: StoreField: r1->field_f = r2
    //     0xb7f338: stur            w2, [x1, #0xf]
    // 0xb7f33c: LoadField: r3 = r0->field_f
    //     0xb7f33c: ldur            w3, [x0, #0xf]
    // 0xb7f340: DecompressPointer r3
    //     0xb7f340: add             x3, x3, HEAP, lsl #32
    // 0xb7f344: mov             x2, x1
    // 0xb7f348: stur            x3, [fp, #-0x10]
    // 0xb7f34c: r1 = Function '<anonymous closure>':.
    //     0xb7f34c: add             x1, PP, #0x55, lsl #12  ; [pp+0x559c8] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xb7f350: ldr             x1, [x1, #0x9c8]
    // 0xb7f354: r0 = AllocateClosure()
    //     0xb7f354: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb7f358: ldur            x1, [fp, #-0x10]
    // 0xb7f35c: mov             x2, x0
    // 0xb7f360: r0 = setState()
    //     0xb7f360: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb7f364: r0 = Null
    //     0xb7f364: mov             x0, NULL
    // 0xb7f368: LeaveFrame
    //     0xb7f368: mov             SP, fp
    //     0xb7f36c: ldp             fp, lr, [SP], #0x10
    // 0xb7f370: ret
    //     0xb7f370: ret             
    // 0xb7f374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7f374: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7f378: b               #0xb7f320
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb7f37c, size: 0xd4
    // 0xb7f37c: EnterFrame
    //     0xb7f37c: stp             fp, lr, [SP, #-0x10]!
    //     0xb7f380: mov             fp, SP
    // 0xb7f384: AllocStack(0x38)
    //     0xb7f384: sub             SP, SP, #0x38
    // 0xb7f388: SetupParameters()
    //     0xb7f388: ldr             x0, [fp, #0x10]
    //     0xb7f38c: ldur            w1, [x0, #0x17]
    //     0xb7f390: add             x1, x1, HEAP, lsl #32
    // 0xb7f394: CheckStackOverflow
    //     0xb7f394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb7f398: cmp             SP, x16
    //     0xb7f39c: b.ls            #0xb7f444
    // 0xb7f3a0: LoadField: r0 = r1->field_f
    //     0xb7f3a0: ldur            w0, [x1, #0xf]
    // 0xb7f3a4: DecompressPointer r0
    //     0xb7f3a4: add             x0, x0, HEAP, lsl #32
    // 0xb7f3a8: LoadField: r1 = r0->field_b
    //     0xb7f3a8: ldur            w1, [x0, #0xb]
    // 0xb7f3ac: DecompressPointer r1
    //     0xb7f3ac: add             x1, x1, HEAP, lsl #32
    // 0xb7f3b0: cmp             w1, NULL
    // 0xb7f3b4: b.eq            #0xb7f44c
    // 0xb7f3b8: LoadField: r0 = r1->field_23
    //     0xb7f3b8: ldur            w0, [x1, #0x23]
    // 0xb7f3bc: DecompressPointer r0
    //     0xb7f3bc: add             x0, x0, HEAP, lsl #32
    // 0xb7f3c0: LoadField: r2 = r1->field_1f
    //     0xb7f3c0: ldur            w2, [x1, #0x1f]
    // 0xb7f3c4: DecompressPointer r2
    //     0xb7f3c4: add             x2, x2, HEAP, lsl #32
    // 0xb7f3c8: LoadField: r3 = r1->field_27
    //     0xb7f3c8: ldur            w3, [x1, #0x27]
    // 0xb7f3cc: DecompressPointer r3
    //     0xb7f3cc: add             x3, x3, HEAP, lsl #32
    // 0xb7f3d0: LoadField: r4 = r1->field_13
    //     0xb7f3d0: ldur            w4, [x1, #0x13]
    // 0xb7f3d4: DecompressPointer r4
    //     0xb7f3d4: add             x4, x4, HEAP, lsl #32
    // 0xb7f3d8: cmp             w4, NULL
    // 0xb7f3dc: b.ne            #0xb7f3e8
    // 0xb7f3e0: r4 = Null
    //     0xb7f3e0: mov             x4, NULL
    // 0xb7f3e4: b               #0xb7f3f4
    // 0xb7f3e8: LoadField: r5 = r4->field_b
    //     0xb7f3e8: ldur            w5, [x4, #0xb]
    // 0xb7f3ec: DecompressPointer r5
    //     0xb7f3ec: add             x5, x5, HEAP, lsl #32
    // 0xb7f3f0: mov             x4, x5
    // 0xb7f3f4: LoadField: r5 = r1->field_f
    //     0xb7f3f4: ldur            w5, [x1, #0xf]
    // 0xb7f3f8: DecompressPointer r5
    //     0xb7f3f8: add             x5, x5, HEAP, lsl #32
    // 0xb7f3fc: LoadField: r6 = r1->field_3b
    //     0xb7f3fc: ldur            w6, [x1, #0x3b]
    // 0xb7f400: DecompressPointer r6
    //     0xb7f400: add             x6, x6, HEAP, lsl #32
    // 0xb7f404: stp             x0, x6, [SP, #0x28]
    // 0xb7f408: r16 = "product_page"
    //     0xb7f408: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0xb7f40c: ldr             x16, [x16, #0x480]
    // 0xb7f410: stp             x16, x2, [SP, #0x18]
    // 0xb7f414: stp             x4, x3, [SP, #8]
    // 0xb7f418: str             x5, [SP]
    // 0xb7f41c: r4 = 0
    //     0xb7f41c: movz            x4, #0
    // 0xb7f420: ldr             x0, [SP, #0x30]
    // 0xb7f424: r16 = UnlinkedCall_0x613b5c
    //     0xb7f424: add             x16, PP, #0x55, lsl #12  ; [pp+0x559d0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb7f428: add             x16, x16, #0x9d0
    // 0xb7f42c: ldp             x5, lr, [x16]
    // 0xb7f430: blr             lr
    // 0xb7f434: r0 = Null
    //     0xb7f434: mov             x0, NULL
    // 0xb7f438: LeaveFrame
    //     0xb7f438: mov             SP, fp
    //     0xb7f43c: ldp             fp, lr, [SP], #0x10
    // 0xb7f440: ret
    //     0xb7f440: ret             
    // 0xb7f444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb7f444: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb7f448: b               #0xb7f3a0
    // 0xb7f44c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb7f44c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87d60, size: 0x84
    // 0xc87d60: EnterFrame
    //     0xc87d60: stp             fp, lr, [SP, #-0x10]!
    //     0xc87d64: mov             fp, SP
    // 0xc87d68: AllocStack(0x8)
    //     0xc87d68: sub             SP, SP, #8
    // 0xc87d6c: SetupParameters(_GroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc87d6c: mov             x0, x1
    //     0xc87d70: stur            x1, [fp, #-8]
    // 0xc87d74: CheckStackOverflow
    //     0xc87d74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87d78: cmp             SP, x16
    //     0xc87d7c: b.ls            #0xc87dc4
    // 0xc87d80: LoadField: r1 = r0->field_13
    //     0xc87d80: ldur            w1, [x0, #0x13]
    // 0xc87d84: DecompressPointer r1
    //     0xc87d84: add             x1, x1, HEAP, lsl #32
    // 0xc87d88: r16 = Sentinel
    //     0xc87d88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87d8c: cmp             w1, w16
    // 0xc87d90: b.eq            #0xc87dcc
    // 0xc87d94: r0 = dispose()
    //     0xc87d94: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87d98: ldur            x0, [fp, #-8]
    // 0xc87d9c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc87d9c: ldur            w1, [x0, #0x17]
    // 0xc87da0: DecompressPointer r1
    //     0xc87da0: add             x1, x1, HEAP, lsl #32
    // 0xc87da4: r16 = Sentinel
    //     0xc87da4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87da8: cmp             w1, w16
    // 0xc87dac: b.eq            #0xc87dd8
    // 0xc87db0: r0 = dispose()
    //     0xc87db0: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87db4: r0 = Null
    //     0xc87db4: mov             x0, NULL
    // 0xc87db8: LeaveFrame
    //     0xc87db8: mov             SP, fp
    //     0xc87dbc: ldp             fp, lr, [SP], #0x10
    // 0xc87dc0: ret
    //     0xc87dc0: ret             
    // 0xc87dc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87dc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87dc8: b               #0xc87d80
    // 0xc87dcc: r9 = _pageController
    //     0xc87dcc: add             x9, PP, #0x55, lsl #12  ; [pp+0x55908] Field <_GroupCarouselItemViewState@1616195414._pageController@1616195414>: late (offset: 0x14)
    //     0xc87dd0: ldr             x9, [x9, #0x908]
    // 0xc87dd4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87dd4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc87dd8: r9 = _imagePageController
    //     0xc87dd8: add             x9, PP, #0x55, lsl #12  ; [pp+0x55978] Field <_GroupCarouselItemViewState@1616195414._imagePageController@1616195414>: late (offset: 0x18)
    //     0xc87ddc: ldr             x9, [x9, #0x978]
    // 0xc87de0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87de0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4062, size: 0x48, field offset: 0xc
//   const constructor, 
class GroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f714, size: 0x34
    // 0xc7f714: EnterFrame
    //     0xc7f714: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f718: mov             fp, SP
    // 0xc7f71c: mov             x0, x1
    // 0xc7f720: r1 = <GroupCarouselItemView>
    //     0xc7f720: add             x1, PP, #0x48, lsl #12  ; [pp+0x487b0] TypeArguments: <GroupCarouselItemView>
    //     0xc7f724: ldr             x1, [x1, #0x7b0]
    // 0xc7f728: r0 = _GroupCarouselItemViewState()
    //     0xc7f728: bl              #0xc7f748  ; Allocate_GroupCarouselItemViewStateStub -> _GroupCarouselItemViewState (size=0x24)
    // 0xc7f72c: r1 = Sentinel
    //     0xc7f72c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f730: StoreField: r0->field_13 = r1
    //     0xc7f730: stur            w1, [x0, #0x13]
    // 0xc7f734: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7f734: stur            w1, [x0, #0x17]
    // 0xc7f738: StoreField: r0->field_1b = rZR
    //     0xc7f738: stur            xzr, [x0, #0x1b]
    // 0xc7f73c: LeaveFrame
    //     0xc7f73c: mov             SP, fp
    //     0xc7f740: ldp             fp, lr, [SP], #0x10
    // 0xc7f744: ret
    //     0xc7f744: ret             
  }
}
