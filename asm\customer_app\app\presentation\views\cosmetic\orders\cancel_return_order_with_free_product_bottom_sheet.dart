// lib: , url: package:customer_app/app/presentation/views/cosmetic/orders/cancel_return_order_with_free_product_bottom_sheet.dart

// class id: 1049290, size: 0x8
class :: {
}

// class id: 3416, size: 0x14, field offset: 0x14
class _CancelReturnOrderWithFreeProductBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xaf829c, size: 0x20fc
    // 0xaf829c: EnterFrame
    //     0xaf829c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf82a0: mov             fp, SP
    // 0xaf82a4: AllocStack(0x98)
    //     0xaf82a4: sub             SP, SP, #0x98
    // 0xaf82a8: SetupParameters(_CancelReturnOrderWithFreeProductBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xaf82a8: mov             x0, x1
    //     0xaf82ac: stur            x1, [fp, #-8]
    //     0xaf82b0: mov             x1, x2
    //     0xaf82b4: stur            x2, [fp, #-0x10]
    // 0xaf82b8: CheckStackOverflow
    //     0xaf82b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf82bc: cmp             SP, x16
    //     0xaf82c0: b.ls            #0xafa2b8
    // 0xaf82c4: r1 = 1
    //     0xaf82c4: movz            x1, #0x1
    // 0xaf82c8: r0 = AllocateContext()
    //     0xaf82c8: bl              #0x16f6108  ; AllocateContextStub
    // 0xaf82cc: mov             x2, x0
    // 0xaf82d0: ldur            x0, [fp, #-8]
    // 0xaf82d4: stur            x2, [fp, #-0x20]
    // 0xaf82d8: StoreField: r2->field_f = r0
    //     0xaf82d8: stur            w0, [x2, #0xf]
    // 0xaf82dc: LoadField: r1 = r0->field_b
    //     0xaf82dc: ldur            w1, [x0, #0xb]
    // 0xaf82e0: DecompressPointer r1
    //     0xaf82e0: add             x1, x1, HEAP, lsl #32
    // 0xaf82e4: cmp             w1, NULL
    // 0xaf82e8: b.eq            #0xafa2c0
    // 0xaf82ec: LoadField: r3 = r1->field_13
    //     0xaf82ec: ldur            w3, [x1, #0x13]
    // 0xaf82f0: DecompressPointer r3
    //     0xaf82f0: add             x3, x3, HEAP, lsl #32
    // 0xaf82f4: ldur            x1, [fp, #-0x10]
    // 0xaf82f8: stur            x3, [fp, #-0x18]
    // 0xaf82fc: r0 = of()
    //     0xaf82fc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf8300: LoadField: r1 = r0->field_87
    //     0xaf8300: ldur            w1, [x0, #0x87]
    // 0xaf8304: DecompressPointer r1
    //     0xaf8304: add             x1, x1, HEAP, lsl #32
    // 0xaf8308: LoadField: r0 = r1->field_7
    //     0xaf8308: ldur            w0, [x1, #7]
    // 0xaf830c: DecompressPointer r0
    //     0xaf830c: add             x0, x0, HEAP, lsl #32
    // 0xaf8310: stur            x0, [fp, #-0x28]
    // 0xaf8314: r1 = Instance_Color
    //     0xaf8314: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf8318: d0 = 0.700000
    //     0xaf8318: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaf831c: ldr             d0, [x17, #0xf48]
    // 0xaf8320: r0 = withOpacity()
    //     0xaf8320: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf8324: r16 = 14.000000
    //     0xaf8324: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf8328: ldr             x16, [x16, #0x1d8]
    // 0xaf832c: stp             x16, x0, [SP]
    // 0xaf8330: ldur            x1, [fp, #-0x28]
    // 0xaf8334: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaf8334: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaf8338: ldr             x4, [x4, #0x9b8]
    // 0xaf833c: r0 = copyWith()
    //     0xaf833c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf8340: stur            x0, [fp, #-0x28]
    // 0xaf8344: r0 = Text()
    //     0xaf8344: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf8348: mov             x2, x0
    // 0xaf834c: ldur            x0, [fp, #-0x18]
    // 0xaf8350: stur            x2, [fp, #-0x30]
    // 0xaf8354: StoreField: r2->field_b = r0
    //     0xaf8354: stur            w0, [x2, #0xb]
    // 0xaf8358: ldur            x0, [fp, #-0x28]
    // 0xaf835c: StoreField: r2->field_13 = r0
    //     0xaf835c: stur            w0, [x2, #0x13]
    // 0xaf8360: ldur            x0, [fp, #-8]
    // 0xaf8364: LoadField: r1 = r0->field_b
    //     0xaf8364: ldur            w1, [x0, #0xb]
    // 0xaf8368: DecompressPointer r1
    //     0xaf8368: add             x1, x1, HEAP, lsl #32
    // 0xaf836c: cmp             w1, NULL
    // 0xaf8370: b.eq            #0xafa2c4
    // 0xaf8374: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xaf8374: ldur            w3, [x1, #0x17]
    // 0xaf8378: DecompressPointer r3
    //     0xaf8378: add             x3, x3, HEAP, lsl #32
    // 0xaf837c: ldur            x1, [fp, #-0x10]
    // 0xaf8380: stur            x3, [fp, #-0x18]
    // 0xaf8384: r0 = of()
    //     0xaf8384: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf8388: LoadField: r1 = r0->field_87
    //     0xaf8388: ldur            w1, [x0, #0x87]
    // 0xaf838c: DecompressPointer r1
    //     0xaf838c: add             x1, x1, HEAP, lsl #32
    // 0xaf8390: LoadField: r0 = r1->field_2b
    //     0xaf8390: ldur            w0, [x1, #0x2b]
    // 0xaf8394: DecompressPointer r0
    //     0xaf8394: add             x0, x0, HEAP, lsl #32
    // 0xaf8398: stur            x0, [fp, #-0x28]
    // 0xaf839c: r1 = Instance_Color
    //     0xaf839c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf83a0: d0 = 0.700000
    //     0xaf83a0: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xaf83a4: ldr             d0, [x17, #0xf48]
    // 0xaf83a8: r0 = withOpacity()
    //     0xaf83a8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf83ac: r16 = 12.000000
    //     0xaf83ac: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf83b0: ldr             x16, [x16, #0x9e8]
    // 0xaf83b4: stp             x16, x0, [SP]
    // 0xaf83b8: ldur            x1, [fp, #-0x28]
    // 0xaf83bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaf83bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaf83c0: ldr             x4, [x4, #0x9b8]
    // 0xaf83c4: r0 = copyWith()
    //     0xaf83c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf83c8: stur            x0, [fp, #-0x28]
    // 0xaf83cc: r0 = Text()
    //     0xaf83cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf83d0: mov             x1, x0
    // 0xaf83d4: ldur            x0, [fp, #-0x18]
    // 0xaf83d8: stur            x1, [fp, #-0x38]
    // 0xaf83dc: StoreField: r1->field_b = r0
    //     0xaf83dc: stur            w0, [x1, #0xb]
    // 0xaf83e0: ldur            x0, [fp, #-0x28]
    // 0xaf83e4: StoreField: r1->field_13 = r0
    //     0xaf83e4: stur            w0, [x1, #0x13]
    // 0xaf83e8: r0 = Padding()
    //     0xaf83e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf83ec: mov             x1, x0
    // 0xaf83f0: r0 = Instance_EdgeInsets
    //     0xaf83f0: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xaf83f4: ldr             x0, [x0, #0x668]
    // 0xaf83f8: stur            x1, [fp, #-0x18]
    // 0xaf83fc: StoreField: r1->field_f = r0
    //     0xaf83fc: stur            w0, [x1, #0xf]
    // 0xaf8400: ldur            x0, [fp, #-0x38]
    // 0xaf8404: StoreField: r1->field_b = r0
    //     0xaf8404: stur            w0, [x1, #0xb]
    // 0xaf8408: r0 = Radius()
    //     0xaf8408: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf840c: d0 = 12.000000
    //     0xaf840c: fmov            d0, #12.00000000
    // 0xaf8410: stur            x0, [fp, #-0x28]
    // 0xaf8414: StoreField: r0->field_7 = d0
    //     0xaf8414: stur            d0, [x0, #7]
    // 0xaf8418: StoreField: r0->field_f = d0
    //     0xaf8418: stur            d0, [x0, #0xf]
    // 0xaf841c: r0 = BorderRadius()
    //     0xaf841c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf8420: mov             x2, x0
    // 0xaf8424: ldur            x0, [fp, #-0x28]
    // 0xaf8428: stur            x2, [fp, #-0x38]
    // 0xaf842c: StoreField: r2->field_7 = r0
    //     0xaf842c: stur            w0, [x2, #7]
    // 0xaf8430: StoreField: r2->field_b = r0
    //     0xaf8430: stur            w0, [x2, #0xb]
    // 0xaf8434: StoreField: r2->field_f = r0
    //     0xaf8434: stur            w0, [x2, #0xf]
    // 0xaf8438: StoreField: r2->field_13 = r0
    //     0xaf8438: stur            w0, [x2, #0x13]
    // 0xaf843c: r1 = Instance_Color
    //     0xaf843c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf8440: d0 = 0.070000
    //     0xaf8440: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xaf8444: ldr             d0, [x17, #0x5f8]
    // 0xaf8448: r0 = withOpacity()
    //     0xaf8448: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xaf844c: r16 = 1.000000
    //     0xaf844c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xaf8450: str             x16, [SP]
    // 0xaf8454: mov             x2, x0
    // 0xaf8458: r1 = Null
    //     0xaf8458: mov             x1, NULL
    // 0xaf845c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xaf845c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xaf8460: ldr             x4, [x4, #0x108]
    // 0xaf8464: r0 = Border.all()
    //     0xaf8464: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xaf8468: stur            x0, [fp, #-0x28]
    // 0xaf846c: r0 = BoxDecoration()
    //     0xaf846c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf8470: mov             x2, x0
    // 0xaf8474: ldur            x0, [fp, #-0x28]
    // 0xaf8478: stur            x2, [fp, #-0x40]
    // 0xaf847c: StoreField: r2->field_f = r0
    //     0xaf847c: stur            w0, [x2, #0xf]
    // 0xaf8480: ldur            x0, [fp, #-0x38]
    // 0xaf8484: StoreField: r2->field_13 = r0
    //     0xaf8484: stur            w0, [x2, #0x13]
    // 0xaf8488: r0 = Instance_LinearGradient
    //     0xaf8488: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xaf848c: ldr             x0, [x0, #0x660]
    // 0xaf8490: StoreField: r2->field_1b = r0
    //     0xaf8490: stur            w0, [x2, #0x1b]
    // 0xaf8494: r0 = Instance_BoxShape
    //     0xaf8494: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf8498: ldr             x0, [x0, #0x80]
    // 0xaf849c: StoreField: r2->field_23 = r0
    //     0xaf849c: stur            w0, [x2, #0x23]
    // 0xaf84a0: ldur            x1, [fp, #-0x10]
    // 0xaf84a4: r0 = of()
    //     0xaf84a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf84a8: LoadField: r1 = r0->field_87
    //     0xaf84a8: ldur            w1, [x0, #0x87]
    // 0xaf84ac: DecompressPointer r1
    //     0xaf84ac: add             x1, x1, HEAP, lsl #32
    // 0xaf84b0: LoadField: r0 = r1->field_7
    //     0xaf84b0: ldur            w0, [x1, #7]
    // 0xaf84b4: DecompressPointer r0
    //     0xaf84b4: add             x0, x0, HEAP, lsl #32
    // 0xaf84b8: r16 = 12.000000
    //     0xaf84b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf84bc: ldr             x16, [x16, #0x9e8]
    // 0xaf84c0: r30 = Instance_Color
    //     0xaf84c0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf84c4: stp             lr, x16, [SP]
    // 0xaf84c8: mov             x1, x0
    // 0xaf84cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf84cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf84d0: ldr             x4, [x4, #0xaa0]
    // 0xaf84d4: r0 = copyWith()
    //     0xaf84d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf84d8: stur            x0, [fp, #-0x28]
    // 0xaf84dc: r0 = Text()
    //     0xaf84dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf84e0: mov             x1, x0
    // 0xaf84e4: r0 = "Free"
    //     0xaf84e4: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xaf84e8: ldr             x0, [x0, #0x668]
    // 0xaf84ec: stur            x1, [fp, #-0x38]
    // 0xaf84f0: StoreField: r1->field_b = r0
    //     0xaf84f0: stur            w0, [x1, #0xb]
    // 0xaf84f4: ldur            x2, [fp, #-0x28]
    // 0xaf84f8: StoreField: r1->field_13 = r2
    //     0xaf84f8: stur            w2, [x1, #0x13]
    // 0xaf84fc: r0 = Center()
    //     0xaf84fc: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaf8500: mov             x1, x0
    // 0xaf8504: r0 = Instance_Alignment
    //     0xaf8504: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xaf8508: ldr             x0, [x0, #0xb10]
    // 0xaf850c: stur            x1, [fp, #-0x28]
    // 0xaf8510: StoreField: r1->field_f = r0
    //     0xaf8510: stur            w0, [x1, #0xf]
    // 0xaf8514: ldur            x0, [fp, #-0x38]
    // 0xaf8518: StoreField: r1->field_b = r0
    //     0xaf8518: stur            w0, [x1, #0xb]
    // 0xaf851c: r0 = RotatedBox()
    //     0xaf851c: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xaf8520: mov             x1, x0
    // 0xaf8524: r0 = -1
    //     0xaf8524: movn            x0, #0
    // 0xaf8528: stur            x1, [fp, #-0x38]
    // 0xaf852c: StoreField: r1->field_f = r0
    //     0xaf852c: stur            x0, [x1, #0xf]
    // 0xaf8530: ldur            x0, [fp, #-0x28]
    // 0xaf8534: StoreField: r1->field_b = r0
    //     0xaf8534: stur            w0, [x1, #0xb]
    // 0xaf8538: r0 = Container()
    //     0xaf8538: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf853c: stur            x0, [fp, #-0x28]
    // 0xaf8540: r16 = 24.000000
    //     0xaf8540: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xaf8544: ldr             x16, [x16, #0xba8]
    // 0xaf8548: r30 = 56.000000
    //     0xaf8548: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xaf854c: ldr             lr, [lr, #0xb78]
    // 0xaf8550: stp             lr, x16, [SP, #0x10]
    // 0xaf8554: r16 = Instance_BoxDecoration
    //     0xaf8554: add             x16, PP, #0x40, lsl #12  ; [pp+0x40db0] Obj!BoxDecoration@d648c1
    //     0xaf8558: ldr             x16, [x16, #0xdb0]
    // 0xaf855c: ldur            lr, [fp, #-0x38]
    // 0xaf8560: stp             lr, x16, [SP]
    // 0xaf8564: mov             x1, x0
    // 0xaf8568: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xaf8568: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xaf856c: ldr             x4, [x4, #0x870]
    // 0xaf8570: r0 = Container()
    //     0xaf8570: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf8574: ldur            x0, [fp, #-8]
    // 0xaf8578: LoadField: r1 = r0->field_b
    //     0xaf8578: ldur            w1, [x0, #0xb]
    // 0xaf857c: DecompressPointer r1
    //     0xaf857c: add             x1, x1, HEAP, lsl #32
    // 0xaf8580: cmp             w1, NULL
    // 0xaf8584: b.eq            #0xafa2c8
    // 0xaf8588: LoadField: r2 = r1->field_f
    //     0xaf8588: ldur            w2, [x1, #0xf]
    // 0xaf858c: DecompressPointer r2
    //     0xaf858c: add             x2, x2, HEAP, lsl #32
    // 0xaf8590: r16 = "return_order_intermediate"
    //     0xaf8590: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xaf8594: ldr             x16, [x16, #0xb00]
    // 0xaf8598: stp             x16, x2, [SP]
    // 0xaf859c: r0 = ==()
    //     0xaf859c: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf85a0: tbnz            w0, #4, #0xaf8608
    // 0xaf85a4: ldur            x0, [fp, #-8]
    // 0xaf85a8: LoadField: r1 = r0->field_b
    //     0xaf85a8: ldur            w1, [x0, #0xb]
    // 0xaf85ac: DecompressPointer r1
    //     0xaf85ac: add             x1, x1, HEAP, lsl #32
    // 0xaf85b0: cmp             w1, NULL
    // 0xaf85b4: b.eq            #0xafa2cc
    // 0xaf85b8: LoadField: r2 = r1->field_23
    //     0xaf85b8: ldur            w2, [x1, #0x23]
    // 0xaf85bc: DecompressPointer r2
    //     0xaf85bc: add             x2, x2, HEAP, lsl #32
    // 0xaf85c0: cmp             w2, NULL
    // 0xaf85c4: b.ne            #0xaf85d0
    // 0xaf85c8: r1 = Null
    //     0xaf85c8: mov             x1, NULL
    // 0xaf85cc: b               #0xaf85f4
    // 0xaf85d0: LoadField: r1 = r2->field_3f
    //     0xaf85d0: ldur            w1, [x2, #0x3f]
    // 0xaf85d4: DecompressPointer r1
    //     0xaf85d4: add             x1, x1, HEAP, lsl #32
    // 0xaf85d8: cmp             w1, NULL
    // 0xaf85dc: b.ne            #0xaf85e8
    // 0xaf85e0: r1 = Null
    //     0xaf85e0: mov             x1, NULL
    // 0xaf85e4: b               #0xaf85f4
    // 0xaf85e8: LoadField: r2 = r1->field_7
    //     0xaf85e8: ldur            w2, [x1, #7]
    // 0xaf85ec: DecompressPointer r2
    //     0xaf85ec: add             x2, x2, HEAP, lsl #32
    // 0xaf85f0: mov             x1, x2
    // 0xaf85f4: cmp             w1, NULL
    // 0xaf85f8: b.ne            #0xaf8600
    // 0xaf85fc: r1 = ""
    //     0xaf85fc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8600: mov             x2, x1
    // 0xaf8604: b               #0xaf86f8
    // 0xaf8608: ldur            x0, [fp, #-8]
    // 0xaf860c: LoadField: r1 = r0->field_b
    //     0xaf860c: ldur            w1, [x0, #0xb]
    // 0xaf8610: DecompressPointer r1
    //     0xaf8610: add             x1, x1, HEAP, lsl #32
    // 0xaf8614: cmp             w1, NULL
    // 0xaf8618: b.eq            #0xafa2d0
    // 0xaf861c: LoadField: r2 = r1->field_f
    //     0xaf861c: ldur            w2, [x1, #0xf]
    // 0xaf8620: DecompressPointer r2
    //     0xaf8620: add             x2, x2, HEAP, lsl #32
    // 0xaf8624: r16 = "cancel_order"
    //     0xaf8624: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xaf8628: ldr             x16, [x16, #0x98]
    // 0xaf862c: stp             x16, x2, [SP]
    // 0xaf8630: r0 = ==()
    //     0xaf8630: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf8634: tbnz            w0, #4, #0xaf8698
    // 0xaf8638: ldur            x0, [fp, #-8]
    // 0xaf863c: LoadField: r1 = r0->field_b
    //     0xaf863c: ldur            w1, [x0, #0xb]
    // 0xaf8640: DecompressPointer r1
    //     0xaf8640: add             x1, x1, HEAP, lsl #32
    // 0xaf8644: cmp             w1, NULL
    // 0xaf8648: b.eq            #0xafa2d4
    // 0xaf864c: LoadField: r2 = r1->field_1b
    //     0xaf864c: ldur            w2, [x1, #0x1b]
    // 0xaf8650: DecompressPointer r2
    //     0xaf8650: add             x2, x2, HEAP, lsl #32
    // 0xaf8654: cmp             w2, NULL
    // 0xaf8658: b.ne            #0xaf8664
    // 0xaf865c: r1 = Null
    //     0xaf865c: mov             x1, NULL
    // 0xaf8660: b               #0xaf8688
    // 0xaf8664: LoadField: r1 = r2->field_7f
    //     0xaf8664: ldur            w1, [x2, #0x7f]
    // 0xaf8668: DecompressPointer r1
    //     0xaf8668: add             x1, x1, HEAP, lsl #32
    // 0xaf866c: cmp             w1, NULL
    // 0xaf8670: b.ne            #0xaf867c
    // 0xaf8674: r1 = Null
    //     0xaf8674: mov             x1, NULL
    // 0xaf8678: b               #0xaf8688
    // 0xaf867c: LoadField: r2 = r1->field_7
    //     0xaf867c: ldur            w2, [x1, #7]
    // 0xaf8680: DecompressPointer r2
    //     0xaf8680: add             x2, x2, HEAP, lsl #32
    // 0xaf8684: mov             x1, x2
    // 0xaf8688: cmp             w1, NULL
    // 0xaf868c: b.ne            #0xaf86f4
    // 0xaf8690: r1 = ""
    //     0xaf8690: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8694: b               #0xaf86f4
    // 0xaf8698: ldur            x0, [fp, #-8]
    // 0xaf869c: LoadField: r1 = r0->field_b
    //     0xaf869c: ldur            w1, [x0, #0xb]
    // 0xaf86a0: DecompressPointer r1
    //     0xaf86a0: add             x1, x1, HEAP, lsl #32
    // 0xaf86a4: cmp             w1, NULL
    // 0xaf86a8: b.eq            #0xafa2d8
    // 0xaf86ac: LoadField: r2 = r1->field_1f
    //     0xaf86ac: ldur            w2, [x1, #0x1f]
    // 0xaf86b0: DecompressPointer r2
    //     0xaf86b0: add             x2, x2, HEAP, lsl #32
    // 0xaf86b4: cmp             w2, NULL
    // 0xaf86b8: b.ne            #0xaf86c4
    // 0xaf86bc: r1 = Null
    //     0xaf86bc: mov             x1, NULL
    // 0xaf86c0: b               #0xaf86e8
    // 0xaf86c4: LoadField: r1 = r2->field_d7
    //     0xaf86c4: ldur            w1, [x2, #0xd7]
    // 0xaf86c8: DecompressPointer r1
    //     0xaf86c8: add             x1, x1, HEAP, lsl #32
    // 0xaf86cc: cmp             w1, NULL
    // 0xaf86d0: b.ne            #0xaf86dc
    // 0xaf86d4: r1 = Null
    //     0xaf86d4: mov             x1, NULL
    // 0xaf86d8: b               #0xaf86e8
    // 0xaf86dc: LoadField: r2 = r1->field_7
    //     0xaf86dc: ldur            w2, [x1, #7]
    // 0xaf86e0: DecompressPointer r2
    //     0xaf86e0: add             x2, x2, HEAP, lsl #32
    // 0xaf86e4: mov             x1, x2
    // 0xaf86e8: cmp             w1, NULL
    // 0xaf86ec: b.ne            #0xaf86f4
    // 0xaf86f0: r1 = ""
    //     0xaf86f0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf86f4: mov             x2, x1
    // 0xaf86f8: stur            x2, [fp, #-0x38]
    // 0xaf86fc: r0 = CachedNetworkImage()
    //     0xaf86fc: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaf8700: stur            x0, [fp, #-0x48]
    // 0xaf8704: r16 = 56.000000
    //     0xaf8704: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xaf8708: ldr             x16, [x16, #0xb78]
    // 0xaf870c: r30 = 56.000000
    //     0xaf870c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xaf8710: ldr             lr, [lr, #0xb78]
    // 0xaf8714: stp             lr, x16, [SP, #8]
    // 0xaf8718: r16 = Instance_BoxFit
    //     0xaf8718: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xaf871c: ldr             x16, [x16, #0x118]
    // 0xaf8720: str             x16, [SP]
    // 0xaf8724: mov             x1, x0
    // 0xaf8728: ldur            x2, [fp, #-0x38]
    // 0xaf872c: r4 = const [0, 0x5, 0x3, 0x2, fit, 0x4, height, 0x3, width, 0x2, null]
    //     0xaf872c: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb40] List(11) [0, 0x5, 0x3, 0x2, "fit", 0x4, "height", 0x3, "width", 0x2, Null]
    //     0xaf8730: ldr             x4, [x4, #0xb40]
    // 0xaf8734: r0 = CachedNetworkImage()
    //     0xaf8734: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaf8738: ldur            x0, [fp, #-8]
    // 0xaf873c: LoadField: r1 = r0->field_b
    //     0xaf873c: ldur            w1, [x0, #0xb]
    // 0xaf8740: DecompressPointer r1
    //     0xaf8740: add             x1, x1, HEAP, lsl #32
    // 0xaf8744: cmp             w1, NULL
    // 0xaf8748: b.eq            #0xafa2dc
    // 0xaf874c: LoadField: r2 = r1->field_f
    //     0xaf874c: ldur            w2, [x1, #0xf]
    // 0xaf8750: DecompressPointer r2
    //     0xaf8750: add             x2, x2, HEAP, lsl #32
    // 0xaf8754: r16 = "return_order_intermediate"
    //     0xaf8754: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xaf8758: ldr             x16, [x16, #0xb00]
    // 0xaf875c: stp             x16, x2, [SP]
    // 0xaf8760: r0 = ==()
    //     0xaf8760: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf8764: tbnz            w0, #4, #0xaf87cc
    // 0xaf8768: ldur            x0, [fp, #-8]
    // 0xaf876c: LoadField: r1 = r0->field_b
    //     0xaf876c: ldur            w1, [x0, #0xb]
    // 0xaf8770: DecompressPointer r1
    //     0xaf8770: add             x1, x1, HEAP, lsl #32
    // 0xaf8774: cmp             w1, NULL
    // 0xaf8778: b.eq            #0xafa2e0
    // 0xaf877c: LoadField: r2 = r1->field_23
    //     0xaf877c: ldur            w2, [x1, #0x23]
    // 0xaf8780: DecompressPointer r2
    //     0xaf8780: add             x2, x2, HEAP, lsl #32
    // 0xaf8784: cmp             w2, NULL
    // 0xaf8788: b.ne            #0xaf8794
    // 0xaf878c: r1 = Null
    //     0xaf878c: mov             x1, NULL
    // 0xaf8790: b               #0xaf87b8
    // 0xaf8794: LoadField: r1 = r2->field_3f
    //     0xaf8794: ldur            w1, [x2, #0x3f]
    // 0xaf8798: DecompressPointer r1
    //     0xaf8798: add             x1, x1, HEAP, lsl #32
    // 0xaf879c: cmp             w1, NULL
    // 0xaf87a0: b.ne            #0xaf87ac
    // 0xaf87a4: r1 = Null
    //     0xaf87a4: mov             x1, NULL
    // 0xaf87a8: b               #0xaf87b8
    // 0xaf87ac: LoadField: r2 = r1->field_b
    //     0xaf87ac: ldur            w2, [x1, #0xb]
    // 0xaf87b0: DecompressPointer r2
    //     0xaf87b0: add             x2, x2, HEAP, lsl #32
    // 0xaf87b4: mov             x1, x2
    // 0xaf87b8: cmp             w1, NULL
    // 0xaf87bc: b.ne            #0xaf87c4
    // 0xaf87c0: r1 = ""
    //     0xaf87c0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf87c4: mov             x2, x1
    // 0xaf87c8: b               #0xaf88bc
    // 0xaf87cc: ldur            x0, [fp, #-8]
    // 0xaf87d0: LoadField: r1 = r0->field_b
    //     0xaf87d0: ldur            w1, [x0, #0xb]
    // 0xaf87d4: DecompressPointer r1
    //     0xaf87d4: add             x1, x1, HEAP, lsl #32
    // 0xaf87d8: cmp             w1, NULL
    // 0xaf87dc: b.eq            #0xafa2e4
    // 0xaf87e0: LoadField: r2 = r1->field_f
    //     0xaf87e0: ldur            w2, [x1, #0xf]
    // 0xaf87e4: DecompressPointer r2
    //     0xaf87e4: add             x2, x2, HEAP, lsl #32
    // 0xaf87e8: r16 = "cancel_order"
    //     0xaf87e8: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xaf87ec: ldr             x16, [x16, #0x98]
    // 0xaf87f0: stp             x16, x2, [SP]
    // 0xaf87f4: r0 = ==()
    //     0xaf87f4: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf87f8: tbnz            w0, #4, #0xaf885c
    // 0xaf87fc: ldur            x0, [fp, #-8]
    // 0xaf8800: LoadField: r1 = r0->field_b
    //     0xaf8800: ldur            w1, [x0, #0xb]
    // 0xaf8804: DecompressPointer r1
    //     0xaf8804: add             x1, x1, HEAP, lsl #32
    // 0xaf8808: cmp             w1, NULL
    // 0xaf880c: b.eq            #0xafa2e8
    // 0xaf8810: LoadField: r2 = r1->field_1b
    //     0xaf8810: ldur            w2, [x1, #0x1b]
    // 0xaf8814: DecompressPointer r2
    //     0xaf8814: add             x2, x2, HEAP, lsl #32
    // 0xaf8818: cmp             w2, NULL
    // 0xaf881c: b.ne            #0xaf8828
    // 0xaf8820: r1 = Null
    //     0xaf8820: mov             x1, NULL
    // 0xaf8824: b               #0xaf884c
    // 0xaf8828: LoadField: r1 = r2->field_7f
    //     0xaf8828: ldur            w1, [x2, #0x7f]
    // 0xaf882c: DecompressPointer r1
    //     0xaf882c: add             x1, x1, HEAP, lsl #32
    // 0xaf8830: cmp             w1, NULL
    // 0xaf8834: b.ne            #0xaf8840
    // 0xaf8838: r1 = Null
    //     0xaf8838: mov             x1, NULL
    // 0xaf883c: b               #0xaf884c
    // 0xaf8840: LoadField: r2 = r1->field_b
    //     0xaf8840: ldur            w2, [x1, #0xb]
    // 0xaf8844: DecompressPointer r2
    //     0xaf8844: add             x2, x2, HEAP, lsl #32
    // 0xaf8848: mov             x1, x2
    // 0xaf884c: cmp             w1, NULL
    // 0xaf8850: b.ne            #0xaf88b8
    // 0xaf8854: r1 = ""
    //     0xaf8854: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8858: b               #0xaf88b8
    // 0xaf885c: ldur            x0, [fp, #-8]
    // 0xaf8860: LoadField: r1 = r0->field_b
    //     0xaf8860: ldur            w1, [x0, #0xb]
    // 0xaf8864: DecompressPointer r1
    //     0xaf8864: add             x1, x1, HEAP, lsl #32
    // 0xaf8868: cmp             w1, NULL
    // 0xaf886c: b.eq            #0xafa2ec
    // 0xaf8870: LoadField: r2 = r1->field_1f
    //     0xaf8870: ldur            w2, [x1, #0x1f]
    // 0xaf8874: DecompressPointer r2
    //     0xaf8874: add             x2, x2, HEAP, lsl #32
    // 0xaf8878: cmp             w2, NULL
    // 0xaf887c: b.ne            #0xaf8888
    // 0xaf8880: r1 = Null
    //     0xaf8880: mov             x1, NULL
    // 0xaf8884: b               #0xaf88ac
    // 0xaf8888: LoadField: r1 = r2->field_d7
    //     0xaf8888: ldur            w1, [x2, #0xd7]
    // 0xaf888c: DecompressPointer r1
    //     0xaf888c: add             x1, x1, HEAP, lsl #32
    // 0xaf8890: cmp             w1, NULL
    // 0xaf8894: b.ne            #0xaf88a0
    // 0xaf8898: r1 = Null
    //     0xaf8898: mov             x1, NULL
    // 0xaf889c: b               #0xaf88ac
    // 0xaf88a0: LoadField: r2 = r1->field_b
    //     0xaf88a0: ldur            w2, [x1, #0xb]
    // 0xaf88a4: DecompressPointer r2
    //     0xaf88a4: add             x2, x2, HEAP, lsl #32
    // 0xaf88a8: mov             x1, x2
    // 0xaf88ac: cmp             w1, NULL
    // 0xaf88b0: b.ne            #0xaf88b8
    // 0xaf88b4: r1 = ""
    //     0xaf88b4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf88b8: mov             x2, x1
    // 0xaf88bc: ldur            x1, [fp, #-0x10]
    // 0xaf88c0: stur            x2, [fp, #-0x38]
    // 0xaf88c4: r0 = of()
    //     0xaf88c4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf88c8: LoadField: r1 = r0->field_87
    //     0xaf88c8: ldur            w1, [x0, #0x87]
    // 0xaf88cc: DecompressPointer r1
    //     0xaf88cc: add             x1, x1, HEAP, lsl #32
    // 0xaf88d0: LoadField: r0 = r1->field_7
    //     0xaf88d0: ldur            w0, [x1, #7]
    // 0xaf88d4: DecompressPointer r0
    //     0xaf88d4: add             x0, x0, HEAP, lsl #32
    // 0xaf88d8: r16 = 12.000000
    //     0xaf88d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf88dc: ldr             x16, [x16, #0x9e8]
    // 0xaf88e0: r30 = Instance_Color
    //     0xaf88e0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf88e4: stp             lr, x16, [SP]
    // 0xaf88e8: mov             x1, x0
    // 0xaf88ec: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf88ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf88f0: ldr             x4, [x4, #0xaa0]
    // 0xaf88f4: r0 = copyWith()
    //     0xaf88f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf88f8: stur            x0, [fp, #-0x50]
    // 0xaf88fc: r0 = Text()
    //     0xaf88fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf8900: mov             x1, x0
    // 0xaf8904: ldur            x0, [fp, #-0x38]
    // 0xaf8908: stur            x1, [fp, #-0x58]
    // 0xaf890c: StoreField: r1->field_b = r0
    //     0xaf890c: stur            w0, [x1, #0xb]
    // 0xaf8910: ldur            x0, [fp, #-0x50]
    // 0xaf8914: StoreField: r1->field_13 = r0
    //     0xaf8914: stur            w0, [x1, #0x13]
    // 0xaf8918: r0 = Instance_TextOverflow
    //     0xaf8918: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xaf891c: ldr             x0, [x0, #0xe10]
    // 0xaf8920: StoreField: r1->field_2b = r0
    //     0xaf8920: stur            w0, [x1, #0x2b]
    // 0xaf8924: r2 = 2
    //     0xaf8924: movz            x2, #0x2
    // 0xaf8928: StoreField: r1->field_37 = r2
    //     0xaf8928: stur            w2, [x1, #0x37]
    // 0xaf892c: r0 = SizedBox()
    //     0xaf892c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaf8930: mov             x2, x0
    // 0xaf8934: r0 = 150.000000
    //     0xaf8934: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xaf8938: ldr             x0, [x0, #0x690]
    // 0xaf893c: stur            x2, [fp, #-0x38]
    // 0xaf8940: StoreField: r2->field_f = r0
    //     0xaf8940: stur            w0, [x2, #0xf]
    // 0xaf8944: ldur            x0, [fp, #-0x58]
    // 0xaf8948: StoreField: r2->field_b = r0
    //     0xaf8948: stur            w0, [x2, #0xb]
    // 0xaf894c: ldur            x1, [fp, #-0x10]
    // 0xaf8950: r0 = of()
    //     0xaf8950: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf8954: LoadField: r1 = r0->field_87
    //     0xaf8954: ldur            w1, [x0, #0x87]
    // 0xaf8958: DecompressPointer r1
    //     0xaf8958: add             x1, x1, HEAP, lsl #32
    // 0xaf895c: LoadField: r0 = r1->field_2b
    //     0xaf895c: ldur            w0, [x1, #0x2b]
    // 0xaf8960: DecompressPointer r0
    //     0xaf8960: add             x0, x0, HEAP, lsl #32
    // 0xaf8964: r16 = 12.000000
    //     0xaf8964: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf8968: ldr             x16, [x16, #0x9e8]
    // 0xaf896c: r30 = Instance_Color
    //     0xaf896c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xaf8970: ldr             lr, [lr, #0x858]
    // 0xaf8974: stp             lr, x16, [SP]
    // 0xaf8978: mov             x1, x0
    // 0xaf897c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf897c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf8980: ldr             x4, [x4, #0xaa0]
    // 0xaf8984: r0 = copyWith()
    //     0xaf8984: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf8988: stur            x0, [fp, #-0x50]
    // 0xaf898c: r0 = Text()
    //     0xaf898c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf8990: mov             x1, x0
    // 0xaf8994: r0 = "Free"
    //     0xaf8994: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xaf8998: ldr             x0, [x0, #0x668]
    // 0xaf899c: stur            x1, [fp, #-0x58]
    // 0xaf89a0: StoreField: r1->field_b = r0
    //     0xaf89a0: stur            w0, [x1, #0xb]
    // 0xaf89a4: ldur            x0, [fp, #-0x50]
    // 0xaf89a8: StoreField: r1->field_13 = r0
    //     0xaf89a8: stur            w0, [x1, #0x13]
    // 0xaf89ac: ldur            x0, [fp, #-8]
    // 0xaf89b0: LoadField: r2 = r0->field_b
    //     0xaf89b0: ldur            w2, [x0, #0xb]
    // 0xaf89b4: DecompressPointer r2
    //     0xaf89b4: add             x2, x2, HEAP, lsl #32
    // 0xaf89b8: cmp             w2, NULL
    // 0xaf89bc: b.eq            #0xafa2f0
    // 0xaf89c0: LoadField: r3 = r2->field_f
    //     0xaf89c0: ldur            w3, [x2, #0xf]
    // 0xaf89c4: DecompressPointer r3
    //     0xaf89c4: add             x3, x3, HEAP, lsl #32
    // 0xaf89c8: r16 = "return_order_intermediate"
    //     0xaf89c8: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xaf89cc: ldr             x16, [x16, #0xb00]
    // 0xaf89d0: stp             x16, x3, [SP]
    // 0xaf89d4: r0 = ==()
    //     0xaf89d4: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf89d8: tbnz            w0, #4, #0xaf8a40
    // 0xaf89dc: ldur            x0, [fp, #-8]
    // 0xaf89e0: LoadField: r1 = r0->field_b
    //     0xaf89e0: ldur            w1, [x0, #0xb]
    // 0xaf89e4: DecompressPointer r1
    //     0xaf89e4: add             x1, x1, HEAP, lsl #32
    // 0xaf89e8: cmp             w1, NULL
    // 0xaf89ec: b.eq            #0xafa2f4
    // 0xaf89f0: LoadField: r2 = r1->field_23
    //     0xaf89f0: ldur            w2, [x1, #0x23]
    // 0xaf89f4: DecompressPointer r2
    //     0xaf89f4: add             x2, x2, HEAP, lsl #32
    // 0xaf89f8: cmp             w2, NULL
    // 0xaf89fc: b.ne            #0xaf8a08
    // 0xaf8a00: r1 = Null
    //     0xaf8a00: mov             x1, NULL
    // 0xaf8a04: b               #0xaf8a2c
    // 0xaf8a08: LoadField: r1 = r2->field_3f
    //     0xaf8a08: ldur            w1, [x2, #0x3f]
    // 0xaf8a0c: DecompressPointer r1
    //     0xaf8a0c: add             x1, x1, HEAP, lsl #32
    // 0xaf8a10: cmp             w1, NULL
    // 0xaf8a14: b.ne            #0xaf8a20
    // 0xaf8a18: r1 = Null
    //     0xaf8a18: mov             x1, NULL
    // 0xaf8a1c: b               #0xaf8a2c
    // 0xaf8a20: LoadField: r2 = r1->field_13
    //     0xaf8a20: ldur            w2, [x1, #0x13]
    // 0xaf8a24: DecompressPointer r2
    //     0xaf8a24: add             x2, x2, HEAP, lsl #32
    // 0xaf8a28: mov             x1, x2
    // 0xaf8a2c: cmp             w1, NULL
    // 0xaf8a30: b.ne            #0xaf8a38
    // 0xaf8a34: r1 = ""
    //     0xaf8a34: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8a38: mov             x6, x1
    // 0xaf8a3c: b               #0xaf8b30
    // 0xaf8a40: ldur            x0, [fp, #-8]
    // 0xaf8a44: LoadField: r1 = r0->field_b
    //     0xaf8a44: ldur            w1, [x0, #0xb]
    // 0xaf8a48: DecompressPointer r1
    //     0xaf8a48: add             x1, x1, HEAP, lsl #32
    // 0xaf8a4c: cmp             w1, NULL
    // 0xaf8a50: b.eq            #0xafa2f8
    // 0xaf8a54: LoadField: r2 = r1->field_f
    //     0xaf8a54: ldur            w2, [x1, #0xf]
    // 0xaf8a58: DecompressPointer r2
    //     0xaf8a58: add             x2, x2, HEAP, lsl #32
    // 0xaf8a5c: r16 = "cancel_order"
    //     0xaf8a5c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xaf8a60: ldr             x16, [x16, #0x98]
    // 0xaf8a64: stp             x16, x2, [SP]
    // 0xaf8a68: r0 = ==()
    //     0xaf8a68: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf8a6c: tbnz            w0, #4, #0xaf8ad0
    // 0xaf8a70: ldur            x0, [fp, #-8]
    // 0xaf8a74: LoadField: r1 = r0->field_b
    //     0xaf8a74: ldur            w1, [x0, #0xb]
    // 0xaf8a78: DecompressPointer r1
    //     0xaf8a78: add             x1, x1, HEAP, lsl #32
    // 0xaf8a7c: cmp             w1, NULL
    // 0xaf8a80: b.eq            #0xafa2fc
    // 0xaf8a84: LoadField: r2 = r1->field_1b
    //     0xaf8a84: ldur            w2, [x1, #0x1b]
    // 0xaf8a88: DecompressPointer r2
    //     0xaf8a88: add             x2, x2, HEAP, lsl #32
    // 0xaf8a8c: cmp             w2, NULL
    // 0xaf8a90: b.ne            #0xaf8a9c
    // 0xaf8a94: r1 = Null
    //     0xaf8a94: mov             x1, NULL
    // 0xaf8a98: b               #0xaf8ac0
    // 0xaf8a9c: LoadField: r1 = r2->field_7f
    //     0xaf8a9c: ldur            w1, [x2, #0x7f]
    // 0xaf8aa0: DecompressPointer r1
    //     0xaf8aa0: add             x1, x1, HEAP, lsl #32
    // 0xaf8aa4: cmp             w1, NULL
    // 0xaf8aa8: b.ne            #0xaf8ab4
    // 0xaf8aac: r1 = Null
    //     0xaf8aac: mov             x1, NULL
    // 0xaf8ab0: b               #0xaf8ac0
    // 0xaf8ab4: LoadField: r2 = r1->field_13
    //     0xaf8ab4: ldur            w2, [x1, #0x13]
    // 0xaf8ab8: DecompressPointer r2
    //     0xaf8ab8: add             x2, x2, HEAP, lsl #32
    // 0xaf8abc: mov             x1, x2
    // 0xaf8ac0: cmp             w1, NULL
    // 0xaf8ac4: b.ne            #0xaf8b2c
    // 0xaf8ac8: r1 = ""
    //     0xaf8ac8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8acc: b               #0xaf8b2c
    // 0xaf8ad0: ldur            x0, [fp, #-8]
    // 0xaf8ad4: LoadField: r1 = r0->field_b
    //     0xaf8ad4: ldur            w1, [x0, #0xb]
    // 0xaf8ad8: DecompressPointer r1
    //     0xaf8ad8: add             x1, x1, HEAP, lsl #32
    // 0xaf8adc: cmp             w1, NULL
    // 0xaf8ae0: b.eq            #0xafa300
    // 0xaf8ae4: LoadField: r2 = r1->field_1f
    //     0xaf8ae4: ldur            w2, [x1, #0x1f]
    // 0xaf8ae8: DecompressPointer r2
    //     0xaf8ae8: add             x2, x2, HEAP, lsl #32
    // 0xaf8aec: cmp             w2, NULL
    // 0xaf8af0: b.ne            #0xaf8afc
    // 0xaf8af4: r1 = Null
    //     0xaf8af4: mov             x1, NULL
    // 0xaf8af8: b               #0xaf8b20
    // 0xaf8afc: LoadField: r1 = r2->field_d7
    //     0xaf8afc: ldur            w1, [x2, #0xd7]
    // 0xaf8b00: DecompressPointer r1
    //     0xaf8b00: add             x1, x1, HEAP, lsl #32
    // 0xaf8b04: cmp             w1, NULL
    // 0xaf8b08: b.ne            #0xaf8b14
    // 0xaf8b0c: r1 = Null
    //     0xaf8b0c: mov             x1, NULL
    // 0xaf8b10: b               #0xaf8b20
    // 0xaf8b14: LoadField: r2 = r1->field_13
    //     0xaf8b14: ldur            w2, [x1, #0x13]
    // 0xaf8b18: DecompressPointer r2
    //     0xaf8b18: add             x2, x2, HEAP, lsl #32
    // 0xaf8b1c: mov             x1, x2
    // 0xaf8b20: cmp             w1, NULL
    // 0xaf8b24: b.ne            #0xaf8b2c
    // 0xaf8b28: r1 = ""
    //     0xaf8b28: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8b2c: mov             x6, x1
    // 0xaf8b30: ldur            x5, [fp, #-0x28]
    // 0xaf8b34: ldur            x4, [fp, #-0x48]
    // 0xaf8b38: ldur            x3, [fp, #-0x38]
    // 0xaf8b3c: ldur            x2, [fp, #-0x58]
    // 0xaf8b40: ldur            x1, [fp, #-0x10]
    // 0xaf8b44: stur            x6, [fp, #-0x50]
    // 0xaf8b48: r0 = of()
    //     0xaf8b48: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf8b4c: LoadField: r1 = r0->field_87
    //     0xaf8b4c: ldur            w1, [x0, #0x87]
    // 0xaf8b50: DecompressPointer r1
    //     0xaf8b50: add             x1, x1, HEAP, lsl #32
    // 0xaf8b54: LoadField: r0 = r1->field_2b
    //     0xaf8b54: ldur            w0, [x1, #0x2b]
    // 0xaf8b58: DecompressPointer r0
    //     0xaf8b58: add             x0, x0, HEAP, lsl #32
    // 0xaf8b5c: r16 = 12.000000
    //     0xaf8b5c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf8b60: ldr             x16, [x16, #0x9e8]
    // 0xaf8b64: r30 = Instance_TextDecoration
    //     0xaf8b64: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xaf8b68: ldr             lr, [lr, #0xe30]
    // 0xaf8b6c: stp             lr, x16, [SP]
    // 0xaf8b70: mov             x1, x0
    // 0xaf8b74: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xaf8b74: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xaf8b78: ldr             x4, [x4, #0x698]
    // 0xaf8b7c: r0 = copyWith()
    //     0xaf8b7c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf8b80: stur            x0, [fp, #-0x60]
    // 0xaf8b84: r0 = Text()
    //     0xaf8b84: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf8b88: mov             x3, x0
    // 0xaf8b8c: ldur            x0, [fp, #-0x50]
    // 0xaf8b90: stur            x3, [fp, #-0x68]
    // 0xaf8b94: StoreField: r3->field_b = r0
    //     0xaf8b94: stur            w0, [x3, #0xb]
    // 0xaf8b98: ldur            x0, [fp, #-0x60]
    // 0xaf8b9c: StoreField: r3->field_13 = r0
    //     0xaf8b9c: stur            w0, [x3, #0x13]
    // 0xaf8ba0: r1 = Null
    //     0xaf8ba0: mov             x1, NULL
    // 0xaf8ba4: r2 = 6
    //     0xaf8ba4: movz            x2, #0x6
    // 0xaf8ba8: r0 = AllocateArray()
    //     0xaf8ba8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf8bac: mov             x2, x0
    // 0xaf8bb0: ldur            x0, [fp, #-0x58]
    // 0xaf8bb4: stur            x2, [fp, #-0x50]
    // 0xaf8bb8: StoreField: r2->field_f = r0
    //     0xaf8bb8: stur            w0, [x2, #0xf]
    // 0xaf8bbc: r16 = Instance_SizedBox
    //     0xaf8bbc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xaf8bc0: ldr             x16, [x16, #0xa50]
    // 0xaf8bc4: StoreField: r2->field_13 = r16
    //     0xaf8bc4: stur            w16, [x2, #0x13]
    // 0xaf8bc8: ldur            x0, [fp, #-0x68]
    // 0xaf8bcc: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf8bcc: stur            w0, [x2, #0x17]
    // 0xaf8bd0: r1 = <Widget>
    //     0xaf8bd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf8bd4: r0 = AllocateGrowableArray()
    //     0xaf8bd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf8bd8: mov             x1, x0
    // 0xaf8bdc: ldur            x0, [fp, #-0x50]
    // 0xaf8be0: stur            x1, [fp, #-0x58]
    // 0xaf8be4: StoreField: r1->field_f = r0
    //     0xaf8be4: stur            w0, [x1, #0xf]
    // 0xaf8be8: r2 = 6
    //     0xaf8be8: movz            x2, #0x6
    // 0xaf8bec: StoreField: r1->field_b = r2
    //     0xaf8bec: stur            w2, [x1, #0xb]
    // 0xaf8bf0: r0 = Row()
    //     0xaf8bf0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf8bf4: mov             x3, x0
    // 0xaf8bf8: r0 = Instance_Axis
    //     0xaf8bf8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf8bfc: stur            x3, [fp, #-0x50]
    // 0xaf8c00: StoreField: r3->field_f = r0
    //     0xaf8c00: stur            w0, [x3, #0xf]
    // 0xaf8c04: r4 = Instance_MainAxisAlignment
    //     0xaf8c04: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf8c08: ldr             x4, [x4, #0xa08]
    // 0xaf8c0c: StoreField: r3->field_13 = r4
    //     0xaf8c0c: stur            w4, [x3, #0x13]
    // 0xaf8c10: r5 = Instance_MainAxisSize
    //     0xaf8c10: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf8c14: ldr             x5, [x5, #0xa10]
    // 0xaf8c18: ArrayStore: r3[0] = r5  ; List_4
    //     0xaf8c18: stur            w5, [x3, #0x17]
    // 0xaf8c1c: r6 = Instance_CrossAxisAlignment
    //     0xaf8c1c: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf8c20: ldr             x6, [x6, #0xa18]
    // 0xaf8c24: StoreField: r3->field_1b = r6
    //     0xaf8c24: stur            w6, [x3, #0x1b]
    // 0xaf8c28: r7 = Instance_VerticalDirection
    //     0xaf8c28: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf8c2c: ldr             x7, [x7, #0xa20]
    // 0xaf8c30: StoreField: r3->field_23 = r7
    //     0xaf8c30: stur            w7, [x3, #0x23]
    // 0xaf8c34: r8 = Instance_Clip
    //     0xaf8c34: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf8c38: ldr             x8, [x8, #0x38]
    // 0xaf8c3c: StoreField: r3->field_2b = r8
    //     0xaf8c3c: stur            w8, [x3, #0x2b]
    // 0xaf8c40: StoreField: r3->field_2f = rZR
    //     0xaf8c40: stur            xzr, [x3, #0x2f]
    // 0xaf8c44: ldur            x1, [fp, #-0x58]
    // 0xaf8c48: StoreField: r3->field_b = r1
    //     0xaf8c48: stur            w1, [x3, #0xb]
    // 0xaf8c4c: r1 = Null
    //     0xaf8c4c: mov             x1, NULL
    // 0xaf8c50: r2 = 6
    //     0xaf8c50: movz            x2, #0x6
    // 0xaf8c54: r0 = AllocateArray()
    //     0xaf8c54: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf8c58: mov             x2, x0
    // 0xaf8c5c: ldur            x0, [fp, #-0x38]
    // 0xaf8c60: stur            x2, [fp, #-0x58]
    // 0xaf8c64: StoreField: r2->field_f = r0
    //     0xaf8c64: stur            w0, [x2, #0xf]
    // 0xaf8c68: r16 = Instance_SizedBox
    //     0xaf8c68: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xaf8c6c: ldr             x16, [x16, #0xc70]
    // 0xaf8c70: StoreField: r2->field_13 = r16
    //     0xaf8c70: stur            w16, [x2, #0x13]
    // 0xaf8c74: ldur            x0, [fp, #-0x50]
    // 0xaf8c78: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf8c78: stur            w0, [x2, #0x17]
    // 0xaf8c7c: r1 = <Widget>
    //     0xaf8c7c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf8c80: r0 = AllocateGrowableArray()
    //     0xaf8c80: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf8c84: mov             x1, x0
    // 0xaf8c88: ldur            x0, [fp, #-0x58]
    // 0xaf8c8c: stur            x1, [fp, #-0x38]
    // 0xaf8c90: StoreField: r1->field_f = r0
    //     0xaf8c90: stur            w0, [x1, #0xf]
    // 0xaf8c94: r2 = 6
    //     0xaf8c94: movz            x2, #0x6
    // 0xaf8c98: StoreField: r1->field_b = r2
    //     0xaf8c98: stur            w2, [x1, #0xb]
    // 0xaf8c9c: r0 = Column()
    //     0xaf8c9c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf8ca0: mov             x1, x0
    // 0xaf8ca4: r0 = Instance_Axis
    //     0xaf8ca4: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf8ca8: stur            x1, [fp, #-0x50]
    // 0xaf8cac: StoreField: r1->field_f = r0
    //     0xaf8cac: stur            w0, [x1, #0xf]
    // 0xaf8cb0: r2 = Instance_MainAxisAlignment
    //     0xaf8cb0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf8cb4: ldr             x2, [x2, #0xa08]
    // 0xaf8cb8: StoreField: r1->field_13 = r2
    //     0xaf8cb8: stur            w2, [x1, #0x13]
    // 0xaf8cbc: r3 = Instance_MainAxisSize
    //     0xaf8cbc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf8cc0: ldr             x3, [x3, #0xa10]
    // 0xaf8cc4: ArrayStore: r1[0] = r3  ; List_4
    //     0xaf8cc4: stur            w3, [x1, #0x17]
    // 0xaf8cc8: r4 = Instance_CrossAxisAlignment
    //     0xaf8cc8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaf8ccc: ldr             x4, [x4, #0x890]
    // 0xaf8cd0: StoreField: r1->field_1b = r4
    //     0xaf8cd0: stur            w4, [x1, #0x1b]
    // 0xaf8cd4: r5 = Instance_VerticalDirection
    //     0xaf8cd4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf8cd8: ldr             x5, [x5, #0xa20]
    // 0xaf8cdc: StoreField: r1->field_23 = r5
    //     0xaf8cdc: stur            w5, [x1, #0x23]
    // 0xaf8ce0: r6 = Instance_Clip
    //     0xaf8ce0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf8ce4: ldr             x6, [x6, #0x38]
    // 0xaf8ce8: StoreField: r1->field_2b = r6
    //     0xaf8ce8: stur            w6, [x1, #0x2b]
    // 0xaf8cec: StoreField: r1->field_2f = rZR
    //     0xaf8cec: stur            xzr, [x1, #0x2f]
    // 0xaf8cf0: ldur            x7, [fp, #-0x38]
    // 0xaf8cf4: StoreField: r1->field_b = r7
    //     0xaf8cf4: stur            w7, [x1, #0xb]
    // 0xaf8cf8: r0 = Padding()
    //     0xaf8cf8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf8cfc: mov             x2, x0
    // 0xaf8d00: r0 = Instance_EdgeInsets
    //     0xaf8d00: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xaf8d04: ldr             x0, [x0, #0xa78]
    // 0xaf8d08: stur            x2, [fp, #-0x38]
    // 0xaf8d0c: StoreField: r2->field_f = r0
    //     0xaf8d0c: stur            w0, [x2, #0xf]
    // 0xaf8d10: ldur            x0, [fp, #-0x50]
    // 0xaf8d14: StoreField: r2->field_b = r0
    //     0xaf8d14: stur            w0, [x2, #0xb]
    // 0xaf8d18: r1 = <FlexParentData>
    //     0xaf8d18: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaf8d1c: ldr             x1, [x1, #0xe00]
    // 0xaf8d20: r0 = Expanded()
    //     0xaf8d20: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf8d24: mov             x3, x0
    // 0xaf8d28: r0 = 1
    //     0xaf8d28: movz            x0, #0x1
    // 0xaf8d2c: stur            x3, [fp, #-0x50]
    // 0xaf8d30: StoreField: r3->field_13 = r0
    //     0xaf8d30: stur            x0, [x3, #0x13]
    // 0xaf8d34: r4 = Instance_FlexFit
    //     0xaf8d34: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaf8d38: ldr             x4, [x4, #0xe08]
    // 0xaf8d3c: StoreField: r3->field_1b = r4
    //     0xaf8d3c: stur            w4, [x3, #0x1b]
    // 0xaf8d40: ldur            x1, [fp, #-0x38]
    // 0xaf8d44: StoreField: r3->field_b = r1
    //     0xaf8d44: stur            w1, [x3, #0xb]
    // 0xaf8d48: r1 = Null
    //     0xaf8d48: mov             x1, NULL
    // 0xaf8d4c: r2 = 6
    //     0xaf8d4c: movz            x2, #0x6
    // 0xaf8d50: r0 = AllocateArray()
    //     0xaf8d50: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf8d54: mov             x2, x0
    // 0xaf8d58: ldur            x0, [fp, #-0x28]
    // 0xaf8d5c: stur            x2, [fp, #-0x38]
    // 0xaf8d60: StoreField: r2->field_f = r0
    //     0xaf8d60: stur            w0, [x2, #0xf]
    // 0xaf8d64: ldur            x0, [fp, #-0x48]
    // 0xaf8d68: StoreField: r2->field_13 = r0
    //     0xaf8d68: stur            w0, [x2, #0x13]
    // 0xaf8d6c: ldur            x0, [fp, #-0x50]
    // 0xaf8d70: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf8d70: stur            w0, [x2, #0x17]
    // 0xaf8d74: r1 = <Widget>
    //     0xaf8d74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf8d78: r0 = AllocateGrowableArray()
    //     0xaf8d78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf8d7c: mov             x1, x0
    // 0xaf8d80: ldur            x0, [fp, #-0x38]
    // 0xaf8d84: stur            x1, [fp, #-0x28]
    // 0xaf8d88: StoreField: r1->field_f = r0
    //     0xaf8d88: stur            w0, [x1, #0xf]
    // 0xaf8d8c: r2 = 6
    //     0xaf8d8c: movz            x2, #0x6
    // 0xaf8d90: StoreField: r1->field_b = r2
    //     0xaf8d90: stur            w2, [x1, #0xb]
    // 0xaf8d94: r0 = Row()
    //     0xaf8d94: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf8d98: mov             x1, x0
    // 0xaf8d9c: r0 = Instance_Axis
    //     0xaf8d9c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf8da0: stur            x1, [fp, #-0x38]
    // 0xaf8da4: StoreField: r1->field_f = r0
    //     0xaf8da4: stur            w0, [x1, #0xf]
    // 0xaf8da8: r2 = Instance_MainAxisAlignment
    //     0xaf8da8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf8dac: ldr             x2, [x2, #0xa08]
    // 0xaf8db0: StoreField: r1->field_13 = r2
    //     0xaf8db0: stur            w2, [x1, #0x13]
    // 0xaf8db4: r3 = Instance_MainAxisSize
    //     0xaf8db4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf8db8: ldr             x3, [x3, #0xa10]
    // 0xaf8dbc: ArrayStore: r1[0] = r3  ; List_4
    //     0xaf8dbc: stur            w3, [x1, #0x17]
    // 0xaf8dc0: r4 = Instance_CrossAxisAlignment
    //     0xaf8dc0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf8dc4: ldr             x4, [x4, #0xa18]
    // 0xaf8dc8: StoreField: r1->field_1b = r4
    //     0xaf8dc8: stur            w4, [x1, #0x1b]
    // 0xaf8dcc: r5 = Instance_VerticalDirection
    //     0xaf8dcc: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf8dd0: ldr             x5, [x5, #0xa20]
    // 0xaf8dd4: StoreField: r1->field_23 = r5
    //     0xaf8dd4: stur            w5, [x1, #0x23]
    // 0xaf8dd8: r6 = Instance_Clip
    //     0xaf8dd8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf8ddc: ldr             x6, [x6, #0x38]
    // 0xaf8de0: StoreField: r1->field_2b = r6
    //     0xaf8de0: stur            w6, [x1, #0x2b]
    // 0xaf8de4: StoreField: r1->field_2f = rZR
    //     0xaf8de4: stur            xzr, [x1, #0x2f]
    // 0xaf8de8: ldur            x7, [fp, #-0x28]
    // 0xaf8dec: StoreField: r1->field_b = r7
    //     0xaf8dec: stur            w7, [x1, #0xb]
    // 0xaf8df0: r0 = Container()
    //     0xaf8df0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf8df4: stur            x0, [fp, #-0x28]
    // 0xaf8df8: ldur            x16, [fp, #-0x40]
    // 0xaf8dfc: ldur            lr, [fp, #-0x38]
    // 0xaf8e00: stp             lr, x16, [SP]
    // 0xaf8e04: mov             x1, x0
    // 0xaf8e08: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xaf8e08: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xaf8e0c: ldr             x4, [x4, #0x88]
    // 0xaf8e10: r0 = Container()
    //     0xaf8e10: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf8e14: r0 = Padding()
    //     0xaf8e14: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf8e18: mov             x1, x0
    // 0xaf8e1c: r0 = Instance_EdgeInsets
    //     0xaf8e1c: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xaf8e20: ldr             x0, [x0, #0xb00]
    // 0xaf8e24: stur            x1, [fp, #-0x38]
    // 0xaf8e28: StoreField: r1->field_f = r0
    //     0xaf8e28: stur            w0, [x1, #0xf]
    // 0xaf8e2c: ldur            x0, [fp, #-0x28]
    // 0xaf8e30: StoreField: r1->field_b = r0
    //     0xaf8e30: stur            w0, [x1, #0xb]
    // 0xaf8e34: r0 = Radius()
    //     0xaf8e34: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf8e38: d0 = 12.000000
    //     0xaf8e38: fmov            d0, #12.00000000
    // 0xaf8e3c: stur            x0, [fp, #-0x28]
    // 0xaf8e40: StoreField: r0->field_7 = d0
    //     0xaf8e40: stur            d0, [x0, #7]
    // 0xaf8e44: StoreField: r0->field_f = d0
    //     0xaf8e44: stur            d0, [x0, #0xf]
    // 0xaf8e48: r0 = BorderRadius()
    //     0xaf8e48: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf8e4c: mov             x1, x0
    // 0xaf8e50: ldur            x0, [fp, #-0x28]
    // 0xaf8e54: stur            x1, [fp, #-0x40]
    // 0xaf8e58: StoreField: r1->field_7 = r0
    //     0xaf8e58: stur            w0, [x1, #7]
    // 0xaf8e5c: StoreField: r1->field_b = r0
    //     0xaf8e5c: stur            w0, [x1, #0xb]
    // 0xaf8e60: StoreField: r1->field_f = r0
    //     0xaf8e60: stur            w0, [x1, #0xf]
    // 0xaf8e64: StoreField: r1->field_13 = r0
    //     0xaf8e64: stur            w0, [x1, #0x13]
    // 0xaf8e68: r0 = BoxDecoration()
    //     0xaf8e68: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xaf8e6c: mov             x1, x0
    // 0xaf8e70: r0 = Instance_Color
    //     0xaf8e70: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf8e74: stur            x1, [fp, #-0x28]
    // 0xaf8e78: StoreField: r1->field_7 = r0
    //     0xaf8e78: stur            w0, [x1, #7]
    // 0xaf8e7c: ldur            x0, [fp, #-0x40]
    // 0xaf8e80: StoreField: r1->field_13 = r0
    //     0xaf8e80: stur            w0, [x1, #0x13]
    // 0xaf8e84: r0 = Instance_BoxShape
    //     0xaf8e84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xaf8e88: ldr             x0, [x0, #0x80]
    // 0xaf8e8c: StoreField: r1->field_23 = r0
    //     0xaf8e8c: stur            w0, [x1, #0x23]
    // 0xaf8e90: ldur            x0, [fp, #-8]
    // 0xaf8e94: LoadField: r2 = r0->field_b
    //     0xaf8e94: ldur            w2, [x0, #0xb]
    // 0xaf8e98: DecompressPointer r2
    //     0xaf8e98: add             x2, x2, HEAP, lsl #32
    // 0xaf8e9c: cmp             w2, NULL
    // 0xaf8ea0: b.eq            #0xafa304
    // 0xaf8ea4: LoadField: r3 = r2->field_f
    //     0xaf8ea4: ldur            w3, [x2, #0xf]
    // 0xaf8ea8: DecompressPointer r3
    //     0xaf8ea8: add             x3, x3, HEAP, lsl #32
    // 0xaf8eac: r16 = "return_order_intermediate"
    //     0xaf8eac: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xaf8eb0: ldr             x16, [x16, #0xb00]
    // 0xaf8eb4: stp             x16, x3, [SP]
    // 0xaf8eb8: r0 = ==()
    //     0xaf8eb8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf8ebc: tbnz            w0, #4, #0xaf8f24
    // 0xaf8ec0: ldur            x0, [fp, #-8]
    // 0xaf8ec4: LoadField: r1 = r0->field_b
    //     0xaf8ec4: ldur            w1, [x0, #0xb]
    // 0xaf8ec8: DecompressPointer r1
    //     0xaf8ec8: add             x1, x1, HEAP, lsl #32
    // 0xaf8ecc: cmp             w1, NULL
    // 0xaf8ed0: b.eq            #0xafa308
    // 0xaf8ed4: LoadField: r2 = r1->field_23
    //     0xaf8ed4: ldur            w2, [x1, #0x23]
    // 0xaf8ed8: DecompressPointer r2
    //     0xaf8ed8: add             x2, x2, HEAP, lsl #32
    // 0xaf8edc: cmp             w2, NULL
    // 0xaf8ee0: b.ne            #0xaf8eec
    // 0xaf8ee4: r1 = Null
    //     0xaf8ee4: mov             x1, NULL
    // 0xaf8ee8: b               #0xaf8f10
    // 0xaf8eec: LoadField: r1 = r2->field_37
    //     0xaf8eec: ldur            w1, [x2, #0x37]
    // 0xaf8ef0: DecompressPointer r1
    //     0xaf8ef0: add             x1, x1, HEAP, lsl #32
    // 0xaf8ef4: cmp             w1, NULL
    // 0xaf8ef8: b.ne            #0xaf8f04
    // 0xaf8efc: r1 = Null
    //     0xaf8efc: mov             x1, NULL
    // 0xaf8f00: b               #0xaf8f10
    // 0xaf8f04: LoadField: r2 = r1->field_7
    //     0xaf8f04: ldur            w2, [x1, #7]
    // 0xaf8f08: DecompressPointer r2
    //     0xaf8f08: add             x2, x2, HEAP, lsl #32
    // 0xaf8f0c: mov             x1, x2
    // 0xaf8f10: cmp             w1, NULL
    // 0xaf8f14: b.ne            #0xaf8f1c
    // 0xaf8f18: r1 = ""
    //     0xaf8f18: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8f1c: mov             x3, x1
    // 0xaf8f20: b               #0xaf8fdc
    // 0xaf8f24: ldur            x0, [fp, #-8]
    // 0xaf8f28: LoadField: r1 = r0->field_b
    //     0xaf8f28: ldur            w1, [x0, #0xb]
    // 0xaf8f2c: DecompressPointer r1
    //     0xaf8f2c: add             x1, x1, HEAP, lsl #32
    // 0xaf8f30: cmp             w1, NULL
    // 0xaf8f34: b.eq            #0xafa30c
    // 0xaf8f38: LoadField: r2 = r1->field_f
    //     0xaf8f38: ldur            w2, [x1, #0xf]
    // 0xaf8f3c: DecompressPointer r2
    //     0xaf8f3c: add             x2, x2, HEAP, lsl #32
    // 0xaf8f40: r16 = "cancel_order"
    //     0xaf8f40: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xaf8f44: ldr             x16, [x16, #0x98]
    // 0xaf8f48: stp             x16, x2, [SP]
    // 0xaf8f4c: r0 = ==()
    //     0xaf8f4c: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf8f50: tbnz            w0, #4, #0xaf8f98
    // 0xaf8f54: ldur            x0, [fp, #-8]
    // 0xaf8f58: LoadField: r1 = r0->field_b
    //     0xaf8f58: ldur            w1, [x0, #0xb]
    // 0xaf8f5c: DecompressPointer r1
    //     0xaf8f5c: add             x1, x1, HEAP, lsl #32
    // 0xaf8f60: cmp             w1, NULL
    // 0xaf8f64: b.eq            #0xafa310
    // 0xaf8f68: LoadField: r2 = r1->field_1b
    //     0xaf8f68: ldur            w2, [x1, #0x1b]
    // 0xaf8f6c: DecompressPointer r2
    //     0xaf8f6c: add             x2, x2, HEAP, lsl #32
    // 0xaf8f70: cmp             w2, NULL
    // 0xaf8f74: b.ne            #0xaf8f80
    // 0xaf8f78: r1 = Null
    //     0xaf8f78: mov             x1, NULL
    // 0xaf8f7c: b               #0xaf8f88
    // 0xaf8f80: LoadField: r1 = r2->field_b
    //     0xaf8f80: ldur            w1, [x2, #0xb]
    // 0xaf8f84: DecompressPointer r1
    //     0xaf8f84: add             x1, x1, HEAP, lsl #32
    // 0xaf8f88: cmp             w1, NULL
    // 0xaf8f8c: b.ne            #0xaf8fd8
    // 0xaf8f90: r1 = ""
    //     0xaf8f90: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8f94: b               #0xaf8fd8
    // 0xaf8f98: ldur            x0, [fp, #-8]
    // 0xaf8f9c: LoadField: r1 = r0->field_b
    //     0xaf8f9c: ldur            w1, [x0, #0xb]
    // 0xaf8fa0: DecompressPointer r1
    //     0xaf8fa0: add             x1, x1, HEAP, lsl #32
    // 0xaf8fa4: cmp             w1, NULL
    // 0xaf8fa8: b.eq            #0xafa314
    // 0xaf8fac: LoadField: r2 = r1->field_1f
    //     0xaf8fac: ldur            w2, [x1, #0x1f]
    // 0xaf8fb0: DecompressPointer r2
    //     0xaf8fb0: add             x2, x2, HEAP, lsl #32
    // 0xaf8fb4: cmp             w2, NULL
    // 0xaf8fb8: b.ne            #0xaf8fc4
    // 0xaf8fbc: r1 = Null
    //     0xaf8fbc: mov             x1, NULL
    // 0xaf8fc0: b               #0xaf8fcc
    // 0xaf8fc4: LoadField: r1 = r2->field_1b
    //     0xaf8fc4: ldur            w1, [x2, #0x1b]
    // 0xaf8fc8: DecompressPointer r1
    //     0xaf8fc8: add             x1, x1, HEAP, lsl #32
    // 0xaf8fcc: cmp             w1, NULL
    // 0xaf8fd0: b.ne            #0xaf8fd8
    // 0xaf8fd4: r1 = ""
    //     0xaf8fd4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf8fd8: mov             x3, x1
    // 0xaf8fdc: stur            x3, [fp, #-0x40]
    // 0xaf8fe0: r1 = Function '<anonymous closure>':.
    //     0xaf8fe0: add             x1, PP, #0x58, lsl #12  ; [pp+0x58408] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf8fe4: ldr             x1, [x1, #0x408]
    // 0xaf8fe8: r2 = Null
    //     0xaf8fe8: mov             x2, NULL
    // 0xaf8fec: r0 = AllocateClosure()
    //     0xaf8fec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf8ff0: r1 = Function '<anonymous closure>':.
    //     0xaf8ff0: add             x1, PP, #0x58, lsl #12  ; [pp+0x58410] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xaf8ff4: ldr             x1, [x1, #0x410]
    // 0xaf8ff8: r2 = Null
    //     0xaf8ff8: mov             x2, NULL
    // 0xaf8ffc: stur            x0, [fp, #-0x48]
    // 0xaf9000: r0 = AllocateClosure()
    //     0xaf9000: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf9004: stur            x0, [fp, #-0x50]
    // 0xaf9008: r0 = CachedNetworkImage()
    //     0xaf9008: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xaf900c: stur            x0, [fp, #-0x58]
    // 0xaf9010: r16 = 56.000000
    //     0xaf9010: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xaf9014: ldr             x16, [x16, #0xb78]
    // 0xaf9018: r30 = 56.000000
    //     0xaf9018: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xaf901c: ldr             lr, [lr, #0xb78]
    // 0xaf9020: stp             lr, x16, [SP, #0x18]
    // 0xaf9024: r16 = Instance_BoxFit
    //     0xaf9024: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xaf9028: ldr             x16, [x16, #0x118]
    // 0xaf902c: ldur            lr, [fp, #-0x48]
    // 0xaf9030: stp             lr, x16, [SP, #8]
    // 0xaf9034: ldur            x16, [fp, #-0x50]
    // 0xaf9038: str             x16, [SP]
    // 0xaf903c: mov             x1, x0
    // 0xaf9040: ldur            x2, [fp, #-0x40]
    // 0xaf9044: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0xaf9044: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0xaf9048: ldr             x4, [x4, #0xc28]
    // 0xaf904c: r0 = CachedNetworkImage()
    //     0xaf904c: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xaf9050: ldur            x0, [fp, #-8]
    // 0xaf9054: LoadField: r1 = r0->field_b
    //     0xaf9054: ldur            w1, [x0, #0xb]
    // 0xaf9058: DecompressPointer r1
    //     0xaf9058: add             x1, x1, HEAP, lsl #32
    // 0xaf905c: cmp             w1, NULL
    // 0xaf9060: b.eq            #0xafa318
    // 0xaf9064: LoadField: r2 = r1->field_f
    //     0xaf9064: ldur            w2, [x1, #0xf]
    // 0xaf9068: DecompressPointer r2
    //     0xaf9068: add             x2, x2, HEAP, lsl #32
    // 0xaf906c: r16 = "return_order_intermediate"
    //     0xaf906c: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xaf9070: ldr             x16, [x16, #0xb00]
    // 0xaf9074: stp             x16, x2, [SP]
    // 0xaf9078: r0 = ==()
    //     0xaf9078: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf907c: tbnz            w0, #4, #0xaf90e4
    // 0xaf9080: ldur            x0, [fp, #-8]
    // 0xaf9084: LoadField: r1 = r0->field_b
    //     0xaf9084: ldur            w1, [x0, #0xb]
    // 0xaf9088: DecompressPointer r1
    //     0xaf9088: add             x1, x1, HEAP, lsl #32
    // 0xaf908c: cmp             w1, NULL
    // 0xaf9090: b.eq            #0xafa31c
    // 0xaf9094: LoadField: r2 = r1->field_23
    //     0xaf9094: ldur            w2, [x1, #0x23]
    // 0xaf9098: DecompressPointer r2
    //     0xaf9098: add             x2, x2, HEAP, lsl #32
    // 0xaf909c: cmp             w2, NULL
    // 0xaf90a0: b.ne            #0xaf90ac
    // 0xaf90a4: r1 = Null
    //     0xaf90a4: mov             x1, NULL
    // 0xaf90a8: b               #0xaf90d0
    // 0xaf90ac: LoadField: r1 = r2->field_37
    //     0xaf90ac: ldur            w1, [x2, #0x37]
    // 0xaf90b0: DecompressPointer r1
    //     0xaf90b0: add             x1, x1, HEAP, lsl #32
    // 0xaf90b4: cmp             w1, NULL
    // 0xaf90b8: b.ne            #0xaf90c4
    // 0xaf90bc: r1 = Null
    //     0xaf90bc: mov             x1, NULL
    // 0xaf90c0: b               #0xaf90d0
    // 0xaf90c4: LoadField: r2 = r1->field_b
    //     0xaf90c4: ldur            w2, [x1, #0xb]
    // 0xaf90c8: DecompressPointer r2
    //     0xaf90c8: add             x2, x2, HEAP, lsl #32
    // 0xaf90cc: mov             x1, x2
    // 0xaf90d0: cmp             w1, NULL
    // 0xaf90d4: b.ne            #0xaf90dc
    // 0xaf90d8: r1 = ""
    //     0xaf90d8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf90dc: mov             x2, x1
    // 0xaf90e0: b               #0xaf919c
    // 0xaf90e4: ldur            x0, [fp, #-8]
    // 0xaf90e8: LoadField: r1 = r0->field_b
    //     0xaf90e8: ldur            w1, [x0, #0xb]
    // 0xaf90ec: DecompressPointer r1
    //     0xaf90ec: add             x1, x1, HEAP, lsl #32
    // 0xaf90f0: cmp             w1, NULL
    // 0xaf90f4: b.eq            #0xafa320
    // 0xaf90f8: LoadField: r2 = r1->field_f
    //     0xaf90f8: ldur            w2, [x1, #0xf]
    // 0xaf90fc: DecompressPointer r2
    //     0xaf90fc: add             x2, x2, HEAP, lsl #32
    // 0xaf9100: r16 = "cancel_order"
    //     0xaf9100: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xaf9104: ldr             x16, [x16, #0x98]
    // 0xaf9108: stp             x16, x2, [SP]
    // 0xaf910c: r0 = ==()
    //     0xaf910c: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf9110: tbnz            w0, #4, #0xaf9158
    // 0xaf9114: ldur            x0, [fp, #-8]
    // 0xaf9118: LoadField: r1 = r0->field_b
    //     0xaf9118: ldur            w1, [x0, #0xb]
    // 0xaf911c: DecompressPointer r1
    //     0xaf911c: add             x1, x1, HEAP, lsl #32
    // 0xaf9120: cmp             w1, NULL
    // 0xaf9124: b.eq            #0xafa324
    // 0xaf9128: LoadField: r2 = r1->field_1b
    //     0xaf9128: ldur            w2, [x1, #0x1b]
    // 0xaf912c: DecompressPointer r2
    //     0xaf912c: add             x2, x2, HEAP, lsl #32
    // 0xaf9130: cmp             w2, NULL
    // 0xaf9134: b.ne            #0xaf9140
    // 0xaf9138: r1 = Null
    //     0xaf9138: mov             x1, NULL
    // 0xaf913c: b               #0xaf9148
    // 0xaf9140: LoadField: r1 = r2->field_f
    //     0xaf9140: ldur            w1, [x2, #0xf]
    // 0xaf9144: DecompressPointer r1
    //     0xaf9144: add             x1, x1, HEAP, lsl #32
    // 0xaf9148: cmp             w1, NULL
    // 0xaf914c: b.ne            #0xaf9198
    // 0xaf9150: r1 = ""
    //     0xaf9150: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf9154: b               #0xaf9198
    // 0xaf9158: ldur            x0, [fp, #-8]
    // 0xaf915c: LoadField: r1 = r0->field_b
    //     0xaf915c: ldur            w1, [x0, #0xb]
    // 0xaf9160: DecompressPointer r1
    //     0xaf9160: add             x1, x1, HEAP, lsl #32
    // 0xaf9164: cmp             w1, NULL
    // 0xaf9168: b.eq            #0xafa328
    // 0xaf916c: LoadField: r2 = r1->field_1f
    //     0xaf916c: ldur            w2, [x1, #0x1f]
    // 0xaf9170: DecompressPointer r2
    //     0xaf9170: add             x2, x2, HEAP, lsl #32
    // 0xaf9174: cmp             w2, NULL
    // 0xaf9178: b.ne            #0xaf9184
    // 0xaf917c: r1 = Null
    //     0xaf917c: mov             x1, NULL
    // 0xaf9180: b               #0xaf918c
    // 0xaf9184: LoadField: r1 = r2->field_b
    //     0xaf9184: ldur            w1, [x2, #0xb]
    // 0xaf9188: DecompressPointer r1
    //     0xaf9188: add             x1, x1, HEAP, lsl #32
    // 0xaf918c: cmp             w1, NULL
    // 0xaf9190: b.ne            #0xaf9198
    // 0xaf9194: r1 = ""
    //     0xaf9194: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf9198: mov             x2, x1
    // 0xaf919c: ldur            x1, [fp, #-0x10]
    // 0xaf91a0: stur            x2, [fp, #-0x40]
    // 0xaf91a4: r0 = of()
    //     0xaf91a4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf91a8: LoadField: r1 = r0->field_87
    //     0xaf91a8: ldur            w1, [x0, #0x87]
    // 0xaf91ac: DecompressPointer r1
    //     0xaf91ac: add             x1, x1, HEAP, lsl #32
    // 0xaf91b0: LoadField: r0 = r1->field_7
    //     0xaf91b0: ldur            w0, [x1, #7]
    // 0xaf91b4: DecompressPointer r0
    //     0xaf91b4: add             x0, x0, HEAP, lsl #32
    // 0xaf91b8: r16 = Instance_Color
    //     0xaf91b8: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf91bc: r30 = 12.000000
    //     0xaf91bc: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf91c0: ldr             lr, [lr, #0x9e8]
    // 0xaf91c4: stp             lr, x16, [SP]
    // 0xaf91c8: mov             x1, x0
    // 0xaf91cc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xaf91cc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xaf91d0: ldr             x4, [x4, #0x9b8]
    // 0xaf91d4: r0 = copyWith()
    //     0xaf91d4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf91d8: stur            x0, [fp, #-0x48]
    // 0xaf91dc: r0 = Text()
    //     0xaf91dc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf91e0: mov             x1, x0
    // 0xaf91e4: ldur            x0, [fp, #-0x40]
    // 0xaf91e8: stur            x1, [fp, #-0x50]
    // 0xaf91ec: StoreField: r1->field_b = r0
    //     0xaf91ec: stur            w0, [x1, #0xb]
    // 0xaf91f0: ldur            x0, [fp, #-0x48]
    // 0xaf91f4: StoreField: r1->field_13 = r0
    //     0xaf91f4: stur            w0, [x1, #0x13]
    // 0xaf91f8: r0 = Instance_TextOverflow
    //     0xaf91f8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xaf91fc: ldr             x0, [x0, #0xe10]
    // 0xaf9200: StoreField: r1->field_2b = r0
    //     0xaf9200: stur            w0, [x1, #0x2b]
    // 0xaf9204: r0 = 4
    //     0xaf9204: movz            x0, #0x4
    // 0xaf9208: StoreField: r1->field_37 = r0
    //     0xaf9208: stur            w0, [x1, #0x37]
    // 0xaf920c: ldur            x0, [fp, #-8]
    // 0xaf9210: LoadField: r2 = r0->field_b
    //     0xaf9210: ldur            w2, [x0, #0xb]
    // 0xaf9214: DecompressPointer r2
    //     0xaf9214: add             x2, x2, HEAP, lsl #32
    // 0xaf9218: cmp             w2, NULL
    // 0xaf921c: b.eq            #0xafa32c
    // 0xaf9220: LoadField: r3 = r2->field_f
    //     0xaf9220: ldur            w3, [x2, #0xf]
    // 0xaf9224: DecompressPointer r3
    //     0xaf9224: add             x3, x3, HEAP, lsl #32
    // 0xaf9228: r16 = "return_order_intermediate"
    //     0xaf9228: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xaf922c: ldr             x16, [x16, #0xb00]
    // 0xaf9230: stp             x16, x3, [SP]
    // 0xaf9234: r0 = ==()
    //     0xaf9234: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf9238: tbnz            w0, #4, #0xaf9450
    // 0xaf923c: ldur            x1, [fp, #-8]
    // 0xaf9240: LoadField: r0 = r1->field_b
    //     0xaf9240: ldur            w0, [x1, #0xb]
    // 0xaf9244: DecompressPointer r0
    //     0xaf9244: add             x0, x0, HEAP, lsl #32
    // 0xaf9248: cmp             w0, NULL
    // 0xaf924c: b.eq            #0xafa330
    // 0xaf9250: LoadField: r2 = r0->field_23
    //     0xaf9250: ldur            w2, [x0, #0x23]
    // 0xaf9254: DecompressPointer r2
    //     0xaf9254: add             x2, x2, HEAP, lsl #32
    // 0xaf9258: cmp             w2, NULL
    // 0xaf925c: b.ne            #0xaf9268
    // 0xaf9260: r0 = Null
    //     0xaf9260: mov             x0, NULL
    // 0xaf9264: b               #0xaf928c
    // 0xaf9268: LoadField: r0 = r2->field_37
    //     0xaf9268: ldur            w0, [x2, #0x37]
    // 0xaf926c: DecompressPointer r0
    //     0xaf926c: add             x0, x0, HEAP, lsl #32
    // 0xaf9270: cmp             w0, NULL
    // 0xaf9274: b.ne            #0xaf9280
    // 0xaf9278: r0 = Null
    //     0xaf9278: mov             x0, NULL
    // 0xaf927c: b               #0xaf928c
    // 0xaf9280: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xaf9280: ldur            w2, [x0, #0x17]
    // 0xaf9284: DecompressPointer r2
    //     0xaf9284: add             x2, x2, HEAP, lsl #32
    // 0xaf9288: mov             x0, x2
    // 0xaf928c: r2 = LoadClassIdInstr(r0)
    //     0xaf928c: ldur            x2, [x0, #-1]
    //     0xaf9290: ubfx            x2, x2, #0xc, #0x14
    // 0xaf9294: r16 = "size"
    //     0xaf9294: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xaf9298: ldr             x16, [x16, #0x9c0]
    // 0xaf929c: stp             x16, x0, [SP]
    // 0xaf92a0: mov             x0, x2
    // 0xaf92a4: mov             lr, x0
    // 0xaf92a8: ldr             lr, [x21, lr, lsl #3]
    // 0xaf92ac: blr             lr
    // 0xaf92b0: tbnz            w0, #4, #0xaf9380
    // 0xaf92b4: ldur            x0, [fp, #-8]
    // 0xaf92b8: r1 = Null
    //     0xaf92b8: mov             x1, NULL
    // 0xaf92bc: r2 = 8
    //     0xaf92bc: movz            x2, #0x8
    // 0xaf92c0: r0 = AllocateArray()
    //     0xaf92c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf92c4: r16 = "Size: "
    //     0xaf92c4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xaf92c8: ldr             x16, [x16, #0xf00]
    // 0xaf92cc: StoreField: r0->field_f = r16
    //     0xaf92cc: stur            w16, [x0, #0xf]
    // 0xaf92d0: ldur            x1, [fp, #-8]
    // 0xaf92d4: LoadField: r2 = r1->field_b
    //     0xaf92d4: ldur            w2, [x1, #0xb]
    // 0xaf92d8: DecompressPointer r2
    //     0xaf92d8: add             x2, x2, HEAP, lsl #32
    // 0xaf92dc: cmp             w2, NULL
    // 0xaf92e0: b.eq            #0xafa334
    // 0xaf92e4: LoadField: r3 = r2->field_23
    //     0xaf92e4: ldur            w3, [x2, #0x23]
    // 0xaf92e8: DecompressPointer r3
    //     0xaf92e8: add             x3, x3, HEAP, lsl #32
    // 0xaf92ec: cmp             w3, NULL
    // 0xaf92f0: b.ne            #0xaf92fc
    // 0xaf92f4: r2 = Null
    //     0xaf92f4: mov             x2, NULL
    // 0xaf92f8: b               #0xaf9320
    // 0xaf92fc: LoadField: r2 = r3->field_37
    //     0xaf92fc: ldur            w2, [x3, #0x37]
    // 0xaf9300: DecompressPointer r2
    //     0xaf9300: add             x2, x2, HEAP, lsl #32
    // 0xaf9304: cmp             w2, NULL
    // 0xaf9308: b.ne            #0xaf9314
    // 0xaf930c: r2 = Null
    //     0xaf930c: mov             x2, NULL
    // 0xaf9310: b               #0xaf9320
    // 0xaf9314: LoadField: r4 = r2->field_f
    //     0xaf9314: ldur            w4, [x2, #0xf]
    // 0xaf9318: DecompressPointer r4
    //     0xaf9318: add             x4, x4, HEAP, lsl #32
    // 0xaf931c: mov             x2, x4
    // 0xaf9320: cmp             w2, NULL
    // 0xaf9324: b.ne            #0xaf932c
    // 0xaf9328: r2 = ""
    //     0xaf9328: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf932c: StoreField: r0->field_13 = r2
    //     0xaf932c: stur            w2, [x0, #0x13]
    // 0xaf9330: r16 = " / Qty: "
    //     0xaf9330: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xaf9334: ldr             x16, [x16, #0x760]
    // 0xaf9338: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf9338: stur            w16, [x0, #0x17]
    // 0xaf933c: cmp             w3, NULL
    // 0xaf9340: b.ne            #0xaf934c
    // 0xaf9344: r2 = Null
    //     0xaf9344: mov             x2, NULL
    // 0xaf9348: b               #0xaf9370
    // 0xaf934c: LoadField: r2 = r3->field_37
    //     0xaf934c: ldur            w2, [x3, #0x37]
    // 0xaf9350: DecompressPointer r2
    //     0xaf9350: add             x2, x2, HEAP, lsl #32
    // 0xaf9354: cmp             w2, NULL
    // 0xaf9358: b.ne            #0xaf9364
    // 0xaf935c: r2 = Null
    //     0xaf935c: mov             x2, NULL
    // 0xaf9360: b               #0xaf9370
    // 0xaf9364: LoadField: r3 = r2->field_13
    //     0xaf9364: ldur            w3, [x2, #0x13]
    // 0xaf9368: DecompressPointer r3
    //     0xaf9368: add             x3, x3, HEAP, lsl #32
    // 0xaf936c: mov             x2, x3
    // 0xaf9370: StoreField: r0->field_1b = r2
    //     0xaf9370: stur            w2, [x0, #0x1b]
    // 0xaf9374: str             x0, [SP]
    // 0xaf9378: r0 = _interpolate()
    //     0xaf9378: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaf937c: b               #0xaf9448
    // 0xaf9380: ldur            x0, [fp, #-8]
    // 0xaf9384: r1 = Null
    //     0xaf9384: mov             x1, NULL
    // 0xaf9388: r2 = 8
    //     0xaf9388: movz            x2, #0x8
    // 0xaf938c: r0 = AllocateArray()
    //     0xaf938c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf9390: r16 = "Variant: "
    //     0xaf9390: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xaf9394: ldr             x16, [x16, #0xf08]
    // 0xaf9398: StoreField: r0->field_f = r16
    //     0xaf9398: stur            w16, [x0, #0xf]
    // 0xaf939c: ldur            x1, [fp, #-8]
    // 0xaf93a0: LoadField: r2 = r1->field_b
    //     0xaf93a0: ldur            w2, [x1, #0xb]
    // 0xaf93a4: DecompressPointer r2
    //     0xaf93a4: add             x2, x2, HEAP, lsl #32
    // 0xaf93a8: cmp             w2, NULL
    // 0xaf93ac: b.eq            #0xafa338
    // 0xaf93b0: LoadField: r3 = r2->field_23
    //     0xaf93b0: ldur            w3, [x2, #0x23]
    // 0xaf93b4: DecompressPointer r3
    //     0xaf93b4: add             x3, x3, HEAP, lsl #32
    // 0xaf93b8: cmp             w3, NULL
    // 0xaf93bc: b.ne            #0xaf93c8
    // 0xaf93c0: r2 = Null
    //     0xaf93c0: mov             x2, NULL
    // 0xaf93c4: b               #0xaf93ec
    // 0xaf93c8: LoadField: r2 = r3->field_37
    //     0xaf93c8: ldur            w2, [x3, #0x37]
    // 0xaf93cc: DecompressPointer r2
    //     0xaf93cc: add             x2, x2, HEAP, lsl #32
    // 0xaf93d0: cmp             w2, NULL
    // 0xaf93d4: b.ne            #0xaf93e0
    // 0xaf93d8: r2 = Null
    //     0xaf93d8: mov             x2, NULL
    // 0xaf93dc: b               #0xaf93ec
    // 0xaf93e0: LoadField: r4 = r2->field_f
    //     0xaf93e0: ldur            w4, [x2, #0xf]
    // 0xaf93e4: DecompressPointer r4
    //     0xaf93e4: add             x4, x4, HEAP, lsl #32
    // 0xaf93e8: mov             x2, x4
    // 0xaf93ec: cmp             w2, NULL
    // 0xaf93f0: b.ne            #0xaf93f8
    // 0xaf93f4: r2 = ""
    //     0xaf93f4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf93f8: StoreField: r0->field_13 = r2
    //     0xaf93f8: stur            w2, [x0, #0x13]
    // 0xaf93fc: r16 = " / Qty: "
    //     0xaf93fc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xaf9400: ldr             x16, [x16, #0x760]
    // 0xaf9404: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf9404: stur            w16, [x0, #0x17]
    // 0xaf9408: cmp             w3, NULL
    // 0xaf940c: b.ne            #0xaf9418
    // 0xaf9410: r2 = Null
    //     0xaf9410: mov             x2, NULL
    // 0xaf9414: b               #0xaf943c
    // 0xaf9418: LoadField: r2 = r3->field_37
    //     0xaf9418: ldur            w2, [x3, #0x37]
    // 0xaf941c: DecompressPointer r2
    //     0xaf941c: add             x2, x2, HEAP, lsl #32
    // 0xaf9420: cmp             w2, NULL
    // 0xaf9424: b.ne            #0xaf9430
    // 0xaf9428: r2 = Null
    //     0xaf9428: mov             x2, NULL
    // 0xaf942c: b               #0xaf943c
    // 0xaf9430: LoadField: r3 = r2->field_13
    //     0xaf9430: ldur            w3, [x2, #0x13]
    // 0xaf9434: DecompressPointer r3
    //     0xaf9434: add             x3, x3, HEAP, lsl #32
    // 0xaf9438: mov             x2, x3
    // 0xaf943c: StoreField: r0->field_1b = r2
    //     0xaf943c: stur            w2, [x0, #0x1b]
    // 0xaf9440: str             x0, [SP]
    // 0xaf9444: r0 = _interpolate()
    //     0xaf9444: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaf9448: mov             x2, x0
    // 0xaf944c: b               #0xaf9788
    // 0xaf9450: ldur            x0, [fp, #-8]
    // 0xaf9454: LoadField: r1 = r0->field_b
    //     0xaf9454: ldur            w1, [x0, #0xb]
    // 0xaf9458: DecompressPointer r1
    //     0xaf9458: add             x1, x1, HEAP, lsl #32
    // 0xaf945c: cmp             w1, NULL
    // 0xaf9460: b.eq            #0xafa33c
    // 0xaf9464: LoadField: r2 = r1->field_f
    //     0xaf9464: ldur            w2, [x1, #0xf]
    // 0xaf9468: DecompressPointer r2
    //     0xaf9468: add             x2, x2, HEAP, lsl #32
    // 0xaf946c: r16 = "cancel_order"
    //     0xaf946c: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xaf9470: ldr             x16, [x16, #0x98]
    // 0xaf9474: stp             x16, x2, [SP]
    // 0xaf9478: r0 = ==()
    //     0xaf9478: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf947c: tbnz            w0, #4, #0xaf9604
    // 0xaf9480: ldur            x1, [fp, #-8]
    // 0xaf9484: LoadField: r0 = r1->field_b
    //     0xaf9484: ldur            w0, [x1, #0xb]
    // 0xaf9488: DecompressPointer r0
    //     0xaf9488: add             x0, x0, HEAP, lsl #32
    // 0xaf948c: cmp             w0, NULL
    // 0xaf9490: b.eq            #0xafa340
    // 0xaf9494: LoadField: r2 = r0->field_1b
    //     0xaf9494: ldur            w2, [x0, #0x1b]
    // 0xaf9498: DecompressPointer r2
    //     0xaf9498: add             x2, x2, HEAP, lsl #32
    // 0xaf949c: cmp             w2, NULL
    // 0xaf94a0: b.ne            #0xaf94ac
    // 0xaf94a4: r0 = Null
    //     0xaf94a4: mov             x0, NULL
    // 0xaf94a8: b               #0xaf94b4
    // 0xaf94ac: LoadField: r0 = r2->field_47
    //     0xaf94ac: ldur            w0, [x2, #0x47]
    // 0xaf94b0: DecompressPointer r0
    //     0xaf94b0: add             x0, x0, HEAP, lsl #32
    // 0xaf94b4: r2 = LoadClassIdInstr(r0)
    //     0xaf94b4: ldur            x2, [x0, #-1]
    //     0xaf94b8: ubfx            x2, x2, #0xc, #0x14
    // 0xaf94bc: r16 = "size"
    //     0xaf94bc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xaf94c0: ldr             x16, [x16, #0x9c0]
    // 0xaf94c4: stp             x16, x0, [SP]
    // 0xaf94c8: mov             x0, x2
    // 0xaf94cc: mov             lr, x0
    // 0xaf94d0: ldr             lr, [x21, lr, lsl #3]
    // 0xaf94d4: blr             lr
    // 0xaf94d8: tbnz            w0, #4, #0xaf9570
    // 0xaf94dc: ldur            x0, [fp, #-8]
    // 0xaf94e0: r1 = Null
    //     0xaf94e0: mov             x1, NULL
    // 0xaf94e4: r2 = 8
    //     0xaf94e4: movz            x2, #0x8
    // 0xaf94e8: r0 = AllocateArray()
    //     0xaf94e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf94ec: r16 = "Size: "
    //     0xaf94ec: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xaf94f0: ldr             x16, [x16, #0xf00]
    // 0xaf94f4: StoreField: r0->field_f = r16
    //     0xaf94f4: stur            w16, [x0, #0xf]
    // 0xaf94f8: ldur            x1, [fp, #-8]
    // 0xaf94fc: LoadField: r2 = r1->field_b
    //     0xaf94fc: ldur            w2, [x1, #0xb]
    // 0xaf9500: DecompressPointer r2
    //     0xaf9500: add             x2, x2, HEAP, lsl #32
    // 0xaf9504: cmp             w2, NULL
    // 0xaf9508: b.eq            #0xafa344
    // 0xaf950c: LoadField: r3 = r2->field_1b
    //     0xaf950c: ldur            w3, [x2, #0x1b]
    // 0xaf9510: DecompressPointer r3
    //     0xaf9510: add             x3, x3, HEAP, lsl #32
    // 0xaf9514: cmp             w3, NULL
    // 0xaf9518: b.ne            #0xaf9524
    // 0xaf951c: r2 = Null
    //     0xaf951c: mov             x2, NULL
    // 0xaf9520: b               #0xaf952c
    // 0xaf9524: LoadField: r2 = r3->field_1f
    //     0xaf9524: ldur            w2, [x3, #0x1f]
    // 0xaf9528: DecompressPointer r2
    //     0xaf9528: add             x2, x2, HEAP, lsl #32
    // 0xaf952c: cmp             w2, NULL
    // 0xaf9530: b.ne            #0xaf9538
    // 0xaf9534: r2 = ""
    //     0xaf9534: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf9538: StoreField: r0->field_13 = r2
    //     0xaf9538: stur            w2, [x0, #0x13]
    // 0xaf953c: r16 = " / Qty: "
    //     0xaf953c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xaf9540: ldr             x16, [x16, #0x760]
    // 0xaf9544: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf9544: stur            w16, [x0, #0x17]
    // 0xaf9548: cmp             w3, NULL
    // 0xaf954c: b.ne            #0xaf9558
    // 0xaf9550: r2 = Null
    //     0xaf9550: mov             x2, NULL
    // 0xaf9554: b               #0xaf9560
    // 0xaf9558: LoadField: r2 = r3->field_23
    //     0xaf9558: ldur            w2, [x3, #0x23]
    // 0xaf955c: DecompressPointer r2
    //     0xaf955c: add             x2, x2, HEAP, lsl #32
    // 0xaf9560: StoreField: r0->field_1b = r2
    //     0xaf9560: stur            w2, [x0, #0x1b]
    // 0xaf9564: str             x0, [SP]
    // 0xaf9568: r0 = _interpolate()
    //     0xaf9568: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaf956c: b               #0xaf9784
    // 0xaf9570: ldur            x0, [fp, #-8]
    // 0xaf9574: r1 = Null
    //     0xaf9574: mov             x1, NULL
    // 0xaf9578: r2 = 8
    //     0xaf9578: movz            x2, #0x8
    // 0xaf957c: r0 = AllocateArray()
    //     0xaf957c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf9580: r16 = "Variant: "
    //     0xaf9580: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xaf9584: ldr             x16, [x16, #0xf08]
    // 0xaf9588: StoreField: r0->field_f = r16
    //     0xaf9588: stur            w16, [x0, #0xf]
    // 0xaf958c: ldur            x1, [fp, #-8]
    // 0xaf9590: LoadField: r2 = r1->field_b
    //     0xaf9590: ldur            w2, [x1, #0xb]
    // 0xaf9594: DecompressPointer r2
    //     0xaf9594: add             x2, x2, HEAP, lsl #32
    // 0xaf9598: cmp             w2, NULL
    // 0xaf959c: b.eq            #0xafa348
    // 0xaf95a0: LoadField: r3 = r2->field_1b
    //     0xaf95a0: ldur            w3, [x2, #0x1b]
    // 0xaf95a4: DecompressPointer r3
    //     0xaf95a4: add             x3, x3, HEAP, lsl #32
    // 0xaf95a8: cmp             w3, NULL
    // 0xaf95ac: b.ne            #0xaf95b8
    // 0xaf95b0: r2 = Null
    //     0xaf95b0: mov             x2, NULL
    // 0xaf95b4: b               #0xaf95c0
    // 0xaf95b8: LoadField: r2 = r3->field_1f
    //     0xaf95b8: ldur            w2, [x3, #0x1f]
    // 0xaf95bc: DecompressPointer r2
    //     0xaf95bc: add             x2, x2, HEAP, lsl #32
    // 0xaf95c0: cmp             w2, NULL
    // 0xaf95c4: b.ne            #0xaf95cc
    // 0xaf95c8: r2 = ""
    //     0xaf95c8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf95cc: StoreField: r0->field_13 = r2
    //     0xaf95cc: stur            w2, [x0, #0x13]
    // 0xaf95d0: r16 = " / Qty: "
    //     0xaf95d0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xaf95d4: ldr             x16, [x16, #0x760]
    // 0xaf95d8: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf95d8: stur            w16, [x0, #0x17]
    // 0xaf95dc: cmp             w3, NULL
    // 0xaf95e0: b.ne            #0xaf95ec
    // 0xaf95e4: r2 = Null
    //     0xaf95e4: mov             x2, NULL
    // 0xaf95e8: b               #0xaf95f4
    // 0xaf95ec: LoadField: r2 = r3->field_23
    //     0xaf95ec: ldur            w2, [x3, #0x23]
    // 0xaf95f0: DecompressPointer r2
    //     0xaf95f0: add             x2, x2, HEAP, lsl #32
    // 0xaf95f4: StoreField: r0->field_1b = r2
    //     0xaf95f4: stur            w2, [x0, #0x1b]
    // 0xaf95f8: str             x0, [SP]
    // 0xaf95fc: r0 = _interpolate()
    //     0xaf95fc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaf9600: b               #0xaf9784
    // 0xaf9604: ldur            x1, [fp, #-8]
    // 0xaf9608: LoadField: r0 = r1->field_b
    //     0xaf9608: ldur            w0, [x1, #0xb]
    // 0xaf960c: DecompressPointer r0
    //     0xaf960c: add             x0, x0, HEAP, lsl #32
    // 0xaf9610: cmp             w0, NULL
    // 0xaf9614: b.eq            #0xafa34c
    // 0xaf9618: LoadField: r2 = r0->field_1f
    //     0xaf9618: ldur            w2, [x0, #0x1f]
    // 0xaf961c: DecompressPointer r2
    //     0xaf961c: add             x2, x2, HEAP, lsl #32
    // 0xaf9620: cmp             w2, NULL
    // 0xaf9624: b.ne            #0xaf9630
    // 0xaf9628: r0 = Null
    //     0xaf9628: mov             x0, NULL
    // 0xaf962c: b               #0xaf9638
    // 0xaf9630: LoadField: r0 = r2->field_7f
    //     0xaf9630: ldur            w0, [x2, #0x7f]
    // 0xaf9634: DecompressPointer r0
    //     0xaf9634: add             x0, x0, HEAP, lsl #32
    // 0xaf9638: r2 = LoadClassIdInstr(r0)
    //     0xaf9638: ldur            x2, [x0, #-1]
    //     0xaf963c: ubfx            x2, x2, #0xc, #0x14
    // 0xaf9640: r16 = "size"
    //     0xaf9640: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xaf9644: ldr             x16, [x16, #0x9c0]
    // 0xaf9648: stp             x16, x0, [SP]
    // 0xaf964c: mov             x0, x2
    // 0xaf9650: mov             lr, x0
    // 0xaf9654: ldr             lr, [x21, lr, lsl #3]
    // 0xaf9658: blr             lr
    // 0xaf965c: tbnz            w0, #4, #0xaf96f4
    // 0xaf9660: ldur            x0, [fp, #-8]
    // 0xaf9664: r1 = Null
    //     0xaf9664: mov             x1, NULL
    // 0xaf9668: r2 = 8
    //     0xaf9668: movz            x2, #0x8
    // 0xaf966c: r0 = AllocateArray()
    //     0xaf966c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf9670: r16 = "Size: "
    //     0xaf9670: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xaf9674: ldr             x16, [x16, #0xf00]
    // 0xaf9678: StoreField: r0->field_f = r16
    //     0xaf9678: stur            w16, [x0, #0xf]
    // 0xaf967c: ldur            x1, [fp, #-8]
    // 0xaf9680: LoadField: r2 = r1->field_b
    //     0xaf9680: ldur            w2, [x1, #0xb]
    // 0xaf9684: DecompressPointer r2
    //     0xaf9684: add             x2, x2, HEAP, lsl #32
    // 0xaf9688: cmp             w2, NULL
    // 0xaf968c: b.eq            #0xafa350
    // 0xaf9690: LoadField: r3 = r2->field_1f
    //     0xaf9690: ldur            w3, [x2, #0x1f]
    // 0xaf9694: DecompressPointer r3
    //     0xaf9694: add             x3, x3, HEAP, lsl #32
    // 0xaf9698: cmp             w3, NULL
    // 0xaf969c: b.ne            #0xaf96a8
    // 0xaf96a0: r2 = Null
    //     0xaf96a0: mov             x2, NULL
    // 0xaf96a4: b               #0xaf96b0
    // 0xaf96a8: LoadField: r2 = r3->field_7
    //     0xaf96a8: ldur            w2, [x3, #7]
    // 0xaf96ac: DecompressPointer r2
    //     0xaf96ac: add             x2, x2, HEAP, lsl #32
    // 0xaf96b0: cmp             w2, NULL
    // 0xaf96b4: b.ne            #0xaf96bc
    // 0xaf96b8: r2 = ""
    //     0xaf96b8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf96bc: StoreField: r0->field_13 = r2
    //     0xaf96bc: stur            w2, [x0, #0x13]
    // 0xaf96c0: r16 = " / Qty: "
    //     0xaf96c0: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xaf96c4: ldr             x16, [x16, #0x760]
    // 0xaf96c8: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf96c8: stur            w16, [x0, #0x17]
    // 0xaf96cc: cmp             w3, NULL
    // 0xaf96d0: b.ne            #0xaf96dc
    // 0xaf96d4: r2 = Null
    //     0xaf96d4: mov             x2, NULL
    // 0xaf96d8: b               #0xaf96e4
    // 0xaf96dc: LoadField: r2 = r3->field_f
    //     0xaf96dc: ldur            w2, [x3, #0xf]
    // 0xaf96e0: DecompressPointer r2
    //     0xaf96e0: add             x2, x2, HEAP, lsl #32
    // 0xaf96e4: StoreField: r0->field_1b = r2
    //     0xaf96e4: stur            w2, [x0, #0x1b]
    // 0xaf96e8: str             x0, [SP]
    // 0xaf96ec: r0 = _interpolate()
    //     0xaf96ec: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaf96f0: b               #0xaf9784
    // 0xaf96f4: ldur            x0, [fp, #-8]
    // 0xaf96f8: r1 = Null
    //     0xaf96f8: mov             x1, NULL
    // 0xaf96fc: r2 = 8
    //     0xaf96fc: movz            x2, #0x8
    // 0xaf9700: r0 = AllocateArray()
    //     0xaf9700: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf9704: r16 = "Variant: "
    //     0xaf9704: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xaf9708: ldr             x16, [x16, #0xf08]
    // 0xaf970c: StoreField: r0->field_f = r16
    //     0xaf970c: stur            w16, [x0, #0xf]
    // 0xaf9710: ldur            x1, [fp, #-8]
    // 0xaf9714: LoadField: r2 = r1->field_b
    //     0xaf9714: ldur            w2, [x1, #0xb]
    // 0xaf9718: DecompressPointer r2
    //     0xaf9718: add             x2, x2, HEAP, lsl #32
    // 0xaf971c: cmp             w2, NULL
    // 0xaf9720: b.eq            #0xafa354
    // 0xaf9724: LoadField: r3 = r2->field_1f
    //     0xaf9724: ldur            w3, [x2, #0x1f]
    // 0xaf9728: DecompressPointer r3
    //     0xaf9728: add             x3, x3, HEAP, lsl #32
    // 0xaf972c: cmp             w3, NULL
    // 0xaf9730: b.ne            #0xaf973c
    // 0xaf9734: r2 = Null
    //     0xaf9734: mov             x2, NULL
    // 0xaf9738: b               #0xaf9744
    // 0xaf973c: LoadField: r2 = r3->field_7
    //     0xaf973c: ldur            w2, [x3, #7]
    // 0xaf9740: DecompressPointer r2
    //     0xaf9740: add             x2, x2, HEAP, lsl #32
    // 0xaf9744: cmp             w2, NULL
    // 0xaf9748: b.ne            #0xaf9750
    // 0xaf974c: r2 = ""
    //     0xaf974c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf9750: StoreField: r0->field_13 = r2
    //     0xaf9750: stur            w2, [x0, #0x13]
    // 0xaf9754: r16 = " / Qty: "
    //     0xaf9754: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xaf9758: ldr             x16, [x16, #0x760]
    // 0xaf975c: ArrayStore: r0[0] = r16  ; List_4
    //     0xaf975c: stur            w16, [x0, #0x17]
    // 0xaf9760: cmp             w3, NULL
    // 0xaf9764: b.ne            #0xaf9770
    // 0xaf9768: r2 = Null
    //     0xaf9768: mov             x2, NULL
    // 0xaf976c: b               #0xaf9778
    // 0xaf9770: LoadField: r2 = r3->field_f
    //     0xaf9770: ldur            w2, [x3, #0xf]
    // 0xaf9774: DecompressPointer r2
    //     0xaf9774: add             x2, x2, HEAP, lsl #32
    // 0xaf9778: StoreField: r0->field_1b = r2
    //     0xaf9778: stur            w2, [x0, #0x1b]
    // 0xaf977c: str             x0, [SP]
    // 0xaf9780: r0 = _interpolate()
    //     0xaf9780: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xaf9784: mov             x2, x0
    // 0xaf9788: ldur            x0, [fp, #-8]
    // 0xaf978c: ldur            x1, [fp, #-0x10]
    // 0xaf9790: stur            x2, [fp, #-0x40]
    // 0xaf9794: r0 = of()
    //     0xaf9794: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf9798: LoadField: r1 = r0->field_87
    //     0xaf9798: ldur            w1, [x0, #0x87]
    // 0xaf979c: DecompressPointer r1
    //     0xaf979c: add             x1, x1, HEAP, lsl #32
    // 0xaf97a0: LoadField: r0 = r1->field_2b
    //     0xaf97a0: ldur            w0, [x1, #0x2b]
    // 0xaf97a4: DecompressPointer r0
    //     0xaf97a4: add             x0, x0, HEAP, lsl #32
    // 0xaf97a8: r16 = 12.000000
    //     0xaf97a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf97ac: ldr             x16, [x16, #0x9e8]
    // 0xaf97b0: r30 = Instance_Color
    //     0xaf97b0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf97b4: stp             lr, x16, [SP]
    // 0xaf97b8: mov             x1, x0
    // 0xaf97bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf97bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf97c0: ldr             x4, [x4, #0xaa0]
    // 0xaf97c4: r0 = copyWith()
    //     0xaf97c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf97c8: stur            x0, [fp, #-0x48]
    // 0xaf97cc: r0 = Text()
    //     0xaf97cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf97d0: mov             x1, x0
    // 0xaf97d4: ldur            x0, [fp, #-0x40]
    // 0xaf97d8: stur            x1, [fp, #-0x60]
    // 0xaf97dc: StoreField: r1->field_b = r0
    //     0xaf97dc: stur            w0, [x1, #0xb]
    // 0xaf97e0: ldur            x0, [fp, #-0x48]
    // 0xaf97e4: StoreField: r1->field_13 = r0
    //     0xaf97e4: stur            w0, [x1, #0x13]
    // 0xaf97e8: r0 = Padding()
    //     0xaf97e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf97ec: mov             x1, x0
    // 0xaf97f0: r0 = Instance_EdgeInsets
    //     0xaf97f0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xaf97f4: ldr             x0, [x0, #0x770]
    // 0xaf97f8: stur            x1, [fp, #-0x40]
    // 0xaf97fc: StoreField: r1->field_f = r0
    //     0xaf97fc: stur            w0, [x1, #0xf]
    // 0xaf9800: ldur            x0, [fp, #-0x60]
    // 0xaf9804: StoreField: r1->field_b = r0
    //     0xaf9804: stur            w0, [x1, #0xb]
    // 0xaf9808: ldur            x0, [fp, #-8]
    // 0xaf980c: LoadField: r2 = r0->field_b
    //     0xaf980c: ldur            w2, [x0, #0xb]
    // 0xaf9810: DecompressPointer r2
    //     0xaf9810: add             x2, x2, HEAP, lsl #32
    // 0xaf9814: cmp             w2, NULL
    // 0xaf9818: b.eq            #0xafa358
    // 0xaf981c: LoadField: r3 = r2->field_f
    //     0xaf981c: ldur            w3, [x2, #0xf]
    // 0xaf9820: DecompressPointer r3
    //     0xaf9820: add             x3, x3, HEAP, lsl #32
    // 0xaf9824: r16 = "return_order_intermediate"
    //     0xaf9824: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xaf9828: ldr             x16, [x16, #0xb00]
    // 0xaf982c: stp             x16, x3, [SP]
    // 0xaf9830: r0 = ==()
    //     0xaf9830: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf9834: tbnz            w0, #4, #0xaf98b4
    // 0xaf9838: ldur            x0, [fp, #-8]
    // 0xaf983c: LoadField: r1 = r0->field_b
    //     0xaf983c: ldur            w1, [x0, #0xb]
    // 0xaf9840: DecompressPointer r1
    //     0xaf9840: add             x1, x1, HEAP, lsl #32
    // 0xaf9844: cmp             w1, NULL
    // 0xaf9848: b.eq            #0xafa35c
    // 0xaf984c: LoadField: r0 = r1->field_23
    //     0xaf984c: ldur            w0, [x1, #0x23]
    // 0xaf9850: DecompressPointer r0
    //     0xaf9850: add             x0, x0, HEAP, lsl #32
    // 0xaf9854: cmp             w0, NULL
    // 0xaf9858: b.ne            #0xaf9864
    // 0xaf985c: r0 = Null
    //     0xaf985c: mov             x0, NULL
    // 0xaf9860: b               #0xaf98a0
    // 0xaf9864: LoadField: r1 = r0->field_37
    //     0xaf9864: ldur            w1, [x0, #0x37]
    // 0xaf9868: DecompressPointer r1
    //     0xaf9868: add             x1, x1, HEAP, lsl #32
    // 0xaf986c: cmp             w1, NULL
    // 0xaf9870: b.ne            #0xaf987c
    // 0xaf9874: r0 = Null
    //     0xaf9874: mov             x0, NULL
    // 0xaf9878: b               #0xaf98a0
    // 0xaf987c: LoadField: r0 = r1->field_1f
    //     0xaf987c: ldur            w0, [x1, #0x1f]
    // 0xaf9880: DecompressPointer r0
    //     0xaf9880: add             x0, x0, HEAP, lsl #32
    // 0xaf9884: cmp             w0, NULL
    // 0xaf9888: b.ne            #0xaf9894
    // 0xaf988c: r0 = Null
    //     0xaf988c: mov             x0, NULL
    // 0xaf9890: b               #0xaf98a0
    // 0xaf9894: LoadField: r1 = r0->field_7
    //     0xaf9894: ldur            w1, [x0, #7]
    // 0xaf9898: DecompressPointer r1
    //     0xaf9898: add             x1, x1, HEAP, lsl #32
    // 0xaf989c: mov             x0, x1
    // 0xaf98a0: cmp             w0, NULL
    // 0xaf98a4: b.ne            #0xaf98ac
    // 0xaf98a8: r0 = ""
    //     0xaf98a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf98ac: mov             x7, x0
    // 0xaf98b0: b               #0xaf9974
    // 0xaf98b4: ldur            x0, [fp, #-8]
    // 0xaf98b8: LoadField: r1 = r0->field_b
    //     0xaf98b8: ldur            w1, [x0, #0xb]
    // 0xaf98bc: DecompressPointer r1
    //     0xaf98bc: add             x1, x1, HEAP, lsl #32
    // 0xaf98c0: cmp             w1, NULL
    // 0xaf98c4: b.eq            #0xafa360
    // 0xaf98c8: LoadField: r2 = r1->field_f
    //     0xaf98c8: ldur            w2, [x1, #0xf]
    // 0xaf98cc: DecompressPointer r2
    //     0xaf98cc: add             x2, x2, HEAP, lsl #32
    // 0xaf98d0: r16 = "cancel_order"
    //     0xaf98d0: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xaf98d4: ldr             x16, [x16, #0x98]
    // 0xaf98d8: stp             x16, x2, [SP]
    // 0xaf98dc: r0 = ==()
    //     0xaf98dc: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xaf98e0: tbnz            w0, #4, #0xaf992c
    // 0xaf98e4: ldur            x0, [fp, #-8]
    // 0xaf98e8: LoadField: r1 = r0->field_b
    //     0xaf98e8: ldur            w1, [x0, #0xb]
    // 0xaf98ec: DecompressPointer r1
    //     0xaf98ec: add             x1, x1, HEAP, lsl #32
    // 0xaf98f0: cmp             w1, NULL
    // 0xaf98f4: b.eq            #0xafa364
    // 0xaf98f8: LoadField: r0 = r1->field_1b
    //     0xaf98f8: ldur            w0, [x1, #0x1b]
    // 0xaf98fc: DecompressPointer r0
    //     0xaf98fc: add             x0, x0, HEAP, lsl #32
    // 0xaf9900: cmp             w0, NULL
    // 0xaf9904: b.ne            #0xaf9910
    // 0xaf9908: r0 = Null
    //     0xaf9908: mov             x0, NULL
    // 0xaf990c: b               #0xaf991c
    // 0xaf9910: LoadField: r1 = r0->field_4b
    //     0xaf9910: ldur            w1, [x0, #0x4b]
    // 0xaf9914: DecompressPointer r1
    //     0xaf9914: add             x1, x1, HEAP, lsl #32
    // 0xaf9918: mov             x0, x1
    // 0xaf991c: cmp             w0, NULL
    // 0xaf9920: b.ne            #0xaf9970
    // 0xaf9924: r0 = ""
    //     0xaf9924: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf9928: b               #0xaf9970
    // 0xaf992c: ldur            x0, [fp, #-8]
    // 0xaf9930: LoadField: r1 = r0->field_b
    //     0xaf9930: ldur            w1, [x0, #0xb]
    // 0xaf9934: DecompressPointer r1
    //     0xaf9934: add             x1, x1, HEAP, lsl #32
    // 0xaf9938: cmp             w1, NULL
    // 0xaf993c: b.eq            #0xafa368
    // 0xaf9940: LoadField: r0 = r1->field_1f
    //     0xaf9940: ldur            w0, [x1, #0x1f]
    // 0xaf9944: DecompressPointer r0
    //     0xaf9944: add             x0, x0, HEAP, lsl #32
    // 0xaf9948: cmp             w0, NULL
    // 0xaf994c: b.ne            #0xaf9958
    // 0xaf9950: r0 = Null
    //     0xaf9950: mov             x0, NULL
    // 0xaf9954: b               #0xaf9964
    // 0xaf9958: LoadField: r1 = r0->field_13
    //     0xaf9958: ldur            w1, [x0, #0x13]
    // 0xaf995c: DecompressPointer r1
    //     0xaf995c: add             x1, x1, HEAP, lsl #32
    // 0xaf9960: mov             x0, x1
    // 0xaf9964: cmp             w0, NULL
    // 0xaf9968: b.ne            #0xaf9970
    // 0xaf996c: r0 = ""
    //     0xaf996c: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xaf9970: mov             x7, x0
    // 0xaf9974: ldur            x6, [fp, #-0x30]
    // 0xaf9978: ldur            x5, [fp, #-0x18]
    // 0xaf997c: ldur            x4, [fp, #-0x38]
    // 0xaf9980: ldur            x3, [fp, #-0x58]
    // 0xaf9984: ldur            x2, [fp, #-0x50]
    // 0xaf9988: ldur            x0, [fp, #-0x40]
    // 0xaf998c: ldur            x1, [fp, #-0x10]
    // 0xaf9990: stur            x7, [fp, #-8]
    // 0xaf9994: r0 = of()
    //     0xaf9994: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf9998: LoadField: r1 = r0->field_87
    //     0xaf9998: ldur            w1, [x0, #0x87]
    // 0xaf999c: DecompressPointer r1
    //     0xaf999c: add             x1, x1, HEAP, lsl #32
    // 0xaf99a0: LoadField: r0 = r1->field_7
    //     0xaf99a0: ldur            w0, [x1, #7]
    // 0xaf99a4: DecompressPointer r0
    //     0xaf99a4: add             x0, x0, HEAP, lsl #32
    // 0xaf99a8: r16 = 12.000000
    //     0xaf99a8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xaf99ac: ldr             x16, [x16, #0x9e8]
    // 0xaf99b0: r30 = Instance_Color
    //     0xaf99b0: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xaf99b4: stp             lr, x16, [SP]
    // 0xaf99b8: mov             x1, x0
    // 0xaf99bc: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf99bc: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf99c0: ldr             x4, [x4, #0xaa0]
    // 0xaf99c4: r0 = copyWith()
    //     0xaf99c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf99c8: stur            x0, [fp, #-0x48]
    // 0xaf99cc: r0 = Text()
    //     0xaf99cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf99d0: mov             x3, x0
    // 0xaf99d4: ldur            x0, [fp, #-8]
    // 0xaf99d8: stur            x3, [fp, #-0x60]
    // 0xaf99dc: StoreField: r3->field_b = r0
    //     0xaf99dc: stur            w0, [x3, #0xb]
    // 0xaf99e0: ldur            x0, [fp, #-0x48]
    // 0xaf99e4: StoreField: r3->field_13 = r0
    //     0xaf99e4: stur            w0, [x3, #0x13]
    // 0xaf99e8: r1 = Null
    //     0xaf99e8: mov             x1, NULL
    // 0xaf99ec: r2 = 8
    //     0xaf99ec: movz            x2, #0x8
    // 0xaf99f0: r0 = AllocateArray()
    //     0xaf99f0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf99f4: mov             x2, x0
    // 0xaf99f8: ldur            x0, [fp, #-0x50]
    // 0xaf99fc: stur            x2, [fp, #-8]
    // 0xaf9a00: StoreField: r2->field_f = r0
    //     0xaf9a00: stur            w0, [x2, #0xf]
    // 0xaf9a04: ldur            x0, [fp, #-0x40]
    // 0xaf9a08: StoreField: r2->field_13 = r0
    //     0xaf9a08: stur            w0, [x2, #0x13]
    // 0xaf9a0c: r16 = Instance_SizedBox
    //     0xaf9a0c: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xaf9a10: ldr             x16, [x16, #0xc70]
    // 0xaf9a14: ArrayStore: r2[0] = r16  ; List_4
    //     0xaf9a14: stur            w16, [x2, #0x17]
    // 0xaf9a18: ldur            x0, [fp, #-0x60]
    // 0xaf9a1c: StoreField: r2->field_1b = r0
    //     0xaf9a1c: stur            w0, [x2, #0x1b]
    // 0xaf9a20: r1 = <Widget>
    //     0xaf9a20: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf9a24: r0 = AllocateGrowableArray()
    //     0xaf9a24: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf9a28: mov             x1, x0
    // 0xaf9a2c: ldur            x0, [fp, #-8]
    // 0xaf9a30: stur            x1, [fp, #-0x40]
    // 0xaf9a34: StoreField: r1->field_f = r0
    //     0xaf9a34: stur            w0, [x1, #0xf]
    // 0xaf9a38: r0 = 8
    //     0xaf9a38: movz            x0, #0x8
    // 0xaf9a3c: StoreField: r1->field_b = r0
    //     0xaf9a3c: stur            w0, [x1, #0xb]
    // 0xaf9a40: r0 = Column()
    //     0xaf9a40: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf9a44: mov             x2, x0
    // 0xaf9a48: r0 = Instance_Axis
    //     0xaf9a48: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf9a4c: stur            x2, [fp, #-8]
    // 0xaf9a50: StoreField: r2->field_f = r0
    //     0xaf9a50: stur            w0, [x2, #0xf]
    // 0xaf9a54: r3 = Instance_MainAxisAlignment
    //     0xaf9a54: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf9a58: ldr             x3, [x3, #0xa08]
    // 0xaf9a5c: StoreField: r2->field_13 = r3
    //     0xaf9a5c: stur            w3, [x2, #0x13]
    // 0xaf9a60: r4 = Instance_MainAxisSize
    //     0xaf9a60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf9a64: ldr             x4, [x4, #0xa10]
    // 0xaf9a68: ArrayStore: r2[0] = r4  ; List_4
    //     0xaf9a68: stur            w4, [x2, #0x17]
    // 0xaf9a6c: r5 = Instance_CrossAxisAlignment
    //     0xaf9a6c: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xaf9a70: ldr             x5, [x5, #0x890]
    // 0xaf9a74: StoreField: r2->field_1b = r5
    //     0xaf9a74: stur            w5, [x2, #0x1b]
    // 0xaf9a78: r6 = Instance_VerticalDirection
    //     0xaf9a78: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf9a7c: ldr             x6, [x6, #0xa20]
    // 0xaf9a80: StoreField: r2->field_23 = r6
    //     0xaf9a80: stur            w6, [x2, #0x23]
    // 0xaf9a84: r7 = Instance_Clip
    //     0xaf9a84: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf9a88: ldr             x7, [x7, #0x38]
    // 0xaf9a8c: StoreField: r2->field_2b = r7
    //     0xaf9a8c: stur            w7, [x2, #0x2b]
    // 0xaf9a90: StoreField: r2->field_2f = rZR
    //     0xaf9a90: stur            xzr, [x2, #0x2f]
    // 0xaf9a94: ldur            x1, [fp, #-0x40]
    // 0xaf9a98: StoreField: r2->field_b = r1
    //     0xaf9a98: stur            w1, [x2, #0xb]
    // 0xaf9a9c: r1 = <FlexParentData>
    //     0xaf9a9c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaf9aa0: ldr             x1, [x1, #0xe00]
    // 0xaf9aa4: r0 = Expanded()
    //     0xaf9aa4: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf9aa8: mov             x3, x0
    // 0xaf9aac: r0 = 1
    //     0xaf9aac: movz            x0, #0x1
    // 0xaf9ab0: stur            x3, [fp, #-0x40]
    // 0xaf9ab4: StoreField: r3->field_13 = r0
    //     0xaf9ab4: stur            x0, [x3, #0x13]
    // 0xaf9ab8: r4 = Instance_FlexFit
    //     0xaf9ab8: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaf9abc: ldr             x4, [x4, #0xe08]
    // 0xaf9ac0: StoreField: r3->field_1b = r4
    //     0xaf9ac0: stur            w4, [x3, #0x1b]
    // 0xaf9ac4: ldur            x1, [fp, #-8]
    // 0xaf9ac8: StoreField: r3->field_b = r1
    //     0xaf9ac8: stur            w1, [x3, #0xb]
    // 0xaf9acc: r1 = Null
    //     0xaf9acc: mov             x1, NULL
    // 0xaf9ad0: r2 = 6
    //     0xaf9ad0: movz            x2, #0x6
    // 0xaf9ad4: r0 = AllocateArray()
    //     0xaf9ad4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf9ad8: mov             x2, x0
    // 0xaf9adc: ldur            x0, [fp, #-0x58]
    // 0xaf9ae0: stur            x2, [fp, #-8]
    // 0xaf9ae4: StoreField: r2->field_f = r0
    //     0xaf9ae4: stur            w0, [x2, #0xf]
    // 0xaf9ae8: r16 = Instance_SizedBox
    //     0xaf9ae8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xaf9aec: ldr             x16, [x16, #0xb20]
    // 0xaf9af0: StoreField: r2->field_13 = r16
    //     0xaf9af0: stur            w16, [x2, #0x13]
    // 0xaf9af4: ldur            x0, [fp, #-0x40]
    // 0xaf9af8: ArrayStore: r2[0] = r0  ; List_4
    //     0xaf9af8: stur            w0, [x2, #0x17]
    // 0xaf9afc: r1 = <Widget>
    //     0xaf9afc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf9b00: r0 = AllocateGrowableArray()
    //     0xaf9b00: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf9b04: mov             x1, x0
    // 0xaf9b08: ldur            x0, [fp, #-8]
    // 0xaf9b0c: stur            x1, [fp, #-0x40]
    // 0xaf9b10: StoreField: r1->field_f = r0
    //     0xaf9b10: stur            w0, [x1, #0xf]
    // 0xaf9b14: r2 = 6
    //     0xaf9b14: movz            x2, #0x6
    // 0xaf9b18: StoreField: r1->field_b = r2
    //     0xaf9b18: stur            w2, [x1, #0xb]
    // 0xaf9b1c: r0 = Row()
    //     0xaf9b1c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xaf9b20: mov             x3, x0
    // 0xaf9b24: r0 = Instance_Axis
    //     0xaf9b24: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xaf9b28: stur            x3, [fp, #-8]
    // 0xaf9b2c: StoreField: r3->field_f = r0
    //     0xaf9b2c: stur            w0, [x3, #0xf]
    // 0xaf9b30: r4 = Instance_MainAxisAlignment
    //     0xaf9b30: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf9b34: ldr             x4, [x4, #0xa08]
    // 0xaf9b38: StoreField: r3->field_13 = r4
    //     0xaf9b38: stur            w4, [x3, #0x13]
    // 0xaf9b3c: r5 = Instance_MainAxisSize
    //     0xaf9b3c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf9b40: ldr             x5, [x5, #0xa10]
    // 0xaf9b44: ArrayStore: r3[0] = r5  ; List_4
    //     0xaf9b44: stur            w5, [x3, #0x17]
    // 0xaf9b48: r6 = Instance_CrossAxisAlignment
    //     0xaf9b48: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf9b4c: ldr             x6, [x6, #0xa18]
    // 0xaf9b50: StoreField: r3->field_1b = r6
    //     0xaf9b50: stur            w6, [x3, #0x1b]
    // 0xaf9b54: r7 = Instance_VerticalDirection
    //     0xaf9b54: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf9b58: ldr             x7, [x7, #0xa20]
    // 0xaf9b5c: StoreField: r3->field_23 = r7
    //     0xaf9b5c: stur            w7, [x3, #0x23]
    // 0xaf9b60: r8 = Instance_Clip
    //     0xaf9b60: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf9b64: ldr             x8, [x8, #0x38]
    // 0xaf9b68: StoreField: r3->field_2b = r8
    //     0xaf9b68: stur            w8, [x3, #0x2b]
    // 0xaf9b6c: StoreField: r3->field_2f = rZR
    //     0xaf9b6c: stur            xzr, [x3, #0x2f]
    // 0xaf9b70: ldur            x1, [fp, #-0x40]
    // 0xaf9b74: StoreField: r3->field_b = r1
    //     0xaf9b74: stur            w1, [x3, #0xb]
    // 0xaf9b78: r1 = Null
    //     0xaf9b78: mov             x1, NULL
    // 0xaf9b7c: r2 = 2
    //     0xaf9b7c: movz            x2, #0x2
    // 0xaf9b80: r0 = AllocateArray()
    //     0xaf9b80: bl              #0x16f7198  ; AllocateArrayStub
    // 0xaf9b84: mov             x2, x0
    // 0xaf9b88: ldur            x0, [fp, #-8]
    // 0xaf9b8c: stur            x2, [fp, #-0x40]
    // 0xaf9b90: StoreField: r2->field_f = r0
    //     0xaf9b90: stur            w0, [x2, #0xf]
    // 0xaf9b94: r1 = <Widget>
    //     0xaf9b94: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xaf9b98: r0 = AllocateGrowableArray()
    //     0xaf9b98: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xaf9b9c: mov             x1, x0
    // 0xaf9ba0: ldur            x0, [fp, #-0x40]
    // 0xaf9ba4: stur            x1, [fp, #-8]
    // 0xaf9ba8: StoreField: r1->field_f = r0
    //     0xaf9ba8: stur            w0, [x1, #0xf]
    // 0xaf9bac: r0 = 2
    //     0xaf9bac: movz            x0, #0x2
    // 0xaf9bb0: StoreField: r1->field_b = r0
    //     0xaf9bb0: stur            w0, [x1, #0xb]
    // 0xaf9bb4: r0 = Column()
    //     0xaf9bb4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xaf9bb8: mov             x1, x0
    // 0xaf9bbc: r0 = Instance_Axis
    //     0xaf9bbc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xaf9bc0: stur            x1, [fp, #-0x40]
    // 0xaf9bc4: StoreField: r1->field_f = r0
    //     0xaf9bc4: stur            w0, [x1, #0xf]
    // 0xaf9bc8: r2 = Instance_MainAxisAlignment
    //     0xaf9bc8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xaf9bcc: ldr             x2, [x2, #0xa08]
    // 0xaf9bd0: StoreField: r1->field_13 = r2
    //     0xaf9bd0: stur            w2, [x1, #0x13]
    // 0xaf9bd4: r3 = Instance_MainAxisSize
    //     0xaf9bd4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xaf9bd8: ldr             x3, [x3, #0xa10]
    // 0xaf9bdc: ArrayStore: r1[0] = r3  ; List_4
    //     0xaf9bdc: stur            w3, [x1, #0x17]
    // 0xaf9be0: r4 = Instance_CrossAxisAlignment
    //     0xaf9be0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xaf9be4: ldr             x4, [x4, #0xa18]
    // 0xaf9be8: StoreField: r1->field_1b = r4
    //     0xaf9be8: stur            w4, [x1, #0x1b]
    // 0xaf9bec: r4 = Instance_VerticalDirection
    //     0xaf9bec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xaf9bf0: ldr             x4, [x4, #0xa20]
    // 0xaf9bf4: StoreField: r1->field_23 = r4
    //     0xaf9bf4: stur            w4, [x1, #0x23]
    // 0xaf9bf8: r5 = Instance_Clip
    //     0xaf9bf8: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xaf9bfc: ldr             x5, [x5, #0x38]
    // 0xaf9c00: StoreField: r1->field_2b = r5
    //     0xaf9c00: stur            w5, [x1, #0x2b]
    // 0xaf9c04: StoreField: r1->field_2f = rZR
    //     0xaf9c04: stur            xzr, [x1, #0x2f]
    // 0xaf9c08: ldur            x6, [fp, #-8]
    // 0xaf9c0c: StoreField: r1->field_b = r6
    //     0xaf9c0c: stur            w6, [x1, #0xb]
    // 0xaf9c10: r0 = Padding()
    //     0xaf9c10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf9c14: mov             x1, x0
    // 0xaf9c18: r0 = Instance_EdgeInsets
    //     0xaf9c18: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!EdgeInsets@d57b01
    //     0xaf9c1c: ldr             x0, [x0, #0xf48]
    // 0xaf9c20: stur            x1, [fp, #-8]
    // 0xaf9c24: StoreField: r1->field_f = r0
    //     0xaf9c24: stur            w0, [x1, #0xf]
    // 0xaf9c28: ldur            x0, [fp, #-0x40]
    // 0xaf9c2c: StoreField: r1->field_b = r0
    //     0xaf9c2c: stur            w0, [x1, #0xb]
    // 0xaf9c30: r0 = Container()
    //     0xaf9c30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xaf9c34: stur            x0, [fp, #-0x40]
    // 0xaf9c38: r16 = Instance_EdgeInsets
    //     0xaf9c38: add             x16, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xaf9c3c: ldr             x16, [x16, #0x668]
    // 0xaf9c40: ldur            lr, [fp, #-0x28]
    // 0xaf9c44: stp             lr, x16, [SP, #8]
    // 0xaf9c48: ldur            x16, [fp, #-8]
    // 0xaf9c4c: str             x16, [SP]
    // 0xaf9c50: mov             x1, x0
    // 0xaf9c54: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, margin, 0x1, null]
    //     0xaf9c54: add             x4, PP, #0x35, lsl #12  ; [pp+0x35f50] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "margin", 0x1, Null]
    //     0xaf9c58: ldr             x4, [x4, #0xf50]
    // 0xaf9c5c: r0 = Container()
    //     0xaf9c5c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xaf9c60: r0 = Padding()
    //     0xaf9c60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaf9c64: mov             x1, x0
    // 0xaf9c68: r0 = Instance_EdgeInsets
    //     0xaf9c68: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xaf9c6c: ldr             x0, [x0, #0x778]
    // 0xaf9c70: stur            x1, [fp, #-8]
    // 0xaf9c74: StoreField: r1->field_f = r0
    //     0xaf9c74: stur            w0, [x1, #0xf]
    // 0xaf9c78: ldur            x0, [fp, #-0x40]
    // 0xaf9c7c: StoreField: r1->field_b = r0
    //     0xaf9c7c: stur            w0, [x1, #0xb]
    // 0xaf9c80: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xaf9c80: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xaf9c84: ldr             x0, [x0, #0x1c80]
    //     0xaf9c88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xaf9c8c: cmp             w0, w16
    //     0xaf9c90: b.ne            #0xaf9c9c
    //     0xaf9c94: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xaf9c98: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xaf9c9c: r0 = GetNavigation.width()
    //     0xaf9c9c: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xaf9ca0: mov             v1.16b, v0.16b
    // 0xaf9ca4: d0 = 1.200000
    //     0xaf9ca4: add             x17, PP, #0x4d, lsl #12  ; [pp+0x4dd20] IMM: double(1.2) from 0x3ff3333333333333
    //     0xaf9ca8: ldr             d0, [x17, #0xd20]
    // 0xaf9cac: fmul            d2, d1, d0
    // 0xaf9cb0: stur            d2, [fp, #-0x70]
    // 0xaf9cb4: r16 = <EdgeInsets>
    //     0xaf9cb4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xaf9cb8: ldr             x16, [x16, #0xda0]
    // 0xaf9cbc: r30 = Instance_EdgeInsets
    //     0xaf9cbc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xaf9cc0: ldr             lr, [lr, #0x1f0]
    // 0xaf9cc4: stp             lr, x16, [SP]
    // 0xaf9cc8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf9cc8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf9ccc: r0 = all()
    //     0xaf9ccc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf9cd0: ldur            x1, [fp, #-0x10]
    // 0xaf9cd4: stur            x0, [fp, #-0x28]
    // 0xaf9cd8: r0 = of()
    //     0xaf9cd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf9cdc: LoadField: r1 = r0->field_5b
    //     0xaf9cdc: ldur            w1, [x0, #0x5b]
    // 0xaf9ce0: DecompressPointer r1
    //     0xaf9ce0: add             x1, x1, HEAP, lsl #32
    // 0xaf9ce4: r16 = <Color>
    //     0xaf9ce4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xaf9ce8: ldr             x16, [x16, #0xf80]
    // 0xaf9cec: stp             x1, x16, [SP]
    // 0xaf9cf0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf9cf0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf9cf4: r0 = all()
    //     0xaf9cf4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf9cf8: stur            x0, [fp, #-0x40]
    // 0xaf9cfc: r0 = Radius()
    //     0xaf9cfc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf9d00: d0 = 20.000000
    //     0xaf9d00: fmov            d0, #20.00000000
    // 0xaf9d04: stur            x0, [fp, #-0x48]
    // 0xaf9d08: StoreField: r0->field_7 = d0
    //     0xaf9d08: stur            d0, [x0, #7]
    // 0xaf9d0c: StoreField: r0->field_f = d0
    //     0xaf9d0c: stur            d0, [x0, #0xf]
    // 0xaf9d10: r0 = BorderRadius()
    //     0xaf9d10: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf9d14: mov             x1, x0
    // 0xaf9d18: ldur            x0, [fp, #-0x48]
    // 0xaf9d1c: stur            x1, [fp, #-0x50]
    // 0xaf9d20: StoreField: r1->field_7 = r0
    //     0xaf9d20: stur            w0, [x1, #7]
    // 0xaf9d24: StoreField: r1->field_b = r0
    //     0xaf9d24: stur            w0, [x1, #0xb]
    // 0xaf9d28: StoreField: r1->field_f = r0
    //     0xaf9d28: stur            w0, [x1, #0xf]
    // 0xaf9d2c: StoreField: r1->field_13 = r0
    //     0xaf9d2c: stur            w0, [x1, #0x13]
    // 0xaf9d30: r0 = RoundedRectangleBorder()
    //     0xaf9d30: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaf9d34: mov             x1, x0
    // 0xaf9d38: ldur            x0, [fp, #-0x50]
    // 0xaf9d3c: StoreField: r1->field_b = r0
    //     0xaf9d3c: stur            w0, [x1, #0xb]
    // 0xaf9d40: r0 = Instance_BorderSide
    //     0xaf9d40: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xaf9d44: ldr             x0, [x0, #0xe20]
    // 0xaf9d48: StoreField: r1->field_7 = r0
    //     0xaf9d48: stur            w0, [x1, #7]
    // 0xaf9d4c: r16 = <RoundedRectangleBorder>
    //     0xaf9d4c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaf9d50: ldr             x16, [x16, #0xf78]
    // 0xaf9d54: stp             x1, x16, [SP]
    // 0xaf9d58: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf9d58: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf9d5c: r0 = all()
    //     0xaf9d5c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf9d60: stur            x0, [fp, #-0x48]
    // 0xaf9d64: r0 = ButtonStyle()
    //     0xaf9d64: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaf9d68: mov             x1, x0
    // 0xaf9d6c: ldur            x0, [fp, #-0x40]
    // 0xaf9d70: stur            x1, [fp, #-0x50]
    // 0xaf9d74: StoreField: r1->field_b = r0
    //     0xaf9d74: stur            w0, [x1, #0xb]
    // 0xaf9d78: ldur            x0, [fp, #-0x28]
    // 0xaf9d7c: StoreField: r1->field_23 = r0
    //     0xaf9d7c: stur            w0, [x1, #0x23]
    // 0xaf9d80: ldur            x0, [fp, #-0x48]
    // 0xaf9d84: StoreField: r1->field_43 = r0
    //     0xaf9d84: stur            w0, [x1, #0x43]
    // 0xaf9d88: r0 = TextButtonThemeData()
    //     0xaf9d88: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xaf9d8c: mov             x2, x0
    // 0xaf9d90: ldur            x0, [fp, #-0x50]
    // 0xaf9d94: stur            x2, [fp, #-0x28]
    // 0xaf9d98: StoreField: r2->field_7 = r0
    //     0xaf9d98: stur            w0, [x2, #7]
    // 0xaf9d9c: ldur            x1, [fp, #-0x10]
    // 0xaf9da0: r0 = of()
    //     0xaf9da0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf9da4: LoadField: r1 = r0->field_87
    //     0xaf9da4: ldur            w1, [x0, #0x87]
    // 0xaf9da8: DecompressPointer r1
    //     0xaf9da8: add             x1, x1, HEAP, lsl #32
    // 0xaf9dac: LoadField: r0 = r1->field_7
    //     0xaf9dac: ldur            w0, [x1, #7]
    // 0xaf9db0: DecompressPointer r0
    //     0xaf9db0: add             x0, x0, HEAP, lsl #32
    // 0xaf9db4: r16 = 14.000000
    //     0xaf9db4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf9db8: ldr             x16, [x16, #0x1d8]
    // 0xaf9dbc: r30 = Instance_Color
    //     0xaf9dbc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xaf9dc0: stp             lr, x16, [SP]
    // 0xaf9dc4: mov             x1, x0
    // 0xaf9dc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf9dc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf9dcc: ldr             x4, [x4, #0xaa0]
    // 0xaf9dd0: r0 = copyWith()
    //     0xaf9dd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf9dd4: stur            x0, [fp, #-0x40]
    // 0xaf9dd8: r0 = Text()
    //     0xaf9dd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf9ddc: mov             x3, x0
    // 0xaf9de0: r0 = "Continue"
    //     0xaf9de0: add             x0, PP, #0x37, lsl #12  ; [pp+0x37fe0] "Continue"
    //     0xaf9de4: ldr             x0, [x0, #0xfe0]
    // 0xaf9de8: stur            x3, [fp, #-0x48]
    // 0xaf9dec: StoreField: r3->field_b = r0
    //     0xaf9dec: stur            w0, [x3, #0xb]
    // 0xaf9df0: ldur            x0, [fp, #-0x40]
    // 0xaf9df4: StoreField: r3->field_13 = r0
    //     0xaf9df4: stur            w0, [x3, #0x13]
    // 0xaf9df8: ldur            x2, [fp, #-0x20]
    // 0xaf9dfc: r1 = Function '<anonymous closure>':.
    //     0xaf9dfc: add             x1, PP, #0x58, lsl #12  ; [pp+0x58418] AnonymousClosure: (0xafa398), in [package:customer_app/app/presentation/views/cosmetic/orders/cancel_return_order_with_free_product_bottom_sheet.dart] _CancelReturnOrderWithFreeProductBottomSheetState::build (0xaf829c)
    //     0xaf9e00: ldr             x1, [x1, #0x418]
    // 0xaf9e04: r0 = AllocateClosure()
    //     0xaf9e04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xaf9e08: stur            x0, [fp, #-0x20]
    // 0xaf9e0c: r0 = TextButton()
    //     0xaf9e0c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xaf9e10: mov             x1, x0
    // 0xaf9e14: ldur            x0, [fp, #-0x20]
    // 0xaf9e18: stur            x1, [fp, #-0x40]
    // 0xaf9e1c: StoreField: r1->field_b = r0
    //     0xaf9e1c: stur            w0, [x1, #0xb]
    // 0xaf9e20: r0 = false
    //     0xaf9e20: add             x0, NULL, #0x30  ; false
    // 0xaf9e24: StoreField: r1->field_27 = r0
    //     0xaf9e24: stur            w0, [x1, #0x27]
    // 0xaf9e28: r2 = true
    //     0xaf9e28: add             x2, NULL, #0x20  ; true
    // 0xaf9e2c: StoreField: r1->field_2f = r2
    //     0xaf9e2c: stur            w2, [x1, #0x2f]
    // 0xaf9e30: ldur            x3, [fp, #-0x48]
    // 0xaf9e34: StoreField: r1->field_37 = r3
    //     0xaf9e34: stur            w3, [x1, #0x37]
    // 0xaf9e38: r0 = TextButtonTheme()
    //     0xaf9e38: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xaf9e3c: mov             x2, x0
    // 0xaf9e40: ldur            x0, [fp, #-0x28]
    // 0xaf9e44: stur            x2, [fp, #-0x20]
    // 0xaf9e48: StoreField: r2->field_f = r0
    //     0xaf9e48: stur            w0, [x2, #0xf]
    // 0xaf9e4c: ldur            x0, [fp, #-0x40]
    // 0xaf9e50: StoreField: r2->field_b = r0
    //     0xaf9e50: stur            w0, [x2, #0xb]
    // 0xaf9e54: r1 = <FlexParentData>
    //     0xaf9e54: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xaf9e58: ldr             x1, [x1, #0xe00]
    // 0xaf9e5c: r0 = Expanded()
    //     0xaf9e5c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xaf9e60: mov             x1, x0
    // 0xaf9e64: r0 = 1
    //     0xaf9e64: movz            x0, #0x1
    // 0xaf9e68: stur            x1, [fp, #-0x28]
    // 0xaf9e6c: StoreField: r1->field_13 = r0
    //     0xaf9e6c: stur            x0, [x1, #0x13]
    // 0xaf9e70: r2 = Instance_FlexFit
    //     0xaf9e70: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xaf9e74: ldr             x2, [x2, #0xe08]
    // 0xaf9e78: StoreField: r1->field_1b = r2
    //     0xaf9e78: stur            w2, [x1, #0x1b]
    // 0xaf9e7c: ldur            x3, [fp, #-0x20]
    // 0xaf9e80: StoreField: r1->field_b = r3
    //     0xaf9e80: stur            w3, [x1, #0xb]
    // 0xaf9e84: r16 = <EdgeInsets>
    //     0xaf9e84: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xaf9e88: ldr             x16, [x16, #0xda0]
    // 0xaf9e8c: r30 = Instance_EdgeInsets
    //     0xaf9e8c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xaf9e90: ldr             lr, [lr, #0x1f0]
    // 0xaf9e94: stp             lr, x16, [SP]
    // 0xaf9e98: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf9e98: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf9e9c: r0 = all()
    //     0xaf9e9c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf9ea0: stur            x0, [fp, #-0x20]
    // 0xaf9ea4: r0 = Radius()
    //     0xaf9ea4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xaf9ea8: d0 = 20.000000
    //     0xaf9ea8: fmov            d0, #20.00000000
    // 0xaf9eac: stur            x0, [fp, #-0x40]
    // 0xaf9eb0: StoreField: r0->field_7 = d0
    //     0xaf9eb0: stur            d0, [x0, #7]
    // 0xaf9eb4: StoreField: r0->field_f = d0
    //     0xaf9eb4: stur            d0, [x0, #0xf]
    // 0xaf9eb8: r0 = BorderRadius()
    //     0xaf9eb8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xaf9ebc: mov             x2, x0
    // 0xaf9ec0: ldur            x0, [fp, #-0x40]
    // 0xaf9ec4: stur            x2, [fp, #-0x48]
    // 0xaf9ec8: StoreField: r2->field_7 = r0
    //     0xaf9ec8: stur            w0, [x2, #7]
    // 0xaf9ecc: StoreField: r2->field_b = r0
    //     0xaf9ecc: stur            w0, [x2, #0xb]
    // 0xaf9ed0: StoreField: r2->field_f = r0
    //     0xaf9ed0: stur            w0, [x2, #0xf]
    // 0xaf9ed4: StoreField: r2->field_13 = r0
    //     0xaf9ed4: stur            w0, [x2, #0x13]
    // 0xaf9ed8: ldur            x1, [fp, #-0x10]
    // 0xaf9edc: r0 = of()
    //     0xaf9edc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf9ee0: LoadField: r1 = r0->field_5b
    //     0xaf9ee0: ldur            w1, [x0, #0x5b]
    // 0xaf9ee4: DecompressPointer r1
    //     0xaf9ee4: add             x1, x1, HEAP, lsl #32
    // 0xaf9ee8: r0 = LoadClassIdInstr(r1)
    //     0xaf9ee8: ldur            x0, [x1, #-1]
    //     0xaf9eec: ubfx            x0, x0, #0xc, #0x14
    // 0xaf9ef0: d0 = 0.070000
    //     0xaf9ef0: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xaf9ef4: ldr             d0, [x17, #0x5f8]
    // 0xaf9ef8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xaf9ef8: sub             lr, x0, #0xffa
    //     0xaf9efc: ldr             lr, [x21, lr, lsl #3]
    //     0xaf9f00: blr             lr
    // 0xaf9f04: stur            x0, [fp, #-0x40]
    // 0xaf9f08: r0 = BorderSide()
    //     0xaf9f08: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xaf9f0c: mov             x1, x0
    // 0xaf9f10: ldur            x0, [fp, #-0x40]
    // 0xaf9f14: stur            x1, [fp, #-0x50]
    // 0xaf9f18: StoreField: r1->field_7 = r0
    //     0xaf9f18: stur            w0, [x1, #7]
    // 0xaf9f1c: d0 = 1.000000
    //     0xaf9f1c: fmov            d0, #1.00000000
    // 0xaf9f20: StoreField: r1->field_b = d0
    //     0xaf9f20: stur            d0, [x1, #0xb]
    // 0xaf9f24: r0 = Instance_BorderStyle
    //     0xaf9f24: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xaf9f28: ldr             x0, [x0, #0xf68]
    // 0xaf9f2c: StoreField: r1->field_13 = r0
    //     0xaf9f2c: stur            w0, [x1, #0x13]
    // 0xaf9f30: d0 = -1.000000
    //     0xaf9f30: fmov            d0, #-1.00000000
    // 0xaf9f34: ArrayStore: r1[0] = d0  ; List_8
    //     0xaf9f34: stur            d0, [x1, #0x17]
    // 0xaf9f38: r0 = RoundedRectangleBorder()
    //     0xaf9f38: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xaf9f3c: mov             x1, x0
    // 0xaf9f40: ldur            x0, [fp, #-0x48]
    // 0xaf9f44: StoreField: r1->field_b = r0
    //     0xaf9f44: stur            w0, [x1, #0xb]
    // 0xaf9f48: ldur            x0, [fp, #-0x50]
    // 0xaf9f4c: StoreField: r1->field_7 = r0
    //     0xaf9f4c: stur            w0, [x1, #7]
    // 0xaf9f50: r16 = <RoundedRectangleBorder>
    //     0xaf9f50: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xaf9f54: ldr             x16, [x16, #0xf78]
    // 0xaf9f58: stp             x1, x16, [SP]
    // 0xaf9f5c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaf9f5c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaf9f60: r0 = all()
    //     0xaf9f60: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xaf9f64: stur            x0, [fp, #-0x40]
    // 0xaf9f68: r0 = ButtonStyle()
    //     0xaf9f68: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xaf9f6c: mov             x1, x0
    // 0xaf9f70: ldur            x0, [fp, #-0x20]
    // 0xaf9f74: stur            x1, [fp, #-0x48]
    // 0xaf9f78: StoreField: r1->field_23 = r0
    //     0xaf9f78: stur            w0, [x1, #0x23]
    // 0xaf9f7c: ldur            x0, [fp, #-0x40]
    // 0xaf9f80: StoreField: r1->field_43 = r0
    //     0xaf9f80: stur            w0, [x1, #0x43]
    // 0xaf9f84: r0 = TextButtonThemeData()
    //     0xaf9f84: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xaf9f88: mov             x2, x0
    // 0xaf9f8c: ldur            x0, [fp, #-0x48]
    // 0xaf9f90: stur            x2, [fp, #-0x20]
    // 0xaf9f94: StoreField: r2->field_7 = r0
    //     0xaf9f94: stur            w0, [x2, #7]
    // 0xaf9f98: ldur            x1, [fp, #-0x10]
    // 0xaf9f9c: r0 = of()
    //     0xaf9f9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf9fa0: LoadField: r1 = r0->field_87
    //     0xaf9fa0: ldur            w1, [x0, #0x87]
    // 0xaf9fa4: DecompressPointer r1
    //     0xaf9fa4: add             x1, x1, HEAP, lsl #32
    // 0xaf9fa8: LoadField: r0 = r1->field_7
    //     0xaf9fa8: ldur            w0, [x1, #7]
    // 0xaf9fac: DecompressPointer r0
    //     0xaf9fac: add             x0, x0, HEAP, lsl #32
    // 0xaf9fb0: ldur            x1, [fp, #-0x10]
    // 0xaf9fb4: stur            x0, [fp, #-0x40]
    // 0xaf9fb8: r0 = of()
    //     0xaf9fb8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xaf9fbc: LoadField: r1 = r0->field_5b
    //     0xaf9fbc: ldur            w1, [x0, #0x5b]
    // 0xaf9fc0: DecompressPointer r1
    //     0xaf9fc0: add             x1, x1, HEAP, lsl #32
    // 0xaf9fc4: r16 = 14.000000
    //     0xaf9fc4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xaf9fc8: ldr             x16, [x16, #0x1d8]
    // 0xaf9fcc: stp             x1, x16, [SP]
    // 0xaf9fd0: ldur            x1, [fp, #-0x40]
    // 0xaf9fd4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xaf9fd4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xaf9fd8: ldr             x4, [x4, #0xaa0]
    // 0xaf9fdc: r0 = copyWith()
    //     0xaf9fdc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xaf9fe0: stur            x0, [fp, #-0x10]
    // 0xaf9fe4: r0 = Text()
    //     0xaf9fe4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xaf9fe8: mov             x3, x0
    // 0xaf9fec: r0 = "Go, Back"
    //     0xaf9fec: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3fe08] "Go, Back"
    //     0xaf9ff0: ldr             x0, [x0, #0xe08]
    // 0xaf9ff4: stur            x3, [fp, #-0x40]
    // 0xaf9ff8: StoreField: r3->field_b = r0
    //     0xaf9ff8: stur            w0, [x3, #0xb]
    // 0xaf9ffc: ldur            x0, [fp, #-0x10]
    // 0xafa000: StoreField: r3->field_13 = r0
    //     0xafa000: stur            w0, [x3, #0x13]
    // 0xafa004: r1 = Function '<anonymous closure>':.
    //     0xafa004: add             x1, PP, #0x58, lsl #12  ; [pp+0x58420] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xafa008: ldr             x1, [x1, #0x420]
    // 0xafa00c: r2 = Null
    //     0xafa00c: mov             x2, NULL
    // 0xafa010: r0 = AllocateClosure()
    //     0xafa010: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xafa014: stur            x0, [fp, #-0x10]
    // 0xafa018: r0 = TextButton()
    //     0xafa018: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xafa01c: mov             x1, x0
    // 0xafa020: ldur            x0, [fp, #-0x10]
    // 0xafa024: stur            x1, [fp, #-0x48]
    // 0xafa028: StoreField: r1->field_b = r0
    //     0xafa028: stur            w0, [x1, #0xb]
    // 0xafa02c: r0 = false
    //     0xafa02c: add             x0, NULL, #0x30  ; false
    // 0xafa030: StoreField: r1->field_27 = r0
    //     0xafa030: stur            w0, [x1, #0x27]
    // 0xafa034: r2 = true
    //     0xafa034: add             x2, NULL, #0x20  ; true
    // 0xafa038: StoreField: r1->field_2f = r2
    //     0xafa038: stur            w2, [x1, #0x2f]
    // 0xafa03c: ldur            x2, [fp, #-0x40]
    // 0xafa040: StoreField: r1->field_37 = r2
    //     0xafa040: stur            w2, [x1, #0x37]
    // 0xafa044: r0 = TextButtonTheme()
    //     0xafa044: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xafa048: mov             x2, x0
    // 0xafa04c: ldur            x0, [fp, #-0x20]
    // 0xafa050: stur            x2, [fp, #-0x10]
    // 0xafa054: StoreField: r2->field_f = r0
    //     0xafa054: stur            w0, [x2, #0xf]
    // 0xafa058: ldur            x0, [fp, #-0x48]
    // 0xafa05c: StoreField: r2->field_b = r0
    //     0xafa05c: stur            w0, [x2, #0xb]
    // 0xafa060: r1 = <FlexParentData>
    //     0xafa060: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xafa064: ldr             x1, [x1, #0xe00]
    // 0xafa068: r0 = Expanded()
    //     0xafa068: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xafa06c: mov             x3, x0
    // 0xafa070: r0 = 1
    //     0xafa070: movz            x0, #0x1
    // 0xafa074: stur            x3, [fp, #-0x20]
    // 0xafa078: StoreField: r3->field_13 = r0
    //     0xafa078: stur            x0, [x3, #0x13]
    // 0xafa07c: r0 = Instance_FlexFit
    //     0xafa07c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xafa080: ldr             x0, [x0, #0xe08]
    // 0xafa084: StoreField: r3->field_1b = r0
    //     0xafa084: stur            w0, [x3, #0x1b]
    // 0xafa088: ldur            x0, [fp, #-0x10]
    // 0xafa08c: StoreField: r3->field_b = r0
    //     0xafa08c: stur            w0, [x3, #0xb]
    // 0xafa090: r1 = Null
    //     0xafa090: mov             x1, NULL
    // 0xafa094: r2 = 6
    //     0xafa094: movz            x2, #0x6
    // 0xafa098: r0 = AllocateArray()
    //     0xafa098: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafa09c: mov             x2, x0
    // 0xafa0a0: ldur            x0, [fp, #-0x28]
    // 0xafa0a4: stur            x2, [fp, #-0x10]
    // 0xafa0a8: StoreField: r2->field_f = r0
    //     0xafa0a8: stur            w0, [x2, #0xf]
    // 0xafa0ac: r16 = Instance_SizedBox
    //     0xafa0ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xafa0b0: ldr             x16, [x16, #0xb20]
    // 0xafa0b4: StoreField: r2->field_13 = r16
    //     0xafa0b4: stur            w16, [x2, #0x13]
    // 0xafa0b8: ldur            x0, [fp, #-0x20]
    // 0xafa0bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xafa0bc: stur            w0, [x2, #0x17]
    // 0xafa0c0: r1 = <Widget>
    //     0xafa0c0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafa0c4: r0 = AllocateGrowableArray()
    //     0xafa0c4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafa0c8: mov             x1, x0
    // 0xafa0cc: ldur            x0, [fp, #-0x10]
    // 0xafa0d0: stur            x1, [fp, #-0x20]
    // 0xafa0d4: StoreField: r1->field_f = r0
    //     0xafa0d4: stur            w0, [x1, #0xf]
    // 0xafa0d8: r0 = 6
    //     0xafa0d8: movz            x0, #0x6
    // 0xafa0dc: StoreField: r1->field_b = r0
    //     0xafa0dc: stur            w0, [x1, #0xb]
    // 0xafa0e0: r0 = Row()
    //     0xafa0e0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xafa0e4: mov             x1, x0
    // 0xafa0e8: r0 = Instance_Axis
    //     0xafa0e8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xafa0ec: stur            x1, [fp, #-0x28]
    // 0xafa0f0: StoreField: r1->field_f = r0
    //     0xafa0f0: stur            w0, [x1, #0xf]
    // 0xafa0f4: r0 = Instance_MainAxisAlignment
    //     0xafa0f4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xafa0f8: ldr             x0, [x0, #0xa8]
    // 0xafa0fc: StoreField: r1->field_13 = r0
    //     0xafa0fc: stur            w0, [x1, #0x13]
    // 0xafa100: r0 = Instance_MainAxisSize
    //     0xafa100: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafa104: ldr             x0, [x0, #0xa10]
    // 0xafa108: ArrayStore: r1[0] = r0  ; List_4
    //     0xafa108: stur            w0, [x1, #0x17]
    // 0xafa10c: r2 = Instance_CrossAxisAlignment
    //     0xafa10c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafa110: ldr             x2, [x2, #0x890]
    // 0xafa114: StoreField: r1->field_1b = r2
    //     0xafa114: stur            w2, [x1, #0x1b]
    // 0xafa118: r3 = Instance_VerticalDirection
    //     0xafa118: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafa11c: ldr             x3, [x3, #0xa20]
    // 0xafa120: StoreField: r1->field_23 = r3
    //     0xafa120: stur            w3, [x1, #0x23]
    // 0xafa124: r4 = Instance_Clip
    //     0xafa124: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafa128: ldr             x4, [x4, #0x38]
    // 0xafa12c: StoreField: r1->field_2b = r4
    //     0xafa12c: stur            w4, [x1, #0x2b]
    // 0xafa130: StoreField: r1->field_2f = rZR
    //     0xafa130: stur            xzr, [x1, #0x2f]
    // 0xafa134: ldur            x5, [fp, #-0x20]
    // 0xafa138: StoreField: r1->field_b = r5
    //     0xafa138: stur            w5, [x1, #0xb]
    // 0xafa13c: ldur            d0, [fp, #-0x70]
    // 0xafa140: r5 = inline_Allocate_Double()
    //     0xafa140: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xafa144: add             x5, x5, #0x10
    //     0xafa148: cmp             x6, x5
    //     0xafa14c: b.ls            #0xafa36c
    //     0xafa150: str             x5, [THR, #0x50]  ; THR::top
    //     0xafa154: sub             x5, x5, #0xf
    //     0xafa158: movz            x6, #0xe15c
    //     0xafa15c: movk            x6, #0x3, lsl #16
    //     0xafa160: stur            x6, [x5, #-1]
    // 0xafa164: StoreField: r5->field_7 = d0
    //     0xafa164: stur            d0, [x5, #7]
    // 0xafa168: stur            x5, [fp, #-0x10]
    // 0xafa16c: r0 = Container()
    //     0xafa16c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xafa170: stur            x0, [fp, #-0x20]
    // 0xafa174: ldur            x16, [fp, #-0x10]
    // 0xafa178: r30 = Instance_Color
    //     0xafa178: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xafa17c: stp             lr, x16, [SP, #8]
    // 0xafa180: ldur            x16, [fp, #-0x28]
    // 0xafa184: str             x16, [SP]
    // 0xafa188: mov             x1, x0
    // 0xafa18c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, width, 0x1, null]
    //     0xafa18c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33828] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "width", 0x1, Null]
    //     0xafa190: ldr             x4, [x4, #0x828]
    // 0xafa194: r0 = Container()
    //     0xafa194: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xafa198: r1 = Null
    //     0xafa198: mov             x1, NULL
    // 0xafa19c: r2 = 10
    //     0xafa19c: movz            x2, #0xa
    // 0xafa1a0: r0 = AllocateArray()
    //     0xafa1a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xafa1a4: mov             x2, x0
    // 0xafa1a8: ldur            x0, [fp, #-0x30]
    // 0xafa1ac: stur            x2, [fp, #-0x10]
    // 0xafa1b0: StoreField: r2->field_f = r0
    //     0xafa1b0: stur            w0, [x2, #0xf]
    // 0xafa1b4: ldur            x0, [fp, #-0x18]
    // 0xafa1b8: StoreField: r2->field_13 = r0
    //     0xafa1b8: stur            w0, [x2, #0x13]
    // 0xafa1bc: ldur            x0, [fp, #-0x38]
    // 0xafa1c0: ArrayStore: r2[0] = r0  ; List_4
    //     0xafa1c0: stur            w0, [x2, #0x17]
    // 0xafa1c4: ldur            x0, [fp, #-8]
    // 0xafa1c8: StoreField: r2->field_1b = r0
    //     0xafa1c8: stur            w0, [x2, #0x1b]
    // 0xafa1cc: ldur            x0, [fp, #-0x20]
    // 0xafa1d0: StoreField: r2->field_1f = r0
    //     0xafa1d0: stur            w0, [x2, #0x1f]
    // 0xafa1d4: r1 = <Widget>
    //     0xafa1d4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xafa1d8: r0 = AllocateGrowableArray()
    //     0xafa1d8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xafa1dc: mov             x1, x0
    // 0xafa1e0: ldur            x0, [fp, #-0x10]
    // 0xafa1e4: stur            x1, [fp, #-8]
    // 0xafa1e8: StoreField: r1->field_f = r0
    //     0xafa1e8: stur            w0, [x1, #0xf]
    // 0xafa1ec: r0 = 10
    //     0xafa1ec: movz            x0, #0xa
    // 0xafa1f0: StoreField: r1->field_b = r0
    //     0xafa1f0: stur            w0, [x1, #0xb]
    // 0xafa1f4: r0 = Column()
    //     0xafa1f4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xafa1f8: mov             x1, x0
    // 0xafa1fc: r0 = Instance_Axis
    //     0xafa1fc: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafa200: stur            x1, [fp, #-0x10]
    // 0xafa204: StoreField: r1->field_f = r0
    //     0xafa204: stur            w0, [x1, #0xf]
    // 0xafa208: r2 = Instance_MainAxisAlignment
    //     0xafa208: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xafa20c: ldr             x2, [x2, #0xa08]
    // 0xafa210: StoreField: r1->field_13 = r2
    //     0xafa210: stur            w2, [x1, #0x13]
    // 0xafa214: r2 = Instance_MainAxisSize
    //     0xafa214: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xafa218: ldr             x2, [x2, #0xa10]
    // 0xafa21c: ArrayStore: r1[0] = r2  ; List_4
    //     0xafa21c: stur            w2, [x1, #0x17]
    // 0xafa220: r2 = Instance_CrossAxisAlignment
    //     0xafa220: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xafa224: ldr             x2, [x2, #0x890]
    // 0xafa228: StoreField: r1->field_1b = r2
    //     0xafa228: stur            w2, [x1, #0x1b]
    // 0xafa22c: r2 = Instance_VerticalDirection
    //     0xafa22c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xafa230: ldr             x2, [x2, #0xa20]
    // 0xafa234: StoreField: r1->field_23 = r2
    //     0xafa234: stur            w2, [x1, #0x23]
    // 0xafa238: r2 = Instance_Clip
    //     0xafa238: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xafa23c: ldr             x2, [x2, #0x38]
    // 0xafa240: StoreField: r1->field_2b = r2
    //     0xafa240: stur            w2, [x1, #0x2b]
    // 0xafa244: StoreField: r1->field_2f = rZR
    //     0xafa244: stur            xzr, [x1, #0x2f]
    // 0xafa248: ldur            x2, [fp, #-8]
    // 0xafa24c: StoreField: r1->field_b = r2
    //     0xafa24c: stur            w2, [x1, #0xb]
    // 0xafa250: r0 = Padding()
    //     0xafa250: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xafa254: mov             x1, x0
    // 0xafa258: r0 = Instance_EdgeInsets
    //     0xafa258: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xafa25c: ldr             x0, [x0, #0x1f0]
    // 0xafa260: stur            x1, [fp, #-8]
    // 0xafa264: StoreField: r1->field_f = r0
    //     0xafa264: stur            w0, [x1, #0xf]
    // 0xafa268: ldur            x0, [fp, #-0x10]
    // 0xafa26c: StoreField: r1->field_b = r0
    //     0xafa26c: stur            w0, [x1, #0xb]
    // 0xafa270: r0 = SingleChildScrollView()
    //     0xafa270: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xafa274: r1 = Instance_Axis
    //     0xafa274: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xafa278: StoreField: r0->field_b = r1
    //     0xafa278: stur            w1, [x0, #0xb]
    // 0xafa27c: r1 = false
    //     0xafa27c: add             x1, NULL, #0x30  ; false
    // 0xafa280: StoreField: r0->field_f = r1
    //     0xafa280: stur            w1, [x0, #0xf]
    // 0xafa284: ldur            x1, [fp, #-8]
    // 0xafa288: StoreField: r0->field_23 = r1
    //     0xafa288: stur            w1, [x0, #0x23]
    // 0xafa28c: r1 = Instance_DragStartBehavior
    //     0xafa28c: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xafa290: StoreField: r0->field_27 = r1
    //     0xafa290: stur            w1, [x0, #0x27]
    // 0xafa294: r1 = Instance_Clip
    //     0xafa294: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xafa298: ldr             x1, [x1, #0x7e0]
    // 0xafa29c: StoreField: r0->field_2b = r1
    //     0xafa29c: stur            w1, [x0, #0x2b]
    // 0xafa2a0: r1 = Instance_HitTestBehavior
    //     0xafa2a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xafa2a4: ldr             x1, [x1, #0x288]
    // 0xafa2a8: StoreField: r0->field_2f = r1
    //     0xafa2a8: stur            w1, [x0, #0x2f]
    // 0xafa2ac: LeaveFrame
    //     0xafa2ac: mov             SP, fp
    //     0xafa2b0: ldp             fp, lr, [SP], #0x10
    // 0xafa2b4: ret
    //     0xafa2b4: ret             
    // 0xafa2b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafa2b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafa2bc: b               #0xaf82c4
    // 0xafa2c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2c8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2cc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2d4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa2fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa2fc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa300: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa304: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa308: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa30c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa30c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa310: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa310: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa314: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa314: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa318: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa318: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa31c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa31c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa320: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa320: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa324: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa324: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa328: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa328: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa32c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa32c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa330: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa330: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa334: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa334: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa338: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa338: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa33c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa33c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa340: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa344: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa348: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa348: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa34c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa34c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa350: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa350: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa354: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa358: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa358: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa35c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa35c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa360: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa360: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa364: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa368: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xafa36c: SaveReg d0
    //     0xafa36c: str             q0, [SP, #-0x10]!
    // 0xafa370: stp             x3, x4, [SP, #-0x10]!
    // 0xafa374: stp             x1, x2, [SP, #-0x10]!
    // 0xafa378: SaveReg r0
    //     0xafa378: str             x0, [SP, #-8]!
    // 0xafa37c: r0 = AllocateDouble()
    //     0xafa37c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xafa380: mov             x5, x0
    // 0xafa384: RestoreReg r0
    //     0xafa384: ldr             x0, [SP], #8
    // 0xafa388: ldp             x1, x2, [SP], #0x10
    // 0xafa38c: ldp             x3, x4, [SP], #0x10
    // 0xafa390: RestoreReg d0
    //     0xafa390: ldr             q0, [SP], #0x10
    // 0xafa394: b               #0xafa164
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xafa398, size: 0x7c
    // 0xafa398: EnterFrame
    //     0xafa398: stp             fp, lr, [SP, #-0x10]!
    //     0xafa39c: mov             fp, SP
    // 0xafa3a0: AllocStack(0x8)
    //     0xafa3a0: sub             SP, SP, #8
    // 0xafa3a4: SetupParameters()
    //     0xafa3a4: ldr             x0, [fp, #0x10]
    //     0xafa3a8: ldur            w1, [x0, #0x17]
    //     0xafa3ac: add             x1, x1, HEAP, lsl #32
    // 0xafa3b0: CheckStackOverflow
    //     0xafa3b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xafa3b4: cmp             SP, x16
    //     0xafa3b8: b.ls            #0xafa408
    // 0xafa3bc: LoadField: r0 = r1->field_f
    //     0xafa3bc: ldur            w0, [x1, #0xf]
    // 0xafa3c0: DecompressPointer r0
    //     0xafa3c0: add             x0, x0, HEAP, lsl #32
    // 0xafa3c4: LoadField: r1 = r0->field_b
    //     0xafa3c4: ldur            w1, [x0, #0xb]
    // 0xafa3c8: DecompressPointer r1
    //     0xafa3c8: add             x1, x1, HEAP, lsl #32
    // 0xafa3cc: cmp             w1, NULL
    // 0xafa3d0: b.eq            #0xafa410
    // 0xafa3d4: LoadField: r0 = r1->field_b
    //     0xafa3d4: ldur            w0, [x1, #0xb]
    // 0xafa3d8: DecompressPointer r0
    //     0xafa3d8: add             x0, x0, HEAP, lsl #32
    // 0xafa3dc: str             x0, [SP]
    // 0xafa3e0: r4 = 0
    //     0xafa3e0: movz            x4, #0
    // 0xafa3e4: ldr             x0, [SP]
    // 0xafa3e8: r16 = UnlinkedCall_0x613b5c
    //     0xafa3e8: add             x16, PP, #0x58, lsl #12  ; [pp+0x58428] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xafa3ec: add             x16, x16, #0x428
    // 0xafa3f0: ldp             x5, lr, [x16]
    // 0xafa3f4: blr             lr
    // 0xafa3f8: r0 = Null
    //     0xafa3f8: mov             x0, NULL
    // 0xafa3fc: LeaveFrame
    //     0xafa3fc: mov             SP, fp
    //     0xafa400: ldp             fp, lr, [SP], #0x10
    // 0xafa404: ret
    //     0xafa404: ret             
    // 0xafa408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xafa408: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xafa40c: b               #0xafa3bc
    // 0xafa410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xafa410: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4153, size: 0x28, field offset: 0xc
//   const constructor, 
class CancelReturnOrderWithFreeProductBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7dd38, size: 0x24
    // 0xc7dd38: EnterFrame
    //     0xc7dd38: stp             fp, lr, [SP, #-0x10]!
    //     0xc7dd3c: mov             fp, SP
    // 0xc7dd40: mov             x0, x1
    // 0xc7dd44: r1 = <CancelReturnOrderWithFreeProductBottomSheet>
    //     0xc7dd44: add             x1, PP, #0x48, lsl #12  ; [pp+0x48bf8] TypeArguments: <CancelReturnOrderWithFreeProductBottomSheet>
    //     0xc7dd48: ldr             x1, [x1, #0xbf8]
    // 0xc7dd4c: r0 = _CancelReturnOrderWithFreeProductBottomSheetState()
    //     0xc7dd4c: bl              #0xc7dd5c  ; Allocate_CancelReturnOrderWithFreeProductBottomSheetStateStub -> _CancelReturnOrderWithFreeProductBottomSheetState (size=0x14)
    // 0xc7dd50: LeaveFrame
    //     0xc7dd50: mov             SP, fp
    //     0xc7dd54: ldp             fp, lr, [SP], #0x10
    // 0xc7dd58: ret
    //     0xc7dd58: ret             
  }
}
