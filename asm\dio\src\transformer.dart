// lib: , url: package:dio/src/transformer.dart

// class id: 1049615, size: 0x8
class :: {
}

// class id: 4971, size: 0x8, field offset: 0x8
abstract class Transformer extends Object {

  static _ isJsonMimeType(/* No info */) {
    // ** addr: 0x865e30, size: 0x1a4
    // 0x865e30: EnterFrame
    //     0x865e30: stp             fp, lr, [SP, #-0x10]!
    //     0x865e34: mov             fp, SP
    // 0x865e38: AllocStack(0x88)
    //     0x865e38: sub             SP, SP, #0x88
    // 0x865e3c: SetupParameters(dynamic _ /* r1 => r0, fp-0x58 */)
    //     0x865e3c: mov             x0, x1
    //     0x865e40: stur            x1, [fp, #-0x58]
    // 0x865e44: CheckStackOverflow
    //     0x865e44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865e48: cmp             SP, x16
    //     0x865e4c: b.ls            #0x865fcc
    // 0x865e50: cmp             w0, NULL
    // 0x865e54: b.ne            #0x865e68
    // 0x865e58: r0 = false
    //     0x865e58: add             x0, NULL, #0x30  ; false
    // 0x865e5c: LeaveFrame
    //     0x865e5c: mov             SP, fp
    //     0x865e60: ldp             fp, lr, [SP], #0x10
    // 0x865e64: ret
    //     0x865e64: ret             
    // 0x865e68: mov             x2, x0
    // 0x865e6c: r1 = Null
    //     0x865e6c: mov             x1, NULL
    // 0x865e70: r0 = MediaType.parse()
    //     0x865e70: bl              #0x865fd4  ; [package:http_parser/src/media_type.dart] MediaType::MediaType.parse
    // 0x865e74: r1 = Null
    //     0x865e74: mov             x1, NULL
    // 0x865e78: r2 = 6
    //     0x865e78: movz            x2, #0x6
    // 0x865e7c: stur            x0, [fp, #-0x60]
    // 0x865e80: r0 = AllocateArray()
    //     0x865e80: bl              #0x16f7198  ; AllocateArrayStub
    // 0x865e84: mov             x1, x0
    // 0x865e88: ldur            x0, [fp, #-0x60]
    // 0x865e8c: LoadField: r2 = r0->field_7
    //     0x865e8c: ldur            w2, [x0, #7]
    // 0x865e90: DecompressPointer r2
    //     0x865e90: add             x2, x2, HEAP, lsl #32
    // 0x865e94: stur            x2, [fp, #-0x70]
    // 0x865e98: StoreField: r1->field_f = r2
    //     0x865e98: stur            w2, [x1, #0xf]
    // 0x865e9c: r16 = "/"
    //     0x865e9c: ldr             x16, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x865ea0: StoreField: r1->field_13 = r16
    //     0x865ea0: stur            w16, [x1, #0x13]
    // 0x865ea4: LoadField: r3 = r0->field_b
    //     0x865ea4: ldur            w3, [x0, #0xb]
    // 0x865ea8: DecompressPointer r3
    //     0x865ea8: add             x3, x3, HEAP, lsl #32
    // 0x865eac: stur            x3, [fp, #-0x68]
    // 0x865eb0: ArrayStore: r1[0] = r3  ; List_4
    //     0x865eb0: stur            w3, [x1, #0x17]
    // 0x865eb4: str             x1, [SP]
    // 0x865eb8: r0 = _interpolate()
    //     0x865eb8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x865ebc: r1 = LoadClassIdInstr(r0)
    //     0x865ebc: ldur            x1, [x0, #-1]
    //     0x865ec0: ubfx            x1, x1, #0xc, #0x14
    // 0x865ec4: r16 = "application/json"
    //     0x865ec4: add             x16, PP, #8, lsl #12  ; [pp+0x8740] "application/json"
    //     0x865ec8: ldr             x16, [x16, #0x740]
    // 0x865ecc: stp             x16, x0, [SP]
    // 0x865ed0: mov             x0, x1
    // 0x865ed4: mov             lr, x0
    // 0x865ed8: ldr             lr, [x21, lr, lsl #3]
    // 0x865edc: blr             lr
    // 0x865ee0: tbz             w0, #4, #0x865f44
    // 0x865ee4: ldur            x0, [fp, #-0x70]
    // 0x865ee8: ldur            x3, [fp, #-0x68]
    // 0x865eec: r1 = Null
    //     0x865eec: mov             x1, NULL
    // 0x865ef0: r2 = 6
    //     0x865ef0: movz            x2, #0x6
    // 0x865ef4: r0 = AllocateArray()
    //     0x865ef4: bl              #0x16f7198  ; AllocateArrayStub
    // 0x865ef8: mov             x1, x0
    // 0x865efc: ldur            x0, [fp, #-0x70]
    // 0x865f00: StoreField: r1->field_f = r0
    //     0x865f00: stur            w0, [x1, #0xf]
    // 0x865f04: r16 = "/"
    //     0x865f04: ldr             x16, [PP, #0xfa8]  ; [pp+0xfa8] "/"
    // 0x865f08: StoreField: r1->field_13 = r16
    //     0x865f08: stur            w16, [x1, #0x13]
    // 0x865f0c: ldur            x0, [fp, #-0x68]
    // 0x865f10: ArrayStore: r1[0] = r0  ; List_4
    //     0x865f10: stur            w0, [x1, #0x17]
    // 0x865f14: str             x1, [SP]
    // 0x865f18: r0 = _interpolate()
    //     0x865f18: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x865f1c: r1 = LoadClassIdInstr(r0)
    //     0x865f1c: ldur            x1, [x0, #-1]
    //     0x865f20: ubfx            x1, x1, #0xc, #0x14
    // 0x865f24: r16 = "text/json"
    //     0x865f24: add             x16, PP, #8, lsl #12  ; [pp+0x8748] "text/json"
    //     0x865f28: ldr             x16, [x16, #0x748]
    // 0x865f2c: stp             x16, x0, [SP]
    // 0x865f30: mov             x0, x1
    // 0x865f34: mov             lr, x0
    // 0x865f38: ldr             lr, [x21, lr, lsl #3]
    // 0x865f3c: blr             lr
    // 0x865f40: tbnz            w0, #4, #0x865f4c
    // 0x865f44: r0 = true
    //     0x865f44: add             x0, NULL, #0x20  ; true
    // 0x865f48: b               #0x865f74
    // 0x865f4c: ldur            x0, [fp, #-0x68]
    // 0x865f50: LoadField: r1 = r0->field_7
    //     0x865f50: ldur            w1, [x0, #7]
    // 0x865f54: r2 = LoadInt32Instr(r1)
    //     0x865f54: sbfx            x2, x1, #1, #0x1f
    // 0x865f58: sub             x1, x2, #5
    // 0x865f5c: lsl             x2, x1, #1
    // 0x865f60: stp             x2, x0, [SP, #8]
    // 0x865f64: r16 = "+json"
    //     0x865f64: add             x16, PP, #8, lsl #12  ; [pp+0x8750] "+json"
    //     0x865f68: ldr             x16, [x16, #0x750]
    // 0x865f6c: str             x16, [SP]
    // 0x865f70: r0 = _substringMatches()
    //     0x865f70: bl              #0x627b40  ; [dart:core] _StringBase::_substringMatches
    // 0x865f74: LeaveFrame
    //     0x865f74: mov             SP, fp
    //     0x865f78: ldp             fp, lr, [SP], #0x10
    // 0x865f7c: ret
    //     0x865f7c: ret             
    // 0x865f80: sub             SP, fp, #0x88
    // 0x865f84: ldur            x3, [fp, #-0x58]
    // 0x865f88: r1 = Null
    //     0x865f88: mov             x1, NULL
    // 0x865f8c: r2 = 6
    //     0x865f8c: movz            x2, #0x6
    // 0x865f90: r0 = AllocateArray()
    //     0x865f90: bl              #0x16f7198  ; AllocateArrayStub
    // 0x865f94: r16 = "Failed to parse the media type: "
    //     0x865f94: add             x16, PP, #8, lsl #12  ; [pp+0x8758] "Failed to parse the media type: "
    //     0x865f98: ldr             x16, [x16, #0x758]
    // 0x865f9c: StoreField: r0->field_f = r16
    //     0x865f9c: stur            w16, [x0, #0xf]
    // 0x865fa0: ldur            x1, [fp, #-0x58]
    // 0x865fa4: StoreField: r0->field_13 = r1
    //     0x865fa4: stur            w1, [x0, #0x13]
    // 0x865fa8: r16 = ", thus it is not a JSON MIME type."
    //     0x865fa8: add             x16, PP, #8, lsl #12  ; [pp+0x8760] ", thus it is not a JSON MIME type."
    //     0x865fac: ldr             x16, [x16, #0x760]
    // 0x865fb0: ArrayStore: r0[0] = r16  ; List_4
    //     0x865fb0: stur            w16, [x0, #0x17]
    // 0x865fb4: str             x0, [SP]
    // 0x865fb8: r0 = _interpolate()
    //     0x865fb8: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x865fbc: r0 = false
    //     0x865fbc: add             x0, NULL, #0x30  ; false
    // 0x865fc0: LeaveFrame
    //     0x865fc0: mov             SP, fp
    //     0x865fc4: ldp             fp, lr, [SP], #0x10
    // 0x865fc8: ret
    //     0x865fc8: ret             
    // 0x865fcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865fcc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865fd0: b               #0x865e50
  }
  static _ urlEncodeQueryMap(/* No info */) {
    // ** addr: 0x88036c, size: 0x60
    // 0x88036c: EnterFrame
    //     0x88036c: stp             fp, lr, [SP, #-0x10]!
    //     0x880370: mov             fp, SP
    // 0x880374: AllocStack(0x10)
    //     0x880374: sub             SP, SP, #0x10
    // 0x880378: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x880378: mov             x0, x1
    //     0x88037c: stur            x1, [fp, #-8]
    // 0x880380: CheckStackOverflow
    //     0x880380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x880384: cmp             SP, x16
    //     0x880388: b.ls            #0x8803c4
    // 0x88038c: r1 = Function '<anonymous closure>': static.
    //     0x88038c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa3e8] AnonymousClosure: static (0x881108), in [package:dio/src/transformer.dart] Transformer::urlEncodeQueryMap (0x88036c)
    //     0x880390: ldr             x1, [x1, #0x3e8]
    // 0x880394: r2 = Null
    //     0x880394: mov             x2, NULL
    // 0x880398: r0 = AllocateClosure()
    //     0x880398: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x88039c: r16 = true
    //     0x88039c: add             x16, NULL, #0x20  ; true
    // 0x8803a0: str             x16, [SP]
    // 0x8803a4: ldur            x1, [fp, #-8]
    // 0x8803a8: mov             x2, x0
    // 0x8803ac: r4 = const [0, 0x3, 0x1, 0x2, isQuery, 0x2, null]
    //     0x8803ac: add             x4, PP, #0xa, lsl #12  ; [pp+0xa3f0] List(7) [0, 0x3, 0x1, 0x2, "isQuery", 0x2, Null]
    //     0x8803b0: ldr             x4, [x4, #0x3f0]
    // 0x8803b4: r0 = encodeMap()
    //     0x8803b4: bl              #0x8803cc  ; [package:dio/src/utils.dart] ::encodeMap
    // 0x8803b8: LeaveFrame
    //     0x8803b8: mov             SP, fp
    //     0x8803bc: ldp             fp, lr, [SP], #0x10
    // 0x8803c0: ret
    //     0x8803c0: ret             
    // 0x8803c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8803c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8803c8: b               #0x88038c
  }
  [closure] static String <anonymous closure>(dynamic, String, Object?) {
    // ** addr: 0x881108, size: 0x7c
    // 0x881108: EnterFrame
    //     0x881108: stp             fp, lr, [SP, #-0x10]!
    //     0x88110c: mov             fp, SP
    // 0x881110: AllocStack(0x8)
    //     0x881110: sub             SP, SP, #8
    // 0x881114: CheckStackOverflow
    //     0x881114: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x881118: cmp             SP, x16
    //     0x88111c: b.ls            #0x88117c
    // 0x881120: ldr             x0, [fp, #0x10]
    // 0x881124: cmp             w0, NULL
    // 0x881128: b.ne            #0x88113c
    // 0x88112c: ldr             x0, [fp, #0x18]
    // 0x881130: LeaveFrame
    //     0x881130: mov             SP, fp
    //     0x881134: ldp             fp, lr, [SP], #0x10
    // 0x881138: ret
    //     0x881138: ret             
    // 0x88113c: ldr             x3, [fp, #0x18]
    // 0x881140: r1 = Null
    //     0x881140: mov             x1, NULL
    // 0x881144: r2 = 6
    //     0x881144: movz            x2, #0x6
    // 0x881148: r0 = AllocateArray()
    //     0x881148: bl              #0x16f7198  ; AllocateArrayStub
    // 0x88114c: mov             x1, x0
    // 0x881150: ldr             x0, [fp, #0x18]
    // 0x881154: StoreField: r1->field_f = r0
    //     0x881154: stur            w0, [x1, #0xf]
    // 0x881158: r16 = "="
    //     0x881158: ldr             x16, [PP, #0x1190]  ; [pp+0x1190] "="
    // 0x88115c: StoreField: r1->field_13 = r16
    //     0x88115c: stur            w16, [x1, #0x13]
    // 0x881160: ldr             x0, [fp, #0x10]
    // 0x881164: ArrayStore: r1[0] = r0  ; List_4
    //     0x881164: stur            w0, [x1, #0x17]
    // 0x881168: str             x1, [SP]
    // 0x88116c: r0 = _interpolate()
    //     0x88116c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x881170: LeaveFrame
    //     0x881170: mov             SP, fp
    //     0x881174: ldp             fp, lr, [SP], #0x10
    // 0x881178: ret
    //     0x881178: ret             
    // 0x88117c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88117c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x881180: b               #0x881120
  }
  static _ defaultTransformRequest(/* No info */) {
    // ** addr: 0x8829e0, size: 0x2d4
    // 0x8829e0: EnterFrame
    //     0x8829e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8829e4: mov             fp, SP
    // 0x8829e8: AllocStack(0x18)
    //     0x8829e8: sub             SP, SP, #0x18
    // 0x8829ec: SetupParameters(dynamic _ /* r1 => r0, fp-0x10 */)
    //     0x8829ec: mov             x0, x1
    //     0x8829f0: stur            x1, [fp, #-0x10]
    // 0x8829f4: CheckStackOverflow
    //     0x8829f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8829f8: cmp             SP, x16
    //     0x8829fc: b.ls            #0x882ca0
    // 0x882a00: LoadField: r1 = r0->field_57
    //     0x882a00: ldur            w1, [x0, #0x57]
    // 0x882a04: DecompressPointer r1
    //     0x882a04: add             x1, x1, HEAP, lsl #32
    // 0x882a08: cmp             w1, NULL
    // 0x882a0c: b.ne            #0x882a18
    // 0x882a10: r2 = ""
    //     0x882a10: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x882a14: b               #0x882a1c
    // 0x882a18: mov             x2, x1
    // 0x882a1c: stur            x2, [fp, #-8]
    // 0x882a20: r1 = 60
    //     0x882a20: movz            x1, #0x3c
    // 0x882a24: branchIfSmi(r2, 0x882a30)
    //     0x882a24: tbz             w2, #0, #0x882a30
    // 0x882a28: r1 = LoadClassIdInstr(r2)
    //     0x882a28: ldur            x1, [x2, #-1]
    //     0x882a2c: ubfx            x1, x1, #0xc, #0x14
    // 0x882a30: sub             x16, x1, #0x5e
    // 0x882a34: cmp             x16, #1
    // 0x882a38: b.ls            #0x882a70
    // 0x882a3c: mov             x1, x0
    // 0x882a40: r0 = contentType()
    //     0x882a40: bl              #0x882e68  ; [package:dio/src/options.dart] _RequestConfig::contentType
    // 0x882a44: mov             x1, x0
    // 0x882a48: r0 = isJsonMimeType()
    //     0x882a48: bl              #0x865e30  ; [package:dio/src/transformer.dart] Transformer::isJsonMimeType
    // 0x882a4c: tbnz            w0, #4, #0x882a70
    // 0x882a50: str             NULL, [SP]
    // 0x882a54: ldur            x1, [fp, #-8]
    // 0x882a58: r4 = const [0, 0x2, 0x1, 0x1, toEncodable, 0x1, null]
    //     0x882a58: add             x4, PP, #0xa, lsl #12  ; [pp+0xa5d0] List(7) [0, 0x2, 0x1, 0x1, "toEncodable", 0x1, Null]
    //     0x882a5c: ldr             x4, [x4, #0x5d0]
    // 0x882a60: r0 = jsonEncode()
    //     0x882a60: bl              #0x882de8  ; [dart:convert] ::jsonEncode
    // 0x882a64: LeaveFrame
    //     0x882a64: mov             SP, fp
    //     0x882a68: ldp             fp, lr, [SP], #0x10
    // 0x882a6c: ret
    //     0x882a6c: ret             
    // 0x882a70: ldur            x0, [fp, #-8]
    // 0x882a74: r2 = Null
    //     0x882a74: mov             x2, NULL
    // 0x882a78: r1 = Null
    //     0x882a78: mov             x1, NULL
    // 0x882a7c: cmp             w0, NULL
    // 0x882a80: b.eq            #0x882b18
    // 0x882a84: branchIfSmi(r0, 0x882b18)
    //     0x882a84: tbz             w0, #0, #0x882b18
    // 0x882a88: r3 = LoadClassIdInstr(r0)
    //     0x882a88: ldur            x3, [x0, #-1]
    //     0x882a8c: ubfx            x3, x3, #0xc, #0x14
    // 0x882a90: r17 = 6675
    //     0x882a90: movz            x17, #0x1a13
    // 0x882a94: cmp             x3, x17
    // 0x882a98: b.eq            #0x882b20
    // 0x882a9c: r4 = LoadClassIdInstr(r0)
    //     0x882a9c: ldur            x4, [x0, #-1]
    //     0x882aa0: ubfx            x4, x4, #0xc, #0x14
    // 0x882aa4: ldr             x3, [THR, #0x778]  ; THR::isolate_group
    // 0x882aa8: ldr             x3, [x3, #0x18]
    // 0x882aac: ldr             x3, [x3, x4, lsl #3]
    // 0x882ab0: LoadField: r3 = r3->field_2b
    //     0x882ab0: ldur            w3, [x3, #0x2b]
    // 0x882ab4: DecompressPointer r3
    //     0x882ab4: add             x3, x3, HEAP, lsl #32
    // 0x882ab8: cmp             w3, NULL
    // 0x882abc: b.eq            #0x882b18
    // 0x882ac0: LoadField: r3 = r3->field_f
    //     0x882ac0: ldur            w3, [x3, #0xf]
    // 0x882ac4: lsr             x3, x3, #3
    // 0x882ac8: r17 = 6675
    //     0x882ac8: movz            x17, #0x1a13
    // 0x882acc: cmp             x3, x17
    // 0x882ad0: b.eq            #0x882b20
    // 0x882ad4: r3 = SubtypeTestCache
    //     0x882ad4: add             x3, PP, #0xa, lsl #12  ; [pp+0xa5d8] SubtypeTestCache
    //     0x882ad8: ldr             x3, [x3, #0x5d8]
    // 0x882adc: r30 = Subtype1TestCacheStub
    //     0x882adc: ldr             lr, [PP, #0x398]  ; [pp+0x398] Stub: Subtype1TestCache (0x612f30)
    // 0x882ae0: LoadField: r30 = r30->field_7
    //     0x882ae0: ldur            lr, [lr, #7]
    // 0x882ae4: blr             lr
    // 0x882ae8: cmp             w7, NULL
    // 0x882aec: b.eq            #0x882af8
    // 0x882af0: tbnz            w7, #4, #0x882b18
    // 0x882af4: b               #0x882b20
    // 0x882af8: r8 = Map
    //     0x882af8: add             x8, PP, #0xa, lsl #12  ; [pp+0xa5e0] Type: Map
    //     0x882afc: ldr             x8, [x8, #0x5e0]
    // 0x882b00: r3 = SubtypeTestCache
    //     0x882b00: add             x3, PP, #0xa, lsl #12  ; [pp+0xa5e8] SubtypeTestCache
    //     0x882b04: ldr             x3, [x3, #0x5e8]
    // 0x882b08: r30 = InstanceOfStub
    //     0x882b08: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x882b0c: LoadField: r30 = r30->field_7
    //     0x882b0c: ldur            lr, [lr, #7]
    // 0x882b10: blr             lr
    // 0x882b14: b               #0x882b24
    // 0x882b18: r0 = false
    //     0x882b18: add             x0, NULL, #0x30  ; false
    // 0x882b1c: b               #0x882b24
    // 0x882b20: r0 = true
    //     0x882b20: add             x0, NULL, #0x20  ; true
    // 0x882b24: tbnz            w0, #4, #0x882c64
    // 0x882b28: ldur            x0, [fp, #-8]
    // 0x882b2c: r2 = Null
    //     0x882b2c: mov             x2, NULL
    // 0x882b30: r1 = Null
    //     0x882b30: mov             x1, NULL
    // 0x882b34: cmp             w0, NULL
    // 0x882b38: b.eq            #0x882b84
    // 0x882b3c: branchIfSmi(r0, 0x882b84)
    //     0x882b3c: tbz             w0, #0, #0x882b84
    // 0x882b40: r3 = SubtypeTestCache
    //     0x882b40: add             x3, PP, #0xa, lsl #12  ; [pp+0xa5f0] SubtypeTestCache
    //     0x882b44: ldr             x3, [x3, #0x5f0]
    // 0x882b48: r30 = Subtype2TestCacheStub
    //     0x882b48: ldr             lr, [PP, #0x30]  ; [pp+0x30] Stub: Subtype2TestCache (0x612da8)
    // 0x882b4c: LoadField: r30 = r30->field_7
    //     0x882b4c: ldur            lr, [lr, #7]
    // 0x882b50: blr             lr
    // 0x882b54: cmp             w7, NULL
    // 0x882b58: b.eq            #0x882b64
    // 0x882b5c: tbnz            w7, #4, #0x882b84
    // 0x882b60: b               #0x882b8c
    // 0x882b64: r8 = Map<String, dynamic>
    //     0x882b64: add             x8, PP, #0xa, lsl #12  ; [pp+0xa5f8] Type: Map<String, dynamic>
    //     0x882b68: ldr             x8, [x8, #0x5f8]
    // 0x882b6c: r3 = SubtypeTestCache
    //     0x882b6c: add             x3, PP, #0xa, lsl #12  ; [pp+0xa600] SubtypeTestCache
    //     0x882b70: ldr             x3, [x3, #0x600]
    // 0x882b74: r30 = InstanceOfStub
    //     0x882b74: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x882b78: LoadField: r30 = r30->field_7
    //     0x882b78: ldur            lr, [lr, #7]
    // 0x882b7c: blr             lr
    // 0x882b80: b               #0x882b90
    // 0x882b84: r0 = false
    //     0x882b84: add             x0, NULL, #0x30  ; false
    // 0x882b88: b               #0x882b90
    // 0x882b8c: r0 = true
    //     0x882b8c: add             x0, NULL, #0x20  ; true
    // 0x882b90: tbnz            w0, #4, #0x882bc0
    // 0x882b94: ldur            x0, [fp, #-0x10]
    // 0x882b98: LoadField: r1 = r0->field_43
    //     0x882b98: ldur            w1, [x0, #0x43]
    // 0x882b9c: DecompressPointer r1
    //     0x882b9c: add             x1, x1, HEAP, lsl #32
    // 0x882ba0: r16 = Sentinel
    //     0x882ba0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x882ba4: cmp             w1, w16
    // 0x882ba8: b.eq            #0x882ca8
    // 0x882bac: ldur            x1, [fp, #-8]
    // 0x882bb0: r0 = urlEncodeMap()
    //     0x882bb0: bl              #0x882cb4  ; [package:dio/src/transformer.dart] Transformer::urlEncodeMap
    // 0x882bb4: LeaveFrame
    //     0x882bb4: mov             SP, fp
    //     0x882bb8: ldp             fp, lr, [SP], #0x10
    // 0x882bbc: ret
    //     0x882bbc: ret             
    // 0x882bc0: ldur            x0, [fp, #-8]
    // 0x882bc4: r1 = Null
    //     0x882bc4: mov             x1, NULL
    // 0x882bc8: r2 = 6
    //     0x882bc8: movz            x2, #0x6
    // 0x882bcc: r0 = AllocateArray()
    //     0x882bcc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x882bd0: stur            x0, [fp, #-0x10]
    // 0x882bd4: r16 = "The data is a type of `Map` ("
    //     0x882bd4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa608] "The data is a type of `Map` ("
    //     0x882bd8: ldr             x16, [x16, #0x608]
    // 0x882bdc: StoreField: r0->field_f = r16
    //     0x882bdc: stur            w16, [x0, #0xf]
    // 0x882be0: ldur            x16, [fp, #-8]
    // 0x882be4: str             x16, [SP]
    // 0x882be8: r0 = runtimeType()
    //     0x882be8: bl              #0x1538034  ; [dart:core] Object::runtimeType
    // 0x882bec: ldur            x1, [fp, #-0x10]
    // 0x882bf0: ArrayStore: r1[1] = r0  ; List_4
    //     0x882bf0: add             x25, x1, #0x13
    //     0x882bf4: str             w0, [x25]
    //     0x882bf8: tbz             w0, #0, #0x882c14
    //     0x882bfc: ldurb           w16, [x1, #-1]
    //     0x882c00: ldurb           w17, [x0, #-1]
    //     0x882c04: and             x16, x17, x16, lsr #2
    //     0x882c08: tst             x16, HEAP, lsr #32
    //     0x882c0c: b.eq            #0x882c14
    //     0x882c10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x882c14: ldur            x0, [fp, #-0x10]
    // 0x882c18: r16 = "), but the transformer can only encode `Map<String, dynamic>`.\nIf you are writing maps using `{}`, consider writing `<String, dynamic>{}`."
    //     0x882c18: add             x16, PP, #0xa, lsl #12  ; [pp+0xa610] "), but the transformer can only encode `Map<String, dynamic>`.\nIf you are writing maps using `{}`, consider writing `<String, dynamic>{}`."
    //     0x882c1c: ldr             x16, [x16, #0x610]
    // 0x882c20: ArrayStore: r0[0] = r16  ; List_4
    //     0x882c20: stur            w16, [x0, #0x17]
    // 0x882c24: str             x0, [SP]
    // 0x882c28: r0 = _interpolate()
    //     0x882c28: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x882c2c: r0 = current()
    //     0x882c2c: bl              #0x61b99c  ; [dart:core] StackTrace::current
    // 0x882c30: ldur            x0, [fp, #-8]
    // 0x882c34: r1 = LoadClassIdInstr(r0)
    //     0x882c34: ldur            x1, [x0, #-1]
    //     0x882c38: ubfx            x1, x1, #0xc, #0x14
    // 0x882c3c: str             x0, [SP]
    // 0x882c40: mov             x0, x1
    // 0x882c44: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x882c44: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x882c48: r0 = GDT[cid_x0 + 0x2700]()
    //     0x882c48: movz            x17, #0x2700
    //     0x882c4c: add             lr, x0, x17
    //     0x882c50: ldr             lr, [x21, lr, lsl #3]
    //     0x882c54: blr             lr
    // 0x882c58: LeaveFrame
    //     0x882c58: mov             SP, fp
    //     0x882c5c: ldp             fp, lr, [SP], #0x10
    // 0x882c60: ret
    //     0x882c60: ret             
    // 0x882c64: ldur            x0, [fp, #-8]
    // 0x882c68: r1 = 60
    //     0x882c68: movz            x1, #0x3c
    // 0x882c6c: branchIfSmi(r0, 0x882c78)
    //     0x882c6c: tbz             w0, #0, #0x882c78
    // 0x882c70: r1 = LoadClassIdInstr(r0)
    //     0x882c70: ldur            x1, [x0, #-1]
    //     0x882c74: ubfx            x1, x1, #0xc, #0x14
    // 0x882c78: str             x0, [SP]
    // 0x882c7c: mov             x0, x1
    // 0x882c80: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x882c80: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x882c84: r0 = GDT[cid_x0 + 0x2700]()
    //     0x882c84: movz            x17, #0x2700
    //     0x882c88: add             lr, x0, x17
    //     0x882c8c: ldr             lr, [x21, lr, lsl #3]
    //     0x882c90: blr             lr
    // 0x882c94: LeaveFrame
    //     0x882c94: mov             SP, fp
    //     0x882c98: ldp             fp, lr, [SP], #0x10
    // 0x882c9c: ret
    //     0x882c9c: ret             
    // 0x882ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x882ca0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x882ca4: b               #0x882a00
    // 0x882ca8: r9 = listFormat
    //     0x882ca8: add             x9, PP, #0xa, lsl #12  ; [pp+0xa3e0] Field <<EMAIL>>: late (offset: 0x44)
    //     0x882cac: ldr             x9, [x9, #0x3e0]
    // 0x882cb0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x882cb0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static _ urlEncodeMap(/* No info */) {
    // ** addr: 0x882cb4, size: 0x54
    // 0x882cb4: EnterFrame
    //     0x882cb4: stp             fp, lr, [SP, #-0x10]!
    //     0x882cb8: mov             fp, SP
    // 0x882cbc: AllocStack(0x8)
    //     0x882cbc: sub             SP, SP, #8
    // 0x882cc0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x882cc0: mov             x0, x1
    //     0x882cc4: stur            x1, [fp, #-8]
    // 0x882cc8: CheckStackOverflow
    //     0x882cc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x882ccc: cmp             SP, x16
    //     0x882cd0: b.ls            #0x882d00
    // 0x882cd4: r1 = Function '<anonymous closure>': static.
    //     0x882cd4: add             x1, PP, #0xa, lsl #12  ; [pp+0xa618] AnonymousClosure: static (0x882d08), in [package:dio/src/transformer.dart] Transformer::urlEncodeMap (0x882cb4)
    //     0x882cd8: ldr             x1, [x1, #0x618]
    // 0x882cdc: r2 = Null
    //     0x882cdc: mov             x2, NULL
    // 0x882ce0: r0 = AllocateClosure()
    //     0x882ce0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x882ce4: ldur            x1, [fp, #-8]
    // 0x882ce8: mov             x2, x0
    // 0x882cec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x882cec: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x882cf0: r0 = encodeMap()
    //     0x882cf0: bl              #0x8803cc  ; [package:dio/src/utils.dart] ::encodeMap
    // 0x882cf4: LeaveFrame
    //     0x882cf4: mov             SP, fp
    //     0x882cf8: ldp             fp, lr, [SP], #0x10
    // 0x882cfc: ret
    //     0x882cfc: ret             
    // 0x882d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x882d00: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x882d04: b               #0x882cd4
  }
  [closure] static String <anonymous closure>(dynamic, String, Object?) {
    // ** addr: 0x882d08, size: 0xe0
    // 0x882d08: EnterFrame
    //     0x882d08: stp             fp, lr, [SP, #-0x10]!
    //     0x882d0c: mov             fp, SP
    // 0x882d10: AllocStack(0x10)
    //     0x882d10: sub             SP, SP, #0x10
    // 0x882d14: CheckStackOverflow
    //     0x882d14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x882d18: cmp             SP, x16
    //     0x882d1c: b.ls            #0x882de0
    // 0x882d20: ldr             x0, [fp, #0x10]
    // 0x882d24: cmp             w0, NULL
    // 0x882d28: b.ne            #0x882d3c
    // 0x882d2c: ldr             x0, [fp, #0x18]
    // 0x882d30: LeaveFrame
    //     0x882d30: mov             SP, fp
    //     0x882d34: ldp             fp, lr, [SP], #0x10
    // 0x882d38: ret
    //     0x882d38: ret             
    // 0x882d3c: ldr             x3, [fp, #0x18]
    // 0x882d40: r1 = Null
    //     0x882d40: mov             x1, NULL
    // 0x882d44: r2 = 6
    //     0x882d44: movz            x2, #0x6
    // 0x882d48: r0 = AllocateArray()
    //     0x882d48: bl              #0x16f7198  ; AllocateArrayStub
    // 0x882d4c: mov             x1, x0
    // 0x882d50: ldr             x0, [fp, #0x18]
    // 0x882d54: stur            x1, [fp, #-8]
    // 0x882d58: StoreField: r1->field_f = r0
    //     0x882d58: stur            w0, [x1, #0xf]
    // 0x882d5c: r16 = "="
    //     0x882d5c: ldr             x16, [PP, #0x1190]  ; [pp+0x1190] "="
    // 0x882d60: StoreField: r1->field_13 = r16
    //     0x882d60: stur            w16, [x1, #0x13]
    // 0x882d64: ldr             x0, [fp, #0x10]
    // 0x882d68: r2 = 60
    //     0x882d68: movz            x2, #0x3c
    // 0x882d6c: branchIfSmi(r0, 0x882d78)
    //     0x882d6c: tbz             w0, #0, #0x882d78
    // 0x882d70: r2 = LoadClassIdInstr(r0)
    //     0x882d70: ldur            x2, [x0, #-1]
    //     0x882d74: ubfx            x2, x2, #0xc, #0x14
    // 0x882d78: str             x0, [SP]
    // 0x882d7c: mov             x0, x2
    // 0x882d80: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x882d80: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x882d84: r0 = GDT[cid_x0 + 0x2700]()
    //     0x882d84: movz            x17, #0x2700
    //     0x882d88: add             lr, x0, x17
    //     0x882d8c: ldr             lr, [x21, lr, lsl #3]
    //     0x882d90: blr             lr
    // 0x882d94: mov             x1, x0
    // 0x882d98: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x882d98: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x882d9c: r0 = encodeQueryComponent()
    //     0x882d9c: bl              #0x623b34  ; [dart:core] Uri::encodeQueryComponent
    // 0x882da0: ldur            x1, [fp, #-8]
    // 0x882da4: ArrayStore: r1[2] = r0  ; List_4
    //     0x882da4: add             x25, x1, #0x17
    //     0x882da8: str             w0, [x25]
    //     0x882dac: tbz             w0, #0, #0x882dc8
    //     0x882db0: ldurb           w16, [x1, #-1]
    //     0x882db4: ldurb           w17, [x0, #-1]
    //     0x882db8: and             x16, x17, x16, lsr #2
    //     0x882dbc: tst             x16, HEAP, lsr #32
    //     0x882dc0: b.eq            #0x882dc8
    //     0x882dc4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x882dc8: ldur            x16, [fp, #-8]
    // 0x882dcc: str             x16, [SP]
    // 0x882dd0: r0 = _interpolate()
    //     0x882dd0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x882dd4: LeaveFrame
    //     0x882dd4: mov             SP, fp
    //     0x882dd8: ldp             fp, lr, [SP], #0x10
    // 0x882ddc: ret
    //     0x882ddc: ret             
    // 0x882de0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x882de0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x882de4: b               #0x882d20
  }
}
