// lib: , url: package:easy_stepper/src/core/easy_line.dart

// class id: 1049629, size: 0x8
class :: {
}

// class id: 4477, size: 0x38, field offset: 0xc
//   const constructor, 
class EasyLine extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0x1299b0c, size: 0x610
    // 0x1299b0c: EnterFrame
    //     0x1299b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x1299b10: mov             fp, SP
    // 0x1299b14: AllocStack(0x88)
    //     0x1299b14: sub             SP, SP, #0x88
    // 0x1299b18: SetupParameters(EasyLine this /* r1 => r1, fp-0x10 */)
    //     0x1299b18: stur            x1, [fp, #-0x10]
    // 0x1299b1c: CheckStackOverflow
    //     0x1299b1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1299b20: cmp             SP, x16
    //     0x1299b24: b.ls            #0x129a078
    // 0x1299b28: LoadField: r0 = r1->field_2f
    //     0x1299b28: ldur            w0, [x1, #0x2f]
    // 0x1299b2c: DecompressPointer r0
    //     0x1299b2c: add             x0, x0, HEAP, lsl #32
    // 0x1299b30: stur            x0, [fp, #-8]
    // 0x1299b34: r16 = Instance_LineType
    //     0x1299b34: add             x16, PP, #0x6e, lsl #12  ; [pp+0x6ec58] Obj!LineType@d74fe1
    //     0x1299b38: ldr             x16, [x16, #0xc58]
    // 0x1299b3c: cmp             w0, w16
    // 0x1299b40: b.eq            #0x1299b54
    // 0x1299b44: r16 = Instance_LineType
    //     0x1299b44: add             x16, PP, #0x6e, lsl #12  ; [pp+0x6ec60] Obj!LineType@d74fc1
    //     0x1299b48: ldr             x16, [x16, #0xc60]
    // 0x1299b4c: cmp             w0, w16
    // 0x1299b50: b.ne            #0x1299b5c
    // 0x1299b54: d0 = 1.000000
    //     0x1299b54: fmov            d0, #1.00000000
    // 0x1299b58: b               #0x1299b60
    // 0x1299b5c: d0 = 0.000000
    //     0x1299b5c: eor             v0.16b, v0.16b, v0.16b
    // 0x1299b60: stur            d0, [fp, #-0x48]
    // 0x1299b64: r0 = EdgeInsets()
    //     0x1299b64: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0x1299b68: stur            x0, [fp, #-0x28]
    // 0x1299b6c: StoreField: r0->field_7 = rZR
    //     0x1299b6c: stur            xzr, [x0, #7]
    // 0x1299b70: ldur            d0, [fp, #-0x48]
    // 0x1299b74: StoreField: r0->field_f = d0
    //     0x1299b74: stur            d0, [x0, #0xf]
    // 0x1299b78: ArrayStore: r0[0] = rZR  ; List_8
    //     0x1299b78: stur            xzr, [x0, #0x17]
    // 0x1299b7c: StoreField: r0->field_1f = rZR
    //     0x1299b7c: stur            xzr, [x0, #0x1f]
    // 0x1299b80: ldur            x1, [fp, #-0x10]
    // 0x1299b84: LoadField: r2 = r1->field_33
    //     0x1299b84: ldur            w2, [x1, #0x33]
    // 0x1299b88: DecompressPointer r2
    //     0x1299b88: add             x2, x2, HEAP, lsl #32
    // 0x1299b8c: stur            x2, [fp, #-0x20]
    // 0x1299b90: r16 = Instance_Axis
    //     0x1299b90: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1299b94: cmp             w2, w16
    // 0x1299b98: b.ne            #0x1299ba8
    // 0x1299b9c: LoadField: d0 = r1->field_b
    //     0x1299b9c: ldur            d0, [x1, #0xb]
    // 0x1299ba0: ldur            x3, [fp, #-8]
    // 0x1299ba4: b               #0x1299bc8
    // 0x1299ba8: ldur            x3, [fp, #-8]
    // 0x1299bac: r16 = Instance_LineType
    //     0x1299bac: add             x16, PP, #0x6e, lsl #12  ; [pp+0x6ec58] Obj!LineType@d74fe1
    //     0x1299bb0: ldr             x16, [x16, #0xc58]
    // 0x1299bb4: cmp             w3, w16
    // 0x1299bb8: b.ne            #0x1299bc4
    // 0x1299bbc: d0 = 0.000000
    //     0x1299bbc: eor             v0.16b, v0.16b, v0.16b
    // 0x1299bc0: b               #0x1299bc8
    // 0x1299bc4: d0 = 1.000000
    //     0x1299bc4: fmov            d0, #1.00000000
    // 0x1299bc8: stur            d0, [fp, #-0x50]
    // 0x1299bcc: r16 = Instance_Axis
    //     0x1299bcc: ldr             x16, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x1299bd0: cmp             w2, w16
    // 0x1299bd4: b.ne            #0x1299be0
    // 0x1299bd8: LoadField: d1 = r1->field_b
    //     0x1299bd8: ldur            d1, [x1, #0xb]
    // 0x1299bdc: b               #0x1299bfc
    // 0x1299be0: r16 = Instance_LineType
    //     0x1299be0: add             x16, PP, #0x6e, lsl #12  ; [pp+0x6ec58] Obj!LineType@d74fe1
    //     0x1299be4: ldr             x16, [x16, #0xc58]
    // 0x1299be8: cmp             w3, w16
    // 0x1299bec: b.ne            #0x1299bf8
    // 0x1299bf0: d1 = 0.000000
    //     0x1299bf0: eor             v1.16b, v1.16b, v1.16b
    // 0x1299bf4: b               #0x1299bfc
    // 0x1299bf8: d1 = 1.000000
    //     0x1299bf8: fmov            d1, #1.00000000
    // 0x1299bfc: stur            d1, [fp, #-0x48]
    // 0x1299c00: r16 = Instance_LineType
    //     0x1299c00: add             x16, PP, #0x54, lsl #12  ; [pp+0x547d8] Obj!LineType@d74fa1
    //     0x1299c04: ldr             x16, [x16, #0x7d8]
    // 0x1299c08: cmp             w3, w16
    // 0x1299c0c: b.ne            #0x1299c78
    // 0x1299c10: LoadField: r4 = r1->field_1b
    //     0x1299c10: ldur            w4, [x1, #0x1b]
    // 0x1299c14: DecompressPointer r4
    //     0x1299c14: add             x4, x4, HEAP, lsl #32
    // 0x1299c18: stur            x4, [fp, #-0x18]
    // 0x1299c1c: r0 = Radius()
    //     0x1299c1c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x1299c20: d0 = 100.000000
    //     0x1299c20: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0x1299c24: stur            x0, [fp, #-0x30]
    // 0x1299c28: StoreField: r0->field_7 = d0
    //     0x1299c28: stur            d0, [x0, #7]
    // 0x1299c2c: StoreField: r0->field_f = d0
    //     0x1299c2c: stur            d0, [x0, #0xf]
    // 0x1299c30: r0 = BorderRadius()
    //     0x1299c30: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x1299c34: mov             x1, x0
    // 0x1299c38: ldur            x0, [fp, #-0x30]
    // 0x1299c3c: stur            x1, [fp, #-0x38]
    // 0x1299c40: StoreField: r1->field_7 = r0
    //     0x1299c40: stur            w0, [x1, #7]
    // 0x1299c44: StoreField: r1->field_b = r0
    //     0x1299c44: stur            w0, [x1, #0xb]
    // 0x1299c48: StoreField: r1->field_f = r0
    //     0x1299c48: stur            w0, [x1, #0xf]
    // 0x1299c4c: StoreField: r1->field_13 = r0
    //     0x1299c4c: stur            w0, [x1, #0x13]
    // 0x1299c50: r0 = BoxDecoration()
    //     0x1299c50: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x1299c54: mov             x1, x0
    // 0x1299c58: ldur            x0, [fp, #-0x18]
    // 0x1299c5c: StoreField: r1->field_7 = r0
    //     0x1299c5c: stur            w0, [x1, #7]
    // 0x1299c60: ldur            x0, [fp, #-0x38]
    // 0x1299c64: StoreField: r1->field_13 = r0
    //     0x1299c64: stur            w0, [x1, #0x13]
    // 0x1299c68: r0 = Instance_BoxShape
    //     0x1299c68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x1299c6c: ldr             x0, [x0, #0x80]
    // 0x1299c70: StoreField: r1->field_23 = r0
    //     0x1299c70: stur            w0, [x1, #0x23]
    // 0x1299c74: b               #0x1299c7c
    // 0x1299c78: r1 = Null
    //     0x1299c78: mov             x1, NULL
    // 0x1299c7c: ldur            x0, [fp, #-8]
    // 0x1299c80: stur            x1, [fp, #-0x18]
    // 0x1299c84: r16 = Instance_LineType
    //     0x1299c84: add             x16, PP, #0x6e, lsl #12  ; [pp+0x6ec58] Obj!LineType@d74fe1
    //     0x1299c88: ldr             x16, [x16, #0xc58]
    // 0x1299c8c: cmp             w0, w16
    // 0x1299c90: b.ne            #0x1299d48
    // 0x1299c94: ldur            x0, [fp, #-0x10]
    // 0x1299c98: ldur            x2, [fp, #-0x20]
    // 0x1299c9c: r16 = 136
    //     0x1299c9c: movz            x16, #0x88
    // 0x1299ca0: stp             x16, NULL, [SP]
    // 0x1299ca4: r0 = ByteData()
    //     0x1299ca4: bl              #0x63e644  ; [dart:typed_data] ByteData::ByteData
    // 0x1299ca8: stur            x0, [fp, #-0x30]
    // 0x1299cac: r0 = Paint()
    //     0x1299cac: bl              #0x673bd4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0x1299cb0: mov             x3, x0
    // 0x1299cb4: ldur            x0, [fp, #-0x30]
    // 0x1299cb8: stur            x3, [fp, #-0x38]
    // 0x1299cbc: StoreField: r3->field_7 = r0
    //     0x1299cbc: stur            w0, [x3, #7]
    // 0x1299cc0: ldur            x0, [fp, #-0x10]
    // 0x1299cc4: LoadField: r2 = r0->field_1b
    //     0x1299cc4: ldur            w2, [x0, #0x1b]
    // 0x1299cc8: DecompressPointer r2
    //     0x1299cc8: add             x2, x2, HEAP, lsl #32
    // 0x1299ccc: mov             x1, x3
    // 0x1299cd0: r0 = color=()
    //     0x1299cd0: bl              #0x6739e0  ; [dart:ui] Paint::color=
    // 0x1299cd4: ldur            x1, [fp, #-0x10]
    // 0x1299cd8: LoadField: d0 = r1->field_b
    //     0x1299cd8: ldur            d0, [x1, #0xb]
    // 0x1299cdc: stur            d0, [fp, #-0x60]
    // 0x1299ce0: LoadField: d1 = r1->field_27
    //     0x1299ce0: ldur            d1, [x1, #0x27]
    // 0x1299ce4: stur            d1, [fp, #-0x58]
    // 0x1299ce8: r0 = _DottedLinePainter()
    //     0x1299ce8: bl              #0x129a11c  ; Allocate_DottedLinePainterStub -> _DottedLinePainter (size=0x2c)
    // 0x1299cec: ldur            d0, [fp, #-0x60]
    // 0x1299cf0: stur            x0, [fp, #-0x30]
    // 0x1299cf4: StoreField: r0->field_b = d0
    //     0x1299cf4: stur            d0, [x0, #0xb]
    // 0x1299cf8: ldur            x1, [fp, #-0x38]
    // 0x1299cfc: StoreField: r0->field_23 = r1
    //     0x1299cfc: stur            w1, [x0, #0x23]
    // 0x1299d00: d0 = 1.000000
    //     0x1299d00: fmov            d0, #1.00000000
    // 0x1299d04: StoreField: r0->field_13 = d0
    //     0x1299d04: stur            d0, [x0, #0x13]
    // 0x1299d08: ldur            d0, [fp, #-0x58]
    // 0x1299d0c: StoreField: r0->field_1b = d0
    //     0x1299d0c: stur            d0, [x0, #0x1b]
    // 0x1299d10: ldur            x1, [fp, #-0x20]
    // 0x1299d14: StoreField: r0->field_27 = r1
    //     0x1299d14: stur            w1, [x0, #0x27]
    // 0x1299d18: r0 = CustomPaint()
    //     0x1299d18: bl              #0xc1bf8c  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0x1299d1c: mov             x1, x0
    // 0x1299d20: ldur            x0, [fp, #-0x30]
    // 0x1299d24: StoreField: r1->field_f = r0
    //     0x1299d24: stur            w0, [x1, #0xf]
    // 0x1299d28: r0 = Instance_Size
    //     0x1299d28: add             x0, PP, #0xd, lsl #12  ; [pp+0xd778] Obj!Size@d6c161
    //     0x1299d2c: ldr             x0, [x0, #0x778]
    // 0x1299d30: ArrayStore: r1[0] = r0  ; List_4
    //     0x1299d30: stur            w0, [x1, #0x17]
    // 0x1299d34: r0 = false
    //     0x1299d34: add             x0, NULL, #0x30  ; false
    // 0x1299d38: StoreField: r1->field_1b = r0
    //     0x1299d38: stur            w0, [x1, #0x1b]
    // 0x1299d3c: StoreField: r1->field_1f = r0
    //     0x1299d3c: stur            w0, [x1, #0x1f]
    // 0x1299d40: mov             x0, x1
    // 0x1299d44: b               #0x1299fcc
    // 0x1299d48: ldur            x1, [fp, #-0x10]
    // 0x1299d4c: r16 = Instance_LineType
    //     0x1299d4c: add             x16, PP, #0x6e, lsl #12  ; [pp+0x6ec60] Obj!LineType@d74fc1
    //     0x1299d50: ldr             x16, [x16, #0xc60]
    // 0x1299d54: cmp             w0, w16
    // 0x1299d58: b.ne            #0x1299fc8
    // 0x1299d5c: d1 = 2.000000
    //     0x1299d5c: fmov            d1, #2.00000000
    // 0x1299d60: d0 = 4.000000
    //     0x1299d60: fmov            d0, #4.00000000
    // 0x1299d64: LoadField: d2 = r1->field_b
    //     0x1299d64: ldur            d2, [x1, #0xb]
    // 0x1299d68: LoadField: d3 = r1->field_27
    //     0x1299d68: ldur            d3, [x1, #0x27]
    // 0x1299d6c: stur            d3, [fp, #-0x58]
    // 0x1299d70: fadd            d4, d3, d0
    // 0x1299d74: fdiv            d0, d4, d1
    // 0x1299d78: r0 = inline_Allocate_Double()
    //     0x1299d78: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x1299d7c: add             x0, x0, #0x10
    //     0x1299d80: cmp             x2, x0
    //     0x1299d84: b.ls            #0x129a080
    //     0x1299d88: str             x0, [THR, #0x50]  ; THR::top
    //     0x1299d8c: sub             x0, x0, #0xf
    //     0x1299d90: movz            x2, #0xe15c
    //     0x1299d94: movk            x2, #0x3, lsl #16
    //     0x1299d98: stur            x2, [x0, #-1]
    // 0x1299d9c: StoreField: r0->field_7 = d2
    //     0x1299d9c: stur            d2, [x0, #7]
    // 0x1299da0: r2 = inline_Allocate_Double()
    //     0x1299da0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x1299da4: add             x2, x2, #0x10
    //     0x1299da8: cmp             x3, x2
    //     0x1299dac: b.ls            #0x129a0a0
    //     0x1299db0: str             x2, [THR, #0x50]  ; THR::top
    //     0x1299db4: sub             x2, x2, #0xf
    //     0x1299db8: movz            x3, #0xe15c
    //     0x1299dbc: movk            x3, #0x3, lsl #16
    //     0x1299dc0: stur            x3, [x2, #-1]
    // 0x1299dc4: StoreField: r2->field_7 = d0
    //     0x1299dc4: stur            d0, [x2, #7]
    // 0x1299dc8: stp             x2, x0, [SP]
    // 0x1299dcc: r0 = ~/()
    //     0x1299dcc: bl              #0x775e70  ; [dart:core] _Double::~/
    // 0x1299dd0: r2 = LoadInt32Instr(r0)
    //     0x1299dd0: sbfx            x2, x0, #1, #0x1f
    //     0x1299dd4: tbz             w0, #0, #0x1299ddc
    //     0x1299dd8: ldur            x2, [x0, #7]
    // 0x1299ddc: r1 = <Widget>
    //     0x1299ddc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x1299de0: r0 = _GrowableList()
    //     0x1299de0: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x1299de4: stur            x0, [fp, #-0x30]
    // 0x1299de8: ldur            d0, [fp, #-0x58]
    // 0x1299dec: r1 = inline_Allocate_Double()
    //     0x1299dec: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x1299df0: add             x1, x1, #0x10
    //     0x1299df4: cmp             x2, x1
    //     0x1299df8: b.ls            #0x129a0bc
    //     0x1299dfc: str             x1, [THR, #0x50]  ; THR::top
    //     0x1299e00: sub             x1, x1, #0xf
    //     0x1299e04: movz            x2, #0xe15c
    //     0x1299e08: movk            x2, #0x3, lsl #16
    //     0x1299e0c: stur            x2, [x1, #-1]
    // 0x1299e10: StoreField: r1->field_7 = d0
    //     0x1299e10: stur            d0, [x1, #7]
    // 0x1299e14: ldur            x2, [fp, #-0x10]
    // 0x1299e18: stur            x1, [fp, #-0x20]
    // 0x1299e1c: LoadField: r3 = r2->field_1b
    //     0x1299e1c: ldur            w3, [x2, #0x1b]
    // 0x1299e20: DecompressPointer r3
    //     0x1299e20: add             x3, x3, HEAP, lsl #32
    // 0x1299e24: stur            x3, [fp, #-8]
    // 0x1299e28: r2 = 0
    //     0x1299e28: movz            x2, #0
    // 0x1299e2c: stur            x2, [fp, #-0x40]
    // 0x1299e30: CheckStackOverflow
    //     0x1299e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1299e34: cmp             SP, x16
    //     0x1299e38: b.ls            #0x129a0d8
    // 0x1299e3c: LoadField: r4 = r0->field_b
    //     0x1299e3c: ldur            w4, [x0, #0xb]
    // 0x1299e40: r5 = LoadInt32Instr(r4)
    //     0x1299e40: sbfx            x5, x4, #1, #0x1f
    // 0x1299e44: cmp             x2, x5
    // 0x1299e48: b.ge            #0x1299f64
    // 0x1299e4c: tbnz            w2, #0, #0x1299e88
    // 0x1299e50: r0 = Container()
    //     0x1299e50: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x1299e54: stur            x0, [fp, #-0x10]
    // 0x1299e58: ldur            x16, [fp, #-8]
    // 0x1299e5c: r30 = 1.000000
    //     0x1299e5c: ldr             lr, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1299e60: stp             lr, x16, [SP, #8]
    // 0x1299e64: r16 = 4.000000
    //     0x1299e64: add             x16, PP, #0x27, lsl #12  ; [pp+0x27838] 4
    //     0x1299e68: ldr             x16, [x16, #0x838]
    // 0x1299e6c: str             x16, [SP]
    // 0x1299e70: mov             x1, x0
    // 0x1299e74: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, height, 0x2, width, 0x3, null]
    //     0x1299e74: add             x4, PP, #0x6e, lsl #12  ; [pp+0x6ec68] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "height", 0x2, "width", 0x3, Null]
    //     0x1299e78: ldr             x4, [x4, #0xc68]
    // 0x1299e7c: r0 = Container()
    //     0x1299e7c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x1299e80: ldur            x3, [fp, #-0x10]
    // 0x1299e84: b               #0x1299eb4
    // 0x1299e88: mov             x0, x1
    // 0x1299e8c: r0 = SizedBox()
    //     0x1299e8c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x1299e90: mov             x2, x0
    // 0x1299e94: ldur            x0, [fp, #-0x20]
    // 0x1299e98: stur            x2, [fp, #-0x10]
    // 0x1299e9c: StoreField: r2->field_f = r0
    //     0x1299e9c: stur            w0, [x2, #0xf]
    // 0x1299ea0: r3 = 1.000000
    //     0x1299ea0: ldr             x3, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x1299ea4: StoreField: r2->field_13 = r3
    //     0x1299ea4: stur            w3, [x2, #0x13]
    // 0x1299ea8: mov             x1, x2
    // 0x1299eac: r0 = _NativeScene._()
    //     0x1299eac: bl              #0x16ed860  ; [dart:ui] _NativeScene::_NativeScene._
    // 0x1299eb0: ldur            x3, [fp, #-0x10]
    // 0x1299eb4: ldur            x0, [fp, #-0x30]
    // 0x1299eb8: ldur            x2, [fp, #-0x40]
    // 0x1299ebc: stur            x3, [fp, #-0x10]
    // 0x1299ec0: r1 = <FlexParentData>
    //     0x1299ec0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x1299ec4: ldr             x1, [x1, #0xe00]
    // 0x1299ec8: r0 = Expanded()
    //     0x1299ec8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x1299ecc: mov             x2, x0
    // 0x1299ed0: r0 = 1
    //     0x1299ed0: movz            x0, #0x1
    // 0x1299ed4: stur            x2, [fp, #-0x38]
    // 0x1299ed8: StoreField: r2->field_13 = r0
    //     0x1299ed8: stur            x0, [x2, #0x13]
    // 0x1299edc: r3 = Instance_FlexFit
    //     0x1299edc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x1299ee0: ldr             x3, [x3, #0xe08]
    // 0x1299ee4: StoreField: r2->field_1b = r3
    //     0x1299ee4: stur            w3, [x2, #0x1b]
    // 0x1299ee8: ldur            x1, [fp, #-0x10]
    // 0x1299eec: StoreField: r2->field_b = r1
    //     0x1299eec: stur            w1, [x2, #0xb]
    // 0x1299ef0: mov             x1, x2
    // 0x1299ef4: r0 = _NativeScene._()
    //     0x1299ef4: bl              #0x16ed860  ; [dart:ui] _NativeScene::_NativeScene._
    // 0x1299ef8: ldur            x3, [fp, #-0x30]
    // 0x1299efc: LoadField: r0 = r3->field_b
    //     0x1299efc: ldur            w0, [x3, #0xb]
    // 0x1299f00: r1 = LoadInt32Instr(r0)
    //     0x1299f00: sbfx            x1, x0, #1, #0x1f
    // 0x1299f04: mov             x0, x1
    // 0x1299f08: ldur            x1, [fp, #-0x40]
    // 0x1299f0c: cmp             x1, x0
    // 0x1299f10: b.hs            #0x129a0e0
    // 0x1299f14: LoadField: r1 = r3->field_f
    //     0x1299f14: ldur            w1, [x3, #0xf]
    // 0x1299f18: DecompressPointer r1
    //     0x1299f18: add             x1, x1, HEAP, lsl #32
    // 0x1299f1c: ldur            x0, [fp, #-0x38]
    // 0x1299f20: ldur            x2, [fp, #-0x40]
    // 0x1299f24: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1299f24: add             x25, x1, x2, lsl #2
    //     0x1299f28: add             x25, x25, #0xf
    //     0x1299f2c: str             w0, [x25]
    //     0x1299f30: tbz             w0, #0, #0x1299f4c
    //     0x1299f34: ldurb           w16, [x1, #-1]
    //     0x1299f38: ldurb           w17, [x0, #-1]
    //     0x1299f3c: and             x16, x17, x16, lsr #2
    //     0x1299f40: tst             x16, HEAP, lsr #32
    //     0x1299f44: b.eq            #0x1299f4c
    //     0x1299f48: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1299f4c: add             x0, x2, #1
    // 0x1299f50: mov             x2, x0
    // 0x1299f54: mov             x0, x3
    // 0x1299f58: ldur            x3, [fp, #-8]
    // 0x1299f5c: ldur            x1, [fp, #-0x20]
    // 0x1299f60: b               #0x1299e2c
    // 0x1299f64: mov             x3, x0
    // 0x1299f68: r0 = Row()
    //     0x1299f68: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x1299f6c: mov             x1, x0
    // 0x1299f70: r0 = Instance_Axis
    //     0x1299f70: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x1299f74: StoreField: r1->field_f = r0
    //     0x1299f74: stur            w0, [x1, #0xf]
    // 0x1299f78: r0 = Instance_MainAxisAlignment
    //     0x1299f78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x1299f7c: ldr             x0, [x0, #0xa08]
    // 0x1299f80: StoreField: r1->field_13 = r0
    //     0x1299f80: stur            w0, [x1, #0x13]
    // 0x1299f84: r0 = Instance_MainAxisSize
    //     0x1299f84: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x1299f88: ldr             x0, [x0, #0xa10]
    // 0x1299f8c: ArrayStore: r1[0] = r0  ; List_4
    //     0x1299f8c: stur            w0, [x1, #0x17]
    // 0x1299f90: r0 = Instance_CrossAxisAlignment
    //     0x1299f90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x1299f94: ldr             x0, [x0, #0xa18]
    // 0x1299f98: StoreField: r1->field_1b = r0
    //     0x1299f98: stur            w0, [x1, #0x1b]
    // 0x1299f9c: r0 = Instance_VerticalDirection
    //     0x1299f9c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x1299fa0: ldr             x0, [x0, #0xa20]
    // 0x1299fa4: StoreField: r1->field_23 = r0
    //     0x1299fa4: stur            w0, [x1, #0x23]
    // 0x1299fa8: r0 = Instance_Clip
    //     0x1299fa8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x1299fac: ldr             x0, [x0, #0x38]
    // 0x1299fb0: StoreField: r1->field_2b = r0
    //     0x1299fb0: stur            w0, [x1, #0x2b]
    // 0x1299fb4: StoreField: r1->field_2f = rZR
    //     0x1299fb4: stur            xzr, [x1, #0x2f]
    // 0x1299fb8: ldur            x0, [fp, #-0x30]
    // 0x1299fbc: StoreField: r1->field_b = r0
    //     0x1299fbc: stur            w0, [x1, #0xb]
    // 0x1299fc0: mov             x0, x1
    // 0x1299fc4: b               #0x1299fcc
    // 0x1299fc8: r0 = Null
    //     0x1299fc8: mov             x0, NULL
    // 0x1299fcc: ldur            d0, [fp, #-0x50]
    // 0x1299fd0: ldur            d1, [fp, #-0x48]
    // 0x1299fd4: stur            x0, [fp, #-0x20]
    // 0x1299fd8: r1 = inline_Allocate_Double()
    //     0x1299fd8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x1299fdc: add             x1, x1, #0x10
    //     0x1299fe0: cmp             x2, x1
    //     0x1299fe4: b.ls            #0x129a0e4
    //     0x1299fe8: str             x1, [THR, #0x50]  ; THR::top
    //     0x1299fec: sub             x1, x1, #0xf
    //     0x1299ff0: movz            x2, #0xe15c
    //     0x1299ff4: movk            x2, #0x3, lsl #16
    //     0x1299ff8: stur            x2, [x1, #-1]
    // 0x1299ffc: StoreField: r1->field_7 = d0
    //     0x1299ffc: stur            d0, [x1, #7]
    // 0x129a000: stur            x1, [fp, #-0x10]
    // 0x129a004: r2 = inline_Allocate_Double()
    //     0x129a004: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x129a008: add             x2, x2, #0x10
    //     0x129a00c: cmp             x3, x2
    //     0x129a010: b.ls            #0x129a100
    //     0x129a014: str             x2, [THR, #0x50]  ; THR::top
    //     0x129a018: sub             x2, x2, #0xf
    //     0x129a01c: movz            x3, #0xe15c
    //     0x129a020: movk            x3, #0x3, lsl #16
    //     0x129a024: stur            x3, [x2, #-1]
    // 0x129a028: StoreField: r2->field_7 = d1
    //     0x129a028: stur            d1, [x2, #7]
    // 0x129a02c: stur            x2, [fp, #-8]
    // 0x129a030: r0 = Container()
    //     0x129a030: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x129a034: stur            x0, [fp, #-0x30]
    // 0x129a038: ldur            x16, [fp, #-0x28]
    // 0x129a03c: ldur            lr, [fp, #-0x10]
    // 0x129a040: stp             lr, x16, [SP, #0x18]
    // 0x129a044: ldur            x16, [fp, #-8]
    // 0x129a048: ldur            lr, [fp, #-0x18]
    // 0x129a04c: stp             lr, x16, [SP, #8]
    // 0x129a050: ldur            x16, [fp, #-0x20]
    // 0x129a054: str             x16, [SP]
    // 0x129a058: mov             x1, x0
    // 0x129a05c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, decoration, 0x4, height, 0x3, margin, 0x1, width, 0x2, null]
    //     0x129a05c: add             x4, PP, #0x6e, lsl #12  ; [pp+0x6ec70] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "decoration", 0x4, "height", 0x3, "margin", 0x1, "width", 0x2, Null]
    //     0x129a060: ldr             x4, [x4, #0xc70]
    // 0x129a064: r0 = Container()
    //     0x129a064: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x129a068: ldur            x0, [fp, #-0x30]
    // 0x129a06c: LeaveFrame
    //     0x129a06c: mov             SP, fp
    //     0x129a070: ldp             fp, lr, [SP], #0x10
    // 0x129a074: ret
    //     0x129a074: ret             
    // 0x129a078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x129a078: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x129a07c: b               #0x1299b28
    // 0x129a080: stp             q2, q3, [SP, #-0x20]!
    // 0x129a084: SaveReg d0
    //     0x129a084: str             q0, [SP, #-0x10]!
    // 0x129a088: SaveReg r1
    //     0x129a088: str             x1, [SP, #-8]!
    // 0x129a08c: r0 = AllocateDouble()
    //     0x129a08c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x129a090: RestoreReg r1
    //     0x129a090: ldr             x1, [SP], #8
    // 0x129a094: RestoreReg d0
    //     0x129a094: ldr             q0, [SP], #0x10
    // 0x129a098: ldp             q2, q3, [SP], #0x20
    // 0x129a09c: b               #0x1299d9c
    // 0x129a0a0: stp             q0, q3, [SP, #-0x20]!
    // 0x129a0a4: stp             x0, x1, [SP, #-0x10]!
    // 0x129a0a8: r0 = AllocateDouble()
    //     0x129a0a8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x129a0ac: mov             x2, x0
    // 0x129a0b0: ldp             x0, x1, [SP], #0x10
    // 0x129a0b4: ldp             q0, q3, [SP], #0x20
    // 0x129a0b8: b               #0x1299dc4
    // 0x129a0bc: SaveReg d0
    //     0x129a0bc: str             q0, [SP, #-0x10]!
    // 0x129a0c0: SaveReg r0
    //     0x129a0c0: str             x0, [SP, #-8]!
    // 0x129a0c4: r0 = AllocateDouble()
    //     0x129a0c4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x129a0c8: mov             x1, x0
    // 0x129a0cc: RestoreReg r0
    //     0x129a0cc: ldr             x0, [SP], #8
    // 0x129a0d0: RestoreReg d0
    //     0x129a0d0: ldr             q0, [SP], #0x10
    // 0x129a0d4: b               #0x1299e10
    // 0x129a0d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x129a0d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x129a0dc: b               #0x1299e3c
    // 0x129a0e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x129a0e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x129a0e4: stp             q0, q1, [SP, #-0x20]!
    // 0x129a0e8: SaveReg r0
    //     0x129a0e8: str             x0, [SP, #-8]!
    // 0x129a0ec: r0 = AllocateDouble()
    //     0x129a0ec: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x129a0f0: mov             x1, x0
    // 0x129a0f4: RestoreReg r0
    //     0x129a0f4: ldr             x0, [SP], #8
    // 0x129a0f8: ldp             q0, q1, [SP], #0x20
    // 0x129a0fc: b               #0x1299ffc
    // 0x129a100: SaveReg d1
    //     0x129a100: str             q1, [SP, #-0x10]!
    // 0x129a104: stp             x0, x1, [SP, #-0x10]!
    // 0x129a108: r0 = AllocateDouble()
    //     0x129a108: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x129a10c: mov             x2, x0
    // 0x129a110: ldp             x0, x1, [SP], #0x10
    // 0x129a114: RestoreReg d1
    //     0x129a114: ldr             q1, [SP], #0x10
    // 0x129a118: b               #0x129a028
  }
}

// class id: 4884, size: 0x2c, field offset: 0xc
class _DottedLinePainter extends CustomPainter {

  _ paint(/* No info */) {
    // ** addr: 0x160e2f8, size: 0x2b0
    // 0x160e2f8: EnterFrame
    //     0x160e2f8: stp             fp, lr, [SP, #-0x10]!
    //     0x160e2fc: mov             fp, SP
    // 0x160e300: AllocStack(0x70)
    //     0x160e300: sub             SP, SP, #0x70
    // 0x160e304: d0 = 0.000000
    //     0x160e304: eor             v0.16b, v0.16b, v0.16b
    // 0x160e308: stur            x1, [fp, #-8]
    // 0x160e30c: stur            x2, [fp, #-0x10]
    // 0x160e310: CheckStackOverflow
    //     0x160e310: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160e314: cmp             SP, x16
    //     0x160e318: b.ls            #0x160e53c
    // 0x160e31c: LoadField: d1 = r1->field_1b
    //     0x160e31c: ldur            d1, [x1, #0x1b]
    // 0x160e320: stur            d1, [fp, #-0x48]
    // 0x160e324: fcmp            d1, d0
    // 0x160e328: b.ne            #0x160e378
    // 0x160e32c: LoadField: d2 = r1->field_b
    //     0x160e32c: ldur            d2, [x1, #0xb]
    // 0x160e330: r0 = inline_Allocate_Double()
    //     0x160e330: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x160e334: add             x0, x0, #0x10
    //     0x160e338: cmp             x3, x0
    //     0x160e33c: b.ls            #0x160e544
    //     0x160e340: str             x0, [THR, #0x50]  ; THR::top
    //     0x160e344: sub             x0, x0, #0xf
    //     0x160e348: movz            x3, #0xe15c
    //     0x160e34c: movk            x3, #0x3, lsl #16
    //     0x160e350: stur            x3, [x0, #-1]
    // 0x160e354: StoreField: r0->field_7 = d2
    //     0x160e354: stur            d2, [x0, #7]
    // 0x160e358: r16 = 1.000000
    //     0x160e358: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x160e35c: stp             x16, x0, [SP]
    // 0x160e360: r0 = ~/()
    //     0x160e360: bl              #0x775e70  ; [dart:core] _Double::~/
    // 0x160e364: r1 = LoadInt32Instr(r0)
    //     0x160e364: sbfx            x1, x0, #1, #0x1f
    //     0x160e368: tbz             w0, #0, #0x160e370
    //     0x160e36c: ldur            x1, [x0, #7]
    // 0x160e370: mov             x2, x1
    // 0x160e374: b               #0x160e3ec
    // 0x160e378: mov             x0, x1
    // 0x160e37c: mov             v0.16b, v1.16b
    // 0x160e380: LoadField: d1 = r0->field_b
    //     0x160e380: ldur            d1, [x0, #0xb]
    // 0x160e384: r1 = inline_Allocate_Double()
    //     0x160e384: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x160e388: add             x1, x1, #0x10
    //     0x160e38c: cmp             x2, x1
    //     0x160e390: b.ls            #0x160e564
    //     0x160e394: str             x1, [THR, #0x50]  ; THR::top
    //     0x160e398: sub             x1, x1, #0xf
    //     0x160e39c: movz            x2, #0xe15c
    //     0x160e3a0: movk            x2, #0x3, lsl #16
    //     0x160e3a4: stur            x2, [x1, #-1]
    // 0x160e3a8: StoreField: r1->field_7 = d1
    //     0x160e3a8: stur            d1, [x1, #7]
    // 0x160e3ac: r2 = inline_Allocate_Double()
    //     0x160e3ac: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x160e3b0: add             x2, x2, #0x10
    //     0x160e3b4: cmp             x3, x2
    //     0x160e3b8: b.ls            #0x160e580
    //     0x160e3bc: str             x2, [THR, #0x50]  ; THR::top
    //     0x160e3c0: sub             x2, x2, #0xf
    //     0x160e3c4: movz            x3, #0xe15c
    //     0x160e3c8: movk            x3, #0x3, lsl #16
    //     0x160e3cc: stur            x3, [x2, #-1]
    // 0x160e3d0: StoreField: r2->field_7 = d0
    //     0x160e3d0: stur            d0, [x2, #7]
    // 0x160e3d4: stp             x2, x1, [SP]
    // 0x160e3d8: r0 = ~/()
    //     0x160e3d8: bl              #0x775e70  ; [dart:core] _Double::~/
    // 0x160e3dc: r1 = LoadInt32Instr(r0)
    //     0x160e3dc: sbfx            x1, x0, #1, #0x1f
    //     0x160e3e0: tbz             w0, #0, #0x160e3e8
    //     0x160e3e4: ldur            x1, [x0, #7]
    // 0x160e3e8: mov             x2, x1
    // 0x160e3ec: ldur            x0, [fp, #-8]
    // 0x160e3f0: stur            x2, [fp, #-0x40]
    // 0x160e3f4: LoadField: r3 = r0->field_27
    //     0x160e3f4: ldur            w3, [x0, #0x27]
    // 0x160e3f8: DecompressPointer r3
    //     0x160e3f8: add             x3, x3, HEAP, lsl #32
    // 0x160e3fc: stur            x3, [fp, #-0x38]
    // 0x160e400: LoadField: r4 = r0->field_23
    //     0x160e400: ldur            w4, [x0, #0x23]
    // 0x160e404: DecompressPointer r4
    //     0x160e404: add             x4, x4, HEAP, lsl #32
    // 0x160e408: stur            x4, [fp, #-0x30]
    // 0x160e40c: LoadField: r0 = r4->field_7
    //     0x160e40c: ldur            w0, [x4, #7]
    // 0x160e410: DecompressPointer r0
    //     0x160e410: add             x0, x0, HEAP, lsl #32
    // 0x160e414: stur            x0, [fp, #-0x28]
    // 0x160e418: d3 = 0.000000
    //     0x160e418: eor             v3.16b, v3.16b, v3.16b
    // 0x160e41c: r6 = 1
    //     0x160e41c: movz            x6, #0x1
    // 0x160e420: ldur            x5, [fp, #-0x10]
    // 0x160e424: ldur            d0, [fp, #-0x48]
    // 0x160e428: d1 = 0.000000
    //     0x160e428: eor             v1.16b, v1.16b, v1.16b
    // 0x160e42c: d2 = 1.000000
    //     0x160e42c: fmov            d2, #1.00000000
    // 0x160e430: stur            x6, [fp, #-0x20]
    // 0x160e434: CheckStackOverflow
    //     0x160e434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x160e438: cmp             SP, x16
    //     0x160e43c: b.ls            #0x160e59c
    // 0x160e440: cmp             x6, x2
    // 0x160e444: b.ge            #0x160e51c
    // 0x160e448: fcmp            d0, d1
    // 0x160e44c: b.ne            #0x160e45c
    // 0x160e450: fadd            d4, d3, d2
    // 0x160e454: mov             v3.16b, v4.16b
    // 0x160e458: b               #0x160e464
    // 0x160e45c: fadd            d4, d3, d0
    // 0x160e460: mov             v3.16b, v4.16b
    // 0x160e464: stur            d3, [fp, #-0x60]
    // 0x160e468: r16 = Instance_Axis
    //     0x160e468: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x160e46c: cmp             w3, w16
    // 0x160e470: b.ne            #0x160e47c
    // 0x160e474: mov             v4.16b, v3.16b
    // 0x160e478: b               #0x160e480
    // 0x160e47c: d4 = 0.000000
    //     0x160e47c: eor             v4.16b, v4.16b, v4.16b
    // 0x160e480: stur            d4, [fp, #-0x58]
    // 0x160e484: r16 = Instance_Axis
    //     0x160e484: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x160e488: cmp             w3, w16
    // 0x160e48c: b.ne            #0x160e498
    // 0x160e490: d5 = 0.000000
    //     0x160e490: eor             v5.16b, v5.16b, v5.16b
    // 0x160e494: b               #0x160e49c
    // 0x160e498: mov             v5.16b, v3.16b
    // 0x160e49c: stur            d5, [fp, #-0x50]
    // 0x160e4a0: LoadField: r7 = r4->field_b
    //     0x160e4a0: ldur            w7, [x4, #0xb]
    // 0x160e4a4: DecompressPointer r7
    //     0x160e4a4: add             x7, x7, HEAP, lsl #32
    // 0x160e4a8: stur            x7, [fp, #-8]
    // 0x160e4ac: LoadField: r1 = r5->field_7
    //     0x160e4ac: ldur            w1, [x5, #7]
    // 0x160e4b0: DecompressPointer r1
    //     0x160e4b0: add             x1, x1, HEAP, lsl #32
    // 0x160e4b4: cmp             w1, NULL
    // 0x160e4b8: b.eq            #0x160e5a4
    // 0x160e4bc: LoadField: r8 = r1->field_7
    //     0x160e4bc: ldur            x8, [x1, #7]
    // 0x160e4c0: ldr             x1, [x8]
    // 0x160e4c4: cbz             x1, #0x160e52c
    // 0x160e4c8: mov             x8, x1
    // 0x160e4cc: stur            x8, [fp, #-0x18]
    // 0x160e4d0: r1 = <Never>
    //     0x160e4d0: ldr             x1, [PP, #0x2290]  ; [pp+0x2290] TypeArguments: <Never>
    // 0x160e4d4: r0 = Pointer()
    //     0x160e4d4: bl              #0x63e38c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x160e4d8: mov             x1, x0
    // 0x160e4dc: ldur            x0, [fp, #-0x18]
    // 0x160e4e0: StoreField: r1->field_7 = r0
    //     0x160e4e0: stur            x0, [x1, #7]
    // 0x160e4e4: ldur            d0, [fp, #-0x58]
    // 0x160e4e8: ldur            d1, [fp, #-0x50]
    // 0x160e4ec: ldur            x2, [fp, #-8]
    // 0x160e4f0: ldur            x3, [fp, #-0x28]
    // 0x160e4f4: d2 = 1.000000
    //     0x160e4f4: fmov            d2, #1.00000000
    // 0x160e4f8: r0 = __drawCircle$Method$FfiNative()
    //     0x160e4f8: bl              #0x77eed8  ; [dart:ui] _NativeCanvas::__drawCircle$Method$FfiNative
    // 0x160e4fc: ldur            x0, [fp, #-0x20]
    // 0x160e500: add             x6, x0, #1
    // 0x160e504: ldur            d3, [fp, #-0x60]
    // 0x160e508: ldur            x2, [fp, #-0x40]
    // 0x160e50c: ldur            x3, [fp, #-0x38]
    // 0x160e510: ldur            x4, [fp, #-0x30]
    // 0x160e514: ldur            x0, [fp, #-0x28]
    // 0x160e518: b               #0x160e420
    // 0x160e51c: r0 = Null
    //     0x160e51c: mov             x0, NULL
    // 0x160e520: LeaveFrame
    //     0x160e520: mov             SP, fp
    //     0x160e524: ldp             fp, lr, [SP], #0x10
    // 0x160e528: ret
    //     0x160e528: ret             
    // 0x160e52c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x160e52c: ldr             x16, [PP, #0x2298]  ; [pp+0x2298] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x160e530: str             x16, [SP]
    // 0x160e534: r0 = _throwNew()
    //     0x160e534: bl              #0x622870  ; [dart:core] StateError::_throwNew
    // 0x160e538: brk             #0
    // 0x160e53c: r0 = StackOverflowSharedWithFPURegs()
    //     0x160e53c: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0x160e540: b               #0x160e31c
    // 0x160e544: stp             q1, q2, [SP, #-0x20]!
    // 0x160e548: SaveReg d0
    //     0x160e548: str             q0, [SP, #-0x10]!
    // 0x160e54c: stp             x1, x2, [SP, #-0x10]!
    // 0x160e550: r0 = AllocateDouble()
    //     0x160e550: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x160e554: ldp             x1, x2, [SP], #0x10
    // 0x160e558: RestoreReg d0
    //     0x160e558: ldr             q0, [SP], #0x10
    // 0x160e55c: ldp             q1, q2, [SP], #0x20
    // 0x160e560: b               #0x160e354
    // 0x160e564: stp             q0, q1, [SP, #-0x20]!
    // 0x160e568: SaveReg r0
    //     0x160e568: str             x0, [SP, #-8]!
    // 0x160e56c: r0 = AllocateDouble()
    //     0x160e56c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x160e570: mov             x1, x0
    // 0x160e574: RestoreReg r0
    //     0x160e574: ldr             x0, [SP], #8
    // 0x160e578: ldp             q0, q1, [SP], #0x20
    // 0x160e57c: b               #0x160e3a8
    // 0x160e580: SaveReg d0
    //     0x160e580: str             q0, [SP, #-0x10]!
    // 0x160e584: stp             x0, x1, [SP, #-0x10]!
    // 0x160e588: r0 = AllocateDouble()
    //     0x160e588: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x160e58c: mov             x2, x0
    // 0x160e590: ldp             x0, x1, [SP], #0x10
    // 0x160e594: RestoreReg d0
    //     0x160e594: ldr             q0, [SP], #0x10
    // 0x160e598: b               #0x160e3d0
    // 0x160e59c: r0 = StackOverflowSharedWithFPURegs()
    //     0x160e59c: bl              #0x16f7320  ; StackOverflowSharedWithFPURegsStub
    // 0x160e5a0: b               #0x160e440
    // 0x160e5a4: r0 = NullErrorSharedWithFPURegs()
    //     0x160e5a4: bl              #0x16f7b24  ; NullErrorSharedWithFPURegsStub
  }
}

// class id: 7070, size: 0x14, field offset: 0x14
enum LineType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x1585a10, size: 0x64
    // 0x1585a10: EnterFrame
    //     0x1585a10: stp             fp, lr, [SP, #-0x10]!
    //     0x1585a14: mov             fp, SP
    // 0x1585a18: AllocStack(0x10)
    //     0x1585a18: sub             SP, SP, #0x10
    // 0x1585a1c: SetupParameters(LineType this /* r1 => r0, fp-0x8 */)
    //     0x1585a1c: mov             x0, x1
    //     0x1585a20: stur            x1, [fp, #-8]
    // 0x1585a24: CheckStackOverflow
    //     0x1585a24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1585a28: cmp             SP, x16
    //     0x1585a2c: b.ls            #0x1585a6c
    // 0x1585a30: r1 = Null
    //     0x1585a30: mov             x1, NULL
    // 0x1585a34: r2 = 4
    //     0x1585a34: movz            x2, #0x4
    // 0x1585a38: r0 = AllocateArray()
    //     0x1585a38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1585a3c: r16 = "LineType."
    //     0x1585a3c: add             x16, PP, #0x61, lsl #12  ; [pp+0x61b10] "LineType."
    //     0x1585a40: ldr             x16, [x16, #0xb10]
    // 0x1585a44: StoreField: r0->field_f = r16
    //     0x1585a44: stur            w16, [x0, #0xf]
    // 0x1585a48: ldur            x1, [fp, #-8]
    // 0x1585a4c: LoadField: r2 = r1->field_f
    //     0x1585a4c: ldur            w2, [x1, #0xf]
    // 0x1585a50: DecompressPointer r2
    //     0x1585a50: add             x2, x2, HEAP, lsl #32
    // 0x1585a54: StoreField: r0->field_13 = r2
    //     0x1585a54: stur            w2, [x0, #0x13]
    // 0x1585a58: str             x0, [SP]
    // 0x1585a5c: r0 = _interpolate()
    //     0x1585a5c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1585a60: LeaveFrame
    //     0x1585a60: mov             SP, fp
    //     0x1585a64: ldp             fp, lr, [SP], #0x10
    // 0x1585a68: ret
    //     0x1585a68: ret             
    // 0x1585a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1585a6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1585a70: b               #0x1585a30
  }
}
