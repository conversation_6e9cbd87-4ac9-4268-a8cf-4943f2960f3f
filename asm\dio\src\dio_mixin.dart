// lib: , url: package:dio/src/dio_mixin.dart

// class id: 1049604, size: 0x8
class :: {
}

// class id: 4984, size: 0x10, field offset: 0x8
abstract class _BaseHandler extends Object {

  _ _BaseHandler(/* No info */) {
    // ** addr: 0x863754, size: 0xa8
    // 0x863754: EnterFrame
    //     0x863754: stp             fp, lr, [SP, #-0x10]!
    //     0x863758: mov             fp, SP
    // 0x86375c: AllocStack(0x10)
    //     0x86375c: sub             SP, SP, #0x10
    // 0x863760: SetupParameters(_BaseHandler this /* r1 => r0, fp-0x8 */)
    //     0x863760: mov             x0, x1
    //     0x863764: stur            x1, [fp, #-8]
    // 0x863768: CheckStackOverflow
    //     0x863768: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86376c: cmp             SP, x16
    //     0x863770: b.ls            #0x8637f4
    // 0x863774: r1 = <InterceptorState>
    //     0x863774: add             x1, PP, #8, lsl #12  ; [pp+0x8408] TypeArguments: <InterceptorState>
    //     0x863778: ldr             x1, [x1, #0x408]
    // 0x86377c: r0 = _Future()
    //     0x86377c: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x863780: stur            x0, [fp, #-0x10]
    // 0x863784: StoreField: r0->field_b = rZR
    //     0x863784: stur            xzr, [x0, #0xb]
    // 0x863788: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x863788: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x86378c: ldr             x0, [x0, #0x778]
    //     0x863790: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x863794: cmp             w0, w16
    //     0x863798: b.ne            #0x8637a4
    //     0x86379c: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x8637a0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x8637a4: mov             x1, x0
    // 0x8637a8: ldur            x0, [fp, #-0x10]
    // 0x8637ac: StoreField: r0->field_13 = r1
    //     0x8637ac: stur            w1, [x0, #0x13]
    // 0x8637b0: r1 = <InterceptorState>
    //     0x8637b0: add             x1, PP, #8, lsl #12  ; [pp+0x8408] TypeArguments: <InterceptorState>
    //     0x8637b4: ldr             x1, [x1, #0x408]
    // 0x8637b8: r0 = _AsyncCompleter()
    //     0x8637b8: bl              #0x632658  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x8637bc: ldur            x1, [fp, #-0x10]
    // 0x8637c0: StoreField: r0->field_b = r1
    //     0x8637c0: stur            w1, [x0, #0xb]
    // 0x8637c4: ldur            x1, [fp, #-8]
    // 0x8637c8: StoreField: r1->field_7 = r0
    //     0x8637c8: stur            w0, [x1, #7]
    //     0x8637cc: ldurb           w16, [x1, #-1]
    //     0x8637d0: ldurb           w17, [x0, #-1]
    //     0x8637d4: and             x16, x17, x16, lsr #2
    //     0x8637d8: tst             x16, HEAP, lsr #32
    //     0x8637dc: b.eq            #0x8637e4
    //     0x8637e0: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8637e4: r0 = Null
    //     0x8637e4: mov             x0, NULL
    // 0x8637e8: LeaveFrame
    //     0x8637e8: mov             SP, fp
    //     0x8637ec: ldp             fp, lr, [SP], #0x10
    // 0x8637f0: ret
    //     0x8637f0: ret             
    // 0x8637f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8637f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8637f8: b               #0x863774
  }
  _ _throwIfCompleted(/* No info */) {
    // ** addr: 0x863c30, size: 0x54
    // 0x863c30: EnterFrame
    //     0x863c30: stp             fp, lr, [SP, #-0x10]!
    //     0x863c34: mov             fp, SP
    // 0x863c38: LoadField: r0 = r1->field_7
    //     0x863c38: ldur            w0, [x1, #7]
    // 0x863c3c: DecompressPointer r0
    //     0x863c3c: add             x0, x0, HEAP, lsl #32
    // 0x863c40: LoadField: r1 = r0->field_b
    //     0x863c40: ldur            w1, [x0, #0xb]
    // 0x863c44: DecompressPointer r1
    //     0x863c44: add             x1, x1, HEAP, lsl #32
    // 0x863c48: LoadField: r0 = r1->field_b
    //     0x863c48: ldur            x0, [x1, #0xb]
    // 0x863c4c: tst             x0, #0x1e
    // 0x863c50: b.ne            #0x863c64
    // 0x863c54: r0 = Null
    //     0x863c54: mov             x0, NULL
    // 0x863c58: LeaveFrame
    //     0x863c58: mov             SP, fp
    //     0x863c5c: ldp             fp, lr, [SP], #0x10
    // 0x863c60: ret
    //     0x863c60: ret             
    // 0x863c64: r0 = StateError()
    //     0x863c64: bl              #0x622864  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x863c68: mov             x1, x0
    // 0x863c6c: r0 = "The `handler` has already been called, make sure each handler gets called only once."
    //     0x863c6c: add             x0, PP, #8, lsl #12  ; [pp+0x8488] "The `handler` has already been called, make sure each handler gets called only once."
    //     0x863c70: ldr             x0, [x0, #0x488]
    // 0x863c74: StoreField: r1->field_b = r0
    //     0x863c74: stur            w0, [x1, #0xb]
    // 0x863c78: mov             x0, x1
    // 0x863c7c: r0 = Throw()
    //     0x863c7c: bl              #0x16f5420  ; ThrowStub
    // 0x863c80: brk             #0
  }
}

// class id: 4985, size: 0x10, field offset: 0x10
class ErrorInterceptorHandler extends _BaseHandler {

  _ next(/* No info */) {
    // ** addr: 0x168b580, size: 0x94
    // 0x168b580: EnterFrame
    //     0x168b580: stp             fp, lr, [SP, #-0x10]!
    //     0x168b584: mov             fp, SP
    // 0x168b588: AllocStack(0x20)
    //     0x168b588: sub             SP, SP, #0x20
    // 0x168b58c: SetupParameters(ErrorInterceptorHandler this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x168b58c: mov             x0, x1
    //     0x168b590: stur            x1, [fp, #-8]
    //     0x168b594: stur            x2, [fp, #-0x10]
    // 0x168b598: CheckStackOverflow
    //     0x168b598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x168b59c: cmp             SP, x16
    //     0x168b5a0: b.ls            #0x168b60c
    // 0x168b5a4: mov             x1, x0
    // 0x168b5a8: r0 = _throwIfCompleted()
    //     0x168b5a8: bl              #0x863c30  ; [package:dio/src/dio_mixin.dart] _BaseHandler::_throwIfCompleted
    // 0x168b5ac: ldur            x0, [fp, #-8]
    // 0x168b5b0: LoadField: r2 = r0->field_7
    //     0x168b5b0: ldur            w2, [x0, #7]
    // 0x168b5b4: DecompressPointer r2
    //     0x168b5b4: add             x2, x2, HEAP, lsl #32
    // 0x168b5b8: stur            x2, [fp, #-0x18]
    // 0x168b5bc: r1 = <DioException>
    //     0x168b5bc: add             x1, PP, #8, lsl #12  ; [pp+0x83c0] TypeArguments: <DioException>
    //     0x168b5c0: ldr             x1, [x1, #0x3c0]
    // 0x168b5c4: r0 = InterceptorState()
    //     0x168b5c4: bl              #0x86364c  ; AllocateInterceptorStateStub -> InterceptorState<X0> (size=0x14)
    // 0x168b5c8: mov             x1, x0
    // 0x168b5cc: ldur            x0, [fp, #-0x10]
    // 0x168b5d0: StoreField: r1->field_b = r0
    //     0x168b5d0: stur            w0, [x1, #0xb]
    // 0x168b5d4: r2 = Instance_InterceptorResultType
    //     0x168b5d4: add             x2, PP, #8, lsl #12  ; [pp+0x83c8] Obj!InterceptorResultType@d751a1
    //     0x168b5d8: ldr             x2, [x2, #0x3c8]
    // 0x168b5dc: StoreField: r1->field_f = r2
    //     0x168b5dc: stur            w2, [x1, #0xf]
    // 0x168b5e0: LoadField: r2 = r0->field_13
    //     0x168b5e0: ldur            w2, [x0, #0x13]
    // 0x168b5e4: DecompressPointer r2
    //     0x168b5e4: add             x2, x2, HEAP, lsl #32
    // 0x168b5e8: str             x2, [SP]
    // 0x168b5ec: mov             x2, x1
    // 0x168b5f0: ldur            x1, [fp, #-0x18]
    // 0x168b5f4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x168b5f4: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x168b5f8: r0 = completeError()
    //     0x168b5f8: bl              #0x633464  ; [dart:async] _Completer::completeError
    // 0x168b5fc: r0 = Null
    //     0x168b5fc: mov             x0, NULL
    // 0x168b600: LeaveFrame
    //     0x168b600: mov             SP, fp
    //     0x168b604: ldp             fp, lr, [SP], #0x10
    // 0x168b608: ret
    //     0x168b608: ret             
    // 0x168b60c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x168b60c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x168b610: b               #0x168b5a4
  }
}

// class id: 4986, size: 0x10, field offset: 0x10
class ResponseInterceptorHandler extends _BaseHandler {

  _ next(/* No info */) {
    // ** addr: 0x1691f14, size: 0x88
    // 0x1691f14: EnterFrame
    //     0x1691f14: stp             fp, lr, [SP, #-0x10]!
    //     0x1691f18: mov             fp, SP
    // 0x1691f1c: AllocStack(0x20)
    //     0x1691f1c: sub             SP, SP, #0x20
    // 0x1691f20: SetupParameters(ResponseInterceptorHandler this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1691f20: mov             x0, x1
    //     0x1691f24: stur            x1, [fp, #-8]
    //     0x1691f28: stur            x2, [fp, #-0x10]
    // 0x1691f2c: CheckStackOverflow
    //     0x1691f2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1691f30: cmp             SP, x16
    //     0x1691f34: b.ls            #0x1691f94
    // 0x1691f38: mov             x1, x0
    // 0x1691f3c: r0 = _throwIfCompleted()
    //     0x1691f3c: bl              #0x863c30  ; [package:dio/src/dio_mixin.dart] _BaseHandler::_throwIfCompleted
    // 0x1691f40: ldur            x0, [fp, #-8]
    // 0x1691f44: LoadField: r2 = r0->field_7
    //     0x1691f44: ldur            w2, [x0, #7]
    // 0x1691f48: DecompressPointer r2
    //     0x1691f48: add             x2, x2, HEAP, lsl #32
    // 0x1691f4c: stur            x2, [fp, #-0x18]
    // 0x1691f50: r1 = <Response>
    //     0x1691f50: add             x1, PP, #8, lsl #12  ; [pp+0x8490] TypeArguments: <Response>
    //     0x1691f54: ldr             x1, [x1, #0x490]
    // 0x1691f58: r0 = InterceptorState()
    //     0x1691f58: bl              #0x86364c  ; AllocateInterceptorStateStub -> InterceptorState<X0> (size=0x14)
    // 0x1691f5c: mov             x1, x0
    // 0x1691f60: ldur            x0, [fp, #-0x10]
    // 0x1691f64: StoreField: r1->field_b = r0
    //     0x1691f64: stur            w0, [x1, #0xb]
    // 0x1691f68: r0 = Instance_InterceptorResultType
    //     0x1691f68: add             x0, PP, #8, lsl #12  ; [pp+0x83c8] Obj!InterceptorResultType@d751a1
    //     0x1691f6c: ldr             x0, [x0, #0x3c8]
    // 0x1691f70: StoreField: r1->field_f = r0
    //     0x1691f70: stur            w0, [x1, #0xf]
    // 0x1691f74: str             x1, [SP]
    // 0x1691f78: ldur            x1, [fp, #-0x18]
    // 0x1691f7c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x1691f7c: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x1691f80: r0 = complete()
    //     0x1691f80: bl              #0x16646a8  ; [dart:async] _AsyncCompleter::complete
    // 0x1691f84: r0 = Null
    //     0x1691f84: mov             x0, NULL
    // 0x1691f88: LeaveFrame
    //     0x1691f88: mov             SP, fp
    //     0x1691f8c: ldp             fp, lr, [SP], #0x10
    // 0x1691f90: ret
    //     0x1691f90: ret             
    // 0x1691f94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1691f94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1691f98: b               #0x1691f38
  }
}

// class id: 4987, size: 0x10, field offset: 0x10
class RequestInterceptorHandler extends _BaseHandler {

  _ reject(/* No info */) {
    // ** addr: 0x863b9c, size: 0x94
    // 0x863b9c: EnterFrame
    //     0x863b9c: stp             fp, lr, [SP, #-0x10]!
    //     0x863ba0: mov             fp, SP
    // 0x863ba4: AllocStack(0x20)
    //     0x863ba4: sub             SP, SP, #0x20
    // 0x863ba8: SetupParameters(RequestInterceptorHandler this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x863ba8: mov             x0, x1
    //     0x863bac: stur            x1, [fp, #-8]
    //     0x863bb0: stur            x2, [fp, #-0x10]
    // 0x863bb4: CheckStackOverflow
    //     0x863bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x863bb8: cmp             SP, x16
    //     0x863bbc: b.ls            #0x863c28
    // 0x863bc0: mov             x1, x0
    // 0x863bc4: r0 = _throwIfCompleted()
    //     0x863bc4: bl              #0x863c30  ; [package:dio/src/dio_mixin.dart] _BaseHandler::_throwIfCompleted
    // 0x863bc8: ldur            x0, [fp, #-8]
    // 0x863bcc: LoadField: r2 = r0->field_7
    //     0x863bcc: ldur            w2, [x0, #7]
    // 0x863bd0: DecompressPointer r2
    //     0x863bd0: add             x2, x2, HEAP, lsl #32
    // 0x863bd4: stur            x2, [fp, #-0x18]
    // 0x863bd8: r1 = <DioException>
    //     0x863bd8: add             x1, PP, #8, lsl #12  ; [pp+0x83c0] TypeArguments: <DioException>
    //     0x863bdc: ldr             x1, [x1, #0x3c0]
    // 0x863be0: r0 = InterceptorState()
    //     0x863be0: bl              #0x86364c  ; AllocateInterceptorStateStub -> InterceptorState<X0> (size=0x14)
    // 0x863be4: mov             x1, x0
    // 0x863be8: ldur            x0, [fp, #-0x10]
    // 0x863bec: StoreField: r1->field_b = r0
    //     0x863bec: stur            w0, [x1, #0xb]
    // 0x863bf0: r2 = Instance_InterceptorResultType
    //     0x863bf0: add             x2, PP, #8, lsl #12  ; [pp+0x83f0] Obj!InterceptorResultType@d75181
    //     0x863bf4: ldr             x2, [x2, #0x3f0]
    // 0x863bf8: StoreField: r1->field_f = r2
    //     0x863bf8: stur            w2, [x1, #0xf]
    // 0x863bfc: LoadField: r2 = r0->field_13
    //     0x863bfc: ldur            w2, [x0, #0x13]
    // 0x863c00: DecompressPointer r2
    //     0x863c00: add             x2, x2, HEAP, lsl #32
    // 0x863c04: str             x2, [SP]
    // 0x863c08: mov             x2, x1
    // 0x863c0c: ldur            x1, [fp, #-0x18]
    // 0x863c10: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x863c10: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x863c14: r0 = completeError()
    //     0x863c14: bl              #0x633464  ; [dart:async] _Completer::completeError
    // 0x863c18: r0 = Null
    //     0x863c18: mov             x0, NULL
    // 0x863c1c: LeaveFrame
    //     0x863c1c: mov             SP, fp
    //     0x863c20: ldp             fp, lr, [SP], #0x10
    // 0x863c24: ret
    //     0x863c24: ret             
    // 0x863c28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x863c28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x863c2c: b               #0x863bc0
  }
  _ resolve(/* No info */) {
    // ** addr: 0x863c84, size: 0x88
    // 0x863c84: EnterFrame
    //     0x863c84: stp             fp, lr, [SP, #-0x10]!
    //     0x863c88: mov             fp, SP
    // 0x863c8c: AllocStack(0x20)
    //     0x863c8c: sub             SP, SP, #0x20
    // 0x863c90: SetupParameters(RequestInterceptorHandler this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x863c90: mov             x0, x1
    //     0x863c94: stur            x1, [fp, #-8]
    //     0x863c98: stur            x2, [fp, #-0x10]
    // 0x863c9c: CheckStackOverflow
    //     0x863c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x863ca0: cmp             SP, x16
    //     0x863ca4: b.ls            #0x863d04
    // 0x863ca8: mov             x1, x0
    // 0x863cac: r0 = _throwIfCompleted()
    //     0x863cac: bl              #0x863c30  ; [package:dio/src/dio_mixin.dart] _BaseHandler::_throwIfCompleted
    // 0x863cb0: ldur            x0, [fp, #-8]
    // 0x863cb4: LoadField: r2 = r0->field_7
    //     0x863cb4: ldur            w2, [x0, #7]
    // 0x863cb8: DecompressPointer r2
    //     0x863cb8: add             x2, x2, HEAP, lsl #32
    // 0x863cbc: stur            x2, [fp, #-0x18]
    // 0x863cc0: r1 = <Response>
    //     0x863cc0: add             x1, PP, #8, lsl #12  ; [pp+0x8490] TypeArguments: <Response>
    //     0x863cc4: ldr             x1, [x1, #0x490]
    // 0x863cc8: r0 = InterceptorState()
    //     0x863cc8: bl              #0x86364c  ; AllocateInterceptorStateStub -> InterceptorState<X0> (size=0x14)
    // 0x863ccc: mov             x1, x0
    // 0x863cd0: ldur            x0, [fp, #-0x10]
    // 0x863cd4: StoreField: r1->field_b = r0
    //     0x863cd4: stur            w0, [x1, #0xb]
    // 0x863cd8: r0 = Instance_InterceptorResultType
    //     0x863cd8: add             x0, PP, #8, lsl #12  ; [pp+0x8460] Obj!InterceptorResultType@d751c1
    //     0x863cdc: ldr             x0, [x0, #0x460]
    // 0x863ce0: StoreField: r1->field_f = r0
    //     0x863ce0: stur            w0, [x1, #0xf]
    // 0x863ce4: str             x1, [SP]
    // 0x863ce8: ldur            x1, [fp, #-0x18]
    // 0x863cec: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x863cec: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x863cf0: r0 = complete()
    //     0x863cf0: bl              #0x16646a8  ; [dart:async] _AsyncCompleter::complete
    // 0x863cf4: r0 = Null
    //     0x863cf4: mov             x0, NULL
    // 0x863cf8: LeaveFrame
    //     0x863cf8: mov             SP, fp
    //     0x863cfc: ldp             fp, lr, [SP], #0x10
    // 0x863d00: ret
    //     0x863d00: ret             
    // 0x863d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x863d04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x863d08: b               #0x863ca8
  }
  _ next(/* No info */) {
    // ** addr: 0x1696a48, size: 0x88
    // 0x1696a48: EnterFrame
    //     0x1696a48: stp             fp, lr, [SP, #-0x10]!
    //     0x1696a4c: mov             fp, SP
    // 0x1696a50: AllocStack(0x20)
    //     0x1696a50: sub             SP, SP, #0x20
    // 0x1696a54: SetupParameters(RequestInterceptorHandler this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1696a54: mov             x0, x1
    //     0x1696a58: stur            x1, [fp, #-8]
    //     0x1696a5c: stur            x2, [fp, #-0x10]
    // 0x1696a60: CheckStackOverflow
    //     0x1696a60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1696a64: cmp             SP, x16
    //     0x1696a68: b.ls            #0x1696ac8
    // 0x1696a6c: mov             x1, x0
    // 0x1696a70: r0 = _throwIfCompleted()
    //     0x1696a70: bl              #0x863c30  ; [package:dio/src/dio_mixin.dart] _BaseHandler::_throwIfCompleted
    // 0x1696a74: ldur            x0, [fp, #-8]
    // 0x1696a78: LoadField: r2 = r0->field_7
    //     0x1696a78: ldur            w2, [x0, #7]
    // 0x1696a7c: DecompressPointer r2
    //     0x1696a7c: add             x2, x2, HEAP, lsl #32
    // 0x1696a80: stur            x2, [fp, #-0x18]
    // 0x1696a84: r1 = <RequestOptions>
    //     0x1696a84: add             x1, PP, #0xa, lsl #12  ; [pp+0xa720] TypeArguments: <RequestOptions>
    //     0x1696a88: ldr             x1, [x1, #0x720]
    // 0x1696a8c: r0 = InterceptorState()
    //     0x1696a8c: bl              #0x86364c  ; AllocateInterceptorStateStub -> InterceptorState<X0> (size=0x14)
    // 0x1696a90: mov             x1, x0
    // 0x1696a94: ldur            x0, [fp, #-0x10]
    // 0x1696a98: StoreField: r1->field_b = r0
    //     0x1696a98: stur            w0, [x1, #0xb]
    // 0x1696a9c: r0 = Instance_InterceptorResultType
    //     0x1696a9c: add             x0, PP, #8, lsl #12  ; [pp+0x83c8] Obj!InterceptorResultType@d751a1
    //     0x1696aa0: ldr             x0, [x0, #0x3c8]
    // 0x1696aa4: StoreField: r1->field_f = r0
    //     0x1696aa4: stur            w0, [x1, #0xf]
    // 0x1696aa8: str             x1, [SP]
    // 0x1696aac: ldur            x1, [fp, #-0x18]
    // 0x1696ab0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x1696ab0: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x1696ab4: r0 = complete()
    //     0x1696ab4: bl              #0x16646a8  ; [dart:async] _AsyncCompleter::complete
    // 0x1696ab8: r0 = Null
    //     0x1696ab8: mov             x0, NULL
    // 0x1696abc: LeaveFrame
    //     0x1696abc: mov             SP, fp
    //     0x1696ac0: ldp             fp, lr, [SP], #0x10
    // 0x1696ac4: ret
    //     0x1696ac4: ret             
    // 0x1696ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1696ac8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1696acc: b               #0x1696a6c
  }
}

// class id: 4988, size: 0x14, field offset: 0x8
//   const constructor, 
class InterceptorState<X0> extends Object {

  _ toString(/* No info */) {
    // ** addr: 0x1568420, size: 0xbc
    // 0x1568420: EnterFrame
    //     0x1568420: stp             fp, lr, [SP, #-0x10]!
    //     0x1568424: mov             fp, SP
    // 0x1568428: AllocStack(0x10)
    //     0x1568428: sub             SP, SP, #0x10
    // 0x156842c: CheckStackOverflow
    //     0x156842c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1568430: cmp             SP, x16
    //     0x1568434: b.ls            #0x15684d4
    // 0x1568438: r1 = Null
    //     0x1568438: mov             x1, NULL
    // 0x156843c: r2 = 14
    //     0x156843c: movz            x2, #0xe
    // 0x1568440: r0 = AllocateArray()
    //     0x1568440: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1568444: stur            x0, [fp, #-8]
    // 0x1568448: r16 = "InterceptorState<"
    //     0x1568448: add             x16, PP, #0x26, lsl #12  ; [pp+0x26290] "InterceptorState<"
    //     0x156844c: ldr             x16, [x16, #0x290]
    // 0x1568450: StoreField: r0->field_f = r16
    //     0x1568450: stur            w16, [x0, #0xf]
    // 0x1568454: ldr             x3, [fp, #0x10]
    // 0x1568458: LoadField: r2 = r3->field_7
    //     0x1568458: ldur            w2, [x3, #7]
    // 0x156845c: DecompressPointer r2
    //     0x156845c: add             x2, x2, HEAP, lsl #32
    // 0x1568460: r1 = Null
    //     0x1568460: mov             x1, NULL
    // 0x1568464: r3 = X0
    //     0x1568464: ldr             x3, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x1568468: r30 = InstantiateTypeNonNullableClassTypeParameterStub
    //     0x1568468: add             lr, PP, #0xa, lsl #12  ; [pp+0xa970] Stub: InstantiateTypeNonNullableClassTypeParameter (0x6011e0)
    //     0x156846c: ldr             lr, [lr, #0x970]
    // 0x1568470: LoadField: r30 = r30->field_7
    //     0x1568470: ldur            lr, [lr, #7]
    // 0x1568474: blr             lr
    // 0x1568478: mov             x1, x0
    // 0x156847c: ldur            x0, [fp, #-8]
    // 0x1568480: StoreField: r0->field_13 = r1
    //     0x1568480: stur            w1, [x0, #0x13]
    // 0x1568484: r16 = ">(type: "
    //     0x1568484: add             x16, PP, #0x26, lsl #12  ; [pp+0x26298] ">(type: "
    //     0x1568488: ldr             x16, [x16, #0x298]
    // 0x156848c: ArrayStore: r0[0] = r16  ; List_4
    //     0x156848c: stur            w16, [x0, #0x17]
    // 0x1568490: ldr             x1, [fp, #0x10]
    // 0x1568494: LoadField: r2 = r1->field_f
    //     0x1568494: ldur            w2, [x1, #0xf]
    // 0x1568498: DecompressPointer r2
    //     0x1568498: add             x2, x2, HEAP, lsl #32
    // 0x156849c: StoreField: r0->field_1b = r2
    //     0x156849c: stur            w2, [x0, #0x1b]
    // 0x15684a0: r16 = ", data: "
    //     0x15684a0: add             x16, PP, #0x26, lsl #12  ; [pp+0x262a0] ", data: "
    //     0x15684a4: ldr             x16, [x16, #0x2a0]
    // 0x15684a8: StoreField: r0->field_1f = r16
    //     0x15684a8: stur            w16, [x0, #0x1f]
    // 0x15684ac: LoadField: r2 = r1->field_b
    //     0x15684ac: ldur            w2, [x1, #0xb]
    // 0x15684b0: DecompressPointer r2
    //     0x15684b0: add             x2, x2, HEAP, lsl #32
    // 0x15684b4: StoreField: r0->field_23 = r2
    //     0x15684b4: stur            w2, [x0, #0x23]
    // 0x15684b8: r16 = ")"
    //     0x15684b8: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0x15684bc: StoreField: r0->field_27 = r16
    //     0x15684bc: stur            w16, [x0, #0x27]
    // 0x15684c0: str             x0, [SP]
    // 0x15684c4: r0 = _interpolate()
    //     0x15684c4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x15684c8: LeaveFrame
    //     0x15684c8: mov             SP, fp
    //     0x15684cc: ldp             fp, lr, [SP], #0x10
    // 0x15684d0: ret
    //     0x15684d0: ret             
    // 0x15684d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15684d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15684d8: b               #0x1568438
  }
}

// class id: 4990, size: 0x8, field offset: 0x8
abstract class DioMixin extends Object
    implements Dio {

  static _ assureDioException(/* No info */) {
    // ** addr: 0x861d24, size: 0x84
    // 0x861d24: EnterFrame
    //     0x861d24: stp             fp, lr, [SP, #-0x10]!
    //     0x861d28: mov             fp, SP
    // 0x861d2c: AllocStack(0x10)
    //     0x861d2c: sub             SP, SP, #0x10
    // 0x861d30: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x861d30: mov             x0, x1
    //     0x861d34: mov             x3, x2
    //     0x861d38: stur            x1, [fp, #-8]
    //     0x861d3c: stur            x2, [fp, #-0x10]
    // 0x861d40: CheckStackOverflow
    //     0x861d40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x861d44: cmp             SP, x16
    //     0x861d48: b.ls            #0x861da0
    // 0x861d4c: r1 = 60
    //     0x861d4c: movz            x1, #0x3c
    // 0x861d50: branchIfSmi(r0, 0x861d5c)
    //     0x861d50: tbz             w0, #0, #0x861d5c
    // 0x861d54: r1 = LoadClassIdInstr(r0)
    //     0x861d54: ldur            x1, [x0, #-1]
    //     0x861d58: ubfx            x1, x1, #0xc, #0x14
    // 0x861d5c: r17 = 4989
    //     0x861d5c: movz            x17, #0x137d
    // 0x861d60: cmp             x1, x17
    // 0x861d64: b.ne            #0x861d74
    // 0x861d68: LeaveFrame
    //     0x861d68: mov             SP, fp
    //     0x861d6c: ldp             fp, lr, [SP], #0x10
    // 0x861d70: ret
    //     0x861d70: ret             
    // 0x861d74: r0 = DioException()
    //     0x861d74: bl              #0x86204c  ; AllocateDioExceptionStub -> DioException (size=0x20)
    // 0x861d78: mov             x1, x0
    // 0x861d7c: ldur            x2, [fp, #-8]
    // 0x861d80: ldur            x3, [fp, #-0x10]
    // 0x861d84: stur            x0, [fp, #-8]
    // 0x861d88: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x861d88: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x861d8c: r0 = DioException()
    //     0x861d8c: bl              #0x861da8  ; [package:dio/src/dio_exception.dart] DioException::DioException
    // 0x861d90: ldur            x0, [fp, #-8]
    // 0x861d94: LeaveFrame
    //     0x861d94: mov             SP, fp
    //     0x861d98: ldp             fp, lr, [SP], #0x10
    // 0x861d9c: ret
    //     0x861d9c: ret             
    // 0x861da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x861da0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x861da4: b               #0x861d4c
  }
  static Response<Y0> assureResponse<Y0>(Object, RequestOptions) {
    // ** addr: 0x862058, size: 0x2bc
    // 0x862058: EnterFrame
    //     0x862058: stp             fp, lr, [SP, #-0x10]!
    //     0x86205c: mov             fp, SP
    // 0x862060: AllocStack(0x50)
    //     0x862060: sub             SP, SP, #0x50
    // 0x862064: SetupParameters()
    //     0x862064: ldur            w0, [x4, #0xf]
    //     0x862068: cbnz            w0, #0x862074
    //     0x86206c: mov             x4, NULL
    //     0x862070: b               #0x862084
    //     0x862074: ldur            w0, [x4, #0x17]
    //     0x862078: add             x1, fp, w0, sxtw #2
    //     0x86207c: ldr             x1, [x1, #0x10]
    //     0x862080: mov             x4, x1
    //     0x862084: ldr             x3, [fp, #0x18]
    //     0x862088: stur            x4, [fp, #-8]
    // 0x86208c: CheckStackOverflow
    //     0x86208c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862090: cmp             SP, x16
    //     0x862094: b.ls            #0x862300
    // 0x862098: r0 = 60
    //     0x862098: movz            x0, #0x3c
    // 0x86209c: branchIfSmi(r3, 0x8620a8)
    //     0x86209c: tbz             w3, #0, #0x8620a8
    // 0x8620a0: r0 = LoadClassIdInstr(r3)
    //     0x8620a0: ldur            x0, [x3, #-1]
    //     0x8620a4: ubfx            x0, x0, #0xc, #0x14
    // 0x8620a8: r17 = 4973
    //     0x8620a8: movz            x17, #0x136d
    // 0x8620ac: cmp             x0, x17
    // 0x8620b0: b.eq            #0x862120
    // 0x8620b4: mov             x0, x3
    // 0x8620b8: mov             x1, x4
    // 0x8620bc: r2 = Null
    //     0x8620bc: mov             x2, NULL
    // 0x8620c0: cmp             w1, NULL
    // 0x8620c4: b.eq            #0x8620e8
    // 0x8620c8: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x8620c8: ldur            w4, [x1, #0x17]
    // 0x8620cc: DecompressPointer r4
    //     0x8620cc: add             x4, x4, HEAP, lsl #32
    // 0x8620d0: r8 = Y0
    //     0x8620d0: add             x8, PP, #0xa, lsl #12  ; [pp+0xa728] TypeParameter: Y0
    //     0x8620d4: ldr             x8, [x8, #0x728]
    // 0x8620d8: LoadField: r9 = r4->field_7
    //     0x8620d8: ldur            x9, [x4, #7]
    // 0x8620dc: r3 = Null
    //     0x8620dc: add             x3, PP, #0xa, lsl #12  ; [pp+0xa730] Null
    //     0x8620e0: ldr             x3, [x3, #0x730]
    // 0x8620e4: blr             x9
    // 0x8620e8: ldur            x1, [fp, #-8]
    // 0x8620ec: r0 = Response()
    //     0x8620ec: bl              #0x862fc0  ; AllocateResponseStub -> Response<X0> (size=0x2c)
    // 0x8620f0: stur            x0, [fp, #-0x10]
    // 0x8620f4: ldr             x16, [fp, #0x18]
    // 0x8620f8: str             x16, [SP]
    // 0x8620fc: mov             x1, x0
    // 0x862100: ldr             x2, [fp, #0x10]
    // 0x862104: r4 = const [0, 0x3, 0x1, 0x2, data, 0x2, null]
    //     0x862104: add             x4, PP, #0xa, lsl #12  ; [pp+0xa740] List(7) [0, 0x3, 0x1, 0x2, "data", 0x2, Null]
    //     0x862108: ldr             x4, [x4, #0x740]
    // 0x86210c: r0 = Response()
    //     0x86210c: bl              #0x862bac  ; [package:dio/src/response.dart] Response::Response
    // 0x862110: ldur            x0, [fp, #-0x10]
    // 0x862114: LeaveFrame
    //     0x862114: mov             SP, fp
    //     0x862118: ldp             fp, lr, [SP], #0x10
    // 0x86211c: ret
    //     0x86211c: ret             
    // 0x862120: ldr             x0, [fp, #0x18]
    // 0x862124: ldur            x1, [fp, #-8]
    // 0x862128: r2 = Null
    //     0x862128: mov             x2, NULL
    // 0x86212c: cmp             w0, NULL
    // 0x862130: b.eq            #0x86217c
    // 0x862134: branchIfSmi(r0, 0x86217c)
    //     0x862134: tbz             w0, #0, #0x86217c
    // 0x862138: r3 = SubtypeTestCache
    //     0x862138: add             x3, PP, #0xa, lsl #12  ; [pp+0xa748] SubtypeTestCache
    //     0x86213c: ldr             x3, [x3, #0x748]
    // 0x862140: r30 = Subtype4TestCacheStub
    //     0x862140: ldr             lr, [PP, #0x20]  ; [pp+0x20] Stub: Subtype4TestCache (0x6129c8)
    // 0x862144: LoadField: r30 = r30->field_7
    //     0x862144: ldur            lr, [lr, #7]
    // 0x862148: blr             lr
    // 0x86214c: cmp             w7, NULL
    // 0x862150: b.eq            #0x86215c
    // 0x862154: tbnz            w7, #4, #0x86217c
    // 0x862158: b               #0x862184
    // 0x86215c: r8 = Response<Y0>
    //     0x86215c: add             x8, PP, #0xa, lsl #12  ; [pp+0xa750] Type: Response<Y0>
    //     0x862160: ldr             x8, [x8, #0x750]
    // 0x862164: r3 = SubtypeTestCache
    //     0x862164: add             x3, PP, #0xa, lsl #12  ; [pp+0xa758] SubtypeTestCache
    //     0x862168: ldr             x3, [x3, #0x758]
    // 0x86216c: r30 = InstanceOfStub
    //     0x86216c: ldr             lr, [PP, #0x340]  ; [pp+0x340] Stub: InstanceOf (0x60127c)
    // 0x862170: LoadField: r30 = r30->field_7
    //     0x862170: ldur            lr, [lr, #7]
    // 0x862174: blr             lr
    // 0x862178: b               #0x862188
    // 0x86217c: r0 = false
    //     0x86217c: add             x0, NULL, #0x30  ; false
    // 0x862180: b               #0x862188
    // 0x862184: r0 = true
    //     0x862184: add             x0, NULL, #0x20  ; true
    // 0x862188: tbz             w0, #4, #0x8622f0
    // 0x86218c: ldr             x3, [fp, #0x18]
    // 0x862190: LoadField: r4 = r3->field_b
    //     0x862190: ldur            w4, [x3, #0xb]
    // 0x862194: DecompressPointer r4
    //     0x862194: add             x4, x4, HEAP, lsl #32
    // 0x862198: mov             x0, x4
    // 0x86219c: ldur            x1, [fp, #-8]
    // 0x8621a0: stur            x4, [fp, #-0x10]
    // 0x8621a4: r2 = Null
    //     0x8621a4: mov             x2, NULL
    // 0x8621a8: cmp             w0, NULL
    // 0x8621ac: b.eq            #0x8621d8
    // 0x8621b0: cmp             w1, NULL
    // 0x8621b4: b.eq            #0x8621d8
    // 0x8621b8: ArrayLoad: r4 = r1[0]  ; List_4
    //     0x8621b8: ldur            w4, [x1, #0x17]
    // 0x8621bc: DecompressPointer r4
    //     0x8621bc: add             x4, x4, HEAP, lsl #32
    // 0x8621c0: r8 = Y0?
    //     0x8621c0: add             x8, PP, #0xa, lsl #12  ; [pp+0xa760] TypeParameter: Y0?
    //     0x8621c4: ldr             x8, [x8, #0x760]
    // 0x8621c8: LoadField: r9 = r4->field_7
    //     0x8621c8: ldur            x9, [x4, #7]
    // 0x8621cc: r3 = Null
    //     0x8621cc: add             x3, PP, #0xa, lsl #12  ; [pp+0xa768] Null
    //     0x8621d0: ldr             x3, [x3, #0x768]
    // 0x8621d4: blr             x9
    // 0x8621d8: ldur            x0, [fp, #-0x10]
    // 0x8621dc: r1 = 60
    //     0x8621dc: movz            x1, #0x3c
    // 0x8621e0: branchIfSmi(r0, 0x8621ec)
    //     0x8621e0: tbz             w0, #0, #0x8621ec
    // 0x8621e4: r1 = LoadClassIdInstr(r0)
    //     0x8621e4: ldur            x1, [x0, #-1]
    //     0x8621e8: ubfx            x1, x1, #0xc, #0x14
    // 0x8621ec: r17 = 4997
    //     0x8621ec: movz            x17, #0x1385
    // 0x8621f0: cmp             x1, x17
    // 0x8621f4: b.ne            #0x86223c
    // 0x8621f8: ldr             x1, [fp, #0x10]
    // 0x8621fc: LoadField: r2 = r0->field_1f
    //     0x8621fc: ldur            w2, [x0, #0x1f]
    // 0x862200: DecompressPointer r2
    //     0x862200: add             x2, x2, HEAP, lsl #32
    // 0x862204: stur            x2, [fp, #-0x18]
    // 0x862208: LoadField: r3 = r1->field_f
    //     0x862208: ldur            w3, [x1, #0xf]
    // 0x86220c: DecompressPointer r3
    //     0x86220c: add             x3, x3, HEAP, lsl #32
    // 0x862210: r16 = Sentinel
    //     0x862210: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x862214: cmp             w3, w16
    // 0x862218: b.eq            #0x862308
    // 0x86221c: r0 = Headers()
    //     0x86221c: bl              #0x862ba0  ; AllocateHeadersStub -> Headers (size=0xc)
    // 0x862220: mov             x1, x0
    // 0x862224: ldur            x2, [fp, #-0x18]
    // 0x862228: stur            x0, [fp, #-0x18]
    // 0x86222c: r0 = Headers.fromMap()
    //     0x86222c: bl              #0x862314  ; [package:dio/src/headers.dart] Headers::Headers.fromMap
    // 0x862230: ldur            x3, [fp, #-0x18]
    // 0x862234: ldr             x0, [fp, #0x18]
    // 0x862238: b               #0x86224c
    // 0x86223c: ldr             x0, [fp, #0x18]
    // 0x862240: LoadField: r1 = r0->field_1b
    //     0x862240: ldur            w1, [x0, #0x1b]
    // 0x862244: DecompressPointer r1
    //     0x862244: add             x1, x1, HEAP, lsl #32
    // 0x862248: mov             x3, x1
    // 0x86224c: ldur            x2, [fp, #-0x10]
    // 0x862250: stur            x3, [fp, #-0x48]
    // 0x862254: LoadField: r4 = r0->field_f
    //     0x862254: ldur            w4, [x0, #0xf]
    // 0x862258: DecompressPointer r4
    //     0x862258: add             x4, x4, HEAP, lsl #32
    // 0x86225c: stur            x4, [fp, #-0x40]
    // 0x862260: LoadField: r5 = r0->field_13
    //     0x862260: ldur            w5, [x0, #0x13]
    // 0x862264: DecompressPointer r5
    //     0x862264: add             x5, x5, HEAP, lsl #32
    // 0x862268: stur            x5, [fp, #-0x38]
    // 0x86226c: LoadField: r6 = r0->field_1f
    //     0x86226c: ldur            w6, [x0, #0x1f]
    // 0x862270: DecompressPointer r6
    //     0x862270: add             x6, x6, HEAP, lsl #32
    // 0x862274: stur            x6, [fp, #-0x30]
    // 0x862278: LoadField: r7 = r0->field_23
    //     0x862278: ldur            w7, [x0, #0x23]
    // 0x86227c: DecompressPointer r7
    //     0x86227c: add             x7, x7, HEAP, lsl #32
    // 0x862280: stur            x7, [fp, #-0x28]
    // 0x862284: ArrayLoad: r8 = r0[0]  ; List_4
    //     0x862284: ldur            w8, [x0, #0x17]
    // 0x862288: DecompressPointer r8
    //     0x862288: add             x8, x8, HEAP, lsl #32
    // 0x86228c: stur            x8, [fp, #-0x20]
    // 0x862290: LoadField: r9 = r0->field_27
    //     0x862290: ldur            w9, [x0, #0x27]
    // 0x862294: DecompressPointer r9
    //     0x862294: add             x9, x9, HEAP, lsl #32
    // 0x862298: ldur            x1, [fp, #-8]
    // 0x86229c: stur            x9, [fp, #-0x18]
    // 0x8622a0: r0 = Response()
    //     0x8622a0: bl              #0x862fc0  ; AllocateResponseStub -> Response<X0> (size=0x2c)
    // 0x8622a4: ldur            x1, [fp, #-0x10]
    // 0x8622a8: StoreField: r0->field_b = r1
    //     0x8622a8: stur            w1, [x0, #0xb]
    // 0x8622ac: ldur            x1, [fp, #-0x40]
    // 0x8622b0: StoreField: r0->field_f = r1
    //     0x8622b0: stur            w1, [x0, #0xf]
    // 0x8622b4: ldur            x1, [fp, #-0x38]
    // 0x8622b8: StoreField: r0->field_13 = r1
    //     0x8622b8: stur            w1, [x0, #0x13]
    // 0x8622bc: ldur            x1, [fp, #-0x20]
    // 0x8622c0: ArrayStore: r0[0] = r1  ; List_4
    //     0x8622c0: stur            w1, [x0, #0x17]
    // 0x8622c4: ldur            x1, [fp, #-0x30]
    // 0x8622c8: StoreField: r0->field_1f = r1
    //     0x8622c8: stur            w1, [x0, #0x1f]
    // 0x8622cc: ldur            x1, [fp, #-0x28]
    // 0x8622d0: StoreField: r0->field_23 = r1
    //     0x8622d0: stur            w1, [x0, #0x23]
    // 0x8622d4: ldur            x1, [fp, #-0x48]
    // 0x8622d8: StoreField: r0->field_1b = r1
    //     0x8622d8: stur            w1, [x0, #0x1b]
    // 0x8622dc: ldur            x1, [fp, #-0x18]
    // 0x8622e0: StoreField: r0->field_27 = r1
    //     0x8622e0: stur            w1, [x0, #0x27]
    // 0x8622e4: LeaveFrame
    //     0x8622e4: mov             SP, fp
    //     0x8622e8: ldp             fp, lr, [SP], #0x10
    // 0x8622ec: ret
    //     0x8622ec: ret             
    // 0x8622f0: ldr             x0, [fp, #0x18]
    // 0x8622f4: LeaveFrame
    //     0x8622f4: mov             SP, fp
    //     0x8622f8: ldp             fp, lr, [SP], #0x10
    // 0x8622fc: ret
    //     0x8622fc: ret             
    // 0x862300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862300: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862304: b               #0x862098
    // 0x862308: r9 = preserveHeaderCase
    //     0x862308: add             x9, PP, #8, lsl #12  ; [pp+0x84c0] Field <<EMAIL>>: late (offset: 0x10)
    //     0x86230c: ldr             x9, [x9, #0x4c0]
    // 0x862310: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x862310: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static Future<Y0> listenCancelForAsyncTask<Y0>(CancelToken?, Future<Y0>) {
    // ** addr: 0x863280, size: 0x130
    // 0x863280: EnterFrame
    //     0x863280: stp             fp, lr, [SP, #-0x10]!
    //     0x863284: mov             fp, SP
    // 0x863288: AllocStack(0x38)
    //     0x863288: sub             SP, SP, #0x38
    // 0x86328c: SetupParameters()
    //     0x86328c: ldur            w0, [x4, #0xf]
    //     0x863290: cbnz            w0, #0x86329c
    //     0x863294: mov             x4, NULL
    //     0x863298: b               #0x8632ac
    //     0x86329c: ldur            w0, [x4, #0x17]
    //     0x8632a0: add             x1, fp, w0, sxtw #2
    //     0x8632a4: ldr             x1, [x1, #0x10]
    //     0x8632a8: mov             x4, x1
    //     0x8632ac: ldr             x0, [fp, #0x18]
    //     0x8632b0: stur            x4, [fp, #-8]
    // 0x8632b4: CheckStackOverflow
    //     0x8632b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8632b8: cmp             SP, x16
    //     0x8632bc: b.ls            #0x8633a8
    // 0x8632c0: cmp             w0, NULL
    // 0x8632c4: b.ne            #0x8632d8
    // 0x8632c8: ldr             x0, [fp, #0x10]
    // 0x8632cc: LeaveFrame
    //     0x8632cc: mov             SP, fp
    //     0x8632d0: ldp             fp, lr, [SP], #0x10
    // 0x8632d4: ret
    //     0x8632d4: ret             
    // 0x8632d8: ldr             x5, [fp, #0x10]
    // 0x8632dc: mov             x1, x4
    // 0x8632e0: r2 = Null
    //     0x8632e0: mov             x2, NULL
    // 0x8632e4: r3 = <Future<Y0>>
    //     0x8632e4: add             x3, PP, #8, lsl #12  ; [pp+0x8428] TypeArguments: <Future<Y0>>
    //     0x8632e8: ldr             x3, [x3, #0x428]
    // 0x8632ec: r30 = InstantiateTypeArgumentsStub
    //     0x8632ec: ldr             lr, [PP, #0x208]  ; [pp+0x208] Stub: InstantiateTypeArguments (0x600f4c)
    // 0x8632f0: LoadField: r30 = r30->field_7
    //     0x8632f0: ldur            lr, [lr, #7]
    // 0x8632f4: blr             lr
    // 0x8632f8: mov             x3, x0
    // 0x8632fc: ldr             x0, [fp, #0x18]
    // 0x863300: stur            x3, [fp, #-0x18]
    // 0x863304: LoadField: r1 = r0->field_7
    //     0x863304: ldur            w1, [x0, #7]
    // 0x863308: DecompressPointer r1
    //     0x863308: add             x1, x1, HEAP, lsl #32
    // 0x86330c: LoadField: r0 = r1->field_b
    //     0x86330c: ldur            w0, [x1, #0xb]
    // 0x863310: DecompressPointer r0
    //     0x863310: add             x0, x0, HEAP, lsl #32
    // 0x863314: stur            x0, [fp, #-0x10]
    // 0x863318: r1 = Function '<anonymous closure>': static.
    //     0x863318: add             x1, PP, #8, lsl #12  ; [pp+0x8430] AnonymousClosure: static (0x863638), in [package:dio/src/dio_mixin.dart] DioMixin::listenCancelForAsyncTask (0x863280)
    //     0x86331c: ldr             x1, [x1, #0x430]
    // 0x863320: r2 = Null
    //     0x863320: mov             x2, NULL
    // 0x863324: r0 = AllocateClosure()
    //     0x863324: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x863328: mov             x1, x0
    // 0x86332c: ldur            x0, [fp, #-8]
    // 0x863330: StoreField: r1->field_b = r0
    //     0x863330: stur            w0, [x1, #0xb]
    // 0x863334: ldur            x16, [fp, #-0x10]
    // 0x863338: stp             x16, x0, [SP, #8]
    // 0x86333c: str             x1, [SP]
    // 0x863340: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x863340: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x863344: r0 = then()
    //     0x863344: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x863348: r1 = Null
    //     0x863348: mov             x1, NULL
    // 0x86334c: r2 = 4
    //     0x86334c: movz            x2, #0x4
    // 0x863350: stur            x0, [fp, #-0x10]
    // 0x863354: r0 = AllocateArray()
    //     0x863354: bl              #0x16f7198  ; AllocateArrayStub
    // 0x863358: mov             x2, x0
    // 0x86335c: ldr             x0, [fp, #0x10]
    // 0x863360: stur            x2, [fp, #-0x20]
    // 0x863364: StoreField: r2->field_f = r0
    //     0x863364: stur            w0, [x2, #0xf]
    // 0x863368: ldur            x0, [fp, #-0x10]
    // 0x86336c: StoreField: r2->field_13 = r0
    //     0x86336c: stur            w0, [x2, #0x13]
    // 0x863370: ldur            x1, [fp, #-0x18]
    // 0x863374: r0 = AllocateGrowableArray()
    //     0x863374: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x863378: mov             x1, x0
    // 0x86337c: ldur            x0, [fp, #-0x20]
    // 0x863380: StoreField: r1->field_f = r0
    //     0x863380: stur            w0, [x1, #0xf]
    // 0x863384: r0 = 4
    //     0x863384: movz            x0, #0x4
    // 0x863388: StoreField: r1->field_b = r0
    //     0x863388: stur            w0, [x1, #0xb]
    // 0x86338c: ldur            x16, [fp, #-8]
    // 0x863390: stp             x1, x16, [SP]
    // 0x863394: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x863394: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x863398: r0 = any()
    //     0x863398: bl              #0x8633b0  ; [dart:async] Future::any
    // 0x86339c: LeaveFrame
    //     0x86339c: mov             SP, fp
    //     0x8633a0: ldp             fp, lr, [SP], #0x10
    // 0x8633a4: ret
    //     0x8633a4: ret             
    // 0x8633a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8633a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8633ac: b               #0x8632c0
  }
  [closure] static Never <anonymous closure>(dynamic, DioException) {
    // ** addr: 0x863638, size: 0x14
    // 0x863638: EnterFrame
    //     0x863638: stp             fp, lr, [SP, #-0x10]!
    //     0x86363c: mov             fp, SP
    // 0x863640: ldr             x0, [fp, #0x10]
    // 0x863644: r0 = Throw()
    //     0x863644: bl              #0x16f5420  ; ThrowStub
    // 0x863648: brk             #0
  }
  static _ checkCancelled(/* No info */) {
    // ** addr: 0x8644bc, size: 0x40
    // 0x8644bc: EnterFrame
    //     0x8644bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8644c0: mov             fp, SP
    // 0x8644c4: cmp             w1, NULL
    // 0x8644c8: b.ne            #0x8644d4
    // 0x8644cc: r0 = Null
    //     0x8644cc: mov             x0, NULL
    // 0x8644d0: b               #0x8644dc
    // 0x8644d4: LoadField: r0 = r1->field_b
    //     0x8644d4: ldur            w0, [x1, #0xb]
    // 0x8644d8: DecompressPointer r0
    //     0x8644d8: add             x0, x0, HEAP, lsl #32
    // 0x8644dc: cmp             w0, NULL
    // 0x8644e0: b.ne            #0x8644f4
    // 0x8644e4: r0 = Null
    //     0x8644e4: mov             x0, NULL
    // 0x8644e8: LeaveFrame
    //     0x8644e8: mov             SP, fp
    //     0x8644ec: ldp             fp, lr, [SP], #0x10
    // 0x8644f0: ret
    //     0x8644f0: ret             
    // 0x8644f4: r0 = Throw()
    //     0x8644f4: bl              #0x16f5420  ; ThrowStub
    // 0x8644f8: brk             #0
  }
  static _ checkOptions(/* No info */) {
    // ** addr: 0x885b94, size: 0x5c
    // 0x885b94: EnterFrame
    //     0x885b94: stp             fp, lr, [SP, #-0x10]!
    //     0x885b98: mov             fp, SP
    // 0x885b9c: AllocStack(0x8)
    //     0x885b9c: sub             SP, SP, #8
    // 0x885ba0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x885ba0: mov             x0, x1
    //     0x885ba4: stur            x1, [fp, #-8]
    // 0x885ba8: cmp             w2, NULL
    // 0x885bac: b.ne            #0x885bbc
    // 0x885bb0: r0 = Options()
    //     0x885bb0: bl              #0x885bf0  ; AllocateOptionsStub -> Options (size=0x48)
    // 0x885bb4: mov             x1, x0
    // 0x885bb8: b               #0x885bc0
    // 0x885bbc: mov             x1, x2
    // 0x885bc0: ldur            x0, [fp, #-8]
    // 0x885bc4: StoreField: r1->field_7 = r0
    //     0x885bc4: stur            w0, [x1, #7]
    //     0x885bc8: ldurb           w16, [x1, #-1]
    //     0x885bcc: ldurb           w17, [x0, #-1]
    //     0x885bd0: and             x16, x17, x16, lsr #2
    //     0x885bd4: tst             x16, HEAP, lsr #32
    //     0x885bd8: b.eq            #0x885be0
    //     0x885bdc: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x885be0: mov             x0, x1
    // 0x885be4: LeaveFrame
    //     0x885be4: mov             SP, fp
    //     0x885be8: ldp             fp, lr, [SP], #0x10
    // 0x885bec: ret
    //     0x885bec: ret             
  }
}

// class id: 5010, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Interceptor extends Object {

  [closure] void onError(dynamic, DioException, ErrorInterceptorHandler) {
    // ** addr: 0x168b548, size: 0x38
    // 0x168b548: EnterFrame
    //     0x168b548: stp             fp, lr, [SP, #-0x10]!
    //     0x168b54c: mov             fp, SP
    // 0x168b550: CheckStackOverflow
    //     0x168b550: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x168b554: cmp             SP, x16
    //     0x168b558: b.ls            #0x168b578
    // 0x168b55c: ldr             x1, [fp, #0x10]
    // 0x168b560: ldr             x2, [fp, #0x18]
    // 0x168b564: r0 = next()
    //     0x168b564: bl              #0x168b580  ; [package:dio/src/dio_mixin.dart] ErrorInterceptorHandler::next
    // 0x168b568: r0 = Null
    //     0x168b568: mov             x0, NULL
    // 0x168b56c: LeaveFrame
    //     0x168b56c: mov             SP, fp
    //     0x168b570: ldp             fp, lr, [SP], #0x10
    // 0x168b574: ret
    //     0x168b574: ret             
    // 0x168b578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x168b578: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x168b57c: b               #0x168b55c
  }
  dynamic onError(dynamic) {
    // ** addr: 0x168d64c, size: 0x24
    // 0x168d64c: EnterFrame
    //     0x168d64c: stp             fp, lr, [SP, #-0x10]!
    //     0x168d650: mov             fp, SP
    // 0x168d654: ldr             x2, [fp, #0x10]
    // 0x168d658: r1 = Function 'onError':.
    //     0x168d658: add             x1, PP, #0x26, lsl #12  ; [pp+0x262c0] AnonymousClosure: (0x168b548), of [package:dio/src/dio_mixin.dart] Interceptor
    //     0x168d65c: ldr             x1, [x1, #0x2c0]
    // 0x168d660: r0 = AllocateClosure()
    //     0x168d660: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x168d664: LeaveFrame
    //     0x168d664: mov             SP, fp
    //     0x168d668: ldp             fp, lr, [SP], #0x10
    // 0x168d66c: ret
    //     0x168d66c: ret             
  }
  [closure] void onResponse(dynamic, Response<dynamic>, ResponseInterceptorHandler) {
    // ** addr: 0x1691edc, size: 0x38
    // 0x1691edc: EnterFrame
    //     0x1691edc: stp             fp, lr, [SP, #-0x10]!
    //     0x1691ee0: mov             fp, SP
    // 0x1691ee4: CheckStackOverflow
    //     0x1691ee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1691ee8: cmp             SP, x16
    //     0x1691eec: b.ls            #0x1691f0c
    // 0x1691ef0: ldr             x1, [fp, #0x10]
    // 0x1691ef4: ldr             x2, [fp, #0x18]
    // 0x1691ef8: r0 = next()
    //     0x1691ef8: bl              #0x1691f14  ; [package:dio/src/dio_mixin.dart] ResponseInterceptorHandler::next
    // 0x1691efc: r0 = Null
    //     0x1691efc: mov             x0, NULL
    // 0x1691f00: LeaveFrame
    //     0x1691f00: mov             SP, fp
    //     0x1691f04: ldp             fp, lr, [SP], #0x10
    // 0x1691f08: ret
    //     0x1691f08: ret             
    // 0x1691f0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1691f0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1691f10: b               #0x1691ef0
  }
  dynamic onResponse(dynamic) {
    // ** addr: 0x169217c, size: 0x24
    // 0x169217c: EnterFrame
    //     0x169217c: stp             fp, lr, [SP, #-0x10]!
    //     0x1692180: mov             fp, SP
    // 0x1692184: ldr             x2, [fp, #0x10]
    // 0x1692188: r1 = Function 'onResponse':.
    //     0x1692188: add             x1, PP, #0x26, lsl #12  ; [pp+0x262c8] AnonymousClosure: (0x1691edc), of [package:dio/src/dio_mixin.dart] Interceptor
    //     0x169218c: ldr             x1, [x1, #0x2c8]
    // 0x1692190: r0 = AllocateClosure()
    //     0x1692190: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1692194: LeaveFrame
    //     0x1692194: mov             SP, fp
    //     0x1692198: ldp             fp, lr, [SP], #0x10
    // 0x169219c: ret
    //     0x169219c: ret             
  }
  [closure] void onRequest(dynamic, RequestOptions, RequestInterceptorHandler) {
    // ** addr: 0x1696a10, size: 0x38
    // 0x1696a10: EnterFrame
    //     0x1696a10: stp             fp, lr, [SP, #-0x10]!
    //     0x1696a14: mov             fp, SP
    // 0x1696a18: CheckStackOverflow
    //     0x1696a18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1696a1c: cmp             SP, x16
    //     0x1696a20: b.ls            #0x1696a40
    // 0x1696a24: ldr             x1, [fp, #0x10]
    // 0x1696a28: ldr             x2, [fp, #0x18]
    // 0x1696a2c: r0 = next()
    //     0x1696a2c: bl              #0x1696a48  ; [package:dio/src/dio_mixin.dart] RequestInterceptorHandler::next
    // 0x1696a30: r0 = Null
    //     0x1696a30: mov             x0, NULL
    // 0x1696a34: LeaveFrame
    //     0x1696a34: mov             SP, fp
    //     0x1696a38: ldp             fp, lr, [SP], #0x10
    // 0x1696a3c: ret
    //     0x1696a3c: ret             
    // 0x1696a40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1696a40: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1696a44: b               #0x1696a24
  }
}

// class id: 5012, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _InterceptorsWrapper&Interceptor&_InterceptorWrapperMixin extends Interceptor
     with _InterceptorWrapperMixin {

  dynamic onError(dynamic) {
    // ** addr: 0x168d3b0, size: 0x24
    // 0x168d3b0: EnterFrame
    //     0x168d3b0: stp             fp, lr, [SP, #-0x10]!
    //     0x168d3b4: mov             fp, SP
    // 0x168d3b8: ldr             x2, [fp, #0x10]
    // 0x168d3bc: r1 = Function 'onError':.
    //     0x168d3bc: add             x1, PP, #0x26, lsl #12  ; [pp+0x262a8] AnonymousClosure: (0x168d3d4), in [package:dio/src/dio_mixin.dart] _InterceptorsWrapper&Interceptor&_InterceptorWrapperMixin::onError (0x168d414)
    //     0x168d3c0: ldr             x1, [x1, #0x2a8]
    // 0x168d3c4: r0 = AllocateClosure()
    //     0x168d3c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x168d3c8: LeaveFrame
    //     0x168d3c8: mov             SP, fp
    //     0x168d3cc: ldp             fp, lr, [SP], #0x10
    // 0x168d3d0: ret
    //     0x168d3d0: ret             
  }
  [closure] void onError(dynamic, DioException, ErrorInterceptorHandler) {
    // ** addr: 0x168d3d4, size: 0x40
    // 0x168d3d4: EnterFrame
    //     0x168d3d4: stp             fp, lr, [SP, #-0x10]!
    //     0x168d3d8: mov             fp, SP
    // 0x168d3dc: ldr             x0, [fp, #0x20]
    // 0x168d3e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x168d3e0: ldur            w1, [x0, #0x17]
    // 0x168d3e4: DecompressPointer r1
    //     0x168d3e4: add             x1, x1, HEAP, lsl #32
    // 0x168d3e8: CheckStackOverflow
    //     0x168d3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x168d3ec: cmp             SP, x16
    //     0x168d3f0: b.ls            #0x168d40c
    // 0x168d3f4: ldr             x2, [fp, #0x18]
    // 0x168d3f8: ldr             x3, [fp, #0x10]
    // 0x168d3fc: r0 = onError()
    //     0x168d3fc: bl              #0x168d414  ; [package:dio/src/dio_mixin.dart] _InterceptorsWrapper&Interceptor&_InterceptorWrapperMixin::onError
    // 0x168d400: LeaveFrame
    //     0x168d400: mov             SP, fp
    //     0x168d404: ldp             fp, lr, [SP], #0x10
    // 0x168d408: ret
    //     0x168d408: ret             
    // 0x168d40c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x168d40c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x168d410: b               #0x168d3f4
  }
  _ onError(/* No info */) {
    // ** addr: 0x168d414, size: 0x38
    // 0x168d414: EnterFrame
    //     0x168d414: stp             fp, lr, [SP, #-0x10]!
    //     0x168d418: mov             fp, SP
    // 0x168d41c: mov             x0, x1
    // 0x168d420: mov             x1, x3
    // 0x168d424: CheckStackOverflow
    //     0x168d424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x168d428: cmp             SP, x16
    //     0x168d42c: b.ls            #0x168d444
    // 0x168d430: r0 = next()
    //     0x168d430: bl              #0x168b580  ; [package:dio/src/dio_mixin.dart] ErrorInterceptorHandler::next
    // 0x168d434: r0 = Null
    //     0x168d434: mov             x0, NULL
    // 0x168d438: LeaveFrame
    //     0x168d438: mov             SP, fp
    //     0x168d43c: ldp             fp, lr, [SP], #0x10
    // 0x168d440: ret
    //     0x168d440: ret             
    // 0x168d444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x168d444: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x168d448: b               #0x168d430
  }
  dynamic onResponse(dynamic) {
    // ** addr: 0x16920b4, size: 0x24
    // 0x16920b4: EnterFrame
    //     0x16920b4: stp             fp, lr, [SP, #-0x10]!
    //     0x16920b8: mov             fp, SP
    // 0x16920bc: ldr             x2, [fp, #0x10]
    // 0x16920c0: r1 = Function 'onResponse':.
    //     0x16920c0: add             x1, PP, #0x26, lsl #12  ; [pp+0x262b0] AnonymousClosure: (0x16920d8), in [package:dio/src/dio_mixin.dart] _InterceptorsWrapper&Interceptor&_InterceptorWrapperMixin::onResponse (0x1692118)
    //     0x16920c4: ldr             x1, [x1, #0x2b0]
    // 0x16920c8: r0 = AllocateClosure()
    //     0x16920c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x16920cc: LeaveFrame
    //     0x16920cc: mov             SP, fp
    //     0x16920d0: ldp             fp, lr, [SP], #0x10
    // 0x16920d4: ret
    //     0x16920d4: ret             
  }
  [closure] void onResponse(dynamic, Response<dynamic>, ResponseInterceptorHandler) {
    // ** addr: 0x16920d8, size: 0x40
    // 0x16920d8: EnterFrame
    //     0x16920d8: stp             fp, lr, [SP, #-0x10]!
    //     0x16920dc: mov             fp, SP
    // 0x16920e0: ldr             x0, [fp, #0x20]
    // 0x16920e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x16920e4: ldur            w1, [x0, #0x17]
    // 0x16920e8: DecompressPointer r1
    //     0x16920e8: add             x1, x1, HEAP, lsl #32
    // 0x16920ec: CheckStackOverflow
    //     0x16920ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x16920f0: cmp             SP, x16
    //     0x16920f4: b.ls            #0x1692110
    // 0x16920f8: ldr             x2, [fp, #0x18]
    // 0x16920fc: ldr             x3, [fp, #0x10]
    // 0x1692100: r0 = onResponse()
    //     0x1692100: bl              #0x1692118  ; [package:dio/src/dio_mixin.dart] _InterceptorsWrapper&Interceptor&_InterceptorWrapperMixin::onResponse
    // 0x1692104: LeaveFrame
    //     0x1692104: mov             SP, fp
    //     0x1692108: ldp             fp, lr, [SP], #0x10
    // 0x169210c: ret
    //     0x169210c: ret             
    // 0x1692110: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1692110: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1692114: b               #0x16920f8
  }
  _ onResponse(/* No info */) {
    // ** addr: 0x1692118, size: 0x38
    // 0x1692118: EnterFrame
    //     0x1692118: stp             fp, lr, [SP, #-0x10]!
    //     0x169211c: mov             fp, SP
    // 0x1692120: mov             x0, x1
    // 0x1692124: mov             x1, x3
    // 0x1692128: CheckStackOverflow
    //     0x1692128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x169212c: cmp             SP, x16
    //     0x1692130: b.ls            #0x1692148
    // 0x1692134: r0 = next()
    //     0x1692134: bl              #0x1691f14  ; [package:dio/src/dio_mixin.dart] ResponseInterceptorHandler::next
    // 0x1692138: r0 = Null
    //     0x1692138: mov             x0, NULL
    // 0x169213c: LeaveFrame
    //     0x169213c: mov             SP, fp
    //     0x1692140: ldp             fp, lr, [SP], #0x10
    // 0x1692144: ret
    //     0x1692144: ret             
    // 0x1692148: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1692148: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x169214c: b               #0x1692134
  }
  _ onRequest(/* No info */) {
    // ** addr: 0x1697800, size: 0x38
    // 0x1697800: EnterFrame
    //     0x1697800: stp             fp, lr, [SP, #-0x10]!
    //     0x1697804: mov             fp, SP
    // 0x1697808: mov             x0, x1
    // 0x169780c: mov             x1, x3
    // 0x1697810: CheckStackOverflow
    //     0x1697810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1697814: cmp             SP, x16
    //     0x1697818: b.ls            #0x1697830
    // 0x169781c: r0 = next()
    //     0x169781c: bl              #0x1696a48  ; [package:dio/src/dio_mixin.dart] RequestInterceptorHandler::next
    // 0x1697820: r0 = Null
    //     0x1697820: mov             x0, NULL
    // 0x1697824: LeaveFrame
    //     0x1697824: mov             SP, fp
    //     0x1697828: ldp             fp, lr, [SP], #0x10
    // 0x169782c: ret
    //     0x169782c: ret             
    // 0x1697830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1697830: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1697834: b               #0x169781c
  }
  [closure] void onRequest(dynamic, RequestOptions, RequestInterceptorHandler) {
    // ** addr: 0x1697838, size: 0x40
    // 0x1697838: EnterFrame
    //     0x1697838: stp             fp, lr, [SP, #-0x10]!
    //     0x169783c: mov             fp, SP
    // 0x1697840: ldr             x0, [fp, #0x20]
    // 0x1697844: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1697844: ldur            w1, [x0, #0x17]
    // 0x1697848: DecompressPointer r1
    //     0x1697848: add             x1, x1, HEAP, lsl #32
    // 0x169784c: CheckStackOverflow
    //     0x169784c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1697850: cmp             SP, x16
    //     0x1697854: b.ls            #0x1697870
    // 0x1697858: ldr             x2, [fp, #0x18]
    // 0x169785c: ldr             x3, [fp, #0x10]
    // 0x1697860: r0 = onRequest()
    //     0x1697860: bl              #0x1697800  ; [package:dio/src/dio_mixin.dart] _InterceptorsWrapper&Interceptor&_InterceptorWrapperMixin::onRequest
    // 0x1697864: LeaveFrame
    //     0x1697864: mov             SP, fp
    //     0x1697868: ldp             fp, lr, [SP], #0x10
    // 0x169786c: ret
    //     0x169786c: ret             
    // 0x1697870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1697870: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1697874: b               #0x1697858
  }
}

// class id: 5013, size: 0x14, field offset: 0x8
abstract class InterceptorsWrapper extends _InterceptorsWrapper&Interceptor&_InterceptorWrapperMixin {
}

// class id: 5015, size: 0x8, field offset: 0x8
abstract class _InterceptorWrapperMixin extends Interceptor {
}

// class id: 6641, size: 0x10, field offset: 0xc
class Interceptors extends ListBase<dynamic> {

  _ []=(/* No info */) {
    // ** addr: 0x64f44c, size: 0x1a0
    // 0x64f44c: EnterFrame
    //     0x64f44c: stp             fp, lr, [SP, #-0x10]!
    //     0x64f450: mov             fp, SP
    // 0x64f454: AllocStack(0x18)
    //     0x64f454: sub             SP, SP, #0x18
    // 0x64f458: CheckStackOverflow
    //     0x64f458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64f45c: cmp             SP, x16
    //     0x64f460: b.ls            #0x64f5e0
    // 0x64f464: ldr             x0, [fp, #0x10]
    // 0x64f468: r2 = Null
    //     0x64f468: mov             x2, NULL
    // 0x64f46c: r1 = Null
    //     0x64f46c: mov             x1, NULL
    // 0x64f470: r4 = 60
    //     0x64f470: movz            x4, #0x3c
    // 0x64f474: branchIfSmi(r0, 0x64f480)
    //     0x64f474: tbz             w0, #0, #0x64f480
    // 0x64f478: r4 = LoadClassIdInstr(r0)
    //     0x64f478: ldur            x4, [x0, #-1]
    //     0x64f47c: ubfx            x4, x4, #0xc, #0x14
    // 0x64f480: r17 = -5011
    //     0x64f480: movn            x17, #0x1392
    // 0x64f484: add             x4, x4, x17
    // 0x64f488: cmp             x4, #5
    // 0x64f48c: b.ls            #0x64f4a4
    // 0x64f490: r8 = Interceptor
    //     0x64f490: add             x8, PP, #0x26, lsl #12  ; [pp+0x262d8] Type: Interceptor
    //     0x64f494: ldr             x8, [x8, #0x2d8]
    // 0x64f498: r3 = Null
    //     0x64f498: add             x3, PP, #0x26, lsl #12  ; [pp+0x262e0] Null
    //     0x64f49c: ldr             x3, [x3, #0x2e0]
    // 0x64f4a0: r0 = Interceptor()
    //     0x64f4a0: bl              #0x64f6a0  ; IsType_Interceptor_Stub
    // 0x64f4a4: ldr             x0, [fp, #0x20]
    // 0x64f4a8: LoadField: r2 = r0->field_b
    //     0x64f4a8: ldur            w2, [x0, #0xb]
    // 0x64f4ac: DecompressPointer r2
    //     0x64f4ac: add             x2, x2, HEAP, lsl #32
    // 0x64f4b0: stur            x2, [fp, #-0x10]
    // 0x64f4b4: LoadField: r0 = r2->field_b
    //     0x64f4b4: ldur            w0, [x2, #0xb]
    // 0x64f4b8: ldr             x1, [fp, #0x18]
    // 0x64f4bc: r3 = LoadInt32Instr(r1)
    //     0x64f4bc: sbfx            x3, x1, #1, #0x1f
    //     0x64f4c0: tbz             w1, #0, #0x64f4c8
    //     0x64f4c4: ldur            x3, [x1, #7]
    // 0x64f4c8: stur            x3, [fp, #-0x18]
    // 0x64f4cc: r4 = LoadInt32Instr(r0)
    //     0x64f4cc: sbfx            x4, x0, #1, #0x1f
    // 0x64f4d0: stur            x4, [fp, #-8]
    // 0x64f4d4: cmp             x4, x3
    // 0x64f4d8: b.ne            #0x64f548
    // 0x64f4dc: LoadField: r0 = r2->field_f
    //     0x64f4dc: ldur            w0, [x2, #0xf]
    // 0x64f4e0: DecompressPointer r0
    //     0x64f4e0: add             x0, x0, HEAP, lsl #32
    // 0x64f4e4: LoadField: r1 = r0->field_b
    //     0x64f4e4: ldur            w1, [x0, #0xb]
    // 0x64f4e8: r0 = LoadInt32Instr(r1)
    //     0x64f4e8: sbfx            x0, x1, #1, #0x1f
    // 0x64f4ec: cmp             x4, x0
    // 0x64f4f0: b.ne            #0x64f4fc
    // 0x64f4f4: mov             x1, x2
    // 0x64f4f8: r0 = _growToNextCapacity()
    //     0x64f4f8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x64f4fc: ldur            x4, [fp, #-0x10]
    // 0x64f500: ldur            x5, [fp, #-8]
    // 0x64f504: add             x0, x5, #1
    // 0x64f508: lsl             x1, x0, #1
    // 0x64f50c: StoreField: r4->field_b = r1
    //     0x64f50c: stur            w1, [x4, #0xb]
    // 0x64f510: LoadField: r1 = r4->field_f
    //     0x64f510: ldur            w1, [x4, #0xf]
    // 0x64f514: DecompressPointer r1
    //     0x64f514: add             x1, x1, HEAP, lsl #32
    // 0x64f518: ldr             x0, [fp, #0x10]
    // 0x64f51c: ArrayStore: r1[r5] = r0  ; List_4
    //     0x64f51c: add             x25, x1, x5, lsl #2
    //     0x64f520: add             x25, x25, #0xf
    //     0x64f524: str             w0, [x25]
    //     0x64f528: tbz             w0, #0, #0x64f544
    //     0x64f52c: ldurb           w16, [x1, #-1]
    //     0x64f530: ldurb           w17, [x0, #-1]
    //     0x64f534: and             x16, x17, x16, lsr #2
    //     0x64f538: tst             x16, HEAP, lsr #32
    //     0x64f53c: b.eq            #0x64f544
    //     0x64f540: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x64f544: b               #0x64f5d0
    // 0x64f548: mov             x5, x4
    // 0x64f54c: mov             x4, x2
    // 0x64f550: LoadField: r2 = r4->field_7
    //     0x64f550: ldur            w2, [x4, #7]
    // 0x64f554: DecompressPointer r2
    //     0x64f554: add             x2, x2, HEAP, lsl #32
    // 0x64f558: ldr             x0, [fp, #0x10]
    // 0x64f55c: r1 = Null
    //     0x64f55c: mov             x1, NULL
    // 0x64f560: cmp             w2, NULL
    // 0x64f564: b.eq            #0x64f584
    // 0x64f568: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x64f568: ldur            w4, [x2, #0x17]
    // 0x64f56c: DecompressPointer r4
    //     0x64f56c: add             x4, x4, HEAP, lsl #32
    // 0x64f570: r8 = X0
    //     0x64f570: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x64f574: LoadField: r9 = r4->field_7
    //     0x64f574: ldur            x9, [x4, #7]
    // 0x64f578: r3 = Null
    //     0x64f578: add             x3, PP, #0x26, lsl #12  ; [pp+0x262f0] Null
    //     0x64f57c: ldr             x3, [x3, #0x2f0]
    // 0x64f580: blr             x9
    // 0x64f584: ldur            x0, [fp, #-8]
    // 0x64f588: ldur            x1, [fp, #-0x18]
    // 0x64f58c: cmp             x1, x0
    // 0x64f590: b.hs            #0x64f5e8
    // 0x64f594: ldur            x2, [fp, #-0x10]
    // 0x64f598: LoadField: r1 = r2->field_f
    //     0x64f598: ldur            w1, [x2, #0xf]
    // 0x64f59c: DecompressPointer r1
    //     0x64f59c: add             x1, x1, HEAP, lsl #32
    // 0x64f5a0: ldr             x0, [fp, #0x10]
    // 0x64f5a4: ldur            x2, [fp, #-0x18]
    // 0x64f5a8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x64f5a8: add             x25, x1, x2, lsl #2
    //     0x64f5ac: add             x25, x25, #0xf
    //     0x64f5b0: str             w0, [x25]
    //     0x64f5b4: tbz             w0, #0, #0x64f5d0
    //     0x64f5b8: ldurb           w16, [x1, #-1]
    //     0x64f5bc: ldurb           w17, [x0, #-1]
    //     0x64f5c0: and             x16, x17, x16, lsr #2
    //     0x64f5c4: tst             x16, HEAP, lsr #32
    //     0x64f5c8: b.eq            #0x64f5d0
    //     0x64f5cc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x64f5d0: r0 = Null
    //     0x64f5d0: mov             x0, NULL
    // 0x64f5d4: LeaveFrame
    //     0x64f5d4: mov             SP, fp
    //     0x64f5d8: ldp             fp, lr, [SP], #0x10
    // 0x64f5dc: ret
    //     0x64f5dc: ret             
    // 0x64f5e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64f5e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64f5e4: b               #0x64f464
    // 0x64f5e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64f5e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  Interceptor [](Interceptors, int) {
    // ** addr: 0x64f604, size: 0xb4
    // 0x64f604: EnterFrame
    //     0x64f604: stp             fp, lr, [SP, #-0x10]!
    //     0x64f608: mov             fp, SP
    // 0x64f60c: ldr             x0, [fp, #0x10]
    // 0x64f610: r2 = Null
    //     0x64f610: mov             x2, NULL
    // 0x64f614: r1 = Null
    //     0x64f614: mov             x1, NULL
    // 0x64f618: branchIfSmi(r0, 0x64f640)
    //     0x64f618: tbz             w0, #0, #0x64f640
    // 0x64f61c: r4 = LoadClassIdInstr(r0)
    //     0x64f61c: ldur            x4, [x0, #-1]
    //     0x64f620: ubfx            x4, x4, #0xc, #0x14
    // 0x64f624: sub             x4, x4, #0x3c
    // 0x64f628: cmp             x4, #1
    // 0x64f62c: b.ls            #0x64f640
    // 0x64f630: r8 = int
    //     0x64f630: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x64f634: r3 = Null
    //     0x64f634: add             x3, PP, #0x26, lsl #12  ; [pp+0x26300] Null
    //     0x64f638: ldr             x3, [x3, #0x300]
    // 0x64f63c: r0 = int()
    //     0x64f63c: bl              #0x16fc548  ; IsType_int_Stub
    // 0x64f640: ldr             x2, [fp, #0x18]
    // 0x64f644: LoadField: r3 = r2->field_b
    //     0x64f644: ldur            w3, [x2, #0xb]
    // 0x64f648: DecompressPointer r3
    //     0x64f648: add             x3, x3, HEAP, lsl #32
    // 0x64f64c: LoadField: r2 = r3->field_b
    //     0x64f64c: ldur            w2, [x3, #0xb]
    // 0x64f650: ldr             x4, [fp, #0x10]
    // 0x64f654: r5 = LoadInt32Instr(r4)
    //     0x64f654: sbfx            x5, x4, #1, #0x1f
    //     0x64f658: tbz             w4, #0, #0x64f660
    //     0x64f65c: ldur            x5, [x4, #7]
    // 0x64f660: r0 = LoadInt32Instr(r2)
    //     0x64f660: sbfx            x0, x2, #1, #0x1f
    // 0x64f664: mov             x1, x5
    // 0x64f668: cmp             x1, x0
    // 0x64f66c: b.hs            #0x64f698
    // 0x64f670: LoadField: r1 = r3->field_f
    //     0x64f670: ldur            w1, [x3, #0xf]
    // 0x64f674: DecompressPointer r1
    //     0x64f674: add             x1, x1, HEAP, lsl #32
    // 0x64f678: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0x64f678: add             x16, x1, x5, lsl #2
    //     0x64f67c: ldur            w0, [x16, #0xf]
    // 0x64f680: DecompressPointer r0
    //     0x64f680: add             x0, x0, HEAP, lsl #32
    // 0x64f684: cmp             w0, NULL
    // 0x64f688: b.eq            #0x64f69c
    // 0x64f68c: LeaveFrame
    //     0x64f68c: mov             SP, fp
    //     0x64f690: ldp             fp, lr, [SP], #0x10
    // 0x64f694: ret
    //     0x64f694: ret             
    // 0x64f698: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64f698: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x64f69c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x64f69c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ clear(/* No info */) {
    // ** addr: 0x8888cc, size: 0x58
    // 0x8888cc: EnterFrame
    //     0x8888cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8888d0: mov             fp, SP
    // 0x8888d4: AllocStack(0x8)
    //     0x8888d4: sub             SP, SP, #8
    // 0x8888d8: CheckStackOverflow
    //     0x8888d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8888dc: cmp             SP, x16
    //     0x8888e0: b.ls            #0x88891c
    // 0x8888e4: LoadField: r0 = r1->field_b
    //     0x8888e4: ldur            w0, [x1, #0xb]
    // 0x8888e8: DecompressPointer r0
    //     0x8888e8: add             x0, x0, HEAP, lsl #32
    // 0x8888ec: stur            x0, [fp, #-8]
    // 0x8888f0: r1 = Function '<anonymous closure>':.
    //     0x8888f0: add             x1, PP, #0xa, lsl #12  ; [pp+0xa8a0] AnonymousClosure: (0x888924), in [package:dio/src/dio_mixin.dart] Interceptors::clear (0x8888cc)
    //     0x8888f4: ldr             x1, [x1, #0x8a0]
    // 0x8888f8: r2 = Null
    //     0x8888f8: mov             x2, NULL
    // 0x8888fc: r0 = AllocateClosure()
    //     0x8888fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x888900: ldur            x1, [fp, #-8]
    // 0x888904: mov             x2, x0
    // 0x888908: r0 = removeWhere()
    //     0x888908: bl              #0x659b54  ; [dart:collection] ListBase::removeWhere
    // 0x88890c: r0 = Null
    //     0x88890c: mov             x0, NULL
    // 0x888910: LeaveFrame
    //     0x888910: mov             SP, fp
    //     0x888914: ldp             fp, lr, [SP], #0x10
    // 0x888918: ret
    //     0x888918: ret             
    // 0x88891c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x88891c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x888920: b               #0x8888e4
  }
  [closure] bool <anonymous closure>(dynamic, Interceptor?) {
    // ** addr: 0x888924, size: 0x24
    // 0x888924: ldr             x1, [SP]
    // 0x888928: r2 = LoadClassIdInstr(r1)
    //     0x888928: ldur            x2, [x1, #-1]
    //     0x88892c: ubfx            x2, x2, #0xc, #0x14
    // 0x888930: r17 = 5011
    //     0x888930: movz            x17, #0x1393
    // 0x888934: cmp             x2, x17
    // 0x888938: r16 = true
    //     0x888938: add             x16, NULL, #0x20  ; true
    // 0x88893c: r17 = false
    //     0x88893c: add             x17, NULL, #0x30  ; false
    // 0x888940: csel            x0, x16, x17, ne
    // 0x888944: ret
    //     0x888944: ret             
  }
  Interceptor [](Interceptors, int) {
    // ** addr: 0x1650720, size: 0x68
    // 0x1650720: EnterFrame
    //     0x1650720: stp             fp, lr, [SP, #-0x10]!
    //     0x1650724: mov             fp, SP
    // 0x1650728: ldr             x2, [fp, #0x18]
    // 0x165072c: LoadField: r3 = r2->field_b
    //     0x165072c: ldur            w3, [x2, #0xb]
    // 0x1650730: DecompressPointer r3
    //     0x1650730: add             x3, x3, HEAP, lsl #32
    // 0x1650734: LoadField: r2 = r3->field_b
    //     0x1650734: ldur            w2, [x3, #0xb]
    // 0x1650738: ldr             x4, [fp, #0x10]
    // 0x165073c: r5 = LoadInt32Instr(r4)
    //     0x165073c: sbfx            x5, x4, #1, #0x1f
    //     0x1650740: tbz             w4, #0, #0x1650748
    //     0x1650744: ldur            x5, [x4, #7]
    // 0x1650748: r0 = LoadInt32Instr(r2)
    //     0x1650748: sbfx            x0, x2, #1, #0x1f
    // 0x165074c: mov             x1, x5
    // 0x1650750: cmp             x1, x0
    // 0x1650754: b.hs            #0x1650780
    // 0x1650758: LoadField: r1 = r3->field_f
    //     0x1650758: ldur            w1, [x3, #0xf]
    // 0x165075c: DecompressPointer r1
    //     0x165075c: add             x1, x1, HEAP, lsl #32
    // 0x1650760: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0x1650760: add             x16, x1, x5, lsl #2
    //     0x1650764: ldur            w0, [x16, #0xf]
    // 0x1650768: DecompressPointer r0
    //     0x1650768: add             x0, x0, HEAP, lsl #32
    // 0x165076c: cmp             w0, NULL
    // 0x1650770: b.eq            #0x1650784
    // 0x1650774: LeaveFrame
    //     0x1650774: mov             SP, fp
    //     0x1650778: ldp             fp, lr, [SP], #0x10
    // 0x165077c: ret
    //     0x165077c: ret             
    // 0x1650780: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x1650780: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x1650784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x1650784: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 7079, size: 0x14, field offset: 0x14
enum InterceptorResultType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0x15857b8, size: 0x64
    // 0x15857b8: EnterFrame
    //     0x15857b8: stp             fp, lr, [SP, #-0x10]!
    //     0x15857bc: mov             fp, SP
    // 0x15857c0: AllocStack(0x10)
    //     0x15857c0: sub             SP, SP, #0x10
    // 0x15857c4: SetupParameters(InterceptorResultType this /* r1 => r0, fp-0x8 */)
    //     0x15857c4: mov             x0, x1
    //     0x15857c8: stur            x1, [fp, #-8]
    // 0x15857cc: CheckStackOverflow
    //     0x15857cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15857d0: cmp             SP, x16
    //     0x15857d4: b.ls            #0x1585814
    // 0x15857d8: r1 = Null
    //     0x15857d8: mov             x1, NULL
    // 0x15857dc: r2 = 4
    //     0x15857dc: movz            x2, #0x4
    // 0x15857e0: r0 = AllocateArray()
    //     0x15857e0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15857e4: r16 = "InterceptorResultType."
    //     0x15857e4: add             x16, PP, #0x2a, lsl #12  ; [pp+0x2afb0] "InterceptorResultType."
    //     0x15857e8: ldr             x16, [x16, #0xfb0]
    // 0x15857ec: StoreField: r0->field_f = r16
    //     0x15857ec: stur            w16, [x0, #0xf]
    // 0x15857f0: ldur            x1, [fp, #-8]
    // 0x15857f4: LoadField: r2 = r1->field_f
    //     0x15857f4: ldur            w2, [x1, #0xf]
    // 0x15857f8: DecompressPointer r2
    //     0x15857f8: add             x2, x2, HEAP, lsl #32
    // 0x15857fc: StoreField: r0->field_13 = r2
    //     0x15857fc: stur            w2, [x0, #0x13]
    // 0x1585800: str             x0, [SP]
    // 0x1585804: r0 = _interpolate()
    //     0x1585804: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1585808: LeaveFrame
    //     0x1585808: mov             SP, fp
    //     0x158580c: ldp             fp, lr, [SP], #0x10
    // 0x1585810: ret
    //     0x1585810: ret             
    // 0x1585814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1585814: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1585818: b               #0x15857d8
  }
}
