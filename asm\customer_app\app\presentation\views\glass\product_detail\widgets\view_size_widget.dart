// lib: , url: package:customer_app/app/presentation/views/glass/product_detail/widgets/view_size_widget.dart

// class id: 1049452, size: 0x8
class :: {
}

// class id: 3300, size: 0x14, field offset: 0x14
class _ViewSizeWidgetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb94814, size: 0x1b0
    // 0xb94814: EnterFrame
    //     0xb94814: stp             fp, lr, [SP, #-0x10]!
    //     0xb94818: mov             fp, SP
    // 0xb9481c: AllocStack(0x30)
    //     0xb9481c: sub             SP, SP, #0x30
    // 0xb94820: SetupParameters(_ViewSizeWidgetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb94820: mov             x0, x1
    //     0xb94824: stur            x1, [fp, #-8]
    //     0xb94828: mov             x1, x2
    //     0xb9482c: stur            x2, [fp, #-0x10]
    // 0xb94830: CheckStackOverflow
    //     0xb94830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94834: cmp             SP, x16
    //     0xb94838: b.ls            #0xb949b8
    // 0xb9483c: r1 = 1
    //     0xb9483c: movz            x1, #0x1
    // 0xb94840: r0 = AllocateContext()
    //     0xb94840: bl              #0x16f6108  ; AllocateContextStub
    // 0xb94844: mov             x2, x0
    // 0xb94848: ldur            x0, [fp, #-8]
    // 0xb9484c: stur            x2, [fp, #-0x18]
    // 0xb94850: StoreField: r2->field_f = r0
    //     0xb94850: stur            w0, [x2, #0xf]
    // 0xb94854: LoadField: r1 = r0->field_b
    //     0xb94854: ldur            w1, [x0, #0xb]
    // 0xb94858: DecompressPointer r1
    //     0xb94858: add             x1, x1, HEAP, lsl #32
    // 0xb9485c: cmp             w1, NULL
    // 0xb94860: b.eq            #0xb949c0
    // 0xb94864: LoadField: r0 = r1->field_b
    //     0xb94864: ldur            w0, [x1, #0xb]
    // 0xb94868: DecompressPointer r0
    //     0xb94868: add             x0, x0, HEAP, lsl #32
    // 0xb9486c: LoadField: r1 = r0->field_7
    //     0xb9486c: ldur            w1, [x0, #7]
    // 0xb94870: cbz             w1, #0xb94938
    // 0xb94874: ldur            x1, [fp, #-0x10]
    // 0xb94878: r0 = of()
    //     0xb94878: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb9487c: LoadField: r1 = r0->field_87
    //     0xb9487c: ldur            w1, [x0, #0x87]
    // 0xb94880: DecompressPointer r1
    //     0xb94880: add             x1, x1, HEAP, lsl #32
    // 0xb94884: LoadField: r0 = r1->field_2b
    //     0xb94884: ldur            w0, [x1, #0x2b]
    // 0xb94888: DecompressPointer r0
    //     0xb94888: add             x0, x0, HEAP, lsl #32
    // 0xb9488c: stur            x0, [fp, #-8]
    // 0xb94890: r1 = Instance_Color
    //     0xb94890: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb94894: d0 = 0.700000
    //     0xb94894: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb94898: ldr             d0, [x17, #0xf48]
    // 0xb9489c: r0 = withOpacity()
    //     0xb9489c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb948a0: r16 = 12.000000
    //     0xb948a0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb948a4: ldr             x16, [x16, #0x9e8]
    // 0xb948a8: stp             x16, x0, [SP, #8]
    // 0xb948ac: r16 = Instance_TextDecoration
    //     0xb948ac: add             x16, PP, #0x36, lsl #12  ; [pp+0x36010] Obj!TextDecoration@d68c61
    //     0xb948b0: ldr             x16, [x16, #0x10]
    // 0xb948b4: str             x16, [SP]
    // 0xb948b8: ldur            x1, [fp, #-8]
    // 0xb948bc: r4 = const [0, 0x4, 0x3, 0x1, color, 0x1, decoration, 0x3, fontSize, 0x2, null]
    //     0xb948bc: add             x4, PP, #0x40, lsl #12  ; [pp+0x407c8] List(11) [0, 0x4, 0x3, 0x1, "color", 0x1, "decoration", 0x3, "fontSize", 0x2, Null]
    //     0xb948c0: ldr             x4, [x4, #0x7c8]
    // 0xb948c4: r0 = copyWith()
    //     0xb948c4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb948c8: stur            x0, [fp, #-8]
    // 0xb948cc: r0 = Text()
    //     0xb948cc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb948d0: mov             x1, x0
    // 0xb948d4: r0 = "View Size Chart"
    //     0xb948d4: add             x0, PP, #0x40, lsl #12  ; [pp+0x407d0] "View Size Chart"
    //     0xb948d8: ldr             x0, [x0, #0x7d0]
    // 0xb948dc: stur            x1, [fp, #-0x10]
    // 0xb948e0: StoreField: r1->field_b = r0
    //     0xb948e0: stur            w0, [x1, #0xb]
    // 0xb948e4: ldur            x0, [fp, #-8]
    // 0xb948e8: StoreField: r1->field_13 = r0
    //     0xb948e8: stur            w0, [x1, #0x13]
    // 0xb948ec: r0 = Instance_TextAlign
    //     0xb948ec: ldr             x0, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb948f0: StoreField: r1->field_1b = r0
    //     0xb948f0: stur            w0, [x1, #0x1b]
    // 0xb948f4: r0 = Align()
    //     0xb948f4: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xb948f8: mov             x1, x0
    // 0xb948fc: r0 = Instance_Alignment
    //     0xb948fc: add             x0, PP, #0x46, lsl #12  ; [pp+0x46a78] Obj!Alignment@d5a781
    //     0xb94900: ldr             x0, [x0, #0xa78]
    // 0xb94904: stur            x1, [fp, #-8]
    // 0xb94908: StoreField: r1->field_f = r0
    //     0xb94908: stur            w0, [x1, #0xf]
    // 0xb9490c: ldur            x0, [fp, #-0x10]
    // 0xb94910: StoreField: r1->field_b = r0
    //     0xb94910: stur            w0, [x1, #0xb]
    // 0xb94914: r0 = Padding()
    //     0xb94914: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb94918: mov             x1, x0
    // 0xb9491c: r0 = Instance_EdgeInsets
    //     0xb9491c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52088] Obj!EdgeInsets@d58ac1
    //     0xb94920: ldr             x0, [x0, #0x88]
    // 0xb94924: StoreField: r1->field_f = r0
    //     0xb94924: stur            w0, [x1, #0xf]
    // 0xb94928: ldur            x0, [fp, #-8]
    // 0xb9492c: StoreField: r1->field_b = r0
    //     0xb9492c: stur            w0, [x1, #0xb]
    // 0xb94930: mov             x0, x1
    // 0xb94934: b               #0xb94950
    // 0xb94938: r0 = Container()
    //     0xb94938: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb9493c: mov             x1, x0
    // 0xb94940: stur            x0, [fp, #-8]
    // 0xb94944: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb94944: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb94948: r0 = Container()
    //     0xb94948: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb9494c: ldur            x0, [fp, #-8]
    // 0xb94950: stur            x0, [fp, #-8]
    // 0xb94954: r0 = InkWell()
    //     0xb94954: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb94958: mov             x3, x0
    // 0xb9495c: ldur            x0, [fp, #-8]
    // 0xb94960: stur            x3, [fp, #-0x10]
    // 0xb94964: StoreField: r3->field_b = r0
    //     0xb94964: stur            w0, [x3, #0xb]
    // 0xb94968: ldur            x2, [fp, #-0x18]
    // 0xb9496c: r1 = Function '<anonymous closure>':.
    //     0xb9496c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55060] AnonymousClosure: (0xb949e4), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/view_size_widget.dart] _ViewSizeWidgetState::build (0xb94814)
    //     0xb94970: ldr             x1, [x1, #0x60]
    // 0xb94974: r0 = AllocateClosure()
    //     0xb94974: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94978: mov             x1, x0
    // 0xb9497c: ldur            x0, [fp, #-0x10]
    // 0xb94980: StoreField: r0->field_f = r1
    //     0xb94980: stur            w1, [x0, #0xf]
    // 0xb94984: r1 = true
    //     0xb94984: add             x1, NULL, #0x20  ; true
    // 0xb94988: StoreField: r0->field_43 = r1
    //     0xb94988: stur            w1, [x0, #0x43]
    // 0xb9498c: r2 = Instance_BoxShape
    //     0xb9498c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb94990: ldr             x2, [x2, #0x80]
    // 0xb94994: StoreField: r0->field_47 = r2
    //     0xb94994: stur            w2, [x0, #0x47]
    // 0xb94998: StoreField: r0->field_6f = r1
    //     0xb94998: stur            w1, [x0, #0x6f]
    // 0xb9499c: r2 = false
    //     0xb9499c: add             x2, NULL, #0x30  ; false
    // 0xb949a0: StoreField: r0->field_73 = r2
    //     0xb949a0: stur            w2, [x0, #0x73]
    // 0xb949a4: StoreField: r0->field_83 = r1
    //     0xb949a4: stur            w1, [x0, #0x83]
    // 0xb949a8: StoreField: r0->field_7b = r2
    //     0xb949a8: stur            w2, [x0, #0x7b]
    // 0xb949ac: LeaveFrame
    //     0xb949ac: mov             SP, fp
    //     0xb949b0: ldp             fp, lr, [SP], #0x10
    // 0xb949b4: ret
    //     0xb949b4: ret             
    // 0xb949b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb949b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb949bc: b               #0xb9483c
    // 0xb949c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb949c0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb949e4, size: 0x78
    // 0xb949e4: EnterFrame
    //     0xb949e4: stp             fp, lr, [SP, #-0x10]!
    //     0xb949e8: mov             fp, SP
    // 0xb949ec: AllocStack(0x18)
    //     0xb949ec: sub             SP, SP, #0x18
    // 0xb949f0: SetupParameters()
    //     0xb949f0: ldr             x0, [fp, #0x10]
    //     0xb949f4: ldur            w2, [x0, #0x17]
    //     0xb949f8: add             x2, x2, HEAP, lsl #32
    //     0xb949fc: stur            x2, [fp, #-8]
    // 0xb94a00: CheckStackOverflow
    //     0xb94a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb94a04: cmp             SP, x16
    //     0xb94a08: b.ls            #0xb94a54
    // 0xb94a0c: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xb94a0c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb94a10: ldr             x0, [x0, #0x1c80]
    //     0xb94a14: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb94a18: cmp             w0, w16
    //     0xb94a1c: b.ne            #0xb94a28
    //     0xb94a20: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xb94a24: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb94a28: ldur            x2, [fp, #-8]
    // 0xb94a2c: r1 = Function '<anonymous closure>':.
    //     0xb94a2c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55068] AnonymousClosure: (0xb94a5c), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/view_size_widget.dart] _ViewSizeWidgetState::build (0xb94814)
    //     0xb94a30: ldr             x1, [x1, #0x68]
    // 0xb94a34: r0 = AllocateClosure()
    //     0xb94a34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb94a38: stp             x0, NULL, [SP]
    // 0xb94a3c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb94a3c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb94a40: r0 = GetNavigation.to()
    //     0xb94a40: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0xb94a44: r0 = Null
    //     0xb94a44: mov             x0, NULL
    // 0xb94a48: LeaveFrame
    //     0xb94a48: mov             SP, fp
    //     0xb94a4c: ldp             fp, lr, [SP], #0x10
    // 0xb94a50: ret
    //     0xb94a50: ret             
    // 0xb94a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb94a54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb94a58: b               #0xb94a0c
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0xb94a5c, size: 0x58
    // 0xb94a5c: EnterFrame
    //     0xb94a5c: stp             fp, lr, [SP, #-0x10]!
    //     0xb94a60: mov             fp, SP
    // 0xb94a64: AllocStack(0x8)
    //     0xb94a64: sub             SP, SP, #8
    // 0xb94a68: SetupParameters()
    //     0xb94a68: ldr             x0, [fp, #0x10]
    //     0xb94a6c: ldur            w1, [x0, #0x17]
    //     0xb94a70: add             x1, x1, HEAP, lsl #32
    // 0xb94a74: LoadField: r0 = r1->field_f
    //     0xb94a74: ldur            w0, [x1, #0xf]
    // 0xb94a78: DecompressPointer r0
    //     0xb94a78: add             x0, x0, HEAP, lsl #32
    // 0xb94a7c: LoadField: r1 = r0->field_b
    //     0xb94a7c: ldur            w1, [x0, #0xb]
    // 0xb94a80: DecompressPointer r1
    //     0xb94a80: add             x1, x1, HEAP, lsl #32
    // 0xb94a84: cmp             w1, NULL
    // 0xb94a88: b.eq            #0xb94ab0
    // 0xb94a8c: LoadField: r0 = r1->field_b
    //     0xb94a8c: ldur            w0, [x1, #0xb]
    // 0xb94a90: DecompressPointer r0
    //     0xb94a90: add             x0, x0, HEAP, lsl #32
    // 0xb94a94: stur            x0, [fp, #-8]
    // 0xb94a98: r0 = ViewSizeChart()
    //     0xb94a98: bl              #0xa1d360  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0xb94a9c: ldur            x1, [fp, #-8]
    // 0xb94aa0: StoreField: r0->field_b = r1
    //     0xb94aa0: stur            w1, [x0, #0xb]
    // 0xb94aa4: LeaveFrame
    //     0xb94aa4: mov             SP, fp
    //     0xb94aa8: ldp             fp, lr, [SP], #0x10
    // 0xb94aac: ret
    //     0xb94aac: ret             
    // 0xb94ab0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb94ab0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4043, size: 0x10, field offset: 0xc
//   const constructor, 
class ViewSizeWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7fc4c, size: 0x24
    // 0xc7fc4c: EnterFrame
    //     0xc7fc4c: stp             fp, lr, [SP, #-0x10]!
    //     0xc7fc50: mov             fp, SP
    // 0xc7fc54: mov             x0, x1
    // 0xc7fc58: r1 = <ViewSizeWidget>
    //     0xc7fc58: add             x1, PP, #0x48, lsl #12  ; [pp+0x48718] TypeArguments: <ViewSizeWidget>
    //     0xc7fc5c: ldr             x1, [x1, #0x718]
    // 0xc7fc60: r0 = _ViewSizeWidgetState()
    //     0xc7fc60: bl              #0xc7fc70  ; Allocate_ViewSizeWidgetStateStub -> _ViewSizeWidgetState (size=0x14)
    // 0xc7fc64: LeaveFrame
    //     0xc7fc64: mov             SP, fp
    //     0xc7fc68: ldp             fp, lr, [SP], #0x10
    // 0xc7fc6c: ret
    //     0xc7fc6c: ret             
  }
}
