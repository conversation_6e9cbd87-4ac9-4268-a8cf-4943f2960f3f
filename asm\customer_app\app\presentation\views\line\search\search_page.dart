// lib: , url: package:customer_app/app/presentation/views/line/search/search_page.dart

// class id: 1049582, size: 0x8
class :: {
}

// class id: 4517, size: 0x14, field offset: 0x14
//   const constructor, 
class SearchPage extends BaseView<dynamic> {

  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String, String) {
    // ** addr: 0x13b0b1c, size: 0x17c
    // 0x13b0b1c: EnterFrame
    //     0x13b0b1c: stp             fp, lr, [SP, #-0x10]!
    //     0x13b0b20: mov             fp, SP
    // 0x13b0b24: AllocStack(0x28)
    //     0x13b0b24: sub             SP, SP, #0x28
    // 0x13b0b28: SetupParameters()
    //     0x13b0b28: ldr             x0, [fp, #0x50]
    //     0x13b0b2c: ldur            w1, [x0, #0x17]
    //     0x13b0b30: add             x1, x1, HEAP, lsl #32
    // 0x13b0b34: CheckStackOverflow
    //     0x13b0b34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b0b38: cmp             SP, x16
    //     0x13b0b3c: b.ls            #0x13b0c90
    // 0x13b0b40: LoadField: r0 = r1->field_f
    //     0x13b0b40: ldur            w0, [x1, #0xf]
    // 0x13b0b44: DecompressPointer r0
    //     0x13b0b44: add             x0, x0, HEAP, lsl #32
    // 0x13b0b48: mov             x1, x0
    // 0x13b0b4c: r0 = controller()
    //     0x13b0b4c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b0b50: stur            x0, [fp, #-8]
    // 0x13b0b54: r0 = EventData()
    //     0x13b0b54: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x13b0b58: mov             x1, x0
    // 0x13b0b5c: ldr             x0, [fp, #0x38]
    // 0x13b0b60: stur            x1, [fp, #-0x10]
    // 0x13b0b64: StoreField: r1->field_13 = r0
    //     0x13b0b64: stur            w0, [x1, #0x13]
    // 0x13b0b68: ldr             x2, [fp, #0x40]
    // 0x13b0b6c: StoreField: r1->field_53 = r2
    //     0x13b0b6c: stur            w2, [x1, #0x53]
    // 0x13b0b70: ldr             x2, [fp, #0x48]
    // 0x13b0b74: StoreField: r1->field_57 = r2
    //     0x13b0b74: stur            w2, [x1, #0x57]
    // 0x13b0b78: ldr             x2, [fp, #0x20]
    // 0x13b0b7c: StoreField: r1->field_ef = r2
    //     0x13b0b7c: stur            w2, [x1, #0xef]
    // 0x13b0b80: ldr             x2, [fp, #0x30]
    // 0x13b0b84: StoreField: r1->field_f3 = r2
    //     0x13b0b84: stur            w2, [x1, #0xf3]
    // 0x13b0b88: r0 = EventsRequest()
    //     0x13b0b88: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x13b0b8c: mov             x1, x0
    // 0x13b0b90: r0 = "widget_clicked"
    //     0x13b0b90: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edd8] "widget_clicked"
    //     0x13b0b94: ldr             x0, [x0, #0xdd8]
    // 0x13b0b98: StoreField: r1->field_7 = r0
    //     0x13b0b98: stur            w0, [x1, #7]
    // 0x13b0b9c: ldur            x0, [fp, #-0x10]
    // 0x13b0ba0: StoreField: r1->field_b = r0
    //     0x13b0ba0: stur            w0, [x1, #0xb]
    // 0x13b0ba4: mov             x2, x1
    // 0x13b0ba8: ldur            x1, [fp, #-8]
    // 0x13b0bac: r0 = postEvents()
    //     0x13b0bac: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x13b0bb0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13b0bb0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13b0bb4: ldr             x0, [x0, #0x1c80]
    //     0x13b0bb8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13b0bbc: cmp             w0, w16
    //     0x13b0bc0: b.ne            #0x13b0bcc
    //     0x13b0bc4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13b0bc8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13b0bcc: r16 = <ProductDetailController>
    //     0x13b0bcc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ede0] TypeArguments: <ProductDetailController>
    //     0x13b0bd0: ldr             x16, [x16, #0xde0]
    // 0x13b0bd4: str             x16, [SP]
    // 0x13b0bd8: r4 = const [0x1, 0, 0, 0, null]
    //     0x13b0bd8: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x13b0bdc: r0 = Inst.delete()
    //     0x13b0bdc: bl              #0x9c7d48  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.delete
    // 0x13b0be0: r16 = <Type>
    //     0x13b0be0: ldr             x16, [PP, #0x4b40]  ; [pp+0x4b40] TypeArguments: <Type>
    // 0x13b0be4: r30 = ProductDetailController
    //     0x13b0be4: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ede8] Type: ProductDetailController
    //     0x13b0be8: ldr             lr, [lr, #0xde8]
    // 0x13b0bec: stp             lr, x16, [SP]
    // 0x13b0bf0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13b0bf0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13b0bf4: r0 = Inst.put()
    //     0x13b0bf4: bl              #0xa5f404  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.put
    // 0x13b0bf8: r1 = Null
    //     0x13b0bf8: mov             x1, NULL
    // 0x13b0bfc: r2 = 16
    //     0x13b0bfc: movz            x2, #0x10
    // 0x13b0c00: r0 = AllocateArray()
    //     0x13b0c00: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b0c04: r16 = "short_id"
    //     0x13b0c04: add             x16, PP, #0xb, lsl #12  ; [pp+0xb488] "short_id"
    //     0x13b0c08: ldr             x16, [x16, #0x488]
    // 0x13b0c0c: StoreField: r0->field_f = r16
    //     0x13b0c0c: stur            w16, [x0, #0xf]
    // 0x13b0c10: ldr             x1, [fp, #0x18]
    // 0x13b0c14: StoreField: r0->field_13 = r1
    //     0x13b0c14: stur            w1, [x0, #0x13]
    // 0x13b0c18: r16 = "sku_id"
    //     0x13b0c18: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x13b0c1c: ldr             x16, [x16, #0x498]
    // 0x13b0c20: ArrayStore: r0[0] = r16  ; List_4
    //     0x13b0c20: stur            w16, [x0, #0x17]
    // 0x13b0c24: ldr             x1, [fp, #0x10]
    // 0x13b0c28: StoreField: r0->field_1b = r1
    //     0x13b0c28: stur            w1, [x0, #0x1b]
    // 0x13b0c2c: r16 = "previousScreenSource"
    //     0x13b0c2c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x13b0c30: ldr             x16, [x16, #0x448]
    // 0x13b0c34: StoreField: r0->field_1f = r16
    //     0x13b0c34: stur            w16, [x0, #0x1f]
    // 0x13b0c38: ldr             x1, [fp, #0x38]
    // 0x13b0c3c: StoreField: r0->field_23 = r1
    //     0x13b0c3c: stur            w1, [x0, #0x23]
    // 0x13b0c40: r16 = "screenSource"
    //     0x13b0c40: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x13b0c44: ldr             x16, [x16, #0x450]
    // 0x13b0c48: StoreField: r0->field_27 = r16
    //     0x13b0c48: stur            w16, [x0, #0x27]
    // 0x13b0c4c: ldr             x1, [fp, #0x28]
    // 0x13b0c50: StoreField: r0->field_2b = r1
    //     0x13b0c50: stur            w1, [x0, #0x2b]
    // 0x13b0c54: r16 = <String, String>
    //     0x13b0c54: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x13b0c58: ldr             x16, [x16, #0x788]
    // 0x13b0c5c: stp             x0, x16, [SP]
    // 0x13b0c60: r0 = Map._fromLiteral()
    //     0x13b0c60: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13b0c64: r16 = "/product-detail"
    //     0x13b0c64: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a8] "/product-detail"
    //     0x13b0c68: ldr             x16, [x16, #0x4a8]
    // 0x13b0c6c: stp             x16, NULL, [SP, #8]
    // 0x13b0c70: str             x0, [SP]
    // 0x13b0c74: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x13b0c74: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x13b0c78: ldr             x4, [x4, #0x438]
    // 0x13b0c7c: r0 = GetNavigation.toNamed()
    //     0x13b0c7c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x13b0c80: r0 = Null
    //     0x13b0c80: mov             x0, NULL
    // 0x13b0c84: LeaveFrame
    //     0x13b0c84: mov             SP, fp
    //     0x13b0c88: ldp             fp, lr, [SP], #0x10
    // 0x13b0c8c: ret
    //     0x13b0c8c: ret             
    // 0x13b0c90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b0c90: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b0c94: b               #0x13b0b40
  }
  [closure] SearchView <anonymous closure>(dynamic) {
    // ** addr: 0x13b0c98, size: 0x234
    // 0x13b0c98: EnterFrame
    //     0x13b0c98: stp             fp, lr, [SP, #-0x10]!
    //     0x13b0c9c: mov             fp, SP
    // 0x13b0ca0: AllocStack(0xd0)
    //     0x13b0ca0: sub             SP, SP, #0xd0
    // 0x13b0ca4: SetupParameters()
    //     0x13b0ca4: ldr             x0, [fp, #0x10]
    //     0x13b0ca8: ldur            w2, [x0, #0x17]
    //     0x13b0cac: add             x2, x2, HEAP, lsl #32
    //     0x13b0cb0: stur            x2, [fp, #-8]
    // 0x13b0cb4: CheckStackOverflow
    //     0x13b0cb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b0cb8: cmp             SP, x16
    //     0x13b0cbc: b.ls            #0x13b0ec4
    // 0x13b0cc0: LoadField: r1 = r2->field_f
    //     0x13b0cc0: ldur            w1, [x2, #0xf]
    // 0x13b0cc4: DecompressPointer r1
    //     0x13b0cc4: add             x1, x1, HEAP, lsl #32
    // 0x13b0cc8: r0 = controller()
    //     0x13b0cc8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b0ccc: mov             x1, x0
    // 0x13b0cd0: r0 = configResponse()
    //     0x13b0cd0: bl              #0x8b5174  ; [package:customer_app/app/config_controller.dart] ConfigController::configResponse
    // 0x13b0cd4: ldur            x2, [fp, #-8]
    // 0x13b0cd8: stur            x0, [fp, #-0x10]
    // 0x13b0cdc: LoadField: r1 = r2->field_f
    //     0x13b0cdc: ldur            w1, [x2, #0xf]
    // 0x13b0ce0: DecompressPointer r1
    //     0x13b0ce0: add             x1, x1, HEAP, lsl #32
    // 0x13b0ce4: r0 = controller()
    //     0x13b0ce4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b0ce8: LoadField: r1 = r0->field_57
    //     0x13b0ce8: ldur            w1, [x0, #0x57]
    // 0x13b0cec: DecompressPointer r1
    //     0x13b0cec: add             x1, x1, HEAP, lsl #32
    // 0x13b0cf0: r0 = value()
    //     0x13b0cf0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13b0cf4: LoadField: r1 = r0->field_b
    //     0x13b0cf4: ldur            w1, [x0, #0xb]
    // 0x13b0cf8: DecompressPointer r1
    //     0x13b0cf8: add             x1, x1, HEAP, lsl #32
    // 0x13b0cfc: cmp             w1, NULL
    // 0x13b0d00: b.ne            #0x13b0d0c
    // 0x13b0d04: r0 = Null
    //     0x13b0d04: mov             x0, NULL
    // 0x13b0d08: b               #0x13b0d20
    // 0x13b0d0c: LoadField: r0 = r1->field_f
    //     0x13b0d0c: ldur            w0, [x1, #0xf]
    // 0x13b0d10: DecompressPointer r0
    //     0x13b0d10: add             x0, x0, HEAP, lsl #32
    // 0x13b0d14: LoadField: r1 = r0->field_b
    //     0x13b0d14: ldur            w1, [x0, #0xb]
    // 0x13b0d18: DecompressPointer r1
    //     0x13b0d18: add             x1, x1, HEAP, lsl #32
    // 0x13b0d1c: mov             x0, x1
    // 0x13b0d20: cmp             w0, NULL
    // 0x13b0d24: b.ne            #0x13b0d30
    // 0x13b0d28: r3 = ""
    //     0x13b0d28: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b0d2c: b               #0x13b0d34
    // 0x13b0d30: mov             x3, x0
    // 0x13b0d34: ldur            x2, [fp, #-8]
    // 0x13b0d38: stur            x3, [fp, #-0x18]
    // 0x13b0d3c: LoadField: r1 = r2->field_f
    //     0x13b0d3c: ldur            w1, [x2, #0xf]
    // 0x13b0d40: DecompressPointer r1
    //     0x13b0d40: add             x1, x1, HEAP, lsl #32
    // 0x13b0d44: r0 = controller()
    //     0x13b0d44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b0d48: mov             x1, x0
    // 0x13b0d4c: r0 = offerParams()
    //     0x13b0d4c: bl              #0x9c3390  ; [package:customer_app/app/presentation/controllers/checkout_variants/checkout_request_address_controller.dart] CheckoutRequestAddressController::offerParams
    // 0x13b0d50: ldur            x2, [fp, #-8]
    // 0x13b0d54: stur            x0, [fp, #-0x20]
    // 0x13b0d58: LoadField: r1 = r2->field_f
    //     0x13b0d58: ldur            w1, [x2, #0xf]
    // 0x13b0d5c: DecompressPointer r1
    //     0x13b0d5c: add             x1, x1, HEAP, lsl #32
    // 0x13b0d60: r0 = controller()
    //     0x13b0d60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b0d64: mov             x1, x0
    // 0x13b0d68: r0 = widgetsList()
    //     0x13b0d68: bl              #0x13b10e8  ; [package:customer_app/app/presentation/controllers/post_order/order_failure_controller.dart] OrderFailureController::widgetsList
    // 0x13b0d6c: ldur            x2, [fp, #-8]
    // 0x13b0d70: stur            x0, [fp, #-0x28]
    // 0x13b0d74: LoadField: r1 = r2->field_f
    //     0x13b0d74: ldur            w1, [x2, #0xf]
    // 0x13b0d78: DecompressPointer r1
    //     0x13b0d78: add             x1, x1, HEAP, lsl #32
    // 0x13b0d7c: r0 = controller()
    //     0x13b0d7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b0d80: mov             x1, x0
    // 0x13b0d84: r0 = appConfigResponse()
    //     0x13b0d84: bl              #0x933060  ; [package:customer_app/app/config_controller.dart] ConfigController::appConfigResponse
    // 0x13b0d88: ldur            x2, [fp, #-8]
    // 0x13b0d8c: r1 = Function '<anonymous closure>':.
    //     0x13b0d8c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ed88] AnonymousClosure: (0x13b3d00), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0d90: ldr             x1, [x1, #0xd88]
    // 0x13b0d94: stur            x0, [fp, #-0x30]
    // 0x13b0d98: r0 = AllocateClosure()
    //     0x13b0d98: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0d9c: ldur            x2, [fp, #-8]
    // 0x13b0da0: r1 = Function '<anonymous closure>':.
    //     0x13b0da0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ed90] AnonymousClosure: (0x13b3290), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0da4: ldr             x1, [x1, #0xd90]
    // 0x13b0da8: stur            x0, [fp, #-0x38]
    // 0x13b0dac: r0 = AllocateClosure()
    //     0x13b0dac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0db0: ldur            x2, [fp, #-8]
    // 0x13b0db4: r1 = Function '<anonymous closure>':.
    //     0x13b0db4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ed98] AnonymousClosure: (0x13b3208), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0db8: ldr             x1, [x1, #0xd98]
    // 0x13b0dbc: stur            x0, [fp, #-0x40]
    // 0x13b0dc0: r0 = AllocateClosure()
    //     0x13b0dc0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0dc4: ldur            x2, [fp, #-8]
    // 0x13b0dc8: r1 = Function '<anonymous closure>':.
    //     0x13b0dc8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eda0] AnonymousClosure: (0x13b3058), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0dcc: ldr             x1, [x1, #0xda0]
    // 0x13b0dd0: stur            x0, [fp, #-0x48]
    // 0x13b0dd4: r0 = AllocateClosure()
    //     0x13b0dd4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0dd8: ldur            x2, [fp, #-8]
    // 0x13b0ddc: r1 = Function '<anonymous closure>':.
    //     0x13b0ddc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eda8] AnonymousClosure: (0x13b2fc4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0de0: ldr             x1, [x1, #0xda8]
    // 0x13b0de4: stur            x0, [fp, #-0x50]
    // 0x13b0de8: r0 = AllocateClosure()
    //     0x13b0de8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0dec: ldur            x2, [fp, #-8]
    // 0x13b0df0: r1 = Function '<anonymous closure>':.
    //     0x13b0df0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2edb0] AnonymousClosure: (0x13b2cdc), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0df4: ldr             x1, [x1, #0xdb0]
    // 0x13b0df8: stur            x0, [fp, #-0x58]
    // 0x13b0dfc: r0 = AllocateClosure()
    //     0x13b0dfc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0e00: ldur            x2, [fp, #-8]
    // 0x13b0e04: r1 = Function '<anonymous closure>':.
    //     0x13b0e04: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2edb8] AnonymousClosure: (0x13b28a0), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0e08: ldr             x1, [x1, #0xdb8]
    // 0x13b0e0c: stur            x0, [fp, #-0x60]
    // 0x13b0e10: r0 = AllocateClosure()
    //     0x13b0e10: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0e14: ldur            x2, [fp, #-8]
    // 0x13b0e18: r1 = Function '<anonymous closure>':.
    //     0x13b0e18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2edc0] AnonymousClosure: (0x13b276c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0e1c: ldr             x1, [x1, #0xdc0]
    // 0x13b0e20: stur            x0, [fp, #-0x68]
    // 0x13b0e24: r0 = AllocateClosure()
    //     0x13b0e24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0e28: ldur            x2, [fp, #-8]
    // 0x13b0e2c: r1 = Function '<anonymous closure>':.
    //     0x13b0e2c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2edc8] AnonymousClosure: (0x13b0b1c), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0e30: ldr             x1, [x1, #0xdc8]
    // 0x13b0e34: stur            x0, [fp, #-0x70]
    // 0x13b0e38: r0 = AllocateClosure()
    //     0x13b0e38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0e3c: ldur            x2, [fp, #-8]
    // 0x13b0e40: r1 = Function '<anonymous closure>':.
    //     0x13b0e40: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2edd0] AnonymousClosure: (0x13b1124), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x13b0e44: ldr             x1, [x1, #0xdd0]
    // 0x13b0e48: stur            x0, [fp, #-8]
    // 0x13b0e4c: r0 = AllocateClosure()
    //     0x13b0e4c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13b0e50: stur            x0, [fp, #-0x78]
    // 0x13b0e54: r0 = SearchView()
    //     0x13b0e54: bl              #0x13b10dc  ; AllocateSearchViewStub -> SearchView (size=0x48)
    // 0x13b0e58: stur            x0, [fp, #-0x80]
    // 0x13b0e5c: ldur            x16, [fp, #-0x28]
    // 0x13b0e60: ldur            lr, [fp, #-0x78]
    // 0x13b0e64: stp             lr, x16, [SP, #0x40]
    // 0x13b0e68: ldur            x16, [fp, #-0x48]
    // 0x13b0e6c: ldur            lr, [fp, #-0x40]
    // 0x13b0e70: stp             lr, x16, [SP, #0x30]
    // 0x13b0e74: ldur            x16, [fp, #-0x38]
    // 0x13b0e78: ldur            lr, [fp, #-8]
    // 0x13b0e7c: stp             lr, x16, [SP, #0x20]
    // 0x13b0e80: ldur            x16, [fp, #-0x50]
    // 0x13b0e84: ldur            lr, [fp, #-0x68]
    // 0x13b0e88: stp             lr, x16, [SP, #0x10]
    // 0x13b0e8c: ldur            x16, [fp, #-0x30]
    // 0x13b0e90: ldur            lr, [fp, #-0x70]
    // 0x13b0e94: stp             lr, x16, [SP]
    // 0x13b0e98: mov             x1, x0
    // 0x13b0e9c: ldur            x2, [fp, #-0x10]
    // 0x13b0ea0: ldur            x3, [fp, #-0x18]
    // 0x13b0ea4: ldur            x5, [fp, #-0x60]
    // 0x13b0ea8: ldur            x6, [fp, #-0x20]
    // 0x13b0eac: ldur            x7, [fp, #-0x58]
    // 0x13b0eb0: r0 = SearchView()
    //     0x13b0eb0: bl              #0x13b0ecc  ; [package:customer_app/app/presentation/views/line/search/widgets/search_view.dart] SearchView::SearchView
    // 0x13b0eb4: ldur            x0, [fp, #-0x80]
    // 0x13b0eb8: LeaveFrame
    //     0x13b0eb8: mov             SP, fp
    //     0x13b0ebc: ldp             fp, lr, [SP], #0x10
    // 0x13b0ec0: ret
    //     0x13b0ec0: ret             
    // 0x13b0ec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b0ec4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b0ec8: b               #0x13b0cc0
  }
  [closure] Null <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x13b1124, size: 0x90
    // 0x13b1124: EnterFrame
    //     0x13b1124: stp             fp, lr, [SP, #-0x10]!
    //     0x13b1128: mov             fp, SP
    // 0x13b112c: AllocStack(0x10)
    //     0x13b112c: sub             SP, SP, #0x10
    // 0x13b1130: SetupParameters()
    //     0x13b1130: ldr             x0, [fp, #0x20]
    //     0x13b1134: ldur            w2, [x0, #0x17]
    //     0x13b1138: add             x2, x2, HEAP, lsl #32
    //     0x13b113c: stur            x2, [fp, #-8]
    // 0x13b1140: CheckStackOverflow
    //     0x13b1140: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b1144: cmp             SP, x16
    //     0x13b1148: b.ls            #0x13b11ac
    // 0x13b114c: LoadField: r1 = r2->field_f
    //     0x13b114c: ldur            w1, [x2, #0xf]
    // 0x13b1150: DecompressPointer r1
    //     0x13b1150: add             x1, x1, HEAP, lsl #32
    // 0x13b1154: r0 = controller()
    //     0x13b1154: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b1158: mov             x1, x0
    // 0x13b115c: ldr             x2, [fp, #0x18]
    // 0x13b1160: ldr             x5, [fp, #0x10]
    // 0x13b1164: r3 = ""
    //     0x13b1164: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b1168: r0 = getSearch()
    //     0x13b1168: bl              #0x13b1260  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::getSearch
    // 0x13b116c: ldur            x0, [fp, #-8]
    // 0x13b1170: LoadField: r1 = r0->field_f
    //     0x13b1170: ldur            w1, [x0, #0xf]
    // 0x13b1174: DecompressPointer r1
    //     0x13b1174: add             x1, x1, HEAP, lsl #32
    // 0x13b1178: r0 = controller()
    //     0x13b1178: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b117c: ldr             x16, [fp, #0x10]
    // 0x13b1180: str             x16, [SP]
    // 0x13b1184: mov             x1, x0
    // 0x13b1188: r2 = "search_results_fetched"
    //     0x13b1188: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "search_results_fetched"
    //     0x13b118c: ldr             x2, [x2, #0xba0]
    // 0x13b1190: r4 = const [0, 0x3, 0x1, 0x2, query, 0x2, null]
    //     0x13b1190: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eba8] List(7) [0, 0x3, 0x1, 0x2, "query", 0x2, Null]
    //     0x13b1194: ldr             x4, [x4, #0xba8]
    // 0x13b1198: r0 = searchEvents()
    //     0x13b1198: bl              #0x13b11b4  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::searchEvents
    // 0x13b119c: r0 = Null
    //     0x13b119c: mov             x0, NULL
    // 0x13b11a0: LeaveFrame
    //     0x13b11a0: mov             SP, fp
    //     0x13b11a4: ldp             fp, lr, [SP], #0x10
    // 0x13b11a8: ret
    //     0x13b11a8: ret             
    // 0x13b11ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b11ac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b11b0: b               #0x13b114c
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String) {
    // ** addr: 0x13b276c, size: 0x134
    // 0x13b276c: EnterFrame
    //     0x13b276c: stp             fp, lr, [SP, #-0x10]!
    //     0x13b2770: mov             fp, SP
    // 0x13b2774: AllocStack(0x28)
    //     0x13b2774: sub             SP, SP, #0x28
    // 0x13b2778: SetupParameters()
    //     0x13b2778: ldr             x0, [fp, #0x38]
    //     0x13b277c: ldur            w1, [x0, #0x17]
    //     0x13b2780: add             x1, x1, HEAP, lsl #32
    // 0x13b2784: CheckStackOverflow
    //     0x13b2784: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b2788: cmp             SP, x16
    //     0x13b278c: b.ls            #0x13b2898
    // 0x13b2790: LoadField: r0 = r1->field_f
    //     0x13b2790: ldur            w0, [x1, #0xf]
    // 0x13b2794: DecompressPointer r0
    //     0x13b2794: add             x0, x0, HEAP, lsl #32
    // 0x13b2798: mov             x1, x0
    // 0x13b279c: r0 = controller()
    //     0x13b279c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b27a0: stur            x0, [fp, #-8]
    // 0x13b27a4: r0 = EventData()
    //     0x13b27a4: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x13b27a8: mov             x1, x0
    // 0x13b27ac: ldr             x0, [fp, #0x20]
    // 0x13b27b0: stur            x1, [fp, #-0x10]
    // 0x13b27b4: StoreField: r1->field_13 = r0
    //     0x13b27b4: stur            w0, [x1, #0x13]
    // 0x13b27b8: ldr             x2, [fp, #0x28]
    // 0x13b27bc: StoreField: r1->field_53 = r2
    //     0x13b27bc: stur            w2, [x1, #0x53]
    // 0x13b27c0: ldr             x2, [fp, #0x30]
    // 0x13b27c4: StoreField: r1->field_57 = r2
    //     0x13b27c4: stur            w2, [x1, #0x57]
    // 0x13b27c8: r2 = "view_all"
    //     0x13b27c8: add             x2, PP, #0x23, lsl #12  ; [pp+0x23ba0] "view_all"
    //     0x13b27cc: ldr             x2, [x2, #0xba0]
    // 0x13b27d0: StoreField: r1->field_eb = r2
    //     0x13b27d0: stur            w2, [x1, #0xeb]
    // 0x13b27d4: r0 = EventsRequest()
    //     0x13b27d4: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x13b27d8: mov             x1, x0
    // 0x13b27dc: r0 = "cta_clicked"
    //     0x13b27dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2edf8] "cta_clicked"
    //     0x13b27e0: ldr             x0, [x0, #0xdf8]
    // 0x13b27e4: StoreField: r1->field_7 = r0
    //     0x13b27e4: stur            w0, [x1, #7]
    // 0x13b27e8: ldur            x0, [fp, #-0x10]
    // 0x13b27ec: StoreField: r1->field_b = r0
    //     0x13b27ec: stur            w0, [x1, #0xb]
    // 0x13b27f0: mov             x2, x1
    // 0x13b27f4: ldur            x1, [fp, #-8]
    // 0x13b27f8: r0 = postEvents()
    //     0x13b27f8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x13b27fc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13b27fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13b2800: ldr             x0, [x0, #0x1c80]
    //     0x13b2804: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13b2808: cmp             w0, w16
    //     0x13b280c: b.ne            #0x13b2818
    //     0x13b2810: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13b2814: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13b2818: r1 = Null
    //     0x13b2818: mov             x1, NULL
    // 0x13b281c: r2 = 12
    //     0x13b281c: movz            x2, #0xc
    // 0x13b2820: r0 = AllocateArray()
    //     0x13b2820: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13b2824: r16 = "link"
    //     0x13b2824: ldr             x16, [PP, #0x7c28]  ; [pp+0x7c28] "link"
    // 0x13b2828: StoreField: r0->field_f = r16
    //     0x13b2828: stur            w16, [x0, #0xf]
    // 0x13b282c: ldr             x1, [fp, #0x10]
    // 0x13b2830: StoreField: r0->field_13 = r1
    //     0x13b2830: stur            w1, [x0, #0x13]
    // 0x13b2834: r16 = "previousScreenSource"
    //     0x13b2834: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x13b2838: ldr             x16, [x16, #0x448]
    // 0x13b283c: ArrayStore: r0[0] = r16  ; List_4
    //     0x13b283c: stur            w16, [x0, #0x17]
    // 0x13b2840: ldr             x1, [fp, #0x20]
    // 0x13b2844: StoreField: r0->field_1b = r1
    //     0x13b2844: stur            w1, [x0, #0x1b]
    // 0x13b2848: r16 = "screenSource"
    //     0x13b2848: add             x16, PP, #0xb, lsl #12  ; [pp+0xb450] "screenSource"
    //     0x13b284c: ldr             x16, [x16, #0x450]
    // 0x13b2850: StoreField: r0->field_1f = r16
    //     0x13b2850: stur            w16, [x0, #0x1f]
    // 0x13b2854: ldr             x1, [fp, #0x18]
    // 0x13b2858: StoreField: r0->field_23 = r1
    //     0x13b2858: stur            w1, [x0, #0x23]
    // 0x13b285c: r16 = <String, String>
    //     0x13b285c: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x13b2860: ldr             x16, [x16, #0x788]
    // 0x13b2864: stp             x0, x16, [SP]
    // 0x13b2868: r0 = Map._fromLiteral()
    //     0x13b2868: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13b286c: r16 = "/collection"
    //     0x13b286c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb458] "/collection"
    //     0x13b2870: ldr             x16, [x16, #0x458]
    // 0x13b2874: stp             x16, NULL, [SP, #8]
    // 0x13b2878: str             x0, [SP]
    // 0x13b287c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x13b287c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x13b2880: ldr             x4, [x4, #0x438]
    // 0x13b2884: r0 = GetNavigation.toNamed()
    //     0x13b2884: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x13b2888: r0 = Null
    //     0x13b2888: mov             x0, NULL
    // 0x13b288c: LeaveFrame
    //     0x13b288c: mov             SP, fp
    //     0x13b2890: ldp             fp, lr, [SP], #0x10
    // 0x13b2894: ret
    //     0x13b2894: ret             
    // 0x13b2898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b2898: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b289c: b               #0x13b2790
  }
  [closure] Null <anonymous closure>(dynamic, dynamic, dynamic, dynamic, dynamic, dynamic) {
    // ** addr: 0x13b28a0, size: 0x180
    // 0x13b28a0: EnterFrame
    //     0x13b28a0: stp             fp, lr, [SP, #-0x10]!
    //     0x13b28a4: mov             fp, SP
    // 0x13b28a8: AllocStack(0x10)
    //     0x13b28a8: sub             SP, SP, #0x10
    // 0x13b28ac: SetupParameters()
    //     0x13b28ac: ldr             x0, [fp, #0x38]
    //     0x13b28b0: ldur            w1, [x0, #0x17]
    //     0x13b28b4: add             x1, x1, HEAP, lsl #32
    // 0x13b28b8: CheckStackOverflow
    //     0x13b28b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b28bc: cmp             SP, x16
    //     0x13b28c0: b.ls            #0x13b2a18
    // 0x13b28c4: LoadField: r0 = r1->field_f
    //     0x13b28c4: ldur            w0, [x1, #0xf]
    // 0x13b28c8: DecompressPointer r0
    //     0x13b28c8: add             x0, x0, HEAP, lsl #32
    // 0x13b28cc: mov             x1, x0
    // 0x13b28d0: r0 = controller()
    //     0x13b28d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b28d4: mov             x3, x0
    // 0x13b28d8: ldr             x0, [fp, #0x28]
    // 0x13b28dc: r2 = Null
    //     0x13b28dc: mov             x2, NULL
    // 0x13b28e0: r1 = Null
    //     0x13b28e0: mov             x1, NULL
    // 0x13b28e4: stur            x3, [fp, #-8]
    // 0x13b28e8: r4 = 60
    //     0x13b28e8: movz            x4, #0x3c
    // 0x13b28ec: branchIfSmi(r0, 0x13b28f8)
    //     0x13b28ec: tbz             w0, #0, #0x13b28f8
    // 0x13b28f0: r4 = LoadClassIdInstr(r0)
    //     0x13b28f0: ldur            x4, [x0, #-1]
    //     0x13b28f4: ubfx            x4, x4, #0xc, #0x14
    // 0x13b28f8: sub             x4, x4, #0x5e
    // 0x13b28fc: cmp             x4, #1
    // 0x13b2900: b.ls            #0x13b2914
    // 0x13b2904: r8 = String
    //     0x13b2904: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x13b2908: r3 = Null
    //     0x13b2908: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ee00] Null
    //     0x13b290c: ldr             x3, [x3, #0xe00]
    // 0x13b2910: r0 = String()
    //     0x13b2910: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x13b2914: ldr             x0, [fp, #0x20]
    // 0x13b2918: r2 = Null
    //     0x13b2918: mov             x2, NULL
    // 0x13b291c: r1 = Null
    //     0x13b291c: mov             x1, NULL
    // 0x13b2920: r4 = 60
    //     0x13b2920: movz            x4, #0x3c
    // 0x13b2924: branchIfSmi(r0, 0x13b2930)
    //     0x13b2924: tbz             w0, #0, #0x13b2930
    // 0x13b2928: r4 = LoadClassIdInstr(r0)
    //     0x13b2928: ldur            x4, [x0, #-1]
    //     0x13b292c: ubfx            x4, x4, #0xc, #0x14
    // 0x13b2930: sub             x4, x4, #0x5e
    // 0x13b2934: cmp             x4, #1
    // 0x13b2938: b.ls            #0x13b294c
    // 0x13b293c: r8 = String
    //     0x13b293c: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x13b2940: r3 = Null
    //     0x13b2940: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ee10] Null
    //     0x13b2944: ldr             x3, [x3, #0xe10]
    // 0x13b2948: r0 = String()
    //     0x13b2948: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x13b294c: ldr             x0, [fp, #0x18]
    // 0x13b2950: r2 = Null
    //     0x13b2950: mov             x2, NULL
    // 0x13b2954: r1 = Null
    //     0x13b2954: mov             x1, NULL
    // 0x13b2958: branchIfSmi(r0, 0x13b2980)
    //     0x13b2958: tbz             w0, #0, #0x13b2980
    // 0x13b295c: r4 = LoadClassIdInstr(r0)
    //     0x13b295c: ldur            x4, [x0, #-1]
    //     0x13b2960: ubfx            x4, x4, #0xc, #0x14
    // 0x13b2964: sub             x4, x4, #0x3c
    // 0x13b2968: cmp             x4, #1
    // 0x13b296c: b.ls            #0x13b2980
    // 0x13b2970: r8 = int
    //     0x13b2970: ldr             x8, [PP, #0x3d8]  ; [pp+0x3d8] Type: int
    // 0x13b2974: r3 = Null
    //     0x13b2974: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ee20] Null
    //     0x13b2978: ldr             x3, [x3, #0xe20]
    // 0x13b297c: r0 = int()
    //     0x13b297c: bl              #0x16fc548  ; IsType_int_Stub
    // 0x13b2980: ldr             x0, [fp, #0x10]
    // 0x13b2984: cmp             w0, NULL
    // 0x13b2988: b.ne            #0x13b2998
    // 0x13b298c: r0 = ProductRating()
    //     0x13b298c: bl              #0x911a74  ; AllocateProductRatingStub -> ProductRating (size=0x18)
    // 0x13b2990: mov             x4, x0
    // 0x13b2994: b               #0x13b299c
    // 0x13b2998: mov             x4, x0
    // 0x13b299c: ldr             x3, [fp, #0x18]
    // 0x13b29a0: mov             x0, x4
    // 0x13b29a4: stur            x4, [fp, #-0x10]
    // 0x13b29a8: r2 = Null
    //     0x13b29a8: mov             x2, NULL
    // 0x13b29ac: r1 = Null
    //     0x13b29ac: mov             x1, NULL
    // 0x13b29b0: r4 = 60
    //     0x13b29b0: movz            x4, #0x3c
    // 0x13b29b4: branchIfSmi(r0, 0x13b29c0)
    //     0x13b29b4: tbz             w0, #0, #0x13b29c0
    // 0x13b29b8: r4 = LoadClassIdInstr(r0)
    //     0x13b29b8: ldur            x4, [x0, #-1]
    //     0x13b29bc: ubfx            x4, x4, #0xc, #0x14
    // 0x13b29c0: r17 = 5178
    //     0x13b29c0: movz            x17, #0x143a
    // 0x13b29c4: cmp             x4, x17
    // 0x13b29c8: b.eq            #0x13b29e0
    // 0x13b29cc: r8 = ProductRating
    //     0x13b29cc: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ee30] Type: ProductRating
    //     0x13b29d0: ldr             x8, [x8, #0xe30]
    // 0x13b29d4: r3 = Null
    //     0x13b29d4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ee38] Null
    //     0x13b29d8: ldr             x3, [x3, #0xe38]
    // 0x13b29dc: r0 = DefaultTypeTest()
    //     0x13b29dc: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x13b29e0: ldr             x0, [fp, #0x18]
    // 0x13b29e4: r6 = LoadInt32Instr(r0)
    //     0x13b29e4: sbfx            x6, x0, #1, #0x1f
    //     0x13b29e8: tbz             w0, #0, #0x13b29f0
    //     0x13b29ec: ldur            x6, [x0, #7]
    // 0x13b29f0: ldur            x1, [fp, #-8]
    // 0x13b29f4: ldr             x2, [fp, #0x30]
    // 0x13b29f8: ldr             x3, [fp, #0x28]
    // 0x13b29fc: ldr             x5, [fp, #0x20]
    // 0x13b2a00: ldur            x7, [fp, #-0x10]
    // 0x13b2a04: r0 = productViewedEvent()
    //     0x13b2a04: bl              #0x13b2a20  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::productViewedEvent
    // 0x13b2a08: r0 = Null
    //     0x13b2a08: mov             x0, NULL
    // 0x13b2a0c: LeaveFrame
    //     0x13b2a0c: mov             SP, fp
    //     0x13b2a10: ldp             fp, lr, [SP], #0x10
    // 0x13b2a14: ret
    //     0x13b2a14: ret             
    // 0x13b2a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b2a18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b2a1c: b               #0x13b28c4
  }
  [closure] Null <anonymous closure>(dynamic, String, int, String) {
    // ** addr: 0x13b2cdc, size: 0x98
    // 0x13b2cdc: EnterFrame
    //     0x13b2cdc: stp             fp, lr, [SP, #-0x10]!
    //     0x13b2ce0: mov             fp, SP
    // 0x13b2ce4: AllocStack(0x10)
    //     0x13b2ce4: sub             SP, SP, #0x10
    // 0x13b2ce8: SetupParameters()
    //     0x13b2ce8: ldr             x0, [fp, #0x28]
    //     0x13b2cec: ldur            w2, [x0, #0x17]
    //     0x13b2cf0: add             x2, x2, HEAP, lsl #32
    //     0x13b2cf4: stur            x2, [fp, #-8]
    // 0x13b2cf8: CheckStackOverflow
    //     0x13b2cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b2cfc: cmp             SP, x16
    //     0x13b2d00: b.ls            #0x13b2d6c
    // 0x13b2d04: LoadField: r1 = r2->field_f
    //     0x13b2d04: ldur            w1, [x2, #0xf]
    // 0x13b2d08: DecompressPointer r1
    //     0x13b2d08: add             x1, x1, HEAP, lsl #32
    // 0x13b2d0c: r0 = controller()
    //     0x13b2d0c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b2d10: mov             x1, x0
    // 0x13b2d14: ldr             x0, [fp, #0x18]
    // 0x13b2d18: r4 = LoadInt32Instr(r0)
    //     0x13b2d18: sbfx            x4, x0, #1, #0x1f
    //     0x13b2d1c: tbz             w0, #0, #0x13b2d24
    //     0x13b2d20: ldur            x4, [x0, #7]
    // 0x13b2d24: ldr             x2, [fp, #0x20]
    // 0x13b2d28: mov             x3, x4
    // 0x13b2d2c: ldr             x5, [fp, #0x10]
    // 0x13b2d30: stur            x4, [fp, #-0x10]
    // 0x13b2d34: r0 = checkoutTriggeredPostEvent()
    //     0x13b2d34: bl              #0x13b2e9c  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::checkoutTriggeredPostEvent
    // 0x13b2d38: ldur            x0, [fp, #-8]
    // 0x13b2d3c: LoadField: r1 = r0->field_f
    //     0x13b2d3c: ldur            w1, [x0, #0xf]
    // 0x13b2d40: DecompressPointer r1
    //     0x13b2d40: add             x1, x1, HEAP, lsl #32
    // 0x13b2d44: r0 = controller()
    //     0x13b2d44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b2d48: mov             x1, x0
    // 0x13b2d4c: ldr             x2, [fp, #0x20]
    // 0x13b2d50: ldur            x3, [fp, #-0x10]
    // 0x13b2d54: ldr             x5, [fp, #0x10]
    // 0x13b2d58: r0 = checkoutStartedPostEvent()
    //     0x13b2d58: bl              #0x13b2d74  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::checkoutStartedPostEvent
    // 0x13b2d5c: r0 = Null
    //     0x13b2d5c: mov             x0, NULL
    // 0x13b2d60: LeaveFrame
    //     0x13b2d60: mov             SP, fp
    //     0x13b2d64: ldp             fp, lr, [SP], #0x10
    // 0x13b2d68: ret
    //     0x13b2d68: ret             
    // 0x13b2d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b2d6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b2d70: b               #0x13b2d04
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String) {
    // ** addr: 0x13b2fc4, size: 0x94
    // 0x13b2fc4: EnterFrame
    //     0x13b2fc4: stp             fp, lr, [SP, #-0x10]!
    //     0x13b2fc8: mov             fp, SP
    // 0x13b2fcc: AllocStack(0x10)
    //     0x13b2fcc: sub             SP, SP, #0x10
    // 0x13b2fd0: SetupParameters()
    //     0x13b2fd0: ldr             x0, [fp, #0x28]
    //     0x13b2fd4: ldur            w2, [x0, #0x17]
    //     0x13b2fd8: add             x2, x2, HEAP, lsl #32
    //     0x13b2fdc: stur            x2, [fp, #-8]
    // 0x13b2fe0: CheckStackOverflow
    //     0x13b2fe0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b2fe4: cmp             SP, x16
    //     0x13b2fe8: b.ls            #0x13b3050
    // 0x13b2fec: LoadField: r1 = r2->field_f
    //     0x13b2fec: ldur            w1, [x2, #0xf]
    // 0x13b2ff0: DecompressPointer r1
    //     0x13b2ff0: add             x1, x1, HEAP, lsl #32
    // 0x13b2ff4: r0 = controller()
    //     0x13b2ff4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b2ff8: mov             x1, x0
    // 0x13b2ffc: ldr             x2, [fp, #0x20]
    // 0x13b3000: ldr             x3, [fp, #0x18]
    // 0x13b3004: r5 = "refresh"
    //     0x13b3004: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ed80] "refresh"
    //     0x13b3008: ldr             x5, [x5, #0xd80]
    // 0x13b300c: r0 = getSearch()
    //     0x13b300c: bl              #0x13b1260  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::getSearch
    // 0x13b3010: ldur            x0, [fp, #-8]
    // 0x13b3014: LoadField: r1 = r0->field_f
    //     0x13b3014: ldur            w1, [x0, #0xf]
    // 0x13b3018: DecompressPointer r1
    //     0x13b3018: add             x1, x1, HEAP, lsl #32
    // 0x13b301c: r0 = controller()
    //     0x13b301c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3020: ldr             x16, [fp, #0x10]
    // 0x13b3024: str             x16, [SP]
    // 0x13b3028: mov             x1, x0
    // 0x13b302c: r2 = "search_results_fetched"
    //     0x13b302c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "search_results_fetched"
    //     0x13b3030: ldr             x2, [x2, #0xba0]
    // 0x13b3034: r4 = const [0, 0x3, 0x1, 0x2, query, 0x2, null]
    //     0x13b3034: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eba8] List(7) [0, 0x3, 0x1, 0x2, "query", 0x2, Null]
    //     0x13b3038: ldr             x4, [x4, #0xba8]
    // 0x13b303c: r0 = searchEvents()
    //     0x13b303c: bl              #0x13b11b4  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::searchEvents
    // 0x13b3040: r0 = Null
    //     0x13b3040: mov             x0, NULL
    // 0x13b3044: LeaveFrame
    //     0x13b3044: mov             SP, fp
    //     0x13b3048: ldp             fp, lr, [SP], #0x10
    // 0x13b304c: ret
    //     0x13b304c: ret             
    // 0x13b3050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b3050: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b3054: b               #0x13b2fec
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x13b3058, size: 0xa0
    // 0x13b3058: EnterFrame
    //     0x13b3058: stp             fp, lr, [SP, #-0x10]!
    //     0x13b305c: mov             fp, SP
    // 0x13b3060: AllocStack(0x10)
    //     0x13b3060: sub             SP, SP, #0x10
    // 0x13b3064: SetupParameters()
    //     0x13b3064: ldr             x0, [fp, #0x10]
    //     0x13b3068: ldur            w2, [x0, #0x17]
    //     0x13b306c: add             x2, x2, HEAP, lsl #32
    //     0x13b3070: stur            x2, [fp, #-8]
    // 0x13b3074: CheckStackOverflow
    //     0x13b3074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b3078: cmp             SP, x16
    //     0x13b307c: b.ls            #0x13b30f0
    // 0x13b3080: LoadField: r1 = r2->field_f
    //     0x13b3080: ldur            w1, [x2, #0xf]
    // 0x13b3084: DecompressPointer r1
    //     0x13b3084: add             x1, x1, HEAP, lsl #32
    // 0x13b3088: r0 = controller()
    //     0x13b3088: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b308c: mov             x1, x0
    // 0x13b3090: r0 = initPaging()
    //     0x13b3090: bl              #0x13b318c  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::initPaging
    // 0x13b3094: ldur            x0, [fp, #-8]
    // 0x13b3098: LoadField: r1 = r0->field_f
    //     0x13b3098: ldur            w1, [x0, #0xf]
    // 0x13b309c: DecompressPointer r1
    //     0x13b309c: add             x1, x1, HEAP, lsl #32
    // 0x13b30a0: r0 = controller()
    //     0x13b30a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b30a4: mov             x1, x0
    // 0x13b30a8: r2 = true
    //     0x13b30a8: add             x2, NULL, #0x20  ; true
    // 0x13b30ac: r0 = onRefreshPage()
    //     0x13b30ac: bl              #0x13b30f8  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::onRefreshPage
    // 0x13b30b0: ldur            x0, [fp, #-8]
    // 0x13b30b4: LoadField: r1 = r0->field_f
    //     0x13b30b4: ldur            w1, [x0, #0xf]
    // 0x13b30b8: DecompressPointer r1
    //     0x13b30b8: add             x1, x1, HEAP, lsl #32
    // 0x13b30bc: r0 = controller()
    //     0x13b30bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b30c0: r16 = ""
    //     0x13b30c0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b30c4: str             x16, [SP]
    // 0x13b30c8: mov             x1, x0
    // 0x13b30cc: r2 = "search_results_fetched"
    //     0x13b30cc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "search_results_fetched"
    //     0x13b30d0: ldr             x2, [x2, #0xba0]
    // 0x13b30d4: r4 = const [0, 0x3, 0x1, 0x2, query, 0x2, null]
    //     0x13b30d4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eba8] List(7) [0, 0x3, 0x1, 0x2, "query", 0x2, Null]
    //     0x13b30d8: ldr             x4, [x4, #0xba8]
    // 0x13b30dc: r0 = searchEvents()
    //     0x13b30dc: bl              #0x13b11b4  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::searchEvents
    // 0x13b30e0: r0 = Null
    //     0x13b30e0: mov             x0, NULL
    // 0x13b30e4: LeaveFrame
    //     0x13b30e4: mov             SP, fp
    //     0x13b30e8: ldp             fp, lr, [SP], #0x10
    // 0x13b30ec: ret
    //     0x13b30ec: ret             
    // 0x13b30f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b30f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b30f4: b               #0x13b3080
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0x13b3208, size: 0x88
    // 0x13b3208: EnterFrame
    //     0x13b3208: stp             fp, lr, [SP, #-0x10]!
    //     0x13b320c: mov             fp, SP
    // 0x13b3210: AllocStack(0x10)
    //     0x13b3210: sub             SP, SP, #0x10
    // 0x13b3214: SetupParameters()
    //     0x13b3214: ldr             x0, [fp, #0x18]
    //     0x13b3218: ldur            w2, [x0, #0x17]
    //     0x13b321c: add             x2, x2, HEAP, lsl #32
    //     0x13b3220: stur            x2, [fp, #-8]
    // 0x13b3224: CheckStackOverflow
    //     0x13b3224: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b3228: cmp             SP, x16
    //     0x13b322c: b.ls            #0x13b3288
    // 0x13b3230: LoadField: r1 = r2->field_f
    //     0x13b3230: ldur            w1, [x2, #0xf]
    // 0x13b3234: DecompressPointer r1
    //     0x13b3234: add             x1, x1, HEAP, lsl #32
    // 0x13b3238: r0 = controller()
    //     0x13b3238: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b323c: mov             x1, x0
    // 0x13b3240: r2 = true
    //     0x13b3240: add             x2, NULL, #0x20  ; true
    // 0x13b3244: r0 = onRefreshPage()
    //     0x13b3244: bl              #0x13b30f8  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::onRefreshPage
    // 0x13b3248: ldur            x0, [fp, #-8]
    // 0x13b324c: LoadField: r1 = r0->field_f
    //     0x13b324c: ldur            w1, [x0, #0xf]
    // 0x13b3250: DecompressPointer r1
    //     0x13b3250: add             x1, x1, HEAP, lsl #32
    // 0x13b3254: r0 = controller()
    //     0x13b3254: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3258: ldr             x16, [fp, #0x10]
    // 0x13b325c: str             x16, [SP]
    // 0x13b3260: mov             x1, x0
    // 0x13b3264: r2 = "search_results_fetched"
    //     0x13b3264: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "search_results_fetched"
    //     0x13b3268: ldr             x2, [x2, #0xba0]
    // 0x13b326c: r4 = const [0, 0x3, 0x1, 0x2, query, 0x2, null]
    //     0x13b326c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eba8] List(7) [0, 0x3, 0x1, 0x2, "query", 0x2, Null]
    //     0x13b3270: ldr             x4, [x4, #0xba8]
    // 0x13b3274: r0 = searchEvents()
    //     0x13b3274: bl              #0x13b11b4  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::searchEvents
    // 0x13b3278: r0 = Null
    //     0x13b3278: mov             x0, NULL
    // 0x13b327c: LeaveFrame
    //     0x13b327c: mov             SP, fp
    //     0x13b3280: ldp             fp, lr, [SP], #0x10
    // 0x13b3284: ret
    //     0x13b3284: ret             
    // 0x13b3288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b3288: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b328c: b               #0x13b3230
  }
  [closure] Null <anonymous closure>(dynamic, String) {
    // ** addr: 0x13b3290, size: 0x88
    // 0x13b3290: EnterFrame
    //     0x13b3290: stp             fp, lr, [SP, #-0x10]!
    //     0x13b3294: mov             fp, SP
    // 0x13b3298: AllocStack(0x10)
    //     0x13b3298: sub             SP, SP, #0x10
    // 0x13b329c: SetupParameters()
    //     0x13b329c: ldr             x0, [fp, #0x18]
    //     0x13b32a0: ldur            w2, [x0, #0x17]
    //     0x13b32a4: add             x2, x2, HEAP, lsl #32
    //     0x13b32a8: stur            x2, [fp, #-8]
    // 0x13b32ac: CheckStackOverflow
    //     0x13b32ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b32b0: cmp             SP, x16
    //     0x13b32b4: b.ls            #0x13b3310
    // 0x13b32b8: LoadField: r1 = r2->field_f
    //     0x13b32b8: ldur            w1, [x2, #0xf]
    // 0x13b32bc: DecompressPointer r1
    //     0x13b32bc: add             x1, x1, HEAP, lsl #32
    // 0x13b32c0: r0 = controller()
    //     0x13b32c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b32c4: mov             x1, x0
    // 0x13b32c8: ldr             x2, [fp, #0x10]
    // 0x13b32cc: r0 = getSearchSuggestionData()
    //     0x13b32cc: bl              #0x13b3318  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::getSearchSuggestionData
    // 0x13b32d0: ldur            x0, [fp, #-8]
    // 0x13b32d4: LoadField: r1 = r0->field_f
    //     0x13b32d4: ldur            w1, [x0, #0xf]
    // 0x13b32d8: DecompressPointer r1
    //     0x13b32d8: add             x1, x1, HEAP, lsl #32
    // 0x13b32dc: r0 = controller()
    //     0x13b32dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b32e0: ldr             x16, [fp, #0x10]
    // 0x13b32e4: str             x16, [SP]
    // 0x13b32e8: mov             x1, x0
    // 0x13b32ec: r2 = "search_results_fetched"
    //     0x13b32ec: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "search_results_fetched"
    //     0x13b32f0: ldr             x2, [x2, #0xba0]
    // 0x13b32f4: r4 = const [0, 0x3, 0x1, 0x2, query, 0x2, null]
    //     0x13b32f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eba8] List(7) [0, 0x3, 0x1, 0x2, "query", 0x2, Null]
    //     0x13b32f8: ldr             x4, [x4, #0xba8]
    // 0x13b32fc: r0 = searchEvents()
    //     0x13b32fc: bl              #0x13b11b4  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::searchEvents
    // 0x13b3300: r0 = Null
    //     0x13b3300: mov             x0, NULL
    // 0x13b3304: LeaveFrame
    //     0x13b3304: mov             SP, fp
    //     0x13b3308: ldp             fp, lr, [SP], #0x10
    // 0x13b330c: ret
    //     0x13b330c: ret             
    // 0x13b3310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b3310: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b3314: b               #0x13b32b8
  }
  [closure] Null <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x13b3d00, size: 0x13c
    // 0x13b3d00: EnterFrame
    //     0x13b3d00: stp             fp, lr, [SP, #-0x10]!
    //     0x13b3d04: mov             fp, SP
    // 0x13b3d08: AllocStack(0x10)
    //     0x13b3d08: sub             SP, SP, #0x10
    // 0x13b3d0c: SetupParameters()
    //     0x13b3d0c: ldr             x0, [fp, #0x20]
    //     0x13b3d10: ldur            w2, [x0, #0x17]
    //     0x13b3d14: add             x2, x2, HEAP, lsl #32
    //     0x13b3d18: stur            x2, [fp, #-8]
    // 0x13b3d1c: CheckStackOverflow
    //     0x13b3d1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13b3d20: cmp             SP, x16
    //     0x13b3d24: b.ls            #0x13b3e34
    // 0x13b3d28: ldr             x0, [fp, #0x18]
    // 0x13b3d2c: LoadField: r1 = r0->field_7
    //     0x13b3d2c: ldur            w1, [x0, #7]
    // 0x13b3d30: r3 = LoadInt32Instr(r1)
    //     0x13b3d30: sbfx            x3, x1, #1, #0x1f
    // 0x13b3d34: cmp             x3, #3
    // 0x13b3d38: b.lt            #0x13b3dac
    // 0x13b3d3c: LoadField: r1 = r2->field_f
    //     0x13b3d3c: ldur            w1, [x2, #0xf]
    // 0x13b3d40: DecompressPointer r1
    //     0x13b3d40: add             x1, x1, HEAP, lsl #32
    // 0x13b3d44: r0 = controller()
    //     0x13b3d44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3d48: mov             x1, x0
    // 0x13b3d4c: r2 = false
    //     0x13b3d4c: add             x2, NULL, #0x30  ; false
    // 0x13b3d50: r0 = onRefreshPage()
    //     0x13b3d50: bl              #0x13b30f8  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::onRefreshPage
    // 0x13b3d54: ldur            x0, [fp, #-8]
    // 0x13b3d58: LoadField: r1 = r0->field_f
    //     0x13b3d58: ldur            w1, [x0, #0xf]
    // 0x13b3d5c: DecompressPointer r1
    //     0x13b3d5c: add             x1, x1, HEAP, lsl #32
    // 0x13b3d60: r0 = controller()
    //     0x13b3d60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3d64: mov             x1, x0
    // 0x13b3d68: ldr             x2, [fp, #0x18]
    // 0x13b3d6c: ldr             x3, [fp, #0x10]
    // 0x13b3d70: r5 = ""
    //     0x13b3d70: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b3d74: r0 = getSearch()
    //     0x13b3d74: bl              #0x13b1260  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::getSearch
    // 0x13b3d78: ldur            x0, [fp, #-8]
    // 0x13b3d7c: LoadField: r1 = r0->field_f
    //     0x13b3d7c: ldur            w1, [x0, #0xf]
    // 0x13b3d80: DecompressPointer r1
    //     0x13b3d80: add             x1, x1, HEAP, lsl #32
    // 0x13b3d84: r0 = controller()
    //     0x13b3d84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3d88: ldr             x16, [fp, #0x18]
    // 0x13b3d8c: str             x16, [SP]
    // 0x13b3d90: mov             x1, x0
    // 0x13b3d94: r2 = "search_results_fetched"
    //     0x13b3d94: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "search_results_fetched"
    //     0x13b3d98: ldr             x2, [x2, #0xba0]
    // 0x13b3d9c: r4 = const [0, 0x3, 0x1, 0x2, query, 0x2, null]
    //     0x13b3d9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eba8] List(7) [0, 0x3, 0x1, 0x2, "query", 0x2, Null]
    //     0x13b3da0: ldr             x4, [x4, #0xba8]
    // 0x13b3da4: r0 = searchEvents()
    //     0x13b3da4: bl              #0x13b11b4  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::searchEvents
    // 0x13b3da8: b               #0x13b3e24
    // 0x13b3dac: mov             x0, x2
    // 0x13b3db0: r16 = Instance_Color
    //     0x13b3db0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0x13b3db4: ldr             x16, [x16, #0x50]
    // 0x13b3db8: str             x16, [SP]
    // 0x13b3dbc: r1 = "Please enter more than 3 characters"
    //     0x13b3dbc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f058] "Please enter more than 3 characters"
    //     0x13b3dc0: ldr             x1, [x1, #0x58]
    // 0x13b3dc4: r4 = const [0, 0x2, 0x1, 0x1, backgroundColor, 0x1, null]
    //     0x13b3dc4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f060] List(7) [0, 0x2, 0x1, 0x1, "backgroundColor", 0x1, Null]
    //     0x13b3dc8: ldr             x4, [x4, #0x60]
    // 0x13b3dcc: r0 = showToast()
    //     0x13b3dcc: bl              #0x89cd74  ; [package:fluttertoast/fluttertoast.dart] Fluttertoast::showToast
    // 0x13b3dd0: ldur            x0, [fp, #-8]
    // 0x13b3dd4: LoadField: r1 = r0->field_f
    //     0x13b3dd4: ldur            w1, [x0, #0xf]
    // 0x13b3dd8: DecompressPointer r1
    //     0x13b3dd8: add             x1, x1, HEAP, lsl #32
    // 0x13b3ddc: r0 = controller()
    //     0x13b3ddc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3de0: mov             x1, x0
    // 0x13b3de4: ldr             x3, [fp, #0x10]
    // 0x13b3de8: r2 = " "
    //     0x13b3de8: ldr             x2, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0x13b3dec: r5 = ""
    //     0x13b3dec: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13b3df0: r0 = getSearch()
    //     0x13b3df0: bl              #0x13b1260  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::getSearch
    // 0x13b3df4: ldur            x0, [fp, #-8]
    // 0x13b3df8: LoadField: r1 = r0->field_f
    //     0x13b3df8: ldur            w1, [x0, #0xf]
    // 0x13b3dfc: DecompressPointer r1
    //     0x13b3dfc: add             x1, x1, HEAP, lsl #32
    // 0x13b3e00: r0 = controller()
    //     0x13b3e00: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13b3e04: ldr             x16, [fp, #0x18]
    // 0x13b3e08: str             x16, [SP]
    // 0x13b3e0c: mov             x1, x0
    // 0x13b3e10: r2 = "search_results_fetched"
    //     0x13b3e10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2eba0] "search_results_fetched"
    //     0x13b3e14: ldr             x2, [x2, #0xba0]
    // 0x13b3e18: r4 = const [0, 0x3, 0x1, 0x2, query, 0x2, null]
    //     0x13b3e18: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eba8] List(7) [0, 0x3, 0x1, 0x2, "query", 0x2, Null]
    //     0x13b3e1c: ldr             x4, [x4, #0xba8]
    // 0x13b3e20: r0 = searchEvents()
    //     0x13b3e20: bl              #0x13b11b4  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::searchEvents
    // 0x13b3e24: r0 = Null
    //     0x13b3e24: mov             x0, NULL
    // 0x13b3e28: LeaveFrame
    //     0x13b3e28: mov             SP, fp
    //     0x13b3e2c: ldp             fp, lr, [SP], #0x10
    // 0x13b3e30: ret
    //     0x13b3e30: ret             
    // 0x13b3e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13b3e34: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13b3e38: b               #0x13b3d28
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1479614, size: 0x50
    // 0x1479614: EnterFrame
    //     0x1479614: stp             fp, lr, [SP, #-0x10]!
    //     0x1479618: mov             fp, SP
    // 0x147961c: ldr             x0, [fp, #0x10]
    // 0x1479620: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x1479620: ldur            w1, [x0, #0x17]
    // 0x1479624: DecompressPointer r1
    //     0x1479624: add             x1, x1, HEAP, lsl #32
    // 0x1479628: CheckStackOverflow
    //     0x1479628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147962c: cmp             SP, x16
    //     0x1479630: b.ls            #0x147965c
    // 0x1479634: LoadField: r0 = r1->field_f
    //     0x1479634: ldur            w0, [x1, #0xf]
    // 0x1479638: DecompressPointer r0
    //     0x1479638: add             x0, x0, HEAP, lsl #32
    // 0x147963c: mov             x1, x0
    // 0x1479640: r0 = controller()
    //     0x1479640: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1479644: mov             x1, x0
    // 0x1479648: r0 = onLoadNextPage()
    //     0x1479648: bl              #0x1479664  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::onLoadNextPage
    // 0x147964c: r0 = Null
    //     0x147964c: mov             x0, NULL
    // 0x1479650: LeaveFrame
    //     0x1479650: mov             SP, fp
    //     0x1479654: ldp             fp, lr, [SP], #0x10
    // 0x1479658: ret
    //     0x1479658: ret             
    // 0x147965c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147965c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479660: b               #0x1479634
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x14796e4, size: 0x68
    // 0x14796e4: EnterFrame
    //     0x14796e4: stp             fp, lr, [SP, #-0x10]!
    //     0x14796e8: mov             fp, SP
    // 0x14796ec: AllocStack(0x10)
    //     0x14796ec: sub             SP, SP, #0x10
    // 0x14796f0: SetupParameters(SearchPage this /* r1 */)
    //     0x14796f0: stur            NULL, [fp, #-8]
    //     0x14796f4: movz            x0, #0
    //     0x14796f8: add             x1, fp, w0, sxtw #2
    //     0x14796fc: ldr             x1, [x1, #0x10]
    //     0x1479700: ldur            w2, [x1, #0x17]
    //     0x1479704: add             x2, x2, HEAP, lsl #32
    //     0x1479708: stur            x2, [fp, #-0x10]
    // 0x147970c: CheckStackOverflow
    //     0x147970c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1479710: cmp             SP, x16
    //     0x1479714: b.ls            #0x1479744
    // 0x1479718: InitAsync() -> Future<void?>
    //     0x1479718: ldr             x0, [PP, #0x300]  ; [pp+0x300] TypeArguments: <void?>
    //     0x147971c: bl              #0x6326e0  ; InitAsyncStub
    // 0x1479720: ldur            x0, [fp, #-0x10]
    // 0x1479724: LoadField: r1 = r0->field_f
    //     0x1479724: ldur            w1, [x0, #0xf]
    // 0x1479728: DecompressPointer r1
    //     0x1479728: add             x1, x1, HEAP, lsl #32
    // 0x147972c: r0 = controller()
    //     0x147972c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1479730: mov             x1, x0
    // 0x1479734: r2 = false
    //     0x1479734: add             x2, NULL, #0x30  ; false
    // 0x1479738: r0 = onRefreshPage()
    //     0x1479738: bl              #0x13b30f8  ; [package:customer_app/app/presentation/controllers/search/search_controller.dart] SearchController::onRefreshPage
    // 0x147973c: r0 = Null
    //     0x147973c: mov             x0, NULL
    // 0x1479740: r0 = ReturnAsyncNotFuture()
    //     0x1479740: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1479744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1479744: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1479748: b               #0x1479718
  }
  _ body(/* No info */) {
    // ** addr: 0x15095b0, size: 0xe4
    // 0x15095b0: EnterFrame
    //     0x15095b0: stp             fp, lr, [SP, #-0x10]!
    //     0x15095b4: mov             fp, SP
    // 0x15095b8: AllocStack(0x20)
    //     0x15095b8: sub             SP, SP, #0x20
    // 0x15095bc: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */)
    //     0x15095bc: mov             x0, x1
    //     0x15095c0: stur            x1, [fp, #-8]
    // 0x15095c4: CheckStackOverflow
    //     0x15095c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15095c8: cmp             SP, x16
    //     0x15095cc: b.ls            #0x150968c
    // 0x15095d0: r1 = 1
    //     0x15095d0: movz            x1, #0x1
    // 0x15095d4: r0 = AllocateContext()
    //     0x15095d4: bl              #0x16f6108  ; AllocateContextStub
    // 0x15095d8: ldur            x2, [fp, #-8]
    // 0x15095dc: stur            x0, [fp, #-0x10]
    // 0x15095e0: StoreField: r0->field_f = r2
    //     0x15095e0: stur            w2, [x0, #0xf]
    // 0x15095e4: r0 = Obx()
    //     0x15095e4: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15095e8: ldur            x2, [fp, #-0x10]
    // 0x15095ec: r1 = Function '<anonymous closure>':.
    //     0x15095ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb80] AnonymousClosure: (0x13b0c98), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x15095f0: ldr             x1, [x1, #0xb80]
    // 0x15095f4: stur            x0, [fp, #-0x18]
    // 0x15095f8: r0 = AllocateClosure()
    //     0x15095f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15095fc: mov             x1, x0
    // 0x1509600: ldur            x0, [fp, #-0x18]
    // 0x1509604: StoreField: r0->field_b = r1
    //     0x1509604: stur            w1, [x0, #0xb]
    // 0x1509608: r0 = WillPopScope()
    //     0x1509608: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x150960c: mov             x3, x0
    // 0x1509610: ldur            x0, [fp, #-0x18]
    // 0x1509614: stur            x3, [fp, #-0x20]
    // 0x1509618: StoreField: r3->field_b = r0
    //     0x1509618: stur            w0, [x3, #0xb]
    // 0x150961c: ldur            x2, [fp, #-8]
    // 0x1509620: r1 = Function 'onBackPress':.
    //     0x1509620: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb88] AnonymousClosure: (0x1509694), in [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress (0x1479784)
    //     0x1509624: ldr             x1, [x1, #0xb88]
    // 0x1509628: r0 = AllocateClosure()
    //     0x1509628: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x150962c: mov             x1, x0
    // 0x1509630: ldur            x0, [fp, #-0x20]
    // 0x1509634: StoreField: r0->field_f = r1
    //     0x1509634: stur            w1, [x0, #0xf]
    // 0x1509638: ldur            x2, [fp, #-0x10]
    // 0x150963c: r1 = Function '<anonymous closure>':.
    //     0x150963c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb90] AnonymousClosure: (0x14796e4), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x1509640: ldr             x1, [x1, #0xb90]
    // 0x1509644: r0 = AllocateClosure()
    //     0x1509644: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1509648: ldur            x2, [fp, #-0x10]
    // 0x150964c: r1 = Function '<anonymous closure>':.
    //     0x150964c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eb98] AnonymousClosure: (0x1479614), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::body (0x15095b0)
    //     0x1509650: ldr             x1, [x1, #0xb98]
    // 0x1509654: stur            x0, [fp, #-8]
    // 0x1509658: r0 = AllocateClosure()
    //     0x1509658: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x150965c: stur            x0, [fp, #-0x10]
    // 0x1509660: r0 = PagingView()
    //     0x1509660: bl              #0x8a3854  ; AllocatePagingViewStub -> PagingView (size=0x20)
    // 0x1509664: mov             x1, x0
    // 0x1509668: ldur            x2, [fp, #-0x20]
    // 0x150966c: ldur            x3, [fp, #-0x10]
    // 0x1509670: ldur            x5, [fp, #-8]
    // 0x1509674: stur            x0, [fp, #-8]
    // 0x1509678: r0 = PagingView()
    //     0x1509678: bl              #0x8a375c  ; [package:customer_app/app/presentation/custom_widgets/paging_view.dart] PagingView::PagingView
    // 0x150967c: ldur            x0, [fp, #-8]
    // 0x1509680: LeaveFrame
    //     0x1509680: mov             SP, fp
    //     0x1509684: ldp             fp, lr, [SP], #0x10
    // 0x1509688: ret
    //     0x1509688: ret             
    // 0x150968c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x150968c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1509690: b               #0x15095d0
  }
  [closure] Future<bool> onBackPress(dynamic) {
    // ** addr: 0x1509694, size: 0x38
    // 0x1509694: EnterFrame
    //     0x1509694: stp             fp, lr, [SP, #-0x10]!
    //     0x1509698: mov             fp, SP
    // 0x150969c: ldr             x0, [fp, #0x10]
    // 0x15096a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15096a0: ldur            w1, [x0, #0x17]
    // 0x15096a4: DecompressPointer r1
    //     0x15096a4: add             x1, x1, HEAP, lsl #32
    // 0x15096a8: CheckStackOverflow
    //     0x15096a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15096ac: cmp             SP, x16
    //     0x15096b0: b.ls            #0x15096c4
    // 0x15096b4: r0 = onBackPress()
    //     0x15096b4: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x15096b8: LeaveFrame
    //     0x15096b8: mov             SP, fp
    //     0x15096bc: ldp             fp, lr, [SP], #0x10
    // 0x15096c0: ret
    //     0x15096c0: ret             
    // 0x15096c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15096c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15096c8: b               #0x15096b4
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15ee2a4, size: 0x27c
    // 0x15ee2a4: EnterFrame
    //     0x15ee2a4: stp             fp, lr, [SP, #-0x10]!
    //     0x15ee2a8: mov             fp, SP
    // 0x15ee2ac: AllocStack(0x38)
    //     0x15ee2ac: sub             SP, SP, #0x38
    // 0x15ee2b0: SetupParameters(SearchPage this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15ee2b0: mov             x0, x1
    //     0x15ee2b4: stur            x1, [fp, #-8]
    //     0x15ee2b8: mov             x1, x2
    //     0x15ee2bc: stur            x2, [fp, #-0x10]
    // 0x15ee2c0: CheckStackOverflow
    //     0x15ee2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ee2c4: cmp             SP, x16
    //     0x15ee2c8: b.ls            #0x15ee508
    // 0x15ee2cc: r1 = 2
    //     0x15ee2cc: movz            x1, #0x2
    // 0x15ee2d0: r0 = AllocateContext()
    //     0x15ee2d0: bl              #0x16f6108  ; AllocateContextStub
    // 0x15ee2d4: ldur            x1, [fp, #-8]
    // 0x15ee2d8: stur            x0, [fp, #-0x18]
    // 0x15ee2dc: StoreField: r0->field_f = r1
    //     0x15ee2dc: stur            w1, [x0, #0xf]
    // 0x15ee2e0: ldur            x2, [fp, #-0x10]
    // 0x15ee2e4: StoreField: r0->field_13 = r2
    //     0x15ee2e4: stur            w2, [x0, #0x13]
    // 0x15ee2e8: r0 = Obx()
    //     0x15ee2e8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15ee2ec: ldur            x2, [fp, #-0x18]
    // 0x15ee2f0: r1 = Function '<anonymous closure>':.
    //     0x15ee2f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f068] AnonymousClosure: (0x15ee568), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::appBar (0x15ee2a4)
    //     0x15ee2f4: ldr             x1, [x1, #0x68]
    // 0x15ee2f8: stur            x0, [fp, #-0x20]
    // 0x15ee2fc: r0 = AllocateClosure()
    //     0x15ee2fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ee300: mov             x1, x0
    // 0x15ee304: ldur            x0, [fp, #-0x20]
    // 0x15ee308: StoreField: r0->field_b = r1
    //     0x15ee308: stur            w1, [x0, #0xb]
    // 0x15ee30c: ldur            x1, [fp, #-0x10]
    // 0x15ee310: r0 = of()
    //     0x15ee310: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ee314: LoadField: r1 = r0->field_5b
    //     0x15ee314: ldur            w1, [x0, #0x5b]
    // 0x15ee318: DecompressPointer r1
    //     0x15ee318: add             x1, x1, HEAP, lsl #32
    // 0x15ee31c: stur            x1, [fp, #-0x10]
    // 0x15ee320: r0 = ColorFilter()
    //     0x15ee320: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15ee324: mov             x1, x0
    // 0x15ee328: ldur            x0, [fp, #-0x10]
    // 0x15ee32c: stur            x1, [fp, #-0x28]
    // 0x15ee330: StoreField: r1->field_7 = r0
    //     0x15ee330: stur            w0, [x1, #7]
    // 0x15ee334: r0 = Instance_BlendMode
    //     0x15ee334: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15ee338: ldr             x0, [x0, #0xb30]
    // 0x15ee33c: StoreField: r1->field_b = r0
    //     0x15ee33c: stur            w0, [x1, #0xb]
    // 0x15ee340: r0 = 1
    //     0x15ee340: movz            x0, #0x1
    // 0x15ee344: StoreField: r1->field_13 = r0
    //     0x15ee344: stur            x0, [x1, #0x13]
    // 0x15ee348: r0 = SvgPicture()
    //     0x15ee348: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15ee34c: stur            x0, [fp, #-0x10]
    // 0x15ee350: ldur            x16, [fp, #-0x28]
    // 0x15ee354: str             x16, [SP]
    // 0x15ee358: mov             x1, x0
    // 0x15ee35c: r2 = "assets/images/appbar_arrow.svg"
    //     0x15ee35c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15ee360: ldr             x2, [x2, #0xa40]
    // 0x15ee364: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15ee364: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15ee368: ldr             x4, [x4, #0xa38]
    // 0x15ee36c: r0 = SvgPicture.asset()
    //     0x15ee36c: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15ee370: r0 = Align()
    //     0x15ee370: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15ee374: mov             x1, x0
    // 0x15ee378: r0 = Instance_Alignment
    //     0x15ee378: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15ee37c: ldr             x0, [x0, #0xb10]
    // 0x15ee380: stur            x1, [fp, #-0x28]
    // 0x15ee384: StoreField: r1->field_f = r0
    //     0x15ee384: stur            w0, [x1, #0xf]
    // 0x15ee388: r0 = 1.000000
    //     0x15ee388: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15ee38c: StoreField: r1->field_13 = r0
    //     0x15ee38c: stur            w0, [x1, #0x13]
    // 0x15ee390: ArrayStore: r1[0] = r0  ; List_4
    //     0x15ee390: stur            w0, [x1, #0x17]
    // 0x15ee394: ldur            x0, [fp, #-0x10]
    // 0x15ee398: StoreField: r1->field_b = r0
    //     0x15ee398: stur            w0, [x1, #0xb]
    // 0x15ee39c: r0 = InkWell()
    //     0x15ee39c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15ee3a0: mov             x3, x0
    // 0x15ee3a4: ldur            x0, [fp, #-0x28]
    // 0x15ee3a8: stur            x3, [fp, #-0x10]
    // 0x15ee3ac: StoreField: r3->field_b = r0
    //     0x15ee3ac: stur            w0, [x3, #0xb]
    // 0x15ee3b0: ldur            x2, [fp, #-0x18]
    // 0x15ee3b4: r1 = Function '<anonymous closure>':.
    //     0x15ee3b4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f070] AnonymousClosure: (0x15ee520), in [package:customer_app/app/presentation/views/line/search/search_page.dart] SearchPage::appBar (0x15ee2a4)
    //     0x15ee3b8: ldr             x1, [x1, #0x70]
    // 0x15ee3bc: r0 = AllocateClosure()
    //     0x15ee3bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15ee3c0: ldur            x2, [fp, #-0x10]
    // 0x15ee3c4: StoreField: r2->field_f = r0
    //     0x15ee3c4: stur            w0, [x2, #0xf]
    // 0x15ee3c8: r0 = true
    //     0x15ee3c8: add             x0, NULL, #0x20  ; true
    // 0x15ee3cc: StoreField: r2->field_43 = r0
    //     0x15ee3cc: stur            w0, [x2, #0x43]
    // 0x15ee3d0: r1 = Instance_BoxShape
    //     0x15ee3d0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15ee3d4: ldr             x1, [x1, #0x80]
    // 0x15ee3d8: StoreField: r2->field_47 = r1
    //     0x15ee3d8: stur            w1, [x2, #0x47]
    // 0x15ee3dc: StoreField: r2->field_6f = r0
    //     0x15ee3dc: stur            w0, [x2, #0x6f]
    // 0x15ee3e0: r1 = false
    //     0x15ee3e0: add             x1, NULL, #0x30  ; false
    // 0x15ee3e4: StoreField: r2->field_73 = r1
    //     0x15ee3e4: stur            w1, [x2, #0x73]
    // 0x15ee3e8: StoreField: r2->field_83 = r0
    //     0x15ee3e8: stur            w0, [x2, #0x83]
    // 0x15ee3ec: StoreField: r2->field_7b = r1
    //     0x15ee3ec: stur            w1, [x2, #0x7b]
    // 0x15ee3f0: ldur            x1, [fp, #-8]
    // 0x15ee3f4: r0 = controller()
    //     0x15ee3f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee3f8: LoadField: r1 = r0->field_67
    //     0x15ee3f8: ldur            w1, [x0, #0x67]
    // 0x15ee3fc: DecompressPointer r1
    //     0x15ee3fc: add             x1, x1, HEAP, lsl #32
    // 0x15ee400: r0 = value()
    //     0x15ee400: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee404: LoadField: r1 = r0->field_3f
    //     0x15ee404: ldur            w1, [x0, #0x3f]
    // 0x15ee408: DecompressPointer r1
    //     0x15ee408: add             x1, x1, HEAP, lsl #32
    // 0x15ee40c: cmp             w1, NULL
    // 0x15ee410: b.ne            #0x15ee41c
    // 0x15ee414: r0 = Null
    //     0x15ee414: mov             x0, NULL
    // 0x15ee418: b               #0x15ee424
    // 0x15ee41c: LoadField: r0 = r1->field_f
    //     0x15ee41c: ldur            w0, [x1, #0xf]
    // 0x15ee420: DecompressPointer r0
    //     0x15ee420: add             x0, x0, HEAP, lsl #32
    // 0x15ee424: r1 = LoadClassIdInstr(r0)
    //     0x15ee424: ldur            x1, [x0, #-1]
    //     0x15ee428: ubfx            x1, x1, #0xc, #0x14
    // 0x15ee42c: r16 = "text"
    //     0x15ee42c: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15ee430: stp             x16, x0, [SP]
    // 0x15ee434: mov             x0, x1
    // 0x15ee438: mov             lr, x0
    // 0x15ee43c: ldr             lr, [x21, lr, lsl #3]
    // 0x15ee440: blr             lr
    // 0x15ee444: tbnz            w0, #4, #0x15ee450
    // 0x15ee448: d0 = 0.000000
    //     0x15ee448: eor             v0.16b, v0.16b, v0.16b
    // 0x15ee44c: b               #0x15ee454
    // 0x15ee450: d0 = 50.000000
    //     0x15ee450: ldr             d0, [PP, #0x5ab0]  ; [pp+0x5ab0] IMM: double(50) from 0x4049000000000000
    // 0x15ee454: r0 = inline_Allocate_Double()
    //     0x15ee454: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x15ee458: add             x0, x0, #0x10
    //     0x15ee45c: cmp             x1, x0
    //     0x15ee460: b.ls            #0x15ee510
    //     0x15ee464: str             x0, [THR, #0x50]  ; THR::top
    //     0x15ee468: sub             x0, x0, #0xf
    //     0x15ee46c: movz            x1, #0xe15c
    //     0x15ee470: movk            x1, #0x3, lsl #16
    //     0x15ee474: stur            x1, [x0, #-1]
    // 0x15ee478: StoreField: r0->field_7 = d0
    //     0x15ee478: stur            d0, [x0, #7]
    // 0x15ee47c: stur            x0, [fp, #-8]
    // 0x15ee480: r0 = SizedBox()
    //     0x15ee480: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x15ee484: mov             x3, x0
    // 0x15ee488: ldur            x0, [fp, #-8]
    // 0x15ee48c: stur            x3, [fp, #-0x18]
    // 0x15ee490: StoreField: r3->field_f = r0
    //     0x15ee490: stur            w0, [x3, #0xf]
    // 0x15ee494: r1 = Null
    //     0x15ee494: mov             x1, NULL
    // 0x15ee498: r2 = 2
    //     0x15ee498: movz            x2, #0x2
    // 0x15ee49c: r0 = AllocateArray()
    //     0x15ee49c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15ee4a0: mov             x2, x0
    // 0x15ee4a4: ldur            x0, [fp, #-0x18]
    // 0x15ee4a8: stur            x2, [fp, #-8]
    // 0x15ee4ac: StoreField: r2->field_f = r0
    //     0x15ee4ac: stur            w0, [x2, #0xf]
    // 0x15ee4b0: r1 = <Widget>
    //     0x15ee4b0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15ee4b4: r0 = AllocateGrowableArray()
    //     0x15ee4b4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15ee4b8: mov             x1, x0
    // 0x15ee4bc: ldur            x0, [fp, #-8]
    // 0x15ee4c0: stur            x1, [fp, #-0x18]
    // 0x15ee4c4: StoreField: r1->field_f = r0
    //     0x15ee4c4: stur            w0, [x1, #0xf]
    // 0x15ee4c8: r0 = 2
    //     0x15ee4c8: movz            x0, #0x2
    // 0x15ee4cc: StoreField: r1->field_b = r0
    //     0x15ee4cc: stur            w0, [x1, #0xb]
    // 0x15ee4d0: r0 = AppBar()
    //     0x15ee4d0: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15ee4d4: stur            x0, [fp, #-8]
    // 0x15ee4d8: ldur            x16, [fp, #-0x20]
    // 0x15ee4dc: ldur            lr, [fp, #-0x18]
    // 0x15ee4e0: stp             lr, x16, [SP]
    // 0x15ee4e4: mov             x1, x0
    // 0x15ee4e8: ldur            x2, [fp, #-0x10]
    // 0x15ee4ec: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15ee4ec: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15ee4f0: ldr             x4, [x4, #0xa58]
    // 0x15ee4f4: r0 = AppBar()
    //     0x15ee4f4: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15ee4f8: ldur            x0, [fp, #-8]
    // 0x15ee4fc: LeaveFrame
    //     0x15ee4fc: mov             SP, fp
    //     0x15ee500: ldp             fp, lr, [SP], #0x10
    // 0x15ee504: ret
    //     0x15ee504: ret             
    // 0x15ee508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ee508: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ee50c: b               #0x15ee2cc
    // 0x15ee510: SaveReg d0
    //     0x15ee510: str             q0, [SP, #-0x10]!
    // 0x15ee514: r0 = AllocateDouble()
    //     0x15ee514: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x15ee518: RestoreReg d0
    //     0x15ee518: ldr             q0, [SP], #0x10
    // 0x15ee51c: b               #0x15ee478
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x15ee520, size: 0x48
    // 0x15ee520: EnterFrame
    //     0x15ee520: stp             fp, lr, [SP, #-0x10]!
    //     0x15ee524: mov             fp, SP
    // 0x15ee528: ldr             x0, [fp, #0x10]
    // 0x15ee52c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x15ee52c: ldur            w1, [x0, #0x17]
    // 0x15ee530: DecompressPointer r1
    //     0x15ee530: add             x1, x1, HEAP, lsl #32
    // 0x15ee534: CheckStackOverflow
    //     0x15ee534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ee538: cmp             SP, x16
    //     0x15ee53c: b.ls            #0x15ee560
    // 0x15ee540: LoadField: r0 = r1->field_f
    //     0x15ee540: ldur            w0, [x1, #0xf]
    // 0x15ee544: DecompressPointer r0
    //     0x15ee544: add             x0, x0, HEAP, lsl #32
    // 0x15ee548: mov             x1, x0
    // 0x15ee54c: r0 = onBackPress()
    //     0x15ee54c: bl              #0x1479784  ; [package:customer_app/app/presentation/views/basic/search/search_page.dart] SearchPage::onBackPress
    // 0x15ee550: r0 = Null
    //     0x15ee550: mov             x0, NULL
    // 0x15ee554: LeaveFrame
    //     0x15ee554: mov             SP, fp
    //     0x15ee558: ldp             fp, lr, [SP], #0x10
    // 0x15ee55c: ret
    //     0x15ee55c: ret             
    // 0x15ee560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15ee560: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15ee564: b               #0x15ee540
  }
  [closure] Row <anonymous closure>(dynamic) {
    // ** addr: 0x15ee568, size: 0x4b0
    // 0x15ee568: EnterFrame
    //     0x15ee568: stp             fp, lr, [SP, #-0x10]!
    //     0x15ee56c: mov             fp, SP
    // 0x15ee570: AllocStack(0x48)
    //     0x15ee570: sub             SP, SP, #0x48
    // 0x15ee574: SetupParameters()
    //     0x15ee574: ldr             x0, [fp, #0x10]
    //     0x15ee578: ldur            w2, [x0, #0x17]
    //     0x15ee57c: add             x2, x2, HEAP, lsl #32
    //     0x15ee580: stur            x2, [fp, #-8]
    // 0x15ee584: CheckStackOverflow
    //     0x15ee584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15ee588: cmp             SP, x16
    //     0x15ee58c: b.ls            #0x15eea10
    // 0x15ee590: LoadField: r1 = r2->field_f
    //     0x15ee590: ldur            w1, [x2, #0xf]
    // 0x15ee594: DecompressPointer r1
    //     0x15ee594: add             x1, x1, HEAP, lsl #32
    // 0x15ee598: r0 = controller()
    //     0x15ee598: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee59c: LoadField: r1 = r0->field_67
    //     0x15ee59c: ldur            w1, [x0, #0x67]
    // 0x15ee5a0: DecompressPointer r1
    //     0x15ee5a0: add             x1, x1, HEAP, lsl #32
    // 0x15ee5a4: r0 = value()
    //     0x15ee5a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee5a8: LoadField: r1 = r0->field_3f
    //     0x15ee5a8: ldur            w1, [x0, #0x3f]
    // 0x15ee5ac: DecompressPointer r1
    //     0x15ee5ac: add             x1, x1, HEAP, lsl #32
    // 0x15ee5b0: cmp             w1, NULL
    // 0x15ee5b4: b.ne            #0x15ee5c0
    // 0x15ee5b8: r0 = Null
    //     0x15ee5b8: mov             x0, NULL
    // 0x15ee5bc: b               #0x15ee5c8
    // 0x15ee5c0: LoadField: r0 = r1->field_f
    //     0x15ee5c0: ldur            w0, [x1, #0xf]
    // 0x15ee5c4: DecompressPointer r0
    //     0x15ee5c4: add             x0, x0, HEAP, lsl #32
    // 0x15ee5c8: r1 = LoadClassIdInstr(r0)
    //     0x15ee5c8: ldur            x1, [x0, #-1]
    //     0x15ee5cc: ubfx            x1, x1, #0xc, #0x14
    // 0x15ee5d0: r16 = "image_text"
    //     0x15ee5d0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15ee5d4: ldr             x16, [x16, #0xa88]
    // 0x15ee5d8: stp             x16, x0, [SP]
    // 0x15ee5dc: mov             x0, x1
    // 0x15ee5e0: mov             lr, x0
    // 0x15ee5e4: ldr             lr, [x21, lr, lsl #3]
    // 0x15ee5e8: blr             lr
    // 0x15ee5ec: tbnz            w0, #4, #0x15ee5f8
    // 0x15ee5f0: r1 = true
    //     0x15ee5f0: add             x1, NULL, #0x20  ; true
    // 0x15ee5f4: b               #0x15ee658
    // 0x15ee5f8: ldur            x0, [fp, #-8]
    // 0x15ee5fc: LoadField: r1 = r0->field_f
    //     0x15ee5fc: ldur            w1, [x0, #0xf]
    // 0x15ee600: DecompressPointer r1
    //     0x15ee600: add             x1, x1, HEAP, lsl #32
    // 0x15ee604: r0 = controller()
    //     0x15ee604: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee608: LoadField: r1 = r0->field_67
    //     0x15ee608: ldur            w1, [x0, #0x67]
    // 0x15ee60c: DecompressPointer r1
    //     0x15ee60c: add             x1, x1, HEAP, lsl #32
    // 0x15ee610: r0 = value()
    //     0x15ee610: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee614: LoadField: r1 = r0->field_3f
    //     0x15ee614: ldur            w1, [x0, #0x3f]
    // 0x15ee618: DecompressPointer r1
    //     0x15ee618: add             x1, x1, HEAP, lsl #32
    // 0x15ee61c: cmp             w1, NULL
    // 0x15ee620: b.ne            #0x15ee62c
    // 0x15ee624: r0 = Null
    //     0x15ee624: mov             x0, NULL
    // 0x15ee628: b               #0x15ee634
    // 0x15ee62c: LoadField: r0 = r1->field_f
    //     0x15ee62c: ldur            w0, [x1, #0xf]
    // 0x15ee630: DecompressPointer r0
    //     0x15ee630: add             x0, x0, HEAP, lsl #32
    // 0x15ee634: r1 = LoadClassIdInstr(r0)
    //     0x15ee634: ldur            x1, [x0, #-1]
    //     0x15ee638: ubfx            x1, x1, #0xc, #0x14
    // 0x15ee63c: r16 = "image"
    //     0x15ee63c: ldr             x16, [PP, #0x7c10]  ; [pp+0x7c10] "image"
    // 0x15ee640: stp             x16, x0, [SP]
    // 0x15ee644: mov             x0, x1
    // 0x15ee648: mov             lr, x0
    // 0x15ee64c: ldr             lr, [x21, lr, lsl #3]
    // 0x15ee650: blr             lr
    // 0x15ee654: mov             x1, x0
    // 0x15ee658: ldur            x0, [fp, #-8]
    // 0x15ee65c: stur            x1, [fp, #-0x10]
    // 0x15ee660: r0 = ImageHeaders.forImages()
    //     0x15ee660: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0x15ee664: mov             x2, x0
    // 0x15ee668: ldur            x0, [fp, #-8]
    // 0x15ee66c: stur            x2, [fp, #-0x18]
    // 0x15ee670: LoadField: r1 = r0->field_f
    //     0x15ee670: ldur            w1, [x0, #0xf]
    // 0x15ee674: DecompressPointer r1
    //     0x15ee674: add             x1, x1, HEAP, lsl #32
    // 0x15ee678: r0 = controller()
    //     0x15ee678: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee67c: LoadField: r1 = r0->field_67
    //     0x15ee67c: ldur            w1, [x0, #0x67]
    // 0x15ee680: DecompressPointer r1
    //     0x15ee680: add             x1, x1, HEAP, lsl #32
    // 0x15ee684: r0 = value()
    //     0x15ee684: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee688: LoadField: r1 = r0->field_27
    //     0x15ee688: ldur            w1, [x0, #0x27]
    // 0x15ee68c: DecompressPointer r1
    //     0x15ee68c: add             x1, x1, HEAP, lsl #32
    // 0x15ee690: cmp             w1, NULL
    // 0x15ee694: b.ne            #0x15ee6a0
    // 0x15ee698: r2 = ""
    //     0x15ee698: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15ee69c: b               #0x15ee6a4
    // 0x15ee6a0: mov             x2, x1
    // 0x15ee6a4: ldur            x0, [fp, #-8]
    // 0x15ee6a8: ldur            x1, [fp, #-0x10]
    // 0x15ee6ac: stur            x2, [fp, #-0x20]
    // 0x15ee6b0: r0 = CachedNetworkImage()
    //     0x15ee6b0: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x15ee6b4: stur            x0, [fp, #-0x28]
    // 0x15ee6b8: ldur            x16, [fp, #-0x18]
    // 0x15ee6bc: r30 = Instance_BoxFit
    //     0x15ee6bc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15ee6c0: ldr             lr, [lr, #0xb18]
    // 0x15ee6c4: stp             lr, x16, [SP, #0x10]
    // 0x15ee6c8: r16 = 50.000000
    //     0x15ee6c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15ee6cc: ldr             x16, [x16, #0xa90]
    // 0x15ee6d0: r30 = 50.000000
    //     0x15ee6d0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2ea90] 50
    //     0x15ee6d4: ldr             lr, [lr, #0xa90]
    // 0x15ee6d8: stp             lr, x16, [SP]
    // 0x15ee6dc: mov             x1, x0
    // 0x15ee6e0: ldur            x2, [fp, #-0x20]
    // 0x15ee6e4: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x3, height, 0x4, httpHeaders, 0x2, width, 0x5, null]
    //     0x15ee6e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea98] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x3, "height", 0x4, "httpHeaders", 0x2, "width", 0x5, Null]
    //     0x15ee6e8: ldr             x4, [x4, #0xa98]
    // 0x15ee6ec: r0 = CachedNetworkImage()
    //     0x15ee6ec: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x15ee6f0: r0 = Visibility()
    //     0x15ee6f0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15ee6f4: mov             x2, x0
    // 0x15ee6f8: ldur            x0, [fp, #-0x28]
    // 0x15ee6fc: stur            x2, [fp, #-0x18]
    // 0x15ee700: StoreField: r2->field_b = r0
    //     0x15ee700: stur            w0, [x2, #0xb]
    // 0x15ee704: r0 = Instance_SizedBox
    //     0x15ee704: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15ee708: StoreField: r2->field_f = r0
    //     0x15ee708: stur            w0, [x2, #0xf]
    // 0x15ee70c: ldur            x1, [fp, #-0x10]
    // 0x15ee710: StoreField: r2->field_13 = r1
    //     0x15ee710: stur            w1, [x2, #0x13]
    // 0x15ee714: r3 = false
    //     0x15ee714: add             x3, NULL, #0x30  ; false
    // 0x15ee718: ArrayStore: r2[0] = r3  ; List_4
    //     0x15ee718: stur            w3, [x2, #0x17]
    // 0x15ee71c: StoreField: r2->field_1b = r3
    //     0x15ee71c: stur            w3, [x2, #0x1b]
    // 0x15ee720: StoreField: r2->field_1f = r3
    //     0x15ee720: stur            w3, [x2, #0x1f]
    // 0x15ee724: StoreField: r2->field_23 = r3
    //     0x15ee724: stur            w3, [x2, #0x23]
    // 0x15ee728: StoreField: r2->field_27 = r3
    //     0x15ee728: stur            w3, [x2, #0x27]
    // 0x15ee72c: StoreField: r2->field_2b = r3
    //     0x15ee72c: stur            w3, [x2, #0x2b]
    // 0x15ee730: ldur            x4, [fp, #-8]
    // 0x15ee734: LoadField: r1 = r4->field_f
    //     0x15ee734: ldur            w1, [x4, #0xf]
    // 0x15ee738: DecompressPointer r1
    //     0x15ee738: add             x1, x1, HEAP, lsl #32
    // 0x15ee73c: r0 = controller()
    //     0x15ee73c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee740: LoadField: r1 = r0->field_67
    //     0x15ee740: ldur            w1, [x0, #0x67]
    // 0x15ee744: DecompressPointer r1
    //     0x15ee744: add             x1, x1, HEAP, lsl #32
    // 0x15ee748: r0 = value()
    //     0x15ee748: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee74c: LoadField: r1 = r0->field_3f
    //     0x15ee74c: ldur            w1, [x0, #0x3f]
    // 0x15ee750: DecompressPointer r1
    //     0x15ee750: add             x1, x1, HEAP, lsl #32
    // 0x15ee754: cmp             w1, NULL
    // 0x15ee758: b.ne            #0x15ee764
    // 0x15ee75c: r0 = Null
    //     0x15ee75c: mov             x0, NULL
    // 0x15ee760: b               #0x15ee76c
    // 0x15ee764: LoadField: r0 = r1->field_f
    //     0x15ee764: ldur            w0, [x1, #0xf]
    // 0x15ee768: DecompressPointer r0
    //     0x15ee768: add             x0, x0, HEAP, lsl #32
    // 0x15ee76c: r1 = LoadClassIdInstr(r0)
    //     0x15ee76c: ldur            x1, [x0, #-1]
    //     0x15ee770: ubfx            x1, x1, #0xc, #0x14
    // 0x15ee774: r16 = "image_text"
    //     0x15ee774: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ea88] "image_text"
    //     0x15ee778: ldr             x16, [x16, #0xa88]
    // 0x15ee77c: stp             x16, x0, [SP]
    // 0x15ee780: mov             x0, x1
    // 0x15ee784: mov             lr, x0
    // 0x15ee788: ldr             lr, [x21, lr, lsl #3]
    // 0x15ee78c: blr             lr
    // 0x15ee790: tbnz            w0, #4, #0x15ee79c
    // 0x15ee794: r2 = true
    //     0x15ee794: add             x2, NULL, #0x20  ; true
    // 0x15ee798: b               #0x15ee7fc
    // 0x15ee79c: ldur            x0, [fp, #-8]
    // 0x15ee7a0: LoadField: r1 = r0->field_f
    //     0x15ee7a0: ldur            w1, [x0, #0xf]
    // 0x15ee7a4: DecompressPointer r1
    //     0x15ee7a4: add             x1, x1, HEAP, lsl #32
    // 0x15ee7a8: r0 = controller()
    //     0x15ee7a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee7ac: LoadField: r1 = r0->field_67
    //     0x15ee7ac: ldur            w1, [x0, #0x67]
    // 0x15ee7b0: DecompressPointer r1
    //     0x15ee7b0: add             x1, x1, HEAP, lsl #32
    // 0x15ee7b4: r0 = value()
    //     0x15ee7b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee7b8: LoadField: r1 = r0->field_3f
    //     0x15ee7b8: ldur            w1, [x0, #0x3f]
    // 0x15ee7bc: DecompressPointer r1
    //     0x15ee7bc: add             x1, x1, HEAP, lsl #32
    // 0x15ee7c0: cmp             w1, NULL
    // 0x15ee7c4: b.ne            #0x15ee7d0
    // 0x15ee7c8: r0 = Null
    //     0x15ee7c8: mov             x0, NULL
    // 0x15ee7cc: b               #0x15ee7d8
    // 0x15ee7d0: LoadField: r0 = r1->field_f
    //     0x15ee7d0: ldur            w0, [x1, #0xf]
    // 0x15ee7d4: DecompressPointer r0
    //     0x15ee7d4: add             x0, x0, HEAP, lsl #32
    // 0x15ee7d8: r1 = LoadClassIdInstr(r0)
    //     0x15ee7d8: ldur            x1, [x0, #-1]
    //     0x15ee7dc: ubfx            x1, x1, #0xc, #0x14
    // 0x15ee7e0: r16 = "text"
    //     0x15ee7e0: ldr             x16, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x15ee7e4: stp             x16, x0, [SP]
    // 0x15ee7e8: mov             x0, x1
    // 0x15ee7ec: mov             lr, x0
    // 0x15ee7f0: ldr             lr, [x21, lr, lsl #3]
    // 0x15ee7f4: blr             lr
    // 0x15ee7f8: mov             x2, x0
    // 0x15ee7fc: ldur            x0, [fp, #-8]
    // 0x15ee800: stur            x2, [fp, #-0x10]
    // 0x15ee804: LoadField: r1 = r0->field_f
    //     0x15ee804: ldur            w1, [x0, #0xf]
    // 0x15ee808: DecompressPointer r1
    //     0x15ee808: add             x1, x1, HEAP, lsl #32
    // 0x15ee80c: r0 = controller()
    //     0x15ee80c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee810: LoadField: r1 = r0->field_67
    //     0x15ee810: ldur            w1, [x0, #0x67]
    // 0x15ee814: DecompressPointer r1
    //     0x15ee814: add             x1, x1, HEAP, lsl #32
    // 0x15ee818: r0 = value()
    //     0x15ee818: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee81c: LoadField: r1 = r0->field_2b
    //     0x15ee81c: ldur            w1, [x0, #0x2b]
    // 0x15ee820: DecompressPointer r1
    //     0x15ee820: add             x1, x1, HEAP, lsl #32
    // 0x15ee824: cmp             w1, NULL
    // 0x15ee828: b.ne            #0x15ee834
    // 0x15ee82c: r0 = Null
    //     0x15ee82c: mov             x0, NULL
    // 0x15ee830: b               #0x15ee84c
    // 0x15ee834: LoadField: r0 = r1->field_7
    //     0x15ee834: ldur            w0, [x1, #7]
    // 0x15ee838: cbnz            w0, #0x15ee844
    // 0x15ee83c: r1 = false
    //     0x15ee83c: add             x1, NULL, #0x30  ; false
    // 0x15ee840: b               #0x15ee848
    // 0x15ee844: r1 = true
    //     0x15ee844: add             x1, NULL, #0x20  ; true
    // 0x15ee848: mov             x0, x1
    // 0x15ee84c: cmp             w0, NULL
    // 0x15ee850: b.eq            #0x15ee918
    // 0x15ee854: tbnz            w0, #4, #0x15ee918
    // 0x15ee858: ldur            x0, [fp, #-8]
    // 0x15ee85c: LoadField: r1 = r0->field_f
    //     0x15ee85c: ldur            w1, [x0, #0xf]
    // 0x15ee860: DecompressPointer r1
    //     0x15ee860: add             x1, x1, HEAP, lsl #32
    // 0x15ee864: r0 = controller()
    //     0x15ee864: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15ee868: LoadField: r1 = r0->field_67
    //     0x15ee868: ldur            w1, [x0, #0x67]
    // 0x15ee86c: DecompressPointer r1
    //     0x15ee86c: add             x1, x1, HEAP, lsl #32
    // 0x15ee870: r0 = value()
    //     0x15ee870: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15ee874: LoadField: r1 = r0->field_2b
    //     0x15ee874: ldur            w1, [x0, #0x2b]
    // 0x15ee878: DecompressPointer r1
    //     0x15ee878: add             x1, x1, HEAP, lsl #32
    // 0x15ee87c: cmp             w1, NULL
    // 0x15ee880: b.ne            #0x15ee88c
    // 0x15ee884: r2 = ""
    //     0x15ee884: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x15ee888: b               #0x15ee890
    // 0x15ee88c: mov             x2, x1
    // 0x15ee890: ldur            x0, [fp, #-8]
    // 0x15ee894: stur            x2, [fp, #-0x20]
    // 0x15ee898: LoadField: r1 = r0->field_13
    //     0x15ee898: ldur            w1, [x0, #0x13]
    // 0x15ee89c: DecompressPointer r1
    //     0x15ee89c: add             x1, x1, HEAP, lsl #32
    // 0x15ee8a0: r0 = of()
    //     0x15ee8a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15ee8a4: LoadField: r1 = r0->field_87
    //     0x15ee8a4: ldur            w1, [x0, #0x87]
    // 0x15ee8a8: DecompressPointer r1
    //     0x15ee8a8: add             x1, x1, HEAP, lsl #32
    // 0x15ee8ac: LoadField: r0 = r1->field_2b
    //     0x15ee8ac: ldur            w0, [x1, #0x2b]
    // 0x15ee8b0: DecompressPointer r0
    //     0x15ee8b0: add             x0, x0, HEAP, lsl #32
    // 0x15ee8b4: r16 = 16.000000
    //     0x15ee8b4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x15ee8b8: ldr             x16, [x16, #0x188]
    // 0x15ee8bc: r30 = Instance_Color
    //     0x15ee8bc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x15ee8c0: stp             lr, x16, [SP]
    // 0x15ee8c4: mov             x1, x0
    // 0x15ee8c8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x15ee8c8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x15ee8cc: ldr             x4, [x4, #0xaa0]
    // 0x15ee8d0: r0 = copyWith()
    //     0x15ee8d0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15ee8d4: stur            x0, [fp, #-8]
    // 0x15ee8d8: r0 = Text()
    //     0x15ee8d8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15ee8dc: mov             x1, x0
    // 0x15ee8e0: ldur            x0, [fp, #-0x20]
    // 0x15ee8e4: stur            x1, [fp, #-0x28]
    // 0x15ee8e8: StoreField: r1->field_b = r0
    //     0x15ee8e8: stur            w0, [x1, #0xb]
    // 0x15ee8ec: ldur            x0, [fp, #-8]
    // 0x15ee8f0: StoreField: r1->field_13 = r0
    //     0x15ee8f0: stur            w0, [x1, #0x13]
    // 0x15ee8f4: r0 = Padding()
    //     0x15ee8f4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15ee8f8: mov             x1, x0
    // 0x15ee8fc: r0 = Instance_EdgeInsets
    //     0x15ee8fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f078] Obj!EdgeInsets@d571a1
    //     0x15ee900: ldr             x0, [x0, #0x78]
    // 0x15ee904: StoreField: r1->field_f = r0
    //     0x15ee904: stur            w0, [x1, #0xf]
    // 0x15ee908: ldur            x0, [fp, #-0x28]
    // 0x15ee90c: StoreField: r1->field_b = r0
    //     0x15ee90c: stur            w0, [x1, #0xb]
    // 0x15ee910: mov             x2, x1
    // 0x15ee914: b               #0x15ee920
    // 0x15ee918: r2 = Instance_SizedBox
    //     0x15ee918: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f58] Obj!SizedBox@d67da1
    //     0x15ee91c: ldr             x2, [x2, #0xf58]
    // 0x15ee920: ldur            x1, [fp, #-0x18]
    // 0x15ee924: ldur            x0, [fp, #-0x10]
    // 0x15ee928: stur            x2, [fp, #-8]
    // 0x15ee92c: r0 = Visibility()
    //     0x15ee92c: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15ee930: mov             x3, x0
    // 0x15ee934: ldur            x0, [fp, #-8]
    // 0x15ee938: stur            x3, [fp, #-0x20]
    // 0x15ee93c: StoreField: r3->field_b = r0
    //     0x15ee93c: stur            w0, [x3, #0xb]
    // 0x15ee940: r0 = Instance_SizedBox
    //     0x15ee940: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15ee944: StoreField: r3->field_f = r0
    //     0x15ee944: stur            w0, [x3, #0xf]
    // 0x15ee948: ldur            x0, [fp, #-0x10]
    // 0x15ee94c: StoreField: r3->field_13 = r0
    //     0x15ee94c: stur            w0, [x3, #0x13]
    // 0x15ee950: r0 = false
    //     0x15ee950: add             x0, NULL, #0x30  ; false
    // 0x15ee954: ArrayStore: r3[0] = r0  ; List_4
    //     0x15ee954: stur            w0, [x3, #0x17]
    // 0x15ee958: StoreField: r3->field_1b = r0
    //     0x15ee958: stur            w0, [x3, #0x1b]
    // 0x15ee95c: StoreField: r3->field_1f = r0
    //     0x15ee95c: stur            w0, [x3, #0x1f]
    // 0x15ee960: StoreField: r3->field_23 = r0
    //     0x15ee960: stur            w0, [x3, #0x23]
    // 0x15ee964: StoreField: r3->field_27 = r0
    //     0x15ee964: stur            w0, [x3, #0x27]
    // 0x15ee968: StoreField: r3->field_2b = r0
    //     0x15ee968: stur            w0, [x3, #0x2b]
    // 0x15ee96c: r1 = Null
    //     0x15ee96c: mov             x1, NULL
    // 0x15ee970: r2 = 4
    //     0x15ee970: movz            x2, #0x4
    // 0x15ee974: r0 = AllocateArray()
    //     0x15ee974: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15ee978: mov             x2, x0
    // 0x15ee97c: ldur            x0, [fp, #-0x18]
    // 0x15ee980: stur            x2, [fp, #-8]
    // 0x15ee984: StoreField: r2->field_f = r0
    //     0x15ee984: stur            w0, [x2, #0xf]
    // 0x15ee988: ldur            x0, [fp, #-0x20]
    // 0x15ee98c: StoreField: r2->field_13 = r0
    //     0x15ee98c: stur            w0, [x2, #0x13]
    // 0x15ee990: r1 = <Widget>
    //     0x15ee990: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15ee994: r0 = AllocateGrowableArray()
    //     0x15ee994: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15ee998: mov             x1, x0
    // 0x15ee99c: ldur            x0, [fp, #-8]
    // 0x15ee9a0: stur            x1, [fp, #-0x10]
    // 0x15ee9a4: StoreField: r1->field_f = r0
    //     0x15ee9a4: stur            w0, [x1, #0xf]
    // 0x15ee9a8: r0 = 4
    //     0x15ee9a8: movz            x0, #0x4
    // 0x15ee9ac: StoreField: r1->field_b = r0
    //     0x15ee9ac: stur            w0, [x1, #0xb]
    // 0x15ee9b0: r0 = Row()
    //     0x15ee9b0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x15ee9b4: r1 = Instance_Axis
    //     0x15ee9b4: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x15ee9b8: StoreField: r0->field_f = r1
    //     0x15ee9b8: stur            w1, [x0, #0xf]
    // 0x15ee9bc: r1 = Instance_MainAxisAlignment
    //     0x15ee9bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0x15ee9c0: ldr             x1, [x1, #0xab0]
    // 0x15ee9c4: StoreField: r0->field_13 = r1
    //     0x15ee9c4: stur            w1, [x0, #0x13]
    // 0x15ee9c8: r1 = Instance_MainAxisSize
    //     0x15ee9c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x15ee9cc: ldr             x1, [x1, #0xa10]
    // 0x15ee9d0: ArrayStore: r0[0] = r1  ; List_4
    //     0x15ee9d0: stur            w1, [x0, #0x17]
    // 0x15ee9d4: r1 = Instance_CrossAxisAlignment
    //     0x15ee9d4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x15ee9d8: ldr             x1, [x1, #0xa18]
    // 0x15ee9dc: StoreField: r0->field_1b = r1
    //     0x15ee9dc: stur            w1, [x0, #0x1b]
    // 0x15ee9e0: r1 = Instance_VerticalDirection
    //     0x15ee9e0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x15ee9e4: ldr             x1, [x1, #0xa20]
    // 0x15ee9e8: StoreField: r0->field_23 = r1
    //     0x15ee9e8: stur            w1, [x0, #0x23]
    // 0x15ee9ec: r1 = Instance_Clip
    //     0x15ee9ec: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x15ee9f0: ldr             x1, [x1, #0x38]
    // 0x15ee9f4: StoreField: r0->field_2b = r1
    //     0x15ee9f4: stur            w1, [x0, #0x2b]
    // 0x15ee9f8: StoreField: r0->field_2f = rZR
    //     0x15ee9f8: stur            xzr, [x0, #0x2f]
    // 0x15ee9fc: ldur            x1, [fp, #-0x10]
    // 0x15eea00: StoreField: r0->field_b = r1
    //     0x15eea00: stur            w1, [x0, #0xb]
    // 0x15eea04: LeaveFrame
    //     0x15eea04: mov             SP, fp
    //     0x15eea08: ldp             fp, lr, [SP], #0x10
    // 0x15eea0c: ret
    //     0x15eea0c: ret             
    // 0x15eea10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15eea10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15eea14: b               #0x15ee590
  }
}
