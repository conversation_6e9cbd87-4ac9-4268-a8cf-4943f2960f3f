// lib: , url: package:customer_app/app/presentation/views/glass/customization/customization_single_select.dart

// class id: 1049387, size: 0x8
class :: {
}

// class id: 3345, size: 0x20, field offset: 0x14
class _CustomizationSingleSelectState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xb59c50, size: 0x308
    // 0xb59c50: EnterFrame
    //     0xb59c50: stp             fp, lr, [SP, #-0x10]!
    //     0xb59c54: mov             fp, SP
    // 0xb59c58: AllocStack(0x40)
    //     0xb59c58: sub             SP, SP, #0x40
    // 0xb59c5c: SetupParameters(_CustomizationSingleSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb59c5c: mov             x0, x1
    //     0xb59c60: stur            x1, [fp, #-8]
    //     0xb59c64: mov             x1, x2
    //     0xb59c68: stur            x2, [fp, #-0x10]
    // 0xb59c6c: CheckStackOverflow
    //     0xb59c6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb59c70: cmp             SP, x16
    //     0xb59c74: b.ls            #0xb59f48
    // 0xb59c78: r1 = 1
    //     0xb59c78: movz            x1, #0x1
    // 0xb59c7c: r0 = AllocateContext()
    //     0xb59c7c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb59c80: mov             x3, x0
    // 0xb59c84: ldur            x0, [fp, #-8]
    // 0xb59c88: stur            x3, [fp, #-0x20]
    // 0xb59c8c: StoreField: r3->field_f = r0
    //     0xb59c8c: stur            w0, [x3, #0xf]
    // 0xb59c90: LoadField: r1 = r0->field_b
    //     0xb59c90: ldur            w1, [x0, #0xb]
    // 0xb59c94: DecompressPointer r1
    //     0xb59c94: add             x1, x1, HEAP, lsl #32
    // 0xb59c98: cmp             w1, NULL
    // 0xb59c9c: b.eq            #0xb59f50
    // 0xb59ca0: LoadField: r2 = r1->field_b
    //     0xb59ca0: ldur            w2, [x1, #0xb]
    // 0xb59ca4: DecompressPointer r2
    //     0xb59ca4: add             x2, x2, HEAP, lsl #32
    // 0xb59ca8: cmp             w2, NULL
    // 0xb59cac: b.ne            #0xb59cb8
    // 0xb59cb0: r1 = Null
    //     0xb59cb0: mov             x1, NULL
    // 0xb59cb4: b               #0xb59cc0
    // 0xb59cb8: LoadField: r1 = r2->field_2b
    //     0xb59cb8: ldur            w1, [x2, #0x2b]
    // 0xb59cbc: DecompressPointer r1
    //     0xb59cbc: add             x1, x1, HEAP, lsl #32
    // 0xb59cc0: cmp             w1, NULL
    // 0xb59cc4: b.eq            #0xb59d20
    // 0xb59cc8: tbnz            w1, #4, #0xb59d20
    // 0xb59ccc: cmp             w2, NULL
    // 0xb59cd0: b.ne            #0xb59cdc
    // 0xb59cd4: r4 = Null
    //     0xb59cd4: mov             x4, NULL
    // 0xb59cd8: b               #0xb59ce8
    // 0xb59cdc: LoadField: r1 = r2->field_1b
    //     0xb59cdc: ldur            w1, [x2, #0x1b]
    // 0xb59ce0: DecompressPointer r1
    //     0xb59ce0: add             x1, x1, HEAP, lsl #32
    // 0xb59ce4: mov             x4, x1
    // 0xb59ce8: stur            x4, [fp, #-0x18]
    // 0xb59cec: r1 = Null
    //     0xb59cec: mov             x1, NULL
    // 0xb59cf0: r2 = 4
    //     0xb59cf0: movz            x2, #0x4
    // 0xb59cf4: r0 = AllocateArray()
    //     0xb59cf4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb59cf8: mov             x1, x0
    // 0xb59cfc: ldur            x0, [fp, #-0x18]
    // 0xb59d00: StoreField: r1->field_f = r0
    //     0xb59d00: stur            w0, [x1, #0xf]
    // 0xb59d04: r16 = " *"
    //     0xb59d04: add             x16, PP, #0x33, lsl #12  ; [pp+0x33fc8] " *"
    //     0xb59d08: ldr             x16, [x16, #0xfc8]
    // 0xb59d0c: StoreField: r1->field_13 = r16
    //     0xb59d0c: stur            w16, [x1, #0x13]
    // 0xb59d10: str             x1, [SP]
    // 0xb59d14: r0 = _interpolate()
    //     0xb59d14: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb59d18: mov             x2, x0
    // 0xb59d1c: b               #0xb59d44
    // 0xb59d20: cmp             w2, NULL
    // 0xb59d24: b.ne            #0xb59d30
    // 0xb59d28: r0 = Null
    //     0xb59d28: mov             x0, NULL
    // 0xb59d2c: b               #0xb59d38
    // 0xb59d30: LoadField: r0 = r2->field_1b
    //     0xb59d30: ldur            w0, [x2, #0x1b]
    // 0xb59d34: DecompressPointer r0
    //     0xb59d34: add             x0, x0, HEAP, lsl #32
    // 0xb59d38: str             x0, [SP]
    // 0xb59d3c: r0 = _interpolateSingle()
    //     0xb59d3c: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb59d40: mov             x2, x0
    // 0xb59d44: ldur            x0, [fp, #-8]
    // 0xb59d48: ldur            x1, [fp, #-0x10]
    // 0xb59d4c: stur            x2, [fp, #-0x18]
    // 0xb59d50: r0 = of()
    //     0xb59d50: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb59d54: LoadField: r1 = r0->field_87
    //     0xb59d54: ldur            w1, [x0, #0x87]
    // 0xb59d58: DecompressPointer r1
    //     0xb59d58: add             x1, x1, HEAP, lsl #32
    // 0xb59d5c: LoadField: r0 = r1->field_7
    //     0xb59d5c: ldur            w0, [x1, #7]
    // 0xb59d60: DecompressPointer r0
    //     0xb59d60: add             x0, x0, HEAP, lsl #32
    // 0xb59d64: r16 = Instance_Color
    //     0xb59d64: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb59d68: r30 = 14.000000
    //     0xb59d68: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb59d6c: ldr             lr, [lr, #0x1d8]
    // 0xb59d70: stp             lr, x16, [SP]
    // 0xb59d74: mov             x1, x0
    // 0xb59d78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb59d78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb59d7c: ldr             x4, [x4, #0x9b8]
    // 0xb59d80: r0 = copyWith()
    //     0xb59d80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb59d84: stur            x0, [fp, #-0x10]
    // 0xb59d88: r0 = Text()
    //     0xb59d88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb59d8c: mov             x1, x0
    // 0xb59d90: ldur            x0, [fp, #-0x18]
    // 0xb59d94: stur            x1, [fp, #-0x28]
    // 0xb59d98: StoreField: r1->field_b = r0
    //     0xb59d98: stur            w0, [x1, #0xb]
    // 0xb59d9c: ldur            x0, [fp, #-0x10]
    // 0xb59da0: StoreField: r1->field_13 = r0
    //     0xb59da0: stur            w0, [x1, #0x13]
    // 0xb59da4: r0 = Padding()
    //     0xb59da4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb59da8: mov             x3, x0
    // 0xb59dac: r0 = Instance_EdgeInsets
    //     0xb59dac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb59db0: ldr             x0, [x0, #0x1f0]
    // 0xb59db4: stur            x3, [fp, #-0x10]
    // 0xb59db8: StoreField: r3->field_f = r0
    //     0xb59db8: stur            w0, [x3, #0xf]
    // 0xb59dbc: ldur            x1, [fp, #-0x28]
    // 0xb59dc0: StoreField: r3->field_b = r1
    //     0xb59dc0: stur            w1, [x3, #0xb]
    // 0xb59dc4: ldur            x1, [fp, #-8]
    // 0xb59dc8: LoadField: r2 = r1->field_b
    //     0xb59dc8: ldur            w2, [x1, #0xb]
    // 0xb59dcc: DecompressPointer r2
    //     0xb59dcc: add             x2, x2, HEAP, lsl #32
    // 0xb59dd0: cmp             w2, NULL
    // 0xb59dd4: b.eq            #0xb59f54
    // 0xb59dd8: LoadField: r1 = r2->field_b
    //     0xb59dd8: ldur            w1, [x2, #0xb]
    // 0xb59ddc: DecompressPointer r1
    //     0xb59ddc: add             x1, x1, HEAP, lsl #32
    // 0xb59de0: cmp             w1, NULL
    // 0xb59de4: b.ne            #0xb59df0
    // 0xb59de8: r1 = Null
    //     0xb59de8: mov             x1, NULL
    // 0xb59dec: b               #0xb59e0c
    // 0xb59df0: LoadField: r2 = r1->field_2f
    //     0xb59df0: ldur            w2, [x1, #0x2f]
    // 0xb59df4: DecompressPointer r2
    //     0xb59df4: add             x2, x2, HEAP, lsl #32
    // 0xb59df8: cmp             w2, NULL
    // 0xb59dfc: b.ne            #0xb59e08
    // 0xb59e00: r1 = Null
    //     0xb59e00: mov             x1, NULL
    // 0xb59e04: b               #0xb59e0c
    // 0xb59e08: LoadField: r1 = r2->field_b
    //     0xb59e08: ldur            w1, [x2, #0xb]
    // 0xb59e0c: cmp             w1, NULL
    // 0xb59e10: b.ne            #0xb59e1c
    // 0xb59e14: r4 = 0
    //     0xb59e14: movz            x4, #0
    // 0xb59e18: b               #0xb59e24
    // 0xb59e1c: r2 = LoadInt32Instr(r1)
    //     0xb59e1c: sbfx            x2, x1, #1, #0x1f
    // 0xb59e20: mov             x4, x2
    // 0xb59e24: ldur            x2, [fp, #-0x20]
    // 0xb59e28: stur            x4, [fp, #-0x30]
    // 0xb59e2c: r1 = Function '<anonymous closure>':.
    //     0xb59e2c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a8e8] AnonymousClosure: (0xb59f78), in [package:customer_app/app/presentation/views/glass/customization/customization_single_select.dart] _CustomizationSingleSelectState::build (0xb59c50)
    //     0xb59e30: ldr             x1, [x1, #0x8e8]
    // 0xb59e34: r0 = AllocateClosure()
    //     0xb59e34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb59e38: r1 = Function '<anonymous closure>':.
    //     0xb59e38: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a8f0] AnonymousClosure: (0x9bccb4), in [package:customer_app/app/presentation/views/line/post_order/order_success/order_success_widget.dart] OrderSuccessWidget::body (0x1506c94)
    //     0xb59e3c: ldr             x1, [x1, #0x8f0]
    // 0xb59e40: r2 = Null
    //     0xb59e40: mov             x2, NULL
    // 0xb59e44: stur            x0, [fp, #-8]
    // 0xb59e48: r0 = AllocateClosure()
    //     0xb59e48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb59e4c: stur            x0, [fp, #-0x18]
    // 0xb59e50: r0 = ListView()
    //     0xb59e50: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xb59e54: stur            x0, [fp, #-0x20]
    // 0xb59e58: r16 = Instance_NeverScrollableScrollPhysics
    //     0xb59e58: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xb59e5c: ldr             x16, [x16, #0x1c8]
    // 0xb59e60: r30 = true
    //     0xb59e60: add             lr, NULL, #0x20  ; true
    // 0xb59e64: stp             lr, x16, [SP]
    // 0xb59e68: mov             x1, x0
    // 0xb59e6c: ldur            x2, [fp, #-8]
    // 0xb59e70: ldur            x3, [fp, #-0x30]
    // 0xb59e74: ldur            x5, [fp, #-0x18]
    // 0xb59e78: r4 = const [0, 0x6, 0x2, 0x4, physics, 0x4, shrinkWrap, 0x5, null]
    //     0xb59e78: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f968] List(9) [0, 0x6, 0x2, 0x4, "physics", 0x4, "shrinkWrap", 0x5, Null]
    //     0xb59e7c: ldr             x4, [x4, #0x968]
    // 0xb59e80: r0 = ListView.separated()
    //     0xb59e80: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xb59e84: r0 = Padding()
    //     0xb59e84: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb59e88: mov             x3, x0
    // 0xb59e8c: r0 = Instance_EdgeInsets
    //     0xb59e8c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xb59e90: ldr             x0, [x0, #0x1f0]
    // 0xb59e94: stur            x3, [fp, #-8]
    // 0xb59e98: StoreField: r3->field_f = r0
    //     0xb59e98: stur            w0, [x3, #0xf]
    // 0xb59e9c: ldur            x0, [fp, #-0x20]
    // 0xb59ea0: StoreField: r3->field_b = r0
    //     0xb59ea0: stur            w0, [x3, #0xb]
    // 0xb59ea4: r1 = Null
    //     0xb59ea4: mov             x1, NULL
    // 0xb59ea8: r2 = 4
    //     0xb59ea8: movz            x2, #0x4
    // 0xb59eac: r0 = AllocateArray()
    //     0xb59eac: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb59eb0: mov             x2, x0
    // 0xb59eb4: ldur            x0, [fp, #-0x10]
    // 0xb59eb8: stur            x2, [fp, #-0x18]
    // 0xb59ebc: StoreField: r2->field_f = r0
    //     0xb59ebc: stur            w0, [x2, #0xf]
    // 0xb59ec0: ldur            x0, [fp, #-8]
    // 0xb59ec4: StoreField: r2->field_13 = r0
    //     0xb59ec4: stur            w0, [x2, #0x13]
    // 0xb59ec8: r1 = <Widget>
    //     0xb59ec8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb59ecc: r0 = AllocateGrowableArray()
    //     0xb59ecc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb59ed0: mov             x1, x0
    // 0xb59ed4: ldur            x0, [fp, #-0x18]
    // 0xb59ed8: stur            x1, [fp, #-8]
    // 0xb59edc: StoreField: r1->field_f = r0
    //     0xb59edc: stur            w0, [x1, #0xf]
    // 0xb59ee0: r0 = 4
    //     0xb59ee0: movz            x0, #0x4
    // 0xb59ee4: StoreField: r1->field_b = r0
    //     0xb59ee4: stur            w0, [x1, #0xb]
    // 0xb59ee8: r0 = Column()
    //     0xb59ee8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb59eec: r1 = Instance_Axis
    //     0xb59eec: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb59ef0: StoreField: r0->field_f = r1
    //     0xb59ef0: stur            w1, [x0, #0xf]
    // 0xb59ef4: r1 = Instance_MainAxisAlignment
    //     0xb59ef4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb59ef8: ldr             x1, [x1, #0xa08]
    // 0xb59efc: StoreField: r0->field_13 = r1
    //     0xb59efc: stur            w1, [x0, #0x13]
    // 0xb59f00: r1 = Instance_MainAxisSize
    //     0xb59f00: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb59f04: ldr             x1, [x1, #0xa10]
    // 0xb59f08: ArrayStore: r0[0] = r1  ; List_4
    //     0xb59f08: stur            w1, [x0, #0x17]
    // 0xb59f0c: r1 = Instance_CrossAxisAlignment
    //     0xb59f0c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb59f10: ldr             x1, [x1, #0x890]
    // 0xb59f14: StoreField: r0->field_1b = r1
    //     0xb59f14: stur            w1, [x0, #0x1b]
    // 0xb59f18: r1 = Instance_VerticalDirection
    //     0xb59f18: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb59f1c: ldr             x1, [x1, #0xa20]
    // 0xb59f20: StoreField: r0->field_23 = r1
    //     0xb59f20: stur            w1, [x0, #0x23]
    // 0xb59f24: r1 = Instance_Clip
    //     0xb59f24: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb59f28: ldr             x1, [x1, #0x38]
    // 0xb59f2c: StoreField: r0->field_2b = r1
    //     0xb59f2c: stur            w1, [x0, #0x2b]
    // 0xb59f30: StoreField: r0->field_2f = rZR
    //     0xb59f30: stur            xzr, [x0, #0x2f]
    // 0xb59f34: ldur            x1, [fp, #-8]
    // 0xb59f38: StoreField: r0->field_b = r1
    //     0xb59f38: stur            w1, [x0, #0xb]
    // 0xb59f3c: LeaveFrame
    //     0xb59f3c: mov             SP, fp
    //     0xb59f40: ldp             fp, lr, [SP], #0x10
    // 0xb59f44: ret
    //     0xb59f44: ret             
    // 0xb59f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb59f48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb59f4c: b               #0xb59c78
    // 0xb59f50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59f50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb59f54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb59f54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb59f78, size: 0x478
    // 0xb59f78: EnterFrame
    //     0xb59f78: stp             fp, lr, [SP, #-0x10]!
    //     0xb59f7c: mov             fp, SP
    // 0xb59f80: AllocStack(0x48)
    //     0xb59f80: sub             SP, SP, #0x48
    // 0xb59f84: SetupParameters()
    //     0xb59f84: ldr             x0, [fp, #0x20]
    //     0xb59f88: ldur            w1, [x0, #0x17]
    //     0xb59f8c: add             x1, x1, HEAP, lsl #32
    //     0xb59f90: stur            x1, [fp, #-8]
    // 0xb59f94: CheckStackOverflow
    //     0xb59f94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb59f98: cmp             SP, x16
    //     0xb59f9c: b.ls            #0xb5a3d4
    // 0xb59fa0: r0 = Radius()
    //     0xb59fa0: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb59fa4: d0 = 30.000000
    //     0xb59fa4: fmov            d0, #30.00000000
    // 0xb59fa8: stur            x0, [fp, #-0x10]
    // 0xb59fac: StoreField: r0->field_7 = d0
    //     0xb59fac: stur            d0, [x0, #7]
    // 0xb59fb0: StoreField: r0->field_f = d0
    //     0xb59fb0: stur            d0, [x0, #0xf]
    // 0xb59fb4: r0 = BorderRadius()
    //     0xb59fb4: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb59fb8: mov             x2, x0
    // 0xb59fbc: ldur            x0, [fp, #-0x10]
    // 0xb59fc0: stur            x2, [fp, #-0x18]
    // 0xb59fc4: StoreField: r2->field_7 = r0
    //     0xb59fc4: stur            w0, [x2, #7]
    // 0xb59fc8: StoreField: r2->field_b = r0
    //     0xb59fc8: stur            w0, [x2, #0xb]
    // 0xb59fcc: StoreField: r2->field_f = r0
    //     0xb59fcc: stur            w0, [x2, #0xf]
    // 0xb59fd0: StoreField: r2->field_13 = r0
    //     0xb59fd0: stur            w0, [x2, #0x13]
    // 0xb59fd4: ldr             x1, [fp, #0x18]
    // 0xb59fd8: r0 = of()
    //     0xb59fd8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb59fdc: LoadField: r1 = r0->field_5b
    //     0xb59fdc: ldur            w1, [x0, #0x5b]
    // 0xb59fe0: DecompressPointer r1
    //     0xb59fe0: add             x1, x1, HEAP, lsl #32
    // 0xb59fe4: r0 = LoadClassIdInstr(r1)
    //     0xb59fe4: ldur            x0, [x1, #-1]
    //     0xb59fe8: ubfx            x0, x0, #0xc, #0x14
    // 0xb59fec: d0 = 0.070000
    //     0xb59fec: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xb59ff0: ldr             d0, [x17, #0x5f8]
    // 0xb59ff4: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb59ff4: sub             lr, x0, #0xffa
    //     0xb59ff8: ldr             lr, [x21, lr, lsl #3]
    //     0xb59ffc: blr             lr
    // 0xb5a000: mov             x2, x0
    // 0xb5a004: r1 = Null
    //     0xb5a004: mov             x1, NULL
    // 0xb5a008: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb5a008: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb5a00c: r0 = Border.all()
    //     0xb5a00c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xb5a010: stur            x0, [fp, #-0x10]
    // 0xb5a014: r0 = BoxDecoration()
    //     0xb5a014: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb5a018: mov             x4, x0
    // 0xb5a01c: ldur            x0, [fp, #-0x10]
    // 0xb5a020: stur            x4, [fp, #-0x28]
    // 0xb5a024: StoreField: r4->field_f = r0
    //     0xb5a024: stur            w0, [x4, #0xf]
    // 0xb5a028: ldur            x0, [fp, #-0x18]
    // 0xb5a02c: StoreField: r4->field_13 = r0
    //     0xb5a02c: stur            w0, [x4, #0x13]
    // 0xb5a030: r0 = Instance_BoxShape
    //     0xb5a030: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb5a034: ldr             x0, [x0, #0x80]
    // 0xb5a038: StoreField: r4->field_23 = r0
    //     0xb5a038: stur            w0, [x4, #0x23]
    // 0xb5a03c: ldur            x5, [fp, #-8]
    // 0xb5a040: LoadField: r2 = r5->field_f
    //     0xb5a040: ldur            w2, [x5, #0xf]
    // 0xb5a044: DecompressPointer r2
    //     0xb5a044: add             x2, x2, HEAP, lsl #32
    // 0xb5a048: LoadField: r0 = r2->field_b
    //     0xb5a048: ldur            w0, [x2, #0xb]
    // 0xb5a04c: DecompressPointer r0
    //     0xb5a04c: add             x0, x0, HEAP, lsl #32
    // 0xb5a050: cmp             w0, NULL
    // 0xb5a054: b.eq            #0xb5a3dc
    // 0xb5a058: LoadField: r1 = r0->field_b
    //     0xb5a058: ldur            w1, [x0, #0xb]
    // 0xb5a05c: DecompressPointer r1
    //     0xb5a05c: add             x1, x1, HEAP, lsl #32
    // 0xb5a060: cmp             w1, NULL
    // 0xb5a064: b.ne            #0xb5a074
    // 0xb5a068: ldr             x6, [fp, #0x10]
    // 0xb5a06c: r0 = Null
    //     0xb5a06c: mov             x0, NULL
    // 0xb5a070: b               #0xb5a0d0
    // 0xb5a074: LoadField: r3 = r1->field_2f
    //     0xb5a074: ldur            w3, [x1, #0x2f]
    // 0xb5a078: DecompressPointer r3
    //     0xb5a078: add             x3, x3, HEAP, lsl #32
    // 0xb5a07c: cmp             w3, NULL
    // 0xb5a080: b.ne            #0xb5a090
    // 0xb5a084: ldr             x6, [fp, #0x10]
    // 0xb5a088: r0 = Null
    //     0xb5a088: mov             x0, NULL
    // 0xb5a08c: b               #0xb5a0d0
    // 0xb5a090: ldr             x6, [fp, #0x10]
    // 0xb5a094: LoadField: r0 = r3->field_b
    //     0xb5a094: ldur            w0, [x3, #0xb]
    // 0xb5a098: r7 = LoadInt32Instr(r6)
    //     0xb5a098: sbfx            x7, x6, #1, #0x1f
    //     0xb5a09c: tbz             w6, #0, #0xb5a0a4
    //     0xb5a0a0: ldur            x7, [x6, #7]
    // 0xb5a0a4: r1 = LoadInt32Instr(r0)
    //     0xb5a0a4: sbfx            x1, x0, #1, #0x1f
    // 0xb5a0a8: mov             x0, x1
    // 0xb5a0ac: mov             x1, x7
    // 0xb5a0b0: cmp             x1, x0
    // 0xb5a0b4: b.hs            #0xb5a3e0
    // 0xb5a0b8: LoadField: r0 = r3->field_f
    //     0xb5a0b8: ldur            w0, [x3, #0xf]
    // 0xb5a0bc: DecompressPointer r0
    //     0xb5a0bc: add             x0, x0, HEAP, lsl #32
    // 0xb5a0c0: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xb5a0c0: add             x16, x0, x7, lsl #2
    //     0xb5a0c4: ldur            w1, [x16, #0xf]
    // 0xb5a0c8: DecompressPointer r1
    //     0xb5a0c8: add             x1, x1, HEAP, lsl #32
    // 0xb5a0cc: mov             x0, x1
    // 0xb5a0d0: r7 = LoadInt32Instr(r6)
    //     0xb5a0d0: sbfx            x7, x6, #1, #0x1f
    //     0xb5a0d4: tbz             w6, #0, #0xb5a0dc
    //     0xb5a0d8: ldur            x7, [x6, #7]
    // 0xb5a0dc: mov             x1, x2
    // 0xb5a0e0: mov             x2, x0
    // 0xb5a0e4: mov             x3, x7
    // 0xb5a0e8: stur            x7, [fp, #-0x20]
    // 0xb5a0ec: r0 = _productBuildItem()
    //     0xb5a0ec: bl              #0xb5a3f0  ; [package:customer_app/app/presentation/views/glass/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem
    // 0xb5a0f0: mov             x3, x0
    // 0xb5a0f4: ldur            x0, [fp, #-8]
    // 0xb5a0f8: stur            x3, [fp, #-0x18]
    // 0xb5a0fc: LoadField: r1 = r0->field_f
    //     0xb5a0fc: ldur            w1, [x0, #0xf]
    // 0xb5a100: DecompressPointer r1
    //     0xb5a100: add             x1, x1, HEAP, lsl #32
    // 0xb5a104: LoadField: r0 = r1->field_b
    //     0xb5a104: ldur            w0, [x1, #0xb]
    // 0xb5a108: DecompressPointer r0
    //     0xb5a108: add             x0, x0, HEAP, lsl #32
    // 0xb5a10c: cmp             w0, NULL
    // 0xb5a110: b.eq            #0xb5a3e4
    // 0xb5a114: LoadField: r4 = r0->field_b
    //     0xb5a114: ldur            w4, [x0, #0xb]
    // 0xb5a118: DecompressPointer r4
    //     0xb5a118: add             x4, x4, HEAP, lsl #32
    // 0xb5a11c: stur            x4, [fp, #-0x10]
    // 0xb5a120: cmp             w4, NULL
    // 0xb5a124: b.ne            #0xb5a134
    // 0xb5a128: ldur            x5, [fp, #-0x20]
    // 0xb5a12c: r0 = Null
    //     0xb5a12c: mov             x0, NULL
    // 0xb5a130: b               #0xb5a188
    // 0xb5a134: LoadField: r2 = r4->field_2f
    //     0xb5a134: ldur            w2, [x4, #0x2f]
    // 0xb5a138: DecompressPointer r2
    //     0xb5a138: add             x2, x2, HEAP, lsl #32
    // 0xb5a13c: cmp             w2, NULL
    // 0xb5a140: b.ne            #0xb5a150
    // 0xb5a144: ldur            x5, [fp, #-0x20]
    // 0xb5a148: r0 = Null
    //     0xb5a148: mov             x0, NULL
    // 0xb5a14c: b               #0xb5a188
    // 0xb5a150: ldur            x5, [fp, #-0x20]
    // 0xb5a154: LoadField: r0 = r2->field_b
    //     0xb5a154: ldur            w0, [x2, #0xb]
    // 0xb5a158: r1 = LoadInt32Instr(r0)
    //     0xb5a158: sbfx            x1, x0, #1, #0x1f
    // 0xb5a15c: mov             x0, x1
    // 0xb5a160: mov             x1, x5
    // 0xb5a164: cmp             x1, x0
    // 0xb5a168: b.hs            #0xb5a3e8
    // 0xb5a16c: LoadField: r0 = r2->field_f
    //     0xb5a16c: ldur            w0, [x2, #0xf]
    // 0xb5a170: DecompressPointer r0
    //     0xb5a170: add             x0, x0, HEAP, lsl #32
    // 0xb5a174: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5a174: add             x16, x0, x5, lsl #2
    //     0xb5a178: ldur            w1, [x16, #0xf]
    // 0xb5a17c: DecompressPointer r1
    //     0xb5a17c: add             x1, x1, HEAP, lsl #32
    // 0xb5a180: LoadField: r0 = r1->field_13
    //     0xb5a180: ldur            w0, [x1, #0x13]
    // 0xb5a184: DecompressPointer r0
    //     0xb5a184: add             x0, x0, HEAP, lsl #32
    // 0xb5a188: cbnz            w0, #0xb5a194
    // 0xb5a18c: r6 = false
    //     0xb5a18c: add             x6, NULL, #0x30  ; false
    // 0xb5a190: b               #0xb5a198
    // 0xb5a194: r6 = true
    //     0xb5a194: add             x6, NULL, #0x20  ; true
    // 0xb5a198: stur            x6, [fp, #-8]
    // 0xb5a19c: r1 = Null
    //     0xb5a19c: mov             x1, NULL
    // 0xb5a1a0: r2 = 4
    //     0xb5a1a0: movz            x2, #0x4
    // 0xb5a1a4: r0 = AllocateArray()
    //     0xb5a1a4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5a1a8: mov             x2, x0
    // 0xb5a1ac: r16 = "+ "
    //     0xb5a1ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xb5a1b0: ldr             x16, [x16, #0xc30]
    // 0xb5a1b4: StoreField: r2->field_f = r16
    //     0xb5a1b4: stur            w16, [x2, #0xf]
    // 0xb5a1b8: ldur            x0, [fp, #-0x10]
    // 0xb5a1bc: cmp             w0, NULL
    // 0xb5a1c0: b.ne            #0xb5a1cc
    // 0xb5a1c4: r3 = Null
    //     0xb5a1c4: mov             x3, NULL
    // 0xb5a1c8: b               #0xb5a220
    // 0xb5a1cc: LoadField: r3 = r0->field_2f
    //     0xb5a1cc: ldur            w3, [x0, #0x2f]
    // 0xb5a1d0: DecompressPointer r3
    //     0xb5a1d0: add             x3, x3, HEAP, lsl #32
    // 0xb5a1d4: cmp             w3, NULL
    // 0xb5a1d8: b.ne            #0xb5a1e4
    // 0xb5a1dc: r0 = Null
    //     0xb5a1dc: mov             x0, NULL
    // 0xb5a1e0: b               #0xb5a21c
    // 0xb5a1e4: ldur            x4, [fp, #-0x20]
    // 0xb5a1e8: LoadField: r0 = r3->field_b
    //     0xb5a1e8: ldur            w0, [x3, #0xb]
    // 0xb5a1ec: r1 = LoadInt32Instr(r0)
    //     0xb5a1ec: sbfx            x1, x0, #1, #0x1f
    // 0xb5a1f0: mov             x0, x1
    // 0xb5a1f4: mov             x1, x4
    // 0xb5a1f8: cmp             x1, x0
    // 0xb5a1fc: b.hs            #0xb5a3ec
    // 0xb5a200: LoadField: r0 = r3->field_f
    //     0xb5a200: ldur            w0, [x3, #0xf]
    // 0xb5a204: DecompressPointer r0
    //     0xb5a204: add             x0, x0, HEAP, lsl #32
    // 0xb5a208: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb5a208: add             x16, x0, x4, lsl #2
    //     0xb5a20c: ldur            w1, [x16, #0xf]
    // 0xb5a210: DecompressPointer r1
    //     0xb5a210: add             x1, x1, HEAP, lsl #32
    // 0xb5a214: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xb5a214: ldur            w0, [x1, #0x17]
    // 0xb5a218: DecompressPointer r0
    //     0xb5a218: add             x0, x0, HEAP, lsl #32
    // 0xb5a21c: mov             x3, x0
    // 0xb5a220: ldur            x0, [fp, #-0x18]
    // 0xb5a224: ldur            x1, [fp, #-8]
    // 0xb5a228: StoreField: r2->field_13 = r3
    //     0xb5a228: stur            w3, [x2, #0x13]
    // 0xb5a22c: str             x2, [SP]
    // 0xb5a230: r0 = _interpolate()
    //     0xb5a230: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb5a234: ldr             x1, [fp, #0x18]
    // 0xb5a238: stur            x0, [fp, #-0x10]
    // 0xb5a23c: r0 = of()
    //     0xb5a23c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5a240: LoadField: r1 = r0->field_87
    //     0xb5a240: ldur            w1, [x0, #0x87]
    // 0xb5a244: DecompressPointer r1
    //     0xb5a244: add             x1, x1, HEAP, lsl #32
    // 0xb5a248: LoadField: r0 = r1->field_2b
    //     0xb5a248: ldur            w0, [x1, #0x2b]
    // 0xb5a24c: DecompressPointer r0
    //     0xb5a24c: add             x0, x0, HEAP, lsl #32
    // 0xb5a250: stur            x0, [fp, #-0x30]
    // 0xb5a254: r1 = Instance_Color
    //     0xb5a254: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb5a258: d0 = 0.700000
    //     0xb5a258: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb5a25c: ldr             d0, [x17, #0xf48]
    // 0xb5a260: r0 = withOpacity()
    //     0xb5a260: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb5a264: r16 = 14.000000
    //     0xb5a264: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5a268: ldr             x16, [x16, #0x1d8]
    // 0xb5a26c: stp             x16, x0, [SP]
    // 0xb5a270: ldur            x1, [fp, #-0x30]
    // 0xb5a274: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb5a274: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb5a278: ldr             x4, [x4, #0x9b8]
    // 0xb5a27c: r0 = copyWith()
    //     0xb5a27c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5a280: stur            x0, [fp, #-0x30]
    // 0xb5a284: r0 = Text()
    //     0xb5a284: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5a288: mov             x1, x0
    // 0xb5a28c: ldur            x0, [fp, #-0x10]
    // 0xb5a290: stur            x1, [fp, #-0x38]
    // 0xb5a294: StoreField: r1->field_b = r0
    //     0xb5a294: stur            w0, [x1, #0xb]
    // 0xb5a298: ldur            x0, [fp, #-0x30]
    // 0xb5a29c: StoreField: r1->field_13 = r0
    //     0xb5a29c: stur            w0, [x1, #0x13]
    // 0xb5a2a0: r0 = Visibility()
    //     0xb5a2a0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xb5a2a4: mov             x3, x0
    // 0xb5a2a8: ldur            x0, [fp, #-0x38]
    // 0xb5a2ac: stur            x3, [fp, #-0x10]
    // 0xb5a2b0: StoreField: r3->field_b = r0
    //     0xb5a2b0: stur            w0, [x3, #0xb]
    // 0xb5a2b4: r0 = Instance_SizedBox
    //     0xb5a2b4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb5a2b8: StoreField: r3->field_f = r0
    //     0xb5a2b8: stur            w0, [x3, #0xf]
    // 0xb5a2bc: ldur            x0, [fp, #-8]
    // 0xb5a2c0: StoreField: r3->field_13 = r0
    //     0xb5a2c0: stur            w0, [x3, #0x13]
    // 0xb5a2c4: r0 = false
    //     0xb5a2c4: add             x0, NULL, #0x30  ; false
    // 0xb5a2c8: ArrayStore: r3[0] = r0  ; List_4
    //     0xb5a2c8: stur            w0, [x3, #0x17]
    // 0xb5a2cc: StoreField: r3->field_1b = r0
    //     0xb5a2cc: stur            w0, [x3, #0x1b]
    // 0xb5a2d0: StoreField: r3->field_1f = r0
    //     0xb5a2d0: stur            w0, [x3, #0x1f]
    // 0xb5a2d4: StoreField: r3->field_23 = r0
    //     0xb5a2d4: stur            w0, [x3, #0x23]
    // 0xb5a2d8: StoreField: r3->field_27 = r0
    //     0xb5a2d8: stur            w0, [x3, #0x27]
    // 0xb5a2dc: StoreField: r3->field_2b = r0
    //     0xb5a2dc: stur            w0, [x3, #0x2b]
    // 0xb5a2e0: r1 = Null
    //     0xb5a2e0: mov             x1, NULL
    // 0xb5a2e4: r2 = 4
    //     0xb5a2e4: movz            x2, #0x4
    // 0xb5a2e8: r0 = AllocateArray()
    //     0xb5a2e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5a2ec: mov             x2, x0
    // 0xb5a2f0: ldur            x0, [fp, #-0x18]
    // 0xb5a2f4: stur            x2, [fp, #-8]
    // 0xb5a2f8: StoreField: r2->field_f = r0
    //     0xb5a2f8: stur            w0, [x2, #0xf]
    // 0xb5a2fc: ldur            x0, [fp, #-0x10]
    // 0xb5a300: StoreField: r2->field_13 = r0
    //     0xb5a300: stur            w0, [x2, #0x13]
    // 0xb5a304: r1 = <Widget>
    //     0xb5a304: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb5a308: r0 = AllocateGrowableArray()
    //     0xb5a308: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5a30c: mov             x1, x0
    // 0xb5a310: ldur            x0, [fp, #-8]
    // 0xb5a314: stur            x1, [fp, #-0x10]
    // 0xb5a318: StoreField: r1->field_f = r0
    //     0xb5a318: stur            w0, [x1, #0xf]
    // 0xb5a31c: r0 = 4
    //     0xb5a31c: movz            x0, #0x4
    // 0xb5a320: StoreField: r1->field_b = r0
    //     0xb5a320: stur            w0, [x1, #0xb]
    // 0xb5a324: r0 = Row()
    //     0xb5a324: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb5a328: mov             x1, x0
    // 0xb5a32c: r0 = Instance_Axis
    //     0xb5a32c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb5a330: stur            x1, [fp, #-8]
    // 0xb5a334: StoreField: r1->field_f = r0
    //     0xb5a334: stur            w0, [x1, #0xf]
    // 0xb5a338: r0 = Instance_MainAxisAlignment
    //     0xb5a338: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb5a33c: ldr             x0, [x0, #0xa8]
    // 0xb5a340: StoreField: r1->field_13 = r0
    //     0xb5a340: stur            w0, [x1, #0x13]
    // 0xb5a344: r0 = Instance_MainAxisSize
    //     0xb5a344: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb5a348: ldr             x0, [x0, #0xa10]
    // 0xb5a34c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb5a34c: stur            w0, [x1, #0x17]
    // 0xb5a350: r0 = Instance_CrossAxisAlignment
    //     0xb5a350: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb5a354: ldr             x0, [x0, #0xa18]
    // 0xb5a358: StoreField: r1->field_1b = r0
    //     0xb5a358: stur            w0, [x1, #0x1b]
    // 0xb5a35c: r0 = Instance_VerticalDirection
    //     0xb5a35c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb5a360: ldr             x0, [x0, #0xa20]
    // 0xb5a364: StoreField: r1->field_23 = r0
    //     0xb5a364: stur            w0, [x1, #0x23]
    // 0xb5a368: r0 = Instance_Clip
    //     0xb5a368: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb5a36c: ldr             x0, [x0, #0x38]
    // 0xb5a370: StoreField: r1->field_2b = r0
    //     0xb5a370: stur            w0, [x1, #0x2b]
    // 0xb5a374: StoreField: r1->field_2f = rZR
    //     0xb5a374: stur            xzr, [x1, #0x2f]
    // 0xb5a378: ldur            x0, [fp, #-0x10]
    // 0xb5a37c: StoreField: r1->field_b = r0
    //     0xb5a37c: stur            w0, [x1, #0xb]
    // 0xb5a380: r0 = Padding()
    //     0xb5a380: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb5a384: mov             x1, x0
    // 0xb5a388: r0 = Instance_EdgeInsets
    //     0xb5a388: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fd48] Obj!EdgeInsets@d57141
    //     0xb5a38c: ldr             x0, [x0, #0xd48]
    // 0xb5a390: stur            x1, [fp, #-0x10]
    // 0xb5a394: StoreField: r1->field_f = r0
    //     0xb5a394: stur            w0, [x1, #0xf]
    // 0xb5a398: ldur            x0, [fp, #-8]
    // 0xb5a39c: StoreField: r1->field_b = r0
    //     0xb5a39c: stur            w0, [x1, #0xb]
    // 0xb5a3a0: r0 = Container()
    //     0xb5a3a0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb5a3a4: stur            x0, [fp, #-8]
    // 0xb5a3a8: ldur            x16, [fp, #-0x28]
    // 0xb5a3ac: ldur            lr, [fp, #-0x10]
    // 0xb5a3b0: stp             lr, x16, [SP]
    // 0xb5a3b4: mov             x1, x0
    // 0xb5a3b8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xb5a3b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xb5a3bc: ldr             x4, [x4, #0x88]
    // 0xb5a3c0: r0 = Container()
    //     0xb5a3c0: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb5a3c4: ldur            x0, [fp, #-8]
    // 0xb5a3c8: LeaveFrame
    //     0xb5a3c8: mov             SP, fp
    //     0xb5a3cc: ldp             fp, lr, [SP], #0x10
    // 0xb5a3d0: ret
    //     0xb5a3d0: ret             
    // 0xb5a3d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5a3d4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5a3d8: b               #0xb59fa0
    // 0xb5a3dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5a3dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5a3e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5a3e0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5a3e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5a3e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5a3e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5a3e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5a3ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5a3ec: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _productBuildItem(/* No info */) {
    // ** addr: 0xb5a3f0, size: 0x2d0
    // 0xb5a3f0: EnterFrame
    //     0xb5a3f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5a3f4: mov             fp, SP
    // 0xb5a3f8: AllocStack(0x40)
    //     0xb5a3f8: sub             SP, SP, #0x40
    // 0xb5a3fc: SetupParameters(_CustomizationSingleSelectState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xb5a3fc: mov             x0, x1
    //     0xb5a400: stur            x1, [fp, #-8]
    //     0xb5a404: mov             x1, x3
    //     0xb5a408: stur            x2, [fp, #-0x10]
    //     0xb5a40c: stur            x3, [fp, #-0x18]
    // 0xb5a410: CheckStackOverflow
    //     0xb5a410: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5a414: cmp             SP, x16
    //     0xb5a418: b.ls            #0xb5a6a0
    // 0xb5a41c: r1 = 2
    //     0xb5a41c: movz            x1, #0x2
    // 0xb5a420: r0 = AllocateContext()
    //     0xb5a420: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5a424: mov             x2, x0
    // 0xb5a428: ldur            x0, [fp, #-8]
    // 0xb5a42c: stur            x2, [fp, #-0x20]
    // 0xb5a430: StoreField: r2->field_f = r0
    //     0xb5a430: stur            w0, [x2, #0xf]
    // 0xb5a434: ldur            x1, [fp, #-0x10]
    // 0xb5a438: StoreField: r2->field_13 = r1
    //     0xb5a438: stur            w1, [x2, #0x13]
    // 0xb5a43c: LoadField: r1 = r0->field_f
    //     0xb5a43c: ldur            w1, [x0, #0xf]
    // 0xb5a440: DecompressPointer r1
    //     0xb5a440: add             x1, x1, HEAP, lsl #32
    // 0xb5a444: cmp             w1, NULL
    // 0xb5a448: b.eq            #0xb5a6a8
    // 0xb5a44c: r0 = of()
    //     0xb5a44c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5a450: LoadField: r2 = r0->field_5b
    //     0xb5a450: ldur            w2, [x0, #0x5b]
    // 0xb5a454: DecompressPointer r2
    //     0xb5a454: add             x2, x2, HEAP, lsl #32
    // 0xb5a458: ldur            x3, [fp, #-8]
    // 0xb5a45c: stur            x2, [fp, #-0x28]
    // 0xb5a460: LoadField: r0 = r3->field_b
    //     0xb5a460: ldur            w0, [x3, #0xb]
    // 0xb5a464: DecompressPointer r0
    //     0xb5a464: add             x0, x0, HEAP, lsl #32
    // 0xb5a468: cmp             w0, NULL
    // 0xb5a46c: b.eq            #0xb5a6ac
    // 0xb5a470: LoadField: r1 = r0->field_b
    //     0xb5a470: ldur            w1, [x0, #0xb]
    // 0xb5a474: DecompressPointer r1
    //     0xb5a474: add             x1, x1, HEAP, lsl #32
    // 0xb5a478: cmp             w1, NULL
    // 0xb5a47c: b.ne            #0xb5a48c
    // 0xb5a480: ldur            x5, [fp, #-0x18]
    // 0xb5a484: r0 = Null
    //     0xb5a484: mov             x0, NULL
    // 0xb5a488: b               #0xb5a4e0
    // 0xb5a48c: LoadField: r4 = r1->field_2f
    //     0xb5a48c: ldur            w4, [x1, #0x2f]
    // 0xb5a490: DecompressPointer r4
    //     0xb5a490: add             x4, x4, HEAP, lsl #32
    // 0xb5a494: cmp             w4, NULL
    // 0xb5a498: b.ne            #0xb5a4a8
    // 0xb5a49c: ldur            x5, [fp, #-0x18]
    // 0xb5a4a0: r0 = Null
    //     0xb5a4a0: mov             x0, NULL
    // 0xb5a4a4: b               #0xb5a4e0
    // 0xb5a4a8: ldur            x5, [fp, #-0x18]
    // 0xb5a4ac: LoadField: r0 = r4->field_b
    //     0xb5a4ac: ldur            w0, [x4, #0xb]
    // 0xb5a4b0: r1 = LoadInt32Instr(r0)
    //     0xb5a4b0: sbfx            x1, x0, #1, #0x1f
    // 0xb5a4b4: mov             x0, x1
    // 0xb5a4b8: mov             x1, x5
    // 0xb5a4bc: cmp             x1, x0
    // 0xb5a4c0: b.hs            #0xb5a6b0
    // 0xb5a4c4: LoadField: r0 = r4->field_f
    //     0xb5a4c4: ldur            w0, [x4, #0xf]
    // 0xb5a4c8: DecompressPointer r0
    //     0xb5a4c8: add             x0, x0, HEAP, lsl #32
    // 0xb5a4cc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5a4cc: add             x16, x0, x5, lsl #2
    //     0xb5a4d0: ldur            w1, [x16, #0xf]
    // 0xb5a4d4: DecompressPointer r1
    //     0xb5a4d4: add             x1, x1, HEAP, lsl #32
    // 0xb5a4d8: LoadField: r0 = r1->field_f
    //     0xb5a4d8: ldur            w0, [x1, #0xf]
    // 0xb5a4dc: DecompressPointer r0
    //     0xb5a4dc: add             x0, x0, HEAP, lsl #32
    // 0xb5a4e0: cmp             w0, NULL
    // 0xb5a4e4: b.ne            #0xb5a4ec
    // 0xb5a4e8: r0 = ""
    //     0xb5a4e8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5a4ec: stur            x0, [fp, #-0x10]
    // 0xb5a4f0: LoadField: r1 = r3->field_f
    //     0xb5a4f0: ldur            w1, [x3, #0xf]
    // 0xb5a4f4: DecompressPointer r1
    //     0xb5a4f4: add             x1, x1, HEAP, lsl #32
    // 0xb5a4f8: cmp             w1, NULL
    // 0xb5a4fc: b.eq            #0xb5a6b4
    // 0xb5a500: r0 = of()
    //     0xb5a500: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb5a504: LoadField: r1 = r0->field_87
    //     0xb5a504: ldur            w1, [x0, #0x87]
    // 0xb5a508: DecompressPointer r1
    //     0xb5a508: add             x1, x1, HEAP, lsl #32
    // 0xb5a50c: LoadField: r0 = r1->field_2b
    //     0xb5a50c: ldur            w0, [x1, #0x2b]
    // 0xb5a510: DecompressPointer r0
    //     0xb5a510: add             x0, x0, HEAP, lsl #32
    // 0xb5a514: r16 = 14.000000
    //     0xb5a514: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xb5a518: ldr             x16, [x16, #0x1d8]
    // 0xb5a51c: str             x16, [SP]
    // 0xb5a520: mov             x1, x0
    // 0xb5a524: r4 = const [0, 0x2, 0x1, 0x1, fontSize, 0x1, null]
    //     0xb5a524: add             x4, PP, #0x33, lsl #12  ; [pp+0x33798] List(7) [0, 0x2, 0x1, 0x1, "fontSize", 0x1, Null]
    //     0xb5a528: ldr             x4, [x4, #0x798]
    // 0xb5a52c: r0 = copyWith()
    //     0xb5a52c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb5a530: stur            x0, [fp, #-0x30]
    // 0xb5a534: r0 = Text()
    //     0xb5a534: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb5a538: mov             x2, x0
    // 0xb5a53c: ldur            x0, [fp, #-0x10]
    // 0xb5a540: stur            x2, [fp, #-0x38]
    // 0xb5a544: StoreField: r2->field_b = r0
    //     0xb5a544: stur            w0, [x2, #0xb]
    // 0xb5a548: ldur            x0, [fp, #-0x30]
    // 0xb5a54c: StoreField: r2->field_13 = r0
    //     0xb5a54c: stur            w0, [x2, #0x13]
    // 0xb5a550: ldur            x3, [fp, #-8]
    // 0xb5a554: LoadField: r0 = r3->field_b
    //     0xb5a554: ldur            w0, [x3, #0xb]
    // 0xb5a558: DecompressPointer r0
    //     0xb5a558: add             x0, x0, HEAP, lsl #32
    // 0xb5a55c: cmp             w0, NULL
    // 0xb5a560: b.eq            #0xb5a6b8
    // 0xb5a564: LoadField: r1 = r0->field_b
    //     0xb5a564: ldur            w1, [x0, #0xb]
    // 0xb5a568: DecompressPointer r1
    //     0xb5a568: add             x1, x1, HEAP, lsl #32
    // 0xb5a56c: cmp             w1, NULL
    // 0xb5a570: b.ne            #0xb5a57c
    // 0xb5a574: r0 = Null
    //     0xb5a574: mov             x0, NULL
    // 0xb5a578: b               #0xb5a5cc
    // 0xb5a57c: LoadField: r4 = r1->field_2f
    //     0xb5a57c: ldur            w4, [x1, #0x2f]
    // 0xb5a580: DecompressPointer r4
    //     0xb5a580: add             x4, x4, HEAP, lsl #32
    // 0xb5a584: cmp             w4, NULL
    // 0xb5a588: b.ne            #0xb5a594
    // 0xb5a58c: r0 = Null
    //     0xb5a58c: mov             x0, NULL
    // 0xb5a590: b               #0xb5a5cc
    // 0xb5a594: ldur            x5, [fp, #-0x18]
    // 0xb5a598: LoadField: r0 = r4->field_b
    //     0xb5a598: ldur            w0, [x4, #0xb]
    // 0xb5a59c: r1 = LoadInt32Instr(r0)
    //     0xb5a59c: sbfx            x1, x0, #1, #0x1f
    // 0xb5a5a0: mov             x0, x1
    // 0xb5a5a4: mov             x1, x5
    // 0xb5a5a8: cmp             x1, x0
    // 0xb5a5ac: b.hs            #0xb5a6bc
    // 0xb5a5b0: LoadField: r0 = r4->field_f
    //     0xb5a5b0: ldur            w0, [x4, #0xf]
    // 0xb5a5b4: DecompressPointer r0
    //     0xb5a5b4: add             x0, x0, HEAP, lsl #32
    // 0xb5a5b8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xb5a5b8: add             x16, x0, x5, lsl #2
    //     0xb5a5bc: ldur            w1, [x16, #0xf]
    // 0xb5a5c0: DecompressPointer r1
    //     0xb5a5c0: add             x1, x1, HEAP, lsl #32
    // 0xb5a5c4: LoadField: r0 = r1->field_f
    //     0xb5a5c4: ldur            w0, [x1, #0xf]
    // 0xb5a5c8: DecompressPointer r0
    //     0xb5a5c8: add             x0, x0, HEAP, lsl #32
    // 0xb5a5cc: cmp             w0, NULL
    // 0xb5a5d0: b.ne            #0xb5a5dc
    // 0xb5a5d4: r4 = ""
    //     0xb5a5d4: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5a5d8: b               #0xb5a5e0
    // 0xb5a5dc: mov             x4, x0
    // 0xb5a5e0: ldur            x0, [fp, #-0x28]
    // 0xb5a5e4: stur            x4, [fp, #-0x30]
    // 0xb5a5e8: LoadField: r5 = r3->field_13
    //     0xb5a5e8: ldur            w5, [x3, #0x13]
    // 0xb5a5ec: DecompressPointer r5
    //     0xb5a5ec: add             x5, x5, HEAP, lsl #32
    // 0xb5a5f0: stur            x5, [fp, #-0x10]
    // 0xb5a5f4: r1 = <String>
    //     0xb5a5f4: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xb5a5f8: r0 = RadioListTile()
    //     0xb5a5f8: bl              #0x997b1c  ; AllocateRadioListTileStub -> RadioListTile<X0> (size=0xa0)
    // 0xb5a5fc: mov             x3, x0
    // 0xb5a600: ldur            x0, [fp, #-0x30]
    // 0xb5a604: stur            x3, [fp, #-8]
    // 0xb5a608: StoreField: r3->field_f = r0
    //     0xb5a608: stur            w0, [x3, #0xf]
    // 0xb5a60c: ldur            x0, [fp, #-0x10]
    // 0xb5a610: StoreField: r3->field_13 = r0
    //     0xb5a610: stur            w0, [x3, #0x13]
    // 0xb5a614: ldur            x2, [fp, #-0x20]
    // 0xb5a618: r1 = Function '<anonymous closure>':.
    //     0xb5a618: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a8f8] AnonymousClosure: (0xb5a6c0), in [package:customer_app/app/presentation/views/glass/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xb5a3f0)
    //     0xb5a61c: ldr             x1, [x1, #0x8f8]
    // 0xb5a620: r0 = AllocateClosure()
    //     0xb5a620: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5a624: mov             x1, x0
    // 0xb5a628: ldur            x0, [fp, #-8]
    // 0xb5a62c: ArrayStore: r0[0] = r1  ; List_4
    //     0xb5a62c: stur            w1, [x0, #0x17]
    // 0xb5a630: r1 = false
    //     0xb5a630: add             x1, NULL, #0x30  ; false
    // 0xb5a634: StoreField: r0->field_1f = r1
    //     0xb5a634: stur            w1, [x0, #0x1f]
    // 0xb5a638: ldur            x2, [fp, #-0x28]
    // 0xb5a63c: StoreField: r0->field_23 = r2
    //     0xb5a63c: stur            w2, [x0, #0x23]
    // 0xb5a640: ldur            x2, [fp, #-0x38]
    // 0xb5a644: StoreField: r0->field_3b = r2
    //     0xb5a644: stur            w2, [x0, #0x3b]
    // 0xb5a648: StoreField: r0->field_4f = r1
    //     0xb5a648: stur            w1, [x0, #0x4f]
    // 0xb5a64c: StoreField: r0->field_57 = r1
    //     0xb5a64c: stur            w1, [x0, #0x57]
    // 0xb5a650: d0 = 1.000000
    //     0xb5a650: fmov            d0, #1.00000000
    // 0xb5a654: StoreField: r0->field_8b = d0
    //     0xb5a654: stur            d0, [x0, #0x8b]
    // 0xb5a658: StoreField: r0->field_83 = r1
    //     0xb5a658: stur            w1, [x0, #0x83]
    // 0xb5a65c: r2 = Instance__RadioType
    //     0xb5a65c: add             x2, PP, #0x38, lsl #12  ; [pp+0x38050] Obj!_RadioType@d74141
    //     0xb5a660: ldr             x2, [x2, #0x50]
    // 0xb5a664: StoreField: r0->field_7b = r2
    //     0xb5a664: stur            w2, [x0, #0x7b]
    // 0xb5a668: StoreField: r0->field_87 = r1
    //     0xb5a668: stur            w1, [x0, #0x87]
    // 0xb5a66c: r1 = <FlexParentData>
    //     0xb5a66c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb5a670: ldr             x1, [x1, #0xe00]
    // 0xb5a674: r0 = Flexible()
    //     0xb5a674: bl              #0x987438  ; AllocateFlexibleStub -> Flexible (size=0x20)
    // 0xb5a678: r1 = 1
    //     0xb5a678: movz            x1, #0x1
    // 0xb5a67c: StoreField: r0->field_13 = r1
    //     0xb5a67c: stur            x1, [x0, #0x13]
    // 0xb5a680: r1 = Instance_FlexFit
    //     0xb5a680: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe20] Obj!FlexFit@d73581
    //     0xb5a684: ldr             x1, [x1, #0xe20]
    // 0xb5a688: StoreField: r0->field_1b = r1
    //     0xb5a688: stur            w1, [x0, #0x1b]
    // 0xb5a68c: ldur            x1, [fp, #-8]
    // 0xb5a690: StoreField: r0->field_b = r1
    //     0xb5a690: stur            w1, [x0, #0xb]
    // 0xb5a694: LeaveFrame
    //     0xb5a694: mov             SP, fp
    //     0xb5a698: ldp             fp, lr, [SP], #0x10
    // 0xb5a69c: ret
    //     0xb5a69c: ret             
    // 0xb5a6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5a6a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5a6a4: b               #0xb5a41c
    // 0xb5a6a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5a6a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5a6ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5a6ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5a6b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5a6b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xb5a6b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5a6b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5a6b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5a6b8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5a6bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb5a6bc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String?) {
    // ** addr: 0xb5a6c0, size: 0x200
    // 0xb5a6c0: EnterFrame
    //     0xb5a6c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5a6c4: mov             fp, SP
    // 0xb5a6c8: AllocStack(0x30)
    //     0xb5a6c8: sub             SP, SP, #0x30
    // 0xb5a6cc: SetupParameters()
    //     0xb5a6cc: ldr             x0, [fp, #0x18]
    //     0xb5a6d0: ldur            w1, [x0, #0x17]
    //     0xb5a6d4: add             x1, x1, HEAP, lsl #32
    //     0xb5a6d8: stur            x1, [fp, #-8]
    // 0xb5a6dc: CheckStackOverflow
    //     0xb5a6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5a6e0: cmp             SP, x16
    //     0xb5a6e4: b.ls            #0xb5a8b4
    // 0xb5a6e8: r1 = 3
    //     0xb5a6e8: movz            x1, #0x3
    // 0xb5a6ec: r0 = AllocateContext()
    //     0xb5a6ec: bl              #0x16f6108  ; AllocateContextStub
    // 0xb5a6f0: mov             x3, x0
    // 0xb5a6f4: ldur            x2, [fp, #-8]
    // 0xb5a6f8: stur            x3, [fp, #-0x10]
    // 0xb5a6fc: StoreField: r3->field_b = r2
    //     0xb5a6fc: stur            w2, [x3, #0xb]
    // 0xb5a700: ldr             x0, [fp, #0x10]
    // 0xb5a704: StoreField: r3->field_f = r0
    //     0xb5a704: stur            w0, [x3, #0xf]
    // 0xb5a708: LoadField: r0 = r2->field_f
    //     0xb5a708: ldur            w0, [x2, #0xf]
    // 0xb5a70c: DecompressPointer r0
    //     0xb5a70c: add             x0, x0, HEAP, lsl #32
    // 0xb5a710: LoadField: r1 = r0->field_b
    //     0xb5a710: ldur            w1, [x0, #0xb]
    // 0xb5a714: DecompressPointer r1
    //     0xb5a714: add             x1, x1, HEAP, lsl #32
    // 0xb5a718: cmp             w1, NULL
    // 0xb5a71c: b.eq            #0xb5a8bc
    // 0xb5a720: LoadField: r4 = r1->field_b
    //     0xb5a720: ldur            w4, [x1, #0xb]
    // 0xb5a724: DecompressPointer r4
    //     0xb5a724: add             x4, x4, HEAP, lsl #32
    // 0xb5a728: cmp             w4, NULL
    // 0xb5a72c: b.ne            #0xb5a738
    // 0xb5a730: r4 = Null
    //     0xb5a730: mov             x4, NULL
    // 0xb5a734: b               #0xb5a744
    // 0xb5a738: LoadField: r5 = r4->field_2b
    //     0xb5a738: ldur            w5, [x4, #0x2b]
    // 0xb5a73c: DecompressPointer r5
    //     0xb5a73c: add             x5, x5, HEAP, lsl #32
    // 0xb5a740: mov             x4, x5
    // 0xb5a744: cmp             w4, NULL
    // 0xb5a748: b.ne            #0xb5a750
    // 0xb5a74c: r4 = false
    //     0xb5a74c: add             x4, NULL, #0x30  ; false
    // 0xb5a750: ArrayLoad: r5 = r0[0]  ; List_8
    //     0xb5a750: ldur            x5, [x0, #0x17]
    // 0xb5a754: LoadField: r6 = r1->field_13
    //     0xb5a754: ldur            w6, [x1, #0x13]
    // 0xb5a758: DecompressPointer r6
    //     0xb5a758: add             x6, x6, HEAP, lsl #32
    // 0xb5a75c: r0 = BoxInt64Instr(r5)
    //     0xb5a75c: sbfiz           x0, x5, #1, #0x1f
    //     0xb5a760: cmp             x5, x0, asr #1
    //     0xb5a764: b.eq            #0xb5a770
    //     0xb5a768: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb5a76c: stur            x5, [x0, #7]
    // 0xb5a770: stp             x4, x6, [SP, #8]
    // 0xb5a774: str             x0, [SP]
    // 0xb5a778: r4 = 0
    //     0xb5a778: movz            x4, #0
    // 0xb5a77c: ldr             x0, [SP, #0x10]
    // 0xb5a780: r16 = UnlinkedCall_0x613b5c
    //     0xb5a780: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a900] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5a784: add             x16, x16, #0x900
    // 0xb5a788: ldp             x5, lr, [x16]
    // 0xb5a78c: blr             lr
    // 0xb5a790: ldur            x0, [fp, #-8]
    // 0xb5a794: LoadField: r1 = r0->field_f
    //     0xb5a794: ldur            w1, [x0, #0xf]
    // 0xb5a798: DecompressPointer r1
    //     0xb5a798: add             x1, x1, HEAP, lsl #32
    // 0xb5a79c: LoadField: r2 = r0->field_13
    //     0xb5a79c: ldur            w2, [x0, #0x13]
    // 0xb5a7a0: DecompressPointer r2
    //     0xb5a7a0: add             x2, x2, HEAP, lsl #32
    // 0xb5a7a4: cmp             w2, NULL
    // 0xb5a7a8: b.ne            #0xb5a7b4
    // 0xb5a7ac: r2 = Null
    //     0xb5a7ac: mov             x2, NULL
    // 0xb5a7b0: b               #0xb5a7c0
    // 0xb5a7b4: LoadField: r3 = r2->field_13
    //     0xb5a7b4: ldur            w3, [x2, #0x13]
    // 0xb5a7b8: DecompressPointer r3
    //     0xb5a7b8: add             x3, x3, HEAP, lsl #32
    // 0xb5a7bc: mov             x2, x3
    // 0xb5a7c0: cmp             w2, NULL
    // 0xb5a7c4: b.ne            #0xb5a7d0
    // 0xb5a7c8: r3 = 0
    //     0xb5a7c8: movz            x3, #0
    // 0xb5a7cc: b               #0xb5a7dc
    // 0xb5a7d0: r3 = LoadInt32Instr(r2)
    //     0xb5a7d0: sbfx            x3, x2, #1, #0x1f
    //     0xb5a7d4: tbz             w2, #0, #0xb5a7dc
    //     0xb5a7d8: ldur            x3, [x2, #7]
    // 0xb5a7dc: ldur            x2, [fp, #-0x10]
    // 0xb5a7e0: ArrayStore: r1[0] = r3  ; List_8
    //     0xb5a7e0: stur            x3, [x1, #0x17]
    // 0xb5a7e4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xb5a7e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xb5a7e8: ldr             x0, [x0]
    //     0xb5a7ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xb5a7f0: cmp             w0, w16
    //     0xb5a7f4: b.ne            #0xb5a800
    //     0xb5a7f8: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xb5a7fc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xb5a800: r1 = <CustomerResponse>
    //     0xb5a800: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0xb5a804: ldr             x1, [x1, #0x5a8]
    // 0xb5a808: stur            x0, [fp, #-0x18]
    // 0xb5a80c: r0 = AllocateGrowableArray()
    //     0xb5a80c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5a810: ldur            x2, [fp, #-0x18]
    // 0xb5a814: StoreField: r0->field_f = r2
    //     0xb5a814: stur            w2, [x0, #0xf]
    // 0xb5a818: StoreField: r0->field_b = rZR
    //     0xb5a818: stur            wzr, [x0, #0xb]
    // 0xb5a81c: ldur            x3, [fp, #-0x10]
    // 0xb5a820: StoreField: r3->field_13 = r0
    //     0xb5a820: stur            w0, [x3, #0x13]
    //     0xb5a824: ldurb           w16, [x3, #-1]
    //     0xb5a828: ldurb           w17, [x0, #-1]
    //     0xb5a82c: and             x16, x17, x16, lsr #2
    //     0xb5a830: tst             x16, HEAP, lsr #32
    //     0xb5a834: b.eq            #0xb5a83c
    //     0xb5a838: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xb5a83c: r1 = <ProductCustomisation>
    //     0xb5a83c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23370] TypeArguments: <ProductCustomisation>
    //     0xb5a840: ldr             x1, [x1, #0x370]
    // 0xb5a844: r0 = AllocateGrowableArray()
    //     0xb5a844: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb5a848: mov             x1, x0
    // 0xb5a84c: ldur            x0, [fp, #-0x18]
    // 0xb5a850: StoreField: r1->field_f = r0
    //     0xb5a850: stur            w0, [x1, #0xf]
    // 0xb5a854: StoreField: r1->field_b = rZR
    //     0xb5a854: stur            wzr, [x1, #0xb]
    // 0xb5a858: mov             x0, x1
    // 0xb5a85c: ldur            x2, [fp, #-0x10]
    // 0xb5a860: ArrayStore: r2[0] = r0  ; List_4
    //     0xb5a860: stur            w0, [x2, #0x17]
    //     0xb5a864: ldurb           w16, [x2, #-1]
    //     0xb5a868: ldurb           w17, [x0, #-1]
    //     0xb5a86c: and             x16, x17, x16, lsr #2
    //     0xb5a870: tst             x16, HEAP, lsr #32
    //     0xb5a874: b.eq            #0xb5a87c
    //     0xb5a878: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb5a87c: ldur            x0, [fp, #-8]
    // 0xb5a880: LoadField: r3 = r0->field_f
    //     0xb5a880: ldur            w3, [x0, #0xf]
    // 0xb5a884: DecompressPointer r3
    //     0xb5a884: add             x3, x3, HEAP, lsl #32
    // 0xb5a888: stur            x3, [fp, #-0x18]
    // 0xb5a88c: r1 = Function '<anonymous closure>':.
    //     0xb5a88c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a910] AnonymousClosure: (0xb5a8c0), in [package:customer_app/app/presentation/views/glass/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xb5a3f0)
    //     0xb5a890: ldr             x1, [x1, #0x910]
    // 0xb5a894: r0 = AllocateClosure()
    //     0xb5a894: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5a898: ldur            x1, [fp, #-0x18]
    // 0xb5a89c: mov             x2, x0
    // 0xb5a8a0: r0 = setState()
    //     0xb5a8a0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb5a8a4: r0 = Null
    //     0xb5a8a4: mov             x0, NULL
    // 0xb5a8a8: LeaveFrame
    //     0xb5a8a8: mov             SP, fp
    //     0xb5a8ac: ldp             fp, lr, [SP], #0x10
    // 0xb5a8b0: ret
    //     0xb5a8b0: ret             
    // 0xb5a8b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5a8b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5a8b8: b               #0xb5a6e8
    // 0xb5a8bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5a8bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb5a8c0, size: 0x57c
    // 0xb5a8c0: EnterFrame
    //     0xb5a8c0: stp             fp, lr, [SP, #-0x10]!
    //     0xb5a8c4: mov             fp, SP
    // 0xb5a8c8: AllocStack(0x70)
    //     0xb5a8c8: sub             SP, SP, #0x70
    // 0xb5a8cc: SetupParameters()
    //     0xb5a8cc: ldr             x0, [fp, #0x10]
    //     0xb5a8d0: ldur            w3, [x0, #0x17]
    //     0xb5a8d4: add             x3, x3, HEAP, lsl #32
    //     0xb5a8d8: stur            x3, [fp, #-0x18]
    // 0xb5a8dc: CheckStackOverflow
    //     0xb5a8dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb5a8e0: cmp             SP, x16
    //     0xb5a8e4: b.ls            #0xb5ae20
    // 0xb5a8e8: LoadField: r0 = r3->field_b
    //     0xb5a8e8: ldur            w0, [x3, #0xb]
    // 0xb5a8ec: DecompressPointer r0
    //     0xb5a8ec: add             x0, x0, HEAP, lsl #32
    // 0xb5a8f0: stur            x0, [fp, #-0x10]
    // 0xb5a8f4: LoadField: r1 = r0->field_f
    //     0xb5a8f4: ldur            w1, [x0, #0xf]
    // 0xb5a8f8: DecompressPointer r1
    //     0xb5a8f8: add             x1, x1, HEAP, lsl #32
    // 0xb5a8fc: LoadField: r2 = r1->field_b
    //     0xb5a8fc: ldur            w2, [x1, #0xb]
    // 0xb5a900: DecompressPointer r2
    //     0xb5a900: add             x2, x2, HEAP, lsl #32
    // 0xb5a904: cmp             w2, NULL
    // 0xb5a908: b.eq            #0xb5ae28
    // 0xb5a90c: LoadField: r4 = r2->field_f
    //     0xb5a90c: ldur            w4, [x2, #0xf]
    // 0xb5a910: DecompressPointer r4
    //     0xb5a910: add             x4, x4, HEAP, lsl #32
    // 0xb5a914: stur            x4, [fp, #-8]
    // 0xb5a918: LoadField: r1 = r4->field_b
    //     0xb5a918: ldur            w1, [x4, #0xb]
    // 0xb5a91c: cbz             w1, #0xb5a9ac
    // 0xb5a920: mov             x2, x3
    // 0xb5a924: r1 = Function '<anonymous closure>':.
    //     0xb5a924: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a918] AnonymousClosure: (0xa3aa20), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xb5a928: ldr             x1, [x1, #0x918]
    // 0xb5a92c: r0 = AllocateClosure()
    //     0xb5a92c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5a930: r1 = Function '<anonymous closure>':.
    //     0xb5a930: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a920] AnonymousClosure: (0xa309d8), in [package:customer_app/app/presentation/views/line/customization/customization_single_select.dart] _CustomizationSingleSelectState::_productBuildItem (0xa3acd0)
    //     0xb5a934: ldr             x1, [x1, #0x920]
    // 0xb5a938: r2 = Null
    //     0xb5a938: mov             x2, NULL
    // 0xb5a93c: stur            x0, [fp, #-0x20]
    // 0xb5a940: r0 = AllocateClosure()
    //     0xb5a940: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5a944: str             x0, [SP]
    // 0xb5a948: ldur            x1, [fp, #-8]
    // 0xb5a94c: ldur            x2, [fp, #-0x20]
    // 0xb5a950: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0xb5a950: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0xb5a954: ldr             x4, [x4, #0xb48]
    // 0xb5a958: r0 = firstWhere()
    //     0xb5a958: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0xb5a95c: LoadField: r1 = r0->field_b
    //     0xb5a95c: ldur            w1, [x0, #0xb]
    // 0xb5a960: DecompressPointer r1
    //     0xb5a960: add             x1, x1, HEAP, lsl #32
    // 0xb5a964: cmp             w1, NULL
    // 0xb5a968: b.eq            #0xb5a9ac
    // 0xb5a96c: ldur            x1, [fp, #-0x10]
    // 0xb5a970: LoadField: r2 = r1->field_f
    //     0xb5a970: ldur            w2, [x1, #0xf]
    // 0xb5a974: DecompressPointer r2
    //     0xb5a974: add             x2, x2, HEAP, lsl #32
    // 0xb5a978: LoadField: r3 = r2->field_b
    //     0xb5a978: ldur            w3, [x2, #0xb]
    // 0xb5a97c: DecompressPointer r3
    //     0xb5a97c: add             x3, x3, HEAP, lsl #32
    // 0xb5a980: cmp             w3, NULL
    // 0xb5a984: b.eq            #0xb5ae2c
    // 0xb5a988: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xb5a988: ldur            w2, [x3, #0x17]
    // 0xb5a98c: DecompressPointer r2
    //     0xb5a98c: add             x2, x2, HEAP, lsl #32
    // 0xb5a990: stp             x0, x2, [SP]
    // 0xb5a994: r4 = 0
    //     0xb5a994: movz            x4, #0
    // 0xb5a998: ldr             x0, [SP, #8]
    // 0xb5a99c: r16 = UnlinkedCall_0x613b5c
    //     0xb5a99c: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a928] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5a9a0: add             x16, x16, #0x928
    // 0xb5a9a4: ldp             x5, lr, [x16]
    // 0xb5a9a8: blr             lr
    // 0xb5a9ac: ldur            x0, [fp, #-0x10]
    // 0xb5a9b0: LoadField: r1 = r0->field_f
    //     0xb5a9b0: ldur            w1, [x0, #0xf]
    // 0xb5a9b4: DecompressPointer r1
    //     0xb5a9b4: add             x1, x1, HEAP, lsl #32
    // 0xb5a9b8: LoadField: r2 = r1->field_b
    //     0xb5a9b8: ldur            w2, [x1, #0xb]
    // 0xb5a9bc: DecompressPointer r2
    //     0xb5a9bc: add             x2, x2, HEAP, lsl #32
    // 0xb5a9c0: cmp             w2, NULL
    // 0xb5a9c4: b.eq            #0xb5ae30
    // 0xb5a9c8: LoadField: r1 = r2->field_b
    //     0xb5a9c8: ldur            w1, [x2, #0xb]
    // 0xb5a9cc: DecompressPointer r1
    //     0xb5a9cc: add             x1, x1, HEAP, lsl #32
    // 0xb5a9d0: cmp             w1, NULL
    // 0xb5a9d4: b.ne            #0xb5a9e0
    // 0xb5a9d8: r1 = Null
    //     0xb5a9d8: mov             x1, NULL
    // 0xb5a9dc: b               #0xb5a9ec
    // 0xb5a9e0: LoadField: r2 = r1->field_1b
    //     0xb5a9e0: ldur            w2, [x1, #0x1b]
    // 0xb5a9e4: DecompressPointer r2
    //     0xb5a9e4: add             x2, x2, HEAP, lsl #32
    // 0xb5a9e8: mov             x1, x2
    // 0xb5a9ec: stur            x1, [fp, #-0x28]
    // 0xb5a9f0: LoadField: r2 = r0->field_13
    //     0xb5a9f0: ldur            w2, [x0, #0x13]
    // 0xb5a9f4: DecompressPointer r2
    //     0xb5a9f4: add             x2, x2, HEAP, lsl #32
    // 0xb5a9f8: cmp             w2, NULL
    // 0xb5a9fc: b.ne            #0xb5aa08
    // 0xb5aa00: r3 = Null
    //     0xb5aa00: mov             x3, NULL
    // 0xb5aa04: b               #0xb5aa10
    // 0xb5aa08: LoadField: r3 = r2->field_b
    //     0xb5aa08: ldur            w3, [x2, #0xb]
    // 0xb5aa0c: DecompressPointer r3
    //     0xb5aa0c: add             x3, x3, HEAP, lsl #32
    // 0xb5aa10: ldur            x2, [fp, #-0x18]
    // 0xb5aa14: stur            x3, [fp, #-0x20]
    // 0xb5aa18: LoadField: r4 = r2->field_f
    //     0xb5aa18: ldur            w4, [x2, #0xf]
    // 0xb5aa1c: DecompressPointer r4
    //     0xb5aa1c: add             x4, x4, HEAP, lsl #32
    // 0xb5aa20: stur            x4, [fp, #-8]
    // 0xb5aa24: r0 = CustomerResponse()
    //     0xb5aa24: bl              #0x8a2438  ; AllocateCustomerResponseStub -> CustomerResponse (size=0x18)
    // 0xb5aa28: mov             x2, x0
    // 0xb5aa2c: ldur            x0, [fp, #-0x28]
    // 0xb5aa30: stur            x2, [fp, #-0x38]
    // 0xb5aa34: StoreField: r2->field_7 = r0
    //     0xb5aa34: stur            w0, [x2, #7]
    // 0xb5aa38: ldur            x0, [fp, #-8]
    // 0xb5aa3c: StoreField: r2->field_b = r0
    //     0xb5aa3c: stur            w0, [x2, #0xb]
    // 0xb5aa40: ldur            x0, [fp, #-0x20]
    // 0xb5aa44: StoreField: r2->field_f = r0
    //     0xb5aa44: stur            w0, [x2, #0xf]
    // 0xb5aa48: ldur            x0, [fp, #-0x18]
    // 0xb5aa4c: LoadField: r3 = r0->field_13
    //     0xb5aa4c: ldur            w3, [x0, #0x13]
    // 0xb5aa50: DecompressPointer r3
    //     0xb5aa50: add             x3, x3, HEAP, lsl #32
    // 0xb5aa54: stur            x3, [fp, #-8]
    // 0xb5aa58: LoadField: r1 = r3->field_b
    //     0xb5aa58: ldur            w1, [x3, #0xb]
    // 0xb5aa5c: LoadField: r4 = r3->field_f
    //     0xb5aa5c: ldur            w4, [x3, #0xf]
    // 0xb5aa60: DecompressPointer r4
    //     0xb5aa60: add             x4, x4, HEAP, lsl #32
    // 0xb5aa64: LoadField: r5 = r4->field_b
    //     0xb5aa64: ldur            w5, [x4, #0xb]
    // 0xb5aa68: r4 = LoadInt32Instr(r1)
    //     0xb5aa68: sbfx            x4, x1, #1, #0x1f
    // 0xb5aa6c: stur            x4, [fp, #-0x30]
    // 0xb5aa70: r1 = LoadInt32Instr(r5)
    //     0xb5aa70: sbfx            x1, x5, #1, #0x1f
    // 0xb5aa74: cmp             x4, x1
    // 0xb5aa78: b.ne            #0xb5aa84
    // 0xb5aa7c: mov             x1, x3
    // 0xb5aa80: r0 = _growToNextCapacity()
    //     0xb5aa80: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5aa84: ldur            x4, [fp, #-0x10]
    // 0xb5aa88: ldur            x3, [fp, #-8]
    // 0xb5aa8c: ldur            x2, [fp, #-0x30]
    // 0xb5aa90: add             x0, x2, #1
    // 0xb5aa94: lsl             x1, x0, #1
    // 0xb5aa98: StoreField: r3->field_b = r1
    //     0xb5aa98: stur            w1, [x3, #0xb]
    // 0xb5aa9c: LoadField: r1 = r3->field_f
    //     0xb5aa9c: ldur            w1, [x3, #0xf]
    // 0xb5aaa0: DecompressPointer r1
    //     0xb5aaa0: add             x1, x1, HEAP, lsl #32
    // 0xb5aaa4: ldur            x0, [fp, #-0x38]
    // 0xb5aaa8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb5aaa8: add             x25, x1, x2, lsl #2
    //     0xb5aaac: add             x25, x25, #0xf
    //     0xb5aab0: str             w0, [x25]
    //     0xb5aab4: tbz             w0, #0, #0xb5aad0
    //     0xb5aab8: ldurb           w16, [x1, #-1]
    //     0xb5aabc: ldurb           w17, [x0, #-1]
    //     0xb5aac0: and             x16, x17, x16, lsr #2
    //     0xb5aac4: tst             x16, HEAP, lsr #32
    //     0xb5aac8: b.eq            #0xb5aad0
    //     0xb5aacc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5aad0: LoadField: r0 = r4->field_f
    //     0xb5aad0: ldur            w0, [x4, #0xf]
    // 0xb5aad4: DecompressPointer r0
    //     0xb5aad4: add             x0, x0, HEAP, lsl #32
    // 0xb5aad8: LoadField: r1 = r0->field_b
    //     0xb5aad8: ldur            w1, [x0, #0xb]
    // 0xb5aadc: DecompressPointer r1
    //     0xb5aadc: add             x1, x1, HEAP, lsl #32
    // 0xb5aae0: cmp             w1, NULL
    // 0xb5aae4: b.eq            #0xb5ae34
    // 0xb5aae8: LoadField: r0 = r1->field_b
    //     0xb5aae8: ldur            w0, [x1, #0xb]
    // 0xb5aaec: DecompressPointer r0
    //     0xb5aaec: add             x0, x0, HEAP, lsl #32
    // 0xb5aaf0: cmp             w0, NULL
    // 0xb5aaf4: b.ne            #0xb5ab00
    // 0xb5aaf8: r0 = Null
    //     0xb5aaf8: mov             x0, NULL
    // 0xb5aafc: b               #0xb5ab0c
    // 0xb5ab00: LoadField: r2 = r0->field_7
    //     0xb5ab00: ldur            w2, [x0, #7]
    // 0xb5ab04: DecompressPointer r2
    //     0xb5ab04: add             x2, x2, HEAP, lsl #32
    // 0xb5ab08: mov             x0, x2
    // 0xb5ab0c: stur            x0, [fp, #-0x40]
    // 0xb5ab10: LoadField: r2 = r1->field_b
    //     0xb5ab10: ldur            w2, [x1, #0xb]
    // 0xb5ab14: DecompressPointer r2
    //     0xb5ab14: add             x2, x2, HEAP, lsl #32
    // 0xb5ab18: cmp             w2, NULL
    // 0xb5ab1c: b.ne            #0xb5ab28
    // 0xb5ab20: r5 = Null
    //     0xb5ab20: mov             x5, NULL
    // 0xb5ab24: b               #0xb5ab30
    // 0xb5ab28: LoadField: r5 = r2->field_b
    //     0xb5ab28: ldur            w5, [x2, #0xb]
    // 0xb5ab2c: DecompressPointer r5
    //     0xb5ab2c: add             x5, x5, HEAP, lsl #32
    // 0xb5ab30: stur            x5, [fp, #-0x38]
    // 0xb5ab34: LoadField: r2 = r1->field_b
    //     0xb5ab34: ldur            w2, [x1, #0xb]
    // 0xb5ab38: DecompressPointer r2
    //     0xb5ab38: add             x2, x2, HEAP, lsl #32
    // 0xb5ab3c: cmp             w2, NULL
    // 0xb5ab40: b.ne            #0xb5ab4c
    // 0xb5ab44: r6 = Null
    //     0xb5ab44: mov             x6, NULL
    // 0xb5ab48: b               #0xb5ab58
    // 0xb5ab4c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xb5ab4c: ldur            w1, [x2, #0x17]
    // 0xb5ab50: DecompressPointer r1
    //     0xb5ab50: add             x1, x1, HEAP, lsl #32
    // 0xb5ab54: mov             x6, x1
    // 0xb5ab58: stur            x6, [fp, #-0x28]
    // 0xb5ab5c: LoadField: r1 = r4->field_13
    //     0xb5ab5c: ldur            w1, [x4, #0x13]
    // 0xb5ab60: DecompressPointer r1
    //     0xb5ab60: add             x1, x1, HEAP, lsl #32
    // 0xb5ab64: cmp             w1, NULL
    // 0xb5ab68: b.ne            #0xb5ab74
    // 0xb5ab6c: r7 = Null
    //     0xb5ab6c: mov             x7, NULL
    // 0xb5ab70: b               #0xb5ab80
    // 0xb5ab74: LoadField: r2 = r1->field_13
    //     0xb5ab74: ldur            w2, [x1, #0x13]
    // 0xb5ab78: DecompressPointer r2
    //     0xb5ab78: add             x2, x2, HEAP, lsl #32
    // 0xb5ab7c: mov             x7, x2
    // 0xb5ab80: stur            x7, [fp, #-0x20]
    // 0xb5ab84: r1 = Null
    //     0xb5ab84: mov             x1, NULL
    // 0xb5ab88: r2 = 4
    //     0xb5ab88: movz            x2, #0x4
    // 0xb5ab8c: r0 = AllocateArray()
    //     0xb5ab8c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb5ab90: stur            x0, [fp, #-0x48]
    // 0xb5ab94: r16 = "₹"
    //     0xb5ab94: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0xb5ab98: ldr             x16, [x16, #0x360]
    // 0xb5ab9c: StoreField: r0->field_f = r16
    //     0xb5ab9c: stur            w16, [x0, #0xf]
    // 0xb5aba0: r1 = Function '<anonymous closure>': static.
    //     0xb5aba0: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0xb5aba4: ldr             x1, [x1, #0x1a0]
    // 0xb5aba8: r2 = Null
    //     0xb5aba8: mov             x2, NULL
    // 0xb5abac: r0 = AllocateClosure()
    //     0xb5abac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb5abb0: mov             x3, x0
    // 0xb5abb4: r1 = Null
    //     0xb5abb4: mov             x1, NULL
    // 0xb5abb8: r2 = Null
    //     0xb5abb8: mov             x2, NULL
    // 0xb5abbc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xb5abbc: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xb5abc0: r0 = NumberFormat._forPattern()
    //     0xb5abc0: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0xb5abc4: mov             x1, x0
    // 0xb5abc8: ldur            x0, [fp, #-0x10]
    // 0xb5abcc: LoadField: r2 = r0->field_13
    //     0xb5abcc: ldur            w2, [x0, #0x13]
    // 0xb5abd0: DecompressPointer r2
    //     0xb5abd0: add             x2, x2, HEAP, lsl #32
    // 0xb5abd4: cmp             w2, NULL
    // 0xb5abd8: b.ne            #0xb5abe4
    // 0xb5abdc: r2 = Null
    //     0xb5abdc: mov             x2, NULL
    // 0xb5abe0: b               #0xb5abf0
    // 0xb5abe4: LoadField: r3 = r2->field_13
    //     0xb5abe4: ldur            w3, [x2, #0x13]
    // 0xb5abe8: DecompressPointer r3
    //     0xb5abe8: add             x3, x3, HEAP, lsl #32
    // 0xb5abec: mov             x2, x3
    // 0xb5abf0: ldur            x8, [fp, #-0x18]
    // 0xb5abf4: ldur            x3, [fp, #-8]
    // 0xb5abf8: ldur            x4, [fp, #-0x40]
    // 0xb5abfc: ldur            x5, [fp, #-0x38]
    // 0xb5ac00: ldur            x6, [fp, #-0x28]
    // 0xb5ac04: ldur            x7, [fp, #-0x20]
    // 0xb5ac08: r0 = format()
    //     0xb5ac08: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0xb5ac0c: ldur            x1, [fp, #-0x48]
    // 0xb5ac10: ArrayStore: r1[1] = r0  ; List_4
    //     0xb5ac10: add             x25, x1, #0x13
    //     0xb5ac14: str             w0, [x25]
    //     0xb5ac18: tbz             w0, #0, #0xb5ac34
    //     0xb5ac1c: ldurb           w16, [x1, #-1]
    //     0xb5ac20: ldurb           w17, [x0, #-1]
    //     0xb5ac24: and             x16, x17, x16, lsr #2
    //     0xb5ac28: tst             x16, HEAP, lsr #32
    //     0xb5ac2c: b.eq            #0xb5ac34
    //     0xb5ac30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5ac34: ldur            x16, [fp, #-0x48]
    // 0xb5ac38: str             x16, [SP]
    // 0xb5ac3c: r0 = _interpolate()
    //     0xb5ac3c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb5ac40: stur            x0, [fp, #-0x48]
    // 0xb5ac44: r0 = ProductCustomisation()
    //     0xb5ac44: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0xb5ac48: mov             x2, x0
    // 0xb5ac4c: ldur            x0, [fp, #-0x40]
    // 0xb5ac50: stur            x2, [fp, #-0x50]
    // 0xb5ac54: StoreField: r2->field_b = r0
    //     0xb5ac54: stur            w0, [x2, #0xb]
    // 0xb5ac58: ldur            x0, [fp, #-0x38]
    // 0xb5ac5c: StoreField: r2->field_f = r0
    //     0xb5ac5c: stur            w0, [x2, #0xf]
    // 0xb5ac60: ldur            x0, [fp, #-0x28]
    // 0xb5ac64: ArrayStore: r2[0] = r0  ; List_4
    //     0xb5ac64: stur            w0, [x2, #0x17]
    // 0xb5ac68: ldur            x0, [fp, #-8]
    // 0xb5ac6c: StoreField: r2->field_23 = r0
    //     0xb5ac6c: stur            w0, [x2, #0x23]
    // 0xb5ac70: ldur            x0, [fp, #-0x20]
    // 0xb5ac74: StoreField: r2->field_27 = r0
    //     0xb5ac74: stur            w0, [x2, #0x27]
    // 0xb5ac78: ldur            x0, [fp, #-0x48]
    // 0xb5ac7c: StoreField: r2->field_2b = r0
    //     0xb5ac7c: stur            w0, [x2, #0x2b]
    // 0xb5ac80: ldur            x0, [fp, #-0x18]
    // 0xb5ac84: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xb5ac84: ldur            w3, [x0, #0x17]
    // 0xb5ac88: DecompressPointer r3
    //     0xb5ac88: add             x3, x3, HEAP, lsl #32
    // 0xb5ac8c: mov             x1, x3
    // 0xb5ac90: stur            x3, [fp, #-8]
    // 0xb5ac94: r0 = clear()
    //     0xb5ac94: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xb5ac98: ldur            x0, [fp, #-8]
    // 0xb5ac9c: LoadField: r1 = r0->field_b
    //     0xb5ac9c: ldur            w1, [x0, #0xb]
    // 0xb5aca0: LoadField: r2 = r0->field_f
    //     0xb5aca0: ldur            w2, [x0, #0xf]
    // 0xb5aca4: DecompressPointer r2
    //     0xb5aca4: add             x2, x2, HEAP, lsl #32
    // 0xb5aca8: LoadField: r3 = r2->field_b
    //     0xb5aca8: ldur            w3, [x2, #0xb]
    // 0xb5acac: r2 = LoadInt32Instr(r1)
    //     0xb5acac: sbfx            x2, x1, #1, #0x1f
    // 0xb5acb0: stur            x2, [fp, #-0x30]
    // 0xb5acb4: r1 = LoadInt32Instr(r3)
    //     0xb5acb4: sbfx            x1, x3, #1, #0x1f
    // 0xb5acb8: cmp             x2, x1
    // 0xb5acbc: b.ne            #0xb5acc8
    // 0xb5acc0: mov             x1, x0
    // 0xb5acc4: r0 = _growToNextCapacity()
    //     0xb5acc4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb5acc8: ldur            x4, [fp, #-0x10]
    // 0xb5accc: ldur            x2, [fp, #-8]
    // 0xb5acd0: ldur            x3, [fp, #-0x30]
    // 0xb5acd4: add             x0, x3, #1
    // 0xb5acd8: lsl             x1, x0, #1
    // 0xb5acdc: StoreField: r2->field_b = r1
    //     0xb5acdc: stur            w1, [x2, #0xb]
    // 0xb5ace0: LoadField: r1 = r2->field_f
    //     0xb5ace0: ldur            w1, [x2, #0xf]
    // 0xb5ace4: DecompressPointer r1
    //     0xb5ace4: add             x1, x1, HEAP, lsl #32
    // 0xb5ace8: ldur            x0, [fp, #-0x50]
    // 0xb5acec: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb5acec: add             x25, x1, x3, lsl #2
    //     0xb5acf0: add             x25, x25, #0xf
    //     0xb5acf4: str             w0, [x25]
    //     0xb5acf8: tbz             w0, #0, #0xb5ad14
    //     0xb5acfc: ldurb           w16, [x1, #-1]
    //     0xb5ad00: ldurb           w17, [x0, #-1]
    //     0xb5ad04: and             x16, x17, x16, lsr #2
    //     0xb5ad08: tst             x16, HEAP, lsr #32
    //     0xb5ad0c: b.eq            #0xb5ad14
    //     0xb5ad10: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb5ad14: LoadField: r0 = r4->field_f
    //     0xb5ad14: ldur            w0, [x4, #0xf]
    // 0xb5ad18: DecompressPointer r0
    //     0xb5ad18: add             x0, x0, HEAP, lsl #32
    // 0xb5ad1c: LoadField: r1 = r0->field_b
    //     0xb5ad1c: ldur            w1, [x0, #0xb]
    // 0xb5ad20: DecompressPointer r1
    //     0xb5ad20: add             x1, x1, HEAP, lsl #32
    // 0xb5ad24: cmp             w1, NULL
    // 0xb5ad28: b.eq            #0xb5ae38
    // 0xb5ad2c: LoadField: r0 = r4->field_13
    //     0xb5ad2c: ldur            w0, [x4, #0x13]
    // 0xb5ad30: DecompressPointer r0
    //     0xb5ad30: add             x0, x0, HEAP, lsl #32
    // 0xb5ad34: cmp             w0, NULL
    // 0xb5ad38: b.ne            #0xb5ad44
    // 0xb5ad3c: r0 = Null
    //     0xb5ad3c: mov             x0, NULL
    // 0xb5ad40: b               #0xb5ad50
    // 0xb5ad44: LoadField: r3 = r0->field_13
    //     0xb5ad44: ldur            w3, [x0, #0x13]
    // 0xb5ad48: DecompressPointer r3
    //     0xb5ad48: add             x3, x3, HEAP, lsl #32
    // 0xb5ad4c: mov             x0, x3
    // 0xb5ad50: LoadField: r3 = r1->field_b
    //     0xb5ad50: ldur            w3, [x1, #0xb]
    // 0xb5ad54: DecompressPointer r3
    //     0xb5ad54: add             x3, x3, HEAP, lsl #32
    // 0xb5ad58: cmp             w3, NULL
    // 0xb5ad5c: b.ne            #0xb5ad68
    // 0xb5ad60: r3 = Null
    //     0xb5ad60: mov             x3, NULL
    // 0xb5ad64: b               #0xb5ad74
    // 0xb5ad68: LoadField: r5 = r3->field_7
    //     0xb5ad68: ldur            w5, [x3, #7]
    // 0xb5ad6c: DecompressPointer r5
    //     0xb5ad6c: add             x5, x5, HEAP, lsl #32
    // 0xb5ad70: mov             x3, x5
    // 0xb5ad74: cmp             w3, NULL
    // 0xb5ad78: b.ne            #0xb5ad84
    // 0xb5ad7c: r5 = ""
    //     0xb5ad7c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb5ad80: b               #0xb5ad88
    // 0xb5ad84: mov             x5, x3
    // 0xb5ad88: ldur            x3, [fp, #-0x18]
    // 0xb5ad8c: LoadField: r6 = r1->field_1b
    //     0xb5ad8c: ldur            w6, [x1, #0x1b]
    // 0xb5ad90: DecompressPointer r6
    //     0xb5ad90: add             x6, x6, HEAP, lsl #32
    // 0xb5ad94: stp             x0, x6, [SP, #0x10]
    // 0xb5ad98: stp             x2, x5, [SP]
    // 0xb5ad9c: r4 = 0
    //     0xb5ad9c: movz            x4, #0
    // 0xb5ada0: ldr             x0, [SP, #0x18]
    // 0xb5ada4: r16 = UnlinkedCall_0x613b5c
    //     0xb5ada4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a938] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb5ada8: add             x16, x16, #0x938
    // 0xb5adac: ldp             x5, lr, [x16]
    // 0xb5adb0: blr             lr
    // 0xb5adb4: ldur            x0, [fp, #-0x10]
    // 0xb5adb8: LoadField: r1 = r0->field_f
    //     0xb5adb8: ldur            w1, [x0, #0xf]
    // 0xb5adbc: DecompressPointer r1
    //     0xb5adbc: add             x1, x1, HEAP, lsl #32
    // 0xb5adc0: ldur            x0, [fp, #-0x18]
    // 0xb5adc4: stur            x1, [fp, #-8]
    // 0xb5adc8: LoadField: r2 = r0->field_f
    //     0xb5adc8: ldur            w2, [x0, #0xf]
    // 0xb5adcc: DecompressPointer r2
    //     0xb5adcc: add             x2, x2, HEAP, lsl #32
    // 0xb5add0: r0 = LoadClassIdInstr(r2)
    //     0xb5add0: ldur            x0, [x2, #-1]
    //     0xb5add4: ubfx            x0, x0, #0xc, #0x14
    // 0xb5add8: str             x2, [SP]
    // 0xb5addc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xb5addc: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xb5ade0: r0 = GDT[cid_x0 + 0x2700]()
    //     0xb5ade0: movz            x17, #0x2700
    //     0xb5ade4: add             lr, x0, x17
    //     0xb5ade8: ldr             lr, [x21, lr, lsl #3]
    //     0xb5adec: blr             lr
    // 0xb5adf0: ldur            x1, [fp, #-8]
    // 0xb5adf4: StoreField: r1->field_13 = r0
    //     0xb5adf4: stur            w0, [x1, #0x13]
    //     0xb5adf8: ldurb           w16, [x1, #-1]
    //     0xb5adfc: ldurb           w17, [x0, #-1]
    //     0xb5ae00: and             x16, x17, x16, lsr #2
    //     0xb5ae04: tst             x16, HEAP, lsr #32
    //     0xb5ae08: b.eq            #0xb5ae10
    //     0xb5ae0c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xb5ae10: r0 = Null
    //     0xb5ae10: mov             x0, NULL
    // 0xb5ae14: LeaveFrame
    //     0xb5ae14: mov             SP, fp
    //     0xb5ae18: ldp             fp, lr, [SP], #0x10
    // 0xb5ae1c: ret
    //     0xb5ae1c: ret             
    // 0xb5ae20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb5ae20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb5ae24: b               #0xb5a8e8
    // 0xb5ae28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ae28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5ae2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ae2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5ae30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ae30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5ae34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ae34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb5ae38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb5ae38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4085, size: 0x20, field offset: 0xc
//   const constructor, 
class CustomizationSingleSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f148, size: 0x30
    // 0xc7f148: EnterFrame
    //     0xc7f148: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f14c: mov             fp, SP
    // 0xc7f150: mov             x0, x1
    // 0xc7f154: r1 = <CustomizationSingleSelect>
    //     0xc7f154: add             x1, PP, #0x61, lsl #12  ; [pp+0x61e10] TypeArguments: <CustomizationSingleSelect>
    //     0xc7f158: ldr             x1, [x1, #0xe10]
    // 0xc7f15c: r0 = _CustomizationSingleSelectState()
    //     0xc7f15c: bl              #0xc7f178  ; Allocate_CustomizationSingleSelectStateStub -> _CustomizationSingleSelectState (size=0x20)
    // 0xc7f160: r1 = ""
    //     0xc7f160: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc7f164: StoreField: r0->field_13 = r1
    //     0xc7f164: stur            w1, [x0, #0x13]
    // 0xc7f168: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc7f168: stur            xzr, [x0, #0x17]
    // 0xc7f16c: LeaveFrame
    //     0xc7f16c: mov             SP, fp
    //     0xc7f170: ldp             fp, lr, [SP], #0x10
    // 0xc7f174: ret
    //     0xc7f174: ret             
  }
}
