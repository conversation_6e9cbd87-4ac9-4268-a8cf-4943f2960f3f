// lib: , url: package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart

// class id: 1049561, size: 0x8
class :: {
}

// class id: 3222, size: 0x2c, field offset: 0x14
class _ProductSelectSizeBottomSheetState extends State<dynamic> {

  late AllSkuDatum dropDownValue; // offset: 0x14

  [closure] bool <anonymous closure>(dynamic, AllSkuDatum?) {
    // ** addr: 0x934a14, size: 0xbc
    // 0x934a14: EnterFrame
    //     0x934a14: stp             fp, lr, [SP, #-0x10]!
    //     0x934a18: mov             fp, SP
    // 0x934a1c: AllocStack(0x10)
    //     0x934a1c: sub             SP, SP, #0x10
    // 0x934a20: SetupParameters()
    //     0x934a20: ldr             x0, [fp, #0x18]
    //     0x934a24: ldur            w1, [x0, #0x17]
    //     0x934a28: add             x1, x1, HEAP, lsl #32
    // 0x934a2c: CheckStackOverflow
    //     0x934a2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934a30: cmp             SP, x16
    //     0x934a34: b.ls            #0x934ac4
    // 0x934a38: ldr             x0, [fp, #0x10]
    // 0x934a3c: cmp             w0, NULL
    // 0x934a40: b.ne            #0x934a4c
    // 0x934a44: r0 = Null
    //     0x934a44: mov             x0, NULL
    // 0x934a48: b               #0x934a58
    // 0x934a4c: LoadField: r2 = r0->field_b
    //     0x934a4c: ldur            w2, [x0, #0xb]
    // 0x934a50: DecompressPointer r2
    //     0x934a50: add             x2, x2, HEAP, lsl #32
    // 0x934a54: mov             x0, x2
    // 0x934a58: LoadField: r2 = r1->field_f
    //     0x934a58: ldur            w2, [x1, #0xf]
    // 0x934a5c: DecompressPointer r2
    //     0x934a5c: add             x2, x2, HEAP, lsl #32
    // 0x934a60: LoadField: r1 = r2->field_b
    //     0x934a60: ldur            w1, [x2, #0xb]
    // 0x934a64: DecompressPointer r1
    //     0x934a64: add             x1, x1, HEAP, lsl #32
    // 0x934a68: cmp             w1, NULL
    // 0x934a6c: b.eq            #0x934acc
    // 0x934a70: LoadField: r2 = r1->field_b
    //     0x934a70: ldur            w2, [x1, #0xb]
    // 0x934a74: DecompressPointer r2
    //     0x934a74: add             x2, x2, HEAP, lsl #32
    // 0x934a78: LoadField: r1 = r2->field_eb
    //     0x934a78: ldur            w1, [x2, #0xeb]
    // 0x934a7c: DecompressPointer r1
    //     0x934a7c: add             x1, x1, HEAP, lsl #32
    // 0x934a80: cmp             w1, NULL
    // 0x934a84: b.ne            #0x934a90
    // 0x934a88: r1 = Null
    //     0x934a88: mov             x1, NULL
    // 0x934a8c: b               #0x934a9c
    // 0x934a90: LoadField: r2 = r1->field_23
    //     0x934a90: ldur            w2, [x1, #0x23]
    // 0x934a94: DecompressPointer r2
    //     0x934a94: add             x2, x2, HEAP, lsl #32
    // 0x934a98: mov             x1, x2
    // 0x934a9c: r2 = LoadClassIdInstr(r0)
    //     0x934a9c: ldur            x2, [x0, #-1]
    //     0x934aa0: ubfx            x2, x2, #0xc, #0x14
    // 0x934aa4: stp             x1, x0, [SP]
    // 0x934aa8: mov             x0, x2
    // 0x934aac: mov             lr, x0
    // 0x934ab0: ldr             lr, [x21, lr, lsl #3]
    // 0x934ab4: blr             lr
    // 0x934ab8: LeaveFrame
    //     0x934ab8: mov             SP, fp
    //     0x934abc: ldp             fp, lr, [SP], #0x10
    // 0x934ac0: ret
    //     0x934ac0: ret             
    // 0x934ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934ac4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x934ac8: b               #0x934a38
    // 0x934acc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x934acc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, AllGroupedSkusDatum?) {
    // ** addr: 0x934af0, size: 0xbc
    // 0x934af0: EnterFrame
    //     0x934af0: stp             fp, lr, [SP, #-0x10]!
    //     0x934af4: mov             fp, SP
    // 0x934af8: AllocStack(0x10)
    //     0x934af8: sub             SP, SP, #0x10
    // 0x934afc: SetupParameters()
    //     0x934afc: ldr             x0, [fp, #0x18]
    //     0x934b00: ldur            w1, [x0, #0x17]
    //     0x934b04: add             x1, x1, HEAP, lsl #32
    // 0x934b08: CheckStackOverflow
    //     0x934b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934b0c: cmp             SP, x16
    //     0x934b10: b.ls            #0x934ba0
    // 0x934b14: ldr             x0, [fp, #0x10]
    // 0x934b18: cmp             w0, NULL
    // 0x934b1c: b.ne            #0x934b28
    // 0x934b20: r0 = Null
    //     0x934b20: mov             x0, NULL
    // 0x934b24: b               #0x934b34
    // 0x934b28: LoadField: r2 = r0->field_7
    //     0x934b28: ldur            w2, [x0, #7]
    // 0x934b2c: DecompressPointer r2
    //     0x934b2c: add             x2, x2, HEAP, lsl #32
    // 0x934b30: mov             x0, x2
    // 0x934b34: LoadField: r2 = r1->field_f
    //     0x934b34: ldur            w2, [x1, #0xf]
    // 0x934b38: DecompressPointer r2
    //     0x934b38: add             x2, x2, HEAP, lsl #32
    // 0x934b3c: LoadField: r1 = r2->field_b
    //     0x934b3c: ldur            w1, [x2, #0xb]
    // 0x934b40: DecompressPointer r1
    //     0x934b40: add             x1, x1, HEAP, lsl #32
    // 0x934b44: cmp             w1, NULL
    // 0x934b48: b.eq            #0x934ba8
    // 0x934b4c: LoadField: r2 = r1->field_b
    //     0x934b4c: ldur            w2, [x1, #0xb]
    // 0x934b50: DecompressPointer r2
    //     0x934b50: add             x2, x2, HEAP, lsl #32
    // 0x934b54: LoadField: r1 = r2->field_eb
    //     0x934b54: ldur            w1, [x2, #0xeb]
    // 0x934b58: DecompressPointer r1
    //     0x934b58: add             x1, x1, HEAP, lsl #32
    // 0x934b5c: cmp             w1, NULL
    // 0x934b60: b.ne            #0x934b6c
    // 0x934b64: r1 = Null
    //     0x934b64: mov             x1, NULL
    // 0x934b68: b               #0x934b78
    // 0x934b6c: LoadField: r2 = r1->field_23
    //     0x934b6c: ldur            w2, [x1, #0x23]
    // 0x934b70: DecompressPointer r2
    //     0x934b70: add             x2, x2, HEAP, lsl #32
    // 0x934b74: mov             x1, x2
    // 0x934b78: r2 = LoadClassIdInstr(r0)
    //     0x934b78: ldur            x2, [x0, #-1]
    //     0x934b7c: ubfx            x2, x2, #0xc, #0x14
    // 0x934b80: stp             x1, x0, [SP]
    // 0x934b84: mov             x0, x2
    // 0x934b88: mov             lr, x0
    // 0x934b8c: ldr             lr, [x21, lr, lsl #3]
    // 0x934b90: blr             lr
    // 0x934b94: LeaveFrame
    //     0x934b94: mov             SP, fp
    //     0x934b98: ldp             fp, lr, [SP], #0x10
    // 0x934b9c: ret
    //     0x934b9c: ret             
    // 0x934ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934ba0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x934ba4: b               #0x934b14
    // 0x934ba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x934ba8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x94aa18, size: 0x51c
    // 0x94aa18: EnterFrame
    //     0x94aa18: stp             fp, lr, [SP, #-0x10]!
    //     0x94aa1c: mov             fp, SP
    // 0x94aa20: AllocStack(0x38)
    //     0x94aa20: sub             SP, SP, #0x38
    // 0x94aa24: SetupParameters(_ProductSelectSizeBottomSheetState this /* r1 => r1, fp-0x8 */)
    //     0x94aa24: stur            x1, [fp, #-8]
    // 0x94aa28: CheckStackOverflow
    //     0x94aa28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94aa2c: cmp             SP, x16
    //     0x94aa30: b.ls            #0x94af08
    // 0x94aa34: r1 = 1
    //     0x94aa34: movz            x1, #0x1
    // 0x94aa38: r0 = AllocateContext()
    //     0x94aa38: bl              #0x16f6108  ; AllocateContextStub
    // 0x94aa3c: mov             x1, x0
    // 0x94aa40: ldur            x0, [fp, #-8]
    // 0x94aa44: stur            x1, [fp, #-0x10]
    // 0x94aa48: StoreField: r1->field_f = r0
    //     0x94aa48: stur            w0, [x1, #0xf]
    // 0x94aa4c: LoadField: r2 = r0->field_b
    //     0x94aa4c: ldur            w2, [x0, #0xb]
    // 0x94aa50: DecompressPointer r2
    //     0x94aa50: add             x2, x2, HEAP, lsl #32
    // 0x94aa54: cmp             w2, NULL
    // 0x94aa58: b.eq            #0x94af10
    // 0x94aa5c: LoadField: r3 = r2->field_b
    //     0x94aa5c: ldur            w3, [x2, #0xb]
    // 0x94aa60: DecompressPointer r3
    //     0x94aa60: add             x3, x3, HEAP, lsl #32
    // 0x94aa64: LoadField: r2 = r3->field_db
    //     0x94aa64: ldur            w2, [x3, #0xdb]
    // 0x94aa68: DecompressPointer r2
    //     0x94aa68: add             x2, x2, HEAP, lsl #32
    // 0x94aa6c: cmp             w2, NULL
    // 0x94aa70: b.ne            #0x94aa7c
    // 0x94aa74: r4 = Null
    //     0x94aa74: mov             x4, NULL
    // 0x94aa78: b               #0x94aa94
    // 0x94aa7c: LoadField: r4 = r2->field_b
    //     0x94aa7c: ldur            w4, [x2, #0xb]
    // 0x94aa80: cbnz            w4, #0x94aa8c
    // 0x94aa84: r5 = false
    //     0x94aa84: add             x5, NULL, #0x30  ; false
    // 0x94aa88: b               #0x94aa90
    // 0x94aa8c: r5 = true
    //     0x94aa8c: add             x5, NULL, #0x20  ; true
    // 0x94aa90: mov             x4, x5
    // 0x94aa94: cmp             w4, NULL
    // 0x94aa98: b.ne            #0x94aaa4
    // 0x94aa9c: mov             x1, x0
    // 0x94aaa0: b               #0x94ad64
    // 0x94aaa4: tbnz            w4, #4, #0x94ad60
    // 0x94aaa8: cmp             w2, NULL
    // 0x94aaac: b.ne            #0x94aab8
    // 0x94aab0: r2 = Null
    //     0x94aab0: mov             x2, NULL
    // 0x94aab4: b               #0x94ab10
    // 0x94aab8: r16 = <AllGroupedSkusDatum?>
    //     0x94aab8: add             x16, PP, #0x52, lsl #12  ; [pp+0x52850] TypeArguments: <AllGroupedSkusDatum?>
    //     0x94aabc: ldr             x16, [x16, #0x850]
    // 0x94aac0: stp             x2, x16, [SP]
    // 0x94aac4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x94aac4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x94aac8: r0 = cast()
    //     0x94aac8: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x94aacc: ldur            x2, [fp, #-0x10]
    // 0x94aad0: r1 = Function '<anonymous closure>':.
    //     0x94aad0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52858] AnonymousClosure: (0x934af0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::initState (0x94aa18)
    //     0x94aad4: ldr             x1, [x1, #0x858]
    // 0x94aad8: stur            x0, [fp, #-0x18]
    // 0x94aadc: r0 = AllocateClosure()
    //     0x94aadc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94aae0: r1 = Function '<anonymous closure>':.
    //     0x94aae0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52860] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x94aae4: ldr             x1, [x1, #0x860]
    // 0x94aae8: r2 = Null
    //     0x94aae8: mov             x2, NULL
    // 0x94aaec: stur            x0, [fp, #-0x20]
    // 0x94aaf0: r0 = AllocateClosure()
    //     0x94aaf0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94aaf4: str             x0, [SP]
    // 0x94aaf8: ldur            x1, [fp, #-0x18]
    // 0x94aafc: ldur            x2, [fp, #-0x20]
    // 0x94ab00: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x94ab00: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x94ab04: ldr             x4, [x4, #0xb48]
    // 0x94ab08: r0 = firstWhere()
    //     0x94ab08: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x94ab0c: mov             x2, x0
    // 0x94ab10: cmp             w2, NULL
    // 0x94ab14: b.eq            #0x94ab9c
    // 0x94ab18: ldur            x0, [fp, #-8]
    // 0x94ab1c: LoadField: r1 = r0->field_b
    //     0x94ab1c: ldur            w1, [x0, #0xb]
    // 0x94ab20: DecompressPointer r1
    //     0x94ab20: add             x1, x1, HEAP, lsl #32
    // 0x94ab24: cmp             w1, NULL
    // 0x94ab28: b.eq            #0x94af14
    // 0x94ab2c: LoadField: r3 = r1->field_b
    //     0x94ab2c: ldur            w3, [x1, #0xb]
    // 0x94ab30: DecompressPointer r3
    //     0x94ab30: add             x3, x3, HEAP, lsl #32
    // 0x94ab34: LoadField: r1 = r3->field_db
    //     0x94ab34: ldur            w1, [x3, #0xdb]
    // 0x94ab38: DecompressPointer r1
    //     0x94ab38: add             x1, x1, HEAP, lsl #32
    // 0x94ab3c: cmp             w1, NULL
    // 0x94ab40: b.ne            #0x94ab4c
    // 0x94ab44: r0 = Null
    //     0x94ab44: mov             x0, NULL
    // 0x94ab48: b               #0x94ab6c
    // 0x94ab4c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x94ab4c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x94ab50: r0 = indexOf()
    //     0x94ab50: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x94ab54: mov             x2, x0
    // 0x94ab58: r0 = BoxInt64Instr(r2)
    //     0x94ab58: sbfiz           x0, x2, #1, #0x1f
    //     0x94ab5c: cmp             x2, x0, asr #1
    //     0x94ab60: b.eq            #0x94ab6c
    //     0x94ab64: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94ab68: stur            x2, [x0, #7]
    // 0x94ab6c: cmp             w0, NULL
    // 0x94ab70: b.ne            #0x94ab7c
    // 0x94ab74: r0 = 0
    //     0x94ab74: movz            x0, #0
    // 0x94ab78: b               #0x94ab8c
    // 0x94ab7c: r1 = LoadInt32Instr(r0)
    //     0x94ab7c: sbfx            x1, x0, #1, #0x1f
    //     0x94ab80: tbz             w0, #0, #0x94ab88
    //     0x94ab84: ldur            x1, [x0, #7]
    // 0x94ab88: mov             x0, x1
    // 0x94ab8c: ldur            x2, [fp, #-8]
    // 0x94ab90: ArrayStore: r2[0] = r0  ; List_8
    //     0x94ab90: stur            x0, [x2, #0x17]
    // 0x94ab94: mov             x3, x0
    // 0x94ab98: b               #0x94aba8
    // 0x94ab9c: ldur            x2, [fp, #-8]
    // 0x94aba0: ArrayStore: r2[0] = rZR  ; List_8
    //     0x94aba0: stur            xzr, [x2, #0x17]
    // 0x94aba4: r3 = 0
    //     0x94aba4: movz            x3, #0
    // 0x94aba8: stur            x3, [fp, #-0x28]
    // 0x94abac: LoadField: r0 = r2->field_b
    //     0x94abac: ldur            w0, [x2, #0xb]
    // 0x94abb0: DecompressPointer r0
    //     0x94abb0: add             x0, x0, HEAP, lsl #32
    // 0x94abb4: cmp             w0, NULL
    // 0x94abb8: b.eq            #0x94af18
    // 0x94abbc: LoadField: r1 = r0->field_b
    //     0x94abbc: ldur            w1, [x0, #0xb]
    // 0x94abc0: DecompressPointer r1
    //     0x94abc0: add             x1, x1, HEAP, lsl #32
    // 0x94abc4: LoadField: r4 = r1->field_db
    //     0x94abc4: ldur            w4, [x1, #0xdb]
    // 0x94abc8: DecompressPointer r4
    //     0x94abc8: add             x4, x4, HEAP, lsl #32
    // 0x94abcc: stur            x4, [fp, #-0x18]
    // 0x94abd0: cmp             w4, NULL
    // 0x94abd4: b.ne            #0x94abe0
    // 0x94abd8: r0 = Null
    //     0x94abd8: mov             x0, NULL
    // 0x94abdc: b               #0x94ac54
    // 0x94abe0: LoadField: r0 = r4->field_b
    //     0x94abe0: ldur            w0, [x4, #0xb]
    // 0x94abe4: r1 = LoadInt32Instr(r0)
    //     0x94abe4: sbfx            x1, x0, #1, #0x1f
    // 0x94abe8: mov             x0, x1
    // 0x94abec: mov             x1, x3
    // 0x94abf0: cmp             x1, x0
    // 0x94abf4: b.hs            #0x94af1c
    // 0x94abf8: LoadField: r0 = r4->field_f
    //     0x94abf8: ldur            w0, [x4, #0xf]
    // 0x94abfc: DecompressPointer r0
    //     0x94abfc: add             x0, x0, HEAP, lsl #32
    // 0x94ac00: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x94ac00: add             x16, x0, x3, lsl #2
    //     0x94ac04: ldur            w1, [x16, #0xf]
    // 0x94ac08: DecompressPointer r1
    //     0x94ac08: add             x1, x1, HEAP, lsl #32
    // 0x94ac0c: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x94ac0c: ldur            w5, [x1, #0x17]
    // 0x94ac10: DecompressPointer r5
    //     0x94ac10: add             x5, x5, HEAP, lsl #32
    // 0x94ac14: cmp             w5, NULL
    // 0x94ac18: b.ne            #0x94ac24
    // 0x94ac1c: r0 = Null
    //     0x94ac1c: mov             x0, NULL
    // 0x94ac20: b               #0x94ac54
    // 0x94ac24: LoadField: r0 = r5->field_b
    //     0x94ac24: ldur            w0, [x5, #0xb]
    // 0x94ac28: r1 = LoadInt32Instr(r0)
    //     0x94ac28: sbfx            x1, x0, #1, #0x1f
    // 0x94ac2c: mov             x0, x1
    // 0x94ac30: mov             x1, x3
    // 0x94ac34: cmp             x1, x0
    // 0x94ac38: b.hs            #0x94af20
    // 0x94ac3c: LoadField: r0 = r5->field_f
    //     0x94ac3c: ldur            w0, [x5, #0xf]
    // 0x94ac40: DecompressPointer r0
    //     0x94ac40: add             x0, x0, HEAP, lsl #32
    // 0x94ac44: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x94ac44: add             x16, x0, x3, lsl #2
    //     0x94ac48: ldur            w1, [x16, #0xf]
    // 0x94ac4c: DecompressPointer r1
    //     0x94ac4c: add             x1, x1, HEAP, lsl #32
    // 0x94ac50: mov             x0, x1
    // 0x94ac54: cmp             w0, NULL
    // 0x94ac58: b.ne            #0x94ac60
    // 0x94ac5c: r0 = AllSkuDatum()
    //     0x94ac5c: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x94ac60: ldur            x1, [fp, #-8]
    // 0x94ac64: ldur            x2, [fp, #-0x18]
    // 0x94ac68: StoreField: r1->field_13 = r0
    //     0x94ac68: stur            w0, [x1, #0x13]
    //     0x94ac6c: ldurb           w16, [x1, #-1]
    //     0x94ac70: ldurb           w17, [x0, #-1]
    //     0x94ac74: and             x16, x17, x16, lsr #2
    //     0x94ac78: tst             x16, HEAP, lsr #32
    //     0x94ac7c: b.eq            #0x94ac84
    //     0x94ac80: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94ac84: LoadField: r3 = r1->field_27
    //     0x94ac84: ldur            w3, [x1, #0x27]
    // 0x94ac88: DecompressPointer r3
    //     0x94ac88: add             x3, x3, HEAP, lsl #32
    // 0x94ac8c: stur            x3, [fp, #-0x20]
    // 0x94ac90: cmp             w2, NULL
    // 0x94ac94: b.ne            #0x94aca0
    // 0x94ac98: r0 = Null
    //     0x94ac98: mov             x0, NULL
    // 0x94ac9c: b               #0x94acd8
    // 0x94aca0: ldur            x4, [fp, #-0x28]
    // 0x94aca4: LoadField: r0 = r2->field_b
    //     0x94aca4: ldur            w0, [x2, #0xb]
    // 0x94aca8: r1 = LoadInt32Instr(r0)
    //     0x94aca8: sbfx            x1, x0, #1, #0x1f
    // 0x94acac: mov             x0, x1
    // 0x94acb0: mov             x1, x4
    // 0x94acb4: cmp             x1, x0
    // 0x94acb8: b.hs            #0x94af24
    // 0x94acbc: LoadField: r0 = r2->field_f
    //     0x94acbc: ldur            w0, [x2, #0xf]
    // 0x94acc0: DecompressPointer r0
    //     0x94acc0: add             x0, x0, HEAP, lsl #32
    // 0x94acc4: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x94acc4: add             x16, x0, x4, lsl #2
    //     0x94acc8: ldur            w1, [x16, #0xf]
    // 0x94accc: DecompressPointer r1
    //     0x94accc: add             x1, x1, HEAP, lsl #32
    // 0x94acd0: LoadField: r0 = r1->field_7
    //     0x94acd0: ldur            w0, [x1, #7]
    // 0x94acd4: DecompressPointer r0
    //     0x94acd4: add             x0, x0, HEAP, lsl #32
    // 0x94acd8: cmp             w0, NULL
    // 0x94acdc: b.ne            #0x94ace4
    // 0x94ace0: r0 = ""
    //     0x94ace0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x94ace4: stur            x0, [fp, #-0x18]
    // 0x94ace8: LoadField: r1 = r3->field_b
    //     0x94ace8: ldur            w1, [x3, #0xb]
    // 0x94acec: LoadField: r2 = r3->field_f
    //     0x94acec: ldur            w2, [x3, #0xf]
    // 0x94acf0: DecompressPointer r2
    //     0x94acf0: add             x2, x2, HEAP, lsl #32
    // 0x94acf4: LoadField: r4 = r2->field_b
    //     0x94acf4: ldur            w4, [x2, #0xb]
    // 0x94acf8: r2 = LoadInt32Instr(r1)
    //     0x94acf8: sbfx            x2, x1, #1, #0x1f
    // 0x94acfc: stur            x2, [fp, #-0x28]
    // 0x94ad00: r1 = LoadInt32Instr(r4)
    //     0x94ad00: sbfx            x1, x4, #1, #0x1f
    // 0x94ad04: cmp             x2, x1
    // 0x94ad08: b.ne            #0x94ad14
    // 0x94ad0c: mov             x1, x3
    // 0x94ad10: r0 = _growToNextCapacity()
    //     0x94ad10: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x94ad14: ldur            x0, [fp, #-0x20]
    // 0x94ad18: ldur            x2, [fp, #-0x28]
    // 0x94ad1c: add             x1, x2, #1
    // 0x94ad20: lsl             x3, x1, #1
    // 0x94ad24: StoreField: r0->field_b = r3
    //     0x94ad24: stur            w3, [x0, #0xb]
    // 0x94ad28: LoadField: r1 = r0->field_f
    //     0x94ad28: ldur            w1, [x0, #0xf]
    // 0x94ad2c: DecompressPointer r1
    //     0x94ad2c: add             x1, x1, HEAP, lsl #32
    // 0x94ad30: ldur            x0, [fp, #-0x18]
    // 0x94ad34: ArrayStore: r1[r2] = r0  ; List_4
    //     0x94ad34: add             x25, x1, x2, lsl #2
    //     0x94ad38: add             x25, x25, #0xf
    //     0x94ad3c: str             w0, [x25]
    //     0x94ad40: tbz             w0, #0, #0x94ad5c
    //     0x94ad44: ldurb           w16, [x1, #-1]
    //     0x94ad48: ldurb           w17, [x0, #-1]
    //     0x94ad4c: and             x16, x17, x16, lsr #2
    //     0x94ad50: tst             x16, HEAP, lsr #32
    //     0x94ad54: b.eq            #0x94ad5c
    //     0x94ad58: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x94ad5c: b               #0x94aef8
    // 0x94ad60: mov             x1, x0
    // 0x94ad64: LoadField: r0 = r3->field_d7
    //     0x94ad64: ldur            w0, [x3, #0xd7]
    // 0x94ad68: DecompressPointer r0
    //     0x94ad68: add             x0, x0, HEAP, lsl #32
    // 0x94ad6c: cmp             w0, NULL
    // 0x94ad70: b.ne            #0x94ad7c
    // 0x94ad74: r2 = Null
    //     0x94ad74: mov             x2, NULL
    // 0x94ad78: b               #0x94add4
    // 0x94ad7c: r16 = <AllSkuDatum?>
    //     0x94ad7c: add             x16, PP, #0x52, lsl #12  ; [pp+0x52868] TypeArguments: <AllSkuDatum?>
    //     0x94ad80: ldr             x16, [x16, #0x868]
    // 0x94ad84: stp             x0, x16, [SP]
    // 0x94ad88: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x94ad88: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x94ad8c: r0 = cast()
    //     0x94ad8c: bl              #0x7d9dc8  ; [dart:collection] ListBase::cast
    // 0x94ad90: ldur            x2, [fp, #-0x10]
    // 0x94ad94: r1 = Function '<anonymous closure>':.
    //     0x94ad94: add             x1, PP, #0x52, lsl #12  ; [pp+0x52870] AnonymousClosure: (0x934a14), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::initState (0x94aa18)
    //     0x94ad98: ldr             x1, [x1, #0x870]
    // 0x94ad9c: stur            x0, [fp, #-0x10]
    // 0x94ada0: r0 = AllocateClosure()
    //     0x94ada0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94ada4: r1 = Function '<anonymous closure>':.
    //     0x94ada4: add             x1, PP, #0x52, lsl #12  ; [pp+0x52878] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0x94ada8: ldr             x1, [x1, #0x878]
    // 0x94adac: r2 = Null
    //     0x94adac: mov             x2, NULL
    // 0x94adb0: stur            x0, [fp, #-0x18]
    // 0x94adb4: r0 = AllocateClosure()
    //     0x94adb4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x94adb8: str             x0, [SP]
    // 0x94adbc: ldur            x1, [fp, #-0x10]
    // 0x94adc0: ldur            x2, [fp, #-0x18]
    // 0x94adc4: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x94adc4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x94adc8: ldr             x4, [x4, #0xb48]
    // 0x94adcc: r0 = firstWhere()
    //     0x94adcc: bl              #0x908130  ; [dart:_internal] __CastListBase&_CastIterableBase&ListMixin::firstWhere
    // 0x94add0: mov             x2, x0
    // 0x94add4: cmp             w2, NULL
    // 0x94add8: b.eq            #0x94ae60
    // 0x94addc: ldur            x0, [fp, #-8]
    // 0x94ade0: LoadField: r1 = r0->field_b
    //     0x94ade0: ldur            w1, [x0, #0xb]
    // 0x94ade4: DecompressPointer r1
    //     0x94ade4: add             x1, x1, HEAP, lsl #32
    // 0x94ade8: cmp             w1, NULL
    // 0x94adec: b.eq            #0x94af28
    // 0x94adf0: LoadField: r3 = r1->field_b
    //     0x94adf0: ldur            w3, [x1, #0xb]
    // 0x94adf4: DecompressPointer r3
    //     0x94adf4: add             x3, x3, HEAP, lsl #32
    // 0x94adf8: LoadField: r1 = r3->field_d7
    //     0x94adf8: ldur            w1, [x3, #0xd7]
    // 0x94adfc: DecompressPointer r1
    //     0x94adfc: add             x1, x1, HEAP, lsl #32
    // 0x94ae00: cmp             w1, NULL
    // 0x94ae04: b.ne            #0x94ae10
    // 0x94ae08: r0 = Null
    //     0x94ae08: mov             x0, NULL
    // 0x94ae0c: b               #0x94ae30
    // 0x94ae10: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x94ae10: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x94ae14: r0 = indexOf()
    //     0x94ae14: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0x94ae18: mov             x2, x0
    // 0x94ae1c: r0 = BoxInt64Instr(r2)
    //     0x94ae1c: sbfiz           x0, x2, #1, #0x1f
    //     0x94ae20: cmp             x2, x0, asr #1
    //     0x94ae24: b.eq            #0x94ae30
    //     0x94ae28: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94ae2c: stur            x2, [x0, #7]
    // 0x94ae30: cmp             w0, NULL
    // 0x94ae34: b.ne            #0x94ae40
    // 0x94ae38: r0 = 0
    //     0x94ae38: movz            x0, #0
    // 0x94ae3c: b               #0x94ae50
    // 0x94ae40: r1 = LoadInt32Instr(r0)
    //     0x94ae40: sbfx            x1, x0, #1, #0x1f
    //     0x94ae44: tbz             w0, #0, #0x94ae4c
    //     0x94ae48: ldur            x1, [x0, #7]
    // 0x94ae4c: mov             x0, x1
    // 0x94ae50: ldur            x2, [fp, #-8]
    // 0x94ae54: ArrayStore: r2[0] = r0  ; List_8
    //     0x94ae54: stur            x0, [x2, #0x17]
    // 0x94ae58: mov             x3, x0
    // 0x94ae5c: b               #0x94ae6c
    // 0x94ae60: ldur            x2, [fp, #-8]
    // 0x94ae64: ArrayStore: r2[0] = rZR  ; List_8
    //     0x94ae64: stur            xzr, [x2, #0x17]
    // 0x94ae68: r3 = 0
    //     0x94ae68: movz            x3, #0
    // 0x94ae6c: LoadField: r0 = r2->field_b
    //     0x94ae6c: ldur            w0, [x2, #0xb]
    // 0x94ae70: DecompressPointer r0
    //     0x94ae70: add             x0, x0, HEAP, lsl #32
    // 0x94ae74: cmp             w0, NULL
    // 0x94ae78: b.eq            #0x94af2c
    // 0x94ae7c: LoadField: r1 = r0->field_b
    //     0x94ae7c: ldur            w1, [x0, #0xb]
    // 0x94ae80: DecompressPointer r1
    //     0x94ae80: add             x1, x1, HEAP, lsl #32
    // 0x94ae84: LoadField: r4 = r1->field_d7
    //     0x94ae84: ldur            w4, [x1, #0xd7]
    // 0x94ae88: DecompressPointer r4
    //     0x94ae88: add             x4, x4, HEAP, lsl #32
    // 0x94ae8c: cmp             w4, NULL
    // 0x94ae90: b.ne            #0x94ae9c
    // 0x94ae94: r0 = Null
    //     0x94ae94: mov             x0, NULL
    // 0x94ae98: b               #0x94aecc
    // 0x94ae9c: LoadField: r0 = r4->field_b
    //     0x94ae9c: ldur            w0, [x4, #0xb]
    // 0x94aea0: r1 = LoadInt32Instr(r0)
    //     0x94aea0: sbfx            x1, x0, #1, #0x1f
    // 0x94aea4: mov             x0, x1
    // 0x94aea8: mov             x1, x3
    // 0x94aeac: cmp             x1, x0
    // 0x94aeb0: b.hs            #0x94af30
    // 0x94aeb4: LoadField: r0 = r4->field_f
    //     0x94aeb4: ldur            w0, [x4, #0xf]
    // 0x94aeb8: DecompressPointer r0
    //     0x94aeb8: add             x0, x0, HEAP, lsl #32
    // 0x94aebc: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0x94aebc: add             x16, x0, x3, lsl #2
    //     0x94aec0: ldur            w1, [x16, #0xf]
    // 0x94aec4: DecompressPointer r1
    //     0x94aec4: add             x1, x1, HEAP, lsl #32
    // 0x94aec8: mov             x0, x1
    // 0x94aecc: cmp             w0, NULL
    // 0x94aed0: b.ne            #0x94aed8
    // 0x94aed4: r0 = AllSkuDatum()
    //     0x94aed4: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0x94aed8: ldur            x1, [fp, #-8]
    // 0x94aedc: StoreField: r1->field_13 = r0
    //     0x94aedc: stur            w0, [x1, #0x13]
    //     0x94aee0: ldurb           w16, [x1, #-1]
    //     0x94aee4: ldurb           w17, [x0, #-1]
    //     0x94aee8: and             x16, x17, x16, lsr #2
    //     0x94aeec: tst             x16, HEAP, lsr #32
    //     0x94aef0: b.eq            #0x94aef8
    //     0x94aef4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x94aef8: r0 = Null
    //     0x94aef8: mov             x0, NULL
    // 0x94aefc: LeaveFrame
    //     0x94aefc: mov             SP, fp
    //     0x94af00: ldp             fp, lr, [SP], #0x10
    // 0x94af04: ret
    //     0x94af04: ret             
    // 0x94af08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94af08: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94af0c: b               #0x94aa34
    // 0x94af10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94af10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94af14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94af14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94af18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94af18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94af1c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94af1c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x94af20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94af20: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x94af24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94af24: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x94af28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94af28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94af2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94af2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x94af30: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94af30: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x997c68, size: 0x60
    // 0x997c68: EnterFrame
    //     0x997c68: stp             fp, lr, [SP, #-0x10]!
    //     0x997c6c: mov             fp, SP
    // 0x997c70: AllocStack(0x10)
    //     0x997c70: sub             SP, SP, #0x10
    // 0x997c74: SetupParameters()
    //     0x997c74: ldr             x0, [fp, #0x10]
    //     0x997c78: ldur            w1, [x0, #0x17]
    //     0x997c7c: add             x1, x1, HEAP, lsl #32
    // 0x997c80: CheckStackOverflow
    //     0x997c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x997c84: cmp             SP, x16
    //     0x997c88: b.ls            #0x997cc0
    // 0x997c8c: LoadField: r0 = r1->field_13
    //     0x997c8c: ldur            w0, [x1, #0x13]
    // 0x997c90: DecompressPointer r0
    //     0x997c90: add             x0, x0, HEAP, lsl #32
    // 0x997c94: mov             x1, x0
    // 0x997c98: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x997c98: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x997c9c: r0 = of()
    //     0x997c9c: bl              #0x7f79ac  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x997ca0: r16 = <Object?>
    //     0x997ca0: ldr             x16, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    // 0x997ca4: stp             x0, x16, [SP]
    // 0x997ca8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x997ca8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x997cac: r0 = pop()
    //     0x997cac: bl              #0x67c754  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0x997cb0: r0 = Null
    //     0x997cb0: mov             x0, NULL
    // 0x997cb4: LeaveFrame
    //     0x997cb4: mov             SP, fp
    //     0x997cb8: ldp             fp, lr, [SP], #0x10
    // 0x997cbc: ret
    //     0x997cbc: ret             
    // 0x997cc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x997cc0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x997cc4: b               #0x997c8c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa84cc8, size: 0x408
    // 0xa84cc8: EnterFrame
    //     0xa84cc8: stp             fp, lr, [SP, #-0x10]!
    //     0xa84ccc: mov             fp, SP
    // 0xa84cd0: AllocStack(0x28)
    //     0xa84cd0: sub             SP, SP, #0x28
    // 0xa84cd4: SetupParameters()
    //     0xa84cd4: ldr             x0, [fp, #0x10]
    //     0xa84cd8: ldur            w2, [x0, #0x17]
    //     0xa84cdc: add             x2, x2, HEAP, lsl #32
    //     0xa84ce0: stur            x2, [fp, #-0x10]
    // 0xa84ce4: CheckStackOverflow
    //     0xa84ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84ce8: cmp             SP, x16
    //     0xa84cec: b.ls            #0xa850a4
    // 0xa84cf0: LoadField: r0 = r2->field_b
    //     0xa84cf0: ldur            w0, [x2, #0xb]
    // 0xa84cf4: DecompressPointer r0
    //     0xa84cf4: add             x0, x0, HEAP, lsl #32
    // 0xa84cf8: stur            x0, [fp, #-8]
    // 0xa84cfc: LoadField: r1 = r0->field_f
    //     0xa84cfc: ldur            w1, [x0, #0xf]
    // 0xa84d00: DecompressPointer r1
    //     0xa84d00: add             x1, x1, HEAP, lsl #32
    // 0xa84d04: LoadField: r3 = r1->field_27
    //     0xa84d04: ldur            w3, [x1, #0x27]
    // 0xa84d08: DecompressPointer r3
    //     0xa84d08: add             x3, x3, HEAP, lsl #32
    // 0xa84d0c: mov             x1, x3
    // 0xa84d10: r0 = clear()
    //     0xa84d10: bl              #0x65b440  ; [dart:core] _GrowableList::clear
    // 0xa84d14: ldur            x3, [fp, #-8]
    // 0xa84d18: LoadField: r0 = r3->field_f
    //     0xa84d18: ldur            w0, [x3, #0xf]
    // 0xa84d1c: DecompressPointer r0
    //     0xa84d1c: add             x0, x0, HEAP, lsl #32
    // 0xa84d20: LoadField: r2 = r0->field_27
    //     0xa84d20: ldur            w2, [x0, #0x27]
    // 0xa84d24: DecompressPointer r2
    //     0xa84d24: add             x2, x2, HEAP, lsl #32
    // 0xa84d28: LoadField: r1 = r0->field_b
    //     0xa84d28: ldur            w1, [x0, #0xb]
    // 0xa84d2c: DecompressPointer r1
    //     0xa84d2c: add             x1, x1, HEAP, lsl #32
    // 0xa84d30: cmp             w1, NULL
    // 0xa84d34: b.eq            #0xa850ac
    // 0xa84d38: LoadField: r0 = r1->field_b
    //     0xa84d38: ldur            w0, [x1, #0xb]
    // 0xa84d3c: DecompressPointer r0
    //     0xa84d3c: add             x0, x0, HEAP, lsl #32
    // 0xa84d40: LoadField: r4 = r0->field_db
    //     0xa84d40: ldur            w4, [x0, #0xdb]
    // 0xa84d44: DecompressPointer r4
    //     0xa84d44: add             x4, x4, HEAP, lsl #32
    // 0xa84d48: cmp             w4, NULL
    // 0xa84d4c: b.ne            #0xa84d5c
    // 0xa84d50: ldur            x5, [fp, #-0x10]
    // 0xa84d54: r0 = Null
    //     0xa84d54: mov             x0, NULL
    // 0xa84d58: b               #0xa84da4
    // 0xa84d5c: ldur            x5, [fp, #-0x10]
    // 0xa84d60: LoadField: r0 = r5->field_f
    //     0xa84d60: ldur            w0, [x5, #0xf]
    // 0xa84d64: DecompressPointer r0
    //     0xa84d64: add             x0, x0, HEAP, lsl #32
    // 0xa84d68: LoadField: r1 = r4->field_b
    //     0xa84d68: ldur            w1, [x4, #0xb]
    // 0xa84d6c: r6 = LoadInt32Instr(r0)
    //     0xa84d6c: sbfx            x6, x0, #1, #0x1f
    //     0xa84d70: tbz             w0, #0, #0xa84d78
    //     0xa84d74: ldur            x6, [x0, #7]
    // 0xa84d78: r0 = LoadInt32Instr(r1)
    //     0xa84d78: sbfx            x0, x1, #1, #0x1f
    // 0xa84d7c: mov             x1, x6
    // 0xa84d80: cmp             x1, x0
    // 0xa84d84: b.hs            #0xa850b0
    // 0xa84d88: LoadField: r0 = r4->field_f
    //     0xa84d88: ldur            w0, [x4, #0xf]
    // 0xa84d8c: DecompressPointer r0
    //     0xa84d8c: add             x0, x0, HEAP, lsl #32
    // 0xa84d90: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa84d90: add             x16, x0, x6, lsl #2
    //     0xa84d94: ldur            w1, [x16, #0xf]
    // 0xa84d98: DecompressPointer r1
    //     0xa84d98: add             x1, x1, HEAP, lsl #32
    // 0xa84d9c: LoadField: r0 = r1->field_7
    //     0xa84d9c: ldur            w0, [x1, #7]
    // 0xa84da0: DecompressPointer r0
    //     0xa84da0: add             x0, x0, HEAP, lsl #32
    // 0xa84da4: cmp             w0, NULL
    // 0xa84da8: b.ne            #0xa84db0
    // 0xa84dac: r0 = ""
    //     0xa84dac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa84db0: mov             x1, x2
    // 0xa84db4: mov             x2, x0
    // 0xa84db8: r0 = contains()
    //     0xa84db8: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa84dbc: tbnz            w0, #4, #0xa84e6c
    // 0xa84dc0: ldur            x3, [fp, #-8]
    // 0xa84dc4: LoadField: r0 = r3->field_f
    //     0xa84dc4: ldur            w0, [x3, #0xf]
    // 0xa84dc8: DecompressPointer r0
    //     0xa84dc8: add             x0, x0, HEAP, lsl #32
    // 0xa84dcc: LoadField: r2 = r0->field_27
    //     0xa84dcc: ldur            w2, [x0, #0x27]
    // 0xa84dd0: DecompressPointer r2
    //     0xa84dd0: add             x2, x2, HEAP, lsl #32
    // 0xa84dd4: LoadField: r1 = r0->field_b
    //     0xa84dd4: ldur            w1, [x0, #0xb]
    // 0xa84dd8: DecompressPointer r1
    //     0xa84dd8: add             x1, x1, HEAP, lsl #32
    // 0xa84ddc: cmp             w1, NULL
    // 0xa84de0: b.eq            #0xa850b4
    // 0xa84de4: LoadField: r0 = r1->field_b
    //     0xa84de4: ldur            w0, [x1, #0xb]
    // 0xa84de8: DecompressPointer r0
    //     0xa84de8: add             x0, x0, HEAP, lsl #32
    // 0xa84dec: LoadField: r4 = r0->field_db
    //     0xa84dec: ldur            w4, [x0, #0xdb]
    // 0xa84df0: DecompressPointer r4
    //     0xa84df0: add             x4, x4, HEAP, lsl #32
    // 0xa84df4: cmp             w4, NULL
    // 0xa84df8: b.ne            #0xa84e08
    // 0xa84dfc: ldur            x5, [fp, #-0x10]
    // 0xa84e00: r0 = Null
    //     0xa84e00: mov             x0, NULL
    // 0xa84e04: b               #0xa84e50
    // 0xa84e08: ldur            x5, [fp, #-0x10]
    // 0xa84e0c: LoadField: r0 = r5->field_f
    //     0xa84e0c: ldur            w0, [x5, #0xf]
    // 0xa84e10: DecompressPointer r0
    //     0xa84e10: add             x0, x0, HEAP, lsl #32
    // 0xa84e14: LoadField: r1 = r4->field_b
    //     0xa84e14: ldur            w1, [x4, #0xb]
    // 0xa84e18: r6 = LoadInt32Instr(r0)
    //     0xa84e18: sbfx            x6, x0, #1, #0x1f
    //     0xa84e1c: tbz             w0, #0, #0xa84e24
    //     0xa84e20: ldur            x6, [x0, #7]
    // 0xa84e24: r0 = LoadInt32Instr(r1)
    //     0xa84e24: sbfx            x0, x1, #1, #0x1f
    // 0xa84e28: mov             x1, x6
    // 0xa84e2c: cmp             x1, x0
    // 0xa84e30: b.hs            #0xa850b8
    // 0xa84e34: LoadField: r0 = r4->field_f
    //     0xa84e34: ldur            w0, [x4, #0xf]
    // 0xa84e38: DecompressPointer r0
    //     0xa84e38: add             x0, x0, HEAP, lsl #32
    // 0xa84e3c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa84e3c: add             x16, x0, x6, lsl #2
    //     0xa84e40: ldur            w1, [x16, #0xf]
    // 0xa84e44: DecompressPointer r1
    //     0xa84e44: add             x1, x1, HEAP, lsl #32
    // 0xa84e48: LoadField: r0 = r1->field_7
    //     0xa84e48: ldur            w0, [x1, #7]
    // 0xa84e4c: DecompressPointer r0
    //     0xa84e4c: add             x0, x0, HEAP, lsl #32
    // 0xa84e50: cmp             w0, NULL
    // 0xa84e54: b.ne            #0xa84e5c
    // 0xa84e58: r0 = ""
    //     0xa84e58: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa84e5c: mov             x1, x2
    // 0xa84e60: mov             x2, x0
    // 0xa84e64: r0 = remove()
    //     0xa84e64: bl              #0x71df18  ; [dart:core] _GrowableList::remove
    // 0xa84e68: b               #0xa84f84
    // 0xa84e6c: ldur            x2, [fp, #-8]
    // 0xa84e70: LoadField: r0 = r2->field_f
    //     0xa84e70: ldur            w0, [x2, #0xf]
    // 0xa84e74: DecompressPointer r0
    //     0xa84e74: add             x0, x0, HEAP, lsl #32
    // 0xa84e78: LoadField: r3 = r0->field_27
    //     0xa84e78: ldur            w3, [x0, #0x27]
    // 0xa84e7c: DecompressPointer r3
    //     0xa84e7c: add             x3, x3, HEAP, lsl #32
    // 0xa84e80: stur            x3, [fp, #-0x28]
    // 0xa84e84: LoadField: r1 = r0->field_b
    //     0xa84e84: ldur            w1, [x0, #0xb]
    // 0xa84e88: DecompressPointer r1
    //     0xa84e88: add             x1, x1, HEAP, lsl #32
    // 0xa84e8c: cmp             w1, NULL
    // 0xa84e90: b.eq            #0xa850bc
    // 0xa84e94: LoadField: r0 = r1->field_b
    //     0xa84e94: ldur            w0, [x1, #0xb]
    // 0xa84e98: DecompressPointer r0
    //     0xa84e98: add             x0, x0, HEAP, lsl #32
    // 0xa84e9c: LoadField: r4 = r0->field_db
    //     0xa84e9c: ldur            w4, [x0, #0xdb]
    // 0xa84ea0: DecompressPointer r4
    //     0xa84ea0: add             x4, x4, HEAP, lsl #32
    // 0xa84ea4: cmp             w4, NULL
    // 0xa84ea8: b.ne            #0xa84eb8
    // 0xa84eac: ldur            x5, [fp, #-0x10]
    // 0xa84eb0: r0 = Null
    //     0xa84eb0: mov             x0, NULL
    // 0xa84eb4: b               #0xa84f00
    // 0xa84eb8: ldur            x5, [fp, #-0x10]
    // 0xa84ebc: LoadField: r0 = r5->field_f
    //     0xa84ebc: ldur            w0, [x5, #0xf]
    // 0xa84ec0: DecompressPointer r0
    //     0xa84ec0: add             x0, x0, HEAP, lsl #32
    // 0xa84ec4: LoadField: r1 = r4->field_b
    //     0xa84ec4: ldur            w1, [x4, #0xb]
    // 0xa84ec8: r6 = LoadInt32Instr(r0)
    //     0xa84ec8: sbfx            x6, x0, #1, #0x1f
    //     0xa84ecc: tbz             w0, #0, #0xa84ed4
    //     0xa84ed0: ldur            x6, [x0, #7]
    // 0xa84ed4: r0 = LoadInt32Instr(r1)
    //     0xa84ed4: sbfx            x0, x1, #1, #0x1f
    // 0xa84ed8: mov             x1, x6
    // 0xa84edc: cmp             x1, x0
    // 0xa84ee0: b.hs            #0xa850c0
    // 0xa84ee4: LoadField: r0 = r4->field_f
    //     0xa84ee4: ldur            w0, [x4, #0xf]
    // 0xa84ee8: DecompressPointer r0
    //     0xa84ee8: add             x0, x0, HEAP, lsl #32
    // 0xa84eec: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa84eec: add             x16, x0, x6, lsl #2
    //     0xa84ef0: ldur            w1, [x16, #0xf]
    // 0xa84ef4: DecompressPointer r1
    //     0xa84ef4: add             x1, x1, HEAP, lsl #32
    // 0xa84ef8: LoadField: r0 = r1->field_7
    //     0xa84ef8: ldur            w0, [x1, #7]
    // 0xa84efc: DecompressPointer r0
    //     0xa84efc: add             x0, x0, HEAP, lsl #32
    // 0xa84f00: cmp             w0, NULL
    // 0xa84f04: b.ne            #0xa84f0c
    // 0xa84f08: r0 = ""
    //     0xa84f08: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa84f0c: stur            x0, [fp, #-0x20]
    // 0xa84f10: LoadField: r1 = r3->field_b
    //     0xa84f10: ldur            w1, [x3, #0xb]
    // 0xa84f14: LoadField: r4 = r3->field_f
    //     0xa84f14: ldur            w4, [x3, #0xf]
    // 0xa84f18: DecompressPointer r4
    //     0xa84f18: add             x4, x4, HEAP, lsl #32
    // 0xa84f1c: LoadField: r6 = r4->field_b
    //     0xa84f1c: ldur            w6, [x4, #0xb]
    // 0xa84f20: r4 = LoadInt32Instr(r1)
    //     0xa84f20: sbfx            x4, x1, #1, #0x1f
    // 0xa84f24: stur            x4, [fp, #-0x18]
    // 0xa84f28: r1 = LoadInt32Instr(r6)
    //     0xa84f28: sbfx            x1, x6, #1, #0x1f
    // 0xa84f2c: cmp             x4, x1
    // 0xa84f30: b.ne            #0xa84f3c
    // 0xa84f34: mov             x1, x3
    // 0xa84f38: r0 = _growToNextCapacity()
    //     0xa84f38: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa84f3c: ldur            x0, [fp, #-0x28]
    // 0xa84f40: ldur            x2, [fp, #-0x18]
    // 0xa84f44: add             x1, x2, #1
    // 0xa84f48: lsl             x3, x1, #1
    // 0xa84f4c: StoreField: r0->field_b = r3
    //     0xa84f4c: stur            w3, [x0, #0xb]
    // 0xa84f50: LoadField: r1 = r0->field_f
    //     0xa84f50: ldur            w1, [x0, #0xf]
    // 0xa84f54: DecompressPointer r1
    //     0xa84f54: add             x1, x1, HEAP, lsl #32
    // 0xa84f58: ldur            x0, [fp, #-0x20]
    // 0xa84f5c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xa84f5c: add             x25, x1, x2, lsl #2
    //     0xa84f60: add             x25, x25, #0xf
    //     0xa84f64: str             w0, [x25]
    //     0xa84f68: tbz             w0, #0, #0xa84f84
    //     0xa84f6c: ldurb           w16, [x1, #-1]
    //     0xa84f70: ldurb           w17, [x0, #-1]
    //     0xa84f74: and             x16, x17, x16, lsr #2
    //     0xa84f78: tst             x16, HEAP, lsr #32
    //     0xa84f7c: b.eq            #0xa84f84
    //     0xa84f80: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xa84f84: ldur            x0, [fp, #-8]
    // 0xa84f88: LoadField: r2 = r0->field_f
    //     0xa84f88: ldur            w2, [x0, #0xf]
    // 0xa84f8c: DecompressPointer r2
    //     0xa84f8c: add             x2, x2, HEAP, lsl #32
    // 0xa84f90: stur            x2, [fp, #-0x20]
    // 0xa84f94: LoadField: r0 = r2->field_b
    //     0xa84f94: ldur            w0, [x2, #0xb]
    // 0xa84f98: DecompressPointer r0
    //     0xa84f98: add             x0, x0, HEAP, lsl #32
    // 0xa84f9c: cmp             w0, NULL
    // 0xa84fa0: b.eq            #0xa850c4
    // 0xa84fa4: LoadField: r1 = r0->field_b
    //     0xa84fa4: ldur            w1, [x0, #0xb]
    // 0xa84fa8: DecompressPointer r1
    //     0xa84fa8: add             x1, x1, HEAP, lsl #32
    // 0xa84fac: LoadField: r3 = r1->field_db
    //     0xa84fac: ldur            w3, [x1, #0xdb]
    // 0xa84fb0: DecompressPointer r3
    //     0xa84fb0: add             x3, x3, HEAP, lsl #32
    // 0xa84fb4: cmp             w3, NULL
    // 0xa84fb8: b.ne            #0xa84fc8
    // 0xa84fbc: ldur            x4, [fp, #-0x10]
    // 0xa84fc0: r0 = Null
    //     0xa84fc0: mov             x0, NULL
    // 0xa84fc4: b               #0xa8504c
    // 0xa84fc8: ldur            x4, [fp, #-0x10]
    // 0xa84fcc: LoadField: r0 = r4->field_f
    //     0xa84fcc: ldur            w0, [x4, #0xf]
    // 0xa84fd0: DecompressPointer r0
    //     0xa84fd0: add             x0, x0, HEAP, lsl #32
    // 0xa84fd4: LoadField: r1 = r3->field_b
    //     0xa84fd4: ldur            w1, [x3, #0xb]
    // 0xa84fd8: r5 = LoadInt32Instr(r0)
    //     0xa84fd8: sbfx            x5, x0, #1, #0x1f
    //     0xa84fdc: tbz             w0, #0, #0xa84fe4
    //     0xa84fe0: ldur            x5, [x0, #7]
    // 0xa84fe4: r0 = LoadInt32Instr(r1)
    //     0xa84fe4: sbfx            x0, x1, #1, #0x1f
    // 0xa84fe8: mov             x1, x5
    // 0xa84fec: cmp             x1, x0
    // 0xa84ff0: b.hs            #0xa850c8
    // 0xa84ff4: LoadField: r0 = r3->field_f
    //     0xa84ff4: ldur            w0, [x3, #0xf]
    // 0xa84ff8: DecompressPointer r0
    //     0xa84ff8: add             x0, x0, HEAP, lsl #32
    // 0xa84ffc: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa84ffc: add             x16, x0, x5, lsl #2
    //     0xa85000: ldur            w1, [x16, #0xf]
    // 0xa85004: DecompressPointer r1
    //     0xa85004: add             x1, x1, HEAP, lsl #32
    // 0xa85008: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xa85008: ldur            w3, [x1, #0x17]
    // 0xa8500c: DecompressPointer r3
    //     0xa8500c: add             x3, x3, HEAP, lsl #32
    // 0xa85010: cmp             w3, NULL
    // 0xa85014: b.ne            #0xa85020
    // 0xa85018: r0 = Null
    //     0xa85018: mov             x0, NULL
    // 0xa8501c: b               #0xa8504c
    // 0xa85020: LoadField: r0 = r3->field_b
    //     0xa85020: ldur            w0, [x3, #0xb]
    // 0xa85024: r1 = LoadInt32Instr(r0)
    //     0xa85024: sbfx            x1, x0, #1, #0x1f
    // 0xa85028: mov             x0, x1
    // 0xa8502c: r1 = 0
    //     0xa8502c: movz            x1, #0
    // 0xa85030: cmp             x1, x0
    // 0xa85034: b.hs            #0xa850cc
    // 0xa85038: LoadField: r0 = r3->field_f
    //     0xa85038: ldur            w0, [x3, #0xf]
    // 0xa8503c: DecompressPointer r0
    //     0xa8503c: add             x0, x0, HEAP, lsl #32
    // 0xa85040: LoadField: r1 = r0->field_f
    //     0xa85040: ldur            w1, [x0, #0xf]
    // 0xa85044: DecompressPointer r1
    //     0xa85044: add             x1, x1, HEAP, lsl #32
    // 0xa85048: mov             x0, x1
    // 0xa8504c: cmp             w0, NULL
    // 0xa85050: b.ne            #0xa85058
    // 0xa85054: r0 = AllSkuDatum()
    //     0xa85054: bl              #0x908124  ; AllocateAllSkuDatumStub -> AllSkuDatum (size=0x1c)
    // 0xa85058: ldur            x2, [fp, #-0x10]
    // 0xa8505c: ldur            x1, [fp, #-0x20]
    // 0xa85060: StoreField: r1->field_13 = r0
    //     0xa85060: stur            w0, [x1, #0x13]
    //     0xa85064: ldurb           w16, [x1, #-1]
    //     0xa85068: ldurb           w17, [x0, #-1]
    //     0xa8506c: and             x16, x17, x16, lsr #2
    //     0xa85070: tst             x16, HEAP, lsr #32
    //     0xa85074: b.eq            #0xa8507c
    //     0xa85078: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0xa8507c: LoadField: r3 = r2->field_f
    //     0xa8507c: ldur            w3, [x2, #0xf]
    // 0xa85080: DecompressPointer r3
    //     0xa85080: add             x3, x3, HEAP, lsl #32
    // 0xa85084: r2 = LoadInt32Instr(r3)
    //     0xa85084: sbfx            x2, x3, #1, #0x1f
    //     0xa85088: tbz             w3, #0, #0xa85090
    //     0xa8508c: ldur            x2, [x3, #7]
    // 0xa85090: StoreField: r1->field_1f = r2
    //     0xa85090: stur            x2, [x1, #0x1f]
    // 0xa85094: r0 = Null
    //     0xa85094: mov             x0, NULL
    // 0xa85098: LeaveFrame
    //     0xa85098: mov             SP, fp
    //     0xa8509c: ldp             fp, lr, [SP], #0x10
    // 0xa850a0: ret
    //     0xa850a0: ret             
    // 0xa850a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa850a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa850a8: b               #0xa84cf0
    // 0xa850ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa850ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa850b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa850b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa850b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa850b4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa850b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa850b8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa850bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa850bc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa850c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa850c0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa850c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa850c4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa850c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa850c8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa850cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa850cc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa850d0, size: 0x68
    // 0xa850d0: EnterFrame
    //     0xa850d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa850d4: mov             fp, SP
    // 0xa850d8: AllocStack(0x8)
    //     0xa850d8: sub             SP, SP, #8
    // 0xa850dc: SetupParameters()
    //     0xa850dc: ldr             x0, [fp, #0x10]
    //     0xa850e0: ldur            w2, [x0, #0x17]
    //     0xa850e4: add             x2, x2, HEAP, lsl #32
    // 0xa850e8: CheckStackOverflow
    //     0xa850e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa850ec: cmp             SP, x16
    //     0xa850f0: b.ls            #0xa85130
    // 0xa850f4: LoadField: r0 = r2->field_b
    //     0xa850f4: ldur            w0, [x2, #0xb]
    // 0xa850f8: DecompressPointer r0
    //     0xa850f8: add             x0, x0, HEAP, lsl #32
    // 0xa850fc: LoadField: r3 = r0->field_f
    //     0xa850fc: ldur            w3, [x0, #0xf]
    // 0xa85100: DecompressPointer r3
    //     0xa85100: add             x3, x3, HEAP, lsl #32
    // 0xa85104: stur            x3, [fp, #-8]
    // 0xa85108: r1 = Function '<anonymous closure>':.
    //     0xa85108: add             x1, PP, #0x52, lsl #12  ; [pp+0x52848] AnonymousClosure: (0xa84cc8), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xa8510c: ldr             x1, [x1, #0x848]
    // 0xa85110: r0 = AllocateClosure()
    //     0xa85110: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa85114: ldur            x1, [fp, #-8]
    // 0xa85118: mov             x2, x0
    // 0xa8511c: r0 = setState()
    //     0xa8511c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa85120: r0 = Null
    //     0xa85120: mov             x0, NULL
    // 0xa85124: LeaveFrame
    //     0xa85124: mov             SP, fp
    //     0xa85128: ldp             fp, lr, [SP], #0x10
    // 0xa8512c: ret
    //     0xa8512c: ret             
    // 0xa85130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85130: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85134: b               #0xa850f4
  }
  [closure] InkWell <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xa85138, size: 0x77c
    // 0xa85138: EnterFrame
    //     0xa85138: stp             fp, lr, [SP, #-0x10]!
    //     0xa8513c: mov             fp, SP
    // 0xa85140: AllocStack(0x60)
    //     0xa85140: sub             SP, SP, #0x60
    // 0xa85144: SetupParameters()
    //     0xa85144: ldr             x0, [fp, #0x20]
    //     0xa85148: ldur            w1, [x0, #0x17]
    //     0xa8514c: add             x1, x1, HEAP, lsl #32
    //     0xa85150: stur            x1, [fp, #-8]
    // 0xa85154: CheckStackOverflow
    //     0xa85154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa85158: cmp             SP, x16
    //     0xa8515c: b.ls            #0xa85884
    // 0xa85160: r1 = 1
    //     0xa85160: movz            x1, #0x1
    // 0xa85164: r0 = AllocateContext()
    //     0xa85164: bl              #0x16f6108  ; AllocateContextStub
    // 0xa85168: mov             x4, x0
    // 0xa8516c: ldur            x3, [fp, #-8]
    // 0xa85170: stur            x4, [fp, #-0x10]
    // 0xa85174: StoreField: r4->field_b = r3
    //     0xa85174: stur            w3, [x4, #0xb]
    // 0xa85178: ldr             x5, [fp, #0x10]
    // 0xa8517c: StoreField: r4->field_f = r5
    //     0xa8517c: stur            w5, [x4, #0xf]
    // 0xa85180: LoadField: r0 = r3->field_f
    //     0xa85180: ldur            w0, [x3, #0xf]
    // 0xa85184: DecompressPointer r0
    //     0xa85184: add             x0, x0, HEAP, lsl #32
    // 0xa85188: LoadField: r2 = r0->field_27
    //     0xa85188: ldur            w2, [x0, #0x27]
    // 0xa8518c: DecompressPointer r2
    //     0xa8518c: add             x2, x2, HEAP, lsl #32
    // 0xa85190: LoadField: r1 = r0->field_b
    //     0xa85190: ldur            w1, [x0, #0xb]
    // 0xa85194: DecompressPointer r1
    //     0xa85194: add             x1, x1, HEAP, lsl #32
    // 0xa85198: cmp             w1, NULL
    // 0xa8519c: b.eq            #0xa8588c
    // 0xa851a0: LoadField: r0 = r1->field_b
    //     0xa851a0: ldur            w0, [x1, #0xb]
    // 0xa851a4: DecompressPointer r0
    //     0xa851a4: add             x0, x0, HEAP, lsl #32
    // 0xa851a8: LoadField: r6 = r0->field_db
    //     0xa851a8: ldur            w6, [x0, #0xdb]
    // 0xa851ac: DecompressPointer r6
    //     0xa851ac: add             x6, x6, HEAP, lsl #32
    // 0xa851b0: cmp             w6, NULL
    // 0xa851b4: b.ne            #0xa851c0
    // 0xa851b8: r0 = Null
    //     0xa851b8: mov             x0, NULL
    // 0xa851bc: b               #0xa85200
    // 0xa851c0: LoadField: r0 = r6->field_b
    //     0xa851c0: ldur            w0, [x6, #0xb]
    // 0xa851c4: r7 = LoadInt32Instr(r5)
    //     0xa851c4: sbfx            x7, x5, #1, #0x1f
    //     0xa851c8: tbz             w5, #0, #0xa851d0
    //     0xa851cc: ldur            x7, [x5, #7]
    // 0xa851d0: r1 = LoadInt32Instr(r0)
    //     0xa851d0: sbfx            x1, x0, #1, #0x1f
    // 0xa851d4: mov             x0, x1
    // 0xa851d8: mov             x1, x7
    // 0xa851dc: cmp             x1, x0
    // 0xa851e0: b.hs            #0xa85890
    // 0xa851e4: LoadField: r0 = r6->field_f
    //     0xa851e4: ldur            w0, [x6, #0xf]
    // 0xa851e8: DecompressPointer r0
    //     0xa851e8: add             x0, x0, HEAP, lsl #32
    // 0xa851ec: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa851ec: add             x16, x0, x7, lsl #2
    //     0xa851f0: ldur            w1, [x16, #0xf]
    // 0xa851f4: DecompressPointer r1
    //     0xa851f4: add             x1, x1, HEAP, lsl #32
    // 0xa851f8: LoadField: r0 = r1->field_7
    //     0xa851f8: ldur            w0, [x1, #7]
    // 0xa851fc: DecompressPointer r0
    //     0xa851fc: add             x0, x0, HEAP, lsl #32
    // 0xa85200: cmp             w0, NULL
    // 0xa85204: b.ne            #0xa8520c
    // 0xa85208: r0 = ""
    //     0xa85208: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa8520c: mov             x1, x2
    // 0xa85210: mov             x2, x0
    // 0xa85214: r0 = contains()
    //     0xa85214: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa85218: tbnz            w0, #4, #0xa85260
    // 0xa8521c: ldr             x1, [fp, #0x18]
    // 0xa85220: r0 = of()
    //     0xa85220: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa85224: LoadField: r2 = r0->field_5b
    //     0xa85224: ldur            w2, [x0, #0x5b]
    // 0xa85228: DecompressPointer r2
    //     0xa85228: add             x2, x2, HEAP, lsl #32
    // 0xa8522c: r1 = Null
    //     0xa8522c: mov             x1, NULL
    // 0xa85230: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa85230: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa85234: r0 = Border.all()
    //     0xa85234: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa85238: stur            x0, [fp, #-0x18]
    // 0xa8523c: r0 = BoxDecoration()
    //     0xa8523c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa85240: mov             x1, x0
    // 0xa85244: ldur            x0, [fp, #-0x18]
    // 0xa85248: StoreField: r1->field_f = r0
    //     0xa85248: stur            w0, [x1, #0xf]
    // 0xa8524c: r0 = Instance_BoxShape
    //     0xa8524c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa85250: ldr             x0, [x0, #0x80]
    // 0xa85254: StoreField: r1->field_23 = r0
    //     0xa85254: stur            w0, [x1, #0x23]
    // 0xa85258: mov             x2, x1
    // 0xa8525c: b               #0xa852a0
    // 0xa85260: r0 = Instance_BoxShape
    //     0xa85260: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa85264: ldr             x0, [x0, #0x80]
    // 0xa85268: r1 = Null
    //     0xa85268: mov             x1, NULL
    // 0xa8526c: r2 = Instance_Color
    //     0xa8526c: add             x2, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xa85270: ldr             x2, [x2, #0xf88]
    // 0xa85274: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa85274: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa85278: r0 = Border.all()
    //     0xa85278: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xa8527c: stur            x0, [fp, #-0x18]
    // 0xa85280: r0 = BoxDecoration()
    //     0xa85280: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xa85284: mov             x1, x0
    // 0xa85288: ldur            x0, [fp, #-0x18]
    // 0xa8528c: StoreField: r1->field_f = r0
    //     0xa8528c: stur            w0, [x1, #0xf]
    // 0xa85290: r0 = Instance_BoxShape
    //     0xa85290: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa85294: ldr             x0, [x0, #0x80]
    // 0xa85298: StoreField: r1->field_23 = r0
    //     0xa85298: stur            w0, [x1, #0x23]
    // 0xa8529c: mov             x2, x1
    // 0xa852a0: ldur            x1, [fp, #-8]
    // 0xa852a4: stur            x2, [fp, #-0x18]
    // 0xa852a8: r0 = ImageHeaders.forImages()
    //     0xa852a8: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xa852ac: mov             x4, x0
    // 0xa852b0: ldur            x3, [fp, #-8]
    // 0xa852b4: stur            x4, [fp, #-0x28]
    // 0xa852b8: LoadField: r0 = r3->field_f
    //     0xa852b8: ldur            w0, [x3, #0xf]
    // 0xa852bc: DecompressPointer r0
    //     0xa852bc: add             x0, x0, HEAP, lsl #32
    // 0xa852c0: LoadField: r1 = r0->field_b
    //     0xa852c0: ldur            w1, [x0, #0xb]
    // 0xa852c4: DecompressPointer r1
    //     0xa852c4: add             x1, x1, HEAP, lsl #32
    // 0xa852c8: cmp             w1, NULL
    // 0xa852cc: b.eq            #0xa85894
    // 0xa852d0: LoadField: r0 = r1->field_b
    //     0xa852d0: ldur            w0, [x1, #0xb]
    // 0xa852d4: DecompressPointer r0
    //     0xa852d4: add             x0, x0, HEAP, lsl #32
    // 0xa852d8: LoadField: r2 = r0->field_db
    //     0xa852d8: ldur            w2, [x0, #0xdb]
    // 0xa852dc: DecompressPointer r2
    //     0xa852dc: add             x2, x2, HEAP, lsl #32
    // 0xa852e0: cmp             w2, NULL
    // 0xa852e4: b.ne            #0xa852f4
    // 0xa852e8: ldr             x5, [fp, #0x10]
    // 0xa852ec: r0 = Null
    //     0xa852ec: mov             x0, NULL
    // 0xa852f0: b               #0xa85338
    // 0xa852f4: ldr             x5, [fp, #0x10]
    // 0xa852f8: LoadField: r0 = r2->field_b
    //     0xa852f8: ldur            w0, [x2, #0xb]
    // 0xa852fc: r6 = LoadInt32Instr(r5)
    //     0xa852fc: sbfx            x6, x5, #1, #0x1f
    //     0xa85300: tbz             w5, #0, #0xa85308
    //     0xa85304: ldur            x6, [x5, #7]
    // 0xa85308: r1 = LoadInt32Instr(r0)
    //     0xa85308: sbfx            x1, x0, #1, #0x1f
    // 0xa8530c: mov             x0, x1
    // 0xa85310: mov             x1, x6
    // 0xa85314: cmp             x1, x0
    // 0xa85318: b.hs            #0xa85898
    // 0xa8531c: LoadField: r0 = r2->field_f
    //     0xa8531c: ldur            w0, [x2, #0xf]
    // 0xa85320: DecompressPointer r0
    //     0xa85320: add             x0, x0, HEAP, lsl #32
    // 0xa85324: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa85324: add             x16, x0, x6, lsl #2
    //     0xa85328: ldur            w1, [x16, #0xf]
    // 0xa8532c: DecompressPointer r1
    //     0xa8532c: add             x1, x1, HEAP, lsl #32
    // 0xa85330: LoadField: r0 = r1->field_f
    //     0xa85330: ldur            w0, [x1, #0xf]
    // 0xa85334: DecompressPointer r0
    //     0xa85334: add             x0, x0, HEAP, lsl #32
    // 0xa85338: cmp             w0, NULL
    // 0xa8533c: b.ne            #0xa85344
    // 0xa85340: r0 = ""
    //     0xa85340: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa85344: stur            x0, [fp, #-0x20]
    // 0xa85348: r1 = Function '<anonymous closure>':.
    //     0xa85348: add             x1, PP, #0x52, lsl #12  ; [pp+0x52818] AnonymousClosure: (0xa858b4), in [package:customer_app/app/presentation/views/line/product_detail/widgets/color_picker_detail.dart] ColorPickerDetail::build (0x1298224)
    //     0xa8534c: ldr             x1, [x1, #0x818]
    // 0xa85350: r2 = Null
    //     0xa85350: mov             x2, NULL
    // 0xa85354: r0 = AllocateClosure()
    //     0xa85354: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa85358: r1 = Function '<anonymous closure>':.
    //     0xa85358: add             x1, PP, #0x52, lsl #12  ; [pp+0x52820] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa8535c: ldr             x1, [x1, #0x820]
    // 0xa85360: r2 = Null
    //     0xa85360: mov             x2, NULL
    // 0xa85364: stur            x0, [fp, #-0x30]
    // 0xa85368: r0 = AllocateClosure()
    //     0xa85368: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa8536c: stur            x0, [fp, #-0x38]
    // 0xa85370: r0 = CachedNetworkImage()
    //     0xa85370: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa85374: stur            x0, [fp, #-0x40]
    // 0xa85378: ldur            x16, [fp, #-0x28]
    // 0xa8537c: r30 = Instance_BoxFit
    //     0xa8537c: add             lr, PP, #0x4e, lsl #12  ; [pp+0x4ea20] Obj!BoxFit@d738a1
    //     0xa85380: ldr             lr, [lr, #0xa20]
    // 0xa85384: stp             lr, x16, [SP, #0x10]
    // 0xa85388: ldur            x16, [fp, #-0x30]
    // 0xa8538c: ldur            lr, [fp, #-0x38]
    // 0xa85390: stp             lr, x16, [SP]
    // 0xa85394: mov             x1, x0
    // 0xa85398: ldur            x2, [fp, #-0x20]
    // 0xa8539c: r4 = const [0, 0x6, 0x4, 0x2, errorWidget, 0x5, fit, 0x3, httpHeaders, 0x2, progressIndicatorBuilder, 0x4, null]
    //     0xa8539c: add             x4, PP, #0x52, lsl #12  ; [pp+0x52828] List(13) [0, 0x6, 0x4, 0x2, "errorWidget", 0x5, "fit", 0x3, "httpHeaders", 0x2, "progressIndicatorBuilder", 0x4, Null]
    //     0xa853a0: ldr             x4, [x4, #0x828]
    // 0xa853a4: r0 = CachedNetworkImage()
    //     0xa853a4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa853a8: r0 = Container()
    //     0xa853a8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xa853ac: stur            x0, [fp, #-0x20]
    // 0xa853b0: r16 = 46.000000
    //     0xa853b0: add             x16, PP, #0x52, lsl #12  ; [pp+0x52830] 46
    //     0xa853b4: ldr             x16, [x16, #0x830]
    // 0xa853b8: r30 = 64.000000
    //     0xa853b8: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa853bc: ldr             lr, [lr, #0x838]
    // 0xa853c0: stp             lr, x16, [SP, #0x10]
    // 0xa853c4: ldur            x16, [fp, #-0x18]
    // 0xa853c8: ldur            lr, [fp, #-0x40]
    // 0xa853cc: stp             lr, x16, [SP]
    // 0xa853d0: mov             x1, x0
    // 0xa853d4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x2, width, 0x1, null]
    //     0xa853d4: add             x4, PP, #0x33, lsl #12  ; [pp+0x33870] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xa853d8: ldr             x4, [x4, #0x870]
    // 0xa853dc: r0 = Container()
    //     0xa853dc: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xa853e0: ldur            x2, [fp, #-8]
    // 0xa853e4: LoadField: r0 = r2->field_f
    //     0xa853e4: ldur            w0, [x2, #0xf]
    // 0xa853e8: DecompressPointer r0
    //     0xa853e8: add             x0, x0, HEAP, lsl #32
    // 0xa853ec: LoadField: r3 = r0->field_b
    //     0xa853ec: ldur            w3, [x0, #0xb]
    // 0xa853f0: DecompressPointer r3
    //     0xa853f0: add             x3, x3, HEAP, lsl #32
    // 0xa853f4: cmp             w3, NULL
    // 0xa853f8: b.eq            #0xa8589c
    // 0xa853fc: LoadField: r0 = r3->field_b
    //     0xa853fc: ldur            w0, [x3, #0xb]
    // 0xa85400: DecompressPointer r0
    //     0xa85400: add             x0, x0, HEAP, lsl #32
    // 0xa85404: LoadField: r4 = r0->field_db
    //     0xa85404: ldur            w4, [x0, #0xdb]
    // 0xa85408: DecompressPointer r4
    //     0xa85408: add             x4, x4, HEAP, lsl #32
    // 0xa8540c: cmp             w4, NULL
    // 0xa85410: b.ne            #0xa85420
    // 0xa85414: ldr             x5, [fp, #0x10]
    // 0xa85418: r0 = Null
    //     0xa85418: mov             x0, NULL
    // 0xa8541c: b               #0xa85464
    // 0xa85420: ldr             x5, [fp, #0x10]
    // 0xa85424: LoadField: r0 = r4->field_b
    //     0xa85424: ldur            w0, [x4, #0xb]
    // 0xa85428: r6 = LoadInt32Instr(r5)
    //     0xa85428: sbfx            x6, x5, #1, #0x1f
    //     0xa8542c: tbz             w5, #0, #0xa85434
    //     0xa85430: ldur            x6, [x5, #7]
    // 0xa85434: r1 = LoadInt32Instr(r0)
    //     0xa85434: sbfx            x1, x0, #1, #0x1f
    // 0xa85438: mov             x0, x1
    // 0xa8543c: mov             x1, x6
    // 0xa85440: cmp             x1, x0
    // 0xa85444: b.hs            #0xa858a0
    // 0xa85448: LoadField: r0 = r4->field_f
    //     0xa85448: ldur            w0, [x4, #0xf]
    // 0xa8544c: DecompressPointer r0
    //     0xa8544c: add             x0, x0, HEAP, lsl #32
    // 0xa85450: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa85450: add             x16, x0, x6, lsl #2
    //     0xa85454: ldur            w1, [x16, #0xf]
    // 0xa85458: DecompressPointer r1
    //     0xa85458: add             x1, x1, HEAP, lsl #32
    // 0xa8545c: LoadField: r0 = r1->field_13
    //     0xa8545c: ldur            w0, [x1, #0x13]
    // 0xa85460: DecompressPointer r0
    //     0xa85460: add             x0, x0, HEAP, lsl #32
    // 0xa85464: cmp             w0, NULL
    // 0xa85468: r16 = true
    //     0xa85468: add             x16, NULL, #0x20  ; true
    // 0xa8546c: r17 = false
    //     0xa8546c: add             x17, NULL, #0x30  ; false
    // 0xa85470: csel            x4, x16, x17, ne
    // 0xa85474: stur            x4, [fp, #-0x18]
    // 0xa85478: LoadField: r0 = r3->field_b
    //     0xa85478: ldur            w0, [x3, #0xb]
    // 0xa8547c: DecompressPointer r0
    //     0xa8547c: add             x0, x0, HEAP, lsl #32
    // 0xa85480: LoadField: r6 = r0->field_db
    //     0xa85480: ldur            w6, [x0, #0xdb]
    // 0xa85484: DecompressPointer r6
    //     0xa85484: add             x6, x6, HEAP, lsl #32
    // 0xa85488: cmp             w6, NULL
    // 0xa8548c: b.ne            #0xa85498
    // 0xa85490: r0 = Null
    //     0xa85490: mov             x0, NULL
    // 0xa85494: b               #0xa854fc
    // 0xa85498: LoadField: r0 = r6->field_b
    //     0xa85498: ldur            w0, [x6, #0xb]
    // 0xa8549c: r7 = LoadInt32Instr(r5)
    //     0xa8549c: sbfx            x7, x5, #1, #0x1f
    //     0xa854a0: tbz             w5, #0, #0xa854a8
    //     0xa854a4: ldur            x7, [x5, #7]
    // 0xa854a8: r1 = LoadInt32Instr(r0)
    //     0xa854a8: sbfx            x1, x0, #1, #0x1f
    // 0xa854ac: mov             x0, x1
    // 0xa854b0: mov             x1, x7
    // 0xa854b4: cmp             x1, x0
    // 0xa854b8: b.hs            #0xa858a4
    // 0xa854bc: LoadField: r0 = r6->field_f
    //     0xa854bc: ldur            w0, [x6, #0xf]
    // 0xa854c0: DecompressPointer r0
    //     0xa854c0: add             x0, x0, HEAP, lsl #32
    // 0xa854c4: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0xa854c4: add             x16, x0, x7, lsl #2
    //     0xa854c8: ldur            w1, [x16, #0xf]
    // 0xa854cc: DecompressPointer r1
    //     0xa854cc: add             x1, x1, HEAP, lsl #32
    // 0xa854d0: LoadField: r0 = r1->field_13
    //     0xa854d0: ldur            w0, [x1, #0x13]
    // 0xa854d4: DecompressPointer r0
    //     0xa854d4: add             x0, x0, HEAP, lsl #32
    // 0xa854d8: cmp             w0, NULL
    // 0xa854dc: b.ne            #0xa854e8
    // 0xa854e0: r0 = Null
    //     0xa854e0: mov             x0, NULL
    // 0xa854e4: b               #0xa854fc
    // 0xa854e8: LoadField: r1 = r0->field_7
    //     0xa854e8: ldur            w1, [x0, #7]
    // 0xa854ec: cbnz            w1, #0xa854f8
    // 0xa854f0: r0 = false
    //     0xa854f0: add             x0, NULL, #0x30  ; false
    // 0xa854f4: b               #0xa854fc
    // 0xa854f8: r0 = true
    //     0xa854f8: add             x0, NULL, #0x20  ; true
    // 0xa854fc: cmp             w0, NULL
    // 0xa85500: b.eq            #0xa85590
    // 0xa85504: tbnz            w0, #4, #0xa85590
    // 0xa85508: LoadField: r0 = r3->field_b
    //     0xa85508: ldur            w0, [x3, #0xb]
    // 0xa8550c: DecompressPointer r0
    //     0xa8550c: add             x0, x0, HEAP, lsl #32
    // 0xa85510: LoadField: r3 = r0->field_db
    //     0xa85510: ldur            w3, [x0, #0xdb]
    // 0xa85514: DecompressPointer r3
    //     0xa85514: add             x3, x3, HEAP, lsl #32
    // 0xa85518: cmp             w3, NULL
    // 0xa8551c: b.ne            #0xa85528
    // 0xa85520: r0 = Null
    //     0xa85520: mov             x0, NULL
    // 0xa85524: b               #0xa85580
    // 0xa85528: LoadField: r0 = r3->field_b
    //     0xa85528: ldur            w0, [x3, #0xb]
    // 0xa8552c: r6 = LoadInt32Instr(r5)
    //     0xa8552c: sbfx            x6, x5, #1, #0x1f
    //     0xa85530: tbz             w5, #0, #0xa85538
    //     0xa85534: ldur            x6, [x5, #7]
    // 0xa85538: r1 = LoadInt32Instr(r0)
    //     0xa85538: sbfx            x1, x0, #1, #0x1f
    // 0xa8553c: mov             x0, x1
    // 0xa85540: mov             x1, x6
    // 0xa85544: cmp             x1, x0
    // 0xa85548: b.hs            #0xa858a8
    // 0xa8554c: LoadField: r0 = r3->field_f
    //     0xa8554c: ldur            w0, [x3, #0xf]
    // 0xa85550: DecompressPointer r0
    //     0xa85550: add             x0, x0, HEAP, lsl #32
    // 0xa85554: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xa85554: add             x16, x0, x6, lsl #2
    //     0xa85558: ldur            w1, [x16, #0xf]
    // 0xa8555c: DecompressPointer r1
    //     0xa8555c: add             x1, x1, HEAP, lsl #32
    // 0xa85560: LoadField: r0 = r1->field_13
    //     0xa85560: ldur            w0, [x1, #0x13]
    // 0xa85564: DecompressPointer r0
    //     0xa85564: add             x0, x0, HEAP, lsl #32
    // 0xa85568: cmp             w0, NULL
    // 0xa8556c: b.ne            #0xa85578
    // 0xa85570: r0 = Null
    //     0xa85570: mov             x0, NULL
    // 0xa85574: b               #0xa85580
    // 0xa85578: mov             x1, x0
    // 0xa8557c: r0 = StringExtension.toTitleCase()
    //     0xa8557c: bl              #0xa61c7c  ; [package:customer_app/app/core/extension/capitalize_all_letter.dart] ::StringExtension.toTitleCase
    // 0xa85580: str             x0, [SP]
    // 0xa85584: r0 = _interpolateSingle()
    //     0xa85584: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa85588: mov             x3, x0
    // 0xa8558c: b               #0xa85594
    // 0xa85590: r3 = ""
    //     0xa85590: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa85594: ldur            x0, [fp, #-8]
    // 0xa85598: stur            x3, [fp, #-0x28]
    // 0xa8559c: LoadField: r1 = r0->field_f
    //     0xa8559c: ldur            w1, [x0, #0xf]
    // 0xa855a0: DecompressPointer r1
    //     0xa855a0: add             x1, x1, HEAP, lsl #32
    // 0xa855a4: LoadField: r2 = r1->field_27
    //     0xa855a4: ldur            w2, [x1, #0x27]
    // 0xa855a8: DecompressPointer r2
    //     0xa855a8: add             x2, x2, HEAP, lsl #32
    // 0xa855ac: LoadField: r0 = r1->field_b
    //     0xa855ac: ldur            w0, [x1, #0xb]
    // 0xa855b0: DecompressPointer r0
    //     0xa855b0: add             x0, x0, HEAP, lsl #32
    // 0xa855b4: cmp             w0, NULL
    // 0xa855b8: b.eq            #0xa858ac
    // 0xa855bc: LoadField: r1 = r0->field_b
    //     0xa855bc: ldur            w1, [x0, #0xb]
    // 0xa855c0: DecompressPointer r1
    //     0xa855c0: add             x1, x1, HEAP, lsl #32
    // 0xa855c4: LoadField: r4 = r1->field_db
    //     0xa855c4: ldur            w4, [x1, #0xdb]
    // 0xa855c8: DecompressPointer r4
    //     0xa855c8: add             x4, x4, HEAP, lsl #32
    // 0xa855cc: cmp             w4, NULL
    // 0xa855d0: b.ne            #0xa855dc
    // 0xa855d4: r0 = Null
    //     0xa855d4: mov             x0, NULL
    // 0xa855d8: b               #0xa8561c
    // 0xa855dc: ldr             x0, [fp, #0x10]
    // 0xa855e0: LoadField: r1 = r4->field_b
    //     0xa855e0: ldur            w1, [x4, #0xb]
    // 0xa855e4: r5 = LoadInt32Instr(r0)
    //     0xa855e4: sbfx            x5, x0, #1, #0x1f
    //     0xa855e8: tbz             w0, #0, #0xa855f0
    //     0xa855ec: ldur            x5, [x0, #7]
    // 0xa855f0: r0 = LoadInt32Instr(r1)
    //     0xa855f0: sbfx            x0, x1, #1, #0x1f
    // 0xa855f4: mov             x1, x5
    // 0xa855f8: cmp             x1, x0
    // 0xa855fc: b.hs            #0xa858b0
    // 0xa85600: LoadField: r0 = r4->field_f
    //     0xa85600: ldur            w0, [x4, #0xf]
    // 0xa85604: DecompressPointer r0
    //     0xa85604: add             x0, x0, HEAP, lsl #32
    // 0xa85608: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xa85608: add             x16, x0, x5, lsl #2
    //     0xa8560c: ldur            w1, [x16, #0xf]
    // 0xa85610: DecompressPointer r1
    //     0xa85610: add             x1, x1, HEAP, lsl #32
    // 0xa85614: LoadField: r0 = r1->field_7
    //     0xa85614: ldur            w0, [x1, #7]
    // 0xa85618: DecompressPointer r0
    //     0xa85618: add             x0, x0, HEAP, lsl #32
    // 0xa8561c: cmp             w0, NULL
    // 0xa85620: b.ne            #0xa85628
    // 0xa85624: r0 = ""
    //     0xa85624: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa85628: mov             x1, x2
    // 0xa8562c: mov             x2, x0
    // 0xa85630: r0 = contains()
    //     0xa85630: bl              #0x7de3d8  ; [dart:collection] ListBase::contains
    // 0xa85634: tbnz            w0, #4, #0xa85678
    // 0xa85638: ldr             x1, [fp, #0x18]
    // 0xa8563c: r0 = of()
    //     0xa8563c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa85640: LoadField: r1 = r0->field_87
    //     0xa85640: ldur            w1, [x0, #0x87]
    // 0xa85644: DecompressPointer r1
    //     0xa85644: add             x1, x1, HEAP, lsl #32
    // 0xa85648: LoadField: r0 = r1->field_7
    //     0xa85648: ldur            w0, [x1, #7]
    // 0xa8564c: DecompressPointer r0
    //     0xa8564c: add             x0, x0, HEAP, lsl #32
    // 0xa85650: r16 = Instance_Color
    //     0xa85650: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa85654: r30 = 12.000000
    //     0xa85654: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa85658: ldr             lr, [lr, #0x9e8]
    // 0xa8565c: stp             lr, x16, [SP]
    // 0xa85660: mov             x1, x0
    // 0xa85664: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa85664: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa85668: ldr             x4, [x4, #0x9b8]
    // 0xa8566c: r0 = copyWith()
    //     0xa8566c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa85670: mov             x3, x0
    // 0xa85674: b               #0xa856c4
    // 0xa85678: ldr             x1, [fp, #0x18]
    // 0xa8567c: r0 = of()
    //     0xa8567c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa85680: LoadField: r1 = r0->field_87
    //     0xa85680: ldur            w1, [x0, #0x87]
    // 0xa85684: DecompressPointer r1
    //     0xa85684: add             x1, x1, HEAP, lsl #32
    // 0xa85688: LoadField: r0 = r1->field_2b
    //     0xa85688: ldur            w0, [x1, #0x2b]
    // 0xa8568c: DecompressPointer r0
    //     0xa8568c: add             x0, x0, HEAP, lsl #32
    // 0xa85690: stur            x0, [fp, #-8]
    // 0xa85694: r1 = Instance_Color
    //     0xa85694: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa85698: d0 = 0.700000
    //     0xa85698: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xa8569c: ldr             d0, [x17, #0xf48]
    // 0xa856a0: r0 = withOpacity()
    //     0xa856a0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xa856a4: r16 = 12.000000
    //     0xa856a4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa856a8: ldr             x16, [x16, #0x9e8]
    // 0xa856ac: stp             x16, x0, [SP]
    // 0xa856b0: ldur            x1, [fp, #-8]
    // 0xa856b4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xa856b4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xa856b8: ldr             x4, [x4, #0x9b8]
    // 0xa856bc: r0 = copyWith()
    //     0xa856bc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa856c0: mov             x3, x0
    // 0xa856c4: ldur            x2, [fp, #-0x20]
    // 0xa856c8: ldur            x1, [fp, #-0x18]
    // 0xa856cc: ldur            x0, [fp, #-0x28]
    // 0xa856d0: stur            x3, [fp, #-8]
    // 0xa856d4: r0 = Text()
    //     0xa856d4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa856d8: mov             x2, x0
    // 0xa856dc: ldur            x0, [fp, #-0x28]
    // 0xa856e0: stur            x2, [fp, #-0x30]
    // 0xa856e4: StoreField: r2->field_b = r0
    //     0xa856e4: stur            w0, [x2, #0xb]
    // 0xa856e8: ldur            x0, [fp, #-8]
    // 0xa856ec: StoreField: r2->field_13 = r0
    //     0xa856ec: stur            w0, [x2, #0x13]
    // 0xa856f0: r1 = <FlexParentData>
    //     0xa856f0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xa856f4: ldr             x1, [x1, #0xe00]
    // 0xa856f8: r0 = Expanded()
    //     0xa856f8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xa856fc: mov             x1, x0
    // 0xa85700: r0 = 1
    //     0xa85700: movz            x0, #0x1
    // 0xa85704: stur            x1, [fp, #-8]
    // 0xa85708: StoreField: r1->field_13 = r0
    //     0xa85708: stur            x0, [x1, #0x13]
    // 0xa8570c: r0 = Instance_FlexFit
    //     0xa8570c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xa85710: ldr             x0, [x0, #0xe08]
    // 0xa85714: StoreField: r1->field_1b = r0
    //     0xa85714: stur            w0, [x1, #0x1b]
    // 0xa85718: ldur            x0, [fp, #-0x30]
    // 0xa8571c: StoreField: r1->field_b = r0
    //     0xa8571c: stur            w0, [x1, #0xb]
    // 0xa85720: r0 = Visibility()
    //     0xa85720: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xa85724: mov             x3, x0
    // 0xa85728: ldur            x0, [fp, #-8]
    // 0xa8572c: stur            x3, [fp, #-0x28]
    // 0xa85730: StoreField: r3->field_b = r0
    //     0xa85730: stur            w0, [x3, #0xb]
    // 0xa85734: r0 = Instance_SizedBox
    //     0xa85734: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xa85738: StoreField: r3->field_f = r0
    //     0xa85738: stur            w0, [x3, #0xf]
    // 0xa8573c: ldur            x0, [fp, #-0x18]
    // 0xa85740: StoreField: r3->field_13 = r0
    //     0xa85740: stur            w0, [x3, #0x13]
    // 0xa85744: r0 = false
    //     0xa85744: add             x0, NULL, #0x30  ; false
    // 0xa85748: ArrayStore: r3[0] = r0  ; List_4
    //     0xa85748: stur            w0, [x3, #0x17]
    // 0xa8574c: StoreField: r3->field_1b = r0
    //     0xa8574c: stur            w0, [x3, #0x1b]
    // 0xa85750: StoreField: r3->field_1f = r0
    //     0xa85750: stur            w0, [x3, #0x1f]
    // 0xa85754: StoreField: r3->field_23 = r0
    //     0xa85754: stur            w0, [x3, #0x23]
    // 0xa85758: StoreField: r3->field_27 = r0
    //     0xa85758: stur            w0, [x3, #0x27]
    // 0xa8575c: StoreField: r3->field_2b = r0
    //     0xa8575c: stur            w0, [x3, #0x2b]
    // 0xa85760: r1 = Null
    //     0xa85760: mov             x1, NULL
    // 0xa85764: r2 = 4
    //     0xa85764: movz            x2, #0x4
    // 0xa85768: r0 = AllocateArray()
    //     0xa85768: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa8576c: mov             x2, x0
    // 0xa85770: ldur            x0, [fp, #-0x20]
    // 0xa85774: stur            x2, [fp, #-8]
    // 0xa85778: StoreField: r2->field_f = r0
    //     0xa85778: stur            w0, [x2, #0xf]
    // 0xa8577c: ldur            x0, [fp, #-0x28]
    // 0xa85780: StoreField: r2->field_13 = r0
    //     0xa85780: stur            w0, [x2, #0x13]
    // 0xa85784: r1 = <Widget>
    //     0xa85784: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa85788: r0 = AllocateGrowableArray()
    //     0xa85788: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa8578c: mov             x1, x0
    // 0xa85790: ldur            x0, [fp, #-8]
    // 0xa85794: stur            x1, [fp, #-0x18]
    // 0xa85798: StoreField: r1->field_f = r0
    //     0xa85798: stur            w0, [x1, #0xf]
    // 0xa8579c: r0 = 4
    //     0xa8579c: movz            x0, #0x4
    // 0xa857a0: StoreField: r1->field_b = r0
    //     0xa857a0: stur            w0, [x1, #0xb]
    // 0xa857a4: r0 = Column()
    //     0xa857a4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa857a8: mov             x1, x0
    // 0xa857ac: r0 = Instance_Axis
    //     0xa857ac: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa857b0: stur            x1, [fp, #-8]
    // 0xa857b4: StoreField: r1->field_f = r0
    //     0xa857b4: stur            w0, [x1, #0xf]
    // 0xa857b8: r0 = Instance_MainAxisAlignment
    //     0xa857b8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa857bc: ldr             x0, [x0, #0xa08]
    // 0xa857c0: StoreField: r1->field_13 = r0
    //     0xa857c0: stur            w0, [x1, #0x13]
    // 0xa857c4: r0 = Instance_MainAxisSize
    //     0xa857c4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa857c8: ldr             x0, [x0, #0xa10]
    // 0xa857cc: ArrayStore: r1[0] = r0  ; List_4
    //     0xa857cc: stur            w0, [x1, #0x17]
    // 0xa857d0: r0 = Instance_CrossAxisAlignment
    //     0xa857d0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa857d4: ldr             x0, [x0, #0xa18]
    // 0xa857d8: StoreField: r1->field_1b = r0
    //     0xa857d8: stur            w0, [x1, #0x1b]
    // 0xa857dc: r0 = Instance_VerticalDirection
    //     0xa857dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa857e0: ldr             x0, [x0, #0xa20]
    // 0xa857e4: StoreField: r1->field_23 = r0
    //     0xa857e4: stur            w0, [x1, #0x23]
    // 0xa857e8: r0 = Instance_Clip
    //     0xa857e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa857ec: ldr             x0, [x0, #0x38]
    // 0xa857f0: StoreField: r1->field_2b = r0
    //     0xa857f0: stur            w0, [x1, #0x2b]
    // 0xa857f4: StoreField: r1->field_2f = rZR
    //     0xa857f4: stur            xzr, [x1, #0x2f]
    // 0xa857f8: ldur            x0, [fp, #-0x18]
    // 0xa857fc: StoreField: r1->field_b = r0
    //     0xa857fc: stur            w0, [x1, #0xb]
    // 0xa85800: r0 = Padding()
    //     0xa85800: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xa85804: mov             x1, x0
    // 0xa85808: r0 = Instance_EdgeInsets
    //     0xa85808: add             x0, PP, #0x52, lsl #12  ; [pp+0x52268] Obj!EdgeInsets@d57111
    //     0xa8580c: ldr             x0, [x0, #0x268]
    // 0xa85810: stur            x1, [fp, #-0x18]
    // 0xa85814: StoreField: r1->field_f = r0
    //     0xa85814: stur            w0, [x1, #0xf]
    // 0xa85818: ldur            x0, [fp, #-8]
    // 0xa8581c: StoreField: r1->field_b = r0
    //     0xa8581c: stur            w0, [x1, #0xb]
    // 0xa85820: r0 = InkWell()
    //     0xa85820: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xa85824: mov             x3, x0
    // 0xa85828: ldur            x0, [fp, #-0x18]
    // 0xa8582c: stur            x3, [fp, #-8]
    // 0xa85830: StoreField: r3->field_b = r0
    //     0xa85830: stur            w0, [x3, #0xb]
    // 0xa85834: ldur            x2, [fp, #-0x10]
    // 0xa85838: r1 = Function '<anonymous closure>':.
    //     0xa85838: add             x1, PP, #0x52, lsl #12  ; [pp+0x52840] AnonymousClosure: (0xa850d0), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xa8583c: ldr             x1, [x1, #0x840]
    // 0xa85840: r0 = AllocateClosure()
    //     0xa85840: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa85844: mov             x1, x0
    // 0xa85848: ldur            x0, [fp, #-8]
    // 0xa8584c: StoreField: r0->field_f = r1
    //     0xa8584c: stur            w1, [x0, #0xf]
    // 0xa85850: r1 = true
    //     0xa85850: add             x1, NULL, #0x20  ; true
    // 0xa85854: StoreField: r0->field_43 = r1
    //     0xa85854: stur            w1, [x0, #0x43]
    // 0xa85858: r2 = Instance_BoxShape
    //     0xa85858: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xa8585c: ldr             x2, [x2, #0x80]
    // 0xa85860: StoreField: r0->field_47 = r2
    //     0xa85860: stur            w2, [x0, #0x47]
    // 0xa85864: StoreField: r0->field_6f = r1
    //     0xa85864: stur            w1, [x0, #0x6f]
    // 0xa85868: r2 = false
    //     0xa85868: add             x2, NULL, #0x30  ; false
    // 0xa8586c: StoreField: r0->field_73 = r2
    //     0xa8586c: stur            w2, [x0, #0x73]
    // 0xa85870: StoreField: r0->field_83 = r1
    //     0xa85870: stur            w1, [x0, #0x83]
    // 0xa85874: StoreField: r0->field_7b = r2
    //     0xa85874: stur            w2, [x0, #0x7b]
    // 0xa85878: LeaveFrame
    //     0xa85878: mov             SP, fp
    //     0xa8587c: ldp             fp, lr, [SP], #0x10
    // 0xa85880: ret
    //     0xa85880: ret             
    // 0xa85884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa85884: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa85888: b               #0xa85160
    // 0xa8588c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8588c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa85890: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa85890: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa85894: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa85894: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa85898: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa85898: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8589c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8589c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa858a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa858a0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa858a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa858a4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa858a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa858a8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa858ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa858ac: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa858b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa858b0: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] DropdownMenuItem<AllSkuDatum> <anonymous closure>(dynamic, AllSkuDatum) {
    // ** addr: 0xbf0138, size: 0x10c
    // 0xbf0138: EnterFrame
    //     0xbf0138: stp             fp, lr, [SP, #-0x10]!
    //     0xbf013c: mov             fp, SP
    // 0xbf0140: AllocStack(0x28)
    //     0xbf0140: sub             SP, SP, #0x28
    // 0xbf0144: SetupParameters()
    //     0xbf0144: ldr             x0, [fp, #0x18]
    //     0xbf0148: ldur            w1, [x0, #0x17]
    //     0xbf014c: add             x1, x1, HEAP, lsl #32
    // 0xbf0150: CheckStackOverflow
    //     0xbf0150: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf0154: cmp             SP, x16
    //     0xbf0158: b.ls            #0xbf023c
    // 0xbf015c: ldr             x0, [fp, #0x10]
    // 0xbf0160: LoadField: r2 = r0->field_7
    //     0xbf0160: ldur            w2, [x0, #7]
    // 0xbf0164: DecompressPointer r2
    //     0xbf0164: add             x2, x2, HEAP, lsl #32
    // 0xbf0168: cmp             w2, NULL
    // 0xbf016c: b.ne            #0xbf0174
    // 0xbf0170: r2 = ""
    //     0xbf0170: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf0174: stur            x2, [fp, #-8]
    // 0xbf0178: LoadField: r3 = r1->field_13
    //     0xbf0178: ldur            w3, [x1, #0x13]
    // 0xbf017c: DecompressPointer r3
    //     0xbf017c: add             x3, x3, HEAP, lsl #32
    // 0xbf0180: mov             x1, x3
    // 0xbf0184: r0 = of()
    //     0xbf0184: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf0188: LoadField: r1 = r0->field_87
    //     0xbf0188: ldur            w1, [x0, #0x87]
    // 0xbf018c: DecompressPointer r1
    //     0xbf018c: add             x1, x1, HEAP, lsl #32
    // 0xbf0190: LoadField: r0 = r1->field_2b
    //     0xbf0190: ldur            w0, [x1, #0x2b]
    // 0xbf0194: DecompressPointer r0
    //     0xbf0194: add             x0, x0, HEAP, lsl #32
    // 0xbf0198: r16 = Instance_Color
    //     0xbf0198: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf019c: r30 = 14.000000
    //     0xbf019c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbf01a0: ldr             lr, [lr, #0x1d8]
    // 0xbf01a4: stp             lr, x16, [SP]
    // 0xbf01a8: mov             x1, x0
    // 0xbf01ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbf01ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbf01b0: ldr             x4, [x4, #0x9b8]
    // 0xbf01b4: r0 = copyWith()
    //     0xbf01b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf01b8: stur            x0, [fp, #-0x10]
    // 0xbf01bc: r0 = Text()
    //     0xbf01bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf01c0: mov             x1, x0
    // 0xbf01c4: ldur            x0, [fp, #-8]
    // 0xbf01c8: stur            x1, [fp, #-0x18]
    // 0xbf01cc: StoreField: r1->field_b = r0
    //     0xbf01cc: stur            w0, [x1, #0xb]
    // 0xbf01d0: ldur            x0, [fp, #-0x10]
    // 0xbf01d4: StoreField: r1->field_13 = r0
    //     0xbf01d4: stur            w0, [x1, #0x13]
    // 0xbf01d8: r0 = Instance_TextAlign
    //     0xbf01d8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xbf01dc: StoreField: r1->field_1b = r0
    //     0xbf01dc: stur            w0, [x1, #0x1b]
    // 0xbf01e0: r0 = Center()
    //     0xbf01e0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbf01e4: mov             x2, x0
    // 0xbf01e8: r0 = Instance_Alignment
    //     0xbf01e8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbf01ec: ldr             x0, [x0, #0xb10]
    // 0xbf01f0: stur            x2, [fp, #-8]
    // 0xbf01f4: StoreField: r2->field_f = r0
    //     0xbf01f4: stur            w0, [x2, #0xf]
    // 0xbf01f8: ldur            x0, [fp, #-0x18]
    // 0xbf01fc: StoreField: r2->field_b = r0
    //     0xbf01fc: stur            w0, [x2, #0xb]
    // 0xbf0200: r1 = <AllSkuDatum>
    //     0xbf0200: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xbf0204: ldr             x1, [x1, #0xea8]
    // 0xbf0208: r0 = DropdownMenuItem()
    //     0xbf0208: bl              #0x9ba75c  ; AllocateDropdownMenuItemStub -> DropdownMenuItem<X0> (size=0x24)
    // 0xbf020c: ldr             x1, [fp, #0x10]
    // 0xbf0210: StoreField: r0->field_1b = r1
    //     0xbf0210: stur            w1, [x0, #0x1b]
    // 0xbf0214: r1 = true
    //     0xbf0214: add             x1, NULL, #0x20  ; true
    // 0xbf0218: StoreField: r0->field_1f = r1
    //     0xbf0218: stur            w1, [x0, #0x1f]
    // 0xbf021c: r1 = Instance_AlignmentDirectional
    //     0xbf021c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] Obj!AlignmentDirectional@d5a601
    //     0xbf0220: ldr             x1, [x1, #0xb70]
    // 0xbf0224: StoreField: r0->field_f = r1
    //     0xbf0224: stur            w1, [x0, #0xf]
    // 0xbf0228: ldur            x1, [fp, #-8]
    // 0xbf022c: StoreField: r0->field_b = r1
    //     0xbf022c: stur            w1, [x0, #0xb]
    // 0xbf0230: LeaveFrame
    //     0xbf0230: mov             SP, fp
    //     0xbf0234: ldp             fp, lr, [SP], #0x10
    // 0xbf0238: ret
    //     0xbf0238: ret             
    // 0xbf023c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf023c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf0240: b               #0xbf015c
  }
  _ build(/* No info */) {
    // ** addr: 0xc05170, size: 0xe94
    // 0xc05170: EnterFrame
    //     0xc05170: stp             fp, lr, [SP, #-0x10]!
    //     0xc05174: mov             fp, SP
    // 0xc05178: AllocStack(0x70)
    //     0xc05178: sub             SP, SP, #0x70
    // 0xc0517c: SetupParameters(_ProductSelectSizeBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xc0517c: mov             x0, x1
    //     0xc05180: stur            x1, [fp, #-8]
    //     0xc05184: mov             x1, x2
    //     0xc05188: stur            x2, [fp, #-0x10]
    // 0xc0518c: CheckStackOverflow
    //     0xc0518c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc05190: cmp             SP, x16
    //     0xc05194: b.ls            #0xc05fc4
    // 0xc05198: r1 = 2
    //     0xc05198: movz            x1, #0x2
    // 0xc0519c: r0 = AllocateContext()
    //     0xc0519c: bl              #0x16f6108  ; AllocateContextStub
    // 0xc051a0: mov             x2, x0
    // 0xc051a4: ldur            x0, [fp, #-8]
    // 0xc051a8: stur            x2, [fp, #-0x18]
    // 0xc051ac: StoreField: r2->field_f = r0
    //     0xc051ac: stur            w0, [x2, #0xf]
    // 0xc051b0: ldur            x1, [fp, #-0x10]
    // 0xc051b4: StoreField: r2->field_13 = r1
    //     0xc051b4: stur            w1, [x2, #0x13]
    // 0xc051b8: r0 = of()
    //     0xc051b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc051bc: LoadField: r1 = r0->field_87
    //     0xc051bc: ldur            w1, [x0, #0x87]
    // 0xc051c0: DecompressPointer r1
    //     0xc051c0: add             x1, x1, HEAP, lsl #32
    // 0xc051c4: LoadField: r0 = r1->field_7
    //     0xc051c4: ldur            w0, [x1, #7]
    // 0xc051c8: DecompressPointer r0
    //     0xc051c8: add             x0, x0, HEAP, lsl #32
    // 0xc051cc: r16 = Instance_Color
    //     0xc051cc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc051d0: r30 = 21.000000
    //     0xc051d0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xc051d4: ldr             lr, [lr, #0x9b0]
    // 0xc051d8: stp             lr, x16, [SP]
    // 0xc051dc: mov             x1, x0
    // 0xc051e0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc051e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc051e4: ldr             x4, [x4, #0x9b8]
    // 0xc051e8: r0 = copyWith()
    //     0xc051e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc051ec: stur            x0, [fp, #-0x10]
    // 0xc051f0: r0 = Text()
    //     0xc051f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc051f4: mov             x1, x0
    // 0xc051f8: r0 = "Select Preference"
    //     0xc051f8: add             x0, PP, #0x52, lsl #12  ; [pp+0x52710] "Select Preference"
    //     0xc051fc: ldr             x0, [x0, #0x710]
    // 0xc05200: stur            x1, [fp, #-0x20]
    // 0xc05204: StoreField: r1->field_b = r0
    //     0xc05204: stur            w0, [x1, #0xb]
    // 0xc05208: ldur            x0, [fp, #-0x10]
    // 0xc0520c: StoreField: r1->field_13 = r0
    //     0xc0520c: stur            w0, [x1, #0x13]
    // 0xc05210: r0 = SvgPicture()
    //     0xc05210: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xc05214: mov             x1, x0
    // 0xc05218: r2 = "assets/images/x.svg"
    //     0xc05218: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xc0521c: ldr             x2, [x2, #0x5e8]
    // 0xc05220: stur            x0, [fp, #-0x10]
    // 0xc05224: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc05224: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc05228: r0 = SvgPicture.asset()
    //     0xc05228: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xc0522c: r0 = InkWell()
    //     0xc0522c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc05230: mov             x3, x0
    // 0xc05234: ldur            x0, [fp, #-0x10]
    // 0xc05238: stur            x3, [fp, #-0x28]
    // 0xc0523c: StoreField: r3->field_b = r0
    //     0xc0523c: stur            w0, [x3, #0xb]
    // 0xc05240: ldur            x2, [fp, #-0x18]
    // 0xc05244: r1 = Function '<anonymous closure>':.
    //     0xc05244: add             x1, PP, #0x52, lsl #12  ; [pp+0x52718] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc05248: ldr             x1, [x1, #0x718]
    // 0xc0524c: r0 = AllocateClosure()
    //     0xc0524c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc05250: mov             x1, x0
    // 0xc05254: ldur            x0, [fp, #-0x28]
    // 0xc05258: StoreField: r0->field_f = r1
    //     0xc05258: stur            w1, [x0, #0xf]
    // 0xc0525c: r3 = true
    //     0xc0525c: add             x3, NULL, #0x20  ; true
    // 0xc05260: StoreField: r0->field_43 = r3
    //     0xc05260: stur            w3, [x0, #0x43]
    // 0xc05264: r4 = Instance_BoxShape
    //     0xc05264: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc05268: ldr             x4, [x4, #0x80]
    // 0xc0526c: StoreField: r0->field_47 = r4
    //     0xc0526c: stur            w4, [x0, #0x47]
    // 0xc05270: StoreField: r0->field_6f = r3
    //     0xc05270: stur            w3, [x0, #0x6f]
    // 0xc05274: r5 = false
    //     0xc05274: add             x5, NULL, #0x30  ; false
    // 0xc05278: StoreField: r0->field_73 = r5
    //     0xc05278: stur            w5, [x0, #0x73]
    // 0xc0527c: StoreField: r0->field_83 = r3
    //     0xc0527c: stur            w3, [x0, #0x83]
    // 0xc05280: StoreField: r0->field_7b = r5
    //     0xc05280: stur            w5, [x0, #0x7b]
    // 0xc05284: r1 = Null
    //     0xc05284: mov             x1, NULL
    // 0xc05288: r2 = 6
    //     0xc05288: movz            x2, #0x6
    // 0xc0528c: r0 = AllocateArray()
    //     0xc0528c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc05290: mov             x2, x0
    // 0xc05294: ldur            x0, [fp, #-0x20]
    // 0xc05298: stur            x2, [fp, #-0x10]
    // 0xc0529c: StoreField: r2->field_f = r0
    //     0xc0529c: stur            w0, [x2, #0xf]
    // 0xc052a0: r16 = Instance_Spacer
    //     0xc052a0: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0xc052a4: ldr             x16, [x16, #0xf0]
    // 0xc052a8: StoreField: r2->field_13 = r16
    //     0xc052a8: stur            w16, [x2, #0x13]
    // 0xc052ac: ldur            x0, [fp, #-0x28]
    // 0xc052b0: ArrayStore: r2[0] = r0  ; List_4
    //     0xc052b0: stur            w0, [x2, #0x17]
    // 0xc052b4: r1 = <Widget>
    //     0xc052b4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc052b8: r0 = AllocateGrowableArray()
    //     0xc052b8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc052bc: mov             x1, x0
    // 0xc052c0: ldur            x0, [fp, #-0x10]
    // 0xc052c4: stur            x1, [fp, #-0x20]
    // 0xc052c8: StoreField: r1->field_f = r0
    //     0xc052c8: stur            w0, [x1, #0xf]
    // 0xc052cc: r2 = 6
    //     0xc052cc: movz            x2, #0x6
    // 0xc052d0: StoreField: r1->field_b = r2
    //     0xc052d0: stur            w2, [x1, #0xb]
    // 0xc052d4: r0 = Row()
    //     0xc052d4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xc052d8: mov             x1, x0
    // 0xc052dc: r0 = Instance_Axis
    //     0xc052dc: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc052e0: stur            x1, [fp, #-0x28]
    // 0xc052e4: StoreField: r1->field_f = r0
    //     0xc052e4: stur            w0, [x1, #0xf]
    // 0xc052e8: r0 = Instance_MainAxisAlignment
    //     0xc052e8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc052ec: ldr             x0, [x0, #0xa08]
    // 0xc052f0: StoreField: r1->field_13 = r0
    //     0xc052f0: stur            w0, [x1, #0x13]
    // 0xc052f4: r2 = Instance_MainAxisSize
    //     0xc052f4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xc052f8: ldr             x2, [x2, #0xa10]
    // 0xc052fc: ArrayStore: r1[0] = r2  ; List_4
    //     0xc052fc: stur            w2, [x1, #0x17]
    // 0xc05300: r2 = Instance_CrossAxisAlignment
    //     0xc05300: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xc05304: ldr             x2, [x2, #0xa18]
    // 0xc05308: StoreField: r1->field_1b = r2
    //     0xc05308: stur            w2, [x1, #0x1b]
    // 0xc0530c: r2 = Instance_VerticalDirection
    //     0xc0530c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc05310: ldr             x2, [x2, #0xa20]
    // 0xc05314: StoreField: r1->field_23 = r2
    //     0xc05314: stur            w2, [x1, #0x23]
    // 0xc05318: r3 = Instance_Clip
    //     0xc05318: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc0531c: ldr             x3, [x3, #0x38]
    // 0xc05320: StoreField: r1->field_2b = r3
    //     0xc05320: stur            w3, [x1, #0x2b]
    // 0xc05324: StoreField: r1->field_2f = rZR
    //     0xc05324: stur            xzr, [x1, #0x2f]
    // 0xc05328: ldur            x4, [fp, #-0x20]
    // 0xc0532c: StoreField: r1->field_b = r4
    //     0xc0532c: stur            w4, [x1, #0xb]
    // 0xc05330: ldur            x4, [fp, #-8]
    // 0xc05334: LoadField: r5 = r4->field_b
    //     0xc05334: ldur            w5, [x4, #0xb]
    // 0xc05338: DecompressPointer r5
    //     0xc05338: add             x5, x5, HEAP, lsl #32
    // 0xc0533c: cmp             w5, NULL
    // 0xc05340: b.eq            #0xc05fcc
    // 0xc05344: LoadField: r6 = r5->field_b
    //     0xc05344: ldur            w6, [x5, #0xb]
    // 0xc05348: DecompressPointer r6
    //     0xc05348: add             x6, x6, HEAP, lsl #32
    // 0xc0534c: LoadField: r5 = r6->field_db
    //     0xc0534c: ldur            w5, [x6, #0xdb]
    // 0xc05350: DecompressPointer r5
    //     0xc05350: add             x5, x5, HEAP, lsl #32
    // 0xc05354: cmp             w5, NULL
    // 0xc05358: b.ne            #0xc05364
    // 0xc0535c: r5 = Null
    //     0xc0535c: mov             x5, NULL
    // 0xc05360: b               #0xc05378
    // 0xc05364: LoadField: r6 = r5->field_b
    //     0xc05364: ldur            w6, [x5, #0xb]
    // 0xc05368: cbnz            w6, #0xc05374
    // 0xc0536c: r5 = false
    //     0xc0536c: add             x5, NULL, #0x30  ; false
    // 0xc05370: b               #0xc05378
    // 0xc05374: r5 = true
    //     0xc05374: add             x5, NULL, #0x20  ; true
    // 0xc05378: cmp             w5, NULL
    // 0xc0537c: b.ne            #0xc05384
    // 0xc05380: r5 = false
    //     0xc05380: add             x5, NULL, #0x30  ; false
    // 0xc05384: stur            x5, [fp, #-0x10]
    // 0xc05388: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xc05388: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc0538c: ldr             x0, [x0, #0x1c80]
    //     0xc05390: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc05394: cmp             w0, w16
    //     0xc05398: b.ne            #0xc053a4
    //     0xc0539c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xc053a0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xc053a4: r0 = GetNavigation.size()
    //     0xc053a4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xc053a8: LoadField: d0 = r0->field_f
    //     0xc053a8: ldur            d0, [x0, #0xf]
    // 0xc053ac: d1 = 0.124000
    //     0xc053ac: add             x17, PP, #0x52, lsl #12  ; [pp+0x52720] IMM: double(0.124) from 0x3fbfbe76c8b43958
    //     0xc053b0: ldr             d1, [x17, #0x720]
    // 0xc053b4: fmul            d2, d0, d1
    // 0xc053b8: stur            d2, [fp, #-0x50]
    // 0xc053bc: r0 = BoxConstraints()
    //     0xc053bc: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xc053c0: stur            x0, [fp, #-0x30]
    // 0xc053c4: StoreField: r0->field_7 = rZR
    //     0xc053c4: stur            xzr, [x0, #7]
    // 0xc053c8: d0 = inf
    //     0xc053c8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xc053cc: StoreField: r0->field_f = d0
    //     0xc053cc: stur            d0, [x0, #0xf]
    // 0xc053d0: ArrayStore: r0[0] = rZR  ; List_8
    //     0xc053d0: stur            xzr, [x0, #0x17]
    // 0xc053d4: ldur            d0, [fp, #-0x50]
    // 0xc053d8: StoreField: r0->field_1f = d0
    //     0xc053d8: stur            d0, [x0, #0x1f]
    // 0xc053dc: ldur            x3, [fp, #-8]
    // 0xc053e0: LoadField: r1 = r3->field_b
    //     0xc053e0: ldur            w1, [x3, #0xb]
    // 0xc053e4: DecompressPointer r1
    //     0xc053e4: add             x1, x1, HEAP, lsl #32
    // 0xc053e8: cmp             w1, NULL
    // 0xc053ec: b.eq            #0xc05fd0
    // 0xc053f0: LoadField: r2 = r1->field_b
    //     0xc053f0: ldur            w2, [x1, #0xb]
    // 0xc053f4: DecompressPointer r2
    //     0xc053f4: add             x2, x2, HEAP, lsl #32
    // 0xc053f8: LoadField: r1 = r2->field_db
    //     0xc053f8: ldur            w1, [x2, #0xdb]
    // 0xc053fc: DecompressPointer r1
    //     0xc053fc: add             x1, x1, HEAP, lsl #32
    // 0xc05400: cmp             w1, NULL
    // 0xc05404: b.ne            #0xc05410
    // 0xc05408: r6 = Null
    //     0xc05408: mov             x6, NULL
    // 0xc0540c: b               #0xc05418
    // 0xc05410: LoadField: r2 = r1->field_b
    //     0xc05410: ldur            w2, [x1, #0xb]
    // 0xc05414: mov             x6, x2
    // 0xc05418: ldur            x4, [fp, #-0x28]
    // 0xc0541c: ldur            x5, [fp, #-0x10]
    // 0xc05420: ldur            x2, [fp, #-0x18]
    // 0xc05424: stur            x6, [fp, #-0x20]
    // 0xc05428: r1 = Function '<anonymous closure>':.
    //     0xc05428: add             x1, PP, #0x52, lsl #12  ; [pp+0x52728] AnonymousClosure: (0xa85138), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc0542c: ldr             x1, [x1, #0x728]
    // 0xc05430: r0 = AllocateClosure()
    //     0xc05430: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc05434: stur            x0, [fp, #-0x38]
    // 0xc05438: r0 = ListView()
    //     0xc05438: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xc0543c: stur            x0, [fp, #-0x40]
    // 0xc05440: r16 = true
    //     0xc05440: add             x16, NULL, #0x20  ; true
    // 0xc05444: r30 = Instance_Axis
    //     0xc05444: ldr             lr, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xc05448: stp             lr, x16, [SP]
    // 0xc0544c: mov             x1, x0
    // 0xc05450: ldur            x2, [fp, #-0x38]
    // 0xc05454: ldur            x3, [fp, #-0x20]
    // 0xc05458: r4 = const [0, 0x5, 0x2, 0x3, scrollDirection, 0x4, shrinkWrap, 0x3, null]
    //     0xc05458: add             x4, PP, #0x37, lsl #12  ; [pp+0x372d0] List(9) [0, 0x5, 0x2, 0x3, "scrollDirection", 0x4, "shrinkWrap", 0x3, Null]
    //     0xc0545c: ldr             x4, [x4, #0x2d0]
    // 0xc05460: r0 = ListView.builder()
    //     0xc05460: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xc05464: r0 = ConstrainedBox()
    //     0xc05464: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0xc05468: mov             x1, x0
    // 0xc0546c: ldur            x0, [fp, #-0x30]
    // 0xc05470: stur            x1, [fp, #-0x20]
    // 0xc05474: StoreField: r1->field_f = r0
    //     0xc05474: stur            w0, [x1, #0xf]
    // 0xc05478: ldur            x0, [fp, #-0x40]
    // 0xc0547c: StoreField: r1->field_b = r0
    //     0xc0547c: stur            w0, [x1, #0xb]
    // 0xc05480: r0 = Align()
    //     0xc05480: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc05484: mov             x1, x0
    // 0xc05488: r0 = Instance_Alignment
    //     0xc05488: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc0548c: ldr             x0, [x0, #0xfa0]
    // 0xc05490: stur            x1, [fp, #-0x30]
    // 0xc05494: StoreField: r1->field_f = r0
    //     0xc05494: stur            w0, [x1, #0xf]
    // 0xc05498: ldur            x2, [fp, #-0x20]
    // 0xc0549c: StoreField: r1->field_b = r2
    //     0xc0549c: stur            w2, [x1, #0xb]
    // 0xc054a0: r0 = Visibility()
    //     0xc054a0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xc054a4: mov             x3, x0
    // 0xc054a8: ldur            x0, [fp, #-0x30]
    // 0xc054ac: stur            x3, [fp, #-0x20]
    // 0xc054b0: StoreField: r3->field_b = r0
    //     0xc054b0: stur            w0, [x3, #0xb]
    // 0xc054b4: r0 = Instance_SizedBox
    //     0xc054b4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xc054b8: StoreField: r3->field_f = r0
    //     0xc054b8: stur            w0, [x3, #0xf]
    // 0xc054bc: ldur            x0, [fp, #-0x10]
    // 0xc054c0: StoreField: r3->field_13 = r0
    //     0xc054c0: stur            w0, [x3, #0x13]
    // 0xc054c4: r0 = false
    //     0xc054c4: add             x0, NULL, #0x30  ; false
    // 0xc054c8: ArrayStore: r3[0] = r0  ; List_4
    //     0xc054c8: stur            w0, [x3, #0x17]
    // 0xc054cc: StoreField: r3->field_1b = r0
    //     0xc054cc: stur            w0, [x3, #0x1b]
    // 0xc054d0: StoreField: r3->field_1f = r0
    //     0xc054d0: stur            w0, [x3, #0x1f]
    // 0xc054d4: StoreField: r3->field_23 = r0
    //     0xc054d4: stur            w0, [x3, #0x23]
    // 0xc054d8: StoreField: r3->field_27 = r0
    //     0xc054d8: stur            w0, [x3, #0x27]
    // 0xc054dc: StoreField: r3->field_2b = r0
    //     0xc054dc: stur            w0, [x3, #0x2b]
    // 0xc054e0: r1 = Null
    //     0xc054e0: mov             x1, NULL
    // 0xc054e4: r2 = 6
    //     0xc054e4: movz            x2, #0x6
    // 0xc054e8: r0 = AllocateArray()
    //     0xc054e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xc054ec: mov             x2, x0
    // 0xc054f0: ldur            x0, [fp, #-0x28]
    // 0xc054f4: stur            x2, [fp, #-0x10]
    // 0xc054f8: StoreField: r2->field_f = r0
    //     0xc054f8: stur            w0, [x2, #0xf]
    // 0xc054fc: r16 = Instance_Divider
    //     0xc054fc: add             x16, PP, #0x37, lsl #12  ; [pp+0x372e0] Obj!Divider@d66be1
    //     0xc05500: ldr             x16, [x16, #0x2e0]
    // 0xc05504: StoreField: r2->field_13 = r16
    //     0xc05504: stur            w16, [x2, #0x13]
    // 0xc05508: ldur            x0, [fp, #-0x20]
    // 0xc0550c: ArrayStore: r2[0] = r0  ; List_4
    //     0xc0550c: stur            w0, [x2, #0x17]
    // 0xc05510: r1 = <Widget>
    //     0xc05510: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xc05514: r0 = AllocateGrowableArray()
    //     0xc05514: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xc05518: mov             x1, x0
    // 0xc0551c: ldur            x0, [fp, #-0x10]
    // 0xc05520: stur            x1, [fp, #-0x20]
    // 0xc05524: StoreField: r1->field_f = r0
    //     0xc05524: stur            w0, [x1, #0xf]
    // 0xc05528: r0 = 6
    //     0xc05528: movz            x0, #0x6
    // 0xc0552c: StoreField: r1->field_b = r0
    //     0xc0552c: stur            w0, [x1, #0xb]
    // 0xc05530: ldur            x2, [fp, #-8]
    // 0xc05534: LoadField: r0 = r2->field_b
    //     0xc05534: ldur            w0, [x2, #0xb]
    // 0xc05538: DecompressPointer r0
    //     0xc05538: add             x0, x0, HEAP, lsl #32
    // 0xc0553c: cmp             w0, NULL
    // 0xc05540: b.eq            #0xc05fd4
    // 0xc05544: LoadField: r3 = r0->field_f
    //     0xc05544: ldur            w3, [x0, #0xf]
    // 0xc05548: DecompressPointer r3
    //     0xc05548: add             x3, x3, HEAP, lsl #32
    // 0xc0554c: r0 = LoadClassIdInstr(r3)
    //     0xc0554c: ldur            x0, [x3, #-1]
    //     0xc05550: ubfx            x0, x0, #0xc, #0x14
    // 0xc05554: r16 = "size"
    //     0xc05554: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xc05558: ldr             x16, [x16, #0x9c0]
    // 0xc0555c: stp             x16, x3, [SP]
    // 0xc05560: mov             lr, x0
    // 0xc05564: ldr             lr, [x21, lr, lsl #3]
    // 0xc05568: blr             lr
    // 0xc0556c: tbnz            w0, #4, #0xc056a4
    // 0xc05570: ldur            x2, [fp, #-0x18]
    // 0xc05574: ldur            x0, [fp, #-0x20]
    // 0xc05578: LoadField: r1 = r2->field_13
    //     0xc05578: ldur            w1, [x2, #0x13]
    // 0xc0557c: DecompressPointer r1
    //     0xc0557c: add             x1, x1, HEAP, lsl #32
    // 0xc05580: r0 = of()
    //     0xc05580: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc05584: LoadField: r1 = r0->field_87
    //     0xc05584: ldur            w1, [x0, #0x87]
    // 0xc05588: DecompressPointer r1
    //     0xc05588: add             x1, x1, HEAP, lsl #32
    // 0xc0558c: LoadField: r0 = r1->field_7
    //     0xc0558c: ldur            w0, [x1, #7]
    // 0xc05590: DecompressPointer r0
    //     0xc05590: add             x0, x0, HEAP, lsl #32
    // 0xc05594: stur            x0, [fp, #-0x10]
    // 0xc05598: r1 = Instance_Color
    //     0xc05598: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc0559c: d0 = 0.700000
    //     0xc0559c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc055a0: ldr             d0, [x17, #0xf48]
    // 0xc055a4: r0 = withOpacity()
    //     0xc055a4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc055a8: r16 = 16.000000
    //     0xc055a8: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc055ac: ldr             x16, [x16, #0x188]
    // 0xc055b0: stp             x16, x0, [SP]
    // 0xc055b4: ldur            x1, [fp, #-0x10]
    // 0xc055b8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc055b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc055bc: ldr             x4, [x4, #0x9b8]
    // 0xc055c0: r0 = copyWith()
    //     0xc055c0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc055c4: stur            x0, [fp, #-0x10]
    // 0xc055c8: r0 = Text()
    //     0xc055c8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc055cc: mov             x1, x0
    // 0xc055d0: r0 = "Size"
    //     0xc055d0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52730] "Size"
    //     0xc055d4: ldr             x0, [x0, #0x730]
    // 0xc055d8: stur            x1, [fp, #-0x28]
    // 0xc055dc: StoreField: r1->field_b = r0
    //     0xc055dc: stur            w0, [x1, #0xb]
    // 0xc055e0: ldur            x0, [fp, #-0x10]
    // 0xc055e4: StoreField: r1->field_13 = r0
    //     0xc055e4: stur            w0, [x1, #0x13]
    // 0xc055e8: r0 = Align()
    //     0xc055e8: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc055ec: mov             x1, x0
    // 0xc055f0: r0 = Instance_Alignment
    //     0xc055f0: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc055f4: ldr             x0, [x0, #0xfa0]
    // 0xc055f8: stur            x1, [fp, #-0x10]
    // 0xc055fc: StoreField: r1->field_f = r0
    //     0xc055fc: stur            w0, [x1, #0xf]
    // 0xc05600: ldur            x0, [fp, #-0x28]
    // 0xc05604: StoreField: r1->field_b = r0
    //     0xc05604: stur            w0, [x1, #0xb]
    // 0xc05608: r0 = Padding()
    //     0xc05608: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc0560c: mov             x2, x0
    // 0xc05610: r0 = Instance_EdgeInsets
    //     0xc05610: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc05614: ldr             x0, [x0, #0x668]
    // 0xc05618: stur            x2, [fp, #-0x28]
    // 0xc0561c: StoreField: r2->field_f = r0
    //     0xc0561c: stur            w0, [x2, #0xf]
    // 0xc05620: ldur            x1, [fp, #-0x10]
    // 0xc05624: StoreField: r2->field_b = r1
    //     0xc05624: stur            w1, [x2, #0xb]
    // 0xc05628: ldur            x3, [fp, #-0x20]
    // 0xc0562c: LoadField: r1 = r3->field_b
    //     0xc0562c: ldur            w1, [x3, #0xb]
    // 0xc05630: LoadField: r4 = r3->field_f
    //     0xc05630: ldur            w4, [x3, #0xf]
    // 0xc05634: DecompressPointer r4
    //     0xc05634: add             x4, x4, HEAP, lsl #32
    // 0xc05638: LoadField: r5 = r4->field_b
    //     0xc05638: ldur            w5, [x4, #0xb]
    // 0xc0563c: r4 = LoadInt32Instr(r1)
    //     0xc0563c: sbfx            x4, x1, #1, #0x1f
    // 0xc05640: stur            x4, [fp, #-0x48]
    // 0xc05644: r1 = LoadInt32Instr(r5)
    //     0xc05644: sbfx            x1, x5, #1, #0x1f
    // 0xc05648: cmp             x4, x1
    // 0xc0564c: b.ne            #0xc05658
    // 0xc05650: mov             x1, x3
    // 0xc05654: r0 = _growToNextCapacity()
    //     0xc05654: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc05658: ldur            x2, [fp, #-0x20]
    // 0xc0565c: ldur            x3, [fp, #-0x48]
    // 0xc05660: add             x0, x3, #1
    // 0xc05664: lsl             x1, x0, #1
    // 0xc05668: StoreField: r2->field_b = r1
    //     0xc05668: stur            w1, [x2, #0xb]
    // 0xc0566c: LoadField: r1 = r2->field_f
    //     0xc0566c: ldur            w1, [x2, #0xf]
    // 0xc05670: DecompressPointer r1
    //     0xc05670: add             x1, x1, HEAP, lsl #32
    // 0xc05674: ldur            x0, [fp, #-0x28]
    // 0xc05678: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc05678: add             x25, x1, x3, lsl #2
    //     0xc0567c: add             x25, x25, #0xf
    //     0xc05680: str             w0, [x25]
    //     0xc05684: tbz             w0, #0, #0xc056a0
    //     0xc05688: ldurb           w16, [x1, #-1]
    //     0xc0568c: ldurb           w17, [x0, #-1]
    //     0xc05690: and             x16, x17, x16, lsr #2
    //     0xc05694: tst             x16, HEAP, lsr #32
    //     0xc05698: b.eq            #0xc056a0
    //     0xc0569c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc056a0: b               #0xc057dc
    // 0xc056a4: ldur            x3, [fp, #-0x18]
    // 0xc056a8: ldur            x2, [fp, #-0x20]
    // 0xc056ac: r0 = Instance_Alignment
    //     0xc056ac: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc056b0: ldr             x0, [x0, #0xfa0]
    // 0xc056b4: LoadField: r1 = r3->field_13
    //     0xc056b4: ldur            w1, [x3, #0x13]
    // 0xc056b8: DecompressPointer r1
    //     0xc056b8: add             x1, x1, HEAP, lsl #32
    // 0xc056bc: r0 = of()
    //     0xc056bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc056c0: LoadField: r1 = r0->field_87
    //     0xc056c0: ldur            w1, [x0, #0x87]
    // 0xc056c4: DecompressPointer r1
    //     0xc056c4: add             x1, x1, HEAP, lsl #32
    // 0xc056c8: LoadField: r0 = r1->field_7
    //     0xc056c8: ldur            w0, [x1, #7]
    // 0xc056cc: DecompressPointer r0
    //     0xc056cc: add             x0, x0, HEAP, lsl #32
    // 0xc056d0: stur            x0, [fp, #-0x10]
    // 0xc056d4: r1 = Instance_Color
    //     0xc056d4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc056d8: d0 = 0.700000
    //     0xc056d8: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xc056dc: ldr             d0, [x17, #0xf48]
    // 0xc056e0: r0 = withOpacity()
    //     0xc056e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc056e4: r16 = 16.000000
    //     0xc056e4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc056e8: ldr             x16, [x16, #0x188]
    // 0xc056ec: stp             x16, x0, [SP]
    // 0xc056f0: ldur            x1, [fp, #-0x10]
    // 0xc056f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xc056f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xc056f8: ldr             x4, [x4, #0x9b8]
    // 0xc056fc: r0 = copyWith()
    //     0xc056fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc05700: stur            x0, [fp, #-0x10]
    // 0xc05704: r0 = Text()
    //     0xc05704: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc05708: mov             x1, x0
    // 0xc0570c: r0 = "Variant"
    //     0xc0570c: add             x0, PP, #0x52, lsl #12  ; [pp+0x52738] "Variant"
    //     0xc05710: ldr             x0, [x0, #0x738]
    // 0xc05714: stur            x1, [fp, #-0x28]
    // 0xc05718: StoreField: r1->field_b = r0
    //     0xc05718: stur            w0, [x1, #0xb]
    // 0xc0571c: ldur            x0, [fp, #-0x10]
    // 0xc05720: StoreField: r1->field_13 = r0
    //     0xc05720: stur            w0, [x1, #0x13]
    // 0xc05724: r0 = Align()
    //     0xc05724: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xc05728: mov             x1, x0
    // 0xc0572c: r0 = Instance_Alignment
    //     0xc0572c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa0] Obj!Alignment@d5a6e1
    //     0xc05730: ldr             x0, [x0, #0xfa0]
    // 0xc05734: stur            x1, [fp, #-0x10]
    // 0xc05738: StoreField: r1->field_f = r0
    //     0xc05738: stur            w0, [x1, #0xf]
    // 0xc0573c: ldur            x0, [fp, #-0x28]
    // 0xc05740: StoreField: r1->field_b = r0
    //     0xc05740: stur            w0, [x1, #0xb]
    // 0xc05744: r0 = Padding()
    //     0xc05744: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc05748: mov             x2, x0
    // 0xc0574c: r0 = Instance_EdgeInsets
    //     0xc0574c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc05750: ldr             x0, [x0, #0x668]
    // 0xc05754: stur            x2, [fp, #-0x28]
    // 0xc05758: StoreField: r2->field_f = r0
    //     0xc05758: stur            w0, [x2, #0xf]
    // 0xc0575c: ldur            x1, [fp, #-0x10]
    // 0xc05760: StoreField: r2->field_b = r1
    //     0xc05760: stur            w1, [x2, #0xb]
    // 0xc05764: ldur            x3, [fp, #-0x20]
    // 0xc05768: LoadField: r1 = r3->field_b
    //     0xc05768: ldur            w1, [x3, #0xb]
    // 0xc0576c: LoadField: r4 = r3->field_f
    //     0xc0576c: ldur            w4, [x3, #0xf]
    // 0xc05770: DecompressPointer r4
    //     0xc05770: add             x4, x4, HEAP, lsl #32
    // 0xc05774: LoadField: r5 = r4->field_b
    //     0xc05774: ldur            w5, [x4, #0xb]
    // 0xc05778: r4 = LoadInt32Instr(r1)
    //     0xc05778: sbfx            x4, x1, #1, #0x1f
    // 0xc0577c: stur            x4, [fp, #-0x48]
    // 0xc05780: r1 = LoadInt32Instr(r5)
    //     0xc05780: sbfx            x1, x5, #1, #0x1f
    // 0xc05784: cmp             x4, x1
    // 0xc05788: b.ne            #0xc05794
    // 0xc0578c: mov             x1, x3
    // 0xc05790: r0 = _growToNextCapacity()
    //     0xc05790: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc05794: ldur            x2, [fp, #-0x20]
    // 0xc05798: ldur            x3, [fp, #-0x48]
    // 0xc0579c: add             x0, x3, #1
    // 0xc057a0: lsl             x1, x0, #1
    // 0xc057a4: StoreField: r2->field_b = r1
    //     0xc057a4: stur            w1, [x2, #0xb]
    // 0xc057a8: LoadField: r1 = r2->field_f
    //     0xc057a8: ldur            w1, [x2, #0xf]
    // 0xc057ac: DecompressPointer r1
    //     0xc057ac: add             x1, x1, HEAP, lsl #32
    // 0xc057b0: ldur            x0, [fp, #-0x28]
    // 0xc057b4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc057b4: add             x25, x1, x3, lsl #2
    //     0xc057b8: add             x25, x25, #0xf
    //     0xc057bc: str             w0, [x25]
    //     0xc057c0: tbz             w0, #0, #0xc057dc
    //     0xc057c4: ldurb           w16, [x1, #-1]
    //     0xc057c8: ldurb           w17, [x0, #-1]
    //     0xc057cc: and             x16, x17, x16, lsr #2
    //     0xc057d0: tst             x16, HEAP, lsr #32
    //     0xc057d4: b.eq            #0xc057dc
    //     0xc057d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc057dc: ldur            x0, [fp, #-8]
    // 0xc057e0: LoadField: r1 = r0->field_b
    //     0xc057e0: ldur            w1, [x0, #0xb]
    // 0xc057e4: DecompressPointer r1
    //     0xc057e4: add             x1, x1, HEAP, lsl #32
    // 0xc057e8: cmp             w1, NULL
    // 0xc057ec: b.eq            #0xc05fd8
    // 0xc057f0: LoadField: r3 = r1->field_b
    //     0xc057f0: ldur            w3, [x1, #0xb]
    // 0xc057f4: DecompressPointer r3
    //     0xc057f4: add             x3, x3, HEAP, lsl #32
    // 0xc057f8: LoadField: r1 = r3->field_db
    //     0xc057f8: ldur            w1, [x3, #0xdb]
    // 0xc057fc: DecompressPointer r1
    //     0xc057fc: add             x1, x1, HEAP, lsl #32
    // 0xc05800: cmp             w1, NULL
    // 0xc05804: b.ne            #0xc05810
    // 0xc05808: r1 = Null
    //     0xc05808: mov             x1, NULL
    // 0xc0580c: b               #0xc05824
    // 0xc05810: LoadField: r3 = r1->field_b
    //     0xc05810: ldur            w3, [x1, #0xb]
    // 0xc05814: cbz             w3, #0xc05820
    // 0xc05818: r1 = false
    //     0xc05818: add             x1, NULL, #0x30  ; false
    // 0xc0581c: b               #0xc05824
    // 0xc05820: r1 = true
    //     0xc05820: add             x1, NULL, #0x20  ; true
    // 0xc05824: cmp             w1, NULL
    // 0xc05828: b.eq            #0xc05830
    // 0xc0582c: tbnz            w1, #4, #0xc05a3c
    // 0xc05830: r1 = Instance_Color
    //     0xc05830: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc05834: d0 = 0.100000
    //     0xc05834: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc05838: r0 = withOpacity()
    //     0xc05838: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc0583c: r16 = 1.000000
    //     0xc0583c: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc05840: str             x16, [SP]
    // 0xc05844: mov             x2, x0
    // 0xc05848: r1 = Null
    //     0xc05848: mov             x1, NULL
    // 0xc0584c: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xc0584c: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xc05850: ldr             x4, [x4, #0x108]
    // 0xc05854: r0 = Border.all()
    //     0xc05854: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc05858: stur            x0, [fp, #-0x10]
    // 0xc0585c: r0 = BoxDecoration()
    //     0xc0585c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc05860: mov             x3, x0
    // 0xc05864: r0 = Instance_Color
    //     0xc05864: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc05868: stur            x3, [fp, #-0x30]
    // 0xc0586c: StoreField: r3->field_7 = r0
    //     0xc0586c: stur            w0, [x3, #7]
    // 0xc05870: ldur            x0, [fp, #-0x10]
    // 0xc05874: StoreField: r3->field_f = r0
    //     0xc05874: stur            w0, [x3, #0xf]
    // 0xc05878: r2 = Instance_BoxShape
    //     0xc05878: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc0587c: ldr             x2, [x2, #0x80]
    // 0xc05880: StoreField: r3->field_23 = r2
    //     0xc05880: stur            w2, [x3, #0x23]
    // 0xc05884: ldur            x0, [fp, #-8]
    // 0xc05888: LoadField: r5 = r0->field_13
    //     0xc05888: ldur            w5, [x0, #0x13]
    // 0xc0588c: DecompressPointer r5
    //     0xc0588c: add             x5, x5, HEAP, lsl #32
    // 0xc05890: r16 = Sentinel
    //     0xc05890: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc05894: cmp             w5, w16
    // 0xc05898: b.eq            #0xc05fdc
    // 0xc0589c: stur            x5, [fp, #-0x28]
    // 0xc058a0: LoadField: r1 = r0->field_b
    //     0xc058a0: ldur            w1, [x0, #0xb]
    // 0xc058a4: DecompressPointer r1
    //     0xc058a4: add             x1, x1, HEAP, lsl #32
    // 0xc058a8: cmp             w1, NULL
    // 0xc058ac: b.eq            #0xc05fe8
    // 0xc058b0: LoadField: r2 = r1->field_b
    //     0xc058b0: ldur            w2, [x1, #0xb]
    // 0xc058b4: DecompressPointer r2
    //     0xc058b4: add             x2, x2, HEAP, lsl #32
    // 0xc058b8: LoadField: r4 = r2->field_d7
    //     0xc058b8: ldur            w4, [x2, #0xd7]
    // 0xc058bc: DecompressPointer r4
    //     0xc058bc: add             x4, x4, HEAP, lsl #32
    // 0xc058c0: stur            x4, [fp, #-0x10]
    // 0xc058c4: cmp             w4, NULL
    // 0xc058c8: b.ne            #0xc058d4
    // 0xc058cc: r3 = Null
    //     0xc058cc: mov             x3, NULL
    // 0xc058d0: b               #0xc05910
    // 0xc058d4: ldur            x2, [fp, #-0x18]
    // 0xc058d8: r1 = Function '<anonymous closure>':.
    //     0xc058d8: add             x1, PP, #0x52, lsl #12  ; [pp+0x52740] AnonymousClosure: (0xc06718), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc058dc: ldr             x1, [x1, #0x740]
    // 0xc058e0: r0 = AllocateClosure()
    //     0xc058e0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc058e4: r16 = <DropdownMenuItem<AllSkuDatum>>
    //     0xc058e4: add             x16, PP, #0x52, lsl #12  ; [pp+0x52748] TypeArguments: <DropdownMenuItem<AllSkuDatum>>
    //     0xc058e8: ldr             x16, [x16, #0x748]
    // 0xc058ec: ldur            lr, [fp, #-0x10]
    // 0xc058f0: stp             lr, x16, [SP, #8]
    // 0xc058f4: str             x0, [SP]
    // 0xc058f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc058f8: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc058fc: r0 = map()
    //     0xc058fc: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xc05900: mov             x1, x0
    // 0xc05904: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc05904: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc05908: r0 = toList()
    //     0xc05908: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xc0590c: mov             x3, x0
    // 0xc05910: ldur            x0, [fp, #-0x20]
    // 0xc05914: ldur            x2, [fp, #-0x18]
    // 0xc05918: stur            x3, [fp, #-0x10]
    // 0xc0591c: r1 = Function '<anonymous closure>':.
    //     0xc0591c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52750] AnonymousClosure: (0xc06568), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc05920: ldr             x1, [x1, #0x750]
    // 0xc05924: r0 = AllocateClosure()
    //     0xc05924: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc05928: r1 = <AllSkuDatum>
    //     0xc05928: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xc0592c: ldr             x1, [x1, #0xea8]
    // 0xc05930: stur            x0, [fp, #-0x38]
    // 0xc05934: r0 = DropdownButton()
    //     0xc05934: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xc05938: mov             x1, x0
    // 0xc0593c: ldur            x2, [fp, #-0x10]
    // 0xc05940: ldur            x3, [fp, #-0x38]
    // 0xc05944: ldur            x5, [fp, #-0x28]
    // 0xc05948: stur            x0, [fp, #-0x10]
    // 0xc0594c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xc0594c: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xc05950: r0 = DropdownButton()
    //     0xc05950: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xc05954: r0 = DropdownButtonHideUnderline()
    //     0xc05954: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xc05958: mov             x1, x0
    // 0xc0595c: ldur            x0, [fp, #-0x10]
    // 0xc05960: stur            x1, [fp, #-0x28]
    // 0xc05964: StoreField: r1->field_b = r0
    //     0xc05964: stur            w0, [x1, #0xb]
    // 0xc05968: r0 = Container()
    //     0xc05968: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc0596c: stur            x0, [fp, #-0x10]
    // 0xc05970: r16 = inf
    //     0xc05970: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc05974: ldr             x16, [x16, #0x9f8]
    // 0xc05978: r30 = Instance_AlignmentDirectional
    //     0xc05978: add             lr, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xc0597c: ldr             lr, [lr, #0x3a8]
    // 0xc05980: stp             lr, x16, [SP, #0x10]
    // 0xc05984: ldur            x16, [fp, #-0x30]
    // 0xc05988: ldur            lr, [fp, #-0x28]
    // 0xc0598c: stp             lr, x16, [SP]
    // 0xc05990: mov             x1, x0
    // 0xc05994: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, child, 0x4, decoration, 0x3, width, 0x1, null]
    //     0xc05994: add             x4, PP, #0x52, lsl #12  ; [pp+0x52758] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "child", 0x4, "decoration", 0x3, "width", 0x1, Null]
    //     0xc05998: ldr             x4, [x4, #0x758]
    // 0xc0599c: r0 = Container()
    //     0xc0599c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc059a0: r0 = Padding()
    //     0xc059a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc059a4: r3 = Instance_EdgeInsets
    //     0xc059a4: add             x3, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc059a8: ldr             x3, [x3, #0x668]
    // 0xc059ac: stur            x0, [fp, #-0x28]
    // 0xc059b0: StoreField: r0->field_f = r3
    //     0xc059b0: stur            w3, [x0, #0xf]
    // 0xc059b4: ldur            x1, [fp, #-0x10]
    // 0xc059b8: StoreField: r0->field_b = r1
    //     0xc059b8: stur            w1, [x0, #0xb]
    // 0xc059bc: ldur            x2, [fp, #-0x20]
    // 0xc059c0: LoadField: r1 = r2->field_b
    //     0xc059c0: ldur            w1, [x2, #0xb]
    // 0xc059c4: LoadField: r3 = r2->field_f
    //     0xc059c4: ldur            w3, [x2, #0xf]
    // 0xc059c8: DecompressPointer r3
    //     0xc059c8: add             x3, x3, HEAP, lsl #32
    // 0xc059cc: LoadField: r4 = r3->field_b
    //     0xc059cc: ldur            w4, [x3, #0xb]
    // 0xc059d0: r3 = LoadInt32Instr(r1)
    //     0xc059d0: sbfx            x3, x1, #1, #0x1f
    // 0xc059d4: stur            x3, [fp, #-0x48]
    // 0xc059d8: r1 = LoadInt32Instr(r4)
    //     0xc059d8: sbfx            x1, x4, #1, #0x1f
    // 0xc059dc: cmp             x3, x1
    // 0xc059e0: b.ne            #0xc059ec
    // 0xc059e4: mov             x1, x2
    // 0xc059e8: r0 = _growToNextCapacity()
    //     0xc059e8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc059ec: ldur            x4, [fp, #-0x20]
    // 0xc059f0: ldur            x2, [fp, #-0x48]
    // 0xc059f4: add             x0, x2, #1
    // 0xc059f8: lsl             x1, x0, #1
    // 0xc059fc: StoreField: r4->field_b = r1
    //     0xc059fc: stur            w1, [x4, #0xb]
    // 0xc05a00: LoadField: r1 = r4->field_f
    //     0xc05a00: ldur            w1, [x4, #0xf]
    // 0xc05a04: DecompressPointer r1
    //     0xc05a04: add             x1, x1, HEAP, lsl #32
    // 0xc05a08: ldur            x0, [fp, #-0x28]
    // 0xc05a0c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc05a0c: add             x25, x1, x2, lsl #2
    //     0xc05a10: add             x25, x25, #0xf
    //     0xc05a14: str             w0, [x25]
    //     0xc05a18: tbz             w0, #0, #0xc05a34
    //     0xc05a1c: ldurb           w16, [x1, #-1]
    //     0xc05a20: ldurb           w17, [x0, #-1]
    //     0xc05a24: and             x16, x17, x16, lsr #2
    //     0xc05a28: tst             x16, HEAP, lsr #32
    //     0xc05a2c: b.eq            #0xc05a34
    //     0xc05a30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc05a34: mov             x2, x4
    // 0xc05a38: b               #0xc05ca8
    // 0xc05a3c: mov             x5, x0
    // 0xc05a40: mov             x4, x2
    // 0xc05a44: r3 = Instance_EdgeInsets
    //     0xc05a44: add             x3, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc05a48: ldr             x3, [x3, #0x668]
    // 0xc05a4c: r0 = Instance_Color
    //     0xc05a4c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc05a50: r2 = Instance_BoxShape
    //     0xc05a50: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc05a54: ldr             x2, [x2, #0x80]
    // 0xc05a58: r1 = Instance_Color
    //     0xc05a58: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc05a5c: d0 = 0.100000
    //     0xc05a5c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xc05a60: r0 = withOpacity()
    //     0xc05a60: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xc05a64: r16 = 1.000000
    //     0xc05a64: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xc05a68: str             x16, [SP]
    // 0xc05a6c: mov             x2, x0
    // 0xc05a70: r1 = Null
    //     0xc05a70: mov             x1, NULL
    // 0xc05a74: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xc05a74: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xc05a78: ldr             x4, [x4, #0x108]
    // 0xc05a7c: r0 = Border.all()
    //     0xc05a7c: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xc05a80: stur            x0, [fp, #-0x10]
    // 0xc05a84: r0 = BoxDecoration()
    //     0xc05a84: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xc05a88: mov             x3, x0
    // 0xc05a8c: r0 = Instance_Color
    //     0xc05a8c: ldr             x0, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc05a90: stur            x3, [fp, #-0x30]
    // 0xc05a94: StoreField: r3->field_7 = r0
    //     0xc05a94: stur            w0, [x3, #7]
    // 0xc05a98: ldur            x0, [fp, #-0x10]
    // 0xc05a9c: StoreField: r3->field_f = r0
    //     0xc05a9c: stur            w0, [x3, #0xf]
    // 0xc05aa0: r0 = Instance_BoxShape
    //     0xc05aa0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xc05aa4: ldr             x0, [x0, #0x80]
    // 0xc05aa8: StoreField: r3->field_23 = r0
    //     0xc05aa8: stur            w0, [x3, #0x23]
    // 0xc05aac: ldur            x4, [fp, #-8]
    // 0xc05ab0: LoadField: r5 = r4->field_13
    //     0xc05ab0: ldur            w5, [x4, #0x13]
    // 0xc05ab4: DecompressPointer r5
    //     0xc05ab4: add             x5, x5, HEAP, lsl #32
    // 0xc05ab8: r16 = Sentinel
    //     0xc05ab8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc05abc: cmp             w5, w16
    // 0xc05ac0: b.eq            #0xc05fec
    // 0xc05ac4: stur            x5, [fp, #-0x28]
    // 0xc05ac8: LoadField: r0 = r4->field_b
    //     0xc05ac8: ldur            w0, [x4, #0xb]
    // 0xc05acc: DecompressPointer r0
    //     0xc05acc: add             x0, x0, HEAP, lsl #32
    // 0xc05ad0: cmp             w0, NULL
    // 0xc05ad4: b.eq            #0xc05ff8
    // 0xc05ad8: LoadField: r1 = r0->field_b
    //     0xc05ad8: ldur            w1, [x0, #0xb]
    // 0xc05adc: DecompressPointer r1
    //     0xc05adc: add             x1, x1, HEAP, lsl #32
    // 0xc05ae0: LoadField: r2 = r1->field_db
    //     0xc05ae0: ldur            w2, [x1, #0xdb]
    // 0xc05ae4: DecompressPointer r2
    //     0xc05ae4: add             x2, x2, HEAP, lsl #32
    // 0xc05ae8: cmp             w2, NULL
    // 0xc05aec: b.ne            #0xc05af8
    // 0xc05af0: r3 = Null
    //     0xc05af0: mov             x3, NULL
    // 0xc05af4: b               #0xc05b80
    // 0xc05af8: LoadField: r6 = r4->field_1f
    //     0xc05af8: ldur            x6, [x4, #0x1f]
    // 0xc05afc: LoadField: r0 = r2->field_b
    //     0xc05afc: ldur            w0, [x2, #0xb]
    // 0xc05b00: r1 = LoadInt32Instr(r0)
    //     0xc05b00: sbfx            x1, x0, #1, #0x1f
    // 0xc05b04: mov             x0, x1
    // 0xc05b08: mov             x1, x6
    // 0xc05b0c: cmp             x1, x0
    // 0xc05b10: b.hs            #0xc05ffc
    // 0xc05b14: LoadField: r0 = r2->field_f
    //     0xc05b14: ldur            w0, [x2, #0xf]
    // 0xc05b18: DecompressPointer r0
    //     0xc05b18: add             x0, x0, HEAP, lsl #32
    // 0xc05b1c: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xc05b1c: add             x16, x0, x6, lsl #2
    //     0xc05b20: ldur            w1, [x16, #0xf]
    // 0xc05b24: DecompressPointer r1
    //     0xc05b24: add             x1, x1, HEAP, lsl #32
    // 0xc05b28: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc05b28: ldur            w0, [x1, #0x17]
    // 0xc05b2c: DecompressPointer r0
    //     0xc05b2c: add             x0, x0, HEAP, lsl #32
    // 0xc05b30: stur            x0, [fp, #-0x10]
    // 0xc05b34: cmp             w0, NULL
    // 0xc05b38: b.ne            #0xc05b44
    // 0xc05b3c: r0 = Null
    //     0xc05b3c: mov             x0, NULL
    // 0xc05b40: b               #0xc05b7c
    // 0xc05b44: ldur            x2, [fp, #-0x18]
    // 0xc05b48: r1 = Function '<anonymous closure>':.
    //     0xc05b48: add             x1, PP, #0x52, lsl #12  ; [pp+0x52760] AnonymousClosure: (0xbf0138), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc05b4c: ldr             x1, [x1, #0x760]
    // 0xc05b50: r0 = AllocateClosure()
    //     0xc05b50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc05b54: r16 = <DropdownMenuItem<AllSkuDatum>>
    //     0xc05b54: add             x16, PP, #0x52, lsl #12  ; [pp+0x52748] TypeArguments: <DropdownMenuItem<AllSkuDatum>>
    //     0xc05b58: ldr             x16, [x16, #0x748]
    // 0xc05b5c: ldur            lr, [fp, #-0x10]
    // 0xc05b60: stp             lr, x16, [SP, #8]
    // 0xc05b64: str             x0, [SP]
    // 0xc05b68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc05b68: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc05b6c: r0 = map()
    //     0xc05b6c: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0xc05b70: mov             x1, x0
    // 0xc05b74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc05b74: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc05b78: r0 = toList()
    //     0xc05b78: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xc05b7c: mov             x3, x0
    // 0xc05b80: ldur            x0, [fp, #-0x20]
    // 0xc05b84: ldur            x2, [fp, #-0x18]
    // 0xc05b88: stur            x3, [fp, #-0x10]
    // 0xc05b8c: r1 = Function '<anonymous closure>':.
    //     0xc05b8c: add             x1, PP, #0x52, lsl #12  ; [pp+0x52768] AnonymousClosure: (0xc06368), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc05b90: ldr             x1, [x1, #0x768]
    // 0xc05b94: r0 = AllocateClosure()
    //     0xc05b94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc05b98: r1 = <AllSkuDatum>
    //     0xc05b98: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xc05b9c: ldr             x1, [x1, #0xea8]
    // 0xc05ba0: stur            x0, [fp, #-0x38]
    // 0xc05ba4: r0 = DropdownButton()
    //     0xc05ba4: bl              #0x9b6174  ; AllocateDropdownButtonStub -> DropdownButton<X0> (size=0x90)
    // 0xc05ba8: mov             x1, x0
    // 0xc05bac: ldur            x2, [fp, #-0x10]
    // 0xc05bb0: ldur            x3, [fp, #-0x38]
    // 0xc05bb4: ldur            x5, [fp, #-0x28]
    // 0xc05bb8: stur            x0, [fp, #-0x10]
    // 0xc05bbc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0xc05bbc: ldr             x4, [PP, #0xfb8]  ; [pp+0xfb8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0xc05bc0: r0 = DropdownButton()
    //     0xc05bc0: bl              #0x9b5e1c  ; [package:flutter/src/material/dropdown.dart] DropdownButton::DropdownButton
    // 0xc05bc4: r0 = DropdownButtonHideUnderline()
    //     0xc05bc4: bl              #0x9b5e10  ; AllocateDropdownButtonHideUnderlineStub -> DropdownButtonHideUnderline (size=0x10)
    // 0xc05bc8: mov             x1, x0
    // 0xc05bcc: ldur            x0, [fp, #-0x10]
    // 0xc05bd0: stur            x1, [fp, #-0x28]
    // 0xc05bd4: StoreField: r1->field_b = r0
    //     0xc05bd4: stur            w0, [x1, #0xb]
    // 0xc05bd8: r0 = Container()
    //     0xc05bd8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xc05bdc: stur            x0, [fp, #-0x10]
    // 0xc05be0: r16 = inf
    //     0xc05be0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc05be4: ldr             x16, [x16, #0x9f8]
    // 0xc05be8: r30 = Instance_AlignmentDirectional
    //     0xc05be8: add             lr, PP, #0x52, lsl #12  ; [pp+0x523a8] Obj!AlignmentDirectional@d5a621
    //     0xc05bec: ldr             lr, [lr, #0x3a8]
    // 0xc05bf0: stp             lr, x16, [SP, #0x10]
    // 0xc05bf4: ldur            x16, [fp, #-0x30]
    // 0xc05bf8: ldur            lr, [fp, #-0x28]
    // 0xc05bfc: stp             lr, x16, [SP]
    // 0xc05c00: mov             x1, x0
    // 0xc05c04: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, child, 0x4, decoration, 0x3, width, 0x1, null]
    //     0xc05c04: add             x4, PP, #0x52, lsl #12  ; [pp+0x52758] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "child", 0x4, "decoration", 0x3, "width", 0x1, Null]
    //     0xc05c08: ldr             x4, [x4, #0x758]
    // 0xc05c0c: r0 = Container()
    //     0xc05c0c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc05c10: r0 = Padding()
    //     0xc05c10: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc05c14: mov             x2, x0
    // 0xc05c18: r0 = Instance_EdgeInsets
    //     0xc05c18: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xc05c1c: ldr             x0, [x0, #0x668]
    // 0xc05c20: stur            x2, [fp, #-0x28]
    // 0xc05c24: StoreField: r2->field_f = r0
    //     0xc05c24: stur            w0, [x2, #0xf]
    // 0xc05c28: ldur            x0, [fp, #-0x10]
    // 0xc05c2c: StoreField: r2->field_b = r0
    //     0xc05c2c: stur            w0, [x2, #0xb]
    // 0xc05c30: ldur            x0, [fp, #-0x20]
    // 0xc05c34: LoadField: r1 = r0->field_b
    //     0xc05c34: ldur            w1, [x0, #0xb]
    // 0xc05c38: LoadField: r3 = r0->field_f
    //     0xc05c38: ldur            w3, [x0, #0xf]
    // 0xc05c3c: DecompressPointer r3
    //     0xc05c3c: add             x3, x3, HEAP, lsl #32
    // 0xc05c40: LoadField: r4 = r3->field_b
    //     0xc05c40: ldur            w4, [x3, #0xb]
    // 0xc05c44: r3 = LoadInt32Instr(r1)
    //     0xc05c44: sbfx            x3, x1, #1, #0x1f
    // 0xc05c48: stur            x3, [fp, #-0x48]
    // 0xc05c4c: r1 = LoadInt32Instr(r4)
    //     0xc05c4c: sbfx            x1, x4, #1, #0x1f
    // 0xc05c50: cmp             x3, x1
    // 0xc05c54: b.ne            #0xc05c60
    // 0xc05c58: mov             x1, x0
    // 0xc05c5c: r0 = _growToNextCapacity()
    //     0xc05c5c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc05c60: ldur            x2, [fp, #-0x20]
    // 0xc05c64: ldur            x3, [fp, #-0x48]
    // 0xc05c68: add             x0, x3, #1
    // 0xc05c6c: lsl             x1, x0, #1
    // 0xc05c70: StoreField: r2->field_b = r1
    //     0xc05c70: stur            w1, [x2, #0xb]
    // 0xc05c74: LoadField: r1 = r2->field_f
    //     0xc05c74: ldur            w1, [x2, #0xf]
    // 0xc05c78: DecompressPointer r1
    //     0xc05c78: add             x1, x1, HEAP, lsl #32
    // 0xc05c7c: ldur            x0, [fp, #-0x28]
    // 0xc05c80: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc05c80: add             x25, x1, x3, lsl #2
    //     0xc05c84: add             x25, x25, #0xf
    //     0xc05c88: str             w0, [x25]
    //     0xc05c8c: tbz             w0, #0, #0xc05ca8
    //     0xc05c90: ldurb           w16, [x1, #-1]
    //     0xc05c94: ldurb           w17, [x0, #-1]
    //     0xc05c98: and             x16, x17, x16, lsr #2
    //     0xc05c9c: tst             x16, HEAP, lsr #32
    //     0xc05ca0: b.eq            #0xc05ca8
    //     0xc05ca4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc05ca8: ldur            x0, [fp, #-8]
    // 0xc05cac: ldur            x1, [fp, #-0x18]
    // 0xc05cb0: r16 = <EdgeInsets>
    //     0xc05cb0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xc05cb4: ldr             x16, [x16, #0xda0]
    // 0xc05cb8: r30 = Instance_EdgeInsets
    //     0xc05cb8: add             lr, PP, #0x34, lsl #12  ; [pp+0x34670] Obj!EdgeInsets@d572c1
    //     0xc05cbc: ldr             lr, [lr, #0x670]
    // 0xc05cc0: stp             lr, x16, [SP]
    // 0xc05cc4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc05cc4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc05cc8: r0 = all()
    //     0xc05cc8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc05ccc: ldur            x2, [fp, #-0x18]
    // 0xc05cd0: stur            x0, [fp, #-0x10]
    // 0xc05cd4: LoadField: r1 = r2->field_13
    //     0xc05cd4: ldur            w1, [x2, #0x13]
    // 0xc05cd8: DecompressPointer r1
    //     0xc05cd8: add             x1, x1, HEAP, lsl #32
    // 0xc05cdc: r0 = of()
    //     0xc05cdc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc05ce0: LoadField: r1 = r0->field_5b
    //     0xc05ce0: ldur            w1, [x0, #0x5b]
    // 0xc05ce4: DecompressPointer r1
    //     0xc05ce4: add             x1, x1, HEAP, lsl #32
    // 0xc05ce8: r16 = <Color>
    //     0xc05ce8: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xc05cec: ldr             x16, [x16, #0xf80]
    // 0xc05cf0: stp             x1, x16, [SP]
    // 0xc05cf4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc05cf4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc05cf8: r0 = all()
    //     0xc05cf8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc05cfc: stur            x0, [fp, #-0x28]
    // 0xc05d00: r16 = <RoundedRectangleBorder>
    //     0xc05d00: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xc05d04: ldr             x16, [x16, #0xf78]
    // 0xc05d08: r30 = Instance_RoundedRectangleBorder
    //     0xc05d08: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xc05d0c: ldr             lr, [lr, #0xd68]
    // 0xc05d10: stp             lr, x16, [SP]
    // 0xc05d14: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc05d14: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc05d18: r0 = all()
    //     0xc05d18: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xc05d1c: stur            x0, [fp, #-0x30]
    // 0xc05d20: r0 = ButtonStyle()
    //     0xc05d20: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xc05d24: mov             x1, x0
    // 0xc05d28: ldur            x0, [fp, #-0x28]
    // 0xc05d2c: stur            x1, [fp, #-0x38]
    // 0xc05d30: StoreField: r1->field_b = r0
    //     0xc05d30: stur            w0, [x1, #0xb]
    // 0xc05d34: ldur            x0, [fp, #-0x10]
    // 0xc05d38: StoreField: r1->field_23 = r0
    //     0xc05d38: stur            w0, [x1, #0x23]
    // 0xc05d3c: ldur            x0, [fp, #-0x30]
    // 0xc05d40: StoreField: r1->field_43 = r0
    //     0xc05d40: stur            w0, [x1, #0x43]
    // 0xc05d44: r0 = TextButtonThemeData()
    //     0xc05d44: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xc05d48: mov             x1, x0
    // 0xc05d4c: ldur            x0, [fp, #-0x38]
    // 0xc05d50: stur            x1, [fp, #-0x10]
    // 0xc05d54: StoreField: r1->field_7 = r0
    //     0xc05d54: stur            w0, [x1, #7]
    // 0xc05d58: ldur            x0, [fp, #-8]
    // 0xc05d5c: LoadField: r2 = r0->field_b
    //     0xc05d5c: ldur            w2, [x0, #0xb]
    // 0xc05d60: DecompressPointer r2
    //     0xc05d60: add             x2, x2, HEAP, lsl #32
    // 0xc05d64: cmp             w2, NULL
    // 0xc05d68: b.eq            #0xc06000
    // 0xc05d6c: LoadField: r0 = r2->field_b
    //     0xc05d6c: ldur            w0, [x2, #0xb]
    // 0xc05d70: DecompressPointer r0
    //     0xc05d70: add             x0, x0, HEAP, lsl #32
    // 0xc05d74: LoadField: r2 = r0->field_f
    //     0xc05d74: ldur            w2, [x0, #0xf]
    // 0xc05d78: DecompressPointer r2
    //     0xc05d78: add             x2, x2, HEAP, lsl #32
    // 0xc05d7c: cmp             w2, NULL
    // 0xc05d80: b.ne            #0xc05d8c
    // 0xc05d84: r0 = Null
    //     0xc05d84: mov             x0, NULL
    // 0xc05d88: b               #0xc05da4
    // 0xc05d8c: r0 = LoadClassIdInstr(r2)
    //     0xc05d8c: ldur            x0, [x2, #-1]
    //     0xc05d90: ubfx            x0, x0, #0xc, #0x14
    // 0xc05d94: str             x2, [SP]
    // 0xc05d98: r0 = GDT[cid_x0 + -0x1000]()
    //     0xc05d98: sub             lr, x0, #1, lsl #12
    //     0xc05d9c: ldr             lr, [x21, lr, lsl #3]
    //     0xc05da0: blr             lr
    // 0xc05da4: cmp             w0, NULL
    // 0xc05da8: b.ne            #0xc05db4
    // 0xc05dac: r4 = ""
    //     0xc05dac: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc05db0: b               #0xc05db8
    // 0xc05db4: mov             x4, x0
    // 0xc05db8: ldur            x2, [fp, #-0x18]
    // 0xc05dbc: ldur            x0, [fp, #-0x10]
    // 0xc05dc0: ldur            x3, [fp, #-0x20]
    // 0xc05dc4: stur            x4, [fp, #-8]
    // 0xc05dc8: LoadField: r1 = r2->field_13
    //     0xc05dc8: ldur            w1, [x2, #0x13]
    // 0xc05dcc: DecompressPointer r1
    //     0xc05dcc: add             x1, x1, HEAP, lsl #32
    // 0xc05dd0: r0 = of()
    //     0xc05dd0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc05dd4: LoadField: r1 = r0->field_87
    //     0xc05dd4: ldur            w1, [x0, #0x87]
    // 0xc05dd8: DecompressPointer r1
    //     0xc05dd8: add             x1, x1, HEAP, lsl #32
    // 0xc05ddc: LoadField: r0 = r1->field_7
    //     0xc05ddc: ldur            w0, [x1, #7]
    // 0xc05de0: DecompressPointer r0
    //     0xc05de0: add             x0, x0, HEAP, lsl #32
    // 0xc05de4: r16 = 16.000000
    //     0xc05de4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xc05de8: ldr             x16, [x16, #0x188]
    // 0xc05dec: r30 = Instance_Color
    //     0xc05dec: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xc05df0: stp             lr, x16, [SP]
    // 0xc05df4: mov             x1, x0
    // 0xc05df8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc05df8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc05dfc: ldr             x4, [x4, #0xaa0]
    // 0xc05e00: r0 = copyWith()
    //     0xc05e00: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc05e04: stur            x0, [fp, #-0x28]
    // 0xc05e08: r0 = Text()
    //     0xc05e08: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc05e0c: mov             x3, x0
    // 0xc05e10: ldur            x0, [fp, #-8]
    // 0xc05e14: stur            x3, [fp, #-0x30]
    // 0xc05e18: StoreField: r3->field_b = r0
    //     0xc05e18: stur            w0, [x3, #0xb]
    // 0xc05e1c: ldur            x0, [fp, #-0x28]
    // 0xc05e20: StoreField: r3->field_13 = r0
    //     0xc05e20: stur            w0, [x3, #0x13]
    // 0xc05e24: ldur            x2, [fp, #-0x18]
    // 0xc05e28: r1 = Function '<anonymous closure>':.
    //     0xc05e28: add             x1, PP, #0x52, lsl #12  ; [pp+0x52770] AnonymousClosure: (0xc06004), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc05e2c: ldr             x1, [x1, #0x770]
    // 0xc05e30: r0 = AllocateClosure()
    //     0xc05e30: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc05e34: stur            x0, [fp, #-8]
    // 0xc05e38: r0 = TextButton()
    //     0xc05e38: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xc05e3c: mov             x1, x0
    // 0xc05e40: ldur            x0, [fp, #-8]
    // 0xc05e44: stur            x1, [fp, #-0x18]
    // 0xc05e48: StoreField: r1->field_b = r0
    //     0xc05e48: stur            w0, [x1, #0xb]
    // 0xc05e4c: r0 = false
    //     0xc05e4c: add             x0, NULL, #0x30  ; false
    // 0xc05e50: StoreField: r1->field_27 = r0
    //     0xc05e50: stur            w0, [x1, #0x27]
    // 0xc05e54: r0 = true
    //     0xc05e54: add             x0, NULL, #0x20  ; true
    // 0xc05e58: StoreField: r1->field_2f = r0
    //     0xc05e58: stur            w0, [x1, #0x2f]
    // 0xc05e5c: ldur            x0, [fp, #-0x30]
    // 0xc05e60: StoreField: r1->field_37 = r0
    //     0xc05e60: stur            w0, [x1, #0x37]
    // 0xc05e64: r0 = Instance_ValueKey
    //     0xc05e64: add             x0, PP, #0x36, lsl #12  ; [pp+0x366d0] Obj!ValueKey<String>@d5b331
    //     0xc05e68: ldr             x0, [x0, #0x6d0]
    // 0xc05e6c: StoreField: r1->field_7 = r0
    //     0xc05e6c: stur            w0, [x1, #7]
    // 0xc05e70: r0 = TextButtonTheme()
    //     0xc05e70: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xc05e74: mov             x1, x0
    // 0xc05e78: ldur            x0, [fp, #-0x10]
    // 0xc05e7c: stur            x1, [fp, #-8]
    // 0xc05e80: StoreField: r1->field_f = r0
    //     0xc05e80: stur            w0, [x1, #0xf]
    // 0xc05e84: ldur            x0, [fp, #-0x18]
    // 0xc05e88: StoreField: r1->field_b = r0
    //     0xc05e88: stur            w0, [x1, #0xb]
    // 0xc05e8c: r0 = SizedBox()
    //     0xc05e8c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xc05e90: mov             x1, x0
    // 0xc05e94: r0 = inf
    //     0xc05e94: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xc05e98: ldr             x0, [x0, #0x9f8]
    // 0xc05e9c: stur            x1, [fp, #-0x10]
    // 0xc05ea0: StoreField: r1->field_f = r0
    //     0xc05ea0: stur            w0, [x1, #0xf]
    // 0xc05ea4: ldur            x0, [fp, #-8]
    // 0xc05ea8: StoreField: r1->field_b = r0
    //     0xc05ea8: stur            w0, [x1, #0xb]
    // 0xc05eac: r0 = Padding()
    //     0xc05eac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc05eb0: mov             x2, x0
    // 0xc05eb4: r0 = Instance_EdgeInsets
    //     0xc05eb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xc05eb8: ldr             x0, [x0, #0xa00]
    // 0xc05ebc: stur            x2, [fp, #-8]
    // 0xc05ec0: StoreField: r2->field_f = r0
    //     0xc05ec0: stur            w0, [x2, #0xf]
    // 0xc05ec4: ldur            x0, [fp, #-0x10]
    // 0xc05ec8: StoreField: r2->field_b = r0
    //     0xc05ec8: stur            w0, [x2, #0xb]
    // 0xc05ecc: ldur            x0, [fp, #-0x20]
    // 0xc05ed0: LoadField: r1 = r0->field_b
    //     0xc05ed0: ldur            w1, [x0, #0xb]
    // 0xc05ed4: LoadField: r3 = r0->field_f
    //     0xc05ed4: ldur            w3, [x0, #0xf]
    // 0xc05ed8: DecompressPointer r3
    //     0xc05ed8: add             x3, x3, HEAP, lsl #32
    // 0xc05edc: LoadField: r4 = r3->field_b
    //     0xc05edc: ldur            w4, [x3, #0xb]
    // 0xc05ee0: r3 = LoadInt32Instr(r1)
    //     0xc05ee0: sbfx            x3, x1, #1, #0x1f
    // 0xc05ee4: stur            x3, [fp, #-0x48]
    // 0xc05ee8: r1 = LoadInt32Instr(r4)
    //     0xc05ee8: sbfx            x1, x4, #1, #0x1f
    // 0xc05eec: cmp             x3, x1
    // 0xc05ef0: b.ne            #0xc05efc
    // 0xc05ef4: mov             x1, x0
    // 0xc05ef8: r0 = _growToNextCapacity()
    //     0xc05ef8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xc05efc: ldur            x2, [fp, #-0x20]
    // 0xc05f00: ldur            x3, [fp, #-0x48]
    // 0xc05f04: add             x0, x3, #1
    // 0xc05f08: lsl             x1, x0, #1
    // 0xc05f0c: StoreField: r2->field_b = r1
    //     0xc05f0c: stur            w1, [x2, #0xb]
    // 0xc05f10: LoadField: r1 = r2->field_f
    //     0xc05f10: ldur            w1, [x2, #0xf]
    // 0xc05f14: DecompressPointer r1
    //     0xc05f14: add             x1, x1, HEAP, lsl #32
    // 0xc05f18: ldur            x0, [fp, #-8]
    // 0xc05f1c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xc05f1c: add             x25, x1, x3, lsl #2
    //     0xc05f20: add             x25, x25, #0xf
    //     0xc05f24: str             w0, [x25]
    //     0xc05f28: tbz             w0, #0, #0xc05f44
    //     0xc05f2c: ldurb           w16, [x1, #-1]
    //     0xc05f30: ldurb           w17, [x0, #-1]
    //     0xc05f34: and             x16, x17, x16, lsr #2
    //     0xc05f38: tst             x16, HEAP, lsr #32
    //     0xc05f3c: b.eq            #0xc05f44
    //     0xc05f40: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xc05f44: r0 = Column()
    //     0xc05f44: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xc05f48: mov             x1, x0
    // 0xc05f4c: r0 = Instance_Axis
    //     0xc05f4c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xc05f50: stur            x1, [fp, #-8]
    // 0xc05f54: StoreField: r1->field_f = r0
    //     0xc05f54: stur            w0, [x1, #0xf]
    // 0xc05f58: r0 = Instance_MainAxisAlignment
    //     0xc05f58: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xc05f5c: ldr             x0, [x0, #0xa08]
    // 0xc05f60: StoreField: r1->field_13 = r0
    //     0xc05f60: stur            w0, [x1, #0x13]
    // 0xc05f64: r0 = Instance_MainAxisSize
    //     0xc05f64: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xc05f68: ldr             x0, [x0, #0xdd0]
    // 0xc05f6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xc05f6c: stur            w0, [x1, #0x17]
    // 0xc05f70: r0 = Instance_CrossAxisAlignment
    //     0xc05f70: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xc05f74: ldr             x0, [x0, #0x890]
    // 0xc05f78: StoreField: r1->field_1b = r0
    //     0xc05f78: stur            w0, [x1, #0x1b]
    // 0xc05f7c: r0 = Instance_VerticalDirection
    //     0xc05f7c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xc05f80: ldr             x0, [x0, #0xa20]
    // 0xc05f84: StoreField: r1->field_23 = r0
    //     0xc05f84: stur            w0, [x1, #0x23]
    // 0xc05f88: r0 = Instance_Clip
    //     0xc05f88: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xc05f8c: ldr             x0, [x0, #0x38]
    // 0xc05f90: StoreField: r1->field_2b = r0
    //     0xc05f90: stur            w0, [x1, #0x2b]
    // 0xc05f94: StoreField: r1->field_2f = rZR
    //     0xc05f94: stur            xzr, [x1, #0x2f]
    // 0xc05f98: ldur            x0, [fp, #-0x20]
    // 0xc05f9c: StoreField: r1->field_b = r0
    //     0xc05f9c: stur            w0, [x1, #0xb]
    // 0xc05fa0: r0 = Padding()
    //     0xc05fa0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc05fa4: r1 = Instance_EdgeInsets
    //     0xc05fa4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xc05fa8: ldr             x1, [x1, #0x1f0]
    // 0xc05fac: StoreField: r0->field_f = r1
    //     0xc05fac: stur            w1, [x0, #0xf]
    // 0xc05fb0: ldur            x1, [fp, #-8]
    // 0xc05fb4: StoreField: r0->field_b = r1
    //     0xc05fb4: stur            w1, [x0, #0xb]
    // 0xc05fb8: LeaveFrame
    //     0xc05fb8: mov             SP, fp
    //     0xc05fbc: ldp             fp, lr, [SP], #0x10
    // 0xc05fc0: ret
    //     0xc05fc0: ret             
    // 0xc05fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc05fc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc05fc8: b               #0xc05198
    // 0xc05fcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc05fcc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc05fd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc05fd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc05fd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc05fd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc05fd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc05fd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc05fdc: r9 = dropDownValue
    //     0xc05fdc: add             x9, PP, #0x52, lsl #12  ; [pp+0x52778] Field <<EMAIL>>: late (offset: 0x14)
    //     0xc05fe0: ldr             x9, [x9, #0x778]
    // 0xc05fe4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc05fe4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc05fe8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc05fe8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc05fec: r9 = dropDownValue
    //     0xc05fec: add             x9, PP, #0x52, lsl #12  ; [pp+0x52778] Field <<EMAIL>>: late (offset: 0x14)
    //     0xc05ff0: ldr             x9, [x9, #0x778]
    // 0xc05ff4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc05ff4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc05ff8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc05ff8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc05ffc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc05ffc: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc06000: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06000: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc06004, size: 0x364
    // 0xc06004: EnterFrame
    //     0xc06004: stp             fp, lr, [SP, #-0x10]!
    //     0xc06008: mov             fp, SP
    // 0xc0600c: AllocStack(0x50)
    //     0xc0600c: sub             SP, SP, #0x50
    // 0xc06010: SetupParameters()
    //     0xc06010: ldr             x0, [fp, #0x10]
    //     0xc06014: ldur            w3, [x0, #0x17]
    //     0xc06018: add             x3, x3, HEAP, lsl #32
    //     0xc0601c: stur            x3, [fp, #-8]
    // 0xc06020: CheckStackOverflow
    //     0xc06020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc06024: cmp             SP, x16
    //     0xc06028: b.ls            #0xc06344
    // 0xc0602c: LoadField: r0 = r3->field_f
    //     0xc0602c: ldur            w0, [x3, #0xf]
    // 0xc06030: DecompressPointer r0
    //     0xc06030: add             x0, x0, HEAP, lsl #32
    // 0xc06034: LoadField: r1 = r0->field_b
    //     0xc06034: ldur            w1, [x0, #0xb]
    // 0xc06038: DecompressPointer r1
    //     0xc06038: add             x1, x1, HEAP, lsl #32
    // 0xc0603c: cmp             w1, NULL
    // 0xc06040: b.eq            #0xc0634c
    // 0xc06044: LoadField: r0 = r1->field_b
    //     0xc06044: ldur            w0, [x1, #0xb]
    // 0xc06048: DecompressPointer r0
    //     0xc06048: add             x0, x0, HEAP, lsl #32
    // 0xc0604c: LoadField: r1 = r0->field_d7
    //     0xc0604c: ldur            w1, [x0, #0xd7]
    // 0xc06050: DecompressPointer r1
    //     0xc06050: add             x1, x1, HEAP, lsl #32
    // 0xc06054: cmp             w1, NULL
    // 0xc06058: b.ne            #0xc06074
    // 0xc0605c: r1 = <AllSkuDatum>
    //     0xc0605c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xc06060: ldr             x1, [x1, #0xea8]
    // 0xc06064: r2 = 0
    //     0xc06064: movz            x2, #0
    // 0xc06068: r0 = _GrowableList()
    //     0xc06068: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc0606c: mov             x3, x0
    // 0xc06070: b               #0xc06078
    // 0xc06074: mov             x3, x1
    // 0xc06078: ldur            x0, [fp, #-8]
    // 0xc0607c: stur            x3, [fp, #-0x10]
    // 0xc06080: LoadField: r1 = r0->field_f
    //     0xc06080: ldur            w1, [x0, #0xf]
    // 0xc06084: DecompressPointer r1
    //     0xc06084: add             x1, x1, HEAP, lsl #32
    // 0xc06088: LoadField: r2 = r1->field_b
    //     0xc06088: ldur            w2, [x1, #0xb]
    // 0xc0608c: DecompressPointer r2
    //     0xc0608c: add             x2, x2, HEAP, lsl #32
    // 0xc06090: cmp             w2, NULL
    // 0xc06094: b.eq            #0xc06350
    // 0xc06098: LoadField: r1 = r2->field_b
    //     0xc06098: ldur            w1, [x2, #0xb]
    // 0xc0609c: DecompressPointer r1
    //     0xc0609c: add             x1, x1, HEAP, lsl #32
    // 0xc060a0: LoadField: r2 = r1->field_db
    //     0xc060a0: ldur            w2, [x1, #0xdb]
    // 0xc060a4: DecompressPointer r2
    //     0xc060a4: add             x2, x2, HEAP, lsl #32
    // 0xc060a8: cmp             w2, NULL
    // 0xc060ac: b.ne            #0xc060c8
    // 0xc060b0: r1 = <AllGroupedSkusDatum>
    //     0xc060b0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30f28] TypeArguments: <AllGroupedSkusDatum>
    //     0xc060b4: ldr             x1, [x1, #0xf28]
    // 0xc060b8: r2 = 0
    //     0xc060b8: movz            x2, #0
    // 0xc060bc: r0 = _GrowableList()
    //     0xc060bc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xc060c0: mov             x3, x0
    // 0xc060c4: b               #0xc060cc
    // 0xc060c8: mov             x3, x2
    // 0xc060cc: ldur            x2, [fp, #-0x10]
    // 0xc060d0: LoadField: r0 = r2->field_b
    //     0xc060d0: ldur            w0, [x2, #0xb]
    // 0xc060d4: r1 = LoadInt32Instr(r0)
    //     0xc060d4: sbfx            x1, x0, #1, #0x1f
    // 0xc060d8: cbz             x1, #0xc060f4
    // 0xc060dc: ldur            x0, [fp, #-8]
    // 0xc060e0: LoadField: r4 = r0->field_f
    //     0xc060e0: ldur            w4, [x0, #0xf]
    // 0xc060e4: DecompressPointer r4
    //     0xc060e4: add             x4, x4, HEAP, lsl #32
    // 0xc060e8: ArrayLoad: r5 = r4[0]  ; List_8
    //     0xc060e8: ldur            x5, [x4, #0x17]
    // 0xc060ec: cmp             x5, x1
    // 0xc060f0: b.lt            #0xc06104
    // 0xc060f4: r0 = Null
    //     0xc060f4: mov             x0, NULL
    // 0xc060f8: LeaveFrame
    //     0xc060f8: mov             SP, fp
    //     0xc060fc: ldp             fp, lr, [SP], #0x10
    // 0xc06100: ret
    //     0xc06100: ret             
    // 0xc06104: mov             x0, x1
    // 0xc06108: mov             x1, x5
    // 0xc0610c: cmp             x1, x0
    // 0xc06110: b.hs            #0xc06354
    // 0xc06114: LoadField: r0 = r2->field_f
    //     0xc06114: ldur            w0, [x2, #0xf]
    // 0xc06118: DecompressPointer r0
    //     0xc06118: add             x0, x0, HEAP, lsl #32
    // 0xc0611c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc0611c: add             x16, x0, x5, lsl #2
    //     0xc06120: ldur            w1, [x16, #0xf]
    // 0xc06124: DecompressPointer r1
    //     0xc06124: add             x1, x1, HEAP, lsl #32
    // 0xc06128: LoadField: r0 = r1->field_f
    //     0xc06128: ldur            w0, [x1, #0xf]
    // 0xc0612c: DecompressPointer r0
    //     0xc0612c: add             x0, x0, HEAP, lsl #32
    // 0xc06130: cmp             w0, NULL
    // 0xc06134: b.ne            #0xc06140
    // 0xc06138: r2 = ""
    //     0xc06138: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc0613c: b               #0xc06144
    // 0xc06140: mov             x2, x0
    // 0xc06144: LoadField: r0 = r1->field_b
    //     0xc06144: ldur            w0, [x1, #0xb]
    // 0xc06148: DecompressPointer r0
    //     0xc06148: add             x0, x0, HEAP, lsl #32
    // 0xc0614c: cmp             w0, NULL
    // 0xc06150: b.ne            #0xc0615c
    // 0xc06154: r6 = ""
    //     0xc06154: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc06158: b               #0xc06160
    // 0xc0615c: mov             x6, x0
    // 0xc06160: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc06160: ldur            w0, [x1, #0x17]
    // 0xc06164: DecompressPointer r0
    //     0xc06164: add             x0, x0, HEAP, lsl #32
    // 0xc06168: cmp             w0, NULL
    // 0xc0616c: b.ne            #0xc06178
    // 0xc06170: r7 = 0
    //     0xc06170: movz            x7, #0
    // 0xc06174: b               #0xc06188
    // 0xc06178: r1 = LoadInt32Instr(r0)
    //     0xc06178: sbfx            x1, x0, #1, #0x1f
    //     0xc0617c: tbz             w0, #0, #0xc06184
    //     0xc06180: ldur            x1, [x0, #7]
    // 0xc06184: mov             x7, x1
    // 0xc06188: LoadField: r0 = r3->field_b
    //     0xc06188: ldur            w0, [x3, #0xb]
    // 0xc0618c: r1 = LoadInt32Instr(r0)
    //     0xc0618c: sbfx            x1, x0, #1, #0x1f
    // 0xc06190: cbz             x1, #0xc062b4
    // 0xc06194: LoadField: r8 = r4->field_1f
    //     0xc06194: ldur            x8, [x4, #0x1f]
    // 0xc06198: cmp             x8, x1
    // 0xc0619c: b.ge            #0xc062b4
    // 0xc061a0: mov             x0, x1
    // 0xc061a4: mov             x1, x8
    // 0xc061a8: cmp             x1, x0
    // 0xc061ac: b.hs            #0xc06358
    // 0xc061b0: LoadField: r0 = r3->field_f
    //     0xc061b0: ldur            w0, [x3, #0xf]
    // 0xc061b4: DecompressPointer r0
    //     0xc061b4: add             x0, x0, HEAP, lsl #32
    // 0xc061b8: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xc061b8: add             x16, x0, x8, lsl #2
    //     0xc061bc: ldur            w1, [x16, #0xf]
    // 0xc061c0: DecompressPointer r1
    //     0xc061c0: add             x1, x1, HEAP, lsl #32
    // 0xc061c4: LoadField: r0 = r1->field_b
    //     0xc061c4: ldur            w0, [x1, #0xb]
    // 0xc061c8: DecompressPointer r0
    //     0xc061c8: add             x0, x0, HEAP, lsl #32
    // 0xc061cc: cmp             w0, NULL
    // 0xc061d0: b.ne            #0xc061dc
    // 0xc061d4: r3 = ""
    //     0xc061d4: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc061d8: b               #0xc061e0
    // 0xc061dc: mov             x3, x0
    // 0xc061e0: LoadField: r0 = r1->field_7
    //     0xc061e0: ldur            w0, [x1, #7]
    // 0xc061e4: DecompressPointer r0
    //     0xc061e4: add             x0, x0, HEAP, lsl #32
    // 0xc061e8: cmp             w0, NULL
    // 0xc061ec: b.ne            #0xc061f8
    // 0xc061f0: r8 = ""
    //     0xc061f0: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc061f4: b               #0xc061fc
    // 0xc061f8: mov             x8, x0
    // 0xc061fc: ArrayLoad: r9 = r1[0]  ; List_4
    //     0xc061fc: ldur            w9, [x1, #0x17]
    // 0xc06200: DecompressPointer r9
    //     0xc06200: add             x9, x9, HEAP, lsl #32
    // 0xc06204: cmp             w9, NULL
    // 0xc06208: b.ne            #0xc06214
    // 0xc0620c: r0 = Null
    //     0xc0620c: mov             x0, NULL
    // 0xc06210: b               #0xc0622c
    // 0xc06214: LoadField: r0 = r9->field_b
    //     0xc06214: ldur            w0, [x9, #0xb]
    // 0xc06218: cbnz            w0, #0xc06224
    // 0xc0621c: r1 = false
    //     0xc0621c: add             x1, NULL, #0x30  ; false
    // 0xc06220: b               #0xc06228
    // 0xc06224: r1 = true
    //     0xc06224: add             x1, NULL, #0x20  ; true
    // 0xc06228: mov             x0, x1
    // 0xc0622c: cmp             w0, NULL
    // 0xc06230: b.eq            #0xc062a0
    // 0xc06234: tbnz            w0, #4, #0xc062a0
    // 0xc06238: cmp             w9, NULL
    // 0xc0623c: b.eq            #0xc0635c
    // 0xc06240: LoadField: r0 = r9->field_b
    //     0xc06240: ldur            w0, [x9, #0xb]
    // 0xc06244: r1 = LoadInt32Instr(r0)
    //     0xc06244: sbfx            x1, x0, #1, #0x1f
    // 0xc06248: cmp             x5, x1
    // 0xc0624c: b.ge            #0xc062a0
    // 0xc06250: mov             x0, x1
    // 0xc06254: mov             x1, x5
    // 0xc06258: cmp             x1, x0
    // 0xc0625c: b.hs            #0xc06360
    // 0xc06260: LoadField: r0 = r9->field_f
    //     0xc06260: ldur            w0, [x9, #0xf]
    // 0xc06264: DecompressPointer r0
    //     0xc06264: add             x0, x0, HEAP, lsl #32
    // 0xc06268: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0xc06268: add             x16, x0, x5, lsl #2
    //     0xc0626c: ldur            w1, [x16, #0xf]
    // 0xc06270: DecompressPointer r1
    //     0xc06270: add             x1, x1, HEAP, lsl #32
    // 0xc06274: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc06274: ldur            w0, [x1, #0x17]
    // 0xc06278: DecompressPointer r0
    //     0xc06278: add             x0, x0, HEAP, lsl #32
    // 0xc0627c: cmp             w0, NULL
    // 0xc06280: b.ne            #0xc0628c
    // 0xc06284: r0 = 0
    //     0xc06284: movz            x0, #0
    // 0xc06288: b               #0xc062a4
    // 0xc0628c: r1 = LoadInt32Instr(r0)
    //     0xc0628c: sbfx            x1, x0, #1, #0x1f
    //     0xc06290: tbz             w0, #0, #0xc06298
    //     0xc06294: ldur            x1, [x0, #7]
    // 0xc06298: mov             x0, x1
    // 0xc0629c: b               #0xc062a4
    // 0xc062a0: r0 = 0
    //     0xc062a0: movz            x0, #0
    // 0xc062a4: mov             x5, x8
    // 0xc062a8: mov             x8, x3
    // 0xc062ac: mov             x3, x0
    // 0xc062b0: b               #0xc062c0
    // 0xc062b4: r8 = ""
    //     0xc062b4: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc062b8: r5 = ""
    //     0xc062b8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc062bc: r3 = 0
    //     0xc062bc: movz            x3, #0
    // 0xc062c0: LoadField: r0 = r4->field_b
    //     0xc062c0: ldur            w0, [x4, #0xb]
    // 0xc062c4: DecompressPointer r0
    //     0xc062c4: add             x0, x0, HEAP, lsl #32
    // 0xc062c8: cmp             w0, NULL
    // 0xc062cc: b.eq            #0xc06364
    // 0xc062d0: LoadField: r4 = r0->field_b
    //     0xc062d0: ldur            w4, [x0, #0xb]
    // 0xc062d4: DecompressPointer r4
    //     0xc062d4: add             x4, x4, HEAP, lsl #32
    // 0xc062d8: LoadField: r9 = r0->field_13
    //     0xc062d8: ldur            w9, [x0, #0x13]
    // 0xc062dc: DecompressPointer r9
    //     0xc062dc: add             x9, x9, HEAP, lsl #32
    // 0xc062e0: r0 = BoxInt64Instr(r7)
    //     0xc062e0: sbfiz           x0, x7, #1, #0x1f
    //     0xc062e4: cmp             x7, x0, asr #1
    //     0xc062e8: b.eq            #0xc062f4
    //     0xc062ec: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc062f0: stur            x7, [x0, #7]
    // 0xc062f4: mov             x7, x0
    // 0xc062f8: r0 = BoxInt64Instr(r3)
    //     0xc062f8: sbfiz           x0, x3, #1, #0x1f
    //     0xc062fc: cmp             x3, x0, asr #1
    //     0xc06300: b.eq            #0xc0630c
    //     0xc06304: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc06308: stur            x3, [x0, #7]
    // 0xc0630c: stp             x2, x9, [SP, #0x30]
    // 0xc06310: stp             x7, x6, [SP, #0x20]
    // 0xc06314: stp             x8, x4, [SP, #0x10]
    // 0xc06318: stp             x0, x5, [SP]
    // 0xc0631c: r4 = 0
    //     0xc0631c: movz            x4, #0
    // 0xc06320: ldr             x0, [SP, #0x38]
    // 0xc06324: r16 = UnlinkedCall_0x613b5c
    //     0xc06324: add             x16, PP, #0x52, lsl #12  ; [pp+0x52780] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xc06328: add             x16, x16, #0x780
    // 0xc0632c: ldp             x5, lr, [x16]
    // 0xc06330: blr             lr
    // 0xc06334: r0 = Null
    //     0xc06334: mov             x0, NULL
    // 0xc06338: LeaveFrame
    //     0xc06338: mov             SP, fp
    //     0xc0633c: ldp             fp, lr, [SP], #0x10
    // 0xc06340: ret
    //     0xc06340: ret             
    // 0xc06344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc06344: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc06348: b               #0xc0602c
    // 0xc0634c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0634c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc06350: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06350: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc06354: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc06354: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc06358: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc06358: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc0635c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0635c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc06360: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc06360: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc06364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06364: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AllSkuDatum?) {
    // ** addr: 0xc06368, size: 0x84
    // 0xc06368: EnterFrame
    //     0xc06368: stp             fp, lr, [SP, #-0x10]!
    //     0xc0636c: mov             fp, SP
    // 0xc06370: AllocStack(0x10)
    //     0xc06370: sub             SP, SP, #0x10
    // 0xc06374: SetupParameters()
    //     0xc06374: ldr             x0, [fp, #0x18]
    //     0xc06378: ldur            w1, [x0, #0x17]
    //     0xc0637c: add             x1, x1, HEAP, lsl #32
    //     0xc06380: stur            x1, [fp, #-8]
    // 0xc06384: CheckStackOverflow
    //     0xc06384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc06388: cmp             SP, x16
    //     0xc0638c: b.ls            #0xc063e4
    // 0xc06390: r1 = 1
    //     0xc06390: movz            x1, #0x1
    // 0xc06394: r0 = AllocateContext()
    //     0xc06394: bl              #0x16f6108  ; AllocateContextStub
    // 0xc06398: mov             x1, x0
    // 0xc0639c: ldur            x0, [fp, #-8]
    // 0xc063a0: StoreField: r1->field_b = r0
    //     0xc063a0: stur            w0, [x1, #0xb]
    // 0xc063a4: ldr             x2, [fp, #0x10]
    // 0xc063a8: StoreField: r1->field_f = r2
    //     0xc063a8: stur            w2, [x1, #0xf]
    // 0xc063ac: LoadField: r3 = r0->field_f
    //     0xc063ac: ldur            w3, [x0, #0xf]
    // 0xc063b0: DecompressPointer r3
    //     0xc063b0: add             x3, x3, HEAP, lsl #32
    // 0xc063b4: mov             x2, x1
    // 0xc063b8: stur            x3, [fp, #-0x10]
    // 0xc063bc: r1 = Function '<anonymous closure>':.
    //     0xc063bc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52808] AnonymousClosure: (0xc063ec), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc063c0: ldr             x1, [x1, #0x808]
    // 0xc063c4: r0 = AllocateClosure()
    //     0xc063c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc063c8: ldur            x1, [fp, #-0x10]
    // 0xc063cc: mov             x2, x0
    // 0xc063d0: r0 = setState()
    //     0xc063d0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc063d4: r0 = Null
    //     0xc063d4: mov             x0, NULL
    // 0xc063d8: LeaveFrame
    //     0xc063d8: mov             SP, fp
    //     0xc063dc: ldp             fp, lr, [SP], #0x10
    // 0xc063e0: ret
    //     0xc063e0: ret             
    // 0xc063e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc063e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc063e8: b               #0xc06390
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc063ec, size: 0x17c
    // 0xc063ec: EnterFrame
    //     0xc063ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc063f0: mov             fp, SP
    // 0xc063f4: AllocStack(0x18)
    //     0xc063f4: sub             SP, SP, #0x18
    // 0xc063f8: SetupParameters()
    //     0xc063f8: ldr             x0, [fp, #0x10]
    //     0xc063fc: ldur            w3, [x0, #0x17]
    //     0xc06400: add             x3, x3, HEAP, lsl #32
    //     0xc06404: stur            x3, [fp, #-0x18]
    // 0xc06408: CheckStackOverflow
    //     0xc06408: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0640c: cmp             SP, x16
    //     0xc06410: b.ls            #0xc06550
    // 0xc06414: LoadField: r4 = r3->field_b
    //     0xc06414: ldur            w4, [x3, #0xb]
    // 0xc06418: DecompressPointer r4
    //     0xc06418: add             x4, x4, HEAP, lsl #32
    // 0xc0641c: stur            x4, [fp, #-0x10]
    // 0xc06420: LoadField: r5 = r4->field_f
    //     0xc06420: ldur            w5, [x4, #0xf]
    // 0xc06424: DecompressPointer r5
    //     0xc06424: add             x5, x5, HEAP, lsl #32
    // 0xc06428: stur            x5, [fp, #-8]
    // 0xc0642c: LoadField: r0 = r5->field_b
    //     0xc0642c: ldur            w0, [x5, #0xb]
    // 0xc06430: DecompressPointer r0
    //     0xc06430: add             x0, x0, HEAP, lsl #32
    // 0xc06434: cmp             w0, NULL
    // 0xc06438: b.eq            #0xc06558
    // 0xc0643c: LoadField: r1 = r0->field_b
    //     0xc0643c: ldur            w1, [x0, #0xb]
    // 0xc06440: DecompressPointer r1
    //     0xc06440: add             x1, x1, HEAP, lsl #32
    // 0xc06444: LoadField: r2 = r1->field_db
    //     0xc06444: ldur            w2, [x1, #0xdb]
    // 0xc06448: DecompressPointer r2
    //     0xc06448: add             x2, x2, HEAP, lsl #32
    // 0xc0644c: cmp             w2, NULL
    // 0xc06450: b.ne            #0xc0645c
    // 0xc06454: r1 = Null
    //     0xc06454: mov             x1, NULL
    // 0xc06458: b               #0xc064dc
    // 0xc0645c: LoadField: r6 = r5->field_1f
    //     0xc0645c: ldur            x6, [x5, #0x1f]
    // 0xc06460: LoadField: r0 = r2->field_b
    //     0xc06460: ldur            w0, [x2, #0xb]
    // 0xc06464: r1 = LoadInt32Instr(r0)
    //     0xc06464: sbfx            x1, x0, #1, #0x1f
    // 0xc06468: mov             x0, x1
    // 0xc0646c: mov             x1, x6
    // 0xc06470: cmp             x1, x0
    // 0xc06474: b.hs            #0xc0655c
    // 0xc06478: LoadField: r0 = r2->field_f
    //     0xc06478: ldur            w0, [x2, #0xf]
    // 0xc0647c: DecompressPointer r0
    //     0xc0647c: add             x0, x0, HEAP, lsl #32
    // 0xc06480: ArrayLoad: r1 = r0[r6]  ; Unknown_4
    //     0xc06480: add             x16, x0, x6, lsl #2
    //     0xc06484: ldur            w1, [x16, #0xf]
    // 0xc06488: DecompressPointer r1
    //     0xc06488: add             x1, x1, HEAP, lsl #32
    // 0xc0648c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc0648c: ldur            w0, [x1, #0x17]
    // 0xc06490: DecompressPointer r0
    //     0xc06490: add             x0, x0, HEAP, lsl #32
    // 0xc06494: cmp             w0, NULL
    // 0xc06498: b.ne            #0xc064a4
    // 0xc0649c: r1 = Null
    //     0xc0649c: mov             x1, NULL
    // 0xc064a0: b               #0xc064dc
    // 0xc064a4: LoadField: r2 = r3->field_f
    //     0xc064a4: ldur            w2, [x3, #0xf]
    // 0xc064a8: DecompressPointer r2
    //     0xc064a8: add             x2, x2, HEAP, lsl #32
    // 0xc064ac: cmp             w2, NULL
    // 0xc064b0: b.eq            #0xc06560
    // 0xc064b4: mov             x1, x0
    // 0xc064b8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc064b8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc064bc: r0 = indexOf()
    //     0xc064bc: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xc064c0: mov             x2, x0
    // 0xc064c4: r0 = BoxInt64Instr(r2)
    //     0xc064c4: sbfiz           x0, x2, #1, #0x1f
    //     0xc064c8: cmp             x2, x0, asr #1
    //     0xc064cc: b.eq            #0xc064d8
    //     0xc064d0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc064d4: stur            x2, [x0, #7]
    // 0xc064d8: mov             x1, x0
    // 0xc064dc: cmp             w1, NULL
    // 0xc064e0: b.ne            #0xc064ec
    // 0xc064e4: r4 = 0
    //     0xc064e4: movz            x4, #0
    // 0xc064e8: b               #0xc064fc
    // 0xc064ec: r2 = LoadInt32Instr(r1)
    //     0xc064ec: sbfx            x2, x1, #1, #0x1f
    //     0xc064f0: tbz             w1, #0, #0xc064f8
    //     0xc064f4: ldur            x2, [x1, #7]
    // 0xc064f8: mov             x4, x2
    // 0xc064fc: ldur            x1, [fp, #-0x18]
    // 0xc06500: ldur            x2, [fp, #-0x10]
    // 0xc06504: ldur            x3, [fp, #-8]
    // 0xc06508: ArrayStore: r3[0] = r4  ; List_8
    //     0xc06508: stur            x4, [x3, #0x17]
    // 0xc0650c: LoadField: r3 = r2->field_f
    //     0xc0650c: ldur            w3, [x2, #0xf]
    // 0xc06510: DecompressPointer r3
    //     0xc06510: add             x3, x3, HEAP, lsl #32
    // 0xc06514: LoadField: r0 = r1->field_f
    //     0xc06514: ldur            w0, [x1, #0xf]
    // 0xc06518: DecompressPointer r0
    //     0xc06518: add             x0, x0, HEAP, lsl #32
    // 0xc0651c: cmp             w0, NULL
    // 0xc06520: b.eq            #0xc06564
    // 0xc06524: StoreField: r3->field_13 = r0
    //     0xc06524: stur            w0, [x3, #0x13]
    //     0xc06528: ldurb           w16, [x3, #-1]
    //     0xc0652c: ldurb           w17, [x0, #-1]
    //     0xc06530: and             x16, x17, x16, lsr #2
    //     0xc06534: tst             x16, HEAP, lsr #32
    //     0xc06538: b.eq            #0xc06540
    //     0xc0653c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc06540: r0 = Null
    //     0xc06540: mov             x0, NULL
    // 0xc06544: LeaveFrame
    //     0xc06544: mov             SP, fp
    //     0xc06548: ldp             fp, lr, [SP], #0x10
    // 0xc0654c: ret
    //     0xc0654c: ret             
    // 0xc06550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc06550: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc06554: b               #0xc06414
    // 0xc06558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06558: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0655c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc0655c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xc06560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06560: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc06564: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06564: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, AllSkuDatum?) {
    // ** addr: 0xc06568, size: 0x84
    // 0xc06568: EnterFrame
    //     0xc06568: stp             fp, lr, [SP, #-0x10]!
    //     0xc0656c: mov             fp, SP
    // 0xc06570: AllocStack(0x10)
    //     0xc06570: sub             SP, SP, #0x10
    // 0xc06574: SetupParameters()
    //     0xc06574: ldr             x0, [fp, #0x18]
    //     0xc06578: ldur            w1, [x0, #0x17]
    //     0xc0657c: add             x1, x1, HEAP, lsl #32
    //     0xc06580: stur            x1, [fp, #-8]
    // 0xc06584: CheckStackOverflow
    //     0xc06584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc06588: cmp             SP, x16
    //     0xc0658c: b.ls            #0xc065e4
    // 0xc06590: r1 = 1
    //     0xc06590: movz            x1, #0x1
    // 0xc06594: r0 = AllocateContext()
    //     0xc06594: bl              #0x16f6108  ; AllocateContextStub
    // 0xc06598: mov             x1, x0
    // 0xc0659c: ldur            x0, [fp, #-8]
    // 0xc065a0: StoreField: r1->field_b = r0
    //     0xc065a0: stur            w0, [x1, #0xb]
    // 0xc065a4: ldr             x2, [fp, #0x10]
    // 0xc065a8: StoreField: r1->field_f = r2
    //     0xc065a8: stur            w2, [x1, #0xf]
    // 0xc065ac: LoadField: r3 = r0->field_f
    //     0xc065ac: ldur            w3, [x0, #0xf]
    // 0xc065b0: DecompressPointer r3
    //     0xc065b0: add             x3, x3, HEAP, lsl #32
    // 0xc065b4: mov             x2, x1
    // 0xc065b8: stur            x3, [fp, #-0x10]
    // 0xc065bc: r1 = Function '<anonymous closure>':.
    //     0xc065bc: add             x1, PP, #0x52, lsl #12  ; [pp+0x52810] AnonymousClosure: (0xc065ec), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xc065c0: ldr             x1, [x1, #0x810]
    // 0xc065c4: r0 = AllocateClosure()
    //     0xc065c4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xc065c8: ldur            x1, [fp, #-0x10]
    // 0xc065cc: mov             x2, x0
    // 0xc065d0: r0 = setState()
    //     0xc065d0: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc065d4: r0 = Null
    //     0xc065d4: mov             x0, NULL
    // 0xc065d8: LeaveFrame
    //     0xc065d8: mov             SP, fp
    //     0xc065dc: ldp             fp, lr, [SP], #0x10
    // 0xc065e0: ret
    //     0xc065e0: ret             
    // 0xc065e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc065e4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc065e8: b               #0xc06590
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc065ec, size: 0x12c
    // 0xc065ec: EnterFrame
    //     0xc065ec: stp             fp, lr, [SP, #-0x10]!
    //     0xc065f0: mov             fp, SP
    // 0xc065f4: AllocStack(0x18)
    //     0xc065f4: sub             SP, SP, #0x18
    // 0xc065f8: SetupParameters()
    //     0xc065f8: ldr             x0, [fp, #0x10]
    //     0xc065fc: ldur            w3, [x0, #0x17]
    //     0xc06600: add             x3, x3, HEAP, lsl #32
    //     0xc06604: stur            x3, [fp, #-0x18]
    // 0xc06608: CheckStackOverflow
    //     0xc06608: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0660c: cmp             SP, x16
    //     0xc06610: b.ls            #0xc06704
    // 0xc06614: LoadField: r0 = r3->field_b
    //     0xc06614: ldur            w0, [x3, #0xb]
    // 0xc06618: DecompressPointer r0
    //     0xc06618: add             x0, x0, HEAP, lsl #32
    // 0xc0661c: stur            x0, [fp, #-0x10]
    // 0xc06620: LoadField: r4 = r0->field_f
    //     0xc06620: ldur            w4, [x0, #0xf]
    // 0xc06624: DecompressPointer r4
    //     0xc06624: add             x4, x4, HEAP, lsl #32
    // 0xc06628: stur            x4, [fp, #-8]
    // 0xc0662c: LoadField: r1 = r4->field_b
    //     0xc0662c: ldur            w1, [x4, #0xb]
    // 0xc06630: DecompressPointer r1
    //     0xc06630: add             x1, x1, HEAP, lsl #32
    // 0xc06634: cmp             w1, NULL
    // 0xc06638: b.eq            #0xc0670c
    // 0xc0663c: LoadField: r2 = r1->field_b
    //     0xc0663c: ldur            w2, [x1, #0xb]
    // 0xc06640: DecompressPointer r2
    //     0xc06640: add             x2, x2, HEAP, lsl #32
    // 0xc06644: LoadField: r1 = r2->field_d7
    //     0xc06644: ldur            w1, [x2, #0xd7]
    // 0xc06648: DecompressPointer r1
    //     0xc06648: add             x1, x1, HEAP, lsl #32
    // 0xc0664c: cmp             w1, NULL
    // 0xc06650: b.ne            #0xc0665c
    // 0xc06654: r1 = Null
    //     0xc06654: mov             x1, NULL
    // 0xc06658: b               #0xc06690
    // 0xc0665c: LoadField: r2 = r3->field_f
    //     0xc0665c: ldur            w2, [x3, #0xf]
    // 0xc06660: DecompressPointer r2
    //     0xc06660: add             x2, x2, HEAP, lsl #32
    // 0xc06664: cmp             w2, NULL
    // 0xc06668: b.eq            #0xc06710
    // 0xc0666c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xc0666c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xc06670: r0 = indexOf()
    //     0xc06670: bl              #0x697e90  ; [dart:collection] ListBase::indexOf
    // 0xc06674: mov             x2, x0
    // 0xc06678: r0 = BoxInt64Instr(r2)
    //     0xc06678: sbfiz           x0, x2, #1, #0x1f
    //     0xc0667c: cmp             x2, x0, asr #1
    //     0xc06680: b.eq            #0xc0668c
    //     0xc06684: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xc06688: stur            x2, [x0, #7]
    // 0xc0668c: mov             x1, x0
    // 0xc06690: cmp             w1, NULL
    // 0xc06694: b.ne            #0xc066a0
    // 0xc06698: r4 = 0
    //     0xc06698: movz            x4, #0
    // 0xc0669c: b               #0xc066b0
    // 0xc066a0: r2 = LoadInt32Instr(r1)
    //     0xc066a0: sbfx            x2, x1, #1, #0x1f
    //     0xc066a4: tbz             w1, #0, #0xc066ac
    //     0xc066a8: ldur            x2, [x1, #7]
    // 0xc066ac: mov             x4, x2
    // 0xc066b0: ldur            x1, [fp, #-0x18]
    // 0xc066b4: ldur            x2, [fp, #-0x10]
    // 0xc066b8: ldur            x3, [fp, #-8]
    // 0xc066bc: ArrayStore: r3[0] = r4  ; List_8
    //     0xc066bc: stur            x4, [x3, #0x17]
    // 0xc066c0: LoadField: r3 = r2->field_f
    //     0xc066c0: ldur            w3, [x2, #0xf]
    // 0xc066c4: DecompressPointer r3
    //     0xc066c4: add             x3, x3, HEAP, lsl #32
    // 0xc066c8: LoadField: r0 = r1->field_f
    //     0xc066c8: ldur            w0, [x1, #0xf]
    // 0xc066cc: DecompressPointer r0
    //     0xc066cc: add             x0, x0, HEAP, lsl #32
    // 0xc066d0: cmp             w0, NULL
    // 0xc066d4: b.eq            #0xc06714
    // 0xc066d8: StoreField: r3->field_13 = r0
    //     0xc066d8: stur            w0, [x3, #0x13]
    //     0xc066dc: ldurb           w16, [x3, #-1]
    //     0xc066e0: ldurb           w17, [x0, #-1]
    //     0xc066e4: and             x16, x17, x16, lsr #2
    //     0xc066e8: tst             x16, HEAP, lsr #32
    //     0xc066ec: b.eq            #0xc066f4
    //     0xc066f0: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0xc066f4: r0 = Null
    //     0xc066f4: mov             x0, NULL
    // 0xc066f8: LeaveFrame
    //     0xc066f8: mov             SP, fp
    //     0xc066fc: ldp             fp, lr, [SP], #0x10
    // 0xc06700: ret
    //     0xc06700: ret             
    // 0xc06704: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc06704: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc06708: b               #0xc06614
    // 0xc0670c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0670c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc06710: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06710: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc06714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc06714: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] DropdownMenuItem<AllSkuDatum> <anonymous closure>(dynamic, AllSkuDatum) {
    // ** addr: 0xc06718, size: 0x10c
    // 0xc06718: EnterFrame
    //     0xc06718: stp             fp, lr, [SP, #-0x10]!
    //     0xc0671c: mov             fp, SP
    // 0xc06720: AllocStack(0x28)
    //     0xc06720: sub             SP, SP, #0x28
    // 0xc06724: SetupParameters()
    //     0xc06724: ldr             x0, [fp, #0x18]
    //     0xc06728: ldur            w1, [x0, #0x17]
    //     0xc0672c: add             x1, x1, HEAP, lsl #32
    // 0xc06730: CheckStackOverflow
    //     0xc06730: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc06734: cmp             SP, x16
    //     0xc06738: b.ls            #0xc0681c
    // 0xc0673c: ldr             x0, [fp, #0x10]
    // 0xc06740: LoadField: r2 = r0->field_7
    //     0xc06740: ldur            w2, [x0, #7]
    // 0xc06744: DecompressPointer r2
    //     0xc06744: add             x2, x2, HEAP, lsl #32
    // 0xc06748: cmp             w2, NULL
    // 0xc0674c: b.ne            #0xc06754
    // 0xc06750: r2 = ""
    //     0xc06750: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xc06754: stur            x2, [fp, #-8]
    // 0xc06758: LoadField: r3 = r1->field_13
    //     0xc06758: ldur            w3, [x1, #0x13]
    // 0xc0675c: DecompressPointer r3
    //     0xc0675c: add             x3, x3, HEAP, lsl #32
    // 0xc06760: mov             x1, x3
    // 0xc06764: r0 = of()
    //     0xc06764: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xc06768: LoadField: r1 = r0->field_87
    //     0xc06768: ldur            w1, [x0, #0x87]
    // 0xc0676c: DecompressPointer r1
    //     0xc0676c: add             x1, x1, HEAP, lsl #32
    // 0xc06770: LoadField: r0 = r1->field_2b
    //     0xc06770: ldur            w0, [x1, #0x2b]
    // 0xc06774: DecompressPointer r0
    //     0xc06774: add             x0, x0, HEAP, lsl #32
    // 0xc06778: r16 = 14.000000
    //     0xc06778: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xc0677c: ldr             x16, [x16, #0x1d8]
    // 0xc06780: r30 = Instance_Color
    //     0xc06780: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xc06784: stp             lr, x16, [SP]
    // 0xc06788: mov             x1, x0
    // 0xc0678c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xc0678c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xc06790: ldr             x4, [x4, #0xaa0]
    // 0xc06794: r0 = copyWith()
    //     0xc06794: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xc06798: stur            x0, [fp, #-0x10]
    // 0xc0679c: r0 = Text()
    //     0xc0679c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xc067a0: mov             x1, x0
    // 0xc067a4: ldur            x0, [fp, #-8]
    // 0xc067a8: stur            x1, [fp, #-0x18]
    // 0xc067ac: StoreField: r1->field_b = r0
    //     0xc067ac: stur            w0, [x1, #0xb]
    // 0xc067b0: ldur            x0, [fp, #-0x10]
    // 0xc067b4: StoreField: r1->field_13 = r0
    //     0xc067b4: stur            w0, [x1, #0x13]
    // 0xc067b8: r0 = Instance_TextAlign
    //     0xc067b8: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xc067bc: StoreField: r1->field_1b = r0
    //     0xc067bc: stur            w0, [x1, #0x1b]
    // 0xc067c0: r0 = Center()
    //     0xc067c0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xc067c4: mov             x2, x0
    // 0xc067c8: r0 = Instance_Alignment
    //     0xc067c8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xc067cc: ldr             x0, [x0, #0xb10]
    // 0xc067d0: stur            x2, [fp, #-8]
    // 0xc067d4: StoreField: r2->field_f = r0
    //     0xc067d4: stur            w0, [x2, #0xf]
    // 0xc067d8: ldur            x0, [fp, #-0x18]
    // 0xc067dc: StoreField: r2->field_b = r0
    //     0xc067dc: stur            w0, [x2, #0xb]
    // 0xc067e0: r1 = <AllSkuDatum>
    //     0xc067e0: add             x1, PP, #0x23, lsl #12  ; [pp+0x23ea8] TypeArguments: <AllSkuDatum>
    //     0xc067e4: ldr             x1, [x1, #0xea8]
    // 0xc067e8: r0 = DropdownMenuItem()
    //     0xc067e8: bl              #0x9ba75c  ; AllocateDropdownMenuItemStub -> DropdownMenuItem<X0> (size=0x24)
    // 0xc067ec: ldr             x1, [fp, #0x10]
    // 0xc067f0: StoreField: r0->field_1b = r1
    //     0xc067f0: stur            w1, [x0, #0x1b]
    // 0xc067f4: r1 = true
    //     0xc067f4: add             x1, NULL, #0x20  ; true
    // 0xc067f8: StoreField: r0->field_1f = r1
    //     0xc067f8: stur            w1, [x0, #0x1f]
    // 0xc067fc: r1 = Instance_AlignmentDirectional
    //     0xc067fc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fb70] Obj!AlignmentDirectional@d5a601
    //     0xc06800: ldr             x1, [x1, #0xb70]
    // 0xc06804: StoreField: r0->field_f = r1
    //     0xc06804: stur            w1, [x0, #0xf]
    // 0xc06808: ldur            x1, [fp, #-8]
    // 0xc0680c: StoreField: r0->field_b = r1
    //     0xc0680c: stur            w1, [x0, #0xb]
    // 0xc06810: LeaveFrame
    //     0xc06810: mov             SP, fp
    //     0xc06814: ldp             fp, lr, [SP], #0x10
    // 0xc06818: ret
    //     0xc06818: ret             
    // 0xc0681c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0681c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc06820: b               #0xc0673c
  }
}

// class id: 3970, size: 0x18, field offset: 0xc
//   const constructor, 
class ProductSelectSizeBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc81040, size: 0x48
    // 0xc81040: EnterFrame
    //     0xc81040: stp             fp, lr, [SP, #-0x10]!
    //     0xc81044: mov             fp, SP
    // 0xc81048: AllocStack(0x8)
    //     0xc81048: sub             SP, SP, #8
    // 0xc8104c: CheckStackOverflow
    //     0xc8104c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc81050: cmp             SP, x16
    //     0xc81054: b.ls            #0xc81080
    // 0xc81058: r1 = <ProductSelectSizeBottomSheet>
    //     0xc81058: add             x1, PP, #0x48, lsl #12  ; [pp+0x482a0] TypeArguments: <ProductSelectSizeBottomSheet>
    //     0xc8105c: ldr             x1, [x1, #0x2a0]
    // 0xc81060: r0 = _ProductSelectSizeBottomSheetState()
    //     0xc81060: bl              #0xc81088  ; Allocate_ProductSelectSizeBottomSheetStateStub -> _ProductSelectSizeBottomSheetState (size=0x2c)
    // 0xc81064: mov             x1, x0
    // 0xc81068: stur            x0, [fp, #-8]
    // 0xc8106c: r0 = _ProductSelectSizeBottomSheetState()
    //     0xc8106c: bl              #0xc7c3b4  ; [package:customer_app/app/presentation/views/basic/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::_ProductSelectSizeBottomSheetState
    // 0xc81070: ldur            x0, [fp, #-8]
    // 0xc81074: LeaveFrame
    //     0xc81074: mov             SP, fp
    //     0xc81078: ldp             fp, lr, [SP], #0x10
    // 0xc8107c: ret
    //     0xc8107c: ret             
    // 0xc81080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc81080: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc81084: b               #0xc81058
  }
}
