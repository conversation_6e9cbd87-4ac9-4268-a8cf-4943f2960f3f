// lib: , url: package:customer_app/app/presentation/views/line/orders/cancel_return_order_with_free_product_bottom_sheet.dart

// class id: 1049534, size: 0x8
class :: {
}

// class id: 3238, size: 0x14, field offset: 0x14
class _CancelReturnOrderWithFreeProductBottomSheetState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xbf4e40, size: 0x2068
    // 0xbf4e40: EnterFrame
    //     0xbf4e40: stp             fp, lr, [SP, #-0x10]!
    //     0xbf4e44: mov             fp, SP
    // 0xbf4e48: AllocStack(0xa0)
    //     0xbf4e48: sub             SP, SP, #0xa0
    // 0xbf4e4c: SetupParameters(_CancelReturnOrderWithFreeProductBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbf4e4c: mov             x0, x1
    //     0xbf4e50: stur            x1, [fp, #-8]
    //     0xbf4e54: mov             x1, x2
    //     0xbf4e58: stur            x2, [fp, #-0x10]
    // 0xbf4e5c: CheckStackOverflow
    //     0xbf4e5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf4e60: cmp             SP, x16
    //     0xbf4e64: b.ls            #0xbf6dc8
    // 0xbf4e68: r1 = 1
    //     0xbf4e68: movz            x1, #0x1
    // 0xbf4e6c: r0 = AllocateContext()
    //     0xbf4e6c: bl              #0x16f6108  ; AllocateContextStub
    // 0xbf4e70: mov             x2, x0
    // 0xbf4e74: ldur            x0, [fp, #-8]
    // 0xbf4e78: stur            x2, [fp, #-0x20]
    // 0xbf4e7c: StoreField: r2->field_f = r0
    //     0xbf4e7c: stur            w0, [x2, #0xf]
    // 0xbf4e80: LoadField: r1 = r0->field_b
    //     0xbf4e80: ldur            w1, [x0, #0xb]
    // 0xbf4e84: DecompressPointer r1
    //     0xbf4e84: add             x1, x1, HEAP, lsl #32
    // 0xbf4e88: cmp             w1, NULL
    // 0xbf4e8c: b.eq            #0xbf6dd0
    // 0xbf4e90: LoadField: r3 = r1->field_13
    //     0xbf4e90: ldur            w3, [x1, #0x13]
    // 0xbf4e94: DecompressPointer r3
    //     0xbf4e94: add             x3, x3, HEAP, lsl #32
    // 0xbf4e98: ldur            x1, [fp, #-0x10]
    // 0xbf4e9c: stur            x3, [fp, #-0x18]
    // 0xbf4ea0: r0 = of()
    //     0xbf4ea0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf4ea4: LoadField: r1 = r0->field_87
    //     0xbf4ea4: ldur            w1, [x0, #0x87]
    // 0xbf4ea8: DecompressPointer r1
    //     0xbf4ea8: add             x1, x1, HEAP, lsl #32
    // 0xbf4eac: LoadField: r0 = r1->field_7
    //     0xbf4eac: ldur            w0, [x1, #7]
    // 0xbf4eb0: DecompressPointer r0
    //     0xbf4eb0: add             x0, x0, HEAP, lsl #32
    // 0xbf4eb4: stur            x0, [fp, #-0x28]
    // 0xbf4eb8: r1 = Instance_Color
    //     0xbf4eb8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf4ebc: d0 = 0.700000
    //     0xbf4ebc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf4ec0: ldr             d0, [x17, #0xf48]
    // 0xbf4ec4: r0 = withOpacity()
    //     0xbf4ec4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf4ec8: r16 = 14.000000
    //     0xbf4ec8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbf4ecc: ldr             x16, [x16, #0x1d8]
    // 0xbf4ed0: stp             x16, x0, [SP]
    // 0xbf4ed4: ldur            x1, [fp, #-0x28]
    // 0xbf4ed8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbf4ed8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbf4edc: ldr             x4, [x4, #0x9b8]
    // 0xbf4ee0: r0 = copyWith()
    //     0xbf4ee0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf4ee4: stur            x0, [fp, #-0x28]
    // 0xbf4ee8: r0 = Text()
    //     0xbf4ee8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf4eec: mov             x2, x0
    // 0xbf4ef0: ldur            x0, [fp, #-0x18]
    // 0xbf4ef4: stur            x2, [fp, #-0x30]
    // 0xbf4ef8: StoreField: r2->field_b = r0
    //     0xbf4ef8: stur            w0, [x2, #0xb]
    // 0xbf4efc: ldur            x0, [fp, #-0x28]
    // 0xbf4f00: StoreField: r2->field_13 = r0
    //     0xbf4f00: stur            w0, [x2, #0x13]
    // 0xbf4f04: ldur            x0, [fp, #-8]
    // 0xbf4f08: LoadField: r1 = r0->field_b
    //     0xbf4f08: ldur            w1, [x0, #0xb]
    // 0xbf4f0c: DecompressPointer r1
    //     0xbf4f0c: add             x1, x1, HEAP, lsl #32
    // 0xbf4f10: cmp             w1, NULL
    // 0xbf4f14: b.eq            #0xbf6dd4
    // 0xbf4f18: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbf4f18: ldur            w3, [x1, #0x17]
    // 0xbf4f1c: DecompressPointer r3
    //     0xbf4f1c: add             x3, x3, HEAP, lsl #32
    // 0xbf4f20: ldur            x1, [fp, #-0x10]
    // 0xbf4f24: stur            x3, [fp, #-0x18]
    // 0xbf4f28: r0 = of()
    //     0xbf4f28: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf4f2c: LoadField: r1 = r0->field_87
    //     0xbf4f2c: ldur            w1, [x0, #0x87]
    // 0xbf4f30: DecompressPointer r1
    //     0xbf4f30: add             x1, x1, HEAP, lsl #32
    // 0xbf4f34: LoadField: r0 = r1->field_2b
    //     0xbf4f34: ldur            w0, [x1, #0x2b]
    // 0xbf4f38: DecompressPointer r0
    //     0xbf4f38: add             x0, x0, HEAP, lsl #32
    // 0xbf4f3c: stur            x0, [fp, #-0x28]
    // 0xbf4f40: r1 = Instance_Color
    //     0xbf4f40: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf4f44: d0 = 0.700000
    //     0xbf4f44: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbf4f48: ldr             d0, [x17, #0xf48]
    // 0xbf4f4c: r0 = withOpacity()
    //     0xbf4f4c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf4f50: r16 = 12.000000
    //     0xbf4f50: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf4f54: ldr             x16, [x16, #0x9e8]
    // 0xbf4f58: stp             x16, x0, [SP]
    // 0xbf4f5c: ldur            x1, [fp, #-0x28]
    // 0xbf4f60: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbf4f60: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbf4f64: ldr             x4, [x4, #0x9b8]
    // 0xbf4f68: r0 = copyWith()
    //     0xbf4f68: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf4f6c: stur            x0, [fp, #-0x28]
    // 0xbf4f70: r0 = Text()
    //     0xbf4f70: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf4f74: mov             x1, x0
    // 0xbf4f78: ldur            x0, [fp, #-0x18]
    // 0xbf4f7c: stur            x1, [fp, #-0x38]
    // 0xbf4f80: StoreField: r1->field_b = r0
    //     0xbf4f80: stur            w0, [x1, #0xb]
    // 0xbf4f84: ldur            x0, [fp, #-0x28]
    // 0xbf4f88: StoreField: r1->field_13 = r0
    //     0xbf4f88: stur            w0, [x1, #0x13]
    // 0xbf4f8c: r0 = Padding()
    //     0xbf4f8c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf4f90: mov             x2, x0
    // 0xbf4f94: r0 = Instance_EdgeInsets
    //     0xbf4f94: add             x0, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbf4f98: ldr             x0, [x0, #0x668]
    // 0xbf4f9c: stur            x2, [fp, #-0x18]
    // 0xbf4fa0: StoreField: r2->field_f = r0
    //     0xbf4fa0: stur            w0, [x2, #0xf]
    // 0xbf4fa4: ldur            x0, [fp, #-0x38]
    // 0xbf4fa8: StoreField: r2->field_b = r0
    //     0xbf4fa8: stur            w0, [x2, #0xb]
    // 0xbf4fac: r1 = Instance_Color
    //     0xbf4fac: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf4fb0: d0 = 0.070000
    //     0xbf4fb0: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbf4fb4: ldr             d0, [x17, #0x5f8]
    // 0xbf4fb8: r0 = withOpacity()
    //     0xbf4fb8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbf4fbc: r16 = 1.000000
    //     0xbf4fbc: ldr             x16, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0xbf4fc0: str             x16, [SP]
    // 0xbf4fc4: mov             x2, x0
    // 0xbf4fc8: r1 = Null
    //     0xbf4fc8: mov             x1, NULL
    // 0xbf4fcc: r4 = const [0, 0x3, 0x1, 0x2, width, 0x2, null]
    //     0xbf4fcc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32108] List(7) [0, 0x3, 0x1, 0x2, "width", 0x2, Null]
    //     0xbf4fd0: ldr             x4, [x4, #0x108]
    // 0xbf4fd4: r0 = Border.all()
    //     0xbf4fd4: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbf4fd8: stur            x0, [fp, #-0x28]
    // 0xbf4fdc: r0 = BoxDecoration()
    //     0xbf4fdc: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf4fe0: mov             x2, x0
    // 0xbf4fe4: ldur            x0, [fp, #-0x28]
    // 0xbf4fe8: stur            x2, [fp, #-0x38]
    // 0xbf4fec: StoreField: r2->field_f = r0
    //     0xbf4fec: stur            w0, [x2, #0xf]
    // 0xbf4ff0: r0 = Instance_LinearGradient
    //     0xbf4ff0: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c660] Obj!LinearGradient@d56931
    //     0xbf4ff4: ldr             x0, [x0, #0x660]
    // 0xbf4ff8: StoreField: r2->field_1b = r0
    //     0xbf4ff8: stur            w0, [x2, #0x1b]
    // 0xbf4ffc: r0 = Instance_BoxShape
    //     0xbf4ffc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf5000: ldr             x0, [x0, #0x80]
    // 0xbf5004: StoreField: r2->field_23 = r0
    //     0xbf5004: stur            w0, [x2, #0x23]
    // 0xbf5008: ldur            x1, [fp, #-0x10]
    // 0xbf500c: r0 = of()
    //     0xbf500c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf5010: LoadField: r1 = r0->field_87
    //     0xbf5010: ldur            w1, [x0, #0x87]
    // 0xbf5014: DecompressPointer r1
    //     0xbf5014: add             x1, x1, HEAP, lsl #32
    // 0xbf5018: LoadField: r0 = r1->field_7
    //     0xbf5018: ldur            w0, [x1, #7]
    // 0xbf501c: DecompressPointer r0
    //     0xbf501c: add             x0, x0, HEAP, lsl #32
    // 0xbf5020: r16 = 12.000000
    //     0xbf5020: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf5024: ldr             x16, [x16, #0x9e8]
    // 0xbf5028: r30 = Instance_Color
    //     0xbf5028: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbf502c: stp             lr, x16, [SP]
    // 0xbf5030: mov             x1, x0
    // 0xbf5034: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf5034: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf5038: ldr             x4, [x4, #0xaa0]
    // 0xbf503c: r0 = copyWith()
    //     0xbf503c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf5040: stur            x0, [fp, #-0x28]
    // 0xbf5044: r0 = Text()
    //     0xbf5044: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf5048: mov             x1, x0
    // 0xbf504c: r0 = "Free"
    //     0xbf504c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbf5050: ldr             x0, [x0, #0x668]
    // 0xbf5054: stur            x1, [fp, #-0x40]
    // 0xbf5058: StoreField: r1->field_b = r0
    //     0xbf5058: stur            w0, [x1, #0xb]
    // 0xbf505c: ldur            x2, [fp, #-0x28]
    // 0xbf5060: StoreField: r1->field_13 = r2
    //     0xbf5060: stur            w2, [x1, #0x13]
    // 0xbf5064: r0 = Center()
    //     0xbf5064: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbf5068: mov             x1, x0
    // 0xbf506c: r0 = Instance_Alignment
    //     0xbf506c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbf5070: ldr             x0, [x0, #0xb10]
    // 0xbf5074: stur            x1, [fp, #-0x28]
    // 0xbf5078: StoreField: r1->field_f = r0
    //     0xbf5078: stur            w0, [x1, #0xf]
    // 0xbf507c: ldur            x0, [fp, #-0x40]
    // 0xbf5080: StoreField: r1->field_b = r0
    //     0xbf5080: stur            w0, [x1, #0xb]
    // 0xbf5084: r0 = RotatedBox()
    //     0xbf5084: bl              #0x9fbd14  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xbf5088: mov             x1, x0
    // 0xbf508c: r0 = -1
    //     0xbf508c: movn            x0, #0
    // 0xbf5090: stur            x1, [fp, #-0x40]
    // 0xbf5094: StoreField: r1->field_f = r0
    //     0xbf5094: stur            x0, [x1, #0xf]
    // 0xbf5098: ldur            x0, [fp, #-0x28]
    // 0xbf509c: StoreField: r1->field_b = r0
    //     0xbf509c: stur            w0, [x1, #0xb]
    // 0xbf50a0: r0 = Container()
    //     0xbf50a0: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf50a4: stur            x0, [fp, #-0x28]
    // 0xbf50a8: r16 = 24.000000
    //     0xbf50a8: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0xbf50ac: ldr             x16, [x16, #0xba8]
    // 0xbf50b0: r30 = 56.000000
    //     0xbf50b0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbf50b4: ldr             lr, [lr, #0xb78]
    // 0xbf50b8: stp             lr, x16, [SP, #0x10]
    // 0xbf50bc: r16 = Instance_Color
    //     0xbf50bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbf50c0: ldr             x16, [x16, #0x858]
    // 0xbf50c4: ldur            lr, [fp, #-0x40]
    // 0xbf50c8: stp             lr, x16, [SP]
    // 0xbf50cc: mov             x1, x0
    // 0xbf50d0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x2, width, 0x1, null]
    //     0xbf50d0: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c670] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xbf50d4: ldr             x4, [x4, #0x670]
    // 0xbf50d8: r0 = Container()
    //     0xbf50d8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf50dc: r0 = ImageHeaders.forImages()
    //     0xbf50dc: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbf50e0: mov             x1, x0
    // 0xbf50e4: ldur            x0, [fp, #-8]
    // 0xbf50e8: stur            x1, [fp, #-0x40]
    // 0xbf50ec: LoadField: r2 = r0->field_b
    //     0xbf50ec: ldur            w2, [x0, #0xb]
    // 0xbf50f0: DecompressPointer r2
    //     0xbf50f0: add             x2, x2, HEAP, lsl #32
    // 0xbf50f4: cmp             w2, NULL
    // 0xbf50f8: b.eq            #0xbf6dd8
    // 0xbf50fc: LoadField: r3 = r2->field_f
    //     0xbf50fc: ldur            w3, [x2, #0xf]
    // 0xbf5100: DecompressPointer r3
    //     0xbf5100: add             x3, x3, HEAP, lsl #32
    // 0xbf5104: r16 = "return_order_intermediate"
    //     0xbf5104: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xbf5108: ldr             x16, [x16, #0xb00]
    // 0xbf510c: stp             x16, x3, [SP]
    // 0xbf5110: r0 = ==()
    //     0xbf5110: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5114: tbnz            w0, #4, #0xbf517c
    // 0xbf5118: ldur            x0, [fp, #-8]
    // 0xbf511c: LoadField: r1 = r0->field_b
    //     0xbf511c: ldur            w1, [x0, #0xb]
    // 0xbf5120: DecompressPointer r1
    //     0xbf5120: add             x1, x1, HEAP, lsl #32
    // 0xbf5124: cmp             w1, NULL
    // 0xbf5128: b.eq            #0xbf6ddc
    // 0xbf512c: LoadField: r2 = r1->field_23
    //     0xbf512c: ldur            w2, [x1, #0x23]
    // 0xbf5130: DecompressPointer r2
    //     0xbf5130: add             x2, x2, HEAP, lsl #32
    // 0xbf5134: cmp             w2, NULL
    // 0xbf5138: b.ne            #0xbf5144
    // 0xbf513c: r1 = Null
    //     0xbf513c: mov             x1, NULL
    // 0xbf5140: b               #0xbf5168
    // 0xbf5144: LoadField: r1 = r2->field_3f
    //     0xbf5144: ldur            w1, [x2, #0x3f]
    // 0xbf5148: DecompressPointer r1
    //     0xbf5148: add             x1, x1, HEAP, lsl #32
    // 0xbf514c: cmp             w1, NULL
    // 0xbf5150: b.ne            #0xbf515c
    // 0xbf5154: r1 = Null
    //     0xbf5154: mov             x1, NULL
    // 0xbf5158: b               #0xbf5168
    // 0xbf515c: LoadField: r2 = r1->field_7
    //     0xbf515c: ldur            w2, [x1, #7]
    // 0xbf5160: DecompressPointer r2
    //     0xbf5160: add             x2, x2, HEAP, lsl #32
    // 0xbf5164: mov             x1, x2
    // 0xbf5168: cmp             w1, NULL
    // 0xbf516c: b.ne            #0xbf5174
    // 0xbf5170: r1 = ""
    //     0xbf5170: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5174: mov             x2, x1
    // 0xbf5178: b               #0xbf526c
    // 0xbf517c: ldur            x0, [fp, #-8]
    // 0xbf5180: LoadField: r1 = r0->field_b
    //     0xbf5180: ldur            w1, [x0, #0xb]
    // 0xbf5184: DecompressPointer r1
    //     0xbf5184: add             x1, x1, HEAP, lsl #32
    // 0xbf5188: cmp             w1, NULL
    // 0xbf518c: b.eq            #0xbf6de0
    // 0xbf5190: LoadField: r2 = r1->field_f
    //     0xbf5190: ldur            w2, [x1, #0xf]
    // 0xbf5194: DecompressPointer r2
    //     0xbf5194: add             x2, x2, HEAP, lsl #32
    // 0xbf5198: r16 = "cancel_order"
    //     0xbf5198: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf519c: ldr             x16, [x16, #0x98]
    // 0xbf51a0: stp             x16, x2, [SP]
    // 0xbf51a4: r0 = ==()
    //     0xbf51a4: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf51a8: tbnz            w0, #4, #0xbf520c
    // 0xbf51ac: ldur            x0, [fp, #-8]
    // 0xbf51b0: LoadField: r1 = r0->field_b
    //     0xbf51b0: ldur            w1, [x0, #0xb]
    // 0xbf51b4: DecompressPointer r1
    //     0xbf51b4: add             x1, x1, HEAP, lsl #32
    // 0xbf51b8: cmp             w1, NULL
    // 0xbf51bc: b.eq            #0xbf6de4
    // 0xbf51c0: LoadField: r2 = r1->field_1b
    //     0xbf51c0: ldur            w2, [x1, #0x1b]
    // 0xbf51c4: DecompressPointer r2
    //     0xbf51c4: add             x2, x2, HEAP, lsl #32
    // 0xbf51c8: cmp             w2, NULL
    // 0xbf51cc: b.ne            #0xbf51d8
    // 0xbf51d0: r1 = Null
    //     0xbf51d0: mov             x1, NULL
    // 0xbf51d4: b               #0xbf51fc
    // 0xbf51d8: LoadField: r1 = r2->field_7f
    //     0xbf51d8: ldur            w1, [x2, #0x7f]
    // 0xbf51dc: DecompressPointer r1
    //     0xbf51dc: add             x1, x1, HEAP, lsl #32
    // 0xbf51e0: cmp             w1, NULL
    // 0xbf51e4: b.ne            #0xbf51f0
    // 0xbf51e8: r1 = Null
    //     0xbf51e8: mov             x1, NULL
    // 0xbf51ec: b               #0xbf51fc
    // 0xbf51f0: LoadField: r2 = r1->field_7
    //     0xbf51f0: ldur            w2, [x1, #7]
    // 0xbf51f4: DecompressPointer r2
    //     0xbf51f4: add             x2, x2, HEAP, lsl #32
    // 0xbf51f8: mov             x1, x2
    // 0xbf51fc: cmp             w1, NULL
    // 0xbf5200: b.ne            #0xbf5268
    // 0xbf5204: r1 = ""
    //     0xbf5204: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5208: b               #0xbf5268
    // 0xbf520c: ldur            x0, [fp, #-8]
    // 0xbf5210: LoadField: r1 = r0->field_b
    //     0xbf5210: ldur            w1, [x0, #0xb]
    // 0xbf5214: DecompressPointer r1
    //     0xbf5214: add             x1, x1, HEAP, lsl #32
    // 0xbf5218: cmp             w1, NULL
    // 0xbf521c: b.eq            #0xbf6de8
    // 0xbf5220: LoadField: r2 = r1->field_1f
    //     0xbf5220: ldur            w2, [x1, #0x1f]
    // 0xbf5224: DecompressPointer r2
    //     0xbf5224: add             x2, x2, HEAP, lsl #32
    // 0xbf5228: cmp             w2, NULL
    // 0xbf522c: b.ne            #0xbf5238
    // 0xbf5230: r1 = Null
    //     0xbf5230: mov             x1, NULL
    // 0xbf5234: b               #0xbf525c
    // 0xbf5238: LoadField: r1 = r2->field_d7
    //     0xbf5238: ldur            w1, [x2, #0xd7]
    // 0xbf523c: DecompressPointer r1
    //     0xbf523c: add             x1, x1, HEAP, lsl #32
    // 0xbf5240: cmp             w1, NULL
    // 0xbf5244: b.ne            #0xbf5250
    // 0xbf5248: r1 = Null
    //     0xbf5248: mov             x1, NULL
    // 0xbf524c: b               #0xbf525c
    // 0xbf5250: LoadField: r2 = r1->field_7
    //     0xbf5250: ldur            w2, [x1, #7]
    // 0xbf5254: DecompressPointer r2
    //     0xbf5254: add             x2, x2, HEAP, lsl #32
    // 0xbf5258: mov             x1, x2
    // 0xbf525c: cmp             w1, NULL
    // 0xbf5260: b.ne            #0xbf5268
    // 0xbf5264: r1 = ""
    //     0xbf5264: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5268: mov             x2, x1
    // 0xbf526c: stur            x2, [fp, #-0x48]
    // 0xbf5270: r0 = CachedNetworkImage()
    //     0xbf5270: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbf5274: stur            x0, [fp, #-0x50]
    // 0xbf5278: ldur            x16, [fp, #-0x40]
    // 0xbf527c: r30 = 56.000000
    //     0xbf527c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbf5280: ldr             lr, [lr, #0xb78]
    // 0xbf5284: stp             lr, x16, [SP, #0x10]
    // 0xbf5288: r16 = 56.000000
    //     0xbf5288: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbf528c: ldr             x16, [x16, #0xb78]
    // 0xbf5290: r30 = Instance_BoxFit
    //     0xbf5290: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbf5294: ldr             lr, [lr, #0x118]
    // 0xbf5298: stp             lr, x16, [SP]
    // 0xbf529c: mov             x1, x0
    // 0xbf52a0: ldur            x2, [fp, #-0x48]
    // 0xbf52a4: r4 = const [0, 0x6, 0x4, 0x2, fit, 0x5, height, 0x4, httpHeaders, 0x2, width, 0x3, null]
    //     0xbf52a4: add             x4, PP, #0x53, lsl #12  ; [pp+0x53418] List(13) [0, 0x6, 0x4, 0x2, "fit", 0x5, "height", 0x4, "httpHeaders", 0x2, "width", 0x3, Null]
    //     0xbf52a8: ldr             x4, [x4, #0x418]
    // 0xbf52ac: r0 = CachedNetworkImage()
    //     0xbf52ac: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbf52b0: ldur            x0, [fp, #-8]
    // 0xbf52b4: LoadField: r1 = r0->field_b
    //     0xbf52b4: ldur            w1, [x0, #0xb]
    // 0xbf52b8: DecompressPointer r1
    //     0xbf52b8: add             x1, x1, HEAP, lsl #32
    // 0xbf52bc: cmp             w1, NULL
    // 0xbf52c0: b.eq            #0xbf6dec
    // 0xbf52c4: LoadField: r2 = r1->field_f
    //     0xbf52c4: ldur            w2, [x1, #0xf]
    // 0xbf52c8: DecompressPointer r2
    //     0xbf52c8: add             x2, x2, HEAP, lsl #32
    // 0xbf52cc: r16 = "return_order_intermediate"
    //     0xbf52cc: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xbf52d0: ldr             x16, [x16, #0xb00]
    // 0xbf52d4: stp             x16, x2, [SP]
    // 0xbf52d8: r0 = ==()
    //     0xbf52d8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf52dc: tbnz            w0, #4, #0xbf5344
    // 0xbf52e0: ldur            x0, [fp, #-8]
    // 0xbf52e4: LoadField: r1 = r0->field_b
    //     0xbf52e4: ldur            w1, [x0, #0xb]
    // 0xbf52e8: DecompressPointer r1
    //     0xbf52e8: add             x1, x1, HEAP, lsl #32
    // 0xbf52ec: cmp             w1, NULL
    // 0xbf52f0: b.eq            #0xbf6df0
    // 0xbf52f4: LoadField: r2 = r1->field_23
    //     0xbf52f4: ldur            w2, [x1, #0x23]
    // 0xbf52f8: DecompressPointer r2
    //     0xbf52f8: add             x2, x2, HEAP, lsl #32
    // 0xbf52fc: cmp             w2, NULL
    // 0xbf5300: b.ne            #0xbf530c
    // 0xbf5304: r1 = Null
    //     0xbf5304: mov             x1, NULL
    // 0xbf5308: b               #0xbf5330
    // 0xbf530c: LoadField: r1 = r2->field_3f
    //     0xbf530c: ldur            w1, [x2, #0x3f]
    // 0xbf5310: DecompressPointer r1
    //     0xbf5310: add             x1, x1, HEAP, lsl #32
    // 0xbf5314: cmp             w1, NULL
    // 0xbf5318: b.ne            #0xbf5324
    // 0xbf531c: r1 = Null
    //     0xbf531c: mov             x1, NULL
    // 0xbf5320: b               #0xbf5330
    // 0xbf5324: LoadField: r2 = r1->field_b
    //     0xbf5324: ldur            w2, [x1, #0xb]
    // 0xbf5328: DecompressPointer r2
    //     0xbf5328: add             x2, x2, HEAP, lsl #32
    // 0xbf532c: mov             x1, x2
    // 0xbf5330: cmp             w1, NULL
    // 0xbf5334: b.ne            #0xbf533c
    // 0xbf5338: r1 = ""
    //     0xbf5338: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf533c: mov             x2, x1
    // 0xbf5340: b               #0xbf5434
    // 0xbf5344: ldur            x0, [fp, #-8]
    // 0xbf5348: LoadField: r1 = r0->field_b
    //     0xbf5348: ldur            w1, [x0, #0xb]
    // 0xbf534c: DecompressPointer r1
    //     0xbf534c: add             x1, x1, HEAP, lsl #32
    // 0xbf5350: cmp             w1, NULL
    // 0xbf5354: b.eq            #0xbf6df4
    // 0xbf5358: LoadField: r2 = r1->field_f
    //     0xbf5358: ldur            w2, [x1, #0xf]
    // 0xbf535c: DecompressPointer r2
    //     0xbf535c: add             x2, x2, HEAP, lsl #32
    // 0xbf5360: r16 = "cancel_order"
    //     0xbf5360: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf5364: ldr             x16, [x16, #0x98]
    // 0xbf5368: stp             x16, x2, [SP]
    // 0xbf536c: r0 = ==()
    //     0xbf536c: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5370: tbnz            w0, #4, #0xbf53d4
    // 0xbf5374: ldur            x0, [fp, #-8]
    // 0xbf5378: LoadField: r1 = r0->field_b
    //     0xbf5378: ldur            w1, [x0, #0xb]
    // 0xbf537c: DecompressPointer r1
    //     0xbf537c: add             x1, x1, HEAP, lsl #32
    // 0xbf5380: cmp             w1, NULL
    // 0xbf5384: b.eq            #0xbf6df8
    // 0xbf5388: LoadField: r2 = r1->field_1b
    //     0xbf5388: ldur            w2, [x1, #0x1b]
    // 0xbf538c: DecompressPointer r2
    //     0xbf538c: add             x2, x2, HEAP, lsl #32
    // 0xbf5390: cmp             w2, NULL
    // 0xbf5394: b.ne            #0xbf53a0
    // 0xbf5398: r1 = Null
    //     0xbf5398: mov             x1, NULL
    // 0xbf539c: b               #0xbf53c4
    // 0xbf53a0: LoadField: r1 = r2->field_7f
    //     0xbf53a0: ldur            w1, [x2, #0x7f]
    // 0xbf53a4: DecompressPointer r1
    //     0xbf53a4: add             x1, x1, HEAP, lsl #32
    // 0xbf53a8: cmp             w1, NULL
    // 0xbf53ac: b.ne            #0xbf53b8
    // 0xbf53b0: r1 = Null
    //     0xbf53b0: mov             x1, NULL
    // 0xbf53b4: b               #0xbf53c4
    // 0xbf53b8: LoadField: r2 = r1->field_b
    //     0xbf53b8: ldur            w2, [x1, #0xb]
    // 0xbf53bc: DecompressPointer r2
    //     0xbf53bc: add             x2, x2, HEAP, lsl #32
    // 0xbf53c0: mov             x1, x2
    // 0xbf53c4: cmp             w1, NULL
    // 0xbf53c8: b.ne            #0xbf5430
    // 0xbf53cc: r1 = ""
    //     0xbf53cc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf53d0: b               #0xbf5430
    // 0xbf53d4: ldur            x0, [fp, #-8]
    // 0xbf53d8: LoadField: r1 = r0->field_b
    //     0xbf53d8: ldur            w1, [x0, #0xb]
    // 0xbf53dc: DecompressPointer r1
    //     0xbf53dc: add             x1, x1, HEAP, lsl #32
    // 0xbf53e0: cmp             w1, NULL
    // 0xbf53e4: b.eq            #0xbf6dfc
    // 0xbf53e8: LoadField: r2 = r1->field_1f
    //     0xbf53e8: ldur            w2, [x1, #0x1f]
    // 0xbf53ec: DecompressPointer r2
    //     0xbf53ec: add             x2, x2, HEAP, lsl #32
    // 0xbf53f0: cmp             w2, NULL
    // 0xbf53f4: b.ne            #0xbf5400
    // 0xbf53f8: r1 = Null
    //     0xbf53f8: mov             x1, NULL
    // 0xbf53fc: b               #0xbf5424
    // 0xbf5400: LoadField: r1 = r2->field_d7
    //     0xbf5400: ldur            w1, [x2, #0xd7]
    // 0xbf5404: DecompressPointer r1
    //     0xbf5404: add             x1, x1, HEAP, lsl #32
    // 0xbf5408: cmp             w1, NULL
    // 0xbf540c: b.ne            #0xbf5418
    // 0xbf5410: r1 = Null
    //     0xbf5410: mov             x1, NULL
    // 0xbf5414: b               #0xbf5424
    // 0xbf5418: LoadField: r2 = r1->field_b
    //     0xbf5418: ldur            w2, [x1, #0xb]
    // 0xbf541c: DecompressPointer r2
    //     0xbf541c: add             x2, x2, HEAP, lsl #32
    // 0xbf5420: mov             x1, x2
    // 0xbf5424: cmp             w1, NULL
    // 0xbf5428: b.ne            #0xbf5430
    // 0xbf542c: r1 = ""
    //     0xbf542c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5430: mov             x2, x1
    // 0xbf5434: ldur            x1, [fp, #-0x10]
    // 0xbf5438: stur            x2, [fp, #-0x40]
    // 0xbf543c: r0 = of()
    //     0xbf543c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf5440: LoadField: r1 = r0->field_87
    //     0xbf5440: ldur            w1, [x0, #0x87]
    // 0xbf5444: DecompressPointer r1
    //     0xbf5444: add             x1, x1, HEAP, lsl #32
    // 0xbf5448: LoadField: r0 = r1->field_7
    //     0xbf5448: ldur            w0, [x1, #7]
    // 0xbf544c: DecompressPointer r0
    //     0xbf544c: add             x0, x0, HEAP, lsl #32
    // 0xbf5450: r16 = 12.000000
    //     0xbf5450: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf5454: ldr             x16, [x16, #0x9e8]
    // 0xbf5458: r30 = Instance_Color
    //     0xbf5458: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf545c: stp             lr, x16, [SP]
    // 0xbf5460: mov             x1, x0
    // 0xbf5464: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf5464: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf5468: ldr             x4, [x4, #0xaa0]
    // 0xbf546c: r0 = copyWith()
    //     0xbf546c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf5470: stur            x0, [fp, #-0x48]
    // 0xbf5474: r0 = Text()
    //     0xbf5474: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf5478: mov             x1, x0
    // 0xbf547c: ldur            x0, [fp, #-0x40]
    // 0xbf5480: stur            x1, [fp, #-0x58]
    // 0xbf5484: StoreField: r1->field_b = r0
    //     0xbf5484: stur            w0, [x1, #0xb]
    // 0xbf5488: ldur            x0, [fp, #-0x48]
    // 0xbf548c: StoreField: r1->field_13 = r0
    //     0xbf548c: stur            w0, [x1, #0x13]
    // 0xbf5490: r0 = Instance_TextOverflow
    //     0xbf5490: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbf5494: ldr             x0, [x0, #0xe10]
    // 0xbf5498: StoreField: r1->field_2b = r0
    //     0xbf5498: stur            w0, [x1, #0x2b]
    // 0xbf549c: r2 = 2
    //     0xbf549c: movz            x2, #0x2
    // 0xbf54a0: StoreField: r1->field_37 = r2
    //     0xbf54a0: stur            w2, [x1, #0x37]
    // 0xbf54a4: r0 = SizedBox()
    //     0xbf54a4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbf54a8: mov             x2, x0
    // 0xbf54ac: r0 = 150.000000
    //     0xbf54ac: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c690] 150
    //     0xbf54b0: ldr             x0, [x0, #0x690]
    // 0xbf54b4: stur            x2, [fp, #-0x40]
    // 0xbf54b8: StoreField: r2->field_f = r0
    //     0xbf54b8: stur            w0, [x2, #0xf]
    // 0xbf54bc: ldur            x0, [fp, #-0x58]
    // 0xbf54c0: StoreField: r2->field_b = r0
    //     0xbf54c0: stur            w0, [x2, #0xb]
    // 0xbf54c4: ldur            x1, [fp, #-0x10]
    // 0xbf54c8: r0 = of()
    //     0xbf54c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf54cc: LoadField: r1 = r0->field_87
    //     0xbf54cc: ldur            w1, [x0, #0x87]
    // 0xbf54d0: DecompressPointer r1
    //     0xbf54d0: add             x1, x1, HEAP, lsl #32
    // 0xbf54d4: LoadField: r0 = r1->field_2b
    //     0xbf54d4: ldur            w0, [x1, #0x2b]
    // 0xbf54d8: DecompressPointer r0
    //     0xbf54d8: add             x0, x0, HEAP, lsl #32
    // 0xbf54dc: r16 = 12.000000
    //     0xbf54dc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf54e0: ldr             x16, [x16, #0x9e8]
    // 0xbf54e4: r30 = Instance_Color
    //     0xbf54e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbf54e8: ldr             lr, [lr, #0x858]
    // 0xbf54ec: stp             lr, x16, [SP]
    // 0xbf54f0: mov             x1, x0
    // 0xbf54f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf54f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf54f8: ldr             x4, [x4, #0xaa0]
    // 0xbf54fc: r0 = copyWith()
    //     0xbf54fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf5500: stur            x0, [fp, #-0x48]
    // 0xbf5504: r0 = Text()
    //     0xbf5504: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf5508: mov             x1, x0
    // 0xbf550c: r0 = "Free"
    //     0xbf550c: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c668] "Free"
    //     0xbf5510: ldr             x0, [x0, #0x668]
    // 0xbf5514: stur            x1, [fp, #-0x58]
    // 0xbf5518: StoreField: r1->field_b = r0
    //     0xbf5518: stur            w0, [x1, #0xb]
    // 0xbf551c: ldur            x0, [fp, #-0x48]
    // 0xbf5520: StoreField: r1->field_13 = r0
    //     0xbf5520: stur            w0, [x1, #0x13]
    // 0xbf5524: ldur            x0, [fp, #-8]
    // 0xbf5528: LoadField: r2 = r0->field_b
    //     0xbf5528: ldur            w2, [x0, #0xb]
    // 0xbf552c: DecompressPointer r2
    //     0xbf552c: add             x2, x2, HEAP, lsl #32
    // 0xbf5530: cmp             w2, NULL
    // 0xbf5534: b.eq            #0xbf6e00
    // 0xbf5538: LoadField: r3 = r2->field_f
    //     0xbf5538: ldur            w3, [x2, #0xf]
    // 0xbf553c: DecompressPointer r3
    //     0xbf553c: add             x3, x3, HEAP, lsl #32
    // 0xbf5540: r16 = "return_order_intermediate"
    //     0xbf5540: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xbf5544: ldr             x16, [x16, #0xb00]
    // 0xbf5548: stp             x16, x3, [SP]
    // 0xbf554c: r0 = ==()
    //     0xbf554c: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5550: tbnz            w0, #4, #0xbf55b8
    // 0xbf5554: ldur            x0, [fp, #-8]
    // 0xbf5558: LoadField: r1 = r0->field_b
    //     0xbf5558: ldur            w1, [x0, #0xb]
    // 0xbf555c: DecompressPointer r1
    //     0xbf555c: add             x1, x1, HEAP, lsl #32
    // 0xbf5560: cmp             w1, NULL
    // 0xbf5564: b.eq            #0xbf6e04
    // 0xbf5568: LoadField: r2 = r1->field_23
    //     0xbf5568: ldur            w2, [x1, #0x23]
    // 0xbf556c: DecompressPointer r2
    //     0xbf556c: add             x2, x2, HEAP, lsl #32
    // 0xbf5570: cmp             w2, NULL
    // 0xbf5574: b.ne            #0xbf5580
    // 0xbf5578: r1 = Null
    //     0xbf5578: mov             x1, NULL
    // 0xbf557c: b               #0xbf55a4
    // 0xbf5580: LoadField: r1 = r2->field_3f
    //     0xbf5580: ldur            w1, [x2, #0x3f]
    // 0xbf5584: DecompressPointer r1
    //     0xbf5584: add             x1, x1, HEAP, lsl #32
    // 0xbf5588: cmp             w1, NULL
    // 0xbf558c: b.ne            #0xbf5598
    // 0xbf5590: r1 = Null
    //     0xbf5590: mov             x1, NULL
    // 0xbf5594: b               #0xbf55a4
    // 0xbf5598: LoadField: r2 = r1->field_13
    //     0xbf5598: ldur            w2, [x1, #0x13]
    // 0xbf559c: DecompressPointer r2
    //     0xbf559c: add             x2, x2, HEAP, lsl #32
    // 0xbf55a0: mov             x1, x2
    // 0xbf55a4: cmp             w1, NULL
    // 0xbf55a8: b.ne            #0xbf55b0
    // 0xbf55ac: r1 = ""
    //     0xbf55ac: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf55b0: mov             x6, x1
    // 0xbf55b4: b               #0xbf56a8
    // 0xbf55b8: ldur            x0, [fp, #-8]
    // 0xbf55bc: LoadField: r1 = r0->field_b
    //     0xbf55bc: ldur            w1, [x0, #0xb]
    // 0xbf55c0: DecompressPointer r1
    //     0xbf55c0: add             x1, x1, HEAP, lsl #32
    // 0xbf55c4: cmp             w1, NULL
    // 0xbf55c8: b.eq            #0xbf6e08
    // 0xbf55cc: LoadField: r2 = r1->field_f
    //     0xbf55cc: ldur            w2, [x1, #0xf]
    // 0xbf55d0: DecompressPointer r2
    //     0xbf55d0: add             x2, x2, HEAP, lsl #32
    // 0xbf55d4: r16 = "cancel_order"
    //     0xbf55d4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf55d8: ldr             x16, [x16, #0x98]
    // 0xbf55dc: stp             x16, x2, [SP]
    // 0xbf55e0: r0 = ==()
    //     0xbf55e0: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf55e4: tbnz            w0, #4, #0xbf5648
    // 0xbf55e8: ldur            x0, [fp, #-8]
    // 0xbf55ec: LoadField: r1 = r0->field_b
    //     0xbf55ec: ldur            w1, [x0, #0xb]
    // 0xbf55f0: DecompressPointer r1
    //     0xbf55f0: add             x1, x1, HEAP, lsl #32
    // 0xbf55f4: cmp             w1, NULL
    // 0xbf55f8: b.eq            #0xbf6e0c
    // 0xbf55fc: LoadField: r2 = r1->field_1b
    //     0xbf55fc: ldur            w2, [x1, #0x1b]
    // 0xbf5600: DecompressPointer r2
    //     0xbf5600: add             x2, x2, HEAP, lsl #32
    // 0xbf5604: cmp             w2, NULL
    // 0xbf5608: b.ne            #0xbf5614
    // 0xbf560c: r1 = Null
    //     0xbf560c: mov             x1, NULL
    // 0xbf5610: b               #0xbf5638
    // 0xbf5614: LoadField: r1 = r2->field_7f
    //     0xbf5614: ldur            w1, [x2, #0x7f]
    // 0xbf5618: DecompressPointer r1
    //     0xbf5618: add             x1, x1, HEAP, lsl #32
    // 0xbf561c: cmp             w1, NULL
    // 0xbf5620: b.ne            #0xbf562c
    // 0xbf5624: r1 = Null
    //     0xbf5624: mov             x1, NULL
    // 0xbf5628: b               #0xbf5638
    // 0xbf562c: LoadField: r2 = r1->field_13
    //     0xbf562c: ldur            w2, [x1, #0x13]
    // 0xbf5630: DecompressPointer r2
    //     0xbf5630: add             x2, x2, HEAP, lsl #32
    // 0xbf5634: mov             x1, x2
    // 0xbf5638: cmp             w1, NULL
    // 0xbf563c: b.ne            #0xbf56a4
    // 0xbf5640: r1 = ""
    //     0xbf5640: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5644: b               #0xbf56a4
    // 0xbf5648: ldur            x0, [fp, #-8]
    // 0xbf564c: LoadField: r1 = r0->field_b
    //     0xbf564c: ldur            w1, [x0, #0xb]
    // 0xbf5650: DecompressPointer r1
    //     0xbf5650: add             x1, x1, HEAP, lsl #32
    // 0xbf5654: cmp             w1, NULL
    // 0xbf5658: b.eq            #0xbf6e10
    // 0xbf565c: LoadField: r2 = r1->field_1f
    //     0xbf565c: ldur            w2, [x1, #0x1f]
    // 0xbf5660: DecompressPointer r2
    //     0xbf5660: add             x2, x2, HEAP, lsl #32
    // 0xbf5664: cmp             w2, NULL
    // 0xbf5668: b.ne            #0xbf5674
    // 0xbf566c: r1 = Null
    //     0xbf566c: mov             x1, NULL
    // 0xbf5670: b               #0xbf5698
    // 0xbf5674: LoadField: r1 = r2->field_d7
    //     0xbf5674: ldur            w1, [x2, #0xd7]
    // 0xbf5678: DecompressPointer r1
    //     0xbf5678: add             x1, x1, HEAP, lsl #32
    // 0xbf567c: cmp             w1, NULL
    // 0xbf5680: b.ne            #0xbf568c
    // 0xbf5684: r1 = Null
    //     0xbf5684: mov             x1, NULL
    // 0xbf5688: b               #0xbf5698
    // 0xbf568c: LoadField: r2 = r1->field_13
    //     0xbf568c: ldur            w2, [x1, #0x13]
    // 0xbf5690: DecompressPointer r2
    //     0xbf5690: add             x2, x2, HEAP, lsl #32
    // 0xbf5694: mov             x1, x2
    // 0xbf5698: cmp             w1, NULL
    // 0xbf569c: b.ne            #0xbf56a4
    // 0xbf56a0: r1 = ""
    //     0xbf56a0: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf56a4: mov             x6, x1
    // 0xbf56a8: ldur            x5, [fp, #-0x28]
    // 0xbf56ac: ldur            x4, [fp, #-0x50]
    // 0xbf56b0: ldur            x3, [fp, #-0x40]
    // 0xbf56b4: ldur            x2, [fp, #-0x58]
    // 0xbf56b8: ldur            x1, [fp, #-0x10]
    // 0xbf56bc: stur            x6, [fp, #-0x48]
    // 0xbf56c0: r0 = of()
    //     0xbf56c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf56c4: LoadField: r1 = r0->field_87
    //     0xbf56c4: ldur            w1, [x0, #0x87]
    // 0xbf56c8: DecompressPointer r1
    //     0xbf56c8: add             x1, x1, HEAP, lsl #32
    // 0xbf56cc: LoadField: r0 = r1->field_2b
    //     0xbf56cc: ldur            w0, [x1, #0x2b]
    // 0xbf56d0: DecompressPointer r0
    //     0xbf56d0: add             x0, x0, HEAP, lsl #32
    // 0xbf56d4: r16 = 12.000000
    //     0xbf56d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf56d8: ldr             x16, [x16, #0x9e8]
    // 0xbf56dc: r30 = Instance_TextDecoration
    //     0xbf56dc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xbf56e0: ldr             lr, [lr, #0xe30]
    // 0xbf56e4: stp             lr, x16, [SP]
    // 0xbf56e8: mov             x1, x0
    // 0xbf56ec: r4 = const [0, 0x3, 0x2, 0x1, decoration, 0x2, fontSize, 0x1, null]
    //     0xbf56ec: add             x4, PP, #0x3c, lsl #12  ; [pp+0x3c698] List(9) [0, 0x3, 0x2, 0x1, "decoration", 0x2, "fontSize", 0x1, Null]
    //     0xbf56f0: ldr             x4, [x4, #0x698]
    // 0xbf56f4: r0 = copyWith()
    //     0xbf56f4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf56f8: stur            x0, [fp, #-0x60]
    // 0xbf56fc: r0 = Text()
    //     0xbf56fc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf5700: mov             x3, x0
    // 0xbf5704: ldur            x0, [fp, #-0x48]
    // 0xbf5708: stur            x3, [fp, #-0x68]
    // 0xbf570c: StoreField: r3->field_b = r0
    //     0xbf570c: stur            w0, [x3, #0xb]
    // 0xbf5710: ldur            x0, [fp, #-0x60]
    // 0xbf5714: StoreField: r3->field_13 = r0
    //     0xbf5714: stur            w0, [x3, #0x13]
    // 0xbf5718: r1 = Null
    //     0xbf5718: mov             x1, NULL
    // 0xbf571c: r2 = 6
    //     0xbf571c: movz            x2, #0x6
    // 0xbf5720: r0 = AllocateArray()
    //     0xbf5720: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf5724: mov             x2, x0
    // 0xbf5728: ldur            x0, [fp, #-0x58]
    // 0xbf572c: stur            x2, [fp, #-0x48]
    // 0xbf5730: StoreField: r2->field_f = r0
    //     0xbf5730: stur            w0, [x2, #0xf]
    // 0xbf5734: r16 = Instance_SizedBox
    //     0xbf5734: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fa50] Obj!SizedBox@d67e01
    //     0xbf5738: ldr             x16, [x16, #0xa50]
    // 0xbf573c: StoreField: r2->field_13 = r16
    //     0xbf573c: stur            w16, [x2, #0x13]
    // 0xbf5740: ldur            x0, [fp, #-0x68]
    // 0xbf5744: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf5744: stur            w0, [x2, #0x17]
    // 0xbf5748: r1 = <Widget>
    //     0xbf5748: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf574c: r0 = AllocateGrowableArray()
    //     0xbf574c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf5750: mov             x1, x0
    // 0xbf5754: ldur            x0, [fp, #-0x48]
    // 0xbf5758: stur            x1, [fp, #-0x58]
    // 0xbf575c: StoreField: r1->field_f = r0
    //     0xbf575c: stur            w0, [x1, #0xf]
    // 0xbf5760: r2 = 6
    //     0xbf5760: movz            x2, #0x6
    // 0xbf5764: StoreField: r1->field_b = r2
    //     0xbf5764: stur            w2, [x1, #0xb]
    // 0xbf5768: r0 = Row()
    //     0xbf5768: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf576c: mov             x3, x0
    // 0xbf5770: r0 = Instance_Axis
    //     0xbf5770: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf5774: stur            x3, [fp, #-0x48]
    // 0xbf5778: StoreField: r3->field_f = r0
    //     0xbf5778: stur            w0, [x3, #0xf]
    // 0xbf577c: r4 = Instance_MainAxisAlignment
    //     0xbf577c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf5780: ldr             x4, [x4, #0xa08]
    // 0xbf5784: StoreField: r3->field_13 = r4
    //     0xbf5784: stur            w4, [x3, #0x13]
    // 0xbf5788: r5 = Instance_MainAxisSize
    //     0xbf5788: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf578c: ldr             x5, [x5, #0xa10]
    // 0xbf5790: ArrayStore: r3[0] = r5  ; List_4
    //     0xbf5790: stur            w5, [x3, #0x17]
    // 0xbf5794: r6 = Instance_CrossAxisAlignment
    //     0xbf5794: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf5798: ldr             x6, [x6, #0xa18]
    // 0xbf579c: StoreField: r3->field_1b = r6
    //     0xbf579c: stur            w6, [x3, #0x1b]
    // 0xbf57a0: r7 = Instance_VerticalDirection
    //     0xbf57a0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf57a4: ldr             x7, [x7, #0xa20]
    // 0xbf57a8: StoreField: r3->field_23 = r7
    //     0xbf57a8: stur            w7, [x3, #0x23]
    // 0xbf57ac: r8 = Instance_Clip
    //     0xbf57ac: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf57b0: ldr             x8, [x8, #0x38]
    // 0xbf57b4: StoreField: r3->field_2b = r8
    //     0xbf57b4: stur            w8, [x3, #0x2b]
    // 0xbf57b8: StoreField: r3->field_2f = rZR
    //     0xbf57b8: stur            xzr, [x3, #0x2f]
    // 0xbf57bc: ldur            x1, [fp, #-0x58]
    // 0xbf57c0: StoreField: r3->field_b = r1
    //     0xbf57c0: stur            w1, [x3, #0xb]
    // 0xbf57c4: r1 = Null
    //     0xbf57c4: mov             x1, NULL
    // 0xbf57c8: r2 = 6
    //     0xbf57c8: movz            x2, #0x6
    // 0xbf57cc: r0 = AllocateArray()
    //     0xbf57cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf57d0: mov             x2, x0
    // 0xbf57d4: ldur            x0, [fp, #-0x40]
    // 0xbf57d8: stur            x2, [fp, #-0x58]
    // 0xbf57dc: StoreField: r2->field_f = r0
    //     0xbf57dc: stur            w0, [x2, #0xf]
    // 0xbf57e0: r16 = Instance_SizedBox
    //     0xbf57e0: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbf57e4: ldr             x16, [x16, #0xc70]
    // 0xbf57e8: StoreField: r2->field_13 = r16
    //     0xbf57e8: stur            w16, [x2, #0x13]
    // 0xbf57ec: ldur            x0, [fp, #-0x48]
    // 0xbf57f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf57f0: stur            w0, [x2, #0x17]
    // 0xbf57f4: r1 = <Widget>
    //     0xbf57f4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf57f8: r0 = AllocateGrowableArray()
    //     0xbf57f8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf57fc: mov             x1, x0
    // 0xbf5800: ldur            x0, [fp, #-0x58]
    // 0xbf5804: stur            x1, [fp, #-0x40]
    // 0xbf5808: StoreField: r1->field_f = r0
    //     0xbf5808: stur            w0, [x1, #0xf]
    // 0xbf580c: r2 = 6
    //     0xbf580c: movz            x2, #0x6
    // 0xbf5810: StoreField: r1->field_b = r2
    //     0xbf5810: stur            w2, [x1, #0xb]
    // 0xbf5814: r0 = Column()
    //     0xbf5814: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf5818: mov             x1, x0
    // 0xbf581c: r0 = Instance_Axis
    //     0xbf581c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf5820: stur            x1, [fp, #-0x48]
    // 0xbf5824: StoreField: r1->field_f = r0
    //     0xbf5824: stur            w0, [x1, #0xf]
    // 0xbf5828: r2 = Instance_MainAxisAlignment
    //     0xbf5828: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf582c: ldr             x2, [x2, #0xa08]
    // 0xbf5830: StoreField: r1->field_13 = r2
    //     0xbf5830: stur            w2, [x1, #0x13]
    // 0xbf5834: r3 = Instance_MainAxisSize
    //     0xbf5834: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf5838: ldr             x3, [x3, #0xa10]
    // 0xbf583c: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf583c: stur            w3, [x1, #0x17]
    // 0xbf5840: r4 = Instance_CrossAxisAlignment
    //     0xbf5840: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf5844: ldr             x4, [x4, #0x890]
    // 0xbf5848: StoreField: r1->field_1b = r4
    //     0xbf5848: stur            w4, [x1, #0x1b]
    // 0xbf584c: r5 = Instance_VerticalDirection
    //     0xbf584c: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf5850: ldr             x5, [x5, #0xa20]
    // 0xbf5854: StoreField: r1->field_23 = r5
    //     0xbf5854: stur            w5, [x1, #0x23]
    // 0xbf5858: r6 = Instance_Clip
    //     0xbf5858: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf585c: ldr             x6, [x6, #0x38]
    // 0xbf5860: StoreField: r1->field_2b = r6
    //     0xbf5860: stur            w6, [x1, #0x2b]
    // 0xbf5864: StoreField: r1->field_2f = rZR
    //     0xbf5864: stur            xzr, [x1, #0x2f]
    // 0xbf5868: ldur            x7, [fp, #-0x40]
    // 0xbf586c: StoreField: r1->field_b = r7
    //     0xbf586c: stur            w7, [x1, #0xb]
    // 0xbf5870: r0 = Padding()
    //     0xbf5870: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf5874: mov             x2, x0
    // 0xbf5878: r0 = Instance_EdgeInsets
    //     0xbf5878: add             x0, PP, #0x36, lsl #12  ; [pp+0x36a78] Obj!EdgeInsets@d57741
    //     0xbf587c: ldr             x0, [x0, #0xa78]
    // 0xbf5880: stur            x2, [fp, #-0x40]
    // 0xbf5884: StoreField: r2->field_f = r0
    //     0xbf5884: stur            w0, [x2, #0xf]
    // 0xbf5888: ldur            x0, [fp, #-0x48]
    // 0xbf588c: StoreField: r2->field_b = r0
    //     0xbf588c: stur            w0, [x2, #0xb]
    // 0xbf5890: r1 = <FlexParentData>
    //     0xbf5890: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbf5894: ldr             x1, [x1, #0xe00]
    // 0xbf5898: r0 = Expanded()
    //     0xbf5898: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbf589c: mov             x3, x0
    // 0xbf58a0: r0 = 1
    //     0xbf58a0: movz            x0, #0x1
    // 0xbf58a4: stur            x3, [fp, #-0x48]
    // 0xbf58a8: StoreField: r3->field_13 = r0
    //     0xbf58a8: stur            x0, [x3, #0x13]
    // 0xbf58ac: r4 = Instance_FlexFit
    //     0xbf58ac: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbf58b0: ldr             x4, [x4, #0xe08]
    // 0xbf58b4: StoreField: r3->field_1b = r4
    //     0xbf58b4: stur            w4, [x3, #0x1b]
    // 0xbf58b8: ldur            x1, [fp, #-0x40]
    // 0xbf58bc: StoreField: r3->field_b = r1
    //     0xbf58bc: stur            w1, [x3, #0xb]
    // 0xbf58c0: r1 = Null
    //     0xbf58c0: mov             x1, NULL
    // 0xbf58c4: r2 = 6
    //     0xbf58c4: movz            x2, #0x6
    // 0xbf58c8: r0 = AllocateArray()
    //     0xbf58c8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf58cc: mov             x2, x0
    // 0xbf58d0: ldur            x0, [fp, #-0x28]
    // 0xbf58d4: stur            x2, [fp, #-0x40]
    // 0xbf58d8: StoreField: r2->field_f = r0
    //     0xbf58d8: stur            w0, [x2, #0xf]
    // 0xbf58dc: ldur            x0, [fp, #-0x50]
    // 0xbf58e0: StoreField: r2->field_13 = r0
    //     0xbf58e0: stur            w0, [x2, #0x13]
    // 0xbf58e4: ldur            x0, [fp, #-0x48]
    // 0xbf58e8: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf58e8: stur            w0, [x2, #0x17]
    // 0xbf58ec: r1 = <Widget>
    //     0xbf58ec: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf58f0: r0 = AllocateGrowableArray()
    //     0xbf58f0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf58f4: mov             x1, x0
    // 0xbf58f8: ldur            x0, [fp, #-0x40]
    // 0xbf58fc: stur            x1, [fp, #-0x28]
    // 0xbf5900: StoreField: r1->field_f = r0
    //     0xbf5900: stur            w0, [x1, #0xf]
    // 0xbf5904: r2 = 6
    //     0xbf5904: movz            x2, #0x6
    // 0xbf5908: StoreField: r1->field_b = r2
    //     0xbf5908: stur            w2, [x1, #0xb]
    // 0xbf590c: r0 = Row()
    //     0xbf590c: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf5910: mov             x1, x0
    // 0xbf5914: r0 = Instance_Axis
    //     0xbf5914: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf5918: stur            x1, [fp, #-0x40]
    // 0xbf591c: StoreField: r1->field_f = r0
    //     0xbf591c: stur            w0, [x1, #0xf]
    // 0xbf5920: r2 = Instance_MainAxisAlignment
    //     0xbf5920: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf5924: ldr             x2, [x2, #0xa08]
    // 0xbf5928: StoreField: r1->field_13 = r2
    //     0xbf5928: stur            w2, [x1, #0x13]
    // 0xbf592c: r3 = Instance_MainAxisSize
    //     0xbf592c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf5930: ldr             x3, [x3, #0xa10]
    // 0xbf5934: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf5934: stur            w3, [x1, #0x17]
    // 0xbf5938: r4 = Instance_CrossAxisAlignment
    //     0xbf5938: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf593c: ldr             x4, [x4, #0xa18]
    // 0xbf5940: StoreField: r1->field_1b = r4
    //     0xbf5940: stur            w4, [x1, #0x1b]
    // 0xbf5944: r5 = Instance_VerticalDirection
    //     0xbf5944: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf5948: ldr             x5, [x5, #0xa20]
    // 0xbf594c: StoreField: r1->field_23 = r5
    //     0xbf594c: stur            w5, [x1, #0x23]
    // 0xbf5950: r6 = Instance_Clip
    //     0xbf5950: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf5954: ldr             x6, [x6, #0x38]
    // 0xbf5958: StoreField: r1->field_2b = r6
    //     0xbf5958: stur            w6, [x1, #0x2b]
    // 0xbf595c: StoreField: r1->field_2f = rZR
    //     0xbf595c: stur            xzr, [x1, #0x2f]
    // 0xbf5960: ldur            x7, [fp, #-0x28]
    // 0xbf5964: StoreField: r1->field_b = r7
    //     0xbf5964: stur            w7, [x1, #0xb]
    // 0xbf5968: r0 = Container()
    //     0xbf5968: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf596c: stur            x0, [fp, #-0x28]
    // 0xbf5970: ldur            x16, [fp, #-0x38]
    // 0xbf5974: ldur            lr, [fp, #-0x40]
    // 0xbf5978: stp             lr, x16, [SP]
    // 0xbf597c: mov             x1, x0
    // 0xbf5980: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xbf5980: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e088] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xbf5984: ldr             x4, [x4, #0x88]
    // 0xbf5988: r0 = Container()
    //     0xbf5988: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf598c: r0 = Padding()
    //     0xbf598c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf5990: mov             x2, x0
    // 0xbf5994: r0 = Instance_EdgeInsets
    //     0xbf5994: add             x0, PP, #0x36, lsl #12  ; [pp+0x36b00] Obj!EdgeInsets@d57cb1
    //     0xbf5998: ldr             x0, [x0, #0xb00]
    // 0xbf599c: stur            x2, [fp, #-0x38]
    // 0xbf59a0: StoreField: r2->field_f = r0
    //     0xbf59a0: stur            w0, [x2, #0xf]
    // 0xbf59a4: ldur            x0, [fp, #-0x28]
    // 0xbf59a8: StoreField: r2->field_b = r0
    //     0xbf59a8: stur            w0, [x2, #0xb]
    // 0xbf59ac: ldur            x1, [fp, #-0x10]
    // 0xbf59b0: r0 = of()
    //     0xbf59b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf59b4: LoadField: r1 = r0->field_5b
    //     0xbf59b4: ldur            w1, [x0, #0x5b]
    // 0xbf59b8: DecompressPointer r1
    //     0xbf59b8: add             x1, x1, HEAP, lsl #32
    // 0xbf59bc: r0 = LoadClassIdInstr(r1)
    //     0xbf59bc: ldur            x0, [x1, #-1]
    //     0xbf59c0: ubfx            x0, x0, #0xc, #0x14
    // 0xbf59c4: d0 = 0.100000
    //     0xbf59c4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0xbf59c8: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbf59c8: sub             lr, x0, #0xffa
    //     0xbf59cc: ldr             lr, [x21, lr, lsl #3]
    //     0xbf59d0: blr             lr
    // 0xbf59d4: mov             x2, x0
    // 0xbf59d8: r1 = Null
    //     0xbf59d8: mov             x1, NULL
    // 0xbf59dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbf59dc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbf59e0: r0 = Border.all()
    //     0xbf59e0: bl              #0x98d764  ; [package:flutter/src/painting/box_border.dart] Border::Border.all
    // 0xbf59e4: stur            x0, [fp, #-0x28]
    // 0xbf59e8: r0 = BoxDecoration()
    //     0xbf59e8: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbf59ec: mov             x1, x0
    // 0xbf59f0: ldur            x0, [fp, #-0x28]
    // 0xbf59f4: stur            x1, [fp, #-0x40]
    // 0xbf59f8: StoreField: r1->field_f = r0
    //     0xbf59f8: stur            w0, [x1, #0xf]
    // 0xbf59fc: r0 = Instance_BoxShape
    //     0xbf59fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbf5a00: ldr             x0, [x0, #0x80]
    // 0xbf5a04: StoreField: r1->field_23 = r0
    //     0xbf5a04: stur            w0, [x1, #0x23]
    // 0xbf5a08: r0 = ImageHeaders.forImages()
    //     0xbf5a08: bl              #0x8feda8  ; [package:customer_app/app/core/extension/extension_function.dart] ::ImageHeaders.forImages
    // 0xbf5a0c: mov             x1, x0
    // 0xbf5a10: ldur            x0, [fp, #-8]
    // 0xbf5a14: stur            x1, [fp, #-0x28]
    // 0xbf5a18: LoadField: r2 = r0->field_b
    //     0xbf5a18: ldur            w2, [x0, #0xb]
    // 0xbf5a1c: DecompressPointer r2
    //     0xbf5a1c: add             x2, x2, HEAP, lsl #32
    // 0xbf5a20: cmp             w2, NULL
    // 0xbf5a24: b.eq            #0xbf6e14
    // 0xbf5a28: LoadField: r3 = r2->field_f
    //     0xbf5a28: ldur            w3, [x2, #0xf]
    // 0xbf5a2c: DecompressPointer r3
    //     0xbf5a2c: add             x3, x3, HEAP, lsl #32
    // 0xbf5a30: r16 = "return_order_intermediate"
    //     0xbf5a30: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xbf5a34: ldr             x16, [x16, #0xb00]
    // 0xbf5a38: stp             x16, x3, [SP]
    // 0xbf5a3c: r0 = ==()
    //     0xbf5a3c: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5a40: tbnz            w0, #4, #0xbf5aa8
    // 0xbf5a44: ldur            x0, [fp, #-8]
    // 0xbf5a48: LoadField: r1 = r0->field_b
    //     0xbf5a48: ldur            w1, [x0, #0xb]
    // 0xbf5a4c: DecompressPointer r1
    //     0xbf5a4c: add             x1, x1, HEAP, lsl #32
    // 0xbf5a50: cmp             w1, NULL
    // 0xbf5a54: b.eq            #0xbf6e18
    // 0xbf5a58: LoadField: r2 = r1->field_23
    //     0xbf5a58: ldur            w2, [x1, #0x23]
    // 0xbf5a5c: DecompressPointer r2
    //     0xbf5a5c: add             x2, x2, HEAP, lsl #32
    // 0xbf5a60: cmp             w2, NULL
    // 0xbf5a64: b.ne            #0xbf5a70
    // 0xbf5a68: r1 = Null
    //     0xbf5a68: mov             x1, NULL
    // 0xbf5a6c: b               #0xbf5a94
    // 0xbf5a70: LoadField: r1 = r2->field_37
    //     0xbf5a70: ldur            w1, [x2, #0x37]
    // 0xbf5a74: DecompressPointer r1
    //     0xbf5a74: add             x1, x1, HEAP, lsl #32
    // 0xbf5a78: cmp             w1, NULL
    // 0xbf5a7c: b.ne            #0xbf5a88
    // 0xbf5a80: r1 = Null
    //     0xbf5a80: mov             x1, NULL
    // 0xbf5a84: b               #0xbf5a94
    // 0xbf5a88: LoadField: r2 = r1->field_7
    //     0xbf5a88: ldur            w2, [x1, #7]
    // 0xbf5a8c: DecompressPointer r2
    //     0xbf5a8c: add             x2, x2, HEAP, lsl #32
    // 0xbf5a90: mov             x1, x2
    // 0xbf5a94: cmp             w1, NULL
    // 0xbf5a98: b.ne            #0xbf5aa0
    // 0xbf5a9c: r1 = ""
    //     0xbf5a9c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5aa0: mov             x3, x1
    // 0xbf5aa4: b               #0xbf5b60
    // 0xbf5aa8: ldur            x0, [fp, #-8]
    // 0xbf5aac: LoadField: r1 = r0->field_b
    //     0xbf5aac: ldur            w1, [x0, #0xb]
    // 0xbf5ab0: DecompressPointer r1
    //     0xbf5ab0: add             x1, x1, HEAP, lsl #32
    // 0xbf5ab4: cmp             w1, NULL
    // 0xbf5ab8: b.eq            #0xbf6e1c
    // 0xbf5abc: LoadField: r2 = r1->field_f
    //     0xbf5abc: ldur            w2, [x1, #0xf]
    // 0xbf5ac0: DecompressPointer r2
    //     0xbf5ac0: add             x2, x2, HEAP, lsl #32
    // 0xbf5ac4: r16 = "cancel_order"
    //     0xbf5ac4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf5ac8: ldr             x16, [x16, #0x98]
    // 0xbf5acc: stp             x16, x2, [SP]
    // 0xbf5ad0: r0 = ==()
    //     0xbf5ad0: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5ad4: tbnz            w0, #4, #0xbf5b1c
    // 0xbf5ad8: ldur            x0, [fp, #-8]
    // 0xbf5adc: LoadField: r1 = r0->field_b
    //     0xbf5adc: ldur            w1, [x0, #0xb]
    // 0xbf5ae0: DecompressPointer r1
    //     0xbf5ae0: add             x1, x1, HEAP, lsl #32
    // 0xbf5ae4: cmp             w1, NULL
    // 0xbf5ae8: b.eq            #0xbf6e20
    // 0xbf5aec: LoadField: r2 = r1->field_1b
    //     0xbf5aec: ldur            w2, [x1, #0x1b]
    // 0xbf5af0: DecompressPointer r2
    //     0xbf5af0: add             x2, x2, HEAP, lsl #32
    // 0xbf5af4: cmp             w2, NULL
    // 0xbf5af8: b.ne            #0xbf5b04
    // 0xbf5afc: r1 = Null
    //     0xbf5afc: mov             x1, NULL
    // 0xbf5b00: b               #0xbf5b0c
    // 0xbf5b04: LoadField: r1 = r2->field_b
    //     0xbf5b04: ldur            w1, [x2, #0xb]
    // 0xbf5b08: DecompressPointer r1
    //     0xbf5b08: add             x1, x1, HEAP, lsl #32
    // 0xbf5b0c: cmp             w1, NULL
    // 0xbf5b10: b.ne            #0xbf5b5c
    // 0xbf5b14: r1 = ""
    //     0xbf5b14: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5b18: b               #0xbf5b5c
    // 0xbf5b1c: ldur            x0, [fp, #-8]
    // 0xbf5b20: LoadField: r1 = r0->field_b
    //     0xbf5b20: ldur            w1, [x0, #0xb]
    // 0xbf5b24: DecompressPointer r1
    //     0xbf5b24: add             x1, x1, HEAP, lsl #32
    // 0xbf5b28: cmp             w1, NULL
    // 0xbf5b2c: b.eq            #0xbf6e24
    // 0xbf5b30: LoadField: r2 = r1->field_1f
    //     0xbf5b30: ldur            w2, [x1, #0x1f]
    // 0xbf5b34: DecompressPointer r2
    //     0xbf5b34: add             x2, x2, HEAP, lsl #32
    // 0xbf5b38: cmp             w2, NULL
    // 0xbf5b3c: b.ne            #0xbf5b48
    // 0xbf5b40: r1 = Null
    //     0xbf5b40: mov             x1, NULL
    // 0xbf5b44: b               #0xbf5b50
    // 0xbf5b48: LoadField: r1 = r2->field_1b
    //     0xbf5b48: ldur            w1, [x2, #0x1b]
    // 0xbf5b4c: DecompressPointer r1
    //     0xbf5b4c: add             x1, x1, HEAP, lsl #32
    // 0xbf5b50: cmp             w1, NULL
    // 0xbf5b54: b.ne            #0xbf5b5c
    // 0xbf5b58: r1 = ""
    //     0xbf5b58: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5b5c: mov             x3, x1
    // 0xbf5b60: stur            x3, [fp, #-0x48]
    // 0xbf5b64: r1 = Function '<anonymous closure>':.
    //     0xbf5b64: add             x1, PP, #0x53, lsl #12  ; [pp+0x53940] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xbf5b68: ldr             x1, [x1, #0x940]
    // 0xbf5b6c: r2 = Null
    //     0xbf5b6c: mov             x2, NULL
    // 0xbf5b70: r0 = AllocateClosure()
    //     0xbf5b70: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf5b74: r1 = Function '<anonymous closure>':.
    //     0xbf5b74: add             x1, PP, #0x53, lsl #12  ; [pp+0x53948] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf5b78: ldr             x1, [x1, #0x948]
    // 0xbf5b7c: r2 = Null
    //     0xbf5b7c: mov             x2, NULL
    // 0xbf5b80: stur            x0, [fp, #-0x50]
    // 0xbf5b84: r0 = AllocateClosure()
    //     0xbf5b84: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf5b88: stur            x0, [fp, #-0x58]
    // 0xbf5b8c: r0 = CachedNetworkImage()
    //     0xbf5b8c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xbf5b90: stur            x0, [fp, #-0x60]
    // 0xbf5b94: r16 = 56.000000
    //     0xbf5b94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbf5b98: ldr             x16, [x16, #0xb78]
    // 0xbf5b9c: r30 = 56.000000
    //     0xbf5b9c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2eb78] 56
    //     0xbf5ba0: ldr             lr, [lr, #0xb78]
    // 0xbf5ba4: stp             lr, x16, [SP, #0x20]
    // 0xbf5ba8: ldur            x16, [fp, #-0x28]
    // 0xbf5bac: r30 = Instance_BoxFit
    //     0xbf5bac: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xbf5bb0: ldr             lr, [lr, #0x118]
    // 0xbf5bb4: stp             lr, x16, [SP, #0x10]
    // 0xbf5bb8: ldur            x16, [fp, #-0x50]
    // 0xbf5bbc: ldur            lr, [fp, #-0x58]
    // 0xbf5bc0: stp             lr, x16, [SP]
    // 0xbf5bc4: mov             x1, x0
    // 0xbf5bc8: ldur            x2, [fp, #-0x48]
    // 0xbf5bcc: r4 = const [0, 0x8, 0x6, 0x2, errorWidget, 0x7, fit, 0x5, height, 0x2, httpHeaders, 0x4, progressIndicatorBuilder, 0x6, width, 0x3, null]
    //     0xbf5bcc: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fbc8] List(17) [0, 0x8, 0x6, 0x2, "errorWidget", 0x7, "fit", 0x5, "height", 0x2, "httpHeaders", 0x4, "progressIndicatorBuilder", 0x6, "width", 0x3, Null]
    //     0xbf5bd0: ldr             x4, [x4, #0xbc8]
    // 0xbf5bd4: r0 = CachedNetworkImage()
    //     0xbf5bd4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xbf5bd8: ldur            x0, [fp, #-8]
    // 0xbf5bdc: LoadField: r1 = r0->field_b
    //     0xbf5bdc: ldur            w1, [x0, #0xb]
    // 0xbf5be0: DecompressPointer r1
    //     0xbf5be0: add             x1, x1, HEAP, lsl #32
    // 0xbf5be4: cmp             w1, NULL
    // 0xbf5be8: b.eq            #0xbf6e28
    // 0xbf5bec: LoadField: r2 = r1->field_f
    //     0xbf5bec: ldur            w2, [x1, #0xf]
    // 0xbf5bf0: DecompressPointer r2
    //     0xbf5bf0: add             x2, x2, HEAP, lsl #32
    // 0xbf5bf4: r16 = "return_order_intermediate"
    //     0xbf5bf4: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xbf5bf8: ldr             x16, [x16, #0xb00]
    // 0xbf5bfc: stp             x16, x2, [SP]
    // 0xbf5c00: r0 = ==()
    //     0xbf5c00: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5c04: tbnz            w0, #4, #0xbf5c6c
    // 0xbf5c08: ldur            x0, [fp, #-8]
    // 0xbf5c0c: LoadField: r1 = r0->field_b
    //     0xbf5c0c: ldur            w1, [x0, #0xb]
    // 0xbf5c10: DecompressPointer r1
    //     0xbf5c10: add             x1, x1, HEAP, lsl #32
    // 0xbf5c14: cmp             w1, NULL
    // 0xbf5c18: b.eq            #0xbf6e2c
    // 0xbf5c1c: LoadField: r2 = r1->field_23
    //     0xbf5c1c: ldur            w2, [x1, #0x23]
    // 0xbf5c20: DecompressPointer r2
    //     0xbf5c20: add             x2, x2, HEAP, lsl #32
    // 0xbf5c24: cmp             w2, NULL
    // 0xbf5c28: b.ne            #0xbf5c34
    // 0xbf5c2c: r1 = Null
    //     0xbf5c2c: mov             x1, NULL
    // 0xbf5c30: b               #0xbf5c58
    // 0xbf5c34: LoadField: r1 = r2->field_37
    //     0xbf5c34: ldur            w1, [x2, #0x37]
    // 0xbf5c38: DecompressPointer r1
    //     0xbf5c38: add             x1, x1, HEAP, lsl #32
    // 0xbf5c3c: cmp             w1, NULL
    // 0xbf5c40: b.ne            #0xbf5c4c
    // 0xbf5c44: r1 = Null
    //     0xbf5c44: mov             x1, NULL
    // 0xbf5c48: b               #0xbf5c58
    // 0xbf5c4c: LoadField: r2 = r1->field_b
    //     0xbf5c4c: ldur            w2, [x1, #0xb]
    // 0xbf5c50: DecompressPointer r2
    //     0xbf5c50: add             x2, x2, HEAP, lsl #32
    // 0xbf5c54: mov             x1, x2
    // 0xbf5c58: cmp             w1, NULL
    // 0xbf5c5c: b.ne            #0xbf5c64
    // 0xbf5c60: r1 = ""
    //     0xbf5c60: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5c64: mov             x2, x1
    // 0xbf5c68: b               #0xbf5d24
    // 0xbf5c6c: ldur            x0, [fp, #-8]
    // 0xbf5c70: LoadField: r1 = r0->field_b
    //     0xbf5c70: ldur            w1, [x0, #0xb]
    // 0xbf5c74: DecompressPointer r1
    //     0xbf5c74: add             x1, x1, HEAP, lsl #32
    // 0xbf5c78: cmp             w1, NULL
    // 0xbf5c7c: b.eq            #0xbf6e30
    // 0xbf5c80: LoadField: r2 = r1->field_f
    //     0xbf5c80: ldur            w2, [x1, #0xf]
    // 0xbf5c84: DecompressPointer r2
    //     0xbf5c84: add             x2, x2, HEAP, lsl #32
    // 0xbf5c88: r16 = "cancel_order"
    //     0xbf5c88: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf5c8c: ldr             x16, [x16, #0x98]
    // 0xbf5c90: stp             x16, x2, [SP]
    // 0xbf5c94: r0 = ==()
    //     0xbf5c94: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5c98: tbnz            w0, #4, #0xbf5ce0
    // 0xbf5c9c: ldur            x0, [fp, #-8]
    // 0xbf5ca0: LoadField: r1 = r0->field_b
    //     0xbf5ca0: ldur            w1, [x0, #0xb]
    // 0xbf5ca4: DecompressPointer r1
    //     0xbf5ca4: add             x1, x1, HEAP, lsl #32
    // 0xbf5ca8: cmp             w1, NULL
    // 0xbf5cac: b.eq            #0xbf6e34
    // 0xbf5cb0: LoadField: r2 = r1->field_1b
    //     0xbf5cb0: ldur            w2, [x1, #0x1b]
    // 0xbf5cb4: DecompressPointer r2
    //     0xbf5cb4: add             x2, x2, HEAP, lsl #32
    // 0xbf5cb8: cmp             w2, NULL
    // 0xbf5cbc: b.ne            #0xbf5cc8
    // 0xbf5cc0: r1 = Null
    //     0xbf5cc0: mov             x1, NULL
    // 0xbf5cc4: b               #0xbf5cd0
    // 0xbf5cc8: LoadField: r1 = r2->field_f
    //     0xbf5cc8: ldur            w1, [x2, #0xf]
    // 0xbf5ccc: DecompressPointer r1
    //     0xbf5ccc: add             x1, x1, HEAP, lsl #32
    // 0xbf5cd0: cmp             w1, NULL
    // 0xbf5cd4: b.ne            #0xbf5d20
    // 0xbf5cd8: r1 = ""
    //     0xbf5cd8: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5cdc: b               #0xbf5d20
    // 0xbf5ce0: ldur            x0, [fp, #-8]
    // 0xbf5ce4: LoadField: r1 = r0->field_b
    //     0xbf5ce4: ldur            w1, [x0, #0xb]
    // 0xbf5ce8: DecompressPointer r1
    //     0xbf5ce8: add             x1, x1, HEAP, lsl #32
    // 0xbf5cec: cmp             w1, NULL
    // 0xbf5cf0: b.eq            #0xbf6e38
    // 0xbf5cf4: LoadField: r2 = r1->field_1f
    //     0xbf5cf4: ldur            w2, [x1, #0x1f]
    // 0xbf5cf8: DecompressPointer r2
    //     0xbf5cf8: add             x2, x2, HEAP, lsl #32
    // 0xbf5cfc: cmp             w2, NULL
    // 0xbf5d00: b.ne            #0xbf5d0c
    // 0xbf5d04: r1 = Null
    //     0xbf5d04: mov             x1, NULL
    // 0xbf5d08: b               #0xbf5d14
    // 0xbf5d0c: LoadField: r1 = r2->field_b
    //     0xbf5d0c: ldur            w1, [x2, #0xb]
    // 0xbf5d10: DecompressPointer r1
    //     0xbf5d10: add             x1, x1, HEAP, lsl #32
    // 0xbf5d14: cmp             w1, NULL
    // 0xbf5d18: b.ne            #0xbf5d20
    // 0xbf5d1c: r1 = ""
    //     0xbf5d1c: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5d20: mov             x2, x1
    // 0xbf5d24: ldur            x1, [fp, #-0x10]
    // 0xbf5d28: stur            x2, [fp, #-0x28]
    // 0xbf5d2c: r0 = of()
    //     0xbf5d2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf5d30: LoadField: r1 = r0->field_87
    //     0xbf5d30: ldur            w1, [x0, #0x87]
    // 0xbf5d34: DecompressPointer r1
    //     0xbf5d34: add             x1, x1, HEAP, lsl #32
    // 0xbf5d38: LoadField: r0 = r1->field_7
    //     0xbf5d38: ldur            w0, [x1, #7]
    // 0xbf5d3c: DecompressPointer r0
    //     0xbf5d3c: add             x0, x0, HEAP, lsl #32
    // 0xbf5d40: r16 = Instance_Color
    //     0xbf5d40: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf5d44: r30 = 12.000000
    //     0xbf5d44: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf5d48: ldr             lr, [lr, #0x9e8]
    // 0xbf5d4c: stp             lr, x16, [SP]
    // 0xbf5d50: mov             x1, x0
    // 0xbf5d54: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbf5d54: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbf5d58: ldr             x4, [x4, #0x9b8]
    // 0xbf5d5c: r0 = copyWith()
    //     0xbf5d5c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf5d60: stur            x0, [fp, #-0x48]
    // 0xbf5d64: r0 = Text()
    //     0xbf5d64: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf5d68: mov             x1, x0
    // 0xbf5d6c: ldur            x0, [fp, #-0x28]
    // 0xbf5d70: stur            x1, [fp, #-0x50]
    // 0xbf5d74: StoreField: r1->field_b = r0
    //     0xbf5d74: stur            w0, [x1, #0xb]
    // 0xbf5d78: ldur            x0, [fp, #-0x48]
    // 0xbf5d7c: StoreField: r1->field_13 = r0
    //     0xbf5d7c: stur            w0, [x1, #0x13]
    // 0xbf5d80: r0 = Instance_TextOverflow
    //     0xbf5d80: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xbf5d84: ldr             x0, [x0, #0xe10]
    // 0xbf5d88: StoreField: r1->field_2b = r0
    //     0xbf5d88: stur            w0, [x1, #0x2b]
    // 0xbf5d8c: r0 = 4
    //     0xbf5d8c: movz            x0, #0x4
    // 0xbf5d90: StoreField: r1->field_37 = r0
    //     0xbf5d90: stur            w0, [x1, #0x37]
    // 0xbf5d94: ldur            x0, [fp, #-8]
    // 0xbf5d98: LoadField: r2 = r0->field_b
    //     0xbf5d98: ldur            w2, [x0, #0xb]
    // 0xbf5d9c: DecompressPointer r2
    //     0xbf5d9c: add             x2, x2, HEAP, lsl #32
    // 0xbf5da0: cmp             w2, NULL
    // 0xbf5da4: b.eq            #0xbf6e3c
    // 0xbf5da8: LoadField: r3 = r2->field_f
    //     0xbf5da8: ldur            w3, [x2, #0xf]
    // 0xbf5dac: DecompressPointer r3
    //     0xbf5dac: add             x3, x3, HEAP, lsl #32
    // 0xbf5db0: r16 = "return_order_intermediate"
    //     0xbf5db0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xbf5db4: ldr             x16, [x16, #0xb00]
    // 0xbf5db8: stp             x16, x3, [SP]
    // 0xbf5dbc: r0 = ==()
    //     0xbf5dbc: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf5dc0: tbnz            w0, #4, #0xbf5fd8
    // 0xbf5dc4: ldur            x1, [fp, #-8]
    // 0xbf5dc8: LoadField: r0 = r1->field_b
    //     0xbf5dc8: ldur            w0, [x1, #0xb]
    // 0xbf5dcc: DecompressPointer r0
    //     0xbf5dcc: add             x0, x0, HEAP, lsl #32
    // 0xbf5dd0: cmp             w0, NULL
    // 0xbf5dd4: b.eq            #0xbf6e40
    // 0xbf5dd8: LoadField: r2 = r0->field_23
    //     0xbf5dd8: ldur            w2, [x0, #0x23]
    // 0xbf5ddc: DecompressPointer r2
    //     0xbf5ddc: add             x2, x2, HEAP, lsl #32
    // 0xbf5de0: cmp             w2, NULL
    // 0xbf5de4: b.ne            #0xbf5df0
    // 0xbf5de8: r0 = Null
    //     0xbf5de8: mov             x0, NULL
    // 0xbf5dec: b               #0xbf5e14
    // 0xbf5df0: LoadField: r0 = r2->field_37
    //     0xbf5df0: ldur            w0, [x2, #0x37]
    // 0xbf5df4: DecompressPointer r0
    //     0xbf5df4: add             x0, x0, HEAP, lsl #32
    // 0xbf5df8: cmp             w0, NULL
    // 0xbf5dfc: b.ne            #0xbf5e08
    // 0xbf5e00: r0 = Null
    //     0xbf5e00: mov             x0, NULL
    // 0xbf5e04: b               #0xbf5e14
    // 0xbf5e08: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbf5e08: ldur            w2, [x0, #0x17]
    // 0xbf5e0c: DecompressPointer r2
    //     0xbf5e0c: add             x2, x2, HEAP, lsl #32
    // 0xbf5e10: mov             x0, x2
    // 0xbf5e14: r2 = LoadClassIdInstr(r0)
    //     0xbf5e14: ldur            x2, [x0, #-1]
    //     0xbf5e18: ubfx            x2, x2, #0xc, #0x14
    // 0xbf5e1c: r16 = "size"
    //     0xbf5e1c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbf5e20: ldr             x16, [x16, #0x9c0]
    // 0xbf5e24: stp             x16, x0, [SP]
    // 0xbf5e28: mov             x0, x2
    // 0xbf5e2c: mov             lr, x0
    // 0xbf5e30: ldr             lr, [x21, lr, lsl #3]
    // 0xbf5e34: blr             lr
    // 0xbf5e38: tbnz            w0, #4, #0xbf5f08
    // 0xbf5e3c: ldur            x0, [fp, #-8]
    // 0xbf5e40: r1 = Null
    //     0xbf5e40: mov             x1, NULL
    // 0xbf5e44: r2 = 8
    //     0xbf5e44: movz            x2, #0x8
    // 0xbf5e48: r0 = AllocateArray()
    //     0xbf5e48: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf5e4c: r16 = "Size: "
    //     0xbf5e4c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xbf5e50: ldr             x16, [x16, #0xf00]
    // 0xbf5e54: StoreField: r0->field_f = r16
    //     0xbf5e54: stur            w16, [x0, #0xf]
    // 0xbf5e58: ldur            x1, [fp, #-8]
    // 0xbf5e5c: LoadField: r2 = r1->field_b
    //     0xbf5e5c: ldur            w2, [x1, #0xb]
    // 0xbf5e60: DecompressPointer r2
    //     0xbf5e60: add             x2, x2, HEAP, lsl #32
    // 0xbf5e64: cmp             w2, NULL
    // 0xbf5e68: b.eq            #0xbf6e44
    // 0xbf5e6c: LoadField: r3 = r2->field_23
    //     0xbf5e6c: ldur            w3, [x2, #0x23]
    // 0xbf5e70: DecompressPointer r3
    //     0xbf5e70: add             x3, x3, HEAP, lsl #32
    // 0xbf5e74: cmp             w3, NULL
    // 0xbf5e78: b.ne            #0xbf5e84
    // 0xbf5e7c: r2 = Null
    //     0xbf5e7c: mov             x2, NULL
    // 0xbf5e80: b               #0xbf5ea8
    // 0xbf5e84: LoadField: r2 = r3->field_37
    //     0xbf5e84: ldur            w2, [x3, #0x37]
    // 0xbf5e88: DecompressPointer r2
    //     0xbf5e88: add             x2, x2, HEAP, lsl #32
    // 0xbf5e8c: cmp             w2, NULL
    // 0xbf5e90: b.ne            #0xbf5e9c
    // 0xbf5e94: r2 = Null
    //     0xbf5e94: mov             x2, NULL
    // 0xbf5e98: b               #0xbf5ea8
    // 0xbf5e9c: LoadField: r4 = r2->field_f
    //     0xbf5e9c: ldur            w4, [x2, #0xf]
    // 0xbf5ea0: DecompressPointer r4
    //     0xbf5ea0: add             x4, x4, HEAP, lsl #32
    // 0xbf5ea4: mov             x2, x4
    // 0xbf5ea8: cmp             w2, NULL
    // 0xbf5eac: b.ne            #0xbf5eb4
    // 0xbf5eb0: r2 = ""
    //     0xbf5eb0: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5eb4: StoreField: r0->field_13 = r2
    //     0xbf5eb4: stur            w2, [x0, #0x13]
    // 0xbf5eb8: r16 = " / Qty: "
    //     0xbf5eb8: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbf5ebc: ldr             x16, [x16, #0x760]
    // 0xbf5ec0: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf5ec0: stur            w16, [x0, #0x17]
    // 0xbf5ec4: cmp             w3, NULL
    // 0xbf5ec8: b.ne            #0xbf5ed4
    // 0xbf5ecc: r2 = Null
    //     0xbf5ecc: mov             x2, NULL
    // 0xbf5ed0: b               #0xbf5ef8
    // 0xbf5ed4: LoadField: r2 = r3->field_37
    //     0xbf5ed4: ldur            w2, [x3, #0x37]
    // 0xbf5ed8: DecompressPointer r2
    //     0xbf5ed8: add             x2, x2, HEAP, lsl #32
    // 0xbf5edc: cmp             w2, NULL
    // 0xbf5ee0: b.ne            #0xbf5eec
    // 0xbf5ee4: r2 = Null
    //     0xbf5ee4: mov             x2, NULL
    // 0xbf5ee8: b               #0xbf5ef8
    // 0xbf5eec: LoadField: r3 = r2->field_13
    //     0xbf5eec: ldur            w3, [x2, #0x13]
    // 0xbf5ef0: DecompressPointer r3
    //     0xbf5ef0: add             x3, x3, HEAP, lsl #32
    // 0xbf5ef4: mov             x2, x3
    // 0xbf5ef8: StoreField: r0->field_1b = r2
    //     0xbf5ef8: stur            w2, [x0, #0x1b]
    // 0xbf5efc: str             x0, [SP]
    // 0xbf5f00: r0 = _interpolate()
    //     0xbf5f00: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf5f04: b               #0xbf5fd0
    // 0xbf5f08: ldur            x0, [fp, #-8]
    // 0xbf5f0c: r1 = Null
    //     0xbf5f0c: mov             x1, NULL
    // 0xbf5f10: r2 = 8
    //     0xbf5f10: movz            x2, #0x8
    // 0xbf5f14: r0 = AllocateArray()
    //     0xbf5f14: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf5f18: r16 = "Variant: "
    //     0xbf5f18: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xbf5f1c: ldr             x16, [x16, #0xf08]
    // 0xbf5f20: StoreField: r0->field_f = r16
    //     0xbf5f20: stur            w16, [x0, #0xf]
    // 0xbf5f24: ldur            x1, [fp, #-8]
    // 0xbf5f28: LoadField: r2 = r1->field_b
    //     0xbf5f28: ldur            w2, [x1, #0xb]
    // 0xbf5f2c: DecompressPointer r2
    //     0xbf5f2c: add             x2, x2, HEAP, lsl #32
    // 0xbf5f30: cmp             w2, NULL
    // 0xbf5f34: b.eq            #0xbf6e48
    // 0xbf5f38: LoadField: r3 = r2->field_23
    //     0xbf5f38: ldur            w3, [x2, #0x23]
    // 0xbf5f3c: DecompressPointer r3
    //     0xbf5f3c: add             x3, x3, HEAP, lsl #32
    // 0xbf5f40: cmp             w3, NULL
    // 0xbf5f44: b.ne            #0xbf5f50
    // 0xbf5f48: r2 = Null
    //     0xbf5f48: mov             x2, NULL
    // 0xbf5f4c: b               #0xbf5f74
    // 0xbf5f50: LoadField: r2 = r3->field_37
    //     0xbf5f50: ldur            w2, [x3, #0x37]
    // 0xbf5f54: DecompressPointer r2
    //     0xbf5f54: add             x2, x2, HEAP, lsl #32
    // 0xbf5f58: cmp             w2, NULL
    // 0xbf5f5c: b.ne            #0xbf5f68
    // 0xbf5f60: r2 = Null
    //     0xbf5f60: mov             x2, NULL
    // 0xbf5f64: b               #0xbf5f74
    // 0xbf5f68: LoadField: r4 = r2->field_f
    //     0xbf5f68: ldur            w4, [x2, #0xf]
    // 0xbf5f6c: DecompressPointer r4
    //     0xbf5f6c: add             x4, x4, HEAP, lsl #32
    // 0xbf5f70: mov             x2, x4
    // 0xbf5f74: cmp             w2, NULL
    // 0xbf5f78: b.ne            #0xbf5f80
    // 0xbf5f7c: r2 = ""
    //     0xbf5f7c: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf5f80: StoreField: r0->field_13 = r2
    //     0xbf5f80: stur            w2, [x0, #0x13]
    // 0xbf5f84: r16 = " / Qty: "
    //     0xbf5f84: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbf5f88: ldr             x16, [x16, #0x760]
    // 0xbf5f8c: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf5f8c: stur            w16, [x0, #0x17]
    // 0xbf5f90: cmp             w3, NULL
    // 0xbf5f94: b.ne            #0xbf5fa0
    // 0xbf5f98: r2 = Null
    //     0xbf5f98: mov             x2, NULL
    // 0xbf5f9c: b               #0xbf5fc4
    // 0xbf5fa0: LoadField: r2 = r3->field_37
    //     0xbf5fa0: ldur            w2, [x3, #0x37]
    // 0xbf5fa4: DecompressPointer r2
    //     0xbf5fa4: add             x2, x2, HEAP, lsl #32
    // 0xbf5fa8: cmp             w2, NULL
    // 0xbf5fac: b.ne            #0xbf5fb8
    // 0xbf5fb0: r2 = Null
    //     0xbf5fb0: mov             x2, NULL
    // 0xbf5fb4: b               #0xbf5fc4
    // 0xbf5fb8: LoadField: r3 = r2->field_13
    //     0xbf5fb8: ldur            w3, [x2, #0x13]
    // 0xbf5fbc: DecompressPointer r3
    //     0xbf5fbc: add             x3, x3, HEAP, lsl #32
    // 0xbf5fc0: mov             x2, x3
    // 0xbf5fc4: StoreField: r0->field_1b = r2
    //     0xbf5fc4: stur            w2, [x0, #0x1b]
    // 0xbf5fc8: str             x0, [SP]
    // 0xbf5fcc: r0 = _interpolate()
    //     0xbf5fcc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf5fd0: mov             x2, x0
    // 0xbf5fd4: b               #0xbf6310
    // 0xbf5fd8: ldur            x0, [fp, #-8]
    // 0xbf5fdc: LoadField: r1 = r0->field_b
    //     0xbf5fdc: ldur            w1, [x0, #0xb]
    // 0xbf5fe0: DecompressPointer r1
    //     0xbf5fe0: add             x1, x1, HEAP, lsl #32
    // 0xbf5fe4: cmp             w1, NULL
    // 0xbf5fe8: b.eq            #0xbf6e4c
    // 0xbf5fec: LoadField: r2 = r1->field_f
    //     0xbf5fec: ldur            w2, [x1, #0xf]
    // 0xbf5ff0: DecompressPointer r2
    //     0xbf5ff0: add             x2, x2, HEAP, lsl #32
    // 0xbf5ff4: r16 = "cancel_order"
    //     0xbf5ff4: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf5ff8: ldr             x16, [x16, #0x98]
    // 0xbf5ffc: stp             x16, x2, [SP]
    // 0xbf6000: r0 = ==()
    //     0xbf6000: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf6004: tbnz            w0, #4, #0xbf618c
    // 0xbf6008: ldur            x1, [fp, #-8]
    // 0xbf600c: LoadField: r0 = r1->field_b
    //     0xbf600c: ldur            w0, [x1, #0xb]
    // 0xbf6010: DecompressPointer r0
    //     0xbf6010: add             x0, x0, HEAP, lsl #32
    // 0xbf6014: cmp             w0, NULL
    // 0xbf6018: b.eq            #0xbf6e50
    // 0xbf601c: LoadField: r2 = r0->field_1b
    //     0xbf601c: ldur            w2, [x0, #0x1b]
    // 0xbf6020: DecompressPointer r2
    //     0xbf6020: add             x2, x2, HEAP, lsl #32
    // 0xbf6024: cmp             w2, NULL
    // 0xbf6028: b.ne            #0xbf6034
    // 0xbf602c: r0 = Null
    //     0xbf602c: mov             x0, NULL
    // 0xbf6030: b               #0xbf603c
    // 0xbf6034: LoadField: r0 = r2->field_47
    //     0xbf6034: ldur            w0, [x2, #0x47]
    // 0xbf6038: DecompressPointer r0
    //     0xbf6038: add             x0, x0, HEAP, lsl #32
    // 0xbf603c: r2 = LoadClassIdInstr(r0)
    //     0xbf603c: ldur            x2, [x0, #-1]
    //     0xbf6040: ubfx            x2, x2, #0xc, #0x14
    // 0xbf6044: r16 = "size"
    //     0xbf6044: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbf6048: ldr             x16, [x16, #0x9c0]
    // 0xbf604c: stp             x16, x0, [SP]
    // 0xbf6050: mov             x0, x2
    // 0xbf6054: mov             lr, x0
    // 0xbf6058: ldr             lr, [x21, lr, lsl #3]
    // 0xbf605c: blr             lr
    // 0xbf6060: tbnz            w0, #4, #0xbf60f8
    // 0xbf6064: ldur            x0, [fp, #-8]
    // 0xbf6068: r1 = Null
    //     0xbf6068: mov             x1, NULL
    // 0xbf606c: r2 = 8
    //     0xbf606c: movz            x2, #0x8
    // 0xbf6070: r0 = AllocateArray()
    //     0xbf6070: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf6074: r16 = "Size: "
    //     0xbf6074: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xbf6078: ldr             x16, [x16, #0xf00]
    // 0xbf607c: StoreField: r0->field_f = r16
    //     0xbf607c: stur            w16, [x0, #0xf]
    // 0xbf6080: ldur            x1, [fp, #-8]
    // 0xbf6084: LoadField: r2 = r1->field_b
    //     0xbf6084: ldur            w2, [x1, #0xb]
    // 0xbf6088: DecompressPointer r2
    //     0xbf6088: add             x2, x2, HEAP, lsl #32
    // 0xbf608c: cmp             w2, NULL
    // 0xbf6090: b.eq            #0xbf6e54
    // 0xbf6094: LoadField: r3 = r2->field_1b
    //     0xbf6094: ldur            w3, [x2, #0x1b]
    // 0xbf6098: DecompressPointer r3
    //     0xbf6098: add             x3, x3, HEAP, lsl #32
    // 0xbf609c: cmp             w3, NULL
    // 0xbf60a0: b.ne            #0xbf60ac
    // 0xbf60a4: r2 = Null
    //     0xbf60a4: mov             x2, NULL
    // 0xbf60a8: b               #0xbf60b4
    // 0xbf60ac: LoadField: r2 = r3->field_1f
    //     0xbf60ac: ldur            w2, [x3, #0x1f]
    // 0xbf60b0: DecompressPointer r2
    //     0xbf60b0: add             x2, x2, HEAP, lsl #32
    // 0xbf60b4: cmp             w2, NULL
    // 0xbf60b8: b.ne            #0xbf60c0
    // 0xbf60bc: r2 = ""
    //     0xbf60bc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf60c0: StoreField: r0->field_13 = r2
    //     0xbf60c0: stur            w2, [x0, #0x13]
    // 0xbf60c4: r16 = " / Qty: "
    //     0xbf60c4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbf60c8: ldr             x16, [x16, #0x760]
    // 0xbf60cc: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf60cc: stur            w16, [x0, #0x17]
    // 0xbf60d0: cmp             w3, NULL
    // 0xbf60d4: b.ne            #0xbf60e0
    // 0xbf60d8: r2 = Null
    //     0xbf60d8: mov             x2, NULL
    // 0xbf60dc: b               #0xbf60e8
    // 0xbf60e0: LoadField: r2 = r3->field_23
    //     0xbf60e0: ldur            w2, [x3, #0x23]
    // 0xbf60e4: DecompressPointer r2
    //     0xbf60e4: add             x2, x2, HEAP, lsl #32
    // 0xbf60e8: StoreField: r0->field_1b = r2
    //     0xbf60e8: stur            w2, [x0, #0x1b]
    // 0xbf60ec: str             x0, [SP]
    // 0xbf60f0: r0 = _interpolate()
    //     0xbf60f0: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf60f4: b               #0xbf630c
    // 0xbf60f8: ldur            x0, [fp, #-8]
    // 0xbf60fc: r1 = Null
    //     0xbf60fc: mov             x1, NULL
    // 0xbf6100: r2 = 8
    //     0xbf6100: movz            x2, #0x8
    // 0xbf6104: r0 = AllocateArray()
    //     0xbf6104: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf6108: r16 = "Variant: "
    //     0xbf6108: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xbf610c: ldr             x16, [x16, #0xf08]
    // 0xbf6110: StoreField: r0->field_f = r16
    //     0xbf6110: stur            w16, [x0, #0xf]
    // 0xbf6114: ldur            x1, [fp, #-8]
    // 0xbf6118: LoadField: r2 = r1->field_b
    //     0xbf6118: ldur            w2, [x1, #0xb]
    // 0xbf611c: DecompressPointer r2
    //     0xbf611c: add             x2, x2, HEAP, lsl #32
    // 0xbf6120: cmp             w2, NULL
    // 0xbf6124: b.eq            #0xbf6e58
    // 0xbf6128: LoadField: r3 = r2->field_1b
    //     0xbf6128: ldur            w3, [x2, #0x1b]
    // 0xbf612c: DecompressPointer r3
    //     0xbf612c: add             x3, x3, HEAP, lsl #32
    // 0xbf6130: cmp             w3, NULL
    // 0xbf6134: b.ne            #0xbf6140
    // 0xbf6138: r2 = Null
    //     0xbf6138: mov             x2, NULL
    // 0xbf613c: b               #0xbf6148
    // 0xbf6140: LoadField: r2 = r3->field_1f
    //     0xbf6140: ldur            w2, [x3, #0x1f]
    // 0xbf6144: DecompressPointer r2
    //     0xbf6144: add             x2, x2, HEAP, lsl #32
    // 0xbf6148: cmp             w2, NULL
    // 0xbf614c: b.ne            #0xbf6154
    // 0xbf6150: r2 = ""
    //     0xbf6150: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf6154: StoreField: r0->field_13 = r2
    //     0xbf6154: stur            w2, [x0, #0x13]
    // 0xbf6158: r16 = " / Qty: "
    //     0xbf6158: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbf615c: ldr             x16, [x16, #0x760]
    // 0xbf6160: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf6160: stur            w16, [x0, #0x17]
    // 0xbf6164: cmp             w3, NULL
    // 0xbf6168: b.ne            #0xbf6174
    // 0xbf616c: r2 = Null
    //     0xbf616c: mov             x2, NULL
    // 0xbf6170: b               #0xbf617c
    // 0xbf6174: LoadField: r2 = r3->field_23
    //     0xbf6174: ldur            w2, [x3, #0x23]
    // 0xbf6178: DecompressPointer r2
    //     0xbf6178: add             x2, x2, HEAP, lsl #32
    // 0xbf617c: StoreField: r0->field_1b = r2
    //     0xbf617c: stur            w2, [x0, #0x1b]
    // 0xbf6180: str             x0, [SP]
    // 0xbf6184: r0 = _interpolate()
    //     0xbf6184: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf6188: b               #0xbf630c
    // 0xbf618c: ldur            x1, [fp, #-8]
    // 0xbf6190: LoadField: r0 = r1->field_b
    //     0xbf6190: ldur            w0, [x1, #0xb]
    // 0xbf6194: DecompressPointer r0
    //     0xbf6194: add             x0, x0, HEAP, lsl #32
    // 0xbf6198: cmp             w0, NULL
    // 0xbf619c: b.eq            #0xbf6e5c
    // 0xbf61a0: LoadField: r2 = r0->field_1f
    //     0xbf61a0: ldur            w2, [x0, #0x1f]
    // 0xbf61a4: DecompressPointer r2
    //     0xbf61a4: add             x2, x2, HEAP, lsl #32
    // 0xbf61a8: cmp             w2, NULL
    // 0xbf61ac: b.ne            #0xbf61b8
    // 0xbf61b0: r0 = Null
    //     0xbf61b0: mov             x0, NULL
    // 0xbf61b4: b               #0xbf61c0
    // 0xbf61b8: LoadField: r0 = r2->field_7f
    //     0xbf61b8: ldur            w0, [x2, #0x7f]
    // 0xbf61bc: DecompressPointer r0
    //     0xbf61bc: add             x0, x0, HEAP, lsl #32
    // 0xbf61c0: r2 = LoadClassIdInstr(r0)
    //     0xbf61c0: ldur            x2, [x0, #-1]
    //     0xbf61c4: ubfx            x2, x2, #0xc, #0x14
    // 0xbf61c8: r16 = "size"
    //     0xbf61c8: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0xbf61cc: ldr             x16, [x16, #0x9c0]
    // 0xbf61d0: stp             x16, x0, [SP]
    // 0xbf61d4: mov             x0, x2
    // 0xbf61d8: mov             lr, x0
    // 0xbf61dc: ldr             lr, [x21, lr, lsl #3]
    // 0xbf61e0: blr             lr
    // 0xbf61e4: tbnz            w0, #4, #0xbf627c
    // 0xbf61e8: ldur            x0, [fp, #-8]
    // 0xbf61ec: r1 = Null
    //     0xbf61ec: mov             x1, NULL
    // 0xbf61f0: r2 = 8
    //     0xbf61f0: movz            x2, #0x8
    // 0xbf61f4: r0 = AllocateArray()
    //     0xbf61f4: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf61f8: r16 = "Size: "
    //     0xbf61f8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0xbf61fc: ldr             x16, [x16, #0xf00]
    // 0xbf6200: StoreField: r0->field_f = r16
    //     0xbf6200: stur            w16, [x0, #0xf]
    // 0xbf6204: ldur            x1, [fp, #-8]
    // 0xbf6208: LoadField: r2 = r1->field_b
    //     0xbf6208: ldur            w2, [x1, #0xb]
    // 0xbf620c: DecompressPointer r2
    //     0xbf620c: add             x2, x2, HEAP, lsl #32
    // 0xbf6210: cmp             w2, NULL
    // 0xbf6214: b.eq            #0xbf6e60
    // 0xbf6218: LoadField: r3 = r2->field_1f
    //     0xbf6218: ldur            w3, [x2, #0x1f]
    // 0xbf621c: DecompressPointer r3
    //     0xbf621c: add             x3, x3, HEAP, lsl #32
    // 0xbf6220: cmp             w3, NULL
    // 0xbf6224: b.ne            #0xbf6230
    // 0xbf6228: r2 = Null
    //     0xbf6228: mov             x2, NULL
    // 0xbf622c: b               #0xbf6238
    // 0xbf6230: LoadField: r2 = r3->field_7
    //     0xbf6230: ldur            w2, [x3, #7]
    // 0xbf6234: DecompressPointer r2
    //     0xbf6234: add             x2, x2, HEAP, lsl #32
    // 0xbf6238: cmp             w2, NULL
    // 0xbf623c: b.ne            #0xbf6244
    // 0xbf6240: r2 = ""
    //     0xbf6240: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf6244: StoreField: r0->field_13 = r2
    //     0xbf6244: stur            w2, [x0, #0x13]
    // 0xbf6248: r16 = " / Qty: "
    //     0xbf6248: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbf624c: ldr             x16, [x16, #0x760]
    // 0xbf6250: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf6250: stur            w16, [x0, #0x17]
    // 0xbf6254: cmp             w3, NULL
    // 0xbf6258: b.ne            #0xbf6264
    // 0xbf625c: r2 = Null
    //     0xbf625c: mov             x2, NULL
    // 0xbf6260: b               #0xbf626c
    // 0xbf6264: LoadField: r2 = r3->field_f
    //     0xbf6264: ldur            w2, [x3, #0xf]
    // 0xbf6268: DecompressPointer r2
    //     0xbf6268: add             x2, x2, HEAP, lsl #32
    // 0xbf626c: StoreField: r0->field_1b = r2
    //     0xbf626c: stur            w2, [x0, #0x1b]
    // 0xbf6270: str             x0, [SP]
    // 0xbf6274: r0 = _interpolate()
    //     0xbf6274: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf6278: b               #0xbf630c
    // 0xbf627c: ldur            x0, [fp, #-8]
    // 0xbf6280: r1 = Null
    //     0xbf6280: mov             x1, NULL
    // 0xbf6284: r2 = 8
    //     0xbf6284: movz            x2, #0x8
    // 0xbf6288: r0 = AllocateArray()
    //     0xbf6288: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf628c: r16 = "Variant: "
    //     0xbf628c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0xbf6290: ldr             x16, [x16, #0xf08]
    // 0xbf6294: StoreField: r0->field_f = r16
    //     0xbf6294: stur            w16, [x0, #0xf]
    // 0xbf6298: ldur            x1, [fp, #-8]
    // 0xbf629c: LoadField: r2 = r1->field_b
    //     0xbf629c: ldur            w2, [x1, #0xb]
    // 0xbf62a0: DecompressPointer r2
    //     0xbf62a0: add             x2, x2, HEAP, lsl #32
    // 0xbf62a4: cmp             w2, NULL
    // 0xbf62a8: b.eq            #0xbf6e64
    // 0xbf62ac: LoadField: r3 = r2->field_1f
    //     0xbf62ac: ldur            w3, [x2, #0x1f]
    // 0xbf62b0: DecompressPointer r3
    //     0xbf62b0: add             x3, x3, HEAP, lsl #32
    // 0xbf62b4: cmp             w3, NULL
    // 0xbf62b8: b.ne            #0xbf62c4
    // 0xbf62bc: r2 = Null
    //     0xbf62bc: mov             x2, NULL
    // 0xbf62c0: b               #0xbf62cc
    // 0xbf62c4: LoadField: r2 = r3->field_7
    //     0xbf62c4: ldur            w2, [x3, #7]
    // 0xbf62c8: DecompressPointer r2
    //     0xbf62c8: add             x2, x2, HEAP, lsl #32
    // 0xbf62cc: cmp             w2, NULL
    // 0xbf62d0: b.ne            #0xbf62d8
    // 0xbf62d4: r2 = ""
    //     0xbf62d4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf62d8: StoreField: r0->field_13 = r2
    //     0xbf62d8: stur            w2, [x0, #0x13]
    // 0xbf62dc: r16 = " / Qty: "
    //     0xbf62dc: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0xbf62e0: ldr             x16, [x16, #0x760]
    // 0xbf62e4: ArrayStore: r0[0] = r16  ; List_4
    //     0xbf62e4: stur            w16, [x0, #0x17]
    // 0xbf62e8: cmp             w3, NULL
    // 0xbf62ec: b.ne            #0xbf62f8
    // 0xbf62f0: r2 = Null
    //     0xbf62f0: mov             x2, NULL
    // 0xbf62f4: b               #0xbf6300
    // 0xbf62f8: LoadField: r2 = r3->field_f
    //     0xbf62f8: ldur            w2, [x3, #0xf]
    // 0xbf62fc: DecompressPointer r2
    //     0xbf62fc: add             x2, x2, HEAP, lsl #32
    // 0xbf6300: StoreField: r0->field_1b = r2
    //     0xbf6300: stur            w2, [x0, #0x1b]
    // 0xbf6304: str             x0, [SP]
    // 0xbf6308: r0 = _interpolate()
    //     0xbf6308: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbf630c: mov             x2, x0
    // 0xbf6310: ldur            x0, [fp, #-8]
    // 0xbf6314: ldur            x1, [fp, #-0x10]
    // 0xbf6318: stur            x2, [fp, #-0x28]
    // 0xbf631c: r0 = of()
    //     0xbf631c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf6320: LoadField: r1 = r0->field_87
    //     0xbf6320: ldur            w1, [x0, #0x87]
    // 0xbf6324: DecompressPointer r1
    //     0xbf6324: add             x1, x1, HEAP, lsl #32
    // 0xbf6328: LoadField: r0 = r1->field_2b
    //     0xbf6328: ldur            w0, [x1, #0x2b]
    // 0xbf632c: DecompressPointer r0
    //     0xbf632c: add             x0, x0, HEAP, lsl #32
    // 0xbf6330: r16 = 12.000000
    //     0xbf6330: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf6334: ldr             x16, [x16, #0x9e8]
    // 0xbf6338: r30 = Instance_Color
    //     0xbf6338: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf633c: stp             lr, x16, [SP]
    // 0xbf6340: mov             x1, x0
    // 0xbf6344: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf6344: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf6348: ldr             x4, [x4, #0xaa0]
    // 0xbf634c: r0 = copyWith()
    //     0xbf634c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf6350: stur            x0, [fp, #-0x48]
    // 0xbf6354: r0 = Text()
    //     0xbf6354: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf6358: mov             x1, x0
    // 0xbf635c: ldur            x0, [fp, #-0x28]
    // 0xbf6360: stur            x1, [fp, #-0x58]
    // 0xbf6364: StoreField: r1->field_b = r0
    //     0xbf6364: stur            w0, [x1, #0xb]
    // 0xbf6368: ldur            x0, [fp, #-0x48]
    // 0xbf636c: StoreField: r1->field_13 = r0
    //     0xbf636c: stur            w0, [x1, #0x13]
    // 0xbf6370: r0 = Padding()
    //     0xbf6370: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf6374: mov             x1, x0
    // 0xbf6378: r0 = Instance_EdgeInsets
    //     0xbf6378: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0xbf637c: ldr             x0, [x0, #0x770]
    // 0xbf6380: stur            x1, [fp, #-0x28]
    // 0xbf6384: StoreField: r1->field_f = r0
    //     0xbf6384: stur            w0, [x1, #0xf]
    // 0xbf6388: ldur            x0, [fp, #-0x58]
    // 0xbf638c: StoreField: r1->field_b = r0
    //     0xbf638c: stur            w0, [x1, #0xb]
    // 0xbf6390: ldur            x0, [fp, #-8]
    // 0xbf6394: LoadField: r2 = r0->field_b
    //     0xbf6394: ldur            w2, [x0, #0xb]
    // 0xbf6398: DecompressPointer r2
    //     0xbf6398: add             x2, x2, HEAP, lsl #32
    // 0xbf639c: cmp             w2, NULL
    // 0xbf63a0: b.eq            #0xbf6e68
    // 0xbf63a4: LoadField: r3 = r2->field_f
    //     0xbf63a4: ldur            w3, [x2, #0xf]
    // 0xbf63a8: DecompressPointer r3
    //     0xbf63a8: add             x3, x3, HEAP, lsl #32
    // 0xbf63ac: r16 = "return_order_intermediate"
    //     0xbf63ac: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b00] "return_order_intermediate"
    //     0xbf63b0: ldr             x16, [x16, #0xb00]
    // 0xbf63b4: stp             x16, x3, [SP]
    // 0xbf63b8: r0 = ==()
    //     0xbf63b8: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf63bc: tbnz            w0, #4, #0xbf643c
    // 0xbf63c0: ldur            x0, [fp, #-8]
    // 0xbf63c4: LoadField: r1 = r0->field_b
    //     0xbf63c4: ldur            w1, [x0, #0xb]
    // 0xbf63c8: DecompressPointer r1
    //     0xbf63c8: add             x1, x1, HEAP, lsl #32
    // 0xbf63cc: cmp             w1, NULL
    // 0xbf63d0: b.eq            #0xbf6e6c
    // 0xbf63d4: LoadField: r0 = r1->field_23
    //     0xbf63d4: ldur            w0, [x1, #0x23]
    // 0xbf63d8: DecompressPointer r0
    //     0xbf63d8: add             x0, x0, HEAP, lsl #32
    // 0xbf63dc: cmp             w0, NULL
    // 0xbf63e0: b.ne            #0xbf63ec
    // 0xbf63e4: r0 = Null
    //     0xbf63e4: mov             x0, NULL
    // 0xbf63e8: b               #0xbf6428
    // 0xbf63ec: LoadField: r1 = r0->field_37
    //     0xbf63ec: ldur            w1, [x0, #0x37]
    // 0xbf63f0: DecompressPointer r1
    //     0xbf63f0: add             x1, x1, HEAP, lsl #32
    // 0xbf63f4: cmp             w1, NULL
    // 0xbf63f8: b.ne            #0xbf6404
    // 0xbf63fc: r0 = Null
    //     0xbf63fc: mov             x0, NULL
    // 0xbf6400: b               #0xbf6428
    // 0xbf6404: LoadField: r0 = r1->field_1f
    //     0xbf6404: ldur            w0, [x1, #0x1f]
    // 0xbf6408: DecompressPointer r0
    //     0xbf6408: add             x0, x0, HEAP, lsl #32
    // 0xbf640c: cmp             w0, NULL
    // 0xbf6410: b.ne            #0xbf641c
    // 0xbf6414: r0 = Null
    //     0xbf6414: mov             x0, NULL
    // 0xbf6418: b               #0xbf6428
    // 0xbf641c: LoadField: r1 = r0->field_7
    //     0xbf641c: ldur            w1, [x0, #7]
    // 0xbf6420: DecompressPointer r1
    //     0xbf6420: add             x1, x1, HEAP, lsl #32
    // 0xbf6424: mov             x0, x1
    // 0xbf6428: cmp             w0, NULL
    // 0xbf642c: b.ne            #0xbf6434
    // 0xbf6430: r0 = ""
    //     0xbf6430: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf6434: mov             x7, x0
    // 0xbf6438: b               #0xbf64fc
    // 0xbf643c: ldur            x0, [fp, #-8]
    // 0xbf6440: LoadField: r1 = r0->field_b
    //     0xbf6440: ldur            w1, [x0, #0xb]
    // 0xbf6444: DecompressPointer r1
    //     0xbf6444: add             x1, x1, HEAP, lsl #32
    // 0xbf6448: cmp             w1, NULL
    // 0xbf644c: b.eq            #0xbf6e70
    // 0xbf6450: LoadField: r2 = r1->field_f
    //     0xbf6450: ldur            w2, [x1, #0xf]
    // 0xbf6454: DecompressPointer r2
    //     0xbf6454: add             x2, x2, HEAP, lsl #32
    // 0xbf6458: r16 = "cancel_order"
    //     0xbf6458: add             x16, PP, #0x36, lsl #12  ; [pp+0x36098] "cancel_order"
    //     0xbf645c: ldr             x16, [x16, #0x98]
    // 0xbf6460: stp             x16, x2, [SP]
    // 0xbf6464: r0 = ==()
    //     0xbf6464: bl              #0x169e5e8  ; [dart:core] _OneByteString::==
    // 0xbf6468: tbnz            w0, #4, #0xbf64b4
    // 0xbf646c: ldur            x0, [fp, #-8]
    // 0xbf6470: LoadField: r1 = r0->field_b
    //     0xbf6470: ldur            w1, [x0, #0xb]
    // 0xbf6474: DecompressPointer r1
    //     0xbf6474: add             x1, x1, HEAP, lsl #32
    // 0xbf6478: cmp             w1, NULL
    // 0xbf647c: b.eq            #0xbf6e74
    // 0xbf6480: LoadField: r0 = r1->field_1b
    //     0xbf6480: ldur            w0, [x1, #0x1b]
    // 0xbf6484: DecompressPointer r0
    //     0xbf6484: add             x0, x0, HEAP, lsl #32
    // 0xbf6488: cmp             w0, NULL
    // 0xbf648c: b.ne            #0xbf6498
    // 0xbf6490: r0 = Null
    //     0xbf6490: mov             x0, NULL
    // 0xbf6494: b               #0xbf64a4
    // 0xbf6498: LoadField: r1 = r0->field_4b
    //     0xbf6498: ldur            w1, [x0, #0x4b]
    // 0xbf649c: DecompressPointer r1
    //     0xbf649c: add             x1, x1, HEAP, lsl #32
    // 0xbf64a0: mov             x0, x1
    // 0xbf64a4: cmp             w0, NULL
    // 0xbf64a8: b.ne            #0xbf64f8
    // 0xbf64ac: r0 = ""
    //     0xbf64ac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf64b0: b               #0xbf64f8
    // 0xbf64b4: ldur            x0, [fp, #-8]
    // 0xbf64b8: LoadField: r1 = r0->field_b
    //     0xbf64b8: ldur            w1, [x0, #0xb]
    // 0xbf64bc: DecompressPointer r1
    //     0xbf64bc: add             x1, x1, HEAP, lsl #32
    // 0xbf64c0: cmp             w1, NULL
    // 0xbf64c4: b.eq            #0xbf6e78
    // 0xbf64c8: LoadField: r0 = r1->field_1f
    //     0xbf64c8: ldur            w0, [x1, #0x1f]
    // 0xbf64cc: DecompressPointer r0
    //     0xbf64cc: add             x0, x0, HEAP, lsl #32
    // 0xbf64d0: cmp             w0, NULL
    // 0xbf64d4: b.ne            #0xbf64e0
    // 0xbf64d8: r0 = Null
    //     0xbf64d8: mov             x0, NULL
    // 0xbf64dc: b               #0xbf64ec
    // 0xbf64e0: LoadField: r1 = r0->field_13
    //     0xbf64e0: ldur            w1, [x0, #0x13]
    // 0xbf64e4: DecompressPointer r1
    //     0xbf64e4: add             x1, x1, HEAP, lsl #32
    // 0xbf64e8: mov             x0, x1
    // 0xbf64ec: cmp             w0, NULL
    // 0xbf64f0: b.ne            #0xbf64f8
    // 0xbf64f4: r0 = ""
    //     0xbf64f4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbf64f8: mov             x7, x0
    // 0xbf64fc: ldur            x6, [fp, #-0x30]
    // 0xbf6500: ldur            x5, [fp, #-0x18]
    // 0xbf6504: ldur            x4, [fp, #-0x38]
    // 0xbf6508: ldur            x3, [fp, #-0x60]
    // 0xbf650c: ldur            x2, [fp, #-0x50]
    // 0xbf6510: ldur            x0, [fp, #-0x28]
    // 0xbf6514: ldur            x1, [fp, #-0x10]
    // 0xbf6518: stur            x7, [fp, #-8]
    // 0xbf651c: r0 = of()
    //     0xbf651c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf6520: LoadField: r1 = r0->field_87
    //     0xbf6520: ldur            w1, [x0, #0x87]
    // 0xbf6524: DecompressPointer r1
    //     0xbf6524: add             x1, x1, HEAP, lsl #32
    // 0xbf6528: LoadField: r0 = r1->field_7
    //     0xbf6528: ldur            w0, [x1, #7]
    // 0xbf652c: DecompressPointer r0
    //     0xbf652c: add             x0, x0, HEAP, lsl #32
    // 0xbf6530: r16 = 12.000000
    //     0xbf6530: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbf6534: ldr             x16, [x16, #0x9e8]
    // 0xbf6538: r30 = Instance_Color
    //     0xbf6538: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbf653c: stp             lr, x16, [SP]
    // 0xbf6540: mov             x1, x0
    // 0xbf6544: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf6544: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf6548: ldr             x4, [x4, #0xaa0]
    // 0xbf654c: r0 = copyWith()
    //     0xbf654c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf6550: stur            x0, [fp, #-0x48]
    // 0xbf6554: r0 = Text()
    //     0xbf6554: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf6558: mov             x3, x0
    // 0xbf655c: ldur            x0, [fp, #-8]
    // 0xbf6560: stur            x3, [fp, #-0x58]
    // 0xbf6564: StoreField: r3->field_b = r0
    //     0xbf6564: stur            w0, [x3, #0xb]
    // 0xbf6568: ldur            x0, [fp, #-0x48]
    // 0xbf656c: StoreField: r3->field_13 = r0
    //     0xbf656c: stur            w0, [x3, #0x13]
    // 0xbf6570: r1 = Null
    //     0xbf6570: mov             x1, NULL
    // 0xbf6574: r2 = 8
    //     0xbf6574: movz            x2, #0x8
    // 0xbf6578: r0 = AllocateArray()
    //     0xbf6578: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf657c: mov             x2, x0
    // 0xbf6580: ldur            x0, [fp, #-0x50]
    // 0xbf6584: stur            x2, [fp, #-8]
    // 0xbf6588: StoreField: r2->field_f = r0
    //     0xbf6588: stur            w0, [x2, #0xf]
    // 0xbf658c: ldur            x0, [fp, #-0x28]
    // 0xbf6590: StoreField: r2->field_13 = r0
    //     0xbf6590: stur            w0, [x2, #0x13]
    // 0xbf6594: r16 = Instance_SizedBox
    //     0xbf6594: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xbf6598: ldr             x16, [x16, #0xc70]
    // 0xbf659c: ArrayStore: r2[0] = r16  ; List_4
    //     0xbf659c: stur            w16, [x2, #0x17]
    // 0xbf65a0: ldur            x0, [fp, #-0x58]
    // 0xbf65a4: StoreField: r2->field_1b = r0
    //     0xbf65a4: stur            w0, [x2, #0x1b]
    // 0xbf65a8: r1 = <Widget>
    //     0xbf65a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf65ac: r0 = AllocateGrowableArray()
    //     0xbf65ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf65b0: mov             x1, x0
    // 0xbf65b4: ldur            x0, [fp, #-8]
    // 0xbf65b8: stur            x1, [fp, #-0x28]
    // 0xbf65bc: StoreField: r1->field_f = r0
    //     0xbf65bc: stur            w0, [x1, #0xf]
    // 0xbf65c0: r0 = 8
    //     0xbf65c0: movz            x0, #0x8
    // 0xbf65c4: StoreField: r1->field_b = r0
    //     0xbf65c4: stur            w0, [x1, #0xb]
    // 0xbf65c8: r0 = Column()
    //     0xbf65c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf65cc: mov             x2, x0
    // 0xbf65d0: r0 = Instance_Axis
    //     0xbf65d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf65d4: stur            x2, [fp, #-8]
    // 0xbf65d8: StoreField: r2->field_f = r0
    //     0xbf65d8: stur            w0, [x2, #0xf]
    // 0xbf65dc: r3 = Instance_MainAxisAlignment
    //     0xbf65dc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf65e0: ldr             x3, [x3, #0xa08]
    // 0xbf65e4: StoreField: r2->field_13 = r3
    //     0xbf65e4: stur            w3, [x2, #0x13]
    // 0xbf65e8: r4 = Instance_MainAxisSize
    //     0xbf65e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf65ec: ldr             x4, [x4, #0xa10]
    // 0xbf65f0: ArrayStore: r2[0] = r4  ; List_4
    //     0xbf65f0: stur            w4, [x2, #0x17]
    // 0xbf65f4: r5 = Instance_CrossAxisAlignment
    //     0xbf65f4: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf65f8: ldr             x5, [x5, #0x890]
    // 0xbf65fc: StoreField: r2->field_1b = r5
    //     0xbf65fc: stur            w5, [x2, #0x1b]
    // 0xbf6600: r6 = Instance_VerticalDirection
    //     0xbf6600: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf6604: ldr             x6, [x6, #0xa20]
    // 0xbf6608: StoreField: r2->field_23 = r6
    //     0xbf6608: stur            w6, [x2, #0x23]
    // 0xbf660c: r7 = Instance_Clip
    //     0xbf660c: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf6610: ldr             x7, [x7, #0x38]
    // 0xbf6614: StoreField: r2->field_2b = r7
    //     0xbf6614: stur            w7, [x2, #0x2b]
    // 0xbf6618: StoreField: r2->field_2f = rZR
    //     0xbf6618: stur            xzr, [x2, #0x2f]
    // 0xbf661c: ldur            x1, [fp, #-0x28]
    // 0xbf6620: StoreField: r2->field_b = r1
    //     0xbf6620: stur            w1, [x2, #0xb]
    // 0xbf6624: r1 = <FlexParentData>
    //     0xbf6624: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbf6628: ldr             x1, [x1, #0xe00]
    // 0xbf662c: r0 = Expanded()
    //     0xbf662c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbf6630: mov             x3, x0
    // 0xbf6634: r0 = 1
    //     0xbf6634: movz            x0, #0x1
    // 0xbf6638: stur            x3, [fp, #-0x28]
    // 0xbf663c: StoreField: r3->field_13 = r0
    //     0xbf663c: stur            x0, [x3, #0x13]
    // 0xbf6640: r4 = Instance_FlexFit
    //     0xbf6640: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbf6644: ldr             x4, [x4, #0xe08]
    // 0xbf6648: StoreField: r3->field_1b = r4
    //     0xbf6648: stur            w4, [x3, #0x1b]
    // 0xbf664c: ldur            x1, [fp, #-8]
    // 0xbf6650: StoreField: r3->field_b = r1
    //     0xbf6650: stur            w1, [x3, #0xb]
    // 0xbf6654: r1 = Null
    //     0xbf6654: mov             x1, NULL
    // 0xbf6658: r2 = 6
    //     0xbf6658: movz            x2, #0x6
    // 0xbf665c: r0 = AllocateArray()
    //     0xbf665c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf6660: mov             x2, x0
    // 0xbf6664: ldur            x0, [fp, #-0x60]
    // 0xbf6668: stur            x2, [fp, #-8]
    // 0xbf666c: StoreField: r2->field_f = r0
    //     0xbf666c: stur            w0, [x2, #0xf]
    // 0xbf6670: r16 = Instance_SizedBox
    //     0xbf6670: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbf6674: ldr             x16, [x16, #0xb20]
    // 0xbf6678: StoreField: r2->field_13 = r16
    //     0xbf6678: stur            w16, [x2, #0x13]
    // 0xbf667c: ldur            x0, [fp, #-0x28]
    // 0xbf6680: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf6680: stur            w0, [x2, #0x17]
    // 0xbf6684: r1 = <Widget>
    //     0xbf6684: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf6688: r0 = AllocateGrowableArray()
    //     0xbf6688: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf668c: mov             x1, x0
    // 0xbf6690: ldur            x0, [fp, #-8]
    // 0xbf6694: stur            x1, [fp, #-0x28]
    // 0xbf6698: StoreField: r1->field_f = r0
    //     0xbf6698: stur            w0, [x1, #0xf]
    // 0xbf669c: r2 = 6
    //     0xbf669c: movz            x2, #0x6
    // 0xbf66a0: StoreField: r1->field_b = r2
    //     0xbf66a0: stur            w2, [x1, #0xb]
    // 0xbf66a4: r0 = Row()
    //     0xbf66a4: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf66a8: mov             x3, x0
    // 0xbf66ac: r0 = Instance_Axis
    //     0xbf66ac: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf66b0: stur            x3, [fp, #-8]
    // 0xbf66b4: StoreField: r3->field_f = r0
    //     0xbf66b4: stur            w0, [x3, #0xf]
    // 0xbf66b8: r4 = Instance_MainAxisAlignment
    //     0xbf66b8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf66bc: ldr             x4, [x4, #0xa08]
    // 0xbf66c0: StoreField: r3->field_13 = r4
    //     0xbf66c0: stur            w4, [x3, #0x13]
    // 0xbf66c4: r5 = Instance_MainAxisSize
    //     0xbf66c4: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf66c8: ldr             x5, [x5, #0xa10]
    // 0xbf66cc: ArrayStore: r3[0] = r5  ; List_4
    //     0xbf66cc: stur            w5, [x3, #0x17]
    // 0xbf66d0: r6 = Instance_CrossAxisAlignment
    //     0xbf66d0: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf66d4: ldr             x6, [x6, #0xa18]
    // 0xbf66d8: StoreField: r3->field_1b = r6
    //     0xbf66d8: stur            w6, [x3, #0x1b]
    // 0xbf66dc: r7 = Instance_VerticalDirection
    //     0xbf66dc: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf66e0: ldr             x7, [x7, #0xa20]
    // 0xbf66e4: StoreField: r3->field_23 = r7
    //     0xbf66e4: stur            w7, [x3, #0x23]
    // 0xbf66e8: r8 = Instance_Clip
    //     0xbf66e8: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf66ec: ldr             x8, [x8, #0x38]
    // 0xbf66f0: StoreField: r3->field_2b = r8
    //     0xbf66f0: stur            w8, [x3, #0x2b]
    // 0xbf66f4: StoreField: r3->field_2f = rZR
    //     0xbf66f4: stur            xzr, [x3, #0x2f]
    // 0xbf66f8: ldur            x1, [fp, #-0x28]
    // 0xbf66fc: StoreField: r3->field_b = r1
    //     0xbf66fc: stur            w1, [x3, #0xb]
    // 0xbf6700: r1 = Null
    //     0xbf6700: mov             x1, NULL
    // 0xbf6704: r2 = 2
    //     0xbf6704: movz            x2, #0x2
    // 0xbf6708: r0 = AllocateArray()
    //     0xbf6708: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf670c: mov             x2, x0
    // 0xbf6710: ldur            x0, [fp, #-8]
    // 0xbf6714: stur            x2, [fp, #-0x28]
    // 0xbf6718: StoreField: r2->field_f = r0
    //     0xbf6718: stur            w0, [x2, #0xf]
    // 0xbf671c: r1 = <Widget>
    //     0xbf671c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf6720: r0 = AllocateGrowableArray()
    //     0xbf6720: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf6724: mov             x1, x0
    // 0xbf6728: ldur            x0, [fp, #-0x28]
    // 0xbf672c: stur            x1, [fp, #-8]
    // 0xbf6730: StoreField: r1->field_f = r0
    //     0xbf6730: stur            w0, [x1, #0xf]
    // 0xbf6734: r0 = 2
    //     0xbf6734: movz            x0, #0x2
    // 0xbf6738: StoreField: r1->field_b = r0
    //     0xbf6738: stur            w0, [x1, #0xb]
    // 0xbf673c: r0 = Column()
    //     0xbf673c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf6740: mov             x1, x0
    // 0xbf6744: r0 = Instance_Axis
    //     0xbf6744: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf6748: stur            x1, [fp, #-0x28]
    // 0xbf674c: StoreField: r1->field_f = r0
    //     0xbf674c: stur            w0, [x1, #0xf]
    // 0xbf6750: r2 = Instance_MainAxisAlignment
    //     0xbf6750: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf6754: ldr             x2, [x2, #0xa08]
    // 0xbf6758: StoreField: r1->field_13 = r2
    //     0xbf6758: stur            w2, [x1, #0x13]
    // 0xbf675c: r3 = Instance_MainAxisSize
    //     0xbf675c: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf6760: ldr             x3, [x3, #0xa10]
    // 0xbf6764: ArrayStore: r1[0] = r3  ; List_4
    //     0xbf6764: stur            w3, [x1, #0x17]
    // 0xbf6768: r4 = Instance_CrossAxisAlignment
    //     0xbf6768: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbf676c: ldr             x4, [x4, #0xa18]
    // 0xbf6770: StoreField: r1->field_1b = r4
    //     0xbf6770: stur            w4, [x1, #0x1b]
    // 0xbf6774: r4 = Instance_VerticalDirection
    //     0xbf6774: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf6778: ldr             x4, [x4, #0xa20]
    // 0xbf677c: StoreField: r1->field_23 = r4
    //     0xbf677c: stur            w4, [x1, #0x23]
    // 0xbf6780: r5 = Instance_Clip
    //     0xbf6780: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf6784: ldr             x5, [x5, #0x38]
    // 0xbf6788: StoreField: r1->field_2b = r5
    //     0xbf6788: stur            w5, [x1, #0x2b]
    // 0xbf678c: StoreField: r1->field_2f = rZR
    //     0xbf678c: stur            xzr, [x1, #0x2f]
    // 0xbf6790: ldur            x6, [fp, #-8]
    // 0xbf6794: StoreField: r1->field_b = r6
    //     0xbf6794: stur            w6, [x1, #0xb]
    // 0xbf6798: r0 = Padding()
    //     0xbf6798: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf679c: mov             x1, x0
    // 0xbf67a0: r0 = Instance_EdgeInsets
    //     0xbf67a0: add             x0, PP, #0x35, lsl #12  ; [pp+0x35f48] Obj!EdgeInsets@d57b01
    //     0xbf67a4: ldr             x0, [x0, #0xf48]
    // 0xbf67a8: stur            x1, [fp, #-8]
    // 0xbf67ac: StoreField: r1->field_f = r0
    //     0xbf67ac: stur            w0, [x1, #0xf]
    // 0xbf67b0: ldur            x0, [fp, #-0x28]
    // 0xbf67b4: StoreField: r1->field_b = r0
    //     0xbf67b4: stur            w0, [x1, #0xb]
    // 0xbf67b8: r0 = Container()
    //     0xbf67b8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf67bc: stur            x0, [fp, #-0x28]
    // 0xbf67c0: r16 = Instance_EdgeInsets
    //     0xbf67c0: add             x16, PP, #0x34, lsl #12  ; [pp+0x34668] Obj!EdgeInsets@d57321
    //     0xbf67c4: ldr             x16, [x16, #0x668]
    // 0xbf67c8: ldur            lr, [fp, #-0x40]
    // 0xbf67cc: stp             lr, x16, [SP, #8]
    // 0xbf67d0: ldur            x16, [fp, #-8]
    // 0xbf67d4: str             x16, [SP]
    // 0xbf67d8: mov             x1, x0
    // 0xbf67dc: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, margin, 0x1, null]
    //     0xbf67dc: add             x4, PP, #0x35, lsl #12  ; [pp+0x35f50] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "margin", 0x1, Null]
    //     0xbf67e0: ldr             x4, [x4, #0xf50]
    // 0xbf67e4: r0 = Container()
    //     0xbf67e4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf67e8: r0 = Padding()
    //     0xbf67e8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf67ec: mov             x1, x0
    // 0xbf67f0: r0 = Instance_EdgeInsets
    //     0xbf67f0: add             x0, PP, #0x33, lsl #12  ; [pp+0x33778] Obj!EdgeInsets@d57d41
    //     0xbf67f4: ldr             x0, [x0, #0x778]
    // 0xbf67f8: stur            x1, [fp, #-8]
    // 0xbf67fc: StoreField: r1->field_f = r0
    //     0xbf67fc: stur            w0, [x1, #0xf]
    // 0xbf6800: ldur            x0, [fp, #-0x28]
    // 0xbf6804: StoreField: r1->field_b = r0
    //     0xbf6804: stur            w0, [x1, #0xb]
    // 0xbf6808: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbf6808: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbf680c: ldr             x0, [x0, #0x1c80]
    //     0xbf6810: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbf6814: cmp             w0, w16
    //     0xbf6818: b.ne            #0xbf6824
    //     0xbf681c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbf6820: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbf6824: r0 = GetNavigation.width()
    //     0xbf6824: bl              #0xa09874  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.width
    // 0xbf6828: mov             v1.16b, v0.16b
    // 0xbf682c: d0 = 1.200000
    //     0xbf682c: add             x17, PP, #0x4d, lsl #12  ; [pp+0x4dd20] IMM: double(1.2) from 0x3ff3333333333333
    //     0xbf6830: ldr             d0, [x17, #0xd20]
    // 0xbf6834: fmul            d2, d1, d0
    // 0xbf6838: stur            d2, [fp, #-0x70]
    // 0xbf683c: r16 = <EdgeInsets>
    //     0xbf683c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbf6840: ldr             x16, [x16, #0xda0]
    // 0xbf6844: r30 = Instance_EdgeInsets
    //     0xbf6844: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbf6848: ldr             lr, [lr, #0x1f0]
    // 0xbf684c: stp             lr, x16, [SP]
    // 0xbf6850: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbf6850: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbf6854: r0 = all()
    //     0xbf6854: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbf6858: ldur            x1, [fp, #-0x10]
    // 0xbf685c: stur            x0, [fp, #-0x28]
    // 0xbf6860: r0 = of()
    //     0xbf6860: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf6864: LoadField: r1 = r0->field_5b
    //     0xbf6864: ldur            w1, [x0, #0x5b]
    // 0xbf6868: DecompressPointer r1
    //     0xbf6868: add             x1, x1, HEAP, lsl #32
    // 0xbf686c: r16 = <Color>
    //     0xbf686c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbf6870: ldr             x16, [x16, #0xf80]
    // 0xbf6874: stp             x1, x16, [SP]
    // 0xbf6878: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbf6878: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbf687c: r0 = all()
    //     0xbf687c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbf6880: stur            x0, [fp, #-0x40]
    // 0xbf6884: r16 = <RoundedRectangleBorder>
    //     0xbf6884: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbf6888: ldr             x16, [x16, #0xf78]
    // 0xbf688c: r30 = Instance_RoundedRectangleBorder
    //     0xbf688c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbf6890: ldr             lr, [lr, #0xd68]
    // 0xbf6894: stp             lr, x16, [SP]
    // 0xbf6898: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbf6898: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbf689c: r0 = all()
    //     0xbf689c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbf68a0: stur            x0, [fp, #-0x48]
    // 0xbf68a4: r0 = ButtonStyle()
    //     0xbf68a4: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbf68a8: mov             x1, x0
    // 0xbf68ac: ldur            x0, [fp, #-0x40]
    // 0xbf68b0: stur            x1, [fp, #-0x50]
    // 0xbf68b4: StoreField: r1->field_b = r0
    //     0xbf68b4: stur            w0, [x1, #0xb]
    // 0xbf68b8: ldur            x0, [fp, #-0x28]
    // 0xbf68bc: StoreField: r1->field_23 = r0
    //     0xbf68bc: stur            w0, [x1, #0x23]
    // 0xbf68c0: ldur            x0, [fp, #-0x48]
    // 0xbf68c4: StoreField: r1->field_43 = r0
    //     0xbf68c4: stur            w0, [x1, #0x43]
    // 0xbf68c8: r0 = TextButtonThemeData()
    //     0xbf68c8: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbf68cc: mov             x2, x0
    // 0xbf68d0: ldur            x0, [fp, #-0x50]
    // 0xbf68d4: stur            x2, [fp, #-0x28]
    // 0xbf68d8: StoreField: r2->field_7 = r0
    //     0xbf68d8: stur            w0, [x2, #7]
    // 0xbf68dc: ldur            x1, [fp, #-0x10]
    // 0xbf68e0: r0 = of()
    //     0xbf68e0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf68e4: LoadField: r1 = r0->field_87
    //     0xbf68e4: ldur            w1, [x0, #0x87]
    // 0xbf68e8: DecompressPointer r1
    //     0xbf68e8: add             x1, x1, HEAP, lsl #32
    // 0xbf68ec: LoadField: r0 = r1->field_7
    //     0xbf68ec: ldur            w0, [x1, #7]
    // 0xbf68f0: DecompressPointer r0
    //     0xbf68f0: add             x0, x0, HEAP, lsl #32
    // 0xbf68f4: r16 = 14.000000
    //     0xbf68f4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbf68f8: ldr             x16, [x16, #0x1d8]
    // 0xbf68fc: r30 = Instance_Color
    //     0xbf68fc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbf6900: stp             lr, x16, [SP]
    // 0xbf6904: mov             x1, x0
    // 0xbf6908: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf6908: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf690c: ldr             x4, [x4, #0xaa0]
    // 0xbf6910: r0 = copyWith()
    //     0xbf6910: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf6914: stur            x0, [fp, #-0x40]
    // 0xbf6918: r0 = Text()
    //     0xbf6918: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf691c: mov             x3, x0
    // 0xbf6920: r0 = "CONTINUE"
    //     0xbf6920: add             x0, PP, #0x37, lsl #12  ; [pp+0x37ac8] "CONTINUE"
    //     0xbf6924: ldr             x0, [x0, #0xac8]
    // 0xbf6928: stur            x3, [fp, #-0x48]
    // 0xbf692c: StoreField: r3->field_b = r0
    //     0xbf692c: stur            w0, [x3, #0xb]
    // 0xbf6930: ldur            x0, [fp, #-0x40]
    // 0xbf6934: StoreField: r3->field_13 = r0
    //     0xbf6934: stur            w0, [x3, #0x13]
    // 0xbf6938: ldur            x2, [fp, #-0x20]
    // 0xbf693c: r1 = Function '<anonymous closure>':.
    //     0xbf693c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53950] AnonymousClosure: (0xbf6ea8), in [package:customer_app/app/presentation/views/line/orders/cancel_return_order_with_free_product_bottom_sheet.dart] _CancelReturnOrderWithFreeProductBottomSheetState::build (0xbf4e40)
    //     0xbf6940: ldr             x1, [x1, #0x950]
    // 0xbf6944: r0 = AllocateClosure()
    //     0xbf6944: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf6948: stur            x0, [fp, #-0x20]
    // 0xbf694c: r0 = TextButton()
    //     0xbf694c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbf6950: mov             x1, x0
    // 0xbf6954: ldur            x0, [fp, #-0x20]
    // 0xbf6958: stur            x1, [fp, #-0x40]
    // 0xbf695c: StoreField: r1->field_b = r0
    //     0xbf695c: stur            w0, [x1, #0xb]
    // 0xbf6960: r0 = false
    //     0xbf6960: add             x0, NULL, #0x30  ; false
    // 0xbf6964: StoreField: r1->field_27 = r0
    //     0xbf6964: stur            w0, [x1, #0x27]
    // 0xbf6968: r2 = true
    //     0xbf6968: add             x2, NULL, #0x20  ; true
    // 0xbf696c: StoreField: r1->field_2f = r2
    //     0xbf696c: stur            w2, [x1, #0x2f]
    // 0xbf6970: ldur            x3, [fp, #-0x48]
    // 0xbf6974: StoreField: r1->field_37 = r3
    //     0xbf6974: stur            w3, [x1, #0x37]
    // 0xbf6978: r0 = TextButtonTheme()
    //     0xbf6978: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbf697c: mov             x2, x0
    // 0xbf6980: ldur            x0, [fp, #-0x28]
    // 0xbf6984: stur            x2, [fp, #-0x20]
    // 0xbf6988: StoreField: r2->field_f = r0
    //     0xbf6988: stur            w0, [x2, #0xf]
    // 0xbf698c: ldur            x0, [fp, #-0x40]
    // 0xbf6990: StoreField: r2->field_b = r0
    //     0xbf6990: stur            w0, [x2, #0xb]
    // 0xbf6994: r1 = <FlexParentData>
    //     0xbf6994: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbf6998: ldr             x1, [x1, #0xe00]
    // 0xbf699c: r0 = Expanded()
    //     0xbf699c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbf69a0: mov             x1, x0
    // 0xbf69a4: r0 = 1
    //     0xbf69a4: movz            x0, #0x1
    // 0xbf69a8: stur            x1, [fp, #-0x28]
    // 0xbf69ac: StoreField: r1->field_13 = r0
    //     0xbf69ac: stur            x0, [x1, #0x13]
    // 0xbf69b0: r2 = Instance_FlexFit
    //     0xbf69b0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbf69b4: ldr             x2, [x2, #0xe08]
    // 0xbf69b8: StoreField: r1->field_1b = r2
    //     0xbf69b8: stur            w2, [x1, #0x1b]
    // 0xbf69bc: ldur            x3, [fp, #-0x20]
    // 0xbf69c0: StoreField: r1->field_b = r3
    //     0xbf69c0: stur            w3, [x1, #0xb]
    // 0xbf69c4: r16 = <EdgeInsets>
    //     0xbf69c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xbf69c8: ldr             x16, [x16, #0xda0]
    // 0xbf69cc: r30 = Instance_EdgeInsets
    //     0xbf69cc: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbf69d0: ldr             lr, [lr, #0x1f0]
    // 0xbf69d4: stp             lr, x16, [SP]
    // 0xbf69d8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbf69d8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbf69dc: r0 = all()
    //     0xbf69dc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbf69e0: ldur            x1, [fp, #-0x10]
    // 0xbf69e4: stur            x0, [fp, #-0x20]
    // 0xbf69e8: r0 = of()
    //     0xbf69e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf69ec: LoadField: r1 = r0->field_5b
    //     0xbf69ec: ldur            w1, [x0, #0x5b]
    // 0xbf69f0: DecompressPointer r1
    //     0xbf69f0: add             x1, x1, HEAP, lsl #32
    // 0xbf69f4: r0 = LoadClassIdInstr(r1)
    //     0xbf69f4: ldur            x0, [x1, #-1]
    //     0xbf69f8: ubfx            x0, x0, #0xc, #0x14
    // 0xbf69fc: d0 = 0.070000
    //     0xbf69fc: add             x17, PP, #0x36, lsl #12  ; [pp+0x365f8] IMM: double(0.07) from 0x3fb1eb851eb851ec
    //     0xbf6a00: ldr             d0, [x17, #0x5f8]
    // 0xbf6a04: r0 = GDT[cid_x0 + -0xffa]()
    //     0xbf6a04: sub             lr, x0, #0xffa
    //     0xbf6a08: ldr             lr, [x21, lr, lsl #3]
    //     0xbf6a0c: blr             lr
    // 0xbf6a10: stur            x0, [fp, #-0x40]
    // 0xbf6a14: r0 = BorderSide()
    //     0xbf6a14: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbf6a18: mov             x1, x0
    // 0xbf6a1c: ldur            x0, [fp, #-0x40]
    // 0xbf6a20: stur            x1, [fp, #-0x48]
    // 0xbf6a24: StoreField: r1->field_7 = r0
    //     0xbf6a24: stur            w0, [x1, #7]
    // 0xbf6a28: d0 = 1.000000
    //     0xbf6a28: fmov            d0, #1.00000000
    // 0xbf6a2c: StoreField: r1->field_b = d0
    //     0xbf6a2c: stur            d0, [x1, #0xb]
    // 0xbf6a30: r0 = Instance_BorderStyle
    //     0xbf6a30: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbf6a34: ldr             x0, [x0, #0xf68]
    // 0xbf6a38: StoreField: r1->field_13 = r0
    //     0xbf6a38: stur            w0, [x1, #0x13]
    // 0xbf6a3c: d0 = -1.000000
    //     0xbf6a3c: fmov            d0, #-1.00000000
    // 0xbf6a40: ArrayStore: r1[0] = d0  ; List_8
    //     0xbf6a40: stur            d0, [x1, #0x17]
    // 0xbf6a44: r0 = RoundedRectangleBorder()
    //     0xbf6a44: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xbf6a48: mov             x1, x0
    // 0xbf6a4c: r0 = Instance_BorderRadius
    //     0xbf6a4c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbf6a50: ldr             x0, [x0, #0xf70]
    // 0xbf6a54: StoreField: r1->field_b = r0
    //     0xbf6a54: stur            w0, [x1, #0xb]
    // 0xbf6a58: ldur            x0, [fp, #-0x48]
    // 0xbf6a5c: StoreField: r1->field_7 = r0
    //     0xbf6a5c: stur            w0, [x1, #7]
    // 0xbf6a60: r16 = <RoundedRectangleBorder>
    //     0xbf6a60: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbf6a64: ldr             x16, [x16, #0xf78]
    // 0xbf6a68: stp             x1, x16, [SP]
    // 0xbf6a6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbf6a6c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbf6a70: r0 = all()
    //     0xbf6a70: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbf6a74: stur            x0, [fp, #-0x40]
    // 0xbf6a78: r0 = ButtonStyle()
    //     0xbf6a78: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbf6a7c: mov             x1, x0
    // 0xbf6a80: ldur            x0, [fp, #-0x20]
    // 0xbf6a84: stur            x1, [fp, #-0x48]
    // 0xbf6a88: StoreField: r1->field_23 = r0
    //     0xbf6a88: stur            w0, [x1, #0x23]
    // 0xbf6a8c: ldur            x0, [fp, #-0x40]
    // 0xbf6a90: StoreField: r1->field_43 = r0
    //     0xbf6a90: stur            w0, [x1, #0x43]
    // 0xbf6a94: r0 = TextButtonThemeData()
    //     0xbf6a94: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbf6a98: mov             x2, x0
    // 0xbf6a9c: ldur            x0, [fp, #-0x48]
    // 0xbf6aa0: stur            x2, [fp, #-0x20]
    // 0xbf6aa4: StoreField: r2->field_7 = r0
    //     0xbf6aa4: stur            w0, [x2, #7]
    // 0xbf6aa8: ldur            x1, [fp, #-0x10]
    // 0xbf6aac: r0 = of()
    //     0xbf6aac: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf6ab0: LoadField: r1 = r0->field_87
    //     0xbf6ab0: ldur            w1, [x0, #0x87]
    // 0xbf6ab4: DecompressPointer r1
    //     0xbf6ab4: add             x1, x1, HEAP, lsl #32
    // 0xbf6ab8: LoadField: r0 = r1->field_7
    //     0xbf6ab8: ldur            w0, [x1, #7]
    // 0xbf6abc: DecompressPointer r0
    //     0xbf6abc: add             x0, x0, HEAP, lsl #32
    // 0xbf6ac0: ldur            x1, [fp, #-0x10]
    // 0xbf6ac4: stur            x0, [fp, #-0x40]
    // 0xbf6ac8: r0 = of()
    //     0xbf6ac8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbf6acc: LoadField: r1 = r0->field_5b
    //     0xbf6acc: ldur            w1, [x0, #0x5b]
    // 0xbf6ad0: DecompressPointer r1
    //     0xbf6ad0: add             x1, x1, HEAP, lsl #32
    // 0xbf6ad4: r16 = 14.000000
    //     0xbf6ad4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbf6ad8: ldr             x16, [x16, #0x1d8]
    // 0xbf6adc: stp             x1, x16, [SP]
    // 0xbf6ae0: ldur            x1, [fp, #-0x40]
    // 0xbf6ae4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbf6ae4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbf6ae8: ldr             x4, [x4, #0xaa0]
    // 0xbf6aec: r0 = copyWith()
    //     0xbf6aec: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbf6af0: stur            x0, [fp, #-0x10]
    // 0xbf6af4: r0 = Text()
    //     0xbf6af4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbf6af8: mov             x3, x0
    // 0xbf6afc: r0 = "GO, BACK"
    //     0xbf6afc: add             x0, PP, #0x53, lsl #12  ; [pp+0x53958] "GO, BACK"
    //     0xbf6b00: ldr             x0, [x0, #0x958]
    // 0xbf6b04: stur            x3, [fp, #-0x40]
    // 0xbf6b08: StoreField: r3->field_b = r0
    //     0xbf6b08: stur            w0, [x3, #0xb]
    // 0xbf6b0c: ldur            x0, [fp, #-0x10]
    // 0xbf6b10: StoreField: r3->field_13 = r0
    //     0xbf6b10: stur            w0, [x3, #0x13]
    // 0xbf6b14: r1 = Function '<anonymous closure>':.
    //     0xbf6b14: add             x1, PP, #0x53, lsl #12  ; [pp+0x53960] AnonymousClosure: (0x9010ec), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xbf6b18: ldr             x1, [x1, #0x960]
    // 0xbf6b1c: r2 = Null
    //     0xbf6b1c: mov             x2, NULL
    // 0xbf6b20: r0 = AllocateClosure()
    //     0xbf6b20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbf6b24: stur            x0, [fp, #-0x10]
    // 0xbf6b28: r0 = TextButton()
    //     0xbf6b28: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbf6b2c: mov             x1, x0
    // 0xbf6b30: ldur            x0, [fp, #-0x10]
    // 0xbf6b34: stur            x1, [fp, #-0x48]
    // 0xbf6b38: StoreField: r1->field_b = r0
    //     0xbf6b38: stur            w0, [x1, #0xb]
    // 0xbf6b3c: r0 = false
    //     0xbf6b3c: add             x0, NULL, #0x30  ; false
    // 0xbf6b40: StoreField: r1->field_27 = r0
    //     0xbf6b40: stur            w0, [x1, #0x27]
    // 0xbf6b44: r2 = true
    //     0xbf6b44: add             x2, NULL, #0x20  ; true
    // 0xbf6b48: StoreField: r1->field_2f = r2
    //     0xbf6b48: stur            w2, [x1, #0x2f]
    // 0xbf6b4c: ldur            x2, [fp, #-0x40]
    // 0xbf6b50: StoreField: r1->field_37 = r2
    //     0xbf6b50: stur            w2, [x1, #0x37]
    // 0xbf6b54: r0 = TextButtonTheme()
    //     0xbf6b54: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbf6b58: mov             x2, x0
    // 0xbf6b5c: ldur            x0, [fp, #-0x20]
    // 0xbf6b60: stur            x2, [fp, #-0x10]
    // 0xbf6b64: StoreField: r2->field_f = r0
    //     0xbf6b64: stur            w0, [x2, #0xf]
    // 0xbf6b68: ldur            x0, [fp, #-0x48]
    // 0xbf6b6c: StoreField: r2->field_b = r0
    //     0xbf6b6c: stur            w0, [x2, #0xb]
    // 0xbf6b70: r1 = <FlexParentData>
    //     0xbf6b70: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbf6b74: ldr             x1, [x1, #0xe00]
    // 0xbf6b78: r0 = Expanded()
    //     0xbf6b78: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbf6b7c: mov             x3, x0
    // 0xbf6b80: r0 = 1
    //     0xbf6b80: movz            x0, #0x1
    // 0xbf6b84: stur            x3, [fp, #-0x20]
    // 0xbf6b88: StoreField: r3->field_13 = r0
    //     0xbf6b88: stur            x0, [x3, #0x13]
    // 0xbf6b8c: r0 = Instance_FlexFit
    //     0xbf6b8c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbf6b90: ldr             x0, [x0, #0xe08]
    // 0xbf6b94: StoreField: r3->field_1b = r0
    //     0xbf6b94: stur            w0, [x3, #0x1b]
    // 0xbf6b98: ldur            x0, [fp, #-0x10]
    // 0xbf6b9c: StoreField: r3->field_b = r0
    //     0xbf6b9c: stur            w0, [x3, #0xb]
    // 0xbf6ba0: r1 = Null
    //     0xbf6ba0: mov             x1, NULL
    // 0xbf6ba4: r2 = 6
    //     0xbf6ba4: movz            x2, #0x6
    // 0xbf6ba8: r0 = AllocateArray()
    //     0xbf6ba8: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf6bac: mov             x2, x0
    // 0xbf6bb0: ldur            x0, [fp, #-0x28]
    // 0xbf6bb4: stur            x2, [fp, #-0x10]
    // 0xbf6bb8: StoreField: r2->field_f = r0
    //     0xbf6bb8: stur            w0, [x2, #0xf]
    // 0xbf6bbc: r16 = Instance_SizedBox
    //     0xbf6bbc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0xbf6bc0: ldr             x16, [x16, #0xb20]
    // 0xbf6bc4: StoreField: r2->field_13 = r16
    //     0xbf6bc4: stur            w16, [x2, #0x13]
    // 0xbf6bc8: ldur            x0, [fp, #-0x20]
    // 0xbf6bcc: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf6bcc: stur            w0, [x2, #0x17]
    // 0xbf6bd0: r1 = <Widget>
    //     0xbf6bd0: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf6bd4: r0 = AllocateGrowableArray()
    //     0xbf6bd4: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf6bd8: mov             x1, x0
    // 0xbf6bdc: ldur            x0, [fp, #-0x10]
    // 0xbf6be0: stur            x1, [fp, #-0x20]
    // 0xbf6be4: StoreField: r1->field_f = r0
    //     0xbf6be4: stur            w0, [x1, #0xf]
    // 0xbf6be8: r0 = 6
    //     0xbf6be8: movz            x0, #0x6
    // 0xbf6bec: StoreField: r1->field_b = r0
    //     0xbf6bec: stur            w0, [x1, #0xb]
    // 0xbf6bf0: r0 = Row()
    //     0xbf6bf0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbf6bf4: mov             x1, x0
    // 0xbf6bf8: r0 = Instance_Axis
    //     0xbf6bf8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbf6bfc: stur            x1, [fp, #-0x28]
    // 0xbf6c00: StoreField: r1->field_f = r0
    //     0xbf6c00: stur            w0, [x1, #0xf]
    // 0xbf6c04: r0 = Instance_MainAxisAlignment
    //     0xbf6c04: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbf6c08: ldr             x0, [x0, #0xa8]
    // 0xbf6c0c: StoreField: r1->field_13 = r0
    //     0xbf6c0c: stur            w0, [x1, #0x13]
    // 0xbf6c10: r0 = Instance_MainAxisSize
    //     0xbf6c10: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf6c14: ldr             x0, [x0, #0xa10]
    // 0xbf6c18: ArrayStore: r1[0] = r0  ; List_4
    //     0xbf6c18: stur            w0, [x1, #0x17]
    // 0xbf6c1c: r2 = Instance_CrossAxisAlignment
    //     0xbf6c1c: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf6c20: ldr             x2, [x2, #0x890]
    // 0xbf6c24: StoreField: r1->field_1b = r2
    //     0xbf6c24: stur            w2, [x1, #0x1b]
    // 0xbf6c28: r3 = Instance_VerticalDirection
    //     0xbf6c28: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf6c2c: ldr             x3, [x3, #0xa20]
    // 0xbf6c30: StoreField: r1->field_23 = r3
    //     0xbf6c30: stur            w3, [x1, #0x23]
    // 0xbf6c34: r4 = Instance_Clip
    //     0xbf6c34: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf6c38: ldr             x4, [x4, #0x38]
    // 0xbf6c3c: StoreField: r1->field_2b = r4
    //     0xbf6c3c: stur            w4, [x1, #0x2b]
    // 0xbf6c40: StoreField: r1->field_2f = rZR
    //     0xbf6c40: stur            xzr, [x1, #0x2f]
    // 0xbf6c44: ldur            x5, [fp, #-0x20]
    // 0xbf6c48: StoreField: r1->field_b = r5
    //     0xbf6c48: stur            w5, [x1, #0xb]
    // 0xbf6c4c: ldur            d0, [fp, #-0x70]
    // 0xbf6c50: r5 = inline_Allocate_Double()
    //     0xbf6c50: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xbf6c54: add             x5, x5, #0x10
    //     0xbf6c58: cmp             x6, x5
    //     0xbf6c5c: b.ls            #0xbf6e7c
    //     0xbf6c60: str             x5, [THR, #0x50]  ; THR::top
    //     0xbf6c64: sub             x5, x5, #0xf
    //     0xbf6c68: movz            x6, #0xe15c
    //     0xbf6c6c: movk            x6, #0x3, lsl #16
    //     0xbf6c70: stur            x6, [x5, #-1]
    // 0xbf6c74: StoreField: r5->field_7 = d0
    //     0xbf6c74: stur            d0, [x5, #7]
    // 0xbf6c78: stur            x5, [fp, #-0x10]
    // 0xbf6c7c: r0 = Container()
    //     0xbf6c7c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbf6c80: stur            x0, [fp, #-0x20]
    // 0xbf6c84: ldur            x16, [fp, #-0x10]
    // 0xbf6c88: r30 = Instance_Color
    //     0xbf6c88: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbf6c8c: stp             lr, x16, [SP, #8]
    // 0xbf6c90: ldur            x16, [fp, #-0x28]
    // 0xbf6c94: str             x16, [SP]
    // 0xbf6c98: mov             x1, x0
    // 0xbf6c9c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, width, 0x1, null]
    //     0xbf6c9c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33828] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "width", 0x1, Null]
    //     0xbf6ca0: ldr             x4, [x4, #0x828]
    // 0xbf6ca4: r0 = Container()
    //     0xbf6ca4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbf6ca8: r1 = Null
    //     0xbf6ca8: mov             x1, NULL
    // 0xbf6cac: r2 = 10
    //     0xbf6cac: movz            x2, #0xa
    // 0xbf6cb0: r0 = AllocateArray()
    //     0xbf6cb0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbf6cb4: mov             x2, x0
    // 0xbf6cb8: ldur            x0, [fp, #-0x30]
    // 0xbf6cbc: stur            x2, [fp, #-0x10]
    // 0xbf6cc0: StoreField: r2->field_f = r0
    //     0xbf6cc0: stur            w0, [x2, #0xf]
    // 0xbf6cc4: ldur            x0, [fp, #-0x18]
    // 0xbf6cc8: StoreField: r2->field_13 = r0
    //     0xbf6cc8: stur            w0, [x2, #0x13]
    // 0xbf6ccc: ldur            x0, [fp, #-0x38]
    // 0xbf6cd0: ArrayStore: r2[0] = r0  ; List_4
    //     0xbf6cd0: stur            w0, [x2, #0x17]
    // 0xbf6cd4: ldur            x0, [fp, #-8]
    // 0xbf6cd8: StoreField: r2->field_1b = r0
    //     0xbf6cd8: stur            w0, [x2, #0x1b]
    // 0xbf6cdc: ldur            x0, [fp, #-0x20]
    // 0xbf6ce0: StoreField: r2->field_1f = r0
    //     0xbf6ce0: stur            w0, [x2, #0x1f]
    // 0xbf6ce4: r1 = <Widget>
    //     0xbf6ce4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbf6ce8: r0 = AllocateGrowableArray()
    //     0xbf6ce8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbf6cec: mov             x1, x0
    // 0xbf6cf0: ldur            x0, [fp, #-0x10]
    // 0xbf6cf4: stur            x1, [fp, #-8]
    // 0xbf6cf8: StoreField: r1->field_f = r0
    //     0xbf6cf8: stur            w0, [x1, #0xf]
    // 0xbf6cfc: r0 = 10
    //     0xbf6cfc: movz            x0, #0xa
    // 0xbf6d00: StoreField: r1->field_b = r0
    //     0xbf6d00: stur            w0, [x1, #0xb]
    // 0xbf6d04: r0 = Column()
    //     0xbf6d04: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbf6d08: mov             x1, x0
    // 0xbf6d0c: r0 = Instance_Axis
    //     0xbf6d0c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf6d10: stur            x1, [fp, #-0x10]
    // 0xbf6d14: StoreField: r1->field_f = r0
    //     0xbf6d14: stur            w0, [x1, #0xf]
    // 0xbf6d18: r2 = Instance_MainAxisAlignment
    //     0xbf6d18: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbf6d1c: ldr             x2, [x2, #0xa08]
    // 0xbf6d20: StoreField: r1->field_13 = r2
    //     0xbf6d20: stur            w2, [x1, #0x13]
    // 0xbf6d24: r2 = Instance_MainAxisSize
    //     0xbf6d24: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbf6d28: ldr             x2, [x2, #0xa10]
    // 0xbf6d2c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbf6d2c: stur            w2, [x1, #0x17]
    // 0xbf6d30: r2 = Instance_CrossAxisAlignment
    //     0xbf6d30: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbf6d34: ldr             x2, [x2, #0x890]
    // 0xbf6d38: StoreField: r1->field_1b = r2
    //     0xbf6d38: stur            w2, [x1, #0x1b]
    // 0xbf6d3c: r2 = Instance_VerticalDirection
    //     0xbf6d3c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbf6d40: ldr             x2, [x2, #0xa20]
    // 0xbf6d44: StoreField: r1->field_23 = r2
    //     0xbf6d44: stur            w2, [x1, #0x23]
    // 0xbf6d48: r2 = Instance_Clip
    //     0xbf6d48: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbf6d4c: ldr             x2, [x2, #0x38]
    // 0xbf6d50: StoreField: r1->field_2b = r2
    //     0xbf6d50: stur            w2, [x1, #0x2b]
    // 0xbf6d54: StoreField: r1->field_2f = rZR
    //     0xbf6d54: stur            xzr, [x1, #0x2f]
    // 0xbf6d58: ldur            x2, [fp, #-8]
    // 0xbf6d5c: StoreField: r1->field_b = r2
    //     0xbf6d5c: stur            w2, [x1, #0xb]
    // 0xbf6d60: r0 = Padding()
    //     0xbf6d60: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbf6d64: mov             x1, x0
    // 0xbf6d68: r0 = Instance_EdgeInsets
    //     0xbf6d68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0xbf6d6c: ldr             x0, [x0, #0x1f0]
    // 0xbf6d70: stur            x1, [fp, #-8]
    // 0xbf6d74: StoreField: r1->field_f = r0
    //     0xbf6d74: stur            w0, [x1, #0xf]
    // 0xbf6d78: ldur            x0, [fp, #-0x10]
    // 0xbf6d7c: StoreField: r1->field_b = r0
    //     0xbf6d7c: stur            w0, [x1, #0xb]
    // 0xbf6d80: r0 = SingleChildScrollView()
    //     0xbf6d80: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xbf6d84: r1 = Instance_Axis
    //     0xbf6d84: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbf6d88: StoreField: r0->field_b = r1
    //     0xbf6d88: stur            w1, [x0, #0xb]
    // 0xbf6d8c: r1 = false
    //     0xbf6d8c: add             x1, NULL, #0x30  ; false
    // 0xbf6d90: StoreField: r0->field_f = r1
    //     0xbf6d90: stur            w1, [x0, #0xf]
    // 0xbf6d94: ldur            x1, [fp, #-8]
    // 0xbf6d98: StoreField: r0->field_23 = r1
    //     0xbf6d98: stur            w1, [x0, #0x23]
    // 0xbf6d9c: r1 = Instance_DragStartBehavior
    //     0xbf6d9c: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0xbf6da0: StoreField: r0->field_27 = r1
    //     0xbf6da0: stur            w1, [x0, #0x27]
    // 0xbf6da4: r1 = Instance_Clip
    //     0xbf6da4: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xbf6da8: ldr             x1, [x1, #0x7e0]
    // 0xbf6dac: StoreField: r0->field_2b = r1
    //     0xbf6dac: stur            w1, [x0, #0x2b]
    // 0xbf6db0: r1 = Instance_HitTestBehavior
    //     0xbf6db0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0xbf6db4: ldr             x1, [x1, #0x288]
    // 0xbf6db8: StoreField: r0->field_2f = r1
    //     0xbf6db8: stur            w1, [x0, #0x2f]
    // 0xbf6dbc: LeaveFrame
    //     0xbf6dbc: mov             SP, fp
    //     0xbf6dc0: ldp             fp, lr, [SP], #0x10
    // 0xbf6dc4: ret
    //     0xbf6dc4: ret             
    // 0xbf6dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf6dc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf6dcc: b               #0xbf4e68
    // 0xbf6dd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6dd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6dd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6dd4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6dd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6dd8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6ddc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6ddc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6de0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6de4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6de4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6de8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6de8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6dec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6dec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6df0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6df0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6df4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6df4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6df8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6dfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6dfc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e00: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e04: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e08: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e0c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e10: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e18: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e24: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e28: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e2c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e30: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e34: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e38: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e3c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e40: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e44: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e48: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e4c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e5c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e60: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e64: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e68: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e6c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e70: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e74: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6e78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbf6e7c: SaveReg d0
    //     0xbf6e7c: str             q0, [SP, #-0x10]!
    // 0xbf6e80: stp             x3, x4, [SP, #-0x10]!
    // 0xbf6e84: stp             x1, x2, [SP, #-0x10]!
    // 0xbf6e88: SaveReg r0
    //     0xbf6e88: str             x0, [SP, #-8]!
    // 0xbf6e8c: r0 = AllocateDouble()
    //     0xbf6e8c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbf6e90: mov             x5, x0
    // 0xbf6e94: RestoreReg r0
    //     0xbf6e94: ldr             x0, [SP], #8
    // 0xbf6e98: ldp             x1, x2, [SP], #0x10
    // 0xbf6e9c: ldp             x3, x4, [SP], #0x10
    // 0xbf6ea0: RestoreReg d0
    //     0xbf6ea0: ldr             q0, [SP], #0x10
    // 0xbf6ea4: b               #0xbf6c74
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbf6ea8, size: 0x7c
    // 0xbf6ea8: EnterFrame
    //     0xbf6ea8: stp             fp, lr, [SP, #-0x10]!
    //     0xbf6eac: mov             fp, SP
    // 0xbf6eb0: AllocStack(0x8)
    //     0xbf6eb0: sub             SP, SP, #8
    // 0xbf6eb4: SetupParameters()
    //     0xbf6eb4: ldr             x0, [fp, #0x10]
    //     0xbf6eb8: ldur            w1, [x0, #0x17]
    //     0xbf6ebc: add             x1, x1, HEAP, lsl #32
    // 0xbf6ec0: CheckStackOverflow
    //     0xbf6ec0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbf6ec4: cmp             SP, x16
    //     0xbf6ec8: b.ls            #0xbf6f18
    // 0xbf6ecc: LoadField: r0 = r1->field_f
    //     0xbf6ecc: ldur            w0, [x1, #0xf]
    // 0xbf6ed0: DecompressPointer r0
    //     0xbf6ed0: add             x0, x0, HEAP, lsl #32
    // 0xbf6ed4: LoadField: r1 = r0->field_b
    //     0xbf6ed4: ldur            w1, [x0, #0xb]
    // 0xbf6ed8: DecompressPointer r1
    //     0xbf6ed8: add             x1, x1, HEAP, lsl #32
    // 0xbf6edc: cmp             w1, NULL
    // 0xbf6ee0: b.eq            #0xbf6f20
    // 0xbf6ee4: LoadField: r0 = r1->field_b
    //     0xbf6ee4: ldur            w0, [x1, #0xb]
    // 0xbf6ee8: DecompressPointer r0
    //     0xbf6ee8: add             x0, x0, HEAP, lsl #32
    // 0xbf6eec: str             x0, [SP]
    // 0xbf6ef0: r4 = 0
    //     0xbf6ef0: movz            x4, #0
    // 0xbf6ef4: ldr             x0, [SP]
    // 0xbf6ef8: r16 = UnlinkedCall_0x613b5c
    //     0xbf6ef8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53968] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbf6efc: add             x16, x16, #0x968
    // 0xbf6f00: ldp             x5, lr, [x16]
    // 0xbf6f04: blr             lr
    // 0xbf6f08: r0 = Null
    //     0xbf6f08: mov             x0, NULL
    // 0xbf6f0c: LeaveFrame
    //     0xbf6f0c: mov             SP, fp
    //     0xbf6f10: ldp             fp, lr, [SP], #0x10
    // 0xbf6f14: ret
    //     0xbf6f14: ret             
    // 0xbf6f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbf6f18: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbf6f1c: b               #0xbf6ecc
    // 0xbf6f20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbf6f20: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3985, size: 0x28, field offset: 0xc
//   const constructor, 
class CancelReturnOrderWithFreeProductBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80d14, size: 0x24
    // 0xc80d14: EnterFrame
    //     0xc80d14: stp             fp, lr, [SP, #-0x10]!
    //     0xc80d18: mov             fp, SP
    // 0xc80d1c: mov             x0, x1
    // 0xc80d20: r1 = <CancelReturnOrderWithFreeProductBottomSheet>
    //     0xc80d20: add             x1, PP, #0x48, lsl #12  ; [pp+0x48438] TypeArguments: <CancelReturnOrderWithFreeProductBottomSheet>
    //     0xc80d24: ldr             x1, [x1, #0x438]
    // 0xc80d28: r0 = _CancelReturnOrderWithFreeProductBottomSheetState()
    //     0xc80d28: bl              #0xc80d38  ; Allocate_CancelReturnOrderWithFreeProductBottomSheetStateStub -> _CancelReturnOrderWithFreeProductBottomSheetState (size=0x14)
    // 0xc80d2c: LeaveFrame
    //     0xc80d2c: mov             SP, fp
    //     0xc80d30: ldp             fp, lr, [SP], #0x10
    // 0xc80d34: ret
    //     0xc80d34: ret             
  }
}
