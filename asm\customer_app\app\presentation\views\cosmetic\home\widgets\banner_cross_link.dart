// lib: , url: package:customer_app/app/presentation/views/cosmetic/home/<USER>/banner_cross_link.dart

// class id: 1049276, size: 0x8
class :: {
}

// class id: 3430, size: 0x14, field offset: 0x14
class _CarouselItemViewState extends State<dynamic> {

  _ build(/* No info */) {
    // ** addr: 0xae3430, size: 0x1b0
    // 0xae3430: EnterFrame
    //     0xae3430: stp             fp, lr, [SP, #-0x10]!
    //     0xae3434: mov             fp, SP
    // 0xae3438: AllocStack(0x50)
    //     0xae3438: sub             SP, SP, #0x50
    // 0xae343c: SetupParameters(_CarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xae343c: mov             x0, x1
    //     0xae3440: stur            x1, [fp, #-8]
    //     0xae3444: mov             x1, x2
    //     0xae3448: stur            x2, [fp, #-0x10]
    // 0xae344c: CheckStackOverflow
    //     0xae344c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3450: cmp             SP, x16
    //     0xae3454: b.ls            #0xae35d0
    // 0xae3458: r1 = 1
    //     0xae3458: movz            x1, #0x1
    // 0xae345c: r0 = AllocateContext()
    //     0xae345c: bl              #0x16f6108  ; AllocateContextStub
    // 0xae3460: mov             x3, x0
    // 0xae3464: ldur            x0, [fp, #-8]
    // 0xae3468: stur            x3, [fp, #-0x18]
    // 0xae346c: StoreField: r3->field_f = r0
    //     0xae346c: stur            w0, [x3, #0xf]
    // 0xae3470: LoadField: r1 = r0->field_b
    //     0xae3470: ldur            w1, [x0, #0xb]
    // 0xae3474: DecompressPointer r1
    //     0xae3474: add             x1, x1, HEAP, lsl #32
    // 0xae3478: cmp             w1, NULL
    // 0xae347c: b.eq            #0xae35d8
    // 0xae3480: LoadField: r2 = r1->field_b
    //     0xae3480: ldur            w2, [x1, #0xb]
    // 0xae3484: DecompressPointer r2
    //     0xae3484: add             x2, x2, HEAP, lsl #32
    // 0xae3488: cmp             w2, NULL
    // 0xae348c: b.ne            #0xae34a4
    // 0xae3490: r1 = <Entity>
    //     0xae3490: add             x1, PP, #0x23, lsl #12  ; [pp+0x23b68] TypeArguments: <Entity>
    //     0xae3494: ldr             x1, [x1, #0xb68]
    // 0xae3498: r2 = 0
    //     0xae3498: movz            x2, #0
    // 0xae349c: r0 = _GrowableList()
    //     0xae349c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xae34a0: mov             x2, x0
    // 0xae34a4: ldur            x0, [fp, #-8]
    // 0xae34a8: stur            x2, [fp, #-0x48]
    // 0xae34ac: LoadField: r1 = r0->field_b
    //     0xae34ac: ldur            w1, [x0, #0xb]
    // 0xae34b0: DecompressPointer r1
    //     0xae34b0: add             x1, x1, HEAP, lsl #32
    // 0xae34b4: cmp             w1, NULL
    // 0xae34b8: b.eq            #0xae35dc
    // 0xae34bc: LoadField: r0 = r1->field_1b
    //     0xae34bc: ldur            w0, [x1, #0x1b]
    // 0xae34c0: DecompressPointer r0
    //     0xae34c0: add             x0, x0, HEAP, lsl #32
    // 0xae34c4: stur            x0, [fp, #-0x40]
    // 0xae34c8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xae34c8: ldur            w3, [x1, #0x17]
    // 0xae34cc: DecompressPointer r3
    //     0xae34cc: add             x3, x3, HEAP, lsl #32
    // 0xae34d0: stur            x3, [fp, #-0x38]
    // 0xae34d4: LoadField: r4 = r1->field_13
    //     0xae34d4: ldur            w4, [x1, #0x13]
    // 0xae34d8: DecompressPointer r4
    //     0xae34d8: add             x4, x4, HEAP, lsl #32
    // 0xae34dc: stur            x4, [fp, #-0x30]
    // 0xae34e0: LoadField: r5 = r1->field_f
    //     0xae34e0: ldur            w5, [x1, #0xf]
    // 0xae34e4: DecompressPointer r5
    //     0xae34e4: add             x5, x5, HEAP, lsl #32
    // 0xae34e8: stur            x5, [fp, #-0x28]
    // 0xae34ec: LoadField: r6 = r1->field_27
    //     0xae34ec: ldur            w6, [x1, #0x27]
    // 0xae34f0: DecompressPointer r6
    //     0xae34f0: add             x6, x6, HEAP, lsl #32
    // 0xae34f4: stur            x6, [fp, #-0x20]
    // 0xae34f8: LoadField: r7 = r1->field_2b
    //     0xae34f8: ldur            w7, [x1, #0x2b]
    // 0xae34fc: DecompressPointer r7
    //     0xae34fc: add             x7, x7, HEAP, lsl #32
    // 0xae3500: ldur            x1, [fp, #-0x10]
    // 0xae3504: stur            x7, [fp, #-8]
    // 0xae3508: r0 = of()
    //     0xae3508: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae350c: LoadField: r1 = r0->field_5b
    //     0xae350c: ldur            w1, [x0, #0x5b]
    // 0xae3510: DecompressPointer r1
    //     0xae3510: add             x1, x1, HEAP, lsl #32
    // 0xae3514: stur            x1, [fp, #-0x10]
    // 0xae3518: r0 = BannerCrossWidget()
    //     0xae3518: bl              #0xa419a0  ; AllocateBannerCrossWidgetStub -> BannerCrossWidget (size=0x4c)
    // 0xae351c: d0 = 200.000000
    //     0xae351c: add             x17, PP, #0x37, lsl #12  ; [pp+0x37360] IMM: double(200) from 0x4069000000000000
    //     0xae3520: ldr             d0, [x17, #0x360]
    // 0xae3524: stur            x0, [fp, #-0x50]
    // 0xae3528: StoreField: r0->field_b = d0
    //     0xae3528: stur            d0, [x0, #0xb]
    // 0xae352c: r1 = true
    //     0xae352c: add             x1, NULL, #0x20  ; true
    // 0xae3530: StoreField: r0->field_13 = r1
    //     0xae3530: stur            w1, [x0, #0x13]
    // 0xae3534: r1 = Instance_Duration
    //     0xae3534: add             x1, PP, #0x52, lsl #12  ; [pp+0x52bd8] Obj!Duration@d77741
    //     0xae3538: ldr             x1, [x1, #0xbd8]
    // 0xae353c: ArrayStore: r0[0] = r1  ; List_4
    //     0xae353c: stur            w1, [x0, #0x17]
    // 0xae3540: ldur            x1, [fp, #-0x10]
    // 0xae3544: StoreField: r0->field_1b = r1
    //     0xae3544: stur            w1, [x0, #0x1b]
    // 0xae3548: r1 = Instance_Color
    //     0xae3548: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xae354c: ldr             x1, [x1, #0x90]
    // 0xae3550: StoreField: r0->field_1f = r1
    //     0xae3550: stur            w1, [x0, #0x1f]
    // 0xae3554: ldur            x1, [fp, #-0x48]
    // 0xae3558: StoreField: r0->field_27 = r1
    //     0xae3558: stur            w1, [x0, #0x27]
    // 0xae355c: ldur            x2, [fp, #-0x18]
    // 0xae3560: r1 = Function '<anonymous closure>':.
    //     0xae3560: add             x1, PP, #0x58, lsl #12  ; [pp+0x583b0] AnonymousClosure: (0xae3fb8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::build (0xae3430)
    //     0xae3564: ldr             x1, [x1, #0x3b0]
    // 0xae3568: r0 = AllocateClosure()
    //     0xae3568: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae356c: mov             x1, x0
    // 0xae3570: ldur            x0, [fp, #-0x50]
    // 0xae3574: StoreField: r0->field_2b = r1
    //     0xae3574: stur            w1, [x0, #0x2b]
    // 0xae3578: ldur            x1, [fp, #-0x40]
    // 0xae357c: StoreField: r0->field_2f = r1
    //     0xae357c: stur            w1, [x0, #0x2f]
    // 0xae3580: ldur            x1, [fp, #-0x38]
    // 0xae3584: StoreField: r0->field_33 = r1
    //     0xae3584: stur            w1, [x0, #0x33]
    // 0xae3588: ldur            x1, [fp, #-8]
    // 0xae358c: StoreField: r0->field_37 = r1
    //     0xae358c: stur            w1, [x0, #0x37]
    // 0xae3590: ldur            x1, [fp, #-0x30]
    // 0xae3594: StoreField: r0->field_3b = r1
    //     0xae3594: stur            w1, [x0, #0x3b]
    // 0xae3598: ldur            x1, [fp, #-0x28]
    // 0xae359c: StoreField: r0->field_3f = r1
    //     0xae359c: stur            w1, [x0, #0x3f]
    // 0xae35a0: ldur            x1, [fp, #-0x20]
    // 0xae35a4: StoreField: r0->field_43 = r1
    //     0xae35a4: stur            w1, [x0, #0x43]
    // 0xae35a8: ldur            x2, [fp, #-0x18]
    // 0xae35ac: r1 = Function '<anonymous closure>':.
    //     0xae35ac: add             x1, PP, #0x58, lsl #12  ; [pp+0x583b8] AnonymousClosure: (0xae3604), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::build (0xae3430)
    //     0xae35b0: ldr             x1, [x1, #0x3b8]
    // 0xae35b4: r0 = AllocateClosure()
    //     0xae35b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae35b8: mov             x1, x0
    // 0xae35bc: ldur            x0, [fp, #-0x50]
    // 0xae35c0: StoreField: r0->field_47 = r1
    //     0xae35c0: stur            w1, [x0, #0x47]
    // 0xae35c4: LeaveFrame
    //     0xae35c4: mov             SP, fp
    //     0xae35c8: ldp             fp, lr, [SP], #0x10
    // 0xae35cc: ret
    //     0xae35cc: ret             
    // 0xae35d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae35d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae35d4: b               #0xae3458
    // 0xae35d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae35d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae35dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae35dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, dynamic, int) {
    // ** addr: 0xae3604, size: 0x80
    // 0xae3604: EnterFrame
    //     0xae3604: stp             fp, lr, [SP, #-0x10]!
    //     0xae3608: mov             fp, SP
    // 0xae360c: AllocStack(0x8)
    //     0xae360c: sub             SP, SP, #8
    // 0xae3610: SetupParameters()
    //     0xae3610: ldr             x0, [fp, #0x20]
    //     0xae3614: ldur            w1, [x0, #0x17]
    //     0xae3618: add             x1, x1, HEAP, lsl #32
    // 0xae361c: CheckStackOverflow
    //     0xae361c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3620: cmp             SP, x16
    //     0xae3624: b.ls            #0xae367c
    // 0xae3628: LoadField: r3 = r1->field_f
    //     0xae3628: ldur            w3, [x1, #0xf]
    // 0xae362c: DecompressPointer r3
    //     0xae362c: add             x3, x3, HEAP, lsl #32
    // 0xae3630: ldr             x0, [fp, #0x18]
    // 0xae3634: stur            x3, [fp, #-8]
    // 0xae3638: r2 = Null
    //     0xae3638: mov             x2, NULL
    // 0xae363c: r1 = Null
    //     0xae363c: mov             x1, NULL
    // 0xae3640: r8 = List<Entity>
    //     0xae3640: add             x8, PP, #0x53, lsl #12  ; [pp+0x538e0] Type: List<Entity>
    //     0xae3644: ldr             x8, [x8, #0x8e0]
    // 0xae3648: r3 = Null
    //     0xae3648: add             x3, PP, #0x58, lsl #12  ; [pp+0x583c0] Null
    //     0xae364c: ldr             x3, [x3, #0x3c0]
    // 0xae3650: r0 = List<Entity>()
    //     0xae3650: bl              #0xa43098  ; IsType_List<Entity>_Stub
    // 0xae3654: ldr             x0, [fp, #0x10]
    // 0xae3658: r3 = LoadInt32Instr(r0)
    //     0xae3658: sbfx            x3, x0, #1, #0x1f
    //     0xae365c: tbz             w0, #0, #0xae3664
    //     0xae3660: ldur            x3, [x0, #7]
    // 0xae3664: ldur            x1, [fp, #-8]
    // 0xae3668: ldr             x2, [fp, #0x18]
    // 0xae366c: r0 = bannerSlider()
    //     0xae366c: bl              #0xae3684  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::bannerSlider
    // 0xae3670: LeaveFrame
    //     0xae3670: mov             SP, fp
    //     0xae3674: ldp             fp, lr, [SP], #0x10
    // 0xae3678: ret
    //     0xae3678: ret             
    // 0xae367c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae367c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3680: b               #0xae3628
  }
  _ bannerSlider(/* No info */) {
    // ** addr: 0xae3684, size: 0x844
    // 0xae3684: EnterFrame
    //     0xae3684: stp             fp, lr, [SP, #-0x10]!
    //     0xae3688: mov             fp, SP
    // 0xae368c: AllocStack(0x78)
    //     0xae368c: sub             SP, SP, #0x78
    // 0xae3690: SetupParameters(_CarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xae3690: stur            x1, [fp, #-8]
    //     0xae3694: stur            x2, [fp, #-0x10]
    //     0xae3698: stur            x3, [fp, #-0x18]
    // 0xae369c: CheckStackOverflow
    //     0xae369c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae36a0: cmp             SP, x16
    //     0xae36a4: b.ls            #0xae3e94
    // 0xae36a8: r1 = 2
    //     0xae36a8: movz            x1, #0x2
    // 0xae36ac: r0 = AllocateContext()
    //     0xae36ac: bl              #0x16f6108  ; AllocateContextStub
    // 0xae36b0: mov             x3, x0
    // 0xae36b4: ldur            x2, [fp, #-8]
    // 0xae36b8: stur            x3, [fp, #-0x20]
    // 0xae36bc: StoreField: r3->field_f = r2
    //     0xae36bc: stur            w2, [x3, #0xf]
    // 0xae36c0: ldur            x4, [fp, #-0x18]
    // 0xae36c4: r0 = BoxInt64Instr(r4)
    //     0xae36c4: sbfiz           x0, x4, #1, #0x1f
    //     0xae36c8: cmp             x4, x0, asr #1
    //     0xae36cc: b.eq            #0xae36d8
    //     0xae36d0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xae36d4: stur            x4, [x0, #7]
    // 0xae36d8: StoreField: r3->field_13 = r0
    //     0xae36d8: stur            w0, [x3, #0x13]
    // 0xae36dc: LoadField: r1 = r2->field_f
    //     0xae36dc: ldur            w1, [x2, #0xf]
    // 0xae36e0: DecompressPointer r1
    //     0xae36e0: add             x1, x1, HEAP, lsl #32
    // 0xae36e4: cmp             w1, NULL
    // 0xae36e8: b.eq            #0xae3e9c
    // 0xae36ec: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae36ec: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae36f0: r0 = _of()
    //     0xae36f0: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae36f4: LoadField: r1 = r0->field_7
    //     0xae36f4: ldur            w1, [x0, #7]
    // 0xae36f8: DecompressPointer r1
    //     0xae36f8: add             x1, x1, HEAP, lsl #32
    // 0xae36fc: LoadField: d0 = r1->field_7
    //     0xae36fc: ldur            d0, [x1, #7]
    // 0xae3700: ldur            x1, [fp, #-8]
    // 0xae3704: stur            d0, [fp, #-0x60]
    // 0xae3708: LoadField: r0 = r1->field_b
    //     0xae3708: ldur            w0, [x1, #0xb]
    // 0xae370c: DecompressPointer r0
    //     0xae370c: add             x0, x0, HEAP, lsl #32
    // 0xae3710: cmp             w0, NULL
    // 0xae3714: b.eq            #0xae3ea0
    // 0xae3718: LoadField: r2 = r0->field_2f
    //     0xae3718: ldur            w2, [x0, #0x2f]
    // 0xae371c: DecompressPointer r2
    //     0xae371c: add             x2, x2, HEAP, lsl #32
    // 0xae3720: stur            x2, [fp, #-0x30]
    // 0xae3724: LoadField: r3 = r0->field_33
    //     0xae3724: ldur            w3, [x0, #0x33]
    // 0xae3728: DecompressPointer r3
    //     0xae3728: add             x3, x3, HEAP, lsl #32
    // 0xae372c: ldur            x4, [fp, #-0x20]
    // 0xae3730: stur            x3, [fp, #-0x28]
    // 0xae3734: LoadField: r0 = r4->field_13
    //     0xae3734: ldur            w0, [x4, #0x13]
    // 0xae3738: DecompressPointer r0
    //     0xae3738: add             x0, x0, HEAP, lsl #32
    // 0xae373c: ldur            x5, [fp, #-0x10]
    // 0xae3740: r6 = LoadClassIdInstr(r5)
    //     0xae3740: ldur            x6, [x5, #-1]
    //     0xae3744: ubfx            x6, x6, #0xc, #0x14
    // 0xae3748: stp             x0, x5, [SP]
    // 0xae374c: mov             x0, x6
    // 0xae3750: r0 = GDT[cid_x0 + -0xb7]()
    //     0xae3750: sub             lr, x0, #0xb7
    //     0xae3754: ldr             lr, [x21, lr, lsl #3]
    //     0xae3758: blr             lr
    // 0xae375c: LoadField: r1 = r0->field_13
    //     0xae375c: ldur            w1, [x0, #0x13]
    // 0xae3760: DecompressPointer r1
    //     0xae3760: add             x1, x1, HEAP, lsl #32
    // 0xae3764: cmp             w1, NULL
    // 0xae3768: b.ne            #0xae3774
    // 0xae376c: r5 = ""
    //     0xae376c: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae3770: b               #0xae3778
    // 0xae3774: mov             x5, x1
    // 0xae3778: ldur            x4, [fp, #-0x10]
    // 0xae377c: ldur            x3, [fp, #-0x20]
    // 0xae3780: ldur            x0, [fp, #-0x28]
    // 0xae3784: ldur            d0, [fp, #-0x60]
    // 0xae3788: stur            x5, [fp, #-0x38]
    // 0xae378c: r1 = Function '<anonymous closure>':.
    //     0xae378c: add             x1, PP, #0x58, lsl #12  ; [pp+0x583d0] AnonymousClosure: (0x8fc578), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xae3790: ldr             x1, [x1, #0x3d0]
    // 0xae3794: r2 = Null
    //     0xae3794: mov             x2, NULL
    // 0xae3798: r0 = AllocateClosure()
    //     0xae3798: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae379c: r1 = Function '<anonymous closure>':.
    //     0xae379c: add             x1, PP, #0x58, lsl #12  ; [pp+0x583d8] AnonymousClosure: (0xa422dc), in [package:customer_app/app/presentation/views/glass/product_detail/widgets/product_banner_cross_link.dart] _ProductBannerCrossLinkState::bannerSlider (0xa42328)
    //     0xae37a0: ldr             x1, [x1, #0x3d8]
    // 0xae37a4: r2 = Null
    //     0xae37a4: mov             x2, NULL
    // 0xae37a8: stur            x0, [fp, #-0x40]
    // 0xae37ac: r0 = AllocateClosure()
    //     0xae37ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae37b0: stur            x0, [fp, #-0x48]
    // 0xae37b4: r0 = CachedNetworkImage()
    //     0xae37b4: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xae37b8: stur            x0, [fp, #-0x50]
    // 0xae37bc: r16 = Instance_BoxFit
    //     0xae37bc: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0xae37c0: ldr             x16, [x16, #0x118]
    // 0xae37c4: ldur            lr, [fp, #-0x40]
    // 0xae37c8: stp             lr, x16, [SP, #8]
    // 0xae37cc: ldur            x16, [fp, #-0x48]
    // 0xae37d0: str             x16, [SP]
    // 0xae37d4: mov             x1, x0
    // 0xae37d8: ldur            x2, [fp, #-0x38]
    // 0xae37dc: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xae37dc: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xae37e0: ldr             x4, [x4, #0x638]
    // 0xae37e4: r0 = CachedNetworkImage()
    //     0xae37e4: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xae37e8: r0 = ClipRRect()
    //     0xae37e8: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xae37ec: mov             x1, x0
    // 0xae37f0: ldur            x0, [fp, #-0x28]
    // 0xae37f4: stur            x1, [fp, #-0x38]
    // 0xae37f8: StoreField: r1->field_f = r0
    //     0xae37f8: stur            w0, [x1, #0xf]
    // 0xae37fc: r0 = Instance_Clip
    //     0xae37fc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xae3800: ldr             x0, [x0, #0x138]
    // 0xae3804: ArrayStore: r1[0] = r0  ; List_4
    //     0xae3804: stur            w0, [x1, #0x17]
    // 0xae3808: ldur            x0, [fp, #-0x50]
    // 0xae380c: StoreField: r1->field_b = r0
    //     0xae380c: stur            w0, [x1, #0xb]
    // 0xae3810: ldur            d0, [fp, #-0x60]
    // 0xae3814: r0 = inline_Allocate_Double()
    //     0xae3814: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae3818: add             x0, x0, #0x10
    //     0xae381c: cmp             x2, x0
    //     0xae3820: b.ls            #0xae3ea4
    //     0xae3824: str             x0, [THR, #0x50]  ; THR::top
    //     0xae3828: sub             x0, x0, #0xf
    //     0xae382c: movz            x2, #0xe15c
    //     0xae3830: movk            x2, #0x3, lsl #16
    //     0xae3834: stur            x2, [x0, #-1]
    // 0xae3838: StoreField: r0->field_7 = d0
    //     0xae3838: stur            d0, [x0, #7]
    // 0xae383c: stur            x0, [fp, #-0x28]
    // 0xae3840: r0 = Container()
    //     0xae3840: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xae3844: stur            x0, [fp, #-0x40]
    // 0xae3848: ldur            x16, [fp, #-0x28]
    // 0xae384c: ldur            lr, [fp, #-0x30]
    // 0xae3850: stp             lr, x16, [SP, #8]
    // 0xae3854: ldur            x16, [fp, #-0x38]
    // 0xae3858: str             x16, [SP]
    // 0xae385c: mov             x1, x0
    // 0xae3860: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, margin, 0x2, width, 0x1, null]
    //     0xae3860: add             x4, PP, #0x42, lsl #12  ; [pp+0x42628] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "margin", 0x2, "width", 0x1, Null]
    //     0xae3864: ldr             x4, [x4, #0x628]
    // 0xae3868: r0 = Container()
    //     0xae3868: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae386c: ldur            x2, [fp, #-0x20]
    // 0xae3870: LoadField: r0 = r2->field_13
    //     0xae3870: ldur            w0, [x2, #0x13]
    // 0xae3874: DecompressPointer r0
    //     0xae3874: add             x0, x0, HEAP, lsl #32
    // 0xae3878: ldur            x1, [fp, #-0x10]
    // 0xae387c: r3 = LoadClassIdInstr(r1)
    //     0xae387c: ldur            x3, [x1, #-1]
    //     0xae3880: ubfx            x3, x3, #0xc, #0x14
    // 0xae3884: stp             x0, x1, [SP]
    // 0xae3888: mov             x0, x3
    // 0xae388c: r0 = GDT[cid_x0 + -0xb7]()
    //     0xae388c: sub             lr, x0, #0xb7
    //     0xae3890: ldr             lr, [x21, lr, lsl #3]
    //     0xae3894: blr             lr
    // 0xae3898: LoadField: r1 = r0->field_7
    //     0xae3898: ldur            w1, [x0, #7]
    // 0xae389c: DecompressPointer r1
    //     0xae389c: add             x1, x1, HEAP, lsl #32
    // 0xae38a0: cmp             w1, NULL
    // 0xae38a4: b.ne            #0xae38b0
    // 0xae38a8: r0 = Null
    //     0xae38a8: mov             x0, NULL
    // 0xae38ac: b               #0xae38c8
    // 0xae38b0: LoadField: r0 = r1->field_7
    //     0xae38b0: ldur            w0, [x1, #7]
    // 0xae38b4: cbnz            w0, #0xae38c0
    // 0xae38b8: r1 = false
    //     0xae38b8: add             x1, NULL, #0x30  ; false
    // 0xae38bc: b               #0xae38c4
    // 0xae38c0: r1 = true
    //     0xae38c0: add             x1, NULL, #0x20  ; true
    // 0xae38c4: mov             x0, x1
    // 0xae38c8: cmp             w0, NULL
    // 0xae38cc: b.ne            #0xae38d8
    // 0xae38d0: r3 = false
    //     0xae38d0: add             x3, NULL, #0x30  ; false
    // 0xae38d4: b               #0xae38dc
    // 0xae38d8: mov             x3, x0
    // 0xae38dc: ldur            x1, [fp, #-0x10]
    // 0xae38e0: ldur            x2, [fp, #-0x20]
    // 0xae38e4: stur            x3, [fp, #-0x28]
    // 0xae38e8: LoadField: r0 = r2->field_13
    //     0xae38e8: ldur            w0, [x2, #0x13]
    // 0xae38ec: DecompressPointer r0
    //     0xae38ec: add             x0, x0, HEAP, lsl #32
    // 0xae38f0: r4 = LoadClassIdInstr(r1)
    //     0xae38f0: ldur            x4, [x1, #-1]
    //     0xae38f4: ubfx            x4, x4, #0xc, #0x14
    // 0xae38f8: stp             x0, x1, [SP]
    // 0xae38fc: mov             x0, x4
    // 0xae3900: r0 = GDT[cid_x0 + -0xb7]()
    //     0xae3900: sub             lr, x0, #0xb7
    //     0xae3904: ldr             lr, [x21, lr, lsl #3]
    //     0xae3908: blr             lr
    // 0xae390c: LoadField: r1 = r0->field_7
    //     0xae390c: ldur            w1, [x0, #7]
    // 0xae3910: DecompressPointer r1
    //     0xae3910: add             x1, x1, HEAP, lsl #32
    // 0xae3914: cmp             w1, NULL
    // 0xae3918: b.ne            #0xae3924
    // 0xae391c: r4 = ""
    //     0xae391c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae3920: b               #0xae3928
    // 0xae3924: mov             x4, x1
    // 0xae3928: ldur            x3, [fp, #-8]
    // 0xae392c: ldur            x0, [fp, #-0x10]
    // 0xae3930: ldur            x2, [fp, #-0x20]
    // 0xae3934: stur            x4, [fp, #-0x30]
    // 0xae3938: LoadField: r1 = r3->field_f
    //     0xae3938: ldur            w1, [x3, #0xf]
    // 0xae393c: DecompressPointer r1
    //     0xae393c: add             x1, x1, HEAP, lsl #32
    // 0xae3940: cmp             w1, NULL
    // 0xae3944: b.eq            #0xae3ebc
    // 0xae3948: r0 = of()
    //     0xae3948: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae394c: LoadField: r1 = r0->field_87
    //     0xae394c: ldur            w1, [x0, #0x87]
    // 0xae3950: DecompressPointer r1
    //     0xae3950: add             x1, x1, HEAP, lsl #32
    // 0xae3954: LoadField: r0 = r1->field_27
    //     0xae3954: ldur            w0, [x1, #0x27]
    // 0xae3958: DecompressPointer r0
    //     0xae3958: add             x0, x0, HEAP, lsl #32
    // 0xae395c: r16 = 21.000000
    //     0xae395c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9b0] 21
    //     0xae3960: ldr             x16, [x16, #0x9b0]
    // 0xae3964: r30 = Instance_Color
    //     0xae3964: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xae3968: stp             lr, x16, [SP]
    // 0xae396c: mov             x1, x0
    // 0xae3970: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xae3970: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xae3974: ldr             x4, [x4, #0xaa0]
    // 0xae3978: r0 = copyWith()
    //     0xae3978: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae397c: stur            x0, [fp, #-0x38]
    // 0xae3980: r0 = Text()
    //     0xae3980: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae3984: mov             x1, x0
    // 0xae3988: ldur            x0, [fp, #-0x30]
    // 0xae398c: stur            x1, [fp, #-0x48]
    // 0xae3990: StoreField: r1->field_b = r0
    //     0xae3990: stur            w0, [x1, #0xb]
    // 0xae3994: ldur            x0, [fp, #-0x38]
    // 0xae3998: StoreField: r1->field_13 = r0
    //     0xae3998: stur            w0, [x1, #0x13]
    // 0xae399c: r0 = Instance_TextOverflow
    //     0xae399c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xae39a0: ldr             x0, [x0, #0xe10]
    // 0xae39a4: StoreField: r1->field_2b = r0
    //     0xae39a4: stur            w0, [x1, #0x2b]
    // 0xae39a8: r2 = 2
    //     0xae39a8: movz            x2, #0x2
    // 0xae39ac: StoreField: r1->field_37 = r2
    //     0xae39ac: stur            w2, [x1, #0x37]
    // 0xae39b0: r0 = Padding()
    //     0xae39b0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae39b4: mov             x1, x0
    // 0xae39b8: r0 = Instance_EdgeInsets
    //     0xae39b8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f078] Obj!EdgeInsets@d571a1
    //     0xae39bc: ldr             x0, [x0, #0x78]
    // 0xae39c0: stur            x1, [fp, #-0x30]
    // 0xae39c4: StoreField: r1->field_f = r0
    //     0xae39c4: stur            w0, [x1, #0xf]
    // 0xae39c8: ldur            x0, [fp, #-0x48]
    // 0xae39cc: StoreField: r1->field_b = r0
    //     0xae39cc: stur            w0, [x1, #0xb]
    // 0xae39d0: ldur            x2, [fp, #-0x20]
    // 0xae39d4: LoadField: r0 = r2->field_13
    //     0xae39d4: ldur            w0, [x2, #0x13]
    // 0xae39d8: DecompressPointer r0
    //     0xae39d8: add             x0, x0, HEAP, lsl #32
    // 0xae39dc: ldur            x3, [fp, #-0x10]
    // 0xae39e0: r4 = LoadClassIdInstr(r3)
    //     0xae39e0: ldur            x4, [x3, #-1]
    //     0xae39e4: ubfx            x4, x4, #0xc, #0x14
    // 0xae39e8: stp             x0, x3, [SP]
    // 0xae39ec: mov             x0, x4
    // 0xae39f0: r0 = GDT[cid_x0 + -0xb7]()
    //     0xae39f0: sub             lr, x0, #0xb7
    //     0xae39f4: ldr             lr, [x21, lr, lsl #3]
    //     0xae39f8: blr             lr
    // 0xae39fc: LoadField: r1 = r0->field_f
    //     0xae39fc: ldur            w1, [x0, #0xf]
    // 0xae3a00: DecompressPointer r1
    //     0xae3a00: add             x1, x1, HEAP, lsl #32
    // 0xae3a04: cmp             w1, NULL
    // 0xae3a08: b.ne            #0xae3a14
    // 0xae3a0c: r0 = Null
    //     0xae3a0c: mov             x0, NULL
    // 0xae3a10: b               #0xae3a2c
    // 0xae3a14: LoadField: r0 = r1->field_7
    //     0xae3a14: ldur            w0, [x1, #7]
    // 0xae3a18: cbnz            w0, #0xae3a24
    // 0xae3a1c: r1 = false
    //     0xae3a1c: add             x1, NULL, #0x30  ; false
    // 0xae3a20: b               #0xae3a28
    // 0xae3a24: r1 = true
    //     0xae3a24: add             x1, NULL, #0x20  ; true
    // 0xae3a28: mov             x0, x1
    // 0xae3a2c: cmp             w0, NULL
    // 0xae3a30: b.ne            #0xae3a3c
    // 0xae3a34: r1 = false
    //     0xae3a34: add             x1, NULL, #0x30  ; false
    // 0xae3a38: b               #0xae3a40
    // 0xae3a3c: mov             x1, x0
    // 0xae3a40: ldur            x0, [fp, #-0x10]
    // 0xae3a44: ldur            x2, [fp, #-0x20]
    // 0xae3a48: stur            x1, [fp, #-0x38]
    // 0xae3a4c: r16 = <Color>
    //     0xae3a4c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xae3a50: ldr             x16, [x16, #0xf80]
    // 0xae3a54: r30 = Instance_Color
    //     0xae3a54: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xae3a58: stp             lr, x16, [SP]
    // 0xae3a5c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae3a5c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae3a60: r0 = all()
    //     0xae3a60: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae3a64: stur            x0, [fp, #-0x48]
    // 0xae3a68: r16 = <RoundedRectangleBorder>
    //     0xae3a68: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xae3a6c: ldr             x16, [x16, #0xf78]
    // 0xae3a70: r30 = Instance_RoundedRectangleBorder
    //     0xae3a70: add             lr, PP, #0x57, lsl #12  ; [pp+0x57e30] Obj!RoundedRectangleBorder@d5ac21
    //     0xae3a74: ldr             lr, [lr, #0xe30]
    // 0xae3a78: stp             lr, x16, [SP]
    // 0xae3a7c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae3a7c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae3a80: r0 = all()
    //     0xae3a80: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xae3a84: stur            x0, [fp, #-0x50]
    // 0xae3a88: r0 = ButtonStyle()
    //     0xae3a88: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xae3a8c: mov             x1, x0
    // 0xae3a90: ldur            x0, [fp, #-0x48]
    // 0xae3a94: stur            x1, [fp, #-0x58]
    // 0xae3a98: StoreField: r1->field_b = r0
    //     0xae3a98: stur            w0, [x1, #0xb]
    // 0xae3a9c: ldur            x0, [fp, #-0x50]
    // 0xae3aa0: StoreField: r1->field_43 = r0
    //     0xae3aa0: stur            w0, [x1, #0x43]
    // 0xae3aa4: r0 = TextButtonThemeData()
    //     0xae3aa4: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xae3aa8: mov             x1, x0
    // 0xae3aac: ldur            x0, [fp, #-0x58]
    // 0xae3ab0: stur            x1, [fp, #-0x48]
    // 0xae3ab4: StoreField: r1->field_7 = r0
    //     0xae3ab4: stur            w0, [x1, #7]
    // 0xae3ab8: ldur            x2, [fp, #-0x20]
    // 0xae3abc: LoadField: r0 = r2->field_13
    //     0xae3abc: ldur            w0, [x2, #0x13]
    // 0xae3ac0: DecompressPointer r0
    //     0xae3ac0: add             x0, x0, HEAP, lsl #32
    // 0xae3ac4: ldur            x3, [fp, #-0x10]
    // 0xae3ac8: r4 = LoadClassIdInstr(r3)
    //     0xae3ac8: ldur            x4, [x3, #-1]
    //     0xae3acc: ubfx            x4, x4, #0xc, #0x14
    // 0xae3ad0: stp             x0, x3, [SP]
    // 0xae3ad4: mov             x0, x4
    // 0xae3ad8: r0 = GDT[cid_x0 + -0xb7]()
    //     0xae3ad8: sub             lr, x0, #0xb7
    //     0xae3adc: ldr             lr, [x21, lr, lsl #3]
    //     0xae3ae0: blr             lr
    // 0xae3ae4: LoadField: r1 = r0->field_f
    //     0xae3ae4: ldur            w1, [x0, #0xf]
    // 0xae3ae8: DecompressPointer r1
    //     0xae3ae8: add             x1, x1, HEAP, lsl #32
    // 0xae3aec: cmp             w1, NULL
    // 0xae3af0: b.ne            #0xae3afc
    // 0xae3af4: r7 = ""
    //     0xae3af4: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xae3af8: b               #0xae3b00
    // 0xae3afc: mov             x7, x1
    // 0xae3b00: ldur            x4, [fp, #-8]
    // 0xae3b04: ldur            x6, [fp, #-0x40]
    // 0xae3b08: ldur            x5, [fp, #-0x28]
    // 0xae3b0c: ldur            x3, [fp, #-0x30]
    // 0xae3b10: ldur            x2, [fp, #-0x38]
    // 0xae3b14: ldur            x0, [fp, #-0x48]
    // 0xae3b18: stur            x7, [fp, #-0x10]
    // 0xae3b1c: LoadField: r1 = r4->field_f
    //     0xae3b1c: ldur            w1, [x4, #0xf]
    // 0xae3b20: DecompressPointer r1
    //     0xae3b20: add             x1, x1, HEAP, lsl #32
    // 0xae3b24: cmp             w1, NULL
    // 0xae3b28: b.eq            #0xae3ec0
    // 0xae3b2c: r0 = of()
    //     0xae3b2c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae3b30: LoadField: r1 = r0->field_87
    //     0xae3b30: ldur            w1, [x0, #0x87]
    // 0xae3b34: DecompressPointer r1
    //     0xae3b34: add             x1, x1, HEAP, lsl #32
    // 0xae3b38: LoadField: r0 = r1->field_7
    //     0xae3b38: ldur            w0, [x1, #7]
    // 0xae3b3c: DecompressPointer r0
    //     0xae3b3c: add             x0, x0, HEAP, lsl #32
    // 0xae3b40: ldur            x1, [fp, #-8]
    // 0xae3b44: stur            x0, [fp, #-0x50]
    // 0xae3b48: LoadField: r2 = r1->field_f
    //     0xae3b48: ldur            w2, [x1, #0xf]
    // 0xae3b4c: DecompressPointer r2
    //     0xae3b4c: add             x2, x2, HEAP, lsl #32
    // 0xae3b50: cmp             w2, NULL
    // 0xae3b54: b.eq            #0xae3ec4
    // 0xae3b58: mov             x1, x2
    // 0xae3b5c: r0 = of()
    //     0xae3b5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xae3b60: LoadField: r1 = r0->field_5b
    //     0xae3b60: ldur            w1, [x0, #0x5b]
    // 0xae3b64: DecompressPointer r1
    //     0xae3b64: add             x1, x1, HEAP, lsl #32
    // 0xae3b68: r16 = 16.000000
    //     0xae3b68: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xae3b6c: ldr             x16, [x16, #0x188]
    // 0xae3b70: stp             x16, x1, [SP]
    // 0xae3b74: ldur            x1, [fp, #-0x50]
    // 0xae3b78: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xae3b78: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xae3b7c: ldr             x4, [x4, #0x9b8]
    // 0xae3b80: r0 = copyWith()
    //     0xae3b80: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae3b84: stur            x0, [fp, #-8]
    // 0xae3b88: r0 = Text()
    //     0xae3b88: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xae3b8c: mov             x1, x0
    // 0xae3b90: ldur            x0, [fp, #-0x10]
    // 0xae3b94: stur            x1, [fp, #-0x50]
    // 0xae3b98: StoreField: r1->field_b = r0
    //     0xae3b98: stur            w0, [x1, #0xb]
    // 0xae3b9c: ldur            x0, [fp, #-8]
    // 0xae3ba0: StoreField: r1->field_13 = r0
    //     0xae3ba0: stur            w0, [x1, #0x13]
    // 0xae3ba4: r0 = Instance_TextOverflow
    //     0xae3ba4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xae3ba8: ldr             x0, [x0, #0xe10]
    // 0xae3bac: StoreField: r1->field_2b = r0
    //     0xae3bac: stur            w0, [x1, #0x2b]
    // 0xae3bb0: r2 = 2
    //     0xae3bb0: movz            x2, #0x2
    // 0xae3bb4: StoreField: r1->field_37 = r2
    //     0xae3bb4: stur            w2, [x1, #0x37]
    // 0xae3bb8: r0 = Padding()
    //     0xae3bb8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae3bbc: mov             x3, x0
    // 0xae3bc0: r0 = Instance_EdgeInsets
    //     0xae3bc0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36c10] Obj!EdgeInsets@d57c81
    //     0xae3bc4: ldr             x0, [x0, #0xc10]
    // 0xae3bc8: stur            x3, [fp, #-8]
    // 0xae3bcc: StoreField: r3->field_f = r0
    //     0xae3bcc: stur            w0, [x3, #0xf]
    // 0xae3bd0: ldur            x0, [fp, #-0x50]
    // 0xae3bd4: StoreField: r3->field_b = r0
    //     0xae3bd4: stur            w0, [x3, #0xb]
    // 0xae3bd8: ldur            x2, [fp, #-0x20]
    // 0xae3bdc: r1 = Function '<anonymous closure>':.
    //     0xae3bdc: add             x1, PP, #0x58, lsl #12  ; [pp+0x583e0] AnonymousClosure: (0xae3ec8), in [package:customer_app/app/presentation/views/cosmetic/home/<USER>/banner_cross_link.dart] _CarouselItemViewState::bannerSlider (0xae3684)
    //     0xae3be0: ldr             x1, [x1, #0x3e0]
    // 0xae3be4: r0 = AllocateClosure()
    //     0xae3be4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xae3be8: stur            x0, [fp, #-0x10]
    // 0xae3bec: r0 = TextButton()
    //     0xae3bec: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xae3bf0: mov             x1, x0
    // 0xae3bf4: ldur            x0, [fp, #-0x10]
    // 0xae3bf8: stur            x1, [fp, #-0x20]
    // 0xae3bfc: StoreField: r1->field_b = r0
    //     0xae3bfc: stur            w0, [x1, #0xb]
    // 0xae3c00: r0 = false
    //     0xae3c00: add             x0, NULL, #0x30  ; false
    // 0xae3c04: StoreField: r1->field_27 = r0
    //     0xae3c04: stur            w0, [x1, #0x27]
    // 0xae3c08: r2 = true
    //     0xae3c08: add             x2, NULL, #0x20  ; true
    // 0xae3c0c: StoreField: r1->field_2f = r2
    //     0xae3c0c: stur            w2, [x1, #0x2f]
    // 0xae3c10: ldur            x2, [fp, #-8]
    // 0xae3c14: StoreField: r1->field_37 = r2
    //     0xae3c14: stur            w2, [x1, #0x37]
    // 0xae3c18: r0 = TextButtonTheme()
    //     0xae3c18: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xae3c1c: mov             x1, x0
    // 0xae3c20: ldur            x0, [fp, #-0x48]
    // 0xae3c24: stur            x1, [fp, #-8]
    // 0xae3c28: StoreField: r1->field_f = r0
    //     0xae3c28: stur            w0, [x1, #0xf]
    // 0xae3c2c: ldur            x0, [fp, #-0x20]
    // 0xae3c30: StoreField: r1->field_b = r0
    //     0xae3c30: stur            w0, [x1, #0xb]
    // 0xae3c34: r0 = Visibility()
    //     0xae3c34: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xae3c38: mov             x3, x0
    // 0xae3c3c: ldur            x0, [fp, #-8]
    // 0xae3c40: stur            x3, [fp, #-0x10]
    // 0xae3c44: StoreField: r3->field_b = r0
    //     0xae3c44: stur            w0, [x3, #0xb]
    // 0xae3c48: r0 = Instance_SizedBox
    //     0xae3c48: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xae3c4c: StoreField: r3->field_f = r0
    //     0xae3c4c: stur            w0, [x3, #0xf]
    // 0xae3c50: ldur            x1, [fp, #-0x38]
    // 0xae3c54: StoreField: r3->field_13 = r1
    //     0xae3c54: stur            w1, [x3, #0x13]
    // 0xae3c58: r4 = false
    //     0xae3c58: add             x4, NULL, #0x30  ; false
    // 0xae3c5c: ArrayStore: r3[0] = r4  ; List_4
    //     0xae3c5c: stur            w4, [x3, #0x17]
    // 0xae3c60: StoreField: r3->field_1b = r4
    //     0xae3c60: stur            w4, [x3, #0x1b]
    // 0xae3c64: StoreField: r3->field_1f = r4
    //     0xae3c64: stur            w4, [x3, #0x1f]
    // 0xae3c68: StoreField: r3->field_23 = r4
    //     0xae3c68: stur            w4, [x3, #0x23]
    // 0xae3c6c: StoreField: r3->field_27 = r4
    //     0xae3c6c: stur            w4, [x3, #0x27]
    // 0xae3c70: StoreField: r3->field_2b = r4
    //     0xae3c70: stur            w4, [x3, #0x2b]
    // 0xae3c74: r1 = Null
    //     0xae3c74: mov             x1, NULL
    // 0xae3c78: r2 = 4
    //     0xae3c78: movz            x2, #0x4
    // 0xae3c7c: r0 = AllocateArray()
    //     0xae3c7c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae3c80: mov             x2, x0
    // 0xae3c84: ldur            x0, [fp, #-0x30]
    // 0xae3c88: stur            x2, [fp, #-8]
    // 0xae3c8c: StoreField: r2->field_f = r0
    //     0xae3c8c: stur            w0, [x2, #0xf]
    // 0xae3c90: ldur            x0, [fp, #-0x10]
    // 0xae3c94: StoreField: r2->field_13 = r0
    //     0xae3c94: stur            w0, [x2, #0x13]
    // 0xae3c98: r1 = <Widget>
    //     0xae3c98: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae3c9c: r0 = AllocateGrowableArray()
    //     0xae3c9c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae3ca0: mov             x1, x0
    // 0xae3ca4: ldur            x0, [fp, #-8]
    // 0xae3ca8: stur            x1, [fp, #-0x10]
    // 0xae3cac: StoreField: r1->field_f = r0
    //     0xae3cac: stur            w0, [x1, #0xf]
    // 0xae3cb0: r2 = 4
    //     0xae3cb0: movz            x2, #0x4
    // 0xae3cb4: StoreField: r1->field_b = r2
    //     0xae3cb4: stur            w2, [x1, #0xb]
    // 0xae3cb8: r0 = Column()
    //     0xae3cb8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xae3cbc: mov             x3, x0
    // 0xae3cc0: r0 = Instance_Axis
    //     0xae3cc0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xae3cc4: stur            x3, [fp, #-8]
    // 0xae3cc8: StoreField: r3->field_f = r0
    //     0xae3cc8: stur            w0, [x3, #0xf]
    // 0xae3ccc: r0 = Instance_MainAxisAlignment
    //     0xae3ccc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xae3cd0: ldr             x0, [x0, #0xab0]
    // 0xae3cd4: StoreField: r3->field_13 = r0
    //     0xae3cd4: stur            w0, [x3, #0x13]
    // 0xae3cd8: r0 = Instance_MainAxisSize
    //     0xae3cd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xae3cdc: ldr             x0, [x0, #0xa10]
    // 0xae3ce0: ArrayStore: r3[0] = r0  ; List_4
    //     0xae3ce0: stur            w0, [x3, #0x17]
    // 0xae3ce4: r0 = Instance_CrossAxisAlignment
    //     0xae3ce4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xae3ce8: ldr             x0, [x0, #0xa18]
    // 0xae3cec: StoreField: r3->field_1b = r0
    //     0xae3cec: stur            w0, [x3, #0x1b]
    // 0xae3cf0: r0 = Instance_VerticalDirection
    //     0xae3cf0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xae3cf4: ldr             x0, [x0, #0xa20]
    // 0xae3cf8: StoreField: r3->field_23 = r0
    //     0xae3cf8: stur            w0, [x3, #0x23]
    // 0xae3cfc: r0 = Instance_Clip
    //     0xae3cfc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xae3d00: ldr             x0, [x0, #0x38]
    // 0xae3d04: StoreField: r3->field_2b = r0
    //     0xae3d04: stur            w0, [x3, #0x2b]
    // 0xae3d08: StoreField: r3->field_2f = rZR
    //     0xae3d08: stur            xzr, [x3, #0x2f]
    // 0xae3d0c: ldur            x0, [fp, #-0x10]
    // 0xae3d10: StoreField: r3->field_b = r0
    //     0xae3d10: stur            w0, [x3, #0xb]
    // 0xae3d14: r1 = Null
    //     0xae3d14: mov             x1, NULL
    // 0xae3d18: r2 = 2
    //     0xae3d18: movz            x2, #0x2
    // 0xae3d1c: r0 = AllocateArray()
    //     0xae3d1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae3d20: mov             x2, x0
    // 0xae3d24: ldur            x0, [fp, #-8]
    // 0xae3d28: stur            x2, [fp, #-0x10]
    // 0xae3d2c: StoreField: r2->field_f = r0
    //     0xae3d2c: stur            w0, [x2, #0xf]
    // 0xae3d30: r1 = <Widget>
    //     0xae3d30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae3d34: r0 = AllocateGrowableArray()
    //     0xae3d34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae3d38: mov             x1, x0
    // 0xae3d3c: ldur            x0, [fp, #-0x10]
    // 0xae3d40: stur            x1, [fp, #-8]
    // 0xae3d44: StoreField: r1->field_f = r0
    //     0xae3d44: stur            w0, [x1, #0xf]
    // 0xae3d48: r0 = 2
    //     0xae3d48: movz            x0, #0x2
    // 0xae3d4c: StoreField: r1->field_b = r0
    //     0xae3d4c: stur            w0, [x1, #0xb]
    // 0xae3d50: r0 = Stack()
    //     0xae3d50: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xae3d54: mov             x1, x0
    // 0xae3d58: r0 = Instance_Alignment
    //     0xae3d58: add             x0, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xae3d5c: ldr             x0, [x0, #0xcb0]
    // 0xae3d60: stur            x1, [fp, #-0x10]
    // 0xae3d64: StoreField: r1->field_f = r0
    //     0xae3d64: stur            w0, [x1, #0xf]
    // 0xae3d68: r2 = Instance_StackFit
    //     0xae3d68: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xae3d6c: ldr             x2, [x2, #0xfa8]
    // 0xae3d70: ArrayStore: r1[0] = r2  ; List_4
    //     0xae3d70: stur            w2, [x1, #0x17]
    // 0xae3d74: r3 = Instance_Clip
    //     0xae3d74: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xae3d78: ldr             x3, [x3, #0x7e0]
    // 0xae3d7c: StoreField: r1->field_1b = r3
    //     0xae3d7c: stur            w3, [x1, #0x1b]
    // 0xae3d80: ldur            x4, [fp, #-8]
    // 0xae3d84: StoreField: r1->field_b = r4
    //     0xae3d84: stur            w4, [x1, #0xb]
    // 0xae3d88: r0 = SizedBox()
    //     0xae3d88: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xae3d8c: mov             x1, x0
    // 0xae3d90: r0 = 200.000000
    //     0xae3d90: add             x0, PP, #0x48, lsl #12  ; [pp+0x48570] 200
    //     0xae3d94: ldr             x0, [x0, #0x570]
    // 0xae3d98: stur            x1, [fp, #-8]
    // 0xae3d9c: StoreField: r1->field_f = r0
    //     0xae3d9c: stur            w0, [x1, #0xf]
    // 0xae3da0: r0 = 110.000000
    //     0xae3da0: add             x0, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xae3da4: ldr             x0, [x0, #0x770]
    // 0xae3da8: StoreField: r1->field_13 = r0
    //     0xae3da8: stur            w0, [x1, #0x13]
    // 0xae3dac: ldur            x0, [fp, #-0x10]
    // 0xae3db0: StoreField: r1->field_b = r0
    //     0xae3db0: stur            w0, [x1, #0xb]
    // 0xae3db4: r0 = Padding()
    //     0xae3db4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae3db8: mov             x1, x0
    // 0xae3dbc: r0 = Instance_EdgeInsets
    //     0xae3dbc: add             x0, PP, #0x32, lsl #12  ; [pp+0x32240] Obj!EdgeInsets@d56ff1
    //     0xae3dc0: ldr             x0, [x0, #0x240]
    // 0xae3dc4: stur            x1, [fp, #-0x10]
    // 0xae3dc8: StoreField: r1->field_f = r0
    //     0xae3dc8: stur            w0, [x1, #0xf]
    // 0xae3dcc: ldur            x0, [fp, #-8]
    // 0xae3dd0: StoreField: r1->field_b = r0
    //     0xae3dd0: stur            w0, [x1, #0xb]
    // 0xae3dd4: r0 = Visibility()
    //     0xae3dd4: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0xae3dd8: mov             x3, x0
    // 0xae3ddc: ldur            x0, [fp, #-0x10]
    // 0xae3de0: stur            x3, [fp, #-8]
    // 0xae3de4: StoreField: r3->field_b = r0
    //     0xae3de4: stur            w0, [x3, #0xb]
    // 0xae3de8: r0 = Instance_SizedBox
    //     0xae3de8: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xae3dec: StoreField: r3->field_f = r0
    //     0xae3dec: stur            w0, [x3, #0xf]
    // 0xae3df0: ldur            x0, [fp, #-0x28]
    // 0xae3df4: StoreField: r3->field_13 = r0
    //     0xae3df4: stur            w0, [x3, #0x13]
    // 0xae3df8: r0 = false
    //     0xae3df8: add             x0, NULL, #0x30  ; false
    // 0xae3dfc: ArrayStore: r3[0] = r0  ; List_4
    //     0xae3dfc: stur            w0, [x3, #0x17]
    // 0xae3e00: StoreField: r3->field_1b = r0
    //     0xae3e00: stur            w0, [x3, #0x1b]
    // 0xae3e04: StoreField: r3->field_1f = r0
    //     0xae3e04: stur            w0, [x3, #0x1f]
    // 0xae3e08: StoreField: r3->field_23 = r0
    //     0xae3e08: stur            w0, [x3, #0x23]
    // 0xae3e0c: StoreField: r3->field_27 = r0
    //     0xae3e0c: stur            w0, [x3, #0x27]
    // 0xae3e10: StoreField: r3->field_2b = r0
    //     0xae3e10: stur            w0, [x3, #0x2b]
    // 0xae3e14: r1 = Null
    //     0xae3e14: mov             x1, NULL
    // 0xae3e18: r2 = 4
    //     0xae3e18: movz            x2, #0x4
    // 0xae3e1c: r0 = AllocateArray()
    //     0xae3e1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xae3e20: mov             x2, x0
    // 0xae3e24: ldur            x0, [fp, #-0x40]
    // 0xae3e28: stur            x2, [fp, #-0x10]
    // 0xae3e2c: StoreField: r2->field_f = r0
    //     0xae3e2c: stur            w0, [x2, #0xf]
    // 0xae3e30: ldur            x0, [fp, #-8]
    // 0xae3e34: StoreField: r2->field_13 = r0
    //     0xae3e34: stur            w0, [x2, #0x13]
    // 0xae3e38: r1 = <Widget>
    //     0xae3e38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xae3e3c: r0 = AllocateGrowableArray()
    //     0xae3e3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xae3e40: mov             x1, x0
    // 0xae3e44: ldur            x0, [fp, #-0x10]
    // 0xae3e48: stur            x1, [fp, #-8]
    // 0xae3e4c: StoreField: r1->field_f = r0
    //     0xae3e4c: stur            w0, [x1, #0xf]
    // 0xae3e50: r0 = 4
    //     0xae3e50: movz            x0, #0x4
    // 0xae3e54: StoreField: r1->field_b = r0
    //     0xae3e54: stur            w0, [x1, #0xb]
    // 0xae3e58: r0 = Stack()
    //     0xae3e58: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xae3e5c: r1 = Instance_Alignment
    //     0xae3e5c: add             x1, PP, #0x4b, lsl #12  ; [pp+0x4bcb0] Obj!Alignment@d5a7c1
    //     0xae3e60: ldr             x1, [x1, #0xcb0]
    // 0xae3e64: StoreField: r0->field_f = r1
    //     0xae3e64: stur            w1, [x0, #0xf]
    // 0xae3e68: r1 = Instance_StackFit
    //     0xae3e68: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xae3e6c: ldr             x1, [x1, #0xfa8]
    // 0xae3e70: ArrayStore: r0[0] = r1  ; List_4
    //     0xae3e70: stur            w1, [x0, #0x17]
    // 0xae3e74: r1 = Instance_Clip
    //     0xae3e74: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xae3e78: ldr             x1, [x1, #0x7e0]
    // 0xae3e7c: StoreField: r0->field_1b = r1
    //     0xae3e7c: stur            w1, [x0, #0x1b]
    // 0xae3e80: ldur            x1, [fp, #-8]
    // 0xae3e84: StoreField: r0->field_b = r1
    //     0xae3e84: stur            w1, [x0, #0xb]
    // 0xae3e88: LeaveFrame
    //     0xae3e88: mov             SP, fp
    //     0xae3e8c: ldp             fp, lr, [SP], #0x10
    // 0xae3e90: ret
    //     0xae3e90: ret             
    // 0xae3e94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3e94: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3e98: b               #0xae36a8
    // 0xae3e9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3e9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3ea0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae3ea0: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xae3ea4: SaveReg d0
    //     0xae3ea4: str             q0, [SP, #-0x10]!
    // 0xae3ea8: SaveReg r1
    //     0xae3ea8: str             x1, [SP, #-8]!
    // 0xae3eac: r0 = AllocateDouble()
    //     0xae3eac: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xae3eb0: RestoreReg r1
    //     0xae3eb0: ldr             x1, [SP], #8
    // 0xae3eb4: RestoreReg d0
    //     0xae3eb4: ldr             q0, [SP], #0x10
    // 0xae3eb8: b               #0xae3838
    // 0xae3ebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3ebc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3ec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3ec0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3ec4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3ec4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3ec8, size: 0xf0
    // 0xae3ec8: EnterFrame
    //     0xae3ec8: stp             fp, lr, [SP, #-0x10]!
    //     0xae3ecc: mov             fp, SP
    // 0xae3ed0: AllocStack(0x20)
    //     0xae3ed0: sub             SP, SP, #0x20
    // 0xae3ed4: SetupParameters()
    //     0xae3ed4: ldr             x0, [fp, #0x10]
    //     0xae3ed8: ldur            w1, [x0, #0x17]
    //     0xae3edc: add             x1, x1, HEAP, lsl #32
    // 0xae3ee0: CheckStackOverflow
    //     0xae3ee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3ee4: cmp             SP, x16
    //     0xae3ee8: b.ls            #0xae3fa8
    // 0xae3eec: LoadField: r0 = r1->field_f
    //     0xae3eec: ldur            w0, [x1, #0xf]
    // 0xae3ef0: DecompressPointer r0
    //     0xae3ef0: add             x0, x0, HEAP, lsl #32
    // 0xae3ef4: LoadField: r2 = r0->field_b
    //     0xae3ef4: ldur            w2, [x0, #0xb]
    // 0xae3ef8: DecompressPointer r2
    //     0xae3ef8: add             x2, x2, HEAP, lsl #32
    // 0xae3efc: cmp             w2, NULL
    // 0xae3f00: b.eq            #0xae3fb0
    // 0xae3f04: LoadField: r3 = r2->field_b
    //     0xae3f04: ldur            w3, [x2, #0xb]
    // 0xae3f08: DecompressPointer r3
    //     0xae3f08: add             x3, x3, HEAP, lsl #32
    // 0xae3f0c: cmp             w3, NULL
    // 0xae3f10: b.ne            #0xae3f1c
    // 0xae3f14: r0 = Null
    //     0xae3f14: mov             x0, NULL
    // 0xae3f18: b               #0xae3f60
    // 0xae3f1c: LoadField: r0 = r1->field_13
    //     0xae3f1c: ldur            w0, [x1, #0x13]
    // 0xae3f20: DecompressPointer r0
    //     0xae3f20: add             x0, x0, HEAP, lsl #32
    // 0xae3f24: LoadField: r1 = r3->field_b
    //     0xae3f24: ldur            w1, [x3, #0xb]
    // 0xae3f28: r4 = LoadInt32Instr(r0)
    //     0xae3f28: sbfx            x4, x0, #1, #0x1f
    //     0xae3f2c: tbz             w0, #0, #0xae3f34
    //     0xae3f30: ldur            x4, [x0, #7]
    // 0xae3f34: r0 = LoadInt32Instr(r1)
    //     0xae3f34: sbfx            x0, x1, #1, #0x1f
    // 0xae3f38: mov             x1, x4
    // 0xae3f3c: cmp             x1, x0
    // 0xae3f40: b.hs            #0xae3fb4
    // 0xae3f44: LoadField: r0 = r3->field_f
    //     0xae3f44: ldur            w0, [x3, #0xf]
    // 0xae3f48: DecompressPointer r0
    //     0xae3f48: add             x0, x0, HEAP, lsl #32
    // 0xae3f4c: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xae3f4c: add             x16, x0, x4, lsl #2
    //     0xae3f50: ldur            w1, [x16, #0xf]
    // 0xae3f54: DecompressPointer r1
    //     0xae3f54: add             x1, x1, HEAP, lsl #32
    // 0xae3f58: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xae3f58: ldur            w0, [x1, #0x17]
    // 0xae3f5c: DecompressPointer r0
    //     0xae3f5c: add             x0, x0, HEAP, lsl #32
    // 0xae3f60: LoadField: r1 = r2->field_1b
    //     0xae3f60: ldur            w1, [x2, #0x1b]
    // 0xae3f64: DecompressPointer r1
    //     0xae3f64: add             x1, x1, HEAP, lsl #32
    // 0xae3f68: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xae3f68: ldur            w3, [x2, #0x17]
    // 0xae3f6c: DecompressPointer r3
    //     0xae3f6c: add             x3, x3, HEAP, lsl #32
    // 0xae3f70: LoadField: r4 = r2->field_23
    //     0xae3f70: ldur            w4, [x2, #0x23]
    // 0xae3f74: DecompressPointer r4
    //     0xae3f74: add             x4, x4, HEAP, lsl #32
    // 0xae3f78: stp             x0, x4, [SP, #0x10]
    // 0xae3f7c: stp             x3, x1, [SP]
    // 0xae3f80: r4 = 0
    //     0xae3f80: movz            x4, #0
    // 0xae3f84: ldr             x0, [SP, #0x18]
    // 0xae3f88: r16 = UnlinkedCall_0x613b5c
    //     0xae3f88: add             x16, PP, #0x58, lsl #12  ; [pp+0x583e8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae3f8c: add             x16, x16, #0x3e8
    // 0xae3f90: ldp             x5, lr, [x16]
    // 0xae3f94: blr             lr
    // 0xae3f98: r0 = Null
    //     0xae3f98: mov             x0, NULL
    // 0xae3f9c: LeaveFrame
    //     0xae3f9c: mov             SP, fp
    //     0xae3fa0: ldp             fp, lr, [SP], #0x10
    // 0xae3fa4: ret
    //     0xae3fa4: ret             
    // 0xae3fa8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3fa8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3fac: b               #0xae3eec
    // 0xae3fb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3fb0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3fb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae3fb4: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String, String, String, String) {
    // ** addr: 0xae3fb8, size: 0xa4
    // 0xae3fb8: EnterFrame
    //     0xae3fb8: stp             fp, lr, [SP, #-0x10]!
    //     0xae3fbc: mov             fp, SP
    // 0xae3fc0: AllocStack(0x40)
    //     0xae3fc0: sub             SP, SP, #0x40
    // 0xae3fc4: SetupParameters()
    //     0xae3fc4: ldr             x0, [fp, #0x48]
    //     0xae3fc8: ldur            w1, [x0, #0x17]
    //     0xae3fcc: add             x1, x1, HEAP, lsl #32
    // 0xae3fd0: CheckStackOverflow
    //     0xae3fd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3fd4: cmp             SP, x16
    //     0xae3fd8: b.ls            #0xae4050
    // 0xae3fdc: LoadField: r0 = r1->field_f
    //     0xae3fdc: ldur            w0, [x1, #0xf]
    // 0xae3fe0: DecompressPointer r0
    //     0xae3fe0: add             x0, x0, HEAP, lsl #32
    // 0xae3fe4: LoadField: r1 = r0->field_b
    //     0xae3fe4: ldur            w1, [x0, #0xb]
    // 0xae3fe8: DecompressPointer r1
    //     0xae3fe8: add             x1, x1, HEAP, lsl #32
    // 0xae3fec: cmp             w1, NULL
    // 0xae3ff0: b.eq            #0xae4058
    // 0xae3ff4: LoadField: r0 = r1->field_1f
    //     0xae3ff4: ldur            w0, [x1, #0x1f]
    // 0xae3ff8: DecompressPointer r0
    //     0xae3ff8: add             x0, x0, HEAP, lsl #32
    // 0xae3ffc: ldr             x16, [fp, #0x40]
    // 0xae4000: stp             x16, x0, [SP, #0x30]
    // 0xae4004: ldr             x16, [fp, #0x38]
    // 0xae4008: ldr             lr, [fp, #0x30]
    // 0xae400c: stp             lr, x16, [SP, #0x20]
    // 0xae4010: ldr             x16, [fp, #0x28]
    // 0xae4014: ldr             lr, [fp, #0x20]
    // 0xae4018: stp             lr, x16, [SP, #0x10]
    // 0xae401c: ldr             x16, [fp, #0x18]
    // 0xae4020: ldr             lr, [fp, #0x10]
    // 0xae4024: stp             lr, x16, [SP]
    // 0xae4028: r4 = 0
    //     0xae4028: movz            x4, #0
    // 0xae402c: ldr             x0, [SP, #0x38]
    // 0xae4030: r16 = UnlinkedCall_0x613b5c
    //     0xae4030: add             x16, PP, #0x58, lsl #12  ; [pp+0x583f8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xae4034: add             x16, x16, #0x3f8
    // 0xae4038: ldp             x5, lr, [x16]
    // 0xae403c: blr             lr
    // 0xae4040: r0 = Null
    //     0xae4040: mov             x0, NULL
    // 0xae4044: LeaveFrame
    //     0xae4044: mov             SP, fp
    //     0xae4048: ldp             fp, lr, [SP], #0x10
    // 0xae404c: ret
    //     0xae404c: ret             
    // 0xae4050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4050: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4054: b               #0xae3fdc
    // 0xae4058: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4058: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4165, size: 0x38, field offset: 0xc
//   const constructor, 
class BannerCrossLink extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7d9a4, size: 0x24
    // 0xc7d9a4: EnterFrame
    //     0xc7d9a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc7d9a8: mov             fp, SP
    // 0xc7d9ac: mov             x0, x1
    // 0xc7d9b0: r1 = <BannerCrossLink>
    //     0xc7d9b0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48bf0] TypeArguments: <BannerCrossLink>
    //     0xc7d9b4: ldr             x1, [x1, #0xbf0]
    // 0xc7d9b8: r0 = _CarouselItemViewState()
    //     0xc7d9b8: bl              #0xc7d9c8  ; Allocate_CarouselItemViewStateStub -> _CarouselItemViewState (size=0x14)
    // 0xc7d9bc: LeaveFrame
    //     0xc7d9bc: mov             SP, fp
    //     0xc7d9c0: ldp             fp, lr, [SP], #0x10
    // 0xc7d9c4: ret
    //     0xc7d9c4: ret             
  }
}
