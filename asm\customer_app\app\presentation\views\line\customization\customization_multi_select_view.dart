// lib: , url: package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart

// class id: 1049507, size: 0x8
class :: {
}

// class id: 3257, size: 0x1c, field offset: 0x14
class _CustomizationMultiSelectState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0x949048, size: 0x30
    // 0x949048: EnterFrame
    //     0x949048: stp             fp, lr, [SP, #-0x10]!
    //     0x94904c: mov             fp, SP
    // 0x949050: CheckStackOverflow
    //     0x949050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x949054: cmp             SP, x16
    //     0x949058: b.ls            #0x949070
    // 0x94905c: r0 = _initializeProductCustomisation()
    //     0x94905c: bl              #0x949098  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_initializeProductCustomisation
    // 0x949060: r0 = Null
    //     0x949060: mov             x0, NULL
    // 0x949064: LeaveFrame
    //     0x949064: mov             SP, fp
    //     0x949068: ldp             fp, lr, [SP], #0x10
    // 0x94906c: ret
    //     0x94906c: ret             
    // 0x949070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949070: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x949074: b               #0x94905c
  }
  _ _initializeProductCustomisation(/* No info */) {
    // ** addr: 0x949098, size: 0x1a0
    // 0x949098: EnterFrame
    //     0x949098: stp             fp, lr, [SP, #-0x10]!
    //     0x94909c: mov             fp, SP
    // 0x9490a0: AllocStack(0x40)
    //     0x9490a0: sub             SP, SP, #0x40
    // 0x9490a4: SetupParameters(_CustomizationMultiSelectState this /* r1 => r0, fp-0x18 */)
    //     0x9490a4: mov             x0, x1
    //     0x9490a8: stur            x1, [fp, #-0x18]
    // 0x9490ac: CheckStackOverflow
    //     0x9490ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9490b0: cmp             SP, x16
    //     0x9490b4: b.ls            #0x949228
    // 0x9490b8: LoadField: r1 = r0->field_b
    //     0x9490b8: ldur            w1, [x0, #0xb]
    // 0x9490bc: DecompressPointer r1
    //     0x9490bc: add             x1, x1, HEAP, lsl #32
    // 0x9490c0: cmp             w1, NULL
    // 0x9490c4: b.eq            #0x949230
    // 0x9490c8: LoadField: r2 = r1->field_b
    //     0x9490c8: ldur            w2, [x1, #0xb]
    // 0x9490cc: DecompressPointer r2
    //     0x9490cc: add             x2, x2, HEAP, lsl #32
    // 0x9490d0: cmp             w2, NULL
    // 0x9490d4: b.ne            #0x9490e0
    // 0x9490d8: r3 = Null
    //     0x9490d8: mov             x3, NULL
    // 0x9490dc: b               #0x9490ec
    // 0x9490e0: LoadField: r1 = r2->field_7
    //     0x9490e0: ldur            w1, [x2, #7]
    // 0x9490e4: DecompressPointer r1
    //     0x9490e4: add             x1, x1, HEAP, lsl #32
    // 0x9490e8: mov             x3, x1
    // 0x9490ec: stur            x3, [fp, #-0x10]
    // 0x9490f0: cmp             w2, NULL
    // 0x9490f4: b.ne            #0x949100
    // 0x9490f8: r4 = Null
    //     0x9490f8: mov             x4, NULL
    // 0x9490fc: b               #0x94910c
    // 0x949100: LoadField: r1 = r2->field_b
    //     0x949100: ldur            w1, [x2, #0xb]
    // 0x949104: DecompressPointer r1
    //     0x949104: add             x1, x1, HEAP, lsl #32
    // 0x949108: mov             x4, x1
    // 0x94910c: stur            x4, [fp, #-8]
    // 0x949110: r1 = <CustomerResponse>
    //     0x949110: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a8] TypeArguments: <CustomerResponse>
    //     0x949114: ldr             x1, [x1, #0x5a8]
    // 0x949118: r2 = 0
    //     0x949118: movz            x2, #0
    // 0x94911c: r0 = _GrowableList()
    //     0x94911c: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0x949120: mov             x1, x0
    // 0x949124: ldur            x0, [fp, #-0x18]
    // 0x949128: stur            x1, [fp, #-0x30]
    // 0x94912c: LoadField: r2 = r0->field_b
    //     0x94912c: ldur            w2, [x0, #0xb]
    // 0x949130: DecompressPointer r2
    //     0x949130: add             x2, x2, HEAP, lsl #32
    // 0x949134: stur            x2, [fp, #-0x28]
    // 0x949138: cmp             w2, NULL
    // 0x94913c: b.eq            #0x949234
    // 0x949140: LoadField: r0 = r2->field_b
    //     0x949140: ldur            w0, [x2, #0xb]
    // 0x949144: DecompressPointer r0
    //     0x949144: add             x0, x0, HEAP, lsl #32
    // 0x949148: cmp             w0, NULL
    // 0x94914c: b.ne            #0x949158
    // 0x949150: r3 = Null
    //     0x949150: mov             x3, NULL
    // 0x949154: b               #0x949160
    // 0x949158: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x949158: ldur            w3, [x0, #0x17]
    // 0x94915c: DecompressPointer r3
    //     0x94915c: add             x3, x3, HEAP, lsl #32
    // 0x949160: stur            x3, [fp, #-0x18]
    // 0x949164: cmp             w0, NULL
    // 0x949168: b.ne            #0x949174
    // 0x94916c: r0 = Null
    //     0x94916c: mov             x0, NULL
    // 0x949170: b               #0x949180
    // 0x949174: LoadField: r4 = r0->field_23
    //     0x949174: ldur            w4, [x0, #0x23]
    // 0x949178: DecompressPointer r4
    //     0x949178: add             x4, x4, HEAP, lsl #32
    // 0x94917c: mov             x0, x4
    // 0x949180: cmp             w0, NULL
    // 0x949184: b.ne            #0x949190
    // 0x949188: r5 = 0
    //     0x949188: movz            x5, #0
    // 0x94918c: b               #0x9491a0
    // 0x949190: r4 = LoadInt32Instr(r0)
    //     0x949190: sbfx            x4, x0, #1, #0x1f
    //     0x949194: tbz             w0, #0, #0x94919c
    //     0x949198: ldur            x4, [x0, #7]
    // 0x94919c: mov             x5, x4
    // 0x9491a0: ldur            x0, [fp, #-0x10]
    // 0x9491a4: ldur            x4, [fp, #-8]
    // 0x9491a8: stur            x5, [fp, #-0x20]
    // 0x9491ac: r0 = ProductCustomisation()
    //     0x9491ac: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0x9491b0: mov             x2, x0
    // 0x9491b4: ldur            x0, [fp, #-0x10]
    // 0x9491b8: StoreField: r2->field_b = r0
    //     0x9491b8: stur            w0, [x2, #0xb]
    // 0x9491bc: ldur            x0, [fp, #-8]
    // 0x9491c0: StoreField: r2->field_f = r0
    //     0x9491c0: stur            w0, [x2, #0xf]
    // 0x9491c4: ldur            x0, [fp, #-0x18]
    // 0x9491c8: ArrayStore: r2[0] = r0  ; List_4
    //     0x9491c8: stur            w0, [x2, #0x17]
    // 0x9491cc: ldur            x0, [fp, #-0x30]
    // 0x9491d0: StoreField: r2->field_23 = r0
    //     0x9491d0: stur            w0, [x2, #0x23]
    // 0x9491d4: ldur            x3, [fp, #-0x20]
    // 0x9491d8: r0 = BoxInt64Instr(r3)
    //     0x9491d8: sbfiz           x0, x3, #1, #0x1f
    //     0x9491dc: cmp             x3, x0, asr #1
    //     0x9491e0: b.eq            #0x9491ec
    //     0x9491e4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9491e8: stur            x3, [x0, #7]
    // 0x9491ec: StoreField: r2->field_27 = r0
    //     0x9491ec: stur            w0, [x2, #0x27]
    // 0x9491f0: ldur            x0, [fp, #-0x28]
    // 0x9491f4: LoadField: r1 = r0->field_f
    //     0x9491f4: ldur            w1, [x0, #0xf]
    // 0x9491f8: DecompressPointer r1
    //     0x9491f8: add             x1, x1, HEAP, lsl #32
    // 0x9491fc: stp             x2, x1, [SP]
    // 0x949200: r4 = 0
    //     0x949200: movz            x4, #0
    // 0x949204: ldr             x0, [SP, #8]
    // 0x949208: r16 = UnlinkedCall_0x613b5c
    //     0x949208: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a4b8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0x94920c: add             x16, x16, #0x4b8
    // 0x949210: ldp             x5, lr, [x16]
    // 0x949214: blr             lr
    // 0x949218: r0 = Null
    //     0x949218: mov             x0, NULL
    // 0x94921c: LeaveFrame
    //     0x94921c: mov             SP, fp
    //     0x949220: ldp             fp, lr, [SP], #0x10
    // 0x949224: ret
    //     0x949224: ret             
    // 0x949228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x949228: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94922c: b               #0x9490b8
    // 0x949230: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949230: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x949234: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x949234: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, CustomerResponse) {
    // ** addr: 0xa3ceb0, size: 0x68
    // 0xa3ceb0: EnterFrame
    //     0xa3ceb0: stp             fp, lr, [SP, #-0x10]!
    //     0xa3ceb4: mov             fp, SP
    // 0xa3ceb8: AllocStack(0x10)
    //     0xa3ceb8: sub             SP, SP, #0x10
    // 0xa3cebc: SetupParameters()
    //     0xa3cebc: ldr             x0, [fp, #0x18]
    //     0xa3cec0: ldur            w1, [x0, #0x17]
    //     0xa3cec4: add             x1, x1, HEAP, lsl #32
    // 0xa3cec8: CheckStackOverflow
    //     0xa3cec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3cecc: cmp             SP, x16
    //     0xa3ced0: b.ls            #0xa3cf10
    // 0xa3ced4: ldr             x0, [fp, #0x10]
    // 0xa3ced8: LoadField: r2 = r0->field_f
    //     0xa3ced8: ldur            w2, [x0, #0xf]
    // 0xa3cedc: DecompressPointer r2
    //     0xa3cedc: add             x2, x2, HEAP, lsl #32
    // 0xa3cee0: LoadField: r0 = r1->field_f
    //     0xa3cee0: ldur            w0, [x1, #0xf]
    // 0xa3cee4: DecompressPointer r0
    //     0xa3cee4: add             x0, x0, HEAP, lsl #32
    // 0xa3cee8: r1 = LoadClassIdInstr(r2)
    //     0xa3cee8: ldur            x1, [x2, #-1]
    //     0xa3ceec: ubfx            x1, x1, #0xc, #0x14
    // 0xa3cef0: stp             x0, x2, [SP]
    // 0xa3cef4: mov             x0, x1
    // 0xa3cef8: mov             lr, x0
    // 0xa3cefc: ldr             lr, [x21, lr, lsl #3]
    // 0xa3cf00: blr             lr
    // 0xa3cf04: LeaveFrame
    //     0xa3cf04: mov             SP, fp
    //     0xa3cf08: ldp             fp, lr, [SP], #0x10
    // 0xa3cf0c: ret
    //     0xa3cf0c: ret             
    // 0xa3cf10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3cf10: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3cf14: b               #0xa3ced4
  }
  _ _findAndRemoveCustomerResponse(/* No info */) {
    // ** addr: 0xa3cf18, size: 0xb4
    // 0xa3cf18: EnterFrame
    //     0xa3cf18: stp             fp, lr, [SP, #-0x10]!
    //     0xa3cf1c: mov             fp, SP
    // 0xa3cf20: AllocStack(0x10)
    //     0xa3cf20: sub             SP, SP, #0x10
    // 0xa3cf24: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa3cf24: stur            x1, [fp, #-8]
    //     0xa3cf28: stur            x2, [fp, #-0x10]
    // 0xa3cf2c: CheckStackOverflow
    //     0xa3cf2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa3cf30: cmp             SP, x16
    //     0xa3cf34: b.ls            #0xa3cfc4
    // 0xa3cf38: r1 = 1
    //     0xa3cf38: movz            x1, #0x1
    // 0xa3cf3c: r0 = AllocateContext()
    //     0xa3cf3c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa3cf40: mov             x1, x0
    // 0xa3cf44: ldur            x0, [fp, #-0x10]
    // 0xa3cf48: StoreField: r1->field_f = r0
    //     0xa3cf48: stur            w0, [x1, #0xf]
    // 0xa3cf4c: cmp             w0, NULL
    // 0xa3cf50: b.ne            #0xa3cf64
    // 0xa3cf54: r0 = Null
    //     0xa3cf54: mov             x0, NULL
    // 0xa3cf58: LeaveFrame
    //     0xa3cf58: mov             SP, fp
    //     0xa3cf5c: ldp             fp, lr, [SP], #0x10
    // 0xa3cf60: ret
    //     0xa3cf60: ret             
    // 0xa3cf64: ldur            x0, [fp, #-8]
    // 0xa3cf68: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa3cf68: ldur            w3, [x0, #0x17]
    // 0xa3cf6c: DecompressPointer r3
    //     0xa3cf6c: add             x3, x3, HEAP, lsl #32
    // 0xa3cf70: mov             x2, x1
    // 0xa3cf74: stur            x3, [fp, #-0x10]
    // 0xa3cf78: r1 = Function '<anonymous closure>':.
    //     0xa3cf78: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a498] AnonymousClosure: (0xa3ceb0), in [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_findAndRemoveCustomerResponse (0xa3cf18)
    //     0xa3cf7c: ldr             x1, [x1, #0x498]
    // 0xa3cf80: r0 = AllocateClosure()
    //     0xa3cf80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa3cf84: ldur            x1, [fp, #-0x10]
    // 0xa3cf88: mov             x2, x0
    // 0xa3cf8c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa3cf8c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa3cf90: r0 = indexWhere()
    //     0xa3cf90: bl              #0x806984  ; [dart:collection] ListBase::indexWhere
    // 0xa3cf94: cmn             x0, #1
    // 0xa3cf98: b.eq            #0xa3cfb4
    // 0xa3cf9c: ldur            x1, [fp, #-0x10]
    // 0xa3cfa0: mov             x2, x0
    // 0xa3cfa4: r0 = removeAt()
    //     0xa3cfa4: bl              #0x7145c0  ; [dart:core] _GrowableList::removeAt
    // 0xa3cfa8: LeaveFrame
    //     0xa3cfa8: mov             SP, fp
    //     0xa3cfac: ldp             fp, lr, [SP], #0x10
    // 0xa3cfb0: ret
    //     0xa3cfb0: ret             
    // 0xa3cfb4: r0 = Null
    //     0xa3cfb4: mov             x0, NULL
    // 0xa3cfb8: LeaveFrame
    //     0xa3cfb8: mov             SP, fp
    //     0xa3cfbc: ldp             fp, lr, [SP], #0x10
    // 0xa3cfc0: ret
    //     0xa3cfc0: ret             
    // 0xa3cfc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa3cfc4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa3cfc8: b               #0xa3cf38
  }
  _ build(/* No info */) {
    // ** addr: 0xbd8c70, size: 0x164
    // 0xbd8c70: EnterFrame
    //     0xbd8c70: stp             fp, lr, [SP, #-0x10]!
    //     0xbd8c74: mov             fp, SP
    // 0xbd8c78: AllocStack(0x18)
    //     0xbd8c78: sub             SP, SP, #0x18
    // 0xbd8c7c: SetupParameters(_CustomizationMultiSelectState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbd8c7c: mov             x3, x1
    //     0xbd8c80: mov             x0, x2
    //     0xbd8c84: stur            x1, [fp, #-8]
    //     0xbd8c88: stur            x2, [fp, #-0x10]
    // 0xbd8c8c: CheckStackOverflow
    //     0xbd8c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd8c90: cmp             SP, x16
    //     0xbd8c94: b.ls            #0xbd8dc8
    // 0xbd8c98: LoadField: r1 = r3->field_b
    //     0xbd8c98: ldur            w1, [x3, #0xb]
    // 0xbd8c9c: DecompressPointer r1
    //     0xbd8c9c: add             x1, x1, HEAP, lsl #32
    // 0xbd8ca0: cmp             w1, NULL
    // 0xbd8ca4: b.eq            #0xbd8dd0
    // 0xbd8ca8: LoadField: r2 = r1->field_b
    //     0xbd8ca8: ldur            w2, [x1, #0xb]
    // 0xbd8cac: DecompressPointer r2
    //     0xbd8cac: add             x2, x2, HEAP, lsl #32
    // 0xbd8cb0: cmp             w2, NULL
    // 0xbd8cb4: b.ne            #0xbd8cc0
    // 0xbd8cb8: r1 = Null
    //     0xbd8cb8: mov             x1, NULL
    // 0xbd8cbc: b               #0xbd8cc8
    // 0xbd8cc0: LoadField: r1 = r2->field_2f
    //     0xbd8cc0: ldur            w1, [x2, #0x2f]
    // 0xbd8cc4: DecompressPointer r1
    //     0xbd8cc4: add             x1, x1, HEAP, lsl #32
    // 0xbd8cc8: cmp             w1, NULL
    // 0xbd8ccc: b.ne            #0xbd8ce4
    // 0xbd8cd0: r1 = <CustomizedEntity>
    //     0xbd8cd0: add             x1, PP, #0x30, lsl #12  ; [pp+0x30240] TypeArguments: <CustomizedEntity>
    //     0xbd8cd4: ldr             x1, [x1, #0x240]
    // 0xbd8cd8: r2 = 0
    //     0xbd8cd8: movz            x2, #0
    // 0xbd8cdc: r0 = _GrowableList()
    //     0xbd8cdc: bl              #0x621804  ; [dart:core] _GrowableList::_GrowableList
    // 0xbd8ce0: b               #0xbd8ce8
    // 0xbd8ce4: mov             x0, x1
    // 0xbd8ce8: stur            x0, [fp, #-0x18]
    // 0xbd8cec: LoadField: r1 = r0->field_b
    //     0xbd8cec: ldur            w1, [x0, #0xb]
    // 0xbd8cf0: cbnz            w1, #0xbd8d04
    // 0xbd8cf4: r0 = Instance_SizedBox
    //     0xbd8cf4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xbd8cf8: LeaveFrame
    //     0xbd8cf8: mov             SP, fp
    //     0xbd8cfc: ldp             fp, lr, [SP], #0x10
    // 0xbd8d00: ret
    //     0xbd8d00: ret             
    // 0xbd8d04: ldur            x1, [fp, #-8]
    // 0xbd8d08: ldur            x2, [fp, #-0x10]
    // 0xbd8d0c: r0 = _buildHeader()
    //     0xbd8d0c: bl              #0xa3d264  ; [package:customer_app/app/presentation/views/basic/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildHeader
    // 0xbd8d10: ldur            x1, [fp, #-8]
    // 0xbd8d14: ldur            x2, [fp, #-0x18]
    // 0xbd8d18: stur            x0, [fp, #-8]
    // 0xbd8d1c: r0 = _buildItemsList()
    //     0xbd8d1c: bl              #0xbd8dd4  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildItemsList
    // 0xbd8d20: r1 = Null
    //     0xbd8d20: mov             x1, NULL
    // 0xbd8d24: r2 = 4
    //     0xbd8d24: movz            x2, #0x4
    // 0xbd8d28: stur            x0, [fp, #-0x10]
    // 0xbd8d2c: r0 = AllocateArray()
    //     0xbd8d2c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd8d30: mov             x2, x0
    // 0xbd8d34: ldur            x0, [fp, #-8]
    // 0xbd8d38: stur            x2, [fp, #-0x18]
    // 0xbd8d3c: StoreField: r2->field_f = r0
    //     0xbd8d3c: stur            w0, [x2, #0xf]
    // 0xbd8d40: ldur            x0, [fp, #-0x10]
    // 0xbd8d44: StoreField: r2->field_13 = r0
    //     0xbd8d44: stur            w0, [x2, #0x13]
    // 0xbd8d48: r1 = <Widget>
    //     0xbd8d48: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd8d4c: r0 = AllocateGrowableArray()
    //     0xbd8d4c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd8d50: mov             x1, x0
    // 0xbd8d54: ldur            x0, [fp, #-0x18]
    // 0xbd8d58: stur            x1, [fp, #-8]
    // 0xbd8d5c: StoreField: r1->field_f = r0
    //     0xbd8d5c: stur            w0, [x1, #0xf]
    // 0xbd8d60: r0 = 4
    //     0xbd8d60: movz            x0, #0x4
    // 0xbd8d64: StoreField: r1->field_b = r0
    //     0xbd8d64: stur            w0, [x1, #0xb]
    // 0xbd8d68: r0 = Column()
    //     0xbd8d68: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbd8d6c: r1 = Instance_Axis
    //     0xbd8d6c: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbd8d70: StoreField: r0->field_f = r1
    //     0xbd8d70: stur            w1, [x0, #0xf]
    // 0xbd8d74: r1 = Instance_MainAxisAlignment
    //     0xbd8d74: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd8d78: ldr             x1, [x1, #0xa08]
    // 0xbd8d7c: StoreField: r0->field_13 = r1
    //     0xbd8d7c: stur            w1, [x0, #0x13]
    // 0xbd8d80: r1 = Instance_MainAxisSize
    //     0xbd8d80: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd8d84: ldr             x1, [x1, #0xa10]
    // 0xbd8d88: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd8d88: stur            w1, [x0, #0x17]
    // 0xbd8d8c: r1 = Instance_CrossAxisAlignment
    //     0xbd8d8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xbd8d90: ldr             x1, [x1, #0x890]
    // 0xbd8d94: StoreField: r0->field_1b = r1
    //     0xbd8d94: stur            w1, [x0, #0x1b]
    // 0xbd8d98: r1 = Instance_VerticalDirection
    //     0xbd8d98: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd8d9c: ldr             x1, [x1, #0xa20]
    // 0xbd8da0: StoreField: r0->field_23 = r1
    //     0xbd8da0: stur            w1, [x0, #0x23]
    // 0xbd8da4: r1 = Instance_Clip
    //     0xbd8da4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd8da8: ldr             x1, [x1, #0x38]
    // 0xbd8dac: StoreField: r0->field_2b = r1
    //     0xbd8dac: stur            w1, [x0, #0x2b]
    // 0xbd8db0: StoreField: r0->field_2f = rZR
    //     0xbd8db0: stur            xzr, [x0, #0x2f]
    // 0xbd8db4: ldur            x1, [fp, #-8]
    // 0xbd8db8: StoreField: r0->field_b = r1
    //     0xbd8db8: stur            w1, [x0, #0xb]
    // 0xbd8dbc: LeaveFrame
    //     0xbd8dbc: mov             SP, fp
    //     0xbd8dc0: ldp             fp, lr, [SP], #0x10
    // 0xbd8dc4: ret
    //     0xbd8dc4: ret             
    // 0xbd8dc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd8dc8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd8dcc: b               #0xbd8c98
    // 0xbd8dd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd8dd0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildItemsList(/* No info */) {
    // ** addr: 0xbd8dd4, size: 0xa0
    // 0xbd8dd4: EnterFrame
    //     0xbd8dd4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd8dd8: mov             fp, SP
    // 0xbd8ddc: AllocStack(0x28)
    //     0xbd8ddc: sub             SP, SP, #0x28
    // 0xbd8de0: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbd8de0: stur            x1, [fp, #-8]
    //     0xbd8de4: stur            x2, [fp, #-0x10]
    // 0xbd8de8: CheckStackOverflow
    //     0xbd8de8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd8dec: cmp             SP, x16
    //     0xbd8df0: b.ls            #0xbd8e6c
    // 0xbd8df4: r1 = 2
    //     0xbd8df4: movz            x1, #0x2
    // 0xbd8df8: r0 = AllocateContext()
    //     0xbd8df8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd8dfc: mov             x1, x0
    // 0xbd8e00: ldur            x0, [fp, #-8]
    // 0xbd8e04: StoreField: r1->field_f = r0
    //     0xbd8e04: stur            w0, [x1, #0xf]
    // 0xbd8e08: ldur            x0, [fp, #-0x10]
    // 0xbd8e0c: StoreField: r1->field_13 = r0
    //     0xbd8e0c: stur            w0, [x1, #0x13]
    // 0xbd8e10: LoadField: r3 = r0->field_b
    //     0xbd8e10: ldur            w3, [x0, #0xb]
    // 0xbd8e14: mov             x2, x1
    // 0xbd8e18: stur            x3, [fp, #-8]
    // 0xbd8e1c: r1 = Function '<anonymous closure>':.
    //     0xbd8e1c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a470] AnonymousClosure: (0xbd8e74), in [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildItemsList (0xbd8dd4)
    //     0xbd8e20: ldr             x1, [x1, #0x470]
    // 0xbd8e24: r0 = AllocateClosure()
    //     0xbd8e24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd8e28: stur            x0, [fp, #-0x10]
    // 0xbd8e2c: r0 = ListView()
    //     0xbd8e2c: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbd8e30: stur            x0, [fp, #-0x18]
    // 0xbd8e34: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbd8e34: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbd8e38: ldr             x16, [x16, #0x1c8]
    // 0xbd8e3c: r30 = true
    //     0xbd8e3c: add             lr, NULL, #0x20  ; true
    // 0xbd8e40: stp             lr, x16, [SP]
    // 0xbd8e44: mov             x1, x0
    // 0xbd8e48: ldur            x2, [fp, #-0x10]
    // 0xbd8e4c: ldur            x3, [fp, #-8]
    // 0xbd8e50: r4 = const [0, 0x5, 0x2, 0x3, physics, 0x3, shrinkWrap, 0x4, null]
    //     0xbd8e50: add             x4, PP, #0x53, lsl #12  ; [pp+0x53d18] List(9) [0, 0x5, 0x2, 0x3, "physics", 0x3, "shrinkWrap", 0x4, Null]
    //     0xbd8e54: ldr             x4, [x4, #0xd18]
    // 0xbd8e58: r0 = ListView.builder()
    //     0xbd8e58: bl              #0x9020d0  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.builder
    // 0xbd8e5c: ldur            x0, [fp, #-0x18]
    // 0xbd8e60: LeaveFrame
    //     0xbd8e60: mov             SP, fp
    //     0xbd8e64: ldp             fp, lr, [SP], #0x10
    // 0xbd8e68: ret
    //     0xbd8e68: ret             
    // 0xbd8e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd8e6c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd8e70: b               #0xbd8df4
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbd8e74, size: 0x90
    // 0xbd8e74: EnterFrame
    //     0xbd8e74: stp             fp, lr, [SP, #-0x10]!
    //     0xbd8e78: mov             fp, SP
    // 0xbd8e7c: ldr             x0, [fp, #0x20]
    // 0xbd8e80: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd8e80: ldur            w1, [x0, #0x17]
    // 0xbd8e84: DecompressPointer r1
    //     0xbd8e84: add             x1, x1, HEAP, lsl #32
    // 0xbd8e88: CheckStackOverflow
    //     0xbd8e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd8e8c: cmp             SP, x16
    //     0xbd8e90: b.ls            #0xbd8ef8
    // 0xbd8e94: LoadField: r2 = r1->field_f
    //     0xbd8e94: ldur            w2, [x1, #0xf]
    // 0xbd8e98: DecompressPointer r2
    //     0xbd8e98: add             x2, x2, HEAP, lsl #32
    // 0xbd8e9c: LoadField: r3 = r1->field_13
    //     0xbd8e9c: ldur            w3, [x1, #0x13]
    // 0xbd8ea0: DecompressPointer r3
    //     0xbd8ea0: add             x3, x3, HEAP, lsl #32
    // 0xbd8ea4: LoadField: r0 = r3->field_b
    //     0xbd8ea4: ldur            w0, [x3, #0xb]
    // 0xbd8ea8: ldr             x1, [fp, #0x10]
    // 0xbd8eac: r4 = LoadInt32Instr(r1)
    //     0xbd8eac: sbfx            x4, x1, #1, #0x1f
    //     0xbd8eb0: tbz             w1, #0, #0xbd8eb8
    //     0xbd8eb4: ldur            x4, [x1, #7]
    // 0xbd8eb8: r1 = LoadInt32Instr(r0)
    //     0xbd8eb8: sbfx            x1, x0, #1, #0x1f
    // 0xbd8ebc: mov             x0, x1
    // 0xbd8ec0: mov             x1, x4
    // 0xbd8ec4: cmp             x1, x0
    // 0xbd8ec8: b.hs            #0xbd8f00
    // 0xbd8ecc: LoadField: r0 = r3->field_f
    //     0xbd8ecc: ldur            w0, [x3, #0xf]
    // 0xbd8ed0: DecompressPointer r0
    //     0xbd8ed0: add             x0, x0, HEAP, lsl #32
    // 0xbd8ed4: ArrayLoad: r3 = r0[r4]  ; Unknown_4
    //     0xbd8ed4: add             x16, x0, x4, lsl #2
    //     0xbd8ed8: ldur            w3, [x16, #0xf]
    // 0xbd8edc: DecompressPointer r3
    //     0xbd8edc: add             x3, x3, HEAP, lsl #32
    // 0xbd8ee0: mov             x1, x2
    // 0xbd8ee4: ldr             x2, [fp, #0x18]
    // 0xbd8ee8: r0 = _buildListItem()
    //     0xbd8ee8: bl              #0xbd8f04  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildListItem
    // 0xbd8eec: LeaveFrame
    //     0xbd8eec: mov             SP, fp
    //     0xbd8ef0: ldp             fp, lr, [SP], #0x10
    // 0xbd8ef4: ret
    //     0xbd8ef4: ret             
    // 0xbd8ef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd8ef8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd8efc: b               #0xbd8e94
    // 0xbd8f00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xbd8f00: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildListItem(/* No info */) {
    // ** addr: 0xbd8f04, size: 0x368
    // 0xbd8f04: EnterFrame
    //     0xbd8f04: stp             fp, lr, [SP, #-0x10]!
    //     0xbd8f08: mov             fp, SP
    // 0xbd8f0c: AllocStack(0x48)
    //     0xbd8f0c: sub             SP, SP, #0x48
    // 0xbd8f10: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xbd8f10: mov             x0, x3
    //     0xbd8f14: stur            x3, [fp, #-0x10]
    //     0xbd8f18: mov             x3, x2
    //     0xbd8f1c: stur            x2, [fp, #-8]
    // 0xbd8f20: CheckStackOverflow
    //     0xbd8f20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd8f24: cmp             SP, x16
    //     0xbd8f28: b.ls            #0xbd9264
    // 0xbd8f2c: mov             x2, x0
    // 0xbd8f30: r0 = _buildCheckbox()
    //     0xbd8f30: bl              #0xbd926c  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildCheckbox
    // 0xbd8f34: stur            x0, [fp, #-0x18]
    // 0xbd8f38: r0 = Padding()
    //     0xbd8f38: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd8f3c: mov             x2, x0
    // 0xbd8f40: r0 = Instance_EdgeInsets
    //     0xbd8f40: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbd8f44: ldr             x0, [x0, #0x980]
    // 0xbd8f48: stur            x2, [fp, #-0x20]
    // 0xbd8f4c: StoreField: r2->field_f = r0
    //     0xbd8f4c: stur            w0, [x2, #0xf]
    // 0xbd8f50: ldur            x1, [fp, #-0x18]
    // 0xbd8f54: StoreField: r2->field_b = r1
    //     0xbd8f54: stur            w1, [x2, #0xb]
    // 0xbd8f58: ldur            x3, [fp, #-0x10]
    // 0xbd8f5c: LoadField: r1 = r3->field_f
    //     0xbd8f5c: ldur            w1, [x3, #0xf]
    // 0xbd8f60: DecompressPointer r1
    //     0xbd8f60: add             x1, x1, HEAP, lsl #32
    // 0xbd8f64: cmp             w1, NULL
    // 0xbd8f68: b.ne            #0xbd8f74
    // 0xbd8f6c: r4 = ""
    //     0xbd8f6c: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd8f70: b               #0xbd8f78
    // 0xbd8f74: mov             x4, x1
    // 0xbd8f78: ldur            x1, [fp, #-8]
    // 0xbd8f7c: stur            x4, [fp, #-0x18]
    // 0xbd8f80: r0 = of()
    //     0xbd8f80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd8f84: LoadField: r1 = r0->field_87
    //     0xbd8f84: ldur            w1, [x0, #0x87]
    // 0xbd8f88: DecompressPointer r1
    //     0xbd8f88: add             x1, x1, HEAP, lsl #32
    // 0xbd8f8c: LoadField: r0 = r1->field_2b
    //     0xbd8f8c: ldur            w0, [x1, #0x2b]
    // 0xbd8f90: DecompressPointer r0
    //     0xbd8f90: add             x0, x0, HEAP, lsl #32
    // 0xbd8f94: stur            x0, [fp, #-0x28]
    // 0xbd8f98: r1 = Instance_Color
    //     0xbd8f98: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd8f9c: d0 = 0.700000
    //     0xbd8f9c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd8fa0: ldr             d0, [x17, #0xf48]
    // 0xbd8fa4: r0 = withOpacity()
    //     0xbd8fa4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd8fa8: r16 = 12.000000
    //     0xbd8fa8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd8fac: ldr             x16, [x16, #0x9e8]
    // 0xbd8fb0: stp             x0, x16, [SP]
    // 0xbd8fb4: ldur            x1, [fp, #-0x28]
    // 0xbd8fb8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd8fb8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd8fbc: ldr             x4, [x4, #0xaa0]
    // 0xbd8fc0: r0 = copyWith()
    //     0xbd8fc0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd8fc4: stur            x0, [fp, #-0x28]
    // 0xbd8fc8: r0 = Text()
    //     0xbd8fc8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd8fcc: mov             x1, x0
    // 0xbd8fd0: ldur            x0, [fp, #-0x18]
    // 0xbd8fd4: stur            x1, [fp, #-0x30]
    // 0xbd8fd8: StoreField: r1->field_b = r0
    //     0xbd8fd8: stur            w0, [x1, #0xb]
    // 0xbd8fdc: ldur            x0, [fp, #-0x28]
    // 0xbd8fe0: StoreField: r1->field_13 = r0
    //     0xbd8fe0: stur            w0, [x1, #0x13]
    // 0xbd8fe4: r0 = Padding()
    //     0xbd8fe4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd8fe8: mov             x2, x0
    // 0xbd8fec: r0 = Instance_EdgeInsets
    //     0xbd8fec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbd8ff0: ldr             x0, [x0, #0x980]
    // 0xbd8ff4: stur            x2, [fp, #-0x18]
    // 0xbd8ff8: StoreField: r2->field_f = r0
    //     0xbd8ff8: stur            w0, [x2, #0xf]
    // 0xbd8ffc: ldur            x1, [fp, #-0x30]
    // 0xbd9000: StoreField: r2->field_b = r1
    //     0xbd9000: stur            w1, [x2, #0xb]
    // 0xbd9004: r1 = <FlexParentData>
    //     0xbd9004: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbd9008: ldr             x1, [x1, #0xe00]
    // 0xbd900c: r0 = Expanded()
    //     0xbd900c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbd9010: mov             x3, x0
    // 0xbd9014: r0 = 1
    //     0xbd9014: movz            x0, #0x1
    // 0xbd9018: stur            x3, [fp, #-0x28]
    // 0xbd901c: StoreField: r3->field_13 = r0
    //     0xbd901c: stur            x0, [x3, #0x13]
    // 0xbd9020: r0 = Instance_FlexFit
    //     0xbd9020: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbd9024: ldr             x0, [x0, #0xe08]
    // 0xbd9028: StoreField: r3->field_1b = r0
    //     0xbd9028: stur            w0, [x3, #0x1b]
    // 0xbd902c: ldur            x0, [fp, #-0x18]
    // 0xbd9030: StoreField: r3->field_b = r0
    //     0xbd9030: stur            w0, [x3, #0xb]
    // 0xbd9034: r1 = Null
    //     0xbd9034: mov             x1, NULL
    // 0xbd9038: r2 = 4
    //     0xbd9038: movz            x2, #0x4
    // 0xbd903c: r0 = AllocateArray()
    //     0xbd903c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd9040: mov             x2, x0
    // 0xbd9044: ldur            x0, [fp, #-0x20]
    // 0xbd9048: stur            x2, [fp, #-0x18]
    // 0xbd904c: StoreField: r2->field_f = r0
    //     0xbd904c: stur            w0, [x2, #0xf]
    // 0xbd9050: ldur            x0, [fp, #-0x28]
    // 0xbd9054: StoreField: r2->field_13 = r0
    //     0xbd9054: stur            w0, [x2, #0x13]
    // 0xbd9058: r1 = <Widget>
    //     0xbd9058: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbd905c: r0 = AllocateGrowableArray()
    //     0xbd905c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbd9060: mov             x3, x0
    // 0xbd9064: ldur            x0, [fp, #-0x18]
    // 0xbd9068: stur            x3, [fp, #-0x20]
    // 0xbd906c: StoreField: r3->field_f = r0
    //     0xbd906c: stur            w0, [x3, #0xf]
    // 0xbd9070: r2 = 4
    //     0xbd9070: movz            x2, #0x4
    // 0xbd9074: StoreField: r3->field_b = r2
    //     0xbd9074: stur            w2, [x3, #0xb]
    // 0xbd9078: ldur            x4, [fp, #-0x10]
    // 0xbd907c: LoadField: r0 = r4->field_13
    //     0xbd907c: ldur            w0, [x4, #0x13]
    // 0xbd9080: DecompressPointer r0
    //     0xbd9080: add             x0, x0, HEAP, lsl #32
    // 0xbd9084: cmp             w0, NULL
    // 0xbd9088: b.ne            #0xbd9094
    // 0xbd908c: r5 = 0
    //     0xbd908c: movz            x5, #0
    // 0xbd9090: b               #0xbd90a4
    // 0xbd9094: r1 = LoadInt32Instr(r0)
    //     0xbd9094: sbfx            x1, x0, #1, #0x1f
    //     0xbd9098: tbz             w0, #0, #0xbd90a0
    //     0xbd909c: ldur            x1, [x0, #7]
    // 0xbd90a0: mov             x5, x1
    // 0xbd90a4: r0 = BoxInt64Instr(r5)
    //     0xbd90a4: sbfiz           x0, x5, #1, #0x1f
    //     0xbd90a8: cmp             x5, x0, asr #1
    //     0xbd90ac: b.eq            #0xbd90b8
    //     0xbd90b0: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd90b4: stur            x5, [x0, #7]
    // 0xbd90b8: cbz             w0, #0xbd9200
    // 0xbd90bc: r1 = Null
    //     0xbd90bc: mov             x1, NULL
    // 0xbd90c0: r0 = AllocateArray()
    //     0xbd90c0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbd90c4: r16 = "+ "
    //     0xbd90c4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fc30] "+ "
    //     0xbd90c8: ldr             x16, [x16, #0xc30]
    // 0xbd90cc: StoreField: r0->field_f = r16
    //     0xbd90cc: stur            w16, [x0, #0xf]
    // 0xbd90d0: ldur            x1, [fp, #-0x10]
    // 0xbd90d4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbd90d4: ldur            w2, [x1, #0x17]
    // 0xbd90d8: DecompressPointer r2
    //     0xbd90d8: add             x2, x2, HEAP, lsl #32
    // 0xbd90dc: cmp             w2, NULL
    // 0xbd90e0: b.ne            #0xbd90e8
    // 0xbd90e4: r2 = ""
    //     0xbd90e4: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd90e8: ldur            x1, [fp, #-0x20]
    // 0xbd90ec: StoreField: r0->field_13 = r2
    //     0xbd90ec: stur            w2, [x0, #0x13]
    // 0xbd90f0: str             x0, [SP]
    // 0xbd90f4: r0 = _interpolate()
    //     0xbd90f4: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xbd90f8: ldur            x1, [fp, #-8]
    // 0xbd90fc: stur            x0, [fp, #-8]
    // 0xbd9100: r0 = of()
    //     0xbd9100: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd9104: LoadField: r1 = r0->field_87
    //     0xbd9104: ldur            w1, [x0, #0x87]
    // 0xbd9108: DecompressPointer r1
    //     0xbd9108: add             x1, x1, HEAP, lsl #32
    // 0xbd910c: LoadField: r0 = r1->field_2b
    //     0xbd910c: ldur            w0, [x1, #0x2b]
    // 0xbd9110: DecompressPointer r0
    //     0xbd9110: add             x0, x0, HEAP, lsl #32
    // 0xbd9114: stur            x0, [fp, #-0x10]
    // 0xbd9118: r1 = Instance_Color
    //     0xbd9118: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbd911c: d0 = 0.700000
    //     0xbd911c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xbd9120: ldr             d0, [x17, #0xf48]
    // 0xbd9124: r0 = withOpacity()
    //     0xbd9124: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbd9128: r16 = 12.000000
    //     0xbd9128: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbd912c: ldr             x16, [x16, #0x9e8]
    // 0xbd9130: stp             x0, x16, [SP]
    // 0xbd9134: ldur            x1, [fp, #-0x10]
    // 0xbd9138: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbd9138: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbd913c: ldr             x4, [x4, #0xaa0]
    // 0xbd9140: r0 = copyWith()
    //     0xbd9140: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbd9144: stur            x0, [fp, #-0x10]
    // 0xbd9148: r0 = Text()
    //     0xbd9148: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbd914c: mov             x1, x0
    // 0xbd9150: ldur            x0, [fp, #-8]
    // 0xbd9154: stur            x1, [fp, #-0x18]
    // 0xbd9158: StoreField: r1->field_b = r0
    //     0xbd9158: stur            w0, [x1, #0xb]
    // 0xbd915c: ldur            x0, [fp, #-0x10]
    // 0xbd9160: StoreField: r1->field_13 = r0
    //     0xbd9160: stur            w0, [x1, #0x13]
    // 0xbd9164: r0 = Padding()
    //     0xbd9164: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbd9168: mov             x2, x0
    // 0xbd916c: r0 = Instance_EdgeInsets
    //     0xbd916c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xbd9170: ldr             x0, [x0, #0x980]
    // 0xbd9174: stur            x2, [fp, #-8]
    // 0xbd9178: StoreField: r2->field_f = r0
    //     0xbd9178: stur            w0, [x2, #0xf]
    // 0xbd917c: ldur            x0, [fp, #-0x18]
    // 0xbd9180: StoreField: r2->field_b = r0
    //     0xbd9180: stur            w0, [x2, #0xb]
    // 0xbd9184: ldur            x0, [fp, #-0x20]
    // 0xbd9188: LoadField: r1 = r0->field_b
    //     0xbd9188: ldur            w1, [x0, #0xb]
    // 0xbd918c: LoadField: r3 = r0->field_f
    //     0xbd918c: ldur            w3, [x0, #0xf]
    // 0xbd9190: DecompressPointer r3
    //     0xbd9190: add             x3, x3, HEAP, lsl #32
    // 0xbd9194: LoadField: r4 = r3->field_b
    //     0xbd9194: ldur            w4, [x3, #0xb]
    // 0xbd9198: r3 = LoadInt32Instr(r1)
    //     0xbd9198: sbfx            x3, x1, #1, #0x1f
    // 0xbd919c: stur            x3, [fp, #-0x38]
    // 0xbd91a0: r1 = LoadInt32Instr(r4)
    //     0xbd91a0: sbfx            x1, x4, #1, #0x1f
    // 0xbd91a4: cmp             x3, x1
    // 0xbd91a8: b.ne            #0xbd91b4
    // 0xbd91ac: mov             x1, x0
    // 0xbd91b0: r0 = _growToNextCapacity()
    //     0xbd91b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbd91b4: ldur            x2, [fp, #-0x20]
    // 0xbd91b8: ldur            x3, [fp, #-0x38]
    // 0xbd91bc: add             x0, x3, #1
    // 0xbd91c0: lsl             x1, x0, #1
    // 0xbd91c4: StoreField: r2->field_b = r1
    //     0xbd91c4: stur            w1, [x2, #0xb]
    // 0xbd91c8: LoadField: r1 = r2->field_f
    //     0xbd91c8: ldur            w1, [x2, #0xf]
    // 0xbd91cc: DecompressPointer r1
    //     0xbd91cc: add             x1, x1, HEAP, lsl #32
    // 0xbd91d0: ldur            x0, [fp, #-8]
    // 0xbd91d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbd91d4: add             x25, x1, x3, lsl #2
    //     0xbd91d8: add             x25, x25, #0xf
    //     0xbd91dc: str             w0, [x25]
    //     0xbd91e0: tbz             w0, #0, #0xbd91fc
    //     0xbd91e4: ldurb           w16, [x1, #-1]
    //     0xbd91e8: ldurb           w17, [x0, #-1]
    //     0xbd91ec: and             x16, x17, x16, lsr #2
    //     0xbd91f0: tst             x16, HEAP, lsr #32
    //     0xbd91f4: b.eq            #0xbd91fc
    //     0xbd91f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd91fc: b               #0xbd9204
    // 0xbd9200: mov             x2, x3
    // 0xbd9204: r0 = Row()
    //     0xbd9204: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbd9208: r1 = Instance_Axis
    //     0xbd9208: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd920c: StoreField: r0->field_f = r1
    //     0xbd920c: stur            w1, [x0, #0xf]
    // 0xbd9210: r1 = Instance_MainAxisAlignment
    //     0xbd9210: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbd9214: ldr             x1, [x1, #0xa08]
    // 0xbd9218: StoreField: r0->field_13 = r1
    //     0xbd9218: stur            w1, [x0, #0x13]
    // 0xbd921c: r1 = Instance_MainAxisSize
    //     0xbd921c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbd9220: ldr             x1, [x1, #0xa10]
    // 0xbd9224: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd9224: stur            w1, [x0, #0x17]
    // 0xbd9228: r1 = Instance_CrossAxisAlignment
    //     0xbd9228: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbd922c: ldr             x1, [x1, #0xa18]
    // 0xbd9230: StoreField: r0->field_1b = r1
    //     0xbd9230: stur            w1, [x0, #0x1b]
    // 0xbd9234: r1 = Instance_VerticalDirection
    //     0xbd9234: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbd9238: ldr             x1, [x1, #0xa20]
    // 0xbd923c: StoreField: r0->field_23 = r1
    //     0xbd923c: stur            w1, [x0, #0x23]
    // 0xbd9240: r1 = Instance_Clip
    //     0xbd9240: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbd9244: ldr             x1, [x1, #0x38]
    // 0xbd9248: StoreField: r0->field_2b = r1
    //     0xbd9248: stur            w1, [x0, #0x2b]
    // 0xbd924c: StoreField: r0->field_2f = rZR
    //     0xbd924c: stur            xzr, [x0, #0x2f]
    // 0xbd9250: ldur            x1, [fp, #-0x20]
    // 0xbd9254: StoreField: r0->field_b = r1
    //     0xbd9254: stur            w1, [x0, #0xb]
    // 0xbd9258: LeaveFrame
    //     0xbd9258: mov             SP, fp
    //     0xbd925c: ldp             fp, lr, [SP], #0x10
    // 0xbd9260: ret
    //     0xbd9260: ret             
    // 0xbd9264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9264: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9268: b               #0xbd8f2c
  }
  _ _buildCheckbox(/* No info */) {
    // ** addr: 0xbd926c, size: 0x100
    // 0xbd926c: EnterFrame
    //     0xbd926c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9270: mov             fp, SP
    // 0xbd9274: AllocStack(0x20)
    //     0xbd9274: sub             SP, SP, #0x20
    // 0xbd9278: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xbd9278: stur            x1, [fp, #-8]
    //     0xbd927c: stur            x2, [fp, #-0x10]
    // 0xbd9280: CheckStackOverflow
    //     0xbd9280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9284: cmp             SP, x16
    //     0xbd9288: b.ls            #0xbd9360
    // 0xbd928c: r1 = 3
    //     0xbd928c: movz            x1, #0x3
    // 0xbd9290: r0 = AllocateContext()
    //     0xbd9290: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd9294: mov             x3, x0
    // 0xbd9298: ldur            x0, [fp, #-8]
    // 0xbd929c: stur            x3, [fp, #-0x18]
    // 0xbd92a0: StoreField: r3->field_f = r0
    //     0xbd92a0: stur            w0, [x3, #0xf]
    // 0xbd92a4: ldur            x1, [fp, #-0x10]
    // 0xbd92a8: StoreField: r3->field_13 = r1
    //     0xbd92a8: stur            w1, [x3, #0x13]
    // 0xbd92ac: LoadField: r2 = r1->field_b
    //     0xbd92ac: ldur            w2, [x1, #0xb]
    // 0xbd92b0: DecompressPointer r2
    //     0xbd92b0: add             x2, x2, HEAP, lsl #32
    // 0xbd92b4: cmp             w2, NULL
    // 0xbd92b8: b.ne            #0xbd92c0
    // 0xbd92bc: r2 = ""
    //     0xbd92bc: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xbd92c0: ArrayStore: r3[0] = r2  ; List_4
    //     0xbd92c0: stur            w2, [x3, #0x17]
    // 0xbd92c4: LoadField: r1 = r0->field_13
    //     0xbd92c4: ldur            w1, [x0, #0x13]
    // 0xbd92c8: DecompressPointer r1
    //     0xbd92c8: add             x1, x1, HEAP, lsl #32
    // 0xbd92cc: r0 = contains()
    //     0xbd92cc: bl              #0x7deb98  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::contains
    // 0xbd92d0: mov             x2, x0
    // 0xbd92d4: ldur            x0, [fp, #-8]
    // 0xbd92d8: stur            x2, [fp, #-0x10]
    // 0xbd92dc: LoadField: r1 = r0->field_f
    //     0xbd92dc: ldur            w1, [x0, #0xf]
    // 0xbd92e0: DecompressPointer r1
    //     0xbd92e0: add             x1, x1, HEAP, lsl #32
    // 0xbd92e4: cmp             w1, NULL
    // 0xbd92e8: b.eq            #0xbd9368
    // 0xbd92ec: r0 = of()
    //     0xbd92ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbd92f0: LoadField: r1 = r0->field_5b
    //     0xbd92f0: ldur            w1, [x0, #0x5b]
    // 0xbd92f4: DecompressPointer r1
    //     0xbd92f4: add             x1, x1, HEAP, lsl #32
    // 0xbd92f8: stur            x1, [fp, #-8]
    // 0xbd92fc: r0 = Checkbox()
    //     0xbd92fc: bl              #0xa2e5f0  ; AllocateCheckboxStub -> Checkbox (size=0x5c)
    // 0xbd9300: mov             x3, x0
    // 0xbd9304: ldur            x0, [fp, #-0x10]
    // 0xbd9308: stur            x3, [fp, #-0x20]
    // 0xbd930c: StoreField: r3->field_b = r0
    //     0xbd930c: stur            w0, [x3, #0xb]
    // 0xbd9310: r0 = false
    //     0xbd9310: add             x0, NULL, #0x30  ; false
    // 0xbd9314: StoreField: r3->field_23 = r0
    //     0xbd9314: stur            w0, [x3, #0x23]
    // 0xbd9318: ldur            x2, [fp, #-0x18]
    // 0xbd931c: r1 = Function '<anonymous closure>':.
    //     0xbd931c: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a478] AnonymousClosure: (0xbd936c), in [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_buildCheckbox (0xbd926c)
    //     0xbd9320: ldr             x1, [x1, #0x478]
    // 0xbd9324: r0 = AllocateClosure()
    //     0xbd9324: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd9328: mov             x1, x0
    // 0xbd932c: ldur            x0, [fp, #-0x20]
    // 0xbd9330: StoreField: r0->field_f = r1
    //     0xbd9330: stur            w1, [x0, #0xf]
    // 0xbd9334: ldur            x1, [fp, #-8]
    // 0xbd9338: ArrayStore: r0[0] = r1  ; List_4
    //     0xbd9338: stur            w1, [x0, #0x17]
    // 0xbd933c: r1 = false
    //     0xbd933c: add             x1, NULL, #0x30  ; false
    // 0xbd9340: StoreField: r0->field_43 = r1
    //     0xbd9340: stur            w1, [x0, #0x43]
    // 0xbd9344: StoreField: r0->field_4f = r1
    //     0xbd9344: stur            w1, [x0, #0x4f]
    // 0xbd9348: r1 = Instance__CheckboxType
    //     0xbd9348: add             x1, PP, #0x53, lsl #12  ; [pp+0x53d80] Obj!_CheckboxType@d74601
    //     0xbd934c: ldr             x1, [x1, #0xd80]
    // 0xbd9350: StoreField: r0->field_57 = r1
    //     0xbd9350: stur            w1, [x0, #0x57]
    // 0xbd9354: LeaveFrame
    //     0xbd9354: mov             SP, fp
    //     0xbd9358: ldp             fp, lr, [SP], #0x10
    // 0xbd935c: ret
    //     0xbd935c: ret             
    // 0xbd9360: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9360: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9364: b               #0xbd928c
    // 0xbd9368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd9368: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool?) {
    // ** addr: 0xbd936c, size: 0x68
    // 0xbd936c: EnterFrame
    //     0xbd936c: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9370: mov             fp, SP
    // 0xbd9374: ldr             x0, [fp, #0x18]
    // 0xbd9378: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd9378: ldur            w1, [x0, #0x17]
    // 0xbd937c: DecompressPointer r1
    //     0xbd937c: add             x1, x1, HEAP, lsl #32
    // 0xbd9380: CheckStackOverflow
    //     0xbd9380: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9384: cmp             SP, x16
    //     0xbd9388: b.ls            #0xbd93cc
    // 0xbd938c: LoadField: r0 = r1->field_f
    //     0xbd938c: ldur            w0, [x1, #0xf]
    // 0xbd9390: DecompressPointer r0
    //     0xbd9390: add             x0, x0, HEAP, lsl #32
    // 0xbd9394: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xbd9394: ldur            w2, [x1, #0x17]
    // 0xbd9398: DecompressPointer r2
    //     0xbd9398: add             x2, x2, HEAP, lsl #32
    // 0xbd939c: ldr             x3, [fp, #0x10]
    // 0xbd93a0: cmp             w3, NULL
    // 0xbd93a4: b.ne            #0xbd93ac
    // 0xbd93a8: r3 = false
    //     0xbd93a8: add             x3, NULL, #0x30  ; false
    // 0xbd93ac: LoadField: r5 = r1->field_13
    //     0xbd93ac: ldur            w5, [x1, #0x13]
    // 0xbd93b0: DecompressPointer r5
    //     0xbd93b0: add             x5, x5, HEAP, lsl #32
    // 0xbd93b4: mov             x1, x0
    // 0xbd93b8: r0 = _handleCheckboxChange()
    //     0xbd93b8: bl              #0xbd93d4  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_handleCheckboxChange
    // 0xbd93bc: r0 = Null
    //     0xbd93bc: mov             x0, NULL
    // 0xbd93c0: LeaveFrame
    //     0xbd93c0: mov             SP, fp
    //     0xbd93c4: ldp             fp, lr, [SP], #0x10
    // 0xbd93c8: ret
    //     0xbd93c8: ret             
    // 0xbd93cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd93cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd93d0: b               #0xbd938c
  }
  _ _handleCheckboxChange(/* No info */) {
    // ** addr: 0xbd93d4, size: 0x44
    // 0xbd93d4: EnterFrame
    //     0xbd93d4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd93d8: mov             fp, SP
    // 0xbd93dc: mov             x0, x3
    // 0xbd93e0: mov             x3, x5
    // 0xbd93e4: CheckStackOverflow
    //     0xbd93e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd93e8: cmp             SP, x16
    //     0xbd93ec: b.ls            #0xbd9410
    // 0xbd93f0: tbnz            w0, #4, #0xbd93fc
    // 0xbd93f4: r0 = _selectItem()
    //     0xbd93f4: bl              #0xbd95fc  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_selectItem
    // 0xbd93f8: b               #0xbd9400
    // 0xbd93fc: r0 = _deselectItem()
    //     0xbd93fc: bl              #0xbd9418  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_deselectItem
    // 0xbd9400: r0 = Null
    //     0xbd9400: mov             x0, NULL
    // 0xbd9404: LeaveFrame
    //     0xbd9404: mov             SP, fp
    //     0xbd9408: ldp             fp, lr, [SP], #0x10
    // 0xbd940c: ret
    //     0xbd940c: ret             
    // 0xbd9410: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9410: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9414: b               #0xbd93f0
  }
  _ _deselectItem(/* No info */) {
    // ** addr: 0xbd9418, size: 0x7c
    // 0xbd9418: EnterFrame
    //     0xbd9418: stp             fp, lr, [SP, #-0x10]!
    //     0xbd941c: mov             fp, SP
    // 0xbd9420: AllocStack(0x18)
    //     0xbd9420: sub             SP, SP, #0x18
    // 0xbd9424: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbd9424: stur            x1, [fp, #-8]
    //     0xbd9428: stur            x2, [fp, #-0x10]
    //     0xbd942c: stur            x3, [fp, #-0x18]
    // 0xbd9430: CheckStackOverflow
    //     0xbd9430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9434: cmp             SP, x16
    //     0xbd9438: b.ls            #0xbd948c
    // 0xbd943c: r1 = 3
    //     0xbd943c: movz            x1, #0x3
    // 0xbd9440: r0 = AllocateContext()
    //     0xbd9440: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd9444: mov             x1, x0
    // 0xbd9448: ldur            x0, [fp, #-8]
    // 0xbd944c: StoreField: r1->field_f = r0
    //     0xbd944c: stur            w0, [x1, #0xf]
    // 0xbd9450: ldur            x2, [fp, #-0x10]
    // 0xbd9454: StoreField: r1->field_13 = r2
    //     0xbd9454: stur            w2, [x1, #0x13]
    // 0xbd9458: ldur            x2, [fp, #-0x18]
    // 0xbd945c: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd945c: stur            w2, [x1, #0x17]
    // 0xbd9460: mov             x2, x1
    // 0xbd9464: r1 = Function '<anonymous closure>':.
    //     0xbd9464: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a480] AnonymousClosure: (0xbd9494), in [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_deselectItem (0xbd9418)
    //     0xbd9468: ldr             x1, [x1, #0x480]
    // 0xbd946c: r0 = AllocateClosure()
    //     0xbd946c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd9470: ldur            x1, [fp, #-8]
    // 0xbd9474: mov             x2, x0
    // 0xbd9478: r0 = setState()
    //     0xbd9478: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbd947c: r0 = Null
    //     0xbd947c: mov             x0, NULL
    // 0xbd9480: LeaveFrame
    //     0xbd9480: mov             SP, fp
    //     0xbd9484: ldp             fp, lr, [SP], #0x10
    // 0xbd9488: ret
    //     0xbd9488: ret             
    // 0xbd948c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd948c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9490: b               #0xbd943c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd9494, size: 0x168
    // 0xbd9494: EnterFrame
    //     0xbd9494: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9498: mov             fp, SP
    // 0xbd949c: AllocStack(0x30)
    //     0xbd949c: sub             SP, SP, #0x30
    // 0xbd94a0: SetupParameters()
    //     0xbd94a0: ldr             x0, [fp, #0x10]
    //     0xbd94a4: ldur            w3, [x0, #0x17]
    //     0xbd94a8: add             x3, x3, HEAP, lsl #32
    //     0xbd94ac: stur            x3, [fp, #-8]
    // 0xbd94b0: CheckStackOverflow
    //     0xbd94b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd94b4: cmp             SP, x16
    //     0xbd94b8: b.ls            #0xbd95f0
    // 0xbd94bc: LoadField: r0 = r3->field_f
    //     0xbd94bc: ldur            w0, [x3, #0xf]
    // 0xbd94c0: DecompressPointer r0
    //     0xbd94c0: add             x0, x0, HEAP, lsl #32
    // 0xbd94c4: LoadField: r1 = r0->field_13
    //     0xbd94c4: ldur            w1, [x0, #0x13]
    // 0xbd94c8: DecompressPointer r1
    //     0xbd94c8: add             x1, x1, HEAP, lsl #32
    // 0xbd94cc: LoadField: r2 = r3->field_13
    //     0xbd94cc: ldur            w2, [x3, #0x13]
    // 0xbd94d0: DecompressPointer r2
    //     0xbd94d0: add             x2, x2, HEAP, lsl #32
    // 0xbd94d4: r0 = remove()
    //     0xbd94d4: bl              #0x16981d8  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::remove
    // 0xbd94d8: ldur            x0, [fp, #-8]
    // 0xbd94dc: LoadField: r1 = r0->field_f
    //     0xbd94dc: ldur            w1, [x0, #0xf]
    // 0xbd94e0: DecompressPointer r1
    //     0xbd94e0: add             x1, x1, HEAP, lsl #32
    // 0xbd94e4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbd94e4: ldur            w2, [x0, #0x17]
    // 0xbd94e8: DecompressPointer r2
    //     0xbd94e8: add             x2, x2, HEAP, lsl #32
    // 0xbd94ec: LoadField: r3 = r2->field_b
    //     0xbd94ec: ldur            w3, [x2, #0xb]
    // 0xbd94f0: DecompressPointer r3
    //     0xbd94f0: add             x3, x3, HEAP, lsl #32
    // 0xbd94f4: mov             x2, x3
    // 0xbd94f8: r0 = _findAndRemoveCustomerResponse()
    //     0xbd94f8: bl              #0xa3cf18  ; [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_findAndRemoveCustomerResponse
    // 0xbd94fc: mov             x2, x0
    // 0xbd9500: cmp             w2, NULL
    // 0xbd9504: b.eq            #0xbd95e0
    // 0xbd9508: ldur            x0, [fp, #-8]
    // 0xbd950c: LoadField: r1 = r0->field_f
    //     0xbd950c: ldur            w1, [x0, #0xf]
    // 0xbd9510: DecompressPointer r1
    //     0xbd9510: add             x1, x1, HEAP, lsl #32
    // 0xbd9514: LoadField: r3 = r1->field_b
    //     0xbd9514: ldur            w3, [x1, #0xb]
    // 0xbd9518: DecompressPointer r3
    //     0xbd9518: add             x3, x3, HEAP, lsl #32
    // 0xbd951c: cmp             w3, NULL
    // 0xbd9520: b.eq            #0xbd95f8
    // 0xbd9524: LoadField: r4 = r3->field_b
    //     0xbd9524: ldur            w4, [x3, #0xb]
    // 0xbd9528: DecompressPointer r4
    //     0xbd9528: add             x4, x4, HEAP, lsl #32
    // 0xbd952c: cmp             w4, NULL
    // 0xbd9530: b.ne            #0xbd953c
    // 0xbd9534: r4 = Null
    //     0xbd9534: mov             x4, NULL
    // 0xbd9538: b               #0xbd9548
    // 0xbd953c: LoadField: r5 = r4->field_2b
    //     0xbd953c: ldur            w5, [x4, #0x2b]
    // 0xbd9540: DecompressPointer r5
    //     0xbd9540: add             x5, x5, HEAP, lsl #32
    // 0xbd9544: mov             x4, x5
    // 0xbd9548: cmp             w4, NULL
    // 0xbd954c: b.ne            #0xbd9554
    // 0xbd9550: r4 = false
    //     0xbd9550: add             x4, NULL, #0x30  ; false
    // 0xbd9554: ArrayLoad: r5 = r1[0]  ; List_4
    //     0xbd9554: ldur            w5, [x1, #0x17]
    // 0xbd9558: DecompressPointer r5
    //     0xbd9558: add             x5, x5, HEAP, lsl #32
    // 0xbd955c: LoadField: r1 = r5->field_b
    //     0xbd955c: ldur            w1, [x5, #0xb]
    // 0xbd9560: cbnz            w1, #0xbd956c
    // 0xbd9564: r5 = false
    //     0xbd9564: add             x5, NULL, #0x30  ; false
    // 0xbd9568: b               #0xbd9570
    // 0xbd956c: r5 = true
    //     0xbd956c: add             x5, NULL, #0x20  ; true
    // 0xbd9570: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd9570: ldur            w1, [x0, #0x17]
    // 0xbd9574: DecompressPointer r1
    //     0xbd9574: add             x1, x1, HEAP, lsl #32
    // 0xbd9578: LoadField: r0 = r1->field_13
    //     0xbd9578: ldur            w0, [x1, #0x13]
    // 0xbd957c: DecompressPointer r0
    //     0xbd957c: add             x0, x0, HEAP, lsl #32
    // 0xbd9580: cmp             w0, NULL
    // 0xbd9584: b.ne            #0xbd9590
    // 0xbd9588: r6 = 0
    //     0xbd9588: movz            x6, #0
    // 0xbd958c: b               #0xbd95a0
    // 0xbd9590: r1 = LoadInt32Instr(r0)
    //     0xbd9590: sbfx            x1, x0, #1, #0x1f
    //     0xbd9594: tbz             w0, #0, #0xbd959c
    //     0xbd9598: ldur            x1, [x0, #7]
    // 0xbd959c: mov             x6, x1
    // 0xbd95a0: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xbd95a0: ldur            w7, [x3, #0x17]
    // 0xbd95a4: DecompressPointer r7
    //     0xbd95a4: add             x7, x7, HEAP, lsl #32
    // 0xbd95a8: r0 = BoxInt64Instr(r6)
    //     0xbd95a8: sbfiz           x0, x6, #1, #0x1f
    //     0xbd95ac: cmp             x6, x0, asr #1
    //     0xbd95b0: b.eq            #0xbd95bc
    //     0xbd95b4: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd95b8: stur            x6, [x0, #7]
    // 0xbd95bc: stp             x0, x7, [SP, #0x18]
    // 0xbd95c0: stp             x4, x5, [SP, #8]
    // 0xbd95c4: str             x2, [SP]
    // 0xbd95c8: r4 = 0
    //     0xbd95c8: movz            x4, #0
    // 0xbd95cc: ldr             x0, [SP, #0x20]
    // 0xbd95d0: r16 = UnlinkedCall_0x613b5c
    //     0xbd95d0: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a488] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd95d4: add             x16, x16, #0x488
    // 0xbd95d8: ldp             x5, lr, [x16]
    // 0xbd95dc: blr             lr
    // 0xbd95e0: r0 = Null
    //     0xbd95e0: mov             x0, NULL
    // 0xbd95e4: LeaveFrame
    //     0xbd95e4: mov             SP, fp
    //     0xbd95e8: ldp             fp, lr, [SP], #0x10
    // 0xbd95ec: ret
    //     0xbd95ec: ret             
    // 0xbd95f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd95f0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd95f4: b               #0xbd94bc
    // 0xbd95f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd95f8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _selectItem(/* No info */) {
    // ** addr: 0xbd95fc, size: 0x7c
    // 0xbd95fc: EnterFrame
    //     0xbd95fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbd9600: mov             fp, SP
    // 0xbd9604: AllocStack(0x18)
    //     0xbd9604: sub             SP, SP, #0x18
    // 0xbd9608: SetupParameters(_CustomizationMultiSelectState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xbd9608: stur            x1, [fp, #-8]
    //     0xbd960c: stur            x2, [fp, #-0x10]
    //     0xbd9610: stur            x3, [fp, #-0x18]
    // 0xbd9614: CheckStackOverflow
    //     0xbd9614: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9618: cmp             SP, x16
    //     0xbd961c: b.ls            #0xbd9670
    // 0xbd9620: r1 = 3
    //     0xbd9620: movz            x1, #0x3
    // 0xbd9624: r0 = AllocateContext()
    //     0xbd9624: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd9628: mov             x1, x0
    // 0xbd962c: ldur            x0, [fp, #-8]
    // 0xbd9630: StoreField: r1->field_f = r0
    //     0xbd9630: stur            w0, [x1, #0xf]
    // 0xbd9634: ldur            x2, [fp, #-0x10]
    // 0xbd9638: StoreField: r1->field_13 = r2
    //     0xbd9638: stur            w2, [x1, #0x13]
    // 0xbd963c: ldur            x2, [fp, #-0x18]
    // 0xbd9640: ArrayStore: r1[0] = r2  ; List_4
    //     0xbd9640: stur            w2, [x1, #0x17]
    // 0xbd9644: mov             x2, x1
    // 0xbd9648: r1 = Function '<anonymous closure>':.
    //     0xbd9648: add             x1, PP, #0x6a, lsl #12  ; [pp+0x6a4a0] AnonymousClosure: (0xbd9678), in [package:customer_app/app/presentation/views/line/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_selectItem (0xbd95fc)
    //     0xbd964c: ldr             x1, [x1, #0x4a0]
    // 0xbd9650: r0 = AllocateClosure()
    //     0xbd9650: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd9654: ldur            x1, [fp, #-8]
    // 0xbd9658: mov             x2, x0
    // 0xbd965c: r0 = setState()
    //     0xbd965c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xbd9660: r0 = Null
    //     0xbd9660: mov             x0, NULL
    // 0xbd9664: LeaveFrame
    //     0xbd9664: mov             SP, fp
    //     0xbd9668: ldp             fp, lr, [SP], #0x10
    // 0xbd966c: ret
    //     0xbd966c: ret             
    // 0xbd9670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9670: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9674: b               #0xbd9620
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbd9678, size: 0x198
    // 0xbd9678: EnterFrame
    //     0xbd9678: stp             fp, lr, [SP, #-0x10]!
    //     0xbd967c: mov             fp, SP
    // 0xbd9680: AllocStack(0x38)
    //     0xbd9680: sub             SP, SP, #0x38
    // 0xbd9684: SetupParameters()
    //     0xbd9684: ldr             x0, [fp, #0x10]
    //     0xbd9688: ldur            w3, [x0, #0x17]
    //     0xbd968c: add             x3, x3, HEAP, lsl #32
    //     0xbd9690: stur            x3, [fp, #-8]
    // 0xbd9694: CheckStackOverflow
    //     0xbd9694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd9698: cmp             SP, x16
    //     0xbd969c: b.ls            #0xbd9804
    // 0xbd96a0: LoadField: r0 = r3->field_f
    //     0xbd96a0: ldur            w0, [x3, #0xf]
    // 0xbd96a4: DecompressPointer r0
    //     0xbd96a4: add             x0, x0, HEAP, lsl #32
    // 0xbd96a8: LoadField: r1 = r0->field_13
    //     0xbd96a8: ldur            w1, [x0, #0x13]
    // 0xbd96ac: DecompressPointer r1
    //     0xbd96ac: add             x1, x1, HEAP, lsl #32
    // 0xbd96b0: LoadField: r2 = r3->field_13
    //     0xbd96b0: ldur            w2, [x3, #0x13]
    // 0xbd96b4: DecompressPointer r2
    //     0xbd96b4: add             x2, x2, HEAP, lsl #32
    // 0xbd96b8: r0 = add()
    //     0xbd96b8: bl              #0x16a4098  ; [dart:_compact_hash] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0xbd96bc: ldur            x0, [fp, #-8]
    // 0xbd96c0: LoadField: r1 = r0->field_f
    //     0xbd96c0: ldur            w1, [x0, #0xf]
    // 0xbd96c4: DecompressPointer r1
    //     0xbd96c4: add             x1, x1, HEAP, lsl #32
    // 0xbd96c8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbd96c8: ldur            w2, [x0, #0x17]
    // 0xbd96cc: DecompressPointer r2
    //     0xbd96cc: add             x2, x2, HEAP, lsl #32
    // 0xbd96d0: r0 = _createCustomerResponse()
    //     0xbd96d0: bl              #0xa3d1e0  ; [package:customer_app/app/presentation/views/basic/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_createCustomerResponse
    // 0xbd96d4: mov             x2, x0
    // 0xbd96d8: ldur            x0, [fp, #-8]
    // 0xbd96dc: stur            x2, [fp, #-0x20]
    // 0xbd96e0: LoadField: r1 = r0->field_f
    //     0xbd96e0: ldur            w1, [x0, #0xf]
    // 0xbd96e4: DecompressPointer r1
    //     0xbd96e4: add             x1, x1, HEAP, lsl #32
    // 0xbd96e8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xbd96e8: ldur            w3, [x1, #0x17]
    // 0xbd96ec: DecompressPointer r3
    //     0xbd96ec: add             x3, x3, HEAP, lsl #32
    // 0xbd96f0: stur            x3, [fp, #-0x18]
    // 0xbd96f4: LoadField: r1 = r3->field_b
    //     0xbd96f4: ldur            w1, [x3, #0xb]
    // 0xbd96f8: LoadField: r4 = r3->field_f
    //     0xbd96f8: ldur            w4, [x3, #0xf]
    // 0xbd96fc: DecompressPointer r4
    //     0xbd96fc: add             x4, x4, HEAP, lsl #32
    // 0xbd9700: LoadField: r5 = r4->field_b
    //     0xbd9700: ldur            w5, [x4, #0xb]
    // 0xbd9704: r4 = LoadInt32Instr(r1)
    //     0xbd9704: sbfx            x4, x1, #1, #0x1f
    // 0xbd9708: stur            x4, [fp, #-0x10]
    // 0xbd970c: r1 = LoadInt32Instr(r5)
    //     0xbd970c: sbfx            x1, x5, #1, #0x1f
    // 0xbd9710: cmp             x4, x1
    // 0xbd9714: b.ne            #0xbd9720
    // 0xbd9718: mov             x1, x3
    // 0xbd971c: r0 = _growToNextCapacity()
    //     0xbd971c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xbd9720: ldur            x2, [fp, #-8]
    // 0xbd9724: ldur            x0, [fp, #-0x18]
    // 0xbd9728: ldur            x3, [fp, #-0x10]
    // 0xbd972c: add             x1, x3, #1
    // 0xbd9730: lsl             x4, x1, #1
    // 0xbd9734: StoreField: r0->field_b = r4
    //     0xbd9734: stur            w4, [x0, #0xb]
    // 0xbd9738: LoadField: r1 = r0->field_f
    //     0xbd9738: ldur            w1, [x0, #0xf]
    // 0xbd973c: DecompressPointer r1
    //     0xbd973c: add             x1, x1, HEAP, lsl #32
    // 0xbd9740: ldur            x0, [fp, #-0x20]
    // 0xbd9744: ArrayStore: r1[r3] = r0  ; List_4
    //     0xbd9744: add             x25, x1, x3, lsl #2
    //     0xbd9748: add             x25, x25, #0xf
    //     0xbd974c: str             w0, [x25]
    //     0xbd9750: tbz             w0, #0, #0xbd976c
    //     0xbd9754: ldurb           w16, [x1, #-1]
    //     0xbd9758: ldurb           w17, [x0, #-1]
    //     0xbd975c: and             x16, x17, x16, lsr #2
    //     0xbd9760: tst             x16, HEAP, lsr #32
    //     0xbd9764: b.eq            #0xbd976c
    //     0xbd9768: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xbd976c: LoadField: r0 = r2->field_f
    //     0xbd976c: ldur            w0, [x2, #0xf]
    // 0xbd9770: DecompressPointer r0
    //     0xbd9770: add             x0, x0, HEAP, lsl #32
    // 0xbd9774: LoadField: r1 = r0->field_b
    //     0xbd9774: ldur            w1, [x0, #0xb]
    // 0xbd9778: DecompressPointer r1
    //     0xbd9778: add             x1, x1, HEAP, lsl #32
    // 0xbd977c: cmp             w1, NULL
    // 0xbd9780: b.eq            #0xbd980c
    // 0xbd9784: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xbd9784: ldur            w0, [x2, #0x17]
    // 0xbd9788: DecompressPointer r0
    //     0xbd9788: add             x0, x0, HEAP, lsl #32
    // 0xbd978c: LoadField: r2 = r0->field_13
    //     0xbd978c: ldur            w2, [x0, #0x13]
    // 0xbd9790: DecompressPointer r2
    //     0xbd9790: add             x2, x2, HEAP, lsl #32
    // 0xbd9794: cmp             w2, NULL
    // 0xbd9798: b.ne            #0xbd97a4
    // 0xbd979c: r2 = 0
    //     0xbd979c: movz            x2, #0
    // 0xbd97a0: b               #0xbd97b4
    // 0xbd97a4: r0 = LoadInt32Instr(r2)
    //     0xbd97a4: sbfx            x0, x2, #1, #0x1f
    //     0xbd97a8: tbz             w2, #0, #0xbd97b0
    //     0xbd97ac: ldur            x0, [x2, #7]
    // 0xbd97b0: mov             x2, x0
    // 0xbd97b4: LoadField: r3 = r1->field_13
    //     0xbd97b4: ldur            w3, [x1, #0x13]
    // 0xbd97b8: DecompressPointer r3
    //     0xbd97b8: add             x3, x3, HEAP, lsl #32
    // 0xbd97bc: r0 = BoxInt64Instr(r2)
    //     0xbd97bc: sbfiz           x0, x2, #1, #0x1f
    //     0xbd97c0: cmp             x2, x0, asr #1
    //     0xbd97c4: b.eq            #0xbd97d0
    //     0xbd97c8: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xbd97cc: stur            x2, [x0, #7]
    // 0xbd97d0: stp             x0, x3, [SP, #8]
    // 0xbd97d4: ldur            x16, [fp, #-0x20]
    // 0xbd97d8: str             x16, [SP]
    // 0xbd97dc: r4 = 0
    //     0xbd97dc: movz            x4, #0
    // 0xbd97e0: ldr             x0, [SP, #0x10]
    // 0xbd97e4: r16 = UnlinkedCall_0x613b5c
    //     0xbd97e4: add             x16, PP, #0x6a, lsl #12  ; [pp+0x6a4a8] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xbd97e8: add             x16, x16, #0x4a8
    // 0xbd97ec: ldp             x5, lr, [x16]
    // 0xbd97f0: blr             lr
    // 0xbd97f4: r0 = Null
    //     0xbd97f4: mov             x0, NULL
    // 0xbd97f8: LeaveFrame
    //     0xbd97f8: mov             SP, fp
    //     0xbd97fc: ldp             fp, lr, [SP], #0x10
    // 0xbd9800: ret
    //     0xbd9800: ret             
    // 0xbd9804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd9804: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd9808: b               #0xbd96a0
    // 0xbd980c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd980c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4003, size: 0x1c, field offset: 0xc
//   const constructor, 
class CustomizationMultiSelect extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80818, size: 0x48
    // 0xc80818: EnterFrame
    //     0xc80818: stp             fp, lr, [SP, #-0x10]!
    //     0xc8081c: mov             fp, SP
    // 0xc80820: AllocStack(0x8)
    //     0xc80820: sub             SP, SP, #8
    // 0xc80824: CheckStackOverflow
    //     0xc80824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc80828: cmp             SP, x16
    //     0xc8082c: b.ls            #0xc80858
    // 0xc80830: r1 = <CustomizationMultiSelect>
    //     0xc80830: add             x1, PP, #0x61, lsl #12  ; [pp+0x61c38] TypeArguments: <CustomizationMultiSelect>
    //     0xc80834: ldr             x1, [x1, #0xc38]
    // 0xc80838: r0 = _CustomizationMultiSelectState()
    //     0xc80838: bl              #0xc80860  ; Allocate_CustomizationMultiSelectStateStub -> _CustomizationMultiSelectState (size=0x1c)
    // 0xc8083c: mov             x1, x0
    // 0xc80840: stur            x0, [fp, #-8]
    // 0xc80844: r0 = _CustomizationMultiSelectState()
    //     0xc80844: bl              #0xc7b854  ; [package:customer_app/app/presentation/views/basic/customization/customization_multi_select_view.dart] _CustomizationMultiSelectState::_CustomizationMultiSelectState
    // 0xc80848: ldur            x0, [fp, #-8]
    // 0xc8084c: LeaveFrame
    //     0xc8084c: mov             SP, fp
    //     0xc80850: ldp             fp, lr, [SP], #0x10
    // 0xc80854: ret
    //     0xc80854: ret             
    // 0xc80858: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc80858: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8085c: b               #0xc80830
  }
}
