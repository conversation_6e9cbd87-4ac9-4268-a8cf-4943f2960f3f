// lib: , url: package:dio/src/headers.dart

// class id: 1049606, size: 0x8
class :: {
}

// class id: 4982, size: 0xc, field offset: 0x8
class Headers extends Object {

  _ Headers.fromMap(/* No info */) {
    // ** addr: 0x862314, size: 0xc0
    // 0x862314: EnterFrame
    //     0x862314: stp             fp, lr, [SP, #-0x10]!
    //     0x862318: mov             fp, SP
    // 0x86231c: AllocStack(0x28)
    //     0x86231c: sub             SP, SP, #0x28
    // 0x862320: SetupParameters(Headers this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x862320: mov             x3, x1
    //     0x862324: mov             x0, x2
    //     0x862328: stur            x1, [fp, #-8]
    //     0x86232c: stur            x2, [fp, #-0x10]
    // 0x862330: CheckStackOverflow
    //     0x862330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862334: cmp             SP, x16
    //     0x862338: b.ls            #0x8623cc
    // 0x86233c: r1 = Function '<anonymous closure>':.
    //     0x86233c: add             x1, PP, #8, lsl #12  ; [pp+0x8b20] AnonymousClosure: (0x862b4c), in [package:dio/src/headers.dart] Headers::Headers.fromMap (0x862314)
    //     0x862340: ldr             x1, [x1, #0xb20]
    // 0x862344: r2 = Null
    //     0x862344: mov             x2, NULL
    // 0x862348: r0 = AllocateClosure()
    //     0x862348: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x86234c: mov             x1, x0
    // 0x862350: ldur            x0, [fp, #-0x10]
    // 0x862354: r2 = LoadClassIdInstr(r0)
    //     0x862354: ldur            x2, [x0, #-1]
    //     0x862358: ubfx            x2, x2, #0xc, #0x14
    // 0x86235c: r16 = <String, List<String>>
    //     0x86235c: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] TypeArguments: <String, List<String>>
    //     0x862360: ldr             x16, [x16, #0xb28]
    // 0x862364: stp             x0, x16, [SP, #8]
    // 0x862368: str             x1, [SP]
    // 0x86236c: mov             x0, x2
    // 0x862370: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x862370: ldr             x4, [PP, #0x7e8]  ; [pp+0x7e8] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x862374: r0 = GDT[cid_x0 + 0x10ba7]()
    //     0x862374: movz            x17, #0xba7
    //     0x862378: movk            x17, #0x1, lsl #16
    //     0x86237c: add             lr, x0, x17
    //     0x862380: ldr             lr, [x21, lr, lsl #3]
    //     0x862384: blr             lr
    // 0x862388: r16 = <List<String>>
    //     0x862388: add             x16, PP, #8, lsl #12  ; [pp+0x8b30] TypeArguments: <List<String>>
    //     0x86238c: ldr             x16, [x16, #0xb30]
    // 0x862390: stp             x0, x16, [SP]
    // 0x862394: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x862394: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x862398: r0 = caseInsensitiveKeyMap()
    //     0x862398: bl              #0x8626d4  ; [package:dio/src/utils.dart] ::caseInsensitiveKeyMap
    // 0x86239c: ldur            x1, [fp, #-8]
    // 0x8623a0: StoreField: r1->field_7 = r0
    //     0x8623a0: stur            w0, [x1, #7]
    //     0x8623a4: ldurb           w16, [x1, #-1]
    //     0x8623a8: ldurb           w17, [x0, #-1]
    //     0x8623ac: and             x16, x17, x16, lsr #2
    //     0x8623b0: tst             x16, HEAP, lsr #32
    //     0x8623b4: b.eq            #0x8623bc
    //     0x8623b8: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8623bc: r0 = Null
    //     0x8623bc: mov             x0, NULL
    // 0x8623c0: LeaveFrame
    //     0x8623c0: mov             SP, fp
    //     0x8623c4: ldp             fp, lr, [SP], #0x10
    // 0x8623c8: ret
    //     0x8623c8: ret             
    // 0x8623cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8623cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8623d0: b               #0x86233c
  }
  void forEach(Headers, (dynamic, String, List<String>) => void) {
    // ** addr: 0x8623ec, size: 0x6c
    // 0x8623ec: EnterFrame
    //     0x8623ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8623f0: mov             fp, SP
    // 0x8623f4: CheckStackOverflow
    //     0x8623f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8623f8: cmp             SP, x16
    //     0x8623fc: b.ls            #0x862438
    // 0x862400: ldr             x0, [fp, #0x10]
    // 0x862404: r2 = Null
    //     0x862404: mov             x2, NULL
    // 0x862408: r1 = Null
    //     0x862408: mov             x1, NULL
    // 0x86240c: r8 = (dynamic this, String, List<String>) => void?
    //     0x86240c: add             x8, PP, #0x26, lsl #12  ; [pp+0x26258] FunctionType: (dynamic this, String, List<String>) => void?
    //     0x862410: ldr             x8, [x8, #0x258]
    // 0x862414: r3 = Null
    //     0x862414: add             x3, PP, #0x26, lsl #12  ; [pp+0x26260] Null
    //     0x862418: ldr             x3, [x3, #0x260]
    // 0x86241c: r0 = DefaultTypeTest()
    //     0x86241c: bl              #0x16f5090  ; DefaultTypeTestStub
    // 0x862420: ldr             x1, [fp, #0x18]
    // 0x862424: ldr             x2, [fp, #0x10]
    // 0x862428: r0 = forEach()
    //     0x862428: bl              #0x862440  ; [package:dio/src/headers.dart] Headers::forEach
    // 0x86242c: LeaveFrame
    //     0x86242c: mov             SP, fp
    //     0x862430: ldp             fp, lr, [SP], #0x10
    // 0x862434: ret
    //     0x862434: ret             
    // 0x862438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862438: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86243c: b               #0x862400
  }
  void forEach(Headers, (dynamic, String, List<String>) => void) {
    // ** addr: 0x862440, size: 0x144
    // 0x862440: EnterFrame
    //     0x862440: stp             fp, lr, [SP, #-0x10]!
    //     0x862444: mov             fp, SP
    // 0x862448: AllocStack(0x40)
    //     0x862448: sub             SP, SP, #0x40
    // 0x86244c: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x86244c: stur            x2, [fp, #-0x10]
    // 0x862450: CheckStackOverflow
    //     0x862450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862454: cmp             SP, x16
    //     0x862458: b.ls            #0x862570
    // 0x86245c: LoadField: r3 = r1->field_7
    //     0x86245c: ldur            w3, [x1, #7]
    // 0x862460: DecompressPointer r3
    //     0x862460: add             x3, x3, HEAP, lsl #32
    // 0x862464: stur            x3, [fp, #-8]
    // 0x862468: r0 = LoadClassIdInstr(r3)
    //     0x862468: ldur            x0, [x3, #-1]
    //     0x86246c: ubfx            x0, x0, #0xc, #0x14
    // 0x862470: mov             x1, x3
    // 0x862474: r0 = GDT[cid_x0 + 0x5fc]()
    //     0x862474: add             lr, x0, #0x5fc
    //     0x862478: ldr             lr, [x21, lr, lsl #3]
    //     0x86247c: blr             lr
    // 0x862480: mov             x1, x0
    // 0x862484: r0 = iterator()
    //     0x862484: bl              #0x7e2290  ; [dart:_compact_hash] _CompactKeysIterable::iterator
    // 0x862488: stur            x0, [fp, #-0x20]
    // 0x86248c: LoadField: r2 = r0->field_7
    //     0x86248c: ldur            w2, [x0, #7]
    // 0x862490: DecompressPointer r2
    //     0x862490: add             x2, x2, HEAP, lsl #32
    // 0x862494: stur            x2, [fp, #-0x18]
    // 0x862498: ldur            x3, [fp, #-8]
    // 0x86249c: CheckStackOverflow
    //     0x86249c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8624a0: cmp             SP, x16
    //     0x8624a4: b.ls            #0x862578
    // 0x8624a8: mov             x1, x0
    // 0x8624ac: r0 = moveNext()
    //     0x8624ac: bl              #0x1626cc8  ; [dart:_compact_hash] _CompactIterator::moveNext
    // 0x8624b0: tbnz            w0, #4, #0x862560
    // 0x8624b4: ldur            x3, [fp, #-0x20]
    // 0x8624b8: LoadField: r4 = r3->field_33
    //     0x8624b8: ldur            w4, [x3, #0x33]
    // 0x8624bc: DecompressPointer r4
    //     0x8624bc: add             x4, x4, HEAP, lsl #32
    // 0x8624c0: stur            x4, [fp, #-0x28]
    // 0x8624c4: cmp             w4, NULL
    // 0x8624c8: b.ne            #0x8624fc
    // 0x8624cc: mov             x0, x4
    // 0x8624d0: ldur            x2, [fp, #-0x18]
    // 0x8624d4: r1 = Null
    //     0x8624d4: mov             x1, NULL
    // 0x8624d8: cmp             w2, NULL
    // 0x8624dc: b.eq            #0x8624fc
    // 0x8624e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8624e0: ldur            w4, [x2, #0x17]
    // 0x8624e4: DecompressPointer r4
    //     0x8624e4: add             x4, x4, HEAP, lsl #32
    // 0x8624e8: r8 = X0
    //     0x8624e8: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x8624ec: LoadField: r9 = r4->field_7
    //     0x8624ec: ldur            x9, [x4, #7]
    // 0x8624f0: r3 = Null
    //     0x8624f0: add             x3, PP, #0x26, lsl #12  ; [pp+0x26270] Null
    //     0x8624f4: ldr             x3, [x3, #0x270]
    // 0x8624f8: blr             x9
    // 0x8624fc: ldur            x0, [fp, #-8]
    // 0x862500: ldur            x1, [fp, #-0x28]
    // 0x862504: r0 = trim()
    //     0x862504: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x862508: ldur            x3, [fp, #-8]
    // 0x86250c: r1 = LoadClassIdInstr(r3)
    //     0x86250c: ldur            x1, [x3, #-1]
    //     0x862510: ubfx            x1, x1, #0xc, #0x14
    // 0x862514: mov             x2, x0
    // 0x862518: mov             x0, x1
    // 0x86251c: mov             x1, x3
    // 0x862520: r0 = GDT[cid_x0 + -0xfe]()
    //     0x862520: sub             lr, x0, #0xfe
    //     0x862524: ldr             lr, [x21, lr, lsl #3]
    //     0x862528: blr             lr
    // 0x86252c: cmp             w0, NULL
    // 0x862530: b.eq            #0x862580
    // 0x862534: ldur            x16, [fp, #-0x10]
    // 0x862538: ldur            lr, [fp, #-0x28]
    // 0x86253c: stp             lr, x16, [SP, #8]
    // 0x862540: str             x0, [SP]
    // 0x862544: ldur            x0, [fp, #-0x10]
    // 0x862548: ClosureCall
    //     0x862548: ldr             x4, [PP, #0x4a0]  ; [pp+0x4a0] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x86254c: ldur            x2, [x0, #0x1f]
    //     0x862550: blr             x2
    // 0x862554: ldur            x0, [fp, #-0x20]
    // 0x862558: ldur            x2, [fp, #-0x18]
    // 0x86255c: b               #0x862498
    // 0x862560: r0 = Null
    //     0x862560: mov             x0, NULL
    // 0x862564: LeaveFrame
    //     0x862564: mov             SP, fp
    //     0x862568: ldp             fp, lr, [SP], #0x10
    // 0x86256c: ret
    //     0x86256c: ret             
    // 0x862570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862570: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862574: b               #0x86245c
    // 0x862578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862578: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86257c: b               #0x8624a8
    // 0x862580: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x862580: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  bool dyn:get:isEmpty(Headers) {
    // ** addr: 0x86259c, size: 0x48
    // 0x86259c: EnterFrame
    //     0x86259c: stp             fp, lr, [SP, #-0x10]!
    //     0x8625a0: mov             fp, SP
    // 0x8625a4: CheckStackOverflow
    //     0x8625a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8625a8: cmp             SP, x16
    //     0x8625ac: b.ls            #0x8625c4
    // 0x8625b0: ldr             x1, [fp, #0x10]
    // 0x8625b4: r0 = isEmpty()
    //     0x8625b4: bl              #0x8625cc  ; [package:dio/src/headers.dart] Headers::isEmpty
    // 0x8625b8: LeaveFrame
    //     0x8625b8: mov             SP, fp
    //     0x8625bc: ldp             fp, lr, [SP], #0x10
    // 0x8625c0: ret
    //     0x8625c0: ret             
    // 0x8625c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8625c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8625c8: b               #0x8625b0
  }
  bool isEmpty(Headers) {
    // ** addr: 0x8625cc, size: 0x50
    // 0x8625cc: EnterFrame
    //     0x8625cc: stp             fp, lr, [SP, #-0x10]!
    //     0x8625d0: mov             fp, SP
    // 0x8625d4: CheckStackOverflow
    //     0x8625d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8625d8: cmp             SP, x16
    //     0x8625dc: b.ls            #0x862614
    // 0x8625e0: LoadField: r0 = r1->field_7
    //     0x8625e0: ldur            w0, [x1, #7]
    // 0x8625e4: DecompressPointer r0
    //     0x8625e4: add             x0, x0, HEAP, lsl #32
    // 0x8625e8: r1 = LoadClassIdInstr(r0)
    //     0x8625e8: ldur            x1, [x0, #-1]
    //     0x8625ec: ubfx            x1, x1, #0xc, #0x14
    // 0x8625f0: mov             x16, x0
    // 0x8625f4: mov             x0, x1
    // 0x8625f8: mov             x1, x16
    // 0x8625fc: r0 = GDT[cid_x0 + 0x667]()
    //     0x8625fc: add             lr, x0, #0x667
    //     0x862600: ldr             lr, [x21, lr, lsl #3]
    //     0x862604: blr             lr
    // 0x862608: LeaveFrame
    //     0x862608: mov             SP, fp
    //     0x86260c: ldp             fp, lr, [SP], #0x10
    // 0x862610: ret
    //     0x862610: ret             
    // 0x862614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862614: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862618: b               #0x8625e0
  }
  List<String>? [](Headers, String) {
    // ** addr: 0x862634, size: 0xb8
    // 0x862634: EnterFrame
    //     0x862634: stp             fp, lr, [SP, #-0x10]!
    //     0x862638: mov             fp, SP
    // 0x86263c: AllocStack(0x8)
    //     0x86263c: sub             SP, SP, #8
    // 0x862640: CheckStackOverflow
    //     0x862640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862644: cmp             SP, x16
    //     0x862648: b.ls            #0x8626cc
    // 0x86264c: ldr             x0, [fp, #0x10]
    // 0x862650: r2 = Null
    //     0x862650: mov             x2, NULL
    // 0x862654: r1 = Null
    //     0x862654: mov             x1, NULL
    // 0x862658: r4 = 60
    //     0x862658: movz            x4, #0x3c
    // 0x86265c: branchIfSmi(r0, 0x862668)
    //     0x86265c: tbz             w0, #0, #0x862668
    // 0x862660: r4 = LoadClassIdInstr(r0)
    //     0x862660: ldur            x4, [x0, #-1]
    //     0x862664: ubfx            x4, x4, #0xc, #0x14
    // 0x862668: sub             x4, x4, #0x5e
    // 0x86266c: cmp             x4, #1
    // 0x862670: b.ls            #0x862684
    // 0x862674: r8 = String
    //     0x862674: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x862678: r3 = Null
    //     0x862678: add             x3, PP, #0x26, lsl #12  ; [pp+0x26280] Null
    //     0x86267c: ldr             x3, [x3, #0x280]
    // 0x862680: r0 = String()
    //     0x862680: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x862684: ldr             x0, [fp, #0x18]
    // 0x862688: LoadField: r2 = r0->field_7
    //     0x862688: ldur            w2, [x0, #7]
    // 0x86268c: DecompressPointer r2
    //     0x86268c: add             x2, x2, HEAP, lsl #32
    // 0x862690: ldr             x1, [fp, #0x10]
    // 0x862694: stur            x2, [fp, #-8]
    // 0x862698: r0 = trim()
    //     0x862698: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x86269c: ldur            x1, [fp, #-8]
    // 0x8626a0: r2 = LoadClassIdInstr(r1)
    //     0x8626a0: ldur            x2, [x1, #-1]
    //     0x8626a4: ubfx            x2, x2, #0xc, #0x14
    // 0x8626a8: mov             x16, x0
    // 0x8626ac: mov             x0, x2
    // 0x8626b0: mov             x2, x16
    // 0x8626b4: r0 = GDT[cid_x0 + -0xfe]()
    //     0x8626b4: sub             lr, x0, #0xfe
    //     0x8626b8: ldr             lr, [x21, lr, lsl #3]
    //     0x8626bc: blr             lr
    // 0x8626c0: LeaveFrame
    //     0x8626c0: mov             SP, fp
    //     0x8626c4: ldp             fp, lr, [SP], #0x10
    // 0x8626c8: ret
    //     0x8626c8: ret             
    // 0x8626cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8626cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8626d0: b               #0x86264c
  }
  [closure] MapEntry<String, List<String>> <anonymous closure>(dynamic, String, List<String>) {
    // ** addr: 0x862b4c, size: 0x54
    // 0x862b4c: EnterFrame
    //     0x862b4c: stp             fp, lr, [SP, #-0x10]!
    //     0x862b50: mov             fp, SP
    // 0x862b54: AllocStack(0x8)
    //     0x862b54: sub             SP, SP, #8
    // 0x862b58: CheckStackOverflow
    //     0x862b58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x862b5c: cmp             SP, x16
    //     0x862b60: b.ls            #0x862b98
    // 0x862b64: ldr             x1, [fp, #0x18]
    // 0x862b68: r0 = trim()
    //     0x862b68: bl              #0x63acb8  ; [dart:core] _StringBase::trim
    // 0x862b6c: r1 = <String, List<String>>
    //     0x862b6c: add             x1, PP, #8, lsl #12  ; [pp+0x8b28] TypeArguments: <String, List<String>>
    //     0x862b70: ldr             x1, [x1, #0xb28]
    // 0x862b74: stur            x0, [fp, #-8]
    // 0x862b78: r0 = MapEntry()
    //     0x862b78: bl              #0x68c07c  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x862b7c: ldur            x1, [fp, #-8]
    // 0x862b80: StoreField: r0->field_b = r1
    //     0x862b80: stur            w1, [x0, #0xb]
    // 0x862b84: ldr             x1, [fp, #0x10]
    // 0x862b88: StoreField: r0->field_f = r1
    //     0x862b88: stur            w1, [x0, #0xf]
    // 0x862b8c: LeaveFrame
    //     0x862b8c: mov             SP, fp
    //     0x862b90: ldp             fp, lr, [SP], #0x10
    // 0x862b94: ret
    //     0x862b94: ret             
    // 0x862b98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x862b98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x862b9c: b               #0x862b64
  }
  _ toString(/* No info */) {
    // ** addr: 0x15684dc, size: 0xa4
    // 0x15684dc: EnterFrame
    //     0x15684dc: stp             fp, lr, [SP, #-0x10]!
    //     0x15684e0: mov             fp, SP
    // 0x15684e4: AllocStack(0x18)
    //     0x15684e4: sub             SP, SP, #0x18
    // 0x15684e8: CheckStackOverflow
    //     0x15684e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15684ec: cmp             SP, x16
    //     0x15684f0: b.ls            #0x1568578
    // 0x15684f4: r0 = StringBuffer()
    //     0x15684f4: bl              #0x620890  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x15684f8: mov             x1, x0
    // 0x15684fc: stur            x0, [fp, #-8]
    // 0x1568500: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x1568500: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x1568504: r0 = StringBuffer()
    //     0x1568504: bl              #0x6200bc  ; [dart:core] StringBuffer::StringBuffer
    // 0x1568508: r1 = 1
    //     0x1568508: movz            x1, #0x1
    // 0x156850c: r0 = AllocateContext()
    //     0x156850c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1568510: mov             x1, x0
    // 0x1568514: ldur            x0, [fp, #-8]
    // 0x1568518: StoreField: r1->field_f = r0
    //     0x1568518: stur            w0, [x1, #0xf]
    // 0x156851c: ldr             x2, [fp, #0x10]
    // 0x1568520: LoadField: r3 = r2->field_7
    //     0x1568520: ldur            w3, [x2, #7]
    // 0x1568524: DecompressPointer r3
    //     0x1568524: add             x3, x3, HEAP, lsl #32
    // 0x1568528: mov             x2, x1
    // 0x156852c: stur            x3, [fp, #-0x10]
    // 0x1568530: r1 = Function '<anonymous closure>':.
    //     0x1568530: add             x1, PP, #0x26, lsl #12  ; [pp+0x26250] AnonymousClosure: (0x1568580), in [package:dio/src/headers.dart] Headers::toString (0x15684dc)
    //     0x1568534: ldr             x1, [x1, #0x250]
    // 0x1568538: r0 = AllocateClosure()
    //     0x1568538: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x156853c: ldur            x1, [fp, #-0x10]
    // 0x1568540: r2 = LoadClassIdInstr(r1)
    //     0x1568540: ldur            x2, [x1, #-1]
    //     0x1568544: ubfx            x2, x2, #0xc, #0x14
    // 0x1568548: mov             x16, x0
    // 0x156854c: mov             x0, x2
    // 0x1568550: mov             x2, x16
    // 0x1568554: r0 = GDT[cid_x0 + 0x6d3]()
    //     0x1568554: add             lr, x0, #0x6d3
    //     0x1568558: ldr             lr, [x21, lr, lsl #3]
    //     0x156855c: blr             lr
    // 0x1568560: ldur            x16, [fp, #-8]
    // 0x1568564: str             x16, [SP]
    // 0x1568568: r0 = toString()
    //     0x1568568: bl              #0x1558aa8  ; [dart:core] StringBuffer::toString
    // 0x156856c: LeaveFrame
    //     0x156856c: mov             SP, fp
    //     0x1568570: ldp             fp, lr, [SP], #0x10
    // 0x1568574: ret
    //     0x1568574: ret             
    // 0x1568578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1568578: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x156857c: b               #0x15684f4
  }
  [closure] void <anonymous closure>(dynamic, String, List<String>) {
    // ** addr: 0x1568580, size: 0x120
    // 0x1568580: EnterFrame
    //     0x1568580: stp             fp, lr, [SP, #-0x10]!
    //     0x1568584: mov             fp, SP
    // 0x1568588: AllocStack(0x20)
    //     0x1568588: sub             SP, SP, #0x20
    // 0x156858c: SetupParameters()
    //     0x156858c: ldr             x0, [fp, #0x20]
    //     0x1568590: ldur            w2, [x0, #0x17]
    //     0x1568594: add             x2, x2, HEAP, lsl #32
    //     0x1568598: stur            x2, [fp, #-8]
    // 0x156859c: CheckStackOverflow
    //     0x156859c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15685a0: cmp             SP, x16
    //     0x15685a4: b.ls            #0x1568690
    // 0x15685a8: ldr             x1, [fp, #0x10]
    // 0x15685ac: r0 = LoadClassIdInstr(r1)
    //     0x15685ac: ldur            x0, [x1, #-1]
    //     0x15685b0: ubfx            x0, x0, #0xc, #0x14
    // 0x15685b4: r0 = GDT[cid_x0 + 0xc907]()
    //     0x15685b4: movz            x17, #0xc907
    //     0x15685b8: add             lr, x0, x17
    //     0x15685bc: ldr             lr, [x21, lr, lsl #3]
    //     0x15685c0: blr             lr
    // 0x15685c4: mov             x2, x0
    // 0x15685c8: ldur            x0, [fp, #-8]
    // 0x15685cc: stur            x2, [fp, #-0x18]
    // 0x15685d0: LoadField: r3 = r0->field_f
    //     0x15685d0: ldur            w3, [x0, #0xf]
    // 0x15685d4: DecompressPointer r3
    //     0x15685d4: add             x3, x3, HEAP, lsl #32
    // 0x15685d8: stur            x3, [fp, #-0x10]
    // 0x15685dc: ldr             x4, [fp, #0x18]
    // 0x15685e0: CheckStackOverflow
    //     0x15685e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15685e4: cmp             SP, x16
    //     0x15685e8: b.ls            #0x1568698
    // 0x15685ec: r0 = LoadClassIdInstr(r2)
    //     0x15685ec: ldur            x0, [x2, #-1]
    //     0x15685f0: ubfx            x0, x0, #0xc, #0x14
    // 0x15685f4: mov             x1, x2
    // 0x15685f8: r0 = GDT[cid_x0 + 0x5ea]()
    //     0x15685f8: add             lr, x0, #0x5ea
    //     0x15685fc: ldr             lr, [x21, lr, lsl #3]
    //     0x1568600: blr             lr
    // 0x1568604: tbnz            w0, #4, #0x1568680
    // 0x1568608: ldr             x3, [fp, #0x18]
    // 0x156860c: ldur            x2, [fp, #-0x18]
    // 0x1568610: r0 = LoadClassIdInstr(r2)
    //     0x1568610: ldur            x0, [x2, #-1]
    //     0x1568614: ubfx            x0, x0, #0xc, #0x14
    // 0x1568618: mov             x1, x2
    // 0x156861c: r0 = GDT[cid_x0 + 0x655]()
    //     0x156861c: add             lr, x0, #0x655
    //     0x1568620: ldr             lr, [x21, lr, lsl #3]
    //     0x1568624: blr             lr
    // 0x1568628: r1 = Null
    //     0x1568628: mov             x1, NULL
    // 0x156862c: r2 = 6
    //     0x156862c: movz            x2, #0x6
    // 0x1568630: stur            x0, [fp, #-8]
    // 0x1568634: r0 = AllocateArray()
    //     0x1568634: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1568638: mov             x1, x0
    // 0x156863c: ldr             x0, [fp, #0x18]
    // 0x1568640: StoreField: r1->field_f = r0
    //     0x1568640: stur            w0, [x1, #0xf]
    // 0x1568644: r16 = ": "
    //     0x1568644: ldr             x16, [PP, #0xc20]  ; [pp+0xc20] ": "
    // 0x1568648: StoreField: r1->field_13 = r16
    //     0x1568648: stur            w16, [x1, #0x13]
    // 0x156864c: ldur            x2, [fp, #-8]
    // 0x1568650: ArrayStore: r1[0] = r2  ; List_4
    //     0x1568650: stur            w2, [x1, #0x17]
    // 0x1568654: str             x1, [SP]
    // 0x1568658: r0 = _interpolate()
    //     0x1568658: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x156865c: ldur            x1, [fp, #-0x10]
    // 0x1568660: mov             x2, x0
    // 0x1568664: r0 = write()
    //     0x1568664: bl              #0x165e2d8  ; [dart:core] StringBuffer::write
    // 0x1568668: ldur            x1, [fp, #-0x10]
    // 0x156866c: r2 = "\n"
    //     0x156866c: ldr             x2, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0x1568670: r0 = _writeString()
    //     0x1568670: bl              #0x62027c  ; [dart:core] StringBuffer::_writeString
    // 0x1568674: ldur            x2, [fp, #-0x18]
    // 0x1568678: ldur            x3, [fp, #-0x10]
    // 0x156867c: b               #0x15685dc
    // 0x1568680: r0 = Null
    //     0x1568680: mov             x0, NULL
    // 0x1568684: LeaveFrame
    //     0x1568684: mov             SP, fp
    //     0x1568688: ldp             fp, lr, [SP], #0x10
    // 0x156868c: ret
    //     0x156868c: ret             
    // 0x1568690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1568690: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1568694: b               #0x15685a8
    // 0x1568698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1568698: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x156869c: b               #0x15685ec
  }
}
