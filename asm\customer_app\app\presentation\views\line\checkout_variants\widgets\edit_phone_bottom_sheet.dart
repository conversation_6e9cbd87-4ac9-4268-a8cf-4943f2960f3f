// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_phone_bottom_sheet.dart

// class id: 1049494, size: 0x8
class :: {
}

// class id: 3270, size: 0x24, field offset: 0x14
class _EditPhoneBottomSheetState extends State<dynamic> {

  late TextEditingController _phoneController; // offset: 0x14

  _ initState(/* No info */) {
    // ** addr: 0x948348, size: 0xdc
    // 0x948348: EnterFrame
    //     0x948348: stp             fp, lr, [SP, #-0x10]!
    //     0x94834c: mov             fp, SP
    // 0x948350: AllocStack(0x20)
    //     0x948350: sub             SP, SP, #0x20
    // 0x948354: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r0, fp-0x10 */)
    //     0x948354: mov             x0, x1
    //     0x948358: stur            x1, [fp, #-0x10]
    // 0x94835c: CheckStackOverflow
    //     0x94835c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x948360: cmp             SP, x16
    //     0x948364: b.ls            #0x948414
    // 0x948368: LoadField: r1 = r0->field_b
    //     0x948368: ldur            w1, [x0, #0xb]
    // 0x94836c: DecompressPointer r1
    //     0x94836c: add             x1, x1, HEAP, lsl #32
    // 0x948370: cmp             w1, NULL
    // 0x948374: b.eq            #0x94841c
    // 0x948378: LoadField: r2 = r1->field_b
    //     0x948378: ldur            w2, [x1, #0xb]
    // 0x94837c: DecompressPointer r2
    //     0x94837c: add             x2, x2, HEAP, lsl #32
    // 0x948380: stur            x2, [fp, #-8]
    // 0x948384: r1 = <TextEditingValue>
    //     0x948384: ldr             x1, [PP, #0x6c80]  ; [pp+0x6c80] TypeArguments: <TextEditingValue>
    // 0x948388: r0 = TextEditingController()
    //     0x948388: bl              #0x905a14  ; AllocateTextEditingControllerStub -> TextEditingController (size=0x2c)
    // 0x94838c: stur            x0, [fp, #-0x18]
    // 0x948390: ldur            x16, [fp, #-8]
    // 0x948394: str             x16, [SP]
    // 0x948398: mov             x1, x0
    // 0x94839c: r4 = const [0, 0x2, 0x1, 0x1, text, 0x1, null]
    //     0x94839c: add             x4, PP, #0x33, lsl #12  ; [pp+0x33c40] List(7) [0, 0x2, 0x1, 0x1, "text", 0x1, Null]
    //     0x9483a0: ldr             x4, [x4, #0xc40]
    // 0x9483a4: r0 = TextEditingController()
    //     0x9483a4: bl              #0x905904  ; [package:flutter/src/widgets/editable_text.dart] TextEditingController::TextEditingController
    // 0x9483a8: ldur            x0, [fp, #-0x18]
    // 0x9483ac: ldur            x3, [fp, #-0x10]
    // 0x9483b0: StoreField: r3->field_13 = r0
    //     0x9483b0: stur            w0, [x3, #0x13]
    //     0x9483b4: ldurb           w16, [x3, #-1]
    //     0x9483b8: ldurb           w17, [x0, #-1]
    //     0x9483bc: and             x16, x17, x16, lsr #2
    //     0x9483c0: tst             x16, HEAP, lsr #32
    //     0x9483c4: b.eq            #0x9483cc
    //     0x9483c8: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x9483cc: LoadField: r0 = r3->field_b
    //     0x9483cc: ldur            w0, [x3, #0xb]
    // 0x9483d0: DecompressPointer r0
    //     0x9483d0: add             x0, x0, HEAP, lsl #32
    // 0x9483d4: cmp             w0, NULL
    // 0x9483d8: b.eq            #0x948420
    // 0x9483dc: LoadField: r2 = r0->field_b
    //     0x9483dc: ldur            w2, [x0, #0xb]
    // 0x9483e0: DecompressPointer r2
    //     0x9483e0: add             x2, x2, HEAP, lsl #32
    // 0x9483e4: LoadField: r0 = r2->field_7
    //     0x9483e4: ldur            w0, [x2, #7]
    // 0x9483e8: cbz             w0, #0x948404
    // 0x9483ec: r0 = true
    //     0x9483ec: add             x0, NULL, #0x20  ; true
    // 0x9483f0: StoreField: r3->field_1b = r0
    //     0x9483f0: stur            w0, [x3, #0x1b]
    // 0x9483f4: mov             x1, x3
    // 0x9483f8: r0 = _isValidPhoneNumber()
    //     0x9483f8: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0x9483fc: ldur            x1, [fp, #-0x10]
    // 0x948400: ArrayStore: r1[0] = r0  ; List_4
    //     0x948400: stur            w0, [x1, #0x17]
    // 0x948404: r0 = Null
    //     0x948404: mov             x0, NULL
    // 0x948408: LeaveFrame
    //     0x948408: mov             SP, fp
    //     0x94840c: ldp             fp, lr, [SP], #0x10
    // 0x948410: ret
    //     0x948410: ret             
    // 0x948414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x948414: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x948418: b               #0x948368
    // 0x94841c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x94841c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x948420: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x948420: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa1f004, size: 0x24
    // 0xa1f004: ldr             x1, [SP]
    // 0xa1f008: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa1f008: ldur            w2, [x1, #0x17]
    // 0xa1f00c: DecompressPointer r2
    //     0xa1f00c: add             x2, x2, HEAP, lsl #32
    // 0xa1f010: LoadField: r1 = r2->field_f
    //     0xa1f010: ldur            w1, [x2, #0xf]
    // 0xa1f014: DecompressPointer r1
    //     0xa1f014: add             x1, x1, HEAP, lsl #32
    // 0xa1f018: LoadField: r0 = r2->field_13
    //     0xa1f018: ldur            w0, [x2, #0x13]
    // 0xa1f01c: DecompressPointer r0
    //     0xa1f01c: add             x0, x0, HEAP, lsl #32
    // 0xa1f020: ArrayStore: r1[0] = r0  ; List_4
    //     0xa1f020: stur            w0, [x1, #0x17]
    // 0xa1f024: ret
    //     0xa1f024: ret             
  }
  _ _handlePhoneNumberChanged(/* No info */) {
    // ** addr: 0xa1f028, size: 0x98
    // 0xa1f028: EnterFrame
    //     0xa1f028: stp             fp, lr, [SP, #-0x10]!
    //     0xa1f02c: mov             fp, SP
    // 0xa1f030: AllocStack(0x18)
    //     0xa1f030: sub             SP, SP, #0x18
    // 0xa1f034: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa1f034: stur            x1, [fp, #-8]
    //     0xa1f038: stur            x2, [fp, #-0x10]
    // 0xa1f03c: CheckStackOverflow
    //     0xa1f03c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1f040: cmp             SP, x16
    //     0xa1f044: b.ls            #0xa1f0b8
    // 0xa1f048: r1 = 2
    //     0xa1f048: movz            x1, #0x2
    // 0xa1f04c: r0 = AllocateContext()
    //     0xa1f04c: bl              #0x16f6108  ; AllocateContextStub
    // 0xa1f050: mov             x3, x0
    // 0xa1f054: ldur            x0, [fp, #-8]
    // 0xa1f058: stur            x3, [fp, #-0x18]
    // 0xa1f05c: StoreField: r3->field_f = r0
    //     0xa1f05c: stur            w0, [x3, #0xf]
    // 0xa1f060: r1 = true
    //     0xa1f060: add             x1, NULL, #0x20  ; true
    // 0xa1f064: StoreField: r0->field_1b = r1
    //     0xa1f064: stur            w1, [x0, #0x1b]
    // 0xa1f068: mov             x1, x0
    // 0xa1f06c: ldur            x2, [fp, #-0x10]
    // 0xa1f070: r0 = _isValidPhoneNumber()
    //     0xa1f070: bl              #0x801170  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_address_widget.dart] CheckoutAddressWidgetState::_isValidPhoneNumber
    // 0xa1f074: ldur            x2, [fp, #-0x18]
    // 0xa1f078: StoreField: r2->field_13 = r0
    //     0xa1f078: stur            w0, [x2, #0x13]
    // 0xa1f07c: ldur            x3, [fp, #-8]
    // 0xa1f080: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xa1f080: ldur            w1, [x3, #0x17]
    // 0xa1f084: DecompressPointer r1
    //     0xa1f084: add             x1, x1, HEAP, lsl #32
    // 0xa1f088: cmp             w1, w0
    // 0xa1f08c: b.eq            #0xa1f0a8
    // 0xa1f090: r1 = Function '<anonymous closure>':.
    //     0xa1f090: add             x1, PP, #0x53, lsl #12  ; [pp+0x53eb0] AnonymousClosure: (0xa1f004), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_handlePhoneNumberChanged (0xa1f028)
    //     0xa1f094: ldr             x1, [x1, #0xeb0]
    // 0xa1f098: r0 = AllocateClosure()
    //     0xa1f098: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa1f09c: ldur            x1, [fp, #-8]
    // 0xa1f0a0: mov             x2, x0
    // 0xa1f0a4: r0 = setState()
    //     0xa1f0a4: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa1f0a8: r0 = Null
    //     0xa1f0a8: mov             x0, NULL
    // 0xa1f0ac: LeaveFrame
    //     0xa1f0ac: mov             SP, fp
    //     0xa1f0b0: ldp             fp, lr, [SP], #0x10
    // 0xa1f0b4: ret
    //     0xa1f0b4: ret             
    // 0xa1f0b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1f0b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1f0bc: b               #0xa1f048
  }
  [closure] void _handlePhoneNumberChanged(dynamic, String) {
    // ** addr: 0xa1f0c0, size: 0x3c
    // 0xa1f0c0: EnterFrame
    //     0xa1f0c0: stp             fp, lr, [SP, #-0x10]!
    //     0xa1f0c4: mov             fp, SP
    // 0xa1f0c8: ldr             x0, [fp, #0x18]
    // 0xa1f0cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa1f0cc: ldur            w1, [x0, #0x17]
    // 0xa1f0d0: DecompressPointer r1
    //     0xa1f0d0: add             x1, x1, HEAP, lsl #32
    // 0xa1f0d4: CheckStackOverflow
    //     0xa1f0d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa1f0d8: cmp             SP, x16
    //     0xa1f0dc: b.ls            #0xa1f0f4
    // 0xa1f0e0: ldr             x2, [fp, #0x10]
    // 0xa1f0e4: r0 = _handlePhoneNumberChanged()
    //     0xa1f0e4: bl              #0xa1f028  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_handlePhoneNumberChanged
    // 0xa1f0e8: LeaveFrame
    //     0xa1f0e8: mov             SP, fp
    //     0xa1f0ec: ldp             fp, lr, [SP], #0x10
    // 0xa1f0f0: ret
    //     0xa1f0f0: ret             
    // 0xa1f0f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa1f0f4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1f0f8: b               #0xa1f0e0
  }
  _ build(/* No info */) {
    // ** addr: 0xbbbb8c, size: 0x81c
    // 0xbbbb8c: EnterFrame
    //     0xbbbb8c: stp             fp, lr, [SP, #-0x10]!
    //     0xbbbb90: mov             fp, SP
    // 0xbbbb94: AllocStack(0xc0)
    //     0xbbbb94: sub             SP, SP, #0xc0
    // 0xbbbb98: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xbbbb98: mov             x0, x1
    //     0xbbbb9c: stur            x1, [fp, #-8]
    //     0xbbbba0: mov             x1, x2
    //     0xbbbba4: stur            x2, [fp, #-0x10]
    // 0xbbbba8: CheckStackOverflow
    //     0xbbbba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbbbac: cmp             SP, x16
    //     0xbbbbb0: b.ls            #0xbbc390
    // 0xbbbbb4: r1 = 2
    //     0xbbbbb4: movz            x1, #0x2
    // 0xbbbbb8: r0 = AllocateContext()
    //     0xbbbbb8: bl              #0x16f6108  ; AllocateContextStub
    // 0xbbbbbc: mov             x2, x0
    // 0xbbbbc0: ldur            x0, [fp, #-8]
    // 0xbbbbc4: stur            x2, [fp, #-0x18]
    // 0xbbbbc8: StoreField: r2->field_f = r0
    //     0xbbbbc8: stur            w0, [x2, #0xf]
    // 0xbbbbcc: ldur            x1, [fp, #-0x10]
    // 0xbbbbd0: StoreField: r2->field_13 = r1
    //     0xbbbbd0: stur            w1, [x2, #0x13]
    // 0xbbbbd4: r0 = of()
    //     0xbbbbd4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbbbd8: stur            x0, [fp, #-0x20]
    // 0xbbbbdc: LoadField: r2 = r0->field_87
    //     0xbbbbdc: ldur            w2, [x0, #0x87]
    // 0xbbbbe0: DecompressPointer r2
    //     0xbbbbe0: add             x2, x2, HEAP, lsl #32
    // 0xbbbbe4: ldur            x3, [fp, #-0x18]
    // 0xbbbbe8: stur            x2, [fp, #-0x10]
    // 0xbbbbec: LoadField: r1 = r3->field_13
    //     0xbbbbec: ldur            w1, [x3, #0x13]
    // 0xbbbbf0: DecompressPointer r1
    //     0xbbbbf0: add             x1, x1, HEAP, lsl #32
    // 0xbbbbf4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xbbbbf4: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xbbbbf8: r0 = _of()
    //     0xbbbbf8: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xbbbbfc: LoadField: r1 = r0->field_7
    //     0xbbbbfc: ldur            w1, [x0, #7]
    // 0xbbbc00: DecompressPointer r1
    //     0xbbbc00: add             x1, x1, HEAP, lsl #32
    // 0xbbbc04: LoadField: d0 = r1->field_f
    //     0xbbbc04: ldur            d0, [x1, #0xf]
    // 0xbbbc08: d1 = 0.280000
    //     0xbbbc08: add             x17, PP, #0x53, lsl #12  ; [pp+0x53e50] IMM: double(0.28) from 0x3fd1eb851eb851ec
    //     0xbbbc0c: ldr             d1, [x17, #0xe50]
    // 0xbbbc10: fmul            d2, d0, d1
    // 0xbbbc14: stur            d2, [fp, #-0x70]
    // 0xbbbc18: r0 = BoxConstraints()
    //     0xbbbc18: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0xbbbc1c: stur            x0, [fp, #-0x28]
    // 0xbbbc20: StoreField: r0->field_7 = rZR
    //     0xbbbc20: stur            xzr, [x0, #7]
    // 0xbbbc24: d0 = inf
    //     0xbbbc24: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0xbbbc28: StoreField: r0->field_f = d0
    //     0xbbbc28: stur            d0, [x0, #0xf]
    // 0xbbbc2c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xbbbc2c: stur            xzr, [x0, #0x17]
    // 0xbbbc30: ldur            d0, [fp, #-0x70]
    // 0xbbbc34: StoreField: r0->field_1f = d0
    //     0xbbbc34: stur            d0, [x0, #0x1f]
    // 0xbbbc38: ldur            x2, [fp, #-0x18]
    // 0xbbbc3c: LoadField: r1 = r2->field_13
    //     0xbbbc3c: ldur            w1, [x2, #0x13]
    // 0xbbbc40: DecompressPointer r1
    //     0xbbbc40: add             x1, x1, HEAP, lsl #32
    // 0xbbbc44: r0 = of()
    //     0xbbbc44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbbc48: LoadField: r1 = r0->field_87
    //     0xbbbc48: ldur            w1, [x0, #0x87]
    // 0xbbbc4c: DecompressPointer r1
    //     0xbbbc4c: add             x1, x1, HEAP, lsl #32
    // 0xbbbc50: LoadField: r0 = r1->field_7
    //     0xbbbc50: ldur            w0, [x1, #7]
    // 0xbbbc54: DecompressPointer r0
    //     0xbbbc54: add             x0, x0, HEAP, lsl #32
    // 0xbbbc58: r16 = Instance_Color
    //     0xbbbc58: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbbc5c: r30 = 14.000000
    //     0xbbbc5c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbbc60: ldr             lr, [lr, #0x1d8]
    // 0xbbbc64: stp             lr, x16, [SP]
    // 0xbbbc68: mov             x1, x0
    // 0xbbbc6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xbbbc6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xbbbc70: ldr             x4, [x4, #0x9b8]
    // 0xbbbc74: r0 = copyWith()
    //     0xbbbc74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbbc78: stur            x0, [fp, #-0x30]
    // 0xbbbc7c: r0 = Text()
    //     0xbbbc7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbbc80: mov             x1, x0
    // 0xbbbc84: r0 = "Edit Phone Number"
    //     0xbbbc84: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e58] "Edit Phone Number"
    //     0xbbbc88: ldr             x0, [x0, #0xe58]
    // 0xbbbc8c: stur            x1, [fp, #-0x38]
    // 0xbbbc90: StoreField: r1->field_b = r0
    //     0xbbbc90: stur            w0, [x1, #0xb]
    // 0xbbbc94: ldur            x0, [fp, #-0x30]
    // 0xbbbc98: StoreField: r1->field_13 = r0
    //     0xbbbc98: stur            w0, [x1, #0x13]
    // 0xbbbc9c: r0 = SvgPicture()
    //     0xbbbc9c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xbbbca0: mov             x1, x0
    // 0xbbbca4: r2 = "assets/images/x.svg"
    //     0xbbbca4: add             x2, PP, #0x48, lsl #12  ; [pp+0x485e8] "assets/images/x.svg"
    //     0xbbbca8: ldr             x2, [x2, #0x5e8]
    // 0xbbbcac: stur            x0, [fp, #-0x30]
    // 0xbbbcb0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xbbbcb0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xbbbcb4: r0 = SvgPicture.asset()
    //     0xbbbcb4: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xbbbcb8: r0 = InkWell()
    //     0xbbbcb8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xbbbcbc: mov             x3, x0
    // 0xbbbcc0: ldur            x0, [fp, #-0x30]
    // 0xbbbcc4: stur            x3, [fp, #-0x40]
    // 0xbbbcc8: StoreField: r3->field_b = r0
    //     0xbbbcc8: stur            w0, [x3, #0xb]
    // 0xbbbccc: ldur            x2, [fp, #-0x18]
    // 0xbbbcd0: r1 = Function '<anonymous closure>':.
    //     0xbbbcd0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53e60] AnonymousClosure: (0x997c68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_select_size_bottom_sheet.dart] _ProductSelectSizeBottomSheetState::build (0xc05170)
    //     0xbbbcd4: ldr             x1, [x1, #0xe60]
    // 0xbbbcd8: r0 = AllocateClosure()
    //     0xbbbcd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbbcdc: mov             x1, x0
    // 0xbbbce0: ldur            x0, [fp, #-0x40]
    // 0xbbbce4: StoreField: r0->field_f = r1
    //     0xbbbce4: stur            w1, [x0, #0xf]
    // 0xbbbce8: r3 = true
    //     0xbbbce8: add             x3, NULL, #0x20  ; true
    // 0xbbbcec: StoreField: r0->field_43 = r3
    //     0xbbbcec: stur            w3, [x0, #0x43]
    // 0xbbbcf0: r4 = Instance_BoxShape
    //     0xbbbcf0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbbcf4: ldr             x4, [x4, #0x80]
    // 0xbbbcf8: StoreField: r0->field_47 = r4
    //     0xbbbcf8: stur            w4, [x0, #0x47]
    // 0xbbbcfc: StoreField: r0->field_6f = r3
    //     0xbbbcfc: stur            w3, [x0, #0x6f]
    // 0xbbbd00: r5 = false
    //     0xbbbd00: add             x5, NULL, #0x30  ; false
    // 0xbbbd04: StoreField: r0->field_73 = r5
    //     0xbbbd04: stur            w5, [x0, #0x73]
    // 0xbbbd08: StoreField: r0->field_83 = r3
    //     0xbbbd08: stur            w3, [x0, #0x83]
    // 0xbbbd0c: StoreField: r0->field_7b = r5
    //     0xbbbd0c: stur            w5, [x0, #0x7b]
    // 0xbbbd10: r1 = Null
    //     0xbbbd10: mov             x1, NULL
    // 0xbbbd14: r2 = 4
    //     0xbbbd14: movz            x2, #0x4
    // 0xbbbd18: r0 = AllocateArray()
    //     0xbbbd18: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbbd1c: mov             x2, x0
    // 0xbbbd20: ldur            x0, [fp, #-0x38]
    // 0xbbbd24: stur            x2, [fp, #-0x30]
    // 0xbbbd28: StoreField: r2->field_f = r0
    //     0xbbbd28: stur            w0, [x2, #0xf]
    // 0xbbbd2c: ldur            x0, [fp, #-0x40]
    // 0xbbbd30: StoreField: r2->field_13 = r0
    //     0xbbbd30: stur            w0, [x2, #0x13]
    // 0xbbbd34: r1 = <Widget>
    //     0xbbbd34: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbbd38: r0 = AllocateGrowableArray()
    //     0xbbbd38: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbbd3c: mov             x1, x0
    // 0xbbbd40: ldur            x0, [fp, #-0x30]
    // 0xbbbd44: stur            x1, [fp, #-0x38]
    // 0xbbbd48: StoreField: r1->field_f = r0
    //     0xbbbd48: stur            w0, [x1, #0xf]
    // 0xbbbd4c: r2 = 4
    //     0xbbbd4c: movz            x2, #0x4
    // 0xbbbd50: StoreField: r1->field_b = r2
    //     0xbbbd50: stur            w2, [x1, #0xb]
    // 0xbbbd54: r0 = Row()
    //     0xbbbd54: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xbbbd58: mov             x1, x0
    // 0xbbbd5c: r0 = Instance_Axis
    //     0xbbbd5c: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbbbd60: stur            x1, [fp, #-0x30]
    // 0xbbbd64: StoreField: r1->field_f = r0
    //     0xbbbd64: stur            w0, [x1, #0xf]
    // 0xbbbd68: r0 = Instance_MainAxisAlignment
    //     0xbbbd68: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xbbbd6c: ldr             x0, [x0, #0xa8]
    // 0xbbbd70: StoreField: r1->field_13 = r0
    //     0xbbbd70: stur            w0, [x1, #0x13]
    // 0xbbbd74: r0 = Instance_MainAxisSize
    //     0xbbbd74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xbbbd78: ldr             x0, [x0, #0xa10]
    // 0xbbbd7c: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbbd7c: stur            w0, [x1, #0x17]
    // 0xbbbd80: r0 = Instance_CrossAxisAlignment
    //     0xbbbd80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbbd84: ldr             x0, [x0, #0xa18]
    // 0xbbbd88: StoreField: r1->field_1b = r0
    //     0xbbbd88: stur            w0, [x1, #0x1b]
    // 0xbbbd8c: r2 = Instance_VerticalDirection
    //     0xbbbd8c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbbd90: ldr             x2, [x2, #0xa20]
    // 0xbbbd94: StoreField: r1->field_23 = r2
    //     0xbbbd94: stur            w2, [x1, #0x23]
    // 0xbbbd98: r3 = Instance_Clip
    //     0xbbbd98: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbbd9c: ldr             x3, [x3, #0x38]
    // 0xbbbda0: StoreField: r1->field_2b = r3
    //     0xbbbda0: stur            w3, [x1, #0x2b]
    // 0xbbbda4: StoreField: r1->field_2f = rZR
    //     0xbbbda4: stur            xzr, [x1, #0x2f]
    // 0xbbbda8: ldur            x4, [fp, #-0x38]
    // 0xbbbdac: StoreField: r1->field_b = r4
    //     0xbbbdac: stur            w4, [x1, #0xb]
    // 0xbbbdb0: r0 = Padding()
    //     0xbbbdb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbbdb4: mov             x1, x0
    // 0xbbbdb8: r0 = Instance_EdgeInsets
    //     0xbbbdb8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e68] Obj!EdgeInsets@d580a1
    //     0xbbbdbc: ldr             x0, [x0, #0xe68]
    // 0xbbbdc0: stur            x1, [fp, #-0x40]
    // 0xbbbdc4: StoreField: r1->field_f = r0
    //     0xbbbdc4: stur            w0, [x1, #0xf]
    // 0xbbbdc8: ldur            x0, [fp, #-0x30]
    // 0xbbbdcc: StoreField: r1->field_b = r0
    //     0xbbbdcc: stur            w0, [x1, #0xb]
    // 0xbbbdd0: ldur            x0, [fp, #-8]
    // 0xbbbdd4: LoadField: r2 = r0->field_1f
    //     0xbbbdd4: ldur            w2, [x0, #0x1f]
    // 0xbbbdd8: DecompressPointer r2
    //     0xbbbdd8: add             x2, x2, HEAP, lsl #32
    // 0xbbbddc: stur            x2, [fp, #-0x38]
    // 0xbbbde0: LoadField: r3 = r0->field_b
    //     0xbbbde0: ldur            w3, [x0, #0xb]
    // 0xbbbde4: DecompressPointer r3
    //     0xbbbde4: add             x3, x3, HEAP, lsl #32
    // 0xbbbde8: cmp             w3, NULL
    // 0xbbbdec: b.eq            #0xbbc398
    // 0xbbbdf0: LoadField: r4 = r3->field_b
    //     0xbbbdf0: ldur            w4, [x3, #0xb]
    // 0xbbbdf4: DecompressPointer r4
    //     0xbbbdf4: add             x4, x4, HEAP, lsl #32
    // 0xbbbdf8: LoadField: r3 = r4->field_7
    //     0xbbbdf8: ldur            w3, [x4, #7]
    // 0xbbbdfc: cbnz            w3, #0xbbbe08
    // 0xbbbe00: r4 = false
    //     0xbbbe00: add             x4, NULL, #0x30  ; false
    // 0xbbbe04: b               #0xbbbe0c
    // 0xbbbe08: r4 = true
    //     0xbbbe08: add             x4, NULL, #0x20  ; true
    // 0xbbbe0c: stur            x4, [fp, #-0x30]
    // 0xbbbe10: r0 = InitLateStaticField(0xa98) // [package:flutter/src/services/text_formatter.dart] FilteringTextInputFormatter::digitsOnly
    //     0xbbbe10: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbbbe14: ldr             x0, [x0, #0x1530]
    //     0xbbbe18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbbbe1c: cmp             w0, w16
    //     0xbbbe20: b.ne            #0xbbbe30
    //     0xbbbe24: add             x2, PP, #0x37, lsl #12  ; [pp+0x37120] Field <FilteringTextInputFormatter.digitsOnly>: static late final (offset: 0xa98)
    //     0xbbbe28: ldr             x2, [x2, #0x120]
    //     0xbbbe2c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbbbe30: stur            x0, [fp, #-0x48]
    // 0xbbbe34: r0 = LengthLimitingTextInputFormatter()
    //     0xbbbe34: bl              #0x997684  ; AllocateLengthLimitingTextInputFormatterStub -> LengthLimitingTextInputFormatter (size=0x10)
    // 0xbbbe38: mov             x3, x0
    // 0xbbbe3c: r0 = 20
    //     0xbbbe3c: movz            x0, #0x14
    // 0xbbbe40: stur            x3, [fp, #-0x50]
    // 0xbbbe44: StoreField: r3->field_7 = r0
    //     0xbbbe44: stur            w0, [x3, #7]
    // 0xbbbe48: r1 = Null
    //     0xbbbe48: mov             x1, NULL
    // 0xbbbe4c: r2 = 4
    //     0xbbbe4c: movz            x2, #0x4
    // 0xbbbe50: r0 = AllocateArray()
    //     0xbbbe50: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbbe54: mov             x2, x0
    // 0xbbbe58: ldur            x0, [fp, #-0x48]
    // 0xbbbe5c: stur            x2, [fp, #-0x58]
    // 0xbbbe60: StoreField: r2->field_f = r0
    //     0xbbbe60: stur            w0, [x2, #0xf]
    // 0xbbbe64: ldur            x0, [fp, #-0x50]
    // 0xbbbe68: StoreField: r2->field_13 = r0
    //     0xbbbe68: stur            w0, [x2, #0x13]
    // 0xbbbe6c: r1 = <TextInputFormatter>
    //     0xbbbe6c: add             x1, PP, #0x33, lsl #12  ; [pp+0x337b0] TypeArguments: <TextInputFormatter>
    //     0xbbbe70: ldr             x1, [x1, #0x7b0]
    // 0xbbbe74: r0 = AllocateGrowableArray()
    //     0xbbbe74: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbbe78: mov             x2, x0
    // 0xbbbe7c: ldur            x0, [fp, #-0x58]
    // 0xbbbe80: stur            x2, [fp, #-0x48]
    // 0xbbbe84: StoreField: r2->field_f = r0
    //     0xbbbe84: stur            w0, [x2, #0xf]
    // 0xbbbe88: r0 = 4
    //     0xbbbe88: movz            x0, #0x4
    // 0xbbbe8c: StoreField: r2->field_b = r0
    //     0xbbbe8c: stur            w0, [x2, #0xb]
    // 0xbbbe90: ldur            x0, [fp, #-0x10]
    // 0xbbbe94: LoadField: r1 = r0->field_2b
    //     0xbbbe94: ldur            w1, [x0, #0x2b]
    // 0xbbbe98: DecompressPointer r1
    //     0xbbbe98: add             x1, x1, HEAP, lsl #32
    // 0xbbbe9c: ldur            x0, [fp, #-0x20]
    // 0xbbbea0: LoadField: r3 = r0->field_5b
    //     0xbbbea0: ldur            w3, [x0, #0x5b]
    // 0xbbbea4: DecompressPointer r3
    //     0xbbbea4: add             x3, x3, HEAP, lsl #32
    // 0xbbbea8: r16 = 14.000000
    //     0xbbbea8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbbeac: ldr             x16, [x16, #0x1d8]
    // 0xbbbeb0: stp             x3, x16, [SP]
    // 0xbbbeb4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbbeb4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbbeb8: ldr             x4, [x4, #0xaa0]
    // 0xbbbebc: r0 = copyWith()
    //     0xbbbebc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbbec0: mov             x3, x0
    // 0xbbbec4: ldur            x0, [fp, #-8]
    // 0xbbbec8: stur            x3, [fp, #-0x20]
    // 0xbbbecc: LoadField: r4 = r0->field_13
    //     0xbbbecc: ldur            w4, [x0, #0x13]
    // 0xbbbed0: DecompressPointer r4
    //     0xbbbed0: add             x4, x4, HEAP, lsl #32
    // 0xbbbed4: r16 = Sentinel
    //     0xbbbed4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbbed8: cmp             w4, w16
    // 0xbbbedc: b.eq            #0xbbc39c
    // 0xbbbee0: ldur            x5, [fp, #-0x18]
    // 0xbbbee4: stur            x4, [fp, #-0x10]
    // 0xbbbee8: LoadField: r2 = r5->field_13
    //     0xbbbee8: ldur            w2, [x5, #0x13]
    // 0xbbbeec: DecompressPointer r2
    //     0xbbbeec: add             x2, x2, HEAP, lsl #32
    // 0xbbbef0: mov             x1, x0
    // 0xbbbef4: r0 = _buildInputDecoration()
    //     0xbbbef4: bl              #0xbbc3a8  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_buildInputDecoration
    // 0xbbbef8: ldur            x2, [fp, #-8]
    // 0xbbbefc: r1 = Function '_validatePhoneNumber@1675067928':.
    //     0xbbbefc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53e70] AnonymousClosure: (0xbbc6ac), in [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber (0xa08f3c)
    //     0xbbbf00: ldr             x1, [x1, #0xe70]
    // 0xbbbf04: stur            x0, [fp, #-0x50]
    // 0xbbbf08: r0 = AllocateClosure()
    //     0xbbbf08: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbbf0c: ldur            x2, [fp, #-8]
    // 0xbbbf10: r1 = Function '_handlePhoneNumberChanged@1675067928':.
    //     0xbbbf10: add             x1, PP, #0x53, lsl #12  ; [pp+0x53e78] AnonymousClosure: (0xa1f0c0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::_handlePhoneNumberChanged (0xa1f028)
    //     0xbbbf14: ldr             x1, [x1, #0xe78]
    // 0xbbbf18: stur            x0, [fp, #-0x58]
    // 0xbbbf1c: r0 = AllocateClosure()
    //     0xbbbf1c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbbf20: r1 = <String>
    //     0xbbbf20: ldr             x1, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbbbf24: stur            x0, [fp, #-0x60]
    // 0xbbbf28: r0 = TextFormField()
    //     0xbbbf28: bl              #0x997678  ; AllocateTextFormFieldStub -> TextFormField (size=0x30)
    // 0xbbbf2c: stur            x0, [fp, #-0x68]
    // 0xbbbf30: ldur            x16, [fp, #-0x58]
    // 0xbbbf34: r30 = true
    //     0xbbbf34: add             lr, NULL, #0x20  ; true
    // 0xbbbf38: stp             lr, x16, [SP, #0x40]
    // 0xbbbf3c: ldur            x16, [fp, #-0x30]
    // 0xbbbf40: r30 = Instance_AutovalidateMode
    //     0xbbbf40: add             lr, PP, #0x33, lsl #12  ; [pp+0x337e8] Obj!AutovalidateMode@d721e1
    //     0xbbbf44: ldr             lr, [lr, #0x7e8]
    // 0xbbbf48: stp             lr, x16, [SP, #0x30]
    // 0xbbbf4c: ldur            x16, [fp, #-0x48]
    // 0xbbbf50: r30 = Instance_TextInputType
    //     0xbbbf50: add             lr, PP, #0x37, lsl #12  ; [pp+0x371a0] Obj!TextInputType@d55b81
    //     0xbbbf54: ldr             lr, [lr, #0x1a0]
    // 0xbbbf58: stp             lr, x16, [SP, #0x20]
    // 0xbbbf5c: ldur            x16, [fp, #-0x20]
    // 0xbbbf60: r30 = 2
    //     0xbbbf60: movz            lr, #0x2
    // 0xbbbf64: stp             lr, x16, [SP, #0x10]
    // 0xbbbf68: ldur            x16, [fp, #-0x10]
    // 0xbbbf6c: ldur            lr, [fp, #-0x60]
    // 0xbbbf70: stp             lr, x16, [SP]
    // 0xbbbf74: mov             x1, x0
    // 0xbbbf78: ldur            x2, [fp, #-0x50]
    // 0xbbbf7c: r4 = const [0, 0xc, 0xa, 0x2, autofocus, 0x4, autovalidateMode, 0x5, controller, 0xa, enableSuggestions, 0x3, inputFormatters, 0x6, keyboardType, 0x7, maxLines, 0x9, onChanged, 0xb, style, 0x8, validator, 0x2, null]
    //     0xbbbf7c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53e80] List(25) [0, 0xc, 0xa, 0x2, "autofocus", 0x4, "autovalidateMode", 0x5, "controller", 0xa, "enableSuggestions", 0x3, "inputFormatters", 0x6, "keyboardType", 0x7, "maxLines", 0x9, "onChanged", 0xb, "style", 0x8, "validator", 0x2, Null]
    //     0xbbbf80: ldr             x4, [x4, #0xe80]
    // 0xbbbf84: r0 = TextFormField()
    //     0xbbbf84: bl              #0x9937b0  ; [package:flutter/src/material/text_form_field.dart] TextFormField::TextFormField
    // 0xbbbf88: r0 = Form()
    //     0xbbbf88: bl              #0x9937a4  ; AllocateFormStub -> Form (size=0x28)
    // 0xbbbf8c: mov             x1, x0
    // 0xbbbf90: ldur            x0, [fp, #-0x68]
    // 0xbbbf94: stur            x1, [fp, #-0x10]
    // 0xbbbf98: StoreField: r1->field_b = r0
    //     0xbbbf98: stur            w0, [x1, #0xb]
    // 0xbbbf9c: r0 = Instance_AutovalidateMode
    //     0xbbbf9c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33800] Obj!AutovalidateMode@d721c1
    //     0xbbbfa0: ldr             x0, [x0, #0x800]
    // 0xbbbfa4: StoreField: r1->field_23 = r0
    //     0xbbbfa4: stur            w0, [x1, #0x23]
    // 0xbbbfa8: ldur            x0, [fp, #-0x38]
    // 0xbbbfac: StoreField: r1->field_7 = r0
    //     0xbbbfac: stur            w0, [x1, #7]
    // 0xbbbfb0: r0 = Padding()
    //     0xbbbfb0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xbbbfb4: mov             x2, x0
    // 0xbbbfb8: r0 = Instance_EdgeInsets
    //     0xbbbfb8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e88] Obj!EdgeInsets@d57231
    //     0xbbbfbc: ldr             x0, [x0, #0xe88]
    // 0xbbbfc0: stur            x2, [fp, #-0x20]
    // 0xbbbfc4: StoreField: r2->field_f = r0
    //     0xbbbfc4: stur            w0, [x2, #0xf]
    // 0xbbbfc8: ldur            x0, [fp, #-0x10]
    // 0xbbbfcc: StoreField: r2->field_b = r0
    //     0xbbbfcc: stur            w0, [x2, #0xb]
    // 0xbbbfd0: r1 = <FlexParentData>
    //     0xbbbfd0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xbbbfd4: ldr             x1, [x1, #0xe00]
    // 0xbbbfd8: r0 = Expanded()
    //     0xbbbfd8: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xbbbfdc: mov             x2, x0
    // 0xbbbfe0: r0 = 1
    //     0xbbbfe0: movz            x0, #0x1
    // 0xbbbfe4: stur            x2, [fp, #-0x10]
    // 0xbbbfe8: StoreField: r2->field_13 = r0
    //     0xbbbfe8: stur            x0, [x2, #0x13]
    // 0xbbbfec: r0 = Instance_FlexFit
    //     0xbbbfec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xbbbff0: ldr             x0, [x0, #0xe08]
    // 0xbbbff4: StoreField: r2->field_1b = r0
    //     0xbbbff4: stur            w0, [x2, #0x1b]
    // 0xbbbff8: ldur            x0, [fp, #-0x20]
    // 0xbbbffc: StoreField: r2->field_b = r0
    //     0xbbbffc: stur            w0, [x2, #0xb]
    // 0xbbc000: ldur            x0, [fp, #-0x18]
    // 0xbbc004: LoadField: r1 = r0->field_13
    //     0xbbc004: ldur            w1, [x0, #0x13]
    // 0xbbc008: DecompressPointer r1
    //     0xbbc008: add             x1, x1, HEAP, lsl #32
    // 0xbbc00c: r0 = of()
    //     0xbbc00c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbc010: LoadField: r1 = r0->field_5b
    //     0xbbc010: ldur            w1, [x0, #0x5b]
    // 0xbbc014: DecompressPointer r1
    //     0xbbc014: add             x1, x1, HEAP, lsl #32
    // 0xbbc018: r0 = LoadClassIdInstr(r1)
    //     0xbbc018: ldur            x0, [x1, #-1]
    //     0xbbc01c: ubfx            x0, x0, #0xc, #0x14
    // 0xbbc020: r2 = 40
    //     0xbbc020: movz            x2, #0x28
    // 0xbbc024: r0 = GDT[cid_x0 + -0xfe7]()
    //     0xbbc024: sub             lr, x0, #0xfe7
    //     0xbbc028: ldr             lr, [x21, lr, lsl #3]
    //     0xbbc02c: blr             lr
    // 0xbbc030: stur            x0, [fp, #-0x20]
    // 0xbbc034: r0 = BorderSide()
    //     0xbbc034: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbbc038: mov             x1, x0
    // 0xbbc03c: ldur            x0, [fp, #-0x20]
    // 0xbbc040: stur            x1, [fp, #-0x30]
    // 0xbbc044: StoreField: r1->field_7 = r0
    //     0xbbc044: stur            w0, [x1, #7]
    // 0xbbc048: d0 = 2.000000
    //     0xbbc048: fmov            d0, #2.00000000
    // 0xbbc04c: StoreField: r1->field_b = d0
    //     0xbbc04c: stur            d0, [x1, #0xb]
    // 0xbbc050: r0 = Instance_BorderStyle
    //     0xbbc050: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbbc054: ldr             x0, [x0, #0xf68]
    // 0xbbc058: StoreField: r1->field_13 = r0
    //     0xbbc058: stur            w0, [x1, #0x13]
    // 0xbbc05c: d0 = -1.000000
    //     0xbbc05c: fmov            d0, #-1.00000000
    // 0xbbc060: ArrayStore: r1[0] = d0  ; List_8
    //     0xbbc060: stur            d0, [x1, #0x17]
    // 0xbbc064: r0 = Border()
    //     0xbbc064: bl              #0x8374f8  ; AllocateBorderStub -> Border (size=0x18)
    // 0xbbc068: mov             x1, x0
    // 0xbbc06c: ldur            x0, [fp, #-0x30]
    // 0xbbc070: stur            x1, [fp, #-0x20]
    // 0xbbc074: StoreField: r1->field_7 = r0
    //     0xbbc074: stur            w0, [x1, #7]
    // 0xbbc078: r0 = Instance_BorderSide
    //     0xbbc078: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xbbc07c: ldr             x0, [x0, #0xe20]
    // 0xbbc080: StoreField: r1->field_b = r0
    //     0xbbc080: stur            w0, [x1, #0xb]
    // 0xbbc084: StoreField: r1->field_f = r0
    //     0xbbc084: stur            w0, [x1, #0xf]
    // 0xbbc088: StoreField: r1->field_13 = r0
    //     0xbbc088: stur            w0, [x1, #0x13]
    // 0xbbc08c: r0 = BoxDecoration()
    //     0xbbc08c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xbbc090: mov             x2, x0
    // 0xbbc094: ldur            x0, [fp, #-0x20]
    // 0xbbc098: stur            x2, [fp, #-0x30]
    // 0xbbc09c: StoreField: r2->field_f = r0
    //     0xbbc09c: stur            w0, [x2, #0xf]
    // 0xbbc0a0: r0 = Instance_BoxShape
    //     0xbbc0a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xbbc0a4: ldr             x0, [x0, #0x80]
    // 0xbbc0a8: StoreField: r2->field_23 = r0
    //     0xbbc0a8: stur            w0, [x2, #0x23]
    // 0xbbc0ac: ldur            x0, [fp, #-8]
    // 0xbbc0b0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbc0b0: ldur            w1, [x0, #0x17]
    // 0xbbc0b4: DecompressPointer r1
    //     0xbbc0b4: add             x1, x1, HEAP, lsl #32
    // 0xbbc0b8: tbnz            w1, #4, #0xbbc0d8
    // 0xbbc0bc: ldur            x3, [fp, #-0x18]
    // 0xbbc0c0: LoadField: r1 = r3->field_13
    //     0xbbc0c0: ldur            w1, [x3, #0x13]
    // 0xbbc0c4: DecompressPointer r1
    //     0xbbc0c4: add             x1, x1, HEAP, lsl #32
    // 0xbbc0c8: r0 = of()
    //     0xbbc0c8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbc0cc: LoadField: r1 = r0->field_5b
    //     0xbbc0cc: ldur            w1, [x0, #0x5b]
    // 0xbbc0d0: DecompressPointer r1
    //     0xbbc0d0: add             x1, x1, HEAP, lsl #32
    // 0xbbc0d4: b               #0xbbc0e0
    // 0xbbc0d8: r1 = Instance_MaterialColor
    //     0xbbc0d8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fdc0] Obj!MaterialColor@d6bd61
    //     0xbbc0dc: ldr             x1, [x1, #0xdc0]
    // 0xbbc0e0: ldur            x0, [fp, #-8]
    // 0xbbc0e4: r16 = <Color>
    //     0xbbc0e4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xbbc0e8: ldr             x16, [x16, #0xf80]
    // 0xbbc0ec: stp             x1, x16, [SP]
    // 0xbbc0f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbc0f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbc0f4: r0 = all()
    //     0xbbc0f4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbc0f8: stur            x0, [fp, #-0x20]
    // 0xbbc0fc: r16 = <RoundedRectangleBorder>
    //     0xbbc0fc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xbbc100: ldr             x16, [x16, #0xf78]
    // 0xbbc104: r30 = Instance_RoundedRectangleBorder
    //     0xbbc104: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2fd68] Obj!RoundedRectangleBorder@d5aba1
    //     0xbbc108: ldr             lr, [lr, #0xd68]
    // 0xbbc10c: stp             lr, x16, [SP]
    // 0xbbc110: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xbbc110: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xbbc114: r0 = all()
    //     0xbbc114: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xbbc118: stur            x0, [fp, #-0x38]
    // 0xbbc11c: r0 = ButtonStyle()
    //     0xbbc11c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xbbc120: mov             x1, x0
    // 0xbbc124: ldur            x0, [fp, #-0x20]
    // 0xbbc128: stur            x1, [fp, #-0x48]
    // 0xbbc12c: StoreField: r1->field_b = r0
    //     0xbbc12c: stur            w0, [x1, #0xb]
    // 0xbbc130: ldur            x0, [fp, #-0x38]
    // 0xbbc134: StoreField: r1->field_43 = r0
    //     0xbbc134: stur            w0, [x1, #0x43]
    // 0xbbc138: r0 = TextButtonThemeData()
    //     0xbbc138: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xbbc13c: mov             x3, x0
    // 0xbbc140: ldur            x0, [fp, #-0x48]
    // 0xbbc144: stur            x3, [fp, #-0x20]
    // 0xbbc148: StoreField: r3->field_7 = r0
    //     0xbbc148: stur            w0, [x3, #7]
    // 0xbbc14c: ldur            x0, [fp, #-8]
    // 0xbbc150: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbc150: ldur            w1, [x0, #0x17]
    // 0xbbc154: DecompressPointer r1
    //     0xbbc154: add             x1, x1, HEAP, lsl #32
    // 0xbbc158: tbnz            w1, #4, #0xbbc174
    // 0xbbc15c: ldur            x2, [fp, #-0x18]
    // 0xbbc160: r1 = Function '<anonymous closure>':.
    //     0xbbc160: add             x1, PP, #0x53, lsl #12  ; [pp+0x53e90] AnonymousClosure: (0xbbc5a0), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/edit_phone_bottom_sheet.dart] _EditPhoneBottomSheetState::build (0xbbbb8c)
    //     0xbbc164: ldr             x1, [x1, #0xe90]
    // 0xbbc168: r0 = AllocateClosure()
    //     0xbbc168: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbbc16c: mov             x4, x0
    // 0xbbc170: b               #0xbbc178
    // 0xbbc174: r4 = Null
    //     0xbbc174: mov             x4, NULL
    // 0xbbc178: ldur            x1, [fp, #-0x18]
    // 0xbbc17c: ldur            x3, [fp, #-0x40]
    // 0xbbc180: ldur            x2, [fp, #-0x10]
    // 0xbbc184: ldur            x0, [fp, #-0x20]
    // 0xbbc188: stur            x4, [fp, #-8]
    // 0xbbc18c: LoadField: r5 = r1->field_13
    //     0xbbc18c: ldur            w5, [x1, #0x13]
    // 0xbbc190: DecompressPointer r5
    //     0xbbc190: add             x5, x5, HEAP, lsl #32
    // 0xbbc194: mov             x1, x5
    // 0xbbc198: r0 = of()
    //     0xbbc198: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbc19c: LoadField: r1 = r0->field_87
    //     0xbbc19c: ldur            w1, [x0, #0x87]
    // 0xbbc1a0: DecompressPointer r1
    //     0xbbc1a0: add             x1, x1, HEAP, lsl #32
    // 0xbbc1a4: LoadField: r0 = r1->field_7
    //     0xbbc1a4: ldur            w0, [x1, #7]
    // 0xbbc1a8: DecompressPointer r0
    //     0xbbc1a8: add             x0, x0, HEAP, lsl #32
    // 0xbbc1ac: r16 = 14.000000
    //     0xbbc1ac: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbc1b0: ldr             x16, [x16, #0x1d8]
    // 0xbbc1b4: r30 = Instance_Color
    //     0xbbc1b4: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xbbc1b8: stp             lr, x16, [SP]
    // 0xbbc1bc: mov             x1, x0
    // 0xbbc1c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbc1c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbc1c4: ldr             x4, [x4, #0xaa0]
    // 0xbbc1c8: r0 = copyWith()
    //     0xbbc1c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbc1cc: stur            x0, [fp, #-0x18]
    // 0xbbc1d0: r0 = Text()
    //     0xbbc1d0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xbbc1d4: mov             x1, x0
    // 0xbbc1d8: r0 = "CONFIRM"
    //     0xbbc1d8: add             x0, PP, #0x53, lsl #12  ; [pp+0x53e98] "CONFIRM"
    //     0xbbc1dc: ldr             x0, [x0, #0xe98]
    // 0xbbc1e0: stur            x1, [fp, #-0x38]
    // 0xbbc1e4: StoreField: r1->field_b = r0
    //     0xbbc1e4: stur            w0, [x1, #0xb]
    // 0xbbc1e8: ldur            x0, [fp, #-0x18]
    // 0xbbc1ec: StoreField: r1->field_13 = r0
    //     0xbbc1ec: stur            w0, [x1, #0x13]
    // 0xbbc1f0: r0 = TextButton()
    //     0xbbc1f0: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xbbc1f4: mov             x1, x0
    // 0xbbc1f8: ldur            x0, [fp, #-8]
    // 0xbbc1fc: stur            x1, [fp, #-0x18]
    // 0xbbc200: StoreField: r1->field_b = r0
    //     0xbbc200: stur            w0, [x1, #0xb]
    // 0xbbc204: r0 = false
    //     0xbbc204: add             x0, NULL, #0x30  ; false
    // 0xbbc208: StoreField: r1->field_27 = r0
    //     0xbbc208: stur            w0, [x1, #0x27]
    // 0xbbc20c: r2 = true
    //     0xbbc20c: add             x2, NULL, #0x20  ; true
    // 0xbbc210: StoreField: r1->field_2f = r2
    //     0xbbc210: stur            w2, [x1, #0x2f]
    // 0xbbc214: ldur            x3, [fp, #-0x38]
    // 0xbbc218: StoreField: r1->field_37 = r3
    //     0xbbc218: stur            w3, [x1, #0x37]
    // 0xbbc21c: r0 = TextButtonTheme()
    //     0xbbc21c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xbbc220: mov             x1, x0
    // 0xbbc224: ldur            x0, [fp, #-0x20]
    // 0xbbc228: stur            x1, [fp, #-8]
    // 0xbbc22c: StoreField: r1->field_f = r0
    //     0xbbc22c: stur            w0, [x1, #0xf]
    // 0xbbc230: ldur            x0, [fp, #-0x18]
    // 0xbbc234: StoreField: r1->field_b = r0
    //     0xbbc234: stur            w0, [x1, #0xb]
    // 0xbbc238: r0 = Container()
    //     0xbbc238: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbc23c: stur            x0, [fp, #-0x18]
    // 0xbbc240: r16 = Instance_EdgeInsets
    //     0xbbc240: add             x16, PP, #0x47, lsl #12  ; [pp+0x47050] Obj!EdgeInsets@d57e61
    //     0xbbc244: ldr             x16, [x16, #0x50]
    // 0xbbc248: r30 = inf
    //     0xbbc248: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0xbbc24c: ldr             lr, [lr, #0x9f8]
    // 0xbbc250: stp             lr, x16, [SP, #0x10]
    // 0xbbc254: ldur            x16, [fp, #-0x30]
    // 0xbbc258: ldur            lr, [fp, #-8]
    // 0xbbc25c: stp             lr, x16, [SP]
    // 0xbbc260: mov             x1, x0
    // 0xbbc264: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, padding, 0x1, width, 0x2, null]
    //     0xbbc264: add             x4, PP, #0x38, lsl #12  ; [pp+0x38018] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "padding", 0x1, "width", 0x2, Null]
    //     0xbbc268: ldr             x4, [x4, #0x18]
    // 0xbbc26c: r0 = Container()
    //     0xbbc26c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbc270: r1 = Null
    //     0xbbc270: mov             x1, NULL
    // 0xbbc274: r2 = 8
    //     0xbbc274: movz            x2, #0x8
    // 0xbbc278: r0 = AllocateArray()
    //     0xbbc278: bl              #0x16f7198  ; AllocateArrayStub
    // 0xbbc27c: mov             x2, x0
    // 0xbbc280: ldur            x0, [fp, #-0x40]
    // 0xbbc284: stur            x2, [fp, #-8]
    // 0xbbc288: StoreField: r2->field_f = r0
    //     0xbbc288: stur            w0, [x2, #0xf]
    // 0xbbc28c: ldur            x0, [fp, #-0x10]
    // 0xbbc290: StoreField: r2->field_13 = r0
    //     0xbbc290: stur            w0, [x2, #0x13]
    // 0xbbc294: r16 = Instance_SizedBox
    //     0xbbc294: add             x16, PP, #0x34, lsl #12  ; [pp+0x34578] Obj!SizedBox@d67de1
    //     0xbbc298: ldr             x16, [x16, #0x578]
    // 0xbbc29c: ArrayStore: r2[0] = r16  ; List_4
    //     0xbbc29c: stur            w16, [x2, #0x17]
    // 0xbbc2a0: ldur            x0, [fp, #-0x18]
    // 0xbbc2a4: StoreField: r2->field_1b = r0
    //     0xbbc2a4: stur            w0, [x2, #0x1b]
    // 0xbbc2a8: r1 = <Widget>
    //     0xbbc2a8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xbbc2ac: r0 = AllocateGrowableArray()
    //     0xbbc2ac: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xbbc2b0: mov             x1, x0
    // 0xbbc2b4: ldur            x0, [fp, #-8]
    // 0xbbc2b8: stur            x1, [fp, #-0x10]
    // 0xbbc2bc: StoreField: r1->field_f = r0
    //     0xbbc2bc: stur            w0, [x1, #0xf]
    // 0xbbc2c0: r0 = 8
    //     0xbbc2c0: movz            x0, #0x8
    // 0xbbc2c4: StoreField: r1->field_b = r0
    //     0xbbc2c4: stur            w0, [x1, #0xb]
    // 0xbbc2c8: r0 = Column()
    //     0xbbc2c8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xbbc2cc: mov             x1, x0
    // 0xbbc2d0: r0 = Instance_Axis
    //     0xbbc2d0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xbbc2d4: stur            x1, [fp, #-8]
    // 0xbbc2d8: StoreField: r1->field_f = r0
    //     0xbbc2d8: stur            w0, [x1, #0xf]
    // 0xbbc2dc: r0 = Instance_MainAxisAlignment
    //     0xbbc2dc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xbbc2e0: ldr             x0, [x0, #0xa08]
    // 0xbbc2e4: StoreField: r1->field_13 = r0
    //     0xbbc2e4: stur            w0, [x1, #0x13]
    // 0xbbc2e8: r0 = Instance_MainAxisSize
    //     0xbbc2e8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xbbc2ec: ldr             x0, [x0, #0xdd0]
    // 0xbbc2f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xbbc2f0: stur            w0, [x1, #0x17]
    // 0xbbc2f4: r0 = Instance_CrossAxisAlignment
    //     0xbbc2f4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xbbc2f8: ldr             x0, [x0, #0xa18]
    // 0xbbc2fc: StoreField: r1->field_1b = r0
    //     0xbbc2fc: stur            w0, [x1, #0x1b]
    // 0xbbc300: r0 = Instance_VerticalDirection
    //     0xbbc300: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xbbc304: ldr             x0, [x0, #0xa20]
    // 0xbbc308: StoreField: r1->field_23 = r0
    //     0xbbc308: stur            w0, [x1, #0x23]
    // 0xbbc30c: r0 = Instance_Clip
    //     0xbbc30c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xbbc310: ldr             x0, [x0, #0x38]
    // 0xbbc314: StoreField: r1->field_2b = r0
    //     0xbbc314: stur            w0, [x1, #0x2b]
    // 0xbbc318: StoreField: r1->field_2f = rZR
    //     0xbbc318: stur            xzr, [x1, #0x2f]
    // 0xbbc31c: ldur            x0, [fp, #-0x10]
    // 0xbbc320: StoreField: r1->field_b = r0
    //     0xbbc320: stur            w0, [x1, #0xb]
    // 0xbbc324: r0 = Container()
    //     0xbbc324: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xbbc328: stur            x0, [fp, #-0x10]
    // 0xbbc32c: ldur            x16, [fp, #-0x28]
    // 0xbbc330: r30 = Instance_BoxDecoration
    //     0xbbc330: add             lr, PP, #0x48, lsl #12  ; [pp+0x485a8] Obj!BoxDecoration@d64801
    //     0xbbc334: ldr             lr, [lr, #0x5a8]
    // 0xbbc338: stp             lr, x16, [SP, #8]
    // 0xbbc33c: ldur            x16, [fp, #-8]
    // 0xbbc340: str             x16, [SP]
    // 0xbbc344: mov             x1, x0
    // 0xbbc348: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, constraints, 0x1, decoration, 0x2, null]
    //     0xbbc348: add             x4, PP, #0x53, lsl #12  ; [pp+0x53ea0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "constraints", 0x1, "decoration", 0x2, Null]
    //     0xbbc34c: ldr             x4, [x4, #0xea0]
    // 0xbbc350: r0 = Container()
    //     0xbbc350: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xbbc354: r0 = SafeArea()
    //     0xbbc354: bl              #0x900fbc  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xbbc358: r1 = true
    //     0xbbc358: add             x1, NULL, #0x20  ; true
    // 0xbbc35c: StoreField: r0->field_b = r1
    //     0xbbc35c: stur            w1, [x0, #0xb]
    // 0xbbc360: StoreField: r0->field_f = r1
    //     0xbbc360: stur            w1, [x0, #0xf]
    // 0xbbc364: StoreField: r0->field_13 = r1
    //     0xbbc364: stur            w1, [x0, #0x13]
    // 0xbbc368: ArrayStore: r0[0] = r1  ; List_4
    //     0xbbc368: stur            w1, [x0, #0x17]
    // 0xbbc36c: r1 = Instance_EdgeInsets
    //     0xbbc36c: ldr             x1, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xbbc370: StoreField: r0->field_1b = r1
    //     0xbbc370: stur            w1, [x0, #0x1b]
    // 0xbbc374: r1 = false
    //     0xbbc374: add             x1, NULL, #0x30  ; false
    // 0xbbc378: StoreField: r0->field_1f = r1
    //     0xbbc378: stur            w1, [x0, #0x1f]
    // 0xbbc37c: ldur            x1, [fp, #-0x10]
    // 0xbbc380: StoreField: r0->field_23 = r1
    //     0xbbc380: stur            w1, [x0, #0x23]
    // 0xbbc384: LeaveFrame
    //     0xbbc384: mov             SP, fp
    //     0xbbc388: ldp             fp, lr, [SP], #0x10
    // 0xbbc38c: ret
    //     0xbbc38c: ret             
    // 0xbbc390: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbc390: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbc394: b               #0xbbbbb4
    // 0xbbc398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbc398: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbc39c: r9 = _phoneController
    //     0xbbc39c: add             x9, PP, #0x53, lsl #12  ; [pp+0x53ea8] Field <_EditPhoneBottomSheetState@1675067928._phoneController@1675067928>: late (offset: 0x14)
    //     0xbbc3a0: ldr             x9, [x9, #0xea8]
    // 0xbbc3a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbc3a4: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildInputDecoration(/* No info */) {
    // ** addr: 0xbbc3a8, size: 0x1f8
    // 0xbbc3a8: EnterFrame
    //     0xbbc3a8: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc3ac: mov             fp, SP
    // 0xbbc3b0: AllocStack(0x78)
    //     0xbbc3b0: sub             SP, SP, #0x78
    // 0xbbc3b4: SetupParameters(_EditPhoneBottomSheetState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xbbc3b4: mov             x0, x2
    //     0xbbc3b8: stur            x2, [fp, #-0x10]
    //     0xbbc3bc: mov             x2, x1
    //     0xbbc3c0: stur            x1, [fp, #-8]
    // 0xbbc3c4: CheckStackOverflow
    //     0xbbc3c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbc3c8: cmp             SP, x16
    //     0xbbc3cc: b.ls            #0xbbc598
    // 0xbbc3d0: mov             x1, x0
    // 0xbbc3d4: r0 = of()
    //     0xbbc3d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xbbc3d8: ldur            x1, [fp, #-0x10]
    // 0xbbc3dc: stur            x0, [fp, #-0x10]
    // 0xbbc3e0: r0 = getTextFormFieldInputDecoration()
    //     0xbbc3e0: bl              #0xbb0738  ; [package:customer_app/app/core/utils/utils.dart] Utils::getTextFormFieldInputDecoration
    // 0xbbc3e4: ldur            x1, [fp, #-8]
    // 0xbbc3e8: ldur            x2, [fp, #-0x10]
    // 0xbbc3ec: stur            x0, [fp, #-0x18]
    // 0xbbc3f0: r0 = _buildPrefixIcon()
    //     0xbbc3f0: bl              #0xa08aec  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_buildPrefixIcon
    // 0xbbc3f4: mov             x1, x0
    // 0xbbc3f8: ldur            x0, [fp, #-8]
    // 0xbbc3fc: stur            x1, [fp, #-0x28]
    // 0xbbc400: LoadField: r2 = r0->field_1b
    //     0xbbc400: ldur            w2, [x0, #0x1b]
    // 0xbbc404: DecompressPointer r2
    //     0xbbc404: add             x2, x2, HEAP, lsl #32
    // 0xbbc408: tbnz            w2, #4, #0xbbc46c
    // 0xbbc40c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xbbc40c: ldur            w2, [x0, #0x17]
    // 0xbbc410: DecompressPointer r2
    //     0xbbc410: add             x2, x2, HEAP, lsl #32
    // 0xbbc414: tbnz            w2, #4, #0xbbc424
    // 0xbbc418: r0 = Instance_IconData
    //     0xbbc418: add             x0, PP, #0x37, lsl #12  ; [pp+0x37130] Obj!IconData@d55381
    //     0xbbc41c: ldr             x0, [x0, #0x130]
    // 0xbbc420: b               #0xbbc42c
    // 0xbbc424: r0 = Instance_IconData
    //     0xbbc424: add             x0, PP, #0x37, lsl #12  ; [pp+0x37138] Obj!IconData@d55161
    //     0xbbc428: ldr             x0, [x0, #0x138]
    // 0xbbc42c: stur            x0, [fp, #-0x20]
    // 0xbbc430: tbnz            w2, #4, #0xbbc440
    // 0xbbc434: r2 = Instance_Color
    //     0xbbc434: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0xbbc438: ldr             x2, [x2, #0x858]
    // 0xbbc43c: b               #0xbbc448
    // 0xbbc440: r2 = Instance_Color
    //     0xbbc440: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f050] Obj!Color@d69b11
    //     0xbbc444: ldr             x2, [x2, #0x50]
    // 0xbbc448: stur            x2, [fp, #-8]
    // 0xbbc44c: r0 = Icon()
    //     0xbbc44c: bl              #0x8faab8  ; AllocateIconStub -> Icon (size=0x40)
    // 0xbbc450: mov             x1, x0
    // 0xbbc454: ldur            x0, [fp, #-0x20]
    // 0xbbc458: StoreField: r1->field_b = r0
    //     0xbbc458: stur            w0, [x1, #0xb]
    // 0xbbc45c: ldur            x0, [fp, #-8]
    // 0xbbc460: StoreField: r1->field_23 = r0
    //     0xbbc460: stur            w0, [x1, #0x23]
    // 0xbbc464: mov             x2, x1
    // 0xbbc468: b               #0xbbc470
    // 0xbbc46c: r2 = Null
    //     0xbbc46c: mov             x2, NULL
    // 0xbbc470: ldur            x0, [fp, #-0x10]
    // 0xbbc474: stur            x2, [fp, #-0x20]
    // 0xbbc478: LoadField: r1 = r0->field_87
    //     0xbbc478: ldur            w1, [x0, #0x87]
    // 0xbbc47c: DecompressPointer r1
    //     0xbbc47c: add             x1, x1, HEAP, lsl #32
    // 0xbbc480: LoadField: r3 = r1->field_2b
    //     0xbbc480: ldur            w3, [x1, #0x2b]
    // 0xbbc484: DecompressPointer r3
    //     0xbbc484: add             x3, x3, HEAP, lsl #32
    // 0xbbc488: stur            x3, [fp, #-8]
    // 0xbbc48c: r1 = Instance_Color
    //     0xbbc48c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xbbc490: d0 = 0.400000
    //     0xbbc490: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0xbbc494: r0 = withOpacity()
    //     0xbbc494: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xbbc498: r16 = 14.000000
    //     0xbbc498: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0xbbc49c: ldr             x16, [x16, #0x1d8]
    // 0xbbc4a0: stp             x0, x16, [SP]
    // 0xbbc4a4: ldur            x1, [fp, #-8]
    // 0xbbc4a8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbc4a8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbc4ac: ldr             x4, [x4, #0xaa0]
    // 0xbbc4b0: r0 = copyWith()
    //     0xbbc4b0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbc4b4: stur            x0, [fp, #-0x30]
    // 0xbbc4b8: r16 = 12.000000
    //     0xbbc4b8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xbbc4bc: ldr             x16, [x16, #0x9e8]
    // 0xbbc4c0: r30 = Instance_MaterialColor
    //     0xbbc4c0: add             lr, PP, #8, lsl #12  ; [pp+0x8180] Obj!MaterialColor@d6bd21
    //     0xbbc4c4: ldr             lr, [lr, #0x180]
    // 0xbbc4c8: stp             lr, x16, [SP]
    // 0xbbc4cc: ldur            x1, [fp, #-8]
    // 0xbbc4d0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xbbc4d0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xbbc4d4: ldr             x4, [x4, #0xaa0]
    // 0xbbc4d8: r0 = copyWith()
    //     0xbbc4d8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xbbc4dc: mov             x1, x0
    // 0xbbc4e0: ldur            x0, [fp, #-0x10]
    // 0xbbc4e4: stur            x1, [fp, #-0x38]
    // 0xbbc4e8: LoadField: r2 = r0->field_5b
    //     0xbbc4e8: ldur            w2, [x0, #0x5b]
    // 0xbbc4ec: DecompressPointer r2
    //     0xbbc4ec: add             x2, x2, HEAP, lsl #32
    // 0xbbc4f0: stur            x2, [fp, #-8]
    // 0xbbc4f4: r0 = BorderSide()
    //     0xbbc4f4: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0xbbc4f8: mov             x1, x0
    // 0xbbc4fc: ldur            x0, [fp, #-8]
    // 0xbbc500: stur            x1, [fp, #-0x10]
    // 0xbbc504: StoreField: r1->field_7 = r0
    //     0xbbc504: stur            w0, [x1, #7]
    // 0xbbc508: d0 = 1.000000
    //     0xbbc508: fmov            d0, #1.00000000
    // 0xbbc50c: StoreField: r1->field_b = d0
    //     0xbbc50c: stur            d0, [x1, #0xb]
    // 0xbbc510: r0 = Instance_BorderStyle
    //     0xbbc510: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0xbbc514: ldr             x0, [x0, #0xf68]
    // 0xbbc518: StoreField: r1->field_13 = r0
    //     0xbbc518: stur            w0, [x1, #0x13]
    // 0xbbc51c: d0 = -1.000000
    //     0xbbc51c: fmov            d0, #-1.00000000
    // 0xbbc520: ArrayStore: r1[0] = d0  ; List_8
    //     0xbbc520: stur            d0, [x1, #0x17]
    // 0xbbc524: r0 = OutlineInputBorder()
    //     0xbbc524: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0xbbc528: mov             x1, x0
    // 0xbbc52c: r0 = Instance_BorderRadius
    //     0xbbc52c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f70] Obj!BorderRadius@d5a1a1
    //     0xbbc530: ldr             x0, [x0, #0xf70]
    // 0xbbc534: StoreField: r1->field_13 = r0
    //     0xbbc534: stur            w0, [x1, #0x13]
    // 0xbbc538: d0 = 4.000000
    //     0xbbc538: fmov            d0, #4.00000000
    // 0xbbc53c: StoreField: r1->field_b = d0
    //     0xbbc53c: stur            d0, [x1, #0xb]
    // 0xbbc540: ldur            x0, [fp, #-0x10]
    // 0xbbc544: StoreField: r1->field_7 = r0
    //     0xbbc544: stur            w0, [x1, #7]
    // 0xbbc548: ldur            x16, [fp, #-0x28]
    // 0xbbc54c: ldur            lr, [fp, #-0x20]
    // 0xbbc550: stp             lr, x16, [SP, #0x30]
    // 0xbbc554: r16 = Instance_EdgeInsets
    //     0xbbc554: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c40] Obj!EdgeInsets@d57021
    //     0xbbc558: ldr             x16, [x16, #0xc40]
    // 0xbbc55c: r30 = "Enter WhatsApp no."
    //     0xbbc55c: add             lr, PP, #0x53, lsl #12  ; [pp+0x53ec8] "Enter WhatsApp no."
    //     0xbbc560: ldr             lr, [lr, #0xec8]
    // 0xbbc564: stp             lr, x16, [SP, #0x20]
    // 0xbbc568: ldur            x16, [fp, #-0x30]
    // 0xbbc56c: ldur            lr, [fp, #-0x38]
    // 0xbbc570: stp             lr, x16, [SP, #0x10]
    // 0xbbc574: r16 = 4
    //     0xbbc574: movz            x16, #0x4
    // 0xbbc578: stp             x1, x16, [SP]
    // 0xbbc57c: ldur            x1, [fp, #-0x18]
    // 0xbbc580: r4 = const [0, 0x9, 0x8, 0x1, contentPadding, 0x3, errorMaxLines, 0x7, errorStyle, 0x6, focusedBorder, 0x8, hintStyle, 0x5, hintText, 0x4, prefixIcon, 0x1, suffixIcon, 0x2, null]
    //     0xbbc580: add             x4, PP, #0x53, lsl #12  ; [pp+0x53ed0] List(21) [0, 0x9, 0x8, 0x1, "contentPadding", 0x3, "errorMaxLines", 0x7, "errorStyle", 0x6, "focusedBorder", 0x8, "hintStyle", 0x5, "hintText", 0x4, "prefixIcon", 0x1, "suffixIcon", 0x2, Null]
    //     0xbbc584: ldr             x4, [x4, #0xed0]
    // 0xbbc588: r0 = copyWith()
    //     0xbbc588: bl              #0x811ca8  ; [package:flutter/src/material/input_decorator.dart] InputDecoration::copyWith
    // 0xbbc58c: LeaveFrame
    //     0xbbc58c: mov             SP, fp
    //     0xbbc590: ldp             fp, lr, [SP], #0x10
    // 0xbbc594: ret
    //     0xbbc594: ret             
    // 0xbbc598: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbc598: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbc59c: b               #0xbbc3d0
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xbbc5a0, size: 0x10c
    // 0xbbc5a0: EnterFrame
    //     0xbbc5a0: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc5a4: mov             fp, SP
    // 0xbbc5a8: AllocStack(0x20)
    //     0xbbc5a8: sub             SP, SP, #0x20
    // 0xbbc5ac: SetupParameters()
    //     0xbbc5ac: ldr             x0, [fp, #0x10]
    //     0xbbc5b0: ldur            w1, [x0, #0x17]
    //     0xbbc5b4: add             x1, x1, HEAP, lsl #32
    //     0xbbc5b8: stur            x1, [fp, #-8]
    // 0xbbc5bc: CheckStackOverflow
    //     0xbbc5bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbc5c0: cmp             SP, x16
    //     0xbbc5c4: b.ls            #0xbbc684
    // 0xbbc5c8: LoadField: r0 = r1->field_f
    //     0xbbc5c8: ldur            w0, [x1, #0xf]
    // 0xbbc5cc: DecompressPointer r0
    //     0xbbc5cc: add             x0, x0, HEAP, lsl #32
    // 0xbbc5d0: LoadField: r2 = r0->field_b
    //     0xbbc5d0: ldur            w2, [x0, #0xb]
    // 0xbbc5d4: DecompressPointer r2
    //     0xbbc5d4: add             x2, x2, HEAP, lsl #32
    // 0xbbc5d8: cmp             w2, NULL
    // 0xbbc5dc: b.eq            #0xbbc68c
    // 0xbbc5e0: LoadField: r3 = r2->field_f
    //     0xbbc5e0: ldur            w3, [x2, #0xf]
    // 0xbbc5e4: DecompressPointer r3
    //     0xbbc5e4: add             x3, x3, HEAP, lsl #32
    // 0xbbc5e8: LoadField: r2 = r0->field_13
    //     0xbbc5e8: ldur            w2, [x0, #0x13]
    // 0xbbc5ec: DecompressPointer r2
    //     0xbbc5ec: add             x2, x2, HEAP, lsl #32
    // 0xbbc5f0: r16 = Sentinel
    //     0xbbc5f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbc5f4: cmp             w2, w16
    // 0xbbc5f8: b.eq            #0xbbc690
    // 0xbbc5fc: LoadField: r0 = r2->field_27
    //     0xbbc5fc: ldur            w0, [x2, #0x27]
    // 0xbbc600: DecompressPointer r0
    //     0xbbc600: add             x0, x0, HEAP, lsl #32
    // 0xbbc604: LoadField: r2 = r0->field_7
    //     0xbbc604: ldur            w2, [x0, #7]
    // 0xbbc608: DecompressPointer r2
    //     0xbbc608: add             x2, x2, HEAP, lsl #32
    // 0xbbc60c: cmp             w3, NULL
    // 0xbbc610: b.eq            #0xbbc69c
    // 0xbbc614: stp             x2, x3, [SP]
    // 0xbbc618: mov             x0, x3
    // 0xbbc61c: ClosureCall
    //     0xbbc61c: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xbbc620: ldur            x2, [x0, #0x1f]
    //     0xbbc624: blr             x2
    // 0xbbc628: ldur            x0, [fp, #-8]
    // 0xbbc62c: LoadField: r1 = r0->field_13
    //     0xbbc62c: ldur            w1, [x0, #0x13]
    // 0xbbc630: DecompressPointer r1
    //     0xbbc630: add             x1, x1, HEAP, lsl #32
    // 0xbbc634: LoadField: r2 = r0->field_f
    //     0xbbc634: ldur            w2, [x0, #0xf]
    // 0xbbc638: DecompressPointer r2
    //     0xbbc638: add             x2, x2, HEAP, lsl #32
    // 0xbbc63c: LoadField: r0 = r2->field_13
    //     0xbbc63c: ldur            w0, [x2, #0x13]
    // 0xbbc640: DecompressPointer r0
    //     0xbbc640: add             x0, x0, HEAP, lsl #32
    // 0xbbc644: r16 = Sentinel
    //     0xbbc644: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xbbc648: cmp             w0, w16
    // 0xbbc64c: b.eq            #0xbbc6a0
    // 0xbbc650: LoadField: r2 = r0->field_27
    //     0xbbc650: ldur            w2, [x0, #0x27]
    // 0xbbc654: DecompressPointer r2
    //     0xbbc654: add             x2, x2, HEAP, lsl #32
    // 0xbbc658: LoadField: r0 = r2->field_7
    //     0xbbc658: ldur            w0, [x2, #7]
    // 0xbbc65c: DecompressPointer r0
    //     0xbbc65c: add             x0, x0, HEAP, lsl #32
    // 0xbbc660: r16 = <String>
    //     0xbbc660: ldr             x16, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    // 0xbbc664: stp             x1, x16, [SP, #8]
    // 0xbbc668: str             x0, [SP]
    // 0xbbc66c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xbbc66c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xbbc670: r0 = pop()
    //     0xbbc670: bl              #0x98871c  ; [package:flutter/src/widgets/navigator.dart] Navigator::pop
    // 0xbbc674: r0 = Null
    //     0xbbc674: mov             x0, NULL
    // 0xbbc678: LeaveFrame
    //     0xbbc678: mov             SP, fp
    //     0xbbc67c: ldp             fp, lr, [SP], #0x10
    // 0xbbc680: ret
    //     0xbbc680: ret             
    // 0xbbc684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbc684: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbc688: b               #0xbbc5c8
    // 0xbbc68c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbbc68c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xbbc690: r9 = _phoneController
    //     0xbbc690: add             x9, PP, #0x53, lsl #12  ; [pp+0x53ea8] Field <_EditPhoneBottomSheetState@1675067928._phoneController@1675067928>: late (offset: 0x14)
    //     0xbbc694: ldr             x9, [x9, #0xea8]
    // 0xbbc698: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbc698: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xbbc69c: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbbc69c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xbbc6a0: r9 = _phoneController
    //     0xbbc6a0: add             x9, PP, #0x53, lsl #12  ; [pp+0x53ea8] Field <_EditPhoneBottomSheetState@1675067928._phoneController@1675067928>: late (offset: 0x14)
    //     0xbbc6a4: ldr             x9, [x9, #0xea8]
    // 0xbbc6a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xbbc6a8: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] String? _validatePhoneNumber(dynamic, String?) {
    // ** addr: 0xbbc6ac, size: 0x3c
    // 0xbbc6ac: EnterFrame
    //     0xbbc6ac: stp             fp, lr, [SP, #-0x10]!
    //     0xbbc6b0: mov             fp, SP
    // 0xbbc6b4: ldr             x0, [fp, #0x18]
    // 0xbbc6b8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbbc6b8: ldur            w1, [x0, #0x17]
    // 0xbbc6bc: DecompressPointer r1
    //     0xbbc6bc: add             x1, x1, HEAP, lsl #32
    // 0xbbc6c0: CheckStackOverflow
    //     0xbbc6c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbbc6c4: cmp             SP, x16
    //     0xbbc6c8: b.ls            #0xbbc6e0
    // 0xbbc6cc: ldr             x2, [fp, #0x10]
    // 0xbbc6d0: r0 = _validatePhoneNumber()
    //     0xbbc6d0: bl              #0xa08f3c  ; [package:customer_app/app/presentation/views/basic/checkout_variants/widgets/checkout_number_widget.dart] _CheckoutNumberWidgetState::_validatePhoneNumber
    // 0xbbc6d4: LeaveFrame
    //     0xbbc6d4: mov             SP, fp
    //     0xbbc6d8: ldp             fp, lr, [SP], #0x10
    // 0xbbc6dc: ret
    //     0xbbc6dc: ret             
    // 0xbbc6e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbbc6e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbbc6e4: b               #0xbbc6cc
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87ff8, size: 0x54
    // 0xc87ff8: EnterFrame
    //     0xc87ff8: stp             fp, lr, [SP, #-0x10]!
    //     0xc87ffc: mov             fp, SP
    // 0xc88000: CheckStackOverflow
    //     0xc88000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc88004: cmp             SP, x16
    //     0xc88008: b.ls            #0xc88038
    // 0xc8800c: LoadField: r0 = r1->field_13
    //     0xc8800c: ldur            w0, [x1, #0x13]
    // 0xc88010: DecompressPointer r0
    //     0xc88010: add             x0, x0, HEAP, lsl #32
    // 0xc88014: r16 = Sentinel
    //     0xc88014: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc88018: cmp             w0, w16
    // 0xc8801c: b.eq            #0xc88040
    // 0xc88020: mov             x1, x0
    // 0xc88024: r0 = dispose()
    //     0xc88024: bl              #0xc90a7c  ; [package:flutter/src/widgets/focus_manager.dart] _FocusNode&Object&DiagnosticableTreeMixin&ChangeNotifier::dispose
    // 0xc88028: r0 = Null
    //     0xc88028: mov             x0, NULL
    // 0xc8802c: LeaveFrame
    //     0xc8802c: mov             SP, fp
    //     0xc88030: ldp             fp, lr, [SP], #0x10
    // 0xc88034: ret
    //     0xc88034: ret             
    // 0xc88038: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc88038: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc8803c: b               #0xc8800c
    // 0xc88040: r9 = _phoneController
    //     0xc88040: add             x9, PP, #0x53, lsl #12  ; [pp+0x53ea8] Field <_EditPhoneBottomSheetState@1675067928._phoneController@1675067928>: late (offset: 0x14)
    //     0xc88044: ldr             x9, [x9, #0xea8]
    // 0xc88048: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc88048: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4014, size: 0x14, field offset: 0xc
//   const constructor, 
class EditPhoneBottomSheet extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80410, size: 0x5c
    // 0xc80410: EnterFrame
    //     0xc80410: stp             fp, lr, [SP, #-0x10]!
    //     0xc80414: mov             fp, SP
    // 0xc80418: AllocStack(0x8)
    //     0xc80418: sub             SP, SP, #8
    // 0xc8041c: SetupParameters(EditPhoneBottomSheet this /* r1 => r0 */)
    //     0xc8041c: mov             x0, x1
    // 0xc80420: r1 = <EditPhoneBottomSheet>
    //     0xc80420: add             x1, PP, #0x48, lsl #12  ; [pp+0x48610] TypeArguments: <EditPhoneBottomSheet>
    //     0xc80424: ldr             x1, [x1, #0x610]
    // 0xc80428: r0 = _EditPhoneBottomSheetState()
    //     0xc80428: bl              #0xc8046c  ; Allocate_EditPhoneBottomSheetStateStub -> _EditPhoneBottomSheetState (size=0x24)
    // 0xc8042c: mov             x2, x0
    // 0xc80430: r0 = Sentinel
    //     0xc80430: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc80434: stur            x2, [fp, #-8]
    // 0xc80438: StoreField: r2->field_13 = r0
    //     0xc80438: stur            w0, [x2, #0x13]
    // 0xc8043c: r0 = false
    //     0xc8043c: add             x0, NULL, #0x30  ; false
    // 0xc80440: ArrayStore: r2[0] = r0  ; List_4
    //     0xc80440: stur            w0, [x2, #0x17]
    // 0xc80444: StoreField: r2->field_1b = r0
    //     0xc80444: stur            w0, [x2, #0x1b]
    // 0xc80448: r1 = <FormState>
    //     0xc80448: add             x1, PP, #0xd, lsl #12  ; [pp+0xdad8] TypeArguments: <FormState>
    //     0xc8044c: ldr             x1, [x1, #0xad8]
    // 0xc80450: r0 = LabeledGlobalKey()
    //     0xc80450: bl              #0x689b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xc80454: mov             x1, x0
    // 0xc80458: ldur            x0, [fp, #-8]
    // 0xc8045c: StoreField: r0->field_1f = r1
    //     0xc8045c: stur            w1, [x0, #0x1f]
    // 0xc80460: LeaveFrame
    //     0xc80460: mov             SP, fp
    //     0xc80464: ldp             fp, lr, [SP], #0x10
    // 0xc80468: ret
    //     0xc80468: ret             
  }
}
