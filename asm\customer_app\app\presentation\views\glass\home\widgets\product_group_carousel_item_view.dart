// lib: , url: package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart

// class id: 1049404, size: 0x8
class :: {
}

// class id: 3334, size: 0x24, field offset: 0x14
class _ProductGroupCarouselItemViewState extends State<dynamic> {

  late PageController _pageController; // offset: 0x14
  late PageController _imagePageController; // offset: 0x18

  _ build(/* No info */) {
    // ** addr: 0xb665fc, size: 0x620
    // 0xb665fc: EnterFrame
    //     0xb665fc: stp             fp, lr, [SP, #-0x10]!
    //     0xb66600: mov             fp, SP
    // 0xb66604: AllocStack(0x88)
    //     0xb66604: sub             SP, SP, #0x88
    // 0xb66608: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xb66608: mov             x0, x1
    //     0xb6660c: stur            x1, [fp, #-8]
    //     0xb66610: mov             x1, x2
    //     0xb66614: stur            x2, [fp, #-0x10]
    // 0xb66618: CheckStackOverflow
    //     0xb66618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6661c: cmp             SP, x16
    //     0xb66620: b.ls            #0xb66bdc
    // 0xb66624: r1 = 1
    //     0xb66624: movz            x1, #0x1
    // 0xb66628: r0 = AllocateContext()
    //     0xb66628: bl              #0x16f6108  ; AllocateContextStub
    // 0xb6662c: mov             x2, x0
    // 0xb66630: ldur            x0, [fp, #-8]
    // 0xb66634: stur            x2, [fp, #-0x20]
    // 0xb66638: StoreField: r2->field_f = r0
    //     0xb66638: stur            w0, [x2, #0xf]
    // 0xb6663c: LoadField: r1 = r0->field_b
    //     0xb6663c: ldur            w1, [x0, #0xb]
    // 0xb66640: DecompressPointer r1
    //     0xb66640: add             x1, x1, HEAP, lsl #32
    // 0xb66644: cmp             w1, NULL
    // 0xb66648: b.eq            #0xb66be4
    // 0xb6664c: LoadField: r3 = r1->field_b
    //     0xb6664c: ldur            w3, [x1, #0xb]
    // 0xb66650: DecompressPointer r3
    //     0xb66650: add             x3, x3, HEAP, lsl #32
    // 0xb66654: cmp             w3, NULL
    // 0xb66658: b.ne            #0xb66664
    // 0xb6665c: r3 = Null
    //     0xb6665c: mov             x3, NULL
    // 0xb66660: b               #0xb66678
    // 0xb66664: LoadField: r4 = r3->field_b
    //     0xb66664: ldur            w4, [x3, #0xb]
    // 0xb66668: cbz             w4, #0xb66674
    // 0xb6666c: r3 = false
    //     0xb6666c: add             x3, NULL, #0x30  ; false
    // 0xb66670: b               #0xb66678
    // 0xb66674: r3 = true
    //     0xb66674: add             x3, NULL, #0x20  ; true
    // 0xb66678: cmp             w3, NULL
    // 0xb6667c: b.eq            #0xb66684
    // 0xb66680: tbnz            w3, #4, #0xb66694
    // 0xb66684: r0 = Instance_SizedBox
    //     0xb66684: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb66688: LeaveFrame
    //     0xb66688: mov             SP, fp
    //     0xb6668c: ldp             fp, lr, [SP], #0x10
    // 0xb66690: ret
    //     0xb66690: ret             
    // 0xb66694: LoadField: r3 = r1->field_1b
    //     0xb66694: ldur            w3, [x1, #0x1b]
    // 0xb66698: DecompressPointer r3
    //     0xb66698: add             x3, x3, HEAP, lsl #32
    // 0xb6669c: LoadField: r1 = r3->field_7
    //     0xb6669c: ldur            w1, [x3, #7]
    // 0xb666a0: DecompressPointer r1
    //     0xb666a0: add             x1, x1, HEAP, lsl #32
    // 0xb666a4: cmp             w1, NULL
    // 0xb666a8: b.ne            #0xb666b4
    // 0xb666ac: r1 = Instance_TitleAlignment
    //     0xb666ac: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb666b0: ldr             x1, [x1, #0x518]
    // 0xb666b4: r16 = Instance_TitleAlignment
    //     0xb666b4: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb666b8: ldr             x16, [x16, #0x520]
    // 0xb666bc: cmp             w1, w16
    // 0xb666c0: b.ne            #0xb666d0
    // 0xb666c4: r3 = Instance_CrossAxisAlignment
    //     0xb666c4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32c68] Obj!CrossAxisAlignment@d733e1
    //     0xb666c8: ldr             x3, [x3, #0xc68]
    // 0xb666cc: b               #0xb666f4
    // 0xb666d0: r16 = Instance_TitleAlignment
    //     0xb666d0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb666d4: ldr             x16, [x16, #0x518]
    // 0xb666d8: cmp             w1, w16
    // 0xb666dc: b.ne            #0xb666ec
    // 0xb666e0: r3 = Instance_CrossAxisAlignment
    //     0xb666e0: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb666e4: ldr             x3, [x3, #0x890]
    // 0xb666e8: b               #0xb666f4
    // 0xb666ec: r3 = Instance_CrossAxisAlignment
    //     0xb666ec: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb666f0: ldr             x3, [x3, #0xa18]
    // 0xb666f4: mov             x1, x0
    // 0xb666f8: stur            x3, [fp, #-0x18]
    // 0xb666fc: r0 = _buildHeader()
    //     0xb666fc: bl              #0xb677f0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildHeader
    // 0xb66700: ldur            x1, [fp, #-8]
    // 0xb66704: stur            x0, [fp, #-0x28]
    // 0xb66708: r0 = _buildBumperCoupon()
    //     0xb66708: bl              #0xb66ff0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildBumperCoupon
    // 0xb6670c: ldur            x1, [fp, #-0x10]
    // 0xb66710: stur            x0, [fp, #-0x30]
    // 0xb66714: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb66714: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb66718: r0 = _of()
    //     0xb66718: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb6671c: LoadField: r1 = r0->field_7
    //     0xb6671c: ldur            w1, [x0, #7]
    // 0xb66720: DecompressPointer r1
    //     0xb66720: add             x1, x1, HEAP, lsl #32
    // 0xb66724: LoadField: d0 = r1->field_f
    //     0xb66724: ldur            d0, [x1, #0xf]
    // 0xb66728: d1 = 0.325000
    //     0xb66728: add             x17, PP, #0x55, lsl #12  ; [pp+0x55c50] IMM: double(0.325) from 0x3fd4cccccccccccd
    //     0xb6672c: ldr             d1, [x17, #0xc50]
    // 0xb66730: fmul            d2, d0, d1
    // 0xb66734: ldur            x1, [fp, #-0x10]
    // 0xb66738: stur            d2, [fp, #-0x68]
    // 0xb6673c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb6673c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb66740: r0 = _of()
    //     0xb66740: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb66744: LoadField: r1 = r0->field_7
    //     0xb66744: ldur            w1, [x0, #7]
    // 0xb66748: DecompressPointer r1
    //     0xb66748: add             x1, x1, HEAP, lsl #32
    // 0xb6674c: LoadField: d0 = r1->field_7
    //     0xb6674c: ldur            d0, [x1, #7]
    // 0xb66750: ldur            x1, [fp, #-8]
    // 0xb66754: stur            d0, [fp, #-0x70]
    // 0xb66758: r0 = totalPages()
    //     0xb66758: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xb6675c: mov             x2, x0
    // 0xb66760: ldur            x3, [fp, #-8]
    // 0xb66764: LoadField: r4 = r3->field_13
    //     0xb66764: ldur            w4, [x3, #0x13]
    // 0xb66768: DecompressPointer r4
    //     0xb66768: add             x4, x4, HEAP, lsl #32
    // 0xb6676c: r16 = Sentinel
    //     0xb6676c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb66770: cmp             w4, w16
    // 0xb66774: b.eq            #0xb66be8
    // 0xb66778: stur            x4, [fp, #-0x40]
    // 0xb6677c: r0 = BoxInt64Instr(r2)
    //     0xb6677c: sbfiz           x0, x2, #1, #0x1f
    //     0xb66780: cmp             x2, x0, asr #1
    //     0xb66784: b.eq            #0xb66790
    //     0xb66788: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb6678c: stur            x2, [x0, #7]
    // 0xb66790: ldur            x2, [fp, #-0x20]
    // 0xb66794: r1 = Function '<anonymous closure>':.
    //     0xb66794: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c58] AnonymousClosure: (0xb69da4), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xb665fc)
    //     0xb66798: ldr             x1, [x1, #0xc58]
    // 0xb6679c: stur            x0, [fp, #-0x38]
    // 0xb667a0: r0 = AllocateClosure()
    //     0xb667a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb667a4: ldur            x2, [fp, #-0x20]
    // 0xb667a8: r1 = Function '<anonymous closure>':.
    //     0xb667a8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c60] AnonymousClosure: (0xb679ac), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xb665fc)
    //     0xb667ac: ldr             x1, [x1, #0xc60]
    // 0xb667b0: stur            x0, [fp, #-0x20]
    // 0xb667b4: r0 = AllocateClosure()
    //     0xb667b4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb667b8: stur            x0, [fp, #-0x48]
    // 0xb667bc: r0 = PageView()
    //     0xb667bc: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb667c0: stur            x0, [fp, #-0x50]
    // 0xb667c4: r16 = Instance_BouncingScrollPhysics
    //     0xb667c4: add             x16, PP, #0x33, lsl #12  ; [pp+0x33890] Obj!BouncingScrollPhysics@d558f1
    //     0xb667c8: ldr             x16, [x16, #0x890]
    // 0xb667cc: r30 = false
    //     0xb667cc: add             lr, NULL, #0x30  ; false
    // 0xb667d0: stp             lr, x16, [SP, #8]
    // 0xb667d4: ldur            x16, [fp, #-0x40]
    // 0xb667d8: str             x16, [SP]
    // 0xb667dc: mov             x1, x0
    // 0xb667e0: ldur            x2, [fp, #-0x48]
    // 0xb667e4: ldur            x3, [fp, #-0x38]
    // 0xb667e8: ldur            x5, [fp, #-0x20]
    // 0xb667ec: r4 = const [0, 0x7, 0x3, 0x4, controller, 0x6, padEnds, 0x5, physics, 0x4, null]
    //     0xb667ec: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d20] List(11) [0, 0x7, 0x3, 0x4, "controller", 0x6, "padEnds", 0x5, "physics", 0x4, Null]
    //     0xb667f0: ldr             x4, [x4, #0xd20]
    // 0xb667f4: r0 = PageView.builder()
    //     0xb667f4: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb667f8: ldur            d0, [fp, #-0x70]
    // 0xb667fc: r0 = inline_Allocate_Double()
    //     0xb667fc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb66800: add             x0, x0, #0x10
    //     0xb66804: cmp             x1, x0
    //     0xb66808: b.ls            #0xb66bf4
    //     0xb6680c: str             x0, [THR, #0x50]  ; THR::top
    //     0xb66810: sub             x0, x0, #0xf
    //     0xb66814: movz            x1, #0xe15c
    //     0xb66818: movk            x1, #0x3, lsl #16
    //     0xb6681c: stur            x1, [x0, #-1]
    // 0xb66820: StoreField: r0->field_7 = d0
    //     0xb66820: stur            d0, [x0, #7]
    // 0xb66824: stur            x0, [fp, #-0x20]
    // 0xb66828: r0 = SizedBox()
    //     0xb66828: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb6682c: mov             x3, x0
    // 0xb66830: ldur            x0, [fp, #-0x20]
    // 0xb66834: stur            x3, [fp, #-0x38]
    // 0xb66838: StoreField: r3->field_f = r0
    //     0xb66838: stur            w0, [x3, #0xf]
    // 0xb6683c: ldur            d0, [fp, #-0x68]
    // 0xb66840: r0 = inline_Allocate_Double()
    //     0xb66840: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xb66844: add             x0, x0, #0x10
    //     0xb66848: cmp             x1, x0
    //     0xb6684c: b.ls            #0xb66c04
    //     0xb66850: str             x0, [THR, #0x50]  ; THR::top
    //     0xb66854: sub             x0, x0, #0xf
    //     0xb66858: movz            x1, #0xe15c
    //     0xb6685c: movk            x1, #0x3, lsl #16
    //     0xb66860: stur            x1, [x0, #-1]
    // 0xb66864: StoreField: r0->field_7 = d0
    //     0xb66864: stur            d0, [x0, #7]
    // 0xb66868: StoreField: r3->field_13 = r0
    //     0xb66868: stur            w0, [x3, #0x13]
    // 0xb6686c: ldur            x0, [fp, #-0x50]
    // 0xb66870: StoreField: r3->field_b = r0
    //     0xb66870: stur            w0, [x3, #0xb]
    // 0xb66874: r1 = Null
    //     0xb66874: mov             x1, NULL
    // 0xb66878: r2 = 8
    //     0xb66878: movz            x2, #0x8
    // 0xb6687c: r0 = AllocateArray()
    //     0xb6687c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb66880: mov             x2, x0
    // 0xb66884: ldur            x0, [fp, #-0x28]
    // 0xb66888: stur            x2, [fp, #-0x20]
    // 0xb6688c: StoreField: r2->field_f = r0
    //     0xb6688c: stur            w0, [x2, #0xf]
    // 0xb66890: r16 = Instance_SizedBox
    //     0xb66890: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0xb66894: ldr             x16, [x16, #0xc70]
    // 0xb66898: StoreField: r2->field_13 = r16
    //     0xb66898: stur            w16, [x2, #0x13]
    // 0xb6689c: ldur            x0, [fp, #-0x30]
    // 0xb668a0: ArrayStore: r2[0] = r0  ; List_4
    //     0xb668a0: stur            w0, [x2, #0x17]
    // 0xb668a4: ldur            x0, [fp, #-0x38]
    // 0xb668a8: StoreField: r2->field_1b = r0
    //     0xb668a8: stur            w0, [x2, #0x1b]
    // 0xb668ac: r1 = <Widget>
    //     0xb668ac: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb668b0: r0 = AllocateGrowableArray()
    //     0xb668b0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb668b4: mov             x2, x0
    // 0xb668b8: ldur            x0, [fp, #-0x20]
    // 0xb668bc: stur            x2, [fp, #-0x28]
    // 0xb668c0: StoreField: r2->field_f = r0
    //     0xb668c0: stur            w0, [x2, #0xf]
    // 0xb668c4: r0 = 8
    //     0xb668c4: movz            x0, #0x8
    // 0xb668c8: StoreField: r2->field_b = r0
    //     0xb668c8: stur            w0, [x2, #0xb]
    // 0xb668cc: ldur            x1, [fp, #-8]
    // 0xb668d0: r0 = totalPages()
    //     0xb668d0: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xb668d4: cmp             x0, #1
    // 0xb668d8: b.le            #0xb66a78
    // 0xb668dc: ldur            x2, [fp, #-8]
    // 0xb668e0: ldur            x0, [fp, #-0x28]
    // 0xb668e4: mov             x1, x2
    // 0xb668e8: r0 = totalPages()
    //     0xb668e8: bl              #0xa55010  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::totalPages
    // 0xb668ec: mov             x2, x0
    // 0xb668f0: ldur            x0, [fp, #-8]
    // 0xb668f4: stur            x2, [fp, #-0x60]
    // 0xb668f8: LoadField: r3 = r0->field_1b
    //     0xb668f8: ldur            x3, [x0, #0x1b]
    // 0xb668fc: ldur            x1, [fp, #-0x10]
    // 0xb66900: stur            x3, [fp, #-0x58]
    // 0xb66904: r0 = of()
    //     0xb66904: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb66908: LoadField: r1 = r0->field_5b
    //     0xb66908: ldur            w1, [x0, #0x5b]
    // 0xb6690c: DecompressPointer r1
    //     0xb6690c: add             x1, x1, HEAP, lsl #32
    // 0xb66910: stur            x1, [fp, #-0x10]
    // 0xb66914: r0 = CarouselIndicator()
    //     0xb66914: bl              #0x98e858  ; AllocateCarouselIndicatorStub -> CarouselIndicator (size=0x24)
    // 0xb66918: mov             x3, x0
    // 0xb6691c: ldur            x0, [fp, #-0x60]
    // 0xb66920: stur            x3, [fp, #-0x20]
    // 0xb66924: StoreField: r3->field_b = r0
    //     0xb66924: stur            x0, [x3, #0xb]
    // 0xb66928: ldur            x0, [fp, #-0x58]
    // 0xb6692c: StoreField: r3->field_13 = r0
    //     0xb6692c: stur            x0, [x3, #0x13]
    // 0xb66930: ldur            x0, [fp, #-0x10]
    // 0xb66934: StoreField: r3->field_1b = r0
    //     0xb66934: stur            w0, [x3, #0x1b]
    // 0xb66938: r0 = Instance_Color
    //     0xb66938: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0xb6693c: ldr             x0, [x0, #0x90]
    // 0xb66940: StoreField: r3->field_1f = r0
    //     0xb66940: stur            w0, [x3, #0x1f]
    // 0xb66944: r1 = Null
    //     0xb66944: mov             x1, NULL
    // 0xb66948: r2 = 2
    //     0xb66948: movz            x2, #0x2
    // 0xb6694c: r0 = AllocateArray()
    //     0xb6694c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb66950: mov             x2, x0
    // 0xb66954: ldur            x0, [fp, #-0x20]
    // 0xb66958: stur            x2, [fp, #-0x10]
    // 0xb6695c: StoreField: r2->field_f = r0
    //     0xb6695c: stur            w0, [x2, #0xf]
    // 0xb66960: r1 = <Widget>
    //     0xb66960: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb66964: r0 = AllocateGrowableArray()
    //     0xb66964: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb66968: mov             x1, x0
    // 0xb6696c: ldur            x0, [fp, #-0x10]
    // 0xb66970: stur            x1, [fp, #-0x20]
    // 0xb66974: StoreField: r1->field_f = r0
    //     0xb66974: stur            w0, [x1, #0xf]
    // 0xb66978: r0 = 2
    //     0xb66978: movz            x0, #0x2
    // 0xb6697c: StoreField: r1->field_b = r0
    //     0xb6697c: stur            w0, [x1, #0xb]
    // 0xb66980: r0 = Row()
    //     0xb66980: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb66984: mov             x1, x0
    // 0xb66988: r0 = Instance_Axis
    //     0xb66988: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb6698c: stur            x1, [fp, #-0x10]
    // 0xb66990: StoreField: r1->field_f = r0
    //     0xb66990: stur            w0, [x1, #0xf]
    // 0xb66994: r0 = Instance_MainAxisAlignment
    //     0xb66994: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2eab0] Obj!MainAxisAlignment@d73481
    //     0xb66998: ldr             x0, [x0, #0xab0]
    // 0xb6699c: StoreField: r1->field_13 = r0
    //     0xb6699c: stur            w0, [x1, #0x13]
    // 0xb669a0: r0 = Instance_MainAxisSize
    //     0xb669a0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb669a4: ldr             x0, [x0, #0xa10]
    // 0xb669a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb669a8: stur            w0, [x1, #0x17]
    // 0xb669ac: r2 = Instance_CrossAxisAlignment
    //     0xb669ac: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb669b0: ldr             x2, [x2, #0xa18]
    // 0xb669b4: StoreField: r1->field_1b = r2
    //     0xb669b4: stur            w2, [x1, #0x1b]
    // 0xb669b8: r2 = Instance_VerticalDirection
    //     0xb669b8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb669bc: ldr             x2, [x2, #0xa20]
    // 0xb669c0: StoreField: r1->field_23 = r2
    //     0xb669c0: stur            w2, [x1, #0x23]
    // 0xb669c4: r3 = Instance_Clip
    //     0xb669c4: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb669c8: ldr             x3, [x3, #0x38]
    // 0xb669cc: StoreField: r1->field_2b = r3
    //     0xb669cc: stur            w3, [x1, #0x2b]
    // 0xb669d0: StoreField: r1->field_2f = rZR
    //     0xb669d0: stur            xzr, [x1, #0x2f]
    // 0xb669d4: ldur            x4, [fp, #-0x20]
    // 0xb669d8: StoreField: r1->field_b = r4
    //     0xb669d8: stur            w4, [x1, #0xb]
    // 0xb669dc: r0 = Padding()
    //     0xb669dc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb669e0: mov             x2, x0
    // 0xb669e4: r0 = Instance_EdgeInsets
    //     0xb669e4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb669e8: ldr             x0, [x0, #0xa00]
    // 0xb669ec: stur            x2, [fp, #-0x20]
    // 0xb669f0: StoreField: r2->field_f = r0
    //     0xb669f0: stur            w0, [x2, #0xf]
    // 0xb669f4: ldur            x0, [fp, #-0x10]
    // 0xb669f8: StoreField: r2->field_b = r0
    //     0xb669f8: stur            w0, [x2, #0xb]
    // 0xb669fc: ldur            x0, [fp, #-0x28]
    // 0xb66a00: LoadField: r1 = r0->field_b
    //     0xb66a00: ldur            w1, [x0, #0xb]
    // 0xb66a04: LoadField: r3 = r0->field_f
    //     0xb66a04: ldur            w3, [x0, #0xf]
    // 0xb66a08: DecompressPointer r3
    //     0xb66a08: add             x3, x3, HEAP, lsl #32
    // 0xb66a0c: LoadField: r4 = r3->field_b
    //     0xb66a0c: ldur            w4, [x3, #0xb]
    // 0xb66a10: r3 = LoadInt32Instr(r1)
    //     0xb66a10: sbfx            x3, x1, #1, #0x1f
    // 0xb66a14: stur            x3, [fp, #-0x58]
    // 0xb66a18: r1 = LoadInt32Instr(r4)
    //     0xb66a18: sbfx            x1, x4, #1, #0x1f
    // 0xb66a1c: cmp             x3, x1
    // 0xb66a20: b.ne            #0xb66a2c
    // 0xb66a24: mov             x1, x0
    // 0xb66a28: r0 = _growToNextCapacity()
    //     0xb66a28: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb66a2c: ldur            x2, [fp, #-0x28]
    // 0xb66a30: ldur            x3, [fp, #-0x58]
    // 0xb66a34: add             x0, x3, #1
    // 0xb66a38: lsl             x1, x0, #1
    // 0xb66a3c: StoreField: r2->field_b = r1
    //     0xb66a3c: stur            w1, [x2, #0xb]
    // 0xb66a40: LoadField: r1 = r2->field_f
    //     0xb66a40: ldur            w1, [x2, #0xf]
    // 0xb66a44: DecompressPointer r1
    //     0xb66a44: add             x1, x1, HEAP, lsl #32
    // 0xb66a48: ldur            x0, [fp, #-0x20]
    // 0xb66a4c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb66a4c: add             x25, x1, x3, lsl #2
    //     0xb66a50: add             x25, x25, #0xf
    //     0xb66a54: str             w0, [x25]
    //     0xb66a58: tbz             w0, #0, #0xb66a74
    //     0xb66a5c: ldurb           w16, [x1, #-1]
    //     0xb66a60: ldurb           w17, [x0, #-1]
    //     0xb66a64: and             x16, x17, x16, lsr #2
    //     0xb66a68: tst             x16, HEAP, lsr #32
    //     0xb66a6c: b.eq            #0xb66a74
    //     0xb66a70: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb66a74: b               #0xb66a7c
    // 0xb66a78: ldur            x2, [fp, #-0x28]
    // 0xb66a7c: LoadField: r0 = r2->field_b
    //     0xb66a7c: ldur            w0, [x2, #0xb]
    // 0xb66a80: LoadField: r1 = r2->field_f
    //     0xb66a80: ldur            w1, [x2, #0xf]
    // 0xb66a84: DecompressPointer r1
    //     0xb66a84: add             x1, x1, HEAP, lsl #32
    // 0xb66a88: LoadField: r3 = r1->field_b
    //     0xb66a88: ldur            w3, [x1, #0xb]
    // 0xb66a8c: r4 = LoadInt32Instr(r0)
    //     0xb66a8c: sbfx            x4, x0, #1, #0x1f
    // 0xb66a90: stur            x4, [fp, #-0x58]
    // 0xb66a94: r0 = LoadInt32Instr(r3)
    //     0xb66a94: sbfx            x0, x3, #1, #0x1f
    // 0xb66a98: cmp             x4, x0
    // 0xb66a9c: b.ne            #0xb66aa8
    // 0xb66aa0: mov             x1, x2
    // 0xb66aa4: r0 = _growToNextCapacity()
    //     0xb66aa4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb66aa8: ldur            x0, [fp, #-0x28]
    // 0xb66aac: ldur            x1, [fp, #-0x58]
    // 0xb66ab0: add             x2, x1, #1
    // 0xb66ab4: lsl             x3, x2, #1
    // 0xb66ab8: StoreField: r0->field_b = r3
    //     0xb66ab8: stur            w3, [x0, #0xb]
    // 0xb66abc: LoadField: r2 = r0->field_f
    //     0xb66abc: ldur            w2, [x0, #0xf]
    // 0xb66ac0: DecompressPointer r2
    //     0xb66ac0: add             x2, x2, HEAP, lsl #32
    // 0xb66ac4: add             x3, x2, x1, lsl #2
    // 0xb66ac8: r16 = Instance_SizedBox
    //     0xb66ac8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0xb66acc: ldr             x16, [x16, #0x8f0]
    // 0xb66ad0: StoreField: r3->field_f = r16
    //     0xb66ad0: stur            w16, [x3, #0xf]
    // 0xb66ad4: ldur            x1, [fp, #-8]
    // 0xb66ad8: r0 = _buildViewAllButton()
    //     0xb66ad8: bl              #0xb66c3c  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildViewAllButton
    // 0xb66adc: mov             x2, x0
    // 0xb66ae0: ldur            x0, [fp, #-0x28]
    // 0xb66ae4: stur            x2, [fp, #-8]
    // 0xb66ae8: LoadField: r1 = r0->field_b
    //     0xb66ae8: ldur            w1, [x0, #0xb]
    // 0xb66aec: LoadField: r3 = r0->field_f
    //     0xb66aec: ldur            w3, [x0, #0xf]
    // 0xb66af0: DecompressPointer r3
    //     0xb66af0: add             x3, x3, HEAP, lsl #32
    // 0xb66af4: LoadField: r4 = r3->field_b
    //     0xb66af4: ldur            w4, [x3, #0xb]
    // 0xb66af8: r3 = LoadInt32Instr(r1)
    //     0xb66af8: sbfx            x3, x1, #1, #0x1f
    // 0xb66afc: stur            x3, [fp, #-0x58]
    // 0xb66b00: r1 = LoadInt32Instr(r4)
    //     0xb66b00: sbfx            x1, x4, #1, #0x1f
    // 0xb66b04: cmp             x3, x1
    // 0xb66b08: b.ne            #0xb66b14
    // 0xb66b0c: mov             x1, x0
    // 0xb66b10: r0 = _growToNextCapacity()
    //     0xb66b10: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb66b14: ldur            x4, [fp, #-0x18]
    // 0xb66b18: ldur            x2, [fp, #-0x28]
    // 0xb66b1c: ldur            x3, [fp, #-0x58]
    // 0xb66b20: add             x0, x3, #1
    // 0xb66b24: lsl             x1, x0, #1
    // 0xb66b28: StoreField: r2->field_b = r1
    //     0xb66b28: stur            w1, [x2, #0xb]
    // 0xb66b2c: LoadField: r1 = r2->field_f
    //     0xb66b2c: ldur            w1, [x2, #0xf]
    // 0xb66b30: DecompressPointer r1
    //     0xb66b30: add             x1, x1, HEAP, lsl #32
    // 0xb66b34: ldur            x0, [fp, #-8]
    // 0xb66b38: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb66b38: add             x25, x1, x3, lsl #2
    //     0xb66b3c: add             x25, x25, #0xf
    //     0xb66b40: str             w0, [x25]
    //     0xb66b44: tbz             w0, #0, #0xb66b60
    //     0xb66b48: ldurb           w16, [x1, #-1]
    //     0xb66b4c: ldurb           w17, [x0, #-1]
    //     0xb66b50: and             x16, x17, x16, lsr #2
    //     0xb66b54: tst             x16, HEAP, lsr #32
    //     0xb66b58: b.eq            #0xb66b60
    //     0xb66b5c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb66b60: r0 = Column()
    //     0xb66b60: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb66b64: mov             x1, x0
    // 0xb66b68: r0 = Instance_Axis
    //     0xb66b68: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb66b6c: stur            x1, [fp, #-8]
    // 0xb66b70: StoreField: r1->field_f = r0
    //     0xb66b70: stur            w0, [x1, #0xf]
    // 0xb66b74: r0 = Instance_MainAxisAlignment
    //     0xb66b74: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb66b78: ldr             x0, [x0, #0xa08]
    // 0xb66b7c: StoreField: r1->field_13 = r0
    //     0xb66b7c: stur            w0, [x1, #0x13]
    // 0xb66b80: r0 = Instance_MainAxisSize
    //     0xb66b80: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb66b84: ldr             x0, [x0, #0xa10]
    // 0xb66b88: ArrayStore: r1[0] = r0  ; List_4
    //     0xb66b88: stur            w0, [x1, #0x17]
    // 0xb66b8c: ldur            x0, [fp, #-0x18]
    // 0xb66b90: StoreField: r1->field_1b = r0
    //     0xb66b90: stur            w0, [x1, #0x1b]
    // 0xb66b94: r0 = Instance_VerticalDirection
    //     0xb66b94: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb66b98: ldr             x0, [x0, #0xa20]
    // 0xb66b9c: StoreField: r1->field_23 = r0
    //     0xb66b9c: stur            w0, [x1, #0x23]
    // 0xb66ba0: r0 = Instance_Clip
    //     0xb66ba0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb66ba4: ldr             x0, [x0, #0x38]
    // 0xb66ba8: StoreField: r1->field_2b = r0
    //     0xb66ba8: stur            w0, [x1, #0x2b]
    // 0xb66bac: StoreField: r1->field_2f = rZR
    //     0xb66bac: stur            xzr, [x1, #0x2f]
    // 0xb66bb0: ldur            x0, [fp, #-0x28]
    // 0xb66bb4: StoreField: r1->field_b = r0
    //     0xb66bb4: stur            w0, [x1, #0xb]
    // 0xb66bb8: r0 = Padding()
    //     0xb66bb8: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb66bbc: r1 = Instance_EdgeInsets
    //     0xb66bbc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3edd8] Obj!EdgeInsets@d58f41
    //     0xb66bc0: ldr             x1, [x1, #0xdd8]
    // 0xb66bc4: StoreField: r0->field_f = r1
    //     0xb66bc4: stur            w1, [x0, #0xf]
    // 0xb66bc8: ldur            x1, [fp, #-8]
    // 0xb66bcc: StoreField: r0->field_b = r1
    //     0xb66bcc: stur            w1, [x0, #0xb]
    // 0xb66bd0: LeaveFrame
    //     0xb66bd0: mov             SP, fp
    //     0xb66bd4: ldp             fp, lr, [SP], #0x10
    // 0xb66bd8: ret
    //     0xb66bd8: ret             
    // 0xb66bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb66bdc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb66be0: b               #0xb66624
    // 0xb66be4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66be4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb66be8: r9 = _pageController
    //     0xb66be8: add             x9, PP, #0x55, lsl #12  ; [pp+0x55c68] Field <_ProductGroupCarouselItemViewState@1589000994._pageController@1589000994>: late (offset: 0x14)
    //     0xb66bec: ldr             x9, [x9, #0xc68]
    // 0xb66bf0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb66bf0: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb66bf4: SaveReg d0
    //     0xb66bf4: str             q0, [SP, #-0x10]!
    // 0xb66bf8: r0 = AllocateDouble()
    //     0xb66bf8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb66bfc: RestoreReg d0
    //     0xb66bfc: ldr             q0, [SP], #0x10
    // 0xb66c00: b               #0xb66820
    // 0xb66c04: SaveReg d0
    //     0xb66c04: str             q0, [SP, #-0x10]!
    // 0xb66c08: SaveReg r3
    //     0xb66c08: str             x3, [SP, #-8]!
    // 0xb66c0c: r0 = AllocateDouble()
    //     0xb66c0c: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb66c10: RestoreReg r3
    //     0xb66c10: ldr             x3, [SP], #8
    // 0xb66c14: RestoreReg d0
    //     0xb66c14: ldr             q0, [SP], #0x10
    // 0xb66c18: b               #0xb66864
  }
  _ _buildViewAllButton(/* No info */) {
    // ** addr: 0xb66c3c, size: 0x288
    // 0xb66c3c: EnterFrame
    //     0xb66c3c: stp             fp, lr, [SP, #-0x10]!
    //     0xb66c40: mov             fp, SP
    // 0xb66c44: AllocStack(0x48)
    //     0xb66c44: sub             SP, SP, #0x48
    // 0xb66c48: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */)
    //     0xb66c48: stur            x1, [fp, #-8]
    // 0xb66c4c: CheckStackOverflow
    //     0xb66c4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb66c50: cmp             SP, x16
    //     0xb66c54: b.ls            #0xb66eac
    // 0xb66c58: r1 = 1
    //     0xb66c58: movz            x1, #0x1
    // 0xb66c5c: r0 = AllocateContext()
    //     0xb66c5c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb66c60: mov             x2, x0
    // 0xb66c64: ldur            x0, [fp, #-8]
    // 0xb66c68: stur            x2, [fp, #-0x10]
    // 0xb66c6c: StoreField: r2->field_f = r0
    //     0xb66c6c: stur            w0, [x2, #0xf]
    // 0xb66c70: LoadField: r1 = r0->field_b
    //     0xb66c70: ldur            w1, [x0, #0xb]
    // 0xb66c74: DecompressPointer r1
    //     0xb66c74: add             x1, x1, HEAP, lsl #32
    // 0xb66c78: cmp             w1, NULL
    // 0xb66c7c: b.eq            #0xb66eb4
    // 0xb66c80: LoadField: r3 = r1->field_13
    //     0xb66c80: ldur            w3, [x1, #0x13]
    // 0xb66c84: DecompressPointer r3
    //     0xb66c84: add             x3, x3, HEAP, lsl #32
    // 0xb66c88: cmp             w3, NULL
    // 0xb66c8c: b.ne            #0xb66c98
    // 0xb66c90: r1 = Null
    //     0xb66c90: mov             x1, NULL
    // 0xb66c94: b               #0xb66cc4
    // 0xb66c98: LoadField: r1 = r3->field_7
    //     0xb66c98: ldur            w1, [x3, #7]
    // 0xb66c9c: DecompressPointer r1
    //     0xb66c9c: add             x1, x1, HEAP, lsl #32
    // 0xb66ca0: cmp             w1, NULL
    // 0xb66ca4: b.ne            #0xb66cb0
    // 0xb66ca8: r1 = Null
    //     0xb66ca8: mov             x1, NULL
    // 0xb66cac: b               #0xb66cc4
    // 0xb66cb0: LoadField: r3 = r1->field_7
    //     0xb66cb0: ldur            w3, [x1, #7]
    // 0xb66cb4: cbz             w3, #0xb66cc0
    // 0xb66cb8: r1 = false
    //     0xb66cb8: add             x1, NULL, #0x30  ; false
    // 0xb66cbc: b               #0xb66cc4
    // 0xb66cc0: r1 = true
    //     0xb66cc0: add             x1, NULL, #0x20  ; true
    // 0xb66cc4: cmp             w1, NULL
    // 0xb66cc8: b.eq            #0xb66cd0
    // 0xb66ccc: tbnz            w1, #4, #0xb66ce0
    // 0xb66cd0: r0 = Instance_SizedBox
    //     0xb66cd0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb66cd4: LeaveFrame
    //     0xb66cd4: mov             SP, fp
    //     0xb66cd8: ldp             fp, lr, [SP], #0x10
    // 0xb66cdc: ret
    //     0xb66cdc: ret             
    // 0xb66ce0: LoadField: r1 = r0->field_f
    //     0xb66ce0: ldur            w1, [x0, #0xf]
    // 0xb66ce4: DecompressPointer r1
    //     0xb66ce4: add             x1, x1, HEAP, lsl #32
    // 0xb66ce8: cmp             w1, NULL
    // 0xb66cec: b.eq            #0xb66eb8
    // 0xb66cf0: r0 = of()
    //     0xb66cf0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb66cf4: LoadField: r1 = r0->field_5b
    //     0xb66cf4: ldur            w1, [x0, #0x5b]
    // 0xb66cf8: DecompressPointer r1
    //     0xb66cf8: add             x1, x1, HEAP, lsl #32
    // 0xb66cfc: stur            x1, [fp, #-0x18]
    // 0xb66d00: r0 = BoxDecoration()
    //     0xb66d00: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb66d04: mov             x2, x0
    // 0xb66d08: ldur            x0, [fp, #-0x18]
    // 0xb66d0c: stur            x2, [fp, #-0x20]
    // 0xb66d10: StoreField: r2->field_7 = r0
    //     0xb66d10: stur            w0, [x2, #7]
    // 0xb66d14: r0 = Instance_BorderRadius
    //     0xb66d14: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f460] Obj!BorderRadius@d5a321
    //     0xb66d18: ldr             x0, [x0, #0x460]
    // 0xb66d1c: StoreField: r2->field_13 = r0
    //     0xb66d1c: stur            w0, [x2, #0x13]
    // 0xb66d20: r0 = Instance_BoxShape
    //     0xb66d20: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb66d24: ldr             x0, [x0, #0x80]
    // 0xb66d28: StoreField: r2->field_23 = r0
    //     0xb66d28: stur            w0, [x2, #0x23]
    // 0xb66d2c: ldur            x1, [fp, #-8]
    // 0xb66d30: LoadField: r3 = r1->field_b
    //     0xb66d30: ldur            w3, [x1, #0xb]
    // 0xb66d34: DecompressPointer r3
    //     0xb66d34: add             x3, x3, HEAP, lsl #32
    // 0xb66d38: cmp             w3, NULL
    // 0xb66d3c: b.eq            #0xb66ebc
    // 0xb66d40: LoadField: r4 = r3->field_13
    //     0xb66d40: ldur            w4, [x3, #0x13]
    // 0xb66d44: DecompressPointer r4
    //     0xb66d44: add             x4, x4, HEAP, lsl #32
    // 0xb66d48: cmp             w4, NULL
    // 0xb66d4c: b.ne            #0xb66d58
    // 0xb66d50: r3 = Null
    //     0xb66d50: mov             x3, NULL
    // 0xb66d54: b               #0xb66d60
    // 0xb66d58: LoadField: r3 = r4->field_7
    //     0xb66d58: ldur            w3, [x4, #7]
    // 0xb66d5c: DecompressPointer r3
    //     0xb66d5c: add             x3, x3, HEAP, lsl #32
    // 0xb66d60: cmp             w3, NULL
    // 0xb66d64: b.ne            #0xb66d6c
    // 0xb66d68: r3 = ""
    //     0xb66d68: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb66d6c: stur            x3, [fp, #-0x18]
    // 0xb66d70: LoadField: r4 = r1->field_f
    //     0xb66d70: ldur            w4, [x1, #0xf]
    // 0xb66d74: DecompressPointer r4
    //     0xb66d74: add             x4, x4, HEAP, lsl #32
    // 0xb66d78: cmp             w4, NULL
    // 0xb66d7c: b.eq            #0xb66ec0
    // 0xb66d80: mov             x1, x4
    // 0xb66d84: r0 = of()
    //     0xb66d84: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb66d88: LoadField: r1 = r0->field_87
    //     0xb66d88: ldur            w1, [x0, #0x87]
    // 0xb66d8c: DecompressPointer r1
    //     0xb66d8c: add             x1, x1, HEAP, lsl #32
    // 0xb66d90: LoadField: r0 = r1->field_2b
    //     0xb66d90: ldur            w0, [x1, #0x2b]
    // 0xb66d94: DecompressPointer r0
    //     0xb66d94: add             x0, x0, HEAP, lsl #32
    // 0xb66d98: r16 = 16.000000
    //     0xb66d98: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb66d9c: ldr             x16, [x16, #0x188]
    // 0xb66da0: r30 = Instance_Color
    //     0xb66da0: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb66da4: stp             lr, x16, [SP]
    // 0xb66da8: mov             x1, x0
    // 0xb66dac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb66dac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb66db0: ldr             x4, [x4, #0xaa0]
    // 0xb66db4: r0 = copyWith()
    //     0xb66db4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb66db8: stur            x0, [fp, #-8]
    // 0xb66dbc: r0 = Text()
    //     0xb66dbc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb66dc0: mov             x1, x0
    // 0xb66dc4: ldur            x0, [fp, #-0x18]
    // 0xb66dc8: stur            x1, [fp, #-0x28]
    // 0xb66dcc: StoreField: r1->field_b = r0
    //     0xb66dcc: stur            w0, [x1, #0xb]
    // 0xb66dd0: ldur            x0, [fp, #-8]
    // 0xb66dd4: StoreField: r1->field_13 = r0
    //     0xb66dd4: stur            w0, [x1, #0x13]
    // 0xb66dd8: r0 = Center()
    //     0xb66dd8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb66ddc: mov             x1, x0
    // 0xb66de0: r0 = Instance_Alignment
    //     0xb66de0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb66de4: ldr             x0, [x0, #0xb10]
    // 0xb66de8: stur            x1, [fp, #-8]
    // 0xb66dec: StoreField: r1->field_f = r0
    //     0xb66dec: stur            w0, [x1, #0xf]
    // 0xb66df0: ldur            x0, [fp, #-0x28]
    // 0xb66df4: StoreField: r1->field_b = r0
    //     0xb66df4: stur            w0, [x1, #0xb]
    // 0xb66df8: r0 = Container()
    //     0xb66df8: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb66dfc: stur            x0, [fp, #-0x18]
    // 0xb66e00: r16 = 40.000000
    //     0xb66e00: add             x16, PP, #0x36, lsl #12  ; [pp+0x36008] 40
    //     0xb66e04: ldr             x16, [x16, #8]
    // 0xb66e08: r30 = 110.000000
    //     0xb66e08: add             lr, PP, #0x48, lsl #12  ; [pp+0x48770] 110
    //     0xb66e0c: ldr             lr, [lr, #0x770]
    // 0xb66e10: stp             lr, x16, [SP, #0x10]
    // 0xb66e14: ldur            x16, [fp, #-0x20]
    // 0xb66e18: ldur            lr, [fp, #-8]
    // 0xb66e1c: stp             lr, x16, [SP]
    // 0xb66e20: mov             x1, x0
    // 0xb66e24: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, width, 0x2, null]
    //     0xb66e24: add             x4, PP, #0x33, lsl #12  ; [pp+0x338c0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xb66e28: ldr             x4, [x4, #0x8c0]
    // 0xb66e2c: r0 = Container()
    //     0xb66e2c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb66e30: r0 = InkWell()
    //     0xb66e30: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb66e34: mov             x3, x0
    // 0xb66e38: ldur            x0, [fp, #-0x18]
    // 0xb66e3c: stur            x3, [fp, #-8]
    // 0xb66e40: StoreField: r3->field_b = r0
    //     0xb66e40: stur            w0, [x3, #0xb]
    // 0xb66e44: ldur            x2, [fp, #-0x10]
    // 0xb66e48: r1 = Function '<anonymous closure>':.
    //     0xb66e48: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d18] AnonymousClosure: (0xb66ec4), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildViewAllButton (0xb66c3c)
    //     0xb66e4c: ldr             x1, [x1, #0xd18]
    // 0xb66e50: r0 = AllocateClosure()
    //     0xb66e50: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb66e54: mov             x1, x0
    // 0xb66e58: ldur            x0, [fp, #-8]
    // 0xb66e5c: StoreField: r0->field_f = r1
    //     0xb66e5c: stur            w1, [x0, #0xf]
    // 0xb66e60: r1 = true
    //     0xb66e60: add             x1, NULL, #0x20  ; true
    // 0xb66e64: StoreField: r0->field_43 = r1
    //     0xb66e64: stur            w1, [x0, #0x43]
    // 0xb66e68: r2 = Instance_BoxShape
    //     0xb66e68: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb66e6c: ldr             x2, [x2, #0x80]
    // 0xb66e70: StoreField: r0->field_47 = r2
    //     0xb66e70: stur            w2, [x0, #0x47]
    // 0xb66e74: StoreField: r0->field_6f = r1
    //     0xb66e74: stur            w1, [x0, #0x6f]
    // 0xb66e78: r2 = false
    //     0xb66e78: add             x2, NULL, #0x30  ; false
    // 0xb66e7c: StoreField: r0->field_73 = r2
    //     0xb66e7c: stur            w2, [x0, #0x73]
    // 0xb66e80: StoreField: r0->field_83 = r1
    //     0xb66e80: stur            w1, [x0, #0x83]
    // 0xb66e84: StoreField: r0->field_7b = r2
    //     0xb66e84: stur            w2, [x0, #0x7b]
    // 0xb66e88: r0 = Padding()
    //     0xb66e88: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb66e8c: r1 = Instance_EdgeInsets
    //     0xb66e8c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fa00] Obj!EdgeInsets@d57681
    //     0xb66e90: ldr             x1, [x1, #0xa00]
    // 0xb66e94: StoreField: r0->field_f = r1
    //     0xb66e94: stur            w1, [x0, #0xf]
    // 0xb66e98: ldur            x1, [fp, #-8]
    // 0xb66e9c: StoreField: r0->field_b = r1
    //     0xb66e9c: stur            w1, [x0, #0xb]
    // 0xb66ea0: LeaveFrame
    //     0xb66ea0: mov             SP, fp
    //     0xb66ea4: ldp             fp, lr, [SP], #0x10
    // 0xb66ea8: ret
    //     0xb66ea8: ret             
    // 0xb66eac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb66eac: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb66eb0: b               #0xb66c58
    // 0xb66eb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66eb4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb66eb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66eb8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb66ebc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66ebc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb66ec0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66ec0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb66ec4, size: 0x48
    // 0xb66ec4: EnterFrame
    //     0xb66ec4: stp             fp, lr, [SP, #-0x10]!
    //     0xb66ec8: mov             fp, SP
    // 0xb66ecc: ldr             x0, [fp, #0x10]
    // 0xb66ed0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb66ed0: ldur            w1, [x0, #0x17]
    // 0xb66ed4: DecompressPointer r1
    //     0xb66ed4: add             x1, x1, HEAP, lsl #32
    // 0xb66ed8: CheckStackOverflow
    //     0xb66ed8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb66edc: cmp             SP, x16
    //     0xb66ee0: b.ls            #0xb66f04
    // 0xb66ee4: LoadField: r0 = r1->field_f
    //     0xb66ee4: ldur            w0, [x1, #0xf]
    // 0xb66ee8: DecompressPointer r0
    //     0xb66ee8: add             x0, x0, HEAP, lsl #32
    // 0xb66eec: mov             x1, x0
    // 0xb66ef0: r0 = _onViewAllTap()
    //     0xb66ef0: bl              #0xb66f0c  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onViewAllTap
    // 0xb66ef4: r0 = Null
    //     0xb66ef4: mov             x0, NULL
    // 0xb66ef8: LeaveFrame
    //     0xb66ef8: mov             SP, fp
    //     0xb66efc: ldp             fp, lr, [SP], #0x10
    // 0xb66f00: ret
    //     0xb66f00: ret             
    // 0xb66f04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb66f04: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb66f08: b               #0xb66ee4
  }
  _ _onViewAllTap(/* No info */) {
    // ** addr: 0xb66f0c, size: 0xe4
    // 0xb66f0c: EnterFrame
    //     0xb66f0c: stp             fp, lr, [SP, #-0x10]!
    //     0xb66f10: mov             fp, SP
    // 0xb66f14: AllocStack(0x30)
    //     0xb66f14: sub             SP, SP, #0x30
    // 0xb66f18: CheckStackOverflow
    //     0xb66f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb66f1c: cmp             SP, x16
    //     0xb66f20: b.ls            #0xb66fe4
    // 0xb66f24: LoadField: r0 = r1->field_b
    //     0xb66f24: ldur            w0, [x1, #0xb]
    // 0xb66f28: DecompressPointer r0
    //     0xb66f28: add             x0, x0, HEAP, lsl #32
    // 0xb66f2c: cmp             w0, NULL
    // 0xb66f30: b.eq            #0xb66fec
    // 0xb66f34: LoadField: r1 = r0->field_27
    //     0xb66f34: ldur            w1, [x0, #0x27]
    // 0xb66f38: DecompressPointer r1
    //     0xb66f38: add             x1, x1, HEAP, lsl #32
    // 0xb66f3c: cmp             w1, NULL
    // 0xb66f40: b.ne            #0xb66f48
    // 0xb66f44: r1 = ""
    //     0xb66f44: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb66f48: LoadField: r2 = r0->field_23
    //     0xb66f48: ldur            w2, [x0, #0x23]
    // 0xb66f4c: DecompressPointer r2
    //     0xb66f4c: add             x2, x2, HEAP, lsl #32
    // 0xb66f50: cmp             w2, NULL
    // 0xb66f54: b.ne            #0xb66f5c
    // 0xb66f58: r2 = ""
    //     0xb66f58: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb66f5c: LoadField: r3 = r0->field_2f
    //     0xb66f5c: ldur            w3, [x0, #0x2f]
    // 0xb66f60: DecompressPointer r3
    //     0xb66f60: add             x3, x3, HEAP, lsl #32
    // 0xb66f64: cmp             w3, NULL
    // 0xb66f68: b.ne            #0xb66f70
    // 0xb66f6c: r3 = ""
    //     0xb66f6c: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb66f70: LoadField: r4 = r0->field_2b
    //     0xb66f70: ldur            w4, [x0, #0x2b]
    // 0xb66f74: DecompressPointer r4
    //     0xb66f74: add             x4, x4, HEAP, lsl #32
    // 0xb66f78: cmp             w4, NULL
    // 0xb66f7c: b.ne            #0xb66f84
    // 0xb66f80: r4 = ""
    //     0xb66f80: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb66f84: LoadField: r5 = r0->field_13
    //     0xb66f84: ldur            w5, [x0, #0x13]
    // 0xb66f88: DecompressPointer r5
    //     0xb66f88: add             x5, x5, HEAP, lsl #32
    // 0xb66f8c: cmp             w5, NULL
    // 0xb66f90: b.ne            #0xb66f9c
    // 0xb66f94: r5 = Null
    //     0xb66f94: mov             x5, NULL
    // 0xb66f98: b               #0xb66fa8
    // 0xb66f9c: LoadField: r6 = r5->field_b
    //     0xb66f9c: ldur            w6, [x5, #0xb]
    // 0xb66fa0: DecompressPointer r6
    //     0xb66fa0: add             x6, x6, HEAP, lsl #32
    // 0xb66fa4: mov             x5, x6
    // 0xb66fa8: LoadField: r6 = r0->field_3f
    //     0xb66fa8: ldur            w6, [x0, #0x3f]
    // 0xb66fac: DecompressPointer r6
    //     0xb66fac: add             x6, x6, HEAP, lsl #32
    // 0xb66fb0: stp             x1, x6, [SP, #0x20]
    // 0xb66fb4: stp             x3, x2, [SP, #0x10]
    // 0xb66fb8: stp             x5, x4, [SP]
    // 0xb66fbc: r4 = 0
    //     0xb66fbc: movz            x4, #0
    // 0xb66fc0: ldr             x0, [SP, #0x28]
    // 0xb66fc4: r16 = UnlinkedCall_0x613b5c
    //     0xb66fc4: add             x16, PP, #0x55, lsl #12  ; [pp+0x55d20] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb66fc8: add             x16, x16, #0xd20
    // 0xb66fcc: ldp             x5, lr, [x16]
    // 0xb66fd0: blr             lr
    // 0xb66fd4: r0 = Null
    //     0xb66fd4: mov             x0, NULL
    // 0xb66fd8: LeaveFrame
    //     0xb66fd8: mov             SP, fp
    //     0xb66fdc: ldp             fp, lr, [SP], #0x10
    // 0xb66fe0: ret
    //     0xb66fe0: ret             
    // 0xb66fe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb66fe4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb66fe8: b               #0xb66f24
    // 0xb66fec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb66fec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildBumperCoupon(/* No info */) {
    // ** addr: 0xb66ff0, size: 0x800
    // 0xb66ff0: EnterFrame
    //     0xb66ff0: stp             fp, lr, [SP, #-0x10]!
    //     0xb66ff4: mov             fp, SP
    // 0xb66ff8: AllocStack(0x60)
    //     0xb66ff8: sub             SP, SP, #0x60
    // 0xb66ffc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x28 */)
    //     0xb66ffc: stur            x1, [fp, #-0x28]
    // 0xb67000: CheckStackOverflow
    //     0xb67000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb67004: cmp             SP, x16
    //     0xb67008: b.ls            #0xb677d0
    // 0xb6700c: LoadField: r0 = r1->field_b
    //     0xb6700c: ldur            w0, [x1, #0xb]
    // 0xb67010: DecompressPointer r0
    //     0xb67010: add             x0, x0, HEAP, lsl #32
    // 0xb67014: cmp             w0, NULL
    // 0xb67018: b.eq            #0xb677d8
    // 0xb6701c: LoadField: r2 = r0->field_33
    //     0xb6701c: ldur            w2, [x0, #0x33]
    // 0xb67020: DecompressPointer r2
    //     0xb67020: add             x2, x2, HEAP, lsl #32
    // 0xb67024: cmp             w2, NULL
    // 0xb67028: b.eq            #0xb6703c
    // 0xb6702c: LoadField: r0 = r2->field_f
    //     0xb6702c: ldur            w0, [x2, #0xf]
    // 0xb67030: DecompressPointer r0
    //     0xb67030: add             x0, x0, HEAP, lsl #32
    // 0xb67034: cmp             w0, NULL
    // 0xb67038: b.ne            #0xb6704c
    // 0xb6703c: r0 = Instance_SizedBox
    //     0xb6703c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb67040: LeaveFrame
    //     0xb67040: mov             SP, fp
    //     0xb67044: ldp             fp, lr, [SP], #0x10
    // 0xb67048: ret
    //     0xb67048: ret             
    // 0xb6704c: LoadField: r0 = r2->field_13
    //     0xb6704c: ldur            w0, [x2, #0x13]
    // 0xb67050: DecompressPointer r0
    //     0xb67050: add             x0, x0, HEAP, lsl #32
    // 0xb67054: stur            x0, [fp, #-0x20]
    // 0xb67058: cmp             w0, NULL
    // 0xb6705c: b.ne            #0xb67068
    // 0xb67060: r2 = Null
    //     0xb67060: mov             x2, NULL
    // 0xb67064: b               #0xb67070
    // 0xb67068: LoadField: r2 = r0->field_7
    //     0xb67068: ldur            w2, [x0, #7]
    // 0xb6706c: DecompressPointer r2
    //     0xb6706c: add             x2, x2, HEAP, lsl #32
    // 0xb67070: cmp             w2, NULL
    // 0xb67074: b.ne            #0xb67080
    // 0xb67078: r2 = 0
    //     0xb67078: movz            x2, #0
    // 0xb6707c: b               #0xb67090
    // 0xb67080: r3 = LoadInt32Instr(r2)
    //     0xb67080: sbfx            x3, x2, #1, #0x1f
    //     0xb67084: tbz             w2, #0, #0xb6708c
    //     0xb67088: ldur            x3, [x2, #7]
    // 0xb6708c: mov             x2, x3
    // 0xb67090: stur            x2, [fp, #-0x18]
    // 0xb67094: cmp             w0, NULL
    // 0xb67098: b.ne            #0xb670a4
    // 0xb6709c: r3 = Null
    //     0xb6709c: mov             x3, NULL
    // 0xb670a0: b               #0xb670ac
    // 0xb670a4: LoadField: r3 = r0->field_b
    //     0xb670a4: ldur            w3, [x0, #0xb]
    // 0xb670a8: DecompressPointer r3
    //     0xb670a8: add             x3, x3, HEAP, lsl #32
    // 0xb670ac: cmp             w3, NULL
    // 0xb670b0: b.ne            #0xb670bc
    // 0xb670b4: r3 = 0
    //     0xb670b4: movz            x3, #0
    // 0xb670b8: b               #0xb670cc
    // 0xb670bc: r4 = LoadInt32Instr(r3)
    //     0xb670bc: sbfx            x4, x3, #1, #0x1f
    //     0xb670c0: tbz             w3, #0, #0xb670c8
    //     0xb670c4: ldur            x4, [x3, #7]
    // 0xb670c8: mov             x3, x4
    // 0xb670cc: stur            x3, [fp, #-0x10]
    // 0xb670d0: cmp             w0, NULL
    // 0xb670d4: b.ne            #0xb670e0
    // 0xb670d8: r4 = Null
    //     0xb670d8: mov             x4, NULL
    // 0xb670dc: b               #0xb670e8
    // 0xb670e0: LoadField: r4 = r0->field_f
    //     0xb670e0: ldur            w4, [x0, #0xf]
    // 0xb670e4: DecompressPointer r4
    //     0xb670e4: add             x4, x4, HEAP, lsl #32
    // 0xb670e8: cmp             w4, NULL
    // 0xb670ec: b.ne            #0xb670f8
    // 0xb670f0: r4 = 0
    //     0xb670f0: movz            x4, #0
    // 0xb670f4: b               #0xb67108
    // 0xb670f8: r5 = LoadInt32Instr(r4)
    //     0xb670f8: sbfx            x5, x4, #1, #0x1f
    //     0xb670fc: tbz             w4, #0, #0xb67104
    //     0xb67100: ldur            x5, [x4, #7]
    // 0xb67104: mov             x4, x5
    // 0xb67108: stur            x4, [fp, #-8]
    // 0xb6710c: r0 = Color()
    //     0xb6710c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb67110: mov             x1, x0
    // 0xb67114: r0 = Instance_ColorSpace
    //     0xb67114: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb67118: stur            x1, [fp, #-0x30]
    // 0xb6711c: StoreField: r1->field_27 = r0
    //     0xb6711c: stur            w0, [x1, #0x27]
    // 0xb67120: d0 = 1.000000
    //     0xb67120: fmov            d0, #1.00000000
    // 0xb67124: StoreField: r1->field_7 = d0
    //     0xb67124: stur            d0, [x1, #7]
    // 0xb67128: ldur            x2, [fp, #-0x18]
    // 0xb6712c: ubfx            x2, x2, #0, #0x20
    // 0xb67130: and             w3, w2, #0xff
    // 0xb67134: ubfx            x3, x3, #0, #0x20
    // 0xb67138: scvtf           d0, x3
    // 0xb6713c: d1 = 255.000000
    //     0xb6713c: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb67140: fdiv            d2, d0, d1
    // 0xb67144: StoreField: r1->field_f = d2
    //     0xb67144: stur            d2, [x1, #0xf]
    // 0xb67148: ldur            x2, [fp, #-0x10]
    // 0xb6714c: ubfx            x2, x2, #0, #0x20
    // 0xb67150: and             w3, w2, #0xff
    // 0xb67154: ubfx            x3, x3, #0, #0x20
    // 0xb67158: scvtf           d0, x3
    // 0xb6715c: fdiv            d2, d0, d1
    // 0xb67160: ArrayStore: r1[0] = d2  ; List_8
    //     0xb67160: stur            d2, [x1, #0x17]
    // 0xb67164: ldur            x2, [fp, #-8]
    // 0xb67168: ubfx            x2, x2, #0, #0x20
    // 0xb6716c: and             w3, w2, #0xff
    // 0xb67170: ubfx            x3, x3, #0, #0x20
    // 0xb67174: scvtf           d0, x3
    // 0xb67178: fdiv            d2, d0, d1
    // 0xb6717c: StoreField: r1->field_1f = d2
    //     0xb6717c: stur            d2, [x1, #0x1f]
    // 0xb67180: ldur            x2, [fp, #-0x20]
    // 0xb67184: cmp             w2, NULL
    // 0xb67188: b.ne            #0xb67194
    // 0xb6718c: r3 = Null
    //     0xb6718c: mov             x3, NULL
    // 0xb67190: b               #0xb6719c
    // 0xb67194: LoadField: r3 = r2->field_7
    //     0xb67194: ldur            w3, [x2, #7]
    // 0xb67198: DecompressPointer r3
    //     0xb67198: add             x3, x3, HEAP, lsl #32
    // 0xb6719c: cmp             w3, NULL
    // 0xb671a0: b.ne            #0xb671ac
    // 0xb671a4: r3 = 0
    //     0xb671a4: movz            x3, #0
    // 0xb671a8: b               #0xb671bc
    // 0xb671ac: r4 = LoadInt32Instr(r3)
    //     0xb671ac: sbfx            x4, x3, #1, #0x1f
    //     0xb671b0: tbz             w3, #0, #0xb671b8
    //     0xb671b4: ldur            x4, [x3, #7]
    // 0xb671b8: mov             x3, x4
    // 0xb671bc: stur            x3, [fp, #-0x18]
    // 0xb671c0: cmp             w2, NULL
    // 0xb671c4: b.ne            #0xb671d0
    // 0xb671c8: r4 = Null
    //     0xb671c8: mov             x4, NULL
    // 0xb671cc: b               #0xb671d8
    // 0xb671d0: LoadField: r4 = r2->field_b
    //     0xb671d0: ldur            w4, [x2, #0xb]
    // 0xb671d4: DecompressPointer r4
    //     0xb671d4: add             x4, x4, HEAP, lsl #32
    // 0xb671d8: cmp             w4, NULL
    // 0xb671dc: b.ne            #0xb671e8
    // 0xb671e0: r4 = 0
    //     0xb671e0: movz            x4, #0
    // 0xb671e4: b               #0xb671f8
    // 0xb671e8: r5 = LoadInt32Instr(r4)
    //     0xb671e8: sbfx            x5, x4, #1, #0x1f
    //     0xb671ec: tbz             w4, #0, #0xb671f4
    //     0xb671f0: ldur            x5, [x4, #7]
    // 0xb671f4: mov             x4, x5
    // 0xb671f8: stur            x4, [fp, #-0x10]
    // 0xb671fc: cmp             w2, NULL
    // 0xb67200: b.ne            #0xb6720c
    // 0xb67204: r2 = Null
    //     0xb67204: mov             x2, NULL
    // 0xb67208: b               #0xb67218
    // 0xb6720c: LoadField: r5 = r2->field_f
    //     0xb6720c: ldur            w5, [x2, #0xf]
    // 0xb67210: DecompressPointer r5
    //     0xb67210: add             x5, x5, HEAP, lsl #32
    // 0xb67214: mov             x2, x5
    // 0xb67218: cmp             w2, NULL
    // 0xb6721c: b.ne            #0xb67228
    // 0xb67220: r5 = 0
    //     0xb67220: movz            x5, #0
    // 0xb67224: b               #0xb67234
    // 0xb67228: r5 = LoadInt32Instr(r2)
    //     0xb67228: sbfx            x5, x2, #1, #0x1f
    //     0xb6722c: tbz             w2, #0, #0xb67234
    //     0xb67230: ldur            x5, [x2, #7]
    // 0xb67234: ldur            x2, [fp, #-0x28]
    // 0xb67238: stur            x5, [fp, #-8]
    // 0xb6723c: r0 = Color()
    //     0xb6723c: bl              #0x6b3f90  ; AllocateColorStub -> Color (size=0x2c)
    // 0xb67240: mov             x3, x0
    // 0xb67244: r0 = Instance_ColorSpace
    //     0xb67244: ldr             x0, [PP, #0x5778]  ; [pp+0x5778] Obj!ColorSpace@d76f21
    // 0xb67248: stur            x3, [fp, #-0x20]
    // 0xb6724c: StoreField: r3->field_27 = r0
    //     0xb6724c: stur            w0, [x3, #0x27]
    // 0xb67250: d0 = 0.700000
    //     0xb67250: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb67254: ldr             d0, [x17, #0xf48]
    // 0xb67258: StoreField: r3->field_7 = d0
    //     0xb67258: stur            d0, [x3, #7]
    // 0xb6725c: ldur            x0, [fp, #-0x18]
    // 0xb67260: ubfx            x0, x0, #0, #0x20
    // 0xb67264: and             w1, w0, #0xff
    // 0xb67268: ubfx            x1, x1, #0, #0x20
    // 0xb6726c: scvtf           d0, x1
    // 0xb67270: d1 = 255.000000
    //     0xb67270: ldr             d1, [PP, #0x28a8]  ; [pp+0x28a8] IMM: double(255) from 0x406fe00000000000
    // 0xb67274: fdiv            d2, d0, d1
    // 0xb67278: StoreField: r3->field_f = d2
    //     0xb67278: stur            d2, [x3, #0xf]
    // 0xb6727c: ldur            x0, [fp, #-0x10]
    // 0xb67280: ubfx            x0, x0, #0, #0x20
    // 0xb67284: and             w1, w0, #0xff
    // 0xb67288: ubfx            x1, x1, #0, #0x20
    // 0xb6728c: scvtf           d0, x1
    // 0xb67290: fdiv            d2, d0, d1
    // 0xb67294: ArrayStore: r3[0] = d2  ; List_8
    //     0xb67294: stur            d2, [x3, #0x17]
    // 0xb67298: ldur            x0, [fp, #-8]
    // 0xb6729c: ubfx            x0, x0, #0, #0x20
    // 0xb672a0: and             w1, w0, #0xff
    // 0xb672a4: ubfx            x1, x1, #0, #0x20
    // 0xb672a8: scvtf           d0, x1
    // 0xb672ac: fdiv            d2, d0, d1
    // 0xb672b0: StoreField: r3->field_1f = d2
    //     0xb672b0: stur            d2, [x3, #0x1f]
    // 0xb672b4: r1 = Null
    //     0xb672b4: mov             x1, NULL
    // 0xb672b8: r2 = 4
    //     0xb672b8: movz            x2, #0x4
    // 0xb672bc: r0 = AllocateArray()
    //     0xb672bc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb672c0: mov             x2, x0
    // 0xb672c4: ldur            x0, [fp, #-0x30]
    // 0xb672c8: stur            x2, [fp, #-0x38]
    // 0xb672cc: StoreField: r2->field_f = r0
    //     0xb672cc: stur            w0, [x2, #0xf]
    // 0xb672d0: ldur            x0, [fp, #-0x20]
    // 0xb672d4: StoreField: r2->field_13 = r0
    //     0xb672d4: stur            w0, [x2, #0x13]
    // 0xb672d8: r1 = <Color>
    //     0xb672d8: add             x1, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb672dc: ldr             x1, [x1, #0xf80]
    // 0xb672e0: r0 = AllocateGrowableArray()
    //     0xb672e0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb672e4: mov             x1, x0
    // 0xb672e8: ldur            x0, [fp, #-0x38]
    // 0xb672ec: stur            x1, [fp, #-0x20]
    // 0xb672f0: StoreField: r1->field_f = r0
    //     0xb672f0: stur            w0, [x1, #0xf]
    // 0xb672f4: r2 = 4
    //     0xb672f4: movz            x2, #0x4
    // 0xb672f8: StoreField: r1->field_b = r2
    //     0xb672f8: stur            w2, [x1, #0xb]
    // 0xb672fc: r0 = LinearGradient()
    //     0xb672fc: bl              #0x9906f4  ; AllocateLinearGradientStub -> LinearGradient (size=0x20)
    // 0xb67300: mov             x1, x0
    // 0xb67304: r0 = Instance_Alignment
    //     0xb67304: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce0] Obj!Alignment@d5a761
    //     0xb67308: ldr             x0, [x0, #0xce0]
    // 0xb6730c: stur            x1, [fp, #-0x30]
    // 0xb67310: StoreField: r1->field_13 = r0
    //     0xb67310: stur            w0, [x1, #0x13]
    // 0xb67314: r0 = Instance_Alignment
    //     0xb67314: add             x0, PP, #0x38, lsl #12  ; [pp+0x38ce8] Obj!Alignment@d5a7e1
    //     0xb67318: ldr             x0, [x0, #0xce8]
    // 0xb6731c: ArrayStore: r1[0] = r0  ; List_4
    //     0xb6731c: stur            w0, [x1, #0x17]
    // 0xb67320: r0 = Instance_TileMode
    //     0xb67320: add             x0, PP, #0x38, lsl #12  ; [pp+0x38cf0] Obj!TileMode@d76e41
    //     0xb67324: ldr             x0, [x0, #0xcf0]
    // 0xb67328: StoreField: r1->field_1b = r0
    //     0xb67328: stur            w0, [x1, #0x1b]
    // 0xb6732c: ldur            x0, [fp, #-0x20]
    // 0xb67330: StoreField: r1->field_7 = r0
    //     0xb67330: stur            w0, [x1, #7]
    // 0xb67334: r0 = BoxDecoration()
    //     0xb67334: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb67338: mov             x2, x0
    // 0xb6733c: r0 = Instance_BorderRadius
    //     0xb6733c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e10] Obj!BorderRadius@d5a1c1
    //     0xb67340: ldr             x0, [x0, #0xe10]
    // 0xb67344: stur            x2, [fp, #-0x20]
    // 0xb67348: StoreField: r2->field_13 = r0
    //     0xb67348: stur            w0, [x2, #0x13]
    // 0xb6734c: ldur            x0, [fp, #-0x30]
    // 0xb67350: StoreField: r2->field_1b = r0
    //     0xb67350: stur            w0, [x2, #0x1b]
    // 0xb67354: r0 = Instance_BoxShape
    //     0xb67354: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb67358: ldr             x0, [x0, #0x80]
    // 0xb6735c: StoreField: r2->field_23 = r0
    //     0xb6735c: stur            w0, [x2, #0x23]
    // 0xb67360: ldur            x0, [fp, #-0x28]
    // 0xb67364: LoadField: r1 = r0->field_f
    //     0xb67364: ldur            w1, [x0, #0xf]
    // 0xb67368: DecompressPointer r1
    //     0xb67368: add             x1, x1, HEAP, lsl #32
    // 0xb6736c: cmp             w1, NULL
    // 0xb67370: b.eq            #0xb677dc
    // 0xb67374: r0 = of()
    //     0xb67374: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb67378: LoadField: r1 = r0->field_87
    //     0xb67378: ldur            w1, [x0, #0x87]
    // 0xb6737c: DecompressPointer r1
    //     0xb6737c: add             x1, x1, HEAP, lsl #32
    // 0xb67380: LoadField: r0 = r1->field_7
    //     0xb67380: ldur            w0, [x1, #7]
    // 0xb67384: DecompressPointer r0
    //     0xb67384: add             x0, x0, HEAP, lsl #32
    // 0xb67388: r16 = 16.000000
    //     0xb67388: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb6738c: ldr             x16, [x16, #0x188]
    // 0xb67390: r30 = Instance_Color
    //     0xb67390: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb67394: stp             lr, x16, [SP]
    // 0xb67398: mov             x1, x0
    // 0xb6739c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6739c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb673a0: ldr             x4, [x4, #0xaa0]
    // 0xb673a4: r0 = copyWith()
    //     0xb673a4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb673a8: stur            x0, [fp, #-0x30]
    // 0xb673ac: r0 = TextSpan()
    //     0xb673ac: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb673b0: mov             x2, x0
    // 0xb673b4: r0 = "BUMPER OFFER\n"
    //     0xb673b4: add             x0, PP, #0x48, lsl #12  ; [pp+0x48338] "BUMPER OFFER\n"
    //     0xb673b8: ldr             x0, [x0, #0x338]
    // 0xb673bc: stur            x2, [fp, #-0x38]
    // 0xb673c0: StoreField: r2->field_b = r0
    //     0xb673c0: stur            w0, [x2, #0xb]
    // 0xb673c4: r0 = Instance__DeferringMouseCursor
    //     0xb673c4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb673c8: ArrayStore: r2[0] = r0  ; List_4
    //     0xb673c8: stur            w0, [x2, #0x17]
    // 0xb673cc: ldur            x1, [fp, #-0x30]
    // 0xb673d0: StoreField: r2->field_7 = r1
    //     0xb673d0: stur            w1, [x2, #7]
    // 0xb673d4: ldur            x3, [fp, #-0x28]
    // 0xb673d8: LoadField: r1 = r3->field_f
    //     0xb673d8: ldur            w1, [x3, #0xf]
    // 0xb673dc: DecompressPointer r1
    //     0xb673dc: add             x1, x1, HEAP, lsl #32
    // 0xb673e0: cmp             w1, NULL
    // 0xb673e4: b.eq            #0xb677e0
    // 0xb673e8: r0 = of()
    //     0xb673e8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb673ec: LoadField: r1 = r0->field_87
    //     0xb673ec: ldur            w1, [x0, #0x87]
    // 0xb673f0: DecompressPointer r1
    //     0xb673f0: add             x1, x1, HEAP, lsl #32
    // 0xb673f4: LoadField: r0 = r1->field_2b
    //     0xb673f4: ldur            w0, [x1, #0x2b]
    // 0xb673f8: DecompressPointer r0
    //     0xb673f8: add             x0, x0, HEAP, lsl #32
    // 0xb673fc: r16 = 12.000000
    //     0xb673fc: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb67400: ldr             x16, [x16, #0x9e8]
    // 0xb67404: r30 = Instance_Color
    //     0xb67404: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb67408: stp             lr, x16, [SP]
    // 0xb6740c: mov             x1, x0
    // 0xb67410: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb67410: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb67414: ldr             x4, [x4, #0xaa0]
    // 0xb67418: r0 = copyWith()
    //     0xb67418: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6741c: stur            x0, [fp, #-0x30]
    // 0xb67420: r0 = TextSpan()
    //     0xb67420: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb67424: mov             x3, x0
    // 0xb67428: r0 = "Unlocked from your last order"
    //     0xb67428: add             x0, PP, #0x48, lsl #12  ; [pp+0x48340] "Unlocked from your last order"
    //     0xb6742c: ldr             x0, [x0, #0x340]
    // 0xb67430: stur            x3, [fp, #-0x40]
    // 0xb67434: StoreField: r3->field_b = r0
    //     0xb67434: stur            w0, [x3, #0xb]
    // 0xb67438: r0 = Instance__DeferringMouseCursor
    //     0xb67438: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb6743c: ArrayStore: r3[0] = r0  ; List_4
    //     0xb6743c: stur            w0, [x3, #0x17]
    // 0xb67440: ldur            x1, [fp, #-0x30]
    // 0xb67444: StoreField: r3->field_7 = r1
    //     0xb67444: stur            w1, [x3, #7]
    // 0xb67448: r1 = Null
    //     0xb67448: mov             x1, NULL
    // 0xb6744c: r2 = 4
    //     0xb6744c: movz            x2, #0x4
    // 0xb67450: r0 = AllocateArray()
    //     0xb67450: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb67454: mov             x2, x0
    // 0xb67458: ldur            x0, [fp, #-0x38]
    // 0xb6745c: stur            x2, [fp, #-0x30]
    // 0xb67460: StoreField: r2->field_f = r0
    //     0xb67460: stur            w0, [x2, #0xf]
    // 0xb67464: ldur            x0, [fp, #-0x40]
    // 0xb67468: StoreField: r2->field_13 = r0
    //     0xb67468: stur            w0, [x2, #0x13]
    // 0xb6746c: r1 = <InlineSpan>
    //     0xb6746c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb67470: ldr             x1, [x1, #0xe40]
    // 0xb67474: r0 = AllocateGrowableArray()
    //     0xb67474: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb67478: mov             x1, x0
    // 0xb6747c: ldur            x0, [fp, #-0x30]
    // 0xb67480: stur            x1, [fp, #-0x38]
    // 0xb67484: StoreField: r1->field_f = r0
    //     0xb67484: stur            w0, [x1, #0xf]
    // 0xb67488: r2 = 4
    //     0xb67488: movz            x2, #0x4
    // 0xb6748c: StoreField: r1->field_b = r2
    //     0xb6748c: stur            w2, [x1, #0xb]
    // 0xb67490: r0 = TextSpan()
    //     0xb67490: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb67494: mov             x1, x0
    // 0xb67498: ldur            x0, [fp, #-0x38]
    // 0xb6749c: stur            x1, [fp, #-0x30]
    // 0xb674a0: StoreField: r1->field_f = r0
    //     0xb674a0: stur            w0, [x1, #0xf]
    // 0xb674a4: r0 = Instance__DeferringMouseCursor
    //     0xb674a4: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb674a8: ArrayStore: r1[0] = r0  ; List_4
    //     0xb674a8: stur            w0, [x1, #0x17]
    // 0xb674ac: r0 = RichText()
    //     0xb674ac: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb674b0: mov             x1, x0
    // 0xb674b4: ldur            x2, [fp, #-0x30]
    // 0xb674b8: stur            x0, [fp, #-0x30]
    // 0xb674bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb674bc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb674c0: r0 = RichText()
    //     0xb674c0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb674c4: ldur            x0, [fp, #-0x28]
    // 0xb674c8: LoadField: r1 = r0->field_b
    //     0xb674c8: ldur            w1, [x0, #0xb]
    // 0xb674cc: DecompressPointer r1
    //     0xb674cc: add             x1, x1, HEAP, lsl #32
    // 0xb674d0: cmp             w1, NULL
    // 0xb674d4: b.eq            #0xb677e4
    // 0xb674d8: LoadField: r2 = r1->field_33
    //     0xb674d8: ldur            w2, [x1, #0x33]
    // 0xb674dc: DecompressPointer r2
    //     0xb674dc: add             x2, x2, HEAP, lsl #32
    // 0xb674e0: cmp             w2, NULL
    // 0xb674e4: b.ne            #0xb674f0
    // 0xb674e8: r4 = Null
    //     0xb674e8: mov             x4, NULL
    // 0xb674ec: b               #0xb674fc
    // 0xb674f0: LoadField: r1 = r2->field_7
    //     0xb674f0: ldur            w1, [x2, #7]
    // 0xb674f4: DecompressPointer r1
    //     0xb674f4: add             x1, x1, HEAP, lsl #32
    // 0xb674f8: mov             x4, x1
    // 0xb674fc: ldur            x3, [fp, #-0x30]
    // 0xb67500: stur            x4, [fp, #-0x38]
    // 0xb67504: r1 = Null
    //     0xb67504: mov             x1, NULL
    // 0xb67508: r2 = 4
    //     0xb67508: movz            x2, #0x4
    // 0xb6750c: r0 = AllocateArray()
    //     0xb6750c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb67510: mov             x1, x0
    // 0xb67514: ldur            x0, [fp, #-0x38]
    // 0xb67518: StoreField: r1->field_f = r0
    //     0xb67518: stur            w0, [x1, #0xf]
    // 0xb6751c: r16 = "\n"
    //     0xb6751c: ldr             x16, [PP, #0x8a0]  ; [pp+0x8a0] "\n"
    // 0xb67520: StoreField: r1->field_13 = r16
    //     0xb67520: stur            w16, [x1, #0x13]
    // 0xb67524: str             x1, [SP]
    // 0xb67528: r0 = _interpolate()
    //     0xb67528: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb6752c: mov             x2, x0
    // 0xb67530: ldur            x0, [fp, #-0x28]
    // 0xb67534: stur            x2, [fp, #-0x38]
    // 0xb67538: LoadField: r1 = r0->field_f
    //     0xb67538: ldur            w1, [x0, #0xf]
    // 0xb6753c: DecompressPointer r1
    //     0xb6753c: add             x1, x1, HEAP, lsl #32
    // 0xb67540: cmp             w1, NULL
    // 0xb67544: b.eq            #0xb677e8
    // 0xb67548: r0 = of()
    //     0xb67548: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6754c: LoadField: r1 = r0->field_87
    //     0xb6754c: ldur            w1, [x0, #0x87]
    // 0xb67550: DecompressPointer r1
    //     0xb67550: add             x1, x1, HEAP, lsl #32
    // 0xb67554: LoadField: r0 = r1->field_23
    //     0xb67554: ldur            w0, [x1, #0x23]
    // 0xb67558: DecompressPointer r0
    //     0xb67558: add             x0, x0, HEAP, lsl #32
    // 0xb6755c: r16 = 32.000000
    //     0xb6755c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb67560: ldr             x16, [x16, #0x848]
    // 0xb67564: r30 = Instance_Color
    //     0xb67564: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb67568: stp             lr, x16, [SP]
    // 0xb6756c: mov             x1, x0
    // 0xb67570: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb67570: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb67574: ldr             x4, [x4, #0xaa0]
    // 0xb67578: r0 = copyWith()
    //     0xb67578: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6757c: mov             x2, x0
    // 0xb67580: ldur            x0, [fp, #-0x28]
    // 0xb67584: stur            x2, [fp, #-0x40]
    // 0xb67588: LoadField: r1 = r0->field_f
    //     0xb67588: ldur            w1, [x0, #0xf]
    // 0xb6758c: DecompressPointer r1
    //     0xb6758c: add             x1, x1, HEAP, lsl #32
    // 0xb67590: cmp             w1, NULL
    // 0xb67594: b.eq            #0xb677ec
    // 0xb67598: r0 = of()
    //     0xb67598: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6759c: LoadField: r1 = r0->field_87
    //     0xb6759c: ldur            w1, [x0, #0x87]
    // 0xb675a0: DecompressPointer r1
    //     0xb675a0: add             x1, x1, HEAP, lsl #32
    // 0xb675a4: LoadField: r0 = r1->field_2b
    //     0xb675a4: ldur            w0, [x1, #0x2b]
    // 0xb675a8: DecompressPointer r0
    //     0xb675a8: add             x0, x0, HEAP, lsl #32
    // 0xb675ac: r16 = Instance_Color
    //     0xb675ac: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb675b0: r30 = 16.000000
    //     0xb675b0: add             lr, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0xb675b4: ldr             lr, [lr, #0x188]
    // 0xb675b8: stp             lr, x16, [SP]
    // 0xb675bc: mov             x1, x0
    // 0xb675c0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0xb675c0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0xb675c4: ldr             x4, [x4, #0x9b8]
    // 0xb675c8: r0 = copyWith()
    //     0xb675c8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb675cc: stur            x0, [fp, #-0x28]
    // 0xb675d0: r0 = TextSpan()
    //     0xb675d0: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb675d4: mov             x3, x0
    // 0xb675d8: r0 = "OFF"
    //     0xb675d8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48348] "OFF"
    //     0xb675dc: ldr             x0, [x0, #0x348]
    // 0xb675e0: stur            x3, [fp, #-0x48]
    // 0xb675e4: StoreField: r3->field_b = r0
    //     0xb675e4: stur            w0, [x3, #0xb]
    // 0xb675e8: r0 = Instance__DeferringMouseCursor
    //     0xb675e8: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb675ec: ArrayStore: r3[0] = r0  ; List_4
    //     0xb675ec: stur            w0, [x3, #0x17]
    // 0xb675f0: ldur            x1, [fp, #-0x28]
    // 0xb675f4: StoreField: r3->field_7 = r1
    //     0xb675f4: stur            w1, [x3, #7]
    // 0xb675f8: r1 = Null
    //     0xb675f8: mov             x1, NULL
    // 0xb675fc: r2 = 2
    //     0xb675fc: movz            x2, #0x2
    // 0xb67600: r0 = AllocateArray()
    //     0xb67600: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb67604: mov             x2, x0
    // 0xb67608: ldur            x0, [fp, #-0x48]
    // 0xb6760c: stur            x2, [fp, #-0x28]
    // 0xb67610: StoreField: r2->field_f = r0
    //     0xb67610: stur            w0, [x2, #0xf]
    // 0xb67614: r1 = <InlineSpan>
    //     0xb67614: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb67618: ldr             x1, [x1, #0xe40]
    // 0xb6761c: r0 = AllocateGrowableArray()
    //     0xb6761c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb67620: mov             x1, x0
    // 0xb67624: ldur            x0, [fp, #-0x28]
    // 0xb67628: stur            x1, [fp, #-0x48]
    // 0xb6762c: StoreField: r1->field_f = r0
    //     0xb6762c: stur            w0, [x1, #0xf]
    // 0xb67630: r0 = 2
    //     0xb67630: movz            x0, #0x2
    // 0xb67634: StoreField: r1->field_b = r0
    //     0xb67634: stur            w0, [x1, #0xb]
    // 0xb67638: r0 = TextSpan()
    //     0xb67638: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb6763c: mov             x1, x0
    // 0xb67640: ldur            x0, [fp, #-0x38]
    // 0xb67644: stur            x1, [fp, #-0x28]
    // 0xb67648: StoreField: r1->field_b = r0
    //     0xb67648: stur            w0, [x1, #0xb]
    // 0xb6764c: ldur            x0, [fp, #-0x48]
    // 0xb67650: StoreField: r1->field_f = r0
    //     0xb67650: stur            w0, [x1, #0xf]
    // 0xb67654: r0 = Instance__DeferringMouseCursor
    //     0xb67654: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb67658: ArrayStore: r1[0] = r0  ; List_4
    //     0xb67658: stur            w0, [x1, #0x17]
    // 0xb6765c: ldur            x0, [fp, #-0x40]
    // 0xb67660: StoreField: r1->field_7 = r0
    //     0xb67660: stur            w0, [x1, #7]
    // 0xb67664: r0 = RichText()
    //     0xb67664: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb67668: stur            x0, [fp, #-0x38]
    // 0xb6766c: r16 = Instance_TextAlign
    //     0xb6766c: ldr             x16, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb67670: str             x16, [SP]
    // 0xb67674: mov             x1, x0
    // 0xb67678: ldur            x2, [fp, #-0x28]
    // 0xb6767c: r4 = const [0, 0x3, 0x1, 0x2, textAlign, 0x2, null]
    //     0xb6767c: add             x4, PP, #0x48, lsl #12  ; [pp+0x48350] List(7) [0, 0x3, 0x1, 0x2, "textAlign", 0x2, Null]
    //     0xb67680: ldr             x4, [x4, #0x350]
    // 0xb67684: r0 = RichText()
    //     0xb67684: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb67688: r1 = Null
    //     0xb67688: mov             x1, NULL
    // 0xb6768c: r2 = 6
    //     0xb6768c: movz            x2, #0x6
    // 0xb67690: r0 = AllocateArray()
    //     0xb67690: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb67694: mov             x2, x0
    // 0xb67698: ldur            x0, [fp, #-0x30]
    // 0xb6769c: stur            x2, [fp, #-0x28]
    // 0xb676a0: StoreField: r2->field_f = r0
    //     0xb676a0: stur            w0, [x2, #0xf]
    // 0xb676a4: r16 = Instance_VerticalDivider
    //     0xb676a4: add             x16, PP, #0x48, lsl #12  ; [pp+0x48760] Obj!VerticalDivider@d66b51
    //     0xb676a8: ldr             x16, [x16, #0x760]
    // 0xb676ac: StoreField: r2->field_13 = r16
    //     0xb676ac: stur            w16, [x2, #0x13]
    // 0xb676b0: ldur            x0, [fp, #-0x38]
    // 0xb676b4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb676b4: stur            w0, [x2, #0x17]
    // 0xb676b8: r1 = <Widget>
    //     0xb676b8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb676bc: r0 = AllocateGrowableArray()
    //     0xb676bc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb676c0: mov             x1, x0
    // 0xb676c4: ldur            x0, [fp, #-0x28]
    // 0xb676c8: stur            x1, [fp, #-0x30]
    // 0xb676cc: StoreField: r1->field_f = r0
    //     0xb676cc: stur            w0, [x1, #0xf]
    // 0xb676d0: r0 = 6
    //     0xb676d0: movz            x0, #0x6
    // 0xb676d4: StoreField: r1->field_b = r0
    //     0xb676d4: stur            w0, [x1, #0xb]
    // 0xb676d8: r0 = Row()
    //     0xb676d8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb676dc: mov             x1, x0
    // 0xb676e0: r0 = Instance_Axis
    //     0xb676e0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb676e4: stur            x1, [fp, #-0x28]
    // 0xb676e8: StoreField: r1->field_f = r0
    //     0xb676e8: stur            w0, [x1, #0xf]
    // 0xb676ec: r0 = Instance_MainAxisAlignment
    //     0xb676ec: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb676f0: ldr             x0, [x0, #0xa8]
    // 0xb676f4: StoreField: r1->field_13 = r0
    //     0xb676f4: stur            w0, [x1, #0x13]
    // 0xb676f8: r0 = Instance_MainAxisSize
    //     0xb676f8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb676fc: ldr             x0, [x0, #0xa10]
    // 0xb67700: ArrayStore: r1[0] = r0  ; List_4
    //     0xb67700: stur            w0, [x1, #0x17]
    // 0xb67704: r0 = Instance_CrossAxisAlignment
    //     0xb67704: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb67708: ldr             x0, [x0, #0xa18]
    // 0xb6770c: StoreField: r1->field_1b = r0
    //     0xb6770c: stur            w0, [x1, #0x1b]
    // 0xb67710: r0 = Instance_VerticalDirection
    //     0xb67710: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb67714: ldr             x0, [x0, #0xa20]
    // 0xb67718: StoreField: r1->field_23 = r0
    //     0xb67718: stur            w0, [x1, #0x23]
    // 0xb6771c: r0 = Instance_Clip
    //     0xb6771c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb67720: ldr             x0, [x0, #0x38]
    // 0xb67724: StoreField: r1->field_2b = r0
    //     0xb67724: stur            w0, [x1, #0x2b]
    // 0xb67728: StoreField: r1->field_2f = rZR
    //     0xb67728: stur            xzr, [x1, #0x2f]
    // 0xb6772c: ldur            x0, [fp, #-0x30]
    // 0xb67730: StoreField: r1->field_b = r0
    //     0xb67730: stur            w0, [x1, #0xb]
    // 0xb67734: r0 = Padding()
    //     0xb67734: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb67738: mov             x1, x0
    // 0xb6773c: r0 = Instance_EdgeInsets
    //     0xb6773c: add             x0, PP, #0x48, lsl #12  ; [pp+0x48768] Obj!EdgeInsets@d585b1
    //     0xb67740: ldr             x0, [x0, #0x768]
    // 0xb67744: stur            x1, [fp, #-0x30]
    // 0xb67748: StoreField: r1->field_f = r0
    //     0xb67748: stur            w0, [x1, #0xf]
    // 0xb6774c: ldur            x0, [fp, #-0x28]
    // 0xb67750: StoreField: r1->field_b = r0
    //     0xb67750: stur            w0, [x1, #0xb]
    // 0xb67754: r0 = IntrinsicHeight()
    //     0xb67754: bl              #0x9906e8  ; AllocateIntrinsicHeightStub -> IntrinsicHeight (size=0x10)
    // 0xb67758: mov             x1, x0
    // 0xb6775c: ldur            x0, [fp, #-0x30]
    // 0xb67760: stur            x1, [fp, #-0x28]
    // 0xb67764: StoreField: r1->field_b = r0
    //     0xb67764: stur            w0, [x1, #0xb]
    // 0xb67768: r0 = Container()
    //     0xb67768: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb6776c: stur            x0, [fp, #-0x30]
    // 0xb67770: r16 = 100.000000
    //     0xb67770: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb67774: ldur            lr, [fp, #-0x20]
    // 0xb67778: stp             lr, x16, [SP, #8]
    // 0xb6777c: ldur            x16, [fp, #-0x28]
    // 0xb67780: str             x16, [SP]
    // 0xb67784: mov             x1, x0
    // 0xb67788: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xb67788: add             x4, PP, #0x32, lsl #12  ; [pp+0x32c78] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xb6778c: ldr             x4, [x4, #0xc78]
    // 0xb67790: r0 = Container()
    //     0xb67790: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb67794: r1 = <Path>
    //     0xb67794: add             x1, PP, #0x38, lsl #12  ; [pp+0x38d30] TypeArguments: <Path>
    //     0xb67798: ldr             x1, [x1, #0xd30]
    // 0xb6779c: r0 = MovieTicketClipper()
    //     0xb6779c: bl              #0x990650  ; AllocateMovieTicketClipperStub -> MovieTicketClipper (size=0x10)
    // 0xb677a0: stur            x0, [fp, #-0x20]
    // 0xb677a4: r0 = ClipPath()
    //     0xb677a4: bl              #0x990644  ; AllocateClipPathStub -> ClipPath (size=0x18)
    // 0xb677a8: ldur            x1, [fp, #-0x20]
    // 0xb677ac: StoreField: r0->field_f = r1
    //     0xb677ac: stur            w1, [x0, #0xf]
    // 0xb677b0: r1 = Instance_Clip
    //     0xb677b0: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0xb677b4: ldr             x1, [x1, #0x138]
    // 0xb677b8: StoreField: r0->field_13 = r1
    //     0xb677b8: stur            w1, [x0, #0x13]
    // 0xb677bc: ldur            x1, [fp, #-0x30]
    // 0xb677c0: StoreField: r0->field_b = r1
    //     0xb677c0: stur            w1, [x0, #0xb]
    // 0xb677c4: LeaveFrame
    //     0xb677c4: mov             SP, fp
    //     0xb677c8: ldp             fp, lr, [SP], #0x10
    // 0xb677cc: ret
    //     0xb677cc: ret             
    // 0xb677d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb677d0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb677d4: b               #0xb6700c
    // 0xb677d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb677d8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb677dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb677dc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb677e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb677e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb677e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb677e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb677e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb677e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb677ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb677ec: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildHeader(/* No info */) {
    // ** addr: 0xb677f0, size: 0x1bc
    // 0xb677f0: EnterFrame
    //     0xb677f0: stp             fp, lr, [SP, #-0x10]!
    //     0xb677f4: mov             fp, SP
    // 0xb677f8: AllocStack(0x30)
    //     0xb677f8: sub             SP, SP, #0x30
    // 0xb677fc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xb677fc: mov             x0, x1
    //     0xb67800: stur            x1, [fp, #-8]
    // 0xb67804: CheckStackOverflow
    //     0xb67804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb67808: cmp             SP, x16
    //     0xb6780c: b.ls            #0xb67998
    // 0xb67810: LoadField: r1 = r0->field_b
    //     0xb67810: ldur            w1, [x0, #0xb]
    // 0xb67814: DecompressPointer r1
    //     0xb67814: add             x1, x1, HEAP, lsl #32
    // 0xb67818: cmp             w1, NULL
    // 0xb6781c: b.eq            #0xb679a0
    // 0xb67820: LoadField: r2 = r1->field_f
    //     0xb67820: ldur            w2, [x1, #0xf]
    // 0xb67824: DecompressPointer r2
    //     0xb67824: add             x2, x2, HEAP, lsl #32
    // 0xb67828: cmp             w2, NULL
    // 0xb6782c: b.ne            #0xb67838
    // 0xb67830: r1 = Null
    //     0xb67830: mov             x1, NULL
    // 0xb67834: b               #0xb67850
    // 0xb67838: LoadField: r1 = r2->field_7
    //     0xb67838: ldur            w1, [x2, #7]
    // 0xb6783c: cbz             w1, #0xb67848
    // 0xb67840: r3 = false
    //     0xb67840: add             x3, NULL, #0x30  ; false
    // 0xb67844: b               #0xb6784c
    // 0xb67848: r3 = true
    //     0xb67848: add             x3, NULL, #0x20  ; true
    // 0xb6784c: mov             x1, x3
    // 0xb67850: cmp             w1, NULL
    // 0xb67854: b.eq            #0xb6785c
    // 0xb67858: tbnz            w1, #4, #0xb6786c
    // 0xb6785c: r0 = Instance_SizedBox
    //     0xb6785c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb67860: LeaveFrame
    //     0xb67860: mov             SP, fp
    //     0xb67864: ldp             fp, lr, [SP], #0x10
    // 0xb67868: ret
    //     0xb67868: ret             
    // 0xb6786c: cmp             w2, NULL
    // 0xb67870: b.ne            #0xb6787c
    // 0xb67874: r1 = ""
    //     0xb67874: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb67878: b               #0xb67880
    // 0xb6787c: mov             x1, x2
    // 0xb67880: r0 = capitalizeFirstWord()
    //     0xb67880: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb67884: mov             x2, x0
    // 0xb67888: ldur            x0, [fp, #-8]
    // 0xb6788c: stur            x2, [fp, #-0x18]
    // 0xb67890: LoadField: r1 = r0->field_b
    //     0xb67890: ldur            w1, [x0, #0xb]
    // 0xb67894: DecompressPointer r1
    //     0xb67894: add             x1, x1, HEAP, lsl #32
    // 0xb67898: cmp             w1, NULL
    // 0xb6789c: b.eq            #0xb679a4
    // 0xb678a0: LoadField: r3 = r1->field_1b
    //     0xb678a0: ldur            w3, [x1, #0x1b]
    // 0xb678a4: DecompressPointer r3
    //     0xb678a4: add             x3, x3, HEAP, lsl #32
    // 0xb678a8: LoadField: r1 = r3->field_7
    //     0xb678a8: ldur            w1, [x3, #7]
    // 0xb678ac: DecompressPointer r1
    //     0xb678ac: add             x1, x1, HEAP, lsl #32
    // 0xb678b0: cmp             w1, NULL
    // 0xb678b4: b.ne            #0xb678c0
    // 0xb678b8: r1 = Instance_TitleAlignment
    //     0xb678b8: add             x1, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb678bc: ldr             x1, [x1, #0x518]
    // 0xb678c0: r16 = Instance_TitleAlignment
    //     0xb678c0: add             x16, PP, #0x24, lsl #12  ; [pp+0x24520] Obj!TitleAlignment@d755c1
    //     0xb678c4: ldr             x16, [x16, #0x520]
    // 0xb678c8: cmp             w1, w16
    // 0xb678cc: b.ne            #0xb678d8
    // 0xb678d0: r3 = Instance_TextAlign
    //     0xb678d0: ldr             x3, [PP, #0x47a8]  ; [pp+0x47a8] Obj!TextAlign@d766e1
    // 0xb678d4: b               #0xb678f4
    // 0xb678d8: r16 = Instance_TitleAlignment
    //     0xb678d8: add             x16, PP, #0x24, lsl #12  ; [pp+0x24518] Obj!TitleAlignment@d755e1
    //     0xb678dc: ldr             x16, [x16, #0x518]
    // 0xb678e0: cmp             w1, w16
    // 0xb678e4: b.ne            #0xb678f0
    // 0xb678e8: r3 = Instance_TextAlign
    //     0xb678e8: ldr             x3, [PP, #0x4538]  ; [pp+0x4538] Obj!TextAlign@d76701
    // 0xb678ec: b               #0xb678f4
    // 0xb678f0: r3 = Instance_TextAlign
    //     0xb678f0: ldr             x3, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xb678f4: stur            x3, [fp, #-0x10]
    // 0xb678f8: LoadField: r1 = r0->field_f
    //     0xb678f8: ldur            w1, [x0, #0xf]
    // 0xb678fc: DecompressPointer r1
    //     0xb678fc: add             x1, x1, HEAP, lsl #32
    // 0xb67900: cmp             w1, NULL
    // 0xb67904: b.eq            #0xb679a8
    // 0xb67908: r0 = of()
    //     0xb67908: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6790c: LoadField: r1 = r0->field_87
    //     0xb6790c: ldur            w1, [x0, #0x87]
    // 0xb67910: DecompressPointer r1
    //     0xb67910: add             x1, x1, HEAP, lsl #32
    // 0xb67914: LoadField: r0 = r1->field_7
    //     0xb67914: ldur            w0, [x1, #7]
    // 0xb67918: DecompressPointer r0
    //     0xb67918: add             x0, x0, HEAP, lsl #32
    // 0xb6791c: stur            x0, [fp, #-8]
    // 0xb67920: r1 = Instance_Color
    //     0xb67920: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb67924: d0 = 0.700000
    //     0xb67924: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb67928: ldr             d0, [x17, #0xf48]
    // 0xb6792c: r0 = withOpacity()
    //     0xb6792c: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb67930: r16 = 32.000000
    //     0xb67930: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f848] 32
    //     0xb67934: ldr             x16, [x16, #0x848]
    // 0xb67938: stp             x0, x16, [SP]
    // 0xb6793c: ldur            x1, [fp, #-8]
    // 0xb67940: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb67940: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb67944: ldr             x4, [x4, #0xaa0]
    // 0xb67948: r0 = copyWith()
    //     0xb67948: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6794c: stur            x0, [fp, #-8]
    // 0xb67950: r0 = Text()
    //     0xb67950: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb67954: mov             x1, x0
    // 0xb67958: ldur            x0, [fp, #-0x18]
    // 0xb6795c: stur            x1, [fp, #-0x20]
    // 0xb67960: StoreField: r1->field_b = r0
    //     0xb67960: stur            w0, [x1, #0xb]
    // 0xb67964: ldur            x0, [fp, #-8]
    // 0xb67968: StoreField: r1->field_13 = r0
    //     0xb67968: stur            w0, [x1, #0x13]
    // 0xb6796c: ldur            x0, [fp, #-0x10]
    // 0xb67970: StoreField: r1->field_1b = r0
    //     0xb67970: stur            w0, [x1, #0x1b]
    // 0xb67974: r0 = Padding()
    //     0xb67974: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb67978: r1 = Instance_EdgeInsets
    //     0xb67978: add             x1, PP, #0x34, lsl #12  ; [pp+0x34100] Obj!EdgeInsets@d57621
    //     0xb6797c: ldr             x1, [x1, #0x100]
    // 0xb67980: StoreField: r0->field_f = r1
    //     0xb67980: stur            w1, [x0, #0xf]
    // 0xb67984: ldur            x1, [fp, #-0x20]
    // 0xb67988: StoreField: r0->field_b = r1
    //     0xb67988: stur            w1, [x0, #0xb]
    // 0xb6798c: LeaveFrame
    //     0xb6798c: mov             SP, fp
    //     0xb67990: ldp             fp, lr, [SP], #0x10
    // 0xb67994: ret
    //     0xb67994: ret             
    // 0xb67998: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb67998: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb6799c: b               #0xb67810
    // 0xb679a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb679a0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb679a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb679a4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb679a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb679a8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb679ac, size: 0x300
    // 0xb679ac: EnterFrame
    //     0xb679ac: stp             fp, lr, [SP, #-0x10]!
    //     0xb679b0: mov             fp, SP
    // 0xb679b4: AllocStack(0x50)
    //     0xb679b4: sub             SP, SP, #0x50
    // 0xb679b8: SetupParameters()
    //     0xb679b8: ldr             x0, [fp, #0x20]
    //     0xb679bc: ldur            w1, [x0, #0x17]
    //     0xb679c0: add             x1, x1, HEAP, lsl #32
    //     0xb679c4: stur            x1, [fp, #-8]
    // 0xb679c8: CheckStackOverflow
    //     0xb679c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb679cc: cmp             SP, x16
    //     0xb679d0: b.ls            #0xb67c88
    // 0xb679d4: r1 = 3
    //     0xb679d4: movz            x1, #0x3
    // 0xb679d8: r0 = AllocateContext()
    //     0xb679d8: bl              #0x16f6108  ; AllocateContextStub
    // 0xb679dc: mov             x5, x0
    // 0xb679e0: ldur            x4, [fp, #-8]
    // 0xb679e4: stur            x5, [fp, #-0x18]
    // 0xb679e8: StoreField: r5->field_b = r4
    //     0xb679e8: stur            w4, [x5, #0xb]
    // 0xb679ec: ldr             x0, [fp, #0x18]
    // 0xb679f0: StoreField: r5->field_f = r0
    //     0xb679f0: stur            w0, [x5, #0xf]
    // 0xb679f4: ldr             x0, [fp, #0x10]
    // 0xb679f8: r1 = LoadInt32Instr(r0)
    //     0xb679f8: sbfx            x1, x0, #1, #0x1f
    //     0xb679fc: tbz             w0, #0, #0xb67a04
    //     0xb67a00: ldur            x1, [x0, #7]
    // 0xb67a04: lsl             x6, x1, #1
    // 0xb67a08: stur            x6, [fp, #-0x10]
    // 0xb67a0c: r0 = BoxInt64Instr(r6)
    //     0xb67a0c: sbfiz           x0, x6, #1, #0x1f
    //     0xb67a10: cmp             x6, x0, asr #1
    //     0xb67a14: b.eq            #0xb67a20
    //     0xb67a18: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb67a1c: stur            x6, [x0, #7]
    // 0xb67a20: StoreField: r5->field_13 = r0
    //     0xb67a20: stur            w0, [x5, #0x13]
    // 0xb67a24: add             x2, x6, #2
    // 0xb67a28: LoadField: r0 = r4->field_f
    //     0xb67a28: ldur            w0, [x4, #0xf]
    // 0xb67a2c: DecompressPointer r0
    //     0xb67a2c: add             x0, x0, HEAP, lsl #32
    // 0xb67a30: LoadField: r1 = r0->field_b
    //     0xb67a30: ldur            w1, [x0, #0xb]
    // 0xb67a34: DecompressPointer r1
    //     0xb67a34: add             x1, x1, HEAP, lsl #32
    // 0xb67a38: cmp             w1, NULL
    // 0xb67a3c: b.eq            #0xb67c90
    // 0xb67a40: LoadField: r0 = r1->field_b
    //     0xb67a40: ldur            w0, [x1, #0xb]
    // 0xb67a44: DecompressPointer r0
    //     0xb67a44: add             x0, x0, HEAP, lsl #32
    // 0xb67a48: cmp             w0, NULL
    // 0xb67a4c: b.eq            #0xb67c94
    // 0xb67a50: LoadField: r3 = r0->field_b
    //     0xb67a50: ldur            w3, [x0, #0xb]
    // 0xb67a54: r0 = BoxInt64Instr(r2)
    //     0xb67a54: sbfiz           x0, x2, #1, #0x1f
    //     0xb67a58: cmp             x2, x0, asr #1
    //     0xb67a5c: b.eq            #0xb67a68
    //     0xb67a60: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb67a64: stur            x2, [x0, #7]
    // 0xb67a68: mov             x1, x0
    // 0xb67a6c: r2 = 0
    //     0xb67a6c: movz            x2, #0
    // 0xb67a70: r0 = clamp()
    //     0xb67a70: bl              #0x6b3958  ; [dart:core] _IntegerImplementation::clamp
    // 0xb67a74: mov             x1, x0
    // 0xb67a78: ldur            x0, [fp, #-8]
    // 0xb67a7c: LoadField: r2 = r0->field_f
    //     0xb67a7c: ldur            w2, [x0, #0xf]
    // 0xb67a80: DecompressPointer r2
    //     0xb67a80: add             x2, x2, HEAP, lsl #32
    // 0xb67a84: LoadField: r3 = r2->field_b
    //     0xb67a84: ldur            w3, [x2, #0xb]
    // 0xb67a88: DecompressPointer r3
    //     0xb67a88: add             x3, x3, HEAP, lsl #32
    // 0xb67a8c: cmp             w3, NULL
    // 0xb67a90: b.eq            #0xb67c98
    // 0xb67a94: LoadField: r2 = r3->field_b
    //     0xb67a94: ldur            w2, [x3, #0xb]
    // 0xb67a98: DecompressPointer r2
    //     0xb67a98: add             x2, x2, HEAP, lsl #32
    // 0xb67a9c: cmp             w2, NULL
    // 0xb67aa0: b.eq            #0xb67c9c
    // 0xb67aa4: str             x1, [SP]
    // 0xb67aa8: mov             x1, x2
    // 0xb67aac: ldur            x2, [fp, #-0x10]
    // 0xb67ab0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xb67ab0: ldr             x4, [PP, #0xc18]  ; [pp+0xc18] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xb67ab4: r0 = sublist()
    //     0xb67ab4: bl              #0x71da80  ; [dart:core] _GrowableList::sublist
    // 0xb67ab8: mov             x1, x0
    // 0xb67abc: ldur            x2, [fp, #-0x18]
    // 0xb67ac0: stur            x1, [fp, #-0x20]
    // 0xb67ac4: ArrayStore: r2[0] = r0  ; List_4
    //     0xb67ac4: stur            w0, [x2, #0x17]
    //     0xb67ac8: ldurb           w16, [x2, #-1]
    //     0xb67acc: ldurb           w17, [x0, #-1]
    //     0xb67ad0: and             x16, x17, x16, lsr #2
    //     0xb67ad4: tst             x16, HEAP, lsr #32
    //     0xb67ad8: b.eq            #0xb67ae0
    //     0xb67adc: bl              #0x16f58a8  ; WriteBarrierWrappersStub
    // 0xb67ae0: r3 = 0
    //     0xb67ae0: movz            x3, #0
    // 0xb67ae4: ldur            x0, [fp, #-8]
    // 0xb67ae8: stur            x3, [fp, #-0x10]
    // 0xb67aec: CheckStackOverflow
    //     0xb67aec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb67af0: cmp             SP, x16
    //     0xb67af4: b.ls            #0xb67ca0
    // 0xb67af8: LoadField: r4 = r1->field_b
    //     0xb67af8: ldur            w4, [x1, #0xb]
    // 0xb67afc: r5 = LoadInt32Instr(r4)
    //     0xb67afc: sbfx            x5, x4, #1, #0x1f
    // 0xb67b00: cmp             x3, x5
    // 0xb67b04: b.ge            #0xb67bb8
    // 0xb67b08: LoadField: r4 = r1->field_f
    //     0xb67b08: ldur            w4, [x1, #0xf]
    // 0xb67b0c: DecompressPointer r4
    //     0xb67b0c: add             x4, x4, HEAP, lsl #32
    // 0xb67b10: ArrayLoad: r5 = r4[r3]  ; Unknown_4
    //     0xb67b10: add             x16, x4, x3, lsl #2
    //     0xb67b14: ldur            w5, [x16, #0xf]
    // 0xb67b18: DecompressPointer r5
    //     0xb67b18: add             x5, x5, HEAP, lsl #32
    // 0xb67b1c: LoadField: r4 = r0->field_f
    //     0xb67b1c: ldur            w4, [x0, #0xf]
    // 0xb67b20: DecompressPointer r4
    //     0xb67b20: add             x4, x4, HEAP, lsl #32
    // 0xb67b24: LoadField: r6 = r4->field_b
    //     0xb67b24: ldur            w6, [x4, #0xb]
    // 0xb67b28: DecompressPointer r6
    //     0xb67b28: add             x6, x6, HEAP, lsl #32
    // 0xb67b2c: cmp             w6, NULL
    // 0xb67b30: b.eq            #0xb67ca8
    // 0xb67b34: LoadField: r4 = r5->field_53
    //     0xb67b34: ldur            w4, [x5, #0x53]
    // 0xb67b38: DecompressPointer r4
    //     0xb67b38: add             x4, x4, HEAP, lsl #32
    // 0xb67b3c: LoadField: r7 = r5->field_3b
    //     0xb67b3c: ldur            w7, [x5, #0x3b]
    // 0xb67b40: DecompressPointer r7
    //     0xb67b40: add             x7, x7, HEAP, lsl #32
    // 0xb67b44: cmp             w7, NULL
    // 0xb67b48: b.ne            #0xb67b54
    // 0xb67b4c: r7 = Null
    //     0xb67b4c: mov             x7, NULL
    // 0xb67b50: b               #0xb67b60
    // 0xb67b54: LoadField: r8 = r7->field_b
    //     0xb67b54: ldur            w8, [x7, #0xb]
    // 0xb67b58: DecompressPointer r8
    //     0xb67b58: add             x8, x8, HEAP, lsl #32
    // 0xb67b5c: mov             x7, x8
    // 0xb67b60: LoadField: r8 = r5->field_2b
    //     0xb67b60: ldur            w8, [x5, #0x2b]
    // 0xb67b64: DecompressPointer r8
    //     0xb67b64: add             x8, x8, HEAP, lsl #32
    // 0xb67b68: LoadField: r9 = r5->field_57
    //     0xb67b68: ldur            w9, [x5, #0x57]
    // 0xb67b6c: DecompressPointer r9
    //     0xb67b6c: add             x9, x9, HEAP, lsl #32
    // 0xb67b70: LoadField: r10 = r5->field_7b
    //     0xb67b70: ldur            w10, [x5, #0x7b]
    // 0xb67b74: DecompressPointer r10
    //     0xb67b74: add             x10, x10, HEAP, lsl #32
    // 0xb67b78: LoadField: r5 = r6->field_3b
    //     0xb67b78: ldur            w5, [x6, #0x3b]
    // 0xb67b7c: DecompressPointer r5
    //     0xb67b7c: add             x5, x5, HEAP, lsl #32
    // 0xb67b80: stp             x4, x5, [SP, #0x20]
    // 0xb67b84: stp             x8, x7, [SP, #0x10]
    // 0xb67b88: stp             x10, x9, [SP]
    // 0xb67b8c: r4 = 0
    //     0xb67b8c: movz            x4, #0
    // 0xb67b90: ldr             x0, [SP, #0x28]
    // 0xb67b94: r16 = UnlinkedCall_0x613b5c
    //     0xb67b94: add             x16, PP, #0x55, lsl #12  ; [pp+0x55c70] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb67b98: add             x16, x16, #0xc70
    // 0xb67b9c: ldp             x5, lr, [x16]
    // 0xb67ba0: blr             lr
    // 0xb67ba4: ldur            x0, [fp, #-0x10]
    // 0xb67ba8: add             x3, x0, #1
    // 0xb67bac: ldur            x2, [fp, #-0x18]
    // 0xb67bb0: ldur            x1, [fp, #-0x20]
    // 0xb67bb4: b               #0xb67ae4
    // 0xb67bb8: ldur            x1, [fp, #-0x20]
    // 0xb67bbc: r0 = asMap()
    //     0xb67bbc: bl              #0x7143ec  ; [dart:collection] ListBase::asMap
    // 0xb67bc0: mov             x1, x0
    // 0xb67bc4: r0 = entries()
    //     0xb67bc4: bl              #0x1641968  ; [dart:collection] MapBase::entries
    // 0xb67bc8: ldur            x2, [fp, #-0x18]
    // 0xb67bcc: r1 = Function '<anonymous closure>':.
    //     0xb67bcc: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c80] AnonymousClosure: (0xb67cac), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::build (0xb665fc)
    //     0xb67bd0: ldr             x1, [x1, #0xc80]
    // 0xb67bd4: stur            x0, [fp, #-8]
    // 0xb67bd8: r0 = AllocateClosure()
    //     0xb67bd8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb67bdc: r16 = <SizedBox>
    //     0xb67bdc: add             x16, PP, #0x53, lsl #12  ; [pp+0x536b0] TypeArguments: <SizedBox>
    //     0xb67be0: ldr             x16, [x16, #0x6b0]
    // 0xb67be4: ldur            lr, [fp, #-8]
    // 0xb67be8: stp             lr, x16, [SP, #8]
    // 0xb67bec: str             x0, [SP]
    // 0xb67bf0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xb67bf0: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xb67bf4: r0 = map()
    //     0xb67bf4: bl              #0x78898c  ; [dart:_internal] ListIterable::map
    // 0xb67bf8: mov             x1, x0
    // 0xb67bfc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb67bfc: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb67c00: r0 = toList()
    //     0xb67c00: bl              #0x789e28  ; [dart:_internal] ListIterable::toList
    // 0xb67c04: stur            x0, [fp, #-8]
    // 0xb67c08: r0 = Row()
    //     0xb67c08: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb67c0c: mov             x1, x0
    // 0xb67c10: r0 = Instance_Axis
    //     0xb67c10: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb67c14: stur            x1, [fp, #-0x18]
    // 0xb67c18: StoreField: r1->field_f = r0
    //     0xb67c18: stur            w0, [x1, #0xf]
    // 0xb67c1c: r0 = Instance_MainAxisAlignment
    //     0xb67c1c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb67c20: ldr             x0, [x0, #0xa08]
    // 0xb67c24: StoreField: r1->field_13 = r0
    //     0xb67c24: stur            w0, [x1, #0x13]
    // 0xb67c28: r0 = Instance_MainAxisSize
    //     0xb67c28: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb67c2c: ldr             x0, [x0, #0xa10]
    // 0xb67c30: ArrayStore: r1[0] = r0  ; List_4
    //     0xb67c30: stur            w0, [x1, #0x17]
    // 0xb67c34: r0 = Instance_CrossAxisAlignment
    //     0xb67c34: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb67c38: ldr             x0, [x0, #0xa18]
    // 0xb67c3c: StoreField: r1->field_1b = r0
    //     0xb67c3c: stur            w0, [x1, #0x1b]
    // 0xb67c40: r0 = Instance_VerticalDirection
    //     0xb67c40: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb67c44: ldr             x0, [x0, #0xa20]
    // 0xb67c48: StoreField: r1->field_23 = r0
    //     0xb67c48: stur            w0, [x1, #0x23]
    // 0xb67c4c: r0 = Instance_Clip
    //     0xb67c4c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb67c50: ldr             x0, [x0, #0x38]
    // 0xb67c54: StoreField: r1->field_2b = r0
    //     0xb67c54: stur            w0, [x1, #0x2b]
    // 0xb67c58: StoreField: r1->field_2f = rZR
    //     0xb67c58: stur            xzr, [x1, #0x2f]
    // 0xb67c5c: ldur            x0, [fp, #-8]
    // 0xb67c60: StoreField: r1->field_b = r0
    //     0xb67c60: stur            w0, [x1, #0xb]
    // 0xb67c64: r0 = Padding()
    //     0xb67c64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb67c68: r1 = Instance_EdgeInsets
    //     0xb67c68: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3f548] Obj!EdgeInsets@d57171
    //     0xb67c6c: ldr             x1, [x1, #0x548]
    // 0xb67c70: StoreField: r0->field_f = r1
    //     0xb67c70: stur            w1, [x0, #0xf]
    // 0xb67c74: ldur            x1, [fp, #-0x18]
    // 0xb67c78: StoreField: r0->field_b = r1
    //     0xb67c78: stur            w1, [x0, #0xb]
    // 0xb67c7c: LeaveFrame
    //     0xb67c7c: mov             SP, fp
    //     0xb67c80: ldp             fp, lr, [SP], #0x10
    // 0xb67c84: ret
    //     0xb67c84: ret             
    // 0xb67c88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb67c88: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb67c8c: b               #0xb679d4
    // 0xb67c90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb67c90: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb67c94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb67c94: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb67c98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb67c98: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb67c9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb67c9c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb67ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb67ca0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb67ca4: b               #0xb67af8
    // 0xb67ca8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb67ca8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SizedBox <anonymous closure>(dynamic, MapEntry<int, Entity>) {
    // ** addr: 0xb67cac, size: 0x330
    // 0xb67cac: EnterFrame
    //     0xb67cac: stp             fp, lr, [SP, #-0x10]!
    //     0xb67cb0: mov             fp, SP
    // 0xb67cb4: AllocStack(0x40)
    //     0xb67cb4: sub             SP, SP, #0x40
    // 0xb67cb8: SetupParameters()
    //     0xb67cb8: ldr             x0, [fp, #0x18]
    //     0xb67cbc: ldur            w2, [x0, #0x17]
    //     0xb67cc0: add             x2, x2, HEAP, lsl #32
    //     0xb67cc4: stur            x2, [fp, #-0x18]
    // 0xb67cc8: CheckStackOverflow
    //     0xb67cc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb67ccc: cmp             SP, x16
    //     0xb67cd0: b.ls            #0xb67fb4
    // 0xb67cd4: ldr             x0, [fp, #0x10]
    // 0xb67cd8: LoadField: r3 = r0->field_b
    //     0xb67cd8: ldur            w3, [x0, #0xb]
    // 0xb67cdc: DecompressPointer r3
    //     0xb67cdc: add             x3, x3, HEAP, lsl #32
    // 0xb67ce0: stur            x3, [fp, #-0x10]
    // 0xb67ce4: LoadField: r4 = r0->field_f
    //     0xb67ce4: ldur            w4, [x0, #0xf]
    // 0xb67ce8: DecompressPointer r4
    //     0xb67ce8: add             x4, x4, HEAP, lsl #32
    // 0xb67cec: stur            x4, [fp, #-8]
    // 0xb67cf0: LoadField: r1 = r2->field_f
    //     0xb67cf0: ldur            w1, [x2, #0xf]
    // 0xb67cf4: DecompressPointer r1
    //     0xb67cf4: add             x1, x1, HEAP, lsl #32
    // 0xb67cf8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xb67cf8: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xb67cfc: r0 = _of()
    //     0xb67cfc: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xb67d00: LoadField: r1 = r0->field_7
    //     0xb67d00: ldur            w1, [x0, #7]
    // 0xb67d04: DecompressPointer r1
    //     0xb67d04: add             x1, x1, HEAP, lsl #32
    // 0xb67d08: LoadField: d0 = r1->field_7
    //     0xb67d08: ldur            d0, [x1, #7]
    // 0xb67d0c: d1 = 0.450000
    //     0xb67d0c: add             x17, PP, #0x52, lsl #12  ; [pp+0x52d08] IMM: double(0.45) from 0x3fdccccccccccccd
    //     0xb67d10: ldr             d1, [x17, #0xd08]
    // 0xb67d14: fmul            d2, d0, d1
    // 0xb67d18: ldur            x0, [fp, #-0x10]
    // 0xb67d1c: stur            d2, [fp, #-0x40]
    // 0xb67d20: cbnz            w0, #0xb67d2c
    // 0xb67d24: d0 = 0.000000
    //     0xb67d24: eor             v0.16b, v0.16b, v0.16b
    // 0xb67d28: b               #0xb67d30
    // 0xb67d2c: d0 = 4.000000
    //     0xb67d2c: fmov            d0, #4.00000000
    // 0xb67d30: ldur            x1, [fp, #-0x18]
    // 0xb67d34: stur            d0, [fp, #-0x38]
    // 0xb67d38: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xb67d38: ldur            w2, [x1, #0x17]
    // 0xb67d3c: DecompressPointer r2
    //     0xb67d3c: add             x2, x2, HEAP, lsl #32
    // 0xb67d40: LoadField: r3 = r2->field_b
    //     0xb67d40: ldur            w3, [x2, #0xb]
    // 0xb67d44: r2 = LoadInt32Instr(r3)
    //     0xb67d44: sbfx            x2, x3, #1, #0x1f
    // 0xb67d48: sub             x3, x2, #1
    // 0xb67d4c: lsl             x2, x3, #1
    // 0xb67d50: cmp             w0, w2
    // 0xb67d54: b.ne            #0xb67d60
    // 0xb67d58: d1 = 0.000000
    //     0xb67d58: eor             v1.16b, v1.16b, v1.16b
    // 0xb67d5c: b               #0xb67d64
    // 0xb67d60: d1 = 4.000000
    //     0xb67d60: fmov            d1, #4.00000000
    // 0xb67d64: ldur            x2, [fp, #-8]
    // 0xb67d68: stur            d1, [fp, #-0x30]
    // 0xb67d6c: r0 = EdgeInsets()
    //     0xb67d6c: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb67d70: ldur            d0, [fp, #-0x38]
    // 0xb67d74: stur            x0, [fp, #-0x20]
    // 0xb67d78: StoreField: r0->field_7 = d0
    //     0xb67d78: stur            d0, [x0, #7]
    // 0xb67d7c: StoreField: r0->field_f = rZR
    //     0xb67d7c: stur            xzr, [x0, #0xf]
    // 0xb67d80: ldur            d0, [fp, #-0x30]
    // 0xb67d84: ArrayStore: r0[0] = d0  ; List_8
    //     0xb67d84: stur            d0, [x0, #0x17]
    // 0xb67d88: StoreField: r0->field_1f = rZR
    //     0xb67d88: stur            xzr, [x0, #0x1f]
    // 0xb67d8c: ldur            x1, [fp, #-0x18]
    // 0xb67d90: LoadField: r2 = r1->field_b
    //     0xb67d90: ldur            w2, [x1, #0xb]
    // 0xb67d94: DecompressPointer r2
    //     0xb67d94: add             x2, x2, HEAP, lsl #32
    // 0xb67d98: LoadField: r3 = r2->field_f
    //     0xb67d98: ldur            w3, [x2, #0xf]
    // 0xb67d9c: DecompressPointer r3
    //     0xb67d9c: add             x3, x3, HEAP, lsl #32
    // 0xb67da0: LoadField: r2 = r1->field_13
    //     0xb67da0: ldur            w2, [x1, #0x13]
    // 0xb67da4: DecompressPointer r2
    //     0xb67da4: add             x2, x2, HEAP, lsl #32
    // 0xb67da8: ldur            x1, [fp, #-0x10]
    // 0xb67dac: cmp             w1, NULL
    // 0xb67db0: b.eq            #0xb67fbc
    // 0xb67db4: r4 = LoadInt32Instr(r2)
    //     0xb67db4: sbfx            x4, x2, #1, #0x1f
    //     0xb67db8: tbz             w2, #0, #0xb67dc0
    //     0xb67dbc: ldur            x4, [x2, #7]
    // 0xb67dc0: r2 = LoadInt32Instr(r1)
    //     0xb67dc0: sbfx            x2, x1, #1, #0x1f
    //     0xb67dc4: tbz             w1, #0, #0xb67dcc
    //     0xb67dc8: ldur            x2, [x1, #7]
    // 0xb67dcc: add             x1, x4, x2
    // 0xb67dd0: mov             x16, x1
    // 0xb67dd4: mov             x1, x3
    // 0xb67dd8: mov             x3, x16
    // 0xb67ddc: ldur            x2, [fp, #-8]
    // 0xb67de0: r0 = glassThemeSlider()
    //     0xb67de0: bl              #0xb67fdc  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::glassThemeSlider
    // 0xb67de4: r1 = Null
    //     0xb67de4: mov             x1, NULL
    // 0xb67de8: r2 = 2
    //     0xb67de8: movz            x2, #0x2
    // 0xb67dec: stur            x0, [fp, #-0x10]
    // 0xb67df0: r0 = AllocateArray()
    //     0xb67df0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb67df4: mov             x2, x0
    // 0xb67df8: ldur            x0, [fp, #-0x10]
    // 0xb67dfc: stur            x2, [fp, #-0x18]
    // 0xb67e00: StoreField: r2->field_f = r0
    //     0xb67e00: stur            w0, [x2, #0xf]
    // 0xb67e04: r1 = <Widget>
    //     0xb67e04: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb67e08: r0 = AllocateGrowableArray()
    //     0xb67e08: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb67e0c: mov             x1, x0
    // 0xb67e10: ldur            x0, [fp, #-0x18]
    // 0xb67e14: stur            x1, [fp, #-0x10]
    // 0xb67e18: StoreField: r1->field_f = r0
    //     0xb67e18: stur            w0, [x1, #0xf]
    // 0xb67e1c: r0 = 2
    //     0xb67e1c: movz            x0, #0x2
    // 0xb67e20: StoreField: r1->field_b = r0
    //     0xb67e20: stur            w0, [x1, #0xb]
    // 0xb67e24: ldur            x0, [fp, #-8]
    // 0xb67e28: cmp             w0, NULL
    // 0xb67e2c: b.eq            #0xb67fc0
    // 0xb67e30: LoadField: r2 = r0->field_bb
    //     0xb67e30: ldur            w2, [x0, #0xbb]
    // 0xb67e34: DecompressPointer r2
    //     0xb67e34: add             x2, x2, HEAP, lsl #32
    // 0xb67e38: cmp             w2, NULL
    // 0xb67e3c: b.ne            #0xb67e48
    // 0xb67e40: mov             x2, x1
    // 0xb67e44: b               #0xb67f08
    // 0xb67e48: tbnz            w2, #4, #0xb67f04
    // 0xb67e4c: r0 = SvgPicture()
    //     0xb67e4c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb67e50: mov             x1, x0
    // 0xb67e54: r2 = "assets/images/free-gift-icon.svg"
    //     0xb67e54: add             x2, PP, #0x52, lsl #12  ; [pp+0x52d40] "assets/images/free-gift-icon.svg"
    //     0xb67e58: ldr             x2, [x2, #0xd40]
    // 0xb67e5c: stur            x0, [fp, #-8]
    // 0xb67e60: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb67e60: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb67e64: r0 = SvgPicture.asset()
    //     0xb67e64: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb67e68: r0 = Padding()
    //     0xb67e68: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb67e6c: mov             x2, x0
    // 0xb67e70: r0 = Instance_EdgeInsets
    //     0xb67e70: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d48] Obj!EdgeInsets@d584c1
    //     0xb67e74: ldr             x0, [x0, #0xd48]
    // 0xb67e78: stur            x2, [fp, #-0x18]
    // 0xb67e7c: StoreField: r2->field_f = r0
    //     0xb67e7c: stur            w0, [x2, #0xf]
    // 0xb67e80: ldur            x0, [fp, #-8]
    // 0xb67e84: StoreField: r2->field_b = r0
    //     0xb67e84: stur            w0, [x2, #0xb]
    // 0xb67e88: ldur            x0, [fp, #-0x10]
    // 0xb67e8c: LoadField: r1 = r0->field_b
    //     0xb67e8c: ldur            w1, [x0, #0xb]
    // 0xb67e90: LoadField: r3 = r0->field_f
    //     0xb67e90: ldur            w3, [x0, #0xf]
    // 0xb67e94: DecompressPointer r3
    //     0xb67e94: add             x3, x3, HEAP, lsl #32
    // 0xb67e98: LoadField: r4 = r3->field_b
    //     0xb67e98: ldur            w4, [x3, #0xb]
    // 0xb67e9c: r3 = LoadInt32Instr(r1)
    //     0xb67e9c: sbfx            x3, x1, #1, #0x1f
    // 0xb67ea0: stur            x3, [fp, #-0x28]
    // 0xb67ea4: r1 = LoadInt32Instr(r4)
    //     0xb67ea4: sbfx            x1, x4, #1, #0x1f
    // 0xb67ea8: cmp             x3, x1
    // 0xb67eac: b.ne            #0xb67eb8
    // 0xb67eb0: mov             x1, x0
    // 0xb67eb4: r0 = _growToNextCapacity()
    //     0xb67eb4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb67eb8: ldur            x2, [fp, #-0x10]
    // 0xb67ebc: ldur            x3, [fp, #-0x28]
    // 0xb67ec0: add             x0, x3, #1
    // 0xb67ec4: lsl             x1, x0, #1
    // 0xb67ec8: StoreField: r2->field_b = r1
    //     0xb67ec8: stur            w1, [x2, #0xb]
    // 0xb67ecc: LoadField: r1 = r2->field_f
    //     0xb67ecc: ldur            w1, [x2, #0xf]
    // 0xb67ed0: DecompressPointer r1
    //     0xb67ed0: add             x1, x1, HEAP, lsl #32
    // 0xb67ed4: ldur            x0, [fp, #-0x18]
    // 0xb67ed8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb67ed8: add             x25, x1, x3, lsl #2
    //     0xb67edc: add             x25, x25, #0xf
    //     0xb67ee0: str             w0, [x25]
    //     0xb67ee4: tbz             w0, #0, #0xb67f00
    //     0xb67ee8: ldurb           w16, [x1, #-1]
    //     0xb67eec: ldurb           w17, [x0, #-1]
    //     0xb67ef0: and             x16, x17, x16, lsr #2
    //     0xb67ef4: tst             x16, HEAP, lsr #32
    //     0xb67ef8: b.eq            #0xb67f00
    //     0xb67efc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb67f00: b               #0xb67f08
    // 0xb67f04: mov             x2, x1
    // 0xb67f08: ldur            d0, [fp, #-0x40]
    // 0xb67f0c: ldur            x0, [fp, #-0x20]
    // 0xb67f10: r0 = Stack()
    //     0xb67f10: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb67f14: mov             x1, x0
    // 0xb67f18: r0 = Instance_Alignment
    //     0xb67f18: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f950] Obj!Alignment@d5a701
    //     0xb67f1c: ldr             x0, [x0, #0x950]
    // 0xb67f20: stur            x1, [fp, #-8]
    // 0xb67f24: StoreField: r1->field_f = r0
    //     0xb67f24: stur            w0, [x1, #0xf]
    // 0xb67f28: r0 = Instance_StackFit
    //     0xb67f28: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb67f2c: ldr             x0, [x0, #0xfa8]
    // 0xb67f30: ArrayStore: r1[0] = r0  ; List_4
    //     0xb67f30: stur            w0, [x1, #0x17]
    // 0xb67f34: r0 = Instance_Clip
    //     0xb67f34: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb67f38: ldr             x0, [x0, #0x7e0]
    // 0xb67f3c: StoreField: r1->field_1b = r0
    //     0xb67f3c: stur            w0, [x1, #0x1b]
    // 0xb67f40: ldur            x0, [fp, #-0x10]
    // 0xb67f44: StoreField: r1->field_b = r0
    //     0xb67f44: stur            w0, [x1, #0xb]
    // 0xb67f48: r0 = Padding()
    //     0xb67f48: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb67f4c: mov             x1, x0
    // 0xb67f50: ldur            x0, [fp, #-0x20]
    // 0xb67f54: stur            x1, [fp, #-0x10]
    // 0xb67f58: StoreField: r1->field_f = r0
    //     0xb67f58: stur            w0, [x1, #0xf]
    // 0xb67f5c: ldur            x0, [fp, #-8]
    // 0xb67f60: StoreField: r1->field_b = r0
    //     0xb67f60: stur            w0, [x1, #0xb]
    // 0xb67f64: ldur            d0, [fp, #-0x40]
    // 0xb67f68: r0 = inline_Allocate_Double()
    //     0xb67f68: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xb67f6c: add             x0, x0, #0x10
    //     0xb67f70: cmp             x2, x0
    //     0xb67f74: b.ls            #0xb67fc4
    //     0xb67f78: str             x0, [THR, #0x50]  ; THR::top
    //     0xb67f7c: sub             x0, x0, #0xf
    //     0xb67f80: movz            x2, #0xe15c
    //     0xb67f84: movk            x2, #0x3, lsl #16
    //     0xb67f88: stur            x2, [x0, #-1]
    // 0xb67f8c: StoreField: r0->field_7 = d0
    //     0xb67f8c: stur            d0, [x0, #7]
    // 0xb67f90: stur            x0, [fp, #-8]
    // 0xb67f94: r0 = SizedBox()
    //     0xb67f94: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb67f98: ldur            x1, [fp, #-8]
    // 0xb67f9c: StoreField: r0->field_f = r1
    //     0xb67f9c: stur            w1, [x0, #0xf]
    // 0xb67fa0: ldur            x1, [fp, #-0x10]
    // 0xb67fa4: StoreField: r0->field_b = r1
    //     0xb67fa4: stur            w1, [x0, #0xb]
    // 0xb67fa8: LeaveFrame
    //     0xb67fa8: mov             SP, fp
    //     0xb67fac: ldp             fp, lr, [SP], #0x10
    // 0xb67fb0: ret
    //     0xb67fb0: ret             
    // 0xb67fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb67fb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb67fb8: b               #0xb67cd4
    // 0xb67fbc: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb67fbc: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb67fc0: r0 = NullErrorSharedWithoutFPURegs()
    //     0xb67fc0: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0xb67fc4: SaveReg d0
    //     0xb67fc4: str             q0, [SP, #-0x10]!
    // 0xb67fc8: SaveReg r1
    //     0xb67fc8: str             x1, [SP, #-8]!
    // 0xb67fcc: r0 = AllocateDouble()
    //     0xb67fcc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xb67fd0: RestoreReg r1
    //     0xb67fd0: ldr             x1, [SP], #8
    // 0xb67fd4: RestoreReg d0
    //     0xb67fd4: ldr             q0, [SP], #0x10
    // 0xb67fd8: b               #0xb67f8c
  }
  _ glassThemeSlider(/* No info */) {
    // ** addr: 0xb67fdc, size: 0x810
    // 0xb67fdc: EnterFrame
    //     0xb67fdc: stp             fp, lr, [SP, #-0x10]!
    //     0xb67fe0: mov             fp, SP
    // 0xb67fe4: AllocStack(0x68)
    //     0xb67fe4: sub             SP, SP, #0x68
    // 0xb67fe8: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb67fe8: stur            x1, [fp, #-8]
    //     0xb67fec: stur            x2, [fp, #-0x10]
    //     0xb67ff0: stur            x3, [fp, #-0x18]
    // 0xb67ff4: CheckStackOverflow
    //     0xb67ff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb67ff8: cmp             SP, x16
    //     0xb67ffc: b.ls            #0xb687c8
    // 0xb68000: r1 = 2
    //     0xb68000: movz            x1, #0x2
    // 0xb68004: r0 = AllocateContext()
    //     0xb68004: bl              #0x16f6108  ; AllocateContextStub
    // 0xb68008: mov             x2, x0
    // 0xb6800c: ldur            x0, [fp, #-8]
    // 0xb68010: stur            x2, [fp, #-0x20]
    // 0xb68014: StoreField: r2->field_f = r0
    //     0xb68014: stur            w0, [x2, #0xf]
    // 0xb68018: ldur            x1, [fp, #-0x10]
    // 0xb6801c: StoreField: r2->field_13 = r1
    //     0xb6801c: stur            w1, [x2, #0x13]
    // 0xb68020: LoadField: r1 = r0->field_f
    //     0xb68020: ldur            w1, [x0, #0xf]
    // 0xb68024: DecompressPointer r1
    //     0xb68024: add             x1, x1, HEAP, lsl #32
    // 0xb68028: cmp             w1, NULL
    // 0xb6802c: b.eq            #0xb687d0
    // 0xb68030: r0 = of()
    //     0xb68030: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb68034: LoadField: r1 = r0->field_5b
    //     0xb68034: ldur            w1, [x0, #0x5b]
    // 0xb68038: DecompressPointer r1
    //     0xb68038: add             x1, x1, HEAP, lsl #32
    // 0xb6803c: r0 = LoadClassIdInstr(r1)
    //     0xb6803c: ldur            x0, [x1, #-1]
    //     0xb68040: ubfx            x0, x0, #0xc, #0x14
    // 0xb68044: d0 = 0.030000
    //     0xb68044: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0xb68048: ldr             d0, [x17, #0x238]
    // 0xb6804c: r0 = GDT[cid_x0 + -0xffa]()
    //     0xb6804c: sub             lr, x0, #0xffa
    //     0xb68050: ldr             lr, [x21, lr, lsl #3]
    //     0xb68054: blr             lr
    // 0xb68058: stur            x0, [fp, #-0x10]
    // 0xb6805c: r0 = Radius()
    //     0xb6805c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb68060: d0 = 12.000000
    //     0xb68060: fmov            d0, #12.00000000
    // 0xb68064: stur            x0, [fp, #-0x28]
    // 0xb68068: StoreField: r0->field_7 = d0
    //     0xb68068: stur            d0, [x0, #7]
    // 0xb6806c: StoreField: r0->field_f = d0
    //     0xb6806c: stur            d0, [x0, #0xf]
    // 0xb68070: r0 = BorderRadius()
    //     0xb68070: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb68074: mov             x1, x0
    // 0xb68078: ldur            x0, [fp, #-0x28]
    // 0xb6807c: stur            x1, [fp, #-0x30]
    // 0xb68080: StoreField: r1->field_7 = r0
    //     0xb68080: stur            w0, [x1, #7]
    // 0xb68084: StoreField: r1->field_b = r0
    //     0xb68084: stur            w0, [x1, #0xb]
    // 0xb68088: StoreField: r1->field_f = r0
    //     0xb68088: stur            w0, [x1, #0xf]
    // 0xb6808c: StoreField: r1->field_13 = r0
    //     0xb6808c: stur            w0, [x1, #0x13]
    // 0xb68090: r0 = RoundedRectangleBorder()
    //     0xb68090: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb68094: mov             x3, x0
    // 0xb68098: ldur            x0, [fp, #-0x30]
    // 0xb6809c: stur            x3, [fp, #-0x38]
    // 0xb680a0: StoreField: r3->field_b = r0
    //     0xb680a0: stur            w0, [x3, #0xb]
    // 0xb680a4: r0 = Instance_BorderSide
    //     0xb680a4: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb680a8: ldr             x0, [x0, #0xe20]
    // 0xb680ac: StoreField: r3->field_7 = r0
    //     0xb680ac: stur            w0, [x3, #7]
    // 0xb680b0: ldur            x0, [fp, #-0x20]
    // 0xb680b4: LoadField: r1 = r0->field_13
    //     0xb680b4: ldur            w1, [x0, #0x13]
    // 0xb680b8: DecompressPointer r1
    //     0xb680b8: add             x1, x1, HEAP, lsl #32
    // 0xb680bc: LoadField: r2 = r1->field_37
    //     0xb680bc: ldur            w2, [x1, #0x37]
    // 0xb680c0: DecompressPointer r2
    //     0xb680c0: add             x2, x2, HEAP, lsl #32
    // 0xb680c4: cmp             w2, NULL
    // 0xb680c8: b.ne            #0xb680d4
    // 0xb680cc: r1 = Null
    //     0xb680cc: mov             x1, NULL
    // 0xb680d0: b               #0xb680d8
    // 0xb680d4: LoadField: r1 = r2->field_b
    //     0xb680d4: ldur            w1, [x2, #0xb]
    // 0xb680d8: cmp             w1, NULL
    // 0xb680dc: b.ne            #0xb680e8
    // 0xb680e0: r1 = 0
    //     0xb680e0: movz            x1, #0
    // 0xb680e4: b               #0xb680f0
    // 0xb680e8: r2 = LoadInt32Instr(r1)
    //     0xb680e8: sbfx            x2, x1, #1, #0x1f
    // 0xb680ec: mov             x1, x2
    // 0xb680f0: ldur            x5, [fp, #-8]
    // 0xb680f4: ldur            x4, [fp, #-0x10]
    // 0xb680f8: ArrayLoad: r6 = r5[0]  ; List_4
    //     0xb680f8: ldur            w6, [x5, #0x17]
    // 0xb680fc: DecompressPointer r6
    //     0xb680fc: add             x6, x6, HEAP, lsl #32
    // 0xb68100: r16 = Sentinel
    //     0xb68100: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xb68104: cmp             w6, w16
    // 0xb68108: b.eq            #0xb687d4
    // 0xb6810c: stur            x6, [fp, #-0x30]
    // 0xb68110: lsl             x7, x1, #1
    // 0xb68114: mov             x2, x0
    // 0xb68118: stur            x7, [fp, #-0x28]
    // 0xb6811c: r1 = Function '<anonymous closure>':.
    //     0xb6811c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c88] AnonymousClosure: (0xb69d20), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::glassThemeSlider (0xb67fdc)
    //     0xb68120: ldr             x1, [x1, #0xc88]
    // 0xb68124: r0 = AllocateClosure()
    //     0xb68124: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb68128: ldur            x2, [fp, #-0x20]
    // 0xb6812c: r1 = Function '<anonymous closure>':.
    //     0xb6812c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55c90] AnonymousClosure: (0xb6937c), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::glassThemeSlider (0xb67fdc)
    //     0xb68130: ldr             x1, [x1, #0xc90]
    // 0xb68134: stur            x0, [fp, #-0x40]
    // 0xb68138: r0 = AllocateClosure()
    //     0xb68138: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb6813c: stur            x0, [fp, #-0x48]
    // 0xb68140: r0 = PageView()
    //     0xb68140: bl              #0x980908  ; AllocatePageViewStub -> PageView (size=0x44)
    // 0xb68144: stur            x0, [fp, #-0x50]
    // 0xb68148: ldur            x16, [fp, #-0x30]
    // 0xb6814c: str             x16, [SP]
    // 0xb68150: mov             x1, x0
    // 0xb68154: ldur            x2, [fp, #-0x48]
    // 0xb68158: ldur            x3, [fp, #-0x28]
    // 0xb6815c: ldur            x5, [fp, #-0x40]
    // 0xb68160: r4 = const [0, 0x5, 0x1, 0x4, controller, 0x4, null]
    //     0xb68160: add             x4, PP, #0x52, lsl #12  ; [pp+0x52d60] List(7) [0, 0x5, 0x1, 0x4, "controller", 0x4, Null]
    //     0xb68164: ldr             x4, [x4, #0xd60]
    // 0xb68168: r0 = PageView.builder()
    //     0xb68168: bl              #0x980678  ; [package:flutter/src/widgets/page_view.dart] PageView::PageView.builder
    // 0xb6816c: r0 = AspectRatio()
    //     0xb6816c: bl              #0x859240  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xb68170: d0 = 1.000000
    //     0xb68170: fmov            d0, #1.00000000
    // 0xb68174: stur            x0, [fp, #-0x28]
    // 0xb68178: StoreField: r0->field_f = d0
    //     0xb68178: stur            d0, [x0, #0xf]
    // 0xb6817c: ldur            x1, [fp, #-0x50]
    // 0xb68180: StoreField: r0->field_b = r1
    //     0xb68180: stur            w1, [x0, #0xb]
    // 0xb68184: r0 = Card()
    //     0xb68184: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb68188: mov             x2, x0
    // 0xb6818c: ldur            x0, [fp, #-0x10]
    // 0xb68190: stur            x2, [fp, #-0x30]
    // 0xb68194: StoreField: r2->field_b = r0
    //     0xb68194: stur            w0, [x2, #0xb]
    // 0xb68198: r0 = 0.000000
    //     0xb68198: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb6819c: ArrayStore: r2[0] = r0  ; List_4
    //     0xb6819c: stur            w0, [x2, #0x17]
    // 0xb681a0: ldur            x0, [fp, #-0x38]
    // 0xb681a4: StoreField: r2->field_1b = r0
    //     0xb681a4: stur            w0, [x2, #0x1b]
    // 0xb681a8: r0 = true
    //     0xb681a8: add             x0, NULL, #0x20  ; true
    // 0xb681ac: StoreField: r2->field_1f = r0
    //     0xb681ac: stur            w0, [x2, #0x1f]
    // 0xb681b0: ldur            x1, [fp, #-0x28]
    // 0xb681b4: StoreField: r2->field_2f = r1
    //     0xb681b4: stur            w1, [x2, #0x2f]
    // 0xb681b8: StoreField: r2->field_2b = r0
    //     0xb681b8: stur            w0, [x2, #0x2b]
    // 0xb681bc: r1 = Instance__CardVariant
    //     0xb681bc: add             x1, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb681c0: ldr             x1, [x1, #0xa68]
    // 0xb681c4: StoreField: r2->field_33 = r1
    //     0xb681c4: stur            w1, [x2, #0x33]
    // 0xb681c8: ldur            x3, [fp, #-0x20]
    // 0xb681cc: LoadField: r1 = r3->field_13
    //     0xb681cc: ldur            w1, [x3, #0x13]
    // 0xb681d0: DecompressPointer r1
    //     0xb681d0: add             x1, x1, HEAP, lsl #32
    // 0xb681d4: LoadField: r4 = r1->field_33
    //     0xb681d4: ldur            w4, [x1, #0x33]
    // 0xb681d8: DecompressPointer r4
    //     0xb681d8: add             x4, x4, HEAP, lsl #32
    // 0xb681dc: cmp             w4, NULL
    // 0xb681e0: b.ne            #0xb681ec
    // 0xb681e4: r1 = Null
    //     0xb681e4: mov             x1, NULL
    // 0xb681e8: b               #0xb681f4
    // 0xb681ec: LoadField: r1 = r4->field_7
    //     0xb681ec: ldur            w1, [x4, #7]
    // 0xb681f0: DecompressPointer r1
    //     0xb681f0: add             x1, x1, HEAP, lsl #32
    // 0xb681f4: cmp             w1, NULL
    // 0xb681f8: b.ne            #0xb68200
    // 0xb681fc: r1 = ""
    //     0xb681fc: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb68200: ldur            x4, [fp, #-8]
    // 0xb68204: r0 = capitalizeFirstWord()
    //     0xb68204: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0xb68208: mov             x2, x0
    // 0xb6820c: ldur            x0, [fp, #-8]
    // 0xb68210: stur            x2, [fp, #-0x10]
    // 0xb68214: LoadField: r1 = r0->field_f
    //     0xb68214: ldur            w1, [x0, #0xf]
    // 0xb68218: DecompressPointer r1
    //     0xb68218: add             x1, x1, HEAP, lsl #32
    // 0xb6821c: cmp             w1, NULL
    // 0xb68220: b.eq            #0xb687e0
    // 0xb68224: r0 = of()
    //     0xb68224: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb68228: LoadField: r1 = r0->field_87
    //     0xb68228: ldur            w1, [x0, #0x87]
    // 0xb6822c: DecompressPointer r1
    //     0xb6822c: add             x1, x1, HEAP, lsl #32
    // 0xb68230: LoadField: r0 = r1->field_2b
    //     0xb68230: ldur            w0, [x1, #0x2b]
    // 0xb68234: DecompressPointer r0
    //     0xb68234: add             x0, x0, HEAP, lsl #32
    // 0xb68238: stur            x0, [fp, #-0x28]
    // 0xb6823c: r1 = Instance_Color
    //     0xb6823c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb68240: d0 = 0.700000
    //     0xb68240: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb68244: ldr             d0, [x17, #0xf48]
    // 0xb68248: r0 = withOpacity()
    //     0xb68248: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6824c: r16 = 12.000000
    //     0xb6824c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb68250: ldr             x16, [x16, #0x9e8]
    // 0xb68254: stp             x0, x16, [SP]
    // 0xb68258: ldur            x1, [fp, #-0x28]
    // 0xb6825c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6825c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb68260: ldr             x4, [x4, #0xaa0]
    // 0xb68264: r0 = copyWith()
    //     0xb68264: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb68268: stur            x0, [fp, #-0x28]
    // 0xb6826c: r0 = Text()
    //     0xb6826c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb68270: mov             x1, x0
    // 0xb68274: ldur            x0, [fp, #-0x10]
    // 0xb68278: stur            x1, [fp, #-0x38]
    // 0xb6827c: StoreField: r1->field_b = r0
    //     0xb6827c: stur            w0, [x1, #0xb]
    // 0xb68280: ldur            x0, [fp, #-0x28]
    // 0xb68284: StoreField: r1->field_13 = r0
    //     0xb68284: stur            w0, [x1, #0x13]
    // 0xb68288: r0 = Instance_TextOverflow
    //     0xb68288: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0xb6828c: ldr             x0, [x0, #0xe10]
    // 0xb68290: StoreField: r1->field_2b = r0
    //     0xb68290: stur            w0, [x1, #0x2b]
    // 0xb68294: r2 = 4
    //     0xb68294: movz            x2, #0x4
    // 0xb68298: StoreField: r1->field_37 = r2
    //     0xb68298: stur            w2, [x1, #0x37]
    // 0xb6829c: r0 = Container()
    //     0xb6829c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb682a0: stur            x0, [fp, #-0x10]
    // 0xb682a4: r16 = 100.000000
    //     0xb682a4: ldr             x16, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xb682a8: r30 = Instance_EdgeInsets
    //     0xb682a8: add             lr, PP, #0x55, lsl #12  ; [pp+0x55c98] Obj!EdgeInsets@d59061
    //     0xb682ac: ldr             lr, [lr, #0xc98]
    // 0xb682b0: stp             lr, x16, [SP, #8]
    // 0xb682b4: ldur            x16, [fp, #-0x38]
    // 0xb682b8: str             x16, [SP]
    // 0xb682bc: mov             x1, x0
    // 0xb682c0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, padding, 0x2, width, 0x1, null]
    //     0xb682c0: add             x4, PP, #0x44, lsl #12  ; [pp+0x44260] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "padding", 0x2, "width", 0x1, Null]
    //     0xb682c4: ldr             x4, [x4, #0x260]
    // 0xb682c8: r0 = Container()
    //     0xb682c8: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb682cc: ldur            x0, [fp, #-0x20]
    // 0xb682d0: LoadField: r2 = r0->field_13
    //     0xb682d0: ldur            w2, [x0, #0x13]
    // 0xb682d4: DecompressPointer r2
    //     0xb682d4: add             x2, x2, HEAP, lsl #32
    // 0xb682d8: ldur            x1, [fp, #-8]
    // 0xb682dc: r0 = _buildRatingWidget()
    //     0xb682dc: bl              #0xb68c60  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildRatingWidget
    // 0xb682e0: mov             x3, x0
    // 0xb682e4: ldur            x0, [fp, #-0x20]
    // 0xb682e8: stur            x3, [fp, #-0x38]
    // 0xb682ec: LoadField: r1 = r0->field_13
    //     0xb682ec: ldur            w1, [x0, #0x13]
    // 0xb682f0: DecompressPointer r1
    //     0xb682f0: add             x1, x1, HEAP, lsl #32
    // 0xb682f4: LoadField: r4 = r1->field_43
    //     0xb682f4: ldur            w4, [x1, #0x43]
    // 0xb682f8: DecompressPointer r4
    //     0xb682f8: add             x4, x4, HEAP, lsl #32
    // 0xb682fc: stur            x4, [fp, #-0x28]
    // 0xb68300: r1 = Null
    //     0xb68300: mov             x1, NULL
    // 0xb68304: r2 = 4
    //     0xb68304: movz            x2, #0x4
    // 0xb68308: r0 = AllocateArray()
    //     0xb68308: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6830c: mov             x1, x0
    // 0xb68310: ldur            x0, [fp, #-0x28]
    // 0xb68314: StoreField: r1->field_f = r0
    //     0xb68314: stur            w0, [x1, #0xf]
    // 0xb68318: r16 = " "
    //     0xb68318: ldr             x16, [PP, #0x8d8]  ; [pp+0x8d8] " "
    // 0xb6831c: StoreField: r1->field_13 = r16
    //     0xb6831c: stur            w16, [x1, #0x13]
    // 0xb68320: str             x1, [SP]
    // 0xb68324: r0 = _interpolate()
    //     0xb68324: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb68328: mov             x2, x0
    // 0xb6832c: ldur            x0, [fp, #-8]
    // 0xb68330: stur            x2, [fp, #-0x28]
    // 0xb68334: LoadField: r1 = r0->field_f
    //     0xb68334: ldur            w1, [x0, #0xf]
    // 0xb68338: DecompressPointer r1
    //     0xb68338: add             x1, x1, HEAP, lsl #32
    // 0xb6833c: cmp             w1, NULL
    // 0xb68340: b.eq            #0xb687e4
    // 0xb68344: r0 = of()
    //     0xb68344: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb68348: LoadField: r1 = r0->field_87
    //     0xb68348: ldur            w1, [x0, #0x87]
    // 0xb6834c: DecompressPointer r1
    //     0xb6834c: add             x1, x1, HEAP, lsl #32
    // 0xb68350: LoadField: r0 = r1->field_2b
    //     0xb68350: ldur            w0, [x1, #0x2b]
    // 0xb68354: DecompressPointer r0
    //     0xb68354: add             x0, x0, HEAP, lsl #32
    // 0xb68358: stur            x0, [fp, #-0x40]
    // 0xb6835c: r1 = Instance_Color
    //     0xb6835c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb68360: d0 = 0.700000
    //     0xb68360: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0xb68364: ldr             d0, [x17, #0xf48]
    // 0xb68368: r0 = withOpacity()
    //     0xb68368: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb6836c: r16 = 12.000000
    //     0xb6836c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb68370: ldr             x16, [x16, #0x9e8]
    // 0xb68374: stp             x0, x16, [SP]
    // 0xb68378: ldur            x1, [fp, #-0x40]
    // 0xb6837c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb6837c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb68380: ldr             x4, [x4, #0xaa0]
    // 0xb68384: r0 = copyWith()
    //     0xb68384: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb68388: stur            x0, [fp, #-0x40]
    // 0xb6838c: r0 = Text()
    //     0xb6838c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb68390: mov             x1, x0
    // 0xb68394: ldur            x0, [fp, #-0x28]
    // 0xb68398: stur            x1, [fp, #-0x48]
    // 0xb6839c: StoreField: r1->field_b = r0
    //     0xb6839c: stur            w0, [x1, #0xb]
    // 0xb683a0: ldur            x0, [fp, #-0x40]
    // 0xb683a4: StoreField: r1->field_13 = r0
    //     0xb683a4: stur            w0, [x1, #0x13]
    // 0xb683a8: r0 = WidgetSpan()
    //     0xb683a8: bl              #0x82d764  ; AllocateWidgetSpanStub -> WidgetSpan (size=0x18)
    // 0xb683ac: mov             x1, x0
    // 0xb683b0: ldur            x0, [fp, #-0x48]
    // 0xb683b4: stur            x1, [fp, #-0x28]
    // 0xb683b8: StoreField: r1->field_13 = r0
    //     0xb683b8: stur            w0, [x1, #0x13]
    // 0xb683bc: r0 = Instance_PlaceholderAlignment
    //     0xb683bc: add             x0, PP, #0x3c, lsl #12  ; [pp+0x3c0a0] Obj!PlaceholderAlignment@d76401
    //     0xb683c0: ldr             x0, [x0, #0xa0]
    // 0xb683c4: StoreField: r1->field_b = r0
    //     0xb683c4: stur            w0, [x1, #0xb]
    // 0xb683c8: ldur            x2, [fp, #-0x20]
    // 0xb683cc: LoadField: r0 = r2->field_13
    //     0xb683cc: ldur            w0, [x2, #0x13]
    // 0xb683d0: DecompressPointer r0
    //     0xb683d0: add             x0, x0, HEAP, lsl #32
    // 0xb683d4: LoadField: r3 = r0->field_4b
    //     0xb683d4: ldur            w3, [x0, #0x4b]
    // 0xb683d8: DecompressPointer r3
    //     0xb683d8: add             x3, x3, HEAP, lsl #32
    // 0xb683dc: str             x3, [SP]
    // 0xb683e0: r0 = _interpolateSingle()
    //     0xb683e0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb683e4: mov             x2, x0
    // 0xb683e8: ldur            x0, [fp, #-8]
    // 0xb683ec: stur            x2, [fp, #-0x40]
    // 0xb683f0: LoadField: r1 = r0->field_f
    //     0xb683f0: ldur            w1, [x0, #0xf]
    // 0xb683f4: DecompressPointer r1
    //     0xb683f4: add             x1, x1, HEAP, lsl #32
    // 0xb683f8: cmp             w1, NULL
    // 0xb683fc: b.eq            #0xb687e8
    // 0xb68400: r0 = of()
    //     0xb68400: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb68404: LoadField: r1 = r0->field_87
    //     0xb68404: ldur            w1, [x0, #0x87]
    // 0xb68408: DecompressPointer r1
    //     0xb68408: add             x1, x1, HEAP, lsl #32
    // 0xb6840c: LoadField: r0 = r1->field_2b
    //     0xb6840c: ldur            w0, [x1, #0x2b]
    // 0xb68410: DecompressPointer r0
    //     0xb68410: add             x0, x0, HEAP, lsl #32
    // 0xb68414: stur            x0, [fp, #-0x48]
    // 0xb68418: r1 = Instance_Color
    //     0xb68418: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb6841c: d0 = 0.250000
    //     0xb6841c: fmov            d0, #0.25000000
    // 0xb68420: r0 = withOpacity()
    //     0xb68420: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0xb68424: r16 = Instance_TextDecoration
    //     0xb68424: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fe30] Obj!TextDecoration@d68c71
    //     0xb68428: ldr             x16, [x16, #0xe30]
    // 0xb6842c: r30 = 12.000000
    //     0xb6842c: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb68430: ldr             lr, [lr, #0x9e8]
    // 0xb68434: stp             lr, x16, [SP, #8]
    // 0xb68438: str             x0, [SP]
    // 0xb6843c: ldur            x1, [fp, #-0x48]
    // 0xb68440: r4 = const [0, 0x4, 0x3, 0x1, color, 0x3, decoration, 0x1, fontSize, 0x2, null]
    //     0xb68440: add             x4, PP, #0x3f, lsl #12  ; [pp+0x3fb60] List(11) [0, 0x4, 0x3, 0x1, "color", 0x3, "decoration", 0x1, "fontSize", 0x2, Null]
    //     0xb68444: ldr             x4, [x4, #0xb60]
    // 0xb68448: r0 = copyWith()
    //     0xb68448: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb6844c: stur            x0, [fp, #-0x48]
    // 0xb68450: r0 = TextSpan()
    //     0xb68450: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb68454: mov             x3, x0
    // 0xb68458: ldur            x0, [fp, #-0x40]
    // 0xb6845c: stur            x3, [fp, #-0x50]
    // 0xb68460: StoreField: r3->field_b = r0
    //     0xb68460: stur            w0, [x3, #0xb]
    // 0xb68464: r0 = Instance__DeferringMouseCursor
    //     0xb68464: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb68468: ArrayStore: r3[0] = r0  ; List_4
    //     0xb68468: stur            w0, [x3, #0x17]
    // 0xb6846c: ldur            x1, [fp, #-0x48]
    // 0xb68470: StoreField: r3->field_7 = r1
    //     0xb68470: stur            w1, [x3, #7]
    // 0xb68474: r1 = Null
    //     0xb68474: mov             x1, NULL
    // 0xb68478: r2 = 4
    //     0xb68478: movz            x2, #0x4
    // 0xb6847c: r0 = AllocateArray()
    //     0xb6847c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb68480: mov             x2, x0
    // 0xb68484: ldur            x0, [fp, #-0x28]
    // 0xb68488: stur            x2, [fp, #-0x40]
    // 0xb6848c: StoreField: r2->field_f = r0
    //     0xb6848c: stur            w0, [x2, #0xf]
    // 0xb68490: ldur            x0, [fp, #-0x50]
    // 0xb68494: StoreField: r2->field_13 = r0
    //     0xb68494: stur            w0, [x2, #0x13]
    // 0xb68498: r1 = <InlineSpan>
    //     0xb68498: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0xb6849c: ldr             x1, [x1, #0xe40]
    // 0xb684a0: r0 = AllocateGrowableArray()
    //     0xb684a0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb684a4: mov             x1, x0
    // 0xb684a8: ldur            x0, [fp, #-0x40]
    // 0xb684ac: stur            x1, [fp, #-0x28]
    // 0xb684b0: StoreField: r1->field_f = r0
    //     0xb684b0: stur            w0, [x1, #0xf]
    // 0xb684b4: r2 = 4
    //     0xb684b4: movz            x2, #0x4
    // 0xb684b8: StoreField: r1->field_b = r2
    //     0xb684b8: stur            w2, [x1, #0xb]
    // 0xb684bc: r0 = TextSpan()
    //     0xb684bc: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0xb684c0: mov             x1, x0
    // 0xb684c4: ldur            x0, [fp, #-0x28]
    // 0xb684c8: stur            x1, [fp, #-0x40]
    // 0xb684cc: StoreField: r1->field_f = r0
    //     0xb684cc: stur            w0, [x1, #0xf]
    // 0xb684d0: r0 = Instance__DeferringMouseCursor
    //     0xb684d0: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0xb684d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xb684d4: stur            w0, [x1, #0x17]
    // 0xb684d8: r0 = RichText()
    //     0xb684d8: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xb684dc: mov             x1, x0
    // 0xb684e0: ldur            x2, [fp, #-0x40]
    // 0xb684e4: stur            x0, [fp, #-0x28]
    // 0xb684e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xb684e8: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xb684ec: r0 = RichText()
    //     0xb684ec: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xb684f0: r0 = Padding()
    //     0xb684f0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb684f4: mov             x3, x0
    // 0xb684f8: r0 = Instance_EdgeInsets
    //     0xb684f8: add             x0, PP, #0x55, lsl #12  ; [pp+0x55c98] Obj!EdgeInsets@d59061
    //     0xb684fc: ldr             x0, [x0, #0xc98]
    // 0xb68500: stur            x3, [fp, #-0x40]
    // 0xb68504: StoreField: r3->field_f = r0
    //     0xb68504: stur            w0, [x3, #0xf]
    // 0xb68508: ldur            x0, [fp, #-0x28]
    // 0xb6850c: StoreField: r3->field_b = r0
    //     0xb6850c: stur            w0, [x3, #0xb]
    // 0xb68510: r1 = Null
    //     0xb68510: mov             x1, NULL
    // 0xb68514: r2 = 6
    //     0xb68514: movz            x2, #0x6
    // 0xb68518: r0 = AllocateArray()
    //     0xb68518: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb6851c: mov             x2, x0
    // 0xb68520: ldur            x0, [fp, #-0x10]
    // 0xb68524: stur            x2, [fp, #-0x28]
    // 0xb68528: StoreField: r2->field_f = r0
    //     0xb68528: stur            w0, [x2, #0xf]
    // 0xb6852c: ldur            x0, [fp, #-0x38]
    // 0xb68530: StoreField: r2->field_13 = r0
    //     0xb68530: stur            w0, [x2, #0x13]
    // 0xb68534: ldur            x0, [fp, #-0x40]
    // 0xb68538: ArrayStore: r2[0] = r0  ; List_4
    //     0xb68538: stur            w0, [x2, #0x17]
    // 0xb6853c: r1 = <Widget>
    //     0xb6853c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb68540: r0 = AllocateGrowableArray()
    //     0xb68540: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb68544: mov             x1, x0
    // 0xb68548: ldur            x0, [fp, #-0x28]
    // 0xb6854c: stur            x1, [fp, #-0x10]
    // 0xb68550: StoreField: r1->field_f = r0
    //     0xb68550: stur            w0, [x1, #0xf]
    // 0xb68554: r0 = 6
    //     0xb68554: movz            x0, #0x6
    // 0xb68558: StoreField: r1->field_b = r0
    //     0xb68558: stur            w0, [x1, #0xb]
    // 0xb6855c: r0 = Column()
    //     0xb6855c: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb68560: mov             x2, x0
    // 0xb68564: r0 = Instance_Axis
    //     0xb68564: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb68568: stur            x2, [fp, #-0x28]
    // 0xb6856c: StoreField: r2->field_f = r0
    //     0xb6856c: stur            w0, [x2, #0xf]
    // 0xb68570: r3 = Instance_MainAxisAlignment
    //     0xb68570: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb68574: ldr             x3, [x3, #0xa08]
    // 0xb68578: StoreField: r2->field_13 = r3
    //     0xb68578: stur            w3, [x2, #0x13]
    // 0xb6857c: r4 = Instance_MainAxisSize
    //     0xb6857c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb68580: ldr             x4, [x4, #0xa10]
    // 0xb68584: ArrayStore: r2[0] = r4  ; List_4
    //     0xb68584: stur            w4, [x2, #0x17]
    // 0xb68588: r5 = Instance_CrossAxisAlignment
    //     0xb68588: add             x5, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb6858c: ldr             x5, [x5, #0x890]
    // 0xb68590: StoreField: r2->field_1b = r5
    //     0xb68590: stur            w5, [x2, #0x1b]
    // 0xb68594: r6 = Instance_VerticalDirection
    //     0xb68594: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb68598: ldr             x6, [x6, #0xa20]
    // 0xb6859c: StoreField: r2->field_23 = r6
    //     0xb6859c: stur            w6, [x2, #0x23]
    // 0xb685a0: r7 = Instance_Clip
    //     0xb685a0: add             x7, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb685a4: ldr             x7, [x7, #0x38]
    // 0xb685a8: StoreField: r2->field_2b = r7
    //     0xb685a8: stur            w7, [x2, #0x2b]
    // 0xb685ac: StoreField: r2->field_2f = rZR
    //     0xb685ac: stur            xzr, [x2, #0x2f]
    // 0xb685b0: ldur            x1, [fp, #-0x10]
    // 0xb685b4: StoreField: r2->field_b = r1
    //     0xb685b4: stur            w1, [x2, #0xb]
    // 0xb685b8: r1 = <FlexParentData>
    //     0xb685b8: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0xb685bc: ldr             x1, [x1, #0xe00]
    // 0xb685c0: r0 = Expanded()
    //     0xb685c0: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xb685c4: mov             x4, x0
    // 0xb685c8: r0 = 1
    //     0xb685c8: movz            x0, #0x1
    // 0xb685cc: stur            x4, [fp, #-0x10]
    // 0xb685d0: StoreField: r4->field_13 = r0
    //     0xb685d0: stur            x0, [x4, #0x13]
    // 0xb685d4: r0 = Instance_FlexFit
    //     0xb685d4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0xb685d8: ldr             x0, [x0, #0xe08]
    // 0xb685dc: StoreField: r4->field_1b = r0
    //     0xb685dc: stur            w0, [x4, #0x1b]
    // 0xb685e0: ldur            x0, [fp, #-0x28]
    // 0xb685e4: StoreField: r4->field_b = r0
    //     0xb685e4: stur            w0, [x4, #0xb]
    // 0xb685e8: ldur            x0, [fp, #-0x20]
    // 0xb685ec: LoadField: r2 = r0->field_13
    //     0xb685ec: ldur            w2, [x0, #0x13]
    // 0xb685f0: DecompressPointer r2
    //     0xb685f0: add             x2, x2, HEAP, lsl #32
    // 0xb685f4: ldur            x1, [fp, #-8]
    // 0xb685f8: ldur            x3, [fp, #-0x18]
    // 0xb685fc: r0 = _buildAddToBagButton()
    //     0xb685fc: bl              #0xb687ec  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildAddToBagButton
    // 0xb68600: r1 = Null
    //     0xb68600: mov             x1, NULL
    // 0xb68604: r2 = 4
    //     0xb68604: movz            x2, #0x4
    // 0xb68608: stur            x0, [fp, #-8]
    // 0xb6860c: r0 = AllocateArray()
    //     0xb6860c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb68610: mov             x2, x0
    // 0xb68614: ldur            x0, [fp, #-0x10]
    // 0xb68618: stur            x2, [fp, #-0x28]
    // 0xb6861c: StoreField: r2->field_f = r0
    //     0xb6861c: stur            w0, [x2, #0xf]
    // 0xb68620: ldur            x0, [fp, #-8]
    // 0xb68624: StoreField: r2->field_13 = r0
    //     0xb68624: stur            w0, [x2, #0x13]
    // 0xb68628: r1 = <Widget>
    //     0xb68628: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb6862c: r0 = AllocateGrowableArray()
    //     0xb6862c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb68630: mov             x1, x0
    // 0xb68634: ldur            x0, [fp, #-0x28]
    // 0xb68638: stur            x1, [fp, #-8]
    // 0xb6863c: StoreField: r1->field_f = r0
    //     0xb6863c: stur            w0, [x1, #0xf]
    // 0xb68640: r2 = 4
    //     0xb68640: movz            x2, #0x4
    // 0xb68644: StoreField: r1->field_b = r2
    //     0xb68644: stur            w2, [x1, #0xb]
    // 0xb68648: r0 = Row()
    //     0xb68648: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb6864c: mov             x1, x0
    // 0xb68650: r0 = Instance_Axis
    //     0xb68650: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb68654: stur            x1, [fp, #-0x10]
    // 0xb68658: StoreField: r1->field_f = r0
    //     0xb68658: stur            w0, [x1, #0xf]
    // 0xb6865c: r0 = Instance_MainAxisAlignment
    //     0xb6865c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0xb68660: ldr             x0, [x0, #0xa8]
    // 0xb68664: StoreField: r1->field_13 = r0
    //     0xb68664: stur            w0, [x1, #0x13]
    // 0xb68668: r0 = Instance_MainAxisSize
    //     0xb68668: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb6866c: ldr             x0, [x0, #0xa10]
    // 0xb68670: ArrayStore: r1[0] = r0  ; List_4
    //     0xb68670: stur            w0, [x1, #0x17]
    // 0xb68674: r0 = Instance_CrossAxisAlignment
    //     0xb68674: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb68678: ldr             x0, [x0, #0xa18]
    // 0xb6867c: StoreField: r1->field_1b = r0
    //     0xb6867c: stur            w0, [x1, #0x1b]
    // 0xb68680: r0 = Instance_VerticalDirection
    //     0xb68680: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb68684: ldr             x0, [x0, #0xa20]
    // 0xb68688: StoreField: r1->field_23 = r0
    //     0xb68688: stur            w0, [x1, #0x23]
    // 0xb6868c: r2 = Instance_Clip
    //     0xb6868c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb68690: ldr             x2, [x2, #0x38]
    // 0xb68694: StoreField: r1->field_2b = r2
    //     0xb68694: stur            w2, [x1, #0x2b]
    // 0xb68698: StoreField: r1->field_2f = rZR
    //     0xb68698: stur            xzr, [x1, #0x2f]
    // 0xb6869c: ldur            x3, [fp, #-8]
    // 0xb686a0: StoreField: r1->field_b = r3
    //     0xb686a0: stur            w3, [x1, #0xb]
    // 0xb686a4: r0 = Padding()
    //     0xb686a4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb686a8: mov             x3, x0
    // 0xb686ac: r0 = Instance_EdgeInsets
    //     0xb686ac: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f850] Obj!EdgeInsets@d57801
    //     0xb686b0: ldr             x0, [x0, #0x850]
    // 0xb686b4: stur            x3, [fp, #-8]
    // 0xb686b8: StoreField: r3->field_f = r0
    //     0xb686b8: stur            w0, [x3, #0xf]
    // 0xb686bc: ldur            x0, [fp, #-0x10]
    // 0xb686c0: StoreField: r3->field_b = r0
    //     0xb686c0: stur            w0, [x3, #0xb]
    // 0xb686c4: r1 = Null
    //     0xb686c4: mov             x1, NULL
    // 0xb686c8: r2 = 4
    //     0xb686c8: movz            x2, #0x4
    // 0xb686cc: r0 = AllocateArray()
    //     0xb686cc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb686d0: mov             x2, x0
    // 0xb686d4: ldur            x0, [fp, #-0x30]
    // 0xb686d8: stur            x2, [fp, #-0x10]
    // 0xb686dc: StoreField: r2->field_f = r0
    //     0xb686dc: stur            w0, [x2, #0xf]
    // 0xb686e0: ldur            x0, [fp, #-8]
    // 0xb686e4: StoreField: r2->field_13 = r0
    //     0xb686e4: stur            w0, [x2, #0x13]
    // 0xb686e8: r1 = <Widget>
    //     0xb686e8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb686ec: r0 = AllocateGrowableArray()
    //     0xb686ec: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb686f0: mov             x1, x0
    // 0xb686f4: ldur            x0, [fp, #-0x10]
    // 0xb686f8: stur            x1, [fp, #-8]
    // 0xb686fc: StoreField: r1->field_f = r0
    //     0xb686fc: stur            w0, [x1, #0xf]
    // 0xb68700: r0 = 4
    //     0xb68700: movz            x0, #0x4
    // 0xb68704: StoreField: r1->field_b = r0
    //     0xb68704: stur            w0, [x1, #0xb]
    // 0xb68708: r0 = Column()
    //     0xb68708: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xb6870c: mov             x1, x0
    // 0xb68710: r0 = Instance_Axis
    //     0xb68710: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xb68714: stur            x1, [fp, #-0x10]
    // 0xb68718: StoreField: r1->field_f = r0
    //     0xb68718: stur            w0, [x1, #0xf]
    // 0xb6871c: r0 = Instance_MainAxisAlignment
    //     0xb6871c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb68720: ldr             x0, [x0, #0xa08]
    // 0xb68724: StoreField: r1->field_13 = r0
    //     0xb68724: stur            w0, [x1, #0x13]
    // 0xb68728: r0 = Instance_MainAxisSize
    //     0xb68728: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0xb6872c: ldr             x0, [x0, #0xdd0]
    // 0xb68730: ArrayStore: r1[0] = r0  ; List_4
    //     0xb68730: stur            w0, [x1, #0x17]
    // 0xb68734: r0 = Instance_CrossAxisAlignment
    //     0xb68734: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0xb68738: ldr             x0, [x0, #0x890]
    // 0xb6873c: StoreField: r1->field_1b = r0
    //     0xb6873c: stur            w0, [x1, #0x1b]
    // 0xb68740: r0 = Instance_VerticalDirection
    //     0xb68740: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb68744: ldr             x0, [x0, #0xa20]
    // 0xb68748: StoreField: r1->field_23 = r0
    //     0xb68748: stur            w0, [x1, #0x23]
    // 0xb6874c: r0 = Instance_Clip
    //     0xb6874c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb68750: ldr             x0, [x0, #0x38]
    // 0xb68754: StoreField: r1->field_2b = r0
    //     0xb68754: stur            w0, [x1, #0x2b]
    // 0xb68758: StoreField: r1->field_2f = rZR
    //     0xb68758: stur            xzr, [x1, #0x2f]
    // 0xb6875c: ldur            x0, [fp, #-8]
    // 0xb68760: StoreField: r1->field_b = r0
    //     0xb68760: stur            w0, [x1, #0xb]
    // 0xb68764: r0 = InkWell()
    //     0xb68764: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb68768: mov             x3, x0
    // 0xb6876c: ldur            x0, [fp, #-0x10]
    // 0xb68770: stur            x3, [fp, #-8]
    // 0xb68774: StoreField: r3->field_b = r0
    //     0xb68774: stur            w0, [x3, #0xb]
    // 0xb68778: ldur            x2, [fp, #-0x20]
    // 0xb6877c: r1 = Function '<anonymous closure>':.
    //     0xb6877c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ca0] AnonymousClosure: (0xb6922c), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::glassThemeSlider (0xb67fdc)
    //     0xb68780: ldr             x1, [x1, #0xca0]
    // 0xb68784: r0 = AllocateClosure()
    //     0xb68784: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb68788: mov             x1, x0
    // 0xb6878c: ldur            x0, [fp, #-8]
    // 0xb68790: StoreField: r0->field_f = r1
    //     0xb68790: stur            w1, [x0, #0xf]
    // 0xb68794: r1 = true
    //     0xb68794: add             x1, NULL, #0x20  ; true
    // 0xb68798: StoreField: r0->field_43 = r1
    //     0xb68798: stur            w1, [x0, #0x43]
    // 0xb6879c: r2 = Instance_BoxShape
    //     0xb6879c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb687a0: ldr             x2, [x2, #0x80]
    // 0xb687a4: StoreField: r0->field_47 = r2
    //     0xb687a4: stur            w2, [x0, #0x47]
    // 0xb687a8: StoreField: r0->field_6f = r1
    //     0xb687a8: stur            w1, [x0, #0x6f]
    // 0xb687ac: r2 = false
    //     0xb687ac: add             x2, NULL, #0x30  ; false
    // 0xb687b0: StoreField: r0->field_73 = r2
    //     0xb687b0: stur            w2, [x0, #0x73]
    // 0xb687b4: StoreField: r0->field_83 = r1
    //     0xb687b4: stur            w1, [x0, #0x83]
    // 0xb687b8: StoreField: r0->field_7b = r2
    //     0xb687b8: stur            w2, [x0, #0x7b]
    // 0xb687bc: LeaveFrame
    //     0xb687bc: mov             SP, fp
    //     0xb687c0: ldp             fp, lr, [SP], #0x10
    // 0xb687c4: ret
    //     0xb687c4: ret             
    // 0xb687c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb687c8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb687cc: b               #0xb68000
    // 0xb687d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb687d0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb687d4: r9 = _imagePageController
    //     0xb687d4: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ca8] Field <_ProductGroupCarouselItemViewState@1589000994._imagePageController@1589000994>: late (offset: 0x18)
    //     0xb687d8: ldr             x9, [x9, #0xca8]
    // 0xb687dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xb687dc: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xb687e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb687e0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb687e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb687e4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb687e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb687e8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildAddToBagButton(/* No info */) {
    // ** addr: 0xb687ec, size: 0x294
    // 0xb687ec: EnterFrame
    //     0xb687ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb687f0: mov             fp, SP
    // 0xb687f4: AllocStack(0x40)
    //     0xb687f4: sub             SP, SP, #0x40
    // 0xb687f8: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xb687f8: stur            x1, [fp, #-8]
    //     0xb687fc: stur            x2, [fp, #-0x10]
    //     0xb68800: stur            x3, [fp, #-0x18]
    // 0xb68804: CheckStackOverflow
    //     0xb68804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68808: cmp             SP, x16
    //     0xb6880c: b.ls            #0xb68a70
    // 0xb68810: r1 = 3
    //     0xb68810: movz            x1, #0x3
    // 0xb68814: r0 = AllocateContext()
    //     0xb68814: bl              #0x16f6108  ; AllocateContextStub
    // 0xb68818: mov             x3, x0
    // 0xb6881c: ldur            x2, [fp, #-8]
    // 0xb68820: stur            x3, [fp, #-0x20]
    // 0xb68824: StoreField: r3->field_f = r2
    //     0xb68824: stur            w2, [x3, #0xf]
    // 0xb68828: ldur            x4, [fp, #-0x10]
    // 0xb6882c: StoreField: r3->field_13 = r4
    //     0xb6882c: stur            w4, [x3, #0x13]
    // 0xb68830: ldur            x5, [fp, #-0x18]
    // 0xb68834: r0 = BoxInt64Instr(r5)
    //     0xb68834: sbfiz           x0, x5, #1, #0x1f
    //     0xb68838: cmp             x5, x0, asr #1
    //     0xb6883c: b.eq            #0xb68848
    //     0xb68840: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0xb68844: stur            x5, [x0, #7]
    // 0xb68848: ArrayStore: r3[0] = r0  ; List_4
    //     0xb68848: stur            w0, [x3, #0x17]
    // 0xb6884c: LoadField: r0 = r2->field_b
    //     0xb6884c: ldur            w0, [x2, #0xb]
    // 0xb68850: DecompressPointer r0
    //     0xb68850: add             x0, x0, HEAP, lsl #32
    // 0xb68854: cmp             w0, NULL
    // 0xb68858: b.eq            #0xb68a78
    // 0xb6885c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb6885c: ldur            w1, [x0, #0x17]
    // 0xb68860: DecompressPointer r1
    //     0xb68860: add             x1, x1, HEAP, lsl #32
    // 0xb68864: LoadField: r0 = r1->field_1f
    //     0xb68864: ldur            w0, [x1, #0x1f]
    // 0xb68868: DecompressPointer r0
    //     0xb68868: add             x0, x0, HEAP, lsl #32
    // 0xb6886c: cmp             w0, NULL
    // 0xb68870: b.ne            #0xb6887c
    // 0xb68874: r0 = Null
    //     0xb68874: mov             x0, NULL
    // 0xb68878: b               #0xb68888
    // 0xb6887c: LoadField: r5 = r0->field_7
    //     0xb6887c: ldur            w5, [x0, #7]
    // 0xb68880: DecompressPointer r5
    //     0xb68880: add             x5, x5, HEAP, lsl #32
    // 0xb68884: mov             x0, x5
    // 0xb68888: cmp             w0, NULL
    // 0xb6888c: b.eq            #0xb688c4
    // 0xb68890: tbnz            w0, #4, #0xb688c4
    // 0xb68894: LoadField: r0 = r1->field_3f
    //     0xb68894: ldur            w0, [x1, #0x3f]
    // 0xb68898: DecompressPointer r0
    //     0xb68898: add             x0, x0, HEAP, lsl #32
    // 0xb6889c: cmp             w0, NULL
    // 0xb688a0: b.ne            #0xb688ac
    // 0xb688a4: r0 = Null
    //     0xb688a4: mov             x0, NULL
    // 0xb688a8: b               #0xb688b8
    // 0xb688ac: LoadField: r1 = r0->field_23
    //     0xb688ac: ldur            w1, [x0, #0x23]
    // 0xb688b0: DecompressPointer r1
    //     0xb688b0: add             x1, x1, HEAP, lsl #32
    // 0xb688b4: mov             x0, x1
    // 0xb688b8: cmp             w0, NULL
    // 0xb688bc: b.eq            #0xb688c4
    // 0xb688c0: tbz             w0, #4, #0xb688d4
    // 0xb688c4: r0 = Instance_SizedBox
    //     0xb688c4: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb688c8: LeaveFrame
    //     0xb688c8: mov             SP, fp
    //     0xb688cc: ldp             fp, lr, [SP], #0x10
    // 0xb688d0: ret
    //     0xb688d0: ret             
    // 0xb688d4: LoadField: r0 = r4->field_4f
    //     0xb688d4: ldur            w0, [x4, #0x4f]
    // 0xb688d8: DecompressPointer r0
    //     0xb688d8: add             x0, x0, HEAP, lsl #32
    // 0xb688dc: cmp             w0, NULL
    // 0xb688e0: b.eq            #0xb688e8
    // 0xb688e4: tbnz            w0, #4, #0xb688f8
    // 0xb688e8: mov             x2, x3
    // 0xb688ec: r0 = Instance_Color
    //     0xb688ec: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb688f0: ldr             x0, [x0, #0xf88]
    // 0xb688f4: b               #0xb6891c
    // 0xb688f8: LoadField: r1 = r2->field_f
    //     0xb688f8: ldur            w1, [x2, #0xf]
    // 0xb688fc: DecompressPointer r1
    //     0xb688fc: add             x1, x1, HEAP, lsl #32
    // 0xb68900: cmp             w1, NULL
    // 0xb68904: b.eq            #0xb68a7c
    // 0xb68908: r0 = of()
    //     0xb68908: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb6890c: LoadField: r1 = r0->field_5b
    //     0xb6890c: ldur            w1, [x0, #0x5b]
    // 0xb68910: DecompressPointer r1
    //     0xb68910: add             x1, x1, HEAP, lsl #32
    // 0xb68914: mov             x0, x1
    // 0xb68918: ldur            x2, [fp, #-0x20]
    // 0xb6891c: stur            x0, [fp, #-8]
    // 0xb68920: r0 = Radius()
    //     0xb68920: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb68924: d0 = 100.000000
    //     0xb68924: ldr             d0, [PP, #0x5920]  ; [pp+0x5920] IMM: double(100) from 0x4059000000000000
    // 0xb68928: stur            x0, [fp, #-0x10]
    // 0xb6892c: StoreField: r0->field_7 = d0
    //     0xb6892c: stur            d0, [x0, #7]
    // 0xb68930: StoreField: r0->field_f = d0
    //     0xb68930: stur            d0, [x0, #0xf]
    // 0xb68934: r0 = BorderRadius()
    //     0xb68934: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb68938: mov             x1, x0
    // 0xb6893c: ldur            x0, [fp, #-0x10]
    // 0xb68940: stur            x1, [fp, #-0x28]
    // 0xb68944: StoreField: r1->field_7 = r0
    //     0xb68944: stur            w0, [x1, #7]
    // 0xb68948: StoreField: r1->field_b = r0
    //     0xb68948: stur            w0, [x1, #0xb]
    // 0xb6894c: StoreField: r1->field_f = r0
    //     0xb6894c: stur            w0, [x1, #0xf]
    // 0xb68950: StoreField: r1->field_13 = r0
    //     0xb68950: stur            w0, [x1, #0x13]
    // 0xb68954: r0 = BoxDecoration()
    //     0xb68954: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xb68958: mov             x1, x0
    // 0xb6895c: ldur            x0, [fp, #-8]
    // 0xb68960: stur            x1, [fp, #-0x10]
    // 0xb68964: StoreField: r1->field_7 = r0
    //     0xb68964: stur            w0, [x1, #7]
    // 0xb68968: ldur            x0, [fp, #-0x28]
    // 0xb6896c: StoreField: r1->field_13 = r0
    //     0xb6896c: stur            w0, [x1, #0x13]
    // 0xb68970: r0 = Instance_BoxShape
    //     0xb68970: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb68974: ldr             x0, [x0, #0x80]
    // 0xb68978: StoreField: r1->field_23 = r0
    //     0xb68978: stur            w0, [x1, #0x23]
    // 0xb6897c: ldur            x2, [fp, #-0x20]
    // 0xb68980: LoadField: r3 = r2->field_13
    //     0xb68980: ldur            w3, [x2, #0x13]
    // 0xb68984: DecompressPointer r3
    //     0xb68984: add             x3, x3, HEAP, lsl #32
    // 0xb68988: LoadField: r4 = r3->field_4f
    //     0xb68988: ldur            w4, [x3, #0x4f]
    // 0xb6898c: DecompressPointer r4
    //     0xb6898c: add             x4, x4, HEAP, lsl #32
    // 0xb68990: cmp             w4, NULL
    // 0xb68994: b.eq            #0xb6899c
    // 0xb68998: tbnz            w4, #4, #0xb689a4
    // 0xb6899c: r0 = Instance_SizedBox
    //     0xb6899c: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb689a0: b               #0xb689d4
    // 0xb689a4: r0 = SvgPicture()
    //     0xb689a4: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb689a8: stur            x0, [fp, #-8]
    // 0xb689ac: r16 = Instance_BoxFit
    //     0xb689ac: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb689b0: ldr             x16, [x16, #0xb18]
    // 0xb689b4: str             x16, [SP]
    // 0xb689b8: mov             x1, x0
    // 0xb689bc: r2 = "assets/images/add_to_bag_glass.svg"
    //     0xb689bc: add             x2, PP, #0x55, lsl #12  ; [pp+0x55cf0] "assets/images/add_to_bag_glass.svg"
    //     0xb689c0: ldr             x2, [x2, #0xcf0]
    // 0xb689c4: r4 = const [0, 0x3, 0x1, 0x2, fit, 0x2, null]
    //     0xb689c4: add             x4, PP, #0x34, lsl #12  ; [pp+0x340b0] List(7) [0, 0x3, 0x1, 0x2, "fit", 0x2, Null]
    //     0xb689c8: ldr             x4, [x4, #0xb0]
    // 0xb689cc: r0 = SvgPicture.asset()
    //     0xb689cc: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb689d0: ldur            x0, [fp, #-8]
    // 0xb689d4: stur            x0, [fp, #-8]
    // 0xb689d8: r0 = InkWell()
    //     0xb689d8: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb689dc: mov             x3, x0
    // 0xb689e0: ldur            x0, [fp, #-8]
    // 0xb689e4: stur            x3, [fp, #-0x28]
    // 0xb689e8: StoreField: r3->field_b = r0
    //     0xb689e8: stur            w0, [x3, #0xb]
    // 0xb689ec: ldur            x2, [fp, #-0x20]
    // 0xb689f0: r1 = Function '<anonymous closure>':.
    //     0xb689f0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55cf8] AnonymousClosure: (0xb68a80), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildAddToBagButton (0xb687ec)
    //     0xb689f4: ldr             x1, [x1, #0xcf8]
    // 0xb689f8: r0 = AllocateClosure()
    //     0xb689f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb689fc: mov             x1, x0
    // 0xb68a00: ldur            x0, [fp, #-0x28]
    // 0xb68a04: StoreField: r0->field_f = r1
    //     0xb68a04: stur            w1, [x0, #0xf]
    // 0xb68a08: r1 = true
    //     0xb68a08: add             x1, NULL, #0x20  ; true
    // 0xb68a0c: StoreField: r0->field_43 = r1
    //     0xb68a0c: stur            w1, [x0, #0x43]
    // 0xb68a10: r2 = Instance_BoxShape
    //     0xb68a10: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb68a14: ldr             x2, [x2, #0x80]
    // 0xb68a18: StoreField: r0->field_47 = r2
    //     0xb68a18: stur            w2, [x0, #0x47]
    // 0xb68a1c: StoreField: r0->field_6f = r1
    //     0xb68a1c: stur            w1, [x0, #0x6f]
    // 0xb68a20: r2 = false
    //     0xb68a20: add             x2, NULL, #0x30  ; false
    // 0xb68a24: StoreField: r0->field_73 = r2
    //     0xb68a24: stur            w2, [x0, #0x73]
    // 0xb68a28: StoreField: r0->field_83 = r1
    //     0xb68a28: stur            w1, [x0, #0x83]
    // 0xb68a2c: StoreField: r0->field_7b = r2
    //     0xb68a2c: stur            w2, [x0, #0x7b]
    // 0xb68a30: r0 = Container()
    //     0xb68a30: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0xb68a34: stur            x0, [fp, #-8]
    // 0xb68a38: ldur            x16, [fp, #-0x10]
    // 0xb68a3c: r30 = Instance_EdgeInsets
    //     0xb68a3c: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0xb68a40: ldr             lr, [lr, #0x980]
    // 0xb68a44: stp             lr, x16, [SP, #8]
    // 0xb68a48: ldur            x16, [fp, #-0x28]
    // 0xb68a4c: str             x16, [SP]
    // 0xb68a50: mov             x1, x0
    // 0xb68a54: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x1, padding, 0x2, null]
    //     0xb68a54: add             x4, PP, #0x36, lsl #12  ; [pp+0x36b40] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x1, "padding", 0x2, Null]
    //     0xb68a58: ldr             x4, [x4, #0xb40]
    // 0xb68a5c: r0 = Container()
    //     0xb68a5c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xb68a60: ldur            x0, [fp, #-8]
    // 0xb68a64: LeaveFrame
    //     0xb68a64: mov             SP, fp
    //     0xb68a68: ldp             fp, lr, [SP], #0x10
    // 0xb68a6c: ret
    //     0xb68a6c: ret             
    // 0xb68a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68a70: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68a74: b               #0xb68810
    // 0xb68a78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb68a78: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb68a7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb68a7c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb68a80, size: 0x68
    // 0xb68a80: EnterFrame
    //     0xb68a80: stp             fp, lr, [SP, #-0x10]!
    //     0xb68a84: mov             fp, SP
    // 0xb68a88: ldr             x0, [fp, #0x10]
    // 0xb68a8c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb68a8c: ldur            w1, [x0, #0x17]
    // 0xb68a90: DecompressPointer r1
    //     0xb68a90: add             x1, x1, HEAP, lsl #32
    // 0xb68a94: CheckStackOverflow
    //     0xb68a94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68a98: cmp             SP, x16
    //     0xb68a9c: b.ls            #0xb68ae0
    // 0xb68aa0: LoadField: r0 = r1->field_f
    //     0xb68aa0: ldur            w0, [x1, #0xf]
    // 0xb68aa4: DecompressPointer r0
    //     0xb68aa4: add             x0, x0, HEAP, lsl #32
    // 0xb68aa8: LoadField: r2 = r1->field_13
    //     0xb68aa8: ldur            w2, [x1, #0x13]
    // 0xb68aac: DecompressPointer r2
    //     0xb68aac: add             x2, x2, HEAP, lsl #32
    // 0xb68ab0: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xb68ab0: ldur            w3, [x1, #0x17]
    // 0xb68ab4: DecompressPointer r3
    //     0xb68ab4: add             x3, x3, HEAP, lsl #32
    // 0xb68ab8: r1 = LoadInt32Instr(r3)
    //     0xb68ab8: sbfx            x1, x3, #1, #0x1f
    //     0xb68abc: tbz             w3, #0, #0xb68ac4
    //     0xb68ac0: ldur            x1, [x3, #7]
    // 0xb68ac4: mov             x3, x1
    // 0xb68ac8: mov             x1, x0
    // 0xb68acc: r0 = _onAddToBagPressed()
    //     0xb68acc: bl              #0xb68ae8  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onAddToBagPressed
    // 0xb68ad0: r0 = Null
    //     0xb68ad0: mov             x0, NULL
    // 0xb68ad4: LeaveFrame
    //     0xb68ad4: mov             SP, fp
    //     0xb68ad8: ldp             fp, lr, [SP], #0x10
    // 0xb68adc: ret
    //     0xb68adc: ret             
    // 0xb68ae0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68ae0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68ae4: b               #0xb68aa0
  }
  _ _onAddToBagPressed(/* No info */) {
    // ** addr: 0xb68ae8, size: 0x178
    // 0xb68ae8: EnterFrame
    //     0xb68ae8: stp             fp, lr, [SP, #-0x10]!
    //     0xb68aec: mov             fp, SP
    // 0xb68af0: AllocStack(0x38)
    //     0xb68af0: sub             SP, SP, #0x38
    // 0xb68af4: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */)
    //     0xb68af4: mov             x0, x1
    //     0xb68af8: stur            x1, [fp, #-8]
    //     0xb68afc: mov             x1, x3
    //     0xb68b00: stur            x2, [fp, #-0x10]
    //     0xb68b04: stur            x3, [fp, #-0x18]
    // 0xb68b08: CheckStackOverflow
    //     0xb68b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68b0c: cmp             SP, x16
    //     0xb68b10: b.ls            #0xb68c48
    // 0xb68b14: LoadField: r3 = r2->field_4f
    //     0xb68b14: ldur            w3, [x2, #0x4f]
    // 0xb68b18: DecompressPointer r3
    //     0xb68b18: add             x3, x3, HEAP, lsl #32
    // 0xb68b1c: cmp             w3, NULL
    // 0xb68b20: b.eq            #0xb68b28
    // 0xb68b24: tbz             w3, #4, #0xb68c38
    // 0xb68b28: LoadField: r3 = r0->field_b
    //     0xb68b28: ldur            w3, [x0, #0xb]
    // 0xb68b2c: DecompressPointer r3
    //     0xb68b2c: add             x3, x3, HEAP, lsl #32
    // 0xb68b30: cmp             w3, NULL
    // 0xb68b34: b.eq            #0xb68c50
    // 0xb68b38: LoadField: r4 = r2->field_2b
    //     0xb68b38: ldur            w4, [x2, #0x2b]
    // 0xb68b3c: DecompressPointer r4
    //     0xb68b3c: add             x4, x4, HEAP, lsl #32
    // 0xb68b40: LoadField: r5 = r2->field_3b
    //     0xb68b40: ldur            w5, [x2, #0x3b]
    // 0xb68b44: DecompressPointer r5
    //     0xb68b44: add             x5, x5, HEAP, lsl #32
    // 0xb68b48: cmp             w5, NULL
    // 0xb68b4c: b.ne            #0xb68b58
    // 0xb68b50: r6 = Null
    //     0xb68b50: mov             x6, NULL
    // 0xb68b54: b               #0xb68b60
    // 0xb68b58: LoadField: r6 = r5->field_7
    //     0xb68b58: ldur            w6, [x5, #7]
    // 0xb68b5c: DecompressPointer r6
    //     0xb68b5c: add             x6, x6, HEAP, lsl #32
    // 0xb68b60: cmp             w5, NULL
    // 0xb68b64: b.ne            #0xb68b70
    // 0xb68b68: r5 = Null
    //     0xb68b68: mov             x5, NULL
    // 0xb68b6c: b               #0xb68b7c
    // 0xb68b70: LoadField: r7 = r5->field_b
    //     0xb68b70: ldur            w7, [x5, #0xb]
    // 0xb68b74: DecompressPointer r7
    //     0xb68b74: add             x7, x7, HEAP, lsl #32
    // 0xb68b78: mov             x5, x7
    // 0xb68b7c: LoadField: r7 = r3->field_37
    //     0xb68b7c: ldur            w7, [x3, #0x37]
    // 0xb68b80: DecompressPointer r7
    //     0xb68b80: add             x7, x7, HEAP, lsl #32
    // 0xb68b84: stp             x4, x7, [SP, #0x10]
    // 0xb68b88: stp             x5, x6, [SP]
    // 0xb68b8c: r4 = 0
    //     0xb68b8c: movz            x4, #0
    // 0xb68b90: ldr             x0, [SP, #0x18]
    // 0xb68b94: r16 = UnlinkedCall_0x613b5c
    //     0xb68b94: add             x16, PP, #0x55, lsl #12  ; [pp+0x55d00] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb68b98: add             x16, x16, #0xd00
    // 0xb68b9c: ldp             x5, lr, [x16]
    // 0xb68ba0: blr             lr
    // 0xb68ba4: ldur            x0, [fp, #-8]
    // 0xb68ba8: LoadField: r2 = r0->field_b
    //     0xb68ba8: ldur            w2, [x0, #0xb]
    // 0xb68bac: DecompressPointer r2
    //     0xb68bac: add             x2, x2, HEAP, lsl #32
    // 0xb68bb0: cmp             w2, NULL
    // 0xb68bb4: b.eq            #0xb68c54
    // 0xb68bb8: LoadField: r3 = r2->field_1f
    //     0xb68bb8: ldur            w3, [x2, #0x1f]
    // 0xb68bbc: DecompressPointer r3
    //     0xb68bbc: add             x3, x3, HEAP, lsl #32
    // 0xb68bc0: LoadField: r4 = r2->field_b
    //     0xb68bc0: ldur            w4, [x2, #0xb]
    // 0xb68bc4: DecompressPointer r4
    //     0xb68bc4: add             x4, x4, HEAP, lsl #32
    // 0xb68bc8: cmp             w4, NULL
    // 0xb68bcc: b.eq            #0xb68c58
    // 0xb68bd0: LoadField: r0 = r4->field_b
    //     0xb68bd0: ldur            w0, [x4, #0xb]
    // 0xb68bd4: r1 = LoadInt32Instr(r0)
    //     0xb68bd4: sbfx            x1, x0, #1, #0x1f
    // 0xb68bd8: mov             x0, x1
    // 0xb68bdc: ldur            x1, [fp, #-0x18]
    // 0xb68be0: cmp             x1, x0
    // 0xb68be4: b.hs            #0xb68c5c
    // 0xb68be8: LoadField: r0 = r4->field_f
    //     0xb68be8: ldur            w0, [x4, #0xf]
    // 0xb68bec: DecompressPointer r0
    //     0xb68bec: add             x0, x0, HEAP, lsl #32
    // 0xb68bf0: ldur            x1, [fp, #-0x18]
    // 0xb68bf4: ArrayLoad: r4 = r0[r1]  ; Unknown_4
    //     0xb68bf4: add             x16, x0, x1, lsl #2
    //     0xb68bf8: ldur            w4, [x16, #0xf]
    // 0xb68bfc: DecompressPointer r4
    //     0xb68bfc: add             x4, x4, HEAP, lsl #32
    // 0xb68c00: LoadField: r0 = r4->field_b3
    //     0xb68c00: ldur            w0, [x4, #0xb3]
    // 0xb68c04: DecompressPointer r0
    //     0xb68c04: add             x0, x0, HEAP, lsl #32
    // 0xb68c08: cmp             w0, NULL
    // 0xb68c0c: b.ne            #0xb68c14
    // 0xb68c10: r0 = ""
    //     0xb68c10: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb68c14: LoadField: r1 = r2->field_47
    //     0xb68c14: ldur            w1, [x2, #0x47]
    // 0xb68c18: DecompressPointer r1
    //     0xb68c18: add             x1, x1, HEAP, lsl #32
    // 0xb68c1c: ldur            x16, [fp, #-0x10]
    // 0xb68c20: stp             x16, x1, [SP, #0x10]
    // 0xb68c24: stp             x0, x3, [SP]
    // 0xb68c28: mov             x0, x1
    // 0xb68c2c: ClosureCall
    //     0xb68c2c: ldr             x4, [PP, #0x9b8]  ; [pp+0x9b8] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xb68c30: ldur            x2, [x0, #0x1f]
    //     0xb68c34: blr             x2
    // 0xb68c38: r0 = Null
    //     0xb68c38: mov             x0, NULL
    // 0xb68c3c: LeaveFrame
    //     0xb68c3c: mov             SP, fp
    //     0xb68c40: ldp             fp, lr, [SP], #0x10
    // 0xb68c44: ret
    //     0xb68c44: ret             
    // 0xb68c48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb68c48: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb68c4c: b               #0xb68b14
    // 0xb68c50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb68c50: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb68c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb68c54: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb68c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb68c58: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb68c5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb68c5c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildRatingWidget(/* No info */) {
    // ** addr: 0xb68c60, size: 0x5cc
    // 0xb68c60: EnterFrame
    //     0xb68c60: stp             fp, lr, [SP, #-0x10]!
    //     0xb68c64: mov             fp, SP
    // 0xb68c68: AllocStack(0x50)
    //     0xb68c68: sub             SP, SP, #0x50
    // 0xb68c6c: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb68c6c: stur            x1, [fp, #-8]
    //     0xb68c70: stur            x2, [fp, #-0x10]
    // 0xb68c74: CheckStackOverflow
    //     0xb68c74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb68c78: cmp             SP, x16
    //     0xb68c7c: b.ls            #0xb691e8
    // 0xb68c80: LoadField: r0 = r2->field_7b
    //     0xb68c80: ldur            w0, [x2, #0x7b]
    // 0xb68c84: DecompressPointer r0
    //     0xb68c84: add             x0, x0, HEAP, lsl #32
    // 0xb68c88: cmp             w0, NULL
    // 0xb68c8c: b.ne            #0xb68c98
    // 0xb68c90: r0 = Null
    //     0xb68c90: mov             x0, NULL
    // 0xb68c94: b               #0xb68ca4
    // 0xb68c98: LoadField: r3 = r0->field_7
    //     0xb68c98: ldur            w3, [x0, #7]
    // 0xb68c9c: DecompressPointer r3
    //     0xb68c9c: add             x3, x3, HEAP, lsl #32
    // 0xb68ca0: mov             x0, x3
    // 0xb68ca4: r3 = LoadClassIdInstr(r0)
    //     0xb68ca4: ldur            x3, [x0, #-1]
    //     0xb68ca8: ubfx            x3, x3, #0xc, #0x14
    // 0xb68cac: r16 = 0.000000
    //     0xb68cac: ldr             x16, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb68cb0: stp             x16, x0, [SP]
    // 0xb68cb4: mov             x0, x3
    // 0xb68cb8: mov             lr, x0
    // 0xb68cbc: ldr             lr, [x21, lr, lsl #3]
    // 0xb68cc0: blr             lr
    // 0xb68cc4: tbz             w0, #4, #0xb68cec
    // 0xb68cc8: ldur            x1, [fp, #-0x10]
    // 0xb68ccc: LoadField: r0 = r1->field_7b
    //     0xb68ccc: ldur            w0, [x1, #0x7b]
    // 0xb68cd0: DecompressPointer r0
    //     0xb68cd0: add             x0, x0, HEAP, lsl #32
    // 0xb68cd4: cmp             w0, NULL
    // 0xb68cd8: b.eq            #0xb68cec
    // 0xb68cdc: LoadField: r2 = r0->field_7
    //     0xb68cdc: ldur            w2, [x0, #7]
    // 0xb68ce0: DecompressPointer r2
    //     0xb68ce0: add             x2, x2, HEAP, lsl #32
    // 0xb68ce4: cmp             w2, NULL
    // 0xb68ce8: b.ne            #0xb68cfc
    // 0xb68cec: r0 = Instance_SizedBox
    //     0xb68cec: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0xb68cf0: LeaveFrame
    //     0xb68cf0: mov             SP, fp
    //     0xb68cf4: ldp             fp, lr, [SP], #0x10
    // 0xb68cf8: ret
    //     0xb68cf8: ret             
    // 0xb68cfc: LoadField: r2 = r0->field_f
    //     0xb68cfc: ldur            w2, [x0, #0xf]
    // 0xb68d00: DecompressPointer r2
    //     0xb68d00: add             x2, x2, HEAP, lsl #32
    // 0xb68d04: r0 = LoadClassIdInstr(r2)
    //     0xb68d04: ldur            x0, [x2, #-1]
    //     0xb68d08: ubfx            x0, x0, #0xc, #0x14
    // 0xb68d0c: r16 = "product_rating"
    //     0xb68d0c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23f20] "product_rating"
    //     0xb68d10: ldr             x16, [x16, #0xf20]
    // 0xb68d14: stp             x16, x2, [SP]
    // 0xb68d18: mov             lr, x0
    // 0xb68d1c: ldr             lr, [x21, lr, lsl #3]
    // 0xb68d20: blr             lr
    // 0xb68d24: stur            x0, [fp, #-0x18]
    // 0xb68d28: tbnz            w0, #4, #0xb68d78
    // 0xb68d2c: ldur            x2, [fp, #-0x10]
    // 0xb68d30: LoadField: r1 = r2->field_7b
    //     0xb68d30: ldur            w1, [x2, #0x7b]
    // 0xb68d34: DecompressPointer r1
    //     0xb68d34: add             x1, x1, HEAP, lsl #32
    // 0xb68d38: cmp             w1, NULL
    // 0xb68d3c: b.ne            #0xb68d48
    // 0xb68d40: r1 = Null
    //     0xb68d40: mov             x1, NULL
    // 0xb68d44: b               #0xb68d54
    // 0xb68d48: LoadField: r3 = r1->field_7
    //     0xb68d48: ldur            w3, [x1, #7]
    // 0xb68d4c: DecompressPointer r3
    //     0xb68d4c: add             x3, x3, HEAP, lsl #32
    // 0xb68d50: mov             x1, x3
    // 0xb68d54: cmp             w1, NULL
    // 0xb68d58: b.ne            #0xb68d64
    // 0xb68d5c: d0 = 0.000000
    //     0xb68d5c: eor             v0.16b, v0.16b, v0.16b
    // 0xb68d60: b               #0xb68d68
    // 0xb68d64: LoadField: d0 = r1->field_7
    //     0xb68d64: ldur            d0, [x1, #7]
    // 0xb68d68: ldur            x1, [fp, #-8]
    // 0xb68d6c: r0 = _getRatingColor()
    //     0xb68d6c: bl              #0xa582f0  ; [package:customer_app/app/presentation/views/basic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_getRatingColor
    // 0xb68d70: mov             x1, x0
    // 0xb68d74: b               #0xb68d98
    // 0xb68d78: ldur            x0, [fp, #-8]
    // 0xb68d7c: LoadField: r1 = r0->field_f
    //     0xb68d7c: ldur            w1, [x0, #0xf]
    // 0xb68d80: DecompressPointer r1
    //     0xb68d80: add             x1, x1, HEAP, lsl #32
    // 0xb68d84: cmp             w1, NULL
    // 0xb68d88: b.eq            #0xb691f0
    // 0xb68d8c: r0 = of()
    //     0xb68d8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb68d90: LoadField: r1 = r0->field_5b
    //     0xb68d90: ldur            w1, [x0, #0x5b]
    // 0xb68d94: DecompressPointer r1
    //     0xb68d94: add             x1, x1, HEAP, lsl #32
    // 0xb68d98: ldur            x0, [fp, #-0x10]
    // 0xb68d9c: stur            x1, [fp, #-0x20]
    // 0xb68da0: r0 = ColorFilter()
    //     0xb68da0: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0xb68da4: mov             x1, x0
    // 0xb68da8: ldur            x0, [fp, #-0x20]
    // 0xb68dac: stur            x1, [fp, #-0x28]
    // 0xb68db0: StoreField: r1->field_7 = r0
    //     0xb68db0: stur            w0, [x1, #7]
    // 0xb68db4: r0 = Instance_BlendMode
    //     0xb68db4: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0xb68db8: ldr             x0, [x0, #0xb30]
    // 0xb68dbc: StoreField: r1->field_b = r0
    //     0xb68dbc: stur            w0, [x1, #0xb]
    // 0xb68dc0: r0 = 1
    //     0xb68dc0: movz            x0, #0x1
    // 0xb68dc4: StoreField: r1->field_13 = r0
    //     0xb68dc4: stur            x0, [x1, #0x13]
    // 0xb68dc8: r0 = SvgPicture()
    //     0xb68dc8: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0xb68dcc: stur            x0, [fp, #-0x20]
    // 0xb68dd0: ldur            x16, [fp, #-0x28]
    // 0xb68dd4: str             x16, [SP]
    // 0xb68dd8: mov             x1, x0
    // 0xb68ddc: r2 = "assets/images/green_star.svg"
    //     0xb68ddc: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f9a0] "assets/images/green_star.svg"
    //     0xb68de0: ldr             x2, [x2, #0x9a0]
    // 0xb68de4: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0xb68de4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0xb68de8: ldr             x4, [x4, #0xa38]
    // 0xb68dec: r0 = SvgPicture.asset()
    //     0xb68dec: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0xb68df0: ldur            x0, [fp, #-0x10]
    // 0xb68df4: LoadField: r1 = r0->field_7b
    //     0xb68df4: ldur            w1, [x0, #0x7b]
    // 0xb68df8: DecompressPointer r1
    //     0xb68df8: add             x1, x1, HEAP, lsl #32
    // 0xb68dfc: cmp             w1, NULL
    // 0xb68e00: b.ne            #0xb68e0c
    // 0xb68e04: r0 = Null
    //     0xb68e04: mov             x0, NULL
    // 0xb68e08: b               #0xb68e2c
    // 0xb68e0c: LoadField: r2 = r1->field_7
    //     0xb68e0c: ldur            w2, [x1, #7]
    // 0xb68e10: DecompressPointer r2
    //     0xb68e10: add             x2, x2, HEAP, lsl #32
    // 0xb68e14: cmp             w2, NULL
    // 0xb68e18: b.ne            #0xb68e24
    // 0xb68e1c: r0 = Null
    //     0xb68e1c: mov             x0, NULL
    // 0xb68e20: b               #0xb68e2c
    // 0xb68e24: str             x2, [SP]
    // 0xb68e28: r0 = toString()
    //     0xb68e28: bl              #0x1583704  ; [dart:core] _Double::toString
    // 0xb68e2c: cmp             w0, NULL
    // 0xb68e30: b.ne            #0xb68e3c
    // 0xb68e34: r4 = ""
    //     0xb68e34: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb68e38: b               #0xb68e40
    // 0xb68e3c: mov             x4, x0
    // 0xb68e40: ldur            x2, [fp, #-8]
    // 0xb68e44: ldur            x3, [fp, #-0x18]
    // 0xb68e48: ldur            x0, [fp, #-0x20]
    // 0xb68e4c: stur            x4, [fp, #-0x28]
    // 0xb68e50: LoadField: r1 = r2->field_f
    //     0xb68e50: ldur            w1, [x2, #0xf]
    // 0xb68e54: DecompressPointer r1
    //     0xb68e54: add             x1, x1, HEAP, lsl #32
    // 0xb68e58: cmp             w1, NULL
    // 0xb68e5c: b.eq            #0xb691f4
    // 0xb68e60: r0 = of()
    //     0xb68e60: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb68e64: LoadField: r1 = r0->field_87
    //     0xb68e64: ldur            w1, [x0, #0x87]
    // 0xb68e68: DecompressPointer r1
    //     0xb68e68: add             x1, x1, HEAP, lsl #32
    // 0xb68e6c: LoadField: r0 = r1->field_7
    //     0xb68e6c: ldur            w0, [x1, #7]
    // 0xb68e70: DecompressPointer r0
    //     0xb68e70: add             x0, x0, HEAP, lsl #32
    // 0xb68e74: r16 = 12.000000
    //     0xb68e74: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb68e78: ldr             x16, [x16, #0x9e8]
    // 0xb68e7c: r30 = Instance_Color
    //     0xb68e7c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb68e80: stp             lr, x16, [SP]
    // 0xb68e84: mov             x1, x0
    // 0xb68e88: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb68e88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb68e8c: ldr             x4, [x4, #0xaa0]
    // 0xb68e90: r0 = copyWith()
    //     0xb68e90: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb68e94: stur            x0, [fp, #-0x30]
    // 0xb68e98: r0 = Text()
    //     0xb68e98: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb68e9c: mov             x3, x0
    // 0xb68ea0: ldur            x0, [fp, #-0x28]
    // 0xb68ea4: stur            x3, [fp, #-0x38]
    // 0xb68ea8: StoreField: r3->field_b = r0
    //     0xb68ea8: stur            w0, [x3, #0xb]
    // 0xb68eac: ldur            x0, [fp, #-0x30]
    // 0xb68eb0: StoreField: r3->field_13 = r0
    //     0xb68eb0: stur            w0, [x3, #0x13]
    // 0xb68eb4: r1 = Null
    //     0xb68eb4: mov             x1, NULL
    // 0xb68eb8: r2 = 4
    //     0xb68eb8: movz            x2, #0x4
    // 0xb68ebc: r0 = AllocateArray()
    //     0xb68ebc: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb68ec0: mov             x2, x0
    // 0xb68ec4: ldur            x0, [fp, #-0x20]
    // 0xb68ec8: stur            x2, [fp, #-0x28]
    // 0xb68ecc: StoreField: r2->field_f = r0
    //     0xb68ecc: stur            w0, [x2, #0xf]
    // 0xb68ed0: ldur            x0, [fp, #-0x38]
    // 0xb68ed4: StoreField: r2->field_13 = r0
    //     0xb68ed4: stur            w0, [x2, #0x13]
    // 0xb68ed8: r1 = <Widget>
    //     0xb68ed8: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb68edc: r0 = AllocateGrowableArray()
    //     0xb68edc: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb68ee0: mov             x3, x0
    // 0xb68ee4: ldur            x0, [fp, #-0x28]
    // 0xb68ee8: stur            x3, [fp, #-0x20]
    // 0xb68eec: StoreField: r3->field_f = r0
    //     0xb68eec: stur            w0, [x3, #0xf]
    // 0xb68ef0: r0 = 4
    //     0xb68ef0: movz            x0, #0x4
    // 0xb68ef4: StoreField: r3->field_b = r0
    //     0xb68ef4: stur            w0, [x3, #0xb]
    // 0xb68ef8: ldur            x0, [fp, #-0x18]
    // 0xb68efc: tbnz            w0, #4, #0xb69078
    // 0xb68f00: ldur            x1, [fp, #-0x10]
    // 0xb68f04: LoadField: r2 = r1->field_7b
    //     0xb68f04: ldur            w2, [x1, #0x7b]
    // 0xb68f08: DecompressPointer r2
    //     0xb68f08: add             x2, x2, HEAP, lsl #32
    // 0xb68f0c: cmp             w2, NULL
    // 0xb68f10: b.ne            #0xb68f1c
    // 0xb68f14: ldur            x2, [fp, #-8]
    // 0xb68f18: b               #0xb6907c
    // 0xb68f1c: LoadField: r4 = r2->field_b
    //     0xb68f1c: ldur            w4, [x2, #0xb]
    // 0xb68f20: DecompressPointer r4
    //     0xb68f20: add             x4, x4, HEAP, lsl #32
    // 0xb68f24: stur            x4, [fp, #-0x10]
    // 0xb68f28: cmp             w4, NULL
    // 0xb68f2c: b.eq            #0xb69070
    // 0xb68f30: ldur            x0, [fp, #-8]
    // 0xb68f34: r1 = Null
    //     0xb68f34: mov             x1, NULL
    // 0xb68f38: r2 = 6
    //     0xb68f38: movz            x2, #0x6
    // 0xb68f3c: r0 = AllocateArray()
    //     0xb68f3c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb68f40: r16 = " | ("
    //     0xb68f40: add             x16, PP, #0x52, lsl #12  ; [pp+0x52d70] " | ("
    //     0xb68f44: ldr             x16, [x16, #0xd70]
    // 0xb68f48: StoreField: r0->field_f = r16
    //     0xb68f48: stur            w16, [x0, #0xf]
    // 0xb68f4c: ldur            x1, [fp, #-0x10]
    // 0xb68f50: LoadField: d0 = r1->field_7
    //     0xb68f50: ldur            d0, [x1, #7]
    // 0xb68f54: fcmp            d0, d0
    // 0xb68f58: b.vs            #0xb691f8
    // 0xb68f5c: fcvtzs          x1, d0
    // 0xb68f60: asr             x16, x1, #0x1e
    // 0xb68f64: cmp             x16, x1, asr #63
    // 0xb68f68: b.ne            #0xb691f8
    // 0xb68f6c: lsl             x1, x1, #1
    // 0xb68f70: StoreField: r0->field_13 = r1
    //     0xb68f70: stur            w1, [x0, #0x13]
    // 0xb68f74: r16 = ")"
    //     0xb68f74: ldr             x16, [PP, #0xde0]  ; [pp+0xde0] ")"
    // 0xb68f78: ArrayStore: r0[0] = r16  ; List_4
    //     0xb68f78: stur            w16, [x0, #0x17]
    // 0xb68f7c: str             x0, [SP]
    // 0xb68f80: r0 = _interpolate()
    //     0xb68f80: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0xb68f84: ldur            x2, [fp, #-8]
    // 0xb68f88: stur            x0, [fp, #-0x10]
    // 0xb68f8c: LoadField: r1 = r2->field_f
    //     0xb68f8c: ldur            w1, [x2, #0xf]
    // 0xb68f90: DecompressPointer r1
    //     0xb68f90: add             x1, x1, HEAP, lsl #32
    // 0xb68f94: cmp             w1, NULL
    // 0xb68f98: b.eq            #0xb69220
    // 0xb68f9c: r0 = of()
    //     0xb68f9c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb68fa0: LoadField: r1 = r0->field_87
    //     0xb68fa0: ldur            w1, [x0, #0x87]
    // 0xb68fa4: DecompressPointer r1
    //     0xb68fa4: add             x1, x1, HEAP, lsl #32
    // 0xb68fa8: LoadField: r0 = r1->field_2b
    //     0xb68fa8: ldur            w0, [x1, #0x2b]
    // 0xb68fac: DecompressPointer r0
    //     0xb68fac: add             x0, x0, HEAP, lsl #32
    // 0xb68fb0: r16 = 12.000000
    //     0xb68fb0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb68fb4: ldr             x16, [x16, #0x9e8]
    // 0xb68fb8: r30 = Instance_Color
    //     0xb68fb8: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb68fbc: stp             lr, x16, [SP]
    // 0xb68fc0: mov             x1, x0
    // 0xb68fc4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb68fc4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb68fc8: ldr             x4, [x4, #0xaa0]
    // 0xb68fcc: r0 = copyWith()
    //     0xb68fcc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb68fd0: stur            x0, [fp, #-0x28]
    // 0xb68fd4: r0 = Text()
    //     0xb68fd4: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb68fd8: mov             x2, x0
    // 0xb68fdc: ldur            x0, [fp, #-0x10]
    // 0xb68fe0: stur            x2, [fp, #-0x30]
    // 0xb68fe4: StoreField: r2->field_b = r0
    //     0xb68fe4: stur            w0, [x2, #0xb]
    // 0xb68fe8: ldur            x0, [fp, #-0x28]
    // 0xb68fec: StoreField: r2->field_13 = r0
    //     0xb68fec: stur            w0, [x2, #0x13]
    // 0xb68ff0: ldur            x0, [fp, #-0x20]
    // 0xb68ff4: LoadField: r1 = r0->field_b
    //     0xb68ff4: ldur            w1, [x0, #0xb]
    // 0xb68ff8: LoadField: r3 = r0->field_f
    //     0xb68ff8: ldur            w3, [x0, #0xf]
    // 0xb68ffc: DecompressPointer r3
    //     0xb68ffc: add             x3, x3, HEAP, lsl #32
    // 0xb69000: LoadField: r4 = r3->field_b
    //     0xb69000: ldur            w4, [x3, #0xb]
    // 0xb69004: r3 = LoadInt32Instr(r1)
    //     0xb69004: sbfx            x3, x1, #1, #0x1f
    // 0xb69008: stur            x3, [fp, #-0x40]
    // 0xb6900c: r1 = LoadInt32Instr(r4)
    //     0xb6900c: sbfx            x1, x4, #1, #0x1f
    // 0xb69010: cmp             x3, x1
    // 0xb69014: b.ne            #0xb69020
    // 0xb69018: mov             x1, x0
    // 0xb6901c: r0 = _growToNextCapacity()
    //     0xb6901c: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb69020: ldur            x3, [fp, #-0x20]
    // 0xb69024: ldur            x2, [fp, #-0x40]
    // 0xb69028: add             x0, x2, #1
    // 0xb6902c: lsl             x1, x0, #1
    // 0xb69030: StoreField: r3->field_b = r1
    //     0xb69030: stur            w1, [x3, #0xb]
    // 0xb69034: LoadField: r1 = r3->field_f
    //     0xb69034: ldur            w1, [x3, #0xf]
    // 0xb69038: DecompressPointer r1
    //     0xb69038: add             x1, x1, HEAP, lsl #32
    // 0xb6903c: ldur            x0, [fp, #-0x30]
    // 0xb69040: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb69040: add             x25, x1, x2, lsl #2
    //     0xb69044: add             x25, x25, #0xf
    //     0xb69048: str             w0, [x25]
    //     0xb6904c: tbz             w0, #0, #0xb69068
    //     0xb69050: ldurb           w16, [x1, #-1]
    //     0xb69054: ldurb           w17, [x0, #-1]
    //     0xb69058: and             x16, x17, x16, lsr #2
    //     0xb6905c: tst             x16, HEAP, lsr #32
    //     0xb69060: b.eq            #0xb69068
    //     0xb69064: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb69068: mov             x2, x3
    // 0xb6906c: b               #0xb69188
    // 0xb69070: ldur            x2, [fp, #-8]
    // 0xb69074: b               #0xb6907c
    // 0xb69078: ldur            x2, [fp, #-8]
    // 0xb6907c: tbz             w0, #4, #0xb69184
    // 0xb69080: LoadField: r1 = r2->field_f
    //     0xb69080: ldur            w1, [x2, #0xf]
    // 0xb69084: DecompressPointer r1
    //     0xb69084: add             x1, x1, HEAP, lsl #32
    // 0xb69088: cmp             w1, NULL
    // 0xb6908c: b.eq            #0xb69224
    // 0xb69090: r0 = of()
    //     0xb69090: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb69094: LoadField: r1 = r0->field_87
    //     0xb69094: ldur            w1, [x0, #0x87]
    // 0xb69098: DecompressPointer r1
    //     0xb69098: add             x1, x1, HEAP, lsl #32
    // 0xb6909c: LoadField: r0 = r1->field_2b
    //     0xb6909c: ldur            w0, [x1, #0x2b]
    // 0xb690a0: DecompressPointer r0
    //     0xb690a0: add             x0, x0, HEAP, lsl #32
    // 0xb690a4: ldur            x1, [fp, #-8]
    // 0xb690a8: stur            x0, [fp, #-0x10]
    // 0xb690ac: LoadField: r2 = r1->field_f
    //     0xb690ac: ldur            w2, [x1, #0xf]
    // 0xb690b0: DecompressPointer r2
    //     0xb690b0: add             x2, x2, HEAP, lsl #32
    // 0xb690b4: cmp             w2, NULL
    // 0xb690b8: b.eq            #0xb69228
    // 0xb690bc: mov             x1, x2
    // 0xb690c0: r0 = of()
    //     0xb690c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb690c4: LoadField: r1 = r0->field_5b
    //     0xb690c4: ldur            w1, [x0, #0x5b]
    // 0xb690c8: DecompressPointer r1
    //     0xb690c8: add             x1, x1, HEAP, lsl #32
    // 0xb690cc: r16 = 10.000000
    //     0xb690cc: ldr             x16, [PP, #0x69f8]  ; [pp+0x69f8] 10
    // 0xb690d0: stp             x1, x16, [SP]
    // 0xb690d4: ldur            x1, [fp, #-0x10]
    // 0xb690d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb690d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb690dc: ldr             x4, [x4, #0xaa0]
    // 0xb690e0: r0 = copyWith()
    //     0xb690e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb690e4: stur            x0, [fp, #-8]
    // 0xb690e8: r0 = Text()
    //     0xb690e8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb690ec: mov             x2, x0
    // 0xb690f0: r0 = " Brand Rating"
    //     0xb690f0: add             x0, PP, #0x52, lsl #12  ; [pp+0x52d78] " Brand Rating"
    //     0xb690f4: ldr             x0, [x0, #0xd78]
    // 0xb690f8: stur            x2, [fp, #-0x10]
    // 0xb690fc: StoreField: r2->field_b = r0
    //     0xb690fc: stur            w0, [x2, #0xb]
    // 0xb69100: ldur            x0, [fp, #-8]
    // 0xb69104: StoreField: r2->field_13 = r0
    //     0xb69104: stur            w0, [x2, #0x13]
    // 0xb69108: ldur            x0, [fp, #-0x20]
    // 0xb6910c: LoadField: r1 = r0->field_b
    //     0xb6910c: ldur            w1, [x0, #0xb]
    // 0xb69110: LoadField: r3 = r0->field_f
    //     0xb69110: ldur            w3, [x0, #0xf]
    // 0xb69114: DecompressPointer r3
    //     0xb69114: add             x3, x3, HEAP, lsl #32
    // 0xb69118: LoadField: r4 = r3->field_b
    //     0xb69118: ldur            w4, [x3, #0xb]
    // 0xb6911c: r3 = LoadInt32Instr(r1)
    //     0xb6911c: sbfx            x3, x1, #1, #0x1f
    // 0xb69120: stur            x3, [fp, #-0x40]
    // 0xb69124: r1 = LoadInt32Instr(r4)
    //     0xb69124: sbfx            x1, x4, #1, #0x1f
    // 0xb69128: cmp             x3, x1
    // 0xb6912c: b.ne            #0xb69138
    // 0xb69130: mov             x1, x0
    // 0xb69134: r0 = _growToNextCapacity()
    //     0xb69134: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb69138: ldur            x2, [fp, #-0x20]
    // 0xb6913c: ldur            x3, [fp, #-0x40]
    // 0xb69140: add             x0, x3, #1
    // 0xb69144: lsl             x1, x0, #1
    // 0xb69148: StoreField: r2->field_b = r1
    //     0xb69148: stur            w1, [x2, #0xb]
    // 0xb6914c: LoadField: r1 = r2->field_f
    //     0xb6914c: ldur            w1, [x2, #0xf]
    // 0xb69150: DecompressPointer r1
    //     0xb69150: add             x1, x1, HEAP, lsl #32
    // 0xb69154: ldur            x0, [fp, #-0x10]
    // 0xb69158: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb69158: add             x25, x1, x3, lsl #2
    //     0xb6915c: add             x25, x25, #0xf
    //     0xb69160: str             w0, [x25]
    //     0xb69164: tbz             w0, #0, #0xb69180
    //     0xb69168: ldurb           w16, [x1, #-1]
    //     0xb6916c: ldurb           w17, [x0, #-1]
    //     0xb69170: and             x16, x17, x16, lsr #2
    //     0xb69174: tst             x16, HEAP, lsr #32
    //     0xb69178: b.eq            #0xb69180
    //     0xb6917c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb69180: b               #0xb69188
    // 0xb69184: mov             x2, x3
    // 0xb69188: r0 = Row()
    //     0xb69188: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0xb6918c: r1 = Instance_Axis
    //     0xb6918c: ldr             x1, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xb69190: StoreField: r0->field_f = r1
    //     0xb69190: stur            w1, [x0, #0xf]
    // 0xb69194: r1 = Instance_MainAxisAlignment
    //     0xb69194: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xb69198: ldr             x1, [x1, #0xa08]
    // 0xb6919c: StoreField: r0->field_13 = r1
    //     0xb6919c: stur            w1, [x0, #0x13]
    // 0xb691a0: r1 = Instance_MainAxisSize
    //     0xb691a0: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xb691a4: ldr             x1, [x1, #0xa10]
    // 0xb691a8: ArrayStore: r0[0] = r1  ; List_4
    //     0xb691a8: stur            w1, [x0, #0x17]
    // 0xb691ac: r1 = Instance_CrossAxisAlignment
    //     0xb691ac: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xb691b0: ldr             x1, [x1, #0xa18]
    // 0xb691b4: StoreField: r0->field_1b = r1
    //     0xb691b4: stur            w1, [x0, #0x1b]
    // 0xb691b8: r1 = Instance_VerticalDirection
    //     0xb691b8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xb691bc: ldr             x1, [x1, #0xa20]
    // 0xb691c0: StoreField: r0->field_23 = r1
    //     0xb691c0: stur            w1, [x0, #0x23]
    // 0xb691c4: r1 = Instance_Clip
    //     0xb691c4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xb691c8: ldr             x1, [x1, #0x38]
    // 0xb691cc: StoreField: r0->field_2b = r1
    //     0xb691cc: stur            w1, [x0, #0x2b]
    // 0xb691d0: StoreField: r0->field_2f = rZR
    //     0xb691d0: stur            xzr, [x0, #0x2f]
    // 0xb691d4: ldur            x1, [fp, #-0x20]
    // 0xb691d8: StoreField: r0->field_b = r1
    //     0xb691d8: stur            w1, [x0, #0xb]
    // 0xb691dc: LeaveFrame
    //     0xb691dc: mov             SP, fp
    //     0xb691e0: ldp             fp, lr, [SP], #0x10
    // 0xb691e4: ret
    //     0xb691e4: ret             
    // 0xb691e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb691e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb691ec: b               #0xb68c80
    // 0xb691f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb691f0: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb691f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb691f4: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb691f8: SaveReg d0
    //     0xb691f8: str             q0, [SP, #-0x10]!
    // 0xb691fc: SaveReg r0
    //     0xb691fc: str             x0, [SP, #-8]!
    // 0xb69200: r0 = 74
    //     0xb69200: movz            x0, #0x4a
    // 0xb69204: r30 = DoubleToIntegerStub
    //     0xb69204: ldr             lr, [PP, #0x17f8]  ; [pp+0x17f8] Stub: DoubleToInteger (0x611848)
    // 0xb69208: LoadField: r30 = r30->field_7
    //     0xb69208: ldur            lr, [lr, #7]
    // 0xb6920c: blr             lr
    // 0xb69210: mov             x1, x0
    // 0xb69214: RestoreReg r0
    //     0xb69214: ldr             x0, [SP], #8
    // 0xb69218: RestoreReg d0
    //     0xb69218: ldr             q0, [SP], #0x10
    // 0xb6921c: b               #0xb68f70
    // 0xb69220: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb69220: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb69224: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb69224: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb69228: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb69228: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xb6922c, size: 0x50
    // 0xb6922c: EnterFrame
    //     0xb6922c: stp             fp, lr, [SP, #-0x10]!
    //     0xb69230: mov             fp, SP
    // 0xb69234: ldr             x0, [fp, #0x10]
    // 0xb69238: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb69238: ldur            w1, [x0, #0x17]
    // 0xb6923c: DecompressPointer r1
    //     0xb6923c: add             x1, x1, HEAP, lsl #32
    // 0xb69240: CheckStackOverflow
    //     0xb69240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69244: cmp             SP, x16
    //     0xb69248: b.ls            #0xb69274
    // 0xb6924c: LoadField: r0 = r1->field_f
    //     0xb6924c: ldur            w0, [x1, #0xf]
    // 0xb69250: DecompressPointer r0
    //     0xb69250: add             x0, x0, HEAP, lsl #32
    // 0xb69254: LoadField: r2 = r1->field_13
    //     0xb69254: ldur            w2, [x1, #0x13]
    // 0xb69258: DecompressPointer r2
    //     0xb69258: add             x2, x2, HEAP, lsl #32
    // 0xb6925c: mov             x1, x0
    // 0xb69260: r0 = _onProductCardTap()
    //     0xb69260: bl              #0xb6927c  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_onProductCardTap
    // 0xb69264: r0 = Null
    //     0xb69264: mov             x0, NULL
    // 0xb69268: LeaveFrame
    //     0xb69268: mov             SP, fp
    //     0xb6926c: ldp             fp, lr, [SP], #0x10
    // 0xb69270: ret
    //     0xb69270: ret             
    // 0xb69274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb69274: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb69278: b               #0xb6924c
  }
  _ _onProductCardTap(/* No info */) {
    // ** addr: 0xb6927c, size: 0x100
    // 0xb6927c: EnterFrame
    //     0xb6927c: stp             fp, lr, [SP, #-0x10]!
    //     0xb69280: mov             fp, SP
    // 0xb69284: AllocStack(0x48)
    //     0xb69284: sub             SP, SP, #0x48
    // 0xb69288: CheckStackOverflow
    //     0xb69288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb6928c: cmp             SP, x16
    //     0xb69290: b.ls            #0xb69370
    // 0xb69294: LoadField: r0 = r1->field_b
    //     0xb69294: ldur            w0, [x1, #0xb]
    // 0xb69298: DecompressPointer r0
    //     0xb69298: add             x0, x0, HEAP, lsl #32
    // 0xb6929c: cmp             w0, NULL
    // 0xb692a0: b.eq            #0xb69378
    // 0xb692a4: LoadField: r1 = r0->field_27
    //     0xb692a4: ldur            w1, [x0, #0x27]
    // 0xb692a8: DecompressPointer r1
    //     0xb692a8: add             x1, x1, HEAP, lsl #32
    // 0xb692ac: cmp             w1, NULL
    // 0xb692b0: b.ne            #0xb692b8
    // 0xb692b4: r1 = ""
    //     0xb692b4: ldr             x1, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb692b8: LoadField: r3 = r0->field_23
    //     0xb692b8: ldur            w3, [x0, #0x23]
    // 0xb692bc: DecompressPointer r3
    //     0xb692bc: add             x3, x3, HEAP, lsl #32
    // 0xb692c0: cmp             w3, NULL
    // 0xb692c4: b.ne            #0xb692cc
    // 0xb692c8: r3 = ""
    //     0xb692c8: ldr             x3, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb692cc: LoadField: r4 = r0->field_2f
    //     0xb692cc: ldur            w4, [x0, #0x2f]
    // 0xb692d0: DecompressPointer r4
    //     0xb692d0: add             x4, x4, HEAP, lsl #32
    // 0xb692d4: LoadField: r5 = r0->field_1f
    //     0xb692d4: ldur            w5, [x0, #0x1f]
    // 0xb692d8: DecompressPointer r5
    //     0xb692d8: add             x5, x5, HEAP, lsl #32
    // 0xb692dc: LoadField: r6 = r0->field_2b
    //     0xb692dc: ldur            w6, [x0, #0x2b]
    // 0xb692e0: DecompressPointer r6
    //     0xb692e0: add             x6, x6, HEAP, lsl #32
    // 0xb692e4: cmp             w6, NULL
    // 0xb692e8: b.ne            #0xb692f0
    // 0xb692ec: r6 = ""
    //     0xb692ec: ldr             x6, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb692f0: LoadField: r7 = r0->field_f
    //     0xb692f0: ldur            w7, [x0, #0xf]
    // 0xb692f4: DecompressPointer r7
    //     0xb692f4: add             x7, x7, HEAP, lsl #32
    // 0xb692f8: cmp             w7, NULL
    // 0xb692fc: b.ne            #0xb69304
    // 0xb69300: r7 = ""
    //     0xb69300: ldr             x7, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb69304: LoadField: r8 = r2->field_2b
    //     0xb69304: ldur            w8, [x2, #0x2b]
    // 0xb69308: DecompressPointer r8
    //     0xb69308: add             x8, x8, HEAP, lsl #32
    // 0xb6930c: LoadField: r9 = r2->field_3b
    //     0xb6930c: ldur            w9, [x2, #0x3b]
    // 0xb69310: DecompressPointer r9
    //     0xb69310: add             x9, x9, HEAP, lsl #32
    // 0xb69314: cmp             w9, NULL
    // 0xb69318: b.ne            #0xb69324
    // 0xb6931c: r2 = Null
    //     0xb6931c: mov             x2, NULL
    // 0xb69320: b               #0xb6932c
    // 0xb69324: LoadField: r2 = r9->field_b
    //     0xb69324: ldur            w2, [x9, #0xb]
    // 0xb69328: DecompressPointer r2
    //     0xb69328: add             x2, x2, HEAP, lsl #32
    // 0xb6932c: LoadField: r9 = r0->field_43
    //     0xb6932c: ldur            w9, [x0, #0x43]
    // 0xb69330: DecompressPointer r9
    //     0xb69330: add             x9, x9, HEAP, lsl #32
    // 0xb69334: stp             x1, x9, [SP, #0x38]
    // 0xb69338: stp             x4, x3, [SP, #0x28]
    // 0xb6933c: stp             x6, x5, [SP, #0x18]
    // 0xb69340: stp             x8, x7, [SP, #8]
    // 0xb69344: str             x2, [SP]
    // 0xb69348: r4 = 0
    //     0xb69348: movz            x4, #0
    // 0xb6934c: ldr             x0, [SP, #0x40]
    // 0xb69350: r16 = UnlinkedCall_0x613b5c
    //     0xb69350: add             x16, PP, #0x55, lsl #12  ; [pp+0x55cb0] UnlinkedCall: 0x613b5c - SwitchableCallMissStub
    //     0xb69354: add             x16, x16, #0xcb0
    // 0xb69358: ldp             x5, lr, [x16]
    // 0xb6935c: blr             lr
    // 0xb69360: r0 = Null
    //     0xb69360: mov             x0, NULL
    // 0xb69364: LeaveFrame
    //     0xb69364: mov             SP, fp
    //     0xb69368: ldp             fp, lr, [SP], #0x10
    // 0xb6936c: ret
    //     0xb6936c: ret             
    // 0xb69370: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb69370: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb69374: b               #0xb69294
    // 0xb69378: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb69378: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xb6937c, size: 0x64
    // 0xb6937c: EnterFrame
    //     0xb6937c: stp             fp, lr, [SP, #-0x10]!
    //     0xb69380: mov             fp, SP
    // 0xb69384: ldr             x0, [fp, #0x20]
    // 0xb69388: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xb69388: ldur            w1, [x0, #0x17]
    // 0xb6938c: DecompressPointer r1
    //     0xb6938c: add             x1, x1, HEAP, lsl #32
    // 0xb69390: CheckStackOverflow
    //     0xb69390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69394: cmp             SP, x16
    //     0xb69398: b.ls            #0xb693d8
    // 0xb6939c: LoadField: r0 = r1->field_f
    //     0xb6939c: ldur            w0, [x1, #0xf]
    // 0xb693a0: DecompressPointer r0
    //     0xb693a0: add             x0, x0, HEAP, lsl #32
    // 0xb693a4: LoadField: r3 = r1->field_13
    //     0xb693a4: ldur            w3, [x1, #0x13]
    // 0xb693a8: DecompressPointer r3
    //     0xb693a8: add             x3, x3, HEAP, lsl #32
    // 0xb693ac: LoadField: r2 = r3->field_37
    //     0xb693ac: ldur            w2, [x3, #0x37]
    // 0xb693b0: DecompressPointer r2
    //     0xb693b0: add             x2, x2, HEAP, lsl #32
    // 0xb693b4: ldr             x1, [fp, #0x10]
    // 0xb693b8: r5 = LoadInt32Instr(r1)
    //     0xb693b8: sbfx            x5, x1, #1, #0x1f
    //     0xb693bc: tbz             w1, #0, #0xb693c4
    //     0xb693c0: ldur            x5, [x1, #7]
    // 0xb693c4: mov             x1, x0
    // 0xb693c8: r0 = glassThemeImageSlider()
    //     0xb693c8: bl              #0xb693e0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::glassThemeImageSlider
    // 0xb693cc: LeaveFrame
    //     0xb693cc: mov             SP, fp
    //     0xb693d0: ldp             fp, lr, [SP], #0x10
    // 0xb693d4: ret
    //     0xb693d4: ret             
    // 0xb693d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb693d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb693dc: b               #0xb6939c
  }
  _ glassThemeImageSlider(/* No info */) {
    // ** addr: 0xb693e0, size: 0x50c
    // 0xb693e0: EnterFrame
    //     0xb693e0: stp             fp, lr, [SP, #-0x10]!
    //     0xb693e4: mov             fp, SP
    // 0xb693e8: AllocStack(0x60)
    //     0xb693e8: sub             SP, SP, #0x60
    // 0xb693ec: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r1, fp-0x20 */)
    //     0xb693ec: mov             x0, x2
    //     0xb693f0: stur            x2, [fp, #-0x10]
    //     0xb693f4: mov             x2, x3
    //     0xb693f8: stur            x3, [fp, #-0x18]
    //     0xb693fc: mov             x3, x1
    //     0xb69400: stur            x1, [fp, #-8]
    //     0xb69404: mov             x1, x5
    //     0xb69408: stur            x5, [fp, #-0x20]
    // 0xb6940c: CheckStackOverflow
    //     0xb6940c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69410: cmp             SP, x16
    //     0xb69414: b.ls            #0xb698e0
    // 0xb69418: r1 = 2
    //     0xb69418: movz            x1, #0x2
    // 0xb6941c: r0 = AllocateContext()
    //     0xb6941c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb69420: ldur            x1, [fp, #-8]
    // 0xb69424: stur            x0, [fp, #-0x28]
    // 0xb69428: StoreField: r0->field_f = r1
    //     0xb69428: stur            w1, [x0, #0xf]
    // 0xb6942c: ldur            x2, [fp, #-0x18]
    // 0xb69430: StoreField: r0->field_13 = r2
    //     0xb69430: stur            w2, [x0, #0x13]
    // 0xb69434: r0 = Radius()
    //     0xb69434: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb69438: d0 = 10.000000
    //     0xb69438: fmov            d0, #10.00000000
    // 0xb6943c: stur            x0, [fp, #-0x30]
    // 0xb69440: StoreField: r0->field_7 = d0
    //     0xb69440: stur            d0, [x0, #7]
    // 0xb69444: StoreField: r0->field_f = d0
    //     0xb69444: stur            d0, [x0, #0xf]
    // 0xb69448: r0 = BorderRadius()
    //     0xb69448: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb6944c: mov             x1, x0
    // 0xb69450: ldur            x0, [fp, #-0x30]
    // 0xb69454: stur            x1, [fp, #-0x38]
    // 0xb69458: StoreField: r1->field_7 = r0
    //     0xb69458: stur            w0, [x1, #7]
    // 0xb6945c: StoreField: r1->field_b = r0
    //     0xb6945c: stur            w0, [x1, #0xb]
    // 0xb69460: StoreField: r1->field_f = r0
    //     0xb69460: stur            w0, [x1, #0xf]
    // 0xb69464: StoreField: r1->field_13 = r0
    //     0xb69464: stur            w0, [x1, #0x13]
    // 0xb69468: r0 = RoundedRectangleBorder()
    //     0xb69468: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb6946c: mov             x3, x0
    // 0xb69470: ldur            x0, [fp, #-0x38]
    // 0xb69474: stur            x3, [fp, #-0x30]
    // 0xb69478: StoreField: r3->field_b = r0
    //     0xb69478: stur            w0, [x3, #0xb]
    // 0xb6947c: r0 = Instance_BorderSide
    //     0xb6947c: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb69480: ldr             x0, [x0, #0xe20]
    // 0xb69484: StoreField: r3->field_7 = r0
    //     0xb69484: stur            w0, [x3, #7]
    // 0xb69488: ldur            x2, [fp, #-0x10]
    // 0xb6948c: cmp             w2, NULL
    // 0xb69490: b.ne            #0xb6949c
    // 0xb69494: r0 = Null
    //     0xb69494: mov             x0, NULL
    // 0xb69498: b               #0xb694d4
    // 0xb6949c: ldur            x4, [fp, #-0x20]
    // 0xb694a0: LoadField: r0 = r2->field_b
    //     0xb694a0: ldur            w0, [x2, #0xb]
    // 0xb694a4: r1 = LoadInt32Instr(r0)
    //     0xb694a4: sbfx            x1, x0, #1, #0x1f
    // 0xb694a8: mov             x0, x1
    // 0xb694ac: mov             x1, x4
    // 0xb694b0: cmp             x1, x0
    // 0xb694b4: b.hs            #0xb698e8
    // 0xb694b8: LoadField: r0 = r2->field_f
    //     0xb694b8: ldur            w0, [x2, #0xf]
    // 0xb694bc: DecompressPointer r0
    //     0xb694bc: add             x0, x0, HEAP, lsl #32
    // 0xb694c0: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0xb694c0: add             x16, x0, x4, lsl #2
    //     0xb694c4: ldur            w1, [x16, #0xf]
    // 0xb694c8: DecompressPointer r1
    //     0xb694c8: add             x1, x1, HEAP, lsl #32
    // 0xb694cc: LoadField: r0 = r1->field_b
    //     0xb694cc: ldur            w0, [x1, #0xb]
    // 0xb694d0: DecompressPointer r0
    //     0xb694d0: add             x0, x0, HEAP, lsl #32
    // 0xb694d4: cmp             w0, NULL
    // 0xb694d8: b.ne            #0xb694e4
    // 0xb694dc: r4 = ""
    //     0xb694dc: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xb694e0: b               #0xb694e8
    // 0xb694e4: mov             x4, x0
    // 0xb694e8: ldur            x0, [fp, #-0x18]
    // 0xb694ec: stur            x4, [fp, #-0x10]
    // 0xb694f0: r1 = Function '<anonymous closure>':.
    //     0xb694f0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55cc0] AnonymousClosure: (0x9b1028), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0xb694f4: ldr             x1, [x1, #0xcc0]
    // 0xb694f8: r2 = Null
    //     0xb694f8: mov             x2, NULL
    // 0xb694fc: r0 = AllocateClosure()
    //     0xb694fc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb69500: r1 = Function '<anonymous closure>':.
    //     0xb69500: add             x1, PP, #0x55, lsl #12  ; [pp+0x55cc8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xb69504: ldr             x1, [x1, #0xcc8]
    // 0xb69508: r2 = Null
    //     0xb69508: mov             x2, NULL
    // 0xb6950c: stur            x0, [fp, #-0x38]
    // 0xb69510: r0 = AllocateClosure()
    //     0xb69510: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb69514: stur            x0, [fp, #-0x40]
    // 0xb69518: r0 = CachedNetworkImage()
    //     0xb69518: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xb6951c: stur            x0, [fp, #-0x48]
    // 0xb69520: r16 = Instance_BoxFit
    //     0xb69520: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xb69524: ldr             x16, [x16, #0xb18]
    // 0xb69528: ldur            lr, [fp, #-0x38]
    // 0xb6952c: stp             lr, x16, [SP, #8]
    // 0xb69530: ldur            x16, [fp, #-0x40]
    // 0xb69534: str             x16, [SP]
    // 0xb69538: mov             x1, x0
    // 0xb6953c: ldur            x2, [fp, #-0x10]
    // 0xb69540: r4 = const [0, 0x5, 0x3, 0x2, errorWidget, 0x4, fit, 0x2, progressIndicatorBuilder, 0x3, null]
    //     0xb69540: add             x4, PP, #0x55, lsl #12  ; [pp+0x55638] List(11) [0, 0x5, 0x3, 0x2, "errorWidget", 0x4, "fit", 0x2, "progressIndicatorBuilder", 0x3, Null]
    //     0xb69544: ldr             x4, [x4, #0x638]
    // 0xb69548: r0 = CachedNetworkImage()
    //     0xb69548: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xb6954c: r0 = Card()
    //     0xb6954c: bl              #0x9afa0c  ; AllocateCardStub -> Card (size=0x38)
    // 0xb69550: mov             x1, x0
    // 0xb69554: r0 = Instance_Color
    //     0xb69554: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f88] Obj!Color@d6aa71
    //     0xb69558: ldr             x0, [x0, #0xf88]
    // 0xb6955c: stur            x1, [fp, #-0x10]
    // 0xb69560: StoreField: r1->field_b = r0
    //     0xb69560: stur            w0, [x1, #0xb]
    // 0xb69564: r0 = 0.000000
    //     0xb69564: ldr             x0, [PP, #0x46e8]  ; [pp+0x46e8] 0
    // 0xb69568: ArrayStore: r1[0] = r0  ; List_4
    //     0xb69568: stur            w0, [x1, #0x17]
    // 0xb6956c: ldur            x0, [fp, #-0x30]
    // 0xb69570: StoreField: r1->field_1b = r0
    //     0xb69570: stur            w0, [x1, #0x1b]
    // 0xb69574: r0 = true
    //     0xb69574: add             x0, NULL, #0x20  ; true
    // 0xb69578: StoreField: r1->field_1f = r0
    //     0xb69578: stur            w0, [x1, #0x1f]
    // 0xb6957c: r2 = Instance_EdgeInsets
    //     0xb6957c: ldr             x2, [PP, #0x4e38]  ; [pp+0x4e38] Obj!EdgeInsets@d56d21
    // 0xb69580: StoreField: r1->field_27 = r2
    //     0xb69580: stur            w2, [x1, #0x27]
    // 0xb69584: r2 = Instance_Clip
    //     0xb69584: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3fb50] Obj!Clip@d76f81
    //     0xb69588: ldr             x2, [x2, #0xb50]
    // 0xb6958c: StoreField: r1->field_23 = r2
    //     0xb6958c: stur            w2, [x1, #0x23]
    // 0xb69590: ldur            x2, [fp, #-0x48]
    // 0xb69594: StoreField: r1->field_2f = r2
    //     0xb69594: stur            w2, [x1, #0x2f]
    // 0xb69598: StoreField: r1->field_2b = r0
    //     0xb69598: stur            w0, [x1, #0x2b]
    // 0xb6959c: r2 = Instance__CardVariant
    //     0xb6959c: add             x2, PP, #0x34, lsl #12  ; [pp+0x34a68] Obj!_CardVariant@d74621
    //     0xb695a0: ldr             x2, [x2, #0xa68]
    // 0xb695a4: StoreField: r1->field_33 = r2
    //     0xb695a4: stur            w2, [x1, #0x33]
    // 0xb695a8: r0 = Center()
    //     0xb695a8: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xb695ac: mov             x3, x0
    // 0xb695b0: r0 = Instance_Alignment
    //     0xb695b0: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xb695b4: ldr             x0, [x0, #0xb10]
    // 0xb695b8: stur            x3, [fp, #-0x30]
    // 0xb695bc: StoreField: r3->field_f = r0
    //     0xb695bc: stur            w0, [x3, #0xf]
    // 0xb695c0: ldur            x0, [fp, #-0x10]
    // 0xb695c4: StoreField: r3->field_b = r0
    //     0xb695c4: stur            w0, [x3, #0xb]
    // 0xb695c8: r1 = Null
    //     0xb695c8: mov             x1, NULL
    // 0xb695cc: r2 = 2
    //     0xb695cc: movz            x2, #0x2
    // 0xb695d0: r0 = AllocateArray()
    //     0xb695d0: bl              #0x16f7198  ; AllocateArrayStub
    // 0xb695d4: mov             x2, x0
    // 0xb695d8: ldur            x0, [fp, #-0x30]
    // 0xb695dc: stur            x2, [fp, #-0x10]
    // 0xb695e0: StoreField: r2->field_f = r0
    //     0xb695e0: stur            w0, [x2, #0xf]
    // 0xb695e4: r1 = <Widget>
    //     0xb695e4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xb695e8: r0 = AllocateGrowableArray()
    //     0xb695e8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xb695ec: mov             x3, x0
    // 0xb695f0: ldur            x0, [fp, #-0x10]
    // 0xb695f4: stur            x3, [fp, #-0x30]
    // 0xb695f8: StoreField: r3->field_f = r0
    //     0xb695f8: stur            w0, [x3, #0xf]
    // 0xb695fc: r0 = 2
    //     0xb695fc: movz            x0, #0x2
    // 0xb69600: StoreField: r3->field_b = r0
    //     0xb69600: stur            w0, [x3, #0xb]
    // 0xb69604: ldur            x0, [fp, #-0x18]
    // 0xb69608: LoadField: r1 = r0->field_7f
    //     0xb69608: ldur            w1, [x0, #0x7f]
    // 0xb6960c: DecompressPointer r1
    //     0xb6960c: add             x1, x1, HEAP, lsl #32
    // 0xb69610: cmp             w1, NULL
    // 0xb69614: b.ne            #0xb69620
    // 0xb69618: r1 = Null
    //     0xb69618: mov             x1, NULL
    // 0xb6961c: b               #0xb69634
    // 0xb69620: LoadField: r2 = r1->field_7
    //     0xb69620: ldur            w2, [x1, #7]
    // 0xb69624: cbnz            w2, #0xb69630
    // 0xb69628: r1 = false
    //     0xb69628: add             x1, NULL, #0x30  ; false
    // 0xb6962c: b               #0xb69634
    // 0xb69630: r1 = true
    //     0xb69630: add             x1, NULL, #0x20  ; true
    // 0xb69634: cmp             w1, NULL
    // 0xb69638: b.eq            #0xb696cc
    // 0xb6963c: tbnz            w1, #4, #0xb696cc
    // 0xb69640: ldur            x1, [fp, #-8]
    // 0xb69644: mov             x2, x0
    // 0xb69648: r0 = _buildDiscountBadge()
    //     0xb69648: bl              #0xaee864  ; [package:customer_app/app/presentation/views/cosmetic/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildDiscountBadge
    // 0xb6964c: mov             x2, x0
    // 0xb69650: ldur            x0, [fp, #-0x30]
    // 0xb69654: stur            x2, [fp, #-0x10]
    // 0xb69658: LoadField: r1 = r0->field_b
    //     0xb69658: ldur            w1, [x0, #0xb]
    // 0xb6965c: LoadField: r3 = r0->field_f
    //     0xb6965c: ldur            w3, [x0, #0xf]
    // 0xb69660: DecompressPointer r3
    //     0xb69660: add             x3, x3, HEAP, lsl #32
    // 0xb69664: LoadField: r4 = r3->field_b
    //     0xb69664: ldur            w4, [x3, #0xb]
    // 0xb69668: r3 = LoadInt32Instr(r1)
    //     0xb69668: sbfx            x3, x1, #1, #0x1f
    // 0xb6966c: stur            x3, [fp, #-0x20]
    // 0xb69670: r1 = LoadInt32Instr(r4)
    //     0xb69670: sbfx            x1, x4, #1, #0x1f
    // 0xb69674: cmp             x3, x1
    // 0xb69678: b.ne            #0xb69684
    // 0xb6967c: mov             x1, x0
    // 0xb69680: r0 = _growToNextCapacity()
    //     0xb69680: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb69684: ldur            x3, [fp, #-0x30]
    // 0xb69688: ldur            x2, [fp, #-0x20]
    // 0xb6968c: add             x0, x2, #1
    // 0xb69690: lsl             x1, x0, #1
    // 0xb69694: StoreField: r3->field_b = r1
    //     0xb69694: stur            w1, [x3, #0xb]
    // 0xb69698: LoadField: r1 = r3->field_f
    //     0xb69698: ldur            w1, [x3, #0xf]
    // 0xb6969c: DecompressPointer r1
    //     0xb6969c: add             x1, x1, HEAP, lsl #32
    // 0xb696a0: ldur            x0, [fp, #-0x10]
    // 0xb696a4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xb696a4: add             x25, x1, x2, lsl #2
    //     0xb696a8: add             x25, x25, #0xf
    //     0xb696ac: str             w0, [x25]
    //     0xb696b0: tbz             w0, #0, #0xb696cc
    //     0xb696b4: ldurb           w16, [x1, #-1]
    //     0xb696b8: ldurb           w17, [x0, #-1]
    //     0xb696bc: and             x16, x17, x16, lsr #2
    //     0xb696c0: tst             x16, HEAP, lsr #32
    //     0xb696c4: b.eq            #0xb696cc
    //     0xb696c8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb696cc: ldur            x0, [fp, #-0x18]
    // 0xb696d0: LoadField: r1 = r0->field_b7
    //     0xb696d0: ldur            w1, [x0, #0xb7]
    // 0xb696d4: DecompressPointer r1
    //     0xb696d4: add             x1, x1, HEAP, lsl #32
    // 0xb696d8: cmp             w1, NULL
    // 0xb696dc: b.ne            #0xb696e8
    // 0xb696e0: r1 = Null
    //     0xb696e0: mov             x1, NULL
    // 0xb696e4: b               #0xb696fc
    // 0xb696e8: LoadField: r2 = r1->field_7
    //     0xb696e8: ldur            w2, [x1, #7]
    // 0xb696ec: cbnz            w2, #0xb696f8
    // 0xb696f0: r1 = false
    //     0xb696f0: add             x1, NULL, #0x30  ; false
    // 0xb696f4: b               #0xb696fc
    // 0xb696f8: r1 = true
    //     0xb696f8: add             x1, NULL, #0x20  ; true
    // 0xb696fc: cmp             w1, NULL
    // 0xb69700: b.ne            #0xb6970c
    // 0xb69704: mov             x2, x3
    // 0xb69708: b               #0xb697a4
    // 0xb6970c: tbnz            w1, #4, #0xb697a0
    // 0xb69710: ldur            x1, [fp, #-8]
    // 0xb69714: mov             x2, x0
    // 0xb69718: r0 = _buildStockAlert()
    //     0xb69718: bl              #0xb69ad0  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildStockAlert
    // 0xb6971c: mov             x2, x0
    // 0xb69720: ldur            x0, [fp, #-0x30]
    // 0xb69724: stur            x2, [fp, #-0x10]
    // 0xb69728: LoadField: r1 = r0->field_b
    //     0xb69728: ldur            w1, [x0, #0xb]
    // 0xb6972c: LoadField: r3 = r0->field_f
    //     0xb6972c: ldur            w3, [x0, #0xf]
    // 0xb69730: DecompressPointer r3
    //     0xb69730: add             x3, x3, HEAP, lsl #32
    // 0xb69734: LoadField: r4 = r3->field_b
    //     0xb69734: ldur            w4, [x3, #0xb]
    // 0xb69738: r3 = LoadInt32Instr(r1)
    //     0xb69738: sbfx            x3, x1, #1, #0x1f
    // 0xb6973c: stur            x3, [fp, #-0x20]
    // 0xb69740: r1 = LoadInt32Instr(r4)
    //     0xb69740: sbfx            x1, x4, #1, #0x1f
    // 0xb69744: cmp             x3, x1
    // 0xb69748: b.ne            #0xb69754
    // 0xb6974c: mov             x1, x0
    // 0xb69750: r0 = _growToNextCapacity()
    //     0xb69750: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb69754: ldur            x2, [fp, #-0x30]
    // 0xb69758: ldur            x3, [fp, #-0x20]
    // 0xb6975c: add             x0, x3, #1
    // 0xb69760: lsl             x1, x0, #1
    // 0xb69764: StoreField: r2->field_b = r1
    //     0xb69764: stur            w1, [x2, #0xb]
    // 0xb69768: LoadField: r1 = r2->field_f
    //     0xb69768: ldur            w1, [x2, #0xf]
    // 0xb6976c: DecompressPointer r1
    //     0xb6976c: add             x1, x1, HEAP, lsl #32
    // 0xb69770: ldur            x0, [fp, #-0x10]
    // 0xb69774: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb69774: add             x25, x1, x3, lsl #2
    //     0xb69778: add             x25, x25, #0xf
    //     0xb6977c: str             w0, [x25]
    //     0xb69780: tbz             w0, #0, #0xb6979c
    //     0xb69784: ldurb           w16, [x1, #-1]
    //     0xb69788: ldurb           w17, [x0, #-1]
    //     0xb6978c: and             x16, x17, x16, lsr #2
    //     0xb69790: tst             x16, HEAP, lsr #32
    //     0xb69794: b.eq            #0xb6979c
    //     0xb69798: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb6979c: b               #0xb697a4
    // 0xb697a0: mov             x2, x3
    // 0xb697a4: ldur            x0, [fp, #-0x18]
    // 0xb697a8: LoadField: r1 = r0->field_8b
    //     0xb697a8: ldur            w1, [x0, #0x8b]
    // 0xb697ac: DecompressPointer r1
    //     0xb697ac: add             x1, x1, HEAP, lsl #32
    // 0xb697b0: cmp             w1, NULL
    // 0xb697b4: b.eq            #0xb69844
    // 0xb697b8: tbnz            w1, #4, #0xb69844
    // 0xb697bc: ldur            x1, [fp, #-8]
    // 0xb697c0: r0 = _buildCustomizationBadge()
    //     0xb697c0: bl              #0xb698ec  ; [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::_buildCustomizationBadge
    // 0xb697c4: mov             x2, x0
    // 0xb697c8: ldur            x0, [fp, #-0x30]
    // 0xb697cc: stur            x2, [fp, #-8]
    // 0xb697d0: LoadField: r1 = r0->field_b
    //     0xb697d0: ldur            w1, [x0, #0xb]
    // 0xb697d4: LoadField: r3 = r0->field_f
    //     0xb697d4: ldur            w3, [x0, #0xf]
    // 0xb697d8: DecompressPointer r3
    //     0xb697d8: add             x3, x3, HEAP, lsl #32
    // 0xb697dc: LoadField: r4 = r3->field_b
    //     0xb697dc: ldur            w4, [x3, #0xb]
    // 0xb697e0: r3 = LoadInt32Instr(r1)
    //     0xb697e0: sbfx            x3, x1, #1, #0x1f
    // 0xb697e4: stur            x3, [fp, #-0x20]
    // 0xb697e8: r1 = LoadInt32Instr(r4)
    //     0xb697e8: sbfx            x1, x4, #1, #0x1f
    // 0xb697ec: cmp             x3, x1
    // 0xb697f0: b.ne            #0xb697fc
    // 0xb697f4: mov             x1, x0
    // 0xb697f8: r0 = _growToNextCapacity()
    //     0xb697f8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xb697fc: ldur            x2, [fp, #-0x30]
    // 0xb69800: ldur            x3, [fp, #-0x20]
    // 0xb69804: add             x0, x3, #1
    // 0xb69808: lsl             x1, x0, #1
    // 0xb6980c: StoreField: r2->field_b = r1
    //     0xb6980c: stur            w1, [x2, #0xb]
    // 0xb69810: LoadField: r1 = r2->field_f
    //     0xb69810: ldur            w1, [x2, #0xf]
    // 0xb69814: DecompressPointer r1
    //     0xb69814: add             x1, x1, HEAP, lsl #32
    // 0xb69818: ldur            x0, [fp, #-8]
    // 0xb6981c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xb6981c: add             x25, x1, x3, lsl #2
    //     0xb69820: add             x25, x25, #0xf
    //     0xb69824: str             w0, [x25]
    //     0xb69828: tbz             w0, #0, #0xb69844
    //     0xb6982c: ldurb           w16, [x1, #-1]
    //     0xb69830: ldurb           w17, [x0, #-1]
    //     0xb69834: and             x16, x17, x16, lsr #2
    //     0xb69838: tst             x16, HEAP, lsr #32
    //     0xb6983c: b.eq            #0xb69844
    //     0xb69840: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0xb69844: r0 = Stack()
    //     0xb69844: bl              #0x901d34  ; AllocateStackStub -> Stack (size=0x20)
    // 0xb69848: mov             x1, x0
    // 0xb6984c: r0 = Instance_Alignment
    //     0xb6984c: add             x0, PP, #0x48, lsl #12  ; [pp+0x485b8] Obj!Alignment@d5a741
    //     0xb69850: ldr             x0, [x0, #0x5b8]
    // 0xb69854: stur            x1, [fp, #-8]
    // 0xb69858: StoreField: r1->field_f = r0
    //     0xb69858: stur            w0, [x1, #0xf]
    // 0xb6985c: r0 = Instance_StackFit
    //     0xb6985c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dfa8] Obj!StackFit@d731a1
    //     0xb69860: ldr             x0, [x0, #0xfa8]
    // 0xb69864: ArrayStore: r1[0] = r0  ; List_4
    //     0xb69864: stur            w0, [x1, #0x17]
    // 0xb69868: r0 = Instance_Clip
    //     0xb69868: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0xb6986c: ldr             x0, [x0, #0x7e0]
    // 0xb69870: StoreField: r1->field_1b = r0
    //     0xb69870: stur            w0, [x1, #0x1b]
    // 0xb69874: ldur            x0, [fp, #-0x30]
    // 0xb69878: StoreField: r1->field_b = r0
    //     0xb69878: stur            w0, [x1, #0xb]
    // 0xb6987c: r0 = InkWell()
    //     0xb6987c: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xb69880: mov             x3, x0
    // 0xb69884: ldur            x0, [fp, #-8]
    // 0xb69888: stur            x3, [fp, #-0x10]
    // 0xb6988c: StoreField: r3->field_b = r0
    //     0xb6988c: stur            w0, [x3, #0xb]
    // 0xb69890: ldur            x2, [fp, #-0x28]
    // 0xb69894: r1 = Function '<anonymous closure>':.
    //     0xb69894: add             x1, PP, #0x55, lsl #12  ; [pp+0x55cd0] AnonymousClosure: (0xb6922c), in [package:customer_app/app/presentation/views/glass/home/<USER>/product_group_carousel_item_view.dart] _ProductGroupCarouselItemViewState::glassThemeSlider (0xb67fdc)
    //     0xb69898: ldr             x1, [x1, #0xcd0]
    // 0xb6989c: r0 = AllocateClosure()
    //     0xb6989c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb698a0: mov             x1, x0
    // 0xb698a4: ldur            x0, [fp, #-0x10]
    // 0xb698a8: StoreField: r0->field_f = r1
    //     0xb698a8: stur            w1, [x0, #0xf]
    // 0xb698ac: r1 = true
    //     0xb698ac: add             x1, NULL, #0x20  ; true
    // 0xb698b0: StoreField: r0->field_43 = r1
    //     0xb698b0: stur            w1, [x0, #0x43]
    // 0xb698b4: r2 = Instance_BoxShape
    //     0xb698b4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0xb698b8: ldr             x2, [x2, #0x80]
    // 0xb698bc: StoreField: r0->field_47 = r2
    //     0xb698bc: stur            w2, [x0, #0x47]
    // 0xb698c0: StoreField: r0->field_6f = r1
    //     0xb698c0: stur            w1, [x0, #0x6f]
    // 0xb698c4: r2 = false
    //     0xb698c4: add             x2, NULL, #0x30  ; false
    // 0xb698c8: StoreField: r0->field_73 = r2
    //     0xb698c8: stur            w2, [x0, #0x73]
    // 0xb698cc: StoreField: r0->field_83 = r1
    //     0xb698cc: stur            w1, [x0, #0x83]
    // 0xb698d0: StoreField: r0->field_7b = r2
    //     0xb698d0: stur            w2, [x0, #0x7b]
    // 0xb698d4: LeaveFrame
    //     0xb698d4: mov             SP, fp
    //     0xb698d8: ldp             fp, lr, [SP], #0x10
    // 0xb698dc: ret
    //     0xb698dc: ret             
    // 0xb698e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb698e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb698e4: b               #0xb69418
    // 0xb698e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xb698e8: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildCustomizationBadge(/* No info */) {
    // ** addr: 0xb698ec, size: 0x1e4
    // 0xb698ec: EnterFrame
    //     0xb698ec: stp             fp, lr, [SP, #-0x10]!
    //     0xb698f0: mov             fp, SP
    // 0xb698f4: AllocStack(0x38)
    //     0xb698f4: sub             SP, SP, #0x38
    // 0xb698f8: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xb698f8: mov             x0, x1
    //     0xb698fc: stur            x1, [fp, #-8]
    // 0xb69900: CheckStackOverflow
    //     0xb69900: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69904: cmp             SP, x16
    //     0xb69908: b.ls            #0xb69ac0
    // 0xb6990c: LoadField: r1 = r0->field_f
    //     0xb6990c: ldur            w1, [x0, #0xf]
    // 0xb69910: DecompressPointer r1
    //     0xb69910: add             x1, x1, HEAP, lsl #32
    // 0xb69914: cmp             w1, NULL
    // 0xb69918: b.eq            #0xb69ac8
    // 0xb6991c: r0 = of()
    //     0xb6991c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb69920: r17 = 307
    //     0xb69920: movz            x17, #0x133
    // 0xb69924: ldr             w1, [x0, x17]
    // 0xb69928: DecompressPointer r1
    //     0xb69928: add             x1, x1, HEAP, lsl #32
    // 0xb6992c: stur            x1, [fp, #-0x10]
    // 0xb69930: r0 = Radius()
    //     0xb69930: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb69934: d0 = 5.000000
    //     0xb69934: fmov            d0, #5.00000000
    // 0xb69938: stur            x0, [fp, #-0x18]
    // 0xb6993c: StoreField: r0->field_7 = d0
    //     0xb6993c: stur            d0, [x0, #7]
    // 0xb69940: StoreField: r0->field_f = d0
    //     0xb69940: stur            d0, [x0, #0xf]
    // 0xb69944: r0 = BorderRadius()
    //     0xb69944: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb69948: mov             x1, x0
    // 0xb6994c: ldur            x0, [fp, #-0x18]
    // 0xb69950: stur            x1, [fp, #-0x20]
    // 0xb69954: StoreField: r1->field_7 = r0
    //     0xb69954: stur            w0, [x1, #7]
    // 0xb69958: StoreField: r1->field_b = r0
    //     0xb69958: stur            w0, [x1, #0xb]
    // 0xb6995c: StoreField: r1->field_f = r0
    //     0xb6995c: stur            w0, [x1, #0xf]
    // 0xb69960: StoreField: r1->field_13 = r0
    //     0xb69960: stur            w0, [x1, #0x13]
    // 0xb69964: r0 = RoundedRectangleBorder()
    //     0xb69964: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb69968: mov             x1, x0
    // 0xb6996c: ldur            x0, [fp, #-0x20]
    // 0xb69970: StoreField: r1->field_b = r0
    //     0xb69970: stur            w0, [x1, #0xb]
    // 0xb69974: r0 = Instance_BorderSide
    //     0xb69974: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb69978: ldr             x0, [x0, #0xe20]
    // 0xb6997c: StoreField: r1->field_7 = r0
    //     0xb6997c: stur            w0, [x1, #7]
    // 0xb69980: r16 = <RoundedRectangleBorder>
    //     0xb69980: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb69984: ldr             x16, [x16, #0xf78]
    // 0xb69988: stp             x1, x16, [SP]
    // 0xb6998c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb6998c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb69990: r0 = all()
    //     0xb69990: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb69994: stur            x0, [fp, #-0x18]
    // 0xb69998: r0 = ButtonStyle()
    //     0xb69998: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb6999c: mov             x2, x0
    // 0xb699a0: ldur            x0, [fp, #-0x18]
    // 0xb699a4: stur            x2, [fp, #-0x20]
    // 0xb699a8: StoreField: r2->field_43 = r0
    //     0xb699a8: stur            w0, [x2, #0x43]
    // 0xb699ac: ldur            x0, [fp, #-8]
    // 0xb699b0: LoadField: r1 = r0->field_f
    //     0xb699b0: ldur            w1, [x0, #0xf]
    // 0xb699b4: DecompressPointer r1
    //     0xb699b4: add             x1, x1, HEAP, lsl #32
    // 0xb699b8: cmp             w1, NULL
    // 0xb699bc: b.eq            #0xb69acc
    // 0xb699c0: r0 = of()
    //     0xb699c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb699c4: LoadField: r1 = r0->field_87
    //     0xb699c4: ldur            w1, [x0, #0x87]
    // 0xb699c8: DecompressPointer r1
    //     0xb699c8: add             x1, x1, HEAP, lsl #32
    // 0xb699cc: LoadField: r0 = r1->field_2b
    //     0xb699cc: ldur            w0, [x1, #0x2b]
    // 0xb699d0: DecompressPointer r0
    //     0xb699d0: add             x0, x0, HEAP, lsl #32
    // 0xb699d4: r16 = 12.000000
    //     0xb699d4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb699d8: ldr             x16, [x16, #0x9e8]
    // 0xb699dc: r30 = Instance_Color
    //     0xb699dc: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb699e0: stp             lr, x16, [SP]
    // 0xb699e4: mov             x1, x0
    // 0xb699e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb699e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb699ec: ldr             x4, [x4, #0xaa0]
    // 0xb699f0: r0 = copyWith()
    //     0xb699f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb699f4: stur            x0, [fp, #-8]
    // 0xb699f8: r0 = Text()
    //     0xb699f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb699fc: mov             x3, x0
    // 0xb69a00: r0 = "Customisable"
    //     0xb69a00: add             x0, PP, #0x52, lsl #12  ; [pp+0x52970] "Customisable"
    //     0xb69a04: ldr             x0, [x0, #0x970]
    // 0xb69a08: stur            x3, [fp, #-0x18]
    // 0xb69a0c: StoreField: r3->field_b = r0
    //     0xb69a0c: stur            w0, [x3, #0xb]
    // 0xb69a10: ldur            x0, [fp, #-8]
    // 0xb69a14: StoreField: r3->field_13 = r0
    //     0xb69a14: stur            w0, [x3, #0x13]
    // 0xb69a18: r1 = Function '<anonymous closure>':.
    //     0xb69a18: add             x1, PP, #0x55, lsl #12  ; [pp+0x55cd8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb69a1c: ldr             x1, [x1, #0xcd8]
    // 0xb69a20: r2 = Null
    //     0xb69a20: mov             x2, NULL
    // 0xb69a24: r0 = AllocateClosure()
    //     0xb69a24: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb69a28: stur            x0, [fp, #-8]
    // 0xb69a2c: r0 = TextButton()
    //     0xb69a2c: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb69a30: mov             x1, x0
    // 0xb69a34: ldur            x0, [fp, #-8]
    // 0xb69a38: stur            x1, [fp, #-0x28]
    // 0xb69a3c: StoreField: r1->field_b = r0
    //     0xb69a3c: stur            w0, [x1, #0xb]
    // 0xb69a40: ldur            x0, [fp, #-0x20]
    // 0xb69a44: StoreField: r1->field_1b = r0
    //     0xb69a44: stur            w0, [x1, #0x1b]
    // 0xb69a48: r0 = false
    //     0xb69a48: add             x0, NULL, #0x30  ; false
    // 0xb69a4c: StoreField: r1->field_27 = r0
    //     0xb69a4c: stur            w0, [x1, #0x27]
    // 0xb69a50: r0 = true
    //     0xb69a50: add             x0, NULL, #0x20  ; true
    // 0xb69a54: StoreField: r1->field_2f = r0
    //     0xb69a54: stur            w0, [x1, #0x2f]
    // 0xb69a58: ldur            x0, [fp, #-0x18]
    // 0xb69a5c: StoreField: r1->field_37 = r0
    //     0xb69a5c: stur            w0, [x1, #0x37]
    // 0xb69a60: r0 = TextButtonTheme()
    //     0xb69a60: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb69a64: mov             x1, x0
    // 0xb69a68: ldur            x0, [fp, #-0x10]
    // 0xb69a6c: stur            x1, [fp, #-8]
    // 0xb69a70: StoreField: r1->field_f = r0
    //     0xb69a70: stur            w0, [x1, #0xf]
    // 0xb69a74: ldur            x0, [fp, #-0x28]
    // 0xb69a78: StoreField: r1->field_b = r0
    //     0xb69a78: stur            w0, [x1, #0xb]
    // 0xb69a7c: r0 = SizedBox()
    //     0xb69a7c: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xb69a80: mov             x1, x0
    // 0xb69a84: r0 = 30.000000
    //     0xb69a84: add             x0, PP, #0x49, lsl #12  ; [pp+0x49768] 30
    //     0xb69a88: ldr             x0, [x0, #0x768]
    // 0xb69a8c: stur            x1, [fp, #-0x10]
    // 0xb69a90: StoreField: r1->field_13 = r0
    //     0xb69a90: stur            w0, [x1, #0x13]
    // 0xb69a94: ldur            x0, [fp, #-8]
    // 0xb69a98: StoreField: r1->field_b = r0
    //     0xb69a98: stur            w0, [x1, #0xb]
    // 0xb69a9c: r0 = Padding()
    //     0xb69a9c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb69aa0: r1 = Instance_EdgeInsets
    //     0xb69aa0: add             x1, PP, #0x52, lsl #12  ; [pp+0x52e68] Obj!EdgeInsets@d58461
    //     0xb69aa4: ldr             x1, [x1, #0xe68]
    // 0xb69aa8: StoreField: r0->field_f = r1
    //     0xb69aa8: stur            w1, [x0, #0xf]
    // 0xb69aac: ldur            x1, [fp, #-0x10]
    // 0xb69ab0: StoreField: r0->field_b = r1
    //     0xb69ab0: stur            w1, [x0, #0xb]
    // 0xb69ab4: LeaveFrame
    //     0xb69ab4: mov             SP, fp
    //     0xb69ab8: ldp             fp, lr, [SP], #0x10
    // 0xb69abc: ret
    //     0xb69abc: ret             
    // 0xb69ac0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb69ac0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb69ac4: b               #0xb6990c
    // 0xb69ac8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb69ac8: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xb69acc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb69acc: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildStockAlert(/* No info */) {
    // ** addr: 0xb69ad0, size: 0x250
    // 0xb69ad0: EnterFrame
    //     0xb69ad0: stp             fp, lr, [SP, #-0x10]!
    //     0xb69ad4: mov             fp, SP
    // 0xb69ad8: AllocStack(0x50)
    //     0xb69ad8: sub             SP, SP, #0x50
    // 0xb69adc: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xb69adc: stur            x1, [fp, #-8]
    //     0xb69ae0: stur            x2, [fp, #-0x10]
    // 0xb69ae4: CheckStackOverflow
    //     0xb69ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69ae8: cmp             SP, x16
    //     0xb69aec: b.ls            #0xb69d14
    // 0xb69af0: LoadField: r0 = r2->field_8b
    //     0xb69af0: ldur            w0, [x2, #0x8b]
    // 0xb69af4: DecompressPointer r0
    //     0xb69af4: add             x0, x0, HEAP, lsl #32
    // 0xb69af8: cmp             w0, NULL
    // 0xb69afc: b.eq            #0xb69b10
    // 0xb69b00: tbnz            w0, #4, #0xb69b10
    // 0xb69b04: d0 = 38.000000
    //     0xb69b04: add             x17, PP, #0x50, lsl #12  ; [pp+0x50d10] IMM: double(38) from 0x4043000000000000
    //     0xb69b08: ldr             d0, [x17, #0xd10]
    // 0xb69b0c: b               #0xb69b14
    // 0xb69b10: d0 = 4.000000
    //     0xb69b10: fmov            d0, #4.00000000
    // 0xb69b14: stur            d0, [fp, #-0x40]
    // 0xb69b18: r0 = EdgeInsets()
    //     0xb69b18: bl              #0x66bcf8  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xb69b1c: d0 = 8.000000
    //     0xb69b1c: fmov            d0, #8.00000000
    // 0xb69b20: stur            x0, [fp, #-0x18]
    // 0xb69b24: StoreField: r0->field_7 = d0
    //     0xb69b24: stur            d0, [x0, #7]
    // 0xb69b28: StoreField: r0->field_f = rZR
    //     0xb69b28: stur            xzr, [x0, #0xf]
    // 0xb69b2c: ArrayStore: r0[0] = rZR  ; List_8
    //     0xb69b2c: stur            xzr, [x0, #0x17]
    // 0xb69b30: ldur            d0, [fp, #-0x40]
    // 0xb69b34: StoreField: r0->field_1f = d0
    //     0xb69b34: stur            d0, [x0, #0x1f]
    // 0xb69b38: r16 = <EdgeInsets>
    //     0xb69b38: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0xb69b3c: ldr             x16, [x16, #0xda0]
    // 0xb69b40: r30 = Instance_EdgeInsets
    //     0xb69b40: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0xb69b44: ldr             lr, [lr, #0x668]
    // 0xb69b48: stp             lr, x16, [SP]
    // 0xb69b4c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb69b4c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb69b50: r0 = all()
    //     0xb69b50: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb69b54: stur            x0, [fp, #-0x20]
    // 0xb69b58: r16 = <Color>
    //     0xb69b58: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0xb69b5c: ldr             x16, [x16, #0xf80]
    // 0xb69b60: r30 = Instance_Color
    //     0xb69b60: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0xb69b64: stp             lr, x16, [SP]
    // 0xb69b68: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb69b68: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb69b6c: r0 = all()
    //     0xb69b6c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb69b70: stur            x0, [fp, #-0x28]
    // 0xb69b74: r0 = Radius()
    //     0xb69b74: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xb69b78: d0 = 5.000000
    //     0xb69b78: fmov            d0, #5.00000000
    // 0xb69b7c: stur            x0, [fp, #-0x30]
    // 0xb69b80: StoreField: r0->field_7 = d0
    //     0xb69b80: stur            d0, [x0, #7]
    // 0xb69b84: StoreField: r0->field_f = d0
    //     0xb69b84: stur            d0, [x0, #0xf]
    // 0xb69b88: r0 = BorderRadius()
    //     0xb69b88: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xb69b8c: mov             x1, x0
    // 0xb69b90: ldur            x0, [fp, #-0x30]
    // 0xb69b94: stur            x1, [fp, #-0x38]
    // 0xb69b98: StoreField: r1->field_7 = r0
    //     0xb69b98: stur            w0, [x1, #7]
    // 0xb69b9c: StoreField: r1->field_b = r0
    //     0xb69b9c: stur            w0, [x1, #0xb]
    // 0xb69ba0: StoreField: r1->field_f = r0
    //     0xb69ba0: stur            w0, [x1, #0xf]
    // 0xb69ba4: StoreField: r1->field_13 = r0
    //     0xb69ba4: stur            w0, [x1, #0x13]
    // 0xb69ba8: r0 = RoundedRectangleBorder()
    //     0xb69ba8: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xb69bac: mov             x1, x0
    // 0xb69bb0: ldur            x0, [fp, #-0x38]
    // 0xb69bb4: StoreField: r1->field_b = r0
    //     0xb69bb4: stur            w0, [x1, #0xb]
    // 0xb69bb8: r0 = Instance_BorderSide
    //     0xb69bb8: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0xb69bbc: ldr             x0, [x0, #0xe20]
    // 0xb69bc0: StoreField: r1->field_7 = r0
    //     0xb69bc0: stur            w0, [x1, #7]
    // 0xb69bc4: r16 = <RoundedRectangleBorder>
    //     0xb69bc4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0xb69bc8: ldr             x16, [x16, #0xf78]
    // 0xb69bcc: stp             x1, x16, [SP]
    // 0xb69bd0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xb69bd0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xb69bd4: r0 = all()
    //     0xb69bd4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0xb69bd8: stur            x0, [fp, #-0x30]
    // 0xb69bdc: r0 = ButtonStyle()
    //     0xb69bdc: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0xb69be0: mov             x1, x0
    // 0xb69be4: ldur            x0, [fp, #-0x28]
    // 0xb69be8: stur            x1, [fp, #-0x38]
    // 0xb69bec: StoreField: r1->field_b = r0
    //     0xb69bec: stur            w0, [x1, #0xb]
    // 0xb69bf0: ldur            x0, [fp, #-0x20]
    // 0xb69bf4: StoreField: r1->field_23 = r0
    //     0xb69bf4: stur            w0, [x1, #0x23]
    // 0xb69bf8: ldur            x0, [fp, #-0x30]
    // 0xb69bfc: StoreField: r1->field_43 = r0
    //     0xb69bfc: stur            w0, [x1, #0x43]
    // 0xb69c00: r0 = TextButtonThemeData()
    //     0xb69c00: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0xb69c04: mov             x1, x0
    // 0xb69c08: ldur            x0, [fp, #-0x38]
    // 0xb69c0c: stur            x1, [fp, #-0x20]
    // 0xb69c10: StoreField: r1->field_7 = r0
    //     0xb69c10: stur            w0, [x1, #7]
    // 0xb69c14: ldur            x0, [fp, #-0x10]
    // 0xb69c18: LoadField: r2 = r0->field_b7
    //     0xb69c18: ldur            w2, [x0, #0xb7]
    // 0xb69c1c: DecompressPointer r2
    //     0xb69c1c: add             x2, x2, HEAP, lsl #32
    // 0xb69c20: str             x2, [SP]
    // 0xb69c24: r0 = _interpolateSingle()
    //     0xb69c24: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xb69c28: mov             x2, x0
    // 0xb69c2c: ldur            x0, [fp, #-8]
    // 0xb69c30: stur            x2, [fp, #-0x10]
    // 0xb69c34: LoadField: r1 = r0->field_f
    //     0xb69c34: ldur            w1, [x0, #0xf]
    // 0xb69c38: DecompressPointer r1
    //     0xb69c38: add             x1, x1, HEAP, lsl #32
    // 0xb69c3c: cmp             w1, NULL
    // 0xb69c40: b.eq            #0xb69d1c
    // 0xb69c44: r0 = of()
    //     0xb69c44: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xb69c48: LoadField: r1 = r0->field_87
    //     0xb69c48: ldur            w1, [x0, #0x87]
    // 0xb69c4c: DecompressPointer r1
    //     0xb69c4c: add             x1, x1, HEAP, lsl #32
    // 0xb69c50: LoadField: r0 = r1->field_2b
    //     0xb69c50: ldur            w0, [x1, #0x2b]
    // 0xb69c54: DecompressPointer r0
    //     0xb69c54: add             x0, x0, HEAP, lsl #32
    // 0xb69c58: r16 = 12.000000
    //     0xb69c58: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xb69c5c: ldr             x16, [x16, #0x9e8]
    // 0xb69c60: r30 = Instance_Color
    //     0xb69c60: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xb69c64: stp             lr, x16, [SP]
    // 0xb69c68: mov             x1, x0
    // 0xb69c6c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xb69c6c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xb69c70: ldr             x4, [x4, #0xaa0]
    // 0xb69c74: r0 = copyWith()
    //     0xb69c74: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xb69c78: stur            x0, [fp, #-8]
    // 0xb69c7c: r0 = Text()
    //     0xb69c7c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xb69c80: mov             x3, x0
    // 0xb69c84: ldur            x0, [fp, #-0x10]
    // 0xb69c88: stur            x3, [fp, #-0x28]
    // 0xb69c8c: StoreField: r3->field_b = r0
    //     0xb69c8c: stur            w0, [x3, #0xb]
    // 0xb69c90: ldur            x0, [fp, #-8]
    // 0xb69c94: StoreField: r3->field_13 = r0
    //     0xb69c94: stur            w0, [x3, #0x13]
    // 0xb69c98: r1 = Function '<anonymous closure>':.
    //     0xb69c98: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ce0] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb69c9c: ldr             x1, [x1, #0xce0]
    // 0xb69ca0: r2 = Null
    //     0xb69ca0: mov             x2, NULL
    // 0xb69ca4: r0 = AllocateClosure()
    //     0xb69ca4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb69ca8: stur            x0, [fp, #-8]
    // 0xb69cac: r0 = TextButton()
    //     0xb69cac: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0xb69cb0: mov             x1, x0
    // 0xb69cb4: ldur            x0, [fp, #-8]
    // 0xb69cb8: stur            x1, [fp, #-0x10]
    // 0xb69cbc: StoreField: r1->field_b = r0
    //     0xb69cbc: stur            w0, [x1, #0xb]
    // 0xb69cc0: r0 = false
    //     0xb69cc0: add             x0, NULL, #0x30  ; false
    // 0xb69cc4: StoreField: r1->field_27 = r0
    //     0xb69cc4: stur            w0, [x1, #0x27]
    // 0xb69cc8: r0 = true
    //     0xb69cc8: add             x0, NULL, #0x20  ; true
    // 0xb69ccc: StoreField: r1->field_2f = r0
    //     0xb69ccc: stur            w0, [x1, #0x2f]
    // 0xb69cd0: ldur            x0, [fp, #-0x28]
    // 0xb69cd4: StoreField: r1->field_37 = r0
    //     0xb69cd4: stur            w0, [x1, #0x37]
    // 0xb69cd8: r0 = TextButtonTheme()
    //     0xb69cd8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0xb69cdc: mov             x1, x0
    // 0xb69ce0: ldur            x0, [fp, #-0x20]
    // 0xb69ce4: stur            x1, [fp, #-8]
    // 0xb69ce8: StoreField: r1->field_f = r0
    //     0xb69ce8: stur            w0, [x1, #0xf]
    // 0xb69cec: ldur            x0, [fp, #-0x10]
    // 0xb69cf0: StoreField: r1->field_b = r0
    //     0xb69cf0: stur            w0, [x1, #0xb]
    // 0xb69cf4: r0 = Padding()
    //     0xb69cf4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xb69cf8: ldur            x1, [fp, #-0x18]
    // 0xb69cfc: StoreField: r0->field_f = r1
    //     0xb69cfc: stur            w1, [x0, #0xf]
    // 0xb69d00: ldur            x1, [fp, #-8]
    // 0xb69d04: StoreField: r0->field_b = r1
    //     0xb69d04: stur            w1, [x0, #0xb]
    // 0xb69d08: LeaveFrame
    //     0xb69d08: mov             SP, fp
    //     0xb69d0c: ldp             fp, lr, [SP], #0x10
    // 0xb69d10: ret
    //     0xb69d10: ret             
    // 0xb69d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb69d14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb69d18: b               #0xb69af0
    // 0xb69d1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xb69d1c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb69d20, size: 0x84
    // 0xb69d20: EnterFrame
    //     0xb69d20: stp             fp, lr, [SP, #-0x10]!
    //     0xb69d24: mov             fp, SP
    // 0xb69d28: AllocStack(0x10)
    //     0xb69d28: sub             SP, SP, #0x10
    // 0xb69d2c: SetupParameters()
    //     0xb69d2c: ldr             x0, [fp, #0x18]
    //     0xb69d30: ldur            w1, [x0, #0x17]
    //     0xb69d34: add             x1, x1, HEAP, lsl #32
    //     0xb69d38: stur            x1, [fp, #-8]
    // 0xb69d3c: CheckStackOverflow
    //     0xb69d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69d40: cmp             SP, x16
    //     0xb69d44: b.ls            #0xb69d9c
    // 0xb69d48: r1 = 1
    //     0xb69d48: movz            x1, #0x1
    // 0xb69d4c: r0 = AllocateContext()
    //     0xb69d4c: bl              #0x16f6108  ; AllocateContextStub
    // 0xb69d50: mov             x1, x0
    // 0xb69d54: ldur            x0, [fp, #-8]
    // 0xb69d58: StoreField: r1->field_b = r0
    //     0xb69d58: stur            w0, [x1, #0xb]
    // 0xb69d5c: ldr             x2, [fp, #0x10]
    // 0xb69d60: StoreField: r1->field_f = r2
    //     0xb69d60: stur            w2, [x1, #0xf]
    // 0xb69d64: LoadField: r3 = r0->field_f
    //     0xb69d64: ldur            w3, [x0, #0xf]
    // 0xb69d68: DecompressPointer r3
    //     0xb69d68: add             x3, x3, HEAP, lsl #32
    // 0xb69d6c: mov             x2, x1
    // 0xb69d70: stur            x3, [fp, #-0x10]
    // 0xb69d74: r1 = Function '<anonymous closure>':.
    //     0xb69d74: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ce8] Function: [dart:ui] _NativeScene::_NativeScene._ (0x16ed860)
    //     0xb69d78: ldr             x1, [x1, #0xce8]
    // 0xb69d7c: r0 = AllocateClosure()
    //     0xb69d7c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb69d80: ldur            x1, [fp, #-0x10]
    // 0xb69d84: mov             x2, x0
    // 0xb69d88: r0 = setState()
    //     0xb69d88: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb69d8c: r0 = Null
    //     0xb69d8c: mov             x0, NULL
    // 0xb69d90: LeaveFrame
    //     0xb69d90: mov             SP, fp
    //     0xb69d94: ldp             fp, lr, [SP], #0x10
    // 0xb69d98: ret
    //     0xb69d98: ret             
    // 0xb69d9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb69d9c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb69da0: b               #0xb69d48
  }
  [closure] void <anonymous closure>(dynamic, int) {
    // ** addr: 0xb69da4, size: 0x84
    // 0xb69da4: EnterFrame
    //     0xb69da4: stp             fp, lr, [SP, #-0x10]!
    //     0xb69da8: mov             fp, SP
    // 0xb69dac: AllocStack(0x10)
    //     0xb69dac: sub             SP, SP, #0x10
    // 0xb69db0: SetupParameters()
    //     0xb69db0: ldr             x0, [fp, #0x18]
    //     0xb69db4: ldur            w1, [x0, #0x17]
    //     0xb69db8: add             x1, x1, HEAP, lsl #32
    //     0xb69dbc: stur            x1, [fp, #-8]
    // 0xb69dc0: CheckStackOverflow
    //     0xb69dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xb69dc4: cmp             SP, x16
    //     0xb69dc8: b.ls            #0xb69e20
    // 0xb69dcc: r1 = 1
    //     0xb69dcc: movz            x1, #0x1
    // 0xb69dd0: r0 = AllocateContext()
    //     0xb69dd0: bl              #0x16f6108  ; AllocateContextStub
    // 0xb69dd4: mov             x1, x0
    // 0xb69dd8: ldur            x0, [fp, #-8]
    // 0xb69ddc: StoreField: r1->field_b = r0
    //     0xb69ddc: stur            w0, [x1, #0xb]
    // 0xb69de0: ldr             x2, [fp, #0x10]
    // 0xb69de4: StoreField: r1->field_f = r2
    //     0xb69de4: stur            w2, [x1, #0xf]
    // 0xb69de8: LoadField: r3 = r0->field_f
    //     0xb69de8: ldur            w3, [x0, #0xf]
    // 0xb69dec: DecompressPointer r3
    //     0xb69dec: add             x3, x3, HEAP, lsl #32
    // 0xb69df0: mov             x2, x1
    // 0xb69df4: stur            x3, [fp, #-0x10]
    // 0xb69df8: r1 = Function '<anonymous closure>':.
    //     0xb69df8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55d10] AnonymousClosure: (0xa59828), in [package:customer_app/app/presentation/views/line/product_detail/widgets/product_media_carousel.dart] _ProductMediaCarouselState::_buildMediaCarousel (0xa598e8)
    //     0xb69dfc: ldr             x1, [x1, #0xd10]
    // 0xb69e00: r0 = AllocateClosure()
    //     0xb69e00: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xb69e04: ldur            x1, [fp, #-0x10]
    // 0xb69e08: mov             x2, x0
    // 0xb69e0c: r0 = setState()
    //     0xb69e0c: bl              #0x7ef890  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xb69e10: r0 = Null
    //     0xb69e10: mov             x0, NULL
    // 0xb69e14: LeaveFrame
    //     0xb69e14: mov             SP, fp
    //     0xb69e18: ldp             fp, lr, [SP], #0x10
    // 0xb69e1c: ret
    //     0xb69e1c: ret             
    // 0xb69e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xb69e20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xb69e24: b               #0xb69dcc
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc87ac4, size: 0x84
    // 0xc87ac4: EnterFrame
    //     0xc87ac4: stp             fp, lr, [SP, #-0x10]!
    //     0xc87ac8: mov             fp, SP
    // 0xc87acc: AllocStack(0x8)
    //     0xc87acc: sub             SP, SP, #8
    // 0xc87ad0: SetupParameters(_ProductGroupCarouselItemViewState this /* r1 => r0, fp-0x8 */)
    //     0xc87ad0: mov             x0, x1
    //     0xc87ad4: stur            x1, [fp, #-8]
    // 0xc87ad8: CheckStackOverflow
    //     0xc87ad8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc87adc: cmp             SP, x16
    //     0xc87ae0: b.ls            #0xc87b28
    // 0xc87ae4: LoadField: r1 = r0->field_13
    //     0xc87ae4: ldur            w1, [x0, #0x13]
    // 0xc87ae8: DecompressPointer r1
    //     0xc87ae8: add             x1, x1, HEAP, lsl #32
    // 0xc87aec: r16 = Sentinel
    //     0xc87aec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87af0: cmp             w1, w16
    // 0xc87af4: b.eq            #0xc87b30
    // 0xc87af8: r0 = dispose()
    //     0xc87af8: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87afc: ldur            x0, [fp, #-8]
    // 0xc87b00: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc87b00: ldur            w1, [x0, #0x17]
    // 0xc87b04: DecompressPointer r1
    //     0xc87b04: add             x1, x1, HEAP, lsl #32
    // 0xc87b08: r16 = Sentinel
    //     0xc87b08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc87b0c: cmp             w1, w16
    // 0xc87b10: b.eq            #0xc87b3c
    // 0xc87b14: r0 = dispose()
    //     0xc87b14: bl              #0xc76764  ; [package:flutter/src/widgets/scroll_controller.dart] ScrollController::dispose
    // 0xc87b18: r0 = Null
    //     0xc87b18: mov             x0, NULL
    // 0xc87b1c: LeaveFrame
    //     0xc87b1c: mov             SP, fp
    //     0xc87b20: ldp             fp, lr, [SP], #0x10
    // 0xc87b24: ret
    //     0xc87b24: ret             
    // 0xc87b28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc87b28: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc87b2c: b               #0xc87ae4
    // 0xc87b30: r9 = _pageController
    //     0xc87b30: add             x9, PP, #0x55, lsl #12  ; [pp+0x55c68] Field <_ProductGroupCarouselItemViewState@1589000994._pageController@1589000994>: late (offset: 0x14)
    //     0xc87b34: ldr             x9, [x9, #0xc68]
    // 0xc87b38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87b38: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc87b3c: r9 = _imagePageController
    //     0xc87b3c: add             x9, PP, #0x55, lsl #12  ; [pp+0x55ca8] Field <_ProductGroupCarouselItemViewState@1589000994._imagePageController@1589000994>: late (offset: 0x18)
    //     0xc87b40: ldr             x9, [x9, #0xca8]
    // 0xc87b44: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc87b44: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4075, size: 0x4c, field offset: 0xc
//   const constructor, 
class ProductGroupCarouselItemView extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc7f364, size: 0x34
    // 0xc7f364: EnterFrame
    //     0xc7f364: stp             fp, lr, [SP, #-0x10]!
    //     0xc7f368: mov             fp, SP
    // 0xc7f36c: mov             x0, x1
    // 0xc7f370: r1 = <ProductGroupCarouselItemView>
    //     0xc7f370: add             x1, PP, #0x48, lsl #12  ; [pp+0x48820] TypeArguments: <ProductGroupCarouselItemView>
    //     0xc7f374: ldr             x1, [x1, #0x820]
    // 0xc7f378: r0 = _ProductGroupCarouselItemViewState()
    //     0xc7f378: bl              #0xc7f398  ; Allocate_ProductGroupCarouselItemViewStateStub -> _ProductGroupCarouselItemViewState (size=0x24)
    // 0xc7f37c: r1 = Sentinel
    //     0xc7f37c: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc7f380: StoreField: r0->field_13 = r1
    //     0xc7f380: stur            w1, [x0, #0x13]
    // 0xc7f384: ArrayStore: r0[0] = r1  ; List_4
    //     0xc7f384: stur            w1, [x0, #0x17]
    // 0xc7f388: StoreField: r0->field_1b = rZR
    //     0xc7f388: stur            xzr, [x0, #0x1b]
    // 0xc7f38c: LeaveFrame
    //     0xc7f38c: mov             SP, fp
    //     0xc7f390: ldp             fp, lr, [SP], #0x10
    // 0xc7f394: ret
    //     0xc7f394: ret             
  }
}
