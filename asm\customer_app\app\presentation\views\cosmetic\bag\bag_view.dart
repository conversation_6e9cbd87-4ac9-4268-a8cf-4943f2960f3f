// lib: , url: package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart

// class id: 1049223, size: 0x8
class :: {
}

// class id: 4614, size: 0x14, field offset: 0x14
//   const constructor, 
class BagView extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1348e44, size: 0x58
    // 0x1348e44: EnterFrame
    //     0x1348e44: stp             fp, lr, [SP, #-0x10]!
    //     0x1348e48: mov             fp, SP
    // 0x1348e4c: AllocStack(0x10)
    //     0x1348e4c: sub             SP, SP, #0x10
    // 0x1348e50: SetupParameters(BagView this /* r1 => r1, fp-0x8 */)
    //     0x1348e50: stur            x1, [fp, #-8]
    // 0x1348e54: r1 = 1
    //     0x1348e54: movz            x1, #0x1
    // 0x1348e58: r0 = AllocateContext()
    //     0x1348e58: bl              #0x16f6108  ; AllocateContextStub
    // 0x1348e5c: mov             x1, x0
    // 0x1348e60: ldur            x0, [fp, #-8]
    // 0x1348e64: stur            x1, [fp, #-0x10]
    // 0x1348e68: StoreField: r1->field_f = r0
    //     0x1348e68: stur            w0, [x1, #0xf]
    // 0x1348e6c: r0 = Obx()
    //     0x1348e6c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1348e70: ldur            x2, [fp, #-0x10]
    // 0x1348e74: r1 = Function '<anonymous closure>':.
    //     0x1348e74: add             x1, PP, #0x44, lsl #12  ; [pp+0x44898] AnonymousClosure: (0x1348e9c), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::bottomNavigationBar (0x1348e44)
    //     0x1348e78: ldr             x1, [x1, #0x898]
    // 0x1348e7c: stur            x0, [fp, #-8]
    // 0x1348e80: r0 = AllocateClosure()
    //     0x1348e80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1348e84: mov             x1, x0
    // 0x1348e88: ldur            x0, [fp, #-8]
    // 0x1348e8c: StoreField: r0->field_b = r1
    //     0x1348e8c: stur            w1, [x0, #0xb]
    // 0x1348e90: LeaveFrame
    //     0x1348e90: mov             SP, fp
    //     0x1348e94: ldp             fp, lr, [SP], #0x10
    // 0x1348e98: ret
    //     0x1348e98: ret             
  }
  [closure] Visibility <anonymous closure>(dynamic) {
    // ** addr: 0x1348e9c, size: 0x40c
    // 0x1348e9c: EnterFrame
    //     0x1348e9c: stp             fp, lr, [SP, #-0x10]!
    //     0x1348ea0: mov             fp, SP
    // 0x1348ea4: AllocStack(0x50)
    //     0x1348ea4: sub             SP, SP, #0x50
    // 0x1348ea8: SetupParameters()
    //     0x1348ea8: ldr             x0, [fp, #0x10]
    //     0x1348eac: ldur            w2, [x0, #0x17]
    //     0x1348eb0: add             x2, x2, HEAP, lsl #32
    //     0x1348eb4: stur            x2, [fp, #-8]
    // 0x1348eb8: CheckStackOverflow
    //     0x1348eb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1348ebc: cmp             SP, x16
    //     0x1348ec0: b.ls            #0x1349298
    // 0x1348ec4: LoadField: r1 = r2->field_f
    //     0x1348ec4: ldur            w1, [x2, #0xf]
    // 0x1348ec8: DecompressPointer r1
    //     0x1348ec8: add             x1, x1, HEAP, lsl #32
    // 0x1348ecc: r0 = controller()
    //     0x1348ecc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1348ed0: LoadField: r1 = r0->field_5b
    //     0x1348ed0: ldur            w1, [x0, #0x5b]
    // 0x1348ed4: DecompressPointer r1
    //     0x1348ed4: add             x1, x1, HEAP, lsl #32
    // 0x1348ed8: r0 = value()
    //     0x1348ed8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1348edc: LoadField: r1 = r0->field_b
    //     0x1348edc: ldur            w1, [x0, #0xb]
    // 0x1348ee0: DecompressPointer r1
    //     0x1348ee0: add             x1, x1, HEAP, lsl #32
    // 0x1348ee4: cmp             w1, NULL
    // 0x1348ee8: b.ne            #0x1348ef4
    // 0x1348eec: r0 = Null
    //     0x1348eec: mov             x0, NULL
    // 0x1348ef0: b               #0x1348f3c
    // 0x1348ef4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1348ef4: ldur            w0, [x1, #0x17]
    // 0x1348ef8: DecompressPointer r0
    //     0x1348ef8: add             x0, x0, HEAP, lsl #32
    // 0x1348efc: stur            x0, [fp, #-0x10]
    // 0x1348f00: r1 = Function '<anonymous closure>':.
    //     0x1348f00: add             x1, PP, #0x44, lsl #12  ; [pp+0x448a0] AnonymousClosure: (0x12c058c), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x1348f04: ldr             x1, [x1, #0x8a0]
    // 0x1348f08: r2 = Null
    //     0x1348f08: mov             x2, NULL
    // 0x1348f0c: r0 = AllocateClosure()
    //     0x1348f0c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1348f10: r1 = Function '<anonymous closure>':.
    //     0x1348f10: add             x1, PP, #0x44, lsl #12  ; [pp+0x448a8] AnonymousClosure: (0x12c0574), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x1348f14: ldr             x1, [x1, #0x8a8]
    // 0x1348f18: r2 = Null
    //     0x1348f18: mov             x2, NULL
    // 0x1348f1c: stur            x0, [fp, #-0x18]
    // 0x1348f20: r0 = AllocateClosure()
    //     0x1348f20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1348f24: str             x0, [SP]
    // 0x1348f28: ldur            x1, [fp, #-0x10]
    // 0x1348f2c: ldur            x2, [fp, #-0x18]
    // 0x1348f30: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x1348f30: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fb48] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    //     0x1348f34: ldr             x4, [x4, #0xb48]
    // 0x1348f38: r0 = firstWhere()
    //     0x1348f38: bl              #0x635994  ; [dart:collection] ListBase::firstWhere
    // 0x1348f3c: cmp             w0, NULL
    // 0x1348f40: b.eq            #0x1348fa8
    // 0x1348f44: LoadField: r1 = r0->field_33
    //     0x1348f44: ldur            w1, [x0, #0x33]
    // 0x1348f48: DecompressPointer r1
    //     0x1348f48: add             x1, x1, HEAP, lsl #32
    // 0x1348f4c: cmp             w1, NULL
    // 0x1348f50: b.eq            #0x1348fa8
    // 0x1348f54: ldur            x2, [fp, #-8]
    // 0x1348f58: LoadField: r1 = r2->field_f
    //     0x1348f58: ldur            w1, [x2, #0xf]
    // 0x1348f5c: DecompressPointer r1
    //     0x1348f5c: add             x1, x1, HEAP, lsl #32
    // 0x1348f60: r0 = controller()
    //     0x1348f60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1348f64: LoadField: r1 = r0->field_5b
    //     0x1348f64: ldur            w1, [x0, #0x5b]
    // 0x1348f68: DecompressPointer r1
    //     0x1348f68: add             x1, x1, HEAP, lsl #32
    // 0x1348f6c: r0 = value()
    //     0x1348f6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1348f70: LoadField: r1 = r0->field_b
    //     0x1348f70: ldur            w1, [x0, #0xb]
    // 0x1348f74: DecompressPointer r1
    //     0x1348f74: add             x1, x1, HEAP, lsl #32
    // 0x1348f78: cmp             w1, NULL
    // 0x1348f7c: b.ne            #0x1348f88
    // 0x1348f80: r0 = Null
    //     0x1348f80: mov             x0, NULL
    // 0x1348f84: b               #0x1348f90
    // 0x1348f88: LoadField: r0 = r1->field_b
    //     0x1348f88: ldur            w0, [x1, #0xb]
    // 0x1348f8c: DecompressPointer r0
    //     0x1348f8c: add             x0, x0, HEAP, lsl #32
    // 0x1348f90: cbnz            w0, #0x1348f9c
    // 0x1348f94: r1 = false
    //     0x1348f94: add             x1, NULL, #0x30  ; false
    // 0x1348f98: b               #0x1348fa0
    // 0x1348f9c: r1 = true
    //     0x1348f9c: add             x1, NULL, #0x20  ; true
    // 0x1348fa0: mov             x0, x1
    // 0x1348fa4: b               #0x1348fac
    // 0x1348fa8: r0 = false
    //     0x1348fa8: add             x0, NULL, #0x30  ; false
    // 0x1348fac: ldur            x2, [fp, #-8]
    // 0x1348fb0: stur            x0, [fp, #-0x10]
    // 0x1348fb4: LoadField: r1 = r2->field_f
    //     0x1348fb4: ldur            w1, [x2, #0xf]
    // 0x1348fb8: DecompressPointer r1
    //     0x1348fb8: add             x1, x1, HEAP, lsl #32
    // 0x1348fbc: r0 = controller()
    //     0x1348fbc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1348fc0: LoadField: r1 = r0->field_5b
    //     0x1348fc0: ldur            w1, [x0, #0x5b]
    // 0x1348fc4: DecompressPointer r1
    //     0x1348fc4: add             x1, x1, HEAP, lsl #32
    // 0x1348fc8: r0 = value()
    //     0x1348fc8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1348fcc: LoadField: r1 = r0->field_b
    //     0x1348fcc: ldur            w1, [x0, #0xb]
    // 0x1348fd0: DecompressPointer r1
    //     0x1348fd0: add             x1, x1, HEAP, lsl #32
    // 0x1348fd4: cmp             w1, NULL
    // 0x1348fd8: b.ne            #0x1348fe4
    // 0x1348fdc: r0 = Null
    //     0x1348fdc: mov             x0, NULL
    // 0x1348fe0: b               #0x1348fec
    // 0x1348fe4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x1348fe4: ldur            w0, [x1, #0x17]
    // 0x1348fe8: DecompressPointer r0
    //     0x1348fe8: add             x0, x0, HEAP, lsl #32
    // 0x1348fec: cmp             w0, NULL
    // 0x1348ff0: b.ne            #0x1349034
    // 0x1348ff4: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x1348ff4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1348ff8: ldr             x0, [x0]
    //     0x1348ffc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1349000: cmp             w0, w16
    //     0x1349004: b.ne            #0x1349010
    //     0x1349008: ldr             x2, [PP, #0x928]  ; [pp+0x928] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x134900c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1349010: r1 = <Catalogue>
    //     0x1349010: add             x1, PP, #0x25, lsl #12  ; [pp+0x25418] TypeArguments: <Catalogue>
    //     0x1349014: ldr             x1, [x1, #0x418]
    // 0x1349018: stur            x0, [fp, #-0x18]
    // 0x134901c: r0 = AllocateGrowableArray()
    //     0x134901c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x1349020: mov             x1, x0
    // 0x1349024: ldur            x0, [fp, #-0x18]
    // 0x1349028: StoreField: r1->field_f = r0
    //     0x1349028: stur            w0, [x1, #0xf]
    // 0x134902c: StoreField: r1->field_b = rZR
    //     0x134902c: stur            wzr, [x1, #0xb]
    // 0x1349030: mov             x0, x1
    // 0x1349034: LoadField: r3 = r0->field_7
    //     0x1349034: ldur            w3, [x0, #7]
    // 0x1349038: DecompressPointer r3
    //     0x1349038: add             x3, x3, HEAP, lsl #32
    // 0x134903c: stur            x3, [fp, #-0x38]
    // 0x1349040: LoadField: r1 = r0->field_b
    //     0x1349040: ldur            w1, [x0, #0xb]
    // 0x1349044: r4 = LoadInt32Instr(r1)
    //     0x1349044: sbfx            x4, x1, #1, #0x1f
    // 0x1349048: stur            x4, [fp, #-0x30]
    // 0x134904c: LoadField: r5 = r0->field_f
    //     0x134904c: ldur            w5, [x0, #0xf]
    // 0x1349050: DecompressPointer r5
    //     0x1349050: add             x5, x5, HEAP, lsl #32
    // 0x1349054: stur            x5, [fp, #-0x28]
    // 0x1349058: r0 = 0
    //     0x1349058: movz            x0, #0
    // 0x134905c: CheckStackOverflow
    //     0x134905c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1349060: cmp             SP, x16
    //     0x1349064: b.ls            #0x13492a0
    // 0x1349068: cmp             x0, x4
    // 0x134906c: b.ge            #0x1349120
    // 0x1349070: ArrayLoad: r6 = r5[r0]  ; Unknown_4
    //     0x1349070: add             x16, x5, x0, lsl #2
    //     0x1349074: ldur            w6, [x16, #0xf]
    // 0x1349078: DecompressPointer r6
    //     0x1349078: add             x6, x6, HEAP, lsl #32
    // 0x134907c: stur            x6, [fp, #-0x18]
    // 0x1349080: add             x7, x0, #1
    // 0x1349084: stur            x7, [fp, #-0x20]
    // 0x1349088: cmp             w6, NULL
    // 0x134908c: b.ne            #0x13490c0
    // 0x1349090: mov             x0, x6
    // 0x1349094: mov             x2, x3
    // 0x1349098: r1 = Null
    //     0x1349098: mov             x1, NULL
    // 0x134909c: cmp             w2, NULL
    // 0x13490a0: b.eq            #0x13490c0
    // 0x13490a4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x13490a4: ldur            w4, [x2, #0x17]
    // 0x13490a8: DecompressPointer r4
    //     0x13490a8: add             x4, x4, HEAP, lsl #32
    // 0x13490ac: r8 = X0
    //     0x13490ac: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x13490b0: LoadField: r9 = r4->field_7
    //     0x13490b0: ldur            x9, [x4, #7]
    // 0x13490b4: r3 = Null
    //     0x13490b4: add             x3, PP, #0x44, lsl #12  ; [pp+0x448b0] Null
    //     0x13490b8: ldr             x3, [x3, #0x8b0]
    // 0x13490bc: blr             x9
    // 0x13490c0: ldur            x0, [fp, #-0x18]
    // 0x13490c4: LoadField: r1 = r0->field_63
    //     0x13490c4: ldur            w1, [x0, #0x63]
    // 0x13490c8: DecompressPointer r1
    //     0x13490c8: add             x1, x1, HEAP, lsl #32
    // 0x13490cc: cmp             w1, #2
    // 0x13490d0: b.ne            #0x134910c
    // 0x13490d4: LoadField: r1 = r0->field_5f
    //     0x13490d4: ldur            w1, [x0, #0x5f]
    // 0x13490d8: DecompressPointer r1
    //     0x13490d8: add             x1, x1, HEAP, lsl #32
    // 0x13490dc: cmp             w1, NULL
    // 0x13490e0: b.ne            #0x13490ec
    // 0x13490e4: r0 = 0
    //     0x13490e4: movz            x0, #0
    // 0x13490e8: b               #0x13490fc
    // 0x13490ec: r2 = LoadInt32Instr(r1)
    //     0x13490ec: sbfx            x2, x1, #1, #0x1f
    //     0x13490f0: tbz             w1, #0, #0x13490f8
    //     0x13490f4: ldur            x2, [x1, #7]
    // 0x13490f8: mov             x0, x2
    // 0x13490fc: cmp             x0, #1
    // 0x1349100: b.le            #0x134910c
    // 0x1349104: r0 = true
    //     0x1349104: add             x0, NULL, #0x20  ; true
    // 0x1349108: b               #0x1349124
    // 0x134910c: ldur            x0, [fp, #-0x20]
    // 0x1349110: ldur            x3, [fp, #-0x38]
    // 0x1349114: ldur            x5, [fp, #-0x28]
    // 0x1349118: ldur            x4, [fp, #-0x30]
    // 0x134911c: b               #0x134905c
    // 0x1349120: r0 = false
    //     0x1349120: add             x0, NULL, #0x30  ; false
    // 0x1349124: ldur            x2, [fp, #-8]
    // 0x1349128: stur            x0, [fp, #-0x18]
    // 0x134912c: LoadField: r1 = r2->field_f
    //     0x134912c: ldur            w1, [x2, #0xf]
    // 0x1349130: DecompressPointer r1
    //     0x1349130: add             x1, x1, HEAP, lsl #32
    // 0x1349134: r0 = controller()
    //     0x1349134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1349138: LoadField: r1 = r0->field_5b
    //     0x1349138: ldur            w1, [x0, #0x5b]
    // 0x134913c: DecompressPointer r1
    //     0x134913c: add             x1, x1, HEAP, lsl #32
    // 0x1349140: r0 = value()
    //     0x1349140: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1349144: LoadField: r1 = r0->field_b
    //     0x1349144: ldur            w1, [x0, #0xb]
    // 0x1349148: DecompressPointer r1
    //     0x1349148: add             x1, x1, HEAP, lsl #32
    // 0x134914c: cmp             w1, NULL
    // 0x1349150: b.ne            #0x134915c
    // 0x1349154: r0 = Null
    //     0x1349154: mov             x0, NULL
    // 0x1349158: b               #0x1349178
    // 0x134915c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x134915c: ldur            w0, [x1, #0x17]
    // 0x1349160: DecompressPointer r0
    //     0x1349160: add             x0, x0, HEAP, lsl #32
    // 0x1349164: LoadField: r1 = r0->field_b
    //     0x1349164: ldur            w1, [x0, #0xb]
    // 0x1349168: cbnz            w1, #0x1349174
    // 0x134916c: r0 = false
    //     0x134916c: add             x0, NULL, #0x30  ; false
    // 0x1349170: b               #0x1349178
    // 0x1349174: r0 = true
    //     0x1349174: add             x0, NULL, #0x20  ; true
    // 0x1349178: cmp             w0, NULL
    // 0x134917c: b.ne            #0x1349184
    // 0x1349180: r0 = false
    //     0x1349180: add             x0, NULL, #0x30  ; false
    // 0x1349184: ldur            x2, [fp, #-8]
    // 0x1349188: stur            x0, [fp, #-0x28]
    // 0x134918c: LoadField: r1 = r2->field_f
    //     0x134918c: ldur            w1, [x2, #0xf]
    // 0x1349190: DecompressPointer r1
    //     0x1349190: add             x1, x1, HEAP, lsl #32
    // 0x1349194: r0 = controller()
    //     0x1349194: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1349198: LoadField: r1 = r0->field_5b
    //     0x1349198: ldur            w1, [x0, #0x5b]
    // 0x134919c: DecompressPointer r1
    //     0x134919c: add             x1, x1, HEAP, lsl #32
    // 0x13491a0: r0 = value()
    //     0x13491a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13491a4: LoadField: r2 = r0->field_b
    //     0x13491a4: ldur            w2, [x0, #0xb]
    // 0x13491a8: DecompressPointer r2
    //     0x13491a8: add             x2, x2, HEAP, lsl #32
    // 0x13491ac: ldur            x0, [fp, #-8]
    // 0x13491b0: stur            x2, [fp, #-0x38]
    // 0x13491b4: LoadField: r1 = r0->field_f
    //     0x13491b4: ldur            w1, [x0, #0xf]
    // 0x13491b8: DecompressPointer r1
    //     0x13491b8: add             x1, x1, HEAP, lsl #32
    // 0x13491bc: r0 = controller()
    //     0x13491bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13491c0: LoadField: r1 = r0->field_63
    //     0x13491c0: ldur            w1, [x0, #0x63]
    // 0x13491c4: DecompressPointer r1
    //     0x13491c4: add             x1, x1, HEAP, lsl #32
    // 0x13491c8: r0 = value()
    //     0x13491c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13491cc: ldur            x2, [fp, #-8]
    // 0x13491d0: LoadField: r1 = r2->field_f
    //     0x13491d0: ldur            w1, [x2, #0xf]
    // 0x13491d4: DecompressPointer r1
    //     0x13491d4: add             x1, x1, HEAP, lsl #32
    // 0x13491d8: r0 = controller()
    //     0x13491d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13491dc: LoadField: r1 = r0->field_83
    //     0x13491dc: ldur            w1, [x0, #0x83]
    // 0x13491e0: DecompressPointer r1
    //     0x13491e0: add             x1, x1, HEAP, lsl #32
    // 0x13491e4: cmp             w1, NULL
    // 0x13491e8: b.ne            #0x13491f4
    // 0x13491ec: r4 = ""
    //     0x13491ec: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13491f0: b               #0x13491f8
    // 0x13491f4: mov             x4, x1
    // 0x13491f8: ldur            x3, [fp, #-0x10]
    // 0x13491fc: ldur            x2, [fp, #-0x18]
    // 0x1349200: ldur            x1, [fp, #-0x28]
    // 0x1349204: ldur            x0, [fp, #-0x38]
    // 0x1349208: stur            x4, [fp, #-0x40]
    // 0x134920c: r0 = BottomView()
    //     0x134920c: bl              #0x13492a8  ; AllocateBottomViewStub -> BottomView (size=0x20)
    // 0x1349210: mov             x3, x0
    // 0x1349214: ldur            x0, [fp, #-0x38]
    // 0x1349218: stur            x3, [fp, #-0x48]
    // 0x134921c: StoreField: r3->field_b = r0
    //     0x134921c: stur            w0, [x3, #0xb]
    // 0x1349220: ldur            x0, [fp, #-0x10]
    // 0x1349224: StoreField: r3->field_f = r0
    //     0x1349224: stur            w0, [x3, #0xf]
    // 0x1349228: ldur            x0, [fp, #-0x18]
    // 0x134922c: StoreField: r3->field_13 = r0
    //     0x134922c: stur            w0, [x3, #0x13]
    // 0x1349230: ldur            x2, [fp, #-8]
    // 0x1349234: r1 = Function '<anonymous closure>':.
    //     0x1349234: add             x1, PP, #0x44, lsl #12  ; [pp+0x448c0] AnonymousClosure: (0x12c00ec), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::bottomNavigationBar (0x13659c0)
    //     0x1349238: ldr             x1, [x1, #0x8c0]
    // 0x134923c: r0 = AllocateClosure()
    //     0x134923c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1349240: mov             x1, x0
    // 0x1349244: ldur            x0, [fp, #-0x48]
    // 0x1349248: ArrayStore: r0[0] = r1  ; List_4
    //     0x1349248: stur            w1, [x0, #0x17]
    // 0x134924c: ldur            x1, [fp, #-0x40]
    // 0x1349250: StoreField: r0->field_1b = r1
    //     0x1349250: stur            w1, [x0, #0x1b]
    // 0x1349254: r0 = Visibility()
    //     0x1349254: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x1349258: ldur            x1, [fp, #-0x48]
    // 0x134925c: StoreField: r0->field_b = r1
    //     0x134925c: stur            w1, [x0, #0xb]
    // 0x1349260: r1 = Instance_SizedBox
    //     0x1349260: ldr             x1, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x1349264: StoreField: r0->field_f = r1
    //     0x1349264: stur            w1, [x0, #0xf]
    // 0x1349268: ldur            x1, [fp, #-0x28]
    // 0x134926c: StoreField: r0->field_13 = r1
    //     0x134926c: stur            w1, [x0, #0x13]
    // 0x1349270: r1 = false
    //     0x1349270: add             x1, NULL, #0x30  ; false
    // 0x1349274: ArrayStore: r0[0] = r1  ; List_4
    //     0x1349274: stur            w1, [x0, #0x17]
    // 0x1349278: StoreField: r0->field_1b = r1
    //     0x1349278: stur            w1, [x0, #0x1b]
    // 0x134927c: StoreField: r0->field_1f = r1
    //     0x134927c: stur            w1, [x0, #0x1f]
    // 0x1349280: StoreField: r0->field_23 = r1
    //     0x1349280: stur            w1, [x0, #0x23]
    // 0x1349284: StoreField: r0->field_27 = r1
    //     0x1349284: stur            w1, [x0, #0x27]
    // 0x1349288: StoreField: r0->field_2b = r1
    //     0x1349288: stur            w1, [x0, #0x2b]
    // 0x134928c: LeaveFrame
    //     0x134928c: mov             SP, fp
    //     0x1349290: ldp             fp, lr, [SP], #0x10
    // 0x1349294: ret
    //     0x1349294: ret             
    // 0x1349298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1349298: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x134929c: b               #0x1348ec4
    // 0x13492a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13492a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13492a4: b               #0x1349068
  }
  _ body(/* No info */) {
    // ** addr: 0x147c4d4, size: 0x94
    // 0x147c4d4: EnterFrame
    //     0x147c4d4: stp             fp, lr, [SP, #-0x10]!
    //     0x147c4d8: mov             fp, SP
    // 0x147c4dc: AllocStack(0x18)
    //     0x147c4dc: sub             SP, SP, #0x18
    // 0x147c4e0: SetupParameters(BagView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x147c4e0: stur            x1, [fp, #-8]
    //     0x147c4e4: stur            x2, [fp, #-0x10]
    // 0x147c4e8: r1 = 2
    //     0x147c4e8: movz            x1, #0x2
    // 0x147c4ec: r0 = AllocateContext()
    //     0x147c4ec: bl              #0x16f6108  ; AllocateContextStub
    // 0x147c4f0: mov             x1, x0
    // 0x147c4f4: ldur            x0, [fp, #-8]
    // 0x147c4f8: stur            x1, [fp, #-0x18]
    // 0x147c4fc: StoreField: r1->field_f = r0
    //     0x147c4fc: stur            w0, [x1, #0xf]
    // 0x147c500: ldur            x0, [fp, #-0x10]
    // 0x147c504: StoreField: r1->field_13 = r0
    //     0x147c504: stur            w0, [x1, #0x13]
    // 0x147c508: r0 = Obx()
    //     0x147c508: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x147c50c: ldur            x2, [fp, #-0x18]
    // 0x147c510: r1 = Function '<anonymous closure>':.
    //     0x147c510: add             x1, PP, #0x44, lsl #12  ; [pp+0x448c8] AnonymousClosure: (0x147c568), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147c514: ldr             x1, [x1, #0x8c8]
    // 0x147c518: stur            x0, [fp, #-8]
    // 0x147c51c: r0 = AllocateClosure()
    //     0x147c51c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c520: mov             x1, x0
    // 0x147c524: ldur            x0, [fp, #-8]
    // 0x147c528: StoreField: r0->field_b = r1
    //     0x147c528: stur            w1, [x0, #0xb]
    // 0x147c52c: r0 = WillPopScope()
    //     0x147c52c: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x147c530: mov             x3, x0
    // 0x147c534: ldur            x0, [fp, #-8]
    // 0x147c538: stur            x3, [fp, #-0x10]
    // 0x147c53c: StoreField: r3->field_b = r0
    //     0x147c53c: stur            w0, [x3, #0xb]
    // 0x147c540: ldur            x2, [fp, #-0x18]
    // 0x147c544: r1 = Function '<anonymous closure>':.
    //     0x147c544: add             x1, PP, #0x44, lsl #12  ; [pp+0x448d0] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x147c548: ldr             x1, [x1, #0x8d0]
    // 0x147c54c: r0 = AllocateClosure()
    //     0x147c54c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c550: mov             x1, x0
    // 0x147c554: ldur            x0, [fp, #-0x10]
    // 0x147c558: StoreField: r0->field_f = r1
    //     0x147c558: stur            w1, [x0, #0xf]
    // 0x147c55c: LeaveFrame
    //     0x147c55c: mov             SP, fp
    //     0x147c560: ldp             fp, lr, [SP], #0x10
    // 0x147c564: ret
    //     0x147c564: ret             
  }
  [closure] Widget <anonymous closure>(dynamic) {
    // ** addr: 0x147c568, size: 0x3f8
    // 0x147c568: EnterFrame
    //     0x147c568: stp             fp, lr, [SP, #-0x10]!
    //     0x147c56c: mov             fp, SP
    // 0x147c570: AllocStack(0x58)
    //     0x147c570: sub             SP, SP, #0x58
    // 0x147c574: SetupParameters()
    //     0x147c574: ldr             x0, [fp, #0x10]
    //     0x147c578: ldur            w2, [x0, #0x17]
    //     0x147c57c: add             x2, x2, HEAP, lsl #32
    //     0x147c580: stur            x2, [fp, #-8]
    // 0x147c584: CheckStackOverflow
    //     0x147c584: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147c588: cmp             SP, x16
    //     0x147c58c: b.ls            #0x147c958
    // 0x147c590: LoadField: r1 = r2->field_f
    //     0x147c590: ldur            w1, [x2, #0xf]
    // 0x147c594: DecompressPointer r1
    //     0x147c594: add             x1, x1, HEAP, lsl #32
    // 0x147c598: r0 = controller()
    //     0x147c598: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c59c: LoadField: r1 = r0->field_5b
    //     0x147c59c: ldur            w1, [x0, #0x5b]
    // 0x147c5a0: DecompressPointer r1
    //     0x147c5a0: add             x1, x1, HEAP, lsl #32
    // 0x147c5a4: r0 = value()
    //     0x147c5a4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c5a8: LoadField: r1 = r0->field_b
    //     0x147c5a8: ldur            w1, [x0, #0xb]
    // 0x147c5ac: DecompressPointer r1
    //     0x147c5ac: add             x1, x1, HEAP, lsl #32
    // 0x147c5b0: cmp             w1, NULL
    // 0x147c5b4: b.ne            #0x147c5c0
    // 0x147c5b8: r0 = Null
    //     0x147c5b8: mov             x0, NULL
    // 0x147c5bc: b               #0x147c5dc
    // 0x147c5c0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x147c5c0: ldur            w0, [x1, #0x17]
    // 0x147c5c4: DecompressPointer r0
    //     0x147c5c4: add             x0, x0, HEAP, lsl #32
    // 0x147c5c8: LoadField: r1 = r0->field_b
    //     0x147c5c8: ldur            w1, [x0, #0xb]
    // 0x147c5cc: cbnz            w1, #0x147c5d8
    // 0x147c5d0: r0 = false
    //     0x147c5d0: add             x0, NULL, #0x30  ; false
    // 0x147c5d4: b               #0x147c5dc
    // 0x147c5d8: r0 = true
    //     0x147c5d8: add             x0, NULL, #0x20  ; true
    // 0x147c5dc: cmp             w0, NULL
    // 0x147c5e0: b.eq            #0x147c8e0
    // 0x147c5e4: tbnz            w0, #4, #0x147c8e0
    // 0x147c5e8: ldur            x2, [fp, #-8]
    // 0x147c5ec: LoadField: r1 = r2->field_f
    //     0x147c5ec: ldur            w1, [x2, #0xf]
    // 0x147c5f0: DecompressPointer r1
    //     0x147c5f0: add             x1, x1, HEAP, lsl #32
    // 0x147c5f4: r0 = controller()
    //     0x147c5f4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c5f8: LoadField: r1 = r0->field_5b
    //     0x147c5f8: ldur            w1, [x0, #0x5b]
    // 0x147c5fc: DecompressPointer r1
    //     0x147c5fc: add             x1, x1, HEAP, lsl #32
    // 0x147c600: r0 = value()
    //     0x147c600: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c604: LoadField: r2 = r0->field_b
    //     0x147c604: ldur            w2, [x0, #0xb]
    // 0x147c608: DecompressPointer r2
    //     0x147c608: add             x2, x2, HEAP, lsl #32
    // 0x147c60c: ldur            x0, [fp, #-8]
    // 0x147c610: stur            x2, [fp, #-0x10]
    // 0x147c614: LoadField: r1 = r0->field_f
    //     0x147c614: ldur            w1, [x0, #0xf]
    // 0x147c618: DecompressPointer r1
    //     0x147c618: add             x1, x1, HEAP, lsl #32
    // 0x147c61c: r0 = controller()
    //     0x147c61c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c620: LoadField: r1 = r0->field_63
    //     0x147c620: ldur            w1, [x0, #0x63]
    // 0x147c624: DecompressPointer r1
    //     0x147c624: add             x1, x1, HEAP, lsl #32
    // 0x147c628: r0 = value()
    //     0x147c628: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c62c: LoadField: r2 = r0->field_b
    //     0x147c62c: ldur            w2, [x0, #0xb]
    // 0x147c630: DecompressPointer r2
    //     0x147c630: add             x2, x2, HEAP, lsl #32
    // 0x147c634: ldur            x0, [fp, #-8]
    // 0x147c638: stur            x2, [fp, #-0x18]
    // 0x147c63c: LoadField: r1 = r0->field_f
    //     0x147c63c: ldur            w1, [x0, #0xf]
    // 0x147c640: DecompressPointer r1
    //     0x147c640: add             x1, x1, HEAP, lsl #32
    // 0x147c644: r0 = controller()
    //     0x147c644: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c648: LoadField: r1 = r0->field_77
    //     0x147c648: ldur            w1, [x0, #0x77]
    // 0x147c64c: DecompressPointer r1
    //     0x147c64c: add             x1, x1, HEAP, lsl #32
    // 0x147c650: r0 = value()
    //     0x147c650: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c654: ldur            x2, [fp, #-8]
    // 0x147c658: stur            x0, [fp, #-0x20]
    // 0x147c65c: LoadField: r1 = r2->field_f
    //     0x147c65c: ldur            w1, [x2, #0xf]
    // 0x147c660: DecompressPointer r1
    //     0x147c660: add             x1, x1, HEAP, lsl #32
    // 0x147c664: r0 = controller()
    //     0x147c664: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c668: LoadField: r2 = r0->field_87
    //     0x147c668: ldur            w2, [x0, #0x87]
    // 0x147c66c: DecompressPointer r2
    //     0x147c66c: add             x2, x2, HEAP, lsl #32
    // 0x147c670: ldur            x0, [fp, #-8]
    // 0x147c674: stur            x2, [fp, #-0x28]
    // 0x147c678: LoadField: r1 = r0->field_f
    //     0x147c678: ldur            w1, [x0, #0xf]
    // 0x147c67c: DecompressPointer r1
    //     0x147c67c: add             x1, x1, HEAP, lsl #32
    // 0x147c680: r0 = controller()
    //     0x147c680: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c684: LoadField: r1 = r0->field_7f
    //     0x147c684: ldur            w1, [x0, #0x7f]
    // 0x147c688: DecompressPointer r1
    //     0x147c688: add             x1, x1, HEAP, lsl #32
    // 0x147c68c: r0 = value()
    //     0x147c68c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c690: ldur            x2, [fp, #-8]
    // 0x147c694: stur            x0, [fp, #-0x30]
    // 0x147c698: LoadField: r1 = r2->field_f
    //     0x147c698: ldur            w1, [x2, #0xf]
    // 0x147c69c: DecompressPointer r1
    //     0x147c69c: add             x1, x1, HEAP, lsl #32
    // 0x147c6a0: r0 = controller()
    //     0x147c6a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c6a4: LoadField: r1 = r0->field_6f
    //     0x147c6a4: ldur            w1, [x0, #0x6f]
    // 0x147c6a8: DecompressPointer r1
    //     0x147c6a8: add             x1, x1, HEAP, lsl #32
    // 0x147c6ac: r0 = value()
    //     0x147c6ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c6b0: LoadField: r1 = r0->field_b
    //     0x147c6b0: ldur            w1, [x0, #0xb]
    // 0x147c6b4: DecompressPointer r1
    //     0x147c6b4: add             x1, x1, HEAP, lsl #32
    // 0x147c6b8: cmp             w1, NULL
    // 0x147c6bc: b.ne            #0x147c6c8
    // 0x147c6c0: r0 = Null
    //     0x147c6c0: mov             x0, NULL
    // 0x147c6c4: b               #0x147c6d0
    // 0x147c6c8: LoadField: r0 = r1->field_7
    //     0x147c6c8: ldur            w0, [x1, #7]
    // 0x147c6cc: DecompressPointer r0
    //     0x147c6cc: add             x0, x0, HEAP, lsl #32
    // 0x147c6d0: cmp             w0, NULL
    // 0x147c6d4: b.ne            #0x147c6e0
    // 0x147c6d8: r4 = ""
    //     0x147c6d8: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x147c6dc: b               #0x147c6e4
    // 0x147c6e0: mov             x4, x0
    // 0x147c6e4: ldur            x3, [fp, #-8]
    // 0x147c6e8: mov             x0, x4
    // 0x147c6ec: stur            x4, [fp, #-0x38]
    // 0x147c6f0: r2 = Null
    //     0x147c6f0: mov             x2, NULL
    // 0x147c6f4: r1 = Null
    //     0x147c6f4: mov             x1, NULL
    // 0x147c6f8: r4 = 60
    //     0x147c6f8: movz            x4, #0x3c
    // 0x147c6fc: branchIfSmi(r0, 0x147c708)
    //     0x147c6fc: tbz             w0, #0, #0x147c708
    // 0x147c700: r4 = LoadClassIdInstr(r0)
    //     0x147c700: ldur            x4, [x0, #-1]
    //     0x147c704: ubfx            x4, x4, #0xc, #0x14
    // 0x147c708: sub             x4, x4, #0x5e
    // 0x147c70c: cmp             x4, #1
    // 0x147c710: b.ls            #0x147c724
    // 0x147c714: r8 = String
    //     0x147c714: ldr             x8, [PP, #0x958]  ; [pp+0x958] Type: String
    // 0x147c718: r3 = Null
    //     0x147c718: add             x3, PP, #0x44, lsl #12  ; [pp+0x448d8] Null
    //     0x147c71c: ldr             x3, [x3, #0x8d8]
    // 0x147c720: r0 = String()
    //     0x147c720: bl              #0x16fbe18  ; IsType_String_Stub
    // 0x147c724: ldur            x2, [fp, #-8]
    // 0x147c728: LoadField: r1 = r2->field_f
    //     0x147c728: ldur            w1, [x2, #0xf]
    // 0x147c72c: DecompressPointer r1
    //     0x147c72c: add             x1, x1, HEAP, lsl #32
    // 0x147c730: r0 = controller()
    //     0x147c730: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c734: LoadField: r1 = r0->field_83
    //     0x147c734: ldur            w1, [x0, #0x83]
    // 0x147c738: DecompressPointer r1
    //     0x147c738: add             x1, x1, HEAP, lsl #32
    // 0x147c73c: cmp             w1, NULL
    // 0x147c740: b.ne            #0x147c74c
    // 0x147c744: r8 = ""
    //     0x147c744: ldr             x8, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x147c748: b               #0x147c750
    // 0x147c74c: mov             x8, x1
    // 0x147c750: ldur            x2, [fp, #-8]
    // 0x147c754: ldur            x7, [fp, #-0x10]
    // 0x147c758: ldur            x6, [fp, #-0x18]
    // 0x147c75c: ldur            x4, [fp, #-0x28]
    // 0x147c760: ldur            x0, [fp, #-0x38]
    // 0x147c764: ldur            x5, [fp, #-0x20]
    // 0x147c768: ldur            x3, [fp, #-0x30]
    // 0x147c76c: stur            x8, [fp, #-0x40]
    // 0x147c770: LoadField: r1 = r2->field_f
    //     0x147c770: ldur            w1, [x2, #0xf]
    // 0x147c774: DecompressPointer r1
    //     0x147c774: add             x1, x1, HEAP, lsl #32
    // 0x147c778: r0 = controller()
    //     0x147c778: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c77c: LoadField: r2 = r0->field_6b
    //     0x147c77c: ldur            w2, [x0, #0x6b]
    // 0x147c780: DecompressPointer r2
    //     0x147c780: add             x2, x2, HEAP, lsl #32
    // 0x147c784: ldur            x0, [fp, #-8]
    // 0x147c788: stur            x2, [fp, #-0x48]
    // 0x147c78c: LoadField: r1 = r0->field_f
    //     0x147c78c: ldur            w1, [x0, #0xf]
    // 0x147c790: DecompressPointer r1
    //     0x147c790: add             x1, x1, HEAP, lsl #32
    // 0x147c794: r0 = controller()
    //     0x147c794: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c798: LoadField: r1 = r0->field_ab
    //     0x147c798: ldur            w1, [x0, #0xab]
    // 0x147c79c: DecompressPointer r1
    //     0x147c79c: add             x1, x1, HEAP, lsl #32
    // 0x147c7a0: r0 = value()
    //     0x147c7a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c7a4: stur            x0, [fp, #-0x50]
    // 0x147c7a8: r0 = BagItemViewWidget()
    //     0x147c7a8: bl              #0x147c960  ; AllocateBagItemViewWidgetStub -> BagItemViewWidget (size=0x50)
    // 0x147c7ac: mov             x3, x0
    // 0x147c7b0: ldur            x0, [fp, #-0x10]
    // 0x147c7b4: stur            x3, [fp, #-0x58]
    // 0x147c7b8: StoreField: r3->field_b = r0
    //     0x147c7b8: stur            w0, [x3, #0xb]
    // 0x147c7bc: ldur            x0, [fp, #-0x18]
    // 0x147c7c0: StoreField: r3->field_f = r0
    //     0x147c7c0: stur            w0, [x3, #0xf]
    // 0x147c7c4: ldur            x2, [fp, #-8]
    // 0x147c7c8: r1 = Function '<anonymous closure>':.
    //     0x147c7c8: add             x1, PP, #0x44, lsl #12  ; [pp+0x448e8] AnonymousClosure: (0x147d4ec), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147c7cc: ldr             x1, [x1, #0x8e8]
    // 0x147c7d0: r0 = AllocateClosure()
    //     0x147c7d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c7d4: mov             x1, x0
    // 0x147c7d8: ldur            x0, [fp, #-0x58]
    // 0x147c7dc: StoreField: r0->field_13 = r1
    //     0x147c7dc: stur            w1, [x0, #0x13]
    // 0x147c7e0: ldur            x2, [fp, #-8]
    // 0x147c7e4: r1 = Function '<anonymous closure>':.
    //     0x147c7e4: add             x1, PP, #0x44, lsl #12  ; [pp+0x448f0] AnonymousClosure: (0x147d104), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147c7e8: ldr             x1, [x1, #0x8f0]
    // 0x147c7ec: r0 = AllocateClosure()
    //     0x147c7ec: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c7f0: mov             x1, x0
    // 0x147c7f4: ldur            x0, [fp, #-0x58]
    // 0x147c7f8: ArrayStore: r0[0] = r1  ; List_4
    //     0x147c7f8: stur            w1, [x0, #0x17]
    // 0x147c7fc: ldur            x1, [fp, #-0x50]
    // 0x147c800: StoreField: r0->field_33 = r1
    //     0x147c800: stur            w1, [x0, #0x33]
    // 0x147c804: ldur            x2, [fp, #-8]
    // 0x147c808: r1 = Function '<anonymous closure>':.
    //     0x147c808: add             x1, PP, #0x44, lsl #12  ; [pp+0x448f8] AnonymousClosure: (0x147cde0), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147c80c: ldr             x1, [x1, #0x8f8]
    // 0x147c810: r0 = AllocateClosure()
    //     0x147c810: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c814: mov             x1, x0
    // 0x147c818: ldur            x0, [fp, #-0x58]
    // 0x147c81c: StoreField: r0->field_1b = r1
    //     0x147c81c: stur            w1, [x0, #0x1b]
    // 0x147c820: ldur            x2, [fp, #-8]
    // 0x147c824: r1 = Function '<anonymous closure>':.
    //     0x147c824: add             x1, PP, #0x44, lsl #12  ; [pp+0x44900] AnonymousClosure: (0x147cafc), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147c828: ldr             x1, [x1, #0x900]
    // 0x147c82c: r0 = AllocateClosure()
    //     0x147c82c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c830: mov             x1, x0
    // 0x147c834: ldur            x0, [fp, #-0x58]
    // 0x147c838: StoreField: r0->field_1f = r1
    //     0x147c838: stur            w1, [x0, #0x1f]
    // 0x147c83c: ldur            x2, [fp, #-8]
    // 0x147c840: r1 = Function '<anonymous closure>':.
    //     0x147c840: add             x1, PP, #0x44, lsl #12  ; [pp+0x44908] AnonymousClosure: (0x135a4e4), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x147c844: ldr             x1, [x1, #0x908]
    // 0x147c848: r0 = AllocateClosure()
    //     0x147c848: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c84c: mov             x1, x0
    // 0x147c850: ldur            x0, [fp, #-0x58]
    // 0x147c854: StoreField: r0->field_23 = r1
    //     0x147c854: stur            w1, [x0, #0x23]
    // 0x147c858: ldur            x2, [fp, #-8]
    // 0x147c85c: r1 = Function '<anonymous closure>':.
    //     0x147c85c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44910] AnonymousClosure: (0x135a3e8), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x147c860: ldr             x1, [x1, #0x910]
    // 0x147c864: r0 = AllocateClosure()
    //     0x147c864: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c868: mov             x1, x0
    // 0x147c86c: ldur            x0, [fp, #-0x58]
    // 0x147c870: StoreField: r0->field_27 = r1
    //     0x147c870: stur            w1, [x0, #0x27]
    // 0x147c874: ldur            x2, [fp, #-8]
    // 0x147c878: r1 = Function '<anonymous closure>':.
    //     0x147c878: add             x1, PP, #0x44, lsl #12  ; [pp+0x44918] AnonymousClosure: (0x147c96c), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147c87c: ldr             x1, [x1, #0x918]
    // 0x147c880: r0 = AllocateClosure()
    //     0x147c880: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c884: mov             x1, x0
    // 0x147c888: ldur            x0, [fp, #-0x58]
    // 0x147c88c: StoreField: r0->field_2b = r1
    //     0x147c88c: stur            w1, [x0, #0x2b]
    // 0x147c890: ldur            x2, [fp, #-8]
    // 0x147c894: r1 = Function '<anonymous closure>':.
    //     0x147c894: add             x1, PP, #0x44, lsl #12  ; [pp+0x44920] AnonymousClosure: (0x137af74), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::body (0x14d2e60)
    //     0x147c898: ldr             x1, [x1, #0x920]
    // 0x147c89c: r0 = AllocateClosure()
    //     0x147c89c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c8a0: mov             x1, x0
    // 0x147c8a4: ldur            x0, [fp, #-0x58]
    // 0x147c8a8: StoreField: r0->field_2f = r1
    //     0x147c8a8: stur            w1, [x0, #0x2f]
    // 0x147c8ac: ldur            x1, [fp, #-0x20]
    // 0x147c8b0: StoreField: r0->field_37 = r1
    //     0x147c8b0: stur            w1, [x0, #0x37]
    // 0x147c8b4: ldur            x1, [fp, #-0x30]
    // 0x147c8b8: StoreField: r0->field_3b = r1
    //     0x147c8b8: stur            w1, [x0, #0x3b]
    // 0x147c8bc: ldur            x1, [fp, #-0x38]
    // 0x147c8c0: StoreField: r0->field_3f = r1
    //     0x147c8c0: stur            w1, [x0, #0x3f]
    // 0x147c8c4: ldur            x1, [fp, #-0x40]
    // 0x147c8c8: StoreField: r0->field_43 = r1
    //     0x147c8c8: stur            w1, [x0, #0x43]
    // 0x147c8cc: ldur            x1, [fp, #-0x48]
    // 0x147c8d0: StoreField: r0->field_47 = r1
    //     0x147c8d0: stur            w1, [x0, #0x47]
    // 0x147c8d4: ldur            x1, [fp, #-0x28]
    // 0x147c8d8: StoreField: r0->field_4b = r1
    //     0x147c8d8: stur            w1, [x0, #0x4b]
    // 0x147c8dc: b               #0x147c94c
    // 0x147c8e0: ldur            x0, [fp, #-8]
    // 0x147c8e4: LoadField: r1 = r0->field_f
    //     0x147c8e4: ldur            w1, [x0, #0xf]
    // 0x147c8e8: DecompressPointer r1
    //     0x147c8e8: add             x1, x1, HEAP, lsl #32
    // 0x147c8ec: r0 = controller()
    //     0x147c8ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147c8f0: LoadField: r1 = r0->field_5b
    //     0x147c8f0: ldur            w1, [x0, #0x5b]
    // 0x147c8f4: DecompressPointer r1
    //     0x147c8f4: add             x1, x1, HEAP, lsl #32
    // 0x147c8f8: r0 = value()
    //     0x147c8f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147c8fc: LoadField: r1 = r0->field_b
    //     0x147c8fc: ldur            w1, [x0, #0xb]
    // 0x147c900: DecompressPointer r1
    //     0x147c900: add             x1, x1, HEAP, lsl #32
    // 0x147c904: cmp             w1, NULL
    // 0x147c908: b.ne            #0x147c928
    // 0x147c90c: r0 = Container()
    //     0x147c90c: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x147c910: mov             x1, x0
    // 0x147c914: stur            x0, [fp, #-8]
    // 0x147c918: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x147c918: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x147c91c: r0 = Container()
    //     0x147c91c: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x147c920: ldur            x0, [fp, #-8]
    // 0x147c924: b               #0x147c930
    // 0x147c928: r0 = Instance_EmptyBagWidget
    //     0x147c928: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e718] Obj!EmptyBagWidget@d66d81
    //     0x147c92c: ldr             x0, [x0, #0x718]
    // 0x147c930: stur            x0, [fp, #-8]
    // 0x147c934: r0 = Center()
    //     0x147c934: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x147c938: r1 = Instance_Alignment
    //     0x147c938: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x147c93c: ldr             x1, [x1, #0xb10]
    // 0x147c940: StoreField: r0->field_f = r1
    //     0x147c940: stur            w1, [x0, #0xf]
    // 0x147c944: ldur            x1, [fp, #-8]
    // 0x147c948: StoreField: r0->field_b = r1
    //     0x147c948: stur            w1, [x0, #0xb]
    // 0x147c94c: LeaveFrame
    //     0x147c94c: mov             SP, fp
    //     0x147c950: ldp             fp, lr, [SP], #0x10
    // 0x147c954: ret
    //     0x147c954: ret             
    // 0x147c958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147c958: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147c95c: b               #0x147c590
  }
  [closure] Null <anonymous closure>(dynamic, int?, Catalogue) {
    // ** addr: 0x147c96c, size: 0xb0
    // 0x147c96c: EnterFrame
    //     0x147c96c: stp             fp, lr, [SP, #-0x10]!
    //     0x147c970: mov             fp, SP
    // 0x147c974: AllocStack(0x40)
    //     0x147c974: sub             SP, SP, #0x40
    // 0x147c978: SetupParameters()
    //     0x147c978: ldr             x0, [fp, #0x20]
    //     0x147c97c: ldur            w1, [x0, #0x17]
    //     0x147c980: add             x1, x1, HEAP, lsl #32
    //     0x147c984: stur            x1, [fp, #-8]
    // 0x147c988: CheckStackOverflow
    //     0x147c988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147c98c: cmp             SP, x16
    //     0x147c990: b.ls            #0x147ca14
    // 0x147c994: r1 = 2
    //     0x147c994: movz            x1, #0x2
    // 0x147c998: r0 = AllocateContext()
    //     0x147c998: bl              #0x16f6108  ; AllocateContextStub
    // 0x147c99c: mov             x1, x0
    // 0x147c9a0: ldur            x0, [fp, #-8]
    // 0x147c9a4: StoreField: r1->field_b = r0
    //     0x147c9a4: stur            w0, [x1, #0xb]
    // 0x147c9a8: ldr             x2, [fp, #0x18]
    // 0x147c9ac: StoreField: r1->field_f = r2
    //     0x147c9ac: stur            w2, [x1, #0xf]
    // 0x147c9b0: ldr             x2, [fp, #0x10]
    // 0x147c9b4: StoreField: r1->field_13 = r2
    //     0x147c9b4: stur            w2, [x1, #0x13]
    // 0x147c9b8: LoadField: r3 = r0->field_13
    //     0x147c9b8: ldur            w3, [x0, #0x13]
    // 0x147c9bc: DecompressPointer r3
    //     0x147c9bc: add             x3, x3, HEAP, lsl #32
    // 0x147c9c0: mov             x2, x1
    // 0x147c9c4: stur            x3, [fp, #-0x10]
    // 0x147c9c8: r1 = Function '<anonymous closure>':.
    //     0x147c9c8: add             x1, PP, #0x44, lsl #12  ; [pp+0x44928] AnonymousClosure: (0x147ca1c), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147c9cc: ldr             x1, [x1, #0x928]
    // 0x147c9d0: r0 = AllocateClosure()
    //     0x147c9d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147c9d4: stp             x0, NULL, [SP, #0x20]
    // 0x147c9d8: ldur            x16, [fp, #-0x10]
    // 0x147c9dc: r30 = Instance_RoundedRectangleBorder
    //     0x147c9dc: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x147c9e0: ldr             lr, [lr, #0xc78]
    // 0x147c9e4: stp             lr, x16, [SP, #0x10]
    // 0x147c9e8: r16 = Instance_Color
    //     0x147c9e8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x147c9ec: ldr             x16, [x16, #0x90]
    // 0x147c9f0: r30 = true
    //     0x147c9f0: add             lr, NULL, #0x20  ; true
    // 0x147c9f4: stp             lr, x16, [SP]
    // 0x147c9f8: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x4, shape, 0x2, null]
    //     0x147c9f8: add             x4, PP, #0x44, lsl #12  ; [pp+0x44930] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x4, "shape", 0x2, Null]
    //     0x147c9fc: ldr             x4, [x4, #0x930]
    // 0x147ca00: r0 = showModalBottomSheet()
    //     0x147ca00: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x147ca04: r0 = Null
    //     0x147ca04: mov             x0, NULL
    // 0x147ca08: LeaveFrame
    //     0x147ca08: mov             SP, fp
    //     0x147ca0c: ldp             fp, lr, [SP], #0x10
    // 0x147ca10: ret
    //     0x147ca10: ret             
    // 0x147ca14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147ca14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147ca18: b               #0x147c994
  }
  [closure] BagBottomSheetToChoose <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x147ca1c, size: 0xd4
    // 0x147ca1c: EnterFrame
    //     0x147ca1c: stp             fp, lr, [SP, #-0x10]!
    //     0x147ca20: mov             fp, SP
    // 0x147ca24: AllocStack(0x28)
    //     0x147ca24: sub             SP, SP, #0x28
    // 0x147ca28: SetupParameters()
    //     0x147ca28: ldr             x0, [fp, #0x18]
    //     0x147ca2c: ldur            w2, [x0, #0x17]
    //     0x147ca30: add             x2, x2, HEAP, lsl #32
    //     0x147ca34: stur            x2, [fp, #-0x20]
    // 0x147ca38: LoadField: r3 = r2->field_f
    //     0x147ca38: ldur            w3, [x2, #0xf]
    // 0x147ca3c: DecompressPointer r3
    //     0x147ca3c: add             x3, x3, HEAP, lsl #32
    // 0x147ca40: stur            x3, [fp, #-0x18]
    // 0x147ca44: cmp             w3, NULL
    // 0x147ca48: b.eq            #0x147ca74
    // 0x147ca4c: r0 = LoadInt32Instr(r3)
    //     0x147ca4c: sbfx            x0, x3, #1, #0x1f
    //     0x147ca50: tbz             w3, #0, #0x147ca58
    //     0x147ca54: ldur            x0, [x3, #7]
    // 0x147ca58: add             x4, x0, #1
    // 0x147ca5c: r0 = BoxInt64Instr(r4)
    //     0x147ca5c: sbfiz           x0, x4, #1, #0x1f
    //     0x147ca60: cmp             x4, x0, asr #1
    //     0x147ca64: b.eq            #0x147ca70
    //     0x147ca68: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x147ca6c: stur            x4, [x0, #7]
    // 0x147ca70: b               #0x147ca78
    // 0x147ca74: mov             x0, x3
    // 0x147ca78: stur            x0, [fp, #-0x10]
    // 0x147ca7c: LoadField: r1 = r2->field_13
    //     0x147ca7c: ldur            w1, [x2, #0x13]
    // 0x147ca80: DecompressPointer r1
    //     0x147ca80: add             x1, x1, HEAP, lsl #32
    // 0x147ca84: stur            x1, [fp, #-8]
    // 0x147ca88: r0 = BagBottomSheetToChoose()
    //     0x147ca88: bl              #0x147caf0  ; AllocateBagBottomSheetToChooseStub -> BagBottomSheetToChoose (size=0x20)
    // 0x147ca8c: mov             x3, x0
    // 0x147ca90: ldur            x0, [fp, #-0x10]
    // 0x147ca94: stur            x3, [fp, #-0x28]
    // 0x147ca98: StoreField: r3->field_b = r0
    //     0x147ca98: stur            w0, [x3, #0xb]
    // 0x147ca9c: ldur            x0, [fp, #-0x18]
    // 0x147caa0: StoreField: r3->field_f = r0
    //     0x147caa0: stur            w0, [x3, #0xf]
    // 0x147caa4: ldur            x0, [fp, #-8]
    // 0x147caa8: StoreField: r3->field_13 = r0
    //     0x147caa8: stur            w0, [x3, #0x13]
    // 0x147caac: ldur            x2, [fp, #-0x20]
    // 0x147cab0: r1 = Function '<anonymous closure>':.
    //     0x147cab0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44938] AnonymousClosure: (0x1359d88), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x147cab4: ldr             x1, [x1, #0x938]
    // 0x147cab8: r0 = AllocateClosure()
    //     0x147cab8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147cabc: mov             x1, x0
    // 0x147cac0: ldur            x0, [fp, #-0x28]
    // 0x147cac4: ArrayStore: r0[0] = r1  ; List_4
    //     0x147cac4: stur            w1, [x0, #0x17]
    // 0x147cac8: ldur            x2, [fp, #-0x20]
    // 0x147cacc: r1 = Function '<anonymous closure>':.
    //     0x147cacc: add             x1, PP, #0x44, lsl #12  ; [pp+0x44940] AnonymousClosure: (0x1359d30), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x147cad0: ldr             x1, [x1, #0x940]
    // 0x147cad4: r0 = AllocateClosure()
    //     0x147cad4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147cad8: mov             x1, x0
    // 0x147cadc: ldur            x0, [fp, #-0x28]
    // 0x147cae0: StoreField: r0->field_1b = r1
    //     0x147cae0: stur            w1, [x0, #0x1b]
    // 0x147cae4: LeaveFrame
    //     0x147cae4: mov             SP, fp
    //     0x147cae8: ldp             fp, lr, [SP], #0x10
    // 0x147caec: ret
    //     0x147caec: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x147cafc, size: 0x84
    // 0x147cafc: EnterFrame
    //     0x147cafc: stp             fp, lr, [SP, #-0x10]!
    //     0x147cb00: mov             fp, SP
    // 0x147cb04: AllocStack(0x38)
    //     0x147cb04: sub             SP, SP, #0x38
    // 0x147cb08: SetupParameters()
    //     0x147cb08: ldr             x0, [fp, #0x10]
    //     0x147cb0c: ldur            w2, [x0, #0x17]
    //     0x147cb10: add             x2, x2, HEAP, lsl #32
    // 0x147cb14: CheckStackOverflow
    //     0x147cb14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147cb18: cmp             SP, x16
    //     0x147cb1c: b.ls            #0x147cb78
    // 0x147cb20: LoadField: r0 = r2->field_13
    //     0x147cb20: ldur            w0, [x2, #0x13]
    // 0x147cb24: DecompressPointer r0
    //     0x147cb24: add             x0, x0, HEAP, lsl #32
    // 0x147cb28: stur            x0, [fp, #-8]
    // 0x147cb2c: r1 = Function '<anonymous closure>':.
    //     0x147cb2c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44948] AnonymousClosure: (0x147cb80), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147cb30: ldr             x1, [x1, #0x948]
    // 0x147cb34: r0 = AllocateClosure()
    //     0x147cb34: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147cb38: stp             x0, NULL, [SP, #0x20]
    // 0x147cb3c: ldur            x16, [fp, #-8]
    // 0x147cb40: r30 = true
    //     0x147cb40: add             lr, NULL, #0x20  ; true
    // 0x147cb44: stp             lr, x16, [SP, #0x10]
    // 0x147cb48: r16 = Instance_Color
    //     0x147cb48: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x147cb4c: ldr             x16, [x16, #0x90]
    // 0x147cb50: r30 = Instance_RoundedRectangleBorder
    //     0x147cb50: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x147cb54: ldr             lr, [lr, #0xc78]
    // 0x147cb58: stp             lr, x16, [SP]
    // 0x147cb5c: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x2, shape, 0x4, null]
    //     0x147cb5c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d308] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x2, "shape", 0x4, Null]
    //     0x147cb60: ldr             x4, [x4, #0x308]
    // 0x147cb64: r0 = showModalBottomSheet()
    //     0x147cb64: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x147cb68: r0 = Null
    //     0x147cb68: mov             x0, NULL
    // 0x147cb6c: LeaveFrame
    //     0x147cb6c: mov             SP, fp
    //     0x147cb70: ldp             fp, lr, [SP], #0x10
    // 0x147cb74: ret
    //     0x147cb74: ret             
    // 0x147cb78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147cb78: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147cb7c: b               #0x147cb20
  }
  [closure] Container <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x147cb80, size: 0x260
    // 0x147cb80: EnterFrame
    //     0x147cb80: stp             fp, lr, [SP, #-0x10]!
    //     0x147cb84: mov             fp, SP
    // 0x147cb88: AllocStack(0x58)
    //     0x147cb88: sub             SP, SP, #0x58
    // 0x147cb8c: SetupParameters()
    //     0x147cb8c: ldr             x0, [fp, #0x18]
    //     0x147cb90: ldur            w2, [x0, #0x17]
    //     0x147cb94: add             x2, x2, HEAP, lsl #32
    //     0x147cb98: stur            x2, [fp, #-8]
    // 0x147cb9c: CheckStackOverflow
    //     0x147cb9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147cba0: cmp             SP, x16
    //     0x147cba4: b.ls            #0x147cdd8
    // 0x147cba8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x147cba8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x147cbac: ldr             x0, [x0, #0x1c80]
    //     0x147cbb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x147cbb4: cmp             w0, w16
    //     0x147cbb8: b.ne            #0x147cbc4
    //     0x147cbbc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x147cbc0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x147cbc4: r0 = GetNavigation.size()
    //     0x147cbc4: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0x147cbc8: LoadField: d0 = r0->field_f
    //     0x147cbc8: ldur            d0, [x0, #0xf]
    // 0x147cbcc: d1 = 0.800000
    //     0x147cbcc: add             x17, PP, #0x32, lsl #12  ; [pp+0x32b28] IMM: double(0.8) from 0x3fe999999999999a
    //     0x147cbd0: ldr             d1, [x17, #0xb28]
    // 0x147cbd4: fmul            d2, d0, d1
    // 0x147cbd8: stur            d2, [fp, #-0x48]
    // 0x147cbdc: r0 = BoxConstraints()
    //     0x147cbdc: bl              #0x6e9c1c  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x147cbe0: stur            x0, [fp, #-0x10]
    // 0x147cbe4: StoreField: r0->field_7 = rZR
    //     0x147cbe4: stur            xzr, [x0, #7]
    // 0x147cbe8: d0 = inf
    //     0x147cbe8: ldr             d0, [PP, #0x2840]  ; [pp+0x2840] IMM: double(inf) from 0x7ff0000000000000
    // 0x147cbec: StoreField: r0->field_f = d0
    //     0x147cbec: stur            d0, [x0, #0xf]
    // 0x147cbf0: ArrayStore: r0[0] = rZR  ; List_8
    //     0x147cbf0: stur            xzr, [x0, #0x17]
    // 0x147cbf4: ldur            d0, [fp, #-0x48]
    // 0x147cbf8: StoreField: r0->field_1f = d0
    //     0x147cbf8: stur            d0, [x0, #0x1f]
    // 0x147cbfc: ldur            x2, [fp, #-8]
    // 0x147cc00: LoadField: r1 = r2->field_f
    //     0x147cc00: ldur            w1, [x2, #0xf]
    // 0x147cc04: DecompressPointer r1
    //     0x147cc04: add             x1, x1, HEAP, lsl #32
    // 0x147cc08: r0 = controller()
    //     0x147cc08: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147cc0c: LoadField: r1 = r0->field_63
    //     0x147cc0c: ldur            w1, [x0, #0x63]
    // 0x147cc10: DecompressPointer r1
    //     0x147cc10: add             x1, x1, HEAP, lsl #32
    // 0x147cc14: r0 = value()
    //     0x147cc14: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147cc18: LoadField: r2 = r0->field_b
    //     0x147cc18: ldur            w2, [x0, #0xb]
    // 0x147cc1c: DecompressPointer r2
    //     0x147cc1c: add             x2, x2, HEAP, lsl #32
    // 0x147cc20: ldur            x0, [fp, #-8]
    // 0x147cc24: stur            x2, [fp, #-0x18]
    // 0x147cc28: LoadField: r1 = r0->field_f
    //     0x147cc28: ldur            w1, [x0, #0xf]
    // 0x147cc2c: DecompressPointer r1
    //     0x147cc2c: add             x1, x1, HEAP, lsl #32
    // 0x147cc30: r0 = controller()
    //     0x147cc30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147cc34: LoadField: r1 = r0->field_5b
    //     0x147cc34: ldur            w1, [x0, #0x5b]
    // 0x147cc38: DecompressPointer r1
    //     0x147cc38: add             x1, x1, HEAP, lsl #32
    // 0x147cc3c: r0 = value()
    //     0x147cc3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147cc40: LoadField: r1 = r0->field_b
    //     0x147cc40: ldur            w1, [x0, #0xb]
    // 0x147cc44: DecompressPointer r1
    //     0x147cc44: add             x1, x1, HEAP, lsl #32
    // 0x147cc48: cmp             w1, NULL
    // 0x147cc4c: b.ne            #0x147cc58
    // 0x147cc50: r0 = Null
    //     0x147cc50: mov             x0, NULL
    // 0x147cc54: b               #0x147cc7c
    // 0x147cc58: LoadField: r0 = r1->field_1b
    //     0x147cc58: ldur            w0, [x1, #0x1b]
    // 0x147cc5c: DecompressPointer r0
    //     0x147cc5c: add             x0, x0, HEAP, lsl #32
    // 0x147cc60: cmp             w0, NULL
    // 0x147cc64: b.ne            #0x147cc70
    // 0x147cc68: r0 = Null
    //     0x147cc68: mov             x0, NULL
    // 0x147cc6c: b               #0x147cc7c
    // 0x147cc70: LoadField: r1 = r0->field_7
    //     0x147cc70: ldur            w1, [x0, #7]
    // 0x147cc74: DecompressPointer r1
    //     0x147cc74: add             x1, x1, HEAP, lsl #32
    // 0x147cc78: mov             x0, x1
    // 0x147cc7c: ldur            x2, [fp, #-8]
    // 0x147cc80: stur            x0, [fp, #-0x20]
    // 0x147cc84: LoadField: r1 = r2->field_f
    //     0x147cc84: ldur            w1, [x2, #0xf]
    // 0x147cc88: DecompressPointer r1
    //     0x147cc88: add             x1, x1, HEAP, lsl #32
    // 0x147cc8c: r0 = controller()
    //     0x147cc8c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147cc90: ldur            x2, [fp, #-8]
    // 0x147cc94: stur            x0, [fp, #-0x28]
    // 0x147cc98: LoadField: r1 = r2->field_f
    //     0x147cc98: ldur            w1, [x2, #0xf]
    // 0x147cc9c: DecompressPointer r1
    //     0x147cc9c: add             x1, x1, HEAP, lsl #32
    // 0x147cca0: r0 = controller()
    //     0x147cca0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147cca4: LoadField: r1 = r0->field_77
    //     0x147cca4: ldur            w1, [x0, #0x77]
    // 0x147cca8: DecompressPointer r1
    //     0x147cca8: add             x1, x1, HEAP, lsl #32
    // 0x147ccac: r0 = value()
    //     0x147ccac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147ccb0: ldur            x2, [fp, #-8]
    // 0x147ccb4: stur            x0, [fp, #-0x30]
    // 0x147ccb8: LoadField: r1 = r2->field_f
    //     0x147ccb8: ldur            w1, [x2, #0xf]
    // 0x147ccbc: DecompressPointer r1
    //     0x147ccbc: add             x1, x1, HEAP, lsl #32
    // 0x147ccc0: r0 = controller()
    //     0x147ccc0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147ccc4: LoadField: r1 = r0->field_5b
    //     0x147ccc4: ldur            w1, [x0, #0x5b]
    // 0x147ccc8: DecompressPointer r1
    //     0x147ccc8: add             x1, x1, HEAP, lsl #32
    // 0x147cccc: r0 = value()
    //     0x147cccc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147ccd0: LoadField: r1 = r0->field_b
    //     0x147ccd0: ldur            w1, [x0, #0xb]
    // 0x147ccd4: DecompressPointer r1
    //     0x147ccd4: add             x1, x1, HEAP, lsl #32
    // 0x147ccd8: cmp             w1, NULL
    // 0x147ccdc: b.ne            #0x147cce8
    // 0x147cce0: r3 = Null
    //     0x147cce0: mov             x3, NULL
    // 0x147cce4: b               #0x147ccf4
    // 0x147cce8: LoadField: r0 = r1->field_1b
    //     0x147cce8: ldur            w0, [x1, #0x1b]
    // 0x147ccec: DecompressPointer r0
    //     0x147ccec: add             x0, x0, HEAP, lsl #32
    // 0x147ccf0: mov             x3, x0
    // 0x147ccf4: ldur            x2, [fp, #-0x18]
    // 0x147ccf8: ldur            x1, [fp, #-0x20]
    // 0x147ccfc: ldur            x0, [fp, #-0x30]
    // 0x147cd00: stur            x3, [fp, #-0x38]
    // 0x147cd04: r0 = OffersListWidget()
    //     0x147cd04: bl              #0xad498c  ; AllocateOffersListWidgetStub -> OffersListWidget (size=0x34)
    // 0x147cd08: mov             x3, x0
    // 0x147cd0c: ldur            x0, [fp, #-0x18]
    // 0x147cd10: stur            x3, [fp, #-0x40]
    // 0x147cd14: StoreField: r3->field_b = r0
    //     0x147cd14: stur            w0, [x3, #0xb]
    // 0x147cd18: ldur            x0, [fp, #-0x20]
    // 0x147cd1c: StoreField: r3->field_f = r0
    //     0x147cd1c: stur            w0, [x3, #0xf]
    // 0x147cd20: ldur            x2, [fp, #-8]
    // 0x147cd24: r1 = Function '<anonymous closure>':.
    //     0x147cd24: add             x1, PP, #0x44, lsl #12  ; [pp+0x44950] AnonymousClosure: (0x1358f48), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x147cd28: ldr             x1, [x1, #0x950]
    // 0x147cd2c: r0 = AllocateClosure()
    //     0x147cd2c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147cd30: mov             x1, x0
    // 0x147cd34: ldur            x0, [fp, #-0x40]
    // 0x147cd38: StoreField: r0->field_13 = r1
    //     0x147cd38: stur            w1, [x0, #0x13]
    // 0x147cd3c: ldur            x2, [fp, #-8]
    // 0x147cd40: r1 = Function '<anonymous closure>':.
    //     0x147cd40: add             x1, PP, #0x44, lsl #12  ; [pp+0x44958] AnonymousClosure: (0x1358c04), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x147cd44: ldr             x1, [x1, #0x958]
    // 0x147cd48: r0 = AllocateClosure()
    //     0x147cd48: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147cd4c: mov             x1, x0
    // 0x147cd50: ldur            x0, [fp, #-0x40]
    // 0x147cd54: ArrayStore: r0[0] = r1  ; List_4
    //     0x147cd54: stur            w1, [x0, #0x17]
    // 0x147cd58: r1 = true
    //     0x147cd58: add             x1, NULL, #0x20  ; true
    // 0x147cd5c: StoreField: r0->field_1b = r1
    //     0x147cd5c: stur            w1, [x0, #0x1b]
    // 0x147cd60: r1 = "bag"
    //     0x147cd60: add             x1, PP, #0xb, lsl #12  ; [pp+0xb460] "bag"
    //     0x147cd64: ldr             x1, [x1, #0x460]
    // 0x147cd68: StoreField: r0->field_23 = r1
    //     0x147cd68: stur            w1, [x0, #0x23]
    // 0x147cd6c: ldur            x2, [fp, #-0x28]
    // 0x147cd70: r1 = Function 'postEvents':.
    //     0x147cd70: add             x1, PP, #0x32, lsl #12  ; [pp+0x321b0] AnonymousClosure: (0x89c5cc), in [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents (0x8608dc)
    //     0x147cd74: ldr             x1, [x1, #0x1b0]
    // 0x147cd78: r0 = AllocateClosure()
    //     0x147cd78: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147cd7c: mov             x1, x0
    // 0x147cd80: ldur            x0, [fp, #-0x40]
    // 0x147cd84: StoreField: r0->field_1f = r1
    //     0x147cd84: stur            w1, [x0, #0x1f]
    // 0x147cd88: r1 = "false"
    //     0x147cd88: add             x1, PP, #8, lsl #12  ; [pp+0x8ed8] "false"
    //     0x147cd8c: ldr             x1, [x1, #0xed8]
    // 0x147cd90: StoreField: r0->field_27 = r1
    //     0x147cd90: stur            w1, [x0, #0x27]
    // 0x147cd94: ldur            x1, [fp, #-0x30]
    // 0x147cd98: StoreField: r0->field_2b = r1
    //     0x147cd98: stur            w1, [x0, #0x2b]
    // 0x147cd9c: ldur            x1, [fp, #-0x38]
    // 0x147cda0: StoreField: r0->field_2f = r1
    //     0x147cda0: stur            w1, [x0, #0x2f]
    // 0x147cda4: r0 = Container()
    //     0x147cda4: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x147cda8: stur            x0, [fp, #-8]
    // 0x147cdac: ldur            x16, [fp, #-0x10]
    // 0x147cdb0: ldur            lr, [fp, #-0x40]
    // 0x147cdb4: stp             lr, x16, [SP]
    // 0x147cdb8: mov             x1, x0
    // 0x147cdbc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, constraints, 0x1, null]
    //     0x147cdbc: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b30] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "constraints", 0x1, Null]
    //     0x147cdc0: ldr             x4, [x4, #0xb30]
    // 0x147cdc4: r0 = Container()
    //     0x147cdc4: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x147cdc8: ldur            x0, [fp, #-8]
    // 0x147cdcc: LeaveFrame
    //     0x147cdcc: mov             SP, fp
    //     0x147cdd0: ldp             fp, lr, [SP], #0x10
    // 0x147cdd4: ret
    //     0x147cdd4: ret             
    // 0x147cdd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147cdd8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147cddc: b               #0x147cba8
  }
  [closure] Null <anonymous closure>(dynamic, String, String, String, String) {
    // ** addr: 0x147cde0, size: 0xc0
    // 0x147cde0: EnterFrame
    //     0x147cde0: stp             fp, lr, [SP, #-0x10]!
    //     0x147cde4: mov             fp, SP
    // 0x147cde8: AllocStack(0x40)
    //     0x147cde8: sub             SP, SP, #0x40
    // 0x147cdec: SetupParameters()
    //     0x147cdec: ldr             x0, [fp, #0x30]
    //     0x147cdf0: ldur            w1, [x0, #0x17]
    //     0x147cdf4: add             x1, x1, HEAP, lsl #32
    //     0x147cdf8: stur            x1, [fp, #-8]
    // 0x147cdfc: CheckStackOverflow
    //     0x147cdfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147ce00: cmp             SP, x16
    //     0x147ce04: b.ls            #0x147ce98
    // 0x147ce08: r1 = 4
    //     0x147ce08: movz            x1, #0x4
    // 0x147ce0c: r0 = AllocateContext()
    //     0x147ce0c: bl              #0x16f6108  ; AllocateContextStub
    // 0x147ce10: mov             x1, x0
    // 0x147ce14: ldur            x0, [fp, #-8]
    // 0x147ce18: StoreField: r1->field_b = r0
    //     0x147ce18: stur            w0, [x1, #0xb]
    // 0x147ce1c: ldr             x2, [fp, #0x28]
    // 0x147ce20: StoreField: r1->field_f = r2
    //     0x147ce20: stur            w2, [x1, #0xf]
    // 0x147ce24: ldr             x2, [fp, #0x20]
    // 0x147ce28: StoreField: r1->field_13 = r2
    //     0x147ce28: stur            w2, [x1, #0x13]
    // 0x147ce2c: ldr             x2, [fp, #0x18]
    // 0x147ce30: ArrayStore: r1[0] = r2  ; List_4
    //     0x147ce30: stur            w2, [x1, #0x17]
    // 0x147ce34: ldr             x2, [fp, #0x10]
    // 0x147ce38: StoreField: r1->field_1b = r2
    //     0x147ce38: stur            w2, [x1, #0x1b]
    // 0x147ce3c: LoadField: r3 = r0->field_13
    //     0x147ce3c: ldur            w3, [x0, #0x13]
    // 0x147ce40: DecompressPointer r3
    //     0x147ce40: add             x3, x3, HEAP, lsl #32
    // 0x147ce44: mov             x2, x1
    // 0x147ce48: stur            x3, [fp, #-0x10]
    // 0x147ce4c: r1 = Function '<anonymous closure>':.
    //     0x147ce4c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44960] AnonymousClosure: (0x147cea0), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::body (0x147c4d4)
    //     0x147ce50: ldr             x1, [x1, #0x960]
    // 0x147ce54: r0 = AllocateClosure()
    //     0x147ce54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147ce58: stp             x0, NULL, [SP, #0x20]
    // 0x147ce5c: ldur            x16, [fp, #-0x10]
    // 0x147ce60: r30 = Instance_RoundedRectangleBorder
    //     0x147ce60: add             lr, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x147ce64: ldr             lr, [lr, #0xc78]
    // 0x147ce68: stp             lr, x16, [SP, #0x10]
    // 0x147ce6c: r16 = Instance_Color
    //     0x147ce6c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e090] Obj!Color@d6ab31
    //     0x147ce70: ldr             x16, [x16, #0x90]
    // 0x147ce74: r30 = true
    //     0x147ce74: add             lr, NULL, #0x20  ; true
    // 0x147ce78: stp             lr, x16, [SP]
    // 0x147ce7c: r4 = const [0x1, 0x5, 0x5, 0x2, backgroundColor, 0x3, isScrollControlled, 0x4, shape, 0x2, null]
    //     0x147ce7c: add             x4, PP, #0x44, lsl #12  ; [pp+0x44930] List(11) [0x1, 0x5, 0x5, 0x2, "backgroundColor", 0x3, "isScrollControlled", 0x4, "shape", 0x2, Null]
    //     0x147ce80: ldr             x4, [x4, #0x930]
    // 0x147ce84: r0 = showModalBottomSheet()
    //     0x147ce84: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x147ce88: r0 = Null
    //     0x147ce88: mov             x0, NULL
    // 0x147ce8c: LeaveFrame
    //     0x147ce8c: mov             SP, fp
    //     0x147ce90: ldp             fp, lr, [SP], #0x10
    // 0x147ce94: ret
    //     0x147ce94: ret             
    // 0x147ce98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147ce98: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147ce9c: b               #0x147ce08
  }
  [closure] CancelOrderConfirmBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x147cea0, size: 0x264
    // 0x147cea0: EnterFrame
    //     0x147cea0: stp             fp, lr, [SP, #-0x10]!
    //     0x147cea4: mov             fp, SP
    // 0x147cea8: AllocStack(0x38)
    //     0x147cea8: sub             SP, SP, #0x38
    // 0x147ceac: SetupParameters()
    //     0x147ceac: ldr             x0, [fp, #0x18]
    //     0x147ceb0: ldur            w2, [x0, #0x17]
    //     0x147ceb4: add             x2, x2, HEAP, lsl #32
    //     0x147ceb8: stur            x2, [fp, #-8]
    // 0x147cebc: CheckStackOverflow
    //     0x147cebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147cec0: cmp             SP, x16
    //     0x147cec4: b.ls            #0x147d0fc
    // 0x147cec8: ldr             x1, [fp, #0x10]
    // 0x147cecc: r0 = of()
    //     0x147cecc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147ced0: LoadField: r1 = r0->field_87
    //     0x147ced0: ldur            w1, [x0, #0x87]
    // 0x147ced4: DecompressPointer r1
    //     0x147ced4: add             x1, x1, HEAP, lsl #32
    // 0x147ced8: LoadField: r0 = r1->field_2b
    //     0x147ced8: ldur            w0, [x1, #0x2b]
    // 0x147cedc: DecompressPointer r0
    //     0x147cedc: add             x0, x0, HEAP, lsl #32
    // 0x147cee0: stur            x0, [fp, #-0x10]
    // 0x147cee4: r1 = Instance_Color
    //     0x147cee4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x147cee8: d0 = 0.400000
    //     0x147cee8: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x147ceec: r0 = withOpacity()
    //     0x147ceec: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x147cef0: r16 = 12.000000
    //     0x147cef0: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x147cef4: ldr             x16, [x16, #0x9e8]
    // 0x147cef8: stp             x0, x16, [SP]
    // 0x147cefc: ldur            x1, [fp, #-0x10]
    // 0x147cf00: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x147cf00: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x147cf04: ldr             x4, [x4, #0xaa0]
    // 0x147cf08: r0 = copyWith()
    //     0x147cf08: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x147cf0c: stur            x0, [fp, #-0x10]
    // 0x147cf10: r0 = Radius()
    //     0x147cf10: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x147cf14: d0 = 12.000000
    //     0x147cf14: fmov            d0, #12.00000000
    // 0x147cf18: stur            x0, [fp, #-0x18]
    // 0x147cf1c: StoreField: r0->field_7 = d0
    //     0x147cf1c: stur            d0, [x0, #7]
    // 0x147cf20: StoreField: r0->field_f = d0
    //     0x147cf20: stur            d0, [x0, #0xf]
    // 0x147cf24: r0 = BorderRadius()
    //     0x147cf24: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x147cf28: mov             x1, x0
    // 0x147cf2c: ldur            x0, [fp, #-0x18]
    // 0x147cf30: stur            x1, [fp, #-0x20]
    // 0x147cf34: StoreField: r1->field_7 = r0
    //     0x147cf34: stur            w0, [x1, #7]
    // 0x147cf38: StoreField: r1->field_b = r0
    //     0x147cf38: stur            w0, [x1, #0xb]
    // 0x147cf3c: StoreField: r1->field_f = r0
    //     0x147cf3c: stur            w0, [x1, #0xf]
    // 0x147cf40: StoreField: r1->field_13 = r0
    //     0x147cf40: stur            w0, [x1, #0x13]
    // 0x147cf44: r0 = OutlineInputBorder()
    //     0x147cf44: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x147cf48: mov             x1, x0
    // 0x147cf4c: ldur            x0, [fp, #-0x20]
    // 0x147cf50: stur            x1, [fp, #-0x18]
    // 0x147cf54: StoreField: r1->field_13 = r0
    //     0x147cf54: stur            w0, [x1, #0x13]
    // 0x147cf58: d0 = 4.000000
    //     0x147cf58: fmov            d0, #4.00000000
    // 0x147cf5c: StoreField: r1->field_b = d0
    //     0x147cf5c: stur            d0, [x1, #0xb]
    // 0x147cf60: r0 = Instance_BorderSide
    //     0x147cf60: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x147cf64: ldr             x0, [x0, #0x118]
    // 0x147cf68: StoreField: r1->field_7 = r0
    //     0x147cf68: stur            w0, [x1, #7]
    // 0x147cf6c: r0 = Radius()
    //     0x147cf6c: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x147cf70: d0 = 12.000000
    //     0x147cf70: fmov            d0, #12.00000000
    // 0x147cf74: stur            x0, [fp, #-0x20]
    // 0x147cf78: StoreField: r0->field_7 = d0
    //     0x147cf78: stur            d0, [x0, #7]
    // 0x147cf7c: StoreField: r0->field_f = d0
    //     0x147cf7c: stur            d0, [x0, #0xf]
    // 0x147cf80: r0 = BorderRadius()
    //     0x147cf80: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x147cf84: mov             x1, x0
    // 0x147cf88: ldur            x0, [fp, #-0x20]
    // 0x147cf8c: stur            x1, [fp, #-0x28]
    // 0x147cf90: StoreField: r1->field_7 = r0
    //     0x147cf90: stur            w0, [x1, #7]
    // 0x147cf94: StoreField: r1->field_b = r0
    //     0x147cf94: stur            w0, [x1, #0xb]
    // 0x147cf98: StoreField: r1->field_f = r0
    //     0x147cf98: stur            w0, [x1, #0xf]
    // 0x147cf9c: StoreField: r1->field_13 = r0
    //     0x147cf9c: stur            w0, [x1, #0x13]
    // 0x147cfa0: r0 = OutlineInputBorder()
    //     0x147cfa0: bl              #0x8b1994  ; AllocateOutlineInputBorderStub -> OutlineInputBorder (size=0x18)
    // 0x147cfa4: mov             x1, x0
    // 0x147cfa8: ldur            x0, [fp, #-0x28]
    // 0x147cfac: stur            x1, [fp, #-0x20]
    // 0x147cfb0: StoreField: r1->field_13 = r0
    //     0x147cfb0: stur            w0, [x1, #0x13]
    // 0x147cfb4: d0 = 4.000000
    //     0x147cfb4: fmov            d0, #4.00000000
    // 0x147cfb8: StoreField: r1->field_b = d0
    //     0x147cfb8: stur            d0, [x1, #0xb]
    // 0x147cfbc: r0 = Instance_BorderSide
    //     0x147cfbc: add             x0, PP, #0x36, lsl #12  ; [pp+0x36118] Obj!BorderSide@d62ef1
    //     0x147cfc0: ldr             x0, [x0, #0x118]
    // 0x147cfc4: StoreField: r1->field_7 = r0
    //     0x147cfc4: stur            w0, [x1, #7]
    // 0x147cfc8: r0 = InputDecoration()
    //     0x147cfc8: bl              #0x81349c  ; AllocateInputDecorationStub -> InputDecoration (size=0xec)
    // 0x147cfcc: mov             x1, x0
    // 0x147cfd0: r0 = "Mention the reason"
    //     0x147cfd0: add             x0, PP, #0x36, lsl #12  ; [pp+0x36120] "Mention the reason"
    //     0x147cfd4: ldr             x0, [x0, #0x120]
    // 0x147cfd8: stur            x1, [fp, #-0x28]
    // 0x147cfdc: StoreField: r1->field_13 = r0
    //     0x147cfdc: stur            w0, [x1, #0x13]
    // 0x147cfe0: ldur            x0, [fp, #-0x10]
    // 0x147cfe4: ArrayStore: r1[0] = r0  ; List_4
    //     0x147cfe4: stur            w0, [x1, #0x17]
    // 0x147cfe8: r0 = true
    //     0x147cfe8: add             x0, NULL, #0x20  ; true
    // 0x147cfec: StoreField: r1->field_47 = r0
    //     0x147cfec: stur            w0, [x1, #0x47]
    // 0x147cff0: StoreField: r1->field_4b = r0
    //     0x147cff0: stur            w0, [x1, #0x4b]
    // 0x147cff4: ldur            x2, [fp, #-0x20]
    // 0x147cff8: StoreField: r1->field_c3 = r2
    //     0x147cff8: stur            w2, [x1, #0xc3]
    // 0x147cffc: ldur            x2, [fp, #-0x18]
    // 0x147d000: StoreField: r1->field_cf = r2
    //     0x147d000: stur            w2, [x1, #0xcf]
    // 0x147d004: StoreField: r1->field_d7 = r0
    //     0x147d004: stur            w0, [x1, #0xd7]
    // 0x147d008: r0 = Radius()
    //     0x147d008: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x147d00c: d0 = 30.000000
    //     0x147d00c: fmov            d0, #30.00000000
    // 0x147d010: stur            x0, [fp, #-0x10]
    // 0x147d014: StoreField: r0->field_7 = d0
    //     0x147d014: stur            d0, [x0, #7]
    // 0x147d018: StoreField: r0->field_f = d0
    //     0x147d018: stur            d0, [x0, #0xf]
    // 0x147d01c: r0 = BorderRadius()
    //     0x147d01c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x147d020: mov             x2, x0
    // 0x147d024: ldur            x0, [fp, #-0x10]
    // 0x147d028: stur            x2, [fp, #-0x18]
    // 0x147d02c: StoreField: r2->field_7 = r0
    //     0x147d02c: stur            w0, [x2, #7]
    // 0x147d030: StoreField: r2->field_b = r0
    //     0x147d030: stur            w0, [x2, #0xb]
    // 0x147d034: StoreField: r2->field_f = r0
    //     0x147d034: stur            w0, [x2, #0xf]
    // 0x147d038: StoreField: r2->field_13 = r0
    //     0x147d038: stur            w0, [x2, #0x13]
    // 0x147d03c: ldr             x1, [fp, #0x10]
    // 0x147d040: r0 = of()
    //     0x147d040: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x147d044: LoadField: r1 = r0->field_5b
    //     0x147d044: ldur            w1, [x0, #0x5b]
    // 0x147d048: DecompressPointer r1
    //     0x147d048: add             x1, x1, HEAP, lsl #32
    // 0x147d04c: stur            x1, [fp, #-0x10]
    // 0x147d050: r0 = BorderSide()
    //     0x147d050: bl              #0x837648  ; AllocateBorderSideStub -> BorderSide (size=0x20)
    // 0x147d054: mov             x1, x0
    // 0x147d058: ldur            x0, [fp, #-0x10]
    // 0x147d05c: stur            x1, [fp, #-0x20]
    // 0x147d060: StoreField: r1->field_7 = r0
    //     0x147d060: stur            w0, [x1, #7]
    // 0x147d064: d0 = 1.000000
    //     0x147d064: fmov            d0, #1.00000000
    // 0x147d068: StoreField: r1->field_b = d0
    //     0x147d068: stur            d0, [x1, #0xb]
    // 0x147d06c: r0 = Instance_BorderStyle
    //     0x147d06c: add             x0, PP, #0x12, lsl #12  ; [pp+0x12f68] Obj!BorderStyle@d73961
    //     0x147d070: ldr             x0, [x0, #0xf68]
    // 0x147d074: StoreField: r1->field_13 = r0
    //     0x147d074: stur            w0, [x1, #0x13]
    // 0x147d078: d0 = -1.000000
    //     0x147d078: fmov            d0, #-1.00000000
    // 0x147d07c: ArrayStore: r1[0] = d0  ; List_8
    //     0x147d07c: stur            d0, [x1, #0x17]
    // 0x147d080: r0 = CancelOrderConfirmBottomSheet()
    //     0x147d080: bl              #0x8b1964  ; AllocateCancelOrderConfirmBottomSheetStub -> CancelOrderConfirmBottomSheet (size=0x34)
    // 0x147d084: mov             x3, x0
    // 0x147d088: r0 = "Are you sure want to remove this item\?"
    //     0x147d088: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7c0] "Are you sure want to remove this item\?"
    //     0x147d08c: ldr             x0, [x0, #0x7c0]
    // 0x147d090: stur            x3, [fp, #-0x10]
    // 0x147d094: StoreField: r3->field_f = r0
    //     0x147d094: stur            w0, [x3, #0xf]
    // 0x147d098: r0 = "Remove item"
    //     0x147d098: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7c8] "Remove item"
    //     0x147d09c: ldr             x0, [x0, #0x7c8]
    // 0x147d0a0: StoreField: r3->field_13 = r0
    //     0x147d0a0: stur            w0, [x3, #0x13]
    // 0x147d0a4: r0 = "Remove"
    //     0x147d0a4: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e7d0] "Remove"
    //     0x147d0a8: ldr             x0, [x0, #0x7d0]
    // 0x147d0ac: ArrayStore: r3[0] = r0  ; List_4
    //     0x147d0ac: stur            w0, [x3, #0x17]
    // 0x147d0b0: ldur            x2, [fp, #-8]
    // 0x147d0b4: r1 = Function '<anonymous closure>':.
    //     0x147d0b4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44968] AnonymousClosure: (0x135a814), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::body (0x14fdaf8)
    //     0x147d0b8: ldr             x1, [x1, #0x968]
    // 0x147d0bc: r0 = AllocateClosure()
    //     0x147d0bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147d0c0: mov             x1, x0
    // 0x147d0c4: ldur            x0, [fp, #-0x10]
    // 0x147d0c8: StoreField: r0->field_1b = r1
    //     0x147d0c8: stur            w1, [x0, #0x1b]
    // 0x147d0cc: ldur            x1, [fp, #-0x18]
    // 0x147d0d0: StoreField: r0->field_1f = r1
    //     0x147d0d0: stur            w1, [x0, #0x1f]
    // 0x147d0d4: ldur            x1, [fp, #-0x28]
    // 0x147d0d8: StoreField: r0->field_23 = r1
    //     0x147d0d8: stur            w1, [x0, #0x23]
    // 0x147d0dc: ldur            x1, [fp, #-0x20]
    // 0x147d0e0: StoreField: r0->field_27 = r1
    //     0x147d0e0: stur            w1, [x0, #0x27]
    // 0x147d0e4: r1 = "Go, Back"
    //     0x147d0e4: add             x1, PP, #0x3f, lsl #12  ; [pp+0x3fe08] "Go, Back"
    //     0x147d0e8: ldr             x1, [x1, #0xe08]
    // 0x147d0ec: StoreField: r0->field_2b = r1
    //     0x147d0ec: stur            w1, [x0, #0x2b]
    // 0x147d0f0: LeaveFrame
    //     0x147d0f0: mov             SP, fp
    //     0x147d0f4: ldp             fp, lr, [SP], #0x10
    // 0x147d0f8: ret
    //     0x147d0f8: ret             
    // 0x147d0fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147d0fc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147d100: b               #0x147cec8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x147d104, size: 0x3e8
    // 0x147d104: EnterFrame
    //     0x147d104: stp             fp, lr, [SP, #-0x10]!
    //     0x147d108: mov             fp, SP
    // 0x147d10c: AllocStack(0x48)
    //     0x147d10c: sub             SP, SP, #0x48
    // 0x147d110: SetupParameters()
    //     0x147d110: ldr             x0, [fp, #0x10]
    //     0x147d114: ldur            w2, [x0, #0x17]
    //     0x147d118: add             x2, x2, HEAP, lsl #32
    //     0x147d11c: stur            x2, [fp, #-8]
    // 0x147d120: CheckStackOverflow
    //     0x147d120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147d124: cmp             SP, x16
    //     0x147d128: b.ls            #0x147d4cc
    // 0x147d12c: LoadField: r1 = r2->field_f
    //     0x147d12c: ldur            w1, [x2, #0xf]
    // 0x147d130: DecompressPointer r1
    //     0x147d130: add             x1, x1, HEAP, lsl #32
    // 0x147d134: r0 = controller()
    //     0x147d134: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d138: mov             x2, x0
    // 0x147d13c: ldur            x0, [fp, #-8]
    // 0x147d140: stur            x2, [fp, #-0x10]
    // 0x147d144: LoadField: r1 = r0->field_f
    //     0x147d144: ldur            w1, [x0, #0xf]
    // 0x147d148: DecompressPointer r1
    //     0x147d148: add             x1, x1, HEAP, lsl #32
    // 0x147d14c: r0 = controller()
    //     0x147d14c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d150: LoadField: r1 = r0->field_5b
    //     0x147d150: ldur            w1, [x0, #0x5b]
    // 0x147d154: DecompressPointer r1
    //     0x147d154: add             x1, x1, HEAP, lsl #32
    // 0x147d158: r0 = value()
    //     0x147d158: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d15c: LoadField: r1 = r0->field_b
    //     0x147d15c: ldur            w1, [x0, #0xb]
    // 0x147d160: DecompressPointer r1
    //     0x147d160: add             x1, x1, HEAP, lsl #32
    // 0x147d164: cmp             w1, NULL
    // 0x147d168: b.ne            #0x147d174
    // 0x147d16c: r0 = Null
    //     0x147d16c: mov             x0, NULL
    // 0x147d170: b               #0x147d198
    // 0x147d174: LoadField: r0 = r1->field_2f
    //     0x147d174: ldur            w0, [x1, #0x2f]
    // 0x147d178: DecompressPointer r0
    //     0x147d178: add             x0, x0, HEAP, lsl #32
    // 0x147d17c: cmp             w0, NULL
    // 0x147d180: b.ne            #0x147d18c
    // 0x147d184: r0 = Null
    //     0x147d184: mov             x0, NULL
    // 0x147d188: b               #0x147d198
    // 0x147d18c: LoadField: r1 = r0->field_23
    //     0x147d18c: ldur            w1, [x0, #0x23]
    // 0x147d190: DecompressPointer r1
    //     0x147d190: add             x1, x1, HEAP, lsl #32
    // 0x147d194: mov             x0, x1
    // 0x147d198: cmp             w0, NULL
    // 0x147d19c: b.ne            #0x147d1a4
    // 0x147d1a0: r0 = ""
    //     0x147d1a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x147d1a4: ldur            x2, [fp, #-8]
    // 0x147d1a8: ldur            x1, [fp, #-0x10]
    // 0x147d1ac: StoreField: r1->field_67 = r0
    //     0x147d1ac: stur            w0, [x1, #0x67]
    //     0x147d1b0: ldurb           w16, [x1, #-1]
    //     0x147d1b4: ldurb           w17, [x0, #-1]
    //     0x147d1b8: and             x16, x17, x16, lsr #2
    //     0x147d1bc: tst             x16, HEAP, lsr #32
    //     0x147d1c0: b.eq            #0x147d1c8
    //     0x147d1c4: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x147d1c8: LoadField: r1 = r2->field_f
    //     0x147d1c8: ldur            w1, [x2, #0xf]
    // 0x147d1cc: DecompressPointer r1
    //     0x147d1cc: add             x1, x1, HEAP, lsl #32
    // 0x147d1d0: r0 = controller()
    //     0x147d1d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d1d4: mov             x1, x0
    // 0x147d1d8: r0 = true
    //     0x147d1d8: add             x0, NULL, #0x20  ; true
    // 0x147d1dc: StoreField: r1->field_93 = r0
    //     0x147d1dc: stur            w0, [x1, #0x93]
    // 0x147d1e0: ldur            x0, [fp, #-8]
    // 0x147d1e4: LoadField: r1 = r0->field_f
    //     0x147d1e4: ldur            w1, [x0, #0xf]
    // 0x147d1e8: DecompressPointer r1
    //     0x147d1e8: add             x1, x1, HEAP, lsl #32
    // 0x147d1ec: r0 = controller()
    //     0x147d1ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d1f0: mov             x1, x0
    // 0x147d1f4: r0 = false
    //     0x147d1f4: add             x0, NULL, #0x30  ; false
    // 0x147d1f8: StoreField: r1->field_8f = r0
    //     0x147d1f8: stur            w0, [x1, #0x8f]
    // 0x147d1fc: ldur            x0, [fp, #-8]
    // 0x147d200: LoadField: r1 = r0->field_f
    //     0x147d200: ldur            w1, [x0, #0xf]
    // 0x147d204: DecompressPointer r1
    //     0x147d204: add             x1, x1, HEAP, lsl #32
    // 0x147d208: r0 = controller()
    //     0x147d208: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d20c: mov             x2, x0
    // 0x147d210: ldur            x0, [fp, #-8]
    // 0x147d214: stur            x2, [fp, #-0x10]
    // 0x147d218: LoadField: r1 = r0->field_f
    //     0x147d218: ldur            w1, [x0, #0xf]
    // 0x147d21c: DecompressPointer r1
    //     0x147d21c: add             x1, x1, HEAP, lsl #32
    // 0x147d220: r0 = controller()
    //     0x147d220: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d224: LoadField: r2 = r0->field_67
    //     0x147d224: ldur            w2, [x0, #0x67]
    // 0x147d228: DecompressPointer r2
    //     0x147d228: add             x2, x2, HEAP, lsl #32
    // 0x147d22c: ldur            x1, [fp, #-0x10]
    // 0x147d230: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x147d230: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x147d234: r0 = getBag()
    //     0x147d234: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x147d238: ldur            x0, [fp, #-8]
    // 0x147d23c: LoadField: r1 = r0->field_f
    //     0x147d23c: ldur            w1, [x0, #0xf]
    // 0x147d240: DecompressPointer r1
    //     0x147d240: add             x1, x1, HEAP, lsl #32
    // 0x147d244: r0 = controller()
    //     0x147d244: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d248: mov             x2, x0
    // 0x147d24c: ldur            x0, [fp, #-8]
    // 0x147d250: stur            x2, [fp, #-0x10]
    // 0x147d254: LoadField: r1 = r0->field_f
    //     0x147d254: ldur            w1, [x0, #0xf]
    // 0x147d258: DecompressPointer r1
    //     0x147d258: add             x1, x1, HEAP, lsl #32
    // 0x147d25c: r0 = controller()
    //     0x147d25c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d260: LoadField: r1 = r0->field_5b
    //     0x147d260: ldur            w1, [x0, #0x5b]
    // 0x147d264: DecompressPointer r1
    //     0x147d264: add             x1, x1, HEAP, lsl #32
    // 0x147d268: r0 = value()
    //     0x147d268: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d26c: LoadField: r1 = r0->field_b
    //     0x147d26c: ldur            w1, [x0, #0xb]
    // 0x147d270: DecompressPointer r1
    //     0x147d270: add             x1, x1, HEAP, lsl #32
    // 0x147d274: cmp             w1, NULL
    // 0x147d278: b.ne            #0x147d284
    // 0x147d27c: r0 = Null
    //     0x147d27c: mov             x0, NULL
    // 0x147d280: b               #0x147d2a4
    // 0x147d284: LoadField: r0 = r1->field_7
    //     0x147d284: ldur            w0, [x1, #7]
    // 0x147d288: DecompressPointer r0
    //     0x147d288: add             x0, x0, HEAP, lsl #32
    // 0x147d28c: cmp             w0, NULL
    // 0x147d290: b.ne            #0x147d29c
    // 0x147d294: r0 = Null
    //     0x147d294: mov             x0, NULL
    // 0x147d298: b               #0x147d2a4
    // 0x147d29c: stp             x0, NULL, [SP]
    // 0x147d2a0: r0 = _Double.fromInteger()
    //     0x147d2a0: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x147d2a4: cmp             w0, NULL
    // 0x147d2a8: b.ne            #0x147d2b4
    // 0x147d2ac: d0 = 0.000000
    //     0x147d2ac: eor             v0.16b, v0.16b, v0.16b
    // 0x147d2b0: b               #0x147d2b8
    // 0x147d2b4: LoadField: d0 = r0->field_7
    //     0x147d2b4: ldur            d0, [x0, #7]
    // 0x147d2b8: ldur            x0, [fp, #-8]
    // 0x147d2bc: stur            d0, [fp, #-0x30]
    // 0x147d2c0: LoadField: r1 = r0->field_f
    //     0x147d2c0: ldur            w1, [x0, #0xf]
    // 0x147d2c4: DecompressPointer r1
    //     0x147d2c4: add             x1, x1, HEAP, lsl #32
    // 0x147d2c8: r0 = controller()
    //     0x147d2c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d2cc: LoadField: r1 = r0->field_5b
    //     0x147d2cc: ldur            w1, [x0, #0x5b]
    // 0x147d2d0: DecompressPointer r1
    //     0x147d2d0: add             x1, x1, HEAP, lsl #32
    // 0x147d2d4: r0 = value()
    //     0x147d2d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d2d8: LoadField: r1 = r0->field_b
    //     0x147d2d8: ldur            w1, [x0, #0xb]
    // 0x147d2dc: DecompressPointer r1
    //     0x147d2dc: add             x1, x1, HEAP, lsl #32
    // 0x147d2e0: cmp             w1, NULL
    // 0x147d2e4: b.ne            #0x147d2f0
    // 0x147d2e8: r0 = Null
    //     0x147d2e8: mov             x0, NULL
    // 0x147d2ec: b               #0x147d2f8
    // 0x147d2f0: LoadField: r0 = r1->field_b
    //     0x147d2f0: ldur            w0, [x1, #0xb]
    // 0x147d2f4: DecompressPointer r0
    //     0x147d2f4: add             x0, x0, HEAP, lsl #32
    // 0x147d2f8: cmp             w0, NULL
    // 0x147d2fc: b.ne            #0x147d308
    // 0x147d300: r2 = 0
    //     0x147d300: movz            x2, #0
    // 0x147d304: b               #0x147d318
    // 0x147d308: r1 = LoadInt32Instr(r0)
    //     0x147d308: sbfx            x1, x0, #1, #0x1f
    //     0x147d30c: tbz             w0, #0, #0x147d314
    //     0x147d310: ldur            x1, [x0, #7]
    // 0x147d314: mov             x2, x1
    // 0x147d318: ldur            x0, [fp, #-8]
    // 0x147d31c: stur            x2, [fp, #-0x18]
    // 0x147d320: LoadField: r1 = r0->field_f
    //     0x147d320: ldur            w1, [x0, #0xf]
    // 0x147d324: DecompressPointer r1
    //     0x147d324: add             x1, x1, HEAP, lsl #32
    // 0x147d328: r0 = controller()
    //     0x147d328: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d32c: LoadField: r1 = r0->field_5b
    //     0x147d32c: ldur            w1, [x0, #0x5b]
    // 0x147d330: DecompressPointer r1
    //     0x147d330: add             x1, x1, HEAP, lsl #32
    // 0x147d334: r0 = value()
    //     0x147d334: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d338: LoadField: r1 = r0->field_b
    //     0x147d338: ldur            w1, [x0, #0xb]
    // 0x147d33c: DecompressPointer r1
    //     0x147d33c: add             x1, x1, HEAP, lsl #32
    // 0x147d340: cmp             w1, NULL
    // 0x147d344: b.ne            #0x147d350
    // 0x147d348: r2 = Null
    //     0x147d348: mov             x2, NULL
    // 0x147d34c: b               #0x147d378
    // 0x147d350: LoadField: r0 = r1->field_2f
    //     0x147d350: ldur            w0, [x1, #0x2f]
    // 0x147d354: DecompressPointer r0
    //     0x147d354: add             x0, x0, HEAP, lsl #32
    // 0x147d358: cmp             w0, NULL
    // 0x147d35c: b.ne            #0x147d368
    // 0x147d360: r0 = Null
    //     0x147d360: mov             x0, NULL
    // 0x147d364: b               #0x147d374
    // 0x147d368: LoadField: r1 = r0->field_27
    //     0x147d368: ldur            w1, [x0, #0x27]
    // 0x147d36c: DecompressPointer r1
    //     0x147d36c: add             x1, x1, HEAP, lsl #32
    // 0x147d370: mov             x0, x1
    // 0x147d374: mov             x2, x0
    // 0x147d378: ldur            x0, [fp, #-8]
    // 0x147d37c: stur            x2, [fp, #-0x20]
    // 0x147d380: LoadField: r1 = r0->field_f
    //     0x147d380: ldur            w1, [x0, #0xf]
    // 0x147d384: DecompressPointer r1
    //     0x147d384: add             x1, x1, HEAP, lsl #32
    // 0x147d388: r0 = controller()
    //     0x147d388: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d38c: LoadField: r1 = r0->field_5b
    //     0x147d38c: ldur            w1, [x0, #0x5b]
    // 0x147d390: DecompressPointer r1
    //     0x147d390: add             x1, x1, HEAP, lsl #32
    // 0x147d394: r0 = value()
    //     0x147d394: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d398: LoadField: r1 = r0->field_b
    //     0x147d398: ldur            w1, [x0, #0xb]
    // 0x147d39c: DecompressPointer r1
    //     0x147d39c: add             x1, x1, HEAP, lsl #32
    // 0x147d3a0: cmp             w1, NULL
    // 0x147d3a4: b.ne            #0x147d3b0
    // 0x147d3a8: r0 = Null
    //     0x147d3a8: mov             x0, NULL
    // 0x147d3ac: b               #0x147d3f8
    // 0x147d3b0: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x147d3b0: ldur            w0, [x1, #0x17]
    // 0x147d3b4: DecompressPointer r0
    //     0x147d3b4: add             x0, x0, HEAP, lsl #32
    // 0x147d3b8: stur            x0, [fp, #-8]
    // 0x147d3bc: r1 = Function '<anonymous closure>':.
    //     0x147d3bc: add             x1, PP, #0x44, lsl #12  ; [pp+0x44970] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x147d3c0: ldr             x1, [x1, #0x970]
    // 0x147d3c4: r2 = Null
    //     0x147d3c4: mov             x2, NULL
    // 0x147d3c8: r0 = AllocateClosure()
    //     0x147d3c8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147d3cc: r16 = <String?>
    //     0x147d3cc: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x147d3d0: ldur            lr, [fp, #-8]
    // 0x147d3d4: stp             lr, x16, [SP, #8]
    // 0x147d3d8: str             x0, [SP]
    // 0x147d3dc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x147d3dc: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x147d3e0: r0 = map()
    //     0x147d3e0: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x147d3e4: r16 = ", "
    //     0x147d3e4: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x147d3e8: str             x16, [SP]
    // 0x147d3ec: mov             x1, x0
    // 0x147d3f0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x147d3f0: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x147d3f4: r0 = join()
    //     0x147d3f4: bl              #0x784778  ; [dart:_internal] ListIterable::join
    // 0x147d3f8: cmp             w0, NULL
    // 0x147d3fc: b.ne            #0x147d408
    // 0x147d400: r2 = ""
    //     0x147d400: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x147d404: b               #0x147d40c
    // 0x147d408: mov             x2, x0
    // 0x147d40c: ldur            d0, [fp, #-0x30]
    // 0x147d410: ldur            x1, [fp, #-0x18]
    // 0x147d414: ldur            x0, [fp, #-0x20]
    // 0x147d418: stur            x2, [fp, #-8]
    // 0x147d41c: r0 = EventData()
    //     0x147d41c: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x147d420: mov             x2, x0
    // 0x147d424: r0 = "bag_page"
    //     0x147d424: add             x0, PP, #0x25, lsl #12  ; [pp+0x25068] "bag_page"
    //     0x147d428: ldr             x0, [x0, #0x68]
    // 0x147d42c: stur            x2, [fp, #-0x28]
    // 0x147d430: StoreField: r2->field_13 = r0
    //     0x147d430: stur            w0, [x2, #0x13]
    // 0x147d434: ldur            d0, [fp, #-0x30]
    // 0x147d438: r0 = inline_Allocate_Double()
    //     0x147d438: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x147d43c: add             x0, x0, #0x10
    //     0x147d440: cmp             x1, x0
    //     0x147d444: b.ls            #0x147d4d4
    //     0x147d448: str             x0, [THR, #0x50]  ; THR::top
    //     0x147d44c: sub             x0, x0, #0xf
    //     0x147d450: movz            x1, #0xe15c
    //     0x147d454: movk            x1, #0x3, lsl #16
    //     0x147d458: stur            x1, [x0, #-1]
    // 0x147d45c: StoreField: r0->field_7 = d0
    //     0x147d45c: stur            d0, [x0, #7]
    // 0x147d460: StoreField: r2->field_3f = r0
    //     0x147d460: stur            w0, [x2, #0x3f]
    // 0x147d464: ldur            x3, [fp, #-0x18]
    // 0x147d468: r0 = BoxInt64Instr(r3)
    //     0x147d468: sbfiz           x0, x3, #1, #0x1f
    //     0x147d46c: cmp             x3, x0, asr #1
    //     0x147d470: b.eq            #0x147d47c
    //     0x147d474: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x147d478: stur            x3, [x0, #7]
    // 0x147d47c: StoreField: r2->field_43 = r0
    //     0x147d47c: stur            w0, [x2, #0x43]
    // 0x147d480: ldur            x0, [fp, #-8]
    // 0x147d484: StoreField: r2->field_47 = r0
    //     0x147d484: stur            w0, [x2, #0x47]
    // 0x147d488: ldur            x0, [fp, #-0x20]
    // 0x147d48c: r17 = 283
    //     0x147d48c: movz            x17, #0x11b
    // 0x147d490: str             w0, [x2, x17]
    // 0x147d494: r0 = EventsRequest()
    //     0x147d494: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x147d498: mov             x1, x0
    // 0x147d49c: r0 = "free_gift_removed"
    //     0x147d49c: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e800] "free_gift_removed"
    //     0x147d4a0: ldr             x0, [x0, #0x800]
    // 0x147d4a4: StoreField: r1->field_7 = r0
    //     0x147d4a4: stur            w0, [x1, #7]
    // 0x147d4a8: ldur            x0, [fp, #-0x28]
    // 0x147d4ac: StoreField: r1->field_b = r0
    //     0x147d4ac: stur            w0, [x1, #0xb]
    // 0x147d4b0: mov             x2, x1
    // 0x147d4b4: ldur            x1, [fp, #-0x10]
    // 0x147d4b8: r0 = postEvents()
    //     0x147d4b8: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x147d4bc: r0 = Null
    //     0x147d4bc: mov             x0, NULL
    // 0x147d4c0: LeaveFrame
    //     0x147d4c0: mov             SP, fp
    //     0x147d4c4: ldp             fp, lr, [SP], #0x10
    // 0x147d4c8: ret
    //     0x147d4c8: ret             
    // 0x147d4cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147d4cc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147d4d0: b               #0x147d12c
    // 0x147d4d4: SaveReg d0
    //     0x147d4d4: str             q0, [SP, #-0x10]!
    // 0x147d4d8: SaveReg r2
    //     0x147d4d8: str             x2, [SP, #-8]!
    // 0x147d4dc: r0 = AllocateDouble()
    //     0x147d4dc: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x147d4e0: RestoreReg r2
    //     0x147d4e0: ldr             x2, [SP], #8
    // 0x147d4e4: RestoreReg d0
    //     0x147d4e4: ldr             q0, [SP], #0x10
    // 0x147d4e8: b               #0x147d45c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x147d4ec, size: 0x3e8
    // 0x147d4ec: EnterFrame
    //     0x147d4ec: stp             fp, lr, [SP, #-0x10]!
    //     0x147d4f0: mov             fp, SP
    // 0x147d4f4: AllocStack(0x48)
    //     0x147d4f4: sub             SP, SP, #0x48
    // 0x147d4f8: SetupParameters()
    //     0x147d4f8: ldr             x0, [fp, #0x10]
    //     0x147d4fc: ldur            w2, [x0, #0x17]
    //     0x147d500: add             x2, x2, HEAP, lsl #32
    //     0x147d504: stur            x2, [fp, #-8]
    // 0x147d508: CheckStackOverflow
    //     0x147d508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x147d50c: cmp             SP, x16
    //     0x147d510: b.ls            #0x147d8b4
    // 0x147d514: LoadField: r1 = r2->field_f
    //     0x147d514: ldur            w1, [x2, #0xf]
    // 0x147d518: DecompressPointer r1
    //     0x147d518: add             x1, x1, HEAP, lsl #32
    // 0x147d51c: r0 = controller()
    //     0x147d51c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d520: mov             x2, x0
    // 0x147d524: ldur            x0, [fp, #-8]
    // 0x147d528: stur            x2, [fp, #-0x10]
    // 0x147d52c: LoadField: r1 = r0->field_f
    //     0x147d52c: ldur            w1, [x0, #0xf]
    // 0x147d530: DecompressPointer r1
    //     0x147d530: add             x1, x1, HEAP, lsl #32
    // 0x147d534: r0 = controller()
    //     0x147d534: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d538: LoadField: r1 = r0->field_5b
    //     0x147d538: ldur            w1, [x0, #0x5b]
    // 0x147d53c: DecompressPointer r1
    //     0x147d53c: add             x1, x1, HEAP, lsl #32
    // 0x147d540: r0 = value()
    //     0x147d540: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d544: LoadField: r1 = r0->field_b
    //     0x147d544: ldur            w1, [x0, #0xb]
    // 0x147d548: DecompressPointer r1
    //     0x147d548: add             x1, x1, HEAP, lsl #32
    // 0x147d54c: cmp             w1, NULL
    // 0x147d550: b.ne            #0x147d55c
    // 0x147d554: r0 = Null
    //     0x147d554: mov             x0, NULL
    // 0x147d558: b               #0x147d580
    // 0x147d55c: LoadField: r0 = r1->field_2f
    //     0x147d55c: ldur            w0, [x1, #0x2f]
    // 0x147d560: DecompressPointer r0
    //     0x147d560: add             x0, x0, HEAP, lsl #32
    // 0x147d564: cmp             w0, NULL
    // 0x147d568: b.ne            #0x147d574
    // 0x147d56c: r0 = Null
    //     0x147d56c: mov             x0, NULL
    // 0x147d570: b               #0x147d580
    // 0x147d574: LoadField: r1 = r0->field_23
    //     0x147d574: ldur            w1, [x0, #0x23]
    // 0x147d578: DecompressPointer r1
    //     0x147d578: add             x1, x1, HEAP, lsl #32
    // 0x147d57c: mov             x0, x1
    // 0x147d580: cmp             w0, NULL
    // 0x147d584: b.ne            #0x147d58c
    // 0x147d588: r0 = ""
    //     0x147d588: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x147d58c: ldur            x2, [fp, #-8]
    // 0x147d590: ldur            x1, [fp, #-0x10]
    // 0x147d594: StoreField: r1->field_67 = r0
    //     0x147d594: stur            w0, [x1, #0x67]
    //     0x147d598: ldurb           w16, [x1, #-1]
    //     0x147d59c: ldurb           w17, [x0, #-1]
    //     0x147d5a0: and             x16, x17, x16, lsr #2
    //     0x147d5a4: tst             x16, HEAP, lsr #32
    //     0x147d5a8: b.eq            #0x147d5b0
    //     0x147d5ac: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x147d5b0: LoadField: r1 = r2->field_f
    //     0x147d5b0: ldur            w1, [x2, #0xf]
    // 0x147d5b4: DecompressPointer r1
    //     0x147d5b4: add             x1, x1, HEAP, lsl #32
    // 0x147d5b8: r0 = controller()
    //     0x147d5b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d5bc: mov             x1, x0
    // 0x147d5c0: r0 = true
    //     0x147d5c0: add             x0, NULL, #0x20  ; true
    // 0x147d5c4: StoreField: r1->field_8f = r0
    //     0x147d5c4: stur            w0, [x1, #0x8f]
    // 0x147d5c8: ldur            x0, [fp, #-8]
    // 0x147d5cc: LoadField: r1 = r0->field_f
    //     0x147d5cc: ldur            w1, [x0, #0xf]
    // 0x147d5d0: DecompressPointer r1
    //     0x147d5d0: add             x1, x1, HEAP, lsl #32
    // 0x147d5d4: r0 = controller()
    //     0x147d5d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d5d8: mov             x1, x0
    // 0x147d5dc: r0 = false
    //     0x147d5dc: add             x0, NULL, #0x30  ; false
    // 0x147d5e0: StoreField: r1->field_93 = r0
    //     0x147d5e0: stur            w0, [x1, #0x93]
    // 0x147d5e4: ldur            x0, [fp, #-8]
    // 0x147d5e8: LoadField: r1 = r0->field_f
    //     0x147d5e8: ldur            w1, [x0, #0xf]
    // 0x147d5ec: DecompressPointer r1
    //     0x147d5ec: add             x1, x1, HEAP, lsl #32
    // 0x147d5f0: r0 = controller()
    //     0x147d5f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d5f4: mov             x2, x0
    // 0x147d5f8: ldur            x0, [fp, #-8]
    // 0x147d5fc: stur            x2, [fp, #-0x10]
    // 0x147d600: LoadField: r1 = r0->field_f
    //     0x147d600: ldur            w1, [x0, #0xf]
    // 0x147d604: DecompressPointer r1
    //     0x147d604: add             x1, x1, HEAP, lsl #32
    // 0x147d608: r0 = controller()
    //     0x147d608: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d60c: LoadField: r2 = r0->field_67
    //     0x147d60c: ldur            w2, [x0, #0x67]
    // 0x147d610: DecompressPointer r2
    //     0x147d610: add             x2, x2, HEAP, lsl #32
    // 0x147d614: ldur            x1, [fp, #-0x10]
    // 0x147d618: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x147d618: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x147d61c: r0 = getBag()
    //     0x147d61c: bl              #0x12c0a90  ; [package:customer_app/app/presentation/controllers/bag/bag_controller.dart] BagController::getBag
    // 0x147d620: ldur            x0, [fp, #-8]
    // 0x147d624: LoadField: r1 = r0->field_f
    //     0x147d624: ldur            w1, [x0, #0xf]
    // 0x147d628: DecompressPointer r1
    //     0x147d628: add             x1, x1, HEAP, lsl #32
    // 0x147d62c: r0 = controller()
    //     0x147d62c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d630: mov             x2, x0
    // 0x147d634: ldur            x0, [fp, #-8]
    // 0x147d638: stur            x2, [fp, #-0x10]
    // 0x147d63c: LoadField: r1 = r0->field_f
    //     0x147d63c: ldur            w1, [x0, #0xf]
    // 0x147d640: DecompressPointer r1
    //     0x147d640: add             x1, x1, HEAP, lsl #32
    // 0x147d644: r0 = controller()
    //     0x147d644: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d648: LoadField: r1 = r0->field_5b
    //     0x147d648: ldur            w1, [x0, #0x5b]
    // 0x147d64c: DecompressPointer r1
    //     0x147d64c: add             x1, x1, HEAP, lsl #32
    // 0x147d650: r0 = value()
    //     0x147d650: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d654: LoadField: r1 = r0->field_b
    //     0x147d654: ldur            w1, [x0, #0xb]
    // 0x147d658: DecompressPointer r1
    //     0x147d658: add             x1, x1, HEAP, lsl #32
    // 0x147d65c: cmp             w1, NULL
    // 0x147d660: b.ne            #0x147d66c
    // 0x147d664: r0 = Null
    //     0x147d664: mov             x0, NULL
    // 0x147d668: b               #0x147d68c
    // 0x147d66c: LoadField: r0 = r1->field_7
    //     0x147d66c: ldur            w0, [x1, #7]
    // 0x147d670: DecompressPointer r0
    //     0x147d670: add             x0, x0, HEAP, lsl #32
    // 0x147d674: cmp             w0, NULL
    // 0x147d678: b.ne            #0x147d684
    // 0x147d67c: r0 = Null
    //     0x147d67c: mov             x0, NULL
    // 0x147d680: b               #0x147d68c
    // 0x147d684: stp             x0, NULL, [SP]
    // 0x147d688: r0 = _Double.fromInteger()
    //     0x147d688: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x147d68c: cmp             w0, NULL
    // 0x147d690: b.ne            #0x147d69c
    // 0x147d694: d0 = 0.000000
    //     0x147d694: eor             v0.16b, v0.16b, v0.16b
    // 0x147d698: b               #0x147d6a0
    // 0x147d69c: LoadField: d0 = r0->field_7
    //     0x147d69c: ldur            d0, [x0, #7]
    // 0x147d6a0: ldur            x0, [fp, #-8]
    // 0x147d6a4: stur            d0, [fp, #-0x30]
    // 0x147d6a8: LoadField: r1 = r0->field_f
    //     0x147d6a8: ldur            w1, [x0, #0xf]
    // 0x147d6ac: DecompressPointer r1
    //     0x147d6ac: add             x1, x1, HEAP, lsl #32
    // 0x147d6b0: r0 = controller()
    //     0x147d6b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d6b4: LoadField: r1 = r0->field_5b
    //     0x147d6b4: ldur            w1, [x0, #0x5b]
    // 0x147d6b8: DecompressPointer r1
    //     0x147d6b8: add             x1, x1, HEAP, lsl #32
    // 0x147d6bc: r0 = value()
    //     0x147d6bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d6c0: LoadField: r1 = r0->field_b
    //     0x147d6c0: ldur            w1, [x0, #0xb]
    // 0x147d6c4: DecompressPointer r1
    //     0x147d6c4: add             x1, x1, HEAP, lsl #32
    // 0x147d6c8: cmp             w1, NULL
    // 0x147d6cc: b.ne            #0x147d6d8
    // 0x147d6d0: r0 = Null
    //     0x147d6d0: mov             x0, NULL
    // 0x147d6d4: b               #0x147d6e0
    // 0x147d6d8: LoadField: r0 = r1->field_b
    //     0x147d6d8: ldur            w0, [x1, #0xb]
    // 0x147d6dc: DecompressPointer r0
    //     0x147d6dc: add             x0, x0, HEAP, lsl #32
    // 0x147d6e0: cmp             w0, NULL
    // 0x147d6e4: b.ne            #0x147d6f0
    // 0x147d6e8: r2 = 0
    //     0x147d6e8: movz            x2, #0
    // 0x147d6ec: b               #0x147d700
    // 0x147d6f0: r1 = LoadInt32Instr(r0)
    //     0x147d6f0: sbfx            x1, x0, #1, #0x1f
    //     0x147d6f4: tbz             w0, #0, #0x147d6fc
    //     0x147d6f8: ldur            x1, [x0, #7]
    // 0x147d6fc: mov             x2, x1
    // 0x147d700: ldur            x0, [fp, #-8]
    // 0x147d704: stur            x2, [fp, #-0x18]
    // 0x147d708: LoadField: r1 = r0->field_f
    //     0x147d708: ldur            w1, [x0, #0xf]
    // 0x147d70c: DecompressPointer r1
    //     0x147d70c: add             x1, x1, HEAP, lsl #32
    // 0x147d710: r0 = controller()
    //     0x147d710: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d714: LoadField: r1 = r0->field_5b
    //     0x147d714: ldur            w1, [x0, #0x5b]
    // 0x147d718: DecompressPointer r1
    //     0x147d718: add             x1, x1, HEAP, lsl #32
    // 0x147d71c: r0 = value()
    //     0x147d71c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d720: LoadField: r1 = r0->field_b
    //     0x147d720: ldur            w1, [x0, #0xb]
    // 0x147d724: DecompressPointer r1
    //     0x147d724: add             x1, x1, HEAP, lsl #32
    // 0x147d728: cmp             w1, NULL
    // 0x147d72c: b.ne            #0x147d738
    // 0x147d730: r2 = Null
    //     0x147d730: mov             x2, NULL
    // 0x147d734: b               #0x147d760
    // 0x147d738: LoadField: r0 = r1->field_2f
    //     0x147d738: ldur            w0, [x1, #0x2f]
    // 0x147d73c: DecompressPointer r0
    //     0x147d73c: add             x0, x0, HEAP, lsl #32
    // 0x147d740: cmp             w0, NULL
    // 0x147d744: b.ne            #0x147d750
    // 0x147d748: r0 = Null
    //     0x147d748: mov             x0, NULL
    // 0x147d74c: b               #0x147d75c
    // 0x147d750: LoadField: r1 = r0->field_27
    //     0x147d750: ldur            w1, [x0, #0x27]
    // 0x147d754: DecompressPointer r1
    //     0x147d754: add             x1, x1, HEAP, lsl #32
    // 0x147d758: mov             x0, x1
    // 0x147d75c: mov             x2, x0
    // 0x147d760: ldur            x0, [fp, #-8]
    // 0x147d764: stur            x2, [fp, #-0x20]
    // 0x147d768: LoadField: r1 = r0->field_f
    //     0x147d768: ldur            w1, [x0, #0xf]
    // 0x147d76c: DecompressPointer r1
    //     0x147d76c: add             x1, x1, HEAP, lsl #32
    // 0x147d770: r0 = controller()
    //     0x147d770: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x147d774: LoadField: r1 = r0->field_5b
    //     0x147d774: ldur            w1, [x0, #0x5b]
    // 0x147d778: DecompressPointer r1
    //     0x147d778: add             x1, x1, HEAP, lsl #32
    // 0x147d77c: r0 = value()
    //     0x147d77c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x147d780: LoadField: r1 = r0->field_b
    //     0x147d780: ldur            w1, [x0, #0xb]
    // 0x147d784: DecompressPointer r1
    //     0x147d784: add             x1, x1, HEAP, lsl #32
    // 0x147d788: cmp             w1, NULL
    // 0x147d78c: b.ne            #0x147d798
    // 0x147d790: r0 = Null
    //     0x147d790: mov             x0, NULL
    // 0x147d794: b               #0x147d7e0
    // 0x147d798: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x147d798: ldur            w0, [x1, #0x17]
    // 0x147d79c: DecompressPointer r0
    //     0x147d79c: add             x0, x0, HEAP, lsl #32
    // 0x147d7a0: stur            x0, [fp, #-8]
    // 0x147d7a4: r1 = Function '<anonymous closure>':.
    //     0x147d7a4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44978] Function: [dart:ui] Paint::_objects (0x1557e38)
    //     0x147d7a8: ldr             x1, [x1, #0x978]
    // 0x147d7ac: r2 = Null
    //     0x147d7ac: mov             x2, NULL
    // 0x147d7b0: r0 = AllocateClosure()
    //     0x147d7b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x147d7b4: r16 = <String?>
    //     0x147d7b4: ldr             x16, [PP, #0xda8]  ; [pp+0xda8] TypeArguments: <String?>
    // 0x147d7b8: ldur            lr, [fp, #-8]
    // 0x147d7bc: stp             lr, x16, [SP, #8]
    // 0x147d7c0: str             x0, [SP]
    // 0x147d7c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x147d7c4: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x147d7c8: r0 = map()
    //     0x147d7c8: bl              #0x7dc75c  ; [dart:collection] ListBase::map
    // 0x147d7cc: r16 = ", "
    //     0x147d7cc: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x147d7d0: str             x16, [SP]
    // 0x147d7d4: mov             x1, x0
    // 0x147d7d8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x147d7d8: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x147d7dc: r0 = join()
    //     0x147d7dc: bl              #0x784778  ; [dart:_internal] ListIterable::join
    // 0x147d7e0: cmp             w0, NULL
    // 0x147d7e4: b.ne            #0x147d7f0
    // 0x147d7e8: r2 = ""
    //     0x147d7e8: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x147d7ec: b               #0x147d7f4
    // 0x147d7f0: mov             x2, x0
    // 0x147d7f4: ldur            d0, [fp, #-0x30]
    // 0x147d7f8: ldur            x1, [fp, #-0x18]
    // 0x147d7fc: ldur            x0, [fp, #-0x20]
    // 0x147d800: stur            x2, [fp, #-8]
    // 0x147d804: r0 = EventData()
    //     0x147d804: bl              #0x89c19c  ; AllocateEventDataStub -> EventData (size=0x17c)
    // 0x147d808: mov             x2, x0
    // 0x147d80c: r0 = "bag_page"
    //     0x147d80c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25068] "bag_page"
    //     0x147d810: ldr             x0, [x0, #0x68]
    // 0x147d814: stur            x2, [fp, #-0x28]
    // 0x147d818: StoreField: r2->field_13 = r0
    //     0x147d818: stur            w0, [x2, #0x13]
    // 0x147d81c: ldur            d0, [fp, #-0x30]
    // 0x147d820: r0 = inline_Allocate_Double()
    //     0x147d820: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x147d824: add             x0, x0, #0x10
    //     0x147d828: cmp             x1, x0
    //     0x147d82c: b.ls            #0x147d8bc
    //     0x147d830: str             x0, [THR, #0x50]  ; THR::top
    //     0x147d834: sub             x0, x0, #0xf
    //     0x147d838: movz            x1, #0xe15c
    //     0x147d83c: movk            x1, #0x3, lsl #16
    //     0x147d840: stur            x1, [x0, #-1]
    // 0x147d844: StoreField: r0->field_7 = d0
    //     0x147d844: stur            d0, [x0, #7]
    // 0x147d848: StoreField: r2->field_3f = r0
    //     0x147d848: stur            w0, [x2, #0x3f]
    // 0x147d84c: ldur            x3, [fp, #-0x18]
    // 0x147d850: r0 = BoxInt64Instr(r3)
    //     0x147d850: sbfiz           x0, x3, #1, #0x1f
    //     0x147d854: cmp             x3, x0, asr #1
    //     0x147d858: b.eq            #0x147d864
    //     0x147d85c: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x147d860: stur            x3, [x0, #7]
    // 0x147d864: StoreField: r2->field_43 = r0
    //     0x147d864: stur            w0, [x2, #0x43]
    // 0x147d868: ldur            x0, [fp, #-8]
    // 0x147d86c: StoreField: r2->field_47 = r0
    //     0x147d86c: stur            w0, [x2, #0x47]
    // 0x147d870: ldur            x0, [fp, #-0x20]
    // 0x147d874: r17 = 283
    //     0x147d874: movz            x17, #0x11b
    // 0x147d878: str             w0, [x2, x17]
    // 0x147d87c: r0 = EventsRequest()
    //     0x147d87c: bl              #0x89c190  ; AllocateEventsRequestStub -> EventsRequest (size=0x10)
    // 0x147d880: mov             x1, x0
    // 0x147d884: r0 = "free_gift_added"
    //     0x147d884: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e810] "free_gift_added"
    //     0x147d888: ldr             x0, [x0, #0x810]
    // 0x147d88c: StoreField: r1->field_7 = r0
    //     0x147d88c: stur            w0, [x1, #7]
    // 0x147d890: ldur            x0, [fp, #-0x28]
    // 0x147d894: StoreField: r1->field_b = r0
    //     0x147d894: stur            w0, [x1, #0xb]
    // 0x147d898: mov             x2, x1
    // 0x147d89c: ldur            x1, [fp, #-0x10]
    // 0x147d8a0: r0 = postEvents()
    //     0x147d8a0: bl              #0x8608dc  ; [package:customer_app/app/core/base/base_controller.dart] BaseController::postEvents
    // 0x147d8a4: r0 = Null
    //     0x147d8a4: mov             x0, NULL
    // 0x147d8a8: LeaveFrame
    //     0x147d8a8: mov             SP, fp
    //     0x147d8ac: ldp             fp, lr, [SP], #0x10
    // 0x147d8b0: ret
    //     0x147d8b0: ret             
    // 0x147d8b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x147d8b4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x147d8b8: b               #0x147d514
    // 0x147d8bc: SaveReg d0
    //     0x147d8bc: str             q0, [SP, #-0x10]!
    // 0x147d8c0: SaveReg r2
    //     0x147d8c0: str             x2, [SP, #-8]!
    // 0x147d8c4: r0 = AllocateDouble()
    //     0x147d8c4: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x147d8c8: RestoreReg r2
    //     0x147d8c8: ldr             x2, [SP], #8
    // 0x147d8cc: RestoreReg d0
    //     0x147d8cc: ldr             q0, [SP], #0x10
    // 0x147d8d0: b               #0x147d844
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d83c8, size: 0x2b4
    // 0x15d83c8: EnterFrame
    //     0x15d83c8: stp             fp, lr, [SP, #-0x10]!
    //     0x15d83cc: mov             fp, SP
    // 0x15d83d0: AllocStack(0x30)
    //     0x15d83d0: sub             SP, SP, #0x30
    // 0x15d83d4: SetupParameters(BagView this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x15d83d4: stur            x1, [fp, #-8]
    //     0x15d83d8: stur            x2, [fp, #-0x10]
    // 0x15d83dc: CheckStackOverflow
    //     0x15d83dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d83e0: cmp             SP, x16
    //     0x15d83e4: b.ls            #0x15d8674
    // 0x15d83e8: r1 = 2
    //     0x15d83e8: movz            x1, #0x2
    // 0x15d83ec: r0 = AllocateContext()
    //     0x15d83ec: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d83f0: ldur            x1, [fp, #-8]
    // 0x15d83f4: stur            x0, [fp, #-0x18]
    // 0x15d83f8: StoreField: r0->field_f = r1
    //     0x15d83f8: stur            w1, [x0, #0xf]
    // 0x15d83fc: ldur            x2, [fp, #-0x10]
    // 0x15d8400: StoreField: r0->field_13 = r2
    //     0x15d8400: stur            w2, [x0, #0x13]
    // 0x15d8404: r0 = Obx()
    //     0x15d8404: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d8408: ldur            x2, [fp, #-0x18]
    // 0x15d840c: r1 = Function '<anonymous closure>':.
    //     0x15d840c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44980] AnonymousClosure: (0x15cb290), in [package:customer_app/app/presentation/views/glass/bag/bag_view.dart] BagView::appBar (0x15df434)
    //     0x15d8410: ldr             x1, [x1, #0x980]
    // 0x15d8414: stur            x0, [fp, #-0x10]
    // 0x15d8418: r0 = AllocateClosure()
    //     0x15d8418: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d841c: mov             x1, x0
    // 0x15d8420: ldur            x0, [fp, #-0x10]
    // 0x15d8424: StoreField: r0->field_b = r1
    //     0x15d8424: stur            w1, [x0, #0xb]
    // 0x15d8428: ldur            x1, [fp, #-8]
    // 0x15d842c: r0 = controller()
    //     0x15d842c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8430: LoadField: r1 = r0->field_b3
    //     0x15d8430: ldur            w1, [x0, #0xb3]
    // 0x15d8434: DecompressPointer r1
    //     0x15d8434: add             x1, x1, HEAP, lsl #32
    // 0x15d8438: r0 = value()
    //     0x15d8438: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d843c: tbnz            w0, #4, #0x15d84d4
    // 0x15d8440: ldur            x2, [fp, #-0x18]
    // 0x15d8444: LoadField: r1 = r2->field_13
    //     0x15d8444: ldur            w1, [x2, #0x13]
    // 0x15d8448: DecompressPointer r1
    //     0x15d8448: add             x1, x1, HEAP, lsl #32
    // 0x15d844c: r0 = of()
    //     0x15d844c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d8450: LoadField: r1 = r0->field_5b
    //     0x15d8450: ldur            w1, [x0, #0x5b]
    // 0x15d8454: DecompressPointer r1
    //     0x15d8454: add             x1, x1, HEAP, lsl #32
    // 0x15d8458: stur            x1, [fp, #-8]
    // 0x15d845c: r0 = ColorFilter()
    //     0x15d845c: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d8460: mov             x1, x0
    // 0x15d8464: ldur            x0, [fp, #-8]
    // 0x15d8468: stur            x1, [fp, #-0x20]
    // 0x15d846c: StoreField: r1->field_7 = r0
    //     0x15d846c: stur            w0, [x1, #7]
    // 0x15d8470: r0 = Instance_BlendMode
    //     0x15d8470: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d8474: ldr             x0, [x0, #0xb30]
    // 0x15d8478: StoreField: r1->field_b = r0
    //     0x15d8478: stur            w0, [x1, #0xb]
    // 0x15d847c: r2 = 1
    //     0x15d847c: movz            x2, #0x1
    // 0x15d8480: StoreField: r1->field_13 = r2
    //     0x15d8480: stur            x2, [x1, #0x13]
    // 0x15d8484: r0 = SvgPicture()
    //     0x15d8484: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d8488: stur            x0, [fp, #-8]
    // 0x15d848c: ldur            x16, [fp, #-0x20]
    // 0x15d8490: str             x16, [SP]
    // 0x15d8494: mov             x1, x0
    // 0x15d8498: r2 = "assets/images/search.svg"
    //     0x15d8498: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea30] "assets/images/search.svg"
    //     0x15d849c: ldr             x2, [x2, #0xa30]
    // 0x15d84a0: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d84a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d84a4: ldr             x4, [x4, #0xa38]
    // 0x15d84a8: r0 = SvgPicture.asset()
    //     0x15d84a8: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d84ac: r0 = Align()
    //     0x15d84ac: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d84b0: r3 = Instance_Alignment
    //     0x15d84b0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d84b4: ldr             x3, [x3, #0xb10]
    // 0x15d84b8: StoreField: r0->field_f = r3
    //     0x15d84b8: stur            w3, [x0, #0xf]
    // 0x15d84bc: r4 = 1.000000
    //     0x15d84bc: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d84c0: StoreField: r0->field_13 = r4
    //     0x15d84c0: stur            w4, [x0, #0x13]
    // 0x15d84c4: ArrayStore: r0[0] = r4  ; List_4
    //     0x15d84c4: stur            w4, [x0, #0x17]
    // 0x15d84c8: ldur            x1, [fp, #-8]
    // 0x15d84cc: StoreField: r0->field_b = r1
    //     0x15d84cc: stur            w1, [x0, #0xb]
    // 0x15d84d0: b               #0x15d8584
    // 0x15d84d4: ldur            x5, [fp, #-0x18]
    // 0x15d84d8: r4 = 1.000000
    //     0x15d84d8: ldr             x4, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d84dc: r0 = Instance_BlendMode
    //     0x15d84dc: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d84e0: ldr             x0, [x0, #0xb30]
    // 0x15d84e4: r3 = Instance_Alignment
    //     0x15d84e4: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d84e8: ldr             x3, [x3, #0xb10]
    // 0x15d84ec: r2 = 1
    //     0x15d84ec: movz            x2, #0x1
    // 0x15d84f0: LoadField: r1 = r5->field_13
    //     0x15d84f0: ldur            w1, [x5, #0x13]
    // 0x15d84f4: DecompressPointer r1
    //     0x15d84f4: add             x1, x1, HEAP, lsl #32
    // 0x15d84f8: r0 = of()
    //     0x15d84f8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d84fc: LoadField: r1 = r0->field_5b
    //     0x15d84fc: ldur            w1, [x0, #0x5b]
    // 0x15d8500: DecompressPointer r1
    //     0x15d8500: add             x1, x1, HEAP, lsl #32
    // 0x15d8504: stur            x1, [fp, #-8]
    // 0x15d8508: r0 = ColorFilter()
    //     0x15d8508: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d850c: mov             x1, x0
    // 0x15d8510: ldur            x0, [fp, #-8]
    // 0x15d8514: stur            x1, [fp, #-0x20]
    // 0x15d8518: StoreField: r1->field_7 = r0
    //     0x15d8518: stur            w0, [x1, #7]
    // 0x15d851c: r0 = Instance_BlendMode
    //     0x15d851c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d8520: ldr             x0, [x0, #0xb30]
    // 0x15d8524: StoreField: r1->field_b = r0
    //     0x15d8524: stur            w0, [x1, #0xb]
    // 0x15d8528: r0 = 1
    //     0x15d8528: movz            x0, #0x1
    // 0x15d852c: StoreField: r1->field_13 = r0
    //     0x15d852c: stur            x0, [x1, #0x13]
    // 0x15d8530: r0 = SvgPicture()
    //     0x15d8530: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d8534: stur            x0, [fp, #-8]
    // 0x15d8538: ldur            x16, [fp, #-0x20]
    // 0x15d853c: str             x16, [SP]
    // 0x15d8540: mov             x1, x0
    // 0x15d8544: r2 = "assets/images/appbar_arrow.svg"
    //     0x15d8544: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15d8548: ldr             x2, [x2, #0xa40]
    // 0x15d854c: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d854c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d8550: ldr             x4, [x4, #0xa38]
    // 0x15d8554: r0 = SvgPicture.asset()
    //     0x15d8554: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d8558: r0 = Align()
    //     0x15d8558: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d855c: mov             x1, x0
    // 0x15d8560: r0 = Instance_Alignment
    //     0x15d8560: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d8564: ldr             x0, [x0, #0xb10]
    // 0x15d8568: StoreField: r1->field_f = r0
    //     0x15d8568: stur            w0, [x1, #0xf]
    // 0x15d856c: r0 = 1.000000
    //     0x15d856c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d8570: StoreField: r1->field_13 = r0
    //     0x15d8570: stur            w0, [x1, #0x13]
    // 0x15d8574: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d8574: stur            w0, [x1, #0x17]
    // 0x15d8578: ldur            x0, [fp, #-8]
    // 0x15d857c: StoreField: r1->field_b = r0
    //     0x15d857c: stur            w0, [x1, #0xb]
    // 0x15d8580: mov             x0, x1
    // 0x15d8584: stur            x0, [fp, #-8]
    // 0x15d8588: r0 = InkWell()
    //     0x15d8588: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d858c: mov             x3, x0
    // 0x15d8590: ldur            x0, [fp, #-8]
    // 0x15d8594: stur            x3, [fp, #-0x20]
    // 0x15d8598: StoreField: r3->field_b = r0
    //     0x15d8598: stur            w0, [x3, #0xb]
    // 0x15d859c: ldur            x2, [fp, #-0x18]
    // 0x15d85a0: r1 = Function '<anonymous closure>':.
    //     0x15d85a0: add             x1, PP, #0x44, lsl #12  ; [pp+0x44988] AnonymousClosure: (0x15cb1c4), in [package:customer_app/app/presentation/views/line/bag/bag_view.dart] BagView::appBar (0x15e64a0)
    //     0x15d85a4: ldr             x1, [x1, #0x988]
    // 0x15d85a8: r0 = AllocateClosure()
    //     0x15d85a8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d85ac: ldur            x2, [fp, #-0x20]
    // 0x15d85b0: StoreField: r2->field_f = r0
    //     0x15d85b0: stur            w0, [x2, #0xf]
    // 0x15d85b4: r0 = true
    //     0x15d85b4: add             x0, NULL, #0x20  ; true
    // 0x15d85b8: StoreField: r2->field_43 = r0
    //     0x15d85b8: stur            w0, [x2, #0x43]
    // 0x15d85bc: r1 = Instance_BoxShape
    //     0x15d85bc: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d85c0: ldr             x1, [x1, #0x80]
    // 0x15d85c4: StoreField: r2->field_47 = r1
    //     0x15d85c4: stur            w1, [x2, #0x47]
    // 0x15d85c8: StoreField: r2->field_6f = r0
    //     0x15d85c8: stur            w0, [x2, #0x6f]
    // 0x15d85cc: r1 = false
    //     0x15d85cc: add             x1, NULL, #0x30  ; false
    // 0x15d85d0: StoreField: r2->field_73 = r1
    //     0x15d85d0: stur            w1, [x2, #0x73]
    // 0x15d85d4: StoreField: r2->field_83 = r0
    //     0x15d85d4: stur            w0, [x2, #0x83]
    // 0x15d85d8: StoreField: r2->field_7b = r1
    //     0x15d85d8: stur            w1, [x2, #0x7b]
    // 0x15d85dc: r0 = Obx()
    //     0x15d85dc: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d85e0: ldur            x2, [fp, #-0x18]
    // 0x15d85e4: r1 = Function '<anonymous closure>':.
    //     0x15d85e4: add             x1, PP, #0x44, lsl #12  ; [pp+0x44990] AnonymousClosure: (0x15d867c), in [package:customer_app/app/presentation/views/cosmetic/bag/bag_view.dart] BagView::appBar (0x15d83c8)
    //     0x15d85e8: ldr             x1, [x1, #0x990]
    // 0x15d85ec: stur            x0, [fp, #-8]
    // 0x15d85f0: r0 = AllocateClosure()
    //     0x15d85f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d85f4: mov             x1, x0
    // 0x15d85f8: ldur            x0, [fp, #-8]
    // 0x15d85fc: StoreField: r0->field_b = r1
    //     0x15d85fc: stur            w1, [x0, #0xb]
    // 0x15d8600: r1 = Null
    //     0x15d8600: mov             x1, NULL
    // 0x15d8604: r2 = 2
    //     0x15d8604: movz            x2, #0x2
    // 0x15d8608: r0 = AllocateArray()
    //     0x15d8608: bl              #0x16f7198  ; AllocateArrayStub
    // 0x15d860c: mov             x2, x0
    // 0x15d8610: ldur            x0, [fp, #-8]
    // 0x15d8614: stur            x2, [fp, #-0x18]
    // 0x15d8618: StoreField: r2->field_f = r0
    //     0x15d8618: stur            w0, [x2, #0xf]
    // 0x15d861c: r1 = <Widget>
    //     0x15d861c: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x15d8620: r0 = AllocateGrowableArray()
    //     0x15d8620: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x15d8624: mov             x1, x0
    // 0x15d8628: ldur            x0, [fp, #-0x18]
    // 0x15d862c: stur            x1, [fp, #-8]
    // 0x15d8630: StoreField: r1->field_f = r0
    //     0x15d8630: stur            w0, [x1, #0xf]
    // 0x15d8634: r0 = 2
    //     0x15d8634: movz            x0, #0x2
    // 0x15d8638: StoreField: r1->field_b = r0
    //     0x15d8638: stur            w0, [x1, #0xb]
    // 0x15d863c: r0 = AppBar()
    //     0x15d863c: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15d8640: stur            x0, [fp, #-0x18]
    // 0x15d8644: ldur            x16, [fp, #-0x10]
    // 0x15d8648: ldur            lr, [fp, #-8]
    // 0x15d864c: stp             lr, x16, [SP]
    // 0x15d8650: mov             x1, x0
    // 0x15d8654: ldur            x2, [fp, #-0x20]
    // 0x15d8658: r4 = const [0, 0x4, 0x2, 0x2, actions, 0x3, title, 0x2, null]
    //     0x15d8658: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea58] List(9) [0, 0x4, 0x2, 0x2, "actions", 0x3, "title", 0x2, Null]
    //     0x15d865c: ldr             x4, [x4, #0xa58]
    // 0x15d8660: r0 = AppBar()
    //     0x15d8660: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15d8664: ldur            x0, [fp, #-0x18]
    // 0x15d8668: LeaveFrame
    //     0x15d8668: mov             SP, fp
    //     0x15d866c: ldp             fp, lr, [SP], #0x10
    // 0x15d8670: ret
    //     0x15d8670: ret             
    // 0x15d8674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d8674: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d8678: b               #0x15d83e8
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x15d867c, size: 0x33c
    // 0x15d867c: EnterFrame
    //     0x15d867c: stp             fp, lr, [SP, #-0x10]!
    //     0x15d8680: mov             fp, SP
    // 0x15d8684: AllocStack(0x58)
    //     0x15d8684: sub             SP, SP, #0x58
    // 0x15d8688: SetupParameters()
    //     0x15d8688: ldr             x0, [fp, #0x10]
    //     0x15d868c: ldur            w2, [x0, #0x17]
    //     0x15d8690: add             x2, x2, HEAP, lsl #32
    //     0x15d8694: stur            x2, [fp, #-8]
    // 0x15d8698: CheckStackOverflow
    //     0x15d8698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d869c: cmp             SP, x16
    //     0x15d86a0: b.ls            #0x15d89b0
    // 0x15d86a4: LoadField: r1 = r2->field_f
    //     0x15d86a4: ldur            w1, [x2, #0xf]
    // 0x15d86a8: DecompressPointer r1
    //     0x15d86a8: add             x1, x1, HEAP, lsl #32
    // 0x15d86ac: r0 = controller()
    //     0x15d86ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d86b0: LoadField: r1 = r0->field_73
    //     0x15d86b0: ldur            w1, [x0, #0x73]
    // 0x15d86b4: DecompressPointer r1
    //     0x15d86b4: add             x1, x1, HEAP, lsl #32
    // 0x15d86b8: r0 = value()
    //     0x15d86b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d86bc: LoadField: r1 = r0->field_1f
    //     0x15d86bc: ldur            w1, [x0, #0x1f]
    // 0x15d86c0: DecompressPointer r1
    //     0x15d86c0: add             x1, x1, HEAP, lsl #32
    // 0x15d86c4: cmp             w1, NULL
    // 0x15d86c8: b.ne            #0x15d86d4
    // 0x15d86cc: r0 = Null
    //     0x15d86cc: mov             x0, NULL
    // 0x15d86d0: b               #0x15d86dc
    // 0x15d86d4: LoadField: r0 = r1->field_7
    //     0x15d86d4: ldur            w0, [x1, #7]
    // 0x15d86d8: DecompressPointer r0
    //     0x15d86d8: add             x0, x0, HEAP, lsl #32
    // 0x15d86dc: cmp             w0, NULL
    // 0x15d86e0: b.ne            #0x15d86ec
    // 0x15d86e4: r0 = false
    //     0x15d86e4: add             x0, NULL, #0x30  ; false
    // 0x15d86e8: b               #0x15d8918
    // 0x15d86ec: tbnz            w0, #4, #0x15d8914
    // 0x15d86f0: ldur            x0, [fp, #-8]
    // 0x15d86f4: LoadField: r1 = r0->field_f
    //     0x15d86f4: ldur            w1, [x0, #0xf]
    // 0x15d86f8: DecompressPointer r1
    //     0x15d86f8: add             x1, x1, HEAP, lsl #32
    // 0x15d86fc: r0 = controller()
    //     0x15d86fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8700: LoadField: r1 = r0->field_b7
    //     0x15d8700: ldur            w1, [x0, #0xb7]
    // 0x15d8704: DecompressPointer r1
    //     0x15d8704: add             x1, x1, HEAP, lsl #32
    // 0x15d8708: r0 = value()
    //     0x15d8708: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d870c: tbnz            w0, #4, #0x15d8744
    // 0x15d8710: ldur            x0, [fp, #-8]
    // 0x15d8714: LoadField: r1 = r0->field_f
    //     0x15d8714: ldur            w1, [x0, #0xf]
    // 0x15d8718: DecompressPointer r1
    //     0x15d8718: add             x1, x1, HEAP, lsl #32
    // 0x15d871c: r0 = controller()
    //     0x15d871c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8720: LoadField: r1 = r0->field_bb
    //     0x15d8720: ldur            w1, [x0, #0xbb]
    // 0x15d8724: DecompressPointer r1
    //     0x15d8724: add             x1, x1, HEAP, lsl #32
    // 0x15d8728: r0 = value()
    //     0x15d8728: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d872c: cmp             w0, NULL
    // 0x15d8730: r16 = true
    //     0x15d8730: add             x16, NULL, #0x20  ; true
    // 0x15d8734: r17 = false
    //     0x15d8734: add             x17, NULL, #0x30  ; false
    // 0x15d8738: csel            x1, x16, x17, ne
    // 0x15d873c: mov             x2, x1
    // 0x15d8740: b               #0x15d8748
    // 0x15d8744: r2 = false
    //     0x15d8744: add             x2, NULL, #0x30  ; false
    // 0x15d8748: ldur            x0, [fp, #-8]
    // 0x15d874c: stur            x2, [fp, #-0x10]
    // 0x15d8750: LoadField: r1 = r0->field_13
    //     0x15d8750: ldur            w1, [x0, #0x13]
    // 0x15d8754: DecompressPointer r1
    //     0x15d8754: add             x1, x1, HEAP, lsl #32
    // 0x15d8758: r0 = of()
    //     0x15d8758: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d875c: LoadField: r2 = r0->field_5b
    //     0x15d875c: ldur            w2, [x0, #0x5b]
    // 0x15d8760: DecompressPointer r2
    //     0x15d8760: add             x2, x2, HEAP, lsl #32
    // 0x15d8764: ldur            x0, [fp, #-8]
    // 0x15d8768: stur            x2, [fp, #-0x18]
    // 0x15d876c: LoadField: r1 = r0->field_f
    //     0x15d876c: ldur            w1, [x0, #0xf]
    // 0x15d8770: DecompressPointer r1
    //     0x15d8770: add             x1, x1, HEAP, lsl #32
    // 0x15d8774: r0 = controller()
    //     0x15d8774: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d8778: LoadField: r1 = r0->field_bb
    //     0x15d8778: ldur            w1, [x0, #0xbb]
    // 0x15d877c: DecompressPointer r1
    //     0x15d877c: add             x1, x1, HEAP, lsl #32
    // 0x15d8780: r0 = value()
    //     0x15d8780: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d8784: cmp             w0, NULL
    // 0x15d8788: r16 = true
    //     0x15d8788: add             x16, NULL, #0x20  ; true
    // 0x15d878c: r17 = false
    //     0x15d878c: add             x17, NULL, #0x30  ; false
    // 0x15d8790: csel            x2, x16, x17, ne
    // 0x15d8794: ldur            x0, [fp, #-8]
    // 0x15d8798: stur            x2, [fp, #-0x20]
    // 0x15d879c: LoadField: r1 = r0->field_f
    //     0x15d879c: ldur            w1, [x0, #0xf]
    // 0x15d87a0: DecompressPointer r1
    //     0x15d87a0: add             x1, x1, HEAP, lsl #32
    // 0x15d87a4: r0 = controller()
    //     0x15d87a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x15d87a8: LoadField: r1 = r0->field_bb
    //     0x15d87a8: ldur            w1, [x0, #0xbb]
    // 0x15d87ac: DecompressPointer r1
    //     0x15d87ac: add             x1, x1, HEAP, lsl #32
    // 0x15d87b0: r0 = value()
    //     0x15d87b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x15d87b4: str             x0, [SP]
    // 0x15d87b8: r0 = _interpolateSingle()
    //     0x15d87b8: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x15d87bc: mov             x2, x0
    // 0x15d87c0: ldur            x0, [fp, #-8]
    // 0x15d87c4: stur            x2, [fp, #-0x28]
    // 0x15d87c8: LoadField: r1 = r0->field_13
    //     0x15d87c8: ldur            w1, [x0, #0x13]
    // 0x15d87cc: DecompressPointer r1
    //     0x15d87cc: add             x1, x1, HEAP, lsl #32
    // 0x15d87d0: r0 = of()
    //     0x15d87d0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d87d4: LoadField: r1 = r0->field_87
    //     0x15d87d4: ldur            w1, [x0, #0x87]
    // 0x15d87d8: DecompressPointer r1
    //     0x15d87d8: add             x1, x1, HEAP, lsl #32
    // 0x15d87dc: LoadField: r0 = r1->field_27
    //     0x15d87dc: ldur            w0, [x1, #0x27]
    // 0x15d87e0: DecompressPointer r0
    //     0x15d87e0: add             x0, x0, HEAP, lsl #32
    // 0x15d87e4: r16 = Instance_Color
    //     0x15d87e4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x15d87e8: str             x16, [SP]
    // 0x15d87ec: mov             x1, x0
    // 0x15d87f0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x15d87f0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x15d87f4: ldr             x4, [x4, #0xf40]
    // 0x15d87f8: r0 = copyWith()
    //     0x15d87f8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x15d87fc: stur            x0, [fp, #-0x30]
    // 0x15d8800: r0 = Text()
    //     0x15d8800: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x15d8804: mov             x2, x0
    // 0x15d8808: ldur            x0, [fp, #-0x28]
    // 0x15d880c: stur            x2, [fp, #-0x38]
    // 0x15d8810: StoreField: r2->field_b = r0
    //     0x15d8810: stur            w0, [x2, #0xb]
    // 0x15d8814: ldur            x0, [fp, #-0x30]
    // 0x15d8818: StoreField: r2->field_13 = r0
    //     0x15d8818: stur            w0, [x2, #0x13]
    // 0x15d881c: ldur            x0, [fp, #-8]
    // 0x15d8820: LoadField: r1 = r0->field_13
    //     0x15d8820: ldur            w1, [x0, #0x13]
    // 0x15d8824: DecompressPointer r1
    //     0x15d8824: add             x1, x1, HEAP, lsl #32
    // 0x15d8828: r0 = of()
    //     0x15d8828: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d882c: LoadField: r1 = r0->field_5b
    //     0x15d882c: ldur            w1, [x0, #0x5b]
    // 0x15d8830: DecompressPointer r1
    //     0x15d8830: add             x1, x1, HEAP, lsl #32
    // 0x15d8834: stur            x1, [fp, #-8]
    // 0x15d8838: r0 = ColorFilter()
    //     0x15d8838: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d883c: mov             x1, x0
    // 0x15d8840: ldur            x0, [fp, #-8]
    // 0x15d8844: stur            x1, [fp, #-0x28]
    // 0x15d8848: StoreField: r1->field_7 = r0
    //     0x15d8848: stur            w0, [x1, #7]
    // 0x15d884c: r0 = Instance_BlendMode
    //     0x15d884c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d8850: ldr             x0, [x0, #0xb30]
    // 0x15d8854: StoreField: r1->field_b = r0
    //     0x15d8854: stur            w0, [x1, #0xb]
    // 0x15d8858: r0 = 1
    //     0x15d8858: movz            x0, #0x1
    // 0x15d885c: StoreField: r1->field_13 = r0
    //     0x15d885c: stur            x0, [x1, #0x13]
    // 0x15d8860: r0 = SvgPicture()
    //     0x15d8860: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d8864: stur            x0, [fp, #-8]
    // 0x15d8868: r16 = Instance_BoxFit
    //     0x15d8868: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0x15d886c: ldr             x16, [x16, #0xb18]
    // 0x15d8870: r30 = 24.000000
    //     0x15d8870: add             lr, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d8874: ldr             lr, [lr, #0xba8]
    // 0x15d8878: stp             lr, x16, [SP, #0x10]
    // 0x15d887c: r16 = 24.000000
    //     0x15d887c: add             x16, PP, #0x27, lsl #12  ; [pp+0x27ba8] 24
    //     0x15d8880: ldr             x16, [x16, #0xba8]
    // 0x15d8884: ldur            lr, [fp, #-0x28]
    // 0x15d8888: stp             lr, x16, [SP]
    // 0x15d888c: mov             x1, x0
    // 0x15d8890: r2 = "assets/images/shopping_bag.svg"
    //     0x15d8890: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea60] "assets/images/shopping_bag.svg"
    //     0x15d8894: ldr             x2, [x2, #0xa60]
    // 0x15d8898: r4 = const [0, 0x6, 0x4, 0x2, colorFilter, 0x5, fit, 0x2, height, 0x3, width, 0x4, null]
    //     0x15d8898: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea68] List(13) [0, 0x6, 0x4, 0x2, "colorFilter", 0x5, "fit", 0x2, "height", 0x3, "width", 0x4, Null]
    //     0x15d889c: ldr             x4, [x4, #0xa68]
    // 0x15d88a0: r0 = SvgPicture.asset()
    //     0x15d88a0: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d88a4: r0 = Badge()
    //     0x15d88a4: bl              #0xa68908  ; AllocateBadgeStub -> Badge (size=0x34)
    // 0x15d88a8: mov             x1, x0
    // 0x15d88ac: ldur            x0, [fp, #-0x18]
    // 0x15d88b0: stur            x1, [fp, #-0x28]
    // 0x15d88b4: StoreField: r1->field_b = r0
    //     0x15d88b4: stur            w0, [x1, #0xb]
    // 0x15d88b8: ldur            x0, [fp, #-0x38]
    // 0x15d88bc: StoreField: r1->field_27 = r0
    //     0x15d88bc: stur            w0, [x1, #0x27]
    // 0x15d88c0: ldur            x0, [fp, #-0x20]
    // 0x15d88c4: StoreField: r1->field_2b = r0
    //     0x15d88c4: stur            w0, [x1, #0x2b]
    // 0x15d88c8: ldur            x0, [fp, #-8]
    // 0x15d88cc: StoreField: r1->field_2f = r0
    //     0x15d88cc: stur            w0, [x1, #0x2f]
    // 0x15d88d0: r0 = Visibility()
    //     0x15d88d0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x15d88d4: mov             x1, x0
    // 0x15d88d8: ldur            x0, [fp, #-0x28]
    // 0x15d88dc: StoreField: r1->field_b = r0
    //     0x15d88dc: stur            w0, [x1, #0xb]
    // 0x15d88e0: r0 = Instance_SizedBox
    //     0x15d88e0: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x15d88e4: StoreField: r1->field_f = r0
    //     0x15d88e4: stur            w0, [x1, #0xf]
    // 0x15d88e8: ldur            x0, [fp, #-0x10]
    // 0x15d88ec: StoreField: r1->field_13 = r0
    //     0x15d88ec: stur            w0, [x1, #0x13]
    // 0x15d88f0: r0 = false
    //     0x15d88f0: add             x0, NULL, #0x30  ; false
    // 0x15d88f4: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d88f4: stur            w0, [x1, #0x17]
    // 0x15d88f8: StoreField: r1->field_1b = r0
    //     0x15d88f8: stur            w0, [x1, #0x1b]
    // 0x15d88fc: StoreField: r1->field_1f = r0
    //     0x15d88fc: stur            w0, [x1, #0x1f]
    // 0x15d8900: StoreField: r1->field_23 = r0
    //     0x15d8900: stur            w0, [x1, #0x23]
    // 0x15d8904: StoreField: r1->field_27 = r0
    //     0x15d8904: stur            w0, [x1, #0x27]
    // 0x15d8908: StoreField: r1->field_2b = r0
    //     0x15d8908: stur            w0, [x1, #0x2b]
    // 0x15d890c: mov             x0, x1
    // 0x15d8910: b               #0x15d8930
    // 0x15d8914: r0 = false
    //     0x15d8914: add             x0, NULL, #0x30  ; false
    // 0x15d8918: r0 = Container()
    //     0x15d8918: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x15d891c: mov             x1, x0
    // 0x15d8920: stur            x0, [fp, #-8]
    // 0x15d8924: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x15d8924: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x15d8928: r0 = Container()
    //     0x15d8928: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x15d892c: ldur            x0, [fp, #-8]
    // 0x15d8930: stur            x0, [fp, #-8]
    // 0x15d8934: r0 = InkWell()
    //     0x15d8934: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d8938: mov             x3, x0
    // 0x15d893c: ldur            x0, [fp, #-8]
    // 0x15d8940: stur            x3, [fp, #-0x10]
    // 0x15d8944: StoreField: r3->field_b = r0
    //     0x15d8944: stur            w0, [x3, #0xb]
    // 0x15d8948: r1 = Function '<anonymous closure>':.
    //     0x15d8948: add             x1, PP, #0x44, lsl #12  ; [pp+0x44998] AnonymousClosure: (0x15cae64), in [package:customer_app/app/presentation/views/line/collections/collection_page.dart] CollectionPage::appBar (0x15e9764)
    //     0x15d894c: ldr             x1, [x1, #0x998]
    // 0x15d8950: r2 = Null
    //     0x15d8950: mov             x2, NULL
    // 0x15d8954: r0 = AllocateClosure()
    //     0x15d8954: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d8958: mov             x1, x0
    // 0x15d895c: ldur            x0, [fp, #-0x10]
    // 0x15d8960: StoreField: r0->field_f = r1
    //     0x15d8960: stur            w1, [x0, #0xf]
    // 0x15d8964: r1 = true
    //     0x15d8964: add             x1, NULL, #0x20  ; true
    // 0x15d8968: StoreField: r0->field_43 = r1
    //     0x15d8968: stur            w1, [x0, #0x43]
    // 0x15d896c: r2 = Instance_BoxShape
    //     0x15d896c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d8970: ldr             x2, [x2, #0x80]
    // 0x15d8974: StoreField: r0->field_47 = r2
    //     0x15d8974: stur            w2, [x0, #0x47]
    // 0x15d8978: StoreField: r0->field_6f = r1
    //     0x15d8978: stur            w1, [x0, #0x6f]
    // 0x15d897c: r2 = false
    //     0x15d897c: add             x2, NULL, #0x30  ; false
    // 0x15d8980: StoreField: r0->field_73 = r2
    //     0x15d8980: stur            w2, [x0, #0x73]
    // 0x15d8984: StoreField: r0->field_83 = r1
    //     0x15d8984: stur            w1, [x0, #0x83]
    // 0x15d8988: StoreField: r0->field_7b = r2
    //     0x15d8988: stur            w2, [x0, #0x7b]
    // 0x15d898c: r0 = Padding()
    //     0x15d898c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x15d8990: r1 = Instance_EdgeInsets
    //     0x15d8990: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea78] Obj!EdgeInsets@d5a0e1
    //     0x15d8994: ldr             x1, [x1, #0xa78]
    // 0x15d8998: StoreField: r0->field_f = r1
    //     0x15d8998: stur            w1, [x0, #0xf]
    // 0x15d899c: ldur            x1, [fp, #-0x10]
    // 0x15d89a0: StoreField: r0->field_b = r1
    //     0x15d89a0: stur            w1, [x0, #0xb]
    // 0x15d89a4: LeaveFrame
    //     0x15d89a4: mov             SP, fp
    //     0x15d89a8: ldp             fp, lr, [SP], #0x10
    // 0x15d89ac: ret
    //     0x15d89ac: ret             
    // 0x15d89b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d89b0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d89b4: b               #0x15d86a4
  }
}
