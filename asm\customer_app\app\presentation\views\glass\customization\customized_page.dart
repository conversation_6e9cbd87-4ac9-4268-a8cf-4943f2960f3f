// lib: , url: package:customer_app/app/presentation/views/glass/customization/customized_page.dart

// class id: 1049388, size: 0x8
class :: {
}

// class id: 4571, size: 0x14, field offset: 0x14
//   const constructor, 
class CustomizedPage extends BaseView<dynamic> {

  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x1307e68, size: 0xd54
    // 0x1307e68: EnterFrame
    //     0x1307e68: stp             fp, lr, [SP, #-0x10]!
    //     0x1307e6c: mov             fp, SP
    // 0x1307e70: AllocStack(0x60)
    //     0x1307e70: sub             SP, SP, #0x60
    // 0x1307e74: SetupParameters(CustomizedPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x1307e74: stur            NULL, [fp, #-8]
    //     0x1307e78: movz            x0, #0
    //     0x1307e7c: add             x1, fp, w0, sxtw #2
    //     0x1307e80: ldr             x1, [x1, #0x18]
    //     0x1307e84: add             x2, fp, w0, sxtw #2
    //     0x1307e88: ldr             x2, [x2, #0x10]
    //     0x1307e8c: stur            x2, [fp, #-0x18]
    //     0x1307e90: ldur            w3, [x1, #0x17]
    //     0x1307e94: add             x3, x3, HEAP, lsl #32
    //     0x1307e98: stur            x3, [fp, #-0x10]
    // 0x1307e9c: CheckStackOverflow
    //     0x1307e9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1307ea0: cmp             SP, x16
    //     0x1307ea4: b.ls            #0x1308bb4
    // 0x1307ea8: InitAsync() -> Future<Null?>
    //     0x1307ea8: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x1307eac: bl              #0x6326e0  ; InitAsyncStub
    // 0x1307eb0: ldur            x0, [fp, #-0x18]
    // 0x1307eb4: r1 = LoadClassIdInstr(r0)
    //     0x1307eb4: ldur            x1, [x0, #-1]
    //     0x1307eb8: ubfx            x1, x1, #0xc, #0x14
    // 0x1307ebc: r16 = ""
    //     0x1307ebc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1307ec0: stp             x16, x0, [SP]
    // 0x1307ec4: mov             x0, x1
    // 0x1307ec8: mov             lr, x0
    // 0x1307ecc: ldr             lr, [x21, lr, lsl #3]
    // 0x1307ed0: blr             lr
    // 0x1307ed4: tbz             w0, #4, #0x130881c
    // 0x1307ed8: ldur            x0, [fp, #-0x10]
    // 0x1307edc: LoadField: r1 = r0->field_f
    //     0x1307edc: ldur            w1, [x0, #0xf]
    // 0x1307ee0: DecompressPointer r1
    //     0x1307ee0: add             x1, x1, HEAP, lsl #32
    // 0x1307ee4: r0 = controller()
    //     0x1307ee4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1307ee8: LoadField: r1 = r0->field_63
    //     0x1307ee8: ldur            w1, [x0, #0x63]
    // 0x1307eec: DecompressPointer r1
    //     0x1307eec: add             x1, x1, HEAP, lsl #32
    // 0x1307ef0: cmp             w1, NULL
    // 0x1307ef4: b.ne            #0x1307f00
    // 0x1307ef8: r0 = Null
    //     0x1307ef8: mov             x0, NULL
    // 0x1307efc: b               #0x1307f2c
    // 0x1307f00: LoadField: r0 = r1->field_23
    //     0x1307f00: ldur            w0, [x1, #0x23]
    // 0x1307f04: DecompressPointer r0
    //     0x1307f04: add             x0, x0, HEAP, lsl #32
    // 0x1307f08: cmp             w0, NULL
    // 0x1307f0c: b.ne            #0x1307f18
    // 0x1307f10: r0 = Null
    //     0x1307f10: mov             x0, NULL
    // 0x1307f14: b               #0x1307f2c
    // 0x1307f18: LoadField: r1 = r0->field_b
    //     0x1307f18: ldur            w1, [x0, #0xb]
    // 0x1307f1c: cbnz            w1, #0x1307f28
    // 0x1307f20: r0 = false
    //     0x1307f20: add             x0, NULL, #0x30  ; false
    // 0x1307f24: b               #0x1307f2c
    // 0x1307f28: r0 = true
    //     0x1307f28: add             x0, NULL, #0x20  ; true
    // 0x1307f2c: cmp             w0, NULL
    // 0x1307f30: b.eq            #0x1308000
    // 0x1307f34: tbnz            w0, #4, #0x1308000
    // 0x1307f38: ldur            x0, [fp, #-0x10]
    // 0x1307f3c: LoadField: r1 = r0->field_f
    //     0x1307f3c: ldur            w1, [x0, #0xf]
    // 0x1307f40: DecompressPointer r1
    //     0x1307f40: add             x1, x1, HEAP, lsl #32
    // 0x1307f44: r0 = controller()
    //     0x1307f44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1307f48: LoadField: r2 = r0->field_9b
    //     0x1307f48: ldur            w2, [x0, #0x9b]
    // 0x1307f4c: DecompressPointer r2
    //     0x1307f4c: add             x2, x2, HEAP, lsl #32
    // 0x1307f50: ldur            x0, [fp, #-0x10]
    // 0x1307f54: stur            x2, [fp, #-0x18]
    // 0x1307f58: LoadField: r1 = r0->field_f
    //     0x1307f58: ldur            w1, [x0, #0xf]
    // 0x1307f5c: DecompressPointer r1
    //     0x1307f5c: add             x1, x1, HEAP, lsl #32
    // 0x1307f60: r0 = controller()
    //     0x1307f60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1307f64: LoadField: r1 = r0->field_63
    //     0x1307f64: ldur            w1, [x0, #0x63]
    // 0x1307f68: DecompressPointer r1
    //     0x1307f68: add             x1, x1, HEAP, lsl #32
    // 0x1307f6c: cmp             w1, NULL
    // 0x1307f70: b.ne            #0x1307f80
    // 0x1307f74: r0 = ProductCustomisation()
    //     0x1307f74: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0x1307f78: mov             x2, x0
    // 0x1307f7c: b               #0x1307f84
    // 0x1307f80: mov             x2, x1
    // 0x1307f84: ldur            x0, [fp, #-0x18]
    // 0x1307f88: stur            x2, [fp, #-0x28]
    // 0x1307f8c: LoadField: r1 = r0->field_b
    //     0x1307f8c: ldur            w1, [x0, #0xb]
    // 0x1307f90: LoadField: r3 = r0->field_f
    //     0x1307f90: ldur            w3, [x0, #0xf]
    // 0x1307f94: DecompressPointer r3
    //     0x1307f94: add             x3, x3, HEAP, lsl #32
    // 0x1307f98: LoadField: r4 = r3->field_b
    //     0x1307f98: ldur            w4, [x3, #0xb]
    // 0x1307f9c: r3 = LoadInt32Instr(r1)
    //     0x1307f9c: sbfx            x3, x1, #1, #0x1f
    // 0x1307fa0: stur            x3, [fp, #-0x20]
    // 0x1307fa4: r1 = LoadInt32Instr(r4)
    //     0x1307fa4: sbfx            x1, x4, #1, #0x1f
    // 0x1307fa8: cmp             x3, x1
    // 0x1307fac: b.ne            #0x1307fb8
    // 0x1307fb0: mov             x1, x0
    // 0x1307fb4: r0 = _growToNextCapacity()
    //     0x1307fb4: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x1307fb8: ldur            x0, [fp, #-0x18]
    // 0x1307fbc: ldur            x2, [fp, #-0x20]
    // 0x1307fc0: add             x1, x2, #1
    // 0x1307fc4: lsl             x3, x1, #1
    // 0x1307fc8: StoreField: r0->field_b = r3
    //     0x1307fc8: stur            w3, [x0, #0xb]
    // 0x1307fcc: LoadField: r1 = r0->field_f
    //     0x1307fcc: ldur            w1, [x0, #0xf]
    // 0x1307fd0: DecompressPointer r1
    //     0x1307fd0: add             x1, x1, HEAP, lsl #32
    // 0x1307fd4: ldur            x0, [fp, #-0x28]
    // 0x1307fd8: ArrayStore: r1[r2] = r0  ; List_4
    //     0x1307fd8: add             x25, x1, x2, lsl #2
    //     0x1307fdc: add             x25, x25, #0xf
    //     0x1307fe0: str             w0, [x25]
    //     0x1307fe4: tbz             w0, #0, #0x1308000
    //     0x1307fe8: ldurb           w16, [x1, #-1]
    //     0x1307fec: ldurb           w17, [x0, #-1]
    //     0x1307ff0: and             x16, x17, x16, lsr #2
    //     0x1307ff4: tst             x16, HEAP, lsr #32
    //     0x1307ff8: b.eq            #0x1308000
    //     0x1307ffc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308000: ldur            x0, [fp, #-0x10]
    // 0x1308004: LoadField: r1 = r0->field_f
    //     0x1308004: ldur            w1, [x0, #0xf]
    // 0x1308008: DecompressPointer r1
    //     0x1308008: add             x1, x1, HEAP, lsl #32
    // 0x130800c: r0 = controller()
    //     0x130800c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308010: LoadField: r1 = r0->field_8f
    //     0x1308010: ldur            w1, [x0, #0x8f]
    // 0x1308014: DecompressPointer r1
    //     0x1308014: add             x1, x1, HEAP, lsl #32
    // 0x1308018: r0 = value()
    //     0x1308018: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130801c: mov             x2, x0
    // 0x1308020: ldur            x0, [fp, #-0x10]
    // 0x1308024: stur            x2, [fp, #-0x18]
    // 0x1308028: LoadField: r1 = r0->field_f
    //     0x1308028: ldur            w1, [x0, #0xf]
    // 0x130802c: DecompressPointer r1
    //     0x130802c: add             x1, x1, HEAP, lsl #32
    // 0x1308030: r0 = controller()
    //     0x1308030: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308034: LoadField: r1 = r0->field_93
    //     0x1308034: ldur            w1, [x0, #0x93]
    // 0x1308038: DecompressPointer r1
    //     0x1308038: add             x1, x1, HEAP, lsl #32
    // 0x130803c: r0 = value()
    //     0x130803c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308040: mov             x2, x0
    // 0x1308044: ldur            x0, [fp, #-0x10]
    // 0x1308048: stur            x2, [fp, #-0x28]
    // 0x130804c: LoadField: r1 = r0->field_f
    //     0x130804c: ldur            w1, [x0, #0xf]
    // 0x1308050: DecompressPointer r1
    //     0x1308050: add             x1, x1, HEAP, lsl #32
    // 0x1308054: r0 = controller()
    //     0x1308054: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308058: LoadField: r1 = r0->field_ab
    //     0x1308058: ldur            w1, [x0, #0xab]
    // 0x130805c: DecompressPointer r1
    //     0x130805c: add             x1, x1, HEAP, lsl #32
    // 0x1308060: r0 = value()
    //     0x1308060: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308064: mov             x2, x0
    // 0x1308068: ldur            x0, [fp, #-0x10]
    // 0x130806c: stur            x2, [fp, #-0x30]
    // 0x1308070: LoadField: r1 = r0->field_f
    //     0x1308070: ldur            w1, [x0, #0xf]
    // 0x1308074: DecompressPointer r1
    //     0x1308074: add             x1, x1, HEAP, lsl #32
    // 0x1308078: r0 = controller()
    //     0x1308078: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130807c: LoadField: r2 = r0->field_9b
    //     0x130807c: ldur            w2, [x0, #0x9b]
    // 0x1308080: DecompressPointer r2
    //     0x1308080: add             x2, x2, HEAP, lsl #32
    // 0x1308084: ldur            x0, [fp, #-0x10]
    // 0x1308088: stur            x2, [fp, #-0x38]
    // 0x130808c: LoadField: r1 = r0->field_f
    //     0x130808c: ldur            w1, [x0, #0xf]
    // 0x1308090: DecompressPointer r1
    //     0x1308090: add             x1, x1, HEAP, lsl #32
    // 0x1308094: r0 = controller()
    //     0x1308094: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308098: LoadField: r1 = r0->field_97
    //     0x1308098: ldur            w1, [x0, #0x97]
    // 0x130809c: DecompressPointer r1
    //     0x130809c: add             x1, x1, HEAP, lsl #32
    // 0x13080a0: r0 = value()
    //     0x13080a0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13080a4: stur            x0, [fp, #-0x40]
    // 0x13080a8: r0 = CustomizedRequest()
    //     0x13080a8: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x13080ac: mov             x2, x0
    // 0x13080b0: ldur            x0, [fp, #-0x18]
    // 0x13080b4: stur            x2, [fp, #-0x48]
    // 0x13080b8: StoreField: r2->field_7 = r0
    //     0x13080b8: stur            w0, [x2, #7]
    // 0x13080bc: ldur            x0, [fp, #-0x28]
    // 0x13080c0: StoreField: r2->field_b = r0
    //     0x13080c0: stur            w0, [x2, #0xb]
    // 0x13080c4: ldur            x0, [fp, #-0x30]
    // 0x13080c8: StoreField: r2->field_f = r0
    //     0x13080c8: stur            w0, [x2, #0xf]
    // 0x13080cc: ldur            x0, [fp, #-0x38]
    // 0x13080d0: StoreField: r2->field_13 = r0
    //     0x13080d0: stur            w0, [x2, #0x13]
    // 0x13080d4: ldur            x0, [fp, #-0x40]
    // 0x13080d8: ArrayStore: r2[0] = r0  ; List_4
    //     0x13080d8: stur            w0, [x2, #0x17]
    // 0x13080dc: ldur            x0, [fp, #-0x10]
    // 0x13080e0: LoadField: r1 = r0->field_f
    //     0x13080e0: ldur            w1, [x0, #0xf]
    // 0x13080e4: DecompressPointer r1
    //     0x13080e4: add             x1, x1, HEAP, lsl #32
    // 0x13080e8: r0 = controller()
    //     0x13080e8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13080ec: LoadField: r1 = r0->field_5f
    //     0x13080ec: ldur            w1, [x0, #0x5f]
    // 0x13080f0: DecompressPointer r1
    //     0x13080f0: add             x1, x1, HEAP, lsl #32
    // 0x13080f4: r0 = value()
    //     0x13080f4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13080f8: LoadField: r1 = r0->field_1b
    //     0x13080f8: ldur            w1, [x0, #0x1b]
    // 0x13080fc: DecompressPointer r1
    //     0x13080fc: add             x1, x1, HEAP, lsl #32
    // 0x1308100: cmp             w1, NULL
    // 0x1308104: b.ne            #0x1308110
    // 0x1308108: r0 = Null
    //     0x1308108: mov             x0, NULL
    // 0x130810c: b               #0x1308128
    // 0x1308110: LoadField: r0 = r1->field_b
    //     0x1308110: ldur            w0, [x1, #0xb]
    // 0x1308114: cbz             w0, #0x1308120
    // 0x1308118: r1 = false
    //     0x1308118: add             x1, NULL, #0x30  ; false
    // 0x130811c: b               #0x1308124
    // 0x1308120: r1 = true
    //     0x1308120: add             x1, NULL, #0x20  ; true
    // 0x1308124: mov             x0, x1
    // 0x1308128: cmp             w0, NULL
    // 0x130812c: b.eq            #0x1308134
    // 0x1308130: tbz             w0, #4, #0x13081c0
    // 0x1308134: ldur            x0, [fp, #-0x10]
    // 0x1308138: LoadField: r1 = r0->field_f
    //     0x1308138: ldur            w1, [x0, #0xf]
    // 0x130813c: DecompressPointer r1
    //     0x130813c: add             x1, x1, HEAP, lsl #32
    // 0x1308140: r0 = controller()
    //     0x1308140: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308144: LoadField: r1 = r0->field_5f
    //     0x1308144: ldur            w1, [x0, #0x5f]
    // 0x1308148: DecompressPointer r1
    //     0x1308148: add             x1, x1, HEAP, lsl #32
    // 0x130814c: r0 = value()
    //     0x130814c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308150: LoadField: r1 = r0->field_1b
    //     0x1308150: ldur            w1, [x0, #0x1b]
    // 0x1308154: DecompressPointer r1
    //     0x1308154: add             x1, x1, HEAP, lsl #32
    // 0x1308158: cmp             w1, NULL
    // 0x130815c: b.ne            #0x1308168
    // 0x1308160: r0 = Null
    //     0x1308160: mov             x0, NULL
    // 0x1308164: b               #0x13081ac
    // 0x1308168: r0 = first()
    //     0x1308168: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0x130816c: cmp             w0, NULL
    // 0x1308170: b.ne            #0x130817c
    // 0x1308174: r0 = Null
    //     0x1308174: mov             x0, NULL
    // 0x1308178: b               #0x13081ac
    // 0x130817c: LoadField: r1 = r0->field_13
    //     0x130817c: ldur            w1, [x0, #0x13]
    // 0x1308180: DecompressPointer r1
    //     0x1308180: add             x1, x1, HEAP, lsl #32
    // 0x1308184: cmp             w1, NULL
    // 0x1308188: b.ne            #0x1308194
    // 0x130818c: r0 = Null
    //     0x130818c: mov             x0, NULL
    // 0x1308190: b               #0x13081ac
    // 0x1308194: LoadField: r0 = r1->field_7
    //     0x1308194: ldur            w0, [x1, #7]
    // 0x1308198: cbz             w0, #0x13081a4
    // 0x130819c: r1 = false
    //     0x130819c: add             x1, NULL, #0x30  ; false
    // 0x13081a0: b               #0x13081a8
    // 0x13081a4: r1 = true
    //     0x13081a4: add             x1, NULL, #0x20  ; true
    // 0x13081a8: mov             x0, x1
    // 0x13081ac: cmp             w0, NULL
    // 0x13081b0: b.ne            #0x13081bc
    // 0x13081b4: ldur            x1, [fp, #-0x10]
    // 0x13081b8: b               #0x13084f4
    // 0x13081bc: tbnz            w0, #4, #0x13084f0
    // 0x13081c0: ldur            x0, [fp, #-0x10]
    // 0x13081c4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13081c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13081c8: ldr             x0, [x0, #0x1c80]
    //     0x13081cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13081d0: cmp             w0, w16
    //     0x13081d4: b.ne            #0x13081e0
    //     0x13081d8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13081dc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13081e0: r1 = Null
    //     0x13081e0: mov             x1, NULL
    // 0x13081e4: r2 = 44
    //     0x13081e4: movz            x2, #0x2c
    // 0x13081e8: r0 = AllocateArray()
    //     0x13081e8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13081ec: stur            x0, [fp, #-0x18]
    // 0x13081f0: r16 = "couponCode"
    //     0x13081f0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x13081f4: ldr             x16, [x16, #0x310]
    // 0x13081f8: StoreField: r0->field_f = r16
    //     0x13081f8: stur            w16, [x0, #0xf]
    // 0x13081fc: r16 = ""
    //     0x13081fc: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1308200: StoreField: r0->field_13 = r16
    //     0x1308200: stur            w16, [x0, #0x13]
    // 0x1308204: r16 = "product_id"
    //     0x1308204: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x1308208: ldr             x16, [x16, #0x9b8]
    // 0x130820c: ArrayStore: r0[0] = r16  ; List_4
    //     0x130820c: stur            w16, [x0, #0x17]
    // 0x1308210: ldur            x2, [fp, #-0x10]
    // 0x1308214: LoadField: r1 = r2->field_f
    //     0x1308214: ldur            w1, [x2, #0xf]
    // 0x1308218: DecompressPointer r1
    //     0x1308218: add             x1, x1, HEAP, lsl #32
    // 0x130821c: r0 = controller()
    //     0x130821c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308220: LoadField: r1 = r0->field_8f
    //     0x1308220: ldur            w1, [x0, #0x8f]
    // 0x1308224: DecompressPointer r1
    //     0x1308224: add             x1, x1, HEAP, lsl #32
    // 0x1308228: r0 = value()
    //     0x1308228: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130822c: ldur            x1, [fp, #-0x18]
    // 0x1308230: ArrayStore: r1[3] = r0  ; List_4
    //     0x1308230: add             x25, x1, #0x1b
    //     0x1308234: str             w0, [x25]
    //     0x1308238: tbz             w0, #0, #0x1308254
    //     0x130823c: ldurb           w16, [x1, #-1]
    //     0x1308240: ldurb           w17, [x0, #-1]
    //     0x1308244: and             x16, x17, x16, lsr #2
    //     0x1308248: tst             x16, HEAP, lsr #32
    //     0x130824c: b.eq            #0x1308254
    //     0x1308250: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308254: ldur            x0, [fp, #-0x18]
    // 0x1308258: r16 = "sku_id"
    //     0x1308258: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130825c: ldr             x16, [x16, #0x498]
    // 0x1308260: StoreField: r0->field_1f = r16
    //     0x1308260: stur            w16, [x0, #0x1f]
    // 0x1308264: ldur            x2, [fp, #-0x10]
    // 0x1308268: LoadField: r1 = r2->field_f
    //     0x1308268: ldur            w1, [x2, #0xf]
    // 0x130826c: DecompressPointer r1
    //     0x130826c: add             x1, x1, HEAP, lsl #32
    // 0x1308270: r0 = controller()
    //     0x1308270: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308274: LoadField: r1 = r0->field_93
    //     0x1308274: ldur            w1, [x0, #0x93]
    // 0x1308278: DecompressPointer r1
    //     0x1308278: add             x1, x1, HEAP, lsl #32
    // 0x130827c: r0 = value()
    //     0x130827c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308280: ldur            x1, [fp, #-0x18]
    // 0x1308284: ArrayStore: r1[5] = r0  ; List_4
    //     0x1308284: add             x25, x1, #0x23
    //     0x1308288: str             w0, [x25]
    //     0x130828c: tbz             w0, #0, #0x13082a8
    //     0x1308290: ldurb           w16, [x1, #-1]
    //     0x1308294: ldurb           w17, [x0, #-1]
    //     0x1308298: and             x16, x17, x16, lsr #2
    //     0x130829c: tst             x16, HEAP, lsr #32
    //     0x13082a0: b.eq            #0x13082a8
    //     0x13082a4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13082a8: ldur            x0, [fp, #-0x18]
    // 0x13082ac: r16 = "coming_from"
    //     0x13082ac: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x13082b0: ldr             x16, [x16, #0x328]
    // 0x13082b4: StoreField: r0->field_27 = r16
    //     0x13082b4: stur            w16, [x0, #0x27]
    // 0x13082b8: r16 = "buyNow"
    //     0x13082b8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x13082bc: ldr             x16, [x16, #0x358]
    // 0x13082c0: StoreField: r0->field_2b = r16
    //     0x13082c0: stur            w16, [x0, #0x2b]
    // 0x13082c4: r16 = "quantity"
    //     0x13082c4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x13082c8: ldr             x16, [x16, #0x428]
    // 0x13082cc: StoreField: r0->field_2f = r16
    //     0x13082cc: stur            w16, [x0, #0x2f]
    // 0x13082d0: ldur            x2, [fp, #-0x10]
    // 0x13082d4: LoadField: r1 = r2->field_f
    //     0x13082d4: ldur            w1, [x2, #0xf]
    // 0x13082d8: DecompressPointer r1
    //     0x13082d8: add             x1, x1, HEAP, lsl #32
    // 0x13082dc: r0 = controller()
    //     0x13082dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13082e0: LoadField: r1 = r0->field_ab
    //     0x13082e0: ldur            w1, [x0, #0xab]
    // 0x13082e4: DecompressPointer r1
    //     0x13082e4: add             x1, x1, HEAP, lsl #32
    // 0x13082e8: r0 = value()
    //     0x13082e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13082ec: ldur            x1, [fp, #-0x18]
    // 0x13082f0: ArrayStore: r1[9] = r0  ; List_4
    //     0x13082f0: add             x25, x1, #0x33
    //     0x13082f4: str             w0, [x25]
    //     0x13082f8: tbz             w0, #0, #0x1308314
    //     0x13082fc: ldurb           w16, [x1, #-1]
    //     0x1308300: ldurb           w17, [x0, #-1]
    //     0x1308304: and             x16, x17, x16, lsr #2
    //     0x1308308: tst             x16, HEAP, lsr #32
    //     0x130830c: b.eq            #0x1308314
    //     0x1308310: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308314: ldur            x3, [fp, #-0x18]
    // 0x1308318: r16 = "previousScreenSource"
    //     0x1308318: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130831c: ldr             x16, [x16, #0x448]
    // 0x1308320: StoreField: r3->field_37 = r16
    //     0x1308320: stur            w16, [x3, #0x37]
    // 0x1308324: r16 = "product_page"
    //     0x1308324: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1308328: ldr             x16, [x16, #0x480]
    // 0x130832c: StoreField: r3->field_3b = r16
    //     0x130832c: stur            w16, [x3, #0x3b]
    // 0x1308330: r16 = "customization_request"
    //     0x1308330: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x1308334: ldr             x16, [x16, #0x2b8]
    // 0x1308338: StoreField: r3->field_3f = r16
    //     0x1308338: stur            w16, [x3, #0x3f]
    // 0x130833c: mov             x1, x3
    // 0x1308340: ldur            x0, [fp, #-0x48]
    // 0x1308344: ArrayStore: r1[13] = r0  ; List_4
    //     0x1308344: add             x25, x1, #0x43
    //     0x1308348: str             w0, [x25]
    //     0x130834c: tbz             w0, #0, #0x1308368
    //     0x1308350: ldurb           w16, [x1, #-1]
    //     0x1308354: ldurb           w17, [x0, #-1]
    //     0x1308358: and             x16, x17, x16, lsr #2
    //     0x130835c: tst             x16, HEAP, lsr #32
    //     0x1308360: b.eq            #0x1308368
    //     0x1308364: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308368: r16 = "customization_prize"
    //     0x1308368: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130836c: ldr             x16, [x16, #0x2e8]
    // 0x1308370: StoreField: r3->field_47 = r16
    //     0x1308370: stur            w16, [x3, #0x47]
    // 0x1308374: r1 = Null
    //     0x1308374: mov             x1, NULL
    // 0x1308378: r2 = 4
    //     0x1308378: movz            x2, #0x4
    // 0x130837c: r0 = AllocateArray()
    //     0x130837c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1308380: stur            x0, [fp, #-0x28]
    // 0x1308384: r16 = "₹"
    //     0x1308384: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x1308388: ldr             x16, [x16, #0x360]
    // 0x130838c: StoreField: r0->field_f = r16
    //     0x130838c: stur            w16, [x0, #0xf]
    // 0x1308390: r1 = Function '<anonymous closure>': static.
    //     0x1308390: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x1308394: ldr             x1, [x1, #0x1a0]
    // 0x1308398: r2 = Null
    //     0x1308398: mov             x2, NULL
    // 0x130839c: r0 = AllocateClosure()
    //     0x130839c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13083a0: mov             x3, x0
    // 0x13083a4: r1 = Null
    //     0x13083a4: mov             x1, NULL
    // 0x13083a8: r2 = Null
    //     0x13083a8: mov             x2, NULL
    // 0x13083ac: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x13083ac: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x13083b0: r0 = NumberFormat._forPattern()
    //     0x13083b0: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x13083b4: mov             x2, x0
    // 0x13083b8: ldur            x0, [fp, #-0x10]
    // 0x13083bc: stur            x2, [fp, #-0x30]
    // 0x13083c0: LoadField: r1 = r0->field_f
    //     0x13083c0: ldur            w1, [x0, #0xf]
    // 0x13083c4: DecompressPointer r1
    //     0x13083c4: add             x1, x1, HEAP, lsl #32
    // 0x13083c8: r0 = controller()
    //     0x13083c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13083cc: LoadField: r1 = r0->field_67
    //     0x13083cc: ldur            w1, [x0, #0x67]
    // 0x13083d0: DecompressPointer r1
    //     0x13083d0: add             x1, x1, HEAP, lsl #32
    // 0x13083d4: r0 = value()
    //     0x13083d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13083d8: ldur            x1, [fp, #-0x30]
    // 0x13083dc: mov             x2, x0
    // 0x13083e0: r0 = format()
    //     0x13083e0: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x13083e4: ldur            x1, [fp, #-0x28]
    // 0x13083e8: ArrayStore: r1[1] = r0  ; List_4
    //     0x13083e8: add             x25, x1, #0x13
    //     0x13083ec: str             w0, [x25]
    //     0x13083f0: tbz             w0, #0, #0x130840c
    //     0x13083f4: ldurb           w16, [x1, #-1]
    //     0x13083f8: ldurb           w17, [x0, #-1]
    //     0x13083fc: and             x16, x17, x16, lsr #2
    //     0x1308400: tst             x16, HEAP, lsr #32
    //     0x1308404: b.eq            #0x130840c
    //     0x1308408: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130840c: ldur            x16, [fp, #-0x28]
    // 0x1308410: str             x16, [SP]
    // 0x1308414: r0 = _interpolate()
    //     0x1308414: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1308418: ldur            x1, [fp, #-0x18]
    // 0x130841c: ArrayStore: r1[15] = r0  ; List_4
    //     0x130841c: add             x25, x1, #0x4b
    //     0x1308420: str             w0, [x25]
    //     0x1308424: tbz             w0, #0, #0x1308440
    //     0x1308428: ldurb           w16, [x1, #-1]
    //     0x130842c: ldurb           w17, [x0, #-1]
    //     0x1308430: and             x16, x17, x16, lsr #2
    //     0x1308434: tst             x16, HEAP, lsr #32
    //     0x1308438: b.eq            #0x1308440
    //     0x130843c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308440: ldur            x0, [fp, #-0x18]
    // 0x1308444: r16 = "is_skipped_address"
    //     0x1308444: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x1308448: ldr             x16, [x16, #0xb80]
    // 0x130844c: StoreField: r0->field_4f = r16
    //     0x130844c: stur            w16, [x0, #0x4f]
    // 0x1308450: r16 = true
    //     0x1308450: add             x16, NULL, #0x20  ; true
    // 0x1308454: StoreField: r0->field_53 = r16
    //     0x1308454: stur            w16, [x0, #0x53]
    // 0x1308458: r16 = "checkout_id"
    //     0x1308458: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130845c: ldr             x16, [x16, #0xb88]
    // 0x1308460: StoreField: r0->field_57 = r16
    //     0x1308460: stur            w16, [x0, #0x57]
    // 0x1308464: r16 = ""
    //     0x1308464: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1308468: StoreField: r0->field_5b = r16
    //     0x1308468: stur            w16, [x0, #0x5b]
    // 0x130846c: r16 = "user_data"
    //     0x130846c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x1308470: ldr             x16, [x16, #0x58]
    // 0x1308474: StoreField: r0->field_5f = r16
    //     0x1308474: stur            w16, [x0, #0x5f]
    // 0x1308478: ldur            x1, [fp, #-0x10]
    // 0x130847c: LoadField: r2 = r1->field_f
    //     0x130847c: ldur            w2, [x1, #0xf]
    // 0x1308480: DecompressPointer r2
    //     0x1308480: add             x2, x2, HEAP, lsl #32
    // 0x1308484: mov             x1, x2
    // 0x1308488: r0 = controller()
    //     0x1308488: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130848c: LoadField: r1 = r0->field_5f
    //     0x130848c: ldur            w1, [x0, #0x5f]
    // 0x1308490: DecompressPointer r1
    //     0x1308490: add             x1, x1, HEAP, lsl #32
    // 0x1308494: r0 = value()
    //     0x1308494: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308498: ldur            x1, [fp, #-0x18]
    // 0x130849c: ArrayStore: r1[21] = r0  ; List_4
    //     0x130849c: add             x25, x1, #0x63
    //     0x13084a0: str             w0, [x25]
    //     0x13084a4: tbz             w0, #0, #0x13084c0
    //     0x13084a8: ldurb           w16, [x1, #-1]
    //     0x13084ac: ldurb           w17, [x0, #-1]
    //     0x13084b0: and             x16, x17, x16, lsr #2
    //     0x13084b4: tst             x16, HEAP, lsr #32
    //     0x13084b8: b.eq            #0x13084c0
    //     0x13084bc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13084c0: r16 = <String, dynamic>
    //     0x13084c0: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x13084c4: ldur            lr, [fp, #-0x18]
    // 0x13084c8: stp             lr, x16, [SP]
    // 0x13084cc: r0 = Map._fromLiteral()
    //     0x13084cc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13084d0: r16 = "/checkout_request_address_page"
    //     0x13084d0: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0x13084d4: ldr             x16, [x16, #0x9e8]
    // 0x13084d8: stp             x16, NULL, [SP, #8]
    // 0x13084dc: str             x0, [SP]
    // 0x13084e0: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x13084e0: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x13084e4: ldr             x4, [x4, #0x438]
    // 0x13084e8: r0 = GetNavigation.toNamed()
    //     0x13084e8: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x13084ec: b               #0x1308bac
    // 0x13084f0: ldur            x1, [fp, #-0x10]
    // 0x13084f4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13084f4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13084f8: ldr             x0, [x0, #0x1c80]
    //     0x13084fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1308500: cmp             w0, w16
    //     0x1308504: b.ne            #0x1308510
    //     0x1308508: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130850c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1308510: r1 = Null
    //     0x1308510: mov             x1, NULL
    // 0x1308514: r2 = 44
    //     0x1308514: movz            x2, #0x2c
    // 0x1308518: r0 = AllocateArray()
    //     0x1308518: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130851c: stur            x0, [fp, #-0x18]
    // 0x1308520: r16 = "couponCode"
    //     0x1308520: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x1308524: ldr             x16, [x16, #0x310]
    // 0x1308528: StoreField: r0->field_f = r16
    //     0x1308528: stur            w16, [x0, #0xf]
    // 0x130852c: r16 = ""
    //     0x130852c: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1308530: StoreField: r0->field_13 = r16
    //     0x1308530: stur            w16, [x0, #0x13]
    // 0x1308534: r16 = "product_id"
    //     0x1308534: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x1308538: ldr             x16, [x16, #0x9b8]
    // 0x130853c: ArrayStore: r0[0] = r16  ; List_4
    //     0x130853c: stur            w16, [x0, #0x17]
    // 0x1308540: ldur            x2, [fp, #-0x10]
    // 0x1308544: LoadField: r1 = r2->field_f
    //     0x1308544: ldur            w1, [x2, #0xf]
    // 0x1308548: DecompressPointer r1
    //     0x1308548: add             x1, x1, HEAP, lsl #32
    // 0x130854c: r0 = controller()
    //     0x130854c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308550: LoadField: r1 = r0->field_8f
    //     0x1308550: ldur            w1, [x0, #0x8f]
    // 0x1308554: DecompressPointer r1
    //     0x1308554: add             x1, x1, HEAP, lsl #32
    // 0x1308558: r0 = value()
    //     0x1308558: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130855c: ldur            x1, [fp, #-0x18]
    // 0x1308560: ArrayStore: r1[3] = r0  ; List_4
    //     0x1308560: add             x25, x1, #0x1b
    //     0x1308564: str             w0, [x25]
    //     0x1308568: tbz             w0, #0, #0x1308584
    //     0x130856c: ldurb           w16, [x1, #-1]
    //     0x1308570: ldurb           w17, [x0, #-1]
    //     0x1308574: and             x16, x17, x16, lsr #2
    //     0x1308578: tst             x16, HEAP, lsr #32
    //     0x130857c: b.eq            #0x1308584
    //     0x1308580: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308584: ldur            x0, [fp, #-0x18]
    // 0x1308588: r16 = "sku_id"
    //     0x1308588: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130858c: ldr             x16, [x16, #0x498]
    // 0x1308590: StoreField: r0->field_1f = r16
    //     0x1308590: stur            w16, [x0, #0x1f]
    // 0x1308594: ldur            x2, [fp, #-0x10]
    // 0x1308598: LoadField: r1 = r2->field_f
    //     0x1308598: ldur            w1, [x2, #0xf]
    // 0x130859c: DecompressPointer r1
    //     0x130859c: add             x1, x1, HEAP, lsl #32
    // 0x13085a0: r0 = controller()
    //     0x13085a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13085a4: LoadField: r1 = r0->field_93
    //     0x13085a4: ldur            w1, [x0, #0x93]
    // 0x13085a8: DecompressPointer r1
    //     0x13085a8: add             x1, x1, HEAP, lsl #32
    // 0x13085ac: r0 = value()
    //     0x13085ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13085b0: ldur            x1, [fp, #-0x18]
    // 0x13085b4: ArrayStore: r1[5] = r0  ; List_4
    //     0x13085b4: add             x25, x1, #0x23
    //     0x13085b8: str             w0, [x25]
    //     0x13085bc: tbz             w0, #0, #0x13085d8
    //     0x13085c0: ldurb           w16, [x1, #-1]
    //     0x13085c4: ldurb           w17, [x0, #-1]
    //     0x13085c8: and             x16, x17, x16, lsr #2
    //     0x13085cc: tst             x16, HEAP, lsr #32
    //     0x13085d0: b.eq            #0x13085d8
    //     0x13085d4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13085d8: ldur            x0, [fp, #-0x18]
    // 0x13085dc: r16 = "coming_from"
    //     0x13085dc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x13085e0: ldr             x16, [x16, #0x328]
    // 0x13085e4: StoreField: r0->field_27 = r16
    //     0x13085e4: stur            w16, [x0, #0x27]
    // 0x13085e8: r16 = "buyNow"
    //     0x13085e8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x13085ec: ldr             x16, [x16, #0x358]
    // 0x13085f0: StoreField: r0->field_2b = r16
    //     0x13085f0: stur            w16, [x0, #0x2b]
    // 0x13085f4: r16 = "quantity"
    //     0x13085f4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x13085f8: ldr             x16, [x16, #0x428]
    // 0x13085fc: StoreField: r0->field_2f = r16
    //     0x13085fc: stur            w16, [x0, #0x2f]
    // 0x1308600: ldur            x2, [fp, #-0x10]
    // 0x1308604: LoadField: r1 = r2->field_f
    //     0x1308604: ldur            w1, [x2, #0xf]
    // 0x1308608: DecompressPointer r1
    //     0x1308608: add             x1, x1, HEAP, lsl #32
    // 0x130860c: r0 = controller()
    //     0x130860c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308610: LoadField: r1 = r0->field_ab
    //     0x1308610: ldur            w1, [x0, #0xab]
    // 0x1308614: DecompressPointer r1
    //     0x1308614: add             x1, x1, HEAP, lsl #32
    // 0x1308618: r0 = value()
    //     0x1308618: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130861c: ldur            x1, [fp, #-0x18]
    // 0x1308620: ArrayStore: r1[9] = r0  ; List_4
    //     0x1308620: add             x25, x1, #0x33
    //     0x1308624: str             w0, [x25]
    //     0x1308628: tbz             w0, #0, #0x1308644
    //     0x130862c: ldurb           w16, [x1, #-1]
    //     0x1308630: ldurb           w17, [x0, #-1]
    //     0x1308634: and             x16, x17, x16, lsr #2
    //     0x1308638: tst             x16, HEAP, lsr #32
    //     0x130863c: b.eq            #0x1308644
    //     0x1308640: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308644: ldur            x3, [fp, #-0x18]
    // 0x1308648: r16 = "previousScreenSource"
    //     0x1308648: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130864c: ldr             x16, [x16, #0x448]
    // 0x1308650: StoreField: r3->field_37 = r16
    //     0x1308650: stur            w16, [x3, #0x37]
    // 0x1308654: r16 = "product_page"
    //     0x1308654: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1308658: ldr             x16, [x16, #0x480]
    // 0x130865c: StoreField: r3->field_3b = r16
    //     0x130865c: stur            w16, [x3, #0x3b]
    // 0x1308660: r16 = "customization_request"
    //     0x1308660: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x1308664: ldr             x16, [x16, #0x2b8]
    // 0x1308668: StoreField: r3->field_3f = r16
    //     0x1308668: stur            w16, [x3, #0x3f]
    // 0x130866c: mov             x1, x3
    // 0x1308670: ldur            x0, [fp, #-0x48]
    // 0x1308674: ArrayStore: r1[13] = r0  ; List_4
    //     0x1308674: add             x25, x1, #0x43
    //     0x1308678: str             w0, [x25]
    //     0x130867c: tbz             w0, #0, #0x1308698
    //     0x1308680: ldurb           w16, [x1, #-1]
    //     0x1308684: ldurb           w17, [x0, #-1]
    //     0x1308688: and             x16, x17, x16, lsr #2
    //     0x130868c: tst             x16, HEAP, lsr #32
    //     0x1308690: b.eq            #0x1308698
    //     0x1308694: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308698: r16 = "customization_prize"
    //     0x1308698: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130869c: ldr             x16, [x16, #0x2e8]
    // 0x13086a0: StoreField: r3->field_47 = r16
    //     0x13086a0: stur            w16, [x3, #0x47]
    // 0x13086a4: r1 = Null
    //     0x13086a4: mov             x1, NULL
    // 0x13086a8: r2 = 4
    //     0x13086a8: movz            x2, #0x4
    // 0x13086ac: r0 = AllocateArray()
    //     0x13086ac: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13086b0: stur            x0, [fp, #-0x28]
    // 0x13086b4: r16 = "₹"
    //     0x13086b4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x13086b8: ldr             x16, [x16, #0x360]
    // 0x13086bc: StoreField: r0->field_f = r16
    //     0x13086bc: stur            w16, [x0, #0xf]
    // 0x13086c0: r1 = Function '<anonymous closure>': static.
    //     0x13086c0: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x13086c4: ldr             x1, [x1, #0x1a0]
    // 0x13086c8: r2 = Null
    //     0x13086c8: mov             x2, NULL
    // 0x13086cc: r0 = AllocateClosure()
    //     0x13086cc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13086d0: mov             x3, x0
    // 0x13086d4: r1 = Null
    //     0x13086d4: mov             x1, NULL
    // 0x13086d8: r2 = Null
    //     0x13086d8: mov             x2, NULL
    // 0x13086dc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x13086dc: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x13086e0: r0 = NumberFormat._forPattern()
    //     0x13086e0: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x13086e4: mov             x2, x0
    // 0x13086e8: ldur            x0, [fp, #-0x10]
    // 0x13086ec: stur            x2, [fp, #-0x30]
    // 0x13086f0: LoadField: r1 = r0->field_f
    //     0x13086f0: ldur            w1, [x0, #0xf]
    // 0x13086f4: DecompressPointer r1
    //     0x13086f4: add             x1, x1, HEAP, lsl #32
    // 0x13086f8: r0 = controller()
    //     0x13086f8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13086fc: LoadField: r1 = r0->field_67
    //     0x13086fc: ldur            w1, [x0, #0x67]
    // 0x1308700: DecompressPointer r1
    //     0x1308700: add             x1, x1, HEAP, lsl #32
    // 0x1308704: r0 = value()
    //     0x1308704: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308708: ldur            x1, [fp, #-0x30]
    // 0x130870c: mov             x2, x0
    // 0x1308710: r0 = format()
    //     0x1308710: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x1308714: ldur            x1, [fp, #-0x28]
    // 0x1308718: ArrayStore: r1[1] = r0  ; List_4
    //     0x1308718: add             x25, x1, #0x13
    //     0x130871c: str             w0, [x25]
    //     0x1308720: tbz             w0, #0, #0x130873c
    //     0x1308724: ldurb           w16, [x1, #-1]
    //     0x1308728: ldurb           w17, [x0, #-1]
    //     0x130872c: and             x16, x17, x16, lsr #2
    //     0x1308730: tst             x16, HEAP, lsr #32
    //     0x1308734: b.eq            #0x130873c
    //     0x1308738: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130873c: ldur            x16, [fp, #-0x28]
    // 0x1308740: str             x16, [SP]
    // 0x1308744: r0 = _interpolate()
    //     0x1308744: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1308748: ldur            x1, [fp, #-0x18]
    // 0x130874c: ArrayStore: r1[15] = r0  ; List_4
    //     0x130874c: add             x25, x1, #0x4b
    //     0x1308750: str             w0, [x25]
    //     0x1308754: tbz             w0, #0, #0x1308770
    //     0x1308758: ldurb           w16, [x1, #-1]
    //     0x130875c: ldurb           w17, [x0, #-1]
    //     0x1308760: and             x16, x17, x16, lsr #2
    //     0x1308764: tst             x16, HEAP, lsr #32
    //     0x1308768: b.eq            #0x1308770
    //     0x130876c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308770: ldur            x0, [fp, #-0x18]
    // 0x1308774: r16 = "is_skipped_address"
    //     0x1308774: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x1308778: ldr             x16, [x16, #0xb80]
    // 0x130877c: StoreField: r0->field_4f = r16
    //     0x130877c: stur            w16, [x0, #0x4f]
    // 0x1308780: r16 = true
    //     0x1308780: add             x16, NULL, #0x20  ; true
    // 0x1308784: StoreField: r0->field_53 = r16
    //     0x1308784: stur            w16, [x0, #0x53]
    // 0x1308788: r16 = "checkout_id"
    //     0x1308788: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130878c: ldr             x16, [x16, #0xb88]
    // 0x1308790: StoreField: r0->field_57 = r16
    //     0x1308790: stur            w16, [x0, #0x57]
    // 0x1308794: r16 = ""
    //     0x1308794: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x1308798: StoreField: r0->field_5b = r16
    //     0x1308798: stur            w16, [x0, #0x5b]
    // 0x130879c: r16 = "user_data"
    //     0x130879c: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x13087a0: ldr             x16, [x16, #0x58]
    // 0x13087a4: StoreField: r0->field_5f = r16
    //     0x13087a4: stur            w16, [x0, #0x5f]
    // 0x13087a8: ldur            x2, [fp, #-0x10]
    // 0x13087ac: LoadField: r1 = r2->field_f
    //     0x13087ac: ldur            w1, [x2, #0xf]
    // 0x13087b0: DecompressPointer r1
    //     0x13087b0: add             x1, x1, HEAP, lsl #32
    // 0x13087b4: r0 = controller()
    //     0x13087b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13087b8: LoadField: r1 = r0->field_5f
    //     0x13087b8: ldur            w1, [x0, #0x5f]
    // 0x13087bc: DecompressPointer r1
    //     0x13087bc: add             x1, x1, HEAP, lsl #32
    // 0x13087c0: r0 = value()
    //     0x13087c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13087c4: ldur            x1, [fp, #-0x18]
    // 0x13087c8: ArrayStore: r1[21] = r0  ; List_4
    //     0x13087c8: add             x25, x1, #0x63
    //     0x13087cc: str             w0, [x25]
    //     0x13087d0: tbz             w0, #0, #0x13087ec
    //     0x13087d4: ldurb           w16, [x1, #-1]
    //     0x13087d8: ldurb           w17, [x0, #-1]
    //     0x13087dc: and             x16, x17, x16, lsr #2
    //     0x13087e0: tst             x16, HEAP, lsr #32
    //     0x13087e4: b.eq            #0x13087ec
    //     0x13087e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13087ec: r16 = <String, dynamic>
    //     0x13087ec: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x13087f0: ldur            lr, [fp, #-0x18]
    // 0x13087f4: stp             lr, x16, [SP]
    // 0x13087f8: r0 = Map._fromLiteral()
    //     0x13087f8: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13087fc: r16 = "/checkout_order_summary_page"
    //     0x13087fc: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0x1308800: ldr             x16, [x16, #0x9d8]
    // 0x1308804: stp             x16, NULL, [SP, #8]
    // 0x1308808: str             x0, [SP]
    // 0x130880c: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130880c: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x1308810: ldr             x4, [x4, #0x438]
    // 0x1308814: r0 = GetNavigation.toNamed()
    //     0x1308814: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1308818: b               #0x1308bac
    // 0x130881c: ldur            x2, [fp, #-0x10]
    // 0x1308820: LoadField: r1 = r2->field_f
    //     0x1308820: ldur            w1, [x2, #0xf]
    // 0x1308824: DecompressPointer r1
    //     0x1308824: add             x1, x1, HEAP, lsl #32
    // 0x1308828: r0 = controller()
    //     0x1308828: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130882c: LoadField: r1 = r0->field_8f
    //     0x130882c: ldur            w1, [x0, #0x8f]
    // 0x1308830: DecompressPointer r1
    //     0x1308830: add             x1, x1, HEAP, lsl #32
    // 0x1308834: r0 = value()
    //     0x1308834: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308838: mov             x2, x0
    // 0x130883c: ldur            x0, [fp, #-0x10]
    // 0x1308840: stur            x2, [fp, #-0x18]
    // 0x1308844: LoadField: r1 = r0->field_f
    //     0x1308844: ldur            w1, [x0, #0xf]
    // 0x1308848: DecompressPointer r1
    //     0x1308848: add             x1, x1, HEAP, lsl #32
    // 0x130884c: r0 = controller()
    //     0x130884c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308850: LoadField: r1 = r0->field_93
    //     0x1308850: ldur            w1, [x0, #0x93]
    // 0x1308854: DecompressPointer r1
    //     0x1308854: add             x1, x1, HEAP, lsl #32
    // 0x1308858: r0 = value()
    //     0x1308858: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130885c: mov             x2, x0
    // 0x1308860: ldur            x0, [fp, #-0x10]
    // 0x1308864: stur            x2, [fp, #-0x28]
    // 0x1308868: LoadField: r1 = r0->field_f
    //     0x1308868: ldur            w1, [x0, #0xf]
    // 0x130886c: DecompressPointer r1
    //     0x130886c: add             x1, x1, HEAP, lsl #32
    // 0x1308870: r0 = controller()
    //     0x1308870: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308874: LoadField: r1 = r0->field_ab
    //     0x1308874: ldur            w1, [x0, #0xab]
    // 0x1308878: DecompressPointer r1
    //     0x1308878: add             x1, x1, HEAP, lsl #32
    // 0x130887c: r0 = value()
    //     0x130887c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308880: mov             x2, x0
    // 0x1308884: ldur            x0, [fp, #-0x10]
    // 0x1308888: stur            x2, [fp, #-0x30]
    // 0x130888c: LoadField: r1 = r0->field_f
    //     0x130888c: ldur            w1, [x0, #0xf]
    // 0x1308890: DecompressPointer r1
    //     0x1308890: add             x1, x1, HEAP, lsl #32
    // 0x1308894: r0 = controller()
    //     0x1308894: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308898: LoadField: r2 = r0->field_9b
    //     0x1308898: ldur            w2, [x0, #0x9b]
    // 0x130889c: DecompressPointer r2
    //     0x130889c: add             x2, x2, HEAP, lsl #32
    // 0x13088a0: ldur            x0, [fp, #-0x10]
    // 0x13088a4: stur            x2, [fp, #-0x38]
    // 0x13088a8: LoadField: r1 = r0->field_f
    //     0x13088a8: ldur            w1, [x0, #0xf]
    // 0x13088ac: DecompressPointer r1
    //     0x13088ac: add             x1, x1, HEAP, lsl #32
    // 0x13088b0: r0 = controller()
    //     0x13088b0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13088b4: LoadField: r1 = r0->field_97
    //     0x13088b4: ldur            w1, [x0, #0x97]
    // 0x13088b8: DecompressPointer r1
    //     0x13088b8: add             x1, x1, HEAP, lsl #32
    // 0x13088bc: r0 = value()
    //     0x13088bc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13088c0: stur            x0, [fp, #-0x40]
    // 0x13088c4: r0 = CustomizedRequest()
    //     0x13088c4: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x13088c8: mov             x1, x0
    // 0x13088cc: ldur            x0, [fp, #-0x18]
    // 0x13088d0: stur            x1, [fp, #-0x48]
    // 0x13088d4: StoreField: r1->field_7 = r0
    //     0x13088d4: stur            w0, [x1, #7]
    // 0x13088d8: ldur            x0, [fp, #-0x28]
    // 0x13088dc: StoreField: r1->field_b = r0
    //     0x13088dc: stur            w0, [x1, #0xb]
    // 0x13088e0: ldur            x0, [fp, #-0x30]
    // 0x13088e4: StoreField: r1->field_f = r0
    //     0x13088e4: stur            w0, [x1, #0xf]
    // 0x13088e8: ldur            x0, [fp, #-0x38]
    // 0x13088ec: StoreField: r1->field_13 = r0
    //     0x13088ec: stur            w0, [x1, #0x13]
    // 0x13088f0: ldur            x0, [fp, #-0x40]
    // 0x13088f4: ArrayStore: r1[0] = r0  ; List_4
    //     0x13088f4: stur            w0, [x1, #0x17]
    // 0x13088f8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13088f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13088fc: ldr             x0, [x0, #0x1c80]
    //     0x1308900: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1308904: cmp             w0, w16
    //     0x1308908: b.ne            #0x1308914
    //     0x130890c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1308910: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1308914: r1 = Null
    //     0x1308914: mov             x1, NULL
    // 0x1308918: r2 = 32
    //     0x1308918: movz            x2, #0x20
    // 0x130891c: r0 = AllocateArray()
    //     0x130891c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1308920: stur            x0, [fp, #-0x18]
    // 0x1308924: r16 = "previousScreenSource"
    //     0x1308924: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x1308928: ldr             x16, [x16, #0x448]
    // 0x130892c: StoreField: r0->field_f = r16
    //     0x130892c: stur            w16, [x0, #0xf]
    // 0x1308930: r16 = "product_page"
    //     0x1308930: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x1308934: ldr             x16, [x16, #0x480]
    // 0x1308938: StoreField: r0->field_13 = r16
    //     0x1308938: stur            w16, [x0, #0x13]
    // 0x130893c: r16 = "product_id"
    //     0x130893c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x1308940: ldr             x16, [x16, #0x9b8]
    // 0x1308944: ArrayStore: r0[0] = r16  ; List_4
    //     0x1308944: stur            w16, [x0, #0x17]
    // 0x1308948: ldur            x2, [fp, #-0x10]
    // 0x130894c: LoadField: r1 = r2->field_f
    //     0x130894c: ldur            w1, [x2, #0xf]
    // 0x1308950: DecompressPointer r1
    //     0x1308950: add             x1, x1, HEAP, lsl #32
    // 0x1308954: r0 = controller()
    //     0x1308954: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308958: LoadField: r1 = r0->field_8f
    //     0x1308958: ldur            w1, [x0, #0x8f]
    // 0x130895c: DecompressPointer r1
    //     0x130895c: add             x1, x1, HEAP, lsl #32
    // 0x1308960: r0 = value()
    //     0x1308960: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308964: ldur            x1, [fp, #-0x18]
    // 0x1308968: ArrayStore: r1[3] = r0  ; List_4
    //     0x1308968: add             x25, x1, #0x1b
    //     0x130896c: str             w0, [x25]
    //     0x1308970: tbz             w0, #0, #0x130898c
    //     0x1308974: ldurb           w16, [x1, #-1]
    //     0x1308978: ldurb           w17, [x0, #-1]
    //     0x130897c: and             x16, x17, x16, lsr #2
    //     0x1308980: tst             x16, HEAP, lsr #32
    //     0x1308984: b.eq            #0x130898c
    //     0x1308988: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130898c: ldur            x0, [fp, #-0x18]
    // 0x1308990: r16 = "sku_id"
    //     0x1308990: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x1308994: ldr             x16, [x16, #0x498]
    // 0x1308998: StoreField: r0->field_1f = r16
    //     0x1308998: stur            w16, [x0, #0x1f]
    // 0x130899c: ldur            x2, [fp, #-0x10]
    // 0x13089a0: LoadField: r1 = r2->field_f
    //     0x13089a0: ldur            w1, [x2, #0xf]
    // 0x13089a4: DecompressPointer r1
    //     0x13089a4: add             x1, x1, HEAP, lsl #32
    // 0x13089a8: r0 = controller()
    //     0x13089a8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13089ac: LoadField: r1 = r0->field_93
    //     0x13089ac: ldur            w1, [x0, #0x93]
    // 0x13089b0: DecompressPointer r1
    //     0x13089b0: add             x1, x1, HEAP, lsl #32
    // 0x13089b4: r0 = value()
    //     0x13089b4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13089b8: ldur            x1, [fp, #-0x18]
    // 0x13089bc: ArrayStore: r1[5] = r0  ; List_4
    //     0x13089bc: add             x25, x1, #0x23
    //     0x13089c0: str             w0, [x25]
    //     0x13089c4: tbz             w0, #0, #0x13089e0
    //     0x13089c8: ldurb           w16, [x1, #-1]
    //     0x13089cc: ldurb           w17, [x0, #-1]
    //     0x13089d0: and             x16, x17, x16, lsr #2
    //     0x13089d4: tst             x16, HEAP, lsr #32
    //     0x13089d8: b.eq            #0x13089e0
    //     0x13089dc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13089e0: ldur            x0, [fp, #-0x18]
    // 0x13089e4: r16 = "quantity"
    //     0x13089e4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x13089e8: ldr             x16, [x16, #0x428]
    // 0x13089ec: StoreField: r0->field_27 = r16
    //     0x13089ec: stur            w16, [x0, #0x27]
    // 0x13089f0: ldur            x2, [fp, #-0x10]
    // 0x13089f4: LoadField: r1 = r2->field_f
    //     0x13089f4: ldur            w1, [x2, #0xf]
    // 0x13089f8: DecompressPointer r1
    //     0x13089f8: add             x1, x1, HEAP, lsl #32
    // 0x13089fc: r0 = controller()
    //     0x13089fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308a00: LoadField: r1 = r0->field_ab
    //     0x1308a00: ldur            w1, [x0, #0xab]
    // 0x1308a04: DecompressPointer r1
    //     0x1308a04: add             x1, x1, HEAP, lsl #32
    // 0x1308a08: r0 = value()
    //     0x1308a08: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308a0c: ldur            x1, [fp, #-0x18]
    // 0x1308a10: ArrayStore: r1[7] = r0  ; List_4
    //     0x1308a10: add             x25, x1, #0x2b
    //     0x1308a14: str             w0, [x25]
    //     0x1308a18: tbz             w0, #0, #0x1308a34
    //     0x1308a1c: ldurb           w16, [x1, #-1]
    //     0x1308a20: ldurb           w17, [x0, #-1]
    //     0x1308a24: and             x16, x17, x16, lsr #2
    //     0x1308a28: tst             x16, HEAP, lsr #32
    //     0x1308a2c: b.eq            #0x1308a34
    //     0x1308a30: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308a34: ldur            x3, [fp, #-0x18]
    // 0x1308a38: r16 = "customization_request"
    //     0x1308a38: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x1308a3c: ldr             x16, [x16, #0x2b8]
    // 0x1308a40: StoreField: r3->field_2f = r16
    //     0x1308a40: stur            w16, [x3, #0x2f]
    // 0x1308a44: mov             x1, x3
    // 0x1308a48: ldur            x0, [fp, #-0x48]
    // 0x1308a4c: ArrayStore: r1[9] = r0  ; List_4
    //     0x1308a4c: add             x25, x1, #0x33
    //     0x1308a50: str             w0, [x25]
    //     0x1308a54: tbz             w0, #0, #0x1308a70
    //     0x1308a58: ldurb           w16, [x1, #-1]
    //     0x1308a5c: ldurb           w17, [x0, #-1]
    //     0x1308a60: and             x16, x17, x16, lsr #2
    //     0x1308a64: tst             x16, HEAP, lsr #32
    //     0x1308a68: b.eq            #0x1308a70
    //     0x1308a6c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308a70: r16 = "coming_from"
    //     0x1308a70: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x1308a74: ldr             x16, [x16, #0x328]
    // 0x1308a78: StoreField: r3->field_37 = r16
    //     0x1308a78: stur            w16, [x3, #0x37]
    // 0x1308a7c: r16 = "buyNow"
    //     0x1308a7c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x1308a80: ldr             x16, [x16, #0x358]
    // 0x1308a84: StoreField: r3->field_3b = r16
    //     0x1308a84: stur            w16, [x3, #0x3b]
    // 0x1308a88: r16 = "customization_prize"
    //     0x1308a88: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x1308a8c: ldr             x16, [x16, #0x2e8]
    // 0x1308a90: StoreField: r3->field_3f = r16
    //     0x1308a90: stur            w16, [x3, #0x3f]
    // 0x1308a94: r1 = Null
    //     0x1308a94: mov             x1, NULL
    // 0x1308a98: r2 = 4
    //     0x1308a98: movz            x2, #0x4
    // 0x1308a9c: r0 = AllocateArray()
    //     0x1308a9c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1308aa0: stur            x0, [fp, #-0x28]
    // 0x1308aa4: r16 = "₹"
    //     0x1308aa4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x1308aa8: ldr             x16, [x16, #0x360]
    // 0x1308aac: StoreField: r0->field_f = r16
    //     0x1308aac: stur            w16, [x0, #0xf]
    // 0x1308ab0: r1 = Function '<anonymous closure>': static.
    //     0x1308ab0: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x1308ab4: ldr             x1, [x1, #0x1a0]
    // 0x1308ab8: r2 = Null
    //     0x1308ab8: mov             x2, NULL
    // 0x1308abc: r0 = AllocateClosure()
    //     0x1308abc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1308ac0: mov             x3, x0
    // 0x1308ac4: r1 = Null
    //     0x1308ac4: mov             x1, NULL
    // 0x1308ac8: r2 = Null
    //     0x1308ac8: mov             x2, NULL
    // 0x1308acc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x1308acc: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x1308ad0: r0 = NumberFormat._forPattern()
    //     0x1308ad0: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x1308ad4: mov             x2, x0
    // 0x1308ad8: ldur            x0, [fp, #-0x10]
    // 0x1308adc: stur            x2, [fp, #-0x30]
    // 0x1308ae0: LoadField: r1 = r0->field_f
    //     0x1308ae0: ldur            w1, [x0, #0xf]
    // 0x1308ae4: DecompressPointer r1
    //     0x1308ae4: add             x1, x1, HEAP, lsl #32
    // 0x1308ae8: r0 = controller()
    //     0x1308ae8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308aec: LoadField: r1 = r0->field_67
    //     0x1308aec: ldur            w1, [x0, #0x67]
    // 0x1308af0: DecompressPointer r1
    //     0x1308af0: add             x1, x1, HEAP, lsl #32
    // 0x1308af4: r0 = value()
    //     0x1308af4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308af8: ldur            x1, [fp, #-0x30]
    // 0x1308afc: mov             x2, x0
    // 0x1308b00: r0 = format()
    //     0x1308b00: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x1308b04: ldur            x1, [fp, #-0x28]
    // 0x1308b08: ArrayStore: r1[1] = r0  ; List_4
    //     0x1308b08: add             x25, x1, #0x13
    //     0x1308b0c: str             w0, [x25]
    //     0x1308b10: tbz             w0, #0, #0x1308b2c
    //     0x1308b14: ldurb           w16, [x1, #-1]
    //     0x1308b18: ldurb           w17, [x0, #-1]
    //     0x1308b1c: and             x16, x17, x16, lsr #2
    //     0x1308b20: tst             x16, HEAP, lsr #32
    //     0x1308b24: b.eq            #0x1308b2c
    //     0x1308b28: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308b2c: ldur            x16, [fp, #-0x28]
    // 0x1308b30: str             x16, [SP]
    // 0x1308b34: r0 = _interpolate()
    //     0x1308b34: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x1308b38: ldur            x1, [fp, #-0x18]
    // 0x1308b3c: ArrayStore: r1[13] = r0  ; List_4
    //     0x1308b3c: add             x25, x1, #0x43
    //     0x1308b40: str             w0, [x25]
    //     0x1308b44: tbz             w0, #0, #0x1308b60
    //     0x1308b48: ldurb           w16, [x1, #-1]
    //     0x1308b4c: ldurb           w17, [x0, #-1]
    //     0x1308b50: and             x16, x17, x16, lsr #2
    //     0x1308b54: tst             x16, HEAP, lsr #32
    //     0x1308b58: b.eq            #0x1308b60
    //     0x1308b5c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1308b60: ldur            x0, [fp, #-0x18]
    // 0x1308b64: r16 = "is_skipped_address"
    //     0x1308b64: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x1308b68: ldr             x16, [x16, #0xb80]
    // 0x1308b6c: StoreField: r0->field_47 = r16
    //     0x1308b6c: stur            w16, [x0, #0x47]
    // 0x1308b70: r16 = true
    //     0x1308b70: add             x16, NULL, #0x20  ; true
    // 0x1308b74: StoreField: r0->field_4b = r16
    //     0x1308b74: stur            w16, [x0, #0x4b]
    // 0x1308b78: r16 = <String, dynamic>
    //     0x1308b78: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x1308b7c: stp             x0, x16, [SP]
    // 0x1308b80: r0 = Map._fromLiteral()
    //     0x1308b80: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x1308b84: r16 = "/checkout_request_number_page"
    //     0x1308b84: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0x1308b88: ldr             x16, [x16, #0x9f8]
    // 0x1308b8c: stp             x16, NULL, [SP, #8]
    // 0x1308b90: str             x0, [SP]
    // 0x1308b94: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x1308b94: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x1308b98: ldr             x4, [x4, #0x438]
    // 0x1308b9c: r0 = GetNavigation.toNamed()
    //     0x1308b9c: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x1308ba0: mov             x1, x0
    // 0x1308ba4: stur            x1, [fp, #-0x18]
    // 0x1308ba8: r0 = Await()
    //     0x1308ba8: bl              #0x63248c  ; AwaitStub
    // 0x1308bac: r0 = Null
    //     0x1308bac: mov             x0, NULL
    // 0x1308bb0: r0 = ReturnAsyncNotFuture()
    //     0x1308bb0: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x1308bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1308bb4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1308bb8: b               #0x1307ea8
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1308bbc, size: 0xcc
    // 0x1308bbc: EnterFrame
    //     0x1308bbc: stp             fp, lr, [SP, #-0x10]!
    //     0x1308bc0: mov             fp, SP
    // 0x1308bc4: AllocStack(0x20)
    //     0x1308bc4: sub             SP, SP, #0x20
    // 0x1308bc8: SetupParameters()
    //     0x1308bc8: ldr             x0, [fp, #0x10]
    //     0x1308bcc: ldur            w2, [x0, #0x17]
    //     0x1308bd0: add             x2, x2, HEAP, lsl #32
    //     0x1308bd4: stur            x2, [fp, #-8]
    // 0x1308bd8: CheckStackOverflow
    //     0x1308bd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1308bdc: cmp             SP, x16
    //     0x1308be0: b.ls            #0x1308c80
    // 0x1308be4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1308be4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1308be8: ldr             x0, [x0, #0x1c80]
    //     0x1308bec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1308bf0: cmp             w0, w16
    //     0x1308bf4: b.ne            #0x1308c00
    //     0x1308bf8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1308bfc: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1308c00: str             NULL, [SP]
    // 0x1308c04: r4 = const [0x1, 0, 0, 0, null]
    //     0x1308c04: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x1308c08: r0 = GetNavigation.back()
    //     0x1308c08: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1308c0c: r16 = PreferenceManager
    //     0x1308c0c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x1308c10: ldr             x16, [x16, #0x878]
    // 0x1308c14: str             x16, [SP]
    // 0x1308c18: r0 = toString()
    //     0x1308c18: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1308c1c: r16 = <PreferenceManager>
    //     0x1308c1c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x1308c20: ldr             x16, [x16, #0x880]
    // 0x1308c24: stp             x0, x16, [SP]
    // 0x1308c28: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1308c28: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1308c2c: r0 = Inst.find()
    //     0x1308c2c: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1308c30: mov             x1, x0
    // 0x1308c34: r2 = "token"
    //     0x1308c34: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x1308c38: ldr             x2, [x2, #0x958]
    // 0x1308c3c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1308c3c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1308c40: r0 = getString()
    //     0x1308c40: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x1308c44: ldur            x2, [fp, #-8]
    // 0x1308c48: r1 = Function '<anonymous closure>':.
    //     0x1308c48: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a70] AnonymousClosure: (0x1307e68), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x136021c)
    //     0x1308c4c: ldr             x1, [x1, #0xa70]
    // 0x1308c50: stur            x0, [fp, #-8]
    // 0x1308c54: r0 = AllocateClosure()
    //     0x1308c54: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1308c58: r16 = <Null?>
    //     0x1308c58: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x1308c5c: ldur            lr, [fp, #-8]
    // 0x1308c60: stp             lr, x16, [SP, #8]
    // 0x1308c64: str             x0, [SP]
    // 0x1308c68: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x1308c68: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x1308c6c: r0 = then()
    //     0x1308c6c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x1308c70: r0 = Null
    //     0x1308c70: mov             x0, NULL
    // 0x1308c74: LeaveFrame
    //     0x1308c74: mov             SP, fp
    //     0x1308c78: ldp             fp, lr, [SP], #0x10
    // 0x1308c7c: ret
    //     0x1308c7c: ret             
    // 0x1308c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1308c80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1308c84: b               #0x1308be4
  }
  [closure] SingleExchangeProductBottomSheet <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0x1308c88, size: 0x1d4
    // 0x1308c88: EnterFrame
    //     0x1308c88: stp             fp, lr, [SP, #-0x10]!
    //     0x1308c8c: mov             fp, SP
    // 0x1308c90: AllocStack(0x58)
    //     0x1308c90: sub             SP, SP, #0x58
    // 0x1308c94: SetupParameters()
    //     0x1308c94: ldr             x0, [fp, #0x18]
    //     0x1308c98: ldur            w2, [x0, #0x17]
    //     0x1308c9c: add             x2, x2, HEAP, lsl #32
    //     0x1308ca0: stur            x2, [fp, #-8]
    // 0x1308ca4: CheckStackOverflow
    //     0x1308ca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1308ca8: cmp             SP, x16
    //     0x1308cac: b.ls            #0x1308e54
    // 0x1308cb0: LoadField: r1 = r2->field_f
    //     0x1308cb0: ldur            w1, [x2, #0xf]
    // 0x1308cb4: DecompressPointer r1
    //     0x1308cb4: add             x1, x1, HEAP, lsl #32
    // 0x1308cb8: r0 = controller()
    //     0x1308cb8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308cbc: LoadField: r1 = r0->field_87
    //     0x1308cbc: ldur            w1, [x0, #0x87]
    // 0x1308cc0: DecompressPointer r1
    //     0x1308cc0: add             x1, x1, HEAP, lsl #32
    // 0x1308cc4: r0 = value()
    //     0x1308cc4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308cc8: ldur            x2, [fp, #-8]
    // 0x1308ccc: stur            x0, [fp, #-0x10]
    // 0x1308cd0: LoadField: r1 = r2->field_f
    //     0x1308cd0: ldur            w1, [x2, #0xf]
    // 0x1308cd4: DecompressPointer r1
    //     0x1308cd4: add             x1, x1, HEAP, lsl #32
    // 0x1308cd8: r0 = controller()
    //     0x1308cd8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308cdc: LoadField: r2 = r0->field_bf
    //     0x1308cdc: ldur            w2, [x0, #0xbf]
    // 0x1308ce0: DecompressPointer r2
    //     0x1308ce0: add             x2, x2, HEAP, lsl #32
    // 0x1308ce4: ldur            x0, [fp, #-8]
    // 0x1308ce8: stur            x2, [fp, #-0x18]
    // 0x1308cec: LoadField: r1 = r0->field_f
    //     0x1308cec: ldur            w1, [x0, #0xf]
    // 0x1308cf0: DecompressPointer r1
    //     0x1308cf0: add             x1, x1, HEAP, lsl #32
    // 0x1308cf4: r0 = controller()
    //     0x1308cf4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308cf8: LoadField: r2 = r0->field_c3
    //     0x1308cf8: ldur            w2, [x0, #0xc3]
    // 0x1308cfc: DecompressPointer r2
    //     0x1308cfc: add             x2, x2, HEAP, lsl #32
    // 0x1308d00: ldur            x0, [fp, #-8]
    // 0x1308d04: stur            x2, [fp, #-0x20]
    // 0x1308d08: LoadField: r1 = r0->field_f
    //     0x1308d08: ldur            w1, [x0, #0xf]
    // 0x1308d0c: DecompressPointer r1
    //     0x1308d0c: add             x1, x1, HEAP, lsl #32
    // 0x1308d10: r0 = controller()
    //     0x1308d10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308d14: LoadField: r2 = r0->field_c7
    //     0x1308d14: ldur            w2, [x0, #0xc7]
    // 0x1308d18: DecompressPointer r2
    //     0x1308d18: add             x2, x2, HEAP, lsl #32
    // 0x1308d1c: ldur            x0, [fp, #-8]
    // 0x1308d20: stur            x2, [fp, #-0x28]
    // 0x1308d24: LoadField: r1 = r0->field_f
    //     0x1308d24: ldur            w1, [x0, #0xf]
    // 0x1308d28: DecompressPointer r1
    //     0x1308d28: add             x1, x1, HEAP, lsl #32
    // 0x1308d2c: r0 = controller()
    //     0x1308d2c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308d30: LoadField: r1 = r0->field_ab
    //     0x1308d30: ldur            w1, [x0, #0xab]
    // 0x1308d34: DecompressPointer r1
    //     0x1308d34: add             x1, x1, HEAP, lsl #32
    // 0x1308d38: r0 = value()
    //     0x1308d38: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308d3c: r1 = 60
    //     0x1308d3c: movz            x1, #0x3c
    // 0x1308d40: branchIfSmi(r0, 0x1308d4c)
    //     0x1308d40: tbz             w0, #0, #0x1308d4c
    // 0x1308d44: r1 = LoadClassIdInstr(r0)
    //     0x1308d44: ldur            x1, [x0, #-1]
    //     0x1308d48: ubfx            x1, x1, #0xc, #0x14
    // 0x1308d4c: str             x0, [SP]
    // 0x1308d50: mov             x0, x1
    // 0x1308d54: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x1308d54: ldr             x4, [PP, #0x2d8]  ; [pp+0x2d8] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x1308d58: r0 = GDT[cid_x0 + 0x2700]()
    //     0x1308d58: movz            x17, #0x2700
    //     0x1308d5c: add             lr, x0, x17
    //     0x1308d60: ldr             lr, [x21, lr, lsl #3]
    //     0x1308d64: blr             lr
    // 0x1308d68: ldur            x2, [fp, #-8]
    // 0x1308d6c: stur            x0, [fp, #-0x30]
    // 0x1308d70: LoadField: r1 = r2->field_f
    //     0x1308d70: ldur            w1, [x2, #0xf]
    // 0x1308d74: DecompressPointer r1
    //     0x1308d74: add             x1, x1, HEAP, lsl #32
    // 0x1308d78: r0 = controller()
    //     0x1308d78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308d7c: LoadField: r2 = r0->field_9b
    //     0x1308d7c: ldur            w2, [x0, #0x9b]
    // 0x1308d80: DecompressPointer r2
    //     0x1308d80: add             x2, x2, HEAP, lsl #32
    // 0x1308d84: ldur            x0, [fp, #-8]
    // 0x1308d88: stur            x2, [fp, #-0x38]
    // 0x1308d8c: LoadField: r1 = r0->field_f
    //     0x1308d8c: ldur            w1, [x0, #0xf]
    // 0x1308d90: DecompressPointer r1
    //     0x1308d90: add             x1, x1, HEAP, lsl #32
    // 0x1308d94: r0 = controller()
    //     0x1308d94: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308d98: mov             x1, x0
    // 0x1308d9c: r0 = getCustomisedPrice()
    //     0x1308d9c: bl              #0x1306ca0  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::getCustomisedPrice
    // 0x1308da0: ldur            x2, [fp, #-8]
    // 0x1308da4: stur            x0, [fp, #-0x40]
    // 0x1308da8: LoadField: r1 = r2->field_f
    //     0x1308da8: ldur            w1, [x2, #0xf]
    // 0x1308dac: DecompressPointer r1
    //     0x1308dac: add             x1, x1, HEAP, lsl #32
    // 0x1308db0: r0 = controller()
    //     0x1308db0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1308db4: LoadField: r1 = r0->field_5b
    //     0x1308db4: ldur            w1, [x0, #0x5b]
    // 0x1308db8: DecompressPointer r1
    //     0x1308db8: add             x1, x1, HEAP, lsl #32
    // 0x1308dbc: r0 = value()
    //     0x1308dbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x1308dc0: stur            x0, [fp, #-0x48]
    // 0x1308dc4: r0 = SingleExchangeProductBottomSheet()
    //     0x1308dc4: bl              #0xa49238  ; AllocateSingleExchangeProductBottomSheetStub -> SingleExchangeProductBottomSheet (size=0x34)
    // 0x1308dc8: mov             x3, x0
    // 0x1308dcc: ldur            x0, [fp, #-0x10]
    // 0x1308dd0: stur            x3, [fp, #-0x50]
    // 0x1308dd4: StoreField: r3->field_b = r0
    //     0x1308dd4: stur            w0, [x3, #0xb]
    // 0x1308dd8: ldur            x0, [fp, #-0x18]
    // 0x1308ddc: StoreField: r3->field_f = r0
    //     0x1308ddc: stur            w0, [x3, #0xf]
    // 0x1308de0: ldur            x0, [fp, #-0x20]
    // 0x1308de4: ArrayStore: r3[0] = r0  ; List_4
    //     0x1308de4: stur            w0, [x3, #0x17]
    // 0x1308de8: ldur            x0, [fp, #-0x28]
    // 0x1308dec: StoreField: r3->field_13 = r0
    //     0x1308dec: stur            w0, [x3, #0x13]
    // 0x1308df0: ldur            x0, [fp, #-0x30]
    // 0x1308df4: StoreField: r3->field_1b = r0
    //     0x1308df4: stur            w0, [x3, #0x1b]
    // 0x1308df8: ldur            x2, [fp, #-8]
    // 0x1308dfc: r1 = Function '<anonymous closure>':.
    //     0x1308dfc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a58] AnonymousClosure: (0x1308bbc), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x136021c)
    //     0x1308e00: ldr             x1, [x1, #0xa58]
    // 0x1308e04: r0 = AllocateClosure()
    //     0x1308e04: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1308e08: mov             x1, x0
    // 0x1308e0c: ldur            x0, [fp, #-0x50]
    // 0x1308e10: StoreField: r0->field_1f = r1
    //     0x1308e10: stur            w1, [x0, #0x1f]
    // 0x1308e14: ldur            x2, [fp, #-8]
    // 0x1308e18: r1 = Function '<anonymous closure>':.
    //     0x1308e18: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a60] AnonymousClosure: (0x1308e5c), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x136021c)
    //     0x1308e1c: ldr             x1, [x1, #0xa60]
    // 0x1308e20: r0 = AllocateClosure()
    //     0x1308e20: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1308e24: mov             x1, x0
    // 0x1308e28: ldur            x0, [fp, #-0x50]
    // 0x1308e2c: StoreField: r0->field_23 = r1
    //     0x1308e2c: stur            w1, [x0, #0x23]
    // 0x1308e30: ldur            x1, [fp, #-0x38]
    // 0x1308e34: StoreField: r0->field_2b = r1
    //     0x1308e34: stur            w1, [x0, #0x2b]
    // 0x1308e38: ldur            x1, [fp, #-0x40]
    // 0x1308e3c: StoreField: r0->field_27 = r1
    //     0x1308e3c: stur            w1, [x0, #0x27]
    // 0x1308e40: ldur            x1, [fp, #-0x48]
    // 0x1308e44: StoreField: r0->field_2f = r1
    //     0x1308e44: stur            w1, [x0, #0x2f]
    // 0x1308e48: LeaveFrame
    //     0x1308e48: mov             SP, fp
    //     0x1308e4c: ldp             fp, lr, [SP], #0x10
    // 0x1308e50: ret
    //     0x1308e50: ret             
    // 0x1308e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1308e54: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1308e58: b               #0x1308cb0
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x1308e5c, size: 0xcc
    // 0x1308e5c: EnterFrame
    //     0x1308e5c: stp             fp, lr, [SP, #-0x10]!
    //     0x1308e60: mov             fp, SP
    // 0x1308e64: AllocStack(0x20)
    //     0x1308e64: sub             SP, SP, #0x20
    // 0x1308e68: SetupParameters()
    //     0x1308e68: ldr             x0, [fp, #0x10]
    //     0x1308e6c: ldur            w2, [x0, #0x17]
    //     0x1308e70: add             x2, x2, HEAP, lsl #32
    //     0x1308e74: stur            x2, [fp, #-8]
    // 0x1308e78: CheckStackOverflow
    //     0x1308e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1308e7c: cmp             SP, x16
    //     0x1308e80: b.ls            #0x1308f20
    // 0x1308e84: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1308e84: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1308e88: ldr             x0, [x0, #0x1c80]
    //     0x1308e8c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x1308e90: cmp             w0, w16
    //     0x1308e94: b.ne            #0x1308ea0
    //     0x1308e98: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1308e9c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x1308ea0: str             NULL, [SP]
    // 0x1308ea4: r4 = const [0x1, 0, 0, 0, null]
    //     0x1308ea4: ldr             x4, [PP, #0x50]  ; [pp+0x50] List(5) [0x1, 0, 0, 0, Null]
    // 0x1308ea8: r0 = GetNavigation.back()
    //     0x1308ea8: bl              #0x8b5310  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.back
    // 0x1308eac: r16 = PreferenceManager
    //     0x1308eac: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x1308eb0: ldr             x16, [x16, #0x878]
    // 0x1308eb4: str             x16, [SP]
    // 0x1308eb8: r0 = toString()
    //     0x1308eb8: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x1308ebc: r16 = <PreferenceManager>
    //     0x1308ebc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x1308ec0: ldr             x16, [x16, #0x880]
    // 0x1308ec4: stp             x0, x16, [SP]
    // 0x1308ec8: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x1308ec8: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x1308ecc: r0 = Inst.find()
    //     0x1308ecc: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x1308ed0: mov             x1, x0
    // 0x1308ed4: r2 = "token"
    //     0x1308ed4: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x1308ed8: ldr             x2, [x2, #0x958]
    // 0x1308edc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x1308edc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x1308ee0: r0 = getString()
    //     0x1308ee0: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x1308ee4: ldur            x2, [fp, #-8]
    // 0x1308ee8: r1 = Function '<anonymous closure>':.
    //     0x1308ee8: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a68] AnonymousClosure: (0x1308f28), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x1367b8c)
    //     0x1308eec: ldr             x1, [x1, #0xa68]
    // 0x1308ef0: stur            x0, [fp, #-8]
    // 0x1308ef4: r0 = AllocateClosure()
    //     0x1308ef4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1308ef8: r16 = <Null?>
    //     0x1308ef8: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x1308efc: ldur            lr, [fp, #-8]
    // 0x1308f00: stp             lr, x16, [SP, #8]
    // 0x1308f04: str             x0, [SP]
    // 0x1308f08: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x1308f08: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x1308f0c: r0 = then()
    //     0x1308f0c: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x1308f10: r0 = Null
    //     0x1308f10: mov             x0, NULL
    // 0x1308f14: LeaveFrame
    //     0x1308f14: mov             SP, fp
    //     0x1308f18: ldp             fp, lr, [SP], #0x10
    // 0x1308f1c: ret
    //     0x1308f1c: ret             
    // 0x1308f20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x1308f20: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1308f24: b               #0x1308e84
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x130c8a0, size: 0x3cc
    // 0x130c8a0: EnterFrame
    //     0x130c8a0: stp             fp, lr, [SP, #-0x10]!
    //     0x130c8a4: mov             fp, SP
    // 0x130c8a8: AllocStack(0x50)
    //     0x130c8a8: sub             SP, SP, #0x50
    // 0x130c8ac: SetupParameters()
    //     0x130c8ac: ldr             x0, [fp, #0x10]
    //     0x130c8b0: ldur            w2, [x0, #0x17]
    //     0x130c8b4: add             x2, x2, HEAP, lsl #32
    //     0x130c8b8: stur            x2, [fp, #-8]
    // 0x130c8bc: CheckStackOverflow
    //     0x130c8bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130c8c0: cmp             SP, x16
    //     0x130c8c4: b.ls            #0x130cc64
    // 0x130c8c8: LoadField: r1 = r2->field_f
    //     0x130c8c8: ldur            w1, [x2, #0xf]
    // 0x130c8cc: DecompressPointer r1
    //     0x130c8cc: add             x1, x1, HEAP, lsl #32
    // 0x130c8d0: r0 = controller()
    //     0x130c8d0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130c8d4: LoadField: r1 = r0->field_5b
    //     0x130c8d4: ldur            w1, [x0, #0x5b]
    // 0x130c8d8: DecompressPointer r1
    //     0x130c8d8: add             x1, x1, HEAP, lsl #32
    // 0x130c8dc: r0 = value()
    //     0x130c8dc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130c8e0: LoadField: r1 = r0->field_67
    //     0x130c8e0: ldur            w1, [x0, #0x67]
    // 0x130c8e4: DecompressPointer r1
    //     0x130c8e4: add             x1, x1, HEAP, lsl #32
    // 0x130c8e8: cmp             w1, NULL
    // 0x130c8ec: b.ne            #0x130c8f8
    // 0x130c8f0: r0 = Null
    //     0x130c8f0: mov             x0, NULL
    // 0x130c8f4: b               #0x130c930
    // 0x130c8f8: LoadField: r0 = r1->field_7
    //     0x130c8f8: ldur            w0, [x1, #7]
    // 0x130c8fc: DecompressPointer r0
    //     0x130c8fc: add             x0, x0, HEAP, lsl #32
    // 0x130c900: cmp             w0, NULL
    // 0x130c904: b.ne            #0x130c910
    // 0x130c908: r0 = Null
    //     0x130c908: mov             x0, NULL
    // 0x130c90c: b               #0x130c930
    // 0x130c910: LoadField: r1 = r0->field_b
    //     0x130c910: ldur            w1, [x0, #0xb]
    // 0x130c914: DecompressPointer r1
    //     0x130c914: add             x1, x1, HEAP, lsl #32
    // 0x130c918: cmp             w1, NULL
    // 0x130c91c: b.ne            #0x130c928
    // 0x130c920: r0 = Null
    //     0x130c920: mov             x0, NULL
    // 0x130c924: b               #0x130c930
    // 0x130c928: LoadField: r0 = r1->field_7
    //     0x130c928: ldur            w0, [x1, #7]
    // 0x130c92c: DecompressPointer r0
    //     0x130c92c: add             x0, x0, HEAP, lsl #32
    // 0x130c930: cmp             w0, NULL
    // 0x130c934: b.eq            #0x130caec
    // 0x130c938: tbnz            w0, #4, #0x130caec
    // 0x130c93c: ldur            x2, [fp, #-8]
    // 0x130c940: LoadField: r1 = r2->field_f
    //     0x130c940: ldur            w1, [x2, #0xf]
    // 0x130c944: DecompressPointer r1
    //     0x130c944: add             x1, x1, HEAP, lsl #32
    // 0x130c948: r0 = controller()
    //     0x130c948: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130c94c: LoadField: r2 = r0->field_4b
    //     0x130c94c: ldur            w2, [x0, #0x4b]
    // 0x130c950: DecompressPointer r2
    //     0x130c950: add             x2, x2, HEAP, lsl #32
    // 0x130c954: ldur            x0, [fp, #-8]
    // 0x130c958: stur            x2, [fp, #-0x10]
    // 0x130c95c: LoadField: r1 = r0->field_f
    //     0x130c95c: ldur            w1, [x0, #0xf]
    // 0x130c960: DecompressPointer r1
    //     0x130c960: add             x1, x1, HEAP, lsl #32
    // 0x130c964: r0 = controller()
    //     0x130c964: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130c968: LoadField: r1 = r0->field_93
    //     0x130c968: ldur            w1, [x0, #0x93]
    // 0x130c96c: DecompressPointer r1
    //     0x130c96c: add             x1, x1, HEAP, lsl #32
    // 0x130c970: r0 = value()
    //     0x130c970: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130c974: ldur            x2, [fp, #-8]
    // 0x130c978: stur            x0, [fp, #-0x18]
    // 0x130c97c: LoadField: r1 = r2->field_f
    //     0x130c97c: ldur            w1, [x2, #0xf]
    // 0x130c980: DecompressPointer r1
    //     0x130c980: add             x1, x1, HEAP, lsl #32
    // 0x130c984: r0 = controller()
    //     0x130c984: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130c988: LoadField: r1 = r0->field_af
    //     0x130c988: ldur            w1, [x0, #0xaf]
    // 0x130c98c: DecompressPointer r1
    //     0x130c98c: add             x1, x1, HEAP, lsl #32
    // 0x130c990: r0 = value()
    //     0x130c990: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130c994: stp             x0, NULL, [SP]
    // 0x130c998: r0 = _Double.fromInteger()
    //     0x130c998: bl              #0x643aa4  ; [dart:core] _Double::_Double.fromInteger
    // 0x130c99c: r1 = Null
    //     0x130c99c: mov             x1, NULL
    // 0x130c9a0: r2 = 12
    //     0x130c9a0: movz            x2, #0xc
    // 0x130c9a4: stur            x0, [fp, #-0x20]
    // 0x130c9a8: r0 = AllocateArray()
    //     0x130c9a8: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130c9ac: stur            x0, [fp, #-0x28]
    // 0x130c9b0: r16 = "id"
    //     0x130c9b0: add             x16, PP, #0xb, lsl #12  ; [pp+0xb400] "id"
    //     0x130c9b4: ldr             x16, [x16, #0x400]
    // 0x130c9b8: StoreField: r0->field_f = r16
    //     0x130c9b8: stur            w16, [x0, #0xf]
    // 0x130c9bc: ldur            x2, [fp, #-8]
    // 0x130c9c0: LoadField: r1 = r2->field_f
    //     0x130c9c0: ldur            w1, [x2, #0xf]
    // 0x130c9c4: DecompressPointer r1
    //     0x130c9c4: add             x1, x1, HEAP, lsl #32
    // 0x130c9c8: r0 = controller()
    //     0x130c9c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130c9cc: LoadField: r1 = r0->field_93
    //     0x130c9cc: ldur            w1, [x0, #0x93]
    // 0x130c9d0: DecompressPointer r1
    //     0x130c9d0: add             x1, x1, HEAP, lsl #32
    // 0x130c9d4: r0 = value()
    //     0x130c9d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130c9d8: ldur            x1, [fp, #-0x28]
    // 0x130c9dc: ArrayStore: r1[1] = r0  ; List_4
    //     0x130c9dc: add             x25, x1, #0x13
    //     0x130c9e0: str             w0, [x25]
    //     0x130c9e4: tbz             w0, #0, #0x130ca00
    //     0x130c9e8: ldurb           w16, [x1, #-1]
    //     0x130c9ec: ldurb           w17, [x0, #-1]
    //     0x130c9f0: and             x16, x17, x16, lsr #2
    //     0x130c9f4: tst             x16, HEAP, lsr #32
    //     0x130c9f8: b.eq            #0x130ca00
    //     0x130c9fc: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130ca00: ldur            x0, [fp, #-0x28]
    // 0x130ca04: r16 = "quantity"
    //     0x130ca04: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130ca08: ldr             x16, [x16, #0x428]
    // 0x130ca0c: ArrayStore: r0[0] = r16  ; List_4
    //     0x130ca0c: stur            w16, [x0, #0x17]
    // 0x130ca10: ldur            x2, [fp, #-8]
    // 0x130ca14: LoadField: r1 = r2->field_f
    //     0x130ca14: ldur            w1, [x2, #0xf]
    // 0x130ca18: DecompressPointer r1
    //     0x130ca18: add             x1, x1, HEAP, lsl #32
    // 0x130ca1c: r0 = controller()
    //     0x130ca1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ca20: LoadField: r1 = r0->field_ab
    //     0x130ca20: ldur            w1, [x0, #0xab]
    // 0x130ca24: DecompressPointer r1
    //     0x130ca24: add             x1, x1, HEAP, lsl #32
    // 0x130ca28: r0 = value()
    //     0x130ca28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ca2c: ldur            x1, [fp, #-0x28]
    // 0x130ca30: ArrayStore: r1[3] = r0  ; List_4
    //     0x130ca30: add             x25, x1, #0x1b
    //     0x130ca34: str             w0, [x25]
    //     0x130ca38: tbz             w0, #0, #0x130ca54
    //     0x130ca3c: ldurb           w16, [x1, #-1]
    //     0x130ca40: ldurb           w17, [x0, #-1]
    //     0x130ca44: and             x16, x17, x16, lsr #2
    //     0x130ca48: tst             x16, HEAP, lsr #32
    //     0x130ca4c: b.eq            #0x130ca54
    //     0x130ca50: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130ca54: ldur            x0, [fp, #-0x28]
    // 0x130ca58: r16 = "item_price"
    //     0x130ca58: add             x16, PP, #0x30, lsl #12  ; [pp+0x30498] "item_price"
    //     0x130ca5c: ldr             x16, [x16, #0x498]
    // 0x130ca60: StoreField: r0->field_1f = r16
    //     0x130ca60: stur            w16, [x0, #0x1f]
    // 0x130ca64: ldur            x2, [fp, #-8]
    // 0x130ca68: LoadField: r1 = r2->field_f
    //     0x130ca68: ldur            w1, [x2, #0xf]
    // 0x130ca6c: DecompressPointer r1
    //     0x130ca6c: add             x1, x1, HEAP, lsl #32
    // 0x130ca70: r0 = controller()
    //     0x130ca70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ca74: LoadField: r1 = r0->field_af
    //     0x130ca74: ldur            w1, [x0, #0xaf]
    // 0x130ca78: DecompressPointer r1
    //     0x130ca78: add             x1, x1, HEAP, lsl #32
    // 0x130ca7c: r0 = value()
    //     0x130ca7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ca80: ldur            x1, [fp, #-0x28]
    // 0x130ca84: ArrayStore: r1[5] = r0  ; List_4
    //     0x130ca84: add             x25, x1, #0x23
    //     0x130ca88: str             w0, [x25]
    //     0x130ca8c: tbz             w0, #0, #0x130caa8
    //     0x130ca90: ldurb           w16, [x1, #-1]
    //     0x130ca94: ldurb           w17, [x0, #-1]
    //     0x130ca98: and             x16, x17, x16, lsr #2
    //     0x130ca9c: tst             x16, HEAP, lsr #32
    //     0x130caa0: b.eq            #0x130caa8
    //     0x130caa4: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130caa8: r16 = <String, dynamic>
    //     0x130caa8: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130caac: ldur            lr, [fp, #-0x28]
    // 0x130cab0: stp             lr, x16, [SP]
    // 0x130cab4: r0 = Map._fromLiteral()
    //     0x130cab4: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130cab8: ldur            x16, [fp, #-0x18]
    // 0x130cabc: r30 = "product"
    //     0x130cabc: add             lr, PP, #0x12, lsl #12  ; [pp+0x12240] "product"
    //     0x130cac0: ldr             lr, [lr, #0x240]
    // 0x130cac4: stp             lr, x16, [SP, #0x18]
    // 0x130cac8: r16 = "INR"
    //     0x130cac8: add             x16, PP, #0x30, lsl #12  ; [pp+0x304c0] "INR"
    //     0x130cacc: ldr             x16, [x16, #0x4c0]
    // 0x130cad0: ldur            lr, [fp, #-0x20]
    // 0x130cad4: stp             lr, x16, [SP, #8]
    // 0x130cad8: str             x0, [SP]
    // 0x130cadc: ldur            x1, [fp, #-0x10]
    // 0x130cae0: r4 = const [0, 0x6, 0x5, 0x1, content, 0x5, currency, 0x3, id, 0x1, price, 0x4, type, 0x2, null]
    //     0x130cae0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32318] List(15) [0, 0x6, 0x5, 0x1, "content", 0x5, "currency", 0x3, "id", 0x1, "price", 0x4, "type", 0x2, Null]
    //     0x130cae4: ldr             x4, [x4, #0x318]
    // 0x130cae8: r0 = logAddToCart()
    //     0x130cae8: bl              #0x8a2ce0  ; [package:facebook_app_events/facebook_app_events.dart] FacebookAppEvents::logAddToCart
    // 0x130caec: ldur            x2, [fp, #-8]
    // 0x130caf0: LoadField: r1 = r2->field_f
    //     0x130caf0: ldur            w1, [x2, #0xf]
    // 0x130caf4: DecompressPointer r1
    //     0x130caf4: add             x1, x1, HEAP, lsl #32
    // 0x130caf8: r0 = controller()
    //     0x130caf8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130cafc: LoadField: r1 = r0->field_bb
    //     0x130cafc: ldur            w1, [x0, #0xbb]
    // 0x130cb00: DecompressPointer r1
    //     0x130cb00: add             x1, x1, HEAP, lsl #32
    // 0x130cb04: r0 = LoadClassIdInstr(r1)
    //     0x130cb04: ldur            x0, [x1, #-1]
    //     0x130cb08: ubfx            x0, x0, #0xc, #0x14
    // 0x130cb0c: r16 = "add_to_bag"
    //     0x130cb0c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10a38] "add_to_bag"
    //     0x130cb10: ldr             x16, [x16, #0xa38]
    // 0x130cb14: stp             x16, x1, [SP]
    // 0x130cb18: mov             lr, x0
    // 0x130cb1c: ldr             lr, [x21, lr, lsl #3]
    // 0x130cb20: blr             lr
    // 0x130cb24: tbnz            w0, #4, #0x130cb44
    // 0x130cb28: ldur            x2, [fp, #-8]
    // 0x130cb2c: LoadField: r1 = r2->field_f
    //     0x130cb2c: ldur            w1, [x2, #0xf]
    // 0x130cb30: DecompressPointer r1
    //     0x130cb30: add             x1, x1, HEAP, lsl #32
    // 0x130cb34: r0 = controller()
    //     0x130cb34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130cb38: mov             x1, x0
    // 0x130cb3c: r0 = addToBag()
    //     0x130cb3c: bl              #0x1307220  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::addToBag
    // 0x130cb40: b               #0x130cc54
    // 0x130cb44: ldur            x2, [fp, #-8]
    // 0x130cb48: LoadField: r1 = r2->field_f
    //     0x130cb48: ldur            w1, [x2, #0xf]
    // 0x130cb4c: DecompressPointer r1
    //     0x130cb4c: add             x1, x1, HEAP, lsl #32
    // 0x130cb50: r0 = controller()
    //     0x130cb50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130cb54: LoadField: r1 = r0->field_83
    //     0x130cb54: ldur            w1, [x0, #0x83]
    // 0x130cb58: DecompressPointer r1
    //     0x130cb58: add             x1, x1, HEAP, lsl #32
    // 0x130cb5c: r0 = value()
    //     0x130cb5c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130cb60: LoadField: r1 = r0->field_b
    //     0x130cb60: ldur            w1, [x0, #0xb]
    // 0x130cb64: DecompressPointer r1
    //     0x130cb64: add             x1, x1, HEAP, lsl #32
    // 0x130cb68: cmp             w1, NULL
    // 0x130cb6c: b.ne            #0x130cb78
    // 0x130cb70: ldur            x2, [fp, #-8]
    // 0x130cb74: b               #0x130cbd4
    // 0x130cb78: LoadField: r0 = r1->field_7
    //     0x130cb78: ldur            w0, [x1, #7]
    // 0x130cb7c: DecompressPointer r0
    //     0x130cb7c: add             x0, x0, HEAP, lsl #32
    // 0x130cb80: cmp             w0, NULL
    // 0x130cb84: b.eq            #0x130cbd0
    // 0x130cb88: ldur            x2, [fp, #-8]
    // 0x130cb8c: LoadField: r0 = r2->field_13
    //     0x130cb8c: ldur            w0, [x2, #0x13]
    // 0x130cb90: DecompressPointer r0
    //     0x130cb90: add             x0, x0, HEAP, lsl #32
    // 0x130cb94: stur            x0, [fp, #-0x10]
    // 0x130cb98: r1 = Function '<anonymous closure>':.
    //     0x130cb98: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a48] AnonymousClosure: (0x1308c88), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x136021c)
    //     0x130cb9c: ldr             x1, [x1, #0xa48]
    // 0x130cba0: r0 = AllocateClosure()
    //     0x130cba0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130cba4: stp             x0, NULL, [SP, #0x18]
    // 0x130cba8: ldur            x16, [fp, #-0x10]
    // 0x130cbac: r30 = true
    //     0x130cbac: add             lr, NULL, #0x20  ; true
    // 0x130cbb0: stp             lr, x16, [SP, #8]
    // 0x130cbb4: r16 = Instance_RoundedRectangleBorder
    //     0x130cbb4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3ec78] Obj!RoundedRectangleBorder@d5abc1
    //     0x130cbb8: ldr             x16, [x16, #0xc78]
    // 0x130cbbc: str             x16, [SP]
    // 0x130cbc0: r4 = const [0x1, 0x4, 0x4, 0x2, isScrollControlled, 0x2, shape, 0x3, null]
    //     0x130cbc0: add             x4, PP, #0x32, lsl #12  ; [pp+0x32b20] List(9) [0x1, 0x4, 0x4, 0x2, "isScrollControlled", 0x2, "shape", 0x3, Null]
    //     0x130cbc4: ldr             x4, [x4, #0xb20]
    // 0x130cbc8: r0 = showModalBottomSheet()
    //     0x130cbc8: bl              #0x8ade00  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0x130cbcc: b               #0x130cc54
    // 0x130cbd0: ldur            x2, [fp, #-8]
    // 0x130cbd4: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130cbd4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130cbd8: ldr             x0, [x0, #0x1c80]
    //     0x130cbdc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130cbe0: cmp             w0, w16
    //     0x130cbe4: b.ne            #0x130cbf0
    //     0x130cbe8: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130cbec: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130cbf0: r16 = PreferenceManager
    //     0x130cbf0: add             x16, PP, #0xa, lsl #12  ; [pp+0xa878] Type: PreferenceManager
    //     0x130cbf4: ldr             x16, [x16, #0x878]
    // 0x130cbf8: str             x16, [SP]
    // 0x130cbfc: r0 = toString()
    //     0x130cbfc: bl              #0x15840d8  ; [dart:core] _AbstractType::toString
    // 0x130cc00: r16 = <PreferenceManager>
    //     0x130cc00: add             x16, PP, #0xa, lsl #12  ; [pp+0xa880] TypeArguments: <PreferenceManager>
    //     0x130cc04: ldr             x16, [x16, #0x880]
    // 0x130cc08: stp             x0, x16, [SP]
    // 0x130cc0c: r4 = const [0x1, 0x1, 0x1, 0, tag, 0, null]
    //     0x130cc0c: ldr             x4, [PP, #0x7e30]  ; [pp+0x7e30] List(7) [0x1, 0x1, 0x1, 0, "tag", 0, Null]
    // 0x130cc10: r0 = Inst.find()
    //     0x130cc10: bl              #0x887b14  ; [package:get/get_instance/src/extension_instance.dart] ::Inst.find
    // 0x130cc14: mov             x1, x0
    // 0x130cc18: r2 = "token"
    //     0x130cc18: add             x2, PP, #0xe, lsl #12  ; [pp+0xe958] "token"
    //     0x130cc1c: ldr             x2, [x2, #0x958]
    // 0x130cc20: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x130cc20: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x130cc24: r0 = getString()
    //     0x130cc24: bl              #0x8939fc  ; [package:customer_app/app/data/local/preference/preference_manager_impl.dart] PreferenceManagerImpl::getString
    // 0x130cc28: ldur            x2, [fp, #-8]
    // 0x130cc2c: r1 = Function '<anonymous closure>':.
    //     0x130cc2c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a50] AnonymousClosure: (0x130cc6c), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x136021c)
    //     0x130cc30: ldr             x1, [x1, #0xa50]
    // 0x130cc34: stur            x0, [fp, #-8]
    // 0x130cc38: r0 = AllocateClosure()
    //     0x130cc38: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130cc3c: r16 = <Null?>
    //     0x130cc3c: ldr             x16, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    // 0x130cc40: ldur            lr, [fp, #-8]
    // 0x130cc44: stp             lr, x16, [SP, #8]
    // 0x130cc48: str             x0, [SP]
    // 0x130cc4c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130cc4c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130cc50: r0 = then()
    //     0x130cc50: bl              #0x167b220  ; [dart:async] _Future::then
    // 0x130cc54: r0 = Null
    //     0x130cc54: mov             x0, NULL
    // 0x130cc58: LeaveFrame
    //     0x130cc58: mov             SP, fp
    //     0x130cc5c: ldp             fp, lr, [SP], #0x10
    // 0x130cc60: ret
    //     0x130cc60: ret             
    // 0x130cc64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130cc64: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130cc68: b               #0x130c8c8
  }
  [closure] Future<Null> <anonymous closure>(dynamic, String) async {
    // ** addr: 0x130cc6c, size: 0xd54
    // 0x130cc6c: EnterFrame
    //     0x130cc6c: stp             fp, lr, [SP, #-0x10]!
    //     0x130cc70: mov             fp, SP
    // 0x130cc74: AllocStack(0x60)
    //     0x130cc74: sub             SP, SP, #0x60
    // 0x130cc78: SetupParameters(CustomizedPage this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x130cc78: stur            NULL, [fp, #-8]
    //     0x130cc7c: movz            x0, #0
    //     0x130cc80: add             x1, fp, w0, sxtw #2
    //     0x130cc84: ldr             x1, [x1, #0x18]
    //     0x130cc88: add             x2, fp, w0, sxtw #2
    //     0x130cc8c: ldr             x2, [x2, #0x10]
    //     0x130cc90: stur            x2, [fp, #-0x18]
    //     0x130cc94: ldur            w3, [x1, #0x17]
    //     0x130cc98: add             x3, x3, HEAP, lsl #32
    //     0x130cc9c: stur            x3, [fp, #-0x10]
    // 0x130cca0: CheckStackOverflow
    //     0x130cca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130cca4: cmp             SP, x16
    //     0x130cca8: b.ls            #0x130d9b8
    // 0x130ccac: InitAsync() -> Future<Null?>
    //     0x130ccac: ldr             x0, [PP, #0x78]  ; [pp+0x78] TypeArguments: <Null?>
    //     0x130ccb0: bl              #0x6326e0  ; InitAsyncStub
    // 0x130ccb4: ldur            x0, [fp, #-0x18]
    // 0x130ccb8: r1 = LoadClassIdInstr(r0)
    //     0x130ccb8: ldur            x1, [x0, #-1]
    //     0x130ccbc: ubfx            x1, x1, #0xc, #0x14
    // 0x130ccc0: r16 = ""
    //     0x130ccc0: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130ccc4: stp             x16, x0, [SP]
    // 0x130ccc8: mov             x0, x1
    // 0x130cccc: mov             lr, x0
    // 0x130ccd0: ldr             lr, [x21, lr, lsl #3]
    // 0x130ccd4: blr             lr
    // 0x130ccd8: tbz             w0, #4, #0x130d620
    // 0x130ccdc: ldur            x0, [fp, #-0x10]
    // 0x130cce0: LoadField: r1 = r0->field_f
    //     0x130cce0: ldur            w1, [x0, #0xf]
    // 0x130cce4: DecompressPointer r1
    //     0x130cce4: add             x1, x1, HEAP, lsl #32
    // 0x130cce8: r0 = controller()
    //     0x130cce8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ccec: LoadField: r1 = r0->field_63
    //     0x130ccec: ldur            w1, [x0, #0x63]
    // 0x130ccf0: DecompressPointer r1
    //     0x130ccf0: add             x1, x1, HEAP, lsl #32
    // 0x130ccf4: cmp             w1, NULL
    // 0x130ccf8: b.ne            #0x130cd04
    // 0x130ccfc: r0 = Null
    //     0x130ccfc: mov             x0, NULL
    // 0x130cd00: b               #0x130cd30
    // 0x130cd04: LoadField: r0 = r1->field_23
    //     0x130cd04: ldur            w0, [x1, #0x23]
    // 0x130cd08: DecompressPointer r0
    //     0x130cd08: add             x0, x0, HEAP, lsl #32
    // 0x130cd0c: cmp             w0, NULL
    // 0x130cd10: b.ne            #0x130cd1c
    // 0x130cd14: r0 = Null
    //     0x130cd14: mov             x0, NULL
    // 0x130cd18: b               #0x130cd30
    // 0x130cd1c: LoadField: r1 = r0->field_b
    //     0x130cd1c: ldur            w1, [x0, #0xb]
    // 0x130cd20: cbnz            w1, #0x130cd2c
    // 0x130cd24: r0 = false
    //     0x130cd24: add             x0, NULL, #0x30  ; false
    // 0x130cd28: b               #0x130cd30
    // 0x130cd2c: r0 = true
    //     0x130cd2c: add             x0, NULL, #0x20  ; true
    // 0x130cd30: cmp             w0, NULL
    // 0x130cd34: b.eq            #0x130ce04
    // 0x130cd38: tbnz            w0, #4, #0x130ce04
    // 0x130cd3c: ldur            x0, [fp, #-0x10]
    // 0x130cd40: LoadField: r1 = r0->field_f
    //     0x130cd40: ldur            w1, [x0, #0xf]
    // 0x130cd44: DecompressPointer r1
    //     0x130cd44: add             x1, x1, HEAP, lsl #32
    // 0x130cd48: r0 = controller()
    //     0x130cd48: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130cd4c: LoadField: r2 = r0->field_9b
    //     0x130cd4c: ldur            w2, [x0, #0x9b]
    // 0x130cd50: DecompressPointer r2
    //     0x130cd50: add             x2, x2, HEAP, lsl #32
    // 0x130cd54: ldur            x0, [fp, #-0x10]
    // 0x130cd58: stur            x2, [fp, #-0x18]
    // 0x130cd5c: LoadField: r1 = r0->field_f
    //     0x130cd5c: ldur            w1, [x0, #0xf]
    // 0x130cd60: DecompressPointer r1
    //     0x130cd60: add             x1, x1, HEAP, lsl #32
    // 0x130cd64: r0 = controller()
    //     0x130cd64: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130cd68: LoadField: r1 = r0->field_63
    //     0x130cd68: ldur            w1, [x0, #0x63]
    // 0x130cd6c: DecompressPointer r1
    //     0x130cd6c: add             x1, x1, HEAP, lsl #32
    // 0x130cd70: cmp             w1, NULL
    // 0x130cd74: b.ne            #0x130cd84
    // 0x130cd78: r0 = ProductCustomisation()
    //     0x130cd78: bl              #0x8a2210  ; AllocateProductCustomisationStub -> ProductCustomisation (size=0x34)
    // 0x130cd7c: mov             x2, x0
    // 0x130cd80: b               #0x130cd88
    // 0x130cd84: mov             x2, x1
    // 0x130cd88: ldur            x0, [fp, #-0x18]
    // 0x130cd8c: stur            x2, [fp, #-0x28]
    // 0x130cd90: LoadField: r1 = r0->field_b
    //     0x130cd90: ldur            w1, [x0, #0xb]
    // 0x130cd94: LoadField: r3 = r0->field_f
    //     0x130cd94: ldur            w3, [x0, #0xf]
    // 0x130cd98: DecompressPointer r3
    //     0x130cd98: add             x3, x3, HEAP, lsl #32
    // 0x130cd9c: LoadField: r4 = r3->field_b
    //     0x130cd9c: ldur            w4, [x3, #0xb]
    // 0x130cda0: r3 = LoadInt32Instr(r1)
    //     0x130cda0: sbfx            x3, x1, #1, #0x1f
    // 0x130cda4: stur            x3, [fp, #-0x20]
    // 0x130cda8: r1 = LoadInt32Instr(r4)
    //     0x130cda8: sbfx            x1, x4, #1, #0x1f
    // 0x130cdac: cmp             x3, x1
    // 0x130cdb0: b.ne            #0x130cdbc
    // 0x130cdb4: mov             x1, x0
    // 0x130cdb8: r0 = _growToNextCapacity()
    //     0x130cdb8: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x130cdbc: ldur            x0, [fp, #-0x18]
    // 0x130cdc0: ldur            x2, [fp, #-0x20]
    // 0x130cdc4: add             x1, x2, #1
    // 0x130cdc8: lsl             x3, x1, #1
    // 0x130cdcc: StoreField: r0->field_b = r3
    //     0x130cdcc: stur            w3, [x0, #0xb]
    // 0x130cdd0: LoadField: r1 = r0->field_f
    //     0x130cdd0: ldur            w1, [x0, #0xf]
    // 0x130cdd4: DecompressPointer r1
    //     0x130cdd4: add             x1, x1, HEAP, lsl #32
    // 0x130cdd8: ldur            x0, [fp, #-0x28]
    // 0x130cddc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x130cddc: add             x25, x1, x2, lsl #2
    //     0x130cde0: add             x25, x25, #0xf
    //     0x130cde4: str             w0, [x25]
    //     0x130cde8: tbz             w0, #0, #0x130ce04
    //     0x130cdec: ldurb           w16, [x1, #-1]
    //     0x130cdf0: ldurb           w17, [x0, #-1]
    //     0x130cdf4: and             x16, x17, x16, lsr #2
    //     0x130cdf8: tst             x16, HEAP, lsr #32
    //     0x130cdfc: b.eq            #0x130ce04
    //     0x130ce00: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130ce04: ldur            x0, [fp, #-0x10]
    // 0x130ce08: LoadField: r1 = r0->field_f
    //     0x130ce08: ldur            w1, [x0, #0xf]
    // 0x130ce0c: DecompressPointer r1
    //     0x130ce0c: add             x1, x1, HEAP, lsl #32
    // 0x130ce10: r0 = controller()
    //     0x130ce10: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ce14: LoadField: r1 = r0->field_8f
    //     0x130ce14: ldur            w1, [x0, #0x8f]
    // 0x130ce18: DecompressPointer r1
    //     0x130ce18: add             x1, x1, HEAP, lsl #32
    // 0x130ce1c: r0 = value()
    //     0x130ce1c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ce20: mov             x2, x0
    // 0x130ce24: ldur            x0, [fp, #-0x10]
    // 0x130ce28: stur            x2, [fp, #-0x18]
    // 0x130ce2c: LoadField: r1 = r0->field_f
    //     0x130ce2c: ldur            w1, [x0, #0xf]
    // 0x130ce30: DecompressPointer r1
    //     0x130ce30: add             x1, x1, HEAP, lsl #32
    // 0x130ce34: r0 = controller()
    //     0x130ce34: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ce38: LoadField: r1 = r0->field_93
    //     0x130ce38: ldur            w1, [x0, #0x93]
    // 0x130ce3c: DecompressPointer r1
    //     0x130ce3c: add             x1, x1, HEAP, lsl #32
    // 0x130ce40: r0 = value()
    //     0x130ce40: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ce44: mov             x2, x0
    // 0x130ce48: ldur            x0, [fp, #-0x10]
    // 0x130ce4c: stur            x2, [fp, #-0x28]
    // 0x130ce50: LoadField: r1 = r0->field_f
    //     0x130ce50: ldur            w1, [x0, #0xf]
    // 0x130ce54: DecompressPointer r1
    //     0x130ce54: add             x1, x1, HEAP, lsl #32
    // 0x130ce58: r0 = controller()
    //     0x130ce58: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ce5c: LoadField: r1 = r0->field_ab
    //     0x130ce5c: ldur            w1, [x0, #0xab]
    // 0x130ce60: DecompressPointer r1
    //     0x130ce60: add             x1, x1, HEAP, lsl #32
    // 0x130ce64: r0 = value()
    //     0x130ce64: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130ce68: mov             x2, x0
    // 0x130ce6c: ldur            x0, [fp, #-0x10]
    // 0x130ce70: stur            x2, [fp, #-0x30]
    // 0x130ce74: LoadField: r1 = r0->field_f
    //     0x130ce74: ldur            w1, [x0, #0xf]
    // 0x130ce78: DecompressPointer r1
    //     0x130ce78: add             x1, x1, HEAP, lsl #32
    // 0x130ce7c: r0 = controller()
    //     0x130ce7c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ce80: LoadField: r2 = r0->field_9b
    //     0x130ce80: ldur            w2, [x0, #0x9b]
    // 0x130ce84: DecompressPointer r2
    //     0x130ce84: add             x2, x2, HEAP, lsl #32
    // 0x130ce88: ldur            x0, [fp, #-0x10]
    // 0x130ce8c: stur            x2, [fp, #-0x38]
    // 0x130ce90: LoadField: r1 = r0->field_f
    //     0x130ce90: ldur            w1, [x0, #0xf]
    // 0x130ce94: DecompressPointer r1
    //     0x130ce94: add             x1, x1, HEAP, lsl #32
    // 0x130ce98: r0 = controller()
    //     0x130ce98: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130ce9c: LoadField: r1 = r0->field_97
    //     0x130ce9c: ldur            w1, [x0, #0x97]
    // 0x130cea0: DecompressPointer r1
    //     0x130cea0: add             x1, x1, HEAP, lsl #32
    // 0x130cea4: r0 = value()
    //     0x130cea4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130cea8: stur            x0, [fp, #-0x40]
    // 0x130ceac: r0 = CustomizedRequest()
    //     0x130ceac: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x130ceb0: mov             x2, x0
    // 0x130ceb4: ldur            x0, [fp, #-0x18]
    // 0x130ceb8: stur            x2, [fp, #-0x48]
    // 0x130cebc: StoreField: r2->field_7 = r0
    //     0x130cebc: stur            w0, [x2, #7]
    // 0x130cec0: ldur            x0, [fp, #-0x28]
    // 0x130cec4: StoreField: r2->field_b = r0
    //     0x130cec4: stur            w0, [x2, #0xb]
    // 0x130cec8: ldur            x0, [fp, #-0x30]
    // 0x130cecc: StoreField: r2->field_f = r0
    //     0x130cecc: stur            w0, [x2, #0xf]
    // 0x130ced0: ldur            x0, [fp, #-0x38]
    // 0x130ced4: StoreField: r2->field_13 = r0
    //     0x130ced4: stur            w0, [x2, #0x13]
    // 0x130ced8: ldur            x0, [fp, #-0x40]
    // 0x130cedc: ArrayStore: r2[0] = r0  ; List_4
    //     0x130cedc: stur            w0, [x2, #0x17]
    // 0x130cee0: ldur            x0, [fp, #-0x10]
    // 0x130cee4: LoadField: r1 = r0->field_f
    //     0x130cee4: ldur            w1, [x0, #0xf]
    // 0x130cee8: DecompressPointer r1
    //     0x130cee8: add             x1, x1, HEAP, lsl #32
    // 0x130ceec: r0 = controller()
    //     0x130ceec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130cef0: LoadField: r1 = r0->field_5f
    //     0x130cef0: ldur            w1, [x0, #0x5f]
    // 0x130cef4: DecompressPointer r1
    //     0x130cef4: add             x1, x1, HEAP, lsl #32
    // 0x130cef8: r0 = value()
    //     0x130cef8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130cefc: LoadField: r1 = r0->field_1b
    //     0x130cefc: ldur            w1, [x0, #0x1b]
    // 0x130cf00: DecompressPointer r1
    //     0x130cf00: add             x1, x1, HEAP, lsl #32
    // 0x130cf04: cmp             w1, NULL
    // 0x130cf08: b.ne            #0x130cf14
    // 0x130cf0c: r0 = Null
    //     0x130cf0c: mov             x0, NULL
    // 0x130cf10: b               #0x130cf2c
    // 0x130cf14: LoadField: r0 = r1->field_b
    //     0x130cf14: ldur            w0, [x1, #0xb]
    // 0x130cf18: cbz             w0, #0x130cf24
    // 0x130cf1c: r1 = false
    //     0x130cf1c: add             x1, NULL, #0x30  ; false
    // 0x130cf20: b               #0x130cf28
    // 0x130cf24: r1 = true
    //     0x130cf24: add             x1, NULL, #0x20  ; true
    // 0x130cf28: mov             x0, x1
    // 0x130cf2c: cmp             w0, NULL
    // 0x130cf30: b.eq            #0x130cf38
    // 0x130cf34: tbz             w0, #4, #0x130cfc4
    // 0x130cf38: ldur            x0, [fp, #-0x10]
    // 0x130cf3c: LoadField: r1 = r0->field_f
    //     0x130cf3c: ldur            w1, [x0, #0xf]
    // 0x130cf40: DecompressPointer r1
    //     0x130cf40: add             x1, x1, HEAP, lsl #32
    // 0x130cf44: r0 = controller()
    //     0x130cf44: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130cf48: LoadField: r1 = r0->field_5f
    //     0x130cf48: ldur            w1, [x0, #0x5f]
    // 0x130cf4c: DecompressPointer r1
    //     0x130cf4c: add             x1, x1, HEAP, lsl #32
    // 0x130cf50: r0 = value()
    //     0x130cf50: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130cf54: LoadField: r1 = r0->field_1b
    //     0x130cf54: ldur            w1, [x0, #0x1b]
    // 0x130cf58: DecompressPointer r1
    //     0x130cf58: add             x1, x1, HEAP, lsl #32
    // 0x130cf5c: cmp             w1, NULL
    // 0x130cf60: b.ne            #0x130cf6c
    // 0x130cf64: r0 = Null
    //     0x130cf64: mov             x0, NULL
    // 0x130cf68: b               #0x130cfb0
    // 0x130cf6c: r0 = first()
    //     0x130cf6c: bl              #0x7e0350  ; [dart:core] _GrowableList::first
    // 0x130cf70: cmp             w0, NULL
    // 0x130cf74: b.ne            #0x130cf80
    // 0x130cf78: r0 = Null
    //     0x130cf78: mov             x0, NULL
    // 0x130cf7c: b               #0x130cfb0
    // 0x130cf80: LoadField: r1 = r0->field_13
    //     0x130cf80: ldur            w1, [x0, #0x13]
    // 0x130cf84: DecompressPointer r1
    //     0x130cf84: add             x1, x1, HEAP, lsl #32
    // 0x130cf88: cmp             w1, NULL
    // 0x130cf8c: b.ne            #0x130cf98
    // 0x130cf90: r0 = Null
    //     0x130cf90: mov             x0, NULL
    // 0x130cf94: b               #0x130cfb0
    // 0x130cf98: LoadField: r0 = r1->field_7
    //     0x130cf98: ldur            w0, [x1, #7]
    // 0x130cf9c: cbz             w0, #0x130cfa8
    // 0x130cfa0: r1 = false
    //     0x130cfa0: add             x1, NULL, #0x30  ; false
    // 0x130cfa4: b               #0x130cfac
    // 0x130cfa8: r1 = true
    //     0x130cfa8: add             x1, NULL, #0x20  ; true
    // 0x130cfac: mov             x0, x1
    // 0x130cfb0: cmp             w0, NULL
    // 0x130cfb4: b.ne            #0x130cfc0
    // 0x130cfb8: ldur            x1, [fp, #-0x10]
    // 0x130cfbc: b               #0x130d2f8
    // 0x130cfc0: tbnz            w0, #4, #0x130d2f4
    // 0x130cfc4: ldur            x0, [fp, #-0x10]
    // 0x130cfc8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130cfc8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130cfcc: ldr             x0, [x0, #0x1c80]
    //     0x130cfd0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130cfd4: cmp             w0, w16
    //     0x130cfd8: b.ne            #0x130cfe4
    //     0x130cfdc: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130cfe0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130cfe4: r1 = Null
    //     0x130cfe4: mov             x1, NULL
    // 0x130cfe8: r2 = 44
    //     0x130cfe8: movz            x2, #0x2c
    // 0x130cfec: r0 = AllocateArray()
    //     0x130cfec: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130cff0: stur            x0, [fp, #-0x18]
    // 0x130cff4: r16 = "couponCode"
    //     0x130cff4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x130cff8: ldr             x16, [x16, #0x310]
    // 0x130cffc: StoreField: r0->field_f = r16
    //     0x130cffc: stur            w16, [x0, #0xf]
    // 0x130d000: r16 = ""
    //     0x130d000: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130d004: StoreField: r0->field_13 = r16
    //     0x130d004: stur            w16, [x0, #0x13]
    // 0x130d008: r16 = "product_id"
    //     0x130d008: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130d00c: ldr             x16, [x16, #0x9b8]
    // 0x130d010: ArrayStore: r0[0] = r16  ; List_4
    //     0x130d010: stur            w16, [x0, #0x17]
    // 0x130d014: ldur            x2, [fp, #-0x10]
    // 0x130d018: LoadField: r1 = r2->field_f
    //     0x130d018: ldur            w1, [x2, #0xf]
    // 0x130d01c: DecompressPointer r1
    //     0x130d01c: add             x1, x1, HEAP, lsl #32
    // 0x130d020: r0 = controller()
    //     0x130d020: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d024: LoadField: r1 = r0->field_8f
    //     0x130d024: ldur            w1, [x0, #0x8f]
    // 0x130d028: DecompressPointer r1
    //     0x130d028: add             x1, x1, HEAP, lsl #32
    // 0x130d02c: r0 = value()
    //     0x130d02c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d030: ldur            x1, [fp, #-0x18]
    // 0x130d034: ArrayStore: r1[3] = r0  ; List_4
    //     0x130d034: add             x25, x1, #0x1b
    //     0x130d038: str             w0, [x25]
    //     0x130d03c: tbz             w0, #0, #0x130d058
    //     0x130d040: ldurb           w16, [x1, #-1]
    //     0x130d044: ldurb           w17, [x0, #-1]
    //     0x130d048: and             x16, x17, x16, lsr #2
    //     0x130d04c: tst             x16, HEAP, lsr #32
    //     0x130d050: b.eq            #0x130d058
    //     0x130d054: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d058: ldur            x0, [fp, #-0x18]
    // 0x130d05c: r16 = "sku_id"
    //     0x130d05c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130d060: ldr             x16, [x16, #0x498]
    // 0x130d064: StoreField: r0->field_1f = r16
    //     0x130d064: stur            w16, [x0, #0x1f]
    // 0x130d068: ldur            x2, [fp, #-0x10]
    // 0x130d06c: LoadField: r1 = r2->field_f
    //     0x130d06c: ldur            w1, [x2, #0xf]
    // 0x130d070: DecompressPointer r1
    //     0x130d070: add             x1, x1, HEAP, lsl #32
    // 0x130d074: r0 = controller()
    //     0x130d074: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d078: LoadField: r1 = r0->field_93
    //     0x130d078: ldur            w1, [x0, #0x93]
    // 0x130d07c: DecompressPointer r1
    //     0x130d07c: add             x1, x1, HEAP, lsl #32
    // 0x130d080: r0 = value()
    //     0x130d080: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d084: ldur            x1, [fp, #-0x18]
    // 0x130d088: ArrayStore: r1[5] = r0  ; List_4
    //     0x130d088: add             x25, x1, #0x23
    //     0x130d08c: str             w0, [x25]
    //     0x130d090: tbz             w0, #0, #0x130d0ac
    //     0x130d094: ldurb           w16, [x1, #-1]
    //     0x130d098: ldurb           w17, [x0, #-1]
    //     0x130d09c: and             x16, x17, x16, lsr #2
    //     0x130d0a0: tst             x16, HEAP, lsr #32
    //     0x130d0a4: b.eq            #0x130d0ac
    //     0x130d0a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d0ac: ldur            x0, [fp, #-0x18]
    // 0x130d0b0: r16 = "coming_from"
    //     0x130d0b0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130d0b4: ldr             x16, [x16, #0x328]
    // 0x130d0b8: StoreField: r0->field_27 = r16
    //     0x130d0b8: stur            w16, [x0, #0x27]
    // 0x130d0bc: r16 = "buyNow"
    //     0x130d0bc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130d0c0: ldr             x16, [x16, #0x358]
    // 0x130d0c4: StoreField: r0->field_2b = r16
    //     0x130d0c4: stur            w16, [x0, #0x2b]
    // 0x130d0c8: r16 = "quantity"
    //     0x130d0c8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130d0cc: ldr             x16, [x16, #0x428]
    // 0x130d0d0: StoreField: r0->field_2f = r16
    //     0x130d0d0: stur            w16, [x0, #0x2f]
    // 0x130d0d4: ldur            x2, [fp, #-0x10]
    // 0x130d0d8: LoadField: r1 = r2->field_f
    //     0x130d0d8: ldur            w1, [x2, #0xf]
    // 0x130d0dc: DecompressPointer r1
    //     0x130d0dc: add             x1, x1, HEAP, lsl #32
    // 0x130d0e0: r0 = controller()
    //     0x130d0e0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d0e4: LoadField: r1 = r0->field_ab
    //     0x130d0e4: ldur            w1, [x0, #0xab]
    // 0x130d0e8: DecompressPointer r1
    //     0x130d0e8: add             x1, x1, HEAP, lsl #32
    // 0x130d0ec: r0 = value()
    //     0x130d0ec: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d0f0: ldur            x1, [fp, #-0x18]
    // 0x130d0f4: ArrayStore: r1[9] = r0  ; List_4
    //     0x130d0f4: add             x25, x1, #0x33
    //     0x130d0f8: str             w0, [x25]
    //     0x130d0fc: tbz             w0, #0, #0x130d118
    //     0x130d100: ldurb           w16, [x1, #-1]
    //     0x130d104: ldurb           w17, [x0, #-1]
    //     0x130d108: and             x16, x17, x16, lsr #2
    //     0x130d10c: tst             x16, HEAP, lsr #32
    //     0x130d110: b.eq            #0x130d118
    //     0x130d114: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d118: ldur            x3, [fp, #-0x18]
    // 0x130d11c: r16 = "previousScreenSource"
    //     0x130d11c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130d120: ldr             x16, [x16, #0x448]
    // 0x130d124: StoreField: r3->field_37 = r16
    //     0x130d124: stur            w16, [x3, #0x37]
    // 0x130d128: r16 = "product_page"
    //     0x130d128: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130d12c: ldr             x16, [x16, #0x480]
    // 0x130d130: StoreField: r3->field_3b = r16
    //     0x130d130: stur            w16, [x3, #0x3b]
    // 0x130d134: r16 = "customization_request"
    //     0x130d134: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130d138: ldr             x16, [x16, #0x2b8]
    // 0x130d13c: StoreField: r3->field_3f = r16
    //     0x130d13c: stur            w16, [x3, #0x3f]
    // 0x130d140: mov             x1, x3
    // 0x130d144: ldur            x0, [fp, #-0x48]
    // 0x130d148: ArrayStore: r1[13] = r0  ; List_4
    //     0x130d148: add             x25, x1, #0x43
    //     0x130d14c: str             w0, [x25]
    //     0x130d150: tbz             w0, #0, #0x130d16c
    //     0x130d154: ldurb           w16, [x1, #-1]
    //     0x130d158: ldurb           w17, [x0, #-1]
    //     0x130d15c: and             x16, x17, x16, lsr #2
    //     0x130d160: tst             x16, HEAP, lsr #32
    //     0x130d164: b.eq            #0x130d16c
    //     0x130d168: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d16c: r16 = "customization_prize"
    //     0x130d16c: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130d170: ldr             x16, [x16, #0x2e8]
    // 0x130d174: StoreField: r3->field_47 = r16
    //     0x130d174: stur            w16, [x3, #0x47]
    // 0x130d178: r1 = Null
    //     0x130d178: mov             x1, NULL
    // 0x130d17c: r2 = 4
    //     0x130d17c: movz            x2, #0x4
    // 0x130d180: r0 = AllocateArray()
    //     0x130d180: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130d184: stur            x0, [fp, #-0x28]
    // 0x130d188: r16 = "₹"
    //     0x130d188: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130d18c: ldr             x16, [x16, #0x360]
    // 0x130d190: StoreField: r0->field_f = r16
    //     0x130d190: stur            w16, [x0, #0xf]
    // 0x130d194: r1 = Function '<anonymous closure>': static.
    //     0x130d194: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x130d198: ldr             x1, [x1, #0x1a0]
    // 0x130d19c: r2 = Null
    //     0x130d19c: mov             x2, NULL
    // 0x130d1a0: r0 = AllocateClosure()
    //     0x130d1a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130d1a4: mov             x3, x0
    // 0x130d1a8: r1 = Null
    //     0x130d1a8: mov             x1, NULL
    // 0x130d1ac: r2 = Null
    //     0x130d1ac: mov             x2, NULL
    // 0x130d1b0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x130d1b0: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x130d1b4: r0 = NumberFormat._forPattern()
    //     0x130d1b4: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x130d1b8: mov             x2, x0
    // 0x130d1bc: ldur            x0, [fp, #-0x10]
    // 0x130d1c0: stur            x2, [fp, #-0x30]
    // 0x130d1c4: LoadField: r1 = r0->field_f
    //     0x130d1c4: ldur            w1, [x0, #0xf]
    // 0x130d1c8: DecompressPointer r1
    //     0x130d1c8: add             x1, x1, HEAP, lsl #32
    // 0x130d1cc: r0 = controller()
    //     0x130d1cc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d1d0: LoadField: r1 = r0->field_67
    //     0x130d1d0: ldur            w1, [x0, #0x67]
    // 0x130d1d4: DecompressPointer r1
    //     0x130d1d4: add             x1, x1, HEAP, lsl #32
    // 0x130d1d8: r0 = value()
    //     0x130d1d8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d1dc: ldur            x1, [fp, #-0x30]
    // 0x130d1e0: mov             x2, x0
    // 0x130d1e4: r0 = format()
    //     0x130d1e4: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130d1e8: ldur            x1, [fp, #-0x28]
    // 0x130d1ec: ArrayStore: r1[1] = r0  ; List_4
    //     0x130d1ec: add             x25, x1, #0x13
    //     0x130d1f0: str             w0, [x25]
    //     0x130d1f4: tbz             w0, #0, #0x130d210
    //     0x130d1f8: ldurb           w16, [x1, #-1]
    //     0x130d1fc: ldurb           w17, [x0, #-1]
    //     0x130d200: and             x16, x17, x16, lsr #2
    //     0x130d204: tst             x16, HEAP, lsr #32
    //     0x130d208: b.eq            #0x130d210
    //     0x130d20c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d210: ldur            x16, [fp, #-0x28]
    // 0x130d214: str             x16, [SP]
    // 0x130d218: r0 = _interpolate()
    //     0x130d218: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130d21c: ldur            x1, [fp, #-0x18]
    // 0x130d220: ArrayStore: r1[15] = r0  ; List_4
    //     0x130d220: add             x25, x1, #0x4b
    //     0x130d224: str             w0, [x25]
    //     0x130d228: tbz             w0, #0, #0x130d244
    //     0x130d22c: ldurb           w16, [x1, #-1]
    //     0x130d230: ldurb           w17, [x0, #-1]
    //     0x130d234: and             x16, x17, x16, lsr #2
    //     0x130d238: tst             x16, HEAP, lsr #32
    //     0x130d23c: b.eq            #0x130d244
    //     0x130d240: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d244: ldur            x0, [fp, #-0x18]
    // 0x130d248: r16 = "is_skipped_address"
    //     0x130d248: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130d24c: ldr             x16, [x16, #0xb80]
    // 0x130d250: StoreField: r0->field_4f = r16
    //     0x130d250: stur            w16, [x0, #0x4f]
    // 0x130d254: r16 = false
    //     0x130d254: add             x16, NULL, #0x30  ; false
    // 0x130d258: StoreField: r0->field_53 = r16
    //     0x130d258: stur            w16, [x0, #0x53]
    // 0x130d25c: r16 = "checkout_id"
    //     0x130d25c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130d260: ldr             x16, [x16, #0xb88]
    // 0x130d264: StoreField: r0->field_57 = r16
    //     0x130d264: stur            w16, [x0, #0x57]
    // 0x130d268: r16 = ""
    //     0x130d268: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130d26c: StoreField: r0->field_5b = r16
    //     0x130d26c: stur            w16, [x0, #0x5b]
    // 0x130d270: r16 = "user_data"
    //     0x130d270: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x130d274: ldr             x16, [x16, #0x58]
    // 0x130d278: StoreField: r0->field_5f = r16
    //     0x130d278: stur            w16, [x0, #0x5f]
    // 0x130d27c: ldur            x1, [fp, #-0x10]
    // 0x130d280: LoadField: r2 = r1->field_f
    //     0x130d280: ldur            w2, [x1, #0xf]
    // 0x130d284: DecompressPointer r2
    //     0x130d284: add             x2, x2, HEAP, lsl #32
    // 0x130d288: mov             x1, x2
    // 0x130d28c: r0 = controller()
    //     0x130d28c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d290: LoadField: r1 = r0->field_5f
    //     0x130d290: ldur            w1, [x0, #0x5f]
    // 0x130d294: DecompressPointer r1
    //     0x130d294: add             x1, x1, HEAP, lsl #32
    // 0x130d298: r0 = value()
    //     0x130d298: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d29c: ldur            x1, [fp, #-0x18]
    // 0x130d2a0: ArrayStore: r1[21] = r0  ; List_4
    //     0x130d2a0: add             x25, x1, #0x63
    //     0x130d2a4: str             w0, [x25]
    //     0x130d2a8: tbz             w0, #0, #0x130d2c4
    //     0x130d2ac: ldurb           w16, [x1, #-1]
    //     0x130d2b0: ldurb           w17, [x0, #-1]
    //     0x130d2b4: and             x16, x17, x16, lsr #2
    //     0x130d2b8: tst             x16, HEAP, lsr #32
    //     0x130d2bc: b.eq            #0x130d2c4
    //     0x130d2c0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d2c4: r16 = <String, dynamic>
    //     0x130d2c4: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130d2c8: ldur            lr, [fp, #-0x18]
    // 0x130d2cc: stp             lr, x16, [SP]
    // 0x130d2d0: r0 = Map._fromLiteral()
    //     0x130d2d0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130d2d4: r16 = "/checkout_request_address_page"
    //     0x130d2d4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9e8] "/checkout_request_address_page"
    //     0x130d2d8: ldr             x16, [x16, #0x9e8]
    // 0x130d2dc: stp             x16, NULL, [SP, #8]
    // 0x130d2e0: str             x0, [SP]
    // 0x130d2e4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130d2e4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130d2e8: ldr             x4, [x4, #0x438]
    // 0x130d2ec: r0 = GetNavigation.toNamed()
    //     0x130d2ec: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130d2f0: b               #0x130d9b0
    // 0x130d2f4: ldur            x1, [fp, #-0x10]
    // 0x130d2f8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130d2f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130d2fc: ldr             x0, [x0, #0x1c80]
    //     0x130d300: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130d304: cmp             w0, w16
    //     0x130d308: b.ne            #0x130d314
    //     0x130d30c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130d310: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130d314: r1 = Null
    //     0x130d314: mov             x1, NULL
    // 0x130d318: r2 = 44
    //     0x130d318: movz            x2, #0x2c
    // 0x130d31c: r0 = AllocateArray()
    //     0x130d31c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130d320: stur            x0, [fp, #-0x18]
    // 0x130d324: r16 = "couponCode"
    //     0x130d324: add             x16, PP, #0x22, lsl #12  ; [pp+0x22310] "couponCode"
    //     0x130d328: ldr             x16, [x16, #0x310]
    // 0x130d32c: StoreField: r0->field_f = r16
    //     0x130d32c: stur            w16, [x0, #0xf]
    // 0x130d330: r16 = ""
    //     0x130d330: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130d334: StoreField: r0->field_13 = r16
    //     0x130d334: stur            w16, [x0, #0x13]
    // 0x130d338: r16 = "product_id"
    //     0x130d338: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130d33c: ldr             x16, [x16, #0x9b8]
    // 0x130d340: ArrayStore: r0[0] = r16  ; List_4
    //     0x130d340: stur            w16, [x0, #0x17]
    // 0x130d344: ldur            x2, [fp, #-0x10]
    // 0x130d348: LoadField: r1 = r2->field_f
    //     0x130d348: ldur            w1, [x2, #0xf]
    // 0x130d34c: DecompressPointer r1
    //     0x130d34c: add             x1, x1, HEAP, lsl #32
    // 0x130d350: r0 = controller()
    //     0x130d350: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d354: LoadField: r1 = r0->field_8f
    //     0x130d354: ldur            w1, [x0, #0x8f]
    // 0x130d358: DecompressPointer r1
    //     0x130d358: add             x1, x1, HEAP, lsl #32
    // 0x130d35c: r0 = value()
    //     0x130d35c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d360: ldur            x1, [fp, #-0x18]
    // 0x130d364: ArrayStore: r1[3] = r0  ; List_4
    //     0x130d364: add             x25, x1, #0x1b
    //     0x130d368: str             w0, [x25]
    //     0x130d36c: tbz             w0, #0, #0x130d388
    //     0x130d370: ldurb           w16, [x1, #-1]
    //     0x130d374: ldurb           w17, [x0, #-1]
    //     0x130d378: and             x16, x17, x16, lsr #2
    //     0x130d37c: tst             x16, HEAP, lsr #32
    //     0x130d380: b.eq            #0x130d388
    //     0x130d384: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d388: ldur            x0, [fp, #-0x18]
    // 0x130d38c: r16 = "sku_id"
    //     0x130d38c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130d390: ldr             x16, [x16, #0x498]
    // 0x130d394: StoreField: r0->field_1f = r16
    //     0x130d394: stur            w16, [x0, #0x1f]
    // 0x130d398: ldur            x2, [fp, #-0x10]
    // 0x130d39c: LoadField: r1 = r2->field_f
    //     0x130d39c: ldur            w1, [x2, #0xf]
    // 0x130d3a0: DecompressPointer r1
    //     0x130d3a0: add             x1, x1, HEAP, lsl #32
    // 0x130d3a4: r0 = controller()
    //     0x130d3a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d3a8: LoadField: r1 = r0->field_93
    //     0x130d3a8: ldur            w1, [x0, #0x93]
    // 0x130d3ac: DecompressPointer r1
    //     0x130d3ac: add             x1, x1, HEAP, lsl #32
    // 0x130d3b0: r0 = value()
    //     0x130d3b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d3b4: ldur            x1, [fp, #-0x18]
    // 0x130d3b8: ArrayStore: r1[5] = r0  ; List_4
    //     0x130d3b8: add             x25, x1, #0x23
    //     0x130d3bc: str             w0, [x25]
    //     0x130d3c0: tbz             w0, #0, #0x130d3dc
    //     0x130d3c4: ldurb           w16, [x1, #-1]
    //     0x130d3c8: ldurb           w17, [x0, #-1]
    //     0x130d3cc: and             x16, x17, x16, lsr #2
    //     0x130d3d0: tst             x16, HEAP, lsr #32
    //     0x130d3d4: b.eq            #0x130d3dc
    //     0x130d3d8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d3dc: ldur            x0, [fp, #-0x18]
    // 0x130d3e0: r16 = "coming_from"
    //     0x130d3e0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130d3e4: ldr             x16, [x16, #0x328]
    // 0x130d3e8: StoreField: r0->field_27 = r16
    //     0x130d3e8: stur            w16, [x0, #0x27]
    // 0x130d3ec: r16 = "buyNow"
    //     0x130d3ec: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130d3f0: ldr             x16, [x16, #0x358]
    // 0x130d3f4: StoreField: r0->field_2b = r16
    //     0x130d3f4: stur            w16, [x0, #0x2b]
    // 0x130d3f8: r16 = "quantity"
    //     0x130d3f8: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130d3fc: ldr             x16, [x16, #0x428]
    // 0x130d400: StoreField: r0->field_2f = r16
    //     0x130d400: stur            w16, [x0, #0x2f]
    // 0x130d404: ldur            x2, [fp, #-0x10]
    // 0x130d408: LoadField: r1 = r2->field_f
    //     0x130d408: ldur            w1, [x2, #0xf]
    // 0x130d40c: DecompressPointer r1
    //     0x130d40c: add             x1, x1, HEAP, lsl #32
    // 0x130d410: r0 = controller()
    //     0x130d410: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d414: LoadField: r1 = r0->field_ab
    //     0x130d414: ldur            w1, [x0, #0xab]
    // 0x130d418: DecompressPointer r1
    //     0x130d418: add             x1, x1, HEAP, lsl #32
    // 0x130d41c: r0 = value()
    //     0x130d41c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d420: ldur            x1, [fp, #-0x18]
    // 0x130d424: ArrayStore: r1[9] = r0  ; List_4
    //     0x130d424: add             x25, x1, #0x33
    //     0x130d428: str             w0, [x25]
    //     0x130d42c: tbz             w0, #0, #0x130d448
    //     0x130d430: ldurb           w16, [x1, #-1]
    //     0x130d434: ldurb           w17, [x0, #-1]
    //     0x130d438: and             x16, x17, x16, lsr #2
    //     0x130d43c: tst             x16, HEAP, lsr #32
    //     0x130d440: b.eq            #0x130d448
    //     0x130d444: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d448: ldur            x3, [fp, #-0x18]
    // 0x130d44c: r16 = "previousScreenSource"
    //     0x130d44c: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130d450: ldr             x16, [x16, #0x448]
    // 0x130d454: StoreField: r3->field_37 = r16
    //     0x130d454: stur            w16, [x3, #0x37]
    // 0x130d458: r16 = "product_page"
    //     0x130d458: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130d45c: ldr             x16, [x16, #0x480]
    // 0x130d460: StoreField: r3->field_3b = r16
    //     0x130d460: stur            w16, [x3, #0x3b]
    // 0x130d464: r16 = "customization_request"
    //     0x130d464: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130d468: ldr             x16, [x16, #0x2b8]
    // 0x130d46c: StoreField: r3->field_3f = r16
    //     0x130d46c: stur            w16, [x3, #0x3f]
    // 0x130d470: mov             x1, x3
    // 0x130d474: ldur            x0, [fp, #-0x48]
    // 0x130d478: ArrayStore: r1[13] = r0  ; List_4
    //     0x130d478: add             x25, x1, #0x43
    //     0x130d47c: str             w0, [x25]
    //     0x130d480: tbz             w0, #0, #0x130d49c
    //     0x130d484: ldurb           w16, [x1, #-1]
    //     0x130d488: ldurb           w17, [x0, #-1]
    //     0x130d48c: and             x16, x17, x16, lsr #2
    //     0x130d490: tst             x16, HEAP, lsr #32
    //     0x130d494: b.eq            #0x130d49c
    //     0x130d498: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d49c: r16 = "customization_prize"
    //     0x130d49c: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130d4a0: ldr             x16, [x16, #0x2e8]
    // 0x130d4a4: StoreField: r3->field_47 = r16
    //     0x130d4a4: stur            w16, [x3, #0x47]
    // 0x130d4a8: r1 = Null
    //     0x130d4a8: mov             x1, NULL
    // 0x130d4ac: r2 = 4
    //     0x130d4ac: movz            x2, #0x4
    // 0x130d4b0: r0 = AllocateArray()
    //     0x130d4b0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130d4b4: stur            x0, [fp, #-0x28]
    // 0x130d4b8: r16 = "₹"
    //     0x130d4b8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130d4bc: ldr             x16, [x16, #0x360]
    // 0x130d4c0: StoreField: r0->field_f = r16
    //     0x130d4c0: stur            w16, [x0, #0xf]
    // 0x130d4c4: r1 = Function '<anonymous closure>': static.
    //     0x130d4c4: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x130d4c8: ldr             x1, [x1, #0x1a0]
    // 0x130d4cc: r2 = Null
    //     0x130d4cc: mov             x2, NULL
    // 0x130d4d0: r0 = AllocateClosure()
    //     0x130d4d0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130d4d4: mov             x3, x0
    // 0x130d4d8: r1 = Null
    //     0x130d4d8: mov             x1, NULL
    // 0x130d4dc: r2 = Null
    //     0x130d4dc: mov             x2, NULL
    // 0x130d4e0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x130d4e0: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x130d4e4: r0 = NumberFormat._forPattern()
    //     0x130d4e4: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x130d4e8: mov             x2, x0
    // 0x130d4ec: ldur            x0, [fp, #-0x10]
    // 0x130d4f0: stur            x2, [fp, #-0x30]
    // 0x130d4f4: LoadField: r1 = r0->field_f
    //     0x130d4f4: ldur            w1, [x0, #0xf]
    // 0x130d4f8: DecompressPointer r1
    //     0x130d4f8: add             x1, x1, HEAP, lsl #32
    // 0x130d4fc: r0 = controller()
    //     0x130d4fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d500: LoadField: r1 = r0->field_67
    //     0x130d500: ldur            w1, [x0, #0x67]
    // 0x130d504: DecompressPointer r1
    //     0x130d504: add             x1, x1, HEAP, lsl #32
    // 0x130d508: r0 = value()
    //     0x130d508: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d50c: ldur            x1, [fp, #-0x30]
    // 0x130d510: mov             x2, x0
    // 0x130d514: r0 = format()
    //     0x130d514: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130d518: ldur            x1, [fp, #-0x28]
    // 0x130d51c: ArrayStore: r1[1] = r0  ; List_4
    //     0x130d51c: add             x25, x1, #0x13
    //     0x130d520: str             w0, [x25]
    //     0x130d524: tbz             w0, #0, #0x130d540
    //     0x130d528: ldurb           w16, [x1, #-1]
    //     0x130d52c: ldurb           w17, [x0, #-1]
    //     0x130d530: and             x16, x17, x16, lsr #2
    //     0x130d534: tst             x16, HEAP, lsr #32
    //     0x130d538: b.eq            #0x130d540
    //     0x130d53c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d540: ldur            x16, [fp, #-0x28]
    // 0x130d544: str             x16, [SP]
    // 0x130d548: r0 = _interpolate()
    //     0x130d548: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130d54c: ldur            x1, [fp, #-0x18]
    // 0x130d550: ArrayStore: r1[15] = r0  ; List_4
    //     0x130d550: add             x25, x1, #0x4b
    //     0x130d554: str             w0, [x25]
    //     0x130d558: tbz             w0, #0, #0x130d574
    //     0x130d55c: ldurb           w16, [x1, #-1]
    //     0x130d560: ldurb           w17, [x0, #-1]
    //     0x130d564: and             x16, x17, x16, lsr #2
    //     0x130d568: tst             x16, HEAP, lsr #32
    //     0x130d56c: b.eq            #0x130d574
    //     0x130d570: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d574: ldur            x0, [fp, #-0x18]
    // 0x130d578: r16 = "is_skipped_address"
    //     0x130d578: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130d57c: ldr             x16, [x16, #0xb80]
    // 0x130d580: StoreField: r0->field_4f = r16
    //     0x130d580: stur            w16, [x0, #0x4f]
    // 0x130d584: r16 = true
    //     0x130d584: add             x16, NULL, #0x20  ; true
    // 0x130d588: StoreField: r0->field_53 = r16
    //     0x130d588: stur            w16, [x0, #0x53]
    // 0x130d58c: r16 = "checkout_id"
    //     0x130d58c: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b88] "checkout_id"
    //     0x130d590: ldr             x16, [x16, #0xb88]
    // 0x130d594: StoreField: r0->field_57 = r16
    //     0x130d594: stur            w16, [x0, #0x57]
    // 0x130d598: r16 = ""
    //     0x130d598: ldr             x16, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130d59c: StoreField: r0->field_5b = r16
    //     0x130d59c: stur            w16, [x0, #0x5b]
    // 0x130d5a0: r16 = "user_data"
    //     0x130d5a0: add             x16, PP, #0x26, lsl #12  ; [pp+0x26058] "user_data"
    //     0x130d5a4: ldr             x16, [x16, #0x58]
    // 0x130d5a8: StoreField: r0->field_5f = r16
    //     0x130d5a8: stur            w16, [x0, #0x5f]
    // 0x130d5ac: ldur            x2, [fp, #-0x10]
    // 0x130d5b0: LoadField: r1 = r2->field_f
    //     0x130d5b0: ldur            w1, [x2, #0xf]
    // 0x130d5b4: DecompressPointer r1
    //     0x130d5b4: add             x1, x1, HEAP, lsl #32
    // 0x130d5b8: r0 = controller()
    //     0x130d5b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d5bc: LoadField: r1 = r0->field_5f
    //     0x130d5bc: ldur            w1, [x0, #0x5f]
    // 0x130d5c0: DecompressPointer r1
    //     0x130d5c0: add             x1, x1, HEAP, lsl #32
    // 0x130d5c4: r0 = value()
    //     0x130d5c4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d5c8: ldur            x1, [fp, #-0x18]
    // 0x130d5cc: ArrayStore: r1[21] = r0  ; List_4
    //     0x130d5cc: add             x25, x1, #0x63
    //     0x130d5d0: str             w0, [x25]
    //     0x130d5d4: tbz             w0, #0, #0x130d5f0
    //     0x130d5d8: ldurb           w16, [x1, #-1]
    //     0x130d5dc: ldurb           w17, [x0, #-1]
    //     0x130d5e0: and             x16, x17, x16, lsr #2
    //     0x130d5e4: tst             x16, HEAP, lsr #32
    //     0x130d5e8: b.eq            #0x130d5f0
    //     0x130d5ec: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d5f0: r16 = <String, dynamic>
    //     0x130d5f0: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130d5f4: ldur            lr, [fp, #-0x18]
    // 0x130d5f8: stp             lr, x16, [SP]
    // 0x130d5fc: r0 = Map._fromLiteral()
    //     0x130d5fc: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130d600: r16 = "/checkout_order_summary_page"
    //     0x130d600: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9d8] "/checkout_order_summary_page"
    //     0x130d604: ldr             x16, [x16, #0x9d8]
    // 0x130d608: stp             x16, NULL, [SP, #8]
    // 0x130d60c: str             x0, [SP]
    // 0x130d610: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130d610: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130d614: ldr             x4, [x4, #0x438]
    // 0x130d618: r0 = GetNavigation.toNamed()
    //     0x130d618: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130d61c: b               #0x130d9b0
    // 0x130d620: ldur            x2, [fp, #-0x10]
    // 0x130d624: LoadField: r1 = r2->field_f
    //     0x130d624: ldur            w1, [x2, #0xf]
    // 0x130d628: DecompressPointer r1
    //     0x130d628: add             x1, x1, HEAP, lsl #32
    // 0x130d62c: r0 = controller()
    //     0x130d62c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d630: LoadField: r1 = r0->field_8f
    //     0x130d630: ldur            w1, [x0, #0x8f]
    // 0x130d634: DecompressPointer r1
    //     0x130d634: add             x1, x1, HEAP, lsl #32
    // 0x130d638: r0 = value()
    //     0x130d638: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d63c: mov             x2, x0
    // 0x130d640: ldur            x0, [fp, #-0x10]
    // 0x130d644: stur            x2, [fp, #-0x18]
    // 0x130d648: LoadField: r1 = r0->field_f
    //     0x130d648: ldur            w1, [x0, #0xf]
    // 0x130d64c: DecompressPointer r1
    //     0x130d64c: add             x1, x1, HEAP, lsl #32
    // 0x130d650: r0 = controller()
    //     0x130d650: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d654: LoadField: r1 = r0->field_93
    //     0x130d654: ldur            w1, [x0, #0x93]
    // 0x130d658: DecompressPointer r1
    //     0x130d658: add             x1, x1, HEAP, lsl #32
    // 0x130d65c: r0 = value()
    //     0x130d65c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d660: mov             x2, x0
    // 0x130d664: ldur            x0, [fp, #-0x10]
    // 0x130d668: stur            x2, [fp, #-0x28]
    // 0x130d66c: LoadField: r1 = r0->field_f
    //     0x130d66c: ldur            w1, [x0, #0xf]
    // 0x130d670: DecompressPointer r1
    //     0x130d670: add             x1, x1, HEAP, lsl #32
    // 0x130d674: r0 = controller()
    //     0x130d674: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d678: LoadField: r1 = r0->field_ab
    //     0x130d678: ldur            w1, [x0, #0xab]
    // 0x130d67c: DecompressPointer r1
    //     0x130d67c: add             x1, x1, HEAP, lsl #32
    // 0x130d680: r0 = value()
    //     0x130d680: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d684: mov             x2, x0
    // 0x130d688: ldur            x0, [fp, #-0x10]
    // 0x130d68c: stur            x2, [fp, #-0x30]
    // 0x130d690: LoadField: r1 = r0->field_f
    //     0x130d690: ldur            w1, [x0, #0xf]
    // 0x130d694: DecompressPointer r1
    //     0x130d694: add             x1, x1, HEAP, lsl #32
    // 0x130d698: r0 = controller()
    //     0x130d698: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d69c: LoadField: r2 = r0->field_9b
    //     0x130d69c: ldur            w2, [x0, #0x9b]
    // 0x130d6a0: DecompressPointer r2
    //     0x130d6a0: add             x2, x2, HEAP, lsl #32
    // 0x130d6a4: ldur            x0, [fp, #-0x10]
    // 0x130d6a8: stur            x2, [fp, #-0x38]
    // 0x130d6ac: LoadField: r1 = r0->field_f
    //     0x130d6ac: ldur            w1, [x0, #0xf]
    // 0x130d6b0: DecompressPointer r1
    //     0x130d6b0: add             x1, x1, HEAP, lsl #32
    // 0x130d6b4: r0 = controller()
    //     0x130d6b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d6b8: LoadField: r1 = r0->field_97
    //     0x130d6b8: ldur            w1, [x0, #0x97]
    // 0x130d6bc: DecompressPointer r1
    //     0x130d6bc: add             x1, x1, HEAP, lsl #32
    // 0x130d6c0: r0 = value()
    //     0x130d6c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d6c4: stur            x0, [fp, #-0x40]
    // 0x130d6c8: r0 = CustomizedRequest()
    //     0x130d6c8: bl              #0x12c41a4  ; AllocateCustomizedRequestStub -> CustomizedRequest (size=0x1c)
    // 0x130d6cc: mov             x1, x0
    // 0x130d6d0: ldur            x0, [fp, #-0x18]
    // 0x130d6d4: stur            x1, [fp, #-0x48]
    // 0x130d6d8: StoreField: r1->field_7 = r0
    //     0x130d6d8: stur            w0, [x1, #7]
    // 0x130d6dc: ldur            x0, [fp, #-0x28]
    // 0x130d6e0: StoreField: r1->field_b = r0
    //     0x130d6e0: stur            w0, [x1, #0xb]
    // 0x130d6e4: ldur            x0, [fp, #-0x30]
    // 0x130d6e8: StoreField: r1->field_f = r0
    //     0x130d6e8: stur            w0, [x1, #0xf]
    // 0x130d6ec: ldur            x0, [fp, #-0x38]
    // 0x130d6f0: StoreField: r1->field_13 = r0
    //     0x130d6f0: stur            w0, [x1, #0x13]
    // 0x130d6f4: ldur            x0, [fp, #-0x40]
    // 0x130d6f8: ArrayStore: r1[0] = r0  ; List_4
    //     0x130d6f8: stur            w0, [x1, #0x17]
    // 0x130d6fc: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x130d6fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x130d700: ldr             x0, [x0, #0x1c80]
    //     0x130d704: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x130d708: cmp             w0, w16
    //     0x130d70c: b.ne            #0x130d718
    //     0x130d710: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x130d714: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x130d718: r1 = Null
    //     0x130d718: mov             x1, NULL
    // 0x130d71c: r2 = 32
    //     0x130d71c: movz            x2, #0x20
    // 0x130d720: r0 = AllocateArray()
    //     0x130d720: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130d724: stur            x0, [fp, #-0x18]
    // 0x130d728: r16 = "previousScreenSource"
    //     0x130d728: add             x16, PP, #0xb, lsl #12  ; [pp+0xb448] "previousScreenSource"
    //     0x130d72c: ldr             x16, [x16, #0x448]
    // 0x130d730: StoreField: r0->field_f = r16
    //     0x130d730: stur            w16, [x0, #0xf]
    // 0x130d734: r16 = "product_page"
    //     0x130d734: add             x16, PP, #0xb, lsl #12  ; [pp+0xb480] "product_page"
    //     0x130d738: ldr             x16, [x16, #0x480]
    // 0x130d73c: StoreField: r0->field_13 = r16
    //     0x130d73c: stur            w16, [x0, #0x13]
    // 0x130d740: r16 = "product_id"
    //     0x130d740: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9b8] "product_id"
    //     0x130d744: ldr             x16, [x16, #0x9b8]
    // 0x130d748: ArrayStore: r0[0] = r16  ; List_4
    //     0x130d748: stur            w16, [x0, #0x17]
    // 0x130d74c: ldur            x2, [fp, #-0x10]
    // 0x130d750: LoadField: r1 = r2->field_f
    //     0x130d750: ldur            w1, [x2, #0xf]
    // 0x130d754: DecompressPointer r1
    //     0x130d754: add             x1, x1, HEAP, lsl #32
    // 0x130d758: r0 = controller()
    //     0x130d758: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d75c: LoadField: r1 = r0->field_8f
    //     0x130d75c: ldur            w1, [x0, #0x8f]
    // 0x130d760: DecompressPointer r1
    //     0x130d760: add             x1, x1, HEAP, lsl #32
    // 0x130d764: r0 = value()
    //     0x130d764: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d768: ldur            x1, [fp, #-0x18]
    // 0x130d76c: ArrayStore: r1[3] = r0  ; List_4
    //     0x130d76c: add             x25, x1, #0x1b
    //     0x130d770: str             w0, [x25]
    //     0x130d774: tbz             w0, #0, #0x130d790
    //     0x130d778: ldurb           w16, [x1, #-1]
    //     0x130d77c: ldurb           w17, [x0, #-1]
    //     0x130d780: and             x16, x17, x16, lsr #2
    //     0x130d784: tst             x16, HEAP, lsr #32
    //     0x130d788: b.eq            #0x130d790
    //     0x130d78c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d790: ldur            x0, [fp, #-0x18]
    // 0x130d794: r16 = "sku_id"
    //     0x130d794: add             x16, PP, #0xb, lsl #12  ; [pp+0xb498] "sku_id"
    //     0x130d798: ldr             x16, [x16, #0x498]
    // 0x130d79c: StoreField: r0->field_1f = r16
    //     0x130d79c: stur            w16, [x0, #0x1f]
    // 0x130d7a0: ldur            x2, [fp, #-0x10]
    // 0x130d7a4: LoadField: r1 = r2->field_f
    //     0x130d7a4: ldur            w1, [x2, #0xf]
    // 0x130d7a8: DecompressPointer r1
    //     0x130d7a8: add             x1, x1, HEAP, lsl #32
    // 0x130d7ac: r0 = controller()
    //     0x130d7ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d7b0: LoadField: r1 = r0->field_93
    //     0x130d7b0: ldur            w1, [x0, #0x93]
    // 0x130d7b4: DecompressPointer r1
    //     0x130d7b4: add             x1, x1, HEAP, lsl #32
    // 0x130d7b8: r0 = value()
    //     0x130d7b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d7bc: ldur            x1, [fp, #-0x18]
    // 0x130d7c0: ArrayStore: r1[5] = r0  ; List_4
    //     0x130d7c0: add             x25, x1, #0x23
    //     0x130d7c4: str             w0, [x25]
    //     0x130d7c8: tbz             w0, #0, #0x130d7e4
    //     0x130d7cc: ldurb           w16, [x1, #-1]
    //     0x130d7d0: ldurb           w17, [x0, #-1]
    //     0x130d7d4: and             x16, x17, x16, lsr #2
    //     0x130d7d8: tst             x16, HEAP, lsr #32
    //     0x130d7dc: b.eq            #0x130d7e4
    //     0x130d7e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d7e4: ldur            x0, [fp, #-0x18]
    // 0x130d7e8: r16 = "coming_from"
    //     0x130d7e8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22328] "coming_from"
    //     0x130d7ec: ldr             x16, [x16, #0x328]
    // 0x130d7f0: StoreField: r0->field_27 = r16
    //     0x130d7f0: stur            w16, [x0, #0x27]
    // 0x130d7f4: r16 = "buyNow"
    //     0x130d7f4: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130d7f8: ldr             x16, [x16, #0x358]
    // 0x130d7fc: StoreField: r0->field_2b = r16
    //     0x130d7fc: stur            w16, [x0, #0x2b]
    // 0x130d800: r16 = "quantity"
    //     0x130d800: add             x16, PP, #0xb, lsl #12  ; [pp+0xb428] "quantity"
    //     0x130d804: ldr             x16, [x16, #0x428]
    // 0x130d808: StoreField: r0->field_2f = r16
    //     0x130d808: stur            w16, [x0, #0x2f]
    // 0x130d80c: ldur            x2, [fp, #-0x10]
    // 0x130d810: LoadField: r1 = r2->field_f
    //     0x130d810: ldur            w1, [x2, #0xf]
    // 0x130d814: DecompressPointer r1
    //     0x130d814: add             x1, x1, HEAP, lsl #32
    // 0x130d818: r0 = controller()
    //     0x130d818: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d81c: LoadField: r1 = r0->field_ab
    //     0x130d81c: ldur            w1, [x0, #0xab]
    // 0x130d820: DecompressPointer r1
    //     0x130d820: add             x1, x1, HEAP, lsl #32
    // 0x130d824: r0 = value()
    //     0x130d824: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d828: ldur            x1, [fp, #-0x18]
    // 0x130d82c: ArrayStore: r1[9] = r0  ; List_4
    //     0x130d82c: add             x25, x1, #0x33
    //     0x130d830: str             w0, [x25]
    //     0x130d834: tbz             w0, #0, #0x130d850
    //     0x130d838: ldurb           w16, [x1, #-1]
    //     0x130d83c: ldurb           w17, [x0, #-1]
    //     0x130d840: and             x16, x17, x16, lsr #2
    //     0x130d844: tst             x16, HEAP, lsr #32
    //     0x130d848: b.eq            #0x130d850
    //     0x130d84c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d850: ldur            x3, [fp, #-0x18]
    // 0x130d854: r16 = "customization_request"
    //     0x130d854: add             x16, PP, #0x22, lsl #12  ; [pp+0x222b8] "customization_request"
    //     0x130d858: ldr             x16, [x16, #0x2b8]
    // 0x130d85c: StoreField: r3->field_37 = r16
    //     0x130d85c: stur            w16, [x3, #0x37]
    // 0x130d860: mov             x1, x3
    // 0x130d864: ldur            x0, [fp, #-0x48]
    // 0x130d868: ArrayStore: r1[11] = r0  ; List_4
    //     0x130d868: add             x25, x1, #0x3b
    //     0x130d86c: str             w0, [x25]
    //     0x130d870: tbz             w0, #0, #0x130d88c
    //     0x130d874: ldurb           w16, [x1, #-1]
    //     0x130d878: ldurb           w17, [x0, #-1]
    //     0x130d87c: and             x16, x17, x16, lsr #2
    //     0x130d880: tst             x16, HEAP, lsr #32
    //     0x130d884: b.eq            #0x130d88c
    //     0x130d888: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d88c: r16 = "customization_prize"
    //     0x130d88c: add             x16, PP, #0x22, lsl #12  ; [pp+0x222e8] "customization_prize"
    //     0x130d890: ldr             x16, [x16, #0x2e8]
    // 0x130d894: StoreField: r3->field_3f = r16
    //     0x130d894: stur            w16, [x3, #0x3f]
    // 0x130d898: r1 = Null
    //     0x130d898: mov             x1, NULL
    // 0x130d89c: r2 = 4
    //     0x130d89c: movz            x2, #0x4
    // 0x130d8a0: r0 = AllocateArray()
    //     0x130d8a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130d8a4: stur            x0, [fp, #-0x28]
    // 0x130d8a8: r16 = "₹"
    //     0x130d8a8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22360] "₹"
    //     0x130d8ac: ldr             x16, [x16, #0x360]
    // 0x130d8b0: StoreField: r0->field_f = r16
    //     0x130d8b0: stur            w16, [x0, #0xf]
    // 0x130d8b4: r1 = Function '<anonymous closure>': static.
    //     0x130d8b4: add             x1, PP, #0x3b, lsl #12  ; [pp+0x3b1a0] AnonymousClosure: static (0xa3aac4), in [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat (0xa30f6c)
    //     0x130d8b8: ldr             x1, [x1, #0x1a0]
    // 0x130d8bc: r2 = Null
    //     0x130d8bc: mov             x2, NULL
    // 0x130d8c0: r0 = AllocateClosure()
    //     0x130d8c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130d8c4: mov             x3, x0
    // 0x130d8c8: r1 = Null
    //     0x130d8c8: mov             x1, NULL
    // 0x130d8cc: r2 = Null
    //     0x130d8cc: mov             x2, NULL
    // 0x130d8d0: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x130d8d0: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x130d8d4: r0 = NumberFormat._forPattern()
    //     0x130d8d4: bl              #0xa33380  ; [package:intl/src/intl/number_format.dart] NumberFormat::NumberFormat._forPattern
    // 0x130d8d8: mov             x2, x0
    // 0x130d8dc: ldur            x0, [fp, #-0x10]
    // 0x130d8e0: stur            x2, [fp, #-0x30]
    // 0x130d8e4: LoadField: r1 = r0->field_f
    //     0x130d8e4: ldur            w1, [x0, #0xf]
    // 0x130d8e8: DecompressPointer r1
    //     0x130d8e8: add             x1, x1, HEAP, lsl #32
    // 0x130d8ec: r0 = controller()
    //     0x130d8ec: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130d8f0: LoadField: r1 = r0->field_67
    //     0x130d8f0: ldur            w1, [x0, #0x67]
    // 0x130d8f4: DecompressPointer r1
    //     0x130d8f4: add             x1, x1, HEAP, lsl #32
    // 0x130d8f8: r0 = value()
    //     0x130d8f8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130d8fc: ldur            x1, [fp, #-0x30]
    // 0x130d900: mov             x2, x0
    // 0x130d904: r0 = format()
    //     0x130d904: bl              #0xa30fb8  ; [package:intl/src/intl/number_format.dart] NumberFormat::format
    // 0x130d908: ldur            x1, [fp, #-0x28]
    // 0x130d90c: ArrayStore: r1[1] = r0  ; List_4
    //     0x130d90c: add             x25, x1, #0x13
    //     0x130d910: str             w0, [x25]
    //     0x130d914: tbz             w0, #0, #0x130d930
    //     0x130d918: ldurb           w16, [x1, #-1]
    //     0x130d91c: ldurb           w17, [x0, #-1]
    //     0x130d920: and             x16, x17, x16, lsr #2
    //     0x130d924: tst             x16, HEAP, lsr #32
    //     0x130d928: b.eq            #0x130d930
    //     0x130d92c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d930: ldur            x16, [fp, #-0x28]
    // 0x130d934: str             x16, [SP]
    // 0x130d938: r0 = _interpolate()
    //     0x130d938: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x130d93c: ldur            x1, [fp, #-0x18]
    // 0x130d940: ArrayStore: r1[13] = r0  ; List_4
    //     0x130d940: add             x25, x1, #0x43
    //     0x130d944: str             w0, [x25]
    //     0x130d948: tbz             w0, #0, #0x130d964
    //     0x130d94c: ldurb           w16, [x1, #-1]
    //     0x130d950: ldurb           w17, [x0, #-1]
    //     0x130d954: and             x16, x17, x16, lsr #2
    //     0x130d958: tst             x16, HEAP, lsr #32
    //     0x130d95c: b.eq            #0x130d964
    //     0x130d960: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x130d964: ldur            x0, [fp, #-0x18]
    // 0x130d968: r16 = "is_skipped_address"
    //     0x130d968: add             x16, PP, #0x31, lsl #12  ; [pp+0x31b80] "is_skipped_address"
    //     0x130d96c: ldr             x16, [x16, #0xb80]
    // 0x130d970: StoreField: r0->field_47 = r16
    //     0x130d970: stur            w16, [x0, #0x47]
    // 0x130d974: r16 = true
    //     0x130d974: add             x16, NULL, #0x20  ; true
    // 0x130d978: StoreField: r0->field_4b = r16
    //     0x130d978: stur            w16, [x0, #0x4b]
    // 0x130d97c: r16 = <String, dynamic>
    //     0x130d97c: ldr             x16, [PP, #0x20c8]  ; [pp+0x20c8] TypeArguments: <String, dynamic>
    // 0x130d980: stp             x0, x16, [SP]
    // 0x130d984: r0 = Map._fromLiteral()
    //     0x130d984: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x130d988: r16 = "/checkout_request_number_page"
    //     0x130d988: add             x16, PP, #0xd, lsl #12  ; [pp+0xd9f8] "/checkout_request_number_page"
    //     0x130d98c: ldr             x16, [x16, #0x9f8]
    // 0x130d990: stp             x16, NULL, [SP, #8]
    // 0x130d994: str             x0, [SP]
    // 0x130d998: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x130d998: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x130d99c: ldr             x4, [x4, #0x438]
    // 0x130d9a0: r0 = GetNavigation.toNamed()
    //     0x130d9a0: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x130d9a4: mov             x1, x0
    // 0x130d9a8: stur            x1, [fp, #-0x18]
    // 0x130d9ac: r0 = Await()
    //     0x130d9ac: bl              #0x63248c  ; AwaitStub
    // 0x130d9b0: r0 = Null
    //     0x130d9b0: mov             x0, NULL
    // 0x130d9b4: r0 = ReturnAsyncNotFuture()
    //     0x130d9b4: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x130d9b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130d9b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130d9bc: b               #0x130ccac
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x130d9c0, size: 0xa0c
    // 0x130d9c0: EnterFrame
    //     0x130d9c0: stp             fp, lr, [SP, #-0x10]!
    //     0x130d9c4: mov             fp, SP
    // 0x130d9c8: AllocStack(0x58)
    //     0x130d9c8: sub             SP, SP, #0x58
    // 0x130d9cc: SetupParameters()
    //     0x130d9cc: ldr             x0, [fp, #0x10]
    //     0x130d9d0: ldur            w2, [x0, #0x17]
    //     0x130d9d4: add             x2, x2, HEAP, lsl #32
    //     0x130d9d8: stur            x2, [fp, #-8]
    // 0x130d9dc: CheckStackOverflow
    //     0x130d9dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130d9e0: cmp             SP, x16
    //     0x130d9e4: b.ls            #0x130e3c4
    // 0x130d9e8: LoadField: r1 = r2->field_13
    //     0x130d9e8: ldur            w1, [x2, #0x13]
    // 0x130d9ec: DecompressPointer r1
    //     0x130d9ec: add             x1, x1, HEAP, lsl #32
    // 0x130d9f0: r0 = of()
    //     0x130d9f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130d9f4: LoadField: r1 = r0->field_5b
    //     0x130d9f4: ldur            w1, [x0, #0x5b]
    // 0x130d9f8: DecompressPointer r1
    //     0x130d9f8: add             x1, x1, HEAP, lsl #32
    // 0x130d9fc: r0 = LoadClassIdInstr(r1)
    //     0x130d9fc: ldur            x0, [x1, #-1]
    //     0x130da00: ubfx            x0, x0, #0xc, #0x14
    // 0x130da04: d0 = 0.100000
    //     0x130da04: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x130da08: r0 = GDT[cid_x0 + -0xffa]()
    //     0x130da08: sub             lr, x0, #0xffa
    //     0x130da0c: ldr             lr, [x21, lr, lsl #3]
    //     0x130da10: blr             lr
    // 0x130da14: stur            x0, [fp, #-0x10]
    // 0x130da18: r0 = Radius()
    //     0x130da18: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x130da1c: d0 = 30.000000
    //     0x130da1c: fmov            d0, #30.00000000
    // 0x130da20: stur            x0, [fp, #-0x18]
    // 0x130da24: StoreField: r0->field_7 = d0
    //     0x130da24: stur            d0, [x0, #7]
    // 0x130da28: StoreField: r0->field_f = d0
    //     0x130da28: stur            d0, [x0, #0xf]
    // 0x130da2c: r0 = BorderRadius()
    //     0x130da2c: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x130da30: mov             x1, x0
    // 0x130da34: ldur            x0, [fp, #-0x18]
    // 0x130da38: stur            x1, [fp, #-0x20]
    // 0x130da3c: StoreField: r1->field_7 = r0
    //     0x130da3c: stur            w0, [x1, #7]
    // 0x130da40: StoreField: r1->field_b = r0
    //     0x130da40: stur            w0, [x1, #0xb]
    // 0x130da44: StoreField: r1->field_f = r0
    //     0x130da44: stur            w0, [x1, #0xf]
    // 0x130da48: StoreField: r1->field_13 = r0
    //     0x130da48: stur            w0, [x1, #0x13]
    // 0x130da4c: r0 = BoxDecoration()
    //     0x130da4c: bl              #0x83dd04  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0x130da50: mov             x2, x0
    // 0x130da54: ldur            x0, [fp, #-0x10]
    // 0x130da58: stur            x2, [fp, #-0x18]
    // 0x130da5c: StoreField: r2->field_7 = r0
    //     0x130da5c: stur            w0, [x2, #7]
    // 0x130da60: ldur            x0, [fp, #-0x20]
    // 0x130da64: StoreField: r2->field_13 = r0
    //     0x130da64: stur            w0, [x2, #0x13]
    // 0x130da68: r0 = Instance_BoxShape
    //     0x130da68: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x130da6c: ldr             x0, [x0, #0x80]
    // 0x130da70: StoreField: r2->field_23 = r0
    //     0x130da70: stur            w0, [x2, #0x23]
    // 0x130da74: ldur            x0, [fp, #-8]
    // 0x130da78: LoadField: r1 = r0->field_13
    //     0x130da78: ldur            w1, [x0, #0x13]
    // 0x130da7c: DecompressPointer r1
    //     0x130da7c: add             x1, x1, HEAP, lsl #32
    // 0x130da80: r0 = of()
    //     0x130da80: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130da84: LoadField: r1 = r0->field_87
    //     0x130da84: ldur            w1, [x0, #0x87]
    // 0x130da88: DecompressPointer r1
    //     0x130da88: add             x1, x1, HEAP, lsl #32
    // 0x130da8c: LoadField: r0 = r1->field_2b
    //     0x130da8c: ldur            w0, [x1, #0x2b]
    // 0x130da90: DecompressPointer r0
    //     0x130da90: add             x0, x0, HEAP, lsl #32
    // 0x130da94: r16 = 12.000000
    //     0x130da94: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x130da98: ldr             x16, [x16, #0x9e8]
    // 0x130da9c: r30 = Instance_Color
    //     0x130da9c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130daa0: stp             lr, x16, [SP]
    // 0x130daa4: mov             x1, x0
    // 0x130daa8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130daa8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130daac: ldr             x4, [x4, #0xaa0]
    // 0x130dab0: r0 = copyWith()
    //     0x130dab0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130dab4: stur            x0, [fp, #-0x10]
    // 0x130dab8: r0 = Text()
    //     0x130dab8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130dabc: mov             x1, x0
    // 0x130dac0: r0 = "We will contact you to confirm the customization detail before shipping the item to you"
    //     0x130dac0: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3b008] "We will contact you to confirm the customization detail before shipping the item to you"
    //     0x130dac4: ldr             x0, [x0, #8]
    // 0x130dac8: stur            x1, [fp, #-0x20]
    // 0x130dacc: StoreField: r1->field_b = r0
    //     0x130dacc: stur            w0, [x1, #0xb]
    // 0x130dad0: ldur            x0, [fp, #-0x10]
    // 0x130dad4: StoreField: r1->field_13 = r0
    //     0x130dad4: stur            w0, [x1, #0x13]
    // 0x130dad8: r2 = 6
    //     0x130dad8: movz            x2, #0x6
    // 0x130dadc: StoreField: r1->field_37 = r2
    //     0x130dadc: stur            w2, [x1, #0x37]
    // 0x130dae0: r0 = ConstrainedBox()
    //     0x130dae0: bl              #0x8b1040  ; AllocateConstrainedBoxStub -> ConstrainedBox (size=0x14)
    // 0x130dae4: mov             x3, x0
    // 0x130dae8: r0 = Instance_BoxConstraints
    //     0x130dae8: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3b010] Obj!BoxConstraints@d56781
    //     0x130daec: ldr             x0, [x0, #0x10]
    // 0x130daf0: stur            x3, [fp, #-0x10]
    // 0x130daf4: StoreField: r3->field_f = r0
    //     0x130daf4: stur            w0, [x3, #0xf]
    // 0x130daf8: ldur            x0, [fp, #-0x20]
    // 0x130dafc: StoreField: r3->field_b = r0
    //     0x130dafc: stur            w0, [x3, #0xb]
    // 0x130db00: r1 = Null
    //     0x130db00: mov             x1, NULL
    // 0x130db04: r2 = 6
    //     0x130db04: movz            x2, #0x6
    // 0x130db08: r0 = AllocateArray()
    //     0x130db08: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130db0c: stur            x0, [fp, #-0x20]
    // 0x130db10: r16 = Instance_Icon
    //     0x130db10: add             x16, PP, #0x36, lsl #12  ; [pp+0x365a8] Obj!Icon@d66171
    //     0x130db14: ldr             x16, [x16, #0x5a8]
    // 0x130db18: StoreField: r0->field_f = r16
    //     0x130db18: stur            w16, [x0, #0xf]
    // 0x130db1c: r16 = Instance_SizedBox
    //     0x130db1c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f940] Obj!SizedBox@d67ec1
    //     0x130db20: ldr             x16, [x16, #0x940]
    // 0x130db24: StoreField: r0->field_13 = r16
    //     0x130db24: stur            w16, [x0, #0x13]
    // 0x130db28: ldur            x1, [fp, #-0x10]
    // 0x130db2c: ArrayStore: r0[0] = r1  ; List_4
    //     0x130db2c: stur            w1, [x0, #0x17]
    // 0x130db30: r1 = <Widget>
    //     0x130db30: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130db34: r0 = AllocateGrowableArray()
    //     0x130db34: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130db38: mov             x1, x0
    // 0x130db3c: ldur            x0, [fp, #-0x20]
    // 0x130db40: stur            x1, [fp, #-0x10]
    // 0x130db44: StoreField: r1->field_f = r0
    //     0x130db44: stur            w0, [x1, #0xf]
    // 0x130db48: r0 = 6
    //     0x130db48: movz            x0, #0x6
    // 0x130db4c: StoreField: r1->field_b = r0
    //     0x130db4c: stur            w0, [x1, #0xb]
    // 0x130db50: r0 = Row()
    //     0x130db50: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x130db54: mov             x1, x0
    // 0x130db58: r0 = Instance_Axis
    //     0x130db58: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x130db5c: stur            x1, [fp, #-0x20]
    // 0x130db60: StoreField: r1->field_f = r0
    //     0x130db60: stur            w0, [x1, #0xf]
    // 0x130db64: r2 = Instance_MainAxisAlignment
    //     0x130db64: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x130db68: ldr             x2, [x2, #0xa08]
    // 0x130db6c: StoreField: r1->field_13 = r2
    //     0x130db6c: stur            w2, [x1, #0x13]
    // 0x130db70: r3 = Instance_MainAxisSize
    //     0x130db70: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130db74: ldr             x3, [x3, #0xa10]
    // 0x130db78: ArrayStore: r1[0] = r3  ; List_4
    //     0x130db78: stur            w3, [x1, #0x17]
    // 0x130db7c: r4 = Instance_CrossAxisAlignment
    //     0x130db7c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x130db80: ldr             x4, [x4, #0xa18]
    // 0x130db84: StoreField: r1->field_1b = r4
    //     0x130db84: stur            w4, [x1, #0x1b]
    // 0x130db88: r4 = Instance_VerticalDirection
    //     0x130db88: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130db8c: ldr             x4, [x4, #0xa20]
    // 0x130db90: StoreField: r1->field_23 = r4
    //     0x130db90: stur            w4, [x1, #0x23]
    // 0x130db94: r5 = Instance_Clip
    //     0x130db94: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130db98: ldr             x5, [x5, #0x38]
    // 0x130db9c: StoreField: r1->field_2b = r5
    //     0x130db9c: stur            w5, [x1, #0x2b]
    // 0x130dba0: StoreField: r1->field_2f = rZR
    //     0x130dba0: stur            xzr, [x1, #0x2f]
    // 0x130dba4: ldur            x6, [fp, #-0x10]
    // 0x130dba8: StoreField: r1->field_b = r6
    //     0x130dba8: stur            w6, [x1, #0xb]
    // 0x130dbac: r0 = Padding()
    //     0x130dbac: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130dbb0: mov             x1, x0
    // 0x130dbb4: r0 = Instance_EdgeInsets
    //     0x130dbb4: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130dbb8: ldr             x0, [x0, #0x980]
    // 0x130dbbc: stur            x1, [fp, #-0x10]
    // 0x130dbc0: StoreField: r1->field_f = r0
    //     0x130dbc0: stur            w0, [x1, #0xf]
    // 0x130dbc4: ldur            x2, [fp, #-0x20]
    // 0x130dbc8: StoreField: r1->field_b = r2
    //     0x130dbc8: stur            w2, [x1, #0xb]
    // 0x130dbcc: r0 = Container()
    //     0x130dbcc: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x130dbd0: stur            x0, [fp, #-0x20]
    // 0x130dbd4: r16 = 80.000000
    //     0x130dbd4: add             x16, PP, #0x37, lsl #12  ; [pp+0x372f8] 80
    //     0x130dbd8: ldr             x16, [x16, #0x2f8]
    // 0x130dbdc: r30 = Instance_Alignment
    //     0x130dbdc: add             lr, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x130dbe0: ldr             lr, [lr, #0xb10]
    // 0x130dbe4: stp             lr, x16, [SP, #0x10]
    // 0x130dbe8: ldur            x16, [fp, #-0x18]
    // 0x130dbec: ldur            lr, [fp, #-0x10]
    // 0x130dbf0: stp             lr, x16, [SP]
    // 0x130dbf4: mov             x1, x0
    // 0x130dbf8: r4 = const [0, 0x5, 0x4, 0x1, alignment, 0x2, child, 0x4, decoration, 0x3, height, 0x1, null]
    //     0x130dbf8: add             x4, PP, #0x40, lsl #12  ; [pp+0x40a28] List(13) [0, 0x5, 0x4, 0x1, "alignment", 0x2, "child", 0x4, "decoration", 0x3, "height", 0x1, Null]
    //     0x130dbfc: ldr             x4, [x4, #0xa28]
    // 0x130dc00: r0 = Container()
    //     0x130dc00: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x130dc04: ldur            x2, [fp, #-8]
    // 0x130dc08: LoadField: r1 = r2->field_13
    //     0x130dc08: ldur            w1, [x2, #0x13]
    // 0x130dc0c: DecompressPointer r1
    //     0x130dc0c: add             x1, x1, HEAP, lsl #32
    // 0x130dc10: r0 = of()
    //     0x130dc10: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130dc14: LoadField: r1 = r0->field_87
    //     0x130dc14: ldur            w1, [x0, #0x87]
    // 0x130dc18: DecompressPointer r1
    //     0x130dc18: add             x1, x1, HEAP, lsl #32
    // 0x130dc1c: LoadField: r0 = r1->field_2b
    //     0x130dc1c: ldur            w0, [x1, #0x2b]
    // 0x130dc20: DecompressPointer r0
    //     0x130dc20: add             x0, x0, HEAP, lsl #32
    // 0x130dc24: stur            x0, [fp, #-0x10]
    // 0x130dc28: r1 = Instance_Color
    //     0x130dc28: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130dc2c: d0 = 0.700000
    //     0x130dc2c: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x130dc30: ldr             d0, [x17, #0xf48]
    // 0x130dc34: r0 = withOpacity()
    //     0x130dc34: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x130dc38: r16 = 16.000000
    //     0x130dc38: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x130dc3c: ldr             x16, [x16, #0x188]
    // 0x130dc40: stp             x16, x0, [SP]
    // 0x130dc44: ldur            x1, [fp, #-0x10]
    // 0x130dc48: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x130dc48: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x130dc4c: ldr             x4, [x4, #0x9b8]
    // 0x130dc50: r0 = copyWith()
    //     0x130dc50: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130dc54: stur            x0, [fp, #-0x10]
    // 0x130dc58: r0 = Text()
    //     0x130dc58: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130dc5c: mov             x2, x0
    // 0x130dc60: r0 = "Customisation fee"
    //     0x130dc60: add             x0, PP, #0x3b, lsl #12  ; [pp+0x3b020] "Customisation fee"
    //     0x130dc64: ldr             x0, [x0, #0x20]
    // 0x130dc68: stur            x2, [fp, #-0x18]
    // 0x130dc6c: StoreField: r2->field_b = r0
    //     0x130dc6c: stur            w0, [x2, #0xb]
    // 0x130dc70: ldur            x0, [fp, #-0x10]
    // 0x130dc74: StoreField: r2->field_13 = r0
    //     0x130dc74: stur            w0, [x2, #0x13]
    // 0x130dc78: ldur            x0, [fp, #-8]
    // 0x130dc7c: LoadField: r1 = r0->field_f
    //     0x130dc7c: ldur            w1, [x0, #0xf]
    // 0x130dc80: DecompressPointer r1
    //     0x130dc80: add             x1, x1, HEAP, lsl #32
    // 0x130dc84: r0 = controller()
    //     0x130dc84: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130dc88: mov             x1, x0
    // 0x130dc8c: r0 = getCustomisedPrice()
    //     0x130dc8c: bl              #0x1306ca0  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::getCustomisedPrice
    // 0x130dc90: ldur            x2, [fp, #-8]
    // 0x130dc94: stur            x0, [fp, #-0x10]
    // 0x130dc98: LoadField: r1 = r2->field_13
    //     0x130dc98: ldur            w1, [x2, #0x13]
    // 0x130dc9c: DecompressPointer r1
    //     0x130dc9c: add             x1, x1, HEAP, lsl #32
    // 0x130dca0: r0 = of()
    //     0x130dca0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130dca4: LoadField: r1 = r0->field_87
    //     0x130dca4: ldur            w1, [x0, #0x87]
    // 0x130dca8: DecompressPointer r1
    //     0x130dca8: add             x1, x1, HEAP, lsl #32
    // 0x130dcac: LoadField: r0 = r1->field_7
    //     0x130dcac: ldur            w0, [x1, #7]
    // 0x130dcb0: DecompressPointer r0
    //     0x130dcb0: add             x0, x0, HEAP, lsl #32
    // 0x130dcb4: r16 = 16.000000
    //     0x130dcb4: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x130dcb8: ldr             x16, [x16, #0x188]
    // 0x130dcbc: r30 = Instance_Color
    //     0x130dcbc: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130dcc0: stp             lr, x16, [SP]
    // 0x130dcc4: mov             x1, x0
    // 0x130dcc8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130dcc8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130dccc: ldr             x4, [x4, #0xaa0]
    // 0x130dcd0: r0 = copyWith()
    //     0x130dcd0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130dcd4: stur            x0, [fp, #-0x28]
    // 0x130dcd8: r0 = Text()
    //     0x130dcd8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130dcdc: mov             x1, x0
    // 0x130dce0: ldur            x0, [fp, #-0x10]
    // 0x130dce4: stur            x1, [fp, #-0x30]
    // 0x130dce8: StoreField: r1->field_b = r0
    //     0x130dce8: stur            w0, [x1, #0xb]
    // 0x130dcec: ldur            x0, [fp, #-0x28]
    // 0x130dcf0: StoreField: r1->field_13 = r0
    //     0x130dcf0: stur            w0, [x1, #0x13]
    // 0x130dcf4: r0 = Padding()
    //     0x130dcf4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130dcf8: mov             x3, x0
    // 0x130dcfc: r0 = Instance_EdgeInsets
    //     0x130dcfc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f990] Obj!EdgeInsets@d574a1
    //     0x130dd00: ldr             x0, [x0, #0x990]
    // 0x130dd04: stur            x3, [fp, #-0x10]
    // 0x130dd08: StoreField: r3->field_f = r0
    //     0x130dd08: stur            w0, [x3, #0xf]
    // 0x130dd0c: ldur            x0, [fp, #-0x30]
    // 0x130dd10: StoreField: r3->field_b = r0
    //     0x130dd10: stur            w0, [x3, #0xb]
    // 0x130dd14: r1 = Null
    //     0x130dd14: mov             x1, NULL
    // 0x130dd18: r2 = 4
    //     0x130dd18: movz            x2, #0x4
    // 0x130dd1c: r0 = AllocateArray()
    //     0x130dd1c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130dd20: mov             x2, x0
    // 0x130dd24: ldur            x0, [fp, #-0x18]
    // 0x130dd28: stur            x2, [fp, #-0x28]
    // 0x130dd2c: StoreField: r2->field_f = r0
    //     0x130dd2c: stur            w0, [x2, #0xf]
    // 0x130dd30: ldur            x0, [fp, #-0x10]
    // 0x130dd34: StoreField: r2->field_13 = r0
    //     0x130dd34: stur            w0, [x2, #0x13]
    // 0x130dd38: r1 = <Widget>
    //     0x130dd38: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130dd3c: r0 = AllocateGrowableArray()
    //     0x130dd3c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130dd40: mov             x1, x0
    // 0x130dd44: ldur            x0, [fp, #-0x28]
    // 0x130dd48: stur            x1, [fp, #-0x10]
    // 0x130dd4c: StoreField: r1->field_f = r0
    //     0x130dd4c: stur            w0, [x1, #0xf]
    // 0x130dd50: r2 = 4
    //     0x130dd50: movz            x2, #0x4
    // 0x130dd54: StoreField: r1->field_b = r2
    //     0x130dd54: stur            w2, [x1, #0xb]
    // 0x130dd58: r0 = Column()
    //     0x130dd58: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x130dd5c: mov             x1, x0
    // 0x130dd60: r0 = Instance_Axis
    //     0x130dd60: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x130dd64: stur            x1, [fp, #-0x18]
    // 0x130dd68: StoreField: r1->field_f = r0
    //     0x130dd68: stur            w0, [x1, #0xf]
    // 0x130dd6c: r0 = Instance_MainAxisAlignment
    //     0x130dd6c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x130dd70: ldr             x0, [x0, #0xa08]
    // 0x130dd74: StoreField: r1->field_13 = r0
    //     0x130dd74: stur            w0, [x1, #0x13]
    // 0x130dd78: r0 = Instance_MainAxisSize
    //     0x130dd78: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130dd7c: ldr             x0, [x0, #0xa10]
    // 0x130dd80: ArrayStore: r1[0] = r0  ; List_4
    //     0x130dd80: stur            w0, [x1, #0x17]
    // 0x130dd84: r2 = Instance_CrossAxisAlignment
    //     0x130dd84: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x130dd88: ldr             x2, [x2, #0x890]
    // 0x130dd8c: StoreField: r1->field_1b = r2
    //     0x130dd8c: stur            w2, [x1, #0x1b]
    // 0x130dd90: r3 = Instance_VerticalDirection
    //     0x130dd90: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130dd94: ldr             x3, [x3, #0xa20]
    // 0x130dd98: StoreField: r1->field_23 = r3
    //     0x130dd98: stur            w3, [x1, #0x23]
    // 0x130dd9c: r4 = Instance_Clip
    //     0x130dd9c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130dda0: ldr             x4, [x4, #0x38]
    // 0x130dda4: StoreField: r1->field_2b = r4
    //     0x130dda4: stur            w4, [x1, #0x2b]
    // 0x130dda8: StoreField: r1->field_2f = rZR
    //     0x130dda8: stur            xzr, [x1, #0x2f]
    // 0x130ddac: ldur            x5, [fp, #-0x10]
    // 0x130ddb0: StoreField: r1->field_b = r5
    //     0x130ddb0: stur            w5, [x1, #0xb]
    // 0x130ddb4: r0 = Padding()
    //     0x130ddb4: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130ddb8: mov             x2, x0
    // 0x130ddbc: r0 = Instance_EdgeInsets
    //     0x130ddbc: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130ddc0: ldr             x0, [x0, #0x980]
    // 0x130ddc4: stur            x2, [fp, #-0x10]
    // 0x130ddc8: StoreField: r2->field_f = r0
    //     0x130ddc8: stur            w0, [x2, #0xf]
    // 0x130ddcc: ldur            x1, [fp, #-0x18]
    // 0x130ddd0: StoreField: r2->field_b = r1
    //     0x130ddd0: stur            w1, [x2, #0xb]
    // 0x130ddd4: r1 = <FlexParentData>
    //     0x130ddd4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x130ddd8: ldr             x1, [x1, #0xe00]
    // 0x130dddc: r0 = Expanded()
    //     0x130dddc: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x130dde0: mov             x1, x0
    // 0x130dde4: r0 = 1
    //     0x130dde4: movz            x0, #0x1
    // 0x130dde8: stur            x1, [fp, #-0x18]
    // 0x130ddec: StoreField: r1->field_13 = r0
    //     0x130ddec: stur            x0, [x1, #0x13]
    // 0x130ddf0: r2 = Instance_FlexFit
    //     0x130ddf0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x130ddf4: ldr             x2, [x2, #0xe08]
    // 0x130ddf8: StoreField: r1->field_1b = r2
    //     0x130ddf8: stur            w2, [x1, #0x1b]
    // 0x130ddfc: ldur            x3, [fp, #-0x10]
    // 0x130de00: StoreField: r1->field_b = r3
    //     0x130de00: stur            w3, [x1, #0xb]
    // 0x130de04: r16 = <EdgeInsets>
    //     0x130de04: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x130de08: ldr             x16, [x16, #0xda0]
    // 0x130de0c: r30 = Instance_EdgeInsets
    //     0x130de0c: add             lr, PP, #0x40, lsl #12  ; [pp+0x40a30] Obj!EdgeInsets@d59a21
    //     0x130de10: ldr             lr, [lr, #0xa30]
    // 0x130de14: stp             lr, x16, [SP]
    // 0x130de18: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130de18: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130de1c: r0 = all()
    //     0x130de1c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130de20: ldur            x2, [fp, #-8]
    // 0x130de24: stur            x0, [fp, #-0x10]
    // 0x130de28: LoadField: r1 = r2->field_f
    //     0x130de28: ldur            w1, [x2, #0xf]
    // 0x130de2c: DecompressPointer r1
    //     0x130de2c: add             x1, x1, HEAP, lsl #32
    // 0x130de30: r0 = controller()
    //     0x130de30: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130de34: LoadField: r1 = r0->field_77
    //     0x130de34: ldur            w1, [x0, #0x77]
    // 0x130de38: DecompressPointer r1
    //     0x130de38: add             x1, x1, HEAP, lsl #32
    // 0x130de3c: r0 = value()
    //     0x130de3c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130de40: tbz             w0, #4, #0x130ded4
    // 0x130de44: ldur            x2, [fp, #-8]
    // 0x130de48: LoadField: r1 = r2->field_f
    //     0x130de48: ldur            w1, [x2, #0xf]
    // 0x130de4c: DecompressPointer r1
    //     0x130de4c: add             x1, x1, HEAP, lsl #32
    // 0x130de50: r0 = controller()
    //     0x130de50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130de54: LoadField: r1 = r0->field_7f
    //     0x130de54: ldur            w1, [x0, #0x7f]
    // 0x130de58: DecompressPointer r1
    //     0x130de58: add             x1, x1, HEAP, lsl #32
    // 0x130de5c: r0 = value()
    //     0x130de5c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130de60: tbz             w0, #4, #0x130ded4
    // 0x130de64: ldur            x2, [fp, #-8]
    // 0x130de68: LoadField: r1 = r2->field_f
    //     0x130de68: ldur            w1, [x2, #0xf]
    // 0x130de6c: DecompressPointer r1
    //     0x130de6c: add             x1, x1, HEAP, lsl #32
    // 0x130de70: r0 = controller()
    //     0x130de70: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130de74: LoadField: r1 = r0->field_73
    //     0x130de74: ldur            w1, [x0, #0x73]
    // 0x130de78: DecompressPointer r1
    //     0x130de78: add             x1, x1, HEAP, lsl #32
    // 0x130de7c: r0 = value()
    //     0x130de7c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130de80: tbz             w0, #4, #0x130ded4
    // 0x130de84: ldur            x2, [fp, #-8]
    // 0x130de88: LoadField: r1 = r2->field_f
    //     0x130de88: ldur            w1, [x2, #0xf]
    // 0x130de8c: DecompressPointer r1
    //     0x130de8c: add             x1, x1, HEAP, lsl #32
    // 0x130de90: r0 = getMandatory()
    //     0x130de90: bl              #0x130e3cc  ; [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::getMandatory
    // 0x130de94: tbz             w0, #4, #0x130ded4
    // 0x130de98: ldur            x2, [fp, #-8]
    // 0x130de9c: LoadField: r1 = r2->field_f
    //     0x130de9c: ldur            w1, [x2, #0xf]
    // 0x130dea0: DecompressPointer r1
    //     0x130dea0: add             x1, x1, HEAP, lsl #32
    // 0x130dea4: r0 = controller()
    //     0x130dea4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130dea8: LoadField: r1 = r0->field_6f
    //     0x130dea8: ldur            w1, [x0, #0x6f]
    // 0x130deac: DecompressPointer r1
    //     0x130deac: add             x1, x1, HEAP, lsl #32
    // 0x130deb0: r0 = value()
    //     0x130deb0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130deb4: tbz             w0, #4, #0x130ded4
    // 0x130deb8: ldur            x2, [fp, #-8]
    // 0x130debc: LoadField: r1 = r2->field_13
    //     0x130debc: ldur            w1, [x2, #0x13]
    // 0x130dec0: DecompressPointer r1
    //     0x130dec0: add             x1, x1, HEAP, lsl #32
    // 0x130dec4: r0 = of()
    //     0x130dec4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130dec8: LoadField: r1 = r0->field_5b
    //     0x130dec8: ldur            w1, [x0, #0x5b]
    // 0x130decc: DecompressPointer r1
    //     0x130decc: add             x1, x1, HEAP, lsl #32
    // 0x130ded0: b               #0x130dee4
    // 0x130ded4: r1 = Instance_Color
    //     0x130ded4: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130ded8: d0 = 0.200000
    //     0x130ded8: ldr             d0, [PP, #0x5b00]  ; [pp+0x5b00] IMM: double(0.2) from 0x3fc999999999999a
    // 0x130dedc: r0 = withOpacity()
    //     0x130dedc: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x130dee0: mov             x1, x0
    // 0x130dee4: ldur            x2, [fp, #-8]
    // 0x130dee8: ldur            x0, [fp, #-0x10]
    // 0x130deec: r16 = <Color>
    //     0x130deec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x130def0: ldr             x16, [x16, #0xf80]
    // 0x130def4: stp             x1, x16, [SP]
    // 0x130def8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130def8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130defc: r0 = all()
    //     0x130defc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130df00: stur            x0, [fp, #-0x28]
    // 0x130df04: r0 = Radius()
    //     0x130df04: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x130df08: d0 = 30.000000
    //     0x130df08: fmov            d0, #30.00000000
    // 0x130df0c: stur            x0, [fp, #-0x30]
    // 0x130df10: StoreField: r0->field_7 = d0
    //     0x130df10: stur            d0, [x0, #7]
    // 0x130df14: StoreField: r0->field_f = d0
    //     0x130df14: stur            d0, [x0, #0xf]
    // 0x130df18: r0 = BorderRadius()
    //     0x130df18: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x130df1c: mov             x1, x0
    // 0x130df20: ldur            x0, [fp, #-0x30]
    // 0x130df24: stur            x1, [fp, #-0x38]
    // 0x130df28: StoreField: r1->field_7 = r0
    //     0x130df28: stur            w0, [x1, #7]
    // 0x130df2c: StoreField: r1->field_b = r0
    //     0x130df2c: stur            w0, [x1, #0xb]
    // 0x130df30: StoreField: r1->field_f = r0
    //     0x130df30: stur            w0, [x1, #0xf]
    // 0x130df34: StoreField: r1->field_13 = r0
    //     0x130df34: stur            w0, [x1, #0x13]
    // 0x130df38: r0 = RoundedRectangleBorder()
    //     0x130df38: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x130df3c: mov             x1, x0
    // 0x130df40: ldur            x0, [fp, #-0x38]
    // 0x130df44: StoreField: r1->field_b = r0
    //     0x130df44: stur            w0, [x1, #0xb]
    // 0x130df48: r0 = Instance_BorderSide
    //     0x130df48: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x130df4c: ldr             x0, [x0, #0xe20]
    // 0x130df50: StoreField: r1->field_7 = r0
    //     0x130df50: stur            w0, [x1, #7]
    // 0x130df54: r16 = <RoundedRectangleBorder>
    //     0x130df54: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x130df58: ldr             x16, [x16, #0xf78]
    // 0x130df5c: stp             x1, x16, [SP]
    // 0x130df60: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x130df60: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x130df64: r0 = all()
    //     0x130df64: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x130df68: stur            x0, [fp, #-0x30]
    // 0x130df6c: r0 = ButtonStyle()
    //     0x130df6c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x130df70: mov             x1, x0
    // 0x130df74: ldur            x0, [fp, #-0x28]
    // 0x130df78: stur            x1, [fp, #-0x38]
    // 0x130df7c: StoreField: r1->field_b = r0
    //     0x130df7c: stur            w0, [x1, #0xb]
    // 0x130df80: ldur            x0, [fp, #-0x10]
    // 0x130df84: StoreField: r1->field_23 = r0
    //     0x130df84: stur            w0, [x1, #0x23]
    // 0x130df88: ldur            x0, [fp, #-0x30]
    // 0x130df8c: StoreField: r1->field_43 = r0
    //     0x130df8c: stur            w0, [x1, #0x43]
    // 0x130df90: r0 = TextButtonThemeData()
    //     0x130df90: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x130df94: mov             x2, x0
    // 0x130df98: ldur            x0, [fp, #-0x38]
    // 0x130df9c: stur            x2, [fp, #-0x10]
    // 0x130dfa0: StoreField: r2->field_7 = r0
    //     0x130dfa0: stur            w0, [x2, #7]
    // 0x130dfa4: ldur            x0, [fp, #-8]
    // 0x130dfa8: LoadField: r1 = r0->field_f
    //     0x130dfa8: ldur            w1, [x0, #0xf]
    // 0x130dfac: DecompressPointer r1
    //     0x130dfac: add             x1, x1, HEAP, lsl #32
    // 0x130dfb0: r0 = controller()
    //     0x130dfb0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130dfb4: LoadField: r1 = r0->field_77
    //     0x130dfb4: ldur            w1, [x0, #0x77]
    // 0x130dfb8: DecompressPointer r1
    //     0x130dfb8: add             x1, x1, HEAP, lsl #32
    // 0x130dfbc: r0 = value()
    //     0x130dfbc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130dfc0: tbz             w0, #4, #0x130e050
    // 0x130dfc4: ldur            x2, [fp, #-8]
    // 0x130dfc8: LoadField: r1 = r2->field_f
    //     0x130dfc8: ldur            w1, [x2, #0xf]
    // 0x130dfcc: DecompressPointer r1
    //     0x130dfcc: add             x1, x1, HEAP, lsl #32
    // 0x130dfd0: r0 = controller()
    //     0x130dfd0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130dfd4: LoadField: r1 = r0->field_7f
    //     0x130dfd4: ldur            w1, [x0, #0x7f]
    // 0x130dfd8: DecompressPointer r1
    //     0x130dfd8: add             x1, x1, HEAP, lsl #32
    // 0x130dfdc: r0 = value()
    //     0x130dfdc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130dfe0: tbz             w0, #4, #0x130e050
    // 0x130dfe4: ldur            x2, [fp, #-8]
    // 0x130dfe8: LoadField: r1 = r2->field_f
    //     0x130dfe8: ldur            w1, [x2, #0xf]
    // 0x130dfec: DecompressPointer r1
    //     0x130dfec: add             x1, x1, HEAP, lsl #32
    // 0x130dff0: r0 = controller()
    //     0x130dff0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130dff4: LoadField: r1 = r0->field_73
    //     0x130dff4: ldur            w1, [x0, #0x73]
    // 0x130dff8: DecompressPointer r1
    //     0x130dff8: add             x1, x1, HEAP, lsl #32
    // 0x130dffc: r0 = value()
    //     0x130dffc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130e000: tbz             w0, #4, #0x130e050
    // 0x130e004: ldur            x2, [fp, #-8]
    // 0x130e008: LoadField: r1 = r2->field_f
    //     0x130e008: ldur            w1, [x2, #0xf]
    // 0x130e00c: DecompressPointer r1
    //     0x130e00c: add             x1, x1, HEAP, lsl #32
    // 0x130e010: r0 = getMandatory()
    //     0x130e010: bl              #0x130e3cc  ; [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::getMandatory
    // 0x130e014: tbz             w0, #4, #0x130e050
    // 0x130e018: ldur            x2, [fp, #-8]
    // 0x130e01c: LoadField: r1 = r2->field_f
    //     0x130e01c: ldur            w1, [x2, #0xf]
    // 0x130e020: DecompressPointer r1
    //     0x130e020: add             x1, x1, HEAP, lsl #32
    // 0x130e024: r0 = controller()
    //     0x130e024: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130e028: LoadField: r1 = r0->field_6f
    //     0x130e028: ldur            w1, [x0, #0x6f]
    // 0x130e02c: DecompressPointer r1
    //     0x130e02c: add             x1, x1, HEAP, lsl #32
    // 0x130e030: r0 = value()
    //     0x130e030: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130e034: tbz             w0, #4, #0x130e050
    // 0x130e038: ldur            x2, [fp, #-8]
    // 0x130e03c: r1 = Function '<anonymous closure>':.
    //     0x130e03c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a38] AnonymousClosure: (0x130c8a0), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x136021c)
    //     0x130e040: ldr             x1, [x1, #0xa38]
    // 0x130e044: r0 = AllocateClosure()
    //     0x130e044: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x130e048: mov             x2, x0
    // 0x130e04c: b               #0x130e054
    // 0x130e050: r2 = Null
    //     0x130e050: mov             x2, NULL
    // 0x130e054: ldur            x0, [fp, #-8]
    // 0x130e058: stur            x2, [fp, #-0x28]
    // 0x130e05c: LoadField: r1 = r0->field_f
    //     0x130e05c: ldur            w1, [x0, #0xf]
    // 0x130e060: DecompressPointer r1
    //     0x130e060: add             x1, x1, HEAP, lsl #32
    // 0x130e064: r0 = controller()
    //     0x130e064: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130e068: LoadField: r1 = r0->field_bb
    //     0x130e068: ldur            w1, [x0, #0xbb]
    // 0x130e06c: DecompressPointer r1
    //     0x130e06c: add             x1, x1, HEAP, lsl #32
    // 0x130e070: r0 = LoadClassIdInstr(r1)
    //     0x130e070: ldur            x0, [x1, #-1]
    //     0x130e074: ubfx            x0, x0, #0xc, #0x14
    // 0x130e078: r16 = "buyNow"
    //     0x130e078: add             x16, PP, #0x22, lsl #12  ; [pp+0x22358] "buyNow"
    //     0x130e07c: ldr             x16, [x16, #0x358]
    // 0x130e080: stp             x16, x1, [SP]
    // 0x130e084: mov             lr, x0
    // 0x130e088: ldr             lr, [x21, lr, lsl #3]
    // 0x130e08c: blr             lr
    // 0x130e090: tbnz            w0, #4, #0x130e0a0
    // 0x130e094: r2 = "Buy Now"
    //     0x130e094: add             x2, PP, #0x40, lsl #12  ; [pp+0x40a40] "Buy Now"
    //     0x130e098: ldr             x2, [x2, #0xa40]
    // 0x130e09c: b               #0x130e0a8
    // 0x130e0a0: r2 = "Add to Bag"
    //     0x130e0a0: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e050] "Add to Bag"
    //     0x130e0a4: ldr             x2, [x2, #0x50]
    // 0x130e0a8: ldur            x0, [fp, #-8]
    // 0x130e0ac: stur            x2, [fp, #-0x30]
    // 0x130e0b0: LoadField: r1 = r0->field_13
    //     0x130e0b0: ldur            w1, [x0, #0x13]
    // 0x130e0b4: DecompressPointer r1
    //     0x130e0b4: add             x1, x1, HEAP, lsl #32
    // 0x130e0b8: r0 = of()
    //     0x130e0b8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x130e0bc: LoadField: r1 = r0->field_87
    //     0x130e0bc: ldur            w1, [x0, #0x87]
    // 0x130e0c0: DecompressPointer r1
    //     0x130e0c0: add             x1, x1, HEAP, lsl #32
    // 0x130e0c4: LoadField: r0 = r1->field_7
    //     0x130e0c4: ldur            w0, [x1, #7]
    // 0x130e0c8: DecompressPointer r0
    //     0x130e0c8: add             x0, x0, HEAP, lsl #32
    // 0x130e0cc: ldur            x2, [fp, #-8]
    // 0x130e0d0: stur            x0, [fp, #-0x38]
    // 0x130e0d4: LoadField: r1 = r2->field_f
    //     0x130e0d4: ldur            w1, [x2, #0xf]
    // 0x130e0d8: DecompressPointer r1
    //     0x130e0d8: add             x1, x1, HEAP, lsl #32
    // 0x130e0dc: r0 = controller()
    //     0x130e0dc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130e0e0: LoadField: r1 = r0->field_77
    //     0x130e0e0: ldur            w1, [x0, #0x77]
    // 0x130e0e4: DecompressPointer r1
    //     0x130e0e4: add             x1, x1, HEAP, lsl #32
    // 0x130e0e8: r0 = value()
    //     0x130e0e8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130e0ec: tbz             w0, #4, #0x130e16c
    // 0x130e0f0: ldur            x0, [fp, #-8]
    // 0x130e0f4: LoadField: r1 = r0->field_f
    //     0x130e0f4: ldur            w1, [x0, #0xf]
    // 0x130e0f8: DecompressPointer r1
    //     0x130e0f8: add             x1, x1, HEAP, lsl #32
    // 0x130e0fc: r0 = controller()
    //     0x130e0fc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130e100: LoadField: r1 = r0->field_7f
    //     0x130e100: ldur            w1, [x0, #0x7f]
    // 0x130e104: DecompressPointer r1
    //     0x130e104: add             x1, x1, HEAP, lsl #32
    // 0x130e108: r0 = value()
    //     0x130e108: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130e10c: tbz             w0, #4, #0x130e16c
    // 0x130e110: ldur            x0, [fp, #-8]
    // 0x130e114: LoadField: r1 = r0->field_f
    //     0x130e114: ldur            w1, [x0, #0xf]
    // 0x130e118: DecompressPointer r1
    //     0x130e118: add             x1, x1, HEAP, lsl #32
    // 0x130e11c: r0 = controller()
    //     0x130e11c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130e120: LoadField: r1 = r0->field_73
    //     0x130e120: ldur            w1, [x0, #0x73]
    // 0x130e124: DecompressPointer r1
    //     0x130e124: add             x1, x1, HEAP, lsl #32
    // 0x130e128: r0 = value()
    //     0x130e128: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130e12c: tbz             w0, #4, #0x130e16c
    // 0x130e130: ldur            x0, [fp, #-8]
    // 0x130e134: LoadField: r1 = r0->field_f
    //     0x130e134: ldur            w1, [x0, #0xf]
    // 0x130e138: DecompressPointer r1
    //     0x130e138: add             x1, x1, HEAP, lsl #32
    // 0x130e13c: r0 = getMandatory()
    //     0x130e13c: bl              #0x130e3cc  ; [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::getMandatory
    // 0x130e140: tbz             w0, #4, #0x130e16c
    // 0x130e144: ldur            x0, [fp, #-8]
    // 0x130e148: LoadField: r1 = r0->field_f
    //     0x130e148: ldur            w1, [x0, #0xf]
    // 0x130e14c: DecompressPointer r1
    //     0x130e14c: add             x1, x1, HEAP, lsl #32
    // 0x130e150: r0 = controller()
    //     0x130e150: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130e154: LoadField: r1 = r0->field_6f
    //     0x130e154: ldur            w1, [x0, #0x6f]
    // 0x130e158: DecompressPointer r1
    //     0x130e158: add             x1, x1, HEAP, lsl #32
    // 0x130e15c: r0 = value()
    //     0x130e15c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130e160: tbz             w0, #4, #0x130e16c
    // 0x130e164: r1 = Instance_Color
    //     0x130e164: ldr             x1, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x130e168: b               #0x130e17c
    // 0x130e16c: r1 = Instance_Color
    //     0x130e16c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x130e170: d0 = 0.400000
    //     0x130e170: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x130e174: r0 = withOpacity()
    //     0x130e174: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x130e178: mov             x1, x0
    // 0x130e17c: ldur            x5, [fp, #-0x20]
    // 0x130e180: ldur            x4, [fp, #-0x18]
    // 0x130e184: ldur            x3, [fp, #-0x10]
    // 0x130e188: ldur            x2, [fp, #-0x28]
    // 0x130e18c: ldur            x0, [fp, #-0x30]
    // 0x130e190: r16 = 16.000000
    //     0x130e190: add             x16, PP, #8, lsl #12  ; [pp+0x8188] 16
    //     0x130e194: ldr             x16, [x16, #0x188]
    // 0x130e198: stp             x1, x16, [SP]
    // 0x130e19c: ldur            x1, [fp, #-0x38]
    // 0x130e1a0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x130e1a0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x130e1a4: ldr             x4, [x4, #0xaa0]
    // 0x130e1a8: r0 = copyWith()
    //     0x130e1a8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x130e1ac: stur            x0, [fp, #-8]
    // 0x130e1b0: r0 = Text()
    //     0x130e1b0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x130e1b4: mov             x1, x0
    // 0x130e1b8: ldur            x0, [fp, #-0x30]
    // 0x130e1bc: stur            x1, [fp, #-0x38]
    // 0x130e1c0: StoreField: r1->field_b = r0
    //     0x130e1c0: stur            w0, [x1, #0xb]
    // 0x130e1c4: ldur            x0, [fp, #-8]
    // 0x130e1c8: StoreField: r1->field_13 = r0
    //     0x130e1c8: stur            w0, [x1, #0x13]
    // 0x130e1cc: r0 = TextButton()
    //     0x130e1cc: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x130e1d0: mov             x1, x0
    // 0x130e1d4: ldur            x0, [fp, #-0x28]
    // 0x130e1d8: stur            x1, [fp, #-8]
    // 0x130e1dc: StoreField: r1->field_b = r0
    //     0x130e1dc: stur            w0, [x1, #0xb]
    // 0x130e1e0: r0 = false
    //     0x130e1e0: add             x0, NULL, #0x30  ; false
    // 0x130e1e4: StoreField: r1->field_27 = r0
    //     0x130e1e4: stur            w0, [x1, #0x27]
    // 0x130e1e8: r0 = true
    //     0x130e1e8: add             x0, NULL, #0x20  ; true
    // 0x130e1ec: StoreField: r1->field_2f = r0
    //     0x130e1ec: stur            w0, [x1, #0x2f]
    // 0x130e1f0: ldur            x0, [fp, #-0x38]
    // 0x130e1f4: StoreField: r1->field_37 = r0
    //     0x130e1f4: stur            w0, [x1, #0x37]
    // 0x130e1f8: r0 = TextButtonTheme()
    //     0x130e1f8: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x130e1fc: mov             x1, x0
    // 0x130e200: ldur            x0, [fp, #-0x10]
    // 0x130e204: stur            x1, [fp, #-0x28]
    // 0x130e208: StoreField: r1->field_f = r0
    //     0x130e208: stur            w0, [x1, #0xf]
    // 0x130e20c: ldur            x0, [fp, #-8]
    // 0x130e210: StoreField: r1->field_b = r0
    //     0x130e210: stur            w0, [x1, #0xb]
    // 0x130e214: r0 = Padding()
    //     0x130e214: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130e218: mov             x2, x0
    // 0x130e21c: r0 = Instance_EdgeInsets
    //     0x130e21c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130e220: ldr             x0, [x0, #0x980]
    // 0x130e224: stur            x2, [fp, #-8]
    // 0x130e228: StoreField: r2->field_f = r0
    //     0x130e228: stur            w0, [x2, #0xf]
    // 0x130e22c: ldur            x1, [fp, #-0x28]
    // 0x130e230: StoreField: r2->field_b = r1
    //     0x130e230: stur            w1, [x2, #0xb]
    // 0x130e234: r1 = <FlexParentData>
    //     0x130e234: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe00] TypeArguments: <FlexParentData>
    //     0x130e238: ldr             x1, [x1, #0xe00]
    // 0x130e23c: r0 = Expanded()
    //     0x130e23c: bl              #0x9839b0  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0x130e240: mov             x3, x0
    // 0x130e244: r0 = 1
    //     0x130e244: movz            x0, #0x1
    // 0x130e248: stur            x3, [fp, #-0x10]
    // 0x130e24c: StoreField: r3->field_13 = r0
    //     0x130e24c: stur            x0, [x3, #0x13]
    // 0x130e250: r0 = Instance_FlexFit
    //     0x130e250: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe08] Obj!FlexFit@d73561
    //     0x130e254: ldr             x0, [x0, #0xe08]
    // 0x130e258: StoreField: r3->field_1b = r0
    //     0x130e258: stur            w0, [x3, #0x1b]
    // 0x130e25c: ldur            x0, [fp, #-8]
    // 0x130e260: StoreField: r3->field_b = r0
    //     0x130e260: stur            w0, [x3, #0xb]
    // 0x130e264: r1 = Null
    //     0x130e264: mov             x1, NULL
    // 0x130e268: r2 = 4
    //     0x130e268: movz            x2, #0x4
    // 0x130e26c: r0 = AllocateArray()
    //     0x130e26c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130e270: mov             x2, x0
    // 0x130e274: ldur            x0, [fp, #-0x18]
    // 0x130e278: stur            x2, [fp, #-8]
    // 0x130e27c: StoreField: r2->field_f = r0
    //     0x130e27c: stur            w0, [x2, #0xf]
    // 0x130e280: ldur            x0, [fp, #-0x10]
    // 0x130e284: StoreField: r2->field_13 = r0
    //     0x130e284: stur            w0, [x2, #0x13]
    // 0x130e288: r1 = <Widget>
    //     0x130e288: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130e28c: r0 = AllocateGrowableArray()
    //     0x130e28c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130e290: mov             x1, x0
    // 0x130e294: ldur            x0, [fp, #-8]
    // 0x130e298: stur            x1, [fp, #-0x10]
    // 0x130e29c: StoreField: r1->field_f = r0
    //     0x130e29c: stur            w0, [x1, #0xf]
    // 0x130e2a0: r2 = 4
    //     0x130e2a0: movz            x2, #0x4
    // 0x130e2a4: StoreField: r1->field_b = r2
    //     0x130e2a4: stur            w2, [x1, #0xb]
    // 0x130e2a8: r0 = Row()
    //     0x130e2a8: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x130e2ac: mov             x3, x0
    // 0x130e2b0: r0 = Instance_Axis
    //     0x130e2b0: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x130e2b4: stur            x3, [fp, #-8]
    // 0x130e2b8: StoreField: r3->field_f = r0
    //     0x130e2b8: stur            w0, [x3, #0xf]
    // 0x130e2bc: r1 = Instance_MainAxisAlignment
    //     0x130e2bc: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f0a8] Obj!MainAxisAlignment@d734c1
    //     0x130e2c0: ldr             x1, [x1, #0xa8]
    // 0x130e2c4: StoreField: r3->field_13 = r1
    //     0x130e2c4: stur            w1, [x3, #0x13]
    // 0x130e2c8: r1 = Instance_MainAxisSize
    //     0x130e2c8: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x130e2cc: ldr             x1, [x1, #0xa10]
    // 0x130e2d0: ArrayStore: r3[0] = r1  ; List_4
    //     0x130e2d0: stur            w1, [x3, #0x17]
    // 0x130e2d4: r1 = Instance_CrossAxisAlignment
    //     0x130e2d4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x130e2d8: ldr             x1, [x1, #0x890]
    // 0x130e2dc: StoreField: r3->field_1b = r1
    //     0x130e2dc: stur            w1, [x3, #0x1b]
    // 0x130e2e0: r4 = Instance_VerticalDirection
    //     0x130e2e0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130e2e4: ldr             x4, [x4, #0xa20]
    // 0x130e2e8: StoreField: r3->field_23 = r4
    //     0x130e2e8: stur            w4, [x3, #0x23]
    // 0x130e2ec: r5 = Instance_Clip
    //     0x130e2ec: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130e2f0: ldr             x5, [x5, #0x38]
    // 0x130e2f4: StoreField: r3->field_2b = r5
    //     0x130e2f4: stur            w5, [x3, #0x2b]
    // 0x130e2f8: StoreField: r3->field_2f = rZR
    //     0x130e2f8: stur            xzr, [x3, #0x2f]
    // 0x130e2fc: ldur            x1, [fp, #-0x10]
    // 0x130e300: StoreField: r3->field_b = r1
    //     0x130e300: stur            w1, [x3, #0xb]
    // 0x130e304: r1 = Null
    //     0x130e304: mov             x1, NULL
    // 0x130e308: r2 = 4
    //     0x130e308: movz            x2, #0x4
    // 0x130e30c: r0 = AllocateArray()
    //     0x130e30c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x130e310: mov             x2, x0
    // 0x130e314: ldur            x0, [fp, #-0x20]
    // 0x130e318: stur            x2, [fp, #-0x10]
    // 0x130e31c: StoreField: r2->field_f = r0
    //     0x130e31c: stur            w0, [x2, #0xf]
    // 0x130e320: ldur            x0, [fp, #-8]
    // 0x130e324: StoreField: r2->field_13 = r0
    //     0x130e324: stur            w0, [x2, #0x13]
    // 0x130e328: r1 = <Widget>
    //     0x130e328: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x130e32c: r0 = AllocateGrowableArray()
    //     0x130e32c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x130e330: mov             x1, x0
    // 0x130e334: ldur            x0, [fp, #-0x10]
    // 0x130e338: stur            x1, [fp, #-8]
    // 0x130e33c: StoreField: r1->field_f = r0
    //     0x130e33c: stur            w0, [x1, #0xf]
    // 0x130e340: r0 = 4
    //     0x130e340: movz            x0, #0x4
    // 0x130e344: StoreField: r1->field_b = r0
    //     0x130e344: stur            w0, [x1, #0xb]
    // 0x130e348: r0 = Wrap()
    //     0x130e348: bl              #0x98c774  ; AllocateWrapStub -> Wrap (size=0x3c)
    // 0x130e34c: mov             x1, x0
    // 0x130e350: r0 = Instance_Axis
    //     0x130e350: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x130e354: stur            x1, [fp, #-0x10]
    // 0x130e358: StoreField: r1->field_f = r0
    //     0x130e358: stur            w0, [x1, #0xf]
    // 0x130e35c: r0 = Instance_WrapAlignment
    //     0x130e35c: add             x0, PP, #0x36, lsl #12  ; [pp+0x366e8] Obj!WrapAlignment@d730c1
    //     0x130e360: ldr             x0, [x0, #0x6e8]
    // 0x130e364: StoreField: r1->field_13 = r0
    //     0x130e364: stur            w0, [x1, #0x13]
    // 0x130e368: ArrayStore: r1[0] = rZR  ; List_8
    //     0x130e368: stur            xzr, [x1, #0x17]
    // 0x130e36c: StoreField: r1->field_1f = r0
    //     0x130e36c: stur            w0, [x1, #0x1f]
    // 0x130e370: StoreField: r1->field_23 = rZR
    //     0x130e370: stur            xzr, [x1, #0x23]
    // 0x130e374: r0 = Instance_WrapCrossAlignment
    //     0x130e374: add             x0, PP, #0x36, lsl #12  ; [pp+0x366f0] Obj!WrapCrossAlignment@d73001
    //     0x130e378: ldr             x0, [x0, #0x6f0]
    // 0x130e37c: StoreField: r1->field_2b = r0
    //     0x130e37c: stur            w0, [x1, #0x2b]
    // 0x130e380: r0 = Instance_VerticalDirection
    //     0x130e380: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x130e384: ldr             x0, [x0, #0xa20]
    // 0x130e388: StoreField: r1->field_33 = r0
    //     0x130e388: stur            w0, [x1, #0x33]
    // 0x130e38c: r0 = Instance_Clip
    //     0x130e38c: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x130e390: ldr             x0, [x0, #0x38]
    // 0x130e394: StoreField: r1->field_37 = r0
    //     0x130e394: stur            w0, [x1, #0x37]
    // 0x130e398: ldur            x0, [fp, #-8]
    // 0x130e39c: StoreField: r1->field_b = r0
    //     0x130e39c: stur            w0, [x1, #0xb]
    // 0x130e3a0: r0 = Padding()
    //     0x130e3a0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x130e3a4: r1 = Instance_EdgeInsets
    //     0x130e3a4: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x130e3a8: ldr             x1, [x1, #0x980]
    // 0x130e3ac: StoreField: r0->field_f = r1
    //     0x130e3ac: stur            w1, [x0, #0xf]
    // 0x130e3b0: ldur            x1, [fp, #-0x10]
    // 0x130e3b4: StoreField: r0->field_b = r1
    //     0x130e3b4: stur            w1, [x0, #0xb]
    // 0x130e3b8: LeaveFrame
    //     0x130e3b8: mov             SP, fp
    //     0x130e3bc: ldp             fp, lr, [SP], #0x10
    // 0x130e3c0: ret
    //     0x130e3c0: ret             
    // 0x130e3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130e3c4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130e3c8: b               #0x130d9e8
  }
  _ getMandatory(/* No info */) {
    // ** addr: 0x130e3cc, size: 0x6bc
    // 0x130e3cc: EnterFrame
    //     0x130e3cc: stp             fp, lr, [SP, #-0x10]!
    //     0x130e3d0: mov             fp, SP
    // 0x130e3d4: AllocStack(0x70)
    //     0x130e3d4: sub             SP, SP, #0x70
    // 0x130e3d8: SetupParameters(CustomizedPage this /* r1 => r0, fp-0x8 */)
    //     0x130e3d8: mov             x0, x1
    //     0x130e3dc: stur            x1, [fp, #-8]
    // 0x130e3e0: CheckStackOverflow
    //     0x130e3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130e3e4: cmp             SP, x16
    //     0x130e3e8: b.ls            #0x130ea74
    // 0x130e3ec: mov             x1, x0
    // 0x130e3f0: r0 = controller()
    //     0x130e3f0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x130e3f4: LoadField: r1 = r0->field_53
    //     0x130e3f4: ldur            w1, [x0, #0x53]
    // 0x130e3f8: DecompressPointer r1
    //     0x130e3f8: add             x1, x1, HEAP, lsl #32
    // 0x130e3fc: r0 = value()
    //     0x130e3fc: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x130e400: LoadField: r1 = r0->field_b
    //     0x130e400: ldur            w1, [x0, #0xb]
    // 0x130e404: DecompressPointer r1
    //     0x130e404: add             x1, x1, HEAP, lsl #32
    // 0x130e408: cmp             w1, NULL
    // 0x130e40c: b.eq            #0x130ea7c
    // 0x130e410: LoadField: r3 = r1->field_f
    //     0x130e410: ldur            w3, [x1, #0xf]
    // 0x130e414: DecompressPointer r3
    //     0x130e414: add             x3, x3, HEAP, lsl #32
    // 0x130e418: mov             x0, x3
    // 0x130e41c: stur            x3, [fp, #-0x10]
    // 0x130e420: r2 = Null
    //     0x130e420: mov             x2, NULL
    // 0x130e424: r1 = Null
    //     0x130e424: mov             x1, NULL
    // 0x130e428: r8 = Iterable
    //     0x130e428: ldr             x8, [PP, #0x1160]  ; [pp+0x1160] Type: Iterable
    // 0x130e42c: r3 = Null
    //     0x130e42c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a78] Null
    //     0x130e430: ldr             x3, [x3, #0xa78]
    // 0x130e434: r0 = Iterable()
    //     0x130e434: bl              #0x6239fc  ; IsType_Iterable_Stub
    // 0x130e438: ldur            x1, [fp, #-0x10]
    // 0x130e43c: LoadField: r2 = r1->field_7
    //     0x130e43c: ldur            w2, [x1, #7]
    // 0x130e440: DecompressPointer r2
    //     0x130e440: add             x2, x2, HEAP, lsl #32
    // 0x130e444: stur            x2, [fp, #-0x30]
    // 0x130e448: LoadField: r0 = r1->field_b
    //     0x130e448: ldur            w0, [x1, #0xb]
    // 0x130e44c: r3 = LoadInt32Instr(r0)
    //     0x130e44c: sbfx            x3, x0, #1, #0x1f
    // 0x130e450: ldur            x0, [fp, #-8]
    // 0x130e454: stur            x3, [fp, #-0x28]
    // 0x130e458: LoadField: r4 = r0->field_b
    //     0x130e458: ldur            w4, [x0, #0xb]
    // 0x130e45c: DecompressPointer r4
    //     0x130e45c: add             x4, x4, HEAP, lsl #32
    // 0x130e460: stur            x4, [fp, #-0x20]
    // 0x130e464: r6 = false
    //     0x130e464: add             x6, NULL, #0x30  ; false
    // 0x130e468: r5 = 0
    //     0x130e468: movz            x5, #0
    // 0x130e46c: stur            x6, [fp, #-8]
    // 0x130e470: stur            x5, [fp, #-0x18]
    // 0x130e474: CheckStackOverflow
    //     0x130e474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x130e478: cmp             SP, x16
    //     0x130e47c: b.ls            #0x130ea80
    // 0x130e480: str             x1, [SP]
    // 0x130e484: r0 = 92
    //     0x130e484: movz            x0, #0x5c
    // 0x130e488: r0 = GDT[cid_x0 + 0xc898]()
    //     0x130e488: movz            x17, #0xc898
    //     0x130e48c: add             lr, x0, x17
    //     0x130e490: ldr             lr, [x21, lr, lsl #3]
    //     0x130e494: blr             lr
    // 0x130e498: r1 = LoadInt32Instr(r0)
    //     0x130e498: sbfx            x1, x0, #1, #0x1f
    //     0x130e49c: tbz             w0, #0, #0x130e4a4
    //     0x130e4a0: ldur            x1, [x0, #7]
    // 0x130e4a4: ldur            x3, [fp, #-0x28]
    // 0x130e4a8: cmp             x3, x1
    // 0x130e4ac: b.ne            #0x130ea54
    // 0x130e4b0: ldur            x4, [fp, #-0x18]
    // 0x130e4b4: cmp             x4, x1
    // 0x130e4b8: b.ge            #0x130ea44
    // 0x130e4bc: ldur            x1, [fp, #-0x10]
    // 0x130e4c0: mov             x2, x4
    // 0x130e4c4: r0 = 92
    //     0x130e4c4: movz            x0, #0x5c
    // 0x130e4c8: r0 = GDT[cid_x0 + 0x114ba]()
    //     0x130e4c8: movz            x17, #0x14ba
    //     0x130e4cc: movk            x17, #0x1, lsl #16
    //     0x130e4d0: add             lr, x0, x17
    //     0x130e4d4: ldr             lr, [x21, lr, lsl #3]
    //     0x130e4d8: blr             lr
    // 0x130e4dc: mov             x3, x0
    // 0x130e4e0: ldur            x0, [fp, #-0x18]
    // 0x130e4e4: stur            x3, [fp, #-0x40]
    // 0x130e4e8: add             x5, x0, #1
    // 0x130e4ec: stur            x5, [fp, #-0x38]
    // 0x130e4f0: cmp             w3, NULL
    // 0x130e4f4: b.ne            #0x130e528
    // 0x130e4f8: mov             x0, x3
    // 0x130e4fc: ldur            x2, [fp, #-0x30]
    // 0x130e500: r1 = Null
    //     0x130e500: mov             x1, NULL
    // 0x130e504: cmp             w2, NULL
    // 0x130e508: b.eq            #0x130e528
    // 0x130e50c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130e50c: ldur            w4, [x2, #0x17]
    // 0x130e510: DecompressPointer r4
    //     0x130e510: add             x4, x4, HEAP, lsl #32
    // 0x130e514: r8 = X0
    //     0x130e514: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130e518: LoadField: r9 = r4->field_7
    //     0x130e518: ldur            x9, [x4, #7]
    // 0x130e51c: r3 = Null
    //     0x130e51c: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a88] Null
    //     0x130e520: ldr             x3, [x3, #0xa88]
    // 0x130e524: blr             x9
    // 0x130e528: ldur            x0, [fp, #-0x40]
    // 0x130e52c: LoadField: r1 = r0->field_b
    //     0x130e52c: ldur            w1, [x0, #0xb]
    // 0x130e530: DecompressPointer r1
    //     0x130e530: add             x1, x1, HEAP, lsl #32
    // 0x130e534: r16 = Instance_CustomisationType
    //     0x130e534: add             x16, PP, #0x23, lsl #12  ; [pp+0x23680] Obj!CustomisationType@d756a1
    //     0x130e538: ldr             x16, [x16, #0x680]
    // 0x130e53c: cmp             w1, w16
    // 0x130e540: b.ne            #0x130e634
    // 0x130e544: r1 = LoadStaticField(0xcb0)
    //     0x130e544: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130e548: ldr             x1, [x1, #0x1960]
    // 0x130e54c: cmp             w1, NULL
    // 0x130e550: b.ne            #0x130e568
    // 0x130e554: r1 = Instance_GetInstance
    //     0x130e554: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e558: ldr             x1, [x1, #0x900]
    // 0x130e55c: StoreStaticField(0xcb0, r1)
    //     0x130e55c: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130e560: str             x1, [x2, #0x1960]
    // 0x130e564: b               #0x130e570
    // 0x130e568: r1 = Instance_GetInstance
    //     0x130e568: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e56c: ldr             x1, [x1, #0x900]
    // 0x130e570: ldur            x16, [fp, #-0x20]
    // 0x130e574: r30 = Instance_GetInstance
    //     0x130e574: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e578: ldr             lr, [lr, #0x900]
    // 0x130e57c: stp             lr, x16, [SP, #8]
    // 0x130e580: str             NULL, [SP]
    // 0x130e584: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130e584: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130e588: r0 = find()
    //     0x130e588: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130e58c: LoadField: r2 = r0->field_cb
    //     0x130e58c: ldur            w2, [x0, #0xcb]
    // 0x130e590: DecompressPointer r2
    //     0x130e590: add             x2, x2, HEAP, lsl #32
    // 0x130e594: ldur            x0, [fp, #-0x40]
    // 0x130e598: stur            x2, [fp, #-0x50]
    // 0x130e59c: LoadField: r1 = r0->field_7
    //     0x130e59c: ldur            w1, [x0, #7]
    // 0x130e5a0: DecompressPointer r1
    //     0x130e5a0: add             x1, x1, HEAP, lsl #32
    // 0x130e5a4: cmp             w1, NULL
    // 0x130e5a8: b.ne            #0x130e5b4
    // 0x130e5ac: r0 = ""
    //     0x130e5ac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130e5b0: b               #0x130e5b8
    // 0x130e5b4: mov             x0, x1
    // 0x130e5b8: mov             x1, x2
    // 0x130e5bc: stur            x0, [fp, #-0x48]
    // 0x130e5c0: r0 = value()
    //     0x130e5c0: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130e5c4: mov             x3, x0
    // 0x130e5c8: ldur            x0, [fp, #-0x50]
    // 0x130e5cc: stur            x3, [fp, #-0x58]
    // 0x130e5d0: LoadField: r2 = r0->field_7
    //     0x130e5d0: ldur            w2, [x0, #7]
    // 0x130e5d4: DecompressPointer r2
    //     0x130e5d4: add             x2, x2, HEAP, lsl #32
    // 0x130e5d8: ldur            x0, [fp, #-0x48]
    // 0x130e5dc: r1 = Null
    //     0x130e5dc: mov             x1, NULL
    // 0x130e5e0: cmp             w2, NULL
    // 0x130e5e4: b.eq            #0x130e604
    // 0x130e5e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130e5e8: ldur            w4, [x2, #0x17]
    // 0x130e5ec: DecompressPointer r4
    //     0x130e5ec: add             x4, x4, HEAP, lsl #32
    // 0x130e5f0: r8 = X0
    //     0x130e5f0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130e5f4: LoadField: r9 = r4->field_7
    //     0x130e5f4: ldur            x9, [x4, #7]
    // 0x130e5f8: r3 = Null
    //     0x130e5f8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a98] Null
    //     0x130e5fc: ldr             x3, [x3, #0xa98]
    // 0x130e600: blr             x9
    // 0x130e604: ldur            x1, [fp, #-0x58]
    // 0x130e608: r0 = LoadClassIdInstr(r1)
    //     0x130e608: ldur            x0, [x1, #-1]
    //     0x130e60c: ubfx            x0, x0, #0xc, #0x14
    // 0x130e610: ldur            x2, [fp, #-0x48]
    // 0x130e614: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130e614: sub             lr, x0, #0xfe
    //     0x130e618: ldr             lr, [x21, lr, lsl #3]
    //     0x130e61c: blr             lr
    // 0x130e620: cmp             w0, NULL
    // 0x130e624: b.ne            #0x130e62c
    // 0x130e628: r0 = false
    //     0x130e628: add             x0, NULL, #0x30  ; false
    // 0x130e62c: mov             x6, x0
    // 0x130e630: b               #0x130ea2c
    // 0x130e634: r16 = Instance_CustomisationType
    //     0x130e634: add             x16, PP, #0x23, lsl #12  ; [pp+0x23690] Obj!CustomisationType@d75681
    //     0x130e638: ldr             x16, [x16, #0x690]
    // 0x130e63c: cmp             w1, w16
    // 0x130e640: b.ne            #0x130e730
    // 0x130e644: r1 = LoadStaticField(0xcb0)
    //     0x130e644: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130e648: ldr             x1, [x1, #0x1960]
    // 0x130e64c: cmp             w1, NULL
    // 0x130e650: b.ne            #0x130e668
    // 0x130e654: r1 = Instance_GetInstance
    //     0x130e654: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e658: ldr             x1, [x1, #0x900]
    // 0x130e65c: StoreStaticField(0xcb0, r1)
    //     0x130e65c: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130e660: str             x1, [x2, #0x1960]
    // 0x130e664: b               #0x130e670
    // 0x130e668: r1 = Instance_GetInstance
    //     0x130e668: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e66c: ldr             x1, [x1, #0x900]
    // 0x130e670: ldur            x16, [fp, #-0x20]
    // 0x130e674: r30 = Instance_GetInstance
    //     0x130e674: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e678: ldr             lr, [lr, #0x900]
    // 0x130e67c: stp             lr, x16, [SP, #8]
    // 0x130e680: str             NULL, [SP]
    // 0x130e684: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130e684: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130e688: r0 = find()
    //     0x130e688: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130e68c: LoadField: r2 = r0->field_cb
    //     0x130e68c: ldur            w2, [x0, #0xcb]
    // 0x130e690: DecompressPointer r2
    //     0x130e690: add             x2, x2, HEAP, lsl #32
    // 0x130e694: ldur            x0, [fp, #-0x40]
    // 0x130e698: stur            x2, [fp, #-0x50]
    // 0x130e69c: LoadField: r1 = r0->field_7
    //     0x130e69c: ldur            w1, [x0, #7]
    // 0x130e6a0: DecompressPointer r1
    //     0x130e6a0: add             x1, x1, HEAP, lsl #32
    // 0x130e6a4: cmp             w1, NULL
    // 0x130e6a8: b.ne            #0x130e6b4
    // 0x130e6ac: r0 = ""
    //     0x130e6ac: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130e6b0: b               #0x130e6b8
    // 0x130e6b4: mov             x0, x1
    // 0x130e6b8: mov             x1, x2
    // 0x130e6bc: stur            x0, [fp, #-0x48]
    // 0x130e6c0: r0 = value()
    //     0x130e6c0: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130e6c4: mov             x3, x0
    // 0x130e6c8: ldur            x0, [fp, #-0x50]
    // 0x130e6cc: stur            x3, [fp, #-0x58]
    // 0x130e6d0: LoadField: r2 = r0->field_7
    //     0x130e6d0: ldur            w2, [x0, #7]
    // 0x130e6d4: DecompressPointer r2
    //     0x130e6d4: add             x2, x2, HEAP, lsl #32
    // 0x130e6d8: ldur            x0, [fp, #-0x48]
    // 0x130e6dc: r1 = Null
    //     0x130e6dc: mov             x1, NULL
    // 0x130e6e0: cmp             w2, NULL
    // 0x130e6e4: b.eq            #0x130e704
    // 0x130e6e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130e6e8: ldur            w4, [x2, #0x17]
    // 0x130e6ec: DecompressPointer r4
    //     0x130e6ec: add             x4, x4, HEAP, lsl #32
    // 0x130e6f0: r8 = X0
    //     0x130e6f0: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130e6f4: LoadField: r9 = r4->field_7
    //     0x130e6f4: ldur            x9, [x4, #7]
    // 0x130e6f8: r3 = Null
    //     0x130e6f8: add             x3, PP, #0x40, lsl #12  ; [pp+0x40aa8] Null
    //     0x130e6fc: ldr             x3, [x3, #0xaa8]
    // 0x130e700: blr             x9
    // 0x130e704: ldur            x1, [fp, #-0x58]
    // 0x130e708: r0 = LoadClassIdInstr(r1)
    //     0x130e708: ldur            x0, [x1, #-1]
    //     0x130e70c: ubfx            x0, x0, #0xc, #0x14
    // 0x130e710: ldur            x2, [fp, #-0x48]
    // 0x130e714: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130e714: sub             lr, x0, #0xfe
    //     0x130e718: ldr             lr, [x21, lr, lsl #3]
    //     0x130e71c: blr             lr
    // 0x130e720: cmp             w0, NULL
    // 0x130e724: b.ne            #0x130ea28
    // 0x130e728: r0 = false
    //     0x130e728: add             x0, NULL, #0x30  ; false
    // 0x130e72c: b               #0x130ea28
    // 0x130e730: r16 = Instance_CustomisationType
    //     0x130e730: add             x16, PP, #0x23, lsl #12  ; [pp+0x23660] Obj!CustomisationType@d756e1
    //     0x130e734: ldr             x16, [x16, #0x660]
    // 0x130e738: cmp             w1, w16
    // 0x130e73c: b.ne            #0x130e82c
    // 0x130e740: r1 = LoadStaticField(0xcb0)
    //     0x130e740: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130e744: ldr             x1, [x1, #0x1960]
    // 0x130e748: cmp             w1, NULL
    // 0x130e74c: b.ne            #0x130e764
    // 0x130e750: r1 = Instance_GetInstance
    //     0x130e750: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e754: ldr             x1, [x1, #0x900]
    // 0x130e758: StoreStaticField(0xcb0, r1)
    //     0x130e758: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130e75c: str             x1, [x2, #0x1960]
    // 0x130e760: b               #0x130e76c
    // 0x130e764: r1 = Instance_GetInstance
    //     0x130e764: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e768: ldr             x1, [x1, #0x900]
    // 0x130e76c: ldur            x16, [fp, #-0x20]
    // 0x130e770: r30 = Instance_GetInstance
    //     0x130e770: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e774: ldr             lr, [lr, #0x900]
    // 0x130e778: stp             lr, x16, [SP, #8]
    // 0x130e77c: str             NULL, [SP]
    // 0x130e780: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130e780: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130e784: r0 = find()
    //     0x130e784: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130e788: LoadField: r2 = r0->field_cb
    //     0x130e788: ldur            w2, [x0, #0xcb]
    // 0x130e78c: DecompressPointer r2
    //     0x130e78c: add             x2, x2, HEAP, lsl #32
    // 0x130e790: ldur            x0, [fp, #-0x40]
    // 0x130e794: stur            x2, [fp, #-0x50]
    // 0x130e798: LoadField: r1 = r0->field_7
    //     0x130e798: ldur            w1, [x0, #7]
    // 0x130e79c: DecompressPointer r1
    //     0x130e79c: add             x1, x1, HEAP, lsl #32
    // 0x130e7a0: cmp             w1, NULL
    // 0x130e7a4: b.ne            #0x130e7b0
    // 0x130e7a8: r0 = ""
    //     0x130e7a8: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130e7ac: b               #0x130e7b4
    // 0x130e7b0: mov             x0, x1
    // 0x130e7b4: mov             x1, x2
    // 0x130e7b8: stur            x0, [fp, #-0x48]
    // 0x130e7bc: r0 = value()
    //     0x130e7bc: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130e7c0: mov             x3, x0
    // 0x130e7c4: ldur            x0, [fp, #-0x50]
    // 0x130e7c8: stur            x3, [fp, #-0x58]
    // 0x130e7cc: LoadField: r2 = r0->field_7
    //     0x130e7cc: ldur            w2, [x0, #7]
    // 0x130e7d0: DecompressPointer r2
    //     0x130e7d0: add             x2, x2, HEAP, lsl #32
    // 0x130e7d4: ldur            x0, [fp, #-0x48]
    // 0x130e7d8: r1 = Null
    //     0x130e7d8: mov             x1, NULL
    // 0x130e7dc: cmp             w2, NULL
    // 0x130e7e0: b.eq            #0x130e800
    // 0x130e7e4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130e7e4: ldur            w4, [x2, #0x17]
    // 0x130e7e8: DecompressPointer r4
    //     0x130e7e8: add             x4, x4, HEAP, lsl #32
    // 0x130e7ec: r8 = X0
    //     0x130e7ec: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130e7f0: LoadField: r9 = r4->field_7
    //     0x130e7f0: ldur            x9, [x4, #7]
    // 0x130e7f4: r3 = Null
    //     0x130e7f4: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ab8] Null
    //     0x130e7f8: ldr             x3, [x3, #0xab8]
    // 0x130e7fc: blr             x9
    // 0x130e800: ldur            x1, [fp, #-0x58]
    // 0x130e804: r0 = LoadClassIdInstr(r1)
    //     0x130e804: ldur            x0, [x1, #-1]
    //     0x130e808: ubfx            x0, x0, #0xc, #0x14
    // 0x130e80c: ldur            x2, [fp, #-0x48]
    // 0x130e810: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130e810: sub             lr, x0, #0xfe
    //     0x130e814: ldr             lr, [x21, lr, lsl #3]
    //     0x130e818: blr             lr
    // 0x130e81c: cmp             w0, NULL
    // 0x130e820: b.ne            #0x130ea28
    // 0x130e824: r0 = false
    //     0x130e824: add             x0, NULL, #0x30  ; false
    // 0x130e828: b               #0x130ea28
    // 0x130e82c: r16 = Instance_CustomisationType
    //     0x130e82c: add             x16, PP, #0x23, lsl #12  ; [pp+0x23670] Obj!CustomisationType@d756c1
    //     0x130e830: ldr             x16, [x16, #0x670]
    // 0x130e834: cmp             w1, w16
    // 0x130e838: b.ne            #0x130e928
    // 0x130e83c: r1 = LoadStaticField(0xcb0)
    //     0x130e83c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130e840: ldr             x1, [x1, #0x1960]
    // 0x130e844: cmp             w1, NULL
    // 0x130e848: b.ne            #0x130e860
    // 0x130e84c: r1 = Instance_GetInstance
    //     0x130e84c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e850: ldr             x1, [x1, #0x900]
    // 0x130e854: StoreStaticField(0xcb0, r1)
    //     0x130e854: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130e858: str             x1, [x2, #0x1960]
    // 0x130e85c: b               #0x130e868
    // 0x130e860: r1 = Instance_GetInstance
    //     0x130e860: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e864: ldr             x1, [x1, #0x900]
    // 0x130e868: ldur            x16, [fp, #-0x20]
    // 0x130e86c: r30 = Instance_GetInstance
    //     0x130e86c: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e870: ldr             lr, [lr, #0x900]
    // 0x130e874: stp             lr, x16, [SP, #8]
    // 0x130e878: str             NULL, [SP]
    // 0x130e87c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130e87c: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130e880: r0 = find()
    //     0x130e880: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130e884: LoadField: r2 = r0->field_cb
    //     0x130e884: ldur            w2, [x0, #0xcb]
    // 0x130e888: DecompressPointer r2
    //     0x130e888: add             x2, x2, HEAP, lsl #32
    // 0x130e88c: ldur            x0, [fp, #-0x40]
    // 0x130e890: stur            x2, [fp, #-0x50]
    // 0x130e894: LoadField: r1 = r0->field_7
    //     0x130e894: ldur            w1, [x0, #7]
    // 0x130e898: DecompressPointer r1
    //     0x130e898: add             x1, x1, HEAP, lsl #32
    // 0x130e89c: cmp             w1, NULL
    // 0x130e8a0: b.ne            #0x130e8ac
    // 0x130e8a4: r0 = ""
    //     0x130e8a4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130e8a8: b               #0x130e8b0
    // 0x130e8ac: mov             x0, x1
    // 0x130e8b0: mov             x1, x2
    // 0x130e8b4: stur            x0, [fp, #-0x48]
    // 0x130e8b8: r0 = value()
    //     0x130e8b8: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130e8bc: mov             x3, x0
    // 0x130e8c0: ldur            x0, [fp, #-0x50]
    // 0x130e8c4: stur            x3, [fp, #-0x58]
    // 0x130e8c8: LoadField: r2 = r0->field_7
    //     0x130e8c8: ldur            w2, [x0, #7]
    // 0x130e8cc: DecompressPointer r2
    //     0x130e8cc: add             x2, x2, HEAP, lsl #32
    // 0x130e8d0: ldur            x0, [fp, #-0x48]
    // 0x130e8d4: r1 = Null
    //     0x130e8d4: mov             x1, NULL
    // 0x130e8d8: cmp             w2, NULL
    // 0x130e8dc: b.eq            #0x130e8fc
    // 0x130e8e0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130e8e0: ldur            w4, [x2, #0x17]
    // 0x130e8e4: DecompressPointer r4
    //     0x130e8e4: add             x4, x4, HEAP, lsl #32
    // 0x130e8e8: r8 = X0
    //     0x130e8e8: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130e8ec: LoadField: r9 = r4->field_7
    //     0x130e8ec: ldur            x9, [x4, #7]
    // 0x130e8f0: r3 = Null
    //     0x130e8f0: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ac8] Null
    //     0x130e8f4: ldr             x3, [x3, #0xac8]
    // 0x130e8f8: blr             x9
    // 0x130e8fc: ldur            x1, [fp, #-0x58]
    // 0x130e900: r0 = LoadClassIdInstr(r1)
    //     0x130e900: ldur            x0, [x1, #-1]
    //     0x130e904: ubfx            x0, x0, #0xc, #0x14
    // 0x130e908: ldur            x2, [fp, #-0x48]
    // 0x130e90c: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130e90c: sub             lr, x0, #0xfe
    //     0x130e910: ldr             lr, [x21, lr, lsl #3]
    //     0x130e914: blr             lr
    // 0x130e918: cmp             w0, NULL
    // 0x130e91c: b.ne            #0x130ea28
    // 0x130e920: r0 = false
    //     0x130e920: add             x0, NULL, #0x30  ; false
    // 0x130e924: b               #0x130ea28
    // 0x130e928: r16 = Instance_CustomisationType
    //     0x130e928: add             x16, PP, #0x23, lsl #12  ; [pp+0x23650] Obj!CustomisationType@d75701
    //     0x130e92c: ldr             x16, [x16, #0x650]
    // 0x130e930: cmp             w1, w16
    // 0x130e934: b.ne            #0x130ea24
    // 0x130e938: r1 = LoadStaticField(0xcb0)
    //     0x130e938: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x130e93c: ldr             x1, [x1, #0x1960]
    // 0x130e940: cmp             w1, NULL
    // 0x130e944: b.ne            #0x130e95c
    // 0x130e948: r1 = Instance_GetInstance
    //     0x130e948: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e94c: ldr             x1, [x1, #0x900]
    // 0x130e950: StoreStaticField(0xcb0, r1)
    //     0x130e950: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0x130e954: str             x1, [x2, #0x1960]
    // 0x130e958: b               #0x130e964
    // 0x130e95c: r1 = Instance_GetInstance
    //     0x130e95c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e960: ldr             x1, [x1, #0x900]
    // 0x130e964: ldur            x16, [fp, #-0x20]
    // 0x130e968: r30 = Instance_GetInstance
    //     0x130e968: add             lr, PP, #0xa, lsl #12  ; [pp+0xa900] Obj!GetInstance@d54111
    //     0x130e96c: ldr             lr, [lr, #0x900]
    // 0x130e970: stp             lr, x16, [SP, #8]
    // 0x130e974: str             NULL, [SP]
    // 0x130e978: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x130e978: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x130e97c: r0 = find()
    //     0x130e97c: bl              #0x887bcc  ; [package:get/get_instance/src/get_instance.dart] GetInstance::find
    // 0x130e980: LoadField: r2 = r0->field_cb
    //     0x130e980: ldur            w2, [x0, #0xcb]
    // 0x130e984: DecompressPointer r2
    //     0x130e984: add             x2, x2, HEAP, lsl #32
    // 0x130e988: ldur            x0, [fp, #-0x40]
    // 0x130e98c: stur            x2, [fp, #-0x48]
    // 0x130e990: LoadField: r1 = r0->field_7
    //     0x130e990: ldur            w1, [x0, #7]
    // 0x130e994: DecompressPointer r1
    //     0x130e994: add             x1, x1, HEAP, lsl #32
    // 0x130e998: cmp             w1, NULL
    // 0x130e99c: b.ne            #0x130e9a8
    // 0x130e9a0: r0 = ""
    //     0x130e9a0: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x130e9a4: b               #0x130e9ac
    // 0x130e9a8: mov             x0, x1
    // 0x130e9ac: mov             x1, x2
    // 0x130e9b0: stur            x0, [fp, #-0x40]
    // 0x130e9b4: r0 = value()
    //     0x130e9b4: bl              #0x151302c  ; [package:get/get_rx/src/rx_types/rx_types.dart] RxMap::value
    // 0x130e9b8: mov             x3, x0
    // 0x130e9bc: ldur            x0, [fp, #-0x48]
    // 0x130e9c0: stur            x3, [fp, #-0x50]
    // 0x130e9c4: LoadField: r2 = r0->field_7
    //     0x130e9c4: ldur            w2, [x0, #7]
    // 0x130e9c8: DecompressPointer r2
    //     0x130e9c8: add             x2, x2, HEAP, lsl #32
    // 0x130e9cc: ldur            x0, [fp, #-0x40]
    // 0x130e9d0: r1 = Null
    //     0x130e9d0: mov             x1, NULL
    // 0x130e9d4: cmp             w2, NULL
    // 0x130e9d8: b.eq            #0x130e9f8
    // 0x130e9dc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x130e9dc: ldur            w4, [x2, #0x17]
    // 0x130e9e0: DecompressPointer r4
    //     0x130e9e0: add             x4, x4, HEAP, lsl #32
    // 0x130e9e4: r8 = X0
    //     0x130e9e4: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x130e9e8: LoadField: r9 = r4->field_7
    //     0x130e9e8: ldur            x9, [x4, #7]
    // 0x130e9ec: r3 = Null
    //     0x130e9ec: add             x3, PP, #0x40, lsl #12  ; [pp+0x40ad8] Null
    //     0x130e9f0: ldr             x3, [x3, #0xad8]
    // 0x130e9f4: blr             x9
    // 0x130e9f8: ldur            x1, [fp, #-0x50]
    // 0x130e9fc: r0 = LoadClassIdInstr(r1)
    //     0x130e9fc: ldur            x0, [x1, #-1]
    //     0x130ea00: ubfx            x0, x0, #0xc, #0x14
    // 0x130ea04: ldur            x2, [fp, #-0x40]
    // 0x130ea08: r0 = GDT[cid_x0 + -0xfe]()
    //     0x130ea08: sub             lr, x0, #0xfe
    //     0x130ea0c: ldr             lr, [x21, lr, lsl #3]
    //     0x130ea10: blr             lr
    // 0x130ea14: cmp             w0, NULL
    // 0x130ea18: b.ne            #0x130ea28
    // 0x130ea1c: r0 = false
    //     0x130ea1c: add             x0, NULL, #0x30  ; false
    // 0x130ea20: b               #0x130ea28
    // 0x130ea24: ldur            x0, [fp, #-8]
    // 0x130ea28: mov             x6, x0
    // 0x130ea2c: ldur            x5, [fp, #-0x38]
    // 0x130ea30: ldur            x4, [fp, #-0x20]
    // 0x130ea34: ldur            x1, [fp, #-0x10]
    // 0x130ea38: ldur            x2, [fp, #-0x30]
    // 0x130ea3c: ldur            x3, [fp, #-0x28]
    // 0x130ea40: b               #0x130e46c
    // 0x130ea44: ldur            x0, [fp, #-8]
    // 0x130ea48: LeaveFrame
    //     0x130ea48: mov             SP, fp
    //     0x130ea4c: ldp             fp, lr, [SP], #0x10
    // 0x130ea50: ret
    //     0x130ea50: ret             
    // 0x130ea54: ldur            x0, [fp, #-0x10]
    // 0x130ea58: r0 = ConcurrentModificationError()
    //     0x130ea58: bl              #0x622324  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x130ea5c: mov             x1, x0
    // 0x130ea60: ldur            x0, [fp, #-0x10]
    // 0x130ea64: StoreField: r1->field_b = r0
    //     0x130ea64: stur            w0, [x1, #0xb]
    // 0x130ea68: mov             x0, x1
    // 0x130ea6c: r0 = Throw()
    //     0x130ea6c: bl              #0x16f5420  ; ThrowStub
    // 0x130ea70: brk             #0
    // 0x130ea74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130ea74: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130ea78: b               #0x130e3ec
    // 0x130ea7c: r0 = NullErrorSharedWithoutFPURegs()
    //     0x130ea7c: bl              #0x16f7ad8  ; NullErrorSharedWithoutFPURegsStub
    // 0x130ea80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x130ea80: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x130ea84: b               #0x130e480
  }
  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x136021c, size: 0x64
    // 0x136021c: EnterFrame
    //     0x136021c: stp             fp, lr, [SP, #-0x10]!
    //     0x1360220: mov             fp, SP
    // 0x1360224: AllocStack(0x18)
    //     0x1360224: sub             SP, SP, #0x18
    // 0x1360228: SetupParameters(CustomizedPage this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1360228: stur            x1, [fp, #-8]
    //     0x136022c: stur            x2, [fp, #-0x10]
    // 0x1360230: r1 = 2
    //     0x1360230: movz            x1, #0x2
    // 0x1360234: r0 = AllocateContext()
    //     0x1360234: bl              #0x16f6108  ; AllocateContextStub
    // 0x1360238: mov             x1, x0
    // 0x136023c: ldur            x0, [fp, #-8]
    // 0x1360240: stur            x1, [fp, #-0x18]
    // 0x1360244: StoreField: r1->field_f = r0
    //     0x1360244: stur            w0, [x1, #0xf]
    // 0x1360248: ldur            x0, [fp, #-0x10]
    // 0x136024c: StoreField: r1->field_13 = r0
    //     0x136024c: stur            w0, [x1, #0x13]
    // 0x1360250: r0 = Obx()
    //     0x1360250: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1360254: ldur            x2, [fp, #-0x18]
    // 0x1360258: r1 = Function '<anonymous closure>':.
    //     0x1360258: add             x1, PP, #0x40, lsl #12  ; [pp+0x40a20] AnonymousClosure: (0x130d9c0), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::bottomNavigationBar (0x136021c)
    //     0x136025c: ldr             x1, [x1, #0xa20]
    // 0x1360260: stur            x0, [fp, #-8]
    // 0x1360264: r0 = AllocateClosure()
    //     0x1360264: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1360268: mov             x1, x0
    // 0x136026c: ldur            x0, [fp, #-8]
    // 0x1360270: StoreField: r0->field_b = r1
    //     0x1360270: stur            w1, [x0, #0xb]
    // 0x1360274: LeaveFrame
    //     0x1360274: mov             SP, fp
    //     0x1360278: ldp             fp, lr, [SP], #0x10
    // 0x136027c: ret
    //     0x136027c: ret             
  }
  [closure] Null <anonymous closure>(dynamic, bool, int) {
    // ** addr: 0x13bf2a4, size: 0xcc
    // 0x13bf2a4: EnterFrame
    //     0x13bf2a4: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf2a8: mov             fp, SP
    // 0x13bf2ac: AllocStack(0x10)
    //     0x13bf2ac: sub             SP, SP, #0x10
    // 0x13bf2b0: SetupParameters()
    //     0x13bf2b0: ldr             x0, [fp, #0x20]
    //     0x13bf2b4: ldur            w2, [x0, #0x17]
    //     0x13bf2b8: add             x2, x2, HEAP, lsl #32
    //     0x13bf2bc: stur            x2, [fp, #-8]
    // 0x13bf2c0: CheckStackOverflow
    //     0x13bf2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf2c4: cmp             SP, x16
    //     0x13bf2c8: b.ls            #0x13bf368
    // 0x13bf2cc: LoadField: r1 = r2->field_f
    //     0x13bf2cc: ldur            w1, [x2, #0xf]
    // 0x13bf2d0: DecompressPointer r1
    //     0x13bf2d0: add             x1, x1, HEAP, lsl #32
    // 0x13bf2d4: r0 = controller()
    //     0x13bf2d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf2d8: LoadField: r1 = r0->field_7b
    //     0x13bf2d8: ldur            w1, [x0, #0x7b]
    // 0x13bf2dc: DecompressPointer r1
    //     0x13bf2dc: add             x1, x1, HEAP, lsl #32
    // 0x13bf2e0: ldr             x2, [fp, #0x18]
    // 0x13bf2e4: r0 = value=()
    //     0x13bf2e4: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf2e8: ldr             x0, [fp, #0x10]
    // 0x13bf2ec: r2 = LoadInt32Instr(r0)
    //     0x13bf2ec: sbfx            x2, x0, #1, #0x1f
    //     0x13bf2f0: tbz             w0, #0, #0x13bf2f8
    //     0x13bf2f4: ldur            x2, [x0, #7]
    // 0x13bf2f8: stur            x2, [fp, #-0x10]
    // 0x13bf2fc: cbz             x2, #0x13bf358
    // 0x13bf300: ldur            x0, [fp, #-8]
    // 0x13bf304: LoadField: r1 = r0->field_f
    //     0x13bf304: ldur            w1, [x0, #0xf]
    // 0x13bf308: DecompressPointer r1
    //     0x13bf308: add             x1, x1, HEAP, lsl #32
    // 0x13bf30c: r0 = controller()
    //     0x13bf30c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf310: LoadField: r2 = r0->field_67
    //     0x13bf310: ldur            w2, [x0, #0x67]
    // 0x13bf314: DecompressPointer r2
    //     0x13bf314: add             x2, x2, HEAP, lsl #32
    // 0x13bf318: mov             x1, x2
    // 0x13bf31c: stur            x2, [fp, #-8]
    // 0x13bf320: r0 = value()
    //     0x13bf320: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bf324: r1 = LoadInt32Instr(r0)
    //     0x13bf324: sbfx            x1, x0, #1, #0x1f
    //     0x13bf328: tbz             w0, #0, #0x13bf330
    //     0x13bf32c: ldur            x1, [x0, #7]
    // 0x13bf330: ldur            x0, [fp, #-0x10]
    // 0x13bf334: sub             x2, x1, x0
    // 0x13bf338: r0 = BoxInt64Instr(r2)
    //     0x13bf338: sbfiz           x0, x2, #1, #0x1f
    //     0x13bf33c: cmp             x2, x0, asr #1
    //     0x13bf340: b.eq            #0x13bf34c
    //     0x13bf344: bl              #0x16f7420  ; AllocateMintSharedWithoutFPURegsStub
    //     0x13bf348: stur            x2, [x0, #7]
    // 0x13bf34c: ldur            x1, [fp, #-8]
    // 0x13bf350: mov             x2, x0
    // 0x13bf354: r0 = value=()
    //     0x13bf354: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf358: r0 = Null
    //     0x13bf358: mov             x0, NULL
    // 0x13bf35c: LeaveFrame
    //     0x13bf35c: mov             SP, fp
    //     0x13bf360: ldp             fp, lr, [SP], #0x10
    // 0x13bf364: ret
    //     0x13bf364: ret             
    // 0x13bf368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf368: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf36c: b               #0x13bf2cc
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x13bf370, size: 0x254
    // 0x13bf370: EnterFrame
    //     0x13bf370: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf374: mov             fp, SP
    // 0x13bf378: AllocStack(0x20)
    //     0x13bf378: sub             SP, SP, #0x20
    // 0x13bf37c: SetupParameters()
    //     0x13bf37c: ldr             x0, [fp, #0x10]
    //     0x13bf380: ldur            w2, [x0, #0x17]
    //     0x13bf384: add             x2, x2, HEAP, lsl #32
    //     0x13bf388: stur            x2, [fp, #-8]
    // 0x13bf38c: CheckStackOverflow
    //     0x13bf38c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf390: cmp             SP, x16
    //     0x13bf394: b.ls            #0x13bf5bc
    // 0x13bf398: LoadField: r1 = r2->field_f
    //     0x13bf398: ldur            w1, [x2, #0xf]
    // 0x13bf39c: DecompressPointer r1
    //     0x13bf39c: add             x1, x1, HEAP, lsl #32
    // 0x13bf3a0: r0 = controller()
    //     0x13bf3a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf3a4: LoadField: r1 = r0->field_53
    //     0x13bf3a4: ldur            w1, [x0, #0x53]
    // 0x13bf3a8: DecompressPointer r1
    //     0x13bf3a8: add             x1, x1, HEAP, lsl #32
    // 0x13bf3ac: r0 = value()
    //     0x13bf3ac: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13bf3b0: ldur            x2, [fp, #-8]
    // 0x13bf3b4: stur            x0, [fp, #-0x10]
    // 0x13bf3b8: LoadField: r1 = r2->field_f
    //     0x13bf3b8: ldur            w1, [x2, #0xf]
    // 0x13bf3bc: DecompressPointer r1
    //     0x13bf3bc: add             x1, x1, HEAP, lsl #32
    // 0x13bf3c0: r0 = controller()
    //     0x13bf3c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf3c4: LoadField: r1 = r0->field_9b
    //     0x13bf3c4: ldur            w1, [x0, #0x9b]
    // 0x13bf3c8: DecompressPointer r1
    //     0x13bf3c8: add             x1, x1, HEAP, lsl #32
    // 0x13bf3cc: stur            x1, [fp, #-0x18]
    // 0x13bf3d0: r0 = CustomizedView()
    //     0x13bf3d0: bl              #0x13bf5c4  ; AllocateCustomizedViewStub -> CustomizedView (size=0x4c)
    // 0x13bf3d4: mov             x3, x0
    // 0x13bf3d8: ldur            x0, [fp, #-0x10]
    // 0x13bf3dc: stur            x3, [fp, #-0x20]
    // 0x13bf3e0: StoreField: r3->field_b = r0
    //     0x13bf3e0: stur            w0, [x3, #0xb]
    // 0x13bf3e4: ldur            x0, [fp, #-0x18]
    // 0x13bf3e8: StoreField: r3->field_f = r0
    //     0x13bf3e8: stur            w0, [x3, #0xf]
    // 0x13bf3ec: ldur            x2, [fp, #-8]
    // 0x13bf3f0: r1 = Function '<anonymous closure>':.
    //     0x13bf3f0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40af8] AnonymousClosure: (0x13bf15c), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf3f4: ldr             x1, [x1, #0xaf8]
    // 0x13bf3f8: r0 = AllocateClosure()
    //     0x13bf3f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf3fc: mov             x1, x0
    // 0x13bf400: ldur            x0, [fp, #-0x20]
    // 0x13bf404: ArrayStore: r0[0] = r1  ; List_4
    //     0x13bf404: stur            w1, [x0, #0x17]
    // 0x13bf408: ldur            x2, [fp, #-8]
    // 0x13bf40c: r1 = Function '<anonymous closure>':.
    //     0x13bf40c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b00] AnonymousClosure: (0x13bf7e8), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::body (0x14dbc58)
    //     0x13bf410: ldr             x1, [x1, #0xb00]
    // 0x13bf414: r0 = AllocateClosure()
    //     0x13bf414: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf418: mov             x1, x0
    // 0x13bf41c: ldur            x0, [fp, #-0x20]
    // 0x13bf420: StoreField: r0->field_13 = r1
    //     0x13bf420: stur            w1, [x0, #0x13]
    // 0x13bf424: ldur            x2, [fp, #-8]
    // 0x13bf428: r1 = Function '<anonymous closure>':.
    //     0x13bf428: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b08] AnonymousClosure: (0x13bedd8), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf42c: ldr             x1, [x1, #0xb08]
    // 0x13bf430: r0 = AllocateClosure()
    //     0x13bf430: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf434: mov             x1, x0
    // 0x13bf438: ldur            x0, [fp, #-0x20]
    // 0x13bf43c: StoreField: r0->field_1b = r1
    //     0x13bf43c: stur            w1, [x0, #0x1b]
    // 0x13bf440: ldur            x2, [fp, #-8]
    // 0x13bf444: r1 = Function '<anonymous closure>':.
    //     0x13bf444: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b10] AnonymousClosure: (0x13bef70), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf448: ldr             x1, [x1, #0xb10]
    // 0x13bf44c: r0 = AllocateClosure()
    //     0x13bf44c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf450: mov             x1, x0
    // 0x13bf454: ldur            x0, [fp, #-0x20]
    // 0x13bf458: StoreField: r0->field_1f = r1
    //     0x13bf458: stur            w1, [x0, #0x1f]
    // 0x13bf45c: ldur            x2, [fp, #-8]
    // 0x13bf460: r1 = Function '<anonymous closure>':.
    //     0x13bf460: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b18] AnonymousClosure: (0x13bf748), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::body (0x14dbc58)
    //     0x13bf464: ldr             x1, [x1, #0xb18]
    // 0x13bf468: r0 = AllocateClosure()
    //     0x13bf468: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf46c: mov             x1, x0
    // 0x13bf470: ldur            x0, [fp, #-0x20]
    // 0x13bf474: StoreField: r0->field_23 = r1
    //     0x13bf474: stur            w1, [x0, #0x23]
    // 0x13bf478: ldur            x2, [fp, #-8]
    // 0x13bf47c: r1 = Function '<anonymous closure>':.
    //     0x13bf47c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b20] AnonymousClosure: (0x13bedd8), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf480: ldr             x1, [x1, #0xb20]
    // 0x13bf484: r0 = AllocateClosure()
    //     0x13bf484: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf488: mov             x1, x0
    // 0x13bf48c: ldur            x0, [fp, #-0x20]
    // 0x13bf490: StoreField: r0->field_27 = r1
    //     0x13bf490: stur            w1, [x0, #0x27]
    // 0x13bf494: ldur            x2, [fp, #-8]
    // 0x13bf498: r1 = Function '<anonymous closure>':.
    //     0x13bf498: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b28] AnonymousClosure: (0x13bed04), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf49c: ldr             x1, [x1, #0xb28]
    // 0x13bf4a0: r0 = AllocateClosure()
    //     0x13bf4a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf4a4: mov             x1, x0
    // 0x13bf4a8: ldur            x0, [fp, #-0x20]
    // 0x13bf4ac: StoreField: r0->field_2b = r1
    //     0x13bf4ac: stur            w1, [x0, #0x2b]
    // 0x13bf4b0: ldur            x2, [fp, #-8]
    // 0x13bf4b4: r1 = Function '<anonymous closure>':.
    //     0x13bf4b4: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b30] AnonymousClosure: (0x13bf6a8), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::body (0x14dbc58)
    //     0x13bf4b8: ldr             x1, [x1, #0xb30]
    // 0x13bf4bc: r0 = AllocateClosure()
    //     0x13bf4bc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf4c0: mov             x1, x0
    // 0x13bf4c4: ldur            x0, [fp, #-0x20]
    // 0x13bf4c8: StoreField: r0->field_2f = r1
    //     0x13bf4c8: stur            w1, [x0, #0x2f]
    // 0x13bf4cc: ldur            x2, [fp, #-8]
    // 0x13bf4d0: r1 = Function '<anonymous closure>':.
    //     0x13bf4d0: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b38] AnonymousClosure: (0x13beb80), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf4d4: ldr             x1, [x1, #0xb38]
    // 0x13bf4d8: r0 = AllocateClosure()
    //     0x13bf4d8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf4dc: mov             x1, x0
    // 0x13bf4e0: ldur            x0, [fp, #-0x20]
    // 0x13bf4e4: StoreField: r0->field_33 = r1
    //     0x13bf4e4: stur            w1, [x0, #0x33]
    // 0x13bf4e8: ldur            x2, [fp, #-8]
    // 0x13bf4ec: r1 = Function '<anonymous closure>':.
    //     0x13bf4ec: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b40] AnonymousClosure: (0x13be884), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf4f0: ldr             x1, [x1, #0xb40]
    // 0x13bf4f4: r0 = AllocateClosure()
    //     0x13bf4f4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf4f8: mov             x1, x0
    // 0x13bf4fc: ldur            x0, [fp, #-0x20]
    // 0x13bf500: StoreField: r0->field_37 = r1
    //     0x13bf500: stur            w1, [x0, #0x37]
    // 0x13bf504: ldur            x2, [fp, #-8]
    // 0x13bf508: r1 = Function '<anonymous closure>':.
    //     0x13bf508: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b48] AnonymousClosure: (0x13bf5d0), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::body (0x14dbc58)
    //     0x13bf50c: ldr             x1, [x1, #0xb48]
    // 0x13bf510: r0 = AllocateClosure()
    //     0x13bf510: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf514: mov             x1, x0
    // 0x13bf518: ldur            x0, [fp, #-0x20]
    // 0x13bf51c: StoreField: r0->field_3b = r1
    //     0x13bf51c: stur            w1, [x0, #0x3b]
    // 0x13bf520: ldur            x2, [fp, #-8]
    // 0x13bf524: r1 = Function '<anonymous closure>':.
    //     0x13bf524: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b50] AnonymousClosure: (0x13bf2a4), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::body (0x14dbc58)
    //     0x13bf528: ldr             x1, [x1, #0xb50]
    // 0x13bf52c: r0 = AllocateClosure()
    //     0x13bf52c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf530: mov             x1, x0
    // 0x13bf534: ldur            x0, [fp, #-0x20]
    // 0x13bf538: StoreField: r0->field_3f = r1
    //     0x13bf538: stur            w1, [x0, #0x3f]
    // 0x13bf53c: ldur            x2, [fp, #-8]
    // 0x13bf540: r1 = Function '<anonymous closure>':.
    //     0x13bf540: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b58] AnonymousClosure: (0x13be064), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf544: ldr             x1, [x1, #0xb58]
    // 0x13bf548: r0 = AllocateClosure()
    //     0x13bf548: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf54c: mov             x1, x0
    // 0x13bf550: ldur            x0, [fp, #-0x20]
    // 0x13bf554: StoreField: r0->field_43 = r1
    //     0x13bf554: stur            w1, [x0, #0x43]
    // 0x13bf558: ldur            x2, [fp, #-8]
    // 0x13bf55c: r1 = Function '<anonymous closure>':.
    //     0x13bf55c: add             x1, PP, #0x40, lsl #12  ; [pp+0x40b60] AnonymousClosure: (0x13bdd60), in [package:customer_app/app/presentation/views/line/customization/customized_page.dart] CustomizedPage::body (0x15058f0)
    //     0x13bf560: ldr             x1, [x1, #0xb60]
    // 0x13bf564: r0 = AllocateClosure()
    //     0x13bf564: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13bf568: mov             x1, x0
    // 0x13bf56c: ldur            x0, [fp, #-0x20]
    // 0x13bf570: StoreField: r0->field_47 = r1
    //     0x13bf570: stur            w1, [x0, #0x47]
    // 0x13bf574: r0 = SingleChildScrollView()
    //     0x13bf574: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x13bf578: r1 = Instance_Axis
    //     0x13bf578: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13bf57c: StoreField: r0->field_b = r1
    //     0x13bf57c: stur            w1, [x0, #0xb]
    // 0x13bf580: r1 = false
    //     0x13bf580: add             x1, NULL, #0x30  ; false
    // 0x13bf584: StoreField: r0->field_f = r1
    //     0x13bf584: stur            w1, [x0, #0xf]
    // 0x13bf588: ldur            x1, [fp, #-0x20]
    // 0x13bf58c: StoreField: r0->field_23 = r1
    //     0x13bf58c: stur            w1, [x0, #0x23]
    // 0x13bf590: r1 = Instance_DragStartBehavior
    //     0x13bf590: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x13bf594: StoreField: r0->field_27 = r1
    //     0x13bf594: stur            w1, [x0, #0x27]
    // 0x13bf598: r1 = Instance_Clip
    //     0x13bf598: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x13bf59c: ldr             x1, [x1, #0x7e0]
    // 0x13bf5a0: StoreField: r0->field_2b = r1
    //     0x13bf5a0: stur            w1, [x0, #0x2b]
    // 0x13bf5a4: r1 = Instance_HitTestBehavior
    //     0x13bf5a4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x13bf5a8: ldr             x1, [x1, #0x288]
    // 0x13bf5ac: StoreField: r0->field_2f = r1
    //     0x13bf5ac: stur            w1, [x0, #0x2f]
    // 0x13bf5b0: LeaveFrame
    //     0x13bf5b0: mov             SP, fp
    //     0x13bf5b4: ldp             fp, lr, [SP], #0x10
    // 0x13bf5b8: ret
    //     0x13bf5b8: ret             
    // 0x13bf5bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf5bc: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf5c0: b               #0x13bf398
  }
  [closure] Null <anonymous closure>(dynamic, int, bool, bool, CustomerResponse) {
    // ** addr: 0x13bf5d0, size: 0xd8
    // 0x13bf5d0: EnterFrame
    //     0x13bf5d0: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf5d4: mov             fp, SP
    // 0x13bf5d8: AllocStack(0x10)
    //     0x13bf5d8: sub             SP, SP, #0x10
    // 0x13bf5dc: SetupParameters()
    //     0x13bf5dc: ldr             x0, [fp, #0x30]
    //     0x13bf5e0: ldur            w2, [x0, #0x17]
    //     0x13bf5e4: add             x2, x2, HEAP, lsl #32
    //     0x13bf5e8: stur            x2, [fp, #-8]
    // 0x13bf5ec: CheckStackOverflow
    //     0x13bf5ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf5f0: cmp             SP, x16
    //     0x13bf5f4: b.ls            #0x13bf6a0
    // 0x13bf5f8: LoadField: r1 = r2->field_f
    //     0x13bf5f8: ldur            w1, [x2, #0xf]
    // 0x13bf5fc: DecompressPointer r1
    //     0x13bf5fc: add             x1, x1, HEAP, lsl #32
    // 0x13bf600: r0 = controller()
    //     0x13bf600: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf604: mov             x1, x0
    // 0x13bf608: ldr             x0, [fp, #0x28]
    // 0x13bf60c: r3 = LoadInt32Instr(r0)
    //     0x13bf60c: sbfx            x3, x0, #1, #0x1f
    //     0x13bf610: tbz             w0, #0, #0x13bf618
    //     0x13bf614: ldur            x3, [x0, #7]
    // 0x13bf618: mov             x2, x3
    // 0x13bf61c: stur            x3, [fp, #-0x10]
    // 0x13bf620: r0 = priceDecrement()
    //     0x13bf620: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13bf624: ldr             x0, [fp, #0x20]
    // 0x13bf628: tbnz            w0, #4, #0x13bf650
    // 0x13bf62c: ldur            x0, [fp, #-8]
    // 0x13bf630: LoadField: r1 = r0->field_f
    //     0x13bf630: ldur            w1, [x0, #0xf]
    // 0x13bf634: DecompressPointer r1
    //     0x13bf634: add             x1, x1, HEAP, lsl #32
    // 0x13bf638: r0 = controller()
    //     0x13bf638: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf63c: LoadField: r1 = r0->field_77
    //     0x13bf63c: ldur            w1, [x0, #0x77]
    // 0x13bf640: DecompressPointer r1
    //     0x13bf640: add             x1, x1, HEAP, lsl #32
    // 0x13bf644: r2 = false
    //     0x13bf644: add             x2, NULL, #0x30  ; false
    // 0x13bf648: r0 = value=()
    //     0x13bf648: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf64c: b               #0x13bf670
    // 0x13bf650: ldur            x0, [fp, #-8]
    // 0x13bf654: LoadField: r1 = r0->field_f
    //     0x13bf654: ldur            w1, [x0, #0xf]
    // 0x13bf658: DecompressPointer r1
    //     0x13bf658: add             x1, x1, HEAP, lsl #32
    // 0x13bf65c: r0 = controller()
    //     0x13bf65c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf660: LoadField: r1 = r0->field_77
    //     0x13bf660: ldur            w1, [x0, #0x77]
    // 0x13bf664: DecompressPointer r1
    //     0x13bf664: add             x1, x1, HEAP, lsl #32
    // 0x13bf668: ldr             x2, [fp, #0x18]
    // 0x13bf66c: r0 = value=()
    //     0x13bf66c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf670: ldur            x0, [fp, #-8]
    // 0x13bf674: LoadField: r1 = r0->field_f
    //     0x13bf674: ldur            w1, [x0, #0xf]
    // 0x13bf678: DecompressPointer r1
    //     0x13bf678: add             x1, x1, HEAP, lsl #32
    // 0x13bf67c: r0 = controller()
    //     0x13bf67c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf680: mov             x1, x0
    // 0x13bf684: ldr             x2, [fp, #0x10]
    // 0x13bf688: ldur            x3, [fp, #-0x10]
    // 0x13bf68c: r0 = removeCustomerCustomisationList()
    //     0x13bf68c: bl              #0x13be3f0  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::removeCustomerCustomisationList
    // 0x13bf690: r0 = Null
    //     0x13bf690: mov             x0, NULL
    // 0x13bf694: LeaveFrame
    //     0x13bf694: mov             SP, fp
    //     0x13bf698: ldp             fp, lr, [SP], #0x10
    // 0x13bf69c: ret
    //     0x13bf69c: ret             
    // 0x13bf6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf6a0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf6a4: b               #0x13bf5f8
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0x13bf6a8, size: 0xa0
    // 0x13bf6a8: EnterFrame
    //     0x13bf6a8: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf6ac: mov             fp, SP
    // 0x13bf6b0: AllocStack(0x8)
    //     0x13bf6b0: sub             SP, SP, #8
    // 0x13bf6b4: SetupParameters()
    //     0x13bf6b4: ldr             x0, [fp, #0x28]
    //     0x13bf6b8: ldur            w2, [x0, #0x17]
    //     0x13bf6bc: add             x2, x2, HEAP, lsl #32
    //     0x13bf6c0: stur            x2, [fp, #-8]
    // 0x13bf6c4: CheckStackOverflow
    //     0x13bf6c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf6c8: cmp             SP, x16
    //     0x13bf6cc: b.ls            #0x13bf740
    // 0x13bf6d0: LoadField: r1 = r2->field_f
    //     0x13bf6d0: ldur            w1, [x2, #0xf]
    // 0x13bf6d4: DecompressPointer r1
    //     0x13bf6d4: add             x1, x1, HEAP, lsl #32
    // 0x13bf6d8: r0 = controller()
    //     0x13bf6d8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf6dc: mov             x1, x0
    // 0x13bf6e0: ldr             x0, [fp, #0x20]
    // 0x13bf6e4: r2 = LoadInt32Instr(r0)
    //     0x13bf6e4: sbfx            x2, x0, #1, #0x1f
    //     0x13bf6e8: tbz             w0, #0, #0x13bf6f0
    //     0x13bf6ec: ldur            x2, [x0, #7]
    // 0x13bf6f0: r0 = priceDecrement()
    //     0x13bf6f0: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13bf6f4: ldur            x0, [fp, #-8]
    // 0x13bf6f8: LoadField: r1 = r0->field_f
    //     0x13bf6f8: ldur            w1, [x0, #0xf]
    // 0x13bf6fc: DecompressPointer r1
    //     0x13bf6fc: add             x1, x1, HEAP, lsl #32
    // 0x13bf700: r0 = controller()
    //     0x13bf700: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf704: LoadField: r1 = r0->field_7f
    //     0x13bf704: ldur            w1, [x0, #0x7f]
    // 0x13bf708: DecompressPointer r1
    //     0x13bf708: add             x1, x1, HEAP, lsl #32
    // 0x13bf70c: ldr             x2, [fp, #0x10]
    // 0x13bf710: r0 = value=()
    //     0x13bf710: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf714: ldur            x0, [fp, #-8]
    // 0x13bf718: LoadField: r1 = r0->field_f
    //     0x13bf718: ldur            w1, [x0, #0xf]
    // 0x13bf71c: DecompressPointer r1
    //     0x13bf71c: add             x1, x1, HEAP, lsl #32
    // 0x13bf720: r0 = controller()
    //     0x13bf720: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf724: mov             x1, x0
    // 0x13bf728: ldr             x2, [fp, #0x18]
    // 0x13bf72c: r0 = setProductCustomisationList()
    //     0x13bf72c: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bf730: r0 = Null
    //     0x13bf730: mov             x0, NULL
    // 0x13bf734: LeaveFrame
    //     0x13bf734: mov             SP, fp
    //     0x13bf738: ldp             fp, lr, [SP], #0x10
    // 0x13bf73c: ret
    //     0x13bf73c: ret             
    // 0x13bf740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf740: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf744: b               #0x13bf6d0
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>, bool) {
    // ** addr: 0x13bf748, size: 0xa0
    // 0x13bf748: EnterFrame
    //     0x13bf748: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf74c: mov             fp, SP
    // 0x13bf750: AllocStack(0x8)
    //     0x13bf750: sub             SP, SP, #8
    // 0x13bf754: SetupParameters()
    //     0x13bf754: ldr             x0, [fp, #0x28]
    //     0x13bf758: ldur            w2, [x0, #0x17]
    //     0x13bf75c: add             x2, x2, HEAP, lsl #32
    //     0x13bf760: stur            x2, [fp, #-8]
    // 0x13bf764: CheckStackOverflow
    //     0x13bf764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf768: cmp             SP, x16
    //     0x13bf76c: b.ls            #0x13bf7e0
    // 0x13bf770: LoadField: r1 = r2->field_f
    //     0x13bf770: ldur            w1, [x2, #0xf]
    // 0x13bf774: DecompressPointer r1
    //     0x13bf774: add             x1, x1, HEAP, lsl #32
    // 0x13bf778: r0 = controller()
    //     0x13bf778: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf77c: mov             x1, x0
    // 0x13bf780: ldr             x0, [fp, #0x20]
    // 0x13bf784: r2 = LoadInt32Instr(r0)
    //     0x13bf784: sbfx            x2, x0, #1, #0x1f
    //     0x13bf788: tbz             w0, #0, #0x13bf790
    //     0x13bf78c: ldur            x2, [x0, #7]
    // 0x13bf790: r0 = priceDecrement()
    //     0x13bf790: bl              #0x13be7f8  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceDecrement
    // 0x13bf794: ldur            x0, [fp, #-8]
    // 0x13bf798: LoadField: r1 = r0->field_f
    //     0x13bf798: ldur            w1, [x0, #0xf]
    // 0x13bf79c: DecompressPointer r1
    //     0x13bf79c: add             x1, x1, HEAP, lsl #32
    // 0x13bf7a0: r0 = controller()
    //     0x13bf7a0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf7a4: LoadField: r1 = r0->field_73
    //     0x13bf7a4: ldur            w1, [x0, #0x73]
    // 0x13bf7a8: DecompressPointer r1
    //     0x13bf7a8: add             x1, x1, HEAP, lsl #32
    // 0x13bf7ac: ldr             x2, [fp, #0x10]
    // 0x13bf7b0: r0 = value=()
    //     0x13bf7b0: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf7b4: ldur            x0, [fp, #-8]
    // 0x13bf7b8: LoadField: r1 = r0->field_f
    //     0x13bf7b8: ldur            w1, [x0, #0xf]
    // 0x13bf7bc: DecompressPointer r1
    //     0x13bf7bc: add             x1, x1, HEAP, lsl #32
    // 0x13bf7c0: r0 = controller()
    //     0x13bf7c0: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf7c4: mov             x1, x0
    // 0x13bf7c8: ldr             x2, [fp, #0x18]
    // 0x13bf7cc: r0 = setProductCustomisationList()
    //     0x13bf7cc: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bf7d0: r0 = Null
    //     0x13bf7d0: mov             x0, NULL
    // 0x13bf7d4: LeaveFrame
    //     0x13bf7d4: mov             SP, fp
    //     0x13bf7d8: ldp             fp, lr, [SP], #0x10
    // 0x13bf7dc: ret
    //     0x13bf7dc: ret             
    // 0x13bf7e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf7e0: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf7e4: b               #0x13bf770
  }
  [closure] Null <anonymous closure>(dynamic, int, List<ProductCustomisation>) {
    // ** addr: 0x13bf7e8, size: 0xa0
    // 0x13bf7e8: EnterFrame
    //     0x13bf7e8: stp             fp, lr, [SP, #-0x10]!
    //     0x13bf7ec: mov             fp, SP
    // 0x13bf7f0: AllocStack(0x8)
    //     0x13bf7f0: sub             SP, SP, #8
    // 0x13bf7f4: SetupParameters()
    //     0x13bf7f4: ldr             x0, [fp, #0x20]
    //     0x13bf7f8: ldur            w2, [x0, #0x17]
    //     0x13bf7fc: add             x2, x2, HEAP, lsl #32
    //     0x13bf800: stur            x2, [fp, #-8]
    // 0x13bf804: CheckStackOverflow
    //     0x13bf804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13bf808: cmp             SP, x16
    //     0x13bf80c: b.ls            #0x13bf880
    // 0x13bf810: LoadField: r1 = r2->field_f
    //     0x13bf810: ldur            w1, [x2, #0xf]
    // 0x13bf814: DecompressPointer r1
    //     0x13bf814: add             x1, x1, HEAP, lsl #32
    // 0x13bf818: r0 = controller()
    //     0x13bf818: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf81c: mov             x1, x0
    // 0x13bf820: ldr             x0, [fp, #0x18]
    // 0x13bf824: r2 = LoadInt32Instr(r0)
    //     0x13bf824: sbfx            x2, x0, #1, #0x1f
    //     0x13bf828: tbz             w0, #0, #0x13bf830
    //     0x13bf82c: ldur            x2, [x0, #7]
    // 0x13bf830: r0 = priceIncrement()
    //     0x13bf830: bl              #0x13bf240  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::priceIncrement
    // 0x13bf834: ldur            x0, [fp, #-8]
    // 0x13bf838: LoadField: r1 = r0->field_f
    //     0x13bf838: ldur            w1, [x0, #0xf]
    // 0x13bf83c: DecompressPointer r1
    //     0x13bf83c: add             x1, x1, HEAP, lsl #32
    // 0x13bf840: r0 = controller()
    //     0x13bf840: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf844: mov             x1, x0
    // 0x13bf848: ldr             x2, [fp, #0x10]
    // 0x13bf84c: r0 = setProductCustomisationList()
    //     0x13bf84c: bl              #0x13bf1dc  ; [package:customer_app/app/presentation/controllers/customization/customization_controller.dart] CustomizationController::setProductCustomisationList
    // 0x13bf850: ldur            x0, [fp, #-8]
    // 0x13bf854: LoadField: r1 = r0->field_f
    //     0x13bf854: ldur            w1, [x0, #0xf]
    // 0x13bf858: DecompressPointer r1
    //     0x13bf858: add             x1, x1, HEAP, lsl #32
    // 0x13bf85c: r0 = controller()
    //     0x13bf85c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13bf860: LoadField: r1 = r0->field_6f
    //     0x13bf860: ldur            w1, [x0, #0x6f]
    // 0x13bf864: DecompressPointer r1
    //     0x13bf864: add             x1, x1, HEAP, lsl #32
    // 0x13bf868: r2 = false
    //     0x13bf868: add             x2, NULL, #0x30  ; false
    // 0x13bf86c: r0 = value=()
    //     0x13bf86c: bl              #0x89d2fc  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value=
    // 0x13bf870: r0 = Null
    //     0x13bf870: mov             x0, NULL
    // 0x13bf874: LeaveFrame
    //     0x13bf874: mov             SP, fp
    //     0x13bf878: ldp             fp, lr, [SP], #0x10
    // 0x13bf87c: ret
    //     0x13bf87c: ret             
    // 0x13bf880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13bf880: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13bf884: b               #0x13bf810
  }
  _ body(/* No info */) {
    // ** addr: 0x14dbc58, size: 0x88
    // 0x14dbc58: EnterFrame
    //     0x14dbc58: stp             fp, lr, [SP, #-0x10]!
    //     0x14dbc5c: mov             fp, SP
    // 0x14dbc60: AllocStack(0x18)
    //     0x14dbc60: sub             SP, SP, #0x18
    // 0x14dbc64: SetupParameters(CustomizedPage this /* r1 => r1, fp-0x8 */)
    //     0x14dbc64: stur            x1, [fp, #-8]
    // 0x14dbc68: r1 = 1
    //     0x14dbc68: movz            x1, #0x1
    // 0x14dbc6c: r0 = AllocateContext()
    //     0x14dbc6c: bl              #0x16f6108  ; AllocateContextStub
    // 0x14dbc70: mov             x1, x0
    // 0x14dbc74: ldur            x0, [fp, #-8]
    // 0x14dbc78: stur            x1, [fp, #-0x10]
    // 0x14dbc7c: StoreField: r1->field_f = r0
    //     0x14dbc7c: stur            w0, [x1, #0xf]
    // 0x14dbc80: r0 = Obx()
    //     0x14dbc80: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x14dbc84: ldur            x2, [fp, #-0x10]
    // 0x14dbc88: r1 = Function '<anonymous closure>':.
    //     0x14dbc88: add             x1, PP, #0x40, lsl #12  ; [pp+0x40ae8] AnonymousClosure: (0x13bf370), in [package:customer_app/app/presentation/views/glass/customization/customized_page.dart] CustomizedPage::body (0x14dbc58)
    //     0x14dbc8c: ldr             x1, [x1, #0xae8]
    // 0x14dbc90: stur            x0, [fp, #-8]
    // 0x14dbc94: r0 = AllocateClosure()
    //     0x14dbc94: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dbc98: mov             x1, x0
    // 0x14dbc9c: ldur            x0, [fp, #-8]
    // 0x14dbca0: StoreField: r0->field_b = r1
    //     0x14dbca0: stur            w1, [x0, #0xb]
    // 0x14dbca4: r0 = WillPopScope()
    //     0x14dbca4: bl              #0xc5cea8  ; AllocateWillPopScopeStub -> WillPopScope (size=0x14)
    // 0x14dbca8: mov             x3, x0
    // 0x14dbcac: ldur            x0, [fp, #-8]
    // 0x14dbcb0: stur            x3, [fp, #-0x18]
    // 0x14dbcb4: StoreField: r3->field_b = r0
    //     0x14dbcb4: stur            w0, [x3, #0xb]
    // 0x14dbcb8: ldur            x2, [fp, #-0x10]
    // 0x14dbcbc: r1 = Function '<anonymous closure>':.
    //     0x14dbcbc: add             x1, PP, #0x40, lsl #12  ; [pp+0x40af0] AnonymousClosure: (0x137aadc), in [package:customer_app/app/presentation/views/line/post_order/replace_order/replace_call_order_view.dart] ReplaceCallOrderView::body (0x1506d28)
    //     0x14dbcc0: ldr             x1, [x1, #0xaf0]
    // 0x14dbcc4: r0 = AllocateClosure()
    //     0x14dbcc4: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x14dbcc8: mov             x1, x0
    // 0x14dbccc: ldur            x0, [fp, #-0x18]
    // 0x14dbcd0: StoreField: r0->field_f = r1
    //     0x14dbcd0: stur            w1, [x0, #0xf]
    // 0x14dbcd4: LeaveFrame
    //     0x14dbcd4: mov             SP, fp
    //     0x14dbcd8: ldp             fp, lr, [SP], #0x10
    // 0x14dbcdc: ret
    //     0x14dbcdc: ret             
  }
}
