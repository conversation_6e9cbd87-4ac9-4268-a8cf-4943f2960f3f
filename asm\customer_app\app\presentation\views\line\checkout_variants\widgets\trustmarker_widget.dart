// lib: , url: package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart

// class id: 1049501, size: 0x8
class :: {
}

// class id: 3261, size: 0x14, field offset: 0x14
class _TrustMarkerWidgetState extends State<dynamic> {

  [closure] void <anonymous closure>(dynamic, Duration) {
    // ** addr: 0x9071e8, size: 0x70
    // 0x9071e8: EnterFrame
    //     0x9071e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9071ec: mov             fp, SP
    // 0x9071f0: AllocStack(0x10)
    //     0x9071f0: sub             SP, SP, #0x10
    // 0x9071f4: SetupParameters()
    //     0x9071f4: ldr             x0, [fp, #0x18]
    //     0x9071f8: ldur            w1, [x0, #0x17]
    //     0x9071fc: add             x1, x1, HEAP, lsl #32
    // 0x907200: CheckStackOverflow
    //     0x907200: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x907204: cmp             SP, x16
    //     0x907208: b.ls            #0x90724c
    // 0x90720c: LoadField: r0 = r1->field_f
    //     0x90720c: ldur            w0, [x1, #0xf]
    // 0x907210: DecompressPointer r0
    //     0x907210: add             x0, x0, HEAP, lsl #32
    // 0x907214: LoadField: r1 = r0->field_b
    //     0x907214: ldur            w1, [x0, #0xb]
    // 0x907218: DecompressPointer r1
    //     0x907218: add             x1, x1, HEAP, lsl #32
    // 0x90721c: cmp             w1, NULL
    // 0x907220: b.eq            #0x907254
    // 0x907224: LoadField: r0 = r1->field_f
    //     0x907224: ldur            w0, [x1, #0xf]
    // 0x907228: DecompressPointer r0
    //     0x907228: add             x0, x0, HEAP, lsl #32
    // 0x90722c: r16 = true
    //     0x90722c: add             x16, NULL, #0x20  ; true
    // 0x907230: stp             x16, x0, [SP]
    // 0x907234: ClosureCall
    //     0x907234: ldr             x4, [PP, #0x158]  ; [pp+0x158] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x907238: ldur            x2, [x0, #0x1f]
    //     0x90723c: blr             x2
    // 0x907240: LeaveFrame
    //     0x907240: mov             SP, fp
    //     0x907244: ldp             fp, lr, [SP], #0x10
    // 0x907248: ret
    //     0x907248: ret             
    // 0x90724c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90724c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x907250: b               #0x90720c
    // 0x907254: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x907254: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0x9488e8, size: 0x130
    // 0x9488e8: EnterFrame
    //     0x9488e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9488ec: mov             fp, SP
    // 0x9488f0: AllocStack(0x18)
    //     0x9488f0: sub             SP, SP, #0x18
    // 0x9488f4: SetupParameters(_TrustMarkerWidgetState this /* r1 => r1, fp-0x8 */)
    //     0x9488f4: stur            x1, [fp, #-8]
    // 0x9488f8: CheckStackOverflow
    //     0x9488f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9488fc: cmp             SP, x16
    //     0x948900: b.ls            #0x948a0c
    // 0x948904: r1 = 1
    //     0x948904: movz            x1, #0x1
    // 0x948908: r0 = AllocateContext()
    //     0x948908: bl              #0x16f6108  ; AllocateContextStub
    // 0x94890c: mov             x1, x0
    // 0x948910: ldur            x0, [fp, #-8]
    // 0x948914: StoreField: r1->field_f = r0
    //     0x948914: stur            w0, [x1, #0xf]
    // 0x948918: r0 = LoadStaticField(0x878)
    //     0x948918: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x94891c: ldr             x0, [x0, #0x10f0]
    // 0x948920: cmp             w0, NULL
    // 0x948924: b.eq            #0x948a14
    // 0x948928: LoadField: r3 = r0->field_53
    //     0x948928: ldur            w3, [x0, #0x53]
    // 0x94892c: DecompressPointer r3
    //     0x94892c: add             x3, x3, HEAP, lsl #32
    // 0x948930: stur            x3, [fp, #-0x10]
    // 0x948934: LoadField: r0 = r3->field_7
    //     0x948934: ldur            w0, [x3, #7]
    // 0x948938: DecompressPointer r0
    //     0x948938: add             x0, x0, HEAP, lsl #32
    // 0x94893c: mov             x2, x1
    // 0x948940: stur            x0, [fp, #-8]
    // 0x948944: r1 = Function '<anonymous closure>':.
    //     0x948944: add             x1, PP, #0x54, lsl #12  ; [pp+0x542c0] AnonymousClosure: (0x9071e8), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::initState (0x9488e8)
    //     0x948948: ldr             x1, [x1, #0x2c0]
    // 0x94894c: r0 = AllocateClosure()
    //     0x94894c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x948950: ldur            x2, [fp, #-8]
    // 0x948954: mov             x3, x0
    // 0x948958: r1 = Null
    //     0x948958: mov             x1, NULL
    // 0x94895c: stur            x3, [fp, #-8]
    // 0x948960: cmp             w2, NULL
    // 0x948964: b.eq            #0x948984
    // 0x948968: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x948968: ldur            w4, [x2, #0x17]
    // 0x94896c: DecompressPointer r4
    //     0x94896c: add             x4, x4, HEAP, lsl #32
    // 0x948970: r8 = X0
    //     0x948970: ldr             x8, [PP, #0x348]  ; [pp+0x348] TypeParameter: X0
    // 0x948974: LoadField: r9 = r4->field_7
    //     0x948974: ldur            x9, [x4, #7]
    // 0x948978: r3 = Null
    //     0x948978: add             x3, PP, #0x54, lsl #12  ; [pp+0x542c8] Null
    //     0x94897c: ldr             x3, [x3, #0x2c8]
    // 0x948980: blr             x9
    // 0x948984: ldur            x0, [fp, #-0x10]
    // 0x948988: LoadField: r1 = r0->field_b
    //     0x948988: ldur            w1, [x0, #0xb]
    // 0x94898c: LoadField: r2 = r0->field_f
    //     0x94898c: ldur            w2, [x0, #0xf]
    // 0x948990: DecompressPointer r2
    //     0x948990: add             x2, x2, HEAP, lsl #32
    // 0x948994: LoadField: r3 = r2->field_b
    //     0x948994: ldur            w3, [x2, #0xb]
    // 0x948998: r2 = LoadInt32Instr(r1)
    //     0x948998: sbfx            x2, x1, #1, #0x1f
    // 0x94899c: stur            x2, [fp, #-0x18]
    // 0x9489a0: r1 = LoadInt32Instr(r3)
    //     0x9489a0: sbfx            x1, x3, #1, #0x1f
    // 0x9489a4: cmp             x2, x1
    // 0x9489a8: b.ne            #0x9489b4
    // 0x9489ac: mov             x1, x0
    // 0x9489b0: r0 = _growToNextCapacity()
    //     0x9489b0: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x9489b4: ldur            x2, [fp, #-0x10]
    // 0x9489b8: ldur            x3, [fp, #-0x18]
    // 0x9489bc: add             x4, x3, #1
    // 0x9489c0: lsl             x5, x4, #1
    // 0x9489c4: StoreField: r2->field_b = r5
    //     0x9489c4: stur            w5, [x2, #0xb]
    // 0x9489c8: LoadField: r1 = r2->field_f
    //     0x9489c8: ldur            w1, [x2, #0xf]
    // 0x9489cc: DecompressPointer r1
    //     0x9489cc: add             x1, x1, HEAP, lsl #32
    // 0x9489d0: ldur            x0, [fp, #-8]
    // 0x9489d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x9489d4: add             x25, x1, x3, lsl #2
    //     0x9489d8: add             x25, x25, #0xf
    //     0x9489dc: str             w0, [x25]
    //     0x9489e0: tbz             w0, #0, #0x9489fc
    //     0x9489e4: ldurb           w16, [x1, #-1]
    //     0x9489e8: ldurb           w17, [x0, #-1]
    //     0x9489ec: and             x16, x17, x16, lsr #2
    //     0x9489f0: tst             x16, HEAP, lsr #32
    //     0x9489f4: b.eq            #0x9489fc
    //     0x9489f8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x9489fc: r0 = Null
    //     0x9489fc: mov             x0, NULL
    // 0x948a00: LeaveFrame
    //     0x948a00: mov             SP, fp
    //     0x948a04: ldp             fp, lr, [SP], #0x10
    // 0x948a08: ret
    //     0x948a08: ret             
    // 0x948a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x948a0c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x948a10: b               #0x948904
    // 0x948a14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x948a14: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Padding <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x998918, size: 0x58
    // 0x998918: EnterFrame
    //     0x998918: stp             fp, lr, [SP, #-0x10]!
    //     0x99891c: mov             fp, SP
    // 0x998920: AllocStack(0x8)
    //     0x998920: sub             SP, SP, #8
    // 0x998924: CheckStackOverflow
    //     0x998924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x998928: cmp             SP, x16
    //     0x99892c: b.ls            #0x998968
    // 0x998930: r0 = Container()
    //     0x998930: bl              #0x7f8b3c  ; AllocateContainerStub -> Container (size=0x34)
    // 0x998934: mov             x1, x0
    // 0x998938: stur            x0, [fp, #-8]
    // 0x99893c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x99893c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x998940: r0 = Container()
    //     0x998940: bl              #0x7f8624  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x998944: r0 = Padding()
    //     0x998944: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x998948: r1 = Instance_EdgeInsets
    //     0x998948: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x99894c: ldr             x1, [x1, #0x1f0]
    // 0x998950: StoreField: r0->field_f = r1
    //     0x998950: stur            w1, [x0, #0xf]
    // 0x998954: ldur            x1, [fp, #-8]
    // 0x998958: StoreField: r0->field_b = r1
    //     0x998958: stur            w1, [x0, #0xb]
    // 0x99895c: LeaveFrame
    //     0x99895c: mov             SP, fp
    //     0x998960: ldp             fp, lr, [SP], #0x10
    // 0x998964: ret
    //     0x998964: ret             
    // 0x998968: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x998968: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x99896c: b               #0x998930
  }
  [closure] Center <anonymous closure>(dynamic, BuildContext, String, DownloadProgress) {
    // ** addr: 0xa2c264, size: 0x120
    // 0xa2c264: EnterFrame
    //     0xa2c264: stp             fp, lr, [SP, #-0x10]!
    //     0xa2c268: mov             fp, SP
    // 0xa2c26c: AllocStack(0x18)
    //     0xa2c26c: sub             SP, SP, #0x18
    // 0xa2c270: CheckStackOverflow
    //     0xa2c270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa2c274: cmp             SP, x16
    //     0xa2c278: b.ls            #0xa2c36c
    // 0xa2c27c: ldr             x0, [fp, #0x10]
    // 0xa2c280: LoadField: r1 = r0->field_b
    //     0xa2c280: ldur            w1, [x0, #0xb]
    // 0xa2c284: DecompressPointer r1
    //     0xa2c284: add             x1, x1, HEAP, lsl #32
    // 0xa2c288: cmp             w1, NULL
    // 0xa2c28c: b.eq            #0xa2c2a8
    // 0xa2c290: LoadField: r2 = r0->field_f
    //     0xa2c290: ldur            x2, [x0, #0xf]
    // 0xa2c294: r0 = LoadInt32Instr(r1)
    //     0xa2c294: sbfx            x0, x1, #1, #0x1f
    //     0xa2c298: tbz             w1, #0, #0xa2c2a0
    //     0xa2c29c: ldur            x0, [x1, #7]
    // 0xa2c2a0: cmp             x2, x0
    // 0xa2c2a4: b.le            #0xa2c2b0
    // 0xa2c2a8: r0 = Null
    //     0xa2c2a8: mov             x0, NULL
    // 0xa2c2ac: b               #0xa2c2e4
    // 0xa2c2b0: scvtf           d0, x2
    // 0xa2c2b4: scvtf           d1, x0
    // 0xa2c2b8: fdiv            d2, d0, d1
    // 0xa2c2bc: r0 = inline_Allocate_Double()
    //     0xa2c2bc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xa2c2c0: add             x0, x0, #0x10
    //     0xa2c2c4: cmp             x1, x0
    //     0xa2c2c8: b.ls            #0xa2c374
    //     0xa2c2cc: str             x0, [THR, #0x50]  ; THR::top
    //     0xa2c2d0: sub             x0, x0, #0xf
    //     0xa2c2d4: movz            x1, #0xe15c
    //     0xa2c2d8: movk            x1, #0x3, lsl #16
    //     0xa2c2dc: stur            x1, [x0, #-1]
    // 0xa2c2e0: StoreField: r0->field_7 = d2
    //     0xa2c2e0: stur            d2, [x0, #7]
    // 0xa2c2e4: ldr             x1, [fp, #0x20]
    // 0xa2c2e8: stur            x0, [fp, #-8]
    // 0xa2c2ec: r0 = of()
    //     0xa2c2ec: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa2c2f0: LoadField: r1 = r0->field_5b
    //     0xa2c2f0: ldur            w1, [x0, #0x5b]
    // 0xa2c2f4: DecompressPointer r1
    //     0xa2c2f4: add             x1, x1, HEAP, lsl #32
    // 0xa2c2f8: stur            x1, [fp, #-0x10]
    // 0xa2c2fc: r0 = CircularProgressIndicator()
    //     0xa2c2fc: bl              #0x8596fc  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x44)
    // 0xa2c300: mov             x1, x0
    // 0xa2c304: r0 = Instance__ActivityIndicatorType
    //     0xa2c304: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f1b0] Obj!_ActivityIndicatorType@d741c1
    //     0xa2c308: ldr             x0, [x0, #0x1b0]
    // 0xa2c30c: stur            x1, [fp, #-0x18]
    // 0xa2c310: StoreField: r1->field_23 = r0
    //     0xa2c310: stur            w0, [x1, #0x23]
    // 0xa2c314: ldur            x0, [fp, #-8]
    // 0xa2c318: StoreField: r1->field_b = r0
    //     0xa2c318: stur            w0, [x1, #0xb]
    // 0xa2c31c: ldur            x0, [fp, #-0x10]
    // 0xa2c320: StoreField: r1->field_13 = r0
    //     0xa2c320: stur            w0, [x1, #0x13]
    // 0xa2c324: r0 = SizedBox()
    //     0xa2c324: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa2c328: mov             x1, x0
    // 0xa2c32c: r0 = 48.000000
    //     0xa2c32c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0xa2c330: ldr             x0, [x0, #0xad8]
    // 0xa2c334: stur            x1, [fp, #-8]
    // 0xa2c338: StoreField: r1->field_f = r0
    //     0xa2c338: stur            w0, [x1, #0xf]
    // 0xa2c33c: StoreField: r1->field_13 = r0
    //     0xa2c33c: stur            w0, [x1, #0x13]
    // 0xa2c340: ldur            x0, [fp, #-0x18]
    // 0xa2c344: StoreField: r1->field_b = r0
    //     0xa2c344: stur            w0, [x1, #0xb]
    // 0xa2c348: r0 = Center()
    //     0xa2c348: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xa2c34c: r1 = Instance_Alignment
    //     0xa2c34c: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xa2c350: ldr             x1, [x1, #0xb10]
    // 0xa2c354: StoreField: r0->field_f = r1
    //     0xa2c354: stur            w1, [x0, #0xf]
    // 0xa2c358: ldur            x1, [fp, #-8]
    // 0xa2c35c: StoreField: r0->field_b = r1
    //     0xa2c35c: stur            w1, [x0, #0xb]
    // 0xa2c360: LeaveFrame
    //     0xa2c360: mov             SP, fp
    //     0xa2c364: ldp             fp, lr, [SP], #0x10
    // 0xa2c368: ret
    //     0xa2c368: ret             
    // 0xa2c36c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2c36c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa2c370: b               #0xa2c27c
    // 0xa2c374: SaveReg d2
    //     0xa2c374: str             q2, [SP, #-0x10]!
    // 0xa2c378: r0 = AllocateDouble()
    //     0xa2c378: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xa2c37c: RestoreReg d2
    //     0xa2c37c: ldr             q2, [SP], #0x10
    // 0xa2c380: b               #0xa2c2e0
  }
  _ trustMarkerLineTheme(/* No info */) {
    // ** addr: 0xa2c384, size: 0x2d0
    // 0xa2c384: EnterFrame
    //     0xa2c384: stp             fp, lr, [SP, #-0x10]!
    //     0xa2c388: mov             fp, SP
    // 0xa2c38c: AllocStack(0x60)
    //     0xa2c38c: sub             SP, SP, #0x60
    // 0xa2c390: SetupParameters(_TrustMarkerWidgetState this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xa2c390: mov             x5, x1
    //     0xa2c394: mov             x4, x2
    //     0xa2c398: stur            x1, [fp, #-0x10]
    //     0xa2c39c: stur            x2, [fp, #-0x18]
    //     0xa2c3a0: stur            x3, [fp, #-0x20]
    // 0xa2c3a4: CheckStackOverflow
    //     0xa2c3a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa2c3a8: cmp             SP, x16
    //     0xa2c3ac: b.ls            #0xa2c640
    // 0xa2c3b0: cmp             w4, NULL
    // 0xa2c3b4: b.ne            #0xa2c3c0
    // 0xa2c3b8: r0 = Null
    //     0xa2c3b8: mov             x0, NULL
    // 0xa2c3bc: b               #0xa2c3f4
    // 0xa2c3c0: LoadField: r0 = r4->field_b
    //     0xa2c3c0: ldur            w0, [x4, #0xb]
    // 0xa2c3c4: r1 = LoadInt32Instr(r0)
    //     0xa2c3c4: sbfx            x1, x0, #1, #0x1f
    // 0xa2c3c8: mov             x0, x1
    // 0xa2c3cc: mov             x1, x3
    // 0xa2c3d0: cmp             x1, x0
    // 0xa2c3d4: b.hs            #0xa2c648
    // 0xa2c3d8: LoadField: r0 = r4->field_f
    //     0xa2c3d8: ldur            w0, [x4, #0xf]
    // 0xa2c3dc: DecompressPointer r0
    //     0xa2c3dc: add             x0, x0, HEAP, lsl #32
    // 0xa2c3e0: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa2c3e0: add             x16, x0, x3, lsl #2
    //     0xa2c3e4: ldur            w1, [x16, #0xf]
    // 0xa2c3e8: DecompressPointer r1
    //     0xa2c3e8: add             x1, x1, HEAP, lsl #32
    // 0xa2c3ec: LoadField: r0 = r1->field_b
    //     0xa2c3ec: ldur            w0, [x1, #0xb]
    // 0xa2c3f0: DecompressPointer r0
    //     0xa2c3f0: add             x0, x0, HEAP, lsl #32
    // 0xa2c3f4: cmp             w0, NULL
    // 0xa2c3f8: b.ne            #0xa2c400
    // 0xa2c3fc: r0 = ""
    //     0xa2c3fc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0xa2c400: stur            x0, [fp, #-8]
    // 0xa2c404: r1 = Function '<anonymous closure>':.
    //     0xa2c404: add             x1, PP, #0x54, lsl #12  ; [pp+0x542a0] AnonymousClosure: (0xa2c264), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::trustMarkerLineTheme (0xa2c384)
    //     0xa2c408: ldr             x1, [x1, #0x2a0]
    // 0xa2c40c: r2 = Null
    //     0xa2c40c: mov             x2, NULL
    // 0xa2c410: r0 = AllocateClosure()
    //     0xa2c410: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa2c414: r1 = Function '<anonymous closure>':.
    //     0xa2c414: add             x1, PP, #0x54, lsl #12  ; [pp+0x542a8] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0xa2c418: ldr             x1, [x1, #0x2a8]
    // 0xa2c41c: r2 = Null
    //     0xa2c41c: mov             x2, NULL
    // 0xa2c420: stur            x0, [fp, #-0x28]
    // 0xa2c424: r0 = AllocateClosure()
    //     0xa2c424: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xa2c428: stur            x0, [fp, #-0x30]
    // 0xa2c42c: r0 = CachedNetworkImage()
    //     0xa2c42c: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0xa2c430: stur            x0, [fp, #-0x38]
    // 0xa2c434: r16 = 64.000000
    //     0xa2c434: add             x16, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa2c438: ldr             x16, [x16, #0x838]
    // 0xa2c43c: r30 = 64.000000
    //     0xa2c43c: add             lr, PP, #0x52, lsl #12  ; [pp+0x52838] 64
    //     0xa2c440: ldr             lr, [lr, #0x838]
    // 0xa2c444: stp             lr, x16, [SP, #0x18]
    // 0xa2c448: ldur            x16, [fp, #-0x28]
    // 0xa2c44c: ldur            lr, [fp, #-0x30]
    // 0xa2c450: stp             lr, x16, [SP, #8]
    // 0xa2c454: r16 = Instance_BoxFit
    //     0xa2c454: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cb18] Obj!BoxFit@d73841
    //     0xa2c458: ldr             x16, [x16, #0xb18]
    // 0xa2c45c: str             x16, [SP]
    // 0xa2c460: mov             x1, x0
    // 0xa2c464: ldur            x2, [fp, #-8]
    // 0xa2c468: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x5, fit, 0x6, height, 0x2, progressIndicatorBuilder, 0x4, width, 0x3, null]
    //     0xa2c468: add             x4, PP, #0x54, lsl #12  ; [pp+0x542b0] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x5, "fit", 0x6, "height", 0x2, "progressIndicatorBuilder", 0x4, "width", 0x3, Null]
    //     0xa2c46c: ldr             x4, [x4, #0x2b0]
    // 0xa2c470: r0 = CachedNetworkImage()
    //     0xa2c470: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0xa2c474: ldur            x2, [fp, #-0x18]
    // 0xa2c478: cmp             w2, NULL
    // 0xa2c47c: b.ne            #0xa2c488
    // 0xa2c480: r2 = Null
    //     0xa2c480: mov             x2, NULL
    // 0xa2c484: b               #0xa2c4c4
    // 0xa2c488: ldur            x3, [fp, #-0x20]
    // 0xa2c48c: LoadField: r0 = r2->field_b
    //     0xa2c48c: ldur            w0, [x2, #0xb]
    // 0xa2c490: r1 = LoadInt32Instr(r0)
    //     0xa2c490: sbfx            x1, x0, #1, #0x1f
    // 0xa2c494: mov             x0, x1
    // 0xa2c498: mov             x1, x3
    // 0xa2c49c: cmp             x1, x0
    // 0xa2c4a0: b.hs            #0xa2c64c
    // 0xa2c4a4: LoadField: r0 = r2->field_f
    //     0xa2c4a4: ldur            w0, [x2, #0xf]
    // 0xa2c4a8: DecompressPointer r0
    //     0xa2c4a8: add             x0, x0, HEAP, lsl #32
    // 0xa2c4ac: ArrayLoad: r1 = r0[r3]  ; Unknown_4
    //     0xa2c4ac: add             x16, x0, x3, lsl #2
    //     0xa2c4b0: ldur            w1, [x16, #0xf]
    // 0xa2c4b4: DecompressPointer r1
    //     0xa2c4b4: add             x1, x1, HEAP, lsl #32
    // 0xa2c4b8: LoadField: r0 = r1->field_7
    //     0xa2c4b8: ldur            w0, [x1, #7]
    // 0xa2c4bc: DecompressPointer r0
    //     0xa2c4bc: add             x0, x0, HEAP, lsl #32
    // 0xa2c4c0: mov             x2, x0
    // 0xa2c4c4: ldur            x1, [fp, #-0x10]
    // 0xa2c4c8: ldur            x0, [fp, #-0x38]
    // 0xa2c4cc: str             x2, [SP]
    // 0xa2c4d0: r0 = _interpolateSingle()
    //     0xa2c4d0: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0xa2c4d4: mov             x2, x0
    // 0xa2c4d8: ldur            x0, [fp, #-0x10]
    // 0xa2c4dc: stur            x2, [fp, #-8]
    // 0xa2c4e0: LoadField: r1 = r0->field_f
    //     0xa2c4e0: ldur            w1, [x0, #0xf]
    // 0xa2c4e4: DecompressPointer r1
    //     0xa2c4e4: add             x1, x1, HEAP, lsl #32
    // 0xa2c4e8: cmp             w1, NULL
    // 0xa2c4ec: b.eq            #0xa2c650
    // 0xa2c4f0: r0 = of()
    //     0xa2c4f0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0xa2c4f4: LoadField: r1 = r0->field_87
    //     0xa2c4f4: ldur            w1, [x0, #0x87]
    // 0xa2c4f8: DecompressPointer r1
    //     0xa2c4f8: add             x1, x1, HEAP, lsl #32
    // 0xa2c4fc: LoadField: r0 = r1->field_2b
    //     0xa2c4fc: ldur            w0, [x1, #0x2b]
    // 0xa2c500: DecompressPointer r0
    //     0xa2c500: add             x0, x0, HEAP, lsl #32
    // 0xa2c504: r16 = 12.000000
    //     0xa2c504: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0xa2c508: ldr             x16, [x16, #0x9e8]
    // 0xa2c50c: r30 = Instance_Color
    //     0xa2c50c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0xa2c510: stp             lr, x16, [SP]
    // 0xa2c514: mov             x1, x0
    // 0xa2c518: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0xa2c518: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0xa2c51c: ldr             x4, [x4, #0xaa0]
    // 0xa2c520: r0 = copyWith()
    //     0xa2c520: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xa2c524: stur            x0, [fp, #-0x10]
    // 0xa2c528: r0 = Text()
    //     0xa2c528: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0xa2c52c: mov             x1, x0
    // 0xa2c530: ldur            x0, [fp, #-8]
    // 0xa2c534: stur            x1, [fp, #-0x18]
    // 0xa2c538: StoreField: r1->field_b = r0
    //     0xa2c538: stur            w0, [x1, #0xb]
    // 0xa2c53c: ldur            x0, [fp, #-0x10]
    // 0xa2c540: StoreField: r1->field_13 = r0
    //     0xa2c540: stur            w0, [x1, #0x13]
    // 0xa2c544: r0 = Instance_TextAlign
    //     0xa2c544: ldr             x0, [PP, #0x47b8]  ; [pp+0x47b8] Obj!TextAlign@d766c1
    // 0xa2c548: StoreField: r1->field_1b = r0
    //     0xa2c548: stur            w0, [x1, #0x1b]
    // 0xa2c54c: r0 = 6
    //     0xa2c54c: movz            x0, #0x6
    // 0xa2c550: StoreField: r1->field_37 = r0
    //     0xa2c550: stur            w0, [x1, #0x37]
    // 0xa2c554: r0 = SizedBox()
    //     0xa2c554: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa2c558: mov             x3, x0
    // 0xa2c55c: r0 = 70.000000
    //     0xa2c55c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33748] 70
    //     0xa2c560: ldr             x0, [x0, #0x748]
    // 0xa2c564: stur            x3, [fp, #-8]
    // 0xa2c568: StoreField: r3->field_f = r0
    //     0xa2c568: stur            w0, [x3, #0xf]
    // 0xa2c56c: ldur            x0, [fp, #-0x18]
    // 0xa2c570: StoreField: r3->field_b = r0
    //     0xa2c570: stur            w0, [x3, #0xb]
    // 0xa2c574: r1 = Null
    //     0xa2c574: mov             x1, NULL
    // 0xa2c578: r2 = 4
    //     0xa2c578: movz            x2, #0x4
    // 0xa2c57c: r0 = AllocateArray()
    //     0xa2c57c: bl              #0x16f7198  ; AllocateArrayStub
    // 0xa2c580: mov             x2, x0
    // 0xa2c584: ldur            x0, [fp, #-0x38]
    // 0xa2c588: stur            x2, [fp, #-0x10]
    // 0xa2c58c: StoreField: r2->field_f = r0
    //     0xa2c58c: stur            w0, [x2, #0xf]
    // 0xa2c590: ldur            x0, [fp, #-8]
    // 0xa2c594: StoreField: r2->field_13 = r0
    //     0xa2c594: stur            w0, [x2, #0x13]
    // 0xa2c598: r1 = <Widget>
    //     0xa2c598: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0xa2c59c: r0 = AllocateGrowableArray()
    //     0xa2c59c: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0xa2c5a0: mov             x1, x0
    // 0xa2c5a4: ldur            x0, [fp, #-0x10]
    // 0xa2c5a8: stur            x1, [fp, #-8]
    // 0xa2c5ac: StoreField: r1->field_f = r0
    //     0xa2c5ac: stur            w0, [x1, #0xf]
    // 0xa2c5b0: r0 = 4
    //     0xa2c5b0: movz            x0, #0x4
    // 0xa2c5b4: StoreField: r1->field_b = r0
    //     0xa2c5b4: stur            w0, [x1, #0xb]
    // 0xa2c5b8: r0 = Column()
    //     0xa2c5b8: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0xa2c5bc: mov             x1, x0
    // 0xa2c5c0: r0 = Instance_Axis
    //     0xa2c5c0: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0xa2c5c4: stur            x1, [fp, #-0x10]
    // 0xa2c5c8: StoreField: r1->field_f = r0
    //     0xa2c5c8: stur            w0, [x1, #0xf]
    // 0xa2c5cc: r0 = Instance_MainAxisAlignment
    //     0xa2c5cc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0xa2c5d0: ldr             x0, [x0, #0xa08]
    // 0xa2c5d4: StoreField: r1->field_13 = r0
    //     0xa2c5d4: stur            w0, [x1, #0x13]
    // 0xa2c5d8: r0 = Instance_MainAxisSize
    //     0xa2c5d8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0xa2c5dc: ldr             x0, [x0, #0xa10]
    // 0xa2c5e0: ArrayStore: r1[0] = r0  ; List_4
    //     0xa2c5e0: stur            w0, [x1, #0x17]
    // 0xa2c5e4: r0 = Instance_CrossAxisAlignment
    //     0xa2c5e4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0xa2c5e8: ldr             x0, [x0, #0xa18]
    // 0xa2c5ec: StoreField: r1->field_1b = r0
    //     0xa2c5ec: stur            w0, [x1, #0x1b]
    // 0xa2c5f0: r0 = Instance_VerticalDirection
    //     0xa2c5f0: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0xa2c5f4: ldr             x0, [x0, #0xa20]
    // 0xa2c5f8: StoreField: r1->field_23 = r0
    //     0xa2c5f8: stur            w0, [x1, #0x23]
    // 0xa2c5fc: r0 = Instance_Clip
    //     0xa2c5fc: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0xa2c600: ldr             x0, [x0, #0x38]
    // 0xa2c604: StoreField: r1->field_2b = r0
    //     0xa2c604: stur            w0, [x1, #0x2b]
    // 0xa2c608: StoreField: r1->field_2f = rZR
    //     0xa2c608: stur            xzr, [x1, #0x2f]
    // 0xa2c60c: ldur            x0, [fp, #-8]
    // 0xa2c610: StoreField: r1->field_b = r0
    //     0xa2c610: stur            w0, [x1, #0xb]
    // 0xa2c614: r0 = SizedBox()
    //     0xa2c614: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xa2c618: r1 = 100.000000
    //     0xa2c618: ldr             x1, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0xa2c61c: StoreField: r0->field_f = r1
    //     0xa2c61c: stur            w1, [x0, #0xf]
    // 0xa2c620: r1 = 98.000000
    //     0xa2c620: add             x1, PP, #0x54, lsl #12  ; [pp+0x542b8] 98
    //     0xa2c624: ldr             x1, [x1, #0x2b8]
    // 0xa2c628: StoreField: r0->field_13 = r1
    //     0xa2c628: stur            w1, [x0, #0x13]
    // 0xa2c62c: ldur            x1, [fp, #-0x10]
    // 0xa2c630: StoreField: r0->field_b = r1
    //     0xa2c630: stur            w1, [x0, #0xb]
    // 0xa2c634: LeaveFrame
    //     0xa2c634: mov             SP, fp
    //     0xa2c638: ldp             fp, lr, [SP], #0x10
    // 0xa2c63c: ret
    //     0xa2c63c: ret             
    // 0xa2c640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2c640: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa2c644: b               #0xa2c3b0
    // 0xa2c648: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa2c648: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa2c64c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa2c64c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa2c650: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa2c650: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xbd43d4, size: 0x210
    // 0xbd43d4: EnterFrame
    //     0xbd43d4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd43d8: mov             fp, SP
    // 0xbd43dc: AllocStack(0x48)
    //     0xbd43dc: sub             SP, SP, #0x48
    // 0xbd43e0: SetupParameters(_TrustMarkerWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xbd43e0: stur            x1, [fp, #-8]
    // 0xbd43e4: CheckStackOverflow
    //     0xbd43e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd43e8: cmp             SP, x16
    //     0xbd43ec: b.ls            #0xbd45a4
    // 0xbd43f0: r1 = 1
    //     0xbd43f0: movz            x1, #0x1
    // 0xbd43f4: r0 = AllocateContext()
    //     0xbd43f4: bl              #0x16f6108  ; AllocateContextStub
    // 0xbd43f8: mov             x1, x0
    // 0xbd43fc: ldur            x0, [fp, #-8]
    // 0xbd4400: stur            x1, [fp, #-0x10]
    // 0xbd4404: StoreField: r1->field_f = r0
    //     0xbd4404: stur            w0, [x1, #0xf]
    // 0xbd4408: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0xbd4408: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbd440c: ldr             x0, [x0, #0x1c80]
    //     0xbd4410: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbd4414: cmp             w0, w16
    //     0xbd4418: b.ne            #0xbd4424
    //     0xbd441c: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0xbd4420: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0xbd4424: r0 = GetNavigation.size()
    //     0xbd4424: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbd4428: LoadField: d0 = r0->field_f
    //     0xbd4428: ldur            d0, [x0, #0xf]
    // 0xbd442c: d1 = 0.160000
    //     0xbd442c: add             x17, PP, #0x54, lsl #12  ; [pp+0x54288] IMM: double(0.16) from 0x3fc47ae147ae147b
    //     0xbd4430: ldr             d1, [x17, #0x288]
    // 0xbd4434: fmul            d2, d0, d1
    // 0xbd4438: stur            d2, [fp, #-0x28]
    // 0xbd443c: r0 = GetNavigation.size()
    //     0xbd443c: bl              #0x8b1078  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.size
    // 0xbd4440: LoadField: d0 = r0->field_7
    //     0xbd4440: ldur            d0, [x0, #7]
    // 0xbd4444: ldur            x0, [fp, #-8]
    // 0xbd4448: stur            d0, [fp, #-0x30]
    // 0xbd444c: LoadField: r1 = r0->field_b
    //     0xbd444c: ldur            w1, [x0, #0xb]
    // 0xbd4450: DecompressPointer r1
    //     0xbd4450: add             x1, x1, HEAP, lsl #32
    // 0xbd4454: cmp             w1, NULL
    // 0xbd4458: b.eq            #0xbd45ac
    // 0xbd445c: LoadField: r0 = r1->field_b
    //     0xbd445c: ldur            w0, [x1, #0xb]
    // 0xbd4460: DecompressPointer r0
    //     0xbd4460: add             x0, x0, HEAP, lsl #32
    // 0xbd4464: LoadField: r1 = r0->field_6b
    //     0xbd4464: ldur            w1, [x0, #0x6b]
    // 0xbd4468: DecompressPointer r1
    //     0xbd4468: add             x1, x1, HEAP, lsl #32
    // 0xbd446c: cmp             w1, NULL
    // 0xbd4470: b.ne            #0xbd447c
    // 0xbd4474: r0 = Null
    //     0xbd4474: mov             x0, NULL
    // 0xbd4478: b               #0xbd4480
    // 0xbd447c: LoadField: r0 = r1->field_b
    //     0xbd447c: ldur            w0, [x1, #0xb]
    // 0xbd4480: cmp             w0, NULL
    // 0xbd4484: b.ne            #0xbd4490
    // 0xbd4488: r3 = 0
    //     0xbd4488: movz            x3, #0
    // 0xbd448c: b               #0xbd4498
    // 0xbd4490: r1 = LoadInt32Instr(r0)
    //     0xbd4490: sbfx            x1, x0, #1, #0x1f
    // 0xbd4494: mov             x3, x1
    // 0xbd4498: ldur            d1, [fp, #-0x28]
    // 0xbd449c: ldur            x2, [fp, #-0x10]
    // 0xbd44a0: stur            x3, [fp, #-0x18]
    // 0xbd44a4: r1 = Function '<anonymous closure>':.
    //     0xbd44a4: add             x1, PP, #0x54, lsl #12  ; [pp+0x54290] AnonymousClosure: (0xbd45e4), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::build (0xbd43d4)
    //     0xbd44a8: ldr             x1, [x1, #0x290]
    // 0xbd44ac: r0 = AllocateClosure()
    //     0xbd44ac: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd44b0: r1 = Function '<anonymous closure>':.
    //     0xbd44b0: add             x1, PP, #0x54, lsl #12  ; [pp+0x54298] AnonymousClosure: (0x998918), in [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::build (0xbd43d4)
    //     0xbd44b4: ldr             x1, [x1, #0x298]
    // 0xbd44b8: r2 = Null
    //     0xbd44b8: mov             x2, NULL
    // 0xbd44bc: stur            x0, [fp, #-8]
    // 0xbd44c0: r0 = AllocateClosure()
    //     0xbd44c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0xbd44c4: stur            x0, [fp, #-0x10]
    // 0xbd44c8: r0 = ListView()
    //     0xbd44c8: bl              #0x8a3d5c  ; AllocateListViewStub -> ListView (size=0x68)
    // 0xbd44cc: stur            x0, [fp, #-0x20]
    // 0xbd44d0: r16 = Instance_NeverScrollableScrollPhysics
    //     0xbd44d0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1c8] Obj!NeverScrollableScrollPhysics@d558b1
    //     0xbd44d4: ldr             x16, [x16, #0x1c8]
    // 0xbd44d8: r30 = true
    //     0xbd44d8: add             lr, NULL, #0x20  ; true
    // 0xbd44dc: stp             lr, x16, [SP, #8]
    // 0xbd44e0: r16 = Instance_Axis
    //     0xbd44e0: ldr             x16, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0xbd44e4: str             x16, [SP]
    // 0xbd44e8: mov             x1, x0
    // 0xbd44ec: ldur            x2, [fp, #-8]
    // 0xbd44f0: ldur            x3, [fp, #-0x18]
    // 0xbd44f4: ldur            x5, [fp, #-0x10]
    // 0xbd44f8: r4 = const [0, 0x7, 0x3, 0x4, physics, 0x4, scrollDirection, 0x6, shrinkWrap, 0x5, null]
    //     0xbd44f8: add             x4, PP, #0x33, lsl #12  ; [pp+0x33898] List(11) [0, 0x7, 0x3, 0x4, "physics", 0x4, "scrollDirection", 0x6, "shrinkWrap", 0x5, Null]
    //     0xbd44fc: ldr             x4, [x4, #0x898]
    // 0xbd4500: r0 = ListView.separated()
    //     0xbd4500: bl              #0x8a3860  ; [package:flutter/src/widgets/scroll_view.dart] ListView::ListView.separated
    // 0xbd4504: r0 = Center()
    //     0xbd4504: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xbd4508: mov             x1, x0
    // 0xbd450c: r0 = Instance_Alignment
    //     0xbd450c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0xbd4510: ldr             x0, [x0, #0xb10]
    // 0xbd4514: stur            x1, [fp, #-0x10]
    // 0xbd4518: StoreField: r1->field_f = r0
    //     0xbd4518: stur            w0, [x1, #0xf]
    // 0xbd451c: ldur            x0, [fp, #-0x20]
    // 0xbd4520: StoreField: r1->field_b = r0
    //     0xbd4520: stur            w0, [x1, #0xb]
    // 0xbd4524: ldur            d0, [fp, #-0x30]
    // 0xbd4528: r0 = inline_Allocate_Double()
    //     0xbd4528: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xbd452c: add             x0, x0, #0x10
    //     0xbd4530: cmp             x2, x0
    //     0xbd4534: b.ls            #0xbd45b0
    //     0xbd4538: str             x0, [THR, #0x50]  ; THR::top
    //     0xbd453c: sub             x0, x0, #0xf
    //     0xbd4540: movz            x2, #0xe15c
    //     0xbd4544: movk            x2, #0x3, lsl #16
    //     0xbd4548: stur            x2, [x0, #-1]
    // 0xbd454c: StoreField: r0->field_7 = d0
    //     0xbd454c: stur            d0, [x0, #7]
    // 0xbd4550: stur            x0, [fp, #-8]
    // 0xbd4554: r0 = SizedBox()
    //     0xbd4554: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xbd4558: ldur            x1, [fp, #-8]
    // 0xbd455c: StoreField: r0->field_f = r1
    //     0xbd455c: stur            w1, [x0, #0xf]
    // 0xbd4560: ldur            d0, [fp, #-0x28]
    // 0xbd4564: r1 = inline_Allocate_Double()
    //     0xbd4564: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xbd4568: add             x1, x1, #0x10
    //     0xbd456c: cmp             x2, x1
    //     0xbd4570: b.ls            #0xbd45c8
    //     0xbd4574: str             x1, [THR, #0x50]  ; THR::top
    //     0xbd4578: sub             x1, x1, #0xf
    //     0xbd457c: movz            x2, #0xe15c
    //     0xbd4580: movk            x2, #0x3, lsl #16
    //     0xbd4584: stur            x2, [x1, #-1]
    // 0xbd4588: StoreField: r1->field_7 = d0
    //     0xbd4588: stur            d0, [x1, #7]
    // 0xbd458c: StoreField: r0->field_13 = r1
    //     0xbd458c: stur            w1, [x0, #0x13]
    // 0xbd4590: ldur            x1, [fp, #-0x10]
    // 0xbd4594: StoreField: r0->field_b = r1
    //     0xbd4594: stur            w1, [x0, #0xb]
    // 0xbd4598: LeaveFrame
    //     0xbd4598: mov             SP, fp
    //     0xbd459c: ldp             fp, lr, [SP], #0x10
    // 0xbd45a0: ret
    //     0xbd45a0: ret             
    // 0xbd45a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd45a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd45a8: b               #0xbd43f0
    // 0xbd45ac: r0 = NullCastErrorSharedWithFPURegs()
    //     0xbd45ac: bl              #0x16f7974  ; NullCastErrorSharedWithFPURegsStub
    // 0xbd45b0: SaveReg d0
    //     0xbd45b0: str             q0, [SP, #-0x10]!
    // 0xbd45b4: SaveReg r1
    //     0xbd45b4: str             x1, [SP, #-8]!
    // 0xbd45b8: r0 = AllocateDouble()
    //     0xbd45b8: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbd45bc: RestoreReg r1
    //     0xbd45bc: ldr             x1, [SP], #8
    // 0xbd45c0: RestoreReg d0
    //     0xbd45c0: ldr             q0, [SP], #0x10
    // 0xbd45c4: b               #0xbd454c
    // 0xbd45c8: SaveReg d0
    //     0xbd45c8: str             q0, [SP, #-0x10]!
    // 0xbd45cc: SaveReg r0
    //     0xbd45cc: str             x0, [SP, #-8]!
    // 0xbd45d0: r0 = AllocateDouble()
    //     0xbd45d0: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0xbd45d4: mov             x1, x0
    // 0xbd45d8: RestoreReg r0
    //     0xbd45d8: ldr             x0, [SP], #8
    // 0xbd45dc: RestoreReg d0
    //     0xbd45dc: ldr             q0, [SP], #0x10
    // 0xbd45e0: b               #0xbd4588
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0xbd45e4, size: 0x7c
    // 0xbd45e4: EnterFrame
    //     0xbd45e4: stp             fp, lr, [SP, #-0x10]!
    //     0xbd45e8: mov             fp, SP
    // 0xbd45ec: ldr             x0, [fp, #0x20]
    // 0xbd45f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbd45f0: ldur            w1, [x0, #0x17]
    // 0xbd45f4: DecompressPointer r1
    //     0xbd45f4: add             x1, x1, HEAP, lsl #32
    // 0xbd45f8: CheckStackOverflow
    //     0xbd45f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbd45fc: cmp             SP, x16
    //     0xbd4600: b.ls            #0xbd4654
    // 0xbd4604: LoadField: r0 = r1->field_f
    //     0xbd4604: ldur            w0, [x1, #0xf]
    // 0xbd4608: DecompressPointer r0
    //     0xbd4608: add             x0, x0, HEAP, lsl #32
    // 0xbd460c: LoadField: r1 = r0->field_b
    //     0xbd460c: ldur            w1, [x0, #0xb]
    // 0xbd4610: DecompressPointer r1
    //     0xbd4610: add             x1, x1, HEAP, lsl #32
    // 0xbd4614: cmp             w1, NULL
    // 0xbd4618: b.eq            #0xbd465c
    // 0xbd461c: LoadField: r2 = r1->field_b
    //     0xbd461c: ldur            w2, [x1, #0xb]
    // 0xbd4620: DecompressPointer r2
    //     0xbd4620: add             x2, x2, HEAP, lsl #32
    // 0xbd4624: LoadField: r1 = r2->field_6b
    //     0xbd4624: ldur            w1, [x2, #0x6b]
    // 0xbd4628: DecompressPointer r1
    //     0xbd4628: add             x1, x1, HEAP, lsl #32
    // 0xbd462c: ldr             x2, [fp, #0x10]
    // 0xbd4630: r3 = LoadInt32Instr(r2)
    //     0xbd4630: sbfx            x3, x2, #1, #0x1f
    //     0xbd4634: tbz             w2, #0, #0xbd463c
    //     0xbd4638: ldur            x3, [x2, #7]
    // 0xbd463c: mov             x2, x1
    // 0xbd4640: mov             x1, x0
    // 0xbd4644: r0 = trustMarkerLineTheme()
    //     0xbd4644: bl              #0xa2c384  ; [package:customer_app/app/presentation/views/line/checkout_variants/widgets/trustmarker_widget.dart] _TrustMarkerWidgetState::trustMarkerLineTheme
    // 0xbd4648: LeaveFrame
    //     0xbd4648: mov             SP, fp
    //     0xbd464c: ldp             fp, lr, [SP], #0x10
    // 0xbd4650: ret
    //     0xbd4650: ret             
    // 0xbd4654: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbd4654: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbd4658: b               #0xbd4604
    // 0xbd465c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbd465c: bl              #0x16f7928  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4007, size: 0x14, field offset: 0xc
//   const constructor, 
class TrustMarkerWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc80710, size: 0x24
    // 0xc80710: EnterFrame
    //     0xc80710: stp             fp, lr, [SP, #-0x10]!
    //     0xc80714: mov             fp, SP
    // 0xc80718: mov             x0, x1
    // 0xc8071c: r1 = <TrustMarkerWidget>
    //     0xc8071c: add             x1, PP, #0x48, lsl #12  ; [pp+0x48670] TypeArguments: <TrustMarkerWidget>
    //     0xc80720: ldr             x1, [x1, #0x670]
    // 0xc80724: r0 = _TrustMarkerWidgetState()
    //     0xc80724: bl              #0xc80734  ; Allocate_TrustMarkerWidgetStateStub -> _TrustMarkerWidgetState (size=0x14)
    // 0xc80728: LeaveFrame
    //     0xc80728: mov             SP, fp
    //     0xc8072c: ldp             fp, lr, [SP], #0x10
    // 0xc80730: ret
    //     0xc80730: ret             
  }
}
