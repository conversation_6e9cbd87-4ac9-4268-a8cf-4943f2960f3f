// lib: , url: package:dio/src/multipart_file.dart

// class id: 1049608, size: 0x8
class :: {
}

// class id: 4981, size: 0x24, field offset: 0x8
class MultipartFile extends Object {

  [closure] Uint8List <anonymous closure>(dynamic, List<int>) {
    // ** addr: 0x884010, size: 0x4c
    // 0x884010: EnterFrame
    //     0x884010: stp             fp, lr, [SP, #-0x10]!
    //     0x884014: mov             fp, SP
    // 0x884018: CheckStackOverflow
    //     0x884018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x88401c: cmp             SP, x16
    //     0x884020: b.ls            #0x884054
    // 0x884024: ldr             x0, [fp, #0x10]
    // 0x884028: r1 = LoadClassIdInstr(r0)
    //     0x884028: ldur            x1, [x0, #-1]
    //     0x88402c: ubfx            x1, x1, #0xc, #0x14
    // 0x884030: sub             x16, x1, #0x74
    // 0x884034: cmp             x16, #3
    // 0x884038: b.ls            #0x884048
    // 0x88403c: mov             x2, x0
    // 0x884040: r1 = Null
    //     0x884040: mov             x1, NULL
    // 0x884044: r0 = Uint8List.fromList()
    //     0x884044: bl              #0x6568d8  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0x884048: LeaveFrame
    //     0x884048: mov             SP, fp
    //     0x88404c: ldp             fp, lr, [SP], #0x10
    // 0x884050: ret
    //     0x884050: ret             
    // 0x884054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x884054: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x884058: b               #0x884024
  }
  _ MultipartFile.fromStream(/* No info */) {
    // ** addr: 0x8ab398, size: 0x114
    // 0x8ab398: EnterFrame
    //     0x8ab398: stp             fp, lr, [SP, #-0x10]!
    //     0x8ab39c: mov             fp, SP
    // 0x8ab3a0: AllocStack(0x28)
    //     0x8ab3a0: sub             SP, SP, #0x28
    // 0x8ab3a4: r0 = false
    //     0x8ab3a4: add             x0, NULL, #0x30  ; false
    // 0x8ab3a8: mov             x4, x1
    // 0x8ab3ac: stur            x1, [fp, #-8]
    // 0x8ab3b0: mov             x1, x6
    // 0x8ab3b4: stur            x5, [fp, #-0x10]
    // 0x8ab3b8: CheckStackOverflow
    //     0x8ab3b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ab3bc: cmp             SP, x16
    //     0x8ab3c0: b.ls            #0x8ab4a4
    // 0x8ab3c4: StoreField: r4->field_1f = r0
    //     0x8ab3c4: stur            w0, [x4, #0x1f]
    // 0x8ab3c8: StoreField: r4->field_7 = r3
    //     0x8ab3c8: stur            x3, [x4, #7]
    // 0x8ab3cc: mov             x0, x1
    // 0x8ab3d0: StoreField: r4->field_f = r0
    //     0x8ab3d0: stur            w0, [x4, #0xf]
    //     0x8ab3d4: ldurb           w16, [x4, #-1]
    //     0x8ab3d8: ldurb           w17, [x0, #-1]
    //     0x8ab3dc: and             x16, x17, x16, lsr #2
    //     0x8ab3e0: tst             x16, HEAP, lsr #32
    //     0x8ab3e4: b.eq            #0x8ab3ec
    //     0x8ab3e8: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x8ab3ec: mov             x0, x2
    // 0x8ab3f0: StoreField: r4->field_1b = r0
    //     0x8ab3f0: stur            w0, [x4, #0x1b]
    //     0x8ab3f4: ldurb           w16, [x4, #-1]
    //     0x8ab3f8: ldurb           w17, [x0, #-1]
    //     0x8ab3fc: and             x16, x17, x16, lsr #2
    //     0x8ab400: tst             x16, HEAP, lsr #32
    //     0x8ab404: b.eq            #0x8ab40c
    //     0x8ab408: bl              #0x16f58e8  ; WriteBarrierWrappersStub
    // 0x8ab40c: r16 = <List<String>>
    //     0x8ab40c: add             x16, PP, #8, lsl #12  ; [pp+0x8b30] TypeArguments: <List<String>>
    //     0x8ab410: ldr             x16, [x16, #0xb30]
    // 0x8ab414: stp             NULL, x16, [SP]
    // 0x8ab418: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x8ab418: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x8ab41c: r0 = caseInsensitiveKeyMap()
    //     0x8ab41c: bl              #0x8626d4  ; [package:dio/src/utils.dart] ::caseInsensitiveKeyMap
    // 0x8ab420: ldur            x1, [fp, #-8]
    // 0x8ab424: StoreField: r1->field_13 = r0
    //     0x8ab424: stur            w0, [x1, #0x13]
    //     0x8ab428: ldurb           w16, [x1, #-1]
    //     0x8ab42c: ldurb           w17, [x0, #-1]
    //     0x8ab430: and             x16, x17, x16, lsr #2
    //     0x8ab434: tst             x16, HEAP, lsr #32
    //     0x8ab438: b.eq            #0x8ab440
    //     0x8ab43c: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8ab440: ldur            x0, [fp, #-0x10]
    // 0x8ab444: cmp             w0, NULL
    // 0x8ab448: b.ne            #0x8ab474
    // 0x8ab44c: r0 = MediaType()
    //     0x8ab44c: bl              #0x866e38  ; AllocateMediaTypeStub -> MediaType (size=0x14)
    // 0x8ab450: mov             x1, x0
    // 0x8ab454: r2 = "application"
    //     0x8ab454: add             x2, PP, #0x33, lsl #12  ; [pp+0x331f0] "application"
    //     0x8ab458: ldr             x2, [x2, #0x1f0]
    // 0x8ab45c: r3 = "octet-stream"
    //     0x8ab45c: add             x3, PP, #0x33, lsl #12  ; [pp+0x331f8] "octet-stream"
    //     0x8ab460: ldr             x3, [x3, #0x1f8]
    // 0x8ab464: stur            x0, [fp, #-0x18]
    // 0x8ab468: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8ab468: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8ab46c: r0 = MediaType()
    //     0x8ab46c: bl              #0x866bb4  ; [package:http_parser/src/media_type.dart] MediaType::MediaType
    // 0x8ab470: ldur            x0, [fp, #-0x18]
    // 0x8ab474: ldur            x1, [fp, #-8]
    // 0x8ab478: ArrayStore: r1[0] = r0  ; List_4
    //     0x8ab478: stur            w0, [x1, #0x17]
    //     0x8ab47c: ldurb           w16, [x1, #-1]
    //     0x8ab480: ldurb           w17, [x0, #-1]
    //     0x8ab484: and             x16, x17, x16, lsr #2
    //     0x8ab488: tst             x16, HEAP, lsr #32
    //     0x8ab48c: b.eq            #0x8ab494
    //     0x8ab490: bl              #0x16f5888  ; WriteBarrierWrappersStub
    // 0x8ab494: r0 = Null
    //     0x8ab494: mov             x0, NULL
    // 0x8ab498: LeaveFrame
    //     0x8ab498: mov             SP, fp
    //     0x8ab49c: ldp             fp, lr, [SP], #0x10
    // 0x8ab4a0: ret
    //     0x8ab4a0: ret             
    // 0x8ab4a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ab4a4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ab4a8: b               #0x8ab3c4
  }
  factory _ MultipartFile.fromString(/* No info */) {
    // ** addr: 0x12c9908, size: 0x184
    // 0x12c9908: EnterFrame
    //     0x12c9908: stp             fp, lr, [SP, #-0x10]!
    //     0x12c990c: mov             fp, SP
    // 0x12c9910: AllocStack(0x30)
    //     0x12c9910: sub             SP, SP, #0x30
    // 0x12c9914: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x12c9914: stur            x2, [fp, #-8]
    // 0x12c9918: CheckStackOverflow
    //     0x12c9918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12c991c: cmp             SP, x16
    //     0x12c9920: b.ls            #0x12c9a84
    // 0x12c9924: r0 = MediaType()
    //     0x12c9924: bl              #0x866e38  ; AllocateMediaTypeStub -> MediaType (size=0x14)
    // 0x12c9928: mov             x1, x0
    // 0x12c992c: r2 = "text"
    //     0x12c992c: ldr             x2, [PP, #0x6e20]  ; [pp+0x6e20] "text"
    // 0x12c9930: r3 = "plain"
    //     0x12c9930: add             x3, PP, #0x38, lsl #12  ; [pp+0x38b80] "plain"
    //     0x12c9934: ldr             x3, [x3, #0xb80]
    // 0x12c9938: stur            x0, [fp, #-0x10]
    // 0x12c993c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x12c993c: ldr             x4, [PP, #0x900]  ; [pp+0x900] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x12c9940: r0 = MediaType()
    //     0x12c9940: bl              #0x866bb4  ; [package:http_parser/src/media_type.dart] MediaType::MediaType
    // 0x12c9944: ldur            x3, [fp, #-0x10]
    // 0x12c9948: LoadField: r0 = r3->field_f
    //     0x12c9948: ldur            w0, [x3, #0xf]
    // 0x12c994c: DecompressPointer r0
    //     0x12c994c: add             x0, x0, HEAP, lsl #32
    // 0x12c9950: LoadField: r1 = r0->field_b
    //     0x12c9950: ldur            w1, [x0, #0xb]
    // 0x12c9954: DecompressPointer r1
    //     0x12c9954: add             x1, x1, HEAP, lsl #32
    // 0x12c9958: r0 = LoadClassIdInstr(r1)
    //     0x12c9958: ldur            x0, [x1, #-1]
    //     0x12c995c: ubfx            x0, x0, #0xc, #0x14
    // 0x12c9960: r2 = "charset"
    //     0x12c9960: add             x2, PP, #9, lsl #12  ; [pp+0x9440] "charset"
    //     0x12c9964: ldr             x2, [x2, #0x440]
    // 0x12c9968: r0 = GDT[cid_x0 + -0xfe]()
    //     0x12c9968: sub             lr, x0, #0xfe
    //     0x12c996c: ldr             lr, [x21, lr, lsl #3]
    //     0x12c9970: blr             lr
    // 0x12c9974: mov             x1, x0
    // 0x12c9978: r0 = encodingForCharset()
    //     0x12c9978: bl              #0x12c9c90  ; [package:dio/src/utils.dart] ::encodingForCharset
    // 0x12c997c: r1 = Null
    //     0x12c997c: mov             x1, NULL
    // 0x12c9980: r2 = 4
    //     0x12c9980: movz            x2, #0x4
    // 0x12c9984: stur            x0, [fp, #-0x18]
    // 0x12c9988: r0 = AllocateArray()
    //     0x12c9988: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12c998c: r16 = "charset"
    //     0x12c998c: add             x16, PP, #9, lsl #12  ; [pp+0x9440] "charset"
    //     0x12c9990: ldr             x16, [x16, #0x440]
    // 0x12c9994: StoreField: r0->field_f = r16
    //     0x12c9994: stur            w16, [x0, #0xf]
    // 0x12c9998: ldur            x1, [fp, #-0x18]
    // 0x12c999c: r2 = LoadClassIdInstr(r1)
    //     0x12c999c: ldur            x2, [x1, #-1]
    //     0x12c99a0: ubfx            x2, x2, #0xc, #0x14
    // 0x12c99a4: stur            x2, [fp, #-0x20]
    // 0x12c99a8: r17 = 6328
    //     0x12c99a8: movz            x17, #0x18b8
    // 0x12c99ac: cmp             x2, x17
    // 0x12c99b0: b.ne            #0x12c99c0
    // 0x12c99b4: r1 = "utf-8"
    //     0x12c99b4: add             x1, PP, #0x38, lsl #12  ; [pp+0x38b88] "utf-8"
    //     0x12c99b8: ldr             x1, [x1, #0xb88]
    // 0x12c99bc: b               #0x12c99e0
    // 0x12c99c0: r17 = 6329
    //     0x12c99c0: movz            x17, #0x18b9
    // 0x12c99c4: cmp             x2, x17
    // 0x12c99c8: b.ne            #0x12c99d8
    // 0x12c99cc: r1 = "iso-8859-1"
    //     0x12c99cc: add             x1, PP, #0x38, lsl #12  ; [pp+0x38b90] "iso-8859-1"
    //     0x12c99d0: ldr             x1, [x1, #0xb90]
    // 0x12c99d4: b               #0x12c99e0
    // 0x12c99d8: r1 = "us-ascii"
    //     0x12c99d8: add             x1, PP, #0x38, lsl #12  ; [pp+0x38b98] "us-ascii"
    //     0x12c99dc: ldr             x1, [x1, #0xb98]
    // 0x12c99e0: StoreField: r0->field_13 = r1
    //     0x12c99e0: stur            w1, [x0, #0x13]
    // 0x12c99e4: r16 = <String, String>
    //     0x12c99e4: add             x16, PP, #8, lsl #12  ; [pp+0x8788] TypeArguments: <String, String>
    //     0x12c99e8: ldr             x16, [x16, #0x788]
    // 0x12c99ec: stp             x0, x16, [SP]
    // 0x12c99f0: r0 = Map._fromLiteral()
    //     0x12c99f0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x12c99f4: ldur            x1, [fp, #-0x10]
    // 0x12c99f8: mov             x2, x0
    // 0x12c99fc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x12c99fc: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x12c9a00: r0 = change()
    //     0x12c9a00: bl              #0x12c9bdc  ; [package:http_parser/src/media_type.dart] MediaType::change
    // 0x12c9a04: mov             x3, x0
    // 0x12c9a08: ldur            x0, [fp, #-0x20]
    // 0x12c9a0c: stur            x3, [fp, #-0x10]
    // 0x12c9a10: r17 = 6328
    //     0x12c9a10: movz            x17, #0x18b8
    // 0x12c9a14: cmp             x0, x17
    // 0x12c9a18: b.ne            #0x12c9a34
    // 0x12c9a1c: ldur            x2, [fp, #-8]
    // 0x12c9a20: r1 = Instance_Utf8Encoder
    //     0x12c9a20: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d6ee21
    // 0x12c9a24: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x12c9a24: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x12c9a28: r0 = convert()
    //     0x12c9a28: bl              #0x163232c  ; [dart:convert] Utf8Encoder::convert
    // 0x12c9a2c: mov             x2, x0
    // 0x12c9a30: b               #0x12c9a6c
    // 0x12c9a34: r17 = 6329
    //     0x12c9a34: movz            x17, #0x18b9
    // 0x12c9a38: cmp             x0, x17
    // 0x12c9a3c: b.ne            #0x12c9a58
    // 0x12c9a40: ldur            x2, [fp, #-8]
    // 0x12c9a44: r1 = Instance_Latin1Encoder
    //     0x12c9a44: ldr             x1, [PP, #0x11c0]  ; [pp+0x11c0] Obj!Latin1Encoder@d6eee1
    // 0x12c9a48: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x12c9a48: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x12c9a4c: r0 = convert()
    //     0x12c9a4c: bl              #0x162f6ac  ; [dart:convert] _UnicodeSubsetEncoder::convert
    // 0x12c9a50: mov             x2, x0
    // 0x12c9a54: b               #0x12c9a6c
    // 0x12c9a58: ldur            x2, [fp, #-8]
    // 0x12c9a5c: r1 = Instance_AsciiEncoder
    //     0x12c9a5c: ldr             x1, [PP, #0x11c8]  ; [pp+0x11c8] Obj!AsciiEncoder@d6ef01
    // 0x12c9a60: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x12c9a60: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x12c9a64: r0 = convert()
    //     0x12c9a64: bl              #0x162f6ac  ; [dart:convert] _UnicodeSubsetEncoder::convert
    // 0x12c9a68: mov             x2, x0
    // 0x12c9a6c: ldur            x3, [fp, #-0x10]
    // 0x12c9a70: r1 = Null
    //     0x12c9a70: mov             x1, NULL
    // 0x12c9a74: r0 = MultipartFile.fromBytes()
    //     0x12c9a74: bl              #0x12c9a8c  ; [package:dio/src/multipart_file.dart] MultipartFile::MultipartFile.fromBytes
    // 0x12c9a78: LeaveFrame
    //     0x12c9a78: mov             SP, fp
    //     0x12c9a7c: ldp             fp, lr, [SP], #0x10
    // 0x12c9a80: ret
    //     0x12c9a80: ret             
    // 0x12c9a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12c9a84: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12c9a88: b               #0x12c9924
  }
  factory _ MultipartFile.fromBytes(/* No info */) {
    // ** addr: 0x12c9a8c, size: 0x90
    // 0x12c9a8c: EnterFrame
    //     0x12c9a8c: stp             fp, lr, [SP, #-0x10]!
    //     0x12c9a90: mov             fp, SP
    // 0x12c9a94: AllocStack(0x20)
    //     0x12c9a94: sub             SP, SP, #0x20
    // 0x12c9a98: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r5, fp-0x10 */)
    //     0x12c9a98: mov             x5, x3
    //     0x12c9a9c: stur            x2, [fp, #-8]
    //     0x12c9aa0: stur            x3, [fp, #-0x10]
    // 0x12c9aa4: CheckStackOverflow
    //     0x12c9aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x12c9aa8: cmp             SP, x16
    //     0x12c9aac: b.ls            #0x12c9b14
    // 0x12c9ab0: r1 = 1
    //     0x12c9ab0: movz            x1, #0x1
    // 0x12c9ab4: r0 = AllocateContext()
    //     0x12c9ab4: bl              #0x16f6108  ; AllocateContextStub
    // 0x12c9ab8: mov             x1, x0
    // 0x12c9abc: ldur            x0, [fp, #-8]
    // 0x12c9ac0: stur            x1, [fp, #-0x20]
    // 0x12c9ac4: StoreField: r1->field_f = r0
    //     0x12c9ac4: stur            w0, [x1, #0xf]
    // 0x12c9ac8: LoadField: r2 = r0->field_13
    //     0x12c9ac8: ldur            w2, [x0, #0x13]
    // 0x12c9acc: r3 = LoadInt32Instr(r2)
    //     0x12c9acc: sbfx            x3, x2, #1, #0x1f
    // 0x12c9ad0: stur            x3, [fp, #-0x18]
    // 0x12c9ad4: r0 = MultipartFile()
    //     0x12c9ad4: bl              #0x8ab4ac  ; AllocateMultipartFileStub -> MultipartFile (size=0x24)
    // 0x12c9ad8: ldur            x2, [fp, #-0x20]
    // 0x12c9adc: r1 = Function '<anonymous closure>': static.
    //     0x12c9adc: add             x1, PP, #0x38, lsl #12  ; [pp+0x38ba0] AnonymousClosure: static (0x12c9b1c), in [package:dio/src/multipart_file.dart] MultipartFile::MultipartFile.fromBytes (0x12c9a8c)
    //     0x12c9ae0: ldr             x1, [x1, #0xba0]
    // 0x12c9ae4: stur            x0, [fp, #-8]
    // 0x12c9ae8: r0 = AllocateClosure()
    //     0x12c9ae8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x12c9aec: ldur            x1, [fp, #-8]
    // 0x12c9af0: mov             x2, x0
    // 0x12c9af4: ldur            x3, [fp, #-0x18]
    // 0x12c9af8: ldur            x5, [fp, #-0x10]
    // 0x12c9afc: r6 = Null
    //     0x12c9afc: mov             x6, NULL
    // 0x12c9b00: r0 = MultipartFile.fromStream()
    //     0x12c9b00: bl              #0x8ab398  ; [package:dio/src/multipart_file.dart] MultipartFile::MultipartFile.fromStream
    // 0x12c9b04: ldur            x0, [fp, #-8]
    // 0x12c9b08: LeaveFrame
    //     0x12c9b08: mov             SP, fp
    //     0x12c9b0c: ldp             fp, lr, [SP], #0x10
    // 0x12c9b10: ret
    //     0x12c9b10: ret             
    // 0x12c9b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x12c9b14: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x12c9b18: b               #0x12c9ab0
  }
  [closure] static Stream<List<int>> <anonymous closure>(dynamic) {
    // ** addr: 0x12c9b1c, size: 0xc0
    // 0x12c9b1c: EnterFrame
    //     0x12c9b1c: stp             fp, lr, [SP, #-0x10]!
    //     0x12c9b20: mov             fp, SP
    // 0x12c9b24: AllocStack(0x10)
    //     0x12c9b24: sub             SP, SP, #0x10
    // 0x12c9b28: SetupParameters()
    //     0x12c9b28: movz            x0, #0x2
    //     0x12c9b2c: ldr             x1, [fp, #0x10]
    //     0x12c9b30: ldur            w2, [x1, #0x17]
    //     0x12c9b34: add             x2, x2, HEAP, lsl #32
    // 0x12c9b28: r0 = 2
    // 0x12c9b38: LoadField: r3 = r2->field_f
    //     0x12c9b38: ldur            w3, [x2, #0xf]
    // 0x12c9b3c: DecompressPointer r3
    //     0x12c9b3c: add             x3, x3, HEAP, lsl #32
    // 0x12c9b40: mov             x2, x0
    // 0x12c9b44: stur            x3, [fp, #-8]
    // 0x12c9b48: r1 = Null
    //     0x12c9b48: mov             x1, NULL
    // 0x12c9b4c: r0 = AllocateArray()
    //     0x12c9b4c: bl              #0x16f7198  ; AllocateArrayStub
    // 0x12c9b50: mov             x2, x0
    // 0x12c9b54: ldur            x0, [fp, #-8]
    // 0x12c9b58: stur            x2, [fp, #-0x10]
    // 0x12c9b5c: StoreField: r2->field_f = r0
    //     0x12c9b5c: stur            w0, [x2, #0xf]
    // 0x12c9b60: r1 = <List<int>>
    //     0x12c9b60: add             x1, PP, #9, lsl #12  ; [pp+0x90f8] TypeArguments: <List<int>>
    //     0x12c9b64: ldr             x1, [x1, #0xf8]
    // 0x12c9b68: r0 = AllocateGrowableArray()
    //     0x12c9b68: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x12c9b6c: mov             x1, x0
    // 0x12c9b70: ldur            x0, [fp, #-0x10]
    // 0x12c9b74: stur            x1, [fp, #-8]
    // 0x12c9b78: StoreField: r1->field_f = r0
    //     0x12c9b78: stur            w0, [x1, #0xf]
    // 0x12c9b7c: r0 = 2
    //     0x12c9b7c: movz            x0, #0x2
    // 0x12c9b80: StoreField: r1->field_b = r0
    //     0x12c9b80: stur            w0, [x1, #0xb]
    // 0x12c9b84: r1 = 2
    //     0x12c9b84: movz            x1, #0x2
    // 0x12c9b88: r0 = AllocateContext()
    //     0x12c9b88: bl              #0x16f6108  ; AllocateContextStub
    // 0x12c9b8c: mov             x1, x0
    // 0x12c9b90: r0 = <List<int>>
    //     0x12c9b90: add             x0, PP, #9, lsl #12  ; [pp+0x90f8] TypeArguments: <List<int>>
    //     0x12c9b94: ldr             x0, [x0, #0xf8]
    // 0x12c9b98: StoreField: r1->field_f = r0
    //     0x12c9b98: stur            w0, [x1, #0xf]
    // 0x12c9b9c: ldur            x2, [fp, #-8]
    // 0x12c9ba0: StoreField: r1->field_13 = r2
    //     0x12c9ba0: stur            w2, [x1, #0x13]
    // 0x12c9ba4: mov             x2, x1
    // 0x12c9ba8: mov             x3, x0
    // 0x12c9bac: r1 = Function '<anonymous closure>': static.
    //     0x12c9bac: add             x1, PP, #0xa, lsl #12  ; [pp+0xa598] AnonymousClosure: static (0x882458), in [dart:async] Stream::Stream.fromIterable (0x8823ac)
    //     0x12c9bb0: ldr             x1, [x1, #0x598]
    // 0x12c9bb4: r0 = AllocateClosureTA()
    //     0x12c9bb4: bl              #0x16f6310  ; AllocateClosureTAStub
    // 0x12c9bb8: r1 = <List<int>>
    //     0x12c9bb8: add             x1, PP, #9, lsl #12  ; [pp+0x90f8] TypeArguments: <List<int>>
    //     0x12c9bbc: ldr             x1, [x1, #0xf8]
    // 0x12c9bc0: stur            x0, [fp, #-8]
    // 0x12c9bc4: r0 = _MultiStream()
    //     0x12c9bc4: bl              #0x88244c  ; Allocate_MultiStreamStub -> _MultiStream<X0> (size=0x10)
    // 0x12c9bc8: ldur            x1, [fp, #-8]
    // 0x12c9bcc: StoreField: r0->field_b = r1
    //     0x12c9bcc: stur            w1, [x0, #0xb]
    // 0x12c9bd0: LeaveFrame
    //     0x12c9bd0: mov             SP, fp
    //     0x12c9bd4: ldp             fp, lr, [SP], #0x10
    // 0x12c9bd8: ret
    //     0x12c9bd8: ret             
  }
}
