// lib: , url: package:dio/src/transformers/fused_transformer.dart

// class id: 1049616, size: 0x8
class :: {
}

// class id: 4972, size: 0x10, field offset: 0x8
class FusedTransformer extends Transformer {

  static late final Converter<List<int>, Object?> _utf8JsonDecoder; // offset: 0xec4

  _ transformResponse(/* No info */) async {
    // ** addr: 0x8644fc, size: 0x18c
    // 0x8644fc: EnterFrame
    //     0x8644fc: stp             fp, lr, [SP, #-0x10]!
    //     0x864500: mov             fp, SP
    // 0x864504: AllocStack(0x30)
    //     0x864504: sub             SP, SP, #0x30
    // 0x864508: SetupParameters(FusedTransformer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x864508: stur            NULL, [fp, #-8]
    //     0x86450c: stur            x1, [fp, #-0x10]
    //     0x864510: mov             x16, x3
    //     0x864514: mov             x3, x1
    //     0x864518: mov             x1, x16
    //     0x86451c: stur            x2, [fp, #-0x18]
    //     0x864520: stur            x1, [fp, #-0x20]
    // 0x864524: CheckStackOverflow
    //     0x864524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x864528: cmp             SP, x16
    //     0x86452c: b.ls            #0x864674
    // 0x864530: InitAsync() -> Future
    //     0x864530: mov             x0, NULL
    //     0x864534: bl              #0x6326e0  ; InitAsyncStub
    // 0x864538: ldur            x0, [fp, #-0x18]
    // 0x86453c: LoadField: r3 = r0->field_1f
    //     0x86453c: ldur            w3, [x0, #0x1f]
    // 0x864540: DecompressPointer r3
    //     0x864540: add             x3, x3, HEAP, lsl #32
    // 0x864544: r16 = Sentinel
    //     0x864544: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x864548: cmp             w3, w16
    // 0x86454c: b.eq            #0x86467c
    // 0x864550: stur            x3, [fp, #-0x28]
    // 0x864554: r16 = Instance_ResponseType
    //     0x864554: add             x16, PP, #8, lsl #12  ; [pp+0x8338] Obj!ResponseType@d75121
    //     0x864558: ldr             x16, [x16, #0x338]
    // 0x86455c: cmp             w3, w16
    // 0x864560: b.ne            #0x86456c
    // 0x864564: ldur            x0, [fp, #-0x20]
    // 0x864568: r0 = ReturnAsyncNotFuture()
    //     0x864568: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x86456c: r16 = Instance_ResponseType
    //     0x86456c: add             x16, PP, #8, lsl #12  ; [pp+0x8330] Obj!ResponseType@d75141
    //     0x864570: ldr             x16, [x16, #0x330]
    // 0x864574: cmp             w3, w16
    // 0x864578: b.ne            #0x864590
    // 0x86457c: ldur            x4, [fp, #-0x20]
    // 0x864580: LoadField: r1 = r4->field_b
    //     0x864580: ldur            w1, [x4, #0xb]
    // 0x864584: DecompressPointer r1
    //     0x864584: add             x1, x1, HEAP, lsl #32
    // 0x864588: r0 = consolidateBytes()
    //     0x864588: bl              #0x867620  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x86458c: r0 = ReturnAsync()
    //     0x86458c: b               #0x63cf54  ; ReturnAsyncStub
    // 0x864590: ldur            x4, [fp, #-0x20]
    // 0x864594: LoadField: r1 = r4->field_1f
    //     0x864594: ldur            w1, [x4, #0x1f]
    // 0x864598: DecompressPointer r1
    //     0x864598: add             x1, x1, HEAP, lsl #32
    // 0x86459c: r0 = LoadClassIdInstr(r1)
    //     0x86459c: ldur            x0, [x1, #-1]
    //     0x8645a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8645a4: r2 = "content-type"
    //     0x8645a4: add             x2, PP, #8, lsl #12  ; [pp+0x8568] "content-type"
    //     0x8645a8: ldr             x2, [x2, #0x568]
    // 0x8645ac: r0 = GDT[cid_x0 + -0xfe]()
    //     0x8645ac: sub             lr, x0, #0xfe
    //     0x8645b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8645b4: blr             lr
    // 0x8645b8: cmp             w0, NULL
    // 0x8645bc: b.ne            #0x8645c8
    // 0x8645c0: r1 = Null
    //     0x8645c0: mov             x1, NULL
    // 0x8645c4: b               #0x8645f0
    // 0x8645c8: r1 = LoadClassIdInstr(r0)
    //     0x8645c8: ldur            x1, [x0, #-1]
    //     0x8645cc: ubfx            x1, x1, #0xc, #0x14
    // 0x8645d0: mov             x16, x0
    // 0x8645d4: mov             x0, x1
    // 0x8645d8: mov             x1, x16
    // 0x8645dc: r0 = GDT[cid_x0 + 0xe358]()
    //     0x8645dc: movz            x17, #0xe358
    //     0x8645e0: add             lr, x0, x17
    //     0x8645e4: ldr             lr, [x21, lr, lsl #3]
    //     0x8645e8: blr             lr
    // 0x8645ec: mov             x1, x0
    // 0x8645f0: r0 = isJsonMimeType()
    //     0x8645f0: bl              #0x865e30  ; [package:dio/src/transformer.dart] Transformer::isJsonMimeType
    // 0x8645f4: tbnz            w0, #4, #0x86461c
    // 0x8645f8: ldur            x0, [fp, #-0x28]
    // 0x8645fc: r16 = Instance_ResponseType
    //     0x8645fc: add             x16, PP, #8, lsl #12  ; [pp+0x8348] Obj!ResponseType@d750e1
    //     0x864600: ldr             x16, [x16, #0x348]
    // 0x864604: cmp             w0, w16
    // 0x864608: r16 = true
    //     0x864608: add             x16, NULL, #0x20  ; true
    // 0x86460c: r17 = false
    //     0x86460c: add             x17, NULL, #0x30  ; false
    // 0x864610: csel            x1, x16, x17, eq
    // 0x864614: mov             x0, x1
    // 0x864618: b               #0x864620
    // 0x86461c: r0 = false
    //     0x86461c: add             x0, NULL, #0x30  ; false
    // 0x864620: stur            x0, [fp, #-0x18]
    // 0x864624: tbnz            w0, #4, #0x864638
    // 0x864628: ldur            x1, [fp, #-0x10]
    // 0x86462c: ldur            x2, [fp, #-0x20]
    // 0x864630: r0 = _fastUtf8JsonDecode()
    //     0x864630: bl              #0x864688  ; [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_fastUtf8JsonDecode
    // 0x864634: r0 = ReturnAsync()
    //     0x864634: b               #0x63cf54  ; ReturnAsyncStub
    // 0x864638: ldur            x1, [fp, #-0x20]
    // 0x86463c: LoadField: r2 = r1->field_b
    //     0x86463c: ldur            w2, [x1, #0xb]
    // 0x864640: DecompressPointer r2
    //     0x864640: add             x2, x2, HEAP, lsl #32
    // 0x864644: mov             x1, x2
    // 0x864648: r0 = consolidateBytes()
    //     0x864648: bl              #0x867620  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x86464c: mov             x1, x0
    // 0x864650: stur            x1, [fp, #-0x10]
    // 0x864654: r0 = Await()
    //     0x864654: bl              #0x63248c  ; AwaitStub
    // 0x864658: r16 = true
    //     0x864658: add             x16, NULL, #0x20  ; true
    // 0x86465c: str             x16, [SP]
    // 0x864660: mov             x2, x0
    // 0x864664: r1 = Instance_Utf8Codec
    //     0x864664: ldr             x1, [PP, #0x11a0]  ; [pp+0x11a0] Obj!Utf8Codec@d6eda1
    // 0x864668: r4 = const [0, 0x3, 0x1, 0x2, allowMalformed, 0x2, null]
    //     0x864668: ldr             x4, [PP, #0x31c0]  ; [pp+0x31c0] List(7) [0, 0x3, 0x1, 0x2, "allowMalformed", 0x2, Null]
    // 0x86466c: r0 = decode()
    //     0x86466c: bl              #0x62a214  ; [dart:convert] Utf8Codec::decode
    // 0x864670: r0 = ReturnAsyncNotFuture()
    //     0x864670: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x864674: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x864674: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x864678: b               #0x864530
    // 0x86467c: r9 = responseType
    //     0x86467c: add             x9, PP, #8, lsl #12  ; [pp+0x83a8] Field <<EMAIL>>: late (offset: 0x20)
    //     0x864680: ldr             x9, [x9, #0x3a8]
    // 0x864684: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x864684: bl              #0x16f7bb0  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _fastUtf8JsonDecode(/* No info */) async {
    // ** addr: 0x864688, size: 0x290
    // 0x864688: EnterFrame
    //     0x864688: stp             fp, lr, [SP, #-0x10]!
    //     0x86468c: mov             fp, SP
    // 0x864690: AllocStack(0x40)
    //     0x864690: sub             SP, SP, #0x40
    // 0x864694: SetupParameters(FusedTransformer this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x864694: stur            NULL, [fp, #-8]
    //     0x864698: stur            x1, [fp, #-0x10]
    //     0x86469c: stur            x2, [fp, #-0x18]
    // 0x8646a0: CheckStackOverflow
    //     0x8646a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8646a4: cmp             SP, x16
    //     0x8646a8: b.ls            #0x864910
    // 0x8646ac: InitAsync() -> Future<Object?>
    //     0x8646ac: ldr             x0, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    //     0x8646b0: bl              #0x6326e0  ; InitAsyncStub
    // 0x8646b4: ldur            x3, [fp, #-0x18]
    // 0x8646b8: LoadField: r1 = r3->field_1f
    //     0x8646b8: ldur            w1, [x3, #0x1f]
    // 0x8646bc: DecompressPointer r1
    //     0x8646bc: add             x1, x1, HEAP, lsl #32
    // 0x8646c0: r0 = LoadClassIdInstr(r1)
    //     0x8646c0: ldur            x0, [x1, #-1]
    //     0x8646c4: ubfx            x0, x0, #0xc, #0x14
    // 0x8646c8: r2 = "content-length"
    //     0x8646c8: add             x2, PP, #8, lsl #12  ; [pp+0x8570] "content-length"
    //     0x8646cc: ldr             x2, [x2, #0x570]
    // 0x8646d0: r0 = GDT[cid_x0 + -0xfe]()
    //     0x8646d0: sub             lr, x0, #0xfe
    //     0x8646d4: ldr             lr, [x21, lr, lsl #3]
    //     0x8646d8: blr             lr
    // 0x8646dc: mov             x2, x0
    // 0x8646e0: stur            x2, [fp, #-0x20]
    // 0x8646e4: cmp             w2, NULL
    // 0x8646e8: b.eq            #0x864740
    // 0x8646ec: r0 = LoadClassIdInstr(r2)
    //     0x8646ec: ldur            x0, [x2, #-1]
    //     0x8646f0: ubfx            x0, x0, #0xc, #0x14
    // 0x8646f4: mov             x1, x2
    // 0x8646f8: r0 = GDT[cid_x0 + 0xe517]()
    //     0x8646f8: movz            x17, #0xe517
    //     0x8646fc: add             lr, x0, x17
    //     0x864700: ldr             lr, [x21, lr, lsl #3]
    //     0x864704: blr             lr
    // 0x864708: tbnz            w0, #4, #0x864740
    // 0x86470c: ldur            x1, [fp, #-0x20]
    // 0x864710: r0 = LoadClassIdInstr(r1)
    //     0x864710: ldur            x0, [x1, #-1]
    //     0x864714: ubfx            x0, x0, #0xc, #0x14
    // 0x864718: r0 = GDT[cid_x0 + 0xe358]()
    //     0x864718: movz            x17, #0xe358
    //     0x86471c: add             lr, x0, x17
    //     0x864720: ldr             lr, [x21, lr, lsl #3]
    //     0x864724: blr             lr
    // 0x864728: mov             x1, x0
    // 0x86472c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x86472c: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x864730: r0 = parse()
    //     0x864730: bl              #0x6255f0  ; [dart:core] int::parse
    // 0x864734: mov             x2, x0
    // 0x864738: r1 = Null
    //     0x864738: mov             x1, NULL
    // 0x86473c: b               #0x864768
    // 0x864740: ldur            x0, [fp, #-0x18]
    // 0x864744: LoadField: r1 = r0->field_b
    //     0x864744: ldur            w1, [x0, #0xb]
    // 0x864748: DecompressPointer r1
    //     0x864748: add             x1, x1, HEAP, lsl #32
    // 0x86474c: r0 = consolidateBytes()
    //     0x86474c: bl              #0x867620  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x864750: mov             x1, x0
    // 0x864754: stur            x1, [fp, #-0x20]
    // 0x864758: r0 = Await()
    //     0x864758: bl              #0x63248c  ; AwaitStub
    // 0x86475c: LoadField: r1 = r0->field_13
    //     0x86475c: ldur            w1, [x0, #0x13]
    // 0x864760: r2 = LoadInt32Instr(r1)
    //     0x864760: sbfx            x2, x1, #1, #0x1f
    // 0x864764: mov             x1, x0
    // 0x864768: ldur            x0, [fp, #-0x10]
    // 0x86476c: stur            x1, [fp, #-0x20]
    // 0x864770: LoadField: r3 = r0->field_7
    //     0x864770: ldur            x3, [x0, #7]
    // 0x864774: tbnz            x3, #0x3f, #0x8647ec
    // 0x864778: cmp             x2, x3
    // 0x86477c: b.lt            #0x8647e4
    // 0x864780: cmp             w1, NULL
    // 0x864784: b.ne            #0x8647a8
    // 0x864788: ldur            x0, [fp, #-0x18]
    // 0x86478c: LoadField: r1 = r0->field_b
    //     0x86478c: ldur            w1, [x0, #0xb]
    // 0x864790: DecompressPointer r1
    //     0x864790: add             x1, x1, HEAP, lsl #32
    // 0x864794: r0 = consolidateBytes()
    //     0x864794: bl              #0x867620  ; [package:dio/src/transformers/util/consolidate_bytes.dart] ::consolidateBytes
    // 0x864798: mov             x1, x0
    // 0x86479c: stur            x1, [fp, #-0x10]
    // 0x8647a0: r0 = Await()
    //     0x8647a0: bl              #0x63248c  ; AwaitStub
    // 0x8647a4: b               #0x8647ac
    // 0x8647a8: mov             x0, x1
    // 0x8647ac: r16 = <Uint8List, Object?>
    //     0x8647ac: add             x16, PP, #8, lsl #12  ; [pp+0x8578] TypeArguments: <Uint8List, Object?>
    //     0x8647b0: ldr             x16, [x16, #0x578]
    // 0x8647b4: r30 = Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static.
    //     0x8647b4: add             lr, PP, #8, lsl #12  ; [pp+0x8580] Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static. (0x7fa737864f20)
    //     0x8647b8: ldr             lr, [lr, #0x580]
    // 0x8647bc: stp             lr, x16, [SP, #0x10]
    // 0x8647c0: r16 = Closure: (Uint8List) => Future<Object?> from Function '_decodeUtf8ToJson@1784206049': static.
    //     0x8647c0: add             x16, PP, #8, lsl #12  ; [pp+0x8588] Closure: (Uint8List) => Future<Object?> from Function '_decodeUtf8ToJson@1784206049': static. (0x7fa737864e28)
    //     0x8647c4: ldr             x16, [x16, #0x588]
    // 0x8647c8: stp             x0, x16, [SP]
    // 0x8647cc: r0 = Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static.
    //     0x8647cc: add             x0, PP, #8, lsl #12  ; [pp+0x8580] Closure: <Y0, Y1>((Y0) => FutureOr<Y1>, Y0, {String? debugLabel}) => Future<Y1> from Function 'compute': static. (0x7fa737864f20)
    //     0x8647d0: ldr             x0, [x0, #0x580]
    // 0x8647d4: ClosureCall
    //     0x8647d4: ldr             x4, [PP, #0x32e0]  ; [pp+0x32e0] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    //     0x8647d8: ldur            x2, [x0, #0x1f]
    //     0x8647dc: blr             x2
    // 0x8647e0: r0 = ReturnAsync()
    //     0x8647e0: b               #0x63cf54  ; ReturnAsyncStub
    // 0x8647e4: ldur            x0, [fp, #-0x18]
    // 0x8647e8: b               #0x8647f0
    // 0x8647ec: ldur            x0, [fp, #-0x18]
    // 0x8647f0: cmp             w1, NULL
    // 0x8647f4: b.eq            #0x864854
    // 0x8647f8: LoadField: r0 = r1->field_13
    //     0x8647f8: ldur            w0, [x1, #0x13]
    // 0x8647fc: cbnz            w0, #0x864808
    // 0x864800: r0 = Null
    //     0x864800: mov             x0, NULL
    // 0x864804: r0 = ReturnAsyncNotFuture()
    //     0x864804: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x864808: r0 = InitLateStaticField(0xec4) // [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_utf8JsonDecoder
    //     0x864808: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x86480c: ldr             x0, [x0, #0x1d88]
    //     0x864810: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x864814: cmp             w0, w16
    //     0x864818: b.ne            #0x864828
    //     0x86481c: add             x2, PP, #8, lsl #12  ; [pp+0x8590] Field <FusedTransformer._utf8JsonDecoder@1784206049>: static late final (offset: 0xec4)
    //     0x864820: ldr             x2, [x2, #0x590]
    //     0x864824: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x864828: r1 = LoadClassIdInstr(r0)
    //     0x864828: ldur            x1, [x0, #-1]
    //     0x86482c: ubfx            x1, x1, #0xc, #0x14
    // 0x864830: mov             x16, x0
    // 0x864834: mov             x0, x1
    // 0x864838: mov             x1, x16
    // 0x86483c: ldur            x2, [fp, #-0x20]
    // 0x864840: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x864840: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x864844: r0 = GDT[cid_x0 + 0x59f]()
    //     0x864844: add             lr, x0, #0x59f
    //     0x864848: ldr             lr, [x21, lr, lsl #3]
    //     0x86484c: blr             lr
    // 0x864850: r0 = ReturnAsync()
    //     0x864850: b               #0x63cf54  ; ReturnAsyncStub
    // 0x864854: LoadField: r1 = r0->field_b
    //     0x864854: ldur            w1, [x0, #0xb]
    // 0x864858: DecompressPointer r1
    //     0x864858: add             x1, x1, HEAP, lsl #32
    // 0x86485c: r16 = <Uint8List>
    //     0x86485c: add             x16, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x864860: ldr             x16, [x16, #0x598]
    // 0x864864: stp             x1, x16, [SP, #8]
    // 0x864868: r16 = Instance_DefaultNullIfEmptyStreamTransformer
    //     0x864868: add             x16, PP, #8, lsl #12  ; [pp+0x85a0] Obj!DefaultNullIfEmptyStreamTransformer@d6f121
    //     0x86486c: ldr             x16, [x16, #0x5a0]
    // 0x864870: str             x16, [SP]
    // 0x864874: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x864874: ldr             x4, [PP, #0x48]  ; [pp+0x48] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x864878: r0 = transform()
    //     0x864878: bl              #0x864bd0  ; [dart:async] Stream::transform
    // 0x86487c: stur            x0, [fp, #-0x10]
    // 0x864880: r0 = InitLateStaticField(0xec4) // [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_utf8JsonDecoder
    //     0x864880: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x864884: ldr             x0, [x0, #0x1d88]
    //     0x864888: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x86488c: cmp             w0, w16
    //     0x864890: b.ne            #0x8648a0
    //     0x864894: add             x2, PP, #8, lsl #12  ; [pp+0x8590] Field <FusedTransformer._utf8JsonDecoder@1784206049>: static late final (offset: 0xec4)
    //     0x864898: ldr             x2, [x2, #0x590]
    //     0x86489c: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x8648a0: mov             x1, x0
    // 0x8648a4: ldur            x2, [fp, #-0x10]
    // 0x8648a8: r0 = bind()
    //     0x8648a8: bl              #0x160c140  ; [dart:convert] Converter::bind
    // 0x8648ac: mov             x1, x0
    // 0x8648b0: r0 = toList()
    //     0x8648b0: bl              #0x864918  ; [dart:async] Stream::toList
    // 0x8648b4: mov             x1, x0
    // 0x8648b8: stur            x1, [fp, #-0x10]
    // 0x8648bc: r0 = Await()
    //     0x8648bc: bl              #0x63248c  ; AwaitStub
    // 0x8648c0: mov             x2, x0
    // 0x8648c4: stur            x2, [fp, #-0x10]
    // 0x8648c8: r0 = LoadClassIdInstr(r2)
    //     0x8648c8: ldur            x0, [x2, #-1]
    //     0x8648cc: ubfx            x0, x0, #0xc, #0x14
    // 0x8648d0: mov             x1, x2
    // 0x8648d4: r0 = GDT[cid_x0 + 0xe667]()
    //     0x8648d4: movz            x17, #0xe667
    //     0x8648d8: add             lr, x0, x17
    //     0x8648dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8648e0: blr             lr
    // 0x8648e4: tbnz            w0, #4, #0x8648f0
    // 0x8648e8: r0 = Null
    //     0x8648e8: mov             x0, NULL
    // 0x8648ec: r0 = ReturnAsyncNotFuture()
    //     0x8648ec: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x8648f0: ldur            x1, [fp, #-0x10]
    // 0x8648f4: r0 = LoadClassIdInstr(r1)
    //     0x8648f4: ldur            x0, [x1, #-1]
    //     0x8648f8: ubfx            x0, x0, #0xc, #0x14
    // 0x8648fc: r0 = GDT[cid_x0 + 0xe358]()
    //     0x8648fc: movz            x17, #0xe358
    //     0x864900: add             lr, x0, x17
    //     0x864904: ldr             lr, [x21, lr, lsl #3]
    //     0x864908: blr             lr
    // 0x86490c: r0 = ReturnAsync()
    //     0x86490c: b               #0x63cf54  ; ReturnAsyncStub
    // 0x864910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x864910: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x864914: b               #0x8646ac
  }
  static Converter<List<int>, Object?> _utf8JsonDecoder() {
    // ** addr: 0x864df4, size: 0x28
    // 0x864df4: EnterFrame
    //     0x864df4: stp             fp, lr, [SP, #-0x10]!
    //     0x864df8: mov             fp, SP
    // 0x864dfc: r1 = <List<int>, Object?>
    //     0x864dfc: add             x1, PP, #8, lsl #12  ; [pp+0x8738] TypeArguments: <List<int>, Object?>
    //     0x864e00: ldr             x1, [x1, #0x738]
    // 0x864e04: r0 = _JsonUtf8Decoder()
    //     0x864e04: bl              #0x864e1c  ; Allocate_JsonUtf8DecoderStub -> _JsonUtf8Decoder (size=0x14)
    // 0x864e08: r1 = false
    //     0x864e08: add             x1, NULL, #0x30  ; false
    // 0x864e0c: StoreField: r0->field_f = r1
    //     0x864e0c: stur            w1, [x0, #0xf]
    // 0x864e10: LeaveFrame
    //     0x864e10: mov             SP, fp
    //     0x864e14: ldp             fp, lr, [SP], #0x10
    // 0x864e18: ret
    //     0x864e18: ret             
  }
  [closure] static Future<Object?> _decodeUtf8ToJson(dynamic, Uint8List) {
    // ** addr: 0x864e28, size: 0x30
    // 0x864e28: EnterFrame
    //     0x864e28: stp             fp, lr, [SP, #-0x10]!
    //     0x864e2c: mov             fp, SP
    // 0x864e30: CheckStackOverflow
    //     0x864e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x864e34: cmp             SP, x16
    //     0x864e38: b.ls            #0x864e50
    // 0x864e3c: ldr             x1, [fp, #0x10]
    // 0x864e40: r0 = _decodeUtf8ToJson()
    //     0x864e40: bl              #0x864e58  ; [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_decodeUtf8ToJson
    // 0x864e44: LeaveFrame
    //     0x864e44: mov             SP, fp
    //     0x864e48: ldp             fp, lr, [SP], #0x10
    // 0x864e4c: ret
    //     0x864e4c: ret             
    // 0x864e50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x864e50: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x864e54: b               #0x864e3c
  }
  static _ _decodeUtf8ToJson(/* No info */) async {
    // ** addr: 0x864e58, size: 0x94
    // 0x864e58: EnterFrame
    //     0x864e58: stp             fp, lr, [SP, #-0x10]!
    //     0x864e5c: mov             fp, SP
    // 0x864e60: AllocStack(0x10)
    //     0x864e60: sub             SP, SP, #0x10
    // 0x864e64: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */)
    //     0x864e64: stur            NULL, [fp, #-8]
    //     0x864e68: mov             x2, x1
    //     0x864e6c: stur            x1, [fp, #-0x10]
    // 0x864e70: CheckStackOverflow
    //     0x864e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x864e74: cmp             SP, x16
    //     0x864e78: b.ls            #0x864ee4
    // 0x864e7c: InitAsync() -> Future<Object?>
    //     0x864e7c: ldr             x0, [PP, #0xf00]  ; [pp+0xf00] TypeArguments: <Object?>
    //     0x864e80: bl              #0x6326e0  ; InitAsyncStub
    // 0x864e84: ldur            x2, [fp, #-0x10]
    // 0x864e88: LoadField: r0 = r2->field_13
    //     0x864e88: ldur            w0, [x2, #0x13]
    // 0x864e8c: cbnz            w0, #0x864e98
    // 0x864e90: r0 = Null
    //     0x864e90: mov             x0, NULL
    // 0x864e94: r0 = ReturnAsyncNotFuture()
    //     0x864e94: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x864e98: r0 = InitLateStaticField(0xec4) // [package:dio/src/transformers/fused_transformer.dart] FusedTransformer::_utf8JsonDecoder
    //     0x864e98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x864e9c: ldr             x0, [x0, #0x1d88]
    //     0x864ea0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x864ea4: cmp             w0, w16
    //     0x864ea8: b.ne            #0x864eb8
    //     0x864eac: add             x2, PP, #8, lsl #12  ; [pp+0x8590] Field <FusedTransformer._utf8JsonDecoder@1784206049>: static late final (offset: 0xec4)
    //     0x864eb0: ldr             x2, [x2, #0x590]
    //     0x864eb4: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x864eb8: r1 = LoadClassIdInstr(r0)
    //     0x864eb8: ldur            x1, [x0, #-1]
    //     0x864ebc: ubfx            x1, x1, #0xc, #0x14
    // 0x864ec0: mov             x16, x0
    // 0x864ec4: mov             x0, x1
    // 0x864ec8: mov             x1, x16
    // 0x864ecc: ldur            x2, [fp, #-0x10]
    // 0x864ed0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x864ed0: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x864ed4: r0 = GDT[cid_x0 + 0x59f]()
    //     0x864ed4: add             lr, x0, #0x59f
    //     0x864ed8: ldr             lr, [x21, lr, lsl #3]
    //     0x864edc: blr             lr
    // 0x864ee0: r0 = ReturnAsync()
    //     0x864ee0: b               #0x63cf54  ; ReturnAsyncStub
    // 0x864ee4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x864ee4: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x864ee8: b               #0x864e7c
  }
  _ transformRequest(/* No info */) async {
    // ** addr: 0x882994, size: 0x4c
    // 0x882994: EnterFrame
    //     0x882994: stp             fp, lr, [SP, #-0x10]!
    //     0x882998: mov             fp, SP
    // 0x88299c: AllocStack(0x18)
    //     0x88299c: sub             SP, SP, #0x18
    // 0x8829a0: SetupParameters(FusedTransformer this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x8829a0: stur            NULL, [fp, #-8]
    //     0x8829a4: stur            x1, [fp, #-0x10]
    //     0x8829a8: mov             x16, x2
    //     0x8829ac: mov             x2, x1
    //     0x8829b0: mov             x1, x16
    //     0x8829b4: stur            x1, [fp, #-0x18]
    // 0x8829b8: CheckStackOverflow
    //     0x8829b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8829bc: cmp             SP, x16
    //     0x8829c0: b.ls            #0x8829d8
    // 0x8829c4: InitAsync() -> Future<String>
    //     0x8829c4: ldr             x0, [PP, #0x8b0]  ; [pp+0x8b0] TypeArguments: <String>
    //     0x8829c8: bl              #0x6326e0  ; InitAsyncStub
    // 0x8829cc: ldur            x1, [fp, #-0x18]
    // 0x8829d0: r0 = defaultTransformRequest()
    //     0x8829d0: bl              #0x8829e0  ; [package:dio/src/transformer.dart] Transformer::defaultTransformRequest
    // 0x8829d4: r0 = ReturnAsync()
    //     0x8829d4: b               #0x63cf54  ; ReturnAsyncStub
    // 0x8829d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8829d8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8829dc: b               #0x8829c4
  }
}
