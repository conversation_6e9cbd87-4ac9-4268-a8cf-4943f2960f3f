// lib: , url: package:dio/src/transformers/util/consolidate_bytes.dart

// class id: 1049617, size: 0x8
class :: {

  static _ consolidateBytes(/* No info */) async {
    // ** addr: 0x867620, size: 0x36c
    // 0x867620: EnterFrame
    //     0x867620: stp             fp, lr, [SP, #-0x10]!
    //     0x867624: mov             fp, SP
    // 0x867628: AllocStack(0x98)
    //     0x867628: sub             SP, SP, #0x98
    // 0x86762c: SetupParameters(dynamic _ /* r1 => r1, fp-0x68 */)
    //     0x86762c: stur            NULL, [fp, #-8]
    //     0x867630: stur            x1, [fp, #-0x68]
    // 0x867634: CheckStackOverflow
    //     0x867634: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x867638: cmp             SP, x16
    //     0x86763c: b.ls            #0x86797c
    // 0x867640: InitAsync() -> Future<Uint8List>
    //     0x867640: add             x0, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x867644: ldr             x0, [x0, #0x598]
    //     0x867648: bl              #0x6326e0  ; InitAsyncStub
    // 0x86764c: r1 = Null
    //     0x86764c: mov             x1, NULL
    // 0x867650: r0 = BytesBuilder()
    //     0x867650: bl              #0x868478  ; [dart:_internal] BytesBuilder::BytesBuilder
    // 0x867654: r1 = <Uint8List>
    //     0x867654: add             x1, PP, #8, lsl #12  ; [pp+0x8598] TypeArguments: <Uint8List>
    //     0x867658: ldr             x1, [x1, #0x598]
    // 0x86765c: stur            x0, [fp, #-0x70]
    // 0x867660: r0 = _StreamIterator()
    //     0x867660: bl              #0x86846c  ; Allocate_StreamIteratorStub -> _StreamIterator<X0> (size=0x18)
    // 0x867664: mov             x2, x0
    // 0x867668: r0 = false
    //     0x867668: add             x0, NULL, #0x30  ; false
    // 0x86766c: stur            x2, [fp, #-0x80]
    // 0x867670: StoreField: r2->field_13 = r0
    //     0x867670: stur            w0, [x2, #0x13]
    // 0x867674: ldur            x1, [fp, #-0x68]
    // 0x867678: StoreField: r2->field_f = r1
    //     0x867678: stur            w1, [x2, #0xf]
    // 0x86767c: ldur            x3, [fp, #-0x70]
    // 0x867680: LoadField: r4 = r3->field_f
    //     0x867680: ldur            w4, [x3, #0xf]
    // 0x867684: DecompressPointer r4
    //     0x867684: add             x4, x4, HEAP, lsl #32
    // 0x867688: stur            x4, [fp, #-0x78]
    // 0x86768c: CheckStackOverflow
    //     0x86768c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x867690: cmp             SP, x16
    //     0x867694: b.ls            #0x867984
    // 0x867698: LoadField: r5 = r2->field_b
    //     0x867698: ldur            w5, [x2, #0xb]
    // 0x86769c: DecompressPointer r5
    //     0x86769c: add             x5, x5, HEAP, lsl #32
    // 0x8676a0: stur            x5, [fp, #-0x68]
    // 0x8676a4: cmp             w5, NULL
    // 0x8676a8: b.eq            #0x867738
    // 0x8676ac: LoadField: r1 = r2->field_13
    //     0x8676ac: ldur            w1, [x2, #0x13]
    // 0x8676b0: DecompressPointer r1
    //     0x8676b0: add             x1, x1, HEAP, lsl #32
    // 0x8676b4: tbnz            w1, #4, #0x867908
    // 0x8676b8: r1 = <bool>
    //     0x8676b8: ldr             x1, [PP, #0x18c0]  ; [pp+0x18c0] TypeArguments: <bool>
    // 0x8676bc: r0 = _Future()
    //     0x8676bc: bl              #0x632664  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8676c0: stur            x0, [fp, #-0x88]
    // 0x8676c4: StoreField: r0->field_b = rZR
    //     0x8676c4: stur            xzr, [x0, #0xb]
    // 0x8676c8: r0 = InitLateStaticField(0x3bc) // [dart:async] Zone::_current
    //     0x8676c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8676cc: ldr             x0, [x0, #0x778]
    //     0x8676d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8676d4: cmp             w0, w16
    //     0x8676d8: b.ne            #0x8676e4
    //     0x8676dc: ldr             x2, [PP, #0x308]  ; [pp+0x308] Field <Zone._current@5048458>: static late (offset: 0x3bc)
    //     0x8676e0: bl              #0x16f5348  ; InitLateStaticFieldStub
    // 0x8676e4: ldur            x2, [fp, #-0x88]
    // 0x8676e8: StoreField: r2->field_13 = r0
    //     0x8676e8: stur            w0, [x2, #0x13]
    // 0x8676ec: mov             x0, x2
    // 0x8676f0: ldur            x3, [fp, #-0x80]
    // 0x8676f4: StoreField: r3->field_f = r0
    //     0x8676f4: stur            w0, [x3, #0xf]
    //     0x8676f8: ldurb           w16, [x3, #-1]
    //     0x8676fc: ldurb           w17, [x0, #-1]
    //     0x867700: and             x16, x17, x16, lsr #2
    //     0x867704: tst             x16, HEAP, lsr #32
    //     0x867708: b.eq            #0x867710
    //     0x86770c: bl              #0x16f58c8  ; WriteBarrierWrappersStub
    // 0x867710: r4 = false
    //     0x867710: add             x4, NULL, #0x30  ; false
    // 0x867714: StoreField: r3->field_13 = r4
    //     0x867714: stur            w4, [x3, #0x13]
    // 0x867718: ldur            x1, [fp, #-0x68]
    // 0x86771c: r0 = LoadClassIdInstr(r1)
    //     0x86771c: ldur            x0, [x1, #-1]
    //     0x867720: ubfx            x0, x0, #0xc, #0x14
    // 0x867724: r0 = GDT[cid_x0 + 0x397]()
    //     0x867724: add             lr, x0, #0x397
    //     0x867728: ldr             lr, [x21, lr, lsl #3]
    //     0x86772c: blr             lr
    // 0x867730: ldur            x1, [fp, #-0x88]
    // 0x867734: b               #0x867744
    // 0x867738: ldur            x1, [fp, #-0x80]
    // 0x86773c: r0 = _initializeOrDone()
    //     0x86773c: bl              #0x867f3c  ; [dart:async] _StreamIterator::_initializeOrDone
    // 0x867740: mov             x1, x0
    // 0x867744: mov             x0, x1
    // 0x867748: stur            x1, [fp, #-0x68]
    // 0x86774c: r0 = Await()
    //     0x86774c: bl              #0x63248c  ; AwaitStub
    // 0x867750: r16 = true
    //     0x867750: add             x16, NULL, #0x20  ; true
    // 0x867754: cmp             w0, w16
    // 0x867758: b.ne            #0x8678d4
    // 0x86775c: ldur            x3, [fp, #-0x80]
    // 0x867760: LoadField: r0 = r3->field_13
    //     0x867760: ldur            w0, [x3, #0x13]
    // 0x867764: DecompressPointer r0
    //     0x867764: add             x0, x0, HEAP, lsl #32
    // 0x867768: tbnz            w0, #4, #0x8677bc
    // 0x86776c: LoadField: r4 = r3->field_f
    //     0x86776c: ldur            w4, [x3, #0xf]
    // 0x867770: DecompressPointer r4
    //     0x867770: add             x4, x4, HEAP, lsl #32
    // 0x867774: mov             x0, x4
    // 0x867778: stur            x4, [fp, #-0x68]
    // 0x86777c: r2 = Null
    //     0x86777c: mov             x2, NULL
    // 0x867780: r1 = Null
    //     0x867780: mov             x1, NULL
    // 0x867784: r4 = 60
    //     0x867784: movz            x4, #0x3c
    // 0x867788: branchIfSmi(r0, 0x867794)
    //     0x867788: tbz             w0, #0, #0x867794
    // 0x86778c: r4 = LoadClassIdInstr(r0)
    //     0x86778c: ldur            x4, [x0, #-1]
    //     0x867790: ubfx            x4, x4, #0xc, #0x14
    // 0x867794: sub             x4, x4, #0x74
    // 0x867798: cmp             x4, #3
    // 0x86779c: b.ls            #0x8677b4
    // 0x8677a0: r8 = Uint8List
    //     0x8677a0: add             x8, PP, #8, lsl #12  ; [pp+0x89a0] Type: Uint8List
    //     0x8677a4: ldr             x8, [x8, #0x9a0]
    // 0x8677a8: r3 = Null
    //     0x8677a8: add             x3, PP, #8, lsl #12  ; [pp+0x89a8] Null
    //     0x8677ac: ldr             x3, [x3, #0x9a8]
    // 0x8677b0: r0 = Uint8List()
    //     0x8677b0: bl              #0x61da58  ; IsType_Uint8List_Stub
    // 0x8677b4: ldur            x0, [fp, #-0x68]
    // 0x8677b8: b               #0x8677f4
    // 0x8677bc: r0 = Null
    //     0x8677bc: mov             x0, NULL
    // 0x8677c0: r2 = Null
    //     0x8677c0: mov             x2, NULL
    // 0x8677c4: r1 = Null
    //     0x8677c4: mov             x1, NULL
    // 0x8677c8: r4 = LoadClassIdInstr(r0)
    //     0x8677c8: ldur            x4, [x0, #-1]
    //     0x8677cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8677d0: sub             x4, x4, #0x74
    // 0x8677d4: cmp             x4, #3
    // 0x8677d8: b.ls            #0x8677f0
    // 0x8677dc: r8 = Uint8List
    //     0x8677dc: add             x8, PP, #8, lsl #12  ; [pp+0x89a0] Type: Uint8List
    //     0x8677e0: ldr             x8, [x8, #0x9a0]
    // 0x8677e4: r3 = Null
    //     0x8677e4: add             x3, PP, #8, lsl #12  ; [pp+0x89b8] Null
    //     0x8677e8: ldr             x3, [x3, #0x9b8]
    // 0x8677ec: r0 = Uint8List()
    //     0x8677ec: bl              #0x61da58  ; IsType_Uint8List_Stub
    // 0x8677f0: r0 = Null
    //     0x8677f0: mov             x0, NULL
    // 0x8677f4: stur            x0, [fp, #-0x68]
    // 0x8677f8: r1 = LoadClassIdInstr(r0)
    //     0x8677f8: ldur            x1, [x0, #-1]
    //     0x8677fc: ubfx            x1, x1, #0xc, #0x14
    // 0x867800: sub             x16, x1, #0x74
    // 0x867804: cmp             x16, #3
    // 0x867808: b.hi            #0x867814
    // 0x86780c: mov             x2, x0
    // 0x867810: b               #0x867824
    // 0x867814: mov             x2, x0
    // 0x867818: r1 = Null
    //     0x867818: mov             x1, NULL
    // 0x86781c: r0 = Uint8List.fromList()
    //     0x86781c: bl              #0x6568d8  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0x867820: mov             x2, x0
    // 0x867824: ldur            x0, [fp, #-0x78]
    // 0x867828: stur            x2, [fp, #-0x98]
    // 0x86782c: LoadField: r3 = r0->field_b
    //     0x86782c: ldur            w3, [x0, #0xb]
    // 0x867830: stur            x3, [fp, #-0x88]
    // 0x867834: LoadField: r1 = r0->field_f
    //     0x867834: ldur            w1, [x0, #0xf]
    // 0x867838: DecompressPointer r1
    //     0x867838: add             x1, x1, HEAP, lsl #32
    // 0x86783c: LoadField: r4 = r1->field_b
    //     0x86783c: ldur            w4, [x1, #0xb]
    // 0x867840: r5 = LoadInt32Instr(r3)
    //     0x867840: sbfx            x5, x3, #1, #0x1f
    // 0x867844: stur            x5, [fp, #-0x90]
    // 0x867848: r1 = LoadInt32Instr(r4)
    //     0x867848: sbfx            x1, x4, #1, #0x1f
    // 0x86784c: cmp             x5, x1
    // 0x867850: b.ne            #0x86785c
    // 0x867854: mov             x1, x0
    // 0x867858: r0 = _growToNextCapacity()
    //     0x867858: bl              #0x627d84  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x86785c: ldur            x5, [fp, #-0x70]
    // 0x867860: ldur            x3, [fp, #-0x98]
    // 0x867864: ldur            x2, [fp, #-0x78]
    // 0x867868: ldur            x4, [fp, #-0x90]
    // 0x86786c: add             x0, x4, #1
    // 0x867870: lsl             x1, x0, #1
    // 0x867874: StoreField: r2->field_b = r1
    //     0x867874: stur            w1, [x2, #0xb]
    // 0x867878: LoadField: r1 = r2->field_f
    //     0x867878: ldur            w1, [x2, #0xf]
    // 0x86787c: DecompressPointer r1
    //     0x86787c: add             x1, x1, HEAP, lsl #32
    // 0x867880: mov             x0, x3
    // 0x867884: ArrayStore: r1[r4] = r0  ; List_4
    //     0x867884: add             x25, x1, x4, lsl #2
    //     0x867888: add             x25, x25, #0xf
    //     0x86788c: str             w0, [x25]
    //     0x867890: tbz             w0, #0, #0x8678ac
    //     0x867894: ldurb           w16, [x1, #-1]
    //     0x867898: ldurb           w17, [x0, #-1]
    //     0x86789c: and             x16, x17, x16, lsr #2
    //     0x8678a0: tst             x16, HEAP, lsr #32
    //     0x8678a4: b.eq            #0x8678ac
    //     0x8678a8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x8678ac: LoadField: r0 = r5->field_7
    //     0x8678ac: ldur            x0, [x5, #7]
    // 0x8678b0: LoadField: r1 = r3->field_13
    //     0x8678b0: ldur            w1, [x3, #0x13]
    // 0x8678b4: r3 = LoadInt32Instr(r1)
    //     0x8678b4: sbfx            x3, x1, #1, #0x1f
    // 0x8678b8: add             x1, x0, x3
    // 0x8678bc: StoreField: r5->field_7 = r1
    //     0x8678bc: stur            x1, [x5, #7]
    // 0x8678c0: mov             x3, x5
    // 0x8678c4: mov             x4, x2
    // 0x8678c8: ldur            x2, [fp, #-0x80]
    // 0x8678cc: r0 = false
    //     0x8678cc: add             x0, NULL, #0x30  ; false
    // 0x8678d0: b               #0x86768c
    // 0x8678d4: ldur            x5, [fp, #-0x70]
    // 0x8678d8: ldur            x1, [fp, #-0x80]
    // 0x8678dc: LoadField: r0 = r1->field_b
    //     0x8678dc: ldur            w0, [x1, #0xb]
    // 0x8678e0: DecompressPointer r0
    //     0x8678e0: add             x0, x0, HEAP, lsl #32
    // 0x8678e4: cmp             w0, NULL
    // 0x8678e8: b.eq            #0x8678fc
    // 0x8678ec: r0 = cancel()
    //     0x8678ec: bl              #0x867dec  ; [dart:async] _StreamIterator::cancel
    // 0x8678f0: mov             x1, x0
    // 0x8678f4: stur            x1, [fp, #-0x68]
    // 0x8678f8: r0 = Await()
    //     0x8678f8: bl              #0x63248c  ; AwaitStub
    // 0x8678fc: ldur            x1, [fp, #-0x70]
    // 0x867900: r0 = takeBytes()
    //     0x867900: bl              #0x8679a0  ; [dart:_internal] _BytesBuilder::takeBytes
    // 0x867904: r0 = ReturnAsyncNotFuture()
    //     0x867904: b               #0x6321b4  ; ReturnAsyncNotFutureStub
    // 0x867908: mov             x1, x2
    // 0x86790c: r0 = StateError()
    //     0x86790c: bl              #0x622864  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x867910: mov             x1, x0
    // 0x867914: r0 = "Already waiting for next."
    //     0x867914: add             x0, PP, #8, lsl #12  ; [pp+0x89c8] "Already waiting for next."
    //     0x867918: ldr             x0, [x0, #0x9c8]
    // 0x86791c: stur            x1, [fp, #-0x68]
    // 0x867920: StoreField: r1->field_b = r0
    //     0x867920: stur            w0, [x1, #0xb]
    // 0x867924: mov             x0, x1
    // 0x867928: r0 = Throw()
    //     0x867928: bl              #0x16f5420  ; ThrowStub
    // 0x86792c: brk             #0
    // 0x867930: sub             SP, fp, #0x98
    // 0x867934: ldur            x2, [fp, #-0x80]
    // 0x867938: mov             x3, x0
    // 0x86793c: stur            x0, [fp, #-0x68]
    // 0x867940: mov             x0, x1
    // 0x867944: stur            x1, [fp, #-0x70]
    // 0x867948: LoadField: r1 = r2->field_b
    //     0x867948: ldur            w1, [x2, #0xb]
    // 0x86794c: DecompressPointer r1
    //     0x86794c: add             x1, x1, HEAP, lsl #32
    // 0x867950: cmp             w1, NULL
    // 0x867954: b.eq            #0x86796c
    // 0x867958: mov             x1, x2
    // 0x86795c: r0 = cancel()
    //     0x86795c: bl              #0x867dec  ; [dart:async] _StreamIterator::cancel
    // 0x867960: mov             x1, x0
    // 0x867964: stur            x1, [fp, #-0x78]
    // 0x867968: r0 = Await()
    //     0x867968: bl              #0x63248c  ; AwaitStub
    // 0x86796c: ldur            x0, [fp, #-0x68]
    // 0x867970: ldur            x1, [fp, #-0x70]
    // 0x867974: r0 = ReThrow()
    //     0x867974: bl              #0x16f53f4  ; ReThrowStub
    // 0x867978: brk             #0
    // 0x86797c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86797c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867980: b               #0x867640
    // 0x867984: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x867984: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x867988: b               #0x867698
  }
}
