// lib: , url: package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart

// class id: 1049149, size: 0x8
class :: {
}

// class id: 4634, size: 0x14, field offset: 0x14
//   const constructor, 
class ExchangeProductSkusScreen extends BaseView<dynamic> {

  _ bottomNavigationBar(/* No info */) {
    // ** addr: 0x1317e84, size: 0x64
    // 0x1317e84: EnterFrame
    //     0x1317e84: stp             fp, lr, [SP, #-0x10]!
    //     0x1317e88: mov             fp, SP
    // 0x1317e8c: AllocStack(0x18)
    //     0x1317e8c: sub             SP, SP, #0x18
    // 0x1317e90: SetupParameters(ExchangeProductSkusScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x1317e90: stur            x1, [fp, #-8]
    //     0x1317e94: stur            x2, [fp, #-0x10]
    // 0x1317e98: r1 = 2
    //     0x1317e98: movz            x1, #0x2
    // 0x1317e9c: r0 = AllocateContext()
    //     0x1317e9c: bl              #0x16f6108  ; AllocateContextStub
    // 0x1317ea0: mov             x1, x0
    // 0x1317ea4: ldur            x0, [fp, #-8]
    // 0x1317ea8: stur            x1, [fp, #-0x18]
    // 0x1317eac: StoreField: r1->field_f = r0
    //     0x1317eac: stur            w0, [x1, #0xf]
    // 0x1317eb0: ldur            x0, [fp, #-0x10]
    // 0x1317eb4: StoreField: r1->field_13 = r0
    //     0x1317eb4: stur            w0, [x1, #0x13]
    // 0x1317eb8: r0 = Obx()
    //     0x1317eb8: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x1317ebc: ldur            x2, [fp, #-0x18]
    // 0x1317ec0: r1 = Function '<anonymous closure>':.
    //     0x1317ec0: add             x1, PP, #0x46, lsl #12  ; [pp+0x464a0] AnonymousClosure: (0x1317ee8), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::bottomNavigationBar (0x1317e84)
    //     0x1317ec4: ldr             x1, [x1, #0x4a0]
    // 0x1317ec8: stur            x0, [fp, #-8]
    // 0x1317ecc: r0 = AllocateClosure()
    //     0x1317ecc: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x1317ed0: mov             x1, x0
    // 0x1317ed4: ldur            x0, [fp, #-8]
    // 0x1317ed8: StoreField: r0->field_b = r1
    //     0x1317ed8: stur            w1, [x0, #0xb]
    // 0x1317edc: LeaveFrame
    //     0x1317edc: mov             SP, fp
    //     0x1317ee0: ldp             fp, lr, [SP], #0x10
    // 0x1317ee4: ret
    //     0x1317ee4: ret             
  }
  [closure] Padding <anonymous closure>(dynamic) {
    // ** addr: 0x1317ee8, size: 0x2c8
    // 0x1317ee8: EnterFrame
    //     0x1317ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x1317eec: mov             fp, SP
    // 0x1317ef0: AllocStack(0x40)
    //     0x1317ef0: sub             SP, SP, #0x40
    // 0x1317ef4: SetupParameters()
    //     0x1317ef4: ldr             x0, [fp, #0x10]
    //     0x1317ef8: ldur            w2, [x0, #0x17]
    //     0x1317efc: add             x2, x2, HEAP, lsl #32
    //     0x1317f00: stur            x2, [fp, #-8]
    // 0x1317f04: CheckStackOverflow
    //     0x1317f04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1317f08: cmp             SP, x16
    //     0x1317f0c: b.ls            #0x13181a8
    // 0x1317f10: r16 = <EdgeInsets>
    //     0x1317f10: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x1317f14: ldr             x16, [x16, #0xda0]
    // 0x1317f18: r30 = Instance_EdgeInsets
    //     0x1317f18: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x1317f1c: ldr             lr, [lr, #0x1f0]
    // 0x1317f20: stp             lr, x16, [SP]
    // 0x1317f24: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1317f24: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1317f28: r0 = all()
    //     0x1317f28: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1317f2c: ldur            x2, [fp, #-8]
    // 0x1317f30: stur            x0, [fp, #-0x10]
    // 0x1317f34: LoadField: r1 = r2->field_f
    //     0x1317f34: ldur            w1, [x2, #0xf]
    // 0x1317f38: DecompressPointer r1
    //     0x1317f38: add             x1, x1, HEAP, lsl #32
    // 0x1317f3c: r0 = controller()
    //     0x1317f3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1317f40: LoadField: r1 = r0->field_5b
    //     0x1317f40: ldur            w1, [x0, #0x5b]
    // 0x1317f44: DecompressPointer r1
    //     0x1317f44: add             x1, x1, HEAP, lsl #32
    // 0x1317f48: r0 = RxStringExt.isNotEmpty()
    //     0x1317f48: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1317f4c: tbnz            w0, #4, #0x1317f70
    // 0x1317f50: ldur            x2, [fp, #-8]
    // 0x1317f54: LoadField: r1 = r2->field_13
    //     0x1317f54: ldur            w1, [x2, #0x13]
    // 0x1317f58: DecompressPointer r1
    //     0x1317f58: add             x1, x1, HEAP, lsl #32
    // 0x1317f5c: r0 = of()
    //     0x1317f5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1317f60: LoadField: r1 = r0->field_5b
    //     0x1317f60: ldur            w1, [x0, #0x5b]
    // 0x1317f64: DecompressPointer r1
    //     0x1317f64: add             x1, x1, HEAP, lsl #32
    // 0x1317f68: mov             x0, x1
    // 0x1317f6c: b               #0x1317f7c
    // 0x1317f70: r1 = Instance_Color
    //     0x1317f70: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1317f74: d0 = 0.400000
    //     0x1317f74: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1317f78: r0 = withOpacity()
    //     0x1317f78: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1317f7c: ldur            x2, [fp, #-8]
    // 0x1317f80: r16 = <Color>
    //     0x1317f80: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1317f84: ldr             x16, [x16, #0xf80]
    // 0x1317f88: stp             x0, x16, [SP]
    // 0x1317f8c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1317f8c: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1317f90: r0 = all()
    //     0x1317f90: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1317f94: ldur            x2, [fp, #-8]
    // 0x1317f98: stur            x0, [fp, #-0x18]
    // 0x1317f9c: LoadField: r1 = r2->field_f
    //     0x1317f9c: ldur            w1, [x2, #0xf]
    // 0x1317fa0: DecompressPointer r1
    //     0x1317fa0: add             x1, x1, HEAP, lsl #32
    // 0x1317fa4: r0 = controller()
    //     0x1317fa4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1317fa8: LoadField: r1 = r0->field_5b
    //     0x1317fa8: ldur            w1, [x0, #0x5b]
    // 0x1317fac: DecompressPointer r1
    //     0x1317fac: add             x1, x1, HEAP, lsl #32
    // 0x1317fb0: r0 = RxStringExt.isNotEmpty()
    //     0x1317fb0: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1317fb4: tbnz            w0, #4, #0x1317fd8
    // 0x1317fb8: ldur            x2, [fp, #-8]
    // 0x1317fbc: LoadField: r1 = r2->field_13
    //     0x1317fbc: ldur            w1, [x2, #0x13]
    // 0x1317fc0: DecompressPointer r1
    //     0x1317fc0: add             x1, x1, HEAP, lsl #32
    // 0x1317fc4: r0 = of()
    //     0x1317fc4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x1317fc8: LoadField: r1 = r0->field_5b
    //     0x1317fc8: ldur            w1, [x0, #0x5b]
    // 0x1317fcc: DecompressPointer r1
    //     0x1317fcc: add             x1, x1, HEAP, lsl #32
    // 0x1317fd0: mov             x3, x1
    // 0x1317fd4: b               #0x1317fe8
    // 0x1317fd8: r1 = Instance_Color
    //     0x1317fd8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x1317fdc: d0 = 0.400000
    //     0x1317fdc: ldr             d0, [PP, #0x64c8]  ; [pp+0x64c8] IMM: double(0.4) from 0x3fd999999999999a
    // 0x1317fe0: r0 = withOpacity()
    //     0x1317fe0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x1317fe4: mov             x3, x0
    // 0x1317fe8: ldur            x2, [fp, #-8]
    // 0x1317fec: ldur            x1, [fp, #-0x10]
    // 0x1317ff0: ldur            x0, [fp, #-0x18]
    // 0x1317ff4: r16 = <Color>
    //     0x1317ff4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x1317ff8: ldr             x16, [x16, #0xf80]
    // 0x1317ffc: stp             x3, x16, [SP]
    // 0x1318000: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1318000: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1318004: r0 = all()
    //     0x1318004: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1318008: stur            x0, [fp, #-0x20]
    // 0x131800c: r16 = <RoundedRectangleBorder>
    //     0x131800c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x1318010: ldr             x16, [x16, #0xf78]
    // 0x1318014: r30 = Instance_RoundedRectangleBorder
    //     0x1318014: add             lr, PP, #0x43, lsl #12  ; [pp+0x438d0] Obj!RoundedRectangleBorder@d5ac11
    //     0x1318018: ldr             lr, [lr, #0x8d0]
    // 0x131801c: stp             lr, x16, [SP]
    // 0x1318020: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x1318020: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x1318024: r0 = all()
    //     0x1318024: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x1318028: stur            x0, [fp, #-0x28]
    // 0x131802c: r0 = ButtonStyle()
    //     0x131802c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x1318030: mov             x1, x0
    // 0x1318034: ldur            x0, [fp, #-0x20]
    // 0x1318038: stur            x1, [fp, #-0x30]
    // 0x131803c: StoreField: r1->field_b = r0
    //     0x131803c: stur            w0, [x1, #0xb]
    // 0x1318040: ldur            x0, [fp, #-0x18]
    // 0x1318044: StoreField: r1->field_f = r0
    //     0x1318044: stur            w0, [x1, #0xf]
    // 0x1318048: ldur            x0, [fp, #-0x10]
    // 0x131804c: StoreField: r1->field_23 = r0
    //     0x131804c: stur            w0, [x1, #0x23]
    // 0x1318050: ldur            x0, [fp, #-0x28]
    // 0x1318054: StoreField: r1->field_43 = r0
    //     0x1318054: stur            w0, [x1, #0x43]
    // 0x1318058: r0 = TextButtonThemeData()
    //     0x1318058: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x131805c: mov             x2, x0
    // 0x1318060: ldur            x0, [fp, #-0x30]
    // 0x1318064: stur            x2, [fp, #-0x10]
    // 0x1318068: StoreField: r2->field_7 = r0
    //     0x1318068: stur            w0, [x2, #7]
    // 0x131806c: ldur            x0, [fp, #-8]
    // 0x1318070: LoadField: r1 = r0->field_f
    //     0x1318070: ldur            w1, [x0, #0xf]
    // 0x1318074: DecompressPointer r1
    //     0x1318074: add             x1, x1, HEAP, lsl #32
    // 0x1318078: r0 = controller()
    //     0x1318078: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131807c: LoadField: r1 = r0->field_5b
    //     0x131807c: ldur            w1, [x0, #0x5b]
    // 0x1318080: DecompressPointer r1
    //     0x1318080: add             x1, x1, HEAP, lsl #32
    // 0x1318084: r0 = RxStringExt.isNotEmpty()
    //     0x1318084: bl              #0x13181b0  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.isNotEmpty
    // 0x1318088: tbnz            w0, #4, #0x13180a4
    // 0x131808c: ldur            x2, [fp, #-8]
    // 0x1318090: r1 = Function '<anonymous closure>':.
    //     0x1318090: add             x1, PP, #0x46, lsl #12  ; [pp+0x464a8] AnonymousClosure: (0x1318224), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::bottomNavigationBar (0x1317e84)
    //     0x1318094: ldr             x1, [x1, #0x4a8]
    // 0x1318098: r0 = AllocateClosure()
    //     0x1318098: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x131809c: mov             x2, x0
    // 0x13180a0: b               #0x13180a8
    // 0x13180a4: r2 = Null
    //     0x13180a4: mov             x2, NULL
    // 0x13180a8: ldur            x1, [fp, #-8]
    // 0x13180ac: ldur            x0, [fp, #-0x10]
    // 0x13180b0: stur            x2, [fp, #-0x18]
    // 0x13180b4: LoadField: r3 = r1->field_13
    //     0x13180b4: ldur            w3, [x1, #0x13]
    // 0x13180b8: DecompressPointer r3
    //     0x13180b8: add             x3, x3, HEAP, lsl #32
    // 0x13180bc: mov             x1, x3
    // 0x13180c0: r0 = of()
    //     0x13180c0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13180c4: LoadField: r1 = r0->field_87
    //     0x13180c4: ldur            w1, [x0, #0x87]
    // 0x13180c8: DecompressPointer r1
    //     0x13180c8: add             x1, x1, HEAP, lsl #32
    // 0x13180cc: LoadField: r0 = r1->field_2b
    //     0x13180cc: ldur            w0, [x1, #0x2b]
    // 0x13180d0: DecompressPointer r0
    //     0x13180d0: add             x0, x0, HEAP, lsl #32
    // 0x13180d4: r16 = Instance_Color
    //     0x13180d4: ldr             x16, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13180d8: str             x16, [SP]
    // 0x13180dc: mov             x1, x0
    // 0x13180e0: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0x13180e0: add             x4, PP, #0x12, lsl #12  ; [pp+0x12f40] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0x13180e4: ldr             x4, [x4, #0xf40]
    // 0x13180e8: r0 = copyWith()
    //     0x13180e8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13180ec: stur            x0, [fp, #-8]
    // 0x13180f0: r0 = Text()
    //     0x13180f0: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13180f4: mov             x1, x0
    // 0x13180f8: r0 = "Next"
    //     0x13180f8: add             x0, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] "Next"
    //     0x13180fc: ldr             x0, [x0, #0x3e8]
    // 0x1318100: stur            x1, [fp, #-0x20]
    // 0x1318104: StoreField: r1->field_b = r0
    //     0x1318104: stur            w0, [x1, #0xb]
    // 0x1318108: ldur            x0, [fp, #-8]
    // 0x131810c: StoreField: r1->field_13 = r0
    //     0x131810c: stur            w0, [x1, #0x13]
    // 0x1318110: r0 = TextButton()
    //     0x1318110: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x1318114: mov             x1, x0
    // 0x1318118: ldur            x0, [fp, #-0x18]
    // 0x131811c: stur            x1, [fp, #-8]
    // 0x1318120: StoreField: r1->field_b = r0
    //     0x1318120: stur            w0, [x1, #0xb]
    // 0x1318124: r0 = false
    //     0x1318124: add             x0, NULL, #0x30  ; false
    // 0x1318128: StoreField: r1->field_27 = r0
    //     0x1318128: stur            w0, [x1, #0x27]
    // 0x131812c: r0 = true
    //     0x131812c: add             x0, NULL, #0x20  ; true
    // 0x1318130: StoreField: r1->field_2f = r0
    //     0x1318130: stur            w0, [x1, #0x2f]
    // 0x1318134: ldur            x0, [fp, #-0x20]
    // 0x1318138: StoreField: r1->field_37 = r0
    //     0x1318138: stur            w0, [x1, #0x37]
    // 0x131813c: r0 = TextButtonTheme()
    //     0x131813c: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x1318140: mov             x1, x0
    // 0x1318144: ldur            x0, [fp, #-0x10]
    // 0x1318148: stur            x1, [fp, #-0x18]
    // 0x131814c: StoreField: r1->field_f = r0
    //     0x131814c: stur            w0, [x1, #0xf]
    // 0x1318150: ldur            x0, [fp, #-8]
    // 0x1318154: StoreField: r1->field_b = r0
    //     0x1318154: stur            w0, [x1, #0xb]
    // 0x1318158: r0 = SizedBox()
    //     0x1318158: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x131815c: mov             x1, x0
    // 0x1318160: r0 = inf
    //     0x1318160: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x1318164: ldr             x0, [x0, #0x9f8]
    // 0x1318168: stur            x1, [fp, #-8]
    // 0x131816c: StoreField: r1->field_f = r0
    //     0x131816c: stur            w0, [x1, #0xf]
    // 0x1318170: r0 = 48.000000
    //     0x1318170: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x1318174: ldr             x0, [x0, #0xad8]
    // 0x1318178: StoreField: r1->field_13 = r0
    //     0x1318178: stur            w0, [x1, #0x13]
    // 0x131817c: ldur            x0, [fp, #-0x18]
    // 0x1318180: StoreField: r1->field_b = r0
    //     0x1318180: stur            w0, [x1, #0xb]
    // 0x1318184: r0 = Padding()
    //     0x1318184: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x1318188: r1 = Instance_EdgeInsets
    //     0x1318188: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x131818c: ldr             x1, [x1, #0x1f0]
    // 0x1318190: StoreField: r0->field_f = r1
    //     0x1318190: stur            w1, [x0, #0xf]
    // 0x1318194: ldur            x1, [fp, #-8]
    // 0x1318198: StoreField: r0->field_b = r1
    //     0x1318198: stur            w1, [x0, #0xb]
    // 0x131819c: LeaveFrame
    //     0x131819c: mov             SP, fp
    //     0x13181a0: ldp             fp, lr, [SP], #0x10
    // 0x13181a4: ret
    //     0x13181a4: ret             
    // 0x13181a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13181a8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13181ac: b               #0x1317f10
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x1318224, size: 0x260
    // 0x1318224: EnterFrame
    //     0x1318224: stp             fp, lr, [SP, #-0x10]!
    //     0x1318228: mov             fp, SP
    // 0x131822c: AllocStack(0x28)
    //     0x131822c: sub             SP, SP, #0x28
    // 0x1318230: SetupParameters()
    //     0x1318230: ldr             x0, [fp, #0x10]
    //     0x1318234: ldur            w2, [x0, #0x17]
    //     0x1318238: add             x2, x2, HEAP, lsl #32
    //     0x131823c: stur            x2, [fp, #-8]
    // 0x1318240: CheckStackOverflow
    //     0x1318240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x1318244: cmp             SP, x16
    //     0x1318248: b.ls            #0x131847c
    // 0x131824c: LoadField: r1 = r2->field_f
    //     0x131824c: ldur            w1, [x2, #0xf]
    // 0x1318250: DecompressPointer r1
    //     0x1318250: add             x1, x1, HEAP, lsl #32
    // 0x1318254: r0 = controller()
    //     0x1318254: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1318258: mov             x1, x0
    // 0x131825c: r2 = "Next"
    //     0x131825c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f3e8] "Next"
    //     0x1318260: ldr             x2, [x2, #0x3e8]
    // 0x1318264: r3 = "return_exchange_continue"
    //     0x1318264: add             x3, PP, #0x33, lsl #12  ; [pp+0x33718] "return_exchange_continue"
    //     0x1318268: ldr             x3, [x3, #0x718]
    // 0x131826c: r0 = ctaPostEvent()
    //     0x131826c: bl              #0x1318484  ; [package:customer_app/app/presentation/controllers/exchange/exchange_same_product_controller.dart] ExchangeSameProductController::ctaPostEvent
    // 0x1318270: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x1318270: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x1318274: ldr             x0, [x0, #0x1c80]
    //     0x1318278: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x131827c: cmp             w0, w16
    //     0x1318280: b.ne            #0x131828c
    //     0x1318284: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x1318288: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x131828c: r1 = Null
    //     0x131828c: mov             x1, NULL
    // 0x1318290: r2 = 20
    //     0x1318290: movz            x2, #0x14
    // 0x1318294: r0 = AllocateArray()
    //     0x1318294: bl              #0x16f7198  ; AllocateArrayStub
    // 0x1318298: stur            x0, [fp, #-0x10]
    // 0x131829c: r16 = "order_id"
    //     0x131829c: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x13182a0: ldr             x16, [x16, #0xa38]
    // 0x13182a4: StoreField: r0->field_f = r16
    //     0x13182a4: stur            w16, [x0, #0xf]
    // 0x13182a8: ldur            x2, [fp, #-8]
    // 0x13182ac: LoadField: r1 = r2->field_f
    //     0x13182ac: ldur            w1, [x2, #0xf]
    // 0x13182b0: DecompressPointer r1
    //     0x13182b0: add             x1, x1, HEAP, lsl #32
    // 0x13182b4: r0 = controller()
    //     0x13182b4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13182b8: LoadField: r1 = r0->field_6f
    //     0x13182b8: ldur            w1, [x0, #0x6f]
    // 0x13182bc: DecompressPointer r1
    //     0x13182bc: add             x1, x1, HEAP, lsl #32
    // 0x13182c0: r0 = value()
    //     0x13182c0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13182c4: ldur            x1, [fp, #-0x10]
    // 0x13182c8: ArrayStore: r1[1] = r0  ; List_4
    //     0x13182c8: add             x25, x1, #0x13
    //     0x13182cc: str             w0, [x25]
    //     0x13182d0: tbz             w0, #0, #0x13182ec
    //     0x13182d4: ldurb           w16, [x1, #-1]
    //     0x13182d8: ldurb           w17, [x0, #-1]
    //     0x13182dc: and             x16, x17, x16, lsr #2
    //     0x13182e0: tst             x16, HEAP, lsr #32
    //     0x13182e4: b.eq            #0x13182ec
    //     0x13182e8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13182ec: ldur            x0, [fp, #-0x10]
    // 0x13182f0: r16 = "charge"
    //     0x13182f0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b28] "charge"
    //     0x13182f4: ldr             x16, [x16, #0xb28]
    // 0x13182f8: ArrayStore: r0[0] = r16  ; List_4
    //     0x13182f8: stur            w16, [x0, #0x17]
    // 0x13182fc: ldur            x2, [fp, #-8]
    // 0x1318300: LoadField: r1 = r2->field_f
    //     0x1318300: ldur            w1, [x2, #0xf]
    // 0x1318304: DecompressPointer r1
    //     0x1318304: add             x1, x1, HEAP, lsl #32
    // 0x1318308: r0 = controller()
    //     0x1318308: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131830c: LoadField: r1 = r0->field_67
    //     0x131830c: ldur            w1, [x0, #0x67]
    // 0x1318310: DecompressPointer r1
    //     0x1318310: add             x1, x1, HEAP, lsl #32
    // 0x1318314: mov             x0, x1
    // 0x1318318: ldur            x1, [fp, #-0x10]
    // 0x131831c: ArrayStore: r1[3] = r0  ; List_4
    //     0x131831c: add             x25, x1, #0x1b
    //     0x1318320: str             w0, [x25]
    //     0x1318324: tbz             w0, #0, #0x1318340
    //     0x1318328: ldurb           w16, [x1, #-1]
    //     0x131832c: ldurb           w17, [x0, #-1]
    //     0x1318330: and             x16, x17, x16, lsr #2
    //     0x1318334: tst             x16, HEAP, lsr #32
    //     0x1318338: b.eq            #0x1318340
    //     0x131833c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1318340: ldur            x0, [fp, #-0x10]
    // 0x1318344: r16 = "type"
    //     0x1318344: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x1318348: StoreField: r0->field_1f = r16
    //     0x1318348: stur            w16, [x0, #0x1f]
    // 0x131834c: ldur            x2, [fp, #-8]
    // 0x1318350: LoadField: r1 = r2->field_f
    //     0x1318350: ldur            w1, [x2, #0xf]
    // 0x1318354: DecompressPointer r1
    //     0x1318354: add             x1, x1, HEAP, lsl #32
    // 0x1318358: r0 = controller()
    //     0x1318358: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x131835c: LoadField: r1 = r0->field_6b
    //     0x131835c: ldur            w1, [x0, #0x6b]
    // 0x1318360: DecompressPointer r1
    //     0x1318360: add             x1, x1, HEAP, lsl #32
    // 0x1318364: mov             x0, x1
    // 0x1318368: ldur            x1, [fp, #-0x10]
    // 0x131836c: ArrayStore: r1[5] = r0  ; List_4
    //     0x131836c: add             x25, x1, #0x23
    //     0x1318370: str             w0, [x25]
    //     0x1318374: tbz             w0, #0, #0x1318390
    //     0x1318378: ldurb           w16, [x1, #-1]
    //     0x131837c: ldurb           w17, [x0, #-1]
    //     0x1318380: and             x16, x17, x16, lsr #2
    //     0x1318384: tst             x16, HEAP, lsr #32
    //     0x1318388: b.eq            #0x1318390
    //     0x131838c: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x1318390: ldur            x0, [fp, #-0x10]
    // 0x1318394: r16 = "sku_short_id"
    //     0x1318394: add             x16, PP, #0xb, lsl #12  ; [pp+0xb4a0] "sku_short_id"
    //     0x1318398: ldr             x16, [x16, #0x4a0]
    // 0x131839c: StoreField: r0->field_27 = r16
    //     0x131839c: stur            w16, [x0, #0x27]
    // 0x13183a0: ldur            x2, [fp, #-8]
    // 0x13183a4: LoadField: r1 = r2->field_f
    //     0x13183a4: ldur            w1, [x2, #0xf]
    // 0x13183a8: DecompressPointer r1
    //     0x13183a8: add             x1, x1, HEAP, lsl #32
    // 0x13183ac: r0 = controller()
    //     0x13183ac: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13183b0: LoadField: r1 = r0->field_5b
    //     0x13183b0: ldur            w1, [x0, #0x5b]
    // 0x13183b4: DecompressPointer r1
    //     0x13183b4: add             x1, x1, HEAP, lsl #32
    // 0x13183b8: r0 = value()
    //     0x13183b8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13183bc: ldur            x1, [fp, #-0x10]
    // 0x13183c0: ArrayStore: r1[7] = r0  ; List_4
    //     0x13183c0: add             x25, x1, #0x2b
    //     0x13183c4: str             w0, [x25]
    //     0x13183c8: tbz             w0, #0, #0x13183e4
    //     0x13183cc: ldurb           w16, [x1, #-1]
    //     0x13183d0: ldurb           w17, [x0, #-1]
    //     0x13183d4: and             x16, x17, x16, lsr #2
    //     0x13183d8: tst             x16, HEAP, lsr #32
    //     0x13183dc: b.eq            #0x13183e4
    //     0x13183e0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13183e4: ldur            x0, [fp, #-0x10]
    // 0x13183e8: r16 = "customer_product_skus_id"
    //     0x13183e8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32cd8] "customer_product_skus_id"
    //     0x13183ec: ldr             x16, [x16, #0xcd8]
    // 0x13183f0: StoreField: r0->field_2f = r16
    //     0x13183f0: stur            w16, [x0, #0x2f]
    // 0x13183f4: ldur            x1, [fp, #-8]
    // 0x13183f8: LoadField: r2 = r1->field_f
    //     0x13183f8: ldur            w2, [x1, #0xf]
    // 0x13183fc: DecompressPointer r2
    //     0x13183fc: add             x2, x2, HEAP, lsl #32
    // 0x1318400: mov             x1, x2
    // 0x1318404: r0 = controller()
    //     0x1318404: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x1318408: LoadField: r1 = r0->field_5f
    //     0x1318408: ldur            w1, [x0, #0x5f]
    // 0x131840c: DecompressPointer r1
    //     0x131840c: add             x1, x1, HEAP, lsl #32
    // 0x1318410: mov             x0, x1
    // 0x1318414: ldur            x1, [fp, #-0x10]
    // 0x1318418: ArrayStore: r1[9] = r0  ; List_4
    //     0x1318418: add             x25, x1, #0x33
    //     0x131841c: str             w0, [x25]
    //     0x1318420: tbz             w0, #0, #0x131843c
    //     0x1318424: ldurb           w16, [x1, #-1]
    //     0x1318428: ldurb           w17, [x0, #-1]
    //     0x131842c: and             x16, x17, x16, lsr #2
    //     0x1318430: tst             x16, HEAP, lsr #32
    //     0x1318434: b.eq            #0x131843c
    //     0x1318438: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x131843c: r16 = <String, Object?>
    //     0x131843c: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0x1318440: ldr             x16, [x16, #0xc28]
    // 0x1318444: ldur            lr, [fp, #-0x10]
    // 0x1318448: stp             lr, x16, [SP]
    // 0x131844c: r0 = Map._fromLiteral()
    //     0x131844c: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x1318450: r16 = "/return-order"
    //     0x1318450: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x1318454: ldr             x16, [x16, #0x8b8]
    // 0x1318458: stp             x16, NULL, [SP, #8]
    // 0x131845c: str             x0, [SP]
    // 0x1318460: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x1318460: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x1318464: ldr             x4, [x4, #0x438]
    // 0x1318468: r0 = GetNavigation.toNamed()
    //     0x1318468: bl              #0x8a56b4  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.toNamed
    // 0x131846c: r0 = Null
    //     0x131846c: mov             x0, NULL
    // 0x1318470: LeaveFrame
    //     0x1318470: mov             SP, fp
    //     0x1318474: ldp             fp, lr, [SP], #0x10
    // 0x1318478: ret
    //     0x1318478: ret             
    // 0x131847c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x131847c: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x1318480: b               #0x131824c
  }
  _ body(/* No info */) {
    // ** addr: 0x13c9858, size: 0x64
    // 0x13c9858: EnterFrame
    //     0x13c9858: stp             fp, lr, [SP, #-0x10]!
    //     0x13c985c: mov             fp, SP
    // 0x13c9860: AllocStack(0x18)
    //     0x13c9860: sub             SP, SP, #0x18
    // 0x13c9864: SetupParameters(ExchangeProductSkusScreen this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x13c9864: stur            x1, [fp, #-8]
    //     0x13c9868: stur            x2, [fp, #-0x10]
    // 0x13c986c: r1 = 2
    //     0x13c986c: movz            x1, #0x2
    // 0x13c9870: r0 = AllocateContext()
    //     0x13c9870: bl              #0x16f6108  ; AllocateContextStub
    // 0x13c9874: mov             x1, x0
    // 0x13c9878: ldur            x0, [fp, #-8]
    // 0x13c987c: stur            x1, [fp, #-0x18]
    // 0x13c9880: StoreField: r1->field_f = r0
    //     0x13c9880: stur            w0, [x1, #0xf]
    // 0x13c9884: ldur            x0, [fp, #-0x10]
    // 0x13c9888: StoreField: r1->field_13 = r0
    //     0x13c9888: stur            w0, [x1, #0x13]
    // 0x13c988c: r0 = Obx()
    //     0x13c988c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13c9890: ldur            x2, [fp, #-0x18]
    // 0x13c9894: r1 = Function '<anonymous closure>':.
    //     0x13c9894: add             x1, PP, #0x46, lsl #12  ; [pp+0x464b0] AnonymousClosure: (0x13c98bc), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x13c9858)
    //     0x13c9898: ldr             x1, [x1, #0x4b0]
    // 0x13c989c: stur            x0, [fp, #-8]
    // 0x13c98a0: r0 = AllocateClosure()
    //     0x13c98a0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c98a4: mov             x1, x0
    // 0x13c98a8: ldur            x0, [fp, #-8]
    // 0x13c98ac: StoreField: r0->field_b = r1
    //     0x13c98ac: stur            w1, [x0, #0xb]
    // 0x13c98b0: LeaveFrame
    //     0x13c98b0: mov             SP, fp
    //     0x13c98b4: ldp             fp, lr, [SP], #0x10
    // 0x13c98b8: ret
    //     0x13c98b8: ret             
  }
  [closure] SingleChildScrollView <anonymous closure>(dynamic) {
    // ** addr: 0x13c98bc, size: 0x15d0
    // 0x13c98bc: EnterFrame
    //     0x13c98bc: stp             fp, lr, [SP, #-0x10]!
    //     0x13c98c0: mov             fp, SP
    // 0x13c98c4: AllocStack(0xa8)
    //     0x13c98c4: sub             SP, SP, #0xa8
    // 0x13c98c8: SetupParameters()
    //     0x13c98c8: ldr             x0, [fp, #0x10]
    //     0x13c98cc: ldur            w2, [x0, #0x17]
    //     0x13c98d0: add             x2, x2, HEAP, lsl #32
    //     0x13c98d4: stur            x2, [fp, #-8]
    // 0x13c98d8: CheckStackOverflow
    //     0x13c98d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13c98dc: cmp             SP, x16
    //     0x13c98e0: b.ls            #0x13cae58
    // 0x13c98e4: r0 = Radius()
    //     0x13c98e4: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13c98e8: d0 = 12.000000
    //     0x13c98e8: fmov            d0, #12.00000000
    // 0x13c98ec: stur            x0, [fp, #-0x10]
    // 0x13c98f0: StoreField: r0->field_7 = d0
    //     0x13c98f0: stur            d0, [x0, #7]
    // 0x13c98f4: StoreField: r0->field_f = d0
    //     0x13c98f4: stur            d0, [x0, #0xf]
    // 0x13c98f8: r0 = BorderRadius()
    //     0x13c98f8: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13c98fc: mov             x2, x0
    // 0x13c9900: ldur            x0, [fp, #-0x10]
    // 0x13c9904: stur            x2, [fp, #-0x18]
    // 0x13c9908: StoreField: r2->field_7 = r0
    //     0x13c9908: stur            w0, [x2, #7]
    // 0x13c990c: StoreField: r2->field_b = r0
    //     0x13c990c: stur            w0, [x2, #0xb]
    // 0x13c9910: StoreField: r2->field_f = r0
    //     0x13c9910: stur            w0, [x2, #0xf]
    // 0x13c9914: StoreField: r2->field_13 = r0
    //     0x13c9914: stur            w0, [x2, #0x13]
    // 0x13c9918: ldur            x0, [fp, #-8]
    // 0x13c991c: LoadField: r1 = r0->field_f
    //     0x13c991c: ldur            w1, [x0, #0xf]
    // 0x13c9920: DecompressPointer r1
    //     0x13c9920: add             x1, x1, HEAP, lsl #32
    // 0x13c9924: r0 = controller()
    //     0x13c9924: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9928: LoadField: r1 = r0->field_4b
    //     0x13c9928: ldur            w1, [x0, #0x4b]
    // 0x13c992c: DecompressPointer r1
    //     0x13c992c: add             x1, x1, HEAP, lsl #32
    // 0x13c9930: r0 = value()
    //     0x13c9930: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9934: LoadField: r1 = r0->field_b
    //     0x13c9934: ldur            w1, [x0, #0xb]
    // 0x13c9938: DecompressPointer r1
    //     0x13c9938: add             x1, x1, HEAP, lsl #32
    // 0x13c993c: cmp             w1, NULL
    // 0x13c9940: b.ne            #0x13c994c
    // 0x13c9944: r0 = Null
    //     0x13c9944: mov             x0, NULL
    // 0x13c9948: b               #0x13c9970
    // 0x13c994c: LoadField: r0 = r1->field_f
    //     0x13c994c: ldur            w0, [x1, #0xf]
    // 0x13c9950: DecompressPointer r0
    //     0x13c9950: add             x0, x0, HEAP, lsl #32
    // 0x13c9954: cmp             w0, NULL
    // 0x13c9958: b.ne            #0x13c9964
    // 0x13c995c: r0 = Null
    //     0x13c995c: mov             x0, NULL
    // 0x13c9960: b               #0x13c9970
    // 0x13c9964: LoadField: r1 = r0->field_7
    //     0x13c9964: ldur            w1, [x0, #7]
    // 0x13c9968: DecompressPointer r1
    //     0x13c9968: add             x1, x1, HEAP, lsl #32
    // 0x13c996c: mov             x0, x1
    // 0x13c9970: cmp             w0, NULL
    // 0x13c9974: b.ne            #0x13c9980
    // 0x13c9978: r4 = ""
    //     0x13c9978: ldr             x4, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c997c: b               #0x13c9984
    // 0x13c9980: mov             x4, x0
    // 0x13c9984: ldur            x3, [fp, #-8]
    // 0x13c9988: ldur            x0, [fp, #-0x18]
    // 0x13c998c: stur            x4, [fp, #-0x10]
    // 0x13c9990: r1 = Function '<anonymous closure>':.
    //     0x13c9990: add             x1, PP, #0x46, lsl #12  ; [pp+0x464b8] AnonymousClosure: (0x9baf68), in [package:customer_app/app/presentation/views/line/product_detail/widgets/reviews_list_widget.dart] ReviewListWidget::body (0x15093c4)
    //     0x13c9994: ldr             x1, [x1, #0x4b8]
    // 0x13c9998: r2 = Null
    //     0x13c9998: mov             x2, NULL
    // 0x13c999c: r0 = AllocateClosure()
    //     0x13c999c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c99a0: r1 = Function '<anonymous closure>':.
    //     0x13c99a0: add             x1, PP, #0x46, lsl #12  ; [pp+0x464c0] AnonymousClosure: (0x900b60), in [package:customer_app/app/presentation/views/line/rating_review/rating_review_media_screen.dart] RatingReviewMediaScreen::body (0x150954c)
    //     0x13c99a4: ldr             x1, [x1, #0x4c0]
    // 0x13c99a8: r2 = Null
    //     0x13c99a8: mov             x2, NULL
    // 0x13c99ac: stur            x0, [fp, #-0x20]
    // 0x13c99b0: r0 = AllocateClosure()
    //     0x13c99b0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13c99b4: stur            x0, [fp, #-0x28]
    // 0x13c99b8: r0 = CachedNetworkImage()
    //     0x13c99b8: bl              #0x859e28  ; AllocateCachedNetworkImageStub -> CachedNetworkImage (size=0x68)
    // 0x13c99bc: stur            x0, [fp, #-0x30]
    // 0x13c99c0: r16 = 60.000000
    //     0x13c99c0: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x13c99c4: ldr             x16, [x16, #0x110]
    // 0x13c99c8: r30 = 60.000000
    //     0x13c99c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f110] 60
    //     0x13c99cc: ldr             lr, [lr, #0x110]
    // 0x13c99d0: stp             lr, x16, [SP, #0x18]
    // 0x13c99d4: r16 = Instance_BoxFit
    //     0x13c99d4: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f118] Obj!BoxFit@d73861
    //     0x13c99d8: ldr             x16, [x16, #0x118]
    // 0x13c99dc: ldur            lr, [fp, #-0x20]
    // 0x13c99e0: stp             lr, x16, [SP, #8]
    // 0x13c99e4: ldur            x16, [fp, #-0x28]
    // 0x13c99e8: str             x16, [SP]
    // 0x13c99ec: mov             x1, x0
    // 0x13c99f0: ldur            x2, [fp, #-0x10]
    // 0x13c99f4: r4 = const [0, 0x7, 0x5, 0x2, errorWidget, 0x6, fit, 0x4, height, 0x2, progressIndicatorBuilder, 0x5, width, 0x3, null]
    //     0x13c99f4: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2fc28] List(15) [0, 0x7, 0x5, 0x2, "errorWidget", 0x6, "fit", 0x4, "height", 0x2, "progressIndicatorBuilder", 0x5, "width", 0x3, Null]
    //     0x13c99f8: ldr             x4, [x4, #0xc28]
    // 0x13c99fc: r0 = CachedNetworkImage()
    //     0x13c99fc: bl              #0x859870  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::CachedNetworkImage
    // 0x13c9a00: r0 = ClipRRect()
    //     0x13c9a00: bl              #0x8fc4f0  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0x13c9a04: mov             x2, x0
    // 0x13c9a08: ldur            x0, [fp, #-0x18]
    // 0x13c9a0c: stur            x2, [fp, #-0x10]
    // 0x13c9a10: StoreField: r2->field_f = r0
    //     0x13c9a10: stur            w0, [x2, #0xf]
    // 0x13c9a14: r0 = Instance_Clip
    //     0x13c9a14: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f138] Obj!Clip@d76fe1
    //     0x13c9a18: ldr             x0, [x0, #0x138]
    // 0x13c9a1c: ArrayStore: r2[0] = r0  ; List_4
    //     0x13c9a1c: stur            w0, [x2, #0x17]
    // 0x13c9a20: ldur            x0, [fp, #-0x30]
    // 0x13c9a24: StoreField: r2->field_b = r0
    //     0x13c9a24: stur            w0, [x2, #0xb]
    // 0x13c9a28: ldur            x0, [fp, #-8]
    // 0x13c9a2c: LoadField: r1 = r0->field_13
    //     0x13c9a2c: ldur            w1, [x0, #0x13]
    // 0x13c9a30: DecompressPointer r1
    //     0x13c9a30: add             x1, x1, HEAP, lsl #32
    // 0x13c9a34: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x13c9a34: ldr             x4, [PP, #0xf0]  ; [pp+0xf0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x13c9a38: r0 = _of()
    //     0x13c9a38: bl              #0x6a9270  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0x13c9a3c: LoadField: r1 = r0->field_7
    //     0x13c9a3c: ldur            w1, [x0, #7]
    // 0x13c9a40: DecompressPointer r1
    //     0x13c9a40: add             x1, x1, HEAP, lsl #32
    // 0x13c9a44: LoadField: d0 = r1->field_7
    //     0x13c9a44: ldur            d0, [x1, #7]
    // 0x13c9a48: d1 = 0.500000
    //     0x13c9a48: fmov            d1, #0.50000000
    // 0x13c9a4c: fmul            d2, d0, d1
    // 0x13c9a50: ldur            x2, [fp, #-8]
    // 0x13c9a54: stur            d2, [fp, #-0x80]
    // 0x13c9a58: LoadField: r1 = r2->field_f
    //     0x13c9a58: ldur            w1, [x2, #0xf]
    // 0x13c9a5c: DecompressPointer r1
    //     0x13c9a5c: add             x1, x1, HEAP, lsl #32
    // 0x13c9a60: r0 = controller()
    //     0x13c9a60: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9a64: LoadField: r1 = r0->field_4b
    //     0x13c9a64: ldur            w1, [x0, #0x4b]
    // 0x13c9a68: DecompressPointer r1
    //     0x13c9a68: add             x1, x1, HEAP, lsl #32
    // 0x13c9a6c: r0 = value()
    //     0x13c9a6c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9a70: LoadField: r1 = r0->field_b
    //     0x13c9a70: ldur            w1, [x0, #0xb]
    // 0x13c9a74: DecompressPointer r1
    //     0x13c9a74: add             x1, x1, HEAP, lsl #32
    // 0x13c9a78: cmp             w1, NULL
    // 0x13c9a7c: b.ne            #0x13c9a88
    // 0x13c9a80: r0 = Null
    //     0x13c9a80: mov             x0, NULL
    // 0x13c9a84: b               #0x13c9aac
    // 0x13c9a88: LoadField: r0 = r1->field_f
    //     0x13c9a88: ldur            w0, [x1, #0xf]
    // 0x13c9a8c: DecompressPointer r0
    //     0x13c9a8c: add             x0, x0, HEAP, lsl #32
    // 0x13c9a90: cmp             w0, NULL
    // 0x13c9a94: b.ne            #0x13c9aa0
    // 0x13c9a98: r0 = Null
    //     0x13c9a98: mov             x0, NULL
    // 0x13c9a9c: b               #0x13c9aac
    // 0x13c9aa0: LoadField: r1 = r0->field_b
    //     0x13c9aa0: ldur            w1, [x0, #0xb]
    // 0x13c9aa4: DecompressPointer r1
    //     0x13c9aa4: add             x1, x1, HEAP, lsl #32
    // 0x13c9aa8: mov             x0, x1
    // 0x13c9aac: cmp             w0, NULL
    // 0x13c9ab0: b.ne            #0x13c9ab8
    // 0x13c9ab4: r0 = ""
    //     0x13c9ab4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c9ab8: ldur            x2, [fp, #-8]
    // 0x13c9abc: stur            x0, [fp, #-0x18]
    // 0x13c9ac0: LoadField: r1 = r2->field_13
    //     0x13c9ac0: ldur            w1, [x2, #0x13]
    // 0x13c9ac4: DecompressPointer r1
    //     0x13c9ac4: add             x1, x1, HEAP, lsl #32
    // 0x13c9ac8: r0 = of()
    //     0x13c9ac8: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c9acc: LoadField: r1 = r0->field_87
    //     0x13c9acc: ldur            w1, [x0, #0x87]
    // 0x13c9ad0: DecompressPointer r1
    //     0x13c9ad0: add             x1, x1, HEAP, lsl #32
    // 0x13c9ad4: LoadField: r0 = r1->field_7
    //     0x13c9ad4: ldur            w0, [x1, #7]
    // 0x13c9ad8: DecompressPointer r0
    //     0x13c9ad8: add             x0, x0, HEAP, lsl #32
    // 0x13c9adc: r16 = Instance_Color
    //     0x13c9adc: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c9ae0: r30 = 12.000000
    //     0x13c9ae0: add             lr, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c9ae4: ldr             lr, [lr, #0x9e8]
    // 0x13c9ae8: stp             lr, x16, [SP]
    // 0x13c9aec: mov             x1, x0
    // 0x13c9af0: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13c9af0: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13c9af4: ldr             x4, [x4, #0x9b8]
    // 0x13c9af8: r0 = copyWith()
    //     0x13c9af8: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c9afc: stur            x0, [fp, #-0x20]
    // 0x13c9b00: r0 = Text()
    //     0x13c9b00: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c9b04: mov             x2, x0
    // 0x13c9b08: ldur            x0, [fp, #-0x18]
    // 0x13c9b0c: stur            x2, [fp, #-0x28]
    // 0x13c9b10: StoreField: r2->field_b = r0
    //     0x13c9b10: stur            w0, [x2, #0xb]
    // 0x13c9b14: ldur            x0, [fp, #-0x20]
    // 0x13c9b18: StoreField: r2->field_13 = r0
    //     0x13c9b18: stur            w0, [x2, #0x13]
    // 0x13c9b1c: r0 = Instance_TextOverflow
    //     0x13c9b1c: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x13c9b20: ldr             x0, [x0, #0xe10]
    // 0x13c9b24: StoreField: r2->field_2b = r0
    //     0x13c9b24: stur            w0, [x2, #0x2b]
    // 0x13c9b28: r0 = 6
    //     0x13c9b28: movz            x0, #0x6
    // 0x13c9b2c: StoreField: r2->field_37 = r0
    //     0x13c9b2c: stur            w0, [x2, #0x37]
    // 0x13c9b30: ldur            x3, [fp, #-8]
    // 0x13c9b34: LoadField: r1 = r3->field_f
    //     0x13c9b34: ldur            w1, [x3, #0xf]
    // 0x13c9b38: DecompressPointer r1
    //     0x13c9b38: add             x1, x1, HEAP, lsl #32
    // 0x13c9b3c: r0 = controller()
    //     0x13c9b3c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9b40: LoadField: r1 = r0->field_4b
    //     0x13c9b40: ldur            w1, [x0, #0x4b]
    // 0x13c9b44: DecompressPointer r1
    //     0x13c9b44: add             x1, x1, HEAP, lsl #32
    // 0x13c9b48: r0 = value()
    //     0x13c9b48: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9b4c: LoadField: r1 = r0->field_b
    //     0x13c9b4c: ldur            w1, [x0, #0xb]
    // 0x13c9b50: DecompressPointer r1
    //     0x13c9b50: add             x1, x1, HEAP, lsl #32
    // 0x13c9b54: cmp             w1, NULL
    // 0x13c9b58: b.ne            #0x13c9b64
    // 0x13c9b5c: r0 = Null
    //     0x13c9b5c: mov             x0, NULL
    // 0x13c9b60: b               #0x13c9b88
    // 0x13c9b64: LoadField: r0 = r1->field_f
    //     0x13c9b64: ldur            w0, [x1, #0xf]
    // 0x13c9b68: DecompressPointer r0
    //     0x13c9b68: add             x0, x0, HEAP, lsl #32
    // 0x13c9b6c: cmp             w0, NULL
    // 0x13c9b70: b.ne            #0x13c9b7c
    // 0x13c9b74: r0 = Null
    //     0x13c9b74: mov             x0, NULL
    // 0x13c9b78: b               #0x13c9b88
    // 0x13c9b7c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13c9b7c: ldur            w1, [x0, #0x17]
    // 0x13c9b80: DecompressPointer r1
    //     0x13c9b80: add             x1, x1, HEAP, lsl #32
    // 0x13c9b84: mov             x0, x1
    // 0x13c9b88: r1 = LoadClassIdInstr(r0)
    //     0x13c9b88: ldur            x1, [x0, #-1]
    //     0x13c9b8c: ubfx            x1, x1, #0xc, #0x14
    // 0x13c9b90: r16 = "size"
    //     0x13c9b90: add             x16, PP, #0xe, lsl #12  ; [pp+0xe9c0] "size"
    //     0x13c9b94: ldr             x16, [x16, #0x9c0]
    // 0x13c9b98: stp             x16, x0, [SP]
    // 0x13c9b9c: mov             x0, x1
    // 0x13c9ba0: mov             lr, x0
    // 0x13c9ba4: ldr             lr, [x21, lr, lsl #3]
    // 0x13c9ba8: blr             lr
    // 0x13c9bac: tbnz            w0, #4, #0x13c9d88
    // 0x13c9bb0: ldur            x0, [fp, #-8]
    // 0x13c9bb4: r1 = Null
    //     0x13c9bb4: mov             x1, NULL
    // 0x13c9bb8: r2 = 8
    //     0x13c9bb8: movz            x2, #0x8
    // 0x13c9bbc: r0 = AllocateArray()
    //     0x13c9bbc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c9bc0: stur            x0, [fp, #-0x18]
    // 0x13c9bc4: r16 = "Size: "
    //     0x13c9bc4: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f00] "Size: "
    //     0x13c9bc8: ldr             x16, [x16, #0xf00]
    // 0x13c9bcc: StoreField: r0->field_f = r16
    //     0x13c9bcc: stur            w16, [x0, #0xf]
    // 0x13c9bd0: ldur            x2, [fp, #-8]
    // 0x13c9bd4: LoadField: r1 = r2->field_f
    //     0x13c9bd4: ldur            w1, [x2, #0xf]
    // 0x13c9bd8: DecompressPointer r1
    //     0x13c9bd8: add             x1, x1, HEAP, lsl #32
    // 0x13c9bdc: r0 = controller()
    //     0x13c9bdc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9be0: LoadField: r1 = r0->field_4b
    //     0x13c9be0: ldur            w1, [x0, #0x4b]
    // 0x13c9be4: DecompressPointer r1
    //     0x13c9be4: add             x1, x1, HEAP, lsl #32
    // 0x13c9be8: r0 = value()
    //     0x13c9be8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9bec: LoadField: r1 = r0->field_b
    //     0x13c9bec: ldur            w1, [x0, #0xb]
    // 0x13c9bf0: DecompressPointer r1
    //     0x13c9bf0: add             x1, x1, HEAP, lsl #32
    // 0x13c9bf4: cmp             w1, NULL
    // 0x13c9bf8: b.ne            #0x13c9c04
    // 0x13c9bfc: r0 = Null
    //     0x13c9bfc: mov             x0, NULL
    // 0x13c9c00: b               #0x13c9c28
    // 0x13c9c04: LoadField: r0 = r1->field_f
    //     0x13c9c04: ldur            w0, [x1, #0xf]
    // 0x13c9c08: DecompressPointer r0
    //     0x13c9c08: add             x0, x0, HEAP, lsl #32
    // 0x13c9c0c: cmp             w0, NULL
    // 0x13c9c10: b.ne            #0x13c9c1c
    // 0x13c9c14: r0 = Null
    //     0x13c9c14: mov             x0, NULL
    // 0x13c9c18: b               #0x13c9c28
    // 0x13c9c1c: LoadField: r1 = r0->field_f
    //     0x13c9c1c: ldur            w1, [x0, #0xf]
    // 0x13c9c20: DecompressPointer r1
    //     0x13c9c20: add             x1, x1, HEAP, lsl #32
    // 0x13c9c24: mov             x0, x1
    // 0x13c9c28: cmp             w0, NULL
    // 0x13c9c2c: b.ne            #0x13c9c34
    // 0x13c9c30: r0 = ""
    //     0x13c9c30: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c9c34: ldur            x3, [fp, #-8]
    // 0x13c9c38: ldur            x2, [fp, #-0x18]
    // 0x13c9c3c: mov             x1, x2
    // 0x13c9c40: ArrayStore: r1[1] = r0  ; List_4
    //     0x13c9c40: add             x25, x1, #0x13
    //     0x13c9c44: str             w0, [x25]
    //     0x13c9c48: tbz             w0, #0, #0x13c9c64
    //     0x13c9c4c: ldurb           w16, [x1, #-1]
    //     0x13c9c50: ldurb           w17, [x0, #-1]
    //     0x13c9c54: and             x16, x17, x16, lsr #2
    //     0x13c9c58: tst             x16, HEAP, lsr #32
    //     0x13c9c5c: b.eq            #0x13c9c64
    //     0x13c9c60: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c9c64: r16 = " / Qty: "
    //     0x13c9c64: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13c9c68: ldr             x16, [x16, #0x760]
    // 0x13c9c6c: ArrayStore: r2[0] = r16  ; List_4
    //     0x13c9c6c: stur            w16, [x2, #0x17]
    // 0x13c9c70: LoadField: r1 = r3->field_f
    //     0x13c9c70: ldur            w1, [x3, #0xf]
    // 0x13c9c74: DecompressPointer r1
    //     0x13c9c74: add             x1, x1, HEAP, lsl #32
    // 0x13c9c78: r0 = controller()
    //     0x13c9c78: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9c7c: LoadField: r1 = r0->field_4b
    //     0x13c9c7c: ldur            w1, [x0, #0x4b]
    // 0x13c9c80: DecompressPointer r1
    //     0x13c9c80: add             x1, x1, HEAP, lsl #32
    // 0x13c9c84: r0 = value()
    //     0x13c9c84: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9c88: LoadField: r1 = r0->field_b
    //     0x13c9c88: ldur            w1, [x0, #0xb]
    // 0x13c9c8c: DecompressPointer r1
    //     0x13c9c8c: add             x1, x1, HEAP, lsl #32
    // 0x13c9c90: cmp             w1, NULL
    // 0x13c9c94: b.ne            #0x13c9ca0
    // 0x13c9c98: r0 = Null
    //     0x13c9c98: mov             x0, NULL
    // 0x13c9c9c: b               #0x13c9cc4
    // 0x13c9ca0: LoadField: r0 = r1->field_f
    //     0x13c9ca0: ldur            w0, [x1, #0xf]
    // 0x13c9ca4: DecompressPointer r0
    //     0x13c9ca4: add             x0, x0, HEAP, lsl #32
    // 0x13c9ca8: cmp             w0, NULL
    // 0x13c9cac: b.ne            #0x13c9cb8
    // 0x13c9cb0: r0 = Null
    //     0x13c9cb0: mov             x0, NULL
    // 0x13c9cb4: b               #0x13c9cc4
    // 0x13c9cb8: LoadField: r1 = r0->field_13
    //     0x13c9cb8: ldur            w1, [x0, #0x13]
    // 0x13c9cbc: DecompressPointer r1
    //     0x13c9cbc: add             x1, x1, HEAP, lsl #32
    // 0x13c9cc0: mov             x0, x1
    // 0x13c9cc4: cmp             w0, NULL
    // 0x13c9cc8: b.ne            #0x13c9cd0
    // 0x13c9ccc: r0 = ""
    //     0x13c9ccc: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c9cd0: ldur            x2, [fp, #-8]
    // 0x13c9cd4: ldur            x1, [fp, #-0x18]
    // 0x13c9cd8: ArrayStore: r1[3] = r0  ; List_4
    //     0x13c9cd8: add             x25, x1, #0x1b
    //     0x13c9cdc: str             w0, [x25]
    //     0x13c9ce0: tbz             w0, #0, #0x13c9cfc
    //     0x13c9ce4: ldurb           w16, [x1, #-1]
    //     0x13c9ce8: ldurb           w17, [x0, #-1]
    //     0x13c9cec: and             x16, x17, x16, lsr #2
    //     0x13c9cf0: tst             x16, HEAP, lsr #32
    //     0x13c9cf4: b.eq            #0x13c9cfc
    //     0x13c9cf8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c9cfc: ldur            x16, [fp, #-0x18]
    // 0x13c9d00: str             x16, [SP]
    // 0x13c9d04: r0 = _interpolate()
    //     0x13c9d04: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13c9d08: ldur            x2, [fp, #-8]
    // 0x13c9d0c: stur            x0, [fp, #-0x18]
    // 0x13c9d10: LoadField: r1 = r2->field_13
    //     0x13c9d10: ldur            w1, [x2, #0x13]
    // 0x13c9d14: DecompressPointer r1
    //     0x13c9d14: add             x1, x1, HEAP, lsl #32
    // 0x13c9d18: r0 = of()
    //     0x13c9d18: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c9d1c: LoadField: r1 = r0->field_87
    //     0x13c9d1c: ldur            w1, [x0, #0x87]
    // 0x13c9d20: DecompressPointer r1
    //     0x13c9d20: add             x1, x1, HEAP, lsl #32
    // 0x13c9d24: LoadField: r0 = r1->field_33
    //     0x13c9d24: ldur            w0, [x1, #0x33]
    // 0x13c9d28: DecompressPointer r0
    //     0x13c9d28: add             x0, x0, HEAP, lsl #32
    // 0x13c9d2c: cmp             w0, NULL
    // 0x13c9d30: b.ne            #0x13c9d3c
    // 0x13c9d34: r1 = Null
    //     0x13c9d34: mov             x1, NULL
    // 0x13c9d38: b               #0x13c9d60
    // 0x13c9d3c: r16 = 12.000000
    //     0x13c9d3c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c9d40: ldr             x16, [x16, #0x9e8]
    // 0x13c9d44: r30 = Instance_Color
    //     0x13c9d44: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c9d48: stp             lr, x16, [SP]
    // 0x13c9d4c: mov             x1, x0
    // 0x13c9d50: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13c9d50: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13c9d54: ldr             x4, [x4, #0xaa0]
    // 0x13c9d58: r0 = copyWith()
    //     0x13c9d58: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c9d5c: mov             x1, x0
    // 0x13c9d60: ldur            x0, [fp, #-0x18]
    // 0x13c9d64: stur            x1, [fp, #-0x20]
    // 0x13c9d68: r0 = Text()
    //     0x13c9d68: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c9d6c: mov             x1, x0
    // 0x13c9d70: ldur            x0, [fp, #-0x18]
    // 0x13c9d74: StoreField: r1->field_b = r0
    //     0x13c9d74: stur            w0, [x1, #0xb]
    // 0x13c9d78: ldur            x0, [fp, #-0x20]
    // 0x13c9d7c: StoreField: r1->field_13 = r0
    //     0x13c9d7c: stur            w0, [x1, #0x13]
    // 0x13c9d80: mov             x0, x1
    // 0x13c9d84: b               #0x13c9f5c
    // 0x13c9d88: ldur            x0, [fp, #-8]
    // 0x13c9d8c: r1 = Null
    //     0x13c9d8c: mov             x1, NULL
    // 0x13c9d90: r2 = 8
    //     0x13c9d90: movz            x2, #0x8
    // 0x13c9d94: r0 = AllocateArray()
    //     0x13c9d94: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13c9d98: stur            x0, [fp, #-0x18]
    // 0x13c9d9c: r16 = "Variant: "
    //     0x13c9d9c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f08] "Variant: "
    //     0x13c9da0: ldr             x16, [x16, #0xf08]
    // 0x13c9da4: StoreField: r0->field_f = r16
    //     0x13c9da4: stur            w16, [x0, #0xf]
    // 0x13c9da8: ldur            x2, [fp, #-8]
    // 0x13c9dac: LoadField: r1 = r2->field_f
    //     0x13c9dac: ldur            w1, [x2, #0xf]
    // 0x13c9db0: DecompressPointer r1
    //     0x13c9db0: add             x1, x1, HEAP, lsl #32
    // 0x13c9db4: r0 = controller()
    //     0x13c9db4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9db8: LoadField: r1 = r0->field_4b
    //     0x13c9db8: ldur            w1, [x0, #0x4b]
    // 0x13c9dbc: DecompressPointer r1
    //     0x13c9dbc: add             x1, x1, HEAP, lsl #32
    // 0x13c9dc0: r0 = value()
    //     0x13c9dc0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9dc4: LoadField: r1 = r0->field_b
    //     0x13c9dc4: ldur            w1, [x0, #0xb]
    // 0x13c9dc8: DecompressPointer r1
    //     0x13c9dc8: add             x1, x1, HEAP, lsl #32
    // 0x13c9dcc: cmp             w1, NULL
    // 0x13c9dd0: b.ne            #0x13c9ddc
    // 0x13c9dd4: r0 = Null
    //     0x13c9dd4: mov             x0, NULL
    // 0x13c9dd8: b               #0x13c9e00
    // 0x13c9ddc: LoadField: r0 = r1->field_f
    //     0x13c9ddc: ldur            w0, [x1, #0xf]
    // 0x13c9de0: DecompressPointer r0
    //     0x13c9de0: add             x0, x0, HEAP, lsl #32
    // 0x13c9de4: cmp             w0, NULL
    // 0x13c9de8: b.ne            #0x13c9df4
    // 0x13c9dec: r0 = Null
    //     0x13c9dec: mov             x0, NULL
    // 0x13c9df0: b               #0x13c9e00
    // 0x13c9df4: LoadField: r1 = r0->field_f
    //     0x13c9df4: ldur            w1, [x0, #0xf]
    // 0x13c9df8: DecompressPointer r1
    //     0x13c9df8: add             x1, x1, HEAP, lsl #32
    // 0x13c9dfc: mov             x0, x1
    // 0x13c9e00: cmp             w0, NULL
    // 0x13c9e04: b.ne            #0x13c9e0c
    // 0x13c9e08: r0 = ""
    //     0x13c9e08: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c9e0c: ldur            x3, [fp, #-8]
    // 0x13c9e10: ldur            x2, [fp, #-0x18]
    // 0x13c9e14: mov             x1, x2
    // 0x13c9e18: ArrayStore: r1[1] = r0  ; List_4
    //     0x13c9e18: add             x25, x1, #0x13
    //     0x13c9e1c: str             w0, [x25]
    //     0x13c9e20: tbz             w0, #0, #0x13c9e3c
    //     0x13c9e24: ldurb           w16, [x1, #-1]
    //     0x13c9e28: ldurb           w17, [x0, #-1]
    //     0x13c9e2c: and             x16, x17, x16, lsr #2
    //     0x13c9e30: tst             x16, HEAP, lsr #32
    //     0x13c9e34: b.eq            #0x13c9e3c
    //     0x13c9e38: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c9e3c: r16 = " / Qty: "
    //     0x13c9e3c: add             x16, PP, #0x33, lsl #12  ; [pp+0x33760] " / Qty: "
    //     0x13c9e40: ldr             x16, [x16, #0x760]
    // 0x13c9e44: ArrayStore: r2[0] = r16  ; List_4
    //     0x13c9e44: stur            w16, [x2, #0x17]
    // 0x13c9e48: LoadField: r1 = r3->field_f
    //     0x13c9e48: ldur            w1, [x3, #0xf]
    // 0x13c9e4c: DecompressPointer r1
    //     0x13c9e4c: add             x1, x1, HEAP, lsl #32
    // 0x13c9e50: r0 = controller()
    //     0x13c9e50: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9e54: LoadField: r1 = r0->field_4b
    //     0x13c9e54: ldur            w1, [x0, #0x4b]
    // 0x13c9e58: DecompressPointer r1
    //     0x13c9e58: add             x1, x1, HEAP, lsl #32
    // 0x13c9e5c: r0 = value()
    //     0x13c9e5c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9e60: LoadField: r1 = r0->field_b
    //     0x13c9e60: ldur            w1, [x0, #0xb]
    // 0x13c9e64: DecompressPointer r1
    //     0x13c9e64: add             x1, x1, HEAP, lsl #32
    // 0x13c9e68: cmp             w1, NULL
    // 0x13c9e6c: b.ne            #0x13c9e78
    // 0x13c9e70: r0 = Null
    //     0x13c9e70: mov             x0, NULL
    // 0x13c9e74: b               #0x13c9e9c
    // 0x13c9e78: LoadField: r0 = r1->field_f
    //     0x13c9e78: ldur            w0, [x1, #0xf]
    // 0x13c9e7c: DecompressPointer r0
    //     0x13c9e7c: add             x0, x0, HEAP, lsl #32
    // 0x13c9e80: cmp             w0, NULL
    // 0x13c9e84: b.ne            #0x13c9e90
    // 0x13c9e88: r0 = Null
    //     0x13c9e88: mov             x0, NULL
    // 0x13c9e8c: b               #0x13c9e9c
    // 0x13c9e90: LoadField: r1 = r0->field_13
    //     0x13c9e90: ldur            w1, [x0, #0x13]
    // 0x13c9e94: DecompressPointer r1
    //     0x13c9e94: add             x1, x1, HEAP, lsl #32
    // 0x13c9e98: mov             x0, x1
    // 0x13c9e9c: cmp             w0, NULL
    // 0x13c9ea0: b.ne            #0x13c9ea8
    // 0x13c9ea4: r0 = ""
    //     0x13c9ea4: ldr             x0, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c9ea8: ldur            x2, [fp, #-8]
    // 0x13c9eac: ldur            x1, [fp, #-0x18]
    // 0x13c9eb0: ArrayStore: r1[3] = r0  ; List_4
    //     0x13c9eb0: add             x25, x1, #0x1b
    //     0x13c9eb4: str             w0, [x25]
    //     0x13c9eb8: tbz             w0, #0, #0x13c9ed4
    //     0x13c9ebc: ldurb           w16, [x1, #-1]
    //     0x13c9ec0: ldurb           w17, [x0, #-1]
    //     0x13c9ec4: and             x16, x17, x16, lsr #2
    //     0x13c9ec8: tst             x16, HEAP, lsr #32
    //     0x13c9ecc: b.eq            #0x13c9ed4
    //     0x13c9ed0: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13c9ed4: ldur            x16, [fp, #-0x18]
    // 0x13c9ed8: str             x16, [SP]
    // 0x13c9edc: r0 = _interpolate()
    //     0x13c9edc: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13c9ee0: ldur            x2, [fp, #-8]
    // 0x13c9ee4: stur            x0, [fp, #-0x18]
    // 0x13c9ee8: LoadField: r1 = r2->field_13
    //     0x13c9ee8: ldur            w1, [x2, #0x13]
    // 0x13c9eec: DecompressPointer r1
    //     0x13c9eec: add             x1, x1, HEAP, lsl #32
    // 0x13c9ef0: r0 = of()
    //     0x13c9ef0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13c9ef4: LoadField: r1 = r0->field_87
    //     0x13c9ef4: ldur            w1, [x0, #0x87]
    // 0x13c9ef8: DecompressPointer r1
    //     0x13c9ef8: add             x1, x1, HEAP, lsl #32
    // 0x13c9efc: LoadField: r0 = r1->field_33
    //     0x13c9efc: ldur            w0, [x1, #0x33]
    // 0x13c9f00: DecompressPointer r0
    //     0x13c9f00: add             x0, x0, HEAP, lsl #32
    // 0x13c9f04: cmp             w0, NULL
    // 0x13c9f08: b.ne            #0x13c9f14
    // 0x13c9f0c: r1 = Null
    //     0x13c9f0c: mov             x1, NULL
    // 0x13c9f10: b               #0x13c9f38
    // 0x13c9f14: r16 = 12.000000
    //     0x13c9f14: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13c9f18: ldr             x16, [x16, #0x9e8]
    // 0x13c9f1c: r30 = Instance_Color
    //     0x13c9f1c: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13c9f20: stp             lr, x16, [SP]
    // 0x13c9f24: mov             x1, x0
    // 0x13c9f28: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13c9f28: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13c9f2c: ldr             x4, [x4, #0xaa0]
    // 0x13c9f30: r0 = copyWith()
    //     0x13c9f30: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13c9f34: mov             x1, x0
    // 0x13c9f38: ldur            x0, [fp, #-0x18]
    // 0x13c9f3c: stur            x1, [fp, #-0x20]
    // 0x13c9f40: r0 = Text()
    //     0x13c9f40: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13c9f44: mov             x1, x0
    // 0x13c9f48: ldur            x0, [fp, #-0x18]
    // 0x13c9f4c: StoreField: r1->field_b = r0
    //     0x13c9f4c: stur            w0, [x1, #0xb]
    // 0x13c9f50: ldur            x0, [fp, #-0x20]
    // 0x13c9f54: StoreField: r1->field_13 = r0
    //     0x13c9f54: stur            w0, [x1, #0x13]
    // 0x13c9f58: mov             x0, x1
    // 0x13c9f5c: ldur            x2, [fp, #-8]
    // 0x13c9f60: stur            x0, [fp, #-0x18]
    // 0x13c9f64: r0 = Padding()
    //     0x13c9f64: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13c9f68: mov             x2, x0
    // 0x13c9f6c: r0 = Instance_EdgeInsets
    //     0x13c9f6c: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x13c9f70: ldr             x0, [x0, #0x770]
    // 0x13c9f74: stur            x2, [fp, #-0x20]
    // 0x13c9f78: StoreField: r2->field_f = r0
    //     0x13c9f78: stur            w0, [x2, #0xf]
    // 0x13c9f7c: ldur            x1, [fp, #-0x18]
    // 0x13c9f80: StoreField: r2->field_b = r1
    //     0x13c9f80: stur            w1, [x2, #0xb]
    // 0x13c9f84: ldur            x3, [fp, #-8]
    // 0x13c9f88: LoadField: r1 = r3->field_f
    //     0x13c9f88: ldur            w1, [x3, #0xf]
    // 0x13c9f8c: DecompressPointer r1
    //     0x13c9f8c: add             x1, x1, HEAP, lsl #32
    // 0x13c9f90: r0 = controller()
    //     0x13c9f90: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13c9f94: LoadField: r1 = r0->field_4b
    //     0x13c9f94: ldur            w1, [x0, #0x4b]
    // 0x13c9f98: DecompressPointer r1
    //     0x13c9f98: add             x1, x1, HEAP, lsl #32
    // 0x13c9f9c: r0 = value()
    //     0x13c9f9c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13c9fa0: LoadField: r1 = r0->field_b
    //     0x13c9fa0: ldur            w1, [x0, #0xb]
    // 0x13c9fa4: DecompressPointer r1
    //     0x13c9fa4: add             x1, x1, HEAP, lsl #32
    // 0x13c9fa8: cmp             w1, NULL
    // 0x13c9fac: b.ne            #0x13c9fb8
    // 0x13c9fb0: r0 = Null
    //     0x13c9fb0: mov             x0, NULL
    // 0x13c9fb4: b               #0x13c9ff0
    // 0x13c9fb8: LoadField: r0 = r1->field_f
    //     0x13c9fb8: ldur            w0, [x1, #0xf]
    // 0x13c9fbc: DecompressPointer r0
    //     0x13c9fbc: add             x0, x0, HEAP, lsl #32
    // 0x13c9fc0: cmp             w0, NULL
    // 0x13c9fc4: b.ne            #0x13c9fd0
    // 0x13c9fc8: r0 = Null
    //     0x13c9fc8: mov             x0, NULL
    // 0x13c9fcc: b               #0x13c9ff0
    // 0x13c9fd0: LoadField: r1 = r0->field_1b
    //     0x13c9fd0: ldur            w1, [x0, #0x1b]
    // 0x13c9fd4: DecompressPointer r1
    //     0x13c9fd4: add             x1, x1, HEAP, lsl #32
    // 0x13c9fd8: cmp             w1, NULL
    // 0x13c9fdc: b.ne            #0x13c9fe8
    // 0x13c9fe0: r0 = Null
    //     0x13c9fe0: mov             x0, NULL
    // 0x13c9fe4: b               #0x13c9ff0
    // 0x13c9fe8: LoadField: r0 = r1->field_7
    //     0x13c9fe8: ldur            w0, [x1, #7]
    // 0x13c9fec: DecompressPointer r0
    //     0x13c9fec: add             x0, x0, HEAP, lsl #32
    // 0x13c9ff0: cmp             w0, NULL
    // 0x13c9ff4: b.ne            #0x13ca000
    // 0x13c9ff8: r5 = ""
    //     0x13c9ff8: ldr             x5, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13c9ffc: b               #0x13ca004
    // 0x13ca000: mov             x5, x0
    // 0x13ca004: ldur            x2, [fp, #-8]
    // 0x13ca008: ldur            x4, [fp, #-0x10]
    // 0x13ca00c: ldur            d0, [fp, #-0x80]
    // 0x13ca010: ldur            x3, [fp, #-0x28]
    // 0x13ca014: ldur            x0, [fp, #-0x20]
    // 0x13ca018: stur            x5, [fp, #-0x18]
    // 0x13ca01c: LoadField: r1 = r2->field_13
    //     0x13ca01c: ldur            w1, [x2, #0x13]
    // 0x13ca020: DecompressPointer r1
    //     0x13ca020: add             x1, x1, HEAP, lsl #32
    // 0x13ca024: r0 = of()
    //     0x13ca024: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ca028: LoadField: r1 = r0->field_87
    //     0x13ca028: ldur            w1, [x0, #0x87]
    // 0x13ca02c: DecompressPointer r1
    //     0x13ca02c: add             x1, x1, HEAP, lsl #32
    // 0x13ca030: LoadField: r0 = r1->field_2b
    //     0x13ca030: ldur            w0, [x1, #0x2b]
    // 0x13ca034: DecompressPointer r0
    //     0x13ca034: add             x0, x0, HEAP, lsl #32
    // 0x13ca038: r16 = 12.000000
    //     0x13ca038: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13ca03c: ldr             x16, [x16, #0x9e8]
    // 0x13ca040: r30 = Instance_Color
    //     0x13ca040: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ca044: stp             lr, x16, [SP]
    // 0x13ca048: mov             x1, x0
    // 0x13ca04c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13ca04c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13ca050: ldr             x4, [x4, #0xaa0]
    // 0x13ca054: r0 = copyWith()
    //     0x13ca054: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ca058: stur            x0, [fp, #-0x30]
    // 0x13ca05c: r0 = Text()
    //     0x13ca05c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13ca060: mov             x1, x0
    // 0x13ca064: ldur            x0, [fp, #-0x18]
    // 0x13ca068: stur            x1, [fp, #-0x38]
    // 0x13ca06c: StoreField: r1->field_b = r0
    //     0x13ca06c: stur            w0, [x1, #0xb]
    // 0x13ca070: ldur            x0, [fp, #-0x30]
    // 0x13ca074: StoreField: r1->field_13 = r0
    //     0x13ca074: stur            w0, [x1, #0x13]
    // 0x13ca078: r0 = Padding()
    //     0x13ca078: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ca07c: mov             x3, x0
    // 0x13ca080: r0 = Instance_EdgeInsets
    //     0x13ca080: add             x0, PP, #0x33, lsl #12  ; [pp+0x33770] Obj!EdgeInsets@d57381
    //     0x13ca084: ldr             x0, [x0, #0x770]
    // 0x13ca088: stur            x3, [fp, #-0x18]
    // 0x13ca08c: StoreField: r3->field_f = r0
    //     0x13ca08c: stur            w0, [x3, #0xf]
    // 0x13ca090: ldur            x0, [fp, #-0x38]
    // 0x13ca094: StoreField: r3->field_b = r0
    //     0x13ca094: stur            w0, [x3, #0xb]
    // 0x13ca098: r1 = Null
    //     0x13ca098: mov             x1, NULL
    // 0x13ca09c: r2 = 6
    //     0x13ca09c: movz            x2, #0x6
    // 0x13ca0a0: r0 = AllocateArray()
    //     0x13ca0a0: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ca0a4: mov             x2, x0
    // 0x13ca0a8: ldur            x0, [fp, #-0x28]
    // 0x13ca0ac: stur            x2, [fp, #-0x30]
    // 0x13ca0b0: StoreField: r2->field_f = r0
    //     0x13ca0b0: stur            w0, [x2, #0xf]
    // 0x13ca0b4: ldur            x0, [fp, #-0x20]
    // 0x13ca0b8: StoreField: r2->field_13 = r0
    //     0x13ca0b8: stur            w0, [x2, #0x13]
    // 0x13ca0bc: ldur            x0, [fp, #-0x18]
    // 0x13ca0c0: ArrayStore: r2[0] = r0  ; List_4
    //     0x13ca0c0: stur            w0, [x2, #0x17]
    // 0x13ca0c4: r1 = <Widget>
    //     0x13ca0c4: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13ca0c8: r0 = AllocateGrowableArray()
    //     0x13ca0c8: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ca0cc: mov             x1, x0
    // 0x13ca0d0: ldur            x0, [fp, #-0x30]
    // 0x13ca0d4: stur            x1, [fp, #-0x18]
    // 0x13ca0d8: StoreField: r1->field_f = r0
    //     0x13ca0d8: stur            w0, [x1, #0xf]
    // 0x13ca0dc: r2 = 6
    //     0x13ca0dc: movz            x2, #0x6
    // 0x13ca0e0: StoreField: r1->field_b = r2
    //     0x13ca0e0: stur            w2, [x1, #0xb]
    // 0x13ca0e4: r0 = Column()
    //     0x13ca0e4: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13ca0e8: mov             x1, x0
    // 0x13ca0ec: r0 = Instance_Axis
    //     0x13ca0ec: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13ca0f0: stur            x1, [fp, #-0x20]
    // 0x13ca0f4: StoreField: r1->field_f = r0
    //     0x13ca0f4: stur            w0, [x1, #0xf]
    // 0x13ca0f8: r2 = Instance_MainAxisAlignment
    //     0x13ca0f8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13ca0fc: ldr             x2, [x2, #0xa08]
    // 0x13ca100: StoreField: r1->field_13 = r2
    //     0x13ca100: stur            w2, [x1, #0x13]
    // 0x13ca104: r3 = Instance_MainAxisSize
    //     0x13ca104: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2fdd0] Obj!MainAxisSize@d73521
    //     0x13ca108: ldr             x3, [x3, #0xdd0]
    // 0x13ca10c: ArrayStore: r1[0] = r3  ; List_4
    //     0x13ca10c: stur            w3, [x1, #0x17]
    // 0x13ca110: r3 = Instance_CrossAxisAlignment
    //     0x13ca110: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13ca114: ldr             x3, [x3, #0x890]
    // 0x13ca118: StoreField: r1->field_1b = r3
    //     0x13ca118: stur            w3, [x1, #0x1b]
    // 0x13ca11c: r4 = Instance_VerticalDirection
    //     0x13ca11c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13ca120: ldr             x4, [x4, #0xa20]
    // 0x13ca124: StoreField: r1->field_23 = r4
    //     0x13ca124: stur            w4, [x1, #0x23]
    // 0x13ca128: r5 = Instance_Clip
    //     0x13ca128: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13ca12c: ldr             x5, [x5, #0x38]
    // 0x13ca130: StoreField: r1->field_2b = r5
    //     0x13ca130: stur            w5, [x1, #0x2b]
    // 0x13ca134: StoreField: r1->field_2f = rZR
    //     0x13ca134: stur            xzr, [x1, #0x2f]
    // 0x13ca138: ldur            x6, [fp, #-0x18]
    // 0x13ca13c: StoreField: r1->field_b = r6
    //     0x13ca13c: stur            w6, [x1, #0xb]
    // 0x13ca140: ldur            d0, [fp, #-0x80]
    // 0x13ca144: r6 = inline_Allocate_Double()
    //     0x13ca144: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0x13ca148: add             x6, x6, #0x10
    //     0x13ca14c: cmp             x7, x6
    //     0x13ca150: b.ls            #0x13cae60
    //     0x13ca154: str             x6, [THR, #0x50]  ; THR::top
    //     0x13ca158: sub             x6, x6, #0xf
    //     0x13ca15c: movz            x7, #0xe15c
    //     0x13ca160: movk            x7, #0x3, lsl #16
    //     0x13ca164: stur            x7, [x6, #-1]
    // 0x13ca168: StoreField: r6->field_7 = d0
    //     0x13ca168: stur            d0, [x6, #7]
    // 0x13ca16c: stur            x6, [fp, #-0x18]
    // 0x13ca170: r0 = SizedBox()
    //     0x13ca170: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13ca174: mov             x3, x0
    // 0x13ca178: ldur            x0, [fp, #-0x18]
    // 0x13ca17c: stur            x3, [fp, #-0x28]
    // 0x13ca180: StoreField: r3->field_f = r0
    //     0x13ca180: stur            w0, [x3, #0xf]
    // 0x13ca184: ldur            x0, [fp, #-0x20]
    // 0x13ca188: StoreField: r3->field_b = r0
    //     0x13ca188: stur            w0, [x3, #0xb]
    // 0x13ca18c: r1 = Null
    //     0x13ca18c: mov             x1, NULL
    // 0x13ca190: r2 = 6
    //     0x13ca190: movz            x2, #0x6
    // 0x13ca194: r0 = AllocateArray()
    //     0x13ca194: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ca198: mov             x2, x0
    // 0x13ca19c: ldur            x0, [fp, #-0x10]
    // 0x13ca1a0: stur            x2, [fp, #-0x18]
    // 0x13ca1a4: StoreField: r2->field_f = r0
    //     0x13ca1a4: stur            w0, [x2, #0xf]
    // 0x13ca1a8: r16 = Instance_SizedBox
    //     0x13ca1a8: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fb20] Obj!SizedBox@d67dc1
    //     0x13ca1ac: ldr             x16, [x16, #0xb20]
    // 0x13ca1b0: StoreField: r2->field_13 = r16
    //     0x13ca1b0: stur            w16, [x2, #0x13]
    // 0x13ca1b4: ldur            x0, [fp, #-0x28]
    // 0x13ca1b8: ArrayStore: r2[0] = r0  ; List_4
    //     0x13ca1b8: stur            w0, [x2, #0x17]
    // 0x13ca1bc: r1 = <Widget>
    //     0x13ca1bc: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13ca1c0: r0 = AllocateGrowableArray()
    //     0x13ca1c0: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ca1c4: mov             x1, x0
    // 0x13ca1c8: ldur            x0, [fp, #-0x18]
    // 0x13ca1cc: stur            x1, [fp, #-0x10]
    // 0x13ca1d0: StoreField: r1->field_f = r0
    //     0x13ca1d0: stur            w0, [x1, #0xf]
    // 0x13ca1d4: r2 = 6
    //     0x13ca1d4: movz            x2, #0x6
    // 0x13ca1d8: StoreField: r1->field_b = r2
    //     0x13ca1d8: stur            w2, [x1, #0xb]
    // 0x13ca1dc: r0 = Row()
    //     0x13ca1dc: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13ca1e0: mov             x1, x0
    // 0x13ca1e4: r0 = Instance_Axis
    //     0x13ca1e4: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13ca1e8: stur            x1, [fp, #-0x18]
    // 0x13ca1ec: StoreField: r1->field_f = r0
    //     0x13ca1ec: stur            w0, [x1, #0xf]
    // 0x13ca1f0: r2 = Instance_MainAxisAlignment
    //     0x13ca1f0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13ca1f4: ldr             x2, [x2, #0xa08]
    // 0x13ca1f8: StoreField: r1->field_13 = r2
    //     0x13ca1f8: stur            w2, [x1, #0x13]
    // 0x13ca1fc: r3 = Instance_MainAxisSize
    //     0x13ca1fc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13ca200: ldr             x3, [x3, #0xa10]
    // 0x13ca204: ArrayStore: r1[0] = r3  ; List_4
    //     0x13ca204: stur            w3, [x1, #0x17]
    // 0x13ca208: r4 = Instance_CrossAxisAlignment
    //     0x13ca208: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13ca20c: ldr             x4, [x4, #0xa18]
    // 0x13ca210: StoreField: r1->field_1b = r4
    //     0x13ca210: stur            w4, [x1, #0x1b]
    // 0x13ca214: r5 = Instance_VerticalDirection
    //     0x13ca214: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13ca218: ldr             x5, [x5, #0xa20]
    // 0x13ca21c: StoreField: r1->field_23 = r5
    //     0x13ca21c: stur            w5, [x1, #0x23]
    // 0x13ca220: r6 = Instance_Clip
    //     0x13ca220: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13ca224: ldr             x6, [x6, #0x38]
    // 0x13ca228: StoreField: r1->field_2b = r6
    //     0x13ca228: stur            w6, [x1, #0x2b]
    // 0x13ca22c: StoreField: r1->field_2f = rZR
    //     0x13ca22c: stur            xzr, [x1, #0x2f]
    // 0x13ca230: ldur            x7, [fp, #-0x10]
    // 0x13ca234: StoreField: r1->field_b = r7
    //     0x13ca234: stur            w7, [x1, #0xb]
    // 0x13ca238: r0 = Padding()
    //     0x13ca238: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ca23c: mov             x1, x0
    // 0x13ca240: r0 = Instance_EdgeInsets
    //     0x13ca240: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f980] Obj!EdgeInsets@d56de1
    //     0x13ca244: ldr             x0, [x0, #0x980]
    // 0x13ca248: stur            x1, [fp, #-0x10]
    // 0x13ca24c: StoreField: r1->field_f = r0
    //     0x13ca24c: stur            w0, [x1, #0xf]
    // 0x13ca250: ldur            x0, [fp, #-0x18]
    // 0x13ca254: StoreField: r1->field_b = r0
    //     0x13ca254: stur            w0, [x1, #0xb]
    // 0x13ca258: r0 = Padding()
    //     0x13ca258: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ca25c: mov             x2, x0
    // 0x13ca260: r0 = Instance_EdgeInsets
    //     0x13ca260: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x13ca264: ldr             x0, [x0, #0x668]
    // 0x13ca268: stur            x2, [fp, #-0x18]
    // 0x13ca26c: StoreField: r2->field_f = r0
    //     0x13ca26c: stur            w0, [x2, #0xf]
    // 0x13ca270: ldur            x1, [fp, #-0x10]
    // 0x13ca274: StoreField: r2->field_b = r1
    //     0x13ca274: stur            w1, [x2, #0xb]
    // 0x13ca278: r1 = Instance_Color
    //     0x13ca278: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ca27c: d0 = 0.100000
    //     0x13ca27c: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13ca280: r0 = withOpacity()
    //     0x13ca280: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13ca284: stur            x0, [fp, #-0x10]
    // 0x13ca288: r0 = Divider()
    //     0x13ca288: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x13ca28c: mov             x1, x0
    // 0x13ca290: ldur            x0, [fp, #-0x10]
    // 0x13ca294: stur            x1, [fp, #-0x20]
    // 0x13ca298: StoreField: r1->field_1f = r0
    //     0x13ca298: stur            w0, [x1, #0x1f]
    // 0x13ca29c: r0 = Padding()
    //     0x13ca29c: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ca2a0: mov             x2, x0
    // 0x13ca2a4: r0 = Instance_EdgeInsets
    //     0x13ca2a4: add             x0, PP, #0x46, lsl #12  ; [pp+0x464c8] Obj!EdgeInsets@d59cc1
    //     0x13ca2a8: ldr             x0, [x0, #0x4c8]
    // 0x13ca2ac: stur            x2, [fp, #-0x10]
    // 0x13ca2b0: StoreField: r2->field_f = r0
    //     0x13ca2b0: stur            w0, [x2, #0xf]
    // 0x13ca2b4: ldur            x1, [fp, #-0x20]
    // 0x13ca2b8: StoreField: r2->field_b = r1
    //     0x13ca2b8: stur            w1, [x2, #0xb]
    // 0x13ca2bc: ldur            x3, [fp, #-8]
    // 0x13ca2c0: LoadField: r1 = r3->field_f
    //     0x13ca2c0: ldur            w1, [x3, #0xf]
    // 0x13ca2c4: DecompressPointer r1
    //     0x13ca2c4: add             x1, x1, HEAP, lsl #32
    // 0x13ca2c8: r0 = controller()
    //     0x13ca2c8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ca2cc: LoadField: r1 = r0->field_4b
    //     0x13ca2cc: ldur            w1, [x0, #0x4b]
    // 0x13ca2d0: DecompressPointer r1
    //     0x13ca2d0: add             x1, x1, HEAP, lsl #32
    // 0x13ca2d4: r0 = value()
    //     0x13ca2d4: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ca2d8: LoadField: r1 = r0->field_b
    //     0x13ca2d8: ldur            w1, [x0, #0xb]
    // 0x13ca2dc: DecompressPointer r1
    //     0x13ca2dc: add             x1, x1, HEAP, lsl #32
    // 0x13ca2e0: cmp             w1, NULL
    // 0x13ca2e4: b.ne            #0x13ca2f0
    // 0x13ca2e8: r0 = Null
    //     0x13ca2e8: mov             x0, NULL
    // 0x13ca2ec: b               #0x13ca314
    // 0x13ca2f0: LoadField: r0 = r1->field_2f
    //     0x13ca2f0: ldur            w0, [x1, #0x2f]
    // 0x13ca2f4: DecompressPointer r0
    //     0x13ca2f4: add             x0, x0, HEAP, lsl #32
    // 0x13ca2f8: cmp             w0, NULL
    // 0x13ca2fc: b.ne            #0x13ca308
    // 0x13ca300: r0 = Null
    //     0x13ca300: mov             x0, NULL
    // 0x13ca304: b               #0x13ca314
    // 0x13ca308: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x13ca308: ldur            w1, [x0, #0x17]
    // 0x13ca30c: DecompressPointer r1
    //     0x13ca30c: add             x1, x1, HEAP, lsl #32
    // 0x13ca310: mov             x0, x1
    // 0x13ca314: cmp             w0, NULL
    // 0x13ca318: b.ne            #0x13ca328
    // 0x13ca31c: r1 = "select Size"
    //     0x13ca31c: add             x1, PP, #0x46, lsl #12  ; [pp+0x464d0] "select Size"
    //     0x13ca320: ldr             x1, [x1, #0x4d0]
    // 0x13ca324: b               #0x13ca32c
    // 0x13ca328: mov             x1, x0
    // 0x13ca32c: ldur            x2, [fp, #-8]
    // 0x13ca330: r0 = capitalizeFirstWord()
    //     0x13ca330: bl              #0x8fc348  ; [package:customer_app/app/core/utils/utils.dart] Utils::capitalizeFirstWord
    // 0x13ca334: ldur            x2, [fp, #-8]
    // 0x13ca338: stur            x0, [fp, #-0x20]
    // 0x13ca33c: LoadField: r1 = r2->field_13
    //     0x13ca33c: ldur            w1, [x2, #0x13]
    // 0x13ca340: DecompressPointer r1
    //     0x13ca340: add             x1, x1, HEAP, lsl #32
    // 0x13ca344: r0 = of()
    //     0x13ca344: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ca348: LoadField: r1 = r0->field_87
    //     0x13ca348: ldur            w1, [x0, #0x87]
    // 0x13ca34c: DecompressPointer r1
    //     0x13ca34c: add             x1, x1, HEAP, lsl #32
    // 0x13ca350: LoadField: r0 = r1->field_7
    //     0x13ca350: ldur            w0, [x1, #7]
    // 0x13ca354: DecompressPointer r0
    //     0x13ca354: add             x0, x0, HEAP, lsl #32
    // 0x13ca358: r16 = 14.000000
    //     0x13ca358: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13ca35c: ldr             x16, [x16, #0x1d8]
    // 0x13ca360: r30 = Instance_Color
    //     0x13ca360: ldr             lr, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ca364: stp             lr, x16, [SP]
    // 0x13ca368: mov             x1, x0
    // 0x13ca36c: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13ca36c: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13ca370: ldr             x4, [x4, #0xaa0]
    // 0x13ca374: r0 = copyWith()
    //     0x13ca374: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ca378: stur            x0, [fp, #-0x28]
    // 0x13ca37c: r0 = Text()
    //     0x13ca37c: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13ca380: mov             x2, x0
    // 0x13ca384: ldur            x0, [fp, #-0x20]
    // 0x13ca388: stur            x2, [fp, #-0x30]
    // 0x13ca38c: StoreField: r2->field_b = r0
    //     0x13ca38c: stur            w0, [x2, #0xb]
    // 0x13ca390: ldur            x0, [fp, #-0x28]
    // 0x13ca394: StoreField: r2->field_13 = r0
    //     0x13ca394: stur            w0, [x2, #0x13]
    // 0x13ca398: ldur            x0, [fp, #-8]
    // 0x13ca39c: LoadField: r1 = r0->field_f
    //     0x13ca39c: ldur            w1, [x0, #0xf]
    // 0x13ca3a0: DecompressPointer r1
    //     0x13ca3a0: add             x1, x1, HEAP, lsl #32
    // 0x13ca3a4: r0 = controller()
    //     0x13ca3a4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ca3a8: LoadField: r1 = r0->field_4b
    //     0x13ca3a8: ldur            w1, [x0, #0x4b]
    // 0x13ca3ac: DecompressPointer r1
    //     0x13ca3ac: add             x1, x1, HEAP, lsl #32
    // 0x13ca3b0: r0 = value()
    //     0x13ca3b0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ca3b4: LoadField: r1 = r0->field_b
    //     0x13ca3b4: ldur            w1, [x0, #0xb]
    // 0x13ca3b8: DecompressPointer r1
    //     0x13ca3b8: add             x1, x1, HEAP, lsl #32
    // 0x13ca3bc: cmp             w1, NULL
    // 0x13ca3c0: b.ne            #0x13ca3cc
    // 0x13ca3c4: r0 = Null
    //     0x13ca3c4: mov             x0, NULL
    // 0x13ca3c8: b               #0x13ca414
    // 0x13ca3cc: LoadField: r0 = r1->field_2f
    //     0x13ca3cc: ldur            w0, [x1, #0x2f]
    // 0x13ca3d0: DecompressPointer r0
    //     0x13ca3d0: add             x0, x0, HEAP, lsl #32
    // 0x13ca3d4: cmp             w0, NULL
    // 0x13ca3d8: b.ne            #0x13ca3e4
    // 0x13ca3dc: r0 = Null
    //     0x13ca3dc: mov             x0, NULL
    // 0x13ca3e0: b               #0x13ca414
    // 0x13ca3e4: LoadField: r1 = r0->field_f
    //     0x13ca3e4: ldur            w1, [x0, #0xf]
    // 0x13ca3e8: DecompressPointer r1
    //     0x13ca3e8: add             x1, x1, HEAP, lsl #32
    // 0x13ca3ec: cmp             w1, NULL
    // 0x13ca3f0: b.ne            #0x13ca3fc
    // 0x13ca3f4: r0 = Null
    //     0x13ca3f4: mov             x0, NULL
    // 0x13ca3f8: b               #0x13ca414
    // 0x13ca3fc: LoadField: r0 = r1->field_7
    //     0x13ca3fc: ldur            w0, [x1, #7]
    // 0x13ca400: cbnz            w0, #0x13ca40c
    // 0x13ca404: r1 = false
    //     0x13ca404: add             x1, NULL, #0x30  ; false
    // 0x13ca408: b               #0x13ca410
    // 0x13ca40c: r1 = true
    //     0x13ca40c: add             x1, NULL, #0x20  ; true
    // 0x13ca410: mov             x0, x1
    // 0x13ca414: cmp             w0, NULL
    // 0x13ca418: b.ne            #0x13ca424
    // 0x13ca41c: r3 = false
    //     0x13ca41c: add             x3, NULL, #0x30  ; false
    // 0x13ca420: b               #0x13ca428
    // 0x13ca424: mov             x3, x0
    // 0x13ca428: ldur            x2, [fp, #-8]
    // 0x13ca42c: ldur            x0, [fp, #-0x30]
    // 0x13ca430: stur            x3, [fp, #-0x20]
    // 0x13ca434: LoadField: r1 = r2->field_13
    //     0x13ca434: ldur            w1, [x2, #0x13]
    // 0x13ca438: DecompressPointer r1
    //     0x13ca438: add             x1, x1, HEAP, lsl #32
    // 0x13ca43c: r0 = of()
    //     0x13ca43c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ca440: LoadField: r1 = r0->field_87
    //     0x13ca440: ldur            w1, [x0, #0x87]
    // 0x13ca444: DecompressPointer r1
    //     0x13ca444: add             x1, x1, HEAP, lsl #32
    // 0x13ca448: LoadField: r0 = r1->field_7
    //     0x13ca448: ldur            w0, [x1, #7]
    // 0x13ca44c: DecompressPointer r0
    //     0x13ca44c: add             x0, x0, HEAP, lsl #32
    // 0x13ca450: r16 = Instance_Color
    //     0x13ca450: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f858] Obj!Color@d6ace1
    //     0x13ca454: ldr             x16, [x16, #0x858]
    // 0x13ca458: r30 = 14.000000
    //     0x13ca458: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13ca45c: ldr             lr, [lr, #0x1d8]
    // 0x13ca460: stp             lr, x16, [SP]
    // 0x13ca464: mov             x1, x0
    // 0x13ca468: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13ca468: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13ca46c: ldr             x4, [x4, #0x9b8]
    // 0x13ca470: r0 = copyWith()
    //     0x13ca470: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ca474: stur            x0, [fp, #-0x28]
    // 0x13ca478: r0 = Text()
    //     0x13ca478: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13ca47c: mov             x1, x0
    // 0x13ca480: r0 = "VIEW CHART"
    //     0x13ca480: add             x0, PP, #0x46, lsl #12  ; [pp+0x464d8] "VIEW CHART"
    //     0x13ca484: ldr             x0, [x0, #0x4d8]
    // 0x13ca488: stur            x1, [fp, #-0x38]
    // 0x13ca48c: StoreField: r1->field_b = r0
    //     0x13ca48c: stur            w0, [x1, #0xb]
    // 0x13ca490: ldur            x0, [fp, #-0x28]
    // 0x13ca494: StoreField: r1->field_13 = r0
    //     0x13ca494: stur            w0, [x1, #0x13]
    // 0x13ca498: r0 = InkWell()
    //     0x13ca498: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x13ca49c: mov             x3, x0
    // 0x13ca4a0: ldur            x0, [fp, #-0x38]
    // 0x13ca4a4: stur            x3, [fp, #-0x28]
    // 0x13ca4a8: StoreField: r3->field_b = r0
    //     0x13ca4a8: stur            w0, [x3, #0xb]
    // 0x13ca4ac: ldur            x2, [fp, #-8]
    // 0x13ca4b0: r1 = Function '<anonymous closure>':.
    //     0x13ca4b0: add             x1, PP, #0x46, lsl #12  ; [pp+0x464e0] AnonymousClosure: (0x13cd4a8), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x13c9858)
    //     0x13ca4b4: ldr             x1, [x1, #0x4e0]
    // 0x13ca4b8: r0 = AllocateClosure()
    //     0x13ca4b8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ca4bc: mov             x1, x0
    // 0x13ca4c0: ldur            x0, [fp, #-0x28]
    // 0x13ca4c4: StoreField: r0->field_f = r1
    //     0x13ca4c4: stur            w1, [x0, #0xf]
    // 0x13ca4c8: r1 = true
    //     0x13ca4c8: add             x1, NULL, #0x20  ; true
    // 0x13ca4cc: StoreField: r0->field_43 = r1
    //     0x13ca4cc: stur            w1, [x0, #0x43]
    // 0x13ca4d0: r2 = Instance_BoxShape
    //     0x13ca4d0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x13ca4d4: ldr             x2, [x2, #0x80]
    // 0x13ca4d8: StoreField: r0->field_47 = r2
    //     0x13ca4d8: stur            w2, [x0, #0x47]
    // 0x13ca4dc: StoreField: r0->field_6f = r1
    //     0x13ca4dc: stur            w1, [x0, #0x6f]
    // 0x13ca4e0: r2 = false
    //     0x13ca4e0: add             x2, NULL, #0x30  ; false
    // 0x13ca4e4: StoreField: r0->field_73 = r2
    //     0x13ca4e4: stur            w2, [x0, #0x73]
    // 0x13ca4e8: StoreField: r0->field_83 = r1
    //     0x13ca4e8: stur            w1, [x0, #0x83]
    // 0x13ca4ec: StoreField: r0->field_7b = r2
    //     0x13ca4ec: stur            w2, [x0, #0x7b]
    // 0x13ca4f0: r0 = Center()
    //     0x13ca4f0: bl              #0x8433cc  ; AllocateCenterStub -> Center (size=0x1c)
    // 0x13ca4f4: mov             x1, x0
    // 0x13ca4f8: r0 = Instance_Alignment
    //     0x13ca4f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x13ca4fc: ldr             x0, [x0, #0xb10]
    // 0x13ca500: stur            x1, [fp, #-0x38]
    // 0x13ca504: StoreField: r1->field_f = r0
    //     0x13ca504: stur            w0, [x1, #0xf]
    // 0x13ca508: ldur            x0, [fp, #-0x28]
    // 0x13ca50c: StoreField: r1->field_b = r0
    //     0x13ca50c: stur            w0, [x1, #0xb]
    // 0x13ca510: r0 = Visibility()
    //     0x13ca510: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13ca514: mov             x3, x0
    // 0x13ca518: ldur            x0, [fp, #-0x38]
    // 0x13ca51c: stur            x3, [fp, #-0x28]
    // 0x13ca520: StoreField: r3->field_b = r0
    //     0x13ca520: stur            w0, [x3, #0xb]
    // 0x13ca524: r0 = Instance_SizedBox
    //     0x13ca524: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13ca528: StoreField: r3->field_f = r0
    //     0x13ca528: stur            w0, [x3, #0xf]
    // 0x13ca52c: ldur            x1, [fp, #-0x20]
    // 0x13ca530: StoreField: r3->field_13 = r1
    //     0x13ca530: stur            w1, [x3, #0x13]
    // 0x13ca534: r4 = false
    //     0x13ca534: add             x4, NULL, #0x30  ; false
    // 0x13ca538: ArrayStore: r3[0] = r4  ; List_4
    //     0x13ca538: stur            w4, [x3, #0x17]
    // 0x13ca53c: StoreField: r3->field_1b = r4
    //     0x13ca53c: stur            w4, [x3, #0x1b]
    // 0x13ca540: StoreField: r3->field_1f = r4
    //     0x13ca540: stur            w4, [x3, #0x1f]
    // 0x13ca544: StoreField: r3->field_23 = r4
    //     0x13ca544: stur            w4, [x3, #0x23]
    // 0x13ca548: StoreField: r3->field_27 = r4
    //     0x13ca548: stur            w4, [x3, #0x27]
    // 0x13ca54c: StoreField: r3->field_2b = r4
    //     0x13ca54c: stur            w4, [x3, #0x2b]
    // 0x13ca550: r1 = Null
    //     0x13ca550: mov             x1, NULL
    // 0x13ca554: r2 = 6
    //     0x13ca554: movz            x2, #0x6
    // 0x13ca558: r0 = AllocateArray()
    //     0x13ca558: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ca55c: mov             x2, x0
    // 0x13ca560: ldur            x0, [fp, #-0x30]
    // 0x13ca564: stur            x2, [fp, #-0x20]
    // 0x13ca568: StoreField: r2->field_f = r0
    //     0x13ca568: stur            w0, [x2, #0xf]
    // 0x13ca56c: r16 = Instance_Spacer
    //     0x13ca56c: add             x16, PP, #0x34, lsl #12  ; [pp+0x340f0] Obj!Spacer@d65c11
    //     0x13ca570: ldr             x16, [x16, #0xf0]
    // 0x13ca574: StoreField: r2->field_13 = r16
    //     0x13ca574: stur            w16, [x2, #0x13]
    // 0x13ca578: ldur            x0, [fp, #-0x28]
    // 0x13ca57c: ArrayStore: r2[0] = r0  ; List_4
    //     0x13ca57c: stur            w0, [x2, #0x17]
    // 0x13ca580: r1 = <Widget>
    //     0x13ca580: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13ca584: r0 = AllocateGrowableArray()
    //     0x13ca584: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ca588: mov             x1, x0
    // 0x13ca58c: ldur            x0, [fp, #-0x20]
    // 0x13ca590: stur            x1, [fp, #-0x28]
    // 0x13ca594: StoreField: r1->field_f = r0
    //     0x13ca594: stur            w0, [x1, #0xf]
    // 0x13ca598: r0 = 6
    //     0x13ca598: movz            x0, #0x6
    // 0x13ca59c: StoreField: r1->field_b = r0
    //     0x13ca59c: stur            w0, [x1, #0xb]
    // 0x13ca5a0: r0 = Row()
    //     0x13ca5a0: bl              #0x8fc33c  ; AllocateRowStub -> Row (size=0x38)
    // 0x13ca5a4: mov             x1, x0
    // 0x13ca5a8: r0 = Instance_Axis
    //     0x13ca5a8: ldr             x0, [PP, #0x4520]  ; [pp+0x4520] Obj!Axis@d73a41
    // 0x13ca5ac: stur            x1, [fp, #-0x20]
    // 0x13ca5b0: StoreField: r1->field_f = r0
    //     0x13ca5b0: stur            w0, [x1, #0xf]
    // 0x13ca5b4: r0 = Instance_MainAxisAlignment
    //     0x13ca5b4: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13ca5b8: ldr             x0, [x0, #0xa08]
    // 0x13ca5bc: StoreField: r1->field_13 = r0
    //     0x13ca5bc: stur            w0, [x1, #0x13]
    // 0x13ca5c0: r2 = Instance_MainAxisSize
    //     0x13ca5c0: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13ca5c4: ldr             x2, [x2, #0xa10]
    // 0x13ca5c8: ArrayStore: r1[0] = r2  ; List_4
    //     0x13ca5c8: stur            w2, [x1, #0x17]
    // 0x13ca5cc: r3 = Instance_CrossAxisAlignment
    //     0x13ca5cc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea18] Obj!CrossAxisAlignment@d73401
    //     0x13ca5d0: ldr             x3, [x3, #0xa18]
    // 0x13ca5d4: StoreField: r1->field_1b = r3
    //     0x13ca5d4: stur            w3, [x1, #0x1b]
    // 0x13ca5d8: r3 = Instance_VerticalDirection
    //     0x13ca5d8: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13ca5dc: ldr             x3, [x3, #0xa20]
    // 0x13ca5e0: StoreField: r1->field_23 = r3
    //     0x13ca5e0: stur            w3, [x1, #0x23]
    // 0x13ca5e4: r4 = Instance_Clip
    //     0x13ca5e4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13ca5e8: ldr             x4, [x4, #0x38]
    // 0x13ca5ec: StoreField: r1->field_2b = r4
    //     0x13ca5ec: stur            w4, [x1, #0x2b]
    // 0x13ca5f0: StoreField: r1->field_2f = rZR
    //     0x13ca5f0: stur            xzr, [x1, #0x2f]
    // 0x13ca5f4: ldur            x5, [fp, #-0x28]
    // 0x13ca5f8: StoreField: r1->field_b = r5
    //     0x13ca5f8: stur            w5, [x1, #0xb]
    // 0x13ca5fc: r0 = Padding()
    //     0x13ca5fc: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ca600: mov             x2, x0
    // 0x13ca604: r0 = Instance_EdgeInsets
    //     0x13ca604: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x13ca608: ldr             x0, [x0, #0x668]
    // 0x13ca60c: stur            x2, [fp, #-0x28]
    // 0x13ca610: StoreField: r2->field_f = r0
    //     0x13ca610: stur            w0, [x2, #0xf]
    // 0x13ca614: ldur            x1, [fp, #-0x20]
    // 0x13ca618: StoreField: r2->field_b = r1
    //     0x13ca618: stur            w1, [x2, #0xb]
    // 0x13ca61c: ldur            x3, [fp, #-8]
    // 0x13ca620: LoadField: r1 = r3->field_f
    //     0x13ca620: ldur            w1, [x3, #0xf]
    // 0x13ca624: DecompressPointer r1
    //     0x13ca624: add             x1, x1, HEAP, lsl #32
    // 0x13ca628: r0 = controller()
    //     0x13ca628: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ca62c: LoadField: r1 = r0->field_4b
    //     0x13ca62c: ldur            w1, [x0, #0x4b]
    // 0x13ca630: DecompressPointer r1
    //     0x13ca630: add             x1, x1, HEAP, lsl #32
    // 0x13ca634: r0 = value()
    //     0x13ca634: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ca638: LoadField: r1 = r0->field_b
    //     0x13ca638: ldur            w1, [x0, #0xb]
    // 0x13ca63c: DecompressPointer r1
    //     0x13ca63c: add             x1, x1, HEAP, lsl #32
    // 0x13ca640: cmp             w1, NULL
    // 0x13ca644: b.ne            #0x13ca650
    // 0x13ca648: r5 = Null
    //     0x13ca648: mov             x5, NULL
    // 0x13ca64c: b               #0x13ca678
    // 0x13ca650: LoadField: r0 = r1->field_2f
    //     0x13ca650: ldur            w0, [x1, #0x2f]
    // 0x13ca654: DecompressPointer r0
    //     0x13ca654: add             x0, x0, HEAP, lsl #32
    // 0x13ca658: cmp             w0, NULL
    // 0x13ca65c: b.ne            #0x13ca668
    // 0x13ca660: r0 = Null
    //     0x13ca660: mov             x0, NULL
    // 0x13ca664: b               #0x13ca674
    // 0x13ca668: LoadField: r1 = r0->field_b
    //     0x13ca668: ldur            w1, [x0, #0xb]
    // 0x13ca66c: DecompressPointer r1
    //     0x13ca66c: add             x1, x1, HEAP, lsl #32
    // 0x13ca670: LoadField: r0 = r1->field_b
    //     0x13ca670: ldur            w0, [x1, #0xb]
    // 0x13ca674: mov             x5, x0
    // 0x13ca678: ldur            x0, [fp, #-8]
    // 0x13ca67c: mov             x2, x0
    // 0x13ca680: stur            x5, [fp, #-0x20]
    // 0x13ca684: r1 = Function '<anonymous closure>':.
    //     0x13ca684: add             x1, PP, #0x46, lsl #12  ; [pp+0x464e8] AnonymousClosure: (0x13cb008), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x13c9858)
    //     0x13ca688: ldr             x1, [x1, #0x4e8]
    // 0x13ca68c: r0 = AllocateClosure()
    //     0x13ca68c: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13ca690: stur            x0, [fp, #-0x30]
    // 0x13ca694: r0 = GridView()
    //     0x13ca694: bl              #0x9010e0  ; AllocateGridViewStub -> GridView (size=0x60)
    // 0x13ca698: mov             x1, x0
    // 0x13ca69c: ldur            x3, [fp, #-0x30]
    // 0x13ca6a0: ldur            x5, [fp, #-0x20]
    // 0x13ca6a4: r2 = Instance_SliverGridDelegateWithFixedCrossAxisCount
    //     0x13ca6a4: add             x2, PP, #0x38, lsl #12  ; [pp+0x380b8] Obj!SliverGridDelegateWithFixedCrossAxisCount@d564e1
    //     0x13ca6a8: ldr             x2, [x2, #0xb8]
    // 0x13ca6ac: stur            x0, [fp, #-0x20]
    // 0x13ca6b0: r0 = GridView.builder()
    //     0x13ca6b0: bl              #0x900fc8  ; [package:flutter/src/widgets/scroll_view.dart] GridView::GridView.builder
    // 0x13ca6b4: r0 = SizedBox()
    //     0x13ca6b4: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13ca6b8: mov             x1, x0
    // 0x13ca6bc: r0 = 100.000000
    //     0x13ca6bc: ldr             x0, [PP, #0x5b28]  ; [pp+0x5b28] 100
    // 0x13ca6c0: stur            x1, [fp, #-0x30]
    // 0x13ca6c4: StoreField: r1->field_13 = r0
    //     0x13ca6c4: stur            w0, [x1, #0x13]
    // 0x13ca6c8: ldur            x0, [fp, #-0x20]
    // 0x13ca6cc: StoreField: r1->field_b = r0
    //     0x13ca6cc: stur            w0, [x1, #0xb]
    // 0x13ca6d0: r0 = Padding()
    //     0x13ca6d0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ca6d4: mov             x2, x0
    // 0x13ca6d8: r0 = Instance_EdgeInsets
    //     0x13ca6d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe68] Obj!EdgeInsets@d57c21
    //     0x13ca6dc: ldr             x0, [x0, #0xe68]
    // 0x13ca6e0: stur            x2, [fp, #-0x20]
    // 0x13ca6e4: StoreField: r2->field_f = r0
    //     0x13ca6e4: stur            w0, [x2, #0xf]
    // 0x13ca6e8: ldur            x0, [fp, #-0x30]
    // 0x13ca6ec: StoreField: r2->field_b = r0
    //     0x13ca6ec: stur            w0, [x2, #0xb]
    // 0x13ca6f0: r1 = Instance_Color
    //     0x13ca6f0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ca6f4: d0 = 0.100000
    //     0x13ca6f4: ldr             d0, [PP, #0x5ac8]  ; [pp+0x5ac8] IMM: double(0.1) from 0x3fb999999999999a
    // 0x13ca6f8: r0 = withOpacity()
    //     0x13ca6f8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13ca6fc: stur            x0, [fp, #-0x30]
    // 0x13ca700: r0 = Divider()
    //     0x13ca700: bl              #0x8a3d68  ; AllocateDividerStub -> Divider (size=0x24)
    // 0x13ca704: mov             x1, x0
    // 0x13ca708: ldur            x0, [fp, #-0x30]
    // 0x13ca70c: stur            x1, [fp, #-0x38]
    // 0x13ca710: StoreField: r1->field_1f = r0
    //     0x13ca710: stur            w0, [x1, #0x1f]
    // 0x13ca714: r0 = Padding()
    //     0x13ca714: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13ca718: mov             x2, x0
    // 0x13ca71c: r0 = Instance_EdgeInsets
    //     0x13ca71c: add             x0, PP, #0x46, lsl #12  ; [pp+0x464c8] Obj!EdgeInsets@d59cc1
    //     0x13ca720: ldr             x0, [x0, #0x4c8]
    // 0x13ca724: stur            x2, [fp, #-0x30]
    // 0x13ca728: StoreField: r2->field_f = r0
    //     0x13ca728: stur            w0, [x2, #0xf]
    // 0x13ca72c: ldur            x0, [fp, #-0x38]
    // 0x13ca730: StoreField: r2->field_b = r0
    //     0x13ca730: stur            w0, [x2, #0xb]
    // 0x13ca734: ldur            x0, [fp, #-8]
    // 0x13ca738: LoadField: r1 = r0->field_f
    //     0x13ca738: ldur            w1, [x0, #0xf]
    // 0x13ca73c: DecompressPointer r1
    //     0x13ca73c: add             x1, x1, HEAP, lsl #32
    // 0x13ca740: r0 = controller()
    //     0x13ca740: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ca744: LoadField: r1 = r0->field_4b
    //     0x13ca744: ldur            w1, [x0, #0x4b]
    // 0x13ca748: DecompressPointer r1
    //     0x13ca748: add             x1, x1, HEAP, lsl #32
    // 0x13ca74c: r0 = value()
    //     0x13ca74c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13ca750: LoadField: r1 = r0->field_b
    //     0x13ca750: ldur            w1, [x0, #0xb]
    // 0x13ca754: DecompressPointer r1
    //     0x13ca754: add             x1, x1, HEAP, lsl #32
    // 0x13ca758: cmp             w1, NULL
    // 0x13ca75c: b.ne            #0x13ca768
    // 0x13ca760: r0 = Null
    //     0x13ca760: mov             x0, NULL
    // 0x13ca764: b               #0x13ca7a0
    // 0x13ca768: LoadField: r0 = r1->field_2f
    //     0x13ca768: ldur            w0, [x1, #0x2f]
    // 0x13ca76c: DecompressPointer r0
    //     0x13ca76c: add             x0, x0, HEAP, lsl #32
    // 0x13ca770: cmp             w0, NULL
    // 0x13ca774: b.ne            #0x13ca780
    // 0x13ca778: r0 = Null
    //     0x13ca778: mov             x0, NULL
    // 0x13ca77c: b               #0x13ca7a0
    // 0x13ca780: LoadField: r1 = r0->field_13
    //     0x13ca780: ldur            w1, [x0, #0x13]
    // 0x13ca784: DecompressPointer r1
    //     0x13ca784: add             x1, x1, HEAP, lsl #32
    // 0x13ca788: LoadField: r0 = r1->field_b
    //     0x13ca788: ldur            w0, [x1, #0xb]
    // 0x13ca78c: cbnz            w0, #0x13ca798
    // 0x13ca790: r1 = false
    //     0x13ca790: add             x1, NULL, #0x30  ; false
    // 0x13ca794: b               #0x13ca79c
    // 0x13ca798: r1 = true
    //     0x13ca798: add             x1, NULL, #0x20  ; true
    // 0x13ca79c: mov             x0, x1
    // 0x13ca7a0: cmp             w0, NULL
    // 0x13ca7a4: b.ne            #0x13ca7ac
    // 0x13ca7a8: r0 = false
    //     0x13ca7a8: add             x0, NULL, #0x30  ; false
    // 0x13ca7ac: ldur            x2, [fp, #-8]
    // 0x13ca7b0: stur            x0, [fp, #-0x38]
    // 0x13ca7b4: LoadField: r1 = r2->field_13
    //     0x13ca7b4: ldur            w1, [x2, #0x13]
    // 0x13ca7b8: DecompressPointer r1
    //     0x13ca7b8: add             x1, x1, HEAP, lsl #32
    // 0x13ca7bc: r0 = of()
    //     0x13ca7bc: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ca7c0: LoadField: r1 = r0->field_87
    //     0x13ca7c0: ldur            w1, [x0, #0x87]
    // 0x13ca7c4: DecompressPointer r1
    //     0x13ca7c4: add             x1, x1, HEAP, lsl #32
    // 0x13ca7c8: LoadField: r0 = r1->field_b
    //     0x13ca7c8: ldur            w0, [x1, #0xb]
    // 0x13ca7cc: DecompressPointer r0
    //     0x13ca7cc: add             x0, x0, HEAP, lsl #32
    // 0x13ca7d0: cmp             w0, NULL
    // 0x13ca7d4: b.ne            #0x13ca7e0
    // 0x13ca7d8: r0 = Null
    //     0x13ca7d8: mov             x0, NULL
    // 0x13ca7dc: b               #0x13ca800
    // 0x13ca7e0: r16 = Instance_Color
    //     0x13ca7e0: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ca7e4: r30 = 14.000000
    //     0x13ca7e4: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13ca7e8: ldr             lr, [lr, #0x1d8]
    // 0x13ca7ec: stp             lr, x16, [SP]
    // 0x13ca7f0: mov             x1, x0
    // 0x13ca7f4: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13ca7f4: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13ca7f8: ldr             x4, [x4, #0x9b8]
    // 0x13ca7fc: r0 = copyWith()
    //     0x13ca7fc: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ca800: ldur            x2, [fp, #-8]
    // 0x13ca804: stur            x0, [fp, #-0x40]
    // 0x13ca808: r0 = TextSpan()
    //     0x13ca808: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13ca80c: mov             x3, x0
    // 0x13ca810: r0 = "Size with different prices"
    //     0x13ca810: add             x0, PP, #0x38, lsl #12  ; [pp+0x380d0] "Size with different prices"
    //     0x13ca814: ldr             x0, [x0, #0xd0]
    // 0x13ca818: stur            x3, [fp, #-0x48]
    // 0x13ca81c: StoreField: r3->field_b = r0
    //     0x13ca81c: stur            w0, [x3, #0xb]
    // 0x13ca820: r0 = Instance__DeferringMouseCursor
    //     0x13ca820: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13ca824: ArrayStore: r3[0] = r0  ; List_4
    //     0x13ca824: stur            w0, [x3, #0x17]
    // 0x13ca828: ldur            x1, [fp, #-0x40]
    // 0x13ca82c: StoreField: r3->field_7 = r1
    //     0x13ca82c: stur            w1, [x3, #7]
    // 0x13ca830: r1 = Null
    //     0x13ca830: mov             x1, NULL
    // 0x13ca834: r2 = 2
    //     0x13ca834: movz            x2, #0x2
    // 0x13ca838: r0 = AllocateArray()
    //     0x13ca838: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ca83c: mov             x2, x0
    // 0x13ca840: ldur            x0, [fp, #-0x48]
    // 0x13ca844: stur            x2, [fp, #-0x40]
    // 0x13ca848: StoreField: r2->field_f = r0
    //     0x13ca848: stur            w0, [x2, #0xf]
    // 0x13ca84c: r1 = <InlineSpan>
    //     0x13ca84c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2fe40] TypeArguments: <InlineSpan>
    //     0x13ca850: ldr             x1, [x1, #0xe40]
    // 0x13ca854: r0 = AllocateGrowableArray()
    //     0x13ca854: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13ca858: mov             x1, x0
    // 0x13ca85c: ldur            x0, [fp, #-0x40]
    // 0x13ca860: stur            x1, [fp, #-0x48]
    // 0x13ca864: StoreField: r1->field_f = r0
    //     0x13ca864: stur            w0, [x1, #0xf]
    // 0x13ca868: r0 = 2
    //     0x13ca868: movz            x0, #0x2
    // 0x13ca86c: StoreField: r1->field_b = r0
    //     0x13ca86c: stur            w0, [x1, #0xb]
    // 0x13ca870: r0 = TextSpan()
    //     0x13ca870: bl              #0x72f080  ; AllocateTextSpanStub -> TextSpan (size=0x34)
    // 0x13ca874: mov             x1, x0
    // 0x13ca878: ldur            x0, [fp, #-0x48]
    // 0x13ca87c: stur            x1, [fp, #-0x40]
    // 0x13ca880: StoreField: r1->field_f = r0
    //     0x13ca880: stur            w0, [x1, #0xf]
    // 0x13ca884: r0 = Instance__DeferringMouseCursor
    //     0x13ca884: ldr             x0, [PP, #0x20f0]  ; [pp+0x20f0] Obj!_DeferringMouseCursor@d645f1
    // 0x13ca888: ArrayStore: r1[0] = r0  ; List_4
    //     0x13ca888: stur            w0, [x1, #0x17]
    // 0x13ca88c: r0 = RichText()
    //     0x13ca88c: bl              #0x82d6c4  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0x13ca890: mov             x1, x0
    // 0x13ca894: ldur            x2, [fp, #-0x40]
    // 0x13ca898: stur            x0, [fp, #-0x40]
    // 0x13ca89c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x13ca89c: ldr             x4, [PP, #0xc8]  ; [pp+0xc8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x13ca8a0: r0 = RichText()
    //     0x13ca8a0: bl              #0x82cc5c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0x13ca8a4: ldur            x2, [fp, #-8]
    // 0x13ca8a8: LoadField: r1 = r2->field_13
    //     0x13ca8a8: ldur            w1, [x2, #0x13]
    // 0x13ca8ac: DecompressPointer r1
    //     0x13ca8ac: add             x1, x1, HEAP, lsl #32
    // 0x13ca8b0: r0 = of()
    //     0x13ca8b0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ca8b4: LoadField: r1 = r0->field_87
    //     0x13ca8b4: ldur            w1, [x0, #0x87]
    // 0x13ca8b8: DecompressPointer r1
    //     0x13ca8b8: add             x1, x1, HEAP, lsl #32
    // 0x13ca8bc: LoadField: r0 = r1->field_2b
    //     0x13ca8bc: ldur            w0, [x1, #0x2b]
    // 0x13ca8c0: DecompressPointer r0
    //     0x13ca8c0: add             x0, x0, HEAP, lsl #32
    // 0x13ca8c4: stur            x0, [fp, #-0x48]
    // 0x13ca8c8: r1 = Instance_Color
    //     0x13ca8c8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ca8cc: d0 = 0.700000
    //     0x13ca8cc: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13ca8d0: ldr             d0, [x17, #0xf48]
    // 0x13ca8d4: r0 = withOpacity()
    //     0x13ca8d4: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13ca8d8: r16 = 12.000000
    //     0x13ca8d8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2e9e8] 12
    //     0x13ca8dc: ldr             x16, [x16, #0x9e8]
    // 0x13ca8e0: stp             x16, x0, [SP]
    // 0x13ca8e4: ldur            x1, [fp, #-0x48]
    // 0x13ca8e8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13ca8e8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13ca8ec: ldr             x4, [x4, #0x9b8]
    // 0x13ca8f0: r0 = copyWith()
    //     0x13ca8f0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ca8f4: stur            x0, [fp, #-0x48]
    // 0x13ca8f8: r0 = Text()
    //     0x13ca8f8: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13ca8fc: mov             x3, x0
    // 0x13ca900: r0 = "Price of some variants/sizes of this product doesn’t match with the previously ordered product. Tap on the below button if you want to exchange with a different priced variant/size."
    //     0x13ca900: add             x0, PP, #0x38, lsl #12  ; [pp+0x380d8] "Price of some variants/sizes of this product doesn’t match with the previously ordered product. Tap on the below button if you want to exchange with a different priced variant/size."
    //     0x13ca904: ldr             x0, [x0, #0xd8]
    // 0x13ca908: stur            x3, [fp, #-0x50]
    // 0x13ca90c: StoreField: r3->field_b = r0
    //     0x13ca90c: stur            w0, [x3, #0xb]
    // 0x13ca910: ldur            x0, [fp, #-0x48]
    // 0x13ca914: StoreField: r3->field_13 = r0
    //     0x13ca914: stur            w0, [x3, #0x13]
    // 0x13ca918: r1 = Null
    //     0x13ca918: mov             x1, NULL
    // 0x13ca91c: r2 = 4
    //     0x13ca91c: movz            x2, #0x4
    // 0x13ca920: r0 = AllocateArray()
    //     0x13ca920: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13ca924: stur            x0, [fp, #-0x48]
    // 0x13ca928: r16 = "Available sizes: "
    //     0x13ca928: add             x16, PP, #0x38, lsl #12  ; [pp+0x380e0] "Available sizes: "
    //     0x13ca92c: ldr             x16, [x16, #0xe0]
    // 0x13ca930: StoreField: r0->field_f = r16
    //     0x13ca930: stur            w16, [x0, #0xf]
    // 0x13ca934: ldur            x2, [fp, #-8]
    // 0x13ca938: LoadField: r1 = r2->field_f
    //     0x13ca938: ldur            w1, [x2, #0xf]
    // 0x13ca93c: DecompressPointer r1
    //     0x13ca93c: add             x1, x1, HEAP, lsl #32
    // 0x13ca940: r0 = controller()
    //     0x13ca940: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13ca944: LoadField: r1 = r0->field_57
    //     0x13ca944: ldur            w1, [x0, #0x57]
    // 0x13ca948: DecompressPointer r1
    //     0x13ca948: add             x1, x1, HEAP, lsl #32
    // 0x13ca94c: r16 = ", "
    //     0x13ca94c: ldr             x16, [PP, #0xe00]  ; [pp+0xe00] ", "
    // 0x13ca950: str             x16, [SP]
    // 0x13ca954: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x13ca954: ldr             x4, [PP, #0xc70]  ; [pp+0xc70] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x13ca958: r0 = join()
    //     0x13ca958: bl              #0x7d6d4c  ; [dart:core] _GrowableList::join
    // 0x13ca95c: ldur            x1, [fp, #-0x48]
    // 0x13ca960: ArrayStore: r1[1] = r0  ; List_4
    //     0x13ca960: add             x25, x1, #0x13
    //     0x13ca964: str             w0, [x25]
    //     0x13ca968: tbz             w0, #0, #0x13ca984
    //     0x13ca96c: ldurb           w16, [x1, #-1]
    //     0x13ca970: ldurb           w17, [x0, #-1]
    //     0x13ca974: and             x16, x17, x16, lsr #2
    //     0x13ca978: tst             x16, HEAP, lsr #32
    //     0x13ca97c: b.eq            #0x13ca984
    //     0x13ca980: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13ca984: ldur            x16, [fp, #-0x48]
    // 0x13ca988: str             x16, [SP]
    // 0x13ca98c: r0 = _interpolate()
    //     0x13ca98c: bl              #0x61db5c  ; [dart:core] _StringBase::_interpolate
    // 0x13ca990: ldur            x2, [fp, #-8]
    // 0x13ca994: stur            x0, [fp, #-0x48]
    // 0x13ca998: LoadField: r1 = r2->field_13
    //     0x13ca998: ldur            w1, [x2, #0x13]
    // 0x13ca99c: DecompressPointer r1
    //     0x13ca99c: add             x1, x1, HEAP, lsl #32
    // 0x13ca9a0: r0 = of()
    //     0x13ca9a0: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13ca9a4: LoadField: r1 = r0->field_87
    //     0x13ca9a4: ldur            w1, [x0, #0x87]
    // 0x13ca9a8: DecompressPointer r1
    //     0x13ca9a8: add             x1, x1, HEAP, lsl #32
    // 0x13ca9ac: LoadField: r0 = r1->field_b
    //     0x13ca9ac: ldur            w0, [x1, #0xb]
    // 0x13ca9b0: DecompressPointer r0
    //     0x13ca9b0: add             x0, x0, HEAP, lsl #32
    // 0x13ca9b4: cmp             w0, NULL
    // 0x13ca9b8: b.ne            #0x13ca9c4
    // 0x13ca9bc: r10 = Null
    //     0x13ca9bc: mov             x10, NULL
    // 0x13ca9c0: b               #0x13ca9e8
    // 0x13ca9c4: r16 = Instance_Color
    //     0x13ca9c4: ldr             x16, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13ca9c8: r30 = 14.000000
    //     0x13ca9c8: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13ca9cc: ldr             lr, [lr, #0x1d8]
    // 0x13ca9d0: stp             lr, x16, [SP]
    // 0x13ca9d4: mov             x1, x0
    // 0x13ca9d8: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13ca9d8: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13ca9dc: ldr             x4, [x4, #0x9b8]
    // 0x13ca9e0: r0 = copyWith()
    //     0x13ca9e0: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13ca9e4: mov             x10, x0
    // 0x13ca9e8: ldur            x2, [fp, #-8]
    // 0x13ca9ec: ldur            x9, [fp, #-0x18]
    // 0x13ca9f0: ldur            x8, [fp, #-0x10]
    // 0x13ca9f4: ldur            x7, [fp, #-0x28]
    // 0x13ca9f8: ldur            x6, [fp, #-0x20]
    // 0x13ca9fc: ldur            x5, [fp, #-0x30]
    // 0x13caa00: ldur            x4, [fp, #-0x38]
    // 0x13caa04: ldur            x3, [fp, #-0x40]
    // 0x13caa08: ldur            x1, [fp, #-0x50]
    // 0x13caa0c: ldur            x0, [fp, #-0x48]
    // 0x13caa10: stur            x10, [fp, #-0x58]
    // 0x13caa14: r0 = Text()
    //     0x13caa14: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13caa18: mov             x1, x0
    // 0x13caa1c: ldur            x0, [fp, #-0x48]
    // 0x13caa20: stur            x1, [fp, #-0x60]
    // 0x13caa24: StoreField: r1->field_b = r0
    //     0x13caa24: stur            w0, [x1, #0xb]
    // 0x13caa28: ldur            x0, [fp, #-0x58]
    // 0x13caa2c: StoreField: r1->field_13 = r0
    //     0x13caa2c: stur            w0, [x1, #0x13]
    // 0x13caa30: r16 = <EdgeInsets>
    //     0x13caa30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13caa34: ldr             x16, [x16, #0xda0]
    // 0x13caa38: r30 = Instance_EdgeInsets
    //     0x13caa38: add             lr, PP, #0x2f, lsl #12  ; [pp+0x2f1f0] Obj!EdgeInsets@d56e71
    //     0x13caa3c: ldr             lr, [lr, #0x1f0]
    // 0x13caa40: stp             lr, x16, [SP]
    // 0x13caa44: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13caa44: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13caa48: r0 = all()
    //     0x13caa48: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13caa4c: ldur            x2, [fp, #-8]
    // 0x13caa50: stur            x0, [fp, #-0x48]
    // 0x13caa54: LoadField: r1 = r2->field_13
    //     0x13caa54: ldur            w1, [x2, #0x13]
    // 0x13caa58: DecompressPointer r1
    //     0x13caa58: add             x1, x1, HEAP, lsl #32
    // 0x13caa5c: r0 = of()
    //     0x13caa5c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13caa60: LoadField: r1 = r0->field_5b
    //     0x13caa60: ldur            w1, [x0, #0x5b]
    // 0x13caa64: DecompressPointer r1
    //     0x13caa64: add             x1, x1, HEAP, lsl #32
    // 0x13caa68: r16 = <Color>
    //     0x13caa68: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13caa6c: ldr             x16, [x16, #0xf80]
    // 0x13caa70: stp             x1, x16, [SP]
    // 0x13caa74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13caa74: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13caa78: r0 = all()
    //     0x13caa78: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13caa7c: ldur            x2, [fp, #-8]
    // 0x13caa80: stur            x0, [fp, #-0x58]
    // 0x13caa84: LoadField: r1 = r2->field_13
    //     0x13caa84: ldur            w1, [x2, #0x13]
    // 0x13caa88: DecompressPointer r1
    //     0x13caa88: add             x1, x1, HEAP, lsl #32
    // 0x13caa8c: r0 = of()
    //     0x13caa8c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13caa90: LoadField: r1 = r0->field_5b
    //     0x13caa90: ldur            w1, [x0, #0x5b]
    // 0x13caa94: DecompressPointer r1
    //     0x13caa94: add             x1, x1, HEAP, lsl #32
    // 0x13caa98: r16 = <Color>
    //     0x13caa98: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13caa9c: ldr             x16, [x16, #0xf80]
    // 0x13caaa0: stp             x1, x16, [SP]
    // 0x13caaa4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13caaa4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13caaa8: r0 = all()
    //     0x13caaa8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13caaac: stur            x0, [fp, #-0x68]
    // 0x13caab0: r16 = <RoundedRectangleBorder>
    //     0x13caab0: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13caab4: ldr             x16, [x16, #0xf78]
    // 0x13caab8: r30 = Instance_RoundedRectangleBorder
    //     0x13caab8: add             lr, PP, #0x43, lsl #12  ; [pp+0x438d0] Obj!RoundedRectangleBorder@d5ac11
    //     0x13caabc: ldr             lr, [lr, #0x8d0]
    // 0x13caac0: stp             lr, x16, [SP]
    // 0x13caac4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13caac4: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13caac8: r0 = all()
    //     0x13caac8: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13caacc: stur            x0, [fp, #-0x70]
    // 0x13caad0: r0 = ButtonStyle()
    //     0x13caad0: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13caad4: mov             x1, x0
    // 0x13caad8: ldur            x0, [fp, #-0x68]
    // 0x13caadc: stur            x1, [fp, #-0x78]
    // 0x13caae0: StoreField: r1->field_b = r0
    //     0x13caae0: stur            w0, [x1, #0xb]
    // 0x13caae4: ldur            x0, [fp, #-0x58]
    // 0x13caae8: StoreField: r1->field_f = r0
    //     0x13caae8: stur            w0, [x1, #0xf]
    // 0x13caaec: ldur            x0, [fp, #-0x48]
    // 0x13caaf0: StoreField: r1->field_23 = r0
    //     0x13caaf0: stur            w0, [x1, #0x23]
    // 0x13caaf4: ldur            x0, [fp, #-0x70]
    // 0x13caaf8: StoreField: r1->field_43 = r0
    //     0x13caaf8: stur            w0, [x1, #0x43]
    // 0x13caafc: r0 = TextButtonThemeData()
    //     0x13caafc: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x13cab00: mov             x2, x0
    // 0x13cab04: ldur            x0, [fp, #-0x78]
    // 0x13cab08: stur            x2, [fp, #-0x48]
    // 0x13cab0c: StoreField: r2->field_7 = r0
    //     0x13cab0c: stur            w0, [x2, #7]
    // 0x13cab10: ldur            x0, [fp, #-8]
    // 0x13cab14: LoadField: r1 = r0->field_13
    //     0x13cab14: ldur            w1, [x0, #0x13]
    // 0x13cab18: DecompressPointer r1
    //     0x13cab18: add             x1, x1, HEAP, lsl #32
    // 0x13cab1c: r0 = of()
    //     0x13cab1c: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cab20: LoadField: r1 = r0->field_87
    //     0x13cab20: ldur            w1, [x0, #0x87]
    // 0x13cab24: DecompressPointer r1
    //     0x13cab24: add             x1, x1, HEAP, lsl #32
    // 0x13cab28: LoadField: r0 = r1->field_7
    //     0x13cab28: ldur            w0, [x1, #7]
    // 0x13cab2c: DecompressPointer r0
    //     0x13cab2c: add             x0, x0, HEAP, lsl #32
    // 0x13cab30: r16 = 14.000000
    //     0x13cab30: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13cab34: ldr             x16, [x16, #0x1d8]
    // 0x13cab38: r30 = Instance_Color
    //     0x13cab38: ldr             lr, [PP, #0x54a0]  ; [pp+0x54a0] Obj!Color@d69d51
    // 0x13cab3c: stp             lr, x16, [SP]
    // 0x13cab40: mov             x1, x0
    // 0x13cab44: r4 = const [0, 0x3, 0x2, 0x1, color, 0x2, fontSize, 0x1, null]
    //     0x13cab44: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2eaa0] List(9) [0, 0x3, 0x2, 0x1, "color", 0x2, "fontSize", 0x1, Null]
    //     0x13cab48: ldr             x4, [x4, #0xaa0]
    // 0x13cab4c: r0 = copyWith()
    //     0x13cab4c: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cab50: stur            x0, [fp, #-0x58]
    // 0x13cab54: r0 = Text()
    //     0x13cab54: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cab58: mov             x3, x0
    // 0x13cab5c: r0 = "Exchange With New Product"
    //     0x13cab5c: add             x0, PP, #0x46, lsl #12  ; [pp+0x464f0] "Exchange With New Product"
    //     0x13cab60: ldr             x0, [x0, #0x4f0]
    // 0x13cab64: stur            x3, [fp, #-0x68]
    // 0x13cab68: StoreField: r3->field_b = r0
    //     0x13cab68: stur            w0, [x3, #0xb]
    // 0x13cab6c: ldur            x0, [fp, #-0x58]
    // 0x13cab70: StoreField: r3->field_13 = r0
    //     0x13cab70: stur            w0, [x3, #0x13]
    // 0x13cab74: ldur            x2, [fp, #-8]
    // 0x13cab78: r1 = Function '<anonymous closure>':.
    //     0x13cab78: add             x1, PP, #0x46, lsl #12  ; [pp+0x464f8] AnonymousClosure: (0x13cae8c), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x13c9858)
    //     0x13cab7c: ldr             x1, [x1, #0x4f8]
    // 0x13cab80: r0 = AllocateClosure()
    //     0x13cab80: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cab84: stur            x0, [fp, #-8]
    // 0x13cab88: r0 = TextButton()
    //     0x13cab88: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13cab8c: mov             x1, x0
    // 0x13cab90: ldur            x0, [fp, #-8]
    // 0x13cab94: stur            x1, [fp, #-0x58]
    // 0x13cab98: StoreField: r1->field_b = r0
    //     0x13cab98: stur            w0, [x1, #0xb]
    // 0x13cab9c: r0 = false
    //     0x13cab9c: add             x0, NULL, #0x30  ; false
    // 0x13caba0: StoreField: r1->field_27 = r0
    //     0x13caba0: stur            w0, [x1, #0x27]
    // 0x13caba4: r2 = true
    //     0x13caba4: add             x2, NULL, #0x20  ; true
    // 0x13caba8: StoreField: r1->field_2f = r2
    //     0x13caba8: stur            w2, [x1, #0x2f]
    // 0x13cabac: ldur            x2, [fp, #-0x68]
    // 0x13cabb0: StoreField: r1->field_37 = r2
    //     0x13cabb0: stur            w2, [x1, #0x37]
    // 0x13cabb4: r0 = TextButtonTheme()
    //     0x13cabb4: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13cabb8: mov             x1, x0
    // 0x13cabbc: ldur            x0, [fp, #-0x48]
    // 0x13cabc0: stur            x1, [fp, #-8]
    // 0x13cabc4: StoreField: r1->field_f = r0
    //     0x13cabc4: stur            w0, [x1, #0xf]
    // 0x13cabc8: ldur            x0, [fp, #-0x58]
    // 0x13cabcc: StoreField: r1->field_b = r0
    //     0x13cabcc: stur            w0, [x1, #0xb]
    // 0x13cabd0: r0 = SizedBox()
    //     0x13cabd0: bl              #0x82da08  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0x13cabd4: mov             x3, x0
    // 0x13cabd8: r0 = inf
    //     0x13cabd8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f9f8] inf
    //     0x13cabdc: ldr             x0, [x0, #0x9f8]
    // 0x13cabe0: stur            x3, [fp, #-0x48]
    // 0x13cabe4: StoreField: r3->field_f = r0
    //     0x13cabe4: stur            w0, [x3, #0xf]
    // 0x13cabe8: r0 = 48.000000
    //     0x13cabe8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fad8] 48
    //     0x13cabec: ldr             x0, [x0, #0xad8]
    // 0x13cabf0: StoreField: r3->field_13 = r0
    //     0x13cabf0: stur            w0, [x3, #0x13]
    // 0x13cabf4: ldur            x0, [fp, #-8]
    // 0x13cabf8: StoreField: r3->field_b = r0
    //     0x13cabf8: stur            w0, [x3, #0xb]
    // 0x13cabfc: r1 = Null
    //     0x13cabfc: mov             x1, NULL
    // 0x13cac00: r2 = 14
    //     0x13cac00: movz            x2, #0xe
    // 0x13cac04: r0 = AllocateArray()
    //     0x13cac04: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cac08: mov             x2, x0
    // 0x13cac0c: ldur            x0, [fp, #-0x40]
    // 0x13cac10: stur            x2, [fp, #-8]
    // 0x13cac14: StoreField: r2->field_f = r0
    //     0x13cac14: stur            w0, [x2, #0xf]
    // 0x13cac18: r16 = Instance_SizedBox
    //     0x13cac18: add             x16, PP, #0x32, lsl #12  ; [pp+0x32c70] Obj!SizedBox@d67ee1
    //     0x13cac1c: ldr             x16, [x16, #0xc70]
    // 0x13cac20: StoreField: r2->field_13 = r16
    //     0x13cac20: stur            w16, [x2, #0x13]
    // 0x13cac24: ldur            x0, [fp, #-0x50]
    // 0x13cac28: ArrayStore: r2[0] = r0  ; List_4
    //     0x13cac28: stur            w0, [x2, #0x17]
    // 0x13cac2c: r16 = Instance_SizedBox
    //     0x13cac2c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x13cac30: ldr             x16, [x16, #0x8f0]
    // 0x13cac34: StoreField: r2->field_1b = r16
    //     0x13cac34: stur            w16, [x2, #0x1b]
    // 0x13cac38: ldur            x0, [fp, #-0x60]
    // 0x13cac3c: StoreField: r2->field_1f = r0
    //     0x13cac3c: stur            w0, [x2, #0x1f]
    // 0x13cac40: r16 = Instance_SizedBox
    //     0x13cac40: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f8f0] Obj!SizedBox@d67d61
    //     0x13cac44: ldr             x16, [x16, #0x8f0]
    // 0x13cac48: StoreField: r2->field_23 = r16
    //     0x13cac48: stur            w16, [x2, #0x23]
    // 0x13cac4c: ldur            x0, [fp, #-0x48]
    // 0x13cac50: StoreField: r2->field_27 = r0
    //     0x13cac50: stur            w0, [x2, #0x27]
    // 0x13cac54: r1 = <Widget>
    //     0x13cac54: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cac58: r0 = AllocateGrowableArray()
    //     0x13cac58: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13cac5c: mov             x1, x0
    // 0x13cac60: ldur            x0, [fp, #-8]
    // 0x13cac64: stur            x1, [fp, #-0x40]
    // 0x13cac68: StoreField: r1->field_f = r0
    //     0x13cac68: stur            w0, [x1, #0xf]
    // 0x13cac6c: r0 = 14
    //     0x13cac6c: movz            x0, #0xe
    // 0x13cac70: StoreField: r1->field_b = r0
    //     0x13cac70: stur            w0, [x1, #0xb]
    // 0x13cac74: r0 = Column()
    //     0x13cac74: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13cac78: mov             x1, x0
    // 0x13cac7c: r0 = Instance_Axis
    //     0x13cac7c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13cac80: stur            x1, [fp, #-8]
    // 0x13cac84: StoreField: r1->field_f = r0
    //     0x13cac84: stur            w0, [x1, #0xf]
    // 0x13cac88: r2 = Instance_MainAxisAlignment
    //     0x13cac88: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13cac8c: ldr             x2, [x2, #0xa08]
    // 0x13cac90: StoreField: r1->field_13 = r2
    //     0x13cac90: stur            w2, [x1, #0x13]
    // 0x13cac94: r3 = Instance_MainAxisSize
    //     0x13cac94: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13cac98: ldr             x3, [x3, #0xa10]
    // 0x13cac9c: ArrayStore: r1[0] = r3  ; List_4
    //     0x13cac9c: stur            w3, [x1, #0x17]
    // 0x13caca0: r4 = Instance_CrossAxisAlignment
    //     0x13caca0: add             x4, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13caca4: ldr             x4, [x4, #0x890]
    // 0x13caca8: StoreField: r1->field_1b = r4
    //     0x13caca8: stur            w4, [x1, #0x1b]
    // 0x13cacac: r5 = Instance_VerticalDirection
    //     0x13cacac: add             x5, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13cacb0: ldr             x5, [x5, #0xa20]
    // 0x13cacb4: StoreField: r1->field_23 = r5
    //     0x13cacb4: stur            w5, [x1, #0x23]
    // 0x13cacb8: r6 = Instance_Clip
    //     0x13cacb8: add             x6, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13cacbc: ldr             x6, [x6, #0x38]
    // 0x13cacc0: StoreField: r1->field_2b = r6
    //     0x13cacc0: stur            w6, [x1, #0x2b]
    // 0x13cacc4: StoreField: r1->field_2f = rZR
    //     0x13cacc4: stur            xzr, [x1, #0x2f]
    // 0x13cacc8: ldur            x7, [fp, #-0x40]
    // 0x13caccc: StoreField: r1->field_b = r7
    //     0x13caccc: stur            w7, [x1, #0xb]
    // 0x13cacd0: r0 = Padding()
    //     0x13cacd0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cacd4: mov             x1, x0
    // 0x13cacd8: r0 = Instance_EdgeInsets
    //     0x13cacd8: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2e668] Obj!EdgeInsets@d56fc1
    //     0x13cacdc: ldr             x0, [x0, #0x668]
    // 0x13cace0: stur            x1, [fp, #-0x40]
    // 0x13cace4: StoreField: r1->field_f = r0
    //     0x13cace4: stur            w0, [x1, #0xf]
    // 0x13cace8: ldur            x0, [fp, #-8]
    // 0x13cacec: StoreField: r1->field_b = r0
    //     0x13cacec: stur            w0, [x1, #0xb]
    // 0x13cacf0: r0 = Visibility()
    //     0x13cacf0: bl              #0x98f638  ; AllocateVisibilityStub -> Visibility (size=0x30)
    // 0x13cacf4: mov             x3, x0
    // 0x13cacf8: ldur            x0, [fp, #-0x40]
    // 0x13cacfc: stur            x3, [fp, #-8]
    // 0x13cad00: StoreField: r3->field_b = r0
    //     0x13cad00: stur            w0, [x3, #0xb]
    // 0x13cad04: r0 = Instance_SizedBox
    //     0x13cad04: ldr             x0, [PP, #0x4b08]  ; [pp+0x4b08] Obj!SizedBox@d67d21
    // 0x13cad08: StoreField: r3->field_f = r0
    //     0x13cad08: stur            w0, [x3, #0xf]
    // 0x13cad0c: ldur            x0, [fp, #-0x38]
    // 0x13cad10: StoreField: r3->field_13 = r0
    //     0x13cad10: stur            w0, [x3, #0x13]
    // 0x13cad14: r0 = false
    //     0x13cad14: add             x0, NULL, #0x30  ; false
    // 0x13cad18: ArrayStore: r3[0] = r0  ; List_4
    //     0x13cad18: stur            w0, [x3, #0x17]
    // 0x13cad1c: StoreField: r3->field_1b = r0
    //     0x13cad1c: stur            w0, [x3, #0x1b]
    // 0x13cad20: StoreField: r3->field_1f = r0
    //     0x13cad20: stur            w0, [x3, #0x1f]
    // 0x13cad24: StoreField: r3->field_23 = r0
    //     0x13cad24: stur            w0, [x3, #0x23]
    // 0x13cad28: StoreField: r3->field_27 = r0
    //     0x13cad28: stur            w0, [x3, #0x27]
    // 0x13cad2c: StoreField: r3->field_2b = r0
    //     0x13cad2c: stur            w0, [x3, #0x2b]
    // 0x13cad30: r1 = Null
    //     0x13cad30: mov             x1, NULL
    // 0x13cad34: r2 = 12
    //     0x13cad34: movz            x2, #0xc
    // 0x13cad38: r0 = AllocateArray()
    //     0x13cad38: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13cad3c: mov             x2, x0
    // 0x13cad40: ldur            x0, [fp, #-0x18]
    // 0x13cad44: stur            x2, [fp, #-0x38]
    // 0x13cad48: StoreField: r2->field_f = r0
    //     0x13cad48: stur            w0, [x2, #0xf]
    // 0x13cad4c: ldur            x0, [fp, #-0x10]
    // 0x13cad50: StoreField: r2->field_13 = r0
    //     0x13cad50: stur            w0, [x2, #0x13]
    // 0x13cad54: ldur            x0, [fp, #-0x28]
    // 0x13cad58: ArrayStore: r2[0] = r0  ; List_4
    //     0x13cad58: stur            w0, [x2, #0x17]
    // 0x13cad5c: ldur            x0, [fp, #-0x20]
    // 0x13cad60: StoreField: r2->field_1b = r0
    //     0x13cad60: stur            w0, [x2, #0x1b]
    // 0x13cad64: ldur            x0, [fp, #-0x30]
    // 0x13cad68: StoreField: r2->field_1f = r0
    //     0x13cad68: stur            w0, [x2, #0x1f]
    // 0x13cad6c: ldur            x0, [fp, #-8]
    // 0x13cad70: StoreField: r2->field_23 = r0
    //     0x13cad70: stur            w0, [x2, #0x23]
    // 0x13cad74: r1 = <Widget>
    //     0x13cad74: ldr             x1, [PP, #0x5030]  ; [pp+0x5030] TypeArguments: <Widget>
    // 0x13cad78: r0 = AllocateGrowableArray()
    //     0x13cad78: bl              #0x16f60cc  ; AllocateGrowableArrayStub
    // 0x13cad7c: mov             x1, x0
    // 0x13cad80: ldur            x0, [fp, #-0x38]
    // 0x13cad84: stur            x1, [fp, #-8]
    // 0x13cad88: StoreField: r1->field_f = r0
    //     0x13cad88: stur            w0, [x1, #0xf]
    // 0x13cad8c: r0 = 12
    //     0x13cad8c: movz            x0, #0xc
    // 0x13cad90: StoreField: r1->field_b = r0
    //     0x13cad90: stur            w0, [x1, #0xb]
    // 0x13cad94: r0 = Column()
    //     0x13cad94: bl              #0x85b6e0  ; AllocateColumnStub -> Column (size=0x38)
    // 0x13cad98: mov             x1, x0
    // 0x13cad9c: r0 = Instance_Axis
    //     0x13cad9c: ldr             x0, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13cada0: stur            x1, [fp, #-0x10]
    // 0x13cada4: StoreField: r1->field_f = r0
    //     0x13cada4: stur            w0, [x1, #0xf]
    // 0x13cada8: r2 = Instance_MainAxisAlignment
    //     0x13cada8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea08] Obj!MainAxisAlignment@d73501
    //     0x13cadac: ldr             x2, [x2, #0xa08]
    // 0x13cadb0: StoreField: r1->field_13 = r2
    //     0x13cadb0: stur            w2, [x1, #0x13]
    // 0x13cadb4: r2 = Instance_MainAxisSize
    //     0x13cadb4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea10] Obj!MainAxisSize@d73541
    //     0x13cadb8: ldr             x2, [x2, #0xa10]
    // 0x13cadbc: ArrayStore: r1[0] = r2  ; List_4
    //     0x13cadbc: stur            w2, [x1, #0x17]
    // 0x13cadc0: r2 = Instance_CrossAxisAlignment
    //     0x13cadc0: add             x2, PP, #0x2f, lsl #12  ; [pp+0x2f890] Obj!CrossAxisAlignment@d73421
    //     0x13cadc4: ldr             x2, [x2, #0x890]
    // 0x13cadc8: StoreField: r1->field_1b = r2
    //     0x13cadc8: stur            w2, [x1, #0x1b]
    // 0x13cadcc: r2 = Instance_VerticalDirection
    //     0x13cadcc: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea20] Obj!VerticalDirection@d73a01
    //     0x13cadd0: ldr             x2, [x2, #0xa20]
    // 0x13cadd4: StoreField: r1->field_23 = r2
    //     0x13cadd4: stur            w2, [x1, #0x23]
    // 0x13cadd8: r2 = Instance_Clip
    //     0x13cadd8: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2e038] Obj!Clip@d76fc1
    //     0x13caddc: ldr             x2, [x2, #0x38]
    // 0x13cade0: StoreField: r1->field_2b = r2
    //     0x13cade0: stur            w2, [x1, #0x2b]
    // 0x13cade4: StoreField: r1->field_2f = rZR
    //     0x13cade4: stur            xzr, [x1, #0x2f]
    // 0x13cade8: ldur            x2, [fp, #-8]
    // 0x13cadec: StoreField: r1->field_b = r2
    //     0x13cadec: stur            w2, [x1, #0xb]
    // 0x13cadf0: r0 = Padding()
    //     0x13cadf0: bl              #0x837294  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0x13cadf4: mov             x1, x0
    // 0x13cadf8: r0 = Instance_EdgeInsets
    //     0x13cadf8: add             x0, PP, #0x42, lsl #12  ; [pp+0x42708] Obj!EdgeInsets@d59cf1
    //     0x13cadfc: ldr             x0, [x0, #0x708]
    // 0x13cae00: stur            x1, [fp, #-8]
    // 0x13cae04: StoreField: r1->field_f = r0
    //     0x13cae04: stur            w0, [x1, #0xf]
    // 0x13cae08: ldur            x0, [fp, #-0x10]
    // 0x13cae0c: StoreField: r1->field_b = r0
    //     0x13cae0c: stur            w0, [x1, #0xb]
    // 0x13cae10: r0 = SingleChildScrollView()
    //     0x13cae10: bl              #0x837d34  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0x13cae14: r1 = Instance_Axis
    //     0x13cae14: ldr             x1, [PP, #0x4518]  ; [pp+0x4518] Obj!Axis@d73a61
    // 0x13cae18: StoreField: r0->field_b = r1
    //     0x13cae18: stur            w1, [x0, #0xb]
    // 0x13cae1c: r1 = false
    //     0x13cae1c: add             x1, NULL, #0x30  ; false
    // 0x13cae20: StoreField: r0->field_f = r1
    //     0x13cae20: stur            w1, [x0, #0xf]
    // 0x13cae24: ldur            x1, [fp, #-8]
    // 0x13cae28: StoreField: r0->field_23 = r1
    //     0x13cae28: stur            w1, [x0, #0x23]
    // 0x13cae2c: r1 = Instance_DragStartBehavior
    //     0x13cae2c: ldr             x1, [PP, #0x6c30]  ; [pp+0x6c30] Obj!DragStartBehavior@d748c1
    // 0x13cae30: StoreField: r0->field_27 = r1
    //     0x13cae30: stur            w1, [x0, #0x27]
    // 0x13cae34: r1 = Instance_Clip
    //     0x13cae34: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d7e0] Obj!Clip@d76fa1
    //     0x13cae38: ldr             x1, [x1, #0x7e0]
    // 0x13cae3c: StoreField: r0->field_2b = r1
    //     0x13cae3c: stur            w1, [x0, #0x2b]
    // 0x13cae40: r1 = Instance_HitTestBehavior
    //     0x13cae40: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e288] Obj!HitTestBehavior@d732e1
    //     0x13cae44: ldr             x1, [x1, #0x288]
    // 0x13cae48: StoreField: r0->field_2f = r1
    //     0x13cae48: stur            w1, [x0, #0x2f]
    // 0x13cae4c: LeaveFrame
    //     0x13cae4c: mov             SP, fp
    //     0x13cae50: ldp             fp, lr, [SP], #0x10
    // 0x13cae54: ret
    //     0x13cae54: ret             
    // 0x13cae58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cae58: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cae5c: b               #0x13c98e4
    // 0x13cae60: SaveReg d0
    //     0x13cae60: str             q0, [SP, #-0x10]!
    // 0x13cae64: stp             x4, x5, [SP, #-0x10]!
    // 0x13cae68: stp             x2, x3, [SP, #-0x10]!
    // 0x13cae6c: stp             x0, x1, [SP, #-0x10]!
    // 0x13cae70: r0 = AllocateDouble()
    //     0x13cae70: bl              #0x16f70f0  ; AllocateDoubleStub
    // 0x13cae74: mov             x6, x0
    // 0x13cae78: ldp             x0, x1, [SP], #0x10
    // 0x13cae7c: ldp             x2, x3, [SP], #0x10
    // 0x13cae80: ldp             x4, x5, [SP], #0x10
    // 0x13cae84: RestoreReg d0
    //     0x13cae84: ldr             q0, [SP], #0x10
    // 0x13cae88: b               #0x13ca168
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13cae8c, size: 0x17c
    // 0x13cae8c: EnterFrame
    //     0x13cae8c: stp             fp, lr, [SP, #-0x10]!
    //     0x13cae90: mov             fp, SP
    // 0x13cae94: AllocStack(0x28)
    //     0x13cae94: sub             SP, SP, #0x28
    // 0x13cae98: SetupParameters()
    //     0x13cae98: ldr             x0, [fp, #0x10]
    //     0x13cae9c: ldur            w2, [x0, #0x17]
    //     0x13caea0: add             x2, x2, HEAP, lsl #32
    //     0x13caea4: stur            x2, [fp, #-8]
    // 0x13caea8: CheckStackOverflow
    //     0x13caea8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13caeac: cmp             SP, x16
    //     0x13caeb0: b.ls            #0x13cb000
    // 0x13caeb4: LoadField: r1 = r2->field_f
    //     0x13caeb4: ldur            w1, [x2, #0xf]
    // 0x13caeb8: DecompressPointer r1
    //     0x13caeb8: add             x1, x1, HEAP, lsl #32
    // 0x13caebc: r0 = controller()
    //     0x13caebc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13caec0: mov             x1, x0
    // 0x13caec4: r2 = "Exchange With New Product"
    //     0x13caec4: add             x2, PP, #0x46, lsl #12  ; [pp+0x464f0] "Exchange With New Product"
    //     0x13caec8: ldr             x2, [x2, #0x4f0]
    // 0x13caecc: r3 = "return_exchange_with_different_product"
    //     0x13caecc: add             x3, PP, #0x38, lsl #12  ; [pp+0x380f8] "return_exchange_with_different_product"
    //     0x13caed0: ldr             x3, [x3, #0xf8]
    // 0x13caed4: r0 = ctaPostEvent()
    //     0x13caed4: bl              #0x1318484  ; [package:customer_app/app/presentation/controllers/exchange/exchange_same_product_controller.dart] ExchangeSameProductController::ctaPostEvent
    // 0x13caed8: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13caed8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13caedc: ldr             x0, [x0, #0x1c80]
    //     0x13caee0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13caee4: cmp             w0, w16
    //     0x13caee8: b.ne            #0x13caef4
    //     0x13caeec: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13caef0: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13caef4: r1 = Null
    //     0x13caef4: mov             x1, NULL
    // 0x13caef8: r2 = 12
    //     0x13caef8: movz            x2, #0xc
    // 0x13caefc: r0 = AllocateArray()
    //     0x13caefc: bl              #0x16f7198  ; AllocateArrayStub
    // 0x13caf00: stur            x0, [fp, #-0x10]
    // 0x13caf04: r16 = "order_id"
    //     0x13caf04: add             x16, PP, #0xe, lsl #12  ; [pp+0xea38] "order_id"
    //     0x13caf08: ldr             x16, [x16, #0xa38]
    // 0x13caf0c: StoreField: r0->field_f = r16
    //     0x13caf0c: stur            w16, [x0, #0xf]
    // 0x13caf10: ldur            x2, [fp, #-8]
    // 0x13caf14: LoadField: r1 = r2->field_f
    //     0x13caf14: ldur            w1, [x2, #0xf]
    // 0x13caf18: DecompressPointer r1
    //     0x13caf18: add             x1, x1, HEAP, lsl #32
    // 0x13caf1c: r0 = controller()
    //     0x13caf1c: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13caf20: LoadField: r1 = r0->field_6f
    //     0x13caf20: ldur            w1, [x0, #0x6f]
    // 0x13caf24: DecompressPointer r1
    //     0x13caf24: add             x1, x1, HEAP, lsl #32
    // 0x13caf28: r0 = value()
    //     0x13caf28: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13caf2c: ldur            x1, [fp, #-0x10]
    // 0x13caf30: ArrayStore: r1[1] = r0  ; List_4
    //     0x13caf30: add             x25, x1, #0x13
    //     0x13caf34: str             w0, [x25]
    //     0x13caf38: tbz             w0, #0, #0x13caf54
    //     0x13caf3c: ldurb           w16, [x1, #-1]
    //     0x13caf40: ldurb           w17, [x0, #-1]
    //     0x13caf44: and             x16, x17, x16, lsr #2
    //     0x13caf48: tst             x16, HEAP, lsr #32
    //     0x13caf4c: b.eq            #0x13caf54
    //     0x13caf50: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13caf54: ldur            x0, [fp, #-0x10]
    // 0x13caf58: r16 = "charge"
    //     0x13caf58: add             x16, PP, #0x37, lsl #12  ; [pp+0x37b28] "charge"
    //     0x13caf5c: ldr             x16, [x16, #0xb28]
    // 0x13caf60: ArrayStore: r0[0] = r16  ; List_4
    //     0x13caf60: stur            w16, [x0, #0x17]
    // 0x13caf64: ldur            x1, [fp, #-8]
    // 0x13caf68: LoadField: r2 = r1->field_f
    //     0x13caf68: ldur            w2, [x1, #0xf]
    // 0x13caf6c: DecompressPointer r2
    //     0x13caf6c: add             x2, x2, HEAP, lsl #32
    // 0x13caf70: mov             x1, x2
    // 0x13caf74: r0 = controller()
    //     0x13caf74: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13caf78: LoadField: r1 = r0->field_67
    //     0x13caf78: ldur            w1, [x0, #0x67]
    // 0x13caf7c: DecompressPointer r1
    //     0x13caf7c: add             x1, x1, HEAP, lsl #32
    // 0x13caf80: mov             x0, x1
    // 0x13caf84: ldur            x1, [fp, #-0x10]
    // 0x13caf88: ArrayStore: r1[3] = r0  ; List_4
    //     0x13caf88: add             x25, x1, #0x1b
    //     0x13caf8c: str             w0, [x25]
    //     0x13caf90: tbz             w0, #0, #0x13cafac
    //     0x13caf94: ldurb           w16, [x1, #-1]
    //     0x13caf98: ldurb           w17, [x0, #-1]
    //     0x13caf9c: and             x16, x17, x16, lsr #2
    //     0x13cafa0: tst             x16, HEAP, lsr #32
    //     0x13cafa4: b.eq            #0x13cafac
    //     0x13cafa8: bl              #0x16f5444  ; ArrayWriteBarrierStub
    // 0x13cafac: ldur            x0, [fp, #-0x10]
    // 0x13cafb0: r16 = "type"
    //     0x13cafb0: ldr             x16, [PP, #0x2e10]  ; [pp+0x2e10] "type"
    // 0x13cafb4: StoreField: r0->field_1f = r16
    //     0x13cafb4: stur            w16, [x0, #0x1f]
    // 0x13cafb8: r16 = "replace-new"
    //     0x13cafb8: add             x16, PP, #0x32, lsl #12  ; [pp+0x32ad8] "replace-new"
    //     0x13cafbc: ldr             x16, [x16, #0xad8]
    // 0x13cafc0: StoreField: r0->field_23 = r16
    //     0x13cafc0: stur            w16, [x0, #0x23]
    // 0x13cafc4: r16 = <String, Object?>
    //     0x13cafc4: add             x16, PP, #0xb, lsl #12  ; [pp+0xbc28] TypeArguments: <String, Object?>
    //     0x13cafc8: ldr             x16, [x16, #0xc28]
    // 0x13cafcc: stp             x0, x16, [SP]
    // 0x13cafd0: r0 = Map._fromLiteral()
    //     0x13cafd0: bl              #0x61a2c0  ; [dart:core] Map::Map._fromLiteral
    // 0x13cafd4: r16 = "/return-order"
    //     0x13cafd4: add             x16, PP, #0xd, lsl #12  ; [pp+0xd8b8] "/return-order"
    //     0x13cafd8: ldr             x16, [x16, #0x8b8]
    // 0x13cafdc: stp             x16, NULL, [SP, #8]
    // 0x13cafe0: str             x0, [SP]
    // 0x13cafe4: r4 = const [0x1, 0x2, 0x2, 0x1, arguments, 0x1, null]
    //     0x13cafe4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb438] List(7) [0x1, 0x2, 0x2, 0x1, "arguments", 0x1, Null]
    //     0x13cafe8: ldr             x4, [x4, #0x438]
    // 0x13cafec: r0 = GetNavigation.offAndToNamed()
    //     0x13cafec: bl              #0x8f4af0  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.offAndToNamed
    // 0x13caff0: r0 = Null
    //     0x13caff0: mov             x0, NULL
    // 0x13caff4: LeaveFrame
    //     0x13caff4: mov             SP, fp
    //     0x13caff8: ldp             fp, lr, [SP], #0x10
    // 0x13caffc: ret
    //     0x13caffc: ret             
    // 0x13cb000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cb000: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cb004: b               #0x13caeb4
  }
  [closure] Obx <anonymous closure>(dynamic, BuildContext, int) {
    // ** addr: 0x13cb008, size: 0x74
    // 0x13cb008: EnterFrame
    //     0x13cb008: stp             fp, lr, [SP, #-0x10]!
    //     0x13cb00c: mov             fp, SP
    // 0x13cb010: AllocStack(0x10)
    //     0x13cb010: sub             SP, SP, #0x10
    // 0x13cb014: SetupParameters()
    //     0x13cb014: ldr             x0, [fp, #0x20]
    //     0x13cb018: ldur            w1, [x0, #0x17]
    //     0x13cb01c: add             x1, x1, HEAP, lsl #32
    //     0x13cb020: stur            x1, [fp, #-8]
    // 0x13cb024: r1 = 2
    //     0x13cb024: movz            x1, #0x2
    // 0x13cb028: r0 = AllocateContext()
    //     0x13cb028: bl              #0x16f6108  ; AllocateContextStub
    // 0x13cb02c: mov             x1, x0
    // 0x13cb030: ldur            x0, [fp, #-8]
    // 0x13cb034: stur            x1, [fp, #-0x10]
    // 0x13cb038: StoreField: r1->field_b = r0
    //     0x13cb038: stur            w0, [x1, #0xb]
    // 0x13cb03c: ldr             x0, [fp, #0x18]
    // 0x13cb040: StoreField: r1->field_f = r0
    //     0x13cb040: stur            w0, [x1, #0xf]
    // 0x13cb044: ldr             x0, [fp, #0x10]
    // 0x13cb048: StoreField: r1->field_13 = r0
    //     0x13cb048: stur            w0, [x1, #0x13]
    // 0x13cb04c: r0 = Obx()
    //     0x13cb04c: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x13cb050: ldur            x2, [fp, #-0x10]
    // 0x13cb054: r1 = Function '<anonymous closure>':.
    //     0x13cb054: add             x1, PP, #0x46, lsl #12  ; [pp+0x46500] AnonymousClosure: (0x13cb07c), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x13c9858)
    //     0x13cb058: ldr             x1, [x1, #0x500]
    // 0x13cb05c: stur            x0, [fp, #-8]
    // 0x13cb060: r0 = AllocateClosure()
    //     0x13cb060: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cb064: mov             x1, x0
    // 0x13cb068: ldur            x0, [fp, #-8]
    // 0x13cb06c: StoreField: r0->field_b = r1
    //     0x13cb06c: stur            w1, [x0, #0xb]
    // 0x13cb070: LeaveFrame
    //     0x13cb070: mov             SP, fp
    //     0x13cb074: ldp             fp, lr, [SP], #0x10
    // 0x13cb078: ret
    //     0x13cb078: ret             
  }
  [closure] TextButtonTheme <anonymous closure>(dynamic) {
    // ** addr: 0x13cb07c, size: 0x4d8
    // 0x13cb07c: EnterFrame
    //     0x13cb07c: stp             fp, lr, [SP, #-0x10]!
    //     0x13cb080: mov             fp, SP
    // 0x13cb084: AllocStack(0x48)
    //     0x13cb084: sub             SP, SP, #0x48
    // 0x13cb088: SetupParameters()
    //     0x13cb088: ldr             x0, [fp, #0x10]
    //     0x13cb08c: ldur            w2, [x0, #0x17]
    //     0x13cb090: add             x2, x2, HEAP, lsl #32
    //     0x13cb094: stur            x2, [fp, #-0x10]
    // 0x13cb098: CheckStackOverflow
    //     0x13cb098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cb09c: cmp             SP, x16
    //     0x13cb0a0: b.ls            #0x13cb544
    // 0x13cb0a4: LoadField: r0 = r2->field_b
    //     0x13cb0a4: ldur            w0, [x2, #0xb]
    // 0x13cb0a8: DecompressPointer r0
    //     0x13cb0a8: add             x0, x0, HEAP, lsl #32
    // 0x13cb0ac: stur            x0, [fp, #-8]
    // 0x13cb0b0: LoadField: r1 = r0->field_f
    //     0x13cb0b0: ldur            w1, [x0, #0xf]
    // 0x13cb0b4: DecompressPointer r1
    //     0x13cb0b4: add             x1, x1, HEAP, lsl #32
    // 0x13cb0b8: r0 = controller()
    //     0x13cb0b8: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb0bc: LoadField: r2 = r0->field_5b
    //     0x13cb0bc: ldur            w2, [x0, #0x5b]
    // 0x13cb0c0: DecompressPointer r2
    //     0x13cb0c0: add             x2, x2, HEAP, lsl #32
    // 0x13cb0c4: ldur            x0, [fp, #-8]
    // 0x13cb0c8: stur            x2, [fp, #-0x18]
    // 0x13cb0cc: LoadField: r1 = r0->field_f
    //     0x13cb0cc: ldur            w1, [x0, #0xf]
    // 0x13cb0d0: DecompressPointer r1
    //     0x13cb0d0: add             x1, x1, HEAP, lsl #32
    // 0x13cb0d4: r0 = controller()
    //     0x13cb0d4: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb0d8: LoadField: r1 = r0->field_4b
    //     0x13cb0d8: ldur            w1, [x0, #0x4b]
    // 0x13cb0dc: DecompressPointer r1
    //     0x13cb0dc: add             x1, x1, HEAP, lsl #32
    // 0x13cb0e0: r0 = value()
    //     0x13cb0e0: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cb0e4: LoadField: r1 = r0->field_b
    //     0x13cb0e4: ldur            w1, [x0, #0xb]
    // 0x13cb0e8: DecompressPointer r1
    //     0x13cb0e8: add             x1, x1, HEAP, lsl #32
    // 0x13cb0ec: cmp             w1, NULL
    // 0x13cb0f0: b.ne            #0x13cb100
    // 0x13cb0f4: ldur            x3, [fp, #-0x10]
    // 0x13cb0f8: r0 = Null
    //     0x13cb0f8: mov             x0, NULL
    // 0x13cb0fc: b               #0x13cb16c
    // 0x13cb100: LoadField: r0 = r1->field_2f
    //     0x13cb100: ldur            w0, [x1, #0x2f]
    // 0x13cb104: DecompressPointer r0
    //     0x13cb104: add             x0, x0, HEAP, lsl #32
    // 0x13cb108: cmp             w0, NULL
    // 0x13cb10c: b.ne            #0x13cb11c
    // 0x13cb110: ldur            x3, [fp, #-0x10]
    // 0x13cb114: r0 = Null
    //     0x13cb114: mov             x0, NULL
    // 0x13cb118: b               #0x13cb16c
    // 0x13cb11c: ldur            x3, [fp, #-0x10]
    // 0x13cb120: LoadField: r2 = r0->field_b
    //     0x13cb120: ldur            w2, [x0, #0xb]
    // 0x13cb124: DecompressPointer r2
    //     0x13cb124: add             x2, x2, HEAP, lsl #32
    // 0x13cb128: LoadField: r0 = r3->field_13
    //     0x13cb128: ldur            w0, [x3, #0x13]
    // 0x13cb12c: DecompressPointer r0
    //     0x13cb12c: add             x0, x0, HEAP, lsl #32
    // 0x13cb130: LoadField: r1 = r2->field_b
    //     0x13cb130: ldur            w1, [x2, #0xb]
    // 0x13cb134: r4 = LoadInt32Instr(r0)
    //     0x13cb134: sbfx            x4, x0, #1, #0x1f
    //     0x13cb138: tbz             w0, #0, #0x13cb140
    //     0x13cb13c: ldur            x4, [x0, #7]
    // 0x13cb140: r0 = LoadInt32Instr(r1)
    //     0x13cb140: sbfx            x0, x1, #1, #0x1f
    // 0x13cb144: mov             x1, x4
    // 0x13cb148: cmp             x1, x0
    // 0x13cb14c: b.hs            #0x13cb54c
    // 0x13cb150: LoadField: r0 = r2->field_f
    //     0x13cb150: ldur            w0, [x2, #0xf]
    // 0x13cb154: DecompressPointer r0
    //     0x13cb154: add             x0, x0, HEAP, lsl #32
    // 0x13cb158: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cb158: add             x16, x0, x4, lsl #2
    //     0x13cb15c: ldur            w1, [x16, #0xf]
    // 0x13cb160: DecompressPointer r1
    //     0x13cb160: add             x1, x1, HEAP, lsl #32
    // 0x13cb164: LoadField: r0 = r1->field_7
    //     0x13cb164: ldur            w0, [x1, #7]
    // 0x13cb168: DecompressPointer r0
    //     0x13cb168: add             x0, x0, HEAP, lsl #32
    // 0x13cb16c: cmp             w0, NULL
    // 0x13cb170: b.ne            #0x13cb17c
    // 0x13cb174: r2 = ""
    //     0x13cb174: ldr             x2, [PP, #0x8e0]  ; [pp+0x8e0] ""
    // 0x13cb178: b               #0x13cb180
    // 0x13cb17c: mov             x2, x0
    // 0x13cb180: ldur            x1, [fp, #-0x18]
    // 0x13cb184: r0 = RxStringExt.contains()
    //     0x13cb184: bl              #0x13cb554  ; [package:get/get_rx/src/rx_types/rx_types.dart] ::RxStringExt.contains
    // 0x13cb188: tbnz            w0, #4, #0x13cb290
    // 0x13cb18c: r16 = <EdgeInsets>
    //     0x13cb18c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13cb190: ldr             x16, [x16, #0xda0]
    // 0x13cb194: r30 = Instance_EdgeInsets
    //     0x13cb194: add             lr, PP, #0x38, lsl #12  ; [pp+0x38140] Obj!EdgeInsets@d59c91
    //     0x13cb198: ldr             lr, [lr, #0x140]
    // 0x13cb19c: stp             lr, x16, [SP]
    // 0x13cb1a0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb1a0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb1a4: r0 = all()
    //     0x13cb1a4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb1a8: r1 = Instance_Color
    //     0x13cb1a8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb1ac: d0 = 0.030000
    //     0x13cb1ac: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x13cb1b0: ldr             d0, [x17, #0x238]
    // 0x13cb1b4: stur            x0, [fp, #-0x18]
    // 0x13cb1b8: r0 = withOpacity()
    //     0x13cb1b8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb1bc: r16 = <Color>
    //     0x13cb1bc: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb1c0: ldr             x16, [x16, #0xf80]
    // 0x13cb1c4: stp             x0, x16, [SP]
    // 0x13cb1c8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb1c8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb1cc: r0 = all()
    //     0x13cb1cc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb1d0: r1 = Instance_Color
    //     0x13cb1d0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb1d4: d0 = 0.030000
    //     0x13cb1d4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x13cb1d8: ldr             d0, [x17, #0x238]
    // 0x13cb1dc: stur            x0, [fp, #-0x20]
    // 0x13cb1e0: r0 = withOpacity()
    //     0x13cb1e0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb1e4: r16 = <Color>
    //     0x13cb1e4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb1e8: ldr             x16, [x16, #0xf80]
    // 0x13cb1ec: stp             x0, x16, [SP]
    // 0x13cb1f0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb1f0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb1f4: r0 = all()
    //     0x13cb1f4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb1f8: stur            x0, [fp, #-0x28]
    // 0x13cb1fc: r0 = Radius()
    //     0x13cb1fc: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13cb200: d0 = 12.000000
    //     0x13cb200: fmov            d0, #12.00000000
    // 0x13cb204: stur            x0, [fp, #-0x30]
    // 0x13cb208: StoreField: r0->field_7 = d0
    //     0x13cb208: stur            d0, [x0, #7]
    // 0x13cb20c: StoreField: r0->field_f = d0
    //     0x13cb20c: stur            d0, [x0, #0xf]
    // 0x13cb210: r0 = BorderRadius()
    //     0x13cb210: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13cb214: mov             x1, x0
    // 0x13cb218: ldur            x0, [fp, #-0x30]
    // 0x13cb21c: stur            x1, [fp, #-0x38]
    // 0x13cb220: StoreField: r1->field_7 = r0
    //     0x13cb220: stur            w0, [x1, #7]
    // 0x13cb224: StoreField: r1->field_b = r0
    //     0x13cb224: stur            w0, [x1, #0xb]
    // 0x13cb228: StoreField: r1->field_f = r0
    //     0x13cb228: stur            w0, [x1, #0xf]
    // 0x13cb22c: StoreField: r1->field_13 = r0
    //     0x13cb22c: stur            w0, [x1, #0x13]
    // 0x13cb230: r0 = RoundedRectangleBorder()
    //     0x13cb230: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x13cb234: mov             x1, x0
    // 0x13cb238: ldur            x0, [fp, #-0x38]
    // 0x13cb23c: StoreField: r1->field_b = r0
    //     0x13cb23c: stur            w0, [x1, #0xb]
    // 0x13cb240: r0 = Instance_BorderSide
    //     0x13cb240: add             x0, PP, #0x46, lsl #12  ; [pp+0x46508] Obj!BorderSide@d62ff1
    //     0x13cb244: ldr             x0, [x0, #0x508]
    // 0x13cb248: StoreField: r1->field_7 = r0
    //     0x13cb248: stur            w0, [x1, #7]
    // 0x13cb24c: r16 = <RoundedRectangleBorder>
    //     0x13cb24c: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13cb250: ldr             x16, [x16, #0xf78]
    // 0x13cb254: stp             x1, x16, [SP]
    // 0x13cb258: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb258: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb25c: r0 = all()
    //     0x13cb25c: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb260: stur            x0, [fp, #-0x30]
    // 0x13cb264: r0 = ButtonStyle()
    //     0x13cb264: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13cb268: mov             x1, x0
    // 0x13cb26c: ldur            x0, [fp, #-0x28]
    // 0x13cb270: StoreField: r1->field_b = r0
    //     0x13cb270: stur            w0, [x1, #0xb]
    // 0x13cb274: ldur            x0, [fp, #-0x20]
    // 0x13cb278: StoreField: r1->field_f = r0
    //     0x13cb278: stur            w0, [x1, #0xf]
    // 0x13cb27c: ldur            x0, [fp, #-0x18]
    // 0x13cb280: StoreField: r1->field_23 = r0
    //     0x13cb280: stur            w0, [x1, #0x23]
    // 0x13cb284: ldur            x0, [fp, #-0x30]
    // 0x13cb288: StoreField: r1->field_43 = r0
    //     0x13cb288: stur            w0, [x1, #0x43]
    // 0x13cb28c: b               #0x13cb394
    // 0x13cb290: d0 = 12.000000
    //     0x13cb290: fmov            d0, #12.00000000
    // 0x13cb294: r16 = <EdgeInsets>
    //     0x13cb294: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2fda0] TypeArguments: <EdgeInsets>
    //     0x13cb298: ldr             x16, [x16, #0xda0]
    // 0x13cb29c: r30 = Instance_EdgeInsets
    //     0x13cb29c: add             lr, PP, #0x38, lsl #12  ; [pp+0x38140] Obj!EdgeInsets@d59c91
    //     0x13cb2a0: ldr             lr, [lr, #0x140]
    // 0x13cb2a4: stp             lr, x16, [SP]
    // 0x13cb2a8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb2a8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb2ac: r0 = all()
    //     0x13cb2ac: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb2b0: r1 = Instance_Color
    //     0x13cb2b0: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb2b4: d0 = 0.030000
    //     0x13cb2b4: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x13cb2b8: ldr             d0, [x17, #0x238]
    // 0x13cb2bc: stur            x0, [fp, #-0x18]
    // 0x13cb2c0: r0 = withOpacity()
    //     0x13cb2c0: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb2c4: r16 = <Color>
    //     0x13cb2c4: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb2c8: ldr             x16, [x16, #0xf80]
    // 0x13cb2cc: stp             x0, x16, [SP]
    // 0x13cb2d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb2d0: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb2d4: r0 = all()
    //     0x13cb2d4: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb2d8: r1 = Instance_Color
    //     0x13cb2d8: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb2dc: d0 = 0.030000
    //     0x13cb2dc: add             x17, PP, #0x32, lsl #12  ; [pp+0x32238] IMM: double(0.03) from 0x3f9eb851eb851eb8
    //     0x13cb2e0: ldr             d0, [x17, #0x238]
    // 0x13cb2e4: stur            x0, [fp, #-0x20]
    // 0x13cb2e8: r0 = withOpacity()
    //     0x13cb2e8: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb2ec: r16 = <Color>
    //     0x13cb2ec: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f80] TypeArguments: <Color>
    //     0x13cb2f0: ldr             x16, [x16, #0xf80]
    // 0x13cb2f4: stp             x0, x16, [SP]
    // 0x13cb2f8: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb2f8: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb2fc: r0 = all()
    //     0x13cb2fc: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb300: stur            x0, [fp, #-0x28]
    // 0x13cb304: r0 = Radius()
    //     0x13cb304: bl              #0x76b020  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x13cb308: d0 = 12.000000
    //     0x13cb308: fmov            d0, #12.00000000
    // 0x13cb30c: stur            x0, [fp, #-0x30]
    // 0x13cb310: StoreField: r0->field_7 = d0
    //     0x13cb310: stur            d0, [x0, #7]
    // 0x13cb314: StoreField: r0->field_f = d0
    //     0x13cb314: stur            d0, [x0, #0xf]
    // 0x13cb318: r0 = BorderRadius()
    //     0x13cb318: bl              #0x80f0b4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0x13cb31c: mov             x1, x0
    // 0x13cb320: ldur            x0, [fp, #-0x30]
    // 0x13cb324: stur            x1, [fp, #-0x38]
    // 0x13cb328: StoreField: r1->field_7 = r0
    //     0x13cb328: stur            w0, [x1, #7]
    // 0x13cb32c: StoreField: r1->field_b = r0
    //     0x13cb32c: stur            w0, [x1, #0xb]
    // 0x13cb330: StoreField: r1->field_f = r0
    //     0x13cb330: stur            w0, [x1, #0xf]
    // 0x13cb334: StoreField: r1->field_13 = r0
    //     0x13cb334: stur            w0, [x1, #0x13]
    // 0x13cb338: r0 = RoundedRectangleBorder()
    //     0x13cb338: bl              #0x98f2bc  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0x13cb33c: mov             x1, x0
    // 0x13cb340: ldur            x0, [fp, #-0x38]
    // 0x13cb344: StoreField: r1->field_b = r0
    //     0x13cb344: stur            w0, [x1, #0xb]
    // 0x13cb348: r0 = Instance_BorderSide
    //     0x13cb348: add             x0, PP, #0x34, lsl #12  ; [pp+0x34e20] Obj!BorderSide@d62ed1
    //     0x13cb34c: ldr             x0, [x0, #0xe20]
    // 0x13cb350: StoreField: r1->field_7 = r0
    //     0x13cb350: stur            w0, [x1, #7]
    // 0x13cb354: r16 = <RoundedRectangleBorder>
    //     0x13cb354: add             x16, PP, #0x12, lsl #12  ; [pp+0x12f78] TypeArguments: <RoundedRectangleBorder>
    //     0x13cb358: ldr             x16, [x16, #0xf78]
    // 0x13cb35c: stp             x1, x16, [SP]
    // 0x13cb360: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cb360: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cb364: r0 = all()
    //     0x13cb364: bl              #0x98f2c8  ; [package:flutter/src/widgets/widget_state.dart] WidgetStateProperty::all
    // 0x13cb368: stur            x0, [fp, #-0x30]
    // 0x13cb36c: r0 = ButtonStyle()
    //     0x13cb36c: bl              #0x98f2b0  ; AllocateButtonStyleStub -> ButtonStyle (size=0x6c)
    // 0x13cb370: mov             x1, x0
    // 0x13cb374: ldur            x0, [fp, #-0x28]
    // 0x13cb378: StoreField: r1->field_b = r0
    //     0x13cb378: stur            w0, [x1, #0xb]
    // 0x13cb37c: ldur            x0, [fp, #-0x20]
    // 0x13cb380: StoreField: r1->field_f = r0
    //     0x13cb380: stur            w0, [x1, #0xf]
    // 0x13cb384: ldur            x0, [fp, #-0x18]
    // 0x13cb388: StoreField: r1->field_23 = r0
    //     0x13cb388: stur            w0, [x1, #0x23]
    // 0x13cb38c: ldur            x0, [fp, #-0x30]
    // 0x13cb390: StoreField: r1->field_43 = r0
    //     0x13cb390: stur            w0, [x1, #0x43]
    // 0x13cb394: ldur            x0, [fp, #-8]
    // 0x13cb398: stur            x1, [fp, #-0x18]
    // 0x13cb39c: r0 = TextButtonThemeData()
    //     0x13cb39c: bl              #0x98f2a4  ; AllocateTextButtonThemeDataStub -> TextButtonThemeData (size=0xc)
    // 0x13cb3a0: mov             x2, x0
    // 0x13cb3a4: ldur            x0, [fp, #-0x18]
    // 0x13cb3a8: stur            x2, [fp, #-0x20]
    // 0x13cb3ac: StoreField: r2->field_7 = r0
    //     0x13cb3ac: stur            w0, [x2, #7]
    // 0x13cb3b0: ldur            x0, [fp, #-8]
    // 0x13cb3b4: LoadField: r1 = r0->field_f
    //     0x13cb3b4: ldur            w1, [x0, #0xf]
    // 0x13cb3b8: DecompressPointer r1
    //     0x13cb3b8: add             x1, x1, HEAP, lsl #32
    // 0x13cb3bc: r0 = controller()
    //     0x13cb3bc: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cb3c0: LoadField: r1 = r0->field_4b
    //     0x13cb3c0: ldur            w1, [x0, #0x4b]
    // 0x13cb3c4: DecompressPointer r1
    //     0x13cb3c4: add             x1, x1, HEAP, lsl #32
    // 0x13cb3c8: r0 = value()
    //     0x13cb3c8: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cb3cc: LoadField: r1 = r0->field_b
    //     0x13cb3cc: ldur            w1, [x0, #0xb]
    // 0x13cb3d0: DecompressPointer r1
    //     0x13cb3d0: add             x1, x1, HEAP, lsl #32
    // 0x13cb3d4: cmp             w1, NULL
    // 0x13cb3d8: b.ne            #0x13cb3e8
    // 0x13cb3dc: ldur            x2, [fp, #-0x10]
    // 0x13cb3e0: r1 = Null
    //     0x13cb3e0: mov             x1, NULL
    // 0x13cb3e4: b               #0x13cb458
    // 0x13cb3e8: LoadField: r0 = r1->field_2f
    //     0x13cb3e8: ldur            w0, [x1, #0x2f]
    // 0x13cb3ec: DecompressPointer r0
    //     0x13cb3ec: add             x0, x0, HEAP, lsl #32
    // 0x13cb3f0: cmp             w0, NULL
    // 0x13cb3f4: b.ne            #0x13cb404
    // 0x13cb3f8: ldur            x2, [fp, #-0x10]
    // 0x13cb3fc: r0 = Null
    //     0x13cb3fc: mov             x0, NULL
    // 0x13cb400: b               #0x13cb454
    // 0x13cb404: ldur            x2, [fp, #-0x10]
    // 0x13cb408: LoadField: r3 = r0->field_b
    //     0x13cb408: ldur            w3, [x0, #0xb]
    // 0x13cb40c: DecompressPointer r3
    //     0x13cb40c: add             x3, x3, HEAP, lsl #32
    // 0x13cb410: LoadField: r0 = r2->field_13
    //     0x13cb410: ldur            w0, [x2, #0x13]
    // 0x13cb414: DecompressPointer r0
    //     0x13cb414: add             x0, x0, HEAP, lsl #32
    // 0x13cb418: LoadField: r1 = r3->field_b
    //     0x13cb418: ldur            w1, [x3, #0xb]
    // 0x13cb41c: r4 = LoadInt32Instr(r0)
    //     0x13cb41c: sbfx            x4, x0, #1, #0x1f
    //     0x13cb420: tbz             w0, #0, #0x13cb428
    //     0x13cb424: ldur            x4, [x0, #7]
    // 0x13cb428: r0 = LoadInt32Instr(r1)
    //     0x13cb428: sbfx            x0, x1, #1, #0x1f
    // 0x13cb42c: mov             x1, x4
    // 0x13cb430: cmp             x1, x0
    // 0x13cb434: b.hs            #0x13cb550
    // 0x13cb438: LoadField: r0 = r3->field_f
    //     0x13cb438: ldur            w0, [x3, #0xf]
    // 0x13cb43c: DecompressPointer r0
    //     0x13cb43c: add             x0, x0, HEAP, lsl #32
    // 0x13cb440: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x13cb440: add             x16, x0, x4, lsl #2
    //     0x13cb444: ldur            w1, [x16, #0xf]
    // 0x13cb448: DecompressPointer r1
    //     0x13cb448: add             x1, x1, HEAP, lsl #32
    // 0x13cb44c: LoadField: r0 = r1->field_f
    //     0x13cb44c: ldur            w0, [x1, #0xf]
    // 0x13cb450: DecompressPointer r0
    //     0x13cb450: add             x0, x0, HEAP, lsl #32
    // 0x13cb454: mov             x1, x0
    // 0x13cb458: ldur            x0, [fp, #-0x20]
    // 0x13cb45c: str             x1, [SP]
    // 0x13cb460: r0 = _interpolateSingle()
    //     0x13cb460: bl              #0x61e100  ; [dart:core] _StringBase::_interpolateSingle
    // 0x13cb464: ldur            x2, [fp, #-0x10]
    // 0x13cb468: stur            x0, [fp, #-8]
    // 0x13cb46c: LoadField: r1 = r2->field_f
    //     0x13cb46c: ldur            w1, [x2, #0xf]
    // 0x13cb470: DecompressPointer r1
    //     0x13cb470: add             x1, x1, HEAP, lsl #32
    // 0x13cb474: r0 = of()
    //     0x13cb474: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x13cb478: LoadField: r1 = r0->field_87
    //     0x13cb478: ldur            w1, [x0, #0x87]
    // 0x13cb47c: DecompressPointer r1
    //     0x13cb47c: add             x1, x1, HEAP, lsl #32
    // 0x13cb480: LoadField: r0 = r1->field_2b
    //     0x13cb480: ldur            w0, [x1, #0x2b]
    // 0x13cb484: DecompressPointer r0
    //     0x13cb484: add             x0, x0, HEAP, lsl #32
    // 0x13cb488: stur            x0, [fp, #-0x18]
    // 0x13cb48c: r1 = Instance_Color
    //     0x13cb48c: ldr             x1, [PP, #0x51e8]  ; [pp+0x51e8] Obj!Color@d69871
    // 0x13cb490: d0 = 0.700000
    //     0x13cb490: add             x17, PP, #0x12, lsl #12  ; [pp+0x12f48] IMM: double(0.7) from 0x3fe6666666666666
    //     0x13cb494: ldr             d0, [x17, #0xf48]
    // 0x13cb498: r0 = withOpacity()
    //     0x13cb498: bl              #0x16860e4  ; [dart:ui] Color::withOpacity
    // 0x13cb49c: r16 = 14.000000
    //     0x13cb49c: add             x16, PP, #0x2f, lsl #12  ; [pp+0x2f1d8] 14
    //     0x13cb4a0: ldr             x16, [x16, #0x1d8]
    // 0x13cb4a4: stp             x16, x0, [SP]
    // 0x13cb4a8: ldur            x1, [fp, #-0x18]
    // 0x13cb4ac: r4 = const [0, 0x3, 0x2, 0x1, color, 0x1, fontSize, 0x2, null]
    //     0x13cb4ac: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2e9b8] List(9) [0, 0x3, 0x2, 0x1, "color", 0x1, "fontSize", 0x2, Null]
    //     0x13cb4b0: ldr             x4, [x4, #0x9b8]
    // 0x13cb4b4: r0 = copyWith()
    //     0x13cb4b4: bl              #0x6afe34  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0x13cb4b8: stur            x0, [fp, #-0x18]
    // 0x13cb4bc: r0 = Text()
    //     0x13cb4bc: bl              #0x811148  ; AllocateTextStub -> Text (size=0x50)
    // 0x13cb4c0: mov             x3, x0
    // 0x13cb4c4: ldur            x0, [fp, #-8]
    // 0x13cb4c8: stur            x3, [fp, #-0x28]
    // 0x13cb4cc: StoreField: r3->field_b = r0
    //     0x13cb4cc: stur            w0, [x3, #0xb]
    // 0x13cb4d0: ldur            x0, [fp, #-0x18]
    // 0x13cb4d4: StoreField: r3->field_13 = r0
    //     0x13cb4d4: stur            w0, [x3, #0x13]
    // 0x13cb4d8: r0 = Instance_TextOverflow
    //     0x13cb4d8: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2fe10] Obj!TextOverflow@d73721
    //     0x13cb4dc: ldr             x0, [x0, #0xe10]
    // 0x13cb4e0: StoreField: r3->field_2b = r0
    //     0x13cb4e0: stur            w0, [x3, #0x2b]
    // 0x13cb4e4: ldur            x2, [fp, #-0x10]
    // 0x13cb4e8: r1 = Function '<anonymous closure>':.
    //     0x13cb4e8: add             x1, PP, #0x46, lsl #12  ; [pp+0x46510] AnonymousClosure: (0x13cb5b4), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x1505a40)
    //     0x13cb4ec: ldr             x1, [x1, #0x510]
    // 0x13cb4f0: r0 = AllocateClosure()
    //     0x13cb4f0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cb4f4: stur            x0, [fp, #-8]
    // 0x13cb4f8: r0 = TextButton()
    //     0x13cb4f8: bl              #0x993798  ; AllocateTextButtonStub -> TextButton (size=0x3c)
    // 0x13cb4fc: mov             x1, x0
    // 0x13cb500: ldur            x0, [fp, #-8]
    // 0x13cb504: stur            x1, [fp, #-0x10]
    // 0x13cb508: StoreField: r1->field_b = r0
    //     0x13cb508: stur            w0, [x1, #0xb]
    // 0x13cb50c: r0 = false
    //     0x13cb50c: add             x0, NULL, #0x30  ; false
    // 0x13cb510: StoreField: r1->field_27 = r0
    //     0x13cb510: stur            w0, [x1, #0x27]
    // 0x13cb514: r0 = true
    //     0x13cb514: add             x0, NULL, #0x20  ; true
    // 0x13cb518: StoreField: r1->field_2f = r0
    //     0x13cb518: stur            w0, [x1, #0x2f]
    // 0x13cb51c: ldur            x0, [fp, #-0x28]
    // 0x13cb520: StoreField: r1->field_37 = r0
    //     0x13cb520: stur            w0, [x1, #0x37]
    // 0x13cb524: r0 = TextButtonTheme()
    //     0x13cb524: bl              #0x796ee0  ; AllocateTextButtonThemeStub -> TextButtonTheme (size=0x14)
    // 0x13cb528: ldur            x1, [fp, #-0x20]
    // 0x13cb52c: StoreField: r0->field_f = r1
    //     0x13cb52c: stur            w1, [x0, #0xf]
    // 0x13cb530: ldur            x1, [fp, #-0x10]
    // 0x13cb534: StoreField: r0->field_b = r1
    //     0x13cb534: stur            w1, [x0, #0xb]
    // 0x13cb538: LeaveFrame
    //     0x13cb538: mov             SP, fp
    //     0x13cb53c: ldp             fp, lr, [SP], #0x10
    // 0x13cb540: ret
    //     0x13cb540: ret             
    // 0x13cb544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cb544: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cb548: b               #0x13cb0a4
    // 0x13cb54c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cb54c: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
    // 0x13cb550: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x13cb550: bl              #0x16f77c8  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x13cd4a8, size: 0x78
    // 0x13cd4a8: EnterFrame
    //     0x13cd4a8: stp             fp, lr, [SP, #-0x10]!
    //     0x13cd4ac: mov             fp, SP
    // 0x13cd4b0: AllocStack(0x18)
    //     0x13cd4b0: sub             SP, SP, #0x18
    // 0x13cd4b4: SetupParameters()
    //     0x13cd4b4: ldr             x0, [fp, #0x10]
    //     0x13cd4b8: ldur            w2, [x0, #0x17]
    //     0x13cd4bc: add             x2, x2, HEAP, lsl #32
    //     0x13cd4c0: stur            x2, [fp, #-8]
    // 0x13cd4c4: CheckStackOverflow
    //     0x13cd4c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cd4c8: cmp             SP, x16
    //     0x13cd4cc: b.ls            #0x13cd518
    // 0x13cd4d0: r0 = InitLateStaticField(0xe40) // [package:get/get_core/src/get_main.dart] ::Get
    //     0x13cd4d0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x13cd4d4: ldr             x0, [x0, #0x1c80]
    //     0x13cd4d8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x13cd4dc: cmp             w0, w16
    //     0x13cd4e0: b.ne            #0x13cd4ec
    //     0x13cd4e4: ldr             x2, [PP, #0x7e18]  ; [pp+0x7e18] Field <::.Get>: static late final (offset: 0xe40)
    //     0x13cd4e8: bl              #0x16f52e0  ; InitLateFinalStaticFieldStub
    // 0x13cd4ec: ldur            x2, [fp, #-8]
    // 0x13cd4f0: r1 = Function '<anonymous closure>':.
    //     0x13cd4f0: add             x1, PP, #0x46, lsl #12  ; [pp+0x46518] AnonymousClosure: (0x13cd520), in [package:customer_app/app/presentation/views/basic/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::body (0x13c9858)
    //     0x13cd4f4: ldr             x1, [x1, #0x518]
    // 0x13cd4f8: r0 = AllocateClosure()
    //     0x13cd4f8: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x13cd4fc: stp             x0, NULL, [SP]
    // 0x13cd500: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x13cd500: ldr             x4, [PP, #0xf48]  ; [pp+0xf48] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x13cd504: r0 = GetNavigation.to()
    //     0x13cd504: bl              #0x9a3184  ; [package:get/get_navigation/src/extension_navigation.dart] ::GetNavigation.to
    // 0x13cd508: r0 = Null
    //     0x13cd508: mov             x0, NULL
    // 0x13cd50c: LeaveFrame
    //     0x13cd50c: mov             SP, fp
    //     0x13cd510: ldp             fp, lr, [SP], #0x10
    // 0x13cd514: ret
    //     0x13cd514: ret             
    // 0x13cd518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cd518: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cd51c: b               #0x13cd4d0
  }
  [closure] ViewSizeChart <anonymous closure>(dynamic) {
    // ** addr: 0x13cd520, size: 0xa0
    // 0x13cd520: EnterFrame
    //     0x13cd520: stp             fp, lr, [SP, #-0x10]!
    //     0x13cd524: mov             fp, SP
    // 0x13cd528: AllocStack(0x8)
    //     0x13cd528: sub             SP, SP, #8
    // 0x13cd52c: SetupParameters()
    //     0x13cd52c: ldr             x0, [fp, #0x10]
    //     0x13cd530: ldur            w1, [x0, #0x17]
    //     0x13cd534: add             x1, x1, HEAP, lsl #32
    // 0x13cd538: CheckStackOverflow
    //     0x13cd538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x13cd53c: cmp             SP, x16
    //     0x13cd540: b.ls            #0x13cd5b8
    // 0x13cd544: LoadField: r0 = r1->field_f
    //     0x13cd544: ldur            w0, [x1, #0xf]
    // 0x13cd548: DecompressPointer r0
    //     0x13cd548: add             x0, x0, HEAP, lsl #32
    // 0x13cd54c: mov             x1, x0
    // 0x13cd550: r0 = controller()
    //     0x13cd550: bl              #0x8a3d74  ; [package:get/get_state_manager/src/simple/get_view.dart] GetView::controller
    // 0x13cd554: LoadField: r1 = r0->field_4b
    //     0x13cd554: ldur            w1, [x0, #0x4b]
    // 0x13cd558: DecompressPointer r1
    //     0x13cd558: add             x1, x1, HEAP, lsl #32
    // 0x13cd55c: r0 = value()
    //     0x13cd55c: bl              #0x152a1b8  ; [package:get/get_rx/src/rx_types/rx_types.dart] __RxImpl&RxNotifier&RxObjectMixin::value
    // 0x13cd560: LoadField: r1 = r0->field_b
    //     0x13cd560: ldur            w1, [x0, #0xb]
    // 0x13cd564: DecompressPointer r1
    //     0x13cd564: add             x1, x1, HEAP, lsl #32
    // 0x13cd568: cmp             w1, NULL
    // 0x13cd56c: b.ne            #0x13cd578
    // 0x13cd570: r0 = Null
    //     0x13cd570: mov             x0, NULL
    // 0x13cd574: b               #0x13cd59c
    // 0x13cd578: LoadField: r0 = r1->field_2f
    //     0x13cd578: ldur            w0, [x1, #0x2f]
    // 0x13cd57c: DecompressPointer r0
    //     0x13cd57c: add             x0, x0, HEAP, lsl #32
    // 0x13cd580: cmp             w0, NULL
    // 0x13cd584: b.ne            #0x13cd590
    // 0x13cd588: r0 = Null
    //     0x13cd588: mov             x0, NULL
    // 0x13cd58c: b               #0x13cd59c
    // 0x13cd590: LoadField: r1 = r0->field_f
    //     0x13cd590: ldur            w1, [x0, #0xf]
    // 0x13cd594: DecompressPointer r1
    //     0x13cd594: add             x1, x1, HEAP, lsl #32
    // 0x13cd598: mov             x0, x1
    // 0x13cd59c: stur            x0, [fp, #-8]
    // 0x13cd5a0: r0 = ViewSizeChart()
    //     0x13cd5a0: bl              #0x9a354c  ; AllocateViewSizeChartStub -> ViewSizeChart (size=0x10)
    // 0x13cd5a4: ldur            x1, [fp, #-8]
    // 0x13cd5a8: StoreField: r0->field_b = r1
    //     0x13cd5a8: stur            w1, [x0, #0xb]
    // 0x13cd5ac: LeaveFrame
    //     0x13cd5ac: mov             SP, fp
    //     0x13cd5b0: ldp             fp, lr, [SP], #0x10
    // 0x13cd5b4: ret
    //     0x13cd5b4: ret             
    // 0x13cd5b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x13cd5b8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x13cd5bc: b               #0x13cd544
  }
  _ appBar(/* No info */) {
    // ** addr: 0x15d0264, size: 0x18c
    // 0x15d0264: EnterFrame
    //     0x15d0264: stp             fp, lr, [SP, #-0x10]!
    //     0x15d0268: mov             fp, SP
    // 0x15d026c: AllocStack(0x28)
    //     0x15d026c: sub             SP, SP, #0x28
    // 0x15d0270: SetupParameters(ExchangeProductSkusScreen this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x15d0270: mov             x0, x1
    //     0x15d0274: stur            x1, [fp, #-8]
    //     0x15d0278: mov             x1, x2
    //     0x15d027c: stur            x2, [fp, #-0x10]
    // 0x15d0280: CheckStackOverflow
    //     0x15d0280: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x15d0284: cmp             SP, x16
    //     0x15d0288: b.ls            #0x15d03e8
    // 0x15d028c: r1 = 2
    //     0x15d028c: movz            x1, #0x2
    // 0x15d0290: r0 = AllocateContext()
    //     0x15d0290: bl              #0x16f6108  ; AllocateContextStub
    // 0x15d0294: mov             x1, x0
    // 0x15d0298: ldur            x0, [fp, #-8]
    // 0x15d029c: stur            x1, [fp, #-0x18]
    // 0x15d02a0: StoreField: r1->field_f = r0
    //     0x15d02a0: stur            w0, [x1, #0xf]
    // 0x15d02a4: ldur            x0, [fp, #-0x10]
    // 0x15d02a8: StoreField: r1->field_13 = r0
    //     0x15d02a8: stur            w0, [x1, #0x13]
    // 0x15d02ac: r0 = Obx()
    //     0x15d02ac: bl              #0x9be380  ; AllocateObxStub -> Obx (size=0x10)
    // 0x15d02b0: ldur            x2, [fp, #-0x18]
    // 0x15d02b4: r1 = Function '<anonymous closure>':.
    //     0x15d02b4: add             x1, PP, #0x46, lsl #12  ; [pp+0x46520] AnonymousClosure: (0x15d03f0), in [package:customer_app/app/presentation/views/line/exchange/exchange_product_skus_screen.dart] ExchangeProductSkusScreen::appBar (0x15e9f10)
    //     0x15d02b8: ldr             x1, [x1, #0x520]
    // 0x15d02bc: stur            x0, [fp, #-8]
    // 0x15d02c0: r0 = AllocateClosure()
    //     0x15d02c0: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d02c4: mov             x1, x0
    // 0x15d02c8: ldur            x0, [fp, #-8]
    // 0x15d02cc: StoreField: r0->field_b = r1
    //     0x15d02cc: stur            w1, [x0, #0xb]
    // 0x15d02d0: ldur            x1, [fp, #-0x10]
    // 0x15d02d4: r0 = of()
    //     0x15d02d4: bl              #0x6ad750  ; [package:flutter/src/material/theme.dart] Theme::of
    // 0x15d02d8: LoadField: r1 = r0->field_5b
    //     0x15d02d8: ldur            w1, [x0, #0x5b]
    // 0x15d02dc: DecompressPointer r1
    //     0x15d02dc: add             x1, x1, HEAP, lsl #32
    // 0x15d02e0: stur            x1, [fp, #-0x10]
    // 0x15d02e4: r0 = ColorFilter()
    //     0x15d02e4: bl              #0x8fc05c  ; AllocateColorFilterStub -> ColorFilter (size=0x1c)
    // 0x15d02e8: mov             x1, x0
    // 0x15d02ec: ldur            x0, [fp, #-0x10]
    // 0x15d02f0: stur            x1, [fp, #-0x20]
    // 0x15d02f4: StoreField: r1->field_7 = r0
    //     0x15d02f4: stur            w0, [x1, #7]
    // 0x15d02f8: r0 = Instance_BlendMode
    //     0x15d02f8: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb30] Obj!BlendMode@d77481
    //     0x15d02fc: ldr             x0, [x0, #0xb30]
    // 0x15d0300: StoreField: r1->field_b = r0
    //     0x15d0300: stur            w0, [x1, #0xb]
    // 0x15d0304: r0 = 1
    //     0x15d0304: movz            x0, #0x1
    // 0x15d0308: StoreField: r1->field_13 = r0
    //     0x15d0308: stur            x0, [x1, #0x13]
    // 0x15d030c: r0 = SvgPicture()
    //     0x15d030c: bl              #0x85960c  ; AllocateSvgPictureStub -> SvgPicture (size=0x40)
    // 0x15d0310: stur            x0, [fp, #-0x10]
    // 0x15d0314: ldur            x16, [fp, #-0x20]
    // 0x15d0318: str             x16, [SP]
    // 0x15d031c: mov             x1, x0
    // 0x15d0320: r2 = "assets/images/appbar_arrow.svg"
    //     0x15d0320: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ea40] "assets/images/appbar_arrow.svg"
    //     0x15d0324: ldr             x2, [x2, #0xa40]
    // 0x15d0328: r4 = const [0, 0x3, 0x1, 0x2, colorFilter, 0x2, null]
    //     0x15d0328: add             x4, PP, #0x2e, lsl #12  ; [pp+0x2ea38] List(7) [0, 0x3, 0x1, 0x2, "colorFilter", 0x2, Null]
    //     0x15d032c: ldr             x4, [x4, #0xa38]
    // 0x15d0330: r0 = SvgPicture.asset()
    //     0x15d0330: bl              #0x8fbd88  ; [package:flutter_svg/svg.dart] SvgPicture::SvgPicture.asset
    // 0x15d0334: r0 = Align()
    //     0x15d0334: bl              #0x840f34  ; AllocateAlignStub -> Align (size=0x1c)
    // 0x15d0338: mov             x1, x0
    // 0x15d033c: r0 = Instance_Alignment
    //     0x15d033c: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2cb10] Obj!Alignment@d5a6c1
    //     0x15d0340: ldr             x0, [x0, #0xb10]
    // 0x15d0344: stur            x1, [fp, #-0x20]
    // 0x15d0348: StoreField: r1->field_f = r0
    //     0x15d0348: stur            w0, [x1, #0xf]
    // 0x15d034c: r0 = 1.000000
    //     0x15d034c: ldr             x0, [PP, #0x47b0]  ; [pp+0x47b0] 1
    // 0x15d0350: StoreField: r1->field_13 = r0
    //     0x15d0350: stur            w0, [x1, #0x13]
    // 0x15d0354: ArrayStore: r1[0] = r0  ; List_4
    //     0x15d0354: stur            w0, [x1, #0x17]
    // 0x15d0358: ldur            x0, [fp, #-0x10]
    // 0x15d035c: StoreField: r1->field_b = r0
    //     0x15d035c: stur            w0, [x1, #0xb]
    // 0x15d0360: r0 = InkWell()
    //     0x15d0360: bl              #0x8fbd7c  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0x15d0364: mov             x3, x0
    // 0x15d0368: ldur            x0, [fp, #-0x20]
    // 0x15d036c: stur            x3, [fp, #-0x10]
    // 0x15d0370: StoreField: r3->field_b = r0
    //     0x15d0370: stur            w0, [x3, #0xb]
    // 0x15d0374: ldur            x2, [fp, #-0x18]
    // 0x15d0378: r1 = Function '<anonymous closure>':.
    //     0x15d0378: add             x1, PP, #0x46, lsl #12  ; [pp+0x46528] AnonymousClosure: (0x140e660), in [package:customer_app/app/presentation/views/line/post_order/return_order/return_order_with_proof_view.dart] ReturnOrderWithProofView::appBar (0x15ece34)
    //     0x15d037c: ldr             x1, [x1, #0x528]
    // 0x15d0380: r0 = AllocateClosure()
    //     0x15d0380: bl              #0x16f64cc  ; AllocateClosureStub
    // 0x15d0384: ldur            x2, [fp, #-0x10]
    // 0x15d0388: StoreField: r2->field_f = r0
    //     0x15d0388: stur            w0, [x2, #0xf]
    // 0x15d038c: r0 = true
    //     0x15d038c: add             x0, NULL, #0x20  ; true
    // 0x15d0390: StoreField: r2->field_43 = r0
    //     0x15d0390: stur            w0, [x2, #0x43]
    // 0x15d0394: r1 = Instance_BoxShape
    //     0x15d0394: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2e080] Obj!BoxShape@d73901
    //     0x15d0398: ldr             x1, [x1, #0x80]
    // 0x15d039c: StoreField: r2->field_47 = r1
    //     0x15d039c: stur            w1, [x2, #0x47]
    // 0x15d03a0: StoreField: r2->field_6f = r0
    //     0x15d03a0: stur            w0, [x2, #0x6f]
    // 0x15d03a4: r1 = false
    //     0x15d03a4: add             x1, NULL, #0x30  ; false
    // 0x15d03a8: StoreField: r2->field_73 = r1
    //     0x15d03a8: stur            w1, [x2, #0x73]
    // 0x15d03ac: StoreField: r2->field_83 = r0
    //     0x15d03ac: stur            w0, [x2, #0x83]
    // 0x15d03b0: StoreField: r2->field_7b = r1
    //     0x15d03b0: stur            w1, [x2, #0x7b]
    // 0x15d03b4: r0 = AppBar()
    //     0x15d03b4: bl              #0x15cab1c  ; AllocateAppBarStub -> AppBar (size=0x94)
    // 0x15d03b8: stur            x0, [fp, #-0x18]
    // 0x15d03bc: ldur            x16, [fp, #-8]
    // 0x15d03c0: str             x16, [SP]
    // 0x15d03c4: mov             x1, x0
    // 0x15d03c8: ldur            x2, [fp, #-0x10]
    // 0x15d03cc: r4 = const [0, 0x3, 0x1, 0x2, title, 0x2, null]
    //     0x15d03cc: add             x4, PP, #0x33, lsl #12  ; [pp+0x33f00] List(7) [0, 0x3, 0x1, 0x2, "title", 0x2, Null]
    //     0x15d03d0: ldr             x4, [x4, #0xf00]
    // 0x15d03d4: r0 = AppBar()
    //     0x15d03d4: bl              #0x15ca70c  ; [package:flutter/src/material/app_bar.dart] AppBar::AppBar
    // 0x15d03d8: ldur            x0, [fp, #-0x18]
    // 0x15d03dc: LeaveFrame
    //     0x15d03dc: mov             SP, fp
    //     0x15d03e0: ldp             fp, lr, [SP], #0x10
    // 0x15d03e4: ret
    //     0x15d03e4: ret             
    // 0x15d03e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x15d03e8: bl              #0x16f72a0  ; StackOverflowSharedWithoutFPURegsStub
    // 0x15d03ec: b               #0x15d028c
  }
}
